import base64
import json
import zlib
from Crypto.Cipher import A<PERSON>

def decrypt_doa():
    key_b64 = "d3gzaHpoZW1BOVN2N2JlNA=="
    iv_b64 = "MWp1dGFkb3dubG9hZA=="

    key = base64.b64decode(key_b64)
    iv_decoded = base64.b64decode(iv_b64)
    iv = iv_decoded.ljust(16, b'\0')

    with open("/home/<USER>/documents/files/apk-resource/apk-res/apk-res/assets/flutter_assets/assets/data/doa.json", 'r') as f:
        encrypted_data_b64 = f.read()

    encrypted_data = base64.b64decode(encrypted_data_b64)

    cipher = AES.new(key, AES.MODE_CTR, nonce=b'', initial_value=iv)
    decrypted_data = cipher.decrypt(encrypted_data)

    for wbits in range(8, 16):
        try:
            decompressed_data = zlib.decompress(decrypted_data, wbits)
            doa_json = json.loads(decompressed_data.decode('utf-8'))
            with open("decoded_doa.json", "w") as f:
                json.dump(doa_json, f, indent=4)
            print(f"Decryption successful with wbits={wbits}. Decoded JSON written to decoded_doa.json")
            return
        except (zlib.error, UnicodeDecodeError, json.JSONDecodeError):
            continue

    try:
        decompressed_data = zlib.decompress(decrypted_data, -zlib.MAX_WBITS)
        doa_json = json.loads(decompressed_data.decode('utf-8'))
        with open("decoded_doa.json", "w") as f:
            json.dump(doa_json, f, indent=4)
        print("Decryption successful with raw zlib. Decoded JSON written to decoded_doa.json")
        return
    except (zlib.error, UnicodeDecodeError, json.JSONDecodeError):
        pass

    print("Decryption failed with all zlib wbits values.")
    print("Writing raw decrypted data to decoded_doa.bin")
    with open("decoded_doa.bin", "wb") as f:
        f.write(decrypted_data)

if __name__ == "__main__":
    decrypt_doa()