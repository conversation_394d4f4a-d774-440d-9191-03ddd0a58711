import base64
import json
from Crypto.Cipher import AES

def decrypt_doa():
    key_b64 = "d3gzaHpoZW1BOVN2N2JlNA=="
    iv_b64 = "MWp1dGFkb3dubG9hZA=="

    key = base64.b64decode(key_b64)
    iv_decoded = base64.b64decode(iv_b64)
    iv = iv_decoded.ljust(16, b'\0')

    with open("/home/<USER>/documents/files/apk-resource/apk-res/apk-res/assets/flutter_assets/assets/data/doa.json", 'r') as f:
        encrypted_strings = json.load(f)

    decrypted_doa_list = []
    for encrypted_str in encrypted_strings:
        encrypted_data = base64.b64decode(encrypted_str)

        cipher = AES.new(key, AES.MODE_CTR, nonce=b'', initial_value=iv)
        decrypted_data = cipher.decrypt(encrypted_data)

        try:
            doa_json = json.loads(decrypted_data.decode('utf-8'))
            decrypted_doa_list.append(doa_json)
        except (UnicodeDecodeError, json.JSONDecodeError) as e:
            print(f"Failed to decode JSON for a string: {e}")


    with open("decoded_doa_final.json", "w") as f:
        json.dump(decrypted_doa_list, f, indent=4)

    print(f"Successfully decrypted {len(decrypted_doa_list)} items.")
    print("Final decrypted JSON written to decoded_doa_final.json")

if __name__ == "__main__":
    decrypt_doa()
