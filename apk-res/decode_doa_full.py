
import json
import base64
import subprocess

def decrypt_string(encrypted_base64, key_hex, iv_hex):
    try:
        # Create temporary files for input and output
        with open("encrypted.bin", "wb") as f_in:
            f_in.write(base64.b64decode(encrypted_base64))

        # Decrypt using openssl
        subprocess.run([
            "openssl", "enc", "-d", "-aes-128-cbc",
            "-in", "encrypted.bin",
            "-out", "decrypted.bin",
            "-K", key_hex,
            "-iv", iv_hex
        ], check=True)

        # Read the decrypted content
        with open("decrypted.bin", "rb") as f_out:
            decrypted_data = f_out.read()
        
        # Clean up temporary files
        subprocess.run(["rm", "encrypted.bin", "decrypted.bin"])

        # Unpad the decrypted data (PKCS7)
        padding_len = decrypted_data[-1]
        unpadded_data = decrypted_data[:-padding_len]

        return unpadded_data.decode('utf-8')

    except Exception as e:
        print(f"An error occurred: {e}")
        # Clean up temporary files in case of error
        subprocess.run(["rm", "-f", "encrypted.bin", "decrypted.bin"])
        return None

def main():
    key = "w3zhzhemA9Sv7be4"
    iv = "1jutadownload"
    
    # Pad IV to 16 bytes with null bytes
    padded_iv = iv.ljust(16, '\0')

    key_hex = key.encode('utf-8').hex()
    iv_hex = padded_iv.encode('utf-8').hex()

    with open("/home/<USER>/documents/files/apk-resource/apk-res/apk-res/assets/flutter_assets/assets/data/doa.json", 'r') as f:
        encrypted_data = json.load(f)

    decrypted_data = []
    for item in encrypted_data:
        decrypted_json_str = decrypt_string(item, key_hex, iv_hex)
        if decrypted_json_str:
            try:
                decrypted_data.append(json.loads(decrypted_json_str))
            except json.JSONDecodeError:
                decrypted_data.append({"error": "JSON decode failed", "original_string": decrypted_json_str})
        else:
            decrypted_data.append({"error": "decryption failed"})

    with open("/home/<USER>/documents/files/apk-resource/apk-res/decoded_doa_final.json", 'w') as f:
        json.dump(decrypted_data, f, indent=4)

    print("Decryption complete. Decoded data written to decoded_doa_final.json")

if __name__ == "__main__":
    main()
