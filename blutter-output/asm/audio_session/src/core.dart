// lib: , url: package:audio_session/src/core.dart

// class id: 1048626, size: 0x8
class :: {
}

// class id: 5968, size: 0x1c, field offset: 0x8
class AudioDevice extends Object {

  String toString(AudioDevice) {
    // ** addr: 0xc131f8, size: 0xc4
    // 0xc131f8: EnterFrame
    //     0xc131f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc131fc: mov             fp, SP
    // 0xc13200: AllocStack(0x8)
    //     0xc13200: sub             SP, SP, #8
    // 0xc13204: CheckStackOverflow
    //     0xc13204: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc13208: cmp             SP, x16
    //     0xc1320c: b.ls            #0xc132b4
    // 0xc13210: r1 = Null
    //     0xc13210: mov             x1, NULL
    // 0xc13214: r2 = 22
    //     0xc13214: movz            x2, #0x16
    // 0xc13218: r0 = AllocateArray()
    //     0xc13218: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1321c: r16 = "AudioDevice(id:"
    //     0xc1321c: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c908] "AudioDevice(id:"
    //     0xc13220: ldr             x16, [x16, #0x908]
    // 0xc13224: StoreField: r0->field_f = r16
    //     0xc13224: stur            w16, [x0, #0xf]
    // 0xc13228: ldr             x1, [fp, #0x10]
    // 0xc1322c: LoadField: r2 = r1->field_7
    //     0xc1322c: ldur            w2, [x1, #7]
    // 0xc13230: DecompressPointer r2
    //     0xc13230: add             x2, x2, HEAP, lsl #32
    // 0xc13234: StoreField: r0->field_13 = r2
    //     0xc13234: stur            w2, [x0, #0x13]
    // 0xc13238: r16 = ",name:"
    //     0xc13238: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c910] ",name:"
    //     0xc1323c: ldr             x16, [x16, #0x910]
    // 0xc13240: ArrayStore: r0[0] = r16  ; List_4
    //     0xc13240: stur            w16, [x0, #0x17]
    // 0xc13244: LoadField: r2 = r1->field_b
    //     0xc13244: ldur            w2, [x1, #0xb]
    // 0xc13248: DecompressPointer r2
    //     0xc13248: add             x2, x2, HEAP, lsl #32
    // 0xc1324c: StoreField: r0->field_1b = r2
    //     0xc1324c: stur            w2, [x0, #0x1b]
    // 0xc13250: r16 = ",isInput:"
    //     0xc13250: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c918] ",isInput:"
    //     0xc13254: ldr             x16, [x16, #0x918]
    // 0xc13258: StoreField: r0->field_1f = r16
    //     0xc13258: stur            w16, [x0, #0x1f]
    // 0xc1325c: LoadField: r2 = r1->field_f
    //     0xc1325c: ldur            w2, [x1, #0xf]
    // 0xc13260: DecompressPointer r2
    //     0xc13260: add             x2, x2, HEAP, lsl #32
    // 0xc13264: StoreField: r0->field_23 = r2
    //     0xc13264: stur            w2, [x0, #0x23]
    // 0xc13268: r16 = ",isOutput:"
    //     0xc13268: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c920] ",isOutput:"
    //     0xc1326c: ldr             x16, [x16, #0x920]
    // 0xc13270: StoreField: r0->field_27 = r16
    //     0xc13270: stur            w16, [x0, #0x27]
    // 0xc13274: LoadField: r2 = r1->field_13
    //     0xc13274: ldur            w2, [x1, #0x13]
    // 0xc13278: DecompressPointer r2
    //     0xc13278: add             x2, x2, HEAP, lsl #32
    // 0xc1327c: StoreField: r0->field_2b = r2
    //     0xc1327c: stur            w2, [x0, #0x2b]
    // 0xc13280: r16 = ",type:"
    //     0xc13280: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c928] ",type:"
    //     0xc13284: ldr             x16, [x16, #0x928]
    // 0xc13288: StoreField: r0->field_2f = r16
    //     0xc13288: stur            w16, [x0, #0x2f]
    // 0xc1328c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc1328c: ldur            w2, [x1, #0x17]
    // 0xc13290: DecompressPointer r2
    //     0xc13290: add             x2, x2, HEAP, lsl #32
    // 0xc13294: StoreField: r0->field_33 = r2
    //     0xc13294: stur            w2, [x0, #0x33]
    // 0xc13298: r16 = ")"
    //     0xc13298: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc1329c: StoreField: r0->field_37 = r16
    //     0xc1329c: stur            w16, [x0, #0x37]
    // 0xc132a0: str             x0, [SP]
    // 0xc132a4: r0 = _interpolate()
    //     0xc132a4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc132a8: LeaveFrame
    //     0xc132a8: mov             SP, fp
    //     0xc132ac: ldp             fp, lr, [SP], #0x10
    // 0xc132b0: ret
    //     0xc132b0: ret             
    // 0xc132b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc132b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc132b8: b               #0xc13210
  }
  _ ==(/* No info */) {
    // ** addr: 0xd3b29c, size: 0x98
    // 0xd3b29c: EnterFrame
    //     0xd3b29c: stp             fp, lr, [SP, #-0x10]!
    //     0xd3b2a0: mov             fp, SP
    // 0xd3b2a4: AllocStack(0x10)
    //     0xd3b2a4: sub             SP, SP, #0x10
    // 0xd3b2a8: CheckStackOverflow
    //     0xd3b2a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3b2ac: cmp             SP, x16
    //     0xd3b2b0: b.ls            #0xd3b32c
    // 0xd3b2b4: ldr             x0, [fp, #0x10]
    // 0xd3b2b8: cmp             w0, NULL
    // 0xd3b2bc: b.ne            #0xd3b2d0
    // 0xd3b2c0: r0 = false
    //     0xd3b2c0: add             x0, NULL, #0x30  ; false
    // 0xd3b2c4: LeaveFrame
    //     0xd3b2c4: mov             SP, fp
    //     0xd3b2c8: ldp             fp, lr, [SP], #0x10
    // 0xd3b2cc: ret
    //     0xd3b2cc: ret             
    // 0xd3b2d0: r1 = 60
    //     0xd3b2d0: movz            x1, #0x3c
    // 0xd3b2d4: branchIfSmi(r0, 0xd3b2e0)
    //     0xd3b2d4: tbz             w0, #0, #0xd3b2e0
    // 0xd3b2d8: r1 = LoadClassIdInstr(r0)
    //     0xd3b2d8: ldur            x1, [x0, #-1]
    //     0xd3b2dc: ubfx            x1, x1, #0xc, #0x14
    // 0xd3b2e0: r17 = 5968
    //     0xd3b2e0: movz            x17, #0x1750
    // 0xd3b2e4: cmp             x1, x17
    // 0xd3b2e8: b.ne            #0xd3b31c
    // 0xd3b2ec: ldr             x1, [fp, #0x18]
    // 0xd3b2f0: LoadField: r2 = r1->field_7
    //     0xd3b2f0: ldur            w2, [x1, #7]
    // 0xd3b2f4: DecompressPointer r2
    //     0xd3b2f4: add             x2, x2, HEAP, lsl #32
    // 0xd3b2f8: LoadField: r1 = r0->field_7
    //     0xd3b2f8: ldur            w1, [x0, #7]
    // 0xd3b2fc: DecompressPointer r1
    //     0xd3b2fc: add             x1, x1, HEAP, lsl #32
    // 0xd3b300: r0 = LoadClassIdInstr(r2)
    //     0xd3b300: ldur            x0, [x2, #-1]
    //     0xd3b304: ubfx            x0, x0, #0xc, #0x14
    // 0xd3b308: stp             x1, x2, [SP]
    // 0xd3b30c: mov             lr, x0
    // 0xd3b310: ldr             lr, [x21, lr, lsl #3]
    // 0xd3b314: blr             lr
    // 0xd3b318: b               #0xd3b320
    // 0xd3b31c: r0 = false
    //     0xd3b31c: add             x0, NULL, #0x30  ; false
    // 0xd3b320: LeaveFrame
    //     0xd3b320: mov             SP, fp
    //     0xd3b324: ldp             fp, lr, [SP], #0x10
    // 0xd3b328: ret
    //     0xd3b328: ret             
    // 0xd3b32c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3b32c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd3b330: b               #0xd3b2b4
  }
}

// class id: 5969, size: 0x8, field offset: 0x8
class AudioDevicesChangedEvent extends Object {
}

// class id: 5970, size: 0x10, field offset: 0x8
class AudioInterruptionEvent extends Object {
}

// class id: 5971, size: 0x28, field offset: 0x8
//   const constructor, 
class AudioSessionConfiguration extends Object {

  AVAudioSessionCategory field_8;
  AVAudioSessionMode field_10;
  AndroidAudioAttributes field_1c;
  AndroidAudioFocusGainType field_20;

  _ AudioSessionConfiguration.fromJson(/* No info */) {
    // ** addr: 0x8b2340, size: 0x648
    // 0x8b2340: EnterFrame
    //     0x8b2340: stp             fp, lr, [SP, #-0x10]!
    //     0x8b2344: mov             fp, SP
    // 0x8b2348: AllocStack(0x60)
    //     0x8b2348: sub             SP, SP, #0x60
    // 0x8b234c: SetupParameters(AudioSessionConfiguration this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8b234c: mov             x4, x1
    //     0x8b2350: mov             x3, x2
    //     0x8b2354: stur            x1, [fp, #-8]
    //     0x8b2358: stur            x2, [fp, #-0x10]
    // 0x8b235c: CheckStackOverflow
    //     0x8b235c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b2360: cmp             SP, x16
    //     0x8b2364: b.ls            #0x8b2974
    // 0x8b2368: r0 = LoadClassIdInstr(r3)
    //     0x8b2368: ldur            x0, [x3, #-1]
    //     0x8b236c: ubfx            x0, x0, #0xc, #0x14
    // 0x8b2370: mov             x1, x3
    // 0x8b2374: r2 = "avAudioSessionCategory"
    //     0x8b2374: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf00] "avAudioSessionCategory"
    //     0x8b2378: ldr             x2, [x2, #0xf00]
    // 0x8b237c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b237c: sub             lr, x0, #0x114
    //     0x8b2380: ldr             lr, [x21, lr, lsl #3]
    //     0x8b2384: blr             lr
    // 0x8b2388: cmp             w0, NULL
    // 0x8b238c: b.ne            #0x8b2398
    // 0x8b2390: r4 = Null
    //     0x8b2390: mov             x4, NULL
    // 0x8b2394: b               #0x8b2404
    // 0x8b2398: ldur            x3, [fp, #-0x10]
    // 0x8b239c: r0 = LoadClassIdInstr(r3)
    //     0x8b239c: ldur            x0, [x3, #-1]
    //     0x8b23a0: ubfx            x0, x0, #0xc, #0x14
    // 0x8b23a4: mov             x1, x3
    // 0x8b23a8: r2 = "avAudioSessionCategory"
    //     0x8b23a8: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf00] "avAudioSessionCategory"
    //     0x8b23ac: ldr             x2, [x2, #0xf00]
    // 0x8b23b0: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b23b0: sub             lr, x0, #0x114
    //     0x8b23b4: ldr             lr, [x21, lr, lsl #3]
    //     0x8b23b8: blr             lr
    // 0x8b23bc: mov             x3, x0
    // 0x8b23c0: r2 = Null
    //     0x8b23c0: mov             x2, NULL
    // 0x8b23c4: r1 = Null
    //     0x8b23c4: mov             x1, NULL
    // 0x8b23c8: stur            x3, [fp, #-0x18]
    // 0x8b23cc: r4 = 60
    //     0x8b23cc: movz            x4, #0x3c
    // 0x8b23d0: branchIfSmi(r0, 0x8b23dc)
    //     0x8b23d0: tbz             w0, #0, #0x8b23dc
    // 0x8b23d4: r4 = LoadClassIdInstr(r0)
    //     0x8b23d4: ldur            x4, [x0, #-1]
    //     0x8b23d8: ubfx            x4, x4, #0xc, #0x14
    // 0x8b23dc: sub             x4, x4, #0x5e
    // 0x8b23e0: cmp             x4, #1
    // 0x8b23e4: b.ls            #0x8b23f8
    // 0x8b23e8: r8 = String
    //     0x8b23e8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8b23ec: r3 = Null
    //     0x8b23ec: add             x3, PP, #0xe, lsl #12  ; [pp+0xe008] Null
    //     0x8b23f0: ldr             x3, [x3, #8]
    // 0x8b23f4: r0 = String()
    //     0x8b23f4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8b23f8: ldur            x1, [fp, #-0x18]
    // 0x8b23fc: r0 = fromRawValue()
    //     0x8b23fc: bl              #0x8b30d4  ; [package:audio_session/src/darwin.dart] AVAudioSessionCategory::fromRawValue
    // 0x8b2400: mov             x4, x0
    // 0x8b2404: ldur            x3, [fp, #-0x10]
    // 0x8b2408: stur            x4, [fp, #-0x18]
    // 0x8b240c: r0 = LoadClassIdInstr(r3)
    //     0x8b240c: ldur            x0, [x3, #-1]
    //     0x8b2410: ubfx            x0, x0, #0xc, #0x14
    // 0x8b2414: mov             x1, x3
    // 0x8b2418: r2 = "avAudioSessionCategoryOptions"
    //     0x8b2418: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf08] "avAudioSessionCategoryOptions"
    //     0x8b241c: ldr             x2, [x2, #0xf08]
    // 0x8b2420: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b2420: sub             lr, x0, #0x114
    //     0x8b2424: ldr             lr, [x21, lr, lsl #3]
    //     0x8b2428: blr             lr
    // 0x8b242c: cmp             w0, NULL
    // 0x8b2430: b.ne            #0x8b243c
    // 0x8b2434: r4 = Null
    //     0x8b2434: mov             x4, NULL
    // 0x8b2438: b               #0x8b24c0
    // 0x8b243c: ldur            x3, [fp, #-0x10]
    // 0x8b2440: r0 = LoadClassIdInstr(r3)
    //     0x8b2440: ldur            x0, [x3, #-1]
    //     0x8b2444: ubfx            x0, x0, #0xc, #0x14
    // 0x8b2448: mov             x1, x3
    // 0x8b244c: r2 = "avAudioSessionCategoryOptions"
    //     0x8b244c: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf08] "avAudioSessionCategoryOptions"
    //     0x8b2450: ldr             x2, [x2, #0xf08]
    // 0x8b2454: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b2454: sub             lr, x0, #0x114
    //     0x8b2458: ldr             lr, [x21, lr, lsl #3]
    //     0x8b245c: blr             lr
    // 0x8b2460: mov             x3, x0
    // 0x8b2464: r2 = Null
    //     0x8b2464: mov             x2, NULL
    // 0x8b2468: r1 = Null
    //     0x8b2468: mov             x1, NULL
    // 0x8b246c: stur            x3, [fp, #-0x20]
    // 0x8b2470: branchIfSmi(r0, 0x8b2498)
    //     0x8b2470: tbz             w0, #0, #0x8b2498
    // 0x8b2474: r4 = LoadClassIdInstr(r0)
    //     0x8b2474: ldur            x4, [x0, #-1]
    //     0x8b2478: ubfx            x4, x4, #0xc, #0x14
    // 0x8b247c: sub             x4, x4, #0x3c
    // 0x8b2480: cmp             x4, #1
    // 0x8b2484: b.ls            #0x8b2498
    // 0x8b2488: r8 = int
    //     0x8b2488: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8b248c: r3 = Null
    //     0x8b248c: add             x3, PP, #0xe, lsl #12  ; [pp+0xe018] Null
    //     0x8b2490: ldr             x3, [x3, #0x18]
    // 0x8b2494: r0 = int()
    //     0x8b2494: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8b2498: ldur            x0, [fp, #-0x20]
    // 0x8b249c: r1 = LoadInt32Instr(r0)
    //     0x8b249c: sbfx            x1, x0, #1, #0x1f
    //     0x8b24a0: tbz             w0, #0, #0x8b24a8
    //     0x8b24a4: ldur            x1, [x0, #7]
    // 0x8b24a8: stur            x1, [fp, #-0x28]
    // 0x8b24ac: r0 = AVAudioSessionCategoryOptions()
    //     0x8b24ac: bl              #0x8b30c8  ; AllocateAVAudioSessionCategoryOptionsStub -> AVAudioSessionCategoryOptions (size=0x10)
    // 0x8b24b0: mov             x1, x0
    // 0x8b24b4: ldur            x0, [fp, #-0x28]
    // 0x8b24b8: StoreField: r1->field_7 = r0
    //     0x8b24b8: stur            x0, [x1, #7]
    // 0x8b24bc: mov             x4, x1
    // 0x8b24c0: ldur            x3, [fp, #-0x10]
    // 0x8b24c4: stur            x4, [fp, #-0x20]
    // 0x8b24c8: r0 = LoadClassIdInstr(r3)
    //     0x8b24c8: ldur            x0, [x3, #-1]
    //     0x8b24cc: ubfx            x0, x0, #0xc, #0x14
    // 0x8b24d0: mov             x1, x3
    // 0x8b24d4: r2 = "avAudioSessionMode"
    //     0x8b24d4: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf10] "avAudioSessionMode"
    //     0x8b24d8: ldr             x2, [x2, #0xf10]
    // 0x8b24dc: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b24dc: sub             lr, x0, #0x114
    //     0x8b24e0: ldr             lr, [x21, lr, lsl #3]
    //     0x8b24e4: blr             lr
    // 0x8b24e8: cmp             w0, NULL
    // 0x8b24ec: b.ne            #0x8b24f8
    // 0x8b24f0: r4 = Null
    //     0x8b24f0: mov             x4, NULL
    // 0x8b24f4: b               #0x8b258c
    // 0x8b24f8: ldur            x3, [fp, #-0x10]
    // 0x8b24fc: r0 = LoadClassIdInstr(r3)
    //     0x8b24fc: ldur            x0, [x3, #-1]
    //     0x8b2500: ubfx            x0, x0, #0xc, #0x14
    // 0x8b2504: mov             x1, x3
    // 0x8b2508: r2 = "avAudioSessionMode"
    //     0x8b2508: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf10] "avAudioSessionMode"
    //     0x8b250c: ldr             x2, [x2, #0xf10]
    // 0x8b2510: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b2510: sub             lr, x0, #0x114
    //     0x8b2514: ldr             lr, [x21, lr, lsl #3]
    //     0x8b2518: blr             lr
    // 0x8b251c: mov             x3, x0
    // 0x8b2520: r2 = Null
    //     0x8b2520: mov             x2, NULL
    // 0x8b2524: r1 = Null
    //     0x8b2524: mov             x1, NULL
    // 0x8b2528: stur            x3, [fp, #-0x30]
    // 0x8b252c: branchIfSmi(r0, 0x8b2554)
    //     0x8b252c: tbz             w0, #0, #0x8b2554
    // 0x8b2530: r4 = LoadClassIdInstr(r0)
    //     0x8b2530: ldur            x4, [x0, #-1]
    //     0x8b2534: ubfx            x4, x4, #0xc, #0x14
    // 0x8b2538: sub             x4, x4, #0x3c
    // 0x8b253c: cmp             x4, #1
    // 0x8b2540: b.ls            #0x8b2554
    // 0x8b2544: r8 = int
    //     0x8b2544: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8b2548: r3 = Null
    //     0x8b2548: add             x3, PP, #0xe, lsl #12  ; [pp+0xe028] Null
    //     0x8b254c: ldr             x3, [x3, #0x28]
    // 0x8b2550: r0 = int()
    //     0x8b2550: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8b2554: ldur            x0, [fp, #-0x30]
    // 0x8b2558: r2 = LoadInt32Instr(r0)
    //     0x8b2558: sbfx            x2, x0, #1, #0x1f
    //     0x8b255c: tbz             w0, #0, #0x8b2564
    //     0x8b2560: ldur            x2, [x0, #7]
    // 0x8b2564: mov             x1, x2
    // 0x8b2568: r0 = 9
    //     0x8b2568: movz            x0, #0x9
    // 0x8b256c: cmp             x1, x0
    // 0x8b2570: b.hs            #0x8b297c
    // 0x8b2574: r0 = const [Instance of 'AVAudioSessionMode', Instance of 'AVAudioSessionMode', Instance of 'AVAudioSessionMode', Instance of 'AVAudioSessionMode', Instance of 'AVAudioSessionMode', Instance of 'AVAudioSessionMode', Instance of 'AVAudioSessionMode', Instance of 'AVAudioSessionMode', Instance of 'AVAudioSessionMode']
    //     0x8b2574: add             x0, PP, #0xe, lsl #12  ; [pp+0xe038] List<AVAudioSessionMode>(9)
    //     0x8b2578: ldr             x0, [x0, #0x38]
    // 0x8b257c: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8b257c: add             x16, x0, x2, lsl #2
    //     0x8b2580: ldur            w1, [x16, #0xf]
    // 0x8b2584: DecompressPointer r1
    //     0x8b2584: add             x1, x1, HEAP, lsl #32
    // 0x8b2588: mov             x4, x1
    // 0x8b258c: ldur            x3, [fp, #-0x10]
    // 0x8b2590: stur            x4, [fp, #-0x30]
    // 0x8b2594: r0 = LoadClassIdInstr(r3)
    //     0x8b2594: ldur            x0, [x3, #-1]
    //     0x8b2598: ubfx            x0, x0, #0xc, #0x14
    // 0x8b259c: mov             x1, x3
    // 0x8b25a0: r2 = "avAudioSessionRouteSharingPolicy"
    //     0x8b25a0: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf18] "avAudioSessionRouteSharingPolicy"
    //     0x8b25a4: ldr             x2, [x2, #0xf18]
    // 0x8b25a8: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b25a8: sub             lr, x0, #0x114
    //     0x8b25ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8b25b0: blr             lr
    // 0x8b25b4: cmp             w0, NULL
    // 0x8b25b8: b.ne            #0x8b25c4
    // 0x8b25bc: r4 = Null
    //     0x8b25bc: mov             x4, NULL
    // 0x8b25c0: b               #0x8b2658
    // 0x8b25c4: ldur            x3, [fp, #-0x10]
    // 0x8b25c8: r0 = LoadClassIdInstr(r3)
    //     0x8b25c8: ldur            x0, [x3, #-1]
    //     0x8b25cc: ubfx            x0, x0, #0xc, #0x14
    // 0x8b25d0: mov             x1, x3
    // 0x8b25d4: r2 = "avAudioSessionRouteSharingPolicy"
    //     0x8b25d4: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf18] "avAudioSessionRouteSharingPolicy"
    //     0x8b25d8: ldr             x2, [x2, #0xf18]
    // 0x8b25dc: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b25dc: sub             lr, x0, #0x114
    //     0x8b25e0: ldr             lr, [x21, lr, lsl #3]
    //     0x8b25e4: blr             lr
    // 0x8b25e8: mov             x3, x0
    // 0x8b25ec: r2 = Null
    //     0x8b25ec: mov             x2, NULL
    // 0x8b25f0: r1 = Null
    //     0x8b25f0: mov             x1, NULL
    // 0x8b25f4: stur            x3, [fp, #-0x38]
    // 0x8b25f8: branchIfSmi(r0, 0x8b2620)
    //     0x8b25f8: tbz             w0, #0, #0x8b2620
    // 0x8b25fc: r4 = LoadClassIdInstr(r0)
    //     0x8b25fc: ldur            x4, [x0, #-1]
    //     0x8b2600: ubfx            x4, x4, #0xc, #0x14
    // 0x8b2604: sub             x4, x4, #0x3c
    // 0x8b2608: cmp             x4, #1
    // 0x8b260c: b.ls            #0x8b2620
    // 0x8b2610: r8 = int
    //     0x8b2610: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8b2614: r3 = Null
    //     0x8b2614: add             x3, PP, #0xe, lsl #12  ; [pp+0xe040] Null
    //     0x8b2618: ldr             x3, [x3, #0x40]
    // 0x8b261c: r0 = int()
    //     0x8b261c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8b2620: ldur            x0, [fp, #-0x38]
    // 0x8b2624: r2 = LoadInt32Instr(r0)
    //     0x8b2624: sbfx            x2, x0, #1, #0x1f
    //     0x8b2628: tbz             w0, #0, #0x8b2630
    //     0x8b262c: ldur            x2, [x0, #7]
    // 0x8b2630: mov             x1, x2
    // 0x8b2634: r0 = 4
    //     0x8b2634: movz            x0, #0x4
    // 0x8b2638: cmp             x1, x0
    // 0x8b263c: b.hs            #0x8b2980
    // 0x8b2640: r0 = const [Instance of 'AVAudioSessionRouteSharingPolicy', Instance of 'AVAudioSessionRouteSharingPolicy', Instance of 'AVAudioSessionRouteSharingPolicy', Instance of 'AVAudioSessionRouteSharingPolicy']
    //     0x8b2640: add             x0, PP, #0xe, lsl #12  ; [pp+0xe050] List<AVAudioSessionRouteSharingPolicy>(4)
    //     0x8b2644: ldr             x0, [x0, #0x50]
    // 0x8b2648: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8b2648: add             x16, x0, x2, lsl #2
    //     0x8b264c: ldur            w1, [x16, #0xf]
    // 0x8b2650: DecompressPointer r1
    //     0x8b2650: add             x1, x1, HEAP, lsl #32
    // 0x8b2654: mov             x4, x1
    // 0x8b2658: ldur            x3, [fp, #-0x10]
    // 0x8b265c: stur            x4, [fp, #-0x38]
    // 0x8b2660: r0 = LoadClassIdInstr(r3)
    //     0x8b2660: ldur            x0, [x3, #-1]
    //     0x8b2664: ubfx            x0, x0, #0xc, #0x14
    // 0x8b2668: mov             x1, x3
    // 0x8b266c: r2 = "avAudioSessionSetActiveOptions"
    //     0x8b266c: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf20] "avAudioSessionSetActiveOptions"
    //     0x8b2670: ldr             x2, [x2, #0xf20]
    // 0x8b2674: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b2674: sub             lr, x0, #0x114
    //     0x8b2678: ldr             lr, [x21, lr, lsl #3]
    //     0x8b267c: blr             lr
    // 0x8b2680: cmp             w0, NULL
    // 0x8b2684: b.ne            #0x8b2690
    // 0x8b2688: r4 = Null
    //     0x8b2688: mov             x4, NULL
    // 0x8b268c: b               #0x8b2714
    // 0x8b2690: ldur            x3, [fp, #-0x10]
    // 0x8b2694: r0 = LoadClassIdInstr(r3)
    //     0x8b2694: ldur            x0, [x3, #-1]
    //     0x8b2698: ubfx            x0, x0, #0xc, #0x14
    // 0x8b269c: mov             x1, x3
    // 0x8b26a0: r2 = "avAudioSessionSetActiveOptions"
    //     0x8b26a0: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf20] "avAudioSessionSetActiveOptions"
    //     0x8b26a4: ldr             x2, [x2, #0xf20]
    // 0x8b26a8: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b26a8: sub             lr, x0, #0x114
    //     0x8b26ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8b26b0: blr             lr
    // 0x8b26b4: mov             x3, x0
    // 0x8b26b8: r2 = Null
    //     0x8b26b8: mov             x2, NULL
    // 0x8b26bc: r1 = Null
    //     0x8b26bc: mov             x1, NULL
    // 0x8b26c0: stur            x3, [fp, #-0x40]
    // 0x8b26c4: branchIfSmi(r0, 0x8b26ec)
    //     0x8b26c4: tbz             w0, #0, #0x8b26ec
    // 0x8b26c8: r4 = LoadClassIdInstr(r0)
    //     0x8b26c8: ldur            x4, [x0, #-1]
    //     0x8b26cc: ubfx            x4, x4, #0xc, #0x14
    // 0x8b26d0: sub             x4, x4, #0x3c
    // 0x8b26d4: cmp             x4, #1
    // 0x8b26d8: b.ls            #0x8b26ec
    // 0x8b26dc: r8 = int
    //     0x8b26dc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8b26e0: r3 = Null
    //     0x8b26e0: add             x3, PP, #0xe, lsl #12  ; [pp+0xe058] Null
    //     0x8b26e4: ldr             x3, [x3, #0x58]
    // 0x8b26e8: r0 = int()
    //     0x8b26e8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8b26ec: ldur            x0, [fp, #-0x40]
    // 0x8b26f0: r1 = LoadInt32Instr(r0)
    //     0x8b26f0: sbfx            x1, x0, #1, #0x1f
    //     0x8b26f4: tbz             w0, #0, #0x8b26fc
    //     0x8b26f8: ldur            x1, [x0, #7]
    // 0x8b26fc: stur            x1, [fp, #-0x28]
    // 0x8b2700: r0 = AVAudioSessionSetActiveOptions()
    //     0x8b2700: bl              #0x8b30bc  ; AllocateAVAudioSessionSetActiveOptionsStub -> AVAudioSessionSetActiveOptions (size=0x10)
    // 0x8b2704: mov             x1, x0
    // 0x8b2708: ldur            x0, [fp, #-0x28]
    // 0x8b270c: StoreField: r1->field_7 = r0
    //     0x8b270c: stur            x0, [x1, #7]
    // 0x8b2710: mov             x4, x1
    // 0x8b2714: ldur            x3, [fp, #-0x10]
    // 0x8b2718: stur            x4, [fp, #-0x40]
    // 0x8b271c: r0 = LoadClassIdInstr(r3)
    //     0x8b271c: ldur            x0, [x3, #-1]
    //     0x8b2720: ubfx            x0, x0, #0xc, #0x14
    // 0x8b2724: mov             x1, x3
    // 0x8b2728: r2 = "androidAudioAttributes"
    //     0x8b2728: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf28] "androidAudioAttributes"
    //     0x8b272c: ldr             x2, [x2, #0xf28]
    // 0x8b2730: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b2730: sub             lr, x0, #0x114
    //     0x8b2734: ldr             lr, [x21, lr, lsl #3]
    //     0x8b2738: blr             lr
    // 0x8b273c: cmp             w0, NULL
    // 0x8b2740: b.ne            #0x8b274c
    // 0x8b2744: r5 = Null
    //     0x8b2744: mov             x5, NULL
    // 0x8b2748: b               #0x8b27d4
    // 0x8b274c: ldur            x3, [fp, #-0x10]
    // 0x8b2750: r0 = LoadClassIdInstr(r3)
    //     0x8b2750: ldur            x0, [x3, #-1]
    //     0x8b2754: ubfx            x0, x0, #0xc, #0x14
    // 0x8b2758: mov             x1, x3
    // 0x8b275c: r2 = "androidAudioAttributes"
    //     0x8b275c: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf28] "androidAudioAttributes"
    //     0x8b2760: ldr             x2, [x2, #0xf28]
    // 0x8b2764: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b2764: sub             lr, x0, #0x114
    //     0x8b2768: ldr             lr, [x21, lr, lsl #3]
    //     0x8b276c: blr             lr
    // 0x8b2770: mov             x3, x0
    // 0x8b2774: r2 = Null
    //     0x8b2774: mov             x2, NULL
    // 0x8b2778: r1 = Null
    //     0x8b2778: mov             x1, NULL
    // 0x8b277c: stur            x3, [fp, #-0x48]
    // 0x8b2780: r8 = Map
    //     0x8b2780: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0x8b2784: r3 = Null
    //     0x8b2784: add             x3, PP, #0xe, lsl #12  ; [pp+0xe068] Null
    //     0x8b2788: ldr             x3, [x3, #0x68]
    // 0x8b278c: r0 = Map()
    //     0x8b278c: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0x8b2790: ldur            x0, [fp, #-0x48]
    // 0x8b2794: r1 = LoadClassIdInstr(r0)
    //     0x8b2794: ldur            x1, [x0, #-1]
    //     0x8b2798: ubfx            x1, x1, #0xc, #0x14
    // 0x8b279c: r16 = <String, dynamic>
    //     0x8b279c: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x8b27a0: stp             x0, x16, [SP]
    // 0x8b27a4: mov             x0, x1
    // 0x8b27a8: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x8b27a8: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x8b27ac: r0 = GDT[cid_x0 + 0x5f4]()
    //     0x8b27ac: add             lr, x0, #0x5f4
    //     0x8b27b0: ldr             lr, [x21, lr, lsl #3]
    //     0x8b27b4: blr             lr
    // 0x8b27b8: stur            x0, [fp, #-0x48]
    // 0x8b27bc: r0 = AndroidAudioAttributes()
    //     0x8b27bc: bl              #0x8b30b0  ; AllocateAndroidAudioAttributesStub -> AndroidAudioAttributes (size=0x14)
    // 0x8b27c0: mov             x1, x0
    // 0x8b27c4: ldur            x2, [fp, #-0x48]
    // 0x8b27c8: stur            x0, [fp, #-0x48]
    // 0x8b27cc: r0 = AndroidAudioAttributes.fromJson()
    //     0x8b27cc: bl              #0x8b2dfc  ; [package:audio_session/src/android.dart] AndroidAudioAttributes::AndroidAudioAttributes.fromJson
    // 0x8b27d0: ldur            x5, [fp, #-0x48]
    // 0x8b27d4: ldur            x4, [fp, #-8]
    // 0x8b27d8: ldur            x3, [fp, #-0x10]
    // 0x8b27dc: stur            x5, [fp, #-0x48]
    // 0x8b27e0: r0 = LoadClassIdInstr(r3)
    //     0x8b27e0: ldur            x0, [x3, #-1]
    //     0x8b27e4: ubfx            x0, x0, #0xc, #0x14
    // 0x8b27e8: mov             x1, x3
    // 0x8b27ec: r2 = "androidAudioFocusGainType"
    //     0x8b27ec: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf30] "androidAudioFocusGainType"
    //     0x8b27f0: ldr             x2, [x2, #0xf30]
    // 0x8b27f4: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b27f4: sub             lr, x0, #0x114
    //     0x8b27f8: ldr             lr, [x21, lr, lsl #3]
    //     0x8b27fc: blr             lr
    // 0x8b2800: mov             x2, x0
    // 0x8b2804: r1 = _ConstMap len:4
    //     0x8b2804: add             x1, PP, #0xe, lsl #12  ; [pp+0xe078] Map<int, AndroidAudioFocusGainType>(4)
    //     0x8b2808: ldr             x1, [x1, #0x78]
    // 0x8b280c: r0 = []()
    //     0x8b280c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x8b2810: mov             x3, x0
    // 0x8b2814: stur            x3, [fp, #-0x50]
    // 0x8b2818: cmp             w3, NULL
    // 0x8b281c: b.eq            #0x8b2984
    // 0x8b2820: ldur            x1, [fp, #-0x10]
    // 0x8b2824: r0 = LoadClassIdInstr(r1)
    //     0x8b2824: ldur            x0, [x1, #-1]
    //     0x8b2828: ubfx            x0, x0, #0xc, #0x14
    // 0x8b282c: r2 = "androidWillPauseWhenDucked"
    //     0x8b282c: add             x2, PP, #0xd, lsl #12  ; [pp+0xdf38] "androidWillPauseWhenDucked"
    //     0x8b2830: ldr             x2, [x2, #0xf38]
    // 0x8b2834: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b2834: sub             lr, x0, #0x114
    //     0x8b2838: ldr             lr, [x21, lr, lsl #3]
    //     0x8b283c: blr             lr
    // 0x8b2840: mov             x3, x0
    // 0x8b2844: r2 = Null
    //     0x8b2844: mov             x2, NULL
    // 0x8b2848: r1 = Null
    //     0x8b2848: mov             x1, NULL
    // 0x8b284c: stur            x3, [fp, #-0x10]
    // 0x8b2850: r4 = 60
    //     0x8b2850: movz            x4, #0x3c
    // 0x8b2854: branchIfSmi(r0, 0x8b2860)
    //     0x8b2854: tbz             w0, #0, #0x8b2860
    // 0x8b2858: r4 = LoadClassIdInstr(r0)
    //     0x8b2858: ldur            x4, [x0, #-1]
    //     0x8b285c: ubfx            x4, x4, #0xc, #0x14
    // 0x8b2860: cmp             x4, #0x3f
    // 0x8b2864: b.eq            #0x8b2878
    // 0x8b2868: r8 = bool?
    //     0x8b2868: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x8b286c: r3 = Null
    //     0x8b286c: add             x3, PP, #0xe, lsl #12  ; [pp+0xe080] Null
    //     0x8b2870: ldr             x3, [x3, #0x80]
    // 0x8b2874: r0 = bool?()
    //     0x8b2874: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x8b2878: ldur            x0, [fp, #-0x18]
    // 0x8b287c: ldur            x1, [fp, #-8]
    // 0x8b2880: StoreField: r1->field_7 = r0
    //     0x8b2880: stur            w0, [x1, #7]
    //     0x8b2884: ldurb           w16, [x1, #-1]
    //     0x8b2888: ldurb           w17, [x0, #-1]
    //     0x8b288c: and             x16, x17, x16, lsr #2
    //     0x8b2890: tst             x16, HEAP, lsr #32
    //     0x8b2894: b.eq            #0x8b289c
    //     0x8b2898: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b289c: ldur            x0, [fp, #-0x20]
    // 0x8b28a0: StoreField: r1->field_b = r0
    //     0x8b28a0: stur            w0, [x1, #0xb]
    //     0x8b28a4: ldurb           w16, [x1, #-1]
    //     0x8b28a8: ldurb           w17, [x0, #-1]
    //     0x8b28ac: and             x16, x17, x16, lsr #2
    //     0x8b28b0: tst             x16, HEAP, lsr #32
    //     0x8b28b4: b.eq            #0x8b28bc
    //     0x8b28b8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b28bc: ldur            x0, [fp, #-0x30]
    // 0x8b28c0: StoreField: r1->field_f = r0
    //     0x8b28c0: stur            w0, [x1, #0xf]
    //     0x8b28c4: ldurb           w16, [x1, #-1]
    //     0x8b28c8: ldurb           w17, [x0, #-1]
    //     0x8b28cc: and             x16, x17, x16, lsr #2
    //     0x8b28d0: tst             x16, HEAP, lsr #32
    //     0x8b28d4: b.eq            #0x8b28dc
    //     0x8b28d8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b28dc: ldur            x0, [fp, #-0x38]
    // 0x8b28e0: StoreField: r1->field_13 = r0
    //     0x8b28e0: stur            w0, [x1, #0x13]
    //     0x8b28e4: ldurb           w16, [x1, #-1]
    //     0x8b28e8: ldurb           w17, [x0, #-1]
    //     0x8b28ec: and             x16, x17, x16, lsr #2
    //     0x8b28f0: tst             x16, HEAP, lsr #32
    //     0x8b28f4: b.eq            #0x8b28fc
    //     0x8b28f8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b28fc: ldur            x0, [fp, #-0x40]
    // 0x8b2900: ArrayStore: r1[0] = r0  ; List_4
    //     0x8b2900: stur            w0, [x1, #0x17]
    //     0x8b2904: ldurb           w16, [x1, #-1]
    //     0x8b2908: ldurb           w17, [x0, #-1]
    //     0x8b290c: and             x16, x17, x16, lsr #2
    //     0x8b2910: tst             x16, HEAP, lsr #32
    //     0x8b2914: b.eq            #0x8b291c
    //     0x8b2918: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b291c: ldur            x0, [fp, #-0x48]
    // 0x8b2920: StoreField: r1->field_1b = r0
    //     0x8b2920: stur            w0, [x1, #0x1b]
    //     0x8b2924: ldurb           w16, [x1, #-1]
    //     0x8b2928: ldurb           w17, [x0, #-1]
    //     0x8b292c: and             x16, x17, x16, lsr #2
    //     0x8b2930: tst             x16, HEAP, lsr #32
    //     0x8b2934: b.eq            #0x8b293c
    //     0x8b2938: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b293c: ldur            x0, [fp, #-0x50]
    // 0x8b2940: StoreField: r1->field_1f = r0
    //     0x8b2940: stur            w0, [x1, #0x1f]
    //     0x8b2944: ldurb           w16, [x1, #-1]
    //     0x8b2948: ldurb           w17, [x0, #-1]
    //     0x8b294c: and             x16, x17, x16, lsr #2
    //     0x8b2950: tst             x16, HEAP, lsr #32
    //     0x8b2954: b.eq            #0x8b295c
    //     0x8b2958: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b295c: ldur            x2, [fp, #-0x10]
    // 0x8b2960: StoreField: r1->field_23 = r2
    //     0x8b2960: stur            w2, [x1, #0x23]
    // 0x8b2964: r0 = Null
    //     0x8b2964: mov             x0, NULL
    // 0x8b2968: LeaveFrame
    //     0x8b2968: mov             SP, fp
    //     0x8b296c: ldp             fp, lr, [SP], #0x10
    // 0x8b2970: ret
    //     0x8b2970: ret             
    // 0x8b2974: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b2974: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b2978: b               #0x8b2368
    // 0x8b297c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b297c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b2980: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b2980: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b2984: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8b2984: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  Map<String, dynamic> toJson(AudioSessionConfiguration) {
    // ** addr: 0x8b29a0, size: 0x48
    // 0x8b29a0: EnterFrame
    //     0x8b29a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8b29a4: mov             fp, SP
    // 0x8b29a8: CheckStackOverflow
    //     0x8b29a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b29ac: cmp             SP, x16
    //     0x8b29b0: b.ls            #0x8b29c8
    // 0x8b29b4: ldr             x1, [fp, #0x10]
    // 0x8b29b8: r0 = toJson()
    //     0x8b29b8: bl              #0x8b29d0  ; [package:audio_session/src/core.dart] AudioSessionConfiguration::toJson
    // 0x8b29bc: LeaveFrame
    //     0x8b29bc: mov             SP, fp
    //     0x8b29c0: ldp             fp, lr, [SP], #0x10
    // 0x8b29c4: ret
    //     0x8b29c4: ret             
    // 0x8b29c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b29c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b29cc: b               #0x8b29b4
  }
  Map<String, dynamic> toJson(AudioSessionConfiguration) {
    // ** addr: 0x8b29d0, size: 0x2e0
    // 0x8b29d0: EnterFrame
    //     0x8b29d0: stp             fp, lr, [SP, #-0x10]!
    //     0x8b29d4: mov             fp, SP
    // 0x8b29d8: AllocStack(0x20)
    //     0x8b29d8: sub             SP, SP, #0x20
    // 0x8b29dc: SetupParameters(AudioSessionConfiguration this /* r1 => r0, fp-0x8 */)
    //     0x8b29dc: mov             x0, x1
    //     0x8b29e0: stur            x1, [fp, #-8]
    // 0x8b29e4: CheckStackOverflow
    //     0x8b29e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b29e8: cmp             SP, x16
    //     0x8b29ec: b.ls            #0x8b2ca8
    // 0x8b29f0: r1 = Null
    //     0x8b29f0: mov             x1, NULL
    // 0x8b29f4: r2 = 32
    //     0x8b29f4: movz            x2, #0x20
    // 0x8b29f8: r0 = AllocateArray()
    //     0x8b29f8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8b29fc: mov             x2, x0
    // 0x8b2a00: stur            x2, [fp, #-0x10]
    // 0x8b2a04: r16 = "avAudioSessionCategory"
    //     0x8b2a04: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf00] "avAudioSessionCategory"
    //     0x8b2a08: ldr             x16, [x16, #0xf00]
    // 0x8b2a0c: StoreField: r2->field_f = r16
    //     0x8b2a0c: stur            w16, [x2, #0xf]
    // 0x8b2a10: ldur            x3, [fp, #-8]
    // 0x8b2a14: LoadField: r0 = r3->field_7
    //     0x8b2a14: ldur            w0, [x3, #7]
    // 0x8b2a18: DecompressPointer r0
    //     0x8b2a18: add             x0, x0, HEAP, lsl #32
    // 0x8b2a1c: cmp             w0, NULL
    // 0x8b2a20: b.ne            #0x8b2a2c
    // 0x8b2a24: r0 = Null
    //     0x8b2a24: mov             x0, NULL
    // 0x8b2a28: b               #0x8b2a38
    // 0x8b2a2c: LoadField: r1 = r0->field_13
    //     0x8b2a2c: ldur            w1, [x0, #0x13]
    // 0x8b2a30: DecompressPointer r1
    //     0x8b2a30: add             x1, x1, HEAP, lsl #32
    // 0x8b2a34: mov             x0, x1
    // 0x8b2a38: StoreField: r2->field_13 = r0
    //     0x8b2a38: stur            w0, [x2, #0x13]
    // 0x8b2a3c: r16 = "avAudioSessionCategoryOptions"
    //     0x8b2a3c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf08] "avAudioSessionCategoryOptions"
    //     0x8b2a40: ldr             x16, [x16, #0xf08]
    // 0x8b2a44: ArrayStore: r2[0] = r16  ; List_4
    //     0x8b2a44: stur            w16, [x2, #0x17]
    // 0x8b2a48: LoadField: r0 = r3->field_b
    //     0x8b2a48: ldur            w0, [x3, #0xb]
    // 0x8b2a4c: DecompressPointer r0
    //     0x8b2a4c: add             x0, x0, HEAP, lsl #32
    // 0x8b2a50: cmp             w0, NULL
    // 0x8b2a54: b.ne            #0x8b2a60
    // 0x8b2a58: r0 = Null
    //     0x8b2a58: mov             x0, NULL
    // 0x8b2a5c: b               #0x8b2a78
    // 0x8b2a60: LoadField: r4 = r0->field_7
    //     0x8b2a60: ldur            x4, [x0, #7]
    // 0x8b2a64: r0 = BoxInt64Instr(r4)
    //     0x8b2a64: sbfiz           x0, x4, #1, #0x1f
    //     0x8b2a68: cmp             x4, x0, asr #1
    //     0x8b2a6c: b.eq            #0x8b2a78
    //     0x8b2a70: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8b2a74: stur            x4, [x0, #7]
    // 0x8b2a78: mov             x1, x2
    // 0x8b2a7c: ArrayStore: r1[3] = r0  ; List_4
    //     0x8b2a7c: add             x25, x1, #0x1b
    //     0x8b2a80: str             w0, [x25]
    //     0x8b2a84: tbz             w0, #0, #0x8b2aa0
    //     0x8b2a88: ldurb           w16, [x1, #-1]
    //     0x8b2a8c: ldurb           w17, [x0, #-1]
    //     0x8b2a90: and             x16, x17, x16, lsr #2
    //     0x8b2a94: tst             x16, HEAP, lsr #32
    //     0x8b2a98: b.eq            #0x8b2aa0
    //     0x8b2a9c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b2aa0: r16 = "avAudioSessionMode"
    //     0x8b2aa0: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf10] "avAudioSessionMode"
    //     0x8b2aa4: ldr             x16, [x16, #0xf10]
    // 0x8b2aa8: StoreField: r2->field_1f = r16
    //     0x8b2aa8: stur            w16, [x2, #0x1f]
    // 0x8b2aac: LoadField: r0 = r3->field_f
    //     0x8b2aac: ldur            w0, [x3, #0xf]
    // 0x8b2ab0: DecompressPointer r0
    //     0x8b2ab0: add             x0, x0, HEAP, lsl #32
    // 0x8b2ab4: cmp             w0, NULL
    // 0x8b2ab8: b.ne            #0x8b2ac4
    // 0x8b2abc: r0 = Null
    //     0x8b2abc: mov             x0, NULL
    // 0x8b2ac0: b               #0x8b2adc
    // 0x8b2ac4: LoadField: r4 = r0->field_7
    //     0x8b2ac4: ldur            x4, [x0, #7]
    // 0x8b2ac8: r0 = BoxInt64Instr(r4)
    //     0x8b2ac8: sbfiz           x0, x4, #1, #0x1f
    //     0x8b2acc: cmp             x4, x0, asr #1
    //     0x8b2ad0: b.eq            #0x8b2adc
    //     0x8b2ad4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8b2ad8: stur            x4, [x0, #7]
    // 0x8b2adc: mov             x1, x2
    // 0x8b2ae0: ArrayStore: r1[5] = r0  ; List_4
    //     0x8b2ae0: add             x25, x1, #0x23
    //     0x8b2ae4: str             w0, [x25]
    //     0x8b2ae8: tbz             w0, #0, #0x8b2b04
    //     0x8b2aec: ldurb           w16, [x1, #-1]
    //     0x8b2af0: ldurb           w17, [x0, #-1]
    //     0x8b2af4: and             x16, x17, x16, lsr #2
    //     0x8b2af8: tst             x16, HEAP, lsr #32
    //     0x8b2afc: b.eq            #0x8b2b04
    //     0x8b2b00: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b2b04: r16 = "avAudioSessionRouteSharingPolicy"
    //     0x8b2b04: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf18] "avAudioSessionRouteSharingPolicy"
    //     0x8b2b08: ldr             x16, [x16, #0xf18]
    // 0x8b2b0c: StoreField: r2->field_27 = r16
    //     0x8b2b0c: stur            w16, [x2, #0x27]
    // 0x8b2b10: LoadField: r0 = r3->field_13
    //     0x8b2b10: ldur            w0, [x3, #0x13]
    // 0x8b2b14: DecompressPointer r0
    //     0x8b2b14: add             x0, x0, HEAP, lsl #32
    // 0x8b2b18: cmp             w0, NULL
    // 0x8b2b1c: b.ne            #0x8b2b28
    // 0x8b2b20: r0 = Null
    //     0x8b2b20: mov             x0, NULL
    // 0x8b2b24: b               #0x8b2b40
    // 0x8b2b28: LoadField: r4 = r0->field_7
    //     0x8b2b28: ldur            x4, [x0, #7]
    // 0x8b2b2c: r0 = BoxInt64Instr(r4)
    //     0x8b2b2c: sbfiz           x0, x4, #1, #0x1f
    //     0x8b2b30: cmp             x4, x0, asr #1
    //     0x8b2b34: b.eq            #0x8b2b40
    //     0x8b2b38: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8b2b3c: stur            x4, [x0, #7]
    // 0x8b2b40: mov             x1, x2
    // 0x8b2b44: ArrayStore: r1[7] = r0  ; List_4
    //     0x8b2b44: add             x25, x1, #0x2b
    //     0x8b2b48: str             w0, [x25]
    //     0x8b2b4c: tbz             w0, #0, #0x8b2b68
    //     0x8b2b50: ldurb           w16, [x1, #-1]
    //     0x8b2b54: ldurb           w17, [x0, #-1]
    //     0x8b2b58: and             x16, x17, x16, lsr #2
    //     0x8b2b5c: tst             x16, HEAP, lsr #32
    //     0x8b2b60: b.eq            #0x8b2b68
    //     0x8b2b64: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b2b68: r16 = "avAudioSessionSetActiveOptions"
    //     0x8b2b68: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf20] "avAudioSessionSetActiveOptions"
    //     0x8b2b6c: ldr             x16, [x16, #0xf20]
    // 0x8b2b70: StoreField: r2->field_2f = r16
    //     0x8b2b70: stur            w16, [x2, #0x2f]
    // 0x8b2b74: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x8b2b74: ldur            w0, [x3, #0x17]
    // 0x8b2b78: DecompressPointer r0
    //     0x8b2b78: add             x0, x0, HEAP, lsl #32
    // 0x8b2b7c: cmp             w0, NULL
    // 0x8b2b80: b.ne            #0x8b2b8c
    // 0x8b2b84: r0 = Null
    //     0x8b2b84: mov             x0, NULL
    // 0x8b2b88: b               #0x8b2ba4
    // 0x8b2b8c: LoadField: r4 = r0->field_7
    //     0x8b2b8c: ldur            x4, [x0, #7]
    // 0x8b2b90: r0 = BoxInt64Instr(r4)
    //     0x8b2b90: sbfiz           x0, x4, #1, #0x1f
    //     0x8b2b94: cmp             x4, x0, asr #1
    //     0x8b2b98: b.eq            #0x8b2ba4
    //     0x8b2b9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8b2ba0: stur            x4, [x0, #7]
    // 0x8b2ba4: mov             x1, x2
    // 0x8b2ba8: ArrayStore: r1[9] = r0  ; List_4
    //     0x8b2ba8: add             x25, x1, #0x33
    //     0x8b2bac: str             w0, [x25]
    //     0x8b2bb0: tbz             w0, #0, #0x8b2bcc
    //     0x8b2bb4: ldurb           w16, [x1, #-1]
    //     0x8b2bb8: ldurb           w17, [x0, #-1]
    //     0x8b2bbc: and             x16, x17, x16, lsr #2
    //     0x8b2bc0: tst             x16, HEAP, lsr #32
    //     0x8b2bc4: b.eq            #0x8b2bcc
    //     0x8b2bc8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b2bcc: r16 = "androidAudioAttributes"
    //     0x8b2bcc: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf28] "androidAudioAttributes"
    //     0x8b2bd0: ldr             x16, [x16, #0xf28]
    // 0x8b2bd4: StoreField: r2->field_37 = r16
    //     0x8b2bd4: stur            w16, [x2, #0x37]
    // 0x8b2bd8: LoadField: r1 = r3->field_1b
    //     0x8b2bd8: ldur            w1, [x3, #0x1b]
    // 0x8b2bdc: DecompressPointer r1
    //     0x8b2bdc: add             x1, x1, HEAP, lsl #32
    // 0x8b2be0: cmp             w1, NULL
    // 0x8b2be4: b.ne            #0x8b2bf0
    // 0x8b2be8: r0 = Null
    //     0x8b2be8: mov             x0, NULL
    // 0x8b2bec: b               #0x8b2bfc
    // 0x8b2bf0: r0 = toJson()
    //     0x8b2bf0: bl              #0x8b2cb0  ; [package:audio_session/src/android.dart] AndroidAudioAttributes::toJson
    // 0x8b2bf4: ldur            x3, [fp, #-8]
    // 0x8b2bf8: ldur            x2, [fp, #-0x10]
    // 0x8b2bfc: mov             x1, x2
    // 0x8b2c00: ArrayStore: r1[11] = r0  ; List_4
    //     0x8b2c00: add             x25, x1, #0x3b
    //     0x8b2c04: str             w0, [x25]
    //     0x8b2c08: tbz             w0, #0, #0x8b2c24
    //     0x8b2c0c: ldurb           w16, [x1, #-1]
    //     0x8b2c10: ldurb           w17, [x0, #-1]
    //     0x8b2c14: and             x16, x17, x16, lsr #2
    //     0x8b2c18: tst             x16, HEAP, lsr #32
    //     0x8b2c1c: b.eq            #0x8b2c24
    //     0x8b2c20: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b2c24: r16 = "androidAudioFocusGainType"
    //     0x8b2c24: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf30] "androidAudioFocusGainType"
    //     0x8b2c28: ldr             x16, [x16, #0xf30]
    // 0x8b2c2c: StoreField: r2->field_3f = r16
    //     0x8b2c2c: stur            w16, [x2, #0x3f]
    // 0x8b2c30: LoadField: r0 = r3->field_1f
    //     0x8b2c30: ldur            w0, [x3, #0x1f]
    // 0x8b2c34: DecompressPointer r0
    //     0x8b2c34: add             x0, x0, HEAP, lsl #32
    // 0x8b2c38: LoadField: r4 = r0->field_7
    //     0x8b2c38: ldur            x4, [x0, #7]
    // 0x8b2c3c: r0 = BoxInt64Instr(r4)
    //     0x8b2c3c: sbfiz           x0, x4, #1, #0x1f
    //     0x8b2c40: cmp             x4, x0, asr #1
    //     0x8b2c44: b.eq            #0x8b2c50
    //     0x8b2c48: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8b2c4c: stur            x4, [x0, #7]
    // 0x8b2c50: mov             x1, x2
    // 0x8b2c54: ArrayStore: r1[13] = r0  ; List_4
    //     0x8b2c54: add             x25, x1, #0x43
    //     0x8b2c58: str             w0, [x25]
    //     0x8b2c5c: tbz             w0, #0, #0x8b2c78
    //     0x8b2c60: ldurb           w16, [x1, #-1]
    //     0x8b2c64: ldurb           w17, [x0, #-1]
    //     0x8b2c68: and             x16, x17, x16, lsr #2
    //     0x8b2c6c: tst             x16, HEAP, lsr #32
    //     0x8b2c70: b.eq            #0x8b2c78
    //     0x8b2c74: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b2c78: r16 = "androidWillPauseWhenDucked"
    //     0x8b2c78: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf38] "androidWillPauseWhenDucked"
    //     0x8b2c7c: ldr             x16, [x16, #0xf38]
    // 0x8b2c80: StoreField: r2->field_47 = r16
    //     0x8b2c80: stur            w16, [x2, #0x47]
    // 0x8b2c84: LoadField: r0 = r3->field_23
    //     0x8b2c84: ldur            w0, [x3, #0x23]
    // 0x8b2c88: DecompressPointer r0
    //     0x8b2c88: add             x0, x0, HEAP, lsl #32
    // 0x8b2c8c: StoreField: r2->field_4b = r0
    //     0x8b2c8c: stur            w0, [x2, #0x4b]
    // 0x8b2c90: r16 = <String, dynamic>
    //     0x8b2c90: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x8b2c94: stp             x2, x16, [SP]
    // 0x8b2c98: r0 = Map._fromLiteral()
    //     0x8b2c98: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8b2c9c: LeaveFrame
    //     0x8b2c9c: mov             SP, fp
    //     0x8b2ca0: ldp             fp, lr, [SP], #0x10
    // 0x8b2ca4: ret
    //     0x8b2ca4: ret             
    // 0x8b2ca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b2ca8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b2cac: b               #0x8b29f0
  }
}

// class id: 5972, size: 0x2c, field offset: 0x8
class AudioSession extends Object {

  late final BehaviorSubject<Set<AudioDevice>> _devicesSubject; // offset: 0x24

  get _ instance(/* No info */) async {
    // ** addr: 0x8b2238, size: 0x108
    // 0x8b2238: EnterFrame
    //     0x8b2238: stp             fp, lr, [SP, #-0x10]!
    //     0x8b223c: mov             fp, SP
    // 0x8b2240: AllocStack(0x58)
    //     0x8b2240: sub             SP, SP, #0x58
    // 0x8b2244: SetupParameters()
    //     0x8b2244: stur            NULL, [fp, #-8]
    // 0x8b2248: CheckStackOverflow
    //     0x8b2248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b224c: cmp             SP, x16
    //     0x8b2250: b.ls            #0x8b2330
    // 0x8b2254: InitAsync() -> Future<AudioSession>
    //     0x8b2254: add             x0, PP, #0xd, lsl #12  ; [pp+0xdff8] TypeArguments: <AudioSession>
    //     0x8b2258: ldr             x0, [x0, #0xff8]
    //     0x8b225c: bl              #0x661298  ; InitAsyncStub
    // 0x8b2260: r0 = LoadStaticField(0xc5c)
    //     0x8b2260: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b2264: ldr             x0, [x0, #0x18b8]
    // 0x8b2268: cmp             w0, NULL
    // 0x8b226c: b.ne            #0x8b231c
    // 0x8b2270: r0 = AudioSession()
    //     0x8b2270: bl              #0x8b5144  ; AllocateAudioSessionStub -> AudioSession (size=0x2c)
    // 0x8b2274: mov             x1, x0
    // 0x8b2278: stur            x0, [fp, #-0x38]
    // 0x8b227c: r0 = AudioSession._()
    //     0x8b227c: bl              #0x8b3124  ; [package:audio_session/src/core.dart] AudioSession::AudioSession._
    // 0x8b2280: ldur            x0, [fp, #-0x38]
    // 0x8b2284: StoreStaticField(0xc5c, r0)
    //     0x8b2284: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x8b2288: str             x0, [x1, #0x18b8]
    // 0x8b228c: r16 = <String, dynamic>
    //     0x8b228c: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x8b2290: r30 = Instance_MethodChannel
    //     0x8b2290: add             lr, PP, #0xd, lsl #12  ; [pp+0xdef0] Obj!MethodChannel@e11151
    //     0x8b2294: ldr             lr, [lr, #0xef0]
    // 0x8b2298: stp             lr, x16, [SP, #8]
    // 0x8b229c: r16 = "getConfiguration"
    //     0x8b229c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe000] "getConfiguration"
    //     0x8b22a0: ldr             x16, [x16]
    // 0x8b22a4: str             x16, [SP]
    // 0x8b22a8: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x8b22a8: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x8b22ac: r0 = invokeMapMethod()
    //     0x8b22ac: bl              #0x698d1c  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMapMethod
    // 0x8b22b0: mov             x1, x0
    // 0x8b22b4: stur            x1, [fp, #-0x38]
    // 0x8b22b8: r0 = Await()
    //     0x8b22b8: bl              #0x661044  ; AwaitStub
    // 0x8b22bc: stur            x0, [fp, #-0x40]
    // 0x8b22c0: cmp             w0, NULL
    // 0x8b22c4: b.eq            #0x8b231c
    // 0x8b22c8: r1 = LoadStaticField(0xc5c)
    //     0x8b22c8: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x8b22cc: ldr             x1, [x1, #0x18b8]
    // 0x8b22d0: stur            x1, [fp, #-0x38]
    // 0x8b22d4: cmp             w1, NULL
    // 0x8b22d8: b.eq            #0x8b2338
    // 0x8b22dc: r0 = AudioSessionConfiguration()
    //     0x8b22dc: bl              #0x8b3118  ; AllocateAudioSessionConfigurationStub -> AudioSessionConfiguration (size=0x28)
    // 0x8b22e0: mov             x1, x0
    // 0x8b22e4: ldur            x2, [fp, #-0x40]
    // 0x8b22e8: stur            x0, [fp, #-0x40]
    // 0x8b22ec: r0 = AudioSessionConfiguration.fromJson()
    //     0x8b22ec: bl              #0x8b2340  ; [package:audio_session/src/core.dart] AudioSessionConfiguration::AudioSessionConfiguration.fromJson
    // 0x8b22f0: ldur            x0, [fp, #-0x40]
    // 0x8b22f4: ldur            x2, [fp, #-0x38]
    // 0x8b22f8: StoreField: r2->field_f = r0
    //     0x8b22f8: stur            w0, [x2, #0xf]
    //     0x8b22fc: ldurb           w16, [x2, #-1]
    //     0x8b2300: ldurb           w17, [x0, #-1]
    //     0x8b2304: and             x16, x17, x16, lsr #2
    //     0x8b2308: tst             x16, HEAP, lsr #32
    //     0x8b230c: b.eq            #0x8b2314
    //     0x8b2310: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8b2314: b               #0x8b231c
    // 0x8b2318: sub             SP, fp, #0x58
    // 0x8b231c: r0 = LoadStaticField(0xc5c)
    //     0x8b231c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b2320: ldr             x0, [x0, #0x18b8]
    // 0x8b2324: cmp             w0, NULL
    // 0x8b2328: b.eq            #0x8b233c
    // 0x8b232c: r0 = ReturnAsyncNotFuture()
    //     0x8b232c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8b2330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b2330: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b2334: b               #0x8b2254
    // 0x8b2338: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8b2338: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8b233c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8b233c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ AudioSession._(/* No info */) {
    // ** addr: 0x8b3124, size: 0x28c
    // 0x8b3124: EnterFrame
    //     0x8b3124: stp             fp, lr, [SP, #-0x10]!
    //     0x8b3128: mov             fp, SP
    // 0x8b312c: AllocStack(0x28)
    //     0x8b312c: sub             SP, SP, #0x28
    // 0x8b3130: SetupParameters(AudioSession this /* r1 => r1, fp-0x8 */)
    //     0x8b3130: stur            x1, [fp, #-8]
    // 0x8b3134: CheckStackOverflow
    //     0x8b3134: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b3138: cmp             SP, x16
    //     0x8b313c: b.ls            #0x8b33a8
    // 0x8b3140: r1 = 1
    //     0x8b3140: movz            x1, #0x1
    // 0x8b3144: r0 = AllocateContext()
    //     0x8b3144: bl              #0xec126c  ; AllocateContextStub
    // 0x8b3148: mov             x2, x0
    // 0x8b314c: ldur            x0, [fp, #-8]
    // 0x8b3150: stur            x2, [fp, #-0x10]
    // 0x8b3154: StoreField: r2->field_f = r0
    //     0x8b3154: stur            w0, [x2, #0xf]
    // 0x8b3158: r1 = Sentinel
    //     0x8b3158: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b315c: StoreField: r0->field_23 = r1
    //     0x8b315c: stur            w1, [x0, #0x23]
    // 0x8b3160: r1 = Null
    //     0x8b3160: mov             x1, NULL
    // 0x8b3164: r0 = AndroidAudioManager()
    //     0x8b3164: bl              #0x8b389c  ; [package:audio_session/src/android.dart] AndroidAudioManager::AndroidAudioManager
    // 0x8b3168: mov             x3, x0
    // 0x8b316c: ldur            x2, [fp, #-8]
    // 0x8b3170: stur            x3, [fp, #-0x18]
    // 0x8b3174: StoreField: r2->field_7 = r0
    //     0x8b3174: stur            w0, [x2, #7]
    //     0x8b3178: ldurb           w16, [x2, #-1]
    //     0x8b317c: ldurb           w17, [x0, #-1]
    //     0x8b3180: and             x16, x17, x16, lsr #2
    //     0x8b3184: tst             x16, HEAP, lsr #32
    //     0x8b3188: b.eq            #0x8b3190
    //     0x8b318c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8b3190: r1 = <AudioSessionConfiguration>
    //     0x8b3190: add             x1, PP, #0xe, lsl #12  ; [pp+0xe0f0] TypeArguments: <AudioSessionConfiguration>
    //     0x8b3194: ldr             x1, [x1, #0xf0]
    // 0x8b3198: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8b3198: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8b319c: r0 = BehaviorSubject()
    //     0x8b319c: bl              #0x8b3488  ; [package:rxdart/src/subjects/behavior_subject.dart] BehaviorSubject::BehaviorSubject
    // 0x8b31a0: ldur            x2, [fp, #-8]
    // 0x8b31a4: StoreField: r2->field_13 = r0
    //     0x8b31a4: stur            w0, [x2, #0x13]
    //     0x8b31a8: ldurb           w16, [x2, #-1]
    //     0x8b31ac: ldurb           w17, [x0, #-1]
    //     0x8b31b0: and             x16, x17, x16, lsr #2
    //     0x8b31b4: tst             x16, HEAP, lsr #32
    //     0x8b31b8: b.eq            #0x8b31c0
    //     0x8b31bc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8b31c0: r1 = <AudioInterruptionEvent>
    //     0x8b31c0: add             x1, PP, #0xe, lsl #12  ; [pp+0xe0f8] TypeArguments: <AudioInterruptionEvent>
    //     0x8b31c4: ldr             x1, [x1, #0xf8]
    // 0x8b31c8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8b31c8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8b31cc: r0 = PublishSubject()
    //     0x8b31cc: bl              #0x8b33bc  ; [package:rxdart/src/subjects/publish_subject.dart] PublishSubject::PublishSubject
    // 0x8b31d0: ldur            x2, [fp, #-8]
    // 0x8b31d4: ArrayStore: r2[0] = r0  ; List_4
    //     0x8b31d4: stur            w0, [x2, #0x17]
    //     0x8b31d8: ldurb           w16, [x2, #-1]
    //     0x8b31dc: ldurb           w17, [x0, #-1]
    //     0x8b31e0: and             x16, x17, x16, lsr #2
    //     0x8b31e4: tst             x16, HEAP, lsr #32
    //     0x8b31e8: b.eq            #0x8b31f0
    //     0x8b31ec: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8b31f0: r1 = <void?>
    //     0x8b31f0: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8b31f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8b31f4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8b31f8: r0 = PublishSubject()
    //     0x8b31f8: bl              #0x8b33bc  ; [package:rxdart/src/subjects/publish_subject.dart] PublishSubject::PublishSubject
    // 0x8b31fc: ldur            x2, [fp, #-8]
    // 0x8b3200: StoreField: r2->field_1b = r0
    //     0x8b3200: stur            w0, [x2, #0x1b]
    //     0x8b3204: ldurb           w16, [x2, #-1]
    //     0x8b3208: ldurb           w17, [x0, #-1]
    //     0x8b320c: and             x16, x17, x16, lsr #2
    //     0x8b3210: tst             x16, HEAP, lsr #32
    //     0x8b3214: b.eq            #0x8b321c
    //     0x8b3218: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8b321c: r1 = <AudioDevicesChangedEvent>
    //     0x8b321c: add             x1, PP, #0xe, lsl #12  ; [pp+0xe100] TypeArguments: <AudioDevicesChangedEvent>
    //     0x8b3220: ldr             x1, [x1, #0x100]
    // 0x8b3224: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8b3224: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8b3228: r0 = PublishSubject()
    //     0x8b3228: bl              #0x8b33bc  ; [package:rxdart/src/subjects/publish_subject.dart] PublishSubject::PublishSubject
    // 0x8b322c: ldur            x3, [fp, #-8]
    // 0x8b3230: StoreField: r3->field_1f = r0
    //     0x8b3230: stur            w0, [x3, #0x1f]
    //     0x8b3234: ldurb           w16, [x3, #-1]
    //     0x8b3238: ldurb           w17, [x0, #-1]
    //     0x8b323c: and             x16, x17, x16, lsr #2
    //     0x8b3240: tst             x16, HEAP, lsr #32
    //     0x8b3244: b.eq            #0x8b324c
    //     0x8b3248: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8b324c: ldur            x2, [fp, #-0x10]
    // 0x8b3250: r1 = Function '<anonymous closure>':.
    //     0x8b3250: add             x1, PP, #0xe, lsl #12  ; [pp+0xe108] AnonymousClosure: (0x8b5088), in [package:audio_session/src/core.dart] AudioSession::AudioSession._ (0x8b3124)
    //     0x8b3254: ldr             x1, [x1, #0x108]
    // 0x8b3258: r0 = AllocateClosure()
    //     0x8b3258: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b325c: str             x0, [SP]
    // 0x8b3260: r1 = <Set<AudioDevice>>
    //     0x8b3260: add             x1, PP, #0xe, lsl #12  ; [pp+0xe110] TypeArguments: <Set<AudioDevice>>
    //     0x8b3264: ldr             x1, [x1, #0x110]
    // 0x8b3268: r4 = const [0, 0x2, 0x1, 0x1, onListen, 0x1, null]
    //     0x8b3268: ldr             x4, [PP, #0x32c0]  ; [pp+0x32c0] List(7) [0, 0x2, 0x1, 0x1, "onListen", 0x1, Null]
    // 0x8b326c: r0 = BehaviorSubject()
    //     0x8b326c: bl              #0x8b3488  ; [package:rxdart/src/subjects/behavior_subject.dart] BehaviorSubject::BehaviorSubject
    // 0x8b3270: mov             x1, x0
    // 0x8b3274: ldur            x0, [fp, #-8]
    // 0x8b3278: stur            x1, [fp, #-0x20]
    // 0x8b327c: LoadField: r2 = r0->field_23
    //     0x8b327c: ldur            w2, [x0, #0x23]
    // 0x8b3280: DecompressPointer r2
    //     0x8b3280: add             x2, x2, HEAP, lsl #32
    // 0x8b3284: r16 = Sentinel
    //     0x8b3284: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b3288: cmp             w2, w16
    // 0x8b328c: b.ne            #0x8b3298
    // 0x8b3290: mov             x1, x0
    // 0x8b3294: b               #0x8b32ac
    // 0x8b3298: r16 = "_devicesSubject@767445051"
    //     0x8b3298: add             x16, PP, #0xe, lsl #12  ; [pp+0xe118] "_devicesSubject@767445051"
    //     0x8b329c: ldr             x16, [x16, #0x118]
    // 0x8b32a0: str             x16, [SP]
    // 0x8b32a4: r0 = _throwFieldAlreadyInitialized()
    //     0x8b32a4: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x8b32a8: ldur            x1, [fp, #-8]
    // 0x8b32ac: ldur            x2, [fp, #-0x18]
    // 0x8b32b0: ldur            x0, [fp, #-0x20]
    // 0x8b32b4: StoreField: r1->field_23 = r0
    //     0x8b32b4: stur            w0, [x1, #0x23]
    //     0x8b32b8: ldurb           w16, [x1, #-1]
    //     0x8b32bc: ldurb           w17, [x0, #-1]
    //     0x8b32c0: and             x16, x17, x16, lsr #2
    //     0x8b32c4: tst             x16, HEAP, lsr #32
    //     0x8b32c8: b.eq            #0x8b32d0
    //     0x8b32cc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b32d0: LoadField: r0 = r2->field_b
    //     0x8b32d0: ldur            w0, [x2, #0xb]
    // 0x8b32d4: DecompressPointer r0
    //     0x8b32d4: add             x0, x0, HEAP, lsl #32
    // 0x8b32d8: stur            x0, [fp, #-8]
    // 0x8b32dc: LoadField: r1 = r0->field_7
    //     0x8b32dc: ldur            w1, [x0, #7]
    // 0x8b32e0: DecompressPointer r1
    //     0x8b32e0: add             x1, x1, HEAP, lsl #32
    // 0x8b32e4: r0 = _SubjectStream()
    //     0x8b32e4: bl              #0x8b33b0  ; Allocate_SubjectStreamStub -> _SubjectStream<X0> (size=0x10)
    // 0x8b32e8: mov             x3, x0
    // 0x8b32ec: ldur            x0, [fp, #-8]
    // 0x8b32f0: stur            x3, [fp, #-0x20]
    // 0x8b32f4: StoreField: r3->field_b = r0
    //     0x8b32f4: stur            w0, [x3, #0xb]
    // 0x8b32f8: ldur            x2, [fp, #-0x10]
    // 0x8b32fc: r1 = Function '<anonymous closure>':.
    //     0x8b32fc: add             x1, PP, #0xe, lsl #12  ; [pp+0xe120] AnonymousClosure: (0x8b4fb0), in [package:audio_session/src/core.dart] AudioSession::AudioSession._ (0x8b3124)
    //     0x8b3300: ldr             x1, [x1, #0x120]
    // 0x8b3304: r0 = AllocateClosure()
    //     0x8b3304: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b3308: ldur            x1, [fp, #-0x20]
    // 0x8b330c: mov             x2, x0
    // 0x8b3310: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8b3310: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8b3314: r0 = listen()
    //     0x8b3314: bl              #0xd110cc  ; [package:rxdart/src/subjects/subject.dart] _SubjectStream::listen
    // 0x8b3318: ldur            x2, [fp, #-0x10]
    // 0x8b331c: r1 = Function '<anonymous closure>':.
    //     0x8b331c: add             x1, PP, #0xe, lsl #12  ; [pp+0xe128] AnonymousClosure: (0x8b4e34), in [package:audio_session/src/core.dart] AudioSession::AudioSession._ (0x8b3124)
    //     0x8b3320: ldr             x1, [x1, #0x128]
    // 0x8b3324: r0 = AllocateClosure()
    //     0x8b3324: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b3328: ldur            x3, [fp, #-0x18]
    // 0x8b332c: StoreField: r3->field_13 = r0
    //     0x8b332c: stur            w0, [x3, #0x13]
    //     0x8b3330: ldurb           w16, [x3, #-1]
    //     0x8b3334: ldurb           w17, [x0, #-1]
    //     0x8b3338: and             x16, x17, x16, lsr #2
    //     0x8b333c: tst             x16, HEAP, lsr #32
    //     0x8b3340: b.eq            #0x8b3348
    //     0x8b3344: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8b3348: ldur            x2, [fp, #-0x10]
    // 0x8b334c: r1 = Function '<anonymous closure>':.
    //     0x8b334c: add             x1, PP, #0xe, lsl #12  ; [pp+0xe130] AnonymousClosure: (0x8b4744), in [package:audio_session/src/core.dart] AudioSession::AudioSession._ (0x8b3124)
    //     0x8b3350: ldr             x1, [x1, #0x130]
    // 0x8b3354: r0 = AllocateClosure()
    //     0x8b3354: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b3358: ldur            x1, [fp, #-0x18]
    // 0x8b335c: ArrayStore: r1[0] = r0  ; List_4
    //     0x8b335c: stur            w0, [x1, #0x17]
    //     0x8b3360: ldurb           w16, [x1, #-1]
    //     0x8b3364: ldurb           w17, [x0, #-1]
    //     0x8b3368: and             x16, x17, x16, lsr #2
    //     0x8b336c: tst             x16, HEAP, lsr #32
    //     0x8b3370: b.eq            #0x8b3378
    //     0x8b3374: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b3378: ldur            x2, [fp, #-0x10]
    // 0x8b337c: r1 = Function '<anonymous closure>':.
    //     0x8b337c: add             x1, PP, #0xe, lsl #12  ; [pp+0xe138] AnonymousClosure: (0x8b45a0), in [package:audio_session/src/core.dart] AudioSession::AudioSession._ (0x8b3124)
    //     0x8b3380: ldr             x1, [x1, #0x138]
    // 0x8b3384: r0 = AllocateClosure()
    //     0x8b3384: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b3388: mov             x2, x0
    // 0x8b338c: r1 = Instance_MethodChannel
    //     0x8b338c: add             x1, PP, #0xd, lsl #12  ; [pp+0xdef0] Obj!MethodChannel@e11151
    //     0x8b3390: ldr             x1, [x1, #0xef0]
    // 0x8b3394: r0 = setMethodCallHandler()
    //     0x8b3394: bl              #0x6921f4  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::setMethodCallHandler
    // 0x8b3398: r0 = Null
    //     0x8b3398: mov             x0, NULL
    // 0x8b339c: LeaveFrame
    //     0x8b339c: mov             SP, fp
    //     0x8b33a0: ldp             fp, lr, [SP], #0x10
    // 0x8b33a4: ret
    //     0x8b33a4: ret             
    // 0x8b33a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b33a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b33ac: b               #0x8b3140
  }
  [closure] Future<Null> <anonymous closure>(dynamic, MethodCall) async {
    // ** addr: 0x8b45a0, size: 0x1a4
    // 0x8b45a0: EnterFrame
    //     0x8b45a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8b45a4: mov             fp, SP
    // 0x8b45a8: AllocStack(0x30)
    //     0x8b45a8: sub             SP, SP, #0x30
    // 0x8b45ac: SetupParameters(AudioSession this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x8b45ac: stur            NULL, [fp, #-8]
    //     0x8b45b0: movz            x0, #0
    //     0x8b45b4: add             x1, fp, w0, sxtw #2
    //     0x8b45b8: ldr             x1, [x1, #0x18]
    //     0x8b45bc: add             x2, fp, w0, sxtw #2
    //     0x8b45c0: ldr             x2, [x2, #0x10]
    //     0x8b45c4: stur            x2, [fp, #-0x18]
    //     0x8b45c8: ldur            w3, [x1, #0x17]
    //     0x8b45cc: add             x3, x3, HEAP, lsl #32
    //     0x8b45d0: stur            x3, [fp, #-0x10]
    // 0x8b45d4: CheckStackOverflow
    //     0x8b45d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b45d8: cmp             SP, x16
    //     0x8b45dc: b.ls            #0x8b4738
    // 0x8b45e0: InitAsync() -> Future<Null?>
    //     0x8b45e0: ldr             x0, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    //     0x8b45e4: bl              #0x661298  ; InitAsyncStub
    // 0x8b45e8: ldur            x3, [fp, #-0x18]
    // 0x8b45ec: LoadField: r4 = r3->field_b
    //     0x8b45ec: ldur            w4, [x3, #0xb]
    // 0x8b45f0: DecompressPointer r4
    //     0x8b45f0: add             x4, x4, HEAP, lsl #32
    // 0x8b45f4: mov             x0, x4
    // 0x8b45f8: stur            x4, [fp, #-0x20]
    // 0x8b45fc: r2 = Null
    //     0x8b45fc: mov             x2, NULL
    // 0x8b4600: r1 = Null
    //     0x8b4600: mov             x1, NULL
    // 0x8b4604: r4 = 60
    //     0x8b4604: movz            x4, #0x3c
    // 0x8b4608: branchIfSmi(r0, 0x8b4614)
    //     0x8b4608: tbz             w0, #0, #0x8b4614
    // 0x8b460c: r4 = LoadClassIdInstr(r0)
    //     0x8b460c: ldur            x4, [x0, #-1]
    //     0x8b4610: ubfx            x4, x4, #0xc, #0x14
    // 0x8b4614: sub             x4, x4, #0x5a
    // 0x8b4618: cmp             x4, #2
    // 0x8b461c: b.ls            #0x8b4634
    // 0x8b4620: r8 = List?
    //     0x8b4620: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x8b4624: ldr             x8, [x8, #0x140]
    // 0x8b4628: r3 = Null
    //     0x8b4628: add             x3, PP, #0xe, lsl #12  ; [pp+0xe148] Null
    //     0x8b462c: ldr             x3, [x3, #0x148]
    // 0x8b4630: r0 = List?()
    //     0x8b4630: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x8b4634: ldur            x0, [fp, #-0x18]
    // 0x8b4638: LoadField: r1 = r0->field_7
    //     0x8b4638: ldur            w1, [x0, #7]
    // 0x8b463c: DecompressPointer r1
    //     0x8b463c: add             x1, x1, HEAP, lsl #32
    // 0x8b4640: r16 = "onConfigurationChanged"
    //     0x8b4640: add             x16, PP, #0xe, lsl #12  ; [pp+0xe158] "onConfigurationChanged"
    //     0x8b4644: ldr             x16, [x16, #0x158]
    // 0x8b4648: stp             x1, x16, [SP]
    // 0x8b464c: r0 = ==()
    //     0x8b464c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8b4650: tbnz            w0, #4, #0x8b4730
    // 0x8b4654: ldur            x1, [fp, #-0x10]
    // 0x8b4658: ldur            x0, [fp, #-0x20]
    // 0x8b465c: LoadField: r2 = r1->field_f
    //     0x8b465c: ldur            w2, [x1, #0xf]
    // 0x8b4660: DecompressPointer r2
    //     0x8b4660: add             x2, x2, HEAP, lsl #32
    // 0x8b4664: stur            x2, [fp, #-0x18]
    // 0x8b4668: LoadField: r1 = r2->field_13
    //     0x8b4668: ldur            w1, [x2, #0x13]
    // 0x8b466c: DecompressPointer r1
    //     0x8b466c: add             x1, x1, HEAP, lsl #32
    // 0x8b4670: stur            x1, [fp, #-0x10]
    // 0x8b4674: cmp             w0, NULL
    // 0x8b4678: b.eq            #0x8b4740
    // 0x8b467c: r3 = LoadClassIdInstr(r0)
    //     0x8b467c: ldur            x3, [x0, #-1]
    //     0x8b4680: ubfx            x3, x3, #0xc, #0x14
    // 0x8b4684: stp             xzr, x0, [SP]
    // 0x8b4688: mov             x0, x3
    // 0x8b468c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b468c: movz            x17, #0x3037
    //     0x8b4690: movk            x17, #0x1, lsl #16
    //     0x8b4694: add             lr, x0, x17
    //     0x8b4698: ldr             lr, [x21, lr, lsl #3]
    //     0x8b469c: blr             lr
    // 0x8b46a0: mov             x3, x0
    // 0x8b46a4: r2 = Null
    //     0x8b46a4: mov             x2, NULL
    // 0x8b46a8: r1 = Null
    //     0x8b46a8: mov             x1, NULL
    // 0x8b46ac: stur            x3, [fp, #-0x20]
    // 0x8b46b0: r8 = Map
    //     0x8b46b0: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0x8b46b4: r3 = Null
    //     0x8b46b4: add             x3, PP, #0xe, lsl #12  ; [pp+0xe160] Null
    //     0x8b46b8: ldr             x3, [x3, #0x160]
    // 0x8b46bc: r0 = Map()
    //     0x8b46bc: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0x8b46c0: ldur            x0, [fp, #-0x20]
    // 0x8b46c4: r1 = LoadClassIdInstr(r0)
    //     0x8b46c4: ldur            x1, [x0, #-1]
    //     0x8b46c8: ubfx            x1, x1, #0xc, #0x14
    // 0x8b46cc: r16 = <String, dynamic>
    //     0x8b46cc: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x8b46d0: stp             x0, x16, [SP]
    // 0x8b46d4: mov             x0, x1
    // 0x8b46d8: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x8b46d8: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x8b46dc: r0 = GDT[cid_x0 + 0x5f4]()
    //     0x8b46dc: add             lr, x0, #0x5f4
    //     0x8b46e0: ldr             lr, [x21, lr, lsl #3]
    //     0x8b46e4: blr             lr
    // 0x8b46e8: stur            x0, [fp, #-0x20]
    // 0x8b46ec: r0 = AudioSessionConfiguration()
    //     0x8b46ec: bl              #0x8b3118  ; AllocateAudioSessionConfigurationStub -> AudioSessionConfiguration (size=0x28)
    // 0x8b46f0: mov             x1, x0
    // 0x8b46f4: ldur            x2, [fp, #-0x20]
    // 0x8b46f8: stur            x0, [fp, #-0x20]
    // 0x8b46fc: r0 = AudioSessionConfiguration.fromJson()
    //     0x8b46fc: bl              #0x8b2340  ; [package:audio_session/src/core.dart] AudioSessionConfiguration::AudioSessionConfiguration.fromJson
    // 0x8b4700: ldur            x0, [fp, #-0x20]
    // 0x8b4704: ldur            x1, [fp, #-0x18]
    // 0x8b4708: StoreField: r1->field_f = r0
    //     0x8b4708: stur            w0, [x1, #0xf]
    //     0x8b470c: ldurb           w16, [x1, #-1]
    //     0x8b4710: ldurb           w17, [x0, #-1]
    //     0x8b4714: and             x16, x17, x16, lsr #2
    //     0x8b4718: tst             x16, HEAP, lsr #32
    //     0x8b471c: b.eq            #0x8b4724
    //     0x8b4720: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b4724: ldur            x1, [fp, #-0x10]
    // 0x8b4728: ldur            x2, [fp, #-0x20]
    // 0x8b472c: r0 = add()
    //     0x8b472c: bl              #0xc7588c  ; [package:rxdart/src/subjects/subject.dart] Subject::add
    // 0x8b4730: r0 = Null
    //     0x8b4730: mov             x0, NULL
    // 0x8b4734: r0 = ReturnAsyncNotFuture()
    //     0x8b4734: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8b4738: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b4738: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b473c: b               #0x8b45e0
    // 0x8b4740: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8b4740: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic, List<AndroidAudioDeviceInfo>) async {
    // ** addr: 0x8b4744, size: 0x19c
    // 0x8b4744: EnterFrame
    //     0x8b4744: stp             fp, lr, [SP, #-0x10]!
    //     0x8b4748: mov             fp, SP
    // 0x8b474c: AllocStack(0x38)
    //     0x8b474c: sub             SP, SP, #0x38
    // 0x8b4750: SetupParameters(AudioSession this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x8b4750: stur            NULL, [fp, #-8]
    //     0x8b4754: movz            x0, #0
    //     0x8b4758: add             x1, fp, w0, sxtw #2
    //     0x8b475c: ldr             x1, [x1, #0x18]
    //     0x8b4760: add             x2, fp, w0, sxtw #2
    //     0x8b4764: ldr             x2, [x2, #0x10]
    //     0x8b4768: stur            x2, [fp, #-0x18]
    //     0x8b476c: ldur            w3, [x1, #0x17]
    //     0x8b4770: add             x3, x3, HEAP, lsl #32
    //     0x8b4774: stur            x3, [fp, #-0x10]
    // 0x8b4778: CheckStackOverflow
    //     0x8b4778: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b477c: cmp             SP, x16
    //     0x8b4780: b.ls            #0x8b48c0
    // 0x8b4784: InitAsync() -> Future<void?>
    //     0x8b4784: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8b4788: bl              #0x661298  ; InitAsyncStub
    // 0x8b478c: ldur            x0, [fp, #-0x10]
    // 0x8b4790: LoadField: r1 = r0->field_f
    //     0x8b4790: ldur            w1, [x0, #0xf]
    // 0x8b4794: DecompressPointer r1
    //     0x8b4794: add             x1, x1, HEAP, lsl #32
    // 0x8b4798: LoadField: r2 = r1->field_1f
    //     0x8b4798: ldur            w2, [x1, #0x1f]
    // 0x8b479c: DecompressPointer r2
    //     0x8b479c: add             x2, x2, HEAP, lsl #32
    // 0x8b47a0: stur            x2, [fp, #-0x20]
    // 0x8b47a4: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x8b47a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b47a8: ldr             x0, [x0, #0x778]
    //     0x8b47ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b47b0: cmp             w0, w16
    //     0x8b47b4: b.ne            #0x8b47c0
    //     0x8b47b8: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x8b47bc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8b47c0: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x8b47c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b47c4: ldr             x0, [x0, #0x780]
    //     0x8b47c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b47cc: cmp             w0, w16
    //     0x8b47d0: b.ne            #0x8b47dc
    //     0x8b47d4: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x8b47d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8b47dc: ldur            x0, [fp, #-0x18]
    // 0x8b47e0: r1 = LoadClassIdInstr(r0)
    //     0x8b47e0: ldur            x1, [x0, #-1]
    //     0x8b47e4: ubfx            x1, x1, #0xc, #0x14
    // 0x8b47e8: r16 = <AudioDevice>
    //     0x8b47e8: add             x16, PP, #0xe, lsl #12  ; [pp+0xe170] TypeArguments: <AudioDevice>
    //     0x8b47ec: ldr             x16, [x16, #0x170]
    // 0x8b47f0: stp             x0, x16, [SP, #8]
    // 0x8b47f4: r16 = Closure: (AndroidAudioDeviceInfo) => AudioDevice from Function '_androidDevice2device@767445051': static.
    //     0x8b47f4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe178] Closure: (AndroidAudioDeviceInfo) => AudioDevice from Function '_androidDevice2device@767445051': static. (0x7e54fb2b4b8c)
    //     0x8b47f8: ldr             x16, [x16, #0x178]
    // 0x8b47fc: str             x16, [SP]
    // 0x8b4800: mov             x0, x1
    // 0x8b4804: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8b4804: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8b4808: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8b4808: movz            x17, #0xf28c
    //     0x8b480c: add             lr, x0, x17
    //     0x8b4810: ldr             lr, [x21, lr, lsl #3]
    //     0x8b4814: blr             lr
    // 0x8b4818: r1 = LoadClassIdInstr(r0)
    //     0x8b4818: ldur            x1, [x0, #-1]
    //     0x8b481c: ubfx            x1, x1, #0xc, #0x14
    // 0x8b4820: mov             x16, x0
    // 0x8b4824: mov             x0, x1
    // 0x8b4828: mov             x1, x16
    // 0x8b482c: r0 = GDT[cid_x0 + 0xf3fc]()
    //     0x8b482c: movz            x17, #0xf3fc
    //     0x8b4830: add             lr, x0, x17
    //     0x8b4834: ldr             lr, [x21, lr, lsl #3]
    //     0x8b4838: blr             lr
    // 0x8b483c: r0 = AudioDevicesChangedEvent()
    //     0x8b483c: bl              #0x8b4b5c  ; AllocateAudioDevicesChangedEventStub -> AudioDevicesChangedEvent (size=0x8)
    // 0x8b4840: ldur            x1, [fp, #-0x20]
    // 0x8b4844: mov             x2, x0
    // 0x8b4848: r0 = add()
    //     0x8b4848: bl              #0xc7588c  ; [package:rxdart/src/subjects/subject.dart] Subject::add
    // 0x8b484c: ldur            x0, [fp, #-0x10]
    // 0x8b4850: LoadField: r1 = r0->field_f
    //     0x8b4850: ldur            w1, [x0, #0xf]
    // 0x8b4854: DecompressPointer r1
    //     0x8b4854: add             x1, x1, HEAP, lsl #32
    // 0x8b4858: LoadField: r2 = r1->field_23
    //     0x8b4858: ldur            w2, [x1, #0x23]
    // 0x8b485c: DecompressPointer r2
    //     0x8b485c: add             x2, x2, HEAP, lsl #32
    // 0x8b4860: r16 = Sentinel
    //     0x8b4860: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b4864: cmp             w2, w16
    // 0x8b4868: b.eq            #0x8b48c8
    // 0x8b486c: mov             x1, x2
    // 0x8b4870: r0 = hasListener()
    //     0x8b4870: bl              #0xd2d6d0  ; [package:rxdart/src/subjects/subject.dart] Subject::hasListener
    // 0x8b4874: tbnz            w0, #4, #0x8b48b8
    // 0x8b4878: ldur            x0, [fp, #-0x10]
    // 0x8b487c: LoadField: r1 = r0->field_f
    //     0x8b487c: ldur            w1, [x0, #0xf]
    // 0x8b4880: DecompressPointer r1
    //     0x8b4880: add             x1, x1, HEAP, lsl #32
    // 0x8b4884: LoadField: r2 = r1->field_23
    //     0x8b4884: ldur            w2, [x1, #0x23]
    // 0x8b4888: DecompressPointer r2
    //     0x8b4888: add             x2, x2, HEAP, lsl #32
    // 0x8b488c: r16 = Sentinel
    //     0x8b488c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b4890: cmp             w2, w16
    // 0x8b4894: b.eq            #0x8b48d4
    // 0x8b4898: stur            x2, [fp, #-0x18]
    // 0x8b489c: r0 = getDevices()
    //     0x8b489c: bl              #0x8b48e0  ; [package:audio_session/src/core.dart] AudioSession::getDevices
    // 0x8b48a0: mov             x1, x0
    // 0x8b48a4: stur            x1, [fp, #-0x20]
    // 0x8b48a8: r0 = Await()
    //     0x8b48a8: bl              #0x661044  ; AwaitStub
    // 0x8b48ac: ldur            x1, [fp, #-0x18]
    // 0x8b48b0: mov             x2, x0
    // 0x8b48b4: r0 = add()
    //     0x8b48b4: bl              #0xc7588c  ; [package:rxdart/src/subjects/subject.dart] Subject::add
    // 0x8b48b8: r0 = Null
    //     0x8b48b8: mov             x0, NULL
    // 0x8b48bc: r0 = ReturnAsyncNotFuture()
    //     0x8b48bc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8b48c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b48c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b48c4: b               #0x8b4784
    // 0x8b48c8: r9 = _devicesSubject
    //     0x8b48c8: add             x9, PP, #0xe, lsl #12  ; [pp+0xe180] Field <AudioSession._devicesSubject@767445051>: late final (offset: 0x24)
    //     0x8b48cc: ldr             x9, [x9, #0x180]
    // 0x8b48d0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8b48d0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8b48d4: r9 = _devicesSubject
    //     0x8b48d4: add             x9, PP, #0xe, lsl #12  ; [pp+0xe180] Field <AudioSession._devicesSubject@767445051>: late final (offset: 0x24)
    //     0x8b48d8: ldr             x9, [x9, #0x180]
    // 0x8b48dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8b48dc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ getDevices(/* No info */) async {
    // ** addr: 0x8b48e0, size: 0x164
    // 0x8b48e0: EnterFrame
    //     0x8b48e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8b48e4: mov             fp, SP
    // 0x8b48e8: AllocStack(0x38)
    //     0x8b48e8: sub             SP, SP, #0x38
    // 0x8b48ec: SetupParameters(AudioSession this /* r1 => r1, fp-0x10 */)
    //     0x8b48ec: stur            NULL, [fp, #-8]
    //     0x8b48f0: stur            x1, [fp, #-0x10]
    // 0x8b48f4: CheckStackOverflow
    //     0x8b48f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b48f8: cmp             SP, x16
    //     0x8b48fc: b.ls            #0x8b4a3c
    // 0x8b4900: InitAsync() -> Future<Set<AudioDevice>>
    //     0x8b4900: add             x0, PP, #0xe, lsl #12  ; [pp+0xe110] TypeArguments: <Set<AudioDevice>>
    //     0x8b4904: ldr             x0, [x0, #0x110]
    //     0x8b4908: bl              #0x661298  ; InitAsyncStub
    // 0x8b490c: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x8b490c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b4910: ldr             x0, [x0, #0x778]
    //     0x8b4914: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b4918: cmp             w0, w16
    //     0x8b491c: b.ne            #0x8b4928
    //     0x8b4920: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x8b4924: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8b4928: r1 = <AudioDevice>
    //     0x8b4928: add             x1, PP, #0xe, lsl #12  ; [pp+0xe170] TypeArguments: <AudioDevice>
    //     0x8b492c: ldr             x1, [x1, #0x170]
    // 0x8b4930: stur            x0, [fp, #-0x18]
    // 0x8b4934: r0 = _Set()
    //     0x8b4934: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x8b4938: mov             x1, x0
    // 0x8b493c: ldur            x0, [fp, #-0x18]
    // 0x8b4940: stur            x1, [fp, #-0x20]
    // 0x8b4944: StoreField: r1->field_1b = r0
    //     0x8b4944: stur            w0, [x1, #0x1b]
    // 0x8b4948: StoreField: r1->field_b = rZR
    //     0x8b4948: stur            wzr, [x1, #0xb]
    // 0x8b494c: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x8b494c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b4950: ldr             x0, [x0, #0x780]
    //     0x8b4954: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b4958: cmp             w0, w16
    //     0x8b495c: b.ne            #0x8b4968
    //     0x8b4960: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x8b4964: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8b4968: mov             x1, x0
    // 0x8b496c: ldur            x0, [fp, #-0x20]
    // 0x8b4970: StoreField: r0->field_f = r1
    //     0x8b4970: stur            w1, [x0, #0xf]
    // 0x8b4974: StoreField: r0->field_13 = rZR
    //     0x8b4974: stur            wzr, [x0, #0x13]
    // 0x8b4978: ArrayStore: r0[0] = rZR  ; List_4
    //     0x8b4978: stur            wzr, [x0, #0x17]
    // 0x8b497c: r1 = Instance_AndroidGetAudioDevicesFlags
    //     0x8b497c: add             x1, PP, #0xe, lsl #12  ; [pp+0xe250] Obj!AndroidGetAudioDevicesFlags@e26171
    //     0x8b4980: ldr             x1, [x1, #0x250]
    // 0x8b4984: r2 = Instance_AndroidGetAudioDevicesFlags
    //     0x8b4984: add             x2, PP, #0xe, lsl #12  ; [pp+0xe258] Obj!AndroidGetAudioDevicesFlags@e26161
    //     0x8b4988: ldr             x2, [x2, #0x258]
    // 0x8b498c: r0 = |()
    //     0x8b498c: bl              #0x8b4b1c  ; [package:audio_session/src/android.dart] AndroidGetAudioDevicesFlags::|
    // 0x8b4990: mov             x1, x0
    // 0x8b4994: r2 = Instance_AndroidGetAudioDevicesFlags
    //     0x8b4994: add             x2, PP, #0xe, lsl #12  ; [pp+0xe260] Obj!AndroidGetAudioDevicesFlags@e26151
    //     0x8b4998: ldr             x2, [x2, #0x260]
    // 0x8b499c: r0 = |()
    //     0x8b499c: bl              #0x8b4b1c  ; [package:audio_session/src/android.dart] AndroidGetAudioDevicesFlags::|
    // 0x8b49a0: mov             x1, x0
    // 0x8b49a4: ldur            x0, [fp, #-0x10]
    // 0x8b49a8: LoadField: r2 = r0->field_7
    //     0x8b49a8: ldur            w2, [x0, #7]
    // 0x8b49ac: DecompressPointer r2
    //     0x8b49ac: add             x2, x2, HEAP, lsl #32
    // 0x8b49b0: mov             x16, x1
    // 0x8b49b4: mov             x1, x2
    // 0x8b49b8: mov             x2, x16
    // 0x8b49bc: r0 = getDevices()
    //     0x8b49bc: bl              #0x8b4a44  ; [package:audio_session/src/android.dart] AndroidAudioManager::getDevices
    // 0x8b49c0: mov             x1, x0
    // 0x8b49c4: stur            x1, [fp, #-0x10]
    // 0x8b49c8: r0 = Await()
    //     0x8b49c8: bl              #0x661044  ; AwaitStub
    // 0x8b49cc: r1 = LoadClassIdInstr(r0)
    //     0x8b49cc: ldur            x1, [x0, #-1]
    //     0x8b49d0: ubfx            x1, x1, #0xc, #0x14
    // 0x8b49d4: r16 = <AudioDevice>
    //     0x8b49d4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe170] TypeArguments: <AudioDevice>
    //     0x8b49d8: ldr             x16, [x16, #0x170]
    // 0x8b49dc: stp             x0, x16, [SP, #8]
    // 0x8b49e0: r16 = Closure: (AndroidAudioDeviceInfo) => AudioDevice from Function '_androidDevice2device@767445051': static.
    //     0x8b49e0: add             x16, PP, #0xe, lsl #12  ; [pp+0xe178] Closure: (AndroidAudioDeviceInfo) => AudioDevice from Function '_androidDevice2device@767445051': static. (0x7e54fb2b4b8c)
    //     0x8b49e4: ldr             x16, [x16, #0x178]
    // 0x8b49e8: str             x16, [SP]
    // 0x8b49ec: mov             x0, x1
    // 0x8b49f0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8b49f0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8b49f4: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8b49f4: movz            x17, #0xf28c
    //     0x8b49f8: add             lr, x0, x17
    //     0x8b49fc: ldr             lr, [x21, lr, lsl #3]
    //     0x8b4a00: blr             lr
    // 0x8b4a04: r1 = LoadClassIdInstr(r0)
    //     0x8b4a04: ldur            x1, [x0, #-1]
    //     0x8b4a08: ubfx            x1, x1, #0xc, #0x14
    // 0x8b4a0c: mov             x16, x0
    // 0x8b4a10: mov             x0, x1
    // 0x8b4a14: mov             x1, x16
    // 0x8b4a18: r0 = GDT[cid_x0 + 0xf3fc]()
    //     0x8b4a18: movz            x17, #0xf3fc
    //     0x8b4a1c: add             lr, x0, x17
    //     0x8b4a20: ldr             lr, [x21, lr, lsl #3]
    //     0x8b4a24: blr             lr
    // 0x8b4a28: ldur            x1, [fp, #-0x20]
    // 0x8b4a2c: mov             x2, x0
    // 0x8b4a30: r0 = addAll()
    //     0x8b4a30: bl              #0xc1ec5c  ; [dart:_compact_hash] _Set::addAll
    // 0x8b4a34: ldur            x0, [fp, #-0x20]
    // 0x8b4a38: r0 = ReturnAsyncNotFuture()
    //     0x8b4a38: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8b4a3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b4a3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b4a40: b               #0x8b4900
  }
  [closure] static AudioDevice _androidDevice2device(dynamic, AndroidAudioDeviceInfo) {
    // ** addr: 0x8b4b8c, size: 0x30
    // 0x8b4b8c: EnterFrame
    //     0x8b4b8c: stp             fp, lr, [SP, #-0x10]!
    //     0x8b4b90: mov             fp, SP
    // 0x8b4b94: CheckStackOverflow
    //     0x8b4b94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b4b98: cmp             SP, x16
    //     0x8b4b9c: b.ls            #0x8b4bb4
    // 0x8b4ba0: ldr             x1, [fp, #0x10]
    // 0x8b4ba4: r0 = _androidDevice2device()
    //     0x8b4ba4: bl              #0x8b4bbc  ; [package:audio_session/src/core.dart] AudioSession::_androidDevice2device
    // 0x8b4ba8: LeaveFrame
    //     0x8b4ba8: mov             SP, fp
    //     0x8b4bac: ldp             fp, lr, [SP], #0x10
    // 0x8b4bb0: ret
    //     0x8b4bb0: ret             
    // 0x8b4bb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b4bb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b4bb8: b               #0x8b4ba0
  }
  static _ _androidDevice2device(/* No info */) {
    // ** addr: 0x8b4bbc, size: 0x248
    // 0x8b4bbc: EnterFrame
    //     0x8b4bbc: stp             fp, lr, [SP, #-0x10]!
    //     0x8b4bc0: mov             fp, SP
    // 0x8b4bc4: AllocStack(0x30)
    //     0x8b4bc4: sub             SP, SP, #0x30
    // 0x8b4bc8: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0x8b4bc8: mov             x2, x1
    //     0x8b4bcc: stur            x1, [fp, #-8]
    // 0x8b4bd0: CheckStackOverflow
    //     0x8b4bd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b4bd4: cmp             SP, x16
    //     0x8b4bd8: b.ls            #0x8b4dfc
    // 0x8b4bdc: LoadField: r3 = r2->field_7
    //     0x8b4bdc: ldur            x3, [x2, #7]
    // 0x8b4be0: r0 = BoxInt64Instr(r3)
    //     0x8b4be0: sbfiz           x0, x3, #1, #0x1f
    //     0x8b4be4: cmp             x3, x0, asr #1
    //     0x8b4be8: b.eq            #0x8b4bf4
    //     0x8b4bec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8b4bf0: stur            x3, [x0, #7]
    // 0x8b4bf4: r1 = 60
    //     0x8b4bf4: movz            x1, #0x3c
    // 0x8b4bf8: branchIfSmi(r0, 0x8b4c04)
    //     0x8b4bf8: tbz             w0, #0, #0x8b4c04
    // 0x8b4bfc: r1 = LoadClassIdInstr(r0)
    //     0x8b4bfc: ldur            x1, [x0, #-1]
    //     0x8b4c00: ubfx            x1, x1, #0xc, #0x14
    // 0x8b4c04: str             x0, [SP]
    // 0x8b4c08: mov             x0, x1
    // 0x8b4c0c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x8b4c0c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x8b4c10: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x8b4c10: movz            x17, #0x2b03
    //     0x8b4c14: add             lr, x0, x17
    //     0x8b4c18: ldr             lr, [x21, lr, lsl #3]
    //     0x8b4c1c: blr             lr
    // 0x8b4c20: mov             x2, x0
    // 0x8b4c24: ldur            x0, [fp, #-8]
    // 0x8b4c28: stur            x2, [fp, #-0x28]
    // 0x8b4c2c: LoadField: r3 = r0->field_f
    //     0x8b4c2c: ldur            w3, [x0, #0xf]
    // 0x8b4c30: DecompressPointer r3
    //     0x8b4c30: add             x3, x3, HEAP, lsl #32
    // 0x8b4c34: stur            x3, [fp, #-0x20]
    // 0x8b4c38: LoadField: r4 = r0->field_13
    //     0x8b4c38: ldur            w4, [x0, #0x13]
    // 0x8b4c3c: DecompressPointer r4
    //     0x8b4c3c: add             x4, x4, HEAP, lsl #32
    // 0x8b4c40: stur            x4, [fp, #-0x18]
    // 0x8b4c44: ArrayLoad: r5 = r0[0]  ; List_4
    //     0x8b4c44: ldur            w5, [x0, #0x17]
    // 0x8b4c48: DecompressPointer r5
    //     0x8b4c48: add             x5, x5, HEAP, lsl #32
    // 0x8b4c4c: stur            x5, [fp, #-0x10]
    // 0x8b4c50: LoadField: r1 = r0->field_1b
    //     0x8b4c50: ldur            w1, [x0, #0x1b]
    // 0x8b4c54: DecompressPointer r1
    //     0x8b4c54: add             x1, x1, HEAP, lsl #32
    // 0x8b4c58: LoadField: r6 = r1->field_7
    //     0x8b4c58: ldur            x6, [x1, #7]
    // 0x8b4c5c: r0 = BoxInt64Instr(r6)
    //     0x8b4c5c: sbfiz           x0, x6, #1, #0x1f
    //     0x8b4c60: cmp             x6, x0, asr #1
    //     0x8b4c64: b.eq            #0x8b4c70
    //     0x8b4c68: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8b4c6c: stur            x6, [x0, #7]
    // 0x8b4c70: r1 = _Int32List
    //     0x8b4c70: add             x1, PP, #0xe, lsl #12  ; [pp+0xe188] _Int32List(26) [0xd0, 0xdc, 0xe8, 0xf4, 0x100, 0x10c, 0x118, 0x124, 0x130, 0x13c, 0x148, 0x154, 0x160, 0x16c, 0x178, 0x184, 0x190, 0x19c, 0x1a8, 0x1b4, 0x1c0, 0x1cc, 0x1d8, 0x1e4, 0x1f0, 0x1fc]
    //     0x8b4c74: ldr             x1, [x1, #0x188]
    // 0x8b4c78: ArrayLoad: r1 = r1[r0]  ; TypedSigned_4
    //     0x8b4c78: add             x16, x1, w0, sxtw #1
    //     0x8b4c7c: ldursw          x1, [x16, #0x17]
    // 0x8b4c80: adr             x6, #0x8b4bbc
    // 0x8b4c84: add             x6, x6, x1
    // 0x8b4c88: br              x6
    // 0x8b4c8c: r0 = Instance_AudioDeviceType
    //     0x8b4c8c: add             x0, PP, #0xe, lsl #12  ; [pp+0xe190] Obj!AudioDeviceType@e388a1
    //     0x8b4c90: ldr             x0, [x0, #0x190]
    // 0x8b4c94: b               #0x8b4dc0
    // 0x8b4c98: r0 = Instance_AudioDeviceType
    //     0x8b4c98: add             x0, PP, #0xe, lsl #12  ; [pp+0xe198] Obj!AudioDeviceType@e38881
    //     0x8b4c9c: ldr             x0, [x0, #0x198]
    // 0x8b4ca0: b               #0x8b4dc0
    // 0x8b4ca4: r0 = Instance_AudioDeviceType
    //     0x8b4ca4: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1a0] Obj!AudioDeviceType@e38861
    //     0x8b4ca8: ldr             x0, [x0, #0x1a0]
    // 0x8b4cac: b               #0x8b4dc0
    // 0x8b4cb0: r0 = Instance_AudioDeviceType
    //     0x8b4cb0: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1a8] Obj!AudioDeviceType@e38841
    //     0x8b4cb4: ldr             x0, [x0, #0x1a8]
    // 0x8b4cb8: b               #0x8b4dc0
    // 0x8b4cbc: r0 = Instance_AudioDeviceType
    //     0x8b4cbc: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1b0] Obj!AudioDeviceType@e38821
    //     0x8b4cc0: ldr             x0, [x0, #0x1b0]
    // 0x8b4cc4: b               #0x8b4dc0
    // 0x8b4cc8: r0 = Instance_AudioDeviceType
    //     0x8b4cc8: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1b8] Obj!AudioDeviceType@e38801
    //     0x8b4ccc: ldr             x0, [x0, #0x1b8]
    // 0x8b4cd0: b               #0x8b4dc0
    // 0x8b4cd4: r0 = Instance_AudioDeviceType
    //     0x8b4cd4: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1c0] Obj!AudioDeviceType@e387e1
    //     0x8b4cd8: ldr             x0, [x0, #0x1c0]
    // 0x8b4cdc: b               #0x8b4dc0
    // 0x8b4ce0: r0 = Instance_AudioDeviceType
    //     0x8b4ce0: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1c8] Obj!AudioDeviceType@e387c1
    //     0x8b4ce4: ldr             x0, [x0, #0x1c8]
    // 0x8b4ce8: b               #0x8b4dc0
    // 0x8b4cec: r0 = Instance_AudioDeviceType
    //     0x8b4cec: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1d0] Obj!AudioDeviceType@e387a1
    //     0x8b4cf0: ldr             x0, [x0, #0x1d0]
    // 0x8b4cf4: b               #0x8b4dc0
    // 0x8b4cf8: r0 = Instance_AudioDeviceType
    //     0x8b4cf8: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1d8] Obj!AudioDeviceType@e38781
    //     0x8b4cfc: ldr             x0, [x0, #0x1d8]
    // 0x8b4d00: b               #0x8b4dc0
    // 0x8b4d04: r0 = Instance_AudioDeviceType
    //     0x8b4d04: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1e0] Obj!AudioDeviceType@e38761
    //     0x8b4d08: ldr             x0, [x0, #0x1e0]
    // 0x8b4d0c: b               #0x8b4dc0
    // 0x8b4d10: r0 = Instance_AudioDeviceType
    //     0x8b4d10: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1e8] Obj!AudioDeviceType@e38741
    //     0x8b4d14: ldr             x0, [x0, #0x1e8]
    // 0x8b4d18: b               #0x8b4dc0
    // 0x8b4d1c: r0 = Instance_AudioDeviceType
    //     0x8b4d1c: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1e8] Obj!AudioDeviceType@e38741
    //     0x8b4d20: ldr             x0, [x0, #0x1e8]
    // 0x8b4d24: b               #0x8b4dc0
    // 0x8b4d28: r0 = Instance_AudioDeviceType
    //     0x8b4d28: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1f0] Obj!AudioDeviceType@e38721
    //     0x8b4d2c: ldr             x0, [x0, #0x1f0]
    // 0x8b4d30: b               #0x8b4dc0
    // 0x8b4d34: r0 = Instance_AudioDeviceType
    //     0x8b4d34: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1f8] Obj!AudioDeviceType@e38701
    //     0x8b4d38: ldr             x0, [x0, #0x1f8]
    // 0x8b4d3c: b               #0x8b4dc0
    // 0x8b4d40: r0 = Instance_AudioDeviceType
    //     0x8b4d40: add             x0, PP, #0xe, lsl #12  ; [pp+0xe200] Obj!AudioDeviceType@e386e1
    //     0x8b4d44: ldr             x0, [x0, #0x200]
    // 0x8b4d48: b               #0x8b4dc0
    // 0x8b4d4c: r0 = Instance_AudioDeviceType
    //     0x8b4d4c: add             x0, PP, #0xe, lsl #12  ; [pp+0xe208] Obj!AudioDeviceType@e386c1
    //     0x8b4d50: ldr             x0, [x0, #0x208]
    // 0x8b4d54: b               #0x8b4dc0
    // 0x8b4d58: r0 = Instance_AudioDeviceType
    //     0x8b4d58: add             x0, PP, #0xe, lsl #12  ; [pp+0xe210] Obj!AudioDeviceType@e386a1
    //     0x8b4d5c: ldr             x0, [x0, #0x210]
    // 0x8b4d60: b               #0x8b4dc0
    // 0x8b4d64: r0 = Instance_AudioDeviceType
    //     0x8b4d64: add             x0, PP, #0xe, lsl #12  ; [pp+0xe218] Obj!AudioDeviceType@e38681
    //     0x8b4d68: ldr             x0, [x0, #0x218]
    // 0x8b4d6c: b               #0x8b4dc0
    // 0x8b4d70: r0 = Instance_AudioDeviceType
    //     0x8b4d70: add             x0, PP, #0xe, lsl #12  ; [pp+0xe220] Obj!AudioDeviceType@e38661
    //     0x8b4d74: ldr             x0, [x0, #0x220]
    // 0x8b4d78: b               #0x8b4dc0
    // 0x8b4d7c: r0 = Instance_AudioDeviceType
    //     0x8b4d7c: add             x0, PP, #0xe, lsl #12  ; [pp+0xe228] Obj!AudioDeviceType@e38641
    //     0x8b4d80: ldr             x0, [x0, #0x228]
    // 0x8b4d84: b               #0x8b4dc0
    // 0x8b4d88: r0 = Instance_AudioDeviceType
    //     0x8b4d88: add             x0, PP, #0xe, lsl #12  ; [pp+0xe230] Obj!AudioDeviceType@e38621
    //     0x8b4d8c: ldr             x0, [x0, #0x230]
    // 0x8b4d90: b               #0x8b4dc0
    // 0x8b4d94: r0 = Instance_AudioDeviceType
    //     0x8b4d94: add             x0, PP, #0xe, lsl #12  ; [pp+0xe1e8] Obj!AudioDeviceType@e38741
    //     0x8b4d98: ldr             x0, [x0, #0x1e8]
    // 0x8b4d9c: b               #0x8b4dc0
    // 0x8b4da0: r0 = Instance_AudioDeviceType
    //     0x8b4da0: add             x0, PP, #0xe, lsl #12  ; [pp+0xe238] Obj!AudioDeviceType@e38601
    //     0x8b4da4: ldr             x0, [x0, #0x238]
    // 0x8b4da8: b               #0x8b4dc0
    // 0x8b4dac: r0 = Instance_AudioDeviceType
    //     0x8b4dac: add             x0, PP, #0xe, lsl #12  ; [pp+0xe240] Obj!AudioDeviceType@e385e1
    //     0x8b4db0: ldr             x0, [x0, #0x240]
    // 0x8b4db4: b               #0x8b4dc0
    // 0x8b4db8: r0 = Instance_AudioDeviceType
    //     0x8b4db8: add             x0, PP, #0xe, lsl #12  ; [pp+0xe248] Obj!AudioDeviceType@e385c1
    //     0x8b4dbc: ldr             x0, [x0, #0x248]
    // 0x8b4dc0: stur            x0, [fp, #-8]
    // 0x8b4dc4: r0 = AudioDevice()
    //     0x8b4dc4: bl              #0x8b4e04  ; AllocateAudioDeviceStub -> AudioDevice (size=0x1c)
    // 0x8b4dc8: ldur            x1, [fp, #-0x28]
    // 0x8b4dcc: StoreField: r0->field_7 = r1
    //     0x8b4dcc: stur            w1, [x0, #7]
    // 0x8b4dd0: ldur            x1, [fp, #-0x20]
    // 0x8b4dd4: StoreField: r0->field_b = r1
    //     0x8b4dd4: stur            w1, [x0, #0xb]
    // 0x8b4dd8: ldur            x1, [fp, #-0x18]
    // 0x8b4ddc: StoreField: r0->field_f = r1
    //     0x8b4ddc: stur            w1, [x0, #0xf]
    // 0x8b4de0: ldur            x1, [fp, #-0x10]
    // 0x8b4de4: StoreField: r0->field_13 = r1
    //     0x8b4de4: stur            w1, [x0, #0x13]
    // 0x8b4de8: ldur            x1, [fp, #-8]
    // 0x8b4dec: ArrayStore: r0[0] = r1  ; List_4
    //     0x8b4dec: stur            w1, [x0, #0x17]
    // 0x8b4df0: LeaveFrame
    //     0x8b4df0: mov             SP, fp
    //     0x8b4df4: ldp             fp, lr, [SP], #0x10
    // 0x8b4df8: ret
    //     0x8b4df8: ret             
    // 0x8b4dfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b4dfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b4e00: b               #0x8b4bdc
  }
  [closure] Future<void> <anonymous closure>(dynamic, List<AndroidAudioDeviceInfo>) async {
    // ** addr: 0x8b4e34, size: 0x17c
    // 0x8b4e34: EnterFrame
    //     0x8b4e34: stp             fp, lr, [SP, #-0x10]!
    //     0x8b4e38: mov             fp, SP
    // 0x8b4e3c: AllocStack(0x38)
    //     0x8b4e3c: sub             SP, SP, #0x38
    // 0x8b4e40: SetupParameters(AudioSession this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x8b4e40: stur            NULL, [fp, #-8]
    //     0x8b4e44: movz            x0, #0
    //     0x8b4e48: add             x1, fp, w0, sxtw #2
    //     0x8b4e4c: ldr             x1, [x1, #0x18]
    //     0x8b4e50: add             x2, fp, w0, sxtw #2
    //     0x8b4e54: ldr             x2, [x2, #0x10]
    //     0x8b4e58: stur            x2, [fp, #-0x18]
    //     0x8b4e5c: ldur            w3, [x1, #0x17]
    //     0x8b4e60: add             x3, x3, HEAP, lsl #32
    //     0x8b4e64: stur            x3, [fp, #-0x10]
    // 0x8b4e68: CheckStackOverflow
    //     0x8b4e68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b4e6c: cmp             SP, x16
    //     0x8b4e70: b.ls            #0x8b4f9c
    // 0x8b4e74: InitAsync() -> Future<void?>
    //     0x8b4e74: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8b4e78: bl              #0x661298  ; InitAsyncStub
    // 0x8b4e7c: ldur            x1, [fp, #-0x10]
    // 0x8b4e80: LoadField: r0 = r1->field_f
    //     0x8b4e80: ldur            w0, [x1, #0xf]
    // 0x8b4e84: DecompressPointer r0
    //     0x8b4e84: add             x0, x0, HEAP, lsl #32
    // 0x8b4e88: LoadField: r2 = r0->field_1f
    //     0x8b4e88: ldur            w2, [x0, #0x1f]
    // 0x8b4e8c: DecompressPointer r2
    //     0x8b4e8c: add             x2, x2, HEAP, lsl #32
    // 0x8b4e90: ldur            x0, [fp, #-0x18]
    // 0x8b4e94: stur            x2, [fp, #-0x20]
    // 0x8b4e98: r3 = LoadClassIdInstr(r0)
    //     0x8b4e98: ldur            x3, [x0, #-1]
    //     0x8b4e9c: ubfx            x3, x3, #0xc, #0x14
    // 0x8b4ea0: r16 = <AudioDevice>
    //     0x8b4ea0: add             x16, PP, #0xe, lsl #12  ; [pp+0xe170] TypeArguments: <AudioDevice>
    //     0x8b4ea4: ldr             x16, [x16, #0x170]
    // 0x8b4ea8: stp             x0, x16, [SP, #8]
    // 0x8b4eac: r16 = Closure: (AndroidAudioDeviceInfo) => AudioDevice from Function '_androidDevice2device@767445051': static.
    //     0x8b4eac: add             x16, PP, #0xe, lsl #12  ; [pp+0xe178] Closure: (AndroidAudioDeviceInfo) => AudioDevice from Function '_androidDevice2device@767445051': static. (0x7e54fb2b4b8c)
    //     0x8b4eb0: ldr             x16, [x16, #0x178]
    // 0x8b4eb4: str             x16, [SP]
    // 0x8b4eb8: mov             x0, x3
    // 0x8b4ebc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8b4ebc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8b4ec0: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8b4ec0: movz            x17, #0xf28c
    //     0x8b4ec4: add             lr, x0, x17
    //     0x8b4ec8: ldr             lr, [x21, lr, lsl #3]
    //     0x8b4ecc: blr             lr
    // 0x8b4ed0: r1 = LoadClassIdInstr(r0)
    //     0x8b4ed0: ldur            x1, [x0, #-1]
    //     0x8b4ed4: ubfx            x1, x1, #0xc, #0x14
    // 0x8b4ed8: mov             x16, x0
    // 0x8b4edc: mov             x0, x1
    // 0x8b4ee0: mov             x1, x16
    // 0x8b4ee4: r0 = GDT[cid_x0 + 0xf3fc]()
    //     0x8b4ee4: movz            x17, #0xf3fc
    //     0x8b4ee8: add             lr, x0, x17
    //     0x8b4eec: ldr             lr, [x21, lr, lsl #3]
    //     0x8b4ef0: blr             lr
    // 0x8b4ef4: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x8b4ef4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b4ef8: ldr             x0, [x0, #0x778]
    //     0x8b4efc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b4f00: cmp             w0, w16
    //     0x8b4f04: b.ne            #0x8b4f10
    //     0x8b4f08: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x8b4f0c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8b4f10: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x8b4f10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b4f14: ldr             x0, [x0, #0x780]
    //     0x8b4f18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b4f1c: cmp             w0, w16
    //     0x8b4f20: b.ne            #0x8b4f2c
    //     0x8b4f24: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x8b4f28: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8b4f2c: r0 = AudioDevicesChangedEvent()
    //     0x8b4f2c: bl              #0x8b4b5c  ; AllocateAudioDevicesChangedEventStub -> AudioDevicesChangedEvent (size=0x8)
    // 0x8b4f30: ldur            x1, [fp, #-0x20]
    // 0x8b4f34: mov             x2, x0
    // 0x8b4f38: r0 = add()
    //     0x8b4f38: bl              #0xc7588c  ; [package:rxdart/src/subjects/subject.dart] Subject::add
    // 0x8b4f3c: ldur            x0, [fp, #-0x10]
    // 0x8b4f40: LoadField: r1 = r0->field_f
    //     0x8b4f40: ldur            w1, [x0, #0xf]
    // 0x8b4f44: DecompressPointer r1
    //     0x8b4f44: add             x1, x1, HEAP, lsl #32
    // 0x8b4f48: LoadField: r2 = r1->field_23
    //     0x8b4f48: ldur            w2, [x1, #0x23]
    // 0x8b4f4c: DecompressPointer r2
    //     0x8b4f4c: add             x2, x2, HEAP, lsl #32
    // 0x8b4f50: r16 = Sentinel
    //     0x8b4f50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b4f54: cmp             w2, w16
    // 0x8b4f58: b.eq            #0x8b4fa4
    // 0x8b4f5c: stur            x2, [fp, #-0x18]
    // 0x8b4f60: LoadField: r3 = r2->field_f
    //     0x8b4f60: ldur            w3, [x2, #0xf]
    // 0x8b4f64: DecompressPointer r3
    //     0x8b4f64: add             x3, x3, HEAP, lsl #32
    // 0x8b4f68: LoadField: r4 = r3->field_1b
    //     0x8b4f68: ldur            w4, [x3, #0x1b]
    // 0x8b4f6c: DecompressPointer r4
    //     0x8b4f6c: add             x4, x4, HEAP, lsl #32
    // 0x8b4f70: cmp             w4, NULL
    // 0x8b4f74: b.eq            #0x8b4f94
    // 0x8b4f78: r0 = getDevices()
    //     0x8b4f78: bl              #0x8b48e0  ; [package:audio_session/src/core.dart] AudioSession::getDevices
    // 0x8b4f7c: mov             x1, x0
    // 0x8b4f80: stur            x1, [fp, #-0x20]
    // 0x8b4f84: r0 = Await()
    //     0x8b4f84: bl              #0x661044  ; AwaitStub
    // 0x8b4f88: ldur            x1, [fp, #-0x18]
    // 0x8b4f8c: mov             x2, x0
    // 0x8b4f90: r0 = add()
    //     0x8b4f90: bl              #0xc7588c  ; [package:rxdart/src/subjects/subject.dart] Subject::add
    // 0x8b4f94: r0 = Null
    //     0x8b4f94: mov             x0, NULL
    // 0x8b4f98: r0 = ReturnAsyncNotFuture()
    //     0x8b4f98: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8b4f9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b4f9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b4fa0: b               #0x8b4e74
    // 0x8b4fa4: r9 = _devicesSubject
    //     0x8b4fa4: add             x9, PP, #0xe, lsl #12  ; [pp+0xe180] Field <AudioSession._devicesSubject@767445051>: late final (offset: 0x24)
    //     0x8b4fa8: ldr             x9, [x9, #0x180]
    // 0x8b4fac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8b4fac: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, void) {
    // ** addr: 0x8b4fb0, size: 0x50
    // 0x8b4fb0: EnterFrame
    //     0x8b4fb0: stp             fp, lr, [SP, #-0x10]!
    //     0x8b4fb4: mov             fp, SP
    // 0x8b4fb8: ldr             x0, [fp, #0x18]
    // 0x8b4fbc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8b4fbc: ldur            w1, [x0, #0x17]
    // 0x8b4fc0: DecompressPointer r1
    //     0x8b4fc0: add             x1, x1, HEAP, lsl #32
    // 0x8b4fc4: CheckStackOverflow
    //     0x8b4fc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b4fc8: cmp             SP, x16
    //     0x8b4fcc: b.ls            #0x8b4ff8
    // 0x8b4fd0: LoadField: r0 = r1->field_f
    //     0x8b4fd0: ldur            w0, [x1, #0xf]
    // 0x8b4fd4: DecompressPointer r0
    //     0x8b4fd4: add             x0, x0, HEAP, lsl #32
    // 0x8b4fd8: LoadField: r1 = r0->field_1b
    //     0x8b4fd8: ldur            w1, [x0, #0x1b]
    // 0x8b4fdc: DecompressPointer r1
    //     0x8b4fdc: add             x1, x1, HEAP, lsl #32
    // 0x8b4fe0: r2 = Null
    //     0x8b4fe0: mov             x2, NULL
    // 0x8b4fe4: r0 = add()
    //     0x8b4fe4: bl              #0xc7588c  ; [package:rxdart/src/subjects/subject.dart] Subject::add
    // 0x8b4fe8: r0 = Null
    //     0x8b4fe8: mov             x0, NULL
    // 0x8b4fec: LeaveFrame
    //     0x8b4fec: mov             SP, fp
    //     0x8b4ff0: ldp             fp, lr, [SP], #0x10
    // 0x8b4ff4: ret
    //     0x8b4ff4: ret             
    // 0x8b4ff8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b4ff8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b4ffc: b               #0x8b4fd0
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x8b5088, size: 0x98
    // 0x8b5088: EnterFrame
    //     0x8b5088: stp             fp, lr, [SP, #-0x10]!
    //     0x8b508c: mov             fp, SP
    // 0x8b5090: AllocStack(0x20)
    //     0x8b5090: sub             SP, SP, #0x20
    // 0x8b5094: SetupParameters(AudioSession this /* r1 */)
    //     0x8b5094: stur            NULL, [fp, #-8]
    //     0x8b5098: movz            x0, #0
    //     0x8b509c: add             x1, fp, w0, sxtw #2
    //     0x8b50a0: ldr             x1, [x1, #0x10]
    //     0x8b50a4: ldur            w2, [x1, #0x17]
    //     0x8b50a8: add             x2, x2, HEAP, lsl #32
    //     0x8b50ac: stur            x2, [fp, #-0x10]
    // 0x8b50b0: CheckStackOverflow
    //     0x8b50b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b50b4: cmp             SP, x16
    //     0x8b50b8: b.ls            #0x8b510c
    // 0x8b50bc: InitAsync() -> Future<void?>
    //     0x8b50bc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8b50c0: bl              #0x661298  ; InitAsyncStub
    // 0x8b50c4: ldur            x0, [fp, #-0x10]
    // 0x8b50c8: LoadField: r1 = r0->field_f
    //     0x8b50c8: ldur            w1, [x0, #0xf]
    // 0x8b50cc: DecompressPointer r1
    //     0x8b50cc: add             x1, x1, HEAP, lsl #32
    // 0x8b50d0: LoadField: r2 = r1->field_23
    //     0x8b50d0: ldur            w2, [x1, #0x23]
    // 0x8b50d4: DecompressPointer r2
    //     0x8b50d4: add             x2, x2, HEAP, lsl #32
    // 0x8b50d8: r16 = Sentinel
    //     0x8b50d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b50dc: cmp             w2, w16
    // 0x8b50e0: b.eq            #0x8b5114
    // 0x8b50e4: stur            x2, [fp, #-0x18]
    // 0x8b50e8: r0 = getDevices()
    //     0x8b50e8: bl              #0x8b48e0  ; [package:audio_session/src/core.dart] AudioSession::getDevices
    // 0x8b50ec: mov             x1, x0
    // 0x8b50f0: stur            x1, [fp, #-0x20]
    // 0x8b50f4: r0 = Await()
    //     0x8b50f4: bl              #0x661044  ; AwaitStub
    // 0x8b50f8: ldur            x1, [fp, #-0x18]
    // 0x8b50fc: mov             x2, x0
    // 0x8b5100: r0 = add()
    //     0x8b5100: bl              #0xc7588c  ; [package:rxdart/src/subjects/subject.dart] Subject::add
    // 0x8b5104: r0 = Null
    //     0x8b5104: mov             x0, NULL
    // 0x8b5108: r0 = ReturnAsyncNotFuture()
    //     0x8b5108: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8b510c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b510c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b5110: b               #0x8b50bc
    // 0x8b5114: r9 = _devicesSubject
    //     0x8b5114: add             x9, PP, #0xe, lsl #12  ; [pp+0xe180] Field <AudioSession._devicesSubject@767445051>: late final (offset: 0x24)
    //     0x8b5118: ldr             x9, [x9, #0x180]
    // 0x8b511c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8b511c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ interruptionEventStream(/* No info */) {
    // ** addr: 0x8ba964, size: 0x38
    // 0x8ba964: EnterFrame
    //     0x8ba964: stp             fp, lr, [SP, #-0x10]!
    //     0x8ba968: mov             fp, SP
    // 0x8ba96c: CheckStackOverflow
    //     0x8ba96c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ba970: cmp             SP, x16
    //     0x8ba974: b.ls            #0x8ba994
    // 0x8ba978: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x8ba978: ldur            w0, [x1, #0x17]
    // 0x8ba97c: DecompressPointer r0
    //     0x8ba97c: add             x0, x0, HEAP, lsl #32
    // 0x8ba980: mov             x1, x0
    // 0x8ba984: r0 = stream()
    //     0x8ba984: bl              #0xd216bc  ; [package:rxdart/src/subjects/subject.dart] Subject::stream
    // 0x8ba988: LeaveFrame
    //     0x8ba988: mov             SP, fp
    //     0x8ba98c: ldp             fp, lr, [SP], #0x10
    // 0x8ba990: ret
    //     0x8ba990: ret             
    // 0x8ba994: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ba994: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ba998: b               #0x8ba978
  }
  get _ becomingNoisyEventStream(/* No info */) {
    // ** addr: 0x8ba99c, size: 0x38
    // 0x8ba99c: EnterFrame
    //     0x8ba99c: stp             fp, lr, [SP, #-0x10]!
    //     0x8ba9a0: mov             fp, SP
    // 0x8ba9a4: AllocStack(0x8)
    //     0x8ba9a4: sub             SP, SP, #8
    // 0x8ba9a8: LoadField: r0 = r1->field_1b
    //     0x8ba9a8: ldur            w0, [x1, #0x1b]
    // 0x8ba9ac: DecompressPointer r0
    //     0x8ba9ac: add             x0, x0, HEAP, lsl #32
    // 0x8ba9b0: stur            x0, [fp, #-8]
    // 0x8ba9b4: LoadField: r1 = r0->field_7
    //     0x8ba9b4: ldur            w1, [x0, #7]
    // 0x8ba9b8: DecompressPointer r1
    //     0x8ba9b8: add             x1, x1, HEAP, lsl #32
    // 0x8ba9bc: r0 = _SubjectStream()
    //     0x8ba9bc: bl              #0x8b33b0  ; Allocate_SubjectStreamStub -> _SubjectStream<X0> (size=0x10)
    // 0x8ba9c0: ldur            x1, [fp, #-8]
    // 0x8ba9c4: StoreField: r0->field_b = r1
    //     0x8ba9c4: stur            w1, [x0, #0xb]
    // 0x8ba9c8: LeaveFrame
    //     0x8ba9c8: mov             SP, fp
    //     0x8ba9cc: ldp             fp, lr, [SP], #0x10
    // 0x8ba9d0: ret
    //     0x8ba9d0: ret             
  }
  _ setActive(/* No info */) async {
    // ** addr: 0x8baf0c, size: 0x13c
    // 0x8baf0c: EnterFrame
    //     0x8baf0c: stp             fp, lr, [SP, #-0x10]!
    //     0x8baf10: mov             fp, SP
    // 0x8baf14: AllocStack(0x38)
    //     0x8baf14: sub             SP, SP, #0x38
    // 0x8baf18: SetupParameters(AudioSession this /* r1 => r1, fp-0x10 */)
    //     0x8baf18: stur            NULL, [fp, #-8]
    //     0x8baf1c: stur            x1, [fp, #-0x10]
    // 0x8baf20: CheckStackOverflow
    //     0x8baf20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8baf24: cmp             SP, x16
    //     0x8baf28: b.ls            #0x8bb040
    // 0x8baf2c: r1 = 3
    //     0x8baf2c: movz            x1, #0x3
    // 0x8baf30: r0 = AllocateContext()
    //     0x8baf30: bl              #0xec126c  ; AllocateContextStub
    // 0x8baf34: mov             x2, x0
    // 0x8baf38: ldur            x1, [fp, #-0x10]
    // 0x8baf3c: stur            x2, [fp, #-0x18]
    // 0x8baf40: StoreField: r2->field_f = r1
    //     0x8baf40: stur            w1, [x2, #0xf]
    // 0x8baf44: InitAsync() -> Future<bool>
    //     0x8baf44: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0x8baf48: bl              #0x661298  ; InitAsyncStub
    // 0x8baf4c: ldur            x0, [fp, #-0x10]
    // 0x8baf50: LoadField: r1 = r0->field_f
    //     0x8baf50: ldur            w1, [x0, #0xf]
    // 0x8baf54: DecompressPointer r1
    //     0x8baf54: add             x1, x1, HEAP, lsl #32
    // 0x8baf58: cmp             w1, NULL
    // 0x8baf5c: b.ne            #0x8baf6c
    // 0x8baf60: r2 = Instance_AudioSessionConfiguration
    //     0x8baf60: add             x2, PP, #0xd, lsl #12  ; [pp+0xde70] Obj!AudioSessionConfiguration@e260e1
    //     0x8baf64: ldr             x2, [x2, #0xe70]
    // 0x8baf68: b               #0x8baf70
    // 0x8baf6c: mov             x2, x1
    // 0x8baf70: stur            x2, [fp, #-0x20]
    // 0x8baf74: cmp             w1, NULL
    // 0x8baf78: b.ne            #0x8baf90
    // 0x8baf7c: mov             x1, x0
    // 0x8baf80: r0 = configure()
    //     0x8baf80: bl              #0x8bb280  ; [package:audio_session/src/core.dart] AudioSession::configure
    // 0x8baf84: mov             x1, x0
    // 0x8baf88: stur            x1, [fp, #-0x28]
    // 0x8baf8c: r0 = Await()
    //     0x8baf8c: bl              #0x661044  ; AwaitStub
    // 0x8baf90: ldur            x0, [fp, #-0x20]
    // 0x8baf94: LoadField: r1 = r0->field_23
    //     0x8baf94: ldur            w1, [x0, #0x23]
    // 0x8baf98: DecompressPointer r1
    //     0x8baf98: add             x1, x1, HEAP, lsl #32
    // 0x8baf9c: stur            x1, [fp, #-0x38]
    // 0x8bafa0: cmp             w1, NULL
    // 0x8bafa4: b.ne            #0x8bafb0
    // 0x8bafa8: r5 = false
    //     0x8bafa8: add             x5, NULL, #0x30  ; false
    // 0x8bafac: b               #0x8bafb4
    // 0x8bafb0: mov             x5, x1
    // 0x8bafb4: ldur            x2, [fp, #-0x10]
    // 0x8bafb8: ldur            x3, [fp, #-0x18]
    // 0x8bafbc: r4 = false
    //     0x8bafbc: add             x4, NULL, #0x30  ; false
    // 0x8bafc0: StoreField: r3->field_13 = r5
    //     0x8bafc0: stur            w5, [x3, #0x13]
    // 0x8bafc4: ArrayStore: r3[0] = r4  ; List_4
    //     0x8bafc4: stur            w4, [x3, #0x17]
    // 0x8bafc8: LoadField: r4 = r2->field_7
    //     0x8bafc8: ldur            w4, [x2, #7]
    // 0x8bafcc: DecompressPointer r4
    //     0x8bafcc: add             x4, x4, HEAP, lsl #32
    // 0x8bafd0: stur            x4, [fp, #-0x30]
    // 0x8bafd4: LoadField: r2 = r0->field_1f
    //     0x8bafd4: ldur            w2, [x0, #0x1f]
    // 0x8bafd8: DecompressPointer r2
    //     0x8bafd8: add             x2, x2, HEAP, lsl #32
    // 0x8bafdc: stur            x2, [fp, #-0x28]
    // 0x8bafe0: LoadField: r5 = r0->field_1b
    //     0x8bafe0: ldur            w5, [x0, #0x1b]
    // 0x8bafe4: DecompressPointer r5
    //     0x8bafe4: add             x5, x5, HEAP, lsl #32
    // 0x8bafe8: stur            x5, [fp, #-0x10]
    // 0x8bafec: r0 = AndroidAudioFocusRequest()
    //     0x8bafec: bl              #0x8bb274  ; AllocateAndroidAudioFocusRequestStub -> AndroidAudioFocusRequest (size=0x18)
    // 0x8baff0: mov             x3, x0
    // 0x8baff4: ldur            x0, [fp, #-0x28]
    // 0x8baff8: stur            x3, [fp, #-0x20]
    // 0x8baffc: StoreField: r3->field_7 = r0
    //     0x8baffc: stur            w0, [x3, #7]
    // 0x8bb000: ldur            x0, [fp, #-0x10]
    // 0x8bb004: StoreField: r3->field_b = r0
    //     0x8bb004: stur            w0, [x3, #0xb]
    // 0x8bb008: ldur            x0, [fp, #-0x38]
    // 0x8bb00c: StoreField: r3->field_f = r0
    //     0x8bb00c: stur            w0, [x3, #0xf]
    // 0x8bb010: ldur            x2, [fp, #-0x18]
    // 0x8bb014: r1 = Function '<anonymous closure>':.
    //     0x8bb014: add             x1, PP, #0xd, lsl #12  ; [pp+0xde78] AnonymousClosure: (0x8bb344), in [package:audio_session/src/core.dart] AudioSession::setActive (0x8baf0c)
    //     0x8bb018: ldr             x1, [x1, #0xe78]
    // 0x8bb01c: r0 = AllocateClosure()
    //     0x8bb01c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8bb020: ldur            x2, [fp, #-0x20]
    // 0x8bb024: StoreField: r2->field_13 = r0
    //     0x8bb024: stur            w0, [x2, #0x13]
    // 0x8bb028: ldur            x1, [fp, #-0x30]
    // 0x8bb02c: r0 = requestAudioFocus()
    //     0x8bb02c: bl              #0x8bb048  ; [package:audio_session/src/android.dart] AndroidAudioManager::requestAudioFocus
    // 0x8bb030: mov             x1, x0
    // 0x8bb034: stur            x1, [fp, #-0x10]
    // 0x8bb038: r0 = Await()
    //     0x8bb038: bl              #0x661044  ; AwaitStub
    // 0x8bb03c: r0 = ReturnAsync()
    //     0x8bb03c: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8bb040: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bb040: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bb044: b               #0x8baf2c
  }
  _ configure(/* No info */) async {
    // ** addr: 0x8bb280, size: 0xc4
    // 0x8bb280: EnterFrame
    //     0x8bb280: stp             fp, lr, [SP, #-0x10]!
    //     0x8bb284: mov             fp, SP
    // 0x8bb288: AllocStack(0x70)
    //     0x8bb288: sub             SP, SP, #0x70
    // 0x8bb28c: SetupParameters(AudioSession this /* r1 => r1, fp-0x48 */)
    //     0x8bb28c: stur            NULL, [fp, #-8]
    //     0x8bb290: stur            x1, [fp, #-0x48]
    // 0x8bb294: CheckStackOverflow
    //     0x8bb294: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bb298: cmp             SP, x16
    //     0x8bb29c: b.ls            #0x8bb33c
    // 0x8bb2a0: InitAsync() -> Future<void?>
    //     0x8bb2a0: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8bb2a4: bl              #0x661298  ; InitAsyncStub
    // 0x8bb2a8: r0 = Null
    //     0x8bb2a8: mov             x0, NULL
    // 0x8bb2ac: r0 = Await()
    //     0x8bb2ac: bl              #0x661044  ; AwaitStub
    // 0x8bb2b0: ldur            x0, [fp, #-0x48]
    // 0x8bb2b4: r1 = Instance_AudioSessionConfiguration
    //     0x8bb2b4: add             x1, PP, #0xd, lsl #12  ; [pp+0xde70] Obj!AudioSessionConfiguration@e260e1
    //     0x8bb2b8: ldr             x1, [x1, #0xe70]
    // 0x8bb2bc: StoreField: r0->field_f = r1
    //     0x8bb2bc: stur            w1, [x0, #0xf]
    // 0x8bb2c0: r0 = toJson()
    //     0x8bb2c0: bl              #0x8b29d0  ; [package:audio_session/src/core.dart] AudioSessionConfiguration::toJson
    // 0x8bb2c4: r1 = Null
    //     0x8bb2c4: mov             x1, NULL
    // 0x8bb2c8: r2 = 2
    //     0x8bb2c8: movz            x2, #0x2
    // 0x8bb2cc: stur            x0, [fp, #-0x48]
    // 0x8bb2d0: r0 = AllocateArray()
    //     0x8bb2d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8bb2d4: mov             x2, x0
    // 0x8bb2d8: ldur            x0, [fp, #-0x48]
    // 0x8bb2dc: stur            x2, [fp, #-0x50]
    // 0x8bb2e0: StoreField: r2->field_f = r0
    //     0x8bb2e0: stur            w0, [x2, #0xf]
    // 0x8bb2e4: r1 = <Map<String, dynamic>>
    //     0x8bb2e4: ldr             x1, [PP, #0x1d40]  ; [pp+0x1d40] TypeArguments: <Map<String, dynamic>>
    // 0x8bb2e8: r0 = AllocateGrowableArray()
    //     0x8bb2e8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8bb2ec: mov             x1, x0
    // 0x8bb2f0: ldur            x0, [fp, #-0x50]
    // 0x8bb2f4: StoreField: r1->field_f = r0
    //     0x8bb2f4: stur            w0, [x1, #0xf]
    // 0x8bb2f8: r0 = 2
    //     0x8bb2f8: movz            x0, #0x2
    // 0x8bb2fc: StoreField: r1->field_b = r0
    //     0x8bb2fc: stur            w0, [x1, #0xb]
    // 0x8bb300: r16 = Instance_MethodChannel
    //     0x8bb300: add             x16, PP, #0xd, lsl #12  ; [pp+0xdef0] Obj!MethodChannel@e11151
    //     0x8bb304: ldr             x16, [x16, #0xef0]
    // 0x8bb308: stp             x16, NULL, [SP, #0x10]
    // 0x8bb30c: r16 = "setConfiguration"
    //     0x8bb30c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdef8] "setConfiguration"
    //     0x8bb310: ldr             x16, [x16, #0xef8]
    // 0x8bb314: stp             x1, x16, [SP]
    // 0x8bb318: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8bb318: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8bb31c: r0 = invokeMethod()
    //     0x8bb31c: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x8bb320: mov             x1, x0
    // 0x8bb324: stur            x1, [fp, #-0x48]
    // 0x8bb328: r0 = Await()
    //     0x8bb328: bl              #0x661044  ; AwaitStub
    // 0x8bb32c: b               #0x8bb334
    // 0x8bb330: sub             SP, fp, #0x70
    // 0x8bb334: r0 = Null
    //     0x8bb334: mov             x0, NULL
    // 0x8bb338: r0 = ReturnAsyncNotFuture()
    //     0x8bb338: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8bb33c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bb33c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bb340: b               #0x8bb2a0
  }
  [closure] void <anonymous closure>(dynamic, AndroidAudioFocus) {
    // ** addr: 0x8bb344, size: 0x20c
    // 0x8bb344: EnterFrame
    //     0x8bb344: stp             fp, lr, [SP, #-0x10]!
    //     0x8bb348: mov             fp, SP
    // 0x8bb34c: AllocStack(0x20)
    //     0x8bb34c: sub             SP, SP, #0x20
    // 0x8bb350: SetupParameters()
    //     0x8bb350: ldr             x0, [fp, #0x18]
    //     0x8bb354: ldur            w1, [x0, #0x17]
    //     0x8bb358: add             x1, x1, HEAP, lsl #32
    //     0x8bb35c: stur            x1, [fp, #-0x18]
    // 0x8bb360: CheckStackOverflow
    //     0x8bb360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bb364: cmp             SP, x16
    //     0x8bb368: b.ls            #0x8bb548
    // 0x8bb36c: ldr             x0, [fp, #0x10]
    // 0x8bb370: r16 = Instance_AndroidAudioFocus
    //     0x8bb370: add             x16, PP, #0xd, lsl #12  ; [pp+0xde80] Obj!AndroidAudioFocus@e261b1
    //     0x8bb374: ldr             x16, [x16, #0xe80]
    // 0x8bb378: cmp             w0, w16
    // 0x8bb37c: b.ne            #0x8bb3ec
    // 0x8bb380: LoadField: r0 = r1->field_f
    //     0x8bb380: ldur            w0, [x1, #0xf]
    // 0x8bb384: DecompressPointer r0
    //     0x8bb384: add             x0, x0, HEAP, lsl #32
    // 0x8bb388: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8bb388: ldur            w2, [x0, #0x17]
    // 0x8bb38c: DecompressPointer r2
    //     0x8bb38c: add             x2, x2, HEAP, lsl #32
    // 0x8bb390: stur            x2, [fp, #-0x10]
    // 0x8bb394: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x8bb394: ldur            w0, [x1, #0x17]
    // 0x8bb398: DecompressPointer r0
    //     0x8bb398: add             x0, x0, HEAP, lsl #32
    // 0x8bb39c: tbnz            w0, #4, #0x8bb3ac
    // 0x8bb3a0: r0 = Instance_AudioInterruptionType
    //     0x8bb3a0: add             x0, PP, #0xd, lsl #12  ; [pp+0xde88] Obj!AudioInterruptionType@e38901
    //     0x8bb3a4: ldr             x0, [x0, #0xe88]
    // 0x8bb3a8: b               #0x8bb3b4
    // 0x8bb3ac: r0 = Instance_AudioInterruptionType
    //     0x8bb3ac: add             x0, PP, #0xd, lsl #12  ; [pp+0xde90] Obj!AudioInterruptionType@e388e1
    //     0x8bb3b0: ldr             x0, [x0, #0xe90]
    // 0x8bb3b4: stur            x0, [fp, #-8]
    // 0x8bb3b8: r0 = AudioInterruptionEvent()
    //     0x8bb3b8: bl              #0x8bb550  ; AllocateAudioInterruptionEventStub -> AudioInterruptionEvent (size=0x10)
    // 0x8bb3bc: mov             x1, x0
    // 0x8bb3c0: r0 = false
    //     0x8bb3c0: add             x0, NULL, #0x30  ; false
    // 0x8bb3c4: StoreField: r1->field_7 = r0
    //     0x8bb3c4: stur            w0, [x1, #7]
    // 0x8bb3c8: ldur            x2, [fp, #-8]
    // 0x8bb3cc: StoreField: r1->field_b = r2
    //     0x8bb3cc: stur            w2, [x1, #0xb]
    // 0x8bb3d0: mov             x2, x1
    // 0x8bb3d4: ldur            x1, [fp, #-0x10]
    // 0x8bb3d8: r0 = add()
    //     0x8bb3d8: bl              #0xc7588c  ; [package:rxdart/src/subjects/subject.dart] Subject::add
    // 0x8bb3dc: ldur            x2, [fp, #-0x18]
    // 0x8bb3e0: r1 = false
    //     0x8bb3e0: add             x1, NULL, #0x30  ; false
    // 0x8bb3e4: ArrayStore: r2[0] = r1  ; List_4
    //     0x8bb3e4: stur            w1, [x2, #0x17]
    // 0x8bb3e8: b               #0x8bb538
    // 0x8bb3ec: mov             x2, x1
    // 0x8bb3f0: r1 = false
    //     0x8bb3f0: add             x1, NULL, #0x30  ; false
    // 0x8bb3f4: r16 = Instance_AndroidAudioFocus
    //     0x8bb3f4: add             x16, PP, #0xd, lsl #12  ; [pp+0xde98] Obj!AndroidAudioFocus@e261a1
    //     0x8bb3f8: ldr             x16, [x16, #0xe98]
    // 0x8bb3fc: cmp             w0, w16
    // 0x8bb400: b.ne            #0x8bb44c
    // 0x8bb404: LoadField: r0 = r2->field_f
    //     0x8bb404: ldur            w0, [x2, #0xf]
    // 0x8bb408: DecompressPointer r0
    //     0x8bb408: add             x0, x0, HEAP, lsl #32
    // 0x8bb40c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x8bb40c: ldur            w3, [x0, #0x17]
    // 0x8bb410: DecompressPointer r3
    //     0x8bb410: add             x3, x3, HEAP, lsl #32
    // 0x8bb414: stur            x3, [fp, #-8]
    // 0x8bb418: r0 = AudioInterruptionEvent()
    //     0x8bb418: bl              #0x8bb550  ; AllocateAudioInterruptionEventStub -> AudioInterruptionEvent (size=0x10)
    // 0x8bb41c: r1 = true
    //     0x8bb41c: add             x1, NULL, #0x20  ; true
    // 0x8bb420: StoreField: r0->field_7 = r1
    //     0x8bb420: stur            w1, [x0, #7]
    // 0x8bb424: r1 = Instance_AudioInterruptionType
    //     0x8bb424: add             x1, PP, #0xd, lsl #12  ; [pp+0xdea0] Obj!AudioInterruptionType@e388c1
    //     0x8bb428: ldr             x1, [x1, #0xea0]
    // 0x8bb42c: StoreField: r0->field_b = r1
    //     0x8bb42c: stur            w1, [x0, #0xb]
    // 0x8bb430: ldur            x1, [fp, #-8]
    // 0x8bb434: mov             x2, x0
    // 0x8bb438: r0 = add()
    //     0x8bb438: bl              #0xc7588c  ; [package:rxdart/src/subjects/subject.dart] Subject::add
    // 0x8bb43c: ldur            x3, [fp, #-0x18]
    // 0x8bb440: r2 = false
    //     0x8bb440: add             x2, NULL, #0x30  ; false
    // 0x8bb444: ArrayStore: r3[0] = r2  ; List_4
    //     0x8bb444: stur            w2, [x3, #0x17]
    // 0x8bb448: b               #0x8bb538
    // 0x8bb44c: mov             x3, x2
    // 0x8bb450: mov             x2, x1
    // 0x8bb454: r1 = true
    //     0x8bb454: add             x1, NULL, #0x20  ; true
    // 0x8bb458: r16 = Instance_AndroidAudioFocus
    //     0x8bb458: add             x16, PP, #0xd, lsl #12  ; [pp+0xdea8] Obj!AndroidAudioFocus@e26191
    //     0x8bb45c: ldr             x16, [x16, #0xea8]
    // 0x8bb460: cmp             w0, w16
    // 0x8bb464: b.ne            #0x8bb4b0
    // 0x8bb468: LoadField: r0 = r3->field_f
    //     0x8bb468: ldur            w0, [x3, #0xf]
    // 0x8bb46c: DecompressPointer r0
    //     0x8bb46c: add             x0, x0, HEAP, lsl #32
    // 0x8bb470: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x8bb470: ldur            w4, [x0, #0x17]
    // 0x8bb474: DecompressPointer r4
    //     0x8bb474: add             x4, x4, HEAP, lsl #32
    // 0x8bb478: stur            x4, [fp, #-8]
    // 0x8bb47c: r0 = AudioInterruptionEvent()
    //     0x8bb47c: bl              #0x8bb550  ; AllocateAudioInterruptionEventStub -> AudioInterruptionEvent (size=0x10)
    // 0x8bb480: r1 = true
    //     0x8bb480: add             x1, NULL, #0x20  ; true
    // 0x8bb484: StoreField: r0->field_7 = r1
    //     0x8bb484: stur            w1, [x0, #7]
    // 0x8bb488: r1 = Instance_AudioInterruptionType
    //     0x8bb488: add             x1, PP, #0xd, lsl #12  ; [pp+0xde90] Obj!AudioInterruptionType@e388e1
    //     0x8bb48c: ldr             x1, [x1, #0xe90]
    // 0x8bb490: StoreField: r0->field_b = r1
    //     0x8bb490: stur            w1, [x0, #0xb]
    // 0x8bb494: ldur            x1, [fp, #-8]
    // 0x8bb498: mov             x2, x0
    // 0x8bb49c: r0 = add()
    //     0x8bb49c: bl              #0xc7588c  ; [package:rxdart/src/subjects/subject.dart] Subject::add
    // 0x8bb4a0: ldur            x2, [fp, #-0x18]
    // 0x8bb4a4: r0 = false
    //     0x8bb4a4: add             x0, NULL, #0x30  ; false
    // 0x8bb4a8: ArrayStore: r2[0] = r0  ; List_4
    //     0x8bb4a8: stur            w0, [x2, #0x17]
    // 0x8bb4ac: b               #0x8bb538
    // 0x8bb4b0: mov             x2, x3
    // 0x8bb4b4: r16 = Instance_AndroidAudioFocus
    //     0x8bb4b4: add             x16, PP, #0xd, lsl #12  ; [pp+0xdeb0] Obj!AndroidAudioFocus@e26181
    //     0x8bb4b8: ldr             x16, [x16, #0xeb0]
    // 0x8bb4bc: cmp             w0, w16
    // 0x8bb4c0: b.ne            #0x8bb538
    // 0x8bb4c4: LoadField: r0 = r2->field_f
    //     0x8bb4c4: ldur            w0, [x2, #0xf]
    // 0x8bb4c8: DecompressPointer r0
    //     0x8bb4c8: add             x0, x0, HEAP, lsl #32
    // 0x8bb4cc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x8bb4cc: ldur            w3, [x0, #0x17]
    // 0x8bb4d0: DecompressPointer r3
    //     0x8bb4d0: add             x3, x3, HEAP, lsl #32
    // 0x8bb4d4: stur            x3, [fp, #-0x20]
    // 0x8bb4d8: LoadField: r0 = r2->field_13
    //     0x8bb4d8: ldur            w0, [x2, #0x13]
    // 0x8bb4dc: DecompressPointer r0
    //     0x8bb4dc: add             x0, x0, HEAP, lsl #32
    // 0x8bb4e0: stur            x0, [fp, #-0x10]
    // 0x8bb4e4: tbnz            w0, #4, #0x8bb4f4
    // 0x8bb4e8: r4 = Instance_AudioInterruptionType
    //     0x8bb4e8: add             x4, PP, #0xd, lsl #12  ; [pp+0xde90] Obj!AudioInterruptionType@e388e1
    //     0x8bb4ec: ldr             x4, [x4, #0xe90]
    // 0x8bb4f0: b               #0x8bb4fc
    // 0x8bb4f4: r4 = Instance_AudioInterruptionType
    //     0x8bb4f4: add             x4, PP, #0xd, lsl #12  ; [pp+0xde88] Obj!AudioInterruptionType@e38901
    //     0x8bb4f8: ldr             x4, [x4, #0xe88]
    // 0x8bb4fc: stur            x4, [fp, #-8]
    // 0x8bb500: r0 = AudioInterruptionEvent()
    //     0x8bb500: bl              #0x8bb550  ; AllocateAudioInterruptionEventStub -> AudioInterruptionEvent (size=0x10)
    // 0x8bb504: mov             x1, x0
    // 0x8bb508: r0 = true
    //     0x8bb508: add             x0, NULL, #0x20  ; true
    // 0x8bb50c: StoreField: r1->field_7 = r0
    //     0x8bb50c: stur            w0, [x1, #7]
    // 0x8bb510: ldur            x2, [fp, #-8]
    // 0x8bb514: StoreField: r1->field_b = r2
    //     0x8bb514: stur            w2, [x1, #0xb]
    // 0x8bb518: mov             x2, x1
    // 0x8bb51c: ldur            x1, [fp, #-0x20]
    // 0x8bb520: r0 = add()
    //     0x8bb520: bl              #0xc7588c  ; [package:rxdart/src/subjects/subject.dart] Subject::add
    // 0x8bb524: ldur            x1, [fp, #-0x10]
    // 0x8bb528: tbz             w1, #4, #0x8bb538
    // 0x8bb52c: ldur            x2, [fp, #-0x18]
    // 0x8bb530: r1 = true
    //     0x8bb530: add             x1, NULL, #0x20  ; true
    // 0x8bb534: ArrayStore: r2[0] = r1  ; List_4
    //     0x8bb534: stur            w1, [x2, #0x17]
    // 0x8bb538: r0 = Null
    //     0x8bb538: mov             x0, NULL
    // 0x8bb53c: LeaveFrame
    //     0x8bb53c: mov             SP, fp
    //     0x8bb540: ldp             fp, lr, [SP], #0x10
    // 0x8bb544: ret
    //     0x8bb544: ret             
    // 0x8bb548: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bb548: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bb54c: b               #0x8bb36c
  }
  get _ configurationStream(/* No info */) {
    // ** addr: 0x8bb978, size: 0x38
    // 0x8bb978: EnterFrame
    //     0x8bb978: stp             fp, lr, [SP, #-0x10]!
    //     0x8bb97c: mov             fp, SP
    // 0x8bb980: AllocStack(0x8)
    //     0x8bb980: sub             SP, SP, #8
    // 0x8bb984: LoadField: r0 = r1->field_13
    //     0x8bb984: ldur            w0, [x1, #0x13]
    // 0x8bb988: DecompressPointer r0
    //     0x8bb988: add             x0, x0, HEAP, lsl #32
    // 0x8bb98c: stur            x0, [fp, #-8]
    // 0x8bb990: LoadField: r1 = r0->field_7
    //     0x8bb990: ldur            w1, [x0, #7]
    // 0x8bb994: DecompressPointer r1
    //     0x8bb994: add             x1, x1, HEAP, lsl #32
    // 0x8bb998: r0 = _BehaviorSubjectStream()
    //     0x8bb998: bl              #0x8b551c  ; Allocate_BehaviorSubjectStreamStub -> _BehaviorSubjectStream<X0> (size=0x10)
    // 0x8bb99c: ldur            x1, [fp, #-8]
    // 0x8bb9a0: StoreField: r0->field_b = r1
    //     0x8bb9a0: stur            w1, [x0, #0xb]
    // 0x8bb9a4: LeaveFrame
    //     0x8bb9a4: mov             SP, fp
    //     0x8bb9a8: ldp             fp, lr, [SP], #0x10
    // 0x8bb9ac: ret
    //     0x8bb9ac: ret             
  }
}

// class id: 7137, size: 0x14, field offset: 0x14
enum AudioDeviceType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc47674, size: 0x64
    // 0xc47674: EnterFrame
    //     0xc47674: stp             fp, lr, [SP, #-0x10]!
    //     0xc47678: mov             fp, SP
    // 0xc4767c: AllocStack(0x10)
    //     0xc4767c: sub             SP, SP, #0x10
    // 0xc47680: SetupParameters(AudioDeviceType this /* r1 => r0, fp-0x8 */)
    //     0xc47680: mov             x0, x1
    //     0xc47684: stur            x1, [fp, #-8]
    // 0xc47688: CheckStackOverflow
    //     0xc47688: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4768c: cmp             SP, x16
    //     0xc47690: b.ls            #0xc476d0
    // 0xc47694: r1 = Null
    //     0xc47694: mov             x1, NULL
    // 0xc47698: r2 = 4
    //     0xc47698: movz            x2, #0x4
    // 0xc4769c: r0 = AllocateArray()
    //     0xc4769c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc476a0: r16 = "AudioDeviceType."
    //     0xc476a0: add             x16, PP, #0x21, lsl #12  ; [pp+0x21f28] "AudioDeviceType."
    //     0xc476a4: ldr             x16, [x16, #0xf28]
    // 0xc476a8: StoreField: r0->field_f = r16
    //     0xc476a8: stur            w16, [x0, #0xf]
    // 0xc476ac: ldur            x1, [fp, #-8]
    // 0xc476b0: LoadField: r2 = r1->field_f
    //     0xc476b0: ldur            w2, [x1, #0xf]
    // 0xc476b4: DecompressPointer r2
    //     0xc476b4: add             x2, x2, HEAP, lsl #32
    // 0xc476b8: StoreField: r0->field_13 = r2
    //     0xc476b8: stur            w2, [x0, #0x13]
    // 0xc476bc: str             x0, [SP]
    // 0xc476c0: r0 = _interpolate()
    //     0xc476c0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc476c4: LeaveFrame
    //     0xc476c4: mov             SP, fp
    //     0xc476c8: ldp             fp, lr, [SP], #0x10
    // 0xc476cc: ret
    //     0xc476cc: ret             
    // 0xc476d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc476d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc476d4: b               #0xc47694
  }
}

// class id: 7138, size: 0x14, field offset: 0x14
enum AudioInterruptionType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc47610, size: 0x64
    // 0xc47610: EnterFrame
    //     0xc47610: stp             fp, lr, [SP, #-0x10]!
    //     0xc47614: mov             fp, SP
    // 0xc47618: AllocStack(0x10)
    //     0xc47618: sub             SP, SP, #0x10
    // 0xc4761c: SetupParameters(AudioInterruptionType this /* r1 => r0, fp-0x8 */)
    //     0xc4761c: mov             x0, x1
    //     0xc47620: stur            x1, [fp, #-8]
    // 0xc47624: CheckStackOverflow
    //     0xc47624: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc47628: cmp             SP, x16
    //     0xc4762c: b.ls            #0xc4766c
    // 0xc47630: r1 = Null
    //     0xc47630: mov             x1, NULL
    // 0xc47634: r2 = 4
    //     0xc47634: movz            x2, #0x4
    // 0xc47638: r0 = AllocateArray()
    //     0xc47638: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4763c: r16 = "AudioInterruptionType."
    //     0xc4763c: add             x16, PP, #0x21, lsl #12  ; [pp+0x21f30] "AudioInterruptionType."
    //     0xc47640: ldr             x16, [x16, #0xf30]
    // 0xc47644: StoreField: r0->field_f = r16
    //     0xc47644: stur            w16, [x0, #0xf]
    // 0xc47648: ldur            x1, [fp, #-8]
    // 0xc4764c: LoadField: r2 = r1->field_f
    //     0xc4764c: ldur            w2, [x1, #0xf]
    // 0xc47650: DecompressPointer r2
    //     0xc47650: add             x2, x2, HEAP, lsl #32
    // 0xc47654: StoreField: r0->field_13 = r2
    //     0xc47654: stur            w2, [x0, #0x13]
    // 0xc47658: str             x0, [SP]
    // 0xc4765c: r0 = _interpolate()
    //     0xc4765c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc47660: LeaveFrame
    //     0xc47660: mov             SP, fp
    //     0xc47664: ldp             fp, lr, [SP], #0x10
    // 0xc47668: ret
    //     0xc47668: ret             
    // 0xc4766c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4766c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc47670: b               #0xc47630
  }
}
