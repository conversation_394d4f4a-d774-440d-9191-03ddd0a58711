// lib: , url: package:collection/src/canonicalized_map.dart

// class id: 1048663, size: 0x8
class :: {
}

// class id: 5810, size: 0x18, field offset: 0x8
abstract class CanonicalizedMap<X0, X1, X2> extends Object
    implements Map<X0, X1> {

  Map<Y0, Y1> cast<Y0, Y1>(CanonicalizedMap<X0, X1, X2>) {
    // ** addr: 0xd25bd4, size: 0x60
    // 0xd25bd4: EnterFrame
    //     0xd25bd4: stp             fp, lr, [SP, #-0x10]!
    //     0xd25bd8: mov             fp, SP
    // 0xd25bdc: AllocStack(0x10)
    //     0xd25bdc: sub             SP, SP, #0x10
    // 0xd25be0: SetupParameters()
    //     0xd25be0: ldur            w0, [x4, #0xf]
    //     0xd25be4: cbnz            w0, #0xd25bf0
    //     0xd25be8: mov             x1, NULL
    //     0xd25bec: b               #0xd25bfc
    //     0xd25bf0: ldur            w0, [x4, #0x17]
    //     0xd25bf4: add             x1, fp, w0, sxtw #2
    //     0xd25bf8: ldr             x1, [x1, #0x10]
    //     0xd25bfc: ldr             x0, [fp, #0x10]
    // 0xd25c00: CheckStackOverflow
    //     0xd25c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd25c04: cmp             SP, x16
    //     0xd25c08: b.ls            #0xd25c2c
    // 0xd25c0c: LoadField: r2 = r0->field_13
    //     0xd25c0c: ldur            w2, [x0, #0x13]
    // 0xd25c10: DecompressPointer r2
    //     0xd25c10: add             x2, x2, HEAP, lsl #32
    // 0xd25c14: stp             x2, x1, [SP]
    // 0xd25c18: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0xd25c18: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0xd25c1c: r0 = cast()
    //     0xd25c1c: bl              #0xd777b8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::cast
    // 0xd25c20: LeaveFrame
    //     0xd25c20: mov             SP, fp
    //     0xd25c24: ldp             fp, lr, [SP], #0x10
    // 0xd25c28: ret
    //     0xd25c28: ret             
    // 0xd25c2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd25c2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd25c30: b               #0xd25c0c
  }
  _ addEntries(/* No info */) {
    // ** addr: 0x6dc52c, size: 0x108
    // 0x6dc52c: EnterFrame
    //     0x6dc52c: stp             fp, lr, [SP, #-0x10]!
    //     0x6dc530: mov             fp, SP
    // 0x6dc534: AllocStack(0x40)
    //     0x6dc534: sub             SP, SP, #0x40
    // 0x6dc538: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x6dc538: mov             x0, x2
    //     0x6dc53c: stur            x1, [fp, #-8]
    //     0x6dc540: stur            x2, [fp, #-0x10]
    // 0x6dc544: CheckStackOverflow
    //     0x6dc544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6dc548: cmp             SP, x16
    //     0x6dc54c: b.ls            #0x6dc62c
    // 0x6dc550: r1 = 1
    //     0x6dc550: movz            x1, #0x1
    // 0x6dc554: r0 = AllocateContext()
    //     0x6dc554: bl              #0xec126c  ; AllocateContextStub
    // 0x6dc558: mov             x4, x0
    // 0x6dc55c: ldur            x3, [fp, #-8]
    // 0x6dc560: stur            x4, [fp, #-0x20]
    // 0x6dc564: StoreField: r4->field_f = r3
    //     0x6dc564: stur            w3, [x4, #0xf]
    // 0x6dc568: LoadField: r5 = r3->field_7
    //     0x6dc568: ldur            w5, [x3, #7]
    // 0x6dc56c: DecompressPointer r5
    //     0x6dc56c: add             x5, x5, HEAP, lsl #32
    // 0x6dc570: ldur            x0, [fp, #-0x10]
    // 0x6dc574: mov             x2, x5
    // 0x6dc578: stur            x5, [fp, #-0x18]
    // 0x6dc57c: r1 = Null
    //     0x6dc57c: mov             x1, NULL
    // 0x6dc580: r8 = Iterable<MapEntry<X1, X2>>
    //     0x6dc580: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b320] Type: Iterable<MapEntry<X1, X2>>
    //     0x6dc584: ldr             x8, [x8, #0x320]
    // 0x6dc588: LoadField: r9 = r8->field_7
    //     0x6dc588: ldur            x9, [x8, #7]
    // 0x6dc58c: r3 = Null
    //     0x6dc58c: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b328] Null
    //     0x6dc590: ldr             x3, [x3, #0x328]
    // 0x6dc594: blr             x9
    // 0x6dc598: ldur            x0, [fp, #-8]
    // 0x6dc59c: LoadField: r4 = r0->field_13
    //     0x6dc59c: ldur            w4, [x0, #0x13]
    // 0x6dc5a0: DecompressPointer r4
    //     0x6dc5a0: add             x4, x4, HEAP, lsl #32
    // 0x6dc5a4: ldur            x2, [fp, #-0x18]
    // 0x6dc5a8: stur            x4, [fp, #-0x28]
    // 0x6dc5ac: r1 = Null
    //     0x6dc5ac: mov             x1, NULL
    // 0x6dc5b0: r3 = <MapEntry<X0, MapEntry<X1, X2>>>
    //     0x6dc5b0: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b338] TypeArguments: <MapEntry<X0, MapEntry<X1, X2>>>
    //     0x6dc5b4: ldr             x3, [x3, #0x338]
    // 0x6dc5b8: r30 = InstantiateTypeArgumentsStub
    //     0x6dc5b8: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x6dc5bc: LoadField: r30 = r30->field_7
    //     0x6dc5bc: ldur            lr, [lr, #7]
    // 0x6dc5c0: blr             lr
    // 0x6dc5c4: ldur            x2, [fp, #-0x20]
    // 0x6dc5c8: ldur            x3, [fp, #-0x18]
    // 0x6dc5cc: r1 = Function '<anonymous closure>':.
    //     0x6dc5cc: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5b340] AnonymousClosure: (0x6dc718), in [package:collection/src/canonicalized_map.dart] CanonicalizedMap::addEntries (0x6dc52c)
    //     0x6dc5d0: ldr             x1, [x1, #0x340]
    // 0x6dc5d4: stur            x0, [fp, #-8]
    // 0x6dc5d8: r0 = AllocateClosureTA()
    //     0x6dc5d8: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x6dc5dc: mov             x1, x0
    // 0x6dc5e0: ldur            x0, [fp, #-0x10]
    // 0x6dc5e4: r2 = LoadClassIdInstr(r0)
    //     0x6dc5e4: ldur            x2, [x0, #-1]
    //     0x6dc5e8: ubfx            x2, x2, #0xc, #0x14
    // 0x6dc5ec: ldur            x16, [fp, #-8]
    // 0x6dc5f0: stp             x0, x16, [SP, #8]
    // 0x6dc5f4: str             x1, [SP]
    // 0x6dc5f8: mov             x0, x2
    // 0x6dc5fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6dc5fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6dc600: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x6dc600: movz            x17, #0xf28c
    //     0x6dc604: add             lr, x0, x17
    //     0x6dc608: ldr             lr, [x21, lr, lsl #3]
    //     0x6dc60c: blr             lr
    // 0x6dc610: ldur            x1, [fp, #-0x28]
    // 0x6dc614: mov             x2, x0
    // 0x6dc618: r0 = addEntries()
    //     0x6dc618: bl              #0x765b84  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::addEntries
    // 0x6dc61c: r0 = Null
    //     0x6dc61c: mov             x0, NULL
    // 0x6dc620: LeaveFrame
    //     0x6dc620: mov             SP, fp
    //     0x6dc624: ldp             fp, lr, [SP], #0x10
    // 0x6dc628: ret
    //     0x6dc628: ret             
    // 0x6dc62c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6dc62c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6dc630: b               #0x6dc550
  }
  Map<Y0, Y1> map<Y0, Y1>(CanonicalizedMap<X0, X1, X2>, (dynamic, X1, X2) => MapEntry<Y0, Y1>) {
    // ** addr: 0x6dc634, size: 0x98
    // 0x6dc634: EnterFrame
    //     0x6dc634: stp             fp, lr, [SP, #-0x10]!
    //     0x6dc638: mov             fp, SP
    // 0x6dc63c: AllocStack(0x20)
    //     0x6dc63c: sub             SP, SP, #0x20
    // 0x6dc640: SetupParameters()
    //     0x6dc640: ldur            w0, [x4, #0xf]
    //     0x6dc644: cbnz            w0, #0x6dc650
    //     0x6dc648: mov             x4, NULL
    //     0x6dc64c: b               #0x6dc660
    //     0x6dc650: ldur            w0, [x4, #0x17]
    //     0x6dc654: add             x1, fp, w0, sxtw #2
    //     0x6dc658: ldr             x1, [x1, #0x10]
    //     0x6dc65c: mov             x4, x1
    //     0x6dc660: ldr             x3, [fp, #0x18]
    //     0x6dc664: stur            x4, [fp, #-8]
    // 0x6dc668: CheckStackOverflow
    //     0x6dc668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6dc66c: cmp             SP, x16
    //     0x6dc670: b.ls            #0x6dc6c4
    // 0x6dc674: LoadField: r2 = r3->field_7
    //     0x6dc674: ldur            w2, [x3, #7]
    // 0x6dc678: DecompressPointer r2
    //     0x6dc678: add             x2, x2, HEAP, lsl #32
    // 0x6dc67c: ldr             x0, [fp, #0x10]
    // 0x6dc680: mov             x1, x4
    // 0x6dc684: r8 = (dynamic this, X1, X2) => MapEntry<Y0, Y1>
    //     0x6dc684: add             x8, PP, #0x39, lsl #12  ; [pp+0x395d8] FunctionType: (dynamic this, X1, X2) => MapEntry<Y0, Y1>
    //     0x6dc688: ldr             x8, [x8, #0x5d8]
    // 0x6dc68c: LoadField: r9 = r8->field_7
    //     0x6dc68c: ldur            x9, [x8, #7]
    // 0x6dc690: r3 = Null
    //     0x6dc690: add             x3, PP, #0x39, lsl #12  ; [pp+0x395e0] Null
    //     0x6dc694: ldr             x3, [x3, #0x5e0]
    // 0x6dc698: blr             x9
    // 0x6dc69c: ldur            x16, [fp, #-8]
    // 0x6dc6a0: ldr             lr, [fp, #0x18]
    // 0x6dc6a4: stp             lr, x16, [SP, #8]
    // 0x6dc6a8: ldr             x16, [fp, #0x10]
    // 0x6dc6ac: str             x16, [SP]
    // 0x6dc6b0: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x6dc6b0: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x6dc6b4: r0 = map()
    //     0x6dc6b4: bl              #0x6dcb70  ; [package:collection/src/canonicalized_map.dart] CanonicalizedMap::map
    // 0x6dc6b8: LeaveFrame
    //     0x6dc6b8: mov             SP, fp
    //     0x6dc6bc: ldp             fp, lr, [SP], #0x10
    // 0x6dc6c0: ret
    //     0x6dc6c0: ret             
    // 0x6dc6c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6dc6c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6dc6c8: b               #0x6dc674
  }
  X2? [](CanonicalizedMap<X0, X1, X2>, Object?) {
    // ** addr: 0x6dc6e4, size: 0x4c
    // 0x6dc6e4: EnterFrame
    //     0x6dc6e4: stp             fp, lr, [SP, #-0x10]!
    //     0x6dc6e8: mov             fp, SP
    // 0x6dc6ec: CheckStackOverflow
    //     0x6dc6ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6dc6f0: cmp             SP, x16
    //     0x6dc6f4: b.ls            #0x6dc710
    // 0x6dc6f8: ldr             x1, [fp, #0x18]
    // 0x6dc6fc: ldr             x2, [fp, #0x10]
    // 0x6dc700: r0 = []()
    //     0x6dc700: bl              #0xd43718  ; [package:collection/src/canonicalized_map.dart] CanonicalizedMap::[]
    // 0x6dc704: LeaveFrame
    //     0x6dc704: mov             SP, fp
    //     0x6dc708: ldp             fp, lr, [SP], #0x10
    // 0x6dc70c: ret
    //     0x6dc70c: ret             
    // 0x6dc710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6dc710: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6dc714: b               #0x6dc6f8
  }
  [closure] MapEntry<X0, MapEntry<X1, X2>> <anonymous closure>(dynamic, MapEntry<X1, X2>) {
    // ** addr: 0x6dc718, size: 0x10c
    // 0x6dc718: EnterFrame
    //     0x6dc718: stp             fp, lr, [SP, #-0x10]!
    //     0x6dc71c: mov             fp, SP
    // 0x6dc720: AllocStack(0x28)
    //     0x6dc720: sub             SP, SP, #0x28
    // 0x6dc724: SetupParameters()
    //     0x6dc724: ldr             x0, [fp, #0x18]
    //     0x6dc728: ldur            w4, [x0, #0x17]
    //     0x6dc72c: add             x4, x4, HEAP, lsl #32
    //     0x6dc730: stur            x4, [fp, #-8]
    // 0x6dc734: CheckStackOverflow
    //     0x6dc734: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6dc738: cmp             SP, x16
    //     0x6dc73c: b.ls            #0x6dc81c
    // 0x6dc740: LoadField: r0 = r4->field_f
    //     0x6dc740: ldur            w0, [x4, #0xf]
    // 0x6dc744: DecompressPointer r0
    //     0x6dc744: add             x0, x0, HEAP, lsl #32
    // 0x6dc748: LoadField: r2 = r0->field_7
    //     0x6dc748: ldur            w2, [x0, #7]
    // 0x6dc74c: DecompressPointer r2
    //     0x6dc74c: add             x2, x2, HEAP, lsl #32
    // 0x6dc750: r1 = Null
    //     0x6dc750: mov             x1, NULL
    // 0x6dc754: r3 = <X0, MapEntry<X1, X2>>
    //     0x6dc754: add             x3, PP, #0x10, lsl #12  ; [pp+0x10818] TypeArguments: <X0, MapEntry<X1, X2>>
    //     0x6dc758: ldr             x3, [x3, #0x818]
    // 0x6dc75c: r30 = InstantiateTypeArgumentsStub
    //     0x6dc75c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x6dc760: LoadField: r30 = r30->field_7
    //     0x6dc760: ldur            lr, [lr, #7]
    // 0x6dc764: blr             lr
    // 0x6dc768: mov             x2, x0
    // 0x6dc76c: ldr             x0, [fp, #0x10]
    // 0x6dc770: stur            x2, [fp, #-0x18]
    // 0x6dc774: LoadField: r3 = r0->field_b
    //     0x6dc774: ldur            w3, [x0, #0xb]
    // 0x6dc778: DecompressPointer r3
    //     0x6dc778: add             x3, x3, HEAP, lsl #32
    // 0x6dc77c: mov             x1, x3
    // 0x6dc780: stur            x3, [fp, #-0x10]
    // 0x6dc784: r0 = _canonicalizer()
    //     0x6dc784: bl              #0x6dc854  ; [package:http_parser/src/case_insensitive_map.dart] CaseInsensitiveMap::_canonicalizer
    // 0x6dc788: mov             x4, x0
    // 0x6dc78c: ldur            x0, [fp, #-8]
    // 0x6dc790: stur            x4, [fp, #-0x20]
    // 0x6dc794: LoadField: r1 = r0->field_f
    //     0x6dc794: ldur            w1, [x0, #0xf]
    // 0x6dc798: DecompressPointer r1
    //     0x6dc798: add             x1, x1, HEAP, lsl #32
    // 0x6dc79c: LoadField: r2 = r1->field_7
    //     0x6dc79c: ldur            w2, [x1, #7]
    // 0x6dc7a0: DecompressPointer r2
    //     0x6dc7a0: add             x2, x2, HEAP, lsl #32
    // 0x6dc7a4: r1 = Null
    //     0x6dc7a4: mov             x1, NULL
    // 0x6dc7a8: r3 = <X1, X2>
    //     0x6dc7a8: add             x3, PP, #0x10, lsl #12  ; [pp+0x10870] TypeArguments: <X1, X2>
    //     0x6dc7ac: ldr             x3, [x3, #0x870]
    // 0x6dc7b0: r0 = Null
    //     0x6dc7b0: mov             x0, NULL
    // 0x6dc7b4: cmp             x2, x0
    // 0x6dc7b8: b.eq            #0x6dc7c8
    // 0x6dc7bc: r30 = InstantiateTypeArgumentsStub
    //     0x6dc7bc: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x6dc7c0: LoadField: r30 = r30->field_7
    //     0x6dc7c0: ldur            lr, [lr, #7]
    // 0x6dc7c4: blr             lr
    // 0x6dc7c8: mov             x1, x0
    // 0x6dc7cc: ldr             x0, [fp, #0x10]
    // 0x6dc7d0: LoadField: r2 = r0->field_f
    //     0x6dc7d0: ldur            w2, [x0, #0xf]
    // 0x6dc7d4: DecompressPointer r2
    //     0x6dc7d4: add             x2, x2, HEAP, lsl #32
    // 0x6dc7d8: stur            x2, [fp, #-8]
    // 0x6dc7dc: r0 = MapEntry()
    //     0x6dc7dc: bl              #0x65ce18  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x6dc7e0: mov             x2, x0
    // 0x6dc7e4: ldur            x0, [fp, #-0x10]
    // 0x6dc7e8: stur            x2, [fp, #-0x28]
    // 0x6dc7ec: StoreField: r2->field_b = r0
    //     0x6dc7ec: stur            w0, [x2, #0xb]
    // 0x6dc7f0: ldur            x0, [fp, #-8]
    // 0x6dc7f4: StoreField: r2->field_f = r0
    //     0x6dc7f4: stur            w0, [x2, #0xf]
    // 0x6dc7f8: ldur            x1, [fp, #-0x18]
    // 0x6dc7fc: r0 = MapEntry()
    //     0x6dc7fc: bl              #0x65ce18  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x6dc800: ldur            x1, [fp, #-0x20]
    // 0x6dc804: StoreField: r0->field_b = r1
    //     0x6dc804: stur            w1, [x0, #0xb]
    // 0x6dc808: ldur            x1, [fp, #-0x28]
    // 0x6dc80c: StoreField: r0->field_f = r1
    //     0x6dc80c: stur            w1, [x0, #0xf]
    // 0x6dc810: LeaveFrame
    //     0x6dc810: mov             SP, fp
    //     0x6dc814: ldp             fp, lr, [SP], #0x10
    // 0x6dc818: ret
    //     0x6dc818: ret             
    // 0x6dc81c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6dc81c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6dc820: b               #0x6dc740
  }
  _ putIfAbsent(/* No info */) {
    // ** addr: 0x6dc920, size: 0x120
    // 0x6dc920: EnterFrame
    //     0x6dc920: stp             fp, lr, [SP, #-0x10]!
    //     0x6dc924: mov             fp, SP
    // 0x6dc928: AllocStack(0x30)
    //     0x6dc928: sub             SP, SP, #0x30
    // 0x6dc92c: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x6dc92c: stur            x1, [fp, #-8]
    //     0x6dc930: mov             x16, x2
    //     0x6dc934: mov             x2, x1
    //     0x6dc938: mov             x1, x16
    //     0x6dc93c: mov             x0, x3
    //     0x6dc940: stur            x1, [fp, #-0x10]
    //     0x6dc944: stur            x3, [fp, #-0x18]
    // 0x6dc948: CheckStackOverflow
    //     0x6dc948: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6dc94c: cmp             SP, x16
    //     0x6dc950: b.ls            #0x6dca38
    // 0x6dc954: r1 = 3
    //     0x6dc954: movz            x1, #0x3
    // 0x6dc958: r0 = AllocateContext()
    //     0x6dc958: bl              #0xec126c  ; AllocateContextStub
    // 0x6dc95c: mov             x4, x0
    // 0x6dc960: ldur            x3, [fp, #-8]
    // 0x6dc964: stur            x4, [fp, #-0x28]
    // 0x6dc968: StoreField: r4->field_f = r3
    //     0x6dc968: stur            w3, [x4, #0xf]
    // 0x6dc96c: ldur            x5, [fp, #-0x10]
    // 0x6dc970: StoreField: r4->field_13 = r5
    //     0x6dc970: stur            w5, [x4, #0x13]
    // 0x6dc974: ldur            x6, [fp, #-0x18]
    // 0x6dc978: ArrayStore: r4[0] = r6  ; List_4
    //     0x6dc978: stur            w6, [x4, #0x17]
    // 0x6dc97c: LoadField: r7 = r3->field_7
    //     0x6dc97c: ldur            w7, [x3, #7]
    // 0x6dc980: DecompressPointer r7
    //     0x6dc980: add             x7, x7, HEAP, lsl #32
    // 0x6dc984: mov             x0, x5
    // 0x6dc988: mov             x2, x7
    // 0x6dc98c: stur            x7, [fp, #-0x20]
    // 0x6dc990: r1 = Null
    //     0x6dc990: mov             x1, NULL
    // 0x6dc994: cmp             w2, NULL
    // 0x6dc998: b.eq            #0x6dc9b8
    // 0x6dc99c: LoadField: r4 = r2->field_1b
    //     0x6dc99c: ldur            w4, [x2, #0x1b]
    // 0x6dc9a0: DecompressPointer r4
    //     0x6dc9a0: add             x4, x4, HEAP, lsl #32
    // 0x6dc9a4: r8 = X1
    //     0x6dc9a4: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x6dc9a8: LoadField: r9 = r4->field_7
    //     0x6dc9a8: ldur            x9, [x4, #7]
    // 0x6dc9ac: r3 = Null
    //     0x6dc9ac: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1ca40] Null
    //     0x6dc9b0: ldr             x3, [x3, #0xa40]
    // 0x6dc9b4: blr             x9
    // 0x6dc9b8: ldur            x0, [fp, #-0x18]
    // 0x6dc9bc: ldur            x2, [fp, #-0x20]
    // 0x6dc9c0: r1 = Null
    //     0x6dc9c0: mov             x1, NULL
    // 0x6dc9c4: r8 = (dynamic this) => X2
    //     0x6dc9c4: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1ca50] FunctionType: (dynamic this) => X2
    //     0x6dc9c8: ldr             x8, [x8, #0xa50]
    // 0x6dc9cc: LoadField: r9 = r8->field_7
    //     0x6dc9cc: ldur            x9, [x8, #7]
    // 0x6dc9d0: r3 = Null
    //     0x6dc9d0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1ca58] Null
    //     0x6dc9d4: ldr             x3, [x3, #0xa58]
    // 0x6dc9d8: blr             x9
    // 0x6dc9dc: ldur            x0, [fp, #-8]
    // 0x6dc9e0: LoadField: r1 = r0->field_13
    //     0x6dc9e0: ldur            w1, [x0, #0x13]
    // 0x6dc9e4: DecompressPointer r1
    //     0x6dc9e4: add             x1, x1, HEAP, lsl #32
    // 0x6dc9e8: stur            x1, [fp, #-0x18]
    // 0x6dc9ec: ldur            x16, [fp, #-0x10]
    // 0x6dc9f0: str             x16, [SP]
    // 0x6dc9f4: r0 = toLowerCase()
    //     0x6dc9f4: bl              #0xebeae4  ; [dart:core] _OneByteString::toLowerCase
    // 0x6dc9f8: ldur            x2, [fp, #-0x28]
    // 0x6dc9fc: ldur            x3, [fp, #-0x20]
    // 0x6dca00: r1 = Function '<anonymous closure>':.
    //     0x6dca00: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1ca68] AnonymousClosure: (0x6dca40), in [package:collection/src/canonicalized_map.dart] CanonicalizedMap::putIfAbsent (0x6dc920)
    //     0x6dca04: ldr             x1, [x1, #0xa68]
    // 0x6dca08: stur            x0, [fp, #-8]
    // 0x6dca0c: r0 = AllocateClosureTA()
    //     0x6dca0c: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x6dca10: ldur            x1, [fp, #-0x18]
    // 0x6dca14: ldur            x2, [fp, #-8]
    // 0x6dca18: mov             x3, x0
    // 0x6dca1c: r0 = putIfAbsent()
    //     0x6dca1c: bl              #0x7661b4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::putIfAbsent
    // 0x6dca20: LoadField: r1 = r0->field_f
    //     0x6dca20: ldur            w1, [x0, #0xf]
    // 0x6dca24: DecompressPointer r1
    //     0x6dca24: add             x1, x1, HEAP, lsl #32
    // 0x6dca28: mov             x0, x1
    // 0x6dca2c: LeaveFrame
    //     0x6dca2c: mov             SP, fp
    //     0x6dca30: ldp             fp, lr, [SP], #0x10
    // 0x6dca34: ret
    //     0x6dca34: ret             
    // 0x6dca38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6dca38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6dca3c: b               #0x6dc954
  }
  [closure] MapEntry<X1, X2> <anonymous closure>(dynamic) {
    // ** addr: 0x6dca40, size: 0xc0
    // 0x6dca40: EnterFrame
    //     0x6dca40: stp             fp, lr, [SP, #-0x10]!
    //     0x6dca44: mov             fp, SP
    // 0x6dca48: AllocStack(0x20)
    //     0x6dca48: sub             SP, SP, #0x20
    // 0x6dca4c: SetupParameters()
    //     0x6dca4c: ldr             x0, [fp, #0x10]
    //     0x6dca50: ldur            w4, [x0, #0x17]
    //     0x6dca54: add             x4, x4, HEAP, lsl #32
    //     0x6dca58: stur            x4, [fp, #-8]
    // 0x6dca5c: CheckStackOverflow
    //     0x6dca5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6dca60: cmp             SP, x16
    //     0x6dca64: b.ls            #0x6dcaf8
    // 0x6dca68: LoadField: r0 = r4->field_f
    //     0x6dca68: ldur            w0, [x4, #0xf]
    // 0x6dca6c: DecompressPointer r0
    //     0x6dca6c: add             x0, x0, HEAP, lsl #32
    // 0x6dca70: LoadField: r2 = r0->field_7
    //     0x6dca70: ldur            w2, [x0, #7]
    // 0x6dca74: DecompressPointer r2
    //     0x6dca74: add             x2, x2, HEAP, lsl #32
    // 0x6dca78: r1 = Null
    //     0x6dca78: mov             x1, NULL
    // 0x6dca7c: r3 = <X1, X2>
    //     0x6dca7c: add             x3, PP, #0x10, lsl #12  ; [pp+0x10870] TypeArguments: <X1, X2>
    //     0x6dca80: ldr             x3, [x3, #0x870]
    // 0x6dca84: r0 = Null
    //     0x6dca84: mov             x0, NULL
    // 0x6dca88: cmp             x2, x0
    // 0x6dca8c: b.eq            #0x6dca9c
    // 0x6dca90: r30 = InstantiateTypeArgumentsStub
    //     0x6dca90: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x6dca94: LoadField: r30 = r30->field_7
    //     0x6dca94: ldur            lr, [lr, #7]
    // 0x6dca98: blr             lr
    // 0x6dca9c: mov             x1, x0
    // 0x6dcaa0: ldur            x0, [fp, #-8]
    // 0x6dcaa4: stur            x1, [fp, #-0x18]
    // 0x6dcaa8: LoadField: r2 = r0->field_13
    //     0x6dcaa8: ldur            w2, [x0, #0x13]
    // 0x6dcaac: DecompressPointer r2
    //     0x6dcaac: add             x2, x2, HEAP, lsl #32
    // 0x6dcab0: stur            x2, [fp, #-0x10]
    // 0x6dcab4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x6dcab4: ldur            w3, [x0, #0x17]
    // 0x6dcab8: DecompressPointer r3
    //     0x6dcab8: add             x3, x3, HEAP, lsl #32
    // 0x6dcabc: str             x3, [SP]
    // 0x6dcac0: mov             x0, x3
    // 0x6dcac4: ClosureCall
    //     0x6dcac4: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x6dcac8: ldur            x2, [x0, #0x1f]
    //     0x6dcacc: blr             x2
    // 0x6dcad0: ldur            x1, [fp, #-0x18]
    // 0x6dcad4: stur            x0, [fp, #-8]
    // 0x6dcad8: r0 = MapEntry()
    //     0x6dcad8: bl              #0x65ce18  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x6dcadc: ldur            x1, [fp, #-0x10]
    // 0x6dcae0: StoreField: r0->field_b = r1
    //     0x6dcae0: stur            w1, [x0, #0xb]
    // 0x6dcae4: ldur            x1, [fp, #-8]
    // 0x6dcae8: StoreField: r0->field_f = r1
    //     0x6dcae8: stur            w1, [x0, #0xf]
    // 0x6dcaec: LeaveFrame
    //     0x6dcaec: mov             SP, fp
    //     0x6dcaf0: ldp             fp, lr, [SP], #0x10
    // 0x6dcaf4: ret
    //     0x6dcaf4: ret             
    // 0x6dcaf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6dcaf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6dcafc: b               #0x6dca68
  }
  Map<Y0, Y1> map<Y0, Y1>(CanonicalizedMap<X0, X1, X2>, (dynamic, X1, X2) => MapEntry<Y0, Y1>) {
    // ** addr: 0x6dcb70, size: 0xb8
    // 0x6dcb70: EnterFrame
    //     0x6dcb70: stp             fp, lr, [SP, #-0x10]!
    //     0x6dcb74: mov             fp, SP
    // 0x6dcb78: AllocStack(0x28)
    //     0x6dcb78: sub             SP, SP, #0x28
    // 0x6dcb7c: SetupParameters()
    //     0x6dcb7c: ldur            w0, [x4, #0xf]
    //     0x6dcb80: cbnz            w0, #0x6dcb8c
    //     0x6dcb84: mov             x2, NULL
    //     0x6dcb88: b               #0x6dcb9c
    //     0x6dcb8c: ldur            w0, [x4, #0x17]
    //     0x6dcb90: add             x1, fp, w0, sxtw #2
    //     0x6dcb94: ldr             x1, [x1, #0x10]
    //     0x6dcb98: mov             x2, x1
    //     0x6dcb9c: ldr             x1, [fp, #0x18]
    //     0x6dcba0: ldr             x0, [fp, #0x10]
    //     0x6dcba4: stur            x2, [fp, #-8]
    // 0x6dcba8: CheckStackOverflow
    //     0x6dcba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6dcbac: cmp             SP, x16
    //     0x6dcbb0: b.ls            #0x6dcc20
    // 0x6dcbb4: r1 = 2
    //     0x6dcbb4: movz            x1, #0x2
    // 0x6dcbb8: r0 = AllocateContext()
    //     0x6dcbb8: bl              #0xec126c  ; AllocateContextStub
    // 0x6dcbbc: mov             x1, x0
    // 0x6dcbc0: ldr             x0, [fp, #0x18]
    // 0x6dcbc4: StoreField: r1->field_f = r0
    //     0x6dcbc4: stur            w0, [x1, #0xf]
    // 0x6dcbc8: ldr             x2, [fp, #0x10]
    // 0x6dcbcc: StoreField: r1->field_13 = r2
    //     0x6dcbcc: stur            w2, [x1, #0x13]
    // 0x6dcbd0: LoadField: r4 = r0->field_13
    //     0x6dcbd0: ldur            w4, [x0, #0x13]
    // 0x6dcbd4: DecompressPointer r4
    //     0x6dcbd4: add             x4, x4, HEAP, lsl #32
    // 0x6dcbd8: stur            x4, [fp, #-0x10]
    // 0x6dcbdc: LoadField: r3 = r0->field_7
    //     0x6dcbdc: ldur            w3, [x0, #7]
    // 0x6dcbe0: DecompressPointer r3
    //     0x6dcbe0: add             x3, x3, HEAP, lsl #32
    // 0x6dcbe4: mov             x2, x1
    // 0x6dcbe8: r1 = Function '<anonymous closure>':.
    //     0x6dcbe8: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1ca70] AnonymousClosure: (0x6dcc28), in [package:collection/src/canonicalized_map.dart] CanonicalizedMap::map (0x6dcb70)
    //     0x6dcbec: ldr             x1, [x1, #0xa70]
    // 0x6dcbf0: r0 = AllocateClosureTA()
    //     0x6dcbf0: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x6dcbf4: mov             x1, x0
    // 0x6dcbf8: ldur            x0, [fp, #-8]
    // 0x6dcbfc: StoreField: r1->field_b = r0
    //     0x6dcbfc: stur            w0, [x1, #0xb]
    // 0x6dcc00: ldur            x16, [fp, #-0x10]
    // 0x6dcc04: stp             x16, x0, [SP, #8]
    // 0x6dcc08: str             x1, [SP]
    // 0x6dcc0c: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x6dcc0c: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x6dcc10: r0 = map()
    //     0x6dcc10: bl              #0x766c60  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::map
    // 0x6dcc14: LeaveFrame
    //     0x6dcc14: mov             SP, fp
    //     0x6dcc18: ldp             fp, lr, [SP], #0x10
    // 0x6dcc1c: ret
    //     0x6dcc1c: ret             
    // 0x6dcc20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6dcc20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6dcc24: b               #0x6dcbb4
  }
  [closure] MapEntry<Y0, Y1> <anonymous closure>(dynamic, X0, MapEntry<X1, X2>) {
    // ** addr: 0x6dcc28, size: 0x68
    // 0x6dcc28: EnterFrame
    //     0x6dcc28: stp             fp, lr, [SP, #-0x10]!
    //     0x6dcc2c: mov             fp, SP
    // 0x6dcc30: AllocStack(0x18)
    //     0x6dcc30: sub             SP, SP, #0x18
    // 0x6dcc34: SetupParameters()
    //     0x6dcc34: ldr             x0, [fp, #0x20]
    //     0x6dcc38: ldur            w1, [x0, #0x17]
    //     0x6dcc3c: add             x1, x1, HEAP, lsl #32
    // 0x6dcc40: CheckStackOverflow
    //     0x6dcc40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6dcc44: cmp             SP, x16
    //     0x6dcc48: b.ls            #0x6dcc88
    // 0x6dcc4c: LoadField: r0 = r1->field_13
    //     0x6dcc4c: ldur            w0, [x1, #0x13]
    // 0x6dcc50: DecompressPointer r0
    //     0x6dcc50: add             x0, x0, HEAP, lsl #32
    // 0x6dcc54: ldr             x1, [fp, #0x10]
    // 0x6dcc58: LoadField: r2 = r1->field_b
    //     0x6dcc58: ldur            w2, [x1, #0xb]
    // 0x6dcc5c: DecompressPointer r2
    //     0x6dcc5c: add             x2, x2, HEAP, lsl #32
    // 0x6dcc60: LoadField: r3 = r1->field_f
    //     0x6dcc60: ldur            w3, [x1, #0xf]
    // 0x6dcc64: DecompressPointer r3
    //     0x6dcc64: add             x3, x3, HEAP, lsl #32
    // 0x6dcc68: stp             x2, x0, [SP, #8]
    // 0x6dcc6c: str             x3, [SP]
    // 0x6dcc70: ClosureCall
    //     0x6dcc70: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x6dcc74: ldur            x2, [x0, #0x1f]
    //     0x6dcc78: blr             x2
    // 0x6dcc7c: LeaveFrame
    //     0x6dcc7c: mov             SP, fp
    //     0x6dcc80: ldp             fp, lr, [SP], #0x10
    // 0x6dcc84: ret
    //     0x6dcc84: ret             
    // 0x6dcc88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6dcc88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6dcc8c: b               #0x6dcc4c
  }
  get _ isNotEmpty(/* No info */) {
    // ** addr: 0x6de7a4, size: 0x44
    // 0x6de7a4: EnterFrame
    //     0x6de7a4: stp             fp, lr, [SP, #-0x10]!
    //     0x6de7a8: mov             fp, SP
    // 0x6de7ac: LoadField: r2 = r1->field_13
    //     0x6de7ac: ldur            w2, [x1, #0x13]
    // 0x6de7b0: DecompressPointer r2
    //     0x6de7b0: add             x2, x2, HEAP, lsl #32
    // 0x6de7b4: LoadField: r1 = r2->field_13
    //     0x6de7b4: ldur            w1, [x2, #0x13]
    // 0x6de7b8: r3 = LoadInt32Instr(r1)
    //     0x6de7b8: sbfx            x3, x1, #1, #0x1f
    // 0x6de7bc: asr             x1, x3, #1
    // 0x6de7c0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x6de7c0: ldur            w3, [x2, #0x17]
    // 0x6de7c4: r2 = LoadInt32Instr(r3)
    //     0x6de7c4: sbfx            x2, x3, #1, #0x1f
    // 0x6de7c8: sub             x3, x1, x2
    // 0x6de7cc: cbnz            x3, #0x6de7d8
    // 0x6de7d0: r0 = false
    //     0x6de7d0: add             x0, NULL, #0x30  ; false
    // 0x6de7d4: b               #0x6de7dc
    // 0x6de7d8: r0 = true
    //     0x6de7d8: add             x0, NULL, #0x20  ; true
    // 0x6de7dc: LeaveFrame
    //     0x6de7dc: mov             SP, fp
    //     0x6de7e0: ldp             fp, lr, [SP], #0x10
    // 0x6de7e4: ret
    //     0x6de7e4: ret             
  }
  _ CanonicalizedMap.from(/* No info */) {
    // ** addr: 0x705600, size: 0xa0
    // 0x705600: EnterFrame
    //     0x705600: stp             fp, lr, [SP, #-0x10]!
    //     0x705604: mov             fp, SP
    // 0x705608: AllocStack(0x20)
    //     0x705608: sub             SP, SP, #0x20
    // 0x70560c: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x70560c: mov             x4, x1
    //     0x705610: mov             x0, x2
    //     0x705614: stur            x1, [fp, #-8]
    //     0x705618: stur            x2, [fp, #-0x10]
    // 0x70561c: CheckStackOverflow
    //     0x70561c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x705620: cmp             SP, x16
    //     0x705624: b.ls            #0x705698
    // 0x705628: LoadField: r2 = r4->field_7
    //     0x705628: ldur            w2, [x4, #7]
    // 0x70562c: DecompressPointer r2
    //     0x70562c: add             x2, x2, HEAP, lsl #32
    // 0x705630: r1 = Null
    //     0x705630: mov             x1, NULL
    // 0x705634: r3 = <X0, MapEntry<X1, X2>>
    //     0x705634: add             x3, PP, #0x10, lsl #12  ; [pp+0x10818] TypeArguments: <X0, MapEntry<X1, X2>>
    //     0x705638: ldr             x3, [x3, #0x818]
    // 0x70563c: r30 = InstantiateTypeArgumentsStub
    //     0x70563c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x705640: LoadField: r30 = r30->field_7
    //     0x705640: ldur            lr, [lr, #7]
    // 0x705644: blr             lr
    // 0x705648: ldr             x16, [THR, #0x90]  ; THR::empty_array
    // 0x70564c: stp             x16, x0, [SP]
    // 0x705650: r0 = Map._fromLiteral()
    //     0x705650: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x705654: ldur            x1, [fp, #-8]
    // 0x705658: StoreField: r1->field_13 = r0
    //     0x705658: stur            w0, [x1, #0x13]
    //     0x70565c: ldurb           w16, [x1, #-1]
    //     0x705660: ldurb           w17, [x0, #-1]
    //     0x705664: and             x16, x17, x16, lsr #2
    //     0x705668: tst             x16, HEAP, lsr #32
    //     0x70566c: b.eq            #0x705674
    //     0x705670: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x705674: r0 = Closure: (String) => String from Function '_canonicalizer@884165596': static.
    //     0x705674: add             x0, PP, #0x10, lsl #12  ; [pp+0x10820] Closure: (String) => String from Function '_canonicalizer@884165596': static. (0x7e54fb0dc824)
    //     0x705678: ldr             x0, [x0, #0x820]
    // 0x70567c: StoreField: r1->field_b = r0
    //     0x70567c: stur            w0, [x1, #0xb]
    // 0x705680: ldur            x2, [fp, #-0x10]
    // 0x705684: r0 = addAll()
    //     0x705684: bl              #0xd12bec  ; [package:collection/src/canonicalized_map.dart] CanonicalizedMap::addAll
    // 0x705688: r0 = Null
    //     0x705688: mov             x0, NULL
    // 0x70568c: LeaveFrame
    //     0x70568c: mov             SP, fp
    //     0x705690: ldp             fp, lr, [SP], #0x10
    // 0x705694: ret
    //     0x705694: ret             
    // 0x705698: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x705698: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x70569c: b               #0x705628
  }
  get _ entries(/* No info */) {
    // ** addr: 0x80039c, size: 0xb8
    // 0x80039c: EnterFrame
    //     0x80039c: stp             fp, lr, [SP, #-0x10]!
    //     0x8003a0: mov             fp, SP
    // 0x8003a4: AllocStack(0x38)
    //     0x8003a4: sub             SP, SP, #0x38
    // 0x8003a8: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r1, fp-0x8 */)
    //     0x8003a8: stur            x1, [fp, #-8]
    // 0x8003ac: CheckStackOverflow
    //     0x8003ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8003b0: cmp             SP, x16
    //     0x8003b4: b.ls            #0x80044c
    // 0x8003b8: r1 = 1
    //     0x8003b8: movz            x1, #0x1
    // 0x8003bc: r0 = AllocateContext()
    //     0x8003bc: bl              #0xec126c  ; AllocateContextStub
    // 0x8003c0: mov             x4, x0
    // 0x8003c4: ldur            x0, [fp, #-8]
    // 0x8003c8: stur            x4, [fp, #-0x18]
    // 0x8003cc: StoreField: r4->field_f = r0
    //     0x8003cc: stur            w0, [x4, #0xf]
    // 0x8003d0: LoadField: r5 = r0->field_7
    //     0x8003d0: ldur            w5, [x0, #7]
    // 0x8003d4: DecompressPointer r5
    //     0x8003d4: add             x5, x5, HEAP, lsl #32
    // 0x8003d8: mov             x2, x5
    // 0x8003dc: stur            x5, [fp, #-0x10]
    // 0x8003e0: r1 = Null
    //     0x8003e0: mov             x1, NULL
    // 0x8003e4: r3 = <MapEntry<X1, X2>>
    //     0x8003e4: add             x3, PP, #0x21, lsl #12  ; [pp+0x21fb0] TypeArguments: <MapEntry<X1, X2>>
    //     0x8003e8: ldr             x3, [x3, #0xfb0]
    // 0x8003ec: r30 = InstantiateTypeArgumentsStub
    //     0x8003ec: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8003f0: LoadField: r30 = r30->field_7
    //     0x8003f0: ldur            lr, [lr, #7]
    // 0x8003f4: blr             lr
    // 0x8003f8: mov             x2, x0
    // 0x8003fc: ldur            x0, [fp, #-8]
    // 0x800400: stur            x2, [fp, #-0x20]
    // 0x800404: LoadField: r1 = r0->field_13
    //     0x800404: ldur            w1, [x0, #0x13]
    // 0x800408: DecompressPointer r1
    //     0x800408: add             x1, x1, HEAP, lsl #32
    // 0x80040c: r0 = entries()
    //     0x80040c: bl              #0x89c028  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::entries
    // 0x800410: ldur            x2, [fp, #-0x18]
    // 0x800414: ldur            x3, [fp, #-0x10]
    // 0x800418: r1 = Function '<anonymous closure>':.
    //     0x800418: add             x1, PP, #0x21, lsl #12  ; [pp+0x21fb8] AnonymousClosure: (0x800454), in [package:collection/src/canonicalized_map.dart] CanonicalizedMap::entries (0x80039c)
    //     0x80041c: ldr             x1, [x1, #0xfb8]
    // 0x800420: stur            x0, [fp, #-8]
    // 0x800424: r0 = AllocateClosureTA()
    //     0x800424: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x800428: ldur            x16, [fp, #-0x20]
    // 0x80042c: ldur            lr, [fp, #-8]
    // 0x800430: stp             lr, x16, [SP, #8]
    // 0x800434: str             x0, [SP]
    // 0x800438: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x800438: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x80043c: r0 = map()
    //     0x80043c: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0x800440: LeaveFrame
    //     0x800440: mov             SP, fp
    //     0x800444: ldp             fp, lr, [SP], #0x10
    // 0x800448: ret
    //     0x800448: ret             
    // 0x80044c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80044c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x800450: b               #0x8003b8
  }
  [closure] MapEntry<X1, X2> <anonymous closure>(dynamic, MapEntry<X0, MapEntry<X1, X2>>) {
    // ** addr: 0x800454, size: 0xa0
    // 0x800454: EnterFrame
    //     0x800454: stp             fp, lr, [SP, #-0x10]!
    //     0x800458: mov             fp, SP
    // 0x80045c: AllocStack(0x10)
    //     0x80045c: sub             SP, SP, #0x10
    // 0x800460: SetupParameters()
    //     0x800460: ldr             x0, [fp, #0x18]
    //     0x800464: ldur            w1, [x0, #0x17]
    //     0x800468: add             x1, x1, HEAP, lsl #32
    // 0x80046c: LoadField: r0 = r1->field_f
    //     0x80046c: ldur            w0, [x1, #0xf]
    // 0x800470: DecompressPointer r0
    //     0x800470: add             x0, x0, HEAP, lsl #32
    // 0x800474: LoadField: r2 = r0->field_7
    //     0x800474: ldur            w2, [x0, #7]
    // 0x800478: DecompressPointer r2
    //     0x800478: add             x2, x2, HEAP, lsl #32
    // 0x80047c: r1 = Null
    //     0x80047c: mov             x1, NULL
    // 0x800480: r3 = <X1, X2>
    //     0x800480: add             x3, PP, #0x10, lsl #12  ; [pp+0x10870] TypeArguments: <X1, X2>
    //     0x800484: ldr             x3, [x3, #0x870]
    // 0x800488: r0 = Null
    //     0x800488: mov             x0, NULL
    // 0x80048c: cmp             x2, x0
    // 0x800490: b.eq            #0x8004a0
    // 0x800494: r30 = InstantiateTypeArgumentsStub
    //     0x800494: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x800498: LoadField: r30 = r30->field_7
    //     0x800498: ldur            lr, [lr, #7]
    // 0x80049c: blr             lr
    // 0x8004a0: mov             x1, x0
    // 0x8004a4: ldr             x0, [fp, #0x10]
    // 0x8004a8: LoadField: r2 = r0->field_f
    //     0x8004a8: ldur            w2, [x0, #0xf]
    // 0x8004ac: DecompressPointer r2
    //     0x8004ac: add             x2, x2, HEAP, lsl #32
    // 0x8004b0: cmp             w2, NULL
    // 0x8004b4: b.eq            #0x8004f0
    // 0x8004b8: LoadField: r0 = r2->field_b
    //     0x8004b8: ldur            w0, [x2, #0xb]
    // 0x8004bc: DecompressPointer r0
    //     0x8004bc: add             x0, x0, HEAP, lsl #32
    // 0x8004c0: stur            x0, [fp, #-0x10]
    // 0x8004c4: LoadField: r3 = r2->field_f
    //     0x8004c4: ldur            w3, [x2, #0xf]
    // 0x8004c8: DecompressPointer r3
    //     0x8004c8: add             x3, x3, HEAP, lsl #32
    // 0x8004cc: stur            x3, [fp, #-8]
    // 0x8004d0: r0 = MapEntry()
    //     0x8004d0: bl              #0x65ce18  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x8004d4: ldur            x1, [fp, #-0x10]
    // 0x8004d8: StoreField: r0->field_b = r1
    //     0x8004d8: stur            w1, [x0, #0xb]
    // 0x8004dc: ldur            x1, [fp, #-8]
    // 0x8004e0: StoreField: r0->field_f = r1
    //     0x8004e0: stur            w1, [x0, #0xf]
    // 0x8004e4: LeaveFrame
    //     0x8004e4: mov             SP, fp
    //     0x8004e8: ldp             fp, lr, [SP], #0x10
    // 0x8004ec: ret
    //     0x8004ec: ret             
    // 0x8004f0: r0 = NullErrorSharedWithoutFPURegs()
    //     0x8004f0: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  get _ length(/* No info */) {
    // ** addr: 0x982874, size: 0x3c
    // 0x982874: EnterFrame
    //     0x982874: stp             fp, lr, [SP, #-0x10]!
    //     0x982878: mov             fp, SP
    // 0x98287c: ldr             x1, [fp, #0x10]
    // 0x982880: LoadField: r2 = r1->field_13
    //     0x982880: ldur            w2, [x1, #0x13]
    // 0x982884: DecompressPointer r2
    //     0x982884: add             x2, x2, HEAP, lsl #32
    // 0x982888: LoadField: r1 = r2->field_13
    //     0x982888: ldur            w1, [x2, #0x13]
    // 0x98288c: r3 = LoadInt32Instr(r1)
    //     0x98288c: sbfx            x3, x1, #1, #0x1f
    // 0x982890: asr             x1, x3, #1
    // 0x982894: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x982894: ldur            w3, [x2, #0x17]
    // 0x982898: r2 = LoadInt32Instr(r3)
    //     0x982898: sbfx            x2, x3, #1, #0x1f
    // 0x98289c: sub             x3, x1, x2
    // 0x9828a0: lsl             x0, x3, #1
    // 0x9828a4: LeaveFrame
    //     0x9828a4: mov             SP, fp
    //     0x9828a8: ldp             fp, lr, [SP], #0x10
    // 0x9828ac: ret
    //     0x9828ac: ret             
  }
  _ addAll(/* No info */) {
    // ** addr: 0xd12bec, size: 0x78
    // 0xd12bec: EnterFrame
    //     0xd12bec: stp             fp, lr, [SP, #-0x10]!
    //     0xd12bf0: mov             fp, SP
    // 0xd12bf4: AllocStack(0x10)
    //     0xd12bf4: sub             SP, SP, #0x10
    // 0xd12bf8: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xd12bf8: mov             x0, x1
    //     0xd12bfc: stur            x1, [fp, #-8]
    //     0xd12c00: mov             x1, x2
    //     0xd12c04: stur            x2, [fp, #-0x10]
    // 0xd12c08: CheckStackOverflow
    //     0xd12c08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd12c0c: cmp             SP, x16
    //     0xd12c10: b.ls            #0xd12c5c
    // 0xd12c14: r1 = 1
    //     0xd12c14: movz            x1, #0x1
    // 0xd12c18: r0 = AllocateContext()
    //     0xd12c18: bl              #0xec126c  ; AllocateContextStub
    // 0xd12c1c: mov             x1, x0
    // 0xd12c20: ldur            x0, [fp, #-8]
    // 0xd12c24: StoreField: r1->field_f = r0
    //     0xd12c24: stur            w0, [x1, #0xf]
    // 0xd12c28: LoadField: r3 = r0->field_7
    //     0xd12c28: ldur            w3, [x0, #7]
    // 0xd12c2c: DecompressPointer r3
    //     0xd12c2c: add             x3, x3, HEAP, lsl #32
    // 0xd12c30: mov             x2, x1
    // 0xd12c34: r1 = Function '<anonymous closure>':.
    //     0xd12c34: add             x1, PP, #0x10, lsl #12  ; [pp+0x10828] AnonymousClosure: (0xd12c64), in [package:collection/src/canonicalized_map.dart] CanonicalizedMap::addAll (0xd12bec)
    //     0xd12c38: ldr             x1, [x1, #0x828]
    // 0xd12c3c: r0 = AllocateClosureTA()
    //     0xd12c3c: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xd12c40: ldur            x1, [fp, #-0x10]
    // 0xd12c44: mov             x2, x0
    // 0xd12c48: r0 = forEach()
    //     0xd12c48: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xd12c4c: r0 = Null
    //     0xd12c4c: mov             x0, NULL
    // 0xd12c50: LeaveFrame
    //     0xd12c50: mov             SP, fp
    //     0xd12c54: ldp             fp, lr, [SP], #0x10
    // 0xd12c58: ret
    //     0xd12c58: ret             
    // 0xd12c5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd12c5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd12c60: b               #0xd12c14
  }
  [closure] void <anonymous closure>(dynamic, X1, X2) {
    // ** addr: 0xd12c64, size: 0x50
    // 0xd12c64: EnterFrame
    //     0xd12c64: stp             fp, lr, [SP, #-0x10]!
    //     0xd12c68: mov             fp, SP
    // 0xd12c6c: ldr             x0, [fp, #0x20]
    // 0xd12c70: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd12c70: ldur            w1, [x0, #0x17]
    // 0xd12c74: DecompressPointer r1
    //     0xd12c74: add             x1, x1, HEAP, lsl #32
    // 0xd12c78: CheckStackOverflow
    //     0xd12c78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd12c7c: cmp             SP, x16
    //     0xd12c80: b.ls            #0xd12cac
    // 0xd12c84: LoadField: r0 = r1->field_f
    //     0xd12c84: ldur            w0, [x1, #0xf]
    // 0xd12c88: DecompressPointer r0
    //     0xd12c88: add             x0, x0, HEAP, lsl #32
    // 0xd12c8c: mov             x1, x0
    // 0xd12c90: ldr             x2, [fp, #0x18]
    // 0xd12c94: ldr             x3, [fp, #0x10]
    // 0xd12c98: r0 = []=()
    //     0xd12c98: bl              #0xd433a8  ; [package:collection/src/canonicalized_map.dart] CanonicalizedMap::[]=
    // 0xd12c9c: ldr             x0, [fp, #0x10]
    // 0xd12ca0: LeaveFrame
    //     0xd12ca0: mov             SP, fp
    //     0xd12ca4: ldp             fp, lr, [SP], #0x10
    // 0xd12ca8: ret
    //     0xd12ca8: ret             
    // 0xd12cac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd12cac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd12cb0: b               #0xd12c84
  }
  get _ values(/* No info */) {
    // ** addr: 0xd20688, size: 0x110
    // 0xd20688: EnterFrame
    //     0xd20688: stp             fp, lr, [SP, #-0x10]!
    //     0xd2068c: mov             fp, SP
    // 0xd20690: AllocStack(0x40)
    //     0xd20690: sub             SP, SP, #0x40
    // 0xd20694: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r1, fp-0x8 */)
    //     0xd20694: stur            x1, [fp, #-8]
    // 0xd20698: CheckStackOverflow
    //     0xd20698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2069c: cmp             SP, x16
    //     0xd206a0: b.ls            #0xd20790
    // 0xd206a4: r1 = 1
    //     0xd206a4: movz            x1, #0x1
    // 0xd206a8: r0 = AllocateContext()
    //     0xd206a8: bl              #0xec126c  ; AllocateContextStub
    // 0xd206ac: mov             x4, x0
    // 0xd206b0: ldur            x0, [fp, #-8]
    // 0xd206b4: stur            x4, [fp, #-0x18]
    // 0xd206b8: StoreField: r4->field_f = r0
    //     0xd206b8: stur            w0, [x4, #0xf]
    // 0xd206bc: LoadField: r5 = r0->field_7
    //     0xd206bc: ldur            w5, [x0, #7]
    // 0xd206c0: DecompressPointer r5
    //     0xd206c0: add             x5, x5, HEAP, lsl #32
    // 0xd206c4: mov             x2, x5
    // 0xd206c8: stur            x5, [fp, #-0x10]
    // 0xd206cc: r1 = Null
    //     0xd206cc: mov             x1, NULL
    // 0xd206d0: r3 = <X2>
    //     0xd206d0: add             x3, PP, #0x21, lsl #12  ; [pp+0x21fa0] TypeArguments: <X2>
    //     0xd206d4: ldr             x3, [x3, #0xfa0]
    // 0xd206d8: r0 = Null
    //     0xd206d8: mov             x0, NULL
    // 0xd206dc: cmp             x2, x0
    // 0xd206e0: b.eq            #0xd206f0
    // 0xd206e4: r30 = InstantiateTypeArgumentsStub
    //     0xd206e4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xd206e8: LoadField: r30 = r30->field_7
    //     0xd206e8: ldur            lr, [lr, #7]
    // 0xd206ec: blr             lr
    // 0xd206f0: mov             x4, x0
    // 0xd206f4: ldur            x0, [fp, #-8]
    // 0xd206f8: stur            x4, [fp, #-0x28]
    // 0xd206fc: LoadField: r5 = r0->field_13
    //     0xd206fc: ldur            w5, [x0, #0x13]
    // 0xd20700: DecompressPointer r5
    //     0xd20700: add             x5, x5, HEAP, lsl #32
    // 0xd20704: stur            x5, [fp, #-0x20]
    // 0xd20708: LoadField: r2 = r5->field_7
    //     0xd20708: ldur            w2, [x5, #7]
    // 0xd2070c: DecompressPointer r2
    //     0xd2070c: add             x2, x2, HEAP, lsl #32
    // 0xd20710: r1 = Null
    //     0xd20710: mov             x1, NULL
    // 0xd20714: r3 = <X1>
    //     0xd20714: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0xd20718: r0 = Null
    //     0xd20718: mov             x0, NULL
    // 0xd2071c: cmp             x2, x0
    // 0xd20720: b.eq            #0xd20730
    // 0xd20724: r30 = InstantiateTypeArgumentsStub
    //     0xd20724: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xd20728: LoadField: r30 = r30->field_7
    //     0xd20728: ldur            lr, [lr, #7]
    // 0xd2072c: blr             lr
    // 0xd20730: mov             x1, x0
    // 0xd20734: r0 = _CompactIterable()
    //     0xd20734: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xd20738: mov             x4, x0
    // 0xd2073c: ldur            x0, [fp, #-0x20]
    // 0xd20740: stur            x4, [fp, #-8]
    // 0xd20744: StoreField: r4->field_b = r0
    //     0xd20744: stur            w0, [x4, #0xb]
    // 0xd20748: r0 = -1
    //     0xd20748: movn            x0, #0
    // 0xd2074c: StoreField: r4->field_f = r0
    //     0xd2074c: stur            x0, [x4, #0xf]
    // 0xd20750: r0 = 2
    //     0xd20750: movz            x0, #0x2
    // 0xd20754: ArrayStore: r4[0] = r0  ; List_8
    //     0xd20754: stur            x0, [x4, #0x17]
    // 0xd20758: ldur            x2, [fp, #-0x18]
    // 0xd2075c: ldur            x3, [fp, #-0x10]
    // 0xd20760: r1 = Function '<anonymous closure>':.
    //     0xd20760: add             x1, PP, #0x21, lsl #12  ; [pp+0x21fa8] Function: [dart:io] _SecureFilterImpl::buffers (0xcfc67c)
    //     0xd20764: ldr             x1, [x1, #0xfa8]
    // 0xd20768: r0 = AllocateClosureTA()
    //     0xd20768: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xd2076c: ldur            x16, [fp, #-0x28]
    // 0xd20770: ldur            lr, [fp, #-8]
    // 0xd20774: stp             lr, x16, [SP, #8]
    // 0xd20778: str             x0, [SP]
    // 0xd2077c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xd2077c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xd20780: r0 = map()
    //     0xd20780: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xd20784: LeaveFrame
    //     0xd20784: mov             SP, fp
    //     0xd20788: ldp             fp, lr, [SP], #0x10
    // 0xd2078c: ret
    //     0xd2078c: ret             
    // 0xd20790: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd20790: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd20794: b               #0xd206a4
  }
  X2? remove(CanonicalizedMap<X0, X1, X2>, Object?) {
    // ** addr: 0xd2080c, size: 0x178
    // 0xd2080c: EnterFrame
    //     0xd2080c: stp             fp, lr, [SP, #-0x10]!
    //     0xd20810: mov             fp, SP
    // 0xd20814: AllocStack(0x20)
    //     0xd20814: sub             SP, SP, #0x20
    // 0xd20818: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xd20818: mov             x4, x1
    //     0xd2081c: mov             x3, x2
    //     0xd20820: stur            x1, [fp, #-0x10]
    //     0xd20824: stur            x2, [fp, #-0x18]
    // 0xd20828: CheckStackOverflow
    //     0xd20828: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2082c: cmp             SP, x16
    //     0xd20830: b.ls            #0xd2097c
    // 0xd20834: LoadField: r5 = r4->field_7
    //     0xd20834: ldur            w5, [x4, #7]
    // 0xd20838: DecompressPointer r5
    //     0xd20838: add             x5, x5, HEAP, lsl #32
    // 0xd2083c: mov             x0, x3
    // 0xd20840: mov             x2, x5
    // 0xd20844: stur            x5, [fp, #-8]
    // 0xd20848: r1 = Null
    //     0xd20848: mov             x1, NULL
    // 0xd2084c: cmp             w2, NULL
    // 0xd20850: b.eq            #0xd208e8
    // 0xd20854: LoadField: r3 = r2->field_1b
    //     0xd20854: ldur            w3, [x2, #0x1b]
    // 0xd20858: DecompressPointer r3
    //     0xd20858: add             x3, x3, HEAP, lsl #32
    // 0xd2085c: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xd20860: cmp             w3, w16
    // 0xd20864: b.eq            #0xd208e8
    // 0xd20868: r16 = Object?
    //     0xd20868: ldr             x16, [PP, #0x1648]  ; [pp+0x1648] Type: Object?
    // 0xd2086c: cmp             w3, w16
    // 0xd20870: b.eq            #0xd208e8
    // 0xd20874: r16 = void?
    //     0xd20874: ldr             x16, [PP, #0x1650]  ; [pp+0x1650] Type: void?
    // 0xd20878: cmp             w3, w16
    // 0xd2087c: b.eq            #0xd208e8
    // 0xd20880: tbnz            w0, #0, #0xd2089c
    // 0xd20884: r16 = int
    //     0xd20884: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xd20888: cmp             w3, w16
    // 0xd2088c: b.eq            #0xd208e8
    // 0xd20890: r16 = num
    //     0xd20890: ldr             x16, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xd20894: cmp             w3, w16
    // 0xd20898: b.eq            #0xd208e8
    // 0xd2089c: r3 = SubtypeTestCache
    //     0xd2089c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1ca18] SubtypeTestCache
    //     0xd208a0: ldr             x3, [x3, #0xa18]
    // 0xd208a4: r30 = Subtype6TestCacheStub
    //     0xd208a4: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27cc)
    // 0xd208a8: LoadField: r30 = r30->field_7
    //     0xd208a8: ldur            lr, [lr, #7]
    // 0xd208ac: blr             lr
    // 0xd208b0: cmp             w7, NULL
    // 0xd208b4: b.eq            #0xd208c0
    // 0xd208b8: tbnz            w7, #4, #0xd208e0
    // 0xd208bc: b               #0xd208e8
    // 0xd208c0: r8 = X1
    //     0xd208c0: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1ca20] TypeParameter: X1
    //     0xd208c4: ldr             x8, [x8, #0xa20]
    // 0xd208c8: r3 = SubtypeTestCache
    //     0xd208c8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1ca28] SubtypeTestCache
    //     0xd208cc: ldr             x3, [x3, #0xa28]
    // 0xd208d0: r30 = InstanceOfStub
    //     0xd208d0: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd208d4: LoadField: r30 = r30->field_7
    //     0xd208d4: ldur            lr, [lr, #7]
    // 0xd208d8: blr             lr
    // 0xd208dc: b               #0xd208ec
    // 0xd208e0: r0 = false
    //     0xd208e0: add             x0, NULL, #0x30  ; false
    // 0xd208e4: b               #0xd208ec
    // 0xd208e8: r0 = true
    //     0xd208e8: add             x0, NULL, #0x20  ; true
    // 0xd208ec: tbnz            w0, #4, #0xd2096c
    // 0xd208f0: ldur            x0, [fp, #-0x10]
    // 0xd208f4: LoadField: r3 = r0->field_13
    //     0xd208f4: ldur            w3, [x0, #0x13]
    // 0xd208f8: DecompressPointer r3
    //     0xd208f8: add             x3, x3, HEAP, lsl #32
    // 0xd208fc: ldur            x0, [fp, #-0x18]
    // 0xd20900: ldur            x2, [fp, #-8]
    // 0xd20904: stur            x3, [fp, #-0x20]
    // 0xd20908: r1 = Null
    //     0xd20908: mov             x1, NULL
    // 0xd2090c: cmp             w2, NULL
    // 0xd20910: b.eq            #0xd20930
    // 0xd20914: LoadField: r4 = r2->field_1b
    //     0xd20914: ldur            w4, [x2, #0x1b]
    // 0xd20918: DecompressPointer r4
    //     0xd20918: add             x4, x4, HEAP, lsl #32
    // 0xd2091c: r8 = X1
    //     0xd2091c: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xd20920: LoadField: r9 = r4->field_7
    //     0xd20920: ldur            x9, [x4, #7]
    // 0xd20924: r3 = Null
    //     0xd20924: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1ca30] Null
    //     0xd20928: ldr             x3, [x3, #0xa30]
    // 0xd2092c: blr             x9
    // 0xd20930: ldur            x1, [fp, #-0x18]
    // 0xd20934: r0 = _canonicalizer()
    //     0xd20934: bl              #0x6dc854  ; [package:http_parser/src/case_insensitive_map.dart] CaseInsensitiveMap::_canonicalizer
    // 0xd20938: ldur            x1, [fp, #-0x20]
    // 0xd2093c: mov             x2, x0
    // 0xd20940: r0 = remove()
    //     0xd20940: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0xd20944: cmp             w0, NULL
    // 0xd20948: b.ne            #0xd20954
    // 0xd2094c: r0 = Null
    //     0xd2094c: mov             x0, NULL
    // 0xd20950: b               #0xd20960
    // 0xd20954: LoadField: r1 = r0->field_f
    //     0xd20954: ldur            w1, [x0, #0xf]
    // 0xd20958: DecompressPointer r1
    //     0xd20958: add             x1, x1, HEAP, lsl #32
    // 0xd2095c: mov             x0, x1
    // 0xd20960: LeaveFrame
    //     0xd20960: mov             SP, fp
    //     0xd20964: ldp             fp, lr, [SP], #0x10
    // 0xd20968: ret
    //     0xd20968: ret             
    // 0xd2096c: r0 = Null
    //     0xd2096c: mov             x0, NULL
    // 0xd20970: LeaveFrame
    //     0xd20970: mov             SP, fp
    //     0xd20974: ldp             fp, lr, [SP], #0x10
    // 0xd20978: ret
    //     0xd20978: ret             
    // 0xd2097c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd2097c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd20980: b               #0xd20834
  }
  get _ isEmpty(/* No info */) {
    // ** addr: 0xd24404, size: 0x44
    // 0xd24404: EnterFrame
    //     0xd24404: stp             fp, lr, [SP, #-0x10]!
    //     0xd24408: mov             fp, SP
    // 0xd2440c: LoadField: r2 = r1->field_13
    //     0xd2440c: ldur            w2, [x1, #0x13]
    // 0xd24410: DecompressPointer r2
    //     0xd24410: add             x2, x2, HEAP, lsl #32
    // 0xd24414: LoadField: r1 = r2->field_13
    //     0xd24414: ldur            w1, [x2, #0x13]
    // 0xd24418: r3 = LoadInt32Instr(r1)
    //     0xd24418: sbfx            x3, x1, #1, #0x1f
    // 0xd2441c: asr             x1, x3, #1
    // 0xd24420: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xd24420: ldur            w3, [x2, #0x17]
    // 0xd24424: r2 = LoadInt32Instr(r3)
    //     0xd24424: sbfx            x2, x3, #1, #0x1f
    // 0xd24428: sub             x3, x1, x2
    // 0xd2442c: cbz             x3, #0xd24438
    // 0xd24430: r0 = false
    //     0xd24430: add             x0, NULL, #0x30  ; false
    // 0xd24434: b               #0xd2443c
    // 0xd24438: r0 = true
    //     0xd24438: add             x0, NULL, #0x20  ; true
    // 0xd2443c: LeaveFrame
    //     0xd2443c: mov             SP, fp
    //     0xd24440: ldp             fp, lr, [SP], #0x10
    // 0xd24444: ret
    //     0xd24444: ret             
  }
  get _ keys(/* No info */) {
    // ** addr: 0xd2532c, size: 0x10c
    // 0xd2532c: EnterFrame
    //     0xd2532c: stp             fp, lr, [SP, #-0x10]!
    //     0xd25330: mov             fp, SP
    // 0xd25334: AllocStack(0x40)
    //     0xd25334: sub             SP, SP, #0x40
    // 0xd25338: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r1, fp-0x8 */)
    //     0xd25338: stur            x1, [fp, #-8]
    // 0xd2533c: CheckStackOverflow
    //     0xd2533c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd25340: cmp             SP, x16
    //     0xd25344: b.ls            #0xd25430
    // 0xd25348: r1 = 1
    //     0xd25348: movz            x1, #0x1
    // 0xd2534c: r0 = AllocateContext()
    //     0xd2534c: bl              #0xec126c  ; AllocateContextStub
    // 0xd25350: mov             x4, x0
    // 0xd25354: ldur            x0, [fp, #-8]
    // 0xd25358: stur            x4, [fp, #-0x18]
    // 0xd2535c: StoreField: r4->field_f = r0
    //     0xd2535c: stur            w0, [x4, #0xf]
    // 0xd25360: LoadField: r5 = r0->field_7
    //     0xd25360: ldur            w5, [x0, #7]
    // 0xd25364: DecompressPointer r5
    //     0xd25364: add             x5, x5, HEAP, lsl #32
    // 0xd25368: mov             x2, x5
    // 0xd2536c: stur            x5, [fp, #-0x10]
    // 0xd25370: r1 = Null
    //     0xd25370: mov             x1, NULL
    // 0xd25374: r3 = <X1>
    //     0xd25374: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0xd25378: r0 = Null
    //     0xd25378: mov             x0, NULL
    // 0xd2537c: cmp             x2, x0
    // 0xd25380: b.eq            #0xd25390
    // 0xd25384: r30 = InstantiateTypeArgumentsStub
    //     0xd25384: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xd25388: LoadField: r30 = r30->field_7
    //     0xd25388: ldur            lr, [lr, #7]
    // 0xd2538c: blr             lr
    // 0xd25390: mov             x4, x0
    // 0xd25394: ldur            x0, [fp, #-8]
    // 0xd25398: stur            x4, [fp, #-0x28]
    // 0xd2539c: LoadField: r5 = r0->field_13
    //     0xd2539c: ldur            w5, [x0, #0x13]
    // 0xd253a0: DecompressPointer r5
    //     0xd253a0: add             x5, x5, HEAP, lsl #32
    // 0xd253a4: stur            x5, [fp, #-0x20]
    // 0xd253a8: LoadField: r2 = r5->field_7
    //     0xd253a8: ldur            w2, [x5, #7]
    // 0xd253ac: DecompressPointer r2
    //     0xd253ac: add             x2, x2, HEAP, lsl #32
    // 0xd253b0: r1 = Null
    //     0xd253b0: mov             x1, NULL
    // 0xd253b4: r3 = <X1>
    //     0xd253b4: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0xd253b8: r0 = Null
    //     0xd253b8: mov             x0, NULL
    // 0xd253bc: cmp             x2, x0
    // 0xd253c0: b.eq            #0xd253d0
    // 0xd253c4: r30 = InstantiateTypeArgumentsStub
    //     0xd253c4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xd253c8: LoadField: r30 = r30->field_7
    //     0xd253c8: ldur            lr, [lr, #7]
    // 0xd253cc: blr             lr
    // 0xd253d0: mov             x1, x0
    // 0xd253d4: r0 = _CompactIterable()
    //     0xd253d4: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xd253d8: mov             x4, x0
    // 0xd253dc: ldur            x0, [fp, #-0x20]
    // 0xd253e0: stur            x4, [fp, #-8]
    // 0xd253e4: StoreField: r4->field_b = r0
    //     0xd253e4: stur            w0, [x4, #0xb]
    // 0xd253e8: r0 = -1
    //     0xd253e8: movn            x0, #0
    // 0xd253ec: StoreField: r4->field_f = r0
    //     0xd253ec: stur            x0, [x4, #0xf]
    // 0xd253f0: r0 = 2
    //     0xd253f0: movz            x0, #0x2
    // 0xd253f4: ArrayStore: r4[0] = r0  ; List_8
    //     0xd253f4: stur            x0, [x4, #0x17]
    // 0xd253f8: ldur            x2, [fp, #-0x18]
    // 0xd253fc: ldur            x3, [fp, #-0x10]
    // 0xd25400: r1 = Function '<anonymous closure>':.
    //     0xd25400: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1ca78] Function: [dart:ui] Paint::_objects (0xbfdb8c)
    //     0xd25404: ldr             x1, [x1, #0xa78]
    // 0xd25408: r0 = AllocateClosureTA()
    //     0xd25408: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xd2540c: ldur            x16, [fp, #-0x28]
    // 0xd25410: ldur            lr, [fp, #-8]
    // 0xd25414: stp             lr, x16, [SP, #8]
    // 0xd25418: str             x0, [SP]
    // 0xd2541c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xd2541c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xd25420: r0 = map()
    //     0xd25420: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xd25424: LeaveFrame
    //     0xd25424: mov             SP, fp
    //     0xd25428: ldp             fp, lr, [SP], #0x10
    // 0xd2542c: ret
    //     0xd2542c: ret             
    // 0xd25430: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd25430: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd25434: b               #0xd25348
  }
  _ forEach(/* No info */) {
    // ** addr: 0xd25514, size: 0x84
    // 0xd25514: EnterFrame
    //     0xd25514: stp             fp, lr, [SP, #-0x10]!
    //     0xd25518: mov             fp, SP
    // 0xd2551c: AllocStack(0x10)
    //     0xd2551c: sub             SP, SP, #0x10
    // 0xd25520: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xd25520: stur            x1, [fp, #-8]
    //     0xd25524: stur            x2, [fp, #-0x10]
    // 0xd25528: CheckStackOverflow
    //     0xd25528: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2552c: cmp             SP, x16
    //     0xd25530: b.ls            #0xd25590
    // 0xd25534: r1 = 2
    //     0xd25534: movz            x1, #0x2
    // 0xd25538: r0 = AllocateContext()
    //     0xd25538: bl              #0xec126c  ; AllocateContextStub
    // 0xd2553c: mov             x1, x0
    // 0xd25540: ldur            x0, [fp, #-8]
    // 0xd25544: StoreField: r1->field_f = r0
    //     0xd25544: stur            w0, [x1, #0xf]
    // 0xd25548: ldur            x2, [fp, #-0x10]
    // 0xd2554c: StoreField: r1->field_13 = r2
    //     0xd2554c: stur            w2, [x1, #0x13]
    // 0xd25550: LoadField: r4 = r0->field_13
    //     0xd25550: ldur            w4, [x0, #0x13]
    // 0xd25554: DecompressPointer r4
    //     0xd25554: add             x4, x4, HEAP, lsl #32
    // 0xd25558: stur            x4, [fp, #-0x10]
    // 0xd2555c: LoadField: r3 = r0->field_7
    //     0xd2555c: ldur            w3, [x0, #7]
    // 0xd25560: DecompressPointer r3
    //     0xd25560: add             x3, x3, HEAP, lsl #32
    // 0xd25564: mov             x2, x1
    // 0xd25568: r1 = Function '<anonymous closure>':.
    //     0xd25568: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1ca80] AnonymousClosure: (0x6dcc28), in [package:collection/src/canonicalized_map.dart] CanonicalizedMap::map (0x6dcb70)
    //     0xd2556c: ldr             x1, [x1, #0xa80]
    // 0xd25570: r0 = AllocateClosureTA()
    //     0xd25570: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xd25574: ldur            x1, [fp, #-0x10]
    // 0xd25578: mov             x2, x0
    // 0xd2557c: r0 = forEach()
    //     0xd2557c: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xd25580: r0 = Null
    //     0xd25580: mov             x0, NULL
    // 0xd25584: LeaveFrame
    //     0xd25584: mov             SP, fp
    //     0xd25588: ldp             fp, lr, [SP], #0x10
    // 0xd2558c: ret
    //     0xd2558c: ret             
    // 0xd25590: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd25590: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd25594: b               #0xd25534
  }
  _ containsKey(/* No info */) {
    // ** addr: 0xd2c078, size: 0x15c
    // 0xd2c078: EnterFrame
    //     0xd2c078: stp             fp, lr, [SP, #-0x10]!
    //     0xd2c07c: mov             fp, SP
    // 0xd2c080: AllocStack(0x20)
    //     0xd2c080: sub             SP, SP, #0x20
    // 0xd2c084: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xd2c084: mov             x4, x1
    //     0xd2c088: mov             x3, x2
    //     0xd2c08c: stur            x1, [fp, #-0x10]
    //     0xd2c090: stur            x2, [fp, #-0x18]
    // 0xd2c094: CheckStackOverflow
    //     0xd2c094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2c098: cmp             SP, x16
    //     0xd2c09c: b.ls            #0xd2c1cc
    // 0xd2c0a0: LoadField: r5 = r4->field_7
    //     0xd2c0a0: ldur            w5, [x4, #7]
    // 0xd2c0a4: DecompressPointer r5
    //     0xd2c0a4: add             x5, x5, HEAP, lsl #32
    // 0xd2c0a8: mov             x0, x3
    // 0xd2c0ac: mov             x2, x5
    // 0xd2c0b0: stur            x5, [fp, #-8]
    // 0xd2c0b4: r1 = Null
    //     0xd2c0b4: mov             x1, NULL
    // 0xd2c0b8: cmp             w2, NULL
    // 0xd2c0bc: b.eq            #0xd2c154
    // 0xd2c0c0: LoadField: r3 = r2->field_1b
    //     0xd2c0c0: ldur            w3, [x2, #0x1b]
    // 0xd2c0c4: DecompressPointer r3
    //     0xd2c0c4: add             x3, x3, HEAP, lsl #32
    // 0xd2c0c8: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xd2c0cc: cmp             w3, w16
    // 0xd2c0d0: b.eq            #0xd2c154
    // 0xd2c0d4: r16 = Object?
    //     0xd2c0d4: ldr             x16, [PP, #0x1648]  ; [pp+0x1648] Type: Object?
    // 0xd2c0d8: cmp             w3, w16
    // 0xd2c0dc: b.eq            #0xd2c154
    // 0xd2c0e0: r16 = void?
    //     0xd2c0e0: ldr             x16, [PP, #0x1650]  ; [pp+0x1650] Type: void?
    // 0xd2c0e4: cmp             w3, w16
    // 0xd2c0e8: b.eq            #0xd2c154
    // 0xd2c0ec: tbnz            w0, #0, #0xd2c108
    // 0xd2c0f0: r16 = int
    //     0xd2c0f0: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xd2c0f4: cmp             w3, w16
    // 0xd2c0f8: b.eq            #0xd2c154
    // 0xd2c0fc: r16 = num
    //     0xd2c0fc: ldr             x16, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xd2c100: cmp             w3, w16
    // 0xd2c104: b.eq            #0xd2c154
    // 0xd2c108: r3 = SubtypeTestCache
    //     0xd2c108: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1ca88] SubtypeTestCache
    //     0xd2c10c: ldr             x3, [x3, #0xa88]
    // 0xd2c110: r30 = Subtype6TestCacheStub
    //     0xd2c110: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27cc)
    // 0xd2c114: LoadField: r30 = r30->field_7
    //     0xd2c114: ldur            lr, [lr, #7]
    // 0xd2c118: blr             lr
    // 0xd2c11c: cmp             w7, NULL
    // 0xd2c120: b.eq            #0xd2c12c
    // 0xd2c124: tbnz            w7, #4, #0xd2c14c
    // 0xd2c128: b               #0xd2c154
    // 0xd2c12c: r8 = X1
    //     0xd2c12c: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1ca90] TypeParameter: X1
    //     0xd2c130: ldr             x8, [x8, #0xa90]
    // 0xd2c134: r3 = SubtypeTestCache
    //     0xd2c134: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1ca98] SubtypeTestCache
    //     0xd2c138: ldr             x3, [x3, #0xa98]
    // 0xd2c13c: r30 = InstanceOfStub
    //     0xd2c13c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd2c140: LoadField: r30 = r30->field_7
    //     0xd2c140: ldur            lr, [lr, #7]
    // 0xd2c144: blr             lr
    // 0xd2c148: b               #0xd2c158
    // 0xd2c14c: r0 = false
    //     0xd2c14c: add             x0, NULL, #0x30  ; false
    // 0xd2c150: b               #0xd2c158
    // 0xd2c154: r0 = true
    //     0xd2c154: add             x0, NULL, #0x20  ; true
    // 0xd2c158: tbnz            w0, #4, #0xd2c1bc
    // 0xd2c15c: ldur            x0, [fp, #-0x10]
    // 0xd2c160: LoadField: r3 = r0->field_13
    //     0xd2c160: ldur            w3, [x0, #0x13]
    // 0xd2c164: DecompressPointer r3
    //     0xd2c164: add             x3, x3, HEAP, lsl #32
    // 0xd2c168: ldur            x0, [fp, #-0x18]
    // 0xd2c16c: ldur            x2, [fp, #-8]
    // 0xd2c170: stur            x3, [fp, #-0x20]
    // 0xd2c174: r1 = Null
    //     0xd2c174: mov             x1, NULL
    // 0xd2c178: cmp             w2, NULL
    // 0xd2c17c: b.eq            #0xd2c19c
    // 0xd2c180: LoadField: r4 = r2->field_1b
    //     0xd2c180: ldur            w4, [x2, #0x1b]
    // 0xd2c184: DecompressPointer r4
    //     0xd2c184: add             x4, x4, HEAP, lsl #32
    // 0xd2c188: r8 = X1
    //     0xd2c188: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xd2c18c: LoadField: r9 = r4->field_7
    //     0xd2c18c: ldur            x9, [x4, #7]
    // 0xd2c190: r3 = Null
    //     0xd2c190: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1caa0] Null
    //     0xd2c194: ldr             x3, [x3, #0xaa0]
    // 0xd2c198: blr             x9
    // 0xd2c19c: ldur            x1, [fp, #-0x18]
    // 0xd2c1a0: r0 = _canonicalizer()
    //     0xd2c1a0: bl              #0x6dc854  ; [package:http_parser/src/case_insensitive_map.dart] CaseInsensitiveMap::_canonicalizer
    // 0xd2c1a4: ldur            x1, [fp, #-0x20]
    // 0xd2c1a8: mov             x2, x0
    // 0xd2c1ac: r0 = containsKey()
    //     0xd2c1ac: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xd2c1b0: LeaveFrame
    //     0xd2c1b0: mov             SP, fp
    //     0xd2c1b4: ldp             fp, lr, [SP], #0x10
    // 0xd2c1b8: ret
    //     0xd2c1b8: ret             
    // 0xd2c1bc: r0 = false
    //     0xd2c1bc: add             x0, NULL, #0x30  ; false
    // 0xd2c1c0: LeaveFrame
    //     0xd2c1c0: mov             SP, fp
    //     0xd2c1c4: ldp             fp, lr, [SP], #0x10
    // 0xd2c1c8: ret
    //     0xd2c1c8: ret             
    // 0xd2c1cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd2c1cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd2c1d0: b               #0xd2c0a0
  }
  _ []=(/* No info */) {
    // ** addr: 0xd433a8, size: 0x270
    // 0xd433a8: EnterFrame
    //     0xd433a8: stp             fp, lr, [SP, #-0x10]!
    //     0xd433ac: mov             fp, SP
    // 0xd433b0: AllocStack(0x30)
    //     0xd433b0: sub             SP, SP, #0x30
    // 0xd433b4: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xd433b4: mov             x5, x1
    //     0xd433b8: mov             x4, x2
    //     0xd433bc: stur            x1, [fp, #-0x10]
    //     0xd433c0: stur            x2, [fp, #-0x18]
    //     0xd433c4: stur            x3, [fp, #-0x20]
    // 0xd433c8: CheckStackOverflow
    //     0xd433c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd433cc: cmp             SP, x16
    //     0xd433d0: b.ls            #0xd43610
    // 0xd433d4: LoadField: r6 = r5->field_7
    //     0xd433d4: ldur            w6, [x5, #7]
    // 0xd433d8: DecompressPointer r6
    //     0xd433d8: add             x6, x6, HEAP, lsl #32
    // 0xd433dc: mov             x0, x4
    // 0xd433e0: mov             x2, x6
    // 0xd433e4: stur            x6, [fp, #-8]
    // 0xd433e8: r1 = Null
    //     0xd433e8: mov             x1, NULL
    // 0xd433ec: cmp             w2, NULL
    // 0xd433f0: b.eq            #0xd43410
    // 0xd433f4: LoadField: r4 = r2->field_1b
    //     0xd433f4: ldur            w4, [x2, #0x1b]
    // 0xd433f8: DecompressPointer r4
    //     0xd433f8: add             x4, x4, HEAP, lsl #32
    // 0xd433fc: r8 = X1
    //     0xd433fc: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xd43400: LoadField: r9 = r4->field_7
    //     0xd43400: ldur            x9, [x4, #7]
    // 0xd43404: r3 = Null
    //     0xd43404: add             x3, PP, #0x10, lsl #12  ; [pp+0x10830] Null
    //     0xd43408: ldr             x3, [x3, #0x830]
    // 0xd4340c: blr             x9
    // 0xd43410: ldur            x0, [fp, #-0x20]
    // 0xd43414: ldur            x2, [fp, #-8]
    // 0xd43418: r1 = Null
    //     0xd43418: mov             x1, NULL
    // 0xd4341c: cmp             w2, NULL
    // 0xd43420: b.eq            #0xd43444
    // 0xd43424: LoadField: r4 = r2->field_1f
    //     0xd43424: ldur            w4, [x2, #0x1f]
    // 0xd43428: DecompressPointer r4
    //     0xd43428: add             x4, x4, HEAP, lsl #32
    // 0xd4342c: r8 = X2
    //     0xd4342c: add             x8, PP, #0x10, lsl #12  ; [pp+0x10840] TypeParameter: X2
    //     0xd43430: ldr             x8, [x8, #0x840]
    // 0xd43434: LoadField: r9 = r4->field_7
    //     0xd43434: ldur            x9, [x4, #7]
    // 0xd43438: r3 = Null
    //     0xd43438: add             x3, PP, #0x10, lsl #12  ; [pp+0x10848] Null
    //     0xd4343c: ldr             x3, [x3, #0x848]
    // 0xd43440: blr             x9
    // 0xd43444: ldur            x0, [fp, #-0x18]
    // 0xd43448: ldur            x2, [fp, #-8]
    // 0xd4344c: r1 = Null
    //     0xd4344c: mov             x1, NULL
    // 0xd43450: cmp             w2, NULL
    // 0xd43454: b.eq            #0xd434ec
    // 0xd43458: LoadField: r3 = r2->field_1b
    //     0xd43458: ldur            w3, [x2, #0x1b]
    // 0xd4345c: DecompressPointer r3
    //     0xd4345c: add             x3, x3, HEAP, lsl #32
    // 0xd43460: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xd43464: cmp             w3, w16
    // 0xd43468: b.eq            #0xd434ec
    // 0xd4346c: r16 = Object?
    //     0xd4346c: ldr             x16, [PP, #0x1648]  ; [pp+0x1648] Type: Object?
    // 0xd43470: cmp             w3, w16
    // 0xd43474: b.eq            #0xd434ec
    // 0xd43478: r16 = void?
    //     0xd43478: ldr             x16, [PP, #0x1650]  ; [pp+0x1650] Type: void?
    // 0xd4347c: cmp             w3, w16
    // 0xd43480: b.eq            #0xd434ec
    // 0xd43484: tbnz            w0, #0, #0xd434a0
    // 0xd43488: r16 = int
    //     0xd43488: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xd4348c: cmp             w3, w16
    // 0xd43490: b.eq            #0xd434ec
    // 0xd43494: r16 = num
    //     0xd43494: ldr             x16, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xd43498: cmp             w3, w16
    // 0xd4349c: b.eq            #0xd434ec
    // 0xd434a0: r3 = SubtypeTestCache
    //     0xd434a0: add             x3, PP, #0x10, lsl #12  ; [pp+0x10858] SubtypeTestCache
    //     0xd434a4: ldr             x3, [x3, #0x858]
    // 0xd434a8: r30 = Subtype6TestCacheStub
    //     0xd434a8: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27cc)
    // 0xd434ac: LoadField: r30 = r30->field_7
    //     0xd434ac: ldur            lr, [lr, #7]
    // 0xd434b0: blr             lr
    // 0xd434b4: cmp             w7, NULL
    // 0xd434b8: b.eq            #0xd434c4
    // 0xd434bc: tbnz            w7, #4, #0xd434e4
    // 0xd434c0: b               #0xd434ec
    // 0xd434c4: r8 = X1
    //     0xd434c4: add             x8, PP, #0x10, lsl #12  ; [pp+0x10860] TypeParameter: X1
    //     0xd434c8: ldr             x8, [x8, #0x860]
    // 0xd434cc: r3 = SubtypeTestCache
    //     0xd434cc: add             x3, PP, #0x10, lsl #12  ; [pp+0x10868] SubtypeTestCache
    //     0xd434d0: ldr             x3, [x3, #0x868]
    // 0xd434d4: r30 = InstanceOfStub
    //     0xd434d4: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd434d8: LoadField: r30 = r30->field_7
    //     0xd434d8: ldur            lr, [lr, #7]
    // 0xd434dc: blr             lr
    // 0xd434e0: b               #0xd434f0
    // 0xd434e4: r0 = false
    //     0xd434e4: add             x0, NULL, #0x30  ; false
    // 0xd434e8: b               #0xd434f0
    // 0xd434ec: r0 = true
    //     0xd434ec: add             x0, NULL, #0x20  ; true
    // 0xd434f0: tbnz            w0, #4, #0xd43600
    // 0xd434f4: ldur            x1, [fp, #-0x10]
    // 0xd434f8: ldur            x2, [fp, #-0x18]
    // 0xd434fc: ldur            x0, [fp, #-0x20]
    // 0xd43500: LoadField: r3 = r1->field_13
    //     0xd43500: ldur            w3, [x1, #0x13]
    // 0xd43504: DecompressPointer r3
    //     0xd43504: add             x3, x3, HEAP, lsl #32
    // 0xd43508: mov             x1, x2
    // 0xd4350c: stur            x3, [fp, #-0x28]
    // 0xd43510: r0 = _canonicalizer()
    //     0xd43510: bl              #0x6dc854  ; [package:http_parser/src/case_insensitive_map.dart] CaseInsensitiveMap::_canonicalizer
    // 0xd43514: ldur            x2, [fp, #-8]
    // 0xd43518: r1 = Null
    //     0xd43518: mov             x1, NULL
    // 0xd4351c: r3 = <X1, X2>
    //     0xd4351c: add             x3, PP, #0x10, lsl #12  ; [pp+0x10870] TypeArguments: <X1, X2>
    //     0xd43520: ldr             x3, [x3, #0x870]
    // 0xd43524: stur            x0, [fp, #-8]
    // 0xd43528: r0 = Null
    //     0xd43528: mov             x0, NULL
    // 0xd4352c: cmp             x2, x0
    // 0xd43530: b.eq            #0xd43540
    // 0xd43534: r30 = InstantiateTypeArgumentsStub
    //     0xd43534: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xd43538: LoadField: r30 = r30->field_7
    //     0xd43538: ldur            lr, [lr, #7]
    // 0xd4353c: blr             lr
    // 0xd43540: mov             x1, x0
    // 0xd43544: r0 = MapEntry()
    //     0xd43544: bl              #0x65ce18  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0xd43548: mov             x3, x0
    // 0xd4354c: ldur            x0, [fp, #-0x18]
    // 0xd43550: stur            x3, [fp, #-0x30]
    // 0xd43554: StoreField: r3->field_b = r0
    //     0xd43554: stur            w0, [x3, #0xb]
    // 0xd43558: ldur            x0, [fp, #-0x20]
    // 0xd4355c: StoreField: r3->field_f = r0
    //     0xd4355c: stur            w0, [x3, #0xf]
    // 0xd43560: ldur            x4, [fp, #-0x28]
    // 0xd43564: LoadField: r5 = r4->field_7
    //     0xd43564: ldur            w5, [x4, #7]
    // 0xd43568: DecompressPointer r5
    //     0xd43568: add             x5, x5, HEAP, lsl #32
    // 0xd4356c: ldur            x0, [fp, #-8]
    // 0xd43570: mov             x2, x5
    // 0xd43574: stur            x5, [fp, #-0x10]
    // 0xd43578: r1 = Null
    //     0xd43578: mov             x1, NULL
    // 0xd4357c: cmp             w2, NULL
    // 0xd43580: b.eq            #0xd435a0
    // 0xd43584: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xd43584: ldur            w4, [x2, #0x17]
    // 0xd43588: DecompressPointer r4
    //     0xd43588: add             x4, x4, HEAP, lsl #32
    // 0xd4358c: r8 = X0
    //     0xd4358c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xd43590: LoadField: r9 = r4->field_7
    //     0xd43590: ldur            x9, [x4, #7]
    // 0xd43594: r3 = Null
    //     0xd43594: add             x3, PP, #0x10, lsl #12  ; [pp+0x10878] Null
    //     0xd43598: ldr             x3, [x3, #0x878]
    // 0xd4359c: blr             x9
    // 0xd435a0: ldur            x0, [fp, #-0x30]
    // 0xd435a4: ldur            x2, [fp, #-0x10]
    // 0xd435a8: r1 = Null
    //     0xd435a8: mov             x1, NULL
    // 0xd435ac: cmp             w2, NULL
    // 0xd435b0: b.eq            #0xd435d0
    // 0xd435b4: LoadField: r4 = r2->field_1b
    //     0xd435b4: ldur            w4, [x2, #0x1b]
    // 0xd435b8: DecompressPointer r4
    //     0xd435b8: add             x4, x4, HEAP, lsl #32
    // 0xd435bc: r8 = X1
    //     0xd435bc: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xd435c0: LoadField: r9 = r4->field_7
    //     0xd435c0: ldur            x9, [x4, #7]
    // 0xd435c4: r3 = Null
    //     0xd435c4: add             x3, PP, #0x10, lsl #12  ; [pp+0x10888] Null
    //     0xd435c8: ldr             x3, [x3, #0x888]
    // 0xd435cc: blr             x9
    // 0xd435d0: ldur            x1, [fp, #-0x28]
    // 0xd435d4: ldur            x2, [fp, #-8]
    // 0xd435d8: r0 = _hashCode()
    //     0xd435d8: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xd435dc: ldur            x1, [fp, #-0x28]
    // 0xd435e0: ldur            x2, [fp, #-8]
    // 0xd435e4: ldur            x3, [fp, #-0x30]
    // 0xd435e8: mov             x5, x0
    // 0xd435ec: r0 = _set()
    //     0xd435ec: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xd435f0: r0 = Null
    //     0xd435f0: mov             x0, NULL
    // 0xd435f4: LeaveFrame
    //     0xd435f4: mov             SP, fp
    //     0xd435f8: ldp             fp, lr, [SP], #0x10
    // 0xd435fc: ret
    //     0xd435fc: ret             
    // 0xd43600: r0 = Null
    //     0xd43600: mov             x0, NULL
    // 0xd43604: LeaveFrame
    //     0xd43604: mov             SP, fp
    //     0xd43608: ldp             fp, lr, [SP], #0x10
    // 0xd4360c: ret
    //     0xd4360c: ret             
    // 0xd43610: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd43610: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd43614: b               #0xd433d4
  }
  X2? [](CanonicalizedMap<X0, X1, X2>, Object?) {
    // ** addr: 0xd43718, size: 0x198
    // 0xd43718: EnterFrame
    //     0xd43718: stp             fp, lr, [SP, #-0x10]!
    //     0xd4371c: mov             fp, SP
    // 0xd43720: AllocStack(0x20)
    //     0xd43720: sub             SP, SP, #0x20
    // 0xd43724: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xd43724: mov             x4, x1
    //     0xd43728: mov             x3, x2
    //     0xd4372c: stur            x1, [fp, #-0x10]
    //     0xd43730: stur            x2, [fp, #-0x18]
    // 0xd43734: CheckStackOverflow
    //     0xd43734: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd43738: cmp             SP, x16
    //     0xd4373c: b.ls            #0xd438a8
    // 0xd43740: LoadField: r5 = r4->field_7
    //     0xd43740: ldur            w5, [x4, #7]
    // 0xd43744: DecompressPointer r5
    //     0xd43744: add             x5, x5, HEAP, lsl #32
    // 0xd43748: mov             x0, x3
    // 0xd4374c: mov             x2, x5
    // 0xd43750: stur            x5, [fp, #-8]
    // 0xd43754: r1 = Null
    //     0xd43754: mov             x1, NULL
    // 0xd43758: cmp             w2, NULL
    // 0xd4375c: b.eq            #0xd437f4
    // 0xd43760: LoadField: r3 = r2->field_1b
    //     0xd43760: ldur            w3, [x2, #0x1b]
    // 0xd43764: DecompressPointer r3
    //     0xd43764: add             x3, x3, HEAP, lsl #32
    // 0xd43768: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xd4376c: cmp             w3, w16
    // 0xd43770: b.eq            #0xd437f4
    // 0xd43774: r16 = Object?
    //     0xd43774: ldr             x16, [PP, #0x1648]  ; [pp+0x1648] Type: Object?
    // 0xd43778: cmp             w3, w16
    // 0xd4377c: b.eq            #0xd437f4
    // 0xd43780: r16 = void?
    //     0xd43780: ldr             x16, [PP, #0x1650]  ; [pp+0x1650] Type: void?
    // 0xd43784: cmp             w3, w16
    // 0xd43788: b.eq            #0xd437f4
    // 0xd4378c: tbnz            w0, #0, #0xd437a8
    // 0xd43790: r16 = int
    //     0xd43790: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xd43794: cmp             w3, w16
    // 0xd43798: b.eq            #0xd437f4
    // 0xd4379c: r16 = num
    //     0xd4379c: ldr             x16, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xd437a0: cmp             w3, w16
    // 0xd437a4: b.eq            #0xd437f4
    // 0xd437a8: r3 = SubtypeTestCache
    //     0xd437a8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1cab0] SubtypeTestCache
    //     0xd437ac: ldr             x3, [x3, #0xab0]
    // 0xd437b0: r30 = Subtype6TestCacheStub
    //     0xd437b0: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27cc)
    // 0xd437b4: LoadField: r30 = r30->field_7
    //     0xd437b4: ldur            lr, [lr, #7]
    // 0xd437b8: blr             lr
    // 0xd437bc: cmp             w7, NULL
    // 0xd437c0: b.eq            #0xd437cc
    // 0xd437c4: tbnz            w7, #4, #0xd437ec
    // 0xd437c8: b               #0xd437f4
    // 0xd437cc: r8 = X1
    //     0xd437cc: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1cab8] TypeParameter: X1
    //     0xd437d0: ldr             x8, [x8, #0xab8]
    // 0xd437d4: r3 = SubtypeTestCache
    //     0xd437d4: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1cac0] SubtypeTestCache
    //     0xd437d8: ldr             x3, [x3, #0xac0]
    // 0xd437dc: r30 = InstanceOfStub
    //     0xd437dc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd437e0: LoadField: r30 = r30->field_7
    //     0xd437e0: ldur            lr, [lr, #7]
    // 0xd437e4: blr             lr
    // 0xd437e8: b               #0xd437f8
    // 0xd437ec: r0 = false
    //     0xd437ec: add             x0, NULL, #0x30  ; false
    // 0xd437f0: b               #0xd437f8
    // 0xd437f4: r0 = true
    //     0xd437f4: add             x0, NULL, #0x20  ; true
    // 0xd437f8: tbnz            w0, #4, #0xd43898
    // 0xd437fc: ldur            x0, [fp, #-0x10]
    // 0xd43800: LoadField: r3 = r0->field_13
    //     0xd43800: ldur            w3, [x0, #0x13]
    // 0xd43804: DecompressPointer r3
    //     0xd43804: add             x3, x3, HEAP, lsl #32
    // 0xd43808: ldur            x0, [fp, #-0x18]
    // 0xd4380c: ldur            x2, [fp, #-8]
    // 0xd43810: stur            x3, [fp, #-0x20]
    // 0xd43814: r1 = Null
    //     0xd43814: mov             x1, NULL
    // 0xd43818: cmp             w2, NULL
    // 0xd4381c: b.eq            #0xd4383c
    // 0xd43820: LoadField: r4 = r2->field_1b
    //     0xd43820: ldur            w4, [x2, #0x1b]
    // 0xd43824: DecompressPointer r4
    //     0xd43824: add             x4, x4, HEAP, lsl #32
    // 0xd43828: r8 = X1
    //     0xd43828: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xd4382c: LoadField: r9 = r4->field_7
    //     0xd4382c: ldur            x9, [x4, #7]
    // 0xd43830: r3 = Null
    //     0xd43830: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1cac8] Null
    //     0xd43834: ldr             x3, [x3, #0xac8]
    // 0xd43838: blr             x9
    // 0xd4383c: ldur            x1, [fp, #-0x18]
    // 0xd43840: r0 = _canonicalizer()
    //     0xd43840: bl              #0x6dc854  ; [package:http_parser/src/case_insensitive_map.dart] CaseInsensitiveMap::_canonicalizer
    // 0xd43844: ldur            x1, [fp, #-0x20]
    // 0xd43848: mov             x2, x0
    // 0xd4384c: r0 = _getValueOrData()
    //     0xd4384c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xd43850: ldur            x1, [fp, #-0x20]
    // 0xd43854: LoadField: r2 = r1->field_f
    //     0xd43854: ldur            w2, [x1, #0xf]
    // 0xd43858: DecompressPointer r2
    //     0xd43858: add             x2, x2, HEAP, lsl #32
    // 0xd4385c: cmp             w2, w0
    // 0xd43860: b.ne            #0xd4386c
    // 0xd43864: r1 = Null
    //     0xd43864: mov             x1, NULL
    // 0xd43868: b               #0xd43870
    // 0xd4386c: mov             x1, x0
    // 0xd43870: cmp             w1, NULL
    // 0xd43874: b.ne            #0xd43880
    // 0xd43878: r0 = Null
    //     0xd43878: mov             x0, NULL
    // 0xd4387c: b               #0xd4388c
    // 0xd43880: LoadField: r2 = r1->field_f
    //     0xd43880: ldur            w2, [x1, #0xf]
    // 0xd43884: DecompressPointer r2
    //     0xd43884: add             x2, x2, HEAP, lsl #32
    // 0xd43888: mov             x0, x2
    // 0xd4388c: LeaveFrame
    //     0xd4388c: mov             SP, fp
    //     0xd43890: ldp             fp, lr, [SP], #0x10
    // 0xd43894: ret
    //     0xd43894: ret             
    // 0xd43898: r0 = Null
    //     0xd43898: mov             x0, NULL
    // 0xd4389c: LeaveFrame
    //     0xd4389c: mov             SP, fp
    //     0xd438a0: ldp             fp, lr, [SP], #0x10
    // 0xd438a4: ret
    //     0xd438a4: ret             
    // 0xd438a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd438a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd438ac: b               #0xd43740
  }
}
