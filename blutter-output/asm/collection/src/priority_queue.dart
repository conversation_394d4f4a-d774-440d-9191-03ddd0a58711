// lib: , url: package:collection/src/priority_queue.dart

// class id: 1048668, size: 0x8
class :: {
}

// class id: 5798, size: 0x1c, field offset: 0x8
class HeapPriorityQueue<X0> extends Object
    implements PriorityQueue<X0> {

  List<X0> toList(HeapPriorityQueue<X0>) {
    // ** addr: 0x691c40, size: 0x48
    // 0x691c40: EnterFrame
    //     0x691c40: stp             fp, lr, [SP, #-0x10]!
    //     0x691c44: mov             fp, SP
    // 0x691c48: CheckStackOverflow
    //     0x691c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x691c4c: cmp             SP, x16
    //     0x691c50: b.ls            #0x691c68
    // 0x691c54: ldr             x1, [fp, #0x10]
    // 0x691c58: r0 = toList()
    //     0x691c58: bl              #0x691c70  ; [package:collection/src/priority_queue.dart] HeapPriorityQueue::toList
    // 0x691c5c: LeaveFrame
    //     0x691c5c: mov             SP, fp
    //     0x691c60: ldp             fp, lr, [SP], #0x10
    // 0x691c64: ret
    //     0x691c64: ret             
    // 0x691c68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x691c68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x691c6c: b               #0x691c54
  }
  List<X0> toList(HeapPriorityQueue<X0>) {
    // ** addr: 0x691c70, size: 0x4c
    // 0x691c70: EnterFrame
    //     0x691c70: stp             fp, lr, [SP, #-0x10]!
    //     0x691c74: mov             fp, SP
    // 0x691c78: AllocStack(0x10)
    //     0x691c78: sub             SP, SP, #0x10
    // 0x691c7c: CheckStackOverflow
    //     0x691c7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x691c80: cmp             SP, x16
    //     0x691c84: b.ls            #0x691cb4
    // 0x691c88: r0 = _toUnorderedList()
    //     0x691c88: bl              #0x691cbc  ; [package:collection/src/priority_queue.dart] HeapPriorityQueue::_toUnorderedList
    // 0x691c8c: stur            x0, [fp, #-8]
    // 0x691c90: r16 = Closure: (_TaskEntry<dynamic>, _TaskEntry<dynamic>) => int from Function '_taskSorter@414222615': static.
    //     0x691c90: ldr             x16, [PP, #0x1da8]  ; [pp+0x1da8] Closure: (_TaskEntry<dynamic>, _TaskEntry<dynamic>) => int from Function '_taskSorter@414222615': static. (0x7e54fb456bd8)
    // 0x691c94: str             x16, [SP]
    // 0x691c98: mov             x1, x0
    // 0x691c9c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x691c9c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x691ca0: r0 = sort()
    //     0x691ca0: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x691ca4: ldur            x0, [fp, #-8]
    // 0x691ca8: LeaveFrame
    //     0x691ca8: mov             SP, fp
    //     0x691cac: ldp             fp, lr, [SP], #0x10
    // 0x691cb0: ret
    //     0x691cb0: ret             
    // 0x691cb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x691cb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x691cb8: b               #0x691c88
  }
  List<X0> _toUnorderedList(HeapPriorityQueue<X0>) {
    // ** addr: 0x691cbc, size: 0x50
    // 0x691cbc: EnterFrame
    //     0x691cbc: stp             fp, lr, [SP, #-0x10]!
    //     0x691cc0: mov             fp, SP
    // 0x691cc4: CheckStackOverflow
    //     0x691cc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x691cc8: cmp             SP, x16
    //     0x691ccc: b.ls            #0x691cfc
    // 0x691cd0: LoadField: r0 = r1->field_7
    //     0x691cd0: ldur            w0, [x1, #7]
    // 0x691cd4: DecompressPointer r0
    //     0x691cd4: add             x0, x0, HEAP, lsl #32
    // 0x691cd8: mov             x1, x0
    // 0x691cdc: r2 = 0
    //     0x691cdc: movz            x2, #0
    // 0x691ce0: r0 = _GrowableList()
    //     0x691ce0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x691ce4: CheckStackOverflow
    //     0x691ce4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x691ce8: cmp             SP, x16
    //     0x691cec: b.ls            #0x691d04
    // 0x691cf0: LeaveFrame
    //     0x691cf0: mov             SP, fp
    //     0x691cf4: ldp             fp, lr, [SP], #0x10
    // 0x691cf8: ret
    //     0x691cf8: ret             
    // 0x691cfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x691cfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x691d00: b               #0x691cd0
    // 0x691d04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x691d04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x691d08: b               #0x691cf0
  }
  _ toString(/* No info */) {
    // ** addr: 0xc140a4, size: 0x44
    // 0xc140a4: EnterFrame
    //     0xc140a4: stp             fp, lr, [SP, #-0x10]!
    //     0xc140a8: mov             fp, SP
    // 0xc140ac: CheckStackOverflow
    //     0xc140ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc140b0: cmp             SP, x16
    //     0xc140b4: b.ls            #0xc140e0
    // 0xc140b8: ldr             x0, [fp, #0x10]
    // 0xc140bc: LoadField: r1 = r0->field_f
    //     0xc140bc: ldur            w1, [x0, #0xf]
    // 0xc140c0: DecompressPointer r1
    //     0xc140c0: add             x1, x1, HEAP, lsl #32
    // 0xc140c4: r2 = 0
    //     0xc140c4: movz            x2, #0
    // 0xc140c8: r0 = take()
    //     0xc140c8: bl              #0x8630a0  ; [dart:collection] ListBase::take
    // 0xc140cc: mov             x1, x0
    // 0xc140d0: r0 = iterableToShortString()
    //     0xc140d0: bl              #0xbfe8bc  ; [dart:core] Iterable::iterableToShortString
    // 0xc140d4: LeaveFrame
    //     0xc140d4: mov             SP, fp
    //     0xc140d8: ldp             fp, lr, [SP], #0x10
    // 0xc140dc: ret
    //     0xc140dc: ret             
    // 0xc140e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc140e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc140e4: b               #0xc140b8
  }
}

// class id: 5799, size: 0xc, field offset: 0x8
abstract class PriorityQueue<X0> extends Object {
}
