// lib: dartz, url: package:dartz/dartz.dart

// class id: 1048683, size: 0x8
class :: {

  static _ right(/* No info */) {
    // ** addr: 0x8c1d2c, size: 0x40
    // 0x8c1d2c: EnterFrame
    //     0x8c1d2c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c1d30: mov             fp, SP
    // 0x8c1d34: LoadField: r0 = r4->field_f
    //     0x8c1d34: ldur            w0, [x4, #0xf]
    // 0x8c1d38: cbnz            w0, #0x8c1d44
    // 0x8c1d3c: r1 = Null
    //     0x8c1d3c: mov             x1, NULL
    // 0x8c1d40: b               #0x8c1d50
    // 0x8c1d44: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x8c1d44: ldur            w0, [x4, #0x17]
    // 0x8c1d48: add             x1, fp, w0, sxtw #2
    // 0x8c1d4c: ldr             x1, [x1, #0x10]
    // 0x8c1d50: ldr             x0, [fp, #0x10]
    // 0x8c1d54: r0 = Right()
    //     0x8c1d54: bl              #0x8c1ae0  ; AllocateRightStub -> Right<X0, X1> (size=0x10)
    // 0x8c1d58: ldr             x1, [fp, #0x10]
    // 0x8c1d5c: StoreField: r0->field_b = r1
    //     0x8c1d5c: stur            w1, [x0, #0xb]
    // 0x8c1d60: LeaveFrame
    //     0x8c1d60: mov             SP, fp
    //     0x8c1d64: ldp             fp, lr, [SP], #0x10
    // 0x8c1d68: ret
    //     0x8c1d68: ret             
  }
  [closure] static Either<Y0, Y1> left<Y0, Y1>(dynamic, Y0) {
    // ** addr: 0x8c1d6c, size: 0x7c
    // 0x8c1d6c: EnterFrame
    //     0x8c1d6c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c1d70: mov             fp, SP
    // 0x8c1d74: AllocStack(0x10)
    //     0x8c1d74: sub             SP, SP, #0x10
    // 0x8c1d78: SetupParameters()
    //     0x8c1d78: ldur            w0, [x4, #0xf]
    //     0x8c1d7c: cbnz            w0, #0x8c1d88
    //     0x8c1d80: mov             x1, NULL
    //     0x8c1d84: b               #0x8c1d94
    //     0x8c1d88: ldur            w0, [x4, #0x17]
    //     0x8c1d8c: add             x1, fp, w0, sxtw #2
    //     0x8c1d90: ldr             x1, [x1, #0x10]
    //     0x8c1d94: ldr             x0, [fp, #0x18]
    //     0x8c1d98: ldur            w2, [x0, #0xf]
    //     0x8c1d9c: add             x2, x2, HEAP, lsl #32
    //     0x8c1da0: ldr             x16, [THR, #0x98]  ; THR::empty_type_arguments
    //     0x8c1da4: cmp             w2, w16
    //     0x8c1da8: b.ne            #0x8c1db4
    //     0x8c1dac: mov             x0, x1
    //     0x8c1db0: b               #0x8c1db8
    //     0x8c1db4: mov             x0, x2
    // 0x8c1db8: CheckStackOverflow
    //     0x8c1db8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c1dbc: cmp             SP, x16
    //     0x8c1dc0: b.ls            #0x8c1de0
    // 0x8c1dc4: ldr             x16, [fp, #0x10]
    // 0x8c1dc8: stp             x16, x0, [SP]
    // 0x8c1dcc: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x8c1dcc: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x8c1dd0: r0 = left()
    //     0x8c1dd0: bl              #0x8c1de8  ; [package:dartz/dartz.dart] ::left
    // 0x8c1dd4: LeaveFrame
    //     0x8c1dd4: mov             SP, fp
    //     0x8c1dd8: ldp             fp, lr, [SP], #0x10
    // 0x8c1ddc: ret
    //     0x8c1ddc: ret             
    // 0x8c1de0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c1de0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c1de4: b               #0x8c1dc4
  }
  static _ left(/* No info */) {
    // ** addr: 0x8c1de8, size: 0x40
    // 0x8c1de8: EnterFrame
    //     0x8c1de8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c1dec: mov             fp, SP
    // 0x8c1df0: LoadField: r0 = r4->field_f
    //     0x8c1df0: ldur            w0, [x4, #0xf]
    // 0x8c1df4: cbnz            w0, #0x8c1e00
    // 0x8c1df8: r1 = Null
    //     0x8c1df8: mov             x1, NULL
    // 0x8c1dfc: b               #0x8c1e0c
    // 0x8c1e00: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x8c1e00: ldur            w0, [x4, #0x17]
    // 0x8c1e04: add             x1, fp, w0, sxtw #2
    // 0x8c1e08: ldr             x1, [x1, #0x10]
    // 0x8c1e0c: ldr             x0, [fp, #0x10]
    // 0x8c1e10: r0 = Left()
    //     0x8c1e10: bl              #0x8c1e28  ; AllocateLeftStub -> Left<X0, X1> (size=0x10)
    // 0x8c1e14: ldr             x1, [fp, #0x10]
    // 0x8c1e18: StoreField: r0->field_b = r1
    //     0x8c1e18: stur            w1, [x0, #0xb]
    // 0x8c1e1c: LeaveFrame
    //     0x8c1e1c: mov             SP, fp
    //     0x8c1e20: ldp             fp, lr, [SP], #0x10
    // 0x8c1e24: ret
    //     0x8c1e24: ret             
  }
}

// class id: 5661, size: 0xc, field offset: 0x8
abstract class FoldableOps<X0, X1> extends Object {
}

// class id: 5662, size: 0xc, field offset: 0x8
abstract class MonadOps<X0, X1> extends Object
    implements ApplicativeOps<X0, X1> {
}

// class id: 5663, size: 0xc, field offset: 0x8
abstract class TraversableMonadOps<X0, X1> extends Object
    implements TraversableOps<X0, X1>, MonadOps<X0, X1> {
}

// class id: 5664, size: 0xc, field offset: 0x8
//   const constructor, 
abstract class Either<X0, X1> extends Object
    implements TraversableMonadOps<X0, X1> {

  Either<X0, Y0> map<Y0>(Either<X0, X1>, (dynamic, X1) => Y0) {
    // ** addr: 0x8c1aec, size: 0x98
    // 0x8c1aec: EnterFrame
    //     0x8c1aec: stp             fp, lr, [SP, #-0x10]!
    //     0x8c1af0: mov             fp, SP
    // 0x8c1af4: AllocStack(0x20)
    //     0x8c1af4: sub             SP, SP, #0x20
    // 0x8c1af8: SetupParameters()
    //     0x8c1af8: ldur            w0, [x4, #0xf]
    //     0x8c1afc: cbnz            w0, #0x8c1b08
    //     0x8c1b00: mov             x4, NULL
    //     0x8c1b04: b               #0x8c1b18
    //     0x8c1b08: ldur            w0, [x4, #0x17]
    //     0x8c1b0c: add             x1, fp, w0, sxtw #2
    //     0x8c1b10: ldr             x1, [x1, #0x10]
    //     0x8c1b14: mov             x4, x1
    //     0x8c1b18: ldr             x3, [fp, #0x18]
    //     0x8c1b1c: stur            x4, [fp, #-8]
    // 0x8c1b20: CheckStackOverflow
    //     0x8c1b20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c1b24: cmp             SP, x16
    //     0x8c1b28: b.ls            #0x8c1b7c
    // 0x8c1b2c: LoadField: r2 = r3->field_7
    //     0x8c1b2c: ldur            w2, [x3, #7]
    // 0x8c1b30: DecompressPointer r2
    //     0x8c1b30: add             x2, x2, HEAP, lsl #32
    // 0x8c1b34: ldr             x0, [fp, #0x10]
    // 0x8c1b38: mov             x1, x4
    // 0x8c1b3c: r8 = (dynamic this, X1) => Y0
    //     0x8c1b3c: add             x8, PP, #0x39, lsl #12  ; [pp+0x393b8] FunctionType: (dynamic this, X1) => Y0
    //     0x8c1b40: ldr             x8, [x8, #0x3b8]
    // 0x8c1b44: LoadField: r9 = r8->field_7
    //     0x8c1b44: ldur            x9, [x8, #7]
    // 0x8c1b48: r3 = Null
    //     0x8c1b48: add             x3, PP, #0x39, lsl #12  ; [pp+0x393c0] Null
    //     0x8c1b4c: ldr             x3, [x3, #0x3c0]
    // 0x8c1b50: blr             x9
    // 0x8c1b54: ldur            x16, [fp, #-8]
    // 0x8c1b58: ldr             lr, [fp, #0x18]
    // 0x8c1b5c: stp             lr, x16, [SP, #8]
    // 0x8c1b60: ldr             x16, [fp, #0x10]
    // 0x8c1b64: str             x16, [SP]
    // 0x8c1b68: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c1b68: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c1b6c: r0 = map()
    //     0x8c1b6c: bl              #0x8c1b84  ; [package:dartz/dartz.dart] Either::map
    // 0x8c1b70: LeaveFrame
    //     0x8c1b70: mov             SP, fp
    //     0x8c1b74: ldp             fp, lr, [SP], #0x10
    // 0x8c1b78: ret
    //     0x8c1b78: ret             
    // 0x8c1b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c1b7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c1b80: b               #0x8c1b2c
  }
  Either<X0, Y0> map<Y0>(Either<X0, X1>, (dynamic, X1) => Y0) {
    // ** addr: 0x8c1b84, size: 0x1a8
    // 0x8c1b84: EnterFrame
    //     0x8c1b84: stp             fp, lr, [SP, #-0x10]!
    //     0x8c1b88: mov             fp, SP
    // 0x8c1b8c: AllocStack(0x40)
    //     0x8c1b8c: sub             SP, SP, #0x40
    // 0x8c1b90: SetupParameters()
    //     0x8c1b90: ldur            w0, [x4, #0xf]
    //     0x8c1b94: cbnz            w0, #0x8c1ba0
    //     0x8c1b98: mov             x5, NULL
    //     0x8c1b9c: b               #0x8c1bb0
    //     0x8c1ba0: ldur            w0, [x4, #0x17]
    //     0x8c1ba4: add             x1, fp, w0, sxtw #2
    //     0x8c1ba8: ldr             x1, [x1, #0x10]
    //     0x8c1bac: mov             x5, x1
    //     0x8c1bb0: ldr             x4, [fp, #0x18]
    //     0x8c1bb4: add             x0, PP, #0x39, lsl #12  ; [pp+0x393d0] Closure: <Y0, Y1>(Y0) => Either<Y0, Y1> from Function 'left': static. (0x7e54fb2c1d6c)
    //     0x8c1bb8: ldr             x0, [x0, #0x3d0]
    //     0x8c1bbc: stur            x5, [fp, #-0x28]
    // 0x8c1bb4: r0 = Closure: <Y0, Y1>(Y0) => Either<Y0, Y1> from Function 'left': static.
    // 0x8c1bc0: CheckStackOverflow
    //     0x8c1bc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c1bc4: cmp             SP, x16
    //     0x8c1bc8: b.ls            #0x8c1d24
    // 0x8c1bcc: LoadField: r6 = r4->field_7
    //     0x8c1bcc: ldur            w6, [x4, #7]
    // 0x8c1bd0: DecompressPointer r6
    //     0x8c1bd0: add             x6, x6, HEAP, lsl #32
    // 0x8c1bd4: stur            x6, [fp, #-0x20]
    // 0x8c1bd8: LoadField: r7 = r0->field_13
    //     0x8c1bd8: ldur            w7, [x0, #0x13]
    // 0x8c1bdc: DecompressPointer r7
    //     0x8c1bdc: add             x7, x7, HEAP, lsl #32
    // 0x8c1be0: stur            x7, [fp, #-0x18]
    // 0x8c1be4: ArrayLoad: r8 = r0[0]  ; List_4
    //     0x8c1be4: ldur            w8, [x0, #0x17]
    // 0x8c1be8: DecompressPointer r8
    //     0x8c1be8: add             x8, x8, HEAP, lsl #32
    // 0x8c1bec: stur            x8, [fp, #-0x10]
    // 0x8c1bf0: LoadField: r9 = r0->field_7
    //     0x8c1bf0: ldur            w9, [x0, #7]
    // 0x8c1bf4: DecompressPointer r9
    //     0x8c1bf4: add             x9, x9, HEAP, lsl #32
    // 0x8c1bf8: mov             x2, x6
    // 0x8c1bfc: mov             x1, x5
    // 0x8c1c00: stur            x9, [fp, #-8]
    // 0x8c1c04: r3 = <X0, Y0>
    //     0x8c1c04: add             x3, PP, #0x39, lsl #12  ; [pp+0x393d8] TypeArguments: <X0, Y0>
    //     0x8c1c08: ldr             x3, [x3, #0x3d8]
    // 0x8c1c0c: r0 = Null
    //     0x8c1c0c: mov             x0, NULL
    // 0x8c1c10: cmp             x2, x0
    // 0x8c1c14: b.ne            #0x8c1c20
    // 0x8c1c18: cmp             x1, x0
    // 0x8c1c1c: b.eq            #0x8c1c2c
    // 0x8c1c20: r30 = InstantiateTypeArgumentsStub
    //     0x8c1c20: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8c1c24: LoadField: r30 = r30->field_7
    //     0x8c1c24: ldur            lr, [lr, #7]
    // 0x8c1c28: blr             lr
    // 0x8c1c2c: stur            x0, [fp, #-0x30]
    // 0x8c1c30: r16 = Closure: <Y0, Y1>(Y0) => Either<Y0, Y1> from Function 'left': static.
    //     0x8c1c30: add             x16, PP, #0x39, lsl #12  ; [pp+0x393d0] Closure: <Y0, Y1>(Y0) => Either<Y0, Y1> from Function 'left': static. (0x7e54fb2c1d6c)
    //     0x8c1c34: ldr             x16, [x16, #0x3d0]
    // 0x8c1c38: stp             x0, x16, [SP]
    // 0x8c1c3c: r0 = _boundsCheckForPartialInstantiation()
    //     0x8c1c3c: bl              #0x6022c8  ; [dart:_internal] ::_boundsCheckForPartialInstantiation
    // 0x8c1c40: ldur            x1, [fp, #-0x18]
    // 0x8c1c44: ldur            x2, [fp, #-0x10]
    // 0x8c1c48: ldur            x3, [fp, #-8]
    // 0x8c1c4c: r0 = AllocateClosureTA()
    //     0x8c1c4c: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x8c1c50: mov             x1, x0
    // 0x8c1c54: ldur            x0, [fp, #-0x30]
    // 0x8c1c58: StoreField: r1->field_f = r0
    //     0x8c1c58: stur            w0, [x1, #0xf]
    // 0x8c1c5c: r0 = Closure: <Y0, Y1>(Y0) => Either<Y0, Y1> from Function 'left': static.
    //     0x8c1c5c: add             x0, PP, #0x39, lsl #12  ; [pp+0x393d0] Closure: <Y0, Y1>(Y0) => Either<Y0, Y1> from Function 'left': static. (0x7e54fb2c1d6c)
    //     0x8c1c60: ldr             x0, [x0, #0x3d0]
    // 0x8c1c64: LoadField: r2 = r0->field_b
    //     0x8c1c64: ldur            w2, [x0, #0xb]
    // 0x8c1c68: DecompressPointer r2
    //     0x8c1c68: add             x2, x2, HEAP, lsl #32
    // 0x8c1c6c: StoreField: r1->field_b = r2
    //     0x8c1c6c: stur            w2, [x1, #0xb]
    // 0x8c1c70: ldr             x0, [fp, #0x18]
    // 0x8c1c74: r2 = LoadClassIdInstr(r0)
    //     0x8c1c74: ldur            x2, [x0, #-1]
    //     0x8c1c78: ubfx            x2, x2, #0xc, #0x14
    // 0x8c1c7c: r17 = 5665
    //     0x8c1c7c: movz            x17, #0x1621
    // 0x8c1c80: cmp             x2, x17
    // 0x8c1c84: b.ne            #0x8c1cfc
    // 0x8c1c88: LoadField: r4 = r0->field_b
    //     0x8c1c88: ldur            w4, [x0, #0xb]
    // 0x8c1c8c: DecompressPointer r4
    //     0x8c1c8c: add             x4, x4, HEAP, lsl #32
    // 0x8c1c90: ldur            x2, [fp, #-0x20]
    // 0x8c1c94: ldur            x1, [fp, #-0x28]
    // 0x8c1c98: stur            x4, [fp, #-8]
    // 0x8c1c9c: r3 = <X0, Y0>
    //     0x8c1c9c: add             x3, PP, #0x39, lsl #12  ; [pp+0x393d8] TypeArguments: <X0, Y0>
    //     0x8c1ca0: ldr             x3, [x3, #0x3d8]
    // 0x8c1ca4: r0 = Null
    //     0x8c1ca4: mov             x0, NULL
    // 0x8c1ca8: cmp             x2, x0
    // 0x8c1cac: b.ne            #0x8c1cb8
    // 0x8c1cb0: cmp             x1, x0
    // 0x8c1cb4: b.eq            #0x8c1cc4
    // 0x8c1cb8: r30 = InstantiateTypeArgumentsStub
    //     0x8c1cb8: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8c1cbc: LoadField: r30 = r30->field_7
    //     0x8c1cbc: ldur            lr, [lr, #7]
    // 0x8c1cc0: blr             lr
    // 0x8c1cc4: mov             x1, x0
    // 0x8c1cc8: stur            x1, [fp, #-0x10]
    // 0x8c1ccc: ldr             x16, [fp, #0x10]
    // 0x8c1cd0: ldur            lr, [fp, #-8]
    // 0x8c1cd4: stp             lr, x16, [SP]
    // 0x8c1cd8: ldr             x0, [fp, #0x10]
    // 0x8c1cdc: ClosureCall
    //     0x8c1cdc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x8c1ce0: ldur            x2, [x0, #0x1f]
    //     0x8c1ce4: blr             x2
    // 0x8c1ce8: ldur            x16, [fp, #-0x10]
    // 0x8c1cec: stp             x0, x16, [SP]
    // 0x8c1cf0: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x8c1cf0: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x8c1cf4: r0 = right()
    //     0x8c1cf4: bl              #0x8c1d2c  ; [package:dartz/dartz.dart] ::right
    // 0x8c1cf8: b               #0x8c1d18
    // 0x8c1cfc: LoadField: r2 = r0->field_b
    //     0x8c1cfc: ldur            w2, [x0, #0xb]
    // 0x8c1d00: DecompressPointer r2
    //     0x8c1d00: add             x2, x2, HEAP, lsl #32
    // 0x8c1d04: stp             x2, x1, [SP]
    // 0x8c1d08: mov             x0, x1
    // 0x8c1d0c: ClosureCall
    //     0x8c1d0c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x8c1d10: ldur            x2, [x0, #0x1f]
    //     0x8c1d14: blr             x2
    // 0x8c1d18: LeaveFrame
    //     0x8c1d18: mov             SP, fp
    //     0x8c1d1c: ldp             fp, lr, [SP], #0x10
    // 0x8c1d20: ret
    //     0x8c1d20: ret             
    // 0x8c1d24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c1d24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c1d28: b               #0x8c1bcc
  }
  _ toString(/* No info */) {
    // ** addr: 0xc1c1b8, size: 0xc0
    // 0xc1c1b8: EnterFrame
    //     0xc1c1b8: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c1bc: mov             fp, SP
    // 0xc1c1c0: AllocStack(0x10)
    //     0xc1c1c0: sub             SP, SP, #0x10
    // 0xc1c1c4: CheckStackOverflow
    //     0xc1c1c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1c1c8: cmp             SP, x16
    //     0xc1c1cc: b.ls            #0xc1c270
    // 0xc1c1d0: ldr             x0, [fp, #0x10]
    // 0xc1c1d4: r1 = LoadClassIdInstr(r0)
    //     0xc1c1d4: ldur            x1, [x0, #-1]
    //     0xc1c1d8: ubfx            x1, x1, #0xc, #0x14
    // 0xc1c1dc: r17 = 5665
    //     0xc1c1dc: movz            x17, #0x1621
    // 0xc1c1e0: cmp             x1, x17
    // 0xc1c1e4: b.ne            #0xc1c228
    // 0xc1c1e8: LoadField: r3 = r0->field_b
    //     0xc1c1e8: ldur            w3, [x0, #0xb]
    // 0xc1c1ec: DecompressPointer r3
    //     0xc1c1ec: add             x3, x3, HEAP, lsl #32
    // 0xc1c1f0: stur            x3, [fp, #-8]
    // 0xc1c1f4: r1 = Null
    //     0xc1c1f4: mov             x1, NULL
    // 0xc1c1f8: r2 = 6
    //     0xc1c1f8: movz            x2, #0x6
    // 0xc1c1fc: r0 = AllocateArray()
    //     0xc1c1fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1c200: r16 = "Right("
    //     0xc1c200: add             x16, PP, #0x39, lsl #12  ; [pp+0x393a8] "Right("
    //     0xc1c204: ldr             x16, [x16, #0x3a8]
    // 0xc1c208: StoreField: r0->field_f = r16
    //     0xc1c208: stur            w16, [x0, #0xf]
    // 0xc1c20c: ldur            x1, [fp, #-8]
    // 0xc1c210: StoreField: r0->field_13 = r1
    //     0xc1c210: stur            w1, [x0, #0x13]
    // 0xc1c214: r16 = ")"
    //     0xc1c214: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc1c218: ArrayStore: r0[0] = r16  ; List_4
    //     0xc1c218: stur            w16, [x0, #0x17]
    // 0xc1c21c: str             x0, [SP]
    // 0xc1c220: r0 = _interpolate()
    //     0xc1c220: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1c224: b               #0xc1c264
    // 0xc1c228: LoadField: r3 = r0->field_b
    //     0xc1c228: ldur            w3, [x0, #0xb]
    // 0xc1c22c: DecompressPointer r3
    //     0xc1c22c: add             x3, x3, HEAP, lsl #32
    // 0xc1c230: stur            x3, [fp, #-8]
    // 0xc1c234: r1 = Null
    //     0xc1c234: mov             x1, NULL
    // 0xc1c238: r2 = 6
    //     0xc1c238: movz            x2, #0x6
    // 0xc1c23c: r0 = AllocateArray()
    //     0xc1c23c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1c240: r16 = "Left("
    //     0xc1c240: add             x16, PP, #0x39, lsl #12  ; [pp+0x393b0] "Left("
    //     0xc1c244: ldr             x16, [x16, #0x3b0]
    // 0xc1c248: StoreField: r0->field_f = r16
    //     0xc1c248: stur            w16, [x0, #0xf]
    // 0xc1c24c: ldur            x1, [fp, #-8]
    // 0xc1c250: StoreField: r0->field_13 = r1
    //     0xc1c250: stur            w1, [x0, #0x13]
    // 0xc1c254: r16 = ")"
    //     0xc1c254: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc1c258: ArrayStore: r0[0] = r16  ; List_4
    //     0xc1c258: stur            w16, [x0, #0x17]
    // 0xc1c25c: str             x0, [SP]
    // 0xc1c260: r0 = _interpolate()
    //     0xc1c260: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1c264: LeaveFrame
    //     0xc1c264: mov             SP, fp
    //     0xc1c268: ldp             fp, lr, [SP], #0x10
    // 0xc1c26c: ret
    //     0xc1c26c: ret             
    // 0xc1c270: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1c270: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1c274: b               #0xc1c1d0
  }
}

// class id: 5665, size: 0x10, field offset: 0xc
//   const constructor, 
class Right<X0, X1> extends Either<X0, X1> {

  _ ==(/* No info */) {
    // ** addr: 0xd3ebe0, size: 0xa4
    // 0xd3ebe0: EnterFrame
    //     0xd3ebe0: stp             fp, lr, [SP, #-0x10]!
    //     0xd3ebe4: mov             fp, SP
    // 0xd3ebe8: AllocStack(0x10)
    //     0xd3ebe8: sub             SP, SP, #0x10
    // 0xd3ebec: CheckStackOverflow
    //     0xd3ebec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3ebf0: cmp             SP, x16
    //     0xd3ebf4: b.ls            #0xd3ec7c
    // 0xd3ebf8: ldr             x0, [fp, #0x10]
    // 0xd3ebfc: cmp             w0, NULL
    // 0xd3ec00: b.ne            #0xd3ec14
    // 0xd3ec04: r0 = false
    //     0xd3ec04: add             x0, NULL, #0x30  ; false
    // 0xd3ec08: LeaveFrame
    //     0xd3ec08: mov             SP, fp
    //     0xd3ec0c: ldp             fp, lr, [SP], #0x10
    // 0xd3ec10: ret
    //     0xd3ec10: ret             
    // 0xd3ec14: r1 = 60
    //     0xd3ec14: movz            x1, #0x3c
    // 0xd3ec18: branchIfSmi(r0, 0xd3ec24)
    //     0xd3ec18: tbz             w0, #0, #0xd3ec24
    // 0xd3ec1c: r1 = LoadClassIdInstr(r0)
    //     0xd3ec1c: ldur            x1, [x0, #-1]
    //     0xd3ec20: ubfx            x1, x1, #0xc, #0x14
    // 0xd3ec24: r17 = 5665
    //     0xd3ec24: movz            x17, #0x1621
    // 0xd3ec28: cmp             x1, x17
    // 0xd3ec2c: b.ne            #0xd3ec6c
    // 0xd3ec30: ldr             x1, [fp, #0x18]
    // 0xd3ec34: LoadField: r2 = r0->field_b
    //     0xd3ec34: ldur            w2, [x0, #0xb]
    // 0xd3ec38: DecompressPointer r2
    //     0xd3ec38: add             x2, x2, HEAP, lsl #32
    // 0xd3ec3c: LoadField: r0 = r1->field_b
    //     0xd3ec3c: ldur            w0, [x1, #0xb]
    // 0xd3ec40: DecompressPointer r0
    //     0xd3ec40: add             x0, x0, HEAP, lsl #32
    // 0xd3ec44: r1 = 60
    //     0xd3ec44: movz            x1, #0x3c
    // 0xd3ec48: branchIfSmi(r2, 0xd3ec54)
    //     0xd3ec48: tbz             w2, #0, #0xd3ec54
    // 0xd3ec4c: r1 = LoadClassIdInstr(r2)
    //     0xd3ec4c: ldur            x1, [x2, #-1]
    //     0xd3ec50: ubfx            x1, x1, #0xc, #0x14
    // 0xd3ec54: stp             x0, x2, [SP]
    // 0xd3ec58: mov             x0, x1
    // 0xd3ec5c: mov             lr, x0
    // 0xd3ec60: ldr             lr, [x21, lr, lsl #3]
    // 0xd3ec64: blr             lr
    // 0xd3ec68: b               #0xd3ec70
    // 0xd3ec6c: r0 = false
    //     0xd3ec6c: add             x0, NULL, #0x30  ; false
    // 0xd3ec70: LeaveFrame
    //     0xd3ec70: mov             SP, fp
    //     0xd3ec74: ldp             fp, lr, [SP], #0x10
    // 0xd3ec78: ret
    //     0xd3ec78: ret             
    // 0xd3ec7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3ec7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd3ec80: b               #0xd3ebf8
  }
}

// class id: 5666, size: 0x10, field offset: 0xc
//   const constructor, 
class Left<X0, X1> extends Either<X0, X1> {

  LocationError field_c;

  _ ==(/* No info */) {
    // ** addr: 0xd3eb3c, size: 0xa4
    // 0xd3eb3c: EnterFrame
    //     0xd3eb3c: stp             fp, lr, [SP, #-0x10]!
    //     0xd3eb40: mov             fp, SP
    // 0xd3eb44: AllocStack(0x10)
    //     0xd3eb44: sub             SP, SP, #0x10
    // 0xd3eb48: CheckStackOverflow
    //     0xd3eb48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3eb4c: cmp             SP, x16
    //     0xd3eb50: b.ls            #0xd3ebd8
    // 0xd3eb54: ldr             x0, [fp, #0x10]
    // 0xd3eb58: cmp             w0, NULL
    // 0xd3eb5c: b.ne            #0xd3eb70
    // 0xd3eb60: r0 = false
    //     0xd3eb60: add             x0, NULL, #0x30  ; false
    // 0xd3eb64: LeaveFrame
    //     0xd3eb64: mov             SP, fp
    //     0xd3eb68: ldp             fp, lr, [SP], #0x10
    // 0xd3eb6c: ret
    //     0xd3eb6c: ret             
    // 0xd3eb70: r1 = 60
    //     0xd3eb70: movz            x1, #0x3c
    // 0xd3eb74: branchIfSmi(r0, 0xd3eb80)
    //     0xd3eb74: tbz             w0, #0, #0xd3eb80
    // 0xd3eb78: r1 = LoadClassIdInstr(r0)
    //     0xd3eb78: ldur            x1, [x0, #-1]
    //     0xd3eb7c: ubfx            x1, x1, #0xc, #0x14
    // 0xd3eb80: r17 = 5666
    //     0xd3eb80: movz            x17, #0x1622
    // 0xd3eb84: cmp             x1, x17
    // 0xd3eb88: b.ne            #0xd3ebc8
    // 0xd3eb8c: ldr             x1, [fp, #0x18]
    // 0xd3eb90: LoadField: r2 = r0->field_b
    //     0xd3eb90: ldur            w2, [x0, #0xb]
    // 0xd3eb94: DecompressPointer r2
    //     0xd3eb94: add             x2, x2, HEAP, lsl #32
    // 0xd3eb98: LoadField: r0 = r1->field_b
    //     0xd3eb98: ldur            w0, [x1, #0xb]
    // 0xd3eb9c: DecompressPointer r0
    //     0xd3eb9c: add             x0, x0, HEAP, lsl #32
    // 0xd3eba0: r1 = 60
    //     0xd3eba0: movz            x1, #0x3c
    // 0xd3eba4: branchIfSmi(r2, 0xd3ebb0)
    //     0xd3eba4: tbz             w2, #0, #0xd3ebb0
    // 0xd3eba8: r1 = LoadClassIdInstr(r2)
    //     0xd3eba8: ldur            x1, [x2, #-1]
    //     0xd3ebac: ubfx            x1, x1, #0xc, #0x14
    // 0xd3ebb0: stp             x0, x2, [SP]
    // 0xd3ebb4: mov             x0, x1
    // 0xd3ebb8: mov             lr, x0
    // 0xd3ebbc: ldr             lr, [x21, lr, lsl #3]
    // 0xd3ebc0: blr             lr
    // 0xd3ebc4: b               #0xd3ebcc
    // 0xd3ebc8: r0 = false
    //     0xd3ebc8: add             x0, NULL, #0x30  ; false
    // 0xd3ebcc: LeaveFrame
    //     0xd3ebcc: mov             SP, fp
    //     0xd3ebd0: ldp             fp, lr, [SP], #0x10
    // 0xd3ebd4: ret
    //     0xd3ebd4: ret             
    // 0xd3ebd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3ebd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd3ebdc: b               #0xd3eb54
  }
}

// class id: 5667, size: 0xc, field offset: 0x8
abstract class FunctorOps<X0, X1> extends Object {
}

// class id: 5668, size: 0xc, field offset: 0xc
//   transformed mixin,
abstract class _TraversableOps&FunctorOps&FoldableOps<X0, X1> extends FunctorOps<X0, X1>
     with FoldableOps<X0, X1> {
}

// class id: 5669, size: 0xc, field offset: 0xc
abstract class TraversableOps<X0, X1> extends _TraversableOps&FunctorOps&FoldableOps<X0, X1> {
}

// class id: 5670, size: 0xc, field offset: 0x8
abstract class ApplicativeOps<X0, X1> extends Object
    implements FunctorOps<X0, X1> {
}
