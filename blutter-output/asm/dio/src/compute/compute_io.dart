// lib: , url: package:dio/src/compute/compute_io.dart

// class id: 1048688, size: 0x8
class :: {

  [closure] static Future<Y1> compute<Y0, Y1>(dynamic, (dynamic, Y0) => FutureOr<Y1>, Y0, {String? debugLabel}) {
    // ** addr: 0x7034e4, size: 0xc8
    // 0x7034e4: EnterFrame
    //     0x7034e4: stp             fp, lr, [SP, #-0x10]!
    //     0x7034e8: mov             fp, SP
    // 0x7034ec: AllocStack(0x20)
    //     0x7034ec: sub             SP, SP, #0x20
    // 0x7034f0: SetupParameters(dynamic _ /* r2 */, dynamic _ /* r3 */, dynamic _ /* r5 */, {dynamic debugLabel = Null /* r0 */})
    //     0x7034f0: ldur            w0, [x4, #0x13]
    //     0x7034f4: sub             x1, x0, #6
    //     0x7034f8: add             x2, fp, w1, sxtw #2
    //     0x7034fc: ldr             x2, [x2, #0x20]
    //     0x703500: add             x3, fp, w1, sxtw #2
    //     0x703504: ldr             x3, [x3, #0x18]
    //     0x703508: add             x5, fp, w1, sxtw #2
    //     0x70350c: ldr             x5, [x5, #0x10]
    //     0x703510: ldur            w1, [x4, #0x1f]
    //     0x703514: add             x1, x1, HEAP, lsl #32
    //     0x703518: ldr             x16, [PP, #0x2590]  ; [pp+0x2590] "debugLabel"
    //     0x70351c: cmp             w1, w16
    //     0x703520: b.ne            #0x70353c
    //     0x703524: ldur            w1, [x4, #0x23]
    //     0x703528: add             x1, x1, HEAP, lsl #32
    //     0x70352c: sub             w6, w0, w1
    //     0x703530: add             x0, fp, w6, sxtw #2
    //     0x703534: ldr             x0, [x0, #8]
    //     0x703538: b               #0x703540
    //     0x70353c: mov             x0, NULL
    //     0x703540: ldur            w1, [x4, #0xf]
    //     0x703544: cbnz            w1, #0x703550
    //     0x703548: mov             x1, NULL
    //     0x70354c: b               #0x703560
    //     0x703550: ldur            w1, [x4, #0x17]
    //     0x703554: add             x4, fp, w1, sxtw #2
    //     0x703558: ldr             x4, [x4, #0x10]
    //     0x70355c: mov             x1, x4
    //     0x703560: ldur            w4, [x2, #0xf]
    //     0x703564: add             x4, x4, HEAP, lsl #32
    //     0x703568: ldr             x16, [THR, #0x98]  ; THR::empty_type_arguments
    //     0x70356c: cmp             w4, w16
    //     0x703570: b.eq            #0x703578
    //     0x703574: mov             x1, x4
    // 0x703578: CheckStackOverflow
    //     0x703578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x70357c: cmp             SP, x16
    //     0x703580: b.ls            #0x7035a4
    // 0x703584: stp             x3, x1, [SP, #0x10]
    // 0x703588: stp             x0, x5, [SP]
    // 0x70358c: r4 = const [0x2, 0x3, 0x3, 0x2, debugLabel, 0x2, null]
    //     0x70358c: add             x4, PP, #0x10, lsl #12  ; [pp+0x10678] List(7) [0x2, 0x3, 0x3, 0x2, "debugLabel", 0x2, Null]
    //     0x703590: ldr             x4, [x4, #0x678]
    // 0x703594: r0 = compute()
    //     0x703594: bl              #0x7035ac  ; [package:dio/src/compute/compute_io.dart] ::compute
    // 0x703598: LeaveFrame
    //     0x703598: mov             SP, fp
    //     0x70359c: ldp             fp, lr, [SP], #0x10
    // 0x7035a0: ret
    //     0x7035a0: ret             
    // 0x7035a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7035a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7035a8: b               #0x703584
  }
  static Future<Y1> compute<Y0, Y1>((dynamic, Y0) => FutureOr<Y1>, Y0, {String? debugLabel}) async {
    // ** addr: 0x7035ac, size: 0x7fc
    // 0x7035ac: EnterFrame
    //     0x7035ac: stp             fp, lr, [SP, #-0x10]!
    //     0x7035b0: mov             fp, SP
    // 0x7035b4: AllocStack(0x120)
    //     0x7035b4: sub             SP, SP, #0x120
    // 0x7035b8: SetupParameters(dynamic _ /* r2, fp-0xc0 */, dynamic _ /* r3, fp-0xb8 */, {dynamic debugLabel = Null /* r0, fp-0xb0 */})
    //     0x7035b8: stur            NULL, [fp, #-8]
    //     0x7035bc: stur            x4, [fp, #-0xc8]
    //     0x7035c0: ldur            w0, [x4, #0x13]
    //     0x7035c4: sub             x1, x0, #4
    //     0x7035c8: add             x2, fp, w1, sxtw #2
    //     0x7035cc: ldr             x2, [x2, #0x18]
    //     0x7035d0: stur            x2, [fp, #-0xc0]
    //     0x7035d4: add             x3, fp, w1, sxtw #2
    //     0x7035d8: ldr             x3, [x3, #0x10]
    //     0x7035dc: stur            x3, [fp, #-0xb8]
    //     0x7035e0: ldur            w1, [x4, #0x1f]
    //     0x7035e4: add             x1, x1, HEAP, lsl #32
    //     0x7035e8: ldr             x16, [PP, #0x2590]  ; [pp+0x2590] "debugLabel"
    //     0x7035ec: cmp             w1, w16
    //     0x7035f0: b.ne            #0x70360c
    //     0x7035f4: ldur            w1, [x4, #0x23]
    //     0x7035f8: add             x1, x1, HEAP, lsl #32
    //     0x7035fc: sub             w5, w0, w1
    //     0x703600: add             x0, fp, w5, sxtw #2
    //     0x703604: ldr             x0, [x0, #8]
    //     0x703608: b               #0x703610
    //     0x70360c: mov             x0, NULL
    //     0x703610: stur            x0, [fp, #-0xb0]
    //     0x703614: ldur            w1, [x4, #0xf]
    //     0x703618: cbnz            w1, #0x703624
    //     0x70361c: mov             x1, NULL
    //     0x703620: b               #0x703634
    //     0x703624: ldur            w1, [x4, #0x17]
    //     0x703628: add             x5, fp, w1, sxtw #2
    //     0x70362c: ldr             x5, [x5, #0x10]
    //     0x703630: mov             x1, x5
    //     0x703634: stur            x1, [fp, #-0xa8]
    // 0x703638: CheckStackOverflow
    //     0x703638: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x70363c: cmp             SP, x16
    //     0x703640: b.ls            #0x703da0
    // 0x703644: r1 = 5
    //     0x703644: movz            x1, #0x5
    // 0x703648: r0 = AllocateContext()
    //     0x703648: bl              #0xec126c  ; AllocateContextStub
    // 0x70364c: mov             x4, x0
    // 0x703650: ldur            x0, [fp, #-0xb0]
    // 0x703654: stur            x4, [fp, #-0xd0]
    // 0x703658: StoreField: r4->field_f = r0
    //     0x703658: stur            w0, [x4, #0xf]
    // 0x70365c: ldur            x1, [fp, #-0xa8]
    // 0x703660: r2 = Null
    //     0x703660: mov             x2, NULL
    // 0x703664: r3 = <Y1>
    //     0x703664: add             x3, PP, #0x10, lsl #12  ; [pp+0x10680] TypeArguments: <Y1>
    //     0x703668: ldr             x3, [x3, #0x680]
    // 0x70366c: r0 = Null
    //     0x70366c: mov             x0, NULL
    // 0x703670: cmp             x2, x0
    // 0x703674: b.ne            #0x703680
    // 0x703678: cmp             x1, x0
    // 0x70367c: b.eq            #0x70368c
    // 0x703680: r30 = InstantiateTypeArgumentsStub
    //     0x703680: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x703684: LoadField: r30 = r30->field_7
    //     0x703684: ldur            lr, [lr, #7]
    // 0x703688: blr             lr
    // 0x70368c: mov             x1, x0
    // 0x703690: stur            x1, [fp, #-0xb0]
    // 0x703694: r0 = InitAsync()
    //     0x703694: bl              #0x661298  ; InitAsyncStub
    // 0x703698: ldur            x2, [fp, #-0xd0]
    // 0x70369c: LoadField: r0 = r2->field_f
    //     0x70369c: ldur            w0, [x2, #0xf]
    // 0x7036a0: DecompressPointer r0
    //     0x7036a0: add             x0, x0, HEAP, lsl #32
    // 0x7036a4: cmp             w0, NULL
    // 0x7036a8: b.ne            #0x7036b8
    // 0x7036ac: r0 = "compute"
    //     0x7036ac: add             x0, PP, #0x10, lsl #12  ; [pp+0x10688] "compute"
    //     0x7036b0: ldr             x0, [x0, #0x688]
    // 0x7036b4: StoreField: r2->field_f = r0
    //     0x7036b4: stur            w0, [x2, #0xf]
    // 0x7036b8: ldur            x0, [fp, #-0xa8]
    // 0x7036bc: r0 = begin()
    //     0x7036bc: bl              #0x704380  ; [dart:developer] Flow::begin
    // 0x7036c0: mov             x4, x0
    // 0x7036c4: ldur            x3, [fp, #-0xd0]
    // 0x7036c8: stur            x4, [fp, #-0xc8]
    // 0x7036cc: StoreField: r3->field_13 = r0
    //     0x7036cc: stur            w0, [x3, #0x13]
    //     0x7036d0: ldurb           w16, [x3, #-1]
    //     0x7036d4: ldurb           w17, [x0, #-1]
    //     0x7036d8: and             x16, x17, x16, lsr #2
    //     0x7036dc: tst             x16, HEAP, lsr #32
    //     0x7036e0: b.eq            #0x7036e8
    //     0x7036e4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7036e8: LoadField: r0 = r3->field_f
    //     0x7036e8: ldur            w0, [x3, #0xf]
    // 0x7036ec: DecompressPointer r0
    //     0x7036ec: add             x0, x0, HEAP, lsl #32
    // 0x7036f0: stur            x0, [fp, #-0xb0]
    // 0x7036f4: r1 = Null
    //     0x7036f4: mov             x1, NULL
    // 0x7036f8: r2 = 4
    //     0x7036f8: movz            x2, #0x4
    // 0x7036fc: r0 = AllocateArray()
    //     0x7036fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x703700: mov             x1, x0
    // 0x703704: ldur            x0, [fp, #-0xb0]
    // 0x703708: StoreField: r1->field_f = r0
    //     0x703708: stur            w0, [x1, #0xf]
    // 0x70370c: r16 = ": start"
    //     0x70370c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10690] ": start"
    //     0x703710: ldr             x16, [x16, #0x690]
    // 0x703714: StoreField: r1->field_13 = r16
    //     0x703714: stur            w16, [x1, #0x13]
    // 0x703718: str             x1, [SP]
    // 0x70371c: r0 = _interpolate()
    //     0x70371c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x703720: mov             x1, x0
    // 0x703724: ldur            x2, [fp, #-0xc8]
    // 0x703728: r0 = startSync()
    //     0x703728: bl              #0x7040b8  ; [dart:developer] Timeline::startSync
    // 0x70372c: r1 = Null
    //     0x70372c: mov             x1, NULL
    // 0x703730: r2 = ""
    //     0x703730: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0x703734: r0 = _RawReceivePort()
    //     0x703734: bl              #0x65f848  ; [dart:isolate] _RawReceivePort::_RawReceivePort
    // 0x703738: mov             x1, x0
    // 0x70373c: stur            x1, [fp, #-0xb0]
    // 0x703740: StoreField: r1->field_f = rNULL
    //     0x703740: stur            NULL, [x1, #0xf]
    // 0x703744: mov             x0, x1
    // 0x703748: ldur            x2, [fp, #-0xd0]
    // 0x70374c: ArrayStore: r2[0] = r0  ; List_4
    //     0x70374c: stur            w0, [x2, #0x17]
    //     0x703750: ldurb           w16, [x2, #-1]
    //     0x703754: ldurb           w17, [x0, #-1]
    //     0x703758: and             x16, x17, x16, lsr #2
    //     0x70375c: tst             x16, HEAP, lsr #32
    //     0x703760: b.eq            #0x703768
    //     0x703764: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x703768: r0 = finishSync()
    //     0x703768: bl              #0x703df0  ; [dart:developer] Timeline::finishSync
    // 0x70376c: ldur            x2, [fp, #-0xd0]
    // 0x703770: r1 = Function 'timeEndAndCleanup': static.
    //     0x703770: add             x1, PP, #0x10, lsl #12  ; [pp+0x10698] AnonymousClosure: static (0x7048ec), in [package:dio/src/compute/compute_io.dart] ::compute (0x7035ac)
    //     0x703774: ldr             x1, [x1, #0x698]
    // 0x703778: r0 = AllocateClosure()
    //     0x703778: bl              #0xec1630  ; AllocateClosureStub
    // 0x70377c: ldur            x2, [fp, #-0xa8]
    // 0x703780: StoreField: r0->field_b = r2
    //     0x703780: stur            w2, [x0, #0xb]
    // 0x703784: ldur            x3, [fp, #-0xd0]
    // 0x703788: StoreField: r3->field_1b = r0
    //     0x703788: stur            w0, [x3, #0x1b]
    //     0x70378c: ldurb           w16, [x3, #-1]
    //     0x703790: ldurb           w17, [x0, #-1]
    //     0x703794: and             x16, x17, x16, lsr #2
    //     0x703798: tst             x16, HEAP, lsr #32
    //     0x70379c: b.eq            #0x7037a4
    //     0x7037a0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7037a4: r1 = Null
    //     0x7037a4: mov             x1, NULL
    // 0x7037a8: r0 = _Future()
    //     0x7037a8: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x7037ac: stur            x0, [fp, #-0xd8]
    // 0x7037b0: StoreField: r0->field_b = rZR
    //     0x7037b0: stur            xzr, [x0, #0xb]
    // 0x7037b4: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x7037b4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7037b8: ldr             x0, [x0, #0x7a0]
    //     0x7037bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7037c0: cmp             w0, w16
    //     0x7037c4: b.ne            #0x7037d0
    //     0x7037c8: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x7037cc: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x7037d0: mov             x1, x0
    // 0x7037d4: ldur            x0, [fp, #-0xd8]
    // 0x7037d8: StoreField: r0->field_13 = r1
    //     0x7037d8: stur            w1, [x0, #0x13]
    // 0x7037dc: r1 = Null
    //     0x7037dc: mov             x1, NULL
    // 0x7037e0: r0 = _AsyncCompleter()
    //     0x7037e0: bl              #0x661210  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x7037e4: ldur            x3, [fp, #-0xd8]
    // 0x7037e8: StoreField: r0->field_b = r3
    //     0x7037e8: stur            w3, [x0, #0xb]
    // 0x7037ec: ldur            x4, [fp, #-0xd0]
    // 0x7037f0: StoreField: r4->field_1f = r0
    //     0x7037f0: stur            w0, [x4, #0x1f]
    //     0x7037f4: ldurb           w16, [x4, #-1]
    //     0x7037f8: ldurb           w17, [x0, #-1]
    //     0x7037fc: and             x16, x17, x16, lsr #2
    //     0x703800: tst             x16, HEAP, lsr #32
    //     0x703804: b.eq            #0x70380c
    //     0x703808: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x70380c: mov             x2, x4
    // 0x703810: r1 = Function '<anonymous closure>': static.
    //     0x703810: add             x1, PP, #0x10, lsl #12  ; [pp+0x106a0] AnonymousClosure: static (0x704804), in [package:dio/src/compute/compute_io.dart] ::compute (0x7035ac)
    //     0x703814: ldr             x1, [x1, #0x6a0]
    // 0x703818: r0 = AllocateClosure()
    //     0x703818: bl              #0xec1630  ; AllocateClosureStub
    // 0x70381c: ldur            x4, [fp, #-0xa8]
    // 0x703820: StoreField: r0->field_b = r4
    //     0x703820: stur            w4, [x0, #0xb]
    // 0x703824: ldur            x5, [fp, #-0xb0]
    // 0x703828: StoreField: r5->field_f = r0
    //     0x703828: stur            w0, [x5, #0xf]
    //     0x70382c: ldurb           w16, [x5, #-1]
    //     0x703830: ldurb           w17, [x0, #-1]
    //     0x703834: and             x16, x17, x16, lsr #2
    //     0x703838: tst             x16, HEAP, lsr #32
    //     0x70383c: b.eq            #0x703844
    //     0x703840: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x703844: ldur            x7, [fp, #-0xc0]
    // 0x703848: ldur            x8, [fp, #-0xb8]
    // 0x70384c: ldur            x0, [fp, #-0xd0]
    // 0x703850: ldur            x6, [fp, #-0xc8]
    // 0x703854: r9 = Closure: <Y0, Y1>(_IsolateConfiguration<Y0, Y1>) => Future<void> from Function '_spawn@872363018': static.
    //     0x703854: add             x9, PP, #0x10, lsl #12  ; [pp+0x106a8] Closure: <Y0, Y1>(_IsolateConfiguration<Y0, Y1>) => Future<void> from Function '_spawn@872363018': static. (0x7e54fb1043d4)
    //     0x703858: ldr             x9, [x9, #0x6a8]
    // 0x70385c: LoadField: r10 = r9->field_13
    //     0x70385c: ldur            w10, [x9, #0x13]
    // 0x703860: DecompressPointer r10
    //     0x703860: add             x10, x10, HEAP, lsl #32
    // 0x703864: stur            x10, [fp, #-0xf0]
    // 0x703868: ArrayLoad: r11 = r9[0]  ; List_4
    //     0x703868: ldur            w11, [x9, #0x17]
    // 0x70386c: DecompressPointer r11
    //     0x70386c: add             x11, x11, HEAP, lsl #32
    // 0x703870: stur            x11, [fp, #-0xe8]
    // 0x703874: LoadField: r12 = r9->field_7
    //     0x703874: ldur            w12, [x9, #7]
    // 0x703878: DecompressPointer r12
    //     0x703878: add             x12, x12, HEAP, lsl #32
    // 0x70387c: mov             x1, x10
    // 0x703880: mov             x2, x11
    // 0x703884: mov             x3, x12
    // 0x703888: stur            x12, [fp, #-0xe0]
    // 0x70388c: r0 = AllocateClosureTA()
    //     0x70388c: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x703890: stur            x0, [fp, #-0xe0]
    // 0x703894: r16 = Closure: <Y0, Y1>(_IsolateConfiguration<Y0, Y1>) => Future<void> from Function '_spawn@872363018': static.
    //     0x703894: add             x16, PP, #0x10, lsl #12  ; [pp+0x106a8] Closure: <Y0, Y1>(_IsolateConfiguration<Y0, Y1>) => Future<void> from Function '_spawn@872363018': static. (0x7e54fb1043d4)
    //     0x703898: ldr             x16, [x16, #0x6a8]
    // 0x70389c: ldur            lr, [fp, #-0xa8]
    // 0x7038a0: stp             lr, x16, [SP]
    // 0x7038a4: r0 = _boundsCheckForPartialInstantiation()
    //     0x7038a4: bl              #0x6022c8  ; [dart:_internal] ::_boundsCheckForPartialInstantiation
    // 0x7038a8: ldur            x0, [fp, #-0xa8]
    // 0x7038ac: ldur            x2, [fp, #-0xe0]
    // 0x7038b0: StoreField: r2->field_f = r0
    //     0x7038b0: stur            w0, [x2, #0xf]
    //     0x7038b4: ldurb           w16, [x2, #-1]
    //     0x7038b8: ldurb           w17, [x0, #-1]
    //     0x7038bc: and             x16, x17, x16, lsr #2
    //     0x7038c0: tst             x16, HEAP, lsr #32
    //     0x7038c4: b.eq            #0x7038cc
    //     0x7038c8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7038cc: r0 = Closure: <Y0, Y1>(_IsolateConfiguration<Y0, Y1>) => Future<void> from Function '_spawn@872363018': static.
    //     0x7038cc: add             x0, PP, #0x10, lsl #12  ; [pp+0x106a8] Closure: <Y0, Y1>(_IsolateConfiguration<Y0, Y1>) => Future<void> from Function '_spawn@872363018': static. (0x7e54fb1043d4)
    //     0x7038d0: ldr             x0, [x0, #0x6a8]
    // 0x7038d4: LoadField: r1 = r0->field_b
    //     0x7038d4: ldur            w1, [x0, #0xb]
    // 0x7038d8: DecompressPointer r1
    //     0x7038d8: add             x1, x1, HEAP, lsl #32
    // 0x7038dc: mov             x0, x1
    // 0x7038e0: StoreField: r2->field_b = r0
    //     0x7038e0: stur            w0, [x2, #0xb]
    //     0x7038e4: ldurb           w16, [x2, #-1]
    //     0x7038e8: ldurb           w17, [x0, #-1]
    //     0x7038ec: and             x16, x17, x16, lsr #2
    //     0x7038f0: tst             x16, HEAP, lsr #32
    //     0x7038f4: b.eq            #0x7038fc
    //     0x7038f8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7038fc: ldur            x1, [fp, #-0xa8]
    // 0x703900: r0 = _IsolateConfiguration()
    //     0x703900: bl              #0x703de4  ; Allocate_IsolateConfigurationStub -> _IsolateConfiguration<X0, X1> (size=0x24)
    // 0x703904: mov             x4, x0
    // 0x703908: ldur            x0, [fp, #-0xb0]
    // 0x70390c: stur            x4, [fp, #-0xf0]
    // 0x703910: LoadField: r5 = r0->field_7
    //     0x703910: ldur            w5, [x0, #7]
    // 0x703914: DecompressPointer r5
    //     0x703914: add             x5, x5, HEAP, lsl #32
    // 0x703918: ldur            x0, [fp, #-0xd0]
    // 0x70391c: stur            x5, [fp, #-0xe8]
    // 0x703920: LoadField: r6 = r0->field_f
    //     0x703920: ldur            w6, [x0, #0xf]
    // 0x703924: DecompressPointer r6
    //     0x703924: add             x6, x6, HEAP, lsl #32
    // 0x703928: ldur            x1, [fp, #-0xc8]
    // 0x70392c: stur            x6, [fp, #-0xb0]
    // 0x703930: LoadField: r2 = r1->field_f
    //     0x703930: ldur            x2, [x1, #0xf]
    // 0x703934: ldur            x1, [fp, #-0xc0]
    // 0x703938: StoreField: r4->field_b = r1
    //     0x703938: stur            w1, [x4, #0xb]
    // 0x70393c: ldur            x1, [fp, #-0xb8]
    // 0x703940: StoreField: r4->field_f = r1
    //     0x703940: stur            w1, [x4, #0xf]
    // 0x703944: StoreField: r4->field_13 = r5
    //     0x703944: stur            w5, [x4, #0x13]
    // 0x703948: ArrayStore: r4[0] = r6  ; List_4
    //     0x703948: stur            w6, [x4, #0x17]
    // 0x70394c: StoreField: r4->field_1b = r2
    //     0x70394c: stur            x2, [x4, #0x1b]
    // 0x703950: ldur            x1, [fp, #-0xa8]
    // 0x703954: r2 = Null
    //     0x703954: mov             x2, NULL
    // 0x703958: r3 = <_IsolateConfiguration<Y0, Y1>>
    //     0x703958: add             x3, PP, #0x10, lsl #12  ; [pp+0x106b0] TypeArguments: <_IsolateConfiguration<Y0, Y1>>
    //     0x70395c: ldr             x3, [x3, #0x6b0]
    // 0x703960: r30 = InstantiateTypeArgumentsStub
    //     0x703960: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x703964: LoadField: r30 = r30->field_7
    //     0x703964: ldur            lr, [lr, #7]
    // 0x703968: blr             lr
    // 0x70396c: ldur            x16, [fp, #-0xe0]
    // 0x703970: stp             x16, x0, [SP, #0x20]
    // 0x703974: ldur            x16, [fp, #-0xf0]
    // 0x703978: ldur            lr, [fp, #-0xb0]
    // 0x70397c: stp             lr, x16, [SP, #0x10]
    // 0x703980: ldur            x16, [fp, #-0xe8]
    // 0x703984: ldur            lr, [fp, #-0xe8]
    // 0x703988: stp             lr, x16, [SP]
    // 0x70398c: r4 = const [0x1, 0x5, 0x5, 0x5, null]
    //     0x70398c: ldr             x4, [PP, #0x1800]  ; [pp+0x1800] List(5) [0x1, 0x5, 0x5, 0x5, Null]
    // 0x703990: r0 = spawn()
    //     0x703990: bl              #0x697054  ; [dart:isolate] Isolate::spawn
    // 0x703994: mov             x1, x0
    // 0x703998: stur            x1, [fp, #-0xb0]
    // 0x70399c: r0 = Await()
    //     0x70399c: bl              #0x661044  ; AwaitStub
    // 0x7039a0: ldur            x0, [fp, #-0xd8]
    // 0x7039a4: r0 = Await()
    //     0x7039a4: bl              #0x661044  ; AwaitStub
    // 0x7039a8: mov             x3, x0
    // 0x7039ac: stur            x3, [fp, #-0xb0]
    // 0x7039b0: cmp             w3, NULL
    // 0x7039b4: b.eq            #0x703b98
    // 0x7039b8: mov             x0, x3
    // 0x7039bc: r2 = Null
    //     0x7039bc: mov             x2, NULL
    // 0x7039c0: r1 = Null
    //     0x7039c0: mov             x1, NULL
    // 0x7039c4: r4 = 60
    //     0x7039c4: movz            x4, #0x3c
    // 0x7039c8: branchIfSmi(r0, 0x7039d4)
    //     0x7039c8: tbz             w0, #0, #0x7039d4
    // 0x7039cc: r4 = LoadClassIdInstr(r0)
    //     0x7039cc: ldur            x4, [x0, #-1]
    //     0x7039d0: ubfx            x4, x4, #0xc, #0x14
    // 0x7039d4: sub             x4, x4, #0x5a
    // 0x7039d8: cmp             x4, #2
    // 0x7039dc: b.ls            #0x7039f0
    // 0x7039e0: r8 = List
    //     0x7039e0: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0x7039e4: r3 = Null
    //     0x7039e4: add             x3, PP, #0x10, lsl #12  ; [pp+0x106b8] Null
    //     0x7039e8: ldr             x3, [x3, #0x6b8]
    // 0x7039ec: r0 = List()
    //     0x7039ec: bl              #0xed6b40  ; IsType_List_Stub
    // 0x7039f0: ldur            x1, [fp, #-0xb0]
    // 0x7039f4: r0 = LoadClassIdInstr(r1)
    //     0x7039f4: ldur            x0, [x1, #-1]
    //     0x7039f8: ubfx            x0, x0, #0xc, #0x14
    // 0x7039fc: str             x1, [SP]
    // 0x703a00: r0 = GDT[cid_x0 + 0xc834]()
    //     0x703a00: movz            x17, #0xc834
    //     0x703a04: add             lr, x0, x17
    //     0x703a08: ldr             lr, [x21, lr, lsl #3]
    //     0x703a0c: blr             lr
    // 0x703a10: r1 = LoadInt32Instr(r0)
    //     0x703a10: sbfx            x1, x0, #1, #0x1f
    //     0x703a14: tbz             w0, #0, #0x703a1c
    //     0x703a18: ldur            x1, [x0, #7]
    // 0x703a1c: cmp             x1, #2
    // 0x703a20: b.gt            #0x703aa0
    // 0x703a24: cmp             x1, #1
    // 0x703a28: b.gt            #0x703bd0
    // 0x703a2c: cmp             w0, #2
    // 0x703a30: b.ne            #0x703a98
    // 0x703a34: ldur            x1, [fp, #-0xb0]
    // 0x703a38: r0 = LoadClassIdInstr(r1)
    //     0x703a38: ldur            x0, [x1, #-1]
    //     0x703a3c: ubfx            x0, x0, #0xc, #0x14
    // 0x703a40: stp             xzr, x1, [SP]
    // 0x703a44: r0 = GDT[cid_x0 + 0x13037]()
    //     0x703a44: movz            x17, #0x3037
    //     0x703a48: movk            x17, #0x1, lsl #16
    //     0x703a4c: add             lr, x0, x17
    //     0x703a50: ldr             lr, [x21, lr, lsl #3]
    //     0x703a54: blr             lr
    // 0x703a58: ldur            x1, [fp, #-0xa8]
    // 0x703a5c: mov             x3, x0
    // 0x703a60: r2 = Null
    //     0x703a60: mov             x2, NULL
    // 0x703a64: stur            x3, [fp, #-0xa8]
    // 0x703a68: cmp             w1, NULL
    // 0x703a6c: b.eq            #0x703a90
    // 0x703a70: LoadField: r4 = r1->field_1b
    //     0x703a70: ldur            w4, [x1, #0x1b]
    // 0x703a74: DecompressPointer r4
    //     0x703a74: add             x4, x4, HEAP, lsl #32
    // 0x703a78: r8 = Y1
    //     0x703a78: add             x8, PP, #0x10, lsl #12  ; [pp+0x106c8] TypeParameter: Y1
    //     0x703a7c: ldr             x8, [x8, #0x6c8]
    // 0x703a80: LoadField: r9 = r4->field_7
    //     0x703a80: ldur            x9, [x4, #7]
    // 0x703a84: r3 = Null
    //     0x703a84: add             x3, PP, #0x10, lsl #12  ; [pp+0x106d0] Null
    //     0x703a88: ldr             x3, [x3, #0x6d0]
    // 0x703a8c: blr             x9
    // 0x703a90: ldur            x0, [fp, #-0xa8]
    // 0x703a94: r0 = ReturnAsync()
    //     0x703a94: b               #0x6576a4  ; ReturnAsyncStub
    // 0x703a98: ldur            x1, [fp, #-0xb0]
    // 0x703a9c: b               #0x703aa4
    // 0x703aa0: ldur            x1, [fp, #-0xb0]
    // 0x703aa4: r0 = LoadClassIdInstr(r1)
    //     0x703aa4: ldur            x0, [x1, #-1]
    //     0x703aa8: ubfx            x0, x0, #0xc, #0x14
    // 0x703aac: stp             xzr, x1, [SP]
    // 0x703ab0: r0 = GDT[cid_x0 + 0x13037]()
    //     0x703ab0: movz            x17, #0x3037
    //     0x703ab4: movk            x17, #0x1, lsl #16
    //     0x703ab8: add             lr, x0, x17
    //     0x703abc: ldr             lr, [x21, lr, lsl #3]
    //     0x703ac0: blr             lr
    // 0x703ac4: mov             x3, x0
    // 0x703ac8: stur            x3, [fp, #-0xa8]
    // 0x703acc: cmp             w3, NULL
    // 0x703ad0: b.ne            #0x703af8
    // 0x703ad4: mov             x0, x3
    // 0x703ad8: r2 = Null
    //     0x703ad8: mov             x2, NULL
    // 0x703adc: r1 = Null
    //     0x703adc: mov             x1, NULL
    // 0x703ae0: cmp             w0, NULL
    // 0x703ae4: b.ne            #0x703af8
    // 0x703ae8: r8 = Object
    //     0x703ae8: ldr             x8, [PP, #0x2998]  ; [pp+0x2998] Type: Object
    // 0x703aec: r3 = Null
    //     0x703aec: add             x3, PP, #0x10, lsl #12  ; [pp+0x106e0] Null
    //     0x703af0: ldr             x3, [x3, #0x6e0]
    // 0x703af4: r0 = Object()
    //     0x703af4: bl              #0xed6558  ; IsType_Object_Stub
    // 0x703af8: ldur            x1, [fp, #-0xb0]
    // 0x703afc: r0 = LoadClassIdInstr(r1)
    //     0x703afc: ldur            x0, [x1, #-1]
    //     0x703b00: ubfx            x0, x0, #0xc, #0x14
    // 0x703b04: r16 = 2
    //     0x703b04: movz            x16, #0x2
    // 0x703b08: stp             x16, x1, [SP]
    // 0x703b0c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x703b0c: movz            x17, #0x3037
    //     0x703b10: movk            x17, #0x1, lsl #16
    //     0x703b14: add             lr, x0, x17
    //     0x703b18: ldr             lr, [x21, lr, lsl #3]
    //     0x703b1c: blr             lr
    // 0x703b20: mov             x3, x0
    // 0x703b24: r2 = Null
    //     0x703b24: mov             x2, NULL
    // 0x703b28: r1 = Null
    //     0x703b28: mov             x1, NULL
    // 0x703b2c: stur            x3, [fp, #-0xb8]
    // 0x703b30: r4 = 60
    //     0x703b30: movz            x4, #0x3c
    // 0x703b34: branchIfSmi(r0, 0x703b40)
    //     0x703b34: tbz             w0, #0, #0x703b40
    // 0x703b38: r4 = LoadClassIdInstr(r0)
    //     0x703b38: ldur            x4, [x0, #-1]
    //     0x703b3c: ubfx            x4, x4, #0xc, #0x14
    // 0x703b40: cmp             x4, #0x4d
    // 0x703b44: b.eq            #0x703b6c
    // 0x703b48: cmp             x4, #0x1c6
    // 0x703b4c: b.eq            #0x703b6c
    // 0x703b50: r17 = 6711
    //     0x703b50: movz            x17, #0x1a37
    // 0x703b54: cmp             x4, x17
    // 0x703b58: b.eq            #0x703b6c
    // 0x703b5c: r8 = StackTrace
    //     0x703b5c: ldr             x8, [PP, #0x1110]  ; [pp+0x1110] Type: StackTrace
    // 0x703b60: r3 = Null
    //     0x703b60: add             x3, PP, #0x10, lsl #12  ; [pp+0x106f0] Null
    //     0x703b64: ldr             x3, [x3, #0x6f0]
    // 0x703b68: r0 = DefaultTypeTest()
    //     0x703b68: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x703b6c: ldur            x16, [fp, #-0xb8]
    // 0x703b70: str             x16, [SP]
    // 0x703b74: ldur            x2, [fp, #-0xa8]
    // 0x703b78: r1 = <Never>
    //     0x703b78: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x703b7c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x703b7c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x703b80: r0 = Future.error()
    //     0x703b80: bl              #0x67873c  ; [dart:async] Future::Future.error
    // 0x703b84: mov             x1, x0
    // 0x703b88: stur            x1, [fp, #-0xa8]
    // 0x703b8c: r0 = Await()
    //     0x703b8c: bl              #0x661044  ; AwaitStub
    // 0x703b90: r0 = Null
    //     0x703b90: mov             x0, NULL
    // 0x703b94: r0 = ReturnAsyncNotFuture()
    //     0x703b94: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x703b98: r0 = RemoteError()
    //     0x703b98: bl              #0x697f48  ; AllocateRemoteErrorStub -> RemoteError (size=0x10)
    // 0x703b9c: mov             x1, x0
    // 0x703ba0: r0 = "Isolate exited without result or error."
    //     0x703ba0: add             x0, PP, #0x10, lsl #12  ; [pp+0x10700] "Isolate exited without result or error."
    //     0x703ba4: ldr             x0, [x0, #0x700]
    // 0x703ba8: stur            x1, [fp, #-0xa8]
    // 0x703bac: StoreField: r1->field_7 = r0
    //     0x703bac: stur            w0, [x1, #7]
    // 0x703bb0: r0 = _StringStackTrace()
    //     0x703bb0: bl              #0x697f3c  ; Allocate_StringStackTraceStub -> _StringStackTrace (size=0xc)
    // 0x703bb4: mov             x1, x0
    // 0x703bb8: r0 = ""
    //     0x703bb8: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x703bbc: StoreField: r1->field_7 = r0
    //     0x703bbc: stur            w0, [x1, #7]
    // 0x703bc0: ldur            x0, [fp, #-0xa8]
    // 0x703bc4: StoreField: r0->field_b = r1
    //     0x703bc4: stur            w1, [x0, #0xb]
    // 0x703bc8: r0 = Throw()
    //     0x703bc8: bl              #0xec04b8  ; ThrowStub
    // 0x703bcc: brk             #0
    // 0x703bd0: ldur            x1, [fp, #-0xb0]
    // 0x703bd4: r0 = LoadClassIdInstr(r1)
    //     0x703bd4: ldur            x0, [x1, #-1]
    //     0x703bd8: ubfx            x0, x0, #0xc, #0x14
    // 0x703bdc: stp             xzr, x1, [SP]
    // 0x703be0: r0 = GDT[cid_x0 + 0x13037]()
    //     0x703be0: movz            x17, #0x3037
    //     0x703be4: movk            x17, #0x1, lsl #16
    //     0x703be8: add             lr, x0, x17
    //     0x703bec: ldr             lr, [x21, lr, lsl #3]
    //     0x703bf0: blr             lr
    // 0x703bf4: mov             x3, x0
    // 0x703bf8: r2 = Null
    //     0x703bf8: mov             x2, NULL
    // 0x703bfc: r1 = Null
    //     0x703bfc: mov             x1, NULL
    // 0x703c00: stur            x3, [fp, #-0xa8]
    // 0x703c04: r4 = 60
    //     0x703c04: movz            x4, #0x3c
    // 0x703c08: branchIfSmi(r0, 0x703c14)
    //     0x703c08: tbz             w0, #0, #0x703c14
    // 0x703c0c: r4 = LoadClassIdInstr(r0)
    //     0x703c0c: ldur            x4, [x0, #-1]
    //     0x703c10: ubfx            x4, x4, #0xc, #0x14
    // 0x703c14: sub             x4, x4, #0x5e
    // 0x703c18: cmp             x4, #1
    // 0x703c1c: b.ls            #0x703c30
    // 0x703c20: r8 = String
    //     0x703c20: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x703c24: r3 = Null
    //     0x703c24: add             x3, PP, #0x10, lsl #12  ; [pp+0x10708] Null
    //     0x703c28: ldr             x3, [x3, #0x708]
    // 0x703c2c: r0 = String()
    //     0x703c2c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x703c30: ldur            x0, [fp, #-0xb0]
    // 0x703c34: r1 = LoadClassIdInstr(r0)
    //     0x703c34: ldur            x1, [x0, #-1]
    //     0x703c38: ubfx            x1, x1, #0xc, #0x14
    // 0x703c3c: r16 = 2
    //     0x703c3c: movz            x16, #0x2
    // 0x703c40: stp             x16, x0, [SP]
    // 0x703c44: mov             x0, x1
    // 0x703c48: r0 = GDT[cid_x0 + 0x13037]()
    //     0x703c48: movz            x17, #0x3037
    //     0x703c4c: movk            x17, #0x1, lsl #16
    //     0x703c50: add             lr, x0, x17
    //     0x703c54: ldr             lr, [x21, lr, lsl #3]
    //     0x703c58: blr             lr
    // 0x703c5c: mov             x3, x0
    // 0x703c60: r2 = Null
    //     0x703c60: mov             x2, NULL
    // 0x703c64: r1 = Null
    //     0x703c64: mov             x1, NULL
    // 0x703c68: stur            x3, [fp, #-0xb0]
    // 0x703c6c: r4 = 60
    //     0x703c6c: movz            x4, #0x3c
    // 0x703c70: branchIfSmi(r0, 0x703c7c)
    //     0x703c70: tbz             w0, #0, #0x703c7c
    // 0x703c74: r4 = LoadClassIdInstr(r0)
    //     0x703c74: ldur            x4, [x0, #-1]
    //     0x703c78: ubfx            x4, x4, #0xc, #0x14
    // 0x703c7c: sub             x4, x4, #0x5e
    // 0x703c80: cmp             x4, #1
    // 0x703c84: b.ls            #0x703c98
    // 0x703c88: r8 = String
    //     0x703c88: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x703c8c: r3 = Null
    //     0x703c8c: add             x3, PP, #0x10, lsl #12  ; [pp+0x10718] Null
    //     0x703c90: ldr             x3, [x3, #0x718]
    // 0x703c94: r0 = String()
    //     0x703c94: bl              #0xed43b0  ; IsType_String_Stub
    // 0x703c98: r0 = RemoteError()
    //     0x703c98: bl              #0x697f48  ; AllocateRemoteErrorStub -> RemoteError (size=0x10)
    // 0x703c9c: mov             x1, x0
    // 0x703ca0: ldur            x0, [fp, #-0xa8]
    // 0x703ca4: stur            x1, [fp, #-0xb8]
    // 0x703ca8: StoreField: r1->field_7 = r0
    //     0x703ca8: stur            w0, [x1, #7]
    // 0x703cac: r0 = _StringStackTrace()
    //     0x703cac: bl              #0x697f3c  ; Allocate_StringStackTraceStub -> _StringStackTrace (size=0xc)
    // 0x703cb0: mov             x1, x0
    // 0x703cb4: ldur            x0, [fp, #-0xb0]
    // 0x703cb8: StoreField: r1->field_7 = r0
    //     0x703cb8: stur            w0, [x1, #7]
    // 0x703cbc: ldur            x2, [fp, #-0xb8]
    // 0x703cc0: StoreField: r2->field_b = r1
    //     0x703cc0: stur            w1, [x2, #0xb]
    // 0x703cc4: r1 = <Never>
    //     0x703cc4: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x703cc8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x703cc8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x703ccc: r0 = Future.error()
    //     0x703ccc: bl              #0x67873c  ; [dart:async] Future::Future.error
    // 0x703cd0: mov             x1, x0
    // 0x703cd4: stur            x1, [fp, #-0xa8]
    // 0x703cd8: r0 = Await()
    //     0x703cd8: bl              #0x661044  ; AwaitStub
    // 0x703cdc: r0 = "Unreachable code."
    //     0x703cdc: add             x0, PP, #0x10, lsl #12  ; [pp+0x10728] "Unreachable code."
    //     0x703ce0: ldr             x0, [x0, #0x728]
    // 0x703ce4: r0 = Throw()
    //     0x703ce4: bl              #0xec04b8  ; ThrowStub
    // 0x703ce8: brk             #0
    // 0x703cec: sub             SP, fp, #0x120
    // 0x703cf0: mov             x3, x0
    // 0x703cf4: stur            x0, [fp, #-0xb8]
    // 0x703cf8: mov             x0, x1
    // 0x703cfc: stur            x1, [fp, #-0xc0]
    // 0x703d00: ldur            x1, [fp, #-0x40]
    // 0x703d04: LoadField: r2 = r1->field_1b
    //     0x703d04: ldur            w2, [x1, #0x1b]
    // 0x703d08: DecompressPointer r2
    //     0x703d08: add             x2, x2, HEAP, lsl #32
    // 0x703d0c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x703d0c: ldur            w4, [x2, #0x17]
    // 0x703d10: DecompressPointer r4
    //     0x703d10: add             x4, x4, HEAP, lsl #32
    // 0x703d14: stur            x4, [fp, #-0xb0]
    // 0x703d18: LoadField: r5 = r4->field_f
    //     0x703d18: ldur            w5, [x4, #0xf]
    // 0x703d1c: DecompressPointer r5
    //     0x703d1c: add             x5, x5, HEAP, lsl #32
    // 0x703d20: stur            x5, [fp, #-0xa8]
    // 0x703d24: r1 = Null
    //     0x703d24: mov             x1, NULL
    // 0x703d28: r2 = 4
    //     0x703d28: movz            x2, #0x4
    // 0x703d2c: r0 = AllocateArray()
    //     0x703d2c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x703d30: mov             x1, x0
    // 0x703d34: ldur            x0, [fp, #-0xa8]
    // 0x703d38: StoreField: r1->field_f = r0
    //     0x703d38: stur            w0, [x1, #0xf]
    // 0x703d3c: r16 = ": end"
    //     0x703d3c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10730] ": end"
    //     0x703d40: ldr             x16, [x16, #0x730]
    // 0x703d44: StoreField: r1->field_13 = r16
    //     0x703d44: stur            w16, [x1, #0x13]
    // 0x703d48: str             x1, [SP]
    // 0x703d4c: r0 = _interpolate()
    //     0x703d4c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x703d50: mov             x2, x0
    // 0x703d54: ldur            x0, [fp, #-0xb0]
    // 0x703d58: stur            x2, [fp, #-0xa8]
    // 0x703d5c: LoadField: r1 = r0->field_13
    //     0x703d5c: ldur            w1, [x0, #0x13]
    // 0x703d60: DecompressPointer r1
    //     0x703d60: add             x1, x1, HEAP, lsl #32
    // 0x703d64: LoadField: r3 = r1->field_f
    //     0x703d64: ldur            x3, [x1, #0xf]
    // 0x703d68: mov             x1, x3
    // 0x703d6c: r0 = end()
    //     0x703d6c: bl              #0x703da8  ; [dart:developer] Flow::end
    // 0x703d70: ldur            x1, [fp, #-0xa8]
    // 0x703d74: mov             x2, x0
    // 0x703d78: r0 = startSync()
    //     0x703d78: bl              #0x7040b8  ; [dart:developer] Timeline::startSync
    // 0x703d7c: ldur            x0, [fp, #-0xb0]
    // 0x703d80: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x703d80: ldur            w1, [x0, #0x17]
    // 0x703d84: DecompressPointer r1
    //     0x703d84: add             x1, x1, HEAP, lsl #32
    // 0x703d88: r0 = close()
    //     0x703d88: bl              #0x65f548  ; [dart:isolate] _RawReceivePort::close
    // 0x703d8c: r0 = finishSync()
    //     0x703d8c: bl              #0x703df0  ; [dart:developer] Timeline::finishSync
    // 0x703d90: ldur            x0, [fp, #-0xb8]
    // 0x703d94: ldur            x1, [fp, #-0xc0]
    // 0x703d98: r0 = ReThrow()
    //     0x703d98: bl              #0xec048c  ; ReThrowStub
    // 0x703d9c: brk             #0
    // 0x703da0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x703da0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x703da4: b               #0x703644
  }
  [closure] static Future<void> _spawn<Y0, Y1>(dynamic, _IsolateConfiguration<Y0, Y1>) {
    // ** addr: 0x7043d4, size: 0x7c
    // 0x7043d4: EnterFrame
    //     0x7043d4: stp             fp, lr, [SP, #-0x10]!
    //     0x7043d8: mov             fp, SP
    // 0x7043dc: AllocStack(0x10)
    //     0x7043dc: sub             SP, SP, #0x10
    // 0x7043e0: SetupParameters()
    //     0x7043e0: ldur            w0, [x4, #0xf]
    //     0x7043e4: cbnz            w0, #0x7043f0
    //     0x7043e8: mov             x1, NULL
    //     0x7043ec: b               #0x7043fc
    //     0x7043f0: ldur            w0, [x4, #0x17]
    //     0x7043f4: add             x1, fp, w0, sxtw #2
    //     0x7043f8: ldr             x1, [x1, #0x10]
    //     0x7043fc: ldr             x0, [fp, #0x18]
    //     0x704400: ldur            w2, [x0, #0xf]
    //     0x704404: add             x2, x2, HEAP, lsl #32
    //     0x704408: ldr             x16, [THR, #0x98]  ; THR::empty_type_arguments
    //     0x70440c: cmp             w2, w16
    //     0x704410: b.ne            #0x70441c
    //     0x704414: mov             x0, x1
    //     0x704418: b               #0x704420
    //     0x70441c: mov             x0, x2
    // 0x704420: CheckStackOverflow
    //     0x704420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x704424: cmp             SP, x16
    //     0x704428: b.ls            #0x704448
    // 0x70442c: ldr             x16, [fp, #0x10]
    // 0x704430: stp             x16, x0, [SP]
    // 0x704434: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x704434: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x704438: r0 = _spawn()
    //     0x704438: bl              #0x704450  ; [package:dio/src/compute/compute_io.dart] ::_spawn
    // 0x70443c: LeaveFrame
    //     0x70443c: mov             SP, fp
    //     0x704440: ldp             fp, lr, [SP], #0x10
    // 0x704444: ret
    //     0x704444: ret             
    // 0x704448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x704448: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x70444c: b               #0x70442c
  }
  static Future<void> _spawn<Y0, Y1>(_IsolateConfiguration<Y0, Y1>) async {
    // ** addr: 0x704450, size: 0x118
    // 0x704450: EnterFrame
    //     0x704450: stp             fp, lr, [SP, #-0x10]!
    //     0x704454: mov             fp, SP
    // 0x704458: AllocStack(0x80)
    //     0x704458: sub             SP, SP, #0x80
    // 0x70445c: SetupParameters(dynamic _ /* r1, fp-0x68 */)
    //     0x70445c: stur            NULL, [fp, #-8]
    //     0x704460: movz            x0, #0
    //     0x704464: stur            x4, [fp, #-0x70]
    //     0x704468: add             x1, fp, w0, sxtw #2
    //     0x70446c: ldr             x1, [x1, #0x10]
    //     0x704470: stur            x1, [fp, #-0x68]
    //     0x704474: ldur            w0, [x4, #0xf]
    //     0x704478: cbnz            w0, #0x704484
    //     0x70447c: mov             x2, NULL
    //     0x704480: b               #0x704490
    //     0x704484: ldur            w0, [x4, #0x17]
    //     0x704488: add             x2, fp, w0, sxtw #2
    //     0x70448c: ldr             x2, [x2, #0x10]
    //     0x704490: stur            x2, [fp, #-0x60]
    // 0x704494: CheckStackOverflow
    //     0x704494: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x704498: cmp             SP, x16
    //     0x70449c: b.ls            #0x704560
    // 0x7044a0: InitAsync() -> Future<void?>
    //     0x7044a0: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7044a4: bl              #0x661298  ; InitAsyncStub
    // 0x7044a8: ldur            x1, [fp, #-0x68]
    // 0x7044ac: r0 = applyAndTime()
    //     0x7044ac: bl              #0x704638  ; [package:dio/src/compute/compute_io.dart] _IsolateConfiguration::applyAndTime
    // 0x7044b0: ldur            x1, [fp, #-0x60]
    // 0x7044b4: r2 = Null
    //     0x7044b4: mov             x2, NULL
    // 0x7044b8: r3 = <Y1>
    //     0x7044b8: add             x3, PP, #0x10, lsl #12  ; [pp+0x10738] TypeArguments: <Y1>
    //     0x7044bc: ldr             x3, [x3, #0x738]
    // 0x7044c0: stur            x0, [fp, #-0x70]
    // 0x7044c4: r0 = Null
    //     0x7044c4: mov             x0, NULL
    // 0x7044c8: cmp             x2, x0
    // 0x7044cc: b.ne            #0x7044d8
    // 0x7044d0: cmp             x1, x0
    // 0x7044d4: b.eq            #0x7044e4
    // 0x7044d8: r30 = InstantiateTypeArgumentsStub
    //     0x7044d8: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7044dc: LoadField: r30 = r30->field_7
    //     0x7044dc: ldur            lr, [lr, #7]
    // 0x7044e0: blr             lr
    // 0x7044e4: mov             x1, x0
    // 0x7044e8: mov             x2, x0
    // 0x7044ec: ldur            x0, [fp, #-0x70]
    // 0x7044f0: stur            x2, [fp, #-0x60]
    // 0x7044f4: r0 = AwaitWithTypeCheck()
    //     0x7044f4: bl              #0x6576d0  ; AwaitWithTypeCheckStub
    // 0x7044f8: stp             x0, NULL, [SP]
    // 0x7044fc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x7044fc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x704500: r0 = _buildSuccessResponse()
    //     0x704500: bl              #0x704568  ; [package:dio/src/compute/compute_io.dart] ::_buildSuccessResponse
    // 0x704504: mov             x2, x0
    // 0x704508: ldur            x0, [fp, #-0x68]
    // 0x70450c: b               #0x70454c
    // 0x704510: sub             SP, fp, #0x80
    // 0x704514: mov             x3, x0
    // 0x704518: stur            x0, [fp, #-0x60]
    // 0x70451c: mov             x0, x1
    // 0x704520: stur            x1, [fp, #-0x68]
    // 0x704524: r1 = Null
    //     0x704524: mov             x1, NULL
    // 0x704528: r2 = 6
    //     0x704528: movz            x2, #0x6
    // 0x70452c: r0 = AllocateArray()
    //     0x70452c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x704530: mov             x1, x0
    // 0x704534: ldur            x0, [fp, #-0x60]
    // 0x704538: StoreField: r1->field_f = r0
    //     0x704538: stur            w0, [x1, #0xf]
    // 0x70453c: ldur            x0, [fp, #-0x68]
    // 0x704540: StoreField: r1->field_13 = r0
    //     0x704540: stur            w0, [x1, #0x13]
    // 0x704544: ldur            x0, [fp, #-0x10]
    // 0x704548: mov             x2, x1
    // 0x70454c: LoadField: r1 = r0->field_13
    //     0x70454c: ldur            w1, [x0, #0x13]
    // 0x704550: DecompressPointer r1
    //     0x704550: add             x1, x1, HEAP, lsl #32
    // 0x704554: r0 = exit()
    //     0x704554: bl              #0x697abc  ; [dart:isolate] Isolate::exit
    // 0x704558: r0 = Null
    //     0x704558: mov             x0, NULL
    // 0x70455c: r0 = ReturnAsyncNotFuture()
    //     0x70455c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x704560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x704560: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x704564: b               #0x7044a0
  }
  static _ _buildSuccessResponse(/* No info */) {
    // ** addr: 0x704568, size: 0xd0
    // 0x704568: EnterFrame
    //     0x704568: stp             fp, lr, [SP, #-0x10]!
    //     0x70456c: mov             fp, SP
    // 0x704570: AllocStack(0x10)
    //     0x704570: sub             SP, SP, #0x10
    // 0x704574: SetupParameters()
    //     0x704574: ldur            w0, [x4, #0xf]
    //     0x704578: cbnz            w0, #0x704584
    //     0x70457c: mov             x3, NULL
    //     0x704580: b               #0x704594
    //     0x704584: ldur            w0, [x4, #0x17]
    //     0x704588: add             x1, fp, w0, sxtw #2
    //     0x70458c: ldr             x1, [x1, #0x10]
    //     0x704590: mov             x3, x1
    //     0x704594: ldr             x0, [fp, #0x10]
    // 0x704598: mov             x1, x3
    // 0x70459c: stur            x3, [fp, #-8]
    // 0x7045a0: r2 = 2
    //     0x7045a0: movz            x2, #0x2
    // 0x7045a4: r0 = AllocateArray()
    //     0x7045a4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7045a8: mov             x4, x0
    // 0x7045ac: ldr             x3, [fp, #0x10]
    // 0x7045b0: stur            x4, [fp, #-0x10]
    // 0x7045b4: cmp             w3, NULL
    // 0x7045b8: b.eq            #0x704620
    // 0x7045bc: mov             x0, x3
    // 0x7045c0: ldur            x2, [fp, #-8]
    // 0x7045c4: r1 = Null
    //     0x7045c4: mov             x1, NULL
    // 0x7045c8: cmp             w2, NULL
    // 0x7045cc: b.eq            #0x7045ec
    // 0x7045d0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7045d0: ldur            w4, [x2, #0x17]
    // 0x7045d4: DecompressPointer r4
    //     0x7045d4: add             x4, x4, HEAP, lsl #32
    // 0x7045d8: r8 = X0
    //     0x7045d8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7045dc: LoadField: r9 = r4->field_7
    //     0x7045dc: ldur            x9, [x4, #7]
    // 0x7045e0: r3 = Null
    //     0x7045e0: add             x3, PP, #0x10, lsl #12  ; [pp+0x10740] Null
    //     0x7045e4: ldr             x3, [x3, #0x740]
    // 0x7045e8: blr             x9
    // 0x7045ec: ldr             x1, [fp, #0x10]
    // 0x7045f0: ldur            x0, [fp, #-0x10]
    // 0x7045f4: r2 = 0
    //     0x7045f4: movz            x2, #0
    // 0x7045f8: CheckStackOverflow
    //     0x7045f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7045fc: cmp             SP, x16
    //     0x704600: b.ls            #0x704630
    // 0x704604: cmp             x2, #1
    // 0x704608: b.ge            #0x704624
    // 0x70460c: ArrayStore: r0[r2] = r1  ; Unknown_4
    //     0x70460c: add             x3, x0, x2, lsl #2
    //     0x704610: stur            w1, [x3, #0xf]
    // 0x704614: add             x3, x2, #1
    // 0x704618: mov             x2, x3
    // 0x70461c: b               #0x7045f8
    // 0x704620: mov             x0, x4
    // 0x704624: LeaveFrame
    //     0x704624: mov             SP, fp
    //     0x704628: ldp             fp, lr, [SP], #0x10
    // 0x70462c: ret
    //     0x70462c: ret             
    // 0x704630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x704630: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x704634: b               #0x704604
  }
  [closure] static Null <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x704804, size: 0xe8
    // 0x704804: EnterFrame
    //     0x704804: stp             fp, lr, [SP, #-0x10]!
    //     0x704808: mov             fp, SP
    // 0x70480c: AllocStack(0x20)
    //     0x70480c: sub             SP, SP, #0x20
    // 0x704810: SetupParameters()
    //     0x704810: ldr             x0, [fp, #0x18]
    //     0x704814: ldur            w3, [x0, #0x17]
    //     0x704818: add             x3, x3, HEAP, lsl #32
    //     0x70481c: stur            x3, [fp, #-0x18]
    // 0x704820: CheckStackOverflow
    //     0x704820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x704824: cmp             SP, x16
    //     0x704828: b.ls            #0x7048e4
    // 0x70482c: LoadField: r0 = r3->field_1b
    //     0x70482c: ldur            w0, [x3, #0x1b]
    // 0x704830: DecompressPointer r0
    //     0x704830: add             x0, x0, HEAP, lsl #32
    // 0x704834: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x704834: ldur            w4, [x0, #0x17]
    // 0x704838: DecompressPointer r4
    //     0x704838: add             x4, x4, HEAP, lsl #32
    // 0x70483c: stur            x4, [fp, #-0x10]
    // 0x704840: LoadField: r0 = r4->field_f
    //     0x704840: ldur            w0, [x4, #0xf]
    // 0x704844: DecompressPointer r0
    //     0x704844: add             x0, x0, HEAP, lsl #32
    // 0x704848: stur            x0, [fp, #-8]
    // 0x70484c: r1 = Null
    //     0x70484c: mov             x1, NULL
    // 0x704850: r2 = 4
    //     0x704850: movz            x2, #0x4
    // 0x704854: r0 = AllocateArray()
    //     0x704854: bl              #0xec22fc  ; AllocateArrayStub
    // 0x704858: mov             x1, x0
    // 0x70485c: ldur            x0, [fp, #-8]
    // 0x704860: StoreField: r1->field_f = r0
    //     0x704860: stur            w0, [x1, #0xf]
    // 0x704864: r16 = ": end"
    //     0x704864: add             x16, PP, #0x10, lsl #12  ; [pp+0x10730] ": end"
    //     0x704868: ldr             x16, [x16, #0x730]
    // 0x70486c: StoreField: r1->field_13 = r16
    //     0x70486c: stur            w16, [x1, #0x13]
    // 0x704870: str             x1, [SP]
    // 0x704874: r0 = _interpolate()
    //     0x704874: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x704878: mov             x2, x0
    // 0x70487c: ldur            x0, [fp, #-0x10]
    // 0x704880: stur            x2, [fp, #-8]
    // 0x704884: LoadField: r1 = r0->field_13
    //     0x704884: ldur            w1, [x0, #0x13]
    // 0x704888: DecompressPointer r1
    //     0x704888: add             x1, x1, HEAP, lsl #32
    // 0x70488c: LoadField: r3 = r1->field_f
    //     0x70488c: ldur            x3, [x1, #0xf]
    // 0x704890: mov             x1, x3
    // 0x704894: r0 = end()
    //     0x704894: bl              #0x703da8  ; [dart:developer] Flow::end
    // 0x704898: ldur            x1, [fp, #-8]
    // 0x70489c: mov             x2, x0
    // 0x7048a0: r0 = startSync()
    //     0x7048a0: bl              #0x7040b8  ; [dart:developer] Timeline::startSync
    // 0x7048a4: ldur            x0, [fp, #-0x10]
    // 0x7048a8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7048a8: ldur            w1, [x0, #0x17]
    // 0x7048ac: DecompressPointer r1
    //     0x7048ac: add             x1, x1, HEAP, lsl #32
    // 0x7048b0: r0 = close()
    //     0x7048b0: bl              #0x65f548  ; [dart:isolate] _RawReceivePort::close
    // 0x7048b4: r0 = finishSync()
    //     0x7048b4: bl              #0x703df0  ; [dart:developer] Timeline::finishSync
    // 0x7048b8: ldur            x0, [fp, #-0x18]
    // 0x7048bc: LoadField: r1 = r0->field_1f
    //     0x7048bc: ldur            w1, [x0, #0x1f]
    // 0x7048c0: DecompressPointer r1
    //     0x7048c0: add             x1, x1, HEAP, lsl #32
    // 0x7048c4: ldr             x16, [fp, #0x10]
    // 0x7048c8: str             x16, [SP]
    // 0x7048cc: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x7048cc: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x7048d0: r0 = complete()
    //     0x7048d0: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0x7048d4: r0 = Null
    //     0x7048d4: mov             x0, NULL
    // 0x7048d8: LeaveFrame
    //     0x7048d8: mov             SP, fp
    //     0x7048dc: ldp             fp, lr, [SP], #0x10
    // 0x7048e0: ret
    //     0x7048e0: ret             
    // 0x7048e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7048e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7048e8: b               #0x70482c
  }
  [closure] static void timeEndAndCleanup(dynamic) {
    // ** addr: 0x7048ec, size: 0xb8
    // 0x7048ec: EnterFrame
    //     0x7048ec: stp             fp, lr, [SP, #-0x10]!
    //     0x7048f0: mov             fp, SP
    // 0x7048f4: AllocStack(0x18)
    //     0x7048f4: sub             SP, SP, #0x18
    // 0x7048f8: SetupParameters()
    //     0x7048f8: ldr             x0, [fp, #0x10]
    //     0x7048fc: ldur            w3, [x0, #0x17]
    //     0x704900: add             x3, x3, HEAP, lsl #32
    //     0x704904: stur            x3, [fp, #-0x10]
    // 0x704908: CheckStackOverflow
    //     0x704908: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x70490c: cmp             SP, x16
    //     0x704910: b.ls            #0x70499c
    // 0x704914: LoadField: r0 = r3->field_f
    //     0x704914: ldur            w0, [x3, #0xf]
    // 0x704918: DecompressPointer r0
    //     0x704918: add             x0, x0, HEAP, lsl #32
    // 0x70491c: stur            x0, [fp, #-8]
    // 0x704920: r1 = Null
    //     0x704920: mov             x1, NULL
    // 0x704924: r2 = 4
    //     0x704924: movz            x2, #0x4
    // 0x704928: r0 = AllocateArray()
    //     0x704928: bl              #0xec22fc  ; AllocateArrayStub
    // 0x70492c: mov             x1, x0
    // 0x704930: ldur            x0, [fp, #-8]
    // 0x704934: StoreField: r1->field_f = r0
    //     0x704934: stur            w0, [x1, #0xf]
    // 0x704938: r16 = ": end"
    //     0x704938: add             x16, PP, #0x10, lsl #12  ; [pp+0x10730] ": end"
    //     0x70493c: ldr             x16, [x16, #0x730]
    // 0x704940: StoreField: r1->field_13 = r16
    //     0x704940: stur            w16, [x1, #0x13]
    // 0x704944: str             x1, [SP]
    // 0x704948: r0 = _interpolate()
    //     0x704948: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x70494c: mov             x2, x0
    // 0x704950: ldur            x0, [fp, #-0x10]
    // 0x704954: stur            x2, [fp, #-8]
    // 0x704958: LoadField: r1 = r0->field_13
    //     0x704958: ldur            w1, [x0, #0x13]
    // 0x70495c: DecompressPointer r1
    //     0x70495c: add             x1, x1, HEAP, lsl #32
    // 0x704960: LoadField: r3 = r1->field_f
    //     0x704960: ldur            x3, [x1, #0xf]
    // 0x704964: mov             x1, x3
    // 0x704968: r0 = end()
    //     0x704968: bl              #0x703da8  ; [dart:developer] Flow::end
    // 0x70496c: ldur            x1, [fp, #-8]
    // 0x704970: mov             x2, x0
    // 0x704974: r0 = startSync()
    //     0x704974: bl              #0x7040b8  ; [dart:developer] Timeline::startSync
    // 0x704978: ldur            x0, [fp, #-0x10]
    // 0x70497c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x70497c: ldur            w1, [x0, #0x17]
    // 0x704980: DecompressPointer r1
    //     0x704980: add             x1, x1, HEAP, lsl #32
    // 0x704984: r0 = close()
    //     0x704984: bl              #0x65f548  ; [dart:isolate] _RawReceivePort::close
    // 0x704988: r0 = finishSync()
    //     0x704988: bl              #0x703df0  ; [dart:developer] Timeline::finishSync
    // 0x70498c: r0 = Null
    //     0x70498c: mov             x0, NULL
    // 0x704990: LeaveFrame
    //     0x704990: mov             SP, fp
    //     0x704994: ldp             fp, lr, [SP], #0x10
    // 0x704998: ret
    //     0x704998: ret             
    // 0x70499c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x70499c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7049a0: b               #0x704914
  }
}

// class id: 5655, size: 0x24, field offset: 0x8
//   const constructor, 
class _IsolateConfiguration<X0, X1> extends Object {

  _ applyAndTime(/* No info */) {
    // ** addr: 0x704638, size: 0xbc
    // 0x704638: EnterFrame
    //     0x704638: stp             fp, lr, [SP, #-0x10]!
    //     0x70463c: mov             fp, SP
    // 0x704640: AllocStack(0x40)
    //     0x704640: sub             SP, SP, #0x40
    // 0x704644: SetupParameters(_IsolateConfiguration<X0, X1> this /* r1 => r1, fp-0x8 */)
    //     0x704644: stur            x1, [fp, #-8]
    // 0x704648: CheckStackOverflow
    //     0x704648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x70464c: cmp             SP, x16
    //     0x704650: b.ls            #0x7046ec
    // 0x704654: r1 = 1
    //     0x704654: movz            x1, #0x1
    // 0x704658: r0 = AllocateContext()
    //     0x704658: bl              #0xec126c  ; AllocateContextStub
    // 0x70465c: mov             x2, x0
    // 0x704660: ldur            x0, [fp, #-8]
    // 0x704664: stur            x2, [fp, #-0x20]
    // 0x704668: StoreField: r2->field_f = r0
    //     0x704668: stur            w0, [x2, #0xf]
    // 0x70466c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x70466c: ldur            w3, [x0, #0x17]
    // 0x704670: DecompressPointer r3
    //     0x704670: add             x3, x3, HEAP, lsl #32
    // 0x704674: stur            x3, [fp, #-0x18]
    // 0x704678: LoadField: r4 = r0->field_7
    //     0x704678: ldur            w4, [x0, #7]
    // 0x70467c: DecompressPointer r4
    //     0x70467c: add             x4, x4, HEAP, lsl #32
    // 0x704680: stur            x4, [fp, #-0x10]
    // 0x704684: LoadField: r1 = r0->field_1b
    //     0x704684: ldur            x1, [x0, #0x1b]
    // 0x704688: r0 = step()
    //     0x704688: bl              #0x704770  ; [dart:developer] Flow::step
    // 0x70468c: ldur            x2, [fp, #-0x10]
    // 0x704690: r1 = Null
    //     0x704690: mov             x1, NULL
    // 0x704694: r3 = <FutureOr<X1>>
    //     0x704694: add             x3, PP, #0x10, lsl #12  ; [pp+0x10750] TypeArguments: <FutureOr<X1>>
    //     0x704698: ldr             x3, [x3, #0x750]
    // 0x70469c: stur            x0, [fp, #-8]
    // 0x7046a0: r30 = InstantiateTypeArgumentsStub
    //     0x7046a0: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7046a4: LoadField: r30 = r30->field_7
    //     0x7046a4: ldur            lr, [lr, #7]
    // 0x7046a8: blr             lr
    // 0x7046ac: ldur            x2, [fp, #-0x20]
    // 0x7046b0: ldur            x3, [fp, #-0x10]
    // 0x7046b4: r1 = Function '<anonymous closure>':.
    //     0x7046b4: add             x1, PP, #0x10, lsl #12  ; [pp+0x10758] AnonymousClosure: (0x7047a0), in [package:dio/src/compute/compute_io.dart] _IsolateConfiguration::applyAndTime (0x704638)
    //     0x7046b8: ldr             x1, [x1, #0x758]
    // 0x7046bc: stur            x0, [fp, #-0x10]
    // 0x7046c0: r0 = AllocateClosureTA()
    //     0x7046c0: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x7046c4: ldur            x16, [fp, #-0x10]
    // 0x7046c8: ldur            lr, [fp, #-0x18]
    // 0x7046cc: stp             lr, x16, [SP, #0x10]
    // 0x7046d0: ldur            x16, [fp, #-8]
    // 0x7046d4: stp             x16, x0, [SP]
    // 0x7046d8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x7046d8: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x7046dc: r0 = timeSync()
    //     0x7046dc: bl              #0x7046f4  ; [dart:developer] Timeline::timeSync
    // 0x7046e0: LeaveFrame
    //     0x7046e0: mov             SP, fp
    //     0x7046e4: ldp             fp, lr, [SP], #0x10
    // 0x7046e8: ret
    //     0x7046e8: ret             
    // 0x7046ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7046ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7046f0: b               #0x704654
  }
  [closure] FutureOr<X1> <anonymous closure>(dynamic) {
    // ** addr: 0x7047a0, size: 0x64
    // 0x7047a0: EnterFrame
    //     0x7047a0: stp             fp, lr, [SP, #-0x10]!
    //     0x7047a4: mov             fp, SP
    // 0x7047a8: AllocStack(0x10)
    //     0x7047a8: sub             SP, SP, #0x10
    // 0x7047ac: SetupParameters()
    //     0x7047ac: ldr             x0, [fp, #0x10]
    //     0x7047b0: ldur            w1, [x0, #0x17]
    //     0x7047b4: add             x1, x1, HEAP, lsl #32
    // 0x7047b8: CheckStackOverflow
    //     0x7047b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7047bc: cmp             SP, x16
    //     0x7047c0: b.ls            #0x7047fc
    // 0x7047c4: LoadField: r0 = r1->field_f
    //     0x7047c4: ldur            w0, [x1, #0xf]
    // 0x7047c8: DecompressPointer r0
    //     0x7047c8: add             x0, x0, HEAP, lsl #32
    // 0x7047cc: LoadField: r1 = r0->field_f
    //     0x7047cc: ldur            w1, [x0, #0xf]
    // 0x7047d0: DecompressPointer r1
    //     0x7047d0: add             x1, x1, HEAP, lsl #32
    // 0x7047d4: LoadField: r2 = r0->field_b
    //     0x7047d4: ldur            w2, [x0, #0xb]
    // 0x7047d8: DecompressPointer r2
    //     0x7047d8: add             x2, x2, HEAP, lsl #32
    // 0x7047dc: stp             x1, x2, [SP]
    // 0x7047e0: mov             x0, x2
    // 0x7047e4: ClosureCall
    //     0x7047e4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x7047e8: ldur            x2, [x0, #0x1f]
    //     0x7047ec: blr             x2
    // 0x7047f0: LeaveFrame
    //     0x7047f0: mov             SP, fp
    //     0x7047f4: ldp             fp, lr, [SP], #0x10
    // 0x7047f8: ret
    //     0x7047f8: ret             
    // 0x7047fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7047fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x704800: b               #0x7047c4
  }
}
