// lib: , url: package:dio/src/dio_exception.dart

// class id: 1048691, size: 0x8
class :: {

  [closure] static String defaultDioExceptionReadableStringBuilder(dynamic, DioException) {
    // ** addr: 0xc1c2f8, size: 0x30
    // 0xc1c2f8: EnterFrame
    //     0xc1c2f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c2fc: mov             fp, SP
    // 0xc1c300: CheckStackOverflow
    //     0xc1c300: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1c304: cmp             SP, x16
    //     0xc1c308: b.ls            #0xc1c320
    // 0xc1c30c: ldr             x1, [fp, #0x10]
    // 0xc1c310: r0 = defaultDioExceptionReadableStringBuilder()
    //     0xc1c310: bl              #0xc1c328  ; [package:dio/src/dio_exception.dart] ::defaultDioExceptionReadableStringBuilder
    // 0xc1c314: LeaveFrame
    //     0xc1c314: mov             SP, fp
    //     0xc1c318: ldp             fp, lr, [SP], #0x10
    // 0xc1c31c: ret
    //     0xc1c31c: ret             
    // 0xc1c320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1c320: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1c324: b               #0xc1c30c
  }
  static _ defaultDioExceptionReadableStringBuilder(/* No info */) {
    // ** addr: 0xc1c328, size: 0x198
    // 0xc1c328: EnterFrame
    //     0xc1c328: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c32c: mov             fp, SP
    // 0xc1c330: AllocStack(0x20)
    //     0xc1c330: sub             SP, SP, #0x20
    // 0xc1c334: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xc1c334: mov             x0, x1
    //     0xc1c338: stur            x1, [fp, #-8]
    // 0xc1c33c: CheckStackOverflow
    //     0xc1c33c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1c340: cmp             SP, x16
    //     0xc1c344: b.ls            #0xc1c4b8
    // 0xc1c348: r1 = Null
    //     0xc1c348: mov             x1, NULL
    // 0xc1c34c: r2 = 8
    //     0xc1c34c: movz            x2, #0x8
    // 0xc1c350: r0 = AllocateArray()
    //     0xc1c350: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1c354: r16 = "DioException ["
    //     0xc1c354: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c4a0] "DioException ["
    //     0xc1c358: ldr             x16, [x16, #0x4a0]
    // 0xc1c35c: StoreField: r0->field_f = r16
    //     0xc1c35c: stur            w16, [x0, #0xf]
    // 0xc1c360: ldur            x1, [fp, #-8]
    // 0xc1c364: LoadField: r2 = r1->field_f
    //     0xc1c364: ldur            w2, [x1, #0xf]
    // 0xc1c368: DecompressPointer r2
    //     0xc1c368: add             x2, x2, HEAP, lsl #32
    // 0xc1c36c: LoadField: r3 = r2->field_7
    //     0xc1c36c: ldur            x3, [x2, #7]
    // 0xc1c370: cmp             x3, #3
    // 0xc1c374: b.gt            #0xc1c3c0
    // 0xc1c378: cmp             x3, #1
    // 0xc1c37c: b.gt            #0xc1c3a0
    // 0xc1c380: cmp             x3, #0
    // 0xc1c384: b.gt            #0xc1c394
    // 0xc1c388: r2 = "connection timeout"
    //     0xc1c388: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c4a8] "connection timeout"
    //     0xc1c38c: ldr             x2, [x2, #0x4a8]
    // 0xc1c390: b               #0xc1c404
    // 0xc1c394: r2 = "send timeout"
    //     0xc1c394: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c4b0] "send timeout"
    //     0xc1c398: ldr             x2, [x2, #0x4b0]
    // 0xc1c39c: b               #0xc1c404
    // 0xc1c3a0: cmp             x3, #2
    // 0xc1c3a4: b.gt            #0xc1c3b4
    // 0xc1c3a8: r2 = "receive timeout"
    //     0xc1c3a8: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c4b8] "receive timeout"
    //     0xc1c3ac: ldr             x2, [x2, #0x4b8]
    // 0xc1c3b0: b               #0xc1c404
    // 0xc1c3b4: r2 = "bad certificate"
    //     0xc1c3b4: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c4c0] "bad certificate"
    //     0xc1c3b8: ldr             x2, [x2, #0x4c0]
    // 0xc1c3bc: b               #0xc1c404
    // 0xc1c3c0: cmp             x3, #5
    // 0xc1c3c4: b.gt            #0xc1c3e8
    // 0xc1c3c8: cmp             x3, #4
    // 0xc1c3cc: b.gt            #0xc1c3dc
    // 0xc1c3d0: r2 = "bad response"
    //     0xc1c3d0: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c4c8] "bad response"
    //     0xc1c3d4: ldr             x2, [x2, #0x4c8]
    // 0xc1c3d8: b               #0xc1c404
    // 0xc1c3dc: r2 = "request cancelled"
    //     0xc1c3dc: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c4d0] "request cancelled"
    //     0xc1c3e0: ldr             x2, [x2, #0x4d0]
    // 0xc1c3e4: b               #0xc1c404
    // 0xc1c3e8: cmp             x3, #6
    // 0xc1c3ec: b.gt            #0xc1c3fc
    // 0xc1c3f0: r2 = "connection error"
    //     0xc1c3f0: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c4d8] "connection error"
    //     0xc1c3f4: ldr             x2, [x2, #0x4d8]
    // 0xc1c3f8: b               #0xc1c404
    // 0xc1c3fc: r2 = "unknown"
    //     0xc1c3fc: add             x2, PP, #0xd, lsl #12  ; [pp+0xd490] "unknown"
    //     0xc1c400: ldr             x2, [x2, #0x490]
    // 0xc1c404: StoreField: r0->field_13 = r2
    //     0xc1c404: stur            w2, [x0, #0x13]
    // 0xc1c408: r16 = "]: "
    //     0xc1c408: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c4e0] "]: "
    //     0xc1c40c: ldr             x16, [x16, #0x4e0]
    // 0xc1c410: ArrayStore: r0[0] = r16  ; List_4
    //     0xc1c410: stur            w16, [x0, #0x17]
    // 0xc1c414: LoadField: r2 = r1->field_1b
    //     0xc1c414: ldur            w2, [x1, #0x1b]
    // 0xc1c418: DecompressPointer r2
    //     0xc1c418: add             x2, x2, HEAP, lsl #32
    // 0xc1c41c: StoreField: r0->field_1b = r2
    //     0xc1c41c: stur            w2, [x0, #0x1b]
    // 0xc1c420: str             x0, [SP]
    // 0xc1c424: r0 = _interpolate()
    //     0xc1c424: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1c428: stur            x0, [fp, #-0x10]
    // 0xc1c42c: r0 = StringBuffer()
    //     0xc1c42c: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xc1c430: stur            x0, [fp, #-0x18]
    // 0xc1c434: ldur            x16, [fp, #-0x10]
    // 0xc1c438: str             x16, [SP]
    // 0xc1c43c: mov             x1, x0
    // 0xc1c440: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xc1c440: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xc1c444: r0 = StringBuffer()
    //     0xc1c444: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xc1c448: ldur            x0, [fp, #-8]
    // 0xc1c44c: LoadField: r2 = r0->field_13
    //     0xc1c44c: ldur            w2, [x0, #0x13]
    // 0xc1c450: DecompressPointer r2
    //     0xc1c450: add             x2, x2, HEAP, lsl #32
    // 0xc1c454: stur            x2, [fp, #-0x10]
    // 0xc1c458: cmp             w2, NULL
    // 0xc1c45c: b.eq            #0xc1c4a0
    // 0xc1c460: ldur            x1, [fp, #-0x18]
    // 0xc1c464: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc1c464: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc1c468: r0 = writeln()
    //     0xc1c468: bl              #0x702a88  ; [dart:core] StringBuffer::writeln
    // 0xc1c46c: r1 = Null
    //     0xc1c46c: mov             x1, NULL
    // 0xc1c470: r2 = 4
    //     0xc1c470: movz            x2, #0x4
    // 0xc1c474: r0 = AllocateArray()
    //     0xc1c474: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1c478: r16 = "Error: "
    //     0xc1c478: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c4e8] "Error: "
    //     0xc1c47c: ldr             x16, [x16, #0x4e8]
    // 0xc1c480: StoreField: r0->field_f = r16
    //     0xc1c480: stur            w16, [x0, #0xf]
    // 0xc1c484: ldur            x1, [fp, #-0x10]
    // 0xc1c488: StoreField: r0->field_13 = r1
    //     0xc1c488: stur            w1, [x0, #0x13]
    // 0xc1c48c: str             x0, [SP]
    // 0xc1c490: r0 = _interpolate()
    //     0xc1c490: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1c494: ldur            x1, [fp, #-0x18]
    // 0xc1c498: mov             x2, x0
    // 0xc1c49c: r0 = write()
    //     0xc1c49c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc1c4a0: ldur            x16, [fp, #-0x18]
    // 0xc1c4a4: str             x16, [SP]
    // 0xc1c4a8: r0 = toString()
    //     0xc1c4a8: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xc1c4ac: LeaveFrame
    //     0xc1c4ac: mov             SP, fp
    //     0xc1c4b0: ldp             fp, lr, [SP], #0x10
    // 0xc1c4b4: ret
    //     0xc1c4b4: ret             
    // 0xc1c4b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1c4b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1c4bc: b               #0xc1c348
  }
}

// class id: 5641, size: 0x24, field offset: 0x8
class DioException extends Object
    implements Exception {

  static late (dynamic, DioException) => String readableStringBuilder; // offset: 0xd04

  _ DioException(/* No info */) {
    // ** addr: 0x70054c, size: 0x204
    // 0x70054c: EnterFrame
    //     0x70054c: stp             fp, lr, [SP, #-0x10]!
    //     0x700550: mov             fp, SP
    // 0x700554: AllocStack(0x8)
    //     0x700554: sub             SP, SP, #8
    // 0x700558: SetupParameters(DioException this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r1 */, {dynamic message = Null /* r6 */, dynamic response = Null /* r7 */, dynamic type = Instance_DioExceptionType /* r4 */})
    //     0x700558: stur            x1, [fp, #-8]
    //     0x70055c: mov             x16, x3
    //     0x700560: mov             x3, x1
    //     0x700564: mov             x1, x16
    //     0x700568: ldur            w0, [x4, #0x13]
    //     0x70056c: ldur            w5, [x4, #0x1f]
    //     0x700570: add             x5, x5, HEAP, lsl #32
    //     0x700574: add             x16, PP, #0xc, lsl #12  ; [pp+0xc330] "message"
    //     0x700578: ldr             x16, [x16, #0x330]
    //     0x70057c: cmp             w5, w16
    //     0x700580: b.ne            #0x7005a4
    //     0x700584: ldur            w5, [x4, #0x23]
    //     0x700588: add             x5, x5, HEAP, lsl #32
    //     0x70058c: sub             w6, w0, w5
    //     0x700590: add             x5, fp, w6, sxtw #2
    //     0x700594: ldr             x5, [x5, #8]
    //     0x700598: mov             x6, x5
    //     0x70059c: movz            x5, #0x1
    //     0x7005a0: b               #0x7005ac
    //     0x7005a4: mov             x6, NULL
    //     0x7005a8: movz            x5, #0
    //     0x7005ac: lsl             x7, x5, #1
    //     0x7005b0: lsl             w8, w7, #1
    //     0x7005b4: add             w9, w8, #8
    //     0x7005b8: add             x16, x4, w9, sxtw #1
    //     0x7005bc: ldur            w10, [x16, #0xf]
    //     0x7005c0: add             x10, x10, HEAP, lsl #32
    //     0x7005c4: ldr             x16, [PP, #0x2f00]  ; [pp+0x2f00] "response"
    //     0x7005c8: cmp             w10, w16
    //     0x7005cc: b.ne            #0x700600
    //     0x7005d0: add             w5, w8, #0xa
    //     0x7005d4: add             x16, x4, w5, sxtw #1
    //     0x7005d8: ldur            w8, [x16, #0xf]
    //     0x7005dc: add             x8, x8, HEAP, lsl #32
    //     0x7005e0: sub             w5, w0, w8
    //     0x7005e4: add             x8, fp, w5, sxtw #2
    //     0x7005e8: ldr             x8, [x8, #8]
    //     0x7005ec: add             w5, w7, #2
    //     0x7005f0: sbfx            x7, x5, #1, #0x1f
    //     0x7005f4: mov             x5, x7
    //     0x7005f8: mov             x7, x8
    //     0x7005fc: b               #0x700604
    //     0x700600: mov             x7, NULL
    //     0x700604: lsl             x8, x5, #1
    //     0x700608: lsl             w5, w8, #1
    //     0x70060c: add             w8, w5, #8
    //     0x700610: add             x16, x4, w8, sxtw #1
    //     0x700614: ldur            w9, [x16, #0xf]
    //     0x700618: add             x9, x9, HEAP, lsl #32
    //     0x70061c: ldr             x16, [PP, #0x3020]  ; [pp+0x3020] "type"
    //     0x700620: cmp             w9, w16
    //     0x700624: b.ne            #0x70064c
    //     0x700628: add             w8, w5, #0xa
    //     0x70062c: add             x16, x4, w8, sxtw #1
    //     0x700630: ldur            w5, [x16, #0xf]
    //     0x700634: add             x5, x5, HEAP, lsl #32
    //     0x700638: sub             w4, w0, w5
    //     0x70063c: add             x0, fp, w4, sxtw #2
    //     0x700640: ldr             x0, [x0, #8]
    //     0x700644: mov             x4, x0
    //     0x700648: b               #0x700654
    //     0x70064c: add             x4, PP, #0xf, lsl #12  ; [pp+0xf740] Obj!DioExceptionType@e37641
    //     0x700650: ldr             x4, [x4, #0x740]
    // 0x700654: CheckStackOverflow
    //     0x700654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x700658: cmp             SP, x16
    //     0x70065c: b.ls            #0x700748
    // 0x700660: mov             x0, x1
    // 0x700664: StoreField: r3->field_7 = r0
    //     0x700664: stur            w0, [x3, #7]
    //     0x700668: ldurb           w16, [x3, #-1]
    //     0x70066c: ldurb           w17, [x0, #-1]
    //     0x700670: and             x16, x17, x16, lsr #2
    //     0x700674: tst             x16, HEAP, lsr #32
    //     0x700678: b.eq            #0x700680
    //     0x70067c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x700680: mov             x0, x7
    // 0x700684: StoreField: r3->field_b = r0
    //     0x700684: stur            w0, [x3, #0xb]
    //     0x700688: ldurb           w16, [x3, #-1]
    //     0x70068c: ldurb           w17, [x0, #-1]
    //     0x700690: and             x16, x17, x16, lsr #2
    //     0x700694: tst             x16, HEAP, lsr #32
    //     0x700698: b.eq            #0x7006a0
    //     0x70069c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7006a0: mov             x0, x4
    // 0x7006a4: StoreField: r3->field_f = r0
    //     0x7006a4: stur            w0, [x3, #0xf]
    //     0x7006a8: ldurb           w16, [x3, #-1]
    //     0x7006ac: ldurb           w17, [x0, #-1]
    //     0x7006b0: and             x16, x17, x16, lsr #2
    //     0x7006b4: tst             x16, HEAP, lsr #32
    //     0x7006b8: b.eq            #0x7006c0
    //     0x7006bc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7006c0: mov             x0, x2
    // 0x7006c4: StoreField: r3->field_13 = r0
    //     0x7006c4: stur            w0, [x3, #0x13]
    //     0x7006c8: tbz             w0, #0, #0x7006e4
    //     0x7006cc: ldurb           w16, [x3, #-1]
    //     0x7006d0: ldurb           w17, [x0, #-1]
    //     0x7006d4: and             x16, x17, x16, lsr #2
    //     0x7006d8: tst             x16, HEAP, lsr #32
    //     0x7006dc: b.eq            #0x7006e4
    //     0x7006e0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7006e4: mov             x0, x6
    // 0x7006e8: StoreField: r3->field_1b = r0
    //     0x7006e8: stur            w0, [x3, #0x1b]
    //     0x7006ec: ldurb           w16, [x3, #-1]
    //     0x7006f0: ldurb           w17, [x0, #-1]
    //     0x7006f4: and             x16, x17, x16, lsr #2
    //     0x7006f8: tst             x16, HEAP, lsr #32
    //     0x7006fc: b.eq            #0x700704
    //     0x700700: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x700704: LoadField: r0 = r1->field_53
    //     0x700704: ldur            w0, [x1, #0x53]
    // 0x700708: DecompressPointer r0
    //     0x700708: add             x0, x0, HEAP, lsl #32
    // 0x70070c: cmp             w0, NULL
    // 0x700710: b.ne            #0x700718
    // 0x700714: r0 = current()
    //     0x700714: bl              #0x5fc8a0  ; [dart:core] StackTrace::current
    // 0x700718: ldur            x1, [fp, #-8]
    // 0x70071c: ArrayStore: r1[0] = r0  ; List_4
    //     0x70071c: stur            w0, [x1, #0x17]
    //     0x700720: ldurb           w16, [x1, #-1]
    //     0x700724: ldurb           w17, [x0, #-1]
    //     0x700728: and             x16, x17, x16, lsr #2
    //     0x70072c: tst             x16, HEAP, lsr #32
    //     0x700730: b.eq            #0x700738
    //     0x700734: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x700738: r0 = Null
    //     0x700738: mov             x0, NULL
    // 0x70073c: LeaveFrame
    //     0x70073c: mov             SP, fp
    //     0x700740: ldp             fp, lr, [SP], #0x10
    // 0x700744: ret
    //     0x700744: ret             
    // 0x700748: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x700748: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x70074c: b               #0x700660
  }
  factory _ DioException.badResponse(/* No info */) {
    // ** addr: 0x702838, size: 0x84
    // 0x702838: EnterFrame
    //     0x702838: stp             fp, lr, [SP, #-0x10]!
    //     0x70283c: mov             fp, SP
    // 0x702840: AllocStack(0x38)
    //     0x702840: sub             SP, SP, #0x38
    // 0x702844: SetupParameters(dynamic _ /* r1 => r2 */, dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r1 */)
    //     0x702844: mov             x0, x2
    //     0x702848: stur            x2, [fp, #-8]
    //     0x70284c: mov             x2, x1
    //     0x702850: mov             x1, x5
    //     0x702854: stur            x3, [fp, #-0x10]
    // 0x702858: CheckStackOverflow
    //     0x702858: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x70285c: cmp             SP, x16
    //     0x702860: b.ls            #0x7028b4
    // 0x702864: r0 = _badResponseExceptionMessage()
    //     0x702864: bl              #0x7028bc  ; [package:dio/src/dio_exception.dart] DioException::_badResponseExceptionMessage
    // 0x702868: stur            x0, [fp, #-0x18]
    // 0x70286c: r0 = DioException()
    //     0x70286c: bl              #0x7007bc  ; AllocateDioExceptionStub -> DioException (size=0x24)
    // 0x702870: stur            x0, [fp, #-0x20]
    // 0x702874: r16 = Instance_DioExceptionType
    //     0x702874: add             x16, PP, #0xf, lsl #12  ; [pp+0xf750] Obj!DioExceptionType@e37681
    //     0x702878: ldr             x16, [x16, #0x750]
    // 0x70287c: ldur            lr, [fp, #-0x10]
    // 0x702880: stp             lr, x16, [SP, #8]
    // 0x702884: ldur            x16, [fp, #-0x18]
    // 0x702888: str             x16, [SP]
    // 0x70288c: mov             x1, x0
    // 0x702890: ldur            x3, [fp, #-8]
    // 0x702894: r2 = Null
    //     0x702894: mov             x2, NULL
    // 0x702898: r4 = const [0, 0x6, 0x3, 0x3, message, 0x5, response, 0x4, type, 0x3, null]
    //     0x702898: add             x4, PP, #0x10, lsl #12  ; [pp+0x105e0] List(11) [0, 0x6, 0x3, 0x3, "message", 0x5, "response", 0x4, "type", 0x3, Null]
    //     0x70289c: ldr             x4, [x4, #0x5e0]
    // 0x7028a0: r0 = DioException()
    //     0x7028a0: bl              #0x70054c  ; [package:dio/src/dio_exception.dart] DioException::DioException
    // 0x7028a4: ldur            x0, [fp, #-0x20]
    // 0x7028a8: LeaveFrame
    //     0x7028a8: mov             SP, fp
    //     0x7028ac: ldp             fp, lr, [SP], #0x10
    // 0x7028b0: ret
    //     0x7028b0: ret             
    // 0x7028b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7028b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7028b8: b               #0x702864
  }
  static String _badResponseExceptionMessage(int) {
    // ** addr: 0x7028bc, size: 0x1cc
    // 0x7028bc: EnterFrame
    //     0x7028bc: stp             fp, lr, [SP, #-0x10]!
    //     0x7028c0: mov             fp, SP
    // 0x7028c4: AllocStack(0x28)
    //     0x7028c4: sub             SP, SP, #0x28
    // 0x7028c8: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */)
    //     0x7028c8: stur            x1, [fp, #-0x10]
    // 0x7028cc: CheckStackOverflow
    //     0x7028cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7028d0: cmp             SP, x16
    //     0x7028d4: b.ls            #0x702a80
    // 0x7028d8: cmp             x1, #0x64
    // 0x7028dc: b.lt            #0x7028f4
    // 0x7028e0: cmp             x1, #0xc8
    // 0x7028e4: b.ge            #0x7028f4
    // 0x7028e8: r0 = "This is an informational response - the request was received, continuing processing"
    //     0x7028e8: add             x0, PP, #0x10, lsl #12  ; [pp+0x105e8] "This is an informational response - the request was received, continuing processing"
    //     0x7028ec: ldr             x0, [x0, #0x5e8]
    // 0x7028f0: b               #0x70296c
    // 0x7028f4: cmp             x1, #0xc8
    // 0x7028f8: b.lt            #0x702910
    // 0x7028fc: cmp             x1, #0x12c
    // 0x702900: b.ge            #0x702910
    // 0x702904: r0 = "The request was successfully received, understood, and accepted"
    //     0x702904: add             x0, PP, #0x10, lsl #12  ; [pp+0x105f0] "The request was successfully received, understood, and accepted"
    //     0x702908: ldr             x0, [x0, #0x5f0]
    // 0x70290c: b               #0x70296c
    // 0x702910: cmp             x1, #0x12c
    // 0x702914: b.lt            #0x70292c
    // 0x702918: cmp             x1, #0x190
    // 0x70291c: b.ge            #0x70292c
    // 0x702920: r0 = "Redirection: further action needs to be taken in order to complete the request"
    //     0x702920: add             x0, PP, #0x10, lsl #12  ; [pp+0x105f8] "Redirection: further action needs to be taken in order to complete the request"
    //     0x702924: ldr             x0, [x0, #0x5f8]
    // 0x702928: b               #0x70296c
    // 0x70292c: cmp             x1, #0x190
    // 0x702930: b.lt            #0x702948
    // 0x702934: cmp             x1, #0x1f4
    // 0x702938: b.ge            #0x702948
    // 0x70293c: r0 = "Client error - the request contains bad syntax or cannot be fulfilled"
    //     0x70293c: add             x0, PP, #0x10, lsl #12  ; [pp+0x10600] "Client error - the request contains bad syntax or cannot be fulfilled"
    //     0x702940: ldr             x0, [x0, #0x600]
    // 0x702944: b               #0x70296c
    // 0x702948: cmp             x1, #0x1f4
    // 0x70294c: b.lt            #0x702964
    // 0x702950: cmp             x1, #0x258
    // 0x702954: b.ge            #0x702964
    // 0x702958: r0 = "Server error - the server failed to fulfil an apparently valid request"
    //     0x702958: add             x0, PP, #0x10, lsl #12  ; [pp+0x10608] "Server error - the server failed to fulfil an apparently valid request"
    //     0x70295c: ldr             x0, [x0, #0x608]
    // 0x702960: b               #0x70296c
    // 0x702964: r0 = "A response with a status code that is not within the range of inclusive 100 to exclusive 600is a non-standard response, possibly due to the server\'s software"
    //     0x702964: add             x0, PP, #0x10, lsl #12  ; [pp+0x10610] "A response with a status code that is not within the range of inclusive 100 to exclusive 600is a non-standard response, possibly due to the server\'s software"
    //     0x702968: ldr             x0, [x0, #0x610]
    // 0x70296c: stur            x0, [fp, #-8]
    // 0x702970: r0 = StringBuffer()
    //     0x702970: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x702974: mov             x1, x0
    // 0x702978: stur            x0, [fp, #-0x18]
    // 0x70297c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x70297c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x702980: r0 = StringBuffer()
    //     0x702980: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0x702984: r1 = Null
    //     0x702984: mov             x1, NULL
    // 0x702988: r2 = 6
    //     0x702988: movz            x2, #0x6
    // 0x70298c: r0 = AllocateArray()
    //     0x70298c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x702990: mov             x2, x0
    // 0x702994: r16 = "This exception was thrown because the response has a status code of "
    //     0x702994: add             x16, PP, #0x10, lsl #12  ; [pp+0x10618] "This exception was thrown because the response has a status code of "
    //     0x702998: ldr             x16, [x16, #0x618]
    // 0x70299c: StoreField: r2->field_f = r16
    //     0x70299c: stur            w16, [x2, #0xf]
    // 0x7029a0: ldur            x3, [fp, #-0x10]
    // 0x7029a4: r0 = BoxInt64Instr(r3)
    //     0x7029a4: sbfiz           x0, x3, #1, #0x1f
    //     0x7029a8: cmp             x3, x0, asr #1
    //     0x7029ac: b.eq            #0x7029b8
    //     0x7029b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7029b4: stur            x3, [x0, #7]
    // 0x7029b8: stur            x0, [fp, #-0x20]
    // 0x7029bc: StoreField: r2->field_13 = r0
    //     0x7029bc: stur            w0, [x2, #0x13]
    // 0x7029c0: r16 = " and RequestOptions.validateStatus was configured to throw for this status code."
    //     0x7029c0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10620] " and RequestOptions.validateStatus was configured to throw for this status code."
    //     0x7029c4: ldr             x16, [x16, #0x620]
    // 0x7029c8: ArrayStore: r2[0] = r16  ; List_4
    //     0x7029c8: stur            w16, [x2, #0x17]
    // 0x7029cc: str             x2, [SP]
    // 0x7029d0: r0 = _interpolate()
    //     0x7029d0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7029d4: str             x0, [SP]
    // 0x7029d8: ldur            x1, [fp, #-0x18]
    // 0x7029dc: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x7029dc: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x7029e0: r0 = writeln()
    //     0x7029e0: bl              #0x702a88  ; [dart:core] StringBuffer::writeln
    // 0x7029e4: r1 = Null
    //     0x7029e4: mov             x1, NULL
    // 0x7029e8: r2 = 10
    //     0x7029e8: movz            x2, #0xa
    // 0x7029ec: r0 = AllocateArray()
    //     0x7029ec: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7029f0: r16 = "The status code of "
    //     0x7029f0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10628] "The status code of "
    //     0x7029f4: ldr             x16, [x16, #0x628]
    // 0x7029f8: StoreField: r0->field_f = r16
    //     0x7029f8: stur            w16, [x0, #0xf]
    // 0x7029fc: ldur            x1, [fp, #-0x20]
    // 0x702a00: StoreField: r0->field_13 = r1
    //     0x702a00: stur            w1, [x0, #0x13]
    // 0x702a04: r16 = " has the following meaning: \""
    //     0x702a04: add             x16, PP, #0x10, lsl #12  ; [pp+0x10630] " has the following meaning: \""
    //     0x702a08: ldr             x16, [x16, #0x630]
    // 0x702a0c: ArrayStore: r0[0] = r16  ; List_4
    //     0x702a0c: stur            w16, [x0, #0x17]
    // 0x702a10: ldur            x1, [fp, #-8]
    // 0x702a14: StoreField: r0->field_1b = r1
    //     0x702a14: stur            w1, [x0, #0x1b]
    // 0x702a18: r16 = "\""
    //     0x702a18: ldr             x16, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0x702a1c: StoreField: r0->field_1f = r16
    //     0x702a1c: stur            w16, [x0, #0x1f]
    // 0x702a20: str             x0, [SP]
    // 0x702a24: r0 = _interpolate()
    //     0x702a24: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x702a28: str             x0, [SP]
    // 0x702a2c: ldur            x1, [fp, #-0x18]
    // 0x702a30: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x702a30: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x702a34: r0 = writeln()
    //     0x702a34: bl              #0x702a88  ; [dart:core] StringBuffer::writeln
    // 0x702a38: r16 = "Read more about status codes at https://developer.mozilla.org/en-US/docs/Web/HTTP/Status"
    //     0x702a38: add             x16, PP, #0x10, lsl #12  ; [pp+0x10638] "Read more about status codes at https://developer.mozilla.org/en-US/docs/Web/HTTP/Status"
    //     0x702a3c: ldr             x16, [x16, #0x638]
    // 0x702a40: str             x16, [SP]
    // 0x702a44: ldur            x1, [fp, #-0x18]
    // 0x702a48: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x702a48: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x702a4c: r0 = writeln()
    //     0x702a4c: bl              #0x702a88  ; [dart:core] StringBuffer::writeln
    // 0x702a50: r16 = "In order to resolve this exception you typically have either to verify and fix your request code or you have to fix the server code."
    //     0x702a50: add             x16, PP, #0x10, lsl #12  ; [pp+0x10640] "In order to resolve this exception you typically have either to verify and fix your request code or you have to fix the server code."
    //     0x702a54: ldr             x16, [x16, #0x640]
    // 0x702a58: str             x16, [SP]
    // 0x702a5c: ldur            x1, [fp, #-0x18]
    // 0x702a60: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x702a60: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x702a64: r0 = writeln()
    //     0x702a64: bl              #0x702a88  ; [dart:core] StringBuffer::writeln
    // 0x702a68: ldur            x16, [fp, #-0x18]
    // 0x702a6c: str             x16, [SP]
    // 0x702a70: r0 = toString()
    //     0x702a70: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0x702a74: LeaveFrame
    //     0x702a74: mov             SP, fp
    //     0x702a78: ldp             fp, lr, [SP], #0x10
    // 0x702a7c: ret
    //     0x702a7c: ret             
    // 0x702a80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x702a80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x702a84: b               #0x7028d8
  }
  factory _ DioException.receiveTimeout(/* No info */) {
    // ** addr: 0x707eb0, size: 0xb8
    // 0x707eb0: EnterFrame
    //     0x707eb0: stp             fp, lr, [SP, #-0x10]!
    //     0x707eb4: mov             fp, SP
    // 0x707eb8: AllocStack(0x30)
    //     0x707eb8: sub             SP, SP, #0x30
    // 0x707ebc: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0x707ebc: mov             x0, x2
    //     0x707ec0: stur            x2, [fp, #-8]
    //     0x707ec4: stur            x3, [fp, #-0x10]
    // 0x707ec8: CheckStackOverflow
    //     0x707ec8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x707ecc: cmp             SP, x16
    //     0x707ed0: b.ls            #0x707f60
    // 0x707ed4: r1 = Null
    //     0x707ed4: mov             x1, NULL
    // 0x707ed8: r2 = 10
    //     0x707ed8: movz            x2, #0xa
    // 0x707edc: r0 = AllocateArray()
    //     0x707edc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x707ee0: r16 = "The request took longer than "
    //     0x707ee0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10b10] "The request took longer than "
    //     0x707ee4: ldr             x16, [x16, #0xb10]
    // 0x707ee8: StoreField: r0->field_f = r16
    //     0x707ee8: stur            w16, [x0, #0xf]
    // 0x707eec: ldur            x1, [fp, #-0x10]
    // 0x707ef0: StoreField: r0->field_13 = r1
    //     0x707ef0: stur            w1, [x0, #0x13]
    // 0x707ef4: r16 = " to receive data. It was aborted. To get rid of this exception, try raising the RequestOptions.receiveTimeout above the duration of "
    //     0x707ef4: add             x16, PP, #0x10, lsl #12  ; [pp+0x10b18] " to receive data. It was aborted. To get rid of this exception, try raising the RequestOptions.receiveTimeout above the duration of "
    //     0x707ef8: ldr             x16, [x16, #0xb18]
    // 0x707efc: ArrayStore: r0[0] = r16  ; List_4
    //     0x707efc: stur            w16, [x0, #0x17]
    // 0x707f00: StoreField: r0->field_1b = r1
    //     0x707f00: stur            w1, [x0, #0x1b]
    // 0x707f04: r16 = " or improve the response time of the server."
    //     0x707f04: add             x16, PP, #0x10, lsl #12  ; [pp+0x10b20] " or improve the response time of the server."
    //     0x707f08: ldr             x16, [x16, #0xb20]
    // 0x707f0c: StoreField: r0->field_1f = r16
    //     0x707f0c: stur            w16, [x0, #0x1f]
    // 0x707f10: str             x0, [SP]
    // 0x707f14: r0 = _interpolate()
    //     0x707f14: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x707f18: stur            x0, [fp, #-0x10]
    // 0x707f1c: r0 = DioException()
    //     0x707f1c: bl              #0x7007bc  ; AllocateDioExceptionStub -> DioException (size=0x24)
    // 0x707f20: stur            x0, [fp, #-0x18]
    // 0x707f24: r16 = Instance_DioExceptionType
    //     0x707f24: add             x16, PP, #0x10, lsl #12  ; [pp+0x10b28] Obj!DioExceptionType@e376a1
    //     0x707f28: ldr             x16, [x16, #0xb28]
    // 0x707f2c: stp             NULL, x16, [SP, #8]
    // 0x707f30: ldur            x16, [fp, #-0x10]
    // 0x707f34: str             x16, [SP]
    // 0x707f38: mov             x1, x0
    // 0x707f3c: ldur            x3, [fp, #-8]
    // 0x707f40: r2 = Null
    //     0x707f40: mov             x2, NULL
    // 0x707f44: r4 = const [0, 0x6, 0x3, 0x3, message, 0x5, response, 0x4, type, 0x3, null]
    //     0x707f44: add             x4, PP, #0x10, lsl #12  ; [pp+0x105e0] List(11) [0, 0x6, 0x3, 0x3, "message", 0x5, "response", 0x4, "type", 0x3, Null]
    //     0x707f48: ldr             x4, [x4, #0x5e0]
    // 0x707f4c: r0 = DioException()
    //     0x707f4c: bl              #0x70054c  ; [package:dio/src/dio_exception.dart] DioException::DioException
    // 0x707f50: ldur            x0, [fp, #-0x18]
    // 0x707f54: LeaveFrame
    //     0x707f54: mov             SP, fp
    //     0x707f58: ldp             fp, lr, [SP], #0x10
    // 0x707f5c: ret
    //     0x707f5c: ret             
    // 0x707f60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x707f60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x707f64: b               #0x707ed4
  }
  factory _ DioException.connectionError(/* No info */) {
    // ** addr: 0x708e34, size: 0xb4
    // 0x708e34: EnterFrame
    //     0x708e34: stp             fp, lr, [SP, #-0x10]!
    //     0x708e38: mov             fp, SP
    // 0x708e3c: AllocStack(0x38)
    //     0x708e3c: sub             SP, SP, #0x38
    // 0x708e40: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0x708e40: mov             x4, x2
    //     0x708e44: mov             x0, x3
    //     0x708e48: stur            x3, [fp, #-0x10]
    //     0x708e4c: mov             x3, x5
    //     0x708e50: stur            x2, [fp, #-8]
    //     0x708e54: stur            x5, [fp, #-0x18]
    // 0x708e58: CheckStackOverflow
    //     0x708e58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x708e5c: cmp             SP, x16
    //     0x708e60: b.ls            #0x708ee0
    // 0x708e64: r1 = Null
    //     0x708e64: mov             x1, NULL
    // 0x708e68: r2 = 6
    //     0x708e68: movz            x2, #0x6
    // 0x708e6c: r0 = AllocateArray()
    //     0x708e6c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x708e70: r16 = "The connection errored: "
    //     0x708e70: add             x16, PP, #0x10, lsl #12  ; [pp+0x10dc0] "The connection errored: "
    //     0x708e74: ldr             x16, [x16, #0xdc0]
    // 0x708e78: StoreField: r0->field_f = r16
    //     0x708e78: stur            w16, [x0, #0xf]
    // 0x708e7c: ldur            x1, [fp, #-0x10]
    // 0x708e80: StoreField: r0->field_13 = r1
    //     0x708e80: stur            w1, [x0, #0x13]
    // 0x708e84: r16 = " This indicates an error which most likely cannot be solved by the library."
    //     0x708e84: add             x16, PP, #0x10, lsl #12  ; [pp+0x10dc8] " This indicates an error which most likely cannot be solved by the library."
    //     0x708e88: ldr             x16, [x16, #0xdc8]
    // 0x708e8c: ArrayStore: r0[0] = r16  ; List_4
    //     0x708e8c: stur            w16, [x0, #0x17]
    // 0x708e90: str             x0, [SP]
    // 0x708e94: r0 = _interpolate()
    //     0x708e94: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x708e98: stur            x0, [fp, #-0x10]
    // 0x708e9c: r0 = DioException()
    //     0x708e9c: bl              #0x7007bc  ; AllocateDioExceptionStub -> DioException (size=0x24)
    // 0x708ea0: stur            x0, [fp, #-0x20]
    // 0x708ea4: r16 = Instance_DioExceptionType
    //     0x708ea4: add             x16, PP, #0x10, lsl #12  ; [pp+0x10dd0] Obj!DioExceptionType@e376c1
    //     0x708ea8: ldr             x16, [x16, #0xdd0]
    // 0x708eac: ldur            lr, [fp, #-0x10]
    // 0x708eb0: stp             lr, x16, [SP, #8]
    // 0x708eb4: str             NULL, [SP]
    // 0x708eb8: mov             x1, x0
    // 0x708ebc: ldur            x2, [fp, #-8]
    // 0x708ec0: ldur            x3, [fp, #-0x18]
    // 0x708ec4: r4 = const [0, 0x6, 0x3, 0x3, message, 0x4, response, 0x5, type, 0x3, null]
    //     0x708ec4: add             x4, PP, #0x10, lsl #12  ; [pp+0x10dd8] List(11) [0, 0x6, 0x3, 0x3, "message", 0x4, "response", 0x5, "type", 0x3, Null]
    //     0x708ec8: ldr             x4, [x4, #0xdd8]
    // 0x708ecc: r0 = DioException()
    //     0x708ecc: bl              #0x70054c  ; [package:dio/src/dio_exception.dart] DioException::DioException
    // 0x708ed0: ldur            x0, [fp, #-0x20]
    // 0x708ed4: LeaveFrame
    //     0x708ed4: mov             SP, fp
    //     0x708ed8: ldp             fp, lr, [SP], #0x10
    // 0x708edc: ret
    //     0x708edc: ret             
    // 0x708ee0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x708ee0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x708ee4: b               #0x708e64
  }
  factory _ DioException.connectionTimeout(/* No info */) {
    // ** addr: 0x708ee8, size: 0xbc
    // 0x708ee8: EnterFrame
    //     0x708ee8: stp             fp, lr, [SP, #-0x10]!
    //     0x708eec: mov             fp, SP
    // 0x708ef0: AllocStack(0x38)
    //     0x708ef0: sub             SP, SP, #0x38
    // 0x708ef4: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0x708ef4: mov             x0, x2
    //     0x708ef8: stur            x2, [fp, #-8]
    //     0x708efc: stur            x3, [fp, #-0x10]
    //     0x708f00: stur            x5, [fp, #-0x18]
    // 0x708f04: CheckStackOverflow
    //     0x708f04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x708f08: cmp             SP, x16
    //     0x708f0c: b.ls            #0x708f9c
    // 0x708f10: r1 = Null
    //     0x708f10: mov             x1, NULL
    // 0x708f14: r2 = 10
    //     0x708f14: movz            x2, #0xa
    // 0x708f18: r0 = AllocateArray()
    //     0x708f18: bl              #0xec22fc  ; AllocateArrayStub
    // 0x708f1c: r16 = "The request connection took longer than "
    //     0x708f1c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10de0] "The request connection took longer than "
    //     0x708f20: ldr             x16, [x16, #0xde0]
    // 0x708f24: StoreField: r0->field_f = r16
    //     0x708f24: stur            w16, [x0, #0xf]
    // 0x708f28: ldur            x1, [fp, #-0x18]
    // 0x708f2c: StoreField: r0->field_13 = r1
    //     0x708f2c: stur            w1, [x0, #0x13]
    // 0x708f30: r16 = " and it was aborted. To get rid of this exception, try raising the RequestOptions.connectTimeout above the duration of "
    //     0x708f30: add             x16, PP, #0x10, lsl #12  ; [pp+0x10de8] " and it was aborted. To get rid of this exception, try raising the RequestOptions.connectTimeout above the duration of "
    //     0x708f34: ldr             x16, [x16, #0xde8]
    // 0x708f38: ArrayStore: r0[0] = r16  ; List_4
    //     0x708f38: stur            w16, [x0, #0x17]
    // 0x708f3c: StoreField: r0->field_1b = r1
    //     0x708f3c: stur            w1, [x0, #0x1b]
    // 0x708f40: r16 = " or improve the response time of the server."
    //     0x708f40: add             x16, PP, #0x10, lsl #12  ; [pp+0x10b20] " or improve the response time of the server."
    //     0x708f44: ldr             x16, [x16, #0xb20]
    // 0x708f48: StoreField: r0->field_1f = r16
    //     0x708f48: stur            w16, [x0, #0x1f]
    // 0x708f4c: str             x0, [SP]
    // 0x708f50: r0 = _interpolate()
    //     0x708f50: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x708f54: stur            x0, [fp, #-0x18]
    // 0x708f58: r0 = DioException()
    //     0x708f58: bl              #0x7007bc  ; AllocateDioExceptionStub -> DioException (size=0x24)
    // 0x708f5c: stur            x0, [fp, #-0x20]
    // 0x708f60: r16 = Instance_DioExceptionType
    //     0x708f60: add             x16, PP, #0x10, lsl #12  ; [pp+0x10df0] Obj!DioExceptionType@e376e1
    //     0x708f64: ldr             x16, [x16, #0xdf0]
    // 0x708f68: stp             NULL, x16, [SP, #8]
    // 0x708f6c: ldur            x16, [fp, #-0x18]
    // 0x708f70: str             x16, [SP]
    // 0x708f74: mov             x1, x0
    // 0x708f78: ldur            x2, [fp, #-8]
    // 0x708f7c: ldur            x3, [fp, #-0x10]
    // 0x708f80: r4 = const [0, 0x6, 0x3, 0x3, message, 0x5, response, 0x4, type, 0x3, null]
    //     0x708f80: add             x4, PP, #0x10, lsl #12  ; [pp+0x105e0] List(11) [0, 0x6, 0x3, 0x3, "message", 0x5, "response", 0x4, "type", 0x3, Null]
    //     0x708f84: ldr             x4, [x4, #0x5e0]
    // 0x708f88: r0 = DioException()
    //     0x708f88: bl              #0x70054c  ; [package:dio/src/dio_exception.dart] DioException::DioException
    // 0x708f8c: ldur            x0, [fp, #-0x20]
    // 0x708f90: LeaveFrame
    //     0x708f90: mov             SP, fp
    //     0x708f94: ldp             fp, lr, [SP], #0x10
    // 0x708f98: ret
    //     0x708f98: ret             
    // 0x708f9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x708f9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x708fa0: b               #0x708f10
  }
  factory _ DioException.sendTimeout(/* No info */) {
    // ** addr: 0x725f94, size: 0xb8
    // 0x725f94: EnterFrame
    //     0x725f94: stp             fp, lr, [SP, #-0x10]!
    //     0x725f98: mov             fp, SP
    // 0x725f9c: AllocStack(0x30)
    //     0x725f9c: sub             SP, SP, #0x30
    // 0x725fa0: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0x725fa0: mov             x0, x2
    //     0x725fa4: stur            x2, [fp, #-8]
    //     0x725fa8: stur            x3, [fp, #-0x10]
    // 0x725fac: CheckStackOverflow
    //     0x725fac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x725fb0: cmp             SP, x16
    //     0x725fb4: b.ls            #0x726044
    // 0x725fb8: r1 = Null
    //     0x725fb8: mov             x1, NULL
    // 0x725fbc: r2 = 10
    //     0x725fbc: movz            x2, #0xa
    // 0x725fc0: r0 = AllocateArray()
    //     0x725fc0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x725fc4: r16 = "The request took longer than "
    //     0x725fc4: add             x16, PP, #0x10, lsl #12  ; [pp+0x10b10] "The request took longer than "
    //     0x725fc8: ldr             x16, [x16, #0xb10]
    // 0x725fcc: StoreField: r0->field_f = r16
    //     0x725fcc: stur            w16, [x0, #0xf]
    // 0x725fd0: ldur            x1, [fp, #-0x10]
    // 0x725fd4: StoreField: r0->field_13 = r1
    //     0x725fd4: stur            w1, [x0, #0x13]
    // 0x725fd8: r16 = " to send data. It was aborted. To get rid of this exception, try raising the RequestOptions.sendTimeout above the duration of "
    //     0x725fd8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10ca0] " to send data. It was aborted. To get rid of this exception, try raising the RequestOptions.sendTimeout above the duration of "
    //     0x725fdc: ldr             x16, [x16, #0xca0]
    // 0x725fe0: ArrayStore: r0[0] = r16  ; List_4
    //     0x725fe0: stur            w16, [x0, #0x17]
    // 0x725fe4: StoreField: r0->field_1b = r1
    //     0x725fe4: stur            w1, [x0, #0x1b]
    // 0x725fe8: r16 = " or improve the response time of the server."
    //     0x725fe8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10b20] " or improve the response time of the server."
    //     0x725fec: ldr             x16, [x16, #0xb20]
    // 0x725ff0: StoreField: r0->field_1f = r16
    //     0x725ff0: stur            w16, [x0, #0x1f]
    // 0x725ff4: str             x0, [SP]
    // 0x725ff8: r0 = _interpolate()
    //     0x725ff8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x725ffc: stur            x0, [fp, #-0x10]
    // 0x726000: r0 = DioException()
    //     0x726000: bl              #0x7007bc  ; AllocateDioExceptionStub -> DioException (size=0x24)
    // 0x726004: stur            x0, [fp, #-0x18]
    // 0x726008: r16 = Instance_DioExceptionType
    //     0x726008: add             x16, PP, #0x10, lsl #12  ; [pp+0x10ca8] Obj!DioExceptionType@e37701
    //     0x72600c: ldr             x16, [x16, #0xca8]
    // 0x726010: stp             NULL, x16, [SP, #8]
    // 0x726014: ldur            x16, [fp, #-0x10]
    // 0x726018: str             x16, [SP]
    // 0x72601c: mov             x1, x0
    // 0x726020: ldur            x3, [fp, #-8]
    // 0x726024: r2 = Null
    //     0x726024: mov             x2, NULL
    // 0x726028: r4 = const [0, 0x6, 0x3, 0x3, message, 0x5, response, 0x4, type, 0x3, null]
    //     0x726028: add             x4, PP, #0x10, lsl #12  ; [pp+0x105e0] List(11) [0, 0x6, 0x3, 0x3, "message", 0x5, "response", 0x4, "type", 0x3, Null]
    //     0x72602c: ldr             x4, [x4, #0x5e0]
    // 0x726030: r0 = DioException()
    //     0x726030: bl              #0x70054c  ; [package:dio/src/dio_exception.dart] DioException::DioException
    // 0x726034: ldur            x0, [fp, #-0x18]
    // 0x726038: LeaveFrame
    //     0x726038: mov             SP, fp
    //     0x72603c: ldp             fp, lr, [SP], #0x10
    // 0x726040: ret
    //     0x726040: ret             
    // 0x726044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x726044: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x726048: b               #0x725fb8
  }
  _ toString(/* No info */) {
    // ** addr: 0xc1c278, size: 0x80
    // 0xc1c278: EnterFrame
    //     0xc1c278: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c27c: mov             fp, SP
    // 0xc1c280: AllocStack(0x40)
    //     0xc1c280: sub             SP, SP, #0x40
    // 0xc1c284: CheckStackOverflow
    //     0xc1c284: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1c288: cmp             SP, x16
    //     0xc1c28c: b.ls            #0xc1c2f0
    // 0xc1c290: r0 = InitLateStaticField(0xd04) // [package:dio/src/dio_exception.dart] DioException::readableStringBuilder
    //     0xc1c290: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc1c294: ldr             x0, [x0, #0x1a08]
    //     0xc1c298: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc1c29c: cmp             w0, w16
    //     0xc1c2a0: b.ne            #0xc1c2b0
    //     0xc1c2a4: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c490] Field <DioException.readableStringBuilder>: static late (offset: 0xd04)
    //     0xc1c2a8: ldr             x2, [x2, #0x490]
    //     0xc1c2ac: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc1c2b0: r0 = Closure: (DioException) => String from Function 'defaultDioExceptionReadableStringBuilder': static.
    //     0xc1c2b0: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c498] Closure: (DioException) => String from Function 'defaultDioExceptionReadableStringBuilder': static. (0x7e54fb61c2f8)
    //     0xc1c2b4: ldr             x0, [x0, #0x498]
    // 0xc1c2b8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xc1c2b8: ldur            w2, [x0, #0x17]
    // 0xc1c2bc: DecompressPointer r2
    //     0xc1c2bc: add             x2, x2, HEAP, lsl #32
    // 0xc1c2c0: ldr             x1, [fp, #0x10]
    // 0xc1c2c4: stur            x2, [fp, #-0x40]
    // 0xc1c2c8: r0 = defaultDioExceptionReadableStringBuilder()
    //     0xc1c2c8: bl              #0xc1c328  ; [package:dio/src/dio_exception.dart] ::defaultDioExceptionReadableStringBuilder
    // 0xc1c2cc: LeaveFrame
    //     0xc1c2cc: mov             SP, fp
    //     0xc1c2d0: ldp             fp, lr, [SP], #0x10
    // 0xc1c2d4: ret
    //     0xc1c2d4: ret             
    // 0xc1c2d8: sub             SP, fp, #0x40
    // 0xc1c2dc: ldr             x1, [fp, #0x10]
    // 0xc1c2e0: r0 = defaultDioExceptionReadableStringBuilder()
    //     0xc1c2e0: bl              #0xc1c328  ; [package:dio/src/dio_exception.dart] ::defaultDioExceptionReadableStringBuilder
    // 0xc1c2e4: LeaveFrame
    //     0xc1c2e4: mov             SP, fp
    //     0xc1c2e8: ldp             fp, lr, [SP], #0x10
    // 0xc1c2ec: ret
    //     0xc1c2ec: ret             
    // 0xc1c2f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1c2f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1c2f4: b               #0xc1c290
  }
  static (dynamic, DioException) => String readableStringBuilder() {
    // ** addr: 0xc1c4c0, size: 0xc
    // 0xc1c4c0: r0 = Closure: (DioException) => String from Function 'defaultDioExceptionReadableStringBuilder': static.
    //     0xc1c4c0: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c498] Closure: (DioException) => String from Function 'defaultDioExceptionReadableStringBuilder': static. (0x7e54fb61c2f8)
    //     0xc1c4c4: ldr             x0, [x0, #0x498]
    // 0xc1c4c8: ret
    //     0xc1c4c8: ret             
  }
}

// class id: 7115, size: 0x14, field offset: 0x14
enum DioExceptionType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc47d84, size: 0x64
    // 0xc47d84: EnterFrame
    //     0xc47d84: stp             fp, lr, [SP, #-0x10]!
    //     0xc47d88: mov             fp, SP
    // 0xc47d8c: AllocStack(0x10)
    //     0xc47d8c: sub             SP, SP, #0x10
    // 0xc47d90: SetupParameters(DioExceptionType this /* r1 => r0, fp-0x8 */)
    //     0xc47d90: mov             x0, x1
    //     0xc47d94: stur            x1, [fp, #-8]
    // 0xc47d98: CheckStackOverflow
    //     0xc47d98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc47d9c: cmp             SP, x16
    //     0xc47da0: b.ls            #0xc47de0
    // 0xc47da4: r1 = Null
    //     0xc47da4: mov             x1, NULL
    // 0xc47da8: r2 = 4
    //     0xc47da8: movz            x2, #0x4
    // 0xc47dac: r0 = AllocateArray()
    //     0xc47dac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc47db0: r16 = "DioExceptionType."
    //     0xc47db0: add             x16, PP, #0x21, lsl #12  ; [pp+0x21eb8] "DioExceptionType."
    //     0xc47db4: ldr             x16, [x16, #0xeb8]
    // 0xc47db8: StoreField: r0->field_f = r16
    //     0xc47db8: stur            w16, [x0, #0xf]
    // 0xc47dbc: ldur            x1, [fp, #-8]
    // 0xc47dc0: LoadField: r2 = r1->field_f
    //     0xc47dc0: ldur            w2, [x1, #0xf]
    // 0xc47dc4: DecompressPointer r2
    //     0xc47dc4: add             x2, x2, HEAP, lsl #32
    // 0xc47dc8: StoreField: r0->field_13 = r2
    //     0xc47dc8: stur            w2, [x0, #0x13]
    // 0xc47dcc: str             x0, [SP]
    // 0xc47dd0: r0 = _interpolate()
    //     0xc47dd0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc47dd4: LeaveFrame
    //     0xc47dd4: mov             SP, fp
    //     0xc47dd8: ldp             fp, lr, [SP], #0x10
    // 0xc47ddc: ret
    //     0xc47ddc: ret             
    // 0xc47de0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc47de0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc47de4: b               #0xc47da4
  }
}
