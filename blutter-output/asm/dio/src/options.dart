// lib: , url: package:dio/src/options.dart

// class id: 1048697, size: 0x8
class :: {

  [closure] static bool _defaultValidateStatus(dynamic, int?) {
    // ** addr: 0x729b74, size: 0x40
    // 0x729b74: ldr             x1, [SP]
    // 0x729b78: cmp             w1, NULL
    // 0x729b7c: b.eq            #0x729bac
    // 0x729b80: r2 = LoadInt32Instr(r1)
    //     0x729b80: sbfx            x2, x1, #1, #0x1f
    //     0x729b84: tbz             w1, #0, #0x729b8c
    //     0x729b88: ldur            x2, [x1, #7]
    // 0x729b8c: cmp             x2, #0xc8
    // 0x729b90: b.lt            #0x729bac
    // 0x729b94: cmp             x2, #0x12c
    // 0x729b98: r16 = true
    //     0x729b98: add             x16, NULL, #0x20  ; true
    // 0x729b9c: r17 = false
    //     0x729b9c: add             x17, NULL, #0x30  ; false
    // 0x729ba0: csel            x1, x16, x17, lt
    // 0x729ba4: mov             x0, x1
    // 0x729ba8: b               #0x729bb0
    // 0x729bac: r0 = false
    //     0x729bac: add             x0, NULL, #0x30  ; false
    // 0x729bb0: ret
    //     0x729bb0: ret             
  }
}

// class id: 5620, size: 0x48, field offset: 0x8
class Options extends Object {

  _ compose(/* No info */) {
    // ** addr: 0x72ad1c, size: 0x440
    // 0x72ad1c: EnterFrame
    //     0x72ad1c: stp             fp, lr, [SP, #-0x10]!
    //     0x72ad20: mov             fp, SP
    // 0x72ad24: AllocStack(0xd8)
    //     0x72ad24: sub             SP, SP, #0xd8
    // 0x72ad28: SetupParameters(Options this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */, dynamic _ /* r5 => r3, fp-0x20 */, dynamic _ /* r6 => r2, fp-0x28 */, dynamic _ /* r7 => r7, fp-0x30 */)
    //     0x72ad28: mov             x4, x1
    //     0x72ad2c: stur            x1, [fp, #-8]
    //     0x72ad30: mov             x1, x2
    //     0x72ad34: mov             x0, x3
    //     0x72ad38: stur            x3, [fp, #-0x18]
    //     0x72ad3c: mov             x3, x5
    //     0x72ad40: stur            x2, [fp, #-0x10]
    //     0x72ad44: mov             x2, x6
    //     0x72ad48: stur            x5, [fp, #-0x20]
    //     0x72ad4c: stur            x6, [fp, #-0x28]
    //     0x72ad50: stur            x7, [fp, #-0x30]
    // 0x72ad54: CheckStackOverflow
    //     0x72ad54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72ad58: cmp             SP, x16
    //     0x72ad5c: b.ls            #0x72b0b8
    // 0x72ad60: r16 = <String, dynamic>
    //     0x72ad60: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x72ad64: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x72ad68: stp             lr, x16, [SP]
    // 0x72ad6c: r0 = Map._fromLiteral()
    //     0x72ad6c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x72ad70: mov             x3, x0
    // 0x72ad74: ldur            x0, [fp, #-0x10]
    // 0x72ad78: stur            x3, [fp, #-0x38]
    // 0x72ad7c: LoadField: r2 = r0->field_4b
    //     0x72ad7c: ldur            w2, [x0, #0x4b]
    // 0x72ad80: DecompressPointer r2
    //     0x72ad80: add             x2, x2, HEAP, lsl #32
    // 0x72ad84: r16 = Sentinel
    //     0x72ad84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72ad88: cmp             w2, w16
    // 0x72ad8c: b.eq            #0x72b0c0
    // 0x72ad90: mov             x1, x3
    // 0x72ad94: r0 = addAll()
    //     0x72ad94: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x72ad98: ldur            x2, [fp, #-0x28]
    // 0x72ad9c: cmp             w2, NULL
    // 0x72ada0: b.eq            #0x72adac
    // 0x72ada4: ldur            x1, [fp, #-0x38]
    // 0x72ada8: r0 = addAll()
    //     0x72ada8: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x72adac: ldur            x1, [fp, #-8]
    // 0x72adb0: ldur            x0, [fp, #-0x10]
    // 0x72adb4: LoadField: r2 = r0->field_b
    //     0x72adb4: ldur            w2, [x0, #0xb]
    // 0x72adb8: DecompressPointer r2
    //     0x72adb8: add             x2, x2, HEAP, lsl #32
    // 0x72adbc: r16 = Sentinel
    //     0x72adbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72adc0: cmp             w2, w16
    // 0x72adc4: b.eq            #0x72b0cc
    // 0x72adc8: stp             x2, NULL, [SP]
    // 0x72adcc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x72adcc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x72add0: r0 = caseInsensitiveKeyMap()
    //     0x72add0: bl              #0x700b44  ; [package:dio/src/utils.dart] ::caseInsensitiveKeyMap
    // 0x72add4: mov             x3, x0
    // 0x72add8: stur            x3, [fp, #-0x28]
    // 0x72addc: r0 = LoadClassIdInstr(r3)
    //     0x72addc: ldur            x0, [x3, #-1]
    //     0x72ade0: ubfx            x0, x0, #0xc, #0x14
    // 0x72ade4: mov             x1, x3
    // 0x72ade8: r2 = "content-type"
    //     0x72ade8: add             x2, PP, #0xf, lsl #12  ; [pp+0xf6a8] "content-type"
    //     0x72adec: ldr             x2, [x2, #0x6a8]
    // 0x72adf0: r0 = GDT[cid_x0 + -0x114]()
    //     0x72adf0: sub             lr, x0, #0x114
    //     0x72adf4: ldr             lr, [x21, lr, lsl #3]
    //     0x72adf8: blr             lr
    // 0x72adfc: mov             x3, x0
    // 0x72ae00: r2 = Null
    //     0x72ae00: mov             x2, NULL
    // 0x72ae04: r1 = Null
    //     0x72ae04: mov             x1, NULL
    // 0x72ae08: stur            x3, [fp, #-0x40]
    // 0x72ae0c: r4 = 60
    //     0x72ae0c: movz            x4, #0x3c
    // 0x72ae10: branchIfSmi(r0, 0x72ae1c)
    //     0x72ae10: tbz             w0, #0, #0x72ae1c
    // 0x72ae14: r4 = LoadClassIdInstr(r0)
    //     0x72ae14: ldur            x4, [x0, #-1]
    //     0x72ae18: ubfx            x4, x4, #0xc, #0x14
    // 0x72ae1c: sub             x4, x4, #0x5e
    // 0x72ae20: cmp             x4, #1
    // 0x72ae24: b.ls            #0x72ae38
    // 0x72ae28: r8 = String?
    //     0x72ae28: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x72ae2c: r3 = Null
    //     0x72ae2c: add             x3, PP, #0x12, lsl #12  ; [pp+0x12410] Null
    //     0x72ae30: ldr             x3, [x3, #0x410]
    // 0x72ae34: r0 = String?()
    //     0x72ae34: bl              #0x600324  ; IsType_String?_Stub
    // 0x72ae38: ldur            x0, [fp, #-0x10]
    // 0x72ae3c: LoadField: r2 = r0->field_2b
    //     0x72ae3c: ldur            w2, [x0, #0x2b]
    // 0x72ae40: DecompressPointer r2
    //     0x72ae40: add             x2, x2, HEAP, lsl #32
    // 0x72ae44: r16 = Sentinel
    //     0x72ae44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72ae48: cmp             w2, w16
    // 0x72ae4c: b.eq            #0x72b0d8
    // 0x72ae50: r1 = <String, dynamic>
    //     0x72ae50: ldr             x1, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x72ae54: r0 = LinkedHashMap.from()
    //     0x72ae54: bl              #0x63c6f0  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0x72ae58: mov             x1, x0
    // 0x72ae5c: ldur            x0, [fp, #-8]
    // 0x72ae60: stur            x1, [fp, #-0x48]
    // 0x72ae64: LoadField: r2 = r0->field_7
    //     0x72ae64: ldur            w2, [x0, #7]
    // 0x72ae68: DecompressPointer r2
    //     0x72ae68: add             x2, x2, HEAP, lsl #32
    // 0x72ae6c: cmp             w2, NULL
    // 0x72ae70: b.ne            #0x72ae90
    // 0x72ae74: ldur            x3, [fp, #-0x10]
    // 0x72ae78: LoadField: r0 = r3->field_7
    //     0x72ae78: ldur            w0, [x3, #7]
    // 0x72ae7c: DecompressPointer r0
    //     0x72ae7c: add             x0, x0, HEAP, lsl #32
    // 0x72ae80: r16 = Sentinel
    //     0x72ae80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72ae84: cmp             w0, w16
    // 0x72ae88: b.eq            #0x72b0e4
    // 0x72ae8c: b               #0x72ae98
    // 0x72ae90: ldur            x3, [fp, #-0x10]
    // 0x72ae94: mov             x0, x2
    // 0x72ae98: ldur            x2, [fp, #-0x40]
    // 0x72ae9c: r4 = LoadClassIdInstr(r0)
    //     0x72ae9c: ldur            x4, [x0, #-1]
    //     0x72aea0: ubfx            x4, x4, #0xc, #0x14
    // 0x72aea4: str             x0, [SP]
    // 0x72aea8: mov             x0, x4
    // 0x72aeac: r0 = GDT[cid_x0 + -0xff6]()
    //     0x72aeac: sub             lr, x0, #0xff6
    //     0x72aeb0: ldr             lr, [x21, lr, lsl #3]
    //     0x72aeb4: blr             lr
    // 0x72aeb8: mov             x3, x0
    // 0x72aebc: ldur            x0, [fp, #-0x10]
    // 0x72aec0: stur            x3, [fp, #-0x68]
    // 0x72aec4: LoadField: r4 = r0->field_47
    //     0x72aec4: ldur            w4, [x0, #0x47]
    // 0x72aec8: DecompressPointer r4
    //     0x72aec8: add             x4, x4, HEAP, lsl #32
    // 0x72aecc: r16 = Sentinel
    //     0x72aecc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72aed0: cmp             w4, w16
    // 0x72aed4: b.eq            #0x72b0f0
    // 0x72aed8: stur            x4, [fp, #-0x60]
    // 0x72aedc: LoadField: r1 = r0->field_f
    //     0x72aedc: ldur            w1, [x0, #0xf]
    // 0x72aee0: DecompressPointer r1
    //     0x72aee0: add             x1, x1, HEAP, lsl #32
    // 0x72aee4: r16 = Sentinel
    //     0x72aee4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72aee8: cmp             w1, w16
    // 0x72aeec: b.eq            #0x72b0fc
    // 0x72aef0: LoadField: r5 = r0->field_13
    //     0x72aef0: ldur            w5, [x0, #0x13]
    // 0x72aef4: DecompressPointer r5
    //     0x72aef4: add             x5, x5, HEAP, lsl #32
    // 0x72aef8: stur            x5, [fp, #-0x58]
    // 0x72aefc: ArrayLoad: r6 = r0[0]  ; List_4
    //     0x72aefc: ldur            w6, [x0, #0x17]
    // 0x72af00: DecompressPointer r6
    //     0x72af00: add             x6, x6, HEAP, lsl #32
    // 0x72af04: stur            x6, [fp, #-0x50]
    // 0x72af08: LoadField: r7 = r0->field_1f
    //     0x72af08: ldur            w7, [x0, #0x1f]
    // 0x72af0c: DecompressPointer r7
    //     0x72af0c: add             x7, x7, HEAP, lsl #32
    // 0x72af10: r16 = Sentinel
    //     0x72af10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72af14: cmp             w7, w16
    // 0x72af18: b.eq            #0x72b108
    // 0x72af1c: stur            x7, [fp, #-8]
    // 0x72af20: LoadField: r1 = r0->field_23
    //     0x72af20: ldur            w1, [x0, #0x23]
    // 0x72af24: DecompressPointer r1
    //     0x72af24: add             x1, x1, HEAP, lsl #32
    // 0x72af28: r16 = Sentinel
    //     0x72af28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72af2c: cmp             w1, w16
    // 0x72af30: b.eq            #0x72b114
    // 0x72af34: LoadField: r1 = r0->field_27
    //     0x72af34: ldur            w1, [x0, #0x27]
    // 0x72af38: DecompressPointer r1
    //     0x72af38: add             x1, x1, HEAP, lsl #32
    // 0x72af3c: r16 = Sentinel
    //     0x72af3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72af40: cmp             w1, w16
    // 0x72af44: b.eq            #0x72b120
    // 0x72af48: LoadField: r1 = r0->field_2f
    //     0x72af48: ldur            w1, [x0, #0x2f]
    // 0x72af4c: DecompressPointer r1
    //     0x72af4c: add             x1, x1, HEAP, lsl #32
    // 0x72af50: r16 = Sentinel
    //     0x72af50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72af54: cmp             w1, w16
    // 0x72af58: b.eq            #0x72b12c
    // 0x72af5c: LoadField: r1 = r0->field_33
    //     0x72af5c: ldur            w1, [x0, #0x33]
    // 0x72af60: DecompressPointer r1
    //     0x72af60: add             x1, x1, HEAP, lsl #32
    // 0x72af64: r16 = Sentinel
    //     0x72af64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72af68: cmp             w1, w16
    // 0x72af6c: b.eq            #0x72b138
    // 0x72af70: LoadField: r1 = r0->field_37
    //     0x72af70: ldur            w1, [x0, #0x37]
    // 0x72af74: DecompressPointer r1
    //     0x72af74: add             x1, x1, HEAP, lsl #32
    // 0x72af78: r16 = Sentinel
    //     0x72af78: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72af7c: cmp             w1, w16
    // 0x72af80: b.eq            #0x72b144
    // 0x72af84: LoadField: r1 = r0->field_43
    //     0x72af84: ldur            w1, [x0, #0x43]
    // 0x72af88: DecompressPointer r1
    //     0x72af88: add             x1, x1, HEAP, lsl #32
    // 0x72af8c: r16 = Sentinel
    //     0x72af8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x72af90: cmp             w1, w16
    // 0x72af94: b.eq            #0x72b150
    // 0x72af98: ldur            x1, [fp, #-0x40]
    // 0x72af9c: cmp             w1, NULL
    // 0x72afa0: b.ne            #0x72afa8
    // 0x72afa4: r1 = Null
    //     0x72afa4: mov             x1, NULL
    // 0x72afa8: cmp             w1, NULL
    // 0x72afac: b.ne            #0x72b018
    // 0x72afb0: LoadField: r1 = r0->field_b
    //     0x72afb0: ldur            w1, [x0, #0xb]
    // 0x72afb4: DecompressPointer r1
    //     0x72afb4: add             x1, x1, HEAP, lsl #32
    // 0x72afb8: r0 = LoadClassIdInstr(r1)
    //     0x72afb8: ldur            x0, [x1, #-1]
    //     0x72afbc: ubfx            x0, x0, #0xc, #0x14
    // 0x72afc0: r2 = "content-type"
    //     0x72afc0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf6a8] "content-type"
    //     0x72afc4: ldr             x2, [x2, #0x6a8]
    // 0x72afc8: r0 = GDT[cid_x0 + -0x114]()
    //     0x72afc8: sub             lr, x0, #0x114
    //     0x72afcc: ldr             lr, [x21, lr, lsl #3]
    //     0x72afd0: blr             lr
    // 0x72afd4: mov             x3, x0
    // 0x72afd8: r2 = Null
    //     0x72afd8: mov             x2, NULL
    // 0x72afdc: r1 = Null
    //     0x72afdc: mov             x1, NULL
    // 0x72afe0: stur            x3, [fp, #-0x10]
    // 0x72afe4: r4 = 60
    //     0x72afe4: movz            x4, #0x3c
    // 0x72afe8: branchIfSmi(r0, 0x72aff4)
    //     0x72afe8: tbz             w0, #0, #0x72aff4
    // 0x72afec: r4 = LoadClassIdInstr(r0)
    //     0x72afec: ldur            x4, [x0, #-1]
    //     0x72aff0: ubfx            x4, x4, #0xc, #0x14
    // 0x72aff4: sub             x4, x4, #0x5e
    // 0x72aff8: cmp             x4, #1
    // 0x72affc: b.ls            #0x72b010
    // 0x72b000: r8 = String?
    //     0x72b000: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x72b004: r3 = Null
    //     0x72b004: add             x3, PP, #0x12, lsl #12  ; [pp+0x12420] Null
    //     0x72b008: ldr             x3, [x3, #0x420]
    // 0x72b00c: r0 = String?()
    //     0x72b00c: bl              #0x600324  ; IsType_String?_Stub
    // 0x72b010: ldur            x0, [fp, #-0x10]
    // 0x72b014: b               #0x72b01c
    // 0x72b018: mov             x0, x1
    // 0x72b01c: stur            x0, [fp, #-0x10]
    // 0x72b020: r0 = RequestOptions()
    //     0x72b020: bl              #0x729bb4  ; AllocateRequestOptionsStub -> RequestOptions (size=0x6c)
    // 0x72b024: stur            x0, [fp, #-0x40]
    // 0x72b028: ldur            x16, [fp, #-0x38]
    // 0x72b02c: ldur            lr, [fp, #-0x50]
    // 0x72b030: stp             lr, x16, [SP, #0x60]
    // 0x72b034: ldur            x16, [fp, #-0x58]
    // 0x72b038: ldur            lr, [fp, #-0x48]
    // 0x72b03c: stp             lr, x16, [SP, #0x50]
    // 0x72b040: r16 = false
    //     0x72b040: add             x16, NULL, #0x30  ; false
    // 0x72b044: ldur            lr, [fp, #-0x30]
    // 0x72b048: stp             lr, x16, [SP, #0x40]
    // 0x72b04c: ldur            x16, [fp, #-8]
    // 0x72b050: r30 = Closure: (int?) => bool from Function '_defaultValidateStatus@868184022': static.
    //     0x72b050: add             lr, PP, #0xf, lsl #12  ; [pp+0xf5c8] Closure: (int?) => bool from Function '_defaultValidateStatus@868184022': static. (0x7e54fb129b74)
    //     0x72b054: ldr             lr, [lr, #0x5c8]
    // 0x72b058: stp             lr, x16, [SP, #0x30]
    // 0x72b05c: r16 = true
    //     0x72b05c: add             x16, NULL, #0x20  ; true
    // 0x72b060: r30 = true
    //     0x72b060: add             lr, NULL, #0x20  ; true
    // 0x72b064: stp             lr, x16, [SP, #0x20]
    // 0x72b068: r16 = 10
    //     0x72b068: movz            x16, #0xa
    // 0x72b06c: r30 = true
    //     0x72b06c: add             lr, NULL, #0x20  ; true
    // 0x72b070: stp             lr, x16, [SP, #0x10]
    // 0x72b074: r16 = Instance_ListFormat
    //     0x72b074: add             x16, PP, #0xf, lsl #12  ; [pp+0xf5d0] Obj!ListFormat@e37521
    //     0x72b078: ldr             x16, [x16, #0x5d0]
    // 0x72b07c: ldur            lr, [fp, #-0x10]
    // 0x72b080: stp             lr, x16, [SP]
    // 0x72b084: mov             x1, x0
    // 0x72b088: ldur            x2, [fp, #-0x60]
    // 0x72b08c: ldur            x3, [fp, #-0x20]
    // 0x72b090: ldur            x5, [fp, #-0x28]
    // 0x72b094: ldur            x6, [fp, #-0x68]
    // 0x72b098: ldur            x7, [fp, #-0x18]
    // 0x72b09c: r4 = const [0, 0x14, 0xe, 0x9, contentType, 0x13, extra, 0x9, followRedirects, 0xf, listFormat, 0x12, maxRedirects, 0x10, persistentConnection, 0x11, preserveHeaderCase, 0xa, receiveDataWhenStatusError, 0xe, responseType, 0xc, sourceStackTrace, 0xb, validateStatus, 0xd, null]
    //     0x72b09c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12430] List(27) [0, 0x14, 0xe, 0x9, "contentType", 0x13, "extra", 0x9, "followRedirects", 0xf, "listFormat", 0x12, "maxRedirects", 0x10, "persistentConnection", 0x11, "preserveHeaderCase", 0xa, "receiveDataWhenStatusError", 0xe, "responseType", 0xc, "sourceStackTrace", 0xb, "validateStatus", 0xd, Null]
    //     0x72b0a0: ldr             x4, [x4, #0x430]
    // 0x72b0a4: r0 = RequestOptions()
    //     0x72b0a4: bl              #0x728fd8  ; [package:dio/src/options.dart] RequestOptions::RequestOptions
    // 0x72b0a8: ldur            x0, [fp, #-0x40]
    // 0x72b0ac: LeaveFrame
    //     0x72b0ac: mov             SP, fp
    //     0x72b0b0: ldp             fp, lr, [SP], #0x10
    // 0x72b0b4: ret
    //     0x72b0b4: ret             
    // 0x72b0b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72b0b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72b0bc: b               #0x72ad60
    // 0x72b0c0: r9 = queryParameters
    //     0x72b0c0: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5e8] Field <_BaseOptions&_RequestConfig&<EMAIL>>: late (offset: 0x4c)
    //     0x72b0c4: ldr             x9, [x9, #0x5e8]
    // 0x72b0c8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b0c8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x72b0cc: r9 = _headers
    //     0x72b0cc: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5c0] Field <_RequestConfig@868184022._headers@868184022>: late (offset: 0xc)
    //     0x72b0d0: ldr             x9, [x9, #0x5c0]
    // 0x72b0d4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b0d4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x72b0d8: r9 = extra
    //     0x72b0d8: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5f0] Field <<EMAIL>>: late (offset: 0x2c)
    //     0x72b0dc: ldr             x9, [x9, #0x5f0]
    // 0x72b0e0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b0e0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x72b0e4: r9 = method
    //     0x72b0e4: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5e0] Field <<EMAIL>>: late (offset: 0x8)
    //     0x72b0e8: ldr             x9, [x9, #0x5e0]
    // 0x72b0ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b0ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x72b0f0: r9 = _baseUrl
    //     0x72b0f0: add             x9, PP, #0x11, lsl #12  ; [pp+0x11ff0] Field <_BaseOptions&_RequestConfig&OptionsMixin@868184022._baseUrl@868184022>: late (offset: 0x48)
    //     0x72b0f4: ldr             x9, [x9, #0xff0]
    // 0x72b0f8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b0f8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x72b0fc: r9 = preserveHeaderCase
    //     0x72b0fc: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5f8] Field <<EMAIL>>: late (offset: 0x10)
    //     0x72b100: ldr             x9, [x9, #0x5f8]
    // 0x72b104: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b104: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x72b108: r9 = responseType
    //     0x72b108: add             x9, PP, #0xf, lsl #12  ; [pp+0xf600] Field <<EMAIL>>: late (offset: 0x20)
    //     0x72b10c: ldr             x9, [x9, #0x600]
    // 0x72b110: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b110: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x72b114: r9 = validateStatus
    //     0x72b114: add             x9, PP, #0xf, lsl #12  ; [pp+0xf608] Field <<EMAIL>>: late (offset: 0x24)
    //     0x72b118: ldr             x9, [x9, #0x608]
    // 0x72b11c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b11c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x72b120: r9 = receiveDataWhenStatusError
    //     0x72b120: add             x9, PP, #0xf, lsl #12  ; [pp+0xf610] Field <<EMAIL>>: late (offset: 0x28)
    //     0x72b124: ldr             x9, [x9, #0x610]
    // 0x72b128: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b128: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x72b12c: r9 = followRedirects
    //     0x72b12c: add             x9, PP, #0xf, lsl #12  ; [pp+0xf618] Field <<EMAIL>>: late (offset: 0x30)
    //     0x72b130: ldr             x9, [x9, #0x618]
    // 0x72b134: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b134: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x72b138: r9 = maxRedirects
    //     0x72b138: add             x9, PP, #0xf, lsl #12  ; [pp+0xf620] Field <<EMAIL>>: late (offset: 0x34)
    //     0x72b13c: ldr             x9, [x9, #0x620]
    // 0x72b140: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b140: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x72b144: r9 = persistentConnection
    //     0x72b144: add             x9, PP, #0xf, lsl #12  ; [pp+0xf628] Field <<EMAIL>>: late (offset: 0x38)
    //     0x72b148: ldr             x9, [x9, #0x628]
    // 0x72b14c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b14c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x72b150: r9 = listFormat
    //     0x72b150: add             x9, PP, #0xf, lsl #12  ; [pp+0xf630] Field <<EMAIL>>: late (offset: 0x44)
    //     0x72b154: ldr             x9, [x9, #0x630]
    // 0x72b158: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72b158: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 5621, size: 0x48, field offset: 0x8
abstract class _RequestConfig extends Object {

  late Map<String, dynamic> _headers; // offset: 0xc
  late String method; // offset: 0x8
  late Map<String, dynamic> extra; // offset: 0x2c
  late bool preserveHeaderCase; // offset: 0x10
  late ResponseType responseType; // offset: 0x20
  late (dynamic, int?) => bool validateStatus; // offset: 0x24
  late bool receiveDataWhenStatusError; // offset: 0x28
  late bool followRedirects; // offset: 0x30
  late int maxRedirects; // offset: 0x34
  late bool persistentConnection; // offset: 0x38
  late ListFormat listFormat; // offset: 0x44

  get _ contentType(/* No info */) {
    // ** addr: 0x72752c, size: 0xb4
    // 0x72752c: EnterFrame
    //     0x72752c: stp             fp, lr, [SP, #-0x10]!
    //     0x727530: mov             fp, SP
    // 0x727534: AllocStack(0x8)
    //     0x727534: sub             SP, SP, #8
    // 0x727538: CheckStackOverflow
    //     0x727538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72753c: cmp             SP, x16
    //     0x727540: b.ls            #0x7275cc
    // 0x727544: LoadField: r0 = r1->field_b
    //     0x727544: ldur            w0, [x1, #0xb]
    // 0x727548: DecompressPointer r0
    //     0x727548: add             x0, x0, HEAP, lsl #32
    // 0x72754c: r16 = Sentinel
    //     0x72754c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x727550: cmp             w0, w16
    // 0x727554: b.eq            #0x7275d4
    // 0x727558: r1 = LoadClassIdInstr(r0)
    //     0x727558: ldur            x1, [x0, #-1]
    //     0x72755c: ubfx            x1, x1, #0xc, #0x14
    // 0x727560: mov             x16, x0
    // 0x727564: mov             x0, x1
    // 0x727568: mov             x1, x16
    // 0x72756c: r2 = "content-type"
    //     0x72756c: add             x2, PP, #0xf, lsl #12  ; [pp+0xf6a8] "content-type"
    //     0x727570: ldr             x2, [x2, #0x6a8]
    // 0x727574: r0 = GDT[cid_x0 + -0x114]()
    //     0x727574: sub             lr, x0, #0x114
    //     0x727578: ldr             lr, [x21, lr, lsl #3]
    //     0x72757c: blr             lr
    // 0x727580: mov             x3, x0
    // 0x727584: r2 = Null
    //     0x727584: mov             x2, NULL
    // 0x727588: r1 = Null
    //     0x727588: mov             x1, NULL
    // 0x72758c: stur            x3, [fp, #-8]
    // 0x727590: r4 = 60
    //     0x727590: movz            x4, #0x3c
    // 0x727594: branchIfSmi(r0, 0x7275a0)
    //     0x727594: tbz             w0, #0, #0x7275a0
    // 0x727598: r4 = LoadClassIdInstr(r0)
    //     0x727598: ldur            x4, [x0, #-1]
    //     0x72759c: ubfx            x4, x4, #0xc, #0x14
    // 0x7275a0: sub             x4, x4, #0x5e
    // 0x7275a4: cmp             x4, #1
    // 0x7275a8: b.ls            #0x7275bc
    // 0x7275ac: r8 = String?
    //     0x7275ac: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7275b0: r3 = Null
    //     0x7275b0: add             x3, PP, #0xf, lsl #12  ; [pp+0xf720] Null
    //     0x7275b4: ldr             x3, [x3, #0x720]
    // 0x7275b8: r0 = String?()
    //     0x7275b8: bl              #0x600324  ; IsType_String?_Stub
    // 0x7275bc: ldur            x0, [fp, #-8]
    // 0x7275c0: LeaveFrame
    //     0x7275c0: mov             SP, fp
    //     0x7275c4: ldp             fp, lr, [SP], #0x10
    // 0x7275c8: ret
    //     0x7275c8: ret             
    // 0x7275cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7275cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7275d0: b               #0x727544
    // 0x7275d4: r9 = _headers
    //     0x7275d4: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5c0] Field <_RequestConfig@868184022._headers@868184022>: late (offset: 0xc)
    //     0x7275d8: ldr             x9, [x9, #0x5c0]
    // 0x7275dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7275dc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _RequestConfig(/* No info */) {
    // ** addr: 0x729660, size: 0x33c
    // 0x729660: EnterFrame
    //     0x729660: stp             fp, lr, [SP, #-0x10]!
    //     0x729664: mov             fp, SP
    // 0x729668: AllocStack(0x30)
    //     0x729668: sub             SP, SP, #0x30
    // 0x72966c: r0 = Sentinel
    //     0x72966c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729670: mov             x4, x1
    // 0x729674: stur            x1, [fp, #-8]
    // 0x729678: mov             x1, x2
    // 0x72967c: stur            x2, [fp, #-0x10]
    // 0x729680: mov             x2, x6
    // 0x729684: stur            x5, [fp, #-0x18]
    // 0x729688: stur            x6, [fp, #-0x20]
    // 0x72968c: CheckStackOverflow
    //     0x72968c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x729690: cmp             SP, x16
    //     0x729694: b.ls            #0x729988
    // 0x729698: StoreField: r4->field_b = r0
    //     0x729698: stur            w0, [x4, #0xb]
    // 0x72969c: ldr             x0, [fp, #0x28]
    // 0x7296a0: ArrayStore: r4[0] = r0  ; List_4
    //     0x7296a0: stur            w0, [x4, #0x17]
    //     0x7296a4: ldurb           w16, [x4, #-1]
    //     0x7296a8: ldurb           w17, [x0, #-1]
    //     0x7296ac: and             x16, x17, x16, lsr #2
    //     0x7296b0: tst             x16, HEAP, lsr #32
    //     0x7296b4: b.eq            #0x7296bc
    //     0x7296b8: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7296bc: ldr             x0, [fp, #0x18]
    // 0x7296c0: StoreField: r4->field_13 = r0
    //     0x7296c0: stur            w0, [x4, #0x13]
    //     0x7296c4: ldurb           w16, [x4, #-1]
    //     0x7296c8: ldurb           w17, [x0, #-1]
    //     0x7296cc: and             x16, x17, x16, lsr #2
    //     0x7296d0: tst             x16, HEAP, lsr #32
    //     0x7296d4: b.eq            #0x7296dc
    //     0x7296d8: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7296dc: ldr             x0, [fp, #0x48]
    // 0x7296e0: cmp             w0, NULL
    // 0x7296e4: b.ne            #0x7296f0
    // 0x7296e8: r0 = "GET"
    //     0x7296e8: add             x0, PP, #0xf, lsl #12  ; [pp+0xf6a0] "GET"
    //     0x7296ec: ldr             x0, [x0, #0x6a0]
    // 0x7296f0: ldr             x6, [fp, #0x38]
    // 0x7296f4: StoreField: r4->field_7 = r0
    //     0x7296f4: stur            w0, [x4, #7]
    //     0x7296f8: ldurb           w16, [x4, #-1]
    //     0x7296fc: ldurb           w17, [x0, #-1]
    //     0x729700: and             x16, x17, x16, lsr #2
    //     0x729704: tst             x16, HEAP, lsr #32
    //     0x729708: b.eq            #0x729710
    //     0x72970c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x729710: cmp             w6, NULL
    // 0x729714: b.ne            #0x729720
    // 0x729718: r0 = false
    //     0x729718: add             x0, NULL, #0x30  ; false
    // 0x72971c: b               #0x729724
    // 0x729720: mov             x0, x6
    // 0x729724: StoreField: r4->field_f = r0
    //     0x729724: stur            w0, [x4, #0xf]
    // 0x729728: cmp             w7, NULL
    // 0x72972c: b.ne            #0x72973c
    // 0x729730: r0 = Instance_ListFormat
    //     0x729730: add             x0, PP, #0xf, lsl #12  ; [pp+0xf5d0] Obj!ListFormat@e37521
    //     0x729734: ldr             x0, [x0, #0x5d0]
    // 0x729738: b               #0x729740
    // 0x72973c: mov             x0, x7
    // 0x729740: StoreField: r4->field_43 = r0
    //     0x729740: stur            w0, [x4, #0x43]
    //     0x729744: ldurb           w16, [x4, #-1]
    //     0x729748: ldurb           w17, [x0, #-1]
    //     0x72974c: and             x16, x17, x16, lsr #2
    //     0x729750: tst             x16, HEAP, lsr #32
    //     0x729754: b.eq            #0x72975c
    //     0x729758: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x72975c: cmp             w3, NULL
    // 0x729760: b.ne            #0x729778
    // 0x729764: r16 = <String, dynamic>
    //     0x729764: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x729768: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x72976c: stp             lr, x16, [SP]
    // 0x729770: r0 = Map._fromLiteral()
    //     0x729770: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x729774: b               #0x72977c
    // 0x729778: mov             x0, x3
    // 0x72977c: ldur            x3, [fp, #-8]
    // 0x729780: ldur            x1, [fp, #-0x18]
    // 0x729784: StoreField: r3->field_2b = r0
    //     0x729784: stur            w0, [x3, #0x2b]
    //     0x729788: tbz             w0, #0, #0x7297a4
    //     0x72978c: ldurb           w16, [x3, #-1]
    //     0x729790: ldurb           w17, [x0, #-1]
    //     0x729794: and             x16, x17, x16, lsr #2
    //     0x729798: tst             x16, HEAP, lsr #32
    //     0x72979c: b.eq            #0x7297a4
    //     0x7297a0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7297a4: cmp             w1, NULL
    // 0x7297a8: b.ne            #0x7297b0
    // 0x7297ac: r1 = true
    //     0x7297ac: add             x1, NULL, #0x20  ; true
    // 0x7297b0: ldr             x0, [fp, #0x50]
    // 0x7297b4: StoreField: r3->field_2f = r1
    //     0x7297b4: stur            w1, [x3, #0x2f]
    // 0x7297b8: cmp             w0, NULL
    // 0x7297bc: b.ne            #0x7297c8
    // 0x7297c0: r1 = 5
    //     0x7297c0: movz            x1, #0x5
    // 0x7297c4: b               #0x7297cc
    // 0x7297c8: r1 = LoadInt32Instr(r0)
    //     0x7297c8: sbfx            x1, x0, #1, #0x1f
    // 0x7297cc: ldr             x0, [fp, #0x40]
    // 0x7297d0: lsl             x2, x1, #1
    // 0x7297d4: StoreField: r3->field_33 = r2
    //     0x7297d4: stur            w2, [x3, #0x33]
    // 0x7297d8: cmp             w0, NULL
    // 0x7297dc: b.ne            #0x7297e8
    // 0x7297e0: r1 = true
    //     0x7297e0: add             x1, NULL, #0x20  ; true
    // 0x7297e4: b               #0x7297ec
    // 0x7297e8: mov             x1, x0
    // 0x7297ec: ldr             x0, [fp, #0x30]
    // 0x7297f0: StoreField: r3->field_37 = r1
    //     0x7297f0: stur            w1, [x3, #0x37]
    // 0x7297f4: cmp             w0, NULL
    // 0x7297f8: b.ne            #0x729804
    // 0x7297fc: r1 = true
    //     0x7297fc: add             x1, NULL, #0x20  ; true
    // 0x729800: b               #0x729808
    // 0x729804: mov             x1, x0
    // 0x729808: ldr             x0, [fp, #0x10]
    // 0x72980c: StoreField: r3->field_27 = r1
    //     0x72980c: stur            w1, [x3, #0x27]
    // 0x729810: cmp             w0, NULL
    // 0x729814: b.ne            #0x729820
    // 0x729818: r0 = Closure: (int?) => bool from Function '_defaultValidateStatus@868184022': static.
    //     0x729818: add             x0, PP, #0xf, lsl #12  ; [pp+0xf5c8] Closure: (int?) => bool from Function '_defaultValidateStatus@868184022': static. (0x7e54fb129b74)
    //     0x72981c: ldr             x0, [x0, #0x5c8]
    // 0x729820: ldr             x1, [fp, #0x20]
    // 0x729824: StoreField: r3->field_23 = r0
    //     0x729824: stur            w0, [x3, #0x23]
    //     0x729828: ldurb           w16, [x3, #-1]
    //     0x72982c: ldurb           w17, [x0, #-1]
    //     0x729830: and             x16, x17, x16, lsr #2
    //     0x729834: tst             x16, HEAP, lsr #32
    //     0x729838: b.eq            #0x729840
    //     0x72983c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x729840: cmp             w1, NULL
    // 0x729844: b.ne            #0x729854
    // 0x729848: r0 = Instance_ResponseType
    //     0x729848: add             x0, PP, #0xf, lsl #12  ; [pp+0xf680] Obj!ResponseType@e375a1
    //     0x72984c: ldr             x0, [x0, #0x680]
    // 0x729850: b               #0x729858
    // 0x729854: mov             x0, x1
    // 0x729858: ldur            x4, [fp, #-0x10]
    // 0x72985c: StoreField: r3->field_1f = r0
    //     0x72985c: stur            w0, [x3, #0x1f]
    //     0x729860: ldurb           w16, [x3, #-1]
    //     0x729864: ldurb           w17, [x0, #-1]
    //     0x729868: and             x16, x17, x16, lsr #2
    //     0x72986c: tst             x16, HEAP, lsr #32
    //     0x729870: b.eq            #0x729878
    //     0x729874: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x729878: mov             x1, x3
    // 0x72987c: ldur            x2, [fp, #-0x20]
    // 0x729880: r0 = headers=()
    //     0x729880: bl              #0x729ab8  ; [package:dio/src/options.dart] _RequestConfig::headers=
    // 0x729884: ldur            x3, [fp, #-8]
    // 0x729888: LoadField: r1 = r3->field_b
    //     0x729888: ldur            w1, [x3, #0xb]
    // 0x72988c: DecompressPointer r1
    //     0x72988c: add             x1, x1, HEAP, lsl #32
    // 0x729890: r16 = Sentinel
    //     0x729890: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729894: cmp             w1, w16
    // 0x729898: b.eq            #0x729990
    // 0x72989c: r0 = LoadClassIdInstr(r1)
    //     0x72989c: ldur            x0, [x1, #-1]
    //     0x7298a0: ubfx            x0, x0, #0xc, #0x14
    // 0x7298a4: r2 = "content-type"
    //     0x7298a4: add             x2, PP, #0xf, lsl #12  ; [pp+0xf6a8] "content-type"
    //     0x7298a8: ldr             x2, [x2, #0x6a8]
    // 0x7298ac: r0 = GDT[cid_x0 + 0x55f]()
    //     0x7298ac: add             lr, x0, #0x55f
    //     0x7298b0: ldr             lr, [x21, lr, lsl #3]
    //     0x7298b4: blr             lr
    // 0x7298b8: mov             x4, x0
    // 0x7298bc: ldur            x3, [fp, #-0x10]
    // 0x7298c0: stur            x4, [fp, #-0x18]
    // 0x7298c4: cmp             w3, NULL
    // 0x7298c8: b.eq            #0x729924
    // 0x7298cc: tbnz            w4, #4, #0x729924
    // 0x7298d0: ldur            x5, [fp, #-8]
    // 0x7298d4: LoadField: r1 = r5->field_b
    //     0x7298d4: ldur            w1, [x5, #0xb]
    // 0x7298d8: DecompressPointer r1
    //     0x7298d8: add             x1, x1, HEAP, lsl #32
    // 0x7298dc: r0 = LoadClassIdInstr(r1)
    //     0x7298dc: ldur            x0, [x1, #-1]
    //     0x7298e0: ubfx            x0, x0, #0xc, #0x14
    // 0x7298e4: r2 = "content-type"
    //     0x7298e4: add             x2, PP, #0xf, lsl #12  ; [pp+0xf6a8] "content-type"
    //     0x7298e8: ldr             x2, [x2, #0x6a8]
    // 0x7298ec: r0 = GDT[cid_x0 + -0x114]()
    //     0x7298ec: sub             lr, x0, #0x114
    //     0x7298f0: ldr             lr, [x21, lr, lsl #3]
    //     0x7298f4: blr             lr
    // 0x7298f8: r1 = 60
    //     0x7298f8: movz            x1, #0x3c
    // 0x7298fc: branchIfSmi(r0, 0x729908)
    //     0x7298fc: tbz             w0, #0, #0x729908
    // 0x729900: r1 = LoadClassIdInstr(r0)
    //     0x729900: ldur            x1, [x0, #-1]
    //     0x729904: ubfx            x1, x1, #0xc, #0x14
    // 0x729908: ldur            x16, [fp, #-0x10]
    // 0x72990c: stp             x16, x0, [SP]
    // 0x729910: mov             x0, x1
    // 0x729914: mov             lr, x0
    // 0x729918: ldr             lr, [x21, lr, lsl #3]
    // 0x72991c: blr             lr
    // 0x729920: tbnz            w0, #4, #0x729948
    // 0x729924: ldur            x0, [fp, #-0x18]
    // 0x729928: tbz             w0, #4, #0x729938
    // 0x72992c: ldur            x1, [fp, #-8]
    // 0x729930: ldur            x2, [fp, #-0x10]
    // 0x729934: r0 = contentType=()
    //     0x729934: bl              #0x72999c  ; [package:dio/src/options.dart] _RequestConfig::contentType=
    // 0x729938: r0 = Null
    //     0x729938: mov             x0, NULL
    // 0x72993c: LeaveFrame
    //     0x72993c: mov             SP, fp
    //     0x729940: ldp             fp, lr, [SP], #0x10
    // 0x729944: ret
    //     0x729944: ret             
    // 0x729948: ldur            x0, [fp, #-0x10]
    // 0x72994c: r0 = ArgumentError()
    //     0x72994c: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x729950: mov             x1, x0
    // 0x729954: r0 = "contentType"
    //     0x729954: add             x0, PP, #0xd, lsl #12  ; [pp+0xdee0] "contentType"
    //     0x729958: ldr             x0, [x0, #0xee0]
    // 0x72995c: StoreField: r1->field_13 = r0
    //     0x72995c: stur            w0, [x1, #0x13]
    // 0x729960: r0 = "Unable to set different values for `contentType` and the content-type header."
    //     0x729960: add             x0, PP, #0xf, lsl #12  ; [pp+0xf6b0] "Unable to set different values for `contentType` and the content-type header."
    //     0x729964: ldr             x0, [x0, #0x6b0]
    // 0x729968: ArrayStore: r1[0] = r0  ; List_4
    //     0x729968: stur            w0, [x1, #0x17]
    // 0x72996c: ldur            x0, [fp, #-0x10]
    // 0x729970: StoreField: r1->field_f = r0
    //     0x729970: stur            w0, [x1, #0xf]
    // 0x729974: r0 = true
    //     0x729974: add             x0, NULL, #0x20  ; true
    // 0x729978: StoreField: r1->field_b = r0
    //     0x729978: stur            w0, [x1, #0xb]
    // 0x72997c: mov             x0, x1
    // 0x729980: r0 = Throw()
    //     0x729980: bl              #0xec04b8  ; ThrowStub
    // 0x729984: brk             #0
    // 0x729988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x729988: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72998c: b               #0x729698
    // 0x729990: r9 = _headers
    //     0x729990: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5c0] Field <_RequestConfig@868184022._headers@868184022>: late (offset: 0xc)
    //     0x729994: ldr             x9, [x9, #0x5c0]
    // 0x729998: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729998: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  set _ contentType=(/* No info */) {
    // ** addr: 0x72999c, size: 0x11c
    // 0x72999c: EnterFrame
    //     0x72999c: stp             fp, lr, [SP, #-0x10]!
    //     0x7299a0: mov             fp, SP
    // 0x7299a4: AllocStack(0x8)
    //     0x7299a4: sub             SP, SP, #8
    // 0x7299a8: SetupParameters(_RequestConfig this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0x7299a8: mov             x0, x1
    //     0x7299ac: stur            x1, [fp, #-8]
    //     0x7299b0: mov             x1, x2
    // 0x7299b4: CheckStackOverflow
    //     0x7299b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7299b8: cmp             SP, x16
    //     0x7299bc: b.ls            #0x729a98
    // 0x7299c0: cmp             w1, NULL
    // 0x7299c4: b.ne            #0x7299d4
    // 0x7299c8: mov             x1, x0
    // 0x7299cc: r2 = Null
    //     0x7299cc: mov             x2, NULL
    // 0x7299d0: b               #0x7299e0
    // 0x7299d4: r0 = trim()
    //     0x7299d4: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0x7299d8: mov             x2, x0
    // 0x7299dc: ldur            x1, [fp, #-8]
    // 0x7299e0: mov             x0, x2
    // 0x7299e4: StoreField: r1->field_1b = r0
    //     0x7299e4: stur            w0, [x1, #0x1b]
    //     0x7299e8: ldurb           w16, [x1, #-1]
    //     0x7299ec: ldurb           w17, [x0, #-1]
    //     0x7299f0: and             x16, x17, x16, lsr #2
    //     0x7299f4: tst             x16, HEAP, lsr #32
    //     0x7299f8: b.eq            #0x729a00
    //     0x7299fc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x729a00: cmp             w2, NULL
    // 0x729a04: b.eq            #0x729a4c
    // 0x729a08: LoadField: r0 = r1->field_b
    //     0x729a08: ldur            w0, [x1, #0xb]
    // 0x729a0c: DecompressPointer r0
    //     0x729a0c: add             x0, x0, HEAP, lsl #32
    // 0x729a10: r16 = Sentinel
    //     0x729a10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729a14: cmp             w0, w16
    // 0x729a18: b.eq            #0x729aa0
    // 0x729a1c: r1 = LoadClassIdInstr(r0)
    //     0x729a1c: ldur            x1, [x0, #-1]
    //     0x729a20: ubfx            x1, x1, #0xc, #0x14
    // 0x729a24: mov             x16, x0
    // 0x729a28: mov             x0, x1
    // 0x729a2c: mov             x1, x16
    // 0x729a30: mov             x3, x2
    // 0x729a34: r2 = "content-type"
    //     0x729a34: add             x2, PP, #0xf, lsl #12  ; [pp+0xf6a8] "content-type"
    //     0x729a38: ldr             x2, [x2, #0x6a8]
    // 0x729a3c: r0 = GDT[cid_x0 + -0x10d]()
    //     0x729a3c: sub             lr, x0, #0x10d
    //     0x729a40: ldr             lr, [x21, lr, lsl #3]
    //     0x729a44: blr             lr
    // 0x729a48: b               #0x729a88
    // 0x729a4c: LoadField: r0 = r1->field_b
    //     0x729a4c: ldur            w0, [x1, #0xb]
    // 0x729a50: DecompressPointer r0
    //     0x729a50: add             x0, x0, HEAP, lsl #32
    // 0x729a54: r16 = Sentinel
    //     0x729a54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729a58: cmp             w0, w16
    // 0x729a5c: b.eq            #0x729aac
    // 0x729a60: r1 = LoadClassIdInstr(r0)
    //     0x729a60: ldur            x1, [x0, #-1]
    //     0x729a64: ubfx            x1, x1, #0xc, #0x14
    // 0x729a68: mov             x16, x0
    // 0x729a6c: mov             x0, x1
    // 0x729a70: mov             x1, x16
    // 0x729a74: r2 = "content-type"
    //     0x729a74: add             x2, PP, #0xf, lsl #12  ; [pp+0xf6a8] "content-type"
    //     0x729a78: ldr             x2, [x2, #0x6a8]
    // 0x729a7c: r0 = GDT[cid_x0 + 0x725]()
    //     0x729a7c: add             lr, x0, #0x725
    //     0x729a80: ldr             lr, [x21, lr, lsl #3]
    //     0x729a84: blr             lr
    // 0x729a88: r0 = Null
    //     0x729a88: mov             x0, NULL
    // 0x729a8c: LeaveFrame
    //     0x729a8c: mov             SP, fp
    //     0x729a90: ldp             fp, lr, [SP], #0x10
    // 0x729a94: ret
    //     0x729a94: ret             
    // 0x729a98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x729a98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x729a9c: b               #0x7299c0
    // 0x729aa0: r9 = _headers
    //     0x729aa0: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5c0] Field <_RequestConfig@868184022._headers@868184022>: late (offset: 0xc)
    //     0x729aa4: ldr             x9, [x9, #0x5c0]
    // 0x729aa8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729aa8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x729aac: r9 = _headers
    //     0x729aac: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5c0] Field <_RequestConfig@868184022._headers@868184022>: late (offset: 0xc)
    //     0x729ab0: ldr             x9, [x9, #0x5c0]
    // 0x729ab4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729ab4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  set _ headers=(/* No info */) {
    // ** addr: 0x729ab8, size: 0xbc
    // 0x729ab8: EnterFrame
    //     0x729ab8: stp             fp, lr, [SP, #-0x10]!
    //     0x729abc: mov             fp, SP
    // 0x729ac0: AllocStack(0x18)
    //     0x729ac0: sub             SP, SP, #0x18
    // 0x729ac4: SetupParameters(_RequestConfig this /* r1 => r1, fp-0x8 */)
    //     0x729ac4: stur            x1, [fp, #-8]
    // 0x729ac8: CheckStackOverflow
    //     0x729ac8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x729acc: cmp             SP, x16
    //     0x729ad0: b.ls            #0x729b6c
    // 0x729ad4: stp             x2, NULL, [SP]
    // 0x729ad8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x729ad8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x729adc: r0 = caseInsensitiveKeyMap()
    //     0x729adc: bl              #0x700b44  ; [package:dio/src/utils.dart] ::caseInsensitiveKeyMap
    // 0x729ae0: mov             x1, x0
    // 0x729ae4: ldur            x3, [fp, #-8]
    // 0x729ae8: StoreField: r3->field_b = r0
    //     0x729ae8: stur            w0, [x3, #0xb]
    //     0x729aec: ldurb           w16, [x3, #-1]
    //     0x729af0: ldurb           w17, [x0, #-1]
    //     0x729af4: and             x16, x17, x16, lsr #2
    //     0x729af8: tst             x16, HEAP, lsr #32
    //     0x729afc: b.eq            #0x729b04
    //     0x729b00: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x729b04: r0 = LoadClassIdInstr(r1)
    //     0x729b04: ldur            x0, [x1, #-1]
    //     0x729b08: ubfx            x0, x0, #0xc, #0x14
    // 0x729b0c: r2 = "content-type"
    //     0x729b0c: add             x2, PP, #0xf, lsl #12  ; [pp+0xf6a8] "content-type"
    //     0x729b10: ldr             x2, [x2, #0x6a8]
    // 0x729b14: r0 = GDT[cid_x0 + 0x55f]()
    //     0x729b14: add             lr, x0, #0x55f
    //     0x729b18: ldr             lr, [x21, lr, lsl #3]
    //     0x729b1c: blr             lr
    // 0x729b20: tbz             w0, #4, #0x729b5c
    // 0x729b24: ldur            x0, [fp, #-8]
    // 0x729b28: LoadField: r3 = r0->field_1b
    //     0x729b28: ldur            w3, [x0, #0x1b]
    // 0x729b2c: DecompressPointer r3
    //     0x729b2c: add             x3, x3, HEAP, lsl #32
    // 0x729b30: cmp             w3, NULL
    // 0x729b34: b.eq            #0x729b5c
    // 0x729b38: LoadField: r1 = r0->field_b
    //     0x729b38: ldur            w1, [x0, #0xb]
    // 0x729b3c: DecompressPointer r1
    //     0x729b3c: add             x1, x1, HEAP, lsl #32
    // 0x729b40: r0 = LoadClassIdInstr(r1)
    //     0x729b40: ldur            x0, [x1, #-1]
    //     0x729b44: ubfx            x0, x0, #0xc, #0x14
    // 0x729b48: r2 = "content-type"
    //     0x729b48: add             x2, PP, #0xf, lsl #12  ; [pp+0xf6a8] "content-type"
    //     0x729b4c: ldr             x2, [x2, #0x6a8]
    // 0x729b50: r0 = GDT[cid_x0 + -0x10d]()
    //     0x729b50: sub             lr, x0, #0x10d
    //     0x729b54: ldr             lr, [x21, lr, lsl #3]
    //     0x729b58: blr             lr
    // 0x729b5c: r0 = Null
    //     0x729b5c: mov             x0, NULL
    // 0x729b60: LeaveFrame
    //     0x729b60: mov             SP, fp
    //     0x729b64: ldp             fp, lr, [SP], #0x10
    // 0x729b68: ret
    //     0x729b68: ret             
    // 0x729b6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x729b6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x729b70: b               #0x729ad4
  }
  set _ receiveTimeout=(/* No info */) {
    // ** addr: 0xecf98c, size: 0x10
    // 0xecf98c: r3 = Instance_Duration
    //     0xecf98c: ldr             x3, [PP, #0x648]  ; [pp+0x648] Obj!Duration@e3a071
    // 0xecf990: ArrayStore: r1[0] = r3  ; List_4
    //     0xecf990: stur            w3, [x1, #0x17]
    // 0xecf994: r0 = Null
    //     0xecf994: mov             x0, NULL
    // 0xecf998: ret
    //     0xecf998: ret             
  }
  set _ sendTimeout=(/* No info */) {
    // ** addr: 0xecf99c, size: 0x10
    // 0xecf99c: r3 = Instance_Duration
    //     0xecf99c: ldr             x3, [PP, #0x648]  ; [pp+0x648] Obj!Duration@e3a071
    // 0xecf9a0: StoreField: r1->field_13 = r3
    //     0xecf9a0: stur            w3, [x1, #0x13]
    // 0xecf9a4: r0 = Null
    //     0xecf9a4: mov             x0, NULL
    // 0xecf9a8: ret
    //     0xecf9a8: ret             
  }
}

// class id: 5622, size: 0x54, field offset: 0x48
//   transformed mixin,
abstract class _BaseOptions&_RequestConfig&OptionsMixin extends _RequestConfig
     with OptionsMixin {

  late Map<String, dynamic> queryParameters; // offset: 0x4c
  late String _baseUrl; // offset: 0x48

  set _ baseUrl=(/* No info */) {
    // ** addr: 0x729508, size: 0xe0
    // 0x729508: EnterFrame
    //     0x729508: stp             fp, lr, [SP, #-0x10]!
    //     0x72950c: mov             fp, SP
    // 0x729510: AllocStack(0x10)
    //     0x729510: sub             SP, SP, #0x10
    // 0x729514: SetupParameters(_BaseOptions&_RequestConfig&OptionsMixin this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x729514: mov             x0, x2
    //     0x729518: stur            x2, [fp, #-0x10]
    //     0x72951c: mov             x2, x1
    //     0x729520: stur            x1, [fp, #-8]
    // 0x729524: CheckStackOverflow
    //     0x729524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x729528: cmp             SP, x16
    //     0x72952c: b.ls            #0x7295e0
    // 0x729530: LoadField: r1 = r0->field_7
    //     0x729530: ldur            w1, [x0, #7]
    // 0x729534: cbz             w1, #0x72956c
    // 0x729538: mov             x1, x0
    // 0x72953c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x72953c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x729540: r0 = parse()
    //     0x729540: bl              #0x60d170  ; [dart:core] Uri::parse
    // 0x729544: r1 = LoadClassIdInstr(r0)
    //     0x729544: ldur            x1, [x0, #-1]
    //     0x729548: ubfx            x1, x1, #0xc, #0x14
    // 0x72954c: mov             x16, x0
    // 0x729550: mov             x0, x1
    // 0x729554: mov             x1, x16
    // 0x729558: r0 = GDT[cid_x0 + -0xf97]()
    //     0x729558: sub             lr, x0, #0xf97
    //     0x72955c: ldr             lr, [x21, lr, lsl #3]
    //     0x729560: blr             lr
    // 0x729564: LoadField: r1 = r0->field_7
    //     0x729564: ldur            w1, [x0, #7]
    // 0x729568: cbz             w1, #0x7295a0
    // 0x72956c: ldur            x1, [fp, #-8]
    // 0x729570: ldur            x0, [fp, #-0x10]
    // 0x729574: StoreField: r1->field_47 = r0
    //     0x729574: stur            w0, [x1, #0x47]
    //     0x729578: ldurb           w16, [x1, #-1]
    //     0x72957c: ldurb           w17, [x0, #-1]
    //     0x729580: and             x16, x17, x16, lsr #2
    //     0x729584: tst             x16, HEAP, lsr #32
    //     0x729588: b.eq            #0x729590
    //     0x72958c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x729590: r0 = Null
    //     0x729590: mov             x0, NULL
    // 0x729594: LeaveFrame
    //     0x729594: mov             SP, fp
    //     0x729598: ldp             fp, lr, [SP], #0x10
    // 0x72959c: ret
    //     0x72959c: ret             
    // 0x7295a0: ldur            x0, [fp, #-0x10]
    // 0x7295a4: r0 = ArgumentError()
    //     0x7295a4: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x7295a8: mov             x1, x0
    // 0x7295ac: r0 = "baseUrl"
    //     0x7295ac: add             x0, PP, #0xf, lsl #12  ; [pp+0xf638] "baseUrl"
    //     0x7295b0: ldr             x0, [x0, #0x638]
    // 0x7295b4: StoreField: r1->field_13 = r0
    //     0x7295b4: stur            w0, [x1, #0x13]
    // 0x7295b8: r0 = "Must be a valid URL on platforms other than Web."
    //     0x7295b8: add             x0, PP, #0xf, lsl #12  ; [pp+0xf698] "Must be a valid URL on platforms other than Web."
    //     0x7295bc: ldr             x0, [x0, #0x698]
    // 0x7295c0: ArrayStore: r1[0] = r0  ; List_4
    //     0x7295c0: stur            w0, [x1, #0x17]
    // 0x7295c4: ldur            x0, [fp, #-0x10]
    // 0x7295c8: StoreField: r1->field_f = r0
    //     0x7295c8: stur            w0, [x1, #0xf]
    // 0x7295cc: r0 = true
    //     0x7295cc: add             x0, NULL, #0x20  ; true
    // 0x7295d0: StoreField: r1->field_b = r0
    //     0x7295d0: stur            w0, [x1, #0xb]
    // 0x7295d4: mov             x0, x1
    // 0x7295d8: r0 = Throw()
    //     0x7295d8: bl              #0xec04b8  ; ThrowStub
    // 0x7295dc: brk             #0
    // 0x7295e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7295e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7295e4: b               #0x729530
  }
  _ _BaseOptions&_RequestConfig&OptionsMixin(/* No info */) {
    // ** addr: 0x7295e8, size: 0x78
    // 0x7295e8: EnterFrame
    //     0x7295e8: stp             fp, lr, [SP, #-0x10]!
    //     0x7295ec: mov             fp, SP
    // 0x7295f0: AllocStack(0x48)
    //     0x7295f0: sub             SP, SP, #0x48
    // 0x7295f4: r0 = Sentinel
    //     0x7295f4: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7295f8: CheckStackOverflow
    //     0x7295f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7295fc: cmp             SP, x16
    //     0x729600: b.ls            #0x729658
    // 0x729604: StoreField: r1->field_47 = r0
    //     0x729604: stur            w0, [x1, #0x47]
    // 0x729608: StoreField: r1->field_4b = r0
    //     0x729608: stur            w0, [x1, #0x4b]
    // 0x72960c: ldr             x16, [fp, #0x50]
    // 0x729610: ldr             lr, [fp, #0x48]
    // 0x729614: stp             lr, x16, [SP, #0x38]
    // 0x729618: ldr             x16, [fp, #0x40]
    // 0x72961c: ldr             lr, [fp, #0x38]
    // 0x729620: stp             lr, x16, [SP, #0x28]
    // 0x729624: ldr             x16, [fp, #0x30]
    // 0x729628: ldr             lr, [fp, #0x28]
    // 0x72962c: stp             lr, x16, [SP, #0x18]
    // 0x729630: ldr             x16, [fp, #0x20]
    // 0x729634: ldr             lr, [fp, #0x18]
    // 0x729638: stp             lr, x16, [SP, #8]
    // 0x72963c: ldr             x16, [fp, #0x10]
    // 0x729640: str             x16, [SP]
    // 0x729644: r0 = _RequestConfig()
    //     0x729644: bl              #0x729660  ; [package:dio/src/options.dart] _RequestConfig::_RequestConfig
    // 0x729648: r0 = Null
    //     0x729648: mov             x0, NULL
    // 0x72964c: LeaveFrame
    //     0x72964c: mov             SP, fp
    //     0x729650: ldp             fp, lr, [SP], #0x10
    // 0x729654: ret
    //     0x729654: ret             
    // 0x729658: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x729658: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72965c: b               #0x729604
  }
}

// class id: 5623, size: 0x6c, field offset: 0x54
class RequestOptions extends _BaseOptions&_RequestConfig&OptionsMixin {

  get _ uri(/* No info */) {
    // ** addr: 0x724600, size: 0x290
    // 0x724600: EnterFrame
    //     0x724600: stp             fp, lr, [SP, #-0x10]!
    //     0x724604: mov             fp, SP
    // 0x724608: AllocStack(0x68)
    //     0x724608: sub             SP, SP, #0x68
    // 0x72460c: SetupParameters(RequestOptions this /* r1 => r1, fp-0x10 */)
    //     0x72460c: stur            x1, [fp, #-0x10]
    // 0x724610: CheckStackOverflow
    //     0x724610: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x724614: cmp             SP, x16
    //     0x724618: b.ls            #0x72485c
    // 0x72461c: LoadField: r0 = r1->field_5b
    //     0x72461c: ldur            w0, [x1, #0x5b]
    // 0x724620: DecompressPointer r0
    //     0x724620: add             x0, x0, HEAP, lsl #32
    // 0x724624: stur            x0, [fp, #-8]
    // 0x724628: r16 = "https\?:"
    //     0x724628: add             x16, PP, #0x11, lsl #12  ; [pp+0x11fe0] "https\?:"
    //     0x72462c: ldr             x16, [x16, #0xfe0]
    // 0x724630: stp             x16, NULL, [SP, #0x20]
    // 0x724634: r16 = false
    //     0x724634: add             x16, NULL, #0x30  ; false
    // 0x724638: r30 = true
    //     0x724638: add             lr, NULL, #0x20  ; true
    // 0x72463c: stp             lr, x16, [SP, #0x10]
    // 0x724640: r16 = false
    //     0x724640: add             x16, NULL, #0x30  ; false
    // 0x724644: r30 = false
    //     0x724644: add             lr, NULL, #0x30  ; false
    // 0x724648: stp             lr, x16, [SP]
    // 0x72464c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x72464c: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x724650: r0 = _RegExp()
    //     0x724650: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x724654: ldur            x1, [fp, #-8]
    // 0x724658: mov             x2, x0
    // 0x72465c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x72465c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x724660: r0 = startsWith()
    //     0x724660: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0x724664: tbz             w0, #4, #0x724788
    // 0x724668: ldur            x0, [fp, #-0x10]
    // 0x72466c: LoadField: r1 = r0->field_47
    //     0x72466c: ldur            w1, [x0, #0x47]
    // 0x724670: DecompressPointer r1
    //     0x724670: add             x1, x1, HEAP, lsl #32
    // 0x724674: r16 = Sentinel
    //     0x724674: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x724678: cmp             w1, w16
    // 0x72467c: b.eq            #0x724864
    // 0x724680: ldur            x16, [fp, #-8]
    // 0x724684: stp             x16, x1, [SP]
    // 0x724688: r0 = +()
    //     0x724688: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0x72468c: mov             x3, x0
    // 0x724690: stur            x3, [fp, #-0x18]
    // 0x724694: r0 = LoadClassIdInstr(r3)
    //     0x724694: ldur            x0, [x3, #-1]
    //     0x724698: ubfx            x0, x0, #0xc, #0x14
    // 0x72469c: mov             x1, x3
    // 0x7246a0: r2 = ":/"
    //     0x7246a0: add             x2, PP, #0x11, lsl #12  ; [pp+0x11fe8] ":/"
    //     0x7246a4: ldr             x2, [x2, #0xfe8]
    // 0x7246a8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7246a8: sub             lr, x0, #1, lsl #12
    //     0x7246ac: ldr             lr, [x21, lr, lsl #3]
    //     0x7246b0: blr             lr
    // 0x7246b4: mov             x2, x0
    // 0x7246b8: LoadField: r0 = r2->field_b
    //     0x7246b8: ldur            w0, [x2, #0xb]
    // 0x7246bc: r3 = LoadInt32Instr(r0)
    //     0x7246bc: sbfx            x3, x0, #1, #0x1f
    // 0x7246c0: stur            x3, [fp, #-0x30]
    // 0x7246c4: cmp             x3, #2
    // 0x7246c8: b.ne            #0x72477c
    // 0x7246cc: mov             x0, x3
    // 0x7246d0: r1 = 0
    //     0x7246d0: movz            x1, #0
    // 0x7246d4: cmp             x1, x0
    // 0x7246d8: b.hs            #0x724870
    // 0x7246dc: LoadField: r0 = r2->field_f
    //     0x7246dc: ldur            w0, [x2, #0xf]
    // 0x7246e0: DecompressPointer r0
    //     0x7246e0: add             x0, x0, HEAP, lsl #32
    // 0x7246e4: stur            x0, [fp, #-0x28]
    // 0x7246e8: LoadField: r4 = r0->field_f
    //     0x7246e8: ldur            w4, [x0, #0xf]
    // 0x7246ec: DecompressPointer r4
    //     0x7246ec: add             x4, x4, HEAP, lsl #32
    // 0x7246f0: stur            x4, [fp, #-0x20]
    // 0x7246f4: r1 = Null
    //     0x7246f4: mov             x1, NULL
    // 0x7246f8: r2 = 6
    //     0x7246f8: movz            x2, #0x6
    // 0x7246fc: r0 = AllocateArray()
    //     0x7246fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x724700: mov             x4, x0
    // 0x724704: ldur            x0, [fp, #-0x20]
    // 0x724708: stur            x4, [fp, #-0x38]
    // 0x72470c: StoreField: r4->field_f = r0
    //     0x72470c: stur            w0, [x4, #0xf]
    // 0x724710: r16 = ":/"
    //     0x724710: add             x16, PP, #0x11, lsl #12  ; [pp+0x11fe8] ":/"
    //     0x724714: ldr             x16, [x16, #0xfe8]
    // 0x724718: StoreField: r4->field_13 = r16
    //     0x724718: stur            w16, [x4, #0x13]
    // 0x72471c: ldur            x0, [fp, #-0x30]
    // 0x724720: r1 = 1
    //     0x724720: movz            x1, #0x1
    // 0x724724: cmp             x1, x0
    // 0x724728: b.hs            #0x724874
    // 0x72472c: ldur            x0, [fp, #-0x28]
    // 0x724730: LoadField: r1 = r0->field_13
    //     0x724730: ldur            w1, [x0, #0x13]
    // 0x724734: DecompressPointer r1
    //     0x724734: add             x1, x1, HEAP, lsl #32
    // 0x724738: r2 = "//"
    //     0x724738: ldr             x2, [PP, #0x3670]  ; [pp+0x3670] "//"
    // 0x72473c: r3 = "/"
    //     0x72473c: ldr             x3, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x724740: r0 = replaceAll()
    //     0x724740: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0x724744: ldur            x1, [fp, #-0x38]
    // 0x724748: ArrayStore: r1[2] = r0  ; List_4
    //     0x724748: add             x25, x1, #0x17
    //     0x72474c: str             w0, [x25]
    //     0x724750: tbz             w0, #0, #0x72476c
    //     0x724754: ldurb           w16, [x1, #-1]
    //     0x724758: ldurb           w17, [x0, #-1]
    //     0x72475c: and             x16, x17, x16, lsr #2
    //     0x724760: tst             x16, HEAP, lsr #32
    //     0x724764: b.eq            #0x72476c
    //     0x724768: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x72476c: ldur            x16, [fp, #-0x38]
    // 0x724770: str             x16, [SP]
    // 0x724774: r0 = _interpolate()
    //     0x724774: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x724778: b               #0x724780
    // 0x72477c: ldur            x0, [fp, #-0x18]
    // 0x724780: mov             x2, x0
    // 0x724784: b               #0x72478c
    // 0x724788: ldur            x2, [fp, #-8]
    // 0x72478c: ldur            x0, [fp, #-0x10]
    // 0x724790: stur            x2, [fp, #-8]
    // 0x724794: LoadField: r1 = r0->field_4b
    //     0x724794: ldur            w1, [x0, #0x4b]
    // 0x724798: DecompressPointer r1
    //     0x724798: add             x1, x1, HEAP, lsl #32
    // 0x72479c: r16 = Sentinel
    //     0x72479c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7247a0: cmp             w1, w16
    // 0x7247a4: b.eq            #0x724878
    // 0x7247a8: LoadField: r3 = r0->field_43
    //     0x7247a8: ldur            w3, [x0, #0x43]
    // 0x7247ac: DecompressPointer r3
    //     0x7247ac: add             x3, x3, HEAP, lsl #32
    // 0x7247b0: r16 = Sentinel
    //     0x7247b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7247b4: cmp             w3, w16
    // 0x7247b8: b.eq            #0x724884
    // 0x7247bc: r0 = urlEncodeQueryMap()
    //     0x7247bc: bl              #0x724890  ; [package:dio/src/transformer.dart] Transformer::urlEncodeQueryMap
    // 0x7247c0: mov             x3, x0
    // 0x7247c4: stur            x3, [fp, #-0x10]
    // 0x7247c8: LoadField: r0 = r3->field_7
    //     0x7247c8: ldur            w0, [x3, #7]
    // 0x7247cc: cbz             w0, #0x724824
    // 0x7247d0: ldur            x4, [fp, #-8]
    // 0x7247d4: r0 = LoadClassIdInstr(r4)
    //     0x7247d4: ldur            x0, [x4, #-1]
    //     0x7247d8: ubfx            x0, x0, #0xc, #0x14
    // 0x7247dc: mov             x1, x4
    // 0x7247e0: r2 = "\?"
    //     0x7247e0: ldr             x2, [PP, #0xf90]  ; [pp+0xf90] "\?"
    // 0x7247e4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7247e4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7247e8: r0 = GDT[cid_x0 + -0xffc]()
    //     0x7247e8: sub             lr, x0, #0xffc
    //     0x7247ec: ldr             lr, [x21, lr, lsl #3]
    //     0x7247f0: blr             lr
    // 0x7247f4: tbnz            w0, #4, #0x724800
    // 0x7247f8: r0 = "&"
    //     0x7247f8: ldr             x0, [PP, #0xde0]  ; [pp+0xde0] "&"
    // 0x7247fc: b               #0x724804
    // 0x724800: r0 = "\?"
    //     0x724800: ldr             x0, [PP, #0xf90]  ; [pp+0xf90] "\?"
    // 0x724804: ldur            x16, [fp, #-0x10]
    // 0x724808: stp             x16, x0, [SP]
    // 0x72480c: r0 = +()
    //     0x72480c: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0x724810: ldur            x16, [fp, #-8]
    // 0x724814: stp             x0, x16, [SP]
    // 0x724818: r0 = +()
    //     0x724818: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0x72481c: mov             x1, x0
    // 0x724820: b               #0x724828
    // 0x724824: ldur            x1, [fp, #-8]
    // 0x724828: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x724828: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x72482c: r0 = parse()
    //     0x72482c: bl              #0x60d170  ; [dart:core] Uri::parse
    // 0x724830: r1 = LoadClassIdInstr(r0)
    //     0x724830: ldur            x1, [x0, #-1]
    //     0x724834: ubfx            x1, x1, #0xc, #0x14
    // 0x724838: mov             x16, x0
    // 0x72483c: mov             x0, x1
    // 0x724840: mov             x1, x16
    // 0x724844: r0 = GDT[cid_x0 + -0x2bd]()
    //     0x724844: sub             lr, x0, #0x2bd
    //     0x724848: ldr             lr, [x21, lr, lsl #3]
    //     0x72484c: blr             lr
    // 0x724850: LeaveFrame
    //     0x724850: mov             SP, fp
    //     0x724854: ldp             fp, lr, [SP], #0x10
    // 0x724858: ret
    //     0x724858: ret             
    // 0x72485c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72485c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x724860: b               #0x72461c
    // 0x724864: r9 = _baseUrl
    //     0x724864: add             x9, PP, #0x11, lsl #12  ; [pp+0x11ff0] Field <_BaseOptions&_RequestConfig&OptionsMixin@868184022._baseUrl@868184022>: late (offset: 0x48)
    //     0x724868: ldr             x9, [x9, #0xff0]
    // 0x72486c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72486c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x724870: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x724870: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x724874: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x724874: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x724878: r9 = queryParameters
    //     0x724878: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5e8] Field <_BaseOptions&_RequestConfig&<EMAIL>>: late (offset: 0x4c)
    //     0x72487c: ldr             x9, [x9, #0x5e8]
    // 0x724880: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x724880: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x724884: r9 = listFormat
    //     0x724884: add             x9, PP, #0xf, lsl #12  ; [pp+0xf630] Field <<EMAIL>>: late (offset: 0x44)
    //     0x724888: ldr             x9, [x9, #0x630]
    // 0x72488c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x72488c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ RequestOptions(/* No info */) {
    // ** addr: 0x728fd8, size: 0x530
    // 0x728fd8: EnterFrame
    //     0x728fd8: stp             fp, lr, [SP, #-0x10]!
    //     0x728fdc: mov             fp, SP
    // 0x728fe0: AllocStack(0x80)
    //     0x728fe0: sub             SP, SP, #0x80
    // 0x728fe4: SetupParameters(RequestOptions this /* r1 => r9, fp-0x38 */, dynamic _ /* r2 => fp-0x8 */, dynamic _ /* r3 => r2 */, dynamic _ /* r5 => fp-0x18 */, dynamic _ /* r7 => r0 */, dynamic _ /* fp-0x10 */, dynamic _ /* r7 */, dynamic _ /* r11 */, {dynamic contentType = Null /* fp-0x20 */, dynamic extra = Null /* fp-0x28 */, dynamic followRedirects = Null /* r14 */, dynamic listFormat = Null /* r19 */, dynamic maxRedirects = Null /* r20 */, dynamic persistentConnection = Null /* r8 */, dynamic preserveHeaderCase = Null /* r10 */, dynamic receiveDataWhenStatusError = Null /* r5 */, dynamic responseType = Null /* r12 */, dynamic sourceStackTrace = Null /* r13, fp-0x30 */, dynamic validateStatus = Null /* r1 */})
    //     0x728fe4: mov             x9, x1
    //     0x728fe8: mov             x8, x2
    //     0x728fec: stur            x2, [fp, #-8]
    //     0x728ff0: mov             x2, x3
    //     0x728ff4: stur            x1, [fp, #-0x38]
    //     0x728ff8: mov             x1, x5
    //     0x728ffc: mov             x0, x7
    //     0x729000: stur            x5, [fp, #-0x18]
    //     0x729004: ldur            w3, [x4, #0x13]
    //     0x729008: sub             x5, x3, #0x12
    //     0x72900c: add             x10, fp, w5, sxtw #2
    //     0x729010: ldr             x10, [x10, #0x20]
    //     0x729014: stur            x10, [fp, #-0x10]
    //     0x729018: add             x7, fp, w5, sxtw #2
    //     0x72901c: ldr             x7, [x7, #0x18]
    //     0x729020: add             x11, fp, w5, sxtw #2
    //     0x729024: ldr             x11, [x11, #0x10]
    //     0x729028: ldur            w5, [x4, #0x1f]
    //     0x72902c: add             x5, x5, HEAP, lsl #32
    //     0x729030: add             x16, PP, #0xd, lsl #12  ; [pp+0xdee0] "contentType"
    //     0x729034: ldr             x16, [x16, #0xee0]
    //     0x729038: cmp             w5, w16
    //     0x72903c: b.ne            #0x729060
    //     0x729040: ldur            w5, [x4, #0x23]
    //     0x729044: add             x5, x5, HEAP, lsl #32
    //     0x729048: sub             w12, w3, w5
    //     0x72904c: add             x5, fp, w12, sxtw #2
    //     0x729050: ldr             x5, [x5, #8]
    //     0x729054: mov             x12, x5
    //     0x729058: movz            x5, #0x1
    //     0x72905c: b               #0x729068
    //     0x729060: mov             x12, NULL
    //     0x729064: movz            x5, #0
    //     0x729068: stur            x12, [fp, #-0x20]
    //     0x72906c: lsl             x13, x5, #1
    //     0x729070: lsl             w14, w13, #1
    //     0x729074: add             w19, w14, #8
    //     0x729078: add             x16, x4, w19, sxtw #1
    //     0x72907c: ldur            w20, [x16, #0xf]
    //     0x729080: add             x20, x20, HEAP, lsl #32
    //     0x729084: add             x16, PP, #0xf, lsl #12  ; [pp+0xf640] "extra"
    //     0x729088: ldr             x16, [x16, #0x640]
    //     0x72908c: cmp             w20, w16
    //     0x729090: b.ne            #0x7290c4
    //     0x729094: add             w5, w14, #0xa
    //     0x729098: add             x16, x4, w5, sxtw #1
    //     0x72909c: ldur            w14, [x16, #0xf]
    //     0x7290a0: add             x14, x14, HEAP, lsl #32
    //     0x7290a4: sub             w5, w3, w14
    //     0x7290a8: add             x14, fp, w5, sxtw #2
    //     0x7290ac: ldr             x14, [x14, #8]
    //     0x7290b0: add             w5, w13, #2
    //     0x7290b4: sbfx            x13, x5, #1, #0x1f
    //     0x7290b8: mov             x5, x13
    //     0x7290bc: mov             x13, x14
    //     0x7290c0: b               #0x7290c8
    //     0x7290c4: mov             x13, NULL
    //     0x7290c8: stur            x13, [fp, #-0x28]
    //     0x7290cc: lsl             x14, x5, #1
    //     0x7290d0: lsl             w19, w14, #1
    //     0x7290d4: add             w20, w19, #8
    //     0x7290d8: add             x16, x4, w20, sxtw #1
    //     0x7290dc: ldur            w23, [x16, #0xf]
    //     0x7290e0: add             x23, x23, HEAP, lsl #32
    //     0x7290e4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf648] "followRedirects"
    //     0x7290e8: ldr             x16, [x16, #0x648]
    //     0x7290ec: cmp             w23, w16
    //     0x7290f0: b.ne            #0x729124
    //     0x7290f4: add             w5, w19, #0xa
    //     0x7290f8: add             x16, x4, w5, sxtw #1
    //     0x7290fc: ldur            w19, [x16, #0xf]
    //     0x729100: add             x19, x19, HEAP, lsl #32
    //     0x729104: sub             w5, w3, w19
    //     0x729108: add             x19, fp, w5, sxtw #2
    //     0x72910c: ldr             x19, [x19, #8]
    //     0x729110: add             w5, w14, #2
    //     0x729114: sbfx            x14, x5, #1, #0x1f
    //     0x729118: mov             x5, x14
    //     0x72911c: mov             x14, x19
    //     0x729120: b               #0x729128
    //     0x729124: mov             x14, NULL
    //     0x729128: lsl             x19, x5, #1
    //     0x72912c: lsl             w20, w19, #1
    //     0x729130: add             w23, w20, #8
    //     0x729134: add             x16, x4, w23, sxtw #1
    //     0x729138: ldur            w24, [x16, #0xf]
    //     0x72913c: add             x24, x24, HEAP, lsl #32
    //     0x729140: add             x16, PP, #0xf, lsl #12  ; [pp+0xf650] "listFormat"
    //     0x729144: ldr             x16, [x16, #0x650]
    //     0x729148: cmp             w24, w16
    //     0x72914c: b.ne            #0x729180
    //     0x729150: add             w5, w20, #0xa
    //     0x729154: add             x16, x4, w5, sxtw #1
    //     0x729158: ldur            w20, [x16, #0xf]
    //     0x72915c: add             x20, x20, HEAP, lsl #32
    //     0x729160: sub             w5, w3, w20
    //     0x729164: add             x20, fp, w5, sxtw #2
    //     0x729168: ldr             x20, [x20, #8]
    //     0x72916c: add             w5, w19, #2
    //     0x729170: sbfx            x19, x5, #1, #0x1f
    //     0x729174: mov             x5, x19
    //     0x729178: mov             x19, x20
    //     0x72917c: b               #0x729184
    //     0x729180: mov             x19, NULL
    //     0x729184: lsl             x20, x5, #1
    //     0x729188: lsl             w23, w20, #1
    //     0x72918c: add             w24, w23, #8
    //     0x729190: add             x16, x4, w24, sxtw #1
    //     0x729194: ldur            w25, [x16, #0xf]
    //     0x729198: add             x25, x25, HEAP, lsl #32
    //     0x72919c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf658] "maxRedirects"
    //     0x7291a0: ldr             x16, [x16, #0x658]
    //     0x7291a4: cmp             w25, w16
    //     0x7291a8: b.ne            #0x7291dc
    //     0x7291ac: add             w5, w23, #0xa
    //     0x7291b0: add             x16, x4, w5, sxtw #1
    //     0x7291b4: ldur            w23, [x16, #0xf]
    //     0x7291b8: add             x23, x23, HEAP, lsl #32
    //     0x7291bc: sub             w5, w3, w23
    //     0x7291c0: add             x23, fp, w5, sxtw #2
    //     0x7291c4: ldr             x23, [x23, #8]
    //     0x7291c8: add             w5, w20, #2
    //     0x7291cc: sbfx            x20, x5, #1, #0x1f
    //     0x7291d0: mov             x5, x20
    //     0x7291d4: mov             x20, x23
    //     0x7291d8: b               #0x7291e0
    //     0x7291dc: mov             x20, NULL
    //     0x7291e0: lsl             x23, x5, #1
    //     0x7291e4: lsl             w24, w23, #1
    //     0x7291e8: add             w25, w24, #8
    //     0x7291ec: add             x16, x4, w25, sxtw #1
    //     0x7291f0: ldur            w8, [x16, #0xf]
    //     0x7291f4: add             x8, x8, HEAP, lsl #32
    //     0x7291f8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf660] "persistentConnection"
    //     0x7291fc: ldr             x16, [x16, #0x660]
    //     0x729200: cmp             w8, w16
    //     0x729204: b.ne            #0x729234
    //     0x729208: add             w5, w24, #0xa
    //     0x72920c: add             x16, x4, w5, sxtw #1
    //     0x729210: ldur            w8, [x16, #0xf]
    //     0x729214: add             x8, x8, HEAP, lsl #32
    //     0x729218: sub             w5, w3, w8
    //     0x72921c: add             x8, fp, w5, sxtw #2
    //     0x729220: ldr             x8, [x8, #8]
    //     0x729224: add             w5, w23, #2
    //     0x729228: sbfx            x23, x5, #1, #0x1f
    //     0x72922c: mov             x5, x23
    //     0x729230: b               #0x729238
    //     0x729234: mov             x8, NULL
    //     0x729238: lsl             x23, x5, #1
    //     0x72923c: lsl             w24, w23, #1
    //     0x729240: add             w25, w24, #8
    //     0x729244: add             x16, x4, w25, sxtw #1
    //     0x729248: ldur            w10, [x16, #0xf]
    //     0x72924c: add             x10, x10, HEAP, lsl #32
    //     0x729250: add             x16, PP, #0x10, lsl #12  ; [pp+0x10fc8] "preserveHeaderCase"
    //     0x729254: ldr             x16, [x16, #0xfc8]
    //     0x729258: cmp             w10, w16
    //     0x72925c: b.ne            #0x72928c
    //     0x729260: add             w5, w24, #0xa
    //     0x729264: add             x16, x4, w5, sxtw #1
    //     0x729268: ldur            w10, [x16, #0xf]
    //     0x72926c: add             x10, x10, HEAP, lsl #32
    //     0x729270: sub             w5, w3, w10
    //     0x729274: add             x10, fp, w5, sxtw #2
    //     0x729278: ldr             x10, [x10, #8]
    //     0x72927c: add             w5, w23, #2
    //     0x729280: sbfx            x23, x5, #1, #0x1f
    //     0x729284: mov             x5, x23
    //     0x729288: b               #0x729290
    //     0x72928c: mov             x10, NULL
    //     0x729290: lsl             x23, x5, #1
    //     0x729294: lsl             w24, w23, #1
    //     0x729298: add             w25, w24, #8
    //     0x72929c: add             x16, x4, w25, sxtw #1
    //     0x7292a0: ldur            w1, [x16, #0xf]
    //     0x7292a4: add             x1, x1, HEAP, lsl #32
    //     0x7292a8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf668] "receiveDataWhenStatusError"
    //     0x7292ac: ldr             x16, [x16, #0x668]
    //     0x7292b0: cmp             w1, w16
    //     0x7292b4: b.ne            #0x7292e4
    //     0x7292b8: add             w1, w24, #0xa
    //     0x7292bc: add             x16, x4, w1, sxtw #1
    //     0x7292c0: ldur            w5, [x16, #0xf]
    //     0x7292c4: add             x5, x5, HEAP, lsl #32
    //     0x7292c8: sub             w1, w3, w5
    //     0x7292cc: add             x5, fp, w1, sxtw #2
    //     0x7292d0: ldr             x5, [x5, #8]
    //     0x7292d4: add             w1, w23, #2
    //     0x7292d8: sbfx            x23, x1, #1, #0x1f
    //     0x7292dc: mov             x1, x23
    //     0x7292e0: b               #0x7292ec
    //     0x7292e4: mov             x1, x5
    //     0x7292e8: mov             x5, NULL
    //     0x7292ec: lsl             x23, x1, #1
    //     0x7292f0: lsl             w24, w23, #1
    //     0x7292f4: add             w25, w24, #8
    //     0x7292f8: add             x16, x4, w25, sxtw #1
    //     0x7292fc: ldur            w12, [x16, #0xf]
    //     0x729300: add             x12, x12, HEAP, lsl #32
    //     0x729304: add             x16, PP, #0xf, lsl #12  ; [pp+0xf678] "responseType"
    //     0x729308: ldr             x16, [x16, #0x678]
    //     0x72930c: cmp             w12, w16
    //     0x729310: b.ne            #0x729340
    //     0x729314: add             w1, w24, #0xa
    //     0x729318: add             x16, x4, w1, sxtw #1
    //     0x72931c: ldur            w12, [x16, #0xf]
    //     0x729320: add             x12, x12, HEAP, lsl #32
    //     0x729324: sub             w1, w3, w12
    //     0x729328: add             x12, fp, w1, sxtw #2
    //     0x72932c: ldr             x12, [x12, #8]
    //     0x729330: add             w1, w23, #2
    //     0x729334: sbfx            x23, x1, #1, #0x1f
    //     0x729338: mov             x1, x23
    //     0x72933c: b               #0x729344
    //     0x729340: mov             x12, NULL
    //     0x729344: lsl             x23, x1, #1
    //     0x729348: lsl             w24, w23, #1
    //     0x72934c: add             w25, w24, #8
    //     0x729350: add             x16, x4, w25, sxtw #1
    //     0x729354: ldur            w13, [x16, #0xf]
    //     0x729358: add             x13, x13, HEAP, lsl #32
    //     0x72935c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12438] "sourceStackTrace"
    //     0x729360: ldr             x16, [x16, #0x438]
    //     0x729364: cmp             w13, w16
    //     0x729368: b.ne            #0x729398
    //     0x72936c: add             w1, w24, #0xa
    //     0x729370: add             x16, x4, w1, sxtw #1
    //     0x729374: ldur            w13, [x16, #0xf]
    //     0x729378: add             x13, x13, HEAP, lsl #32
    //     0x72937c: sub             w1, w3, w13
    //     0x729380: add             x13, fp, w1, sxtw #2
    //     0x729384: ldr             x13, [x13, #8]
    //     0x729388: add             w1, w23, #2
    //     0x72938c: sbfx            x23, x1, #1, #0x1f
    //     0x729390: mov             x1, x23
    //     0x729394: b               #0x72939c
    //     0x729398: mov             x13, NULL
    //     0x72939c: stur            x13, [fp, #-0x30]
    //     0x7293a0: lsl             x23, x1, #1
    //     0x7293a4: lsl             w1, w23, #1
    //     0x7293a8: add             w23, w1, #8
    //     0x7293ac: add             x16, x4, w23, sxtw #1
    //     0x7293b0: ldur            w24, [x16, #0xf]
    //     0x7293b4: add             x24, x24, HEAP, lsl #32
    //     0x7293b8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf690] "validateStatus"
    //     0x7293bc: ldr             x16, [x16, #0x690]
    //     0x7293c0: cmp             w24, w16
    //     0x7293c4: b.ne            #0x7293e8
    //     0x7293c8: add             w23, w1, #0xa
    //     0x7293cc: add             x16, x4, w23, sxtw #1
    //     0x7293d0: ldur            w1, [x16, #0xf]
    //     0x7293d4: add             x1, x1, HEAP, lsl #32
    //     0x7293d8: sub             w4, w3, w1
    //     0x7293dc: add             x1, fp, w4, sxtw #2
    //     0x7293e0: ldr             x1, [x1, #8]
    //     0x7293e4: b               #0x7293ec
    //     0x7293e8: mov             x1, NULL
    // 0x7293ec: CheckStackOverflow
    //     0x7293ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7293f0: cmp             SP, x16
    //     0x7293f4: b.ls            #0x729500
    // 0x7293f8: StoreField: r9->field_5b = r0
    //     0x7293f8: stur            w0, [x9, #0x5b]
    //     0x7293fc: ldurb           w16, [x9, #-1]
    //     0x729400: ldurb           w17, [x0, #-1]
    //     0x729404: and             x16, x17, x16, lsr #2
    //     0x729408: tst             x16, HEAP, lsr #32
    //     0x72940c: b.eq            #0x729414
    //     0x729410: bl              #0xec0b28  ; WriteBarrierWrappersStub
    // 0x729414: mov             x0, x2
    // 0x729418: StoreField: r9->field_57 = r0
    //     0x729418: stur            w0, [x9, #0x57]
    //     0x72941c: tbz             w0, #0, #0x729438
    //     0x729420: ldurb           w16, [x9, #-1]
    //     0x729424: ldurb           w17, [x0, #-1]
    //     0x729428: and             x16, x17, x16, lsr #2
    //     0x72942c: tst             x16, HEAP, lsr #32
    //     0x729430: b.eq            #0x729438
    //     0x729434: bl              #0xec0b28  ; WriteBarrierWrappersStub
    // 0x729438: stp             x6, x20, [SP, #0x38]
    // 0x72943c: stp             x10, x8, [SP, #0x28]
    // 0x729440: stp             x7, x5, [SP, #0x18]
    // 0x729444: stp             x11, x12, [SP, #8]
    // 0x729448: str             x1, [SP]
    // 0x72944c: mov             x1, x9
    // 0x729450: ldur            x2, [fp, #-0x20]
    // 0x729454: ldur            x3, [fp, #-0x28]
    // 0x729458: mov             x5, x14
    // 0x72945c: ldur            x6, [fp, #-0x18]
    // 0x729460: mov             x7, x19
    // 0x729464: r0 = _BaseOptions&_RequestConfig&OptionsMixin()
    //     0x729464: bl              #0x7295e8  ; [package:dio/src/options.dart] _BaseOptions&_RequestConfig&OptionsMixin::_BaseOptions&_RequestConfig&OptionsMixin
    // 0x729468: ldur            x0, [fp, #-0x30]
    // 0x72946c: cmp             w0, NULL
    // 0x729470: b.ne            #0x729478
    // 0x729474: r0 = current()
    //     0x729474: bl              #0x5fc8a0  ; [dart:core] StackTrace::current
    // 0x729478: ldur            x1, [fp, #-0x38]
    // 0x72947c: ldur            x2, [fp, #-0x10]
    // 0x729480: StoreField: r1->field_53 = r0
    //     0x729480: stur            w0, [x1, #0x53]
    //     0x729484: ldurb           w16, [x1, #-1]
    //     0x729488: ldurb           w17, [x0, #-1]
    //     0x72948c: and             x16, x17, x16, lsr #2
    //     0x729490: tst             x16, HEAP, lsr #32
    //     0x729494: b.eq            #0x72949c
    //     0x729498: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x72949c: cmp             w2, NULL
    // 0x7294a0: b.ne            #0x7294b8
    // 0x7294a4: r16 = <String, dynamic>
    //     0x7294a4: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x7294a8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x7294ac: stp             lr, x16, [SP]
    // 0x7294b0: r0 = Map._fromLiteral()
    //     0x7294b0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7294b4: b               #0x7294bc
    // 0x7294b8: mov             x0, x2
    // 0x7294bc: ldur            x3, [fp, #-0x38]
    // 0x7294c0: StoreField: r3->field_4b = r0
    //     0x7294c0: stur            w0, [x3, #0x4b]
    //     0x7294c4: ldurb           w16, [x3, #-1]
    //     0x7294c8: ldurb           w17, [x0, #-1]
    //     0x7294cc: and             x16, x17, x16, lsr #2
    //     0x7294d0: tst             x16, HEAP, lsr #32
    //     0x7294d4: b.eq            #0x7294dc
    //     0x7294d8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7294dc: mov             x1, x3
    // 0x7294e0: ldur            x2, [fp, #-8]
    // 0x7294e4: r0 = baseUrl=()
    //     0x7294e4: bl              #0x729508  ; [package:dio/src/options.dart] _BaseOptions&_RequestConfig&OptionsMixin::baseUrl=
    // 0x7294e8: ldur            x1, [fp, #-0x38]
    // 0x7294ec: StoreField: r1->field_4f = rNULL
    //     0x7294ec: stur            NULL, [x1, #0x4f]
    // 0x7294f0: r0 = Null
    //     0x7294f0: mov             x0, NULL
    // 0x7294f4: LeaveFrame
    //     0x7294f4: mov             SP, fp
    //     0x7294f8: ldp             fp, lr, [SP], #0x10
    // 0x7294fc: ret
    //     0x7294fc: ret             
    // 0x729500: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x729500: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x729504: b               #0x7293f8
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xd71750, size: 0x34c
    // 0xd71750: EnterFrame
    //     0xd71750: stp             fp, lr, [SP, #-0x10]!
    //     0xd71754: mov             fp, SP
    // 0xd71758: AllocStack(0xd0)
    //     0xd71758: sub             SP, SP, #0xd0
    // 0xd7175c: SetupParameters(RequestOptions this /* r1 => r0, fp-0x30 */, dynamic _ /* r2 => r3, fp-0x38 */)
    //     0xd7175c: mov             x0, x1
    //     0xd71760: mov             x3, x2
    //     0xd71764: stur            x1, [fp, #-0x30]
    //     0xd71768: stur            x2, [fp, #-0x38]
    // 0xd7176c: CheckStackOverflow
    //     0xd7176c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd71770: cmp             SP, x16
    //     0xd71774: b.ls            #0xd719f8
    // 0xd71778: LoadField: r6 = r0->field_7
    //     0xd71778: ldur            w6, [x0, #7]
    // 0xd7177c: DecompressPointer r6
    //     0xd7177c: add             x6, x6, HEAP, lsl #32
    // 0xd71780: r16 = Sentinel
    //     0xd71780: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd71784: cmp             w6, w16
    // 0xd71788: b.eq            #0xd71a00
    // 0xd7178c: stur            x6, [fp, #-0x28]
    // 0xd71790: LoadField: r4 = r0->field_13
    //     0xd71790: ldur            w4, [x0, #0x13]
    // 0xd71794: DecompressPointer r4
    //     0xd71794: add             x4, x4, HEAP, lsl #32
    // 0xd71798: stur            x4, [fp, #-0x20]
    // 0xd7179c: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xd7179c: ldur            w5, [x0, #0x17]
    // 0xd717a0: DecompressPointer r5
    //     0xd717a0: add             x5, x5, HEAP, lsl #32
    // 0xd717a4: stur            x5, [fp, #-0x18]
    // 0xd717a8: LoadField: r7 = r0->field_5b
    //     0xd717a8: ldur            w7, [x0, #0x5b]
    // 0xd717ac: DecompressPointer r7
    //     0xd717ac: add             x7, x7, HEAP, lsl #32
    // 0xd717b0: stur            x7, [fp, #-0x10]
    // 0xd717b4: LoadField: r8 = r0->field_47
    //     0xd717b4: ldur            w8, [x0, #0x47]
    // 0xd717b8: DecompressPointer r8
    //     0xd717b8: add             x8, x8, HEAP, lsl #32
    // 0xd717bc: r16 = Sentinel
    //     0xd717bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd717c0: cmp             w8, w16
    // 0xd717c4: b.eq            #0xd71a0c
    // 0xd717c8: stur            x8, [fp, #-8]
    // 0xd717cc: LoadField: r2 = r0->field_4b
    //     0xd717cc: ldur            w2, [x0, #0x4b]
    // 0xd717d0: DecompressPointer r2
    //     0xd717d0: add             x2, x2, HEAP, lsl #32
    // 0xd717d4: r16 = Sentinel
    //     0xd717d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd717d8: cmp             w2, w16
    // 0xd717dc: b.eq            #0xd71a18
    // 0xd717e0: r1 = <String, dynamic>
    //     0xd717e0: ldr             x1, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xd717e4: r0 = LinkedHashMap.from()
    //     0xd717e4: bl              #0x63c6f0  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0xd717e8: mov             x3, x0
    // 0xd717ec: ldur            x0, [fp, #-0x30]
    // 0xd717f0: stur            x3, [fp, #-0x40]
    // 0xd717f4: LoadField: r2 = r0->field_2b
    //     0xd717f4: ldur            w2, [x0, #0x2b]
    // 0xd717f8: DecompressPointer r2
    //     0xd717f8: add             x2, x2, HEAP, lsl #32
    // 0xd717fc: r16 = Sentinel
    //     0xd717fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd71800: cmp             w2, w16
    // 0xd71804: b.eq            #0xd71a24
    // 0xd71808: r1 = <String, dynamic>
    //     0xd71808: ldr             x1, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xd7180c: r0 = LinkedHashMap.from()
    //     0xd7180c: bl              #0x63c6f0  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0xd71810: mov             x3, x0
    // 0xd71814: ldur            x0, [fp, #-0x30]
    // 0xd71818: stur            x3, [fp, #-0x48]
    // 0xd7181c: LoadField: r2 = r0->field_b
    //     0xd7181c: ldur            w2, [x0, #0xb]
    // 0xd71820: DecompressPointer r2
    //     0xd71820: add             x2, x2, HEAP, lsl #32
    // 0xd71824: r16 = Sentinel
    //     0xd71824: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd71828: cmp             w2, w16
    // 0xd7182c: b.eq            #0xd71a30
    // 0xd71830: r1 = <String, dynamic>
    //     0xd71830: ldr             x1, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xd71834: r0 = LinkedHashMap.from()
    //     0xd71834: bl              #0x63c6f0  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0xd71838: mov             x1, x0
    // 0xd7183c: ldur            x0, [fp, #-0x30]
    // 0xd71840: stur            x1, [fp, #-0x60]
    // 0xd71844: LoadField: r2 = r0->field_f
    //     0xd71844: ldur            w2, [x0, #0xf]
    // 0xd71848: DecompressPointer r2
    //     0xd71848: add             x2, x2, HEAP, lsl #32
    // 0xd7184c: r16 = Sentinel
    //     0xd7184c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd71850: cmp             w2, w16
    // 0xd71854: b.eq            #0xd71a3c
    // 0xd71858: LoadField: r2 = r0->field_1f
    //     0xd71858: ldur            w2, [x0, #0x1f]
    // 0xd7185c: DecompressPointer r2
    //     0xd7185c: add             x2, x2, HEAP, lsl #32
    // 0xd71860: r16 = Sentinel
    //     0xd71860: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd71864: cmp             w2, w16
    // 0xd71868: b.eq            #0xd71a48
    // 0xd7186c: stur            x2, [fp, #-0x58]
    // 0xd71870: LoadField: r3 = r0->field_23
    //     0xd71870: ldur            w3, [x0, #0x23]
    // 0xd71874: DecompressPointer r3
    //     0xd71874: add             x3, x3, HEAP, lsl #32
    // 0xd71878: r16 = Sentinel
    //     0xd71878: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd7187c: cmp             w3, w16
    // 0xd71880: b.eq            #0xd71a54
    // 0xd71884: LoadField: r3 = r0->field_27
    //     0xd71884: ldur            w3, [x0, #0x27]
    // 0xd71888: DecompressPointer r3
    //     0xd71888: add             x3, x3, HEAP, lsl #32
    // 0xd7188c: r16 = Sentinel
    //     0xd7188c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd71890: cmp             w3, w16
    // 0xd71894: b.eq            #0xd71a60
    // 0xd71898: LoadField: r3 = r0->field_2f
    //     0xd71898: ldur            w3, [x0, #0x2f]
    // 0xd7189c: DecompressPointer r3
    //     0xd7189c: add             x3, x3, HEAP, lsl #32
    // 0xd718a0: r16 = Sentinel
    //     0xd718a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd718a4: cmp             w3, w16
    // 0xd718a8: b.eq            #0xd71a6c
    // 0xd718ac: LoadField: r3 = r0->field_33
    //     0xd718ac: ldur            w3, [x0, #0x33]
    // 0xd718b0: DecompressPointer r3
    //     0xd718b0: add             x3, x3, HEAP, lsl #32
    // 0xd718b4: r16 = Sentinel
    //     0xd718b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd718b8: cmp             w3, w16
    // 0xd718bc: b.eq            #0xd71a78
    // 0xd718c0: LoadField: r3 = r0->field_37
    //     0xd718c0: ldur            w3, [x0, #0x37]
    // 0xd718c4: DecompressPointer r3
    //     0xd718c4: add             x3, x3, HEAP, lsl #32
    // 0xd718c8: r16 = Sentinel
    //     0xd718c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd718cc: cmp             w3, w16
    // 0xd718d0: b.eq            #0xd71a84
    // 0xd718d4: LoadField: r3 = r0->field_43
    //     0xd718d4: ldur            w3, [x0, #0x43]
    // 0xd718d8: DecompressPointer r3
    //     0xd718d8: add             x3, x3, HEAP, lsl #32
    // 0xd718dc: r16 = Sentinel
    //     0xd718dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd718e0: cmp             w3, w16
    // 0xd718e4: b.eq            #0xd71a90
    // 0xd718e8: LoadField: r3 = r0->field_53
    //     0xd718e8: ldur            w3, [x0, #0x53]
    // 0xd718ec: DecompressPointer r3
    //     0xd718ec: add             x3, x3, HEAP, lsl #32
    // 0xd718f0: stur            x3, [fp, #-0x50]
    // 0xd718f4: r0 = RequestOptions()
    //     0xd718f4: bl              #0x729bb4  ; AllocateRequestOptionsStub -> RequestOptions (size=0x6c)
    // 0xd718f8: stur            x0, [fp, #-0x68]
    // 0xd718fc: ldur            x16, [fp, #-0x40]
    // 0xd71900: ldur            lr, [fp, #-0x18]
    // 0xd71904: stp             lr, x16, [SP, #0x58]
    // 0xd71908: ldur            x16, [fp, #-0x20]
    // 0xd7190c: ldur            lr, [fp, #-0x48]
    // 0xd71910: stp             lr, x16, [SP, #0x48]
    // 0xd71914: r16 = false
    //     0xd71914: add             x16, NULL, #0x30  ; false
    // 0xd71918: ldur            lr, [fp, #-0x58]
    // 0xd7191c: stp             lr, x16, [SP, #0x38]
    // 0xd71920: r16 = Closure: (int?) => bool from Function '_defaultValidateStatus@868184022': static.
    //     0xd71920: add             x16, PP, #0xf, lsl #12  ; [pp+0xf5c8] Closure: (int?) => bool from Function '_defaultValidateStatus@868184022': static. (0x7e54fb129b74)
    //     0xd71924: ldr             x16, [x16, #0x5c8]
    // 0xd71928: r30 = true
    //     0xd71928: add             lr, NULL, #0x20  ; true
    // 0xd7192c: stp             lr, x16, [SP, #0x28]
    // 0xd71930: r16 = true
    //     0xd71930: add             x16, NULL, #0x20  ; true
    // 0xd71934: r30 = 10
    //     0xd71934: movz            lr, #0xa
    // 0xd71938: stp             lr, x16, [SP, #0x18]
    // 0xd7193c: r16 = true
    //     0xd7193c: add             x16, NULL, #0x20  ; true
    // 0xd71940: r30 = Instance_ListFormat
    //     0xd71940: add             lr, PP, #0xf, lsl #12  ; [pp+0xf5d0] Obj!ListFormat@e37521
    //     0xd71944: ldr             lr, [lr, #0x5d0]
    // 0xd71948: stp             lr, x16, [SP, #8]
    // 0xd7194c: ldur            x16, [fp, #-0x50]
    // 0xd71950: str             x16, [SP]
    // 0xd71954: mov             x1, x0
    // 0xd71958: ldur            x2, [fp, #-8]
    // 0xd7195c: ldur            x3, [fp, #-0x38]
    // 0xd71960: ldur            x5, [fp, #-0x60]
    // 0xd71964: ldur            x6, [fp, #-0x28]
    // 0xd71968: ldur            x7, [fp, #-0x10]
    // 0xd7196c: r4 = const [0, 0x13, 0xd, 0x9, extra, 0x9, followRedirects, 0xe, listFormat, 0x11, maxRedirects, 0xf, persistentConnection, 0x10, preserveHeaderCase, 0xa, receiveDataWhenStatusError, 0xd, responseType, 0xb, sourceStackTrace, 0x12, validateStatus, 0xc, null]
    //     0xd7196c: add             x4, PP, #0x1c, lsl #12  ; [pp+0x1c2d8] List(25) [0, 0x13, 0xd, 0x9, "extra", 0x9, "followRedirects", 0xe, "listFormat", 0x11, "maxRedirects", 0xf, "persistentConnection", 0x10, "preserveHeaderCase", 0xa, "receiveDataWhenStatusError", 0xd, "responseType", 0xb, "sourceStackTrace", 0x12, "validateStatus", 0xc, Null]
    //     0xd71970: ldr             x4, [x4, #0x2d8]
    // 0xd71974: r0 = RequestOptions()
    //     0xd71974: bl              #0x728fd8  ; [package:dio/src/options.dart] RequestOptions::RequestOptions
    // 0xd71978: ldur            x0, [fp, #-0x30]
    // 0xd7197c: LoadField: r1 = r0->field_b
    //     0xd7197c: ldur            w1, [x0, #0xb]
    // 0xd71980: DecompressPointer r1
    //     0xd71980: add             x1, x1, HEAP, lsl #32
    // 0xd71984: r0 = LoadClassIdInstr(r1)
    //     0xd71984: ldur            x0, [x1, #-1]
    //     0xd71988: ubfx            x0, x0, #0xc, #0x14
    // 0xd7198c: r2 = "content-type"
    //     0xd7198c: add             x2, PP, #0xf, lsl #12  ; [pp+0xf6a8] "content-type"
    //     0xd71990: ldr             x2, [x2, #0x6a8]
    // 0xd71994: r0 = GDT[cid_x0 + -0x114]()
    //     0xd71994: sub             lr, x0, #0x114
    //     0xd71998: ldr             lr, [x21, lr, lsl #3]
    //     0xd7199c: blr             lr
    // 0xd719a0: mov             x3, x0
    // 0xd719a4: r2 = Null
    //     0xd719a4: mov             x2, NULL
    // 0xd719a8: r1 = Null
    //     0xd719a8: mov             x1, NULL
    // 0xd719ac: stur            x3, [fp, #-8]
    // 0xd719b0: r4 = 60
    //     0xd719b0: movz            x4, #0x3c
    // 0xd719b4: branchIfSmi(r0, 0xd719c0)
    //     0xd719b4: tbz             w0, #0, #0xd719c0
    // 0xd719b8: r4 = LoadClassIdInstr(r0)
    //     0xd719b8: ldur            x4, [x0, #-1]
    //     0xd719bc: ubfx            x4, x4, #0xc, #0x14
    // 0xd719c0: sub             x4, x4, #0x5e
    // 0xd719c4: cmp             x4, #1
    // 0xd719c8: b.ls            #0xd719dc
    // 0xd719cc: r8 = String?
    //     0xd719cc: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xd719d0: r3 = Null
    //     0xd719d0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c2e0] Null
    //     0xd719d4: ldr             x3, [x3, #0x2e0]
    // 0xd719d8: r0 = String?()
    //     0xd719d8: bl              #0x600324  ; IsType_String?_Stub
    // 0xd719dc: ldur            x1, [fp, #-0x68]
    // 0xd719e0: ldur            x2, [fp, #-8]
    // 0xd719e4: r0 = contentType=()
    //     0xd719e4: bl              #0x72999c  ; [package:dio/src/options.dart] _RequestConfig::contentType=
    // 0xd719e8: ldur            x0, [fp, #-0x68]
    // 0xd719ec: LeaveFrame
    //     0xd719ec: mov             SP, fp
    //     0xd719f0: ldp             fp, lr, [SP], #0x10
    // 0xd719f4: ret
    //     0xd719f4: ret             
    // 0xd719f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd719f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd719fc: b               #0xd71778
    // 0xd71a00: r9 = method
    //     0xd71a00: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5e0] Field <<EMAIL>>: late (offset: 0x8)
    //     0xd71a04: ldr             x9, [x9, #0x5e0]
    // 0xd71a08: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a08: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd71a0c: r9 = _baseUrl
    //     0xd71a0c: add             x9, PP, #0x11, lsl #12  ; [pp+0x11ff0] Field <_BaseOptions&_RequestConfig&OptionsMixin@868184022._baseUrl@868184022>: late (offset: 0x48)
    //     0xd71a10: ldr             x9, [x9, #0xff0]
    // 0xd71a14: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a14: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd71a18: r9 = queryParameters
    //     0xd71a18: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5e8] Field <_BaseOptions&_RequestConfig&<EMAIL>>: late (offset: 0x4c)
    //     0xd71a1c: ldr             x9, [x9, #0x5e8]
    // 0xd71a20: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a20: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd71a24: r9 = extra
    //     0xd71a24: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5f0] Field <<EMAIL>>: late (offset: 0x2c)
    //     0xd71a28: ldr             x9, [x9, #0x5f0]
    // 0xd71a2c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a2c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd71a30: r9 = _headers
    //     0xd71a30: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5c0] Field <_RequestConfig@868184022._headers@868184022>: late (offset: 0xc)
    //     0xd71a34: ldr             x9, [x9, #0x5c0]
    // 0xd71a38: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a38: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd71a3c: r9 = preserveHeaderCase
    //     0xd71a3c: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5f8] Field <<EMAIL>>: late (offset: 0x10)
    //     0xd71a40: ldr             x9, [x9, #0x5f8]
    // 0xd71a44: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a44: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd71a48: r9 = responseType
    //     0xd71a48: add             x9, PP, #0xf, lsl #12  ; [pp+0xf600] Field <<EMAIL>>: late (offset: 0x20)
    //     0xd71a4c: ldr             x9, [x9, #0x600]
    // 0xd71a50: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a50: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd71a54: r9 = validateStatus
    //     0xd71a54: add             x9, PP, #0xf, lsl #12  ; [pp+0xf608] Field <<EMAIL>>: late (offset: 0x24)
    //     0xd71a58: ldr             x9, [x9, #0x608]
    // 0xd71a5c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a5c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd71a60: r9 = receiveDataWhenStatusError
    //     0xd71a60: add             x9, PP, #0xf, lsl #12  ; [pp+0xf610] Field <<EMAIL>>: late (offset: 0x28)
    //     0xd71a64: ldr             x9, [x9, #0x610]
    // 0xd71a68: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a68: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd71a6c: r9 = followRedirects
    //     0xd71a6c: add             x9, PP, #0xf, lsl #12  ; [pp+0xf618] Field <<EMAIL>>: late (offset: 0x30)
    //     0xd71a70: ldr             x9, [x9, #0x618]
    // 0xd71a74: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a74: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd71a78: r9 = maxRedirects
    //     0xd71a78: add             x9, PP, #0xf, lsl #12  ; [pp+0xf620] Field <<EMAIL>>: late (offset: 0x34)
    //     0xd71a7c: ldr             x9, [x9, #0x620]
    // 0xd71a80: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a80: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd71a84: r9 = persistentConnection
    //     0xd71a84: add             x9, PP, #0xf, lsl #12  ; [pp+0xf628] Field <<EMAIL>>: late (offset: 0x38)
    //     0xd71a88: ldr             x9, [x9, #0x628]
    // 0xd71a8c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a8c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd71a90: r9 = listFormat
    //     0xd71a90: add             x9, PP, #0xf, lsl #12  ; [pp+0xf630] Field <<EMAIL>>: late (offset: 0x44)
    //     0xd71a94: ldr             x9, [x9, #0x630]
    // 0xd71a98: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd71a98: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 5624, size: 0x54, field offset: 0x54
class BaseOptions extends _BaseOptions&_RequestConfig&OptionsMixin {

  _ copyWith(/* No info */) {
    // ** addr: 0x729d08, size: 0x26c
    // 0x729d08: EnterFrame
    //     0x729d08: stp             fp, lr, [SP, #-0x10]!
    //     0x729d0c: mov             fp, SP
    // 0x729d10: AllocStack(0xc8)
    //     0x729d10: sub             SP, SP, #0xc8
    // 0x729d14: SetupParameters(BaseOptions this /* r1 => r4, fp-0x28 */, dynamic _ /* r2 => r0, fp-0x30 */, dynamic _ /* r3 => r3, fp-0x38 */)
    //     0x729d14: mov             x4, x1
    //     0x729d18: mov             x0, x2
    //     0x729d1c: stur            x1, [fp, #-0x28]
    //     0x729d20: stur            x2, [fp, #-0x30]
    //     0x729d24: stur            x3, [fp, #-0x38]
    // 0x729d28: CheckStackOverflow
    //     0x729d28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x729d2c: cmp             SP, x16
    //     0x729d30: b.ls            #0x729ee8
    // 0x729d34: LoadField: r5 = r4->field_7
    //     0x729d34: ldur            w5, [x4, #7]
    // 0x729d38: DecompressPointer r5
    //     0x729d38: add             x5, x5, HEAP, lsl #32
    // 0x729d3c: r16 = Sentinel
    //     0x729d3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729d40: cmp             w5, w16
    // 0x729d44: b.eq            #0x729ef0
    // 0x729d48: stur            x5, [fp, #-0x20]
    // 0x729d4c: LoadField: r6 = r4->field_4b
    //     0x729d4c: ldur            w6, [x4, #0x4b]
    // 0x729d50: DecompressPointer r6
    //     0x729d50: add             x6, x6, HEAP, lsl #32
    // 0x729d54: r16 = Sentinel
    //     0x729d54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729d58: cmp             w6, w16
    // 0x729d5c: b.eq            #0x729efc
    // 0x729d60: stur            x6, [fp, #-0x18]
    // 0x729d64: ArrayLoad: r7 = r4[0]  ; List_4
    //     0x729d64: ldur            w7, [x4, #0x17]
    // 0x729d68: DecompressPointer r7
    //     0x729d68: add             x7, x7, HEAP, lsl #32
    // 0x729d6c: stur            x7, [fp, #-0x10]
    // 0x729d70: LoadField: r8 = r4->field_13
    //     0x729d70: ldur            w8, [x4, #0x13]
    // 0x729d74: DecompressPointer r8
    //     0x729d74: add             x8, x8, HEAP, lsl #32
    // 0x729d78: stur            x8, [fp, #-8]
    // 0x729d7c: LoadField: r2 = r4->field_2b
    //     0x729d7c: ldur            w2, [x4, #0x2b]
    // 0x729d80: DecompressPointer r2
    //     0x729d80: add             x2, x2, HEAP, lsl #32
    // 0x729d84: r16 = Sentinel
    //     0x729d84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729d88: cmp             w2, w16
    // 0x729d8c: b.eq            #0x729f08
    // 0x729d90: r1 = <String, dynamic>
    //     0x729d90: ldr             x1, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x729d94: r0 = LinkedHashMap.from()
    //     0x729d94: bl              #0x63c6f0  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0x729d98: mov             x2, x0
    // 0x729d9c: ldur            x0, [fp, #-0x28]
    // 0x729da0: stur            x2, [fp, #-0x48]
    // 0x729da4: LoadField: r1 = r0->field_f
    //     0x729da4: ldur            w1, [x0, #0xf]
    // 0x729da8: DecompressPointer r1
    //     0x729da8: add             x1, x1, HEAP, lsl #32
    // 0x729dac: r16 = Sentinel
    //     0x729dac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729db0: cmp             w1, w16
    // 0x729db4: b.eq            #0x729f14
    // 0x729db8: LoadField: r3 = r0->field_1f
    //     0x729db8: ldur            w3, [x0, #0x1f]
    // 0x729dbc: DecompressPointer r3
    //     0x729dbc: add             x3, x3, HEAP, lsl #32
    // 0x729dc0: r16 = Sentinel
    //     0x729dc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729dc4: cmp             w3, w16
    // 0x729dc8: b.eq            #0x729f20
    // 0x729dcc: mov             x1, x0
    // 0x729dd0: stur            x3, [fp, #-0x40]
    // 0x729dd4: r0 = contentType()
    //     0x729dd4: bl              #0x72752c  ; [package:dio/src/options.dart] _RequestConfig::contentType
    // 0x729dd8: mov             x1, x0
    // 0x729ddc: ldur            x0, [fp, #-0x28]
    // 0x729de0: stur            x1, [fp, #-0x50]
    // 0x729de4: LoadField: r2 = r0->field_23
    //     0x729de4: ldur            w2, [x0, #0x23]
    // 0x729de8: DecompressPointer r2
    //     0x729de8: add             x2, x2, HEAP, lsl #32
    // 0x729dec: r16 = Sentinel
    //     0x729dec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729df0: cmp             w2, w16
    // 0x729df4: b.eq            #0x729f2c
    // 0x729df8: LoadField: r2 = r0->field_27
    //     0x729df8: ldur            w2, [x0, #0x27]
    // 0x729dfc: DecompressPointer r2
    //     0x729dfc: add             x2, x2, HEAP, lsl #32
    // 0x729e00: r16 = Sentinel
    //     0x729e00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729e04: cmp             w2, w16
    // 0x729e08: b.eq            #0x729f38
    // 0x729e0c: LoadField: r2 = r0->field_2f
    //     0x729e0c: ldur            w2, [x0, #0x2f]
    // 0x729e10: DecompressPointer r2
    //     0x729e10: add             x2, x2, HEAP, lsl #32
    // 0x729e14: r16 = Sentinel
    //     0x729e14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729e18: cmp             w2, w16
    // 0x729e1c: b.eq            #0x729f44
    // 0x729e20: LoadField: r2 = r0->field_33
    //     0x729e20: ldur            w2, [x0, #0x33]
    // 0x729e24: DecompressPointer r2
    //     0x729e24: add             x2, x2, HEAP, lsl #32
    // 0x729e28: r16 = Sentinel
    //     0x729e28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729e2c: cmp             w2, w16
    // 0x729e30: b.eq            #0x729f50
    // 0x729e34: LoadField: r2 = r0->field_37
    //     0x729e34: ldur            w2, [x0, #0x37]
    // 0x729e38: DecompressPointer r2
    //     0x729e38: add             x2, x2, HEAP, lsl #32
    // 0x729e3c: r16 = Sentinel
    //     0x729e3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729e40: cmp             w2, w16
    // 0x729e44: b.eq            #0x729f5c
    // 0x729e48: LoadField: r2 = r0->field_43
    //     0x729e48: ldur            w2, [x0, #0x43]
    // 0x729e4c: DecompressPointer r2
    //     0x729e4c: add             x2, x2, HEAP, lsl #32
    // 0x729e50: r16 = Sentinel
    //     0x729e50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x729e54: cmp             w2, w16
    // 0x729e58: b.eq            #0x729f68
    // 0x729e5c: r0 = BaseOptions()
    //     0x729e5c: bl              #0x72a570  ; AllocateBaseOptionsStub -> BaseOptions (size=0x54)
    // 0x729e60: stur            x0, [fp, #-0x28]
    // 0x729e64: ldur            x16, [fp, #-0x20]
    // 0x729e68: ldur            lr, [fp, #-0x30]
    // 0x729e6c: stp             lr, x16, [SP, #0x68]
    // 0x729e70: ldur            x16, [fp, #-0x18]
    // 0x729e74: ldur            lr, [fp, #-0x10]
    // 0x729e78: stp             lr, x16, [SP, #0x58]
    // 0x729e7c: ldur            x16, [fp, #-8]
    // 0x729e80: ldur            lr, [fp, #-0x48]
    // 0x729e84: stp             lr, x16, [SP, #0x48]
    // 0x729e88: ldur            x16, [fp, #-0x38]
    // 0x729e8c: ldur            lr, [fp, #-0x40]
    // 0x729e90: stp             lr, x16, [SP, #0x38]
    // 0x729e94: ldur            x16, [fp, #-0x50]
    // 0x729e98: r30 = Closure: (int?) => bool from Function '_defaultValidateStatus@868184022': static.
    //     0x729e98: add             lr, PP, #0xf, lsl #12  ; [pp+0xf5c8] Closure: (int?) => bool from Function '_defaultValidateStatus@868184022': static. (0x7e54fb129b74)
    //     0x729e9c: ldr             lr, [lr, #0x5c8]
    // 0x729ea0: stp             lr, x16, [SP, #0x28]
    // 0x729ea4: r16 = true
    //     0x729ea4: add             x16, NULL, #0x20  ; true
    // 0x729ea8: r30 = true
    //     0x729ea8: add             lr, NULL, #0x20  ; true
    // 0x729eac: stp             lr, x16, [SP, #0x18]
    // 0x729eb0: r16 = 10
    //     0x729eb0: movz            x16, #0xa
    // 0x729eb4: r30 = true
    //     0x729eb4: add             lr, NULL, #0x20  ; true
    // 0x729eb8: stp             lr, x16, [SP, #8]
    // 0x729ebc: r16 = Instance_ListFormat
    //     0x729ebc: add             x16, PP, #0xf, lsl #12  ; [pp+0xf5d0] Obj!ListFormat@e37521
    //     0x729ec0: ldr             x16, [x16, #0x5d0]
    // 0x729ec4: str             x16, [SP]
    // 0x729ec8: mov             x1, x0
    // 0x729ecc: r4 = const [0, 0x10, 0xf, 0x1, baseUrl, 0x2, contentType, 0x9, extra, 0x6, followRedirects, 0xc, headers, 0x7, listFormat, 0xf, maxRedirects, 0xd, method, 0x1, persistentConnection, 0xe, queryParameters, 0x3, receiveDataWhenStatusError, 0xb, receiveTimeout, 0x4, responseType, 0x8, sendTimeout, 0x5, validateStatus, 0xa, null]
    //     0x729ecc: add             x4, PP, #0xf, lsl #12  ; [pp+0xf5d8] List(35) [0, 0x10, 0xf, 0x1, "baseUrl", 0x2, "contentType", 0x9, "extra", 0x6, "followRedirects", 0xc, "headers", 0x7, "listFormat", 0xf, "maxRedirects", 0xd, "method", 0x1, "persistentConnection", 0xe, "queryParameters", 0x3, "receiveDataWhenStatusError", 0xb, "receiveTimeout", 0x4, "responseType", 0x8, "sendTimeout", 0x5, "validateStatus", 0xa, Null]
    //     0x729ed0: ldr             x4, [x4, #0x5d8]
    // 0x729ed4: r0 = BaseOptions()
    //     0x729ed4: bl              #0x729f74  ; [package:dio/src/options.dart] BaseOptions::BaseOptions
    // 0x729ed8: ldur            x0, [fp, #-0x28]
    // 0x729edc: LeaveFrame
    //     0x729edc: mov             SP, fp
    //     0x729ee0: ldp             fp, lr, [SP], #0x10
    // 0x729ee4: ret
    //     0x729ee4: ret             
    // 0x729ee8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x729ee8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x729eec: b               #0x729d34
    // 0x729ef0: r9 = method
    //     0x729ef0: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5e0] Field <<EMAIL>>: late (offset: 0x8)
    //     0x729ef4: ldr             x9, [x9, #0x5e0]
    // 0x729ef8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729ef8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x729efc: r9 = queryParameters
    //     0x729efc: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5e8] Field <_BaseOptions&_RequestConfig&<EMAIL>>: late (offset: 0x4c)
    //     0x729f00: ldr             x9, [x9, #0x5e8]
    // 0x729f04: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729f04: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x729f08: r9 = extra
    //     0x729f08: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5f0] Field <<EMAIL>>: late (offset: 0x2c)
    //     0x729f0c: ldr             x9, [x9, #0x5f0]
    // 0x729f10: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729f10: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x729f14: r9 = preserveHeaderCase
    //     0x729f14: add             x9, PP, #0xf, lsl #12  ; [pp+0xf5f8] Field <<EMAIL>>: late (offset: 0x10)
    //     0x729f18: ldr             x9, [x9, #0x5f8]
    // 0x729f1c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729f1c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x729f20: r9 = responseType
    //     0x729f20: add             x9, PP, #0xf, lsl #12  ; [pp+0xf600] Field <<EMAIL>>: late (offset: 0x20)
    //     0x729f24: ldr             x9, [x9, #0x600]
    // 0x729f28: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729f28: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x729f2c: r9 = validateStatus
    //     0x729f2c: add             x9, PP, #0xf, lsl #12  ; [pp+0xf608] Field <<EMAIL>>: late (offset: 0x24)
    //     0x729f30: ldr             x9, [x9, #0x608]
    // 0x729f34: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729f34: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x729f38: r9 = receiveDataWhenStatusError
    //     0x729f38: add             x9, PP, #0xf, lsl #12  ; [pp+0xf610] Field <<EMAIL>>: late (offset: 0x28)
    //     0x729f3c: ldr             x9, [x9, #0x610]
    // 0x729f40: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729f40: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x729f44: r9 = followRedirects
    //     0x729f44: add             x9, PP, #0xf, lsl #12  ; [pp+0xf618] Field <<EMAIL>>: late (offset: 0x30)
    //     0x729f48: ldr             x9, [x9, #0x618]
    // 0x729f4c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729f4c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x729f50: r9 = maxRedirects
    //     0x729f50: add             x9, PP, #0xf, lsl #12  ; [pp+0xf620] Field <<EMAIL>>: late (offset: 0x34)
    //     0x729f54: ldr             x9, [x9, #0x620]
    // 0x729f58: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729f58: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x729f5c: r9 = persistentConnection
    //     0x729f5c: add             x9, PP, #0xf, lsl #12  ; [pp+0xf628] Field <<EMAIL>>: late (offset: 0x38)
    //     0x729f60: ldr             x9, [x9, #0x628]
    // 0x729f64: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729f64: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x729f68: r9 = listFormat
    //     0x729f68: add             x9, PP, #0xf, lsl #12  ; [pp+0xf630] Field <<EMAIL>>: late (offset: 0x44)
    //     0x729f6c: ldr             x9, [x9, #0x630]
    // 0x729f70: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x729f70: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ BaseOptions(/* No info */) {
    // ** addr: 0x729f74, size: 0x5fc
    // 0x729f74: EnterFrame
    //     0x729f74: stp             fp, lr, [SP, #-0x10]!
    //     0x729f78: mov             fp, SP
    // 0x729f7c: AllocStack(0x60)
    //     0x729f7c: sub             SP, SP, #0x60
    // 0x729f80: SetupParameters(BaseOptions this /* r1 => fp-0x8 */, {dynamic baseUrl = "" /* r8, fp-0x18 */, dynamic contentType = Null /* r3 */, dynamic extra = Null /* r5 */, dynamic followRedirects = Null /* r6 */, dynamic headers = Null /* r7 */, dynamic listFormat = Null /* r9 */, dynamic maxRedirects = Null /* r10 */, dynamic method = Null /* r11 */, dynamic persistentConnection = Null /* r12 */, dynamic queryParameters = Null /* r13, fp-0x10 */, dynamic receiveDataWhenStatusError = Null /* r14 */, dynamic receiveTimeout = Null /* r19 */, dynamic responseType = Instance_ResponseType /* r20 */, dynamic sendTimeout = Null /* r2 */, dynamic validateStatus = Null /* r0 */})
    //     0x729f80: mov             x0, x1
    //     0x729f84: stur            x1, [fp, #-8]
    //     0x729f88: ldur            w1, [x4, #0x13]
    //     0x729f8c: ldur            w2, [x4, #0x1f]
    //     0x729f90: add             x2, x2, HEAP, lsl #32
    //     0x729f94: add             x16, PP, #0xf, lsl #12  ; [pp+0xf638] "baseUrl"
    //     0x729f98: ldr             x16, [x16, #0x638]
    //     0x729f9c: cmp             w2, w16
    //     0x729fa0: b.ne            #0x729fc4
    //     0x729fa4: ldur            w2, [x4, #0x23]
    //     0x729fa8: add             x2, x2, HEAP, lsl #32
    //     0x729fac: sub             w3, w1, w2
    //     0x729fb0: add             x2, fp, w3, sxtw #2
    //     0x729fb4: ldr             x2, [x2, #8]
    //     0x729fb8: mov             x8, x2
    //     0x729fbc: movz            x2, #0x1
    //     0x729fc0: b               #0x729fcc
    //     0x729fc4: ldr             x8, [PP, #0x288]  ; [pp+0x288] ""
    //     0x729fc8: movz            x2, #0
    //     0x729fcc: stur            x8, [fp, #-0x18]
    //     0x729fd0: lsl             x3, x2, #1
    //     0x729fd4: lsl             w5, w3, #1
    //     0x729fd8: add             w6, w5, #8
    //     0x729fdc: add             x16, x4, w6, sxtw #1
    //     0x729fe0: ldur            w7, [x16, #0xf]
    //     0x729fe4: add             x7, x7, HEAP, lsl #32
    //     0x729fe8: add             x16, PP, #0xd, lsl #12  ; [pp+0xdee0] "contentType"
    //     0x729fec: ldr             x16, [x16, #0xee0]
    //     0x729ff0: cmp             w7, w16
    //     0x729ff4: b.ne            #0x72a028
    //     0x729ff8: add             w2, w5, #0xa
    //     0x729ffc: add             x16, x4, w2, sxtw #1
    //     0x72a000: ldur            w5, [x16, #0xf]
    //     0x72a004: add             x5, x5, HEAP, lsl #32
    //     0x72a008: sub             w2, w1, w5
    //     0x72a00c: add             x5, fp, w2, sxtw #2
    //     0x72a010: ldr             x5, [x5, #8]
    //     0x72a014: add             w2, w3, #2
    //     0x72a018: sbfx            x3, x2, #1, #0x1f
    //     0x72a01c: mov             x2, x3
    //     0x72a020: mov             x3, x5
    //     0x72a024: b               #0x72a02c
    //     0x72a028: mov             x3, NULL
    //     0x72a02c: lsl             x5, x2, #1
    //     0x72a030: lsl             w6, w5, #1
    //     0x72a034: add             w7, w6, #8
    //     0x72a038: add             x16, x4, w7, sxtw #1
    //     0x72a03c: ldur            w9, [x16, #0xf]
    //     0x72a040: add             x9, x9, HEAP, lsl #32
    //     0x72a044: add             x16, PP, #0xf, lsl #12  ; [pp+0xf640] "extra"
    //     0x72a048: ldr             x16, [x16, #0x640]
    //     0x72a04c: cmp             w9, w16
    //     0x72a050: b.ne            #0x72a084
    //     0x72a054: add             w2, w6, #0xa
    //     0x72a058: add             x16, x4, w2, sxtw #1
    //     0x72a05c: ldur            w6, [x16, #0xf]
    //     0x72a060: add             x6, x6, HEAP, lsl #32
    //     0x72a064: sub             w2, w1, w6
    //     0x72a068: add             x6, fp, w2, sxtw #2
    //     0x72a06c: ldr             x6, [x6, #8]
    //     0x72a070: add             w2, w5, #2
    //     0x72a074: sbfx            x5, x2, #1, #0x1f
    //     0x72a078: mov             x2, x5
    //     0x72a07c: mov             x5, x6
    //     0x72a080: b               #0x72a088
    //     0x72a084: mov             x5, NULL
    //     0x72a088: lsl             x6, x2, #1
    //     0x72a08c: lsl             w7, w6, #1
    //     0x72a090: add             w9, w7, #8
    //     0x72a094: add             x16, x4, w9, sxtw #1
    //     0x72a098: ldur            w10, [x16, #0xf]
    //     0x72a09c: add             x10, x10, HEAP, lsl #32
    //     0x72a0a0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf648] "followRedirects"
    //     0x72a0a4: ldr             x16, [x16, #0x648]
    //     0x72a0a8: cmp             w10, w16
    //     0x72a0ac: b.ne            #0x72a0e0
    //     0x72a0b0: add             w2, w7, #0xa
    //     0x72a0b4: add             x16, x4, w2, sxtw #1
    //     0x72a0b8: ldur            w7, [x16, #0xf]
    //     0x72a0bc: add             x7, x7, HEAP, lsl #32
    //     0x72a0c0: sub             w2, w1, w7
    //     0x72a0c4: add             x7, fp, w2, sxtw #2
    //     0x72a0c8: ldr             x7, [x7, #8]
    //     0x72a0cc: add             w2, w6, #2
    //     0x72a0d0: sbfx            x6, x2, #1, #0x1f
    //     0x72a0d4: mov             x2, x6
    //     0x72a0d8: mov             x6, x7
    //     0x72a0dc: b               #0x72a0e4
    //     0x72a0e0: mov             x6, NULL
    //     0x72a0e4: lsl             x7, x2, #1
    //     0x72a0e8: lsl             w9, w7, #1
    //     0x72a0ec: add             w10, w9, #8
    //     0x72a0f0: add             x16, x4, w10, sxtw #1
    //     0x72a0f4: ldur            w11, [x16, #0xf]
    //     0x72a0f8: add             x11, x11, HEAP, lsl #32
    //     0x72a0fc: add             x16, PP, #0xe, lsl #12  ; [pp+0xe8a8] "headers"
    //     0x72a100: ldr             x16, [x16, #0x8a8]
    //     0x72a104: cmp             w11, w16
    //     0x72a108: b.ne            #0x72a13c
    //     0x72a10c: add             w2, w9, #0xa
    //     0x72a110: add             x16, x4, w2, sxtw #1
    //     0x72a114: ldur            w9, [x16, #0xf]
    //     0x72a118: add             x9, x9, HEAP, lsl #32
    //     0x72a11c: sub             w2, w1, w9
    //     0x72a120: add             x9, fp, w2, sxtw #2
    //     0x72a124: ldr             x9, [x9, #8]
    //     0x72a128: add             w2, w7, #2
    //     0x72a12c: sbfx            x7, x2, #1, #0x1f
    //     0x72a130: mov             x2, x7
    //     0x72a134: mov             x7, x9
    //     0x72a138: b               #0x72a140
    //     0x72a13c: mov             x7, NULL
    //     0x72a140: lsl             x9, x2, #1
    //     0x72a144: lsl             w10, w9, #1
    //     0x72a148: add             w11, w10, #8
    //     0x72a14c: add             x16, x4, w11, sxtw #1
    //     0x72a150: ldur            w12, [x16, #0xf]
    //     0x72a154: add             x12, x12, HEAP, lsl #32
    //     0x72a158: add             x16, PP, #0xf, lsl #12  ; [pp+0xf650] "listFormat"
    //     0x72a15c: ldr             x16, [x16, #0x650]
    //     0x72a160: cmp             w12, w16
    //     0x72a164: b.ne            #0x72a198
    //     0x72a168: add             w2, w10, #0xa
    //     0x72a16c: add             x16, x4, w2, sxtw #1
    //     0x72a170: ldur            w10, [x16, #0xf]
    //     0x72a174: add             x10, x10, HEAP, lsl #32
    //     0x72a178: sub             w2, w1, w10
    //     0x72a17c: add             x10, fp, w2, sxtw #2
    //     0x72a180: ldr             x10, [x10, #8]
    //     0x72a184: add             w2, w9, #2
    //     0x72a188: sbfx            x9, x2, #1, #0x1f
    //     0x72a18c: mov             x2, x9
    //     0x72a190: mov             x9, x10
    //     0x72a194: b               #0x72a19c
    //     0x72a198: mov             x9, NULL
    //     0x72a19c: lsl             x10, x2, #1
    //     0x72a1a0: lsl             w11, w10, #1
    //     0x72a1a4: add             w12, w11, #8
    //     0x72a1a8: add             x16, x4, w12, sxtw #1
    //     0x72a1ac: ldur            w13, [x16, #0xf]
    //     0x72a1b0: add             x13, x13, HEAP, lsl #32
    //     0x72a1b4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf658] "maxRedirects"
    //     0x72a1b8: ldr             x16, [x16, #0x658]
    //     0x72a1bc: cmp             w13, w16
    //     0x72a1c0: b.ne            #0x72a1f4
    //     0x72a1c4: add             w2, w11, #0xa
    //     0x72a1c8: add             x16, x4, w2, sxtw #1
    //     0x72a1cc: ldur            w11, [x16, #0xf]
    //     0x72a1d0: add             x11, x11, HEAP, lsl #32
    //     0x72a1d4: sub             w2, w1, w11
    //     0x72a1d8: add             x11, fp, w2, sxtw #2
    //     0x72a1dc: ldr             x11, [x11, #8]
    //     0x72a1e0: add             w2, w10, #2
    //     0x72a1e4: sbfx            x10, x2, #1, #0x1f
    //     0x72a1e8: mov             x2, x10
    //     0x72a1ec: mov             x10, x11
    //     0x72a1f0: b               #0x72a1f8
    //     0x72a1f4: mov             x10, NULL
    //     0x72a1f8: lsl             x11, x2, #1
    //     0x72a1fc: lsl             w12, w11, #1
    //     0x72a200: add             w13, w12, #8
    //     0x72a204: add             x16, x4, w13, sxtw #1
    //     0x72a208: ldur            w14, [x16, #0xf]
    //     0x72a20c: add             x14, x14, HEAP, lsl #32
    //     0x72a210: add             x16, PP, #0xd, lsl #12  ; [pp+0xd660] "method"
    //     0x72a214: ldr             x16, [x16, #0x660]
    //     0x72a218: cmp             w14, w16
    //     0x72a21c: b.ne            #0x72a250
    //     0x72a220: add             w2, w12, #0xa
    //     0x72a224: add             x16, x4, w2, sxtw #1
    //     0x72a228: ldur            w12, [x16, #0xf]
    //     0x72a22c: add             x12, x12, HEAP, lsl #32
    //     0x72a230: sub             w2, w1, w12
    //     0x72a234: add             x12, fp, w2, sxtw #2
    //     0x72a238: ldr             x12, [x12, #8]
    //     0x72a23c: add             w2, w11, #2
    //     0x72a240: sbfx            x11, x2, #1, #0x1f
    //     0x72a244: mov             x2, x11
    //     0x72a248: mov             x11, x12
    //     0x72a24c: b               #0x72a254
    //     0x72a250: mov             x11, NULL
    //     0x72a254: lsl             x12, x2, #1
    //     0x72a258: lsl             w13, w12, #1
    //     0x72a25c: add             w14, w13, #8
    //     0x72a260: add             x16, x4, w14, sxtw #1
    //     0x72a264: ldur            w19, [x16, #0xf]
    //     0x72a268: add             x19, x19, HEAP, lsl #32
    //     0x72a26c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf660] "persistentConnection"
    //     0x72a270: ldr             x16, [x16, #0x660]
    //     0x72a274: cmp             w19, w16
    //     0x72a278: b.ne            #0x72a2ac
    //     0x72a27c: add             w2, w13, #0xa
    //     0x72a280: add             x16, x4, w2, sxtw #1
    //     0x72a284: ldur            w13, [x16, #0xf]
    //     0x72a288: add             x13, x13, HEAP, lsl #32
    //     0x72a28c: sub             w2, w1, w13
    //     0x72a290: add             x13, fp, w2, sxtw #2
    //     0x72a294: ldr             x13, [x13, #8]
    //     0x72a298: add             w2, w12, #2
    //     0x72a29c: sbfx            x12, x2, #1, #0x1f
    //     0x72a2a0: mov             x2, x12
    //     0x72a2a4: mov             x12, x13
    //     0x72a2a8: b               #0x72a2b0
    //     0x72a2ac: mov             x12, NULL
    //     0x72a2b0: lsl             x13, x2, #1
    //     0x72a2b4: lsl             w14, w13, #1
    //     0x72a2b8: add             w19, w14, #8
    //     0x72a2bc: add             x16, x4, w19, sxtw #1
    //     0x72a2c0: ldur            w20, [x16, #0xf]
    //     0x72a2c4: add             x20, x20, HEAP, lsl #32
    //     0x72a2c8: ldr             x16, [PP, #0x3658]  ; [pp+0x3658] "queryParameters"
    //     0x72a2cc: cmp             w20, w16
    //     0x72a2d0: b.ne            #0x72a304
    //     0x72a2d4: add             w2, w14, #0xa
    //     0x72a2d8: add             x16, x4, w2, sxtw #1
    //     0x72a2dc: ldur            w14, [x16, #0xf]
    //     0x72a2e0: add             x14, x14, HEAP, lsl #32
    //     0x72a2e4: sub             w2, w1, w14
    //     0x72a2e8: add             x14, fp, w2, sxtw #2
    //     0x72a2ec: ldr             x14, [x14, #8]
    //     0x72a2f0: add             w2, w13, #2
    //     0x72a2f4: sbfx            x13, x2, #1, #0x1f
    //     0x72a2f8: mov             x2, x13
    //     0x72a2fc: mov             x13, x14
    //     0x72a300: b               #0x72a308
    //     0x72a304: mov             x13, NULL
    //     0x72a308: stur            x13, [fp, #-0x10]
    //     0x72a30c: lsl             x14, x2, #1
    //     0x72a310: lsl             w19, w14, #1
    //     0x72a314: add             w20, w19, #8
    //     0x72a318: add             x16, x4, w20, sxtw #1
    //     0x72a31c: ldur            w23, [x16, #0xf]
    //     0x72a320: add             x23, x23, HEAP, lsl #32
    //     0x72a324: add             x16, PP, #0xf, lsl #12  ; [pp+0xf668] "receiveDataWhenStatusError"
    //     0x72a328: ldr             x16, [x16, #0x668]
    //     0x72a32c: cmp             w23, w16
    //     0x72a330: b.ne            #0x72a364
    //     0x72a334: add             w2, w19, #0xa
    //     0x72a338: add             x16, x4, w2, sxtw #1
    //     0x72a33c: ldur            w19, [x16, #0xf]
    //     0x72a340: add             x19, x19, HEAP, lsl #32
    //     0x72a344: sub             w2, w1, w19
    //     0x72a348: add             x19, fp, w2, sxtw #2
    //     0x72a34c: ldr             x19, [x19, #8]
    //     0x72a350: add             w2, w14, #2
    //     0x72a354: sbfx            x14, x2, #1, #0x1f
    //     0x72a358: mov             x2, x14
    //     0x72a35c: mov             x14, x19
    //     0x72a360: b               #0x72a368
    //     0x72a364: mov             x14, NULL
    //     0x72a368: lsl             x19, x2, #1
    //     0x72a36c: lsl             w20, w19, #1
    //     0x72a370: add             w23, w20, #8
    //     0x72a374: add             x16, x4, w23, sxtw #1
    //     0x72a378: ldur            w24, [x16, #0xf]
    //     0x72a37c: add             x24, x24, HEAP, lsl #32
    //     0x72a380: add             x16, PP, #0xf, lsl #12  ; [pp+0xf670] "receiveTimeout"
    //     0x72a384: ldr             x16, [x16, #0x670]
    //     0x72a388: cmp             w24, w16
    //     0x72a38c: b.ne            #0x72a3c0
    //     0x72a390: add             w2, w20, #0xa
    //     0x72a394: add             x16, x4, w2, sxtw #1
    //     0x72a398: ldur            w20, [x16, #0xf]
    //     0x72a39c: add             x20, x20, HEAP, lsl #32
    //     0x72a3a0: sub             w2, w1, w20
    //     0x72a3a4: add             x20, fp, w2, sxtw #2
    //     0x72a3a8: ldr             x20, [x20, #8]
    //     0x72a3ac: add             w2, w19, #2
    //     0x72a3b0: sbfx            x19, x2, #1, #0x1f
    //     0x72a3b4: mov             x2, x19
    //     0x72a3b8: mov             x19, x20
    //     0x72a3bc: b               #0x72a3c4
    //     0x72a3c0: mov             x19, NULL
    //     0x72a3c4: lsl             x20, x2, #1
    //     0x72a3c8: lsl             w23, w20, #1
    //     0x72a3cc: add             w24, w23, #8
    //     0x72a3d0: add             x16, x4, w24, sxtw #1
    //     0x72a3d4: ldur            w25, [x16, #0xf]
    //     0x72a3d8: add             x25, x25, HEAP, lsl #32
    //     0x72a3dc: add             x16, PP, #0xf, lsl #12  ; [pp+0xf678] "responseType"
    //     0x72a3e0: ldr             x16, [x16, #0x678]
    //     0x72a3e4: cmp             w25, w16
    //     0x72a3e8: b.ne            #0x72a41c
    //     0x72a3ec: add             w2, w23, #0xa
    //     0x72a3f0: add             x16, x4, w2, sxtw #1
    //     0x72a3f4: ldur            w23, [x16, #0xf]
    //     0x72a3f8: add             x23, x23, HEAP, lsl #32
    //     0x72a3fc: sub             w2, w1, w23
    //     0x72a400: add             x23, fp, w2, sxtw #2
    //     0x72a404: ldr             x23, [x23, #8]
    //     0x72a408: add             w2, w20, #2
    //     0x72a40c: sbfx            x20, x2, #1, #0x1f
    //     0x72a410: mov             x2, x20
    //     0x72a414: mov             x20, x23
    //     0x72a418: b               #0x72a424
    //     0x72a41c: add             x20, PP, #0xf, lsl #12  ; [pp+0xf680] Obj!ResponseType@e375a1
    //     0x72a420: ldr             x20, [x20, #0x680]
    //     0x72a424: lsl             x23, x2, #1
    //     0x72a428: lsl             w24, w23, #1
    //     0x72a42c: add             w25, w24, #8
    //     0x72a430: add             x16, x4, w25, sxtw #1
    //     0x72a434: ldur            w0, [x16, #0xf]
    //     0x72a438: add             x0, x0, HEAP, lsl #32
    //     0x72a43c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf688] "sendTimeout"
    //     0x72a440: ldr             x16, [x16, #0x688]
    //     0x72a444: cmp             w0, w16
    //     0x72a448: b.ne            #0x72a478
    //     0x72a44c: add             w0, w24, #0xa
    //     0x72a450: add             x16, x4, w0, sxtw #1
    //     0x72a454: ldur            w2, [x16, #0xf]
    //     0x72a458: add             x2, x2, HEAP, lsl #32
    //     0x72a45c: sub             w0, w1, w2
    //     0x72a460: add             x2, fp, w0, sxtw #2
    //     0x72a464: ldr             x2, [x2, #8]
    //     0x72a468: add             w0, w23, #2
    //     0x72a46c: sbfx            x23, x0, #1, #0x1f
    //     0x72a470: mov             x0, x23
    //     0x72a474: b               #0x72a480
    //     0x72a478: mov             x0, x2
    //     0x72a47c: mov             x2, NULL
    //     0x72a480: lsl             x23, x0, #1
    //     0x72a484: lsl             w0, w23, #1
    //     0x72a488: add             w23, w0, #8
    //     0x72a48c: add             x16, x4, w23, sxtw #1
    //     0x72a490: ldur            w24, [x16, #0xf]
    //     0x72a494: add             x24, x24, HEAP, lsl #32
    //     0x72a498: add             x16, PP, #0xf, lsl #12  ; [pp+0xf690] "validateStatus"
    //     0x72a49c: ldr             x16, [x16, #0x690]
    //     0x72a4a0: cmp             w24, w16
    //     0x72a4a4: b.ne            #0x72a4c8
    //     0x72a4a8: add             w23, w0, #0xa
    //     0x72a4ac: add             x16, x4, w23, sxtw #1
    //     0x72a4b0: ldur            w0, [x16, #0xf]
    //     0x72a4b4: add             x0, x0, HEAP, lsl #32
    //     0x72a4b8: sub             w4, w1, w0
    //     0x72a4bc: add             x0, fp, w4, sxtw #2
    //     0x72a4c0: ldr             x0, [x0, #8]
    //     0x72a4c4: b               #0x72a4cc
    //     0x72a4c8: mov             x0, NULL
    // 0x72a4cc: CheckStackOverflow
    //     0x72a4cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72a4d0: cmp             SP, x16
    //     0x72a4d4: b.ls            #0x72a568
    // 0x72a4d8: stp             x11, x10, [SP, #0x38]
    // 0x72a4dc: r16 = false
    //     0x72a4dc: add             x16, NULL, #0x30  ; false
    // 0x72a4e0: stp             x16, x12, [SP, #0x28]
    // 0x72a4e4: stp             x19, x14, [SP, #0x18]
    // 0x72a4e8: stp             x2, x20, [SP, #8]
    // 0x72a4ec: str             x0, [SP]
    // 0x72a4f0: ldur            x1, [fp, #-8]
    // 0x72a4f4: mov             x2, x3
    // 0x72a4f8: mov             x3, x5
    // 0x72a4fc: mov             x5, x6
    // 0x72a500: mov             x6, x7
    // 0x72a504: mov             x7, x9
    // 0x72a508: r0 = _BaseOptions&_RequestConfig&OptionsMixin()
    //     0x72a508: bl              #0x7295e8  ; [package:dio/src/options.dart] _BaseOptions&_RequestConfig&OptionsMixin::_BaseOptions&_RequestConfig&OptionsMixin
    // 0x72a50c: ldur            x1, [fp, #-8]
    // 0x72a510: ldur            x2, [fp, #-0x18]
    // 0x72a514: r0 = baseUrl=()
    //     0x72a514: bl              #0x729508  ; [package:dio/src/options.dart] _BaseOptions&_RequestConfig&OptionsMixin::baseUrl=
    // 0x72a518: ldur            x0, [fp, #-0x10]
    // 0x72a51c: cmp             w0, NULL
    // 0x72a520: b.ne            #0x72a534
    // 0x72a524: r16 = <String, dynamic>
    //     0x72a524: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x72a528: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x72a52c: stp             lr, x16, [SP]
    // 0x72a530: r0 = Map._fromLiteral()
    //     0x72a530: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x72a534: ldur            x1, [fp, #-8]
    // 0x72a538: StoreField: r1->field_4b = r0
    //     0x72a538: stur            w0, [x1, #0x4b]
    //     0x72a53c: ldurb           w16, [x1, #-1]
    //     0x72a540: ldurb           w17, [x0, #-1]
    //     0x72a544: and             x16, x17, x16, lsr #2
    //     0x72a548: tst             x16, HEAP, lsr #32
    //     0x72a54c: b.eq            #0x72a554
    //     0x72a550: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x72a554: StoreField: r1->field_4f = rNULL
    //     0x72a554: stur            NULL, [x1, #0x4f]
    // 0x72a558: r0 = Null
    //     0x72a558: mov             x0, NULL
    // 0x72a55c: LeaveFrame
    //     0x72a55c: mov             SP, fp
    //     0x72a560: ldp             fp, lr, [SP], #0x10
    // 0x72a564: ret
    //     0x72a564: ret             
    // 0x72a568: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72a568: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72a56c: b               #0x72a4d8
  }
}

// class id: 5625, size: 0x8, field offset: 0x8
abstract class OptionsMixin extends Object {
}

// class id: 7112, size: 0x14, field offset: 0x14
enum ListFormat extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc47eb0, size: 0x64
    // 0xc47eb0: EnterFrame
    //     0xc47eb0: stp             fp, lr, [SP, #-0x10]!
    //     0xc47eb4: mov             fp, SP
    // 0xc47eb8: AllocStack(0x10)
    //     0xc47eb8: sub             SP, SP, #0x10
    // 0xc47ebc: SetupParameters(ListFormat this /* r1 => r0, fp-0x8 */)
    //     0xc47ebc: mov             x0, x1
    //     0xc47ec0: stur            x1, [fp, #-8]
    // 0xc47ec4: CheckStackOverflow
    //     0xc47ec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc47ec8: cmp             SP, x16
    //     0xc47ecc: b.ls            #0xc47f0c
    // 0xc47ed0: r1 = Null
    //     0xc47ed0: mov             x1, NULL
    // 0xc47ed4: r2 = 4
    //     0xc47ed4: movz            x2, #0x4
    // 0xc47ed8: r0 = AllocateArray()
    //     0xc47ed8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc47edc: r16 = "ListFormat."
    //     0xc47edc: add             x16, PP, #0x21, lsl #12  ; [pp+0x21ec0] "ListFormat."
    //     0xc47ee0: ldr             x16, [x16, #0xec0]
    // 0xc47ee4: StoreField: r0->field_f = r16
    //     0xc47ee4: stur            w16, [x0, #0xf]
    // 0xc47ee8: ldur            x1, [fp, #-8]
    // 0xc47eec: LoadField: r2 = r1->field_f
    //     0xc47eec: ldur            w2, [x1, #0xf]
    // 0xc47ef0: DecompressPointer r2
    //     0xc47ef0: add             x2, x2, HEAP, lsl #32
    // 0xc47ef4: StoreField: r0->field_13 = r2
    //     0xc47ef4: stur            w2, [x0, #0x13]
    // 0xc47ef8: str             x0, [SP]
    // 0xc47efc: r0 = _interpolate()
    //     0xc47efc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc47f00: LeaveFrame
    //     0xc47f00: mov             SP, fp
    //     0xc47f04: ldp             fp, lr, [SP], #0x10
    // 0xc47f08: ret
    //     0xc47f08: ret             
    // 0xc47f0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc47f0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc47f10: b               #0xc47ed0
  }
}

// class id: 7113, size: 0x14, field offset: 0x14
enum ResponseType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc47e4c, size: 0x64
    // 0xc47e4c: EnterFrame
    //     0xc47e4c: stp             fp, lr, [SP, #-0x10]!
    //     0xc47e50: mov             fp, SP
    // 0xc47e54: AllocStack(0x10)
    //     0xc47e54: sub             SP, SP, #0x10
    // 0xc47e58: SetupParameters(ResponseType this /* r1 => r0, fp-0x8 */)
    //     0xc47e58: mov             x0, x1
    //     0xc47e5c: stur            x1, [fp, #-8]
    // 0xc47e60: CheckStackOverflow
    //     0xc47e60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc47e64: cmp             SP, x16
    //     0xc47e68: b.ls            #0xc47ea8
    // 0xc47e6c: r1 = Null
    //     0xc47e6c: mov             x1, NULL
    // 0xc47e70: r2 = 4
    //     0xc47e70: movz            x2, #0x4
    // 0xc47e74: r0 = AllocateArray()
    //     0xc47e74: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc47e78: r16 = "ResponseType."
    //     0xc47e78: add             x16, PP, #0x21, lsl #12  ; [pp+0x21ec8] "ResponseType."
    //     0xc47e7c: ldr             x16, [x16, #0xec8]
    // 0xc47e80: StoreField: r0->field_f = r16
    //     0xc47e80: stur            w16, [x0, #0xf]
    // 0xc47e84: ldur            x1, [fp, #-8]
    // 0xc47e88: LoadField: r2 = r1->field_f
    //     0xc47e88: ldur            w2, [x1, #0xf]
    // 0xc47e8c: DecompressPointer r2
    //     0xc47e8c: add             x2, x2, HEAP, lsl #32
    // 0xc47e90: StoreField: r0->field_13 = r2
    //     0xc47e90: stur            w2, [x0, #0x13]
    // 0xc47e94: str             x0, [SP]
    // 0xc47e98: r0 = _interpolate()
    //     0xc47e98: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc47e9c: LeaveFrame
    //     0xc47e9c: mov             SP, fp
    //     0xc47ea0: ldp             fp, lr, [SP], #0x10
    // 0xc47ea4: ret
    //     0xc47ea4: ret             
    // 0xc47ea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc47ea8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc47eac: b               #0xc47e6c
  }
}
