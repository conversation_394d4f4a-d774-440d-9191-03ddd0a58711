// lib: , url: package:dio/src/transformers/fused_transformer.dart

// class id: 1048703, size: 0x8
class :: {
}

// class id: 5617, size: 0x10, field offset: 0x8
class FusedTransformer extends Transformer {

  static late final Converter<List<int>, Object?> _utf8JsonDecoder; // offset: 0xd34

  _ transformResponse(/* No info */) async {
    // ** addr: 0x702af8, size: 0x18c
    // 0x702af8: EnterFrame
    //     0x702af8: stp             fp, lr, [SP, #-0x10]!
    //     0x702afc: mov             fp, SP
    // 0x702b00: AllocStack(0x30)
    //     0x702b00: sub             SP, SP, #0x30
    // 0x702b04: SetupParameters(FusedTransformer this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r1, fp-0x20 */)
    //     0x702b04: stur            NULL, [fp, #-8]
    //     0x702b08: stur            x1, [fp, #-0x10]
    //     0x702b0c: mov             x16, x3
    //     0x702b10: mov             x3, x1
    //     0x702b14: mov             x1, x16
    //     0x702b18: stur            x2, [fp, #-0x18]
    //     0x702b1c: stur            x1, [fp, #-0x20]
    // 0x702b20: CheckStackOverflow
    //     0x702b20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x702b24: cmp             SP, x16
    //     0x702b28: b.ls            #0x702c70
    // 0x702b2c: InitAsync() -> Future
    //     0x702b2c: mov             x0, NULL
    //     0x702b30: bl              #0x661298  ; InitAsyncStub
    // 0x702b34: ldur            x0, [fp, #-0x18]
    // 0x702b38: LoadField: r3 = r0->field_1f
    //     0x702b38: ldur            w3, [x0, #0x1f]
    // 0x702b3c: DecompressPointer r3
    //     0x702b3c: add             x3, x3, HEAP, lsl #32
    // 0x702b40: r16 = Sentinel
    //     0x702b40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x702b44: cmp             w3, w16
    // 0x702b48: b.eq            #0x702c78
    // 0x702b4c: stur            x3, [fp, #-0x28]
    // 0x702b50: r16 = Instance_ResponseType
    //     0x702b50: add             x16, PP, #0x10, lsl #12  ; [pp+0x10460] Obj!ResponseType@e37561
    //     0x702b54: ldr             x16, [x16, #0x460]
    // 0x702b58: cmp             w3, w16
    // 0x702b5c: b.ne            #0x702b68
    // 0x702b60: ldur            x0, [fp, #-0x20]
    // 0x702b64: r0 = ReturnAsyncNotFuture()
    //     0x702b64: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x702b68: r16 = Instance_ResponseType
    //     0x702b68: add             x16, PP, #0x10, lsl #12  ; [pp+0x10458] Obj!ResponseType@e37581
    //     0x702b6c: ldr             x16, [x16, #0x458]
    // 0x702b70: cmp             w3, w16
    // 0x702b74: b.ne            #0x702b8c
    // 0x702b78: ldur            x4, [fp, #-0x20]
    // 0x702b7c: LoadField: r1 = r4->field_b
    //     0x702b7c: ldur            w1, [x4, #0xb]
    // 0x702b80: DecompressPointer r1
    //     0x702b80: add             x1, x1, HEAP, lsl #32
    // 0x702b84: r0 = consolidateBytes()
    //     0x702b84: bl              #0x7065bc  ; [package:dio/src/transformers/util/consolidate_bytes.dart] ::consolidateBytes
    // 0x702b88: r0 = ReturnAsync()
    //     0x702b88: b               #0x6576a4  ; ReturnAsyncStub
    // 0x702b8c: ldur            x4, [fp, #-0x20]
    // 0x702b90: LoadField: r1 = r4->field_1f
    //     0x702b90: ldur            w1, [x4, #0x1f]
    // 0x702b94: DecompressPointer r1
    //     0x702b94: add             x1, x1, HEAP, lsl #32
    // 0x702b98: r0 = LoadClassIdInstr(r1)
    //     0x702b98: ldur            x0, [x1, #-1]
    //     0x702b9c: ubfx            x0, x0, #0xc, #0x14
    // 0x702ba0: r2 = "content-type"
    //     0x702ba0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf6a8] "content-type"
    //     0x702ba4: ldr             x2, [x2, #0x6a8]
    // 0x702ba8: r0 = GDT[cid_x0 + -0x114]()
    //     0x702ba8: sub             lr, x0, #0x114
    //     0x702bac: ldr             lr, [x21, lr, lsl #3]
    //     0x702bb0: blr             lr
    // 0x702bb4: cmp             w0, NULL
    // 0x702bb8: b.ne            #0x702bc4
    // 0x702bbc: r1 = Null
    //     0x702bbc: mov             x1, NULL
    // 0x702bc0: b               #0x702bec
    // 0x702bc4: r1 = LoadClassIdInstr(r0)
    //     0x702bc4: ldur            x1, [x0, #-1]
    //     0x702bc8: ubfx            x1, x1, #0xc, #0x14
    // 0x702bcc: mov             x16, x0
    // 0x702bd0: mov             x0, x1
    // 0x702bd4: mov             x1, x16
    // 0x702bd8: r0 = GDT[cid_x0 + 0xd20f]()
    //     0x702bd8: movz            x17, #0xd20f
    //     0x702bdc: add             lr, x0, x17
    //     0x702be0: ldr             lr, [x21, lr, lsl #3]
    //     0x702be4: blr             lr
    // 0x702be8: mov             x1, x0
    // 0x702bec: r0 = isJsonMimeType()
    //     0x702bec: bl              #0x7049a4  ; [package:dio/src/transformer.dart] Transformer::isJsonMimeType
    // 0x702bf0: tbnz            w0, #4, #0x702c18
    // 0x702bf4: ldur            x0, [fp, #-0x28]
    // 0x702bf8: r16 = Instance_ResponseType
    //     0x702bf8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf680] Obj!ResponseType@e375a1
    //     0x702bfc: ldr             x16, [x16, #0x680]
    // 0x702c00: cmp             w0, w16
    // 0x702c04: r16 = true
    //     0x702c04: add             x16, NULL, #0x20  ; true
    // 0x702c08: r17 = false
    //     0x702c08: add             x17, NULL, #0x30  ; false
    // 0x702c0c: csel            x1, x16, x17, eq
    // 0x702c10: mov             x0, x1
    // 0x702c14: b               #0x702c1c
    // 0x702c18: r0 = false
    //     0x702c18: add             x0, NULL, #0x30  ; false
    // 0x702c1c: stur            x0, [fp, #-0x18]
    // 0x702c20: tbnz            w0, #4, #0x702c34
    // 0x702c24: ldur            x1, [fp, #-0x10]
    // 0x702c28: ldur            x2, [fp, #-0x20]
    // 0x702c2c: r0 = _fastUtf8JsonDecode()
    //     0x702c2c: bl              #0x702c84  ; [package:dio/src/transformers/fused_transformer.dart] FusedTransformer::_fastUtf8JsonDecode
    // 0x702c30: r0 = ReturnAsync()
    //     0x702c30: b               #0x6576a4  ; ReturnAsyncStub
    // 0x702c34: ldur            x1, [fp, #-0x20]
    // 0x702c38: LoadField: r2 = r1->field_b
    //     0x702c38: ldur            w2, [x1, #0xb]
    // 0x702c3c: DecompressPointer r2
    //     0x702c3c: add             x2, x2, HEAP, lsl #32
    // 0x702c40: mov             x1, x2
    // 0x702c44: r0 = consolidateBytes()
    //     0x702c44: bl              #0x7065bc  ; [package:dio/src/transformers/util/consolidate_bytes.dart] ::consolidateBytes
    // 0x702c48: mov             x1, x0
    // 0x702c4c: stur            x1, [fp, #-0x10]
    // 0x702c50: r0 = Await()
    //     0x702c50: bl              #0x661044  ; AwaitStub
    // 0x702c54: r16 = true
    //     0x702c54: add             x16, NULL, #0x20  ; true
    // 0x702c58: str             x16, [SP]
    // 0x702c5c: mov             x2, x0
    // 0x702c60: r1 = Instance_Utf8Codec
    //     0x702c60: ldr             x1, [PP, #0x200]  ; [pp+0x200] Obj!Utf8Codec@e2ccf1
    // 0x702c64: r4 = const [0, 0x3, 0x1, 0x2, allowMalformed, 0x2, null]
    //     0x702c64: ldr             x4, [PP, #0x3368]  ; [pp+0x3368] List(7) [0, 0x3, 0x1, 0x2, "allowMalformed", 0x2, Null]
    // 0x702c68: r0 = decode()
    //     0x702c68: bl              #0x60b038  ; [dart:convert] Utf8Codec::decode
    // 0x702c6c: r0 = ReturnAsyncNotFuture()
    //     0x702c6c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x702c70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x702c70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x702c74: b               #0x702b2c
    // 0x702c78: r9 = responseType
    //     0x702c78: add             x9, PP, #0xf, lsl #12  ; [pp+0xf600] Field <<EMAIL>>: late (offset: 0x20)
    //     0x702c7c: ldr             x9, [x9, #0x600]
    // 0x702c80: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x702c80: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _fastUtf8JsonDecode(/* No info */) async {
    // ** addr: 0x702c84, size: 0x28c
    // 0x702c84: EnterFrame
    //     0x702c84: stp             fp, lr, [SP, #-0x10]!
    //     0x702c88: mov             fp, SP
    // 0x702c8c: AllocStack(0x40)
    //     0x702c8c: sub             SP, SP, #0x40
    // 0x702c90: SetupParameters(FusedTransformer this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x702c90: stur            NULL, [fp, #-8]
    //     0x702c94: stur            x1, [fp, #-0x10]
    //     0x702c98: stur            x2, [fp, #-0x18]
    // 0x702c9c: CheckStackOverflow
    //     0x702c9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x702ca0: cmp             SP, x16
    //     0x702ca4: b.ls            #0x702f08
    // 0x702ca8: InitAsync() -> Future<Object?>
    //     0x702ca8: ldr             x0, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    //     0x702cac: bl              #0x661298  ; InitAsyncStub
    // 0x702cb0: ldur            x3, [fp, #-0x18]
    // 0x702cb4: LoadField: r1 = r3->field_1f
    //     0x702cb4: ldur            w1, [x3, #0x1f]
    // 0x702cb8: DecompressPointer r1
    //     0x702cb8: add             x1, x1, HEAP, lsl #32
    // 0x702cbc: r0 = LoadClassIdInstr(r1)
    //     0x702cbc: ldur            x0, [x1, #-1]
    //     0x702cc0: ubfx            x0, x0, #0xc, #0x14
    // 0x702cc4: r2 = "content-length"
    //     0x702cc4: add             x2, PP, #0x10, lsl #12  ; [pp+0x10648] "content-length"
    //     0x702cc8: ldr             x2, [x2, #0x648]
    // 0x702ccc: r0 = GDT[cid_x0 + -0x114]()
    //     0x702ccc: sub             lr, x0, #0x114
    //     0x702cd0: ldr             lr, [x21, lr, lsl #3]
    //     0x702cd4: blr             lr
    // 0x702cd8: mov             x2, x0
    // 0x702cdc: stur            x2, [fp, #-0x20]
    // 0x702ce0: cmp             w2, NULL
    // 0x702ce4: b.eq            #0x702d3c
    // 0x702ce8: r0 = LoadClassIdInstr(r2)
    //     0x702ce8: ldur            x0, [x2, #-1]
    //     0x702cec: ubfx            x0, x0, #0xc, #0x14
    // 0x702cf0: mov             x1, x2
    // 0x702cf4: r0 = GDT[cid_x0 + 0xd488]()
    //     0x702cf4: movz            x17, #0xd488
    //     0x702cf8: add             lr, x0, x17
    //     0x702cfc: ldr             lr, [x21, lr, lsl #3]
    //     0x702d00: blr             lr
    // 0x702d04: tbnz            w0, #4, #0x702d3c
    // 0x702d08: ldur            x1, [fp, #-0x20]
    // 0x702d0c: r0 = LoadClassIdInstr(r1)
    //     0x702d0c: ldur            x0, [x1, #-1]
    //     0x702d10: ubfx            x0, x0, #0xc, #0x14
    // 0x702d14: r0 = GDT[cid_x0 + 0xd20f]()
    //     0x702d14: movz            x17, #0xd20f
    //     0x702d18: add             lr, x0, x17
    //     0x702d1c: ldr             lr, [x21, lr, lsl #3]
    //     0x702d20: blr             lr
    // 0x702d24: mov             x1, x0
    // 0x702d28: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x702d28: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x702d2c: r0 = parse()
    //     0x702d2c: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x702d30: mov             x2, x0
    // 0x702d34: r1 = Null
    //     0x702d34: mov             x1, NULL
    // 0x702d38: b               #0x702d64
    // 0x702d3c: ldur            x0, [fp, #-0x18]
    // 0x702d40: LoadField: r1 = r0->field_b
    //     0x702d40: ldur            w1, [x0, #0xb]
    // 0x702d44: DecompressPointer r1
    //     0x702d44: add             x1, x1, HEAP, lsl #32
    // 0x702d48: r0 = consolidateBytes()
    //     0x702d48: bl              #0x7065bc  ; [package:dio/src/transformers/util/consolidate_bytes.dart] ::consolidateBytes
    // 0x702d4c: mov             x1, x0
    // 0x702d50: stur            x1, [fp, #-0x20]
    // 0x702d54: r0 = Await()
    //     0x702d54: bl              #0x661044  ; AwaitStub
    // 0x702d58: LoadField: r1 = r0->field_13
    //     0x702d58: ldur            w1, [x0, #0x13]
    // 0x702d5c: r2 = LoadInt32Instr(r1)
    //     0x702d5c: sbfx            x2, x1, #1, #0x1f
    // 0x702d60: mov             x1, x0
    // 0x702d64: ldur            x0, [fp, #-0x10]
    // 0x702d68: stur            x1, [fp, #-0x20]
    // 0x702d6c: LoadField: r3 = r0->field_7
    //     0x702d6c: ldur            x3, [x0, #7]
    // 0x702d70: tbnz            x3, #0x3f, #0x702de8
    // 0x702d74: cmp             x2, x3
    // 0x702d78: b.lt            #0x702de0
    // 0x702d7c: cmp             w1, NULL
    // 0x702d80: b.ne            #0x702da4
    // 0x702d84: ldur            x0, [fp, #-0x18]
    // 0x702d88: LoadField: r1 = r0->field_b
    //     0x702d88: ldur            w1, [x0, #0xb]
    // 0x702d8c: DecompressPointer r1
    //     0x702d8c: add             x1, x1, HEAP, lsl #32
    // 0x702d90: r0 = consolidateBytes()
    //     0x702d90: bl              #0x7065bc  ; [package:dio/src/transformers/util/consolidate_bytes.dart] ::consolidateBytes
    // 0x702d94: mov             x1, x0
    // 0x702d98: stur            x1, [fp, #-0x10]
    // 0x702d9c: r0 = Await()
    //     0x702d9c: bl              #0x661044  ; AwaitStub
    // 0x702da0: b               #0x702da8
    // 0x702da4: mov             x0, x1
    // 0x702da8: r16 = <Uint8List, Object?>
    //     0x702da8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10650] TypeArguments: <Uint8List, Object?>
    //     0x702dac: ldr             x16, [x16, #0x650]
    // 0x702db0: r30 = Closure: <Y0, Y1>((Y0) => FutureOr<Y1>, Y0, {String? debugLabel}) => Future<Y1> from Function 'compute': static.
    //     0x702db0: add             lr, PP, #0x10, lsl #12  ; [pp+0x10658] Closure: <Y0, Y1>((Y0) => FutureOr<Y1>, Y0, {String? debugLabel}) => Future<Y1> from Function 'compute': static. (0x7e54fb1034e4)
    //     0x702db4: ldr             lr, [lr, #0x658]
    // 0x702db8: stp             lr, x16, [SP, #0x10]
    // 0x702dbc: r16 = Closure: (Uint8List) => Future<Object?> from Function '_decodeUtf8ToJson@891206049': static.
    //     0x702dbc: add             x16, PP, #0x10, lsl #12  ; [pp+0x10660] Closure: (Uint8List) => Future<Object?> from Function '_decodeUtf8ToJson@891206049': static. (0x7e54fb103420)
    //     0x702dc0: ldr             x16, [x16, #0x660]
    // 0x702dc4: stp             x0, x16, [SP]
    // 0x702dc8: r0 = Closure: <Y0, Y1>((Y0) => FutureOr<Y1>, Y0, {String? debugLabel}) => Future<Y1> from Function 'compute': static.
    //     0x702dc8: add             x0, PP, #0x10, lsl #12  ; [pp+0x10658] Closure: <Y0, Y1>((Y0) => FutureOr<Y1>, Y0, {String? debugLabel}) => Future<Y1> from Function 'compute': static. (0x7e54fb1034e4)
    //     0x702dcc: ldr             x0, [x0, #0x658]
    // 0x702dd0: ClosureCall
    //     0x702dd0: ldr             x4, [PP, #0x3310]  ; [pp+0x3310] List(5) [0x2, 0x3, 0x3, 0x3, Null]
    //     0x702dd4: ldur            x2, [x0, #0x1f]
    //     0x702dd8: blr             x2
    // 0x702ddc: r0 = ReturnAsync()
    //     0x702ddc: b               #0x6576a4  ; ReturnAsyncStub
    // 0x702de0: ldur            x0, [fp, #-0x18]
    // 0x702de4: b               #0x702dec
    // 0x702de8: ldur            x0, [fp, #-0x18]
    // 0x702dec: cmp             w1, NULL
    // 0x702df0: b.eq            #0x702e50
    // 0x702df4: LoadField: r0 = r1->field_13
    //     0x702df4: ldur            w0, [x1, #0x13]
    // 0x702df8: cbnz            w0, #0x702e04
    // 0x702dfc: r0 = Null
    //     0x702dfc: mov             x0, NULL
    // 0x702e00: r0 = ReturnAsyncNotFuture()
    //     0x702e00: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x702e04: r0 = InitLateStaticField(0xd34) // [package:dio/src/transformers/fused_transformer.dart] FusedTransformer::_utf8JsonDecoder
    //     0x702e04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x702e08: ldr             x0, [x0, #0x1a68]
    //     0x702e0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x702e10: cmp             w0, w16
    //     0x702e14: b.ne            #0x702e24
    //     0x702e18: add             x2, PP, #0x10, lsl #12  ; [pp+0x10668] Field <FusedTransformer._utf8JsonDecoder@891206049>: static late final (offset: 0xd34)
    //     0x702e1c: ldr             x2, [x2, #0x668]
    //     0x702e20: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x702e24: r1 = LoadClassIdInstr(r0)
    //     0x702e24: ldur            x1, [x0, #-1]
    //     0x702e28: ubfx            x1, x1, #0xc, #0x14
    // 0x702e2c: mov             x16, x0
    // 0x702e30: mov             x0, x1
    // 0x702e34: mov             x1, x16
    // 0x702e38: ldur            x2, [fp, #-0x20]
    // 0x702e3c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x702e3c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x702e40: r0 = GDT[cid_x0 + 0x928]()
    //     0x702e40: add             lr, x0, #0x928
    //     0x702e44: ldr             lr, [x21, lr, lsl #3]
    //     0x702e48: blr             lr
    // 0x702e4c: r0 = ReturnAsync()
    //     0x702e4c: b               #0x6576a4  ; ReturnAsyncStub
    // 0x702e50: LoadField: r1 = r0->field_b
    //     0x702e50: ldur            w1, [x0, #0xb]
    // 0x702e54: DecompressPointer r1
    //     0x702e54: add             x1, x1, HEAP, lsl #32
    // 0x702e58: r16 = <Uint8List>
    //     0x702e58: ldr             x16, [PP, #0xf80]  ; [pp+0xf80] TypeArguments: <Uint8List>
    // 0x702e5c: stp             x1, x16, [SP, #8]
    // 0x702e60: r16 = Instance_DefaultNullIfEmptyStreamTransformer
    //     0x702e60: add             x16, PP, #0x10, lsl #12  ; [pp+0x10670] Obj!DefaultNullIfEmptyStreamTransformer@e2d061
    //     0x702e64: ldr             x16, [x16, #0x670]
    // 0x702e68: str             x16, [SP]
    // 0x702e6c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x702e6c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x702e70: r0 = transform()
    //     0x702e70: bl              #0x7031c8  ; [dart:async] Stream::transform
    // 0x702e74: stur            x0, [fp, #-0x10]
    // 0x702e78: r0 = InitLateStaticField(0xd34) // [package:dio/src/transformers/fused_transformer.dart] FusedTransformer::_utf8JsonDecoder
    //     0x702e78: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x702e7c: ldr             x0, [x0, #0x1a68]
    //     0x702e80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x702e84: cmp             w0, w16
    //     0x702e88: b.ne            #0x702e98
    //     0x702e8c: add             x2, PP, #0x10, lsl #12  ; [pp+0x10668] Field <FusedTransformer._utf8JsonDecoder@891206049>: static late final (offset: 0xd34)
    //     0x702e90: ldr             x2, [x2, #0x668]
    //     0x702e94: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x702e98: mov             x1, x0
    // 0x702e9c: ldur            x2, [fp, #-0x10]
    // 0x702ea0: r0 = bind()
    //     0x702ea0: bl              #0xcd6908  ; [dart:convert] Converter::bind
    // 0x702ea4: mov             x1, x0
    // 0x702ea8: r0 = toList()
    //     0x702ea8: bl              #0x702f10  ; [dart:async] Stream::toList
    // 0x702eac: mov             x1, x0
    // 0x702eb0: stur            x1, [fp, #-0x10]
    // 0x702eb4: r0 = Await()
    //     0x702eb4: bl              #0x661044  ; AwaitStub
    // 0x702eb8: mov             x2, x0
    // 0x702ebc: stur            x2, [fp, #-0x10]
    // 0x702ec0: r0 = LoadClassIdInstr(r2)
    //     0x702ec0: ldur            x0, [x2, #-1]
    //     0x702ec4: ubfx            x0, x0, #0xc, #0x14
    // 0x702ec8: mov             x1, x2
    // 0x702ecc: r0 = GDT[cid_x0 + 0xe879]()
    //     0x702ecc: movz            x17, #0xe879
    //     0x702ed0: add             lr, x0, x17
    //     0x702ed4: ldr             lr, [x21, lr, lsl #3]
    //     0x702ed8: blr             lr
    // 0x702edc: tbnz            w0, #4, #0x702ee8
    // 0x702ee0: r0 = Null
    //     0x702ee0: mov             x0, NULL
    // 0x702ee4: r0 = ReturnAsyncNotFuture()
    //     0x702ee4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x702ee8: ldur            x1, [fp, #-0x10]
    // 0x702eec: r0 = LoadClassIdInstr(r1)
    //     0x702eec: ldur            x0, [x1, #-1]
    //     0x702ef0: ubfx            x0, x0, #0xc, #0x14
    // 0x702ef4: r0 = GDT[cid_x0 + 0xd20f]()
    //     0x702ef4: movz            x17, #0xd20f
    //     0x702ef8: add             lr, x0, x17
    //     0x702efc: ldr             lr, [x21, lr, lsl #3]
    //     0x702f00: blr             lr
    // 0x702f04: r0 = ReturnAsync()
    //     0x702f04: b               #0x6576a4  ; ReturnAsyncStub
    // 0x702f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x702f08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x702f0c: b               #0x702ca8
  }
  static Converter<List<int>, Object?> _utf8JsonDecoder() {
    // ** addr: 0x7033ec, size: 0x28
    // 0x7033ec: EnterFrame
    //     0x7033ec: stp             fp, lr, [SP, #-0x10]!
    //     0x7033f0: mov             fp, SP
    // 0x7033f4: r1 = <List<int>, Object?>
    //     0x7033f4: add             x1, PP, #0x10, lsl #12  ; [pp+0x107a8] TypeArguments: <List<int>, Object?>
    //     0x7033f8: ldr             x1, [x1, #0x7a8]
    // 0x7033fc: r0 = _JsonUtf8Decoder()
    //     0x7033fc: bl              #0x703414  ; Allocate_JsonUtf8DecoderStub -> _JsonUtf8Decoder (size=0x14)
    // 0x703400: r1 = false
    //     0x703400: add             x1, NULL, #0x30  ; false
    // 0x703404: StoreField: r0->field_f = r1
    //     0x703404: stur            w1, [x0, #0xf]
    // 0x703408: LeaveFrame
    //     0x703408: mov             SP, fp
    //     0x70340c: ldp             fp, lr, [SP], #0x10
    // 0x703410: ret
    //     0x703410: ret             
  }
  [closure] static Future<Object?> _decodeUtf8ToJson(dynamic, Uint8List) {
    // ** addr: 0x703420, size: 0x30
    // 0x703420: EnterFrame
    //     0x703420: stp             fp, lr, [SP, #-0x10]!
    //     0x703424: mov             fp, SP
    // 0x703428: CheckStackOverflow
    //     0x703428: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x70342c: cmp             SP, x16
    //     0x703430: b.ls            #0x703448
    // 0x703434: ldr             x1, [fp, #0x10]
    // 0x703438: r0 = _decodeUtf8ToJson()
    //     0x703438: bl              #0x703450  ; [package:dio/src/transformers/fused_transformer.dart] FusedTransformer::_decodeUtf8ToJson
    // 0x70343c: LeaveFrame
    //     0x70343c: mov             SP, fp
    //     0x703440: ldp             fp, lr, [SP], #0x10
    // 0x703444: ret
    //     0x703444: ret             
    // 0x703448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x703448: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x70344c: b               #0x703434
  }
  static _ _decodeUtf8ToJson(/* No info */) async {
    // ** addr: 0x703450, size: 0x94
    // 0x703450: EnterFrame
    //     0x703450: stp             fp, lr, [SP, #-0x10]!
    //     0x703454: mov             fp, SP
    // 0x703458: AllocStack(0x10)
    //     0x703458: sub             SP, SP, #0x10
    // 0x70345c: SetupParameters(dynamic _ /* r1 => r2, fp-0x10 */)
    //     0x70345c: stur            NULL, [fp, #-8]
    //     0x703460: mov             x2, x1
    //     0x703464: stur            x1, [fp, #-0x10]
    // 0x703468: CheckStackOverflow
    //     0x703468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x70346c: cmp             SP, x16
    //     0x703470: b.ls            #0x7034dc
    // 0x703474: InitAsync() -> Future<Object?>
    //     0x703474: ldr             x0, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    //     0x703478: bl              #0x661298  ; InitAsyncStub
    // 0x70347c: ldur            x2, [fp, #-0x10]
    // 0x703480: LoadField: r0 = r2->field_13
    //     0x703480: ldur            w0, [x2, #0x13]
    // 0x703484: cbnz            w0, #0x703490
    // 0x703488: r0 = Null
    //     0x703488: mov             x0, NULL
    // 0x70348c: r0 = ReturnAsyncNotFuture()
    //     0x70348c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x703490: r0 = InitLateStaticField(0xd34) // [package:dio/src/transformers/fused_transformer.dart] FusedTransformer::_utf8JsonDecoder
    //     0x703490: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x703494: ldr             x0, [x0, #0x1a68]
    //     0x703498: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x70349c: cmp             w0, w16
    //     0x7034a0: b.ne            #0x7034b0
    //     0x7034a4: add             x2, PP, #0x10, lsl #12  ; [pp+0x10668] Field <FusedTransformer._utf8JsonDecoder@891206049>: static late final (offset: 0xd34)
    //     0x7034a8: ldr             x2, [x2, #0x668]
    //     0x7034ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7034b0: r1 = LoadClassIdInstr(r0)
    //     0x7034b0: ldur            x1, [x0, #-1]
    //     0x7034b4: ubfx            x1, x1, #0xc, #0x14
    // 0x7034b8: mov             x16, x0
    // 0x7034bc: mov             x0, x1
    // 0x7034c0: mov             x1, x16
    // 0x7034c4: ldur            x2, [fp, #-0x10]
    // 0x7034c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7034c8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7034cc: r0 = GDT[cid_x0 + 0x928]()
    //     0x7034cc: add             lr, x0, #0x928
    //     0x7034d0: ldr             lr, [x21, lr, lsl #3]
    //     0x7034d4: blr             lr
    // 0x7034d8: r0 = ReturnAsync()
    //     0x7034d8: b               #0x6576a4  ; ReturnAsyncStub
    // 0x7034dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7034dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7034e0: b               #0x703474
  }
  _ transformRequest(/* No info */) async {
    // ** addr: 0x7271c0, size: 0x4c
    // 0x7271c0: EnterFrame
    //     0x7271c0: stp             fp, lr, [SP, #-0x10]!
    //     0x7271c4: mov             fp, SP
    // 0x7271c8: AllocStack(0x18)
    //     0x7271c8: sub             SP, SP, #0x18
    // 0x7271cc: SetupParameters(FusedTransformer this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x7271cc: stur            NULL, [fp, #-8]
    //     0x7271d0: stur            x1, [fp, #-0x10]
    //     0x7271d4: mov             x16, x2
    //     0x7271d8: mov             x2, x1
    //     0x7271dc: mov             x1, x16
    //     0x7271e0: stur            x1, [fp, #-0x18]
    // 0x7271e4: CheckStackOverflow
    //     0x7271e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7271e8: cmp             SP, x16
    //     0x7271ec: b.ls            #0x727204
    // 0x7271f0: InitAsync() -> Future<String>
    //     0x7271f0: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    //     0x7271f4: bl              #0x661298  ; InitAsyncStub
    // 0x7271f8: ldur            x1, [fp, #-0x18]
    // 0x7271fc: r0 = defaultTransformRequest()
    //     0x7271fc: bl              #0x72720c  ; [package:dio/src/transformer.dart] Transformer::defaultTransformRequest
    // 0x727200: r0 = ReturnAsync()
    //     0x727200: b               #0x6576a4  ; ReturnAsyncStub
    // 0x727204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x727204: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x727208: b               #0x7271f0
  }
}
