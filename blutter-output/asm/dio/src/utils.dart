// lib: , url: package:dio/src/utils.dart

// class id: 1048706, size: 0x8
class :: {

  static Map<String, Y0> caseInsensitiveKeyMap<Y0>([Map<String, Y0>?]) {
    // ** addr: 0x700b44, size: 0x134
    // 0x700b44: EnterFrame
    //     0x700b44: stp             fp, lr, [SP, #-0x10]!
    //     0x700b48: mov             fp, SP
    // 0x700b4c: AllocStack(0x20)
    //     0x700b4c: sub             SP, SP, #0x20
    // 0x700b50: SetupParameters([dynamic _ = Null /* r0, fp-0x10 */])
    //     0x700b50: ldur            w0, [x4, #0x13]
    //     0x700b54: cmp             w0, #2
    //     0x700b58: b.lt            #0x700b6c
    //     0x700b5c: add             x1, fp, w0, sxtw #2
    //     0x700b60: ldr             x1, [x1, #8]
    //     0x700b64: mov             x0, x1
    //     0x700b68: b               #0x700b70
    //     0x700b6c: mov             x0, NULL
    //     0x700b70: stur            x0, [fp, #-0x10]
    //     0x700b74: ldur            w1, [x4, #0xf]
    //     0x700b78: cbnz            w1, #0x700b84
    //     0x700b7c: mov             x3, NULL
    //     0x700b80: b               #0x700b94
    //     0x700b84: ldur            w1, [x4, #0x17]
    //     0x700b88: add             x2, fp, w1, sxtw #2
    //     0x700b8c: ldr             x2, [x2, #0x10]
    //     0x700b90: mov             x3, x2
    //     0x700b94: stur            x3, [fp, #-8]
    // 0x700b98: CheckStackOverflow
    //     0x700b98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x700b9c: cmp             SP, x16
    //     0x700ba0: b.ls            #0x700c70
    // 0x700ba4: r1 = Function '<anonymous closure>': static.
    //     0x700ba4: add             x1, PP, #0xf, lsl #12  ; [pp+0xf6b8] AnonymousClosure: static (0x700fe8), in [package:dio/src/utils.dart] ::caseInsensitiveKeyMap (0x700b44)
    //     0x700ba8: ldr             x1, [x1, #0x6b8]
    // 0x700bac: r2 = Null
    //     0x700bac: mov             x2, NULL
    // 0x700bb0: r0 = AllocateClosure()
    //     0x700bb0: bl              #0xec1630  ; AllocateClosureStub
    // 0x700bb4: mov             x3, x0
    // 0x700bb8: ldur            x0, [fp, #-8]
    // 0x700bbc: stur            x3, [fp, #-0x18]
    // 0x700bc0: StoreField: r3->field_b = r0
    //     0x700bc0: stur            w0, [x3, #0xb]
    // 0x700bc4: r1 = Function '<anonymous closure>': static.
    //     0x700bc4: add             x1, PP, #0xf, lsl #12  ; [pp+0xf6c0] AnonymousClosure: static (0x700f7c), in [package:dio/src/utils.dart] ::caseInsensitiveKeyMap (0x700b44)
    //     0x700bc8: ldr             x1, [x1, #0x6c0]
    // 0x700bcc: r2 = Null
    //     0x700bcc: mov             x2, NULL
    // 0x700bd0: r0 = AllocateClosure()
    //     0x700bd0: bl              #0xec1630  ; AllocateClosureStub
    // 0x700bd4: ldur            x1, [fp, #-8]
    // 0x700bd8: stur            x0, [fp, #-0x20]
    // 0x700bdc: StoreField: r0->field_b = r1
    //     0x700bdc: stur            w1, [x0, #0xb]
    // 0x700be0: r2 = Null
    //     0x700be0: mov             x2, NULL
    // 0x700be4: r3 = <String, Y0>
    //     0x700be4: add             x3, PP, #0xf, lsl #12  ; [pp+0xf6c8] TypeArguments: <String, Y0>
    //     0x700be8: ldr             x3, [x3, #0x6c8]
    // 0x700bec: r30 = InstantiateTypeArgumentsStub
    //     0x700bec: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x700bf0: LoadField: r30 = r30->field_7
    //     0x700bf0: ldur            lr, [lr, #7]
    // 0x700bf4: blr             lr
    // 0x700bf8: mov             x1, x0
    // 0x700bfc: ldur            x2, [fp, #-0x18]
    // 0x700c00: ldur            x3, [fp, #-0x20]
    // 0x700c04: r0 = LinkedHashMap()
    //     0x700c04: bl              #0x700c78  ; [dart:collection] LinkedHashMap::LinkedHashMap
    // 0x700c08: mov             x3, x0
    // 0x700c0c: ldur            x2, [fp, #-0x10]
    // 0x700c10: stur            x3, [fp, #-8]
    // 0x700c14: cmp             w2, NULL
    // 0x700c18: b.eq            #0x700c60
    // 0x700c1c: r0 = LoadClassIdInstr(r2)
    //     0x700c1c: ldur            x0, [x2, #-1]
    //     0x700c20: ubfx            x0, x0, #0xc, #0x14
    // 0x700c24: mov             x1, x2
    // 0x700c28: r0 = GDT[cid_x0 + 0x11f66]()
    //     0x700c28: movz            x17, #0x1f66
    //     0x700c2c: movk            x17, #0x1, lsl #16
    //     0x700c30: add             lr, x0, x17
    //     0x700c34: ldr             lr, [x21, lr, lsl #3]
    //     0x700c38: blr             lr
    // 0x700c3c: tbnz            w0, #4, #0x700c60
    // 0x700c40: ldur            x3, [fp, #-8]
    // 0x700c44: r0 = LoadClassIdInstr(r3)
    //     0x700c44: ldur            x0, [x3, #-1]
    //     0x700c48: ubfx            x0, x0, #0xc, #0x14
    // 0x700c4c: mov             x1, x3
    // 0x700c50: ldur            x2, [fp, #-0x10]
    // 0x700c54: r0 = GDT[cid_x0 + 0x931]()
    //     0x700c54: add             lr, x0, #0x931
    //     0x700c58: ldr             lr, [x21, lr, lsl #3]
    //     0x700c5c: blr             lr
    // 0x700c60: ldur            x0, [fp, #-8]
    // 0x700c64: LeaveFrame
    //     0x700c64: mov             SP, fp
    //     0x700c68: ldp             fp, lr, [SP], #0x10
    // 0x700c6c: ret
    //     0x700c6c: ret             
    // 0x700c70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x700c70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x700c74: b               #0x700ba4
  }
  [closure] static int <anonymous closure>(dynamic, String) {
    // ** addr: 0x700f7c, size: 0x6c
    // 0x700f7c: EnterFrame
    //     0x700f7c: stp             fp, lr, [SP, #-0x10]!
    //     0x700f80: mov             fp, SP
    // 0x700f84: AllocStack(0x8)
    //     0x700f84: sub             SP, SP, #8
    // 0x700f88: CheckStackOverflow
    //     0x700f88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x700f8c: cmp             SP, x16
    //     0x700f90: b.ls            #0x700fe0
    // 0x700f94: ldr             x0, [fp, #0x10]
    // 0x700f98: r1 = LoadClassIdInstr(r0)
    //     0x700f98: ldur            x1, [x0, #-1]
    //     0x700f9c: ubfx            x1, x1, #0xc, #0x14
    // 0x700fa0: str             x0, [SP]
    // 0x700fa4: mov             x0, x1
    // 0x700fa8: r0 = GDT[cid_x0 + -0xffe]()
    //     0x700fa8: sub             lr, x0, #0xffe
    //     0x700fac: ldr             lr, [x21, lr, lsl #3]
    //     0x700fb0: blr             lr
    // 0x700fb4: r1 = LoadClassIdInstr(r0)
    //     0x700fb4: ldur            x1, [x0, #-1]
    //     0x700fb8: ubfx            x1, x1, #0xc, #0x14
    // 0x700fbc: str             x0, [SP]
    // 0x700fc0: mov             x0, x1
    // 0x700fc4: r0 = GDT[cid_x0 + 0x64af]()
    //     0x700fc4: movz            x17, #0x64af
    //     0x700fc8: add             lr, x0, x17
    //     0x700fcc: ldr             lr, [x21, lr, lsl #3]
    //     0x700fd0: blr             lr
    // 0x700fd4: LeaveFrame
    //     0x700fd4: mov             SP, fp
    //     0x700fd8: ldp             fp, lr, [SP], #0x10
    // 0x700fdc: ret
    //     0x700fdc: ret             
    // 0x700fe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x700fe0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x700fe4: b               #0x700f94
  }
  [closure] static bool <anonymous closure>(dynamic, String, String) {
    // ** addr: 0x700fe8, size: 0x98
    // 0x700fe8: EnterFrame
    //     0x700fe8: stp             fp, lr, [SP, #-0x10]!
    //     0x700fec: mov             fp, SP
    // 0x700ff0: AllocStack(0x18)
    //     0x700ff0: sub             SP, SP, #0x18
    // 0x700ff4: CheckStackOverflow
    //     0x700ff4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x700ff8: cmp             SP, x16
    //     0x700ffc: b.ls            #0x701078
    // 0x701000: ldr             x0, [fp, #0x18]
    // 0x701004: r1 = LoadClassIdInstr(r0)
    //     0x701004: ldur            x1, [x0, #-1]
    //     0x701008: ubfx            x1, x1, #0xc, #0x14
    // 0x70100c: str             x0, [SP]
    // 0x701010: mov             x0, x1
    // 0x701014: r0 = GDT[cid_x0 + -0xffe]()
    //     0x701014: sub             lr, x0, #0xffe
    //     0x701018: ldr             lr, [x21, lr, lsl #3]
    //     0x70101c: blr             lr
    // 0x701020: mov             x1, x0
    // 0x701024: ldr             x0, [fp, #0x10]
    // 0x701028: stur            x1, [fp, #-8]
    // 0x70102c: r2 = LoadClassIdInstr(r0)
    //     0x70102c: ldur            x2, [x0, #-1]
    //     0x701030: ubfx            x2, x2, #0xc, #0x14
    // 0x701034: str             x0, [SP]
    // 0x701038: mov             x0, x2
    // 0x70103c: r0 = GDT[cid_x0 + -0xffe]()
    //     0x70103c: sub             lr, x0, #0xffe
    //     0x701040: ldr             lr, [x21, lr, lsl #3]
    //     0x701044: blr             lr
    // 0x701048: mov             x1, x0
    // 0x70104c: ldur            x0, [fp, #-8]
    // 0x701050: r2 = LoadClassIdInstr(r0)
    //     0x701050: ldur            x2, [x0, #-1]
    //     0x701054: ubfx            x2, x2, #0xc, #0x14
    // 0x701058: stp             x1, x0, [SP]
    // 0x70105c: mov             x0, x2
    // 0x701060: mov             lr, x0
    // 0x701064: ldr             lr, [x21, lr, lsl #3]
    // 0x701068: blr             lr
    // 0x70106c: LeaveFrame
    //     0x70106c: mov             SP, fp
    //     0x701070: ldp             fp, lr, [SP], #0x10
    // 0x701074: ret
    //     0x701074: ret             
    // 0x701078: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x701078: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x70107c: b               #0x701000
  }
  static _ encodeMap(/* No info */) {
    // ** addr: 0x7248f0, size: 0x1dc
    // 0x7248f0: EnterFrame
    //     0x7248f0: stp             fp, lr, [SP, #-0x10]!
    //     0x7248f4: mov             fp, SP
    // 0x7248f8: AllocStack(0x38)
    //     0x7248f8: sub             SP, SP, #0x38
    // 0x7248fc: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, {dynamic isQuery = false /* r0, fp-0x8 */})
    //     0x7248fc: stur            x1, [fp, #-0x10]
    //     0x724900: stur            x2, [fp, #-0x18]
    //     0x724904: ldur            w0, [x4, #0x13]
    //     0x724908: ldur            w3, [x4, #0x1f]
    //     0x72490c: add             x3, x3, HEAP, lsl #32
    //     0x724910: add             x16, PP, #0x12, lsl #12  ; [pp+0x12008] "isQuery"
    //     0x724914: ldr             x16, [x16, #8]
    //     0x724918: cmp             w3, w16
    //     0x72491c: b.ne            #0x724938
    //     0x724920: ldur            w3, [x4, #0x23]
    //     0x724924: add             x3, x3, HEAP, lsl #32
    //     0x724928: sub             w4, w0, w3
    //     0x72492c: add             x0, fp, w4, sxtw #2
    //     0x724930: ldr             x0, [x0, #8]
    //     0x724934: b               #0x72493c
    //     0x724938: add             x0, NULL, #0x30  ; false
    //     0x72493c: stur            x0, [fp, #-8]
    // 0x724940: CheckStackOverflow
    //     0x724940: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x724944: cmp             SP, x16
    //     0x724948: b.ls            #0x724ac4
    // 0x72494c: r1 = 9
    //     0x72494c: movz            x1, #0x9
    // 0x724950: r0 = AllocateContext()
    //     0x724950: bl              #0xec126c  ; AllocateContextStub
    // 0x724954: mov             x1, x0
    // 0x724958: ldur            x0, [fp, #-0x18]
    // 0x72495c: stur            x1, [fp, #-0x20]
    // 0x724960: StoreField: r1->field_f = r0
    //     0x724960: stur            w0, [x1, #0xf]
    // 0x724964: ldur            x0, [fp, #-8]
    // 0x724968: StoreField: r1->field_13 = r0
    //     0x724968: stur            w0, [x1, #0x13]
    // 0x72496c: r0 = StringBuffer()
    //     0x72496c: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x724970: stur            x0, [fp, #-8]
    // 0x724974: r16 = ""
    //     0x724974: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0x724978: str             x16, [SP]
    // 0x72497c: mov             x1, x0
    // 0x724980: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x724980: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x724984: r0 = StringBuffer()
    //     0x724984: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0x724988: ldur            x0, [fp, #-8]
    // 0x72498c: ldur            x3, [fp, #-0x20]
    // 0x724990: ArrayStore: r3[0] = r0  ; List_4
    //     0x724990: stur            w0, [x3, #0x17]
    //     0x724994: ldurb           w16, [x3, #-1]
    //     0x724998: ldurb           w17, [x0, #-1]
    //     0x72499c: and             x16, x17, x16, lsr #2
    //     0x7249a0: tst             x16, HEAP, lsr #32
    //     0x7249a4: b.eq            #0x7249ac
    //     0x7249a8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7249ac: r0 = true
    //     0x7249ac: add             x0, NULL, #0x20  ; true
    // 0x7249b0: StoreField: r3->field_1b = r0
    //     0x7249b0: stur            w0, [x3, #0x1b]
    // 0x7249b4: LoadField: r1 = r3->field_13
    //     0x7249b4: ldur            w1, [x3, #0x13]
    // 0x7249b8: DecompressPointer r1
    //     0x7249b8: add             x1, x1, HEAP, lsl #32
    // 0x7249bc: tbnz            w1, #4, #0x7249c8
    // 0x7249c0: r0 = "["
    //     0x7249c0: ldr             x0, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0x7249c4: b               #0x7249d0
    // 0x7249c8: r0 = "%5B"
    //     0x7249c8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12010] "%5B"
    //     0x7249cc: ldr             x0, [x0, #0x10]
    // 0x7249d0: StoreField: r3->field_1f = r0
    //     0x7249d0: stur            w0, [x3, #0x1f]
    //     0x7249d4: ldurb           w16, [x3, #-1]
    //     0x7249d8: ldurb           w17, [x0, #-1]
    //     0x7249dc: and             x16, x17, x16, lsr #2
    //     0x7249e0: tst             x16, HEAP, lsr #32
    //     0x7249e4: b.eq            #0x7249ec
    //     0x7249e8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7249ec: tbnz            w1, #4, #0x7249f8
    // 0x7249f0: r0 = "]"
    //     0x7249f0: ldr             x0, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0x7249f4: b               #0x724a00
    // 0x7249f8: r0 = "%5D"
    //     0x7249f8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12018] "%5D"
    //     0x7249fc: ldr             x0, [x0, #0x18]
    // 0x724a00: r1 = Closure: (String, {Encoding encoding}) => String from Function 'encodeQueryComponent': static.
    //     0x724a00: add             x1, PP, #0x12, lsl #12  ; [pp+0x12020] Closure: (String, {Encoding encoding}) => String from Function 'encodeQueryComponent': static. (0x7e54fb0049d4)
    //     0x724a04: ldr             x1, [x1, #0x20]
    // 0x724a08: StoreField: r3->field_23 = r0
    //     0x724a08: stur            w0, [x3, #0x23]
    //     0x724a0c: ldurb           w16, [x3, #-1]
    //     0x724a10: ldurb           w17, [x0, #-1]
    //     0x724a14: and             x16, x17, x16, lsr #2
    //     0x724a18: tst             x16, HEAP, lsr #32
    //     0x724a1c: b.eq            #0x724a24
    //     0x724a20: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x724a24: StoreField: r3->field_27 = r1
    //     0x724a24: stur            w1, [x3, #0x27]
    // 0x724a28: mov             x2, x3
    // 0x724a2c: r1 = Function 'maybeEncode': static.
    //     0x724a2c: add             x1, PP, #0x12, lsl #12  ; [pp+0x12028] AnonymousClosure: static (0x72550c), in [package:dio/src/utils.dart] ::encodeMap (0x7248f0)
    //     0x724a30: ldr             x1, [x1, #0x28]
    // 0x724a34: r0 = AllocateClosure()
    //     0x724a34: bl              #0xec1630  ; AllocateClosureStub
    // 0x724a38: ldur            x3, [fp, #-0x20]
    // 0x724a3c: StoreField: r3->field_2b = r0
    //     0x724a3c: stur            w0, [x3, #0x2b]
    //     0x724a40: ldurb           w16, [x3, #-1]
    //     0x724a44: ldurb           w17, [x0, #-1]
    //     0x724a48: and             x16, x17, x16, lsr #2
    //     0x724a4c: tst             x16, HEAP, lsr #32
    //     0x724a50: b.eq            #0x724a58
    //     0x724a54: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x724a58: mov             x2, x3
    // 0x724a5c: r1 = Function 'urlEncode': static.
    //     0x724a5c: add             x1, PP, #0x12, lsl #12  ; [pp+0x12030] AnonymousClosure: static (0x724acc), in [package:dio/src/utils.dart] ::encodeMap (0x7248f0)
    //     0x724a60: ldr             x1, [x1, #0x30]
    // 0x724a64: r0 = AllocateClosure()
    //     0x724a64: bl              #0xec1630  ; AllocateClosureStub
    // 0x724a68: mov             x2, x0
    // 0x724a6c: ldur            x1, [fp, #-0x20]
    // 0x724a70: StoreField: r1->field_2f = r0
    //     0x724a70: stur            w0, [x1, #0x2f]
    //     0x724a74: ldurb           w16, [x1, #-1]
    //     0x724a78: ldurb           w17, [x0, #-1]
    //     0x724a7c: and             x16, x17, x16, lsr #2
    //     0x724a80: tst             x16, HEAP, lsr #32
    //     0x724a84: b.eq            #0x724a8c
    //     0x724a88: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x724a8c: ldur            x16, [fp, #-0x10]
    // 0x724a90: stp             x16, x2, [SP, #8]
    // 0x724a94: r16 = ""
    //     0x724a94: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0x724a98: str             x16, [SP]
    // 0x724a9c: mov             x0, x2
    // 0x724aa0: ClosureCall
    //     0x724aa0: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x724aa4: ldur            x2, [x0, #0x1f]
    //     0x724aa8: blr             x2
    // 0x724aac: ldur            x16, [fp, #-8]
    // 0x724ab0: str             x16, [SP]
    // 0x724ab4: r0 = toString()
    //     0x724ab4: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0x724ab8: LeaveFrame
    //     0x724ab8: mov             SP, fp
    //     0x724abc: ldp             fp, lr, [SP], #0x10
    // 0x724ac0: ret
    //     0x724ac0: ret             
    // 0x724ac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x724ac4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x724ac8: b               #0x72494c
  }
  [closure] static void urlEncode(dynamic, Object?, String) {
    // ** addr: 0x724acc, size: 0x6f4
    // 0x724acc: EnterFrame
    //     0x724acc: stp             fp, lr, [SP, #-0x10]!
    //     0x724ad0: mov             fp, SP
    // 0x724ad4: AllocStack(0x80)
    //     0x724ad4: sub             SP, SP, #0x80
    // 0x724ad8: SetupParameters()
    //     0x724ad8: ldr             x0, [fp, #0x20]
    //     0x724adc: ldur            w1, [x0, #0x17]
    //     0x724ae0: add             x1, x1, HEAP, lsl #32
    //     0x724ae4: stur            x1, [fp, #-8]
    // 0x724ae8: CheckStackOverflow
    //     0x724ae8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x724aec: cmp             SP, x16
    //     0x724af0: b.ls            #0x7251b0
    // 0x724af4: r1 = 1
    //     0x724af4: movz            x1, #0x1
    // 0x724af8: r0 = AllocateContext()
    //     0x724af8: bl              #0xec126c  ; AllocateContextStub
    // 0x724afc: mov             x4, x0
    // 0x724b00: ldur            x3, [fp, #-8]
    // 0x724b04: stur            x4, [fp, #-0x10]
    // 0x724b08: StoreField: r4->field_b = r3
    //     0x724b08: stur            w3, [x4, #0xb]
    // 0x724b0c: ldr             x5, [fp, #0x10]
    // 0x724b10: StoreField: r4->field_f = r5
    //     0x724b10: stur            w5, [x4, #0xf]
    // 0x724b14: ldr             x0, [fp, #0x18]
    // 0x724b18: r2 = Null
    //     0x724b18: mov             x2, NULL
    // 0x724b1c: r1 = Null
    //     0x724b1c: mov             x1, NULL
    // 0x724b20: cmp             w0, NULL
    // 0x724b24: b.eq            #0x724bc8
    // 0x724b28: branchIfSmi(r0, 0x724bc8)
    //     0x724b28: tbz             w0, #0, #0x724bc8
    // 0x724b2c: r3 = LoadClassIdInstr(r0)
    //     0x724b2c: ldur            x3, [x0, #-1]
    //     0x724b30: ubfx            x3, x3, #0xc, #0x14
    // 0x724b34: r17 = 6718
    //     0x724b34: movz            x17, #0x1a3e
    // 0x724b38: cmp             x3, x17
    // 0x724b3c: b.eq            #0x724bd0
    // 0x724b40: sub             x3, x3, #0x5a
    // 0x724b44: cmp             x3, #2
    // 0x724b48: b.ls            #0x724bd0
    // 0x724b4c: r4 = LoadClassIdInstr(r0)
    //     0x724b4c: ldur            x4, [x0, #-1]
    //     0x724b50: ubfx            x4, x4, #0xc, #0x14
    // 0x724b54: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x724b58: ldr             x3, [x3, #0x18]
    // 0x724b5c: ldr             x3, [x3, x4, lsl #3]
    // 0x724b60: LoadField: r3 = r3->field_2b
    //     0x724b60: ldur            w3, [x3, #0x2b]
    // 0x724b64: DecompressPointer r3
    //     0x724b64: add             x3, x3, HEAP, lsl #32
    // 0x724b68: cmp             w3, NULL
    // 0x724b6c: b.eq            #0x724bc8
    // 0x724b70: LoadField: r3 = r3->field_f
    //     0x724b70: ldur            w3, [x3, #0xf]
    // 0x724b74: lsr             x3, x3, #3
    // 0x724b78: r17 = 6718
    //     0x724b78: movz            x17, #0x1a3e
    // 0x724b7c: cmp             x3, x17
    // 0x724b80: b.eq            #0x724bd0
    // 0x724b84: r3 = SubtypeTestCache
    //     0x724b84: add             x3, PP, #0x12, lsl #12  ; [pp+0x12038] SubtypeTestCache
    //     0x724b88: ldr             x3, [x3, #0x38]
    // 0x724b8c: r30 = Subtype1TestCacheStub
    //     0x724b8c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x724b90: LoadField: r30 = r30->field_7
    //     0x724b90: ldur            lr, [lr, #7]
    // 0x724b94: blr             lr
    // 0x724b98: cmp             w7, NULL
    // 0x724b9c: b.eq            #0x724ba8
    // 0x724ba0: tbnz            w7, #4, #0x724bc8
    // 0x724ba4: b               #0x724bd0
    // 0x724ba8: r8 = List
    //     0x724ba8: add             x8, PP, #0x12, lsl #12  ; [pp+0x12040] Type: List
    //     0x724bac: ldr             x8, [x8, #0x40]
    // 0x724bb0: r3 = SubtypeTestCache
    //     0x724bb0: add             x3, PP, #0x12, lsl #12  ; [pp+0x12048] SubtypeTestCache
    //     0x724bb4: ldr             x3, [x3, #0x48]
    // 0x724bb8: r30 = InstanceOfStub
    //     0x724bb8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x724bbc: LoadField: r30 = r30->field_7
    //     0x724bbc: ldur            lr, [lr, #7]
    // 0x724bc0: blr             lr
    // 0x724bc4: b               #0x724bd4
    // 0x724bc8: r0 = false
    //     0x724bc8: add             x0, NULL, #0x30  ; false
    // 0x724bcc: b               #0x724bd4
    // 0x724bd0: r0 = true
    //     0x724bd0: add             x0, NULL, #0x20  ; true
    // 0x724bd4: tbnz            w0, #4, #0x725000
    // 0x724bd8: ldur            x3, [fp, #-8]
    // 0x724bdc: LoadField: r1 = r3->field_2f
    //     0x724bdc: ldur            w1, [x3, #0x2f]
    // 0x724be0: DecompressPointer r1
    //     0x724be0: add             x1, x1, HEAP, lsl #32
    // 0x724be4: stur            x1, [fp, #-0x40]
    // 0x724be8: LoadField: r0 = r3->field_2b
    //     0x724be8: ldur            w0, [x3, #0x2b]
    // 0x724bec: DecompressPointer r0
    //     0x724bec: add             x0, x0, HEAP, lsl #32
    // 0x724bf0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x724bf0: ldur            w2, [x0, #0x17]
    // 0x724bf4: DecompressPointer r2
    //     0x724bf4: add             x2, x2, HEAP, lsl #32
    // 0x724bf8: stur            x2, [fp, #-0x38]
    // 0x724bfc: LoadField: r4 = r2->field_27
    //     0x724bfc: ldur            w4, [x2, #0x27]
    // 0x724c00: DecompressPointer r4
    //     0x724c00: add             x4, x4, HEAP, lsl #32
    // 0x724c04: stur            x4, [fp, #-0x30]
    // 0x724c08: LoadField: r5 = r3->field_1f
    //     0x724c08: ldur            w5, [x3, #0x1f]
    // 0x724c0c: DecompressPointer r5
    //     0x724c0c: add             x5, x5, HEAP, lsl #32
    // 0x724c10: stur            x5, [fp, #-0x28]
    // 0x724c14: LoadField: r6 = r3->field_23
    //     0x724c14: ldur            w6, [x3, #0x23]
    // 0x724c18: DecompressPointer r6
    //     0x724c18: add             x6, x6, HEAP, lsl #32
    // 0x724c1c: stur            x6, [fp, #-0x20]
    // 0x724c20: r8 = 0
    //     0x724c20: movz            x8, #0
    // 0x724c24: ldr             x7, [fp, #0x18]
    // 0x724c28: ldur            x3, [fp, #-0x10]
    // 0x724c2c: stur            x8, [fp, #-0x18]
    // 0x724c30: CheckStackOverflow
    //     0x724c30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x724c34: cmp             SP, x16
    //     0x724c38: b.ls            #0x7251b8
    // 0x724c3c: r0 = LoadClassIdInstr(r7)
    //     0x724c3c: ldur            x0, [x7, #-1]
    //     0x724c40: ubfx            x0, x0, #0xc, #0x14
    // 0x724c44: str             x7, [SP]
    // 0x724c48: r0 = GDT[cid_x0 + 0xc834]()
    //     0x724c48: movz            x17, #0xc834
    //     0x724c4c: add             lr, x0, x17
    //     0x724c50: ldr             lr, [x21, lr, lsl #3]
    //     0x724c54: blr             lr
    // 0x724c58: r1 = LoadInt32Instr(r0)
    //     0x724c58: sbfx            x1, x0, #1, #0x1f
    //     0x724c5c: tbz             w0, #0, #0x724c64
    //     0x724c60: ldur            x1, [x0, #7]
    // 0x724c64: ldur            x2, [fp, #-0x18]
    // 0x724c68: cmp             x2, x1
    // 0x724c6c: b.ge            #0x7251a0
    // 0x724c70: ldr             x3, [fp, #0x18]
    // 0x724c74: r0 = BoxInt64Instr(r2)
    //     0x724c74: sbfiz           x0, x2, #1, #0x1f
    //     0x724c78: cmp             x2, x0, asr #1
    //     0x724c7c: b.eq            #0x724c88
    //     0x724c80: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x724c84: stur            x2, [x0, #7]
    // 0x724c88: mov             x1, x0
    // 0x724c8c: stur            x1, [fp, #-0x48]
    // 0x724c90: r0 = LoadClassIdInstr(r3)
    //     0x724c90: ldur            x0, [x3, #-1]
    //     0x724c94: ubfx            x0, x0, #0xc, #0x14
    // 0x724c98: stp             x1, x3, [SP]
    // 0x724c9c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x724c9c: movz            x17, #0x3037
    //     0x724ca0: movk            x17, #0x1, lsl #16
    //     0x724ca4: add             lr, x0, x17
    //     0x724ca8: ldr             lr, [x21, lr, lsl #3]
    //     0x724cac: blr             lr
    // 0x724cb0: r2 = Null
    //     0x724cb0: mov             x2, NULL
    // 0x724cb4: r1 = Null
    //     0x724cb4: mov             x1, NULL
    // 0x724cb8: cmp             w0, NULL
    // 0x724cbc: b.eq            #0x724d54
    // 0x724cc0: branchIfSmi(r0, 0x724d54)
    //     0x724cc0: tbz             w0, #0, #0x724d54
    // 0x724cc4: r3 = LoadClassIdInstr(r0)
    //     0x724cc4: ldur            x3, [x0, #-1]
    //     0x724cc8: ubfx            x3, x3, #0xc, #0x14
    // 0x724ccc: r17 = 6717
    //     0x724ccc: movz            x17, #0x1a3d
    // 0x724cd0: cmp             x3, x17
    // 0x724cd4: b.eq            #0x724d5c
    // 0x724cd8: r4 = LoadClassIdInstr(r0)
    //     0x724cd8: ldur            x4, [x0, #-1]
    //     0x724cdc: ubfx            x4, x4, #0xc, #0x14
    // 0x724ce0: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x724ce4: ldr             x3, [x3, #0x18]
    // 0x724ce8: ldr             x3, [x3, x4, lsl #3]
    // 0x724cec: LoadField: r3 = r3->field_2b
    //     0x724cec: ldur            w3, [x3, #0x2b]
    // 0x724cf0: DecompressPointer r3
    //     0x724cf0: add             x3, x3, HEAP, lsl #32
    // 0x724cf4: cmp             w3, NULL
    // 0x724cf8: b.eq            #0x724d54
    // 0x724cfc: LoadField: r3 = r3->field_f
    //     0x724cfc: ldur            w3, [x3, #0xf]
    // 0x724d00: lsr             x3, x3, #3
    // 0x724d04: r17 = 6717
    //     0x724d04: movz            x17, #0x1a3d
    // 0x724d08: cmp             x3, x17
    // 0x724d0c: b.eq            #0x724d5c
    // 0x724d10: r3 = SubtypeTestCache
    //     0x724d10: add             x3, PP, #0x12, lsl #12  ; [pp+0x12050] SubtypeTestCache
    //     0x724d14: ldr             x3, [x3, #0x50]
    // 0x724d18: r30 = Subtype1TestCacheStub
    //     0x724d18: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x724d1c: LoadField: r30 = r30->field_7
    //     0x724d1c: ldur            lr, [lr, #7]
    // 0x724d20: blr             lr
    // 0x724d24: cmp             w7, NULL
    // 0x724d28: b.eq            #0x724d34
    // 0x724d2c: tbnz            w7, #4, #0x724d54
    // 0x724d30: b               #0x724d5c
    // 0x724d34: r8 = Map
    //     0x724d34: add             x8, PP, #0x12, lsl #12  ; [pp+0x12058] Type: Map
    //     0x724d38: ldr             x8, [x8, #0x58]
    // 0x724d3c: r3 = SubtypeTestCache
    //     0x724d3c: add             x3, PP, #0x12, lsl #12  ; [pp+0x12060] SubtypeTestCache
    //     0x724d40: ldr             x3, [x3, #0x60]
    // 0x724d44: r30 = InstanceOfStub
    //     0x724d44: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x724d48: LoadField: r30 = r30->field_7
    //     0x724d48: ldur            lr, [lr, #7]
    // 0x724d4c: blr             lr
    // 0x724d50: b               #0x724d60
    // 0x724d54: r0 = false
    //     0x724d54: add             x0, NULL, #0x30  ; false
    // 0x724d58: b               #0x724d60
    // 0x724d5c: r0 = true
    //     0x724d5c: add             x0, NULL, #0x20  ; true
    // 0x724d60: tbz             w0, #4, #0x724e4c
    // 0x724d64: ldr             x1, [fp, #0x18]
    // 0x724d68: r0 = LoadClassIdInstr(r1)
    //     0x724d68: ldur            x0, [x1, #-1]
    //     0x724d6c: ubfx            x0, x0, #0xc, #0x14
    // 0x724d70: ldur            x16, [fp, #-0x48]
    // 0x724d74: stp             x16, x1, [SP]
    // 0x724d78: r0 = GDT[cid_x0 + 0x13037]()
    //     0x724d78: movz            x17, #0x3037
    //     0x724d7c: movk            x17, #0x1, lsl #16
    //     0x724d80: add             lr, x0, x17
    //     0x724d84: ldr             lr, [x21, lr, lsl #3]
    //     0x724d88: blr             lr
    // 0x724d8c: r2 = Null
    //     0x724d8c: mov             x2, NULL
    // 0x724d90: r1 = Null
    //     0x724d90: mov             x1, NULL
    // 0x724d94: cmp             w0, NULL
    // 0x724d98: b.eq            #0x724e3c
    // 0x724d9c: branchIfSmi(r0, 0x724e3c)
    //     0x724d9c: tbz             w0, #0, #0x724e3c
    // 0x724da0: r3 = LoadClassIdInstr(r0)
    //     0x724da0: ldur            x3, [x0, #-1]
    //     0x724da4: ubfx            x3, x3, #0xc, #0x14
    // 0x724da8: r17 = 6718
    //     0x724da8: movz            x17, #0x1a3e
    // 0x724dac: cmp             x3, x17
    // 0x724db0: b.eq            #0x724e44
    // 0x724db4: sub             x3, x3, #0x5a
    // 0x724db8: cmp             x3, #2
    // 0x724dbc: b.ls            #0x724e44
    // 0x724dc0: r4 = LoadClassIdInstr(r0)
    //     0x724dc0: ldur            x4, [x0, #-1]
    //     0x724dc4: ubfx            x4, x4, #0xc, #0x14
    // 0x724dc8: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x724dcc: ldr             x3, [x3, #0x18]
    // 0x724dd0: ldr             x3, [x3, x4, lsl #3]
    // 0x724dd4: LoadField: r3 = r3->field_2b
    //     0x724dd4: ldur            w3, [x3, #0x2b]
    // 0x724dd8: DecompressPointer r3
    //     0x724dd8: add             x3, x3, HEAP, lsl #32
    // 0x724ddc: cmp             w3, NULL
    // 0x724de0: b.eq            #0x724e3c
    // 0x724de4: LoadField: r3 = r3->field_f
    //     0x724de4: ldur            w3, [x3, #0xf]
    // 0x724de8: lsr             x3, x3, #3
    // 0x724dec: r17 = 6718
    //     0x724dec: movz            x17, #0x1a3e
    // 0x724df0: cmp             x3, x17
    // 0x724df4: b.eq            #0x724e44
    // 0x724df8: r3 = SubtypeTestCache
    //     0x724df8: add             x3, PP, #0x12, lsl #12  ; [pp+0x12068] SubtypeTestCache
    //     0x724dfc: ldr             x3, [x3, #0x68]
    // 0x724e00: r30 = Subtype1TestCacheStub
    //     0x724e00: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x724e04: LoadField: r30 = r30->field_7
    //     0x724e04: ldur            lr, [lr, #7]
    // 0x724e08: blr             lr
    // 0x724e0c: cmp             w7, NULL
    // 0x724e10: b.eq            #0x724e1c
    // 0x724e14: tbnz            w7, #4, #0x724e3c
    // 0x724e18: b               #0x724e44
    // 0x724e1c: r8 = List
    //     0x724e1c: add             x8, PP, #0x12, lsl #12  ; [pp+0x12070] Type: List
    //     0x724e20: ldr             x8, [x8, #0x70]
    // 0x724e24: r3 = SubtypeTestCache
    //     0x724e24: add             x3, PP, #0x12, lsl #12  ; [pp+0x12078] SubtypeTestCache
    //     0x724e28: ldr             x3, [x3, #0x78]
    // 0x724e2c: r30 = InstanceOfStub
    //     0x724e2c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x724e30: LoadField: r30 = r30->field_7
    //     0x724e30: ldur            lr, [lr, #7]
    // 0x724e34: blr             lr
    // 0x724e38: b               #0x724e48
    // 0x724e3c: r0 = false
    //     0x724e3c: add             x0, NULL, #0x30  ; false
    // 0x724e40: b               #0x724e48
    // 0x724e44: r0 = true
    //     0x724e44: add             x0, NULL, #0x20  ; true
    // 0x724e48: tbnz            w0, #4, #0x724e54
    // 0x724e4c: r3 = true
    //     0x724e4c: add             x3, NULL, #0x20  ; true
    // 0x724e50: b               #0x724e80
    // 0x724e54: ldr             x1, [fp, #0x18]
    // 0x724e58: r0 = LoadClassIdInstr(r1)
    //     0x724e58: ldur            x0, [x1, #-1]
    //     0x724e5c: ubfx            x0, x0, #0xc, #0x14
    // 0x724e60: ldur            x16, [fp, #-0x48]
    // 0x724e64: stp             x16, x1, [SP]
    // 0x724e68: r0 = GDT[cid_x0 + 0x13037]()
    //     0x724e68: movz            x17, #0x3037
    //     0x724e6c: movk            x17, #0x1, lsl #16
    //     0x724e70: add             lr, x0, x17
    //     0x724e74: ldr             lr, [x21, lr, lsl #3]
    //     0x724e78: blr             lr
    // 0x724e7c: r3 = false
    //     0x724e7c: add             x3, NULL, #0x30  ; false
    // 0x724e80: ldr             x1, [fp, #0x18]
    // 0x724e84: ldur            x2, [fp, #-0x38]
    // 0x724e88: stur            x3, [fp, #-0x50]
    // 0x724e8c: r0 = LoadClassIdInstr(r1)
    //     0x724e8c: ldur            x0, [x1, #-1]
    //     0x724e90: ubfx            x0, x0, #0xc, #0x14
    // 0x724e94: ldur            x16, [fp, #-0x48]
    // 0x724e98: stp             x16, x1, [SP]
    // 0x724e9c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x724e9c: movz            x17, #0x3037
    //     0x724ea0: movk            x17, #0x1, lsl #16
    //     0x724ea4: add             lr, x0, x17
    //     0x724ea8: ldr             lr, [x21, lr, lsl #3]
    //     0x724eac: blr             lr
    // 0x724eb0: ldur            x1, [fp, #-0x38]
    // 0x724eb4: LoadField: r2 = r1->field_13
    //     0x724eb4: ldur            w2, [x1, #0x13]
    // 0x724eb8: DecompressPointer r2
    //     0x724eb8: add             x2, x2, HEAP, lsl #32
    // 0x724ebc: tbnz            w2, #4, #0x724ee4
    // 0x724ec0: cmp             w0, NULL
    // 0x724ec4: b.eq            #0x724ee4
    // 0x724ec8: r2 = 60
    //     0x724ec8: movz            x2, #0x3c
    // 0x724ecc: branchIfSmi(r0, 0x724ed8)
    //     0x724ecc: tbz             w0, #0, #0x724ed8
    // 0x724ed0: r2 = LoadClassIdInstr(r0)
    //     0x724ed0: ldur            x2, [x0, #-1]
    //     0x724ed4: ubfx            x2, x2, #0xc, #0x14
    // 0x724ed8: sub             x16, x2, #0x5e
    // 0x724edc: cmp             x16, #1
    // 0x724ee0: b.ls            #0x724eec
    // 0x724ee4: mov             x4, x0
    // 0x724ee8: b               #0x724f08
    // 0x724eec: ldur            x16, [fp, #-0x30]
    // 0x724ef0: stp             x0, x16, [SP]
    // 0x724ef4: ldur            x0, [fp, #-0x30]
    // 0x724ef8: ClosureCall
    //     0x724ef8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x724efc: ldur            x2, [x0, #0x1f]
    //     0x724f00: blr             x2
    // 0x724f04: mov             x4, x0
    // 0x724f08: ldur            x3, [fp, #-0x10]
    // 0x724f0c: ldur            x0, [fp, #-0x50]
    // 0x724f10: stur            x4, [fp, #-0x60]
    // 0x724f14: LoadField: r5 = r3->field_f
    //     0x724f14: ldur            w5, [x3, #0xf]
    // 0x724f18: DecompressPointer r5
    //     0x724f18: add             x5, x5, HEAP, lsl #32
    // 0x724f1c: stur            x5, [fp, #-0x58]
    // 0x724f20: r1 = Null
    //     0x724f20: mov             x1, NULL
    // 0x724f24: r2 = 4
    //     0x724f24: movz            x2, #0x4
    // 0x724f28: r0 = AllocateArray()
    //     0x724f28: bl              #0xec22fc  ; AllocateArrayStub
    // 0x724f2c: mov             x3, x0
    // 0x724f30: ldur            x0, [fp, #-0x58]
    // 0x724f34: stur            x3, [fp, #-0x68]
    // 0x724f38: StoreField: r3->field_f = r0
    //     0x724f38: stur            w0, [x3, #0xf]
    // 0x724f3c: ldur            x0, [fp, #-0x50]
    // 0x724f40: tbnz            w0, #4, #0x724f84
    // 0x724f44: ldur            x4, [fp, #-0x28]
    // 0x724f48: ldur            x5, [fp, #-0x20]
    // 0x724f4c: ldur            x0, [fp, #-0x48]
    // 0x724f50: r1 = Null
    //     0x724f50: mov             x1, NULL
    // 0x724f54: r2 = 6
    //     0x724f54: movz            x2, #0x6
    // 0x724f58: r0 = AllocateArray()
    //     0x724f58: bl              #0xec22fc  ; AllocateArrayStub
    // 0x724f5c: mov             x1, x0
    // 0x724f60: ldur            x0, [fp, #-0x28]
    // 0x724f64: StoreField: r1->field_f = r0
    //     0x724f64: stur            w0, [x1, #0xf]
    // 0x724f68: ldur            x2, [fp, #-0x48]
    // 0x724f6c: StoreField: r1->field_13 = r2
    //     0x724f6c: stur            w2, [x1, #0x13]
    // 0x724f70: ldur            x2, [fp, #-0x20]
    // 0x724f74: ArrayStore: r1[0] = r2  ; List_4
    //     0x724f74: stur            w2, [x1, #0x17]
    // 0x724f78: str             x1, [SP]
    // 0x724f7c: r0 = _interpolate()
    //     0x724f7c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x724f80: b               #0x724f88
    // 0x724f84: r0 = ""
    //     0x724f84: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x724f88: ldur            x2, [fp, #-0x18]
    // 0x724f8c: ldur            x1, [fp, #-0x68]
    // 0x724f90: ArrayStore: r1[1] = r0  ; List_4
    //     0x724f90: add             x25, x1, #0x13
    //     0x724f94: str             w0, [x25]
    //     0x724f98: tbz             w0, #0, #0x724fb4
    //     0x724f9c: ldurb           w16, [x1, #-1]
    //     0x724fa0: ldurb           w17, [x0, #-1]
    //     0x724fa4: and             x16, x17, x16, lsr #2
    //     0x724fa8: tst             x16, HEAP, lsr #32
    //     0x724fac: b.eq            #0x724fb4
    //     0x724fb0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x724fb4: ldur            x16, [fp, #-0x68]
    // 0x724fb8: str             x16, [SP]
    // 0x724fbc: r0 = _interpolate()
    //     0x724fbc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x724fc0: ldur            x16, [fp, #-0x40]
    // 0x724fc4: ldur            lr, [fp, #-0x60]
    // 0x724fc8: stp             lr, x16, [SP, #8]
    // 0x724fcc: str             x0, [SP]
    // 0x724fd0: ldur            x0, [fp, #-0x40]
    // 0x724fd4: ClosureCall
    //     0x724fd4: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x724fd8: ldur            x2, [x0, #0x1f]
    //     0x724fdc: blr             x2
    // 0x724fe0: ldur            x0, [fp, #-0x18]
    // 0x724fe4: add             x8, x0, #1
    // 0x724fe8: ldur            x1, [fp, #-0x40]
    // 0x724fec: ldur            x5, [fp, #-0x28]
    // 0x724ff0: ldur            x6, [fp, #-0x20]
    // 0x724ff4: ldur            x2, [fp, #-0x38]
    // 0x724ff8: ldur            x4, [fp, #-0x30]
    // 0x724ffc: b               #0x724c24
    // 0x725000: ldur            x3, [fp, #-8]
    // 0x725004: ldr             x0, [fp, #0x18]
    // 0x725008: r2 = Null
    //     0x725008: mov             x2, NULL
    // 0x72500c: r1 = Null
    //     0x72500c: mov             x1, NULL
    // 0x725010: cmp             w0, NULL
    // 0x725014: b.eq            #0x7250ac
    // 0x725018: branchIfSmi(r0, 0x7250ac)
    //     0x725018: tbz             w0, #0, #0x7250ac
    // 0x72501c: r3 = LoadClassIdInstr(r0)
    //     0x72501c: ldur            x3, [x0, #-1]
    //     0x725020: ubfx            x3, x3, #0xc, #0x14
    // 0x725024: r17 = 6717
    //     0x725024: movz            x17, #0x1a3d
    // 0x725028: cmp             x3, x17
    // 0x72502c: b.eq            #0x7250b4
    // 0x725030: r4 = LoadClassIdInstr(r0)
    //     0x725030: ldur            x4, [x0, #-1]
    //     0x725034: ubfx            x4, x4, #0xc, #0x14
    // 0x725038: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x72503c: ldr             x3, [x3, #0x18]
    // 0x725040: ldr             x3, [x3, x4, lsl #3]
    // 0x725044: LoadField: r3 = r3->field_2b
    //     0x725044: ldur            w3, [x3, #0x2b]
    // 0x725048: DecompressPointer r3
    //     0x725048: add             x3, x3, HEAP, lsl #32
    // 0x72504c: cmp             w3, NULL
    // 0x725050: b.eq            #0x7250ac
    // 0x725054: LoadField: r3 = r3->field_f
    //     0x725054: ldur            w3, [x3, #0xf]
    // 0x725058: lsr             x3, x3, #3
    // 0x72505c: r17 = 6717
    //     0x72505c: movz            x17, #0x1a3d
    // 0x725060: cmp             x3, x17
    // 0x725064: b.eq            #0x7250b4
    // 0x725068: r3 = SubtypeTestCache
    //     0x725068: add             x3, PP, #0x12, lsl #12  ; [pp+0x12080] SubtypeTestCache
    //     0x72506c: ldr             x3, [x3, #0x80]
    // 0x725070: r30 = Subtype1TestCacheStub
    //     0x725070: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x725074: LoadField: r30 = r30->field_7
    //     0x725074: ldur            lr, [lr, #7]
    // 0x725078: blr             lr
    // 0x72507c: cmp             w7, NULL
    // 0x725080: b.eq            #0x72508c
    // 0x725084: tbnz            w7, #4, #0x7250ac
    // 0x725088: b               #0x7250b4
    // 0x72508c: r8 = Map
    //     0x72508c: add             x8, PP, #0x12, lsl #12  ; [pp+0x12088] Type: Map
    //     0x725090: ldr             x8, [x8, #0x88]
    // 0x725094: r3 = SubtypeTestCache
    //     0x725094: add             x3, PP, #0x12, lsl #12  ; [pp+0x12090] SubtypeTestCache
    //     0x725098: ldr             x3, [x3, #0x90]
    // 0x72509c: r30 = InstanceOfStub
    //     0x72509c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x7250a0: LoadField: r30 = r30->field_7
    //     0x7250a0: ldur            lr, [lr, #7]
    // 0x7250a4: blr             lr
    // 0x7250a8: b               #0x7250b8
    // 0x7250ac: r0 = false
    //     0x7250ac: add             x0, NULL, #0x30  ; false
    // 0x7250b0: b               #0x7250b8
    // 0x7250b4: r0 = true
    //     0x7250b4: add             x0, NULL, #0x20  ; true
    // 0x7250b8: tbnz            w0, #4, #0x7250f8
    // 0x7250bc: ldr             x0, [fp, #0x18]
    // 0x7250c0: ldur            x2, [fp, #-0x10]
    // 0x7250c4: r1 = Function '<anonymous closure>': static.
    //     0x7250c4: add             x1, PP, #0x12, lsl #12  ; [pp+0x12098] AnonymousClosure: static (0x7251c0), in [package:dio/src/utils.dart] ::encodeMap (0x7248f0)
    //     0x7250c8: ldr             x1, [x1, #0x98]
    // 0x7250cc: r0 = AllocateClosure()
    //     0x7250cc: bl              #0xec1630  ; AllocateClosureStub
    // 0x7250d0: ldr             x1, [fp, #0x18]
    // 0x7250d4: r2 = LoadClassIdInstr(r1)
    //     0x7250d4: ldur            x2, [x1, #-1]
    //     0x7250d8: ubfx            x2, x2, #0xc, #0x14
    // 0x7250dc: mov             x16, x0
    // 0x7250e0: mov             x0, x2
    // 0x7250e4: mov             x2, x16
    // 0x7250e8: r0 = GDT[cid_x0 + 0x64e]()
    //     0x7250e8: add             lr, x0, #0x64e
    //     0x7250ec: ldr             lr, [x21, lr, lsl #3]
    //     0x7250f0: blr             lr
    // 0x7250f4: b               #0x7251a0
    // 0x7250f8: ldr             x1, [fp, #0x18]
    // 0x7250fc: ldur            x2, [fp, #-8]
    // 0x725100: LoadField: r0 = r2->field_f
    //     0x725100: ldur            w0, [x2, #0xf]
    // 0x725104: DecompressPointer r0
    //     0x725104: add             x0, x0, HEAP, lsl #32
    // 0x725108: ldr             x16, [fp, #0x10]
    // 0x72510c: stp             x16, x0, [SP, #8]
    // 0x725110: str             x1, [SP]
    // 0x725114: ClosureCall
    //     0x725114: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x725118: ldur            x2, [x0, #0x1f]
    //     0x72511c: blr             x2
    // 0x725120: stur            x0, [fp, #-0x10]
    // 0x725124: cmp             w0, NULL
    // 0x725128: b.eq            #0x725150
    // 0x72512c: mov             x1, x0
    // 0x725130: r0 = trim()
    //     0x725130: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0x725134: LoadField: r1 = r0->field_7
    //     0x725134: ldur            w1, [x0, #7]
    // 0x725138: cbnz            w1, #0x725144
    // 0x72513c: r0 = false
    //     0x72513c: add             x0, NULL, #0x30  ; false
    // 0x725140: b               #0x725148
    // 0x725144: r0 = true
    //     0x725144: add             x0, NULL, #0x20  ; true
    // 0x725148: mov             x3, x0
    // 0x72514c: b               #0x725154
    // 0x725150: r3 = false
    //     0x725150: add             x3, NULL, #0x30  ; false
    // 0x725154: ldur            x0, [fp, #-8]
    // 0x725158: stur            x3, [fp, #-0x20]
    // 0x72515c: LoadField: r1 = r0->field_1b
    //     0x72515c: ldur            w1, [x0, #0x1b]
    // 0x725160: DecompressPointer r1
    //     0x725160: add             x1, x1, HEAP, lsl #32
    // 0x725164: tbz             w1, #4, #0x72517c
    // 0x725168: tbnz            w3, #4, #0x72517c
    // 0x72516c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x72516c: ldur            w1, [x0, #0x17]
    // 0x725170: DecompressPointer r1
    //     0x725170: add             x1, x1, HEAP, lsl #32
    // 0x725174: r2 = "&"
    //     0x725174: ldr             x2, [PP, #0xde0]  ; [pp+0xde0] "&"
    // 0x725178: r0 = write()
    //     0x725178: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x72517c: ldur            x0, [fp, #-8]
    // 0x725180: ldur            x1, [fp, #-0x20]
    // 0x725184: r2 = false
    //     0x725184: add             x2, NULL, #0x30  ; false
    // 0x725188: StoreField: r0->field_1b = r2
    //     0x725188: stur            w2, [x0, #0x1b]
    // 0x72518c: tbnz            w1, #4, #0x7251a0
    // 0x725190: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x725190: ldur            w1, [x0, #0x17]
    // 0x725194: DecompressPointer r1
    //     0x725194: add             x1, x1, HEAP, lsl #32
    // 0x725198: ldur            x2, [fp, #-0x10]
    // 0x72519c: r0 = write()
    //     0x72519c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x7251a0: r0 = Null
    //     0x7251a0: mov             x0, NULL
    // 0x7251a4: LeaveFrame
    //     0x7251a4: mov             SP, fp
    //     0x7251a8: ldp             fp, lr, [SP], #0x10
    // 0x7251ac: ret
    //     0x7251ac: ret             
    // 0x7251b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7251b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7251b4: b               #0x724af4
    // 0x7251b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7251b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7251bc: b               #0x724c3c
  }
  [closure] static void <anonymous closure>(dynamic, dynamic, dynamic) {
    // ** addr: 0x7251c0, size: 0x34c
    // 0x7251c0: EnterFrame
    //     0x7251c0: stp             fp, lr, [SP, #-0x10]!
    //     0x7251c4: mov             fp, SP
    // 0x7251c8: AllocStack(0x48)
    //     0x7251c8: sub             SP, SP, #0x48
    // 0x7251cc: SetupParameters()
    //     0x7251cc: ldr             x0, [fp, #0x20]
    //     0x7251d0: ldur            w1, [x0, #0x17]
    //     0x7251d4: add             x1, x1, HEAP, lsl #32
    //     0x7251d8: stur            x1, [fp, #-8]
    // 0x7251dc: CheckStackOverflow
    //     0x7251dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7251e0: cmp             SP, x16
    //     0x7251e4: b.ls            #0x725504
    // 0x7251e8: LoadField: r0 = r1->field_f
    //     0x7251e8: ldur            w0, [x1, #0xf]
    // 0x7251ec: DecompressPointer r0
    //     0x7251ec: add             x0, x0, HEAP, lsl #32
    // 0x7251f0: r2 = LoadClassIdInstr(r0)
    //     0x7251f0: ldur            x2, [x0, #-1]
    //     0x7251f4: ubfx            x2, x2, #0xc, #0x14
    // 0x7251f8: r16 = ""
    //     0x7251f8: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7251fc: stp             x16, x0, [SP]
    // 0x725200: mov             x0, x2
    // 0x725204: mov             lr, x0
    // 0x725208: ldr             lr, [x21, lr, lsl #3]
    // 0x72520c: blr             lr
    // 0x725210: tbnz            w0, #4, #0x725334
    // 0x725214: ldur            x1, [fp, #-8]
    // 0x725218: LoadField: r2 = r1->field_b
    //     0x725218: ldur            w2, [x1, #0xb]
    // 0x72521c: DecompressPointer r2
    //     0x72521c: add             x2, x2, HEAP, lsl #32
    // 0x725220: stur            x2, [fp, #-0x18]
    // 0x725224: LoadField: r1 = r2->field_2f
    //     0x725224: ldur            w1, [x2, #0x2f]
    // 0x725228: DecompressPointer r1
    //     0x725228: add             x1, x1, HEAP, lsl #32
    // 0x72522c: stur            x1, [fp, #-0x10]
    // 0x725230: LoadField: r0 = r2->field_2b
    //     0x725230: ldur            w0, [x2, #0x2b]
    // 0x725234: DecompressPointer r0
    //     0x725234: add             x0, x0, HEAP, lsl #32
    // 0x725238: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x725238: ldur            w3, [x0, #0x17]
    // 0x72523c: DecompressPointer r3
    //     0x72523c: add             x3, x3, HEAP, lsl #32
    // 0x725240: LoadField: r0 = r3->field_13
    //     0x725240: ldur            w0, [x3, #0x13]
    // 0x725244: DecompressPointer r0
    //     0x725244: add             x0, x0, HEAP, lsl #32
    // 0x725248: tbz             w0, #4, #0x725254
    // 0x72524c: ldr             x0, [fp, #0x10]
    // 0x725250: b               #0x72527c
    // 0x725254: ldr             x0, [fp, #0x10]
    // 0x725258: cmp             w0, NULL
    // 0x72525c: b.eq            #0x72527c
    // 0x725260: r4 = 60
    //     0x725260: movz            x4, #0x3c
    // 0x725264: branchIfSmi(r0, 0x725270)
    //     0x725264: tbz             w0, #0, #0x725270
    // 0x725268: r4 = LoadClassIdInstr(r0)
    //     0x725268: ldur            x4, [x0, #-1]
    //     0x72526c: ubfx            x4, x4, #0xc, #0x14
    // 0x725270: sub             x16, x4, #0x5e
    // 0x725274: cmp             x16, #1
    // 0x725278: b.ls            #0x725288
    // 0x72527c: mov             x3, x0
    // 0x725280: mov             x0, x2
    // 0x725284: b               #0x7252ac
    // 0x725288: LoadField: r4 = r3->field_27
    //     0x725288: ldur            w4, [x3, #0x27]
    // 0x72528c: DecompressPointer r4
    //     0x72528c: add             x4, x4, HEAP, lsl #32
    // 0x725290: stp             x0, x4, [SP]
    // 0x725294: mov             x0, x4
    // 0x725298: ClosureCall
    //     0x725298: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x72529c: ldur            x2, [x0, #0x1f]
    //     0x7252a0: blr             x2
    // 0x7252a4: mov             x3, x0
    // 0x7252a8: ldur            x0, [fp, #-0x18]
    // 0x7252ac: stur            x3, [fp, #-0x28]
    // 0x7252b0: LoadField: r4 = r0->field_27
    //     0x7252b0: ldur            w4, [x0, #0x27]
    // 0x7252b4: DecompressPointer r4
    //     0x7252b4: add             x4, x4, HEAP, lsl #32
    // 0x7252b8: ldr             x0, [fp, #0x18]
    // 0x7252bc: stur            x4, [fp, #-0x20]
    // 0x7252c0: r2 = Null
    //     0x7252c0: mov             x2, NULL
    // 0x7252c4: r1 = Null
    //     0x7252c4: mov             x1, NULL
    // 0x7252c8: r4 = 60
    //     0x7252c8: movz            x4, #0x3c
    // 0x7252cc: branchIfSmi(r0, 0x7252d8)
    //     0x7252cc: tbz             w0, #0, #0x7252d8
    // 0x7252d0: r4 = LoadClassIdInstr(r0)
    //     0x7252d0: ldur            x4, [x0, #-1]
    //     0x7252d4: ubfx            x4, x4, #0xc, #0x14
    // 0x7252d8: sub             x4, x4, #0x5e
    // 0x7252dc: cmp             x4, #1
    // 0x7252e0: b.ls            #0x7252f4
    // 0x7252e4: r8 = String
    //     0x7252e4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7252e8: r3 = Null
    //     0x7252e8: add             x3, PP, #0x12, lsl #12  ; [pp+0x120a0] Null
    //     0x7252ec: ldr             x3, [x3, #0xa0]
    // 0x7252f0: r0 = String()
    //     0x7252f0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7252f4: ldur            x16, [fp, #-0x20]
    // 0x7252f8: ldr             lr, [fp, #0x18]
    // 0x7252fc: stp             lr, x16, [SP]
    // 0x725300: ldur            x0, [fp, #-0x20]
    // 0x725304: ClosureCall
    //     0x725304: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x725308: ldur            x2, [x0, #0x1f]
    //     0x72530c: blr             x2
    // 0x725310: ldur            x16, [fp, #-0x10]
    // 0x725314: ldur            lr, [fp, #-0x28]
    // 0x725318: stp             lr, x16, [SP, #8]
    // 0x72531c: str             x0, [SP]
    // 0x725320: ldur            x0, [fp, #-0x10]
    // 0x725324: ClosureCall
    //     0x725324: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x725328: ldur            x2, [x0, #0x1f]
    //     0x72532c: blr             x2
    // 0x725330: b               #0x7254f4
    // 0x725334: ldr             x0, [fp, #0x10]
    // 0x725338: ldur            x1, [fp, #-8]
    // 0x72533c: LoadField: r2 = r1->field_b
    //     0x72533c: ldur            w2, [x1, #0xb]
    // 0x725340: DecompressPointer r2
    //     0x725340: add             x2, x2, HEAP, lsl #32
    // 0x725344: stur            x2, [fp, #-0x18]
    // 0x725348: LoadField: r3 = r2->field_2f
    //     0x725348: ldur            w3, [x2, #0x2f]
    // 0x72534c: DecompressPointer r3
    //     0x72534c: add             x3, x3, HEAP, lsl #32
    // 0x725350: stur            x3, [fp, #-0x10]
    // 0x725354: LoadField: r4 = r2->field_2b
    //     0x725354: ldur            w4, [x2, #0x2b]
    // 0x725358: DecompressPointer r4
    //     0x725358: add             x4, x4, HEAP, lsl #32
    // 0x72535c: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x72535c: ldur            w5, [x4, #0x17]
    // 0x725360: DecompressPointer r5
    //     0x725360: add             x5, x5, HEAP, lsl #32
    // 0x725364: LoadField: r4 = r5->field_13
    //     0x725364: ldur            w4, [x5, #0x13]
    // 0x725368: DecompressPointer r4
    //     0x725368: add             x4, x4, HEAP, lsl #32
    // 0x72536c: tbnz            w4, #4, #0x725394
    // 0x725370: cmp             w0, NULL
    // 0x725374: b.eq            #0x725394
    // 0x725378: r4 = 60
    //     0x725378: movz            x4, #0x3c
    // 0x72537c: branchIfSmi(r0, 0x725388)
    //     0x72537c: tbz             w0, #0, #0x725388
    // 0x725380: r4 = LoadClassIdInstr(r0)
    //     0x725380: ldur            x4, [x0, #-1]
    //     0x725384: ubfx            x4, x4, #0xc, #0x14
    // 0x725388: sub             x16, x4, #0x5e
    // 0x72538c: cmp             x16, #1
    // 0x725390: b.ls            #0x7253a4
    // 0x725394: mov             x4, x0
    // 0x725398: mov             x0, x1
    // 0x72539c: mov             x3, x2
    // 0x7253a0: b               #0x7253cc
    // 0x7253a4: LoadField: r4 = r5->field_27
    //     0x7253a4: ldur            w4, [x5, #0x27]
    // 0x7253a8: DecompressPointer r4
    //     0x7253a8: add             x4, x4, HEAP, lsl #32
    // 0x7253ac: stp             x0, x4, [SP]
    // 0x7253b0: mov             x0, x4
    // 0x7253b4: ClosureCall
    //     0x7253b4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x7253b8: ldur            x2, [x0, #0x1f]
    //     0x7253bc: blr             x2
    // 0x7253c0: mov             x4, x0
    // 0x7253c4: ldur            x0, [fp, #-8]
    // 0x7253c8: ldur            x3, [fp, #-0x18]
    // 0x7253cc: stur            x4, [fp, #-0x28]
    // 0x7253d0: LoadField: r5 = r0->field_f
    //     0x7253d0: ldur            w5, [x0, #0xf]
    // 0x7253d4: DecompressPointer r5
    //     0x7253d4: add             x5, x5, HEAP, lsl #32
    // 0x7253d8: stur            x5, [fp, #-0x20]
    // 0x7253dc: r1 = Null
    //     0x7253dc: mov             x1, NULL
    // 0x7253e0: r2 = 8
    //     0x7253e0: movz            x2, #0x8
    // 0x7253e4: r0 = AllocateArray()
    //     0x7253e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7253e8: mov             x3, x0
    // 0x7253ec: ldur            x0, [fp, #-0x20]
    // 0x7253f0: stur            x3, [fp, #-0x30]
    // 0x7253f4: StoreField: r3->field_f = r0
    //     0x7253f4: stur            w0, [x3, #0xf]
    // 0x7253f8: ldur            x4, [fp, #-0x18]
    // 0x7253fc: LoadField: r0 = r4->field_1f
    //     0x7253fc: ldur            w0, [x4, #0x1f]
    // 0x725400: DecompressPointer r0
    //     0x725400: add             x0, x0, HEAP, lsl #32
    // 0x725404: StoreField: r3->field_13 = r0
    //     0x725404: stur            w0, [x3, #0x13]
    // 0x725408: LoadField: r5 = r4->field_27
    //     0x725408: ldur            w5, [x4, #0x27]
    // 0x72540c: DecompressPointer r5
    //     0x72540c: add             x5, x5, HEAP, lsl #32
    // 0x725410: ldr             x0, [fp, #0x18]
    // 0x725414: stur            x5, [fp, #-8]
    // 0x725418: r2 = Null
    //     0x725418: mov             x2, NULL
    // 0x72541c: r1 = Null
    //     0x72541c: mov             x1, NULL
    // 0x725420: r4 = 60
    //     0x725420: movz            x4, #0x3c
    // 0x725424: branchIfSmi(r0, 0x725430)
    //     0x725424: tbz             w0, #0, #0x725430
    // 0x725428: r4 = LoadClassIdInstr(r0)
    //     0x725428: ldur            x4, [x0, #-1]
    //     0x72542c: ubfx            x4, x4, #0xc, #0x14
    // 0x725430: sub             x4, x4, #0x5e
    // 0x725434: cmp             x4, #1
    // 0x725438: b.ls            #0x72544c
    // 0x72543c: r8 = String
    //     0x72543c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x725440: r3 = Null
    //     0x725440: add             x3, PP, #0x12, lsl #12  ; [pp+0x120b0] Null
    //     0x725444: ldr             x3, [x3, #0xb0]
    // 0x725448: r0 = String()
    //     0x725448: bl              #0xed43b0  ; IsType_String_Stub
    // 0x72544c: ldur            x16, [fp, #-8]
    // 0x725450: ldr             lr, [fp, #0x18]
    // 0x725454: stp             lr, x16, [SP]
    // 0x725458: ldur            x0, [fp, #-8]
    // 0x72545c: ClosureCall
    //     0x72545c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x725460: ldur            x2, [x0, #0x1f]
    //     0x725464: blr             x2
    // 0x725468: ldur            x1, [fp, #-0x30]
    // 0x72546c: ArrayStore: r1[2] = r0  ; List_4
    //     0x72546c: add             x25, x1, #0x17
    //     0x725470: str             w0, [x25]
    //     0x725474: tbz             w0, #0, #0x725490
    //     0x725478: ldurb           w16, [x1, #-1]
    //     0x72547c: ldurb           w17, [x0, #-1]
    //     0x725480: and             x16, x17, x16, lsr #2
    //     0x725484: tst             x16, HEAP, lsr #32
    //     0x725488: b.eq            #0x725490
    //     0x72548c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x725490: ldur            x0, [fp, #-0x18]
    // 0x725494: LoadField: r1 = r0->field_23
    //     0x725494: ldur            w1, [x0, #0x23]
    // 0x725498: DecompressPointer r1
    //     0x725498: add             x1, x1, HEAP, lsl #32
    // 0x72549c: mov             x0, x1
    // 0x7254a0: ldur            x1, [fp, #-0x30]
    // 0x7254a4: ArrayStore: r1[3] = r0  ; List_4
    //     0x7254a4: add             x25, x1, #0x1b
    //     0x7254a8: str             w0, [x25]
    //     0x7254ac: tbz             w0, #0, #0x7254c8
    //     0x7254b0: ldurb           w16, [x1, #-1]
    //     0x7254b4: ldurb           w17, [x0, #-1]
    //     0x7254b8: and             x16, x17, x16, lsr #2
    //     0x7254bc: tst             x16, HEAP, lsr #32
    //     0x7254c0: b.eq            #0x7254c8
    //     0x7254c4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7254c8: ldur            x16, [fp, #-0x30]
    // 0x7254cc: str             x16, [SP]
    // 0x7254d0: r0 = _interpolate()
    //     0x7254d0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7254d4: ldur            x16, [fp, #-0x10]
    // 0x7254d8: ldur            lr, [fp, #-0x28]
    // 0x7254dc: stp             lr, x16, [SP, #8]
    // 0x7254e0: str             x0, [SP]
    // 0x7254e4: ldur            x0, [fp, #-0x10]
    // 0x7254e8: ClosureCall
    //     0x7254e8: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x7254ec: ldur            x2, [x0, #0x1f]
    //     0x7254f0: blr             x2
    // 0x7254f4: r0 = Null
    //     0x7254f4: mov             x0, NULL
    // 0x7254f8: LeaveFrame
    //     0x7254f8: mov             SP, fp
    //     0x7254fc: ldp             fp, lr, [SP], #0x10
    // 0x725500: ret
    //     0x725500: ret             
    // 0x725504: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x725504: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x725508: b               #0x7251e8
  }
  [closure] static Object? maybeEncode(dynamic, Object?) {
    // ** addr: 0x72550c, size: 0x9c
    // 0x72550c: EnterFrame
    //     0x72550c: stp             fp, lr, [SP, #-0x10]!
    //     0x725510: mov             fp, SP
    // 0x725514: AllocStack(0x10)
    //     0x725514: sub             SP, SP, #0x10
    // 0x725518: SetupParameters()
    //     0x725518: ldr             x0, [fp, #0x18]
    //     0x72551c: ldur            w1, [x0, #0x17]
    //     0x725520: add             x1, x1, HEAP, lsl #32
    // 0x725524: CheckStackOverflow
    //     0x725524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x725528: cmp             SP, x16
    //     0x72552c: b.ls            #0x7255a0
    // 0x725530: LoadField: r0 = r1->field_13
    //     0x725530: ldur            w0, [x1, #0x13]
    // 0x725534: DecompressPointer r0
    //     0x725534: add             x0, x0, HEAP, lsl #32
    // 0x725538: tbz             w0, #4, #0x725544
    // 0x72553c: ldr             x0, [fp, #0x10]
    // 0x725540: b               #0x72556c
    // 0x725544: ldr             x0, [fp, #0x10]
    // 0x725548: cmp             w0, NULL
    // 0x72554c: b.eq            #0x72556c
    // 0x725550: r2 = 60
    //     0x725550: movz            x2, #0x3c
    // 0x725554: branchIfSmi(r0, 0x725560)
    //     0x725554: tbz             w0, #0, #0x725560
    // 0x725558: r2 = LoadClassIdInstr(r0)
    //     0x725558: ldur            x2, [x0, #-1]
    //     0x72555c: ubfx            x2, x2, #0xc, #0x14
    // 0x725560: sub             x16, x2, #0x5e
    // 0x725564: cmp             x16, #1
    // 0x725568: b.ls            #0x725578
    // 0x72556c: LeaveFrame
    //     0x72556c: mov             SP, fp
    //     0x725570: ldp             fp, lr, [SP], #0x10
    // 0x725574: ret
    //     0x725574: ret             
    // 0x725578: LoadField: r2 = r1->field_27
    //     0x725578: ldur            w2, [x1, #0x27]
    // 0x72557c: DecompressPointer r2
    //     0x72557c: add             x2, x2, HEAP, lsl #32
    // 0x725580: stp             x0, x2, [SP]
    // 0x725584: mov             x0, x2
    // 0x725588: ClosureCall
    //     0x725588: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x72558c: ldur            x2, [x0, #0x1f]
    //     0x725590: blr             x2
    // 0x725594: LeaveFrame
    //     0x725594: mov             SP, fp
    //     0x725598: ldp             fp, lr, [SP], #0x10
    // 0x72559c: ret
    //     0x72559c: ret             
    // 0x7255a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7255a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7255a4: b               #0x725530
  }
}
