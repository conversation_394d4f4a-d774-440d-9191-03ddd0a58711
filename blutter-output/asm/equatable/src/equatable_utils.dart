// lib: , url: package:equatable/src/equatable_utils.dart

// class id: 1048715, size: 0x8
class :: {

  static _ mapPropsToHashCode(/* No info */) {
    // ** addr: 0xbe1140, size: 0x90
    // 0xbe1140: EnterFrame
    //     0xbe1140: stp             fp, lr, [SP, #-0x10]!
    //     0xbe1144: mov             fp, SP
    // 0xbe1148: AllocStack(0x20)
    //     0xbe1148: sub             SP, SP, #0x20
    // 0xbe114c: CheckStackOverflow
    //     0xbe114c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe1150: cmp             SP, x16
    //     0xbe1154: b.ls            #0xbe11c8
    // 0xbe1158: r16 = <int>
    //     0xbe1158: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbe115c: stp             x1, x16, [SP, #0x10]
    // 0xbe1160: r16 = Closure: (int, Object?) => int from Function '_combine@1015072953': static.
    //     0xbe1160: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1bda8] Closure: (int, Object?) => int from Function '_combine@1015072953': static. (0x7e54fb5e11d0)
    //     0xbe1164: ldr             x16, [x16, #0xda8]
    // 0xbe1168: stp             x16, xzr, [SP]
    // 0xbe116c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xbe116c: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xbe1170: r0 = fold()
    //     0xbe1170: bl              #0x895b28  ; [dart:collection] ListBase::fold
    // 0xbe1174: r1 = LoadInt32Instr(r0)
    //     0xbe1174: sbfx            x1, x0, #1, #0x1f
    //     0xbe1178: tbz             w0, #0, #0xbe1180
    //     0xbe117c: ldur            x1, [x0, #7]
    // 0xbe1180: r2 = 67108863
    //     0xbe1180: orr             x2, xzr, #0x3ffffff
    // 0xbe1184: and             x3, x1, x2
    // 0xbe1188: lsl             w2, w3, #3
    // 0xbe118c: add             w3, w1, w2
    // 0xbe1190: r1 = 536870911
    //     0xbe1190: orr             x1, xzr, #0x1fffffff
    // 0xbe1194: and             x2, x3, x1
    // 0xbe1198: lsr             w3, w2, #0xb
    // 0xbe119c: eor             x4, x2, x3
    // 0xbe11a0: r2 = 16383
    //     0xbe11a0: orr             x2, xzr, #0x3fff
    // 0xbe11a4: and             x3, x4, x2
    // 0xbe11a8: lsl             w2, w3, #0xf
    // 0xbe11ac: add             w3, w4, w2
    // 0xbe11b0: and             x2, x3, x1
    // 0xbe11b4: ubfx            x2, x2, #0, #0x20
    // 0xbe11b8: mov             x0, x2
    // 0xbe11bc: LeaveFrame
    //     0xbe11bc: mov             SP, fp
    //     0xbe11c0: ldp             fp, lr, [SP], #0x10
    // 0xbe11c4: ret
    //     0xbe11c4: ret             
    // 0xbe11c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe11c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe11cc: b               #0xbe1158
  }
  [closure] static int _combine(dynamic, int, Object?) {
    // ** addr: 0xbe11d0, size: 0x4c
    // 0xbe11d0: EnterFrame
    //     0xbe11d0: stp             fp, lr, [SP, #-0x10]!
    //     0xbe11d4: mov             fp, SP
    // 0xbe11d8: CheckStackOverflow
    //     0xbe11d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe11dc: cmp             SP, x16
    //     0xbe11e0: b.ls            #0xbe1214
    // 0xbe11e4: ldr             x1, [fp, #0x18]
    // 0xbe11e8: ldr             x2, [fp, #0x10]
    // 0xbe11ec: r0 = _combine()
    //     0xbe11ec: bl              #0xbe121c  ; [package:equatable/src/equatable_utils.dart] ::_combine
    // 0xbe11f0: mov             x2, x0
    // 0xbe11f4: r0 = BoxInt64Instr(r2)
    //     0xbe11f4: sbfiz           x0, x2, #1, #0x1f
    //     0xbe11f8: cmp             x2, x0, asr #1
    //     0xbe11fc: b.eq            #0xbe1208
    //     0xbe1200: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe1204: stur            x2, [x0, #7]
    // 0xbe1208: LeaveFrame
    //     0xbe1208: mov             SP, fp
    //     0xbe120c: ldp             fp, lr, [SP], #0x10
    // 0xbe1210: ret
    //     0xbe1210: ret             
    // 0xbe1214: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe1214: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe1218: b               #0xbe11e4
  }
  static _ _combine(/* No info */) {
    // ** addr: 0xbe121c, size: 0x604
    // 0xbe121c: EnterFrame
    //     0xbe121c: stp             fp, lr, [SP, #-0x10]!
    //     0xbe1220: mov             fp, SP
    // 0xbe1224: AllocStack(0x50)
    //     0xbe1224: sub             SP, SP, #0x50
    // 0xbe1228: SetupParameters(dynamic _ /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xbe1228: mov             x4, x1
    //     0xbe122c: mov             x3, x2
    //     0xbe1230: stur            x1, [fp, #-8]
    //     0xbe1234: stur            x2, [fp, #-0x10]
    // 0xbe1238: CheckStackOverflow
    //     0xbe1238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe123c: cmp             SP, x16
    //     0xbe1240: b.ls            #0xbe1804
    // 0xbe1244: mov             x0, x3
    // 0xbe1248: r2 = Null
    //     0xbe1248: mov             x2, NULL
    // 0xbe124c: r1 = Null
    //     0xbe124c: mov             x1, NULL
    // 0xbe1250: cmp             w0, NULL
    // 0xbe1254: b.eq            #0xbe12ec
    // 0xbe1258: branchIfSmi(r0, 0xbe12ec)
    //     0xbe1258: tbz             w0, #0, #0xbe12ec
    // 0xbe125c: r3 = LoadClassIdInstr(r0)
    //     0xbe125c: ldur            x3, [x0, #-1]
    //     0xbe1260: ubfx            x3, x3, #0xc, #0x14
    // 0xbe1264: r17 = 6717
    //     0xbe1264: movz            x17, #0x1a3d
    // 0xbe1268: cmp             x3, x17
    // 0xbe126c: b.eq            #0xbe12f4
    // 0xbe1270: r4 = LoadClassIdInstr(r0)
    //     0xbe1270: ldur            x4, [x0, #-1]
    //     0xbe1274: ubfx            x4, x4, #0xc, #0x14
    // 0xbe1278: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xbe127c: ldr             x3, [x3, #0x18]
    // 0xbe1280: ldr             x3, [x3, x4, lsl #3]
    // 0xbe1284: LoadField: r3 = r3->field_2b
    //     0xbe1284: ldur            w3, [x3, #0x2b]
    // 0xbe1288: DecompressPointer r3
    //     0xbe1288: add             x3, x3, HEAP, lsl #32
    // 0xbe128c: cmp             w3, NULL
    // 0xbe1290: b.eq            #0xbe12ec
    // 0xbe1294: LoadField: r3 = r3->field_f
    //     0xbe1294: ldur            w3, [x3, #0xf]
    // 0xbe1298: lsr             x3, x3, #3
    // 0xbe129c: r17 = 6717
    //     0xbe129c: movz            x17, #0x1a3d
    // 0xbe12a0: cmp             x3, x17
    // 0xbe12a4: b.eq            #0xbe12f4
    // 0xbe12a8: r3 = SubtypeTestCache
    //     0xbe12a8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bdb0] SubtypeTestCache
    //     0xbe12ac: ldr             x3, [x3, #0xdb0]
    // 0xbe12b0: r30 = Subtype1TestCacheStub
    //     0xbe12b0: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xbe12b4: LoadField: r30 = r30->field_7
    //     0xbe12b4: ldur            lr, [lr, #7]
    // 0xbe12b8: blr             lr
    // 0xbe12bc: cmp             w7, NULL
    // 0xbe12c0: b.eq            #0xbe12cc
    // 0xbe12c4: tbnz            w7, #4, #0xbe12ec
    // 0xbe12c8: b               #0xbe12f4
    // 0xbe12cc: r8 = Map
    //     0xbe12cc: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bdb8] Type: Map
    //     0xbe12d0: ldr             x8, [x8, #0xdb8]
    // 0xbe12d4: r3 = SubtypeTestCache
    //     0xbe12d4: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bdc0] SubtypeTestCache
    //     0xbe12d8: ldr             x3, [x3, #0xdc0]
    // 0xbe12dc: r30 = InstanceOfStub
    //     0xbe12dc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xbe12e0: LoadField: r30 = r30->field_7
    //     0xbe12e0: ldur            lr, [lr, #7]
    // 0xbe12e4: blr             lr
    // 0xbe12e8: b               #0xbe12f8
    // 0xbe12ec: r0 = false
    //     0xbe12ec: add             x0, NULL, #0x30  ; false
    // 0xbe12f0: b               #0xbe12f8
    // 0xbe12f4: r0 = true
    //     0xbe12f4: add             x0, NULL, #0x20  ; true
    // 0xbe12f8: tbnz            w0, #4, #0xbe1488
    // 0xbe12fc: ldur            x3, [fp, #-8]
    // 0xbe1300: ldur            x2, [fp, #-0x10]
    // 0xbe1304: r0 = LoadClassIdInstr(r2)
    //     0xbe1304: ldur            x0, [x2, #-1]
    //     0xbe1308: ubfx            x0, x0, #0xc, #0x14
    // 0xbe130c: mov             x1, x2
    // 0xbe1310: r0 = GDT[cid_x0 + 0x656]()
    //     0xbe1310: add             lr, x0, #0x656
    //     0xbe1314: ldr             lr, [x21, lr, lsl #3]
    //     0xbe1318: blr             lr
    // 0xbe131c: mov             x2, x0
    // 0xbe1320: r1 = Null
    //     0xbe1320: mov             x1, NULL
    // 0xbe1324: r0 = _GrowableList.of()
    //     0xbe1324: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xbe1328: r1 = Function '<anonymous closure>': static.
    //     0xbe1328: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1bdc8] AnonymousClosure: static (0xbe1820), in [package:equatable/src/equatable_utils.dart] ::_combine (0xbe121c)
    //     0xbe132c: ldr             x1, [x1, #0xdc8]
    // 0xbe1330: r2 = Null
    //     0xbe1330: mov             x2, NULL
    // 0xbe1334: stur            x0, [fp, #-0x18]
    // 0xbe1338: r0 = AllocateClosure()
    //     0xbe1338: bl              #0xec1630  ; AllocateClosureStub
    // 0xbe133c: str             x0, [SP]
    // 0xbe1340: ldur            x1, [fp, #-0x18]
    // 0xbe1344: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xbe1344: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xbe1348: r0 = sort()
    //     0xbe1348: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0xbe134c: ldur            x3, [fp, #-0x18]
    // 0xbe1350: LoadField: r4 = r3->field_b
    //     0xbe1350: ldur            w4, [x3, #0xb]
    // 0xbe1354: ldur            x5, [fp, #-8]
    // 0xbe1358: stur            x4, [fp, #-0x38]
    // 0xbe135c: r0 = LoadInt32Instr(r5)
    //     0xbe135c: sbfx            x0, x5, #1, #0x1f
    //     0xbe1360: tbz             w5, #0, #0xbe1368
    //     0xbe1364: ldur            x0, [x5, #7]
    // 0xbe1368: r1 = LoadInt32Instr(r4)
    //     0xbe1368: sbfx            x1, x4, #1, #0x1f
    // 0xbe136c: mov             x6, x0
    // 0xbe1370: mov             x0, x1
    // 0xbe1374: r7 = 0
    //     0xbe1374: movz            x7, #0
    // 0xbe1378: ldur            x5, [fp, #-0x10]
    // 0xbe137c: stur            x7, [fp, #-0x28]
    // 0xbe1380: stur            x6, [fp, #-0x30]
    // 0xbe1384: CheckStackOverflow
    //     0xbe1384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe1388: cmp             SP, x16
    //     0xbe138c: b.ls            #0xbe180c
    // 0xbe1390: cmp             x7, x0
    // 0xbe1394: b.ge            #0xbe1478
    // 0xbe1398: mov             x1, x7
    // 0xbe139c: cmp             x1, x0
    // 0xbe13a0: b.hs            #0xbe1814
    // 0xbe13a4: LoadField: r0 = r3->field_f
    //     0xbe13a4: ldur            w0, [x3, #0xf]
    // 0xbe13a8: DecompressPointer r0
    //     0xbe13a8: add             x0, x0, HEAP, lsl #32
    // 0xbe13ac: ArrayLoad: r8 = r0[r7]  ; Unknown_4
    //     0xbe13ac: add             x16, x0, x7, lsl #2
    //     0xbe13b0: ldur            w8, [x16, #0xf]
    // 0xbe13b4: DecompressPointer r8
    //     0xbe13b4: add             x8, x8, HEAP, lsl #32
    // 0xbe13b8: stur            x8, [fp, #-0x20]
    // 0xbe13bc: r0 = LoadClassIdInstr(r5)
    //     0xbe13bc: ldur            x0, [x5, #-1]
    //     0xbe13c0: ubfx            x0, x0, #0xc, #0x14
    // 0xbe13c4: mov             x1, x5
    // 0xbe13c8: mov             x2, x8
    // 0xbe13cc: r0 = GDT[cid_x0 + -0x114]()
    //     0xbe13cc: sub             lr, x0, #0x114
    //     0xbe13d0: ldr             lr, [x21, lr, lsl #3]
    //     0xbe13d4: blr             lr
    // 0xbe13d8: r1 = Null
    //     0xbe13d8: mov             x1, NULL
    // 0xbe13dc: r2 = 4
    //     0xbe13dc: movz            x2, #0x4
    // 0xbe13e0: stur            x0, [fp, #-0x40]
    // 0xbe13e4: r0 = AllocateArray()
    //     0xbe13e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbe13e8: mov             x2, x0
    // 0xbe13ec: ldur            x0, [fp, #-0x20]
    // 0xbe13f0: stur            x2, [fp, #-0x48]
    // 0xbe13f4: StoreField: r2->field_f = r0
    //     0xbe13f4: stur            w0, [x2, #0xf]
    // 0xbe13f8: ldur            x0, [fp, #-0x40]
    // 0xbe13fc: StoreField: r2->field_13 = r0
    //     0xbe13fc: stur            w0, [x2, #0x13]
    // 0xbe1400: r1 = Null
    //     0xbe1400: mov             x1, NULL
    // 0xbe1404: r0 = AllocateGrowableArray()
    //     0xbe1404: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbe1408: mov             x2, x0
    // 0xbe140c: ldur            x0, [fp, #-0x48]
    // 0xbe1410: StoreField: r2->field_f = r0
    //     0xbe1410: stur            w0, [x2, #0xf]
    // 0xbe1414: r3 = 4
    //     0xbe1414: movz            x3, #0x4
    // 0xbe1418: StoreField: r2->field_b = r3
    //     0xbe1418: stur            w3, [x2, #0xb]
    // 0xbe141c: ldur            x4, [fp, #-0x30]
    // 0xbe1420: r0 = BoxInt64Instr(r4)
    //     0xbe1420: sbfiz           x0, x4, #1, #0x1f
    //     0xbe1424: cmp             x4, x0, asr #1
    //     0xbe1428: b.eq            #0xbe1434
    //     0xbe142c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe1430: stur            x4, [x0, #7]
    // 0xbe1434: mov             x1, x0
    // 0xbe1438: r0 = _combine()
    //     0xbe1438: bl              #0xbe121c  ; [package:equatable/src/equatable_utils.dart] ::_combine
    // 0xbe143c: mov             x1, x0
    // 0xbe1440: ldur            x0, [fp, #-0x30]
    // 0xbe1444: eor             x6, x0, x1
    // 0xbe1448: ldur            x1, [fp, #-0x18]
    // 0xbe144c: LoadField: r0 = r1->field_b
    //     0xbe144c: ldur            w0, [x1, #0xb]
    // 0xbe1450: ldur            x2, [fp, #-0x38]
    // 0xbe1454: cmp             w0, w2
    // 0xbe1458: b.ne            #0xbe17e8
    // 0xbe145c: ldur            x3, [fp, #-0x28]
    // 0xbe1460: add             x7, x3, #1
    // 0xbe1464: r3 = LoadInt32Instr(r0)
    //     0xbe1464: sbfx            x3, x0, #1, #0x1f
    // 0xbe1468: mov             x0, x3
    // 0xbe146c: mov             x3, x1
    // 0xbe1470: mov             x4, x2
    // 0xbe1474: b               #0xbe1378
    // 0xbe1478: mov             x0, x6
    // 0xbe147c: LeaveFrame
    //     0xbe147c: mov             SP, fp
    //     0xbe1480: ldp             fp, lr, [SP], #0x10
    // 0xbe1484: ret
    //     0xbe1484: ret             
    // 0xbe1488: ldur            x5, [fp, #-8]
    // 0xbe148c: ldur            x0, [fp, #-0x10]
    // 0xbe1490: r2 = Null
    //     0xbe1490: mov             x2, NULL
    // 0xbe1494: r1 = Null
    //     0xbe1494: mov             x1, NULL
    // 0xbe1498: cmp             w0, NULL
    // 0xbe149c: b.eq            #0xbe1534
    // 0xbe14a0: branchIfSmi(r0, 0xbe1534)
    //     0xbe14a0: tbz             w0, #0, #0xbe1534
    // 0xbe14a4: r3 = LoadClassIdInstr(r0)
    //     0xbe14a4: ldur            x3, [x0, #-1]
    //     0xbe14a8: ubfx            x3, x3, #0xc, #0x14
    // 0xbe14ac: r17 = 6713
    //     0xbe14ac: movz            x17, #0x1a39
    // 0xbe14b0: cmp             x3, x17
    // 0xbe14b4: b.eq            #0xbe153c
    // 0xbe14b8: r4 = LoadClassIdInstr(r0)
    //     0xbe14b8: ldur            x4, [x0, #-1]
    //     0xbe14bc: ubfx            x4, x4, #0xc, #0x14
    // 0xbe14c0: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xbe14c4: ldr             x3, [x3, #0x18]
    // 0xbe14c8: ldr             x3, [x3, x4, lsl #3]
    // 0xbe14cc: LoadField: r3 = r3->field_2b
    //     0xbe14cc: ldur            w3, [x3, #0x2b]
    // 0xbe14d0: DecompressPointer r3
    //     0xbe14d0: add             x3, x3, HEAP, lsl #32
    // 0xbe14d4: cmp             w3, NULL
    // 0xbe14d8: b.eq            #0xbe1534
    // 0xbe14dc: LoadField: r3 = r3->field_f
    //     0xbe14dc: ldur            w3, [x3, #0xf]
    // 0xbe14e0: lsr             x3, x3, #3
    // 0xbe14e4: r17 = 6713
    //     0xbe14e4: movz            x17, #0x1a39
    // 0xbe14e8: cmp             x3, x17
    // 0xbe14ec: b.eq            #0xbe153c
    // 0xbe14f0: r3 = SubtypeTestCache
    //     0xbe14f0: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bdd0] SubtypeTestCache
    //     0xbe14f4: ldr             x3, [x3, #0xdd0]
    // 0xbe14f8: r30 = Subtype1TestCacheStub
    //     0xbe14f8: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xbe14fc: LoadField: r30 = r30->field_7
    //     0xbe14fc: ldur            lr, [lr, #7]
    // 0xbe1500: blr             lr
    // 0xbe1504: cmp             w7, NULL
    // 0xbe1508: b.eq            #0xbe1514
    // 0xbe150c: tbnz            w7, #4, #0xbe1534
    // 0xbe1510: b               #0xbe153c
    // 0xbe1514: r8 = Set
    //     0xbe1514: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bdd8] Type: Set
    //     0xbe1518: ldr             x8, [x8, #0xdd8]
    // 0xbe151c: r3 = SubtypeTestCache
    //     0xbe151c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bde0] SubtypeTestCache
    //     0xbe1520: ldr             x3, [x3, #0xde0]
    // 0xbe1524: r30 = InstanceOfStub
    //     0xbe1524: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xbe1528: LoadField: r30 = r30->field_7
    //     0xbe1528: ldur            lr, [lr, #7]
    // 0xbe152c: blr             lr
    // 0xbe1530: b               #0xbe1540
    // 0xbe1534: r0 = false
    //     0xbe1534: add             x0, NULL, #0x30  ; false
    // 0xbe1538: b               #0xbe1540
    // 0xbe153c: r0 = true
    //     0xbe153c: add             x0, NULL, #0x20  ; true
    // 0xbe1540: tbnz            w0, #4, #0xbe157c
    // 0xbe1544: ldur            x2, [fp, #-0x10]
    // 0xbe1548: r1 = Null
    //     0xbe1548: mov             x1, NULL
    // 0xbe154c: r0 = _GrowableList.of()
    //     0xbe154c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xbe1550: r1 = Function '<anonymous closure>': static.
    //     0xbe1550: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1bde8] AnonymousClosure: static (0xbe1820), in [package:equatable/src/equatable_utils.dart] ::_combine (0xbe121c)
    //     0xbe1554: ldr             x1, [x1, #0xde8]
    // 0xbe1558: r2 = Null
    //     0xbe1558: mov             x2, NULL
    // 0xbe155c: stur            x0, [fp, #-0x20]
    // 0xbe1560: r0 = AllocateClosure()
    //     0xbe1560: bl              #0xec1630  ; AllocateClosureStub
    // 0xbe1564: str             x0, [SP]
    // 0xbe1568: ldur            x1, [fp, #-0x20]
    // 0xbe156c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xbe156c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xbe1570: r0 = sort()
    //     0xbe1570: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0xbe1574: ldur            x3, [fp, #-0x20]
    // 0xbe1578: b               #0xbe1580
    // 0xbe157c: ldur            x3, [fp, #-0x10]
    // 0xbe1580: mov             x0, x3
    // 0xbe1584: stur            x3, [fp, #-0x10]
    // 0xbe1588: r2 = Null
    //     0xbe1588: mov             x2, NULL
    // 0xbe158c: r1 = Null
    //     0xbe158c: mov             x1, NULL
    // 0xbe1590: cmp             w0, NULL
    // 0xbe1594: b.eq            #0xbe162c
    // 0xbe1598: branchIfSmi(r0, 0xbe162c)
    //     0xbe1598: tbz             w0, #0, #0xbe162c
    // 0xbe159c: r3 = LoadClassIdInstr(r0)
    //     0xbe159c: ldur            x3, [x0, #-1]
    //     0xbe15a0: ubfx            x3, x3, #0xc, #0x14
    // 0xbe15a4: r17 = 7205
    //     0xbe15a4: movz            x17, #0x1c25
    // 0xbe15a8: cmp             x3, x17
    // 0xbe15ac: b.eq            #0xbe1634
    // 0xbe15b0: r4 = LoadClassIdInstr(r0)
    //     0xbe15b0: ldur            x4, [x0, #-1]
    //     0xbe15b4: ubfx            x4, x4, #0xc, #0x14
    // 0xbe15b8: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xbe15bc: ldr             x3, [x3, #0x18]
    // 0xbe15c0: ldr             x3, [x3, x4, lsl #3]
    // 0xbe15c4: LoadField: r3 = r3->field_2b
    //     0xbe15c4: ldur            w3, [x3, #0x2b]
    // 0xbe15c8: DecompressPointer r3
    //     0xbe15c8: add             x3, x3, HEAP, lsl #32
    // 0xbe15cc: cmp             w3, NULL
    // 0xbe15d0: b.eq            #0xbe162c
    // 0xbe15d4: LoadField: r3 = r3->field_f
    //     0xbe15d4: ldur            w3, [x3, #0xf]
    // 0xbe15d8: lsr             x3, x3, #3
    // 0xbe15dc: r17 = 7205
    //     0xbe15dc: movz            x17, #0x1c25
    // 0xbe15e0: cmp             x3, x17
    // 0xbe15e4: b.eq            #0xbe1634
    // 0xbe15e8: r3 = SubtypeTestCache
    //     0xbe15e8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bdf0] SubtypeTestCache
    //     0xbe15ec: ldr             x3, [x3, #0xdf0]
    // 0xbe15f0: r30 = Subtype1TestCacheStub
    //     0xbe15f0: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xbe15f4: LoadField: r30 = r30->field_7
    //     0xbe15f4: ldur            lr, [lr, #7]
    // 0xbe15f8: blr             lr
    // 0xbe15fc: cmp             w7, NULL
    // 0xbe1600: b.eq            #0xbe160c
    // 0xbe1604: tbnz            w7, #4, #0xbe162c
    // 0xbe1608: b               #0xbe1634
    // 0xbe160c: r8 = Iterable
    //     0xbe160c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bdf8] Type: Iterable
    //     0xbe1610: ldr             x8, [x8, #0xdf8]
    // 0xbe1614: r3 = SubtypeTestCache
    //     0xbe1614: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1be00] SubtypeTestCache
    //     0xbe1618: ldr             x3, [x3, #0xe00]
    // 0xbe161c: r30 = InstanceOfStub
    //     0xbe161c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xbe1620: LoadField: r30 = r30->field_7
    //     0xbe1620: ldur            lr, [lr, #7]
    // 0xbe1624: blr             lr
    // 0xbe1628: b               #0xbe1638
    // 0xbe162c: r0 = false
    //     0xbe162c: add             x0, NULL, #0x30  ; false
    // 0xbe1630: b               #0xbe1638
    // 0xbe1634: r0 = true
    //     0xbe1634: add             x0, NULL, #0x20  ; true
    // 0xbe1638: tbnz            w0, #4, #0xbe1758
    // 0xbe163c: ldur            x3, [fp, #-8]
    // 0xbe1640: ldur            x2, [fp, #-0x10]
    // 0xbe1644: r0 = LoadClassIdInstr(r2)
    //     0xbe1644: ldur            x0, [x2, #-1]
    //     0xbe1648: ubfx            x0, x0, #0xc, #0x14
    // 0xbe164c: mov             x1, x2
    // 0xbe1650: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xbe1650: movz            x17, #0xd35d
    //     0xbe1654: add             lr, x0, x17
    //     0xbe1658: ldr             lr, [x21, lr, lsl #3]
    //     0xbe165c: blr             lr
    // 0xbe1660: mov             x2, x0
    // 0xbe1664: ldur            x1, [fp, #-8]
    // 0xbe1668: stur            x2, [fp, #-0x20]
    // 0xbe166c: r0 = LoadInt32Instr(r1)
    //     0xbe166c: sbfx            x0, x1, #1, #0x1f
    //     0xbe1670: tbz             w1, #0, #0xbe1678
    //     0xbe1674: ldur            x0, [x1, #7]
    // 0xbe1678: mov             x3, x0
    // 0xbe167c: stur            x3, [fp, #-0x28]
    // 0xbe1680: CheckStackOverflow
    //     0xbe1680: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe1684: cmp             SP, x16
    //     0xbe1688: b.ls            #0xbe1818
    // 0xbe168c: r0 = LoadClassIdInstr(r2)
    //     0xbe168c: ldur            x0, [x2, #-1]
    //     0xbe1690: ubfx            x0, x0, #0xc, #0x14
    // 0xbe1694: mov             x1, x2
    // 0xbe1698: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xbe1698: movz            x17, #0x292d
    //     0xbe169c: movk            x17, #0x1, lsl #16
    //     0xbe16a0: add             lr, x0, x17
    //     0xbe16a4: ldr             lr, [x21, lr, lsl #3]
    //     0xbe16a8: blr             lr
    // 0xbe16ac: tbnz            w0, #4, #0xbe170c
    // 0xbe16b0: ldur            x2, [fp, #-0x20]
    // 0xbe16b4: ldur            x3, [fp, #-0x28]
    // 0xbe16b8: r0 = LoadClassIdInstr(r2)
    //     0xbe16b8: ldur            x0, [x2, #-1]
    //     0xbe16bc: ubfx            x0, x0, #0xc, #0x14
    // 0xbe16c0: mov             x1, x2
    // 0xbe16c4: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xbe16c4: movz            x17, #0x384d
    //     0xbe16c8: movk            x17, #0x1, lsl #16
    //     0xbe16cc: add             lr, x0, x17
    //     0xbe16d0: ldr             lr, [x21, lr, lsl #3]
    //     0xbe16d4: blr             lr
    // 0xbe16d8: mov             x2, x0
    // 0xbe16dc: ldur            x3, [fp, #-0x28]
    // 0xbe16e0: r0 = BoxInt64Instr(r3)
    //     0xbe16e0: sbfiz           x0, x3, #1, #0x1f
    //     0xbe16e4: cmp             x3, x0, asr #1
    //     0xbe16e8: b.eq            #0xbe16f4
    //     0xbe16ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe16f0: stur            x3, [x0, #7]
    // 0xbe16f4: mov             x1, x0
    // 0xbe16f8: r0 = _combine()
    //     0xbe16f8: bl              #0xbe121c  ; [package:equatable/src/equatable_utils.dart] ::_combine
    // 0xbe16fc: ldur            x1, [fp, #-0x28]
    // 0xbe1700: eor             x3, x1, x0
    // 0xbe1704: ldur            x2, [fp, #-0x20]
    // 0xbe1708: b               #0xbe167c
    // 0xbe170c: ldur            x0, [fp, #-0x10]
    // 0xbe1710: ldur            x1, [fp, #-0x28]
    // 0xbe1714: r2 = LoadClassIdInstr(r0)
    //     0xbe1714: ldur            x2, [x0, #-1]
    //     0xbe1718: ubfx            x2, x2, #0xc, #0x14
    // 0xbe171c: str             x0, [SP]
    // 0xbe1720: mov             x0, x2
    // 0xbe1724: r0 = GDT[cid_x0 + 0xc834]()
    //     0xbe1724: movz            x17, #0xc834
    //     0xbe1728: add             lr, x0, x17
    //     0xbe172c: ldr             lr, [x21, lr, lsl #3]
    //     0xbe1730: blr             lr
    // 0xbe1734: r1 = LoadInt32Instr(r0)
    //     0xbe1734: sbfx            x1, x0, #1, #0x1f
    //     0xbe1738: tbz             w0, #0, #0xbe1740
    //     0xbe173c: ldur            x1, [x0, #7]
    // 0xbe1740: ldur            x0, [fp, #-0x28]
    // 0xbe1744: eor             x2, x0, x1
    // 0xbe1748: mov             x0, x2
    // 0xbe174c: LeaveFrame
    //     0xbe174c: mov             SP, fp
    //     0xbe1750: ldp             fp, lr, [SP], #0x10
    // 0xbe1754: ret
    //     0xbe1754: ret             
    // 0xbe1758: ldur            x1, [fp, #-8]
    // 0xbe175c: ldur            x0, [fp, #-0x10]
    // 0xbe1760: r2 = 60
    //     0xbe1760: movz            x2, #0x3c
    // 0xbe1764: branchIfSmi(r0, 0xbe1770)
    //     0xbe1764: tbz             w0, #0, #0xbe1770
    // 0xbe1768: r2 = LoadClassIdInstr(r0)
    //     0xbe1768: ldur            x2, [x0, #-1]
    //     0xbe176c: ubfx            x2, x2, #0xc, #0x14
    // 0xbe1770: str             x0, [SP]
    // 0xbe1774: mov             x0, x2
    // 0xbe1778: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbe1778: movz            x17, #0x64af
    //     0xbe177c: add             lr, x0, x17
    //     0xbe1780: ldr             lr, [x21, lr, lsl #3]
    //     0xbe1784: blr             lr
    // 0xbe1788: mov             x1, x0
    // 0xbe178c: ldur            x0, [fp, #-8]
    // 0xbe1790: r2 = LoadInt32Instr(r0)
    //     0xbe1790: sbfx            x2, x0, #1, #0x1f
    //     0xbe1794: tbz             w0, #0, #0xbe179c
    //     0xbe1798: ldur            x2, [x0, #7]
    // 0xbe179c: r0 = LoadInt32Instr(r1)
    //     0xbe179c: sbfx            x0, x1, #1, #0x1f
    //     0xbe17a0: tbz             w1, #0, #0xbe17a8
    //     0xbe17a4: ldur            x0, [x1, #7]
    // 0xbe17a8: add             w1, w2, w0
    // 0xbe17ac: r0 = 536870911
    //     0xbe17ac: orr             x0, xzr, #0x1fffffff
    // 0xbe17b0: and             x2, x1, x0
    // 0xbe17b4: r1 = 524287
    //     0xbe17b4: orr             x1, xzr, #0x7ffff
    // 0xbe17b8: and             x3, x2, x1
    // 0xbe17bc: lsl             w1, w3, #0xa
    // 0xbe17c0: add             w3, w2, w1
    // 0xbe17c4: and             x1, x3, x0
    // 0xbe17c8: mov             x0, x1
    // 0xbe17cc: ubfx            x0, x0, #0, #0x20
    // 0xbe17d0: asr             x2, x0, #6
    // 0xbe17d4: ubfx            x1, x1, #0, #0x20
    // 0xbe17d8: eor             x0, x1, x2
    // 0xbe17dc: LeaveFrame
    //     0xbe17dc: mov             SP, fp
    //     0xbe17e0: ldp             fp, lr, [SP], #0x10
    // 0xbe17e4: ret
    //     0xbe17e4: ret             
    // 0xbe17e8: r0 = ConcurrentModificationError()
    //     0xbe17e8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xbe17ec: mov             x1, x0
    // 0xbe17f0: ldur            x0, [fp, #-0x18]
    // 0xbe17f4: StoreField: r1->field_b = r0
    //     0xbe17f4: stur            w0, [x1, #0xb]
    // 0xbe17f8: mov             x0, x1
    // 0xbe17fc: r0 = Throw()
    //     0xbe17fc: bl              #0xec04b8  ; ThrowStub
    // 0xbe1800: brk             #0
    // 0xbe1804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe1804: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe1808: b               #0xbe1244
    // 0xbe180c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe180c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe1810: b               #0xbe1390
    // 0xbe1814: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbe1814: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbe1818: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe1818: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe181c: b               #0xbe168c
  }
  [closure] static int <anonymous closure>(dynamic, Object?, Object?) {
    // ** addr: 0xbe1820, size: 0xc0
    // 0xbe1820: EnterFrame
    //     0xbe1820: stp             fp, lr, [SP, #-0x10]!
    //     0xbe1824: mov             fp, SP
    // 0xbe1828: AllocStack(0x10)
    //     0xbe1828: sub             SP, SP, #0x10
    // 0xbe182c: CheckStackOverflow
    //     0xbe182c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe1830: cmp             SP, x16
    //     0xbe1834: b.ls            #0xbe18d8
    // 0xbe1838: ldr             x0, [fp, #0x18]
    // 0xbe183c: r1 = 60
    //     0xbe183c: movz            x1, #0x3c
    // 0xbe1840: branchIfSmi(r0, 0xbe184c)
    //     0xbe1840: tbz             w0, #0, #0xbe184c
    // 0xbe1844: r1 = LoadClassIdInstr(r0)
    //     0xbe1844: ldur            x1, [x0, #-1]
    //     0xbe1848: ubfx            x1, x1, #0xc, #0x14
    // 0xbe184c: str             x0, [SP]
    // 0xbe1850: mov             x0, x1
    // 0xbe1854: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbe1854: movz            x17, #0x64af
    //     0xbe1858: add             lr, x0, x17
    //     0xbe185c: ldr             lr, [x21, lr, lsl #3]
    //     0xbe1860: blr             lr
    // 0xbe1864: mov             x1, x0
    // 0xbe1868: ldr             x0, [fp, #0x10]
    // 0xbe186c: stur            x1, [fp, #-8]
    // 0xbe1870: r2 = 60
    //     0xbe1870: movz            x2, #0x3c
    // 0xbe1874: branchIfSmi(r0, 0xbe1880)
    //     0xbe1874: tbz             w0, #0, #0xbe1880
    // 0xbe1878: r2 = LoadClassIdInstr(r0)
    //     0xbe1878: ldur            x2, [x0, #-1]
    //     0xbe187c: ubfx            x2, x2, #0xc, #0x14
    // 0xbe1880: str             x0, [SP]
    // 0xbe1884: mov             x0, x2
    // 0xbe1888: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbe1888: movz            x17, #0x64af
    //     0xbe188c: add             lr, x0, x17
    //     0xbe1890: ldr             lr, [x21, lr, lsl #3]
    //     0xbe1894: blr             lr
    // 0xbe1898: ldur            x2, [fp, #-8]
    // 0xbe189c: r3 = LoadInt32Instr(r2)
    //     0xbe189c: sbfx            x3, x2, #1, #0x1f
    //     0xbe18a0: tbz             w2, #0, #0xbe18a8
    //     0xbe18a4: ldur            x3, [x2, #7]
    // 0xbe18a8: r2 = LoadInt32Instr(r0)
    //     0xbe18a8: sbfx            x2, x0, #1, #0x1f
    //     0xbe18ac: tbz             w0, #0, #0xbe18b4
    //     0xbe18b0: ldur            x2, [x0, #7]
    // 0xbe18b4: sub             x4, x3, x2
    // 0xbe18b8: r0 = BoxInt64Instr(r4)
    //     0xbe18b8: sbfiz           x0, x4, #1, #0x1f
    //     0xbe18bc: cmp             x4, x0, asr #1
    //     0xbe18c0: b.eq            #0xbe18cc
    //     0xbe18c4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe18c8: stur            x4, [x0, #7]
    // 0xbe18cc: LeaveFrame
    //     0xbe18cc: mov             SP, fp
    //     0xbe18d0: ldp             fp, lr, [SP], #0x10
    // 0xbe18d4: ret
    //     0xbe18d4: ret             
    // 0xbe18d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe18d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe18dc: b               #0xbe1838
  }
  static _ iterableEquals(/* No info */) {
    // ** addr: 0xd3fa14, size: 0x8e4
    // 0xd3fa14: EnterFrame
    //     0xd3fa14: stp             fp, lr, [SP, #-0x10]!
    //     0xd3fa18: mov             fp, SP
    // 0xd3fa1c: AllocStack(0x48)
    //     0xd3fa1c: sub             SP, SP, #0x48
    // 0xd3fa20: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xd3fa20: stur            x1, [fp, #-8]
    //     0xd3fa24: mov             x16, x2
    //     0xd3fa28: mov             x2, x1
    //     0xd3fa2c: mov             x1, x16
    //     0xd3fa30: stur            x1, [fp, #-0x10]
    // 0xd3fa34: CheckStackOverflow
    //     0xd3fa34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3fa38: cmp             SP, x16
    //     0xd3fa3c: b.ls            #0xd402e0
    // 0xd3fa40: cmp             w2, w1
    // 0xd3fa44: b.ne            #0xd3fa58
    // 0xd3fa48: r0 = true
    //     0xd3fa48: add             x0, NULL, #0x20  ; true
    // 0xd3fa4c: LeaveFrame
    //     0xd3fa4c: mov             SP, fp
    //     0xd3fa50: ldp             fp, lr, [SP], #0x10
    // 0xd3fa54: ret
    //     0xd3fa54: ret             
    // 0xd3fa58: r0 = LoadClassIdInstr(r2)
    //     0xd3fa58: ldur            x0, [x2, #-1]
    //     0xd3fa5c: ubfx            x0, x0, #0xc, #0x14
    // 0xd3fa60: str             x2, [SP]
    // 0xd3fa64: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd3fa64: movz            x17, #0xc834
    //     0xd3fa68: add             lr, x0, x17
    //     0xd3fa6c: ldr             lr, [x21, lr, lsl #3]
    //     0xd3fa70: blr             lr
    // 0xd3fa74: mov             x2, x0
    // 0xd3fa78: ldur            x1, [fp, #-0x10]
    // 0xd3fa7c: stur            x2, [fp, #-0x18]
    // 0xd3fa80: r0 = LoadClassIdInstr(r1)
    //     0xd3fa80: ldur            x0, [x1, #-1]
    //     0xd3fa84: ubfx            x0, x0, #0xc, #0x14
    // 0xd3fa88: str             x1, [SP]
    // 0xd3fa8c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd3fa8c: movz            x17, #0xc834
    //     0xd3fa90: add             lr, x0, x17
    //     0xd3fa94: ldr             lr, [x21, lr, lsl #3]
    //     0xd3fa98: blr             lr
    // 0xd3fa9c: mov             x1, x0
    // 0xd3faa0: ldur            x0, [fp, #-0x18]
    // 0xd3faa4: r2 = LoadInt32Instr(r0)
    //     0xd3faa4: sbfx            x2, x0, #1, #0x1f
    //     0xd3faa8: tbz             w0, #0, #0xd3fab0
    //     0xd3faac: ldur            x2, [x0, #7]
    // 0xd3fab0: r0 = LoadInt32Instr(r1)
    //     0xd3fab0: sbfx            x0, x1, #1, #0x1f
    //     0xd3fab4: tbz             w1, #0, #0xd3fabc
    //     0xd3fab8: ldur            x0, [x1, #7]
    // 0xd3fabc: cmp             x2, x0
    // 0xd3fac0: b.eq            #0xd3fad4
    // 0xd3fac4: r0 = false
    //     0xd3fac4: add             x0, NULL, #0x30  ; false
    // 0xd3fac8: LeaveFrame
    //     0xd3fac8: mov             SP, fp
    //     0xd3facc: ldp             fp, lr, [SP], #0x10
    // 0xd3fad0: ret
    //     0xd3fad0: ret             
    // 0xd3fad4: r3 = 0
    //     0xd3fad4: movz            x3, #0
    // 0xd3fad8: ldur            x2, [fp, #-8]
    // 0xd3fadc: ldur            x1, [fp, #-0x10]
    // 0xd3fae0: stur            x3, [fp, #-0x20]
    // 0xd3fae4: CheckStackOverflow
    //     0xd3fae4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3fae8: cmp             SP, x16
    //     0xd3faec: b.ls            #0xd402e8
    // 0xd3faf0: r0 = LoadClassIdInstr(r2)
    //     0xd3faf0: ldur            x0, [x2, #-1]
    //     0xd3faf4: ubfx            x0, x0, #0xc, #0x14
    // 0xd3faf8: str             x2, [SP]
    // 0xd3fafc: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd3fafc: movz            x17, #0xc834
    //     0xd3fb00: add             lr, x0, x17
    //     0xd3fb04: ldr             lr, [x21, lr, lsl #3]
    //     0xd3fb08: blr             lr
    // 0xd3fb0c: r1 = LoadInt32Instr(r0)
    //     0xd3fb0c: sbfx            x1, x0, #1, #0x1f
    //     0xd3fb10: tbz             w0, #0, #0xd3fb18
    //     0xd3fb14: ldur            x1, [x0, #7]
    // 0xd3fb18: ldur            x3, [fp, #-0x20]
    // 0xd3fb1c: cmp             x3, x1
    // 0xd3fb20: b.ge            #0xd402d0
    // 0xd3fb24: ldur            x5, [fp, #-8]
    // 0xd3fb28: ldur            x4, [fp, #-0x10]
    // 0xd3fb2c: r0 = LoadClassIdInstr(r5)
    //     0xd3fb2c: ldur            x0, [x5, #-1]
    //     0xd3fb30: ubfx            x0, x0, #0xc, #0x14
    // 0xd3fb34: mov             x1, x5
    // 0xd3fb38: mov             x2, x3
    // 0xd3fb3c: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xd3fb3c: movz            x17, #0xd28f
    //     0xd3fb40: add             lr, x0, x17
    //     0xd3fb44: ldr             lr, [x21, lr, lsl #3]
    //     0xd3fb48: blr             lr
    // 0xd3fb4c: mov             x4, x0
    // 0xd3fb50: ldur            x3, [fp, #-0x10]
    // 0xd3fb54: stur            x4, [fp, #-0x18]
    // 0xd3fb58: r0 = LoadClassIdInstr(r3)
    //     0xd3fb58: ldur            x0, [x3, #-1]
    //     0xd3fb5c: ubfx            x0, x0, #0xc, #0x14
    // 0xd3fb60: mov             x1, x3
    // 0xd3fb64: ldur            x2, [fp, #-0x20]
    // 0xd3fb68: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xd3fb68: movz            x17, #0xd28f
    //     0xd3fb6c: add             lr, x0, x17
    //     0xd3fb70: ldr             lr, [x21, lr, lsl #3]
    //     0xd3fb74: blr             lr
    // 0xd3fb78: mov             x1, x0
    // 0xd3fb7c: mov             x2, x0
    // 0xd3fb80: ldur            x0, [fp, #-0x18]
    // 0xd3fb84: stur            x2, [fp, #-0x28]
    // 0xd3fb88: stp             x1, x0, [SP, #-0x10]!
    // 0xd3fb8c: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0xd3fb8c: ldr             lr, [PP, #0x1200]  ; [pp+0x1200] Stub: OptimizedIdenticalWithNumberCheck (0x5f32bc)
    // 0xd3fb90: LoadField: r30 = r30->field_7
    //     0xd3fb90: ldur            lr, [lr, #7]
    // 0xd3fb94: blr             lr
    // 0xd3fb98: ldp             x1, x0, [SP], #0x10
    // 0xd3fb9c: b.eq            #0xd402b4
    // 0xd3fba0: ldur            x3, [fp, #-0x18]
    // 0xd3fba4: r0 = 60
    //     0xd3fba4: movz            x0, #0x3c
    // 0xd3fba8: branchIfSmi(r3, 0xd3fbb4)
    //     0xd3fba8: tbz             w3, #0, #0xd3fbb4
    // 0xd3fbac: r0 = LoadClassIdInstr(r3)
    //     0xd3fbac: ldur            x0, [x3, #-1]
    //     0xd3fbb0: ubfx            x0, x0, #0xc, #0x14
    // 0xd3fbb4: sub             x16, x0, #0x3c
    // 0xd3fbb8: cmp             x16, #2
    // 0xd3fbbc: b.hi            #0xd3fc08
    // 0xd3fbc0: ldur            x4, [fp, #-0x28]
    // 0xd3fbc4: r1 = 60
    //     0xd3fbc4: movz            x1, #0x3c
    // 0xd3fbc8: branchIfSmi(r4, 0xd3fbd4)
    //     0xd3fbc8: tbz             w4, #0, #0xd3fbd4
    // 0xd3fbcc: r1 = LoadClassIdInstr(r4)
    //     0xd3fbcc: ldur            x1, [x4, #-1]
    //     0xd3fbd0: ubfx            x1, x1, #0xc, #0x14
    // 0xd3fbd4: sub             x16, x1, #0x3c
    // 0xd3fbd8: cmp             x16, #2
    // 0xd3fbdc: b.hi            #0xd3fc0c
    // 0xd3fbe0: r0 = 60
    //     0xd3fbe0: movz            x0, #0x3c
    // 0xd3fbe4: branchIfSmi(r3, 0xd3fbf0)
    //     0xd3fbe4: tbz             w3, #0, #0xd3fbf0
    // 0xd3fbe8: r0 = LoadClassIdInstr(r3)
    //     0xd3fbe8: ldur            x0, [x3, #-1]
    //     0xd3fbec: ubfx            x0, x0, #0xc, #0x14
    // 0xd3fbf0: stp             x4, x3, [SP]
    // 0xd3fbf4: mov             lr, x0
    // 0xd3fbf8: ldr             lr, [x21, lr, lsl #3]
    // 0xd3fbfc: blr             lr
    // 0xd3fc00: tbz             w0, #4, #0xd402b4
    // 0xd3fc04: b               #0xd402c0
    // 0xd3fc08: ldur            x4, [fp, #-0x28]
    // 0xd3fc0c: r17 = -5561
    //     0xd3fc0c: movn            x17, #0x15b8
    // 0xd3fc10: add             x16, x0, x17
    // 0xd3fc14: cmp             x16, #0x2a
    // 0xd3fc18: b.hi            #0xd3fc64
    // 0xd3fc1c: r0 = 60
    //     0xd3fc1c: movz            x0, #0x3c
    // 0xd3fc20: branchIfSmi(r4, 0xd3fc2c)
    //     0xd3fc20: tbz             w4, #0, #0xd3fc2c
    // 0xd3fc24: r0 = LoadClassIdInstr(r4)
    //     0xd3fc24: ldur            x0, [x4, #-1]
    //     0xd3fc28: ubfx            x0, x0, #0xc, #0x14
    // 0xd3fc2c: r17 = -5561
    //     0xd3fc2c: movn            x17, #0x15b8
    // 0xd3fc30: add             x16, x0, x17
    // 0xd3fc34: cmp             x16, #0x2a
    // 0xd3fc38: b.hi            #0xd3fc64
    // 0xd3fc3c: r0 = 60
    //     0xd3fc3c: movz            x0, #0x3c
    // 0xd3fc40: branchIfSmi(r3, 0xd3fc4c)
    //     0xd3fc40: tbz             w3, #0, #0xd3fc4c
    // 0xd3fc44: r0 = LoadClassIdInstr(r3)
    //     0xd3fc44: ldur            x0, [x3, #-1]
    //     0xd3fc48: ubfx            x0, x0, #0xc, #0x14
    // 0xd3fc4c: stp             x4, x3, [SP]
    // 0xd3fc50: mov             lr, x0
    // 0xd3fc54: ldr             lr, [x21, lr, lsl #3]
    // 0xd3fc58: blr             lr
    // 0xd3fc5c: tbz             w0, #4, #0xd402b4
    // 0xd3fc60: b               #0xd402c0
    // 0xd3fc64: mov             x0, x3
    // 0xd3fc68: r2 = Null
    //     0xd3fc68: mov             x2, NULL
    // 0xd3fc6c: r1 = Null
    //     0xd3fc6c: mov             x1, NULL
    // 0xd3fc70: cmp             w0, NULL
    // 0xd3fc74: b.eq            #0xd3fd0c
    // 0xd3fc78: branchIfSmi(r0, 0xd3fd0c)
    //     0xd3fc78: tbz             w0, #0, #0xd3fd0c
    // 0xd3fc7c: r3 = LoadClassIdInstr(r0)
    //     0xd3fc7c: ldur            x3, [x0, #-1]
    //     0xd3fc80: ubfx            x3, x3, #0xc, #0x14
    // 0xd3fc84: r17 = 6713
    //     0xd3fc84: movz            x17, #0x1a39
    // 0xd3fc88: cmp             x3, x17
    // 0xd3fc8c: b.eq            #0xd3fd14
    // 0xd3fc90: r4 = LoadClassIdInstr(r0)
    //     0xd3fc90: ldur            x4, [x0, #-1]
    //     0xd3fc94: ubfx            x4, x4, #0xc, #0x14
    // 0xd3fc98: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd3fc9c: ldr             x3, [x3, #0x18]
    // 0xd3fca0: ldr             x3, [x3, x4, lsl #3]
    // 0xd3fca4: LoadField: r3 = r3->field_2b
    //     0xd3fca4: ldur            w3, [x3, #0x2b]
    // 0xd3fca8: DecompressPointer r3
    //     0xd3fca8: add             x3, x3, HEAP, lsl #32
    // 0xd3fcac: cmp             w3, NULL
    // 0xd3fcb0: b.eq            #0xd3fd0c
    // 0xd3fcb4: LoadField: r3 = r3->field_f
    //     0xd3fcb4: ldur            w3, [x3, #0xf]
    // 0xd3fcb8: lsr             x3, x3, #3
    // 0xd3fcbc: r17 = 6713
    //     0xd3fcbc: movz            x17, #0x1a39
    // 0xd3fcc0: cmp             x3, x17
    // 0xd3fcc4: b.eq            #0xd3fd14
    // 0xd3fcc8: r3 = SubtypeTestCache
    //     0xd3fcc8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bfb8] SubtypeTestCache
    //     0xd3fccc: ldr             x3, [x3, #0xfb8]
    // 0xd3fcd0: r30 = Subtype1TestCacheStub
    //     0xd3fcd0: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd3fcd4: LoadField: r30 = r30->field_7
    //     0xd3fcd4: ldur            lr, [lr, #7]
    // 0xd3fcd8: blr             lr
    // 0xd3fcdc: cmp             w7, NULL
    // 0xd3fce0: b.eq            #0xd3fcec
    // 0xd3fce4: tbnz            w7, #4, #0xd3fd0c
    // 0xd3fce8: b               #0xd3fd14
    // 0xd3fcec: r8 = Set
    //     0xd3fcec: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bfc0] Type: Set
    //     0xd3fcf0: ldr             x8, [x8, #0xfc0]
    // 0xd3fcf4: r3 = SubtypeTestCache
    //     0xd3fcf4: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bfc8] SubtypeTestCache
    //     0xd3fcf8: ldr             x3, [x3, #0xfc8]
    // 0xd3fcfc: r30 = InstanceOfStub
    //     0xd3fcfc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd3fd00: LoadField: r30 = r30->field_7
    //     0xd3fd00: ldur            lr, [lr, #7]
    // 0xd3fd04: blr             lr
    // 0xd3fd08: b               #0xd3fd18
    // 0xd3fd0c: r0 = false
    //     0xd3fd0c: add             x0, NULL, #0x30  ; false
    // 0xd3fd10: b               #0xd3fd18
    // 0xd3fd14: r0 = true
    //     0xd3fd14: add             x0, NULL, #0x20  ; true
    // 0xd3fd18: tbnz            w0, #4, #0xd3fde8
    // 0xd3fd1c: ldur            x0, [fp, #-0x28]
    // 0xd3fd20: r2 = Null
    //     0xd3fd20: mov             x2, NULL
    // 0xd3fd24: r1 = Null
    //     0xd3fd24: mov             x1, NULL
    // 0xd3fd28: cmp             w0, NULL
    // 0xd3fd2c: b.eq            #0xd3fdc4
    // 0xd3fd30: branchIfSmi(r0, 0xd3fdc4)
    //     0xd3fd30: tbz             w0, #0, #0xd3fdc4
    // 0xd3fd34: r3 = LoadClassIdInstr(r0)
    //     0xd3fd34: ldur            x3, [x0, #-1]
    //     0xd3fd38: ubfx            x3, x3, #0xc, #0x14
    // 0xd3fd3c: r17 = 6713
    //     0xd3fd3c: movz            x17, #0x1a39
    // 0xd3fd40: cmp             x3, x17
    // 0xd3fd44: b.eq            #0xd3fdcc
    // 0xd3fd48: r4 = LoadClassIdInstr(r0)
    //     0xd3fd48: ldur            x4, [x0, #-1]
    //     0xd3fd4c: ubfx            x4, x4, #0xc, #0x14
    // 0xd3fd50: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd3fd54: ldr             x3, [x3, #0x18]
    // 0xd3fd58: ldr             x3, [x3, x4, lsl #3]
    // 0xd3fd5c: LoadField: r3 = r3->field_2b
    //     0xd3fd5c: ldur            w3, [x3, #0x2b]
    // 0xd3fd60: DecompressPointer r3
    //     0xd3fd60: add             x3, x3, HEAP, lsl #32
    // 0xd3fd64: cmp             w3, NULL
    // 0xd3fd68: b.eq            #0xd3fdc4
    // 0xd3fd6c: LoadField: r3 = r3->field_f
    //     0xd3fd6c: ldur            w3, [x3, #0xf]
    // 0xd3fd70: lsr             x3, x3, #3
    // 0xd3fd74: r17 = 6713
    //     0xd3fd74: movz            x17, #0x1a39
    // 0xd3fd78: cmp             x3, x17
    // 0xd3fd7c: b.eq            #0xd3fdcc
    // 0xd3fd80: r3 = SubtypeTestCache
    //     0xd3fd80: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bfd0] SubtypeTestCache
    //     0xd3fd84: ldr             x3, [x3, #0xfd0]
    // 0xd3fd88: r30 = Subtype1TestCacheStub
    //     0xd3fd88: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd3fd8c: LoadField: r30 = r30->field_7
    //     0xd3fd8c: ldur            lr, [lr, #7]
    // 0xd3fd90: blr             lr
    // 0xd3fd94: cmp             w7, NULL
    // 0xd3fd98: b.eq            #0xd3fda4
    // 0xd3fd9c: tbnz            w7, #4, #0xd3fdc4
    // 0xd3fda0: b               #0xd3fdcc
    // 0xd3fda4: r8 = Set
    //     0xd3fda4: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bfd8] Type: Set
    //     0xd3fda8: ldr             x8, [x8, #0xfd8]
    // 0xd3fdac: r3 = SubtypeTestCache
    //     0xd3fdac: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bfe0] SubtypeTestCache
    //     0xd3fdb0: ldr             x3, [x3, #0xfe0]
    // 0xd3fdb4: r30 = InstanceOfStub
    //     0xd3fdb4: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd3fdb8: LoadField: r30 = r30->field_7
    //     0xd3fdb8: ldur            lr, [lr, #7]
    // 0xd3fdbc: blr             lr
    // 0xd3fdc0: b               #0xd3fdd0
    // 0xd3fdc4: r0 = false
    //     0xd3fdc4: add             x0, NULL, #0x30  ; false
    // 0xd3fdc8: b               #0xd3fdd0
    // 0xd3fdcc: r0 = true
    //     0xd3fdcc: add             x0, NULL, #0x20  ; true
    // 0xd3fdd0: tbnz            w0, #4, #0xd3fde8
    // 0xd3fdd4: ldur            x1, [fp, #-0x18]
    // 0xd3fdd8: ldur            x2, [fp, #-0x28]
    // 0xd3fddc: r0 = setEquals()
    //     0xd3fddc: bl              #0xd42018  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0xd3fde0: tbz             w0, #4, #0xd402b4
    // 0xd3fde4: b               #0xd402c0
    // 0xd3fde8: ldur            x0, [fp, #-0x18]
    // 0xd3fdec: r2 = Null
    //     0xd3fdec: mov             x2, NULL
    // 0xd3fdf0: r1 = Null
    //     0xd3fdf0: mov             x1, NULL
    // 0xd3fdf4: cmp             w0, NULL
    // 0xd3fdf8: b.eq            #0xd3fe90
    // 0xd3fdfc: branchIfSmi(r0, 0xd3fe90)
    //     0xd3fdfc: tbz             w0, #0, #0xd3fe90
    // 0xd3fe00: r3 = LoadClassIdInstr(r0)
    //     0xd3fe00: ldur            x3, [x0, #-1]
    //     0xd3fe04: ubfx            x3, x3, #0xc, #0x14
    // 0xd3fe08: r17 = 7205
    //     0xd3fe08: movz            x17, #0x1c25
    // 0xd3fe0c: cmp             x3, x17
    // 0xd3fe10: b.eq            #0xd3fe98
    // 0xd3fe14: r4 = LoadClassIdInstr(r0)
    //     0xd3fe14: ldur            x4, [x0, #-1]
    //     0xd3fe18: ubfx            x4, x4, #0xc, #0x14
    // 0xd3fe1c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd3fe20: ldr             x3, [x3, #0x18]
    // 0xd3fe24: ldr             x3, [x3, x4, lsl #3]
    // 0xd3fe28: LoadField: r3 = r3->field_2b
    //     0xd3fe28: ldur            w3, [x3, #0x2b]
    // 0xd3fe2c: DecompressPointer r3
    //     0xd3fe2c: add             x3, x3, HEAP, lsl #32
    // 0xd3fe30: cmp             w3, NULL
    // 0xd3fe34: b.eq            #0xd3fe90
    // 0xd3fe38: LoadField: r3 = r3->field_f
    //     0xd3fe38: ldur            w3, [x3, #0xf]
    // 0xd3fe3c: lsr             x3, x3, #3
    // 0xd3fe40: r17 = 7205
    //     0xd3fe40: movz            x17, #0x1c25
    // 0xd3fe44: cmp             x3, x17
    // 0xd3fe48: b.eq            #0xd3fe98
    // 0xd3fe4c: r3 = SubtypeTestCache
    //     0xd3fe4c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bfe8] SubtypeTestCache
    //     0xd3fe50: ldr             x3, [x3, #0xfe8]
    // 0xd3fe54: r30 = Subtype1TestCacheStub
    //     0xd3fe54: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd3fe58: LoadField: r30 = r30->field_7
    //     0xd3fe58: ldur            lr, [lr, #7]
    // 0xd3fe5c: blr             lr
    // 0xd3fe60: cmp             w7, NULL
    // 0xd3fe64: b.eq            #0xd3fe70
    // 0xd3fe68: tbnz            w7, #4, #0xd3fe90
    // 0xd3fe6c: b               #0xd3fe98
    // 0xd3fe70: r8 = Iterable
    //     0xd3fe70: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bff0] Type: Iterable
    //     0xd3fe74: ldr             x8, [x8, #0xff0]
    // 0xd3fe78: r3 = SubtypeTestCache
    //     0xd3fe78: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bff8] SubtypeTestCache
    //     0xd3fe7c: ldr             x3, [x3, #0xff8]
    // 0xd3fe80: r30 = InstanceOfStub
    //     0xd3fe80: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd3fe84: LoadField: r30 = r30->field_7
    //     0xd3fe84: ldur            lr, [lr, #7]
    // 0xd3fe88: blr             lr
    // 0xd3fe8c: b               #0xd3fe9c
    // 0xd3fe90: r0 = false
    //     0xd3fe90: add             x0, NULL, #0x30  ; false
    // 0xd3fe94: b               #0xd3fe9c
    // 0xd3fe98: r0 = true
    //     0xd3fe98: add             x0, NULL, #0x20  ; true
    // 0xd3fe9c: tbnz            w0, #4, #0xd40094
    // 0xd3fea0: ldur            x0, [fp, #-0x28]
    // 0xd3fea4: r2 = Null
    //     0xd3fea4: mov             x2, NULL
    // 0xd3fea8: r1 = Null
    //     0xd3fea8: mov             x1, NULL
    // 0xd3feac: cmp             w0, NULL
    // 0xd3feb0: b.eq            #0xd3ff48
    // 0xd3feb4: branchIfSmi(r0, 0xd3ff48)
    //     0xd3feb4: tbz             w0, #0, #0xd3ff48
    // 0xd3feb8: r3 = LoadClassIdInstr(r0)
    //     0xd3feb8: ldur            x3, [x0, #-1]
    //     0xd3febc: ubfx            x3, x3, #0xc, #0x14
    // 0xd3fec0: r17 = 7205
    //     0xd3fec0: movz            x17, #0x1c25
    // 0xd3fec4: cmp             x3, x17
    // 0xd3fec8: b.eq            #0xd3ff50
    // 0xd3fecc: r4 = LoadClassIdInstr(r0)
    //     0xd3fecc: ldur            x4, [x0, #-1]
    //     0xd3fed0: ubfx            x4, x4, #0xc, #0x14
    // 0xd3fed4: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd3fed8: ldr             x3, [x3, #0x18]
    // 0xd3fedc: ldr             x3, [x3, x4, lsl #3]
    // 0xd3fee0: LoadField: r3 = r3->field_2b
    //     0xd3fee0: ldur            w3, [x3, #0x2b]
    // 0xd3fee4: DecompressPointer r3
    //     0xd3fee4: add             x3, x3, HEAP, lsl #32
    // 0xd3fee8: cmp             w3, NULL
    // 0xd3feec: b.eq            #0xd3ff48
    // 0xd3fef0: LoadField: r3 = r3->field_f
    //     0xd3fef0: ldur            w3, [x3, #0xf]
    // 0xd3fef4: lsr             x3, x3, #3
    // 0xd3fef8: r17 = 7205
    //     0xd3fef8: movz            x17, #0x1c25
    // 0xd3fefc: cmp             x3, x17
    // 0xd3ff00: b.eq            #0xd3ff50
    // 0xd3ff04: r3 = SubtypeTestCache
    //     0xd3ff04: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c000] SubtypeTestCache
    //     0xd3ff08: ldr             x3, [x3]
    // 0xd3ff0c: r30 = Subtype1TestCacheStub
    //     0xd3ff0c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd3ff10: LoadField: r30 = r30->field_7
    //     0xd3ff10: ldur            lr, [lr, #7]
    // 0xd3ff14: blr             lr
    // 0xd3ff18: cmp             w7, NULL
    // 0xd3ff1c: b.eq            #0xd3ff28
    // 0xd3ff20: tbnz            w7, #4, #0xd3ff48
    // 0xd3ff24: b               #0xd3ff50
    // 0xd3ff28: r8 = Iterable
    //     0xd3ff28: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c008] Type: Iterable
    //     0xd3ff2c: ldr             x8, [x8, #8]
    // 0xd3ff30: r3 = SubtypeTestCache
    //     0xd3ff30: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c010] SubtypeTestCache
    //     0xd3ff34: ldr             x3, [x3, #0x10]
    // 0xd3ff38: r30 = InstanceOfStub
    //     0xd3ff38: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd3ff3c: LoadField: r30 = r30->field_7
    //     0xd3ff3c: ldur            lr, [lr, #7]
    // 0xd3ff40: blr             lr
    // 0xd3ff44: b               #0xd3ff54
    // 0xd3ff48: r0 = false
    //     0xd3ff48: add             x0, NULL, #0x30  ; false
    // 0xd3ff4c: b               #0xd3ff54
    // 0xd3ff50: r0 = true
    //     0xd3ff50: add             x0, NULL, #0x20  ; true
    // 0xd3ff54: tbnz            w0, #4, #0xd40094
    // 0xd3ff58: ldur            x1, [fp, #-0x18]
    // 0xd3ff5c: ldur            x2, [fp, #-0x28]
    // 0xd3ff60: cmp             w1, w2
    // 0xd3ff64: b.eq            #0xd402b4
    // 0xd3ff68: r0 = LoadClassIdInstr(r1)
    //     0xd3ff68: ldur            x0, [x1, #-1]
    //     0xd3ff6c: ubfx            x0, x0, #0xc, #0x14
    // 0xd3ff70: str             x1, [SP]
    // 0xd3ff74: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd3ff74: movz            x17, #0xc834
    //     0xd3ff78: add             lr, x0, x17
    //     0xd3ff7c: ldr             lr, [x21, lr, lsl #3]
    //     0xd3ff80: blr             lr
    // 0xd3ff84: mov             x2, x0
    // 0xd3ff88: ldur            x1, [fp, #-0x28]
    // 0xd3ff8c: stur            x2, [fp, #-0x30]
    // 0xd3ff90: r0 = LoadClassIdInstr(r1)
    //     0xd3ff90: ldur            x0, [x1, #-1]
    //     0xd3ff94: ubfx            x0, x0, #0xc, #0x14
    // 0xd3ff98: str             x1, [SP]
    // 0xd3ff9c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd3ff9c: movz            x17, #0xc834
    //     0xd3ffa0: add             lr, x0, x17
    //     0xd3ffa4: ldr             lr, [x21, lr, lsl #3]
    //     0xd3ffa8: blr             lr
    // 0xd3ffac: mov             x1, x0
    // 0xd3ffb0: ldur            x0, [fp, #-0x30]
    // 0xd3ffb4: r2 = LoadInt32Instr(r0)
    //     0xd3ffb4: sbfx            x2, x0, #1, #0x1f
    //     0xd3ffb8: tbz             w0, #0, #0xd3ffc0
    //     0xd3ffbc: ldur            x2, [x0, #7]
    // 0xd3ffc0: r0 = LoadInt32Instr(r1)
    //     0xd3ffc0: sbfx            x0, x1, #1, #0x1f
    //     0xd3ffc4: tbz             w1, #0, #0xd3ffcc
    //     0xd3ffc8: ldur            x0, [x1, #7]
    // 0xd3ffcc: cmp             x2, x0
    // 0xd3ffd0: b.ne            #0xd402c0
    // 0xd3ffd4: r3 = 0
    //     0xd3ffd4: movz            x3, #0
    // 0xd3ffd8: ldur            x2, [fp, #-0x18]
    // 0xd3ffdc: ldur            x1, [fp, #-0x28]
    // 0xd3ffe0: stur            x3, [fp, #-0x38]
    // 0xd3ffe4: CheckStackOverflow
    //     0xd3ffe4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3ffe8: cmp             SP, x16
    //     0xd3ffec: b.ls            #0xd402f0
    // 0xd3fff0: r0 = LoadClassIdInstr(r2)
    //     0xd3fff0: ldur            x0, [x2, #-1]
    //     0xd3fff4: ubfx            x0, x0, #0xc, #0x14
    // 0xd3fff8: str             x2, [SP]
    // 0xd3fffc: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd3fffc: movz            x17, #0xc834
    //     0xd40000: add             lr, x0, x17
    //     0xd40004: ldr             lr, [x21, lr, lsl #3]
    //     0xd40008: blr             lr
    // 0xd4000c: r1 = LoadInt32Instr(r0)
    //     0xd4000c: sbfx            x1, x0, #1, #0x1f
    //     0xd40010: tbz             w0, #0, #0xd40018
    //     0xd40014: ldur            x1, [x0, #7]
    // 0xd40018: ldur            x3, [fp, #-0x38]
    // 0xd4001c: cmp             x3, x1
    // 0xd40020: b.ge            #0xd402b4
    // 0xd40024: ldur            x5, [fp, #-0x18]
    // 0xd40028: ldur            x4, [fp, #-0x28]
    // 0xd4002c: r0 = LoadClassIdInstr(r5)
    //     0xd4002c: ldur            x0, [x5, #-1]
    //     0xd40030: ubfx            x0, x0, #0xc, #0x14
    // 0xd40034: mov             x1, x5
    // 0xd40038: mov             x2, x3
    // 0xd4003c: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xd4003c: movz            x17, #0xd28f
    //     0xd40040: add             lr, x0, x17
    //     0xd40044: ldr             lr, [x21, lr, lsl #3]
    //     0xd40048: blr             lr
    // 0xd4004c: mov             x4, x0
    // 0xd40050: ldur            x3, [fp, #-0x28]
    // 0xd40054: stur            x4, [fp, #-0x30]
    // 0xd40058: r0 = LoadClassIdInstr(r3)
    //     0xd40058: ldur            x0, [x3, #-1]
    //     0xd4005c: ubfx            x0, x0, #0xc, #0x14
    // 0xd40060: mov             x1, x3
    // 0xd40064: ldur            x2, [fp, #-0x38]
    // 0xd40068: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xd40068: movz            x17, #0xd28f
    //     0xd4006c: add             lr, x0, x17
    //     0xd40070: ldr             lr, [x21, lr, lsl #3]
    //     0xd40074: blr             lr
    // 0xd40078: ldur            x1, [fp, #-0x30]
    // 0xd4007c: mov             x2, x0
    // 0xd40080: r0 = objectsEquals()
    //     0xd40080: bl              #0xd41234  ; [package:equatable/src/equatable_utils.dart] ::objectsEquals
    // 0xd40084: tbnz            w0, #4, #0xd402c0
    // 0xd40088: ldur            x0, [fp, #-0x38]
    // 0xd4008c: add             x3, x0, #1
    // 0xd40090: b               #0xd3ffd8
    // 0xd40094: ldur            x0, [fp, #-0x18]
    // 0xd40098: r2 = Null
    //     0xd40098: mov             x2, NULL
    // 0xd4009c: r1 = Null
    //     0xd4009c: mov             x1, NULL
    // 0xd400a0: cmp             w0, NULL
    // 0xd400a4: b.eq            #0xd4013c
    // 0xd400a8: branchIfSmi(r0, 0xd4013c)
    //     0xd400a8: tbz             w0, #0, #0xd4013c
    // 0xd400ac: r3 = LoadClassIdInstr(r0)
    //     0xd400ac: ldur            x3, [x0, #-1]
    //     0xd400b0: ubfx            x3, x3, #0xc, #0x14
    // 0xd400b4: r17 = 6717
    //     0xd400b4: movz            x17, #0x1a3d
    // 0xd400b8: cmp             x3, x17
    // 0xd400bc: b.eq            #0xd40144
    // 0xd400c0: r4 = LoadClassIdInstr(r0)
    //     0xd400c0: ldur            x4, [x0, #-1]
    //     0xd400c4: ubfx            x4, x4, #0xc, #0x14
    // 0xd400c8: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd400cc: ldr             x3, [x3, #0x18]
    // 0xd400d0: ldr             x3, [x3, x4, lsl #3]
    // 0xd400d4: LoadField: r3 = r3->field_2b
    //     0xd400d4: ldur            w3, [x3, #0x2b]
    // 0xd400d8: DecompressPointer r3
    //     0xd400d8: add             x3, x3, HEAP, lsl #32
    // 0xd400dc: cmp             w3, NULL
    // 0xd400e0: b.eq            #0xd4013c
    // 0xd400e4: LoadField: r3 = r3->field_f
    //     0xd400e4: ldur            w3, [x3, #0xf]
    // 0xd400e8: lsr             x3, x3, #3
    // 0xd400ec: r17 = 6717
    //     0xd400ec: movz            x17, #0x1a3d
    // 0xd400f0: cmp             x3, x17
    // 0xd400f4: b.eq            #0xd40144
    // 0xd400f8: r3 = SubtypeTestCache
    //     0xd400f8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c018] SubtypeTestCache
    //     0xd400fc: ldr             x3, [x3, #0x18]
    // 0xd40100: r30 = Subtype1TestCacheStub
    //     0xd40100: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd40104: LoadField: r30 = r30->field_7
    //     0xd40104: ldur            lr, [lr, #7]
    // 0xd40108: blr             lr
    // 0xd4010c: cmp             w7, NULL
    // 0xd40110: b.eq            #0xd4011c
    // 0xd40114: tbnz            w7, #4, #0xd4013c
    // 0xd40118: b               #0xd40144
    // 0xd4011c: r8 = Map
    //     0xd4011c: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c020] Type: Map
    //     0xd40120: ldr             x8, [x8, #0x20]
    // 0xd40124: r3 = SubtypeTestCache
    //     0xd40124: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c028] SubtypeTestCache
    //     0xd40128: ldr             x3, [x3, #0x28]
    // 0xd4012c: r30 = InstanceOfStub
    //     0xd4012c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd40130: LoadField: r30 = r30->field_7
    //     0xd40130: ldur            lr, [lr, #7]
    // 0xd40134: blr             lr
    // 0xd40138: b               #0xd40148
    // 0xd4013c: r0 = false
    //     0xd4013c: add             x0, NULL, #0x30  ; false
    // 0xd40140: b               #0xd40148
    // 0xd40144: r0 = true
    //     0xd40144: add             x0, NULL, #0x20  ; true
    // 0xd40148: tbnz            w0, #4, #0xd40218
    // 0xd4014c: ldur            x0, [fp, #-0x28]
    // 0xd40150: r2 = Null
    //     0xd40150: mov             x2, NULL
    // 0xd40154: r1 = Null
    //     0xd40154: mov             x1, NULL
    // 0xd40158: cmp             w0, NULL
    // 0xd4015c: b.eq            #0xd401f4
    // 0xd40160: branchIfSmi(r0, 0xd401f4)
    //     0xd40160: tbz             w0, #0, #0xd401f4
    // 0xd40164: r3 = LoadClassIdInstr(r0)
    //     0xd40164: ldur            x3, [x0, #-1]
    //     0xd40168: ubfx            x3, x3, #0xc, #0x14
    // 0xd4016c: r17 = 6717
    //     0xd4016c: movz            x17, #0x1a3d
    // 0xd40170: cmp             x3, x17
    // 0xd40174: b.eq            #0xd401fc
    // 0xd40178: r4 = LoadClassIdInstr(r0)
    //     0xd40178: ldur            x4, [x0, #-1]
    //     0xd4017c: ubfx            x4, x4, #0xc, #0x14
    // 0xd40180: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd40184: ldr             x3, [x3, #0x18]
    // 0xd40188: ldr             x3, [x3, x4, lsl #3]
    // 0xd4018c: LoadField: r3 = r3->field_2b
    //     0xd4018c: ldur            w3, [x3, #0x2b]
    // 0xd40190: DecompressPointer r3
    //     0xd40190: add             x3, x3, HEAP, lsl #32
    // 0xd40194: cmp             w3, NULL
    // 0xd40198: b.eq            #0xd401f4
    // 0xd4019c: LoadField: r3 = r3->field_f
    //     0xd4019c: ldur            w3, [x3, #0xf]
    // 0xd401a0: lsr             x3, x3, #3
    // 0xd401a4: r17 = 6717
    //     0xd401a4: movz            x17, #0x1a3d
    // 0xd401a8: cmp             x3, x17
    // 0xd401ac: b.eq            #0xd401fc
    // 0xd401b0: r3 = SubtypeTestCache
    //     0xd401b0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c030] SubtypeTestCache
    //     0xd401b4: ldr             x3, [x3, #0x30]
    // 0xd401b8: r30 = Subtype1TestCacheStub
    //     0xd401b8: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd401bc: LoadField: r30 = r30->field_7
    //     0xd401bc: ldur            lr, [lr, #7]
    // 0xd401c0: blr             lr
    // 0xd401c4: cmp             w7, NULL
    // 0xd401c8: b.eq            #0xd401d4
    // 0xd401cc: tbnz            w7, #4, #0xd401f4
    // 0xd401d0: b               #0xd401fc
    // 0xd401d4: r8 = Map
    //     0xd401d4: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c038] Type: Map
    //     0xd401d8: ldr             x8, [x8, #0x38]
    // 0xd401dc: r3 = SubtypeTestCache
    //     0xd401dc: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c040] SubtypeTestCache
    //     0xd401e0: ldr             x3, [x3, #0x40]
    // 0xd401e4: r30 = InstanceOfStub
    //     0xd401e4: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd401e8: LoadField: r30 = r30->field_7
    //     0xd401e8: ldur            lr, [lr, #7]
    // 0xd401ec: blr             lr
    // 0xd401f0: b               #0xd40200
    // 0xd401f4: r0 = false
    //     0xd401f4: add             x0, NULL, #0x30  ; false
    // 0xd401f8: b               #0xd40200
    // 0xd401fc: r0 = true
    //     0xd401fc: add             x0, NULL, #0x20  ; true
    // 0xd40200: tbnz            w0, #4, #0xd40218
    // 0xd40204: ldur            x1, [fp, #-0x18]
    // 0xd40208: ldur            x2, [fp, #-0x28]
    // 0xd4020c: r0 = mapEquals()
    //     0xd4020c: bl              #0xd402f8  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0xd40210: tbz             w0, #4, #0xd402b4
    // 0xd40214: b               #0xd402c0
    // 0xd40218: ldur            x0, [fp, #-0x18]
    // 0xd4021c: cmp             w0, NULL
    // 0xd40220: b.ne            #0xd4022c
    // 0xd40224: r1 = Null
    //     0xd40224: mov             x1, NULL
    // 0xd40228: b               #0xd40238
    // 0xd4022c: str             x0, [SP]
    // 0xd40230: r0 = runtimeType()
    //     0xd40230: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd40234: mov             x1, x0
    // 0xd40238: ldur            x0, [fp, #-0x28]
    // 0xd4023c: stur            x1, [fp, #-0x30]
    // 0xd40240: cmp             w0, NULL
    // 0xd40244: b.ne            #0xd40254
    // 0xd40248: mov             x0, x1
    // 0xd4024c: r1 = Null
    //     0xd4024c: mov             x1, NULL
    // 0xd40250: b               #0xd40264
    // 0xd40254: str             x0, [SP]
    // 0xd40258: r0 = runtimeType()
    //     0xd40258: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd4025c: mov             x1, x0
    // 0xd40260: ldur            x0, [fp, #-0x30]
    // 0xd40264: r2 = LoadClassIdInstr(r0)
    //     0xd40264: ldur            x2, [x0, #-1]
    //     0xd40268: ubfx            x2, x2, #0xc, #0x14
    // 0xd4026c: stp             x1, x0, [SP]
    // 0xd40270: mov             x0, x2
    // 0xd40274: mov             lr, x0
    // 0xd40278: ldr             lr, [x21, lr, lsl #3]
    // 0xd4027c: blr             lr
    // 0xd40280: tbnz            w0, #4, #0xd402c0
    // 0xd40284: ldur            x0, [fp, #-0x18]
    // 0xd40288: r1 = 60
    //     0xd40288: movz            x1, #0x3c
    // 0xd4028c: branchIfSmi(r0, 0xd40298)
    //     0xd4028c: tbz             w0, #0, #0xd40298
    // 0xd40290: r1 = LoadClassIdInstr(r0)
    //     0xd40290: ldur            x1, [x0, #-1]
    //     0xd40294: ubfx            x1, x1, #0xc, #0x14
    // 0xd40298: ldur            x16, [fp, #-0x28]
    // 0xd4029c: stp             x16, x0, [SP]
    // 0xd402a0: mov             x0, x1
    // 0xd402a4: mov             lr, x0
    // 0xd402a8: ldr             lr, [x21, lr, lsl #3]
    // 0xd402ac: blr             lr
    // 0xd402b0: tbnz            w0, #4, #0xd402c0
    // 0xd402b4: ldur            x1, [fp, #-0x20]
    // 0xd402b8: add             x3, x1, #1
    // 0xd402bc: b               #0xd3fad8
    // 0xd402c0: r0 = false
    //     0xd402c0: add             x0, NULL, #0x30  ; false
    // 0xd402c4: LeaveFrame
    //     0xd402c4: mov             SP, fp
    //     0xd402c8: ldp             fp, lr, [SP], #0x10
    // 0xd402cc: ret
    //     0xd402cc: ret             
    // 0xd402d0: r0 = true
    //     0xd402d0: add             x0, NULL, #0x20  ; true
    // 0xd402d4: LeaveFrame
    //     0xd402d4: mov             SP, fp
    //     0xd402d8: ldp             fp, lr, [SP], #0x10
    // 0xd402dc: ret
    //     0xd402dc: ret             
    // 0xd402e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd402e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd402e4: b               #0xd3fa40
    // 0xd402e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd402e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd402ec: b               #0xd3faf0
    // 0xd402f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd402f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd402f4: b               #0xd3fff0
  }
  static _ mapEquals(/* No info */) {
    // ** addr: 0xd402f8, size: 0xf3c
    // 0xd402f8: EnterFrame
    //     0xd402f8: stp             fp, lr, [SP, #-0x10]!
    //     0xd402fc: mov             fp, SP
    // 0xd40300: AllocStack(0x58)
    //     0xd40300: sub             SP, SP, #0x58
    // 0xd40304: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xd40304: stur            x1, [fp, #-8]
    //     0xd40308: mov             x16, x2
    //     0xd4030c: mov             x2, x1
    //     0xd40310: mov             x1, x16
    //     0xd40314: stur            x1, [fp, #-0x10]
    // 0xd40318: CheckStackOverflow
    //     0xd40318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd4031c: cmp             SP, x16
    //     0xd40320: b.ls            #0xd4121c
    // 0xd40324: cmp             w2, w1
    // 0xd40328: b.ne            #0xd4033c
    // 0xd4032c: r0 = true
    //     0xd4032c: add             x0, NULL, #0x20  ; true
    // 0xd40330: LeaveFrame
    //     0xd40330: mov             SP, fp
    //     0xd40334: ldp             fp, lr, [SP], #0x10
    // 0xd40338: ret
    //     0xd40338: ret             
    // 0xd4033c: r0 = LoadClassIdInstr(r2)
    //     0xd4033c: ldur            x0, [x2, #-1]
    //     0xd40340: ubfx            x0, x0, #0xc, #0x14
    // 0xd40344: str             x2, [SP]
    // 0xd40348: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd40348: movz            x17, #0xc834
    //     0xd4034c: add             lr, x0, x17
    //     0xd40350: ldr             lr, [x21, lr, lsl #3]
    //     0xd40354: blr             lr
    // 0xd40358: mov             x2, x0
    // 0xd4035c: ldur            x1, [fp, #-0x10]
    // 0xd40360: stur            x2, [fp, #-0x18]
    // 0xd40364: r0 = LoadClassIdInstr(r1)
    //     0xd40364: ldur            x0, [x1, #-1]
    //     0xd40368: ubfx            x0, x0, #0xc, #0x14
    // 0xd4036c: str             x1, [SP]
    // 0xd40370: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd40370: movz            x17, #0xc834
    //     0xd40374: add             lr, x0, x17
    //     0xd40378: ldr             lr, [x21, lr, lsl #3]
    //     0xd4037c: blr             lr
    // 0xd40380: mov             x1, x0
    // 0xd40384: ldur            x0, [fp, #-0x18]
    // 0xd40388: r2 = LoadInt32Instr(r0)
    //     0xd40388: sbfx            x2, x0, #1, #0x1f
    //     0xd4038c: tbz             w0, #0, #0xd40394
    //     0xd40390: ldur            x2, [x0, #7]
    // 0xd40394: r0 = LoadInt32Instr(r1)
    //     0xd40394: sbfx            x0, x1, #1, #0x1f
    //     0xd40398: tbz             w1, #0, #0xd403a0
    //     0xd4039c: ldur            x0, [x1, #7]
    // 0xd403a0: cmp             x2, x0
    // 0xd403a4: b.eq            #0xd403b8
    // 0xd403a8: r0 = false
    //     0xd403a8: add             x0, NULL, #0x30  ; false
    // 0xd403ac: LeaveFrame
    //     0xd403ac: mov             SP, fp
    //     0xd403b0: ldp             fp, lr, [SP], #0x10
    // 0xd403b4: ret
    //     0xd403b4: ret             
    // 0xd403b8: ldur            x2, [fp, #-8]
    // 0xd403bc: r0 = LoadClassIdInstr(r2)
    //     0xd403bc: ldur            x0, [x2, #-1]
    //     0xd403c0: ubfx            x0, x0, #0xc, #0x14
    // 0xd403c4: mov             x1, x2
    // 0xd403c8: r0 = GDT[cid_x0 + 0x656]()
    //     0xd403c8: add             lr, x0, #0x656
    //     0xd403cc: ldr             lr, [x21, lr, lsl #3]
    //     0xd403d0: blr             lr
    // 0xd403d4: r1 = LoadClassIdInstr(r0)
    //     0xd403d4: ldur            x1, [x0, #-1]
    //     0xd403d8: ubfx            x1, x1, #0xc, #0x14
    // 0xd403dc: mov             x16, x0
    // 0xd403e0: mov             x0, x1
    // 0xd403e4: mov             x1, x16
    // 0xd403e8: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xd403e8: movz            x17, #0xd35d
    //     0xd403ec: add             lr, x0, x17
    //     0xd403f0: ldr             lr, [x21, lr, lsl #3]
    //     0xd403f4: blr             lr
    // 0xd403f8: mov             x2, x0
    // 0xd403fc: stur            x2, [fp, #-0x18]
    // 0xd40400: ldur            x3, [fp, #-8]
    // 0xd40404: ldur            x4, [fp, #-0x10]
    // 0xd40408: CheckStackOverflow
    //     0xd40408: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd4040c: cmp             SP, x16
    //     0xd40410: b.ls            #0xd41224
    // 0xd40414: r0 = LoadClassIdInstr(r2)
    //     0xd40414: ldur            x0, [x2, #-1]
    //     0xd40418: ubfx            x0, x0, #0xc, #0x14
    // 0xd4041c: mov             x1, x2
    // 0xd40420: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xd40420: movz            x17, #0x292d
    //     0xd40424: movk            x17, #0x1, lsl #16
    //     0xd40428: add             lr, x0, x17
    //     0xd4042c: ldr             lr, [x21, lr, lsl #3]
    //     0xd40430: blr             lr
    // 0xd40434: tbnz            w0, #4, #0xd4120c
    // 0xd40438: ldur            x3, [fp, #-8]
    // 0xd4043c: ldur            x4, [fp, #-0x10]
    // 0xd40440: ldur            x2, [fp, #-0x18]
    // 0xd40444: r0 = LoadClassIdInstr(r2)
    //     0xd40444: ldur            x0, [x2, #-1]
    //     0xd40448: ubfx            x0, x0, #0xc, #0x14
    // 0xd4044c: mov             x1, x2
    // 0xd40450: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xd40450: movz            x17, #0x384d
    //     0xd40454: movk            x17, #0x1, lsl #16
    //     0xd40458: add             lr, x0, x17
    //     0xd4045c: ldr             lr, [x21, lr, lsl #3]
    //     0xd40460: blr             lr
    // 0xd40464: mov             x4, x0
    // 0xd40468: ldur            x3, [fp, #-8]
    // 0xd4046c: stur            x4, [fp, #-0x20]
    // 0xd40470: r0 = LoadClassIdInstr(r3)
    //     0xd40470: ldur            x0, [x3, #-1]
    //     0xd40474: ubfx            x0, x0, #0xc, #0x14
    // 0xd40478: mov             x1, x3
    // 0xd4047c: mov             x2, x4
    // 0xd40480: r0 = GDT[cid_x0 + -0x114]()
    //     0xd40480: sub             lr, x0, #0x114
    //     0xd40484: ldr             lr, [x21, lr, lsl #3]
    //     0xd40488: blr             lr
    // 0xd4048c: mov             x4, x0
    // 0xd40490: ldur            x3, [fp, #-0x10]
    // 0xd40494: stur            x4, [fp, #-0x28]
    // 0xd40498: r0 = LoadClassIdInstr(r3)
    //     0xd40498: ldur            x0, [x3, #-1]
    //     0xd4049c: ubfx            x0, x0, #0xc, #0x14
    // 0xd404a0: mov             x1, x3
    // 0xd404a4: ldur            x2, [fp, #-0x20]
    // 0xd404a8: r0 = GDT[cid_x0 + -0x114]()
    //     0xd404a8: sub             lr, x0, #0x114
    //     0xd404ac: ldr             lr, [x21, lr, lsl #3]
    //     0xd404b0: blr             lr
    // 0xd404b4: mov             x1, x0
    // 0xd404b8: mov             x2, x0
    // 0xd404bc: ldur            x0, [fp, #-0x28]
    // 0xd404c0: stur            x2, [fp, #-0x20]
    // 0xd404c4: stp             x1, x0, [SP, #-0x10]!
    // 0xd404c8: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0xd404c8: ldr             lr, [PP, #0x1200]  ; [pp+0x1200] Stub: OptimizedIdenticalWithNumberCheck (0x5f32bc)
    // 0xd404cc: LoadField: r30 = r30->field_7
    //     0xd404cc: ldur            lr, [lr, #7]
    // 0xd404d0: blr             lr
    // 0xd404d4: ldp             x1, x0, [SP], #0x10
    // 0xd404d8: b.eq            #0xd411f4
    // 0xd404dc: ldur            x3, [fp, #-0x28]
    // 0xd404e0: r0 = 60
    //     0xd404e0: movz            x0, #0x3c
    // 0xd404e4: branchIfSmi(r3, 0xd404f0)
    //     0xd404e4: tbz             w3, #0, #0xd404f0
    // 0xd404e8: r0 = LoadClassIdInstr(r3)
    //     0xd404e8: ldur            x0, [x3, #-1]
    //     0xd404ec: ubfx            x0, x0, #0xc, #0x14
    // 0xd404f0: sub             x16, x0, #0x3c
    // 0xd404f4: cmp             x16, #2
    // 0xd404f8: b.hi            #0xd40544
    // 0xd404fc: ldur            x4, [fp, #-0x20]
    // 0xd40500: r1 = 60
    //     0xd40500: movz            x1, #0x3c
    // 0xd40504: branchIfSmi(r4, 0xd40510)
    //     0xd40504: tbz             w4, #0, #0xd40510
    // 0xd40508: r1 = LoadClassIdInstr(r4)
    //     0xd40508: ldur            x1, [x4, #-1]
    //     0xd4050c: ubfx            x1, x1, #0xc, #0x14
    // 0xd40510: sub             x16, x1, #0x3c
    // 0xd40514: cmp             x16, #2
    // 0xd40518: b.hi            #0xd40548
    // 0xd4051c: r0 = 60
    //     0xd4051c: movz            x0, #0x3c
    // 0xd40520: branchIfSmi(r3, 0xd4052c)
    //     0xd40520: tbz             w3, #0, #0xd4052c
    // 0xd40524: r0 = LoadClassIdInstr(r3)
    //     0xd40524: ldur            x0, [x3, #-1]
    //     0xd40528: ubfx            x0, x0, #0xc, #0x14
    // 0xd4052c: stp             x4, x3, [SP]
    // 0xd40530: mov             lr, x0
    // 0xd40534: ldr             lr, [x21, lr, lsl #3]
    // 0xd40538: blr             lr
    // 0xd4053c: tbz             w0, #4, #0xd411f4
    // 0xd40540: b               #0xd411fc
    // 0xd40544: ldur            x4, [fp, #-0x20]
    // 0xd40548: r17 = -5561
    //     0xd40548: movn            x17, #0x15b8
    // 0xd4054c: add             x16, x0, x17
    // 0xd40550: cmp             x16, #0x2a
    // 0xd40554: b.hi            #0xd405a0
    // 0xd40558: r0 = 60
    //     0xd40558: movz            x0, #0x3c
    // 0xd4055c: branchIfSmi(r4, 0xd40568)
    //     0xd4055c: tbz             w4, #0, #0xd40568
    // 0xd40560: r0 = LoadClassIdInstr(r4)
    //     0xd40560: ldur            x0, [x4, #-1]
    //     0xd40564: ubfx            x0, x0, #0xc, #0x14
    // 0xd40568: r17 = -5561
    //     0xd40568: movn            x17, #0x15b8
    // 0xd4056c: add             x16, x0, x17
    // 0xd40570: cmp             x16, #0x2a
    // 0xd40574: b.hi            #0xd405a0
    // 0xd40578: r0 = 60
    //     0xd40578: movz            x0, #0x3c
    // 0xd4057c: branchIfSmi(r3, 0xd40588)
    //     0xd4057c: tbz             w3, #0, #0xd40588
    // 0xd40580: r0 = LoadClassIdInstr(r3)
    //     0xd40580: ldur            x0, [x3, #-1]
    //     0xd40584: ubfx            x0, x0, #0xc, #0x14
    // 0xd40588: stp             x4, x3, [SP]
    // 0xd4058c: mov             lr, x0
    // 0xd40590: ldr             lr, [x21, lr, lsl #3]
    // 0xd40594: blr             lr
    // 0xd40598: tbz             w0, #4, #0xd411f4
    // 0xd4059c: b               #0xd411fc
    // 0xd405a0: mov             x0, x3
    // 0xd405a4: r2 = Null
    //     0xd405a4: mov             x2, NULL
    // 0xd405a8: r1 = Null
    //     0xd405a8: mov             x1, NULL
    // 0xd405ac: cmp             w0, NULL
    // 0xd405b0: b.eq            #0xd40648
    // 0xd405b4: branchIfSmi(r0, 0xd40648)
    //     0xd405b4: tbz             w0, #0, #0xd40648
    // 0xd405b8: r3 = LoadClassIdInstr(r0)
    //     0xd405b8: ldur            x3, [x0, #-1]
    //     0xd405bc: ubfx            x3, x3, #0xc, #0x14
    // 0xd405c0: r17 = 6713
    //     0xd405c0: movz            x17, #0x1a39
    // 0xd405c4: cmp             x3, x17
    // 0xd405c8: b.eq            #0xd40650
    // 0xd405cc: r4 = LoadClassIdInstr(r0)
    //     0xd405cc: ldur            x4, [x0, #-1]
    //     0xd405d0: ubfx            x4, x4, #0xc, #0x14
    // 0xd405d4: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd405d8: ldr             x3, [x3, #0x18]
    // 0xd405dc: ldr             x3, [x3, x4, lsl #3]
    // 0xd405e0: LoadField: r3 = r3->field_2b
    //     0xd405e0: ldur            w3, [x3, #0x2b]
    // 0xd405e4: DecompressPointer r3
    //     0xd405e4: add             x3, x3, HEAP, lsl #32
    // 0xd405e8: cmp             w3, NULL
    // 0xd405ec: b.eq            #0xd40648
    // 0xd405f0: LoadField: r3 = r3->field_f
    //     0xd405f0: ldur            w3, [x3, #0xf]
    // 0xd405f4: lsr             x3, x3, #3
    // 0xd405f8: r17 = 6713
    //     0xd405f8: movz            x17, #0x1a39
    // 0xd405fc: cmp             x3, x17
    // 0xd40600: b.eq            #0xd40650
    // 0xd40604: r3 = SubtypeTestCache
    //     0xd40604: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1be98] SubtypeTestCache
    //     0xd40608: ldr             x3, [x3, #0xe98]
    // 0xd4060c: r30 = Subtype1TestCacheStub
    //     0xd4060c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd40610: LoadField: r30 = r30->field_7
    //     0xd40610: ldur            lr, [lr, #7]
    // 0xd40614: blr             lr
    // 0xd40618: cmp             w7, NULL
    // 0xd4061c: b.eq            #0xd40628
    // 0xd40620: tbnz            w7, #4, #0xd40648
    // 0xd40624: b               #0xd40650
    // 0xd40628: r8 = Set
    //     0xd40628: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bea0] Type: Set
    //     0xd4062c: ldr             x8, [x8, #0xea0]
    // 0xd40630: r3 = SubtypeTestCache
    //     0xd40630: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bea8] SubtypeTestCache
    //     0xd40634: ldr             x3, [x3, #0xea8]
    // 0xd40638: r30 = InstanceOfStub
    //     0xd40638: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd4063c: LoadField: r30 = r30->field_7
    //     0xd4063c: ldur            lr, [lr, #7]
    // 0xd40640: blr             lr
    // 0xd40644: b               #0xd40654
    // 0xd40648: r0 = false
    //     0xd40648: add             x0, NULL, #0x30  ; false
    // 0xd4064c: b               #0xd40654
    // 0xd40650: r0 = true
    //     0xd40650: add             x0, NULL, #0x20  ; true
    // 0xd40654: tbnz            w0, #4, #0xd40724
    // 0xd40658: ldur            x0, [fp, #-0x20]
    // 0xd4065c: r2 = Null
    //     0xd4065c: mov             x2, NULL
    // 0xd40660: r1 = Null
    //     0xd40660: mov             x1, NULL
    // 0xd40664: cmp             w0, NULL
    // 0xd40668: b.eq            #0xd40700
    // 0xd4066c: branchIfSmi(r0, 0xd40700)
    //     0xd4066c: tbz             w0, #0, #0xd40700
    // 0xd40670: r3 = LoadClassIdInstr(r0)
    //     0xd40670: ldur            x3, [x0, #-1]
    //     0xd40674: ubfx            x3, x3, #0xc, #0x14
    // 0xd40678: r17 = 6713
    //     0xd40678: movz            x17, #0x1a39
    // 0xd4067c: cmp             x3, x17
    // 0xd40680: b.eq            #0xd40708
    // 0xd40684: r4 = LoadClassIdInstr(r0)
    //     0xd40684: ldur            x4, [x0, #-1]
    //     0xd40688: ubfx            x4, x4, #0xc, #0x14
    // 0xd4068c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd40690: ldr             x3, [x3, #0x18]
    // 0xd40694: ldr             x3, [x3, x4, lsl #3]
    // 0xd40698: LoadField: r3 = r3->field_2b
    //     0xd40698: ldur            w3, [x3, #0x2b]
    // 0xd4069c: DecompressPointer r3
    //     0xd4069c: add             x3, x3, HEAP, lsl #32
    // 0xd406a0: cmp             w3, NULL
    // 0xd406a4: b.eq            #0xd40700
    // 0xd406a8: LoadField: r3 = r3->field_f
    //     0xd406a8: ldur            w3, [x3, #0xf]
    // 0xd406ac: lsr             x3, x3, #3
    // 0xd406b0: r17 = 6713
    //     0xd406b0: movz            x17, #0x1a39
    // 0xd406b4: cmp             x3, x17
    // 0xd406b8: b.eq            #0xd40708
    // 0xd406bc: r3 = SubtypeTestCache
    //     0xd406bc: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1beb0] SubtypeTestCache
    //     0xd406c0: ldr             x3, [x3, #0xeb0]
    // 0xd406c4: r30 = Subtype1TestCacheStub
    //     0xd406c4: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd406c8: LoadField: r30 = r30->field_7
    //     0xd406c8: ldur            lr, [lr, #7]
    // 0xd406cc: blr             lr
    // 0xd406d0: cmp             w7, NULL
    // 0xd406d4: b.eq            #0xd406e0
    // 0xd406d8: tbnz            w7, #4, #0xd40700
    // 0xd406dc: b               #0xd40708
    // 0xd406e0: r8 = Set
    //     0xd406e0: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1beb8] Type: Set
    //     0xd406e4: ldr             x8, [x8, #0xeb8]
    // 0xd406e8: r3 = SubtypeTestCache
    //     0xd406e8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bec0] SubtypeTestCache
    //     0xd406ec: ldr             x3, [x3, #0xec0]
    // 0xd406f0: r30 = InstanceOfStub
    //     0xd406f0: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd406f4: LoadField: r30 = r30->field_7
    //     0xd406f4: ldur            lr, [lr, #7]
    // 0xd406f8: blr             lr
    // 0xd406fc: b               #0xd4070c
    // 0xd40700: r0 = false
    //     0xd40700: add             x0, NULL, #0x30  ; false
    // 0xd40704: b               #0xd4070c
    // 0xd40708: r0 = true
    //     0xd40708: add             x0, NULL, #0x20  ; true
    // 0xd4070c: tbnz            w0, #4, #0xd40724
    // 0xd40710: ldur            x1, [fp, #-0x28]
    // 0xd40714: ldur            x2, [fp, #-0x20]
    // 0xd40718: r0 = setEquals()
    //     0xd40718: bl              #0xd42018  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0xd4071c: tbz             w0, #4, #0xd411f4
    // 0xd40720: b               #0xd411fc
    // 0xd40724: ldur            x0, [fp, #-0x28]
    // 0xd40728: r2 = Null
    //     0xd40728: mov             x2, NULL
    // 0xd4072c: r1 = Null
    //     0xd4072c: mov             x1, NULL
    // 0xd40730: cmp             w0, NULL
    // 0xd40734: b.eq            #0xd407cc
    // 0xd40738: branchIfSmi(r0, 0xd407cc)
    //     0xd40738: tbz             w0, #0, #0xd407cc
    // 0xd4073c: r3 = LoadClassIdInstr(r0)
    //     0xd4073c: ldur            x3, [x0, #-1]
    //     0xd40740: ubfx            x3, x3, #0xc, #0x14
    // 0xd40744: r17 = 7205
    //     0xd40744: movz            x17, #0x1c25
    // 0xd40748: cmp             x3, x17
    // 0xd4074c: b.eq            #0xd407d4
    // 0xd40750: r4 = LoadClassIdInstr(r0)
    //     0xd40750: ldur            x4, [x0, #-1]
    //     0xd40754: ubfx            x4, x4, #0xc, #0x14
    // 0xd40758: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd4075c: ldr             x3, [x3, #0x18]
    // 0xd40760: ldr             x3, [x3, x4, lsl #3]
    // 0xd40764: LoadField: r3 = r3->field_2b
    //     0xd40764: ldur            w3, [x3, #0x2b]
    // 0xd40768: DecompressPointer r3
    //     0xd40768: add             x3, x3, HEAP, lsl #32
    // 0xd4076c: cmp             w3, NULL
    // 0xd40770: b.eq            #0xd407cc
    // 0xd40774: LoadField: r3 = r3->field_f
    //     0xd40774: ldur            w3, [x3, #0xf]
    // 0xd40778: lsr             x3, x3, #3
    // 0xd4077c: r17 = 7205
    //     0xd4077c: movz            x17, #0x1c25
    // 0xd40780: cmp             x3, x17
    // 0xd40784: b.eq            #0xd407d4
    // 0xd40788: r3 = SubtypeTestCache
    //     0xd40788: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bec8] SubtypeTestCache
    //     0xd4078c: ldr             x3, [x3, #0xec8]
    // 0xd40790: r30 = Subtype1TestCacheStub
    //     0xd40790: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd40794: LoadField: r30 = r30->field_7
    //     0xd40794: ldur            lr, [lr, #7]
    // 0xd40798: blr             lr
    // 0xd4079c: cmp             w7, NULL
    // 0xd407a0: b.eq            #0xd407ac
    // 0xd407a4: tbnz            w7, #4, #0xd407cc
    // 0xd407a8: b               #0xd407d4
    // 0xd407ac: r8 = Iterable
    //     0xd407ac: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bed0] Type: Iterable
    //     0xd407b0: ldr             x8, [x8, #0xed0]
    // 0xd407b4: r3 = SubtypeTestCache
    //     0xd407b4: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bed8] SubtypeTestCache
    //     0xd407b8: ldr             x3, [x3, #0xed8]
    // 0xd407bc: r30 = InstanceOfStub
    //     0xd407bc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd407c0: LoadField: r30 = r30->field_7
    //     0xd407c0: ldur            lr, [lr, #7]
    // 0xd407c4: blr             lr
    // 0xd407c8: b               #0xd407d8
    // 0xd407cc: r0 = false
    //     0xd407cc: add             x0, NULL, #0x30  ; false
    // 0xd407d0: b               #0xd407d8
    // 0xd407d4: r0 = true
    //     0xd407d4: add             x0, NULL, #0x20  ; true
    // 0xd407d8: tbnz            w0, #4, #0xd40fd4
    // 0xd407dc: ldur            x0, [fp, #-0x20]
    // 0xd407e0: r2 = Null
    //     0xd407e0: mov             x2, NULL
    // 0xd407e4: r1 = Null
    //     0xd407e4: mov             x1, NULL
    // 0xd407e8: cmp             w0, NULL
    // 0xd407ec: b.eq            #0xd40884
    // 0xd407f0: branchIfSmi(r0, 0xd40884)
    //     0xd407f0: tbz             w0, #0, #0xd40884
    // 0xd407f4: r3 = LoadClassIdInstr(r0)
    //     0xd407f4: ldur            x3, [x0, #-1]
    //     0xd407f8: ubfx            x3, x3, #0xc, #0x14
    // 0xd407fc: r17 = 7205
    //     0xd407fc: movz            x17, #0x1c25
    // 0xd40800: cmp             x3, x17
    // 0xd40804: b.eq            #0xd4088c
    // 0xd40808: r4 = LoadClassIdInstr(r0)
    //     0xd40808: ldur            x4, [x0, #-1]
    //     0xd4080c: ubfx            x4, x4, #0xc, #0x14
    // 0xd40810: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd40814: ldr             x3, [x3, #0x18]
    // 0xd40818: ldr             x3, [x3, x4, lsl #3]
    // 0xd4081c: LoadField: r3 = r3->field_2b
    //     0xd4081c: ldur            w3, [x3, #0x2b]
    // 0xd40820: DecompressPointer r3
    //     0xd40820: add             x3, x3, HEAP, lsl #32
    // 0xd40824: cmp             w3, NULL
    // 0xd40828: b.eq            #0xd40884
    // 0xd4082c: LoadField: r3 = r3->field_f
    //     0xd4082c: ldur            w3, [x3, #0xf]
    // 0xd40830: lsr             x3, x3, #3
    // 0xd40834: r17 = 7205
    //     0xd40834: movz            x17, #0x1c25
    // 0xd40838: cmp             x3, x17
    // 0xd4083c: b.eq            #0xd4088c
    // 0xd40840: r3 = SubtypeTestCache
    //     0xd40840: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bee0] SubtypeTestCache
    //     0xd40844: ldr             x3, [x3, #0xee0]
    // 0xd40848: r30 = Subtype1TestCacheStub
    //     0xd40848: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd4084c: LoadField: r30 = r30->field_7
    //     0xd4084c: ldur            lr, [lr, #7]
    // 0xd40850: blr             lr
    // 0xd40854: cmp             w7, NULL
    // 0xd40858: b.eq            #0xd40864
    // 0xd4085c: tbnz            w7, #4, #0xd40884
    // 0xd40860: b               #0xd4088c
    // 0xd40864: r8 = Iterable
    //     0xd40864: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bee8] Type: Iterable
    //     0xd40868: ldr             x8, [x8, #0xee8]
    // 0xd4086c: r3 = SubtypeTestCache
    //     0xd4086c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bef0] SubtypeTestCache
    //     0xd40870: ldr             x3, [x3, #0xef0]
    // 0xd40874: r30 = InstanceOfStub
    //     0xd40874: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd40878: LoadField: r30 = r30->field_7
    //     0xd40878: ldur            lr, [lr, #7]
    // 0xd4087c: blr             lr
    // 0xd40880: b               #0xd40890
    // 0xd40884: r0 = false
    //     0xd40884: add             x0, NULL, #0x30  ; false
    // 0xd40888: b               #0xd40890
    // 0xd4088c: r0 = true
    //     0xd4088c: add             x0, NULL, #0x20  ; true
    // 0xd40890: tbnz            w0, #4, #0xd40fd4
    // 0xd40894: ldur            x1, [fp, #-0x28]
    // 0xd40898: ldur            x2, [fp, #-0x20]
    // 0xd4089c: cmp             w1, w2
    // 0xd408a0: b.eq            #0xd411f4
    // 0xd408a4: r0 = LoadClassIdInstr(r1)
    //     0xd408a4: ldur            x0, [x1, #-1]
    //     0xd408a8: ubfx            x0, x0, #0xc, #0x14
    // 0xd408ac: str             x1, [SP]
    // 0xd408b0: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd408b0: movz            x17, #0xc834
    //     0xd408b4: add             lr, x0, x17
    //     0xd408b8: ldr             lr, [x21, lr, lsl #3]
    //     0xd408bc: blr             lr
    // 0xd408c0: mov             x2, x0
    // 0xd408c4: ldur            x1, [fp, #-0x20]
    // 0xd408c8: stur            x2, [fp, #-0x30]
    // 0xd408cc: r0 = LoadClassIdInstr(r1)
    //     0xd408cc: ldur            x0, [x1, #-1]
    //     0xd408d0: ubfx            x0, x0, #0xc, #0x14
    // 0xd408d4: str             x1, [SP]
    // 0xd408d8: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd408d8: movz            x17, #0xc834
    //     0xd408dc: add             lr, x0, x17
    //     0xd408e0: ldr             lr, [x21, lr, lsl #3]
    //     0xd408e4: blr             lr
    // 0xd408e8: mov             x1, x0
    // 0xd408ec: ldur            x0, [fp, #-0x30]
    // 0xd408f0: r2 = LoadInt32Instr(r0)
    //     0xd408f0: sbfx            x2, x0, #1, #0x1f
    //     0xd408f4: tbz             w0, #0, #0xd408fc
    //     0xd408f8: ldur            x2, [x0, #7]
    // 0xd408fc: r0 = LoadInt32Instr(r1)
    //     0xd408fc: sbfx            x0, x1, #1, #0x1f
    //     0xd40900: tbz             w1, #0, #0xd40908
    //     0xd40904: ldur            x0, [x1, #7]
    // 0xd40908: cmp             x2, x0
    // 0xd4090c: b.ne            #0xd411fc
    // 0xd40910: r3 = 0
    //     0xd40910: movz            x3, #0
    // 0xd40914: ldur            x2, [fp, #-0x28]
    // 0xd40918: ldur            x1, [fp, #-0x20]
    // 0xd4091c: stur            x3, [fp, #-0x38]
    // 0xd40920: CheckStackOverflow
    //     0xd40920: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd40924: cmp             SP, x16
    //     0xd40928: b.ls            #0xd4122c
    // 0xd4092c: r0 = LoadClassIdInstr(r2)
    //     0xd4092c: ldur            x0, [x2, #-1]
    //     0xd40930: ubfx            x0, x0, #0xc, #0x14
    // 0xd40934: str             x2, [SP]
    // 0xd40938: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd40938: movz            x17, #0xc834
    //     0xd4093c: add             lr, x0, x17
    //     0xd40940: ldr             lr, [x21, lr, lsl #3]
    //     0xd40944: blr             lr
    // 0xd40948: r1 = LoadInt32Instr(r0)
    //     0xd40948: sbfx            x1, x0, #1, #0x1f
    //     0xd4094c: tbz             w0, #0, #0xd40954
    //     0xd40950: ldur            x1, [x0, #7]
    // 0xd40954: ldur            x3, [fp, #-0x38]
    // 0xd40958: cmp             x3, x1
    // 0xd4095c: b.ge            #0xd411f4
    // 0xd40960: ldur            x5, [fp, #-0x28]
    // 0xd40964: ldur            x4, [fp, #-0x20]
    // 0xd40968: r0 = LoadClassIdInstr(r5)
    //     0xd40968: ldur            x0, [x5, #-1]
    //     0xd4096c: ubfx            x0, x0, #0xc, #0x14
    // 0xd40970: mov             x1, x5
    // 0xd40974: mov             x2, x3
    // 0xd40978: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xd40978: movz            x17, #0xd28f
    //     0xd4097c: add             lr, x0, x17
    //     0xd40980: ldr             lr, [x21, lr, lsl #3]
    //     0xd40984: blr             lr
    // 0xd40988: mov             x4, x0
    // 0xd4098c: ldur            x3, [fp, #-0x20]
    // 0xd40990: stur            x4, [fp, #-0x30]
    // 0xd40994: r0 = LoadClassIdInstr(r3)
    //     0xd40994: ldur            x0, [x3, #-1]
    //     0xd40998: ubfx            x0, x0, #0xc, #0x14
    // 0xd4099c: mov             x1, x3
    // 0xd409a0: ldur            x2, [fp, #-0x38]
    // 0xd409a4: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xd409a4: movz            x17, #0xd28f
    //     0xd409a8: add             lr, x0, x17
    //     0xd409ac: ldr             lr, [x21, lr, lsl #3]
    //     0xd409b0: blr             lr
    // 0xd409b4: mov             x1, x0
    // 0xd409b8: mov             x2, x0
    // 0xd409bc: ldur            x0, [fp, #-0x30]
    // 0xd409c0: stur            x2, [fp, #-0x40]
    // 0xd409c4: stp             x1, x0, [SP, #-0x10]!
    // 0xd409c8: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0xd409c8: ldr             lr, [PP, #0x1200]  ; [pp+0x1200] Stub: OptimizedIdenticalWithNumberCheck (0x5f32bc)
    // 0xd409cc: LoadField: r30 = r30->field_7
    //     0xd409cc: ldur            lr, [lr, #7]
    // 0xd409d0: blr             lr
    // 0xd409d4: ldp             x1, x0, [SP], #0x10
    // 0xd409d8: b.eq            #0xd40fc8
    // 0xd409dc: ldur            x3, [fp, #-0x30]
    // 0xd409e0: r0 = 60
    //     0xd409e0: movz            x0, #0x3c
    // 0xd409e4: branchIfSmi(r3, 0xd409f0)
    //     0xd409e4: tbz             w3, #0, #0xd409f0
    // 0xd409e8: r0 = LoadClassIdInstr(r3)
    //     0xd409e8: ldur            x0, [x3, #-1]
    //     0xd409ec: ubfx            x0, x0, #0xc, #0x14
    // 0xd409f0: sub             x16, x0, #0x3c
    // 0xd409f4: cmp             x16, #2
    // 0xd409f8: b.hi            #0xd40a44
    // 0xd409fc: ldur            x4, [fp, #-0x40]
    // 0xd40a00: r1 = 60
    //     0xd40a00: movz            x1, #0x3c
    // 0xd40a04: branchIfSmi(r4, 0xd40a10)
    //     0xd40a04: tbz             w4, #0, #0xd40a10
    // 0xd40a08: r1 = LoadClassIdInstr(r4)
    //     0xd40a08: ldur            x1, [x4, #-1]
    //     0xd40a0c: ubfx            x1, x1, #0xc, #0x14
    // 0xd40a10: sub             x16, x1, #0x3c
    // 0xd40a14: cmp             x16, #2
    // 0xd40a18: b.hi            #0xd40a48
    // 0xd40a1c: r0 = 60
    //     0xd40a1c: movz            x0, #0x3c
    // 0xd40a20: branchIfSmi(r3, 0xd40a2c)
    //     0xd40a20: tbz             w3, #0, #0xd40a2c
    // 0xd40a24: r0 = LoadClassIdInstr(r3)
    //     0xd40a24: ldur            x0, [x3, #-1]
    //     0xd40a28: ubfx            x0, x0, #0xc, #0x14
    // 0xd40a2c: stp             x4, x3, [SP]
    // 0xd40a30: mov             lr, x0
    // 0xd40a34: ldr             lr, [x21, lr, lsl #3]
    // 0xd40a38: blr             lr
    // 0xd40a3c: tbz             w0, #4, #0xd40fc8
    // 0xd40a40: b               #0xd411fc
    // 0xd40a44: ldur            x4, [fp, #-0x40]
    // 0xd40a48: r17 = -5561
    //     0xd40a48: movn            x17, #0x15b8
    // 0xd40a4c: add             x16, x0, x17
    // 0xd40a50: cmp             x16, #0x2a
    // 0xd40a54: b.hi            #0xd40aa0
    // 0xd40a58: r0 = 60
    //     0xd40a58: movz            x0, #0x3c
    // 0xd40a5c: branchIfSmi(r4, 0xd40a68)
    //     0xd40a5c: tbz             w4, #0, #0xd40a68
    // 0xd40a60: r0 = LoadClassIdInstr(r4)
    //     0xd40a60: ldur            x0, [x4, #-1]
    //     0xd40a64: ubfx            x0, x0, #0xc, #0x14
    // 0xd40a68: r17 = -5561
    //     0xd40a68: movn            x17, #0x15b8
    // 0xd40a6c: add             x16, x0, x17
    // 0xd40a70: cmp             x16, #0x2a
    // 0xd40a74: b.hi            #0xd40aa0
    // 0xd40a78: r0 = 60
    //     0xd40a78: movz            x0, #0x3c
    // 0xd40a7c: branchIfSmi(r3, 0xd40a88)
    //     0xd40a7c: tbz             w3, #0, #0xd40a88
    // 0xd40a80: r0 = LoadClassIdInstr(r3)
    //     0xd40a80: ldur            x0, [x3, #-1]
    //     0xd40a84: ubfx            x0, x0, #0xc, #0x14
    // 0xd40a88: stp             x4, x3, [SP]
    // 0xd40a8c: mov             lr, x0
    // 0xd40a90: ldr             lr, [x21, lr, lsl #3]
    // 0xd40a94: blr             lr
    // 0xd40a98: tbz             w0, #4, #0xd40fc8
    // 0xd40a9c: b               #0xd411fc
    // 0xd40aa0: mov             x0, x3
    // 0xd40aa4: r2 = Null
    //     0xd40aa4: mov             x2, NULL
    // 0xd40aa8: r1 = Null
    //     0xd40aa8: mov             x1, NULL
    // 0xd40aac: cmp             w0, NULL
    // 0xd40ab0: b.eq            #0xd40b48
    // 0xd40ab4: branchIfSmi(r0, 0xd40b48)
    //     0xd40ab4: tbz             w0, #0, #0xd40b48
    // 0xd40ab8: r3 = LoadClassIdInstr(r0)
    //     0xd40ab8: ldur            x3, [x0, #-1]
    //     0xd40abc: ubfx            x3, x3, #0xc, #0x14
    // 0xd40ac0: r17 = 6713
    //     0xd40ac0: movz            x17, #0x1a39
    // 0xd40ac4: cmp             x3, x17
    // 0xd40ac8: b.eq            #0xd40b50
    // 0xd40acc: r4 = LoadClassIdInstr(r0)
    //     0xd40acc: ldur            x4, [x0, #-1]
    //     0xd40ad0: ubfx            x4, x4, #0xc, #0x14
    // 0xd40ad4: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd40ad8: ldr             x3, [x3, #0x18]
    // 0xd40adc: ldr             x3, [x3, x4, lsl #3]
    // 0xd40ae0: LoadField: r3 = r3->field_2b
    //     0xd40ae0: ldur            w3, [x3, #0x2b]
    // 0xd40ae4: DecompressPointer r3
    //     0xd40ae4: add             x3, x3, HEAP, lsl #32
    // 0xd40ae8: cmp             w3, NULL
    // 0xd40aec: b.eq            #0xd40b48
    // 0xd40af0: LoadField: r3 = r3->field_f
    //     0xd40af0: ldur            w3, [x3, #0xf]
    // 0xd40af4: lsr             x3, x3, #3
    // 0xd40af8: r17 = 6713
    //     0xd40af8: movz            x17, #0x1a39
    // 0xd40afc: cmp             x3, x17
    // 0xd40b00: b.eq            #0xd40b50
    // 0xd40b04: r3 = SubtypeTestCache
    //     0xd40b04: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bef8] SubtypeTestCache
    //     0xd40b08: ldr             x3, [x3, #0xef8]
    // 0xd40b0c: r30 = Subtype1TestCacheStub
    //     0xd40b0c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd40b10: LoadField: r30 = r30->field_7
    //     0xd40b10: ldur            lr, [lr, #7]
    // 0xd40b14: blr             lr
    // 0xd40b18: cmp             w7, NULL
    // 0xd40b1c: b.eq            #0xd40b28
    // 0xd40b20: tbnz            w7, #4, #0xd40b48
    // 0xd40b24: b               #0xd40b50
    // 0xd40b28: r8 = Set
    //     0xd40b28: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bf00] Type: Set
    //     0xd40b2c: ldr             x8, [x8, #0xf00]
    // 0xd40b30: r3 = SubtypeTestCache
    //     0xd40b30: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf08] SubtypeTestCache
    //     0xd40b34: ldr             x3, [x3, #0xf08]
    // 0xd40b38: r30 = InstanceOfStub
    //     0xd40b38: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd40b3c: LoadField: r30 = r30->field_7
    //     0xd40b3c: ldur            lr, [lr, #7]
    // 0xd40b40: blr             lr
    // 0xd40b44: b               #0xd40b54
    // 0xd40b48: r0 = false
    //     0xd40b48: add             x0, NULL, #0x30  ; false
    // 0xd40b4c: b               #0xd40b54
    // 0xd40b50: r0 = true
    //     0xd40b50: add             x0, NULL, #0x20  ; true
    // 0xd40b54: tbnz            w0, #4, #0xd40c24
    // 0xd40b58: ldur            x0, [fp, #-0x40]
    // 0xd40b5c: r2 = Null
    //     0xd40b5c: mov             x2, NULL
    // 0xd40b60: r1 = Null
    //     0xd40b60: mov             x1, NULL
    // 0xd40b64: cmp             w0, NULL
    // 0xd40b68: b.eq            #0xd40c00
    // 0xd40b6c: branchIfSmi(r0, 0xd40c00)
    //     0xd40b6c: tbz             w0, #0, #0xd40c00
    // 0xd40b70: r3 = LoadClassIdInstr(r0)
    //     0xd40b70: ldur            x3, [x0, #-1]
    //     0xd40b74: ubfx            x3, x3, #0xc, #0x14
    // 0xd40b78: r17 = 6713
    //     0xd40b78: movz            x17, #0x1a39
    // 0xd40b7c: cmp             x3, x17
    // 0xd40b80: b.eq            #0xd40c08
    // 0xd40b84: r4 = LoadClassIdInstr(r0)
    //     0xd40b84: ldur            x4, [x0, #-1]
    //     0xd40b88: ubfx            x4, x4, #0xc, #0x14
    // 0xd40b8c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd40b90: ldr             x3, [x3, #0x18]
    // 0xd40b94: ldr             x3, [x3, x4, lsl #3]
    // 0xd40b98: LoadField: r3 = r3->field_2b
    //     0xd40b98: ldur            w3, [x3, #0x2b]
    // 0xd40b9c: DecompressPointer r3
    //     0xd40b9c: add             x3, x3, HEAP, lsl #32
    // 0xd40ba0: cmp             w3, NULL
    // 0xd40ba4: b.eq            #0xd40c00
    // 0xd40ba8: LoadField: r3 = r3->field_f
    //     0xd40ba8: ldur            w3, [x3, #0xf]
    // 0xd40bac: lsr             x3, x3, #3
    // 0xd40bb0: r17 = 6713
    //     0xd40bb0: movz            x17, #0x1a39
    // 0xd40bb4: cmp             x3, x17
    // 0xd40bb8: b.eq            #0xd40c08
    // 0xd40bbc: r3 = SubtypeTestCache
    //     0xd40bbc: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf10] SubtypeTestCache
    //     0xd40bc0: ldr             x3, [x3, #0xf10]
    // 0xd40bc4: r30 = Subtype1TestCacheStub
    //     0xd40bc4: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd40bc8: LoadField: r30 = r30->field_7
    //     0xd40bc8: ldur            lr, [lr, #7]
    // 0xd40bcc: blr             lr
    // 0xd40bd0: cmp             w7, NULL
    // 0xd40bd4: b.eq            #0xd40be0
    // 0xd40bd8: tbnz            w7, #4, #0xd40c00
    // 0xd40bdc: b               #0xd40c08
    // 0xd40be0: r8 = Set
    //     0xd40be0: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bf18] Type: Set
    //     0xd40be4: ldr             x8, [x8, #0xf18]
    // 0xd40be8: r3 = SubtypeTestCache
    //     0xd40be8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf20] SubtypeTestCache
    //     0xd40bec: ldr             x3, [x3, #0xf20]
    // 0xd40bf0: r30 = InstanceOfStub
    //     0xd40bf0: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd40bf4: LoadField: r30 = r30->field_7
    //     0xd40bf4: ldur            lr, [lr, #7]
    // 0xd40bf8: blr             lr
    // 0xd40bfc: b               #0xd40c0c
    // 0xd40c00: r0 = false
    //     0xd40c00: add             x0, NULL, #0x30  ; false
    // 0xd40c04: b               #0xd40c0c
    // 0xd40c08: r0 = true
    //     0xd40c08: add             x0, NULL, #0x20  ; true
    // 0xd40c0c: tbnz            w0, #4, #0xd40c24
    // 0xd40c10: ldur            x1, [fp, #-0x30]
    // 0xd40c14: ldur            x2, [fp, #-0x40]
    // 0xd40c18: r0 = setEquals()
    //     0xd40c18: bl              #0xd42018  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0xd40c1c: tbz             w0, #4, #0xd40fc8
    // 0xd40c20: b               #0xd411fc
    // 0xd40c24: ldur            x0, [fp, #-0x30]
    // 0xd40c28: r2 = Null
    //     0xd40c28: mov             x2, NULL
    // 0xd40c2c: r1 = Null
    //     0xd40c2c: mov             x1, NULL
    // 0xd40c30: cmp             w0, NULL
    // 0xd40c34: b.eq            #0xd40ccc
    // 0xd40c38: branchIfSmi(r0, 0xd40ccc)
    //     0xd40c38: tbz             w0, #0, #0xd40ccc
    // 0xd40c3c: r3 = LoadClassIdInstr(r0)
    //     0xd40c3c: ldur            x3, [x0, #-1]
    //     0xd40c40: ubfx            x3, x3, #0xc, #0x14
    // 0xd40c44: r17 = 7205
    //     0xd40c44: movz            x17, #0x1c25
    // 0xd40c48: cmp             x3, x17
    // 0xd40c4c: b.eq            #0xd40cd4
    // 0xd40c50: r4 = LoadClassIdInstr(r0)
    //     0xd40c50: ldur            x4, [x0, #-1]
    //     0xd40c54: ubfx            x4, x4, #0xc, #0x14
    // 0xd40c58: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd40c5c: ldr             x3, [x3, #0x18]
    // 0xd40c60: ldr             x3, [x3, x4, lsl #3]
    // 0xd40c64: LoadField: r3 = r3->field_2b
    //     0xd40c64: ldur            w3, [x3, #0x2b]
    // 0xd40c68: DecompressPointer r3
    //     0xd40c68: add             x3, x3, HEAP, lsl #32
    // 0xd40c6c: cmp             w3, NULL
    // 0xd40c70: b.eq            #0xd40ccc
    // 0xd40c74: LoadField: r3 = r3->field_f
    //     0xd40c74: ldur            w3, [x3, #0xf]
    // 0xd40c78: lsr             x3, x3, #3
    // 0xd40c7c: r17 = 7205
    //     0xd40c7c: movz            x17, #0x1c25
    // 0xd40c80: cmp             x3, x17
    // 0xd40c84: b.eq            #0xd40cd4
    // 0xd40c88: r3 = SubtypeTestCache
    //     0xd40c88: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf28] SubtypeTestCache
    //     0xd40c8c: ldr             x3, [x3, #0xf28]
    // 0xd40c90: r30 = Subtype1TestCacheStub
    //     0xd40c90: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd40c94: LoadField: r30 = r30->field_7
    //     0xd40c94: ldur            lr, [lr, #7]
    // 0xd40c98: blr             lr
    // 0xd40c9c: cmp             w7, NULL
    // 0xd40ca0: b.eq            #0xd40cac
    // 0xd40ca4: tbnz            w7, #4, #0xd40ccc
    // 0xd40ca8: b               #0xd40cd4
    // 0xd40cac: r8 = Iterable
    //     0xd40cac: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bf30] Type: Iterable
    //     0xd40cb0: ldr             x8, [x8, #0xf30]
    // 0xd40cb4: r3 = SubtypeTestCache
    //     0xd40cb4: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf38] SubtypeTestCache
    //     0xd40cb8: ldr             x3, [x3, #0xf38]
    // 0xd40cbc: r30 = InstanceOfStub
    //     0xd40cbc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd40cc0: LoadField: r30 = r30->field_7
    //     0xd40cc0: ldur            lr, [lr, #7]
    // 0xd40cc4: blr             lr
    // 0xd40cc8: b               #0xd40cd8
    // 0xd40ccc: r0 = false
    //     0xd40ccc: add             x0, NULL, #0x30  ; false
    // 0xd40cd0: b               #0xd40cd8
    // 0xd40cd4: r0 = true
    //     0xd40cd4: add             x0, NULL, #0x20  ; true
    // 0xd40cd8: tbnz            w0, #4, #0xd40da8
    // 0xd40cdc: ldur            x0, [fp, #-0x40]
    // 0xd40ce0: r2 = Null
    //     0xd40ce0: mov             x2, NULL
    // 0xd40ce4: r1 = Null
    //     0xd40ce4: mov             x1, NULL
    // 0xd40ce8: cmp             w0, NULL
    // 0xd40cec: b.eq            #0xd40d84
    // 0xd40cf0: branchIfSmi(r0, 0xd40d84)
    //     0xd40cf0: tbz             w0, #0, #0xd40d84
    // 0xd40cf4: r3 = LoadClassIdInstr(r0)
    //     0xd40cf4: ldur            x3, [x0, #-1]
    //     0xd40cf8: ubfx            x3, x3, #0xc, #0x14
    // 0xd40cfc: r17 = 7205
    //     0xd40cfc: movz            x17, #0x1c25
    // 0xd40d00: cmp             x3, x17
    // 0xd40d04: b.eq            #0xd40d8c
    // 0xd40d08: r4 = LoadClassIdInstr(r0)
    //     0xd40d08: ldur            x4, [x0, #-1]
    //     0xd40d0c: ubfx            x4, x4, #0xc, #0x14
    // 0xd40d10: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd40d14: ldr             x3, [x3, #0x18]
    // 0xd40d18: ldr             x3, [x3, x4, lsl #3]
    // 0xd40d1c: LoadField: r3 = r3->field_2b
    //     0xd40d1c: ldur            w3, [x3, #0x2b]
    // 0xd40d20: DecompressPointer r3
    //     0xd40d20: add             x3, x3, HEAP, lsl #32
    // 0xd40d24: cmp             w3, NULL
    // 0xd40d28: b.eq            #0xd40d84
    // 0xd40d2c: LoadField: r3 = r3->field_f
    //     0xd40d2c: ldur            w3, [x3, #0xf]
    // 0xd40d30: lsr             x3, x3, #3
    // 0xd40d34: r17 = 7205
    //     0xd40d34: movz            x17, #0x1c25
    // 0xd40d38: cmp             x3, x17
    // 0xd40d3c: b.eq            #0xd40d8c
    // 0xd40d40: r3 = SubtypeTestCache
    //     0xd40d40: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf40] SubtypeTestCache
    //     0xd40d44: ldr             x3, [x3, #0xf40]
    // 0xd40d48: r30 = Subtype1TestCacheStub
    //     0xd40d48: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd40d4c: LoadField: r30 = r30->field_7
    //     0xd40d4c: ldur            lr, [lr, #7]
    // 0xd40d50: blr             lr
    // 0xd40d54: cmp             w7, NULL
    // 0xd40d58: b.eq            #0xd40d64
    // 0xd40d5c: tbnz            w7, #4, #0xd40d84
    // 0xd40d60: b               #0xd40d8c
    // 0xd40d64: r8 = Iterable
    //     0xd40d64: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bf48] Type: Iterable
    //     0xd40d68: ldr             x8, [x8, #0xf48]
    // 0xd40d6c: r3 = SubtypeTestCache
    //     0xd40d6c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf50] SubtypeTestCache
    //     0xd40d70: ldr             x3, [x3, #0xf50]
    // 0xd40d74: r30 = InstanceOfStub
    //     0xd40d74: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd40d78: LoadField: r30 = r30->field_7
    //     0xd40d78: ldur            lr, [lr, #7]
    // 0xd40d7c: blr             lr
    // 0xd40d80: b               #0xd40d90
    // 0xd40d84: r0 = false
    //     0xd40d84: add             x0, NULL, #0x30  ; false
    // 0xd40d88: b               #0xd40d90
    // 0xd40d8c: r0 = true
    //     0xd40d8c: add             x0, NULL, #0x20  ; true
    // 0xd40d90: tbnz            w0, #4, #0xd40da8
    // 0xd40d94: ldur            x1, [fp, #-0x30]
    // 0xd40d98: ldur            x2, [fp, #-0x40]
    // 0xd40d9c: r0 = iterableEquals()
    //     0xd40d9c: bl              #0xd3fa14  ; [package:equatable/src/equatable_utils.dart] ::iterableEquals
    // 0xd40da0: tbz             w0, #4, #0xd40fc8
    // 0xd40da4: b               #0xd411fc
    // 0xd40da8: ldur            x0, [fp, #-0x30]
    // 0xd40dac: r2 = Null
    //     0xd40dac: mov             x2, NULL
    // 0xd40db0: r1 = Null
    //     0xd40db0: mov             x1, NULL
    // 0xd40db4: cmp             w0, NULL
    // 0xd40db8: b.eq            #0xd40e50
    // 0xd40dbc: branchIfSmi(r0, 0xd40e50)
    //     0xd40dbc: tbz             w0, #0, #0xd40e50
    // 0xd40dc0: r3 = LoadClassIdInstr(r0)
    //     0xd40dc0: ldur            x3, [x0, #-1]
    //     0xd40dc4: ubfx            x3, x3, #0xc, #0x14
    // 0xd40dc8: r17 = 6717
    //     0xd40dc8: movz            x17, #0x1a3d
    // 0xd40dcc: cmp             x3, x17
    // 0xd40dd0: b.eq            #0xd40e58
    // 0xd40dd4: r4 = LoadClassIdInstr(r0)
    //     0xd40dd4: ldur            x4, [x0, #-1]
    //     0xd40dd8: ubfx            x4, x4, #0xc, #0x14
    // 0xd40ddc: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd40de0: ldr             x3, [x3, #0x18]
    // 0xd40de4: ldr             x3, [x3, x4, lsl #3]
    // 0xd40de8: LoadField: r3 = r3->field_2b
    //     0xd40de8: ldur            w3, [x3, #0x2b]
    // 0xd40dec: DecompressPointer r3
    //     0xd40dec: add             x3, x3, HEAP, lsl #32
    // 0xd40df0: cmp             w3, NULL
    // 0xd40df4: b.eq            #0xd40e50
    // 0xd40df8: LoadField: r3 = r3->field_f
    //     0xd40df8: ldur            w3, [x3, #0xf]
    // 0xd40dfc: lsr             x3, x3, #3
    // 0xd40e00: r17 = 6717
    //     0xd40e00: movz            x17, #0x1a3d
    // 0xd40e04: cmp             x3, x17
    // 0xd40e08: b.eq            #0xd40e58
    // 0xd40e0c: r3 = SubtypeTestCache
    //     0xd40e0c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf58] SubtypeTestCache
    //     0xd40e10: ldr             x3, [x3, #0xf58]
    // 0xd40e14: r30 = Subtype1TestCacheStub
    //     0xd40e14: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd40e18: LoadField: r30 = r30->field_7
    //     0xd40e18: ldur            lr, [lr, #7]
    // 0xd40e1c: blr             lr
    // 0xd40e20: cmp             w7, NULL
    // 0xd40e24: b.eq            #0xd40e30
    // 0xd40e28: tbnz            w7, #4, #0xd40e50
    // 0xd40e2c: b               #0xd40e58
    // 0xd40e30: r8 = Map
    //     0xd40e30: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bf60] Type: Map
    //     0xd40e34: ldr             x8, [x8, #0xf60]
    // 0xd40e38: r3 = SubtypeTestCache
    //     0xd40e38: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf68] SubtypeTestCache
    //     0xd40e3c: ldr             x3, [x3, #0xf68]
    // 0xd40e40: r30 = InstanceOfStub
    //     0xd40e40: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd40e44: LoadField: r30 = r30->field_7
    //     0xd40e44: ldur            lr, [lr, #7]
    // 0xd40e48: blr             lr
    // 0xd40e4c: b               #0xd40e5c
    // 0xd40e50: r0 = false
    //     0xd40e50: add             x0, NULL, #0x30  ; false
    // 0xd40e54: b               #0xd40e5c
    // 0xd40e58: r0 = true
    //     0xd40e58: add             x0, NULL, #0x20  ; true
    // 0xd40e5c: tbnz            w0, #4, #0xd40f2c
    // 0xd40e60: ldur            x0, [fp, #-0x40]
    // 0xd40e64: r2 = Null
    //     0xd40e64: mov             x2, NULL
    // 0xd40e68: r1 = Null
    //     0xd40e68: mov             x1, NULL
    // 0xd40e6c: cmp             w0, NULL
    // 0xd40e70: b.eq            #0xd40f08
    // 0xd40e74: branchIfSmi(r0, 0xd40f08)
    //     0xd40e74: tbz             w0, #0, #0xd40f08
    // 0xd40e78: r3 = LoadClassIdInstr(r0)
    //     0xd40e78: ldur            x3, [x0, #-1]
    //     0xd40e7c: ubfx            x3, x3, #0xc, #0x14
    // 0xd40e80: r17 = 6717
    //     0xd40e80: movz            x17, #0x1a3d
    // 0xd40e84: cmp             x3, x17
    // 0xd40e88: b.eq            #0xd40f10
    // 0xd40e8c: r4 = LoadClassIdInstr(r0)
    //     0xd40e8c: ldur            x4, [x0, #-1]
    //     0xd40e90: ubfx            x4, x4, #0xc, #0x14
    // 0xd40e94: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd40e98: ldr             x3, [x3, #0x18]
    // 0xd40e9c: ldr             x3, [x3, x4, lsl #3]
    // 0xd40ea0: LoadField: r3 = r3->field_2b
    //     0xd40ea0: ldur            w3, [x3, #0x2b]
    // 0xd40ea4: DecompressPointer r3
    //     0xd40ea4: add             x3, x3, HEAP, lsl #32
    // 0xd40ea8: cmp             w3, NULL
    // 0xd40eac: b.eq            #0xd40f08
    // 0xd40eb0: LoadField: r3 = r3->field_f
    //     0xd40eb0: ldur            w3, [x3, #0xf]
    // 0xd40eb4: lsr             x3, x3, #3
    // 0xd40eb8: r17 = 6717
    //     0xd40eb8: movz            x17, #0x1a3d
    // 0xd40ebc: cmp             x3, x17
    // 0xd40ec0: b.eq            #0xd40f10
    // 0xd40ec4: r3 = SubtypeTestCache
    //     0xd40ec4: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf70] SubtypeTestCache
    //     0xd40ec8: ldr             x3, [x3, #0xf70]
    // 0xd40ecc: r30 = Subtype1TestCacheStub
    //     0xd40ecc: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd40ed0: LoadField: r30 = r30->field_7
    //     0xd40ed0: ldur            lr, [lr, #7]
    // 0xd40ed4: blr             lr
    // 0xd40ed8: cmp             w7, NULL
    // 0xd40edc: b.eq            #0xd40ee8
    // 0xd40ee0: tbnz            w7, #4, #0xd40f08
    // 0xd40ee4: b               #0xd40f10
    // 0xd40ee8: r8 = Map
    //     0xd40ee8: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bf78] Type: Map
    //     0xd40eec: ldr             x8, [x8, #0xf78]
    // 0xd40ef0: r3 = SubtypeTestCache
    //     0xd40ef0: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf80] SubtypeTestCache
    //     0xd40ef4: ldr             x3, [x3, #0xf80]
    // 0xd40ef8: r30 = InstanceOfStub
    //     0xd40ef8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd40efc: LoadField: r30 = r30->field_7
    //     0xd40efc: ldur            lr, [lr, #7]
    // 0xd40f00: blr             lr
    // 0xd40f04: b               #0xd40f14
    // 0xd40f08: r0 = false
    //     0xd40f08: add             x0, NULL, #0x30  ; false
    // 0xd40f0c: b               #0xd40f14
    // 0xd40f10: r0 = true
    //     0xd40f10: add             x0, NULL, #0x20  ; true
    // 0xd40f14: tbnz            w0, #4, #0xd40f2c
    // 0xd40f18: ldur            x1, [fp, #-0x30]
    // 0xd40f1c: ldur            x2, [fp, #-0x40]
    // 0xd40f20: r0 = mapEquals()
    //     0xd40f20: bl              #0xd402f8  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0xd40f24: tbz             w0, #4, #0xd40fc8
    // 0xd40f28: b               #0xd411fc
    // 0xd40f2c: ldur            x0, [fp, #-0x30]
    // 0xd40f30: cmp             w0, NULL
    // 0xd40f34: b.ne            #0xd40f40
    // 0xd40f38: r1 = Null
    //     0xd40f38: mov             x1, NULL
    // 0xd40f3c: b               #0xd40f4c
    // 0xd40f40: str             x0, [SP]
    // 0xd40f44: r0 = runtimeType()
    //     0xd40f44: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd40f48: mov             x1, x0
    // 0xd40f4c: ldur            x0, [fp, #-0x40]
    // 0xd40f50: stur            x1, [fp, #-0x48]
    // 0xd40f54: cmp             w0, NULL
    // 0xd40f58: b.ne            #0xd40f68
    // 0xd40f5c: mov             x0, x1
    // 0xd40f60: r1 = Null
    //     0xd40f60: mov             x1, NULL
    // 0xd40f64: b               #0xd40f78
    // 0xd40f68: str             x0, [SP]
    // 0xd40f6c: r0 = runtimeType()
    //     0xd40f6c: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd40f70: mov             x1, x0
    // 0xd40f74: ldur            x0, [fp, #-0x48]
    // 0xd40f78: r2 = LoadClassIdInstr(r0)
    //     0xd40f78: ldur            x2, [x0, #-1]
    //     0xd40f7c: ubfx            x2, x2, #0xc, #0x14
    // 0xd40f80: stp             x1, x0, [SP]
    // 0xd40f84: mov             x0, x2
    // 0xd40f88: mov             lr, x0
    // 0xd40f8c: ldr             lr, [x21, lr, lsl #3]
    // 0xd40f90: blr             lr
    // 0xd40f94: tbnz            w0, #4, #0xd411fc
    // 0xd40f98: ldur            x0, [fp, #-0x30]
    // 0xd40f9c: r1 = 60
    //     0xd40f9c: movz            x1, #0x3c
    // 0xd40fa0: branchIfSmi(r0, 0xd40fac)
    //     0xd40fa0: tbz             w0, #0, #0xd40fac
    // 0xd40fa4: r1 = LoadClassIdInstr(r0)
    //     0xd40fa4: ldur            x1, [x0, #-1]
    //     0xd40fa8: ubfx            x1, x1, #0xc, #0x14
    // 0xd40fac: ldur            x16, [fp, #-0x40]
    // 0xd40fb0: stp             x16, x0, [SP]
    // 0xd40fb4: mov             x0, x1
    // 0xd40fb8: mov             lr, x0
    // 0xd40fbc: ldr             lr, [x21, lr, lsl #3]
    // 0xd40fc0: blr             lr
    // 0xd40fc4: tbnz            w0, #4, #0xd411fc
    // 0xd40fc8: ldur            x0, [fp, #-0x38]
    // 0xd40fcc: add             x3, x0, #1
    // 0xd40fd0: b               #0xd40914
    // 0xd40fd4: ldur            x0, [fp, #-0x28]
    // 0xd40fd8: r2 = Null
    //     0xd40fd8: mov             x2, NULL
    // 0xd40fdc: r1 = Null
    //     0xd40fdc: mov             x1, NULL
    // 0xd40fe0: cmp             w0, NULL
    // 0xd40fe4: b.eq            #0xd4107c
    // 0xd40fe8: branchIfSmi(r0, 0xd4107c)
    //     0xd40fe8: tbz             w0, #0, #0xd4107c
    // 0xd40fec: r3 = LoadClassIdInstr(r0)
    //     0xd40fec: ldur            x3, [x0, #-1]
    //     0xd40ff0: ubfx            x3, x3, #0xc, #0x14
    // 0xd40ff4: r17 = 6717
    //     0xd40ff4: movz            x17, #0x1a3d
    // 0xd40ff8: cmp             x3, x17
    // 0xd40ffc: b.eq            #0xd41084
    // 0xd41000: r4 = LoadClassIdInstr(r0)
    //     0xd41000: ldur            x4, [x0, #-1]
    //     0xd41004: ubfx            x4, x4, #0xc, #0x14
    // 0xd41008: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd4100c: ldr             x3, [x3, #0x18]
    // 0xd41010: ldr             x3, [x3, x4, lsl #3]
    // 0xd41014: LoadField: r3 = r3->field_2b
    //     0xd41014: ldur            w3, [x3, #0x2b]
    // 0xd41018: DecompressPointer r3
    //     0xd41018: add             x3, x3, HEAP, lsl #32
    // 0xd4101c: cmp             w3, NULL
    // 0xd41020: b.eq            #0xd4107c
    // 0xd41024: LoadField: r3 = r3->field_f
    //     0xd41024: ldur            w3, [x3, #0xf]
    // 0xd41028: lsr             x3, x3, #3
    // 0xd4102c: r17 = 6717
    //     0xd4102c: movz            x17, #0x1a3d
    // 0xd41030: cmp             x3, x17
    // 0xd41034: b.eq            #0xd41084
    // 0xd41038: r3 = SubtypeTestCache
    //     0xd41038: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf88] SubtypeTestCache
    //     0xd4103c: ldr             x3, [x3, #0xf88]
    // 0xd41040: r30 = Subtype1TestCacheStub
    //     0xd41040: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd41044: LoadField: r30 = r30->field_7
    //     0xd41044: ldur            lr, [lr, #7]
    // 0xd41048: blr             lr
    // 0xd4104c: cmp             w7, NULL
    // 0xd41050: b.eq            #0xd4105c
    // 0xd41054: tbnz            w7, #4, #0xd4107c
    // 0xd41058: b               #0xd41084
    // 0xd4105c: r8 = Map
    //     0xd4105c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bf90] Type: Map
    //     0xd41060: ldr             x8, [x8, #0xf90]
    // 0xd41064: r3 = SubtypeTestCache
    //     0xd41064: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bf98] SubtypeTestCache
    //     0xd41068: ldr             x3, [x3, #0xf98]
    // 0xd4106c: r30 = InstanceOfStub
    //     0xd4106c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd41070: LoadField: r30 = r30->field_7
    //     0xd41070: ldur            lr, [lr, #7]
    // 0xd41074: blr             lr
    // 0xd41078: b               #0xd41088
    // 0xd4107c: r0 = false
    //     0xd4107c: add             x0, NULL, #0x30  ; false
    // 0xd41080: b               #0xd41088
    // 0xd41084: r0 = true
    //     0xd41084: add             x0, NULL, #0x20  ; true
    // 0xd41088: tbnz            w0, #4, #0xd41158
    // 0xd4108c: ldur            x0, [fp, #-0x20]
    // 0xd41090: r2 = Null
    //     0xd41090: mov             x2, NULL
    // 0xd41094: r1 = Null
    //     0xd41094: mov             x1, NULL
    // 0xd41098: cmp             w0, NULL
    // 0xd4109c: b.eq            #0xd41134
    // 0xd410a0: branchIfSmi(r0, 0xd41134)
    //     0xd410a0: tbz             w0, #0, #0xd41134
    // 0xd410a4: r3 = LoadClassIdInstr(r0)
    //     0xd410a4: ldur            x3, [x0, #-1]
    //     0xd410a8: ubfx            x3, x3, #0xc, #0x14
    // 0xd410ac: r17 = 6717
    //     0xd410ac: movz            x17, #0x1a3d
    // 0xd410b0: cmp             x3, x17
    // 0xd410b4: b.eq            #0xd4113c
    // 0xd410b8: r4 = LoadClassIdInstr(r0)
    //     0xd410b8: ldur            x4, [x0, #-1]
    //     0xd410bc: ubfx            x4, x4, #0xc, #0x14
    // 0xd410c0: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd410c4: ldr             x3, [x3, #0x18]
    // 0xd410c8: ldr             x3, [x3, x4, lsl #3]
    // 0xd410cc: LoadField: r3 = r3->field_2b
    //     0xd410cc: ldur            w3, [x3, #0x2b]
    // 0xd410d0: DecompressPointer r3
    //     0xd410d0: add             x3, x3, HEAP, lsl #32
    // 0xd410d4: cmp             w3, NULL
    // 0xd410d8: b.eq            #0xd41134
    // 0xd410dc: LoadField: r3 = r3->field_f
    //     0xd410dc: ldur            w3, [x3, #0xf]
    // 0xd410e0: lsr             x3, x3, #3
    // 0xd410e4: r17 = 6717
    //     0xd410e4: movz            x17, #0x1a3d
    // 0xd410e8: cmp             x3, x17
    // 0xd410ec: b.eq            #0xd4113c
    // 0xd410f0: r3 = SubtypeTestCache
    //     0xd410f0: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bfa0] SubtypeTestCache
    //     0xd410f4: ldr             x3, [x3, #0xfa0]
    // 0xd410f8: r30 = Subtype1TestCacheStub
    //     0xd410f8: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd410fc: LoadField: r30 = r30->field_7
    //     0xd410fc: ldur            lr, [lr, #7]
    // 0xd41100: blr             lr
    // 0xd41104: cmp             w7, NULL
    // 0xd41108: b.eq            #0xd41114
    // 0xd4110c: tbnz            w7, #4, #0xd41134
    // 0xd41110: b               #0xd4113c
    // 0xd41114: r8 = Map
    //     0xd41114: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1bfa8] Type: Map
    //     0xd41118: ldr             x8, [x8, #0xfa8]
    // 0xd4111c: r3 = SubtypeTestCache
    //     0xd4111c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1bfb0] SubtypeTestCache
    //     0xd41120: ldr             x3, [x3, #0xfb0]
    // 0xd41124: r30 = InstanceOfStub
    //     0xd41124: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd41128: LoadField: r30 = r30->field_7
    //     0xd41128: ldur            lr, [lr, #7]
    // 0xd4112c: blr             lr
    // 0xd41130: b               #0xd41140
    // 0xd41134: r0 = false
    //     0xd41134: add             x0, NULL, #0x30  ; false
    // 0xd41138: b               #0xd41140
    // 0xd4113c: r0 = true
    //     0xd4113c: add             x0, NULL, #0x20  ; true
    // 0xd41140: tbnz            w0, #4, #0xd41158
    // 0xd41144: ldur            x1, [fp, #-0x28]
    // 0xd41148: ldur            x2, [fp, #-0x20]
    // 0xd4114c: r0 = mapEquals()
    //     0xd4114c: bl              #0xd402f8  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0xd41150: tbz             w0, #4, #0xd411f4
    // 0xd41154: b               #0xd411fc
    // 0xd41158: ldur            x0, [fp, #-0x28]
    // 0xd4115c: cmp             w0, NULL
    // 0xd41160: b.ne            #0xd4116c
    // 0xd41164: r1 = Null
    //     0xd41164: mov             x1, NULL
    // 0xd41168: b               #0xd41178
    // 0xd4116c: str             x0, [SP]
    // 0xd41170: r0 = runtimeType()
    //     0xd41170: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd41174: mov             x1, x0
    // 0xd41178: ldur            x0, [fp, #-0x20]
    // 0xd4117c: stur            x1, [fp, #-0x30]
    // 0xd41180: cmp             w0, NULL
    // 0xd41184: b.ne            #0xd41194
    // 0xd41188: mov             x0, x1
    // 0xd4118c: r1 = Null
    //     0xd4118c: mov             x1, NULL
    // 0xd41190: b               #0xd411a4
    // 0xd41194: str             x0, [SP]
    // 0xd41198: r0 = runtimeType()
    //     0xd41198: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd4119c: mov             x1, x0
    // 0xd411a0: ldur            x0, [fp, #-0x30]
    // 0xd411a4: r2 = LoadClassIdInstr(r0)
    //     0xd411a4: ldur            x2, [x0, #-1]
    //     0xd411a8: ubfx            x2, x2, #0xc, #0x14
    // 0xd411ac: stp             x1, x0, [SP]
    // 0xd411b0: mov             x0, x2
    // 0xd411b4: mov             lr, x0
    // 0xd411b8: ldr             lr, [x21, lr, lsl #3]
    // 0xd411bc: blr             lr
    // 0xd411c0: tbnz            w0, #4, #0xd411fc
    // 0xd411c4: ldur            x0, [fp, #-0x28]
    // 0xd411c8: r1 = 60
    //     0xd411c8: movz            x1, #0x3c
    // 0xd411cc: branchIfSmi(r0, 0xd411d8)
    //     0xd411cc: tbz             w0, #0, #0xd411d8
    // 0xd411d0: r1 = LoadClassIdInstr(r0)
    //     0xd411d0: ldur            x1, [x0, #-1]
    //     0xd411d4: ubfx            x1, x1, #0xc, #0x14
    // 0xd411d8: ldur            x16, [fp, #-0x20]
    // 0xd411dc: stp             x16, x0, [SP]
    // 0xd411e0: mov             x0, x1
    // 0xd411e4: mov             lr, x0
    // 0xd411e8: ldr             lr, [x21, lr, lsl #3]
    // 0xd411ec: blr             lr
    // 0xd411f0: tbnz            w0, #4, #0xd411fc
    // 0xd411f4: ldur            x2, [fp, #-0x18]
    // 0xd411f8: b               #0xd40400
    // 0xd411fc: r0 = false
    //     0xd411fc: add             x0, NULL, #0x30  ; false
    // 0xd41200: LeaveFrame
    //     0xd41200: mov             SP, fp
    //     0xd41204: ldp             fp, lr, [SP], #0x10
    // 0xd41208: ret
    //     0xd41208: ret             
    // 0xd4120c: r0 = true
    //     0xd4120c: add             x0, NULL, #0x20  ; true
    // 0xd41210: LeaveFrame
    //     0xd41210: mov             SP, fp
    //     0xd41214: ldp             fp, lr, [SP], #0x10
    // 0xd41218: ret
    //     0xd41218: ret             
    // 0xd4121c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd4121c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd41220: b               #0xd40324
    // 0xd41224: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd41224: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd41228: b               #0xd40414
    // 0xd4122c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd4122c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd41230: b               #0xd4092c
  }
  static bool objectsEquals(Object?, Object?) {
    // ** addr: 0xd41234, size: 0xde4
    // 0xd41234: EnterFrame
    //     0xd41234: stp             fp, lr, [SP, #-0x10]!
    //     0xd41238: mov             fp, SP
    // 0xd4123c: AllocStack(0x40)
    //     0xd4123c: sub             SP, SP, #0x40
    // 0xd41240: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xd41240: mov             x3, x1
    //     0xd41244: stur            x1, [fp, #-8]
    //     0xd41248: stur            x2, [fp, #-0x10]
    // 0xd4124c: CheckStackOverflow
    //     0xd4124c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd41250: cmp             SP, x16
    //     0xd41254: b.ls            #0xd42008
    // 0xd41258: mov             x0, x3
    // 0xd4125c: mov             x1, x2
    // 0xd41260: stp             x1, x0, [SP, #-0x10]!
    // 0xd41264: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0xd41264: ldr             lr, [PP, #0x1200]  ; [pp+0x1200] Stub: OptimizedIdenticalWithNumberCheck (0x5f32bc)
    // 0xd41268: LoadField: r30 = r30->field_7
    //     0xd41268: ldur            lr, [lr, #7]
    // 0xd4126c: blr             lr
    // 0xd41270: ldp             x1, x0, [SP], #0x10
    // 0xd41274: b.ne            #0xd41288
    // 0xd41278: r0 = true
    //     0xd41278: add             x0, NULL, #0x20  ; true
    // 0xd4127c: LeaveFrame
    //     0xd4127c: mov             SP, fp
    //     0xd41280: ldp             fp, lr, [SP], #0x10
    // 0xd41284: ret
    //     0xd41284: ret             
    // 0xd41288: ldur            x3, [fp, #-8]
    // 0xd4128c: r0 = 60
    //     0xd4128c: movz            x0, #0x3c
    // 0xd41290: branchIfSmi(r3, 0xd4129c)
    //     0xd41290: tbz             w3, #0, #0xd4129c
    // 0xd41294: r0 = LoadClassIdInstr(r3)
    //     0xd41294: ldur            x0, [x3, #-1]
    //     0xd41298: ubfx            x0, x0, #0xc, #0x14
    // 0xd4129c: sub             x16, x0, #0x3c
    // 0xd412a0: cmp             x16, #2
    // 0xd412a4: b.hi            #0xd412f4
    // 0xd412a8: ldur            x4, [fp, #-0x10]
    // 0xd412ac: r1 = 60
    //     0xd412ac: movz            x1, #0x3c
    // 0xd412b0: branchIfSmi(r4, 0xd412bc)
    //     0xd412b0: tbz             w4, #0, #0xd412bc
    // 0xd412b4: r1 = LoadClassIdInstr(r4)
    //     0xd412b4: ldur            x1, [x4, #-1]
    //     0xd412b8: ubfx            x1, x1, #0xc, #0x14
    // 0xd412bc: sub             x16, x1, #0x3c
    // 0xd412c0: cmp             x16, #2
    // 0xd412c4: b.hi            #0xd412f8
    // 0xd412c8: r0 = 60
    //     0xd412c8: movz            x0, #0x3c
    // 0xd412cc: branchIfSmi(r3, 0xd412d8)
    //     0xd412cc: tbz             w3, #0, #0xd412d8
    // 0xd412d0: r0 = LoadClassIdInstr(r3)
    //     0xd412d0: ldur            x0, [x3, #-1]
    //     0xd412d4: ubfx            x0, x0, #0xc, #0x14
    // 0xd412d8: stp             x4, x3, [SP]
    // 0xd412dc: mov             lr, x0
    // 0xd412e0: ldr             lr, [x21, lr, lsl #3]
    // 0xd412e4: blr             lr
    // 0xd412e8: LeaveFrame
    //     0xd412e8: mov             SP, fp
    //     0xd412ec: ldp             fp, lr, [SP], #0x10
    // 0xd412f0: ret
    //     0xd412f0: ret             
    // 0xd412f4: ldur            x4, [fp, #-0x10]
    // 0xd412f8: r17 = -5561
    //     0xd412f8: movn            x17, #0x15b8
    // 0xd412fc: add             x16, x0, x17
    // 0xd41300: cmp             x16, #0x2a
    // 0xd41304: b.hi            #0xd41354
    // 0xd41308: r0 = 60
    //     0xd41308: movz            x0, #0x3c
    // 0xd4130c: branchIfSmi(r4, 0xd41318)
    //     0xd4130c: tbz             w4, #0, #0xd41318
    // 0xd41310: r0 = LoadClassIdInstr(r4)
    //     0xd41310: ldur            x0, [x4, #-1]
    //     0xd41314: ubfx            x0, x0, #0xc, #0x14
    // 0xd41318: r17 = -5561
    //     0xd41318: movn            x17, #0x15b8
    // 0xd4131c: add             x16, x0, x17
    // 0xd41320: cmp             x16, #0x2a
    // 0xd41324: b.hi            #0xd41354
    // 0xd41328: r0 = 60
    //     0xd41328: movz            x0, #0x3c
    // 0xd4132c: branchIfSmi(r3, 0xd41338)
    //     0xd4132c: tbz             w3, #0, #0xd41338
    // 0xd41330: r0 = LoadClassIdInstr(r3)
    //     0xd41330: ldur            x0, [x3, #-1]
    //     0xd41334: ubfx            x0, x0, #0xc, #0x14
    // 0xd41338: stp             x4, x3, [SP]
    // 0xd4133c: mov             lr, x0
    // 0xd41340: ldr             lr, [x21, lr, lsl #3]
    // 0xd41344: blr             lr
    // 0xd41348: LeaveFrame
    //     0xd41348: mov             SP, fp
    //     0xd4134c: ldp             fp, lr, [SP], #0x10
    // 0xd41350: ret
    //     0xd41350: ret             
    // 0xd41354: mov             x0, x3
    // 0xd41358: r2 = Null
    //     0xd41358: mov             x2, NULL
    // 0xd4135c: r1 = Null
    //     0xd4135c: mov             x1, NULL
    // 0xd41360: cmp             w0, NULL
    // 0xd41364: b.eq            #0xd413fc
    // 0xd41368: branchIfSmi(r0, 0xd413fc)
    //     0xd41368: tbz             w0, #0, #0xd413fc
    // 0xd4136c: r3 = LoadClassIdInstr(r0)
    //     0xd4136c: ldur            x3, [x0, #-1]
    //     0xd41370: ubfx            x3, x3, #0xc, #0x14
    // 0xd41374: r17 = 6713
    //     0xd41374: movz            x17, #0x1a39
    // 0xd41378: cmp             x3, x17
    // 0xd4137c: b.eq            #0xd41404
    // 0xd41380: r4 = LoadClassIdInstr(r0)
    //     0xd41380: ldur            x4, [x0, #-1]
    //     0xd41384: ubfx            x4, x4, #0xc, #0x14
    // 0xd41388: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd4138c: ldr             x3, [x3, #0x18]
    // 0xd41390: ldr             x3, [x3, x4, lsl #3]
    // 0xd41394: LoadField: r3 = r3->field_2b
    //     0xd41394: ldur            w3, [x3, #0x2b]
    // 0xd41398: DecompressPointer r3
    //     0xd41398: add             x3, x3, HEAP, lsl #32
    // 0xd4139c: cmp             w3, NULL
    // 0xd413a0: b.eq            #0xd413fc
    // 0xd413a4: LoadField: r3 = r3->field_f
    //     0xd413a4: ldur            w3, [x3, #0xf]
    // 0xd413a8: lsr             x3, x3, #3
    // 0xd413ac: r17 = 6713
    //     0xd413ac: movz            x17, #0x1a39
    // 0xd413b0: cmp             x3, x17
    // 0xd413b4: b.eq            #0xd41404
    // 0xd413b8: r3 = SubtypeTestCache
    //     0xd413b8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c048] SubtypeTestCache
    //     0xd413bc: ldr             x3, [x3, #0x48]
    // 0xd413c0: r30 = Subtype1TestCacheStub
    //     0xd413c0: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd413c4: LoadField: r30 = r30->field_7
    //     0xd413c4: ldur            lr, [lr, #7]
    // 0xd413c8: blr             lr
    // 0xd413cc: cmp             w7, NULL
    // 0xd413d0: b.eq            #0xd413dc
    // 0xd413d4: tbnz            w7, #4, #0xd413fc
    // 0xd413d8: b               #0xd41404
    // 0xd413dc: r8 = Set
    //     0xd413dc: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c050] Type: Set
    //     0xd413e0: ldr             x8, [x8, #0x50]
    // 0xd413e4: r3 = SubtypeTestCache
    //     0xd413e4: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c058] SubtypeTestCache
    //     0xd413e8: ldr             x3, [x3, #0x58]
    // 0xd413ec: r30 = InstanceOfStub
    //     0xd413ec: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd413f0: LoadField: r30 = r30->field_7
    //     0xd413f0: ldur            lr, [lr, #7]
    // 0xd413f4: blr             lr
    // 0xd413f8: b               #0xd41408
    // 0xd413fc: r0 = false
    //     0xd413fc: add             x0, NULL, #0x30  ; false
    // 0xd41400: b               #0xd41408
    // 0xd41404: r0 = true
    //     0xd41404: add             x0, NULL, #0x20  ; true
    // 0xd41408: tbnz            w0, #4, #0xd414dc
    // 0xd4140c: ldur            x0, [fp, #-0x10]
    // 0xd41410: r2 = Null
    //     0xd41410: mov             x2, NULL
    // 0xd41414: r1 = Null
    //     0xd41414: mov             x1, NULL
    // 0xd41418: cmp             w0, NULL
    // 0xd4141c: b.eq            #0xd414b4
    // 0xd41420: branchIfSmi(r0, 0xd414b4)
    //     0xd41420: tbz             w0, #0, #0xd414b4
    // 0xd41424: r3 = LoadClassIdInstr(r0)
    //     0xd41424: ldur            x3, [x0, #-1]
    //     0xd41428: ubfx            x3, x3, #0xc, #0x14
    // 0xd4142c: r17 = 6713
    //     0xd4142c: movz            x17, #0x1a39
    // 0xd41430: cmp             x3, x17
    // 0xd41434: b.eq            #0xd414bc
    // 0xd41438: r4 = LoadClassIdInstr(r0)
    //     0xd41438: ldur            x4, [x0, #-1]
    //     0xd4143c: ubfx            x4, x4, #0xc, #0x14
    // 0xd41440: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd41444: ldr             x3, [x3, #0x18]
    // 0xd41448: ldr             x3, [x3, x4, lsl #3]
    // 0xd4144c: LoadField: r3 = r3->field_2b
    //     0xd4144c: ldur            w3, [x3, #0x2b]
    // 0xd41450: DecompressPointer r3
    //     0xd41450: add             x3, x3, HEAP, lsl #32
    // 0xd41454: cmp             w3, NULL
    // 0xd41458: b.eq            #0xd414b4
    // 0xd4145c: LoadField: r3 = r3->field_f
    //     0xd4145c: ldur            w3, [x3, #0xf]
    // 0xd41460: lsr             x3, x3, #3
    // 0xd41464: r17 = 6713
    //     0xd41464: movz            x17, #0x1a39
    // 0xd41468: cmp             x3, x17
    // 0xd4146c: b.eq            #0xd414bc
    // 0xd41470: r3 = SubtypeTestCache
    //     0xd41470: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c060] SubtypeTestCache
    //     0xd41474: ldr             x3, [x3, #0x60]
    // 0xd41478: r30 = Subtype1TestCacheStub
    //     0xd41478: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd4147c: LoadField: r30 = r30->field_7
    //     0xd4147c: ldur            lr, [lr, #7]
    // 0xd41480: blr             lr
    // 0xd41484: cmp             w7, NULL
    // 0xd41488: b.eq            #0xd41494
    // 0xd4148c: tbnz            w7, #4, #0xd414b4
    // 0xd41490: b               #0xd414bc
    // 0xd41494: r8 = Set
    //     0xd41494: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c068] Type: Set
    //     0xd41498: ldr             x8, [x8, #0x68]
    // 0xd4149c: r3 = SubtypeTestCache
    //     0xd4149c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c070] SubtypeTestCache
    //     0xd414a0: ldr             x3, [x3, #0x70]
    // 0xd414a4: r30 = InstanceOfStub
    //     0xd414a4: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd414a8: LoadField: r30 = r30->field_7
    //     0xd414a8: ldur            lr, [lr, #7]
    // 0xd414ac: blr             lr
    // 0xd414b0: b               #0xd414c0
    // 0xd414b4: r0 = false
    //     0xd414b4: add             x0, NULL, #0x30  ; false
    // 0xd414b8: b               #0xd414c0
    // 0xd414bc: r0 = true
    //     0xd414bc: add             x0, NULL, #0x20  ; true
    // 0xd414c0: tbnz            w0, #4, #0xd414dc
    // 0xd414c4: ldur            x1, [fp, #-8]
    // 0xd414c8: ldur            x2, [fp, #-0x10]
    // 0xd414cc: r0 = setEquals()
    //     0xd414cc: bl              #0xd42018  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0xd414d0: LeaveFrame
    //     0xd414d0: mov             SP, fp
    //     0xd414d4: ldp             fp, lr, [SP], #0x10
    // 0xd414d8: ret
    //     0xd414d8: ret             
    // 0xd414dc: ldur            x0, [fp, #-8]
    // 0xd414e0: r2 = Null
    //     0xd414e0: mov             x2, NULL
    // 0xd414e4: r1 = Null
    //     0xd414e4: mov             x1, NULL
    // 0xd414e8: cmp             w0, NULL
    // 0xd414ec: b.eq            #0xd41584
    // 0xd414f0: branchIfSmi(r0, 0xd41584)
    //     0xd414f0: tbz             w0, #0, #0xd41584
    // 0xd414f4: r3 = LoadClassIdInstr(r0)
    //     0xd414f4: ldur            x3, [x0, #-1]
    //     0xd414f8: ubfx            x3, x3, #0xc, #0x14
    // 0xd414fc: r17 = 7205
    //     0xd414fc: movz            x17, #0x1c25
    // 0xd41500: cmp             x3, x17
    // 0xd41504: b.eq            #0xd4158c
    // 0xd41508: r4 = LoadClassIdInstr(r0)
    //     0xd41508: ldur            x4, [x0, #-1]
    //     0xd4150c: ubfx            x4, x4, #0xc, #0x14
    // 0xd41510: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd41514: ldr             x3, [x3, #0x18]
    // 0xd41518: ldr             x3, [x3, x4, lsl #3]
    // 0xd4151c: LoadField: r3 = r3->field_2b
    //     0xd4151c: ldur            w3, [x3, #0x2b]
    // 0xd41520: DecompressPointer r3
    //     0xd41520: add             x3, x3, HEAP, lsl #32
    // 0xd41524: cmp             w3, NULL
    // 0xd41528: b.eq            #0xd41584
    // 0xd4152c: LoadField: r3 = r3->field_f
    //     0xd4152c: ldur            w3, [x3, #0xf]
    // 0xd41530: lsr             x3, x3, #3
    // 0xd41534: r17 = 7205
    //     0xd41534: movz            x17, #0x1c25
    // 0xd41538: cmp             x3, x17
    // 0xd4153c: b.eq            #0xd4158c
    // 0xd41540: r3 = SubtypeTestCache
    //     0xd41540: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c078] SubtypeTestCache
    //     0xd41544: ldr             x3, [x3, #0x78]
    // 0xd41548: r30 = Subtype1TestCacheStub
    //     0xd41548: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd4154c: LoadField: r30 = r30->field_7
    //     0xd4154c: ldur            lr, [lr, #7]
    // 0xd41550: blr             lr
    // 0xd41554: cmp             w7, NULL
    // 0xd41558: b.eq            #0xd41564
    // 0xd4155c: tbnz            w7, #4, #0xd41584
    // 0xd41560: b               #0xd4158c
    // 0xd41564: r8 = Iterable
    //     0xd41564: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c080] Type: Iterable
    //     0xd41568: ldr             x8, [x8, #0x80]
    // 0xd4156c: r3 = SubtypeTestCache
    //     0xd4156c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c088] SubtypeTestCache
    //     0xd41570: ldr             x3, [x3, #0x88]
    // 0xd41574: r30 = InstanceOfStub
    //     0xd41574: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd41578: LoadField: r30 = r30->field_7
    //     0xd41578: ldur            lr, [lr, #7]
    // 0xd4157c: blr             lr
    // 0xd41580: b               #0xd41590
    // 0xd41584: r0 = false
    //     0xd41584: add             x0, NULL, #0x30  ; false
    // 0xd41588: b               #0xd41590
    // 0xd4158c: r0 = true
    //     0xd4158c: add             x0, NULL, #0x20  ; true
    // 0xd41590: tbnz            w0, #4, #0xd41db4
    // 0xd41594: ldur            x0, [fp, #-0x10]
    // 0xd41598: r2 = Null
    //     0xd41598: mov             x2, NULL
    // 0xd4159c: r1 = Null
    //     0xd4159c: mov             x1, NULL
    // 0xd415a0: cmp             w0, NULL
    // 0xd415a4: b.eq            #0xd4163c
    // 0xd415a8: branchIfSmi(r0, 0xd4163c)
    //     0xd415a8: tbz             w0, #0, #0xd4163c
    // 0xd415ac: r3 = LoadClassIdInstr(r0)
    //     0xd415ac: ldur            x3, [x0, #-1]
    //     0xd415b0: ubfx            x3, x3, #0xc, #0x14
    // 0xd415b4: r17 = 7205
    //     0xd415b4: movz            x17, #0x1c25
    // 0xd415b8: cmp             x3, x17
    // 0xd415bc: b.eq            #0xd41644
    // 0xd415c0: r4 = LoadClassIdInstr(r0)
    //     0xd415c0: ldur            x4, [x0, #-1]
    //     0xd415c4: ubfx            x4, x4, #0xc, #0x14
    // 0xd415c8: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd415cc: ldr             x3, [x3, #0x18]
    // 0xd415d0: ldr             x3, [x3, x4, lsl #3]
    // 0xd415d4: LoadField: r3 = r3->field_2b
    //     0xd415d4: ldur            w3, [x3, #0x2b]
    // 0xd415d8: DecompressPointer r3
    //     0xd415d8: add             x3, x3, HEAP, lsl #32
    // 0xd415dc: cmp             w3, NULL
    // 0xd415e0: b.eq            #0xd4163c
    // 0xd415e4: LoadField: r3 = r3->field_f
    //     0xd415e4: ldur            w3, [x3, #0xf]
    // 0xd415e8: lsr             x3, x3, #3
    // 0xd415ec: r17 = 7205
    //     0xd415ec: movz            x17, #0x1c25
    // 0xd415f0: cmp             x3, x17
    // 0xd415f4: b.eq            #0xd41644
    // 0xd415f8: r3 = SubtypeTestCache
    //     0xd415f8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c090] SubtypeTestCache
    //     0xd415fc: ldr             x3, [x3, #0x90]
    // 0xd41600: r30 = Subtype1TestCacheStub
    //     0xd41600: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd41604: LoadField: r30 = r30->field_7
    //     0xd41604: ldur            lr, [lr, #7]
    // 0xd41608: blr             lr
    // 0xd4160c: cmp             w7, NULL
    // 0xd41610: b.eq            #0xd4161c
    // 0xd41614: tbnz            w7, #4, #0xd4163c
    // 0xd41618: b               #0xd41644
    // 0xd4161c: r8 = Iterable
    //     0xd4161c: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c098] Type: Iterable
    //     0xd41620: ldr             x8, [x8, #0x98]
    // 0xd41624: r3 = SubtypeTestCache
    //     0xd41624: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c0a0] SubtypeTestCache
    //     0xd41628: ldr             x3, [x3, #0xa0]
    // 0xd4162c: r30 = InstanceOfStub
    //     0xd4162c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd41630: LoadField: r30 = r30->field_7
    //     0xd41630: ldur            lr, [lr, #7]
    // 0xd41634: blr             lr
    // 0xd41638: b               #0xd41648
    // 0xd4163c: r0 = false
    //     0xd4163c: add             x0, NULL, #0x30  ; false
    // 0xd41640: b               #0xd41648
    // 0xd41644: r0 = true
    //     0xd41644: add             x0, NULL, #0x20  ; true
    // 0xd41648: tbnz            w0, #4, #0xd41db4
    // 0xd4164c: ldur            x1, [fp, #-8]
    // 0xd41650: ldur            x2, [fp, #-0x10]
    // 0xd41654: cmp             w1, w2
    // 0xd41658: b.ne            #0xd41664
    // 0xd4165c: r0 = true
    //     0xd4165c: add             x0, NULL, #0x20  ; true
    // 0xd41660: b               #0xd41da8
    // 0xd41664: r0 = LoadClassIdInstr(r1)
    //     0xd41664: ldur            x0, [x1, #-1]
    //     0xd41668: ubfx            x0, x0, #0xc, #0x14
    // 0xd4166c: str             x1, [SP]
    // 0xd41670: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd41670: movz            x17, #0xc834
    //     0xd41674: add             lr, x0, x17
    //     0xd41678: ldr             lr, [x21, lr, lsl #3]
    //     0xd4167c: blr             lr
    // 0xd41680: mov             x2, x0
    // 0xd41684: ldur            x1, [fp, #-0x10]
    // 0xd41688: stur            x2, [fp, #-0x18]
    // 0xd4168c: r0 = LoadClassIdInstr(r1)
    //     0xd4168c: ldur            x0, [x1, #-1]
    //     0xd41690: ubfx            x0, x0, #0xc, #0x14
    // 0xd41694: str             x1, [SP]
    // 0xd41698: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd41698: movz            x17, #0xc834
    //     0xd4169c: add             lr, x0, x17
    //     0xd416a0: ldr             lr, [x21, lr, lsl #3]
    //     0xd416a4: blr             lr
    // 0xd416a8: mov             x1, x0
    // 0xd416ac: ldur            x0, [fp, #-0x18]
    // 0xd416b0: r2 = LoadInt32Instr(r0)
    //     0xd416b0: sbfx            x2, x0, #1, #0x1f
    //     0xd416b4: tbz             w0, #0, #0xd416bc
    //     0xd416b8: ldur            x2, [x0, #7]
    // 0xd416bc: r0 = LoadInt32Instr(r1)
    //     0xd416bc: sbfx            x0, x1, #1, #0x1f
    //     0xd416c0: tbz             w1, #0, #0xd416c8
    //     0xd416c4: ldur            x0, [x1, #7]
    // 0xd416c8: cmp             x2, x0
    // 0xd416cc: b.eq            #0xd416d8
    // 0xd416d0: r0 = false
    //     0xd416d0: add             x0, NULL, #0x30  ; false
    // 0xd416d4: b               #0xd41da8
    // 0xd416d8: r3 = 0
    //     0xd416d8: movz            x3, #0
    // 0xd416dc: ldur            x2, [fp, #-8]
    // 0xd416e0: ldur            x1, [fp, #-0x10]
    // 0xd416e4: stur            x3, [fp, #-0x20]
    // 0xd416e8: CheckStackOverflow
    //     0xd416e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd416ec: cmp             SP, x16
    //     0xd416f0: b.ls            #0xd42010
    // 0xd416f4: r0 = LoadClassIdInstr(r2)
    //     0xd416f4: ldur            x0, [x2, #-1]
    //     0xd416f8: ubfx            x0, x0, #0xc, #0x14
    // 0xd416fc: str             x2, [SP]
    // 0xd41700: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd41700: movz            x17, #0xc834
    //     0xd41704: add             lr, x0, x17
    //     0xd41708: ldr             lr, [x21, lr, lsl #3]
    //     0xd4170c: blr             lr
    // 0xd41710: r1 = LoadInt32Instr(r0)
    //     0xd41710: sbfx            x1, x0, #1, #0x1f
    //     0xd41714: tbz             w0, #0, #0xd4171c
    //     0xd41718: ldur            x1, [x0, #7]
    // 0xd4171c: ldur            x3, [fp, #-0x20]
    // 0xd41720: cmp             x3, x1
    // 0xd41724: b.ge            #0xd41da4
    // 0xd41728: ldur            x5, [fp, #-8]
    // 0xd4172c: ldur            x4, [fp, #-0x10]
    // 0xd41730: r0 = LoadClassIdInstr(r5)
    //     0xd41730: ldur            x0, [x5, #-1]
    //     0xd41734: ubfx            x0, x0, #0xc, #0x14
    // 0xd41738: mov             x1, x5
    // 0xd4173c: mov             x2, x3
    // 0xd41740: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xd41740: movz            x17, #0xd28f
    //     0xd41744: add             lr, x0, x17
    //     0xd41748: ldr             lr, [x21, lr, lsl #3]
    //     0xd4174c: blr             lr
    // 0xd41750: mov             x4, x0
    // 0xd41754: ldur            x3, [fp, #-0x10]
    // 0xd41758: stur            x4, [fp, #-0x18]
    // 0xd4175c: r0 = LoadClassIdInstr(r3)
    //     0xd4175c: ldur            x0, [x3, #-1]
    //     0xd41760: ubfx            x0, x0, #0xc, #0x14
    // 0xd41764: mov             x1, x3
    // 0xd41768: ldur            x2, [fp, #-0x20]
    // 0xd4176c: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xd4176c: movz            x17, #0xd28f
    //     0xd41770: add             lr, x0, x17
    //     0xd41774: ldr             lr, [x21, lr, lsl #3]
    //     0xd41778: blr             lr
    // 0xd4177c: mov             x1, x0
    // 0xd41780: mov             x2, x0
    // 0xd41784: ldur            x0, [fp, #-0x18]
    // 0xd41788: stur            x2, [fp, #-0x28]
    // 0xd4178c: stp             x1, x0, [SP, #-0x10]!
    // 0xd41790: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0xd41790: ldr             lr, [PP, #0x1200]  ; [pp+0x1200] Stub: OptimizedIdenticalWithNumberCheck (0x5f32bc)
    // 0xd41794: LoadField: r30 = r30->field_7
    //     0xd41794: ldur            lr, [lr, #7]
    // 0xd41798: blr             lr
    // 0xd4179c: ldp             x1, x0, [SP], #0x10
    // 0xd417a0: b.eq            #0xd41d90
    // 0xd417a4: ldur            x3, [fp, #-0x18]
    // 0xd417a8: r0 = 60
    //     0xd417a8: movz            x0, #0x3c
    // 0xd417ac: branchIfSmi(r3, 0xd417b8)
    //     0xd417ac: tbz             w3, #0, #0xd417b8
    // 0xd417b0: r0 = LoadClassIdInstr(r3)
    //     0xd417b0: ldur            x0, [x3, #-1]
    //     0xd417b4: ubfx            x0, x0, #0xc, #0x14
    // 0xd417b8: sub             x16, x0, #0x3c
    // 0xd417bc: cmp             x16, #2
    // 0xd417c0: b.hi            #0xd4180c
    // 0xd417c4: ldur            x4, [fp, #-0x28]
    // 0xd417c8: r1 = 60
    //     0xd417c8: movz            x1, #0x3c
    // 0xd417cc: branchIfSmi(r4, 0xd417d8)
    //     0xd417cc: tbz             w4, #0, #0xd417d8
    // 0xd417d0: r1 = LoadClassIdInstr(r4)
    //     0xd417d0: ldur            x1, [x4, #-1]
    //     0xd417d4: ubfx            x1, x1, #0xc, #0x14
    // 0xd417d8: sub             x16, x1, #0x3c
    // 0xd417dc: cmp             x16, #2
    // 0xd417e0: b.hi            #0xd41810
    // 0xd417e4: r0 = 60
    //     0xd417e4: movz            x0, #0x3c
    // 0xd417e8: branchIfSmi(r3, 0xd417f4)
    //     0xd417e8: tbz             w3, #0, #0xd417f4
    // 0xd417ec: r0 = LoadClassIdInstr(r3)
    //     0xd417ec: ldur            x0, [x3, #-1]
    //     0xd417f0: ubfx            x0, x0, #0xc, #0x14
    // 0xd417f4: stp             x4, x3, [SP]
    // 0xd417f8: mov             lr, x0
    // 0xd417fc: ldr             lr, [x21, lr, lsl #3]
    // 0xd41800: blr             lr
    // 0xd41804: tbz             w0, #4, #0xd41d90
    // 0xd41808: b               #0xd41d9c
    // 0xd4180c: ldur            x4, [fp, #-0x28]
    // 0xd41810: r17 = -5561
    //     0xd41810: movn            x17, #0x15b8
    // 0xd41814: add             x16, x0, x17
    // 0xd41818: cmp             x16, #0x2a
    // 0xd4181c: b.hi            #0xd41868
    // 0xd41820: r0 = 60
    //     0xd41820: movz            x0, #0x3c
    // 0xd41824: branchIfSmi(r4, 0xd41830)
    //     0xd41824: tbz             w4, #0, #0xd41830
    // 0xd41828: r0 = LoadClassIdInstr(r4)
    //     0xd41828: ldur            x0, [x4, #-1]
    //     0xd4182c: ubfx            x0, x0, #0xc, #0x14
    // 0xd41830: r17 = -5561
    //     0xd41830: movn            x17, #0x15b8
    // 0xd41834: add             x16, x0, x17
    // 0xd41838: cmp             x16, #0x2a
    // 0xd4183c: b.hi            #0xd41868
    // 0xd41840: r0 = 60
    //     0xd41840: movz            x0, #0x3c
    // 0xd41844: branchIfSmi(r3, 0xd41850)
    //     0xd41844: tbz             w3, #0, #0xd41850
    // 0xd41848: r0 = LoadClassIdInstr(r3)
    //     0xd41848: ldur            x0, [x3, #-1]
    //     0xd4184c: ubfx            x0, x0, #0xc, #0x14
    // 0xd41850: stp             x4, x3, [SP]
    // 0xd41854: mov             lr, x0
    // 0xd41858: ldr             lr, [x21, lr, lsl #3]
    // 0xd4185c: blr             lr
    // 0xd41860: tbz             w0, #4, #0xd41d90
    // 0xd41864: b               #0xd41d9c
    // 0xd41868: mov             x0, x3
    // 0xd4186c: r2 = Null
    //     0xd4186c: mov             x2, NULL
    // 0xd41870: r1 = Null
    //     0xd41870: mov             x1, NULL
    // 0xd41874: cmp             w0, NULL
    // 0xd41878: b.eq            #0xd41910
    // 0xd4187c: branchIfSmi(r0, 0xd41910)
    //     0xd4187c: tbz             w0, #0, #0xd41910
    // 0xd41880: r3 = LoadClassIdInstr(r0)
    //     0xd41880: ldur            x3, [x0, #-1]
    //     0xd41884: ubfx            x3, x3, #0xc, #0x14
    // 0xd41888: r17 = 6713
    //     0xd41888: movz            x17, #0x1a39
    // 0xd4188c: cmp             x3, x17
    // 0xd41890: b.eq            #0xd41918
    // 0xd41894: r4 = LoadClassIdInstr(r0)
    //     0xd41894: ldur            x4, [x0, #-1]
    //     0xd41898: ubfx            x4, x4, #0xc, #0x14
    // 0xd4189c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd418a0: ldr             x3, [x3, #0x18]
    // 0xd418a4: ldr             x3, [x3, x4, lsl #3]
    // 0xd418a8: LoadField: r3 = r3->field_2b
    //     0xd418a8: ldur            w3, [x3, #0x2b]
    // 0xd418ac: DecompressPointer r3
    //     0xd418ac: add             x3, x3, HEAP, lsl #32
    // 0xd418b0: cmp             w3, NULL
    // 0xd418b4: b.eq            #0xd41910
    // 0xd418b8: LoadField: r3 = r3->field_f
    //     0xd418b8: ldur            w3, [x3, #0xf]
    // 0xd418bc: lsr             x3, x3, #3
    // 0xd418c0: r17 = 6713
    //     0xd418c0: movz            x17, #0x1a39
    // 0xd418c4: cmp             x3, x17
    // 0xd418c8: b.eq            #0xd41918
    // 0xd418cc: r3 = SubtypeTestCache
    //     0xd418cc: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c0a8] SubtypeTestCache
    //     0xd418d0: ldr             x3, [x3, #0xa8]
    // 0xd418d4: r30 = Subtype1TestCacheStub
    //     0xd418d4: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd418d8: LoadField: r30 = r30->field_7
    //     0xd418d8: ldur            lr, [lr, #7]
    // 0xd418dc: blr             lr
    // 0xd418e0: cmp             w7, NULL
    // 0xd418e4: b.eq            #0xd418f0
    // 0xd418e8: tbnz            w7, #4, #0xd41910
    // 0xd418ec: b               #0xd41918
    // 0xd418f0: r8 = Set
    //     0xd418f0: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c0b0] Type: Set
    //     0xd418f4: ldr             x8, [x8, #0xb0]
    // 0xd418f8: r3 = SubtypeTestCache
    //     0xd418f8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c0b8] SubtypeTestCache
    //     0xd418fc: ldr             x3, [x3, #0xb8]
    // 0xd41900: r30 = InstanceOfStub
    //     0xd41900: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd41904: LoadField: r30 = r30->field_7
    //     0xd41904: ldur            lr, [lr, #7]
    // 0xd41908: blr             lr
    // 0xd4190c: b               #0xd4191c
    // 0xd41910: r0 = false
    //     0xd41910: add             x0, NULL, #0x30  ; false
    // 0xd41914: b               #0xd4191c
    // 0xd41918: r0 = true
    //     0xd41918: add             x0, NULL, #0x20  ; true
    // 0xd4191c: tbnz            w0, #4, #0xd419ec
    // 0xd41920: ldur            x0, [fp, #-0x28]
    // 0xd41924: r2 = Null
    //     0xd41924: mov             x2, NULL
    // 0xd41928: r1 = Null
    //     0xd41928: mov             x1, NULL
    // 0xd4192c: cmp             w0, NULL
    // 0xd41930: b.eq            #0xd419c8
    // 0xd41934: branchIfSmi(r0, 0xd419c8)
    //     0xd41934: tbz             w0, #0, #0xd419c8
    // 0xd41938: r3 = LoadClassIdInstr(r0)
    //     0xd41938: ldur            x3, [x0, #-1]
    //     0xd4193c: ubfx            x3, x3, #0xc, #0x14
    // 0xd41940: r17 = 6713
    //     0xd41940: movz            x17, #0x1a39
    // 0xd41944: cmp             x3, x17
    // 0xd41948: b.eq            #0xd419d0
    // 0xd4194c: r4 = LoadClassIdInstr(r0)
    //     0xd4194c: ldur            x4, [x0, #-1]
    //     0xd41950: ubfx            x4, x4, #0xc, #0x14
    // 0xd41954: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd41958: ldr             x3, [x3, #0x18]
    // 0xd4195c: ldr             x3, [x3, x4, lsl #3]
    // 0xd41960: LoadField: r3 = r3->field_2b
    //     0xd41960: ldur            w3, [x3, #0x2b]
    // 0xd41964: DecompressPointer r3
    //     0xd41964: add             x3, x3, HEAP, lsl #32
    // 0xd41968: cmp             w3, NULL
    // 0xd4196c: b.eq            #0xd419c8
    // 0xd41970: LoadField: r3 = r3->field_f
    //     0xd41970: ldur            w3, [x3, #0xf]
    // 0xd41974: lsr             x3, x3, #3
    // 0xd41978: r17 = 6713
    //     0xd41978: movz            x17, #0x1a39
    // 0xd4197c: cmp             x3, x17
    // 0xd41980: b.eq            #0xd419d0
    // 0xd41984: r3 = SubtypeTestCache
    //     0xd41984: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c0c0] SubtypeTestCache
    //     0xd41988: ldr             x3, [x3, #0xc0]
    // 0xd4198c: r30 = Subtype1TestCacheStub
    //     0xd4198c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd41990: LoadField: r30 = r30->field_7
    //     0xd41990: ldur            lr, [lr, #7]
    // 0xd41994: blr             lr
    // 0xd41998: cmp             w7, NULL
    // 0xd4199c: b.eq            #0xd419a8
    // 0xd419a0: tbnz            w7, #4, #0xd419c8
    // 0xd419a4: b               #0xd419d0
    // 0xd419a8: r8 = Set
    //     0xd419a8: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c0c8] Type: Set
    //     0xd419ac: ldr             x8, [x8, #0xc8]
    // 0xd419b0: r3 = SubtypeTestCache
    //     0xd419b0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c0d0] SubtypeTestCache
    //     0xd419b4: ldr             x3, [x3, #0xd0]
    // 0xd419b8: r30 = InstanceOfStub
    //     0xd419b8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd419bc: LoadField: r30 = r30->field_7
    //     0xd419bc: ldur            lr, [lr, #7]
    // 0xd419c0: blr             lr
    // 0xd419c4: b               #0xd419d4
    // 0xd419c8: r0 = false
    //     0xd419c8: add             x0, NULL, #0x30  ; false
    // 0xd419cc: b               #0xd419d4
    // 0xd419d0: r0 = true
    //     0xd419d0: add             x0, NULL, #0x20  ; true
    // 0xd419d4: tbnz            w0, #4, #0xd419ec
    // 0xd419d8: ldur            x1, [fp, #-0x18]
    // 0xd419dc: ldur            x2, [fp, #-0x28]
    // 0xd419e0: r0 = setEquals()
    //     0xd419e0: bl              #0xd42018  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0xd419e4: tbz             w0, #4, #0xd41d90
    // 0xd419e8: b               #0xd41d9c
    // 0xd419ec: ldur            x0, [fp, #-0x18]
    // 0xd419f0: r2 = Null
    //     0xd419f0: mov             x2, NULL
    // 0xd419f4: r1 = Null
    //     0xd419f4: mov             x1, NULL
    // 0xd419f8: cmp             w0, NULL
    // 0xd419fc: b.eq            #0xd41a94
    // 0xd41a00: branchIfSmi(r0, 0xd41a94)
    //     0xd41a00: tbz             w0, #0, #0xd41a94
    // 0xd41a04: r3 = LoadClassIdInstr(r0)
    //     0xd41a04: ldur            x3, [x0, #-1]
    //     0xd41a08: ubfx            x3, x3, #0xc, #0x14
    // 0xd41a0c: r17 = 7205
    //     0xd41a0c: movz            x17, #0x1c25
    // 0xd41a10: cmp             x3, x17
    // 0xd41a14: b.eq            #0xd41a9c
    // 0xd41a18: r4 = LoadClassIdInstr(r0)
    //     0xd41a18: ldur            x4, [x0, #-1]
    //     0xd41a1c: ubfx            x4, x4, #0xc, #0x14
    // 0xd41a20: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd41a24: ldr             x3, [x3, #0x18]
    // 0xd41a28: ldr             x3, [x3, x4, lsl #3]
    // 0xd41a2c: LoadField: r3 = r3->field_2b
    //     0xd41a2c: ldur            w3, [x3, #0x2b]
    // 0xd41a30: DecompressPointer r3
    //     0xd41a30: add             x3, x3, HEAP, lsl #32
    // 0xd41a34: cmp             w3, NULL
    // 0xd41a38: b.eq            #0xd41a94
    // 0xd41a3c: LoadField: r3 = r3->field_f
    //     0xd41a3c: ldur            w3, [x3, #0xf]
    // 0xd41a40: lsr             x3, x3, #3
    // 0xd41a44: r17 = 7205
    //     0xd41a44: movz            x17, #0x1c25
    // 0xd41a48: cmp             x3, x17
    // 0xd41a4c: b.eq            #0xd41a9c
    // 0xd41a50: r3 = SubtypeTestCache
    //     0xd41a50: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c0d8] SubtypeTestCache
    //     0xd41a54: ldr             x3, [x3, #0xd8]
    // 0xd41a58: r30 = Subtype1TestCacheStub
    //     0xd41a58: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd41a5c: LoadField: r30 = r30->field_7
    //     0xd41a5c: ldur            lr, [lr, #7]
    // 0xd41a60: blr             lr
    // 0xd41a64: cmp             w7, NULL
    // 0xd41a68: b.eq            #0xd41a74
    // 0xd41a6c: tbnz            w7, #4, #0xd41a94
    // 0xd41a70: b               #0xd41a9c
    // 0xd41a74: r8 = Iterable
    //     0xd41a74: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c0e0] Type: Iterable
    //     0xd41a78: ldr             x8, [x8, #0xe0]
    // 0xd41a7c: r3 = SubtypeTestCache
    //     0xd41a7c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c0e8] SubtypeTestCache
    //     0xd41a80: ldr             x3, [x3, #0xe8]
    // 0xd41a84: r30 = InstanceOfStub
    //     0xd41a84: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd41a88: LoadField: r30 = r30->field_7
    //     0xd41a88: ldur            lr, [lr, #7]
    // 0xd41a8c: blr             lr
    // 0xd41a90: b               #0xd41aa0
    // 0xd41a94: r0 = false
    //     0xd41a94: add             x0, NULL, #0x30  ; false
    // 0xd41a98: b               #0xd41aa0
    // 0xd41a9c: r0 = true
    //     0xd41a9c: add             x0, NULL, #0x20  ; true
    // 0xd41aa0: tbnz            w0, #4, #0xd41b70
    // 0xd41aa4: ldur            x0, [fp, #-0x28]
    // 0xd41aa8: r2 = Null
    //     0xd41aa8: mov             x2, NULL
    // 0xd41aac: r1 = Null
    //     0xd41aac: mov             x1, NULL
    // 0xd41ab0: cmp             w0, NULL
    // 0xd41ab4: b.eq            #0xd41b4c
    // 0xd41ab8: branchIfSmi(r0, 0xd41b4c)
    //     0xd41ab8: tbz             w0, #0, #0xd41b4c
    // 0xd41abc: r3 = LoadClassIdInstr(r0)
    //     0xd41abc: ldur            x3, [x0, #-1]
    //     0xd41ac0: ubfx            x3, x3, #0xc, #0x14
    // 0xd41ac4: r17 = 7205
    //     0xd41ac4: movz            x17, #0x1c25
    // 0xd41ac8: cmp             x3, x17
    // 0xd41acc: b.eq            #0xd41b54
    // 0xd41ad0: r4 = LoadClassIdInstr(r0)
    //     0xd41ad0: ldur            x4, [x0, #-1]
    //     0xd41ad4: ubfx            x4, x4, #0xc, #0x14
    // 0xd41ad8: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd41adc: ldr             x3, [x3, #0x18]
    // 0xd41ae0: ldr             x3, [x3, x4, lsl #3]
    // 0xd41ae4: LoadField: r3 = r3->field_2b
    //     0xd41ae4: ldur            w3, [x3, #0x2b]
    // 0xd41ae8: DecompressPointer r3
    //     0xd41ae8: add             x3, x3, HEAP, lsl #32
    // 0xd41aec: cmp             w3, NULL
    // 0xd41af0: b.eq            #0xd41b4c
    // 0xd41af4: LoadField: r3 = r3->field_f
    //     0xd41af4: ldur            w3, [x3, #0xf]
    // 0xd41af8: lsr             x3, x3, #3
    // 0xd41afc: r17 = 7205
    //     0xd41afc: movz            x17, #0x1c25
    // 0xd41b00: cmp             x3, x17
    // 0xd41b04: b.eq            #0xd41b54
    // 0xd41b08: r3 = SubtypeTestCache
    //     0xd41b08: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c0f0] SubtypeTestCache
    //     0xd41b0c: ldr             x3, [x3, #0xf0]
    // 0xd41b10: r30 = Subtype1TestCacheStub
    //     0xd41b10: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd41b14: LoadField: r30 = r30->field_7
    //     0xd41b14: ldur            lr, [lr, #7]
    // 0xd41b18: blr             lr
    // 0xd41b1c: cmp             w7, NULL
    // 0xd41b20: b.eq            #0xd41b2c
    // 0xd41b24: tbnz            w7, #4, #0xd41b4c
    // 0xd41b28: b               #0xd41b54
    // 0xd41b2c: r8 = Iterable
    //     0xd41b2c: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c0f8] Type: Iterable
    //     0xd41b30: ldr             x8, [x8, #0xf8]
    // 0xd41b34: r3 = SubtypeTestCache
    //     0xd41b34: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c100] SubtypeTestCache
    //     0xd41b38: ldr             x3, [x3, #0x100]
    // 0xd41b3c: r30 = InstanceOfStub
    //     0xd41b3c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd41b40: LoadField: r30 = r30->field_7
    //     0xd41b40: ldur            lr, [lr, #7]
    // 0xd41b44: blr             lr
    // 0xd41b48: b               #0xd41b58
    // 0xd41b4c: r0 = false
    //     0xd41b4c: add             x0, NULL, #0x30  ; false
    // 0xd41b50: b               #0xd41b58
    // 0xd41b54: r0 = true
    //     0xd41b54: add             x0, NULL, #0x20  ; true
    // 0xd41b58: tbnz            w0, #4, #0xd41b70
    // 0xd41b5c: ldur            x1, [fp, #-0x18]
    // 0xd41b60: ldur            x2, [fp, #-0x28]
    // 0xd41b64: r0 = iterableEquals()
    //     0xd41b64: bl              #0xd3fa14  ; [package:equatable/src/equatable_utils.dart] ::iterableEquals
    // 0xd41b68: tbz             w0, #4, #0xd41d90
    // 0xd41b6c: b               #0xd41d9c
    // 0xd41b70: ldur            x0, [fp, #-0x18]
    // 0xd41b74: r2 = Null
    //     0xd41b74: mov             x2, NULL
    // 0xd41b78: r1 = Null
    //     0xd41b78: mov             x1, NULL
    // 0xd41b7c: cmp             w0, NULL
    // 0xd41b80: b.eq            #0xd41c18
    // 0xd41b84: branchIfSmi(r0, 0xd41c18)
    //     0xd41b84: tbz             w0, #0, #0xd41c18
    // 0xd41b88: r3 = LoadClassIdInstr(r0)
    //     0xd41b88: ldur            x3, [x0, #-1]
    //     0xd41b8c: ubfx            x3, x3, #0xc, #0x14
    // 0xd41b90: r17 = 6717
    //     0xd41b90: movz            x17, #0x1a3d
    // 0xd41b94: cmp             x3, x17
    // 0xd41b98: b.eq            #0xd41c20
    // 0xd41b9c: r4 = LoadClassIdInstr(r0)
    //     0xd41b9c: ldur            x4, [x0, #-1]
    //     0xd41ba0: ubfx            x4, x4, #0xc, #0x14
    // 0xd41ba4: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd41ba8: ldr             x3, [x3, #0x18]
    // 0xd41bac: ldr             x3, [x3, x4, lsl #3]
    // 0xd41bb0: LoadField: r3 = r3->field_2b
    //     0xd41bb0: ldur            w3, [x3, #0x2b]
    // 0xd41bb4: DecompressPointer r3
    //     0xd41bb4: add             x3, x3, HEAP, lsl #32
    // 0xd41bb8: cmp             w3, NULL
    // 0xd41bbc: b.eq            #0xd41c18
    // 0xd41bc0: LoadField: r3 = r3->field_f
    //     0xd41bc0: ldur            w3, [x3, #0xf]
    // 0xd41bc4: lsr             x3, x3, #3
    // 0xd41bc8: r17 = 6717
    //     0xd41bc8: movz            x17, #0x1a3d
    // 0xd41bcc: cmp             x3, x17
    // 0xd41bd0: b.eq            #0xd41c20
    // 0xd41bd4: r3 = SubtypeTestCache
    //     0xd41bd4: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c108] SubtypeTestCache
    //     0xd41bd8: ldr             x3, [x3, #0x108]
    // 0xd41bdc: r30 = Subtype1TestCacheStub
    //     0xd41bdc: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd41be0: LoadField: r30 = r30->field_7
    //     0xd41be0: ldur            lr, [lr, #7]
    // 0xd41be4: blr             lr
    // 0xd41be8: cmp             w7, NULL
    // 0xd41bec: b.eq            #0xd41bf8
    // 0xd41bf0: tbnz            w7, #4, #0xd41c18
    // 0xd41bf4: b               #0xd41c20
    // 0xd41bf8: r8 = Map
    //     0xd41bf8: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c110] Type: Map
    //     0xd41bfc: ldr             x8, [x8, #0x110]
    // 0xd41c00: r3 = SubtypeTestCache
    //     0xd41c00: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c118] SubtypeTestCache
    //     0xd41c04: ldr             x3, [x3, #0x118]
    // 0xd41c08: r30 = InstanceOfStub
    //     0xd41c08: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd41c0c: LoadField: r30 = r30->field_7
    //     0xd41c0c: ldur            lr, [lr, #7]
    // 0xd41c10: blr             lr
    // 0xd41c14: b               #0xd41c24
    // 0xd41c18: r0 = false
    //     0xd41c18: add             x0, NULL, #0x30  ; false
    // 0xd41c1c: b               #0xd41c24
    // 0xd41c20: r0 = true
    //     0xd41c20: add             x0, NULL, #0x20  ; true
    // 0xd41c24: tbnz            w0, #4, #0xd41cf4
    // 0xd41c28: ldur            x0, [fp, #-0x28]
    // 0xd41c2c: r2 = Null
    //     0xd41c2c: mov             x2, NULL
    // 0xd41c30: r1 = Null
    //     0xd41c30: mov             x1, NULL
    // 0xd41c34: cmp             w0, NULL
    // 0xd41c38: b.eq            #0xd41cd0
    // 0xd41c3c: branchIfSmi(r0, 0xd41cd0)
    //     0xd41c3c: tbz             w0, #0, #0xd41cd0
    // 0xd41c40: r3 = LoadClassIdInstr(r0)
    //     0xd41c40: ldur            x3, [x0, #-1]
    //     0xd41c44: ubfx            x3, x3, #0xc, #0x14
    // 0xd41c48: r17 = 6717
    //     0xd41c48: movz            x17, #0x1a3d
    // 0xd41c4c: cmp             x3, x17
    // 0xd41c50: b.eq            #0xd41cd8
    // 0xd41c54: r4 = LoadClassIdInstr(r0)
    //     0xd41c54: ldur            x4, [x0, #-1]
    //     0xd41c58: ubfx            x4, x4, #0xc, #0x14
    // 0xd41c5c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd41c60: ldr             x3, [x3, #0x18]
    // 0xd41c64: ldr             x3, [x3, x4, lsl #3]
    // 0xd41c68: LoadField: r3 = r3->field_2b
    //     0xd41c68: ldur            w3, [x3, #0x2b]
    // 0xd41c6c: DecompressPointer r3
    //     0xd41c6c: add             x3, x3, HEAP, lsl #32
    // 0xd41c70: cmp             w3, NULL
    // 0xd41c74: b.eq            #0xd41cd0
    // 0xd41c78: LoadField: r3 = r3->field_f
    //     0xd41c78: ldur            w3, [x3, #0xf]
    // 0xd41c7c: lsr             x3, x3, #3
    // 0xd41c80: r17 = 6717
    //     0xd41c80: movz            x17, #0x1a3d
    // 0xd41c84: cmp             x3, x17
    // 0xd41c88: b.eq            #0xd41cd8
    // 0xd41c8c: r3 = SubtypeTestCache
    //     0xd41c8c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c120] SubtypeTestCache
    //     0xd41c90: ldr             x3, [x3, #0x120]
    // 0xd41c94: r30 = Subtype1TestCacheStub
    //     0xd41c94: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd41c98: LoadField: r30 = r30->field_7
    //     0xd41c98: ldur            lr, [lr, #7]
    // 0xd41c9c: blr             lr
    // 0xd41ca0: cmp             w7, NULL
    // 0xd41ca4: b.eq            #0xd41cb0
    // 0xd41ca8: tbnz            w7, #4, #0xd41cd0
    // 0xd41cac: b               #0xd41cd8
    // 0xd41cb0: r8 = Map
    //     0xd41cb0: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c128] Type: Map
    //     0xd41cb4: ldr             x8, [x8, #0x128]
    // 0xd41cb8: r3 = SubtypeTestCache
    //     0xd41cb8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c130] SubtypeTestCache
    //     0xd41cbc: ldr             x3, [x3, #0x130]
    // 0xd41cc0: r30 = InstanceOfStub
    //     0xd41cc0: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd41cc4: LoadField: r30 = r30->field_7
    //     0xd41cc4: ldur            lr, [lr, #7]
    // 0xd41cc8: blr             lr
    // 0xd41ccc: b               #0xd41cdc
    // 0xd41cd0: r0 = false
    //     0xd41cd0: add             x0, NULL, #0x30  ; false
    // 0xd41cd4: b               #0xd41cdc
    // 0xd41cd8: r0 = true
    //     0xd41cd8: add             x0, NULL, #0x20  ; true
    // 0xd41cdc: tbnz            w0, #4, #0xd41cf4
    // 0xd41ce0: ldur            x1, [fp, #-0x18]
    // 0xd41ce4: ldur            x2, [fp, #-0x28]
    // 0xd41ce8: r0 = mapEquals()
    //     0xd41ce8: bl              #0xd402f8  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0xd41cec: tbz             w0, #4, #0xd41d90
    // 0xd41cf0: b               #0xd41d9c
    // 0xd41cf4: ldur            x0, [fp, #-0x18]
    // 0xd41cf8: cmp             w0, NULL
    // 0xd41cfc: b.ne            #0xd41d08
    // 0xd41d00: r1 = Null
    //     0xd41d00: mov             x1, NULL
    // 0xd41d04: b               #0xd41d14
    // 0xd41d08: str             x0, [SP]
    // 0xd41d0c: r0 = runtimeType()
    //     0xd41d0c: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd41d10: mov             x1, x0
    // 0xd41d14: ldur            x0, [fp, #-0x28]
    // 0xd41d18: stur            x1, [fp, #-0x30]
    // 0xd41d1c: cmp             w0, NULL
    // 0xd41d20: b.ne            #0xd41d30
    // 0xd41d24: mov             x0, x1
    // 0xd41d28: r1 = Null
    //     0xd41d28: mov             x1, NULL
    // 0xd41d2c: b               #0xd41d40
    // 0xd41d30: str             x0, [SP]
    // 0xd41d34: r0 = runtimeType()
    //     0xd41d34: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd41d38: mov             x1, x0
    // 0xd41d3c: ldur            x0, [fp, #-0x30]
    // 0xd41d40: r2 = LoadClassIdInstr(r0)
    //     0xd41d40: ldur            x2, [x0, #-1]
    //     0xd41d44: ubfx            x2, x2, #0xc, #0x14
    // 0xd41d48: stp             x1, x0, [SP]
    // 0xd41d4c: mov             x0, x2
    // 0xd41d50: mov             lr, x0
    // 0xd41d54: ldr             lr, [x21, lr, lsl #3]
    // 0xd41d58: blr             lr
    // 0xd41d5c: tbnz            w0, #4, #0xd41d9c
    // 0xd41d60: ldur            x0, [fp, #-0x18]
    // 0xd41d64: r1 = 60
    //     0xd41d64: movz            x1, #0x3c
    // 0xd41d68: branchIfSmi(r0, 0xd41d74)
    //     0xd41d68: tbz             w0, #0, #0xd41d74
    // 0xd41d6c: r1 = LoadClassIdInstr(r0)
    //     0xd41d6c: ldur            x1, [x0, #-1]
    //     0xd41d70: ubfx            x1, x1, #0xc, #0x14
    // 0xd41d74: ldur            x16, [fp, #-0x28]
    // 0xd41d78: stp             x16, x0, [SP]
    // 0xd41d7c: mov             x0, x1
    // 0xd41d80: mov             lr, x0
    // 0xd41d84: ldr             lr, [x21, lr, lsl #3]
    // 0xd41d88: blr             lr
    // 0xd41d8c: tbnz            w0, #4, #0xd41d9c
    // 0xd41d90: ldur            x0, [fp, #-0x20]
    // 0xd41d94: add             x3, x0, #1
    // 0xd41d98: b               #0xd416dc
    // 0xd41d9c: r0 = false
    //     0xd41d9c: add             x0, NULL, #0x30  ; false
    // 0xd41da0: b               #0xd41da8
    // 0xd41da4: r0 = true
    //     0xd41da4: add             x0, NULL, #0x20  ; true
    // 0xd41da8: LeaveFrame
    //     0xd41da8: mov             SP, fp
    //     0xd41dac: ldp             fp, lr, [SP], #0x10
    // 0xd41db0: ret
    //     0xd41db0: ret             
    // 0xd41db4: ldur            x0, [fp, #-8]
    // 0xd41db8: r2 = Null
    //     0xd41db8: mov             x2, NULL
    // 0xd41dbc: r1 = Null
    //     0xd41dbc: mov             x1, NULL
    // 0xd41dc0: cmp             w0, NULL
    // 0xd41dc4: b.eq            #0xd41e5c
    // 0xd41dc8: branchIfSmi(r0, 0xd41e5c)
    //     0xd41dc8: tbz             w0, #0, #0xd41e5c
    // 0xd41dcc: r3 = LoadClassIdInstr(r0)
    //     0xd41dcc: ldur            x3, [x0, #-1]
    //     0xd41dd0: ubfx            x3, x3, #0xc, #0x14
    // 0xd41dd4: r17 = 6717
    //     0xd41dd4: movz            x17, #0x1a3d
    // 0xd41dd8: cmp             x3, x17
    // 0xd41ddc: b.eq            #0xd41e64
    // 0xd41de0: r4 = LoadClassIdInstr(r0)
    //     0xd41de0: ldur            x4, [x0, #-1]
    //     0xd41de4: ubfx            x4, x4, #0xc, #0x14
    // 0xd41de8: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd41dec: ldr             x3, [x3, #0x18]
    // 0xd41df0: ldr             x3, [x3, x4, lsl #3]
    // 0xd41df4: LoadField: r3 = r3->field_2b
    //     0xd41df4: ldur            w3, [x3, #0x2b]
    // 0xd41df8: DecompressPointer r3
    //     0xd41df8: add             x3, x3, HEAP, lsl #32
    // 0xd41dfc: cmp             w3, NULL
    // 0xd41e00: b.eq            #0xd41e5c
    // 0xd41e04: LoadField: r3 = r3->field_f
    //     0xd41e04: ldur            w3, [x3, #0xf]
    // 0xd41e08: lsr             x3, x3, #3
    // 0xd41e0c: r17 = 6717
    //     0xd41e0c: movz            x17, #0x1a3d
    // 0xd41e10: cmp             x3, x17
    // 0xd41e14: b.eq            #0xd41e64
    // 0xd41e18: r3 = SubtypeTestCache
    //     0xd41e18: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c138] SubtypeTestCache
    //     0xd41e1c: ldr             x3, [x3, #0x138]
    // 0xd41e20: r30 = Subtype1TestCacheStub
    //     0xd41e20: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd41e24: LoadField: r30 = r30->field_7
    //     0xd41e24: ldur            lr, [lr, #7]
    // 0xd41e28: blr             lr
    // 0xd41e2c: cmp             w7, NULL
    // 0xd41e30: b.eq            #0xd41e3c
    // 0xd41e34: tbnz            w7, #4, #0xd41e5c
    // 0xd41e38: b               #0xd41e64
    // 0xd41e3c: r8 = Map
    //     0xd41e3c: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c140] Type: Map
    //     0xd41e40: ldr             x8, [x8, #0x140]
    // 0xd41e44: r3 = SubtypeTestCache
    //     0xd41e44: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c148] SubtypeTestCache
    //     0xd41e48: ldr             x3, [x3, #0x148]
    // 0xd41e4c: r30 = InstanceOfStub
    //     0xd41e4c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd41e50: LoadField: r30 = r30->field_7
    //     0xd41e50: ldur            lr, [lr, #7]
    // 0xd41e54: blr             lr
    // 0xd41e58: b               #0xd41e68
    // 0xd41e5c: r0 = false
    //     0xd41e5c: add             x0, NULL, #0x30  ; false
    // 0xd41e60: b               #0xd41e68
    // 0xd41e64: r0 = true
    //     0xd41e64: add             x0, NULL, #0x20  ; true
    // 0xd41e68: tbnz            w0, #4, #0xd41f3c
    // 0xd41e6c: ldur            x0, [fp, #-0x10]
    // 0xd41e70: r2 = Null
    //     0xd41e70: mov             x2, NULL
    // 0xd41e74: r1 = Null
    //     0xd41e74: mov             x1, NULL
    // 0xd41e78: cmp             w0, NULL
    // 0xd41e7c: b.eq            #0xd41f14
    // 0xd41e80: branchIfSmi(r0, 0xd41f14)
    //     0xd41e80: tbz             w0, #0, #0xd41f14
    // 0xd41e84: r3 = LoadClassIdInstr(r0)
    //     0xd41e84: ldur            x3, [x0, #-1]
    //     0xd41e88: ubfx            x3, x3, #0xc, #0x14
    // 0xd41e8c: r17 = 6717
    //     0xd41e8c: movz            x17, #0x1a3d
    // 0xd41e90: cmp             x3, x17
    // 0xd41e94: b.eq            #0xd41f1c
    // 0xd41e98: r4 = LoadClassIdInstr(r0)
    //     0xd41e98: ldur            x4, [x0, #-1]
    //     0xd41e9c: ubfx            x4, x4, #0xc, #0x14
    // 0xd41ea0: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd41ea4: ldr             x3, [x3, #0x18]
    // 0xd41ea8: ldr             x3, [x3, x4, lsl #3]
    // 0xd41eac: LoadField: r3 = r3->field_2b
    //     0xd41eac: ldur            w3, [x3, #0x2b]
    // 0xd41eb0: DecompressPointer r3
    //     0xd41eb0: add             x3, x3, HEAP, lsl #32
    // 0xd41eb4: cmp             w3, NULL
    // 0xd41eb8: b.eq            #0xd41f14
    // 0xd41ebc: LoadField: r3 = r3->field_f
    //     0xd41ebc: ldur            w3, [x3, #0xf]
    // 0xd41ec0: lsr             x3, x3, #3
    // 0xd41ec4: r17 = 6717
    //     0xd41ec4: movz            x17, #0x1a3d
    // 0xd41ec8: cmp             x3, x17
    // 0xd41ecc: b.eq            #0xd41f1c
    // 0xd41ed0: r3 = SubtypeTestCache
    //     0xd41ed0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c150] SubtypeTestCache
    //     0xd41ed4: ldr             x3, [x3, #0x150]
    // 0xd41ed8: r30 = Subtype1TestCacheStub
    //     0xd41ed8: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd41edc: LoadField: r30 = r30->field_7
    //     0xd41edc: ldur            lr, [lr, #7]
    // 0xd41ee0: blr             lr
    // 0xd41ee4: cmp             w7, NULL
    // 0xd41ee8: b.eq            #0xd41ef4
    // 0xd41eec: tbnz            w7, #4, #0xd41f14
    // 0xd41ef0: b               #0xd41f1c
    // 0xd41ef4: r8 = Map
    //     0xd41ef4: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c158] Type: Map
    //     0xd41ef8: ldr             x8, [x8, #0x158]
    // 0xd41efc: r3 = SubtypeTestCache
    //     0xd41efc: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c160] SubtypeTestCache
    //     0xd41f00: ldr             x3, [x3, #0x160]
    // 0xd41f04: r30 = InstanceOfStub
    //     0xd41f04: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd41f08: LoadField: r30 = r30->field_7
    //     0xd41f08: ldur            lr, [lr, #7]
    // 0xd41f0c: blr             lr
    // 0xd41f10: b               #0xd41f20
    // 0xd41f14: r0 = false
    //     0xd41f14: add             x0, NULL, #0x30  ; false
    // 0xd41f18: b               #0xd41f20
    // 0xd41f1c: r0 = true
    //     0xd41f1c: add             x0, NULL, #0x20  ; true
    // 0xd41f20: tbnz            w0, #4, #0xd41f3c
    // 0xd41f24: ldur            x1, [fp, #-8]
    // 0xd41f28: ldur            x2, [fp, #-0x10]
    // 0xd41f2c: r0 = mapEquals()
    //     0xd41f2c: bl              #0xd402f8  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0xd41f30: LeaveFrame
    //     0xd41f30: mov             SP, fp
    //     0xd41f34: ldp             fp, lr, [SP], #0x10
    // 0xd41f38: ret
    //     0xd41f38: ret             
    // 0xd41f3c: ldur            x0, [fp, #-8]
    // 0xd41f40: cmp             w0, NULL
    // 0xd41f44: b.ne            #0xd41f50
    // 0xd41f48: r1 = Null
    //     0xd41f48: mov             x1, NULL
    // 0xd41f4c: b               #0xd41f5c
    // 0xd41f50: str             x0, [SP]
    // 0xd41f54: r0 = runtimeType()
    //     0xd41f54: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd41f58: mov             x1, x0
    // 0xd41f5c: ldur            x0, [fp, #-0x10]
    // 0xd41f60: stur            x1, [fp, #-0x18]
    // 0xd41f64: cmp             w0, NULL
    // 0xd41f68: b.ne            #0xd41f78
    // 0xd41f6c: mov             x0, x1
    // 0xd41f70: r1 = Null
    //     0xd41f70: mov             x1, NULL
    // 0xd41f74: b               #0xd41f88
    // 0xd41f78: str             x0, [SP]
    // 0xd41f7c: r0 = runtimeType()
    //     0xd41f7c: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd41f80: mov             x1, x0
    // 0xd41f84: ldur            x0, [fp, #-0x18]
    // 0xd41f88: r2 = LoadClassIdInstr(r0)
    //     0xd41f88: ldur            x2, [x0, #-1]
    //     0xd41f8c: ubfx            x2, x2, #0xc, #0x14
    // 0xd41f90: stp             x1, x0, [SP]
    // 0xd41f94: mov             x0, x2
    // 0xd41f98: mov             lr, x0
    // 0xd41f9c: ldr             lr, [x21, lr, lsl #3]
    // 0xd41fa0: blr             lr
    // 0xd41fa4: tbz             w0, #4, #0xd41fb8
    // 0xd41fa8: r0 = false
    //     0xd41fa8: add             x0, NULL, #0x30  ; false
    // 0xd41fac: LeaveFrame
    //     0xd41fac: mov             SP, fp
    //     0xd41fb0: ldp             fp, lr, [SP], #0x10
    // 0xd41fb4: ret
    //     0xd41fb4: ret             
    // 0xd41fb8: ldur            x0, [fp, #-8]
    // 0xd41fbc: r1 = 60
    //     0xd41fbc: movz            x1, #0x3c
    // 0xd41fc0: branchIfSmi(r0, 0xd41fcc)
    //     0xd41fc0: tbz             w0, #0, #0xd41fcc
    // 0xd41fc4: r1 = LoadClassIdInstr(r0)
    //     0xd41fc4: ldur            x1, [x0, #-1]
    //     0xd41fc8: ubfx            x1, x1, #0xc, #0x14
    // 0xd41fcc: ldur            x16, [fp, #-0x10]
    // 0xd41fd0: stp             x16, x0, [SP]
    // 0xd41fd4: mov             x0, x1
    // 0xd41fd8: mov             lr, x0
    // 0xd41fdc: ldr             lr, [x21, lr, lsl #3]
    // 0xd41fe0: blr             lr
    // 0xd41fe4: tbz             w0, #4, #0xd41ff8
    // 0xd41fe8: r0 = false
    //     0xd41fe8: add             x0, NULL, #0x30  ; false
    // 0xd41fec: LeaveFrame
    //     0xd41fec: mov             SP, fp
    //     0xd41ff0: ldp             fp, lr, [SP], #0x10
    // 0xd41ff4: ret
    //     0xd41ff4: ret             
    // 0xd41ff8: r0 = true
    //     0xd41ff8: add             x0, NULL, #0x20  ; true
    // 0xd41ffc: LeaveFrame
    //     0xd41ffc: mov             SP, fp
    //     0xd42000: ldp             fp, lr, [SP], #0x10
    // 0xd42004: ret
    //     0xd42004: ret             
    // 0xd42008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd42008: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd4200c: b               #0xd41258
    // 0xd42010: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd42010: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd42014: b               #0xd416f4
  }
  static _ setEquals(/* No info */) {
    // ** addr: 0xd42018, size: 0x1d0
    // 0xd42018: EnterFrame
    //     0xd42018: stp             fp, lr, [SP, #-0x10]!
    //     0xd4201c: mov             fp, SP
    // 0xd42020: AllocStack(0x20)
    //     0xd42020: sub             SP, SP, #0x20
    // 0xd42024: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xd42024: stur            x1, [fp, #-8]
    //     0xd42028: mov             x16, x2
    //     0xd4202c: mov             x2, x1
    //     0xd42030: mov             x1, x16
    //     0xd42034: stur            x1, [fp, #-0x10]
    // 0xd42038: CheckStackOverflow
    //     0xd42038: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd4203c: cmp             SP, x16
    //     0xd42040: b.ls            #0xd421d8
    // 0xd42044: cmp             w2, w1
    // 0xd42048: b.ne            #0xd4205c
    // 0xd4204c: r0 = true
    //     0xd4204c: add             x0, NULL, #0x20  ; true
    // 0xd42050: LeaveFrame
    //     0xd42050: mov             SP, fp
    //     0xd42054: ldp             fp, lr, [SP], #0x10
    // 0xd42058: ret
    //     0xd42058: ret             
    // 0xd4205c: r0 = LoadClassIdInstr(r2)
    //     0xd4205c: ldur            x0, [x2, #-1]
    //     0xd42060: ubfx            x0, x0, #0xc, #0x14
    // 0xd42064: str             x2, [SP]
    // 0xd42068: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd42068: movz            x17, #0xc834
    //     0xd4206c: add             lr, x0, x17
    //     0xd42070: ldr             lr, [x21, lr, lsl #3]
    //     0xd42074: blr             lr
    // 0xd42078: mov             x2, x0
    // 0xd4207c: ldur            x1, [fp, #-0x10]
    // 0xd42080: stur            x2, [fp, #-0x18]
    // 0xd42084: r0 = LoadClassIdInstr(r1)
    //     0xd42084: ldur            x0, [x1, #-1]
    //     0xd42088: ubfx            x0, x0, #0xc, #0x14
    // 0xd4208c: str             x1, [SP]
    // 0xd42090: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd42090: movz            x17, #0xc834
    //     0xd42094: add             lr, x0, x17
    //     0xd42098: ldr             lr, [x21, lr, lsl #3]
    //     0xd4209c: blr             lr
    // 0xd420a0: mov             x1, x0
    // 0xd420a4: ldur            x0, [fp, #-0x18]
    // 0xd420a8: r2 = LoadInt32Instr(r0)
    //     0xd420a8: sbfx            x2, x0, #1, #0x1f
    //     0xd420ac: tbz             w0, #0, #0xd420b4
    //     0xd420b0: ldur            x2, [x0, #7]
    // 0xd420b4: r0 = LoadInt32Instr(r1)
    //     0xd420b4: sbfx            x0, x1, #1, #0x1f
    //     0xd420b8: tbz             w1, #0, #0xd420c0
    //     0xd420bc: ldur            x0, [x1, #7]
    // 0xd420c0: cmp             x2, x0
    // 0xd420c4: b.eq            #0xd420d8
    // 0xd420c8: r0 = false
    //     0xd420c8: add             x0, NULL, #0x30  ; false
    // 0xd420cc: LeaveFrame
    //     0xd420cc: mov             SP, fp
    //     0xd420d0: ldp             fp, lr, [SP], #0x10
    // 0xd420d4: ret
    //     0xd420d4: ret             
    // 0xd420d8: ldur            x1, [fp, #-8]
    // 0xd420dc: r0 = LoadClassIdInstr(r1)
    //     0xd420dc: ldur            x0, [x1, #-1]
    //     0xd420e0: ubfx            x0, x0, #0xc, #0x14
    // 0xd420e4: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xd420e4: movz            x17, #0xd35d
    //     0xd420e8: add             lr, x0, x17
    //     0xd420ec: ldr             lr, [x21, lr, lsl #3]
    //     0xd420f0: blr             lr
    // 0xd420f4: mov             x2, x0
    // 0xd420f8: stur            x2, [fp, #-8]
    // 0xd420fc: ldur            x3, [fp, #-0x10]
    // 0xd42100: CheckStackOverflow
    //     0xd42100: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd42104: cmp             SP, x16
    //     0xd42108: b.ls            #0xd421e0
    // 0xd4210c: r0 = LoadClassIdInstr(r2)
    //     0xd4210c: ldur            x0, [x2, #-1]
    //     0xd42110: ubfx            x0, x0, #0xc, #0x14
    // 0xd42114: mov             x1, x2
    // 0xd42118: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xd42118: movz            x17, #0x292d
    //     0xd4211c: movk            x17, #0x1, lsl #16
    //     0xd42120: add             lr, x0, x17
    //     0xd42124: ldr             lr, [x21, lr, lsl #3]
    //     0xd42128: blr             lr
    // 0xd4212c: tbnz            w0, #4, #0xd421c8
    // 0xd42130: ldur            x3, [fp, #-0x10]
    // 0xd42134: ldur            x2, [fp, #-8]
    // 0xd42138: r0 = LoadClassIdInstr(r2)
    //     0xd42138: ldur            x0, [x2, #-1]
    //     0xd4213c: ubfx            x0, x0, #0xc, #0x14
    // 0xd42140: mov             x1, x2
    // 0xd42144: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xd42144: movz            x17, #0x384d
    //     0xd42148: movk            x17, #0x1, lsl #16
    //     0xd4214c: add             lr, x0, x17
    //     0xd42150: ldr             lr, [x21, lr, lsl #3]
    //     0xd42154: blr             lr
    // 0xd42158: stur            x0, [fp, #-0x18]
    // 0xd4215c: r1 = 1
    //     0xd4215c: movz            x1, #0x1
    // 0xd42160: r0 = AllocateContext()
    //     0xd42160: bl              #0xec126c  ; AllocateContextStub
    // 0xd42164: mov             x1, x0
    // 0xd42168: ldur            x0, [fp, #-0x18]
    // 0xd4216c: StoreField: r1->field_f = r0
    //     0xd4216c: stur            w0, [x1, #0xf]
    // 0xd42170: mov             x2, x1
    // 0xd42174: r1 = Function '<anonymous closure>': static.
    //     0xd42174: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c168] AnonymousClosure: static (0xd421e8), in [package:equatable/src/equatable_utils.dart] ::setEquals (0xd42018)
    //     0xd42178: ldr             x1, [x1, #0x168]
    // 0xd4217c: r0 = AllocateClosure()
    //     0xd4217c: bl              #0xec1630  ; AllocateClosureStub
    // 0xd42180: ldur            x3, [fp, #-0x10]
    // 0xd42184: r1 = LoadClassIdInstr(r3)
    //     0xd42184: ldur            x1, [x3, #-1]
    //     0xd42188: ubfx            x1, x1, #0xc, #0x14
    // 0xd4218c: mov             x2, x0
    // 0xd42190: mov             x0, x1
    // 0xd42194: mov             x1, x3
    // 0xd42198: r0 = GDT[cid_x0 + 0x10bb9]()
    //     0xd42198: movz            x17, #0xbb9
    //     0xd4219c: movk            x17, #0x1, lsl #16
    //     0xd421a0: add             lr, x0, x17
    //     0xd421a4: ldr             lr, [x21, lr, lsl #3]
    //     0xd421a8: blr             lr
    // 0xd421ac: tbnz            w0, #4, #0xd421b8
    // 0xd421b0: ldur            x2, [fp, #-8]
    // 0xd421b4: b               #0xd420fc
    // 0xd421b8: r0 = false
    //     0xd421b8: add             x0, NULL, #0x30  ; false
    // 0xd421bc: LeaveFrame
    //     0xd421bc: mov             SP, fp
    //     0xd421c0: ldp             fp, lr, [SP], #0x10
    // 0xd421c4: ret
    //     0xd421c4: ret             
    // 0xd421c8: r0 = true
    //     0xd421c8: add             x0, NULL, #0x20  ; true
    // 0xd421cc: LeaveFrame
    //     0xd421cc: mov             SP, fp
    //     0xd421d0: ldp             fp, lr, [SP], #0x10
    // 0xd421d4: ret
    //     0xd421d4: ret             
    // 0xd421d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd421d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd421dc: b               #0xd42044
    // 0xd421e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd421e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd421e4: b               #0xd4210c
  }
  [closure] static bool <anonymous closure>(dynamic, Object?) {
    // ** addr: 0xd421e8, size: 0xdb0
    // 0xd421e8: EnterFrame
    //     0xd421e8: stp             fp, lr, [SP, #-0x10]!
    //     0xd421ec: mov             fp, SP
    // 0xd421f0: AllocStack(0x38)
    //     0xd421f0: sub             SP, SP, #0x38
    // 0xd421f4: SetupParameters()
    //     0xd421f4: ldr             x0, [fp, #0x18]
    //     0xd421f8: ldur            w1, [x0, #0x17]
    //     0xd421fc: add             x1, x1, HEAP, lsl #32
    // 0xd42200: CheckStackOverflow
    //     0xd42200: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd42204: cmp             SP, x16
    //     0xd42208: b.ls            #0xd42f88
    // 0xd4220c: LoadField: r2 = r1->field_f
    //     0xd4220c: ldur            w2, [x1, #0xf]
    // 0xd42210: DecompressPointer r2
    //     0xd42210: add             x2, x2, HEAP, lsl #32
    // 0xd42214: mov             x0, x2
    // 0xd42218: ldr             x1, [fp, #0x10]
    // 0xd4221c: stur            x2, [fp, #-8]
    // 0xd42220: stp             x1, x0, [SP, #-0x10]!
    // 0xd42224: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0xd42224: ldr             lr, [PP, #0x1200]  ; [pp+0x1200] Stub: OptimizedIdenticalWithNumberCheck (0x5f32bc)
    // 0xd42228: LoadField: r30 = r30->field_7
    //     0xd42228: ldur            lr, [lr, #7]
    // 0xd4222c: blr             lr
    // 0xd42230: ldp             x1, x0, [SP], #0x10
    // 0xd42234: b.ne            #0xd42240
    // 0xd42238: r0 = true
    //     0xd42238: add             x0, NULL, #0x20  ; true
    // 0xd4223c: b               #0xd42f7c
    // 0xd42240: ldur            x3, [fp, #-8]
    // 0xd42244: r0 = 60
    //     0xd42244: movz            x0, #0x3c
    // 0xd42248: branchIfSmi(r3, 0xd42254)
    //     0xd42248: tbz             w3, #0, #0xd42254
    // 0xd4224c: r0 = LoadClassIdInstr(r3)
    //     0xd4224c: ldur            x0, [x3, #-1]
    //     0xd42250: ubfx            x0, x0, #0xc, #0x14
    // 0xd42254: sub             x16, x0, #0x3c
    // 0xd42258: cmp             x16, #2
    // 0xd4225c: b.hi            #0xd422a4
    // 0xd42260: ldr             x4, [fp, #0x10]
    // 0xd42264: r1 = 60
    //     0xd42264: movz            x1, #0x3c
    // 0xd42268: branchIfSmi(r4, 0xd42274)
    //     0xd42268: tbz             w4, #0, #0xd42274
    // 0xd4226c: r1 = LoadClassIdInstr(r4)
    //     0xd4226c: ldur            x1, [x4, #-1]
    //     0xd42270: ubfx            x1, x1, #0xc, #0x14
    // 0xd42274: sub             x16, x1, #0x3c
    // 0xd42278: cmp             x16, #2
    // 0xd4227c: b.hi            #0xd422a8
    // 0xd42280: r0 = 60
    //     0xd42280: movz            x0, #0x3c
    // 0xd42284: branchIfSmi(r3, 0xd42290)
    //     0xd42284: tbz             w3, #0, #0xd42290
    // 0xd42288: r0 = LoadClassIdInstr(r3)
    //     0xd42288: ldur            x0, [x3, #-1]
    //     0xd4228c: ubfx            x0, x0, #0xc, #0x14
    // 0xd42290: stp             x4, x3, [SP]
    // 0xd42294: mov             lr, x0
    // 0xd42298: ldr             lr, [x21, lr, lsl #3]
    // 0xd4229c: blr             lr
    // 0xd422a0: b               #0xd42f7c
    // 0xd422a4: ldr             x4, [fp, #0x10]
    // 0xd422a8: r17 = -5561
    //     0xd422a8: movn            x17, #0x15b8
    // 0xd422ac: add             x16, x0, x17
    // 0xd422b0: cmp             x16, #0x2a
    // 0xd422b4: b.hi            #0xd422fc
    // 0xd422b8: r0 = 60
    //     0xd422b8: movz            x0, #0x3c
    // 0xd422bc: branchIfSmi(r4, 0xd422c8)
    //     0xd422bc: tbz             w4, #0, #0xd422c8
    // 0xd422c0: r0 = LoadClassIdInstr(r4)
    //     0xd422c0: ldur            x0, [x4, #-1]
    //     0xd422c4: ubfx            x0, x0, #0xc, #0x14
    // 0xd422c8: r17 = -5561
    //     0xd422c8: movn            x17, #0x15b8
    // 0xd422cc: add             x16, x0, x17
    // 0xd422d0: cmp             x16, #0x2a
    // 0xd422d4: b.hi            #0xd422fc
    // 0xd422d8: r0 = 60
    //     0xd422d8: movz            x0, #0x3c
    // 0xd422dc: branchIfSmi(r3, 0xd422e8)
    //     0xd422dc: tbz             w3, #0, #0xd422e8
    // 0xd422e0: r0 = LoadClassIdInstr(r3)
    //     0xd422e0: ldur            x0, [x3, #-1]
    //     0xd422e4: ubfx            x0, x0, #0xc, #0x14
    // 0xd422e8: stp             x4, x3, [SP]
    // 0xd422ec: mov             lr, x0
    // 0xd422f0: ldr             lr, [x21, lr, lsl #3]
    // 0xd422f4: blr             lr
    // 0xd422f8: b               #0xd42f7c
    // 0xd422fc: mov             x0, x3
    // 0xd42300: r2 = Null
    //     0xd42300: mov             x2, NULL
    // 0xd42304: r1 = Null
    //     0xd42304: mov             x1, NULL
    // 0xd42308: cmp             w0, NULL
    // 0xd4230c: b.eq            #0xd423a4
    // 0xd42310: branchIfSmi(r0, 0xd423a4)
    //     0xd42310: tbz             w0, #0, #0xd423a4
    // 0xd42314: r3 = LoadClassIdInstr(r0)
    //     0xd42314: ldur            x3, [x0, #-1]
    //     0xd42318: ubfx            x3, x3, #0xc, #0x14
    // 0xd4231c: r17 = 6713
    //     0xd4231c: movz            x17, #0x1a39
    // 0xd42320: cmp             x3, x17
    // 0xd42324: b.eq            #0xd423ac
    // 0xd42328: r4 = LoadClassIdInstr(r0)
    //     0xd42328: ldur            x4, [x0, #-1]
    //     0xd4232c: ubfx            x4, x4, #0xc, #0x14
    // 0xd42330: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd42334: ldr             x3, [x3, #0x18]
    // 0xd42338: ldr             x3, [x3, x4, lsl #3]
    // 0xd4233c: LoadField: r3 = r3->field_2b
    //     0xd4233c: ldur            w3, [x3, #0x2b]
    // 0xd42340: DecompressPointer r3
    //     0xd42340: add             x3, x3, HEAP, lsl #32
    // 0xd42344: cmp             w3, NULL
    // 0xd42348: b.eq            #0xd423a4
    // 0xd4234c: LoadField: r3 = r3->field_f
    //     0xd4234c: ldur            w3, [x3, #0xf]
    // 0xd42350: lsr             x3, x3, #3
    // 0xd42354: r17 = 6713
    //     0xd42354: movz            x17, #0x1a39
    // 0xd42358: cmp             x3, x17
    // 0xd4235c: b.eq            #0xd423ac
    // 0xd42360: r3 = SubtypeTestCache
    //     0xd42360: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c170] SubtypeTestCache
    //     0xd42364: ldr             x3, [x3, #0x170]
    // 0xd42368: r30 = Subtype1TestCacheStub
    //     0xd42368: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd4236c: LoadField: r30 = r30->field_7
    //     0xd4236c: ldur            lr, [lr, #7]
    // 0xd42370: blr             lr
    // 0xd42374: cmp             w7, NULL
    // 0xd42378: b.eq            #0xd42384
    // 0xd4237c: tbnz            w7, #4, #0xd423a4
    // 0xd42380: b               #0xd423ac
    // 0xd42384: r8 = Set
    //     0xd42384: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c178] Type: Set
    //     0xd42388: ldr             x8, [x8, #0x178]
    // 0xd4238c: r3 = SubtypeTestCache
    //     0xd4238c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c180] SubtypeTestCache
    //     0xd42390: ldr             x3, [x3, #0x180]
    // 0xd42394: r30 = InstanceOfStub
    //     0xd42394: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd42398: LoadField: r30 = r30->field_7
    //     0xd42398: ldur            lr, [lr, #7]
    // 0xd4239c: blr             lr
    // 0xd423a0: b               #0xd423b0
    // 0xd423a4: r0 = false
    //     0xd423a4: add             x0, NULL, #0x30  ; false
    // 0xd423a8: b               #0xd423b0
    // 0xd423ac: r0 = true
    //     0xd423ac: add             x0, NULL, #0x20  ; true
    // 0xd423b0: tbnz            w0, #4, #0xd4247c
    // 0xd423b4: ldr             x0, [fp, #0x10]
    // 0xd423b8: r2 = Null
    //     0xd423b8: mov             x2, NULL
    // 0xd423bc: r1 = Null
    //     0xd423bc: mov             x1, NULL
    // 0xd423c0: cmp             w0, NULL
    // 0xd423c4: b.eq            #0xd4245c
    // 0xd423c8: branchIfSmi(r0, 0xd4245c)
    //     0xd423c8: tbz             w0, #0, #0xd4245c
    // 0xd423cc: r3 = LoadClassIdInstr(r0)
    //     0xd423cc: ldur            x3, [x0, #-1]
    //     0xd423d0: ubfx            x3, x3, #0xc, #0x14
    // 0xd423d4: r17 = 6713
    //     0xd423d4: movz            x17, #0x1a39
    // 0xd423d8: cmp             x3, x17
    // 0xd423dc: b.eq            #0xd42464
    // 0xd423e0: r4 = LoadClassIdInstr(r0)
    //     0xd423e0: ldur            x4, [x0, #-1]
    //     0xd423e4: ubfx            x4, x4, #0xc, #0x14
    // 0xd423e8: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd423ec: ldr             x3, [x3, #0x18]
    // 0xd423f0: ldr             x3, [x3, x4, lsl #3]
    // 0xd423f4: LoadField: r3 = r3->field_2b
    //     0xd423f4: ldur            w3, [x3, #0x2b]
    // 0xd423f8: DecompressPointer r3
    //     0xd423f8: add             x3, x3, HEAP, lsl #32
    // 0xd423fc: cmp             w3, NULL
    // 0xd42400: b.eq            #0xd4245c
    // 0xd42404: LoadField: r3 = r3->field_f
    //     0xd42404: ldur            w3, [x3, #0xf]
    // 0xd42408: lsr             x3, x3, #3
    // 0xd4240c: r17 = 6713
    //     0xd4240c: movz            x17, #0x1a39
    // 0xd42410: cmp             x3, x17
    // 0xd42414: b.eq            #0xd42464
    // 0xd42418: r3 = SubtypeTestCache
    //     0xd42418: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c188] SubtypeTestCache
    //     0xd4241c: ldr             x3, [x3, #0x188]
    // 0xd42420: r30 = Subtype1TestCacheStub
    //     0xd42420: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd42424: LoadField: r30 = r30->field_7
    //     0xd42424: ldur            lr, [lr, #7]
    // 0xd42428: blr             lr
    // 0xd4242c: cmp             w7, NULL
    // 0xd42430: b.eq            #0xd4243c
    // 0xd42434: tbnz            w7, #4, #0xd4245c
    // 0xd42438: b               #0xd42464
    // 0xd4243c: r8 = Set
    //     0xd4243c: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c190] Type: Set
    //     0xd42440: ldr             x8, [x8, #0x190]
    // 0xd42444: r3 = SubtypeTestCache
    //     0xd42444: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c198] SubtypeTestCache
    //     0xd42448: ldr             x3, [x3, #0x198]
    // 0xd4244c: r30 = InstanceOfStub
    //     0xd4244c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd42450: LoadField: r30 = r30->field_7
    //     0xd42450: ldur            lr, [lr, #7]
    // 0xd42454: blr             lr
    // 0xd42458: b               #0xd42468
    // 0xd4245c: r0 = false
    //     0xd4245c: add             x0, NULL, #0x30  ; false
    // 0xd42460: b               #0xd42468
    // 0xd42464: r0 = true
    //     0xd42464: add             x0, NULL, #0x20  ; true
    // 0xd42468: tbnz            w0, #4, #0xd4247c
    // 0xd4246c: ldur            x1, [fp, #-8]
    // 0xd42470: ldr             x2, [fp, #0x10]
    // 0xd42474: r0 = setEquals()
    //     0xd42474: bl              #0xd42018  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0xd42478: b               #0xd42f7c
    // 0xd4247c: ldur            x0, [fp, #-8]
    // 0xd42480: r2 = Null
    //     0xd42480: mov             x2, NULL
    // 0xd42484: r1 = Null
    //     0xd42484: mov             x1, NULL
    // 0xd42488: cmp             w0, NULL
    // 0xd4248c: b.eq            #0xd42524
    // 0xd42490: branchIfSmi(r0, 0xd42524)
    //     0xd42490: tbz             w0, #0, #0xd42524
    // 0xd42494: r3 = LoadClassIdInstr(r0)
    //     0xd42494: ldur            x3, [x0, #-1]
    //     0xd42498: ubfx            x3, x3, #0xc, #0x14
    // 0xd4249c: r17 = 7205
    //     0xd4249c: movz            x17, #0x1c25
    // 0xd424a0: cmp             x3, x17
    // 0xd424a4: b.eq            #0xd4252c
    // 0xd424a8: r4 = LoadClassIdInstr(r0)
    //     0xd424a8: ldur            x4, [x0, #-1]
    //     0xd424ac: ubfx            x4, x4, #0xc, #0x14
    // 0xd424b0: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd424b4: ldr             x3, [x3, #0x18]
    // 0xd424b8: ldr             x3, [x3, x4, lsl #3]
    // 0xd424bc: LoadField: r3 = r3->field_2b
    //     0xd424bc: ldur            w3, [x3, #0x2b]
    // 0xd424c0: DecompressPointer r3
    //     0xd424c0: add             x3, x3, HEAP, lsl #32
    // 0xd424c4: cmp             w3, NULL
    // 0xd424c8: b.eq            #0xd42524
    // 0xd424cc: LoadField: r3 = r3->field_f
    //     0xd424cc: ldur            w3, [x3, #0xf]
    // 0xd424d0: lsr             x3, x3, #3
    // 0xd424d4: r17 = 7205
    //     0xd424d4: movz            x17, #0x1c25
    // 0xd424d8: cmp             x3, x17
    // 0xd424dc: b.eq            #0xd4252c
    // 0xd424e0: r3 = SubtypeTestCache
    //     0xd424e0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c1a0] SubtypeTestCache
    //     0xd424e4: ldr             x3, [x3, #0x1a0]
    // 0xd424e8: r30 = Subtype1TestCacheStub
    //     0xd424e8: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd424ec: LoadField: r30 = r30->field_7
    //     0xd424ec: ldur            lr, [lr, #7]
    // 0xd424f0: blr             lr
    // 0xd424f4: cmp             w7, NULL
    // 0xd424f8: b.eq            #0xd42504
    // 0xd424fc: tbnz            w7, #4, #0xd42524
    // 0xd42500: b               #0xd4252c
    // 0xd42504: r8 = Iterable
    //     0xd42504: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c1a8] Type: Iterable
    //     0xd42508: ldr             x8, [x8, #0x1a8]
    // 0xd4250c: r3 = SubtypeTestCache
    //     0xd4250c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c1b0] SubtypeTestCache
    //     0xd42510: ldr             x3, [x3, #0x1b0]
    // 0xd42514: r30 = InstanceOfStub
    //     0xd42514: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd42518: LoadField: r30 = r30->field_7
    //     0xd42518: ldur            lr, [lr, #7]
    // 0xd4251c: blr             lr
    // 0xd42520: b               #0xd42530
    // 0xd42524: r0 = false
    //     0xd42524: add             x0, NULL, #0x30  ; false
    // 0xd42528: b               #0xd42530
    // 0xd4252c: r0 = true
    //     0xd4252c: add             x0, NULL, #0x20  ; true
    // 0xd42530: tbnz            w0, #4, #0xd42d4c
    // 0xd42534: ldr             x0, [fp, #0x10]
    // 0xd42538: r2 = Null
    //     0xd42538: mov             x2, NULL
    // 0xd4253c: r1 = Null
    //     0xd4253c: mov             x1, NULL
    // 0xd42540: cmp             w0, NULL
    // 0xd42544: b.eq            #0xd425dc
    // 0xd42548: branchIfSmi(r0, 0xd425dc)
    //     0xd42548: tbz             w0, #0, #0xd425dc
    // 0xd4254c: r3 = LoadClassIdInstr(r0)
    //     0xd4254c: ldur            x3, [x0, #-1]
    //     0xd42550: ubfx            x3, x3, #0xc, #0x14
    // 0xd42554: r17 = 7205
    //     0xd42554: movz            x17, #0x1c25
    // 0xd42558: cmp             x3, x17
    // 0xd4255c: b.eq            #0xd425e4
    // 0xd42560: r4 = LoadClassIdInstr(r0)
    //     0xd42560: ldur            x4, [x0, #-1]
    //     0xd42564: ubfx            x4, x4, #0xc, #0x14
    // 0xd42568: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd4256c: ldr             x3, [x3, #0x18]
    // 0xd42570: ldr             x3, [x3, x4, lsl #3]
    // 0xd42574: LoadField: r3 = r3->field_2b
    //     0xd42574: ldur            w3, [x3, #0x2b]
    // 0xd42578: DecompressPointer r3
    //     0xd42578: add             x3, x3, HEAP, lsl #32
    // 0xd4257c: cmp             w3, NULL
    // 0xd42580: b.eq            #0xd425dc
    // 0xd42584: LoadField: r3 = r3->field_f
    //     0xd42584: ldur            w3, [x3, #0xf]
    // 0xd42588: lsr             x3, x3, #3
    // 0xd4258c: r17 = 7205
    //     0xd4258c: movz            x17, #0x1c25
    // 0xd42590: cmp             x3, x17
    // 0xd42594: b.eq            #0xd425e4
    // 0xd42598: r3 = SubtypeTestCache
    //     0xd42598: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c1b8] SubtypeTestCache
    //     0xd4259c: ldr             x3, [x3, #0x1b8]
    // 0xd425a0: r30 = Subtype1TestCacheStub
    //     0xd425a0: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd425a4: LoadField: r30 = r30->field_7
    //     0xd425a4: ldur            lr, [lr, #7]
    // 0xd425a8: blr             lr
    // 0xd425ac: cmp             w7, NULL
    // 0xd425b0: b.eq            #0xd425bc
    // 0xd425b4: tbnz            w7, #4, #0xd425dc
    // 0xd425b8: b               #0xd425e4
    // 0xd425bc: r8 = Iterable
    //     0xd425bc: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c1c0] Type: Iterable
    //     0xd425c0: ldr             x8, [x8, #0x1c0]
    // 0xd425c4: r3 = SubtypeTestCache
    //     0xd425c4: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c1c8] SubtypeTestCache
    //     0xd425c8: ldr             x3, [x3, #0x1c8]
    // 0xd425cc: r30 = InstanceOfStub
    //     0xd425cc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd425d0: LoadField: r30 = r30->field_7
    //     0xd425d0: ldur            lr, [lr, #7]
    // 0xd425d4: blr             lr
    // 0xd425d8: b               #0xd425e8
    // 0xd425dc: r0 = false
    //     0xd425dc: add             x0, NULL, #0x30  ; false
    // 0xd425e0: b               #0xd425e8
    // 0xd425e4: r0 = true
    //     0xd425e4: add             x0, NULL, #0x20  ; true
    // 0xd425e8: tbnz            w0, #4, #0xd42d4c
    // 0xd425ec: ldr             x2, [fp, #0x10]
    // 0xd425f0: ldur            x1, [fp, #-8]
    // 0xd425f4: cmp             w1, w2
    // 0xd425f8: b.ne            #0xd42604
    // 0xd425fc: r0 = true
    //     0xd425fc: add             x0, NULL, #0x20  ; true
    // 0xd42600: b               #0xd42f7c
    // 0xd42604: r0 = LoadClassIdInstr(r1)
    //     0xd42604: ldur            x0, [x1, #-1]
    //     0xd42608: ubfx            x0, x0, #0xc, #0x14
    // 0xd4260c: str             x1, [SP]
    // 0xd42610: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd42610: movz            x17, #0xc834
    //     0xd42614: add             lr, x0, x17
    //     0xd42618: ldr             lr, [x21, lr, lsl #3]
    //     0xd4261c: blr             lr
    // 0xd42620: mov             x2, x0
    // 0xd42624: ldr             x1, [fp, #0x10]
    // 0xd42628: stur            x2, [fp, #-0x10]
    // 0xd4262c: r0 = LoadClassIdInstr(r1)
    //     0xd4262c: ldur            x0, [x1, #-1]
    //     0xd42630: ubfx            x0, x0, #0xc, #0x14
    // 0xd42634: str             x1, [SP]
    // 0xd42638: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd42638: movz            x17, #0xc834
    //     0xd4263c: add             lr, x0, x17
    //     0xd42640: ldr             lr, [x21, lr, lsl #3]
    //     0xd42644: blr             lr
    // 0xd42648: mov             x1, x0
    // 0xd4264c: ldur            x0, [fp, #-0x10]
    // 0xd42650: r2 = LoadInt32Instr(r0)
    //     0xd42650: sbfx            x2, x0, #1, #0x1f
    //     0xd42654: tbz             w0, #0, #0xd4265c
    //     0xd42658: ldur            x2, [x0, #7]
    // 0xd4265c: r0 = LoadInt32Instr(r1)
    //     0xd4265c: sbfx            x0, x1, #1, #0x1f
    //     0xd42660: tbz             w1, #0, #0xd42668
    //     0xd42664: ldur            x0, [x1, #7]
    // 0xd42668: cmp             x2, x0
    // 0xd4266c: b.eq            #0xd42678
    // 0xd42670: r0 = false
    //     0xd42670: add             x0, NULL, #0x30  ; false
    // 0xd42674: b               #0xd42f7c
    // 0xd42678: r3 = 0
    //     0xd42678: movz            x3, #0
    // 0xd4267c: ldr             x1, [fp, #0x10]
    // 0xd42680: ldur            x2, [fp, #-8]
    // 0xd42684: stur            x3, [fp, #-0x18]
    // 0xd42688: CheckStackOverflow
    //     0xd42688: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd4268c: cmp             SP, x16
    //     0xd42690: b.ls            #0xd42f90
    // 0xd42694: r0 = LoadClassIdInstr(r2)
    //     0xd42694: ldur            x0, [x2, #-1]
    //     0xd42698: ubfx            x0, x0, #0xc, #0x14
    // 0xd4269c: str             x2, [SP]
    // 0xd426a0: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd426a0: movz            x17, #0xc834
    //     0xd426a4: add             lr, x0, x17
    //     0xd426a8: ldr             lr, [x21, lr, lsl #3]
    //     0xd426ac: blr             lr
    // 0xd426b0: r1 = LoadInt32Instr(r0)
    //     0xd426b0: sbfx            x1, x0, #1, #0x1f
    //     0xd426b4: tbz             w0, #0, #0xd426bc
    //     0xd426b8: ldur            x1, [x0, #7]
    // 0xd426bc: ldur            x3, [fp, #-0x18]
    // 0xd426c0: cmp             x3, x1
    // 0xd426c4: b.ge            #0xd42d44
    // 0xd426c8: ldr             x4, [fp, #0x10]
    // 0xd426cc: ldur            x5, [fp, #-8]
    // 0xd426d0: r0 = LoadClassIdInstr(r5)
    //     0xd426d0: ldur            x0, [x5, #-1]
    //     0xd426d4: ubfx            x0, x0, #0xc, #0x14
    // 0xd426d8: mov             x1, x5
    // 0xd426dc: mov             x2, x3
    // 0xd426e0: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xd426e0: movz            x17, #0xd28f
    //     0xd426e4: add             lr, x0, x17
    //     0xd426e8: ldr             lr, [x21, lr, lsl #3]
    //     0xd426ec: blr             lr
    // 0xd426f0: mov             x4, x0
    // 0xd426f4: ldr             x3, [fp, #0x10]
    // 0xd426f8: stur            x4, [fp, #-0x10]
    // 0xd426fc: r0 = LoadClassIdInstr(r3)
    //     0xd426fc: ldur            x0, [x3, #-1]
    //     0xd42700: ubfx            x0, x0, #0xc, #0x14
    // 0xd42704: mov             x1, x3
    // 0xd42708: ldur            x2, [fp, #-0x18]
    // 0xd4270c: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xd4270c: movz            x17, #0xd28f
    //     0xd42710: add             lr, x0, x17
    //     0xd42714: ldr             lr, [x21, lr, lsl #3]
    //     0xd42718: blr             lr
    // 0xd4271c: mov             x1, x0
    // 0xd42720: mov             x2, x0
    // 0xd42724: ldur            x0, [fp, #-0x10]
    // 0xd42728: stur            x2, [fp, #-0x20]
    // 0xd4272c: stp             x1, x0, [SP, #-0x10]!
    // 0xd42730: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0xd42730: ldr             lr, [PP, #0x1200]  ; [pp+0x1200] Stub: OptimizedIdenticalWithNumberCheck (0x5f32bc)
    // 0xd42734: LoadField: r30 = r30->field_7
    //     0xd42734: ldur            lr, [lr, #7]
    // 0xd42738: blr             lr
    // 0xd4273c: ldp             x1, x0, [SP], #0x10
    // 0xd42740: b.eq            #0xd42d30
    // 0xd42744: ldur            x3, [fp, #-0x10]
    // 0xd42748: r0 = 60
    //     0xd42748: movz            x0, #0x3c
    // 0xd4274c: branchIfSmi(r3, 0xd42758)
    //     0xd4274c: tbz             w3, #0, #0xd42758
    // 0xd42750: r0 = LoadClassIdInstr(r3)
    //     0xd42750: ldur            x0, [x3, #-1]
    //     0xd42754: ubfx            x0, x0, #0xc, #0x14
    // 0xd42758: sub             x16, x0, #0x3c
    // 0xd4275c: cmp             x16, #2
    // 0xd42760: b.hi            #0xd427ac
    // 0xd42764: ldur            x4, [fp, #-0x20]
    // 0xd42768: r1 = 60
    //     0xd42768: movz            x1, #0x3c
    // 0xd4276c: branchIfSmi(r4, 0xd42778)
    //     0xd4276c: tbz             w4, #0, #0xd42778
    // 0xd42770: r1 = LoadClassIdInstr(r4)
    //     0xd42770: ldur            x1, [x4, #-1]
    //     0xd42774: ubfx            x1, x1, #0xc, #0x14
    // 0xd42778: sub             x16, x1, #0x3c
    // 0xd4277c: cmp             x16, #2
    // 0xd42780: b.hi            #0xd427b0
    // 0xd42784: r0 = 60
    //     0xd42784: movz            x0, #0x3c
    // 0xd42788: branchIfSmi(r3, 0xd42794)
    //     0xd42788: tbz             w3, #0, #0xd42794
    // 0xd4278c: r0 = LoadClassIdInstr(r3)
    //     0xd4278c: ldur            x0, [x3, #-1]
    //     0xd42790: ubfx            x0, x0, #0xc, #0x14
    // 0xd42794: stp             x4, x3, [SP]
    // 0xd42798: mov             lr, x0
    // 0xd4279c: ldr             lr, [x21, lr, lsl #3]
    // 0xd427a0: blr             lr
    // 0xd427a4: tbz             w0, #4, #0xd42d30
    // 0xd427a8: b               #0xd42d3c
    // 0xd427ac: ldur            x4, [fp, #-0x20]
    // 0xd427b0: r17 = -5561
    //     0xd427b0: movn            x17, #0x15b8
    // 0xd427b4: add             x16, x0, x17
    // 0xd427b8: cmp             x16, #0x2a
    // 0xd427bc: b.hi            #0xd42808
    // 0xd427c0: r0 = 60
    //     0xd427c0: movz            x0, #0x3c
    // 0xd427c4: branchIfSmi(r4, 0xd427d0)
    //     0xd427c4: tbz             w4, #0, #0xd427d0
    // 0xd427c8: r0 = LoadClassIdInstr(r4)
    //     0xd427c8: ldur            x0, [x4, #-1]
    //     0xd427cc: ubfx            x0, x0, #0xc, #0x14
    // 0xd427d0: r17 = -5561
    //     0xd427d0: movn            x17, #0x15b8
    // 0xd427d4: add             x16, x0, x17
    // 0xd427d8: cmp             x16, #0x2a
    // 0xd427dc: b.hi            #0xd42808
    // 0xd427e0: r0 = 60
    //     0xd427e0: movz            x0, #0x3c
    // 0xd427e4: branchIfSmi(r3, 0xd427f0)
    //     0xd427e4: tbz             w3, #0, #0xd427f0
    // 0xd427e8: r0 = LoadClassIdInstr(r3)
    //     0xd427e8: ldur            x0, [x3, #-1]
    //     0xd427ec: ubfx            x0, x0, #0xc, #0x14
    // 0xd427f0: stp             x4, x3, [SP]
    // 0xd427f4: mov             lr, x0
    // 0xd427f8: ldr             lr, [x21, lr, lsl #3]
    // 0xd427fc: blr             lr
    // 0xd42800: tbz             w0, #4, #0xd42d30
    // 0xd42804: b               #0xd42d3c
    // 0xd42808: mov             x0, x3
    // 0xd4280c: r2 = Null
    //     0xd4280c: mov             x2, NULL
    // 0xd42810: r1 = Null
    //     0xd42810: mov             x1, NULL
    // 0xd42814: cmp             w0, NULL
    // 0xd42818: b.eq            #0xd428b0
    // 0xd4281c: branchIfSmi(r0, 0xd428b0)
    //     0xd4281c: tbz             w0, #0, #0xd428b0
    // 0xd42820: r3 = LoadClassIdInstr(r0)
    //     0xd42820: ldur            x3, [x0, #-1]
    //     0xd42824: ubfx            x3, x3, #0xc, #0x14
    // 0xd42828: r17 = 6713
    //     0xd42828: movz            x17, #0x1a39
    // 0xd4282c: cmp             x3, x17
    // 0xd42830: b.eq            #0xd428b8
    // 0xd42834: r4 = LoadClassIdInstr(r0)
    //     0xd42834: ldur            x4, [x0, #-1]
    //     0xd42838: ubfx            x4, x4, #0xc, #0x14
    // 0xd4283c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd42840: ldr             x3, [x3, #0x18]
    // 0xd42844: ldr             x3, [x3, x4, lsl #3]
    // 0xd42848: LoadField: r3 = r3->field_2b
    //     0xd42848: ldur            w3, [x3, #0x2b]
    // 0xd4284c: DecompressPointer r3
    //     0xd4284c: add             x3, x3, HEAP, lsl #32
    // 0xd42850: cmp             w3, NULL
    // 0xd42854: b.eq            #0xd428b0
    // 0xd42858: LoadField: r3 = r3->field_f
    //     0xd42858: ldur            w3, [x3, #0xf]
    // 0xd4285c: lsr             x3, x3, #3
    // 0xd42860: r17 = 6713
    //     0xd42860: movz            x17, #0x1a39
    // 0xd42864: cmp             x3, x17
    // 0xd42868: b.eq            #0xd428b8
    // 0xd4286c: r3 = SubtypeTestCache
    //     0xd4286c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c1d0] SubtypeTestCache
    //     0xd42870: ldr             x3, [x3, #0x1d0]
    // 0xd42874: r30 = Subtype1TestCacheStub
    //     0xd42874: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd42878: LoadField: r30 = r30->field_7
    //     0xd42878: ldur            lr, [lr, #7]
    // 0xd4287c: blr             lr
    // 0xd42880: cmp             w7, NULL
    // 0xd42884: b.eq            #0xd42890
    // 0xd42888: tbnz            w7, #4, #0xd428b0
    // 0xd4288c: b               #0xd428b8
    // 0xd42890: r8 = Set
    //     0xd42890: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c1d8] Type: Set
    //     0xd42894: ldr             x8, [x8, #0x1d8]
    // 0xd42898: r3 = SubtypeTestCache
    //     0xd42898: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c1e0] SubtypeTestCache
    //     0xd4289c: ldr             x3, [x3, #0x1e0]
    // 0xd428a0: r30 = InstanceOfStub
    //     0xd428a0: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd428a4: LoadField: r30 = r30->field_7
    //     0xd428a4: ldur            lr, [lr, #7]
    // 0xd428a8: blr             lr
    // 0xd428ac: b               #0xd428bc
    // 0xd428b0: r0 = false
    //     0xd428b0: add             x0, NULL, #0x30  ; false
    // 0xd428b4: b               #0xd428bc
    // 0xd428b8: r0 = true
    //     0xd428b8: add             x0, NULL, #0x20  ; true
    // 0xd428bc: tbnz            w0, #4, #0xd4298c
    // 0xd428c0: ldur            x0, [fp, #-0x20]
    // 0xd428c4: r2 = Null
    //     0xd428c4: mov             x2, NULL
    // 0xd428c8: r1 = Null
    //     0xd428c8: mov             x1, NULL
    // 0xd428cc: cmp             w0, NULL
    // 0xd428d0: b.eq            #0xd42968
    // 0xd428d4: branchIfSmi(r0, 0xd42968)
    //     0xd428d4: tbz             w0, #0, #0xd42968
    // 0xd428d8: r3 = LoadClassIdInstr(r0)
    //     0xd428d8: ldur            x3, [x0, #-1]
    //     0xd428dc: ubfx            x3, x3, #0xc, #0x14
    // 0xd428e0: r17 = 6713
    //     0xd428e0: movz            x17, #0x1a39
    // 0xd428e4: cmp             x3, x17
    // 0xd428e8: b.eq            #0xd42970
    // 0xd428ec: r4 = LoadClassIdInstr(r0)
    //     0xd428ec: ldur            x4, [x0, #-1]
    //     0xd428f0: ubfx            x4, x4, #0xc, #0x14
    // 0xd428f4: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd428f8: ldr             x3, [x3, #0x18]
    // 0xd428fc: ldr             x3, [x3, x4, lsl #3]
    // 0xd42900: LoadField: r3 = r3->field_2b
    //     0xd42900: ldur            w3, [x3, #0x2b]
    // 0xd42904: DecompressPointer r3
    //     0xd42904: add             x3, x3, HEAP, lsl #32
    // 0xd42908: cmp             w3, NULL
    // 0xd4290c: b.eq            #0xd42968
    // 0xd42910: LoadField: r3 = r3->field_f
    //     0xd42910: ldur            w3, [x3, #0xf]
    // 0xd42914: lsr             x3, x3, #3
    // 0xd42918: r17 = 6713
    //     0xd42918: movz            x17, #0x1a39
    // 0xd4291c: cmp             x3, x17
    // 0xd42920: b.eq            #0xd42970
    // 0xd42924: r3 = SubtypeTestCache
    //     0xd42924: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c1e8] SubtypeTestCache
    //     0xd42928: ldr             x3, [x3, #0x1e8]
    // 0xd4292c: r30 = Subtype1TestCacheStub
    //     0xd4292c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd42930: LoadField: r30 = r30->field_7
    //     0xd42930: ldur            lr, [lr, #7]
    // 0xd42934: blr             lr
    // 0xd42938: cmp             w7, NULL
    // 0xd4293c: b.eq            #0xd42948
    // 0xd42940: tbnz            w7, #4, #0xd42968
    // 0xd42944: b               #0xd42970
    // 0xd42948: r8 = Set
    //     0xd42948: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c1f0] Type: Set
    //     0xd4294c: ldr             x8, [x8, #0x1f0]
    // 0xd42950: r3 = SubtypeTestCache
    //     0xd42950: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c1f8] SubtypeTestCache
    //     0xd42954: ldr             x3, [x3, #0x1f8]
    // 0xd42958: r30 = InstanceOfStub
    //     0xd42958: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd4295c: LoadField: r30 = r30->field_7
    //     0xd4295c: ldur            lr, [lr, #7]
    // 0xd42960: blr             lr
    // 0xd42964: b               #0xd42974
    // 0xd42968: r0 = false
    //     0xd42968: add             x0, NULL, #0x30  ; false
    // 0xd4296c: b               #0xd42974
    // 0xd42970: r0 = true
    //     0xd42970: add             x0, NULL, #0x20  ; true
    // 0xd42974: tbnz            w0, #4, #0xd4298c
    // 0xd42978: ldur            x1, [fp, #-0x10]
    // 0xd4297c: ldur            x2, [fp, #-0x20]
    // 0xd42980: r0 = setEquals()
    //     0xd42980: bl              #0xd42018  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0xd42984: tbz             w0, #4, #0xd42d30
    // 0xd42988: b               #0xd42d3c
    // 0xd4298c: ldur            x0, [fp, #-0x10]
    // 0xd42990: r2 = Null
    //     0xd42990: mov             x2, NULL
    // 0xd42994: r1 = Null
    //     0xd42994: mov             x1, NULL
    // 0xd42998: cmp             w0, NULL
    // 0xd4299c: b.eq            #0xd42a34
    // 0xd429a0: branchIfSmi(r0, 0xd42a34)
    //     0xd429a0: tbz             w0, #0, #0xd42a34
    // 0xd429a4: r3 = LoadClassIdInstr(r0)
    //     0xd429a4: ldur            x3, [x0, #-1]
    //     0xd429a8: ubfx            x3, x3, #0xc, #0x14
    // 0xd429ac: r17 = 7205
    //     0xd429ac: movz            x17, #0x1c25
    // 0xd429b0: cmp             x3, x17
    // 0xd429b4: b.eq            #0xd42a3c
    // 0xd429b8: r4 = LoadClassIdInstr(r0)
    //     0xd429b8: ldur            x4, [x0, #-1]
    //     0xd429bc: ubfx            x4, x4, #0xc, #0x14
    // 0xd429c0: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd429c4: ldr             x3, [x3, #0x18]
    // 0xd429c8: ldr             x3, [x3, x4, lsl #3]
    // 0xd429cc: LoadField: r3 = r3->field_2b
    //     0xd429cc: ldur            w3, [x3, #0x2b]
    // 0xd429d0: DecompressPointer r3
    //     0xd429d0: add             x3, x3, HEAP, lsl #32
    // 0xd429d4: cmp             w3, NULL
    // 0xd429d8: b.eq            #0xd42a34
    // 0xd429dc: LoadField: r3 = r3->field_f
    //     0xd429dc: ldur            w3, [x3, #0xf]
    // 0xd429e0: lsr             x3, x3, #3
    // 0xd429e4: r17 = 7205
    //     0xd429e4: movz            x17, #0x1c25
    // 0xd429e8: cmp             x3, x17
    // 0xd429ec: b.eq            #0xd42a3c
    // 0xd429f0: r3 = SubtypeTestCache
    //     0xd429f0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c200] SubtypeTestCache
    //     0xd429f4: ldr             x3, [x3, #0x200]
    // 0xd429f8: r30 = Subtype1TestCacheStub
    //     0xd429f8: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd429fc: LoadField: r30 = r30->field_7
    //     0xd429fc: ldur            lr, [lr, #7]
    // 0xd42a00: blr             lr
    // 0xd42a04: cmp             w7, NULL
    // 0xd42a08: b.eq            #0xd42a14
    // 0xd42a0c: tbnz            w7, #4, #0xd42a34
    // 0xd42a10: b               #0xd42a3c
    // 0xd42a14: r8 = Iterable
    //     0xd42a14: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c208] Type: Iterable
    //     0xd42a18: ldr             x8, [x8, #0x208]
    // 0xd42a1c: r3 = SubtypeTestCache
    //     0xd42a1c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c210] SubtypeTestCache
    //     0xd42a20: ldr             x3, [x3, #0x210]
    // 0xd42a24: r30 = InstanceOfStub
    //     0xd42a24: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd42a28: LoadField: r30 = r30->field_7
    //     0xd42a28: ldur            lr, [lr, #7]
    // 0xd42a2c: blr             lr
    // 0xd42a30: b               #0xd42a40
    // 0xd42a34: r0 = false
    //     0xd42a34: add             x0, NULL, #0x30  ; false
    // 0xd42a38: b               #0xd42a40
    // 0xd42a3c: r0 = true
    //     0xd42a3c: add             x0, NULL, #0x20  ; true
    // 0xd42a40: tbnz            w0, #4, #0xd42b10
    // 0xd42a44: ldur            x0, [fp, #-0x20]
    // 0xd42a48: r2 = Null
    //     0xd42a48: mov             x2, NULL
    // 0xd42a4c: r1 = Null
    //     0xd42a4c: mov             x1, NULL
    // 0xd42a50: cmp             w0, NULL
    // 0xd42a54: b.eq            #0xd42aec
    // 0xd42a58: branchIfSmi(r0, 0xd42aec)
    //     0xd42a58: tbz             w0, #0, #0xd42aec
    // 0xd42a5c: r3 = LoadClassIdInstr(r0)
    //     0xd42a5c: ldur            x3, [x0, #-1]
    //     0xd42a60: ubfx            x3, x3, #0xc, #0x14
    // 0xd42a64: r17 = 7205
    //     0xd42a64: movz            x17, #0x1c25
    // 0xd42a68: cmp             x3, x17
    // 0xd42a6c: b.eq            #0xd42af4
    // 0xd42a70: r4 = LoadClassIdInstr(r0)
    //     0xd42a70: ldur            x4, [x0, #-1]
    //     0xd42a74: ubfx            x4, x4, #0xc, #0x14
    // 0xd42a78: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd42a7c: ldr             x3, [x3, #0x18]
    // 0xd42a80: ldr             x3, [x3, x4, lsl #3]
    // 0xd42a84: LoadField: r3 = r3->field_2b
    //     0xd42a84: ldur            w3, [x3, #0x2b]
    // 0xd42a88: DecompressPointer r3
    //     0xd42a88: add             x3, x3, HEAP, lsl #32
    // 0xd42a8c: cmp             w3, NULL
    // 0xd42a90: b.eq            #0xd42aec
    // 0xd42a94: LoadField: r3 = r3->field_f
    //     0xd42a94: ldur            w3, [x3, #0xf]
    // 0xd42a98: lsr             x3, x3, #3
    // 0xd42a9c: r17 = 7205
    //     0xd42a9c: movz            x17, #0x1c25
    // 0xd42aa0: cmp             x3, x17
    // 0xd42aa4: b.eq            #0xd42af4
    // 0xd42aa8: r3 = SubtypeTestCache
    //     0xd42aa8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c218] SubtypeTestCache
    //     0xd42aac: ldr             x3, [x3, #0x218]
    // 0xd42ab0: r30 = Subtype1TestCacheStub
    //     0xd42ab0: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd42ab4: LoadField: r30 = r30->field_7
    //     0xd42ab4: ldur            lr, [lr, #7]
    // 0xd42ab8: blr             lr
    // 0xd42abc: cmp             w7, NULL
    // 0xd42ac0: b.eq            #0xd42acc
    // 0xd42ac4: tbnz            w7, #4, #0xd42aec
    // 0xd42ac8: b               #0xd42af4
    // 0xd42acc: r8 = Iterable
    //     0xd42acc: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c220] Type: Iterable
    //     0xd42ad0: ldr             x8, [x8, #0x220]
    // 0xd42ad4: r3 = SubtypeTestCache
    //     0xd42ad4: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c228] SubtypeTestCache
    //     0xd42ad8: ldr             x3, [x3, #0x228]
    // 0xd42adc: r30 = InstanceOfStub
    //     0xd42adc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd42ae0: LoadField: r30 = r30->field_7
    //     0xd42ae0: ldur            lr, [lr, #7]
    // 0xd42ae4: blr             lr
    // 0xd42ae8: b               #0xd42af8
    // 0xd42aec: r0 = false
    //     0xd42aec: add             x0, NULL, #0x30  ; false
    // 0xd42af0: b               #0xd42af8
    // 0xd42af4: r0 = true
    //     0xd42af4: add             x0, NULL, #0x20  ; true
    // 0xd42af8: tbnz            w0, #4, #0xd42b10
    // 0xd42afc: ldur            x1, [fp, #-0x10]
    // 0xd42b00: ldur            x2, [fp, #-0x20]
    // 0xd42b04: r0 = iterableEquals()
    //     0xd42b04: bl              #0xd3fa14  ; [package:equatable/src/equatable_utils.dart] ::iterableEquals
    // 0xd42b08: tbz             w0, #4, #0xd42d30
    // 0xd42b0c: b               #0xd42d3c
    // 0xd42b10: ldur            x0, [fp, #-0x10]
    // 0xd42b14: r2 = Null
    //     0xd42b14: mov             x2, NULL
    // 0xd42b18: r1 = Null
    //     0xd42b18: mov             x1, NULL
    // 0xd42b1c: cmp             w0, NULL
    // 0xd42b20: b.eq            #0xd42bb8
    // 0xd42b24: branchIfSmi(r0, 0xd42bb8)
    //     0xd42b24: tbz             w0, #0, #0xd42bb8
    // 0xd42b28: r3 = LoadClassIdInstr(r0)
    //     0xd42b28: ldur            x3, [x0, #-1]
    //     0xd42b2c: ubfx            x3, x3, #0xc, #0x14
    // 0xd42b30: r17 = 6717
    //     0xd42b30: movz            x17, #0x1a3d
    // 0xd42b34: cmp             x3, x17
    // 0xd42b38: b.eq            #0xd42bc0
    // 0xd42b3c: r4 = LoadClassIdInstr(r0)
    //     0xd42b3c: ldur            x4, [x0, #-1]
    //     0xd42b40: ubfx            x4, x4, #0xc, #0x14
    // 0xd42b44: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd42b48: ldr             x3, [x3, #0x18]
    // 0xd42b4c: ldr             x3, [x3, x4, lsl #3]
    // 0xd42b50: LoadField: r3 = r3->field_2b
    //     0xd42b50: ldur            w3, [x3, #0x2b]
    // 0xd42b54: DecompressPointer r3
    //     0xd42b54: add             x3, x3, HEAP, lsl #32
    // 0xd42b58: cmp             w3, NULL
    // 0xd42b5c: b.eq            #0xd42bb8
    // 0xd42b60: LoadField: r3 = r3->field_f
    //     0xd42b60: ldur            w3, [x3, #0xf]
    // 0xd42b64: lsr             x3, x3, #3
    // 0xd42b68: r17 = 6717
    //     0xd42b68: movz            x17, #0x1a3d
    // 0xd42b6c: cmp             x3, x17
    // 0xd42b70: b.eq            #0xd42bc0
    // 0xd42b74: r3 = SubtypeTestCache
    //     0xd42b74: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c230] SubtypeTestCache
    //     0xd42b78: ldr             x3, [x3, #0x230]
    // 0xd42b7c: r30 = Subtype1TestCacheStub
    //     0xd42b7c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd42b80: LoadField: r30 = r30->field_7
    //     0xd42b80: ldur            lr, [lr, #7]
    // 0xd42b84: blr             lr
    // 0xd42b88: cmp             w7, NULL
    // 0xd42b8c: b.eq            #0xd42b98
    // 0xd42b90: tbnz            w7, #4, #0xd42bb8
    // 0xd42b94: b               #0xd42bc0
    // 0xd42b98: r8 = Map
    //     0xd42b98: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c238] Type: Map
    //     0xd42b9c: ldr             x8, [x8, #0x238]
    // 0xd42ba0: r3 = SubtypeTestCache
    //     0xd42ba0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c240] SubtypeTestCache
    //     0xd42ba4: ldr             x3, [x3, #0x240]
    // 0xd42ba8: r30 = InstanceOfStub
    //     0xd42ba8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd42bac: LoadField: r30 = r30->field_7
    //     0xd42bac: ldur            lr, [lr, #7]
    // 0xd42bb0: blr             lr
    // 0xd42bb4: b               #0xd42bc4
    // 0xd42bb8: r0 = false
    //     0xd42bb8: add             x0, NULL, #0x30  ; false
    // 0xd42bbc: b               #0xd42bc4
    // 0xd42bc0: r0 = true
    //     0xd42bc0: add             x0, NULL, #0x20  ; true
    // 0xd42bc4: tbnz            w0, #4, #0xd42c94
    // 0xd42bc8: ldur            x0, [fp, #-0x20]
    // 0xd42bcc: r2 = Null
    //     0xd42bcc: mov             x2, NULL
    // 0xd42bd0: r1 = Null
    //     0xd42bd0: mov             x1, NULL
    // 0xd42bd4: cmp             w0, NULL
    // 0xd42bd8: b.eq            #0xd42c70
    // 0xd42bdc: branchIfSmi(r0, 0xd42c70)
    //     0xd42bdc: tbz             w0, #0, #0xd42c70
    // 0xd42be0: r3 = LoadClassIdInstr(r0)
    //     0xd42be0: ldur            x3, [x0, #-1]
    //     0xd42be4: ubfx            x3, x3, #0xc, #0x14
    // 0xd42be8: r17 = 6717
    //     0xd42be8: movz            x17, #0x1a3d
    // 0xd42bec: cmp             x3, x17
    // 0xd42bf0: b.eq            #0xd42c78
    // 0xd42bf4: r4 = LoadClassIdInstr(r0)
    //     0xd42bf4: ldur            x4, [x0, #-1]
    //     0xd42bf8: ubfx            x4, x4, #0xc, #0x14
    // 0xd42bfc: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd42c00: ldr             x3, [x3, #0x18]
    // 0xd42c04: ldr             x3, [x3, x4, lsl #3]
    // 0xd42c08: LoadField: r3 = r3->field_2b
    //     0xd42c08: ldur            w3, [x3, #0x2b]
    // 0xd42c0c: DecompressPointer r3
    //     0xd42c0c: add             x3, x3, HEAP, lsl #32
    // 0xd42c10: cmp             w3, NULL
    // 0xd42c14: b.eq            #0xd42c70
    // 0xd42c18: LoadField: r3 = r3->field_f
    //     0xd42c18: ldur            w3, [x3, #0xf]
    // 0xd42c1c: lsr             x3, x3, #3
    // 0xd42c20: r17 = 6717
    //     0xd42c20: movz            x17, #0x1a3d
    // 0xd42c24: cmp             x3, x17
    // 0xd42c28: b.eq            #0xd42c78
    // 0xd42c2c: r3 = SubtypeTestCache
    //     0xd42c2c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c248] SubtypeTestCache
    //     0xd42c30: ldr             x3, [x3, #0x248]
    // 0xd42c34: r30 = Subtype1TestCacheStub
    //     0xd42c34: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd42c38: LoadField: r30 = r30->field_7
    //     0xd42c38: ldur            lr, [lr, #7]
    // 0xd42c3c: blr             lr
    // 0xd42c40: cmp             w7, NULL
    // 0xd42c44: b.eq            #0xd42c50
    // 0xd42c48: tbnz            w7, #4, #0xd42c70
    // 0xd42c4c: b               #0xd42c78
    // 0xd42c50: r8 = Map
    //     0xd42c50: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c250] Type: Map
    //     0xd42c54: ldr             x8, [x8, #0x250]
    // 0xd42c58: r3 = SubtypeTestCache
    //     0xd42c58: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c258] SubtypeTestCache
    //     0xd42c5c: ldr             x3, [x3, #0x258]
    // 0xd42c60: r30 = InstanceOfStub
    //     0xd42c60: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd42c64: LoadField: r30 = r30->field_7
    //     0xd42c64: ldur            lr, [lr, #7]
    // 0xd42c68: blr             lr
    // 0xd42c6c: b               #0xd42c7c
    // 0xd42c70: r0 = false
    //     0xd42c70: add             x0, NULL, #0x30  ; false
    // 0xd42c74: b               #0xd42c7c
    // 0xd42c78: r0 = true
    //     0xd42c78: add             x0, NULL, #0x20  ; true
    // 0xd42c7c: tbnz            w0, #4, #0xd42c94
    // 0xd42c80: ldur            x1, [fp, #-0x10]
    // 0xd42c84: ldur            x2, [fp, #-0x20]
    // 0xd42c88: r0 = mapEquals()
    //     0xd42c88: bl              #0xd402f8  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0xd42c8c: tbz             w0, #4, #0xd42d30
    // 0xd42c90: b               #0xd42d3c
    // 0xd42c94: ldur            x0, [fp, #-0x10]
    // 0xd42c98: cmp             w0, NULL
    // 0xd42c9c: b.ne            #0xd42ca8
    // 0xd42ca0: r1 = Null
    //     0xd42ca0: mov             x1, NULL
    // 0xd42ca4: b               #0xd42cb4
    // 0xd42ca8: str             x0, [SP]
    // 0xd42cac: r0 = runtimeType()
    //     0xd42cac: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd42cb0: mov             x1, x0
    // 0xd42cb4: ldur            x0, [fp, #-0x20]
    // 0xd42cb8: stur            x1, [fp, #-0x28]
    // 0xd42cbc: cmp             w0, NULL
    // 0xd42cc0: b.ne            #0xd42cd0
    // 0xd42cc4: mov             x0, x1
    // 0xd42cc8: r1 = Null
    //     0xd42cc8: mov             x1, NULL
    // 0xd42ccc: b               #0xd42ce0
    // 0xd42cd0: str             x0, [SP]
    // 0xd42cd4: r0 = runtimeType()
    //     0xd42cd4: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd42cd8: mov             x1, x0
    // 0xd42cdc: ldur            x0, [fp, #-0x28]
    // 0xd42ce0: r2 = LoadClassIdInstr(r0)
    //     0xd42ce0: ldur            x2, [x0, #-1]
    //     0xd42ce4: ubfx            x2, x2, #0xc, #0x14
    // 0xd42ce8: stp             x1, x0, [SP]
    // 0xd42cec: mov             x0, x2
    // 0xd42cf0: mov             lr, x0
    // 0xd42cf4: ldr             lr, [x21, lr, lsl #3]
    // 0xd42cf8: blr             lr
    // 0xd42cfc: tbnz            w0, #4, #0xd42d3c
    // 0xd42d00: ldur            x0, [fp, #-0x10]
    // 0xd42d04: r1 = 60
    //     0xd42d04: movz            x1, #0x3c
    // 0xd42d08: branchIfSmi(r0, 0xd42d14)
    //     0xd42d08: tbz             w0, #0, #0xd42d14
    // 0xd42d0c: r1 = LoadClassIdInstr(r0)
    //     0xd42d0c: ldur            x1, [x0, #-1]
    //     0xd42d10: ubfx            x1, x1, #0xc, #0x14
    // 0xd42d14: ldur            x16, [fp, #-0x20]
    // 0xd42d18: stp             x16, x0, [SP]
    // 0xd42d1c: mov             x0, x1
    // 0xd42d20: mov             lr, x0
    // 0xd42d24: ldr             lr, [x21, lr, lsl #3]
    // 0xd42d28: blr             lr
    // 0xd42d2c: tbnz            w0, #4, #0xd42d3c
    // 0xd42d30: ldur            x0, [fp, #-0x18]
    // 0xd42d34: add             x3, x0, #1
    // 0xd42d38: b               #0xd4267c
    // 0xd42d3c: r0 = false
    //     0xd42d3c: add             x0, NULL, #0x30  ; false
    // 0xd42d40: b               #0xd42f7c
    // 0xd42d44: r0 = true
    //     0xd42d44: add             x0, NULL, #0x20  ; true
    // 0xd42d48: b               #0xd42f7c
    // 0xd42d4c: ldur            x0, [fp, #-8]
    // 0xd42d50: r2 = Null
    //     0xd42d50: mov             x2, NULL
    // 0xd42d54: r1 = Null
    //     0xd42d54: mov             x1, NULL
    // 0xd42d58: cmp             w0, NULL
    // 0xd42d5c: b.eq            #0xd42df4
    // 0xd42d60: branchIfSmi(r0, 0xd42df4)
    //     0xd42d60: tbz             w0, #0, #0xd42df4
    // 0xd42d64: r3 = LoadClassIdInstr(r0)
    //     0xd42d64: ldur            x3, [x0, #-1]
    //     0xd42d68: ubfx            x3, x3, #0xc, #0x14
    // 0xd42d6c: r17 = 6717
    //     0xd42d6c: movz            x17, #0x1a3d
    // 0xd42d70: cmp             x3, x17
    // 0xd42d74: b.eq            #0xd42dfc
    // 0xd42d78: r4 = LoadClassIdInstr(r0)
    //     0xd42d78: ldur            x4, [x0, #-1]
    //     0xd42d7c: ubfx            x4, x4, #0xc, #0x14
    // 0xd42d80: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd42d84: ldr             x3, [x3, #0x18]
    // 0xd42d88: ldr             x3, [x3, x4, lsl #3]
    // 0xd42d8c: LoadField: r3 = r3->field_2b
    //     0xd42d8c: ldur            w3, [x3, #0x2b]
    // 0xd42d90: DecompressPointer r3
    //     0xd42d90: add             x3, x3, HEAP, lsl #32
    // 0xd42d94: cmp             w3, NULL
    // 0xd42d98: b.eq            #0xd42df4
    // 0xd42d9c: LoadField: r3 = r3->field_f
    //     0xd42d9c: ldur            w3, [x3, #0xf]
    // 0xd42da0: lsr             x3, x3, #3
    // 0xd42da4: r17 = 6717
    //     0xd42da4: movz            x17, #0x1a3d
    // 0xd42da8: cmp             x3, x17
    // 0xd42dac: b.eq            #0xd42dfc
    // 0xd42db0: r3 = SubtypeTestCache
    //     0xd42db0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c260] SubtypeTestCache
    //     0xd42db4: ldr             x3, [x3, #0x260]
    // 0xd42db8: r30 = Subtype1TestCacheStub
    //     0xd42db8: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd42dbc: LoadField: r30 = r30->field_7
    //     0xd42dbc: ldur            lr, [lr, #7]
    // 0xd42dc0: blr             lr
    // 0xd42dc4: cmp             w7, NULL
    // 0xd42dc8: b.eq            #0xd42dd4
    // 0xd42dcc: tbnz            w7, #4, #0xd42df4
    // 0xd42dd0: b               #0xd42dfc
    // 0xd42dd4: r8 = Map
    //     0xd42dd4: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c268] Type: Map
    //     0xd42dd8: ldr             x8, [x8, #0x268]
    // 0xd42ddc: r3 = SubtypeTestCache
    //     0xd42ddc: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c270] SubtypeTestCache
    //     0xd42de0: ldr             x3, [x3, #0x270]
    // 0xd42de4: r30 = InstanceOfStub
    //     0xd42de4: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd42de8: LoadField: r30 = r30->field_7
    //     0xd42de8: ldur            lr, [lr, #7]
    // 0xd42dec: blr             lr
    // 0xd42df0: b               #0xd42e00
    // 0xd42df4: r0 = false
    //     0xd42df4: add             x0, NULL, #0x30  ; false
    // 0xd42df8: b               #0xd42e00
    // 0xd42dfc: r0 = true
    //     0xd42dfc: add             x0, NULL, #0x20  ; true
    // 0xd42e00: tbnz            w0, #4, #0xd42ecc
    // 0xd42e04: ldr             x0, [fp, #0x10]
    // 0xd42e08: r2 = Null
    //     0xd42e08: mov             x2, NULL
    // 0xd42e0c: r1 = Null
    //     0xd42e0c: mov             x1, NULL
    // 0xd42e10: cmp             w0, NULL
    // 0xd42e14: b.eq            #0xd42eac
    // 0xd42e18: branchIfSmi(r0, 0xd42eac)
    //     0xd42e18: tbz             w0, #0, #0xd42eac
    // 0xd42e1c: r3 = LoadClassIdInstr(r0)
    //     0xd42e1c: ldur            x3, [x0, #-1]
    //     0xd42e20: ubfx            x3, x3, #0xc, #0x14
    // 0xd42e24: r17 = 6717
    //     0xd42e24: movz            x17, #0x1a3d
    // 0xd42e28: cmp             x3, x17
    // 0xd42e2c: b.eq            #0xd42eb4
    // 0xd42e30: r4 = LoadClassIdInstr(r0)
    //     0xd42e30: ldur            x4, [x0, #-1]
    //     0xd42e34: ubfx            x4, x4, #0xc, #0x14
    // 0xd42e38: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd42e3c: ldr             x3, [x3, #0x18]
    // 0xd42e40: ldr             x3, [x3, x4, lsl #3]
    // 0xd42e44: LoadField: r3 = r3->field_2b
    //     0xd42e44: ldur            w3, [x3, #0x2b]
    // 0xd42e48: DecompressPointer r3
    //     0xd42e48: add             x3, x3, HEAP, lsl #32
    // 0xd42e4c: cmp             w3, NULL
    // 0xd42e50: b.eq            #0xd42eac
    // 0xd42e54: LoadField: r3 = r3->field_f
    //     0xd42e54: ldur            w3, [x3, #0xf]
    // 0xd42e58: lsr             x3, x3, #3
    // 0xd42e5c: r17 = 6717
    //     0xd42e5c: movz            x17, #0x1a3d
    // 0xd42e60: cmp             x3, x17
    // 0xd42e64: b.eq            #0xd42eb4
    // 0xd42e68: r3 = SubtypeTestCache
    //     0xd42e68: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c278] SubtypeTestCache
    //     0xd42e6c: ldr             x3, [x3, #0x278]
    // 0xd42e70: r30 = Subtype1TestCacheStub
    //     0xd42e70: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd42e74: LoadField: r30 = r30->field_7
    //     0xd42e74: ldur            lr, [lr, #7]
    // 0xd42e78: blr             lr
    // 0xd42e7c: cmp             w7, NULL
    // 0xd42e80: b.eq            #0xd42e8c
    // 0xd42e84: tbnz            w7, #4, #0xd42eac
    // 0xd42e88: b               #0xd42eb4
    // 0xd42e8c: r8 = Map
    //     0xd42e8c: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c280] Type: Map
    //     0xd42e90: ldr             x8, [x8, #0x280]
    // 0xd42e94: r3 = SubtypeTestCache
    //     0xd42e94: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c288] SubtypeTestCache
    //     0xd42e98: ldr             x3, [x3, #0x288]
    // 0xd42e9c: r30 = InstanceOfStub
    //     0xd42e9c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd42ea0: LoadField: r30 = r30->field_7
    //     0xd42ea0: ldur            lr, [lr, #7]
    // 0xd42ea4: blr             lr
    // 0xd42ea8: b               #0xd42eb8
    // 0xd42eac: r0 = false
    //     0xd42eac: add             x0, NULL, #0x30  ; false
    // 0xd42eb0: b               #0xd42eb8
    // 0xd42eb4: r0 = true
    //     0xd42eb4: add             x0, NULL, #0x20  ; true
    // 0xd42eb8: tbnz            w0, #4, #0xd42ecc
    // 0xd42ebc: ldur            x1, [fp, #-8]
    // 0xd42ec0: ldr             x2, [fp, #0x10]
    // 0xd42ec4: r0 = mapEquals()
    //     0xd42ec4: bl              #0xd402f8  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0xd42ec8: b               #0xd42f7c
    // 0xd42ecc: ldur            x0, [fp, #-8]
    // 0xd42ed0: cmp             w0, NULL
    // 0xd42ed4: b.ne            #0xd42ee0
    // 0xd42ed8: r1 = Null
    //     0xd42ed8: mov             x1, NULL
    // 0xd42edc: b               #0xd42eec
    // 0xd42ee0: str             x0, [SP]
    // 0xd42ee4: r0 = runtimeType()
    //     0xd42ee4: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd42ee8: mov             x1, x0
    // 0xd42eec: ldr             x0, [fp, #0x10]
    // 0xd42ef0: stur            x1, [fp, #-0x10]
    // 0xd42ef4: cmp             w0, NULL
    // 0xd42ef8: b.ne            #0xd42f08
    // 0xd42efc: mov             x0, x1
    // 0xd42f00: r1 = Null
    //     0xd42f00: mov             x1, NULL
    // 0xd42f04: b               #0xd42f18
    // 0xd42f08: str             x0, [SP]
    // 0xd42f0c: r0 = runtimeType()
    //     0xd42f0c: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd42f10: mov             x1, x0
    // 0xd42f14: ldur            x0, [fp, #-0x10]
    // 0xd42f18: r2 = LoadClassIdInstr(r0)
    //     0xd42f18: ldur            x2, [x0, #-1]
    //     0xd42f1c: ubfx            x2, x2, #0xc, #0x14
    // 0xd42f20: stp             x1, x0, [SP]
    // 0xd42f24: mov             x0, x2
    // 0xd42f28: mov             lr, x0
    // 0xd42f2c: ldr             lr, [x21, lr, lsl #3]
    // 0xd42f30: blr             lr
    // 0xd42f34: tbz             w0, #4, #0xd42f40
    // 0xd42f38: r0 = false
    //     0xd42f38: add             x0, NULL, #0x30  ; false
    // 0xd42f3c: b               #0xd42f7c
    // 0xd42f40: ldur            x0, [fp, #-8]
    // 0xd42f44: r1 = 60
    //     0xd42f44: movz            x1, #0x3c
    // 0xd42f48: branchIfSmi(r0, 0xd42f54)
    //     0xd42f48: tbz             w0, #0, #0xd42f54
    // 0xd42f4c: r1 = LoadClassIdInstr(r0)
    //     0xd42f4c: ldur            x1, [x0, #-1]
    //     0xd42f50: ubfx            x1, x1, #0xc, #0x14
    // 0xd42f54: ldr             x16, [fp, #0x10]
    // 0xd42f58: stp             x16, x0, [SP]
    // 0xd42f5c: mov             x0, x1
    // 0xd42f60: mov             lr, x0
    // 0xd42f64: ldr             lr, [x21, lr, lsl #3]
    // 0xd42f68: blr             lr
    // 0xd42f6c: tbz             w0, #4, #0xd42f78
    // 0xd42f70: r0 = false
    //     0xd42f70: add             x0, NULL, #0x30  ; false
    // 0xd42f74: b               #0xd42f7c
    // 0xd42f78: r0 = true
    //     0xd42f78: add             x0, NULL, #0x20  ; true
    // 0xd42f7c: LeaveFrame
    //     0xd42f7c: mov             SP, fp
    //     0xd42f80: ldp             fp, lr, [SP], #0x10
    // 0xd42f84: ret
    //     0xd42f84: ret             
    // 0xd42f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd42f88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd42f8c: b               #0xd4220c
    // 0xd42f90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd42f90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd42f94: b               #0xd42694
  }
}
