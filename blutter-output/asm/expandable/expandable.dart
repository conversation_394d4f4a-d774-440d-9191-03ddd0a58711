// lib: expandable, url: package:expandable/expandable.dart

// class id: 1048716, size: 0x8
class :: {
}

// class id: 3679, size: 0x2c, field offset: 0x2c
class ExpandableController extends ValueNotifier<dynamic> {

  static _ of(/* No info */) {
    // ** addr: 0x92cd04, size: 0xb8
    // 0x92cd04: EnterFrame
    //     0x92cd04: stp             fp, lr, [SP, #-0x10]!
    //     0x92cd08: mov             fp, SP
    // 0x92cd0c: AllocStack(0x10)
    //     0x92cd0c: sub             SP, SP, #0x10
    // 0x92cd10: SetupParameters({dynamic rebuildOnChange = true /* r0 */})
    //     0x92cd10: ldur            w0, [x4, #0x13]
    //     0x92cd14: ldur            w2, [x4, #0x1f]
    //     0x92cd18: add             x2, x2, HEAP, lsl #32
    //     0x92cd1c: add             x16, PP, #0x43, lsl #12  ; [pp+0x43500] "rebuildOnChange"
    //     0x92cd20: ldr             x16, [x16, #0x500]
    //     0x92cd24: cmp             w2, w16
    //     0x92cd28: b.ne            #0x92cd44
    //     0x92cd2c: ldur            w2, [x4, #0x23]
    //     0x92cd30: add             x2, x2, HEAP, lsl #32
    //     0x92cd34: sub             w3, w0, w2
    //     0x92cd38: add             x0, fp, w3, sxtw #2
    //     0x92cd3c: ldr             x0, [x0, #8]
    //     0x92cd40: b               #0x92cd48
    //     0x92cd44: add             x0, NULL, #0x20  ; true
    // 0x92cd48: CheckStackOverflow
    //     0x92cd48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92cd4c: cmp             SP, x16
    //     0x92cd50: b.ls            #0x92cdb4
    // 0x92cd54: tbnz            w0, #4, #0x92cd74
    // 0x92cd58: r16 = <_ExpandableControllerNotifier>
    //     0x92cd58: add             x16, PP, #0x43, lsl #12  ; [pp+0x43508] TypeArguments: <_ExpandableControllerNotifier>
    //     0x92cd5c: ldr             x16, [x16, #0x508]
    // 0x92cd60: stp             x1, x16, [SP]
    // 0x92cd64: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x92cd64: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x92cd68: r0 = dependOnInheritedWidgetOfExactType()
    //     0x92cd68: bl              #0x6396d8  ; [package:flutter/src/widgets/framework.dart] Element::dependOnInheritedWidgetOfExactType
    // 0x92cd6c: mov             x1, x0
    // 0x92cd70: b               #0x92cd8c
    // 0x92cd74: r16 = <_ExpandableControllerNotifier>
    //     0x92cd74: add             x16, PP, #0x43, lsl #12  ; [pp+0x43508] TypeArguments: <_ExpandableControllerNotifier>
    //     0x92cd78: ldr             x16, [x16, #0x508]
    // 0x92cd7c: stp             x1, x16, [SP]
    // 0x92cd80: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x92cd80: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x92cd84: r0 = findAncestorWidgetOfExactType()
    //     0x92cd84: bl              #0x67a014  ; [package:flutter/src/widgets/framework.dart] Element::findAncestorWidgetOfExactType
    // 0x92cd88: mov             x1, x0
    // 0x92cd8c: cmp             w1, NULL
    // 0x92cd90: b.ne            #0x92cd9c
    // 0x92cd94: r0 = Null
    //     0x92cd94: mov             x0, NULL
    // 0x92cd98: b               #0x92cda8
    // 0x92cd9c: LoadField: r2 = r1->field_13
    //     0x92cd9c: ldur            w2, [x1, #0x13]
    // 0x92cda0: DecompressPointer r2
    //     0x92cda0: add             x2, x2, HEAP, lsl #32
    // 0x92cda4: mov             x0, x2
    // 0x92cda8: LeaveFrame
    //     0x92cda8: mov             SP, fp
    //     0x92cdac: ldp             fp, lr, [SP], #0x10
    // 0x92cdb0: ret
    //     0x92cdb0: ret             
    // 0x92cdb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92cdb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92cdb8: b               #0x92cd54
  }
  _ toggle(/* No info */) {
    // ** addr: 0xa96f78, size: 0x3c
    // 0xa96f78: EnterFrame
    //     0xa96f78: stp             fp, lr, [SP, #-0x10]!
    //     0xa96f7c: mov             fp, SP
    // 0xa96f80: CheckStackOverflow
    //     0xa96f80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa96f84: cmp             SP, x16
    //     0xa96f88: b.ls            #0xa96fac
    // 0xa96f8c: LoadField: r0 = r1->field_27
    //     0xa96f8c: ldur            w0, [x1, #0x27]
    // 0xa96f90: DecompressPointer r0
    //     0xa96f90: add             x0, x0, HEAP, lsl #32
    // 0xa96f94: eor             x2, x0, #0x10
    // 0xa96f98: r0 = value=()
    //     0xa96f98: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0xa96f9c: r0 = Null
    //     0xa96f9c: mov             x0, NULL
    // 0xa96fa0: LeaveFrame
    //     0xa96fa0: mov             SP, fp
    //     0xa96fa4: ldp             fp, lr, [SP], #0x10
    // 0xa96fa8: ret
    //     0xa96fa8: ret             
    // 0xa96fac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa96fac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa96fb0: b               #0xa96f8c
  }
  [closure] void toggle(dynamic) {
    // ** addr: 0xa96fb4, size: 0x38
    // 0xa96fb4: EnterFrame
    //     0xa96fb4: stp             fp, lr, [SP, #-0x10]!
    //     0xa96fb8: mov             fp, SP
    // 0xa96fbc: ldr             x0, [fp, #0x10]
    // 0xa96fc0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa96fc0: ldur            w1, [x0, #0x17]
    // 0xa96fc4: DecompressPointer r1
    //     0xa96fc4: add             x1, x1, HEAP, lsl #32
    // 0xa96fc8: CheckStackOverflow
    //     0xa96fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa96fcc: cmp             SP, x16
    //     0xa96fd0: b.ls            #0xa96fe4
    // 0xa96fd4: r0 = toggle()
    //     0xa96fd4: bl              #0xa96f78  ; [package:expandable/expandable.dart] ExpandableController::toggle
    // 0xa96fd8: LeaveFrame
    //     0xa96fd8: mov             SP, fp
    //     0xa96fdc: ldp             fp, lr, [SP], #0x10
    // 0xa96fe0: ret
    //     0xa96fe0: ret             
    // 0xa96fe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa96fe4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa96fe8: b               #0xa96fd4
  }
}

// class id: 4364, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __ExpandableIconState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6efa40, size: 0x98
    // 0x6efa40: EnterFrame
    //     0x6efa40: stp             fp, lr, [SP, #-0x10]!
    //     0x6efa44: mov             fp, SP
    // 0x6efa48: AllocStack(0x10)
    //     0x6efa48: sub             SP, SP, #0x10
    // 0x6efa4c: SetupParameters(__ExpandableIconState&State&SingleTickerProviderStateMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6efa4c: stur            x1, [fp, #-8]
    //     0x6efa50: stur            x2, [fp, #-0x10]
    // 0x6efa54: CheckStackOverflow
    //     0x6efa54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6efa58: cmp             SP, x16
    //     0x6efa5c: b.ls            #0x6efacc
    // 0x6efa60: r0 = Ticker()
    //     0x6efa60: bl              #0x6efe64  ; AllocateTickerStub -> Ticker (size=0x1c)
    // 0x6efa64: mov             x1, x0
    // 0x6efa68: r0 = false
    //     0x6efa68: add             x0, NULL, #0x30  ; false
    // 0x6efa6c: StoreField: r1->field_b = r0
    //     0x6efa6c: stur            w0, [x1, #0xb]
    // 0x6efa70: ldur            x0, [fp, #-0x10]
    // 0x6efa74: StoreField: r1->field_13 = r0
    //     0x6efa74: stur            w0, [x1, #0x13]
    // 0x6efa78: mov             x0, x1
    // 0x6efa7c: ldur            x2, [fp, #-8]
    // 0x6efa80: StoreField: r2->field_13 = r0
    //     0x6efa80: stur            w0, [x2, #0x13]
    //     0x6efa84: ldurb           w16, [x2, #-1]
    //     0x6efa88: ldurb           w17, [x0, #-1]
    //     0x6efa8c: and             x16, x17, x16, lsr #2
    //     0x6efa90: tst             x16, HEAP, lsr #32
    //     0x6efa94: b.eq            #0x6efa9c
    //     0x6efa98: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6efa9c: mov             x1, x2
    // 0x6efaa0: r0 = _updateTickerModeNotifier()
    //     0x6efaa0: bl              #0x6efc74  ; [package:expandable/expandable.dart] __ExpandableIconState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6efaa4: ldur            x1, [fp, #-8]
    // 0x6efaa8: r0 = _updateTicker()
    //     0x6efaa8: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6efaac: ldur            x1, [fp, #-8]
    // 0x6efab0: LoadField: r0 = r1->field_13
    //     0x6efab0: ldur            w0, [x1, #0x13]
    // 0x6efab4: DecompressPointer r0
    //     0x6efab4: add             x0, x0, HEAP, lsl #32
    // 0x6efab8: cmp             w0, NULL
    // 0x6efabc: b.eq            #0x6efad4
    // 0x6efac0: LeaveFrame
    //     0x6efac0: mov             SP, fp
    //     0x6efac4: ldp             fp, lr, [SP], #0x10
    // 0x6efac8: ret
    //     0x6efac8: ret             
    // 0x6efacc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6efacc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6efad0: b               #0x6efa60
    // 0x6efad4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6efad4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6efc74, size: 0x124
    // 0x6efc74: EnterFrame
    //     0x6efc74: stp             fp, lr, [SP, #-0x10]!
    //     0x6efc78: mov             fp, SP
    // 0x6efc7c: AllocStack(0x18)
    //     0x6efc7c: sub             SP, SP, #0x18
    // 0x6efc80: SetupParameters(__ExpandableIconState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6efc80: mov             x2, x1
    //     0x6efc84: stur            x1, [fp, #-8]
    // 0x6efc88: CheckStackOverflow
    //     0x6efc88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6efc8c: cmp             SP, x16
    //     0x6efc90: b.ls            #0x6efd8c
    // 0x6efc94: LoadField: r1 = r2->field_f
    //     0x6efc94: ldur            w1, [x2, #0xf]
    // 0x6efc98: DecompressPointer r1
    //     0x6efc98: add             x1, x1, HEAP, lsl #32
    // 0x6efc9c: cmp             w1, NULL
    // 0x6efca0: b.eq            #0x6efd94
    // 0x6efca4: r0 = getNotifier()
    //     0x6efca4: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6efca8: mov             x3, x0
    // 0x6efcac: ldur            x0, [fp, #-8]
    // 0x6efcb0: stur            x3, [fp, #-0x18]
    // 0x6efcb4: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6efcb4: ldur            w4, [x0, #0x17]
    // 0x6efcb8: DecompressPointer r4
    //     0x6efcb8: add             x4, x4, HEAP, lsl #32
    // 0x6efcbc: stur            x4, [fp, #-0x10]
    // 0x6efcc0: cmp             w3, w4
    // 0x6efcc4: b.ne            #0x6efcd8
    // 0x6efcc8: r0 = Null
    //     0x6efcc8: mov             x0, NULL
    // 0x6efccc: LeaveFrame
    //     0x6efccc: mov             SP, fp
    //     0x6efcd0: ldp             fp, lr, [SP], #0x10
    // 0x6efcd4: ret
    //     0x6efcd4: ret             
    // 0x6efcd8: cmp             w4, NULL
    // 0x6efcdc: b.eq            #0x6efd20
    // 0x6efce0: mov             x2, x0
    // 0x6efce4: r1 = Function '_updateTicker@364311458':.
    //     0x6efce4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54678] AnonymousClosure: (0x6efe2c), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6efce8: ldr             x1, [x1, #0x678]
    // 0x6efcec: r0 = AllocateClosure()
    //     0x6efcec: bl              #0xec1630  ; AllocateClosureStub
    // 0x6efcf0: ldur            x1, [fp, #-0x10]
    // 0x6efcf4: r2 = LoadClassIdInstr(r1)
    //     0x6efcf4: ldur            x2, [x1, #-1]
    //     0x6efcf8: ubfx            x2, x2, #0xc, #0x14
    // 0x6efcfc: mov             x16, x0
    // 0x6efd00: mov             x0, x2
    // 0x6efd04: mov             x2, x16
    // 0x6efd08: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6efd08: movz            x17, #0xbf5c
    //     0x6efd0c: add             lr, x0, x17
    //     0x6efd10: ldr             lr, [x21, lr, lsl #3]
    //     0x6efd14: blr             lr
    // 0x6efd18: ldur            x0, [fp, #-8]
    // 0x6efd1c: ldur            x3, [fp, #-0x18]
    // 0x6efd20: mov             x2, x0
    // 0x6efd24: r1 = Function '_updateTicker@364311458':.
    //     0x6efd24: add             x1, PP, #0x54, lsl #12  ; [pp+0x54678] AnonymousClosure: (0x6efe2c), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6efd28: ldr             x1, [x1, #0x678]
    // 0x6efd2c: r0 = AllocateClosure()
    //     0x6efd2c: bl              #0xec1630  ; AllocateClosureStub
    // 0x6efd30: ldur            x3, [fp, #-0x18]
    // 0x6efd34: r1 = LoadClassIdInstr(r3)
    //     0x6efd34: ldur            x1, [x3, #-1]
    //     0x6efd38: ubfx            x1, x1, #0xc, #0x14
    // 0x6efd3c: mov             x2, x0
    // 0x6efd40: mov             x0, x1
    // 0x6efd44: mov             x1, x3
    // 0x6efd48: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6efd48: movz            x17, #0xc407
    //     0x6efd4c: add             lr, x0, x17
    //     0x6efd50: ldr             lr, [x21, lr, lsl #3]
    //     0x6efd54: blr             lr
    // 0x6efd58: ldur            x0, [fp, #-0x18]
    // 0x6efd5c: ldur            x1, [fp, #-8]
    // 0x6efd60: ArrayStore: r1[0] = r0  ; List_4
    //     0x6efd60: stur            w0, [x1, #0x17]
    //     0x6efd64: ldurb           w16, [x1, #-1]
    //     0x6efd68: ldurb           w17, [x0, #-1]
    //     0x6efd6c: and             x16, x17, x16, lsr #2
    //     0x6efd70: tst             x16, HEAP, lsr #32
    //     0x6efd74: b.eq            #0x6efd7c
    //     0x6efd78: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6efd7c: r0 = Null
    //     0x6efd7c: mov             x0, NULL
    // 0x6efd80: LeaveFrame
    //     0x6efd80: mov             SP, fp
    //     0x6efd84: ldp             fp, lr, [SP], #0x10
    // 0x6efd88: ret
    //     0x6efd88: ret             
    // 0x6efd8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6efd8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6efd90: b               #0x6efc94
    // 0x6efd94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6efd94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTicker(dynamic) {
    // ** addr: 0x6efe2c, size: 0x38
    // 0x6efe2c: EnterFrame
    //     0x6efe2c: stp             fp, lr, [SP, #-0x10]!
    //     0x6efe30: mov             fp, SP
    // 0x6efe34: ldr             x0, [fp, #0x10]
    // 0x6efe38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6efe38: ldur            w1, [x0, #0x17]
    // 0x6efe3c: DecompressPointer r1
    //     0x6efe3c: add             x1, x1, HEAP, lsl #32
    // 0x6efe40: CheckStackOverflow
    //     0x6efe40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6efe44: cmp             SP, x16
    //     0x6efe48: b.ls            #0x6efe5c
    // 0x6efe4c: r0 = _updateTicker()
    //     0x6efe4c: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6efe50: LeaveFrame
    //     0x6efe50: mov             SP, fp
    //     0x6efe54: ldp             fp, lr, [SP], #0x10
    // 0x6efe58: ret
    //     0x6efe58: ret             
    // 0x6efe5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6efe5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6efe60: b               #0x6efe4c
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7ab7c, size: 0x94
    // 0xa7ab7c: EnterFrame
    //     0xa7ab7c: stp             fp, lr, [SP, #-0x10]!
    //     0xa7ab80: mov             fp, SP
    // 0xa7ab84: AllocStack(0x10)
    //     0xa7ab84: sub             SP, SP, #0x10
    // 0xa7ab88: SetupParameters(__ExpandableIconState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa7ab88: mov             x0, x1
    //     0xa7ab8c: stur            x1, [fp, #-0x10]
    // 0xa7ab90: CheckStackOverflow
    //     0xa7ab90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7ab94: cmp             SP, x16
    //     0xa7ab98: b.ls            #0xa7ac08
    // 0xa7ab9c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa7ab9c: ldur            w3, [x0, #0x17]
    // 0xa7aba0: DecompressPointer r3
    //     0xa7aba0: add             x3, x3, HEAP, lsl #32
    // 0xa7aba4: stur            x3, [fp, #-8]
    // 0xa7aba8: cmp             w3, NULL
    // 0xa7abac: b.ne            #0xa7abb8
    // 0xa7abb0: mov             x1, x0
    // 0xa7abb4: b               #0xa7abf4
    // 0xa7abb8: mov             x2, x0
    // 0xa7abbc: r1 = Function '_updateTicker@364311458':.
    //     0xa7abbc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54678] AnonymousClosure: (0x6efe2c), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0xa7abc0: ldr             x1, [x1, #0x678]
    // 0xa7abc4: r0 = AllocateClosure()
    //     0xa7abc4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7abc8: ldur            x1, [fp, #-8]
    // 0xa7abcc: r2 = LoadClassIdInstr(r1)
    //     0xa7abcc: ldur            x2, [x1, #-1]
    //     0xa7abd0: ubfx            x2, x2, #0xc, #0x14
    // 0xa7abd4: mov             x16, x0
    // 0xa7abd8: mov             x0, x2
    // 0xa7abdc: mov             x2, x16
    // 0xa7abe0: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa7abe0: movz            x17, #0xbf5c
    //     0xa7abe4: add             lr, x0, x17
    //     0xa7abe8: ldr             lr, [x21, lr, lsl #3]
    //     0xa7abec: blr             lr
    // 0xa7abf0: ldur            x1, [fp, #-0x10]
    // 0xa7abf4: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa7abf4: stur            NULL, [x1, #0x17]
    // 0xa7abf8: r0 = Null
    //     0xa7abf8: mov             x0, NULL
    // 0xa7abfc: LeaveFrame
    //     0xa7abfc: mov             SP, fp
    //     0xa7ac00: ldp             fp, lr, [SP], #0x10
    // 0xa7ac04: ret
    //     0xa7ac04: ret             
    // 0xa7ac08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7ac08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7ac0c: b               #0xa7ab9c
  }
  _ activate(/* No info */) {
    // ** addr: 0xa848f8, size: 0x48
    // 0xa848f8: EnterFrame
    //     0xa848f8: stp             fp, lr, [SP, #-0x10]!
    //     0xa848fc: mov             fp, SP
    // 0xa84900: AllocStack(0x8)
    //     0xa84900: sub             SP, SP, #8
    // 0xa84904: SetupParameters(__ExpandableIconState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa84904: mov             x0, x1
    //     0xa84908: stur            x1, [fp, #-8]
    // 0xa8490c: CheckStackOverflow
    //     0xa8490c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa84910: cmp             SP, x16
    //     0xa84914: b.ls            #0xa84938
    // 0xa84918: mov             x1, x0
    // 0xa8491c: r0 = _updateTickerModeNotifier()
    //     0xa8491c: bl              #0x6efc74  ; [package:expandable/expandable.dart] __ExpandableIconState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa84920: ldur            x1, [fp, #-8]
    // 0xa84924: r0 = _updateTicker()
    //     0xa84924: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0xa84928: r0 = Null
    //     0xa84928: mov             x0, NULL
    // 0xa8492c: LeaveFrame
    //     0xa8492c: mov             SP, fp
    //     0xa84930: ldp             fp, lr, [SP], #0x10
    // 0xa84934: ret
    //     0xa84934: ret             
    // 0xa84938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa84938: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8493c: b               #0xa84918
  }
}

// class id: 4365, size: 0x28, field offset: 0x1c
class _ExpandableIconState extends __ExpandableIconState&State&SingleTickerProviderStateMixin {

  _ initState(/* No info */) {
    // ** addr: 0x92cacc, size: 0x238
    // 0x92cacc: EnterFrame
    //     0x92cacc: stp             fp, lr, [SP, #-0x10]!
    //     0x92cad0: mov             fp, SP
    // 0x92cad4: AllocStack(0x30)
    //     0x92cad4: sub             SP, SP, #0x30
    // 0x92cad8: SetupParameters(_ExpandableIconState this /* r1 => r0, fp-0x8 */)
    //     0x92cad8: mov             x0, x1
    //     0x92cadc: stur            x1, [fp, #-8]
    // 0x92cae0: CheckStackOverflow
    //     0x92cae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92cae4: cmp             SP, x16
    //     0x92cae8: b.ls            #0x92cce8
    // 0x92caec: LoadField: r1 = r0->field_b
    //     0x92caec: ldur            w1, [x0, #0xb]
    // 0x92caf0: DecompressPointer r1
    //     0x92caf0: add             x1, x1, HEAP, lsl #32
    // 0x92caf4: cmp             w1, NULL
    // 0x92caf8: b.eq            #0x92ccf0
    // 0x92cafc: LoadField: r2 = r1->field_b
    //     0x92cafc: ldur            w2, [x1, #0xb]
    // 0x92cb00: DecompressPointer r2
    //     0x92cb00: add             x2, x2, HEAP, lsl #32
    // 0x92cb04: LoadField: r1 = r0->field_f
    //     0x92cb04: ldur            w1, [x0, #0xf]
    // 0x92cb08: DecompressPointer r1
    //     0x92cb08: add             x1, x1, HEAP, lsl #32
    // 0x92cb0c: cmp             w1, NULL
    // 0x92cb10: b.eq            #0x92ccf4
    // 0x92cb14: r16 = false
    //     0x92cb14: add             x16, NULL, #0x30  ; false
    // 0x92cb18: str             x16, [SP]
    // 0x92cb1c: mov             x16, x1
    // 0x92cb20: mov             x1, x2
    // 0x92cb24: mov             x2, x16
    // 0x92cb28: r4 = const [0, 0x3, 0x1, 0x2, rebuildOnChange, 0x2, null]
    //     0x92cb28: add             x4, PP, #0x54, lsl #12  ; [pp+0x546c8] List(7) [0, 0x3, 0x1, 0x2, "rebuildOnChange", 0x2, Null]
    //     0x92cb2c: ldr             x4, [x4, #0x6c8]
    // 0x92cb30: r0 = withDefaults()
    //     0x92cb30: bl              #0x92cdbc  ; [package:expandable/expandable.dart] ExpandableThemeData::withDefaults
    // 0x92cb34: stur            x0, [fp, #-0x18]
    // 0x92cb38: LoadField: r2 = r0->field_f
    //     0x92cb38: ldur            w2, [x0, #0xf]
    // 0x92cb3c: DecompressPointer r2
    //     0x92cb3c: add             x2, x2, HEAP, lsl #32
    // 0x92cb40: stur            x2, [fp, #-0x10]
    // 0x92cb44: r1 = <double>
    //     0x92cb44: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x92cb48: r0 = AnimationController()
    //     0x92cb48: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x92cb4c: stur            x0, [fp, #-0x20]
    // 0x92cb50: ldur            x16, [fp, #-0x10]
    // 0x92cb54: str             x16, [SP]
    // 0x92cb58: mov             x1, x0
    // 0x92cb5c: ldur            x2, [fp, #-8]
    // 0x92cb60: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x92cb60: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x92cb64: ldr             x4, [x4, #0x408]
    // 0x92cb68: r0 = AnimationController()
    //     0x92cb68: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x92cb6c: ldur            x0, [fp, #-0x20]
    // 0x92cb70: ldur            x2, [fp, #-8]
    // 0x92cb74: StoreField: r2->field_1b = r0
    //     0x92cb74: stur            w0, [x2, #0x1b]
    //     0x92cb78: ldurb           w16, [x2, #-1]
    //     0x92cb7c: ldurb           w17, [x0, #-1]
    //     0x92cb80: and             x16, x17, x16, lsr #2
    //     0x92cb84: tst             x16, HEAP, lsr #32
    //     0x92cb88: b.eq            #0x92cb90
    //     0x92cb8c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x92cb90: r1 = <double>
    //     0x92cb90: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x92cb94: r0 = Tween()
    //     0x92cb94: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x92cb98: mov             x2, x0
    // 0x92cb9c: r0 = 0.000000
    //     0x92cb9c: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x92cba0: stur            x2, [fp, #-0x28]
    // 0x92cba4: StoreField: r2->field_b = r0
    //     0x92cba4: stur            w0, [x2, #0xb]
    // 0x92cba8: r0 = 1.000000
    //     0x92cba8: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x92cbac: StoreField: r2->field_f = r0
    //     0x92cbac: stur            w0, [x2, #0xf]
    // 0x92cbb0: ldur            x0, [fp, #-0x18]
    // 0x92cbb4: LoadField: r3 = r0->field_23
    //     0x92cbb4: ldur            w3, [x0, #0x23]
    // 0x92cbb8: DecompressPointer r3
    //     0x92cbb8: add             x3, x3, HEAP, lsl #32
    // 0x92cbbc: stur            x3, [fp, #-0x10]
    // 0x92cbc0: cmp             w3, NULL
    // 0x92cbc4: b.eq            #0x92ccf8
    // 0x92cbc8: r1 = <double>
    //     0x92cbc8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x92cbcc: r0 = CurveTween()
    //     0x92cbcc: bl              #0x796a9c  ; AllocateCurveTweenStub -> CurveTween (size=0x10)
    // 0x92cbd0: mov             x1, x0
    // 0x92cbd4: ldur            x0, [fp, #-0x10]
    // 0x92cbd8: StoreField: r1->field_b = r0
    //     0x92cbd8: stur            w0, [x1, #0xb]
    // 0x92cbdc: mov             x2, x1
    // 0x92cbe0: ldur            x1, [fp, #-0x28]
    // 0x92cbe4: r0 = chain()
    //     0x92cbe4: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x92cbe8: mov             x1, x0
    // 0x92cbec: ldur            x2, [fp, #-0x20]
    // 0x92cbf0: r0 = animate()
    //     0x92cbf0: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x92cbf4: ldur            x2, [fp, #-8]
    // 0x92cbf8: StoreField: r2->field_1f = r0
    //     0x92cbf8: stur            w0, [x2, #0x1f]
    //     0x92cbfc: ldurb           w16, [x2, #-1]
    //     0x92cc00: ldurb           w17, [x0, #-1]
    //     0x92cc04: and             x16, x17, x16, lsr #2
    //     0x92cc08: tst             x16, HEAP, lsr #32
    //     0x92cc0c: b.eq            #0x92cc14
    //     0x92cc10: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x92cc14: LoadField: r1 = r2->field_f
    //     0x92cc14: ldur            w1, [x2, #0xf]
    // 0x92cc18: DecompressPointer r1
    //     0x92cc18: add             x1, x1, HEAP, lsl #32
    // 0x92cc1c: cmp             w1, NULL
    // 0x92cc20: b.eq            #0x92ccfc
    // 0x92cc24: r16 = false
    //     0x92cc24: add             x16, NULL, #0x30  ; false
    // 0x92cc28: str             x16, [SP]
    // 0x92cc2c: r4 = const [0, 0x2, 0x1, 0x1, rebuildOnChange, 0x1, null]
    //     0x92cc2c: add             x4, PP, #0x43, lsl #12  ; [pp+0x434d8] List(7) [0, 0x2, 0x1, 0x1, "rebuildOnChange", 0x1, Null]
    //     0x92cc30: ldr             x4, [x4, #0x4d8]
    // 0x92cc34: r0 = of()
    //     0x92cc34: bl              #0x92cd04  ; [package:expandable/expandable.dart] ExpandableController::of
    // 0x92cc38: mov             x4, x0
    // 0x92cc3c: ldur            x3, [fp, #-8]
    // 0x92cc40: stur            x4, [fp, #-0x10]
    // 0x92cc44: StoreField: r3->field_23 = r0
    //     0x92cc44: stur            w0, [x3, #0x23]
    //     0x92cc48: ldurb           w16, [x3, #-1]
    //     0x92cc4c: ldurb           w17, [x0, #-1]
    //     0x92cc50: and             x16, x17, x16, lsr #2
    //     0x92cc54: tst             x16, HEAP, lsr #32
    //     0x92cc58: b.eq            #0x92cc60
    //     0x92cc5c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x92cc60: cmp             w4, NULL
    // 0x92cc64: b.ne            #0x92cc70
    // 0x92cc68: mov             x0, x3
    // 0x92cc6c: b               #0x92cc90
    // 0x92cc70: mov             x2, x3
    // 0x92cc74: r1 = Function '_expandedStateChanged@1016245272':.
    //     0x92cc74: add             x1, PP, #0x54, lsl #12  ; [pp+0x54688] AnonymousClosure: (0x92d588), in [package:expandable/expandable.dart] _ExpandableIconState::_expandedStateChanged (0x92d5c0)
    //     0x92cc78: ldr             x1, [x1, #0x688]
    // 0x92cc7c: r0 = AllocateClosure()
    //     0x92cc7c: bl              #0xec1630  ; AllocateClosureStub
    // 0x92cc80: ldur            x1, [fp, #-0x10]
    // 0x92cc84: mov             x2, x0
    // 0x92cc88: r0 = addListener()
    //     0x92cc88: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x92cc8c: ldur            x0, [fp, #-8]
    // 0x92cc90: LoadField: r1 = r0->field_23
    //     0x92cc90: ldur            w1, [x0, #0x23]
    // 0x92cc94: DecompressPointer r1
    //     0x92cc94: add             x1, x1, HEAP, lsl #32
    // 0x92cc98: cmp             w1, NULL
    // 0x92cc9c: b.ne            #0x92cca8
    // 0x92cca0: r1 = Null
    //     0x92cca0: mov             x1, NULL
    // 0x92cca4: b               #0x92ccb4
    // 0x92cca8: LoadField: r2 = r1->field_27
    //     0x92cca8: ldur            w2, [x1, #0x27]
    // 0x92ccac: DecompressPointer r2
    //     0x92ccac: add             x2, x2, HEAP, lsl #32
    // 0x92ccb0: mov             x1, x2
    // 0x92ccb4: cmp             w1, NULL
    // 0x92ccb8: b.eq            #0x92ccc0
    // 0x92ccbc: tbnz            w1, #4, #0x92ccd8
    // 0x92ccc0: LoadField: r1 = r0->field_1b
    //     0x92ccc0: ldur            w1, [x0, #0x1b]
    // 0x92ccc4: DecompressPointer r1
    //     0x92ccc4: add             x1, x1, HEAP, lsl #32
    // 0x92ccc8: cmp             w1, NULL
    // 0x92cccc: b.eq            #0x92cd00
    // 0x92ccd0: d0 = 1.000000
    //     0x92ccd0: fmov            d0, #1.00000000
    // 0x92ccd4: r0 = value=()
    //     0x92ccd4: bl              #0x6567c4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::value=
    // 0x92ccd8: r0 = Null
    //     0x92ccd8: mov             x0, NULL
    // 0x92ccdc: LeaveFrame
    //     0x92ccdc: mov             SP, fp
    //     0x92cce0: ldp             fp, lr, [SP], #0x10
    // 0x92cce4: ret
    //     0x92cce4: ret             
    // 0x92cce8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92cce8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92ccec: b               #0x92caec
    // 0x92ccf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92ccf0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92ccf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92ccf4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92ccf8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92ccf8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92ccfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92ccfc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92cd00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92cd00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic _expandedStateChanged(dynamic) {
    // ** addr: 0x92d588, size: 0x38
    // 0x92d588: EnterFrame
    //     0x92d588: stp             fp, lr, [SP, #-0x10]!
    //     0x92d58c: mov             fp, SP
    // 0x92d590: ldr             x0, [fp, #0x10]
    // 0x92d594: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x92d594: ldur            w1, [x0, #0x17]
    // 0x92d598: DecompressPointer r1
    //     0x92d598: add             x1, x1, HEAP, lsl #32
    // 0x92d59c: CheckStackOverflow
    //     0x92d59c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92d5a0: cmp             SP, x16
    //     0x92d5a4: b.ls            #0x92d5b8
    // 0x92d5a8: r0 = _expandedStateChanged()
    //     0x92d5a8: bl              #0x92d5c0  ; [package:expandable/expandable.dart] _ExpandableIconState::_expandedStateChanged
    // 0x92d5ac: LeaveFrame
    //     0x92d5ac: mov             SP, fp
    //     0x92d5b0: ldp             fp, lr, [SP], #0x10
    // 0x92d5b4: ret
    //     0x92d5b4: ret             
    // 0x92d5b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92d5b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92d5bc: b               #0x92d5a8
  }
  _ _expandedStateChanged(/* No info */) {
    // ** addr: 0x92d5c0, size: 0x140
    // 0x92d5c0: EnterFrame
    //     0x92d5c0: stp             fp, lr, [SP, #-0x10]!
    //     0x92d5c4: mov             fp, SP
    // 0x92d5c8: AllocStack(0x8)
    //     0x92d5c8: sub             SP, SP, #8
    // 0x92d5cc: SetupParameters(_ExpandableIconState this /* r1 => r0, fp-0x8 */)
    //     0x92d5cc: mov             x0, x1
    //     0x92d5d0: stur            x1, [fp, #-8]
    // 0x92d5d4: CheckStackOverflow
    //     0x92d5d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92d5d8: cmp             SP, x16
    //     0x92d5dc: b.ls            #0x92d6d0
    // 0x92d5e0: LoadField: r1 = r0->field_23
    //     0x92d5e0: ldur            w1, [x0, #0x23]
    // 0x92d5e4: DecompressPointer r1
    //     0x92d5e4: add             x1, x1, HEAP, lsl #32
    // 0x92d5e8: cmp             w1, NULL
    // 0x92d5ec: b.eq            #0x92d6d8
    // 0x92d5f0: LoadField: r2 = r1->field_27
    //     0x92d5f0: ldur            w2, [x1, #0x27]
    // 0x92d5f4: DecompressPointer r2
    //     0x92d5f4: add             x2, x2, HEAP, lsl #32
    // 0x92d5f8: tbnz            w2, #4, #0x92d654
    // 0x92d5fc: LoadField: r1 = r0->field_1b
    //     0x92d5fc: ldur            w1, [x0, #0x1b]
    // 0x92d600: DecompressPointer r1
    //     0x92d600: add             x1, x1, HEAP, lsl #32
    // 0x92d604: cmp             w1, NULL
    // 0x92d608: b.eq            #0x92d6dc
    // 0x92d60c: LoadField: r2 = r1->field_43
    //     0x92d60c: ldur            w2, [x1, #0x43]
    // 0x92d610: DecompressPointer r2
    //     0x92d610: add             x2, x2, HEAP, lsl #32
    // 0x92d614: r16 = Sentinel
    //     0x92d614: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x92d618: cmp             w2, w16
    // 0x92d61c: b.eq            #0x92d6e0
    // 0x92d620: r1 = const [Instance of 'AnimationStatus', Instance of 'AnimationStatus']
    //     0x92d620: add             x1, PP, #0x54, lsl #12  ; [pp+0x54690] List<AnimationStatus>(2)
    //     0x92d624: ldr             x1, [x1, #0x690]
    // 0x92d628: r0 = contains()
    //     0x92d628: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0x92d62c: tbnz            w0, #4, #0x92d650
    // 0x92d630: ldur            x0, [fp, #-8]
    // 0x92d634: LoadField: r1 = r0->field_1b
    //     0x92d634: ldur            w1, [x0, #0x1b]
    // 0x92d638: DecompressPointer r1
    //     0x92d638: add             x1, x1, HEAP, lsl #32
    // 0x92d63c: cmp             w1, NULL
    // 0x92d640: b.eq            #0x92d6e8
    // 0x92d644: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x92d644: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x92d648: r0 = forward()
    //     0x92d648: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x92d64c: b               #0x92d6c0
    // 0x92d650: ldur            x0, [fp, #-8]
    // 0x92d654: LoadField: r1 = r0->field_23
    //     0x92d654: ldur            w1, [x0, #0x23]
    // 0x92d658: DecompressPointer r1
    //     0x92d658: add             x1, x1, HEAP, lsl #32
    // 0x92d65c: cmp             w1, NULL
    // 0x92d660: b.eq            #0x92d6ec
    // 0x92d664: LoadField: r2 = r1->field_27
    //     0x92d664: ldur            w2, [x1, #0x27]
    // 0x92d668: DecompressPointer r2
    //     0x92d668: add             x2, x2, HEAP, lsl #32
    // 0x92d66c: tbz             w2, #4, #0x92d6c0
    // 0x92d670: LoadField: r1 = r0->field_1b
    //     0x92d670: ldur            w1, [x0, #0x1b]
    // 0x92d674: DecompressPointer r1
    //     0x92d674: add             x1, x1, HEAP, lsl #32
    // 0x92d678: cmp             w1, NULL
    // 0x92d67c: b.eq            #0x92d6f0
    // 0x92d680: LoadField: r2 = r1->field_43
    //     0x92d680: ldur            w2, [x1, #0x43]
    // 0x92d684: DecompressPointer r2
    //     0x92d684: add             x2, x2, HEAP, lsl #32
    // 0x92d688: r16 = Sentinel
    //     0x92d688: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x92d68c: cmp             w2, w16
    // 0x92d690: b.eq            #0x92d6f4
    // 0x92d694: r1 = const [Instance of 'AnimationStatus', Instance of 'AnimationStatus']
    //     0x92d694: add             x1, PP, #0x54, lsl #12  ; [pp+0x54698] List<AnimationStatus>(2)
    //     0x92d698: ldr             x1, [x1, #0x698]
    // 0x92d69c: r0 = contains()
    //     0x92d69c: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0x92d6a0: tbnz            w0, #4, #0x92d6c0
    // 0x92d6a4: ldur            x0, [fp, #-8]
    // 0x92d6a8: LoadField: r1 = r0->field_1b
    //     0x92d6a8: ldur            w1, [x0, #0x1b]
    // 0x92d6ac: DecompressPointer r1
    //     0x92d6ac: add             x1, x1, HEAP, lsl #32
    // 0x92d6b0: cmp             w1, NULL
    // 0x92d6b4: b.eq            #0x92d6fc
    // 0x92d6b8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x92d6b8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x92d6bc: r0 = reverse()
    //     0x92d6bc: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0x92d6c0: r0 = Null
    //     0x92d6c0: mov             x0, NULL
    // 0x92d6c4: LeaveFrame
    //     0x92d6c4: mov             SP, fp
    //     0x92d6c8: ldp             fp, lr, [SP], #0x10
    // 0x92d6cc: ret
    //     0x92d6cc: ret             
    // 0x92d6d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92d6d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92d6d4: b               #0x92d5e0
    // 0x92d6d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92d6d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92d6dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92d6dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92d6e0: r9 = _status
    //     0x92d6e0: ldr             x9, [PP, #0x4ed8]  ; [pp+0x4ed8] Field <AnimationController._status@441066280>: late (offset: 0x44)
    // 0x92d6e4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x92d6e4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x92d6e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92d6e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92d6ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92d6ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92d6f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92d6f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92d6f4: r9 = _status
    //     0x92d6f4: ldr             x9, [PP, #0x4ed8]  ; [pp+0x4ed8] Field <AnimationController._status@441066280>: late (offset: 0x44)
    // 0x92d6f8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x92d6f8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x92d6fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92d6fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x982bf8, size: 0xec
    // 0x982bf8: EnterFrame
    //     0x982bf8: stp             fp, lr, [SP, #-0x10]!
    //     0x982bfc: mov             fp, SP
    // 0x982c00: AllocStack(0x20)
    //     0x982c00: sub             SP, SP, #0x20
    // 0x982c04: SetupParameters(_ExpandableIconState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x982c04: mov             x4, x1
    //     0x982c08: mov             x3, x2
    //     0x982c0c: stur            x1, [fp, #-8]
    //     0x982c10: stur            x2, [fp, #-0x10]
    // 0x982c14: CheckStackOverflow
    //     0x982c14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x982c18: cmp             SP, x16
    //     0x982c1c: b.ls            #0x982cd8
    // 0x982c20: mov             x0, x3
    // 0x982c24: r2 = Null
    //     0x982c24: mov             x2, NULL
    // 0x982c28: r1 = Null
    //     0x982c28: mov             x1, NULL
    // 0x982c2c: r4 = 60
    //     0x982c2c: movz            x4, #0x3c
    // 0x982c30: branchIfSmi(r0, 0x982c3c)
    //     0x982c30: tbz             w0, #0, #0x982c3c
    // 0x982c34: r4 = LoadClassIdInstr(r0)
    //     0x982c34: ldur            x4, [x0, #-1]
    //     0x982c38: ubfx            x4, x4, #0xc, #0x14
    // 0x982c3c: r17 = 4899
    //     0x982c3c: movz            x17, #0x1323
    // 0x982c40: cmp             x4, x17
    // 0x982c44: b.eq            #0x982c5c
    // 0x982c48: r8 = ExpandableIcon
    //     0x982c48: add             x8, PP, #0x54, lsl #12  ; [pp+0x546a0] Type: ExpandableIcon
    //     0x982c4c: ldr             x8, [x8, #0x6a0]
    // 0x982c50: r3 = Null
    //     0x982c50: add             x3, PP, #0x54, lsl #12  ; [pp+0x546a8] Null
    //     0x982c54: ldr             x3, [x3, #0x6a8]
    // 0x982c58: r0 = ExpandableIcon()
    //     0x982c58: bl              #0x6efad8  ; IsType_ExpandableIcon_Stub
    // 0x982c5c: ldur            x3, [fp, #-8]
    // 0x982c60: LoadField: r2 = r3->field_7
    //     0x982c60: ldur            w2, [x3, #7]
    // 0x982c64: DecompressPointer r2
    //     0x982c64: add             x2, x2, HEAP, lsl #32
    // 0x982c68: ldur            x0, [fp, #-0x10]
    // 0x982c6c: r1 = Null
    //     0x982c6c: mov             x1, NULL
    // 0x982c70: cmp             w2, NULL
    // 0x982c74: b.eq            #0x982c98
    // 0x982c78: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x982c78: ldur            w4, [x2, #0x17]
    // 0x982c7c: DecompressPointer r4
    //     0x982c7c: add             x4, x4, HEAP, lsl #32
    // 0x982c80: r8 = X0 bound StatefulWidget
    //     0x982c80: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x982c84: ldr             x8, [x8, #0x7f8]
    // 0x982c88: LoadField: r9 = r4->field_7
    //     0x982c88: ldur            x9, [x4, #7]
    // 0x982c8c: r3 = Null
    //     0x982c8c: add             x3, PP, #0x54, lsl #12  ; [pp+0x546b8] Null
    //     0x982c90: ldr             x3, [x3, #0x6b8]
    // 0x982c94: blr             x9
    // 0x982c98: ldur            x0, [fp, #-8]
    // 0x982c9c: LoadField: r1 = r0->field_b
    //     0x982c9c: ldur            w1, [x0, #0xb]
    // 0x982ca0: DecompressPointer r1
    //     0x982ca0: add             x1, x1, HEAP, lsl #32
    // 0x982ca4: cmp             w1, NULL
    // 0x982ca8: b.eq            #0x982ce0
    // 0x982cac: LoadField: r0 = r1->field_b
    //     0x982cac: ldur            w0, [x1, #0xb]
    // 0x982cb0: DecompressPointer r0
    //     0x982cb0: add             x0, x0, HEAP, lsl #32
    // 0x982cb4: ldur            x1, [fp, #-0x10]
    // 0x982cb8: LoadField: r2 = r1->field_b
    //     0x982cb8: ldur            w2, [x1, #0xb]
    // 0x982cbc: DecompressPointer r2
    //     0x982cbc: add             x2, x2, HEAP, lsl #32
    // 0x982cc0: stp             x2, x0, [SP]
    // 0x982cc4: r0 = ==()
    //     0x982cc4: bl              #0xd42f98  ; [package:expandable/expandable.dart] ExpandableThemeData::==
    // 0x982cc8: r0 = Null
    //     0x982cc8: mov             x0, NULL
    // 0x982ccc: LeaveFrame
    //     0x982ccc: mov             SP, fp
    //     0x982cd0: ldp             fp, lr, [SP], #0x10
    // 0x982cd4: ret
    //     0x982cd4: ret             
    // 0x982cd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x982cd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x982cdc: b               #0x982c20
    // 0x982ce0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x982ce0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9a2a74, size: 0x154
    // 0x9a2a74: EnterFrame
    //     0x9a2a74: stp             fp, lr, [SP, #-0x10]!
    //     0x9a2a78: mov             fp, SP
    // 0x9a2a7c: AllocStack(0x20)
    //     0x9a2a7c: sub             SP, SP, #0x20
    // 0x9a2a80: SetupParameters(_ExpandableIconState this /* r1 => r2, fp-0x8 */)
    //     0x9a2a80: mov             x2, x1
    //     0x9a2a84: stur            x1, [fp, #-8]
    // 0x9a2a88: CheckStackOverflow
    //     0x9a2a88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a2a8c: cmp             SP, x16
    //     0x9a2a90: b.ls            #0x9a2bb8
    // 0x9a2a94: LoadField: r1 = r2->field_f
    //     0x9a2a94: ldur            w1, [x2, #0xf]
    // 0x9a2a98: DecompressPointer r1
    //     0x9a2a98: add             x1, x1, HEAP, lsl #32
    // 0x9a2a9c: cmp             w1, NULL
    // 0x9a2aa0: b.eq            #0x9a2bc0
    // 0x9a2aa4: r16 = false
    //     0x9a2aa4: add             x16, NULL, #0x30  ; false
    // 0x9a2aa8: str             x16, [SP]
    // 0x9a2aac: r4 = const [0, 0x2, 0x1, 0x1, rebuildOnChange, 0x1, null]
    //     0x9a2aac: add             x4, PP, #0x43, lsl #12  ; [pp+0x434d8] List(7) [0, 0x2, 0x1, 0x1, "rebuildOnChange", 0x1, Null]
    //     0x9a2ab0: ldr             x4, [x4, #0x4d8]
    // 0x9a2ab4: r0 = of()
    //     0x9a2ab4: bl              #0x92cd04  ; [package:expandable/expandable.dart] ExpandableController::of
    // 0x9a2ab8: mov             x3, x0
    // 0x9a2abc: ldur            x0, [fp, #-8]
    // 0x9a2ac0: stur            x3, [fp, #-0x18]
    // 0x9a2ac4: LoadField: r4 = r0->field_23
    //     0x9a2ac4: ldur            w4, [x0, #0x23]
    // 0x9a2ac8: DecompressPointer r4
    //     0x9a2ac8: add             x4, x4, HEAP, lsl #32
    // 0x9a2acc: stur            x4, [fp, #-0x10]
    // 0x9a2ad0: cmp             w3, w4
    // 0x9a2ad4: b.eq            #0x9a2ba8
    // 0x9a2ad8: cmp             w4, NULL
    // 0x9a2adc: b.ne            #0x9a2aec
    // 0x9a2ae0: mov             x4, x3
    // 0x9a2ae4: mov             x3, x0
    // 0x9a2ae8: b               #0x9a2b10
    // 0x9a2aec: mov             x2, x0
    // 0x9a2af0: r1 = Function '_expandedStateChanged@1016245272':.
    //     0x9a2af0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54688] AnonymousClosure: (0x92d588), in [package:expandable/expandable.dart] _ExpandableIconState::_expandedStateChanged (0x92d5c0)
    //     0x9a2af4: ldr             x1, [x1, #0x688]
    // 0x9a2af8: r0 = AllocateClosure()
    //     0x9a2af8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a2afc: ldur            x1, [fp, #-0x10]
    // 0x9a2b00: mov             x2, x0
    // 0x9a2b04: r0 = removeListener()
    //     0x9a2b04: bl              #0xa8a5dc  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x9a2b08: ldur            x3, [fp, #-8]
    // 0x9a2b0c: ldur            x4, [fp, #-0x18]
    // 0x9a2b10: mov             x0, x4
    // 0x9a2b14: StoreField: r3->field_23 = r0
    //     0x9a2b14: stur            w0, [x3, #0x23]
    //     0x9a2b18: ldurb           w16, [x3, #-1]
    //     0x9a2b1c: ldurb           w17, [x0, #-1]
    //     0x9a2b20: and             x16, x17, x16, lsr #2
    //     0x9a2b24: tst             x16, HEAP, lsr #32
    //     0x9a2b28: b.eq            #0x9a2b30
    //     0x9a2b2c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x9a2b30: cmp             w4, NULL
    // 0x9a2b34: b.ne            #0x9a2b40
    // 0x9a2b38: mov             x0, x3
    // 0x9a2b3c: b               #0x9a2b60
    // 0x9a2b40: mov             x2, x3
    // 0x9a2b44: r1 = Function '_expandedStateChanged@1016245272':.
    //     0x9a2b44: add             x1, PP, #0x54, lsl #12  ; [pp+0x54688] AnonymousClosure: (0x92d588), in [package:expandable/expandable.dart] _ExpandableIconState::_expandedStateChanged (0x92d5c0)
    //     0x9a2b48: ldr             x1, [x1, #0x688]
    // 0x9a2b4c: r0 = AllocateClosure()
    //     0x9a2b4c: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a2b50: ldur            x1, [fp, #-0x18]
    // 0x9a2b54: mov             x2, x0
    // 0x9a2b58: r0 = addListener()
    //     0x9a2b58: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x9a2b5c: ldur            x0, [fp, #-8]
    // 0x9a2b60: LoadField: r1 = r0->field_23
    //     0x9a2b60: ldur            w1, [x0, #0x23]
    // 0x9a2b64: DecompressPointer r1
    //     0x9a2b64: add             x1, x1, HEAP, lsl #32
    // 0x9a2b68: cmp             w1, NULL
    // 0x9a2b6c: b.ne            #0x9a2b78
    // 0x9a2b70: r1 = Null
    //     0x9a2b70: mov             x1, NULL
    // 0x9a2b74: b               #0x9a2b84
    // 0x9a2b78: LoadField: r2 = r1->field_27
    //     0x9a2b78: ldur            w2, [x1, #0x27]
    // 0x9a2b7c: DecompressPointer r2
    //     0x9a2b7c: add             x2, x2, HEAP, lsl #32
    // 0x9a2b80: mov             x1, x2
    // 0x9a2b84: cmp             w1, NULL
    // 0x9a2b88: b.eq            #0x9a2b90
    // 0x9a2b8c: tbnz            w1, #4, #0x9a2ba8
    // 0x9a2b90: LoadField: r1 = r0->field_1b
    //     0x9a2b90: ldur            w1, [x0, #0x1b]
    // 0x9a2b94: DecompressPointer r1
    //     0x9a2b94: add             x1, x1, HEAP, lsl #32
    // 0x9a2b98: cmp             w1, NULL
    // 0x9a2b9c: b.eq            #0x9a2bc4
    // 0x9a2ba0: d0 = 1.000000
    //     0x9a2ba0: fmov            d0, #1.00000000
    // 0x9a2ba4: r0 = value=()
    //     0x9a2ba4: bl              #0x6567c4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::value=
    // 0x9a2ba8: r0 = Null
    //     0x9a2ba8: mov             x0, NULL
    // 0x9a2bac: LeaveFrame
    //     0x9a2bac: mov             SP, fp
    //     0x9a2bb0: ldp             fp, lr, [SP], #0x10
    // 0x9a2bb4: ret
    //     0x9a2bb4: ret             
    // 0x9a2bb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a2bb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a2bbc: b               #0x9a2a94
    // 0x9a2bc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a2bc0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a2bc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a2bc4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0x9d3f0c, size: 0x110
    // 0x9d3f0c: EnterFrame
    //     0x9d3f0c: stp             fp, lr, [SP, #-0x10]!
    //     0x9d3f10: mov             fp, SP
    // 0x9d3f14: AllocStack(0x20)
    //     0x9d3f14: sub             SP, SP, #0x20
    // 0x9d3f18: SetupParameters(_ExpandableIconState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x9d3f18: stur            x1, [fp, #-8]
    //     0x9d3f1c: stur            x2, [fp, #-0x10]
    // 0x9d3f20: CheckStackOverflow
    //     0x9d3f20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d3f24: cmp             SP, x16
    //     0x9d3f28: b.ls            #0x9d4008
    // 0x9d3f2c: r1 = 2
    //     0x9d3f2c: movz            x1, #0x2
    // 0x9d3f30: r0 = AllocateContext()
    //     0x9d3f30: bl              #0xec126c  ; AllocateContextStub
    // 0x9d3f34: mov             x3, x0
    // 0x9d3f38: ldur            x0, [fp, #-8]
    // 0x9d3f3c: stur            x3, [fp, #-0x18]
    // 0x9d3f40: StoreField: r3->field_f = r0
    //     0x9d3f40: stur            w0, [x3, #0xf]
    // 0x9d3f44: LoadField: r1 = r0->field_b
    //     0x9d3f44: ldur            w1, [x0, #0xb]
    // 0x9d3f48: DecompressPointer r1
    //     0x9d3f48: add             x1, x1, HEAP, lsl #32
    // 0x9d3f4c: cmp             w1, NULL
    // 0x9d3f50: b.eq            #0x9d4010
    // 0x9d3f54: LoadField: r2 = r1->field_b
    //     0x9d3f54: ldur            w2, [x1, #0xb]
    // 0x9d3f58: DecompressPointer r2
    //     0x9d3f58: add             x2, x2, HEAP, lsl #32
    // 0x9d3f5c: mov             x1, x2
    // 0x9d3f60: ldur            x2, [fp, #-0x10]
    // 0x9d3f64: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9d3f64: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9d3f68: r0 = withDefaults()
    //     0x9d3f68: bl              #0x92cdbc  ; [package:expandable/expandable.dart] ExpandableThemeData::withDefaults
    // 0x9d3f6c: mov             x1, x0
    // 0x9d3f70: ldur            x2, [fp, #-0x18]
    // 0x9d3f74: StoreField: r2->field_13 = r0
    //     0x9d3f74: stur            w0, [x2, #0x13]
    //     0x9d3f78: ldurb           w16, [x2, #-1]
    //     0x9d3f7c: ldurb           w17, [x0, #-1]
    //     0x9d3f80: and             x16, x17, x16, lsr #2
    //     0x9d3f84: tst             x16, HEAP, lsr #32
    //     0x9d3f88: b.eq            #0x9d3f90
    //     0x9d3f8c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9d3f90: LoadField: r0 = r1->field_47
    //     0x9d3f90: ldur            w0, [x1, #0x47]
    // 0x9d3f94: DecompressPointer r0
    //     0x9d3f94: add             x0, x0, HEAP, lsl #32
    // 0x9d3f98: stur            x0, [fp, #-0x20]
    // 0x9d3f9c: cmp             w0, NULL
    // 0x9d3fa0: b.eq            #0x9d4014
    // 0x9d3fa4: ldur            x1, [fp, #-8]
    // 0x9d3fa8: LoadField: r3 = r1->field_1f
    //     0x9d3fa8: ldur            w3, [x1, #0x1f]
    // 0x9d3fac: DecompressPointer r3
    //     0x9d3fac: add             x3, x3, HEAP, lsl #32
    // 0x9d3fb0: stur            x3, [fp, #-0x10]
    // 0x9d3fb4: cmp             w3, NULL
    // 0x9d3fb8: b.eq            #0x9d4018
    // 0x9d3fbc: r1 = Function '<anonymous closure>':.
    //     0x9d3fbc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54680] AnonymousClosure: (0x9d4028), in [package:expandable/expandable.dart] _ExpandableIconState::build (0x9d3f0c)
    //     0x9d3fc0: ldr             x1, [x1, #0x680]
    // 0x9d3fc4: r0 = AllocateClosure()
    //     0x9d3fc4: bl              #0xec1630  ; AllocateClosureStub
    // 0x9d3fc8: stur            x0, [fp, #-8]
    // 0x9d3fcc: r0 = AnimatedBuilder()
    //     0x9d3fcc: bl              #0x7e5888  ; AllocateAnimatedBuilderStub -> AnimatedBuilder (size=0x18)
    // 0x9d3fd0: mov             x1, x0
    // 0x9d3fd4: ldur            x0, [fp, #-8]
    // 0x9d3fd8: stur            x1, [fp, #-0x18]
    // 0x9d3fdc: StoreField: r1->field_f = r0
    //     0x9d3fdc: stur            w0, [x1, #0xf]
    // 0x9d3fe0: ldur            x0, [fp, #-0x10]
    // 0x9d3fe4: StoreField: r1->field_b = r0
    //     0x9d3fe4: stur            w0, [x1, #0xb]
    // 0x9d3fe8: r0 = Padding()
    //     0x9d3fe8: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9d3fec: ldur            x1, [fp, #-0x20]
    // 0x9d3ff0: StoreField: r0->field_f = r1
    //     0x9d3ff0: stur            w1, [x0, #0xf]
    // 0x9d3ff4: ldur            x1, [fp, #-0x18]
    // 0x9d3ff8: StoreField: r0->field_b = r1
    //     0x9d3ff8: stur            w1, [x0, #0xb]
    // 0x9d3ffc: LeaveFrame
    //     0x9d3ffc: mov             SP, fp
    //     0x9d4000: ldp             fp, lr, [SP], #0x10
    // 0x9d4004: ret
    //     0x9d4004: ret             
    // 0x9d4008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d4008: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d400c: b               #0x9d3f2c
    // 0x9d4010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d4010: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d4014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d4014: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d4018: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d4018: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Transform <anonymous closure>(dynamic, BuildContext, Widget?) {
    // ** addr: 0x9d4028, size: 0x26c
    // 0x9d4028: EnterFrame
    //     0x9d4028: stp             fp, lr, [SP, #-0x10]!
    //     0x9d402c: mov             fp, SP
    // 0x9d4030: AllocStack(0x38)
    //     0x9d4030: sub             SP, SP, #0x38
    // 0x9d4034: SetupParameters()
    //     0x9d4034: ldr             x0, [fp, #0x20]
    //     0x9d4038: ldur            w1, [x0, #0x17]
    //     0x9d403c: add             x1, x1, HEAP, lsl #32
    //     0x9d4040: stur            x1, [fp, #-0x20]
    // 0x9d4044: CheckStackOverflow
    //     0x9d4044: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d4048: cmp             SP, x16
    //     0x9d404c: b.ls            #0x9d4254
    // 0x9d4050: LoadField: r0 = r1->field_13
    //     0x9d4050: ldur            w0, [x1, #0x13]
    // 0x9d4054: DecompressPointer r0
    //     0x9d4054: add             x0, x0, HEAP, lsl #32
    // 0x9d4058: stur            x0, [fp, #-0x18]
    // 0x9d405c: LoadField: r2 = r0->field_53
    //     0x9d405c: ldur            w2, [x0, #0x53]
    // 0x9d4060: DecompressPointer r2
    //     0x9d4060: add             x2, x2, HEAP, lsl #32
    // 0x9d4064: stur            x2, [fp, #-0x10]
    // 0x9d4068: cmp             w2, NULL
    // 0x9d406c: b.eq            #0x9d425c
    // 0x9d4070: LoadField: r3 = r0->field_4f
    //     0x9d4070: ldur            w3, [x0, #0x4f]
    // 0x9d4074: DecompressPointer r3
    //     0x9d4074: add             x3, x3, HEAP, lsl #32
    // 0x9d4078: stur            x3, [fp, #-8]
    // 0x9d407c: cmp             w3, NULL
    // 0x9d4080: b.eq            #0x9d4260
    // 0x9d4084: stp             x3, x2, [SP]
    // 0x9d4088: r0 = ==()
    //     0x9d4088: bl              #0xd68380  ; [package:flutter/src/widgets/icon_data.dart] IconData::==
    // 0x9d408c: tbz             w0, #4, #0x9d40e0
    // 0x9d4090: ldur            x0, [fp, #-0x20]
    // 0x9d4094: d0 = 0.500000
    //     0x9d4094: fmov            d0, #0.50000000
    // 0x9d4098: LoadField: r1 = r0->field_f
    //     0x9d4098: ldur            w1, [x0, #0xf]
    // 0x9d409c: DecompressPointer r1
    //     0x9d409c: add             x1, x1, HEAP, lsl #32
    // 0x9d40a0: LoadField: r2 = r1->field_1b
    //     0x9d40a0: ldur            w2, [x1, #0x1b]
    // 0x9d40a4: DecompressPointer r2
    //     0x9d40a4: add             x2, x2, HEAP, lsl #32
    // 0x9d40a8: cmp             w2, NULL
    // 0x9d40ac: b.eq            #0x9d4264
    // 0x9d40b0: LoadField: r1 = r2->field_37
    //     0x9d40b0: ldur            w1, [x2, #0x37]
    // 0x9d40b4: DecompressPointer r1
    //     0x9d40b4: add             x1, x1, HEAP, lsl #32
    // 0x9d40b8: r16 = Sentinel
    //     0x9d40b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9d40bc: cmp             w1, w16
    // 0x9d40c0: b.eq            #0x9d4268
    // 0x9d40c4: LoadField: d1 = r1->field_7
    //     0x9d40c4: ldur            d1, [x1, #7]
    // 0x9d40c8: fcmp            d1, d0
    // 0x9d40cc: r16 = true
    //     0x9d40cc: add             x16, NULL, #0x20  ; true
    // 0x9d40d0: r17 = false
    //     0x9d40d0: add             x17, NULL, #0x30  ; false
    // 0x9d40d4: csel            x1, x16, x17, ge
    // 0x9d40d8: mov             x2, x1
    // 0x9d40dc: b               #0x9d40e8
    // 0x9d40e0: ldur            x0, [fp, #-0x20]
    // 0x9d40e4: r2 = false
    //     0x9d40e4: add             x2, NULL, #0x30  ; false
    // 0x9d40e8: ldur            x1, [fp, #-0x18]
    // 0x9d40ec: LoadField: r3 = r1->field_4b
    //     0x9d40ec: ldur            w3, [x1, #0x4b]
    // 0x9d40f0: DecompressPointer r3
    //     0x9d40f0: add             x3, x3, HEAP, lsl #32
    // 0x9d40f4: cmp             w3, NULL
    // 0x9d40f8: b.eq            #0x9d4270
    // 0x9d40fc: tbnz            w2, #4, #0x9d4140
    // 0x9d4100: d0 = 1.000000
    //     0x9d4100: fmov            d0, #1.00000000
    // 0x9d4104: LoadField: r4 = r0->field_f
    //     0x9d4104: ldur            w4, [x0, #0xf]
    // 0x9d4108: DecompressPointer r4
    //     0x9d4108: add             x4, x4, HEAP, lsl #32
    // 0x9d410c: LoadField: r0 = r4->field_1b
    //     0x9d410c: ldur            w0, [x4, #0x1b]
    // 0x9d4110: DecompressPointer r0
    //     0x9d4110: add             x0, x0, HEAP, lsl #32
    // 0x9d4114: cmp             w0, NULL
    // 0x9d4118: b.eq            #0x9d4274
    // 0x9d411c: LoadField: r4 = r0->field_37
    //     0x9d411c: ldur            w4, [x0, #0x37]
    // 0x9d4120: DecompressPointer r4
    //     0x9d4120: add             x4, x4, HEAP, lsl #32
    // 0x9d4124: r16 = Sentinel
    //     0x9d4124: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9d4128: cmp             w4, w16
    // 0x9d412c: b.eq            #0x9d4278
    // 0x9d4130: LoadField: d1 = r4->field_7
    //     0x9d4130: ldur            d1, [x4, #7]
    // 0x9d4134: fsub            d2, d0, d1
    // 0x9d4138: fneg            d0, d2
    // 0x9d413c: b               #0x9d4170
    // 0x9d4140: LoadField: r4 = r0->field_f
    //     0x9d4140: ldur            w4, [x0, #0xf]
    // 0x9d4144: DecompressPointer r4
    //     0x9d4144: add             x4, x4, HEAP, lsl #32
    // 0x9d4148: LoadField: r0 = r4->field_1b
    //     0x9d4148: ldur            w0, [x4, #0x1b]
    // 0x9d414c: DecompressPointer r0
    //     0x9d414c: add             x0, x0, HEAP, lsl #32
    // 0x9d4150: cmp             w0, NULL
    // 0x9d4154: b.eq            #0x9d4280
    // 0x9d4158: LoadField: r4 = r0->field_37
    //     0x9d4158: ldur            w4, [x0, #0x37]
    // 0x9d415c: DecompressPointer r4
    //     0x9d415c: add             x4, x4, HEAP, lsl #32
    // 0x9d4160: r16 = Sentinel
    //     0x9d4160: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9d4164: cmp             w4, w16
    // 0x9d4168: b.eq            #0x9d4284
    // 0x9d416c: LoadField: d0 = r4->field_7
    //     0x9d416c: ldur            d0, [x4, #7]
    // 0x9d4170: LoadField: d1 = r3->field_7
    //     0x9d4170: ldur            d1, [x3, #7]
    // 0x9d4174: fmul            d2, d1, d0
    // 0x9d4178: stur            d2, [fp, #-0x28]
    // 0x9d417c: tbnz            w2, #4, #0x9d4188
    // 0x9d4180: ldur            x0, [fp, #-0x10]
    // 0x9d4184: b               #0x9d418c
    // 0x9d4188: ldur            x0, [fp, #-8]
    // 0x9d418c: stur            x0, [fp, #-0x20]
    // 0x9d4190: LoadField: r2 = r1->field_7
    //     0x9d4190: ldur            w2, [x1, #7]
    // 0x9d4194: DecompressPointer r2
    //     0x9d4194: add             x2, x2, HEAP, lsl #32
    // 0x9d4198: stur            x2, [fp, #-0x10]
    // 0x9d419c: cmp             w2, NULL
    // 0x9d41a0: b.eq            #0x9d428c
    // 0x9d41a4: LoadField: r3 = r1->field_43
    //     0x9d41a4: ldur            w3, [x1, #0x43]
    // 0x9d41a8: DecompressPointer r3
    //     0x9d41a8: add             x3, x3, HEAP, lsl #32
    // 0x9d41ac: stur            x3, [fp, #-8]
    // 0x9d41b0: cmp             w3, NULL
    // 0x9d41b4: b.eq            #0x9d4290
    // 0x9d41b8: r0 = Icon()
    //     0x9d41b8: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0x9d41bc: mov             x1, x0
    // 0x9d41c0: ldur            x0, [fp, #-0x20]
    // 0x9d41c4: stur            x1, [fp, #-0x18]
    // 0x9d41c8: StoreField: r1->field_b = r0
    //     0x9d41c8: stur            w0, [x1, #0xb]
    // 0x9d41cc: ldur            x0, [fp, #-8]
    // 0x9d41d0: StoreField: r1->field_f = r0
    //     0x9d41d0: stur            w0, [x1, #0xf]
    // 0x9d41d4: ldur            x0, [fp, #-0x10]
    // 0x9d41d8: StoreField: r1->field_23 = r0
    //     0x9d41d8: stur            w0, [x1, #0x23]
    // 0x9d41dc: r0 = Transform()
    //     0x9d41dc: bl              #0x9d3c68  ; AllocateTransformStub -> Transform (size=0x24)
    // 0x9d41e0: mov             x1, x0
    // 0x9d41e4: r0 = Instance_Alignment
    //     0x9d41e4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x9d41e8: ldr             x0, [x0, #0x898]
    // 0x9d41ec: stur            x1, [fp, #-8]
    // 0x9d41f0: ArrayStore: r1[0] = r0  ; List_4
    //     0x9d41f0: stur            w0, [x1, #0x17]
    // 0x9d41f4: r0 = true
    //     0x9d41f4: add             x0, NULL, #0x20  ; true
    // 0x9d41f8: StoreField: r1->field_1b = r0
    //     0x9d41f8: stur            w0, [x1, #0x1b]
    // 0x9d41fc: ldur            d0, [fp, #-0x28]
    // 0x9d4200: r0 = _computeRotation()
    //     0x9d4200: bl              #0x9d4294  ; [package:flutter/src/widgets/basic.dart] Transform::_computeRotation
    // 0x9d4204: ldur            x1, [fp, #-8]
    // 0x9d4208: StoreField: r1->field_f = r0
    //     0x9d4208: stur            w0, [x1, #0xf]
    //     0x9d420c: ldurb           w16, [x1, #-1]
    //     0x9d4210: ldurb           w17, [x0, #-1]
    //     0x9d4214: and             x16, x17, x16, lsr #2
    //     0x9d4218: tst             x16, HEAP, lsr #32
    //     0x9d421c: b.eq            #0x9d4224
    //     0x9d4220: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9d4224: ldur            x0, [fp, #-0x18]
    // 0x9d4228: StoreField: r1->field_b = r0
    //     0x9d4228: stur            w0, [x1, #0xb]
    //     0x9d422c: ldurb           w16, [x1, #-1]
    //     0x9d4230: ldurb           w17, [x0, #-1]
    //     0x9d4234: and             x16, x17, x16, lsr #2
    //     0x9d4238: tst             x16, HEAP, lsr #32
    //     0x9d423c: b.eq            #0x9d4244
    //     0x9d4240: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9d4244: mov             x0, x1
    // 0x9d4248: LeaveFrame
    //     0x9d4248: mov             SP, fp
    //     0x9d424c: ldp             fp, lr, [SP], #0x10
    // 0x9d4250: ret
    //     0x9d4250: ret             
    // 0x9d4254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d4254: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d4258: b               #0x9d4050
    // 0x9d425c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d425c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d4260: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d4260: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d4264: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9d4264: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9d4268: r9 = _value
    //     0x9d4268: ldr             x9, [PP, #0x4ea8]  ; [pp+0x4ea8] Field <AnimationController._value@441066280>: late (offset: 0x38)
    // 0x9d426c: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x9d426c: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x9d4270: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d4270: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d4274: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9d4274: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9d4278: r9 = _value
    //     0x9d4278: ldr             x9, [PP, #0x4ea8]  ; [pp+0x4ea8] Field <AnimationController._value@441066280>: late (offset: 0x38)
    // 0x9d427c: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x9d427c: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x9d4280: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d4280: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d4284: r9 = _value
    //     0x9d4284: ldr             x9, [PP, #0x4ea8]  ; [pp+0x4ea8] Field <AnimationController._value@441066280>: late (offset: 0x38)
    // 0x9d4288: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9d4288: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9d428c: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9d428c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9d4290: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9d4290: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7aaf4, size: 0x88
    // 0xa7aaf4: EnterFrame
    //     0xa7aaf4: stp             fp, lr, [SP, #-0x10]!
    //     0xa7aaf8: mov             fp, SP
    // 0xa7aafc: AllocStack(0x10)
    //     0xa7aafc: sub             SP, SP, #0x10
    // 0xa7ab00: SetupParameters(_ExpandableIconState this /* r1 => r0, fp-0x10 */)
    //     0xa7ab00: mov             x0, x1
    //     0xa7ab04: stur            x1, [fp, #-0x10]
    // 0xa7ab08: CheckStackOverflow
    //     0xa7ab08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7ab0c: cmp             SP, x16
    //     0xa7ab10: b.ls            #0xa7ab74
    // 0xa7ab14: LoadField: r3 = r0->field_23
    //     0xa7ab14: ldur            w3, [x0, #0x23]
    // 0xa7ab18: DecompressPointer r3
    //     0xa7ab18: add             x3, x3, HEAP, lsl #32
    // 0xa7ab1c: stur            x3, [fp, #-8]
    // 0xa7ab20: cmp             w3, NULL
    // 0xa7ab24: b.eq            #0xa7ab48
    // 0xa7ab28: mov             x2, x0
    // 0xa7ab2c: r1 = Function '_expandedStateChanged@1016245272':.
    //     0xa7ab2c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54688] AnonymousClosure: (0x92d588), in [package:expandable/expandable.dart] _ExpandableIconState::_expandedStateChanged (0x92d5c0)
    //     0xa7ab30: ldr             x1, [x1, #0x688]
    // 0xa7ab34: r0 = AllocateClosure()
    //     0xa7ab34: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7ab38: ldur            x1, [fp, #-8]
    // 0xa7ab3c: mov             x2, x0
    // 0xa7ab40: r0 = removeListener()
    //     0xa7ab40: bl              #0xa8a5dc  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xa7ab44: ldur            x0, [fp, #-0x10]
    // 0xa7ab48: LoadField: r1 = r0->field_1b
    //     0xa7ab48: ldur            w1, [x0, #0x1b]
    // 0xa7ab4c: DecompressPointer r1
    //     0xa7ab4c: add             x1, x1, HEAP, lsl #32
    // 0xa7ab50: cmp             w1, NULL
    // 0xa7ab54: b.eq            #0xa7ab5c
    // 0xa7ab58: r0 = dispose()
    //     0xa7ab58: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa7ab5c: ldur            x1, [fp, #-0x10]
    // 0xa7ab60: r0 = dispose()
    //     0xa7ab60: bl              #0xa7ab7c  ; [package:expandable/expandable.dart] __ExpandableIconState&State&SingleTickerProviderStateMixin::dispose
    // 0xa7ab64: r0 = Null
    //     0xa7ab64: mov             x0, NULL
    // 0xa7ab68: LeaveFrame
    //     0xa7ab68: mov             SP, fp
    //     0xa7ab6c: ldp             fp, lr, [SP], #0x10
    // 0xa7ab70: ret
    //     0xa7ab70: ret             
    // 0xa7ab74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7ab74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7ab78: b               #0xa7ab14
  }
}

// class id: 4367, size: 0x1c, field offset: 0x14
class _ExpandableNotifierState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x92c9e4, size: 0xb8
    // 0x92c9e4: EnterFrame
    //     0x92c9e4: stp             fp, lr, [SP, #-0x10]!
    //     0x92c9e8: mov             fp, SP
    // 0x92c9ec: AllocStack(0x10)
    //     0x92c9ec: sub             SP, SP, #0x10
    // 0x92c9f0: SetupParameters(_ExpandableNotifierState this /* r1 => r0, fp-0x8 */)
    //     0x92c9f0: mov             x0, x1
    //     0x92c9f4: stur            x1, [fp, #-8]
    // 0x92c9f8: CheckStackOverflow
    //     0x92c9f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92c9fc: cmp             SP, x16
    //     0x92ca00: b.ls            #0x92ca90
    // 0x92ca04: LoadField: r1 = r0->field_b
    //     0x92ca04: ldur            w1, [x0, #0xb]
    // 0x92ca08: DecompressPointer r1
    //     0x92ca08: add             x1, x1, HEAP, lsl #32
    // 0x92ca0c: cmp             w1, NULL
    // 0x92ca10: b.eq            #0x92ca98
    // 0x92ca14: r1 = <bool>
    //     0x92ca14: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0x92ca18: r0 = ExpandableController()
    //     0x92ca18: bl              #0x92cac0  ; AllocateExpandableControllerStub -> ExpandableController (size=0x2c)
    // 0x92ca1c: mov             x1, x0
    // 0x92ca20: r0 = false
    //     0x92ca20: add             x0, NULL, #0x30  ; false
    // 0x92ca24: stur            x1, [fp, #-0x10]
    // 0x92ca28: StoreField: r1->field_27 = r0
    //     0x92ca28: stur            w0, [x1, #0x27]
    // 0x92ca2c: StoreField: r1->field_7 = rZR
    //     0x92ca2c: stur            xzr, [x1, #7]
    // 0x92ca30: StoreField: r1->field_13 = rZR
    //     0x92ca30: stur            xzr, [x1, #0x13]
    // 0x92ca34: StoreField: r1->field_1b = rZR
    //     0x92ca34: stur            xzr, [x1, #0x1b]
    // 0x92ca38: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0x92ca38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x92ca3c: ldr             x0, [x0, #0xca8]
    //     0x92ca40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x92ca44: cmp             w0, w16
    //     0x92ca48: b.ne            #0x92ca54
    //     0x92ca4c: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0x92ca50: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x92ca54: mov             x1, x0
    // 0x92ca58: ldur            x0, [fp, #-0x10]
    // 0x92ca5c: StoreField: r0->field_f = r1
    //     0x92ca5c: stur            w1, [x0, #0xf]
    // 0x92ca60: ldur            x1, [fp, #-8]
    // 0x92ca64: StoreField: r1->field_13 = r0
    //     0x92ca64: stur            w0, [x1, #0x13]
    //     0x92ca68: ldurb           w16, [x1, #-1]
    //     0x92ca6c: ldurb           w17, [x0, #-1]
    //     0x92ca70: and             x16, x17, x16, lsr #2
    //     0x92ca74: tst             x16, HEAP, lsr #32
    //     0x92ca78: b.eq            #0x92ca80
    //     0x92ca7c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x92ca80: r0 = Null
    //     0x92ca80: mov             x0, NULL
    // 0x92ca84: LeaveFrame
    //     0x92ca84: mov             SP, fp
    //     0x92ca88: ldp             fp, lr, [SP], #0x10
    // 0x92ca8c: ret
    //     0x92ca8c: ret             
    // 0x92ca90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92ca90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92ca94: b               #0x92ca04
    // 0x92ca98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92ca98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x982b3c, size: 0xbc
    // 0x982b3c: EnterFrame
    //     0x982b3c: stp             fp, lr, [SP, #-0x10]!
    //     0x982b40: mov             fp, SP
    // 0x982b44: AllocStack(0x10)
    //     0x982b44: sub             SP, SP, #0x10
    // 0x982b48: SetupParameters(_ExpandableNotifierState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x982b48: mov             x0, x2
    //     0x982b4c: mov             x4, x1
    //     0x982b50: mov             x3, x2
    //     0x982b54: stur            x1, [fp, #-8]
    //     0x982b58: stur            x2, [fp, #-0x10]
    // 0x982b5c: r2 = Null
    //     0x982b5c: mov             x2, NULL
    // 0x982b60: r1 = Null
    //     0x982b60: mov             x1, NULL
    // 0x982b64: r4 = 60
    //     0x982b64: movz            x4, #0x3c
    // 0x982b68: branchIfSmi(r0, 0x982b74)
    //     0x982b68: tbz             w0, #0, #0x982b74
    // 0x982b6c: r4 = LoadClassIdInstr(r0)
    //     0x982b6c: ldur            x4, [x0, #-1]
    //     0x982b70: ubfx            x4, x4, #0xc, #0x14
    // 0x982b74: r17 = 4900
    //     0x982b74: movz            x17, #0x1324
    // 0x982b78: cmp             x4, x17
    // 0x982b7c: b.eq            #0x982b94
    // 0x982b80: r8 = ExpandableNotifier
    //     0x982b80: add             x8, PP, #0x54, lsl #12  ; [pp+0x546d8] Type: ExpandableNotifier
    //     0x982b84: ldr             x8, [x8, #0x6d8]
    // 0x982b88: r3 = Null
    //     0x982b88: add             x3, PP, #0x54, lsl #12  ; [pp+0x546e0] Null
    //     0x982b8c: ldr             x3, [x3, #0x6e0]
    // 0x982b90: r0 = ExpandableNotifier()
    //     0x982b90: bl              #0x92ca9c  ; IsType_ExpandableNotifier_Stub
    // 0x982b94: ldur            x3, [fp, #-8]
    // 0x982b98: LoadField: r2 = r3->field_7
    //     0x982b98: ldur            w2, [x3, #7]
    // 0x982b9c: DecompressPointer r2
    //     0x982b9c: add             x2, x2, HEAP, lsl #32
    // 0x982ba0: ldur            x0, [fp, #-0x10]
    // 0x982ba4: r1 = Null
    //     0x982ba4: mov             x1, NULL
    // 0x982ba8: cmp             w2, NULL
    // 0x982bac: b.eq            #0x982bd0
    // 0x982bb0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x982bb0: ldur            w4, [x2, #0x17]
    // 0x982bb4: DecompressPointer r4
    //     0x982bb4: add             x4, x4, HEAP, lsl #32
    // 0x982bb8: r8 = X0 bound StatefulWidget
    //     0x982bb8: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x982bbc: ldr             x8, [x8, #0x7f8]
    // 0x982bc0: LoadField: r9 = r4->field_7
    //     0x982bc0: ldur            x9, [x4, #7]
    // 0x982bc4: r3 = Null
    //     0x982bc4: add             x3, PP, #0x54, lsl #12  ; [pp+0x546f0] Null
    //     0x982bc8: ldr             x3, [x3, #0x6f0]
    // 0x982bcc: blr             x9
    // 0x982bd0: ldur            x1, [fp, #-8]
    // 0x982bd4: LoadField: r2 = r1->field_b
    //     0x982bd4: ldur            w2, [x1, #0xb]
    // 0x982bd8: DecompressPointer r2
    //     0x982bd8: add             x2, x2, HEAP, lsl #32
    // 0x982bdc: cmp             w2, NULL
    // 0x982be0: b.eq            #0x982bf4
    // 0x982be4: r0 = Null
    //     0x982be4: mov             x0, NULL
    // 0x982be8: LeaveFrame
    //     0x982be8: mov             SP, fp
    //     0x982bec: ldp             fp, lr, [SP], #0x10
    // 0x982bf0: ret
    //     0x982bf0: ret             
    // 0x982bf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x982bf4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0x9d3ea0, size: 0x60
    // 0x9d3ea0: EnterFrame
    //     0x9d3ea0: stp             fp, lr, [SP, #-0x10]!
    //     0x9d3ea4: mov             fp, SP
    // 0x9d3ea8: AllocStack(0x10)
    //     0x9d3ea8: sub             SP, SP, #0x10
    // 0x9d3eac: LoadField: r0 = r1->field_13
    //     0x9d3eac: ldur            w0, [x1, #0x13]
    // 0x9d3eb0: DecompressPointer r0
    //     0x9d3eb0: add             x0, x0, HEAP, lsl #32
    // 0x9d3eb4: stur            x0, [fp, #-0x10]
    // 0x9d3eb8: LoadField: r2 = r1->field_b
    //     0x9d3eb8: ldur            w2, [x1, #0xb]
    // 0x9d3ebc: DecompressPointer r2
    //     0x9d3ebc: add             x2, x2, HEAP, lsl #32
    // 0x9d3ec0: cmp             w2, NULL
    // 0x9d3ec4: b.eq            #0x9d3efc
    // 0x9d3ec8: LoadField: r3 = r2->field_13
    //     0x9d3ec8: ldur            w3, [x2, #0x13]
    // 0x9d3ecc: DecompressPointer r3
    //     0x9d3ecc: add             x3, x3, HEAP, lsl #32
    // 0x9d3ed0: stur            x3, [fp, #-8]
    // 0x9d3ed4: r1 = <ExpandableController>
    //     0x9d3ed4: add             x1, PP, #0x54, lsl #12  ; [pp+0x546d0] TypeArguments: <ExpandableController>
    //     0x9d3ed8: ldr             x1, [x1, #0x6d0]
    // 0x9d3edc: r0 = _ExpandableControllerNotifier()
    //     0x9d3edc: bl              #0x9d3f00  ; Allocate_ExpandableControllerNotifierStub -> _ExpandableControllerNotifier (size=0x18)
    // 0x9d3ee0: ldur            x1, [fp, #-0x10]
    // 0x9d3ee4: StoreField: r0->field_13 = r1
    //     0x9d3ee4: stur            w1, [x0, #0x13]
    // 0x9d3ee8: ldur            x1, [fp, #-8]
    // 0x9d3eec: StoreField: r0->field_b = r1
    //     0x9d3eec: stur            w1, [x0, #0xb]
    // 0x9d3ef0: LeaveFrame
    //     0x9d3ef0: mov             SP, fp
    //     0x9d3ef4: ldp             fp, lr, [SP], #0x10
    // 0x9d3ef8: ret
    //     0x9d3ef8: ret             
    // 0x9d3efc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d3efc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4679, size: 0x14, field offset: 0x10
class _ExpandableThemeNotifier extends InheritedWidget {
}

// class id: 4683, size: 0x18, field offset: 0x18
class _ExpandableControllerNotifier extends InheritedNotifier<dynamic> {
}

// class id: 4899, size: 0x10, field offset: 0xc
class ExpandableIcon extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa8ed94, size: 0x24
    // 0xa8ed94: EnterFrame
    //     0xa8ed94: stp             fp, lr, [SP, #-0x10]!
    //     0xa8ed98: mov             fp, SP
    // 0xa8ed9c: mov             x0, x1
    // 0xa8eda0: r1 = <ExpandableIcon>
    //     0xa8eda0: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d008] TypeArguments: <ExpandableIcon>
    //     0xa8eda4: ldr             x1, [x1, #8]
    // 0xa8eda8: r0 = _ExpandableIconState()
    //     0xa8eda8: bl              #0xa8edb8  ; Allocate_ExpandableIconStateStub -> _ExpandableIconState (size=0x28)
    // 0xa8edac: LeaveFrame
    //     0xa8edac: mov             SP, fp
    //     0xa8edb0: ldp             fp, lr, [SP], #0x10
    // 0xa8edb4: ret
    //     0xa8edb4: ret             
  }
}

// class id: 4900, size: 0x18, field offset: 0xc
class ExpandableNotifier extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa8ed64, size: 0x24
    // 0xa8ed64: EnterFrame
    //     0xa8ed64: stp             fp, lr, [SP, #-0x10]!
    //     0xa8ed68: mov             fp, SP
    // 0xa8ed6c: mov             x0, x1
    // 0xa8ed70: r1 = <ExpandableNotifier>
    //     0xa8ed70: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4cfd0] TypeArguments: <ExpandableNotifier>
    //     0xa8ed74: ldr             x1, [x1, #0xfd0]
    // 0xa8ed78: r0 = _ExpandableNotifierState()
    //     0xa8ed78: bl              #0xa8ed88  ; Allocate_ExpandableNotifierStateStub -> _ExpandableNotifierState (size=0x1c)
    // 0xa8ed7c: LeaveFrame
    //     0xa8ed7c: mov             SP, fp
    //     0xa8ed80: ldp             fp, lr, [SP], #0x10
    // 0xa8ed84: ret
    //     0xa8ed84: ret             
  }
}

// class id: 5419, size: 0x14, field offset: 0xc
class ExpandableButton extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xa972ec, size: 0x130
    // 0xa972ec: EnterFrame
    //     0xa972ec: stp             fp, lr, [SP, #-0x10]!
    //     0xa972f0: mov             fp, SP
    // 0xa972f4: AllocStack(0x20)
    //     0xa972f4: sub             SP, SP, #0x20
    // 0xa972f8: SetupParameters(ExpandableButton this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa972f8: mov             x0, x2
    //     0xa972fc: stur            x2, [fp, #-0x10]
    //     0xa97300: mov             x2, x1
    //     0xa97304: stur            x1, [fp, #-8]
    // 0xa97308: CheckStackOverflow
    //     0xa97308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9730c: cmp             SP, x16
    //     0xa97310: b.ls            #0xa9740c
    // 0xa97314: mov             x1, x0
    // 0xa97318: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa97318: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa9731c: r0 = of()
    //     0xa9731c: bl              #0x92cd04  ; [package:expandable/expandable.dart] ExpandableController::of
    // 0xa97320: mov             x3, x0
    // 0xa97324: ldur            x0, [fp, #-8]
    // 0xa97328: stur            x3, [fp, #-0x18]
    // 0xa9732c: LoadField: r1 = r0->field_f
    //     0xa9732c: ldur            w1, [x0, #0xf]
    // 0xa97330: DecompressPointer r1
    //     0xa97330: add             x1, x1, HEAP, lsl #32
    // 0xa97334: ldur            x2, [fp, #-0x10]
    // 0xa97338: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa97338: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa9733c: r0 = withDefaults()
    //     0xa9733c: bl              #0x92cdbc  ; [package:expandable/expandable.dart] ExpandableThemeData::withDefaults
    // 0xa97340: stur            x0, [fp, #-0x10]
    // 0xa97344: LoadField: r1 = r0->field_b
    //     0xa97344: ldur            w1, [x0, #0xb]
    // 0xa97348: DecompressPointer r1
    //     0xa97348: add             x1, x1, HEAP, lsl #32
    // 0xa9734c: cmp             w1, NULL
    // 0xa97350: b.eq            #0xa97414
    // 0xa97354: tbnz            w1, #4, #0xa97400
    // 0xa97358: ldur            x2, [fp, #-0x18]
    // 0xa9735c: cmp             w2, NULL
    // 0xa97360: b.ne            #0xa9736c
    // 0xa97364: r2 = Null
    //     0xa97364: mov             x2, NULL
    // 0xa97368: b               #0xa97380
    // 0xa9736c: r1 = Function 'toggle':.
    //     0xa9736c: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4cff8] AnonymousClosure: (0xa96fb4), in [package:expandable/expandable.dart] ExpandableController::toggle (0xa96f78)
    //     0xa97370: ldr             x1, [x1, #0xff8]
    // 0xa97374: r0 = AllocateClosure()
    //     0xa97374: bl              #0xec1630  ; AllocateClosureStub
    // 0xa97378: mov             x2, x0
    // 0xa9737c: ldur            x0, [fp, #-0x10]
    // 0xa97380: ldur            x1, [fp, #-8]
    // 0xa97384: stur            x2, [fp, #-0x20]
    // 0xa97388: LoadField: r3 = r1->field_b
    //     0xa97388: ldur            w3, [x1, #0xb]
    // 0xa9738c: DecompressPointer r3
    //     0xa9738c: add             x3, x3, HEAP, lsl #32
    // 0xa97390: stur            x3, [fp, #-0x18]
    // 0xa97394: LoadField: r1 = r0->field_57
    //     0xa97394: ldur            w1, [x0, #0x57]
    // 0xa97398: DecompressPointer r1
    //     0xa97398: add             x1, x1, HEAP, lsl #32
    // 0xa9739c: stur            x1, [fp, #-8]
    // 0xa973a0: cmp             w1, NULL
    // 0xa973a4: b.eq            #0xa97418
    // 0xa973a8: r0 = InkWell()
    //     0xa973a8: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa973ac: mov             x1, x0
    // 0xa973b0: ldur            x0, [fp, #-0x18]
    // 0xa973b4: StoreField: r1->field_b = r0
    //     0xa973b4: stur            w0, [x1, #0xb]
    // 0xa973b8: ldur            x0, [fp, #-0x20]
    // 0xa973bc: StoreField: r1->field_f = r0
    //     0xa973bc: stur            w0, [x1, #0xf]
    // 0xa973c0: r0 = true
    //     0xa973c0: add             x0, NULL, #0x20  ; true
    // 0xa973c4: StoreField: r1->field_43 = r0
    //     0xa973c4: stur            w0, [x1, #0x43]
    // 0xa973c8: r2 = Instance_BoxShape
    //     0xa973c8: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa973cc: ldr             x2, [x2, #0xca8]
    // 0xa973d0: StoreField: r1->field_47 = r2
    //     0xa973d0: stur            w2, [x1, #0x47]
    // 0xa973d4: ldur            x2, [fp, #-8]
    // 0xa973d8: StoreField: r1->field_4f = r2
    //     0xa973d8: stur            w2, [x1, #0x4f]
    // 0xa973dc: StoreField: r1->field_6f = r0
    //     0xa973dc: stur            w0, [x1, #0x6f]
    // 0xa973e0: r2 = false
    //     0xa973e0: add             x2, NULL, #0x30  ; false
    // 0xa973e4: StoreField: r1->field_73 = r2
    //     0xa973e4: stur            w2, [x1, #0x73]
    // 0xa973e8: StoreField: r1->field_83 = r0
    //     0xa973e8: stur            w0, [x1, #0x83]
    // 0xa973ec: StoreField: r1->field_7b = r2
    //     0xa973ec: stur            w2, [x1, #0x7b]
    // 0xa973f0: mov             x0, x1
    // 0xa973f4: LeaveFrame
    //     0xa973f4: mov             SP, fp
    //     0xa973f8: ldp             fp, lr, [SP], #0x10
    // 0xa973fc: ret
    //     0xa973fc: ret             
    // 0xa97400: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0xa97400: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0xa97404: r0 = Throw()
    //     0xa97404: bl              #0xec04b8  ; ThrowStub
    // 0xa97408: brk             #0
    // 0xa9740c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9740c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa97410: b               #0xa97314
    // 0xa97414: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa97414: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa97418: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa97418: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5420, size: 0x24, field offset: 0xc
class ExpandablePanel extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xa96aa4, size: 0x228
    // 0xa96aa4: EnterFrame
    //     0xa96aa4: stp             fp, lr, [SP, #-0x10]!
    //     0xa96aa8: mov             fp, SP
    // 0xa96aac: AllocStack(0x28)
    //     0xa96aac: sub             SP, SP, #0x28
    // 0xa96ab0: SetupParameters(ExpandablePanel this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa96ab0: stur            x1, [fp, #-8]
    //     0xa96ab4: stur            x2, [fp, #-0x10]
    // 0xa96ab8: CheckStackOverflow
    //     0xa96ab8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa96abc: cmp             SP, x16
    //     0xa96ac0: b.ls            #0xa96cc4
    // 0xa96ac4: r1 = 5
    //     0xa96ac4: movz            x1, #0x5
    // 0xa96ac8: r0 = AllocateContext()
    //     0xa96ac8: bl              #0xec126c  ; AllocateContextStub
    // 0xa96acc: mov             x3, x0
    // 0xa96ad0: ldur            x0, [fp, #-8]
    // 0xa96ad4: stur            x3, [fp, #-0x18]
    // 0xa96ad8: StoreField: r3->field_f = r0
    //     0xa96ad8: stur            w0, [x3, #0xf]
    // 0xa96adc: ldur            x2, [fp, #-0x10]
    // 0xa96ae0: StoreField: r3->field_13 = r2
    //     0xa96ae0: stur            w2, [x3, #0x13]
    // 0xa96ae4: LoadField: r1 = r0->field_1f
    //     0xa96ae4: ldur            w1, [x0, #0x1f]
    // 0xa96ae8: DecompressPointer r1
    //     0xa96ae8: add             x1, x1, HEAP, lsl #32
    // 0xa96aec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa96aec: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa96af0: r0 = withDefaults()
    //     0xa96af0: bl              #0x92cdbc  ; [package:expandable/expandable.dart] ExpandableThemeData::withDefaults
    // 0xa96af4: ldur            x3, [fp, #-0x18]
    // 0xa96af8: ArrayStore: r3[0] = r0  ; List_4
    //     0xa96af8: stur            w0, [x3, #0x17]
    //     0xa96afc: ldurb           w16, [x3, #-1]
    //     0xa96b00: ldurb           w17, [x0, #-1]
    //     0xa96b04: and             x16, x17, x16, lsr #2
    //     0xa96b08: tst             x16, HEAP, lsr #32
    //     0xa96b0c: b.eq            #0xa96b14
    //     0xa96b10: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa96b14: mov             x2, x3
    // 0xa96b18: r1 = Function 'buildHeaderRow':.
    //     0xa96b18: add             x1, PP, #0x43, lsl #12  ; [pp+0x434c8] AnonymousClosure: (0xa97044), in [package:expandable/expandable.dart] ExpandablePanel::build (0xa96aa4)
    //     0xa96b1c: ldr             x1, [x1, #0x4c8]
    // 0xa96b20: r0 = AllocateClosure()
    //     0xa96b20: bl              #0xec1630  ; AllocateClosureStub
    // 0xa96b24: mov             x4, x0
    // 0xa96b28: ldur            x3, [fp, #-0x18]
    // 0xa96b2c: stur            x4, [fp, #-8]
    // 0xa96b30: StoreField: r3->field_1b = r0
    //     0xa96b30: stur            w0, [x3, #0x1b]
    //     0xa96b34: ldurb           w16, [x3, #-1]
    //     0xa96b38: ldurb           w17, [x0, #-1]
    //     0xa96b3c: and             x16, x17, x16, lsr #2
    //     0xa96b40: tst             x16, HEAP, lsr #32
    //     0xa96b44: b.eq            #0xa96b4c
    //     0xa96b48: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa96b4c: mov             x2, x3
    // 0xa96b50: r1 = Function 'buildBody':.
    //     0xa96b50: add             x1, PP, #0x43, lsl #12  ; [pp+0x434d0] AnonymousClosure: (0xa96cd8), in [package:expandable/expandable.dart] ExpandablePanel::build (0xa96aa4)
    //     0xa96b54: ldr             x1, [x1, #0x4d0]
    // 0xa96b58: r0 = AllocateClosure()
    //     0xa96b58: bl              #0xec1630  ; AllocateClosureStub
    // 0xa96b5c: mov             x2, x0
    // 0xa96b60: ldur            x1, [fp, #-0x18]
    // 0xa96b64: stur            x2, [fp, #-0x10]
    // 0xa96b68: StoreField: r1->field_1f = r0
    //     0xa96b68: stur            w0, [x1, #0x1f]
    //     0xa96b6c: ldurb           w16, [x1, #-1]
    //     0xa96b70: ldurb           w17, [x0, #-1]
    //     0xa96b74: and             x16, x17, x16, lsr #2
    //     0xa96b78: tst             x16, HEAP, lsr #32
    //     0xa96b7c: b.eq            #0xa96b84
    //     0xa96b80: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa96b84: ldur            x16, [fp, #-8]
    // 0xa96b88: str             x16, [SP]
    // 0xa96b8c: ldur            x0, [fp, #-8]
    // 0xa96b90: ClosureCall
    //     0xa96b90: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xa96b94: ldur            x2, [x0, #0x1f]
    //     0xa96b98: blr             x2
    // 0xa96b9c: mov             x1, x0
    // 0xa96ba0: stur            x1, [fp, #-8]
    // 0xa96ba4: ldur            x16, [fp, #-0x10]
    // 0xa96ba8: str             x16, [SP]
    // 0xa96bac: ldur            x0, [fp, #-0x10]
    // 0xa96bb0: ClosureCall
    //     0xa96bb0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xa96bb4: ldur            x2, [x0, #0x1f]
    //     0xa96bb8: blr             x2
    // 0xa96bbc: r1 = Null
    //     0xa96bbc: mov             x1, NULL
    // 0xa96bc0: r2 = 4
    //     0xa96bc0: movz            x2, #0x4
    // 0xa96bc4: stur            x0, [fp, #-0x10]
    // 0xa96bc8: r0 = AllocateArray()
    //     0xa96bc8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa96bcc: mov             x2, x0
    // 0xa96bd0: ldur            x0, [fp, #-8]
    // 0xa96bd4: stur            x2, [fp, #-0x20]
    // 0xa96bd8: StoreField: r2->field_f = r0
    //     0xa96bd8: stur            w0, [x2, #0xf]
    // 0xa96bdc: ldur            x0, [fp, #-0x10]
    // 0xa96be0: StoreField: r2->field_13 = r0
    //     0xa96be0: stur            w0, [x2, #0x13]
    // 0xa96be4: r1 = <Widget>
    //     0xa96be4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa96be8: r0 = AllocateGrowableArray()
    //     0xa96be8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa96bec: mov             x1, x0
    // 0xa96bf0: ldur            x0, [fp, #-0x20]
    // 0xa96bf4: stur            x1, [fp, #-8]
    // 0xa96bf8: StoreField: r1->field_f = r0
    //     0xa96bf8: stur            w0, [x1, #0xf]
    // 0xa96bfc: r0 = 4
    //     0xa96bfc: movz            x0, #0x4
    // 0xa96c00: StoreField: r1->field_b = r0
    //     0xa96c00: stur            w0, [x1, #0xb]
    // 0xa96c04: r0 = Column()
    //     0xa96c04: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa96c08: mov             x2, x0
    // 0xa96c0c: r0 = Instance_Axis
    //     0xa96c0c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa96c10: stur            x2, [fp, #-0x10]
    // 0xa96c14: StoreField: r2->field_f = r0
    //     0xa96c14: stur            w0, [x2, #0xf]
    // 0xa96c18: r0 = Instance_MainAxisAlignment
    //     0xa96c18: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xa96c1c: ldr             x0, [x0, #0x730]
    // 0xa96c20: StoreField: r2->field_13 = r0
    //     0xa96c20: stur            w0, [x2, #0x13]
    // 0xa96c24: r0 = Instance_MainAxisSize
    //     0xa96c24: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xa96c28: ldr             x0, [x0, #0x738]
    // 0xa96c2c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa96c2c: stur            w0, [x2, #0x17]
    // 0xa96c30: r0 = Instance_CrossAxisAlignment
    //     0xa96c30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0xa96c34: ldr             x0, [x0, #0xf50]
    // 0xa96c38: StoreField: r2->field_1b = r0
    //     0xa96c38: stur            w0, [x2, #0x1b]
    // 0xa96c3c: r0 = Instance_VerticalDirection
    //     0xa96c3c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa96c40: ldr             x0, [x0, #0x748]
    // 0xa96c44: StoreField: r2->field_23 = r0
    //     0xa96c44: stur            w0, [x2, #0x23]
    // 0xa96c48: r0 = Instance_Clip
    //     0xa96c48: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa96c4c: ldr             x0, [x0, #0x750]
    // 0xa96c50: StoreField: r2->field_2b = r0
    //     0xa96c50: stur            w0, [x2, #0x2b]
    // 0xa96c54: StoreField: r2->field_2f = rZR
    //     0xa96c54: stur            xzr, [x2, #0x2f]
    // 0xa96c58: ldur            x0, [fp, #-8]
    // 0xa96c5c: StoreField: r2->field_b = r0
    //     0xa96c5c: stur            w0, [x2, #0xb]
    // 0xa96c60: mov             x1, x2
    // 0xa96c64: r0 = forceCompileTimeTreeShaking()
    //     0xa96c64: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xa96c68: ldur            x0, [fp, #-0x18]
    // 0xa96c6c: LoadField: r1 = r0->field_13
    //     0xa96c6c: ldur            w1, [x0, #0x13]
    // 0xa96c70: DecompressPointer r1
    //     0xa96c70: add             x1, x1, HEAP, lsl #32
    // 0xa96c74: r16 = false
    //     0xa96c74: add             x16, NULL, #0x30  ; false
    // 0xa96c78: str             x16, [SP]
    // 0xa96c7c: r4 = const [0, 0x2, 0x1, 0x1, rebuildOnChange, 0x1, null]
    //     0xa96c7c: add             x4, PP, #0x43, lsl #12  ; [pp+0x434d8] List(7) [0, 0x2, 0x1, 0x1, "rebuildOnChange", 0x1, Null]
    //     0xa96c80: ldr             x4, [x4, #0x4d8]
    // 0xa96c84: r0 = of()
    //     0xa96c84: bl              #0x92cd04  ; [package:expandable/expandable.dart] ExpandableController::of
    // 0xa96c88: cmp             w0, NULL
    // 0xa96c8c: b.ne            #0xa96cb4
    // 0xa96c90: ldur            x0, [fp, #-0x10]
    // 0xa96c94: r0 = ExpandableNotifier()
    //     0xa96c94: bl              #0xa96ccc  ; AllocateExpandableNotifierStub -> ExpandableNotifier (size=0x18)
    // 0xa96c98: mov             x1, x0
    // 0xa96c9c: ldur            x0, [fp, #-0x10]
    // 0xa96ca0: StoreField: r1->field_13 = r0
    //     0xa96ca0: stur            w0, [x1, #0x13]
    // 0xa96ca4: mov             x0, x1
    // 0xa96ca8: LeaveFrame
    //     0xa96ca8: mov             SP, fp
    //     0xa96cac: ldp             fp, lr, [SP], #0x10
    // 0xa96cb0: ret
    //     0xa96cb0: ret             
    // 0xa96cb4: ldur            x0, [fp, #-0x10]
    // 0xa96cb8: LeaveFrame
    //     0xa96cb8: mov             SP, fp
    //     0xa96cbc: ldp             fp, lr, [SP], #0x10
    // 0xa96cc0: ret
    //     0xa96cc0: ret             
    // 0xa96cc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa96cc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa96cc8: b               #0xa96ac4
  }
  [closure] Widget buildBody(dynamic) {
    // ** addr: 0xa96cd8, size: 0x244
    // 0xa96cd8: EnterFrame
    //     0xa96cd8: stp             fp, lr, [SP, #-0x10]!
    //     0xa96cdc: mov             fp, SP
    // 0xa96ce0: AllocStack(0x68)
    //     0xa96ce0: sub             SP, SP, #0x68
    // 0xa96ce4: SetupParameters()
    //     0xa96ce4: ldr             x0, [fp, #0x10]
    //     0xa96ce8: ldur            w3, [x0, #0x17]
    //     0xa96cec: add             x3, x3, HEAP, lsl #32
    //     0xa96cf0: stur            x3, [fp, #-8]
    // 0xa96cf4: CheckStackOverflow
    //     0xa96cf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa96cf8: cmp             SP, x16
    //     0xa96cfc: b.ls            #0xa96f08
    // 0xa96d00: mov             x2, x3
    // 0xa96d04: r1 = Function '<anonymous closure>':.
    //     0xa96d04: add             x1, PP, #0x43, lsl #12  ; [pp+0x434e0] AnonymousClosure: (0xa96fec), in [package:expandable/expandable.dart] ExpandablePanel::build (0xa96aa4)
    //     0xa96d08: ldr             x1, [x1, #0x4e0]
    // 0xa96d0c: r0 = AllocateClosure()
    //     0xa96d0c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa96d10: ldur            x2, [fp, #-8]
    // 0xa96d14: stur            x0, [fp, #-0x38]
    // 0xa96d18: LoadField: r1 = r2->field_13
    //     0xa96d18: ldur            w1, [x2, #0x13]
    // 0xa96d1c: DecompressPointer r1
    //     0xa96d1c: add             x1, x1, HEAP, lsl #32
    // 0xa96d20: stur            x1, [fp, #-0x30]
    // 0xa96d24: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa96d24: ldur            w3, [x2, #0x17]
    // 0xa96d28: DecompressPointer r3
    //     0xa96d28: add             x3, x3, HEAP, lsl #32
    // 0xa96d2c: stur            x3, [fp, #-0x28]
    // 0xa96d30: LoadField: r4 = r3->field_37
    //     0xa96d30: ldur            w4, [x3, #0x37]
    // 0xa96d34: DecompressPointer r4
    //     0xa96d34: add             x4, x4, HEAP, lsl #32
    // 0xa96d38: stur            x4, [fp, #-0x20]
    // 0xa96d3c: cmp             w4, NULL
    // 0xa96d40: b.eq            #0xa96f10
    // 0xa96d44: LoadField: r5 = r3->field_2b
    //     0xa96d44: ldur            w5, [x3, #0x2b]
    // 0xa96d48: DecompressPointer r5
    //     0xa96d48: add             x5, x5, HEAP, lsl #32
    // 0xa96d4c: cmp             w5, NULL
    // 0xa96d50: b.eq            #0xa96f14
    // 0xa96d54: LoadField: r6 = r5->field_7
    //     0xa96d54: ldur            x6, [x5, #7]
    // 0xa96d58: stur            x6, [fp, #-0x18]
    // 0xa96d5c: cmp             x6, #1
    // 0xa96d60: b.gt            #0xa96d84
    // 0xa96d64: cmp             x6, #0
    // 0xa96d68: b.gt            #0xa96d78
    // 0xa96d6c: r5 = Instance_Alignment
    //     0xa96d6c: add             x5, PP, #0x25, lsl #12  ; [pp+0x25370] Obj!Alignment@e13e11
    //     0xa96d70: ldr             x5, [x5, #0x370]
    // 0xa96d74: b               #0xa96d8c
    // 0xa96d78: r5 = Instance_Alignment
    //     0xa96d78: add             x5, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!Alignment@e13eb1
    //     0xa96d7c: ldr             x5, [x5, #0xe0]
    // 0xa96d80: b               #0xa96d8c
    // 0xa96d84: r5 = Instance_Alignment
    //     0xa96d84: add             x5, PP, #0x2b, lsl #12  ; [pp+0x2b5d8] Obj!Alignment@e13f11
    //     0xa96d88: ldr             x5, [x5, #0x5d8]
    // 0xa96d8c: stur            x5, [fp, #-0x10]
    // 0xa96d90: r0 = Align()
    //     0xa96d90: bl              #0x9d4ff8  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa96d94: mov             x1, x0
    // 0xa96d98: ldur            x0, [fp, #-0x10]
    // 0xa96d9c: stur            x1, [fp, #-0x40]
    // 0xa96da0: StoreField: r1->field_f = r0
    //     0xa96da0: stur            w0, [x1, #0xf]
    // 0xa96da4: r0 = Instance_SizedBox
    //     0xa96da4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xa96da8: ldr             x0, [x0, #0xc40]
    // 0xa96dac: StoreField: r1->field_b = r0
    //     0xa96dac: stur            w0, [x1, #0xb]
    // 0xa96db0: ldur            x0, [fp, #-0x20]
    // 0xa96db4: tbz             w0, #4, #0xa96dc0
    // 0xa96db8: mov             x3, x1
    // 0xa96dbc: b               #0xa96e00
    // 0xa96dc0: r0 = GestureDetector()
    //     0xa96dc0: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa96dc4: ldur            x2, [fp, #-8]
    // 0xa96dc8: r1 = Function '<anonymous closure>':.
    //     0xa96dc8: add             x1, PP, #0x43, lsl #12  ; [pp+0x434e8] AnonymousClosure: (0xa96f1c), of [package:expandable/expandable.dart] ExpandablePanel
    //     0xa96dcc: ldr             x1, [x1, #0x4e8]
    // 0xa96dd0: stur            x0, [fp, #-0x10]
    // 0xa96dd4: r0 = AllocateClosure()
    //     0xa96dd4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa96dd8: r16 = Instance_HitTestBehavior
    //     0xa96dd8: add             x16, PP, #0x25, lsl #12  ; [pp+0x251d0] Obj!HitTestBehavior@e358e1
    //     0xa96ddc: ldr             x16, [x16, #0x1d0]
    // 0xa96de0: ldur            lr, [fp, #-0x40]
    // 0xa96de4: stp             lr, x16, [SP, #8]
    // 0xa96de8: str             x0, [SP]
    // 0xa96dec: ldur            x1, [fp, #-0x10]
    // 0xa96df0: r4 = const [0, 0x4, 0x3, 0x1, behavior, 0x1, child, 0x2, onTap, 0x3, null]
    //     0xa96df0: add             x4, PP, #0x43, lsl #12  ; [pp+0x434f0] List(11) [0, 0x4, 0x3, 0x1, "behavior", 0x1, "child", 0x2, "onTap", 0x3, Null]
    //     0xa96df4: ldr             x4, [x4, #0x4f0]
    // 0xa96df8: r0 = GestureDetector()
    //     0xa96df8: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa96dfc: ldur            x3, [fp, #-0x10]
    // 0xa96e00: ldur            x2, [fp, #-8]
    // 0xa96e04: ldur            x0, [fp, #-0x28]
    // 0xa96e08: ldur            x1, [fp, #-0x18]
    // 0xa96e0c: stur            x3, [fp, #-0x48]
    // 0xa96e10: LoadField: r4 = r2->field_f
    //     0xa96e10: ldur            w4, [x2, #0xf]
    // 0xa96e14: DecompressPointer r4
    //     0xa96e14: add             x4, x4, HEAP, lsl #32
    // 0xa96e18: LoadField: r5 = r4->field_13
    //     0xa96e18: ldur            w5, [x4, #0x13]
    // 0xa96e1c: DecompressPointer r5
    //     0xa96e1c: add             x5, x5, HEAP, lsl #32
    // 0xa96e20: stur            x5, [fp, #-0x40]
    // 0xa96e24: LoadField: r4 = r0->field_3b
    //     0xa96e24: ldur            w4, [x0, #0x3b]
    // 0xa96e28: DecompressPointer r4
    //     0xa96e28: add             x4, x4, HEAP, lsl #32
    // 0xa96e2c: stur            x4, [fp, #-0x20]
    // 0xa96e30: cmp             w4, NULL
    // 0xa96e34: b.eq            #0xa96f18
    // 0xa96e38: cmp             x1, #1
    // 0xa96e3c: b.gt            #0xa96e60
    // 0xa96e40: cmp             x1, #0
    // 0xa96e44: b.gt            #0xa96e54
    // 0xa96e48: r0 = Instance_Alignment
    //     0xa96e48: add             x0, PP, #0x25, lsl #12  ; [pp+0x25370] Obj!Alignment@e13e11
    //     0xa96e4c: ldr             x0, [x0, #0x370]
    // 0xa96e50: b               #0xa96e68
    // 0xa96e54: r0 = Instance_Alignment
    //     0xa96e54: add             x0, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!Alignment@e13eb1
    //     0xa96e58: ldr             x0, [x0, #0xe0]
    // 0xa96e5c: b               #0xa96e68
    // 0xa96e60: r0 = Instance_Alignment
    //     0xa96e60: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b5d8] Obj!Alignment@e13f11
    //     0xa96e64: ldr             x0, [x0, #0x5d8]
    // 0xa96e68: stur            x0, [fp, #-0x10]
    // 0xa96e6c: r0 = Align()
    //     0xa96e6c: bl              #0x9d4ff8  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa96e70: mov             x1, x0
    // 0xa96e74: ldur            x0, [fp, #-0x10]
    // 0xa96e78: stur            x1, [fp, #-0x28]
    // 0xa96e7c: StoreField: r1->field_f = r0
    //     0xa96e7c: stur            w0, [x1, #0xf]
    // 0xa96e80: ldur            x0, [fp, #-0x40]
    // 0xa96e84: StoreField: r1->field_b = r0
    //     0xa96e84: stur            w0, [x1, #0xb]
    // 0xa96e88: ldur            x0, [fp, #-0x20]
    // 0xa96e8c: tbz             w0, #4, #0xa96e98
    // 0xa96e90: mov             x0, x1
    // 0xa96e94: b               #0xa96ed8
    // 0xa96e98: r0 = GestureDetector()
    //     0xa96e98: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa96e9c: ldur            x2, [fp, #-8]
    // 0xa96ea0: r1 = Function '<anonymous closure>':.
    //     0xa96ea0: add             x1, PP, #0x43, lsl #12  ; [pp+0x434e8] AnonymousClosure: (0xa96f1c), of [package:expandable/expandable.dart] ExpandablePanel
    //     0xa96ea4: ldr             x1, [x1, #0x4e8]
    // 0xa96ea8: stur            x0, [fp, #-8]
    // 0xa96eac: r0 = AllocateClosure()
    //     0xa96eac: bl              #0xec1630  ; AllocateClosureStub
    // 0xa96eb0: r16 = Instance_HitTestBehavior
    //     0xa96eb0: add             x16, PP, #0x25, lsl #12  ; [pp+0x251d0] Obj!HitTestBehavior@e358e1
    //     0xa96eb4: ldr             x16, [x16, #0x1d0]
    // 0xa96eb8: ldur            lr, [fp, #-0x28]
    // 0xa96ebc: stp             lr, x16, [SP, #8]
    // 0xa96ec0: str             x0, [SP]
    // 0xa96ec4: ldur            x1, [fp, #-8]
    // 0xa96ec8: r4 = const [0, 0x4, 0x3, 0x1, behavior, 0x1, child, 0x2, onTap, 0x3, null]
    //     0xa96ec8: add             x4, PP, #0x43, lsl #12  ; [pp+0x434f0] List(11) [0, 0x4, 0x3, 0x1, "behavior", 0x1, "child", 0x2, "onTap", 0x3, Null]
    //     0xa96ecc: ldr             x4, [x4, #0x4f0]
    // 0xa96ed0: r0 = GestureDetector()
    //     0xa96ed0: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa96ed4: ldur            x0, [fp, #-8]
    // 0xa96ed8: ldur            x16, [fp, #-0x38]
    // 0xa96edc: ldur            lr, [fp, #-0x30]
    // 0xa96ee0: stp             lr, x16, [SP, #0x10]
    // 0xa96ee4: ldur            x16, [fp, #-0x48]
    // 0xa96ee8: stp             x0, x16, [SP]
    // 0xa96eec: ldur            x0, [fp, #-0x38]
    // 0xa96ef0: ClosureCall
    //     0xa96ef0: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xa96ef4: ldur            x2, [x0, #0x1f]
    //     0xa96ef8: blr             x2
    // 0xa96efc: LeaveFrame
    //     0xa96efc: mov             SP, fp
    //     0xa96f00: ldp             fp, lr, [SP], #0x10
    // 0xa96f04: ret
    //     0xa96f04: ret             
    // 0xa96f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa96f08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa96f0c: b               #0xa96d00
    // 0xa96f10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa96f10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa96f14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa96f14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa96f18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa96f18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa96f1c, size: 0x5c
    // 0xa96f1c: EnterFrame
    //     0xa96f1c: stp             fp, lr, [SP, #-0x10]!
    //     0xa96f20: mov             fp, SP
    // 0xa96f24: ldr             x0, [fp, #0x10]
    // 0xa96f28: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa96f28: ldur            w1, [x0, #0x17]
    // 0xa96f2c: DecompressPointer r1
    //     0xa96f2c: add             x1, x1, HEAP, lsl #32
    // 0xa96f30: CheckStackOverflow
    //     0xa96f30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa96f34: cmp             SP, x16
    //     0xa96f38: b.ls            #0xa96f70
    // 0xa96f3c: LoadField: r0 = r1->field_13
    //     0xa96f3c: ldur            w0, [x1, #0x13]
    // 0xa96f40: DecompressPointer r0
    //     0xa96f40: add             x0, x0, HEAP, lsl #32
    // 0xa96f44: mov             x1, x0
    // 0xa96f48: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa96f48: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa96f4c: r0 = of()
    //     0xa96f4c: bl              #0x92cd04  ; [package:expandable/expandable.dart] ExpandableController::of
    // 0xa96f50: cmp             w0, NULL
    // 0xa96f54: b.eq            #0xa96f60
    // 0xa96f58: mov             x1, x0
    // 0xa96f5c: r0 = toggle()
    //     0xa96f5c: bl              #0xa96f78  ; [package:expandable/expandable.dart] ExpandableController::toggle
    // 0xa96f60: r0 = Null
    //     0xa96f60: mov             x0, NULL
    // 0xa96f64: LeaveFrame
    //     0xa96f64: mov             SP, fp
    //     0xa96f68: ldp             fp, lr, [SP], #0x10
    // 0xa96f6c: ret
    //     0xa96f6c: ret             
    // 0xa96f70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa96f70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa96f74: b               #0xa96f3c
  }
  [closure] Expandable <anonymous closure>(dynamic, BuildContext, Widget, Widget) {
    // ** addr: 0xa96fec, size: 0x4c
    // 0xa96fec: EnterFrame
    //     0xa96fec: stp             fp, lr, [SP, #-0x10]!
    //     0xa96ff0: mov             fp, SP
    // 0xa96ff4: AllocStack(0x8)
    //     0xa96ff4: sub             SP, SP, #8
    // 0xa96ff8: SetupParameters()
    //     0xa96ff8: ldr             x0, [fp, #0x28]
    //     0xa96ffc: ldur            w1, [x0, #0x17]
    //     0xa97000: add             x1, x1, HEAP, lsl #32
    // 0xa97004: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa97004: ldur            w0, [x1, #0x17]
    // 0xa97008: DecompressPointer r0
    //     0xa97008: add             x0, x0, HEAP, lsl #32
    // 0xa9700c: stur            x0, [fp, #-8]
    // 0xa97010: r0 = Expandable()
    //     0xa97010: bl              #0xa97038  ; AllocateExpandableStub -> Expandable (size=0x1c)
    // 0xa97014: ldr             x1, [fp, #0x18]
    // 0xa97018: StoreField: r0->field_b = r1
    //     0xa97018: stur            w1, [x0, #0xb]
    // 0xa9701c: ldr             x1, [fp, #0x10]
    // 0xa97020: StoreField: r0->field_f = r1
    //     0xa97020: stur            w1, [x0, #0xf]
    // 0xa97024: ldur            x1, [fp, #-8]
    // 0xa97028: ArrayStore: r0[0] = r1  ; List_4
    //     0xa97028: stur            w1, [x0, #0x17]
    // 0xa9702c: LeaveFrame
    //     0xa9702c: mov             SP, fp
    //     0xa97030: ldp             fp, lr, [SP], #0x10
    // 0xa97034: ret
    //     0xa97034: ret             
  }
  [closure] Widget buildHeaderRow(dynamic) {
    // ** addr: 0xa97044, size: 0x290
    // 0xa97044: EnterFrame
    //     0xa97044: stp             fp, lr, [SP, #-0x10]!
    //     0xa97048: mov             fp, SP
    // 0xa9704c: AllocStack(0x28)
    //     0xa9704c: sub             SP, SP, #0x28
    // 0xa97050: SetupParameters()
    //     0xa97050: ldr             x0, [fp, #0x10]
    //     0xa97054: ldur            w1, [x0, #0x17]
    //     0xa97058: add             x1, x1, HEAP, lsl #32
    // 0xa9705c: CheckStackOverflow
    //     0xa9705c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa97060: cmp             SP, x16
    //     0xa97064: b.ls            #0xa972bc
    // 0xa97068: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa97068: ldur            w0, [x1, #0x17]
    // 0xa9706c: DecompressPointer r0
    //     0xa9706c: add             x0, x0, HEAP, lsl #32
    // 0xa97070: stur            x0, [fp, #-0x10]
    // 0xa97074: LoadField: r2 = r0->field_3f
    //     0xa97074: ldur            w2, [x0, #0x3f]
    // 0xa97078: DecompressPointer r2
    //     0xa97078: add             x2, x2, HEAP, lsl #32
    // 0xa9707c: cmp             w2, NULL
    // 0xa97080: b.eq            #0xa972c4
    // 0xa97084: tbnz            w2, #4, #0xa972b0
    // 0xa97088: LoadField: r2 = r1->field_f
    //     0xa97088: ldur            w2, [x1, #0xf]
    // 0xa9708c: DecompressPointer r2
    //     0xa9708c: add             x2, x2, HEAP, lsl #32
    // 0xa97090: LoadField: r3 = r2->field_b
    //     0xa97090: ldur            w3, [x2, #0xb]
    // 0xa97094: DecompressPointer r3
    //     0xa97094: add             x3, x3, HEAP, lsl #32
    // 0xa97098: stur            x3, [fp, #-8]
    // 0xa9709c: r1 = <FlexParentData>
    //     0xa9709c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xa970a0: ldr             x1, [x1, #0x720]
    // 0xa970a4: r0 = Expanded()
    //     0xa970a4: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa970a8: mov             x1, x0
    // 0xa970ac: r0 = 1
    //     0xa970ac: movz            x0, #0x1
    // 0xa970b0: stur            x1, [fp, #-0x18]
    // 0xa970b4: StoreField: r1->field_13 = r0
    //     0xa970b4: stur            x0, [x1, #0x13]
    // 0xa970b8: r0 = Instance_FlexFit
    //     0xa970b8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xa970bc: ldr             x0, [x0, #0x728]
    // 0xa970c0: StoreField: r1->field_1b = r0
    //     0xa970c0: stur            w0, [x1, #0x1b]
    // 0xa970c4: ldur            x0, [fp, #-8]
    // 0xa970c8: StoreField: r1->field_b = r0
    //     0xa970c8: stur            w0, [x1, #0xb]
    // 0xa970cc: r0 = ExpandableIcon()
    //     0xa970cc: bl              #0xa972e0  ; AllocateExpandableIconStub -> ExpandableIcon (size=0x10)
    // 0xa970d0: mov             x1, x0
    // 0xa970d4: ldur            x0, [fp, #-0x10]
    // 0xa970d8: stur            x1, [fp, #-0x20]
    // 0xa970dc: StoreField: r1->field_b = r0
    //     0xa970dc: stur            w0, [x1, #0xb]
    // 0xa970e0: LoadField: r2 = r0->field_33
    //     0xa970e0: ldur            w2, [x0, #0x33]
    // 0xa970e4: DecompressPointer r2
    //     0xa970e4: add             x2, x2, HEAP, lsl #32
    // 0xa970e8: stur            x2, [fp, #-8]
    // 0xa970ec: cmp             w2, NULL
    // 0xa970f0: b.eq            #0xa972c8
    // 0xa970f4: eor             x3, x2, #0x10
    // 0xa970f8: tbnz            w3, #4, #0xa9711c
    // 0xa970fc: r0 = ExpandableButton()
    //     0xa970fc: bl              #0xa972d4  ; AllocateExpandableButtonStub -> ExpandableButton (size=0x14)
    // 0xa97100: mov             x1, x0
    // 0xa97104: ldur            x0, [fp, #-0x20]
    // 0xa97108: StoreField: r1->field_b = r0
    //     0xa97108: stur            w0, [x1, #0xb]
    // 0xa9710c: ldur            x3, [fp, #-0x10]
    // 0xa97110: StoreField: r1->field_f = r3
    //     0xa97110: stur            w3, [x1, #0xf]
    // 0xa97114: mov             x5, x1
    // 0xa97118: b               #0xa97128
    // 0xa9711c: mov             x3, x0
    // 0xa97120: mov             x0, x1
    // 0xa97124: mov             x5, x0
    // 0xa97128: ldur            x0, [fp, #-0x18]
    // 0xa9712c: r4 = 4
    //     0xa9712c: movz            x4, #0x4
    // 0xa97130: mov             x2, x4
    // 0xa97134: stur            x5, [fp, #-0x20]
    // 0xa97138: r1 = Null
    //     0xa97138: mov             x1, NULL
    // 0xa9713c: r0 = AllocateArray()
    //     0xa9713c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa97140: mov             x2, x0
    // 0xa97144: ldur            x0, [fp, #-0x18]
    // 0xa97148: stur            x2, [fp, #-0x28]
    // 0xa9714c: StoreField: r2->field_f = r0
    //     0xa9714c: stur            w0, [x2, #0xf]
    // 0xa97150: ldur            x0, [fp, #-0x20]
    // 0xa97154: StoreField: r2->field_13 = r0
    //     0xa97154: stur            w0, [x2, #0x13]
    // 0xa97158: r1 = <Widget>
    //     0xa97158: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa9715c: r0 = AllocateGrowableArray()
    //     0xa9715c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa97160: mov             x2, x0
    // 0xa97164: ldur            x0, [fp, #-0x28]
    // 0xa97168: stur            x2, [fp, #-0x20]
    // 0xa9716c: StoreField: r2->field_f = r0
    //     0xa9716c: stur            w0, [x2, #0xf]
    // 0xa97170: r0 = 4
    //     0xa97170: movz            x0, #0x4
    // 0xa97174: StoreField: r2->field_b = r0
    //     0xa97174: stur            w0, [x2, #0xb]
    // 0xa97178: ldur            x0, [fp, #-0x10]
    // 0xa9717c: LoadField: r1 = r0->field_27
    //     0xa9717c: ldur            w1, [x0, #0x27]
    // 0xa97180: DecompressPointer r1
    //     0xa97180: add             x1, x1, HEAP, lsl #32
    // 0xa97184: cmp             w1, NULL
    // 0xa97188: b.eq            #0xa972cc
    // 0xa9718c: LoadField: r3 = r1->field_7
    //     0xa9718c: ldur            x3, [x1, #7]
    // 0xa97190: cmp             x3, #1
    // 0xa97194: b.gt            #0xa971b8
    // 0xa97198: cmp             x3, #0
    // 0xa9719c: b.gt            #0xa971ac
    // 0xa971a0: r3 = Instance_CrossAxisAlignment
    //     0xa971a0: add             x3, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xa971a4: ldr             x3, [x3, #0x68]
    // 0xa971a8: b               #0xa971c0
    // 0xa971ac: r3 = Instance_CrossAxisAlignment
    //     0xa971ac: add             x3, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xa971b0: ldr             x3, [x3, #0x740]
    // 0xa971b4: b               #0xa971c0
    // 0xa971b8: r3 = Instance_CrossAxisAlignment
    //     0xa971b8: add             x3, PP, #0x33, lsl #12  ; [pp+0x33fa8] Obj!CrossAxisAlignment@e359c1
    //     0xa971bc: ldr             x3, [x3, #0xfa8]
    // 0xa971c0: stur            x3, [fp, #-0x18]
    // 0xa971c4: LoadField: r1 = r0->field_2f
    //     0xa971c4: ldur            w1, [x0, #0x2f]
    // 0xa971c8: DecompressPointer r1
    //     0xa971c8: add             x1, x1, HEAP, lsl #32
    // 0xa971cc: cmp             w1, NULL
    // 0xa971d0: b.eq            #0xa972d0
    // 0xa971d4: r16 = Instance_ExpandablePanelIconPlacement
    //     0xa971d4: add             x16, PP, #0x43, lsl #12  ; [pp+0x434f8] Obj!ExpandablePanelIconPlacement@e37401
    //     0xa971d8: ldr             x16, [x16, #0x4f8]
    // 0xa971dc: cmp             w1, w16
    // 0xa971e0: b.ne            #0xa971ec
    // 0xa971e4: mov             x0, x3
    // 0xa971e8: b               #0xa97214
    // 0xa971ec: r1 = <Widget>
    //     0xa971ec: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa971f0: r0 = ReversedListIterable()
    //     0xa971f0: bl              #0x668634  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0xa971f4: mov             x1, x0
    // 0xa971f8: ldur            x0, [fp, #-0x20]
    // 0xa971fc: StoreField: r1->field_b = r0
    //     0xa971fc: stur            w0, [x1, #0xb]
    // 0xa97200: mov             x2, x1
    // 0xa97204: r1 = <Widget>
    //     0xa97204: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa97208: r0 = _GrowableList._ofEfficientLengthIterable()
    //     0xa97208: bl              #0x60b858  ; [dart:core] _GrowableList::_GrowableList._ofEfficientLengthIterable
    // 0xa9720c: mov             x2, x0
    // 0xa97210: ldur            x0, [fp, #-0x18]
    // 0xa97214: ldur            x1, [fp, #-8]
    // 0xa97218: stur            x2, [fp, #-0x20]
    // 0xa9721c: r0 = Row()
    //     0xa9721c: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa97220: mov             x1, x0
    // 0xa97224: r0 = Instance_Axis
    //     0xa97224: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xa97228: stur            x1, [fp, #-0x28]
    // 0xa9722c: StoreField: r1->field_f = r0
    //     0xa9722c: stur            w0, [x1, #0xf]
    // 0xa97230: r0 = Instance_MainAxisAlignment
    //     0xa97230: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xa97234: ldr             x0, [x0, #0x730]
    // 0xa97238: StoreField: r1->field_13 = r0
    //     0xa97238: stur            w0, [x1, #0x13]
    // 0xa9723c: r0 = Instance_MainAxisSize
    //     0xa9723c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xa97240: ldr             x0, [x0, #0x738]
    // 0xa97244: ArrayStore: r1[0] = r0  ; List_4
    //     0xa97244: stur            w0, [x1, #0x17]
    // 0xa97248: ldur            x0, [fp, #-0x18]
    // 0xa9724c: StoreField: r1->field_1b = r0
    //     0xa9724c: stur            w0, [x1, #0x1b]
    // 0xa97250: r0 = Instance_VerticalDirection
    //     0xa97250: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa97254: ldr             x0, [x0, #0x748]
    // 0xa97258: StoreField: r1->field_23 = r0
    //     0xa97258: stur            w0, [x1, #0x23]
    // 0xa9725c: r0 = Instance_Clip
    //     0xa9725c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa97260: ldr             x0, [x0, #0x750]
    // 0xa97264: StoreField: r1->field_2b = r0
    //     0xa97264: stur            w0, [x1, #0x2b]
    // 0xa97268: StoreField: r1->field_2f = rZR
    //     0xa97268: stur            xzr, [x1, #0x2f]
    // 0xa9726c: ldur            x0, [fp, #-0x20]
    // 0xa97270: StoreField: r1->field_b = r0
    //     0xa97270: stur            w0, [x1, #0xb]
    // 0xa97274: ldur            x0, [fp, #-8]
    // 0xa97278: tbnz            w0, #4, #0xa972a0
    // 0xa9727c: ldur            x0, [fp, #-0x10]
    // 0xa97280: r0 = ExpandableButton()
    //     0xa97280: bl              #0xa972d4  ; AllocateExpandableButtonStub -> ExpandableButton (size=0x14)
    // 0xa97284: mov             x1, x0
    // 0xa97288: ldur            x0, [fp, #-0x28]
    // 0xa9728c: StoreField: r1->field_b = r0
    //     0xa9728c: stur            w0, [x1, #0xb]
    // 0xa97290: ldur            x0, [fp, #-0x10]
    // 0xa97294: StoreField: r1->field_f = r0
    //     0xa97294: stur            w0, [x1, #0xf]
    // 0xa97298: mov             x0, x1
    // 0xa9729c: b               #0xa972a4
    // 0xa972a0: mov             x0, x1
    // 0xa972a4: LeaveFrame
    //     0xa972a4: mov             SP, fp
    //     0xa972a8: ldp             fp, lr, [SP], #0x10
    // 0xa972ac: ret
    //     0xa972ac: ret             
    // 0xa972b0: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0xa972b0: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0xa972b4: r0 = Throw()
    //     0xa972b4: bl              #0xec04b8  ; ThrowStub
    // 0xa972b8: brk             #0
    // 0xa972bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa972bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa972c0: b               #0xa97068
    // 0xa972c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa972c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa972c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa972c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa972cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa972cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa972d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa972d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5421, size: 0x1c, field offset: 0xc
class Expandable extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xa96804, size: 0x294
    // 0xa96804: EnterFrame
    //     0xa96804: stp             fp, lr, [SP, #-0x10]!
    //     0xa96808: mov             fp, SP
    // 0xa9680c: AllocStack(0x48)
    //     0xa9680c: sub             SP, SP, #0x48
    // 0xa96810: SetupParameters(Expandable this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa96810: mov             x0, x2
    //     0xa96814: stur            x2, [fp, #-0x10]
    //     0xa96818: mov             x2, x1
    //     0xa9681c: stur            x1, [fp, #-8]
    // 0xa96820: CheckStackOverflow
    //     0xa96820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa96824: cmp             SP, x16
    //     0xa96828: b.ls            #0xa96a7c
    // 0xa9682c: mov             x1, x0
    // 0xa96830: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa96830: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa96834: r0 = of()
    //     0xa96834: bl              #0x92cd04  ; [package:expandable/expandable.dart] ExpandableController::of
    // 0xa96838: mov             x3, x0
    // 0xa9683c: ldur            x0, [fp, #-8]
    // 0xa96840: stur            x3, [fp, #-0x18]
    // 0xa96844: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa96844: ldur            w1, [x0, #0x17]
    // 0xa96848: DecompressPointer r1
    //     0xa96848: add             x1, x1, HEAP, lsl #32
    // 0xa9684c: ldur            x2, [fp, #-0x10]
    // 0xa96850: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa96850: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa96854: r0 = withDefaults()
    //     0xa96854: bl              #0x92cdbc  ; [package:expandable/expandable.dart] ExpandableThemeData::withDefaults
    // 0xa96858: stur            x0, [fp, #-0x28]
    // 0xa9685c: LoadField: r1 = r0->field_1b
    //     0xa9685c: ldur            w1, [x0, #0x1b]
    // 0xa96860: DecompressPointer r1
    //     0xa96860: add             x1, x1, HEAP, lsl #32
    // 0xa96864: cmp             w1, NULL
    // 0xa96868: b.eq            #0xa96a84
    // 0xa9686c: ldur            x1, [fp, #-8]
    // 0xa96870: LoadField: r2 = r1->field_b
    //     0xa96870: ldur            w2, [x1, #0xb]
    // 0xa96874: DecompressPointer r2
    //     0xa96874: add             x2, x2, HEAP, lsl #32
    // 0xa96878: stur            x2, [fp, #-0x20]
    // 0xa9687c: LoadField: r3 = r1->field_f
    //     0xa9687c: ldur            w3, [x1, #0xf]
    // 0xa96880: DecompressPointer r3
    //     0xa96880: add             x3, x3, HEAP, lsl #32
    // 0xa96884: stur            x3, [fp, #-0x10]
    // 0xa96888: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa96888: ldur            w1, [x0, #0x17]
    // 0xa9688c: DecompressPointer r1
    //     0xa9688c: add             x1, x1, HEAP, lsl #32
    // 0xa96890: cmp             w1, NULL
    // 0xa96894: b.eq            #0xa96a88
    // 0xa96898: LoadField: d0 = r1->field_7
    //     0xa96898: ldur            d0, [x1, #7]
    // 0xa9689c: stur            d0, [fp, #-0x48]
    // 0xa968a0: d1 = 0.500000
    //     0xa968a0: fmov            d1, #0.50000000
    // 0xa968a4: fcmp            d1, d0
    // 0xa968a8: b.le            #0xa968bc
    // 0xa968ac: d4 = 0.000000
    //     0xa968ac: eor             v4.16b, v4.16b, v4.16b
    // 0xa968b0: d3 = 2.000000
    //     0xa968b0: fmov            d3, #2.00000000
    // 0xa968b4: d2 = 1.000000
    //     0xa968b4: fmov            d2, #1.00000000
    // 0xa968b8: b               #0xa968d0
    // 0xa968bc: d3 = 2.000000
    //     0xa968bc: fmov            d3, #2.00000000
    // 0xa968c0: d2 = 1.000000
    //     0xa968c0: fmov            d2, #1.00000000
    // 0xa968c4: fmul            d4, d0, d3
    // 0xa968c8: fsub            d5, d4, d2
    // 0xa968cc: mov             v4.16b, v5.16b
    // 0xa968d0: stur            d4, [fp, #-0x40]
    // 0xa968d4: fcmp            d1, d0
    // 0xa968d8: b.le            #0xa968e4
    // 0xa968dc: fmul            d5, d0, d3
    // 0xa968e0: b               #0xa968e8
    // 0xa968e4: d5 = 1.000000
    //     0xa968e4: fmov            d5, #1.00000000
    // 0xa968e8: stur            d5, [fp, #-0x38]
    // 0xa968ec: LoadField: r1 = r0->field_1f
    //     0xa968ec: ldur            w1, [x0, #0x1f]
    // 0xa968f0: DecompressPointer r1
    //     0xa968f0: add             x1, x1, HEAP, lsl #32
    // 0xa968f4: cmp             w1, NULL
    // 0xa968f8: b.eq            #0xa96a8c
    // 0xa968fc: r1 = <double>
    //     0xa968fc: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xa96900: r0 = Interval()
    //     0xa96900: bl              #0x796aa8  ; AllocateIntervalStub -> Interval (size=0x20)
    // 0xa96904: ldur            d0, [fp, #-0x40]
    // 0xa96908: stur            x0, [fp, #-8]
    // 0xa9690c: StoreField: r0->field_b = d0
    //     0xa9690c: stur            d0, [x0, #0xb]
    // 0xa96910: ldur            d0, [fp, #-0x38]
    // 0xa96914: StoreField: r0->field_13 = d0
    //     0xa96914: stur            d0, [x0, #0x13]
    // 0xa96918: r2 = Instance__Linear
    //     0xa96918: ldr             x2, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xa9691c: StoreField: r0->field_1b = r2
    //     0xa9691c: stur            w2, [x0, #0x1b]
    // 0xa96920: ldur            d0, [fp, #-0x48]
    // 0xa96924: d1 = 0.500000
    //     0xa96924: fmov            d1, #0.50000000
    // 0xa96928: fcmp            d1, d0
    // 0xa9692c: b.le            #0xa9693c
    // 0xa96930: d2 = 0.000000
    //     0xa96930: eor             v2.16b, v2.16b, v2.16b
    // 0xa96934: d3 = 2.000000
    //     0xa96934: fmov            d3, #2.00000000
    // 0xa96938: b               #0xa96950
    // 0xa9693c: d3 = 2.000000
    //     0xa9693c: fmov            d3, #2.00000000
    // 0xa96940: d2 = 1.000000
    //     0xa96940: fmov            d2, #1.00000000
    // 0xa96944: fmul            d4, d0, d3
    // 0xa96948: fsub            d5, d4, d2
    // 0xa9694c: mov             v2.16b, v5.16b
    // 0xa96950: stur            d2, [fp, #-0x40]
    // 0xa96954: fcmp            d1, d0
    // 0xa96958: b.le            #0xa96968
    // 0xa9695c: fmul            d1, d0, d3
    // 0xa96960: mov             v0.16b, v1.16b
    // 0xa96964: b               #0xa9696c
    // 0xa96968: d0 = 1.000000
    //     0xa96968: fmov            d0, #1.00000000
    // 0xa9696c: ldur            x3, [fp, #-0x28]
    // 0xa96970: ldur            x4, [fp, #-0x18]
    // 0xa96974: stur            d0, [fp, #-0x38]
    // 0xa96978: r1 = <double>
    //     0xa96978: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xa9697c: r0 = Interval()
    //     0xa9697c: bl              #0x796aa8  ; AllocateIntervalStub -> Interval (size=0x20)
    // 0xa96980: ldur            d0, [fp, #-0x40]
    // 0xa96984: stur            x0, [fp, #-0x30]
    // 0xa96988: StoreField: r0->field_b = d0
    //     0xa96988: stur            d0, [x0, #0xb]
    // 0xa9698c: ldur            d0, [fp, #-0x38]
    // 0xa96990: StoreField: r0->field_13 = d0
    //     0xa96990: stur            d0, [x0, #0x13]
    // 0xa96994: r1 = Instance__Linear
    //     0xa96994: ldr             x1, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xa96998: StoreField: r0->field_1b = r1
    //     0xa96998: stur            w1, [x0, #0x1b]
    // 0xa9699c: ldur            x1, [fp, #-0x28]
    // 0xa969a0: LoadField: r2 = r1->field_23
    //     0xa969a0: ldur            w2, [x1, #0x23]
    // 0xa969a4: DecompressPointer r2
    //     0xa969a4: add             x2, x2, HEAP, lsl #32
    // 0xa969a8: cmp             w2, NULL
    // 0xa969ac: b.eq            #0xa96a90
    // 0xa969b0: ldur            x2, [fp, #-0x18]
    // 0xa969b4: cmp             w2, NULL
    // 0xa969b8: b.ne            #0xa969c4
    // 0xa969bc: r2 = Null
    //     0xa969bc: mov             x2, NULL
    // 0xa969c0: b               #0xa969d0
    // 0xa969c4: LoadField: r3 = r2->field_27
    //     0xa969c4: ldur            w3, [x2, #0x27]
    // 0xa969c8: DecompressPointer r3
    //     0xa969c8: add             x3, x3, HEAP, lsl #32
    // 0xa969cc: mov             x2, x3
    // 0xa969d0: cmp             w2, NULL
    // 0xa969d4: b.eq            #0xa969dc
    // 0xa969d8: tbnz            w2, #4, #0xa969e8
    // 0xa969dc: r5 = Instance_CrossFadeState
    //     0xa969dc: add             x5, PP, #0x4c, lsl #12  ; [pp+0x4cfd8] Obj!CrossFadeState@e34901
    //     0xa969e0: ldr             x5, [x5, #0xfd8]
    // 0xa969e4: b               #0xa969f0
    // 0xa969e8: r5 = Instance_CrossFadeState
    //     0xa969e8: add             x5, PP, #0x4c, lsl #12  ; [pp+0x4cfe0] Obj!CrossFadeState@e34921
    //     0xa969ec: ldr             x5, [x5, #0xfe0]
    // 0xa969f0: ldur            x3, [fp, #-0x20]
    // 0xa969f4: ldur            x4, [fp, #-0x10]
    // 0xa969f8: ldur            x2, [fp, #-8]
    // 0xa969fc: stur            x5, [fp, #-0x18]
    // 0xa96a00: LoadField: r6 = r1->field_f
    //     0xa96a00: ldur            w6, [x1, #0xf]
    // 0xa96a04: DecompressPointer r6
    //     0xa96a04: add             x6, x6, HEAP, lsl #32
    // 0xa96a08: cmp             w6, NULL
    // 0xa96a0c: b.eq            #0xa96a94
    // 0xa96a10: r0 = AnimatedCrossFade()
    //     0xa96a10: bl              #0xa96a98  ; AllocateAnimatedCrossFadeStub -> AnimatedCrossFade (size=0x38)
    // 0xa96a14: ldur            x1, [fp, #-0x20]
    // 0xa96a18: StoreField: r0->field_b = r1
    //     0xa96a18: stur            w1, [x0, #0xb]
    // 0xa96a1c: ldur            x1, [fp, #-0x10]
    // 0xa96a20: StoreField: r0->field_f = r1
    //     0xa96a20: stur            w1, [x0, #0xf]
    // 0xa96a24: ldur            x1, [fp, #-8]
    // 0xa96a28: StoreField: r0->field_1f = r1
    //     0xa96a28: stur            w1, [x0, #0x1f]
    // 0xa96a2c: ldur            x1, [fp, #-0x30]
    // 0xa96a30: StoreField: r0->field_23 = r1
    //     0xa96a30: stur            w1, [x0, #0x23]
    // 0xa96a34: r1 = Instance_Cubic
    //     0xa96a34: ldr             x1, [PP, #0x6e20]  ; [pp+0x6e20] Obj!Cubic@e14d41
    // 0xa96a38: StoreField: r0->field_27 = r1
    //     0xa96a38: stur            w1, [x0, #0x27]
    // 0xa96a3c: r1 = Instance_Alignment
    //     0xa96a3c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25370] Obj!Alignment@e13e11
    //     0xa96a40: ldr             x1, [x1, #0x370]
    // 0xa96a44: StoreField: r0->field_2b = r1
    //     0xa96a44: stur            w1, [x0, #0x2b]
    // 0xa96a48: ldur            x1, [fp, #-0x18]
    // 0xa96a4c: StoreField: r0->field_13 = r1
    //     0xa96a4c: stur            w1, [x0, #0x13]
    // 0xa96a50: r1 = Instance_Duration
    //     0xa96a50: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0xa96a54: ldr             x1, [x1, #0x9c0]
    // 0xa96a58: ArrayStore: r0[0] = r1  ; List_4
    //     0xa96a58: stur            w1, [x0, #0x17]
    // 0xa96a5c: r1 = Closure: (Widget, Key, Widget, Key) => Widget from Function 'defaultLayoutBuilder': static.
    //     0xa96a5c: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4cfe8] Closure: (Widget, Key, Widget, Key) => Widget from Function 'defaultLayoutBuilder': static. (0x7e54fb40ff58)
    //     0xa96a60: ldr             x1, [x1, #0xfe8]
    // 0xa96a64: StoreField: r0->field_2f = r1
    //     0xa96a64: stur            w1, [x0, #0x2f]
    // 0xa96a68: r1 = true
    //     0xa96a68: add             x1, NULL, #0x20  ; true
    // 0xa96a6c: StoreField: r0->field_33 = r1
    //     0xa96a6c: stur            w1, [x0, #0x33]
    // 0xa96a70: LeaveFrame
    //     0xa96a70: mov             SP, fp
    //     0xa96a74: ldp             fp, lr, [SP], #0x10
    // 0xa96a78: ret
    //     0xa96a78: ret             
    // 0xa96a7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa96a7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa96a80: b               #0xa9682c
    // 0xa96a84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa96a84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa96a88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa96a88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa96a8c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa96a8c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa96a90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa96a90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa96a94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa96a94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5558, size: 0x5c, field offset: 0x8
//   const constructor, 
class ExpandableThemeData extends Object {

  static late final ExpandableThemeData defaults; // offset: 0xf70
  static late final ExpandableThemeData empty; // offset: 0xf74

  static _ withDefaults(/* No info */) {
    // ** addr: 0x92cdbc, size: 0xe0
    // 0x92cdbc: EnterFrame
    //     0x92cdbc: stp             fp, lr, [SP, #-0x10]!
    //     0x92cdc0: mov             fp, SP
    // 0x92cdc4: AllocStack(0x18)
    //     0x92cdc4: sub             SP, SP, #0x18
    // 0x92cdc8: SetupParameters(dynamic _ /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, {dynamic rebuildOnChange = true /* r3, fp-0x8 */})
    //     0x92cdc8: mov             x0, x2
    //     0x92cdcc: stur            x2, [fp, #-0x18]
    //     0x92cdd0: mov             x2, x1
    //     0x92cdd4: stur            x1, [fp, #-0x10]
    //     0x92cdd8: ldur            w1, [x4, #0x13]
    //     0x92cddc: ldur            w3, [x4, #0x1f]
    //     0x92cde0: add             x3, x3, HEAP, lsl #32
    //     0x92cde4: add             x16, PP, #0x43, lsl #12  ; [pp+0x43500] "rebuildOnChange"
    //     0x92cde8: ldr             x16, [x16, #0x500]
    //     0x92cdec: cmp             w3, w16
    //     0x92cdf0: b.ne            #0x92ce10
    //     0x92cdf4: ldur            w3, [x4, #0x23]
    //     0x92cdf8: add             x3, x3, HEAP, lsl #32
    //     0x92cdfc: sub             w4, w1, w3
    //     0x92ce00: add             x1, fp, w4, sxtw #2
    //     0x92ce04: ldr             x1, [x1, #8]
    //     0x92ce08: mov             x3, x1
    //     0x92ce0c: b               #0x92ce14
    //     0x92ce10: add             x3, NULL, #0x20  ; true
    //     0x92ce14: stur            x3, [fp, #-8]
    // 0x92ce18: CheckStackOverflow
    //     0x92ce18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92ce1c: cmp             SP, x16
    //     0x92ce20: b.ls            #0x92ce94
    // 0x92ce24: mov             x1, x2
    // 0x92ce28: r0 = isFull()
    //     0x92ce28: bl              #0x92d36c  ; [package:expandable/expandable.dart] ExpandableThemeData::isFull
    // 0x92ce2c: tbnz            w0, #4, #0x92ce40
    // 0x92ce30: ldur            x0, [fp, #-0x10]
    // 0x92ce34: LeaveFrame
    //     0x92ce34: mov             SP, fp
    //     0x92ce38: ldp             fp, lr, [SP], #0x10
    // 0x92ce3c: ret
    //     0x92ce3c: ret             
    // 0x92ce40: ldur            x1, [fp, #-0x18]
    // 0x92ce44: ldur            x2, [fp, #-8]
    // 0x92ce48: r0 = of()
    //     0x92ce48: bl              #0x92d2f0  ; [package:expandable/expandable.dart] ExpandableThemeData::of
    // 0x92ce4c: ldur            x1, [fp, #-0x10]
    // 0x92ce50: mov             x2, x0
    // 0x92ce54: r0 = combine()
    //     0x92ce54: bl              #0x92ce9c  ; [package:expandable/expandable.dart] ExpandableThemeData::combine
    // 0x92ce58: stur            x0, [fp, #-8]
    // 0x92ce5c: r0 = InitLateStaticField(0xf70) // [package:expandable/expandable.dart] ExpandableThemeData::defaults
    //     0x92ce5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x92ce60: ldr             x0, [x0, #0x1ee0]
    //     0x92ce64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x92ce68: cmp             w0, w16
    //     0x92ce6c: b.ne            #0x92ce7c
    //     0x92ce70: add             x2, PP, #0x43, lsl #12  ; [pp+0x43510] Field <ExpandableThemeData.defaults>: static late final (offset: 0xf70)
    //     0x92ce74: ldr             x2, [x2, #0x510]
    //     0x92ce78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x92ce7c: ldur            x1, [fp, #-8]
    // 0x92ce80: mov             x2, x0
    // 0x92ce84: r0 = combine()
    //     0x92ce84: bl              #0x92ce9c  ; [package:expandable/expandable.dart] ExpandableThemeData::combine
    // 0x92ce88: LeaveFrame
    //     0x92ce88: mov             SP, fp
    //     0x92ce8c: ldp             fp, lr, [SP], #0x10
    // 0x92ce90: ret
    //     0x92ce90: ret             
    // 0x92ce94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92ce94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92ce98: b               #0x92ce24
  }
  static _ combine(/* No info */) {
    // ** addr: 0x92ce9c, size: 0x3d4
    // 0x92ce9c: EnterFrame
    //     0x92ce9c: stp             fp, lr, [SP, #-0x10]!
    //     0x92cea0: mov             fp, SP
    // 0x92cea4: AllocStack(0xb0)
    //     0x92cea4: sub             SP, SP, #0xb0
    // 0x92cea8: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x92cea8: mov             x0, x2
    //     0x92ceac: stur            x2, [fp, #-0x10]
    //     0x92ceb0: mov             x2, x1
    //     0x92ceb4: stur            x1, [fp, #-8]
    // 0x92ceb8: CheckStackOverflow
    //     0x92ceb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92cebc: cmp             SP, x16
    //     0x92cec0: b.ls            #0x92d268
    // 0x92cec4: mov             x1, x0
    // 0x92cec8: r0 = isEmpty()
    //     0x92cec8: bl              #0x92d27c  ; [package:expandable/expandable.dart] ExpandableThemeData::isEmpty
    // 0x92cecc: tbnz            w0, #4, #0x92cf08
    // 0x92ced0: ldur            x0, [fp, #-8]
    // 0x92ced4: cmp             w0, NULL
    // 0x92ced8: b.ne            #0x92cefc
    // 0x92cedc: r0 = InitLateStaticField(0xf74) // [package:expandable/expandable.dart] ExpandableThemeData::empty
    //     0x92cedc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x92cee0: ldr             x0, [x0, #0x1ee8]
    //     0x92cee4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x92cee8: cmp             w0, w16
    //     0x92ceec: b.ne            #0x92cefc
    //     0x92cef0: add             x2, PP, #0x43, lsl #12  ; [pp+0x43518] Field <ExpandableThemeData.empty>: static late final (offset: 0xf74)
    //     0x92cef4: ldr             x2, [x2, #0x518]
    //     0x92cef8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x92cefc: LeaveFrame
    //     0x92cefc: mov             SP, fp
    //     0x92cf00: ldp             fp, lr, [SP], #0x10
    // 0x92cf04: ret
    //     0x92cf04: ret             
    // 0x92cf08: ldur            x0, [fp, #-8]
    // 0x92cf0c: cmp             w0, NULL
    // 0x92cf10: b.eq            #0x92cf20
    // 0x92cf14: mov             x1, x0
    // 0x92cf18: r0 = isEmpty()
    //     0x92cf18: bl              #0x92d27c  ; [package:expandable/expandable.dart] ExpandableThemeData::isEmpty
    // 0x92cf1c: tbnz            w0, #4, #0x92cf30
    // 0x92cf20: ldur            x0, [fp, #-0x10]
    // 0x92cf24: LeaveFrame
    //     0x92cf24: mov             SP, fp
    //     0x92cf28: ldp             fp, lr, [SP], #0x10
    // 0x92cf2c: ret
    //     0x92cf2c: ret             
    // 0x92cf30: ldur            x1, [fp, #-8]
    // 0x92cf34: r0 = isFull()
    //     0x92cf34: bl              #0x92d36c  ; [package:expandable/expandable.dart] ExpandableThemeData::isFull
    // 0x92cf38: tbnz            w0, #4, #0x92cf4c
    // 0x92cf3c: ldur            x0, [fp, #-8]
    // 0x92cf40: LeaveFrame
    //     0x92cf40: mov             SP, fp
    //     0x92cf44: ldp             fp, lr, [SP], #0x10
    // 0x92cf48: ret
    //     0x92cf48: ret             
    // 0x92cf4c: ldur            x0, [fp, #-8]
    // 0x92cf50: LoadField: r1 = r0->field_7
    //     0x92cf50: ldur            w1, [x0, #7]
    // 0x92cf54: DecompressPointer r1
    //     0x92cf54: add             x1, x1, HEAP, lsl #32
    // 0x92cf58: cmp             w1, NULL
    // 0x92cf5c: b.ne            #0x92cf70
    // 0x92cf60: ldur            x2, [fp, #-0x10]
    // 0x92cf64: LoadField: r1 = r2->field_7
    //     0x92cf64: ldur            w1, [x2, #7]
    // 0x92cf68: DecompressPointer r1
    //     0x92cf68: add             x1, x1, HEAP, lsl #32
    // 0x92cf6c: b               #0x92cf74
    // 0x92cf70: ldur            x2, [fp, #-0x10]
    // 0x92cf74: stur            x1, [fp, #-0x18]
    // 0x92cf78: LoadField: r3 = r0->field_b
    //     0x92cf78: ldur            w3, [x0, #0xb]
    // 0x92cf7c: DecompressPointer r3
    //     0x92cf7c: add             x3, x3, HEAP, lsl #32
    // 0x92cf80: cmp             w3, NULL
    // 0x92cf84: b.ne            #0x92cf90
    // 0x92cf88: LoadField: r3 = r2->field_b
    //     0x92cf88: ldur            w3, [x2, #0xb]
    // 0x92cf8c: DecompressPointer r3
    //     0x92cf8c: add             x3, x3, HEAP, lsl #32
    // 0x92cf90: stur            x3, [fp, #-0xb0]
    // 0x92cf94: LoadField: r4 = r0->field_57
    //     0x92cf94: ldur            w4, [x0, #0x57]
    // 0x92cf98: DecompressPointer r4
    //     0x92cf98: add             x4, x4, HEAP, lsl #32
    // 0x92cf9c: cmp             w4, NULL
    // 0x92cfa0: b.ne            #0x92cfac
    // 0x92cfa4: LoadField: r4 = r2->field_57
    //     0x92cfa4: ldur            w4, [x2, #0x57]
    // 0x92cfa8: DecompressPointer r4
    //     0x92cfa8: add             x4, x4, HEAP, lsl #32
    // 0x92cfac: stur            x4, [fp, #-0xa8]
    // 0x92cfb0: LoadField: r5 = r0->field_f
    //     0x92cfb0: ldur            w5, [x0, #0xf]
    // 0x92cfb4: DecompressPointer r5
    //     0x92cfb4: add             x5, x5, HEAP, lsl #32
    // 0x92cfb8: cmp             w5, NULL
    // 0x92cfbc: b.ne            #0x92cfc8
    // 0x92cfc0: LoadField: r5 = r2->field_f
    //     0x92cfc0: ldur            w5, [x2, #0xf]
    // 0x92cfc4: DecompressPointer r5
    //     0x92cfc4: add             x5, x5, HEAP, lsl #32
    // 0x92cfc8: stur            x5, [fp, #-0xa0]
    // 0x92cfcc: LoadField: r6 = r0->field_13
    //     0x92cfcc: ldur            w6, [x0, #0x13]
    // 0x92cfd0: DecompressPointer r6
    //     0x92cfd0: add             x6, x6, HEAP, lsl #32
    // 0x92cfd4: cmp             w6, NULL
    // 0x92cfd8: b.ne            #0x92cfe4
    // 0x92cfdc: LoadField: r6 = r2->field_13
    //     0x92cfdc: ldur            w6, [x2, #0x13]
    // 0x92cfe0: DecompressPointer r6
    //     0x92cfe0: add             x6, x6, HEAP, lsl #32
    // 0x92cfe4: stur            x6, [fp, #-0x98]
    // 0x92cfe8: ArrayLoad: r7 = r0[0]  ; List_4
    //     0x92cfe8: ldur            w7, [x0, #0x17]
    // 0x92cfec: DecompressPointer r7
    //     0x92cfec: add             x7, x7, HEAP, lsl #32
    // 0x92cff0: cmp             w7, NULL
    // 0x92cff4: b.ne            #0x92d000
    // 0x92cff8: ArrayLoad: r7 = r2[0]  ; List_4
    //     0x92cff8: ldur            w7, [x2, #0x17]
    // 0x92cffc: DecompressPointer r7
    //     0x92cffc: add             x7, x7, HEAP, lsl #32
    // 0x92d000: stur            x7, [fp, #-0x90]
    // 0x92d004: LoadField: r8 = r0->field_1f
    //     0x92d004: ldur            w8, [x0, #0x1f]
    // 0x92d008: DecompressPointer r8
    //     0x92d008: add             x8, x8, HEAP, lsl #32
    // 0x92d00c: cmp             w8, NULL
    // 0x92d010: b.ne            #0x92d01c
    // 0x92d014: LoadField: r8 = r2->field_1f
    //     0x92d014: ldur            w8, [x2, #0x1f]
    // 0x92d018: DecompressPointer r8
    //     0x92d018: add             x8, x8, HEAP, lsl #32
    // 0x92d01c: stur            x8, [fp, #-0x88]
    // 0x92d020: LoadField: r9 = r0->field_23
    //     0x92d020: ldur            w9, [x0, #0x23]
    // 0x92d024: DecompressPointer r9
    //     0x92d024: add             x9, x9, HEAP, lsl #32
    // 0x92d028: cmp             w9, NULL
    // 0x92d02c: b.ne            #0x92d038
    // 0x92d030: LoadField: r9 = r2->field_23
    //     0x92d030: ldur            w9, [x2, #0x23]
    // 0x92d034: DecompressPointer r9
    //     0x92d034: add             x9, x9, HEAP, lsl #32
    // 0x92d038: stur            x9, [fp, #-0x80]
    // 0x92d03c: LoadField: r10 = r0->field_1b
    //     0x92d03c: ldur            w10, [x0, #0x1b]
    // 0x92d040: DecompressPointer r10
    //     0x92d040: add             x10, x10, HEAP, lsl #32
    // 0x92d044: cmp             w10, NULL
    // 0x92d048: b.ne            #0x92d054
    // 0x92d04c: LoadField: r10 = r2->field_1b
    //     0x92d04c: ldur            w10, [x2, #0x1b]
    // 0x92d050: DecompressPointer r10
    //     0x92d050: add             x10, x10, HEAP, lsl #32
    // 0x92d054: stur            x10, [fp, #-0x78]
    // 0x92d058: LoadField: r11 = r0->field_27
    //     0x92d058: ldur            w11, [x0, #0x27]
    // 0x92d05c: DecompressPointer r11
    //     0x92d05c: add             x11, x11, HEAP, lsl #32
    // 0x92d060: cmp             w11, NULL
    // 0x92d064: b.ne            #0x92d070
    // 0x92d068: LoadField: r11 = r2->field_27
    //     0x92d068: ldur            w11, [x2, #0x27]
    // 0x92d06c: DecompressPointer r11
    //     0x92d06c: add             x11, x11, HEAP, lsl #32
    // 0x92d070: stur            x11, [fp, #-0x70]
    // 0x92d074: LoadField: r12 = r0->field_2b
    //     0x92d074: ldur            w12, [x0, #0x2b]
    // 0x92d078: DecompressPointer r12
    //     0x92d078: add             x12, x12, HEAP, lsl #32
    // 0x92d07c: cmp             w12, NULL
    // 0x92d080: b.ne            #0x92d08c
    // 0x92d084: LoadField: r12 = r2->field_2b
    //     0x92d084: ldur            w12, [x2, #0x2b]
    // 0x92d088: DecompressPointer r12
    //     0x92d088: add             x12, x12, HEAP, lsl #32
    // 0x92d08c: stur            x12, [fp, #-0x68]
    // 0x92d090: LoadField: r13 = r0->field_2f
    //     0x92d090: ldur            w13, [x0, #0x2f]
    // 0x92d094: DecompressPointer r13
    //     0x92d094: add             x13, x13, HEAP, lsl #32
    // 0x92d098: cmp             w13, NULL
    // 0x92d09c: b.ne            #0x92d0a8
    // 0x92d0a0: LoadField: r13 = r2->field_2f
    //     0x92d0a0: ldur            w13, [x2, #0x2f]
    // 0x92d0a4: DecompressPointer r13
    //     0x92d0a4: add             x13, x13, HEAP, lsl #32
    // 0x92d0a8: stur            x13, [fp, #-0x60]
    // 0x92d0ac: LoadField: r14 = r0->field_33
    //     0x92d0ac: ldur            w14, [x0, #0x33]
    // 0x92d0b0: DecompressPointer r14
    //     0x92d0b0: add             x14, x14, HEAP, lsl #32
    // 0x92d0b4: cmp             w14, NULL
    // 0x92d0b8: b.ne            #0x92d0c4
    // 0x92d0bc: LoadField: r14 = r2->field_33
    //     0x92d0bc: ldur            w14, [x2, #0x33]
    // 0x92d0c0: DecompressPointer r14
    //     0x92d0c0: add             x14, x14, HEAP, lsl #32
    // 0x92d0c4: stur            x14, [fp, #-0x58]
    // 0x92d0c8: LoadField: r19 = r0->field_37
    //     0x92d0c8: ldur            w19, [x0, #0x37]
    // 0x92d0cc: DecompressPointer r19
    //     0x92d0cc: add             x19, x19, HEAP, lsl #32
    // 0x92d0d0: cmp             w19, NULL
    // 0x92d0d4: b.ne            #0x92d0e0
    // 0x92d0d8: LoadField: r19 = r2->field_37
    //     0x92d0d8: ldur            w19, [x2, #0x37]
    // 0x92d0dc: DecompressPointer r19
    //     0x92d0dc: add             x19, x19, HEAP, lsl #32
    // 0x92d0e0: stur            x19, [fp, #-0x50]
    // 0x92d0e4: LoadField: r20 = r0->field_3b
    //     0x92d0e4: ldur            w20, [x0, #0x3b]
    // 0x92d0e8: DecompressPointer r20
    //     0x92d0e8: add             x20, x20, HEAP, lsl #32
    // 0x92d0ec: cmp             w20, NULL
    // 0x92d0f0: b.ne            #0x92d0fc
    // 0x92d0f4: LoadField: r20 = r2->field_3b
    //     0x92d0f4: ldur            w20, [x2, #0x3b]
    // 0x92d0f8: DecompressPointer r20
    //     0x92d0f8: add             x20, x20, HEAP, lsl #32
    // 0x92d0fc: stur            x20, [fp, #-0x48]
    // 0x92d100: LoadField: r23 = r0->field_3f
    //     0x92d100: ldur            w23, [x0, #0x3f]
    // 0x92d104: DecompressPointer r23
    //     0x92d104: add             x23, x23, HEAP, lsl #32
    // 0x92d108: cmp             w23, NULL
    // 0x92d10c: b.ne            #0x92d118
    // 0x92d110: LoadField: r23 = r2->field_3f
    //     0x92d110: ldur            w23, [x2, #0x3f]
    // 0x92d114: DecompressPointer r23
    //     0x92d114: add             x23, x23, HEAP, lsl #32
    // 0x92d118: stur            x23, [fp, #-0x40]
    // 0x92d11c: LoadField: r24 = r0->field_43
    //     0x92d11c: ldur            w24, [x0, #0x43]
    // 0x92d120: DecompressPointer r24
    //     0x92d120: add             x24, x24, HEAP, lsl #32
    // 0x92d124: cmp             w24, NULL
    // 0x92d128: b.ne            #0x92d134
    // 0x92d12c: LoadField: r24 = r2->field_43
    //     0x92d12c: ldur            w24, [x2, #0x43]
    // 0x92d130: DecompressPointer r24
    //     0x92d130: add             x24, x24, HEAP, lsl #32
    // 0x92d134: stur            x24, [fp, #-0x38]
    // 0x92d138: LoadField: r25 = r0->field_47
    //     0x92d138: ldur            w25, [x0, #0x47]
    // 0x92d13c: DecompressPointer r25
    //     0x92d13c: add             x25, x25, HEAP, lsl #32
    // 0x92d140: cmp             w25, NULL
    // 0x92d144: b.ne            #0x92d150
    // 0x92d148: LoadField: r25 = r2->field_47
    //     0x92d148: ldur            w25, [x2, #0x47]
    // 0x92d14c: DecompressPointer r25
    //     0x92d14c: add             x25, x25, HEAP, lsl #32
    // 0x92d150: stur            x25, [fp, #-0x30]
    // 0x92d154: LoadField: r1 = r0->field_4b
    //     0x92d154: ldur            w1, [x0, #0x4b]
    // 0x92d158: DecompressPointer r1
    //     0x92d158: add             x1, x1, HEAP, lsl #32
    // 0x92d15c: cmp             w1, NULL
    // 0x92d160: b.ne            #0x92d16c
    // 0x92d164: LoadField: r1 = r2->field_4b
    //     0x92d164: ldur            w1, [x2, #0x4b]
    // 0x92d168: DecompressPointer r1
    //     0x92d168: add             x1, x1, HEAP, lsl #32
    // 0x92d16c: stur            x1, [fp, #-0x20]
    // 0x92d170: LoadField: r1 = r0->field_4f
    //     0x92d170: ldur            w1, [x0, #0x4f]
    // 0x92d174: DecompressPointer r1
    //     0x92d174: add             x1, x1, HEAP, lsl #32
    // 0x92d178: cmp             w1, NULL
    // 0x92d17c: b.ne            #0x92d188
    // 0x92d180: LoadField: r1 = r2->field_4f
    //     0x92d180: ldur            w1, [x2, #0x4f]
    // 0x92d184: DecompressPointer r1
    //     0x92d184: add             x1, x1, HEAP, lsl #32
    // 0x92d188: stur            x1, [fp, #-0x28]
    // 0x92d18c: LoadField: r1 = r0->field_53
    //     0x92d18c: ldur            w1, [x0, #0x53]
    // 0x92d190: DecompressPointer r1
    //     0x92d190: add             x1, x1, HEAP, lsl #32
    // 0x92d194: cmp             w1, NULL
    // 0x92d198: b.ne            #0x92d1a8
    // 0x92d19c: LoadField: r0 = r2->field_53
    //     0x92d19c: ldur            w0, [x2, #0x53]
    // 0x92d1a0: DecompressPointer r0
    //     0x92d1a0: add             x0, x0, HEAP, lsl #32
    // 0x92d1a4: b               #0x92d1ac
    // 0x92d1a8: mov             x0, x1
    // 0x92d1ac: stur            x0, [fp, #-8]
    // 0x92d1b0: r0 = ExpandableThemeData()
    //     0x92d1b0: bl              #0x92d270  ; AllocateExpandableThemeDataStub -> ExpandableThemeData (size=0x5c)
    // 0x92d1b4: ldur            x1, [fp, #-0x18]
    // 0x92d1b8: StoreField: r0->field_7 = r1
    //     0x92d1b8: stur            w1, [x0, #7]
    // 0x92d1bc: ldur            x1, [fp, #-0xb0]
    // 0x92d1c0: StoreField: r0->field_b = r1
    //     0x92d1c0: stur            w1, [x0, #0xb]
    // 0x92d1c4: ldur            x1, [fp, #-0xa0]
    // 0x92d1c8: StoreField: r0->field_f = r1
    //     0x92d1c8: stur            w1, [x0, #0xf]
    // 0x92d1cc: ldur            x1, [fp, #-0x98]
    // 0x92d1d0: StoreField: r0->field_13 = r1
    //     0x92d1d0: stur            w1, [x0, #0x13]
    // 0x92d1d4: ldur            x1, [fp, #-0x90]
    // 0x92d1d8: ArrayStore: r0[0] = r1  ; List_4
    //     0x92d1d8: stur            w1, [x0, #0x17]
    // 0x92d1dc: ldur            x1, [fp, #-0x88]
    // 0x92d1e0: StoreField: r0->field_1f = r1
    //     0x92d1e0: stur            w1, [x0, #0x1f]
    // 0x92d1e4: ldur            x1, [fp, #-0x80]
    // 0x92d1e8: StoreField: r0->field_23 = r1
    //     0x92d1e8: stur            w1, [x0, #0x23]
    // 0x92d1ec: ldur            x1, [fp, #-0x78]
    // 0x92d1f0: StoreField: r0->field_1b = r1
    //     0x92d1f0: stur            w1, [x0, #0x1b]
    // 0x92d1f4: ldur            x1, [fp, #-0x70]
    // 0x92d1f8: StoreField: r0->field_27 = r1
    //     0x92d1f8: stur            w1, [x0, #0x27]
    // 0x92d1fc: ldur            x1, [fp, #-0x68]
    // 0x92d200: StoreField: r0->field_2b = r1
    //     0x92d200: stur            w1, [x0, #0x2b]
    // 0x92d204: ldur            x1, [fp, #-0x60]
    // 0x92d208: StoreField: r0->field_2f = r1
    //     0x92d208: stur            w1, [x0, #0x2f]
    // 0x92d20c: ldur            x1, [fp, #-0x58]
    // 0x92d210: StoreField: r0->field_33 = r1
    //     0x92d210: stur            w1, [x0, #0x33]
    // 0x92d214: ldur            x1, [fp, #-0x50]
    // 0x92d218: StoreField: r0->field_37 = r1
    //     0x92d218: stur            w1, [x0, #0x37]
    // 0x92d21c: ldur            x1, [fp, #-0x48]
    // 0x92d220: StoreField: r0->field_3b = r1
    //     0x92d220: stur            w1, [x0, #0x3b]
    // 0x92d224: ldur            x1, [fp, #-0x40]
    // 0x92d228: StoreField: r0->field_3f = r1
    //     0x92d228: stur            w1, [x0, #0x3f]
    // 0x92d22c: ldur            x1, [fp, #-0x38]
    // 0x92d230: StoreField: r0->field_43 = r1
    //     0x92d230: stur            w1, [x0, #0x43]
    // 0x92d234: ldur            x1, [fp, #-0x30]
    // 0x92d238: StoreField: r0->field_47 = r1
    //     0x92d238: stur            w1, [x0, #0x47]
    // 0x92d23c: ldur            x1, [fp, #-0x20]
    // 0x92d240: StoreField: r0->field_4b = r1
    //     0x92d240: stur            w1, [x0, #0x4b]
    // 0x92d244: ldur            x1, [fp, #-0x28]
    // 0x92d248: StoreField: r0->field_4f = r1
    //     0x92d248: stur            w1, [x0, #0x4f]
    // 0x92d24c: ldur            x1, [fp, #-8]
    // 0x92d250: StoreField: r0->field_53 = r1
    //     0x92d250: stur            w1, [x0, #0x53]
    // 0x92d254: ldur            x1, [fp, #-0xa8]
    // 0x92d258: StoreField: r0->field_57 = r1
    //     0x92d258: stur            w1, [x0, #0x57]
    // 0x92d25c: LeaveFrame
    //     0x92d25c: mov             SP, fp
    //     0x92d260: ldp             fp, lr, [SP], #0x10
    // 0x92d264: ret
    //     0x92d264: ret             
    // 0x92d268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92d268: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92d26c: b               #0x92cec4
  }
  _ isEmpty(/* No info */) {
    // ** addr: 0x92d27c, size: 0x5c
    // 0x92d27c: EnterFrame
    //     0x92d27c: stp             fp, lr, [SP, #-0x10]!
    //     0x92d280: mov             fp, SP
    // 0x92d284: AllocStack(0x18)
    //     0x92d284: sub             SP, SP, #0x18
    // 0x92d288: SetupParameters(ExpandableThemeData this /* r1 => r1, fp-0x8 */)
    //     0x92d288: stur            x1, [fp, #-8]
    // 0x92d28c: CheckStackOverflow
    //     0x92d28c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92d290: cmp             SP, x16
    //     0x92d294: b.ls            #0x92d2d0
    // 0x92d298: r0 = InitLateStaticField(0xf74) // [package:expandable/expandable.dart] ExpandableThemeData::empty
    //     0x92d298: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x92d29c: ldr             x0, [x0, #0x1ee8]
    //     0x92d2a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x92d2a4: cmp             w0, w16
    //     0x92d2a8: b.ne            #0x92d2b8
    //     0x92d2ac: add             x2, PP, #0x43, lsl #12  ; [pp+0x43518] Field <ExpandableThemeData.empty>: static late final (offset: 0xf74)
    //     0x92d2b0: ldr             x2, [x2, #0x518]
    //     0x92d2b4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x92d2b8: ldur            x16, [fp, #-8]
    // 0x92d2bc: stp             x0, x16, [SP]
    // 0x92d2c0: r0 = ==()
    //     0x92d2c0: bl              #0xd42f98  ; [package:expandable/expandable.dart] ExpandableThemeData::==
    // 0x92d2c4: LeaveFrame
    //     0x92d2c4: mov             SP, fp
    //     0x92d2c8: ldp             fp, lr, [SP], #0x10
    // 0x92d2cc: ret
    //     0x92d2cc: ret             
    // 0x92d2d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92d2d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92d2d4: b               #0x92d298
  }
  static ExpandableThemeData empty() {
    // ** addr: 0x92d2d8, size: 0x18
    // 0x92d2d8: EnterFrame
    //     0x92d2d8: stp             fp, lr, [SP, #-0x10]!
    //     0x92d2dc: mov             fp, SP
    // 0x92d2e0: r0 = ExpandableThemeData()
    //     0x92d2e0: bl              #0x92d270  ; AllocateExpandableThemeDataStub -> ExpandableThemeData (size=0x5c)
    // 0x92d2e4: LeaveFrame
    //     0x92d2e4: mov             SP, fp
    //     0x92d2e8: ldp             fp, lr, [SP], #0x10
    // 0x92d2ec: ret
    //     0x92d2ec: ret             
  }
  static _ of(/* No info */) {
    // ** addr: 0x92d2f0, size: 0x7c
    // 0x92d2f0: EnterFrame
    //     0x92d2f0: stp             fp, lr, [SP, #-0x10]!
    //     0x92d2f4: mov             fp, SP
    // 0x92d2f8: AllocStack(0x10)
    //     0x92d2f8: sub             SP, SP, #0x10
    // 0x92d2fc: CheckStackOverflow
    //     0x92d2fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92d300: cmp             SP, x16
    //     0x92d304: b.ls            #0x92d364
    // 0x92d308: tbnz            w2, #4, #0x92d324
    // 0x92d30c: r16 = <_ExpandableThemeNotifier>
    //     0x92d30c: add             x16, PP, #0x43, lsl #12  ; [pp+0x43520] TypeArguments: <_ExpandableThemeNotifier>
    //     0x92d310: ldr             x16, [x16, #0x520]
    // 0x92d314: stp             x1, x16, [SP]
    // 0x92d318: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x92d318: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x92d31c: r0 = dependOnInheritedWidgetOfExactType()
    //     0x92d31c: bl              #0x6396d8  ; [package:flutter/src/widgets/framework.dart] Element::dependOnInheritedWidgetOfExactType
    // 0x92d320: b               #0x92d338
    // 0x92d324: r16 = <_ExpandableThemeNotifier>
    //     0x92d324: add             x16, PP, #0x43, lsl #12  ; [pp+0x43520] TypeArguments: <_ExpandableThemeNotifier>
    //     0x92d328: ldr             x16, [x16, #0x520]
    // 0x92d32c: stp             x1, x16, [SP]
    // 0x92d330: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x92d330: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x92d334: r0 = findAncestorWidgetOfExactType()
    //     0x92d334: bl              #0x67a014  ; [package:flutter/src/widgets/framework.dart] Element::findAncestorWidgetOfExactType
    // 0x92d338: r0 = InitLateStaticField(0xf70) // [package:expandable/expandable.dart] ExpandableThemeData::defaults
    //     0x92d338: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x92d33c: ldr             x0, [x0, #0x1ee0]
    //     0x92d340: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x92d344: cmp             w0, w16
    //     0x92d348: b.ne            #0x92d358
    //     0x92d34c: add             x2, PP, #0x43, lsl #12  ; [pp+0x43510] Field <ExpandableThemeData.defaults>: static late final (offset: 0xf70)
    //     0x92d350: ldr             x2, [x2, #0x510]
    //     0x92d354: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x92d358: LeaveFrame
    //     0x92d358: mov             SP, fp
    //     0x92d35c: ldp             fp, lr, [SP], #0x10
    // 0x92d360: ret
    //     0x92d360: ret             
    // 0x92d364: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92d364: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92d368: b               #0x92d308
  }
  _ isFull(/* No info */) {
    // ** addr: 0x92d36c, size: 0x148
    // 0x92d36c: LoadField: r2 = r1->field_7
    //     0x92d36c: ldur            w2, [x1, #7]
    // 0x92d370: DecompressPointer r2
    //     0x92d370: add             x2, x2, HEAP, lsl #32
    // 0x92d374: cmp             w2, NULL
    // 0x92d378: b.eq            #0x92d4ac
    // 0x92d37c: LoadField: r2 = r1->field_b
    //     0x92d37c: ldur            w2, [x1, #0xb]
    // 0x92d380: DecompressPointer r2
    //     0x92d380: add             x2, x2, HEAP, lsl #32
    // 0x92d384: cmp             w2, NULL
    // 0x92d388: b.eq            #0x92d4ac
    // 0x92d38c: LoadField: r2 = r1->field_57
    //     0x92d38c: ldur            w2, [x1, #0x57]
    // 0x92d390: DecompressPointer r2
    //     0x92d390: add             x2, x2, HEAP, lsl #32
    // 0x92d394: cmp             w2, NULL
    // 0x92d398: b.eq            #0x92d4ac
    // 0x92d39c: LoadField: r2 = r1->field_f
    //     0x92d39c: ldur            w2, [x1, #0xf]
    // 0x92d3a0: DecompressPointer r2
    //     0x92d3a0: add             x2, x2, HEAP, lsl #32
    // 0x92d3a4: cmp             w2, NULL
    // 0x92d3a8: b.eq            #0x92d4ac
    // 0x92d3ac: LoadField: r2 = r1->field_13
    //     0x92d3ac: ldur            w2, [x1, #0x13]
    // 0x92d3b0: DecompressPointer r2
    //     0x92d3b0: add             x2, x2, HEAP, lsl #32
    // 0x92d3b4: cmp             w2, NULL
    // 0x92d3b8: b.eq            #0x92d4ac
    // 0x92d3bc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x92d3bc: ldur            w2, [x1, #0x17]
    // 0x92d3c0: DecompressPointer r2
    //     0x92d3c0: add             x2, x2, HEAP, lsl #32
    // 0x92d3c4: cmp             w2, NULL
    // 0x92d3c8: b.eq            #0x92d4ac
    // 0x92d3cc: LoadField: r2 = r1->field_1f
    //     0x92d3cc: ldur            w2, [x1, #0x1f]
    // 0x92d3d0: DecompressPointer r2
    //     0x92d3d0: add             x2, x2, HEAP, lsl #32
    // 0x92d3d4: cmp             w2, NULL
    // 0x92d3d8: b.eq            #0x92d4ac
    // 0x92d3dc: LoadField: r2 = r1->field_23
    //     0x92d3dc: ldur            w2, [x1, #0x23]
    // 0x92d3e0: DecompressPointer r2
    //     0x92d3e0: add             x2, x2, HEAP, lsl #32
    // 0x92d3e4: cmp             w2, NULL
    // 0x92d3e8: b.eq            #0x92d4ac
    // 0x92d3ec: LoadField: r2 = r1->field_1b
    //     0x92d3ec: ldur            w2, [x1, #0x1b]
    // 0x92d3f0: DecompressPointer r2
    //     0x92d3f0: add             x2, x2, HEAP, lsl #32
    // 0x92d3f4: cmp             w2, NULL
    // 0x92d3f8: b.eq            #0x92d4ac
    // 0x92d3fc: LoadField: r2 = r1->field_27
    //     0x92d3fc: ldur            w2, [x1, #0x27]
    // 0x92d400: DecompressPointer r2
    //     0x92d400: add             x2, x2, HEAP, lsl #32
    // 0x92d404: cmp             w2, NULL
    // 0x92d408: b.eq            #0x92d4ac
    // 0x92d40c: LoadField: r2 = r1->field_2b
    //     0x92d40c: ldur            w2, [x1, #0x2b]
    // 0x92d410: DecompressPointer r2
    //     0x92d410: add             x2, x2, HEAP, lsl #32
    // 0x92d414: cmp             w2, NULL
    // 0x92d418: b.eq            #0x92d4ac
    // 0x92d41c: LoadField: r2 = r1->field_2f
    //     0x92d41c: ldur            w2, [x1, #0x2f]
    // 0x92d420: DecompressPointer r2
    //     0x92d420: add             x2, x2, HEAP, lsl #32
    // 0x92d424: cmp             w2, NULL
    // 0x92d428: b.eq            #0x92d4ac
    // 0x92d42c: LoadField: r2 = r1->field_33
    //     0x92d42c: ldur            w2, [x1, #0x33]
    // 0x92d430: DecompressPointer r2
    //     0x92d430: add             x2, x2, HEAP, lsl #32
    // 0x92d434: cmp             w2, NULL
    // 0x92d438: b.eq            #0x92d4ac
    // 0x92d43c: LoadField: r2 = r1->field_37
    //     0x92d43c: ldur            w2, [x1, #0x37]
    // 0x92d440: DecompressPointer r2
    //     0x92d440: add             x2, x2, HEAP, lsl #32
    // 0x92d444: cmp             w2, NULL
    // 0x92d448: b.eq            #0x92d4ac
    // 0x92d44c: LoadField: r2 = r1->field_3b
    //     0x92d44c: ldur            w2, [x1, #0x3b]
    // 0x92d450: DecompressPointer r2
    //     0x92d450: add             x2, x2, HEAP, lsl #32
    // 0x92d454: cmp             w2, NULL
    // 0x92d458: b.eq            #0x92d4ac
    // 0x92d45c: LoadField: r2 = r1->field_3f
    //     0x92d45c: ldur            w2, [x1, #0x3f]
    // 0x92d460: DecompressPointer r2
    //     0x92d460: add             x2, x2, HEAP, lsl #32
    // 0x92d464: cmp             w2, NULL
    // 0x92d468: b.eq            #0x92d4ac
    // 0x92d46c: LoadField: r2 = r1->field_4b
    //     0x92d46c: ldur            w2, [x1, #0x4b]
    // 0x92d470: DecompressPointer r2
    //     0x92d470: add             x2, x2, HEAP, lsl #32
    // 0x92d474: cmp             w2, NULL
    // 0x92d478: b.eq            #0x92d4ac
    // 0x92d47c: LoadField: r2 = r1->field_4f
    //     0x92d47c: ldur            w2, [x1, #0x4f]
    // 0x92d480: DecompressPointer r2
    //     0x92d480: add             x2, x2, HEAP, lsl #32
    // 0x92d484: cmp             w2, NULL
    // 0x92d488: b.eq            #0x92d4ac
    // 0x92d48c: LoadField: r2 = r1->field_53
    //     0x92d48c: ldur            w2, [x1, #0x53]
    // 0x92d490: DecompressPointer r2
    //     0x92d490: add             x2, x2, HEAP, lsl #32
    // 0x92d494: cmp             w2, NULL
    // 0x92d498: r16 = true
    //     0x92d498: add             x16, NULL, #0x20  ; true
    // 0x92d49c: r17 = false
    //     0x92d49c: add             x17, NULL, #0x30  ; false
    // 0x92d4a0: csel            x1, x16, x17, ne
    // 0x92d4a4: mov             x0, x1
    // 0x92d4a8: b               #0x92d4b0
    // 0x92d4ac: r0 = false
    //     0x92d4ac: add             x0, NULL, #0x30  ; false
    // 0x92d4b0: ret
    //     0x92d4b0: ret             
  }
  static ExpandableThemeData defaults() {
    // ** addr: 0x92d4b4, size: 0xd4
    // 0x92d4b4: EnterFrame
    //     0x92d4b4: stp             fp, lr, [SP, #-0x10]!
    //     0x92d4b8: mov             fp, SP
    // 0x92d4bc: r0 = ExpandableThemeData()
    //     0x92d4bc: bl              #0x92d270  ; AllocateExpandableThemeDataStub -> ExpandableThemeData (size=0x5c)
    // 0x92d4c0: r1 = Instance_Color
    //     0x92d4c0: ldr             x1, [PP, #0x5470]  ; [pp+0x5470] Obj!Color@e270c1
    // 0x92d4c4: StoreField: r0->field_7 = r1
    //     0x92d4c4: stur            w1, [x0, #7]
    // 0x92d4c8: r1 = true
    //     0x92d4c8: add             x1, NULL, #0x20  ; true
    // 0x92d4cc: StoreField: r0->field_b = r1
    //     0x92d4cc: stur            w1, [x0, #0xb]
    // 0x92d4d0: r2 = Instance_Duration
    //     0x92d4d0: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0x92d4d4: ldr             x2, [x2, #0x9c0]
    // 0x92d4d8: StoreField: r0->field_f = r2
    //     0x92d4d8: stur            w2, [x0, #0xf]
    // 0x92d4dc: StoreField: r0->field_13 = r2
    //     0x92d4dc: stur            w2, [x0, #0x13]
    // 0x92d4e0: r2 = 0.500000
    //     0x92d4e0: ldr             x2, [PP, #0x4928]  ; [pp+0x4928] 0.5
    // 0x92d4e4: ArrayStore: r0[0] = r2  ; List_4
    //     0x92d4e4: stur            w2, [x0, #0x17]
    // 0x92d4e8: r2 = Instance__Linear
    //     0x92d4e8: ldr             x2, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0x92d4ec: StoreField: r0->field_1f = r2
    //     0x92d4ec: stur            w2, [x0, #0x1f]
    // 0x92d4f0: r2 = Instance_Cubic
    //     0x92d4f0: ldr             x2, [PP, #0x6e20]  ; [pp+0x6e20] Obj!Cubic@e14d41
    // 0x92d4f4: StoreField: r0->field_23 = r2
    //     0x92d4f4: stur            w2, [x0, #0x23]
    // 0x92d4f8: r2 = Instance_Alignment
    //     0x92d4f8: add             x2, PP, #0x25, lsl #12  ; [pp+0x25370] Obj!Alignment@e13e11
    //     0x92d4fc: ldr             x2, [x2, #0x370]
    // 0x92d500: StoreField: r0->field_1b = r2
    //     0x92d500: stur            w2, [x0, #0x1b]
    // 0x92d504: r2 = Instance_ExpandablePanelHeaderAlignment
    //     0x92d504: add             x2, PP, #0x43, lsl #12  ; [pp+0x43528] Obj!ExpandablePanelHeaderAlignment@e373c1
    //     0x92d508: ldr             x2, [x2, #0x528]
    // 0x92d50c: StoreField: r0->field_27 = r2
    //     0x92d50c: stur            w2, [x0, #0x27]
    // 0x92d510: r2 = Instance_ExpandablePanelBodyAlignment
    //     0x92d510: add             x2, PP, #0x43, lsl #12  ; [pp+0x43530] Obj!ExpandablePanelBodyAlignment@e373a1
    //     0x92d514: ldr             x2, [x2, #0x530]
    // 0x92d518: StoreField: r0->field_2b = r2
    //     0x92d518: stur            w2, [x0, #0x2b]
    // 0x92d51c: r2 = Instance_ExpandablePanelIconPlacement
    //     0x92d51c: add             x2, PP, #0x43, lsl #12  ; [pp+0x434f8] Obj!ExpandablePanelIconPlacement@e37401
    //     0x92d520: ldr             x2, [x2, #0x4f8]
    // 0x92d524: StoreField: r0->field_2f = r2
    //     0x92d524: stur            w2, [x0, #0x2f]
    // 0x92d528: StoreField: r0->field_33 = r1
    //     0x92d528: stur            w1, [x0, #0x33]
    // 0x92d52c: r2 = false
    //     0x92d52c: add             x2, NULL, #0x30  ; false
    // 0x92d530: StoreField: r0->field_37 = r2
    //     0x92d530: stur            w2, [x0, #0x37]
    // 0x92d534: StoreField: r0->field_3b = r2
    //     0x92d534: stur            w2, [x0, #0x3b]
    // 0x92d538: StoreField: r0->field_3f = r1
    //     0x92d538: stur            w1, [x0, #0x3f]
    // 0x92d53c: r1 = 24.000000
    //     0x92d53c: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0x92d540: ldr             x1, [x1, #0x368]
    // 0x92d544: StoreField: r0->field_43 = r1
    //     0x92d544: stur            w1, [x0, #0x43]
    // 0x92d548: r1 = Instance_EdgeInsets
    //     0x92d548: add             x1, PP, #0x29, lsl #12  ; [pp+0x29de8] Obj!EdgeInsets@e120d1
    //     0x92d54c: ldr             x1, [x1, #0xde8]
    // 0x92d550: StoreField: r0->field_47 = r1
    //     0x92d550: stur            w1, [x0, #0x47]
    // 0x92d554: r1 = -3.141593
    //     0x92d554: add             x1, PP, #0x43, lsl #12  ; [pp+0x43538] -3.141592653589793
    //     0x92d558: ldr             x1, [x1, #0x538]
    // 0x92d55c: StoreField: r0->field_4b = r1
    //     0x92d55c: stur            w1, [x0, #0x4b]
    // 0x92d560: r1 = Instance_IconData
    //     0x92d560: add             x1, PP, #0x43, lsl #12  ; [pp+0x43540] Obj!IconData@e0ff91
    //     0x92d564: ldr             x1, [x1, #0x540]
    // 0x92d568: StoreField: r0->field_4f = r1
    //     0x92d568: stur            w1, [x0, #0x4f]
    // 0x92d56c: StoreField: r0->field_53 = r1
    //     0x92d56c: stur            w1, [x0, #0x53]
    // 0x92d570: r1 = Instance_BorderRadius
    //     0x92d570: add             x1, PP, #0x31, lsl #12  ; [pp+0x31b68] Obj!BorderRadius@e13a71
    //     0x92d574: ldr             x1, [x1, #0xb68]
    // 0x92d578: StoreField: r0->field_57 = r1
    //     0x92d578: stur            w1, [x0, #0x57]
    // 0x92d57c: LeaveFrame
    //     0x92d57c: mov             SP, fp
    //     0x92d580: ldp             fp, lr, [SP], #0x10
    // 0x92d584: ret
    //     0x92d584: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd42f98, size: 0x370
    // 0xd42f98: EnterFrame
    //     0xd42f98: stp             fp, lr, [SP, #-0x10]!
    //     0xd42f9c: mov             fp, SP
    // 0xd42fa0: AllocStack(0x10)
    //     0xd42fa0: sub             SP, SP, #0x10
    // 0xd42fa4: CheckStackOverflow
    //     0xd42fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd42fa8: cmp             SP, x16
    //     0xd42fac: b.ls            #0xd43300
    // 0xd42fb0: ldr             x1, [fp, #0x10]
    // 0xd42fb4: cmp             w1, NULL
    // 0xd42fb8: b.ne            #0xd42fcc
    // 0xd42fbc: r0 = false
    //     0xd42fbc: add             x0, NULL, #0x30  ; false
    // 0xd42fc0: LeaveFrame
    //     0xd42fc0: mov             SP, fp
    //     0xd42fc4: ldp             fp, lr, [SP], #0x10
    // 0xd42fc8: ret
    //     0xd42fc8: ret             
    // 0xd42fcc: ldr             x2, [fp, #0x18]
    // 0xd42fd0: cmp             w2, w1
    // 0xd42fd4: b.ne            #0xd42fe8
    // 0xd42fd8: r0 = true
    //     0xd42fd8: add             x0, NULL, #0x20  ; true
    // 0xd42fdc: LeaveFrame
    //     0xd42fdc: mov             SP, fp
    //     0xd42fe0: ldp             fp, lr, [SP], #0x10
    // 0xd42fe4: ret
    //     0xd42fe4: ret             
    // 0xd42fe8: r0 = 60
    //     0xd42fe8: movz            x0, #0x3c
    // 0xd42fec: branchIfSmi(r1, 0xd42ff8)
    //     0xd42fec: tbz             w1, #0, #0xd42ff8
    // 0xd42ff0: r0 = LoadClassIdInstr(r1)
    //     0xd42ff0: ldur            x0, [x1, #-1]
    //     0xd42ff4: ubfx            x0, x0, #0xc, #0x14
    // 0xd42ff8: r17 = 5558
    //     0xd42ff8: movz            x17, #0x15b6
    // 0xd42ffc: cmp             x0, x17
    // 0xd43000: b.ne            #0xd432f0
    // 0xd43004: LoadField: r0 = r2->field_7
    //     0xd43004: ldur            w0, [x2, #7]
    // 0xd43008: DecompressPointer r0
    //     0xd43008: add             x0, x0, HEAP, lsl #32
    // 0xd4300c: LoadField: r3 = r1->field_7
    //     0xd4300c: ldur            w3, [x1, #7]
    // 0xd43010: DecompressPointer r3
    //     0xd43010: add             x3, x3, HEAP, lsl #32
    // 0xd43014: r4 = LoadClassIdInstr(r0)
    //     0xd43014: ldur            x4, [x0, #-1]
    //     0xd43018: ubfx            x4, x4, #0xc, #0x14
    // 0xd4301c: stp             x3, x0, [SP]
    // 0xd43020: mov             x0, x4
    // 0xd43024: mov             lr, x0
    // 0xd43028: ldr             lr, [x21, lr, lsl #3]
    // 0xd4302c: blr             lr
    // 0xd43030: tbnz            w0, #4, #0xd432e0
    // 0xd43034: ldr             x2, [fp, #0x18]
    // 0xd43038: ldr             x1, [fp, #0x10]
    // 0xd4303c: LoadField: r0 = r2->field_b
    //     0xd4303c: ldur            w0, [x2, #0xb]
    // 0xd43040: DecompressPointer r0
    //     0xd43040: add             x0, x0, HEAP, lsl #32
    // 0xd43044: LoadField: r3 = r1->field_b
    //     0xd43044: ldur            w3, [x1, #0xb]
    // 0xd43048: DecompressPointer r3
    //     0xd43048: add             x3, x3, HEAP, lsl #32
    // 0xd4304c: cmp             w0, w3
    // 0xd43050: b.ne            #0xd432e0
    // 0xd43054: LoadField: r0 = r2->field_57
    //     0xd43054: ldur            w0, [x2, #0x57]
    // 0xd43058: DecompressPointer r0
    //     0xd43058: add             x0, x0, HEAP, lsl #32
    // 0xd4305c: LoadField: r3 = r1->field_57
    //     0xd4305c: ldur            w3, [x1, #0x57]
    // 0xd43060: DecompressPointer r3
    //     0xd43060: add             x3, x3, HEAP, lsl #32
    // 0xd43064: r4 = LoadClassIdInstr(r0)
    //     0xd43064: ldur            x4, [x0, #-1]
    //     0xd43068: ubfx            x4, x4, #0xc, #0x14
    // 0xd4306c: stp             x3, x0, [SP]
    // 0xd43070: mov             x0, x4
    // 0xd43074: mov             lr, x0
    // 0xd43078: ldr             lr, [x21, lr, lsl #3]
    // 0xd4307c: blr             lr
    // 0xd43080: tbnz            w0, #4, #0xd432e0
    // 0xd43084: ldr             x2, [fp, #0x18]
    // 0xd43088: ldr             x1, [fp, #0x10]
    // 0xd4308c: LoadField: r0 = r2->field_f
    //     0xd4308c: ldur            w0, [x2, #0xf]
    // 0xd43090: DecompressPointer r0
    //     0xd43090: add             x0, x0, HEAP, lsl #32
    // 0xd43094: LoadField: r3 = r1->field_f
    //     0xd43094: ldur            w3, [x1, #0xf]
    // 0xd43098: DecompressPointer r3
    //     0xd43098: add             x3, x3, HEAP, lsl #32
    // 0xd4309c: r4 = LoadClassIdInstr(r0)
    //     0xd4309c: ldur            x4, [x0, #-1]
    //     0xd430a0: ubfx            x4, x4, #0xc, #0x14
    // 0xd430a4: stp             x3, x0, [SP]
    // 0xd430a8: mov             x0, x4
    // 0xd430ac: mov             lr, x0
    // 0xd430b0: ldr             lr, [x21, lr, lsl #3]
    // 0xd430b4: blr             lr
    // 0xd430b8: tbnz            w0, #4, #0xd432e0
    // 0xd430bc: ldr             x2, [fp, #0x18]
    // 0xd430c0: ldr             x1, [fp, #0x10]
    // 0xd430c4: LoadField: r0 = r2->field_13
    //     0xd430c4: ldur            w0, [x2, #0x13]
    // 0xd430c8: DecompressPointer r0
    //     0xd430c8: add             x0, x0, HEAP, lsl #32
    // 0xd430cc: LoadField: r3 = r1->field_13
    //     0xd430cc: ldur            w3, [x1, #0x13]
    // 0xd430d0: DecompressPointer r3
    //     0xd430d0: add             x3, x3, HEAP, lsl #32
    // 0xd430d4: r4 = LoadClassIdInstr(r0)
    //     0xd430d4: ldur            x4, [x0, #-1]
    //     0xd430d8: ubfx            x4, x4, #0xc, #0x14
    // 0xd430dc: stp             x3, x0, [SP]
    // 0xd430e0: mov             x0, x4
    // 0xd430e4: mov             lr, x0
    // 0xd430e8: ldr             lr, [x21, lr, lsl #3]
    // 0xd430ec: blr             lr
    // 0xd430f0: tbnz            w0, #4, #0xd432e0
    // 0xd430f4: ldr             x2, [fp, #0x18]
    // 0xd430f8: ldr             x1, [fp, #0x10]
    // 0xd430fc: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xd430fc: ldur            w0, [x2, #0x17]
    // 0xd43100: DecompressPointer r0
    //     0xd43100: add             x0, x0, HEAP, lsl #32
    // 0xd43104: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xd43104: ldur            w3, [x1, #0x17]
    // 0xd43108: DecompressPointer r3
    //     0xd43108: add             x3, x3, HEAP, lsl #32
    // 0xd4310c: r4 = LoadClassIdInstr(r0)
    //     0xd4310c: ldur            x4, [x0, #-1]
    //     0xd43110: ubfx            x4, x4, #0xc, #0x14
    // 0xd43114: stp             x3, x0, [SP]
    // 0xd43118: mov             x0, x4
    // 0xd4311c: mov             lr, x0
    // 0xd43120: ldr             lr, [x21, lr, lsl #3]
    // 0xd43124: blr             lr
    // 0xd43128: tbnz            w0, #4, #0xd432e0
    // 0xd4312c: ldr             x2, [fp, #0x18]
    // 0xd43130: ldr             x1, [fp, #0x10]
    // 0xd43134: LoadField: r0 = r2->field_1f
    //     0xd43134: ldur            w0, [x2, #0x1f]
    // 0xd43138: DecompressPointer r0
    //     0xd43138: add             x0, x0, HEAP, lsl #32
    // 0xd4313c: LoadField: r3 = r1->field_1f
    //     0xd4313c: ldur            w3, [x1, #0x1f]
    // 0xd43140: DecompressPointer r3
    //     0xd43140: add             x3, x3, HEAP, lsl #32
    // 0xd43144: cmp             w0, w3
    // 0xd43148: b.ne            #0xd432e0
    // 0xd4314c: LoadField: r0 = r2->field_23
    //     0xd4314c: ldur            w0, [x2, #0x23]
    // 0xd43150: DecompressPointer r0
    //     0xd43150: add             x0, x0, HEAP, lsl #32
    // 0xd43154: LoadField: r3 = r1->field_23
    //     0xd43154: ldur            w3, [x1, #0x23]
    // 0xd43158: DecompressPointer r3
    //     0xd43158: add             x3, x3, HEAP, lsl #32
    // 0xd4315c: cmp             w0, w3
    // 0xd43160: b.ne            #0xd432e0
    // 0xd43164: LoadField: r0 = r2->field_1b
    //     0xd43164: ldur            w0, [x2, #0x1b]
    // 0xd43168: DecompressPointer r0
    //     0xd43168: add             x0, x0, HEAP, lsl #32
    // 0xd4316c: LoadField: r3 = r1->field_1b
    //     0xd4316c: ldur            w3, [x1, #0x1b]
    // 0xd43170: DecompressPointer r3
    //     0xd43170: add             x3, x3, HEAP, lsl #32
    // 0xd43174: r4 = LoadClassIdInstr(r0)
    //     0xd43174: ldur            x4, [x0, #-1]
    //     0xd43178: ubfx            x4, x4, #0xc, #0x14
    // 0xd4317c: stp             x3, x0, [SP]
    // 0xd43180: mov             x0, x4
    // 0xd43184: mov             lr, x0
    // 0xd43188: ldr             lr, [x21, lr, lsl #3]
    // 0xd4318c: blr             lr
    // 0xd43190: tbnz            w0, #4, #0xd432e0
    // 0xd43194: ldr             x2, [fp, #0x18]
    // 0xd43198: ldr             x1, [fp, #0x10]
    // 0xd4319c: LoadField: r0 = r2->field_27
    //     0xd4319c: ldur            w0, [x2, #0x27]
    // 0xd431a0: DecompressPointer r0
    //     0xd431a0: add             x0, x0, HEAP, lsl #32
    // 0xd431a4: LoadField: r3 = r1->field_27
    //     0xd431a4: ldur            w3, [x1, #0x27]
    // 0xd431a8: DecompressPointer r3
    //     0xd431a8: add             x3, x3, HEAP, lsl #32
    // 0xd431ac: cmp             w0, w3
    // 0xd431b0: b.ne            #0xd432e0
    // 0xd431b4: LoadField: r0 = r2->field_2b
    //     0xd431b4: ldur            w0, [x2, #0x2b]
    // 0xd431b8: DecompressPointer r0
    //     0xd431b8: add             x0, x0, HEAP, lsl #32
    // 0xd431bc: LoadField: r3 = r1->field_2b
    //     0xd431bc: ldur            w3, [x1, #0x2b]
    // 0xd431c0: DecompressPointer r3
    //     0xd431c0: add             x3, x3, HEAP, lsl #32
    // 0xd431c4: cmp             w0, w3
    // 0xd431c8: b.ne            #0xd432e0
    // 0xd431cc: LoadField: r0 = r2->field_2f
    //     0xd431cc: ldur            w0, [x2, #0x2f]
    // 0xd431d0: DecompressPointer r0
    //     0xd431d0: add             x0, x0, HEAP, lsl #32
    // 0xd431d4: LoadField: r3 = r1->field_2f
    //     0xd431d4: ldur            w3, [x1, #0x2f]
    // 0xd431d8: DecompressPointer r3
    //     0xd431d8: add             x3, x3, HEAP, lsl #32
    // 0xd431dc: cmp             w0, w3
    // 0xd431e0: b.ne            #0xd432e0
    // 0xd431e4: LoadField: r0 = r2->field_33
    //     0xd431e4: ldur            w0, [x2, #0x33]
    // 0xd431e8: DecompressPointer r0
    //     0xd431e8: add             x0, x0, HEAP, lsl #32
    // 0xd431ec: LoadField: r3 = r1->field_33
    //     0xd431ec: ldur            w3, [x1, #0x33]
    // 0xd431f0: DecompressPointer r3
    //     0xd431f0: add             x3, x3, HEAP, lsl #32
    // 0xd431f4: cmp             w0, w3
    // 0xd431f8: b.ne            #0xd432e0
    // 0xd431fc: LoadField: r0 = r2->field_37
    //     0xd431fc: ldur            w0, [x2, #0x37]
    // 0xd43200: DecompressPointer r0
    //     0xd43200: add             x0, x0, HEAP, lsl #32
    // 0xd43204: LoadField: r3 = r1->field_37
    //     0xd43204: ldur            w3, [x1, #0x37]
    // 0xd43208: DecompressPointer r3
    //     0xd43208: add             x3, x3, HEAP, lsl #32
    // 0xd4320c: cmp             w0, w3
    // 0xd43210: b.ne            #0xd432e0
    // 0xd43214: LoadField: r0 = r2->field_3b
    //     0xd43214: ldur            w0, [x2, #0x3b]
    // 0xd43218: DecompressPointer r0
    //     0xd43218: add             x0, x0, HEAP, lsl #32
    // 0xd4321c: LoadField: r3 = r1->field_3b
    //     0xd4321c: ldur            w3, [x1, #0x3b]
    // 0xd43220: DecompressPointer r3
    //     0xd43220: add             x3, x3, HEAP, lsl #32
    // 0xd43224: cmp             w0, w3
    // 0xd43228: b.ne            #0xd432e0
    // 0xd4322c: LoadField: r0 = r2->field_3f
    //     0xd4322c: ldur            w0, [x2, #0x3f]
    // 0xd43230: DecompressPointer r0
    //     0xd43230: add             x0, x0, HEAP, lsl #32
    // 0xd43234: LoadField: r3 = r1->field_3f
    //     0xd43234: ldur            w3, [x1, #0x3f]
    // 0xd43238: DecompressPointer r3
    //     0xd43238: add             x3, x3, HEAP, lsl #32
    // 0xd4323c: cmp             w0, w3
    // 0xd43240: b.ne            #0xd432e0
    // 0xd43244: LoadField: r0 = r2->field_4b
    //     0xd43244: ldur            w0, [x2, #0x4b]
    // 0xd43248: DecompressPointer r0
    //     0xd43248: add             x0, x0, HEAP, lsl #32
    // 0xd4324c: LoadField: r3 = r1->field_4b
    //     0xd4324c: ldur            w3, [x1, #0x4b]
    // 0xd43250: DecompressPointer r3
    //     0xd43250: add             x3, x3, HEAP, lsl #32
    // 0xd43254: r4 = LoadClassIdInstr(r0)
    //     0xd43254: ldur            x4, [x0, #-1]
    //     0xd43258: ubfx            x4, x4, #0xc, #0x14
    // 0xd4325c: stp             x3, x0, [SP]
    // 0xd43260: mov             x0, x4
    // 0xd43264: mov             lr, x0
    // 0xd43268: ldr             lr, [x21, lr, lsl #3]
    // 0xd4326c: blr             lr
    // 0xd43270: tbnz            w0, #4, #0xd432e0
    // 0xd43274: ldr             x2, [fp, #0x18]
    // 0xd43278: ldr             x1, [fp, #0x10]
    // 0xd4327c: LoadField: r0 = r2->field_4f
    //     0xd4327c: ldur            w0, [x2, #0x4f]
    // 0xd43280: DecompressPointer r0
    //     0xd43280: add             x0, x0, HEAP, lsl #32
    // 0xd43284: LoadField: r3 = r1->field_4f
    //     0xd43284: ldur            w3, [x1, #0x4f]
    // 0xd43288: DecompressPointer r3
    //     0xd43288: add             x3, x3, HEAP, lsl #32
    // 0xd4328c: r4 = LoadClassIdInstr(r0)
    //     0xd4328c: ldur            x4, [x0, #-1]
    //     0xd43290: ubfx            x4, x4, #0xc, #0x14
    // 0xd43294: stp             x3, x0, [SP]
    // 0xd43298: mov             x0, x4
    // 0xd4329c: mov             lr, x0
    // 0xd432a0: ldr             lr, [x21, lr, lsl #3]
    // 0xd432a4: blr             lr
    // 0xd432a8: tbnz            w0, #4, #0xd432e0
    // 0xd432ac: ldr             x1, [fp, #0x18]
    // 0xd432b0: ldr             x0, [fp, #0x10]
    // 0xd432b4: LoadField: r2 = r1->field_53
    //     0xd432b4: ldur            w2, [x1, #0x53]
    // 0xd432b8: DecompressPointer r2
    //     0xd432b8: add             x2, x2, HEAP, lsl #32
    // 0xd432bc: LoadField: r1 = r0->field_53
    //     0xd432bc: ldur            w1, [x0, #0x53]
    // 0xd432c0: DecompressPointer r1
    //     0xd432c0: add             x1, x1, HEAP, lsl #32
    // 0xd432c4: r0 = LoadClassIdInstr(r2)
    //     0xd432c4: ldur            x0, [x2, #-1]
    //     0xd432c8: ubfx            x0, x0, #0xc, #0x14
    // 0xd432cc: stp             x1, x2, [SP]
    // 0xd432d0: mov             lr, x0
    // 0xd432d4: ldr             lr, [x21, lr, lsl #3]
    // 0xd432d8: blr             lr
    // 0xd432dc: b               #0xd432e4
    // 0xd432e0: r0 = false
    //     0xd432e0: add             x0, NULL, #0x30  ; false
    // 0xd432e4: LeaveFrame
    //     0xd432e4: mov             SP, fp
    //     0xd432e8: ldp             fp, lr, [SP], #0x10
    // 0xd432ec: ret
    //     0xd432ec: ret             
    // 0xd432f0: r0 = false
    //     0xd432f0: add             x0, NULL, #0x30  ; false
    // 0xd432f4: LeaveFrame
    //     0xd432f4: mov             SP, fp
    //     0xd432f8: ldp             fp, lr, [SP], #0x10
    // 0xd432fc: ret
    //     0xd432fc: ret             
    // 0xd43300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd43300: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd43304: b               #0xd42fb0
  }
}

// class id: 7108, size: 0x14, field offset: 0x14
enum ExpandablePanelBodyAlignment extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc48040, size: 0x64
    // 0xc48040: EnterFrame
    //     0xc48040: stp             fp, lr, [SP, #-0x10]!
    //     0xc48044: mov             fp, SP
    // 0xc48048: AllocStack(0x10)
    //     0xc48048: sub             SP, SP, #0x10
    // 0xc4804c: SetupParameters(ExpandablePanelBodyAlignment this /* r1 => r0, fp-0x8 */)
    //     0xc4804c: mov             x0, x1
    //     0xc48050: stur            x1, [fp, #-8]
    // 0xc48054: CheckStackOverflow
    //     0xc48054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc48058: cmp             SP, x16
    //     0xc4805c: b.ls            #0xc4809c
    // 0xc48060: r1 = Null
    //     0xc48060: mov             x1, NULL
    // 0xc48064: r2 = 4
    //     0xc48064: movz            x2, #0x4
    // 0xc48068: r0 = AllocateArray()
    //     0xc48068: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4806c: r16 = "ExpandablePanelBodyAlignment."
    //     0xc4806c: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4d000] "ExpandablePanelBodyAlignment."
    //     0xc48070: ldr             x16, [x16]
    // 0xc48074: StoreField: r0->field_f = r16
    //     0xc48074: stur            w16, [x0, #0xf]
    // 0xc48078: ldur            x1, [fp, #-8]
    // 0xc4807c: LoadField: r2 = r1->field_f
    //     0xc4807c: ldur            w2, [x1, #0xf]
    // 0xc48080: DecompressPointer r2
    //     0xc48080: add             x2, x2, HEAP, lsl #32
    // 0xc48084: StoreField: r0->field_13 = r2
    //     0xc48084: stur            w2, [x0, #0x13]
    // 0xc48088: str             x0, [SP]
    // 0xc4808c: r0 = _interpolate()
    //     0xc4808c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc48090: LeaveFrame
    //     0xc48090: mov             SP, fp
    //     0xc48094: ldp             fp, lr, [SP], #0x10
    // 0xc48098: ret
    //     0xc48098: ret             
    // 0xc4809c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4809c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc480a0: b               #0xc48060
  }
}

// class id: 7109, size: 0x14, field offset: 0x14
enum ExpandablePanelHeaderAlignment extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc47fdc, size: 0x64
    // 0xc47fdc: EnterFrame
    //     0xc47fdc: stp             fp, lr, [SP, #-0x10]!
    //     0xc47fe0: mov             fp, SP
    // 0xc47fe4: AllocStack(0x10)
    //     0xc47fe4: sub             SP, SP, #0x10
    // 0xc47fe8: SetupParameters(ExpandablePanelHeaderAlignment this /* r1 => r0, fp-0x8 */)
    //     0xc47fe8: mov             x0, x1
    //     0xc47fec: stur            x1, [fp, #-8]
    // 0xc47ff0: CheckStackOverflow
    //     0xc47ff0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc47ff4: cmp             SP, x16
    //     0xc47ff8: b.ls            #0xc48038
    // 0xc47ffc: r1 = Null
    //     0xc47ffc: mov             x1, NULL
    // 0xc48000: r2 = 4
    //     0xc48000: movz            x2, #0x4
    // 0xc48004: r0 = AllocateArray()
    //     0xc48004: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc48008: r16 = "ExpandablePanelHeaderAlignment."
    //     0xc48008: add             x16, PP, #0x43, lsl #12  ; [pp+0x43548] "ExpandablePanelHeaderAlignment."
    //     0xc4800c: ldr             x16, [x16, #0x548]
    // 0xc48010: StoreField: r0->field_f = r16
    //     0xc48010: stur            w16, [x0, #0xf]
    // 0xc48014: ldur            x1, [fp, #-8]
    // 0xc48018: LoadField: r2 = r1->field_f
    //     0xc48018: ldur            w2, [x1, #0xf]
    // 0xc4801c: DecompressPointer r2
    //     0xc4801c: add             x2, x2, HEAP, lsl #32
    // 0xc48020: StoreField: r0->field_13 = r2
    //     0xc48020: stur            w2, [x0, #0x13]
    // 0xc48024: str             x0, [SP]
    // 0xc48028: r0 = _interpolate()
    //     0xc48028: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4802c: LeaveFrame
    //     0xc4802c: mov             SP, fp
    //     0xc48030: ldp             fp, lr, [SP], #0x10
    // 0xc48034: ret
    //     0xc48034: ret             
    // 0xc48038: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc48038: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4803c: b               #0xc47ffc
  }
}

// class id: 7110, size: 0x14, field offset: 0x14
enum ExpandablePanelIconPlacement extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc47f78, size: 0x64
    // 0xc47f78: EnterFrame
    //     0xc47f78: stp             fp, lr, [SP, #-0x10]!
    //     0xc47f7c: mov             fp, SP
    // 0xc47f80: AllocStack(0x10)
    //     0xc47f80: sub             SP, SP, #0x10
    // 0xc47f84: SetupParameters(ExpandablePanelIconPlacement this /* r1 => r0, fp-0x8 */)
    //     0xc47f84: mov             x0, x1
    //     0xc47f88: stur            x1, [fp, #-8]
    // 0xc47f8c: CheckStackOverflow
    //     0xc47f8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc47f90: cmp             SP, x16
    //     0xc47f94: b.ls            #0xc47fd4
    // 0xc47f98: r1 = Null
    //     0xc47f98: mov             x1, NULL
    // 0xc47f9c: r2 = 4
    //     0xc47f9c: movz            x2, #0x4
    // 0xc47fa0: r0 = AllocateArray()
    //     0xc47fa0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc47fa4: r16 = "ExpandablePanelIconPlacement."
    //     0xc47fa4: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4cff0] "ExpandablePanelIconPlacement."
    //     0xc47fa8: ldr             x16, [x16, #0xff0]
    // 0xc47fac: StoreField: r0->field_f = r16
    //     0xc47fac: stur            w16, [x0, #0xf]
    // 0xc47fb0: ldur            x1, [fp, #-8]
    // 0xc47fb4: LoadField: r2 = r1->field_f
    //     0xc47fb4: ldur            w2, [x1, #0xf]
    // 0xc47fb8: DecompressPointer r2
    //     0xc47fb8: add             x2, x2, HEAP, lsl #32
    // 0xc47fbc: StoreField: r0->field_13 = r2
    //     0xc47fbc: stur            w2, [x0, #0x13]
    // 0xc47fc0: str             x0, [SP]
    // 0xc47fc4: r0 = _interpolate()
    //     0xc47fc4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc47fc8: LeaveFrame
    //     0xc47fc8: mov             SP, fp
    //     0xc47fcc: ldp             fp, lr, [SP], #0x10
    // 0xc47fd0: ret
    //     0xc47fd0: ret             
    // 0xc47fd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc47fd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc47fd8: b               #0xc47f98
  }
}
