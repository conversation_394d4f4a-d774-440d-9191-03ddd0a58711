// lib: , url: package:firebase_analytics/observer.dart

// class id: 1048734, size: 0x8
class :: {

  [closure] static bool defaultRouteFilter(dynamic, Route<dynamic>?) {
    // ** addr: 0x90fcb8, size: 0x24
    // 0x90fcb8: ldr             x1, [SP]
    // 0x90fcbc: r2 = LoadClassIdInstr(r1)
    //     0x90fcbc: ldur            x2, [x1, #-1]
    //     0x90fcc0: ubfx            x2, x2, #0xc, #0x14
    // 0x90fcc4: sub             x16, x2, #0xa62
    // 0x90fcc8: cmp             x16, #3
    // 0x90fccc: r16 = true
    //     0x90fccc: add             x16, NULL, #0x20  ; true
    // 0x90fcd0: r17 = false
    //     0x90fcd0: add             x17, NULL, #0x30  ; false
    // 0x90fcd4: csel            x0, x16, x17, ls
    // 0x90fcd8: ret
    //     0x90fcd8: ret             
  }
}

// class id: 2679, size: 0x20, field offset: 0x10
class FirebaseAnalyticsObserver extends RouteObserver<dynamic> {

  _ FirebaseAnalyticsObserver(/* No info */) {
    // ** addr: 0x90fbe4, size: 0xd4
    // 0x90fbe4: EnterFrame
    //     0x90fbe4: stp             fp, lr, [SP, #-0x10]!
    //     0x90fbe8: mov             fp, SP
    // 0x90fbec: AllocStack(0x18)
    //     0x90fbec: sub             SP, SP, #0x18
    // 0x90fbf0: r4 = Closure: (Route<dynamic>?) => bool from Function 'defaultRouteFilter': static.
    //     0x90fbf0: add             x4, PP, #0x1b, lsl #12  ; [pp+0x1b588] Closure: (Route<dynamic>?) => bool from Function 'defaultRouteFilter': static. (0x7e54fb30fcb8)
    //     0x90fbf4: ldr             x4, [x4, #0x588]
    // 0x90fbf8: mov             x5, x1
    // 0x90fbfc: mov             x0, x2
    // 0x90fc00: stur            x1, [fp, #-8]
    // 0x90fc04: mov             x1, x3
    // 0x90fc08: CheckStackOverflow
    //     0x90fc08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90fc0c: cmp             SP, x16
    //     0x90fc10: b.ls            #0x90fcb0
    // 0x90fc14: StoreField: r5->field_f = r0
    //     0x90fc14: stur            w0, [x5, #0xf]
    //     0x90fc18: ldurb           w16, [x5, #-1]
    //     0x90fc1c: ldurb           w17, [x0, #-1]
    //     0x90fc20: and             x16, x17, x16, lsr #2
    //     0x90fc24: tst             x16, HEAP, lsr #32
    //     0x90fc28: b.eq            #0x90fc30
    //     0x90fc2c: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x90fc30: mov             x0, x1
    // 0x90fc34: StoreField: r5->field_13 = r0
    //     0x90fc34: stur            w0, [x5, #0x13]
    //     0x90fc38: ldurb           w16, [x5, #-1]
    //     0x90fc3c: ldurb           w17, [x0, #-1]
    //     0x90fc40: and             x16, x17, x16, lsr #2
    //     0x90fc44: tst             x16, HEAP, lsr #32
    //     0x90fc48: b.eq            #0x90fc50
    //     0x90fc4c: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x90fc50: ArrayStore: r5[0] = r4  ; List_4
    //     0x90fc50: stur            w4, [x5, #0x17]
    // 0x90fc54: LoadField: r2 = r5->field_7
    //     0x90fc54: ldur            w2, [x5, #7]
    // 0x90fc58: DecompressPointer r2
    //     0x90fc58: add             x2, x2, HEAP, lsl #32
    // 0x90fc5c: r1 = Null
    //     0x90fc5c: mov             x1, NULL
    // 0x90fc60: r3 = <X0 bound Route, Set<RouteAware>>
    //     0x90fc60: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b590] TypeArguments: <X0 bound Route, Set<RouteAware>>
    //     0x90fc64: ldr             x3, [x3, #0x590]
    // 0x90fc68: r30 = InstantiateTypeArgumentsStub
    //     0x90fc68: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x90fc6c: LoadField: r30 = r30->field_7
    //     0x90fc6c: ldur            lr, [lr, #7]
    // 0x90fc70: blr             lr
    // 0x90fc74: ldr             x16, [THR, #0x90]  ; THR::empty_array
    // 0x90fc78: stp             x16, x0, [SP]
    // 0x90fc7c: r0 = Map._fromLiteral()
    //     0x90fc7c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90fc80: ldur            x1, [fp, #-8]
    // 0x90fc84: StoreField: r1->field_b = r0
    //     0x90fc84: stur            w0, [x1, #0xb]
    //     0x90fc88: ldurb           w16, [x1, #-1]
    //     0x90fc8c: ldurb           w17, [x0, #-1]
    //     0x90fc90: and             x16, x17, x16, lsr #2
    //     0x90fc94: tst             x16, HEAP, lsr #32
    //     0x90fc98: b.eq            #0x90fca0
    //     0x90fc9c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x90fca0: r0 = Null
    //     0x90fca0: mov             x0, NULL
    // 0x90fca4: LeaveFrame
    //     0x90fca4: mov             SP, fp
    //     0x90fca8: ldp             fp, lr, [SP], #0x10
    // 0x90fcac: ret
    //     0x90fcac: ret             
    // 0x90fcb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90fcb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90fcb4: b               #0x90fc14
  }
  _ didReplace(/* No info */) {
    // ** addr: 0xdadfa8, size: 0x44
    // 0xdadfa8: EnterFrame
    //     0xdadfa8: stp             fp, lr, [SP, #-0x10]!
    //     0xdadfac: mov             fp, SP
    // 0xdadfb0: CheckStackOverflow
    //     0xdadfb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdadfb4: cmp             SP, x16
    //     0xdadfb8: b.ls            #0xdadfe4
    // 0xdadfbc: r0 = LoadClassIdInstr(r2)
    //     0xdadfbc: ldur            x0, [x2, #-1]
    //     0xdadfc0: ubfx            x0, x0, #0xc, #0x14
    // 0xdadfc4: sub             x16, x0, #0xa62
    // 0xdadfc8: cmp             x16, #3
    // 0xdadfcc: b.hi            #0xdadfd4
    // 0xdadfd0: r0 = _sendScreenView()
    //     0xdadfd0: bl              #0xdadfec  ; [package:firebase_analytics/observer.dart] FirebaseAnalyticsObserver::_sendScreenView
    // 0xdadfd4: r0 = Null
    //     0xdadfd4: mov             x0, NULL
    // 0xdadfd8: LeaveFrame
    //     0xdadfd8: mov             SP, fp
    //     0xdadfdc: ldp             fp, lr, [SP], #0x10
    // 0xdadfe0: ret
    //     0xdadfe0: ret             
    // 0xdadfe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdadfe4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdadfe8: b               #0xdadfbc
  }
  _ _sendScreenView(/* No info */) {
    // ** addr: 0xdadfec, size: 0xd4
    // 0xdadfec: EnterFrame
    //     0xdadfec: stp             fp, lr, [SP, #-0x10]!
    //     0xdadff0: mov             fp, SP
    // 0xdadff4: AllocStack(0x20)
    //     0xdadff4: sub             SP, SP, #0x20
    // 0xdadff8: SetupParameters(FirebaseAnalyticsObserver this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xdadff8: stur            x1, [fp, #-8]
    //     0xdadffc: stur            x2, [fp, #-0x10]
    // 0xdae000: CheckStackOverflow
    //     0xdae000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdae004: cmp             SP, x16
    //     0xdae008: b.ls            #0xdae0b8
    // 0xdae00c: r1 = 1
    //     0xdae00c: movz            x1, #0x1
    // 0xdae010: r0 = AllocateContext()
    //     0xdae010: bl              #0xec126c  ; AllocateContextStub
    // 0xdae014: mov             x3, x0
    // 0xdae018: ldur            x0, [fp, #-8]
    // 0xdae01c: stur            x3, [fp, #-0x18]
    // 0xdae020: StoreField: r3->field_f = r0
    //     0xdae020: stur            w0, [x3, #0xf]
    // 0xdae024: ldur            x1, [fp, #-0x10]
    // 0xdae028: LoadField: r2 = r1->field_13
    //     0xdae028: ldur            w2, [x1, #0x13]
    // 0xdae02c: DecompressPointer r2
    //     0xdae02c: add             x2, x2, HEAP, lsl #32
    // 0xdae030: LoadField: r1 = r0->field_13
    //     0xdae030: ldur            w1, [x0, #0x13]
    // 0xdae034: DecompressPointer r1
    //     0xdae034: add             x1, x1, HEAP, lsl #32
    // 0xdae038: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xdae038: ldur            w4, [x1, #0x17]
    // 0xdae03c: DecompressPointer r4
    //     0xdae03c: add             x4, x4, HEAP, lsl #32
    // 0xdae040: mov             x1, x4
    // 0xdae044: r0 = nameExtractor()
    //     0xdae044: bl              #0x910318  ; [package:nuonline/services/analytic_service.dart] AnalyticService::nameExtractor
    // 0xdae048: cmp             w0, NULL
    // 0xdae04c: b.eq            #0xdae0a8
    // 0xdae050: ldur            x1, [fp, #-8]
    // 0xdae054: LoadField: r2 = r1->field_f
    //     0xdae054: ldur            w2, [x1, #0xf]
    // 0xdae058: DecompressPointer r2
    //     0xdae058: add             x2, x2, HEAP, lsl #32
    // 0xdae05c: mov             x1, x2
    // 0xdae060: mov             x2, x0
    // 0xdae064: r0 = logScreenView()
    //     0xdae064: bl              #0x8abce4  ; [package:firebase_analytics/firebase_analytics.dart] FirebaseAnalytics::logScreenView
    // 0xdae068: ldur            x2, [fp, #-0x18]
    // 0xdae06c: r1 = Function '<anonymous closure>':.
    //     0xdae06c: add             x1, PP, #0x22, lsl #12  ; [pp+0x22390] AnonymousClosure: (0xdae0e8), in [package:firebase_analytics/observer.dart] FirebaseAnalyticsObserver::_sendScreenView (0xdadfec)
    //     0xdae070: ldr             x1, [x1, #0x390]
    // 0xdae074: stur            x0, [fp, #-8]
    // 0xdae078: r0 = AllocateClosure()
    //     0xdae078: bl              #0xec1630  ; AllocateClosureStub
    // 0xdae07c: r1 = Function '<anonymous closure>':.
    //     0xdae07c: add             x1, PP, #0x22, lsl #12  ; [pp+0x22398] AnonymousClosure: (0xdae0c0), in [package:firebase_analytics/observer.dart] FirebaseAnalyticsObserver::_sendScreenView (0xdadfec)
    //     0xdae080: ldr             x1, [x1, #0x398]
    // 0xdae084: r2 = Null
    //     0xdae084: mov             x2, NULL
    // 0xdae088: stur            x0, [fp, #-0x10]
    // 0xdae08c: r0 = AllocateClosure()
    //     0xdae08c: bl              #0xec1630  ; AllocateClosureStub
    // 0xdae090: str             x0, [SP]
    // 0xdae094: ldur            x1, [fp, #-8]
    // 0xdae098: ldur            x2, [fp, #-0x10]
    // 0xdae09c: r4 = const [0, 0x3, 0x1, 0x2, test, 0x2, null]
    //     0xdae09c: add             x4, PP, #0x11, lsl #12  ; [pp+0x110c8] List(7) [0, 0x3, 0x1, 0x2, "test", 0x2, Null]
    //     0xdae0a0: ldr             x4, [x4, #0xc8]
    // 0xdae0a4: r0 = catchError()
    //     0xdae0a4: bl              #0xd67a64  ; [dart:async] _Future::catchError
    // 0xdae0a8: r0 = Null
    //     0xdae0a8: mov             x0, NULL
    // 0xdae0ac: LeaveFrame
    //     0xdae0ac: mov             SP, fp
    //     0xdae0b0: ldp             fp, lr, [SP], #0x10
    // 0xdae0b4: ret
    //     0xdae0b4: ret             
    // 0xdae0b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdae0b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdae0bc: b               #0xdae00c
  }
  [closure] bool <anonymous closure>(dynamic, Object) {
    // ** addr: 0xdae0c0, size: 0x28
    // 0xdae0c0: ldr             x1, [SP]
    // 0xdae0c4: r2 = 60
    //     0xdae0c4: movz            x2, #0x3c
    // 0xdae0c8: branchIfSmi(r1, 0xdae0d4)
    //     0xdae0c8: tbz             w1, #0, #0xdae0d4
    // 0xdae0cc: r2 = LoadClassIdInstr(r1)
    //     0xdae0cc: ldur            x2, [x1, #-1]
    //     0xdae0d0: ubfx            x2, x2, #0xc, #0x14
    // 0xdae0d4: cmp             x2, #0xaf3
    // 0xdae0d8: r16 = true
    //     0xdae0d8: add             x16, NULL, #0x20  ; true
    // 0xdae0dc: r17 = false
    //     0xdae0dc: add             x17, NULL, #0x30  ; false
    // 0xdae0e0: csel            x0, x16, x17, eq
    // 0xdae0e4: ret
    //     0xdae0e4: ret             
  }
  [closure] Null <anonymous closure>(dynamic, Object) {
    // ** addr: 0xdae0e8, size: 0x8c
    // 0xdae0e8: EnterFrame
    //     0xdae0e8: stp             fp, lr, [SP, #-0x10]!
    //     0xdae0ec: mov             fp, SP
    // 0xdae0f0: AllocStack(0x8)
    //     0xdae0f0: sub             SP, SP, #8
    // 0xdae0f4: CheckStackOverflow
    //     0xdae0f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdae0f8: cmp             SP, x16
    //     0xdae0fc: b.ls            #0xdae16c
    // 0xdae100: r0 = InitLateStaticField(0x674) // [package:flutter/src/foundation/print.dart] ::debugPrint
    //     0xdae100: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xdae104: ldr             x0, [x0, #0xce8]
    //     0xdae108: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xdae10c: cmp             w0, w16
    //     0xdae110: b.ne            #0xdae11c
    //     0xdae114: ldr             x2, [PP, #0x490]  ; [pp+0x490] Field <::.debugPrint>: static late (offset: 0x674)
    //     0xdae118: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xdae11c: r1 = Null
    //     0xdae11c: mov             x1, NULL
    // 0xdae120: r2 = 6
    //     0xdae120: movz            x2, #0x6
    // 0xdae124: r0 = AllocateArray()
    //     0xdae124: bl              #0xec22fc  ; AllocateArrayStub
    // 0xdae128: r16 = FirebaseAnalyticsObserver
    //     0xdae128: add             x16, PP, #0x22, lsl #12  ; [pp+0x223a0] Type: FirebaseAnalyticsObserver
    //     0xdae12c: ldr             x16, [x16, #0x3a0]
    // 0xdae130: StoreField: r0->field_f = r16
    //     0xdae130: stur            w16, [x0, #0xf]
    // 0xdae134: r16 = ": "
    //     0xdae134: ldr             x16, [PP, #0x7d8]  ; [pp+0x7d8] ": "
    // 0xdae138: StoreField: r0->field_13 = r16
    //     0xdae138: stur            w16, [x0, #0x13]
    // 0xdae13c: ldr             x1, [fp, #0x10]
    // 0xdae140: ArrayStore: r0[0] = r1  ; List_4
    //     0xdae140: stur            w1, [x0, #0x17]
    // 0xdae144: str             x0, [SP]
    // 0xdae148: r0 = _interpolate()
    //     0xdae148: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xdae14c: str             NULL, [SP]
    // 0xdae150: mov             x1, x0
    // 0xdae154: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0xdae154: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0xdae158: r0 = debugPrintThrottled()
    //     0xdae158: bl              #0x63fa18  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0xdae15c: r0 = Null
    //     0xdae15c: mov             x0, NULL
    // 0xdae160: LeaveFrame
    //     0xdae160: mov             SP, fp
    //     0xdae164: ldp             fp, lr, [SP], #0x10
    // 0xdae168: ret
    //     0xdae168: ret             
    // 0xdae16c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdae16c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdae170: b               #0xdae100
  }
  _ didPop(/* No info */) {
    // ** addr: 0xdb125c, size: 0x98
    // 0xdb125c: EnterFrame
    //     0xdb125c: stp             fp, lr, [SP, #-0x10]!
    //     0xdb1260: mov             fp, SP
    // 0xdb1264: AllocStack(0x18)
    //     0xdb1264: sub             SP, SP, #0x18
    // 0xdb1268: SetupParameters(FirebaseAnalyticsObserver this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xdb1268: mov             x5, x1
    //     0xdb126c: mov             x4, x2
    //     0xdb1270: mov             x0, x3
    //     0xdb1274: stur            x1, [fp, #-8]
    //     0xdb1278: stur            x2, [fp, #-0x10]
    //     0xdb127c: stur            x3, [fp, #-0x18]
    // 0xdb1280: CheckStackOverflow
    //     0xdb1280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb1284: cmp             SP, x16
    //     0xdb1288: b.ls            #0xdb12ec
    // 0xdb128c: mov             x1, x5
    // 0xdb1290: mov             x2, x4
    // 0xdb1294: mov             x3, x0
    // 0xdb1298: r0 = didPop()
    //     0xdb1298: bl              #0xdb12f4  ; [package:flutter/src/widgets/routes.dart] RouteObserver::didPop
    // 0xdb129c: ldur            x2, [fp, #-0x18]
    // 0xdb12a0: cmp             w2, NULL
    // 0xdb12a4: b.eq            #0xdb12dc
    // 0xdb12a8: r0 = LoadClassIdInstr(r2)
    //     0xdb12a8: ldur            x0, [x2, #-1]
    //     0xdb12ac: ubfx            x0, x0, #0xc, #0x14
    // 0xdb12b0: sub             x16, x0, #0xa62
    // 0xdb12b4: cmp             x16, #3
    // 0xdb12b8: b.hi            #0xdb12dc
    // 0xdb12bc: ldur            x0, [fp, #-0x10]
    // 0xdb12c0: r1 = LoadClassIdInstr(r0)
    //     0xdb12c0: ldur            x1, [x0, #-1]
    //     0xdb12c4: ubfx            x1, x1, #0xc, #0x14
    // 0xdb12c8: sub             x16, x1, #0xa62
    // 0xdb12cc: cmp             x16, #3
    // 0xdb12d0: b.hi            #0xdb12dc
    // 0xdb12d4: ldur            x1, [fp, #-8]
    // 0xdb12d8: r0 = _sendScreenView()
    //     0xdb12d8: bl              #0xdadfec  ; [package:firebase_analytics/observer.dart] FirebaseAnalyticsObserver::_sendScreenView
    // 0xdb12dc: r0 = Null
    //     0xdb12dc: mov             x0, NULL
    // 0xdb12e0: LeaveFrame
    //     0xdb12e0: mov             SP, fp
    //     0xdb12e4: ldp             fp, lr, [SP], #0x10
    // 0xdb12e8: ret
    //     0xdb12e8: ret             
    // 0xdb12ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb12ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb12f0: b               #0xdb128c
  }
  _ didPush(/* No info */) {
    // ** addr: 0xdb1b6c, size: 0x6c
    // 0xdb1b6c: EnterFrame
    //     0xdb1b6c: stp             fp, lr, [SP, #-0x10]!
    //     0xdb1b70: mov             fp, SP
    // 0xdb1b74: AllocStack(0x10)
    //     0xdb1b74: sub             SP, SP, #0x10
    // 0xdb1b78: SetupParameters(FirebaseAnalyticsObserver this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xdb1b78: mov             x4, x1
    //     0xdb1b7c: mov             x0, x2
    //     0xdb1b80: stur            x1, [fp, #-8]
    //     0xdb1b84: stur            x2, [fp, #-0x10]
    // 0xdb1b88: CheckStackOverflow
    //     0xdb1b88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb1b8c: cmp             SP, x16
    //     0xdb1b90: b.ls            #0xdb1bd0
    // 0xdb1b94: mov             x1, x4
    // 0xdb1b98: mov             x2, x0
    // 0xdb1b9c: r0 = didPush()
    //     0xdb1b9c: bl              #0xdb1bd8  ; [package:flutter/src/widgets/routes.dart] RouteObserver::didPush
    // 0xdb1ba0: ldur            x2, [fp, #-0x10]
    // 0xdb1ba4: r0 = LoadClassIdInstr(r2)
    //     0xdb1ba4: ldur            x0, [x2, #-1]
    //     0xdb1ba8: ubfx            x0, x0, #0xc, #0x14
    // 0xdb1bac: sub             x16, x0, #0xa62
    // 0xdb1bb0: cmp             x16, #3
    // 0xdb1bb4: b.hi            #0xdb1bc0
    // 0xdb1bb8: ldur            x1, [fp, #-8]
    // 0xdb1bbc: r0 = _sendScreenView()
    //     0xdb1bbc: bl              #0xdadfec  ; [package:firebase_analytics/observer.dart] FirebaseAnalyticsObserver::_sendScreenView
    // 0xdb1bc0: r0 = Null
    //     0xdb1bc0: mov             x0, NULL
    // 0xdb1bc4: LeaveFrame
    //     0xdb1bc4: mov             SP, fp
    //     0xdb1bc8: ldp             fp, lr, [SP], #0x10
    // 0xdb1bcc: ret
    //     0xdb1bcc: ret             
    // 0xdb1bd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb1bd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb1bd4: b               #0xdb1b94
  }
}
