// lib: , url: package:firebase_messaging/firebase_messaging.dart

// class id: 1048756, size: 0x8
class :: {

  static void _assertTopicName(String) {
    // ** addr: 0xecc850, size: 0x70
    // 0xecc850: EnterFrame
    //     0xecc850: stp             fp, lr, [SP, #-0x10]!
    //     0xecc854: mov             fp, SP
    // 0xecc858: AllocStack(0x38)
    //     0xecc858: sub             SP, SP, #0x38
    // 0xecc85c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xecc85c: stur            x1, [fp, #-8]
    // 0xecc860: CheckStackOverflow
    //     0xecc860: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecc864: cmp             SP, x16
    //     0xecc868: b.ls            #0xecc8b8
    // 0xecc86c: r16 = "^[a-zA-Z0-9-_.~%]{1,900}$"
    //     0xecc86c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12570] "^[a-zA-Z0-9-_.~%]{1,900}$"
    //     0xecc870: ldr             x16, [x16, #0x570]
    // 0xecc874: stp             x16, NULL, [SP, #0x20]
    // 0xecc878: r16 = false
    //     0xecc878: add             x16, NULL, #0x30  ; false
    // 0xecc87c: r30 = true
    //     0xecc87c: add             lr, NULL, #0x20  ; true
    // 0xecc880: stp             lr, x16, [SP, #0x10]
    // 0xecc884: r16 = false
    //     0xecc884: add             x16, NULL, #0x30  ; false
    // 0xecc888: r30 = false
    //     0xecc888: add             lr, NULL, #0x30  ; false
    // 0xecc88c: stp             lr, x16, [SP]
    // 0xecc890: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xecc890: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xecc894: r0 = _RegExp()
    //     0xecc894: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xecc898: ldur            x16, [fp, #-8]
    // 0xecc89c: stp             x16, x0, [SP, #8]
    // 0xecc8a0: str             xzr, [SP]
    // 0xecc8a4: r0 = _ExecuteMatch()
    //     0xecc8a4: bl              #0x644494  ; [dart:core] _RegExp::_ExecuteMatch
    // 0xecc8a8: r0 = Null
    //     0xecc8a8: mov             x0, NULL
    // 0xecc8ac: LeaveFrame
    //     0xecc8ac: mov             SP, fp
    //     0xecc8b0: ldp             fp, lr, [SP], #0x10
    // 0xecc8b4: ret
    //     0xecc8b4: ret             
    // 0xecc8b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecc8b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecc8bc: b               #0xecc86c
  }
}

// class id: 5948, size: 0x18, field offset: 0x10
class FirebaseMessaging extends FirebasePluginPlatform {

  static late Map<String, FirebaseMessaging> _firebaseMessagingInstances; // offset: 0xfe4

  _ unsubscribeFromTopic(/* No info */) {
    // ** addr: 0xecb62c, size: 0x58
    // 0xecb62c: EnterFrame
    //     0xecb62c: stp             fp, lr, [SP, #-0x10]!
    //     0xecb630: mov             fp, SP
    // 0xecb634: AllocStack(0x10)
    //     0xecb634: sub             SP, SP, #0x10
    // 0xecb638: SetupParameters(FirebaseMessaging this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xecb638: mov             x0, x2
    //     0xecb63c: stur            x2, [fp, #-0x10]
    //     0xecb640: mov             x2, x1
    //     0xecb644: stur            x1, [fp, #-8]
    // 0xecb648: CheckStackOverflow
    //     0xecb648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecb64c: cmp             SP, x16
    //     0xecb650: b.ls            #0xecb67c
    // 0xecb654: mov             x1, x0
    // 0xecb658: r0 = _assertTopicName()
    //     0xecb658: bl              #0xecc850  ; [package:firebase_messaging/firebase_messaging.dart] ::_assertTopicName
    // 0xecb65c: ldur            x1, [fp, #-8]
    // 0xecb660: r0 = _delegate()
    //     0xecb660: bl              #0xecb848  ; [package:firebase_messaging/firebase_messaging.dart] FirebaseMessaging::_delegate
    // 0xecb664: mov             x1, x0
    // 0xecb668: ldur            x2, [fp, #-0x10]
    // 0xecb66c: r0 = unsubscribeFromTopic()
    //     0xecb66c: bl              #0xecb684  ; [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] MethodChannelFirebaseMessaging::unsubscribeFromTopic
    // 0xecb670: LeaveFrame
    //     0xecb670: mov             SP, fp
    //     0xecb674: ldp             fp, lr, [SP], #0x10
    // 0xecb678: ret
    //     0xecb678: ret             
    // 0xecb67c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecb67c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecb680: b               #0xecb654
  }
  get _ _delegate(/* No info */) {
    // ** addr: 0xecb848, size: 0x98
    // 0xecb848: EnterFrame
    //     0xecb848: stp             fp, lr, [SP, #-0x10]!
    //     0xecb84c: mov             fp, SP
    // 0xecb850: AllocStack(0x10)
    //     0xecb850: sub             SP, SP, #0x10
    // 0xecb854: SetupParameters(FirebaseMessaging this /* r1 => r0, fp-0x10 */)
    //     0xecb854: mov             x0, x1
    //     0xecb858: stur            x1, [fp, #-0x10]
    // 0xecb85c: CheckStackOverflow
    //     0xecb85c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecb860: cmp             SP, x16
    //     0xecb864: b.ls            #0xecb8d8
    // 0xecb868: LoadField: r1 = r0->field_f
    //     0xecb868: ldur            w1, [x0, #0xf]
    // 0xecb86c: DecompressPointer r1
    //     0xecb86c: add             x1, x1, HEAP, lsl #32
    // 0xecb870: cmp             w1, NULL
    // 0xecb874: b.ne            #0xecb8c8
    // 0xecb878: LoadField: r2 = r0->field_13
    //     0xecb878: ldur            w2, [x0, #0x13]
    // 0xecb87c: DecompressPointer r2
    //     0xecb87c: add             x2, x2, HEAP, lsl #32
    // 0xecb880: mov             x1, x0
    // 0xecb884: stur            x2, [fp, #-8]
    // 0xecb888: r0 = pluginConstants()
    //     0xecb888: bl              #0x7ef5c0  ; [package:firebase_core_platform_interface/firebase_core_platform_interface.dart] FirebasePluginPlatform::pluginConstants
    // 0xecb88c: ldur            x2, [fp, #-8]
    // 0xecb890: mov             x3, x0
    // 0xecb894: r1 = Null
    //     0xecb894: mov             x1, NULL
    // 0xecb898: r0 = FirebaseMessagingPlatform.instanceFor()
    //     0xecb898: bl              #0xecb8e0  ; [package:firebase_messaging_platform_interface/src/platform_interface/platform_interface_messaging.dart] FirebaseMessagingPlatform::FirebaseMessagingPlatform.instanceFor
    // 0xecb89c: mov             x1, x0
    // 0xecb8a0: ldur            x2, [fp, #-0x10]
    // 0xecb8a4: StoreField: r2->field_f = r0
    //     0xecb8a4: stur            w0, [x2, #0xf]
    //     0xecb8a8: ldurb           w16, [x2, #-1]
    //     0xecb8ac: ldurb           w17, [x0, #-1]
    //     0xecb8b0: and             x16, x17, x16, lsr #2
    //     0xecb8b4: tst             x16, HEAP, lsr #32
    //     0xecb8b8: b.eq            #0xecb8c0
    //     0xecb8bc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xecb8c0: mov             x0, x1
    // 0xecb8c4: b               #0xecb8cc
    // 0xecb8c8: mov             x0, x1
    // 0xecb8cc: LeaveFrame
    //     0xecb8cc: mov             SP, fp
    //     0xecb8d0: ldp             fp, lr, [SP], #0x10
    // 0xecb8d4: ret
    //     0xecb8d4: ret             
    // 0xecb8d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecb8d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecb8dc: b               #0xecb868
  }
  get _ instance(/* No info */) {
    // ** addr: 0xecc8c0, size: 0x38
    // 0xecc8c0: EnterFrame
    //     0xecc8c0: stp             fp, lr, [SP, #-0x10]!
    //     0xecc8c4: mov             fp, SP
    // 0xecc8c8: CheckStackOverflow
    //     0xecc8c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecc8cc: cmp             SP, x16
    //     0xecc8d0: b.ls            #0xecc8f0
    // 0xecc8d4: r0 = app()
    //     0xecc8d4: bl              #0x90ffc4  ; [package:firebase_core/firebase_core.dart] Firebase::app
    // 0xecc8d8: mov             x2, x0
    // 0xecc8dc: r1 = Null
    //     0xecc8dc: mov             x1, NULL
    // 0xecc8e0: r0 = FirebaseMessaging._instanceFor()
    //     0xecc8e0: bl              #0xeccee4  ; [package:firebase_messaging/firebase_messaging.dart] FirebaseMessaging::FirebaseMessaging._instanceFor
    // 0xecc8e4: LeaveFrame
    //     0xecc8e4: mov             SP, fp
    //     0xecc8e8: ldp             fp, lr, [SP], #0x10
    // 0xecc8ec: ret
    //     0xecc8ec: ret             
    // 0xecc8f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecc8f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecc8f4: b               #0xecc8d4
  }
  _ subscribeToTopic(/* No info */) {
    // ** addr: 0xecc8f8, size: 0x58
    // 0xecc8f8: EnterFrame
    //     0xecc8f8: stp             fp, lr, [SP, #-0x10]!
    //     0xecc8fc: mov             fp, SP
    // 0xecc900: AllocStack(0x10)
    //     0xecc900: sub             SP, SP, #0x10
    // 0xecc904: SetupParameters(FirebaseMessaging this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xecc904: mov             x0, x2
    //     0xecc908: stur            x2, [fp, #-0x10]
    //     0xecc90c: mov             x2, x1
    //     0xecc910: stur            x1, [fp, #-8]
    // 0xecc914: CheckStackOverflow
    //     0xecc914: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecc918: cmp             SP, x16
    //     0xecc91c: b.ls            #0xecc948
    // 0xecc920: mov             x1, x0
    // 0xecc924: r0 = _assertTopicName()
    //     0xecc924: bl              #0xecc850  ; [package:firebase_messaging/firebase_messaging.dart] ::_assertTopicName
    // 0xecc928: ldur            x1, [fp, #-8]
    // 0xecc92c: r0 = _delegate()
    //     0xecc92c: bl              #0xecb848  ; [package:firebase_messaging/firebase_messaging.dart] FirebaseMessaging::_delegate
    // 0xecc930: mov             x1, x0
    // 0xecc934: ldur            x2, [fp, #-0x10]
    // 0xecc938: r0 = subscribeToTopic()
    //     0xecc938: bl              #0xecc950  ; [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] MethodChannelFirebaseMessaging::subscribeToTopic
    // 0xecc93c: LeaveFrame
    //     0xecc93c: mov             SP, fp
    //     0xecc940: ldp             fp, lr, [SP], #0x10
    // 0xecc944: ret
    //     0xecc944: ret             
    // 0xecc948: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecc948: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecc94c: b               #0xecc920
  }
  get _ onTokenRefresh(/* No info */) {
    // ** addr: 0xeccab0, size: 0x34
    // 0xeccab0: EnterFrame
    //     0xeccab0: stp             fp, lr, [SP, #-0x10]!
    //     0xeccab4: mov             fp, SP
    // 0xeccab8: CheckStackOverflow
    //     0xeccab8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeccabc: cmp             SP, x16
    //     0xeccac0: b.ls            #0xeccadc
    // 0xeccac4: r0 = _delegate()
    //     0xeccac4: bl              #0xecb848  ; [package:firebase_messaging/firebase_messaging.dart] FirebaseMessaging::_delegate
    // 0xeccac8: mov             x1, x0
    // 0xeccacc: r0 = onTokenRefresh()
    //     0xeccacc: bl              #0xeccae4  ; [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] MethodChannelFirebaseMessaging::onTokenRefresh
    // 0xeccad0: LeaveFrame
    //     0xeccad0: mov             SP, fp
    //     0xeccad4: ldp             fp, lr, [SP], #0x10
    // 0xeccad8: ret
    //     0xeccad8: ret             
    // 0xeccadc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeccadc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeccae0: b               #0xeccac4
  }
  _ getToken(/* No info */) {
    // ** addr: 0xeccb48, size: 0x34
    // 0xeccb48: EnterFrame
    //     0xeccb48: stp             fp, lr, [SP, #-0x10]!
    //     0xeccb4c: mov             fp, SP
    // 0xeccb50: CheckStackOverflow
    //     0xeccb50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeccb54: cmp             SP, x16
    //     0xeccb58: b.ls            #0xeccb74
    // 0xeccb5c: r0 = _delegate()
    //     0xeccb5c: bl              #0xecb848  ; [package:firebase_messaging/firebase_messaging.dart] FirebaseMessaging::_delegate
    // 0xeccb60: mov             x1, x0
    // 0xeccb64: r0 = getToken()
    //     0xeccb64: bl              #0xeccb7c  ; [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] MethodChannelFirebaseMessaging::getToken
    // 0xeccb68: LeaveFrame
    //     0xeccb68: mov             SP, fp
    //     0xeccb6c: ldp             fp, lr, [SP], #0x10
    // 0xeccb70: ret
    //     0xeccb70: ret             
    // 0xeccb74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeccb74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeccb78: b               #0xeccb5c
  }
  get _ onMessage(/* No info */) {
    // ** addr: 0xecccd0, size: 0x64
    // 0xecccd0: EnterFrame
    //     0xecccd0: stp             fp, lr, [SP, #-0x10]!
    //     0xecccd4: mov             fp, SP
    // 0xecccd8: AllocStack(0x8)
    //     0xecccd8: sub             SP, SP, #8
    // 0xecccdc: CheckStackOverflow
    //     0xecccdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeccce0: cmp             SP, x16
    //     0xeccce4: b.ls            #0xeccd2c
    // 0xeccce8: r0 = InitLateStaticField(0xff0) // [package:firebase_messaging_platform_interface/src/platform_interface/platform_interface_messaging.dart] FirebaseMessagingPlatform::onMessage
    //     0xeccce8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xecccec: ldr             x0, [x0, #0x1fe0]
    //     0xecccf0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xecccf4: cmp             w0, w16
    //     0xecccf8: b.ne            #0xeccd08
    //     0xecccfc: add             x2, PP, #0x12, lsl #12  ; [pp+0x12528] Field <FirebaseMessagingPlatform.onMessage>: static late final (offset: 0xff0)
    //     0xeccd00: ldr             x2, [x2, #0x528]
    //     0xeccd04: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xeccd08: stur            x0, [fp, #-8]
    // 0xeccd0c: LoadField: r1 = r0->field_7
    //     0xeccd0c: ldur            w1, [x0, #7]
    // 0xeccd10: DecompressPointer r1
    //     0xeccd10: add             x1, x1, HEAP, lsl #32
    // 0xeccd14: r0 = _BroadcastStream()
    //     0xeccd14: bl              #0x836570  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0xeccd18: ldur            x1, [fp, #-8]
    // 0xeccd1c: StoreField: r0->field_b = r1
    //     0xeccd1c: stur            w1, [x0, #0xb]
    // 0xeccd20: LeaveFrame
    //     0xeccd20: mov             SP, fp
    //     0xeccd24: ldp             fp, lr, [SP], #0x10
    // 0xeccd28: ret
    //     0xeccd28: ret             
    // 0xeccd2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeccd2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeccd30: b               #0xeccce8
  }
  static void onBackgroundMessage() {
    // ** addr: 0xeccd34, size: 0x38
    // 0xeccd34: EnterFrame
    //     0xeccd34: stp             fp, lr, [SP, #-0x10]!
    //     0xeccd38: mov             fp, SP
    // 0xeccd3c: CheckStackOverflow
    //     0xeccd3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeccd40: cmp             SP, x16
    //     0xeccd44: b.ls            #0xeccd64
    // 0xeccd48: r1 = Closure: (RemoteMessage) => Future<void> from Function '_firebaseMessagingBackgroundHandler@2539139854': static.
    //     0xeccd48: add             x1, PP, #0x12, lsl #12  ; [pp+0x12590] Closure: (RemoteMessage) => Future<void> from Function '_firebaseMessagingBackgroundHandler@2539139854': static. (0x7e54fb8cbe6c)
    //     0xeccd4c: ldr             x1, [x1, #0x590]
    // 0xeccd50: r0 = onBackgroundMessage=()
    //     0xeccd50: bl              #0xeccd6c  ; [package:firebase_messaging_platform_interface/src/platform_interface/platform_interface_messaging.dart] FirebaseMessagingPlatform::onBackgroundMessage=
    // 0xeccd54: r0 = Null
    //     0xeccd54: mov             x0, NULL
    // 0xeccd58: LeaveFrame
    //     0xeccd58: mov             SP, fp
    //     0xeccd5c: ldp             fp, lr, [SP], #0x10
    // 0xeccd60: ret
    //     0xeccd60: ret             
    // 0xeccd64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeccd64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeccd68: b               #0xeccd48
  }
  factory _ FirebaseMessaging._instanceFor(/* No info */) {
    // ** addr: 0xeccee4, size: 0xa8
    // 0xeccee4: EnterFrame
    //     0xeccee4: stp             fp, lr, [SP, #-0x10]!
    //     0xeccee8: mov             fp, SP
    // 0xecceec: AllocStack(0x18)
    //     0xecceec: sub             SP, SP, #0x18
    // 0xeccef0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xeccef0: stur            x2, [fp, #-8]
    // 0xeccef4: CheckStackOverflow
    //     0xeccef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeccef8: cmp             SP, x16
    //     0xeccefc: b.ls            #0xeccf84
    // 0xeccf00: r1 = 1
    //     0xeccf00: movz            x1, #0x1
    // 0xeccf04: r0 = AllocateContext()
    //     0xeccf04: bl              #0xec126c  ; AllocateContextStub
    // 0xeccf08: mov             x1, x0
    // 0xeccf0c: ldur            x0, [fp, #-8]
    // 0xeccf10: stur            x1, [fp, #-0x10]
    // 0xeccf14: StoreField: r1->field_f = r0
    //     0xeccf14: stur            w0, [x1, #0xf]
    // 0xeccf18: r0 = InitLateStaticField(0xfe4) // [package:firebase_messaging/firebase_messaging.dart] FirebaseMessaging::_firebaseMessagingInstances
    //     0xeccf18: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeccf1c: ldr             x0, [x0, #0x1fc8]
    //     0xeccf20: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeccf24: cmp             w0, w16
    //     0xeccf28: b.ne            #0xeccf38
    //     0xeccf2c: add             x2, PP, #0x12, lsl #12  ; [pp+0x125b0] Field <FirebaseMessaging._firebaseMessagingInstances@1048057947>: static late (offset: 0xfe4)
    //     0xeccf30: ldr             x2, [x2, #0x5b0]
    //     0xeccf34: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xeccf38: ldur            x2, [fp, #-0x10]
    // 0xeccf3c: stur            x0, [fp, #-0x18]
    // 0xeccf40: LoadField: r1 = r2->field_f
    //     0xeccf40: ldur            w1, [x2, #0xf]
    // 0xeccf44: DecompressPointer r1
    //     0xeccf44: add             x1, x1, HEAP, lsl #32
    // 0xeccf48: LoadField: r3 = r1->field_7
    //     0xeccf48: ldur            w3, [x1, #7]
    // 0xeccf4c: DecompressPointer r3
    //     0xeccf4c: add             x3, x3, HEAP, lsl #32
    // 0xeccf50: LoadField: r4 = r3->field_7
    //     0xeccf50: ldur            w4, [x3, #7]
    // 0xeccf54: DecompressPointer r4
    //     0xeccf54: add             x4, x4, HEAP, lsl #32
    // 0xeccf58: stur            x4, [fp, #-8]
    // 0xeccf5c: r1 = Function '<anonymous closure>': static.
    //     0xeccf5c: add             x1, PP, #0x12, lsl #12  ; [pp+0x125b8] AnonymousClosure: static (0xeccf8c), in [package:firebase_messaging/firebase_messaging.dart] FirebaseMessaging::FirebaseMessaging._instanceFor (0xeccee4)
    //     0xeccf60: ldr             x1, [x1, #0x5b8]
    // 0xeccf64: r0 = AllocateClosure()
    //     0xeccf64: bl              #0xec1630  ; AllocateClosureStub
    // 0xeccf68: ldur            x1, [fp, #-0x18]
    // 0xeccf6c: ldur            x2, [fp, #-8]
    // 0xeccf70: mov             x3, x0
    // 0xeccf74: r0 = putIfAbsent()
    //     0xeccf74: bl              #0x7661b4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::putIfAbsent
    // 0xeccf78: LeaveFrame
    //     0xeccf78: mov             SP, fp
    //     0xeccf7c: ldp             fp, lr, [SP], #0x10
    // 0xeccf80: ret
    //     0xeccf80: ret             
    // 0xeccf84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeccf84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeccf88: b               #0xeccf00
  }
  [closure] static FirebaseMessaging <anonymous closure>(dynamic) {
    // ** addr: 0xeccf8c, size: 0x7c
    // 0xeccf8c: EnterFrame
    //     0xeccf8c: stp             fp, lr, [SP, #-0x10]!
    //     0xeccf90: mov             fp, SP
    // 0xeccf94: AllocStack(0x10)
    //     0xeccf94: sub             SP, SP, #0x10
    // 0xeccf98: SetupParameters()
    //     0xeccf98: ldr             x0, [fp, #0x10]
    //     0xeccf9c: ldur            w1, [x0, #0x17]
    //     0xeccfa0: add             x1, x1, HEAP, lsl #32
    // 0xeccfa4: CheckStackOverflow
    //     0xeccfa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeccfa8: cmp             SP, x16
    //     0xeccfac: b.ls            #0xecd000
    // 0xeccfb0: LoadField: r0 = r1->field_f
    //     0xeccfb0: ldur            w0, [x1, #0xf]
    // 0xeccfb4: DecompressPointer r0
    //     0xeccfb4: add             x0, x0, HEAP, lsl #32
    // 0xeccfb8: stur            x0, [fp, #-8]
    // 0xeccfbc: r0 = FirebaseMessaging()
    //     0xeccfbc: bl              #0xecd008  ; AllocateFirebaseMessagingStub -> FirebaseMessaging (size=0x18)
    // 0xeccfc0: mov             x4, x0
    // 0xeccfc4: ldur            x0, [fp, #-8]
    // 0xeccfc8: stur            x4, [fp, #-0x10]
    // 0xeccfcc: StoreField: r4->field_13 = r0
    //     0xeccfcc: stur            w0, [x4, #0x13]
    // 0xeccfd0: LoadField: r1 = r0->field_7
    //     0xeccfd0: ldur            w1, [x0, #7]
    // 0xeccfd4: DecompressPointer r1
    //     0xeccfd4: add             x1, x1, HEAP, lsl #32
    // 0xeccfd8: LoadField: r2 = r1->field_7
    //     0xeccfd8: ldur            w2, [x1, #7]
    // 0xeccfdc: DecompressPointer r2
    //     0xeccfdc: add             x2, x2, HEAP, lsl #32
    // 0xeccfe0: mov             x1, x4
    // 0xeccfe4: r3 = "plugins.flutter.io/firebase_messaging"
    //     0xeccfe4: add             x3, PP, #0x12, lsl #12  ; [pp+0x125c0] "plugins.flutter.io/firebase_messaging"
    //     0xeccfe8: ldr             x3, [x3, #0x5c0]
    // 0xeccfec: r0 = FirebasePluginPlatform()
    //     0xeccfec: bl              #0x90feac  ; [package:firebase_core_platform_interface/firebase_core_platform_interface.dart] FirebasePluginPlatform::FirebasePluginPlatform
    // 0xeccff0: ldur            x0, [fp, #-0x10]
    // 0xeccff4: LeaveFrame
    //     0xeccff4: mov             SP, fp
    //     0xeccff8: ldp             fp, lr, [SP], #0x10
    // 0xeccffc: ret
    //     0xeccffc: ret             
    // 0xecd000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecd000: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecd004: b               #0xeccfb0
  }
  static Map<String, FirebaseMessaging> _firebaseMessagingInstances() {
    // ** addr: 0xecd014, size: 0x40
    // 0xecd014: EnterFrame
    //     0xecd014: stp             fp, lr, [SP, #-0x10]!
    //     0xecd018: mov             fp, SP
    // 0xecd01c: AllocStack(0x10)
    //     0xecd01c: sub             SP, SP, #0x10
    // 0xecd020: CheckStackOverflow
    //     0xecd020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecd024: cmp             SP, x16
    //     0xecd028: b.ls            #0xecd04c
    // 0xecd02c: r16 = <String, FirebaseMessaging>
    //     0xecd02c: add             x16, PP, #0x12, lsl #12  ; [pp+0x125c8] TypeArguments: <String, FirebaseMessaging>
    //     0xecd030: ldr             x16, [x16, #0x5c8]
    // 0xecd034: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xecd038: stp             lr, x16, [SP]
    // 0xecd03c: r0 = Map._fromLiteral()
    //     0xecd03c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xecd040: LeaveFrame
    //     0xecd040: mov             SP, fp
    //     0xecd044: ldp             fp, lr, [SP], #0x10
    // 0xecd048: ret
    //     0xecd048: ret             
    // 0xecd04c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecd04c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecd050: b               #0xecd02c
  }
}
