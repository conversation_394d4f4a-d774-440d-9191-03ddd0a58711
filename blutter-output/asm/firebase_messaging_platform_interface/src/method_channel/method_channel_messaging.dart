// lib: , url: package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart

// class id: 1048757, size: 0x8
class :: {

  static void _firebaseMessagingCallbackDispatcher() {
    // ** addr: 0xec4b9c, size: 0x78
    // 0xec4b9c: EnterFrame
    //     0xec4b9c: stp             fp, lr, [SP, #-0x10]!
    //     0xec4ba0: mov             fp, SP
    // 0xec4ba4: AllocStack(0x18)
    //     0xec4ba4: sub             SP, SP, #0x18
    // 0xec4ba8: CheckStackOverflow
    //     0xec4ba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec4bac: cmp             SP, x16
    //     0xec4bb0: b.ls            #0xec4c0c
    // 0xec4bb4: r0 = ensureInitialized()
    //     0xec4bb4: bl              #0x6916f4  ; [package:flutter/src/widgets/binding.dart] WidgetsFlutterBinding::ensureInitialized
    // 0xec4bb8: r1 = Function '<anonymous closure>': static.
    //     0xec4bb8: add             x1, PP, #0xc, lsl #12  ; [pp+0xc2d0] AnonymousClosure: static (0xec4c14), in [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] ::_firebaseMessagingCallbackDispatcher (0xec4b9c)
    //     0xec4bbc: ldr             x1, [x1, #0x2d0]
    // 0xec4bc0: r2 = Null
    //     0xec4bc0: mov             x2, NULL
    // 0xec4bc4: r0 = AllocateClosure()
    //     0xec4bc4: bl              #0xec1630  ; AllocateClosureStub
    // 0xec4bc8: mov             x2, x0
    // 0xec4bcc: r1 = Instance_MethodChannel
    //     0xec4bcc: add             x1, PP, #0xc, lsl #12  ; [pp+0xc2d8] Obj!MethodChannel@e11371
    //     0xec4bd0: ldr             x1, [x1, #0x2d8]
    // 0xec4bd4: r0 = setMethodCallHandler()
    //     0xec4bd4: bl              #0x6921f4  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::setMethodCallHandler
    // 0xec4bd8: r16 = <void?>
    //     0xec4bd8: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xec4bdc: r30 = Instance_MethodChannel
    //     0xec4bdc: add             lr, PP, #0xc, lsl #12  ; [pp+0xc2d8] Obj!MethodChannel@e11371
    //     0xec4be0: ldr             lr, [lr, #0x2d8]
    // 0xec4be4: stp             lr, x16, [SP, #8]
    // 0xec4be8: r16 = "MessagingBackground#initialized"
    //     0xec4be8: add             x16, PP, #0xc, lsl #12  ; [pp+0xc2e0] "MessagingBackground#initialized"
    //     0xec4bec: ldr             x16, [x16, #0x2e0]
    // 0xec4bf0: str             x16, [SP]
    // 0xec4bf4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xec4bf4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xec4bf8: r0 = invokeMethod()
    //     0xec4bf8: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xec4bfc: r0 = Null
    //     0xec4bfc: mov             x0, NULL
    // 0xec4c00: LeaveFrame
    //     0xec4c00: mov             SP, fp
    //     0xec4c04: ldp             fp, lr, [SP], #0x10
    // 0xec4c08: ret
    //     0xec4c08: ret             
    // 0xec4c0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec4c0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec4c10: b               #0xec4bb4
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, MethodCall) async {
    // ** addr: 0xec4c14, size: 0x24c
    // 0xec4c14: EnterFrame
    //     0xec4c14: stp             fp, lr, [SP, #-0x10]!
    //     0xec4c18: mov             fp, SP
    // 0xec4c1c: AllocStack(0x88)
    //     0xec4c1c: sub             SP, SP, #0x88
    // 0xec4c20: SetupParameters(dynamic _ /* r1, fp-0x70 */, dynamic _ /* r2, fp-0x68 */)
    //     0xec4c20: stur            NULL, [fp, #-8]
    //     0xec4c24: movz            x0, #0
    //     0xec4c28: add             x1, fp, w0, sxtw #2
    //     0xec4c2c: ldr             x1, [x1, #0x18]
    //     0xec4c30: stur            x1, [fp, #-0x70]
    //     0xec4c34: add             x2, fp, w0, sxtw #2
    //     0xec4c38: ldr             x2, [x2, #0x10]
    //     0xec4c3c: stur            x2, [fp, #-0x68]
    //     0xec4c40: ldur            w3, [x1, #0x17]
    //     0xec4c44: add             x3, x3, HEAP, lsl #32
    //     0xec4c48: stur            x3, [fp, #-0x60]
    // 0xec4c4c: CheckStackOverflow
    //     0xec4c4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec4c50: cmp             SP, x16
    //     0xec4c54: b.ls            #0xec4e54
    // 0xec4c58: InitAsync() -> Future<Null?>
    //     0xec4c58: ldr             x0, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    //     0xec4c5c: bl              #0x661298  ; InitAsyncStub
    // 0xec4c60: ldur            x1, [fp, #-0x68]
    // 0xec4c64: LoadField: r2 = r1->field_7
    //     0xec4c64: ldur            w2, [x1, #7]
    // 0xec4c68: DecompressPointer r2
    //     0xec4c68: add             x2, x2, HEAP, lsl #32
    // 0xec4c6c: stur            x2, [fp, #-0x60]
    // 0xec4c70: r0 = LoadClassIdInstr(r2)
    //     0xec4c70: ldur            x0, [x2, #-1]
    //     0xec4c74: ubfx            x0, x0, #0xc, #0x14
    // 0xec4c78: r16 = "MessagingBackground#onMessage"
    //     0xec4c78: add             x16, PP, #0xc, lsl #12  ; [pp+0xc2e8] "MessagingBackground#onMessage"
    //     0xec4c7c: ldr             x16, [x16, #0x2e8]
    // 0xec4c80: stp             x16, x2, [SP]
    // 0xec4c84: mov             lr, x0
    // 0xec4c88: ldr             lr, [x21, lr, lsl #3]
    // 0xec4c8c: blr             lr
    // 0xec4c90: tbnz            w0, #4, #0xec4e04
    // 0xec4c94: ldur            x0, [fp, #-0x68]
    // 0xec4c98: LoadField: r1 = r0->field_b
    //     0xec4c98: ldur            w1, [x0, #0xb]
    // 0xec4c9c: DecompressPointer r1
    //     0xec4c9c: add             x1, x1, HEAP, lsl #32
    // 0xec4ca0: stur            x1, [fp, #-0x70]
    // 0xec4ca4: r16 = "userCallbackHandle"
    //     0xec4ca4: add             x16, PP, #0xc, lsl #12  ; [pp+0xc2f0] "userCallbackHandle"
    //     0xec4ca8: ldr             x16, [x16, #0x2f0]
    // 0xec4cac: stp             x16, x1, [SP]
    // 0xec4cb0: r4 = 0
    //     0xec4cb0: movz            x4, #0
    // 0xec4cb4: ldr             x0, [SP, #8]
    // 0xec4cb8: r16 = UnlinkedCall_0x5f3c08
    //     0xec4cb8: add             x16, PP, #0xc, lsl #12  ; [pp+0xc2f8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xec4cbc: add             x16, x16, #0x2f8
    // 0xec4cc0: ldp             x5, lr, [x16]
    // 0xec4cc4: blr             lr
    // 0xec4cc8: mov             x3, x0
    // 0xec4ccc: r2 = Null
    //     0xec4ccc: mov             x2, NULL
    // 0xec4cd0: r1 = Null
    //     0xec4cd0: mov             x1, NULL
    // 0xec4cd4: stur            x3, [fp, #-0x68]
    // 0xec4cd8: branchIfSmi(r0, 0xec4d00)
    //     0xec4cd8: tbz             w0, #0, #0xec4d00
    // 0xec4cdc: r4 = LoadClassIdInstr(r0)
    //     0xec4cdc: ldur            x4, [x0, #-1]
    //     0xec4ce0: ubfx            x4, x4, #0xc, #0x14
    // 0xec4ce4: sub             x4, x4, #0x3c
    // 0xec4ce8: cmp             x4, #1
    // 0xec4cec: b.ls            #0xec4d00
    // 0xec4cf0: r8 = int
    //     0xec4cf0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xec4cf4: r3 = Null
    //     0xec4cf4: add             x3, PP, #0xc, lsl #12  ; [pp+0xc308] Null
    //     0xec4cf8: ldr             x3, [x3, #0x308]
    // 0xec4cfc: r0 = int()
    //     0xec4cfc: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xec4d00: ldur            x0, [fp, #-0x68]
    // 0xec4d04: r1 = LoadInt32Instr(r0)
    //     0xec4d04: sbfx            x1, x0, #1, #0x1f
    //     0xec4d08: tbz             w0, #0, #0xec4d10
    //     0xec4d0c: ldur            x1, [x0, #7]
    // 0xec4d10: stur            x1, [fp, #-0x78]
    // 0xec4d14: r0 = CallbackHandle()
    //     0xec4d14: bl              #0x8374c8  ; AllocateCallbackHandleStub -> CallbackHandle (size=0x10)
    // 0xec4d18: mov             x1, x0
    // 0xec4d1c: ldur            x0, [fp, #-0x78]
    // 0xec4d20: StoreField: r1->field_7 = r0
    //     0xec4d20: stur            x0, [x1, #7]
    // 0xec4d24: r0 = getCallbackFromHandle()
    //     0xec4d24: bl              #0x9a6c10  ; [dart:ui] PluginUtilities::getCallbackFromHandle
    // 0xec4d28: mov             x3, x0
    // 0xec4d2c: stur            x3, [fp, #-0x68]
    // 0xec4d30: cmp             w3, NULL
    // 0xec4d34: b.eq            #0xec4e5c
    // 0xec4d38: mov             x0, x3
    // 0xec4d3c: r2 = Null
    //     0xec4d3c: mov             x2, NULL
    // 0xec4d40: r1 = Null
    //     0xec4d40: mov             x1, NULL
    // 0xec4d44: r8 = (dynamic this, RemoteMessage) => Future<void?>
    //     0xec4d44: add             x8, PP, #0xc, lsl #12  ; [pp+0xc318] FunctionType: (dynamic this, RemoteMessage) => Future<void?>
    //     0xec4d48: ldr             x8, [x8, #0x318]
    // 0xec4d4c: r3 = Null
    //     0xec4d4c: add             x3, PP, #0xc, lsl #12  ; [pp+0xc320] Null
    //     0xec4d50: ldr             x3, [x3, #0x320]
    // 0xec4d54: r0 = DefaultTypeTest()
    //     0xec4d54: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xec4d58: ldur            x16, [fp, #-0x70]
    // 0xec4d5c: r30 = "message"
    //     0xec4d5c: add             lr, PP, #0xc, lsl #12  ; [pp+0xc330] "message"
    //     0xec4d60: ldr             lr, [lr, #0x330]
    // 0xec4d64: stp             lr, x16, [SP]
    // 0xec4d68: r4 = 0
    //     0xec4d68: movz            x4, #0
    // 0xec4d6c: ldr             x0, [SP, #8]
    // 0xec4d70: r16 = UnlinkedCall_0x5f3c08
    //     0xec4d70: add             x16, PP, #0xc, lsl #12  ; [pp+0xc338] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xec4d74: add             x16, x16, #0x338
    // 0xec4d78: ldp             x5, lr, [x16]
    // 0xec4d7c: blr             lr
    // 0xec4d80: mov             x3, x0
    // 0xec4d84: r2 = Null
    //     0xec4d84: mov             x2, NULL
    // 0xec4d88: r1 = Null
    //     0xec4d88: mov             x1, NULL
    // 0xec4d8c: stur            x3, [fp, #-0x70]
    // 0xec4d90: r8 = Map
    //     0xec4d90: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0xec4d94: r3 = Null
    //     0xec4d94: add             x3, PP, #0xc, lsl #12  ; [pp+0xc348] Null
    //     0xec4d98: ldr             x3, [x3, #0x348]
    // 0xec4d9c: r0 = Map()
    //     0xec4d9c: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0xec4da0: ldur            x2, [fp, #-0x70]
    // 0xec4da4: r1 = <String, dynamic>
    //     0xec4da4: ldr             x1, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xec4da8: r0 = LinkedHashMap.from()
    //     0xec4da8: bl              #0x63c6f0  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0xec4dac: mov             x2, x0
    // 0xec4db0: r1 = Null
    //     0xec4db0: mov             x1, NULL
    // 0xec4db4: r0 = RemoteMessage.fromMap()
    //     0xec4db4: bl              #0xec4e60  ; [package:firebase_messaging_platform_interface/src/remote_message.dart] RemoteMessage::RemoteMessage.fromMap
    // 0xec4db8: ldur            x16, [fp, #-0x68]
    // 0xec4dbc: stp             x0, x16, [SP]
    // 0xec4dc0: ldur            x0, [fp, #-0x68]
    // 0xec4dc4: ClosureCall
    //     0xec4dc4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xec4dc8: ldur            x2, [x0, #0x1f]
    //     0xec4dcc: blr             x2
    // 0xec4dd0: mov             x1, x0
    // 0xec4dd4: stur            x1, [fp, #-0x68]
    // 0xec4dd8: r0 = Await()
    //     0xec4dd8: bl              #0x661044  ; AwaitStub
    // 0xec4ddc: b               #0xec4dfc
    // 0xec4de0: sub             SP, fp, #0x88
    // 0xec4de4: stur            x0, [fp, #-0x68]
    // 0xec4de8: r1 = "FlutterFire Messaging: An error occurred in your background messaging handler:"
    //     0xec4de8: add             x1, PP, #0xc, lsl #12  ; [pp+0xc358] "FlutterFire Messaging: An error occurred in your background messaging handler:"
    //     0xec4dec: ldr             x1, [x1, #0x358]
    // 0xec4df0: r0 = print()
    //     0xec4df0: bl              #0x63fe38  ; [dart:core] ::print
    // 0xec4df4: ldur            x1, [fp, #-0x68]
    // 0xec4df8: r0 = print()
    //     0xec4df8: bl              #0x63fe38  ; [dart:core] ::print
    // 0xec4dfc: r0 = Null
    //     0xec4dfc: mov             x0, NULL
    // 0xec4e00: r0 = ReturnAsyncNotFuture()
    //     0xec4e00: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xec4e04: ldur            x0, [fp, #-0x60]
    // 0xec4e08: r1 = Null
    //     0xec4e08: mov             x1, NULL
    // 0xec4e0c: r2 = 4
    //     0xec4e0c: movz            x2, #0x4
    // 0xec4e10: r0 = AllocateArray()
    //     0xec4e10: bl              #0xec22fc  ; AllocateArrayStub
    // 0xec4e14: mov             x1, x0
    // 0xec4e18: ldur            x0, [fp, #-0x60]
    // 0xec4e1c: StoreField: r1->field_f = r0
    //     0xec4e1c: stur            w0, [x1, #0xf]
    // 0xec4e20: r16 = " has not been implemented"
    //     0xec4e20: add             x16, PP, #0xc, lsl #12  ; [pp+0xc360] " has not been implemented"
    //     0xec4e24: ldr             x16, [x16, #0x360]
    // 0xec4e28: StoreField: r1->field_13 = r16
    //     0xec4e28: stur            w16, [x1, #0x13]
    // 0xec4e2c: str             x1, [SP]
    // 0xec4e30: r0 = _interpolate()
    //     0xec4e30: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xec4e34: stur            x0, [fp, #-0x60]
    // 0xec4e38: r0 = UnimplementedError()
    //     0xec4e38: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0xec4e3c: mov             x1, x0
    // 0xec4e40: ldur            x0, [fp, #-0x60]
    // 0xec4e44: StoreField: r1->field_b = r0
    //     0xec4e44: stur            w0, [x1, #0xb]
    // 0xec4e48: mov             x0, x1
    // 0xec4e4c: r0 = Throw()
    //     0xec4e4c: bl              #0xec04b8  ; ThrowStub
    // 0xec4e50: brk             #0
    // 0xec4e54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec4e54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec4e58: b               #0xec4c58
    // 0xec4e5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xec4e5c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static void _firebaseMessagingCallbackDispatcher(dynamic) {
    // ** addr: 0xec62e8, size: 0x2c
    // 0xec62e8: EnterFrame
    //     0xec62e8: stp             fp, lr, [SP, #-0x10]!
    //     0xec62ec: mov             fp, SP
    // 0xec62f0: CheckStackOverflow
    //     0xec62f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec62f4: cmp             SP, x16
    //     0xec62f8: b.ls            #0xec630c
    // 0xec62fc: r0 = _firebaseMessagingCallbackDispatcher()
    //     0xec62fc: bl              #0xec4b9c  ; [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] ::_firebaseMessagingCallbackDispatcher
    // 0xec6300: LeaveFrame
    //     0xec6300: mov             SP, fp
    //     0xec6304: ldp             fp, lr, [SP], #0x10
    // 0xec6308: ret
    //     0xec6308: ret             
    // 0xec630c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec630c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec6310: b               #0xec62fc
  }
}

// class id: 5935, size: 0xc, field offset: 0xc
class MethodChannelFirebaseMessaging extends FirebaseMessagingPlatform {

  static late StreamController<String> tokenStreamController; // offset: 0x1008

  _ unsubscribeFromTopic(/* No info */) async {
    // ** addr: 0xecb684, size: 0x144
    // 0xecb684: EnterFrame
    //     0xecb684: stp             fp, lr, [SP, #-0x10]!
    //     0xecb688: mov             fp, SP
    // 0xecb68c: AllocStack(0x88)
    //     0xecb68c: sub             SP, SP, #0x88
    // 0xecb690: SetupParameters(MethodChannelFirebaseMessaging this /* r1 => r2, fp-0x58 */, dynamic _ /* r2 => r1, fp-0x60 */)
    //     0xecb690: stur            NULL, [fp, #-8]
    //     0xecb694: stur            x1, [fp, #-0x58]
    //     0xecb698: mov             x16, x2
    //     0xecb69c: mov             x2, x1
    //     0xecb6a0: mov             x1, x16
    //     0xecb6a4: stur            x1, [fp, #-0x60]
    // 0xecb6a8: CheckStackOverflow
    //     0xecb6a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecb6ac: cmp             SP, x16
    //     0xecb6b0: b.ls            #0xecb7c0
    // 0xecb6b4: InitAsync() -> Future<void?>
    //     0xecb6b4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xecb6b8: bl              #0x661298  ; InitAsyncStub
    // 0xecb6bc: ldur            x1, [fp, #-0x58]
    // 0xecb6c0: r0 = onOnlineModeFailure()
    //     0xecb6c0: bl              #0xe35dcc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onOnlineModeFailure
    // 0xecb6c4: mov             x1, x0
    // 0xecb6c8: stur            x1, [fp, #-0x68]
    // 0xecb6cc: r0 = Await()
    //     0xecb6cc: bl              #0x661044  ; AwaitStub
    // 0xecb6d0: r1 = Null
    //     0xecb6d0: mov             x1, NULL
    // 0xecb6d4: r2 = 8
    //     0xecb6d4: movz            x2, #0x8
    // 0xecb6d8: r0 = AllocateArray()
    //     0xecb6d8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xecb6dc: stur            x0, [fp, #-0x68]
    // 0xecb6e0: r16 = "appName"
    //     0xecb6e0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf9c0] "appName"
    //     0xecb6e4: ldr             x16, [x16, #0x9c0]
    // 0xecb6e8: StoreField: r0->field_f = r16
    //     0xecb6e8: stur            w16, [x0, #0xf]
    // 0xecb6ec: ldur            x1, [fp, #-0x58]
    // 0xecb6f0: r0 = app()
    //     0xecb6f0: bl              #0xecb800  ; [package:firebase_messaging_platform_interface/src/platform_interface/platform_interface_messaging.dart] FirebaseMessagingPlatform::app
    // 0xecb6f4: LoadField: r1 = r0->field_7
    //     0xecb6f4: ldur            w1, [x0, #7]
    // 0xecb6f8: DecompressPointer r1
    //     0xecb6f8: add             x1, x1, HEAP, lsl #32
    // 0xecb6fc: LoadField: r0 = r1->field_7
    //     0xecb6fc: ldur            w0, [x1, #7]
    // 0xecb700: DecompressPointer r0
    //     0xecb700: add             x0, x0, HEAP, lsl #32
    // 0xecb704: ldur            x1, [fp, #-0x68]
    // 0xecb708: ArrayStore: r1[1] = r0  ; List_4
    //     0xecb708: add             x25, x1, #0x13
    //     0xecb70c: str             w0, [x25]
    //     0xecb710: tbz             w0, #0, #0xecb72c
    //     0xecb714: ldurb           w16, [x1, #-1]
    //     0xecb718: ldurb           w17, [x0, #-1]
    //     0xecb71c: and             x16, x17, x16, lsr #2
    //     0xecb720: tst             x16, HEAP, lsr #32
    //     0xecb724: b.eq            #0xecb72c
    //     0xecb728: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xecb72c: ldur            x2, [fp, #-0x68]
    // 0xecb730: r16 = "topic"
    //     0xecb730: add             x16, PP, #0x12, lsl #12  ; [pp+0x124a8] "topic"
    //     0xecb734: ldr             x16, [x16, #0x4a8]
    // 0xecb738: ArrayStore: r2[0] = r16  ; List_4
    //     0xecb738: stur            w16, [x2, #0x17]
    // 0xecb73c: mov             x1, x2
    // 0xecb740: ldur            x0, [fp, #-0x60]
    // 0xecb744: ArrayStore: r1[3] = r0  ; List_4
    //     0xecb744: add             x25, x1, #0x1b
    //     0xecb748: str             w0, [x25]
    //     0xecb74c: tbz             w0, #0, #0xecb768
    //     0xecb750: ldurb           w16, [x1, #-1]
    //     0xecb754: ldurb           w17, [x0, #-1]
    //     0xecb758: and             x16, x17, x16, lsr #2
    //     0xecb75c: tst             x16, HEAP, lsr #32
    //     0xecb760: b.eq            #0xecb768
    //     0xecb764: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xecb768: r16 = <String, String>
    //     0xecb768: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xecb76c: ldr             x16, [x16, #0x668]
    // 0xecb770: stp             x2, x16, [SP]
    // 0xecb774: r0 = Map._fromLiteral()
    //     0xecb774: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xecb778: r16 = Instance_MethodChannel
    //     0xecb778: add             x16, PP, #0x12, lsl #12  ; [pp+0x124b0] Obj!MethodChannel@e113d1
    //     0xecb77c: ldr             x16, [x16, #0x4b0]
    // 0xecb780: stp             x16, NULL, [SP, #0x10]
    // 0xecb784: r16 = "Messaging#unsubscribeFromTopic"
    //     0xecb784: add             x16, PP, #0x12, lsl #12  ; [pp+0x124b8] "Messaging#unsubscribeFromTopic"
    //     0xecb788: ldr             x16, [x16, #0x4b8]
    // 0xecb78c: stp             x0, x16, [SP]
    // 0xecb790: r4 = const [0x2, 0x3, 0x3, 0x3, null]
    //     0xecb790: ldr             x4, [PP, #0x3310]  ; [pp+0x3310] List(5) [0x2, 0x3, 0x3, 0x3, Null]
    // 0xecb794: r0 = invokeMapMethod()
    //     0xecb794: bl              #0x698d1c  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMapMethod
    // 0xecb798: mov             x1, x0
    // 0xecb79c: stur            x1, [fp, #-0x58]
    // 0xecb7a0: r0 = Await()
    //     0xecb7a0: bl              #0x661044  ; AwaitStub
    // 0xecb7a4: b               #0xecb7b8
    // 0xecb7a8: sub             SP, fp, #0x88
    // 0xecb7ac: mov             x2, x1
    // 0xecb7b0: mov             x1, x0
    // 0xecb7b4: r0 = convertPlatformException()
    //     0xecb7b4: bl              #0xecb7c8  ; [package:firebase_messaging_platform_interface/src/method_channel/utils/exception.dart] ::convertPlatformException
    // 0xecb7b8: r0 = Null
    //     0xecb7b8: mov             x0, NULL
    // 0xecb7bc: r0 = ReturnAsyncNotFuture()
    //     0xecb7bc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xecb7c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecb7c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecb7c4: b               #0xecb6b4
  }
  _ delegateFor(/* No info */) {
    // ** addr: 0xecb998, size: 0x98
    // 0xecb998: EnterFrame
    //     0xecb998: stp             fp, lr, [SP, #-0x10]!
    //     0xecb99c: mov             fp, SP
    // 0xecb9a0: AllocStack(0x10)
    //     0xecb9a0: sub             SP, SP, #0x10
    // 0xecb9a4: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xecb9a4: stur            x2, [fp, #-8]
    // 0xecb9a8: CheckStackOverflow
    //     0xecb9a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecb9ac: cmp             SP, x16
    //     0xecb9b0: b.ls            #0xecba28
    // 0xecb9b4: r0 = MethodChannelFirebaseMessaging()
    //     0xecb9b4: bl              #0xecba30  ; AllocateMethodChannelFirebaseMessagingStub -> MethodChannelFirebaseMessaging (size=0xc)
    // 0xecb9b8: mov             x1, x0
    // 0xecb9bc: ldur            x0, [fp, #-8]
    // 0xecb9c0: stur            x1, [fp, #-0x10]
    // 0xecb9c4: StoreField: r1->field_7 = r0
    //     0xecb9c4: stur            w0, [x1, #7]
    // 0xecb9c8: r0 = InitLateStaticField(0xfe8) // [package:firebase_messaging_platform_interface/src/platform_interface/platform_interface_messaging.dart] FirebaseMessagingPlatform::_token
    //     0xecb9c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xecb9cc: ldr             x0, [x0, #0x1fd0]
    //     0xecb9d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xecb9d4: cmp             w0, w16
    //     0xecb9d8: b.ne            #0xecb9e8
    //     0xecb9dc: add             x2, PP, #0x12, lsl #12  ; [pp+0x124e0] Field <FirebaseMessagingPlatform._token@1049004436>: static late final (offset: 0xfe8)
    //     0xecb9e0: ldr             x2, [x2, #0x4e0]
    //     0xecb9e4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xecb9e8: stur            x0, [fp, #-8]
    // 0xecb9ec: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0xecb9ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xecb9f0: ldr             x0, [x0, #0xc08]
    //     0xecb9f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xecb9f8: cmp             w0, w16
    //     0xecb9fc: b.ne            #0xecba08
    //     0xecba00: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0xecba04: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xecba08: mov             x1, x0
    // 0xecba0c: ldur            x2, [fp, #-0x10]
    // 0xecba10: ldur            x3, [fp, #-8]
    // 0xecba14: r0 = []=()
    //     0xecba14: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0xecba18: ldur            x0, [fp, #-0x10]
    // 0xecba1c: LeaveFrame
    //     0xecba1c: mov             SP, fp
    //     0xecba20: ldp             fp, lr, [SP], #0x10
    // 0xecba24: ret
    //     0xecba24: ret             
    // 0xecba28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecba28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecba2c: b               #0xecb9b4
  }
  get _ instance(/* No info */) {
    // ** addr: 0xecba94, size: 0x8c
    // 0xecba94: EnterFrame
    //     0xecba94: stp             fp, lr, [SP, #-0x10]!
    //     0xecba98: mov             fp, SP
    // 0xecba9c: AllocStack(0x10)
    //     0xecba9c: sub             SP, SP, #0x10
    // 0xecbaa0: CheckStackOverflow
    //     0xecbaa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecbaa4: cmp             SP, x16
    //     0xecbaa8: b.ls            #0xecbb18
    // 0xecbaac: r0 = InitLateStaticField(0xfe8) // [package:firebase_messaging_platform_interface/src/platform_interface/platform_interface_messaging.dart] FirebaseMessagingPlatform::_token
    //     0xecbaac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xecbab0: ldr             x0, [x0, #0x1fd0]
    //     0xecbab4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xecbab8: cmp             w0, w16
    //     0xecbabc: b.ne            #0xecbacc
    //     0xecbac0: add             x2, PP, #0x12, lsl #12  ; [pp+0x124e0] Field <FirebaseMessagingPlatform._token@1049004436>: static late final (offset: 0xfe8)
    //     0xecbac4: ldr             x2, [x2, #0x4e0]
    //     0xecbac8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xecbacc: stur            x0, [fp, #-8]
    // 0xecbad0: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0xecbad0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xecbad4: ldr             x0, [x0, #0xc08]
    //     0xecbad8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xecbadc: cmp             w0, w16
    //     0xecbae0: b.ne            #0xecbaec
    //     0xecbae4: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0xecbae8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xecbaec: stur            x0, [fp, #-0x10]
    // 0xecbaf0: r0 = MethodChannelFirebaseMessaging()
    //     0xecbaf0: bl              #0xecba30  ; AllocateMethodChannelFirebaseMessagingStub -> MethodChannelFirebaseMessaging (size=0xc)
    // 0xecbaf4: ldur            x1, [fp, #-0x10]
    // 0xecbaf8: mov             x2, x0
    // 0xecbafc: ldur            x3, [fp, #-8]
    // 0xecbb00: stur            x0, [fp, #-8]
    // 0xecbb04: r0 = []=()
    //     0xecbb04: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0xecbb08: ldur            x0, [fp, #-8]
    // 0xecbb0c: LeaveFrame
    //     0xecbb0c: mov             SP, fp
    //     0xecbb10: ldp             fp, lr, [SP], #0x10
    // 0xecbb14: ret
    //     0xecbb14: ret             
    // 0xecbb18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecbb18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecbb1c: b               #0xecbaac
  }
  static void setMethodCallHandlers() {
    // ** addr: 0xecbb20, size: 0x4c
    // 0xecbb20: EnterFrame
    //     0xecbb20: stp             fp, lr, [SP, #-0x10]!
    //     0xecbb24: mov             fp, SP
    // 0xecbb28: CheckStackOverflow
    //     0xecbb28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecbb2c: cmp             SP, x16
    //     0xecbb30: b.ls            #0xecbb64
    // 0xecbb34: r1 = Function '<anonymous closure>': static.
    //     0xecbb34: add             x1, PP, #0x12, lsl #12  ; [pp+0x124e8] AnonymousClosure: static (0xecbb6c), in [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] MethodChannelFirebaseMessaging::setMethodCallHandlers (0xecbb20)
    //     0xecbb38: ldr             x1, [x1, #0x4e8]
    // 0xecbb3c: r2 = Null
    //     0xecbb3c: mov             x2, NULL
    // 0xecbb40: r0 = AllocateClosure()
    //     0xecbb40: bl              #0xec1630  ; AllocateClosureStub
    // 0xecbb44: mov             x2, x0
    // 0xecbb48: r1 = Instance_MethodChannel
    //     0xecbb48: add             x1, PP, #0x12, lsl #12  ; [pp+0x124b0] Obj!MethodChannel@e113d1
    //     0xecbb4c: ldr             x1, [x1, #0x4b0]
    // 0xecbb50: r0 = setMethodCallHandler()
    //     0xecbb50: bl              #0x6921f4  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::setMethodCallHandler
    // 0xecbb54: r0 = Null
    //     0xecbb54: mov             x0, NULL
    // 0xecbb58: LeaveFrame
    //     0xecbb58: mov             SP, fp
    //     0xecbb5c: ldp             fp, lr, [SP], #0x10
    // 0xecbb60: ret
    //     0xecbb60: ret             
    // 0xecbb64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecbb64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecbb68: b               #0xecbb34
  }
  [closure] static Future<void> <anonymous closure>(dynamic, MethodCall) async {
    // ** addr: 0xecbb6c, size: 0x300
    // 0xecbb6c: EnterFrame
    //     0xecbb6c: stp             fp, lr, [SP, #-0x10]!
    //     0xecbb70: mov             fp, SP
    // 0xecbb74: AllocStack(0x38)
    //     0xecbb74: sub             SP, SP, #0x38
    // 0xecbb78: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xecbb78: stur            NULL, [fp, #-8]
    //     0xecbb7c: movz            x0, #0
    //     0xecbb80: add             x1, fp, w0, sxtw #2
    //     0xecbb84: ldr             x1, [x1, #0x18]
    //     0xecbb88: add             x2, fp, w0, sxtw #2
    //     0xecbb8c: ldr             x2, [x2, #0x10]
    //     0xecbb90: stur            x2, [fp, #-0x18]
    //     0xecbb94: ldur            w3, [x1, #0x17]
    //     0xecbb98: add             x3, x3, HEAP, lsl #32
    //     0xecbb9c: stur            x3, [fp, #-0x10]
    // 0xecbba0: CheckStackOverflow
    //     0xecbba0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecbba4: cmp             SP, x16
    //     0xecbba8: b.ls            #0xecbe64
    // 0xecbbac: InitAsync() -> Future<void?>
    //     0xecbbac: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xecbbb0: bl              #0x661298  ; InitAsyncStub
    // 0xecbbb4: ldur            x0, [fp, #-0x18]
    // 0xecbbb8: LoadField: r1 = r0->field_7
    //     0xecbbb8: ldur            w1, [x0, #7]
    // 0xecbbbc: DecompressPointer r1
    //     0xecbbbc: add             x1, x1, HEAP, lsl #32
    // 0xecbbc0: stur            x1, [fp, #-0x10]
    // 0xecbbc4: r16 = "Messaging#onTokenRefresh"
    //     0xecbbc4: add             x16, PP, #0x12, lsl #12  ; [pp+0x124f0] "Messaging#onTokenRefresh"
    //     0xecbbc8: ldr             x16, [x16, #0x4f0]
    // 0xecbbcc: stp             x1, x16, [SP]
    // 0xecbbd0: r0 = ==()
    //     0xecbbd0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xecbbd4: tbnz            w0, #4, #0xecbc5c
    // 0xecbbd8: ldur            x0, [fp, #-0x18]
    // 0xecbbdc: r0 = InitLateStaticField(0x1008) // [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] MethodChannelFirebaseMessaging::tokenStreamController
    //     0xecbbdc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xecbbe0: ldr             x0, [x0, #0x2010]
    //     0xecbbe4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xecbbe8: cmp             w0, w16
    //     0xecbbec: b.ne            #0xecbbfc
    //     0xecbbf0: add             x2, PP, #0x12, lsl #12  ; [pp+0x124f8] Field <MethodChannelFirebaseMessaging.tokenStreamController>: static late (offset: 0x1008)
    //     0xecbbf4: ldr             x2, [x2, #0x4f8]
    //     0xecbbf8: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xecbbfc: mov             x3, x0
    // 0xecbc00: ldur            x0, [fp, #-0x18]
    // 0xecbc04: stur            x3, [fp, #-0x28]
    // 0xecbc08: LoadField: r4 = r0->field_b
    //     0xecbc08: ldur            w4, [x0, #0xb]
    // 0xecbc0c: DecompressPointer r4
    //     0xecbc0c: add             x4, x4, HEAP, lsl #32
    // 0xecbc10: mov             x0, x4
    // 0xecbc14: stur            x4, [fp, #-0x20]
    // 0xecbc18: r2 = Null
    //     0xecbc18: mov             x2, NULL
    // 0xecbc1c: r1 = Null
    //     0xecbc1c: mov             x1, NULL
    // 0xecbc20: r4 = 60
    //     0xecbc20: movz            x4, #0x3c
    // 0xecbc24: branchIfSmi(r0, 0xecbc30)
    //     0xecbc24: tbz             w0, #0, #0xecbc30
    // 0xecbc28: r4 = LoadClassIdInstr(r0)
    //     0xecbc28: ldur            x4, [x0, #-1]
    //     0xecbc2c: ubfx            x4, x4, #0xc, #0x14
    // 0xecbc30: sub             x4, x4, #0x5e
    // 0xecbc34: cmp             x4, #1
    // 0xecbc38: b.ls            #0xecbc4c
    // 0xecbc3c: r8 = String
    //     0xecbc3c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xecbc40: r3 = Null
    //     0xecbc40: add             x3, PP, #0x12, lsl #12  ; [pp+0x12500] Null
    //     0xecbc44: ldr             x3, [x3, #0x500]
    // 0xecbc48: r0 = String()
    //     0xecbc48: bl              #0xed43b0  ; IsType_String_Stub
    // 0xecbc4c: ldur            x1, [fp, #-0x28]
    // 0xecbc50: ldur            x2, [fp, #-0x20]
    // 0xecbc54: r0 = add()
    //     0xecbc54: bl              #0xc7541c  ; [dart:async] _BroadcastStreamController::add
    // 0xecbc58: b               #0xecbd88
    // 0xecbc5c: ldur            x0, [fp, #-0x18]
    // 0xecbc60: r16 = "Messaging#onMessage"
    //     0xecbc60: add             x16, PP, #0x12, lsl #12  ; [pp+0x12510] "Messaging#onMessage"
    //     0xecbc64: ldr             x16, [x16, #0x510]
    // 0xecbc68: ldur            lr, [fp, #-0x10]
    // 0xecbc6c: stp             lr, x16, [SP]
    // 0xecbc70: r0 = ==()
    //     0xecbc70: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xecbc74: tbnz            w0, #4, #0xecbcf4
    // 0xecbc78: ldur            x0, [fp, #-0x18]
    // 0xecbc7c: LoadField: r3 = r0->field_b
    //     0xecbc7c: ldur            w3, [x0, #0xb]
    // 0xecbc80: DecompressPointer r3
    //     0xecbc80: add             x3, x3, HEAP, lsl #32
    // 0xecbc84: mov             x0, x3
    // 0xecbc88: stur            x3, [fp, #-0x20]
    // 0xecbc8c: r2 = Null
    //     0xecbc8c: mov             x2, NULL
    // 0xecbc90: r1 = Null
    //     0xecbc90: mov             x1, NULL
    // 0xecbc94: r8 = Map
    //     0xecbc94: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0xecbc98: r3 = Null
    //     0xecbc98: add             x3, PP, #0x12, lsl #12  ; [pp+0x12518] Null
    //     0xecbc9c: ldr             x3, [x3, #0x518]
    // 0xecbca0: r0 = Map()
    //     0xecbca0: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0xecbca4: ldur            x2, [fp, #-0x20]
    // 0xecbca8: r1 = <String, dynamic>
    //     0xecbca8: ldr             x1, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xecbcac: r0 = LinkedHashMap.from()
    //     0xecbcac: bl              #0x63c6f0  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0xecbcb0: stur            x0, [fp, #-0x20]
    // 0xecbcb4: r0 = InitLateStaticField(0xff0) // [package:firebase_messaging_platform_interface/src/platform_interface/platform_interface_messaging.dart] FirebaseMessagingPlatform::onMessage
    //     0xecbcb4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xecbcb8: ldr             x0, [x0, #0x1fe0]
    //     0xecbcbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xecbcc0: cmp             w0, w16
    //     0xecbcc4: b.ne            #0xecbcd4
    //     0xecbcc8: add             x2, PP, #0x12, lsl #12  ; [pp+0x12528] Field <FirebaseMessagingPlatform.onMessage>: static late final (offset: 0xff0)
    //     0xecbccc: ldr             x2, [x2, #0x528]
    //     0xecbcd0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xecbcd4: ldur            x2, [fp, #-0x20]
    // 0xecbcd8: r1 = Null
    //     0xecbcd8: mov             x1, NULL
    // 0xecbcdc: stur            x0, [fp, #-0x20]
    // 0xecbce0: r0 = RemoteMessage.fromMap()
    //     0xecbce0: bl              #0xec4e60  ; [package:firebase_messaging_platform_interface/src/remote_message.dart] RemoteMessage::RemoteMessage.fromMap
    // 0xecbce4: ldur            x1, [fp, #-0x20]
    // 0xecbce8: mov             x2, x0
    // 0xecbcec: r0 = add()
    //     0xecbcec: bl              #0xc7541c  ; [dart:async] _BroadcastStreamController::add
    // 0xecbcf0: b               #0xecbd88
    // 0xecbcf4: ldur            x0, [fp, #-0x18]
    // 0xecbcf8: r16 = "Messaging#onMessageOpenedApp"
    //     0xecbcf8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12530] "Messaging#onMessageOpenedApp"
    //     0xecbcfc: ldr             x16, [x16, #0x530]
    // 0xecbd00: ldur            lr, [fp, #-0x10]
    // 0xecbd04: stp             lr, x16, [SP]
    // 0xecbd08: r0 = ==()
    //     0xecbd08: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xecbd0c: tbnz            w0, #4, #0xecbd90
    // 0xecbd10: ldur            x0, [fp, #-0x18]
    // 0xecbd14: LoadField: r3 = r0->field_b
    //     0xecbd14: ldur            w3, [x0, #0xb]
    // 0xecbd18: DecompressPointer r3
    //     0xecbd18: add             x3, x3, HEAP, lsl #32
    // 0xecbd1c: mov             x0, x3
    // 0xecbd20: stur            x3, [fp, #-0x20]
    // 0xecbd24: r2 = Null
    //     0xecbd24: mov             x2, NULL
    // 0xecbd28: r1 = Null
    //     0xecbd28: mov             x1, NULL
    // 0xecbd2c: r8 = Map
    //     0xecbd2c: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0xecbd30: r3 = Null
    //     0xecbd30: add             x3, PP, #0x12, lsl #12  ; [pp+0x12538] Null
    //     0xecbd34: ldr             x3, [x3, #0x538]
    // 0xecbd38: r0 = Map()
    //     0xecbd38: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0xecbd3c: ldur            x2, [fp, #-0x20]
    // 0xecbd40: r1 = <String, dynamic>
    //     0xecbd40: ldr             x1, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xecbd44: r0 = LinkedHashMap.from()
    //     0xecbd44: bl              #0x63c6f0  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0xecbd48: stur            x0, [fp, #-0x20]
    // 0xecbd4c: r0 = InitLateStaticField(0xff4) // [package:firebase_messaging_platform_interface/src/platform_interface/platform_interface_messaging.dart] FirebaseMessagingPlatform::onMessageOpenedApp
    //     0xecbd4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xecbd50: ldr             x0, [x0, #0x1fe8]
    //     0xecbd54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xecbd58: cmp             w0, w16
    //     0xecbd5c: b.ne            #0xecbd6c
    //     0xecbd60: add             x2, PP, #0x12, lsl #12  ; [pp+0x12548] Field <FirebaseMessagingPlatform.onMessageOpenedApp>: static late final (offset: 0xff4)
    //     0xecbd64: ldr             x2, [x2, #0x548]
    //     0xecbd68: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xecbd6c: ldur            x2, [fp, #-0x20]
    // 0xecbd70: r1 = Null
    //     0xecbd70: mov             x1, NULL
    // 0xecbd74: stur            x0, [fp, #-0x20]
    // 0xecbd78: r0 = RemoteMessage.fromMap()
    //     0xecbd78: bl              #0xec4e60  ; [package:firebase_messaging_platform_interface/src/remote_message.dart] RemoteMessage::RemoteMessage.fromMap
    // 0xecbd7c: ldur            x1, [fp, #-0x20]
    // 0xecbd80: mov             x2, x0
    // 0xecbd84: r0 = add()
    //     0xecbd84: bl              #0xc7541c  ; [dart:async] _BroadcastStreamController::add
    // 0xecbd88: r0 = Null
    //     0xecbd88: mov             x0, NULL
    // 0xecbd8c: r0 = ReturnAsyncNotFuture()
    //     0xecbd8c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xecbd90: ldur            x0, [fp, #-0x18]
    // 0xecbd94: r16 = "Messaging#onBackgroundMessage"
    //     0xecbd94: add             x16, PP, #0x12, lsl #12  ; [pp+0x12550] "Messaging#onBackgroundMessage"
    //     0xecbd98: ldr             x16, [x16, #0x550]
    // 0xecbd9c: ldur            lr, [fp, #-0x10]
    // 0xecbda0: stp             lr, x16, [SP]
    // 0xecbda4: r0 = ==()
    //     0xecbda4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xecbda8: tbnz            w0, #4, #0xecbe14
    // 0xecbdac: ldur            x0, [fp, #-0x18]
    // 0xecbdb0: LoadField: r3 = r0->field_b
    //     0xecbdb0: ldur            w3, [x0, #0xb]
    // 0xecbdb4: DecompressPointer r3
    //     0xecbdb4: add             x3, x3, HEAP, lsl #32
    // 0xecbdb8: mov             x0, x3
    // 0xecbdbc: stur            x3, [fp, #-0x20]
    // 0xecbdc0: r2 = Null
    //     0xecbdc0: mov             x2, NULL
    // 0xecbdc4: r1 = Null
    //     0xecbdc4: mov             x1, NULL
    // 0xecbdc8: r8 = Map
    //     0xecbdc8: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0xecbdcc: r3 = Null
    //     0xecbdcc: add             x3, PP, #0x12, lsl #12  ; [pp+0x12558] Null
    //     0xecbdd0: ldr             x3, [x3, #0x558]
    // 0xecbdd4: r0 = Map()
    //     0xecbdd4: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0xecbdd8: ldur            x2, [fp, #-0x20]
    // 0xecbddc: r1 = <String, dynamic>
    //     0xecbddc: ldr             x1, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xecbde0: r0 = LinkedHashMap.from()
    //     0xecbde0: bl              #0x63c6f0  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0xecbde4: r1 = LoadStaticField(0xff8)
    //     0xecbde4: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xecbde8: ldr             x1, [x1, #0x1ff0]
    // 0xecbdec: cmp             w1, NULL
    // 0xecbdf0: b.ne            #0xecbdfc
    // 0xecbdf4: r0 = Null
    //     0xecbdf4: mov             x0, NULL
    // 0xecbdf8: b               #0xecbe10
    // 0xecbdfc: mov             x2, x0
    // 0xecbe00: r1 = Null
    //     0xecbe00: mov             x1, NULL
    // 0xecbe04: r0 = RemoteMessage.fromMap()
    //     0xecbe04: bl              #0xec4e60  ; [package:firebase_messaging_platform_interface/src/remote_message.dart] RemoteMessage::RemoteMessage.fromMap
    // 0xecbe08: mov             x1, x0
    // 0xecbe0c: r0 = _firebaseMessagingBackgroundHandler()
    //     0xecbe0c: bl              #0xecbe9c  ; [package:nuonline/services/services.dart] ::_firebaseMessagingBackgroundHandler
    // 0xecbe10: r0 = ReturnAsync()
    //     0xecbe10: b               #0x6576a4  ; ReturnAsyncStub
    // 0xecbe14: ldur            x0, [fp, #-0x10]
    // 0xecbe18: r1 = Null
    //     0xecbe18: mov             x1, NULL
    // 0xecbe1c: r2 = 4
    //     0xecbe1c: movz            x2, #0x4
    // 0xecbe20: r0 = AllocateArray()
    //     0xecbe20: bl              #0xec22fc  ; AllocateArrayStub
    // 0xecbe24: mov             x1, x0
    // 0xecbe28: ldur            x0, [fp, #-0x10]
    // 0xecbe2c: StoreField: r1->field_f = r0
    //     0xecbe2c: stur            w0, [x1, #0xf]
    // 0xecbe30: r16 = " has not been implemented"
    //     0xecbe30: add             x16, PP, #0xc, lsl #12  ; [pp+0xc360] " has not been implemented"
    //     0xecbe34: ldr             x16, [x16, #0x360]
    // 0xecbe38: StoreField: r1->field_13 = r16
    //     0xecbe38: stur            w16, [x1, #0x13]
    // 0xecbe3c: str             x1, [SP]
    // 0xecbe40: r0 = _interpolate()
    //     0xecbe40: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xecbe44: stur            x0, [fp, #-0x10]
    // 0xecbe48: r0 = UnimplementedError()
    //     0xecbe48: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0xecbe4c: mov             x1, x0
    // 0xecbe50: ldur            x0, [fp, #-0x10]
    // 0xecbe54: StoreField: r1->field_b = r0
    //     0xecbe54: stur            w0, [x1, #0xb]
    // 0xecbe58: mov             x0, x1
    // 0xecbe5c: r0 = Throw()
    //     0xecbe5c: bl              #0xec04b8  ; ThrowStub
    // 0xecbe60: brk             #0
    // 0xecbe64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecbe64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecbe68: b               #0xecbbac
  }
  static StreamController<String> tokenStreamController() {
    // ** addr: 0xecc81c, size: 0x34
    // 0xecc81c: EnterFrame
    //     0xecc81c: stp             fp, lr, [SP, #-0x10]!
    //     0xecc820: mov             fp, SP
    // 0xecc824: CheckStackOverflow
    //     0xecc824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecc828: cmp             SP, x16
    //     0xecc82c: b.ls            #0xecc848
    // 0xecc830: r1 = <String>
    //     0xecc830: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xecc834: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xecc834: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xecc838: r0 = StreamController.broadcast()
    //     0xecc838: bl              #0x83522c  ; [dart:async] StreamController::StreamController.broadcast
    // 0xecc83c: LeaveFrame
    //     0xecc83c: mov             SP, fp
    //     0xecc840: ldp             fp, lr, [SP], #0x10
    // 0xecc844: ret
    //     0xecc844: ret             
    // 0xecc848: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecc848: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecc84c: b               #0xecc830
  }
  _ subscribeToTopic(/* No info */) async {
    // ** addr: 0xecc950, size: 0x160
    // 0xecc950: EnterFrame
    //     0xecc950: stp             fp, lr, [SP, #-0x10]!
    //     0xecc954: mov             fp, SP
    // 0xecc958: AllocStack(0x88)
    //     0xecc958: sub             SP, SP, #0x88
    // 0xecc95c: SetupParameters(MethodChannelFirebaseMessaging this /* r1 => r2, fp-0x58 */, dynamic _ /* r2 => r1, fp-0x60 */)
    //     0xecc95c: stur            NULL, [fp, #-8]
    //     0xecc960: stur            x1, [fp, #-0x58]
    //     0xecc964: mov             x16, x2
    //     0xecc968: mov             x2, x1
    //     0xecc96c: mov             x1, x16
    //     0xecc970: stur            x1, [fp, #-0x60]
    // 0xecc974: CheckStackOverflow
    //     0xecc974: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecc978: cmp             SP, x16
    //     0xecc97c: b.ls            #0xeccaa8
    // 0xecc980: InitAsync() -> Future<void?>
    //     0xecc980: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xecc984: bl              #0x661298  ; InitAsyncStub
    // 0xecc988: ldur            x1, [fp, #-0x58]
    // 0xecc98c: r0 = onOnlineModeFailure()
    //     0xecc98c: bl              #0xe35dcc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onOnlineModeFailure
    // 0xecc990: mov             x1, x0
    // 0xecc994: stur            x1, [fp, #-0x68]
    // 0xecc998: r0 = Await()
    //     0xecc998: bl              #0x661044  ; AwaitStub
    // 0xecc99c: ldur            x0, [fp, #-0x58]
    // 0xecc9a0: r1 = Null
    //     0xecc9a0: mov             x1, NULL
    // 0xecc9a4: r2 = 8
    //     0xecc9a4: movz            x2, #0x8
    // 0xecc9a8: r0 = AllocateArray()
    //     0xecc9a8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xecc9ac: stur            x0, [fp, #-0x68]
    // 0xecc9b0: r16 = "appName"
    //     0xecc9b0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf9c0] "appName"
    //     0xecc9b4: ldr             x16, [x16, #0x9c0]
    // 0xecc9b8: StoreField: r0->field_f = r16
    //     0xecc9b8: stur            w16, [x0, #0xf]
    // 0xecc9bc: ldur            x1, [fp, #-0x58]
    // 0xecc9c0: LoadField: r2 = r1->field_7
    //     0xecc9c0: ldur            w2, [x1, #7]
    // 0xecc9c4: DecompressPointer r2
    //     0xecc9c4: add             x2, x2, HEAP, lsl #32
    // 0xecc9c8: cmp             w2, NULL
    // 0xecc9cc: b.ne            #0xecc9d8
    // 0xecc9d0: r0 = app()
    //     0xecc9d0: bl              #0x90ffc4  ; [package:firebase_core/firebase_core.dart] Firebase::app
    // 0xecc9d4: b               #0xecc9dc
    // 0xecc9d8: mov             x0, x2
    // 0xecc9dc: ldur            x2, [fp, #-0x68]
    // 0xecc9e0: LoadField: r1 = r0->field_7
    //     0xecc9e0: ldur            w1, [x0, #7]
    // 0xecc9e4: DecompressPointer r1
    //     0xecc9e4: add             x1, x1, HEAP, lsl #32
    // 0xecc9e8: LoadField: r0 = r1->field_7
    //     0xecc9e8: ldur            w0, [x1, #7]
    // 0xecc9ec: DecompressPointer r0
    //     0xecc9ec: add             x0, x0, HEAP, lsl #32
    // 0xecc9f0: mov             x1, x2
    // 0xecc9f4: ArrayStore: r1[1] = r0  ; List_4
    //     0xecc9f4: add             x25, x1, #0x13
    //     0xecc9f8: str             w0, [x25]
    //     0xecc9fc: tbz             w0, #0, #0xecca18
    //     0xecca00: ldurb           w16, [x1, #-1]
    //     0xecca04: ldurb           w17, [x0, #-1]
    //     0xecca08: and             x16, x17, x16, lsr #2
    //     0xecca0c: tst             x16, HEAP, lsr #32
    //     0xecca10: b.eq            #0xecca18
    //     0xecca14: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xecca18: r16 = "topic"
    //     0xecca18: add             x16, PP, #0x12, lsl #12  ; [pp+0x124a8] "topic"
    //     0xecca1c: ldr             x16, [x16, #0x4a8]
    // 0xecca20: ArrayStore: r2[0] = r16  ; List_4
    //     0xecca20: stur            w16, [x2, #0x17]
    // 0xecca24: mov             x1, x2
    // 0xecca28: ldur            x0, [fp, #-0x60]
    // 0xecca2c: ArrayStore: r1[3] = r0  ; List_4
    //     0xecca2c: add             x25, x1, #0x1b
    //     0xecca30: str             w0, [x25]
    //     0xecca34: tbz             w0, #0, #0xecca50
    //     0xecca38: ldurb           w16, [x1, #-1]
    //     0xecca3c: ldurb           w17, [x0, #-1]
    //     0xecca40: and             x16, x17, x16, lsr #2
    //     0xecca44: tst             x16, HEAP, lsr #32
    //     0xecca48: b.eq            #0xecca50
    //     0xecca4c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xecca50: r16 = <String, String>
    //     0xecca50: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xecca54: ldr             x16, [x16, #0x668]
    // 0xecca58: stp             x2, x16, [SP]
    // 0xecca5c: r0 = Map._fromLiteral()
    //     0xecca5c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xecca60: r16 = Instance_MethodChannel
    //     0xecca60: add             x16, PP, #0x12, lsl #12  ; [pp+0x124b0] Obj!MethodChannel@e113d1
    //     0xecca64: ldr             x16, [x16, #0x4b0]
    // 0xecca68: stp             x16, NULL, [SP, #0x10]
    // 0xecca6c: r16 = "Messaging#subscribeToTopic"
    //     0xecca6c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12578] "Messaging#subscribeToTopic"
    //     0xecca70: ldr             x16, [x16, #0x578]
    // 0xecca74: stp             x0, x16, [SP]
    // 0xecca78: r4 = const [0x2, 0x3, 0x3, 0x3, null]
    //     0xecca78: ldr             x4, [PP, #0x3310]  ; [pp+0x3310] List(5) [0x2, 0x3, 0x3, 0x3, Null]
    // 0xecca7c: r0 = invokeMapMethod()
    //     0xecca7c: bl              #0x698d1c  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMapMethod
    // 0xecca80: mov             x1, x0
    // 0xecca84: stur            x1, [fp, #-0x58]
    // 0xecca88: r0 = Await()
    //     0xecca88: bl              #0x661044  ; AwaitStub
    // 0xecca8c: b               #0xeccaa0
    // 0xecca90: sub             SP, fp, #0x88
    // 0xecca94: mov             x2, x1
    // 0xecca98: mov             x1, x0
    // 0xecca9c: r0 = convertPlatformException()
    //     0xecca9c: bl              #0xecb7c8  ; [package:firebase_messaging_platform_interface/src/method_channel/utils/exception.dart] ::convertPlatformException
    // 0xeccaa0: r0 = Null
    //     0xeccaa0: mov             x0, NULL
    // 0xeccaa4: r0 = ReturnAsyncNotFuture()
    //     0xeccaa4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xeccaa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeccaa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeccaac: b               #0xecc980
  }
  get _ onTokenRefresh(/* No info */) {
    // ** addr: 0xeccae4, size: 0x64
    // 0xeccae4: EnterFrame
    //     0xeccae4: stp             fp, lr, [SP, #-0x10]!
    //     0xeccae8: mov             fp, SP
    // 0xeccaec: AllocStack(0x8)
    //     0xeccaec: sub             SP, SP, #8
    // 0xeccaf0: CheckStackOverflow
    //     0xeccaf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeccaf4: cmp             SP, x16
    //     0xeccaf8: b.ls            #0xeccb40
    // 0xeccafc: r0 = InitLateStaticField(0x1008) // [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] MethodChannelFirebaseMessaging::tokenStreamController
    //     0xeccafc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeccb00: ldr             x0, [x0, #0x2010]
    //     0xeccb04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeccb08: cmp             w0, w16
    //     0xeccb0c: b.ne            #0xeccb1c
    //     0xeccb10: add             x2, PP, #0x12, lsl #12  ; [pp+0x124f8] Field <MethodChannelFirebaseMessaging.tokenStreamController>: static late (offset: 0x1008)
    //     0xeccb14: ldr             x2, [x2, #0x4f8]
    //     0xeccb18: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xeccb1c: stur            x0, [fp, #-8]
    // 0xeccb20: LoadField: r1 = r0->field_7
    //     0xeccb20: ldur            w1, [x0, #7]
    // 0xeccb24: DecompressPointer r1
    //     0xeccb24: add             x1, x1, HEAP, lsl #32
    // 0xeccb28: r0 = _BroadcastStream()
    //     0xeccb28: bl              #0x836570  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0xeccb2c: ldur            x1, [fp, #-8]
    // 0xeccb30: StoreField: r0->field_b = r1
    //     0xeccb30: stur            w1, [x0, #0xb]
    // 0xeccb34: LeaveFrame
    //     0xeccb34: mov             SP, fp
    //     0xeccb38: ldp             fp, lr, [SP], #0x10
    // 0xeccb3c: ret
    //     0xeccb3c: ret             
    // 0xeccb40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeccb40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeccb44: b               #0xeccafc
  }
  _ getToken(/* No info */) async {
    // ** addr: 0xeccb7c, size: 0x154
    // 0xeccb7c: EnterFrame
    //     0xeccb7c: stp             fp, lr, [SP, #-0x10]!
    //     0xeccb80: mov             fp, SP
    // 0xeccb84: AllocStack(0x78)
    //     0xeccb84: sub             SP, SP, #0x78
    // 0xeccb88: SetupParameters(MethodChannelFirebaseMessaging this /* r1 => r1, fp-0x50 */)
    //     0xeccb88: stur            NULL, [fp, #-8]
    //     0xeccb8c: stur            x1, [fp, #-0x50]
    // 0xeccb90: CheckStackOverflow
    //     0xeccb90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeccb94: cmp             SP, x16
    //     0xeccb98: b.ls            #0xecccc4
    // 0xeccb9c: InitAsync() -> Future<String?>
    //     0xeccb9c: ldr             x0, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    //     0xeccba0: bl              #0x661298  ; InitAsyncStub
    // 0xeccba4: ldur            x1, [fp, #-0x50]
    // 0xeccba8: r0 = onOnlineModeFailure()
    //     0xeccba8: bl              #0xe35dcc  ; [package:nuonline/app/modules/doa/doa_detail/controllers/doa_detail_controller.dart] DoaDetailController::onOnlineModeFailure
    // 0xeccbac: mov             x1, x0
    // 0xeccbb0: stur            x1, [fp, #-0x58]
    // 0xeccbb4: r0 = Await()
    //     0xeccbb4: bl              #0x661044  ; AwaitStub
    // 0xeccbb8: ldur            x0, [fp, #-0x50]
    // 0xeccbbc: r1 = Null
    //     0xeccbbc: mov             x1, NULL
    // 0xeccbc0: r2 = 4
    //     0xeccbc0: movz            x2, #0x4
    // 0xeccbc4: r0 = AllocateArray()
    //     0xeccbc4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeccbc8: stur            x0, [fp, #-0x58]
    // 0xeccbcc: r16 = "appName"
    //     0xeccbcc: add             x16, PP, #0xf, lsl #12  ; [pp+0xf9c0] "appName"
    //     0xeccbd0: ldr             x16, [x16, #0x9c0]
    // 0xeccbd4: StoreField: r0->field_f = r16
    //     0xeccbd4: stur            w16, [x0, #0xf]
    // 0xeccbd8: ldur            x1, [fp, #-0x50]
    // 0xeccbdc: LoadField: r2 = r1->field_7
    //     0xeccbdc: ldur            w2, [x1, #7]
    // 0xeccbe0: DecompressPointer r2
    //     0xeccbe0: add             x2, x2, HEAP, lsl #32
    // 0xeccbe4: cmp             w2, NULL
    // 0xeccbe8: b.ne            #0xeccbf4
    // 0xeccbec: r0 = app()
    //     0xeccbec: bl              #0x90ffc4  ; [package:firebase_core/firebase_core.dart] Firebase::app
    // 0xeccbf0: b               #0xeccbf8
    // 0xeccbf4: mov             x0, x2
    // 0xeccbf8: LoadField: r1 = r0->field_7
    //     0xeccbf8: ldur            w1, [x0, #7]
    // 0xeccbfc: DecompressPointer r1
    //     0xeccbfc: add             x1, x1, HEAP, lsl #32
    // 0xeccc00: LoadField: r0 = r1->field_7
    //     0xeccc00: ldur            w0, [x1, #7]
    // 0xeccc04: DecompressPointer r0
    //     0xeccc04: add             x0, x0, HEAP, lsl #32
    // 0xeccc08: ldur            x1, [fp, #-0x58]
    // 0xeccc0c: ArrayStore: r1[1] = r0  ; List_4
    //     0xeccc0c: add             x25, x1, #0x13
    //     0xeccc10: str             w0, [x25]
    //     0xeccc14: tbz             w0, #0, #0xeccc30
    //     0xeccc18: ldurb           w16, [x1, #-1]
    //     0xeccc1c: ldurb           w17, [x0, #-1]
    //     0xeccc20: and             x16, x17, x16, lsr #2
    //     0xeccc24: tst             x16, HEAP, lsr #32
    //     0xeccc28: b.eq            #0xeccc30
    //     0xeccc2c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xeccc30: r16 = <String, String>
    //     0xeccc30: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xeccc34: ldr             x16, [x16, #0x668]
    // 0xeccc38: ldur            lr, [fp, #-0x58]
    // 0xeccc3c: stp             lr, x16, [SP]
    // 0xeccc40: r0 = Map._fromLiteral()
    //     0xeccc40: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xeccc44: r16 = <String, String>
    //     0xeccc44: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xeccc48: ldr             x16, [x16, #0x668]
    // 0xeccc4c: r30 = Instance_MethodChannel
    //     0xeccc4c: add             lr, PP, #0x12, lsl #12  ; [pp+0x124b0] Obj!MethodChannel@e113d1
    //     0xeccc50: ldr             lr, [lr, #0x4b0]
    // 0xeccc54: stp             lr, x16, [SP, #0x10]
    // 0xeccc58: r16 = "Messaging#getToken"
    //     0xeccc58: add             x16, PP, #0x12, lsl #12  ; [pp+0x12580] "Messaging#getToken"
    //     0xeccc5c: ldr             x16, [x16, #0x580]
    // 0xeccc60: stp             x0, x16, [SP]
    // 0xeccc64: r4 = const [0x2, 0x3, 0x3, 0x3, null]
    //     0xeccc64: ldr             x4, [PP, #0x3310]  ; [pp+0x3310] List(5) [0x2, 0x3, 0x3, 0x3, Null]
    // 0xeccc68: r0 = invokeMapMethod()
    //     0xeccc68: bl              #0x698d1c  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMapMethod
    // 0xeccc6c: mov             x1, x0
    // 0xeccc70: stur            x1, [fp, #-0x50]
    // 0xeccc74: r0 = Await()
    //     0xeccc74: bl              #0x661044  ; AwaitStub
    // 0xeccc78: cmp             w0, NULL
    // 0xeccc7c: b.eq            #0xeccccc
    // 0xeccc80: r1 = LoadClassIdInstr(r0)
    //     0xeccc80: ldur            x1, [x0, #-1]
    //     0xeccc84: ubfx            x1, x1, #0xc, #0x14
    // 0xeccc88: mov             x16, x0
    // 0xeccc8c: mov             x0, x1
    // 0xeccc90: mov             x1, x16
    // 0xeccc94: r2 = "token"
    //     0xeccc94: add             x2, PP, #0x12, lsl #12  ; [pp+0x12588] "token"
    //     0xeccc98: ldr             x2, [x2, #0x588]
    // 0xeccc9c: r0 = GDT[cid_x0 + -0x114]()
    //     0xeccc9c: sub             lr, x0, #0x114
    //     0xeccca0: ldr             lr, [x21, lr, lsl #3]
    //     0xeccca4: blr             lr
    // 0xeccca8: r0 = ReturnAsync()
    //     0xeccca8: b               #0x6576a4  ; ReturnAsyncStub
    // 0xecccac: sub             SP, fp, #0x78
    // 0xecccb0: mov             x2, x1
    // 0xecccb4: mov             x1, x0
    // 0xecccb8: r0 = convertPlatformException()
    //     0xecccb8: bl              #0xecb7c8  ; [package:firebase_messaging_platform_interface/src/method_channel/utils/exception.dart] ::convertPlatformException
    // 0xecccbc: r0 = Null
    //     0xecccbc: mov             x0, NULL
    // 0xecccc0: r0 = ReturnAsyncNotFuture()
    //     0xecccc0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xecccc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecccc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecccc8: b               #0xeccb9c
    // 0xeccccc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeccccc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ registerBackgroundMessageHandler(/* No info */) async {
    // ** addr: 0xeccdbc, size: 0x128
    // 0xeccdbc: EnterFrame
    //     0xeccdbc: stp             fp, lr, [SP, #-0x10]!
    //     0xeccdc0: mov             fp, SP
    // 0xeccdc4: AllocStack(0x38)
    //     0xeccdc4: sub             SP, SP, #0x38
    // 0xeccdc8: SetupParameters(MethodChannelFirebaseMessaging this /* r1 => r1, fp-0x10 */)
    //     0xeccdc8: stur            NULL, [fp, #-8]
    //     0xeccdcc: stur            x1, [fp, #-0x10]
    // 0xeccdd0: CheckStackOverflow
    //     0xeccdd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeccdd4: cmp             SP, x16
    //     0xeccdd8: b.ls            #0xecced4
    // 0xeccddc: InitAsync() -> Future<void?>
    //     0xeccddc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xeccde0: bl              #0x661298  ; InitAsyncStub
    // 0xeccde4: r0 = LoadStaticField(0x1004)
    //     0xeccde4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeccde8: ldr             x0, [x0, #0x2008]
    // 0xeccdec: tbz             w0, #4, #0xeccecc
    // 0xeccdf0: r0 = true
    //     0xeccdf0: add             x0, NULL, #0x20  ; true
    // 0xeccdf4: StoreStaticField(0x1004, r0)
    //     0xeccdf4: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xeccdf8: str             x0, [x1, #0x2008]
    // 0xeccdfc: r1 = Closure: () => void from Function '_firebaseMessagingCallbackDispatcher@1053184891': static.
    //     0xeccdfc: add             x1, PP, #0x12, lsl #12  ; [pp+0x12598] Closure: () => void from Function '_firebaseMessagingCallbackDispatcher@1053184891': static. (0x7e54fb8c62e8)
    //     0xecce00: ldr             x1, [x1, #0x598]
    // 0xecce04: r0 = getCallbackHandle()
    //     0xecce04: bl              #0x8373bc  ; [dart:ui] PluginUtilities::getCallbackHandle
    // 0xecce08: stur            x0, [fp, #-0x10]
    // 0xecce0c: cmp             w0, NULL
    // 0xecce10: b.eq            #0xeccedc
    // 0xecce14: r1 = Closure: (RemoteMessage) => Future<void> from Function '_firebaseMessagingBackgroundHandler@2539139854': static.
    //     0xecce14: add             x1, PP, #0x12, lsl #12  ; [pp+0x12590] Closure: (RemoteMessage) => Future<void> from Function '_firebaseMessagingBackgroundHandler@2539139854': static. (0x7e54fb8cbe6c)
    //     0xecce18: ldr             x1, [x1, #0x590]
    // 0xecce1c: r0 = getCallbackHandle()
    //     0xecce1c: bl              #0x8373bc  ; [dart:ui] PluginUtilities::getCallbackHandle
    // 0xecce20: stur            x0, [fp, #-0x18]
    // 0xecce24: cmp             w0, NULL
    // 0xecce28: b.eq            #0xeccee0
    // 0xecce2c: r1 = Null
    //     0xecce2c: mov             x1, NULL
    // 0xecce30: r2 = 8
    //     0xecce30: movz            x2, #0x8
    // 0xecce34: r0 = AllocateArray()
    //     0xecce34: bl              #0xec22fc  ; AllocateArrayStub
    // 0xecce38: mov             x2, x0
    // 0xecce3c: r16 = "pluginCallbackHandle"
    //     0xecce3c: add             x16, PP, #0x12, lsl #12  ; [pp+0x125a0] "pluginCallbackHandle"
    //     0xecce40: ldr             x16, [x16, #0x5a0]
    // 0xecce44: StoreField: r2->field_f = r16
    //     0xecce44: stur            w16, [x2, #0xf]
    // 0xecce48: ldur            x0, [fp, #-0x10]
    // 0xecce4c: LoadField: r3 = r0->field_7
    //     0xecce4c: ldur            x3, [x0, #7]
    // 0xecce50: r0 = BoxInt64Instr(r3)
    //     0xecce50: sbfiz           x0, x3, #1, #0x1f
    //     0xecce54: cmp             x3, x0, asr #1
    //     0xecce58: b.eq            #0xecce64
    //     0xecce5c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xecce60: stur            x3, [x0, #7]
    // 0xecce64: StoreField: r2->field_13 = r0
    //     0xecce64: stur            w0, [x2, #0x13]
    // 0xecce68: r16 = "userCallbackHandle"
    //     0xecce68: add             x16, PP, #0xc, lsl #12  ; [pp+0xc2f0] "userCallbackHandle"
    //     0xecce6c: ldr             x16, [x16, #0x2f0]
    // 0xecce70: ArrayStore: r2[0] = r16  ; List_4
    //     0xecce70: stur            w16, [x2, #0x17]
    // 0xecce74: ldur            x0, [fp, #-0x18]
    // 0xecce78: LoadField: r3 = r0->field_7
    //     0xecce78: ldur            x3, [x0, #7]
    // 0xecce7c: r0 = BoxInt64Instr(r3)
    //     0xecce7c: sbfiz           x0, x3, #1, #0x1f
    //     0xecce80: cmp             x3, x0, asr #1
    //     0xecce84: b.eq            #0xecce90
    //     0xecce88: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xecce8c: stur            x3, [x0, #7]
    // 0xecce90: StoreField: r2->field_1b = r0
    //     0xecce90: stur            w0, [x2, #0x1b]
    // 0xecce94: r16 = <String, int>
    //     0xecce94: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xecce98: stp             x2, x16, [SP]
    // 0xecce9c: r0 = Map._fromLiteral()
    //     0xecce9c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xeccea0: r16 = Instance_MethodChannel
    //     0xeccea0: add             x16, PP, #0x12, lsl #12  ; [pp+0x124b0] Obj!MethodChannel@e113d1
    //     0xeccea4: ldr             x16, [x16, #0x4b0]
    // 0xeccea8: stp             x16, NULL, [SP, #0x10]
    // 0xecceac: r16 = "Messaging#startBackgroundIsolate"
    //     0xecceac: add             x16, PP, #0x12, lsl #12  ; [pp+0x125a8] "Messaging#startBackgroundIsolate"
    //     0xecceb0: ldr             x16, [x16, #0x5a8]
    // 0xecceb4: stp             x0, x16, [SP]
    // 0xecceb8: r4 = const [0x2, 0x3, 0x3, 0x3, null]
    //     0xecceb8: ldr             x4, [PP, #0x3310]  ; [pp+0x3310] List(5) [0x2, 0x3, 0x3, 0x3, Null]
    // 0xeccebc: r0 = invokeMapMethod()
    //     0xeccebc: bl              #0x698d1c  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMapMethod
    // 0xeccec0: mov             x1, x0
    // 0xeccec4: stur            x1, [fp, #-0x10]
    // 0xeccec8: r0 = Await()
    //     0xeccec8: bl              #0x661044  ; AwaitStub
    // 0xeccecc: r0 = Null
    //     0xeccecc: mov             x0, NULL
    // 0xecced0: r0 = ReturnAsyncNotFuture()
    //     0xecced0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xecced4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecced4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecced8: b               #0xeccddc
    // 0xeccedc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeccedc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeccee0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeccee0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
