// lib: , url: package:firebase_messaging_platform_interface/src/platform_interface/platform_interface_messaging.dart

// class id: 1048759, size: 0x8
class :: {
}

// class id: 5934, size: 0xc, field offset: 0x8
abstract class FirebaseMessagingPlatform extends PlatformInterface {

  static late final Object _token; // offset: 0xfe8
  static late final StreamController<RemoteMessage> onMessage; // offset: 0xff0
  static late final StreamController<RemoteMessage> onMessageOpenedApp; // offset: 0xff4

  get _ app(/* No info */) {
    // ** addr: 0xecb800, size: 0x48
    // 0xecb800: EnterFrame
    //     0xecb800: stp             fp, lr, [SP, #-0x10]!
    //     0xecb804: mov             fp, SP
    // 0xecb808: CheckStackOverflow
    //     0xecb808: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecb80c: cmp             SP, x16
    //     0xecb810: b.ls            #0xecb840
    // 0xecb814: LoadField: r0 = r1->field_7
    //     0xecb814: ldur            w0, [x1, #7]
    // 0xecb818: DecompressPointer r0
    //     0xecb818: add             x0, x0, HEAP, lsl #32
    // 0xecb81c: cmp             w0, NULL
    // 0xecb820: b.ne            #0xecb834
    // 0xecb824: r0 = app()
    //     0xecb824: bl              #0x90ffc4  ; [package:firebase_core/firebase_core.dart] Firebase::app
    // 0xecb828: LeaveFrame
    //     0xecb828: mov             SP, fp
    //     0xecb82c: ldp             fp, lr, [SP], #0x10
    // 0xecb830: ret
    //     0xecb830: ret             
    // 0xecb834: LeaveFrame
    //     0xecb834: mov             SP, fp
    //     0xecb838: ldp             fp, lr, [SP], #0x10
    // 0xecb83c: ret
    //     0xecb83c: ret             
    // 0xecb840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecb840: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecb844: b               #0xecb814
  }
  factory _ FirebaseMessagingPlatform.instanceFor(/* No info */) {
    // ** addr: 0xecb8e0, size: 0xb8
    // 0xecb8e0: EnterFrame
    //     0xecb8e0: stp             fp, lr, [SP, #-0x10]!
    //     0xecb8e4: mov             fp, SP
    // 0xecb8e8: AllocStack(0x10)
    //     0xecb8e8: sub             SP, SP, #0x10
    // 0xecb8ec: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r1, fp-0x10 */)
    //     0xecb8ec: mov             x0, x1
    //     0xecb8f0: mov             x1, x3
    //     0xecb8f4: stur            x2, [fp, #-8]
    //     0xecb8f8: stur            x3, [fp, #-0x10]
    // 0xecb8fc: CheckStackOverflow
    //     0xecb8fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecb900: cmp             SP, x16
    //     0xecb904: b.ls            #0xecb990
    // 0xecb908: r0 = instance()
    //     0xecb908: bl              #0xecba3c  ; [package:firebase_messaging_platform_interface/src/platform_interface/platform_interface_messaging.dart] FirebaseMessagingPlatform::instance
    // 0xecb90c: mov             x1, x0
    // 0xecb910: ldur            x2, [fp, #-8]
    // 0xecb914: r0 = delegateFor()
    //     0xecb914: bl              #0xecb998  ; [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] MethodChannelFirebaseMessaging::delegateFor
    // 0xecb918: mov             x3, x0
    // 0xecb91c: ldur            x1, [fp, #-0x10]
    // 0xecb920: stur            x3, [fp, #-8]
    // 0xecb924: r0 = LoadClassIdInstr(r1)
    //     0xecb924: ldur            x0, [x1, #-1]
    //     0xecb928: ubfx            x0, x0, #0xc, #0x14
    // 0xecb92c: r2 = "AUTO_INIT_ENABLED"
    //     0xecb92c: add             x2, PP, #0x12, lsl #12  ; [pp+0x124c8] "AUTO_INIT_ENABLED"
    //     0xecb930: ldr             x2, [x2, #0x4c8]
    // 0xecb934: r0 = GDT[cid_x0 + -0x114]()
    //     0xecb934: sub             lr, x0, #0x114
    //     0xecb938: ldr             lr, [x21, lr, lsl #3]
    //     0xecb93c: blr             lr
    // 0xecb940: mov             x3, x0
    // 0xecb944: r2 = Null
    //     0xecb944: mov             x2, NULL
    // 0xecb948: r1 = Null
    //     0xecb948: mov             x1, NULL
    // 0xecb94c: stur            x3, [fp, #-0x10]
    // 0xecb950: r4 = 60
    //     0xecb950: movz            x4, #0x3c
    // 0xecb954: branchIfSmi(r0, 0xecb960)
    //     0xecb954: tbz             w0, #0, #0xecb960
    // 0xecb958: r4 = LoadClassIdInstr(r0)
    //     0xecb958: ldur            x4, [x0, #-1]
    //     0xecb95c: ubfx            x4, x4, #0xc, #0x14
    // 0xecb960: cmp             x4, #0x3f
    // 0xecb964: b.eq            #0xecb978
    // 0xecb968: r8 = bool?
    //     0xecb968: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0xecb96c: r3 = Null
    //     0xecb96c: add             x3, PP, #0x12, lsl #12  ; [pp+0x124d0] Null
    //     0xecb970: ldr             x3, [x3, #0x4d0]
    // 0xecb974: r0 = bool?()
    //     0xecb974: bl              #0x60b174  ; IsType_bool?_Stub
    // 0xecb978: ldur            x1, [fp, #-8]
    // 0xecb97c: ldur            x2, [fp, #-0x10]
    // 0xecb980: r0 = asUnmodifiableView()
    //     0xecb980: bl              #0xebb654  ; [dart:typed_data] _UnmodifiableByteDataView::asUnmodifiableView
    // 0xecb984: LeaveFrame
    //     0xecb984: mov             SP, fp
    //     0xecb988: ldp             fp, lr, [SP], #0x10
    // 0xecb98c: ret
    //     0xecb98c: ret             
    // 0xecb990: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecb990: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecb994: b               #0xecb908
  }
  get _ instance(/* No info */) {
    // ** addr: 0xecba3c, size: 0x58
    // 0xecba3c: EnterFrame
    //     0xecba3c: stp             fp, lr, [SP, #-0x10]!
    //     0xecba40: mov             fp, SP
    // 0xecba44: CheckStackOverflow
    //     0xecba44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecba48: cmp             SP, x16
    //     0xecba4c: b.ls            #0xecba8c
    // 0xecba50: r0 = LoadStaticField(0xfec)
    //     0xecba50: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xecba54: ldr             x0, [x0, #0x1fd8]
    // 0xecba58: cmp             w0, NULL
    // 0xecba5c: b.ne            #0xecba64
    // 0xecba60: r0 = setMethodCallHandlers()
    //     0xecba60: bl              #0xecbb20  ; [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] MethodChannelFirebaseMessaging::setMethodCallHandlers
    // 0xecba64: r0 = LoadStaticField(0xfec)
    //     0xecba64: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xecba68: ldr             x0, [x0, #0x1fd8]
    // 0xecba6c: cmp             w0, NULL
    // 0xecba70: b.ne            #0xecba80
    // 0xecba74: r0 = instance()
    //     0xecba74: bl              #0xecba94  ; [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] MethodChannelFirebaseMessaging::instance
    // 0xecba78: StoreStaticField(0xfec, r0)
    //     0xecba78: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xecba7c: str             x0, [x1, #0x1fd8]
    // 0xecba80: LeaveFrame
    //     0xecba80: mov             SP, fp
    //     0xecba84: ldp             fp, lr, [SP], #0x10
    // 0xecba88: ret
    //     0xecba88: ret             
    // 0xecba8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecba8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecba90: b               #0xecba50
  }
  static StreamController<RemoteMessage> onMessage() {
    // ** addr: 0xecc7e4, size: 0x38
    // 0xecc7e4: EnterFrame
    //     0xecc7e4: stp             fp, lr, [SP, #-0x10]!
    //     0xecc7e8: mov             fp, SP
    // 0xecc7ec: CheckStackOverflow
    //     0xecc7ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecc7f0: cmp             SP, x16
    //     0xecc7f4: b.ls            #0xecc814
    // 0xecc7f8: r1 = <RemoteMessage>
    //     0xecc7f8: add             x1, PP, #0x12, lsl #12  ; [pp+0x12568] TypeArguments: <RemoteMessage>
    //     0xecc7fc: ldr             x1, [x1, #0x568]
    // 0xecc800: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xecc800: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xecc804: r0 = StreamController.broadcast()
    //     0xecc804: bl              #0x83522c  ; [dart:async] StreamController::StreamController.broadcast
    // 0xecc808: LeaveFrame
    //     0xecc808: mov             SP, fp
    //     0xecc80c: ldp             fp, lr, [SP], #0x10
    // 0xecc810: ret
    //     0xecc810: ret             
    // 0xecc814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecc814: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecc818: b               #0xecc7f8
  }
  set _ onBackgroundMessage=(/* No info */) {
    // ** addr: 0xeccd6c, size: 0x50
    // 0xeccd6c: EnterFrame
    //     0xeccd6c: stp             fp, lr, [SP, #-0x10]!
    //     0xeccd70: mov             fp, SP
    // 0xeccd74: r2 = Closure: (RemoteMessage) => Future<void> from Function '_firebaseMessagingBackgroundHandler@2539139854': static.
    //     0xeccd74: add             x2, PP, #0x12, lsl #12  ; [pp+0x12590] Closure: (RemoteMessage) => Future<void> from Function '_firebaseMessagingBackgroundHandler@2539139854': static. (0x7e54fb8cbe6c)
    //     0xeccd78: ldr             x2, [x2, #0x590]
    // 0xeccd7c: CheckStackOverflow
    //     0xeccd7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeccd80: cmp             SP, x16
    //     0xeccd84: b.ls            #0xeccdb4
    // 0xeccd88: StoreStaticField(0xff8, r2)
    //     0xeccd88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeccd8c: str             x2, [x0, #0x1ff0]
    // 0xeccd90: r0 = instance()
    //     0xeccd90: bl              #0xecba3c  ; [package:firebase_messaging_platform_interface/src/platform_interface/platform_interface_messaging.dart] FirebaseMessagingPlatform::instance
    // 0xeccd94: mov             x1, x0
    // 0xeccd98: r2 = Closure: (RemoteMessage) => Future<void> from Function '_firebaseMessagingBackgroundHandler@2539139854': static.
    //     0xeccd98: add             x2, PP, #0x12, lsl #12  ; [pp+0x12590] Closure: (RemoteMessage) => Future<void> from Function '_firebaseMessagingBackgroundHandler@2539139854': static. (0x7e54fb8cbe6c)
    //     0xeccd9c: ldr             x2, [x2, #0x590]
    // 0xeccda0: r0 = registerBackgroundMessageHandler()
    //     0xeccda0: bl              #0xeccdbc  ; [package:firebase_messaging_platform_interface/src/method_channel/method_channel_messaging.dart] MethodChannelFirebaseMessaging::registerBackgroundMessageHandler
    // 0xeccda4: r0 = Null
    //     0xeccda4: mov             x0, NULL
    // 0xeccda8: LeaveFrame
    //     0xeccda8: mov             SP, fp
    //     0xeccdac: ldp             fp, lr, [SP], #0x10
    // 0xeccdb0: ret
    //     0xeccdb0: ret             
    // 0xeccdb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeccdb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeccdb8: b               #0xeccd88
  }
}
