// lib: , url: package:flutter/src/cupertino/text_selection_toolbar.dart

// class id: 1048798, size: 0x8
class :: {
}

// class id: 3067, size: 0x68, field offset: 0x68
//   transformed mixin,
abstract class __RenderCupertinoTextSelectionToolbarItems&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin extends __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin
     with RenderBoxContainerDefaultsMixin<X0 bound RenderBox, X1 bound ContainerBoxParentData> {
}

// class id: 3068, size: 0x90, field offset: 0x68
class _RenderCupertinoTextSelectionToolbarItems extends __RenderCupertinoTextSelectionToolbarItems&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin {

  late bool hasNextPage; // offset: 0x6c
  late bool hasPreviousPage; // offset: 0x70

  _ attach(/* No info */) {
    // ** addr: 0x761be4, size: 0x154
    // 0x761be4: EnterFrame
    //     0x761be4: stp             fp, lr, [SP, #-0x10]!
    //     0x761be8: mov             fp, SP
    // 0x761bec: AllocStack(0x20)
    //     0x761bec: sub             SP, SP, #0x20
    // 0x761bf0: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x761bf0: mov             x3, x1
    //     0x761bf4: mov             x0, x2
    //     0x761bf8: stur            x1, [fp, #-8]
    //     0x761bfc: stur            x2, [fp, #-0x10]
    // 0x761c00: CheckStackOverflow
    //     0x761c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x761c04: cmp             SP, x16
    //     0x761c08: b.ls            #0x761d28
    // 0x761c0c: mov             x1, x3
    // 0x761c10: mov             x2, x0
    // 0x761c14: r0 = attach()
    //     0x761c14: bl              #0x761d38  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::attach
    // 0x761c18: ldur            x0, [fp, #-8]
    // 0x761c1c: LoadField: r4 = r0->field_67
    //     0x761c1c: ldur            w4, [x0, #0x67]
    // 0x761c20: DecompressPointer r4
    //     0x761c20: add             x4, x4, HEAP, lsl #32
    // 0x761c24: stur            x4, [fp, #-0x18]
    // 0x761c28: LoadField: r2 = r4->field_7
    //     0x761c28: ldur            w2, [x4, #7]
    // 0x761c2c: DecompressPointer r2
    //     0x761c2c: add             x2, x2, HEAP, lsl #32
    // 0x761c30: r1 = Null
    //     0x761c30: mov             x1, NULL
    // 0x761c34: r3 = <X1>
    //     0x761c34: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x761c38: r0 = Null
    //     0x761c38: mov             x0, NULL
    // 0x761c3c: cmp             x2, x0
    // 0x761c40: b.eq            #0x761c50
    // 0x761c44: r30 = InstantiateTypeArgumentsStub
    //     0x761c44: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x761c48: LoadField: r30 = r30->field_7
    //     0x761c48: ldur            lr, [lr, #7]
    // 0x761c4c: blr             lr
    // 0x761c50: mov             x1, x0
    // 0x761c54: r0 = _CompactIterable()
    //     0x761c54: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x761c58: mov             x1, x0
    // 0x761c5c: ldur            x0, [fp, #-0x18]
    // 0x761c60: StoreField: r1->field_b = r0
    //     0x761c60: stur            w0, [x1, #0xb]
    // 0x761c64: r0 = -1
    //     0x761c64: movn            x0, #0
    // 0x761c68: StoreField: r1->field_f = r0
    //     0x761c68: stur            x0, [x1, #0xf]
    // 0x761c6c: r0 = 2
    //     0x761c6c: movz            x0, #0x2
    // 0x761c70: ArrayStore: r1[0] = r0  ; List_8
    //     0x761c70: stur            x0, [x1, #0x17]
    // 0x761c74: r0 = iterator()
    //     0x761c74: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x761c78: stur            x0, [fp, #-0x18]
    // 0x761c7c: LoadField: r2 = r0->field_7
    //     0x761c7c: ldur            w2, [x0, #7]
    // 0x761c80: DecompressPointer r2
    //     0x761c80: add             x2, x2, HEAP, lsl #32
    // 0x761c84: stur            x2, [fp, #-8]
    // 0x761c88: CheckStackOverflow
    //     0x761c88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x761c8c: cmp             SP, x16
    //     0x761c90: b.ls            #0x761d30
    // 0x761c94: mov             x1, x0
    // 0x761c98: r0 = moveNext()
    //     0x761c98: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x761c9c: tbnz            w0, #4, #0x761d18
    // 0x761ca0: ldur            x3, [fp, #-0x18]
    // 0x761ca4: LoadField: r4 = r3->field_33
    //     0x761ca4: ldur            w4, [x3, #0x33]
    // 0x761ca8: DecompressPointer r4
    //     0x761ca8: add             x4, x4, HEAP, lsl #32
    // 0x761cac: stur            x4, [fp, #-0x20]
    // 0x761cb0: cmp             w4, NULL
    // 0x761cb4: b.ne            #0x761ce8
    // 0x761cb8: mov             x0, x4
    // 0x761cbc: ldur            x2, [fp, #-8]
    // 0x761cc0: r1 = Null
    //     0x761cc0: mov             x1, NULL
    // 0x761cc4: cmp             w2, NULL
    // 0x761cc8: b.eq            #0x761ce8
    // 0x761ccc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x761ccc: ldur            w4, [x2, #0x17]
    // 0x761cd0: DecompressPointer r4
    //     0x761cd0: add             x4, x4, HEAP, lsl #32
    // 0x761cd4: r8 = X0
    //     0x761cd4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x761cd8: LoadField: r9 = r4->field_7
    //     0x761cd8: ldur            x9, [x4, #7]
    // 0x761cdc: r3 = Null
    //     0x761cdc: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d0a8] Null
    //     0x761ce0: ldr             x3, [x3, #0xa8]
    // 0x761ce4: blr             x9
    // 0x761ce8: ldur            x1, [fp, #-0x20]
    // 0x761cec: r0 = LoadClassIdInstr(r1)
    //     0x761cec: ldur            x0, [x1, #-1]
    //     0x761cf0: ubfx            x0, x0, #0xc, #0x14
    // 0x761cf4: ldur            x2, [fp, #-0x10]
    // 0x761cf8: r0 = GDT[cid_x0 + 0x11974]()
    //     0x761cf8: movz            x17, #0x1974
    //     0x761cfc: movk            x17, #0x1, lsl #16
    //     0x761d00: add             lr, x0, x17
    //     0x761d04: ldr             lr, [x21, lr, lsl #3]
    //     0x761d08: blr             lr
    // 0x761d0c: ldur            x0, [fp, #-0x18]
    // 0x761d10: ldur            x2, [fp, #-8]
    // 0x761d14: b               #0x761c88
    // 0x761d18: r0 = Null
    //     0x761d18: mov             x0, NULL
    // 0x761d1c: LeaveFrame
    //     0x761d1c: mov             SP, fp
    //     0x761d20: ldp             fp, lr, [SP], #0x10
    // 0x761d24: ret
    //     0x761d24: ret             
    // 0x761d28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x761d28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x761d2c: b               #0x761c0c
    // 0x761d30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x761d30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x761d34: b               #0x761c94
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x76b908, size: 0x760
    // 0x76b908: EnterFrame
    //     0x76b908: stp             fp, lr, [SP, #-0x10]!
    //     0x76b90c: mov             fp, SP
    // 0x76b910: AllocStack(0x40)
    //     0x76b910: sub             SP, SP, #0x40
    // 0x76b914: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r1, fp-0x8 */)
    //     0x76b914: stur            x1, [fp, #-8]
    // 0x76b918: CheckStackOverflow
    //     0x76b918: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76b91c: cmp             SP, x16
    //     0x76b920: b.ls            #0x76c004
    // 0x76b924: r1 = 7
    //     0x76b924: movz            x1, #0x7
    // 0x76b928: r0 = AllocateContext()
    //     0x76b928: bl              #0xec126c  ; AllocateContextStub
    // 0x76b92c: ldur            x3, [fp, #-8]
    // 0x76b930: stur            x0, [fp, #-0x18]
    // 0x76b934: StoreField: r0->field_f = r3
    //     0x76b934: stur            w3, [x0, #0xf]
    // 0x76b938: LoadField: r1 = r3->field_5f
    //     0x76b938: ldur            w1, [x3, #0x5f]
    // 0x76b93c: DecompressPointer r1
    //     0x76b93c: add             x1, x1, HEAP, lsl #32
    // 0x76b940: cmp             w1, NULL
    // 0x76b944: b.ne            #0x76b9c4
    // 0x76b948: LoadField: r4 = r3->field_27
    //     0x76b948: ldur            w4, [x3, #0x27]
    // 0x76b94c: DecompressPointer r4
    //     0x76b94c: add             x4, x4, HEAP, lsl #32
    // 0x76b950: stur            x4, [fp, #-0x10]
    // 0x76b954: cmp             w4, NULL
    // 0x76b958: b.eq            #0x76bfa8
    // 0x76b95c: mov             x0, x4
    // 0x76b960: r2 = Null
    //     0x76b960: mov             x2, NULL
    // 0x76b964: r1 = Null
    //     0x76b964: mov             x1, NULL
    // 0x76b968: r4 = LoadClassIdInstr(r0)
    //     0x76b968: ldur            x4, [x0, #-1]
    //     0x76b96c: ubfx            x4, x4, #0xc, #0x14
    // 0x76b970: sub             x4, x4, #0xc83
    // 0x76b974: cmp             x4, #1
    // 0x76b978: b.ls            #0x76b98c
    // 0x76b97c: r8 = BoxConstraints
    //     0x76b97c: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76b980: r3 = Null
    //     0x76b980: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d118] Null
    //     0x76b984: ldr             x3, [x3, #0x118]
    // 0x76b988: r0 = BoxConstraints()
    //     0x76b988: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76b98c: ldur            x1, [fp, #-0x10]
    // 0x76b990: r0 = smallest()
    //     0x76b990: bl              #0x7335c0  ; [package:flutter/src/rendering/box.dart] BoxConstraints::smallest
    // 0x76b994: ldur            x3, [fp, #-8]
    // 0x76b998: StoreField: r3->field_53 = r0
    //     0x76b998: stur            w0, [x3, #0x53]
    //     0x76b99c: ldurb           w16, [x3, #-1]
    //     0x76b9a0: ldurb           w17, [x0, #-1]
    //     0x76b9a4: and             x16, x17, x16, lsr #2
    //     0x76b9a8: tst             x16, HEAP, lsr #32
    //     0x76b9ac: b.eq            #0x76b9b4
    //     0x76b9b0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x76b9b4: r0 = Null
    //     0x76b9b4: mov             x0, NULL
    // 0x76b9b8: LeaveFrame
    //     0x76b9b8: mov             SP, fp
    //     0x76b9bc: ldp             fp, lr, [SP], #0x10
    // 0x76b9c0: ret
    //     0x76b9c0: ret             
    // 0x76b9c4: r4 = 0.000000
    //     0x76b9c4: ldr             x4, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x76b9c8: StoreField: r0->field_13 = r4
    //     0x76b9c8: stur            w4, [x0, #0x13]
    // 0x76b9cc: mov             x2, x0
    // 0x76b9d0: r1 = Function '<anonymous closure>':.
    //     0x76b9d0: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d128] AnonymousClosure: (0x76c730), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::performLayout (0x76b908)
    //     0x76b9d4: ldr             x1, [x1, #0x128]
    // 0x76b9d8: r0 = AllocateClosure()
    //     0x76b9d8: bl              #0xec1630  ; AllocateClosureStub
    // 0x76b9dc: ldur            x1, [fp, #-8]
    // 0x76b9e0: mov             x2, x0
    // 0x76b9e4: r0 = visitChildren()
    //     0x76b9e4: bl              #0x7867e8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::visitChildren
    // 0x76b9e8: ldur            x3, [fp, #-8]
    // 0x76b9ec: LoadField: r4 = r3->field_27
    //     0x76b9ec: ldur            w4, [x3, #0x27]
    // 0x76b9f0: DecompressPointer r4
    //     0x76b9f0: add             x4, x4, HEAP, lsl #32
    // 0x76b9f4: stur            x4, [fp, #-0x10]
    // 0x76b9f8: cmp             w4, NULL
    // 0x76b9fc: b.eq            #0x76bfc4
    // 0x76ba00: ldur            x5, [fp, #-0x18]
    // 0x76ba04: mov             x0, x4
    // 0x76ba08: r2 = Null
    //     0x76ba08: mov             x2, NULL
    // 0x76ba0c: r1 = Null
    //     0x76ba0c: mov             x1, NULL
    // 0x76ba10: r4 = LoadClassIdInstr(r0)
    //     0x76ba10: ldur            x4, [x0, #-1]
    //     0x76ba14: ubfx            x4, x4, #0xc, #0x14
    // 0x76ba18: sub             x4, x4, #0xc83
    // 0x76ba1c: cmp             x4, #1
    // 0x76ba20: b.ls            #0x76ba34
    // 0x76ba24: r8 = BoxConstraints
    //     0x76ba24: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76ba28: r3 = Null
    //     0x76ba28: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d130] Null
    //     0x76ba2c: ldr             x3, [x3, #0x130]
    // 0x76ba30: r0 = BoxConstraints()
    //     0x76ba30: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76ba34: ldur            x0, [fp, #-0x10]
    // 0x76ba38: LoadField: d0 = r0->field_f
    //     0x76ba38: ldur            d0, [x0, #0xf]
    // 0x76ba3c: ldur            x2, [fp, #-0x18]
    // 0x76ba40: stur            d0, [fp, #-0x38]
    // 0x76ba44: LoadField: r0 = r2->field_13
    //     0x76ba44: ldur            w0, [x2, #0x13]
    // 0x76ba48: DecompressPointer r0
    //     0x76ba48: add             x0, x0, HEAP, lsl #32
    // 0x76ba4c: stur            x0, [fp, #-0x10]
    // 0x76ba50: r0 = BoxConstraints()
    //     0x76ba50: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x76ba54: mov             x3, x0
    // 0x76ba58: stur            x3, [fp, #-0x20]
    // 0x76ba5c: StoreField: r3->field_7 = rZR
    //     0x76ba5c: stur            xzr, [x3, #7]
    // 0x76ba60: ldur            d0, [fp, #-0x38]
    // 0x76ba64: StoreField: r3->field_f = d0
    //     0x76ba64: stur            d0, [x3, #0xf]
    // 0x76ba68: ldur            x0, [fp, #-0x10]
    // 0x76ba6c: LoadField: d0 = r0->field_7
    //     0x76ba6c: ldur            d0, [x0, #7]
    // 0x76ba70: ArrayStore: r3[0] = d0  ; List_8
    //     0x76ba70: stur            d0, [x3, #0x17]
    // 0x76ba74: StoreField: r3->field_1f = d0
    //     0x76ba74: stur            d0, [x3, #0x1f]
    // 0x76ba78: ldur            x4, [fp, #-8]
    // 0x76ba7c: LoadField: r1 = r4->field_87
    //     0x76ba7c: ldur            w1, [x4, #0x87]
    // 0x76ba80: DecompressPointer r1
    //     0x76ba80: add             x1, x1, HEAP, lsl #32
    // 0x76ba84: cmp             w1, NULL
    // 0x76ba88: b.eq            #0x76c00c
    // 0x76ba8c: r0 = LoadClassIdInstr(r1)
    //     0x76ba8c: ldur            x0, [x1, #-1]
    //     0x76ba90: ubfx            x0, x0, #0xc, #0x14
    // 0x76ba94: r16 = true
    //     0x76ba94: add             x16, NULL, #0x20  ; true
    // 0x76ba98: str             x16, [SP]
    // 0x76ba9c: mov             x2, x3
    // 0x76baa0: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x76baa0: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x76baa4: ldr             x4, [x4, #0x5c0]
    // 0x76baa8: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x76baa8: movz            x17, #0xed1d
    //     0x76baac: add             lr, x0, x17
    //     0x76bab0: ldr             lr, [x21, lr, lsl #3]
    //     0x76bab4: blr             lr
    // 0x76bab8: ldur            x3, [fp, #-8]
    // 0x76babc: LoadField: r1 = r3->field_8b
    //     0x76babc: ldur            w1, [x3, #0x8b]
    // 0x76bac0: DecompressPointer r1
    //     0x76bac0: add             x1, x1, HEAP, lsl #32
    // 0x76bac4: cmp             w1, NULL
    // 0x76bac8: b.eq            #0x76c010
    // 0x76bacc: r0 = LoadClassIdInstr(r1)
    //     0x76bacc: ldur            x0, [x1, #-1]
    //     0x76bad0: ubfx            x0, x0, #0xc, #0x14
    // 0x76bad4: r16 = true
    //     0x76bad4: add             x16, NULL, #0x20  ; true
    // 0x76bad8: str             x16, [SP]
    // 0x76badc: ldur            x2, [fp, #-0x20]
    // 0x76bae0: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x76bae0: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x76bae4: ldr             x4, [x4, #0x5c0]
    // 0x76bae8: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x76bae8: movz            x17, #0xed1d
    //     0x76baec: add             lr, x0, x17
    //     0x76baf0: ldr             lr, [x21, lr, lsl #3]
    //     0x76baf4: blr             lr
    // 0x76baf8: ldur            x0, [fp, #-8]
    // 0x76bafc: LoadField: r1 = r0->field_87
    //     0x76bafc: ldur            w1, [x0, #0x87]
    // 0x76bb00: DecompressPointer r1
    //     0x76bb00: add             x1, x1, HEAP, lsl #32
    // 0x76bb04: cmp             w1, NULL
    // 0x76bb08: b.eq            #0x76c014
    // 0x76bb0c: r0 = size()
    //     0x76bb0c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76bb10: LoadField: d0 = r0->field_7
    //     0x76bb10: ldur            d0, [x0, #7]
    // 0x76bb14: ldur            x0, [fp, #-8]
    // 0x76bb18: stur            d0, [fp, #-0x38]
    // 0x76bb1c: LoadField: r1 = r0->field_8b
    //     0x76bb1c: ldur            w1, [x0, #0x8b]
    // 0x76bb20: DecompressPointer r1
    //     0x76bb20: add             x1, x1, HEAP, lsl #32
    // 0x76bb24: cmp             w1, NULL
    // 0x76bb28: b.eq            #0x76c018
    // 0x76bb2c: r0 = size()
    //     0x76bb2c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76bb30: LoadField: d0 = r0->field_7
    //     0x76bb30: ldur            d0, [x0, #7]
    // 0x76bb34: ldur            d1, [fp, #-0x38]
    // 0x76bb38: fadd            d2, d1, d0
    // 0x76bb3c: r0 = inline_Allocate_Double()
    //     0x76bb3c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x76bb40: add             x0, x0, #0x10
    //     0x76bb44: cmp             x1, x0
    //     0x76bb48: b.ls            #0x76c01c
    //     0x76bb4c: str             x0, [THR, #0x50]  ; THR::top
    //     0x76bb50: sub             x0, x0, #0xf
    //     0x76bb54: movz            x1, #0xe15c
    //     0x76bb58: movk            x1, #0x3, lsl #16
    //     0x76bb5c: stur            x1, [x0, #-1]
    // 0x76bb60: StoreField: r0->field_7 = d2
    //     0x76bb60: stur            d2, [x0, #7]
    // 0x76bb64: ldur            x3, [fp, #-0x18]
    // 0x76bb68: ArrayStore: r3[0] = r0  ; List_4
    //     0x76bb68: stur            w0, [x3, #0x17]
    //     0x76bb6c: ldurb           w16, [x3, #-1]
    //     0x76bb70: ldurb           w17, [x0, #-1]
    //     0x76bb74: and             x16, x17, x16, lsr #2
    //     0x76bb78: tst             x16, HEAP, lsr #32
    //     0x76bb7c: b.eq            #0x76bb84
    //     0x76bb80: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x76bb84: r0 = 0.000000
    //     0x76bb84: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x76bb88: StoreField: r3->field_1b = r0
    //     0x76bb88: stur            w0, [x3, #0x1b]
    // 0x76bb8c: r0 = Sentinel
    //     0x76bb8c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x76bb90: StoreField: r3->field_1f = r0
    //     0x76bb90: stur            w0, [x3, #0x1f]
    // 0x76bb94: StoreField: r3->field_23 = rZR
    //     0x76bb94: stur            wzr, [x3, #0x23]
    // 0x76bb98: r0 = -2
    //     0x76bb98: orr             x0, xzr, #0xfffffffffffffffe
    // 0x76bb9c: StoreField: r3->field_27 = r0
    //     0x76bb9c: stur            w0, [x3, #0x27]
    // 0x76bba0: mov             x2, x3
    // 0x76bba4: r1 = Function '<anonymous closure>':.
    //     0x76bba4: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d140] AnonymousClosure: (0x76c068), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::performLayout (0x76b908)
    //     0x76bba8: ldr             x1, [x1, #0x140]
    // 0x76bbac: r0 = AllocateClosure()
    //     0x76bbac: bl              #0xec1630  ; AllocateClosureStub
    // 0x76bbb0: ldur            x1, [fp, #-8]
    // 0x76bbb4: mov             x2, x0
    // 0x76bbb8: r0 = visitChildren()
    //     0x76bbb8: bl              #0x7867e8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::visitChildren
    // 0x76bbbc: ldur            x3, [fp, #-0x18]
    // 0x76bbc0: LoadField: r0 = r3->field_23
    //     0x76bbc0: ldur            w0, [x3, #0x23]
    // 0x76bbc4: DecompressPointer r0
    //     0x76bbc4: add             x0, x0, HEAP, lsl #32
    // 0x76bbc8: r4 = LoadInt32Instr(r0)
    //     0x76bbc8: sbfx            x4, x0, #1, #0x1f
    //     0x76bbcc: tbz             w0, #0, #0x76bbd4
    //     0x76bbd0: ldur            x4, [x0, #7]
    // 0x76bbd4: stur            x4, [fp, #-0x28]
    // 0x76bbd8: cmp             x4, #0
    // 0x76bbdc: b.le            #0x76bdf8
    // 0x76bbe0: ldur            x5, [fp, #-8]
    // 0x76bbe4: LoadField: r0 = r5->field_8b
    //     0x76bbe4: ldur            w0, [x5, #0x8b]
    // 0x76bbe8: DecompressPointer r0
    //     0x76bbe8: add             x0, x0, HEAP, lsl #32
    // 0x76bbec: cmp             w0, NULL
    // 0x76bbf0: b.eq            #0x76c02c
    // 0x76bbf4: LoadField: r6 = r0->field_7
    //     0x76bbf4: ldur            w6, [x0, #7]
    // 0x76bbf8: DecompressPointer r6
    //     0x76bbf8: add             x6, x6, HEAP, lsl #32
    // 0x76bbfc: stur            x6, [fp, #-0x10]
    // 0x76bc00: cmp             w6, NULL
    // 0x76bc04: b.eq            #0x76c030
    // 0x76bc08: mov             x0, x6
    // 0x76bc0c: r2 = Null
    //     0x76bc0c: mov             x2, NULL
    // 0x76bc10: r1 = Null
    //     0x76bc10: mov             x1, NULL
    // 0x76bc14: r4 = LoadClassIdInstr(r0)
    //     0x76bc14: ldur            x4, [x0, #-1]
    //     0x76bc18: ubfx            x4, x4, #0xc, #0x14
    // 0x76bc1c: cmp             x4, #0xc77
    // 0x76bc20: b.eq            #0x76bc38
    // 0x76bc24: r8 = ToolbarItemsParentData
    //     0x76bc24: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x76bc28: ldr             x8, [x8, #0x490]
    // 0x76bc2c: r3 = Null
    //     0x76bc2c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d148] Null
    //     0x76bc30: ldr             x3, [x3, #0x148]
    // 0x76bc34: r0 = DefaultTypeTest()
    //     0x76bc34: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x76bc38: ldur            x3, [fp, #-8]
    // 0x76bc3c: LoadField: r0 = r3->field_87
    //     0x76bc3c: ldur            w0, [x3, #0x87]
    // 0x76bc40: DecompressPointer r0
    //     0x76bc40: add             x0, x0, HEAP, lsl #32
    // 0x76bc44: cmp             w0, NULL
    // 0x76bc48: b.eq            #0x76c034
    // 0x76bc4c: LoadField: r4 = r0->field_7
    //     0x76bc4c: ldur            w4, [x0, #7]
    // 0x76bc50: DecompressPointer r4
    //     0x76bc50: add             x4, x4, HEAP, lsl #32
    // 0x76bc54: stur            x4, [fp, #-0x20]
    // 0x76bc58: cmp             w4, NULL
    // 0x76bc5c: b.eq            #0x76c038
    // 0x76bc60: mov             x0, x4
    // 0x76bc64: r2 = Null
    //     0x76bc64: mov             x2, NULL
    // 0x76bc68: r1 = Null
    //     0x76bc68: mov             x1, NULL
    // 0x76bc6c: r4 = LoadClassIdInstr(r0)
    //     0x76bc6c: ldur            x4, [x0, #-1]
    //     0x76bc70: ubfx            x4, x4, #0xc, #0x14
    // 0x76bc74: cmp             x4, #0xc77
    // 0x76bc78: b.eq            #0x76bc90
    // 0x76bc7c: r8 = ToolbarItemsParentData
    //     0x76bc7c: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x76bc80: ldr             x8, [x8, #0x490]
    // 0x76bc84: r3 = Null
    //     0x76bc84: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d158] Null
    //     0x76bc88: ldr             x3, [x3, #0x158]
    // 0x76bc8c: r0 = DefaultTypeTest()
    //     0x76bc8c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x76bc90: ldur            x0, [fp, #-8]
    // 0x76bc94: LoadField: r1 = r0->field_73
    //     0x76bc94: ldur            x1, [x0, #0x73]
    // 0x76bc98: ldur            x2, [fp, #-0x28]
    // 0x76bc9c: cmp             x1, x2
    // 0x76bca0: b.eq            #0x76bdc4
    // 0x76bca4: ldur            x1, [fp, #-0x18]
    // 0x76bca8: LoadField: r2 = r1->field_1f
    //     0x76bca8: ldur            w2, [x1, #0x1f]
    // 0x76bcac: DecompressPointer r2
    //     0x76bcac: add             x2, x2, HEAP, lsl #32
    // 0x76bcb0: r16 = Sentinel
    //     0x76bcb0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x76bcb4: cmp             w2, w16
    // 0x76bcb8: b.ne            #0x76bccc
    // 0x76bcbc: r16 = "toolbarWidth"
    //     0x76bcbc: add             x16, PP, #0x5d, lsl #12  ; [pp+0x5d168] "toolbarWidth"
    //     0x76bcc0: ldr             x16, [x16, #0x168]
    // 0x76bcc4: str             x16, [SP]
    // 0x76bcc8: r0 = _throwLocalNotInitialized()
    //     0x76bcc8: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x76bccc: ldur            x0, [fp, #-0x18]
    // 0x76bcd0: ldur            x1, [fp, #-0x10]
    // 0x76bcd4: LoadField: r2 = r0->field_1f
    //     0x76bcd4: ldur            w2, [x0, #0x1f]
    // 0x76bcd8: DecompressPointer r2
    //     0x76bcd8: add             x2, x2, HEAP, lsl #32
    // 0x76bcdc: stur            x2, [fp, #-0x30]
    // 0x76bce0: LoadField: d0 = r2->field_7
    //     0x76bce0: ldur            d0, [x2, #7]
    // 0x76bce4: stur            d0, [fp, #-0x38]
    // 0x76bce8: r0 = Offset()
    //     0x76bce8: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x76bcec: ldur            d0, [fp, #-0x38]
    // 0x76bcf0: StoreField: r0->field_7 = d0
    //     0x76bcf0: stur            d0, [x0, #7]
    // 0x76bcf4: StoreField: r0->field_f = rZR
    //     0x76bcf4: stur            xzr, [x0, #0xf]
    // 0x76bcf8: ldur            x1, [fp, #-0x10]
    // 0x76bcfc: StoreField: r1->field_7 = r0
    //     0x76bcfc: stur            w0, [x1, #7]
    //     0x76bd00: ldurb           w16, [x1, #-1]
    //     0x76bd04: ldurb           w17, [x0, #-1]
    //     0x76bd08: and             x16, x17, x16, lsr #2
    //     0x76bd0c: tst             x16, HEAP, lsr #32
    //     0x76bd10: b.eq            #0x76bd18
    //     0x76bd14: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76bd18: r0 = true
    //     0x76bd18: add             x0, NULL, #0x20  ; true
    // 0x76bd1c: ArrayStore: r1[0] = r0  ; List_4
    //     0x76bd1c: stur            w0, [x1, #0x17]
    // 0x76bd20: ldur            x1, [fp, #-0x30]
    // 0x76bd24: r16 = Sentinel
    //     0x76bd24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x76bd28: cmp             w1, w16
    // 0x76bd2c: b.ne            #0x76bd40
    // 0x76bd30: r16 = "toolbarWidth"
    //     0x76bd30: add             x16, PP, #0x5d, lsl #12  ; [pp+0x5d168] "toolbarWidth"
    //     0x76bd34: ldr             x16, [x16, #0x168]
    // 0x76bd38: str             x16, [SP]
    // 0x76bd3c: r0 = _throwLocalNotInitialized()
    //     0x76bd3c: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x76bd40: ldur            x2, [fp, #-8]
    // 0x76bd44: ldur            x0, [fp, #-0x18]
    // 0x76bd48: LoadField: r3 = r0->field_1f
    //     0x76bd48: ldur            w3, [x0, #0x1f]
    // 0x76bd4c: DecompressPointer r3
    //     0x76bd4c: add             x3, x3, HEAP, lsl #32
    // 0x76bd50: stur            x3, [fp, #-0x10]
    // 0x76bd54: LoadField: r1 = r2->field_8b
    //     0x76bd54: ldur            w1, [x2, #0x8b]
    // 0x76bd58: DecompressPointer r1
    //     0x76bd58: add             x1, x1, HEAP, lsl #32
    // 0x76bd5c: cmp             w1, NULL
    // 0x76bd60: b.eq            #0x76c03c
    // 0x76bd64: r0 = size()
    //     0x76bd64: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76bd68: LoadField: d0 = r0->field_7
    //     0x76bd68: ldur            d0, [x0, #7]
    // 0x76bd6c: ldur            x0, [fp, #-0x10]
    // 0x76bd70: LoadField: d1 = r0->field_7
    //     0x76bd70: ldur            d1, [x0, #7]
    // 0x76bd74: fadd            d2, d1, d0
    // 0x76bd78: r0 = inline_Allocate_Double()
    //     0x76bd78: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x76bd7c: add             x0, x0, #0x10
    //     0x76bd80: cmp             x1, x0
    //     0x76bd84: b.ls            #0x76c040
    //     0x76bd88: str             x0, [THR, #0x50]  ; THR::top
    //     0x76bd8c: sub             x0, x0, #0xf
    //     0x76bd90: movz            x1, #0xe15c
    //     0x76bd94: movk            x1, #0x3, lsl #16
    //     0x76bd98: stur            x1, [x0, #-1]
    // 0x76bd9c: StoreField: r0->field_7 = d2
    //     0x76bd9c: stur            d2, [x0, #7]
    // 0x76bda0: ldur            x1, [fp, #-0x18]
    // 0x76bda4: StoreField: r1->field_1f = r0
    //     0x76bda4: stur            w0, [x1, #0x1f]
    //     0x76bda8: ldurb           w16, [x1, #-1]
    //     0x76bdac: ldurb           w17, [x0, #-1]
    //     0x76bdb0: and             x16, x17, x16, lsr #2
    //     0x76bdb4: tst             x16, HEAP, lsr #32
    //     0x76bdb8: b.eq            #0x76bdc0
    //     0x76bdbc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76bdc0: b               #0x76bdc8
    // 0x76bdc4: ldur            x1, [fp, #-0x18]
    // 0x76bdc8: ldur            x0, [fp, #-8]
    // 0x76bdcc: LoadField: r2 = r0->field_73
    //     0x76bdcc: ldur            x2, [x0, #0x73]
    // 0x76bdd0: cmp             x2, #0
    // 0x76bdd4: b.le            #0x76bdec
    // 0x76bdd8: ldur            x3, [fp, #-0x20]
    // 0x76bddc: r2 = true
    //     0x76bddc: add             x2, NULL, #0x20  ; true
    // 0x76bde0: r4 = Instance_Offset
    //     0x76bde0: ldr             x4, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x76bde4: StoreField: r3->field_7 = r4
    //     0x76bde4: stur            w4, [x3, #7]
    // 0x76bde8: ArrayStore: r3[0] = r2  ; List_4
    //     0x76bde8: stur            w2, [x3, #0x17]
    // 0x76bdec: mov             x4, x0
    // 0x76bdf0: mov             x3, x1
    // 0x76bdf4: b               #0x76be84
    // 0x76bdf8: ldur            x0, [fp, #-8]
    // 0x76bdfc: mov             x1, x3
    // 0x76be00: LoadField: r2 = r1->field_1f
    //     0x76be00: ldur            w2, [x1, #0x1f]
    // 0x76be04: DecompressPointer r2
    //     0x76be04: add             x2, x2, HEAP, lsl #32
    // 0x76be08: r16 = Sentinel
    //     0x76be08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x76be0c: cmp             w2, w16
    // 0x76be10: b.ne            #0x76be24
    // 0x76be14: r16 = "toolbarWidth"
    //     0x76be14: add             x16, PP, #0x5d, lsl #12  ; [pp+0x5d168] "toolbarWidth"
    //     0x76be18: ldr             x16, [x16, #0x168]
    // 0x76be1c: str             x16, [SP]
    // 0x76be20: r0 = _throwLocalNotInitialized()
    //     0x76be20: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x76be24: ldur            x4, [fp, #-8]
    // 0x76be28: ldur            x3, [fp, #-0x18]
    // 0x76be2c: LoadField: r0 = r3->field_1f
    //     0x76be2c: ldur            w0, [x3, #0x1f]
    // 0x76be30: DecompressPointer r0
    //     0x76be30: add             x0, x0, HEAP, lsl #32
    // 0x76be34: LoadField: d0 = r4->field_7f
    //     0x76be34: ldur            d0, [x4, #0x7f]
    // 0x76be38: LoadField: d1 = r0->field_7
    //     0x76be38: ldur            d1, [x0, #7]
    // 0x76be3c: fsub            d2, d1, d0
    // 0x76be40: r0 = inline_Allocate_Double()
    //     0x76be40: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x76be44: add             x0, x0, #0x10
    //     0x76be48: cmp             x1, x0
    //     0x76be4c: b.ls            #0x76c050
    //     0x76be50: str             x0, [THR, #0x50]  ; THR::top
    //     0x76be54: sub             x0, x0, #0xf
    //     0x76be58: movz            x1, #0xe15c
    //     0x76be5c: movk            x1, #0x3, lsl #16
    //     0x76be60: stur            x1, [x0, #-1]
    // 0x76be64: StoreField: r0->field_7 = d2
    //     0x76be64: stur            d2, [x0, #7]
    // 0x76be68: StoreField: r3->field_1f = r0
    //     0x76be68: stur            w0, [x3, #0x1f]
    //     0x76be6c: ldurb           w16, [x3, #-1]
    //     0x76be70: ldurb           w17, [x0, #-1]
    //     0x76be74: and             x16, x17, x16, lsr #2
    //     0x76be78: tst             x16, HEAP, lsr #32
    //     0x76be7c: b.eq            #0x76be84
    //     0x76be80: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x76be84: LoadField: r0 = r4->field_73
    //     0x76be84: ldur            x0, [x4, #0x73]
    // 0x76be88: LoadField: r1 = r3->field_23
    //     0x76be88: ldur            w1, [x3, #0x23]
    // 0x76be8c: DecompressPointer r1
    //     0x76be8c: add             x1, x1, HEAP, lsl #32
    // 0x76be90: r2 = LoadInt32Instr(r1)
    //     0x76be90: sbfx            x2, x1, #1, #0x1f
    //     0x76be94: tbz             w1, #0, #0x76be9c
    //     0x76be98: ldur            x2, [x1, #7]
    // 0x76be9c: cmp             x0, x2
    // 0x76bea0: r16 = true
    //     0x76bea0: add             x16, NULL, #0x20  ; true
    // 0x76bea4: r17 = false
    //     0x76bea4: add             x17, NULL, #0x30  ; false
    // 0x76bea8: csel            x1, x16, x17, ne
    // 0x76beac: StoreField: r4->field_6b = r1
    //     0x76beac: stur            w1, [x4, #0x6b]
    // 0x76beb0: cmp             x0, #0
    // 0x76beb4: r16 = true
    //     0x76beb4: add             x16, NULL, #0x20  ; true
    // 0x76beb8: r17 = false
    //     0x76beb8: add             x17, NULL, #0x30  ; false
    // 0x76bebc: csel            x1, x16, x17, gt
    // 0x76bec0: StoreField: r4->field_6f = r1
    //     0x76bec0: stur            w1, [x4, #0x6f]
    // 0x76bec4: LoadField: r5 = r4->field_27
    //     0x76bec4: ldur            w5, [x4, #0x27]
    // 0x76bec8: DecompressPointer r5
    //     0x76bec8: add             x5, x5, HEAP, lsl #32
    // 0x76becc: stur            x5, [fp, #-0x10]
    // 0x76bed0: cmp             w5, NULL
    // 0x76bed4: b.eq            #0x76bfe4
    // 0x76bed8: mov             x0, x5
    // 0x76bedc: r2 = Null
    //     0x76bedc: mov             x2, NULL
    // 0x76bee0: r1 = Null
    //     0x76bee0: mov             x1, NULL
    // 0x76bee4: r4 = LoadClassIdInstr(r0)
    //     0x76bee4: ldur            x4, [x0, #-1]
    //     0x76bee8: ubfx            x4, x4, #0xc, #0x14
    // 0x76beec: sub             x4, x4, #0xc83
    // 0x76bef0: cmp             x4, #1
    // 0x76bef4: b.ls            #0x76bf08
    // 0x76bef8: r8 = BoxConstraints
    //     0x76bef8: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76befc: r3 = Null
    //     0x76befc: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d170] Null
    //     0x76bf00: ldr             x3, [x3, #0x170]
    // 0x76bf04: r0 = BoxConstraints()
    //     0x76bf04: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76bf08: ldur            x0, [fp, #-0x18]
    // 0x76bf0c: LoadField: r1 = r0->field_1f
    //     0x76bf0c: ldur            w1, [x0, #0x1f]
    // 0x76bf10: DecompressPointer r1
    //     0x76bf10: add             x1, x1, HEAP, lsl #32
    // 0x76bf14: r16 = Sentinel
    //     0x76bf14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x76bf18: cmp             w1, w16
    // 0x76bf1c: b.ne            #0x76bf30
    // 0x76bf20: r16 = "toolbarWidth"
    //     0x76bf20: add             x16, PP, #0x5d, lsl #12  ; [pp+0x5d168] "toolbarWidth"
    //     0x76bf24: ldr             x16, [x16, #0x168]
    // 0x76bf28: str             x16, [SP]
    // 0x76bf2c: r0 = _throwLocalNotInitialized()
    //     0x76bf2c: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x76bf30: ldur            x1, [fp, #-8]
    // 0x76bf34: ldur            x0, [fp, #-0x18]
    // 0x76bf38: LoadField: r2 = r0->field_1f
    //     0x76bf38: ldur            w2, [x0, #0x1f]
    // 0x76bf3c: DecompressPointer r2
    //     0x76bf3c: add             x2, x2, HEAP, lsl #32
    // 0x76bf40: LoadField: r3 = r0->field_13
    //     0x76bf40: ldur            w3, [x0, #0x13]
    // 0x76bf44: DecompressPointer r3
    //     0x76bf44: add             x3, x3, HEAP, lsl #32
    // 0x76bf48: stur            x3, [fp, #-0x20]
    // 0x76bf4c: LoadField: d0 = r2->field_7
    //     0x76bf4c: ldur            d0, [x2, #7]
    // 0x76bf50: stur            d0, [fp, #-0x38]
    // 0x76bf54: r0 = Size()
    //     0x76bf54: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x76bf58: ldur            d0, [fp, #-0x38]
    // 0x76bf5c: StoreField: r0->field_7 = d0
    //     0x76bf5c: stur            d0, [x0, #7]
    // 0x76bf60: ldur            x1, [fp, #-0x20]
    // 0x76bf64: LoadField: d0 = r1->field_7
    //     0x76bf64: ldur            d0, [x1, #7]
    // 0x76bf68: StoreField: r0->field_f = d0
    //     0x76bf68: stur            d0, [x0, #0xf]
    // 0x76bf6c: ldur            x1, [fp, #-0x10]
    // 0x76bf70: mov             x2, x0
    // 0x76bf74: r0 = constrain()
    //     0x76bf74: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x76bf78: ldur            x1, [fp, #-8]
    // 0x76bf7c: StoreField: r1->field_53 = r0
    //     0x76bf7c: stur            w0, [x1, #0x53]
    //     0x76bf80: ldurb           w16, [x1, #-1]
    //     0x76bf84: ldurb           w17, [x0, #-1]
    //     0x76bf88: and             x16, x17, x16, lsr #2
    //     0x76bf8c: tst             x16, HEAP, lsr #32
    //     0x76bf90: b.eq            #0x76bf98
    //     0x76bf94: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76bf98: r0 = Null
    //     0x76bf98: mov             x0, NULL
    // 0x76bf9c: LeaveFrame
    //     0x76bf9c: mov             SP, fp
    //     0x76bfa0: ldp             fp, lr, [SP], #0x10
    // 0x76bfa4: ret
    //     0x76bfa4: ret             
    // 0x76bfa8: r0 = StateError()
    //     0x76bfa8: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76bfac: mov             x1, x0
    // 0x76bfb0: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76bfb0: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76bfb4: StoreField: r1->field_b = r0
    //     0x76bfb4: stur            w0, [x1, #0xb]
    // 0x76bfb8: mov             x0, x1
    // 0x76bfbc: r0 = Throw()
    //     0x76bfbc: bl              #0xec04b8  ; ThrowStub
    // 0x76bfc0: brk             #0
    // 0x76bfc4: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76bfc4: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76bfc8: r0 = StateError()
    //     0x76bfc8: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76bfcc: mov             x1, x0
    // 0x76bfd0: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76bfd0: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76bfd4: StoreField: r1->field_b = r0
    //     0x76bfd4: stur            w0, [x1, #0xb]
    // 0x76bfd8: mov             x0, x1
    // 0x76bfdc: r0 = Throw()
    //     0x76bfdc: bl              #0xec04b8  ; ThrowStub
    // 0x76bfe0: brk             #0
    // 0x76bfe4: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76bfe4: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76bfe8: r0 = StateError()
    //     0x76bfe8: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76bfec: mov             x1, x0
    // 0x76bff0: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76bff0: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76bff4: StoreField: r1->field_b = r0
    //     0x76bff4: stur            w0, [x1, #0xb]
    // 0x76bff8: mov             x0, x1
    // 0x76bffc: r0 = Throw()
    //     0x76bffc: bl              #0xec04b8  ; ThrowStub
    // 0x76c000: brk             #0
    // 0x76c004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76c004: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76c008: b               #0x76b924
    // 0x76c00c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76c00c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76c010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76c010: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76c014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76c014: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76c018: r0 = NullCastErrorSharedWithFPURegs()
    //     0x76c018: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x76c01c: SaveReg d2
    //     0x76c01c: str             q2, [SP, #-0x10]!
    // 0x76c020: r0 = AllocateDouble()
    //     0x76c020: bl              #0xec2254  ; AllocateDoubleStub
    // 0x76c024: RestoreReg d2
    //     0x76c024: ldr             q2, [SP], #0x10
    // 0x76c028: b               #0x76bb60
    // 0x76c02c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76c02c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76c030: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76c030: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76c034: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76c034: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76c038: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76c038: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76c03c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76c03c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76c040: SaveReg d2
    //     0x76c040: str             q2, [SP, #-0x10]!
    // 0x76c044: r0 = AllocateDouble()
    //     0x76c044: bl              #0xec2254  ; AllocateDoubleStub
    // 0x76c048: RestoreReg d2
    //     0x76c048: ldr             q2, [SP], #0x10
    // 0x76c04c: b               #0x76bd9c
    // 0x76c050: SaveReg d2
    //     0x76c050: str             q2, [SP, #-0x10]!
    // 0x76c054: stp             x3, x4, [SP, #-0x10]!
    // 0x76c058: r0 = AllocateDouble()
    //     0x76c058: bl              #0xec2254  ; AllocateDoubleStub
    // 0x76c05c: ldp             x3, x4, [SP], #0x10
    // 0x76c060: RestoreReg d2
    //     0x76c060: ldr             q2, [SP], #0x10
    // 0x76c064: b               #0x76be64
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x76c068, size: 0x6c8
    // 0x76c068: EnterFrame
    //     0x76c068: stp             fp, lr, [SP, #-0x10]!
    //     0x76c06c: mov             fp, SP
    // 0x76c070: AllocStack(0x40)
    //     0x76c070: sub             SP, SP, #0x40
    // 0x76c074: SetupParameters()
    //     0x76c074: ldr             x0, [fp, #0x18]
    //     0x76c078: ldur            w3, [x0, #0x17]
    //     0x76c07c: add             x3, x3, HEAP, lsl #32
    //     0x76c080: stur            x3, [fp, #-0x10]
    // 0x76c084: CheckStackOverflow
    //     0x76c084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76c088: cmp             SP, x16
    //     0x76c08c: b.ls            #0x76c6e0
    // 0x76c090: LoadField: r0 = r3->field_27
    //     0x76c090: ldur            w0, [x3, #0x27]
    // 0x76c094: DecompressPointer r0
    //     0x76c094: add             x0, x0, HEAP, lsl #32
    // 0x76c098: r1 = LoadInt32Instr(r0)
    //     0x76c098: sbfx            x1, x0, #1, #0x1f
    //     0x76c09c: tbz             w0, #0, #0x76c0a4
    //     0x76c0a0: ldur            x1, [x0, #7]
    // 0x76c0a4: add             x4, x1, #1
    // 0x76c0a8: stur            x4, [fp, #-8]
    // 0x76c0ac: r0 = BoxInt64Instr(r4)
    //     0x76c0ac: sbfiz           x0, x4, #1, #0x1f
    //     0x76c0b0: cmp             x4, x0, asr #1
    //     0x76c0b4: b.eq            #0x76c0c0
    //     0x76c0b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x76c0bc: stur            x4, [x0, #7]
    // 0x76c0c0: StoreField: r3->field_27 = r0
    //     0x76c0c0: stur            w0, [x3, #0x27]
    //     0x76c0c4: tbz             w0, #0, #0x76c0e0
    //     0x76c0c8: ldurb           w16, [x3, #-1]
    //     0x76c0cc: ldurb           w17, [x0, #-1]
    //     0x76c0d0: and             x16, x17, x16, lsr #2
    //     0x76c0d4: tst             x16, HEAP, lsr #32
    //     0x76c0d8: b.eq            #0x76c0e0
    //     0x76c0dc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x76c0e0: ldr             x0, [fp, #0x10]
    // 0x76c0e4: r2 = Null
    //     0x76c0e4: mov             x2, NULL
    // 0x76c0e8: r1 = Null
    //     0x76c0e8: mov             x1, NULL
    // 0x76c0ec: r4 = LoadClassIdInstr(r0)
    //     0x76c0ec: ldur            x4, [x0, #-1]
    //     0x76c0f0: ubfx            x4, x4, #0xc, #0x14
    // 0x76c0f4: sub             x4, x4, #0xbba
    // 0x76c0f8: cmp             x4, #0x9a
    // 0x76c0fc: b.ls            #0x76c110
    // 0x76c100: r8 = RenderBox
    //     0x76c100: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x76c104: r3 = Null
    //     0x76c104: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d180] Null
    //     0x76c108: ldr             x3, [x3, #0x180]
    // 0x76c10c: r0 = RenderBox()
    //     0x76c10c: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x76c110: ldr             x3, [fp, #0x10]
    // 0x76c114: LoadField: r4 = r3->field_7
    //     0x76c114: ldur            w4, [x3, #7]
    // 0x76c118: DecompressPointer r4
    //     0x76c118: add             x4, x4, HEAP, lsl #32
    // 0x76c11c: stur            x4, [fp, #-0x18]
    // 0x76c120: cmp             w4, NULL
    // 0x76c124: b.eq            #0x76c6e8
    // 0x76c128: mov             x0, x4
    // 0x76c12c: r2 = Null
    //     0x76c12c: mov             x2, NULL
    // 0x76c130: r1 = Null
    //     0x76c130: mov             x1, NULL
    // 0x76c134: r4 = LoadClassIdInstr(r0)
    //     0x76c134: ldur            x4, [x0, #-1]
    //     0x76c138: ubfx            x4, x4, #0xc, #0x14
    // 0x76c13c: cmp             x4, #0xc77
    // 0x76c140: b.eq            #0x76c158
    // 0x76c144: r8 = ToolbarItemsParentData
    //     0x76c144: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x76c148: ldr             x8, [x8, #0x490]
    // 0x76c14c: r3 = Null
    //     0x76c14c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d190] Null
    //     0x76c150: ldr             x3, [x3, #0x190]
    // 0x76c154: r0 = DefaultTypeTest()
    //     0x76c154: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x76c158: ldur            x0, [fp, #-0x18]
    // 0x76c15c: r1 = false
    //     0x76c15c: add             x1, NULL, #0x30  ; false
    // 0x76c160: ArrayStore: r0[0] = r1  ; List_4
    //     0x76c160: stur            w1, [x0, #0x17]
    // 0x76c164: ldur            x2, [fp, #-0x10]
    // 0x76c168: LoadField: r1 = r2->field_f
    //     0x76c168: ldur            w1, [x2, #0xf]
    // 0x76c16c: DecompressPointer r1
    //     0x76c16c: add             x1, x1, HEAP, lsl #32
    // 0x76c170: LoadField: r3 = r1->field_87
    //     0x76c170: ldur            w3, [x1, #0x87]
    // 0x76c174: DecompressPointer r3
    //     0x76c174: add             x3, x3, HEAP, lsl #32
    // 0x76c178: ldr             x4, [fp, #0x10]
    // 0x76c17c: cmp             w4, w3
    // 0x76c180: b.eq            #0x76c1b4
    // 0x76c184: LoadField: r3 = r1->field_8b
    //     0x76c184: ldur            w3, [x1, #0x8b]
    // 0x76c188: DecompressPointer r3
    //     0x76c188: add             x3, x3, HEAP, lsl #32
    // 0x76c18c: cmp             w4, w3
    // 0x76c190: b.eq            #0x76c1b4
    // 0x76c194: LoadField: r5 = r2->field_23
    //     0x76c194: ldur            w5, [x2, #0x23]
    // 0x76c198: DecompressPointer r5
    //     0x76c198: add             x5, x5, HEAP, lsl #32
    // 0x76c19c: LoadField: r6 = r1->field_73
    //     0x76c19c: ldur            x6, [x1, #0x73]
    // 0x76c1a0: r7 = LoadInt32Instr(r5)
    //     0x76c1a0: sbfx            x7, x5, #1, #0x1f
    //     0x76c1a4: tbz             w5, #0, #0x76c1ac
    //     0x76c1a8: ldur            x7, [x5, #7]
    // 0x76c1ac: cmp             x7, x6
    // 0x76c1b0: b.le            #0x76c1c4
    // 0x76c1b4: r0 = Null
    //     0x76c1b4: mov             x0, NULL
    // 0x76c1b8: LeaveFrame
    //     0x76c1b8: mov             SP, fp
    //     0x76c1bc: ldp             fp, lr, [SP], #0x10
    // 0x76c1c0: ret
    //     0x76c1c0: ret             
    // 0x76c1c4: cbnz            x7, #0x76c200
    // 0x76c1c8: ldur            x5, [fp, #-8]
    // 0x76c1cc: LoadField: r6 = r1->field_57
    //     0x76c1cc: ldur            x6, [x1, #0x57]
    // 0x76c1d0: add             x1, x6, #1
    // 0x76c1d4: cmp             x5, x1
    // 0x76c1d8: b.ne            #0x76c1e4
    // 0x76c1dc: d0 = 0.000000
    //     0x76c1dc: eor             v0.16b, v0.16b, v0.16b
    // 0x76c1e0: b               #0x76c1f8
    // 0x76c1e4: cmp             w3, NULL
    // 0x76c1e8: b.eq            #0x76c6ec
    // 0x76c1ec: mov             x1, x3
    // 0x76c1f0: r0 = size()
    //     0x76c1f0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76c1f4: LoadField: d0 = r0->field_7
    //     0x76c1f4: ldur            d0, [x0, #7]
    // 0x76c1f8: ldur            x3, [fp, #-0x10]
    // 0x76c1fc: b               #0x76c210
    // 0x76c200: mov             x3, x2
    // 0x76c204: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x76c204: ldur            w0, [x3, #0x17]
    // 0x76c208: DecompressPointer r0
    //     0x76c208: add             x0, x0, HEAP, lsl #32
    // 0x76c20c: LoadField: d0 = r0->field_7
    //     0x76c20c: ldur            d0, [x0, #7]
    // 0x76c210: stur            d0, [fp, #-0x30]
    // 0x76c214: LoadField: r0 = r3->field_f
    //     0x76c214: ldur            w0, [x3, #0xf]
    // 0x76c218: DecompressPointer r0
    //     0x76c218: add             x0, x0, HEAP, lsl #32
    // 0x76c21c: LoadField: r4 = r0->field_27
    //     0x76c21c: ldur            w4, [x0, #0x27]
    // 0x76c220: DecompressPointer r4
    //     0x76c220: add             x4, x4, HEAP, lsl #32
    // 0x76c224: stur            x4, [fp, #-0x20]
    // 0x76c228: cmp             w4, NULL
    // 0x76c22c: b.eq            #0x76c684
    // 0x76c230: ldr             x5, [fp, #0x10]
    // 0x76c234: mov             x0, x4
    // 0x76c238: r2 = Null
    //     0x76c238: mov             x2, NULL
    // 0x76c23c: r1 = Null
    //     0x76c23c: mov             x1, NULL
    // 0x76c240: r4 = LoadClassIdInstr(r0)
    //     0x76c240: ldur            x4, [x0, #-1]
    //     0x76c244: ubfx            x4, x4, #0xc, #0x14
    // 0x76c248: sub             x4, x4, #0xc83
    // 0x76c24c: cmp             x4, #1
    // 0x76c250: b.ls            #0x76c264
    // 0x76c254: r8 = BoxConstraints
    //     0x76c254: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76c258: r3 = Null
    //     0x76c258: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d1a0] Null
    //     0x76c25c: ldr             x3, [x3, #0x1a0]
    // 0x76c260: r0 = BoxConstraints()
    //     0x76c260: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76c264: ldur            x0, [fp, #-0x20]
    // 0x76c268: LoadField: d0 = r0->field_f
    //     0x76c268: ldur            d0, [x0, #0xf]
    // 0x76c26c: ldur            d1, [fp, #-0x30]
    // 0x76c270: fsub            d2, d0, d1
    // 0x76c274: ldur            x0, [fp, #-0x10]
    // 0x76c278: stur            d2, [fp, #-0x38]
    // 0x76c27c: LoadField: r1 = r0->field_13
    //     0x76c27c: ldur            w1, [x0, #0x13]
    // 0x76c280: DecompressPointer r1
    //     0x76c280: add             x1, x1, HEAP, lsl #32
    // 0x76c284: stur            x1, [fp, #-0x20]
    // 0x76c288: r0 = BoxConstraints()
    //     0x76c288: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x76c28c: StoreField: r0->field_7 = rZR
    //     0x76c28c: stur            xzr, [x0, #7]
    // 0x76c290: ldur            d0, [fp, #-0x38]
    // 0x76c294: StoreField: r0->field_f = d0
    //     0x76c294: stur            d0, [x0, #0xf]
    // 0x76c298: ldur            x1, [fp, #-0x20]
    // 0x76c29c: LoadField: d0 = r1->field_7
    //     0x76c29c: ldur            d0, [x1, #7]
    // 0x76c2a0: ArrayStore: r0[0] = d0  ; List_8
    //     0x76c2a0: stur            d0, [x0, #0x17]
    // 0x76c2a4: StoreField: r0->field_1f = d0
    //     0x76c2a4: stur            d0, [x0, #0x1f]
    // 0x76c2a8: ldr             x3, [fp, #0x10]
    // 0x76c2ac: r1 = LoadClassIdInstr(r3)
    //     0x76c2ac: ldur            x1, [x3, #-1]
    //     0x76c2b0: ubfx            x1, x1, #0xc, #0x14
    // 0x76c2b4: r16 = true
    //     0x76c2b4: add             x16, NULL, #0x20  ; true
    // 0x76c2b8: str             x16, [SP]
    // 0x76c2bc: mov             x2, x0
    // 0x76c2c0: mov             x0, x1
    // 0x76c2c4: mov             x1, x3
    // 0x76c2c8: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x76c2c8: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x76c2cc: ldr             x4, [x4, #0x5c0]
    // 0x76c2d0: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x76c2d0: movz            x17, #0xed1d
    //     0x76c2d4: add             lr, x0, x17
    //     0x76c2d8: ldr             lr, [x21, lr, lsl #3]
    //     0x76c2dc: blr             lr
    // 0x76c2e0: ldur            x0, [fp, #-0x10]
    // 0x76c2e4: LoadField: r1 = r0->field_1b
    //     0x76c2e4: ldur            w1, [x0, #0x1b]
    // 0x76c2e8: DecompressPointer r1
    //     0x76c2e8: add             x1, x1, HEAP, lsl #32
    // 0x76c2ec: LoadField: d0 = r1->field_7
    //     0x76c2ec: ldur            d0, [x1, #7]
    // 0x76c2f0: ldur            d1, [fp, #-0x30]
    // 0x76c2f4: fadd            d2, d0, d1
    // 0x76c2f8: ldr             x1, [fp, #0x10]
    // 0x76c2fc: stur            d2, [fp, #-0x38]
    // 0x76c300: r0 = size()
    //     0x76c300: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76c304: LoadField: d0 = r0->field_7
    //     0x76c304: ldur            d0, [x0, #7]
    // 0x76c308: ldur            d1, [fp, #-0x38]
    // 0x76c30c: fadd            d2, d1, d0
    // 0x76c310: ldur            x3, [fp, #-0x10]
    // 0x76c314: stur            d2, [fp, #-0x30]
    // 0x76c318: LoadField: r4 = r3->field_f
    //     0x76c318: ldur            w4, [x3, #0xf]
    // 0x76c31c: DecompressPointer r4
    //     0x76c31c: add             x4, x4, HEAP, lsl #32
    // 0x76c320: stur            x4, [fp, #-0x28]
    // 0x76c324: LoadField: r5 = r4->field_27
    //     0x76c324: ldur            w5, [x4, #0x27]
    // 0x76c328: DecompressPointer r5
    //     0x76c328: add             x5, x5, HEAP, lsl #32
    // 0x76c32c: stur            x5, [fp, #-0x20]
    // 0x76c330: cmp             w5, NULL
    // 0x76c334: b.eq            #0x76c6a0
    // 0x76c338: mov             x0, x5
    // 0x76c33c: r2 = Null
    //     0x76c33c: mov             x2, NULL
    // 0x76c340: r1 = Null
    //     0x76c340: mov             x1, NULL
    // 0x76c344: r4 = LoadClassIdInstr(r0)
    //     0x76c344: ldur            x4, [x0, #-1]
    //     0x76c348: ubfx            x4, x4, #0xc, #0x14
    // 0x76c34c: sub             x4, x4, #0xc83
    // 0x76c350: cmp             x4, #1
    // 0x76c354: b.ls            #0x76c368
    // 0x76c358: r8 = BoxConstraints
    //     0x76c358: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76c35c: r3 = Null
    //     0x76c35c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d1b0] Null
    //     0x76c360: ldr             x3, [x3, #0x1b0]
    // 0x76c364: r0 = BoxConstraints()
    //     0x76c364: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76c368: ldur            x0, [fp, #-0x20]
    // 0x76c36c: LoadField: d0 = r0->field_f
    //     0x76c36c: ldur            d0, [x0, #0xf]
    // 0x76c370: ldur            d1, [fp, #-0x30]
    // 0x76c374: fcmp            d1, d0
    // 0x76c378: b.le            #0x76c564
    // 0x76c37c: ldur            x2, [fp, #-0x10]
    // 0x76c380: ldur            x3, [fp, #-0x28]
    // 0x76c384: LoadField: r0 = r2->field_23
    //     0x76c384: ldur            w0, [x2, #0x23]
    // 0x76c388: DecompressPointer r0
    //     0x76c388: add             x0, x0, HEAP, lsl #32
    // 0x76c38c: r1 = LoadInt32Instr(r0)
    //     0x76c38c: sbfx            x1, x0, #1, #0x1f
    //     0x76c390: tbz             w0, #0, #0x76c398
    //     0x76c394: ldur            x1, [x0, #7]
    // 0x76c398: add             x4, x1, #1
    // 0x76c39c: r0 = BoxInt64Instr(r4)
    //     0x76c39c: sbfiz           x0, x4, #1, #0x1f
    //     0x76c3a0: cmp             x4, x0, asr #1
    //     0x76c3a4: b.eq            #0x76c3b0
    //     0x76c3a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x76c3ac: stur            x4, [x0, #7]
    // 0x76c3b0: StoreField: r2->field_23 = r0
    //     0x76c3b0: stur            w0, [x2, #0x23]
    //     0x76c3b4: tbz             w0, #0, #0x76c3d0
    //     0x76c3b8: ldurb           w16, [x2, #-1]
    //     0x76c3bc: ldurb           w17, [x0, #-1]
    //     0x76c3c0: and             x16, x17, x16, lsr #2
    //     0x76c3c4: tst             x16, HEAP, lsr #32
    //     0x76c3c8: b.eq            #0x76c3d0
    //     0x76c3cc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x76c3d0: LoadField: r1 = r3->field_87
    //     0x76c3d0: ldur            w1, [x3, #0x87]
    // 0x76c3d4: DecompressPointer r1
    //     0x76c3d4: add             x1, x1, HEAP, lsl #32
    // 0x76c3d8: cmp             w1, NULL
    // 0x76c3dc: b.eq            #0x76c6f0
    // 0x76c3e0: r0 = size()
    //     0x76c3e0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76c3e4: LoadField: d0 = r0->field_7
    //     0x76c3e4: ldur            d0, [x0, #7]
    // 0x76c3e8: ldur            x2, [fp, #-0x10]
    // 0x76c3ec: LoadField: r1 = r2->field_f
    //     0x76c3ec: ldur            w1, [x2, #0xf]
    // 0x76c3f0: DecompressPointer r1
    //     0x76c3f0: add             x1, x1, HEAP, lsl #32
    // 0x76c3f4: LoadField: d1 = r1->field_7f
    //     0x76c3f4: ldur            d1, [x1, #0x7f]
    // 0x76c3f8: fadd            d2, d0, d1
    // 0x76c3fc: r0 = inline_Allocate_Double()
    //     0x76c3fc: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x76c400: add             x0, x0, #0x10
    //     0x76c404: cmp             x3, x0
    //     0x76c408: b.ls            #0x76c6f4
    //     0x76c40c: str             x0, [THR, #0x50]  ; THR::top
    //     0x76c410: sub             x0, x0, #0xf
    //     0x76c414: movz            x3, #0xe15c
    //     0x76c418: movk            x3, #0x3, lsl #16
    //     0x76c41c: stur            x3, [x0, #-1]
    // 0x76c420: StoreField: r0->field_7 = d2
    //     0x76c420: stur            d2, [x0, #7]
    // 0x76c424: StoreField: r2->field_1b = r0
    //     0x76c424: stur            w0, [x2, #0x1b]
    //     0x76c428: ldurb           w16, [x2, #-1]
    //     0x76c42c: ldurb           w17, [x0, #-1]
    //     0x76c430: and             x16, x17, x16, lsr #2
    //     0x76c434: tst             x16, HEAP, lsr #32
    //     0x76c438: b.eq            #0x76c440
    //     0x76c43c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x76c440: LoadField: r0 = r1->field_87
    //     0x76c440: ldur            w0, [x1, #0x87]
    // 0x76c444: DecompressPointer r0
    //     0x76c444: add             x0, x0, HEAP, lsl #32
    // 0x76c448: cmp             w0, NULL
    // 0x76c44c: b.eq            #0x76c70c
    // 0x76c450: mov             x1, x0
    // 0x76c454: r0 = size()
    //     0x76c454: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76c458: LoadField: d0 = r0->field_7
    //     0x76c458: ldur            d0, [x0, #7]
    // 0x76c45c: ldur            x0, [fp, #-0x10]
    // 0x76c460: stur            d0, [fp, #-0x30]
    // 0x76c464: LoadField: r1 = r0->field_f
    //     0x76c464: ldur            w1, [x0, #0xf]
    // 0x76c468: DecompressPointer r1
    //     0x76c468: add             x1, x1, HEAP, lsl #32
    // 0x76c46c: LoadField: r2 = r1->field_8b
    //     0x76c46c: ldur            w2, [x1, #0x8b]
    // 0x76c470: DecompressPointer r2
    //     0x76c470: add             x2, x2, HEAP, lsl #32
    // 0x76c474: cmp             w2, NULL
    // 0x76c478: b.eq            #0x76c710
    // 0x76c47c: mov             x1, x2
    // 0x76c480: r0 = size()
    //     0x76c480: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76c484: LoadField: d0 = r0->field_7
    //     0x76c484: ldur            d0, [x0, #7]
    // 0x76c488: ldur            d1, [fp, #-0x30]
    // 0x76c48c: fadd            d2, d1, d0
    // 0x76c490: ldur            x3, [fp, #-0x10]
    // 0x76c494: stur            d2, [fp, #-0x38]
    // 0x76c498: LoadField: r0 = r3->field_f
    //     0x76c498: ldur            w0, [x3, #0xf]
    // 0x76c49c: DecompressPointer r0
    //     0x76c49c: add             x0, x0, HEAP, lsl #32
    // 0x76c4a0: LoadField: r4 = r0->field_27
    //     0x76c4a0: ldur            w4, [x0, #0x27]
    // 0x76c4a4: DecompressPointer r4
    //     0x76c4a4: add             x4, x4, HEAP, lsl #32
    // 0x76c4a8: stur            x4, [fp, #-0x20]
    // 0x76c4ac: cmp             w4, NULL
    // 0x76c4b0: b.eq            #0x76c6c0
    // 0x76c4b4: ldr             x5, [fp, #0x10]
    // 0x76c4b8: mov             x0, x4
    // 0x76c4bc: r2 = Null
    //     0x76c4bc: mov             x2, NULL
    // 0x76c4c0: r1 = Null
    //     0x76c4c0: mov             x1, NULL
    // 0x76c4c4: r4 = LoadClassIdInstr(r0)
    //     0x76c4c4: ldur            x4, [x0, #-1]
    //     0x76c4c8: ubfx            x4, x4, #0xc, #0x14
    // 0x76c4cc: sub             x4, x4, #0xc83
    // 0x76c4d0: cmp             x4, #1
    // 0x76c4d4: b.ls            #0x76c4e8
    // 0x76c4d8: r8 = BoxConstraints
    //     0x76c4d8: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76c4dc: r3 = Null
    //     0x76c4dc: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d1c0] Null
    //     0x76c4e0: ldr             x3, [x3, #0x1c0]
    // 0x76c4e4: r0 = BoxConstraints()
    //     0x76c4e4: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76c4e8: ldur            x0, [fp, #-0x20]
    // 0x76c4ec: LoadField: d0 = r0->field_f
    //     0x76c4ec: ldur            d0, [x0, #0xf]
    // 0x76c4f0: ldur            d1, [fp, #-0x38]
    // 0x76c4f4: fsub            d2, d0, d1
    // 0x76c4f8: ldur            x0, [fp, #-0x10]
    // 0x76c4fc: stur            d2, [fp, #-0x30]
    // 0x76c500: LoadField: r1 = r0->field_13
    //     0x76c500: ldur            w1, [x0, #0x13]
    // 0x76c504: DecompressPointer r1
    //     0x76c504: add             x1, x1, HEAP, lsl #32
    // 0x76c508: stur            x1, [fp, #-0x20]
    // 0x76c50c: r0 = BoxConstraints()
    //     0x76c50c: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x76c510: StoreField: r0->field_7 = rZR
    //     0x76c510: stur            xzr, [x0, #7]
    // 0x76c514: ldur            d0, [fp, #-0x30]
    // 0x76c518: StoreField: r0->field_f = d0
    //     0x76c518: stur            d0, [x0, #0xf]
    // 0x76c51c: ldur            x1, [fp, #-0x20]
    // 0x76c520: LoadField: d0 = r1->field_7
    //     0x76c520: ldur            d0, [x1, #7]
    // 0x76c524: ArrayStore: r0[0] = d0  ; List_8
    //     0x76c524: stur            d0, [x0, #0x17]
    // 0x76c528: StoreField: r0->field_1f = d0
    //     0x76c528: stur            d0, [x0, #0x1f]
    // 0x76c52c: ldr             x3, [fp, #0x10]
    // 0x76c530: r1 = LoadClassIdInstr(r3)
    //     0x76c530: ldur            x1, [x3, #-1]
    //     0x76c534: ubfx            x1, x1, #0xc, #0x14
    // 0x76c538: r16 = true
    //     0x76c538: add             x16, NULL, #0x20  ; true
    // 0x76c53c: str             x16, [SP]
    // 0x76c540: mov             x2, x0
    // 0x76c544: mov             x0, x1
    // 0x76c548: mov             x1, x3
    // 0x76c54c: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x76c54c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x76c550: ldr             x4, [x4, #0x5c0]
    // 0x76c554: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x76c554: movz            x17, #0xed1d
    //     0x76c558: add             lr, x0, x17
    //     0x76c55c: ldr             lr, [x21, lr, lsl #3]
    //     0x76c560: blr             lr
    // 0x76c564: ldur            x0, [fp, #-0x10]
    // 0x76c568: ldur            x1, [fp, #-0x18]
    // 0x76c56c: LoadField: r2 = r0->field_1b
    //     0x76c56c: ldur            w2, [x0, #0x1b]
    // 0x76c570: DecompressPointer r2
    //     0x76c570: add             x2, x2, HEAP, lsl #32
    // 0x76c574: LoadField: d0 = r2->field_7
    //     0x76c574: ldur            d0, [x2, #7]
    // 0x76c578: stur            d0, [fp, #-0x30]
    // 0x76c57c: r0 = Offset()
    //     0x76c57c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x76c580: ldur            d0, [fp, #-0x30]
    // 0x76c584: StoreField: r0->field_7 = d0
    //     0x76c584: stur            d0, [x0, #7]
    // 0x76c588: StoreField: r0->field_f = rZR
    //     0x76c588: stur            xzr, [x0, #0xf]
    // 0x76c58c: ldur            x2, [fp, #-0x18]
    // 0x76c590: StoreField: r2->field_7 = r0
    //     0x76c590: stur            w0, [x2, #7]
    //     0x76c594: ldurb           w16, [x2, #-1]
    //     0x76c598: ldurb           w17, [x0, #-1]
    //     0x76c59c: and             x16, x17, x16, lsr #2
    //     0x76c5a0: tst             x16, HEAP, lsr #32
    //     0x76c5a4: b.eq            #0x76c5ac
    //     0x76c5a8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x76c5ac: ldr             x1, [fp, #0x10]
    // 0x76c5b0: r0 = size()
    //     0x76c5b0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76c5b4: LoadField: d0 = r0->field_7
    //     0x76c5b4: ldur            d0, [x0, #7]
    // 0x76c5b8: ldur            x1, [fp, #-0x10]
    // 0x76c5bc: LoadField: r2 = r1->field_f
    //     0x76c5bc: ldur            w2, [x1, #0xf]
    // 0x76c5c0: DecompressPointer r2
    //     0x76c5c0: add             x2, x2, HEAP, lsl #32
    // 0x76c5c4: LoadField: d1 = r2->field_7f
    //     0x76c5c4: ldur            d1, [x2, #0x7f]
    // 0x76c5c8: fadd            d2, d0, d1
    // 0x76c5cc: ldur            d0, [fp, #-0x30]
    // 0x76c5d0: fadd            d1, d0, d2
    // 0x76c5d4: r3 = inline_Allocate_Double()
    //     0x76c5d4: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x76c5d8: add             x3, x3, #0x10
    //     0x76c5dc: cmp             x0, x3
    //     0x76c5e0: b.ls            #0x76c714
    //     0x76c5e4: str             x3, [THR, #0x50]  ; THR::top
    //     0x76c5e8: sub             x3, x3, #0xf
    //     0x76c5ec: movz            x0, #0xe15c
    //     0x76c5f0: movk            x0, #0x3, lsl #16
    //     0x76c5f4: stur            x0, [x3, #-1]
    // 0x76c5f8: StoreField: r3->field_7 = d1
    //     0x76c5f8: stur            d1, [x3, #7]
    // 0x76c5fc: mov             x0, x3
    // 0x76c600: StoreField: r1->field_1b = r0
    //     0x76c600: stur            w0, [x1, #0x1b]
    //     0x76c604: ldurb           w16, [x1, #-1]
    //     0x76c608: ldurb           w17, [x0, #-1]
    //     0x76c60c: and             x16, x17, x16, lsr #2
    //     0x76c610: tst             x16, HEAP, lsr #32
    //     0x76c614: b.eq            #0x76c61c
    //     0x76c618: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76c61c: LoadField: r0 = r1->field_23
    //     0x76c61c: ldur            w0, [x1, #0x23]
    // 0x76c620: DecompressPointer r0
    //     0x76c620: add             x0, x0, HEAP, lsl #32
    // 0x76c624: LoadField: r4 = r2->field_73
    //     0x76c624: ldur            x4, [x2, #0x73]
    // 0x76c628: r2 = LoadInt32Instr(r0)
    //     0x76c628: sbfx            x2, x0, #1, #0x1f
    //     0x76c62c: tbz             w0, #0, #0x76c634
    //     0x76c630: ldur            x2, [x0, #7]
    // 0x76c634: cmp             x2, x4
    // 0x76c638: r16 = true
    //     0x76c638: add             x16, NULL, #0x20  ; true
    // 0x76c63c: r17 = false
    //     0x76c63c: add             x17, NULL, #0x30  ; false
    // 0x76c640: csel            x0, x16, x17, eq
    // 0x76c644: ldur            x5, [fp, #-0x18]
    // 0x76c648: ArrayStore: r5[0] = r0  ; List_4
    //     0x76c648: stur            w0, [x5, #0x17]
    // 0x76c64c: cmp             x2, x4
    // 0x76c650: b.ne            #0x76c674
    // 0x76c654: mov             x0, x3
    // 0x76c658: StoreField: r1->field_1f = r0
    //     0x76c658: stur            w0, [x1, #0x1f]
    //     0x76c65c: ldurb           w16, [x1, #-1]
    //     0x76c660: ldurb           w17, [x0, #-1]
    //     0x76c664: and             x16, x17, x16, lsr #2
    //     0x76c668: tst             x16, HEAP, lsr #32
    //     0x76c66c: b.eq            #0x76c674
    //     0x76c670: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76c674: r0 = Null
    //     0x76c674: mov             x0, NULL
    // 0x76c678: LeaveFrame
    //     0x76c678: mov             SP, fp
    //     0x76c67c: ldp             fp, lr, [SP], #0x10
    // 0x76c680: ret
    //     0x76c680: ret             
    // 0x76c684: r0 = StateError()
    //     0x76c684: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76c688: mov             x1, x0
    // 0x76c68c: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76c68c: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76c690: StoreField: r1->field_b = r0
    //     0x76c690: stur            w0, [x1, #0xb]
    // 0x76c694: mov             x0, x1
    // 0x76c698: r0 = Throw()
    //     0x76c698: bl              #0xec04b8  ; ThrowStub
    // 0x76c69c: brk             #0
    // 0x76c6a0: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76c6a0: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76c6a4: r0 = StateError()
    //     0x76c6a4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76c6a8: mov             x1, x0
    // 0x76c6ac: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76c6ac: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76c6b0: StoreField: r1->field_b = r0
    //     0x76c6b0: stur            w0, [x1, #0xb]
    // 0x76c6b4: mov             x0, x1
    // 0x76c6b8: r0 = Throw()
    //     0x76c6b8: bl              #0xec04b8  ; ThrowStub
    // 0x76c6bc: brk             #0
    // 0x76c6c0: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76c6c0: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76c6c4: r0 = StateError()
    //     0x76c6c4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76c6c8: mov             x1, x0
    // 0x76c6cc: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76c6cc: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76c6d0: StoreField: r1->field_b = r0
    //     0x76c6d0: stur            w0, [x1, #0xb]
    // 0x76c6d4: mov             x0, x1
    // 0x76c6d8: r0 = Throw()
    //     0x76c6d8: bl              #0xec04b8  ; ThrowStub
    // 0x76c6dc: brk             #0
    // 0x76c6e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76c6e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76c6e4: b               #0x76c090
    // 0x76c6e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76c6e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76c6ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76c6ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76c6f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76c6f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76c6f4: SaveReg d2
    //     0x76c6f4: str             q2, [SP, #-0x10]!
    // 0x76c6f8: stp             x1, x2, [SP, #-0x10]!
    // 0x76c6fc: r0 = AllocateDouble()
    //     0x76c6fc: bl              #0xec2254  ; AllocateDoubleStub
    // 0x76c700: ldp             x1, x2, [SP], #0x10
    // 0x76c704: RestoreReg d2
    //     0x76c704: ldr             q2, [SP], #0x10
    // 0x76c708: b               #0x76c420
    // 0x76c70c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76c70c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76c710: r0 = NullCastErrorSharedWithFPURegs()
    //     0x76c710: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x76c714: SaveReg d1
    //     0x76c714: str             q1, [SP, #-0x10]!
    // 0x76c718: stp             x1, x2, [SP, #-0x10]!
    // 0x76c71c: r0 = AllocateDouble()
    //     0x76c71c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x76c720: mov             x3, x0
    // 0x76c724: ldp             x1, x2, [SP], #0x10
    // 0x76c728: RestoreReg d1
    //     0x76c728: ldr             q1, [SP], #0x10
    // 0x76c72c: b               #0x76c5f8
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x76c730, size: 0x160
    // 0x76c730: EnterFrame
    //     0x76c730: stp             fp, lr, [SP, #-0x10]!
    //     0x76c734: mov             fp, SP
    // 0x76c738: AllocStack(0x10)
    //     0x76c738: sub             SP, SP, #0x10
    // 0x76c73c: SetupParameters()
    //     0x76c73c: ldr             x0, [fp, #0x18]
    //     0x76c740: ldur            w3, [x0, #0x17]
    //     0x76c744: add             x3, x3, HEAP, lsl #32
    //     0x76c748: stur            x3, [fp, #-8]
    // 0x76c74c: CheckStackOverflow
    //     0x76c74c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76c750: cmp             SP, x16
    //     0x76c754: b.ls            #0x76c870
    // 0x76c758: ldr             x0, [fp, #0x10]
    // 0x76c75c: r2 = Null
    //     0x76c75c: mov             x2, NULL
    // 0x76c760: r1 = Null
    //     0x76c760: mov             x1, NULL
    // 0x76c764: r4 = LoadClassIdInstr(r0)
    //     0x76c764: ldur            x4, [x0, #-1]
    //     0x76c768: ubfx            x4, x4, #0xc, #0x14
    // 0x76c76c: sub             x4, x4, #0xbba
    // 0x76c770: cmp             x4, #0x9a
    // 0x76c774: b.ls            #0x76c788
    // 0x76c778: r8 = RenderBox
    //     0x76c778: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x76c77c: r3 = Null
    //     0x76c77c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d1d0] Null
    //     0x76c780: ldr             x3, [x3, #0x1d0]
    // 0x76c784: r0 = RenderBox()
    //     0x76c784: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x76c788: ldur            x3, [fp, #-8]
    // 0x76c78c: LoadField: r0 = r3->field_f
    //     0x76c78c: ldur            w0, [x3, #0xf]
    // 0x76c790: DecompressPointer r0
    //     0x76c790: add             x0, x0, HEAP, lsl #32
    // 0x76c794: LoadField: r4 = r0->field_27
    //     0x76c794: ldur            w4, [x0, #0x27]
    // 0x76c798: DecompressPointer r4
    //     0x76c798: add             x4, x4, HEAP, lsl #32
    // 0x76c79c: stur            x4, [fp, #-0x10]
    // 0x76c7a0: cmp             w4, NULL
    // 0x76c7a4: b.eq            #0x76c854
    // 0x76c7a8: mov             x0, x4
    // 0x76c7ac: r2 = Null
    //     0x76c7ac: mov             x2, NULL
    // 0x76c7b0: r1 = Null
    //     0x76c7b0: mov             x1, NULL
    // 0x76c7b4: r4 = LoadClassIdInstr(r0)
    //     0x76c7b4: ldur            x4, [x0, #-1]
    //     0x76c7b8: ubfx            x4, x4, #0xc, #0x14
    // 0x76c7bc: sub             x4, x4, #0xc83
    // 0x76c7c0: cmp             x4, #1
    // 0x76c7c4: b.ls            #0x76c7d8
    // 0x76c7c8: r8 = BoxConstraints
    //     0x76c7c8: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76c7cc: r3 = Null
    //     0x76c7cc: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d1e0] Null
    //     0x76c7d0: ldr             x3, [x3, #0x1e0]
    // 0x76c7d4: r0 = BoxConstraints()
    //     0x76c7d4: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76c7d8: ldur            x0, [fp, #-0x10]
    // 0x76c7dc: LoadField: d0 = r0->field_f
    //     0x76c7dc: ldur            d0, [x0, #0xf]
    // 0x76c7e0: ldr             x1, [fp, #0x10]
    // 0x76c7e4: r0 = getMaxIntrinsicHeight()
    //     0x76c7e4: bl              #0x72d954  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicHeight
    // 0x76c7e8: ldur            x1, [fp, #-8]
    // 0x76c7ec: LoadField: r0 = r1->field_13
    //     0x76c7ec: ldur            w0, [x1, #0x13]
    // 0x76c7f0: DecompressPointer r0
    //     0x76c7f0: add             x0, x0, HEAP, lsl #32
    // 0x76c7f4: LoadField: d1 = r0->field_7
    //     0x76c7f4: ldur            d1, [x0, #7]
    // 0x76c7f8: fcmp            d0, d1
    // 0x76c7fc: b.le            #0x76c844
    // 0x76c800: r0 = inline_Allocate_Double()
    //     0x76c800: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x76c804: add             x0, x0, #0x10
    //     0x76c808: cmp             x2, x0
    //     0x76c80c: b.ls            #0x76c878
    //     0x76c810: str             x0, [THR, #0x50]  ; THR::top
    //     0x76c814: sub             x0, x0, #0xf
    //     0x76c818: movz            x2, #0xe15c
    //     0x76c81c: movk            x2, #0x3, lsl #16
    //     0x76c820: stur            x2, [x0, #-1]
    // 0x76c824: StoreField: r0->field_7 = d0
    //     0x76c824: stur            d0, [x0, #7]
    // 0x76c828: StoreField: r1->field_13 = r0
    //     0x76c828: stur            w0, [x1, #0x13]
    //     0x76c82c: ldurb           w16, [x1, #-1]
    //     0x76c830: ldurb           w17, [x0, #-1]
    //     0x76c834: and             x16, x17, x16, lsr #2
    //     0x76c838: tst             x16, HEAP, lsr #32
    //     0x76c83c: b.eq            #0x76c844
    //     0x76c840: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76c844: r0 = Null
    //     0x76c844: mov             x0, NULL
    // 0x76c848: LeaveFrame
    //     0x76c848: mov             SP, fp
    //     0x76c84c: ldp             fp, lr, [SP], #0x10
    // 0x76c850: ret
    //     0x76c850: ret             
    // 0x76c854: r0 = StateError()
    //     0x76c854: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76c858: mov             x1, x0
    // 0x76c85c: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76c85c: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76c860: StoreField: r1->field_b = r0
    //     0x76c860: stur            w0, [x1, #0xb]
    // 0x76c864: mov             x0, x1
    // 0x76c868: r0 = Throw()
    //     0x76c868: bl              #0xec04b8  ; ThrowStub
    // 0x76c86c: brk             #0
    // 0x76c870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76c870: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76c874: b               #0x76c758
    // 0x76c878: SaveReg d0
    //     0x76c878: str             q0, [SP, #-0x10]!
    // 0x76c87c: SaveReg r1
    //     0x76c87c: str             x1, [SP, #-8]!
    // 0x76c880: r0 = AllocateDouble()
    //     0x76c880: bl              #0xec2254  ; AllocateDoubleStub
    // 0x76c884: RestoreReg r1
    //     0x76c884: ldr             x1, [SP], #8
    // 0x76c888: RestoreReg d0
    //     0x76c888: ldr             q0, [SP], #0x10
    // 0x76c88c: b               #0x76c824
  }
  _ visitChildren(/* No info */) {
    // ** addr: 0x7867e8, size: 0xa0
    // 0x7867e8: EnterFrame
    //     0x7867e8: stp             fp, lr, [SP, #-0x10]!
    //     0x7867ec: mov             fp, SP
    // 0x7867f0: AllocStack(0x20)
    //     0x7867f0: sub             SP, SP, #0x20
    // 0x7867f4: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x7867f4: stur            x1, [fp, #-8]
    //     0x7867f8: mov             x16, x2
    //     0x7867fc: mov             x2, x1
    //     0x786800: mov             x1, x16
    //     0x786804: stur            x1, [fp, #-0x10]
    // 0x786808: CheckStackOverflow
    //     0x786808: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x78680c: cmp             SP, x16
    //     0x786810: b.ls            #0x786880
    // 0x786814: LoadField: r0 = r2->field_87
    //     0x786814: ldur            w0, [x2, #0x87]
    // 0x786818: DecompressPointer r0
    //     0x786818: add             x0, x0, HEAP, lsl #32
    // 0x78681c: cmp             w0, NULL
    // 0x786820: b.eq            #0x786838
    // 0x786824: stp             x0, x1, [SP]
    // 0x786828: mov             x0, x1
    // 0x78682c: ClosureCall
    //     0x78682c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x786830: ldur            x2, [x0, #0x1f]
    //     0x786834: blr             x2
    // 0x786838: ldur            x1, [fp, #-8]
    // 0x78683c: LoadField: r0 = r1->field_8b
    //     0x78683c: ldur            w0, [x1, #0x8b]
    // 0x786840: DecompressPointer r0
    //     0x786840: add             x0, x0, HEAP, lsl #32
    // 0x786844: cmp             w0, NULL
    // 0x786848: b.eq            #0x786864
    // 0x78684c: ldur            x16, [fp, #-0x10]
    // 0x786850: stp             x0, x16, [SP]
    // 0x786854: ldur            x0, [fp, #-0x10]
    // 0x786858: ClosureCall
    //     0x786858: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x78685c: ldur            x2, [x0, #0x1f]
    //     0x786860: blr             x2
    // 0x786864: ldur            x1, [fp, #-8]
    // 0x786868: ldur            x2, [fp, #-0x10]
    // 0x78686c: r0 = visitChildren()
    //     0x78686c: bl              #0x786888  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::visitChildren
    // 0x786870: r0 = Null
    //     0x786870: mov             x0, NULL
    // 0x786874: LeaveFrame
    //     0x786874: mov             SP, fp
    //     0x786878: ldp             fp, lr, [SP], #0x10
    // 0x78687c: ret
    //     0x78687c: ret             
    // 0x786880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x786880: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x786884: b               #0x786814
  }
  _ setupParentData(/* No info */) {
    // ** addr: 0x787d6c, size: 0xb8
    // 0x787d6c: EnterFrame
    //     0x787d6c: stp             fp, lr, [SP, #-0x10]!
    //     0x787d70: mov             fp, SP
    // 0x787d74: AllocStack(0x8)
    //     0x787d74: sub             SP, SP, #8
    // 0x787d78: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r4 */, dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x787d78: mov             x0, x2
    //     0x787d7c: mov             x4, x1
    //     0x787d80: mov             x3, x2
    //     0x787d84: stur            x2, [fp, #-8]
    // 0x787d88: r2 = Null
    //     0x787d88: mov             x2, NULL
    // 0x787d8c: r1 = Null
    //     0x787d8c: mov             x1, NULL
    // 0x787d90: r4 = 60
    //     0x787d90: movz            x4, #0x3c
    // 0x787d94: branchIfSmi(r0, 0x787da0)
    //     0x787d94: tbz             w0, #0, #0x787da0
    // 0x787d98: r4 = LoadClassIdInstr(r0)
    //     0x787d98: ldur            x4, [x0, #-1]
    //     0x787d9c: ubfx            x4, x4, #0xc, #0x14
    // 0x787da0: sub             x4, x4, #0xbba
    // 0x787da4: cmp             x4, #0x9a
    // 0x787da8: b.ls            #0x787dbc
    // 0x787dac: r8 = RenderBox
    //     0x787dac: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x787db0: r3 = Null
    //     0x787db0: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d0e0] Null
    //     0x787db4: ldr             x3, [x3, #0xe0]
    // 0x787db8: r0 = RenderBox()
    //     0x787db8: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x787dbc: ldur            x0, [fp, #-8]
    // 0x787dc0: LoadField: r1 = r0->field_7
    //     0x787dc0: ldur            w1, [x0, #7]
    // 0x787dc4: DecompressPointer r1
    //     0x787dc4: add             x1, x1, HEAP, lsl #32
    // 0x787dc8: r2 = LoadClassIdInstr(r1)
    //     0x787dc8: ldur            x2, [x1, #-1]
    //     0x787dcc: ubfx            x2, x2, #0xc, #0x14
    // 0x787dd0: cmp             x2, #0xc77
    // 0x787dd4: b.eq            #0x787e14
    // 0x787dd8: r1 = <RenderBox>
    //     0x787dd8: add             x1, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x787ddc: ldr             x1, [x1, #0x1d8]
    // 0x787de0: r0 = ToolbarItemsParentData()
    //     0x787de0: bl              #0x787a88  ; AllocateToolbarItemsParentDataStub -> ToolbarItemsParentData (size=0x1c)
    // 0x787de4: r1 = false
    //     0x787de4: add             x1, NULL, #0x30  ; false
    // 0x787de8: ArrayStore: r0[0] = r1  ; List_4
    //     0x787de8: stur            w1, [x0, #0x17]
    // 0x787dec: r1 = Instance_Offset
    //     0x787dec: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x787df0: StoreField: r0->field_7 = r1
    //     0x787df0: stur            w1, [x0, #7]
    // 0x787df4: ldur            x1, [fp, #-8]
    // 0x787df8: StoreField: r1->field_7 = r0
    //     0x787df8: stur            w0, [x1, #7]
    //     0x787dfc: ldurb           w16, [x1, #-1]
    //     0x787e00: ldurb           w17, [x0, #-1]
    //     0x787e04: and             x16, x17, x16, lsr #2
    //     0x787e08: tst             x16, HEAP, lsr #32
    //     0x787e0c: b.eq            #0x787e14
    //     0x787e10: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x787e14: r0 = Null
    //     0x787e14: mov             x0, NULL
    // 0x787e18: LeaveFrame
    //     0x787e18: mov             SP, fp
    //     0x787e1c: ldp             fp, lr, [SP], #0x10
    // 0x787e20: ret
    //     0x787e20: ret             
  }
  _ paint(/* No info */) {
    // ** addr: 0x796e94, size: 0x7c
    // 0x796e94: EnterFrame
    //     0x796e94: stp             fp, lr, [SP, #-0x10]!
    //     0x796e98: mov             fp, SP
    // 0x796e9c: AllocStack(0x18)
    //     0x796e9c: sub             SP, SP, #0x18
    // 0x796ea0: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x796ea0: stur            x1, [fp, #-8]
    //     0x796ea4: stur            x2, [fp, #-0x10]
    //     0x796ea8: stur            x3, [fp, #-0x18]
    // 0x796eac: CheckStackOverflow
    //     0x796eac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x796eb0: cmp             SP, x16
    //     0x796eb4: b.ls            #0x796f08
    // 0x796eb8: r1 = 3
    //     0x796eb8: movz            x1, #0x3
    // 0x796ebc: r0 = AllocateContext()
    //     0x796ebc: bl              #0xec126c  ; AllocateContextStub
    // 0x796ec0: mov             x1, x0
    // 0x796ec4: ldur            x0, [fp, #-8]
    // 0x796ec8: StoreField: r1->field_f = r0
    //     0x796ec8: stur            w0, [x1, #0xf]
    // 0x796ecc: ldur            x2, [fp, #-0x10]
    // 0x796ed0: StoreField: r1->field_13 = r2
    //     0x796ed0: stur            w2, [x1, #0x13]
    // 0x796ed4: ldur            x2, [fp, #-0x18]
    // 0x796ed8: ArrayStore: r1[0] = r2  ; List_4
    //     0x796ed8: stur            w2, [x1, #0x17]
    // 0x796edc: mov             x2, x1
    // 0x796ee0: r1 = Function '<anonymous closure>':.
    //     0x796ee0: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d0f0] AnonymousClosure: (0x796f10), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::paint (0x796e94)
    //     0x796ee4: ldr             x1, [x1, #0xf0]
    // 0x796ee8: r0 = AllocateClosure()
    //     0x796ee8: bl              #0xec1630  ; AllocateClosureStub
    // 0x796eec: ldur            x1, [fp, #-8]
    // 0x796ef0: mov             x2, x0
    // 0x796ef4: r0 = visitChildren()
    //     0x796ef4: bl              #0x7867e8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::visitChildren
    // 0x796ef8: r0 = Null
    //     0x796ef8: mov             x0, NULL
    // 0x796efc: LeaveFrame
    //     0x796efc: mov             SP, fp
    //     0x796f00: ldp             fp, lr, [SP], #0x10
    // 0x796f04: ret
    //     0x796f04: ret             
    // 0x796f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x796f08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x796f0c: b               #0x796eb8
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x796f10, size: 0x21c
    // 0x796f10: EnterFrame
    //     0x796f10: stp             fp, lr, [SP, #-0x10]!
    //     0x796f14: mov             fp, SP
    // 0x796f18: AllocStack(0x50)
    //     0x796f18: sub             SP, SP, #0x50
    // 0x796f1c: SetupParameters()
    //     0x796f1c: ldr             x0, [fp, #0x18]
    //     0x796f20: ldur            w3, [x0, #0x17]
    //     0x796f24: add             x3, x3, HEAP, lsl #32
    //     0x796f28: stur            x3, [fp, #-8]
    // 0x796f2c: CheckStackOverflow
    //     0x796f2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x796f30: cmp             SP, x16
    //     0x796f34: b.ls            #0x797120
    // 0x796f38: ldr             x0, [fp, #0x10]
    // 0x796f3c: r2 = Null
    //     0x796f3c: mov             x2, NULL
    // 0x796f40: r1 = Null
    //     0x796f40: mov             x1, NULL
    // 0x796f44: r4 = LoadClassIdInstr(r0)
    //     0x796f44: ldur            x4, [x0, #-1]
    //     0x796f48: ubfx            x4, x4, #0xc, #0x14
    // 0x796f4c: sub             x4, x4, #0xbba
    // 0x796f50: cmp             x4, #0x9a
    // 0x796f54: b.ls            #0x796f68
    // 0x796f58: r8 = RenderBox
    //     0x796f58: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x796f5c: r3 = Null
    //     0x796f5c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d0f8] Null
    //     0x796f60: ldr             x3, [x3, #0xf8]
    // 0x796f64: r0 = RenderBox()
    //     0x796f64: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x796f68: ldr             x3, [fp, #0x10]
    // 0x796f6c: LoadField: r4 = r3->field_7
    //     0x796f6c: ldur            w4, [x3, #7]
    // 0x796f70: DecompressPointer r4
    //     0x796f70: add             x4, x4, HEAP, lsl #32
    // 0x796f74: stur            x4, [fp, #-0x10]
    // 0x796f78: cmp             w4, NULL
    // 0x796f7c: b.eq            #0x797128
    // 0x796f80: mov             x0, x4
    // 0x796f84: r2 = Null
    //     0x796f84: mov             x2, NULL
    // 0x796f88: r1 = Null
    //     0x796f88: mov             x1, NULL
    // 0x796f8c: r4 = LoadClassIdInstr(r0)
    //     0x796f8c: ldur            x4, [x0, #-1]
    //     0x796f90: ubfx            x4, x4, #0xc, #0x14
    // 0x796f94: cmp             x4, #0xc77
    // 0x796f98: b.eq            #0x796fb0
    // 0x796f9c: r8 = ToolbarItemsParentData
    //     0x796f9c: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x796fa0: ldr             x8, [x8, #0x490]
    // 0x796fa4: r3 = Null
    //     0x796fa4: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d108] Null
    //     0x796fa8: ldr             x3, [x3, #0x108]
    // 0x796fac: r0 = DefaultTypeTest()
    //     0x796fac: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x796fb0: ldur            x0, [fp, #-0x10]
    // 0x796fb4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x796fb4: ldur            w1, [x0, #0x17]
    // 0x796fb8: DecompressPointer r1
    //     0x796fb8: add             x1, x1, HEAP, lsl #32
    // 0x796fbc: tbnz            w1, #4, #0x797110
    // 0x796fc0: ldur            x3, [fp, #-8]
    // 0x796fc4: LoadField: r1 = r0->field_7
    //     0x796fc4: ldur            w1, [x0, #7]
    // 0x796fc8: DecompressPointer r1
    //     0x796fc8: add             x1, x1, HEAP, lsl #32
    // 0x796fcc: ArrayLoad: r2 = r3[0]  ; List_4
    //     0x796fcc: ldur            w2, [x3, #0x17]
    // 0x796fd0: DecompressPointer r2
    //     0x796fd0: add             x2, x2, HEAP, lsl #32
    // 0x796fd4: r0 = +()
    //     0x796fd4: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x796fd8: mov             x4, x0
    // 0x796fdc: ldur            x0, [fp, #-8]
    // 0x796fe0: stur            x4, [fp, #-0x18]
    // 0x796fe4: LoadField: r1 = r0->field_13
    //     0x796fe4: ldur            w1, [x0, #0x13]
    // 0x796fe8: DecompressPointer r1
    //     0x796fe8: add             x1, x1, HEAP, lsl #32
    // 0x796fec: ldr             x2, [fp, #0x10]
    // 0x796ff0: mov             x3, x4
    // 0x796ff4: r0 = paintChild()
    //     0x796ff4: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x796ff8: ldur            x0, [fp, #-0x10]
    // 0x796ffc: LoadField: r1 = r0->field_13
    //     0x796ffc: ldur            w1, [x0, #0x13]
    // 0x797000: DecompressPointer r1
    //     0x797000: add             x1, x1, HEAP, lsl #32
    // 0x797004: cmp             w1, NULL
    // 0x797008: b.eq            #0x797018
    // 0x79700c: ldr             x2, [fp, #0x10]
    // 0x797010: ldur            x0, [fp, #-8]
    // 0x797014: b               #0x797038
    // 0x797018: ldr             x2, [fp, #0x10]
    // 0x79701c: ldur            x0, [fp, #-8]
    // 0x797020: LoadField: r1 = r0->field_f
    //     0x797020: ldur            w1, [x0, #0xf]
    // 0x797024: DecompressPointer r1
    //     0x797024: add             x1, x1, HEAP, lsl #32
    // 0x797028: LoadField: r3 = r1->field_87
    //     0x797028: ldur            w3, [x1, #0x87]
    // 0x79702c: DecompressPointer r3
    //     0x79702c: add             x3, x3, HEAP, lsl #32
    // 0x797030: cmp             w2, w3
    // 0x797034: b.ne            #0x797110
    // 0x797038: LoadField: r1 = r0->field_13
    //     0x797038: ldur            w1, [x0, #0x13]
    // 0x79703c: DecompressPointer r1
    //     0x79703c: add             x1, x1, HEAP, lsl #32
    // 0x797040: r0 = canvas()
    //     0x797040: bl              #0x789cf4  ; [package:flutter/src/rendering/object.dart] PaintingContext::canvas
    // 0x797044: ldr             x1, [fp, #0x10]
    // 0x797048: stur            x0, [fp, #-0x10]
    // 0x79704c: r0 = size()
    //     0x79704c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x797050: LoadField: d0 = r0->field_7
    //     0x797050: ldur            d0, [x0, #7]
    // 0x797054: stur            d0, [fp, #-0x38]
    // 0x797058: r0 = Offset()
    //     0x797058: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x79705c: ldur            d0, [fp, #-0x38]
    // 0x797060: StoreField: r0->field_7 = d0
    //     0x797060: stur            d0, [x0, #7]
    // 0x797064: StoreField: r0->field_f = rZR
    //     0x797064: stur            xzr, [x0, #0xf]
    // 0x797068: mov             x1, x0
    // 0x79706c: ldur            x2, [fp, #-0x18]
    // 0x797070: r0 = +()
    //     0x797070: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x797074: ldr             x1, [fp, #0x10]
    // 0x797078: stur            x0, [fp, #-0x20]
    // 0x79707c: r0 = size()
    //     0x79707c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x797080: LoadField: d0 = r0->field_7
    //     0x797080: ldur            d0, [x0, #7]
    // 0x797084: ldr             x1, [fp, #0x10]
    // 0x797088: stur            d0, [fp, #-0x38]
    // 0x79708c: r0 = size()
    //     0x79708c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x797090: LoadField: d0 = r0->field_f
    //     0x797090: ldur            d0, [x0, #0xf]
    // 0x797094: stur            d0, [fp, #-0x40]
    // 0x797098: r0 = Offset()
    //     0x797098: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x79709c: ldur            d0, [fp, #-0x38]
    // 0x7970a0: StoreField: r0->field_7 = d0
    //     0x7970a0: stur            d0, [x0, #7]
    // 0x7970a4: ldur            d0, [fp, #-0x40]
    // 0x7970a8: StoreField: r0->field_f = d0
    //     0x7970a8: stur            d0, [x0, #0xf]
    // 0x7970ac: mov             x1, x0
    // 0x7970b0: ldur            x2, [fp, #-0x18]
    // 0x7970b4: r0 = +()
    //     0x7970b4: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x7970b8: stur            x0, [fp, #-0x18]
    // 0x7970bc: r16 = 136
    //     0x7970bc: movz            x16, #0x88
    // 0x7970c0: stp             x16, NULL, [SP]
    // 0x7970c4: r0 = ByteData()
    //     0x7970c4: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0x7970c8: stur            x0, [fp, #-0x28]
    // 0x7970cc: r0 = Paint()
    //     0x7970cc: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0x7970d0: mov             x3, x0
    // 0x7970d4: ldur            x0, [fp, #-0x28]
    // 0x7970d8: stur            x3, [fp, #-0x30]
    // 0x7970dc: StoreField: r3->field_7 = r0
    //     0x7970dc: stur            w0, [x3, #7]
    // 0x7970e0: ldur            x0, [fp, #-8]
    // 0x7970e4: LoadField: r1 = r0->field_f
    //     0x7970e4: ldur            w1, [x0, #0xf]
    // 0x7970e8: DecompressPointer r1
    //     0x7970e8: add             x1, x1, HEAP, lsl #32
    // 0x7970ec: LoadField: r2 = r1->field_7b
    //     0x7970ec: ldur            w2, [x1, #0x7b]
    // 0x7970f0: DecompressPointer r2
    //     0x7970f0: add             x2, x2, HEAP, lsl #32
    // 0x7970f4: mov             x1, x3
    // 0x7970f8: r0 = color=()
    //     0x7970f8: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0x7970fc: ldur            x1, [fp, #-0x10]
    // 0x797100: ldur            x2, [fp, #-0x20]
    // 0x797104: ldur            x3, [fp, #-0x18]
    // 0x797108: ldur            x5, [fp, #-0x30]
    // 0x79710c: r0 = drawLine()
    //     0x79710c: bl              #0x79712c  ; [dart:ui] _NativeCanvas::drawLine
    // 0x797110: r0 = Null
    //     0x797110: mov             x0, NULL
    // 0x797114: LeaveFrame
    //     0x797114: mov             SP, fp
    //     0x797118: ldp             fp, lr, [SP], #0x10
    // 0x79711c: ret
    //     0x79711c: ret             
    // 0x797120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x797120: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x797124: b               #0x796f38
    // 0x797128: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x797128: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  set _ nextButton=(/* No info */) {
    // ** addr: 0x7aec0c, size: 0x74
    // 0x7aec0c: EnterFrame
    //     0x7aec0c: stp             fp, lr, [SP, #-0x10]!
    //     0x7aec10: mov             fp, SP
    // 0x7aec14: AllocStack(0x8)
    //     0x7aec14: sub             SP, SP, #8
    // 0x7aec18: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3 */)
    //     0x7aec18: mov             x0, x1
    //     0x7aec1c: mov             x3, x2
    //     0x7aec20: stur            x1, [fp, #-8]
    // 0x7aec24: CheckStackOverflow
    //     0x7aec24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7aec28: cmp             SP, x16
    //     0x7aec2c: b.ls            #0x7aec78
    // 0x7aec30: LoadField: r2 = r0->field_8b
    //     0x7aec30: ldur            w2, [x0, #0x8b]
    // 0x7aec34: DecompressPointer r2
    //     0x7aec34: add             x2, x2, HEAP, lsl #32
    // 0x7aec38: mov             x1, x0
    // 0x7aec3c: r5 = Instance__CupertinoTextSelectionToolbarItemsSlot
    //     0x7aec3c: add             x5, PP, #0x5c, lsl #12  ; [pp+0x5cf40] Obj!_CupertinoTextSelectionToolbarItemsSlot@e37081
    //     0x7aec40: ldr             x5, [x5, #0xf40]
    // 0x7aec44: r0 = _updateChild()
    //     0x7aec44: bl              #0x7aec80  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::_updateChild
    // 0x7aec48: ldur            x1, [fp, #-8]
    // 0x7aec4c: StoreField: r1->field_8b = r0
    //     0x7aec4c: stur            w0, [x1, #0x8b]
    //     0x7aec50: ldurb           w16, [x1, #-1]
    //     0x7aec54: ldurb           w17, [x0, #-1]
    //     0x7aec58: and             x16, x17, x16, lsr #2
    //     0x7aec5c: tst             x16, HEAP, lsr #32
    //     0x7aec60: b.eq            #0x7aec68
    //     0x7aec64: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7aec68: r0 = Null
    //     0x7aec68: mov             x0, NULL
    // 0x7aec6c: LeaveFrame
    //     0x7aec6c: mov             SP, fp
    //     0x7aec70: ldp             fp, lr, [SP], #0x10
    // 0x7aec74: ret
    //     0x7aec74: ret             
    // 0x7aec78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7aec78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7aec7c: b               #0x7aec30
  }
  _ _updateChild(/* No info */) {
    // ** addr: 0x7aec80, size: 0x98
    // 0x7aec80: EnterFrame
    //     0x7aec80: stp             fp, lr, [SP, #-0x10]!
    //     0x7aec84: mov             fp, SP
    // 0x7aec88: AllocStack(0x18)
    //     0x7aec88: sub             SP, SP, #0x18
    // 0x7aec8c: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r0, fp-0x18 */)
    //     0x7aec8c: mov             x4, x1
    //     0x7aec90: mov             x0, x5
    //     0x7aec94: stur            x1, [fp, #-8]
    //     0x7aec98: stur            x3, [fp, #-0x10]
    //     0x7aec9c: stur            x5, [fp, #-0x18]
    // 0x7aeca0: CheckStackOverflow
    //     0x7aeca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7aeca4: cmp             SP, x16
    //     0x7aeca8: b.ls            #0x7aed10
    // 0x7aecac: cmp             w2, NULL
    // 0x7aecb0: b.eq            #0x7aecd0
    // 0x7aecb4: mov             x1, x4
    // 0x7aecb8: r0 = dropChild()
    //     0x7aecb8: bl              #0x8019dc  ; [package:flutter/src/rendering/object.dart] RenderObject::dropChild
    // 0x7aecbc: ldur            x0, [fp, #-8]
    // 0x7aecc0: LoadField: r1 = r0->field_67
    //     0x7aecc0: ldur            w1, [x0, #0x67]
    // 0x7aecc4: DecompressPointer r1
    //     0x7aecc4: add             x1, x1, HEAP, lsl #32
    // 0x7aecc8: ldur            x2, [fp, #-0x18]
    // 0x7aeccc: r0 = remove()
    //     0x7aeccc: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x7aecd0: ldur            x0, [fp, #-0x10]
    // 0x7aecd4: cmp             w0, NULL
    // 0x7aecd8: b.eq            #0x7aed00
    // 0x7aecdc: ldur            x4, [fp, #-8]
    // 0x7aece0: LoadField: r1 = r4->field_67
    //     0x7aece0: ldur            w1, [x4, #0x67]
    // 0x7aece4: DecompressPointer r1
    //     0x7aece4: add             x1, x1, HEAP, lsl #32
    // 0x7aece8: ldur            x2, [fp, #-0x18]
    // 0x7aecec: mov             x3, x0
    // 0x7aecf0: r0 = []=()
    //     0x7aecf0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7aecf4: ldur            x1, [fp, #-8]
    // 0x7aecf8: ldur            x2, [fp, #-0x10]
    // 0x7aecfc: r0 = adoptChild()
    //     0x7aecfc: bl              #0x8057b8  ; [package:flutter/src/rendering/object.dart] RenderObject::adoptChild
    // 0x7aed00: ldur            x0, [fp, #-0x10]
    // 0x7aed04: LeaveFrame
    //     0x7aed04: mov             SP, fp
    //     0x7aed08: ldp             fp, lr, [SP], #0x10
    // 0x7aed0c: ret
    //     0x7aed0c: ret             
    // 0x7aed10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7aed10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7aed14: b               #0x7aecac
  }
  set _ backButton=(/* No info */) {
    // ** addr: 0x7aed3c, size: 0x74
    // 0x7aed3c: EnterFrame
    //     0x7aed3c: stp             fp, lr, [SP, #-0x10]!
    //     0x7aed40: mov             fp, SP
    // 0x7aed44: AllocStack(0x8)
    //     0x7aed44: sub             SP, SP, #8
    // 0x7aed48: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3 */)
    //     0x7aed48: mov             x0, x1
    //     0x7aed4c: mov             x3, x2
    //     0x7aed50: stur            x1, [fp, #-8]
    // 0x7aed54: CheckStackOverflow
    //     0x7aed54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7aed58: cmp             SP, x16
    //     0x7aed5c: b.ls            #0x7aeda8
    // 0x7aed60: LoadField: r2 = r0->field_87
    //     0x7aed60: ldur            w2, [x0, #0x87]
    // 0x7aed64: DecompressPointer r2
    //     0x7aed64: add             x2, x2, HEAP, lsl #32
    // 0x7aed68: mov             x1, x0
    // 0x7aed6c: r5 = Instance__CupertinoTextSelectionToolbarItemsSlot
    //     0x7aed6c: add             x5, PP, #0x5c, lsl #12  ; [pp+0x5cf38] Obj!_CupertinoTextSelectionToolbarItemsSlot@e370a1
    //     0x7aed70: ldr             x5, [x5, #0xf38]
    // 0x7aed74: r0 = _updateChild()
    //     0x7aed74: bl              #0x7aec80  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::_updateChild
    // 0x7aed78: ldur            x1, [fp, #-8]
    // 0x7aed7c: StoreField: r1->field_87 = r0
    //     0x7aed7c: stur            w0, [x1, #0x87]
    //     0x7aed80: ldurb           w16, [x1, #-1]
    //     0x7aed84: ldurb           w17, [x0, #-1]
    //     0x7aed88: and             x16, x17, x16, lsr #2
    //     0x7aed8c: tst             x16, HEAP, lsr #32
    //     0x7aed90: b.eq            #0x7aed98
    //     0x7aed94: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7aed98: r0 = Null
    //     0x7aed98: mov             x0, NULL
    // 0x7aed9c: LeaveFrame
    //     0x7aed9c: mov             SP, fp
    //     0x7aeda0: ldp             fp, lr, [SP], #0x10
    // 0x7aeda4: ret
    //     0x7aeda4: ret             
    // 0x7aeda8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7aeda8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7aedac: b               #0x7aed60
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x7fd300, size: 0x1c0
    // 0x7fd300: EnterFrame
    //     0x7fd300: stp             fp, lr, [SP, #-0x10]!
    //     0x7fd304: mov             fp, SP
    // 0x7fd308: AllocStack(0x30)
    //     0x7fd308: sub             SP, SP, #0x30
    // 0x7fd30c: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */, dynamic _ /* r3 => r5, fp-0x28 */)
    //     0x7fd30c: mov             x4, x1
    //     0x7fd310: mov             x5, x3
    //     0x7fd314: stur            x3, [fp, #-0x28]
    //     0x7fd318: mov             x3, x2
    //     0x7fd31c: stur            x1, [fp, #-0x18]
    //     0x7fd320: stur            x2, [fp, #-0x20]
    // 0x7fd324: CheckStackOverflow
    //     0x7fd324: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fd328: cmp             SP, x16
    //     0x7fd32c: b.ls            #0x7fd4ac
    // 0x7fd330: LoadField: r0 = r4->field_63
    //     0x7fd330: ldur            w0, [x4, #0x63]
    // 0x7fd334: DecompressPointer r0
    //     0x7fd334: add             x0, x0, HEAP, lsl #32
    // 0x7fd338: mov             x6, x0
    // 0x7fd33c: stur            x6, [fp, #-0x10]
    // 0x7fd340: CheckStackOverflow
    //     0x7fd340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fd344: cmp             SP, x16
    //     0x7fd348: b.ls            #0x7fd4b4
    // 0x7fd34c: cmp             w6, NULL
    // 0x7fd350: b.eq            #0x7fd444
    // 0x7fd354: LoadField: r7 = r6->field_7
    //     0x7fd354: ldur            w7, [x6, #7]
    // 0x7fd358: DecompressPointer r7
    //     0x7fd358: add             x7, x7, HEAP, lsl #32
    // 0x7fd35c: stur            x7, [fp, #-8]
    // 0x7fd360: cmp             w7, NULL
    // 0x7fd364: b.eq            #0x7fd4bc
    // 0x7fd368: mov             x0, x7
    // 0x7fd36c: r2 = Null
    //     0x7fd36c: mov             x2, NULL
    // 0x7fd370: r1 = Null
    //     0x7fd370: mov             x1, NULL
    // 0x7fd374: r4 = LoadClassIdInstr(r0)
    //     0x7fd374: ldur            x4, [x0, #-1]
    //     0x7fd378: ubfx            x4, x4, #0xc, #0x14
    // 0x7fd37c: cmp             x4, #0xc77
    // 0x7fd380: b.eq            #0x7fd398
    // 0x7fd384: r8 = ToolbarItemsParentData
    //     0x7fd384: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x7fd388: ldr             x8, [x8, #0x490]
    // 0x7fd38c: r3 = Null
    //     0x7fd38c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d0b8] Null
    //     0x7fd390: ldr             x3, [x3, #0xb8]
    // 0x7fd394: r0 = DefaultTypeTest()
    //     0x7fd394: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7fd398: ldur            x0, [fp, #-8]
    // 0x7fd39c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7fd39c: ldur            w1, [x0, #0x17]
    // 0x7fd3a0: DecompressPointer r1
    //     0x7fd3a0: add             x1, x1, HEAP, lsl #32
    // 0x7fd3a4: stur            x1, [fp, #-0x30]
    // 0x7fd3a8: tbz             w1, #4, #0x7fd3bc
    // 0x7fd3ac: LoadField: r1 = r0->field_f
    //     0x7fd3ac: ldur            w1, [x0, #0xf]
    // 0x7fd3b0: DecompressPointer r1
    //     0x7fd3b0: add             x1, x1, HEAP, lsl #32
    // 0x7fd3b4: mov             x6, x1
    // 0x7fd3b8: b               #0x7fd424
    // 0x7fd3bc: ldur            x2, [fp, #-0x10]
    // 0x7fd3c0: r1 = 1
    //     0x7fd3c0: movz            x1, #0x1
    // 0x7fd3c4: r0 = AllocateContext()
    //     0x7fd3c4: bl              #0xec126c  ; AllocateContextStub
    // 0x7fd3c8: mov             x1, x0
    // 0x7fd3cc: ldur            x0, [fp, #-0x10]
    // 0x7fd3d0: StoreField: r1->field_f = r0
    //     0x7fd3d0: stur            w0, [x1, #0xf]
    // 0x7fd3d4: ldur            x0, [fp, #-0x30]
    // 0x7fd3d8: tbnz            w0, #4, #0x7fd414
    // 0x7fd3dc: ldur            x0, [fp, #-8]
    // 0x7fd3e0: LoadField: r3 = r0->field_7
    //     0x7fd3e0: ldur            w3, [x0, #7]
    // 0x7fd3e4: DecompressPointer r3
    //     0x7fd3e4: add             x3, x3, HEAP, lsl #32
    // 0x7fd3e8: mov             x2, x1
    // 0x7fd3ec: stur            x3, [fp, #-0x10]
    // 0x7fd3f0: r1 = Function '<anonymous closure>': static.
    //     0x7fd3f0: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d0c8] AnonymousClosure: (0x7fcdd0), in [package:flutter/src/rendering/shifted_box.dart] RenderShiftedBox::hitTestChildren (0x7fccd4)
    //     0x7fd3f4: ldr             x1, [x1, #0xc8]
    // 0x7fd3f8: r0 = AllocateClosure()
    //     0x7fd3f8: bl              #0xec1630  ; AllocateClosureStub
    // 0x7fd3fc: ldur            x1, [fp, #-0x20]
    // 0x7fd400: mov             x2, x0
    // 0x7fd404: ldur            x3, [fp, #-0x10]
    // 0x7fd408: ldur            x5, [fp, #-0x28]
    // 0x7fd40c: r0 = addWithPaintOffset()
    //     0x7fd40c: bl              #0x7fc1f8  ; [package:flutter/src/rendering/box.dart] BoxHitTestResult::addWithPaintOffset
    // 0x7fd410: tbz             w0, #4, #0x7fd434
    // 0x7fd414: ldur            x0, [fp, #-8]
    // 0x7fd418: LoadField: r1 = r0->field_f
    //     0x7fd418: ldur            w1, [x0, #0xf]
    // 0x7fd41c: DecompressPointer r1
    //     0x7fd41c: add             x1, x1, HEAP, lsl #32
    // 0x7fd420: mov             x6, x1
    // 0x7fd424: ldur            x4, [fp, #-0x18]
    // 0x7fd428: ldur            x3, [fp, #-0x20]
    // 0x7fd42c: ldur            x5, [fp, #-0x28]
    // 0x7fd430: b               #0x7fd33c
    // 0x7fd434: r0 = true
    //     0x7fd434: add             x0, NULL, #0x20  ; true
    // 0x7fd438: LeaveFrame
    //     0x7fd438: mov             SP, fp
    //     0x7fd43c: ldp             fp, lr, [SP], #0x10
    // 0x7fd440: ret
    //     0x7fd440: ret             
    // 0x7fd444: mov             x0, x4
    // 0x7fd448: LoadField: r1 = r0->field_87
    //     0x7fd448: ldur            w1, [x0, #0x87]
    // 0x7fd44c: DecompressPointer r1
    //     0x7fd44c: add             x1, x1, HEAP, lsl #32
    // 0x7fd450: ldur            x2, [fp, #-0x20]
    // 0x7fd454: ldur            x3, [fp, #-0x28]
    // 0x7fd458: r0 = hitTestChild()
    //     0x7fd458: bl              #0x7fd4c0  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::hitTestChild
    // 0x7fd45c: tbnz            w0, #4, #0x7fd470
    // 0x7fd460: r0 = true
    //     0x7fd460: add             x0, NULL, #0x20  ; true
    // 0x7fd464: LeaveFrame
    //     0x7fd464: mov             SP, fp
    //     0x7fd468: ldp             fp, lr, [SP], #0x10
    // 0x7fd46c: ret
    //     0x7fd46c: ret             
    // 0x7fd470: ldur            x0, [fp, #-0x18]
    // 0x7fd474: LoadField: r1 = r0->field_8b
    //     0x7fd474: ldur            w1, [x0, #0x8b]
    // 0x7fd478: DecompressPointer r1
    //     0x7fd478: add             x1, x1, HEAP, lsl #32
    // 0x7fd47c: ldur            x2, [fp, #-0x20]
    // 0x7fd480: ldur            x3, [fp, #-0x28]
    // 0x7fd484: r0 = hitTestChild()
    //     0x7fd484: bl              #0x7fd4c0  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::hitTestChild
    // 0x7fd488: tbnz            w0, #4, #0x7fd49c
    // 0x7fd48c: r0 = true
    //     0x7fd48c: add             x0, NULL, #0x20  ; true
    // 0x7fd490: LeaveFrame
    //     0x7fd490: mov             SP, fp
    //     0x7fd494: ldp             fp, lr, [SP], #0x10
    // 0x7fd498: ret
    //     0x7fd498: ret             
    // 0x7fd49c: r0 = false
    //     0x7fd49c: add             x0, NULL, #0x30  ; false
    // 0x7fd4a0: LeaveFrame
    //     0x7fd4a0: mov             SP, fp
    //     0x7fd4a4: ldp             fp, lr, [SP], #0x10
    // 0x7fd4a8: ret
    //     0x7fd4a8: ret             
    // 0x7fd4ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fd4ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fd4b0: b               #0x7fd330
    // 0x7fd4b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fd4b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fd4b8: b               #0x7fd34c
    // 0x7fd4bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fd4bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ hitTestChild(/* No info */) {
    // ** addr: 0x7fd4c0, size: 0x10c
    // 0x7fd4c0: EnterFrame
    //     0x7fd4c0: stp             fp, lr, [SP, #-0x10]!
    //     0x7fd4c4: mov             fp, SP
    // 0x7fd4c8: AllocStack(0x28)
    //     0x7fd4c8: sub             SP, SP, #0x28
    // 0x7fd4cc: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */)
    //     0x7fd4cc: mov             x0, x1
    //     0x7fd4d0: stur            x1, [fp, #-8]
    //     0x7fd4d4: mov             x1, x2
    //     0x7fd4d8: mov             x5, x3
    //     0x7fd4dc: stur            x2, [fp, #-0x10]
    //     0x7fd4e0: stur            x3, [fp, #-0x18]
    // 0x7fd4e4: CheckStackOverflow
    //     0x7fd4e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fd4e8: cmp             SP, x16
    //     0x7fd4ec: b.ls            #0x7fd5c0
    // 0x7fd4f0: r1 = 1
    //     0x7fd4f0: movz            x1, #0x1
    // 0x7fd4f4: r0 = AllocateContext()
    //     0x7fd4f4: bl              #0xec126c  ; AllocateContextStub
    // 0x7fd4f8: mov             x3, x0
    // 0x7fd4fc: ldur            x0, [fp, #-8]
    // 0x7fd500: stur            x3, [fp, #-0x28]
    // 0x7fd504: StoreField: r3->field_f = r0
    //     0x7fd504: stur            w0, [x3, #0xf]
    // 0x7fd508: cmp             w0, NULL
    // 0x7fd50c: b.ne            #0x7fd520
    // 0x7fd510: r0 = false
    //     0x7fd510: add             x0, NULL, #0x30  ; false
    // 0x7fd514: LeaveFrame
    //     0x7fd514: mov             SP, fp
    //     0x7fd518: ldp             fp, lr, [SP], #0x10
    // 0x7fd51c: ret
    //     0x7fd51c: ret             
    // 0x7fd520: LoadField: r4 = r0->field_7
    //     0x7fd520: ldur            w4, [x0, #7]
    // 0x7fd524: DecompressPointer r4
    //     0x7fd524: add             x4, x4, HEAP, lsl #32
    // 0x7fd528: stur            x4, [fp, #-0x20]
    // 0x7fd52c: cmp             w4, NULL
    // 0x7fd530: b.eq            #0x7fd5c8
    // 0x7fd534: mov             x0, x4
    // 0x7fd538: r2 = Null
    //     0x7fd538: mov             x2, NULL
    // 0x7fd53c: r1 = Null
    //     0x7fd53c: mov             x1, NULL
    // 0x7fd540: r4 = LoadClassIdInstr(r0)
    //     0x7fd540: ldur            x4, [x0, #-1]
    //     0x7fd544: ubfx            x4, x4, #0xc, #0x14
    // 0x7fd548: cmp             x4, #0xc77
    // 0x7fd54c: b.eq            #0x7fd564
    // 0x7fd550: r8 = ToolbarItemsParentData
    //     0x7fd550: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x7fd554: ldr             x8, [x8, #0x490]
    // 0x7fd558: r3 = Null
    //     0x7fd558: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d0d0] Null
    //     0x7fd55c: ldr             x3, [x3, #0xd0]
    // 0x7fd560: r0 = DefaultTypeTest()
    //     0x7fd560: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7fd564: ldur            x0, [fp, #-0x20]
    // 0x7fd568: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7fd568: ldur            w1, [x0, #0x17]
    // 0x7fd56c: DecompressPointer r1
    //     0x7fd56c: add             x1, x1, HEAP, lsl #32
    // 0x7fd570: tbz             w1, #4, #0x7fd584
    // 0x7fd574: r0 = false
    //     0x7fd574: add             x0, NULL, #0x30  ; false
    // 0x7fd578: LeaveFrame
    //     0x7fd578: mov             SP, fp
    //     0x7fd57c: ldp             fp, lr, [SP], #0x10
    // 0x7fd580: ret
    //     0x7fd580: ret             
    // 0x7fd584: LoadField: r3 = r0->field_7
    //     0x7fd584: ldur            w3, [x0, #7]
    // 0x7fd588: DecompressPointer r3
    //     0x7fd588: add             x3, x3, HEAP, lsl #32
    // 0x7fd58c: ldur            x2, [fp, #-0x28]
    // 0x7fd590: stur            x3, [fp, #-8]
    // 0x7fd594: r1 = Function '<anonymous closure>': static.
    //     0x7fd594: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d0c8] AnonymousClosure: (0x7fcdd0), in [package:flutter/src/rendering/shifted_box.dart] RenderShiftedBox::hitTestChildren (0x7fccd4)
    //     0x7fd598: ldr             x1, [x1, #0xc8]
    // 0x7fd59c: r0 = AllocateClosure()
    //     0x7fd59c: bl              #0xec1630  ; AllocateClosureStub
    // 0x7fd5a0: ldur            x1, [fp, #-0x10]
    // 0x7fd5a4: mov             x2, x0
    // 0x7fd5a8: ldur            x3, [fp, #-8]
    // 0x7fd5ac: ldur            x5, [fp, #-0x18]
    // 0x7fd5b0: r0 = addWithPaintOffset()
    //     0x7fd5b0: bl              #0x7fc1f8  ; [package:flutter/src/rendering/box.dart] BoxHitTestResult::addWithPaintOffset
    // 0x7fd5b4: LeaveFrame
    //     0x7fd5b4: mov             SP, fp
    //     0x7fd5b8: ldp             fp, lr, [SP], #0x10
    // 0x7fd5bc: ret
    //     0x7fd5bc: ret             
    // 0x7fd5c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fd5c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fd5c4: b               #0x7fd4f0
    // 0x7fd5c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fd5c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitChildrenForSemantics(/* No info */) {
    // ** addr: 0x8009fc, size: 0x68
    // 0x8009fc: EnterFrame
    //     0x8009fc: stp             fp, lr, [SP, #-0x10]!
    //     0x800a00: mov             fp, SP
    // 0x800a04: AllocStack(0x10)
    //     0x800a04: sub             SP, SP, #0x10
    // 0x800a08: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x800a08: stur            x1, [fp, #-8]
    //     0x800a0c: stur            x2, [fp, #-0x10]
    // 0x800a10: CheckStackOverflow
    //     0x800a10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x800a14: cmp             SP, x16
    //     0x800a18: b.ls            #0x800a5c
    // 0x800a1c: r1 = 1
    //     0x800a1c: movz            x1, #0x1
    // 0x800a20: r0 = AllocateContext()
    //     0x800a20: bl              #0xec126c  ; AllocateContextStub
    // 0x800a24: mov             x1, x0
    // 0x800a28: ldur            x0, [fp, #-0x10]
    // 0x800a2c: StoreField: r1->field_f = r0
    //     0x800a2c: stur            w0, [x1, #0xf]
    // 0x800a30: mov             x2, x1
    // 0x800a34: r1 = Function '<anonymous closure>':.
    //     0x800a34: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d058] AnonymousClosure: (0x800a64), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::visitChildrenForSemantics (0x8009fc)
    //     0x800a38: ldr             x1, [x1, #0x58]
    // 0x800a3c: r0 = AllocateClosure()
    //     0x800a3c: bl              #0xec1630  ; AllocateClosureStub
    // 0x800a40: ldur            x1, [fp, #-8]
    // 0x800a44: mov             x2, x0
    // 0x800a48: r0 = visitChildren()
    //     0x800a48: bl              #0x7867e8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::visitChildren
    // 0x800a4c: r0 = Null
    //     0x800a4c: mov             x0, NULL
    // 0x800a50: LeaveFrame
    //     0x800a50: mov             SP, fp
    //     0x800a54: ldp             fp, lr, [SP], #0x10
    // 0x800a58: ret
    //     0x800a58: ret             
    // 0x800a5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x800a5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x800a60: b               #0x800a1c
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x800a64, size: 0xf0
    // 0x800a64: EnterFrame
    //     0x800a64: stp             fp, lr, [SP, #-0x10]!
    //     0x800a68: mov             fp, SP
    // 0x800a6c: AllocStack(0x20)
    //     0x800a6c: sub             SP, SP, #0x20
    // 0x800a70: SetupParameters()
    //     0x800a70: ldr             x0, [fp, #0x18]
    //     0x800a74: ldur            w3, [x0, #0x17]
    //     0x800a78: add             x3, x3, HEAP, lsl #32
    //     0x800a7c: stur            x3, [fp, #-8]
    // 0x800a80: CheckStackOverflow
    //     0x800a80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x800a84: cmp             SP, x16
    //     0x800a88: b.ls            #0x800b48
    // 0x800a8c: ldr             x0, [fp, #0x10]
    // 0x800a90: r2 = Null
    //     0x800a90: mov             x2, NULL
    // 0x800a94: r1 = Null
    //     0x800a94: mov             x1, NULL
    // 0x800a98: r4 = LoadClassIdInstr(r0)
    //     0x800a98: ldur            x4, [x0, #-1]
    //     0x800a9c: ubfx            x4, x4, #0xc, #0x14
    // 0x800aa0: sub             x4, x4, #0xbba
    // 0x800aa4: cmp             x4, #0x9a
    // 0x800aa8: b.ls            #0x800abc
    // 0x800aac: r8 = RenderBox
    //     0x800aac: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x800ab0: r3 = Null
    //     0x800ab0: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d060] Null
    //     0x800ab4: ldr             x3, [x3, #0x60]
    // 0x800ab8: r0 = RenderBox()
    //     0x800ab8: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x800abc: ldr             x3, [fp, #0x10]
    // 0x800ac0: LoadField: r4 = r3->field_7
    //     0x800ac0: ldur            w4, [x3, #7]
    // 0x800ac4: DecompressPointer r4
    //     0x800ac4: add             x4, x4, HEAP, lsl #32
    // 0x800ac8: stur            x4, [fp, #-0x10]
    // 0x800acc: cmp             w4, NULL
    // 0x800ad0: b.eq            #0x800b50
    // 0x800ad4: mov             x0, x4
    // 0x800ad8: r2 = Null
    //     0x800ad8: mov             x2, NULL
    // 0x800adc: r1 = Null
    //     0x800adc: mov             x1, NULL
    // 0x800ae0: r4 = LoadClassIdInstr(r0)
    //     0x800ae0: ldur            x4, [x0, #-1]
    //     0x800ae4: ubfx            x4, x4, #0xc, #0x14
    // 0x800ae8: cmp             x4, #0xc77
    // 0x800aec: b.eq            #0x800b04
    // 0x800af0: r8 = ToolbarItemsParentData
    //     0x800af0: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x800af4: ldr             x8, [x8, #0x490]
    // 0x800af8: r3 = Null
    //     0x800af8: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d070] Null
    //     0x800afc: ldr             x3, [x3, #0x70]
    // 0x800b00: r0 = DefaultTypeTest()
    //     0x800b00: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x800b04: ldur            x0, [fp, #-0x10]
    // 0x800b08: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x800b08: ldur            w1, [x0, #0x17]
    // 0x800b0c: DecompressPointer r1
    //     0x800b0c: add             x1, x1, HEAP, lsl #32
    // 0x800b10: tbnz            w1, #4, #0x800b38
    // 0x800b14: ldur            x0, [fp, #-8]
    // 0x800b18: LoadField: r1 = r0->field_f
    //     0x800b18: ldur            w1, [x0, #0xf]
    // 0x800b1c: DecompressPointer r1
    //     0x800b1c: add             x1, x1, HEAP, lsl #32
    // 0x800b20: ldr             x16, [fp, #0x10]
    // 0x800b24: stp             x16, x1, [SP]
    // 0x800b28: mov             x0, x1
    // 0x800b2c: ClosureCall
    //     0x800b2c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x800b30: ldur            x2, [x0, #0x1f]
    //     0x800b34: blr             x2
    // 0x800b38: r0 = Null
    //     0x800b38: mov             x0, NULL
    // 0x800b3c: LeaveFrame
    //     0x800b3c: mov             SP, fp
    //     0x800b40: ldp             fp, lr, [SP], #0x10
    // 0x800b44: ret
    //     0x800b44: ret             
    // 0x800b48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x800b48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x800b4c: b               #0x800a8c
    // 0x800b50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x800b50: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ detach(/* No info */) {
    // ** addr: 0x806078, size: 0x140
    // 0x806078: EnterFrame
    //     0x806078: stp             fp, lr, [SP, #-0x10]!
    //     0x80607c: mov             fp, SP
    // 0x806080: AllocStack(0x18)
    //     0x806080: sub             SP, SP, #0x18
    // 0x806084: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r0, fp-0x8 */)
    //     0x806084: mov             x0, x1
    //     0x806088: stur            x1, [fp, #-8]
    // 0x80608c: CheckStackOverflow
    //     0x80608c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x806090: cmp             SP, x16
    //     0x806094: b.ls            #0x8061a8
    // 0x806098: mov             x1, x0
    // 0x80609c: r0 = detach()
    //     0x80609c: bl              #0x8061b8  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::detach
    // 0x8060a0: ldur            x0, [fp, #-8]
    // 0x8060a4: LoadField: r4 = r0->field_67
    //     0x8060a4: ldur            w4, [x0, #0x67]
    // 0x8060a8: DecompressPointer r4
    //     0x8060a8: add             x4, x4, HEAP, lsl #32
    // 0x8060ac: stur            x4, [fp, #-0x10]
    // 0x8060b0: LoadField: r2 = r4->field_7
    //     0x8060b0: ldur            w2, [x4, #7]
    // 0x8060b4: DecompressPointer r2
    //     0x8060b4: add             x2, x2, HEAP, lsl #32
    // 0x8060b8: r1 = Null
    //     0x8060b8: mov             x1, NULL
    // 0x8060bc: r3 = <X1>
    //     0x8060bc: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x8060c0: r0 = Null
    //     0x8060c0: mov             x0, NULL
    // 0x8060c4: cmp             x2, x0
    // 0x8060c8: b.eq            #0x8060d8
    // 0x8060cc: r30 = InstantiateTypeArgumentsStub
    //     0x8060cc: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8060d0: LoadField: r30 = r30->field_7
    //     0x8060d0: ldur            lr, [lr, #7]
    // 0x8060d4: blr             lr
    // 0x8060d8: mov             x1, x0
    // 0x8060dc: r0 = _CompactIterable()
    //     0x8060dc: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x8060e0: mov             x1, x0
    // 0x8060e4: ldur            x0, [fp, #-0x10]
    // 0x8060e8: StoreField: r1->field_b = r0
    //     0x8060e8: stur            w0, [x1, #0xb]
    // 0x8060ec: r0 = -1
    //     0x8060ec: movn            x0, #0
    // 0x8060f0: StoreField: r1->field_f = r0
    //     0x8060f0: stur            x0, [x1, #0xf]
    // 0x8060f4: r0 = 2
    //     0x8060f4: movz            x0, #0x2
    // 0x8060f8: ArrayStore: r1[0] = r0  ; List_8
    //     0x8060f8: stur            x0, [x1, #0x17]
    // 0x8060fc: r0 = iterator()
    //     0x8060fc: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x806100: stur            x0, [fp, #-0x10]
    // 0x806104: LoadField: r2 = r0->field_7
    //     0x806104: ldur            w2, [x0, #7]
    // 0x806108: DecompressPointer r2
    //     0x806108: add             x2, x2, HEAP, lsl #32
    // 0x80610c: stur            x2, [fp, #-8]
    // 0x806110: CheckStackOverflow
    //     0x806110: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x806114: cmp             SP, x16
    //     0x806118: b.ls            #0x8061b0
    // 0x80611c: mov             x1, x0
    // 0x806120: r0 = moveNext()
    //     0x806120: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x806124: tbnz            w0, #4, #0x806198
    // 0x806128: ldur            x3, [fp, #-0x10]
    // 0x80612c: LoadField: r4 = r3->field_33
    //     0x80612c: ldur            w4, [x3, #0x33]
    // 0x806130: DecompressPointer r4
    //     0x806130: add             x4, x4, HEAP, lsl #32
    // 0x806134: stur            x4, [fp, #-0x18]
    // 0x806138: cmp             w4, NULL
    // 0x80613c: b.ne            #0x806170
    // 0x806140: mov             x0, x4
    // 0x806144: ldur            x2, [fp, #-8]
    // 0x806148: r1 = Null
    //     0x806148: mov             x1, NULL
    // 0x80614c: cmp             w2, NULL
    // 0x806150: b.eq            #0x806170
    // 0x806154: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x806154: ldur            w4, [x2, #0x17]
    // 0x806158: DecompressPointer r4
    //     0x806158: add             x4, x4, HEAP, lsl #32
    // 0x80615c: r8 = X0
    //     0x80615c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x806160: LoadField: r9 = r4->field_7
    //     0x806160: ldur            x9, [x4, #7]
    // 0x806164: r3 = Null
    //     0x806164: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d098] Null
    //     0x806168: ldr             x3, [x3, #0x98]
    // 0x80616c: blr             x9
    // 0x806170: ldur            x1, [fp, #-0x18]
    // 0x806174: r0 = LoadClassIdInstr(r1)
    //     0x806174: ldur            x0, [x1, #-1]
    //     0x806178: ubfx            x0, x0, #0xc, #0x14
    // 0x80617c: r0 = GDT[cid_x0 + 0xeec9]()
    //     0x80617c: movz            x17, #0xeec9
    //     0x806180: add             lr, x0, x17
    //     0x806184: ldr             lr, [x21, lr, lsl #3]
    //     0x806188: blr             lr
    // 0x80618c: ldur            x0, [fp, #-0x10]
    // 0x806190: ldur            x2, [fp, #-8]
    // 0x806194: b               #0x806110
    // 0x806198: r0 = Null
    //     0x806198: mov             x0, NULL
    // 0x80619c: LeaveFrame
    //     0x80619c: mov             SP, fp
    //     0x8061a0: ldp             fp, lr, [SP], #0x10
    // 0x8061a4: ret
    //     0x8061a4: ret             
    // 0x8061a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8061a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8061ac: b               #0x806098
    // 0x8061b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8061b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8061b4: b               #0x80611c
  }
  _ redepthChildren(/* No info */) {
    // ** addr: 0x8085d4, size: 0x64
    // 0x8085d4: EnterFrame
    //     0x8085d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8085d8: mov             fp, SP
    // 0x8085dc: AllocStack(0x8)
    //     0x8085dc: sub             SP, SP, #8
    // 0x8085e0: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r1, fp-0x8 */)
    //     0x8085e0: stur            x1, [fp, #-8]
    // 0x8085e4: CheckStackOverflow
    //     0x8085e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8085e8: cmp             SP, x16
    //     0x8085ec: b.ls            #0x808630
    // 0x8085f0: r1 = 1
    //     0x8085f0: movz            x1, #0x1
    // 0x8085f4: r0 = AllocateContext()
    //     0x8085f4: bl              #0xec126c  ; AllocateContextStub
    // 0x8085f8: mov             x1, x0
    // 0x8085fc: ldur            x0, [fp, #-8]
    // 0x808600: StoreField: r1->field_f = r0
    //     0x808600: stur            w0, [x1, #0xf]
    // 0x808604: mov             x2, x1
    // 0x808608: r1 = Function '<anonymous closure>':.
    //     0x808608: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d080] AnonymousClosure: (0x808638), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::redepthChildren (0x8085d4)
    //     0x80860c: ldr             x1, [x1, #0x80]
    // 0x808610: r0 = AllocateClosure()
    //     0x808610: bl              #0xec1630  ; AllocateClosureStub
    // 0x808614: ldur            x1, [fp, #-8]
    // 0x808618: mov             x2, x0
    // 0x80861c: r0 = visitChildren()
    //     0x80861c: bl              #0x7867e8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::visitChildren
    // 0x808620: r0 = Null
    //     0x808620: mov             x0, NULL
    // 0x808624: LeaveFrame
    //     0x808624: mov             SP, fp
    //     0x808628: ldp             fp, lr, [SP], #0x10
    // 0x80862c: ret
    //     0x80862c: ret             
    // 0x808630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x808630: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x808634: b               #0x8085f0
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x808638, size: 0x84
    // 0x808638: EnterFrame
    //     0x808638: stp             fp, lr, [SP, #-0x10]!
    //     0x80863c: mov             fp, SP
    // 0x808640: AllocStack(0x8)
    //     0x808640: sub             SP, SP, #8
    // 0x808644: SetupParameters()
    //     0x808644: ldr             x0, [fp, #0x18]
    //     0x808648: ldur            w3, [x0, #0x17]
    //     0x80864c: add             x3, x3, HEAP, lsl #32
    //     0x808650: stur            x3, [fp, #-8]
    // 0x808654: CheckStackOverflow
    //     0x808654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x808658: cmp             SP, x16
    //     0x80865c: b.ls            #0x8086b4
    // 0x808660: ldr             x0, [fp, #0x10]
    // 0x808664: r2 = Null
    //     0x808664: mov             x2, NULL
    // 0x808668: r1 = Null
    //     0x808668: mov             x1, NULL
    // 0x80866c: r4 = LoadClassIdInstr(r0)
    //     0x80866c: ldur            x4, [x0, #-1]
    //     0x808670: ubfx            x4, x4, #0xc, #0x14
    // 0x808674: sub             x4, x4, #0xbba
    // 0x808678: cmp             x4, #0x9a
    // 0x80867c: b.ls            #0x808690
    // 0x808680: r8 = RenderBox
    //     0x808680: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x808684: r3 = Null
    //     0x808684: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d088] Null
    //     0x808688: ldr             x3, [x3, #0x88]
    // 0x80868c: r0 = RenderBox()
    //     0x80868c: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x808690: ldur            x0, [fp, #-8]
    // 0x808694: LoadField: r1 = r0->field_f
    //     0x808694: ldur            w1, [x0, #0xf]
    // 0x808698: DecompressPointer r1
    //     0x808698: add             x1, x1, HEAP, lsl #32
    // 0x80869c: ldr             x2, [fp, #0x10]
    // 0x8086a0: r0 = redepthChild()
    //     0x8086a0: bl              #0x8058b0  ; [package:flutter/src/rendering/object.dart] RenderObject::redepthChild
    // 0x8086a4: r0 = Null
    //     0x8086a4: mov             x0, NULL
    // 0x8086a8: LeaveFrame
    //     0x8086a8: mov             SP, fp
    //     0x8086ac: ldp             fp, lr, [SP], #0x10
    // 0x8086b0: ret
    //     0x8086b0: ret             
    // 0x8086b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8086b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8086b8: b               #0x808660
  }
  _ _RenderCupertinoTextSelectionToolbarItems(/* No info */) {
    // ** addr: 0x8608d4, size: 0xe8
    // 0x8608d4: EnterFrame
    //     0x8608d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8608d8: mov             fp, SP
    // 0x8608dc: AllocStack(0x30)
    //     0x8608dc: sub             SP, SP, #0x30
    // 0x8608e0: r0 = Sentinel
    //     0x8608e0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8608e4: stur            x1, [fp, #-8]
    // 0x8608e8: mov             x16, x2
    // 0x8608ec: mov             x2, x1
    // 0x8608f0: mov             x1, x16
    // 0x8608f4: stur            x1, [fp, #-0x10]
    // 0x8608f8: stur            x3, [fp, #-0x18]
    // 0x8608fc: stur            d0, [fp, #-0x20]
    // 0x860900: CheckStackOverflow
    //     0x860900: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x860904: cmp             SP, x16
    //     0x860908: b.ls            #0x8609b4
    // 0x86090c: StoreField: r2->field_6b = r0
    //     0x86090c: stur            w0, [x2, #0x6b]
    // 0x860910: StoreField: r2->field_6f = r0
    //     0x860910: stur            w0, [x2, #0x6f]
    // 0x860914: r16 = <_CupertinoTextSelectionToolbarItemsSlot, RenderBox>
    //     0x860914: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5a628] TypeArguments: <_CupertinoTextSelectionToolbarItemsSlot, RenderBox>
    //     0x860918: ldr             x16, [x16, #0x628]
    // 0x86091c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x860920: stp             lr, x16, [SP]
    // 0x860924: r0 = Map._fromLiteral()
    //     0x860924: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x860928: ldur            x1, [fp, #-8]
    // 0x86092c: StoreField: r1->field_67 = r0
    //     0x86092c: stur            w0, [x1, #0x67]
    //     0x860930: ldurb           w16, [x1, #-1]
    //     0x860934: ldurb           w17, [x0, #-1]
    //     0x860938: and             x16, x17, x16, lsr #2
    //     0x86093c: tst             x16, HEAP, lsr #32
    //     0x860940: b.eq            #0x860948
    //     0x860944: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x860948: ldur            x0, [fp, #-0x10]
    // 0x86094c: StoreField: r1->field_7b = r0
    //     0x86094c: stur            w0, [x1, #0x7b]
    //     0x860950: ldurb           w16, [x1, #-1]
    //     0x860954: ldurb           w17, [x0, #-1]
    //     0x860958: and             x16, x17, x16, lsr #2
    //     0x86095c: tst             x16, HEAP, lsr #32
    //     0x860960: b.eq            #0x860968
    //     0x860964: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x860968: ldur            d0, [fp, #-0x20]
    // 0x86096c: StoreField: r1->field_7f = d0
    //     0x86096c: stur            d0, [x1, #0x7f]
    // 0x860970: ldur            x0, [fp, #-0x18]
    // 0x860974: StoreField: r1->field_73 = r0
    //     0x860974: stur            x0, [x1, #0x73]
    // 0x860978: StoreField: r1->field_57 = rZR
    //     0x860978: stur            xzr, [x1, #0x57]
    // 0x86097c: r0 = _LayoutCacheStorage()
    //     0x86097c: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x860980: ldur            x1, [fp, #-8]
    // 0x860984: StoreField: r1->field_4f = r0
    //     0x860984: stur            w0, [x1, #0x4f]
    //     0x860988: ldurb           w16, [x1, #-1]
    //     0x86098c: ldurb           w17, [x0, #-1]
    //     0x860990: and             x16, x17, x16, lsr #2
    //     0x860994: tst             x16, HEAP, lsr #32
    //     0x860998: b.eq            #0x8609a0
    //     0x86099c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8609a0: r0 = RenderObject()
    //     0x8609a0: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x8609a4: r0 = Null
    //     0x8609a4: mov             x0, NULL
    // 0x8609a8: LeaveFrame
    //     0x8609a8: mov             SP, fp
    //     0x8609ac: ldp             fp, lr, [SP], #0x10
    // 0x8609b0: ret
    //     0x8609b0: ret             
    // 0x8609b4: r0 = StackOverflowSharedWithFPURegs()
    //     0x8609b4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x8609b8: b               #0x86090c
  }
  set _ dividerWidth=(/* No info */) {
    // ** addr: 0xc72964, size: 0x50
    // 0xc72964: EnterFrame
    //     0xc72964: stp             fp, lr, [SP, #-0x10]!
    //     0xc72968: mov             fp, SP
    // 0xc7296c: CheckStackOverflow
    //     0xc7296c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc72970: cmp             SP, x16
    //     0xc72974: b.ls            #0xc729ac
    // 0xc72978: LoadField: d1 = r1->field_7f
    //     0xc72978: ldur            d1, [x1, #0x7f]
    // 0xc7297c: fcmp            d0, d1
    // 0xc72980: b.ne            #0xc72994
    // 0xc72984: r0 = Null
    //     0xc72984: mov             x0, NULL
    // 0xc72988: LeaveFrame
    //     0xc72988: mov             SP, fp
    //     0xc7298c: ldp             fp, lr, [SP], #0x10
    // 0xc72990: ret
    //     0xc72990: ret             
    // 0xc72994: StoreField: r1->field_7f = d0
    //     0xc72994: stur            d0, [x1, #0x7f]
    // 0xc72998: r0 = markNeedsLayout()
    //     0xc72998: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc7299c: r0 = Null
    //     0xc7299c: mov             x0, NULL
    // 0xc729a0: LeaveFrame
    //     0xc729a0: mov             SP, fp
    //     0xc729a4: ldp             fp, lr, [SP], #0x10
    // 0xc729a8: ret
    //     0xc729a8: ret             
    // 0xc729ac: r0 = StackOverflowSharedWithFPURegs()
    //     0xc729ac: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc729b0: b               #0xc72978
  }
  set _ dividerColor=(/* No info */) {
    // ** addr: 0xc729b4, size: 0x88
    // 0xc729b4: EnterFrame
    //     0xc729b4: stp             fp, lr, [SP, #-0x10]!
    //     0xc729b8: mov             fp, SP
    // 0xc729bc: AllocStack(0x20)
    //     0xc729bc: sub             SP, SP, #0x20
    // 0xc729c0: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc729c0: mov             x0, x2
    //     0xc729c4: stur            x1, [fp, #-8]
    //     0xc729c8: stur            x2, [fp, #-0x10]
    // 0xc729cc: CheckStackOverflow
    //     0xc729cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc729d0: cmp             SP, x16
    //     0xc729d4: b.ls            #0xc72a34
    // 0xc729d8: LoadField: r2 = r1->field_7b
    //     0xc729d8: ldur            w2, [x1, #0x7b]
    // 0xc729dc: DecompressPointer r2
    //     0xc729dc: add             x2, x2, HEAP, lsl #32
    // 0xc729e0: stp             x2, x0, [SP]
    // 0xc729e4: r0 = ==()
    //     0xc729e4: bl              #0xd48828  ; [package:flutter/src/cupertino/colors.dart] CupertinoDynamicColor::==
    // 0xc729e8: tbnz            w0, #4, #0xc729fc
    // 0xc729ec: r0 = Null
    //     0xc729ec: mov             x0, NULL
    // 0xc729f0: LeaveFrame
    //     0xc729f0: mov             SP, fp
    //     0xc729f4: ldp             fp, lr, [SP], #0x10
    // 0xc729f8: ret
    //     0xc729f8: ret             
    // 0xc729fc: ldur            x1, [fp, #-8]
    // 0xc72a00: ldur            x0, [fp, #-0x10]
    // 0xc72a04: StoreField: r1->field_7b = r0
    //     0xc72a04: stur            w0, [x1, #0x7b]
    //     0xc72a08: ldurb           w16, [x1, #-1]
    //     0xc72a0c: ldurb           w17, [x0, #-1]
    //     0xc72a10: and             x16, x17, x16, lsr #2
    //     0xc72a14: tst             x16, HEAP, lsr #32
    //     0xc72a18: b.eq            #0xc72a20
    //     0xc72a1c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc72a20: r0 = markNeedsLayout()
    //     0xc72a20: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc72a24: r0 = Null
    //     0xc72a24: mov             x0, NULL
    // 0xc72a28: LeaveFrame
    //     0xc72a28: mov             SP, fp
    //     0xc72a2c: ldp             fp, lr, [SP], #0x10
    // 0xc72a30: ret
    //     0xc72a30: ret             
    // 0xc72a34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc72a34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc72a38: b               #0xc729d8
  }
  set _ page=(/* No info */) {
    // ** addr: 0xc72a3c, size: 0x50
    // 0xc72a3c: EnterFrame
    //     0xc72a3c: stp             fp, lr, [SP, #-0x10]!
    //     0xc72a40: mov             fp, SP
    // 0xc72a44: CheckStackOverflow
    //     0xc72a44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc72a48: cmp             SP, x16
    //     0xc72a4c: b.ls            #0xc72a84
    // 0xc72a50: LoadField: r0 = r1->field_73
    //     0xc72a50: ldur            x0, [x1, #0x73]
    // 0xc72a54: cmp             x2, x0
    // 0xc72a58: b.ne            #0xc72a6c
    // 0xc72a5c: r0 = Null
    //     0xc72a5c: mov             x0, NULL
    // 0xc72a60: LeaveFrame
    //     0xc72a60: mov             SP, fp
    //     0xc72a64: ldp             fp, lr, [SP], #0x10
    // 0xc72a68: ret
    //     0xc72a68: ret             
    // 0xc72a6c: StoreField: r1->field_73 = r2
    //     0xc72a6c: stur            x2, [x1, #0x73]
    // 0xc72a70: r0 = markNeedsLayout()
    //     0xc72a70: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc72a74: r0 = Null
    //     0xc72a74: mov             x0, NULL
    // 0xc72a78: LeaveFrame
    //     0xc72a78: mov             SP, fp
    //     0xc72a7c: ldp             fp, lr, [SP], #0x10
    // 0xc72a80: ret
    //     0xc72a80: ret             
    // 0xc72a84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc72a84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc72a88: b               #0xc72a50
  }
}

// class id: 3092, size: 0x6c, field offset: 0x5c
class _RenderCupertinoTextSelectionToolbarShape extends RenderShiftedBox {

  _ computeDryBaseline(/* No info */) {
    // ** addr: 0x73ccdc, size: 0x13c
    // 0x73ccdc: EnterFrame
    //     0x73ccdc: stp             fp, lr, [SP, #-0x10]!
    //     0x73cce0: mov             fp, SP
    // 0x73cce4: AllocStack(0x20)
    //     0x73cce4: sub             SP, SP, #0x20
    // 0x73cce8: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x73cce8: mov             x5, x1
    //     0x73ccec: mov             x4, x2
    //     0x73ccf0: stur            x1, [fp, #-8]
    //     0x73ccf4: stur            x2, [fp, #-0x10]
    //     0x73ccf8: stur            x3, [fp, #-0x18]
    // 0x73ccfc: CheckStackOverflow
    //     0x73ccfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73cd00: cmp             SP, x16
    //     0x73cd04: b.ls            #0x73cdfc
    // 0x73cd08: mov             x0, x4
    // 0x73cd0c: r2 = Null
    //     0x73cd0c: mov             x2, NULL
    // 0x73cd10: r1 = Null
    //     0x73cd10: mov             x1, NULL
    // 0x73cd14: r4 = 60
    //     0x73cd14: movz            x4, #0x3c
    // 0x73cd18: branchIfSmi(r0, 0x73cd24)
    //     0x73cd18: tbz             w0, #0, #0x73cd24
    // 0x73cd1c: r4 = LoadClassIdInstr(r0)
    //     0x73cd1c: ldur            x4, [x0, #-1]
    //     0x73cd20: ubfx            x4, x4, #0xc, #0x14
    // 0x73cd24: sub             x4, x4, #0xc83
    // 0x73cd28: cmp             x4, #1
    // 0x73cd2c: b.ls            #0x73cd40
    // 0x73cd30: r8 = BoxConstraints
    //     0x73cd30: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x73cd34: r3 = Null
    //     0x73cd34: add             x3, PP, #0x50, lsl #12  ; [pp+0x508d8] Null
    //     0x73cd38: ldr             x3, [x3, #0x8d8]
    // 0x73cd3c: r0 = BoxConstraints()
    //     0x73cd3c: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x73cd40: ldur            x0, [fp, #-8]
    // 0x73cd44: LoadField: r3 = r0->field_57
    //     0x73cd44: ldur            w3, [x0, #0x57]
    // 0x73cd48: DecompressPointer r3
    //     0x73cd48: add             x3, x3, HEAP, lsl #32
    // 0x73cd4c: stur            x3, [fp, #-0x20]
    // 0x73cd50: cmp             w3, NULL
    // 0x73cd54: b.ne            #0x73cd68
    // 0x73cd58: r0 = Null
    //     0x73cd58: mov             x0, NULL
    // 0x73cd5c: LeaveFrame
    //     0x73cd5c: mov             SP, fp
    //     0x73cd60: ldp             fp, lr, [SP], #0x10
    // 0x73cd64: ret
    //     0x73cd64: ret             
    // 0x73cd68: mov             x1, x0
    // 0x73cd6c: ldur            x2, [fp, #-0x10]
    // 0x73cd70: r0 = _constraintsForChild()
    //     0x73cd70: bl              #0x73ce98  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_constraintsForChild
    // 0x73cd74: ldur            x1, [fp, #-0x20]
    // 0x73cd78: mov             x2, x0
    // 0x73cd7c: ldur            x3, [fp, #-0x18]
    // 0x73cd80: stur            x0, [fp, #-0x10]
    // 0x73cd84: r0 = getDryBaseline()
    //     0x73cd84: bl              #0x730f54  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryBaseline
    // 0x73cd88: stur            x0, [fp, #-0x18]
    // 0x73cd8c: cmp             w0, NULL
    // 0x73cd90: b.ne            #0x73cd9c
    // 0x73cd94: r0 = Null
    //     0x73cd94: mov             x0, NULL
    // 0x73cd98: b               #0x73cdf0
    // 0x73cd9c: ldur            x1, [fp, #-0x20]
    // 0x73cda0: ldur            x2, [fp, #-0x10]
    // 0x73cda4: r0 = getDryLayout()
    //     0x73cda4: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x73cda8: ldur            x1, [fp, #-8]
    // 0x73cdac: mov             x2, x0
    // 0x73cdb0: r0 = _computeChildOffset()
    //     0x73cdb0: bl              #0x73ce18  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_computeChildOffset
    // 0x73cdb4: LoadField: d0 = r0->field_f
    //     0x73cdb4: ldur            d0, [x0, #0xf]
    // 0x73cdb8: ldur            x1, [fp, #-0x18]
    // 0x73cdbc: LoadField: d1 = r1->field_7
    //     0x73cdbc: ldur            d1, [x1, #7]
    // 0x73cdc0: fadd            d2, d1, d0
    // 0x73cdc4: r1 = inline_Allocate_Double()
    //     0x73cdc4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x73cdc8: add             x1, x1, #0x10
    //     0x73cdcc: cmp             x2, x1
    //     0x73cdd0: b.ls            #0x73ce04
    //     0x73cdd4: str             x1, [THR, #0x50]  ; THR::top
    //     0x73cdd8: sub             x1, x1, #0xf
    //     0x73cddc: movz            x2, #0xe15c
    //     0x73cde0: movk            x2, #0x3, lsl #16
    //     0x73cde4: stur            x2, [x1, #-1]
    // 0x73cde8: StoreField: r1->field_7 = d2
    //     0x73cde8: stur            d2, [x1, #7]
    // 0x73cdec: mov             x0, x1
    // 0x73cdf0: LeaveFrame
    //     0x73cdf0: mov             SP, fp
    //     0x73cdf4: ldp             fp, lr, [SP], #0x10
    // 0x73cdf8: ret
    //     0x73cdf8: ret             
    // 0x73cdfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73cdfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73ce00: b               #0x73cd08
    // 0x73ce04: SaveReg d2
    //     0x73ce04: str             q2, [SP, #-0x10]!
    // 0x73ce08: r0 = AllocateDouble()
    //     0x73ce08: bl              #0xec2254  ; AllocateDoubleStub
    // 0x73ce0c: mov             x1, x0
    // 0x73ce10: RestoreReg d2
    //     0x73ce10: ldr             q2, [SP], #0x10
    // 0x73ce14: b               #0x73cde8
  }
  _ _computeChildOffset(/* No info */) {
    // ** addr: 0x73ce18, size: 0x58
    // 0x73ce18: EnterFrame
    //     0x73ce18: stp             fp, lr, [SP, #-0x10]!
    //     0x73ce1c: mov             fp, SP
    // 0x73ce20: AllocStack(0x8)
    //     0x73ce20: sub             SP, SP, #8
    // 0x73ce24: CheckStackOverflow
    //     0x73ce24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73ce28: cmp             SP, x16
    //     0x73ce2c: b.ls            #0x73ce68
    // 0x73ce30: LoadField: d0 = r2->field_f
    //     0x73ce30: ldur            d0, [x2, #0xf]
    // 0x73ce34: r0 = _isAbove()
    //     0x73ce34: bl              #0x73ce70  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_isAbove
    // 0x73ce38: tbnz            w0, #4, #0x73ce44
    // 0x73ce3c: d0 = -7.000000
    //     0x73ce3c: fmov            d0, #-7.00000000
    // 0x73ce40: b               #0x73ce48
    // 0x73ce44: d0 = 0.000000
    //     0x73ce44: eor             v0.16b, v0.16b, v0.16b
    // 0x73ce48: stur            d0, [fp, #-8]
    // 0x73ce4c: r0 = Offset()
    //     0x73ce4c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x73ce50: StoreField: r0->field_7 = rZR
    //     0x73ce50: stur            xzr, [x0, #7]
    // 0x73ce54: ldur            d0, [fp, #-8]
    // 0x73ce58: StoreField: r0->field_f = d0
    //     0x73ce58: stur            d0, [x0, #0xf]
    // 0x73ce5c: LeaveFrame
    //     0x73ce5c: mov             SP, fp
    //     0x73ce60: ldp             fp, lr, [SP], #0x10
    // 0x73ce64: ret
    //     0x73ce64: ret             
    // 0x73ce68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73ce68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73ce6c: b               #0x73ce30
  }
  _ _isAbove(/* No info */) {
    // ** addr: 0x73ce70, size: 0x28
    // 0x73ce70: d1 = 14.000000
    //     0x73ce70: fmov            d1, #14.00000000
    // 0x73ce74: LoadField: r2 = r1->field_5b
    //     0x73ce74: ldur            w2, [x1, #0x5b]
    // 0x73ce78: DecompressPointer r2
    //     0x73ce78: add             x2, x2, HEAP, lsl #32
    // 0x73ce7c: LoadField: d2 = r2->field_f
    //     0x73ce7c: ldur            d2, [x2, #0xf]
    // 0x73ce80: fsub            d3, d0, d1
    // 0x73ce84: fcmp            d2, d3
    // 0x73ce88: r16 = true
    //     0x73ce88: add             x16, NULL, #0x20  ; true
    // 0x73ce8c: r17 = false
    //     0x73ce8c: add             x17, NULL, #0x30  ; false
    // 0x73ce90: csel            x0, x16, x17, ge
    // 0x73ce94: ret
    //     0x73ce94: ret             
  }
  _ _constraintsForChild(/* No info */) {
    // ** addr: 0x73ce98, size: 0x6c
    // 0x73ce98: EnterFrame
    //     0x73ce98: stp             fp, lr, [SP, #-0x10]!
    //     0x73ce9c: mov             fp, SP
    // 0x73cea0: AllocStack(0x10)
    //     0x73cea0: sub             SP, SP, #0x10
    // 0x73cea4: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0x73cea4: mov             x0, x1
    //     0x73cea8: mov             x1, x2
    //     0x73ceac: stur            x2, [fp, #-8]
    // 0x73ceb0: CheckStackOverflow
    //     0x73ceb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73ceb4: cmp             SP, x16
    //     0x73ceb8: b.ls            #0x73cefc
    // 0x73cebc: r0 = BoxConstraints()
    //     0x73cebc: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x73cec0: d0 = 30.000000
    //     0x73cec0: fmov            d0, #30.00000000
    // 0x73cec4: stur            x0, [fp, #-0x10]
    // 0x73cec8: StoreField: r0->field_7 = d0
    //     0x73cec8: stur            d0, [x0, #7]
    // 0x73cecc: d0 = inf
    //     0x73cecc: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x73ced0: StoreField: r0->field_f = d0
    //     0x73ced0: stur            d0, [x0, #0xf]
    // 0x73ced4: ArrayStore: r0[0] = rZR  ; List_8
    //     0x73ced4: stur            xzr, [x0, #0x17]
    // 0x73ced8: StoreField: r0->field_1f = d0
    //     0x73ced8: stur            d0, [x0, #0x1f]
    // 0x73cedc: ldur            x1, [fp, #-8]
    // 0x73cee0: r0 = loosen()
    //     0x73cee0: bl              #0x73cf04  ; [package:flutter/src/rendering/box.dart] BoxConstraints::loosen
    // 0x73cee4: ldur            x1, [fp, #-0x10]
    // 0x73cee8: mov             x2, x0
    // 0x73ceec: r0 = enforce()
    //     0x73ceec: bl              #0x733638  ; [package:flutter/src/rendering/box.dart] BoxConstraints::enforce
    // 0x73cef0: LeaveFrame
    //     0x73cef0: mov             SP, fp
    //     0x73cef4: ldp             fp, lr, [SP], #0x10
    // 0x73cef8: ret
    //     0x73cef8: ret             
    // 0x73cefc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73cefc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73cf00: b               #0x73cebc
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x768dd8, size: 0x1e8
    // 0x768dd8: EnterFrame
    //     0x768dd8: stp             fp, lr, [SP, #-0x10]!
    //     0x768ddc: mov             fp, SP
    // 0x768de0: AllocStack(0x30)
    //     0x768de0: sub             SP, SP, #0x30
    // 0x768de4: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r3, fp-0x18 */)
    //     0x768de4: mov             x3, x1
    //     0x768de8: stur            x1, [fp, #-0x18]
    // 0x768dec: CheckStackOverflow
    //     0x768dec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x768df0: cmp             SP, x16
    //     0x768df4: b.ls            #0x768fb4
    // 0x768df8: LoadField: r4 = r3->field_57
    //     0x768df8: ldur            w4, [x3, #0x57]
    // 0x768dfc: DecompressPointer r4
    //     0x768dfc: add             x4, x4, HEAP, lsl #32
    // 0x768e00: stur            x4, [fp, #-0x10]
    // 0x768e04: cmp             w4, NULL
    // 0x768e08: b.ne            #0x768e1c
    // 0x768e0c: r0 = Null
    //     0x768e0c: mov             x0, NULL
    // 0x768e10: LeaveFrame
    //     0x768e10: mov             SP, fp
    //     0x768e14: ldp             fp, lr, [SP], #0x10
    // 0x768e18: ret
    //     0x768e18: ret             
    // 0x768e1c: LoadField: r5 = r3->field_27
    //     0x768e1c: ldur            w5, [x3, #0x27]
    // 0x768e20: DecompressPointer r5
    //     0x768e20: add             x5, x5, HEAP, lsl #32
    // 0x768e24: stur            x5, [fp, #-8]
    // 0x768e28: cmp             w5, NULL
    // 0x768e2c: b.eq            #0x768f98
    // 0x768e30: mov             x0, x5
    // 0x768e34: r2 = Null
    //     0x768e34: mov             x2, NULL
    // 0x768e38: r1 = Null
    //     0x768e38: mov             x1, NULL
    // 0x768e3c: r4 = LoadClassIdInstr(r0)
    //     0x768e3c: ldur            x4, [x0, #-1]
    //     0x768e40: ubfx            x4, x4, #0xc, #0x14
    // 0x768e44: sub             x4, x4, #0xc83
    // 0x768e48: cmp             x4, #1
    // 0x768e4c: b.ls            #0x768e60
    // 0x768e50: r8 = BoxConstraints
    //     0x768e50: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x768e54: r3 = Null
    //     0x768e54: add             x3, PP, #0x50, lsl #12  ; [pp+0x508b8] Null
    //     0x768e58: ldr             x3, [x3, #0x8b8]
    // 0x768e5c: r0 = BoxConstraints()
    //     0x768e5c: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x768e60: ldur            x1, [fp, #-0x18]
    // 0x768e64: ldur            x2, [fp, #-8]
    // 0x768e68: r0 = _constraintsForChild()
    //     0x768e68: bl              #0x73ce98  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_constraintsForChild
    // 0x768e6c: ldur            x3, [fp, #-0x10]
    // 0x768e70: r1 = LoadClassIdInstr(r3)
    //     0x768e70: ldur            x1, [x3, #-1]
    //     0x768e74: ubfx            x1, x1, #0xc, #0x14
    // 0x768e78: r16 = true
    //     0x768e78: add             x16, NULL, #0x20  ; true
    // 0x768e7c: str             x16, [SP]
    // 0x768e80: mov             x2, x0
    // 0x768e84: mov             x0, x1
    // 0x768e88: mov             x1, x3
    // 0x768e8c: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x768e8c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x768e90: ldr             x4, [x4, #0x5c0]
    // 0x768e94: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x768e94: movz            x17, #0xed1d
    //     0x768e98: add             lr, x0, x17
    //     0x768e9c: ldr             lr, [x21, lr, lsl #3]
    //     0x768ea0: blr             lr
    // 0x768ea4: ldur            x3, [fp, #-0x10]
    // 0x768ea8: LoadField: r4 = r3->field_7
    //     0x768ea8: ldur            w4, [x3, #7]
    // 0x768eac: DecompressPointer r4
    //     0x768eac: add             x4, x4, HEAP, lsl #32
    // 0x768eb0: stur            x4, [fp, #-8]
    // 0x768eb4: cmp             w4, NULL
    // 0x768eb8: b.eq            #0x768fbc
    // 0x768ebc: mov             x0, x4
    // 0x768ec0: r2 = Null
    //     0x768ec0: mov             x2, NULL
    // 0x768ec4: r1 = Null
    //     0x768ec4: mov             x1, NULL
    // 0x768ec8: r4 = LoadClassIdInstr(r0)
    //     0x768ec8: ldur            x4, [x0, #-1]
    //     0x768ecc: ubfx            x4, x4, #0xc, #0x14
    // 0x768ed0: sub             x4, x4, #0xc71
    // 0x768ed4: cmp             x4, #0xf
    // 0x768ed8: b.ls            #0x768ef0
    // 0x768edc: r8 = BoxParentData
    //     0x768edc: add             x8, PP, #0x25, lsl #12  ; [pp+0x252c8] Type: BoxParentData
    //     0x768ee0: ldr             x8, [x8, #0x2c8]
    // 0x768ee4: r3 = Null
    //     0x768ee4: add             x3, PP, #0x50, lsl #12  ; [pp+0x508c8] Null
    //     0x768ee8: ldr             x3, [x3, #0x8c8]
    // 0x768eec: r0 = DefaultTypeTest()
    //     0x768eec: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x768ef0: ldur            x1, [fp, #-0x10]
    // 0x768ef4: r0 = size()
    //     0x768ef4: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x768ef8: ldur            x1, [fp, #-0x18]
    // 0x768efc: mov             x2, x0
    // 0x768f00: r0 = _computeChildOffset()
    //     0x768f00: bl              #0x73ce18  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_computeChildOffset
    // 0x768f04: ldur            x1, [fp, #-8]
    // 0x768f08: StoreField: r1->field_7 = r0
    //     0x768f08: stur            w0, [x1, #7]
    //     0x768f0c: ldurb           w16, [x1, #-1]
    //     0x768f10: ldurb           w17, [x0, #-1]
    //     0x768f14: and             x16, x17, x16, lsr #2
    //     0x768f18: tst             x16, HEAP, lsr #32
    //     0x768f1c: b.eq            #0x768f24
    //     0x768f20: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x768f24: ldur            x1, [fp, #-0x10]
    // 0x768f28: r0 = size()
    //     0x768f28: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x768f2c: LoadField: d0 = r0->field_7
    //     0x768f2c: ldur            d0, [x0, #7]
    // 0x768f30: ldur            x1, [fp, #-0x10]
    // 0x768f34: stur            d0, [fp, #-0x20]
    // 0x768f38: r0 = size()
    //     0x768f38: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x768f3c: LoadField: d0 = r0->field_f
    //     0x768f3c: ldur            d0, [x0, #0xf]
    // 0x768f40: r0 = Instance_Size
    //     0x768f40: add             x0, PP, #0x50, lsl #12  ; [pp+0x50880] Obj!Size@e2c041
    //     0x768f44: ldr             x0, [x0, #0x880]
    // 0x768f48: LoadField: d1 = r0->field_f
    //     0x768f48: ldur            d1, [x0, #0xf]
    // 0x768f4c: fsub            d2, d0, d1
    // 0x768f50: stur            d2, [fp, #-0x28]
    // 0x768f54: r0 = Size()
    //     0x768f54: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x768f58: ldur            d0, [fp, #-0x20]
    // 0x768f5c: StoreField: r0->field_7 = d0
    //     0x768f5c: stur            d0, [x0, #7]
    // 0x768f60: ldur            d0, [fp, #-0x28]
    // 0x768f64: StoreField: r0->field_f = d0
    //     0x768f64: stur            d0, [x0, #0xf]
    // 0x768f68: ldur            x1, [fp, #-0x18]
    // 0x768f6c: StoreField: r1->field_53 = r0
    //     0x768f6c: stur            w0, [x1, #0x53]
    //     0x768f70: ldurb           w16, [x1, #-1]
    //     0x768f74: ldurb           w17, [x0, #-1]
    //     0x768f78: and             x16, x17, x16, lsr #2
    //     0x768f7c: tst             x16, HEAP, lsr #32
    //     0x768f80: b.eq            #0x768f88
    //     0x768f84: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x768f88: r0 = Null
    //     0x768f88: mov             x0, NULL
    // 0x768f8c: LeaveFrame
    //     0x768f8c: mov             SP, fp
    //     0x768f90: ldp             fp, lr, [SP], #0x10
    // 0x768f94: ret
    //     0x768f94: ret             
    // 0x768f98: r0 = StateError()
    //     0x768f98: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x768f9c: mov             x1, x0
    // 0x768fa0: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x768fa0: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x768fa4: StoreField: r1->field_b = r0
    //     0x768fa4: stur            w0, [x1, #0xb]
    // 0x768fa8: mov             x0, x1
    // 0x768fac: r0 = Throw()
    //     0x768fac: bl              #0xec04b8  ; ThrowStub
    // 0x768fb0: brk             #0
    // 0x768fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x768fb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x768fb8: b               #0x768df8
    // 0x768fbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x768fbc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ paint(/* No info */) {
    // ** addr: 0x794068, size: 0x2b8
    // 0x794068: EnterFrame
    //     0x794068: stp             fp, lr, [SP, #-0x10]!
    //     0x79406c: mov             fp, SP
    // 0x794070: AllocStack(0x78)
    //     0x794070: sub             SP, SP, #0x78
    // 0x794074: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, dynamic _ /* r3 => r1, fp-0x20 */)
    //     0x794074: mov             x0, x2
    //     0x794078: stur            x2, [fp, #-0x18]
    //     0x79407c: mov             x2, x1
    //     0x794080: stur            x1, [fp, #-0x10]
    //     0x794084: mov             x1, x3
    //     0x794088: stur            x3, [fp, #-0x20]
    // 0x79408c: CheckStackOverflow
    //     0x79408c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x794090: cmp             SP, x16
    //     0x794094: b.ls            #0x79430c
    // 0x794098: LoadField: r3 = r2->field_57
    //     0x794098: ldur            w3, [x2, #0x57]
    // 0x79409c: DecompressPointer r3
    //     0x79409c: add             x3, x3, HEAP, lsl #32
    // 0x7940a0: stur            x3, [fp, #-8]
    // 0x7940a4: r1 = 1
    //     0x7940a4: movz            x1, #0x1
    // 0x7940a8: r0 = AllocateContext()
    //     0x7940a8: bl              #0xec126c  ; AllocateContextStub
    // 0x7940ac: mov             x4, x0
    // 0x7940b0: ldur            x3, [fp, #-8]
    // 0x7940b4: stur            x4, [fp, #-0x30]
    // 0x7940b8: StoreField: r4->field_f = r3
    //     0x7940b8: stur            w3, [x4, #0xf]
    // 0x7940bc: cmp             w3, NULL
    // 0x7940c0: b.ne            #0x7940d4
    // 0x7940c4: r0 = Null
    //     0x7940c4: mov             x0, NULL
    // 0x7940c8: LeaveFrame
    //     0x7940c8: mov             SP, fp
    //     0x7940cc: ldp             fp, lr, [SP], #0x10
    // 0x7940d0: ret
    //     0x7940d0: ret             
    // 0x7940d4: ldur            x5, [fp, #-0x10]
    // 0x7940d8: LoadField: r6 = r3->field_7
    //     0x7940d8: ldur            w6, [x3, #7]
    // 0x7940dc: DecompressPointer r6
    //     0x7940dc: add             x6, x6, HEAP, lsl #32
    // 0x7940e0: stur            x6, [fp, #-0x28]
    // 0x7940e4: cmp             w6, NULL
    // 0x7940e8: b.eq            #0x794314
    // 0x7940ec: mov             x0, x6
    // 0x7940f0: r2 = Null
    //     0x7940f0: mov             x2, NULL
    // 0x7940f4: r1 = Null
    //     0x7940f4: mov             x1, NULL
    // 0x7940f8: r4 = LoadClassIdInstr(r0)
    //     0x7940f8: ldur            x4, [x0, #-1]
    //     0x7940fc: ubfx            x4, x4, #0xc, #0x14
    // 0x794100: sub             x4, x4, #0xc71
    // 0x794104: cmp             x4, #0xf
    // 0x794108: b.ls            #0x794120
    // 0x79410c: r8 = BoxParentData
    //     0x79410c: add             x8, PP, #0x25, lsl #12  ; [pp+0x252c8] Type: BoxParentData
    //     0x794110: ldr             x8, [x8, #0x2c8]
    // 0x794114: r3 = Null
    //     0x794114: add             x3, PP, #0x50, lsl #12  ; [pp+0x50888] Null
    //     0x794118: ldr             x3, [x3, #0x888]
    // 0x79411c: r0 = DefaultTypeTest()
    //     0x79411c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x794120: ldur            x1, [fp, #-0x10]
    // 0x794124: ldur            x2, [fp, #-8]
    // 0x794128: r0 = _shapeRRect()
    //     0x794128: bl              #0x7951e8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_shapeRRect
    // 0x79412c: ldur            x1, [fp, #-0x10]
    // 0x794130: ldur            x2, [fp, #-8]
    // 0x794134: mov             x3, x0
    // 0x794138: stur            x0, [fp, #-0x38]
    // 0x79413c: r0 = _clipPath()
    //     0x79413c: bl              #0x79436c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_clipPath
    // 0x794140: mov             x1, x0
    // 0x794144: ldur            x0, [fp, #-0x10]
    // 0x794148: stur            x1, [fp, #-0x48]
    // 0x79414c: LoadField: r2 = r0->field_63
    //     0x79414c: ldur            w2, [x0, #0x63]
    // 0x794150: DecompressPointer r2
    //     0x794150: add             x2, x2, HEAP, lsl #32
    // 0x794154: stur            x2, [fp, #-0x40]
    // 0x794158: cmp             w2, NULL
    // 0x79415c: b.eq            #0x794248
    // 0x794160: ldur            x4, [fp, #-0x28]
    // 0x794164: ldur            x3, [fp, #-0x38]
    // 0x794168: r0 = BoxShadow()
    //     0x794168: bl              #0x794360  ; AllocateBoxShadowStub -> BoxShadow (size=0x24)
    // 0x79416c: stur            x0, [fp, #-0x50]
    // 0x794170: ArrayStore: r0[0] = rZR  ; List_8
    //     0x794170: stur            xzr, [x0, #0x17]
    // 0x794174: r1 = Instance_BlurStyle
    //     0x794174: add             x1, PP, #0x29, lsl #12  ; [pp+0x29010] Obj!BlurStyle@e39a01
    //     0x794178: ldr             x1, [x1, #0x10]
    // 0x79417c: StoreField: r0->field_1f = r1
    //     0x79417c: stur            w1, [x0, #0x1f]
    // 0x794180: ldur            x1, [fp, #-0x40]
    // 0x794184: StoreField: r0->field_7 = r1
    //     0x794184: stur            w1, [x0, #7]
    // 0x794188: r2 = Instance_Offset
    //     0x794188: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x79418c: StoreField: r0->field_b = r2
    //     0x79418c: stur            w2, [x0, #0xb]
    // 0x794190: d0 = 15.000000
    //     0x794190: fmov            d0, #15.00000000
    // 0x794194: StoreField: r0->field_f = d0
    //     0x794194: stur            d0, [x0, #0xf]
    // 0x794198: ldur            x1, [fp, #-0x38]
    // 0x79419c: LoadField: d0 = r1->field_7
    //     0x79419c: ldur            d0, [x1, #7]
    // 0x7941a0: stur            d0, [fp, #-0x70]
    // 0x7941a4: LoadField: d1 = r1->field_f
    //     0x7941a4: ldur            d1, [x1, #0xf]
    // 0x7941a8: stur            d1, [fp, #-0x68]
    // 0x7941ac: ArrayLoad: d2 = r1[0]  ; List_8
    //     0x7941ac: ldur            d2, [x1, #0x17]
    // 0x7941b0: stur            d2, [fp, #-0x60]
    // 0x7941b4: LoadField: d3 = r1->field_1f
    //     0x7941b4: ldur            d3, [x1, #0x1f]
    // 0x7941b8: r1 = Instance_Size
    //     0x7941b8: add             x1, PP, #0x50, lsl #12  ; [pp+0x50880] Obj!Size@e2c041
    //     0x7941bc: ldr             x1, [x1, #0x880]
    // 0x7941c0: LoadField: d4 = r1->field_f
    //     0x7941c0: ldur            d4, [x1, #0xf]
    // 0x7941c4: fadd            d5, d3, d4
    // 0x7941c8: stur            d5, [fp, #-0x58]
    // 0x7941cc: r0 = RRect()
    //     0x7941cc: bl              #0x789ce8  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x7941d0: mov             x1, x0
    // 0x7941d4: ldur            d0, [fp, #-0x70]
    // 0x7941d8: ldur            d1, [fp, #-0x68]
    // 0x7941dc: ldur            d2, [fp, #-0x60]
    // 0x7941e0: ldur            d3, [fp, #-0x58]
    // 0x7941e4: r2 = Instance_Radius
    //     0x7941e4: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d9a0] Obj!Radius@e2bd31
    //     0x7941e8: ldr             x2, [x2, #0x9a0]
    // 0x7941ec: stur            x0, [fp, #-0x38]
    // 0x7941f0: r0 = RRect.fromLTRBR()
    //     0x7941f0: bl              #0x794320  ; [dart:ui] RRect::RRect.fromLTRBR
    // 0x7941f4: ldur            x0, [fp, #-0x28]
    // 0x7941f8: LoadField: r2 = r0->field_7
    //     0x7941f8: ldur            w2, [x0, #7]
    // 0x7941fc: DecompressPointer r2
    //     0x7941fc: add             x2, x2, HEAP, lsl #32
    // 0x794200: ldur            x1, [fp, #-0x20]
    // 0x794204: r0 = +()
    //     0x794204: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x794208: mov             x1, x0
    // 0x79420c: r2 = Instance_Offset
    //     0x79420c: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x794210: r0 = +()
    //     0x794210: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x794214: ldur            x1, [fp, #-0x38]
    // 0x794218: mov             x2, x0
    // 0x79421c: r0 = shift()
    //     0x79421c: bl              #0x789760  ; [dart:ui] RRect::shift
    // 0x794220: ldur            x1, [fp, #-0x18]
    // 0x794224: stur            x0, [fp, #-0x38]
    // 0x794228: r0 = canvas()
    //     0x794228: bl              #0x789cf4  ; [package:flutter/src/rendering/object.dart] PaintingContext::canvas
    // 0x79422c: ldur            x1, [fp, #-0x50]
    // 0x794230: stur            x0, [fp, #-0x40]
    // 0x794234: r0 = toPaint()
    //     0x794234: bl              #0x78933c  ; [package:flutter/src/painting/box_shadow.dart] BoxShadow::toPaint
    // 0x794238: ldur            x1, [fp, #-0x40]
    // 0x79423c: ldur            x2, [fp, #-0x38]
    // 0x794240: mov             x3, x0
    // 0x794244: r0 = drawRRect()
    //     0x794244: bl              #0x7899c4  ; [dart:ui] _NativeCanvas::drawRRect
    // 0x794248: ldur            x1, [fp, #-0x10]
    // 0x79424c: ldur            x0, [fp, #-0x28]
    // 0x794250: LoadField: r3 = r1->field_67
    //     0x794250: ldur            w3, [x1, #0x67]
    // 0x794254: DecompressPointer r3
    //     0x794254: add             x3, x3, HEAP, lsl #32
    // 0x794258: stur            x3, [fp, #-0x40]
    // 0x79425c: LoadField: r4 = r1->field_37
    //     0x79425c: ldur            w4, [x1, #0x37]
    // 0x794260: DecompressPointer r4
    //     0x794260: add             x4, x4, HEAP, lsl #32
    // 0x794264: r16 = Sentinel
    //     0x794264: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x794268: cmp             w4, w16
    // 0x79426c: b.eq            #0x794318
    // 0x794270: stur            x4, [fp, #-0x38]
    // 0x794274: LoadField: r2 = r0->field_7
    //     0x794274: ldur            w2, [x0, #7]
    // 0x794278: DecompressPointer r2
    //     0x794278: add             x2, x2, HEAP, lsl #32
    // 0x79427c: ldur            x1, [fp, #-0x20]
    // 0x794280: r0 = +()
    //     0x794280: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x794284: ldur            x1, [fp, #-8]
    // 0x794288: stur            x0, [fp, #-8]
    // 0x79428c: r0 = size()
    //     0x79428c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x794290: mov             x2, x0
    // 0x794294: r1 = Instance_Offset
    //     0x794294: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x794298: r0 = &()
    //     0x794298: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x79429c: mov             x3, x0
    // 0x7942a0: ldur            x0, [fp, #-0x40]
    // 0x7942a4: stur            x3, [fp, #-0x20]
    // 0x7942a8: LoadField: r4 = r0->field_b
    //     0x7942a8: ldur            w4, [x0, #0xb]
    // 0x7942ac: DecompressPointer r4
    //     0x7942ac: add             x4, x4, HEAP, lsl #32
    // 0x7942b0: ldur            x2, [fp, #-0x30]
    // 0x7942b4: stur            x4, [fp, #-0x10]
    // 0x7942b8: r1 = Function '<anonymous closure>':.
    //     0x7942b8: add             x1, PP, #0x50, lsl #12  ; [pp+0x50898] AnonymousClosure: (0x795678), in [package:flutter/src/widgets/widget_span.dart] _RenderScaledInlineWidget::paint (0x795f88)
    //     0x7942bc: ldr             x1, [x1, #0x898]
    // 0x7942c0: r0 = AllocateClosure()
    //     0x7942c0: bl              #0xec1630  ; AllocateClosureStub
    // 0x7942c4: ldur            x16, [fp, #-0x10]
    // 0x7942c8: str             x16, [SP]
    // 0x7942cc: ldur            x1, [fp, #-0x18]
    // 0x7942d0: ldur            x2, [fp, #-0x38]
    // 0x7942d4: ldur            x3, [fp, #-8]
    // 0x7942d8: ldur            x5, [fp, #-0x20]
    // 0x7942dc: ldur            x6, [fp, #-0x48]
    // 0x7942e0: mov             x7, x0
    // 0x7942e4: r4 = const [0, 0x7, 0x1, 0x7, null]
    //     0x7942e4: add             x4, PP, #0x50, lsl #12  ; [pp+0x508a0] List(5) [0, 0x7, 0x1, 0x7, Null]
    //     0x7942e8: ldr             x4, [x4, #0x8a0]
    // 0x7942ec: r0 = pushClipPath()
    //     0x7942ec: bl              #0x78dc30  ; [package:flutter/src/rendering/object.dart] PaintingContext::pushClipPath
    // 0x7942f0: ldur            x1, [fp, #-0x40]
    // 0x7942f4: mov             x2, x0
    // 0x7942f8: r0 = layer=()
    //     0x7942f8: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0x7942fc: r0 = Null
    //     0x7942fc: mov             x0, NULL
    // 0x794300: LeaveFrame
    //     0x794300: mov             SP, fp
    //     0x794304: ldp             fp, lr, [SP], #0x10
    // 0x794308: ret
    //     0x794308: ret             
    // 0x79430c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x79430c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x794310: b               #0x794098
    // 0x794314: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x794314: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x794318: r9 = _needsCompositing
    //     0x794318: ldr             x9, [PP, #0x2c58]  ; [pp+0x2c58] Field <RenderObject._needsCompositing@390266271>: late (offset: 0x38)
    // 0x79431c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x79431c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _clipPath(/* No info */) {
    // ** addr: 0x79436c, size: 0x588
    // 0x79436c: EnterFrame
    //     0x79436c: stp             fp, lr, [SP, #-0x10]!
    //     0x794370: mov             fp, SP
    // 0x794374: AllocStack(0x58)
    //     0x794374: sub             SP, SP, #0x58
    // 0x794378: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0x794378: mov             x0, x1
    //     0x79437c: stur            x1, [fp, #-8]
    //     0x794380: mov             x1, x2
    //     0x794384: stur            x2, [fp, #-0x10]
    //     0x794388: mov             x2, x3
    //     0x79438c: stur            x3, [fp, #-0x18]
    // 0x794390: CheckStackOverflow
    //     0x794390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x794394: cmp             SP, x16
    //     0x794398: b.ls            #0x7948cc
    // 0x79439c: r0 = _NativePath()
    //     0x79439c: bl              #0x78e3a4  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0x7943a0: mov             x1, x0
    // 0x7943a4: stur            x0, [fp, #-0x20]
    // 0x7943a8: r0 = __constructor$Method$FfiNative()
    //     0x7943a8: bl              #0x78eb3c  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0x7943ac: ldur            x1, [fp, #-8]
    // 0x7943b0: r0 = size()
    //     0x7943b0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7943b4: LoadField: d0 = r0->field_7
    //     0x7943b4: ldur            d0, [x0, #7]
    // 0x7943b8: d1 = 30.000000
    //     0x7943b8: fmov            d1, #30.00000000
    // 0x7943bc: fcmp            d1, d0
    // 0x7943c0: b.le            #0x7944d8
    // 0x7943c4: ldur            x2, [fp, #-0x18]
    // 0x7943c8: ldur            x0, [fp, #-0x20]
    // 0x7943cc: LoadField: d0 = r2->field_7
    //     0x7943cc: ldur            d0, [x2, #7]
    // 0x7943d0: fcvt            s1, d0
    // 0x7943d4: stur            d1, [fp, #-0x38]
    // 0x7943d8: r4 = 24
    //     0x7943d8: movz            x4, #0x18
    // 0x7943dc: r0 = AllocateFloat32Array()
    //     0x7943dc: bl              #0xec19f8  ; AllocateFloat32ArrayStub
    // 0x7943e0: ldur            d0, [fp, #-0x38]
    // 0x7943e4: stur            x0, [fp, #-0x30]
    // 0x7943e8: ArrayStore: r0[0] = d0  ; List_8
    //     0x7943e8: stur            s0, [x0, #0x17]
    // 0x7943ec: ldur            x2, [fp, #-0x18]
    // 0x7943f0: LoadField: d0 = r2->field_f
    //     0x7943f0: ldur            d0, [x2, #0xf]
    // 0x7943f4: fcvt            s1, d0
    // 0x7943f8: StoreField: r0->field_1b = d1
    //     0x7943f8: stur            s1, [x0, #0x1b]
    // 0x7943fc: ArrayLoad: d0 = r2[0]  ; List_8
    //     0x7943fc: ldur            d0, [x2, #0x17]
    // 0x794400: fcvt            s1, d0
    // 0x794404: StoreField: r0->field_1f = d1
    //     0x794404: stur            s1, [x0, #0x1f]
    // 0x794408: LoadField: d0 = r2->field_1f
    //     0x794408: ldur            d0, [x2, #0x1f]
    // 0x79440c: fcvt            s1, d0
    // 0x794410: StoreField: r0->field_23 = d1
    //     0x794410: stur            s1, [x0, #0x23]
    // 0x794414: LoadField: d0 = r2->field_27
    //     0x794414: ldur            d0, [x2, #0x27]
    // 0x794418: fcvt            s1, d0
    // 0x79441c: StoreField: r0->field_27 = d1
    //     0x79441c: stur            s1, [x0, #0x27]
    // 0x794420: LoadField: d0 = r2->field_2f
    //     0x794420: ldur            d0, [x2, #0x2f]
    // 0x794424: fcvt            s1, d0
    // 0x794428: StoreField: r0->field_2b = d1
    //     0x794428: stur            s1, [x0, #0x2b]
    // 0x79442c: LoadField: d0 = r2->field_37
    //     0x79442c: ldur            d0, [x2, #0x37]
    // 0x794430: fcvt            s1, d0
    // 0x794434: StoreField: r0->field_2f = d1
    //     0x794434: stur            s1, [x0, #0x2f]
    // 0x794438: LoadField: d0 = r2->field_3f
    //     0x794438: ldur            d0, [x2, #0x3f]
    // 0x79443c: fcvt            s1, d0
    // 0x794440: StoreField: r0->field_33 = d1
    //     0x794440: stur            s1, [x0, #0x33]
    // 0x794444: LoadField: d0 = r2->field_47
    //     0x794444: ldur            d0, [x2, #0x47]
    // 0x794448: fcvt            s1, d0
    // 0x79444c: StoreField: r0->field_37 = d1
    //     0x79444c: stur            s1, [x0, #0x37]
    // 0x794450: LoadField: d0 = r2->field_4f
    //     0x794450: ldur            d0, [x2, #0x4f]
    // 0x794454: fcvt            s1, d0
    // 0x794458: StoreField: r0->field_3b = d1
    //     0x794458: stur            s1, [x0, #0x3b]
    // 0x79445c: LoadField: d0 = r2->field_57
    //     0x79445c: ldur            d0, [x2, #0x57]
    // 0x794460: fcvt            s1, d0
    // 0x794464: StoreField: r0->field_3f = d1
    //     0x794464: stur            s1, [x0, #0x3f]
    // 0x794468: LoadField: d0 = r2->field_5f
    //     0x794468: ldur            d0, [x2, #0x5f]
    // 0x79446c: fcvt            s1, d0
    // 0x794470: StoreField: r0->field_43 = d1
    //     0x794470: stur            s1, [x0, #0x43]
    // 0x794474: ldur            x1, [fp, #-0x20]
    // 0x794478: LoadField: r2 = r1->field_7
    //     0x794478: ldur            w2, [x1, #7]
    // 0x79447c: DecompressPointer r2
    //     0x79447c: add             x2, x2, HEAP, lsl #32
    // 0x794480: cmp             w2, NULL
    // 0x794484: b.eq            #0x7948d4
    // 0x794488: LoadField: r3 = r2->field_7
    //     0x794488: ldur            x3, [x2, #7]
    // 0x79448c: ldr             x2, [x3]
    // 0x794490: stur            x2, [fp, #-0x28]
    // 0x794494: cbnz            x2, #0x7944a4
    // 0x794498: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x794498: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x79449c: str             x16, [SP]
    // 0x7944a0: r0 = _throwNew()
    //     0x7944a0: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x7944a4: ldur            x0, [fp, #-0x28]
    // 0x7944a8: stur            x0, [fp, #-0x28]
    // 0x7944ac: r1 = <Never>
    //     0x7944ac: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7944b0: r0 = Pointer()
    //     0x7944b0: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7944b4: mov             x1, x0
    // 0x7944b8: ldur            x0, [fp, #-0x28]
    // 0x7944bc: StoreField: r1->field_7 = r0
    //     0x7944bc: stur            x0, [x1, #7]
    // 0x7944c0: ldur            x2, [fp, #-0x30]
    // 0x7944c4: r0 = __addRRect$Method$FfiNative()
    //     0x7944c4: bl              #0x78e988  ; [dart:ui] _NativePath::__addRRect$Method$FfiNative
    // 0x7944c8: ldur            x0, [fp, #-0x20]
    // 0x7944cc: LeaveFrame
    //     0x7944cc: mov             SP, fp
    //     0x7944d0: ldp             fp, lr, [SP], #0x10
    // 0x7944d4: ret
    //     0x7944d4: ret             
    // 0x7944d8: ldur            x2, [fp, #-0x18]
    // 0x7944dc: ldur            x1, [fp, #-0x10]
    // 0x7944e0: r0 = size()
    //     0x7944e0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7944e4: LoadField: d0 = r0->field_f
    //     0x7944e4: ldur            d0, [x0, #0xf]
    // 0x7944e8: ldur            x1, [fp, #-8]
    // 0x7944ec: r0 = _isAbove()
    //     0x7944ec: bl              #0x73ce70  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_isAbove
    // 0x7944f0: stur            x0, [fp, #-0x30]
    // 0x7944f4: tbnz            w0, #4, #0x79450c
    // 0x7944f8: ldur            x3, [fp, #-8]
    // 0x7944fc: LoadField: r1 = r3->field_5b
    //     0x7944fc: ldur            w1, [x3, #0x5b]
    // 0x794500: DecompressPointer r1
    //     0x794500: add             x1, x1, HEAP, lsl #32
    // 0x794504: mov             x2, x1
    // 0x794508: b               #0x79451c
    // 0x79450c: ldur            x3, [fp, #-8]
    // 0x794510: LoadField: r1 = r3->field_5f
    //     0x794510: ldur            w1, [x3, #0x5f]
    // 0x794514: DecompressPointer r1
    //     0x794514: add             x1, x1, HEAP, lsl #32
    // 0x794518: mov             x2, x1
    // 0x79451c: mov             x1, x3
    // 0x794520: r0 = globalToLocal()
    //     0x794520: bl              #0x6a9a0c  ; [package:flutter/src/rendering/box.dart] RenderBox::globalToLocal
    // 0x794524: LoadField: d0 = r0->field_7
    //     0x794524: ldur            d0, [x0, #7]
    // 0x794528: ldur            x1, [fp, #-8]
    // 0x79452c: stur            d0, [fp, #-0x38]
    // 0x794530: r0 = size()
    //     0x794530: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x794534: LoadField: d0 = r0->field_7
    //     0x794534: ldur            d0, [x0, #7]
    // 0x794538: d1 = 7.000000
    //     0x794538: fmov            d1, #7.00000000
    // 0x79453c: fsub            d2, d0, d1
    // 0x794540: r0 = Instance_Radius
    //     0x794540: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4d9a0] Obj!Radius@e2bd31
    //     0x794544: ldr             x0, [x0, #0x9a0]
    // 0x794548: LoadField: d0 = r0->field_7
    //     0x794548: ldur            d0, [x0, #7]
    // 0x79454c: fsub            d3, d2, d0
    // 0x794550: ldur            d0, [fp, #-0x38]
    // 0x794554: d2 = 15.000000
    //     0x794554: fmov            d2, #15.00000000
    // 0x794558: fcmp            d2, d0
    // 0x79455c: b.le            #0x794568
    // 0x794560: d0 = 15.000000
    //     0x794560: fmov            d0, #15.00000000
    // 0x794564: b               #0x794584
    // 0x794568: fcmp            d0, d3
    // 0x79456c: b.le            #0x794578
    // 0x794570: mov             v0.16b, v3.16b
    // 0x794574: b               #0x794584
    // 0x794578: fcmp            d0, d0
    // 0x79457c: b.vc            #0x794584
    // 0x794580: mov             v0.16b, v3.16b
    // 0x794584: ldur            x0, [fp, #-0x30]
    // 0x794588: stur            d0, [fp, #-0x38]
    // 0x79458c: tbnz            w0, #4, #0x7946fc
    // 0x794590: ldur            x2, [fp, #-0x20]
    // 0x794594: ldur            x1, [fp, #-0x10]
    // 0x794598: r0 = size()
    //     0x794598: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x79459c: LoadField: d0 = r0->field_f
    //     0x79459c: ldur            d0, [x0, #0xf]
    // 0x7945a0: r0 = Instance_Size
    //     0x7945a0: add             x0, PP, #0x50, lsl #12  ; [pp+0x50880] Obj!Size@e2c041
    //     0x7945a4: ldr             x0, [x0, #0x880]
    // 0x7945a8: LoadField: d1 = r0->field_f
    //     0x7945a8: ldur            d1, [x0, #0xf]
    // 0x7945ac: fsub            d2, d0, d1
    // 0x7945b0: ldur            x1, [fp, #-0x10]
    // 0x7945b4: stur            d2, [fp, #-0x40]
    // 0x7945b8: r0 = size()
    //     0x7945b8: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7945bc: LoadField: d1 = r0->field_f
    //     0x7945bc: ldur            d1, [x0, #0xf]
    // 0x7945c0: ldur            d2, [fp, #-0x38]
    // 0x7945c4: stur            d1, [fp, #-0x50]
    // 0x7945c8: d0 = 7.000000
    //     0x7945c8: fmov            d0, #7.00000000
    // 0x7945cc: fadd            d3, d2, d0
    // 0x7945d0: ldur            x1, [fp, #-0x20]
    // 0x7945d4: stur            d3, [fp, #-0x48]
    // 0x7945d8: LoadField: r0 = r1->field_7
    //     0x7945d8: ldur            w0, [x1, #7]
    // 0x7945dc: DecompressPointer r0
    //     0x7945dc: add             x0, x0, HEAP, lsl #32
    // 0x7945e0: cmp             w0, NULL
    // 0x7945e4: b.eq            #0x7948d8
    // 0x7945e8: LoadField: r2 = r0->field_7
    //     0x7945e8: ldur            x2, [x0, #7]
    // 0x7945ec: ldr             x0, [x2]
    // 0x7945f0: stur            x0, [fp, #-0x28]
    // 0x7945f4: cbnz            x0, #0x794604
    // 0x7945f8: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7945f8: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7945fc: str             x16, [SP]
    // 0x794600: r0 = _throwNew()
    //     0x794600: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x794604: ldur            x0, [fp, #-0x20]
    // 0x794608: ldur            x2, [fp, #-0x28]
    // 0x79460c: stur            x2, [fp, #-0x28]
    // 0x794610: r1 = <Never>
    //     0x794610: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x794614: r0 = Pointer()
    //     0x794614: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x794618: mov             x1, x0
    // 0x79461c: ldur            x0, [fp, #-0x28]
    // 0x794620: StoreField: r1->field_7 = r0
    //     0x794620: stur            x0, [x1, #7]
    // 0x794624: ldur            d0, [fp, #-0x48]
    // 0x794628: ldur            d1, [fp, #-0x40]
    // 0x79462c: r0 = _moveTo$Method$FfiNative()
    //     0x79462c: bl              #0x795144  ; [dart:ui] _NativePath::_moveTo$Method$FfiNative
    // 0x794630: ldur            x1, [fp, #-0x20]
    // 0x794634: LoadField: r0 = r1->field_7
    //     0x794634: ldur            w0, [x1, #7]
    // 0x794638: DecompressPointer r0
    //     0x794638: add             x0, x0, HEAP, lsl #32
    // 0x79463c: cmp             w0, NULL
    // 0x794640: b.eq            #0x7948dc
    // 0x794644: LoadField: r2 = r0->field_7
    //     0x794644: ldur            x2, [x0, #7]
    // 0x794648: ldr             x0, [x2]
    // 0x79464c: stur            x0, [fp, #-0x28]
    // 0x794650: cbnz            x0, #0x794660
    // 0x794654: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x794654: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x794658: str             x16, [SP]
    // 0x79465c: r0 = _throwNew()
    //     0x79465c: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x794660: ldur            x0, [fp, #-0x20]
    // 0x794664: ldur            d0, [fp, #-0x38]
    // 0x794668: ldur            x2, [fp, #-0x28]
    // 0x79466c: stur            x2, [fp, #-0x28]
    // 0x794670: r1 = <Never>
    //     0x794670: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x794674: r0 = Pointer()
    //     0x794674: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x794678: mov             x1, x0
    // 0x79467c: ldur            x0, [fp, #-0x28]
    // 0x794680: StoreField: r1->field_7 = r0
    //     0x794680: stur            x0, [x1, #7]
    // 0x794684: ldur            d0, [fp, #-0x38]
    // 0x794688: ldur            d1, [fp, #-0x50]
    // 0x79468c: r0 = _lineTo$Method$FfiNative()
    //     0x79468c: bl              #0x7950a0  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0x794690: ldur            d1, [fp, #-0x38]
    // 0x794694: d0 = 7.000000
    //     0x794694: fmov            d0, #7.00000000
    // 0x794698: fsub            d2, d1, d0
    // 0x79469c: ldur            x1, [fp, #-0x20]
    // 0x7946a0: stur            d2, [fp, #-0x48]
    // 0x7946a4: LoadField: r0 = r1->field_7
    //     0x7946a4: ldur            w0, [x1, #7]
    // 0x7946a8: DecompressPointer r0
    //     0x7946a8: add             x0, x0, HEAP, lsl #32
    // 0x7946ac: cmp             w0, NULL
    // 0x7946b0: b.eq            #0x7948e0
    // 0x7946b4: LoadField: r2 = r0->field_7
    //     0x7946b4: ldur            x2, [x0, #7]
    // 0x7946b8: ldr             x0, [x2]
    // 0x7946bc: stur            x0, [fp, #-0x28]
    // 0x7946c0: cbnz            x0, #0x7946d0
    // 0x7946c4: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7946c4: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7946c8: str             x16, [SP]
    // 0x7946cc: r0 = _throwNew()
    //     0x7946cc: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x7946d0: ldur            x0, [fp, #-0x28]
    // 0x7946d4: stur            x0, [fp, #-0x28]
    // 0x7946d8: r1 = <Never>
    //     0x7946d8: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7946dc: r0 = Pointer()
    //     0x7946dc: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7946e0: mov             x1, x0
    // 0x7946e4: ldur            x0, [fp, #-0x28]
    // 0x7946e8: StoreField: r1->field_7 = r0
    //     0x7946e8: stur            x0, [x1, #7]
    // 0x7946ec: ldur            d0, [fp, #-0x48]
    // 0x7946f0: ldur            d1, [fp, #-0x40]
    // 0x7946f4: r0 = _lineTo$Method$FfiNative()
    //     0x7946f4: bl              #0x7950a0  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0x7946f8: b               #0x794844
    // 0x7946fc: ldur            x1, [fp, #-0x20]
    // 0x794700: mov             v31.16b, v1.16b
    // 0x794704: mov             v1.16b, v0.16b
    // 0x794708: mov             v0.16b, v31.16b
    // 0x79470c: r0 = Instance_Size
    //     0x79470c: add             x0, PP, #0x50, lsl #12  ; [pp+0x50880] Obj!Size@e2c041
    //     0x794710: ldr             x0, [x0, #0x880]
    // 0x794714: LoadField: d2 = r0->field_f
    //     0x794714: ldur            d2, [x0, #0xf]
    // 0x794718: stur            d2, [fp, #-0x48]
    // 0x79471c: fsub            d3, d1, d0
    // 0x794720: stur            d3, [fp, #-0x40]
    // 0x794724: LoadField: r0 = r1->field_7
    //     0x794724: ldur            w0, [x1, #7]
    // 0x794728: DecompressPointer r0
    //     0x794728: add             x0, x0, HEAP, lsl #32
    // 0x79472c: cmp             w0, NULL
    // 0x794730: b.eq            #0x7948e4
    // 0x794734: LoadField: r2 = r0->field_7
    //     0x794734: ldur            x2, [x0, #7]
    // 0x794738: ldr             x0, [x2]
    // 0x79473c: stur            x0, [fp, #-0x28]
    // 0x794740: cbnz            x0, #0x794750
    // 0x794744: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x794744: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x794748: str             x16, [SP]
    // 0x79474c: r0 = _throwNew()
    //     0x79474c: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x794750: ldur            x0, [fp, #-0x20]
    // 0x794754: ldur            x2, [fp, #-0x28]
    // 0x794758: stur            x2, [fp, #-0x28]
    // 0x79475c: r1 = <Never>
    //     0x79475c: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x794760: r0 = Pointer()
    //     0x794760: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x794764: mov             x1, x0
    // 0x794768: ldur            x0, [fp, #-0x28]
    // 0x79476c: StoreField: r1->field_7 = r0
    //     0x79476c: stur            x0, [x1, #7]
    // 0x794770: ldur            d0, [fp, #-0x40]
    // 0x794774: ldur            d1, [fp, #-0x48]
    // 0x794778: r0 = _moveTo$Method$FfiNative()
    //     0x794778: bl              #0x795144  ; [dart:ui] _NativePath::_moveTo$Method$FfiNative
    // 0x79477c: ldur            x1, [fp, #-0x20]
    // 0x794780: LoadField: r0 = r1->field_7
    //     0x794780: ldur            w0, [x1, #7]
    // 0x794784: DecompressPointer r0
    //     0x794784: add             x0, x0, HEAP, lsl #32
    // 0x794788: cmp             w0, NULL
    // 0x79478c: b.eq            #0x7948e8
    // 0x794790: LoadField: r2 = r0->field_7
    //     0x794790: ldur            x2, [x0, #7]
    // 0x794794: ldr             x0, [x2]
    // 0x794798: stur            x0, [fp, #-0x28]
    // 0x79479c: cbnz            x0, #0x7947ac
    // 0x7947a0: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7947a0: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7947a4: str             x16, [SP]
    // 0x7947a8: r0 = _throwNew()
    //     0x7947a8: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x7947ac: ldur            x0, [fp, #-0x20]
    // 0x7947b0: ldur            d0, [fp, #-0x38]
    // 0x7947b4: ldur            x2, [fp, #-0x28]
    // 0x7947b8: stur            x2, [fp, #-0x28]
    // 0x7947bc: r1 = <Never>
    //     0x7947bc: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7947c0: r0 = Pointer()
    //     0x7947c0: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7947c4: mov             x1, x0
    // 0x7947c8: ldur            x0, [fp, #-0x28]
    // 0x7947cc: StoreField: r1->field_7 = r0
    //     0x7947cc: stur            x0, [x1, #7]
    // 0x7947d0: ldur            d0, [fp, #-0x38]
    // 0x7947d4: d1 = 0.000000
    //     0x7947d4: eor             v1.16b, v1.16b, v1.16b
    // 0x7947d8: r0 = _lineTo$Method$FfiNative()
    //     0x7947d8: bl              #0x7950a0  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0x7947dc: ldur            d1, [fp, #-0x38]
    // 0x7947e0: d0 = 7.000000
    //     0x7947e0: fmov            d0, #7.00000000
    // 0x7947e4: fadd            d2, d1, d0
    // 0x7947e8: ldur            x1, [fp, #-0x20]
    // 0x7947ec: stur            d2, [fp, #-0x40]
    // 0x7947f0: LoadField: r0 = r1->field_7
    //     0x7947f0: ldur            w0, [x1, #7]
    // 0x7947f4: DecompressPointer r0
    //     0x7947f4: add             x0, x0, HEAP, lsl #32
    // 0x7947f8: cmp             w0, NULL
    // 0x7947fc: b.eq            #0x7948ec
    // 0x794800: LoadField: r2 = r0->field_7
    //     0x794800: ldur            x2, [x0, #7]
    // 0x794804: ldr             x0, [x2]
    // 0x794808: stur            x0, [fp, #-0x28]
    // 0x79480c: cbnz            x0, #0x79481c
    // 0x794810: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x794810: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x794814: str             x16, [SP]
    // 0x794818: r0 = _throwNew()
    //     0x794818: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x79481c: ldur            x0, [fp, #-0x28]
    // 0x794820: stur            x0, [fp, #-0x28]
    // 0x794824: r1 = <Never>
    //     0x794824: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x794828: r0 = Pointer()
    //     0x794828: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x79482c: mov             x1, x0
    // 0x794830: ldur            x0, [fp, #-0x28]
    // 0x794834: StoreField: r1->field_7 = r0
    //     0x794834: stur            x0, [x1, #7]
    // 0x794838: ldur            d0, [fp, #-0x40]
    // 0x79483c: ldur            d1, [fp, #-0x48]
    // 0x794840: r0 = _lineTo$Method$FfiNative()
    //     0x794840: bl              #0x7950a0  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0x794844: ldur            x0, [fp, #-0x30]
    // 0x794848: tbnz            w0, #4, #0x794858
    // 0x79484c: d0 = 1.570796
    //     0x79484c: add             x17, PP, #0x43, lsl #12  ; [pp+0x43900] IMM: double(1.5707963267948966) from 0x3ff921fb54442d18
    //     0x794850: ldr             d0, [x17, #0x900]
    // 0x794854: b               #0x794860
    // 0x794858: d0 = -1.570796
    //     0x794858: add             x17, PP, #0x36, lsl #12  ; [pp+0x36708] IMM: double(-1.5707963267948966) from 0xbff921fb54442d18
    //     0x79485c: ldr             d0, [x17, #0x708]
    // 0x794860: ldur            x1, [fp, #-0x20]
    // 0x794864: ldur            x2, [fp, #-0x18]
    // 0x794868: r0 = _addRRectToPath()
    //     0x794868: bl              #0x794988  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_addRRectToPath
    // 0x79486c: stur            x0, [fp, #-8]
    // 0x794870: LoadField: r1 = r0->field_7
    //     0x794870: ldur            w1, [x0, #7]
    // 0x794874: DecompressPointer r1
    //     0x794874: add             x1, x1, HEAP, lsl #32
    // 0x794878: cmp             w1, NULL
    // 0x79487c: b.eq            #0x7948f0
    // 0x794880: LoadField: r2 = r1->field_7
    //     0x794880: ldur            x2, [x1, #7]
    // 0x794884: ldr             x1, [x2]
    // 0x794888: stur            x1, [fp, #-0x28]
    // 0x79488c: cbnz            x1, #0x79489c
    // 0x794890: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x794890: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x794894: str             x16, [SP]
    // 0x794898: r0 = _throwNew()
    //     0x794898: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x79489c: ldur            x0, [fp, #-0x28]
    // 0x7948a0: stur            x0, [fp, #-0x28]
    // 0x7948a4: r1 = <Never>
    //     0x7948a4: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7948a8: r0 = Pointer()
    //     0x7948a8: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7948ac: mov             x1, x0
    // 0x7948b0: ldur            x0, [fp, #-0x28]
    // 0x7948b4: StoreField: r1->field_7 = r0
    //     0x7948b4: stur            x0, [x1, #7]
    // 0x7948b8: r0 = _close$Method$FfiNative()
    //     0x7948b8: bl              #0x7948f4  ; [dart:ui] _NativePath::_close$Method$FfiNative
    // 0x7948bc: ldur            x0, [fp, #-8]
    // 0x7948c0: LeaveFrame
    //     0x7948c0: mov             SP, fp
    //     0x7948c4: ldp             fp, lr, [SP], #0x10
    // 0x7948c8: ret
    //     0x7948c8: ret             
    // 0x7948cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7948cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7948d0: b               #0x79439c
    // 0x7948d4: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7948d4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7948d8: r0 = NullErrorSharedWithFPURegs()
    //     0x7948d8: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0x7948dc: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7948dc: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7948e0: r0 = NullErrorSharedWithFPURegs()
    //     0x7948e0: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0x7948e4: r0 = NullErrorSharedWithFPURegs()
    //     0x7948e4: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0x7948e8: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7948e8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7948ec: r0 = NullErrorSharedWithFPURegs()
    //     0x7948ec: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0x7948f0: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7948f0: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  static _ _addRRectToPath(/* No info */) {
    // ** addr: 0x794988, size: 0x504
    // 0x794988: EnterFrame
    //     0x794988: stp             fp, lr, [SP, #-0x10]!
    //     0x79498c: mov             fp, SP
    // 0x794990: AllocStack(0x98)
    //     0x794990: sub             SP, SP, #0x98
    // 0x794994: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x68 */)
    //     0x794994: mov             x0, x2
    //     0x794998: stur            x2, [fp, #-0x10]
    //     0x79499c: mov             x2, x1
    //     0x7949a0: stur            x1, [fp, #-8]
    //     0x7949a4: stur            d0, [fp, #-0x68]
    // 0x7949a8: CheckStackOverflow
    //     0x7949a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7949ac: cmp             SP, x16
    //     0x7949b0: b.ls            #0x794e54
    // 0x7949b4: mov             x1, x0
    // 0x7949b8: r0 = outerRect()
    //     0x7949b8: bl              #0x67b7a4  ; [dart:ui] RRect::outerRect
    // 0x7949bc: stur            x0, [fp, #-0x18]
    // 0x7949c0: ArrayLoad: d0 = r0[0]  ; List_8
    //     0x7949c0: ldur            d0, [x0, #0x17]
    // 0x7949c4: stur            d0, [fp, #-0x78]
    // 0x7949c8: LoadField: d1 = r0->field_1f
    //     0x7949c8: ldur            d1, [x0, #0x1f]
    // 0x7949cc: stur            d1, [fp, #-0x70]
    // 0x7949d0: r0 = Offset()
    //     0x7949d0: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7949d4: ldur            d0, [fp, #-0x78]
    // 0x7949d8: stur            x0, [fp, #-0x20]
    // 0x7949dc: StoreField: r0->field_7 = d0
    //     0x7949dc: stur            d0, [x0, #7]
    // 0x7949e0: ldur            d1, [fp, #-0x70]
    // 0x7949e4: StoreField: r0->field_f = d1
    //     0x7949e4: stur            d1, [x0, #0xf]
    // 0x7949e8: ldur            x1, [fp, #-0x10]
    // 0x7949ec: LoadField: d2 = r1->field_47
    //     0x7949ec: ldur            d2, [x1, #0x47]
    // 0x7949f0: stur            d2, [fp, #-0x88]
    // 0x7949f4: LoadField: d3 = r1->field_4f
    //     0x7949f4: ldur            d3, [x1, #0x4f]
    // 0x7949f8: stur            d3, [fp, #-0x80]
    // 0x7949fc: r0 = Radius()
    //     0x7949fc: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x794a00: ldur            d0, [fp, #-0x88]
    // 0x794a04: StoreField: r0->field_7 = d0
    //     0x794a04: stur            d0, [x0, #7]
    // 0x794a08: ldur            d0, [fp, #-0x80]
    // 0x794a0c: StoreField: r0->field_f = d0
    //     0x794a0c: stur            d0, [x0, #0xf]
    // 0x794a10: mov             x1, x0
    // 0x794a14: r0 = unary-()
    //     0x794a14: bl              #0x79505c  ; [dart:ui] Radius::unary-
    // 0x794a18: mov             x1, x0
    // 0x794a1c: ldur            x0, [fp, #-0x18]
    // 0x794a20: stur            x1, [fp, #-0x28]
    // 0x794a24: LoadField: d0 = r0->field_7
    //     0x794a24: ldur            d0, [x0, #7]
    // 0x794a28: stur            d0, [fp, #-0x80]
    // 0x794a2c: r0 = Offset()
    //     0x794a2c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x794a30: ldur            d0, [fp, #-0x80]
    // 0x794a34: stur            x0, [fp, #-0x30]
    // 0x794a38: StoreField: r0->field_7 = d0
    //     0x794a38: stur            d0, [x0, #7]
    // 0x794a3c: ldur            d1, [fp, #-0x70]
    // 0x794a40: StoreField: r0->field_f = d1
    //     0x794a40: stur            d1, [x0, #0xf]
    // 0x794a44: ldur            x1, [fp, #-0x10]
    // 0x794a48: LoadField: d1 = r1->field_57
    //     0x794a48: ldur            d1, [x1, #0x57]
    // 0x794a4c: stur            d1, [fp, #-0x88]
    // 0x794a50: LoadField: d2 = r1->field_5f
    //     0x794a50: ldur            d2, [x1, #0x5f]
    // 0x794a54: fneg            d3, d2
    // 0x794a58: stur            d3, [fp, #-0x70]
    // 0x794a5c: r0 = Radius()
    //     0x794a5c: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x794a60: ldur            d0, [fp, #-0x88]
    // 0x794a64: stur            x0, [fp, #-0x38]
    // 0x794a68: StoreField: r0->field_7 = d0
    //     0x794a68: stur            d0, [x0, #7]
    // 0x794a6c: ldur            d0, [fp, #-0x70]
    // 0x794a70: StoreField: r0->field_f = d0
    //     0x794a70: stur            d0, [x0, #0xf]
    // 0x794a74: ldur            x1, [fp, #-0x18]
    // 0x794a78: LoadField: d0 = r1->field_f
    //     0x794a78: ldur            d0, [x1, #0xf]
    // 0x794a7c: stur            d0, [fp, #-0x70]
    // 0x794a80: r0 = Offset()
    //     0x794a80: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x794a84: ldur            d0, [fp, #-0x80]
    // 0x794a88: stur            x0, [fp, #-0x18]
    // 0x794a8c: StoreField: r0->field_7 = d0
    //     0x794a8c: stur            d0, [x0, #7]
    // 0x794a90: ldur            d0, [fp, #-0x70]
    // 0x794a94: StoreField: r0->field_f = d0
    //     0x794a94: stur            d0, [x0, #0xf]
    // 0x794a98: ldur            x1, [fp, #-0x10]
    // 0x794a9c: LoadField: d1 = r1->field_27
    //     0x794a9c: ldur            d1, [x1, #0x27]
    // 0x794aa0: stur            d1, [fp, #-0x88]
    // 0x794aa4: LoadField: d2 = r1->field_2f
    //     0x794aa4: ldur            d2, [x1, #0x2f]
    // 0x794aa8: stur            d2, [fp, #-0x80]
    // 0x794aac: r0 = Radius()
    //     0x794aac: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x794ab0: ldur            d0, [fp, #-0x88]
    // 0x794ab4: stur            x0, [fp, #-0x40]
    // 0x794ab8: StoreField: r0->field_7 = d0
    //     0x794ab8: stur            d0, [x0, #7]
    // 0x794abc: ldur            d0, [fp, #-0x80]
    // 0x794ac0: StoreField: r0->field_f = d0
    //     0x794ac0: stur            d0, [x0, #0xf]
    // 0x794ac4: r0 = Offset()
    //     0x794ac4: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x794ac8: ldur            d0, [fp, #-0x78]
    // 0x794acc: stur            x0, [fp, #-0x48]
    // 0x794ad0: StoreField: r0->field_7 = d0
    //     0x794ad0: stur            d0, [x0, #7]
    // 0x794ad4: ldur            d0, [fp, #-0x70]
    // 0x794ad8: StoreField: r0->field_f = d0
    //     0x794ad8: stur            d0, [x0, #0xf]
    // 0x794adc: ldur            x1, [fp, #-0x10]
    // 0x794ae0: LoadField: d0 = r1->field_37
    //     0x794ae0: ldur            d0, [x1, #0x37]
    // 0x794ae4: fneg            d1, d0
    // 0x794ae8: stur            d1, [fp, #-0x78]
    // 0x794aec: LoadField: d0 = r1->field_3f
    //     0x794aec: ldur            d0, [x1, #0x3f]
    // 0x794af0: stur            d0, [fp, #-0x70]
    // 0x794af4: r0 = Radius()
    //     0x794af4: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x794af8: ldur            d0, [fp, #-0x78]
    // 0x794afc: stur            x0, [fp, #-0x10]
    // 0x794b00: StoreField: r0->field_7 = d0
    //     0x794b00: stur            d0, [x0, #7]
    // 0x794b04: ldur            d0, [fp, #-0x70]
    // 0x794b08: StoreField: r0->field_f = d0
    //     0x794b08: stur            d0, [x0, #0xf]
    // 0x794b0c: ldur            x2, [fp, #-0x20]
    // 0x794b10: ldur            x3, [fp, #-0x28]
    // 0x794b14: r0 = AllocateRecord2()
    //     0x794b14: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x794b18: r1 = Null
    //     0x794b18: mov             x1, NULL
    // 0x794b1c: r2 = 8
    //     0x794b1c: movz            x2, #0x8
    // 0x794b20: stur            x0, [fp, #-0x20]
    // 0x794b24: r0 = AllocateArray()
    //     0x794b24: bl              #0xec22fc  ; AllocateArrayStub
    // 0x794b28: mov             x1, x0
    // 0x794b2c: ldur            x0, [fp, #-0x20]
    // 0x794b30: stur            x1, [fp, #-0x28]
    // 0x794b34: StoreField: r1->field_f = r0
    //     0x794b34: stur            w0, [x1, #0xf]
    // 0x794b38: ldur            x2, [fp, #-0x30]
    // 0x794b3c: ldur            x3, [fp, #-0x38]
    // 0x794b40: r0 = AllocateRecord2()
    //     0x794b40: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x794b44: mov             x1, x0
    // 0x794b48: ldur            x0, [fp, #-0x28]
    // 0x794b4c: StoreField: r0->field_13 = r1
    //     0x794b4c: stur            w1, [x0, #0x13]
    // 0x794b50: ldur            x2, [fp, #-0x18]
    // 0x794b54: ldur            x3, [fp, #-0x40]
    // 0x794b58: r0 = AllocateRecord2()
    //     0x794b58: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x794b5c: mov             x1, x0
    // 0x794b60: ldur            x0, [fp, #-0x28]
    // 0x794b64: ArrayStore: r0[0] = r1  ; List_4
    //     0x794b64: stur            w1, [x0, #0x17]
    // 0x794b68: ldur            x2, [fp, #-0x48]
    // 0x794b6c: ldur            x3, [fp, #-0x10]
    // 0x794b70: r0 = AllocateRecord2()
    //     0x794b70: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x794b74: mov             x1, x0
    // 0x794b78: ldur            x0, [fp, #-0x28]
    // 0x794b7c: StoreField: r0->field_1b = r1
    //     0x794b7c: stur            w1, [x0, #0x1b]
    // 0x794b80: ldur            d0, [fp, #-0x68]
    // 0x794b84: r1 = inline_Allocate_Double()
    //     0x794b84: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x794b88: add             x1, x1, #0x10
    //     0x794b8c: cmp             x2, x1
    //     0x794b90: b.ls            #0x794e5c
    //     0x794b94: str             x1, [THR, #0x50]  ; THR::top
    //     0x794b98: sub             x1, x1, #0xf
    //     0x794b9c: movz            x2, #0xe15c
    //     0x794ba0: movk            x2, #0x3, lsl #16
    //     0x794ba4: stur            x2, [x1, #-1]
    // 0x794ba8: StoreField: r1->field_7 = d0
    //     0x794ba8: stur            d0, [x1, #7]
    // 0x794bac: r16 = 1.570796
    //     0x794bac: add             x16, PP, #0x4e, lsl #12  ; [pp+0x4e298] 1.5707963267948966
    //     0x794bb0: ldr             x16, [x16, #0x298]
    // 0x794bb4: stp             x16, x1, [SP]
    // 0x794bb8: r0 = ~/()
    //     0x794bb8: bl              #0x784048  ; [dart:core] _Double::~/
    // 0x794bbc: r1 = LoadInt32Instr(r0)
    //     0x794bbc: sbfx            x1, x0, #1, #0x1f
    //     0x794bc0: tbz             w0, #0, #0x794bc8
    //     0x794bc4: ldur            x1, [x0, #7]
    // 0x794bc8: add             x0, x1, #4
    // 0x794bcc: stur            x0, [fp, #-0x60]
    // 0x794bd0: mov             x4, x1
    // 0x794bd4: ldur            x2, [fp, #-8]
    // 0x794bd8: ldur            x1, [fp, #-0x28]
    // 0x794bdc: d1 = 0.000000
    //     0x794bdc: eor             v1.16b, v1.16b, v1.16b
    // 0x794be0: d0 = 2.000000
    //     0x794be0: fmov            d0, #2.00000000
    // 0x794be4: r3 = 4
    //     0x794be4: movz            x3, #0x4
    // 0x794be8: d5 = 1.570796
    //     0x794be8: add             x17, PP, #0x43, lsl #12  ; [pp+0x43900] IMM: double(1.5707963267948966) from 0x3ff921fb54442d18
    //     0x794bec: ldr             d5, [x17, #0x900]
    // 0x794bf0: stur            x4, [fp, #-0x58]
    // 0x794bf4: CheckStackOverflow
    //     0x794bf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x794bf8: cmp             SP, x16
    //     0x794bfc: b.ls            #0x794e78
    // 0x794c00: cmp             x4, x0
    // 0x794c04: b.ge            #0x794e44
    // 0x794c08: sdiv            x6, x4, x3
    // 0x794c0c: msub            x5, x6, x3, x4
    // 0x794c10: cmp             x5, xzr
    // 0x794c14: b.lt            #0x794e80
    // 0x794c18: ArrayLoad: r6 = r1[r5]  ; Unknown_4
    //     0x794c18: add             x16, x1, x5, lsl #2
    //     0x794c1c: ldur            w6, [x16, #0xf]
    // 0x794c20: DecompressPointer r6
    //     0x794c20: add             x6, x6, HEAP, lsl #32
    // 0x794c24: LoadField: r5 = r6->field_f
    //     0x794c24: ldur            w5, [x6, #0xf]
    // 0x794c28: DecompressPointer r5
    //     0x794c28: add             x5, x5, HEAP, lsl #32
    // 0x794c2c: LoadField: r7 = r6->field_13
    //     0x794c2c: ldur            w7, [x6, #0x13]
    // 0x794c30: DecompressPointer r7
    //     0x794c30: add             x7, x7, HEAP, lsl #32
    // 0x794c34: LoadField: d2 = r5->field_7
    //     0x794c34: ldur            d2, [x5, #7]
    // 0x794c38: LoadField: d3 = r7->field_7
    //     0x794c38: ldur            d3, [x7, #7]
    // 0x794c3c: fmul            d4, d3, d0
    // 0x794c40: fadd            d3, d2, d4
    // 0x794c44: LoadField: d4 = r5->field_f
    //     0x794c44: ldur            d4, [x5, #0xf]
    // 0x794c48: LoadField: d6 = r7->field_f
    //     0x794c48: ldur            d6, [x7, #0xf]
    // 0x794c4c: fmul            d7, d6, d0
    // 0x794c50: fadd            d6, d4, d7
    // 0x794c54: fcmp            d2, d3
    // 0x794c58: b.le            #0x794c64
    // 0x794c5c: mov             v7.16b, v3.16b
    // 0x794c60: b               #0x794cc0
    // 0x794c64: fcmp            d3, d2
    // 0x794c68: b.le            #0x794c74
    // 0x794c6c: mov             v7.16b, v2.16b
    // 0x794c70: b               #0x794cc0
    // 0x794c74: fcmp            d2, d1
    // 0x794c78: b.ne            #0x794c8c
    // 0x794c7c: fadd            d7, d2, d3
    // 0x794c80: fmul            d8, d7, d2
    // 0x794c84: fmul            d7, d8, d3
    // 0x794c88: b               #0x794cc0
    // 0x794c8c: fcmp            d2, d1
    // 0x794c90: b.ne            #0x794cac
    // 0x794c94: fcmp            d3, #0.0
    // 0x794c98: b.vs            #0x794cac
    // 0x794c9c: b.ne            #0x794ca8
    // 0x794ca0: r5 = 0.000000
    //     0x794ca0: fmov            x5, d3
    // 0x794ca4: cmp             x5, #0
    // 0x794ca8: b.lt            #0x794cb4
    // 0x794cac: fcmp            d3, d3
    // 0x794cb0: b.vc            #0x794cbc
    // 0x794cb4: mov             v7.16b, v3.16b
    // 0x794cb8: b               #0x794cc0
    // 0x794cbc: mov             v7.16b, v2.16b
    // 0x794cc0: stur            d7, [fp, #-0x88]
    // 0x794cc4: fcmp            d4, d6
    // 0x794cc8: b.le            #0x794cd4
    // 0x794ccc: mov             v8.16b, v6.16b
    // 0x794cd0: b               #0x794d30
    // 0x794cd4: fcmp            d6, d4
    // 0x794cd8: b.le            #0x794ce4
    // 0x794cdc: mov             v8.16b, v4.16b
    // 0x794ce0: b               #0x794d30
    // 0x794ce4: fcmp            d4, d1
    // 0x794ce8: b.ne            #0x794cfc
    // 0x794cec: fadd            d8, d4, d6
    // 0x794cf0: fmul            d9, d8, d4
    // 0x794cf4: fmul            d8, d9, d6
    // 0x794cf8: b               #0x794d30
    // 0x794cfc: fcmp            d4, d1
    // 0x794d00: b.ne            #0x794d1c
    // 0x794d04: fcmp            d6, #0.0
    // 0x794d08: b.vs            #0x794d1c
    // 0x794d0c: b.ne            #0x794d18
    // 0x794d10: r5 = 0.000000
    //     0x794d10: fmov            x5, d6
    // 0x794d14: cmp             x5, #0
    // 0x794d18: b.lt            #0x794d24
    // 0x794d1c: fcmp            d6, d6
    // 0x794d20: b.vc            #0x794d2c
    // 0x794d24: mov             v8.16b, v6.16b
    // 0x794d28: b               #0x794d30
    // 0x794d2c: mov             v8.16b, v4.16b
    // 0x794d30: stur            d8, [fp, #-0x80]
    // 0x794d34: fcmp            d2, d3
    // 0x794d38: b.gt            #0x794d6c
    // 0x794d3c: fcmp            d3, d2
    // 0x794d40: b.le            #0x794d4c
    // 0x794d44: mov             v2.16b, v3.16b
    // 0x794d48: b               #0x794d6c
    // 0x794d4c: fcmp            d2, d1
    // 0x794d50: b.ne            #0x794d60
    // 0x794d54: fadd            d9, d2, d3
    // 0x794d58: mov             v2.16b, v9.16b
    // 0x794d5c: b               #0x794d6c
    // 0x794d60: fcmp            d3, d3
    // 0x794d64: b.vc            #0x794d6c
    // 0x794d68: mov             v2.16b, v3.16b
    // 0x794d6c: stur            d2, [fp, #-0x78]
    // 0x794d70: fcmp            d4, d6
    // 0x794d74: b.le            #0x794d80
    // 0x794d78: mov             v3.16b, v4.16b
    // 0x794d7c: b               #0x794db4
    // 0x794d80: fcmp            d6, d4
    // 0x794d84: b.le            #0x794d90
    // 0x794d88: mov             v3.16b, v6.16b
    // 0x794d8c: b               #0x794db4
    // 0x794d90: fcmp            d4, d1
    // 0x794d94: b.ne            #0x794da0
    // 0x794d98: fadd            d3, d4, d6
    // 0x794d9c: b               #0x794db4
    // 0x794da0: fcmp            d6, d6
    // 0x794da4: b.vc            #0x794db0
    // 0x794da8: mov             v3.16b, v6.16b
    // 0x794dac: b               #0x794db4
    // 0x794db0: mov             v3.16b, v4.16b
    // 0x794db4: stur            d3, [fp, #-0x70]
    // 0x794db8: scvtf           d4, x4
    // 0x794dbc: fmul            d6, d4, d5
    // 0x794dc0: stur            d6, [fp, #-0x68]
    // 0x794dc4: LoadField: r5 = r2->field_7
    //     0x794dc4: ldur            w5, [x2, #7]
    // 0x794dc8: DecompressPointer r5
    //     0x794dc8: add             x5, x5, HEAP, lsl #32
    // 0x794dcc: cmp             w5, NULL
    // 0x794dd0: b.eq            #0x794e88
    // 0x794dd4: LoadField: r6 = r5->field_7
    //     0x794dd4: ldur            x6, [x5, #7]
    // 0x794dd8: ldr             x5, [x6]
    // 0x794ddc: stur            x5, [fp, #-0x50]
    // 0x794de0: cbnz            x5, #0x794df0
    // 0x794de4: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x794de4: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x794de8: str             x16, [SP]
    // 0x794dec: r0 = _throwNew()
    //     0x794dec: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x794df0: ldur            x0, [fp, #-0x58]
    // 0x794df4: ldur            x2, [fp, #-0x50]
    // 0x794df8: stur            x2, [fp, #-0x50]
    // 0x794dfc: r1 = <Never>
    //     0x794dfc: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x794e00: r0 = Pointer()
    //     0x794e00: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x794e04: mov             x1, x0
    // 0x794e08: ldur            x0, [fp, #-0x50]
    // 0x794e0c: StoreField: r1->field_7 = r0
    //     0x794e0c: stur            x0, [x1, #7]
    // 0x794e10: ldur            d0, [fp, #-0x88]
    // 0x794e14: ldur            d1, [fp, #-0x80]
    // 0x794e18: ldur            d2, [fp, #-0x78]
    // 0x794e1c: ldur            d3, [fp, #-0x70]
    // 0x794e20: ldur            d4, [fp, #-0x68]
    // 0x794e24: d5 = 1.570796
    //     0x794e24: add             x17, PP, #0x43, lsl #12  ; [pp+0x43900] IMM: double(1.5707963267948966) from 0x3ff921fb54442d18
    //     0x794e28: ldr             d5, [x17, #0x900]
    // 0x794e2c: r2 = false
    //     0x794e2c: add             x2, NULL, #0x30  ; false
    // 0x794e30: r0 = __arcTo$Method$FfiNative()
    //     0x794e30: bl              #0x794f7c  ; [dart:ui] _NativePath::__arcTo$Method$FfiNative
    // 0x794e34: ldur            x1, [fp, #-0x58]
    // 0x794e38: add             x4, x1, #1
    // 0x794e3c: ldur            x0, [fp, #-0x60]
    // 0x794e40: b               #0x794bd4
    // 0x794e44: ldur            x0, [fp, #-8]
    // 0x794e48: LeaveFrame
    //     0x794e48: mov             SP, fp
    //     0x794e4c: ldp             fp, lr, [SP], #0x10
    // 0x794e50: ret
    //     0x794e50: ret             
    // 0x794e54: r0 = StackOverflowSharedWithFPURegs()
    //     0x794e54: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x794e58: b               #0x7949b4
    // 0x794e5c: SaveReg d0
    //     0x794e5c: str             q0, [SP, #-0x10]!
    // 0x794e60: SaveReg r0
    //     0x794e60: str             x0, [SP, #-8]!
    // 0x794e64: r0 = AllocateDouble()
    //     0x794e64: bl              #0xec2254  ; AllocateDoubleStub
    // 0x794e68: mov             x1, x0
    // 0x794e6c: RestoreReg r0
    //     0x794e6c: ldr             x0, [SP], #8
    // 0x794e70: RestoreReg d0
    //     0x794e70: ldr             q0, [SP], #0x10
    // 0x794e74: b               #0x794ba8
    // 0x794e78: r0 = StackOverflowSharedWithFPURegs()
    //     0x794e78: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x794e7c: b               #0x794c00
    // 0x794e80: add             x5, x5, x3
    // 0x794e84: b               #0x794c18
    // 0x794e88: r0 = NullErrorSharedWithFPURegs()
    //     0x794e88: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
  }
  _ _shapeRRect(/* No info */) {
    // ** addr: 0x7951e8, size: 0xd0
    // 0x7951e8: EnterFrame
    //     0x7951e8: stp             fp, lr, [SP, #-0x10]!
    //     0x7951ec: mov             fp, SP
    // 0x7951f0: AllocStack(0x20)
    //     0x7951f0: sub             SP, SP, #0x20
    // 0x7951f4: r0 = Instance_Size
    //     0x7951f4: add             x0, PP, #0x50, lsl #12  ; [pp+0x50880] Obj!Size@e2c041
    //     0x7951f8: ldr             x0, [x0, #0x880]
    // 0x7951fc: mov             x16, x2
    // 0x795200: mov             x2, x1
    // 0x795204: mov             x1, x16
    // 0x795208: stur            x1, [fp, #-8]
    // 0x79520c: CheckStackOverflow
    //     0x79520c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x795210: cmp             SP, x16
    //     0x795214: b.ls            #0x7952b0
    // 0x795218: LoadField: d0 = r0->field_f
    //     0x795218: ldur            d0, [x0, #0xf]
    // 0x79521c: stur            d0, [fp, #-0x18]
    // 0x795220: r0 = Offset()
    //     0x795220: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x795224: stur            x0, [fp, #-0x10]
    // 0x795228: StoreField: r0->field_7 = rZR
    //     0x795228: stur            xzr, [x0, #7]
    // 0x79522c: ldur            d0, [fp, #-0x18]
    // 0x795230: StoreField: r0->field_f = d0
    //     0x795230: stur            d0, [x0, #0xf]
    // 0x795234: ldur            x1, [fp, #-8]
    // 0x795238: r0 = size()
    //     0x795238: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x79523c: LoadField: d0 = r0->field_7
    //     0x79523c: ldur            d0, [x0, #7]
    // 0x795240: ldur            x1, [fp, #-8]
    // 0x795244: stur            d0, [fp, #-0x18]
    // 0x795248: r0 = size()
    //     0x795248: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x79524c: LoadField: d0 = r0->field_f
    //     0x79524c: ldur            d0, [x0, #0xf]
    // 0x795250: d1 = 14.000000
    //     0x795250: fmov            d1, #14.00000000
    // 0x795254: fsub            d2, d0, d1
    // 0x795258: stur            d2, [fp, #-0x20]
    // 0x79525c: r0 = Size()
    //     0x79525c: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x795260: ldur            d0, [fp, #-0x18]
    // 0x795264: StoreField: r0->field_7 = d0
    //     0x795264: stur            d0, [x0, #7]
    // 0x795268: ldur            d0, [fp, #-0x20]
    // 0x79526c: StoreField: r0->field_f = d0
    //     0x79526c: stur            d0, [x0, #0xf]
    // 0x795270: ldur            x1, [fp, #-0x10]
    // 0x795274: mov             x2, x0
    // 0x795278: r0 = &()
    //     0x795278: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x79527c: stur            x0, [fp, #-8]
    // 0x795280: r0 = RRect()
    //     0x795280: bl              #0x789ce8  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x795284: mov             x1, x0
    // 0x795288: ldur            x2, [fp, #-8]
    // 0x79528c: r3 = Instance_Radius
    //     0x79528c: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d9a0] Obj!Radius@e2bd31
    //     0x795290: ldr             x3, [x3, #0x9a0]
    // 0x795294: stur            x0, [fp, #-8]
    // 0x795298: r0 = RRect.fromRectAndRadius()
    //     0x795298: bl              #0x789854  ; [dart:ui] RRect::RRect.fromRectAndRadius
    // 0x79529c: ldur            x1, [fp, #-8]
    // 0x7952a0: r0 = scaleRadii()
    //     0x7952a0: bl              #0x7952b8  ; [dart:ui] RRect::scaleRadii
    // 0x7952a4: LeaveFrame
    //     0x7952a4: mov             SP, fp
    //     0x7952a8: ldp             fp, lr, [SP], #0x10
    // 0x7952ac: ret
    //     0x7952ac: ret             
    // 0x7952b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7952b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7952b4: b               #0x795218
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x7fcb64, size: 0x170
    // 0x7fcb64: EnterFrame
    //     0x7fcb64: stp             fp, lr, [SP, #-0x10]!
    //     0x7fcb68: mov             fp, SP
    // 0x7fcb6c: AllocStack(0x48)
    //     0x7fcb6c: sub             SP, SP, #0x48
    // 0x7fcb70: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r5, fp-0x18 */, dynamic _ /* r2 => r4, fp-0x20 */, dynamic _ /* r3 => r3, fp-0x28 */)
    //     0x7fcb70: mov             x5, x1
    //     0x7fcb74: mov             x4, x2
    //     0x7fcb78: stur            x1, [fp, #-0x18]
    //     0x7fcb7c: stur            x2, [fp, #-0x20]
    //     0x7fcb80: stur            x3, [fp, #-0x28]
    // 0x7fcb84: CheckStackOverflow
    //     0x7fcb84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fcb88: cmp             SP, x16
    //     0x7fcb8c: b.ls            #0x7fccc8
    // 0x7fcb90: LoadField: r6 = r5->field_57
    //     0x7fcb90: ldur            w6, [x5, #0x57]
    // 0x7fcb94: DecompressPointer r6
    //     0x7fcb94: add             x6, x6, HEAP, lsl #32
    // 0x7fcb98: stur            x6, [fp, #-0x10]
    // 0x7fcb9c: cmp             w6, NULL
    // 0x7fcba0: b.ne            #0x7fcbb4
    // 0x7fcba4: r0 = false
    //     0x7fcba4: add             x0, NULL, #0x30  ; false
    // 0x7fcba8: LeaveFrame
    //     0x7fcba8: mov             SP, fp
    //     0x7fcbac: ldp             fp, lr, [SP], #0x10
    // 0x7fcbb0: ret
    //     0x7fcbb0: ret             
    // 0x7fcbb4: LoadField: r7 = r6->field_7
    //     0x7fcbb4: ldur            w7, [x6, #7]
    // 0x7fcbb8: DecompressPointer r7
    //     0x7fcbb8: add             x7, x7, HEAP, lsl #32
    // 0x7fcbbc: stur            x7, [fp, #-8]
    // 0x7fcbc0: cmp             w7, NULL
    // 0x7fcbc4: b.eq            #0x7fccd0
    // 0x7fcbc8: mov             x0, x7
    // 0x7fcbcc: r2 = Null
    //     0x7fcbcc: mov             x2, NULL
    // 0x7fcbd0: r1 = Null
    //     0x7fcbd0: mov             x1, NULL
    // 0x7fcbd4: r4 = LoadClassIdInstr(r0)
    //     0x7fcbd4: ldur            x4, [x0, #-1]
    //     0x7fcbd8: ubfx            x4, x4, #0xc, #0x14
    // 0x7fcbdc: sub             x4, x4, #0xc71
    // 0x7fcbe0: cmp             x4, #0xf
    // 0x7fcbe4: b.ls            #0x7fcbfc
    // 0x7fcbe8: r8 = BoxParentData
    //     0x7fcbe8: add             x8, PP, #0x25, lsl #12  ; [pp+0x252c8] Type: BoxParentData
    //     0x7fcbec: ldr             x8, [x8, #0x2c8]
    // 0x7fcbf0: r3 = Null
    //     0x7fcbf0: add             x3, PP, #0x50, lsl #12  ; [pp+0x50870] Null
    //     0x7fcbf4: ldr             x3, [x3, #0x870]
    // 0x7fcbf8: r0 = DefaultTypeTest()
    //     0x7fcbf8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7fcbfc: ldur            x0, [fp, #-8]
    // 0x7fcc00: LoadField: r1 = r0->field_7
    //     0x7fcc00: ldur            w1, [x0, #7]
    // 0x7fcc04: DecompressPointer r1
    //     0x7fcc04: add             x1, x1, HEAP, lsl #32
    // 0x7fcc08: LoadField: d0 = r1->field_7
    //     0x7fcc08: ldur            d0, [x1, #7]
    // 0x7fcc0c: stur            d0, [fp, #-0x38]
    // 0x7fcc10: LoadField: d1 = r1->field_f
    //     0x7fcc10: ldur            d1, [x1, #0xf]
    // 0x7fcc14: r0 = Instance_Size
    //     0x7fcc14: add             x0, PP, #0x50, lsl #12  ; [pp+0x50880] Obj!Size@e2c041
    //     0x7fcc18: ldr             x0, [x0, #0x880]
    // 0x7fcc1c: LoadField: d2 = r0->field_f
    //     0x7fcc1c: ldur            d2, [x0, #0xf]
    // 0x7fcc20: fadd            d3, d1, d2
    // 0x7fcc24: ldur            x1, [fp, #-0x10]
    // 0x7fcc28: stur            d3, [fp, #-0x30]
    // 0x7fcc2c: r0 = size()
    //     0x7fcc2c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fcc30: LoadField: d0 = r0->field_7
    //     0x7fcc30: ldur            d0, [x0, #7]
    // 0x7fcc34: ldur            x1, [fp, #-0x10]
    // 0x7fcc38: stur            d0, [fp, #-0x40]
    // 0x7fcc3c: r0 = size()
    //     0x7fcc3c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fcc40: LoadField: d0 = r0->field_f
    //     0x7fcc40: ldur            d0, [x0, #0xf]
    // 0x7fcc44: d1 = 14.000000
    //     0x7fcc44: fmov            d1, #14.00000000
    // 0x7fcc48: fsub            d2, d0, d1
    // 0x7fcc4c: ldur            d1, [fp, #-0x38]
    // 0x7fcc50: ldur            d0, [fp, #-0x40]
    // 0x7fcc54: fadd            d3, d1, d0
    // 0x7fcc58: ldur            d0, [fp, #-0x30]
    // 0x7fcc5c: stur            d3, [fp, #-0x48]
    // 0x7fcc60: fadd            d4, d0, d2
    // 0x7fcc64: stur            d4, [fp, #-0x40]
    // 0x7fcc68: r0 = Rect()
    //     0x7fcc68: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x7fcc6c: ldur            d0, [fp, #-0x38]
    // 0x7fcc70: StoreField: r0->field_7 = d0
    //     0x7fcc70: stur            d0, [x0, #7]
    // 0x7fcc74: ldur            d0, [fp, #-0x30]
    // 0x7fcc78: StoreField: r0->field_f = d0
    //     0x7fcc78: stur            d0, [x0, #0xf]
    // 0x7fcc7c: ldur            d0, [fp, #-0x48]
    // 0x7fcc80: ArrayStore: r0[0] = d0  ; List_8
    //     0x7fcc80: stur            d0, [x0, #0x17]
    // 0x7fcc84: ldur            d0, [fp, #-0x40]
    // 0x7fcc88: StoreField: r0->field_1f = d0
    //     0x7fcc88: stur            d0, [x0, #0x1f]
    // 0x7fcc8c: mov             x1, x0
    // 0x7fcc90: ldur            x2, [fp, #-0x28]
    // 0x7fcc94: r0 = contains()
    //     0x7fcc94: bl              #0x7992fc  ; [dart:ui] Rect::contains
    // 0x7fcc98: tbz             w0, #4, #0x7fccac
    // 0x7fcc9c: r0 = false
    //     0x7fcc9c: add             x0, NULL, #0x30  ; false
    // 0x7fcca0: LeaveFrame
    //     0x7fcca0: mov             SP, fp
    //     0x7fcca4: ldp             fp, lr, [SP], #0x10
    // 0x7fcca8: ret
    //     0x7fcca8: ret             
    // 0x7fccac: ldur            x1, [fp, #-0x18]
    // 0x7fccb0: ldur            x2, [fp, #-0x20]
    // 0x7fccb4: ldur            x3, [fp, #-0x28]
    // 0x7fccb8: r0 = hitTestChildren()
    //     0x7fccb8: bl              #0x7fccd4  ; [package:flutter/src/rendering/shifted_box.dart] RenderShiftedBox::hitTestChildren
    // 0x7fccbc: LeaveFrame
    //     0x7fccbc: mov             SP, fp
    //     0x7fccc0: ldp             fp, lr, [SP], #0x10
    // 0x7fccc4: ret
    //     0x7fccc4: ret             
    // 0x7fccc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fccc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fcccc: b               #0x7fcb90
    // 0x7fccd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fccd0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _RenderCupertinoTextSelectionToolbarShape(/* No info */) {
    // ** addr: 0x859e88, size: 0x118
    // 0x859e88: EnterFrame
    //     0x859e88: stp             fp, lr, [SP, #-0x10]!
    //     0x859e8c: mov             fp, SP
    // 0x859e90: AllocStack(0x20)
    //     0x859e90: sub             SP, SP, #0x20
    // 0x859e94: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0x859e94: mov             x4, x1
    //     0x859e98: stur            x2, [fp, #-0x10]
    //     0x859e9c: mov             x16, x3
    //     0x859ea0: mov             x3, x2
    //     0x859ea4: mov             x2, x16
    //     0x859ea8: mov             x0, x5
    //     0x859eac: stur            x1, [fp, #-8]
    //     0x859eb0: stur            x2, [fp, #-0x18]
    //     0x859eb4: stur            x5, [fp, #-0x20]
    // 0x859eb8: CheckStackOverflow
    //     0x859eb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x859ebc: cmp             SP, x16
    //     0x859ec0: b.ls            #0x859f98
    // 0x859ec4: r1 = <ClipPathLayer>
    //     0x859ec4: add             x1, PP, #0x46, lsl #12  ; [pp+0x46770] TypeArguments: <ClipPathLayer>
    //     0x859ec8: ldr             x1, [x1, #0x770]
    // 0x859ecc: r0 = LayerHandle()
    //     0x859ecc: bl              #0x6d28c4  ; AllocateLayerHandleStub -> LayerHandle<X0 bound Layer> (size=0x10)
    // 0x859ed0: ldur            x1, [fp, #-8]
    // 0x859ed4: StoreField: r1->field_67 = r0
    //     0x859ed4: stur            w0, [x1, #0x67]
    //     0x859ed8: ldurb           w16, [x1, #-1]
    //     0x859edc: ldurb           w17, [x0, #-1]
    //     0x859ee0: and             x16, x17, x16, lsr #2
    //     0x859ee4: tst             x16, HEAP, lsr #32
    //     0x859ee8: b.eq            #0x859ef0
    //     0x859eec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x859ef0: ldur            x0, [fp, #-0x10]
    // 0x859ef4: StoreField: r1->field_5b = r0
    //     0x859ef4: stur            w0, [x1, #0x5b]
    //     0x859ef8: ldurb           w16, [x1, #-1]
    //     0x859efc: ldurb           w17, [x0, #-1]
    //     0x859f00: and             x16, x17, x16, lsr #2
    //     0x859f04: tst             x16, HEAP, lsr #32
    //     0x859f08: b.eq            #0x859f10
    //     0x859f0c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x859f10: ldur            x0, [fp, #-0x18]
    // 0x859f14: StoreField: r1->field_5f = r0
    //     0x859f14: stur            w0, [x1, #0x5f]
    //     0x859f18: ldurb           w16, [x1, #-1]
    //     0x859f1c: ldurb           w17, [x0, #-1]
    //     0x859f20: and             x16, x17, x16, lsr #2
    //     0x859f24: tst             x16, HEAP, lsr #32
    //     0x859f28: b.eq            #0x859f30
    //     0x859f2c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x859f30: ldur            x0, [fp, #-0x20]
    // 0x859f34: StoreField: r1->field_63 = r0
    //     0x859f34: stur            w0, [x1, #0x63]
    //     0x859f38: ldurb           w16, [x1, #-1]
    //     0x859f3c: ldurb           w17, [x0, #-1]
    //     0x859f40: and             x16, x17, x16, lsr #2
    //     0x859f44: tst             x16, HEAP, lsr #32
    //     0x859f48: b.eq            #0x859f50
    //     0x859f4c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x859f50: r0 = _LayoutCacheStorage()
    //     0x859f50: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x859f54: ldur            x2, [fp, #-8]
    // 0x859f58: StoreField: r2->field_4f = r0
    //     0x859f58: stur            w0, [x2, #0x4f]
    //     0x859f5c: ldurb           w16, [x2, #-1]
    //     0x859f60: ldurb           w17, [x0, #-1]
    //     0x859f64: and             x16, x17, x16, lsr #2
    //     0x859f68: tst             x16, HEAP, lsr #32
    //     0x859f6c: b.eq            #0x859f74
    //     0x859f70: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x859f74: mov             x1, x2
    // 0x859f78: r0 = RenderObject()
    //     0x859f78: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x859f7c: ldur            x1, [fp, #-8]
    // 0x859f80: r2 = Null
    //     0x859f80: mov             x2, NULL
    // 0x859f84: r0 = child=()
    //     0x859f84: bl              #0x895c88  ; [package:flutter/src/rendering/shifted_box.dart] _RenderShiftedBox&RenderBox&RenderObjectWithChildMixin::child=
    // 0x859f88: r0 = Null
    //     0x859f88: mov             x0, NULL
    // 0x859f8c: LeaveFrame
    //     0x859f8c: mov             SP, fp
    //     0x859f90: ldp             fp, lr, [SP], #0x10
    // 0x859f94: ret
    //     0x859f94: ret             
    // 0x859f98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x859f98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x859f9c: b               #0x859ec4
  }
  set _ shadowColor=(/* No info */) {
    // ** addr: 0xc6d000, size: 0xa4
    // 0xc6d000: EnterFrame
    //     0xc6d000: stp             fp, lr, [SP, #-0x10]!
    //     0xc6d004: mov             fp, SP
    // 0xc6d008: AllocStack(0x20)
    //     0xc6d008: sub             SP, SP, #0x20
    // 0xc6d00c: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc6d00c: stur            x1, [fp, #-8]
    //     0xc6d010: mov             x16, x2
    //     0xc6d014: mov             x2, x1
    //     0xc6d018: mov             x1, x16
    //     0xc6d01c: stur            x1, [fp, #-0x10]
    // 0xc6d020: CheckStackOverflow
    //     0xc6d020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6d024: cmp             SP, x16
    //     0xc6d028: b.ls            #0xc6d09c
    // 0xc6d02c: LoadField: r0 = r2->field_63
    //     0xc6d02c: ldur            w0, [x2, #0x63]
    // 0xc6d030: DecompressPointer r0
    //     0xc6d030: add             x0, x0, HEAP, lsl #32
    // 0xc6d034: r3 = LoadClassIdInstr(r1)
    //     0xc6d034: ldur            x3, [x1, #-1]
    //     0xc6d038: ubfx            x3, x3, #0xc, #0x14
    // 0xc6d03c: stp             x0, x1, [SP]
    // 0xc6d040: mov             x0, x3
    // 0xc6d044: mov             lr, x0
    // 0xc6d048: ldr             lr, [x21, lr, lsl #3]
    // 0xc6d04c: blr             lr
    // 0xc6d050: tbnz            w0, #4, #0xc6d064
    // 0xc6d054: r0 = Null
    //     0xc6d054: mov             x0, NULL
    // 0xc6d058: LeaveFrame
    //     0xc6d058: mov             SP, fp
    //     0xc6d05c: ldp             fp, lr, [SP], #0x10
    // 0xc6d060: ret
    //     0xc6d060: ret             
    // 0xc6d064: ldur            x1, [fp, #-8]
    // 0xc6d068: ldur            x0, [fp, #-0x10]
    // 0xc6d06c: StoreField: r1->field_63 = r0
    //     0xc6d06c: stur            w0, [x1, #0x63]
    //     0xc6d070: ldurb           w16, [x1, #-1]
    //     0xc6d074: ldurb           w17, [x0, #-1]
    //     0xc6d078: and             x16, x17, x16, lsr #2
    //     0xc6d07c: tst             x16, HEAP, lsr #32
    //     0xc6d080: b.eq            #0xc6d088
    //     0xc6d084: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6d088: r0 = markNeedsPaint()
    //     0xc6d088: bl              #0x786214  ; [package:flutter/src/rendering/object.dart] RenderObject::markNeedsPaint
    // 0xc6d08c: r0 = Null
    //     0xc6d08c: mov             x0, NULL
    // 0xc6d090: LeaveFrame
    //     0xc6d090: mov             SP, fp
    //     0xc6d094: ldp             fp, lr, [SP], #0x10
    // 0xc6d098: ret
    //     0xc6d098: ret             
    // 0xc6d09c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6d09c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6d0a0: b               #0xc6d02c
  }
  set _ anchorBelow=(/* No info */) {
    // ** addr: 0xc6d0a4, size: 0x88
    // 0xc6d0a4: EnterFrame
    //     0xc6d0a4: stp             fp, lr, [SP, #-0x10]!
    //     0xc6d0a8: mov             fp, SP
    // 0xc6d0ac: AllocStack(0x20)
    //     0xc6d0ac: sub             SP, SP, #0x20
    // 0xc6d0b0: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc6d0b0: mov             x0, x2
    //     0xc6d0b4: stur            x1, [fp, #-8]
    //     0xc6d0b8: stur            x2, [fp, #-0x10]
    // 0xc6d0bc: CheckStackOverflow
    //     0xc6d0bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6d0c0: cmp             SP, x16
    //     0xc6d0c4: b.ls            #0xc6d124
    // 0xc6d0c8: LoadField: r2 = r1->field_5f
    //     0xc6d0c8: ldur            w2, [x1, #0x5f]
    // 0xc6d0cc: DecompressPointer r2
    //     0xc6d0cc: add             x2, x2, HEAP, lsl #32
    // 0xc6d0d0: stp             x2, x0, [SP]
    // 0xc6d0d4: r0 = ==()
    //     0xc6d0d4: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0xc6d0d8: tbnz            w0, #4, #0xc6d0ec
    // 0xc6d0dc: r0 = Null
    //     0xc6d0dc: mov             x0, NULL
    // 0xc6d0e0: LeaveFrame
    //     0xc6d0e0: mov             SP, fp
    //     0xc6d0e4: ldp             fp, lr, [SP], #0x10
    // 0xc6d0e8: ret
    //     0xc6d0e8: ret             
    // 0xc6d0ec: ldur            x1, [fp, #-8]
    // 0xc6d0f0: ldur            x0, [fp, #-0x10]
    // 0xc6d0f4: StoreField: r1->field_5f = r0
    //     0xc6d0f4: stur            w0, [x1, #0x5f]
    //     0xc6d0f8: ldurb           w16, [x1, #-1]
    //     0xc6d0fc: ldurb           w17, [x0, #-1]
    //     0xc6d100: and             x16, x17, x16, lsr #2
    //     0xc6d104: tst             x16, HEAP, lsr #32
    //     0xc6d108: b.eq            #0xc6d110
    //     0xc6d10c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6d110: r0 = markNeedsLayout()
    //     0xc6d110: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6d114: r0 = Null
    //     0xc6d114: mov             x0, NULL
    // 0xc6d118: LeaveFrame
    //     0xc6d118: mov             SP, fp
    //     0xc6d11c: ldp             fp, lr, [SP], #0x10
    // 0xc6d120: ret
    //     0xc6d120: ret             
    // 0xc6d124: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6d124: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6d128: b               #0xc6d0c8
  }
  set _ anchorAbove=(/* No info */) {
    // ** addr: 0xc6d12c, size: 0x88
    // 0xc6d12c: EnterFrame
    //     0xc6d12c: stp             fp, lr, [SP, #-0x10]!
    //     0xc6d130: mov             fp, SP
    // 0xc6d134: AllocStack(0x20)
    //     0xc6d134: sub             SP, SP, #0x20
    // 0xc6d138: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc6d138: mov             x0, x2
    //     0xc6d13c: stur            x1, [fp, #-8]
    //     0xc6d140: stur            x2, [fp, #-0x10]
    // 0xc6d144: CheckStackOverflow
    //     0xc6d144: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6d148: cmp             SP, x16
    //     0xc6d14c: b.ls            #0xc6d1ac
    // 0xc6d150: LoadField: r2 = r1->field_5b
    //     0xc6d150: ldur            w2, [x1, #0x5b]
    // 0xc6d154: DecompressPointer r2
    //     0xc6d154: add             x2, x2, HEAP, lsl #32
    // 0xc6d158: stp             x2, x0, [SP]
    // 0xc6d15c: r0 = ==()
    //     0xc6d15c: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0xc6d160: tbnz            w0, #4, #0xc6d174
    // 0xc6d164: r0 = Null
    //     0xc6d164: mov             x0, NULL
    // 0xc6d168: LeaveFrame
    //     0xc6d168: mov             SP, fp
    //     0xc6d16c: ldp             fp, lr, [SP], #0x10
    // 0xc6d170: ret
    //     0xc6d170: ret             
    // 0xc6d174: ldur            x1, [fp, #-8]
    // 0xc6d178: ldur            x0, [fp, #-0x10]
    // 0xc6d17c: StoreField: r1->field_5b = r0
    //     0xc6d17c: stur            w0, [x1, #0x5b]
    //     0xc6d180: ldurb           w16, [x1, #-1]
    //     0xc6d184: ldurb           w17, [x0, #-1]
    //     0xc6d188: and             x16, x17, x16, lsr #2
    //     0xc6d18c: tst             x16, HEAP, lsr #32
    //     0xc6d190: b.eq            #0xc6d198
    //     0xc6d194: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6d198: r0 = markNeedsLayout()
    //     0xc6d198: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6d19c: r0 = Null
    //     0xc6d19c: mov             x0, NULL
    // 0xc6d1a0: LeaveFrame
    //     0xc6d1a0: mov             SP, fp
    //     0xc6d1a4: ldp             fp, lr, [SP], #0x10
    // 0xc6d1a8: ret
    //     0xc6d1a8: ret             
    // 0xc6d1ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6d1ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6d1b0: b               #0xc6d150
  }
}

// class id: 4333, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin extends State<dynamic>
     with TickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6f26bc, size: 0x184
    // 0x6f26bc: EnterFrame
    //     0x6f26bc: stp             fp, lr, [SP, #-0x10]!
    //     0x6f26c0: mov             fp, SP
    // 0x6f26c4: AllocStack(0x20)
    //     0x6f26c4: sub             SP, SP, #0x20
    // 0x6f26c8: SetupParameters(__CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6f26c8: mov             x0, x1
    //     0x6f26cc: stur            x1, [fp, #-8]
    //     0x6f26d0: stur            x2, [fp, #-0x10]
    // 0x6f26d4: CheckStackOverflow
    //     0x6f26d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f26d8: cmp             SP, x16
    //     0x6f26dc: b.ls            #0x6f2830
    // 0x6f26e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6f26e0: ldur            w1, [x0, #0x17]
    // 0x6f26e4: DecompressPointer r1
    //     0x6f26e4: add             x1, x1, HEAP, lsl #32
    // 0x6f26e8: cmp             w1, NULL
    // 0x6f26ec: b.ne            #0x6f26f8
    // 0x6f26f0: mov             x1, x0
    // 0x6f26f4: r0 = _updateTickerModeNotifier()
    //     0x6f26f4: bl              #0x6f2864  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6f26f8: ldur            x0, [fp, #-8]
    // 0x6f26fc: LoadField: r1 = r0->field_13
    //     0x6f26fc: ldur            w1, [x0, #0x13]
    // 0x6f2700: DecompressPointer r1
    //     0x6f2700: add             x1, x1, HEAP, lsl #32
    // 0x6f2704: cmp             w1, NULL
    // 0x6f2708: b.ne            #0x6f27a0
    // 0x6f270c: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x6f270c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6f2710: ldr             x0, [x0, #0x778]
    //     0x6f2714: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6f2718: cmp             w0, w16
    //     0x6f271c: b.ne            #0x6f2728
    //     0x6f2720: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x6f2724: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6f2728: r1 = <_WidgetTicker>
    //     0x6f2728: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8c0] TypeArguments: <_WidgetTicker>
    //     0x6f272c: ldr             x1, [x1, #0x8c0]
    // 0x6f2730: stur            x0, [fp, #-0x18]
    // 0x6f2734: r0 = _Set()
    //     0x6f2734: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x6f2738: mov             x1, x0
    // 0x6f273c: ldur            x0, [fp, #-0x18]
    // 0x6f2740: stur            x1, [fp, #-0x20]
    // 0x6f2744: StoreField: r1->field_1b = r0
    //     0x6f2744: stur            w0, [x1, #0x1b]
    // 0x6f2748: StoreField: r1->field_b = rZR
    //     0x6f2748: stur            wzr, [x1, #0xb]
    // 0x6f274c: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x6f274c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6f2750: ldr             x0, [x0, #0x780]
    //     0x6f2754: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6f2758: cmp             w0, w16
    //     0x6f275c: b.ne            #0x6f2768
    //     0x6f2760: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x6f2764: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6f2768: mov             x1, x0
    // 0x6f276c: ldur            x0, [fp, #-0x20]
    // 0x6f2770: StoreField: r0->field_f = r1
    //     0x6f2770: stur            w1, [x0, #0xf]
    // 0x6f2774: StoreField: r0->field_13 = rZR
    //     0x6f2774: stur            wzr, [x0, #0x13]
    // 0x6f2778: ArrayStore: r0[0] = rZR  ; List_4
    //     0x6f2778: stur            wzr, [x0, #0x17]
    // 0x6f277c: ldur            x1, [fp, #-8]
    // 0x6f2780: StoreField: r1->field_13 = r0
    //     0x6f2780: stur            w0, [x1, #0x13]
    //     0x6f2784: ldurb           w16, [x1, #-1]
    //     0x6f2788: ldurb           w17, [x0, #-1]
    //     0x6f278c: and             x16, x17, x16, lsr #2
    //     0x6f2790: tst             x16, HEAP, lsr #32
    //     0x6f2794: b.eq            #0x6f279c
    //     0x6f2798: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6f279c: b               #0x6f27a4
    // 0x6f27a0: mov             x1, x0
    // 0x6f27a4: ldur            x0, [fp, #-0x10]
    // 0x6f27a8: r0 = _WidgetTicker()
    //     0x6f27a8: bl              #0x6f03ec  ; Allocate_WidgetTickerStub -> _WidgetTicker (size=0x20)
    // 0x6f27ac: mov             x3, x0
    // 0x6f27b0: ldur            x2, [fp, #-8]
    // 0x6f27b4: stur            x3, [fp, #-0x18]
    // 0x6f27b8: StoreField: r3->field_1b = r2
    //     0x6f27b8: stur            w2, [x3, #0x1b]
    // 0x6f27bc: r0 = false
    //     0x6f27bc: add             x0, NULL, #0x30  ; false
    // 0x6f27c0: StoreField: r3->field_b = r0
    //     0x6f27c0: stur            w0, [x3, #0xb]
    // 0x6f27c4: ldur            x0, [fp, #-0x10]
    // 0x6f27c8: StoreField: r3->field_13 = r0
    //     0x6f27c8: stur            w0, [x3, #0x13]
    // 0x6f27cc: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6f27cc: ldur            w1, [x2, #0x17]
    // 0x6f27d0: DecompressPointer r1
    //     0x6f27d0: add             x1, x1, HEAP, lsl #32
    // 0x6f27d4: cmp             w1, NULL
    // 0x6f27d8: b.eq            #0x6f2838
    // 0x6f27dc: r0 = LoadClassIdInstr(r1)
    //     0x6f27dc: ldur            x0, [x1, #-1]
    //     0x6f27e0: ubfx            x0, x0, #0xc, #0x14
    // 0x6f27e4: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6f27e4: movz            x17, #0x276f
    //     0x6f27e8: movk            x17, #0x1, lsl #16
    //     0x6f27ec: add             lr, x0, x17
    //     0x6f27f0: ldr             lr, [x21, lr, lsl #3]
    //     0x6f27f4: blr             lr
    // 0x6f27f8: eor             x2, x0, #0x10
    // 0x6f27fc: ldur            x1, [fp, #-0x18]
    // 0x6f2800: r0 = muted=()
    //     0x6f2800: bl              #0x6efbf4  ; [package:flutter/src/scheduler/ticker.dart] Ticker::muted=
    // 0x6f2804: ldur            x0, [fp, #-8]
    // 0x6f2808: LoadField: r1 = r0->field_13
    //     0x6f2808: ldur            w1, [x0, #0x13]
    // 0x6f280c: DecompressPointer r1
    //     0x6f280c: add             x1, x1, HEAP, lsl #32
    // 0x6f2810: cmp             w1, NULL
    // 0x6f2814: b.eq            #0x6f283c
    // 0x6f2818: ldur            x2, [fp, #-0x18]
    // 0x6f281c: r0 = add()
    //     0x6f281c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x6f2820: ldur            x0, [fp, #-0x18]
    // 0x6f2824: LeaveFrame
    //     0x6f2824: mov             SP, fp
    //     0x6f2828: ldp             fp, lr, [SP], #0x10
    // 0x6f282c: ret
    //     0x6f282c: ret             
    // 0x6f2830: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f2830: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f2834: b               #0x6f26e0
    // 0x6f2838: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f2838: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6f283c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f283c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6f2864, size: 0x124
    // 0x6f2864: EnterFrame
    //     0x6f2864: stp             fp, lr, [SP, #-0x10]!
    //     0x6f2868: mov             fp, SP
    // 0x6f286c: AllocStack(0x18)
    //     0x6f286c: sub             SP, SP, #0x18
    // 0x6f2870: SetupParameters(__CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6f2870: mov             x2, x1
    //     0x6f2874: stur            x1, [fp, #-8]
    // 0x6f2878: CheckStackOverflow
    //     0x6f2878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f287c: cmp             SP, x16
    //     0x6f2880: b.ls            #0x6f297c
    // 0x6f2884: LoadField: r1 = r2->field_f
    //     0x6f2884: ldur            w1, [x2, #0xf]
    // 0x6f2888: DecompressPointer r1
    //     0x6f2888: add             x1, x1, HEAP, lsl #32
    // 0x6f288c: cmp             w1, NULL
    // 0x6f2890: b.eq            #0x6f2984
    // 0x6f2894: r0 = getNotifier()
    //     0x6f2894: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6f2898: mov             x3, x0
    // 0x6f289c: ldur            x0, [fp, #-8]
    // 0x6f28a0: stur            x3, [fp, #-0x18]
    // 0x6f28a4: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6f28a4: ldur            w4, [x0, #0x17]
    // 0x6f28a8: DecompressPointer r4
    //     0x6f28a8: add             x4, x4, HEAP, lsl #32
    // 0x6f28ac: stur            x4, [fp, #-0x10]
    // 0x6f28b0: cmp             w3, w4
    // 0x6f28b4: b.ne            #0x6f28c8
    // 0x6f28b8: r0 = Null
    //     0x6f28b8: mov             x0, NULL
    // 0x6f28bc: LeaveFrame
    //     0x6f28bc: mov             SP, fp
    //     0x6f28c0: ldp             fp, lr, [SP], #0x10
    // 0x6f28c4: ret
    //     0x6f28c4: ret             
    // 0x6f28c8: cmp             w4, NULL
    // 0x6f28cc: b.eq            #0x6f2910
    // 0x6f28d0: mov             x2, x0
    // 0x6f28d4: r1 = Function '_updateTickers@364311458':.
    //     0x6f28d4: add             x1, PP, #0x57, lsl #12  ; [pp+0x575d0] AnonymousClosure: (0x6f2988), in [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickers (0x6f29c0)
    //     0x6f28d8: ldr             x1, [x1, #0x5d0]
    // 0x6f28dc: r0 = AllocateClosure()
    //     0x6f28dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f28e0: ldur            x1, [fp, #-0x10]
    // 0x6f28e4: r2 = LoadClassIdInstr(r1)
    //     0x6f28e4: ldur            x2, [x1, #-1]
    //     0x6f28e8: ubfx            x2, x2, #0xc, #0x14
    // 0x6f28ec: mov             x16, x0
    // 0x6f28f0: mov             x0, x2
    // 0x6f28f4: mov             x2, x16
    // 0x6f28f8: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6f28f8: movz            x17, #0xbf5c
    //     0x6f28fc: add             lr, x0, x17
    //     0x6f2900: ldr             lr, [x21, lr, lsl #3]
    //     0x6f2904: blr             lr
    // 0x6f2908: ldur            x0, [fp, #-8]
    // 0x6f290c: ldur            x3, [fp, #-0x18]
    // 0x6f2910: mov             x2, x0
    // 0x6f2914: r1 = Function '_updateTickers@364311458':.
    //     0x6f2914: add             x1, PP, #0x57, lsl #12  ; [pp+0x575d0] AnonymousClosure: (0x6f2988), in [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickers (0x6f29c0)
    //     0x6f2918: ldr             x1, [x1, #0x5d0]
    // 0x6f291c: r0 = AllocateClosure()
    //     0x6f291c: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f2920: ldur            x3, [fp, #-0x18]
    // 0x6f2924: r1 = LoadClassIdInstr(r3)
    //     0x6f2924: ldur            x1, [x3, #-1]
    //     0x6f2928: ubfx            x1, x1, #0xc, #0x14
    // 0x6f292c: mov             x2, x0
    // 0x6f2930: mov             x0, x1
    // 0x6f2934: mov             x1, x3
    // 0x6f2938: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6f2938: movz            x17, #0xc407
    //     0x6f293c: add             lr, x0, x17
    //     0x6f2940: ldr             lr, [x21, lr, lsl #3]
    //     0x6f2944: blr             lr
    // 0x6f2948: ldur            x0, [fp, #-0x18]
    // 0x6f294c: ldur            x1, [fp, #-8]
    // 0x6f2950: ArrayStore: r1[0] = r0  ; List_4
    //     0x6f2950: stur            w0, [x1, #0x17]
    //     0x6f2954: ldurb           w16, [x1, #-1]
    //     0x6f2958: ldurb           w17, [x0, #-1]
    //     0x6f295c: and             x16, x17, x16, lsr #2
    //     0x6f2960: tst             x16, HEAP, lsr #32
    //     0x6f2964: b.eq            #0x6f296c
    //     0x6f2968: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6f296c: r0 = Null
    //     0x6f296c: mov             x0, NULL
    // 0x6f2970: LeaveFrame
    //     0x6f2970: mov             SP, fp
    //     0x6f2974: ldp             fp, lr, [SP], #0x10
    // 0x6f2978: ret
    //     0x6f2978: ret             
    // 0x6f297c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f297c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f2980: b               #0x6f2884
    // 0x6f2984: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f2984: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTickers(dynamic) {
    // ** addr: 0x6f2988, size: 0x38
    // 0x6f2988: EnterFrame
    //     0x6f2988: stp             fp, lr, [SP, #-0x10]!
    //     0x6f298c: mov             fp, SP
    // 0x6f2990: ldr             x0, [fp, #0x10]
    // 0x6f2994: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6f2994: ldur            w1, [x0, #0x17]
    // 0x6f2998: DecompressPointer r1
    //     0x6f2998: add             x1, x1, HEAP, lsl #32
    // 0x6f299c: CheckStackOverflow
    //     0x6f299c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f29a0: cmp             SP, x16
    //     0x6f29a4: b.ls            #0x6f29b8
    // 0x6f29a8: r0 = _updateTickers()
    //     0x6f29a8: bl              #0x6f29c0  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickers
    // 0x6f29ac: LeaveFrame
    //     0x6f29ac: mov             SP, fp
    //     0x6f29b0: ldp             fp, lr, [SP], #0x10
    // 0x6f29b4: ret
    //     0x6f29b4: ret             
    // 0x6f29b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f29b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f29bc: b               #0x6f29a8
  }
  _ _updateTickers(/* No info */) {
    // ** addr: 0x6f29c0, size: 0x164
    // 0x6f29c0: EnterFrame
    //     0x6f29c0: stp             fp, lr, [SP, #-0x10]!
    //     0x6f29c4: mov             fp, SP
    // 0x6f29c8: AllocStack(0x20)
    //     0x6f29c8: sub             SP, SP, #0x20
    // 0x6f29cc: SetupParameters(__CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6f29cc: mov             x2, x1
    //     0x6f29d0: stur            x1, [fp, #-8]
    // 0x6f29d4: CheckStackOverflow
    //     0x6f29d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f29d8: cmp             SP, x16
    //     0x6f29dc: b.ls            #0x6f2b0c
    // 0x6f29e0: LoadField: r0 = r2->field_13
    //     0x6f29e0: ldur            w0, [x2, #0x13]
    // 0x6f29e4: DecompressPointer r0
    //     0x6f29e4: add             x0, x0, HEAP, lsl #32
    // 0x6f29e8: cmp             w0, NULL
    // 0x6f29ec: b.eq            #0x6f2afc
    // 0x6f29f0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6f29f0: ldur            w1, [x2, #0x17]
    // 0x6f29f4: DecompressPointer r1
    //     0x6f29f4: add             x1, x1, HEAP, lsl #32
    // 0x6f29f8: cmp             w1, NULL
    // 0x6f29fc: b.eq            #0x6f2b14
    // 0x6f2a00: r0 = LoadClassIdInstr(r1)
    //     0x6f2a00: ldur            x0, [x1, #-1]
    //     0x6f2a04: ubfx            x0, x0, #0xc, #0x14
    // 0x6f2a08: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6f2a08: movz            x17, #0x276f
    //     0x6f2a0c: movk            x17, #0x1, lsl #16
    //     0x6f2a10: add             lr, x0, x17
    //     0x6f2a14: ldr             lr, [x21, lr, lsl #3]
    //     0x6f2a18: blr             lr
    // 0x6f2a1c: eor             x2, x0, #0x10
    // 0x6f2a20: ldur            x0, [fp, #-8]
    // 0x6f2a24: stur            x2, [fp, #-0x10]
    // 0x6f2a28: LoadField: r1 = r0->field_13
    //     0x6f2a28: ldur            w1, [x0, #0x13]
    // 0x6f2a2c: DecompressPointer r1
    //     0x6f2a2c: add             x1, x1, HEAP, lsl #32
    // 0x6f2a30: cmp             w1, NULL
    // 0x6f2a34: b.eq            #0x6f2b18
    // 0x6f2a38: r0 = iterator()
    //     0x6f2a38: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x6f2a3c: stur            x0, [fp, #-0x18]
    // 0x6f2a40: LoadField: r2 = r0->field_7
    //     0x6f2a40: ldur            w2, [x0, #7]
    // 0x6f2a44: DecompressPointer r2
    //     0x6f2a44: add             x2, x2, HEAP, lsl #32
    // 0x6f2a48: stur            x2, [fp, #-8]
    // 0x6f2a4c: ldur            x3, [fp, #-0x10]
    // 0x6f2a50: CheckStackOverflow
    //     0x6f2a50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f2a54: cmp             SP, x16
    //     0x6f2a58: b.ls            #0x6f2b1c
    // 0x6f2a5c: mov             x1, x0
    // 0x6f2a60: r0 = moveNext()
    //     0x6f2a60: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x6f2a64: tbnz            w0, #4, #0x6f2afc
    // 0x6f2a68: ldur            x3, [fp, #-0x18]
    // 0x6f2a6c: LoadField: r4 = r3->field_33
    //     0x6f2a6c: ldur            w4, [x3, #0x33]
    // 0x6f2a70: DecompressPointer r4
    //     0x6f2a70: add             x4, x4, HEAP, lsl #32
    // 0x6f2a74: stur            x4, [fp, #-0x20]
    // 0x6f2a78: cmp             w4, NULL
    // 0x6f2a7c: b.ne            #0x6f2ab0
    // 0x6f2a80: mov             x0, x4
    // 0x6f2a84: ldur            x2, [fp, #-8]
    // 0x6f2a88: r1 = Null
    //     0x6f2a88: mov             x1, NULL
    // 0x6f2a8c: cmp             w2, NULL
    // 0x6f2a90: b.eq            #0x6f2ab0
    // 0x6f2a94: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6f2a94: ldur            w4, [x2, #0x17]
    // 0x6f2a98: DecompressPointer r4
    //     0x6f2a98: add             x4, x4, HEAP, lsl #32
    // 0x6f2a9c: r8 = X0
    //     0x6f2a9c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6f2aa0: LoadField: r9 = r4->field_7
    //     0x6f2aa0: ldur            x9, [x4, #7]
    // 0x6f2aa4: r3 = Null
    //     0x6f2aa4: add             x3, PP, #0x57, lsl #12  ; [pp+0x575c0] Null
    //     0x6f2aa8: ldr             x3, [x3, #0x5c0]
    // 0x6f2aac: blr             x9
    // 0x6f2ab0: ldur            x2, [fp, #-0x10]
    // 0x6f2ab4: ldur            x0, [fp, #-0x20]
    // 0x6f2ab8: LoadField: r1 = r0->field_b
    //     0x6f2ab8: ldur            w1, [x0, #0xb]
    // 0x6f2abc: DecompressPointer r1
    //     0x6f2abc: add             x1, x1, HEAP, lsl #32
    // 0x6f2ac0: cmp             w2, w1
    // 0x6f2ac4: b.eq            #0x6f2af0
    // 0x6f2ac8: StoreField: r0->field_b = r2
    //     0x6f2ac8: stur            w2, [x0, #0xb]
    // 0x6f2acc: tbnz            w2, #4, #0x6f2adc
    // 0x6f2ad0: mov             x1, x0
    // 0x6f2ad4: r0 = unscheduleTick()
    //     0x6f2ad4: bl              #0x656684  ; [package:flutter/src/scheduler/ticker.dart] Ticker::unscheduleTick
    // 0x6f2ad8: b               #0x6f2af0
    // 0x6f2adc: mov             x1, x0
    // 0x6f2ae0: r0 = shouldScheduleTick()
    //     0x6f2ae0: bl              #0x655b90  ; [package:flutter/src/scheduler/ticker.dart] Ticker::shouldScheduleTick
    // 0x6f2ae4: tbnz            w0, #4, #0x6f2af0
    // 0x6f2ae8: ldur            x1, [fp, #-0x20]
    // 0x6f2aec: r0 = scheduleTick()
    //     0x6f2aec: bl              #0x655924  ; [package:flutter/src/scheduler/ticker.dart] Ticker::scheduleTick
    // 0x6f2af0: ldur            x0, [fp, #-0x18]
    // 0x6f2af4: ldur            x2, [fp, #-8]
    // 0x6f2af8: b               #0x6f2a4c
    // 0x6f2afc: r0 = Null
    //     0x6f2afc: mov             x0, NULL
    // 0x6f2b00: LeaveFrame
    //     0x6f2b00: mov             SP, fp
    //     0x6f2b04: ldp             fp, lr, [SP], #0x10
    // 0x6f2b08: ret
    //     0x6f2b08: ret             
    // 0x6f2b0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f2b0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f2b10: b               #0x6f29e0
    // 0x6f2b14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f2b14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6f2b18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f2b18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6f2b1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f2b1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f2b20: b               #0x6f2a5c
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7bd9c, size: 0x94
    // 0xa7bd9c: EnterFrame
    //     0xa7bd9c: stp             fp, lr, [SP, #-0x10]!
    //     0xa7bda0: mov             fp, SP
    // 0xa7bda4: AllocStack(0x10)
    //     0xa7bda4: sub             SP, SP, #0x10
    // 0xa7bda8: SetupParameters(__CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa7bda8: mov             x0, x1
    //     0xa7bdac: stur            x1, [fp, #-0x10]
    // 0xa7bdb0: CheckStackOverflow
    //     0xa7bdb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7bdb4: cmp             SP, x16
    //     0xa7bdb8: b.ls            #0xa7be28
    // 0xa7bdbc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa7bdbc: ldur            w3, [x0, #0x17]
    // 0xa7bdc0: DecompressPointer r3
    //     0xa7bdc0: add             x3, x3, HEAP, lsl #32
    // 0xa7bdc4: stur            x3, [fp, #-8]
    // 0xa7bdc8: cmp             w3, NULL
    // 0xa7bdcc: b.ne            #0xa7bdd8
    // 0xa7bdd0: mov             x1, x0
    // 0xa7bdd4: b               #0xa7be14
    // 0xa7bdd8: mov             x2, x0
    // 0xa7bddc: r1 = Function '_updateTickers@364311458':.
    //     0xa7bddc: add             x1, PP, #0x57, lsl #12  ; [pp+0x575d0] AnonymousClosure: (0x6f2988), in [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickers (0x6f29c0)
    //     0xa7bde0: ldr             x1, [x1, #0x5d0]
    // 0xa7bde4: r0 = AllocateClosure()
    //     0xa7bde4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7bde8: ldur            x1, [fp, #-8]
    // 0xa7bdec: r2 = LoadClassIdInstr(r1)
    //     0xa7bdec: ldur            x2, [x1, #-1]
    //     0xa7bdf0: ubfx            x2, x2, #0xc, #0x14
    // 0xa7bdf4: mov             x16, x0
    // 0xa7bdf8: mov             x0, x2
    // 0xa7bdfc: mov             x2, x16
    // 0xa7be00: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa7be00: movz            x17, #0xbf5c
    //     0xa7be04: add             lr, x0, x17
    //     0xa7be08: ldr             lr, [x21, lr, lsl #3]
    //     0xa7be0c: blr             lr
    // 0xa7be10: ldur            x1, [fp, #-0x10]
    // 0xa7be14: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa7be14: stur            NULL, [x1, #0x17]
    // 0xa7be18: r0 = Null
    //     0xa7be18: mov             x0, NULL
    // 0xa7be1c: LeaveFrame
    //     0xa7be1c: mov             SP, fp
    //     0xa7be20: ldp             fp, lr, [SP], #0x10
    // 0xa7be24: ret
    //     0xa7be24: ret             
    // 0xa7be28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7be28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7be2c: b               #0xa7bdbc
  }
  _ activate(/* No info */) {
    // ** addr: 0xa84c10, size: 0x48
    // 0xa84c10: EnterFrame
    //     0xa84c10: stp             fp, lr, [SP, #-0x10]!
    //     0xa84c14: mov             fp, SP
    // 0xa84c18: AllocStack(0x8)
    //     0xa84c18: sub             SP, SP, #8
    // 0xa84c1c: SetupParameters(__CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa84c1c: mov             x0, x1
    //     0xa84c20: stur            x1, [fp, #-8]
    // 0xa84c24: CheckStackOverflow
    //     0xa84c24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa84c28: cmp             SP, x16
    //     0xa84c2c: b.ls            #0xa84c50
    // 0xa84c30: mov             x1, x0
    // 0xa84c34: r0 = _updateTickerModeNotifier()
    //     0xa84c34: bl              #0x6f2864  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa84c38: ldur            x1, [fp, #-8]
    // 0xa84c3c: r0 = _updateTickers()
    //     0xa84c3c: bl              #0x6f29c0  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickers
    // 0xa84c40: r0 = Null
    //     0xa84c40: mov             x0, NULL
    // 0xa84c44: LeaveFrame
    //     0xa84c44: mov             SP, fp
    //     0xa84c48: ldp             fp, lr, [SP], #0x10
    // 0xa84c4c: ret
    //     0xa84c4c: ret             
    // 0xa84c50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa84c50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa84c54: b               #0xa84c30
  }
}

// class id: 4334, size: 0x30, field offset: 0x1c
class _CupertinoTextSelectionToolbarContentState extends __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin {

  late AnimationController _controller; // offset: 0x1c

  _ initState(/* No info */) {
    // ** addr: 0x931ff4, size: 0x88
    // 0x931ff4: EnterFrame
    //     0x931ff4: stp             fp, lr, [SP, #-0x10]!
    //     0x931ff8: mov             fp, SP
    // 0x931ffc: AllocStack(0x20)
    //     0x931ffc: sub             SP, SP, #0x20
    // 0x932000: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r2, fp-0x8 */)
    //     0x932000: mov             x2, x1
    //     0x932004: stur            x1, [fp, #-8]
    // 0x932008: CheckStackOverflow
    //     0x932008: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93200c: cmp             SP, x16
    //     0x932010: b.ls            #0x932074
    // 0x932014: r1 = <double>
    //     0x932014: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x932018: r0 = AnimationController()
    //     0x932018: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x93201c: stur            x0, [fp, #-0x10]
    // 0x932020: r16 = 1.000000
    //     0x932020: ldr             x16, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x932024: r30 = Instance_Duration
    //     0x932024: ldr             lr, [PP, #0x4e40]  ; [pp+0x4e40] Obj!Duration@e3a0c1
    // 0x932028: stp             lr, x16, [SP]
    // 0x93202c: mov             x1, x0
    // 0x932030: ldur            x2, [fp, #-8]
    // 0x932034: r4 = const [0, 0x4, 0x2, 0x2, duration, 0x3, value, 0x2, null]
    //     0x932034: add             x4, PP, #0x44, lsl #12  ; [pp+0x44060] List(9) [0, 0x4, 0x2, 0x2, "duration", 0x3, "value", 0x2, Null]
    //     0x932038: ldr             x4, [x4, #0x60]
    // 0x93203c: r0 = AnimationController()
    //     0x93203c: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x932040: ldur            x0, [fp, #-0x10]
    // 0x932044: ldur            x1, [fp, #-8]
    // 0x932048: StoreField: r1->field_1b = r0
    //     0x932048: stur            w0, [x1, #0x1b]
    //     0x93204c: ldurb           w16, [x1, #-1]
    //     0x932050: ldurb           w17, [x0, #-1]
    //     0x932054: and             x16, x17, x16, lsr #2
    //     0x932058: tst             x16, HEAP, lsr #32
    //     0x93205c: b.eq            #0x932064
    //     0x932060: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x932064: r0 = Null
    //     0x932064: mov             x0, NULL
    // 0x932068: LeaveFrame
    //     0x932068: mov             SP, fp
    //     0x93206c: ldp             fp, lr, [SP], #0x10
    // 0x932070: ret
    //     0x932070: ret             
    // 0x932074: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x932074: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x932078: b               #0x932014
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x983ce8, size: 0x144
    // 0x983ce8: EnterFrame
    //     0x983ce8: stp             fp, lr, [SP, #-0x10]!
    //     0x983cec: mov             fp, SP
    // 0x983cf0: AllocStack(0x10)
    //     0x983cf0: sub             SP, SP, #0x10
    // 0x983cf4: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x983cf4: mov             x4, x1
    //     0x983cf8: mov             x3, x2
    //     0x983cfc: stur            x1, [fp, #-8]
    //     0x983d00: stur            x2, [fp, #-0x10]
    // 0x983d04: CheckStackOverflow
    //     0x983d04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x983d08: cmp             SP, x16
    //     0x983d0c: b.ls            #0x983e14
    // 0x983d10: mov             x0, x3
    // 0x983d14: r2 = Null
    //     0x983d14: mov             x2, NULL
    // 0x983d18: r1 = Null
    //     0x983d18: mov             x1, NULL
    // 0x983d1c: r4 = 60
    //     0x983d1c: movz            x4, #0x3c
    // 0x983d20: branchIfSmi(r0, 0x983d2c)
    //     0x983d20: tbz             w0, #0, #0x983d2c
    // 0x983d24: r4 = LoadClassIdInstr(r0)
    //     0x983d24: ldur            x4, [x0, #-1]
    //     0x983d28: ubfx            x4, x4, #0xc, #0x14
    // 0x983d2c: r17 = 4882
    //     0x983d2c: movz            x17, #0x1312
    // 0x983d30: cmp             x4, x17
    // 0x983d34: b.eq            #0x983d4c
    // 0x983d38: r8 = _CupertinoTextSelectionToolbarContent
    //     0x983d38: add             x8, PP, #0x57, lsl #12  ; [pp+0x57598] Type: _CupertinoTextSelectionToolbarContent
    //     0x983d3c: ldr             x8, [x8, #0x598]
    // 0x983d40: r3 = Null
    //     0x983d40: add             x3, PP, #0x57, lsl #12  ; [pp+0x575a0] Null
    //     0x983d44: ldr             x3, [x3, #0x5a0]
    // 0x983d48: r0 = _CupertinoTextSelectionToolbarContent()
    //     0x983d48: bl              #0x6f2840  ; IsType__CupertinoTextSelectionToolbarContent_Stub
    // 0x983d4c: ldur            x3, [fp, #-8]
    // 0x983d50: LoadField: r2 = r3->field_7
    //     0x983d50: ldur            w2, [x3, #7]
    // 0x983d54: DecompressPointer r2
    //     0x983d54: add             x2, x2, HEAP, lsl #32
    // 0x983d58: ldur            x0, [fp, #-0x10]
    // 0x983d5c: r1 = Null
    //     0x983d5c: mov             x1, NULL
    // 0x983d60: cmp             w2, NULL
    // 0x983d64: b.eq            #0x983d88
    // 0x983d68: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x983d68: ldur            w4, [x2, #0x17]
    // 0x983d6c: DecompressPointer r4
    //     0x983d6c: add             x4, x4, HEAP, lsl #32
    // 0x983d70: r8 = X0 bound StatefulWidget
    //     0x983d70: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x983d74: ldr             x8, [x8, #0x7f8]
    // 0x983d78: LoadField: r9 = r4->field_7
    //     0x983d78: ldur            x9, [x4, #7]
    // 0x983d7c: r3 = Null
    //     0x983d7c: add             x3, PP, #0x57, lsl #12  ; [pp+0x575b0] Null
    //     0x983d80: ldr             x3, [x3, #0x5b0]
    // 0x983d84: blr             x9
    // 0x983d88: ldur            x2, [fp, #-8]
    // 0x983d8c: LoadField: r0 = r2->field_b
    //     0x983d8c: ldur            w0, [x2, #0xb]
    // 0x983d90: DecompressPointer r0
    //     0x983d90: add             x0, x0, HEAP, lsl #32
    // 0x983d94: cmp             w0, NULL
    // 0x983d98: b.eq            #0x983e1c
    // 0x983d9c: LoadField: r1 = r0->field_13
    //     0x983d9c: ldur            w1, [x0, #0x13]
    // 0x983da0: DecompressPointer r1
    //     0x983da0: add             x1, x1, HEAP, lsl #32
    // 0x983da4: ldur            x0, [fp, #-0x10]
    // 0x983da8: LoadField: r3 = r0->field_13
    //     0x983da8: ldur            w3, [x0, #0x13]
    // 0x983dac: DecompressPointer r3
    //     0x983dac: add             x3, x3, HEAP, lsl #32
    // 0x983db0: cmp             w1, w3
    // 0x983db4: b.eq            #0x983e04
    // 0x983db8: StoreField: r2->field_23 = rZR
    //     0x983db8: stur            xzr, [x2, #0x23]
    // 0x983dbc: StoreField: r2->field_1f = rNULL
    //     0x983dbc: stur            NULL, [x2, #0x1f]
    // 0x983dc0: LoadField: r1 = r2->field_1b
    //     0x983dc0: ldur            w1, [x2, #0x1b]
    // 0x983dc4: DecompressPointer r1
    //     0x983dc4: add             x1, x1, HEAP, lsl #32
    // 0x983dc8: r16 = Sentinel
    //     0x983dc8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x983dcc: cmp             w1, w16
    // 0x983dd0: b.eq            #0x983e20
    // 0x983dd4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x983dd4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x983dd8: r0 = forward()
    //     0x983dd8: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x983ddc: ldur            x2, [fp, #-8]
    // 0x983de0: LoadField: r0 = r2->field_1b
    //     0x983de0: ldur            w0, [x2, #0x1b]
    // 0x983de4: DecompressPointer r0
    //     0x983de4: add             x0, x0, HEAP, lsl #32
    // 0x983de8: stur            x0, [fp, #-0x10]
    // 0x983dec: r1 = Function '_statusListener@247408280':.
    //     0x983dec: add             x1, PP, #0x57, lsl #12  ; [pp+0x57568] AnonymousClosure: (0x983e2c), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_statusListener (0x983e68)
    //     0x983df0: ldr             x1, [x1, #0x568]
    // 0x983df4: r0 = AllocateClosure()
    //     0x983df4: bl              #0xec1630  ; AllocateClosureStub
    // 0x983df8: ldur            x1, [fp, #-0x10]
    // 0x983dfc: mov             x2, x0
    // 0x983e00: r0 = removeStatusListener()
    //     0x983e00: bl              #0xd35e3c  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::removeStatusListener
    // 0x983e04: r0 = Null
    //     0x983e04: mov             x0, NULL
    // 0x983e08: LeaveFrame
    //     0x983e08: mov             SP, fp
    //     0x983e0c: ldp             fp, lr, [SP], #0x10
    // 0x983e10: ret
    //     0x983e10: ret             
    // 0x983e14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x983e14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x983e18: b               #0x983d10
    // 0x983e1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x983e1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x983e20: r9 = _controller
    //     0x983e20: add             x9, PP, #0x57, lsl #12  ; [pp+0x57550] Field <_CupertinoTextSelectionToolbarContentState@247408280._controller@247408280>: late (offset: 0x1c)
    //     0x983e24: ldr             x9, [x9, #0x550]
    // 0x983e28: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x983e28: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void _statusListener(dynamic, AnimationStatus) {
    // ** addr: 0x983e2c, size: 0x3c
    // 0x983e2c: EnterFrame
    //     0x983e2c: stp             fp, lr, [SP, #-0x10]!
    //     0x983e30: mov             fp, SP
    // 0x983e34: ldr             x0, [fp, #0x18]
    // 0x983e38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x983e38: ldur            w1, [x0, #0x17]
    // 0x983e3c: DecompressPointer r1
    //     0x983e3c: add             x1, x1, HEAP, lsl #32
    // 0x983e40: CheckStackOverflow
    //     0x983e40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x983e44: cmp             SP, x16
    //     0x983e48: b.ls            #0x983e60
    // 0x983e4c: ldr             x2, [fp, #0x10]
    // 0x983e50: r0 = _statusListener()
    //     0x983e50: bl              #0x983e68  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_statusListener
    // 0x983e54: LeaveFrame
    //     0x983e54: mov             SP, fp
    //     0x983e58: ldp             fp, lr, [SP], #0x10
    // 0x983e5c: ret
    //     0x983e5c: ret             
    // 0x983e60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x983e60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x983e64: b               #0x983e4c
  }
  _ _statusListener(/* No info */) {
    // ** addr: 0x983e68, size: 0xdc
    // 0x983e68: EnterFrame
    //     0x983e68: stp             fp, lr, [SP, #-0x10]!
    //     0x983e6c: mov             fp, SP
    // 0x983e70: AllocStack(0x10)
    //     0x983e70: sub             SP, SP, #0x10
    // 0x983e74: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x983e74: stur            x1, [fp, #-8]
    //     0x983e78: stur            x2, [fp, #-0x10]
    // 0x983e7c: CheckStackOverflow
    //     0x983e7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x983e80: cmp             SP, x16
    //     0x983e84: b.ls            #0x983f30
    // 0x983e88: r1 = 1
    //     0x983e88: movz            x1, #0x1
    // 0x983e8c: r0 = AllocateContext()
    //     0x983e8c: bl              #0xec126c  ; AllocateContextStub
    // 0x983e90: mov             x1, x0
    // 0x983e94: ldur            x0, [fp, #-8]
    // 0x983e98: StoreField: r1->field_f = r0
    //     0x983e98: stur            w0, [x1, #0xf]
    // 0x983e9c: ldur            x2, [fp, #-0x10]
    // 0x983ea0: r16 = Instance_AnimationStatus
    //     0x983ea0: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0x983ea4: cmp             w2, w16
    // 0x983ea8: b.eq            #0x983ebc
    // 0x983eac: r0 = Null
    //     0x983eac: mov             x0, NULL
    // 0x983eb0: LeaveFrame
    //     0x983eb0: mov             SP, fp
    //     0x983eb4: ldp             fp, lr, [SP], #0x10
    // 0x983eb8: ret
    //     0x983eb8: ret             
    // 0x983ebc: mov             x2, x1
    // 0x983ec0: r1 = Function '<anonymous closure>':.
    //     0x983ec0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57578] AnonymousClosure: (0x983f44), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_statusListener (0x983e68)
    //     0x983ec4: ldr             x1, [x1, #0x578]
    // 0x983ec8: r0 = AllocateClosure()
    //     0x983ec8: bl              #0xec1630  ; AllocateClosureStub
    // 0x983ecc: ldur            x1, [fp, #-8]
    // 0x983ed0: mov             x2, x0
    // 0x983ed4: r0 = setState()
    //     0x983ed4: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x983ed8: ldur            x2, [fp, #-8]
    // 0x983edc: LoadField: r1 = r2->field_1b
    //     0x983edc: ldur            w1, [x2, #0x1b]
    // 0x983ee0: DecompressPointer r1
    //     0x983ee0: add             x1, x1, HEAP, lsl #32
    // 0x983ee4: r16 = Sentinel
    //     0x983ee4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x983ee8: cmp             w1, w16
    // 0x983eec: b.eq            #0x983f38
    // 0x983ef0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x983ef0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x983ef4: r0 = forward()
    //     0x983ef4: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x983ef8: ldur            x2, [fp, #-8]
    // 0x983efc: LoadField: r0 = r2->field_1b
    //     0x983efc: ldur            w0, [x2, #0x1b]
    // 0x983f00: DecompressPointer r0
    //     0x983f00: add             x0, x0, HEAP, lsl #32
    // 0x983f04: stur            x0, [fp, #-0x10]
    // 0x983f08: r1 = Function '_statusListener@247408280':.
    //     0x983f08: add             x1, PP, #0x57, lsl #12  ; [pp+0x57568] AnonymousClosure: (0x983e2c), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_statusListener (0x983e68)
    //     0x983f0c: ldr             x1, [x1, #0x568]
    // 0x983f10: r0 = AllocateClosure()
    //     0x983f10: bl              #0xec1630  ; AllocateClosureStub
    // 0x983f14: ldur            x1, [fp, #-0x10]
    // 0x983f18: mov             x2, x0
    // 0x983f1c: r0 = removeStatusListener()
    //     0x983f1c: bl              #0xd35e3c  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::removeStatusListener
    // 0x983f20: r0 = Null
    //     0x983f20: mov             x0, NULL
    // 0x983f24: LeaveFrame
    //     0x983f24: mov             SP, fp
    //     0x983f28: ldp             fp, lr, [SP], #0x10
    // 0x983f2c: ret
    //     0x983f2c: ret             
    // 0x983f30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x983f30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x983f34: b               #0x983e88
    // 0x983f38: r9 = _controller
    //     0x983f38: add             x9, PP, #0x57, lsl #12  ; [pp+0x57550] Field <_CupertinoTextSelectionToolbarContentState@247408280._controller@247408280>: late (offset: 0x1c)
    //     0x983f3c: ldr             x9, [x9, #0x550]
    // 0x983f40: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x983f40: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x983f44, size: 0x4c
    // 0x983f44: ldr             x1, [SP]
    // 0x983f48: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x983f48: ldur            w2, [x1, #0x17]
    // 0x983f4c: DecompressPointer r2
    //     0x983f4c: add             x2, x2, HEAP, lsl #32
    // 0x983f50: LoadField: r1 = r2->field_f
    //     0x983f50: ldur            w1, [x2, #0xf]
    // 0x983f54: DecompressPointer r1
    //     0x983f54: add             x1, x1, HEAP, lsl #32
    // 0x983f58: LoadField: r2 = r1->field_1f
    //     0x983f58: ldur            w2, [x1, #0x1f]
    // 0x983f5c: DecompressPointer r2
    //     0x983f5c: add             x2, x2, HEAP, lsl #32
    // 0x983f60: cmp             w2, NULL
    // 0x983f64: b.eq            #0x983f84
    // 0x983f68: r3 = LoadInt32Instr(r2)
    //     0x983f68: sbfx            x3, x2, #1, #0x1f
    //     0x983f6c: tbz             w2, #0, #0x983f74
    //     0x983f70: ldur            x3, [x2, #7]
    // 0x983f74: StoreField: r1->field_23 = r3
    //     0x983f74: stur            x3, [x1, #0x23]
    // 0x983f78: StoreField: r1->field_1f = rNULL
    //     0x983f78: stur            NULL, [x1, #0x1f]
    // 0x983f7c: r0 = Null
    //     0x983f7c: mov             x0, NULL
    // 0x983f80: ret
    //     0x983f80: ret             
    // 0x983f84: EnterFrame
    //     0x983f84: stp             fp, lr, [SP, #-0x10]!
    //     0x983f88: mov             fp, SP
    // 0x983f8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x983f8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0x9e2b2c, size: 0x3b0
    // 0x9e2b2c: EnterFrame
    //     0x9e2b2c: stp             fp, lr, [SP, #-0x10]!
    //     0x9e2b30: mov             fp, SP
    // 0x9e2b34: AllocStack(0x80)
    //     0x9e2b34: sub             SP, SP, #0x80
    // 0x9e2b38: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9e2b38: mov             x3, x1
    //     0x9e2b3c: mov             x0, x2
    //     0x9e2b40: stur            x1, [fp, #-8]
    //     0x9e2b44: stur            x2, [fp, #-0x10]
    // 0x9e2b48: CheckStackOverflow
    //     0x9e2b48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e2b4c: cmp             SP, x16
    //     0x9e2b50: b.ls            #0x9e2ec0
    // 0x9e2b54: mov             x2, x0
    // 0x9e2b58: r1 = Instance_CupertinoDynamicColor
    //     0x9e2b58: add             x1, PP, #0x50, lsl #12  ; [pp+0x50858] Obj!CupertinoDynamicColor@e1d8e1
    //     0x9e2b5c: ldr             x1, [x1, #0x858]
    // 0x9e2b60: r0 = resolveFrom()
    //     0x9e2b60: bl              #0x853be8  ; [package:flutter/src/cupertino/colors.dart] CupertinoDynamicColor::resolveFrom
    // 0x9e2b64: stur            x0, [fp, #-0x18]
    // 0x9e2b68: r0 = _LeftCupertinoChevronPainter()
    //     0x9e2b68: bl              #0x9e3124  ; Allocate_LeftCupertinoChevronPainterStub -> _LeftCupertinoChevronPainter (size=0x14)
    // 0x9e2b6c: mov             x1, x0
    // 0x9e2b70: ldur            x0, [fp, #-0x18]
    // 0x9e2b74: stur            x1, [fp, #-0x20]
    // 0x9e2b78: StoreField: r1->field_b = r0
    //     0x9e2b78: stur            w0, [x1, #0xb]
    // 0x9e2b7c: r2 = true
    //     0x9e2b7c: add             x2, NULL, #0x20  ; true
    // 0x9e2b80: StoreField: r1->field_f = r2
    //     0x9e2b80: stur            w2, [x1, #0xf]
    // 0x9e2b84: r0 = CustomPaint()
    //     0x9e2b84: bl              #0x9d4558  ; AllocateCustomPaintStub -> CustomPaint (size=0x24)
    // 0x9e2b88: mov             x1, x0
    // 0x9e2b8c: ldur            x0, [fp, #-0x20]
    // 0x9e2b90: stur            x1, [fp, #-0x28]
    // 0x9e2b94: StoreField: r1->field_f = r0
    //     0x9e2b94: stur            w0, [x1, #0xf]
    // 0x9e2b98: r0 = Instance_Size
    //     0x9e2b98: add             x0, PP, #0x57, lsl #12  ; [pp+0x57518] Obj!Size@e2c101
    //     0x9e2b9c: ldr             x0, [x0, #0x518]
    // 0x9e2ba0: ArrayStore: r1[0] = r0  ; List_4
    //     0x9e2ba0: stur            w0, [x1, #0x17]
    // 0x9e2ba4: r2 = false
    //     0x9e2ba4: add             x2, NULL, #0x30  ; false
    // 0x9e2ba8: StoreField: r1->field_1b = r2
    //     0x9e2ba8: stur            w2, [x1, #0x1b]
    // 0x9e2bac: StoreField: r1->field_1f = r2
    //     0x9e2bac: stur            w2, [x1, #0x1f]
    // 0x9e2bb0: r0 = IgnorePointer()
    //     0x9e2bb0: bl              #0x9e3118  ; AllocateIgnorePointerStub -> IgnorePointer (size=0x18)
    // 0x9e2bb4: mov             x3, x0
    // 0x9e2bb8: r0 = true
    //     0x9e2bb8: add             x0, NULL, #0x20  ; true
    // 0x9e2bbc: stur            x3, [fp, #-0x20]
    // 0x9e2bc0: StoreField: r3->field_f = r0
    //     0x9e2bc0: stur            w0, [x3, #0xf]
    // 0x9e2bc4: ldur            x1, [fp, #-0x28]
    // 0x9e2bc8: StoreField: r3->field_b = r1
    //     0x9e2bc8: stur            w1, [x3, #0xb]
    // 0x9e2bcc: ldur            x2, [fp, #-8]
    // 0x9e2bd0: r1 = Function '_handlePreviousPage@247408280':.
    //     0x9e2bd0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57520] AnonymousClosure: (0x9e3504), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_handlePreviousPage (0x9e3330)
    //     0x9e2bd4: ldr             x1, [x1, #0x520]
    // 0x9e2bd8: r0 = AllocateClosure()
    //     0x9e2bd8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9e2bdc: stur            x0, [fp, #-0x28]
    // 0x9e2be0: r0 = CupertinoTextSelectionToolbarButton()
    //     0x9e2be0: bl              #0x9e30e8  ; AllocateCupertinoTextSelectionToolbarButtonStub -> CupertinoTextSelectionToolbarButton (size=0x1c)
    // 0x9e2be4: mov             x1, x0
    // 0x9e2be8: ldur            x0, [fp, #-0x28]
    // 0x9e2bec: stur            x1, [fp, #-0x30]
    // 0x9e2bf0: StoreField: r1->field_f = r0
    //     0x9e2bf0: stur            w0, [x1, #0xf]
    // 0x9e2bf4: ldur            x0, [fp, #-0x20]
    // 0x9e2bf8: StoreField: r1->field_b = r0
    //     0x9e2bf8: stur            w0, [x1, #0xb]
    // 0x9e2bfc: r0 = Center()
    //     0x9e2bfc: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x9e2c00: mov             x1, x0
    // 0x9e2c04: r0 = Instance_Alignment
    //     0x9e2c04: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x9e2c08: ldr             x0, [x0, #0x898]
    // 0x9e2c0c: stur            x1, [fp, #-0x20]
    // 0x9e2c10: StoreField: r1->field_f = r0
    //     0x9e2c10: stur            w0, [x1, #0xf]
    // 0x9e2c14: r2 = 1.000000
    //     0x9e2c14: ldr             x2, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x9e2c18: StoreField: r1->field_13 = r2
    //     0x9e2c18: stur            w2, [x1, #0x13]
    // 0x9e2c1c: ArrayStore: r1[0] = r2  ; List_4
    //     0x9e2c1c: stur            w2, [x1, #0x17]
    // 0x9e2c20: ldur            x3, [fp, #-0x30]
    // 0x9e2c24: StoreField: r1->field_b = r3
    //     0x9e2c24: stur            w3, [x1, #0xb]
    // 0x9e2c28: r0 = _RightCupertinoChevronPainter()
    //     0x9e2c28: bl              #0x9e30dc  ; Allocate_RightCupertinoChevronPainterStub -> _RightCupertinoChevronPainter (size=0x14)
    // 0x9e2c2c: mov             x1, x0
    // 0x9e2c30: ldur            x0, [fp, #-0x18]
    // 0x9e2c34: stur            x1, [fp, #-0x28]
    // 0x9e2c38: StoreField: r1->field_b = r0
    //     0x9e2c38: stur            w0, [x1, #0xb]
    // 0x9e2c3c: r0 = false
    //     0x9e2c3c: add             x0, NULL, #0x30  ; false
    // 0x9e2c40: StoreField: r1->field_f = r0
    //     0x9e2c40: stur            w0, [x1, #0xf]
    // 0x9e2c44: r0 = CustomPaint()
    //     0x9e2c44: bl              #0x9d4558  ; AllocateCustomPaintStub -> CustomPaint (size=0x24)
    // 0x9e2c48: mov             x1, x0
    // 0x9e2c4c: ldur            x0, [fp, #-0x28]
    // 0x9e2c50: stur            x1, [fp, #-0x18]
    // 0x9e2c54: StoreField: r1->field_f = r0
    //     0x9e2c54: stur            w0, [x1, #0xf]
    // 0x9e2c58: r0 = Instance_Size
    //     0x9e2c58: add             x0, PP, #0x57, lsl #12  ; [pp+0x57518] Obj!Size@e2c101
    //     0x9e2c5c: ldr             x0, [x0, #0x518]
    // 0x9e2c60: ArrayStore: r1[0] = r0  ; List_4
    //     0x9e2c60: stur            w0, [x1, #0x17]
    // 0x9e2c64: r0 = false
    //     0x9e2c64: add             x0, NULL, #0x30  ; false
    // 0x9e2c68: StoreField: r1->field_1b = r0
    //     0x9e2c68: stur            w0, [x1, #0x1b]
    // 0x9e2c6c: StoreField: r1->field_1f = r0
    //     0x9e2c6c: stur            w0, [x1, #0x1f]
    // 0x9e2c70: r0 = IgnorePointer()
    //     0x9e2c70: bl              #0x9e3118  ; AllocateIgnorePointerStub -> IgnorePointer (size=0x18)
    // 0x9e2c74: mov             x3, x0
    // 0x9e2c78: r0 = true
    //     0x9e2c78: add             x0, NULL, #0x20  ; true
    // 0x9e2c7c: stur            x3, [fp, #-0x28]
    // 0x9e2c80: StoreField: r3->field_f = r0
    //     0x9e2c80: stur            w0, [x3, #0xf]
    // 0x9e2c84: ldur            x0, [fp, #-0x18]
    // 0x9e2c88: StoreField: r3->field_b = r0
    //     0x9e2c88: stur            w0, [x3, #0xb]
    // 0x9e2c8c: ldur            x2, [fp, #-8]
    // 0x9e2c90: r1 = Function '_handleNextPage@247408280':.
    //     0x9e2c90: add             x1, PP, #0x57, lsl #12  ; [pp+0x57528] AnonymousClosure: (0x9e34cc), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_handleNextPage (0x9e31cc)
    //     0x9e2c94: ldr             x1, [x1, #0x528]
    // 0x9e2c98: r0 = AllocateClosure()
    //     0x9e2c98: bl              #0xec1630  ; AllocateClosureStub
    // 0x9e2c9c: stur            x0, [fp, #-0x18]
    // 0x9e2ca0: r0 = CupertinoTextSelectionToolbarButton()
    //     0x9e2ca0: bl              #0x9e30e8  ; AllocateCupertinoTextSelectionToolbarButtonStub -> CupertinoTextSelectionToolbarButton (size=0x1c)
    // 0x9e2ca4: mov             x1, x0
    // 0x9e2ca8: ldur            x0, [fp, #-0x18]
    // 0x9e2cac: stur            x1, [fp, #-0x30]
    // 0x9e2cb0: StoreField: r1->field_f = r0
    //     0x9e2cb0: stur            w0, [x1, #0xf]
    // 0x9e2cb4: ldur            x0, [fp, #-0x28]
    // 0x9e2cb8: StoreField: r1->field_b = r0
    //     0x9e2cb8: stur            w0, [x1, #0xb]
    // 0x9e2cbc: r0 = Center()
    //     0x9e2cbc: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x9e2cc0: mov             x3, x0
    // 0x9e2cc4: r0 = Instance_Alignment
    //     0x9e2cc4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x9e2cc8: ldr             x0, [x0, #0x898]
    // 0x9e2ccc: stur            x3, [fp, #-0x28]
    // 0x9e2cd0: StoreField: r3->field_f = r0
    //     0x9e2cd0: stur            w0, [x3, #0xf]
    // 0x9e2cd4: r1 = 1.000000
    //     0x9e2cd4: ldr             x1, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x9e2cd8: StoreField: r3->field_13 = r1
    //     0x9e2cd8: stur            w1, [x3, #0x13]
    // 0x9e2cdc: ArrayStore: r3[0] = r1  ; List_4
    //     0x9e2cdc: stur            w1, [x3, #0x17]
    // 0x9e2ce0: ldur            x1, [fp, #-0x30]
    // 0x9e2ce4: StoreField: r3->field_b = r1
    //     0x9e2ce4: stur            w1, [x3, #0xb]
    // 0x9e2ce8: ldur            x4, [fp, #-8]
    // 0x9e2cec: LoadField: r1 = r4->field_b
    //     0x9e2cec: ldur            w1, [x4, #0xb]
    // 0x9e2cf0: DecompressPointer r1
    //     0x9e2cf0: add             x1, x1, HEAP, lsl #32
    // 0x9e2cf4: cmp             w1, NULL
    // 0x9e2cf8: b.eq            #0x9e2ec8
    // 0x9e2cfc: LoadField: r5 = r1->field_13
    //     0x9e2cfc: ldur            w5, [x1, #0x13]
    // 0x9e2d00: DecompressPointer r5
    //     0x9e2d00: add             x5, x5, HEAP, lsl #32
    // 0x9e2d04: stur            x5, [fp, #-0x18]
    // 0x9e2d08: r1 = Function '<anonymous closure>':.
    //     0x9e2d08: add             x1, PP, #0x57, lsl #12  ; [pp+0x57530] AnonymousClosure: (0x9e3494), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::build (0x9e2b2c)
    //     0x9e2d0c: ldr             x1, [x1, #0x530]
    // 0x9e2d10: r2 = Null
    //     0x9e2d10: mov             x2, NULL
    // 0x9e2d14: r0 = AllocateClosure()
    //     0x9e2d14: bl              #0xec1630  ; AllocateClosureStub
    // 0x9e2d18: r16 = <Center>
    //     0x9e2d18: add             x16, PP, #0x57, lsl #12  ; [pp+0x57538] TypeArguments: <Center>
    //     0x9e2d1c: ldr             x16, [x16, #0x538]
    // 0x9e2d20: ldur            lr, [fp, #-0x18]
    // 0x9e2d24: stp             lr, x16, [SP, #8]
    // 0x9e2d28: str             x0, [SP]
    // 0x9e2d2c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9e2d2c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9e2d30: r0 = map()
    //     0x9e2d30: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x9e2d34: LoadField: r1 = r0->field_7
    //     0x9e2d34: ldur            w1, [x0, #7]
    // 0x9e2d38: DecompressPointer r1
    //     0x9e2d38: add             x1, x1, HEAP, lsl #32
    // 0x9e2d3c: mov             x2, x0
    // 0x9e2d40: r0 = _GrowableList.of()
    //     0x9e2d40: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x9e2d44: mov             x3, x0
    // 0x9e2d48: ldur            x0, [fp, #-8]
    // 0x9e2d4c: stur            x3, [fp, #-0x50]
    // 0x9e2d50: LoadField: r1 = r0->field_b
    //     0x9e2d50: ldur            w1, [x0, #0xb]
    // 0x9e2d54: DecompressPointer r1
    //     0x9e2d54: add             x1, x1, HEAP, lsl #32
    // 0x9e2d58: cmp             w1, NULL
    // 0x9e2d5c: b.eq            #0x9e2ecc
    // 0x9e2d60: LoadField: r4 = r1->field_b
    //     0x9e2d60: ldur            w4, [x1, #0xb]
    // 0x9e2d64: DecompressPointer r4
    //     0x9e2d64: add             x4, x4, HEAP, lsl #32
    // 0x9e2d68: stur            x4, [fp, #-0x48]
    // 0x9e2d6c: LoadField: r5 = r1->field_f
    //     0x9e2d6c: ldur            w5, [x1, #0xf]
    // 0x9e2d70: DecompressPointer r5
    //     0x9e2d70: add             x5, x5, HEAP, lsl #32
    // 0x9e2d74: stur            x5, [fp, #-0x40]
    // 0x9e2d78: LoadField: r6 = r0->field_1b
    //     0x9e2d78: ldur            w6, [x0, #0x1b]
    // 0x9e2d7c: DecompressPointer r6
    //     0x9e2d7c: add             x6, x6, HEAP, lsl #32
    // 0x9e2d80: r16 = Sentinel
    //     0x9e2d80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e2d84: cmp             w6, w16
    // 0x9e2d88: b.eq            #0x9e2ed0
    // 0x9e2d8c: stur            x6, [fp, #-0x30]
    // 0x9e2d90: LoadField: r7 = r0->field_2b
    //     0x9e2d90: ldur            w7, [x0, #0x2b]
    // 0x9e2d94: DecompressPointer r7
    //     0x9e2d94: add             x7, x7, HEAP, lsl #32
    // 0x9e2d98: stur            x7, [fp, #-0x18]
    // 0x9e2d9c: LoadField: r8 = r0->field_23
    //     0x9e2d9c: ldur            x8, [x0, #0x23]
    // 0x9e2da0: ldur            x2, [fp, #-0x10]
    // 0x9e2da4: stur            x8, [fp, #-0x38]
    // 0x9e2da8: r1 = Instance_CupertinoDynamicColor
    //     0x9e2da8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57540] Obj!CupertinoDynamicColor@e1dae1
    //     0x9e2dac: ldr             x1, [x1, #0x540]
    // 0x9e2db0: r0 = resolveFrom()
    //     0x9e2db0: bl              #0x853be8  ; [package:flutter/src/cupertino/colors.dart] CupertinoDynamicColor::resolveFrom
    // 0x9e2db4: ldur            x1, [fp, #-0x10]
    // 0x9e2db8: stur            x0, [fp, #-0x58]
    // 0x9e2dbc: r0 = devicePixelRatioOf()
    //     0x9e2dbc: bl              #0x85fc3c  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::devicePixelRatioOf
    // 0x9e2dc0: mov             v1.16b, v0.16b
    // 0x9e2dc4: d0 = 1.000000
    //     0x9e2dc4: fmov            d0, #1.00000000
    // 0x9e2dc8: fdiv            d2, d0, d1
    // 0x9e2dcc: stur            d2, [fp, #-0x68]
    // 0x9e2dd0: r0 = _CupertinoTextSelectionToolbarItems()
    //     0x9e2dd0: bl              #0x9e30d0  ; Allocate_CupertinoTextSelectionToolbarItemsStub -> _CupertinoTextSelectionToolbarItems (size=0x2c)
    // 0x9e2dd4: mov             x1, x0
    // 0x9e2dd8: ldur            x0, [fp, #-0x38]
    // 0x9e2ddc: stur            x1, [fp, #-0x60]
    // 0x9e2de0: StoreField: r1->field_23 = r0
    //     0x9e2de0: stur            x0, [x1, #0x23]
    // 0x9e2de4: ldur            x0, [fp, #-0x50]
    // 0x9e2de8: StoreField: r1->field_f = r0
    //     0x9e2de8: stur            w0, [x1, #0xf]
    // 0x9e2dec: ldur            x0, [fp, #-0x20]
    // 0x9e2df0: StoreField: r1->field_b = r0
    //     0x9e2df0: stur            w0, [x1, #0xb]
    // 0x9e2df4: ldur            x0, [fp, #-0x58]
    // 0x9e2df8: StoreField: r1->field_13 = r0
    //     0x9e2df8: stur            w0, [x1, #0x13]
    // 0x9e2dfc: ldur            d0, [fp, #-0x68]
    // 0x9e2e00: ArrayStore: r1[0] = d0  ; List_8
    //     0x9e2e00: stur            d0, [x1, #0x17]
    // 0x9e2e04: ldur            x0, [fp, #-0x28]
    // 0x9e2e08: StoreField: r1->field_1f = r0
    //     0x9e2e08: stur            w0, [x1, #0x1f]
    // 0x9e2e0c: ldur            x0, [fp, #-0x18]
    // 0x9e2e10: StoreField: r1->field_7 = r0
    //     0x9e2e10: stur            w0, [x1, #7]
    // 0x9e2e14: r0 = GestureDetector()
    //     0x9e2e14: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x9e2e18: ldur            x2, [fp, #-8]
    // 0x9e2e1c: r1 = Function '_onHorizontalDragEnd@247408280':.
    //     0x9e2e1c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57548] AnonymousClosure: (0x9e3130), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_onHorizontalDragEnd (0x9e316c)
    //     0x9e2e20: ldr             x1, [x1, #0x548]
    // 0x9e2e24: stur            x0, [fp, #-8]
    // 0x9e2e28: r0 = AllocateClosure()
    //     0x9e2e28: bl              #0xec1630  ; AllocateClosureStub
    // 0x9e2e2c: ldur            x16, [fp, #-0x60]
    // 0x9e2e30: stp             x16, x0, [SP]
    // 0x9e2e34: ldur            x1, [fp, #-8]
    // 0x9e2e38: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onHorizontalDragEnd, 0x1, null]
    //     0x9e2e38: add             x4, PP, #0x34, lsl #12  ; [pp+0x34da8] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onHorizontalDragEnd", 0x1, Null]
    //     0x9e2e3c: ldr             x4, [x4, #0xda8]
    // 0x9e2e40: r0 = GestureDetector()
    //     0x9e2e40: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x9e2e44: r0 = AnimatedSize()
    //     0x9e2e44: bl              #0x9e30c4  ; AllocateAnimatedSizeStub -> AnimatedSize (size=0x28)
    // 0x9e2e48: mov             x1, x0
    // 0x9e2e4c: ldur            x0, [fp, #-8]
    // 0x9e2e50: stur            x1, [fp, #-0x18]
    // 0x9e2e54: StoreField: r1->field_b = r0
    //     0x9e2e54: stur            w0, [x1, #0xb]
    // 0x9e2e58: r0 = Instance_Alignment
    //     0x9e2e58: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x9e2e5c: ldr             x0, [x0, #0x898]
    // 0x9e2e60: StoreField: r1->field_f = r0
    //     0x9e2e60: stur            w0, [x1, #0xf]
    // 0x9e2e64: r0 = Instance__DecelerateCurve
    //     0x9e2e64: ldr             x0, [PP, #0x4e48]  ; [pp+0x4e48] Obj!_DecelerateCurve@e14cc1
    // 0x9e2e68: StoreField: r1->field_13 = r0
    //     0x9e2e68: stur            w0, [x1, #0x13]
    // 0x9e2e6c: r0 = Instance_Duration
    //     0x9e2e6c: ldr             x0, [PP, #0x4e40]  ; [pp+0x4e40] Obj!Duration@e3a0c1
    // 0x9e2e70: ArrayStore: r1[0] = r0  ; List_4
    //     0x9e2e70: stur            w0, [x1, #0x17]
    // 0x9e2e74: r0 = Instance_Clip
    //     0x9e2e74: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0x9e2e78: ldr             x0, [x0, #0x7c0]
    // 0x9e2e7c: StoreField: r1->field_1f = r0
    //     0x9e2e7c: stur            w0, [x1, #0x1f]
    // 0x9e2e80: r0 = FadeTransition()
    //     0x9e2e80: bl              #0x91a518  ; AllocateFadeTransitionStub -> FadeTransition (size=0x18)
    // 0x9e2e84: mov             x1, x0
    // 0x9e2e88: ldur            x0, [fp, #-0x30]
    // 0x9e2e8c: StoreField: r1->field_f = r0
    //     0x9e2e8c: stur            w0, [x1, #0xf]
    // 0x9e2e90: r0 = false
    //     0x9e2e90: add             x0, NULL, #0x30  ; false
    // 0x9e2e94: StoreField: r1->field_13 = r0
    //     0x9e2e94: stur            w0, [x1, #0x13]
    // 0x9e2e98: ldur            x0, [fp, #-0x18]
    // 0x9e2e9c: StoreField: r1->field_b = r0
    //     0x9e2e9c: stur            w0, [x1, #0xb]
    // 0x9e2ea0: mov             x5, x1
    // 0x9e2ea4: ldur            x1, [fp, #-0x10]
    // 0x9e2ea8: ldur            x2, [fp, #-0x48]
    // 0x9e2eac: ldur            x3, [fp, #-0x40]
    // 0x9e2eb0: r0 = _defaultToolbarBuilder()
    //     0x9e2eb0: bl              #0x9e2f18  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] CupertinoTextSelectionToolbar::_defaultToolbarBuilder
    // 0x9e2eb4: LeaveFrame
    //     0x9e2eb4: mov             SP, fp
    //     0x9e2eb8: ldp             fp, lr, [SP], #0x10
    // 0x9e2ebc: ret
    //     0x9e2ebc: ret             
    // 0x9e2ec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e2ec0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e2ec4: b               #0x9e2b54
    // 0x9e2ec8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e2ec8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e2ecc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e2ecc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e2ed0: r9 = _controller
    //     0x9e2ed0: add             x9, PP, #0x57, lsl #12  ; [pp+0x57550] Field <_CupertinoTextSelectionToolbarContentState@247408280._controller@247408280>: late (offset: 0x1c)
    //     0x9e2ed4: ldr             x9, [x9, #0x550]
    // 0x9e2ed8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9e2ed8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void _onHorizontalDragEnd(dynamic, DragEndDetails) {
    // ** addr: 0x9e3130, size: 0x3c
    // 0x9e3130: EnterFrame
    //     0x9e3130: stp             fp, lr, [SP, #-0x10]!
    //     0x9e3134: mov             fp, SP
    // 0x9e3138: ldr             x0, [fp, #0x18]
    // 0x9e313c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9e313c: ldur            w1, [x0, #0x17]
    // 0x9e3140: DecompressPointer r1
    //     0x9e3140: add             x1, x1, HEAP, lsl #32
    // 0x9e3144: CheckStackOverflow
    //     0x9e3144: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e3148: cmp             SP, x16
    //     0x9e314c: b.ls            #0x9e3164
    // 0x9e3150: ldr             x2, [fp, #0x10]
    // 0x9e3154: r0 = _onHorizontalDragEnd()
    //     0x9e3154: bl              #0x9e316c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_onHorizontalDragEnd
    // 0x9e3158: LeaveFrame
    //     0x9e3158: mov             SP, fp
    //     0x9e315c: ldp             fp, lr, [SP], #0x10
    // 0x9e3160: ret
    //     0x9e3160: ret             
    // 0x9e3164: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e3164: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e3168: b               #0x9e3150
  }
  _ _onHorizontalDragEnd(/* No info */) {
    // ** addr: 0x9e316c, size: 0x60
    // 0x9e316c: EnterFrame
    //     0x9e316c: stp             fp, lr, [SP, #-0x10]!
    //     0x9e3170: mov             fp, SP
    // 0x9e3174: CheckStackOverflow
    //     0x9e3174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e3178: cmp             SP, x16
    //     0x9e317c: b.ls            #0x9e31c4
    // 0x9e3180: LoadField: r0 = r2->field_b
    //     0x9e3180: ldur            w0, [x2, #0xb]
    // 0x9e3184: DecompressPointer r0
    //     0x9e3184: add             x0, x0, HEAP, lsl #32
    // 0x9e3188: cmp             w0, NULL
    // 0x9e318c: b.eq            #0x9e31b4
    // 0x9e3190: d0 = 0.000000
    //     0x9e3190: eor             v0.16b, v0.16b, v0.16b
    // 0x9e3194: LoadField: d1 = r0->field_7
    //     0x9e3194: ldur            d1, [x0, #7]
    // 0x9e3198: fcmp            d1, d0
    // 0x9e319c: b.eq            #0x9e31b4
    // 0x9e31a0: fcmp            d1, d0
    // 0x9e31a4: b.le            #0x9e31b0
    // 0x9e31a8: r0 = _handlePreviousPage()
    //     0x9e31a8: bl              #0x9e3330  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_handlePreviousPage
    // 0x9e31ac: b               #0x9e31b4
    // 0x9e31b0: r0 = _handleNextPage()
    //     0x9e31b0: bl              #0x9e31cc  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_handleNextPage
    // 0x9e31b4: r0 = Null
    //     0x9e31b4: mov             x0, NULL
    // 0x9e31b8: LeaveFrame
    //     0x9e31b8: mov             SP, fp
    //     0x9e31bc: ldp             fp, lr, [SP], #0x10
    // 0x9e31c0: ret
    //     0x9e31c0: ret             
    // 0x9e31c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e31c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e31c8: b               #0x9e3180
  }
  _ _handleNextPage(/* No info */) {
    // ** addr: 0x9e31cc, size: 0x164
    // 0x9e31cc: EnterFrame
    //     0x9e31cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9e31d0: mov             fp, SP
    // 0x9e31d4: AllocStack(0x10)
    //     0x9e31d4: sub             SP, SP, #0x10
    // 0x9e31d8: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r2, fp-0x8 */)
    //     0x9e31d8: mov             x2, x1
    //     0x9e31dc: stur            x1, [fp, #-8]
    // 0x9e31e0: CheckStackOverflow
    //     0x9e31e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e31e4: cmp             SP, x16
    //     0x9e31e8: b.ls            #0x9e3310
    // 0x9e31ec: LoadField: r1 = r2->field_2b
    //     0x9e31ec: ldur            w1, [x2, #0x2b]
    // 0x9e31f0: DecompressPointer r1
    //     0x9e31f0: add             x1, x1, HEAP, lsl #32
    // 0x9e31f4: r0 = _currentElement()
    //     0x9e31f4: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x9e31f8: cmp             w0, NULL
    // 0x9e31fc: b.ne            #0x9e3208
    // 0x9e3200: r3 = Null
    //     0x9e3200: mov             x3, NULL
    // 0x9e3204: b               #0x9e3214
    // 0x9e3208: mov             x1, x0
    // 0x9e320c: r0 = findRenderObject()
    //     0x9e320c: bl              #0x6852d8  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0x9e3210: mov             x3, x0
    // 0x9e3214: mov             x0, x3
    // 0x9e3218: stur            x3, [fp, #-0x10]
    // 0x9e321c: r2 = Null
    //     0x9e321c: mov             x2, NULL
    // 0x9e3220: r1 = Null
    //     0x9e3220: mov             x1, NULL
    // 0x9e3224: r4 = LoadClassIdInstr(r0)
    //     0x9e3224: ldur            x4, [x0, #-1]
    //     0x9e3228: ubfx            x4, x4, #0xc, #0x14
    // 0x9e322c: sub             x4, x4, #0xbba
    // 0x9e3230: cmp             x4, #0x9a
    // 0x9e3234: b.ls            #0x9e3248
    // 0x9e3238: r8 = RenderBox?
    //     0x9e3238: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x9e323c: r3 = Null
    //     0x9e323c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57558] Null
    //     0x9e3240: ldr             x3, [x3, #0x558]
    // 0x9e3244: r0 = RenderBox?()
    //     0x9e3244: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x9e3248: ldur            x0, [fp, #-0x10]
    // 0x9e324c: r1 = LoadClassIdInstr(r0)
    //     0x9e324c: ldur            x1, [x0, #-1]
    //     0x9e3250: ubfx            x1, x1, #0xc, #0x14
    // 0x9e3254: cmp             x1, #0xbfc
    // 0x9e3258: b.ne            #0x9e3300
    // 0x9e325c: LoadField: r1 = r0->field_6b
    //     0x9e325c: ldur            w1, [x0, #0x6b]
    // 0x9e3260: DecompressPointer r1
    //     0x9e3260: add             x1, x1, HEAP, lsl #32
    // 0x9e3264: r16 = Sentinel
    //     0x9e3264: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e3268: cmp             w1, w16
    // 0x9e326c: b.eq            #0x9e3318
    // 0x9e3270: tbnz            w1, #4, #0x9e3300
    // 0x9e3274: ldur            x2, [fp, #-8]
    // 0x9e3278: LoadField: r1 = r2->field_1b
    //     0x9e3278: ldur            w1, [x2, #0x1b]
    // 0x9e327c: DecompressPointer r1
    //     0x9e327c: add             x1, x1, HEAP, lsl #32
    // 0x9e3280: r16 = Sentinel
    //     0x9e3280: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e3284: cmp             w1, w16
    // 0x9e3288: b.eq            #0x9e3324
    // 0x9e328c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9e328c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9e3290: r0 = reverse()
    //     0x9e3290: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0x9e3294: ldur            x0, [fp, #-8]
    // 0x9e3298: LoadField: r3 = r0->field_1b
    //     0x9e3298: ldur            w3, [x0, #0x1b]
    // 0x9e329c: DecompressPointer r3
    //     0x9e329c: add             x3, x3, HEAP, lsl #32
    // 0x9e32a0: mov             x2, x0
    // 0x9e32a4: stur            x3, [fp, #-0x10]
    // 0x9e32a8: r1 = Function '_statusListener@247408280':.
    //     0x9e32a8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57568] AnonymousClosure: (0x983e2c), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_statusListener (0x983e68)
    //     0x9e32ac: ldr             x1, [x1, #0x568]
    // 0x9e32b0: r0 = AllocateClosure()
    //     0x9e32b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x9e32b4: ldur            x1, [fp, #-0x10]
    // 0x9e32b8: mov             x2, x0
    // 0x9e32bc: r0 = addStatusListener()
    //     0x9e32bc: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0x9e32c0: ldur            x2, [fp, #-8]
    // 0x9e32c4: LoadField: r3 = r2->field_23
    //     0x9e32c4: ldur            x3, [x2, #0x23]
    // 0x9e32c8: add             x4, x3, #1
    // 0x9e32cc: r0 = BoxInt64Instr(r4)
    //     0x9e32cc: sbfiz           x0, x4, #1, #0x1f
    //     0x9e32d0: cmp             x4, x0, asr #1
    //     0x9e32d4: b.eq            #0x9e32e0
    //     0x9e32d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9e32dc: stur            x4, [x0, #7]
    // 0x9e32e0: StoreField: r2->field_1f = r0
    //     0x9e32e0: stur            w0, [x2, #0x1f]
    //     0x9e32e4: tbz             w0, #0, #0x9e3300
    //     0x9e32e8: ldurb           w16, [x2, #-1]
    //     0x9e32ec: ldurb           w17, [x0, #-1]
    //     0x9e32f0: and             x16, x17, x16, lsr #2
    //     0x9e32f4: tst             x16, HEAP, lsr #32
    //     0x9e32f8: b.eq            #0x9e3300
    //     0x9e32fc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9e3300: r0 = Null
    //     0x9e3300: mov             x0, NULL
    // 0x9e3304: LeaveFrame
    //     0x9e3304: mov             SP, fp
    //     0x9e3308: ldp             fp, lr, [SP], #0x10
    // 0x9e330c: ret
    //     0x9e330c: ret             
    // 0x9e3310: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e3310: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e3314: b               #0x9e31ec
    // 0x9e3318: r9 = hasNextPage
    //     0x9e3318: add             x9, PP, #0x57, lsl #12  ; [pp+0x57570] Field <<EMAIL>>: late (offset: 0x6c)
    //     0x9e331c: ldr             x9, [x9, #0x570]
    // 0x9e3320: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9e3320: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9e3324: r9 = _controller
    //     0x9e3324: add             x9, PP, #0x57, lsl #12  ; [pp+0x57550] Field <_CupertinoTextSelectionToolbarContentState@247408280._controller@247408280>: late (offset: 0x1c)
    //     0x9e3328: ldr             x9, [x9, #0x550]
    // 0x9e332c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9e332c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _handlePreviousPage(/* No info */) {
    // ** addr: 0x9e3330, size: 0x164
    // 0x9e3330: EnterFrame
    //     0x9e3330: stp             fp, lr, [SP, #-0x10]!
    //     0x9e3334: mov             fp, SP
    // 0x9e3338: AllocStack(0x10)
    //     0x9e3338: sub             SP, SP, #0x10
    // 0x9e333c: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r2, fp-0x8 */)
    //     0x9e333c: mov             x2, x1
    //     0x9e3340: stur            x1, [fp, #-8]
    // 0x9e3344: CheckStackOverflow
    //     0x9e3344: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e3348: cmp             SP, x16
    //     0x9e334c: b.ls            #0x9e3474
    // 0x9e3350: LoadField: r1 = r2->field_2b
    //     0x9e3350: ldur            w1, [x2, #0x2b]
    // 0x9e3354: DecompressPointer r1
    //     0x9e3354: add             x1, x1, HEAP, lsl #32
    // 0x9e3358: r0 = _currentElement()
    //     0x9e3358: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x9e335c: cmp             w0, NULL
    // 0x9e3360: b.ne            #0x9e336c
    // 0x9e3364: r3 = Null
    //     0x9e3364: mov             x3, NULL
    // 0x9e3368: b               #0x9e3378
    // 0x9e336c: mov             x1, x0
    // 0x9e3370: r0 = findRenderObject()
    //     0x9e3370: bl              #0x6852d8  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0x9e3374: mov             x3, x0
    // 0x9e3378: mov             x0, x3
    // 0x9e337c: stur            x3, [fp, #-0x10]
    // 0x9e3380: r2 = Null
    //     0x9e3380: mov             x2, NULL
    // 0x9e3384: r1 = Null
    //     0x9e3384: mov             x1, NULL
    // 0x9e3388: r4 = LoadClassIdInstr(r0)
    //     0x9e3388: ldur            x4, [x0, #-1]
    //     0x9e338c: ubfx            x4, x4, #0xc, #0x14
    // 0x9e3390: sub             x4, x4, #0xbba
    // 0x9e3394: cmp             x4, #0x9a
    // 0x9e3398: b.ls            #0x9e33ac
    // 0x9e339c: r8 = RenderBox?
    //     0x9e339c: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x9e33a0: r3 = Null
    //     0x9e33a0: add             x3, PP, #0x57, lsl #12  ; [pp+0x57580] Null
    //     0x9e33a4: ldr             x3, [x3, #0x580]
    // 0x9e33a8: r0 = RenderBox?()
    //     0x9e33a8: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x9e33ac: ldur            x0, [fp, #-0x10]
    // 0x9e33b0: r1 = LoadClassIdInstr(r0)
    //     0x9e33b0: ldur            x1, [x0, #-1]
    //     0x9e33b4: ubfx            x1, x1, #0xc, #0x14
    // 0x9e33b8: cmp             x1, #0xbfc
    // 0x9e33bc: b.ne            #0x9e3464
    // 0x9e33c0: LoadField: r1 = r0->field_6f
    //     0x9e33c0: ldur            w1, [x0, #0x6f]
    // 0x9e33c4: DecompressPointer r1
    //     0x9e33c4: add             x1, x1, HEAP, lsl #32
    // 0x9e33c8: r16 = Sentinel
    //     0x9e33c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e33cc: cmp             w1, w16
    // 0x9e33d0: b.eq            #0x9e347c
    // 0x9e33d4: tbnz            w1, #4, #0x9e3464
    // 0x9e33d8: ldur            x2, [fp, #-8]
    // 0x9e33dc: LoadField: r1 = r2->field_1b
    //     0x9e33dc: ldur            w1, [x2, #0x1b]
    // 0x9e33e0: DecompressPointer r1
    //     0x9e33e0: add             x1, x1, HEAP, lsl #32
    // 0x9e33e4: r16 = Sentinel
    //     0x9e33e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e33e8: cmp             w1, w16
    // 0x9e33ec: b.eq            #0x9e3488
    // 0x9e33f0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9e33f0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9e33f4: r0 = reverse()
    //     0x9e33f4: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0x9e33f8: ldur            x0, [fp, #-8]
    // 0x9e33fc: LoadField: r3 = r0->field_1b
    //     0x9e33fc: ldur            w3, [x0, #0x1b]
    // 0x9e3400: DecompressPointer r3
    //     0x9e3400: add             x3, x3, HEAP, lsl #32
    // 0x9e3404: mov             x2, x0
    // 0x9e3408: stur            x3, [fp, #-0x10]
    // 0x9e340c: r1 = Function '_statusListener@247408280':.
    //     0x9e340c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57568] AnonymousClosure: (0x983e2c), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_statusListener (0x983e68)
    //     0x9e3410: ldr             x1, [x1, #0x568]
    // 0x9e3414: r0 = AllocateClosure()
    //     0x9e3414: bl              #0xec1630  ; AllocateClosureStub
    // 0x9e3418: ldur            x1, [fp, #-0x10]
    // 0x9e341c: mov             x2, x0
    // 0x9e3420: r0 = addStatusListener()
    //     0x9e3420: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0x9e3424: ldur            x2, [fp, #-8]
    // 0x9e3428: LoadField: r3 = r2->field_23
    //     0x9e3428: ldur            x3, [x2, #0x23]
    // 0x9e342c: sub             x4, x3, #1
    // 0x9e3430: r0 = BoxInt64Instr(r4)
    //     0x9e3430: sbfiz           x0, x4, #1, #0x1f
    //     0x9e3434: cmp             x4, x0, asr #1
    //     0x9e3438: b.eq            #0x9e3444
    //     0x9e343c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9e3440: stur            x4, [x0, #7]
    // 0x9e3444: StoreField: r2->field_1f = r0
    //     0x9e3444: stur            w0, [x2, #0x1f]
    //     0x9e3448: tbz             w0, #0, #0x9e3464
    //     0x9e344c: ldurb           w16, [x2, #-1]
    //     0x9e3450: ldurb           w17, [x0, #-1]
    //     0x9e3454: and             x16, x17, x16, lsr #2
    //     0x9e3458: tst             x16, HEAP, lsr #32
    //     0x9e345c: b.eq            #0x9e3464
    //     0x9e3460: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9e3464: r0 = Null
    //     0x9e3464: mov             x0, NULL
    // 0x9e3468: LeaveFrame
    //     0x9e3468: mov             SP, fp
    //     0x9e346c: ldp             fp, lr, [SP], #0x10
    // 0x9e3470: ret
    //     0x9e3470: ret             
    // 0x9e3474: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e3474: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e3478: b               #0x9e3350
    // 0x9e347c: r9 = hasPreviousPage
    //     0x9e347c: add             x9, PP, #0x57, lsl #12  ; [pp+0x57590] Field <<EMAIL>>: late (offset: 0x70)
    //     0x9e3480: ldr             x9, [x9, #0x590]
    // 0x9e3484: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9e3484: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9e3488: r9 = _controller
    //     0x9e3488: add             x9, PP, #0x57, lsl #12  ; [pp+0x57550] Field <_CupertinoTextSelectionToolbarContentState@247408280._controller@247408280>: late (offset: 0x1c)
    //     0x9e348c: ldr             x9, [x9, #0x550]
    // 0x9e3490: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9e3490: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Center <anonymous closure>(dynamic, Widget) {
    // ** addr: 0x9e3494, size: 0x38
    // 0x9e3494: EnterFrame
    //     0x9e3494: stp             fp, lr, [SP, #-0x10]!
    //     0x9e3498: mov             fp, SP
    // 0x9e349c: r0 = Center()
    //     0x9e349c: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x9e34a0: r1 = Instance_Alignment
    //     0x9e34a0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x9e34a4: ldr             x1, [x1, #0x898]
    // 0x9e34a8: StoreField: r0->field_f = r1
    //     0x9e34a8: stur            w1, [x0, #0xf]
    // 0x9e34ac: r1 = 1.000000
    //     0x9e34ac: ldr             x1, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x9e34b0: StoreField: r0->field_13 = r1
    //     0x9e34b0: stur            w1, [x0, #0x13]
    // 0x9e34b4: ArrayStore: r0[0] = r1  ; List_4
    //     0x9e34b4: stur            w1, [x0, #0x17]
    // 0x9e34b8: ldr             x1, [fp, #0x10]
    // 0x9e34bc: StoreField: r0->field_b = r1
    //     0x9e34bc: stur            w1, [x0, #0xb]
    // 0x9e34c0: LeaveFrame
    //     0x9e34c0: mov             SP, fp
    //     0x9e34c4: ldp             fp, lr, [SP], #0x10
    // 0x9e34c8: ret
    //     0x9e34c8: ret             
  }
  [closure] void _handleNextPage(dynamic) {
    // ** addr: 0x9e34cc, size: 0x38
    // 0x9e34cc: EnterFrame
    //     0x9e34cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9e34d0: mov             fp, SP
    // 0x9e34d4: ldr             x0, [fp, #0x10]
    // 0x9e34d8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9e34d8: ldur            w1, [x0, #0x17]
    // 0x9e34dc: DecompressPointer r1
    //     0x9e34dc: add             x1, x1, HEAP, lsl #32
    // 0x9e34e0: CheckStackOverflow
    //     0x9e34e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e34e4: cmp             SP, x16
    //     0x9e34e8: b.ls            #0x9e34fc
    // 0x9e34ec: r0 = _handleNextPage()
    //     0x9e34ec: bl              #0x9e31cc  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_handleNextPage
    // 0x9e34f0: LeaveFrame
    //     0x9e34f0: mov             SP, fp
    //     0x9e34f4: ldp             fp, lr, [SP], #0x10
    // 0x9e34f8: ret
    //     0x9e34f8: ret             
    // 0x9e34fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e34fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e3500: b               #0x9e34ec
  }
  [closure] void _handlePreviousPage(dynamic) {
    // ** addr: 0x9e3504, size: 0x38
    // 0x9e3504: EnterFrame
    //     0x9e3504: stp             fp, lr, [SP, #-0x10]!
    //     0x9e3508: mov             fp, SP
    // 0x9e350c: ldr             x0, [fp, #0x10]
    // 0x9e3510: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9e3510: ldur            w1, [x0, #0x17]
    // 0x9e3514: DecompressPointer r1
    //     0x9e3514: add             x1, x1, HEAP, lsl #32
    // 0x9e3518: CheckStackOverflow
    //     0x9e3518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e351c: cmp             SP, x16
    //     0x9e3520: b.ls            #0x9e3534
    // 0x9e3524: r0 = _handlePreviousPage()
    //     0x9e3524: bl              #0x9e3330  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_handlePreviousPage
    // 0x9e3528: LeaveFrame
    //     0x9e3528: mov             SP, fp
    //     0x9e352c: ldp             fp, lr, [SP], #0x10
    // 0x9e3530: ret
    //     0x9e3530: ret             
    // 0x9e3534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e3534: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e3538: b               #0x9e3524
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7bd38, size: 0x64
    // 0xa7bd38: EnterFrame
    //     0xa7bd38: stp             fp, lr, [SP, #-0x10]!
    //     0xa7bd3c: mov             fp, SP
    // 0xa7bd40: AllocStack(0x8)
    //     0xa7bd40: sub             SP, SP, #8
    // 0xa7bd44: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r0, fp-0x8 */)
    //     0xa7bd44: mov             x0, x1
    //     0xa7bd48: stur            x1, [fp, #-8]
    // 0xa7bd4c: CheckStackOverflow
    //     0xa7bd4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7bd50: cmp             SP, x16
    //     0xa7bd54: b.ls            #0xa7bd88
    // 0xa7bd58: LoadField: r1 = r0->field_1b
    //     0xa7bd58: ldur            w1, [x0, #0x1b]
    // 0xa7bd5c: DecompressPointer r1
    //     0xa7bd5c: add             x1, x1, HEAP, lsl #32
    // 0xa7bd60: r16 = Sentinel
    //     0xa7bd60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7bd64: cmp             w1, w16
    // 0xa7bd68: b.eq            #0xa7bd90
    // 0xa7bd6c: r0 = dispose()
    //     0xa7bd6c: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa7bd70: ldur            x1, [fp, #-8]
    // 0xa7bd74: r0 = dispose()
    //     0xa7bd74: bl              #0xa7bd9c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::dispose
    // 0xa7bd78: r0 = Null
    //     0xa7bd78: mov             x0, NULL
    // 0xa7bd7c: LeaveFrame
    //     0xa7bd7c: mov             SP, fp
    //     0xa7bd80: ldp             fp, lr, [SP], #0x10
    // 0xa7bd84: ret
    //     0xa7bd84: ret             
    // 0xa7bd88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7bd88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7bd8c: b               #0xa7bd58
    // 0xa7bd90: r9 = _controller
    //     0xa7bd90: add             x9, PP, #0x57, lsl #12  ; [pp+0x57550] Field <_CupertinoTextSelectionToolbarContentState@247408280._controller@247408280>: late (offset: 0x1c)
    //     0xa7bd94: ldr             x9, [x9, #0x550]
    // 0xa7bd98: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa7bd98: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4430, size: 0x50, field offset: 0x44
class _CupertinoTextSelectionToolbarItemsElement extends RenderObjectElement {

  late List<Element> _children; // offset: 0x44

  _ insertRenderObjectChild(/* No info */) {
    // ** addr: 0x7ae95c, size: 0x1c0
    // 0x7ae95c: EnterFrame
    //     0x7ae95c: stp             fp, lr, [SP, #-0x10]!
    //     0x7ae960: mov             fp, SP
    // 0x7ae964: AllocStack(0x20)
    //     0x7ae964: sub             SP, SP, #0x20
    // 0x7ae968: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7ae968: mov             x5, x1
    //     0x7ae96c: mov             x4, x2
    //     0x7ae970: stur            x1, [fp, #-8]
    //     0x7ae974: stur            x2, [fp, #-0x10]
    //     0x7ae978: stur            x3, [fp, #-0x18]
    // 0x7ae97c: CheckStackOverflow
    //     0x7ae97c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ae980: cmp             SP, x16
    //     0x7ae984: b.ls            #0x7aeb10
    // 0x7ae988: r0 = 60
    //     0x7ae988: movz            x0, #0x3c
    // 0x7ae98c: branchIfSmi(r3, 0x7ae998)
    //     0x7ae98c: tbz             w3, #0, #0x7ae998
    // 0x7ae990: r0 = LoadClassIdInstr(r3)
    //     0x7ae990: ldur            x0, [x3, #-1]
    //     0x7ae994: ubfx            x0, x0, #0xc, #0x14
    // 0x7ae998: r17 = 7094
    //     0x7ae998: movz            x17, #0x1bb6
    // 0x7ae99c: cmp             x0, x17
    // 0x7ae9a0: b.ne            #0x7ae9f4
    // 0x7ae9a4: mov             x0, x4
    // 0x7ae9a8: r2 = Null
    //     0x7ae9a8: mov             x2, NULL
    // 0x7ae9ac: r1 = Null
    //     0x7ae9ac: mov             x1, NULL
    // 0x7ae9b0: r4 = LoadClassIdInstr(r0)
    //     0x7ae9b0: ldur            x4, [x0, #-1]
    //     0x7ae9b4: ubfx            x4, x4, #0xc, #0x14
    // 0x7ae9b8: sub             x4, x4, #0xbba
    // 0x7ae9bc: cmp             x4, #0x9a
    // 0x7ae9c0: b.ls            #0x7ae9d4
    // 0x7ae9c4: r8 = RenderBox
    //     0x7ae9c4: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7ae9c8: r3 = Null
    //     0x7ae9c8: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d008] Null
    //     0x7ae9cc: ldr             x3, [x3, #8]
    // 0x7ae9d0: r0 = RenderBox()
    //     0x7ae9d0: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7ae9d4: ldur            x1, [fp, #-8]
    // 0x7ae9d8: ldur            x2, [fp, #-0x10]
    // 0x7ae9dc: ldur            x3, [fp, #-0x18]
    // 0x7ae9e0: r0 = _updateRenderObject()
    //     0x7ae9e0: bl              #0x7aeb1c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_updateRenderObject
    // 0x7ae9e4: r0 = Null
    //     0x7ae9e4: mov             x0, NULL
    // 0x7ae9e8: LeaveFrame
    //     0x7ae9e8: mov             SP, fp
    //     0x7ae9ec: ldp             fp, lr, [SP], #0x10
    // 0x7ae9f0: ret
    //     0x7ae9f0: ret             
    // 0x7ae9f4: cmp             x0, #0xa7f
    // 0x7ae9f8: b.ne            #0x7aeb00
    // 0x7ae9fc: ldur            x0, [fp, #-8]
    // 0x7aea00: ldur            x3, [fp, #-0x18]
    // 0x7aea04: LoadField: r4 = r0->field_3b
    //     0x7aea04: ldur            w4, [x0, #0x3b]
    // 0x7aea08: DecompressPointer r4
    //     0x7aea08: add             x4, x4, HEAP, lsl #32
    // 0x7aea0c: stur            x4, [fp, #-0x20]
    // 0x7aea10: cmp             w4, NULL
    // 0x7aea14: b.eq            #0x7aeb18
    // 0x7aea18: mov             x0, x4
    // 0x7aea1c: r2 = Null
    //     0x7aea1c: mov             x2, NULL
    // 0x7aea20: r1 = Null
    //     0x7aea20: mov             x1, NULL
    // 0x7aea24: r4 = LoadClassIdInstr(r0)
    //     0x7aea24: ldur            x4, [x0, #-1]
    //     0x7aea28: ubfx            x4, x4, #0xc, #0x14
    // 0x7aea2c: cmp             x4, #0xbfc
    // 0x7aea30: b.eq            #0x7aea48
    // 0x7aea34: r8 = _RenderCupertinoTextSelectionToolbarItems
    //     0x7aea34: add             x8, PP, #0x5a, lsl #12  ; [pp+0x5a610] Type: _RenderCupertinoTextSelectionToolbarItems
    //     0x7aea38: ldr             x8, [x8, #0x610]
    // 0x7aea3c: r3 = Null
    //     0x7aea3c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d018] Null
    //     0x7aea40: ldr             x3, [x3, #0x18]
    // 0x7aea44: r0 = DefaultTypeTest()
    //     0x7aea44: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7aea48: ldur            x0, [fp, #-0x10]
    // 0x7aea4c: r2 = Null
    //     0x7aea4c: mov             x2, NULL
    // 0x7aea50: r1 = Null
    //     0x7aea50: mov             x1, NULL
    // 0x7aea54: r4 = LoadClassIdInstr(r0)
    //     0x7aea54: ldur            x4, [x0, #-1]
    //     0x7aea58: ubfx            x4, x4, #0xc, #0x14
    // 0x7aea5c: sub             x4, x4, #0xbba
    // 0x7aea60: cmp             x4, #0x9a
    // 0x7aea64: b.ls            #0x7aea78
    // 0x7aea68: r8 = RenderBox
    //     0x7aea68: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7aea6c: r3 = Null
    //     0x7aea6c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d028] Null
    //     0x7aea70: ldr             x3, [x3, #0x28]
    // 0x7aea74: r0 = RenderBox()
    //     0x7aea74: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7aea78: ldur            x0, [fp, #-0x18]
    // 0x7aea7c: LoadField: r1 = r0->field_b
    //     0x7aea7c: ldur            w1, [x0, #0xb]
    // 0x7aea80: DecompressPointer r1
    //     0x7aea80: add             x1, x1, HEAP, lsl #32
    // 0x7aea84: cmp             w1, NULL
    // 0x7aea88: b.ne            #0x7aea94
    // 0x7aea8c: r3 = Null
    //     0x7aea8c: mov             x3, NULL
    // 0x7aea90: b               #0x7aeaac
    // 0x7aea94: r0 = LoadClassIdInstr(r1)
    //     0x7aea94: ldur            x0, [x1, #-1]
    //     0x7aea98: ubfx            x0, x0, #0xc, #0x14
    // 0x7aea9c: r0 = GDT[cid_x0 + 0xe16]()
    //     0x7aea9c: add             lr, x0, #0xe16
    //     0x7aeaa0: ldr             lr, [x21, lr, lsl #3]
    //     0x7aeaa4: blr             lr
    // 0x7aeaa8: mov             x3, x0
    // 0x7aeaac: mov             x0, x3
    // 0x7aeab0: stur            x3, [fp, #-8]
    // 0x7aeab4: r2 = Null
    //     0x7aeab4: mov             x2, NULL
    // 0x7aeab8: r1 = Null
    //     0x7aeab8: mov             x1, NULL
    // 0x7aeabc: r4 = LoadClassIdInstr(r0)
    //     0x7aeabc: ldur            x4, [x0, #-1]
    //     0x7aeac0: ubfx            x4, x4, #0xc, #0x14
    // 0x7aeac4: sub             x4, x4, #0xbba
    // 0x7aeac8: cmp             x4, #0x9a
    // 0x7aeacc: b.ls            #0x7aeae0
    // 0x7aead0: r8 = RenderBox?
    //     0x7aead0: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x7aead4: r3 = Null
    //     0x7aead4: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d038] Null
    //     0x7aead8: ldr             x3, [x3, #0x38]
    // 0x7aeadc: r0 = RenderBox?()
    //     0x7aeadc: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x7aeae0: ldur            x1, [fp, #-0x20]
    // 0x7aeae4: ldur            x2, [fp, #-0x10]
    // 0x7aeae8: ldur            x3, [fp, #-8]
    // 0x7aeaec: r0 = insert()
    //     0x7aeaec: bl              #0x7b0abc  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::insert
    // 0x7aeaf0: r0 = Null
    //     0x7aeaf0: mov             x0, NULL
    // 0x7aeaf4: LeaveFrame
    //     0x7aeaf4: mov             SP, fp
    //     0x7aeaf8: ldp             fp, lr, [SP], #0x10
    // 0x7aeafc: ret
    //     0x7aeafc: ret             
    // 0x7aeb00: r0 = Null
    //     0x7aeb00: mov             x0, NULL
    // 0x7aeb04: LeaveFrame
    //     0x7aeb04: mov             SP, fp
    //     0x7aeb08: ldp             fp, lr, [SP], #0x10
    // 0x7aeb0c: ret
    //     0x7aeb0c: ret             
    // 0x7aeb10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7aeb10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7aeb14: b               #0x7ae988
    // 0x7aeb18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7aeb18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateRenderObject(/* No info */) {
    // ** addr: 0x7aeb1c, size: 0xf0
    // 0x7aeb1c: EnterFrame
    //     0x7aeb1c: stp             fp, lr, [SP, #-0x10]!
    //     0x7aeb20: mov             fp, SP
    // 0x7aeb24: AllocStack(0x10)
    //     0x7aeb24: sub             SP, SP, #0x10
    // 0x7aeb28: SetupParameters(dynamic _ /* r2 => r4, fp-0x10 */)
    //     0x7aeb28: mov             x4, x2
    //     0x7aeb2c: stur            x2, [fp, #-0x10]
    // 0x7aeb30: CheckStackOverflow
    //     0x7aeb30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7aeb34: cmp             SP, x16
    //     0x7aeb38: b.ls            #0x7aebfc
    // 0x7aeb3c: LoadField: r0 = r3->field_7
    //     0x7aeb3c: ldur            x0, [x3, #7]
    // 0x7aeb40: cmp             x0, #0
    // 0x7aeb44: b.gt            #0x7aeb9c
    // 0x7aeb48: LoadField: r3 = r1->field_3b
    //     0x7aeb48: ldur            w3, [x1, #0x3b]
    // 0x7aeb4c: DecompressPointer r3
    //     0x7aeb4c: add             x3, x3, HEAP, lsl #32
    // 0x7aeb50: stur            x3, [fp, #-8]
    // 0x7aeb54: cmp             w3, NULL
    // 0x7aeb58: b.eq            #0x7aec04
    // 0x7aeb5c: mov             x0, x3
    // 0x7aeb60: r2 = Null
    //     0x7aeb60: mov             x2, NULL
    // 0x7aeb64: r1 = Null
    //     0x7aeb64: mov             x1, NULL
    // 0x7aeb68: r4 = LoadClassIdInstr(r0)
    //     0x7aeb68: ldur            x4, [x0, #-1]
    //     0x7aeb6c: ubfx            x4, x4, #0xc, #0x14
    // 0x7aeb70: cmp             x4, #0xbfc
    // 0x7aeb74: b.eq            #0x7aeb8c
    // 0x7aeb78: r8 = _RenderCupertinoTextSelectionToolbarItems
    //     0x7aeb78: add             x8, PP, #0x5a, lsl #12  ; [pp+0x5a610] Type: _RenderCupertinoTextSelectionToolbarItems
    //     0x7aeb7c: ldr             x8, [x8, #0x610]
    // 0x7aeb80: r3 = Null
    //     0x7aeb80: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf88] Null
    //     0x7aeb84: ldr             x3, [x3, #0xf88]
    // 0x7aeb88: r0 = DefaultTypeTest()
    //     0x7aeb88: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7aeb8c: ldur            x1, [fp, #-8]
    // 0x7aeb90: ldur            x2, [fp, #-0x10]
    // 0x7aeb94: r0 = backButton=()
    //     0x7aeb94: bl              #0x7aed3c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::backButton=
    // 0x7aeb98: b               #0x7aebec
    // 0x7aeb9c: LoadField: r3 = r1->field_3b
    //     0x7aeb9c: ldur            w3, [x1, #0x3b]
    // 0x7aeba0: DecompressPointer r3
    //     0x7aeba0: add             x3, x3, HEAP, lsl #32
    // 0x7aeba4: stur            x3, [fp, #-8]
    // 0x7aeba8: cmp             w3, NULL
    // 0x7aebac: b.eq            #0x7aec08
    // 0x7aebb0: mov             x0, x3
    // 0x7aebb4: r2 = Null
    //     0x7aebb4: mov             x2, NULL
    // 0x7aebb8: r1 = Null
    //     0x7aebb8: mov             x1, NULL
    // 0x7aebbc: r4 = LoadClassIdInstr(r0)
    //     0x7aebbc: ldur            x4, [x0, #-1]
    //     0x7aebc0: ubfx            x4, x4, #0xc, #0x14
    // 0x7aebc4: cmp             x4, #0xbfc
    // 0x7aebc8: b.eq            #0x7aebe0
    // 0x7aebcc: r8 = _RenderCupertinoTextSelectionToolbarItems
    //     0x7aebcc: add             x8, PP, #0x5a, lsl #12  ; [pp+0x5a610] Type: _RenderCupertinoTextSelectionToolbarItems
    //     0x7aebd0: ldr             x8, [x8, #0x610]
    // 0x7aebd4: r3 = Null
    //     0x7aebd4: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf98] Null
    //     0x7aebd8: ldr             x3, [x3, #0xf98]
    // 0x7aebdc: r0 = DefaultTypeTest()
    //     0x7aebdc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7aebe0: ldur            x1, [fp, #-8]
    // 0x7aebe4: ldur            x2, [fp, #-0x10]
    // 0x7aebe8: r0 = nextButton=()
    //     0x7aebe8: bl              #0x7aec0c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::nextButton=
    // 0x7aebec: r0 = Null
    //     0x7aebec: mov             x0, NULL
    // 0x7aebf0: LeaveFrame
    //     0x7aebf0: mov             SP, fp
    //     0x7aebf4: ldp             fp, lr, [SP], #0x10
    // 0x7aebf8: ret
    //     0x7aebf8: ret             
    // 0x7aebfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7aebfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7aec00: b               #0x7aeb3c
    // 0x7aec04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7aec04: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7aec08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7aec08: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ moveRenderObjectChild(/* No info */) {
    // ** addr: 0x7f0cb0, size: 0x168
    // 0x7f0cb0: EnterFrame
    //     0x7f0cb0: stp             fp, lr, [SP, #-0x10]!
    //     0x7f0cb4: mov             fp, SP
    // 0x7f0cb8: AllocStack(0x20)
    //     0x7f0cb8: sub             SP, SP, #0x20
    // 0x7f0cbc: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r0 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0x7f0cbc: mov             x0, x3
    //     0x7f0cc0: mov             x3, x5
    //     0x7f0cc4: stur            x5, [fp, #-0x18]
    //     0x7f0cc8: mov             x5, x1
    //     0x7f0ccc: mov             x4, x2
    //     0x7f0cd0: stur            x1, [fp, #-8]
    //     0x7f0cd4: stur            x2, [fp, #-0x10]
    // 0x7f0cd8: CheckStackOverflow
    //     0x7f0cd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f0cdc: cmp             SP, x16
    //     0x7f0ce0: b.ls            #0x7f0e0c
    // 0x7f0ce4: r2 = Null
    //     0x7f0ce4: mov             x2, NULL
    // 0x7f0ce8: r1 = Null
    //     0x7f0ce8: mov             x1, NULL
    // 0x7f0cec: r8 = IndexedSlot<Element>
    //     0x7f0cec: add             x8, PP, #0x5c, lsl #12  ; [pp+0x5cfa8] Type: IndexedSlot<Element>
    //     0x7f0cf0: ldr             x8, [x8, #0xfa8]
    // 0x7f0cf4: r3 = Null
    //     0x7f0cf4: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cfb0] Null
    //     0x7f0cf8: ldr             x3, [x3, #0xfb0]
    // 0x7f0cfc: r0 = IndexedSlot<Element>()
    //     0x7f0cfc: bl              #0x7f0e18  ; IsType_IndexedSlot<Element>_Stub
    // 0x7f0d00: ldur            x0, [fp, #-0x18]
    // 0x7f0d04: r2 = Null
    //     0x7f0d04: mov             x2, NULL
    // 0x7f0d08: r1 = Null
    //     0x7f0d08: mov             x1, NULL
    // 0x7f0d0c: r8 = IndexedSlot<Element>
    //     0x7f0d0c: add             x8, PP, #0x5c, lsl #12  ; [pp+0x5cfa8] Type: IndexedSlot<Element>
    //     0x7f0d10: ldr             x8, [x8, #0xfa8]
    // 0x7f0d14: r3 = Null
    //     0x7f0d14: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cfc0] Null
    //     0x7f0d18: ldr             x3, [x3, #0xfc0]
    // 0x7f0d1c: r0 = IndexedSlot<Element>()
    //     0x7f0d1c: bl              #0x7f0e18  ; IsType_IndexedSlot<Element>_Stub
    // 0x7f0d20: ldur            x0, [fp, #-8]
    // 0x7f0d24: LoadField: r3 = r0->field_3b
    //     0x7f0d24: ldur            w3, [x0, #0x3b]
    // 0x7f0d28: DecompressPointer r3
    //     0x7f0d28: add             x3, x3, HEAP, lsl #32
    // 0x7f0d2c: stur            x3, [fp, #-0x20]
    // 0x7f0d30: cmp             w3, NULL
    // 0x7f0d34: b.eq            #0x7f0e14
    // 0x7f0d38: mov             x0, x3
    // 0x7f0d3c: r2 = Null
    //     0x7f0d3c: mov             x2, NULL
    // 0x7f0d40: r1 = Null
    //     0x7f0d40: mov             x1, NULL
    // 0x7f0d44: r4 = LoadClassIdInstr(r0)
    //     0x7f0d44: ldur            x4, [x0, #-1]
    //     0x7f0d48: ubfx            x4, x4, #0xc, #0x14
    // 0x7f0d4c: cmp             x4, #0xbfc
    // 0x7f0d50: b.eq            #0x7f0d68
    // 0x7f0d54: r8 = _RenderCupertinoTextSelectionToolbarItems
    //     0x7f0d54: add             x8, PP, #0x5a, lsl #12  ; [pp+0x5a610] Type: _RenderCupertinoTextSelectionToolbarItems
    //     0x7f0d58: ldr             x8, [x8, #0x610]
    // 0x7f0d5c: r3 = Null
    //     0x7f0d5c: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cfd0] Null
    //     0x7f0d60: ldr             x3, [x3, #0xfd0]
    // 0x7f0d64: r0 = DefaultTypeTest()
    //     0x7f0d64: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7f0d68: ldur            x0, [fp, #-0x10]
    // 0x7f0d6c: r2 = Null
    //     0x7f0d6c: mov             x2, NULL
    // 0x7f0d70: r1 = Null
    //     0x7f0d70: mov             x1, NULL
    // 0x7f0d74: r4 = LoadClassIdInstr(r0)
    //     0x7f0d74: ldur            x4, [x0, #-1]
    //     0x7f0d78: ubfx            x4, x4, #0xc, #0x14
    // 0x7f0d7c: sub             x4, x4, #0xbba
    // 0x7f0d80: cmp             x4, #0x9a
    // 0x7f0d84: b.ls            #0x7f0d98
    // 0x7f0d88: r8 = RenderBox
    //     0x7f0d88: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7f0d8c: r3 = Null
    //     0x7f0d8c: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cfe0] Null
    //     0x7f0d90: ldr             x3, [x3, #0xfe0]
    // 0x7f0d94: r0 = RenderBox()
    //     0x7f0d94: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7f0d98: ldur            x0, [fp, #-0x18]
    // 0x7f0d9c: LoadField: r1 = r0->field_b
    //     0x7f0d9c: ldur            w1, [x0, #0xb]
    // 0x7f0da0: DecompressPointer r1
    //     0x7f0da0: add             x1, x1, HEAP, lsl #32
    // 0x7f0da4: r0 = LoadClassIdInstr(r1)
    //     0x7f0da4: ldur            x0, [x1, #-1]
    //     0x7f0da8: ubfx            x0, x0, #0xc, #0x14
    // 0x7f0dac: r0 = GDT[cid_x0 + 0xe16]()
    //     0x7f0dac: add             lr, x0, #0xe16
    //     0x7f0db0: ldr             lr, [x21, lr, lsl #3]
    //     0x7f0db4: blr             lr
    // 0x7f0db8: mov             x3, x0
    // 0x7f0dbc: r2 = Null
    //     0x7f0dbc: mov             x2, NULL
    // 0x7f0dc0: r1 = Null
    //     0x7f0dc0: mov             x1, NULL
    // 0x7f0dc4: stur            x3, [fp, #-8]
    // 0x7f0dc8: r4 = LoadClassIdInstr(r0)
    //     0x7f0dc8: ldur            x4, [x0, #-1]
    //     0x7f0dcc: ubfx            x4, x4, #0xc, #0x14
    // 0x7f0dd0: sub             x4, x4, #0xbba
    // 0x7f0dd4: cmp             x4, #0x9a
    // 0x7f0dd8: b.ls            #0x7f0dec
    // 0x7f0ddc: r8 = RenderBox?
    //     0x7f0ddc: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x7f0de0: r3 = Null
    //     0x7f0de0: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cff8] Null
    //     0x7f0de4: ldr             x3, [x3, #0xff8]
    // 0x7f0de8: r0 = RenderBox?()
    //     0x7f0de8: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x7f0dec: ldur            x1, [fp, #-0x20]
    // 0x7f0df0: ldur            x2, [fp, #-0x10]
    // 0x7f0df4: ldur            x3, [fp, #-8]
    // 0x7f0df8: r0 = move()
    //     0x7f0df8: bl              #0x7cdb78  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::move
    // 0x7f0dfc: r0 = Null
    //     0x7f0dfc: mov             x0, NULL
    // 0x7f0e00: LeaveFrame
    //     0x7f0e00: mov             SP, fp
    //     0x7f0e04: ldp             fp, lr, [SP], #0x10
    // 0x7f0e08: ret
    //     0x7f0e08: ret             
    // 0x7f0e0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f0e0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f0e10: b               #0x7f0ce4
    // 0x7f0e14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f0e14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ removeRenderObjectChild(/* No info */) {
    // ** addr: 0x803314, size: 0xb4
    // 0x803314: EnterFrame
    //     0x803314: stp             fp, lr, [SP, #-0x10]!
    //     0x803318: mov             fp, SP
    // 0x80331c: AllocStack(0x10)
    //     0x80331c: sub             SP, SP, #0x10
    // 0x803320: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x803320: mov             x0, x2
    //     0x803324: stur            x2, [fp, #-8]
    // 0x803328: CheckStackOverflow
    //     0x803328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80332c: cmp             SP, x16
    //     0x803330: b.ls            #0x8033c0
    // 0x803334: r2 = 60
    //     0x803334: movz            x2, #0x3c
    // 0x803338: branchIfSmi(r3, 0x803344)
    //     0x803338: tbz             w3, #0, #0x803344
    // 0x80333c: r2 = LoadClassIdInstr(r3)
    //     0x80333c: ldur            x2, [x3, #-1]
    //     0x803340: ubfx            x2, x2, #0xc, #0x14
    // 0x803344: r17 = 7094
    //     0x803344: movz            x17, #0x1bb6
    // 0x803348: cmp             x2, x17
    // 0x80334c: b.ne            #0x803368
    // 0x803350: r2 = Null
    //     0x803350: mov             x2, NULL
    // 0x803354: r0 = _updateRenderObject()
    //     0x803354: bl              #0x7aeb1c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_updateRenderObject
    // 0x803358: r0 = Null
    //     0x803358: mov             x0, NULL
    // 0x80335c: LeaveFrame
    //     0x80335c: mov             SP, fp
    //     0x803360: ldp             fp, lr, [SP], #0x10
    // 0x803364: ret
    //     0x803364: ret             
    // 0x803368: r0 = renderObject()
    //     0x803368: bl              #0xd151f8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::renderObject
    // 0x80336c: mov             x3, x0
    // 0x803370: ldur            x0, [fp, #-8]
    // 0x803374: r2 = Null
    //     0x803374: mov             x2, NULL
    // 0x803378: r1 = Null
    //     0x803378: mov             x1, NULL
    // 0x80337c: stur            x3, [fp, #-0x10]
    // 0x803380: r4 = LoadClassIdInstr(r0)
    //     0x803380: ldur            x4, [x0, #-1]
    //     0x803384: ubfx            x4, x4, #0xc, #0x14
    // 0x803388: sub             x4, x4, #0xbba
    // 0x80338c: cmp             x4, #0x9a
    // 0x803390: b.ls            #0x8033a4
    // 0x803394: r8 = RenderBox
    //     0x803394: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x803398: r3 = Null
    //     0x803398: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf78] Null
    //     0x80339c: ldr             x3, [x3, #0xf78]
    // 0x8033a0: r0 = RenderBox()
    //     0x8033a0: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x8033a4: ldur            x1, [fp, #-0x10]
    // 0x8033a8: ldur            x2, [fp, #-8]
    // 0x8033ac: r0 = remove()
    //     0x8033ac: bl              #0x7b1f90  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::remove
    // 0x8033b0: r0 = Null
    //     0x8033b0: mov             x0, NULL
    // 0x8033b4: LeaveFrame
    //     0x8033b4: mov             SP, fp
    //     0x8033b8: ldp             fp, lr, [SP], #0x10
    // 0x8033bc: ret
    //     0x8033bc: ret             
    // 0x8033c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8033c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8033c4: b               #0x803334
  }
  _ update(/* No info */) {
    // ** addr: 0x86d7e4, size: 0x184
    // 0x86d7e4: EnterFrame
    //     0x86d7e4: stp             fp, lr, [SP, #-0x10]!
    //     0x86d7e8: mov             fp, SP
    // 0x86d7ec: AllocStack(0x10)
    //     0x86d7ec: sub             SP, SP, #0x10
    // 0x86d7f0: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x86d7f0: mov             x4, x1
    //     0x86d7f4: mov             x3, x2
    //     0x86d7f8: stur            x1, [fp, #-8]
    //     0x86d7fc: stur            x2, [fp, #-0x10]
    // 0x86d800: CheckStackOverflow
    //     0x86d800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86d804: cmp             SP, x16
    //     0x86d808: b.ls            #0x86d950
    // 0x86d80c: mov             x0, x3
    // 0x86d810: r2 = Null
    //     0x86d810: mov             x2, NULL
    // 0x86d814: r1 = Null
    //     0x86d814: mov             x1, NULL
    // 0x86d818: r4 = 60
    //     0x86d818: movz            x4, #0x3c
    // 0x86d81c: branchIfSmi(r0, 0x86d828)
    //     0x86d81c: tbz             w0, #0, #0x86d828
    // 0x86d820: r4 = LoadClassIdInstr(r0)
    //     0x86d820: ldur            x4, [x0, #-1]
    //     0x86d824: ubfx            x4, x4, #0xc, #0x14
    // 0x86d828: r17 = 4480
    //     0x86d828: movz            x17, #0x1180
    // 0x86d82c: cmp             x4, x17
    // 0x86d830: b.eq            #0x86d848
    // 0x86d834: r8 = _CupertinoTextSelectionToolbarItems
    //     0x86d834: add             x8, PP, #0x5c, lsl #12  ; [pp+0x5cf10] Type: _CupertinoTextSelectionToolbarItems
    //     0x86d838: ldr             x8, [x8, #0xf10]
    // 0x86d83c: r3 = Null
    //     0x86d83c: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf18] Null
    //     0x86d840: ldr             x3, [x3, #0xf18]
    // 0x86d844: r0 = DefaultTypeTest()
    //     0x86d844: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x86d848: ldur            x1, [fp, #-8]
    // 0x86d84c: ldur            x2, [fp, #-0x10]
    // 0x86d850: r0 = update()
    //     0x86d850: bl              #0x86fd38  ; [package:flutter/src/widgets/framework.dart] RenderObjectElement::update
    // 0x86d854: ldur            x3, [fp, #-8]
    // 0x86d858: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x86d858: ldur            w4, [x3, #0x17]
    // 0x86d85c: DecompressPointer r4
    //     0x86d85c: add             x4, x4, HEAP, lsl #32
    // 0x86d860: stur            x4, [fp, #-0x10]
    // 0x86d864: cmp             w4, NULL
    // 0x86d868: b.eq            #0x86d958
    // 0x86d86c: mov             x0, x4
    // 0x86d870: r2 = Null
    //     0x86d870: mov             x2, NULL
    // 0x86d874: r1 = Null
    //     0x86d874: mov             x1, NULL
    // 0x86d878: r4 = LoadClassIdInstr(r0)
    //     0x86d878: ldur            x4, [x0, #-1]
    //     0x86d87c: ubfx            x4, x4, #0xc, #0x14
    // 0x86d880: r17 = 4480
    //     0x86d880: movz            x17, #0x1180
    // 0x86d884: cmp             x4, x17
    // 0x86d888: b.eq            #0x86d8a0
    // 0x86d88c: r8 = _CupertinoTextSelectionToolbarItems
    //     0x86d88c: add             x8, PP, #0x5c, lsl #12  ; [pp+0x5cf10] Type: _CupertinoTextSelectionToolbarItems
    //     0x86d890: ldr             x8, [x8, #0xf10]
    // 0x86d894: r3 = Null
    //     0x86d894: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf28] Null
    //     0x86d898: ldr             x3, [x3, #0xf28]
    // 0x86d89c: r0 = DefaultTypeTest()
    //     0x86d89c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x86d8a0: ldur            x0, [fp, #-0x10]
    // 0x86d8a4: LoadField: r2 = r0->field_b
    //     0x86d8a4: ldur            w2, [x0, #0xb]
    // 0x86d8a8: DecompressPointer r2
    //     0x86d8a8: add             x2, x2, HEAP, lsl #32
    // 0x86d8ac: ldur            x1, [fp, #-8]
    // 0x86d8b0: r3 = Instance__CupertinoTextSelectionToolbarItemsSlot
    //     0x86d8b0: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf38] Obj!_CupertinoTextSelectionToolbarItemsSlot@e370a1
    //     0x86d8b4: ldr             x3, [x3, #0xf38]
    // 0x86d8b8: r0 = _mountChild()
    //     0x86d8b8: bl              #0x86ef90  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_mountChild
    // 0x86d8bc: ldur            x0, [fp, #-0x10]
    // 0x86d8c0: LoadField: r2 = r0->field_1f
    //     0x86d8c0: ldur            w2, [x0, #0x1f]
    // 0x86d8c4: DecompressPointer r2
    //     0x86d8c4: add             x2, x2, HEAP, lsl #32
    // 0x86d8c8: ldur            x1, [fp, #-8]
    // 0x86d8cc: r3 = Instance__CupertinoTextSelectionToolbarItemsSlot
    //     0x86d8cc: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf40] Obj!_CupertinoTextSelectionToolbarItemsSlot@e37081
    //     0x86d8d0: ldr             x3, [x3, #0xf40]
    // 0x86d8d4: r0 = _mountChild()
    //     0x86d8d4: bl              #0x86ef90  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_mountChild
    // 0x86d8d8: ldur            x0, [fp, #-8]
    // 0x86d8dc: LoadField: r2 = r0->field_43
    //     0x86d8dc: ldur            w2, [x0, #0x43]
    // 0x86d8e0: DecompressPointer r2
    //     0x86d8e0: add             x2, x2, HEAP, lsl #32
    // 0x86d8e4: r16 = Sentinel
    //     0x86d8e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x86d8e8: cmp             w2, w16
    // 0x86d8ec: b.eq            #0x86d95c
    // 0x86d8f0: ldur            x1, [fp, #-0x10]
    // 0x86d8f4: LoadField: r3 = r1->field_f
    //     0x86d8f4: ldur            w3, [x1, #0xf]
    // 0x86d8f8: DecompressPointer r3
    //     0x86d8f8: add             x3, x3, HEAP, lsl #32
    // 0x86d8fc: LoadField: r4 = r0->field_4b
    //     0x86d8fc: ldur            w4, [x0, #0x4b]
    // 0x86d900: DecompressPointer r4
    //     0x86d900: add             x4, x4, HEAP, lsl #32
    // 0x86d904: mov             x1, x0
    // 0x86d908: mov             x5, x4
    // 0x86d90c: stur            x4, [fp, #-0x10]
    // 0x86d910: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x86d910: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x86d914: r0 = updateChildren()
    //     0x86d914: bl              #0x86d968  ; [package:flutter/src/widgets/framework.dart] Element::updateChildren
    // 0x86d918: ldur            x1, [fp, #-8]
    // 0x86d91c: StoreField: r1->field_43 = r0
    //     0x86d91c: stur            w0, [x1, #0x43]
    //     0x86d920: ldurb           w16, [x1, #-1]
    //     0x86d924: ldurb           w17, [x0, #-1]
    //     0x86d928: and             x16, x17, x16, lsr #2
    //     0x86d92c: tst             x16, HEAP, lsr #32
    //     0x86d930: b.eq            #0x86d938
    //     0x86d934: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x86d938: ldur            x1, [fp, #-0x10]
    // 0x86d93c: r0 = clear()
    //     0x86d93c: bl              #0x64ab10  ; [dart:collection] _HashSet::clear
    // 0x86d940: r0 = Null
    //     0x86d940: mov             x0, NULL
    // 0x86d944: LeaveFrame
    //     0x86d944: mov             SP, fp
    //     0x86d948: ldp             fp, lr, [SP], #0x10
    // 0x86d94c: ret
    //     0x86d94c: ret             
    // 0x86d950: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86d950: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86d954: b               #0x86d80c
    // 0x86d958: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x86d958: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x86d95c: r9 = _children
    //     0x86d95c: add             x9, PP, #0x5c, lsl #12  ; [pp+0x5cf48] Field <_CupertinoTextSelectionToolbarItemsElement@247408280._children@247408280>: late (offset: 0x44)
    //     0x86d960: ldr             x9, [x9, #0xf48]
    // 0x86d964: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x86d964: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _mountChild(/* No info */) {
    // ** addr: 0x86ef90, size: 0x208
    // 0x86ef90: EnterFrame
    //     0x86ef90: stp             fp, lr, [SP, #-0x10]!
    //     0x86ef94: mov             fp, SP
    // 0x86ef98: AllocStack(0x38)
    //     0x86ef98: sub             SP, SP, #0x38
    // 0x86ef9c: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0x86ef9c: mov             x4, x1
    //     0x86efa0: mov             x0, x3
    //     0x86efa4: stur            x3, [fp, #-0x20]
    //     0x86efa8: mov             x3, x2
    //     0x86efac: stur            x1, [fp, #-0x10]
    //     0x86efb0: stur            x2, [fp, #-0x18]
    // 0x86efb4: CheckStackOverflow
    //     0x86efb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86efb8: cmp             SP, x16
    //     0x86efbc: b.ls            #0x86f190
    // 0x86efc0: LoadField: r5 = r4->field_47
    //     0x86efc0: ldur            w5, [x4, #0x47]
    // 0x86efc4: DecompressPointer r5
    //     0x86efc4: add             x5, x5, HEAP, lsl #32
    // 0x86efc8: mov             x1, x5
    // 0x86efcc: mov             x2, x0
    // 0x86efd0: stur            x5, [fp, #-8]
    // 0x86efd4: r0 = _getValueOrData()
    //     0x86efd4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x86efd8: ldur            x2, [fp, #-8]
    // 0x86efdc: LoadField: r1 = r2->field_f
    //     0x86efdc: ldur            w1, [x2, #0xf]
    // 0x86efe0: DecompressPointer r1
    //     0x86efe0: add             x1, x1, HEAP, lsl #32
    // 0x86efe4: cmp             w1, w0
    // 0x86efe8: b.ne            #0x86eff4
    // 0x86efec: r3 = Null
    //     0x86efec: mov             x3, NULL
    // 0x86eff0: b               #0x86eff8
    // 0x86eff4: mov             x3, x0
    // 0x86eff8: stur            x3, [fp, #-0x28]
    // 0x86effc: cmp             w3, NULL
    // 0x86f000: b.eq            #0x86f140
    // 0x86f004: ldur            x4, [fp, #-0x18]
    // 0x86f008: r0 = LoadClassIdInstr(r3)
    //     0x86f008: ldur            x0, [x3, #-1]
    //     0x86f00c: ubfx            x0, x0, #0xc, #0x14
    // 0x86f010: mov             x1, x3
    // 0x86f014: r0 = GDT[cid_x0 + 0xee4]()
    //     0x86f014: add             lr, x0, #0xee4
    //     0x86f018: ldr             lr, [x21, lr, lsl #3]
    //     0x86f01c: blr             lr
    // 0x86f020: ldur            x2, [fp, #-0x18]
    // 0x86f024: cmp             w0, w2
    // 0x86f028: b.ne            #0x86f07c
    // 0x86f02c: ldur            x2, [fp, #-0x28]
    // 0x86f030: LoadField: r0 = r2->field_f
    //     0x86f030: ldur            w0, [x2, #0xf]
    // 0x86f034: DecompressPointer r0
    //     0x86f034: add             x0, x0, HEAP, lsl #32
    // 0x86f038: r1 = 60
    //     0x86f038: movz            x1, #0x3c
    // 0x86f03c: branchIfSmi(r0, 0x86f048)
    //     0x86f03c: tbz             w0, #0, #0x86f048
    // 0x86f040: r1 = LoadClassIdInstr(r0)
    //     0x86f040: ldur            x1, [x0, #-1]
    //     0x86f044: ubfx            x1, x1, #0xc, #0x14
    // 0x86f048: ldur            x16, [fp, #-0x20]
    // 0x86f04c: stp             x16, x0, [SP]
    // 0x86f050: mov             x0, x1
    // 0x86f054: mov             lr, x0
    // 0x86f058: ldr             lr, [x21, lr, lsl #3]
    // 0x86f05c: blr             lr
    // 0x86f060: tbz             w0, #4, #0x86f074
    // 0x86f064: ldur            x1, [fp, #-0x10]
    // 0x86f068: ldur            x2, [fp, #-0x28]
    // 0x86f06c: ldur            x3, [fp, #-0x20]
    // 0x86f070: r0 = updateSlotForChild()
    //     0x86f070: bl              #0x86ece8  ; [package:flutter/src/widgets/framework.dart] Element::updateSlotForChild
    // 0x86f074: ldur            x0, [fp, #-0x28]
    // 0x86f078: b               #0x86f138
    // 0x86f07c: ldur            x3, [fp, #-0x28]
    // 0x86f080: r0 = LoadClassIdInstr(r3)
    //     0x86f080: ldur            x0, [x3, #-1]
    //     0x86f084: ubfx            x0, x0, #0xc, #0x14
    // 0x86f088: mov             x1, x3
    // 0x86f08c: r0 = GDT[cid_x0 + 0xee4]()
    //     0x86f08c: add             lr, x0, #0xee4
    //     0x86f090: ldr             lr, [x21, lr, lsl #3]
    //     0x86f094: blr             lr
    // 0x86f098: mov             x1, x0
    // 0x86f09c: ldur            x2, [fp, #-0x18]
    // 0x86f0a0: r0 = canUpdate()
    //     0x86f0a0: bl              #0x86bb0c  ; [package:flutter/src/widgets/framework.dart] Widget::canUpdate
    // 0x86f0a4: tbnz            w0, #4, #0x86f11c
    // 0x86f0a8: ldur            x2, [fp, #-0x28]
    // 0x86f0ac: LoadField: r0 = r2->field_f
    //     0x86f0ac: ldur            w0, [x2, #0xf]
    // 0x86f0b0: DecompressPointer r0
    //     0x86f0b0: add             x0, x0, HEAP, lsl #32
    // 0x86f0b4: r1 = 60
    //     0x86f0b4: movz            x1, #0x3c
    // 0x86f0b8: branchIfSmi(r0, 0x86f0c4)
    //     0x86f0b8: tbz             w0, #0, #0x86f0c4
    // 0x86f0bc: r1 = LoadClassIdInstr(r0)
    //     0x86f0bc: ldur            x1, [x0, #-1]
    //     0x86f0c0: ubfx            x1, x1, #0xc, #0x14
    // 0x86f0c4: ldur            x16, [fp, #-0x20]
    // 0x86f0c8: stp             x16, x0, [SP]
    // 0x86f0cc: mov             x0, x1
    // 0x86f0d0: mov             lr, x0
    // 0x86f0d4: ldr             lr, [x21, lr, lsl #3]
    // 0x86f0d8: blr             lr
    // 0x86f0dc: tbz             w0, #4, #0x86f0f0
    // 0x86f0e0: ldur            x1, [fp, #-0x10]
    // 0x86f0e4: ldur            x2, [fp, #-0x28]
    // 0x86f0e8: ldur            x3, [fp, #-0x20]
    // 0x86f0ec: r0 = updateSlotForChild()
    //     0x86f0ec: bl              #0x86ece8  ; [package:flutter/src/widgets/framework.dart] Element::updateSlotForChild
    // 0x86f0f0: ldur            x3, [fp, #-0x28]
    // 0x86f0f4: r0 = LoadClassIdInstr(r3)
    //     0x86f0f4: ldur            x0, [x3, #-1]
    //     0x86f0f8: ubfx            x0, x0, #0xc, #0x14
    // 0x86f0fc: mov             x1, x3
    // 0x86f100: ldur            x2, [fp, #-0x18]
    // 0x86f104: r0 = GDT[cid_x0 + 0xe072]()
    //     0x86f104: movz            x17, #0xe072
    //     0x86f108: add             lr, x0, x17
    //     0x86f10c: ldr             lr, [x21, lr, lsl #3]
    //     0x86f110: blr             lr
    // 0x86f114: ldur            x0, [fp, #-0x28]
    // 0x86f118: b               #0x86f138
    // 0x86f11c: ldur            x1, [fp, #-0x10]
    // 0x86f120: ldur            x2, [fp, #-0x28]
    // 0x86f124: r0 = deactivateChild()
    //     0x86f124: bl              #0x86b388  ; [package:flutter/src/widgets/framework.dart] Element::deactivateChild
    // 0x86f128: ldur            x1, [fp, #-0x10]
    // 0x86f12c: ldur            x2, [fp, #-0x18]
    // 0x86f130: ldur            x3, [fp, #-0x20]
    // 0x86f134: r0 = inflateWidget()
    //     0x86f134: bl              #0x86b1c8  ; [package:flutter/src/widgets/framework.dart] Element::inflateWidget
    // 0x86f138: mov             x3, x0
    // 0x86f13c: b               #0x86f154
    // 0x86f140: ldur            x1, [fp, #-0x10]
    // 0x86f144: ldur            x2, [fp, #-0x18]
    // 0x86f148: ldur            x3, [fp, #-0x20]
    // 0x86f14c: r0 = inflateWidget()
    //     0x86f14c: bl              #0x86b1c8  ; [package:flutter/src/widgets/framework.dart] Element::inflateWidget
    // 0x86f150: mov             x3, x0
    // 0x86f154: ldur            x0, [fp, #-0x28]
    // 0x86f158: stur            x3, [fp, #-0x10]
    // 0x86f15c: cmp             w0, NULL
    // 0x86f160: b.eq            #0x86f170
    // 0x86f164: ldur            x1, [fp, #-8]
    // 0x86f168: ldur            x2, [fp, #-0x20]
    // 0x86f16c: r0 = remove()
    //     0x86f16c: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x86f170: ldur            x1, [fp, #-8]
    // 0x86f174: ldur            x2, [fp, #-0x20]
    // 0x86f178: ldur            x3, [fp, #-0x10]
    // 0x86f17c: r0 = []=()
    //     0x86f17c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x86f180: r0 = Null
    //     0x86f180: mov             x0, NULL
    // 0x86f184: LeaveFrame
    //     0x86f184: mov             SP, fp
    //     0x86f188: ldp             fp, lr, [SP], #0x10
    // 0x86f18c: ret
    //     0x86f18c: ret             
    // 0x86f190: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86f190: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86f194: b               #0x86efc0
  }
  _ mount(/* No info */) {
    // ** addr: 0x88fce0, size: 0x200
    // 0x88fce0: EnterFrame
    //     0x88fce0: stp             fp, lr, [SP, #-0x10]!
    //     0x88fce4: mov             fp, SP
    // 0x88fce8: AllocStack(0x38)
    //     0x88fce8: sub             SP, SP, #0x38
    // 0x88fcec: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r0, fp-0x8 */)
    //     0x88fcec: mov             x0, x1
    //     0x88fcf0: stur            x1, [fp, #-8]
    // 0x88fcf4: CheckStackOverflow
    //     0x88fcf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88fcf8: cmp             SP, x16
    //     0x88fcfc: b.ls            #0x88fec8
    // 0x88fd00: mov             x1, x0
    // 0x88fd04: r0 = mount()
    //     0x88fd04: bl              #0x8911b0  ; [package:flutter/src/widgets/framework.dart] RenderObjectElement::mount
    // 0x88fd08: ldur            x3, [fp, #-8]
    // 0x88fd0c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x88fd0c: ldur            w4, [x3, #0x17]
    // 0x88fd10: DecompressPointer r4
    //     0x88fd10: add             x4, x4, HEAP, lsl #32
    // 0x88fd14: stur            x4, [fp, #-0x10]
    // 0x88fd18: cmp             w4, NULL
    // 0x88fd1c: b.eq            #0x88fed0
    // 0x88fd20: mov             x0, x4
    // 0x88fd24: r2 = Null
    //     0x88fd24: mov             x2, NULL
    // 0x88fd28: r1 = Null
    //     0x88fd28: mov             x1, NULL
    // 0x88fd2c: r4 = LoadClassIdInstr(r0)
    //     0x88fd2c: ldur            x4, [x0, #-1]
    //     0x88fd30: ubfx            x4, x4, #0xc, #0x14
    // 0x88fd34: r17 = 4480
    //     0x88fd34: movz            x17, #0x1180
    // 0x88fd38: cmp             x4, x17
    // 0x88fd3c: b.eq            #0x88fd54
    // 0x88fd40: r8 = _CupertinoTextSelectionToolbarItems
    //     0x88fd40: add             x8, PP, #0x5c, lsl #12  ; [pp+0x5cf10] Type: _CupertinoTextSelectionToolbarItems
    //     0x88fd44: ldr             x8, [x8, #0xf10]
    // 0x88fd48: r3 = Null
    //     0x88fd48: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf50] Null
    //     0x88fd4c: ldr             x3, [x3, #0xf50]
    // 0x88fd50: r0 = DefaultTypeTest()
    //     0x88fd50: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x88fd54: ldur            x0, [fp, #-0x10]
    // 0x88fd58: LoadField: r2 = r0->field_b
    //     0x88fd58: ldur            w2, [x0, #0xb]
    // 0x88fd5c: DecompressPointer r2
    //     0x88fd5c: add             x2, x2, HEAP, lsl #32
    // 0x88fd60: ldur            x1, [fp, #-8]
    // 0x88fd64: r3 = Instance__CupertinoTextSelectionToolbarItemsSlot
    //     0x88fd64: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf38] Obj!_CupertinoTextSelectionToolbarItemsSlot@e370a1
    //     0x88fd68: ldr             x3, [x3, #0xf38]
    // 0x88fd6c: r0 = _mountChild()
    //     0x88fd6c: bl              #0x86ef90  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_mountChild
    // 0x88fd70: ldur            x0, [fp, #-0x10]
    // 0x88fd74: LoadField: r2 = r0->field_1f
    //     0x88fd74: ldur            w2, [x0, #0x1f]
    // 0x88fd78: DecompressPointer r2
    //     0x88fd78: add             x2, x2, HEAP, lsl #32
    // 0x88fd7c: ldur            x1, [fp, #-8]
    // 0x88fd80: r3 = Instance__CupertinoTextSelectionToolbarItemsSlot
    //     0x88fd80: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf40] Obj!_CupertinoTextSelectionToolbarItemsSlot@e37081
    //     0x88fd84: ldr             x3, [x3, #0xf40]
    // 0x88fd88: r0 = _mountChild()
    //     0x88fd88: bl              #0x86ef90  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_mountChild
    // 0x88fd8c: ldur            x0, [fp, #-0x10]
    // 0x88fd90: LoadField: r3 = r0->field_f
    //     0x88fd90: ldur            w3, [x0, #0xf]
    // 0x88fd94: DecompressPointer r3
    //     0x88fd94: add             x3, x3, HEAP, lsl #32
    // 0x88fd98: stur            x3, [fp, #-0x18]
    // 0x88fd9c: LoadField: r0 = r3->field_b
    //     0x88fd9c: ldur            w0, [x3, #0xb]
    // 0x88fda0: mov             x2, x0
    // 0x88fda4: stur            x0, [fp, #-0x10]
    // 0x88fda8: r1 = <Element>
    //     0x88fda8: ldr             x1, [PP, #0x2158]  ; [pp+0x2158] TypeArguments: <Element>
    // 0x88fdac: r0 = AllocateArray()
    //     0x88fdac: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88fdb0: mov             x2, x0
    // 0x88fdb4: ldur            x0, [fp, #-0x10]
    // 0x88fdb8: stur            x2, [fp, #-0x38]
    // 0x88fdbc: r3 = LoadInt32Instr(r0)
    //     0x88fdbc: sbfx            x3, x0, #1, #0x1f
    // 0x88fdc0: stur            x3, [fp, #-0x30]
    // 0x88fdc4: r6 = 0
    //     0x88fdc4: movz            x6, #0
    // 0x88fdc8: r5 = Null
    //     0x88fdc8: mov             x5, NULL
    // 0x88fdcc: ldur            x4, [fp, #-0x18]
    // 0x88fdd0: stur            x6, [fp, #-0x20]
    // 0x88fdd4: stur            x5, [fp, #-0x28]
    // 0x88fdd8: CheckStackOverflow
    //     0x88fdd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88fddc: cmp             SP, x16
    //     0x88fde0: b.ls            #0x88fed4
    // 0x88fde4: cmp             x6, x3
    // 0x88fde8: b.ge            #0x88fe94
    // 0x88fdec: LoadField: r0 = r4->field_b
    //     0x88fdec: ldur            w0, [x4, #0xb]
    // 0x88fdf0: r1 = LoadInt32Instr(r0)
    //     0x88fdf0: sbfx            x1, x0, #1, #0x1f
    // 0x88fdf4: mov             x0, x1
    // 0x88fdf8: mov             x1, x6
    // 0x88fdfc: cmp             x1, x0
    // 0x88fe00: b.hs            #0x88fedc
    // 0x88fe04: LoadField: r0 = r4->field_f
    //     0x88fe04: ldur            w0, [x4, #0xf]
    // 0x88fe08: DecompressPointer r0
    //     0x88fe08: add             x0, x0, HEAP, lsl #32
    // 0x88fe0c: ArrayLoad: r7 = r0[r6]  ; Unknown_4
    //     0x88fe0c: add             x16, x0, x6, lsl #2
    //     0x88fe10: ldur            w7, [x16, #0xf]
    // 0x88fe14: DecompressPointer r7
    //     0x88fe14: add             x7, x7, HEAP, lsl #32
    // 0x88fe18: stur            x7, [fp, #-0x10]
    // 0x88fe1c: r1 = <Element?>
    //     0x88fe1c: add             x1, PP, #0x46, lsl #12  ; [pp+0x46288] TypeArguments: <Element?>
    //     0x88fe20: ldr             x1, [x1, #0x288]
    // 0x88fe24: r0 = IndexedSlot()
    //     0x88fe24: bl              #0x86ef2c  ; AllocateIndexedSlotStub -> IndexedSlot<X0 bound Element?> (size=0x18)
    // 0x88fe28: mov             x1, x0
    // 0x88fe2c: ldur            x0, [fp, #-0x20]
    // 0x88fe30: StoreField: r1->field_f = r0
    //     0x88fe30: stur            x0, [x1, #0xf]
    // 0x88fe34: ldur            x2, [fp, #-0x28]
    // 0x88fe38: StoreField: r1->field_b = r2
    //     0x88fe38: stur            w2, [x1, #0xb]
    // 0x88fe3c: mov             x3, x1
    // 0x88fe40: ldur            x1, [fp, #-8]
    // 0x88fe44: ldur            x2, [fp, #-0x10]
    // 0x88fe48: r0 = inflateWidget()
    //     0x88fe48: bl              #0x86b1c8  ; [package:flutter/src/widgets/framework.dart] Element::inflateWidget
    // 0x88fe4c: ldur            x1, [fp, #-0x38]
    // 0x88fe50: mov             x3, x0
    // 0x88fe54: ldur            x2, [fp, #-0x20]
    // 0x88fe58: ArrayStore: r1[r2] = r0  ; List_4
    //     0x88fe58: add             x25, x1, x2, lsl #2
    //     0x88fe5c: add             x25, x25, #0xf
    //     0x88fe60: str             w0, [x25]
    //     0x88fe64: tbz             w0, #0, #0x88fe80
    //     0x88fe68: ldurb           w16, [x1, #-1]
    //     0x88fe6c: ldurb           w17, [x0, #-1]
    //     0x88fe70: and             x16, x17, x16, lsr #2
    //     0x88fe74: tst             x16, HEAP, lsr #32
    //     0x88fe78: b.eq            #0x88fe80
    //     0x88fe7c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x88fe80: add             x6, x2, #1
    // 0x88fe84: mov             x5, x3
    // 0x88fe88: ldur            x2, [fp, #-0x38]
    // 0x88fe8c: ldur            x3, [fp, #-0x30]
    // 0x88fe90: b               #0x88fdcc
    // 0x88fe94: ldur            x1, [fp, #-8]
    // 0x88fe98: ldur            x0, [fp, #-0x38]
    // 0x88fe9c: StoreField: r1->field_43 = r0
    //     0x88fe9c: stur            w0, [x1, #0x43]
    //     0x88fea0: ldurb           w16, [x1, #-1]
    //     0x88fea4: ldurb           w17, [x0, #-1]
    //     0x88fea8: and             x16, x17, x16, lsr #2
    //     0x88feac: tst             x16, HEAP, lsr #32
    //     0x88feb0: b.eq            #0x88feb8
    //     0x88feb4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x88feb8: r0 = Null
    //     0x88feb8: mov             x0, NULL
    // 0x88febc: LeaveFrame
    //     0x88febc: mov             SP, fp
    //     0x88fec0: ldp             fp, lr, [SP], #0x10
    // 0x88fec4: ret
    //     0x88fec4: ret             
    // 0x88fec8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88fec8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88fecc: b               #0x88fd00
    // 0x88fed0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x88fed0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x88fed4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88fed4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88fed8: b               #0x88fde4
    // 0x88fedc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x88fedc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ forgetChild(/* No info */) {
    // ** addr: 0x89ea40, size: 0xdc
    // 0x89ea40: EnterFrame
    //     0x89ea40: stp             fp, lr, [SP, #-0x10]!
    //     0x89ea44: mov             fp, SP
    // 0x89ea48: AllocStack(0x20)
    //     0x89ea48: sub             SP, SP, #0x20
    // 0x89ea4c: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x89ea4c: mov             x3, x1
    //     0x89ea50: mov             x0, x2
    //     0x89ea54: stur            x1, [fp, #-0x10]
    //     0x89ea58: stur            x2, [fp, #-0x18]
    // 0x89ea5c: CheckStackOverflow
    //     0x89ea5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89ea60: cmp             SP, x16
    //     0x89ea64: b.ls            #0x89eb10
    // 0x89ea68: LoadField: r4 = r3->field_47
    //     0x89ea68: ldur            w4, [x3, #0x47]
    // 0x89ea6c: DecompressPointer r4
    //     0x89ea6c: add             x4, x4, HEAP, lsl #32
    // 0x89ea70: stur            x4, [fp, #-8]
    // 0x89ea74: LoadField: r2 = r0->field_f
    //     0x89ea74: ldur            w2, [x0, #0xf]
    // 0x89ea78: DecompressPointer r2
    //     0x89ea78: add             x2, x2, HEAP, lsl #32
    // 0x89ea7c: mov             x1, x4
    // 0x89ea80: r0 = containsKey()
    //     0x89ea80: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x89ea84: tbnz            w0, #4, #0x89eaec
    // 0x89ea88: ldur            x2, [fp, #-0x18]
    // 0x89ea8c: LoadField: r3 = r2->field_f
    //     0x89ea8c: ldur            w3, [x2, #0xf]
    // 0x89ea90: DecompressPointer r3
    //     0x89ea90: add             x3, x3, HEAP, lsl #32
    // 0x89ea94: stur            x3, [fp, #-0x20]
    // 0x89ea98: cmp             w3, NULL
    // 0x89ea9c: b.eq            #0x89eb18
    // 0x89eaa0: mov             x0, x3
    // 0x89eaa4: r2 = Null
    //     0x89eaa4: mov             x2, NULL
    // 0x89eaa8: r1 = Null
    //     0x89eaa8: mov             x1, NULL
    // 0x89eaac: r4 = 60
    //     0x89eaac: movz            x4, #0x3c
    // 0x89eab0: branchIfSmi(r0, 0x89eabc)
    //     0x89eab0: tbz             w0, #0, #0x89eabc
    // 0x89eab4: r4 = LoadClassIdInstr(r0)
    //     0x89eab4: ldur            x4, [x0, #-1]
    //     0x89eab8: ubfx            x4, x4, #0xc, #0x14
    // 0x89eabc: r17 = 7094
    //     0x89eabc: movz            x17, #0x1bb6
    // 0x89eac0: cmp             x4, x17
    // 0x89eac4: b.eq            #0x89eadc
    // 0x89eac8: r8 = _CupertinoTextSelectionToolbarItemsSlot
    //     0x89eac8: add             x8, PP, #0x5c, lsl #12  ; [pp+0x5cf60] Type: _CupertinoTextSelectionToolbarItemsSlot
    //     0x89eacc: ldr             x8, [x8, #0xf60]
    // 0x89ead0: r3 = Null
    //     0x89ead0: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf68] Null
    //     0x89ead4: ldr             x3, [x3, #0xf68]
    // 0x89ead8: r0 = _CupertinoTextSelectionToolbarItemsSlot()
    //     0x89ead8: bl              #0x7aed18  ; IsType__CupertinoTextSelectionToolbarItemsSlot_Stub
    // 0x89eadc: ldur            x1, [fp, #-8]
    // 0x89eae0: ldur            x2, [fp, #-0x20]
    // 0x89eae4: r0 = remove()
    //     0x89eae4: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x89eae8: b               #0x89eb00
    // 0x89eaec: ldur            x0, [fp, #-0x10]
    // 0x89eaf0: ldur            x2, [fp, #-0x18]
    // 0x89eaf4: LoadField: r1 = r0->field_4b
    //     0x89eaf4: ldur            w1, [x0, #0x4b]
    // 0x89eaf8: DecompressPointer r1
    //     0x89eaf8: add             x1, x1, HEAP, lsl #32
    // 0x89eafc: r0 = add()
    //     0x89eafc: bl              #0xd5b078  ; [dart:collection] _HashSet::add
    // 0x89eb00: r0 = Null
    //     0x89eb00: mov             x0, NULL
    // 0x89eb04: LeaveFrame
    //     0x89eb04: mov             SP, fp
    //     0x89eb08: ldp             fp, lr, [SP], #0x10
    // 0x89eb0c: ret
    //     0x89eb0c: ret             
    // 0x89eb10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89eb10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89eb14: b               #0x89ea68
    // 0x89eb18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x89eb18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitChildren(/* No info */) {
    // ** addr: 0x89ec48, size: 0x158
    // 0x89ec48: EnterFrame
    //     0x89ec48: stp             fp, lr, [SP, #-0x10]!
    //     0x89ec4c: mov             fp, SP
    // 0x89ec50: AllocStack(0x48)
    //     0x89ec50: sub             SP, SP, #0x48
    // 0x89ec54: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x89ec54: mov             x4, x1
    //     0x89ec58: mov             x0, x2
    //     0x89ec5c: stur            x1, [fp, #-0x10]
    //     0x89ec60: stur            x2, [fp, #-0x18]
    // 0x89ec64: CheckStackOverflow
    //     0x89ec64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89ec68: cmp             SP, x16
    //     0x89ec6c: b.ls            #0x89ed84
    // 0x89ec70: LoadField: r5 = r4->field_47
    //     0x89ec70: ldur            w5, [x4, #0x47]
    // 0x89ec74: DecompressPointer r5
    //     0x89ec74: add             x5, x5, HEAP, lsl #32
    // 0x89ec78: stur            x5, [fp, #-8]
    // 0x89ec7c: LoadField: r2 = r5->field_7
    //     0x89ec7c: ldur            w2, [x5, #7]
    // 0x89ec80: DecompressPointer r2
    //     0x89ec80: add             x2, x2, HEAP, lsl #32
    // 0x89ec84: r1 = Null
    //     0x89ec84: mov             x1, NULL
    // 0x89ec88: r3 = <X1>
    //     0x89ec88: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x89ec8c: r0 = Null
    //     0x89ec8c: mov             x0, NULL
    // 0x89ec90: cmp             x2, x0
    // 0x89ec94: b.eq            #0x89eca4
    // 0x89ec98: r30 = InstantiateTypeArgumentsStub
    //     0x89ec98: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x89ec9c: LoadField: r30 = r30->field_7
    //     0x89ec9c: ldur            lr, [lr, #7]
    // 0x89eca0: blr             lr
    // 0x89eca4: mov             x1, x0
    // 0x89eca8: r0 = _CompactIterable()
    //     0x89eca8: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x89ecac: mov             x1, x0
    // 0x89ecb0: ldur            x0, [fp, #-8]
    // 0x89ecb4: StoreField: r1->field_b = r0
    //     0x89ecb4: stur            w0, [x1, #0xb]
    // 0x89ecb8: r0 = -1
    //     0x89ecb8: movn            x0, #0
    // 0x89ecbc: StoreField: r1->field_f = r0
    //     0x89ecbc: stur            x0, [x1, #0xf]
    // 0x89ecc0: r0 = 2
    //     0x89ecc0: movz            x0, #0x2
    // 0x89ecc4: ArrayStore: r1[0] = r0  ; List_8
    //     0x89ecc4: stur            x0, [x1, #0x17]
    // 0x89ecc8: ldur            x2, [fp, #-0x18]
    // 0x89eccc: r0 = forEach()
    //     0x89eccc: bl              #0x7e1920  ; [dart:core] Iterable::forEach
    // 0x89ecd0: ldur            x0, [fp, #-0x10]
    // 0x89ecd4: LoadField: r3 = r0->field_43
    //     0x89ecd4: ldur            w3, [x0, #0x43]
    // 0x89ecd8: DecompressPointer r3
    //     0x89ecd8: add             x3, x3, HEAP, lsl #32
    // 0x89ecdc: r16 = Sentinel
    //     0x89ecdc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x89ece0: cmp             w3, w16
    // 0x89ece4: b.eq            #0x89ed8c
    // 0x89ece8: stur            x3, [fp, #-0x38]
    // 0x89ecec: LoadField: r1 = r3->field_b
    //     0x89ecec: ldur            w1, [x3, #0xb]
    // 0x89ecf0: r4 = LoadInt32Instr(r1)
    //     0x89ecf0: sbfx            x4, x1, #1, #0x1f
    // 0x89ecf4: stur            x4, [fp, #-0x30]
    // 0x89ecf8: LoadField: r5 = r0->field_4b
    //     0x89ecf8: ldur            w5, [x0, #0x4b]
    // 0x89ecfc: DecompressPointer r5
    //     0x89ecfc: add             x5, x5, HEAP, lsl #32
    // 0x89ed00: stur            x5, [fp, #-0x28]
    // 0x89ed04: r0 = 0
    //     0x89ed04: movz            x0, #0
    // 0x89ed08: CheckStackOverflow
    //     0x89ed08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89ed0c: cmp             SP, x16
    //     0x89ed10: b.ls            #0x89ed98
    // 0x89ed14: cmp             x0, x4
    // 0x89ed18: b.ge            #0x89ed74
    // 0x89ed1c: ArrayLoad: r6 = r3[r0]  ; Unknown_4
    //     0x89ed1c: add             x16, x3, x0, lsl #2
    //     0x89ed20: ldur            w6, [x16, #0xf]
    // 0x89ed24: DecompressPointer r6
    //     0x89ed24: add             x6, x6, HEAP, lsl #32
    // 0x89ed28: stur            x6, [fp, #-8]
    // 0x89ed2c: add             x7, x0, #1
    // 0x89ed30: mov             x1, x5
    // 0x89ed34: mov             x2, x6
    // 0x89ed38: stur            x7, [fp, #-0x20]
    // 0x89ed3c: r0 = contains()
    //     0x89ed3c: bl              #0x7cd538  ; [dart:collection] _HashSet::contains
    // 0x89ed40: tbz             w0, #4, #0x89ed60
    // 0x89ed44: ldur            x16, [fp, #-0x18]
    // 0x89ed48: ldur            lr, [fp, #-8]
    // 0x89ed4c: stp             lr, x16, [SP]
    // 0x89ed50: ldur            x0, [fp, #-0x18]
    // 0x89ed54: ClosureCall
    //     0x89ed54: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x89ed58: ldur            x2, [x0, #0x1f]
    //     0x89ed5c: blr             x2
    // 0x89ed60: ldur            x0, [fp, #-0x20]
    // 0x89ed64: ldur            x5, [fp, #-0x28]
    // 0x89ed68: ldur            x3, [fp, #-0x38]
    // 0x89ed6c: ldur            x4, [fp, #-0x30]
    // 0x89ed70: b               #0x89ed08
    // 0x89ed74: r0 = Null
    //     0x89ed74: mov             x0, NULL
    // 0x89ed78: LeaveFrame
    //     0x89ed78: mov             SP, fp
    //     0x89ed7c: ldp             fp, lr, [SP], #0x10
    // 0x89ed80: ret
    //     0x89ed80: ret             
    // 0x89ed84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89ed84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89ed88: b               #0x89ec70
    // 0x89ed8c: r9 = _children
    //     0x89ed8c: add             x9, PP, #0x5c, lsl #12  ; [pp+0x5cf48] Field <_CupertinoTextSelectionToolbarItemsElement@247408280._children@247408280>: late (offset: 0x44)
    //     0x89ed90: ldr             x9, [x9, #0xf48]
    // 0x89ed94: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x89ed94: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x89ed98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89ed98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89ed9c: b               #0x89ed14
  }
  _ _CupertinoTextSelectionToolbarItemsElement(/* No info */) {
    // ** addr: 0xbbdec4, size: 0x118
    // 0xbbdec4: EnterFrame
    //     0xbbdec4: stp             fp, lr, [SP, #-0x10]!
    //     0xbbdec8: mov             fp, SP
    // 0xbbdecc: AllocStack(0x28)
    //     0xbbdecc: sub             SP, SP, #0x28
    // 0xbbded0: r0 = Sentinel
    //     0xbbded0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbded4: stur            x1, [fp, #-8]
    // 0xbbded8: mov             x16, x2
    // 0xbbdedc: mov             x2, x1
    // 0xbbdee0: mov             x1, x16
    // 0xbbdee4: stur            x1, [fp, #-0x10]
    // 0xbbdee8: CheckStackOverflow
    //     0xbbdee8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbdeec: cmp             SP, x16
    //     0xbbdef0: b.ls            #0xbbdfd4
    // 0xbbdef4: StoreField: r2->field_43 = r0
    //     0xbbdef4: stur            w0, [x2, #0x43]
    // 0xbbdef8: r16 = <_CupertinoTextSelectionToolbarItemsSlot, Element>
    //     0xbbdef8: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5a608] TypeArguments: <_CupertinoTextSelectionToolbarItemsSlot, Element>
    //     0xbbdefc: ldr             x16, [x16, #0x608]
    // 0xbbdf00: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xbbdf04: stp             lr, x16, [SP]
    // 0xbbdf08: r0 = Map._fromLiteral()
    //     0xbbdf08: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xbbdf0c: ldur            x2, [fp, #-8]
    // 0xbbdf10: StoreField: r2->field_47 = r0
    //     0xbbdf10: stur            w0, [x2, #0x47]
    //     0xbbdf14: ldurb           w16, [x2, #-1]
    //     0xbbdf18: ldurb           w17, [x0, #-1]
    //     0xbbdf1c: and             x16, x17, x16, lsr #2
    //     0xbbdf20: tst             x16, HEAP, lsr #32
    //     0xbbdf24: b.eq            #0xbbdf2c
    //     0xbbdf28: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbbdf2c: r1 = <Element>
    //     0xbbdf2c: ldr             x1, [PP, #0x2158]  ; [pp+0x2158] TypeArguments: <Element>
    // 0xbbdf30: r0 = _HashSet()
    //     0xbbdf30: bl              #0x653810  ; Allocate_HashSetStub -> _HashSet<X0> (size=0x20)
    // 0xbbdf34: stur            x0, [fp, #-0x18]
    // 0xbbdf38: StoreField: r0->field_f = rZR
    //     0xbbdf38: stur            xzr, [x0, #0xf]
    // 0xbbdf3c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xbbdf3c: stur            xzr, [x0, #0x17]
    // 0xbbdf40: r1 = <_HashSetEntry<Element>?>
    //     0xbbdf40: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a358] TypeArguments: <_HashSetEntry<Element>?>
    //     0xbbdf44: ldr             x1, [x1, #0x358]
    // 0xbbdf48: r2 = 16
    //     0xbbdf48: movz            x2, #0x10
    // 0xbbdf4c: r0 = AllocateArray()
    //     0xbbdf4c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbbdf50: mov             x1, x0
    // 0xbbdf54: ldur            x0, [fp, #-0x18]
    // 0xbbdf58: StoreField: r0->field_b = r1
    //     0xbbdf58: stur            w1, [x0, #0xb]
    // 0xbbdf5c: ldur            x1, [fp, #-8]
    // 0xbbdf60: StoreField: r1->field_4b = r0
    //     0xbbdf60: stur            w0, [x1, #0x4b]
    //     0xbbdf64: ldurb           w16, [x1, #-1]
    //     0xbbdf68: ldurb           w17, [x0, #-1]
    //     0xbbdf6c: and             x16, x17, x16, lsr #2
    //     0xbbdf70: tst             x16, HEAP, lsr #32
    //     0xbbdf74: b.eq            #0xbbdf7c
    //     0xbbdf78: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbbdf7c: r2 = Sentinel
    //     0xbbdf7c: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbdf80: StoreField: r1->field_13 = r2
    //     0xbbdf80: stur            w2, [x1, #0x13]
    // 0xbbdf84: r2 = Instance__ElementLifecycle
    //     0xbbdf84: add             x2, PP, #0xd, lsl #12  ; [pp+0xda00] Obj!_ElementLifecycle@e343c1
    //     0xbbdf88: ldr             x2, [x2, #0xa00]
    // 0xbbdf8c: StoreField: r1->field_23 = r2
    //     0xbbdf8c: stur            w2, [x1, #0x23]
    // 0xbbdf90: r2 = false
    //     0xbbdf90: add             x2, NULL, #0x30  ; false
    // 0xbbdf94: StoreField: r1->field_2f = r2
    //     0xbbdf94: stur            w2, [x1, #0x2f]
    // 0xbbdf98: r3 = true
    //     0xbbdf98: add             x3, NULL, #0x20  ; true
    // 0xbbdf9c: StoreField: r1->field_33 = r3
    //     0xbbdf9c: stur            w3, [x1, #0x33]
    // 0xbbdfa0: StoreField: r1->field_37 = r2
    //     0xbbdfa0: stur            w2, [x1, #0x37]
    // 0xbbdfa4: ldur            x0, [fp, #-0x10]
    // 0xbbdfa8: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbdfa8: stur            w0, [x1, #0x17]
    //     0xbbdfac: ldurb           w16, [x1, #-1]
    //     0xbbdfb0: ldurb           w17, [x0, #-1]
    //     0xbbdfb4: and             x16, x17, x16, lsr #2
    //     0xbbdfb8: tst             x16, HEAP, lsr #32
    //     0xbbdfbc: b.eq            #0xbbdfc4
    //     0xbbdfc0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbbdfc4: r0 = Null
    //     0xbbdfc4: mov             x0, NULL
    // 0xbbdfc8: LeaveFrame
    //     0xbbdfc8: mov             SP, fp
    //     0xbbdfcc: ldp             fp, lr, [SP], #0x10
    // 0xbbdfd0: ret
    //     0xbbdfd0: ret             
    // 0xbbdfd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbdfd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbdfd8: b               #0xbbdef4
  }
  get _ renderObject(/* No info */) {
    // ** addr: 0xd151f8, size: 0x64
    // 0xd151f8: EnterFrame
    //     0xd151f8: stp             fp, lr, [SP, #-0x10]!
    //     0xd151fc: mov             fp, SP
    // 0xd15200: AllocStack(0x8)
    //     0xd15200: sub             SP, SP, #8
    // 0xd15204: LoadField: r3 = r1->field_3b
    //     0xd15204: ldur            w3, [x1, #0x3b]
    // 0xd15208: DecompressPointer r3
    //     0xd15208: add             x3, x3, HEAP, lsl #32
    // 0xd1520c: stur            x3, [fp, #-8]
    // 0xd15210: cmp             w3, NULL
    // 0xd15214: b.eq            #0xd15258
    // 0xd15218: mov             x0, x3
    // 0xd1521c: r2 = Null
    //     0xd1521c: mov             x2, NULL
    // 0xd15220: r1 = Null
    //     0xd15220: mov             x1, NULL
    // 0xd15224: r4 = LoadClassIdInstr(r0)
    //     0xd15224: ldur            x4, [x0, #-1]
    //     0xd15228: ubfx            x4, x4, #0xc, #0x14
    // 0xd1522c: cmp             x4, #0xbfc
    // 0xd15230: b.eq            #0xd15248
    // 0xd15234: r8 = _RenderCupertinoTextSelectionToolbarItems
    //     0xd15234: add             x8, PP, #0x5a, lsl #12  ; [pp+0x5a610] Type: _RenderCupertinoTextSelectionToolbarItems
    //     0xd15238: ldr             x8, [x8, #0x610]
    // 0xd1523c: r3 = Null
    //     0xd1523c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d048] Null
    //     0xd15240: ldr             x3, [x3, #0x48]
    // 0xd15244: r0 = DefaultTypeTest()
    //     0xd15244: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xd15248: ldur            x0, [fp, #-8]
    // 0xd1524c: LeaveFrame
    //     0xd1524c: mov             SP, fp
    //     0xd15250: ldp             fp, lr, [SP], #0x10
    // 0xd15254: ret
    //     0xd15254: ret             
    // 0xd15258: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd15258: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4480, size: 0x2c, field offset: 0xc
class _CupertinoTextSelectionToolbarItems extends RenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x86086c, size: 0x68
    // 0x86086c: EnterFrame
    //     0x86086c: stp             fp, lr, [SP, #-0x10]!
    //     0x860870: mov             fp, SP
    // 0x860874: AllocStack(0x18)
    //     0x860874: sub             SP, SP, #0x18
    // 0x860878: CheckStackOverflow
    //     0x860878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86087c: cmp             SP, x16
    //     0x860880: b.ls            #0x8608cc
    // 0x860884: LoadField: r2 = r1->field_13
    //     0x860884: ldur            w2, [x1, #0x13]
    // 0x860888: DecompressPointer r2
    //     0x860888: add             x2, x2, HEAP, lsl #32
    // 0x86088c: stur            x2, [fp, #-0x10]
    // 0x860890: ArrayLoad: d0 = r1[0]  ; List_8
    //     0x860890: ldur            d0, [x1, #0x17]
    // 0x860894: stur            d0, [fp, #-0x18]
    // 0x860898: LoadField: r3 = r1->field_23
    //     0x860898: ldur            x3, [x1, #0x23]
    // 0x86089c: stur            x3, [fp, #-8]
    // 0x8608a0: r0 = _RenderCupertinoTextSelectionToolbarItems()
    //     0x8608a0: bl              #0x8609bc  ; Allocate_RenderCupertinoTextSelectionToolbarItemsStub -> _RenderCupertinoTextSelectionToolbarItems (size=0x90)
    // 0x8608a4: mov             x1, x0
    // 0x8608a8: ldur            x2, [fp, #-0x10]
    // 0x8608ac: ldur            d0, [fp, #-0x18]
    // 0x8608b0: ldur            x3, [fp, #-8]
    // 0x8608b4: stur            x0, [fp, #-0x10]
    // 0x8608b8: r0 = _RenderCupertinoTextSelectionToolbarItems()
    //     0x8608b8: bl              #0x8608d4  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::_RenderCupertinoTextSelectionToolbarItems
    // 0x8608bc: ldur            x0, [fp, #-0x10]
    // 0x8608c0: LeaveFrame
    //     0x8608c0: mov             SP, fp
    //     0x8608c4: ldp             fp, lr, [SP], #0x10
    // 0x8608c8: ret
    //     0x8608c8: ret             
    // 0x8608cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8608cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8608d0: b               #0x860884
  }
  _ createElement(/* No info */) {
    // ** addr: 0xbbde78, size: 0x4c
    // 0xbbde78: EnterFrame
    //     0xbbde78: stp             fp, lr, [SP, #-0x10]!
    //     0xbbde7c: mov             fp, SP
    // 0xbbde80: AllocStack(0x8)
    //     0xbbde80: sub             SP, SP, #8
    // 0xbbde84: SetupParameters(_CupertinoTextSelectionToolbarItems this /* r1 => r2, fp-0x8 */)
    //     0xbbde84: mov             x2, x1
    //     0xbbde88: stur            x1, [fp, #-8]
    // 0xbbde8c: CheckStackOverflow
    //     0xbbde8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbde90: cmp             SP, x16
    //     0xbbde94: b.ls            #0xbbdebc
    // 0xbbde98: r0 = _CupertinoTextSelectionToolbarItemsElement()
    //     0xbbde98: bl              #0xbbdfdc  ; Allocate_CupertinoTextSelectionToolbarItemsElementStub -> _CupertinoTextSelectionToolbarItemsElement (size=0x50)
    // 0xbbde9c: mov             x1, x0
    // 0xbbdea0: ldur            x2, [fp, #-8]
    // 0xbbdea4: stur            x0, [fp, #-8]
    // 0xbbdea8: r0 = _CupertinoTextSelectionToolbarItemsElement()
    //     0xbbdea8: bl              #0xbbdec4  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_CupertinoTextSelectionToolbarItemsElement
    // 0xbbdeac: ldur            x0, [fp, #-8]
    // 0xbbdeb0: LeaveFrame
    //     0xbbdeb0: mov             SP, fp
    //     0xbbdeb4: ldp             fp, lr, [SP], #0x10
    // 0xbbdeb8: ret
    //     0xbbdeb8: ret             
    // 0xbbdebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbdebc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbdec0: b               #0xbbde98
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xc728bc, size: 0xa8
    // 0xc728bc: EnterFrame
    //     0xc728bc: stp             fp, lr, [SP, #-0x10]!
    //     0xc728c0: mov             fp, SP
    // 0xc728c4: AllocStack(0x10)
    //     0xc728c4: sub             SP, SP, #0x10
    // 0xc728c8: SetupParameters(_CupertinoTextSelectionToolbarItems this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xc728c8: mov             x4, x1
    //     0xc728cc: stur            x1, [fp, #-8]
    //     0xc728d0: stur            x3, [fp, #-0x10]
    // 0xc728d4: CheckStackOverflow
    //     0xc728d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc728d8: cmp             SP, x16
    //     0xc728dc: b.ls            #0xc7295c
    // 0xc728e0: mov             x0, x3
    // 0xc728e4: r2 = Null
    //     0xc728e4: mov             x2, NULL
    // 0xc728e8: r1 = Null
    //     0xc728e8: mov             x1, NULL
    // 0xc728ec: r4 = 60
    //     0xc728ec: movz            x4, #0x3c
    // 0xc728f0: branchIfSmi(r0, 0xc728fc)
    //     0xc728f0: tbz             w0, #0, #0xc728fc
    // 0xc728f4: r4 = LoadClassIdInstr(r0)
    //     0xc728f4: ldur            x4, [x0, #-1]
    //     0xc728f8: ubfx            x4, x4, #0xc, #0x14
    // 0xc728fc: cmp             x4, #0xbfc
    // 0xc72900: b.eq            #0xc72918
    // 0xc72904: r8 = _RenderCupertinoTextSelectionToolbarItems
    //     0xc72904: add             x8, PP, #0x5a, lsl #12  ; [pp+0x5a610] Type: _RenderCupertinoTextSelectionToolbarItems
    //     0xc72908: ldr             x8, [x8, #0x610]
    // 0xc7290c: r3 = Null
    //     0xc7290c: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5a618] Null
    //     0xc72910: ldr             x3, [x3, #0x618]
    // 0xc72914: r0 = DefaultTypeTest()
    //     0xc72914: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc72918: ldur            x0, [fp, #-8]
    // 0xc7291c: LoadField: r2 = r0->field_23
    //     0xc7291c: ldur            x2, [x0, #0x23]
    // 0xc72920: ldur            x1, [fp, #-0x10]
    // 0xc72924: r0 = page=()
    //     0xc72924: bl              #0xc72a3c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::page=
    // 0xc72928: ldur            x0, [fp, #-8]
    // 0xc7292c: LoadField: r2 = r0->field_13
    //     0xc7292c: ldur            w2, [x0, #0x13]
    // 0xc72930: DecompressPointer r2
    //     0xc72930: add             x2, x2, HEAP, lsl #32
    // 0xc72934: ldur            x1, [fp, #-0x10]
    // 0xc72938: r0 = dividerColor=()
    //     0xc72938: bl              #0xc729b4  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::dividerColor=
    // 0xc7293c: ldur            x0, [fp, #-8]
    // 0xc72940: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xc72940: ldur            d0, [x0, #0x17]
    // 0xc72944: ldur            x1, [fp, #-0x10]
    // 0xc72948: r0 = dividerWidth=()
    //     0xc72948: bl              #0xc72964  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::dividerWidth=
    // 0xc7294c: r0 = Null
    //     0xc7294c: mov             x0, NULL
    // 0xc72950: LeaveFrame
    //     0xc72950: mov             SP, fp
    //     0xc72954: ldp             fp, lr, [SP], #0x10
    // 0xc72958: ret
    //     0xc72958: ret             
    // 0xc7295c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7295c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc72960: b               #0xc728e0
  }
}

// class id: 4561, size: 0x1c, field offset: 0x10
//   const constructor, 
class _CupertinoTextSelectionToolbarShape extends SingleChildRenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x859e18, size: 0x70
    // 0x859e18: EnterFrame
    //     0x859e18: stp             fp, lr, [SP, #-0x10]!
    //     0x859e1c: mov             fp, SP
    // 0x859e20: AllocStack(0x18)
    //     0x859e20: sub             SP, SP, #0x18
    // 0x859e24: CheckStackOverflow
    //     0x859e24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x859e28: cmp             SP, x16
    //     0x859e2c: b.ls            #0x859e80
    // 0x859e30: LoadField: r2 = r1->field_f
    //     0x859e30: ldur            w2, [x1, #0xf]
    // 0x859e34: DecompressPointer r2
    //     0x859e34: add             x2, x2, HEAP, lsl #32
    // 0x859e38: stur            x2, [fp, #-0x18]
    // 0x859e3c: LoadField: r3 = r1->field_13
    //     0x859e3c: ldur            w3, [x1, #0x13]
    // 0x859e40: DecompressPointer r3
    //     0x859e40: add             x3, x3, HEAP, lsl #32
    // 0x859e44: stur            x3, [fp, #-0x10]
    // 0x859e48: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x859e48: ldur            w5, [x1, #0x17]
    // 0x859e4c: DecompressPointer r5
    //     0x859e4c: add             x5, x5, HEAP, lsl #32
    // 0x859e50: stur            x5, [fp, #-8]
    // 0x859e54: r0 = _RenderCupertinoTextSelectionToolbarShape()
    //     0x859e54: bl              #0x859fa0  ; Allocate_RenderCupertinoTextSelectionToolbarShapeStub -> _RenderCupertinoTextSelectionToolbarShape (size=0x6c)
    // 0x859e58: mov             x1, x0
    // 0x859e5c: ldur            x2, [fp, #-0x18]
    // 0x859e60: ldur            x3, [fp, #-0x10]
    // 0x859e64: ldur            x5, [fp, #-8]
    // 0x859e68: stur            x0, [fp, #-8]
    // 0x859e6c: r0 = _RenderCupertinoTextSelectionToolbarShape()
    //     0x859e6c: bl              #0x859e88  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_RenderCupertinoTextSelectionToolbarShape
    // 0x859e70: ldur            x0, [fp, #-8]
    // 0x859e74: LeaveFrame
    //     0x859e74: mov             SP, fp
    //     0x859e78: ldp             fp, lr, [SP], #0x10
    // 0x859e7c: ret
    //     0x859e7c: ret             
    // 0x859e80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x859e80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x859e84: b               #0x859e30
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xc6cf50, size: 0xb0
    // 0xc6cf50: EnterFrame
    //     0xc6cf50: stp             fp, lr, [SP, #-0x10]!
    //     0xc6cf54: mov             fp, SP
    // 0xc6cf58: AllocStack(0x10)
    //     0xc6cf58: sub             SP, SP, #0x10
    // 0xc6cf5c: SetupParameters(_CupertinoTextSelectionToolbarShape this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xc6cf5c: mov             x4, x1
    //     0xc6cf60: stur            x1, [fp, #-8]
    //     0xc6cf64: stur            x3, [fp, #-0x10]
    // 0xc6cf68: CheckStackOverflow
    //     0xc6cf68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6cf6c: cmp             SP, x16
    //     0xc6cf70: b.ls            #0xc6cff8
    // 0xc6cf74: mov             x0, x3
    // 0xc6cf78: r2 = Null
    //     0xc6cf78: mov             x2, NULL
    // 0xc6cf7c: r1 = Null
    //     0xc6cf7c: mov             x1, NULL
    // 0xc6cf80: r4 = 60
    //     0xc6cf80: movz            x4, #0x3c
    // 0xc6cf84: branchIfSmi(r0, 0xc6cf90)
    //     0xc6cf84: tbz             w0, #0, #0xc6cf90
    // 0xc6cf88: r4 = LoadClassIdInstr(r0)
    //     0xc6cf88: ldur            x4, [x0, #-1]
    //     0xc6cf8c: ubfx            x4, x4, #0xc, #0x14
    // 0xc6cf90: cmp             x4, #0xc14
    // 0xc6cf94: b.eq            #0xc6cfac
    // 0xc6cf98: r8 = _RenderCupertinoTextSelectionToolbarShape
    //     0xc6cf98: add             x8, PP, #0x46, lsl #12  ; [pp+0x46758] Type: _RenderCupertinoTextSelectionToolbarShape
    //     0xc6cf9c: ldr             x8, [x8, #0x758]
    // 0xc6cfa0: r3 = Null
    //     0xc6cfa0: add             x3, PP, #0x46, lsl #12  ; [pp+0x46760] Null
    //     0xc6cfa4: ldr             x3, [x3, #0x760]
    // 0xc6cfa8: r0 = DefaultTypeTest()
    //     0xc6cfa8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc6cfac: ldur            x0, [fp, #-8]
    // 0xc6cfb0: LoadField: r2 = r0->field_f
    //     0xc6cfb0: ldur            w2, [x0, #0xf]
    // 0xc6cfb4: DecompressPointer r2
    //     0xc6cfb4: add             x2, x2, HEAP, lsl #32
    // 0xc6cfb8: ldur            x1, [fp, #-0x10]
    // 0xc6cfbc: r0 = anchorAbove=()
    //     0xc6cfbc: bl              #0xc6d12c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::anchorAbove=
    // 0xc6cfc0: ldur            x0, [fp, #-8]
    // 0xc6cfc4: LoadField: r2 = r0->field_13
    //     0xc6cfc4: ldur            w2, [x0, #0x13]
    // 0xc6cfc8: DecompressPointer r2
    //     0xc6cfc8: add             x2, x2, HEAP, lsl #32
    // 0xc6cfcc: ldur            x1, [fp, #-0x10]
    // 0xc6cfd0: r0 = anchorBelow=()
    //     0xc6cfd0: bl              #0xc6d0a4  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::anchorBelow=
    // 0xc6cfd4: ldur            x0, [fp, #-8]
    // 0xc6cfd8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xc6cfd8: ldur            w2, [x0, #0x17]
    // 0xc6cfdc: DecompressPointer r2
    //     0xc6cfdc: add             x2, x2, HEAP, lsl #32
    // 0xc6cfe0: ldur            x1, [fp, #-0x10]
    // 0xc6cfe4: r0 = shadowColor=()
    //     0xc6cfe4: bl              #0xc6d000  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::shadowColor=
    // 0xc6cfe8: r0 = Null
    //     0xc6cfe8: mov             x0, NULL
    // 0xc6cfec: LeaveFrame
    //     0xc6cfec: mov             SP, fp
    //     0xc6cff0: ldp             fp, lr, [SP], #0x10
    // 0xc6cff4: ret
    //     0xc6cff4: ret             
    // 0xc6cff8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6cff8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6cffc: b               #0xc6cf74
  }
}

// class id: 4882, size: 0x1c, field offset: 0xc
//   const constructor, 
class _CupertinoTextSelectionToolbarContent extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa8f754, size: 0x50
    // 0xa8f754: EnterFrame
    //     0xa8f754: stp             fp, lr, [SP, #-0x10]!
    //     0xa8f758: mov             fp, SP
    // 0xa8f75c: AllocStack(0x8)
    //     0xa8f75c: sub             SP, SP, #8
    // 0xa8f760: SetupParameters(_CupertinoTextSelectionToolbarContent this /* r1 => r0 */)
    //     0xa8f760: mov             x0, x1
    // 0xa8f764: r1 = <_CupertinoTextSelectionToolbarContent>
    //     0xa8f764: add             x1, PP, #0x50, lsl #12  ; [pp+0x50868] TypeArguments: <_CupertinoTextSelectionToolbarContent>
    //     0xa8f768: ldr             x1, [x1, #0x868]
    // 0xa8f76c: r0 = _CupertinoTextSelectionToolbarContentState()
    //     0xa8f76c: bl              #0xa8f7a4  ; Allocate_CupertinoTextSelectionToolbarContentStateStub -> _CupertinoTextSelectionToolbarContentState (size=0x30)
    // 0xa8f770: mov             x2, x0
    // 0xa8f774: r0 = Sentinel
    //     0xa8f774: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa8f778: stur            x2, [fp, #-8]
    // 0xa8f77c: StoreField: r2->field_1b = r0
    //     0xa8f77c: stur            w0, [x2, #0x1b]
    // 0xa8f780: StoreField: r2->field_23 = rZR
    //     0xa8f780: stur            xzr, [x2, #0x23]
    // 0xa8f784: r1 = <State<StatefulWidget>>
    //     0xa8f784: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0xa8f788: r0 = LabeledGlobalKey()
    //     0xa8f788: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xa8f78c: mov             x1, x0
    // 0xa8f790: ldur            x0, [fp, #-8]
    // 0xa8f794: StoreField: r0->field_2b = r1
    //     0xa8f794: stur            w1, [x0, #0x2b]
    // 0xa8f798: LeaveFrame
    //     0xa8f798: mov             SP, fp
    //     0xa8f79c: ldp             fp, lr, [SP], #0x10
    // 0xa8f7a0: ret
    //     0xa8f7a0: ret             
  }
}

// class id: 5417, size: 0x1c, field offset: 0xc
//   const constructor, 
class CupertinoTextSelectionToolbar extends StatelessWidget {

  [closure] static Widget _defaultToolbarBuilder(dynamic, BuildContext, Offset, Offset, Widget) {
    // ** addr: 0x9e2edc, size: 0x3c
    // 0x9e2edc: EnterFrame
    //     0x9e2edc: stp             fp, lr, [SP, #-0x10]!
    //     0x9e2ee0: mov             fp, SP
    // 0x9e2ee4: CheckStackOverflow
    //     0x9e2ee4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e2ee8: cmp             SP, x16
    //     0x9e2eec: b.ls            #0x9e2f10
    // 0x9e2ef0: ldr             x1, [fp, #0x28]
    // 0x9e2ef4: ldr             x2, [fp, #0x20]
    // 0x9e2ef8: ldr             x3, [fp, #0x18]
    // 0x9e2efc: ldr             x5, [fp, #0x10]
    // 0x9e2f00: r0 = _defaultToolbarBuilder()
    //     0x9e2f00: bl              #0x9e2f18  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] CupertinoTextSelectionToolbar::_defaultToolbarBuilder
    // 0x9e2f04: LeaveFrame
    //     0x9e2f04: mov             SP, fp
    //     0x9e2f08: ldp             fp, lr, [SP], #0x10
    // 0x9e2f0c: ret
    //     0x9e2f0c: ret             
    // 0x9e2f10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e2f10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e2f14: b               #0x9e2ef0
  }
  static _ _defaultToolbarBuilder(/* No info */) {
    // ** addr: 0x9e2f18, size: 0xd0
    // 0x9e2f18: EnterFrame
    //     0x9e2f18: stp             fp, lr, [SP, #-0x10]!
    //     0x9e2f1c: mov             fp, SP
    // 0x9e2f20: AllocStack(0x30)
    //     0x9e2f20: sub             SP, SP, #0x30
    // 0x9e2f24: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x9e2f24: mov             x0, x1
    //     0x9e2f28: stur            x1, [fp, #-8]
    //     0x9e2f2c: stur            x2, [fp, #-0x10]
    //     0x9e2f30: stur            x3, [fp, #-0x18]
    //     0x9e2f34: stur            x5, [fp, #-0x20]
    // 0x9e2f38: CheckStackOverflow
    //     0x9e2f38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e2f3c: cmp             SP, x16
    //     0x9e2f40: b.ls            #0x9e2fe0
    // 0x9e2f44: mov             x1, x0
    // 0x9e2f48: r0 = brightnessOf()
    //     0x9e2f48: bl              #0x9e3000  ; [package:flutter/src/cupertino/theme.dart] CupertinoTheme::brightnessOf
    // 0x9e2f4c: r16 = Instance_Brightness
    //     0x9e2f4c: ldr             x16, [PP, #0x5420]  ; [pp+0x5420] Obj!Brightness@e39121
    // 0x9e2f50: cmp             w0, w16
    // 0x9e2f54: b.ne            #0x9e2f6c
    // 0x9e2f58: r1 = Instance_Color
    //     0x9e2f58: ldr             x1, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0x9e2f5c: d0 = 0.200000
    //     0x9e2f5c: ldr             d0, [PP, #0x5b18]  ; [pp+0x5b18] IMM: double(0.2) from 0x3fc999999999999a
    // 0x9e2f60: r0 = withOpacity()
    //     0x9e2f60: bl              #0xd72290  ; [dart:ui] Color::withOpacity
    // 0x9e2f64: mov             x5, x0
    // 0x9e2f68: b               #0x9e2f70
    // 0x9e2f6c: r5 = Null
    //     0x9e2f6c: mov             x5, NULL
    // 0x9e2f70: ldur            x4, [fp, #-0x10]
    // 0x9e2f74: ldur            x3, [fp, #-0x18]
    // 0x9e2f78: ldur            x0, [fp, #-0x20]
    // 0x9e2f7c: ldur            x2, [fp, #-8]
    // 0x9e2f80: stur            x5, [fp, #-0x28]
    // 0x9e2f84: r1 = Instance_CupertinoDynamicColor
    //     0x9e2f84: add             x1, PP, #0x39, lsl #12  ; [pp+0x39a58] Obj!CupertinoDynamicColor@e1daa1
    //     0x9e2f88: ldr             x1, [x1, #0xa58]
    // 0x9e2f8c: r0 = resolveFrom()
    //     0x9e2f8c: bl              #0x853be8  ; [package:flutter/src/cupertino/colors.dart] CupertinoDynamicColor::resolveFrom
    // 0x9e2f90: stur            x0, [fp, #-8]
    // 0x9e2f94: r0 = ColoredBox()
    //     0x9e2f94: bl              #0x9e2ff4  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0x9e2f98: mov             x1, x0
    // 0x9e2f9c: ldur            x0, [fp, #-8]
    // 0x9e2fa0: stur            x1, [fp, #-0x30]
    // 0x9e2fa4: StoreField: r1->field_f = r0
    //     0x9e2fa4: stur            w0, [x1, #0xf]
    // 0x9e2fa8: ldur            x0, [fp, #-0x20]
    // 0x9e2fac: StoreField: r1->field_b = r0
    //     0x9e2fac: stur            w0, [x1, #0xb]
    // 0x9e2fb0: r0 = _CupertinoTextSelectionToolbarShape()
    //     0x9e2fb0: bl              #0x9e2fe8  ; Allocate_CupertinoTextSelectionToolbarShapeStub -> _CupertinoTextSelectionToolbarShape (size=0x1c)
    // 0x9e2fb4: ldur            x1, [fp, #-0x10]
    // 0x9e2fb8: StoreField: r0->field_f = r1
    //     0x9e2fb8: stur            w1, [x0, #0xf]
    // 0x9e2fbc: ldur            x1, [fp, #-0x18]
    // 0x9e2fc0: StoreField: r0->field_13 = r1
    //     0x9e2fc0: stur            w1, [x0, #0x13]
    // 0x9e2fc4: ldur            x1, [fp, #-0x28]
    // 0x9e2fc8: ArrayStore: r0[0] = r1  ; List_4
    //     0x9e2fc8: stur            w1, [x0, #0x17]
    // 0x9e2fcc: ldur            x1, [fp, #-0x30]
    // 0x9e2fd0: StoreField: r0->field_b = r1
    //     0x9e2fd0: stur            w1, [x0, #0xb]
    // 0x9e2fd4: LeaveFrame
    //     0x9e2fd4: mov             SP, fp
    //     0x9e2fd8: ldp             fp, lr, [SP], #0x10
    // 0x9e2fdc: ret
    //     0x9e2fdc: ret             
    // 0x9e2fe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e2fe0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e2fe4: b               #0x9e2f44
  }
  _ build(/* No info */) {
    // ** addr: 0xa978a4, size: 0x234
    // 0xa978a4: EnterFrame
    //     0xa978a4: stp             fp, lr, [SP, #-0x10]!
    //     0xa978a8: mov             fp, SP
    // 0xa978ac: AllocStack(0x58)
    //     0xa978ac: sub             SP, SP, #0x58
    // 0xa978b0: SetupParameters(CupertinoTextSelectionToolbar this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa978b0: mov             x0, x2
    //     0xa978b4: stur            x2, [fp, #-0x10]
    //     0xa978b8: mov             x2, x1
    //     0xa978bc: stur            x1, [fp, #-8]
    // 0xa978c0: CheckStackOverflow
    //     0xa978c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa978c4: cmp             SP, x16
    //     0xa978c8: b.ls            #0xa97ad0
    // 0xa978cc: mov             x1, x0
    // 0xa978d0: r0 = paddingOf()
    //     0xa978d0: bl              #0x9daabc  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::paddingOf
    // 0xa978d4: stur            x0, [fp, #-0x18]
    // 0xa978d8: LoadField: d0 = r0->field_f
    //     0xa978d8: ldur            d0, [x0, #0xf]
    // 0xa978dc: d1 = 8.000000
    //     0xa978dc: fmov            d1, #8.00000000
    // 0xa978e0: fadd            d2, d0, d1
    // 0xa978e4: stur            d2, [fp, #-0x40]
    // 0xa978e8: LoadField: d0 = r0->field_7
    //     0xa978e8: ldur            d0, [x0, #7]
    // 0xa978ec: d3 = 26.000000
    //     0xa978ec: fmov            d3, #26.00000000
    // 0xa978f0: fadd            d4, d0, d3
    // 0xa978f4: ldur            x1, [fp, #-0x10]
    // 0xa978f8: stur            d4, [fp, #-0x38]
    // 0xa978fc: r0 = sizeOf()
    //     0xa978fc: bl              #0x6a7e2c  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::sizeOf
    // 0xa97900: LoadField: d0 = r0->field_7
    //     0xa97900: ldur            d0, [x0, #7]
    // 0xa97904: ldur            x0, [fp, #-0x18]
    // 0xa97908: ArrayLoad: d1 = r0[0]  ; List_8
    //     0xa97908: ldur            d1, [x0, #0x17]
    // 0xa9790c: fsub            d2, d0, d1
    // 0xa97910: d0 = 26.000000
    //     0xa97910: fmov            d0, #26.00000000
    // 0xa97914: fsub            d1, d2, d0
    // 0xa97918: ldur            x0, [fp, #-8]
    // 0xa9791c: stur            d1, [fp, #-0x58]
    // 0xa97920: LoadField: r1 = r0->field_b
    //     0xa97920: ldur            w1, [x0, #0xb]
    // 0xa97924: DecompressPointer r1
    //     0xa97924: add             x1, x1, HEAP, lsl #32
    // 0xa97928: LoadField: d0 = r1->field_7
    //     0xa97928: ldur            d0, [x1, #7]
    // 0xa9792c: ldur            d2, [fp, #-0x38]
    // 0xa97930: fcmp            d2, d0
    // 0xa97934: b.le            #0xa97940
    // 0xa97938: mov             v4.16b, v2.16b
    // 0xa9793c: b               #0xa97964
    // 0xa97940: fcmp            d0, d1
    // 0xa97944: b.le            #0xa97950
    // 0xa97948: mov             v4.16b, v1.16b
    // 0xa9794c: b               #0xa97964
    // 0xa97950: fcmp            d0, d0
    // 0xa97954: b.vc            #0xa97960
    // 0xa97958: mov             v4.16b, v1.16b
    // 0xa9795c: b               #0xa97964
    // 0xa97960: mov             v4.16b, v0.16b
    // 0xa97964: ldur            d3, [fp, #-0x40]
    // 0xa97968: d0 = 8.000000
    //     0xa97968: fmov            d0, #8.00000000
    // 0xa9796c: stur            d4, [fp, #-0x50]
    // 0xa97970: LoadField: d5 = r1->field_f
    //     0xa97970: ldur            d5, [x1, #0xf]
    // 0xa97974: fsub            d6, d5, d0
    // 0xa97978: fsub            d5, d6, d3
    // 0xa9797c: stur            d5, [fp, #-0x48]
    // 0xa97980: r0 = Offset()
    //     0xa97980: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xa97984: ldur            d0, [fp, #-0x50]
    // 0xa97988: stur            x0, [fp, #-0x10]
    // 0xa9798c: StoreField: r0->field_7 = d0
    //     0xa9798c: stur            d0, [x0, #7]
    // 0xa97990: ldur            d0, [fp, #-0x48]
    // 0xa97994: StoreField: r0->field_f = d0
    //     0xa97994: stur            d0, [x0, #0xf]
    // 0xa97998: ldur            x1, [fp, #-8]
    // 0xa9799c: LoadField: r2 = r1->field_f
    //     0xa9799c: ldur            w2, [x1, #0xf]
    // 0xa979a0: DecompressPointer r2
    //     0xa979a0: add             x2, x2, HEAP, lsl #32
    // 0xa979a4: LoadField: d0 = r2->field_7
    //     0xa979a4: ldur            d0, [x2, #7]
    // 0xa979a8: ldur            d1, [fp, #-0x38]
    // 0xa979ac: fcmp            d1, d0
    // 0xa979b0: b.le            #0xa979bc
    // 0xa979b4: mov             v2.16b, v1.16b
    // 0xa979b8: b               #0xa979e4
    // 0xa979bc: ldur            d1, [fp, #-0x58]
    // 0xa979c0: fcmp            d0, d1
    // 0xa979c4: b.le            #0xa979d0
    // 0xa979c8: mov             v2.16b, v1.16b
    // 0xa979cc: b               #0xa979e4
    // 0xa979d0: fcmp            d0, d0
    // 0xa979d4: b.vc            #0xa979e0
    // 0xa979d8: mov             v2.16b, v1.16b
    // 0xa979dc: b               #0xa979e4
    // 0xa979e0: mov             v2.16b, v0.16b
    // 0xa979e4: ldur            d1, [fp, #-0x40]
    // 0xa979e8: d0 = 8.000000
    //     0xa979e8: fmov            d0, #8.00000000
    // 0xa979ec: stur            d2, [fp, #-0x48]
    // 0xa979f0: LoadField: d3 = r2->field_f
    //     0xa979f0: ldur            d3, [x2, #0xf]
    // 0xa979f4: fadd            d4, d3, d0
    // 0xa979f8: fsub            d3, d4, d1
    // 0xa979fc: stur            d3, [fp, #-0x38]
    // 0xa97a00: r0 = Offset()
    //     0xa97a00: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xa97a04: ldur            d0, [fp, #-0x48]
    // 0xa97a08: stur            x0, [fp, #-0x18]
    // 0xa97a0c: StoreField: r0->field_7 = d0
    //     0xa97a0c: stur            d0, [x0, #7]
    // 0xa97a10: ldur            d0, [fp, #-0x38]
    // 0xa97a14: StoreField: r0->field_f = d0
    //     0xa97a14: stur            d0, [x0, #0xf]
    // 0xa97a18: r0 = EdgeInsets()
    //     0xa97a18: bl              #0x65dd90  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xa97a1c: d0 = 8.000000
    //     0xa97a1c: fmov            d0, #8.00000000
    // 0xa97a20: stur            x0, [fp, #-0x20]
    // 0xa97a24: StoreField: r0->field_7 = d0
    //     0xa97a24: stur            d0, [x0, #7]
    // 0xa97a28: ldur            d1, [fp, #-0x40]
    // 0xa97a2c: StoreField: r0->field_f = d1
    //     0xa97a2c: stur            d1, [x0, #0xf]
    // 0xa97a30: ArrayStore: r0[0] = d0  ; List_8
    //     0xa97a30: stur            d0, [x0, #0x17]
    // 0xa97a34: StoreField: r0->field_1f = d0
    //     0xa97a34: stur            d0, [x0, #0x1f]
    // 0xa97a38: r0 = TextSelectionToolbarLayoutDelegate()
    //     0xa97a38: bl              #0xa97ae4  ; AllocateTextSelectionToolbarLayoutDelegateStub -> TextSelectionToolbarLayoutDelegate (size=0x18)
    // 0xa97a3c: mov             x1, x0
    // 0xa97a40: ldur            x0, [fp, #-0x10]
    // 0xa97a44: stur            x1, [fp, #-0x30]
    // 0xa97a48: StoreField: r1->field_b = r0
    //     0xa97a48: stur            w0, [x1, #0xb]
    // 0xa97a4c: ldur            x2, [fp, #-0x18]
    // 0xa97a50: StoreField: r1->field_f = r2
    //     0xa97a50: stur            w2, [x1, #0xf]
    // 0xa97a54: ldur            x3, [fp, #-8]
    // 0xa97a58: LoadField: r4 = r3->field_13
    //     0xa97a58: ldur            w4, [x3, #0x13]
    // 0xa97a5c: DecompressPointer r4
    //     0xa97a5c: add             x4, x4, HEAP, lsl #32
    // 0xa97a60: stur            x4, [fp, #-0x28]
    // 0xa97a64: r0 = _CupertinoTextSelectionToolbarContent()
    //     0xa97a64: bl              #0xa97ad8  ; Allocate_CupertinoTextSelectionToolbarContentStub -> _CupertinoTextSelectionToolbarContent (size=0x1c)
    // 0xa97a68: mov             x1, x0
    // 0xa97a6c: ldur            x0, [fp, #-0x10]
    // 0xa97a70: stur            x1, [fp, #-8]
    // 0xa97a74: StoreField: r1->field_b = r0
    //     0xa97a74: stur            w0, [x1, #0xb]
    // 0xa97a78: ldur            x0, [fp, #-0x18]
    // 0xa97a7c: StoreField: r1->field_f = r0
    //     0xa97a7c: stur            w0, [x1, #0xf]
    // 0xa97a80: r0 = Closure: (BuildContext, Offset, Offset, Widget) => Widget from Function '_defaultToolbarBuilder@247408280': static.
    //     0xa97a80: add             x0, PP, #0x39, lsl #12  ; [pp+0x39a50] Closure: (BuildContext, Offset, Offset, Widget) => Widget from Function '_defaultToolbarBuilder@247408280': static. (0x7e54fb3e2edc)
    //     0xa97a84: ldr             x0, [x0, #0xa50]
    // 0xa97a88: ArrayStore: r1[0] = r0  ; List_4
    //     0xa97a88: stur            w0, [x1, #0x17]
    // 0xa97a8c: ldur            x0, [fp, #-0x28]
    // 0xa97a90: StoreField: r1->field_13 = r0
    //     0xa97a90: stur            w0, [x1, #0x13]
    // 0xa97a94: r0 = CustomSingleChildLayout()
    //     0xa97a94: bl              #0x9e6a80  ; AllocateCustomSingleChildLayoutStub -> CustomSingleChildLayout (size=0x14)
    // 0xa97a98: mov             x1, x0
    // 0xa97a9c: ldur            x0, [fp, #-0x30]
    // 0xa97aa0: stur            x1, [fp, #-0x10]
    // 0xa97aa4: StoreField: r1->field_f = r0
    //     0xa97aa4: stur            w0, [x1, #0xf]
    // 0xa97aa8: ldur            x0, [fp, #-8]
    // 0xa97aac: StoreField: r1->field_b = r0
    //     0xa97aac: stur            w0, [x1, #0xb]
    // 0xa97ab0: r0 = Padding()
    //     0xa97ab0: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa97ab4: ldur            x1, [fp, #-0x20]
    // 0xa97ab8: StoreField: r0->field_f = r1
    //     0xa97ab8: stur            w1, [x0, #0xf]
    // 0xa97abc: ldur            x1, [fp, #-0x10]
    // 0xa97ac0: StoreField: r0->field_b = r1
    //     0xa97ac0: stur            w1, [x0, #0xb]
    // 0xa97ac4: LeaveFrame
    //     0xa97ac4: mov             SP, fp
    //     0xa97ac8: ldp             fp, lr, [SP], #0x10
    // 0xa97acc: ret
    //     0xa97acc: ret             
    // 0xa97ad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa97ad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa97ad4: b               #0xa978cc
  }
}

// class id: 5469, size: 0x14, field offset: 0xc
abstract class _CupertinoChevronPainter extends CustomPainter {

  _ paint(/* No info */) {
    // ** addr: 0x7cf798, size: 0x1c4
    // 0x7cf798: EnterFrame
    //     0x7cf798: stp             fp, lr, [SP, #-0x10]!
    //     0x7cf79c: mov             fp, SP
    // 0x7cf7a0: AllocStack(0x60)
    //     0x7cf7a0: sub             SP, SP, #0x60
    // 0x7cf7a4: d0 = 4.000000
    //     0x7cf7a4: fmov            d0, #4.00000000
    // 0x7cf7a8: mov             x0, x1
    // 0x7cf7ac: stur            x1, [fp, #-0x10]
    // 0x7cf7b0: mov             x1, x2
    // 0x7cf7b4: stur            x2, [fp, #-0x18]
    // 0x7cf7b8: CheckStackOverflow
    //     0x7cf7b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cf7bc: cmp             SP, x16
    //     0x7cf7c0: b.ls            #0x7cf954
    // 0x7cf7c4: LoadField: d1 = r3->field_f
    //     0x7cf7c4: ldur            d1, [x3, #0xf]
    // 0x7cf7c8: stur            d1, [fp, #-0x48]
    // 0x7cf7cc: fdiv            d2, d1, d0
    // 0x7cf7d0: LoadField: r2 = r0->field_f
    //     0x7cf7d0: ldur            w2, [x0, #0xf]
    // 0x7cf7d4: DecompressPointer r2
    //     0x7cf7d4: add             x2, x2, HEAP, lsl #32
    // 0x7cf7d8: stur            x2, [fp, #-8]
    // 0x7cf7dc: tbnz            w2, #4, #0x7cf7e8
    // 0x7cf7e0: r3 = 1
    //     0x7cf7e0: movz            x3, #0x1
    // 0x7cf7e4: b               #0x7cf7ec
    // 0x7cf7e8: r3 = -1
    //     0x7cf7e8: movn            x3, #0
    // 0x7cf7ec: scvtf           d0, x3
    // 0x7cf7f0: fmul            d3, d2, d0
    // 0x7cf7f4: stur            d3, [fp, #-0x40]
    // 0x7cf7f8: r0 = Offset()
    //     0x7cf7f8: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7cf7fc: ldur            d0, [fp, #-0x40]
    // 0x7cf800: stur            x0, [fp, #-0x20]
    // 0x7cf804: StoreField: r0->field_7 = d0
    //     0x7cf804: stur            d0, [x0, #7]
    // 0x7cf808: StoreField: r0->field_f = rZR
    //     0x7cf808: stur            xzr, [x0, #0xf]
    // 0x7cf80c: ldur            d0, [fp, #-0x48]
    // 0x7cf810: d1 = 2.000000
    //     0x7cf810: fmov            d1, #2.00000000
    // 0x7cf814: fdiv            d2, d0, d1
    // 0x7cf818: stur            d2, [fp, #-0x40]
    // 0x7cf81c: r0 = Offset()
    //     0x7cf81c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7cf820: ldur            d0, [fp, #-0x40]
    // 0x7cf824: StoreField: r0->field_7 = d0
    //     0x7cf824: stur            d0, [x0, #7]
    // 0x7cf828: StoreField: r0->field_f = rZR
    //     0x7cf828: stur            xzr, [x0, #0xf]
    // 0x7cf82c: mov             x1, x0
    // 0x7cf830: ldur            x2, [fp, #-0x20]
    // 0x7cf834: r0 = +()
    //     0x7cf834: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x7cf838: mov             x1, x0
    // 0x7cf83c: ldur            x0, [fp, #-8]
    // 0x7cf840: stur            x1, [fp, #-0x28]
    // 0x7cf844: tbnz            w0, #4, #0x7cf850
    // 0x7cf848: d2 = 0.000000
    //     0x7cf848: eor             v2.16b, v2.16b, v2.16b
    // 0x7cf84c: b               #0x7cf854
    // 0x7cf850: ldur            d2, [fp, #-0x48]
    // 0x7cf854: ldur            x0, [fp, #-0x10]
    // 0x7cf858: ldur            d0, [fp, #-0x40]
    // 0x7cf85c: ldur            d1, [fp, #-0x48]
    // 0x7cf860: stur            d2, [fp, #-0x50]
    // 0x7cf864: r0 = Offset()
    //     0x7cf864: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7cf868: ldur            d0, [fp, #-0x50]
    // 0x7cf86c: StoreField: r0->field_7 = d0
    //     0x7cf86c: stur            d0, [x0, #7]
    // 0x7cf870: ldur            d0, [fp, #-0x40]
    // 0x7cf874: StoreField: r0->field_f = d0
    //     0x7cf874: stur            d0, [x0, #0xf]
    // 0x7cf878: mov             x1, x0
    // 0x7cf87c: ldur            x2, [fp, #-0x20]
    // 0x7cf880: r0 = +()
    //     0x7cf880: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x7cf884: stur            x0, [fp, #-8]
    // 0x7cf888: r0 = Offset()
    //     0x7cf888: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7cf88c: ldur            d0, [fp, #-0x40]
    // 0x7cf890: StoreField: r0->field_7 = d0
    //     0x7cf890: stur            d0, [x0, #7]
    // 0x7cf894: ldur            d0, [fp, #-0x48]
    // 0x7cf898: StoreField: r0->field_f = d0
    //     0x7cf898: stur            d0, [x0, #0xf]
    // 0x7cf89c: mov             x1, x0
    // 0x7cf8a0: ldur            x2, [fp, #-0x20]
    // 0x7cf8a4: r0 = +()
    //     0x7cf8a4: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x7cf8a8: stur            x0, [fp, #-0x20]
    // 0x7cf8ac: r16 = 136
    //     0x7cf8ac: movz            x16, #0x88
    // 0x7cf8b0: stp             x16, NULL, [SP]
    // 0x7cf8b4: r0 = ByteData()
    //     0x7cf8b4: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0x7cf8b8: stur            x0, [fp, #-0x30]
    // 0x7cf8bc: r0 = Paint()
    //     0x7cf8bc: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0x7cf8c0: mov             x3, x0
    // 0x7cf8c4: ldur            x0, [fp, #-0x30]
    // 0x7cf8c8: stur            x3, [fp, #-0x38]
    // 0x7cf8cc: StoreField: r3->field_7 = r0
    //     0x7cf8cc: stur            w0, [x3, #7]
    // 0x7cf8d0: ldur            x1, [fp, #-0x10]
    // 0x7cf8d4: LoadField: r2 = r1->field_b
    //     0x7cf8d4: ldur            w2, [x1, #0xb]
    // 0x7cf8d8: DecompressPointer r2
    //     0x7cf8d8: add             x2, x2, HEAP, lsl #32
    // 0x7cf8dc: mov             x1, x3
    // 0x7cf8e0: r0 = color=()
    //     0x7cf8e0: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0x7cf8e4: ldur            x0, [fp, #-0x30]
    // 0x7cf8e8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7cf8e8: ldur            w1, [x0, #0x17]
    // 0x7cf8ec: DecompressPointer r1
    //     0x7cf8ec: add             x1, x1, HEAP, lsl #32
    // 0x7cf8f0: LoadField: r0 = r1->field_7
    //     0x7cf8f0: ldur            x0, [x1, #7]
    // 0x7cf8f4: r2 = 1
    //     0x7cf8f4: movz            x2, #0x1
    // 0x7cf8f8: str             w2, [x0, #0x1c]
    // 0x7cf8fc: LoadField: r0 = r1->field_7
    //     0x7cf8fc: ldur            x0, [x1, #7]
    // 0x7cf900: d0 = 0.000000
    //     0x7cf900: add             x17, PP, #0x54, lsl #12  ; [pp+0x54ac8] IMM: 0x40000000
    //     0x7cf904: ldr             s0, [x17, #0xac8]
    // 0x7cf908: str             s0, [x0, #0x20]
    // 0x7cf90c: LoadField: r0 = r1->field_7
    //     0x7cf90c: ldur            x0, [x1, #7]
    // 0x7cf910: str             w2, [x0, #0x24]
    // 0x7cf914: LoadField: r0 = r1->field_7
    //     0x7cf914: ldur            x0, [x1, #7]
    // 0x7cf918: str             w2, [x0, #0x28]
    // 0x7cf91c: ldur            x1, [fp, #-0x18]
    // 0x7cf920: ldur            x2, [fp, #-0x28]
    // 0x7cf924: ldur            x3, [fp, #-8]
    // 0x7cf928: ldur            x5, [fp, #-0x38]
    // 0x7cf92c: r0 = drawLine()
    //     0x7cf92c: bl              #0x79712c  ; [dart:ui] _NativeCanvas::drawLine
    // 0x7cf930: ldur            x1, [fp, #-0x18]
    // 0x7cf934: ldur            x2, [fp, #-8]
    // 0x7cf938: ldur            x3, [fp, #-0x20]
    // 0x7cf93c: ldur            x5, [fp, #-0x38]
    // 0x7cf940: r0 = drawLine()
    //     0x7cf940: bl              #0x79712c  ; [dart:ui] _NativeCanvas::drawLine
    // 0x7cf944: r0 = Null
    //     0x7cf944: mov             x0, NULL
    // 0x7cf948: LeaveFrame
    //     0x7cf948: mov             SP, fp
    //     0x7cf94c: ldp             fp, lr, [SP], #0x10
    // 0x7cf950: ret
    //     0x7cf950: ret             
    // 0x7cf954: r0 = StackOverflowSharedWithFPURegs()
    //     0x7cf954: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7cf958: b               #0x7cf7c4
  }
  _ shouldRepaint(/* No info */) {
    // ** addr: 0x8a0650, size: 0xd4
    // 0x8a0650: EnterFrame
    //     0x8a0650: stp             fp, lr, [SP, #-0x10]!
    //     0x8a0654: mov             fp, SP
    // 0x8a0658: AllocStack(0x20)
    //     0x8a0658: sub             SP, SP, #0x20
    // 0x8a065c: SetupParameters(_CupertinoChevronPainter this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8a065c: mov             x4, x1
    //     0x8a0660: mov             x3, x2
    //     0x8a0664: stur            x1, [fp, #-8]
    //     0x8a0668: stur            x2, [fp, #-0x10]
    // 0x8a066c: CheckStackOverflow
    //     0x8a066c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a0670: cmp             SP, x16
    //     0x8a0674: b.ls            #0x8a071c
    // 0x8a0678: mov             x0, x3
    // 0x8a067c: r2 = Null
    //     0x8a067c: mov             x2, NULL
    // 0x8a0680: r1 = Null
    //     0x8a0680: mov             x1, NULL
    // 0x8a0684: r4 = 60
    //     0x8a0684: movz            x4, #0x3c
    // 0x8a0688: branchIfSmi(r0, 0x8a0694)
    //     0x8a0688: tbz             w0, #0, #0x8a0694
    // 0x8a068c: r4 = LoadClassIdInstr(r0)
    //     0x8a068c: ldur            x4, [x0, #-1]
    //     0x8a0690: ubfx            x4, x4, #0xc, #0x14
    // 0x8a0694: r17 = -5470
    //     0x8a0694: movn            x17, #0x155d
    // 0x8a0698: add             x4, x4, x17
    // 0x8a069c: cmp             x4, #1
    // 0x8a06a0: b.ls            #0x8a06b8
    // 0x8a06a4: r8 = _CupertinoChevronPainter
    //     0x8a06a4: add             x8, PP, #0x5a, lsl #12  ; [pp+0x5a5f0] Type: _CupertinoChevronPainter
    //     0x8a06a8: ldr             x8, [x8, #0x5f0]
    // 0x8a06ac: r3 = Null
    //     0x8a06ac: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5a5f8] Null
    //     0x8a06b0: ldr             x3, [x3, #0x5f8]
    // 0x8a06b4: r0 = DefaultTypeTest()
    //     0x8a06b4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8a06b8: ldur            x0, [fp, #-0x10]
    // 0x8a06bc: LoadField: r1 = r0->field_b
    //     0x8a06bc: ldur            w1, [x0, #0xb]
    // 0x8a06c0: DecompressPointer r1
    //     0x8a06c0: add             x1, x1, HEAP, lsl #32
    // 0x8a06c4: ldur            x2, [fp, #-8]
    // 0x8a06c8: LoadField: r3 = r2->field_b
    //     0x8a06c8: ldur            w3, [x2, #0xb]
    // 0x8a06cc: DecompressPointer r3
    //     0x8a06cc: add             x3, x3, HEAP, lsl #32
    // 0x8a06d0: stp             x3, x1, [SP]
    // 0x8a06d4: r0 = ==()
    //     0x8a06d4: bl              #0xd48828  ; [package:flutter/src/cupertino/colors.dart] CupertinoDynamicColor::==
    // 0x8a06d8: tbz             w0, #4, #0x8a06e4
    // 0x8a06dc: r0 = true
    //     0x8a06dc: add             x0, NULL, #0x20  ; true
    // 0x8a06e0: b               #0x8a0710
    // 0x8a06e4: ldur            x2, [fp, #-8]
    // 0x8a06e8: ldur            x1, [fp, #-0x10]
    // 0x8a06ec: LoadField: r3 = r1->field_f
    //     0x8a06ec: ldur            w3, [x1, #0xf]
    // 0x8a06f0: DecompressPointer r3
    //     0x8a06f0: add             x3, x3, HEAP, lsl #32
    // 0x8a06f4: LoadField: r1 = r2->field_f
    //     0x8a06f4: ldur            w1, [x2, #0xf]
    // 0x8a06f8: DecompressPointer r1
    //     0x8a06f8: add             x1, x1, HEAP, lsl #32
    // 0x8a06fc: cmp             w3, w1
    // 0x8a0700: r16 = true
    //     0x8a0700: add             x16, NULL, #0x20  ; true
    // 0x8a0704: r17 = false
    //     0x8a0704: add             x17, NULL, #0x30  ; false
    // 0x8a0708: csel            x2, x16, x17, ne
    // 0x8a070c: mov             x0, x2
    // 0x8a0710: LeaveFrame
    //     0x8a0710: mov             SP, fp
    //     0x8a0714: ldp             fp, lr, [SP], #0x10
    // 0x8a0718: ret
    //     0x8a0718: ret             
    // 0x8a071c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a071c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a0720: b               #0x8a0678
  }
}

// class id: 5470, size: 0x14, field offset: 0x14
class _RightCupertinoChevronPainter extends _CupertinoChevronPainter {
}

// class id: 5471, size: 0x14, field offset: 0x14
class _LeftCupertinoChevronPainter extends _CupertinoChevronPainter {
}

// class id: 7094, size: 0x14, field offset: 0x14
enum _CupertinoTextSelectionToolbarItemsSlot extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc48428, size: 0x64
    // 0xc48428: EnterFrame
    //     0xc48428: stp             fp, lr, [SP, #-0x10]!
    //     0xc4842c: mov             fp, SP
    // 0xc48430: AllocStack(0x10)
    //     0xc48430: sub             SP, SP, #0x10
    // 0xc48434: SetupParameters(_CupertinoTextSelectionToolbarItemsSlot this /* r1 => r0, fp-0x8 */)
    //     0xc48434: mov             x0, x1
    //     0xc48438: stur            x1, [fp, #-8]
    // 0xc4843c: CheckStackOverflow
    //     0xc4843c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc48440: cmp             SP, x16
    //     0xc48444: b.ls            #0xc48484
    // 0xc48448: r1 = Null
    //     0xc48448: mov             x1, NULL
    // 0xc4844c: r2 = 4
    //     0xc4844c: movz            x2, #0x4
    // 0xc48450: r0 = AllocateArray()
    //     0xc48450: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc48454: r16 = "_CupertinoTextSelectionToolbarItemsSlot."
    //     0xc48454: add             x16, PP, #0x5d, lsl #12  ; [pp+0x5dd78] "_CupertinoTextSelectionToolbarItemsSlot."
    //     0xc48458: ldr             x16, [x16, #0xd78]
    // 0xc4845c: StoreField: r0->field_f = r16
    //     0xc4845c: stur            w16, [x0, #0xf]
    // 0xc48460: ldur            x1, [fp, #-8]
    // 0xc48464: LoadField: r2 = r1->field_f
    //     0xc48464: ldur            w2, [x1, #0xf]
    // 0xc48468: DecompressPointer r2
    //     0xc48468: add             x2, x2, HEAP, lsl #32
    // 0xc4846c: StoreField: r0->field_13 = r2
    //     0xc4846c: stur            w2, [x0, #0x13]
    // 0xc48470: str             x0, [SP]
    // 0xc48474: r0 = _interpolate()
    //     0xc48474: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc48478: LeaveFrame
    //     0xc48478: mov             SP, fp
    //     0xc4847c: ldp             fp, lr, [SP], #0x10
    // 0xc48480: ret
    //     0xc48480: ret             
    // 0xc48484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc48484: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc48488: b               #0xc48448
  }
}
