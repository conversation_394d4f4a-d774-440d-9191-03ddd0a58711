// lib: , url: package:flutter/src/foundation/assertions.dart

// class id: 1048805, size: 0x8
class :: {

  static _ debugPrintStack(/* No info */) {
    // ** addr: 0x642508, size: 0x120
    // 0x642508: EnterFrame
    //     0x642508: stp             fp, lr, [SP, #-0x10]!
    //     0x64250c: mov             fp, SP
    // 0x642510: AllocStack(0x18)
    //     0x642510: sub             SP, SP, #0x18
    // 0x642514: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x642514: stur            x1, [fp, #-8]
    //     0x642518: stur            x2, [fp, #-0x10]
    // 0x64251c: CheckStackOverflow
    //     0x64251c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x642520: cmp             SP, x16
    //     0x642524: b.ls            #0x642620
    // 0x642528: r0 = InitLateStaticField(0x674) // [package:flutter/src/foundation/print.dart] ::debugPrint
    //     0x642528: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x64252c: ldr             x0, [x0, #0xce8]
    //     0x642530: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x642534: cmp             w0, w16
    //     0x642538: b.ne            #0x642544
    //     0x64253c: ldr             x2, [PP, #0x490]  ; [pp+0x490] Field <::.debugPrint>: static late (offset: 0x674)
    //     0x642540: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x642544: str             NULL, [SP]
    // 0x642548: ldur            x1, [fp, #-8]
    // 0x64254c: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0x64254c: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0x642550: r0 = debugPrintThrottled()
    //     0x642550: bl              #0x63fa18  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0x642554: ldur            x0, [fp, #-0x10]
    // 0x642558: cmp             w0, NULL
    // 0x64255c: b.ne            #0x642568
    // 0x642560: r0 = current()
    //     0x642560: bl              #0x5fc8a0  ; [dart:core] StackTrace::current
    // 0x642564: b               #0x642588
    // 0x642568: r0 = InitLateStaticField(0x644) // [package:flutter/src/foundation/assertions.dart] FlutterError::demangleStackTrace
    //     0x642568: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x64256c: ldr             x0, [x0, #0xc88]
    //     0x642570: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x642574: cmp             w0, w16
    //     0x642578: b.ne            #0x642584
    //     0x64257c: ldr             x2, [PP, #0x840]  ; [pp+0x840] Field <FlutterError.demangleStackTrace>: static late (offset: 0x644)
    //     0x642580: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x642584: ldur            x0, [fp, #-0x10]
    // 0x642588: r1 = LoadClassIdInstr(r0)
    //     0x642588: ldur            x1, [x0, #-1]
    //     0x64258c: ubfx            x1, x1, #0xc, #0x14
    // 0x642590: str             x0, [SP]
    // 0x642594: mov             x0, x1
    // 0x642598: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x642598: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x64259c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x64259c: movz            x17, #0x2b03
    //     0x6425a0: add             lr, x0, x17
    //     0x6425a4: ldr             lr, [x21, lr, lsl #3]
    //     0x6425a8: blr             lr
    // 0x6425ac: mov             x1, x0
    // 0x6425b0: r0 = trimRight()
    //     0x6425b0: bl              #0x642358  ; [dart:core] _StringBase::trimRight
    // 0x6425b4: r1 = LoadClassIdInstr(r0)
    //     0x6425b4: ldur            x1, [x0, #-1]
    //     0x6425b8: ubfx            x1, x1, #0xc, #0x14
    // 0x6425bc: mov             x16, x0
    // 0x6425c0: mov             x0, x1
    // 0x6425c4: mov             x1, x16
    // 0x6425c8: r2 = "\n"
    //     0x6425c8: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0x6425cc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x6425cc: sub             lr, x0, #1, lsl #12
    //     0x6425d0: ldr             lr, [x21, lr, lsl #3]
    //     0x6425d4: blr             lr
    // 0x6425d8: mov             x1, x0
    // 0x6425dc: r2 = 100
    //     0x6425dc: movz            x2, #0x64
    // 0x6425e0: r0 = take()
    //     0x6425e0: bl              #0x8630a0  ; [dart:collection] ListBase::take
    // 0x6425e4: mov             x1, x0
    // 0x6425e8: r0 = defaultStackFilter()
    //     0x6425e8: bl              #0x642628  ; [package:flutter/src/foundation/assertions.dart] FlutterError::defaultStackFilter
    // 0x6425ec: r16 = "\n"
    //     0x6425ec: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0x6425f0: str             x16, [SP]
    // 0x6425f4: mov             x1, x0
    // 0x6425f8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x6425f8: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x6425fc: r0 = join()
    //     0x6425fc: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0x642600: str             NULL, [SP]
    // 0x642604: mov             x1, x0
    // 0x642608: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0x642608: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0x64260c: r0 = debugPrintThrottled()
    //     0x64260c: bl              #0x63fa18  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0x642610: r0 = Null
    //     0x642610: mov             x0, NULL
    // 0x642614: LeaveFrame
    //     0x642614: mov             SP, fp
    //     0x642618: ldp             fp, lr, [SP], #0x10
    // 0x64261c: ret
    //     0x64261c: ret             
    // 0x642620: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x642620: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x642624: b               #0x642528
  }
}

// class id: 3610, size: 0x2c, field offset: 0x2c
abstract class _ErrorDiagnostic extends DiagnosticsProperty<dynamic> {

  _ _ErrorDiagnostic(/* No info */) {
    // ** addr: 0x644784, size: 0xb8
    // 0x644784: EnterFrame
    //     0x644784: stp             fp, lr, [SP, #-0x10]!
    //     0x644788: mov             fp, SP
    // 0x64478c: AllocStack(0x20)
    //     0x64478c: sub             SP, SP, #0x20
    // 0x644790: r0 = 2
    //     0x644790: movz            x0, #0x2
    // 0x644794: mov             x4, x2
    // 0x644798: stur            x2, [fp, #-0x10]
    // 0x64479c: mov             x2, x0
    // 0x6447a0: mov             x5, x1
    // 0x6447a4: stur            x1, [fp, #-8]
    // 0x6447a8: stur            x3, [fp, #-0x18]
    // 0x6447ac: r1 = Null
    //     0x6447ac: mov             x1, NULL
    // 0x6447b0: r0 = AllocateArray()
    //     0x6447b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6447b4: mov             x2, x0
    // 0x6447b8: ldur            x0, [fp, #-0x10]
    // 0x6447bc: stur            x2, [fp, #-0x20]
    // 0x6447c0: StoreField: r2->field_f = r0
    //     0x6447c0: stur            w0, [x2, #0xf]
    // 0x6447c4: r1 = <Object>
    //     0x6447c4: ldr             x1, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x6447c8: r0 = AllocateGrowableArray()
    //     0x6447c8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x6447cc: ldur            x1, [fp, #-0x20]
    // 0x6447d0: StoreField: r0->field_f = r1
    //     0x6447d0: stur            w1, [x0, #0xf]
    // 0x6447d4: r1 = 2
    //     0x6447d4: movz            x1, #0x2
    // 0x6447d8: StoreField: r0->field_b = r1
    //     0x6447d8: stur            w1, [x0, #0xb]
    // 0x6447dc: ldur            x2, [fp, #-8]
    // 0x6447e0: r1 = false
    //     0x6447e0: add             x1, NULL, #0x30  ; false
    // 0x6447e4: StoreField: r2->field_13 = r1
    //     0x6447e4: stur            w1, [x2, #0x13]
    // 0x6447e8: r1 = true
    //     0x6447e8: add             x1, NULL, #0x20  ; true
    // 0x6447ec: StoreField: r2->field_1b = r1
    //     0x6447ec: stur            w1, [x2, #0x1b]
    // 0x6447f0: ArrayStore: r2[0] = r0  ; List_4
    //     0x6447f0: stur            w0, [x2, #0x17]
    //     0x6447f4: ldurb           w16, [x2, #-1]
    //     0x6447f8: ldurb           w17, [x0, #-1]
    //     0x6447fc: and             x16, x17, x16, lsr #2
    //     0x644800: tst             x16, HEAP, lsr #32
    //     0x644804: b.eq            #0x64480c
    //     0x644808: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x64480c: ldur            x0, [fp, #-0x18]
    // 0x644810: StoreField: r2->field_27 = r0
    //     0x644810: stur            w0, [x2, #0x27]
    //     0x644814: ldurb           w16, [x2, #-1]
    //     0x644818: ldurb           w17, [x0, #-1]
    //     0x64481c: and             x16, x17, x16, lsr #2
    //     0x644820: tst             x16, HEAP, lsr #32
    //     0x644824: b.eq            #0x64482c
    //     0x644828: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x64482c: r0 = Null
    //     0x64482c: mov             x0, NULL
    // 0x644830: LeaveFrame
    //     0x644830: mov             SP, fp
    //     0x644834: ldp             fp, lr, [SP], #0x10
    // 0x644838: ret
    //     0x644838: ret             
  }
  get _ value(/* No info */) {
    // ** addr: 0x86513c, size: 0x38
    // 0x86513c: EnterFrame
    //     0x86513c: stp             fp, lr, [SP, #-0x10]!
    //     0x865140: mov             fp, SP
    // 0x865144: CheckStackOverflow
    //     0x865144: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865148: cmp             SP, x16
    //     0x86514c: b.ls            #0x865168
    // 0x865150: r0 = name()
    //     0x865150: bl              #0xebac0c  ; [package:xml/src/xml/nodes/element.dart] XmlElement::name
    // 0x865154: cmp             w0, NULL
    // 0x865158: b.eq            #0x865170
    // 0x86515c: LeaveFrame
    //     0x86515c: mov             SP, fp
    //     0x865160: ldp             fp, lr, [SP], #0x10
    // 0x865164: ret
    //     0x865164: ret             
    // 0x865168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86516c: b               #0x865150
    // 0x865170: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x865170: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ valueToString(/* No info */) {
    // ** addr: 0xbfd930, size: 0x54
    // 0xbfd930: EnterFrame
    //     0xbfd930: stp             fp, lr, [SP, #-0x10]!
    //     0xbfd934: mov             fp, SP
    // 0xbfd938: CheckStackOverflow
    //     0xbfd938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfd93c: cmp             SP, x16
    //     0xbfd940: b.ls            #0xbfd97c
    // 0xbfd944: r0 = value()
    //     0xbfd944: bl              #0x86513c  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::value
    // 0xbfd948: r1 = LoadClassIdInstr(r0)
    //     0xbfd948: ldur            x1, [x0, #-1]
    //     0xbfd94c: ubfx            x1, x1, #0xc, #0x14
    // 0xbfd950: mov             x16, x0
    // 0xbfd954: mov             x0, x1
    // 0xbfd958: mov             x1, x16
    // 0xbfd95c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbfd95c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbfd960: r0 = GDT[cid_x0 + 0xf18c]()
    //     0xbfd960: movz            x17, #0xf18c
    //     0xbfd964: add             lr, x0, x17
    //     0xbfd968: ldr             lr, [x21, lr, lsl #3]
    //     0xbfd96c: blr             lr
    // 0xbfd970: LeaveFrame
    //     0xbfd970: mov             SP, fp
    //     0xbfd974: ldp             fp, lr, [SP], #0x10
    // 0xbfd978: ret
    //     0xbfd978: ret             
    // 0xbfd97c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfd97c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfd980: b               #0xbfd944
  }
}

// class id: 3611, size: 0x2c, field offset: 0x2c
class ErrorHint extends _ErrorDiagnostic {
}

// class id: 3612, size: 0x2c, field offset: 0x2c
class ErrorSummary extends _ErrorDiagnostic {
}

// class id: 3613, size: 0x2c, field offset: 0x2c
class ErrorDescription extends _ErrorDiagnostic {
}

// class id: 3694, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class StackFilter extends Object {
}

// class id: 4062, size: 0x1c, field offset: 0x8
//   const constructor, 
class FlutterErrorDetails extends _DiagnosticableTree&Object&Diagnosticable {

  get _ summary(/* No info */) {
    // ** addr: 0x641ad4, size: 0x8c
    // 0x641ad4: EnterFrame
    //     0x641ad4: stp             fp, lr, [SP, #-0x10]!
    //     0x641ad8: mov             fp, SP
    // 0x641adc: CheckStackOverflow
    //     0x641adc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x641ae0: cmp             SP, x16
    //     0x641ae4: b.ls            #0x641b54
    // 0x641ae8: r0 = exceptionAsString()
    //     0x641ae8: bl              #0x641bac  ; [package:flutter/src/foundation/assertions.dart] FlutterErrorDetails::exceptionAsString
    // 0x641aec: r1 = LoadClassIdInstr(r0)
    //     0x641aec: ldur            x1, [x0, #-1]
    //     0x641af0: ubfx            x1, x1, #0xc, #0x14
    // 0x641af4: mov             x16, x0
    // 0x641af8: mov             x0, x1
    // 0x641afc: mov             x1, x16
    // 0x641b00: r2 = "\n"
    //     0x641b00: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0x641b04: r0 = GDT[cid_x0 + -0x1000]()
    //     0x641b04: sub             lr, x0, #1, lsl #12
    //     0x641b08: ldr             lr, [x21, lr, lsl #3]
    //     0x641b0c: blr             lr
    // 0x641b10: mov             x2, x0
    // 0x641b14: LoadField: r0 = r2->field_b
    //     0x641b14: ldur            w0, [x2, #0xb]
    // 0x641b18: r1 = LoadInt32Instr(r0)
    //     0x641b18: sbfx            x1, x0, #1, #0x1f
    // 0x641b1c: mov             x0, x1
    // 0x641b20: r1 = 0
    //     0x641b20: movz            x1, #0
    // 0x641b24: cmp             x1, x0
    // 0x641b28: b.hs            #0x641b5c
    // 0x641b2c: LoadField: r0 = r2->field_f
    //     0x641b2c: ldur            w0, [x2, #0xf]
    // 0x641b30: DecompressPointer r0
    //     0x641b30: add             x0, x0, HEAP, lsl #32
    // 0x641b34: LoadField: r1 = r0->field_f
    //     0x641b34: ldur            w1, [x0, #0xf]
    // 0x641b38: DecompressPointer r1
    //     0x641b38: add             x1, x1, HEAP, lsl #32
    // 0x641b3c: r0 = trimLeft()
    //     0x641b3c: bl              #0x6418ac  ; [dart:core] _StringBase::trimLeft
    // 0x641b40: r1 = Null
    //     0x641b40: mov             x1, NULL
    // 0x641b44: r0 = DiagnosticsNode.message()
    //     0x641b44: bl              #0x641b60  ; [package:flutter/src/foundation/diagnostics.dart] DiagnosticsNode::DiagnosticsNode.message
    // 0x641b48: LeaveFrame
    //     0x641b48: mov             SP, fp
    //     0x641b4c: ldp             fp, lr, [SP], #0x10
    // 0x641b50: ret
    //     0x641b50: ret             
    // 0x641b54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x641b54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x641b58: b               #0x641ae8
    // 0x641b5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x641b5c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ exceptionAsString(/* No info */) {
    // ** addr: 0x641bac, size: 0x5a4
    // 0x641bac: EnterFrame
    //     0x641bac: stp             fp, lr, [SP, #-0x10]!
    //     0x641bb0: mov             fp, SP
    // 0x641bb4: AllocStack(0x58)
    //     0x641bb4: sub             SP, SP, #0x58
    // 0x641bb8: CheckStackOverflow
    //     0x641bb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x641bbc: cmp             SP, x16
    //     0x641bc0: b.ls            #0x642140
    // 0x641bc4: LoadField: r3 = r1->field_7
    //     0x641bc4: ldur            w3, [x1, #7]
    // 0x641bc8: DecompressPointer r3
    //     0x641bc8: add             x3, x3, HEAP, lsl #32
    // 0x641bcc: mov             x0, x3
    // 0x641bd0: stur            x3, [fp, #-8]
    // 0x641bd4: r2 = Null
    //     0x641bd4: mov             x2, NULL
    // 0x641bd8: r1 = Null
    //     0x641bd8: mov             x1, NULL
    // 0x641bdc: cmp             w0, NULL
    // 0x641be0: b.eq            #0x641c0c
    // 0x641be4: branchIfSmi(r0, 0x641c0c)
    //     0x641be4: tbz             w0, #0, #0x641c0c
    // 0x641be8: r3 = LoadClassIdInstr(r0)
    //     0x641be8: ldur            x3, [x0, #-1]
    //     0x641bec: ubfx            x3, x3, #0xc, #0x14
    // 0x641bf0: r17 = 7347
    //     0x641bf0: movz            x17, #0x1cb3
    // 0x641bf4: cmp             x3, x17
    // 0x641bf8: b.eq            #0x641c14
    // 0x641bfc: r17 = -7367
    //     0x641bfc: movn            x17, #0x1cc6
    // 0x641c00: add             x3, x3, x17
    // 0x641c04: cmp             x3, #1
    // 0x641c08: b.ls            #0x641c14
    // 0x641c0c: r0 = false
    //     0x641c0c: add             x0, NULL, #0x30  ; false
    // 0x641c10: b               #0x641c18
    // 0x641c14: r0 = true
    //     0x641c14: add             x0, NULL, #0x20  ; true
    // 0x641c18: tbnz            w0, #4, #0x641f1c
    // 0x641c1c: ldur            x2, [fp, #-8]
    // 0x641c20: r0 = LoadClassIdInstr(r2)
    //     0x641c20: ldur            x0, [x2, #-1]
    //     0x641c24: ubfx            x0, x0, #0xc, #0x14
    // 0x641c28: mov             x1, x2
    // 0x641c2c: r0 = GDT[cid_x0 + -0xfa0]()
    //     0x641c2c: sub             lr, x0, #0xfa0
    //     0x641c30: ldr             lr, [x21, lr, lsl #3]
    //     0x641c34: blr             lr
    // 0x641c38: mov             x1, x0
    // 0x641c3c: ldur            x3, [fp, #-8]
    // 0x641c40: stur            x1, [fp, #-0x10]
    // 0x641c44: r0 = LoadClassIdInstr(r3)
    //     0x641c44: ldur            x0, [x3, #-1]
    //     0x641c48: ubfx            x0, x0, #0xc, #0x14
    // 0x641c4c: str             x3, [SP]
    // 0x641c50: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x641c50: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x641c54: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x641c54: movz            x17, #0x2b03
    //     0x641c58: add             lr, x0, x17
    //     0x641c5c: ldr             lr, [x21, lr, lsl #3]
    //     0x641c60: blr             lr
    // 0x641c64: mov             x2, x0
    // 0x641c68: ldur            x1, [fp, #-0x10]
    // 0x641c6c: stur            x2, [fp, #-0x18]
    // 0x641c70: r0 = 60
    //     0x641c70: movz            x0, #0x3c
    // 0x641c74: branchIfSmi(r1, 0x641c80)
    //     0x641c74: tbz             w1, #0, #0x641c80
    // 0x641c78: r0 = LoadClassIdInstr(r1)
    //     0x641c78: ldur            x0, [x1, #-1]
    //     0x641c7c: ubfx            x0, x0, #0xc, #0x14
    // 0x641c80: sub             x16, x0, #0x5e
    // 0x641c84: cmp             x16, #1
    // 0x641c88: b.hi            #0x641f04
    // 0x641c8c: r0 = LoadClassIdInstr(r1)
    //     0x641c8c: ldur            x0, [x1, #-1]
    //     0x641c90: ubfx            x0, x0, #0xc, #0x14
    // 0x641c94: stp             x2, x1, [SP]
    // 0x641c98: mov             lr, x0
    // 0x641c9c: ldr             lr, [x21, lr, lsl #3]
    // 0x641ca0: blr             lr
    // 0x641ca4: tbz             w0, #4, #0x641f04
    // 0x641ca8: ldur            x2, [fp, #-0x10]
    // 0x641cac: ldur            x3, [fp, #-0x18]
    // 0x641cb0: LoadField: r0 = r3->field_7
    //     0x641cb0: ldur            w0, [x3, #7]
    // 0x641cb4: LoadField: r1 = r2->field_7
    //     0x641cb4: ldur            w1, [x2, #7]
    // 0x641cb8: r4 = LoadInt32Instr(r0)
    //     0x641cb8: sbfx            x4, x0, #1, #0x1f
    // 0x641cbc: r0 = LoadInt32Instr(r1)
    //     0x641cbc: sbfx            x0, x1, #1, #0x1f
    // 0x641cc0: cmp             x4, x0
    // 0x641cc4: b.le            #0x641efc
    // 0x641cc8: sub             x5, x4, x0
    // 0x641ccc: stur            x5, [fp, #-0x28]
    // 0x641cd0: cmp             x5, x4
    // 0x641cd4: b.ge            #0x641ce0
    // 0x641cd8: mov             x0, x5
    // 0x641cdc: b               #0x641ce4
    // 0x641ce0: mov             x0, x4
    // 0x641ce4: mov             x4, x0
    // 0x641ce8: stur            x4, [fp, #-0x20]
    // 0x641cec: CheckStackOverflow
    //     0x641cec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x641cf0: cmp             SP, x16
    //     0x641cf4: b.ls            #0x642148
    // 0x641cf8: tbnz            x4, #0x3f, #0x641d44
    // 0x641cfc: r0 = BoxInt64Instr(r4)
    //     0x641cfc: sbfiz           x0, x4, #1, #0x1f
    //     0x641d00: cmp             x4, x0, asr #1
    //     0x641d04: b.eq            #0x641d10
    //     0x641d08: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x641d0c: stur            x4, [x0, #7]
    // 0x641d10: stp             x0, x3, [SP, #8]
    // 0x641d14: str             x2, [SP]
    // 0x641d18: r0 = _substringMatches()
    //     0x641d18: bl              #0x6085f8  ; [dart:core] _StringBase::_substringMatches
    // 0x641d1c: tbz             w0, #4, #0x641d38
    // 0x641d20: ldur            x0, [fp, #-0x20]
    // 0x641d24: sub             x4, x0, #1
    // 0x641d28: ldur            x2, [fp, #-0x10]
    // 0x641d2c: ldur            x3, [fp, #-0x18]
    // 0x641d30: ldur            x5, [fp, #-0x28]
    // 0x641d34: b               #0x641ce8
    // 0x641d38: ldur            x0, [fp, #-0x20]
    // 0x641d3c: mov             x2, x0
    // 0x641d40: b               #0x641d48
    // 0x641d44: r2 = -1
    //     0x641d44: movn            x2, #0
    // 0x641d48: ldur            x0, [fp, #-0x28]
    // 0x641d4c: cmp             x2, x0
    // 0x641d50: b.ne            #0x641ef4
    // 0x641d54: cmp             x2, #2
    // 0x641d58: b.le            #0x641ef4
    // 0x641d5c: sub             x3, x2, #2
    // 0x641d60: stur            x3, [fp, #-0x20]
    // 0x641d64: r0 = BoxInt64Instr(r2)
    //     0x641d64: sbfiz           x0, x2, #1, #0x1f
    //     0x641d68: cmp             x2, x0, asr #1
    //     0x641d6c: b.eq            #0x641d78
    //     0x641d70: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x641d74: stur            x2, [x0, #7]
    // 0x641d78: str             x0, [SP]
    // 0x641d7c: ldur            x1, [fp, #-0x18]
    // 0x641d80: mov             x2, x3
    // 0x641d84: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x641d84: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x641d88: r0 = substring()
    //     0x641d88: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x641d8c: r1 = LoadClassIdInstr(r0)
    //     0x641d8c: ldur            x1, [x0, #-1]
    //     0x641d90: ubfx            x1, x1, #0xc, #0x14
    // 0x641d94: r16 = ": "
    //     0x641d94: ldr             x16, [PP, #0x7d8]  ; [pp+0x7d8] ": "
    // 0x641d98: stp             x16, x0, [SP]
    // 0x641d9c: mov             x0, x1
    // 0x641da0: mov             lr, x0
    // 0x641da4: ldr             lr, [x21, lr, lsl #3]
    // 0x641da8: blr             lr
    // 0x641dac: tbnz            w0, #4, #0x641ef4
    // 0x641db0: ldur            x2, [fp, #-0x20]
    // 0x641db4: r0 = BoxInt64Instr(r2)
    //     0x641db4: sbfiz           x0, x2, #1, #0x1f
    //     0x641db8: cmp             x2, x0, asr #1
    //     0x641dbc: b.eq            #0x641dc8
    //     0x641dc0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x641dc4: stur            x2, [x0, #7]
    // 0x641dc8: str             x0, [SP]
    // 0x641dcc: ldur            x1, [fp, #-0x18]
    // 0x641dd0: r2 = 0
    //     0x641dd0: movz            x2, #0
    // 0x641dd4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x641dd4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x641dd8: r0 = substring()
    //     0x641dd8: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x641ddc: mov             x3, x0
    // 0x641de0: stur            x3, [fp, #-0x30]
    // 0x641de4: r0 = LoadClassIdInstr(r3)
    //     0x641de4: ldur            x0, [x3, #-1]
    //     0x641de8: ubfx            x0, x0, #0xc, #0x14
    // 0x641dec: mov             x1, x3
    // 0x641df0: r2 = " Failed assertion:"
    //     0x641df0: ldr             x2, [PP, #0x7e0]  ; [pp+0x7e0] " Failed assertion:"
    // 0x641df4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x641df4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x641df8: r0 = GDT[cid_x0 + -0xffa]()
    //     0x641df8: sub             lr, x0, #0xffa
    //     0x641dfc: ldr             lr, [x21, lr, lsl #3]
    //     0x641e00: blr             lr
    // 0x641e04: mov             x3, x0
    // 0x641e08: stur            x3, [fp, #-0x20]
    // 0x641e0c: tbnz            x3, #0x3f, #0x641eac
    // 0x641e10: r0 = BoxInt64Instr(r3)
    //     0x641e10: sbfiz           x0, x3, #1, #0x1f
    //     0x641e14: cmp             x3, x0, asr #1
    //     0x641e18: b.eq            #0x641e24
    //     0x641e1c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x641e20: stur            x3, [x0, #7]
    // 0x641e24: str             x0, [SP]
    // 0x641e28: ldur            x1, [fp, #-0x30]
    // 0x641e2c: r2 = 0
    //     0x641e2c: movz            x2, #0
    // 0x641e30: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x641e30: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x641e34: r0 = substring()
    //     0x641e34: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x641e38: r1 = Null
    //     0x641e38: mov             x1, NULL
    // 0x641e3c: r2 = 6
    //     0x641e3c: movz            x2, #0x6
    // 0x641e40: stur            x0, [fp, #-0x38]
    // 0x641e44: r0 = AllocateArray()
    //     0x641e44: bl              #0xec22fc  ; AllocateArrayStub
    // 0x641e48: mov             x3, x0
    // 0x641e4c: ldur            x0, [fp, #-0x38]
    // 0x641e50: stur            x3, [fp, #-0x40]
    // 0x641e54: StoreField: r3->field_f = r0
    //     0x641e54: stur            w0, [x3, #0xf]
    // 0x641e58: r16 = "\n"
    //     0x641e58: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0x641e5c: StoreField: r3->field_13 = r16
    //     0x641e5c: stur            w16, [x3, #0x13]
    // 0x641e60: ldur            x0, [fp, #-0x20]
    // 0x641e64: add             x2, x0, #1
    // 0x641e68: ldur            x1, [fp, #-0x30]
    // 0x641e6c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x641e6c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x641e70: r0 = substring()
    //     0x641e70: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x641e74: ldur            x1, [fp, #-0x40]
    // 0x641e78: ArrayStore: r1[2] = r0  ; List_4
    //     0x641e78: add             x25, x1, #0x17
    //     0x641e7c: str             w0, [x25]
    //     0x641e80: tbz             w0, #0, #0x641e9c
    //     0x641e84: ldurb           w16, [x1, #-1]
    //     0x641e88: ldurb           w17, [x0, #-1]
    //     0x641e8c: and             x16, x17, x16, lsr #2
    //     0x641e90: tst             x16, HEAP, lsr #32
    //     0x641e94: b.eq            #0x641e9c
    //     0x641e98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x641e9c: ldur            x16, [fp, #-0x40]
    // 0x641ea0: str             x16, [SP]
    // 0x641ea4: r0 = _interpolate()
    //     0x641ea4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x641ea8: b               #0x641eb0
    // 0x641eac: ldur            x0, [fp, #-0x30]
    // 0x641eb0: ldur            x1, [fp, #-0x10]
    // 0x641eb4: stur            x0, [fp, #-0x30]
    // 0x641eb8: r0 = trimRight()
    //     0x641eb8: bl              #0x642358  ; [dart:core] _StringBase::trimRight
    // 0x641ebc: r1 = Null
    //     0x641ebc: mov             x1, NULL
    // 0x641ec0: r2 = 6
    //     0x641ec0: movz            x2, #0x6
    // 0x641ec4: stur            x0, [fp, #-0x10]
    // 0x641ec8: r0 = AllocateArray()
    //     0x641ec8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x641ecc: mov             x1, x0
    // 0x641ed0: ldur            x0, [fp, #-0x10]
    // 0x641ed4: StoreField: r1->field_f = r0
    //     0x641ed4: stur            w0, [x1, #0xf]
    // 0x641ed8: r16 = "\n"
    //     0x641ed8: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0x641edc: StoreField: r1->field_13 = r16
    //     0x641edc: stur            w16, [x1, #0x13]
    // 0x641ee0: ldur            x0, [fp, #-0x30]
    // 0x641ee4: ArrayStore: r1[0] = r0  ; List_4
    //     0x641ee4: stur            w0, [x1, #0x17]
    // 0x641ee8: str             x1, [SP]
    // 0x641eec: r0 = _interpolate()
    //     0x641eec: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x641ef0: b               #0x641f08
    // 0x641ef4: r0 = Null
    //     0x641ef4: mov             x0, NULL
    // 0x641ef8: b               #0x641f08
    // 0x641efc: r0 = Null
    //     0x641efc: mov             x0, NULL
    // 0x641f00: b               #0x641f08
    // 0x641f04: r0 = Null
    //     0x641f04: mov             x0, NULL
    // 0x641f08: cmp             w0, NULL
    // 0x641f0c: b.ne            #0x641f14
    // 0x641f10: ldur            x0, [fp, #-0x18]
    // 0x641f14: mov             x1, x0
    // 0x641f18: b               #0x642124
    // 0x641f1c: ldur            x3, [fp, #-8]
    // 0x641f20: r0 = 60
    //     0x641f20: movz            x0, #0x3c
    // 0x641f24: branchIfSmi(r3, 0x641f30)
    //     0x641f24: tbz             w3, #0, #0x641f30
    // 0x641f28: r0 = LoadClassIdInstr(r3)
    //     0x641f28: ldur            x0, [x3, #-1]
    //     0x641f2c: ubfx            x0, x0, #0xc, #0x14
    // 0x641f30: sub             x16, x0, #0x5e
    // 0x641f34: cmp             x16, #1
    // 0x641f38: b.hi            #0x641f78
    // 0x641f3c: mov             x0, x3
    // 0x641f40: r2 = Null
    //     0x641f40: mov             x2, NULL
    // 0x641f44: r1 = Null
    //     0x641f44: mov             x1, NULL
    // 0x641f48: r4 = 60
    //     0x641f48: movz            x4, #0x3c
    // 0x641f4c: branchIfSmi(r0, 0x641f58)
    //     0x641f4c: tbz             w0, #0, #0x641f58
    // 0x641f50: r4 = LoadClassIdInstr(r0)
    //     0x641f50: ldur            x4, [x0, #-1]
    //     0x641f54: ubfx            x4, x4, #0xc, #0x14
    // 0x641f58: sub             x4, x4, #0x5e
    // 0x641f5c: cmp             x4, #1
    // 0x641f60: b.ls            #0x641f70
    // 0x641f64: r8 = String
    //     0x641f64: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x641f68: r3 = Null
    //     0x641f68: ldr             x3, [PP, #0x7e8]  ; [pp+0x7e8] Null
    // 0x641f6c: r0 = String()
    //     0x641f6c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x641f70: ldur            x0, [fp, #-8]
    // 0x641f74: b               #0x642120
    // 0x641f78: ldur            x0, [fp, #-8]
    // 0x641f7c: r2 = Null
    //     0x641f7c: mov             x2, NULL
    // 0x641f80: r1 = Null
    //     0x641f80: mov             x1, NULL
    // 0x641f84: cmp             w0, NULL
    // 0x641f88: b.eq            #0x642014
    // 0x641f8c: branchIfSmi(r0, 0x642014)
    //     0x641f8c: tbz             w0, #0, #0x642014
    // 0x641f90: r3 = LoadClassIdInstr(r0)
    //     0x641f90: ldur            x3, [x0, #-1]
    //     0x641f94: ubfx            x3, x3, #0xc, #0x14
    // 0x641f98: r17 = 7341
    //     0x641f98: movz            x17, #0x1cad
    // 0x641f9c: cmp             x3, x17
    // 0x641fa0: b.eq            #0x64201c
    // 0x641fa4: r4 = LoadClassIdInstr(r0)
    //     0x641fa4: ldur            x4, [x0, #-1]
    //     0x641fa8: ubfx            x4, x4, #0xc, #0x14
    // 0x641fac: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x641fb0: ldr             x3, [x3, #0x18]
    // 0x641fb4: ldr             x3, [x3, x4, lsl #3]
    // 0x641fb8: LoadField: r3 = r3->field_2b
    //     0x641fb8: ldur            w3, [x3, #0x2b]
    // 0x641fbc: DecompressPointer r3
    //     0x641fbc: add             x3, x3, HEAP, lsl #32
    // 0x641fc0: cmp             w3, NULL
    // 0x641fc4: b.eq            #0x642014
    // 0x641fc8: LoadField: r3 = r3->field_f
    //     0x641fc8: ldur            w3, [x3, #0xf]
    // 0x641fcc: lsr             x3, x3, #3
    // 0x641fd0: r17 = 7341
    //     0x641fd0: movz            x17, #0x1cad
    // 0x641fd4: cmp             x3, x17
    // 0x641fd8: b.eq            #0x64201c
    // 0x641fdc: r3 = SubtypeTestCache
    //     0x641fdc: ldr             x3, [PP, #0x7f8]  ; [pp+0x7f8] SubtypeTestCache
    // 0x641fe0: r30 = Subtype1TestCacheStub
    //     0x641fe0: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x641fe4: LoadField: r30 = r30->field_7
    //     0x641fe4: ldur            lr, [lr, #7]
    // 0x641fe8: blr             lr
    // 0x641fec: cmp             w7, NULL
    // 0x641ff0: b.eq            #0x641ffc
    // 0x641ff4: tbnz            w7, #4, #0x642014
    // 0x641ff8: b               #0x64201c
    // 0x641ffc: r8 = Error
    //     0x641ffc: ldr             x8, [PP, #0x808]  ; [pp+0x808] Type: Error
    // 0x642000: r3 = SubtypeTestCache
    //     0x642000: ldr             x3, [PP, #0x810]  ; [pp+0x810] SubtypeTestCache
    // 0x642004: r30 = InstanceOfStub
    //     0x642004: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x642008: LoadField: r30 = r30->field_7
    //     0x642008: ldur            lr, [lr, #7]
    // 0x64200c: blr             lr
    // 0x642010: b               #0x642020
    // 0x642014: r0 = false
    //     0x642014: add             x0, NULL, #0x30  ; false
    // 0x642018: b               #0x642020
    // 0x64201c: r0 = true
    //     0x64201c: add             x0, NULL, #0x20  ; true
    // 0x642020: tbz             w0, #4, #0x6420c4
    // 0x642024: ldur            x0, [fp, #-8]
    // 0x642028: r2 = Null
    //     0x642028: mov             x2, NULL
    // 0x64202c: r1 = Null
    //     0x64202c: mov             x1, NULL
    // 0x642030: cmp             w0, NULL
    // 0x642034: b.eq            #0x6420b4
    // 0x642038: branchIfSmi(r0, 0x6420b4)
    //     0x642038: tbz             w0, #0, #0x6420b4
    // 0x64203c: r3 = LoadClassIdInstr(r0)
    //     0x64203c: ldur            x3, [x0, #-1]
    //     0x642040: ubfx            x3, x3, #0xc, #0x14
    // 0x642044: r4 = LoadClassIdInstr(r0)
    //     0x642044: ldur            x4, [x0, #-1]
    //     0x642048: ubfx            x4, x4, #0xc, #0x14
    // 0x64204c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x642050: ldr             x3, [x3, #0x18]
    // 0x642054: ldr             x3, [x3, x4, lsl #3]
    // 0x642058: LoadField: r3 = r3->field_2b
    //     0x642058: ldur            w3, [x3, #0x2b]
    // 0x64205c: DecompressPointer r3
    //     0x64205c: add             x3, x3, HEAP, lsl #32
    // 0x642060: cmp             w3, NULL
    // 0x642064: b.eq            #0x6420b4
    // 0x642068: LoadField: r3 = r3->field_f
    //     0x642068: ldur            w3, [x3, #0xf]
    // 0x64206c: lsr             x3, x3, #3
    // 0x642070: r17 = 6724
    //     0x642070: movz            x17, #0x1a44
    // 0x642074: cmp             x3, x17
    // 0x642078: b.eq            #0x6420bc
    // 0x64207c: r3 = SubtypeTestCache
    //     0x64207c: ldr             x3, [PP, #0x818]  ; [pp+0x818] SubtypeTestCache
    // 0x642080: r30 = Subtype1TestCacheStub
    //     0x642080: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x642084: LoadField: r30 = r30->field_7
    //     0x642084: ldur            lr, [lr, #7]
    // 0x642088: blr             lr
    // 0x64208c: cmp             w7, NULL
    // 0x642090: b.eq            #0x64209c
    // 0x642094: tbnz            w7, #4, #0x6420b4
    // 0x642098: b               #0x6420bc
    // 0x64209c: r8 = Exception
    //     0x64209c: ldr             x8, [PP, #0x820]  ; [pp+0x820] Type: Exception
    // 0x6420a0: r3 = SubtypeTestCache
    //     0x6420a0: ldr             x3, [PP, #0x828]  ; [pp+0x828] SubtypeTestCache
    // 0x6420a4: r30 = InstanceOfStub
    //     0x6420a4: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x6420a8: LoadField: r30 = r30->field_7
    //     0x6420a8: ldur            lr, [lr, #7]
    // 0x6420ac: blr             lr
    // 0x6420b0: b               #0x6420c0
    // 0x6420b4: r0 = false
    //     0x6420b4: add             x0, NULL, #0x30  ; false
    // 0x6420b8: b               #0x6420c0
    // 0x6420bc: r0 = true
    //     0x6420bc: add             x0, NULL, #0x20  ; true
    // 0x6420c0: tbnz            w0, #4, #0x6420f8
    // 0x6420c4: ldur            x0, [fp, #-8]
    // 0x6420c8: r1 = 60
    //     0x6420c8: movz            x1, #0x3c
    // 0x6420cc: branchIfSmi(r0, 0x6420d8)
    //     0x6420cc: tbz             w0, #0, #0x6420d8
    // 0x6420d0: r1 = LoadClassIdInstr(r0)
    //     0x6420d0: ldur            x1, [x0, #-1]
    //     0x6420d4: ubfx            x1, x1, #0xc, #0x14
    // 0x6420d8: str             x0, [SP]
    // 0x6420dc: mov             x0, x1
    // 0x6420e0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x6420e0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x6420e4: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x6420e4: movz            x17, #0x2b03
    //     0x6420e8: add             lr, x0, x17
    //     0x6420ec: ldr             lr, [x21, lr, lsl #3]
    //     0x6420f0: blr             lr
    // 0x6420f4: b               #0x642120
    // 0x6420f8: ldur            x0, [fp, #-8]
    // 0x6420fc: r1 = Null
    //     0x6420fc: mov             x1, NULL
    // 0x642100: r2 = 4
    //     0x642100: movz            x2, #0x4
    // 0x642104: r0 = AllocateArray()
    //     0x642104: bl              #0xec22fc  ; AllocateArrayStub
    // 0x642108: r16 = "  "
    //     0x642108: ldr             x16, [PP, #0x830]  ; [pp+0x830] "  "
    // 0x64210c: StoreField: r0->field_f = r16
    //     0x64210c: stur            w16, [x0, #0xf]
    // 0x642110: ldur            x1, [fp, #-8]
    // 0x642114: StoreField: r0->field_13 = r1
    //     0x642114: stur            w1, [x0, #0x13]
    // 0x642118: str             x0, [SP]
    // 0x64211c: r0 = _interpolate()
    //     0x64211c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x642120: mov             x1, x0
    // 0x642124: r0 = trimRight()
    //     0x642124: bl              #0x642358  ; [dart:core] _StringBase::trimRight
    // 0x642128: LoadField: r1 = r0->field_7
    //     0x642128: ldur            w1, [x0, #7]
    // 0x64212c: cbnz            w1, #0x642134
    // 0x642130: r0 = "  <no message available>"
    //     0x642130: ldr             x0, [PP, #0x838]  ; [pp+0x838] "  <no message available>"
    // 0x642134: LeaveFrame
    //     0x642134: mov             SP, fp
    //     0x642138: ldp             fp, lr, [SP], #0x10
    // 0x64213c: ret
    //     0x64213c: ret             
    // 0x642140: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x642140: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x642144: b               #0x641bc4
    // 0x642148: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x642148: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64214c: b               #0x641cf8
  }
}

// class id: 7346, size: 0xc, field offset: 0xc
//   transformed mixin,
abstract class _FlutterError&Error&DiagnosticableTreeMixin extends Error
     with DiagnosticableTreeMixin {
}

// class id: 7347, size: 0x10, field offset: 0xc
class FlutterError extends _FlutterError&Error&DiagnosticableTreeMixin
    implements AssertionError {

  static late ((dynamic, FlutterErrorDetails) => void)? onError; // offset: 0x640
  static late (dynamic, FlutterErrorDetails) => void presentError; // offset: 0x648
  static late (dynamic, StackTrace) => StackTrace demangleStackTrace; // offset: 0x644
  static late final List<StackFilter> _stackFilters; // offset: 0x650

  static _ reportError(/* No info */) {
    // ** addr: 0x63f6cc, size: 0x6c
    // 0x63f6cc: EnterFrame
    //     0x63f6cc: stp             fp, lr, [SP, #-0x10]!
    //     0x63f6d0: mov             fp, SP
    // 0x63f6d4: AllocStack(0x18)
    //     0x63f6d4: sub             SP, SP, #0x18
    // 0x63f6d8: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x63f6d8: stur            x1, [fp, #-8]
    // 0x63f6dc: CheckStackOverflow
    //     0x63f6dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63f6e0: cmp             SP, x16
    //     0x63f6e4: b.ls            #0x63f730
    // 0x63f6e8: r0 = InitLateStaticField(0x640) // [package:flutter/src/foundation/assertions.dart] FlutterError::onError
    //     0x63f6e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63f6ec: ldr             x0, [x0, #0xc80]
    //     0x63f6f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x63f6f4: cmp             w0, w16
    //     0x63f6f8: b.ne            #0x63f704
    //     0x63f6fc: ldr             x2, [PP, #0x458]  ; [pp+0x458] Field <FlutterError.onError>: static late (offset: 0x640)
    //     0x63f700: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x63f704: cmp             w0, NULL
    // 0x63f708: b.eq            #0x63f720
    // 0x63f70c: ldur            x16, [fp, #-8]
    // 0x63f710: stp             x16, x0, [SP]
    // 0x63f714: ClosureCall
    //     0x63f714: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x63f718: ldur            x2, [x0, #0x1f]
    //     0x63f71c: blr             x2
    // 0x63f720: r0 = Null
    //     0x63f720: mov             x0, NULL
    // 0x63f724: LeaveFrame
    //     0x63f724: mov             SP, fp
    //     0x63f728: ldp             fp, lr, [SP], #0x10
    // 0x63f72c: ret
    //     0x63f72c: ret             
    // 0x63f730: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63f730: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63f734: b               #0x63f6e8
  }
  static ((dynamic, FlutterErrorDetails) => void)? onError() {
    // ** addr: 0x63f738, size: 0x48
    // 0x63f738: EnterFrame
    //     0x63f738: stp             fp, lr, [SP, #-0x10]!
    //     0x63f73c: mov             fp, SP
    // 0x63f740: CheckStackOverflow
    //     0x63f740: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63f744: cmp             SP, x16
    //     0x63f748: b.ls            #0x63f778
    // 0x63f74c: r0 = InitLateStaticField(0x648) // [package:flutter/src/foundation/assertions.dart] FlutterError::presentError
    //     0x63f74c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63f750: ldr             x0, [x0, #0xc90]
    //     0x63f754: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x63f758: cmp             w0, w16
    //     0x63f75c: b.ne            #0x63f768
    //     0x63f760: ldr             x2, [PP, #0x468]  ; [pp+0x468] Field <FlutterError.presentError>: static late (offset: 0x648)
    //     0x63f764: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x63f768: r0 = Closure: (FlutterErrorDetails, {bool forceReport}) => void from Function 'dumpErrorToConsole': static.
    //     0x63f768: ldr             x0, [PP, #0x470]  ; [pp+0x470] Closure: (FlutterErrorDetails, {bool forceReport}) => void from Function 'dumpErrorToConsole': static. (0x7e54fb03f780)
    // 0x63f76c: LeaveFrame
    //     0x63f76c: mov             SP, fp
    //     0x63f770: ldp             fp, lr, [SP], #0x10
    // 0x63f774: ret
    //     0x63f774: ret             
    // 0x63f778: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63f778: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63f77c: b               #0x63f74c
  }
  [closure] static void dumpErrorToConsole(dynamic, FlutterErrorDetails, {bool forceReport}) {
    // ** addr: 0x63f780, size: 0x7c
    // 0x63f780: EnterFrame
    //     0x63f780: stp             fp, lr, [SP, #-0x10]!
    //     0x63f784: mov             fp, SP
    // 0x63f788: AllocStack(0x8)
    //     0x63f788: sub             SP, SP, #8
    // 0x63f78c: SetupParameters(dynamic _ /* r2 */, {dynamic forceReport = false /* r0 */})
    //     0x63f78c: ldur            w0, [x4, #0x13]
    //     0x63f790: sub             x1, x0, #4
    //     0x63f794: add             x2, fp, w1, sxtw #2
    //     0x63f798: ldr             x2, [x2, #0x10]
    //     0x63f79c: ldur            w1, [x4, #0x1f]
    //     0x63f7a0: add             x1, x1, HEAP, lsl #32
    //     0x63f7a4: ldr             x16, [PP, #0x478]  ; [pp+0x478] "forceReport"
    //     0x63f7a8: cmp             w1, w16
    //     0x63f7ac: b.ne            #0x63f7c8
    //     0x63f7b0: ldur            w1, [x4, #0x23]
    //     0x63f7b4: add             x1, x1, HEAP, lsl #32
    //     0x63f7b8: sub             w3, w0, w1
    //     0x63f7bc: add             x0, fp, w3, sxtw #2
    //     0x63f7c0: ldr             x0, [x0, #8]
    //     0x63f7c4: b               #0x63f7cc
    //     0x63f7c8: add             x0, NULL, #0x30  ; false
    // 0x63f7cc: CheckStackOverflow
    //     0x63f7cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63f7d0: cmp             SP, x16
    //     0x63f7d4: b.ls            #0x63f7f4
    // 0x63f7d8: str             x0, [SP]
    // 0x63f7dc: mov             x1, x2
    // 0x63f7e0: r4 = const [0, 0x2, 0x1, 0x1, forceReport, 0x1, null]
    //     0x63f7e0: ldr             x4, [PP, #0x480]  ; [pp+0x480] List(7) [0, 0x2, 0x1, 0x1, "forceReport", 0x1, Null]
    // 0x63f7e4: r0 = dumpErrorToConsole()
    //     0x63f7e4: bl              #0x63f7fc  ; [package:flutter/src/foundation/assertions.dart] FlutterError::dumpErrorToConsole
    // 0x63f7e8: LeaveFrame
    //     0x63f7e8: mov             SP, fp
    //     0x63f7ec: ldp             fp, lr, [SP], #0x10
    // 0x63f7f0: ret
    //     0x63f7f0: ret             
    // 0x63f7f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63f7f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63f7f8: b               #0x63f7d8
  }
  static _ dumpErrorToConsole(/* No info */) {
    // ** addr: 0x63f7fc, size: 0x1a0
    // 0x63f7fc: EnterFrame
    //     0x63f7fc: stp             fp, lr, [SP, #-0x10]!
    //     0x63f800: mov             fp, SP
    // 0x63f804: AllocStack(0x18)
    //     0x63f804: sub             SP, SP, #0x18
    // 0x63f808: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */, {dynamic forceReport = false /* r0 */})
    //     0x63f808: stur            x1, [fp, #-0x10]
    //     0x63f80c: ldur            w0, [x4, #0x13]
    //     0x63f810: ldur            w2, [x4, #0x1f]
    //     0x63f814: add             x2, x2, HEAP, lsl #32
    //     0x63f818: ldr             x16, [PP, #0x478]  ; [pp+0x478] "forceReport"
    //     0x63f81c: cmp             w2, w16
    //     0x63f820: b.ne            #0x63f83c
    //     0x63f824: ldur            w2, [x4, #0x23]
    //     0x63f828: add             x2, x2, HEAP, lsl #32
    //     0x63f82c: sub             w3, w0, w2
    //     0x63f830: add             x0, fp, w3, sxtw #2
    //     0x63f834: ldr             x0, [x0, #8]
    //     0x63f838: b               #0x63f840
    //     0x63f83c: add             x0, NULL, #0x30  ; false
    // 0x63f840: CheckStackOverflow
    //     0x63f840: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63f844: cmp             SP, x16
    //     0x63f848: b.ls            #0x63f994
    // 0x63f84c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x63f84c: ldur            w2, [x1, #0x17]
    // 0x63f850: DecompressPointer r2
    //     0x63f850: add             x2, x2, HEAP, lsl #32
    // 0x63f854: eor             x3, x2, #0x10
    // 0x63f858: tbz             w3, #4, #0x63f870
    // 0x63f85c: tbz             w0, #4, #0x63f870
    // 0x63f860: r0 = Null
    //     0x63f860: mov             x0, NULL
    // 0x63f864: LeaveFrame
    //     0x63f864: mov             SP, fp
    //     0x63f868: ldp             fp, lr, [SP], #0x10
    // 0x63f86c: ret
    //     0x63f86c: ret             
    // 0x63f870: r2 = LoadStaticField(0x64c)
    //     0x63f870: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x63f874: ldr             x2, [x2, #0xc98]
    // 0x63f878: cbz             w2, #0x63f880
    // 0x63f87c: tbnz            w0, #4, #0x63f8d0
    // 0x63f880: LoadField: r2 = r1->field_b
    //     0x63f880: ldur            w2, [x1, #0xb]
    // 0x63f884: DecompressPointer r2
    //     0x63f884: add             x2, x2, HEAP, lsl #32
    // 0x63f888: stur            x2, [fp, #-8]
    // 0x63f88c: LoadField: r0 = r1->field_7
    //     0x63f88c: ldur            w0, [x1, #7]
    // 0x63f890: DecompressPointer r0
    //     0x63f890: add             x0, x0, HEAP, lsl #32
    // 0x63f894: r1 = 60
    //     0x63f894: movz            x1, #0x3c
    // 0x63f898: branchIfSmi(r0, 0x63f8a4)
    //     0x63f898: tbz             w0, #0, #0x63f8a4
    // 0x63f89c: r1 = LoadClassIdInstr(r0)
    //     0x63f89c: ldur            x1, [x0, #-1]
    //     0x63f8a0: ubfx            x1, x1, #0xc, #0x14
    // 0x63f8a4: str             x0, [SP]
    // 0x63f8a8: mov             x0, x1
    // 0x63f8ac: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x63f8ac: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x63f8b0: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x63f8b0: movz            x17, #0x2b03
    //     0x63f8b4: add             lr, x0, x17
    //     0x63f8b8: ldr             lr, [x21, lr, lsl #3]
    //     0x63f8bc: blr             lr
    // 0x63f8c0: mov             x1, x0
    // 0x63f8c4: ldur            x2, [fp, #-8]
    // 0x63f8c8: r0 = debugPrintStack()
    //     0x63f8c8: bl              #0x642508  ; [package:flutter/src/foundation/assertions.dart] ::debugPrintStack
    // 0x63f8cc: b               #0x63f950
    // 0x63f8d0: r0 = InitLateStaticField(0x674) // [package:flutter/src/foundation/print.dart] ::debugPrint
    //     0x63f8d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63f8d4: ldr             x0, [x0, #0xce8]
    //     0x63f8d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x63f8dc: cmp             w0, w16
    //     0x63f8e0: b.ne            #0x63f8ec
    //     0x63f8e4: ldr             x2, [PP, #0x490]  ; [pp+0x490] Field <::.debugPrint>: static late (offset: 0x674)
    //     0x63f8e8: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x63f8ec: r1 = Null
    //     0x63f8ec: mov             x1, NULL
    // 0x63f8f0: r2 = 4
    //     0x63f8f0: movz            x2, #0x4
    // 0x63f8f4: r0 = AllocateArray()
    //     0x63f8f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x63f8f8: stur            x0, [fp, #-8]
    // 0x63f8fc: r16 = "Another exception was thrown: "
    //     0x63f8fc: ldr             x16, [PP, #0x498]  ; [pp+0x498] "Another exception was thrown: "
    // 0x63f900: StoreField: r0->field_f = r16
    //     0x63f900: stur            w16, [x0, #0xf]
    // 0x63f904: ldur            x1, [fp, #-0x10]
    // 0x63f908: r0 = summary()
    //     0x63f908: bl              #0x641ad4  ; [package:flutter/src/foundation/assertions.dart] FlutterErrorDetails::summary
    // 0x63f90c: ldur            x1, [fp, #-8]
    // 0x63f910: ArrayStore: r1[1] = r0  ; List_4
    //     0x63f910: add             x25, x1, #0x13
    //     0x63f914: str             w0, [x25]
    //     0x63f918: tbz             w0, #0, #0x63f934
    //     0x63f91c: ldurb           w16, [x1, #-1]
    //     0x63f920: ldurb           w17, [x0, #-1]
    //     0x63f924: and             x16, x17, x16, lsr #2
    //     0x63f928: tst             x16, HEAP, lsr #32
    //     0x63f92c: b.eq            #0x63f934
    //     0x63f930: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x63f934: ldur            x16, [fp, #-8]
    // 0x63f938: str             x16, [SP]
    // 0x63f93c: r0 = _interpolate()
    //     0x63f93c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x63f940: str             NULL, [SP]
    // 0x63f944: mov             x1, x0
    // 0x63f948: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0x63f948: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0x63f94c: r0 = debugPrintThrottled()
    //     0x63f94c: bl              #0x63fa18  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0x63f950: r2 = LoadStaticField(0x64c)
    //     0x63f950: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x63f954: ldr             x2, [x2, #0xc98]
    // 0x63f958: r3 = LoadInt32Instr(r2)
    //     0x63f958: sbfx            x3, x2, #1, #0x1f
    //     0x63f95c: tbz             w2, #0, #0x63f964
    //     0x63f960: ldur            x3, [x2, #7]
    // 0x63f964: add             x2, x3, #1
    // 0x63f968: r0 = BoxInt64Instr(r2)
    //     0x63f968: sbfiz           x0, x2, #1, #0x1f
    //     0x63f96c: cmp             x2, x0, asr #1
    //     0x63f970: b.eq            #0x63f97c
    //     0x63f974: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x63f978: stur            x2, [x0, #7]
    // 0x63f97c: StoreStaticField(0x64c, r0)
    //     0x63f97c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x63f980: str             x0, [x1, #0xc98]
    // 0x63f984: r0 = Null
    //     0x63f984: mov             x0, NULL
    // 0x63f988: LeaveFrame
    //     0x63f988: mov             SP, fp
    //     0x63f98c: ldp             fp, lr, [SP], #0x10
    // 0x63f990: ret
    //     0x63f990: ret             
    // 0x63f994: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63f994: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63f998: b               #0x63f84c
  }
  static _ defaultStackFilter(/* No info */) {
    // ** addr: 0x642628, size: 0x1128
    // 0x642628: EnterFrame
    //     0x642628: stp             fp, lr, [SP, #-0x10]!
    //     0x64262c: mov             fp, SP
    // 0x642630: AllocStack(0xa0)
    //     0x642630: sub             SP, SP, #0xa0
    // 0x642634: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x642634: mov             x0, x1
    //     0x642638: stur            x1, [fp, #-8]
    // 0x64263c: CheckStackOverflow
    //     0x64263c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x642640: cmp             SP, x16
    //     0x642644: b.ls            #0x6436c8
    // 0x642648: r1 = Null
    //     0x642648: mov             x1, NULL
    // 0x64264c: r2 = 32
    //     0x64264c: movz            x2, #0x20
    // 0x642650: r0 = AllocateArray()
    //     0x642650: bl              #0xec22fc  ; AllocateArrayStub
    // 0x642654: r16 = "dart:async-patch"
    //     0x642654: ldr             x16, [PP, #0x8d0]  ; [pp+0x8d0] "dart:async-patch"
    // 0x642658: StoreField: r0->field_f = r16
    //     0x642658: stur            w16, [x0, #0xf]
    // 0x64265c: StoreField: r0->field_13 = rZR
    //     0x64265c: stur            wzr, [x0, #0x13]
    // 0x642660: r16 = "dart:async"
    //     0x642660: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] "dart:async"
    // 0x642664: ArrayStore: r0[0] = r16  ; List_4
    //     0x642664: stur            w16, [x0, #0x17]
    // 0x642668: StoreField: r0->field_1b = rZR
    //     0x642668: stur            wzr, [x0, #0x1b]
    // 0x64266c: r16 = "package:stack_trace"
    //     0x64266c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] "package:stack_trace"
    // 0x642670: StoreField: r0->field_1f = r16
    //     0x642670: stur            w16, [x0, #0x1f]
    // 0x642674: StoreField: r0->field_23 = rZR
    //     0x642674: stur            wzr, [x0, #0x23]
    // 0x642678: r16 = "class _AssertionError"
    //     0x642678: ldr             x16, [PP, #0x8e8]  ; [pp+0x8e8] "class _AssertionError"
    // 0x64267c: StoreField: r0->field_27 = r16
    //     0x64267c: stur            w16, [x0, #0x27]
    // 0x642680: StoreField: r0->field_2b = rZR
    //     0x642680: stur            wzr, [x0, #0x2b]
    // 0x642684: r16 = "class _FakeAsync"
    //     0x642684: ldr             x16, [PP, #0x8f0]  ; [pp+0x8f0] "class _FakeAsync"
    // 0x642688: StoreField: r0->field_2f = r16
    //     0x642688: stur            w16, [x0, #0x2f]
    // 0x64268c: StoreField: r0->field_33 = rZR
    //     0x64268c: stur            wzr, [x0, #0x33]
    // 0x642690: r16 = "class _FrameCallbackEntry"
    //     0x642690: ldr             x16, [PP, #0x8f8]  ; [pp+0x8f8] "class _FrameCallbackEntry"
    // 0x642694: StoreField: r0->field_37 = r16
    //     0x642694: stur            w16, [x0, #0x37]
    // 0x642698: StoreField: r0->field_3b = rZR
    //     0x642698: stur            wzr, [x0, #0x3b]
    // 0x64269c: r16 = "class _Timer"
    //     0x64269c: ldr             x16, [PP, #0x900]  ; [pp+0x900] "class _Timer"
    // 0x6426a0: StoreField: r0->field_3f = r16
    //     0x6426a0: stur            w16, [x0, #0x3f]
    // 0x6426a4: StoreField: r0->field_43 = rZR
    //     0x6426a4: stur            wzr, [x0, #0x43]
    // 0x6426a8: r16 = "class _RawReceivePortImpl"
    //     0x6426a8: ldr             x16, [PP, #0x908]  ; [pp+0x908] "class _RawReceivePortImpl"
    // 0x6426ac: StoreField: r0->field_47 = r16
    //     0x6426ac: stur            w16, [x0, #0x47]
    // 0x6426b0: StoreField: r0->field_4b = rZR
    //     0x6426b0: stur            wzr, [x0, #0x4b]
    // 0x6426b4: r16 = <String, int>
    //     0x6426b4: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0x6426b8: stp             x0, x16, [SP]
    // 0x6426bc: r0 = Map._fromLiteral()
    //     0x6426bc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x6426c0: mov             x2, x0
    // 0x6426c4: ldur            x1, [fp, #-8]
    // 0x6426c8: stur            x2, [fp, #-0x10]
    // 0x6426cc: r0 = LoadClassIdInstr(r1)
    //     0x6426cc: ldur            x0, [x1, #-1]
    //     0x6426d0: ubfx            x0, x0, #0xc, #0x14
    // 0x6426d4: r16 = "\n"
    //     0x6426d4: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0x6426d8: str             x16, [SP]
    // 0x6426dc: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x6426dc: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x6426e0: r0 = GDT[cid_x0 + 0xf18c]()
    //     0x6426e0: movz            x17, #0xf18c
    //     0x6426e4: add             lr, x0, x17
    //     0x6426e8: ldr             lr, [x21, lr, lsl #3]
    //     0x6426ec: blr             lr
    // 0x6426f0: mov             x1, x0
    // 0x6426f4: r0 = fromStackString()
    //     0x6426f4: bl              #0x643988  ; [package:flutter/src/foundation/stack_frame.dart] StackFrame::fromStackString
    // 0x6426f8: stur            x0, [fp, #-0x30]
    // 0x6426fc: LoadField: r3 = r0->field_7
    //     0x6426fc: ldur            w3, [x0, #7]
    // 0x642700: DecompressPointer r3
    //     0x642700: add             x3, x3, HEAP, lsl #32
    // 0x642704: stur            x3, [fp, #-0x28]
    // 0x642708: r6 = 0
    //     0x642708: movz            x6, #0
    // 0x64270c: r5 = 0
    //     0x64270c: movz            x5, #0
    // 0x642710: ldur            x4, [fp, #-0x10]
    // 0x642714: stur            x6, [fp, #-0x18]
    // 0x642718: stur            x5, [fp, #-0x20]
    // 0x64271c: CheckStackOverflow
    //     0x64271c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x642720: cmp             SP, x16
    //     0x642724: b.ls            #0x6436d0
    // 0x642728: LoadField: r2 = r0->field_b
    //     0x642728: ldur            w2, [x0, #0xb]
    // 0x64272c: r7 = LoadInt32Instr(r2)
    //     0x64272c: sbfx            x7, x2, #1, #0x1f
    // 0x642730: stur            x7, [fp, #-0x80]
    // 0x642734: cmp             x5, x7
    // 0x642738: b.ge            #0x642d34
    // 0x64273c: LoadField: r1 = r0->field_f
    //     0x64273c: ldur            w1, [x0, #0xf]
    // 0x642740: DecompressPointer r1
    //     0x642740: add             x1, x1, HEAP, lsl #32
    // 0x642744: ArrayLoad: r7 = r1[r5]  ; Unknown_4
    //     0x642744: add             x16, x1, x5, lsl #2
    //     0x642748: ldur            w7, [x16, #0xf]
    // 0x64274c: DecompressPointer r7
    //     0x64274c: add             x7, x7, HEAP, lsl #32
    // 0x642750: stur            x7, [fp, #-8]
    // 0x642754: r1 = Null
    //     0x642754: mov             x1, NULL
    // 0x642758: r2 = 4
    //     0x642758: movz            x2, #0x4
    // 0x64275c: r0 = AllocateArray()
    //     0x64275c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x642760: r16 = "class "
    //     0x642760: ldr             x16, [PP, #0x918]  ; [pp+0x918] "class "
    // 0x642764: StoreField: r0->field_f = r16
    //     0x642764: stur            w16, [x0, #0xf]
    // 0x642768: ldur            x1, [fp, #-8]
    // 0x64276c: LoadField: r2 = r1->field_2f
    //     0x64276c: ldur            w2, [x1, #0x2f]
    // 0x642770: DecompressPointer r2
    //     0x642770: add             x2, x2, HEAP, lsl #32
    // 0x642774: StoreField: r0->field_13 = r2
    //     0x642774: stur            w2, [x0, #0x13]
    // 0x642778: str             x0, [SP]
    // 0x64277c: r0 = _interpolate()
    //     0x64277c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x642780: mov             x3, x0
    // 0x642784: ldur            x0, [fp, #-8]
    // 0x642788: stur            x3, [fp, #-0x40]
    // 0x64278c: LoadField: r4 = r0->field_13
    //     0x64278c: ldur            w4, [x0, #0x13]
    // 0x642790: DecompressPointer r4
    //     0x642790: add             x4, x4, HEAP, lsl #32
    // 0x642794: stur            x4, [fp, #-0x38]
    // 0x642798: r1 = Null
    //     0x642798: mov             x1, NULL
    // 0x64279c: r2 = 6
    //     0x64279c: movz            x2, #0x6
    // 0x6427a0: r0 = AllocateArray()
    //     0x6427a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6427a4: mov             x1, x0
    // 0x6427a8: ldur            x0, [fp, #-0x38]
    // 0x6427ac: StoreField: r1->field_f = r0
    //     0x6427ac: stur            w0, [x1, #0xf]
    // 0x6427b0: r16 = ":"
    //     0x6427b0: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0x6427b4: StoreField: r1->field_13 = r16
    //     0x6427b4: stur            w16, [x1, #0x13]
    // 0x6427b8: ldur            x0, [fp, #-8]
    // 0x6427bc: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x6427bc: ldur            w2, [x0, #0x17]
    // 0x6427c0: DecompressPointer r2
    //     0x6427c0: add             x2, x2, HEAP, lsl #32
    // 0x6427c4: ArrayStore: r1[0] = r2  ; List_4
    //     0x6427c4: stur            w2, [x1, #0x17]
    // 0x6427c8: str             x1, [SP]
    // 0x6427cc: r0 = _interpolate()
    //     0x6427cc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x6427d0: mov             x3, x0
    // 0x6427d4: ldur            x0, [fp, #-0x10]
    // 0x6427d8: stur            x3, [fp, #-0x38]
    // 0x6427dc: LoadField: r4 = r0->field_f
    //     0x6427dc: ldur            w4, [x0, #0xf]
    // 0x6427e0: DecompressPointer r4
    //     0x6427e0: add             x4, x4, HEAP, lsl #32
    // 0x6427e4: mov             x1, x0
    // 0x6427e8: ldur            x2, [fp, #-0x40]
    // 0x6427ec: stur            x4, [fp, #-8]
    // 0x6427f0: r0 = _getValueOrData()
    //     0x6427f0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x6427f4: mov             x1, x0
    // 0x6427f8: ldur            x0, [fp, #-8]
    // 0x6427fc: cmp             w0, w1
    // 0x642800: b.eq            #0x642a70
    // 0x642804: ldur            x0, [fp, #-0x30]
    // 0x642808: ldur            x4, [fp, #-0x18]
    // 0x64280c: ldur            x3, [fp, #-0x20]
    // 0x642810: add             x5, x4, #1
    // 0x642814: stur            x5, [fp, #-0x48]
    // 0x642818: r1 = Function '<anonymous closure>': static.
    //     0x642818: ldr             x1, [PP, #0x928]  ; [pp+0x928] AnonymousClosure: static (0x644724), in [package:flutter/src/foundation/assertions.dart] FlutterError::defaultStackFilter (0x642628)
    // 0x64281c: r2 = Null
    //     0x64281c: mov             x2, NULL
    // 0x642820: r0 = AllocateClosure()
    //     0x642820: bl              #0xec1630  ; AllocateClosureStub
    // 0x642824: ldur            x1, [fp, #-0x10]
    // 0x642828: ldur            x2, [fp, #-0x40]
    // 0x64282c: mov             x3, x0
    // 0x642830: r0 = update()
    //     0x642830: bl              #0x6437f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::update
    // 0x642834: ldur            x3, [fp, #-0x30]
    // 0x642838: LoadField: r0 = r3->field_b
    //     0x642838: ldur            w0, [x3, #0xb]
    // 0x64283c: r4 = LoadInt32Instr(r0)
    //     0x64283c: sbfx            x4, x0, #1, #0x1f
    // 0x642840: mov             x0, x4
    // 0x642844: ldur            x1, [fp, #-0x20]
    // 0x642848: stur            x4, [fp, #-0x70]
    // 0x64284c: cmp             x1, x0
    // 0x642850: b.hs            #0x6436d8
    // 0x642854: LoadField: r5 = r3->field_f
    //     0x642854: ldur            w5, [x3, #0xf]
    // 0x642858: DecompressPointer r5
    //     0x642858: add             x5, x5, HEAP, lsl #32
    // 0x64285c: stur            x5, [fp, #-0x40]
    // 0x642860: sub             x6, x4, #1
    // 0x642864: ldur            x7, [fp, #-0x20]
    // 0x642868: stur            x6, [fp, #-0x68]
    // 0x64286c: cmp             x7, x6
    // 0x642870: b.ge            #0x642a4c
    // 0x642874: add             x8, x7, #1
    // 0x642878: stur            x8, [fp, #-0x60]
    // 0x64287c: sub             x0, x6, x7
    // 0x642880: cmp             x8, x7
    // 0x642884: b.ge            #0x642974
    // 0x642888: add             x1, x8, x0
    // 0x64288c: sub             x2, x1, #1
    // 0x642890: add             x1, x7, x0
    // 0x642894: sub             x0, x1, #1
    // 0x642898: mov             x10, x2
    // 0x64289c: mov             x9, x0
    // 0x6428a0: stur            x10, [fp, #-0x50]
    // 0x6428a4: stur            x9, [fp, #-0x58]
    // 0x6428a8: CheckStackOverflow
    //     0x6428a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6428ac: cmp             SP, x16
    //     0x6428b0: b.ls            #0x6436dc
    // 0x6428b4: cmp             x10, x8
    // 0x6428b8: b.lt            #0x642a4c
    // 0x6428bc: mov             x0, x4
    // 0x6428c0: mov             x1, x10
    // 0x6428c4: cmp             x1, x0
    // 0x6428c8: b.hs            #0x6436e4
    // 0x6428cc: ArrayLoad: r11 = r5[r10]  ; Unknown_4
    //     0x6428cc: add             x16, x5, x10, lsl #2
    //     0x6428d0: ldur            w11, [x16, #0xf]
    // 0x6428d4: DecompressPointer r11
    //     0x6428d4: add             x11, x11, HEAP, lsl #32
    // 0x6428d8: mov             x0, x11
    // 0x6428dc: ldur            x2, [fp, #-0x28]
    // 0x6428e0: stur            x11, [fp, #-8]
    // 0x6428e4: r1 = Null
    //     0x6428e4: mov             x1, NULL
    // 0x6428e8: cmp             w2, NULL
    // 0x6428ec: b.eq            #0x642908
    // 0x6428f0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6428f0: ldur            w4, [x2, #0x17]
    // 0x6428f4: DecompressPointer r4
    //     0x6428f4: add             x4, x4, HEAP, lsl #32
    // 0x6428f8: r8 = X0
    //     0x6428f8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6428fc: LoadField: r9 = r4->field_7
    //     0x6428fc: ldur            x9, [x4, #7]
    // 0x642900: r3 = Null
    //     0x642900: ldr             x3, [PP, #0x930]  ; [pp+0x930] Null
    // 0x642904: blr             x9
    // 0x642908: ldur            x0, [fp, #-0x70]
    // 0x64290c: ldur            x1, [fp, #-0x58]
    // 0x642910: cmp             x1, x0
    // 0x642914: b.hs            #0x6436e8
    // 0x642918: ldur            x1, [fp, #-0x40]
    // 0x64291c: ldur            x0, [fp, #-8]
    // 0x642920: ldur            x2, [fp, #-0x58]
    // 0x642924: ArrayStore: r1[r2] = r0  ; List_4
    //     0x642924: add             x25, x1, x2, lsl #2
    //     0x642928: add             x25, x25, #0xf
    //     0x64292c: str             w0, [x25]
    //     0x642930: tbz             w0, #0, #0x64294c
    //     0x642934: ldurb           w16, [x1, #-1]
    //     0x642938: ldurb           w17, [x0, #-1]
    //     0x64293c: and             x16, x17, x16, lsr #2
    //     0x642940: tst             x16, HEAP, lsr #32
    //     0x642944: b.eq            #0x64294c
    //     0x642948: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x64294c: ldur            x0, [fp, #-0x50]
    // 0x642950: sub             x10, x0, #1
    // 0x642954: sub             x9, x2, #1
    // 0x642958: ldur            x3, [fp, #-0x30]
    // 0x64295c: ldur            x7, [fp, #-0x20]
    // 0x642960: ldur            x6, [fp, #-0x68]
    // 0x642964: ldur            x8, [fp, #-0x60]
    // 0x642968: ldur            x5, [fp, #-0x40]
    // 0x64296c: ldur            x4, [fp, #-0x70]
    // 0x642970: b               #0x6428a0
    // 0x642974: mov             x1, x8
    // 0x642978: add             x3, x1, x0
    // 0x64297c: stur            x3, [fp, #-0x78]
    // 0x642980: mov             x6, x1
    // 0x642984: ldur            x5, [fp, #-0x20]
    // 0x642988: ldur            x4, [fp, #-0x40]
    // 0x64298c: stur            x6, [fp, #-0x50]
    // 0x642990: stur            x5, [fp, #-0x58]
    // 0x642994: CheckStackOverflow
    //     0x642994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x642998: cmp             SP, x16
    //     0x64299c: b.ls            #0x6436ec
    // 0x6429a0: cmp             x6, x3
    // 0x6429a4: b.ge            #0x642a4c
    // 0x6429a8: ldur            x0, [fp, #-0x70]
    // 0x6429ac: mov             x1, x6
    // 0x6429b0: cmp             x1, x0
    // 0x6429b4: b.hs            #0x6436f4
    // 0x6429b8: ArrayLoad: r7 = r4[r6]  ; Unknown_4
    //     0x6429b8: add             x16, x4, x6, lsl #2
    //     0x6429bc: ldur            w7, [x16, #0xf]
    // 0x6429c0: DecompressPointer r7
    //     0x6429c0: add             x7, x7, HEAP, lsl #32
    // 0x6429c4: mov             x0, x7
    // 0x6429c8: ldur            x2, [fp, #-0x28]
    // 0x6429cc: stur            x7, [fp, #-8]
    // 0x6429d0: r1 = Null
    //     0x6429d0: mov             x1, NULL
    // 0x6429d4: cmp             w2, NULL
    // 0x6429d8: b.eq            #0x6429f4
    // 0x6429dc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6429dc: ldur            w4, [x2, #0x17]
    // 0x6429e0: DecompressPointer r4
    //     0x6429e0: add             x4, x4, HEAP, lsl #32
    // 0x6429e4: r8 = X0
    //     0x6429e4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6429e8: LoadField: r9 = r4->field_7
    //     0x6429e8: ldur            x9, [x4, #7]
    // 0x6429ec: r3 = Null
    //     0x6429ec: ldr             x3, [PP, #0x940]  ; [pp+0x940] Null
    // 0x6429f0: blr             x9
    // 0x6429f4: ldur            x0, [fp, #-0x70]
    // 0x6429f8: ldur            x1, [fp, #-0x58]
    // 0x6429fc: cmp             x1, x0
    // 0x642a00: b.hs            #0x6436f8
    // 0x642a04: ldur            x1, [fp, #-0x40]
    // 0x642a08: ldur            x0, [fp, #-8]
    // 0x642a0c: ldur            x2, [fp, #-0x58]
    // 0x642a10: ArrayStore: r1[r2] = r0  ; List_4
    //     0x642a10: add             x25, x1, x2, lsl #2
    //     0x642a14: add             x25, x25, #0xf
    //     0x642a18: str             w0, [x25]
    //     0x642a1c: tbz             w0, #0, #0x642a38
    //     0x642a20: ldurb           w16, [x1, #-1]
    //     0x642a24: ldurb           w17, [x0, #-1]
    //     0x642a28: and             x16, x17, x16, lsr #2
    //     0x642a2c: tst             x16, HEAP, lsr #32
    //     0x642a30: b.eq            #0x642a38
    //     0x642a34: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x642a38: ldur            x0, [fp, #-0x50]
    // 0x642a3c: add             x6, x0, #1
    // 0x642a40: add             x5, x2, #1
    // 0x642a44: ldur            x3, [fp, #-0x78]
    // 0x642a48: b               #0x642988
    // 0x642a4c: ldur            x0, [fp, #-0x20]
    // 0x642a50: ldur            x1, [fp, #-0x30]
    // 0x642a54: ldur            x2, [fp, #-0x68]
    // 0x642a58: r0 = length=()
    //     0x642a58: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0x642a5c: ldur            x0, [fp, #-0x20]
    // 0x642a60: sub             x1, x0, #1
    // 0x642a64: ldur            x6, [fp, #-0x48]
    // 0x642a68: mov             x0, x1
    // 0x642a6c: b               #0x642d24
    // 0x642a70: ldur            x3, [fp, #-0x10]
    // 0x642a74: ldur            x4, [fp, #-0x18]
    // 0x642a78: ldur            x0, [fp, #-0x20]
    // 0x642a7c: LoadField: r5 = r3->field_f
    //     0x642a7c: ldur            w5, [x3, #0xf]
    // 0x642a80: DecompressPointer r5
    //     0x642a80: add             x5, x5, HEAP, lsl #32
    // 0x642a84: mov             x1, x3
    // 0x642a88: ldur            x2, [fp, #-0x38]
    // 0x642a8c: stur            x5, [fp, #-8]
    // 0x642a90: r0 = _getValueOrData()
    //     0x642a90: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x642a94: mov             x1, x0
    // 0x642a98: ldur            x0, [fp, #-8]
    // 0x642a9c: cmp             w0, w1
    // 0x642aa0: b.eq            #0x642d0c
    // 0x642aa4: ldur            x4, [fp, #-0x30]
    // 0x642aa8: ldur            x0, [fp, #-0x18]
    // 0x642aac: ldur            x3, [fp, #-0x20]
    // 0x642ab0: add             x5, x0, #1
    // 0x642ab4: stur            x5, [fp, #-0x48]
    // 0x642ab8: r1 = Function '<anonymous closure>': static.
    //     0x642ab8: ldr             x1, [PP, #0x950]  ; [pp+0x950] AnonymousClosure: static (0x644724), in [package:flutter/src/foundation/assertions.dart] FlutterError::defaultStackFilter (0x642628)
    // 0x642abc: r2 = Null
    //     0x642abc: mov             x2, NULL
    // 0x642ac0: r0 = AllocateClosure()
    //     0x642ac0: bl              #0xec1630  ; AllocateClosureStub
    // 0x642ac4: ldur            x1, [fp, #-0x10]
    // 0x642ac8: ldur            x2, [fp, #-0x38]
    // 0x642acc: mov             x3, x0
    // 0x642ad0: r0 = update()
    //     0x642ad0: bl              #0x6437f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::update
    // 0x642ad4: ldur            x3, [fp, #-0x30]
    // 0x642ad8: LoadField: r0 = r3->field_b
    //     0x642ad8: ldur            w0, [x3, #0xb]
    // 0x642adc: r4 = LoadInt32Instr(r0)
    //     0x642adc: sbfx            x4, x0, #1, #0x1f
    // 0x642ae0: mov             x0, x4
    // 0x642ae4: ldur            x1, [fp, #-0x20]
    // 0x642ae8: stur            x4, [fp, #-0x70]
    // 0x642aec: cmp             x1, x0
    // 0x642af0: b.hs            #0x6436fc
    // 0x642af4: LoadField: r5 = r3->field_f
    //     0x642af4: ldur            w5, [x3, #0xf]
    // 0x642af8: DecompressPointer r5
    //     0x642af8: add             x5, x5, HEAP, lsl #32
    // 0x642afc: stur            x5, [fp, #-0x38]
    // 0x642b00: sub             x6, x4, #1
    // 0x642b04: ldur            x7, [fp, #-0x20]
    // 0x642b08: stur            x6, [fp, #-0x68]
    // 0x642b0c: cmp             x7, x6
    // 0x642b10: b.ge            #0x642cec
    // 0x642b14: add             x8, x7, #1
    // 0x642b18: stur            x8, [fp, #-0x60]
    // 0x642b1c: sub             x0, x6, x7
    // 0x642b20: cmp             x8, x7
    // 0x642b24: b.ge            #0x642c14
    // 0x642b28: add             x1, x8, x0
    // 0x642b2c: sub             x2, x1, #1
    // 0x642b30: add             x1, x7, x0
    // 0x642b34: sub             x0, x1, #1
    // 0x642b38: mov             x10, x2
    // 0x642b3c: mov             x9, x0
    // 0x642b40: stur            x10, [fp, #-0x50]
    // 0x642b44: stur            x9, [fp, #-0x58]
    // 0x642b48: CheckStackOverflow
    //     0x642b48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x642b4c: cmp             SP, x16
    //     0x642b50: b.ls            #0x643700
    // 0x642b54: cmp             x10, x8
    // 0x642b58: b.lt            #0x642cec
    // 0x642b5c: mov             x0, x4
    // 0x642b60: mov             x1, x10
    // 0x642b64: cmp             x1, x0
    // 0x642b68: b.hs            #0x643708
    // 0x642b6c: ArrayLoad: r11 = r5[r10]  ; Unknown_4
    //     0x642b6c: add             x16, x5, x10, lsl #2
    //     0x642b70: ldur            w11, [x16, #0xf]
    // 0x642b74: DecompressPointer r11
    //     0x642b74: add             x11, x11, HEAP, lsl #32
    // 0x642b78: mov             x0, x11
    // 0x642b7c: ldur            x2, [fp, #-0x28]
    // 0x642b80: stur            x11, [fp, #-8]
    // 0x642b84: r1 = Null
    //     0x642b84: mov             x1, NULL
    // 0x642b88: cmp             w2, NULL
    // 0x642b8c: b.eq            #0x642ba8
    // 0x642b90: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x642b90: ldur            w4, [x2, #0x17]
    // 0x642b94: DecompressPointer r4
    //     0x642b94: add             x4, x4, HEAP, lsl #32
    // 0x642b98: r8 = X0
    //     0x642b98: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x642b9c: LoadField: r9 = r4->field_7
    //     0x642b9c: ldur            x9, [x4, #7]
    // 0x642ba0: r3 = Null
    //     0x642ba0: ldr             x3, [PP, #0x958]  ; [pp+0x958] Null
    // 0x642ba4: blr             x9
    // 0x642ba8: ldur            x0, [fp, #-0x70]
    // 0x642bac: ldur            x1, [fp, #-0x58]
    // 0x642bb0: cmp             x1, x0
    // 0x642bb4: b.hs            #0x64370c
    // 0x642bb8: ldur            x1, [fp, #-0x38]
    // 0x642bbc: ldur            x0, [fp, #-8]
    // 0x642bc0: ldur            x2, [fp, #-0x58]
    // 0x642bc4: ArrayStore: r1[r2] = r0  ; List_4
    //     0x642bc4: add             x25, x1, x2, lsl #2
    //     0x642bc8: add             x25, x25, #0xf
    //     0x642bcc: str             w0, [x25]
    //     0x642bd0: tbz             w0, #0, #0x642bec
    //     0x642bd4: ldurb           w16, [x1, #-1]
    //     0x642bd8: ldurb           w17, [x0, #-1]
    //     0x642bdc: and             x16, x17, x16, lsr #2
    //     0x642be0: tst             x16, HEAP, lsr #32
    //     0x642be4: b.eq            #0x642bec
    //     0x642be8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x642bec: ldur            x0, [fp, #-0x50]
    // 0x642bf0: sub             x10, x0, #1
    // 0x642bf4: sub             x9, x2, #1
    // 0x642bf8: ldur            x3, [fp, #-0x30]
    // 0x642bfc: ldur            x7, [fp, #-0x20]
    // 0x642c00: ldur            x6, [fp, #-0x68]
    // 0x642c04: ldur            x8, [fp, #-0x60]
    // 0x642c08: ldur            x5, [fp, #-0x38]
    // 0x642c0c: ldur            x4, [fp, #-0x70]
    // 0x642c10: b               #0x642b40
    // 0x642c14: mov             x1, x8
    // 0x642c18: add             x3, x1, x0
    // 0x642c1c: stur            x3, [fp, #-0x78]
    // 0x642c20: mov             x6, x1
    // 0x642c24: ldur            x5, [fp, #-0x20]
    // 0x642c28: ldur            x4, [fp, #-0x38]
    // 0x642c2c: stur            x6, [fp, #-0x50]
    // 0x642c30: stur            x5, [fp, #-0x58]
    // 0x642c34: CheckStackOverflow
    //     0x642c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x642c38: cmp             SP, x16
    //     0x642c3c: b.ls            #0x643710
    // 0x642c40: cmp             x6, x3
    // 0x642c44: b.ge            #0x642cec
    // 0x642c48: ldur            x0, [fp, #-0x70]
    // 0x642c4c: mov             x1, x6
    // 0x642c50: cmp             x1, x0
    // 0x642c54: b.hs            #0x643718
    // 0x642c58: ArrayLoad: r7 = r4[r6]  ; Unknown_4
    //     0x642c58: add             x16, x4, x6, lsl #2
    //     0x642c5c: ldur            w7, [x16, #0xf]
    // 0x642c60: DecompressPointer r7
    //     0x642c60: add             x7, x7, HEAP, lsl #32
    // 0x642c64: mov             x0, x7
    // 0x642c68: ldur            x2, [fp, #-0x28]
    // 0x642c6c: stur            x7, [fp, #-8]
    // 0x642c70: r1 = Null
    //     0x642c70: mov             x1, NULL
    // 0x642c74: cmp             w2, NULL
    // 0x642c78: b.eq            #0x642c94
    // 0x642c7c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x642c7c: ldur            w4, [x2, #0x17]
    // 0x642c80: DecompressPointer r4
    //     0x642c80: add             x4, x4, HEAP, lsl #32
    // 0x642c84: r8 = X0
    //     0x642c84: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x642c88: LoadField: r9 = r4->field_7
    //     0x642c88: ldur            x9, [x4, #7]
    // 0x642c8c: r3 = Null
    //     0x642c8c: ldr             x3, [PP, #0x968]  ; [pp+0x968] Null
    // 0x642c90: blr             x9
    // 0x642c94: ldur            x0, [fp, #-0x70]
    // 0x642c98: ldur            x1, [fp, #-0x58]
    // 0x642c9c: cmp             x1, x0
    // 0x642ca0: b.hs            #0x64371c
    // 0x642ca4: ldur            x1, [fp, #-0x38]
    // 0x642ca8: ldur            x0, [fp, #-8]
    // 0x642cac: ldur            x2, [fp, #-0x58]
    // 0x642cb0: ArrayStore: r1[r2] = r0  ; List_4
    //     0x642cb0: add             x25, x1, x2, lsl #2
    //     0x642cb4: add             x25, x25, #0xf
    //     0x642cb8: str             w0, [x25]
    //     0x642cbc: tbz             w0, #0, #0x642cd8
    //     0x642cc0: ldurb           w16, [x1, #-1]
    //     0x642cc4: ldurb           w17, [x0, #-1]
    //     0x642cc8: and             x16, x17, x16, lsr #2
    //     0x642ccc: tst             x16, HEAP, lsr #32
    //     0x642cd0: b.eq            #0x642cd8
    //     0x642cd4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x642cd8: ldur            x0, [fp, #-0x50]
    // 0x642cdc: add             x6, x0, #1
    // 0x642ce0: add             x5, x2, #1
    // 0x642ce4: ldur            x3, [fp, #-0x78]
    // 0x642ce8: b               #0x642c28
    // 0x642cec: ldur            x0, [fp, #-0x20]
    // 0x642cf0: ldur            x1, [fp, #-0x30]
    // 0x642cf4: ldur            x2, [fp, #-0x68]
    // 0x642cf8: r0 = length=()
    //     0x642cf8: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0x642cfc: ldur            x1, [fp, #-0x20]
    // 0x642d00: sub             x0, x1, #1
    // 0x642d04: ldur            x1, [fp, #-0x48]
    // 0x642d08: b               #0x642d20
    // 0x642d0c: ldur            x0, [fp, #-0x18]
    // 0x642d10: ldur            x1, [fp, #-0x20]
    // 0x642d14: mov             x16, x1
    // 0x642d18: mov             x1, x0
    // 0x642d1c: mov             x0, x16
    // 0x642d20: mov             x6, x1
    // 0x642d24: add             x5, x0, #1
    // 0x642d28: ldur            x0, [fp, #-0x30]
    // 0x642d2c: ldur            x3, [fp, #-0x28]
    // 0x642d30: b               #0x642710
    // 0x642d34: mov             x0, x6
    // 0x642d38: r1 = <String?>
    //     0x642d38: ldr             x1, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0x642d3c: r0 = AllocateArray()
    //     0x642d3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x642d40: stur            x0, [fp, #-8]
    // 0x642d44: r0 = InitLateStaticField(0x650) // [package:flutter/src/foundation/assertions.dart] FlutterError::_stackFilters
    //     0x642d44: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x642d48: ldr             x0, [x0, #0xca0]
    //     0x642d4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x642d50: cmp             w0, w16
    //     0x642d54: b.ne            #0x642d60
    //     0x642d58: ldr             x2, [PP, #0x978]  ; [pp+0x978] Field <FlutterError._stackFilters@34022608>: static late final (offset: 0x650)
    //     0x642d5c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x642d60: mov             x2, x0
    // 0x642d64: LoadField: r3 = r2->field_7
    //     0x642d64: ldur            w3, [x2, #7]
    // 0x642d68: DecompressPointer r3
    //     0x642d68: add             x3, x3, HEAP, lsl #32
    // 0x642d6c: LoadField: r0 = r2->field_b
    //     0x642d6c: ldur            w0, [x2, #0xb]
    // 0x642d70: r1 = LoadInt32Instr(r0)
    //     0x642d70: sbfx            x1, x0, #1, #0x1f
    // 0x642d74: cmp             x1, #0
    // 0x642d78: b.gt            #0x643668
    // 0x642d7c: ldur            x0, [fp, #-0x80]
    // 0x642d80: r1 = <String>
    //     0x642d80: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x642d84: r2 = 0
    //     0x642d84: movz            x2, #0
    // 0x642d88: r0 = _GrowableList()
    //     0x642d88: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x642d8c: mov             x3, x0
    // 0x642d90: ldur            x2, [fp, #-0x80]
    // 0x642d94: stur            x3, [fp, #-0x28]
    // 0x642d98: sub             x4, x2, #1
    // 0x642d9c: stur            x4, [fp, #-0x58]
    // 0x642da0: r7 = 0
    //     0x642da0: movz            x7, #0
    // 0x642da4: ldur            x6, [fp, #-0x30]
    // 0x642da8: ldur            x5, [fp, #-8]
    // 0x642dac: stur            x7, [fp, #-0x50]
    // 0x642db0: CheckStackOverflow
    //     0x642db0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x642db4: cmp             SP, x16
    //     0x642db8: b.ls            #0x643720
    // 0x642dbc: LoadField: r0 = r6->field_b
    //     0x642dbc: ldur            w0, [x6, #0xb]
    // 0x642dc0: r1 = LoadInt32Instr(r0)
    //     0x642dc0: sbfx            x1, x0, #1, #0x1f
    // 0x642dc4: cmp             x7, x1
    // 0x642dc8: b.ge            #0x643024
    // 0x642dcc: mov             x8, x7
    // 0x642dd0: stur            x8, [fp, #-0x48]
    // 0x642dd4: CheckStackOverflow
    //     0x642dd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x642dd8: cmp             SP, x16
    //     0x642ddc: b.ls            #0x643728
    // 0x642de0: cmp             x8, x4
    // 0x642de4: b.ge            #0x642e70
    // 0x642de8: mov             x0, x2
    // 0x642dec: mov             x1, x8
    // 0x642df0: cmp             x1, x0
    // 0x642df4: b.hs            #0x643730
    // 0x642df8: ArrayLoad: r9 = r5[r8]  ; Unknown_4
    //     0x642df8: add             x16, x5, x8, lsl #2
    //     0x642dfc: ldur            w9, [x16, #0xf]
    // 0x642e00: DecompressPointer r9
    //     0x642e00: add             x9, x9, HEAP, lsl #32
    // 0x642e04: cmp             w9, NULL
    // 0x642e08: b.eq            #0x642e70
    // 0x642e0c: add             x10, x8, #1
    // 0x642e10: mov             x0, x2
    // 0x642e14: mov             x1, x10
    // 0x642e18: stur            x10, [fp, #-0x20]
    // 0x642e1c: cmp             x1, x0
    // 0x642e20: b.hs            #0x643734
    // 0x642e24: ArrayLoad: r0 = r5[r10]  ; Unknown_4
    //     0x642e24: add             x16, x5, x10, lsl #2
    //     0x642e28: ldur            w0, [x16, #0xf]
    // 0x642e2c: DecompressPointer r0
    //     0x642e2c: add             x0, x0, HEAP, lsl #32
    // 0x642e30: r1 = LoadClassIdInstr(r0)
    //     0x642e30: ldur            x1, [x0, #-1]
    //     0x642e34: ubfx            x1, x1, #0xc, #0x14
    // 0x642e38: stp             x9, x0, [SP]
    // 0x642e3c: mov             x0, x1
    // 0x642e40: mov             lr, x0
    // 0x642e44: ldr             lr, [x21, lr, lsl #3]
    // 0x642e48: blr             lr
    // 0x642e4c: tbnz            w0, #4, #0x642e70
    // 0x642e50: ldur            x8, [fp, #-0x20]
    // 0x642e54: ldur            x6, [fp, #-0x30]
    // 0x642e58: ldur            x3, [fp, #-0x28]
    // 0x642e5c: ldur            x7, [fp, #-0x50]
    // 0x642e60: ldur            x4, [fp, #-0x58]
    // 0x642e64: ldur            x5, [fp, #-8]
    // 0x642e68: ldur            x2, [fp, #-0x80]
    // 0x642e6c: b               #0x642dd0
    // 0x642e70: ldur            x4, [fp, #-0x48]
    // 0x642e74: ldur            x3, [fp, #-8]
    // 0x642e78: ldur            x0, [fp, #-0x80]
    // 0x642e7c: mov             x1, x4
    // 0x642e80: cmp             x1, x0
    // 0x642e84: b.hs            #0x643738
    // 0x642e88: ArrayLoad: r0 = r3[r4]  ; Unknown_4
    //     0x642e88: add             x16, x3, x4, lsl #2
    //     0x642e8c: ldur            w0, [x16, #0xf]
    // 0x642e90: DecompressPointer r0
    //     0x642e90: add             x0, x0, HEAP, lsl #32
    // 0x642e94: cmp             w0, NULL
    // 0x642e98: b.eq            #0x642ef4
    // 0x642e9c: ldur            x0, [fp, #-0x50]
    // 0x642ea0: cmp             x4, x0
    // 0x642ea4: b.eq            #0x642ee8
    // 0x642ea8: r1 = Null
    //     0x642ea8: mov             x1, NULL
    // 0x642eac: r2 = 6
    //     0x642eac: movz            x2, #0x6
    // 0x642eb0: r0 = AllocateArray()
    //     0x642eb0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x642eb4: r16 = " ("
    //     0x642eb4: ldr             x16, [PP, #0x980]  ; [pp+0x980] " ("
    // 0x642eb8: StoreField: r0->field_f = r16
    //     0x642eb8: stur            w16, [x0, #0xf]
    // 0x642ebc: ldur            x2, [fp, #-0x50]
    // 0x642ec0: ldur            x1, [fp, #-0x48]
    // 0x642ec4: sub             x3, x1, x2
    // 0x642ec8: add             x2, x3, #2
    // 0x642ecc: lsl             x3, x2, #1
    // 0x642ed0: StoreField: r0->field_13 = r3
    //     0x642ed0: stur            w3, [x0, #0x13]
    // 0x642ed4: r16 = " frames)"
    //     0x642ed4: ldr             x16, [PP, #0x988]  ; [pp+0x988] " frames)"
    // 0x642ed8: ArrayStore: r0[0] = r16  ; List_4
    //     0x642ed8: stur            w16, [x0, #0x17]
    // 0x642edc: str             x0, [SP]
    // 0x642ee0: r0 = _interpolate()
    //     0x642ee0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x642ee4: b               #0x642eec
    // 0x642ee8: r0 = " (1 frame)"
    //     0x642ee8: ldr             x0, [PP, #0x990]  ; [pp+0x990] " (1 frame)"
    // 0x642eec: mov             x5, x0
    // 0x642ef0: b               #0x642ef8
    // 0x642ef4: r5 = ""
    //     0x642ef4: ldr             x5, [PP, #0x288]  ; [pp+0x288] ""
    // 0x642ef8: ldur            x3, [fp, #-0x48]
    // 0x642efc: ldur            x4, [fp, #-8]
    // 0x642f00: stur            x5, [fp, #-0x40]
    // 0x642f04: ArrayLoad: r0 = r4[r3]  ; Unknown_4
    //     0x642f04: add             x16, x4, x3, lsl #2
    //     0x642f08: ldur            w0, [x16, #0xf]
    // 0x642f0c: DecompressPointer r0
    //     0x642f0c: add             x0, x0, HEAP, lsl #32
    // 0x642f10: cmp             w0, NULL
    // 0x642f14: b.ne            #0x642f58
    // 0x642f18: ldur            x6, [fp, #-0x30]
    // 0x642f1c: LoadField: r0 = r6->field_b
    //     0x642f1c: ldur            w0, [x6, #0xb]
    // 0x642f20: r1 = LoadInt32Instr(r0)
    //     0x642f20: sbfx            x1, x0, #1, #0x1f
    // 0x642f24: mov             x0, x1
    // 0x642f28: mov             x1, x3
    // 0x642f2c: cmp             x1, x0
    // 0x642f30: b.hs            #0x64373c
    // 0x642f34: LoadField: r0 = r6->field_f
    //     0x642f34: ldur            w0, [x6, #0xf]
    // 0x642f38: DecompressPointer r0
    //     0x642f38: add             x0, x0, HEAP, lsl #32
    // 0x642f3c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x642f3c: add             x16, x0, x3, lsl #2
    //     0x642f40: ldur            w1, [x16, #0xf]
    // 0x642f44: DecompressPointer r1
    //     0x642f44: add             x1, x1, HEAP, lsl #32
    // 0x642f48: LoadField: r0 = r1->field_7
    //     0x642f48: ldur            w0, [x1, #7]
    // 0x642f4c: DecompressPointer r0
    //     0x642f4c: add             x0, x0, HEAP, lsl #32
    // 0x642f50: mov             x7, x0
    // 0x642f54: b               #0x642f60
    // 0x642f58: ldur            x6, [fp, #-0x30]
    // 0x642f5c: mov             x7, x0
    // 0x642f60: ldur            x0, [fp, #-0x28]
    // 0x642f64: stur            x7, [fp, #-0x38]
    // 0x642f68: r1 = Null
    //     0x642f68: mov             x1, NULL
    // 0x642f6c: r2 = 4
    //     0x642f6c: movz            x2, #0x4
    // 0x642f70: r0 = AllocateArray()
    //     0x642f70: bl              #0xec22fc  ; AllocateArrayStub
    // 0x642f74: mov             x1, x0
    // 0x642f78: ldur            x0, [fp, #-0x38]
    // 0x642f7c: StoreField: r1->field_f = r0
    //     0x642f7c: stur            w0, [x1, #0xf]
    // 0x642f80: ldur            x0, [fp, #-0x40]
    // 0x642f84: StoreField: r1->field_13 = r0
    //     0x642f84: stur            w0, [x1, #0x13]
    // 0x642f88: str             x1, [SP]
    // 0x642f8c: r0 = _interpolate()
    //     0x642f8c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x642f90: mov             x2, x0
    // 0x642f94: ldur            x0, [fp, #-0x28]
    // 0x642f98: stur            x2, [fp, #-0x38]
    // 0x642f9c: LoadField: r1 = r0->field_b
    //     0x642f9c: ldur            w1, [x0, #0xb]
    // 0x642fa0: LoadField: r3 = r0->field_f
    //     0x642fa0: ldur            w3, [x0, #0xf]
    // 0x642fa4: DecompressPointer r3
    //     0x642fa4: add             x3, x3, HEAP, lsl #32
    // 0x642fa8: LoadField: r4 = r3->field_b
    //     0x642fa8: ldur            w4, [x3, #0xb]
    // 0x642fac: r3 = LoadInt32Instr(r1)
    //     0x642fac: sbfx            x3, x1, #1, #0x1f
    // 0x642fb0: stur            x3, [fp, #-0x20]
    // 0x642fb4: r1 = LoadInt32Instr(r4)
    //     0x642fb4: sbfx            x1, x4, #1, #0x1f
    // 0x642fb8: cmp             x3, x1
    // 0x642fbc: b.ne            #0x642fc8
    // 0x642fc0: mov             x1, x0
    // 0x642fc4: r0 = _growToNextCapacity()
    //     0x642fc4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x642fc8: ldur            x3, [fp, #-0x28]
    // 0x642fcc: ldur            x4, [fp, #-0x48]
    // 0x642fd0: ldur            x2, [fp, #-0x20]
    // 0x642fd4: add             x0, x2, #1
    // 0x642fd8: lsl             x1, x0, #1
    // 0x642fdc: StoreField: r3->field_b = r1
    //     0x642fdc: stur            w1, [x3, #0xb]
    // 0x642fe0: LoadField: r1 = r3->field_f
    //     0x642fe0: ldur            w1, [x3, #0xf]
    // 0x642fe4: DecompressPointer r1
    //     0x642fe4: add             x1, x1, HEAP, lsl #32
    // 0x642fe8: ldur            x0, [fp, #-0x38]
    // 0x642fec: ArrayStore: r1[r2] = r0  ; List_4
    //     0x642fec: add             x25, x1, x2, lsl #2
    //     0x642ff0: add             x25, x25, #0xf
    //     0x642ff4: str             w0, [x25]
    //     0x642ff8: tbz             w0, #0, #0x643014
    //     0x642ffc: ldurb           w16, [x1, #-1]
    //     0x643000: ldurb           w17, [x0, #-1]
    //     0x643004: and             x16, x17, x16, lsr #2
    //     0x643008: tst             x16, HEAP, lsr #32
    //     0x64300c: b.eq            #0x643014
    //     0x643010: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x643014: add             x7, x4, #1
    // 0x643018: ldur            x4, [fp, #-0x58]
    // 0x64301c: ldur            x2, [fp, #-0x80]
    // 0x643020: b               #0x642da4
    // 0x643024: r1 = <String>
    //     0x643024: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x643028: r2 = 0
    //     0x643028: movz            x2, #0
    // 0x64302c: r0 = _GrowableList()
    //     0x64302c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x643030: ldur            x1, [fp, #-0x10]
    // 0x643034: stur            x0, [fp, #-8]
    // 0x643038: r0 = entries()
    //     0x643038: bl              #0x89c028  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::entries
    // 0x64303c: mov             x1, x0
    // 0x643040: r0 = iterator()
    //     0x643040: bl              #0x887d48  ; [dart:_internal] MappedIterable::iterator
    // 0x643044: mov             x2, x0
    // 0x643048: stur            x2, [fp, #-0x40]
    // 0x64304c: LoadField: r3 = r2->field_f
    //     0x64304c: ldur            w3, [x2, #0xf]
    // 0x643050: DecompressPointer r3
    //     0x643050: add             x3, x3, HEAP, lsl #32
    // 0x643054: stur            x3, [fp, #-0x38]
    // 0x643058: LoadField: r4 = r2->field_13
    //     0x643058: ldur            w4, [x2, #0x13]
    // 0x64305c: DecompressPointer r4
    //     0x64305c: add             x4, x4, HEAP, lsl #32
    // 0x643060: stur            x4, [fp, #-0x30]
    // 0x643064: LoadField: r5 = r2->field_7
    //     0x643064: ldur            w5, [x2, #7]
    // 0x643068: DecompressPointer r5
    //     0x643068: add             x5, x5, HEAP, lsl #32
    // 0x64306c: stur            x5, [fp, #-0x10]
    // 0x643070: ldur            x6, [fp, #-8]
    // 0x643074: CheckStackOverflow
    //     0x643074: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x643078: cmp             SP, x16
    //     0x64307c: b.ls            #0x643740
    // 0x643080: r0 = LoadClassIdInstr(r3)
    //     0x643080: ldur            x0, [x3, #-1]
    //     0x643084: ubfx            x0, x0, #0xc, #0x14
    // 0x643088: mov             x1, x3
    // 0x64308c: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x64308c: movz            x17, #0x292d
    //     0x643090: movk            x17, #0x1, lsl #16
    //     0x643094: add             lr, x0, x17
    //     0x643098: ldr             lr, [x21, lr, lsl #3]
    //     0x64309c: blr             lr
    // 0x6430a0: tbnz            w0, #4, #0x643220
    // 0x6430a4: ldur            x2, [fp, #-0x40]
    // 0x6430a8: ldur            x3, [fp, #-0x38]
    // 0x6430ac: r0 = LoadClassIdInstr(r3)
    //     0x6430ac: ldur            x0, [x3, #-1]
    //     0x6430b0: ubfx            x0, x0, #0xc, #0x14
    // 0x6430b4: mov             x1, x3
    // 0x6430b8: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x6430b8: movz            x17, #0x384d
    //     0x6430bc: movk            x17, #0x1, lsl #16
    //     0x6430c0: add             lr, x0, x17
    //     0x6430c4: ldr             lr, [x21, lr, lsl #3]
    //     0x6430c8: blr             lr
    // 0x6430cc: ldur            x16, [fp, #-0x30]
    // 0x6430d0: stp             x0, x16, [SP]
    // 0x6430d4: ldur            x0, [fp, #-0x30]
    // 0x6430d8: ClosureCall
    //     0x6430d8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x6430dc: ldur            x2, [x0, #0x1f]
    //     0x6430e0: blr             x2
    // 0x6430e4: mov             x4, x0
    // 0x6430e8: ldur            x3, [fp, #-0x40]
    // 0x6430ec: stur            x4, [fp, #-0x88]
    // 0x6430f0: StoreField: r3->field_b = r0
    //     0x6430f0: stur            w0, [x3, #0xb]
    //     0x6430f4: tbz             w0, #0, #0x643110
    //     0x6430f8: ldurb           w16, [x3, #-1]
    //     0x6430fc: ldurb           w17, [x0, #-1]
    //     0x643100: and             x16, x17, x16, lsr #2
    //     0x643104: tst             x16, HEAP, lsr #32
    //     0x643108: b.eq            #0x643110
    //     0x64310c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x643110: cmp             w4, NULL
    // 0x643114: b.ne            #0x643144
    // 0x643118: mov             x0, x4
    // 0x64311c: ldur            x2, [fp, #-0x10]
    // 0x643120: r1 = Null
    //     0x643120: mov             x1, NULL
    // 0x643124: cmp             w2, NULL
    // 0x643128: b.eq            #0x643144
    // 0x64312c: LoadField: r4 = r2->field_1b
    //     0x64312c: ldur            w4, [x2, #0x1b]
    // 0x643130: DecompressPointer r4
    //     0x643130: add             x4, x4, HEAP, lsl #32
    // 0x643134: r8 = X1
    //     0x643134: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x643138: LoadField: r9 = r4->field_7
    //     0x643138: ldur            x9, [x4, #7]
    // 0x64313c: r3 = Null
    //     0x64313c: ldr             x3, [PP, #0x998]  ; [pp+0x998] Null
    // 0x643140: blr             x9
    // 0x643144: ldur            x1, [fp, #-0x88]
    // 0x643148: LoadField: r0 = r1->field_f
    //     0x643148: ldur            w0, [x1, #0xf]
    // 0x64314c: DecompressPointer r0
    //     0x64314c: add             x0, x0, HEAP, lsl #32
    // 0x643150: r2 = 60
    //     0x643150: movz            x2, #0x3c
    // 0x643154: branchIfSmi(r0, 0x643160)
    //     0x643154: tbz             w0, #0, #0x643160
    // 0x643158: r2 = LoadClassIdInstr(r0)
    //     0x643158: ldur            x2, [x0, #-1]
    //     0x64315c: ubfx            x2, x2, #0xc, #0x14
    // 0x643160: stp             xzr, x0, [SP]
    // 0x643164: mov             x0, x2
    // 0x643168: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x643168: sub             lr, x0, #0xfe3
    //     0x64316c: ldr             lr, [x21, lr, lsl #3]
    //     0x643170: blr             lr
    // 0x643174: tbnz            w0, #4, #0x643204
    // 0x643178: ldur            x2, [fp, #-8]
    // 0x64317c: ldur            x0, [fp, #-0x88]
    // 0x643180: LoadField: r3 = r0->field_b
    //     0x643180: ldur            w3, [x0, #0xb]
    // 0x643184: DecompressPointer r3
    //     0x643184: add             x3, x3, HEAP, lsl #32
    // 0x643188: stur            x3, [fp, #-0x90]
    // 0x64318c: LoadField: r0 = r2->field_b
    //     0x64318c: ldur            w0, [x2, #0xb]
    // 0x643190: LoadField: r1 = r2->field_f
    //     0x643190: ldur            w1, [x2, #0xf]
    // 0x643194: DecompressPointer r1
    //     0x643194: add             x1, x1, HEAP, lsl #32
    // 0x643198: LoadField: r4 = r1->field_b
    //     0x643198: ldur            w4, [x1, #0xb]
    // 0x64319c: r5 = LoadInt32Instr(r0)
    //     0x64319c: sbfx            x5, x0, #1, #0x1f
    // 0x6431a0: stur            x5, [fp, #-0x20]
    // 0x6431a4: r0 = LoadInt32Instr(r4)
    //     0x6431a4: sbfx            x0, x4, #1, #0x1f
    // 0x6431a8: cmp             x5, x0
    // 0x6431ac: b.ne            #0x6431b8
    // 0x6431b0: mov             x1, x2
    // 0x6431b4: r0 = _growToNextCapacity()
    //     0x6431b4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6431b8: ldur            x2, [fp, #-8]
    // 0x6431bc: ldur            x3, [fp, #-0x20]
    // 0x6431c0: add             x0, x3, #1
    // 0x6431c4: lsl             x1, x0, #1
    // 0x6431c8: StoreField: r2->field_b = r1
    //     0x6431c8: stur            w1, [x2, #0xb]
    // 0x6431cc: LoadField: r1 = r2->field_f
    //     0x6431cc: ldur            w1, [x2, #0xf]
    // 0x6431d0: DecompressPointer r1
    //     0x6431d0: add             x1, x1, HEAP, lsl #32
    // 0x6431d4: ldur            x0, [fp, #-0x90]
    // 0x6431d8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6431d8: add             x25, x1, x3, lsl #2
    //     0x6431dc: add             x25, x25, #0xf
    //     0x6431e0: str             w0, [x25]
    //     0x6431e4: tbz             w0, #0, #0x643200
    //     0x6431e8: ldurb           w16, [x1, #-1]
    //     0x6431ec: ldurb           w17, [x0, #-1]
    //     0x6431f0: and             x16, x17, x16, lsr #2
    //     0x6431f4: tst             x16, HEAP, lsr #32
    //     0x6431f8: b.eq            #0x643200
    //     0x6431fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x643200: b               #0x643208
    // 0x643204: ldur            x2, [fp, #-8]
    // 0x643208: mov             x6, x2
    // 0x64320c: ldur            x2, [fp, #-0x40]
    // 0x643210: ldur            x5, [fp, #-0x10]
    // 0x643214: ldur            x3, [fp, #-0x38]
    // 0x643218: ldur            x4, [fp, #-0x30]
    // 0x64321c: b               #0x643074
    // 0x643220: ldur            x3, [fp, #-0x18]
    // 0x643224: ldur            x2, [fp, #-8]
    // 0x643228: ldur            x0, [fp, #-0x40]
    // 0x64322c: StoreField: r0->field_b = rNULL
    //     0x64322c: stur            NULL, [x0, #0xb]
    // 0x643230: mov             x1, x2
    // 0x643234: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x643234: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x643238: r0 = sort()
    //     0x643238: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x64323c: ldur            x0, [fp, #-0x18]
    // 0x643240: cmp             x0, #1
    // 0x643244: b.ne            #0x643330
    // 0x643248: ldur            x0, [fp, #-0x28]
    // 0x64324c: r1 = Null
    //     0x64324c: mov             x1, NULL
    // 0x643250: r2 = 6
    //     0x643250: movz            x2, #0x6
    // 0x643254: r0 = AllocateArray()
    //     0x643254: bl              #0xec22fc  ; AllocateArrayStub
    // 0x643258: stur            x0, [fp, #-0x10]
    // 0x64325c: r16 = "(elided one frame from "
    //     0x64325c: ldr             x16, [PP, #0x9b0]  ; [pp+0x9b0] "(elided one frame from "
    // 0x643260: StoreField: r0->field_f = r16
    //     0x643260: stur            w16, [x0, #0xf]
    // 0x643264: ldur            x1, [fp, #-8]
    // 0x643268: r0 = single()
    //     0x643268: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x64326c: ldur            x1, [fp, #-0x10]
    // 0x643270: ArrayStore: r1[1] = r0  ; List_4
    //     0x643270: add             x25, x1, #0x13
    //     0x643274: str             w0, [x25]
    //     0x643278: tbz             w0, #0, #0x643294
    //     0x64327c: ldurb           w16, [x1, #-1]
    //     0x643280: ldurb           w17, [x0, #-1]
    //     0x643284: and             x16, x17, x16, lsr #2
    //     0x643288: tst             x16, HEAP, lsr #32
    //     0x64328c: b.eq            #0x643294
    //     0x643290: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x643294: ldur            x0, [fp, #-0x10]
    // 0x643298: r16 = ")"
    //     0x643298: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0x64329c: ArrayStore: r0[0] = r16  ; List_4
    //     0x64329c: stur            w16, [x0, #0x17]
    // 0x6432a0: str             x0, [SP]
    // 0x6432a4: r0 = _interpolate()
    //     0x6432a4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x6432a8: mov             x2, x0
    // 0x6432ac: ldur            x0, [fp, #-0x28]
    // 0x6432b0: stur            x2, [fp, #-0x10]
    // 0x6432b4: LoadField: r1 = r0->field_b
    //     0x6432b4: ldur            w1, [x0, #0xb]
    // 0x6432b8: LoadField: r3 = r0->field_f
    //     0x6432b8: ldur            w3, [x0, #0xf]
    // 0x6432bc: DecompressPointer r3
    //     0x6432bc: add             x3, x3, HEAP, lsl #32
    // 0x6432c0: LoadField: r4 = r3->field_b
    //     0x6432c0: ldur            w4, [x3, #0xb]
    // 0x6432c4: r3 = LoadInt32Instr(r1)
    //     0x6432c4: sbfx            x3, x1, #1, #0x1f
    // 0x6432c8: stur            x3, [fp, #-0x20]
    // 0x6432cc: r1 = LoadInt32Instr(r4)
    //     0x6432cc: sbfx            x1, x4, #1, #0x1f
    // 0x6432d0: cmp             x3, x1
    // 0x6432d4: b.ne            #0x6432e0
    // 0x6432d8: mov             x1, x0
    // 0x6432dc: r0 = _growToNextCapacity()
    //     0x6432dc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6432e0: ldur            x3, [fp, #-0x28]
    // 0x6432e4: ldur            x2, [fp, #-0x20]
    // 0x6432e8: add             x0, x2, #1
    // 0x6432ec: lsl             x1, x0, #1
    // 0x6432f0: StoreField: r3->field_b = r1
    //     0x6432f0: stur            w1, [x3, #0xb]
    // 0x6432f4: LoadField: r1 = r3->field_f
    //     0x6432f4: ldur            w1, [x3, #0xf]
    // 0x6432f8: DecompressPointer r1
    //     0x6432f8: add             x1, x1, HEAP, lsl #32
    // 0x6432fc: ldur            x0, [fp, #-0x10]
    // 0x643300: ArrayStore: r1[r2] = r0  ; List_4
    //     0x643300: add             x25, x1, x2, lsl #2
    //     0x643304: add             x25, x25, #0xf
    //     0x643308: str             w0, [x25]
    //     0x64330c: tbz             w0, #0, #0x643328
    //     0x643310: ldurb           w16, [x1, #-1]
    //     0x643314: ldurb           w17, [x0, #-1]
    //     0x643318: and             x16, x17, x16, lsr #2
    //     0x64331c: tst             x16, HEAP, lsr #32
    //     0x643320: b.eq            #0x643328
    //     0x643324: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x643328: mov             x2, x3
    // 0x64332c: b               #0x643658
    // 0x643330: ldur            x3, [fp, #-0x28]
    // 0x643334: cmp             x0, #1
    // 0x643338: b.le            #0x643654
    // 0x64333c: ldur            x4, [fp, #-8]
    // 0x643340: LoadField: r1 = r4->field_b
    //     0x643340: ldur            w1, [x4, #0xb]
    // 0x643344: r2 = LoadInt32Instr(r1)
    //     0x643344: sbfx            x2, x1, #1, #0x1f
    // 0x643348: cmp             x2, #1
    // 0x64334c: b.le            #0x643408
    // 0x643350: sub             x5, x2, #1
    // 0x643354: stur            x5, [fp, #-0x20]
    // 0x643358: r1 = Null
    //     0x643358: mov             x1, NULL
    // 0x64335c: r2 = 4
    //     0x64335c: movz            x2, #0x4
    // 0x643360: r0 = AllocateArray()
    //     0x643360: bl              #0xec22fc  ; AllocateArrayStub
    // 0x643364: stur            x0, [fp, #-0x10]
    // 0x643368: r16 = "and "
    //     0x643368: ldr             x16, [PP, #0x9c0]  ; [pp+0x9c0] "and "
    // 0x64336c: StoreField: r0->field_f = r16
    //     0x64336c: stur            w16, [x0, #0xf]
    // 0x643370: ldur            x1, [fp, #-8]
    // 0x643374: r0 = last()
    //     0x643374: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x643378: ldur            x1, [fp, #-0x10]
    // 0x64337c: ArrayStore: r1[1] = r0  ; List_4
    //     0x64337c: add             x25, x1, #0x13
    //     0x643380: str             w0, [x25]
    //     0x643384: tbz             w0, #0, #0x6433a0
    //     0x643388: ldurb           w16, [x1, #-1]
    //     0x64338c: ldurb           w17, [x0, #-1]
    //     0x643390: and             x16, x17, x16, lsr #2
    //     0x643394: tst             x16, HEAP, lsr #32
    //     0x643398: b.eq            #0x6433a0
    //     0x64339c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6433a0: ldur            x16, [fp, #-0x10]
    // 0x6433a4: str             x16, [SP]
    // 0x6433a8: r0 = _interpolate()
    //     0x6433a8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x6433ac: mov             x2, x0
    // 0x6433b0: ldur            x3, [fp, #-8]
    // 0x6433b4: LoadField: r4 = r3->field_b
    //     0x6433b4: ldur            w4, [x3, #0xb]
    // 0x6433b8: r0 = LoadInt32Instr(r4)
    //     0x6433b8: sbfx            x0, x4, #1, #0x1f
    // 0x6433bc: ldur            x1, [fp, #-0x20]
    // 0x6433c0: cmp             x1, x0
    // 0x6433c4: b.hs            #0x643748
    // 0x6433c8: LoadField: r1 = r3->field_f
    //     0x6433c8: ldur            w1, [x3, #0xf]
    // 0x6433cc: DecompressPointer r1
    //     0x6433cc: add             x1, x1, HEAP, lsl #32
    // 0x6433d0: mov             x0, x2
    // 0x6433d4: ldur            x2, [fp, #-0x20]
    // 0x6433d8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6433d8: add             x25, x1, x2, lsl #2
    //     0x6433dc: add             x25, x25, #0xf
    //     0x6433e0: str             w0, [x25]
    //     0x6433e4: tbz             w0, #0, #0x643400
    //     0x6433e8: ldurb           w16, [x1, #-1]
    //     0x6433ec: ldurb           w17, [x0, #-1]
    //     0x6433f0: and             x16, x17, x16, lsr #2
    //     0x6433f4: tst             x16, HEAP, lsr #32
    //     0x6433f8: b.eq            #0x643400
    //     0x6433fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x643400: r0 = LoadInt32Instr(r4)
    //     0x643400: sbfx            x0, x4, #1, #0x1f
    // 0x643404: b               #0x643410
    // 0x643408: mov             x3, x4
    // 0x64340c: r0 = LoadInt32Instr(r1)
    //     0x64340c: sbfx            x0, x1, #1, #0x1f
    // 0x643410: cmp             x0, #2
    // 0x643414: b.le            #0x643538
    // 0x643418: ldur            x0, [fp, #-0x18]
    // 0x64341c: ldur            x4, [fp, #-0x28]
    // 0x643420: r1 = Null
    //     0x643420: mov             x1, NULL
    // 0x643424: r2 = 10
    //     0x643424: movz            x2, #0xa
    // 0x643428: r0 = AllocateArray()
    //     0x643428: bl              #0xec22fc  ; AllocateArrayStub
    // 0x64342c: mov             x2, x0
    // 0x643430: stur            x2, [fp, #-0x10]
    // 0x643434: r16 = "(elided "
    //     0x643434: ldr             x16, [PP, #0x9c8]  ; [pp+0x9c8] "(elided "
    // 0x643438: StoreField: r2->field_f = r16
    //     0x643438: stur            w16, [x2, #0xf]
    // 0x64343c: ldur            x3, [fp, #-0x18]
    // 0x643440: r0 = BoxInt64Instr(r3)
    //     0x643440: sbfiz           x0, x3, #1, #0x1f
    //     0x643444: cmp             x3, x0, asr #1
    //     0x643448: b.eq            #0x643454
    //     0x64344c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x643450: stur            x3, [x0, #7]
    // 0x643454: StoreField: r2->field_13 = r0
    //     0x643454: stur            w0, [x2, #0x13]
    // 0x643458: r16 = " frames from "
    //     0x643458: ldr             x16, [PP, #0x9d0]  ; [pp+0x9d0] " frames from "
    // 0x64345c: ArrayStore: r2[0] = r16  ; List_4
    //     0x64345c: stur            w16, [x2, #0x17]
    // 0x643460: r16 = ", "
    //     0x643460: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0x643464: str             x16, [SP]
    // 0x643468: ldur            x1, [fp, #-8]
    // 0x64346c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x64346c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x643470: r0 = join()
    //     0x643470: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0x643474: ldur            x1, [fp, #-0x10]
    // 0x643478: ArrayStore: r1[3] = r0  ; List_4
    //     0x643478: add             x25, x1, #0x1b
    //     0x64347c: str             w0, [x25]
    //     0x643480: tbz             w0, #0, #0x64349c
    //     0x643484: ldurb           w16, [x1, #-1]
    //     0x643488: ldurb           w17, [x0, #-1]
    //     0x64348c: and             x16, x17, x16, lsr #2
    //     0x643490: tst             x16, HEAP, lsr #32
    //     0x643494: b.eq            #0x64349c
    //     0x643498: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x64349c: ldur            x0, [fp, #-0x10]
    // 0x6434a0: r16 = ")"
    //     0x6434a0: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0x6434a4: StoreField: r0->field_1f = r16
    //     0x6434a4: stur            w16, [x0, #0x1f]
    // 0x6434a8: str             x0, [SP]
    // 0x6434ac: r0 = _interpolate()
    //     0x6434ac: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x6434b0: mov             x2, x0
    // 0x6434b4: ldur            x0, [fp, #-0x28]
    // 0x6434b8: stur            x2, [fp, #-0x10]
    // 0x6434bc: LoadField: r1 = r0->field_b
    //     0x6434bc: ldur            w1, [x0, #0xb]
    // 0x6434c0: LoadField: r3 = r0->field_f
    //     0x6434c0: ldur            w3, [x0, #0xf]
    // 0x6434c4: DecompressPointer r3
    //     0x6434c4: add             x3, x3, HEAP, lsl #32
    // 0x6434c8: LoadField: r4 = r3->field_b
    //     0x6434c8: ldur            w4, [x3, #0xb]
    // 0x6434cc: r3 = LoadInt32Instr(r1)
    //     0x6434cc: sbfx            x3, x1, #1, #0x1f
    // 0x6434d0: stur            x3, [fp, #-0x20]
    // 0x6434d4: r1 = LoadInt32Instr(r4)
    //     0x6434d4: sbfx            x1, x4, #1, #0x1f
    // 0x6434d8: cmp             x3, x1
    // 0x6434dc: b.ne            #0x6434e8
    // 0x6434e0: mov             x1, x0
    // 0x6434e4: r0 = _growToNextCapacity()
    //     0x6434e4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6434e8: ldur            x4, [fp, #-0x28]
    // 0x6434ec: ldur            x2, [fp, #-0x20]
    // 0x6434f0: add             x0, x2, #1
    // 0x6434f4: lsl             x1, x0, #1
    // 0x6434f8: StoreField: r4->field_b = r1
    //     0x6434f8: stur            w1, [x4, #0xb]
    // 0x6434fc: LoadField: r1 = r4->field_f
    //     0x6434fc: ldur            w1, [x4, #0xf]
    // 0x643500: DecompressPointer r1
    //     0x643500: add             x1, x1, HEAP, lsl #32
    // 0x643504: ldur            x0, [fp, #-0x10]
    // 0x643508: ArrayStore: r1[r2] = r0  ; List_4
    //     0x643508: add             x25, x1, x2, lsl #2
    //     0x64350c: add             x25, x25, #0xf
    //     0x643510: str             w0, [x25]
    //     0x643514: tbz             w0, #0, #0x643530
    //     0x643518: ldurb           w16, [x1, #-1]
    //     0x64351c: ldurb           w17, [x0, #-1]
    //     0x643520: and             x16, x17, x16, lsr #2
    //     0x643524: tst             x16, HEAP, lsr #32
    //     0x643528: b.eq            #0x643530
    //     0x64352c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x643530: mov             x2, x4
    // 0x643534: b               #0x643658
    // 0x643538: ldur            x3, [fp, #-0x18]
    // 0x64353c: ldur            x4, [fp, #-0x28]
    // 0x643540: r1 = Null
    //     0x643540: mov             x1, NULL
    // 0x643544: r2 = 10
    //     0x643544: movz            x2, #0xa
    // 0x643548: r0 = AllocateArray()
    //     0x643548: bl              #0xec22fc  ; AllocateArrayStub
    // 0x64354c: mov             x2, x0
    // 0x643550: stur            x2, [fp, #-0x10]
    // 0x643554: r16 = "(elided "
    //     0x643554: ldr             x16, [PP, #0x9c8]  ; [pp+0x9c8] "(elided "
    // 0x643558: StoreField: r2->field_f = r16
    //     0x643558: stur            w16, [x2, #0xf]
    // 0x64355c: ldur            x3, [fp, #-0x18]
    // 0x643560: r0 = BoxInt64Instr(r3)
    //     0x643560: sbfiz           x0, x3, #1, #0x1f
    //     0x643564: cmp             x3, x0, asr #1
    //     0x643568: b.eq            #0x643574
    //     0x64356c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x643570: stur            x3, [x0, #7]
    // 0x643574: StoreField: r2->field_13 = r0
    //     0x643574: stur            w0, [x2, #0x13]
    // 0x643578: r16 = " frames from "
    //     0x643578: ldr             x16, [PP, #0x9d0]  ; [pp+0x9d0] " frames from "
    // 0x64357c: ArrayStore: r2[0] = r16  ; List_4
    //     0x64357c: stur            w16, [x2, #0x17]
    // 0x643580: r16 = " "
    //     0x643580: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x643584: str             x16, [SP]
    // 0x643588: ldur            x1, [fp, #-8]
    // 0x64358c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x64358c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x643590: r0 = join()
    //     0x643590: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0x643594: ldur            x1, [fp, #-0x10]
    // 0x643598: ArrayStore: r1[3] = r0  ; List_4
    //     0x643598: add             x25, x1, #0x1b
    //     0x64359c: str             w0, [x25]
    //     0x6435a0: tbz             w0, #0, #0x6435bc
    //     0x6435a4: ldurb           w16, [x1, #-1]
    //     0x6435a8: ldurb           w17, [x0, #-1]
    //     0x6435ac: and             x16, x17, x16, lsr #2
    //     0x6435b0: tst             x16, HEAP, lsr #32
    //     0x6435b4: b.eq            #0x6435bc
    //     0x6435b8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6435bc: ldur            x0, [fp, #-0x10]
    // 0x6435c0: r16 = ")"
    //     0x6435c0: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0x6435c4: StoreField: r0->field_1f = r16
    //     0x6435c4: stur            w16, [x0, #0x1f]
    // 0x6435c8: str             x0, [SP]
    // 0x6435cc: r0 = _interpolate()
    //     0x6435cc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x6435d0: mov             x2, x0
    // 0x6435d4: ldur            x0, [fp, #-0x28]
    // 0x6435d8: stur            x2, [fp, #-8]
    // 0x6435dc: LoadField: r1 = r0->field_b
    //     0x6435dc: ldur            w1, [x0, #0xb]
    // 0x6435e0: LoadField: r3 = r0->field_f
    //     0x6435e0: ldur            w3, [x0, #0xf]
    // 0x6435e4: DecompressPointer r3
    //     0x6435e4: add             x3, x3, HEAP, lsl #32
    // 0x6435e8: LoadField: r4 = r3->field_b
    //     0x6435e8: ldur            w4, [x3, #0xb]
    // 0x6435ec: r3 = LoadInt32Instr(r1)
    //     0x6435ec: sbfx            x3, x1, #1, #0x1f
    // 0x6435f0: stur            x3, [fp, #-0x18]
    // 0x6435f4: r1 = LoadInt32Instr(r4)
    //     0x6435f4: sbfx            x1, x4, #1, #0x1f
    // 0x6435f8: cmp             x3, x1
    // 0x6435fc: b.ne            #0x643608
    // 0x643600: mov             x1, x0
    // 0x643604: r0 = _growToNextCapacity()
    //     0x643604: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x643608: ldur            x2, [fp, #-0x28]
    // 0x64360c: ldur            x3, [fp, #-0x18]
    // 0x643610: add             x0, x3, #1
    // 0x643614: lsl             x1, x0, #1
    // 0x643618: StoreField: r2->field_b = r1
    //     0x643618: stur            w1, [x2, #0xb]
    // 0x64361c: LoadField: r1 = r2->field_f
    //     0x64361c: ldur            w1, [x2, #0xf]
    // 0x643620: DecompressPointer r1
    //     0x643620: add             x1, x1, HEAP, lsl #32
    // 0x643624: ldur            x0, [fp, #-8]
    // 0x643628: ArrayStore: r1[r3] = r0  ; List_4
    //     0x643628: add             x25, x1, x3, lsl #2
    //     0x64362c: add             x25, x25, #0xf
    //     0x643630: str             w0, [x25]
    //     0x643634: tbz             w0, #0, #0x643650
    //     0x643638: ldurb           w16, [x1, #-1]
    //     0x64363c: ldurb           w17, [x0, #-1]
    //     0x643640: and             x16, x17, x16, lsr #2
    //     0x643644: tst             x16, HEAP, lsr #32
    //     0x643648: b.eq            #0x643650
    //     0x64364c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x643650: b               #0x643658
    // 0x643654: mov             x2, x3
    // 0x643658: mov             x0, x2
    // 0x64365c: LeaveFrame
    //     0x64365c: mov             SP, fp
    //     0x643660: ldp             fp, lr, [SP], #0x10
    // 0x643664: ret
    //     0x643664: ret             
    // 0x643668: mov             x0, x1
    // 0x64366c: r1 = 0
    //     0x64366c: movz            x1, #0
    // 0x643670: cmp             x1, x0
    // 0x643674: b.hs            #0x64374c
    // 0x643678: LoadField: r0 = r2->field_f
    //     0x643678: ldur            w0, [x2, #0xf]
    // 0x64367c: DecompressPointer r0
    //     0x64367c: add             x0, x0, HEAP, lsl #32
    // 0x643680: LoadField: r1 = r0->field_f
    //     0x643680: ldur            w1, [x0, #0xf]
    // 0x643684: DecompressPointer r1
    //     0x643684: add             x1, x1, HEAP, lsl #32
    // 0x643688: cmp             w1, NULL
    // 0x64368c: b.ne            #0x6436bc
    // 0x643690: mov             x0, x1
    // 0x643694: mov             x2, x3
    // 0x643698: r1 = Null
    //     0x643698: mov             x1, NULL
    // 0x64369c: cmp             w2, NULL
    // 0x6436a0: b.eq            #0x6436bc
    // 0x6436a4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6436a4: ldur            w4, [x2, #0x17]
    // 0x6436a8: DecompressPointer r4
    //     0x6436a8: add             x4, x4, HEAP, lsl #32
    // 0x6436ac: r8 = X0
    //     0x6436ac: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6436b0: LoadField: r9 = r4->field_7
    //     0x6436b0: ldur            x9, [x4, #7]
    // 0x6436b4: r3 = Null
    //     0x6436b4: ldr             x3, [PP, #0x9e0]  ; [pp+0x9e0] Null
    // 0x6436b8: blr             x9
    // 0x6436bc: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x6436bc: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x6436c0: r0 = Throw()
    //     0x6436c0: bl              #0xec04b8  ; ThrowStub
    // 0x6436c4: brk             #0
    // 0x6436c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6436c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6436cc: b               #0x642648
    // 0x6436d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6436d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6436d4: b               #0x642728
    // 0x6436d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6436d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6436dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6436dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6436e0: b               #0x6428b4
    // 0x6436e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6436e4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6436e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6436e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6436ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6436ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6436f0: b               #0x6429a0
    // 0x6436f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6436f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6436f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6436f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6436fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6436fc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x643700: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x643700: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x643704: b               #0x642b54
    // 0x643708: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x643708: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x64370c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x64370c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x643710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x643710: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x643714: b               #0x642c40
    // 0x643718: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x643718: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x64371c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x64371c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x643720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x643720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x643724: b               #0x642dbc
    // 0x643728: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x643728: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64372c: b               #0x642de0
    // 0x643730: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x643730: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x643734: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x643734: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x643738: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x643738: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x64373c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x64373c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x643740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x643740: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x643744: b               #0x643080
    // 0x643748: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x643748: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x64374c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x64374c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static List<StackFilter> _stackFilters() {
    // ** addr: 0x6446d4, size: 0x34
    // 0x6446d4: EnterFrame
    //     0x6446d4: stp             fp, lr, [SP, #-0x10]!
    //     0x6446d8: mov             fp, SP
    // 0x6446dc: CheckStackOverflow
    //     0x6446dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6446e0: cmp             SP, x16
    //     0x6446e4: b.ls            #0x644700
    // 0x6446e8: r1 = <StackFilter>
    //     0x6446e8: ldr             x1, [PP, #0x10f8]  ; [pp+0x10f8] TypeArguments: <StackFilter>
    // 0x6446ec: r2 = 0
    //     0x6446ec: movz            x2, #0
    // 0x6446f0: r0 = _GrowableList()
    //     0x6446f0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x6446f4: LeaveFrame
    //     0x6446f4: mov             SP, fp
    //     0x6446f8: ldp             fp, lr, [SP], #0x10
    // 0x6446fc: ret
    //     0x6446fc: ret             
    // 0x644700: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x644700: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x644704: b               #0x6446e8
  }
  [closure] static int <anonymous closure>(dynamic, int) {
    // ** addr: 0x644724, size: 0x3c
    // 0x644724: ldr             x2, [SP]
    // 0x644728: r3 = LoadInt32Instr(r2)
    //     0x644728: sbfx            x3, x2, #1, #0x1f
    //     0x64472c: tbz             w2, #0, #0x644734
    //     0x644730: ldur            x3, [x2, #7]
    // 0x644734: add             x2, x3, #1
    // 0x644738: r0 = BoxInt64Instr(r2)
    //     0x644738: sbfiz           x0, x2, #1, #0x1f
    //     0x64473c: cmp             x2, x0, asr #1
    //     0x644740: b.eq            #0x64475c
    //     0x644744: stp             fp, lr, [SP, #-0x10]!
    //     0x644748: mov             fp, SP
    //     0x64474c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x644750: mov             SP, fp
    //     0x644754: ldp             fp, lr, [SP], #0x10
    //     0x644758: stur            x2, [x0, #7]
    // 0x64475c: ret
    //     0x64475c: ret             
  }
  static (dynamic, StackTrace) => StackTrace demangleStackTrace() {
    // ** addr: 0x644760, size: 0x8
    // 0x644760: r0 = Closure: (StackTrace) => StackTrace from Function '_defaultStackTraceDemangler@34022608': static.
    //     0x644760: ldr             x0, [PP, #0x1128]  ; [pp+0x1128] Closure: (StackTrace) => StackTrace from Function '_defaultStackTraceDemangler@34022608': static. (0x7e54fb8bd554)
    // 0x644764: ret
    //     0x644764: ret             
  }
  static (dynamic, FlutterErrorDetails) => void presentError() {
    // ** addr: 0x644770, size: 0x8
    // 0x644770: r0 = Closure: (FlutterErrorDetails, {bool forceReport}) => void from Function 'dumpErrorToConsole': static.
    //     0x644770: ldr             x0, [PP, #0x470]  ; [pp+0x470] Closure: (FlutterErrorDetails, {bool forceReport}) => void from Function 'dumpErrorToConsole': static. (0x7e54fb03f780)
    // 0x644774: ret
    //     0x644774: ret             
  }
  factory _ FlutterError(/* No info */) {
    // ** addr: 0x6803c0, size: 0x100
    // 0x6803c0: EnterFrame
    //     0x6803c0: stp             fp, lr, [SP, #-0x10]!
    //     0x6803c4: mov             fp, SP
    // 0x6803c8: AllocStack(0x30)
    //     0x6803c8: sub             SP, SP, #0x30
    // 0x6803cc: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x6803cc: mov             x0, x1
    //     0x6803d0: mov             x1, x2
    // 0x6803d4: CheckStackOverflow
    //     0x6803d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6803d8: cmp             SP, x16
    //     0x6803dc: b.ls            #0x6804b8
    // 0x6803e0: r0 = LoadClassIdInstr(r1)
    //     0x6803e0: ldur            x0, [x1, #-1]
    //     0x6803e4: ubfx            x0, x0, #0xc, #0x14
    // 0x6803e8: r2 = "\n"
    //     0x6803e8: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0x6803ec: r0 = GDT[cid_x0 + -0x1000]()
    //     0x6803ec: sub             lr, x0, #1, lsl #12
    //     0x6803f0: ldr             lr, [x21, lr, lsl #3]
    //     0x6803f4: blr             lr
    // 0x6803f8: mov             x1, x0
    // 0x6803fc: stur            x0, [fp, #-8]
    // 0x680400: r0 = first()
    //     0x680400: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x680404: r1 = <List<Object>>
    //     0x680404: ldr             x1, [PP, #0x440]  ; [pp+0x440] TypeArguments: <List<Object>>
    // 0x680408: stur            x0, [fp, #-0x10]
    // 0x68040c: r0 = ErrorSummary()
    //     0x68040c: bl              #0x6804cc  ; AllocateErrorSummaryStub -> ErrorSummary (size=0x2c)
    // 0x680410: mov             x1, x0
    // 0x680414: ldur            x2, [fp, #-0x10]
    // 0x680418: r3 = Instance_DiagnosticLevel
    //     0x680418: ldr             x3, [PP, #0x2748]  ; [pp+0x2748] Obj!DiagnosticLevel@e37041
    // 0x68041c: stur            x0, [fp, #-0x10]
    // 0x680420: r0 = _ErrorDiagnostic()
    //     0x680420: bl              #0x644784  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::_ErrorDiagnostic
    // 0x680424: r1 = Null
    //     0x680424: mov             x1, NULL
    // 0x680428: r2 = 2
    //     0x680428: movz            x2, #0x2
    // 0x68042c: r0 = AllocateArray()
    //     0x68042c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x680430: mov             x2, x0
    // 0x680434: ldur            x0, [fp, #-0x10]
    // 0x680438: stur            x2, [fp, #-0x18]
    // 0x68043c: StoreField: r2->field_f = r0
    //     0x68043c: stur            w0, [x2, #0xf]
    // 0x680440: r1 = <DiagnosticsNode>
    //     0x680440: ldr             x1, [PP, #0x23c0]  ; [pp+0x23c0] TypeArguments: <DiagnosticsNode>
    // 0x680444: r0 = AllocateGrowableArray()
    //     0x680444: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x680448: mov             x3, x0
    // 0x68044c: ldur            x0, [fp, #-0x18]
    // 0x680450: stur            x3, [fp, #-0x10]
    // 0x680454: StoreField: r3->field_f = r0
    //     0x680454: stur            w0, [x3, #0xf]
    // 0x680458: r0 = 2
    //     0x680458: movz            x0, #0x2
    // 0x68045c: StoreField: r3->field_b = r0
    //     0x68045c: stur            w0, [x3, #0xb]
    // 0x680460: ldur            x1, [fp, #-8]
    // 0x680464: r2 = 1
    //     0x680464: movz            x2, #0x1
    // 0x680468: r0 = skip()
    //     0x680468: bl              #0xa5a484  ; [dart:collection] ListBase::skip
    // 0x68046c: r1 = Function '<anonymous closure>': static.
    //     0x68046c: ldr             x1, [PP, #0x3d60]  ; [pp+0x3d60] AnonymousClosure: static (0x6804d8), in [package:flutter/src/foundation/assertions.dart] FlutterError::FlutterError (0x6803c0)
    // 0x680470: r2 = Null
    //     0x680470: mov             x2, NULL
    // 0x680474: stur            x0, [fp, #-8]
    // 0x680478: r0 = AllocateClosure()
    //     0x680478: bl              #0xec1630  ; AllocateClosureStub
    // 0x68047c: r16 = <DiagnosticsNode>
    //     0x68047c: ldr             x16, [PP, #0x23c0]  ; [pp+0x23c0] TypeArguments: <DiagnosticsNode>
    // 0x680480: ldur            lr, [fp, #-8]
    // 0x680484: stp             lr, x16, [SP, #8]
    // 0x680488: str             x0, [SP]
    // 0x68048c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x68048c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x680490: r0 = map()
    //     0x680490: bl              #0x7abe64  ; [dart:_internal] ListIterable::map
    // 0x680494: ldur            x1, [fp, #-0x10]
    // 0x680498: mov             x2, x0
    // 0x68049c: r0 = addAll()
    //     0x68049c: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x6804a0: r0 = FlutterError()
    //     0x6804a0: bl              #0x6804c0  ; AllocateFlutterErrorStub -> FlutterError (size=0x10)
    // 0x6804a4: ldur            x1, [fp, #-0x10]
    // 0x6804a8: StoreField: r0->field_b = r1
    //     0x6804a8: stur            w1, [x0, #0xb]
    // 0x6804ac: LeaveFrame
    //     0x6804ac: mov             SP, fp
    //     0x6804b0: ldp             fp, lr, [SP], #0x10
    // 0x6804b4: ret
    //     0x6804b4: ret             
    // 0x6804b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6804b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6804bc: b               #0x6803e0
  }
  [closure] static ErrorDescription <anonymous closure>(dynamic, String) {
    // ** addr: 0x6804d8, size: 0x4c
    // 0x6804d8: EnterFrame
    //     0x6804d8: stp             fp, lr, [SP, #-0x10]!
    //     0x6804dc: mov             fp, SP
    // 0x6804e0: AllocStack(0x8)
    //     0x6804e0: sub             SP, SP, #8
    // 0x6804e4: CheckStackOverflow
    //     0x6804e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6804e8: cmp             SP, x16
    //     0x6804ec: b.ls            #0x68051c
    // 0x6804f0: r1 = <List<Object>>
    //     0x6804f0: ldr             x1, [PP, #0x440]  ; [pp+0x440] TypeArguments: <List<Object>>
    // 0x6804f4: r0 = ErrorDescription()
    //     0x6804f4: bl              #0x64483c  ; AllocateErrorDescriptionStub -> ErrorDescription (size=0x2c)
    // 0x6804f8: mov             x1, x0
    // 0x6804fc: ldr             x2, [fp, #0x10]
    // 0x680500: r3 = Instance_DiagnosticLevel
    //     0x680500: ldr             x3, [PP, #0x450]  ; [pp+0x450] Obj!DiagnosticLevel@e37021
    // 0x680504: stur            x0, [fp, #-8]
    // 0x680508: r0 = _ErrorDiagnostic()
    //     0x680508: bl              #0x644784  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::_ErrorDiagnostic
    // 0x68050c: ldur            x0, [fp, #-8]
    // 0x680510: LeaveFrame
    //     0x680510: mov             SP, fp
    //     0x680514: ldp             fp, lr, [SP], #0x10
    // 0x680518: ret
    //     0x680518: ret             
    // 0x68051c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68051c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x680520: b               #0x6804f0
  }
  _ toString(/* No info */) {
    // ** addr: 0xbfd87c, size: 0xb4
    // 0xbfd87c: EnterFrame
    //     0xbfd87c: stp             fp, lr, [SP, #-0x10]!
    //     0xbfd880: mov             fp, SP
    // 0xbfd884: AllocStack(0x18)
    //     0xbfd884: sub             SP, SP, #0x18
    // 0xbfd888: SetupParameters(FlutterError this /* r0 */)
    //     0xbfd888: ldur            w0, [x4, #0x13]
    //     0xbfd88c: sub             x1, x0, #2
    //     0xbfd890: add             x0, fp, w1, sxtw #2
    //     0xbfd894: ldr             x0, [x0, #0x10]
    // 0xbfd898: CheckStackOverflow
    //     0xbfd898: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfd89c: cmp             SP, x16
    //     0xbfd8a0: b.ls            #0xbfd928
    // 0xbfd8a4: LoadField: r1 = r0->field_b
    //     0xbfd8a4: ldur            w1, [x0, #0xb]
    // 0xbfd8a8: DecompressPointer r1
    //     0xbfd8a8: add             x1, x1, HEAP, lsl #32
    // 0xbfd8ac: r16 = <_ErrorDiagnostic>
    //     0xbfd8ac: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1db68] TypeArguments: <_ErrorDiagnostic>
    //     0xbfd8b0: ldr             x16, [x16, #0xb68]
    // 0xbfd8b4: stp             x1, x16, [SP]
    // 0xbfd8b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbfd8b8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbfd8bc: r0 = whereType()
    //     0xbfd8bc: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0xbfd8c0: mov             x1, x0
    // 0xbfd8c4: stur            x0, [fp, #-8]
    // 0xbfd8c8: r0 = iterator()
    //     0xbfd8c8: bl              #0x888158  ; [dart:_internal] WhereTypeIterable::iterator
    // 0xbfd8cc: r1 = LoadClassIdInstr(r0)
    //     0xbfd8cc: ldur            x1, [x0, #-1]
    //     0xbfd8d0: ubfx            x1, x1, #0xc, #0x14
    // 0xbfd8d4: mov             x16, x0
    // 0xbfd8d8: mov             x0, x1
    // 0xbfd8dc: mov             x1, x16
    // 0xbfd8e0: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xbfd8e0: movz            x17, #0x292d
    //     0xbfd8e4: movk            x17, #0x1, lsl #16
    //     0xbfd8e8: add             lr, x0, x17
    //     0xbfd8ec: ldr             lr, [x21, lr, lsl #3]
    //     0xbfd8f0: blr             lr
    // 0xbfd8f4: eor             x1, x0, #0x10
    // 0xbfd8f8: eor             x0, x1, #0x10
    // 0xbfd8fc: tbnz            w0, #4, #0xbfd914
    // 0xbfd900: ldur            x1, [fp, #-8]
    // 0xbfd904: r0 = first()
    //     0xbfd904: bl              #0x8939e0  ; [dart:core] Iterable::first
    // 0xbfd908: mov             x1, x0
    // 0xbfd90c: r0 = valueToString()
    //     0xbfd90c: bl              #0xbfd930  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::valueToString
    // 0xbfd910: b               #0xbfd91c
    // 0xbfd914: r0 = "FlutterError"
    //     0xbfd914: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1db70] "FlutterError"
    //     0xbfd918: ldr             x0, [x0, #0xb70]
    // 0xbfd91c: LeaveFrame
    //     0xbfd91c: mov             SP, fp
    //     0xbfd920: ldp             fp, lr, [SP], #0x10
    // 0xbfd924: ret
    //     0xbfd924: ret             
    // 0xbfd928: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfd928: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfd92c: b               #0xbfd8a4
  }
  get _ message(/* No info */) {
    // ** addr: 0xd5dce4, size: 0x38
    // 0xd5dce4: EnterFrame
    //     0xd5dce4: stp             fp, lr, [SP, #-0x10]!
    //     0xd5dce8: mov             fp, SP
    // 0xd5dcec: AllocStack(0x8)
    //     0xd5dcec: sub             SP, SP, #8
    // 0xd5dcf0: CheckStackOverflow
    //     0xd5dcf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd5dcf4: cmp             SP, x16
    //     0xd5dcf8: b.ls            #0xd5dd14
    // 0xd5dcfc: str             x1, [SP]
    // 0xd5dd00: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xd5dd00: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xd5dd04: r0 = toString()
    //     0xd5dd04: bl              #0xbfd87c  ; [package:flutter/src/foundation/assertions.dart] FlutterError::toString
    // 0xd5dd08: LeaveFrame
    //     0xd5dd08: mov             SP, fp
    //     0xd5dd0c: ldp             fp, lr, [SP], #0x10
    // 0xd5dd10: ret
    //     0xd5dd10: ret             
    // 0xd5dd14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd5dd14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd5dd18: b               #0xd5dcfc
  }
}
