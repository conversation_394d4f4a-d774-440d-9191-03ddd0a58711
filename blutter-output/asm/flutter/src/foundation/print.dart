// lib: , url: package:flutter/src/foundation/print.dart

// class id: 1048819, size: 0x8
class :: {

  static late (dynamic, String?, {int? wrapWidth}) => void debugPrint; // offset: 0x674
  static late final Queue<String> _debugPrintBuffer; // offset: 0x67c
  static late final RegExp _indentPattern; // offset: 0x68c
  static late final Stopwatch _debugPrintStopwatch; // offset: 0x680

  [closure] static void debugPrintThrottled(dynamic, String?, {int? wrapWidth}) {
    // ** addr: 0x63f99c, size: 0x7c
    // 0x63f99c: EnterFrame
    //     0x63f99c: stp             fp, lr, [SP, #-0x10]!
    //     0x63f9a0: mov             fp, SP
    // 0x63f9a4: AllocStack(0x8)
    //     0x63f9a4: sub             SP, SP, #8
    // 0x63f9a8: SetupParameters(dynamic _ /* r2 */, {dynamic wrapWidth = Null /* r0 */})
    //     0x63f9a8: ldur            w0, [x4, #0x13]
    //     0x63f9ac: sub             x1, x0, #4
    //     0x63f9b0: add             x2, fp, w1, sxtw #2
    //     0x63f9b4: ldr             x2, [x2, #0x10]
    //     0x63f9b8: ldur            w1, [x4, #0x1f]
    //     0x63f9bc: add             x1, x1, HEAP, lsl #32
    //     0x63f9c0: ldr             x16, [PP, #0x4a8]  ; [pp+0x4a8] "wrapWidth"
    //     0x63f9c4: cmp             w1, w16
    //     0x63f9c8: b.ne            #0x63f9e4
    //     0x63f9cc: ldur            w1, [x4, #0x23]
    //     0x63f9d0: add             x1, x1, HEAP, lsl #32
    //     0x63f9d4: sub             w3, w0, w1
    //     0x63f9d8: add             x0, fp, w3, sxtw #2
    //     0x63f9dc: ldr             x0, [x0, #8]
    //     0x63f9e0: b               #0x63f9e8
    //     0x63f9e4: mov             x0, NULL
    // 0x63f9e8: CheckStackOverflow
    //     0x63f9e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63f9ec: cmp             SP, x16
    //     0x63f9f0: b.ls            #0x63fa10
    // 0x63f9f4: str             x0, [SP]
    // 0x63f9f8: mov             x1, x2
    // 0x63f9fc: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0x63f9fc: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0x63fa00: r0 = debugPrintThrottled()
    //     0x63fa00: bl              #0x63fa18  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0x63fa04: LeaveFrame
    //     0x63fa04: mov             SP, fp
    //     0x63fa08: ldp             fp, lr, [SP], #0x10
    // 0x63fa0c: ret
    //     0x63fa0c: ret             
    // 0x63fa10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63fa10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63fa14: b               #0x63f9f4
  }
  static _ debugPrintThrottled(/* No info */) {
    // ** addr: 0x63fa18, size: 0x198
    // 0x63fa18: EnterFrame
    //     0x63fa18: stp             fp, lr, [SP, #-0x10]!
    //     0x63fa1c: mov             fp, SP
    // 0x63fa20: AllocStack(0x30)
    //     0x63fa20: sub             SP, SP, #0x30
    // 0x63fa24: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */, {dynamic wrapWidth = Null /* r0, fp-0x8 */})
    //     0x63fa24: stur            x1, [fp, #-0x10]
    //     0x63fa28: ldur            w0, [x4, #0x13]
    //     0x63fa2c: ldur            w2, [x4, #0x1f]
    //     0x63fa30: add             x2, x2, HEAP, lsl #32
    //     0x63fa34: ldr             x16, [PP, #0x4a8]  ; [pp+0x4a8] "wrapWidth"
    //     0x63fa38: cmp             w2, w16
    //     0x63fa3c: b.ne            #0x63fa58
    //     0x63fa40: ldur            w2, [x4, #0x23]
    //     0x63fa44: add             x2, x2, HEAP, lsl #32
    //     0x63fa48: sub             w3, w0, w2
    //     0x63fa4c: add             x0, fp, w3, sxtw #2
    //     0x63fa50: ldr             x0, [x0, #8]
    //     0x63fa54: b               #0x63fa5c
    //     0x63fa58: mov             x0, NULL
    //     0x63fa5c: stur            x0, [fp, #-8]
    // 0x63fa60: CheckStackOverflow
    //     0x63fa60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63fa64: cmp             SP, x16
    //     0x63fa68: b.ls            #0x63fba8
    // 0x63fa6c: r1 = 1
    //     0x63fa6c: movz            x1, #0x1
    // 0x63fa70: r0 = AllocateContext()
    //     0x63fa70: bl              #0xec126c  ; AllocateContextStub
    // 0x63fa74: mov             x3, x0
    // 0x63fa78: ldur            x0, [fp, #-8]
    // 0x63fa7c: stur            x3, [fp, #-0x18]
    // 0x63fa80: StoreField: r3->field_f = r0
    //     0x63fa80: stur            w0, [x3, #0xf]
    // 0x63fa84: ldur            x1, [fp, #-0x10]
    // 0x63fa88: cmp             w1, NULL
    // 0x63fa8c: b.ne            #0x63fa98
    // 0x63fa90: r0 = Null
    //     0x63fa90: mov             x0, NULL
    // 0x63fa94: b               #0x63fab0
    // 0x63fa98: r0 = LoadClassIdInstr(r1)
    //     0x63fa98: ldur            x0, [x1, #-1]
    //     0x63fa9c: ubfx            x0, x0, #0xc, #0x14
    // 0x63faa0: r2 = "\n"
    //     0x63faa0: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0x63faa4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x63faa4: sub             lr, x0, #1, lsl #12
    //     0x63faa8: ldr             lr, [x21, lr, lsl #3]
    //     0x63faac: blr             lr
    // 0x63fab0: cmp             w0, NULL
    // 0x63fab4: b.ne            #0x63faf4
    // 0x63fab8: r0 = 2
    //     0x63fab8: movz            x0, #0x2
    // 0x63fabc: mov             x2, x0
    // 0x63fac0: r1 = Null
    //     0x63fac0: mov             x1, NULL
    // 0x63fac4: r0 = AllocateArray()
    //     0x63fac4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x63fac8: stur            x0, [fp, #-8]
    // 0x63facc: r16 = "null"
    //     0x63facc: ldr             x16, [PP, #0x4b8]  ; [pp+0x4b8] "null"
    // 0x63fad0: StoreField: r0->field_f = r16
    //     0x63fad0: stur            w16, [x0, #0xf]
    // 0x63fad4: r1 = <String>
    //     0x63fad4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x63fad8: r0 = AllocateGrowableArray()
    //     0x63fad8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x63fadc: mov             x1, x0
    // 0x63fae0: ldur            x0, [fp, #-8]
    // 0x63fae4: StoreField: r1->field_f = r0
    //     0x63fae4: stur            w0, [x1, #0xf]
    // 0x63fae8: r0 = 2
    //     0x63fae8: movz            x0, #0x2
    // 0x63faec: StoreField: r1->field_b = r0
    //     0x63faec: stur            w0, [x1, #0xb]
    // 0x63faf0: mov             x0, x1
    // 0x63faf4: ldur            x2, [fp, #-0x18]
    // 0x63faf8: stur            x0, [fp, #-8]
    // 0x63fafc: LoadField: r1 = r2->field_f
    //     0x63fafc: ldur            w1, [x2, #0xf]
    // 0x63fb00: DecompressPointer r1
    //     0x63fb00: add             x1, x1, HEAP, lsl #32
    // 0x63fb04: cmp             w1, NULL
    // 0x63fb08: b.eq            #0x63fb60
    // 0x63fb0c: r0 = InitLateStaticField(0x67c) // [package:flutter/src/foundation/print.dart] ::_debugPrintBuffer
    //     0x63fb0c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63fb10: ldr             x0, [x0, #0xcf8]
    //     0x63fb14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x63fb18: cmp             w0, w16
    //     0x63fb1c: b.ne            #0x63fb28
    //     0x63fb20: ldr             x2, [PP, #0x4c0]  ; [pp+0x4c0] Field <::._debugPrintBuffer@48110992>: static late final (offset: 0x67c)
    //     0x63fb24: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x63fb28: ldur            x2, [fp, #-0x18]
    // 0x63fb2c: r1 = Function '<anonymous closure>': static.
    //     0x63fb2c: ldr             x1, [PP, #0x4c8]  ; [pp+0x4c8] AnonymousClosure: static (0x640e80), in [package:flutter/src/foundation/print.dart] ::debugPrintThrottled (0x63fa18)
    // 0x63fb30: stur            x0, [fp, #-0x10]
    // 0x63fb34: r0 = AllocateClosure()
    //     0x63fb34: bl              #0xec1630  ; AllocateClosureStub
    // 0x63fb38: r16 = <String>
    //     0x63fb38: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x63fb3c: ldur            lr, [fp, #-8]
    // 0x63fb40: stp             lr, x16, [SP, #8]
    // 0x63fb44: str             x0, [SP]
    // 0x63fb48: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x63fb48: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x63fb4c: r0 = expand()
    //     0x63fb4c: bl              #0x6409ac  ; [dart:collection] ListBase::expand
    // 0x63fb50: ldur            x1, [fp, #-0x10]
    // 0x63fb54: mov             x2, x0
    // 0x63fb58: r0 = addAll()
    //     0x63fb58: bl              #0x6404a0  ; [dart:collection] ListQueue::addAll
    // 0x63fb5c: b               #0x63fb88
    // 0x63fb60: r0 = InitLateStaticField(0x67c) // [package:flutter/src/foundation/print.dart] ::_debugPrintBuffer
    //     0x63fb60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63fb64: ldr             x0, [x0, #0xcf8]
    //     0x63fb68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x63fb6c: cmp             w0, w16
    //     0x63fb70: b.ne            #0x63fb7c
    //     0x63fb74: ldr             x2, [PP, #0x4c0]  ; [pp+0x4c0] Field <::._debugPrintBuffer@48110992>: static late final (offset: 0x67c)
    //     0x63fb78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x63fb7c: mov             x1, x0
    // 0x63fb80: ldur            x2, [fp, #-8]
    // 0x63fb84: r0 = addAll()
    //     0x63fb84: bl              #0x6404a0  ; [dart:collection] ListQueue::addAll
    // 0x63fb88: r0 = LoadStaticField(0x688)
    //     0x63fb88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63fb8c: ldr             x0, [x0, #0xd10]
    // 0x63fb90: tbz             w0, #4, #0x63fb98
    // 0x63fb94: r0 = _debugPrintTask()
    //     0x63fb94: bl              #0x63fbb0  ; [package:flutter/src/foundation/print.dart] ::_debugPrintTask
    // 0x63fb98: r0 = Null
    //     0x63fb98: mov             x0, NULL
    // 0x63fb9c: LeaveFrame
    //     0x63fb9c: mov             SP, fp
    //     0x63fba0: ldp             fp, lr, [SP], #0x10
    // 0x63fba4: ret
    //     0x63fba4: ret             
    // 0x63fba8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63fba8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63fbac: b               #0x63fa6c
  }
  static void _debugPrintTask() {
    // ** addr: 0x63fbb0, size: 0x258
    // 0x63fbb0: EnterFrame
    //     0x63fbb0: stp             fp, lr, [SP, #-0x10]!
    //     0x63fbb4: mov             fp, SP
    // 0x63fbb8: AllocStack(0x18)
    //     0x63fbb8: sub             SP, SP, #0x18
    // 0x63fbbc: r0 = false
    //     0x63fbbc: add             x0, NULL, #0x30  ; false
    // 0x63fbc0: CheckStackOverflow
    //     0x63fbc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63fbc4: cmp             SP, x16
    //     0x63fbc8: b.ls            #0x63fdf8
    // 0x63fbcc: StoreStaticField(0x688, r0)
    //     0x63fbcc: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x63fbd0: str             x0, [x1, #0xd10]
    // 0x63fbd4: r0 = InitLateStaticField(0x680) // [package:flutter/src/foundation/print.dart] ::_debugPrintStopwatch
    //     0x63fbd4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63fbd8: ldr             x0, [x0, #0xd00]
    //     0x63fbdc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x63fbe0: cmp             w0, w16
    //     0x63fbe4: b.ne            #0x63fbf0
    //     0x63fbe8: ldr             x2, [PP, #0x640]  ; [pp+0x640] Field <::._debugPrintStopwatch@48110992>: static late final (offset: 0x680)
    //     0x63fbec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x63fbf0: mov             x1, x0
    // 0x63fbf4: stur            x0, [fp, #-8]
    // 0x63fbf8: r0 = elapsed()
    //     0x63fbf8: bl              #0x640174  ; [dart:core] Stopwatch::elapsed
    // 0x63fbfc: LoadField: r1 = r0->field_7
    //     0x63fbfc: ldur            x1, [x0, #7]
    // 0x63fc00: r17 = 1000000
    //     0x63fc00: movz            x17, #0x4240
    //     0x63fc04: movk            x17, #0xf, lsl #16
    // 0x63fc08: cmp             x1, x17
    // 0x63fc0c: b.le            #0x63fc30
    // 0x63fc10: ldur            x1, [fp, #-8]
    // 0x63fc14: r0 = stop()
    //     0x63fc14: bl              #0x640108  ; [dart:core] Stopwatch::stop
    // 0x63fc18: ldur            x1, [fp, #-8]
    // 0x63fc1c: r0 = reset()
    //     0x63fc1c: bl              #0x640094  ; [dart:core] Stopwatch::reset
    // 0x63fc20: r0 = 0
    //     0x63fc20: movz            x0, #0
    // 0x63fc24: StoreStaticField(0x678, r0)
    //     0x63fc24: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x63fc28: str             x0, [x1, #0xcf0]
    // 0x63fc2c: b               #0x63fc34
    // 0x63fc30: r0 = 0
    //     0x63fc30: movz            x0, #0
    // 0x63fc34: CheckStackOverflow
    //     0x63fc34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63fc38: cmp             SP, x16
    //     0x63fc3c: b.ls            #0x63fe00
    // 0x63fc40: r1 = LoadStaticField(0x678)
    //     0x63fc40: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x63fc44: ldr             x1, [x1, #0xcf0]
    // 0x63fc48: r2 = LoadInt32Instr(r1)
    //     0x63fc48: sbfx            x2, x1, #1, #0x1f
    //     0x63fc4c: tbz             w1, #0, #0x63fc54
    //     0x63fc50: ldur            x2, [x1, #7]
    // 0x63fc54: cmp             x2, #3, lsl #12
    // 0x63fc58: b.ge            #0x63fd04
    // 0x63fc5c: r0 = InitLateStaticField(0x67c) // [package:flutter/src/foundation/print.dart] ::_debugPrintBuffer
    //     0x63fc5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63fc60: ldr             x0, [x0, #0xcf8]
    //     0x63fc64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x63fc68: cmp             w0, w16
    //     0x63fc6c: b.ne            #0x63fc78
    //     0x63fc70: ldr             x2, [PP, #0x4c0]  ; [pp+0x4c0] Field <::._debugPrintBuffer@48110992>: static late final (offset: 0x67c)
    //     0x63fc74: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x63fc78: LoadField: r1 = r0->field_f
    //     0x63fc78: ldur            x1, [x0, #0xf]
    // 0x63fc7c: ArrayLoad: r2 = r0[0]  ; List_8
    //     0x63fc7c: ldur            x2, [x0, #0x17]
    // 0x63fc80: cmp             x1, x2
    // 0x63fc84: b.eq            #0x63fd04
    // 0x63fc88: mov             x1, x0
    // 0x63fc8c: r0 = removeFirst()
    //     0x63fc8c: bl              #0x61a918  ; [dart:collection] ListQueue::removeFirst
    // 0x63fc90: mov             x2, x0
    // 0x63fc94: r0 = LoadStaticField(0x678)
    //     0x63fc94: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63fc98: ldr             x0, [x0, #0xcf0]
    // 0x63fc9c: LoadField: r1 = r2->field_7
    //     0x63fc9c: ldur            w1, [x2, #7]
    // 0x63fca0: r3 = LoadInt32Instr(r0)
    //     0x63fca0: sbfx            x3, x0, #1, #0x1f
    //     0x63fca4: tbz             w0, #0, #0x63fcac
    //     0x63fca8: ldur            x3, [x0, #7]
    // 0x63fcac: r0 = LoadInt32Instr(r1)
    //     0x63fcac: sbfx            x0, x1, #1, #0x1f
    // 0x63fcb0: add             x4, x3, x0
    // 0x63fcb4: r0 = BoxInt64Instr(r4)
    //     0x63fcb4: sbfiz           x0, x4, #1, #0x1f
    //     0x63fcb8: cmp             x4, x0, asr #1
    //     0x63fcbc: b.eq            #0x63fcc8
    //     0x63fcc0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x63fcc4: stur            x4, [x0, #7]
    // 0x63fcc8: StoreStaticField(0x678, r0)
    //     0x63fcc8: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x63fccc: str             x0, [x1, #0xcf0]
    // 0x63fcd0: str             x2, [SP]
    // 0x63fcd4: r0 = _interpolateSingle()
    //     0x63fcd4: bl              #0x5fff0c  ; [dart:core] _StringBase::_interpolateSingle
    // 0x63fcd8: r1 = LoadStaticField(0x318)
    //     0x63fcd8: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x63fcdc: ldr             x1, [x1, #0x630]
    // 0x63fce0: cmp             w1, NULL
    // 0x63fce4: b.ne            #0x63fcf4
    // 0x63fce8: mov             x1, x0
    // 0x63fcec: r0 = printToConsole()
    //     0x63fcec: bl              #0x640030  ; [dart:_internal] ::printToConsole
    // 0x63fcf0: b               #0x63fcfc
    // 0x63fcf4: mov             x1, x0
    // 0x63fcf8: r0 = _printToZone()
    //     0x63fcf8: bl              #0x63ffb8  ; [dart:async] ::_printToZone
    // 0x63fcfc: r0 = 0
    //     0x63fcfc: movz            x0, #0
    // 0x63fd00: b               #0x63fc34
    // 0x63fd04: r0 = InitLateStaticField(0x67c) // [package:flutter/src/foundation/print.dart] ::_debugPrintBuffer
    //     0x63fd04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63fd08: ldr             x0, [x0, #0xcf8]
    //     0x63fd0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x63fd10: cmp             w0, w16
    //     0x63fd14: b.ne            #0x63fd20
    //     0x63fd18: ldr             x2, [PP, #0x4c0]  ; [pp+0x4c0] Field <::._debugPrintBuffer@48110992>: static late final (offset: 0x67c)
    //     0x63fd1c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x63fd20: mov             x1, x0
    // 0x63fd24: r0 = isNotEmpty()
    //     0x63fd24: bl              #0x874164  ; [dart:core] Iterable::isNotEmpty
    // 0x63fd28: tbnz            w0, #4, #0x63fdc0
    // 0x63fd2c: r1 = true
    //     0x63fd2c: add             x1, NULL, #0x20  ; true
    // 0x63fd30: r0 = 0
    //     0x63fd30: movz            x0, #0
    // 0x63fd34: StoreStaticField(0x688, r1)
    //     0x63fd34: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x63fd38: str             x1, [x2, #0xd10]
    // 0x63fd3c: StoreStaticField(0x678, r0)
    //     0x63fd3c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x63fd40: str             x0, [x1, #0xcf0]
    // 0x63fd44: r1 = Null
    //     0x63fd44: mov             x1, NULL
    // 0x63fd48: r2 = Instance_Duration
    //     0x63fd48: ldr             x2, [PP, #0x648]  ; [pp+0x648] Obj!Duration@e3a071
    // 0x63fd4c: r3 = Closure: () => void from Function '_debugPrintTask@48110992': static.
    //     0x63fd4c: ldr             x3, [PP, #0x650]  ; [pp+0x650] Closure: () => void from Function '_debugPrintTask@48110992': static. (0x7e54fb03fe94)
    // 0x63fd50: r0 = Timer()
    //     0x63fd50: bl              #0x5f9778  ; [dart:async] Timer::Timer
    // 0x63fd54: r0 = LoadStaticField(0x684)
    //     0x63fd54: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63fd58: ldr             x0, [x0, #0xd08]
    // 0x63fd5c: cmp             w0, NULL
    // 0x63fd60: b.ne            #0x63fdb8
    // 0x63fd64: r1 = <void?>
    //     0x63fd64: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x63fd68: r0 = _Future()
    //     0x63fd68: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x63fd6c: stur            x0, [fp, #-0x10]
    // 0x63fd70: StoreField: r0->field_b = rZR
    //     0x63fd70: stur            xzr, [x0, #0xb]
    // 0x63fd74: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x63fd74: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63fd78: ldr             x0, [x0, #0x7a0]
    //     0x63fd7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x63fd80: cmp             w0, w16
    //     0x63fd84: b.ne            #0x63fd90
    //     0x63fd88: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x63fd8c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x63fd90: mov             x1, x0
    // 0x63fd94: ldur            x0, [fp, #-0x10]
    // 0x63fd98: StoreField: r0->field_13 = r1
    //     0x63fd98: stur            w1, [x0, #0x13]
    // 0x63fd9c: r1 = <void?>
    //     0x63fd9c: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x63fda0: r0 = _AsyncCompleter()
    //     0x63fda0: bl              #0x661210  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x63fda4: mov             x1, x0
    // 0x63fda8: ldur            x0, [fp, #-0x10]
    // 0x63fdac: StoreField: r1->field_b = r0
    //     0x63fdac: stur            w0, [x1, #0xb]
    // 0x63fdb0: StoreStaticField(0x684, r1)
    //     0x63fdb0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x63fdb4: str             x1, [x0, #0xd08]
    // 0x63fdb8: r0 = Null
    //     0x63fdb8: mov             x0, NULL
    // 0x63fdbc: b               #0x63fdec
    // 0x63fdc0: ldur            x1, [fp, #-8]
    // 0x63fdc4: r0 = start()
    //     0x63fdc4: bl              #0x63fec0  ; [dart:core] Stopwatch::start
    // 0x63fdc8: r1 = LoadStaticField(0x684)
    //     0x63fdc8: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x63fdcc: ldr             x1, [x1, #0xd08]
    // 0x63fdd0: cmp             w1, NULL
    // 0x63fdd4: b.eq            #0x63fde0
    // 0x63fdd8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x63fdd8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x63fddc: r0 = complete()
    //     0x63fddc: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0x63fde0: r0 = Null
    //     0x63fde0: mov             x0, NULL
    // 0x63fde4: StoreStaticField(0x684, r0)
    //     0x63fde4: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x63fde8: str             x0, [x1, #0xd08]
    // 0x63fdec: LeaveFrame
    //     0x63fdec: mov             SP, fp
    //     0x63fdf0: ldp             fp, lr, [SP], #0x10
    // 0x63fdf4: ret
    //     0x63fdf4: ret             
    // 0x63fdf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63fdf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63fdfc: b               #0x63fbcc
    // 0x63fe00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63fe00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63fe04: b               #0x63fc40
  }
  [closure] static void _debugPrintTask(dynamic) {
    // ** addr: 0x63fe94, size: 0x2c
    // 0x63fe94: EnterFrame
    //     0x63fe94: stp             fp, lr, [SP, #-0x10]!
    //     0x63fe98: mov             fp, SP
    // 0x63fe9c: CheckStackOverflow
    //     0x63fe9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63fea0: cmp             SP, x16
    //     0x63fea4: b.ls            #0x63feb8
    // 0x63fea8: r0 = _debugPrintTask()
    //     0x63fea8: bl              #0x63fbb0  ; [package:flutter/src/foundation/print.dart] ::_debugPrintTask
    // 0x63feac: LeaveFrame
    //     0x63feac: mov             SP, fp
    //     0x63feb0: ldp             fp, lr, [SP], #0x10
    // 0x63feb4: ret
    //     0x63feb4: ret             
    // 0x63feb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63feb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63febc: b               #0x63fea8
  }
  static Stopwatch _debugPrintStopwatch() {
    // ** addr: 0x640438, size: 0x5c
    // 0x640438: EnterFrame
    //     0x640438: stp             fp, lr, [SP, #-0x10]!
    //     0x64043c: mov             fp, SP
    // 0x640440: AllocStack(0x8)
    //     0x640440: sub             SP, SP, #8
    // 0x640444: CheckStackOverflow
    //     0x640444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x640448: cmp             SP, x16
    //     0x64044c: b.ls            #0x64048c
    // 0x640450: r0 = Stopwatch()
    //     0x640450: bl              #0x640494  ; AllocateStopwatchStub -> Stopwatch (size=0x14)
    // 0x640454: stur            x0, [fp, #-8]
    // 0x640458: StoreField: r0->field_7 = rZR
    //     0x640458: stur            xzr, [x0, #7]
    // 0x64045c: StoreField: r0->field_f = rZR
    //     0x64045c: stur            wzr, [x0, #0xf]
    // 0x640460: r0 = InitLateStaticField(0x37c) // [dart:core] Stopwatch::_frequency
    //     0x640460: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x640464: ldr             x0, [x0, #0x6f8]
    //     0x640468: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x64046c: cmp             w0, w16
    //     0x640470: b.ne            #0x64047c
    //     0x640474: ldr             x2, [PP, #0x6b0]  ; [pp+0x6b0] Field <Stopwatch._frequency@0150898>: static late final (offset: 0x37c)
    //     0x640478: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x64047c: ldur            x0, [fp, #-8]
    // 0x640480: LeaveFrame
    //     0x640480: mov             SP, fp
    //     0x640484: ldp             fp, lr, [SP], #0x10
    // 0x640488: ret
    //     0x640488: ret             
    // 0x64048c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x64048c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x640490: b               #0x640450
  }
  [closure] static Iterable<String> <anonymous closure>(dynamic, String) {
    // ** addr: 0x640e80, size: 0x44
    // 0x640e80: EnterFrame
    //     0x640e80: stp             fp, lr, [SP, #-0x10]!
    //     0x640e84: mov             fp, SP
    // 0x640e88: ldr             x0, [fp, #0x18]
    // 0x640e8c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x640e8c: ldur            w1, [x0, #0x17]
    // 0x640e90: DecompressPointer r1
    //     0x640e90: add             x1, x1, HEAP, lsl #32
    // 0x640e94: CheckStackOverflow
    //     0x640e94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x640e98: cmp             SP, x16
    //     0x640e9c: b.ls            #0x640ebc
    // 0x640ea0: LoadField: r2 = r1->field_f
    //     0x640ea0: ldur            w2, [x1, #0xf]
    // 0x640ea4: DecompressPointer r2
    //     0x640ea4: add             x2, x2, HEAP, lsl #32
    // 0x640ea8: ldr             x1, [fp, #0x10]
    // 0x640eac: r0 = debugWordWrap()
    //     0x640eac: bl              #0x640ec4  ; [package:flutter/src/foundation/print.dart] ::debugWordWrap
    // 0x640eb0: LeaveFrame
    //     0x640eb0: mov             SP, fp
    //     0x640eb4: ldp             fp, lr, [SP], #0x10
    // 0x640eb8: ret
    //     0x640eb8: ret             
    // 0x640ebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x640ebc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x640ec0: b               #0x640ea0
  }
  static _ debugWordWrap(/* No info */) {
    // ** addr: 0x640ec4, size: 0x624
    // 0x640ec4: EnterFrame
    //     0x640ec4: stp             fp, lr, [SP, #-0x10]!
    //     0x640ec8: mov             fp, SP
    // 0x640ecc: AllocStack(0x80)
    //     0x640ecc: sub             SP, SP, #0x80
    // 0x640ed0: SetupParameters(dynamic _ /* r1 => r0, fp-0x18 */)
    //     0x640ed0: mov             x0, x1
    //     0x640ed4: stur            x1, [fp, #-0x18]
    // 0x640ed8: CheckStackOverflow
    //     0x640ed8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x640edc: cmp             SP, x16
    //     0x640ee0: b.ls            #0x6414b4
    // 0x640ee4: LoadField: r1 = r0->field_7
    //     0x640ee4: ldur            w1, [x0, #7]
    // 0x640ee8: cmp             w2, NULL
    // 0x640eec: b.eq            #0x6414bc
    // 0x640ef0: r3 = LoadInt32Instr(r1)
    //     0x640ef0: sbfx            x3, x1, #1, #0x1f
    // 0x640ef4: stur            x3, [fp, #-0x10]
    // 0x640ef8: r4 = LoadInt32Instr(r2)
    //     0x640ef8: sbfx            x4, x2, #1, #0x1f
    //     0x640efc: tbz             w2, #0, #0x640f04
    //     0x640f00: ldur            x4, [x2, #7]
    // 0x640f04: stur            x4, [fp, #-8]
    // 0x640f08: cmp             x3, x4
    // 0x640f0c: b.lt            #0x640f44
    // 0x640f10: mov             x1, x0
    // 0x640f14: r0 = trimLeft()
    //     0x640f14: bl              #0x6418ac  ; [dart:core] _StringBase::trimLeft
    // 0x640f18: stp             xzr, x0, [SP]
    // 0x640f1c: r0 = []()
    //     0x640f1c: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0x640f20: r1 = LoadClassIdInstr(r0)
    //     0x640f20: ldur            x1, [x0, #-1]
    //     0x640f24: ubfx            x1, x1, #0xc, #0x14
    // 0x640f28: r16 = "#"
    //     0x640f28: ldr             x16, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0x640f2c: stp             x16, x0, [SP]
    // 0x640f30: mov             x0, x1
    // 0x640f34: mov             lr, x0
    // 0x640f38: ldr             lr, [x21, lr, lsl #3]
    // 0x640f3c: blr             lr
    // 0x640f40: tbnz            w0, #4, #0x640f5c
    // 0x640f44: ldur            x2, [fp, #-0x18]
    // 0x640f48: r1 = <String>
    //     0x640f48: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x640f4c: r0 = _GrowableList._literal1()
    //     0x640f4c: bl              #0x6138b0  ; [dart:core] _GrowableList::_GrowableList._literal1
    // 0x640f50: LeaveFrame
    //     0x640f50: mov             SP, fp
    //     0x640f54: ldp             fp, lr, [SP], #0x10
    // 0x640f58: ret
    //     0x640f58: ret             
    // 0x640f5c: r1 = <String>
    //     0x640f5c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x640f60: r2 = 0
    //     0x640f60: movz            x2, #0
    // 0x640f64: r0 = _GrowableList()
    //     0x640f64: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x640f68: stur            x0, [fp, #-0x20]
    // 0x640f6c: r0 = InitLateStaticField(0x68c) // [package:flutter/src/foundation/print.dart] ::_indentPattern
    //     0x640f6c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x640f70: ldr             x0, [x0, #0xd18]
    //     0x640f74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x640f78: cmp             w0, w16
    //     0x640f7c: b.ne            #0x640f88
    //     0x640f80: ldr             x2, [PP, #0x4d8]  ; [pp+0x4d8] Field <::._indentPattern@48110992>: static late final (offset: 0x68c)
    //     0x640f84: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x640f88: mov             x1, x0
    // 0x640f8c: ldur            x2, [fp, #-0x18]
    // 0x640f90: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x640f90: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x640f94: r0 = matchAsPrefix()
    //     0x640f94: bl              #0xebcc68  ; [dart:core] _RegExp::matchAsPrefix
    // 0x640f98: cmp             w0, NULL
    // 0x640f9c: b.eq            #0x6414c0
    // 0x640fa0: mov             x1, x0
    // 0x640fa4: r2 = 0
    //     0x640fa4: movz            x2, #0
    // 0x640fa8: r0 = group()
    //     0x640fa8: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0x640fac: cmp             w0, NULL
    // 0x640fb0: b.eq            #0x6414c4
    // 0x640fb4: LoadField: r1 = r0->field_7
    //     0x640fb4: ldur            w1, [x0, #7]
    // 0x640fb8: r2 = LoadInt32Instr(r1)
    //     0x640fb8: sbfx            x2, x1, #1, #0x1f
    // 0x640fbc: r1 = " "
    //     0x640fbc: ldr             x1, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x640fc0: r0 = *()
    //     0x640fc0: bl              #0xebcfc4  ; [dart:core] _OneByteString::*
    // 0x640fc4: r16 = ""
    //     0x640fc4: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0x640fc8: stp             x0, x16, [SP]
    // 0x640fcc: r0 = +()
    //     0x640fcc: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0x640fd0: stur            x0, [fp, #-0x58]
    // 0x640fd4: LoadField: r1 = r0->field_7
    //     0x640fd4: ldur            w1, [x0, #7]
    // 0x640fd8: r2 = LoadInt32Instr(r1)
    //     0x640fd8: sbfx            x2, x1, #1, #0x1f
    // 0x640fdc: stur            x2, [fp, #-0x50]
    // 0x640fe0: mov             x8, x2
    // 0x640fe4: r11 = 0
    //     0x640fe4: movz            x11, #0
    // 0x640fe8: r10 = 0
    //     0x640fe8: movz            x10, #0
    // 0x640fec: r9 = false
    //     0x640fec: add             x9, NULL, #0x30  ; false
    // 0x640ff0: r7 = Instance__WordWrapParseMode
    //     0x640ff0: ldr             x7, [PP, #0x4e8]  ; [pp+0x4e8] Obj!_WordWrapParseMode@e36f41
    // 0x640ff4: r6 = Sentinel
    //     0x640ff4: ldr             x6, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x640ff8: r5 = Null
    //     0x640ff8: mov             x5, NULL
    // 0x640ffc: ldur            x1, [fp, #-0x20]
    // 0x641000: ldur            x3, [fp, #-0x10]
    // 0x641004: ldur            x4, [fp, #-8]
    // 0x641008: stur            x11, [fp, #-0x30]
    // 0x64100c: stur            x10, [fp, #-0x38]
    // 0x641010: stur            x9, [fp, #-0x40]
    // 0x641014: stur            x5, [fp, #-0x48]
    // 0x641018: stur            x6, [fp, #-0x60]
    // 0x64101c: stur            x8, [fp, #-0x68]
    // 0x641020: CheckStackOverflow
    //     0x641020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x641024: cmp             SP, x16
    //     0x641028: b.ls            #0x6414c8
    // 0x64102c: LoadField: r12 = r7->field_7
    //     0x64102c: ldur            x12, [x7, #7]
    // 0x641030: cmp             x12, #1
    // 0x641034: b.gt            #0x641184
    // 0x641038: cmp             x12, #0
    // 0x64103c: b.gt            #0x6410f4
    // 0x641040: mov             x6, x8
    // 0x641044: stur            x6, [fp, #-0x28]
    // 0x641048: CheckStackOverflow
    //     0x641048: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64104c: cmp             SP, x16
    //     0x641050: b.ls            #0x6414d0
    // 0x641054: cmp             x6, x3
    // 0x641058: b.ge            #0x6410c8
    // 0x64105c: lsl             x7, x6, #1
    // 0x641060: ldur            x16, [fp, #-0x18]
    // 0x641064: stp             x7, x16, [SP]
    // 0x641068: r0 = []()
    //     0x641068: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0x64106c: r1 = LoadClassIdInstr(r0)
    //     0x64106c: ldur            x1, [x0, #-1]
    //     0x641070: ubfx            x1, x1, #0xc, #0x14
    // 0x641074: r16 = " "
    //     0x641074: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x641078: stp             x16, x0, [SP]
    // 0x64107c: mov             x0, x1
    // 0x641080: mov             lr, x0
    // 0x641084: ldr             lr, [x21, lr, lsl #3]
    // 0x641088: blr             lr
    // 0x64108c: tbnz            w0, #4, #0x6410c0
    // 0x641090: ldur            x0, [fp, #-0x28]
    // 0x641094: add             x6, x0, #1
    // 0x641098: ldur            x1, [fp, #-0x20]
    // 0x64109c: ldur            x0, [fp, #-0x58]
    // 0x6410a0: ldur            x11, [fp, #-0x30]
    // 0x6410a4: ldur            x10, [fp, #-0x38]
    // 0x6410a8: ldur            x9, [fp, #-0x40]
    // 0x6410ac: ldur            x5, [fp, #-0x48]
    // 0x6410b0: ldur            x3, [fp, #-0x10]
    // 0x6410b4: ldur            x4, [fp, #-8]
    // 0x6410b8: ldur            x2, [fp, #-0x50]
    // 0x6410bc: b               #0x641044
    // 0x6410c0: ldur            x0, [fp, #-0x28]
    // 0x6410c4: b               #0x6410cc
    // 0x6410c8: mov             x0, x6
    // 0x6410cc: lsl             x1, x0, #1
    // 0x6410d0: ldur            x11, [fp, #-0x30]
    // 0x6410d4: ldur            x10, [fp, #-0x38]
    // 0x6410d8: ldur            x9, [fp, #-0x40]
    // 0x6410dc: mov             x8, x0
    // 0x6410e0: mov             x6, x1
    // 0x6410e4: ldur            x5, [fp, #-0x48]
    // 0x6410e8: ldur            x4, [fp, #-0x50]
    // 0x6410ec: r7 = Instance__WordWrapParseMode
    //     0x6410ec: ldr             x7, [PP, #0x4f0]  ; [pp+0x4f0] Obj!_WordWrapParseMode@e36f21
    // 0x6410f0: b               #0x64148c
    // 0x6410f4: mov             x0, x8
    // 0x6410f8: ldur            x3, [fp, #-0x10]
    // 0x6410fc: stur            x0, [fp, #-0x28]
    // 0x641100: CheckStackOverflow
    //     0x641100: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x641104: cmp             SP, x16
    //     0x641108: b.ls            #0x6414d8
    // 0x64110c: cmp             x0, x3
    // 0x641110: b.ge            #0x641160
    // 0x641114: lsl             x1, x0, #1
    // 0x641118: ldur            x16, [fp, #-0x18]
    // 0x64111c: stp             x1, x16, [SP]
    // 0x641120: r0 = []()
    //     0x641120: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0x641124: r1 = LoadClassIdInstr(r0)
    //     0x641124: ldur            x1, [x0, #-1]
    //     0x641128: ubfx            x1, x1, #0xc, #0x14
    // 0x64112c: r16 = " "
    //     0x64112c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x641130: stp             x16, x0, [SP]
    // 0x641134: mov             x0, x1
    // 0x641138: mov             lr, x0
    // 0x64113c: ldr             lr, [x21, lr, lsl #3]
    // 0x641140: blr             lr
    // 0x641144: tbz             w0, #4, #0x64115c
    // 0x641148: ldur            x0, [fp, #-0x28]
    // 0x64114c: add             x1, x0, #1
    // 0x641150: mov             x0, x1
    // 0x641154: ldur            x6, [fp, #-0x60]
    // 0x641158: b               #0x6410f8
    // 0x64115c: ldur            x0, [fp, #-0x28]
    // 0x641160: ldur            x11, [fp, #-0x30]
    // 0x641164: ldur            x10, [fp, #-0x38]
    // 0x641168: ldur            x9, [fp, #-0x40]
    // 0x64116c: mov             x8, x0
    // 0x641170: ldur            x6, [fp, #-0x60]
    // 0x641174: ldur            x5, [fp, #-0x48]
    // 0x641178: ldur            x4, [fp, #-0x50]
    // 0x64117c: r7 = Instance__WordWrapParseMode
    //     0x64117c: ldr             x7, [PP, #0x4f8]  ; [pp+0x4f8] Obj!_WordWrapParseMode@e36f01
    // 0x641180: b               #0x64148c
    // 0x641184: mov             x0, x10
    // 0x641188: sub             x1, x8, x0
    // 0x64118c: cmp             x1, x4
    // 0x641190: b.le            #0x64119c
    // 0x641194: ldur            x5, [fp, #-0x10]
    // 0x641198: b               #0x6411a8
    // 0x64119c: ldur            x5, [fp, #-0x10]
    // 0x6411a0: cmp             x8, x5
    // 0x6411a4: b.ne            #0x641454
    // 0x6411a8: cmp             x1, x4
    // 0x6411ac: b.le            #0x6411bc
    // 0x6411b0: ldur            x0, [fp, #-0x48]
    // 0x6411b4: cmp             w0, NULL
    // 0x6411b8: b.ne            #0x6411c4
    // 0x6411bc: mov             x6, x8
    // 0x6411c0: b               #0x6411cc
    // 0x6411c4: r1 = LoadInt32Instr(r0)
    //     0x6411c4: sbfx            x1, x0, #1, #0x1f
    // 0x6411c8: mov             x6, x1
    // 0x6411cc: ldur            x1, [fp, #-0x40]
    // 0x6411d0: stur            x6, [fp, #-0x28]
    // 0x6411d4: tbnz            w1, #4, #0x6412a8
    // 0x6411d8: ldur            x7, [fp, #-0x20]
    // 0x6411dc: r0 = BoxInt64Instr(r6)
    //     0x6411dc: sbfiz           x0, x6, #1, #0x1f
    //     0x6411e0: cmp             x6, x0, asr #1
    //     0x6411e4: b.eq            #0x6411f0
    //     0x6411e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6411ec: stur            x6, [x0, #7]
    // 0x6411f0: ldur            x1, [fp, #-0x30]
    // 0x6411f4: mov             x2, x0
    // 0x6411f8: mov             x3, x5
    // 0x6411fc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x6411fc: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x641200: r0 = checkValidRange()
    //     0x641200: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x641204: ldur            x1, [fp, #-0x18]
    // 0x641208: ldur            x2, [fp, #-0x30]
    // 0x64120c: mov             x3, x0
    // 0x641210: r0 = _substringUnchecked()
    //     0x641210: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0x641214: ldur            x16, [fp, #-0x58]
    // 0x641218: stp             x0, x16, [SP]
    // 0x64121c: r0 = +()
    //     0x64121c: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0x641220: mov             x2, x0
    // 0x641224: ldur            x0, [fp, #-0x20]
    // 0x641228: stur            x2, [fp, #-0x48]
    // 0x64122c: LoadField: r1 = r0->field_b
    //     0x64122c: ldur            w1, [x0, #0xb]
    // 0x641230: LoadField: r3 = r0->field_f
    //     0x641230: ldur            w3, [x0, #0xf]
    // 0x641234: DecompressPointer r3
    //     0x641234: add             x3, x3, HEAP, lsl #32
    // 0x641238: LoadField: r4 = r3->field_b
    //     0x641238: ldur            w4, [x3, #0xb]
    // 0x64123c: r3 = LoadInt32Instr(r1)
    //     0x64123c: sbfx            x3, x1, #1, #0x1f
    // 0x641240: stur            x3, [fp, #-0x70]
    // 0x641244: r1 = LoadInt32Instr(r4)
    //     0x641244: sbfx            x1, x4, #1, #0x1f
    // 0x641248: cmp             x3, x1
    // 0x64124c: b.ne            #0x641258
    // 0x641250: mov             x1, x0
    // 0x641254: r0 = _growToNextCapacity()
    //     0x641254: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x641258: ldur            x4, [fp, #-0x20]
    // 0x64125c: ldur            x2, [fp, #-0x70]
    // 0x641260: add             x0, x2, #1
    // 0x641264: lsl             x1, x0, #1
    // 0x641268: StoreField: r4->field_b = r1
    //     0x641268: stur            w1, [x4, #0xb]
    // 0x64126c: LoadField: r1 = r4->field_f
    //     0x64126c: ldur            w1, [x4, #0xf]
    // 0x641270: DecompressPointer r1
    //     0x641270: add             x1, x1, HEAP, lsl #32
    // 0x641274: ldur            x0, [fp, #-0x48]
    // 0x641278: ArrayStore: r1[r2] = r0  ; List_4
    //     0x641278: add             x25, x1, x2, lsl #2
    //     0x64127c: add             x25, x25, #0xf
    //     0x641280: str             w0, [x25]
    //     0x641284: tbz             w0, #0, #0x6412a0
    //     0x641288: ldurb           w16, [x1, #-1]
    //     0x64128c: ldurb           w17, [x0, #-1]
    //     0x641290: and             x16, x17, x16, lsr #2
    //     0x641294: tst             x16, HEAP, lsr #32
    //     0x641298: b.eq            #0x6412a0
    //     0x64129c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6412a0: mov             x2, x4
    // 0x6412a4: b               #0x641368
    // 0x6412a8: ldur            x4, [fp, #-0x20]
    // 0x6412ac: mov             x5, x6
    // 0x6412b0: r0 = BoxInt64Instr(r5)
    //     0x6412b0: sbfiz           x0, x5, #1, #0x1f
    //     0x6412b4: cmp             x5, x0, asr #1
    //     0x6412b8: b.eq            #0x6412c4
    //     0x6412bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6412c0: stur            x5, [x0, #7]
    // 0x6412c4: ldur            x1, [fp, #-0x30]
    // 0x6412c8: mov             x2, x0
    // 0x6412cc: ldur            x3, [fp, #-0x10]
    // 0x6412d0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x6412d0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x6412d4: r0 = checkValidRange()
    //     0x6412d4: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x6412d8: ldur            x1, [fp, #-0x18]
    // 0x6412dc: ldur            x2, [fp, #-0x30]
    // 0x6412e0: mov             x3, x0
    // 0x6412e4: r0 = _substringUnchecked()
    //     0x6412e4: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0x6412e8: mov             x2, x0
    // 0x6412ec: ldur            x0, [fp, #-0x20]
    // 0x6412f0: stur            x2, [fp, #-0x48]
    // 0x6412f4: LoadField: r1 = r0->field_b
    //     0x6412f4: ldur            w1, [x0, #0xb]
    // 0x6412f8: LoadField: r3 = r0->field_f
    //     0x6412f8: ldur            w3, [x0, #0xf]
    // 0x6412fc: DecompressPointer r3
    //     0x6412fc: add             x3, x3, HEAP, lsl #32
    // 0x641300: LoadField: r4 = r3->field_b
    //     0x641300: ldur            w4, [x3, #0xb]
    // 0x641304: r3 = LoadInt32Instr(r1)
    //     0x641304: sbfx            x3, x1, #1, #0x1f
    // 0x641308: stur            x3, [fp, #-0x70]
    // 0x64130c: r1 = LoadInt32Instr(r4)
    //     0x64130c: sbfx            x1, x4, #1, #0x1f
    // 0x641310: cmp             x3, x1
    // 0x641314: b.ne            #0x641320
    // 0x641318: mov             x1, x0
    // 0x64131c: r0 = _growToNextCapacity()
    //     0x64131c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x641320: ldur            x2, [fp, #-0x20]
    // 0x641324: ldur            x3, [fp, #-0x70]
    // 0x641328: add             x0, x3, #1
    // 0x64132c: lsl             x1, x0, #1
    // 0x641330: StoreField: r2->field_b = r1
    //     0x641330: stur            w1, [x2, #0xb]
    // 0x641334: LoadField: r1 = r2->field_f
    //     0x641334: ldur            w1, [x2, #0xf]
    // 0x641338: DecompressPointer r1
    //     0x641338: add             x1, x1, HEAP, lsl #32
    // 0x64133c: ldur            x0, [fp, #-0x48]
    // 0x641340: ArrayStore: r1[r3] = r0  ; List_4
    //     0x641340: add             x25, x1, x3, lsl #2
    //     0x641344: add             x25, x25, #0xf
    //     0x641348: str             w0, [x25]
    //     0x64134c: tbz             w0, #0, #0x641368
    //     0x641350: ldurb           w16, [x1, #-1]
    //     0x641354: ldurb           w17, [x0, #-1]
    //     0x641358: and             x16, x17, x16, lsr #2
    //     0x64135c: tst             x16, HEAP, lsr #32
    //     0x641360: b.eq            #0x641368
    //     0x641364: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x641368: ldur            x0, [fp, #-0x28]
    // 0x64136c: ldur            x1, [fp, #-0x10]
    // 0x641370: cmp             x0, x1
    // 0x641374: b.ge            #0x641444
    // 0x641378: ldur            x3, [fp, #-0x68]
    // 0x64137c: cmp             x0, x3
    // 0x641380: b.ne            #0x641404
    // 0x641384: mov             x0, x3
    // 0x641388: stur            x0, [fp, #-0x28]
    // 0x64138c: CheckStackOverflow
    //     0x64138c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x641390: cmp             SP, x16
    //     0x641394: b.ls            #0x6414e0
    // 0x641398: cmp             x0, x1
    // 0x64139c: b.ge            #0x6413f0
    // 0x6413a0: lsl             x3, x0, #1
    // 0x6413a4: ldur            x16, [fp, #-0x18]
    // 0x6413a8: stp             x3, x16, [SP]
    // 0x6413ac: r0 = []()
    //     0x6413ac: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0x6413b0: r1 = LoadClassIdInstr(r0)
    //     0x6413b0: ldur            x1, [x0, #-1]
    //     0x6413b4: ubfx            x1, x1, #0xc, #0x14
    // 0x6413b8: r16 = " "
    //     0x6413b8: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x6413bc: stp             x16, x0, [SP]
    // 0x6413c0: mov             x0, x1
    // 0x6413c4: mov             lr, x0
    // 0x6413c8: ldr             lr, [x21, lr, lsl #3]
    // 0x6413cc: blr             lr
    // 0x6413d0: tbnz            w0, #4, #0x6413ec
    // 0x6413d4: ldur            x0, [fp, #-0x28]
    // 0x6413d8: add             x1, x0, #1
    // 0x6413dc: mov             x0, x1
    // 0x6413e0: ldur            x2, [fp, #-0x20]
    // 0x6413e4: ldur            x1, [fp, #-0x10]
    // 0x6413e8: b               #0x641388
    // 0x6413ec: ldur            x0, [fp, #-0x28]
    // 0x6413f0: mov             x3, x0
    // 0x6413f4: mov             x1, x0
    // 0x6413f8: ldur            x2, [fp, #-0x60]
    // 0x6413fc: r0 = Instance__WordWrapParseMode
    //     0x6413fc: ldr             x0, [PP, #0x4f0]  ; [pp+0x4f0] Obj!_WordWrapParseMode@e36f21
    // 0x641400: b               #0x641424
    // 0x641404: ldur            x2, [fp, #-0x60]
    // 0x641408: r16 = Sentinel
    //     0x641408: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x64140c: cmp             w2, w16
    // 0x641410: b.eq            #0x641498
    // 0x641414: r0 = LoadInt32Instr(r2)
    //     0x641414: sbfx            x0, x2, #1, #0x1f
    // 0x641418: mov             x1, x3
    // 0x64141c: mov             x3, x0
    // 0x641420: r0 = Instance__WordWrapParseMode
    //     0x641420: ldr             x0, [PP, #0x4f8]  ; [pp+0x4f8] Obj!_WordWrapParseMode@e36f01
    // 0x641424: ldur            x4, [fp, #-0x50]
    // 0x641428: sub             x10, x3, x4
    // 0x64142c: mov             x11, x3
    // 0x641430: mov             x3, x1
    // 0x641434: mov             x1, x0
    // 0x641438: r9 = true
    //     0x641438: add             x9, NULL, #0x20  ; true
    // 0x64143c: r0 = Null
    //     0x64143c: mov             x0, NULL
    // 0x641440: b               #0x64147c
    // 0x641444: ldur            x0, [fp, #-0x20]
    // 0x641448: LeaveFrame
    //     0x641448: mov             SP, fp
    //     0x64144c: ldp             fp, lr, [SP], #0x10
    // 0x641450: ret
    //     0x641450: ret             
    // 0x641454: ldur            x1, [fp, #-0x40]
    // 0x641458: mov             x3, x8
    // 0x64145c: ldur            x2, [fp, #-0x60]
    // 0x641460: ldur            x4, [fp, #-0x50]
    // 0x641464: lsl             x5, x3, #1
    // 0x641468: ldur            x11, [fp, #-0x30]
    // 0x64146c: mov             x10, x0
    // 0x641470: mov             x9, x1
    // 0x641474: mov             x0, x5
    // 0x641478: r1 = Instance__WordWrapParseMode
    //     0x641478: ldr             x1, [PP, #0x4e8]  ; [pp+0x4e8] Obj!_WordWrapParseMode@e36f41
    // 0x64147c: mov             x8, x3
    // 0x641480: mov             x7, x1
    // 0x641484: mov             x6, x2
    // 0x641488: mov             x5, x0
    // 0x64148c: ldur            x0, [fp, #-0x58]
    // 0x641490: mov             x2, x4
    // 0x641494: b               #0x640ffc
    // 0x641498: r0 = LateError()
    //     0x641498: bl              #0x6418a0  ; AllocateLateErrorStub -> LateError (size=0x10)
    // 0x64149c: mov             x1, x0
    // 0x6414a0: r0 = "Local \'lastWordStart\' has not been initialized."
    //     0x6414a0: ldr             x0, [PP, #0x508]  ; [pp+0x508] "Local \'lastWordStart\' has not been initialized."
    // 0x6414a4: StoreField: r1->field_b = r0
    //     0x6414a4: stur            w0, [x1, #0xb]
    // 0x6414a8: mov             x0, x1
    // 0x6414ac: r0 = Throw()
    //     0x6414ac: bl              #0xec04b8  ; ThrowStub
    // 0x6414b0: brk             #0
    // 0x6414b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6414b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6414b8: b               #0x640ee4
    // 0x6414bc: r0 = NullErrorSharedWithoutFPURegs()
    //     0x6414bc: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x6414c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6414c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6414c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6414c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6414c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6414c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6414cc: b               #0x64102c
    // 0x6414d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6414d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6414d4: b               #0x641054
    // 0x6414d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6414d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6414dc: b               #0x64110c
    // 0x6414e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6414e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6414e4: b               #0x641398
  }
  static RegExp _indentPattern() {
    // ** addr: 0x641a54, size: 0x38
    // 0x641a54: EnterFrame
    //     0x641a54: stp             fp, lr, [SP, #-0x10]!
    //     0x641a58: mov             fp, SP
    // 0x641a5c: CheckStackOverflow
    //     0x641a5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x641a60: cmp             SP, x16
    //     0x641a64: b.ls            #0x641a84
    // 0x641a68: r1 = Null
    //     0x641a68: mov             x1, NULL
    // 0x641a6c: r2 = "^ *(\?:[-+*] |[0-9]+[.):] )\?"
    //     0x641a6c: ldr             x2, [PP, #0x5e8]  ; [pp+0x5e8] "^ *(\?:[-+*] |[0-9]+[.):] )\?"
    // 0x641a70: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x641a70: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x641a74: r0 = RegExp()
    //     0x641a74: bl              #0x60939c  ; [dart:core] RegExp::RegExp
    // 0x641a78: LeaveFrame
    //     0x641a78: mov             SP, fp
    //     0x641a7c: ldp             fp, lr, [SP], #0x10
    // 0x641a80: ret
    //     0x641a80: ret             
    // 0x641a84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x641a84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x641a88: b               #0x641a68
  }
  static Queue<String> _debugPrintBuffer() {
    // ** addr: 0x641a8c, size: 0x48
    // 0x641a8c: EnterFrame
    //     0x641a8c: stp             fp, lr, [SP, #-0x10]!
    //     0x641a90: mov             fp, SP
    // 0x641a94: AllocStack(0x8)
    //     0x641a94: sub             SP, SP, #8
    // 0x641a98: CheckStackOverflow
    //     0x641a98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x641a9c: cmp             SP, x16
    //     0x641aa0: b.ls            #0x641acc
    // 0x641aa4: r1 = <String>
    //     0x641aa4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x641aa8: r0 = ListQueue()
    //     0x641aa8: bl              #0x61b3b8  ; AllocateListQueueStub -> ListQueue<X0> (size=0x28)
    // 0x641aac: mov             x1, x0
    // 0x641ab0: stur            x0, [fp, #-8]
    // 0x641ab4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x641ab4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x641ab8: r0 = ListQueue()
    //     0x641ab8: bl              #0x61b248  ; [dart:collection] ListQueue::ListQueue
    // 0x641abc: ldur            x0, [fp, #-8]
    // 0x641ac0: LeaveFrame
    //     0x641ac0: mov             SP, fp
    //     0x641ac4: ldp             fp, lr, [SP], #0x10
    // 0x641ac8: ret
    //     0x641ac8: ret             
    // 0x641acc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x641acc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x641ad0: b               #0x641aa4
  }
  static (dynamic, String?, {int? wrapWidth}) => void debugPrint() {
    // ** addr: 0x644768, size: 0x8
    // 0x644768: r0 = Closure: (String?, {int? wrapWidth}) => void from Function 'debugPrintThrottled': static.
    //     0x644768: ldr             x0, [PP, #0x1130]  ; [pp+0x1130] Closure: (String?, {int? wrapWidth}) => void from Function 'debugPrintThrottled': static. (0x7e54fb03f99c)
    // 0x64476c: ret
    //     0x64476c: ret             
  }
}

// class id: 7090, size: 0x14, field offset: 0x14
enum _WordWrapParseMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc48554, size: 0x64
    // 0xc48554: EnterFrame
    //     0xc48554: stp             fp, lr, [SP, #-0x10]!
    //     0xc48558: mov             fp, SP
    // 0xc4855c: AllocStack(0x10)
    //     0xc4855c: sub             SP, SP, #0x10
    // 0xc48560: SetupParameters(_WordWrapParseMode this /* r1 => r0, fp-0x8 */)
    //     0xc48560: mov             x0, x1
    //     0xc48564: stur            x1, [fp, #-8]
    // 0xc48568: CheckStackOverflow
    //     0xc48568: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4856c: cmp             SP, x16
    //     0xc48570: b.ls            #0xc485b0
    // 0xc48574: r1 = Null
    //     0xc48574: mov             x1, NULL
    // 0xc48578: r2 = 4
    //     0xc48578: movz            x2, #0x4
    // 0xc4857c: r0 = AllocateArray()
    //     0xc4857c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc48580: r16 = "_WordWrapParseMode."
    //     0xc48580: add             x16, PP, #0x22, lsl #12  ; [pp+0x226e8] "_WordWrapParseMode."
    //     0xc48584: ldr             x16, [x16, #0x6e8]
    // 0xc48588: StoreField: r0->field_f = r16
    //     0xc48588: stur            w16, [x0, #0xf]
    // 0xc4858c: ldur            x1, [fp, #-8]
    // 0xc48590: LoadField: r2 = r1->field_f
    //     0xc48590: ldur            w2, [x1, #0xf]
    // 0xc48594: DecompressPointer r2
    //     0xc48594: add             x2, x2, HEAP, lsl #32
    // 0xc48598: StoreField: r0->field_13 = r2
    //     0xc48598: stur            w2, [x0, #0x13]
    // 0xc4859c: str             x0, [SP]
    // 0xc485a0: r0 = _interpolate()
    //     0xc485a0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc485a4: LeaveFrame
    //     0xc485a4: mov             SP, fp
    //     0xc485a8: ldp             fp, lr, [SP], #0x10
    // 0xc485ac: ret
    //     0xc485ac: ret             
    // 0xc485b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc485b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc485b4: b               #0xc48574
  }
}
