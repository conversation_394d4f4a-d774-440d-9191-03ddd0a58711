// lib: , url: package:flutter/src/foundation/stack_frame.dart

// class id: 1048821, size: 0x8
class :: {
}

// class id: 3556, size: 0x3c, field offset: 0x8
//   const constructor, 
class StackFrame extends Object {

  _OneByteString field_8;
  _Mint field_c;
  _OneByteString field_14;
  _OneByteString field_18;
  _OneByteString field_1c;
  _Mint field_20;
  _Mint field_28;
  _OneByteString field_30;
  _OneByteString field_34;
  bool field_38;
  static late final RegExp _webNonDebugFramePattern; // offset: 0x694

  static _ fromStackString(/* No info */) {
    // ** addr: 0x643988, size: 0xa8
    // 0x643988: EnterFrame
    //     0x643988: stp             fp, lr, [SP, #-0x10]!
    //     0x64398c: mov             fp, SP
    // 0x643990: AllocStack(0x20)
    //     0x643990: sub             SP, SP, #0x20
    // 0x643994: CheckStackOverflow
    //     0x643994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x643998: cmp             SP, x16
    //     0x64399c: b.ls            #0x643a28
    // 0x6439a0: r0 = trim()
    //     0x6439a0: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0x6439a4: r1 = LoadClassIdInstr(r0)
    //     0x6439a4: ldur            x1, [x0, #-1]
    //     0x6439a8: ubfx            x1, x1, #0xc, #0x14
    // 0x6439ac: mov             x16, x0
    // 0x6439b0: mov             x0, x1
    // 0x6439b4: mov             x1, x16
    // 0x6439b8: r2 = "\n"
    //     0x6439b8: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0x6439bc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x6439bc: sub             lr, x0, #1, lsl #12
    //     0x6439c0: ldr             lr, [x21, lr, lsl #3]
    //     0x6439c4: blr             lr
    // 0x6439c8: r1 = Function '<anonymous closure>': static.
    //     0x6439c8: ldr             x1, [PP, #0xac8]  ; [pp+0xac8] AnonymousClosure: static (0x6446b8), in [package:flutter/src/foundation/stack_frame.dart] StackFrame::fromStackString (0x643988)
    // 0x6439cc: r2 = Null
    //     0x6439cc: mov             x2, NULL
    // 0x6439d0: stur            x0, [fp, #-8]
    // 0x6439d4: r0 = AllocateClosure()
    //     0x6439d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x6439d8: ldur            x1, [fp, #-8]
    // 0x6439dc: mov             x2, x0
    // 0x6439e0: r0 = where()
    //     0x6439e0: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x6439e4: r16 = <StackFrame?>
    //     0x6439e4: ldr             x16, [PP, #0xad0]  ; [pp+0xad0] TypeArguments: <StackFrame?>
    // 0x6439e8: stp             x0, x16, [SP, #8]
    // 0x6439ec: r16 = Closure: (String) => StackFrame? from Function 'fromStackTraceLine': static.
    //     0x6439ec: ldr             x16, [PP, #0xad8]  ; [pp+0xad8] Closure: (String) => StackFrame? from Function 'fromStackTraceLine': static. (0x7e54fb043a30)
    // 0x6439f0: str             x16, [SP]
    // 0x6439f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6439f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6439f8: r0 = map()
    //     0x6439f8: bl              #0x7abfa0  ; [dart:_internal] WhereIterable::map
    // 0x6439fc: r16 = <StackFrame>
    //     0x6439fc: ldr             x16, [PP, #0xae0]  ; [pp+0xae0] TypeArguments: <StackFrame>
    // 0x643a00: stp             x0, x16, [SP]
    // 0x643a04: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x643a04: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x643a08: r0 = whereType()
    //     0x643a08: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0x643a0c: LoadField: r1 = r0->field_7
    //     0x643a0c: ldur            w1, [x0, #7]
    // 0x643a10: DecompressPointer r1
    //     0x643a10: add             x1, x1, HEAP, lsl #32
    // 0x643a14: mov             x2, x0
    // 0x643a18: r0 = _GrowableList.of()
    //     0x643a18: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x643a1c: LeaveFrame
    //     0x643a1c: mov             SP, fp
    //     0x643a20: ldp             fp, lr, [SP], #0x10
    // 0x643a24: ret
    //     0x643a24: ret             
    // 0x643a28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x643a28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x643a2c: b               #0x6439a0
  }
  [closure] static StackFrame? fromStackTraceLine(dynamic, String) {
    // ** addr: 0x643a30, size: 0x30
    // 0x643a30: EnterFrame
    //     0x643a30: stp             fp, lr, [SP, #-0x10]!
    //     0x643a34: mov             fp, SP
    // 0x643a38: CheckStackOverflow
    //     0x643a38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x643a3c: cmp             SP, x16
    //     0x643a40: b.ls            #0x643a58
    // 0x643a44: ldr             x1, [fp, #0x10]
    // 0x643a48: r0 = fromStackTraceLine()
    //     0x643a48: bl              #0x643a60  ; [package:flutter/src/foundation/stack_frame.dart] StackFrame::fromStackTraceLine
    // 0x643a4c: LeaveFrame
    //     0x643a4c: mov             SP, fp
    //     0x643a50: ldp             fp, lr, [SP], #0x10
    // 0x643a54: ret
    //     0x643a54: ret             
    // 0x643a58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x643a58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x643a5c: b               #0x643a44
  }
  static _ fromStackTraceLine(/* No info */) {
    // ** addr: 0x643a60, size: 0x654
    // 0x643a60: EnterFrame
    //     0x643a60: stp             fp, lr, [SP, #-0x10]!
    //     0x643a64: mov             fp, SP
    // 0x643a68: AllocStack(0x98)
    //     0x643a68: sub             SP, SP, #0x98
    // 0x643a6c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x643a6c: stur            x1, [fp, #-8]
    // 0x643a70: CheckStackOverflow
    //     0x643a70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x643a74: cmp             SP, x16
    //     0x643a78: b.ls            #0x644078
    // 0x643a7c: r0 = LoadClassIdInstr(r1)
    //     0x643a7c: ldur            x0, [x1, #-1]
    //     0x643a80: ubfx            x0, x0, #0xc, #0x14
    // 0x643a84: r16 = "<asynchronous suspension>"
    //     0x643a84: ldr             x16, [PP, #0xaf0]  ; [pp+0xaf0] "<asynchronous suspension>"
    // 0x643a88: stp             x16, x1, [SP]
    // 0x643a8c: mov             lr, x0
    // 0x643a90: ldr             lr, [x21, lr, lsl #3]
    // 0x643a94: blr             lr
    // 0x643a98: tbnz            w0, #4, #0x643aac
    // 0x643a9c: r0 = Instance_StackFrame
    //     0x643a9c: ldr             x0, [PP, #0xaf8]  ; [pp+0xaf8] Obj!StackFrame@e14af1
    // 0x643aa0: LeaveFrame
    //     0x643aa0: mov             SP, fp
    //     0x643aa4: ldp             fp, lr, [SP], #0x10
    // 0x643aa8: ret
    //     0x643aa8: ret             
    // 0x643aac: ldur            x1, [fp, #-8]
    // 0x643ab0: r0 = LoadClassIdInstr(r1)
    //     0x643ab0: ldur            x0, [x1, #-1]
    //     0x643ab4: ubfx            x0, x0, #0xc, #0x14
    // 0x643ab8: r16 = "..."
    //     0x643ab8: ldr             x16, [PP, #0xb00]  ; [pp+0xb00] "..."
    // 0x643abc: stp             x16, x1, [SP]
    // 0x643ac0: mov             lr, x0
    // 0x643ac4: ldr             lr, [x21, lr, lsl #3]
    // 0x643ac8: blr             lr
    // 0x643acc: tbnz            w0, #4, #0x643ae0
    // 0x643ad0: r0 = Instance_StackFrame
    //     0x643ad0: ldr             x0, [PP, #0xb08]  ; [pp+0xb08] Obj!StackFrame@e14ab1
    // 0x643ad4: LeaveFrame
    //     0x643ad4: mov             SP, fp
    //     0x643ad8: ldp             fp, lr, [SP], #0x10
    // 0x643adc: ret
    //     0x643adc: ret             
    // 0x643ae0: ldur            x1, [fp, #-8]
    // 0x643ae4: r2 = "#"
    //     0x643ae4: ldr             x2, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0x643ae8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x643ae8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x643aec: r0 = startsWith()
    //     0x643aec: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0x643af0: tbz             w0, #4, #0x643b08
    // 0x643af4: ldur            x1, [fp, #-8]
    // 0x643af8: r0 = _tryParseWebNonDebugFrame()
    //     0x643af8: bl              #0x6444fc  ; [package:flutter/src/foundation/stack_frame.dart] StackFrame::_tryParseWebNonDebugFrame
    // 0x643afc: LeaveFrame
    //     0x643afc: mov             SP, fp
    //     0x643b00: ldp             fp, lr, [SP], #0x10
    // 0x643b04: ret
    //     0x643b04: ret             
    // 0x643b08: r16 = "^#(\\d+) +(.+) \\((.+\?):\?(\\d+){0,1}:\?(\\d+){0,1}\\)$"
    //     0x643b08: ldr             x16, [PP, #0xb10]  ; [pp+0xb10] "^#(\\d+) +(.+) \\((.+\?):\?(\\d+){0,1}:\?(\\d+){0,1}\\)$"
    // 0x643b0c: stp             x16, NULL, [SP, #0x20]
    // 0x643b10: r16 = false
    //     0x643b10: add             x16, NULL, #0x30  ; false
    // 0x643b14: r30 = true
    //     0x643b14: add             lr, NULL, #0x20  ; true
    // 0x643b18: stp             lr, x16, [SP, #0x10]
    // 0x643b1c: r16 = false
    //     0x643b1c: add             x16, NULL, #0x30  ; false
    // 0x643b20: r30 = false
    //     0x643b20: add             lr, NULL, #0x30  ; false
    // 0x643b24: stp             lr, x16, [SP]
    // 0x643b28: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x643b28: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x643b2c: r0 = _RegExp()
    //     0x643b2c: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x643b30: mov             x1, x0
    // 0x643b34: ldur            x2, [fp, #-8]
    // 0x643b38: r0 = firstMatch()
    //     0x643b38: bl              #0x644328  ; [dart:core] _RegExp::firstMatch
    // 0x643b3c: stur            x0, [fp, #-0x10]
    // 0x643b40: cmp             w0, NULL
    // 0x643b44: b.eq            #0x644080
    // 0x643b48: mov             x1, x0
    // 0x643b4c: r2 = 2
    //     0x643b4c: movz            x2, #0x2
    // 0x643b50: r0 = group()
    //     0x643b50: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0x643b54: cmp             w0, NULL
    // 0x643b58: b.eq            #0x644084
    // 0x643b5c: mov             x1, x0
    // 0x643b60: r2 = ".<anonymous closure>"
    //     0x643b60: ldr             x2, [PP, #0xb18]  ; [pp+0xb18] ".<anonymous closure>"
    // 0x643b64: r3 = ""
    //     0x643b64: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0x643b68: r0 = replaceAll()
    //     0x643b68: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0x643b6c: mov             x1, x0
    // 0x643b70: r2 = "new"
    //     0x643b70: ldr             x2, [PP, #0xb20]  ; [pp+0xb20] "new"
    // 0x643b74: stur            x0, [fp, #-0x18]
    // 0x643b78: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x643b78: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x643b7c: r0 = startsWith()
    //     0x643b7c: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0x643b80: tbnz            w0, #4, #0x643cb0
    // 0x643b84: ldur            x3, [fp, #-0x18]
    // 0x643b88: r0 = LoadClassIdInstr(r3)
    //     0x643b88: ldur            x0, [x3, #-1]
    //     0x643b8c: ubfx            x0, x0, #0xc, #0x14
    // 0x643b90: mov             x1, x3
    // 0x643b94: r2 = " "
    //     0x643b94: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x643b98: r0 = GDT[cid_x0 + -0x1000]()
    //     0x643b98: sub             lr, x0, #1, lsl #12
    //     0x643b9c: ldr             lr, [x21, lr, lsl #3]
    //     0x643ba0: blr             lr
    // 0x643ba4: LoadField: r1 = r0->field_b
    //     0x643ba4: ldur            w1, [x0, #0xb]
    // 0x643ba8: r0 = LoadInt32Instr(r1)
    //     0x643ba8: sbfx            x0, x1, #1, #0x1f
    // 0x643bac: cmp             x0, #1
    // 0x643bb0: b.le            #0x643c08
    // 0x643bb4: ldur            x3, [fp, #-0x18]
    // 0x643bb8: r0 = LoadClassIdInstr(r3)
    //     0x643bb8: ldur            x0, [x3, #-1]
    //     0x643bbc: ubfx            x0, x0, #0xc, #0x14
    // 0x643bc0: mov             x1, x3
    // 0x643bc4: r2 = " "
    //     0x643bc4: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x643bc8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x643bc8: sub             lr, x0, #1, lsl #12
    //     0x643bcc: ldr             lr, [x21, lr, lsl #3]
    //     0x643bd0: blr             lr
    // 0x643bd4: mov             x2, x0
    // 0x643bd8: LoadField: r0 = r2->field_b
    //     0x643bd8: ldur            w0, [x2, #0xb]
    // 0x643bdc: r1 = LoadInt32Instr(r0)
    //     0x643bdc: sbfx            x1, x0, #1, #0x1f
    // 0x643be0: mov             x0, x1
    // 0x643be4: r1 = 1
    //     0x643be4: movz            x1, #0x1
    // 0x643be8: cmp             x1, x0
    // 0x643bec: b.hs            #0x644088
    // 0x643bf0: LoadField: r0 = r2->field_f
    //     0x643bf0: ldur            w0, [x2, #0xf]
    // 0x643bf4: DecompressPointer r0
    //     0x643bf4: add             x0, x0, HEAP, lsl #32
    // 0x643bf8: LoadField: r1 = r0->field_13
    //     0x643bf8: ldur            w1, [x0, #0x13]
    // 0x643bfc: DecompressPointer r1
    //     0x643bfc: add             x1, x1, HEAP, lsl #32
    // 0x643c00: mov             x3, x1
    // 0x643c04: b               #0x643c0c
    // 0x643c08: r3 = "<unknown>"
    //     0x643c08: ldr             x3, [PP, #0xb28]  ; [pp+0xb28] "<unknown>"
    // 0x643c0c: stur            x3, [fp, #-0x20]
    // 0x643c10: r0 = LoadClassIdInstr(r3)
    //     0x643c10: ldur            x0, [x3, #-1]
    //     0x643c14: ubfx            x0, x0, #0xc, #0x14
    // 0x643c18: mov             x1, x3
    // 0x643c1c: r2 = "."
    //     0x643c1c: ldr             x2, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x643c20: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x643c20: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x643c24: r0 = GDT[cid_x0 + -0xffc]()
    //     0x643c24: sub             lr, x0, #0xffc
    //     0x643c28: ldr             lr, [x21, lr, lsl #3]
    //     0x643c2c: blr             lr
    // 0x643c30: tbnz            w0, #4, #0x643c9c
    // 0x643c34: ldur            x1, [fp, #-0x20]
    // 0x643c38: r0 = LoadClassIdInstr(r1)
    //     0x643c38: ldur            x0, [x1, #-1]
    //     0x643c3c: ubfx            x0, x0, #0xc, #0x14
    // 0x643c40: r2 = "."
    //     0x643c40: ldr             x2, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x643c44: r0 = GDT[cid_x0 + -0x1000]()
    //     0x643c44: sub             lr, x0, #1, lsl #12
    //     0x643c48: ldr             lr, [x21, lr, lsl #3]
    //     0x643c4c: blr             lr
    // 0x643c50: mov             x2, x0
    // 0x643c54: LoadField: r0 = r2->field_b
    //     0x643c54: ldur            w0, [x2, #0xb]
    // 0x643c58: r3 = LoadInt32Instr(r0)
    //     0x643c58: sbfx            x3, x0, #1, #0x1f
    // 0x643c5c: mov             x0, x3
    // 0x643c60: r1 = 0
    //     0x643c60: movz            x1, #0
    // 0x643c64: cmp             x1, x0
    // 0x643c68: b.hs            #0x64408c
    // 0x643c6c: LoadField: r4 = r2->field_f
    //     0x643c6c: ldur            w4, [x2, #0xf]
    // 0x643c70: DecompressPointer r4
    //     0x643c70: add             x4, x4, HEAP, lsl #32
    // 0x643c74: LoadField: r2 = r4->field_f
    //     0x643c74: ldur            w2, [x4, #0xf]
    // 0x643c78: DecompressPointer r2
    //     0x643c78: add             x2, x2, HEAP, lsl #32
    // 0x643c7c: mov             x0, x3
    // 0x643c80: r1 = 1
    //     0x643c80: movz            x1, #0x1
    // 0x643c84: cmp             x1, x0
    // 0x643c88: b.hs            #0x644090
    // 0x643c8c: LoadField: r0 = r4->field_13
    //     0x643c8c: ldur            w0, [x4, #0x13]
    // 0x643c90: DecompressPointer r0
    //     0x643c90: add             x0, x0, HEAP, lsl #32
    // 0x643c94: mov             x1, x2
    // 0x643c98: b               #0x643ca4
    // 0x643c9c: ldur            x1, [fp, #-0x20]
    // 0x643ca0: r0 = ""
    //     0x643ca0: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x643ca4: mov             x3, x1
    // 0x643ca8: r4 = true
    //     0x643ca8: add             x4, NULL, #0x20  ; true
    // 0x643cac: b               #0x643d54
    // 0x643cb0: ldur            x3, [fp, #-0x18]
    // 0x643cb4: r0 = LoadClassIdInstr(r3)
    //     0x643cb4: ldur            x0, [x3, #-1]
    //     0x643cb8: ubfx            x0, x0, #0xc, #0x14
    // 0x643cbc: mov             x1, x3
    // 0x643cc0: r2 = "."
    //     0x643cc0: ldr             x2, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x643cc4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x643cc4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x643cc8: r0 = GDT[cid_x0 + -0xffc]()
    //     0x643cc8: sub             lr, x0, #0xffc
    //     0x643ccc: ldr             lr, [x21, lr, lsl #3]
    //     0x643cd0: blr             lr
    // 0x643cd4: tbnz            w0, #4, #0x643d40
    // 0x643cd8: ldur            x1, [fp, #-0x18]
    // 0x643cdc: r0 = LoadClassIdInstr(r1)
    //     0x643cdc: ldur            x0, [x1, #-1]
    //     0x643ce0: ubfx            x0, x0, #0xc, #0x14
    // 0x643ce4: r2 = "."
    //     0x643ce4: ldr             x2, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x643ce8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x643ce8: sub             lr, x0, #1, lsl #12
    //     0x643cec: ldr             lr, [x21, lr, lsl #3]
    //     0x643cf0: blr             lr
    // 0x643cf4: mov             x2, x0
    // 0x643cf8: LoadField: r0 = r2->field_b
    //     0x643cf8: ldur            w0, [x2, #0xb]
    // 0x643cfc: r3 = LoadInt32Instr(r0)
    //     0x643cfc: sbfx            x3, x0, #1, #0x1f
    // 0x643d00: mov             x0, x3
    // 0x643d04: r1 = 0
    //     0x643d04: movz            x1, #0
    // 0x643d08: cmp             x1, x0
    // 0x643d0c: b.hs            #0x644094
    // 0x643d10: LoadField: r4 = r2->field_f
    //     0x643d10: ldur            w4, [x2, #0xf]
    // 0x643d14: DecompressPointer r4
    //     0x643d14: add             x4, x4, HEAP, lsl #32
    // 0x643d18: LoadField: r2 = r4->field_f
    //     0x643d18: ldur            w2, [x4, #0xf]
    // 0x643d1c: DecompressPointer r2
    //     0x643d1c: add             x2, x2, HEAP, lsl #32
    // 0x643d20: mov             x0, x3
    // 0x643d24: r1 = 1
    //     0x643d24: movz            x1, #0x1
    // 0x643d28: cmp             x1, x0
    // 0x643d2c: b.hs            #0x644098
    // 0x643d30: LoadField: r0 = r4->field_13
    //     0x643d30: ldur            w0, [x4, #0x13]
    // 0x643d34: DecompressPointer r0
    //     0x643d34: add             x0, x0, HEAP, lsl #32
    // 0x643d38: mov             x1, x2
    // 0x643d3c: b               #0x643d4c
    // 0x643d40: ldur            x1, [fp, #-0x18]
    // 0x643d44: mov             x0, x1
    // 0x643d48: r1 = ""
    //     0x643d48: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x643d4c: mov             x3, x1
    // 0x643d50: r4 = false
    //     0x643d50: add             x4, NULL, #0x30  ; false
    // 0x643d54: ldur            x1, [fp, #-0x10]
    // 0x643d58: stur            x4, [fp, #-0x18]
    // 0x643d5c: stur            x3, [fp, #-0x20]
    // 0x643d60: stur            x0, [fp, #-0x28]
    // 0x643d64: r2 = 3
    //     0x643d64: movz            x2, #0x3
    // 0x643d68: r0 = group()
    //     0x643d68: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0x643d6c: cmp             w0, NULL
    // 0x643d70: b.eq            #0x64409c
    // 0x643d74: mov             x1, x0
    // 0x643d78: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x643d78: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x643d7c: r0 = parse()
    //     0x643d7c: bl              #0x60d170  ; [dart:core] Uri::parse
    // 0x643d80: mov             x2, x0
    // 0x643d84: stur            x2, [fp, #-0x30]
    // 0x643d88: r0 = LoadClassIdInstr(r2)
    //     0x643d88: ldur            x0, [x2, #-1]
    //     0x643d8c: ubfx            x0, x0, #0xc, #0x14
    // 0x643d90: mov             x1, x2
    // 0x643d94: r0 = GDT[cid_x0 + -0xffa]()
    //     0x643d94: sub             lr, x0, #0xffa
    //     0x643d98: ldr             lr, [x21, lr, lsl #3]
    //     0x643d9c: blr             lr
    // 0x643da0: mov             x3, x0
    // 0x643da4: ldur            x2, [fp, #-0x30]
    // 0x643da8: stur            x3, [fp, #-0x38]
    // 0x643dac: r0 = LoadClassIdInstr(r2)
    //     0x643dac: ldur            x0, [x2, #-1]
    //     0x643db0: ubfx            x0, x0, #0xc, #0x14
    // 0x643db4: mov             x1, x2
    // 0x643db8: r0 = GDT[cid_x0 + -0xfcb]()
    //     0x643db8: sub             lr, x0, #0xfcb
    //     0x643dbc: ldr             lr, [x21, lr, lsl #3]
    //     0x643dc0: blr             lr
    // 0x643dc4: r1 = LoadClassIdInstr(r0)
    //     0x643dc4: ldur            x1, [x0, #-1]
    //     0x643dc8: ubfx            x1, x1, #0xc, #0x14
    // 0x643dcc: r16 = "dart"
    //     0x643dcc: ldr             x16, [PP, #0xb38]  ; [pp+0xb38] "dart"
    // 0x643dd0: stp             x16, x0, [SP]
    // 0x643dd4: mov             x0, x1
    // 0x643dd8: mov             lr, x0
    // 0x643ddc: ldr             lr, [x21, lr, lsl #3]
    // 0x643de0: blr             lr
    // 0x643de4: tbz             w0, #4, #0x643e28
    // 0x643de8: ldur            x2, [fp, #-0x30]
    // 0x643dec: r0 = LoadClassIdInstr(r2)
    //     0x643dec: ldur            x0, [x2, #-1]
    //     0x643df0: ubfx            x0, x0, #0xc, #0x14
    // 0x643df4: mov             x1, x2
    // 0x643df8: r0 = GDT[cid_x0 + -0xfcb]()
    //     0x643df8: sub             lr, x0, #0xfcb
    //     0x643dfc: ldr             lr, [x21, lr, lsl #3]
    //     0x643e00: blr             lr
    // 0x643e04: r1 = LoadClassIdInstr(r0)
    //     0x643e04: ldur            x1, [x0, #-1]
    //     0x643e08: ubfx            x1, x1, #0xc, #0x14
    // 0x643e0c: r16 = "package"
    //     0x643e0c: ldr             x16, [PP, #0xb40]  ; [pp+0xb40] "package"
    // 0x643e10: stp             x16, x0, [SP]
    // 0x643e14: mov             x0, x1
    // 0x643e18: mov             lr, x0
    // 0x643e1c: ldr             lr, [x21, lr, lsl #3]
    // 0x643e20: blr             lr
    // 0x643e24: tbnz            w0, #4, #0x643f18
    // 0x643e28: ldur            x2, [fp, #-0x30]
    // 0x643e2c: r0 = LoadClassIdInstr(r2)
    //     0x643e2c: ldur            x0, [x2, #-1]
    //     0x643e30: ubfx            x0, x0, #0xc, #0x14
    // 0x643e34: mov             x1, x2
    // 0x643e38: r0 = GDT[cid_x0 + -0xeab]()
    //     0x643e38: sub             lr, x0, #0xeab
    //     0x643e3c: ldr             lr, [x21, lr, lsl #3]
    //     0x643e40: blr             lr
    // 0x643e44: mov             x2, x0
    // 0x643e48: LoadField: r0 = r2->field_b
    //     0x643e48: ldur            w0, [x2, #0xb]
    // 0x643e4c: r1 = LoadInt32Instr(r0)
    //     0x643e4c: sbfx            x1, x0, #1, #0x1f
    // 0x643e50: mov             x0, x1
    // 0x643e54: r1 = 0
    //     0x643e54: movz            x1, #0
    // 0x643e58: cmp             x1, x0
    // 0x643e5c: b.hs            #0x6440a0
    // 0x643e60: LoadField: r3 = r2->field_f
    //     0x643e60: ldur            w3, [x2, #0xf]
    // 0x643e64: DecompressPointer r3
    //     0x643e64: add             x3, x3, HEAP, lsl #32
    // 0x643e68: ldur            x2, [fp, #-0x30]
    // 0x643e6c: stur            x3, [fp, #-0x40]
    // 0x643e70: r0 = LoadClassIdInstr(r2)
    //     0x643e70: ldur            x0, [x2, #-1]
    //     0x643e74: ubfx            x0, x0, #0xc, #0x14
    // 0x643e78: mov             x1, x2
    // 0x643e7c: r0 = GDT[cid_x0 + -0xffa]()
    //     0x643e7c: sub             lr, x0, #0xffa
    //     0x643e80: ldr             lr, [x21, lr, lsl #3]
    //     0x643e84: blr             lr
    // 0x643e88: mov             x3, x0
    // 0x643e8c: ldur            x2, [fp, #-0x30]
    // 0x643e90: stur            x3, [fp, #-0x48]
    // 0x643e94: r0 = LoadClassIdInstr(r2)
    //     0x643e94: ldur            x0, [x2, #-1]
    //     0x643e98: ubfx            x0, x0, #0xc, #0x14
    // 0x643e9c: mov             x1, x2
    // 0x643ea0: r0 = GDT[cid_x0 + -0xeab]()
    //     0x643ea0: sub             lr, x0, #0xeab
    //     0x643ea4: ldr             lr, [x21, lr, lsl #3]
    //     0x643ea8: blr             lr
    // 0x643eac: mov             x2, x0
    // 0x643eb0: LoadField: r0 = r2->field_b
    //     0x643eb0: ldur            w0, [x2, #0xb]
    // 0x643eb4: r1 = LoadInt32Instr(r0)
    //     0x643eb4: sbfx            x1, x0, #1, #0x1f
    // 0x643eb8: mov             x0, x1
    // 0x643ebc: r1 = 0
    //     0x643ebc: movz            x1, #0
    // 0x643ec0: cmp             x1, x0
    // 0x643ec4: b.hs            #0x6440a4
    // 0x643ec8: LoadField: r0 = r2->field_f
    //     0x643ec8: ldur            w0, [x2, #0xf]
    // 0x643ecc: DecompressPointer r0
    //     0x643ecc: add             x0, x0, HEAP, lsl #32
    // 0x643ed0: stur            x0, [fp, #-0x50]
    // 0x643ed4: r1 = Null
    //     0x643ed4: mov             x1, NULL
    // 0x643ed8: r2 = 4
    //     0x643ed8: movz            x2, #0x4
    // 0x643edc: r0 = AllocateArray()
    //     0x643edc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x643ee0: mov             x1, x0
    // 0x643ee4: ldur            x0, [fp, #-0x50]
    // 0x643ee8: StoreField: r1->field_f = r0
    //     0x643ee8: stur            w0, [x1, #0xf]
    // 0x643eec: r16 = "/"
    //     0x643eec: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x643ef0: StoreField: r1->field_13 = r16
    //     0x643ef0: stur            w16, [x1, #0x13]
    // 0x643ef4: str             x1, [SP]
    // 0x643ef8: r0 = _interpolate()
    //     0x643ef8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x643efc: ldur            x1, [fp, #-0x48]
    // 0x643f00: mov             x2, x0
    // 0x643f04: r3 = ""
    //     0x643f04: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0x643f08: r0 = replaceFirst()
    //     0x643f08: bl              #0x6440c0  ; [dart:core] _StringBase::replaceFirst
    // 0x643f0c: ldur            x4, [fp, #-0x40]
    // 0x643f10: mov             x3, x0
    // 0x643f14: b               #0x643f20
    // 0x643f18: ldur            x3, [fp, #-0x38]
    // 0x643f1c: r4 = "<unknown>"
    //     0x643f1c: ldr             x4, [PP, #0xb28]  ; [pp+0xb28] "<unknown>"
    // 0x643f20: ldur            x0, [fp, #-0x30]
    // 0x643f24: ldur            x1, [fp, #-0x10]
    // 0x643f28: stur            x4, [fp, #-0x38]
    // 0x643f2c: stur            x3, [fp, #-0x40]
    // 0x643f30: r2 = 1
    //     0x643f30: movz            x2, #0x1
    // 0x643f34: r0 = group()
    //     0x643f34: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0x643f38: cmp             w0, NULL
    // 0x643f3c: b.eq            #0x6440a8
    // 0x643f40: mov             x1, x0
    // 0x643f44: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x643f44: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x643f48: r0 = parse()
    //     0x643f48: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x643f4c: mov             x2, x0
    // 0x643f50: ldur            x1, [fp, #-0x30]
    // 0x643f54: stur            x2, [fp, #-0x58]
    // 0x643f58: r0 = LoadClassIdInstr(r1)
    //     0x643f58: ldur            x0, [x1, #-1]
    //     0x643f5c: ubfx            x0, x0, #0xc, #0x14
    // 0x643f60: r0 = GDT[cid_x0 + -0xfcb]()
    //     0x643f60: sub             lr, x0, #0xfcb
    //     0x643f64: ldr             lr, [x21, lr, lsl #3]
    //     0x643f68: blr             lr
    // 0x643f6c: ldur            x1, [fp, #-0x10]
    // 0x643f70: r2 = 4
    //     0x643f70: movz            x2, #0x4
    // 0x643f74: stur            x0, [fp, #-0x30]
    // 0x643f78: r0 = group()
    //     0x643f78: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0x643f7c: cmp             w0, NULL
    // 0x643f80: b.ne            #0x643f8c
    // 0x643f84: r0 = -1
    //     0x643f84: movn            x0, #0
    // 0x643f88: b               #0x643fac
    // 0x643f8c: ldur            x1, [fp, #-0x10]
    // 0x643f90: r2 = 4
    //     0x643f90: movz            x2, #0x4
    // 0x643f94: r0 = group()
    //     0x643f94: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0x643f98: cmp             w0, NULL
    // 0x643f9c: b.eq            #0x6440ac
    // 0x643fa0: mov             x1, x0
    // 0x643fa4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x643fa4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x643fa8: r0 = parse()
    //     0x643fa8: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x643fac: ldur            x1, [fp, #-0x10]
    // 0x643fb0: stur            x0, [fp, #-0x60]
    // 0x643fb4: r2 = 5
    //     0x643fb4: movz            x2, #0x5
    // 0x643fb8: r0 = group()
    //     0x643fb8: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0x643fbc: cmp             w0, NULL
    // 0x643fc0: b.ne            #0x643fcc
    // 0x643fc4: r9 = -1
    //     0x643fc4: movn            x9, #0
    // 0x643fc8: b               #0x643ff0
    // 0x643fcc: ldur            x1, [fp, #-0x10]
    // 0x643fd0: r2 = 5
    //     0x643fd0: movz            x2, #0x5
    // 0x643fd4: r0 = group()
    //     0x643fd4: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0x643fd8: cmp             w0, NULL
    // 0x643fdc: b.eq            #0x6440b0
    // 0x643fe0: mov             x1, x0
    // 0x643fe4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x643fe4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x643fe8: r0 = parse()
    //     0x643fe8: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x643fec: mov             x9, x0
    // 0x643ff0: ldur            x8, [fp, #-8]
    // 0x643ff4: ldur            x7, [fp, #-0x18]
    // 0x643ff8: ldur            x6, [fp, #-0x20]
    // 0x643ffc: ldur            x5, [fp, #-0x28]
    // 0x644000: ldur            x4, [fp, #-0x38]
    // 0x644004: ldur            x3, [fp, #-0x40]
    // 0x644008: ldur            x2, [fp, #-0x58]
    // 0x64400c: ldur            x1, [fp, #-0x30]
    // 0x644010: ldur            x0, [fp, #-0x60]
    // 0x644014: stur            x9, [fp, #-0x68]
    // 0x644018: r0 = StackFrame()
    //     0x644018: bl              #0x6440b4  ; AllocateStackFrameStub -> StackFrame (size=0x3c)
    // 0x64401c: ldur            x1, [fp, #-0x58]
    // 0x644020: StoreField: r0->field_b = r1
    //     0x644020: stur            x1, [x0, #0xb]
    // 0x644024: ldur            x1, [fp, #-0x68]
    // 0x644028: StoreField: r0->field_27 = r1
    //     0x644028: stur            x1, [x0, #0x27]
    // 0x64402c: ldur            x1, [fp, #-0x60]
    // 0x644030: StoreField: r0->field_1f = r1
    //     0x644030: stur            x1, [x0, #0x1f]
    // 0x644034: ldur            x1, [fp, #-0x30]
    // 0x644038: StoreField: r0->field_13 = r1
    //     0x644038: stur            w1, [x0, #0x13]
    // 0x64403c: ldur            x1, [fp, #-0x38]
    // 0x644040: ArrayStore: r0[0] = r1  ; List_4
    //     0x644040: stur            w1, [x0, #0x17]
    // 0x644044: ldur            x1, [fp, #-0x40]
    // 0x644048: StoreField: r0->field_1b = r1
    //     0x644048: stur            w1, [x0, #0x1b]
    // 0x64404c: ldur            x1, [fp, #-0x20]
    // 0x644050: StoreField: r0->field_2f = r1
    //     0x644050: stur            w1, [x0, #0x2f]
    // 0x644054: ldur            x1, [fp, #-0x28]
    // 0x644058: StoreField: r0->field_33 = r1
    //     0x644058: stur            w1, [x0, #0x33]
    // 0x64405c: ldur            x1, [fp, #-0x18]
    // 0x644060: StoreField: r0->field_37 = r1
    //     0x644060: stur            w1, [x0, #0x37]
    // 0x644064: ldur            x1, [fp, #-8]
    // 0x644068: StoreField: r0->field_7 = r1
    //     0x644068: stur            w1, [x0, #7]
    // 0x64406c: LeaveFrame
    //     0x64406c: mov             SP, fp
    //     0x644070: ldp             fp, lr, [SP], #0x10
    // 0x644074: ret
    //     0x644074: ret             
    // 0x644078: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x644078: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64407c: b               #0x643a7c
    // 0x644080: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x644080: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x644084: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x644084: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x644088: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x644088: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x64408c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x64408c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x644090: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x644090: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x644094: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x644094: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x644098: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x644098: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x64409c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x64409c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6440a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6440a0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6440a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6440a4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6440a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6440a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6440ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6440ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6440b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6440b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _tryParseWebNonDebugFrame(/* No info */) {
    // ** addr: 0x6444fc, size: 0x168
    // 0x6444fc: EnterFrame
    //     0x6444fc: stp             fp, lr, [SP, #-0x10]!
    //     0x644500: mov             fp, SP
    // 0x644504: AllocStack(0x20)
    //     0x644504: sub             SP, SP, #0x20
    // 0x644508: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0x644508: mov             x2, x1
    //     0x64450c: stur            x1, [fp, #-8]
    // 0x644510: CheckStackOverflow
    //     0x644510: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x644514: cmp             SP, x16
    //     0x644518: b.ls            #0x644658
    // 0x64451c: r0 = InitLateStaticField(0x694) // [package:flutter/src/foundation/stack_frame.dart] StackFrame::_webNonDebugFramePattern
    //     0x64451c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x644520: ldr             x0, [x0, #0xd28]
    //     0x644524: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x644528: cmp             w0, w16
    //     0x64452c: b.ne            #0x644538
    //     0x644530: ldr             x2, [PP, #0x10b8]  ; [pp+0x10b8] Field <StackFrame._webNonDebugFramePattern@50425567>: static late final (offset: 0x694)
    //     0x644534: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x644538: mov             x1, x0
    // 0x64453c: ldur            x2, [fp, #-8]
    // 0x644540: r0 = firstMatch()
    //     0x644540: bl              #0x644328  ; [dart:core] _RegExp::firstMatch
    // 0x644544: cmp             w0, NULL
    // 0x644548: b.ne            #0x64455c
    // 0x64454c: r0 = Null
    //     0x64454c: mov             x0, NULL
    // 0x644550: LeaveFrame
    //     0x644550: mov             SP, fp
    //     0x644554: ldp             fp, lr, [SP], #0x10
    // 0x644558: ret
    //     0x644558: ret             
    // 0x64455c: mov             x1, x0
    // 0x644560: r2 = 1
    //     0x644560: movz            x2, #0x1
    // 0x644564: r0 = group()
    //     0x644564: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0x644568: cmp             w0, NULL
    // 0x64456c: b.eq            #0x644660
    // 0x644570: r1 = LoadClassIdInstr(r0)
    //     0x644570: ldur            x1, [x0, #-1]
    //     0x644574: ubfx            x1, x1, #0xc, #0x14
    // 0x644578: mov             x16, x0
    // 0x64457c: mov             x0, x1
    // 0x644580: mov             x1, x16
    // 0x644584: r2 = "."
    //     0x644584: ldr             x2, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x644588: r0 = GDT[cid_x0 + -0x1000]()
    //     0x644588: sub             lr, x0, #1, lsl #12
    //     0x64458c: ldr             lr, [x21, lr, lsl #3]
    //     0x644590: blr             lr
    // 0x644594: stur            x0, [fp, #-0x10]
    // 0x644598: LoadField: r1 = r0->field_b
    //     0x644598: ldur            w1, [x0, #0xb]
    // 0x64459c: r2 = LoadInt32Instr(r1)
    //     0x64459c: sbfx            x2, x1, #1, #0x1f
    // 0x6445a0: cmp             x2, #1
    // 0x6445a4: b.le            #0x6445b4
    // 0x6445a8: mov             x1, x0
    // 0x6445ac: r0 = first()
    //     0x6445ac: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6445b0: b               #0x6445b8
    // 0x6445b4: r0 = "<unknown>"
    //     0x6445b4: ldr             x0, [PP, #0xb28]  ; [pp+0xb28] "<unknown>"
    // 0x6445b8: ldur            x1, [fp, #-0x10]
    // 0x6445bc: stur            x0, [fp, #-0x18]
    // 0x6445c0: LoadField: r2 = r1->field_b
    //     0x6445c0: ldur            w2, [x1, #0xb]
    // 0x6445c4: r3 = LoadInt32Instr(r2)
    //     0x6445c4: sbfx            x3, x2, #1, #0x1f
    // 0x6445c8: cmp             x3, #1
    // 0x6445cc: b.le            #0x6445f4
    // 0x6445d0: r2 = 1
    //     0x6445d0: movz            x2, #0x1
    // 0x6445d4: r0 = skip()
    //     0x6445d4: bl              #0xa5a484  ; [dart:collection] ListBase::skip
    // 0x6445d8: r16 = "."
    //     0x6445d8: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x6445dc: str             x16, [SP]
    // 0x6445e0: mov             x1, x0
    // 0x6445e4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x6445e4: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x6445e8: r0 = join()
    //     0x6445e8: bl              #0x7adcb0  ; [dart:_internal] ListIterable::join
    // 0x6445ec: mov             x2, x0
    // 0x6445f0: b               #0x6445fc
    // 0x6445f4: r0 = single()
    //     0x6445f4: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x6445f8: mov             x2, x0
    // 0x6445fc: ldur            x1, [fp, #-8]
    // 0x644600: ldur            x0, [fp, #-0x18]
    // 0x644604: stur            x2, [fp, #-0x10]
    // 0x644608: r0 = StackFrame()
    //     0x644608: bl              #0x6440b4  ; AllocateStackFrameStub -> StackFrame (size=0x3c)
    // 0x64460c: r1 = -1
    //     0x64460c: movn            x1, #0
    // 0x644610: StoreField: r0->field_b = r1
    //     0x644610: stur            x1, [x0, #0xb]
    // 0x644614: StoreField: r0->field_27 = r1
    //     0x644614: stur            x1, [x0, #0x27]
    // 0x644618: StoreField: r0->field_1f = r1
    //     0x644618: stur            x1, [x0, #0x1f]
    // 0x64461c: r1 = "<unknown>"
    //     0x64461c: ldr             x1, [PP, #0xb28]  ; [pp+0xb28] "<unknown>"
    // 0x644620: StoreField: r0->field_13 = r1
    //     0x644620: stur            w1, [x0, #0x13]
    // 0x644624: ArrayStore: r0[0] = r1  ; List_4
    //     0x644624: stur            w1, [x0, #0x17]
    // 0x644628: StoreField: r0->field_1b = r1
    //     0x644628: stur            w1, [x0, #0x1b]
    // 0x64462c: ldur            x1, [fp, #-0x18]
    // 0x644630: StoreField: r0->field_2f = r1
    //     0x644630: stur            w1, [x0, #0x2f]
    // 0x644634: ldur            x1, [fp, #-0x10]
    // 0x644638: StoreField: r0->field_33 = r1
    //     0x644638: stur            w1, [x0, #0x33]
    // 0x64463c: r1 = false
    //     0x64463c: add             x1, NULL, #0x30  ; false
    // 0x644640: StoreField: r0->field_37 = r1
    //     0x644640: stur            w1, [x0, #0x37]
    // 0x644644: ldur            x1, [fp, #-8]
    // 0x644648: StoreField: r0->field_7 = r1
    //     0x644648: stur            w1, [x0, #7]
    // 0x64464c: LeaveFrame
    //     0x64464c: mov             SP, fp
    //     0x644650: ldp             fp, lr, [SP], #0x10
    // 0x644654: ret
    //     0x644654: ret             
    // 0x644658: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x644658: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64465c: b               #0x64451c
    // 0x644660: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x644660: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static RegExp _webNonDebugFramePattern() {
    // ** addr: 0x644664, size: 0x54
    // 0x644664: EnterFrame
    //     0x644664: stp             fp, lr, [SP, #-0x10]!
    //     0x644668: mov             fp, SP
    // 0x64466c: AllocStack(0x30)
    //     0x64466c: sub             SP, SP, #0x30
    // 0x644670: CheckStackOverflow
    //     0x644670: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x644674: cmp             SP, x16
    //     0x644678: b.ls            #0x6446b0
    // 0x64467c: r16 = "^\\s*at ([^\\s]+).*$"
    //     0x64467c: ldr             x16, [PP, #0x10c0]  ; [pp+0x10c0] "^\\s*at ([^\\s]+).*$"
    // 0x644680: stp             x16, NULL, [SP, #0x20]
    // 0x644684: r16 = false
    //     0x644684: add             x16, NULL, #0x30  ; false
    // 0x644688: r30 = true
    //     0x644688: add             lr, NULL, #0x20  ; true
    // 0x64468c: stp             lr, x16, [SP, #0x10]
    // 0x644690: r16 = false
    //     0x644690: add             x16, NULL, #0x30  ; false
    // 0x644694: r30 = false
    //     0x644694: add             lr, NULL, #0x30  ; false
    // 0x644698: stp             lr, x16, [SP]
    // 0x64469c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x64469c: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x6446a0: r0 = _RegExp()
    //     0x6446a0: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x6446a4: LeaveFrame
    //     0x6446a4: mov             SP, fp
    //     0x6446a8: ldp             fp, lr, [SP], #0x10
    // 0x6446ac: ret
    //     0x6446ac: ret             
    // 0x6446b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6446b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6446b4: b               #0x64467c
  }
  [closure] static bool <anonymous closure>(dynamic, String) {
    // ** addr: 0x6446b8, size: 0x1c
    // 0x6446b8: ldr             x1, [SP]
    // 0x6446bc: LoadField: r2 = r1->field_7
    //     0x6446bc: ldur            w2, [x1, #7]
    // 0x6446c0: cbnz            w2, #0x6446cc
    // 0x6446c4: r0 = false
    //     0x6446c4: add             x0, NULL, #0x30  ; false
    // 0x6446c8: b               #0x6446d0
    // 0x6446cc: r0 = true
    //     0x6446cc: add             x0, NULL, #0x20  ; true
    // 0x6446d0: ret
    //     0x6446d0: ret             
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbeb5d8, size: 0xd8
    // 0xbeb5d8: EnterFrame
    //     0xbeb5d8: stp             fp, lr, [SP, #-0x10]!
    //     0xbeb5dc: mov             fp, SP
    // 0xbeb5e0: AllocStack(0x28)
    //     0xbeb5e0: sub             SP, SP, #0x28
    // 0xbeb5e4: CheckStackOverflow
    //     0xbeb5e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeb5e8: cmp             SP, x16
    //     0xbeb5ec: b.ls            #0xbeb6a8
    // 0xbeb5f0: ldr             x0, [fp, #0x10]
    // 0xbeb5f4: LoadField: r2 = r0->field_b
    //     0xbeb5f4: ldur            x2, [x0, #0xb]
    // 0xbeb5f8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xbeb5f8: ldur            w3, [x0, #0x17]
    // 0xbeb5fc: DecompressPointer r3
    //     0xbeb5fc: add             x3, x3, HEAP, lsl #32
    // 0xbeb600: LoadField: r4 = r0->field_1f
    //     0xbeb600: ldur            x4, [x0, #0x1f]
    // 0xbeb604: LoadField: r5 = r0->field_27
    //     0xbeb604: ldur            x5, [x0, #0x27]
    // 0xbeb608: LoadField: r6 = r0->field_2f
    //     0xbeb608: ldur            w6, [x0, #0x2f]
    // 0xbeb60c: DecompressPointer r6
    //     0xbeb60c: add             x6, x6, HEAP, lsl #32
    // 0xbeb610: LoadField: r7 = r0->field_33
    //     0xbeb610: ldur            w7, [x0, #0x33]
    // 0xbeb614: DecompressPointer r7
    //     0xbeb614: add             x7, x7, HEAP, lsl #32
    // 0xbeb618: LoadField: r8 = r0->field_7
    //     0xbeb618: ldur            w8, [x0, #7]
    // 0xbeb61c: DecompressPointer r8
    //     0xbeb61c: add             x8, x8, HEAP, lsl #32
    // 0xbeb620: r0 = BoxInt64Instr(r2)
    //     0xbeb620: sbfiz           x0, x2, #1, #0x1f
    //     0xbeb624: cmp             x2, x0, asr #1
    //     0xbeb628: b.eq            #0xbeb634
    //     0xbeb62c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbeb630: stur            x2, [x0, #7]
    // 0xbeb634: mov             x2, x0
    // 0xbeb638: r0 = BoxInt64Instr(r4)
    //     0xbeb638: sbfiz           x0, x4, #1, #0x1f
    //     0xbeb63c: cmp             x4, x0, asr #1
    //     0xbeb640: b.eq            #0xbeb64c
    //     0xbeb644: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbeb648: stur            x4, [x0, #7]
    // 0xbeb64c: mov             x4, x0
    // 0xbeb650: r0 = BoxInt64Instr(r5)
    //     0xbeb650: sbfiz           x0, x5, #1, #0x1f
    //     0xbeb654: cmp             x5, x0, asr #1
    //     0xbeb658: b.eq            #0xbeb664
    //     0xbeb65c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbeb660: stur            x5, [x0, #7]
    // 0xbeb664: stp             x0, x4, [SP, #0x18]
    // 0xbeb668: stp             x7, x6, [SP, #8]
    // 0xbeb66c: str             x8, [SP]
    // 0xbeb670: mov             x1, x2
    // 0xbeb674: mov             x2, x3
    // 0xbeb678: r4 = const [0, 0x7, 0x5, 0x7, null]
    //     0xbeb678: add             x4, PP, #0xb, lsl #12  ; [pp+0xb8c0] List(5) [0, 0x7, 0x5, 0x7, Null]
    //     0xbeb67c: ldr             x4, [x4, #0x8c0]
    // 0xbeb680: r0 = hash()
    //     0xbeb680: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbeb684: mov             x2, x0
    // 0xbeb688: r0 = BoxInt64Instr(r2)
    //     0xbeb688: sbfiz           x0, x2, #1, #0x1f
    //     0xbeb68c: cmp             x2, x0, asr #1
    //     0xbeb690: b.eq            #0xbeb69c
    //     0xbeb694: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbeb698: stur            x2, [x0, #7]
    // 0xbeb69c: LeaveFrame
    //     0xbeb69c: mov             SP, fp
    //     0xbeb6a0: ldp             fp, lr, [SP], #0x10
    // 0xbeb6a4: ret
    //     0xbeb6a4: ret             
    // 0xbeb6a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbeb6a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbeb6ac: b               #0xbeb5f0
  }
  _ ==(/* No info */) {
    // ** addr: 0xd5c6b0, size: 0x1b4
    // 0xd5c6b0: EnterFrame
    //     0xd5c6b0: stp             fp, lr, [SP, #-0x10]!
    //     0xd5c6b4: mov             fp, SP
    // 0xd5c6b8: AllocStack(0x10)
    //     0xd5c6b8: sub             SP, SP, #0x10
    // 0xd5c6bc: CheckStackOverflow
    //     0xd5c6bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd5c6c0: cmp             SP, x16
    //     0xd5c6c4: b.ls            #0xd5c85c
    // 0xd5c6c8: ldr             x0, [fp, #0x10]
    // 0xd5c6cc: cmp             w0, NULL
    // 0xd5c6d0: b.ne            #0xd5c6e4
    // 0xd5c6d4: r0 = false
    //     0xd5c6d4: add             x0, NULL, #0x30  ; false
    // 0xd5c6d8: LeaveFrame
    //     0xd5c6d8: mov             SP, fp
    //     0xd5c6dc: ldp             fp, lr, [SP], #0x10
    // 0xd5c6e0: ret
    //     0xd5c6e0: ret             
    // 0xd5c6e4: str             x0, [SP]
    // 0xd5c6e8: r0 = runtimeType()
    //     0xd5c6e8: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd5c6ec: r1 = LoadClassIdInstr(r0)
    //     0xd5c6ec: ldur            x1, [x0, #-1]
    //     0xd5c6f0: ubfx            x1, x1, #0xc, #0x14
    // 0xd5c6f4: r16 = StackFrame
    //     0xd5c6f4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1db48] Type: StackFrame
    //     0xd5c6f8: ldr             x16, [x16, #0xb48]
    // 0xd5c6fc: stp             x16, x0, [SP]
    // 0xd5c700: mov             x0, x1
    // 0xd5c704: mov             lr, x0
    // 0xd5c708: ldr             lr, [x21, lr, lsl #3]
    // 0xd5c70c: blr             lr
    // 0xd5c710: tbz             w0, #4, #0xd5c724
    // 0xd5c714: r0 = false
    //     0xd5c714: add             x0, NULL, #0x30  ; false
    // 0xd5c718: LeaveFrame
    //     0xd5c718: mov             SP, fp
    //     0xd5c71c: ldp             fp, lr, [SP], #0x10
    // 0xd5c720: ret
    //     0xd5c720: ret             
    // 0xd5c724: ldr             x1, [fp, #0x10]
    // 0xd5c728: r0 = 60
    //     0xd5c728: movz            x0, #0x3c
    // 0xd5c72c: branchIfSmi(r1, 0xd5c738)
    //     0xd5c72c: tbz             w1, #0, #0xd5c738
    // 0xd5c730: r0 = LoadClassIdInstr(r1)
    //     0xd5c730: ldur            x0, [x1, #-1]
    //     0xd5c734: ubfx            x0, x0, #0xc, #0x14
    // 0xd5c738: cmp             x0, #0xde4
    // 0xd5c73c: b.ne            #0xd5c84c
    // 0xd5c740: ldr             x2, [fp, #0x18]
    // 0xd5c744: LoadField: r0 = r1->field_b
    //     0xd5c744: ldur            x0, [x1, #0xb]
    // 0xd5c748: LoadField: r3 = r2->field_b
    //     0xd5c748: ldur            x3, [x2, #0xb]
    // 0xd5c74c: cmp             x0, x3
    // 0xd5c750: b.ne            #0xd5c84c
    // 0xd5c754: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xd5c754: ldur            w0, [x1, #0x17]
    // 0xd5c758: DecompressPointer r0
    //     0xd5c758: add             x0, x0, HEAP, lsl #32
    // 0xd5c75c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xd5c75c: ldur            w3, [x2, #0x17]
    // 0xd5c760: DecompressPointer r3
    //     0xd5c760: add             x3, x3, HEAP, lsl #32
    // 0xd5c764: r4 = LoadClassIdInstr(r0)
    //     0xd5c764: ldur            x4, [x0, #-1]
    //     0xd5c768: ubfx            x4, x4, #0xc, #0x14
    // 0xd5c76c: stp             x3, x0, [SP]
    // 0xd5c770: mov             x0, x4
    // 0xd5c774: mov             lr, x0
    // 0xd5c778: ldr             lr, [x21, lr, lsl #3]
    // 0xd5c77c: blr             lr
    // 0xd5c780: tbnz            w0, #4, #0xd5c84c
    // 0xd5c784: ldr             x2, [fp, #0x18]
    // 0xd5c788: ldr             x1, [fp, #0x10]
    // 0xd5c78c: LoadField: r0 = r1->field_1f
    //     0xd5c78c: ldur            x0, [x1, #0x1f]
    // 0xd5c790: LoadField: r3 = r2->field_1f
    //     0xd5c790: ldur            x3, [x2, #0x1f]
    // 0xd5c794: cmp             x0, x3
    // 0xd5c798: b.ne            #0xd5c84c
    // 0xd5c79c: LoadField: r0 = r1->field_27
    //     0xd5c79c: ldur            x0, [x1, #0x27]
    // 0xd5c7a0: LoadField: r3 = r2->field_27
    //     0xd5c7a0: ldur            x3, [x2, #0x27]
    // 0xd5c7a4: cmp             x0, x3
    // 0xd5c7a8: b.ne            #0xd5c84c
    // 0xd5c7ac: LoadField: r0 = r1->field_2f
    //     0xd5c7ac: ldur            w0, [x1, #0x2f]
    // 0xd5c7b0: DecompressPointer r0
    //     0xd5c7b0: add             x0, x0, HEAP, lsl #32
    // 0xd5c7b4: LoadField: r3 = r2->field_2f
    //     0xd5c7b4: ldur            w3, [x2, #0x2f]
    // 0xd5c7b8: DecompressPointer r3
    //     0xd5c7b8: add             x3, x3, HEAP, lsl #32
    // 0xd5c7bc: r4 = LoadClassIdInstr(r0)
    //     0xd5c7bc: ldur            x4, [x0, #-1]
    //     0xd5c7c0: ubfx            x4, x4, #0xc, #0x14
    // 0xd5c7c4: stp             x3, x0, [SP]
    // 0xd5c7c8: mov             x0, x4
    // 0xd5c7cc: mov             lr, x0
    // 0xd5c7d0: ldr             lr, [x21, lr, lsl #3]
    // 0xd5c7d4: blr             lr
    // 0xd5c7d8: tbnz            w0, #4, #0xd5c84c
    // 0xd5c7dc: ldr             x2, [fp, #0x18]
    // 0xd5c7e0: ldr             x1, [fp, #0x10]
    // 0xd5c7e4: LoadField: r0 = r1->field_33
    //     0xd5c7e4: ldur            w0, [x1, #0x33]
    // 0xd5c7e8: DecompressPointer r0
    //     0xd5c7e8: add             x0, x0, HEAP, lsl #32
    // 0xd5c7ec: LoadField: r3 = r2->field_33
    //     0xd5c7ec: ldur            w3, [x2, #0x33]
    // 0xd5c7f0: DecompressPointer r3
    //     0xd5c7f0: add             x3, x3, HEAP, lsl #32
    // 0xd5c7f4: r4 = LoadClassIdInstr(r0)
    //     0xd5c7f4: ldur            x4, [x0, #-1]
    //     0xd5c7f8: ubfx            x4, x4, #0xc, #0x14
    // 0xd5c7fc: stp             x3, x0, [SP]
    // 0xd5c800: mov             x0, x4
    // 0xd5c804: mov             lr, x0
    // 0xd5c808: ldr             lr, [x21, lr, lsl #3]
    // 0xd5c80c: blr             lr
    // 0xd5c810: tbnz            w0, #4, #0xd5c84c
    // 0xd5c814: ldr             x1, [fp, #0x18]
    // 0xd5c818: ldr             x0, [fp, #0x10]
    // 0xd5c81c: LoadField: r2 = r0->field_7
    //     0xd5c81c: ldur            w2, [x0, #7]
    // 0xd5c820: DecompressPointer r2
    //     0xd5c820: add             x2, x2, HEAP, lsl #32
    // 0xd5c824: LoadField: r0 = r1->field_7
    //     0xd5c824: ldur            w0, [x1, #7]
    // 0xd5c828: DecompressPointer r0
    //     0xd5c828: add             x0, x0, HEAP, lsl #32
    // 0xd5c82c: r1 = LoadClassIdInstr(r2)
    //     0xd5c82c: ldur            x1, [x2, #-1]
    //     0xd5c830: ubfx            x1, x1, #0xc, #0x14
    // 0xd5c834: stp             x0, x2, [SP]
    // 0xd5c838: mov             x0, x1
    // 0xd5c83c: mov             lr, x0
    // 0xd5c840: ldr             lr, [x21, lr, lsl #3]
    // 0xd5c844: blr             lr
    // 0xd5c848: b               #0xd5c850
    // 0xd5c84c: r0 = false
    //     0xd5c84c: add             x0, NULL, #0x30  ; false
    // 0xd5c850: LeaveFrame
    //     0xd5c850: mov             SP, fp
    //     0xd5c854: ldp             fp, lr, [SP], #0x10
    // 0xd5c858: ret
    //     0xd5c858: ret             
    // 0xd5c85c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd5c85c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd5c860: b               #0xd5c6c8
  }
}
