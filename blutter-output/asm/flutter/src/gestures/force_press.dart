// lib: , url: package:flutter/src/gestures/force_press.dart

// class id: 1048830, size: 0x8
class :: {
}

// class id: 3469, size: 0xc, field offset: 0x8
class ForcePressDetails extends Object {
}

// class id: 3546, size: 0x4c, field offset: 0x24
class ForcePressGestureRecognizer extends OneSequenceGestureRecognizer {

  late OffsetPair _lastPosition; // offset: 0x40
  late double _lastPressure; // offset: 0x44

  dynamic handleEvent(dynamic) {
    // ** addr: 0x75b1cc, size: 0x24
    // 0x75b1cc: EnterFrame
    //     0x75b1cc: stp             fp, lr, [SP, #-0x10]!
    //     0x75b1d0: mov             fp, SP
    // 0x75b1d4: ldr             x2, [fp, #0x10]
    // 0x75b1d8: r1 = Function 'handleEvent':.
    //     0x75b1d8: add             x1, PP, #0x59, lsl #12  ; [pp+0x590f8] AnonymousClosure: (0x75b210), in [package:flutter/src/gestures/force_press.dart] ForcePressGestureRecognizer::handleEvent (0x75b24c)
    //     0x75b1dc: ldr             x1, [x1, #0xf8]
    // 0x75b1e0: r0 = AllocateClosure()
    //     0x75b1e0: bl              #0xec1630  ; AllocateClosureStub
    // 0x75b1e4: LeaveFrame
    //     0x75b1e4: mov             SP, fp
    //     0x75b1e8: ldp             fp, lr, [SP], #0x10
    // 0x75b1ec: ret
    //     0x75b1ec: ret             
  }
  [closure] void handleEvent(dynamic, PointerEvent) {
    // ** addr: 0x75b210, size: 0x3c
    // 0x75b210: EnterFrame
    //     0x75b210: stp             fp, lr, [SP, #-0x10]!
    //     0x75b214: mov             fp, SP
    // 0x75b218: ldr             x0, [fp, #0x18]
    // 0x75b21c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x75b21c: ldur            w1, [x0, #0x17]
    // 0x75b220: DecompressPointer r1
    //     0x75b220: add             x1, x1, HEAP, lsl #32
    // 0x75b224: CheckStackOverflow
    //     0x75b224: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75b228: cmp             SP, x16
    //     0x75b22c: b.ls            #0x75b244
    // 0x75b230: ldr             x2, [fp, #0x10]
    // 0x75b234: r0 = handleEvent()
    //     0x75b234: bl              #0x75b24c  ; [package:flutter/src/gestures/force_press.dart] ForcePressGestureRecognizer::handleEvent
    // 0x75b238: LeaveFrame
    //     0x75b238: mov             SP, fp
    //     0x75b23c: ldp             fp, lr, [SP], #0x10
    // 0x75b240: ret
    //     0x75b240: ret             
    // 0x75b244: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75b244: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75b248: b               #0x75b230
  }
  _ handleEvent(/* No info */) {
    // ** addr: 0x75b24c, size: 0x45c
    // 0x75b24c: EnterFrame
    //     0x75b24c: stp             fp, lr, [SP, #-0x10]!
    //     0x75b250: mov             fp, SP
    // 0x75b254: AllocStack(0x48)
    //     0x75b254: sub             SP, SP, #0x48
    // 0x75b258: SetupParameters(ForcePressGestureRecognizer this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x75b258: mov             x0, x2
    //     0x75b25c: stur            x1, [fp, #-8]
    //     0x75b260: stur            x2, [fp, #-0x10]
    // 0x75b264: CheckStackOverflow
    //     0x75b264: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75b268: cmp             SP, x16
    //     0x75b26c: b.ls            #0x75b63c
    // 0x75b270: r1 = 1
    //     0x75b270: movz            x1, #0x1
    // 0x75b274: r0 = AllocateContext()
    //     0x75b274: bl              #0xec126c  ; AllocateContextStub
    // 0x75b278: mov             x4, x0
    // 0x75b27c: ldur            x3, [fp, #-8]
    // 0x75b280: stur            x4, [fp, #-0x18]
    // 0x75b284: StoreField: r4->field_f = r3
    //     0x75b284: stur            w3, [x4, #0xf]
    // 0x75b288: ldur            x0, [fp, #-0x10]
    // 0x75b28c: r2 = Null
    //     0x75b28c: mov             x2, NULL
    // 0x75b290: r1 = Null
    //     0x75b290: mov             x1, NULL
    // 0x75b294: cmp             w0, NULL
    // 0x75b298: b.eq            #0x75b2b8
    // 0x75b29c: branchIfSmi(r0, 0x75b2b8)
    //     0x75b29c: tbz             w0, #0, #0x75b2b8
    // 0x75b2a0: r3 = LoadClassIdInstr(r0)
    //     0x75b2a0: ldur            x3, [x0, #-1]
    //     0x75b2a4: ubfx            x3, x3, #0xc, #0x14
    // 0x75b2a8: cmp             x3, #0xda5
    // 0x75b2ac: b.eq            #0x75b2c0
    // 0x75b2b0: cmp             x3, #0xfcf
    // 0x75b2b4: b.eq            #0x75b2c0
    // 0x75b2b8: r0 = false
    //     0x75b2b8: add             x0, NULL, #0x30  ; false
    // 0x75b2bc: b               #0x75b2c4
    // 0x75b2c0: r0 = true
    //     0x75b2c0: add             x0, NULL, #0x20  ; true
    // 0x75b2c4: tbz             w0, #4, #0x75b308
    // 0x75b2c8: ldur            x0, [fp, #-0x10]
    // 0x75b2cc: r2 = Null
    //     0x75b2cc: mov             x2, NULL
    // 0x75b2d0: r1 = Null
    //     0x75b2d0: mov             x1, NULL
    // 0x75b2d4: cmp             w0, NULL
    // 0x75b2d8: b.eq            #0x75b2f8
    // 0x75b2dc: branchIfSmi(r0, 0x75b2f8)
    //     0x75b2dc: tbz             w0, #0, #0x75b2f8
    // 0x75b2e0: r3 = LoadClassIdInstr(r0)
    //     0x75b2e0: ldur            x3, [x0, #-1]
    //     0x75b2e4: ubfx            x3, x3, #0xc, #0x14
    // 0x75b2e8: cmp             x3, #0xda7
    // 0x75b2ec: b.eq            #0x75b300
    // 0x75b2f0: cmp             x3, #0xfd1
    // 0x75b2f4: b.eq            #0x75b300
    // 0x75b2f8: r0 = false
    //     0x75b2f8: add             x0, NULL, #0x30  ; false
    // 0x75b2fc: b               #0x75b304
    // 0x75b300: r0 = true
    //     0x75b300: add             x0, NULL, #0x20  ; true
    // 0x75b304: tbnz            w0, #4, #0x75b620
    // 0x75b308: ldur            x2, [fp, #-8]
    // 0x75b30c: ldur            x3, [fp, #-0x10]
    // 0x75b310: r0 = LoadClassIdInstr(r3)
    //     0x75b310: ldur            x0, [x3, #-1]
    //     0x75b314: ubfx            x0, x0, #0xc, #0x14
    // 0x75b318: mov             x1, x3
    // 0x75b31c: r0 = GDT[cid_x0 + 0x12186]()
    //     0x75b31c: movz            x17, #0x2186
    //     0x75b320: movk            x17, #0x1, lsl #16
    //     0x75b324: add             lr, x0, x17
    //     0x75b328: ldr             lr, [x21, lr, lsl #3]
    //     0x75b32c: blr             lr
    // 0x75b330: ldur            x2, [fp, #-0x10]
    // 0x75b334: stur            d0, [fp, #-0x28]
    // 0x75b338: r0 = LoadClassIdInstr(r2)
    //     0x75b338: ldur            x0, [x2, #-1]
    //     0x75b33c: ubfx            x0, x0, #0xc, #0x14
    // 0x75b340: mov             x1, x2
    // 0x75b344: r0 = GDT[cid_x0 + 0x9e5a]()
    //     0x75b344: movz            x17, #0x9e5a
    //     0x75b348: add             lr, x0, x17
    //     0x75b34c: ldr             lr, [x21, lr, lsl #3]
    //     0x75b350: blr             lr
    // 0x75b354: ldur            x2, [fp, #-0x10]
    // 0x75b358: stur            d0, [fp, #-0x30]
    // 0x75b35c: r0 = LoadClassIdInstr(r2)
    //     0x75b35c: ldur            x0, [x2, #-1]
    //     0x75b360: ubfx            x0, x0, #0xc, #0x14
    // 0x75b364: mov             x1, x2
    // 0x75b368: r0 = GDT[cid_x0 + 0x1342a]()
    //     0x75b368: movz            x17, #0x342a
    //     0x75b36c: movk            x17, #0x1, lsl #16
    //     0x75b370: add             lr, x0, x17
    //     0x75b374: ldr             lr, [x21, lr, lsl #3]
    //     0x75b378: blr             lr
    // 0x75b37c: mov             v1.16b, v0.16b
    // 0x75b380: ldur            d0, [fp, #-0x28]
    // 0x75b384: r1 = inline_Allocate_Double()
    //     0x75b384: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0x75b388: add             x1, x1, #0x10
    //     0x75b38c: cmp             x0, x1
    //     0x75b390: b.ls            #0x75b644
    //     0x75b394: str             x1, [THR, #0x50]  ; THR::top
    //     0x75b398: sub             x1, x1, #0xf
    //     0x75b39c: movz            x0, #0xe15c
    //     0x75b3a0: movk            x0, #0x3, lsl #16
    //     0x75b3a4: stur            x0, [x1, #-1]
    // 0x75b3a8: StoreField: r1->field_7 = d0
    //     0x75b3a8: stur            d0, [x1, #7]
    // 0x75b3ac: ldur            d0, [fp, #-0x30]
    // 0x75b3b0: r2 = inline_Allocate_Double()
    //     0x75b3b0: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x75b3b4: add             x2, x2, #0x10
    //     0x75b3b8: cmp             x0, x2
    //     0x75b3bc: b.ls            #0x75b658
    //     0x75b3c0: str             x2, [THR, #0x50]  ; THR::top
    //     0x75b3c4: sub             x2, x2, #0xf
    //     0x75b3c8: movz            x0, #0xe15c
    //     0x75b3cc: movk            x0, #0x3, lsl #16
    //     0x75b3d0: stur            x0, [x2, #-1]
    // 0x75b3d4: StoreField: r2->field_7 = d0
    //     0x75b3d4: stur            d0, [x2, #7]
    // 0x75b3d8: r3 = inline_Allocate_Double()
    //     0x75b3d8: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x75b3dc: add             x3, x3, #0x10
    //     0x75b3e0: cmp             x0, x3
    //     0x75b3e4: b.ls            #0x75b674
    //     0x75b3e8: str             x3, [THR, #0x50]  ; THR::top
    //     0x75b3ec: sub             x3, x3, #0xf
    //     0x75b3f0: movz            x0, #0xe15c
    //     0x75b3f4: movk            x0, #0x3, lsl #16
    //     0x75b3f8: stur            x0, [x3, #-1]
    // 0x75b3fc: StoreField: r3->field_7 = d1
    //     0x75b3fc: stur            d1, [x3, #7]
    // 0x75b400: r0 = _inverseLerp()
    //     0x75b400: bl              #0x75b92c  ; [package:flutter/src/gestures/force_press.dart] ForcePressGestureRecognizer::_inverseLerp
    // 0x75b404: stur            d0, [fp, #-0x28]
    // 0x75b408: r0 = OffsetPair()
    //     0x75b408: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x75b40c: mov             x1, x0
    // 0x75b410: ldur            x2, [fp, #-0x10]
    // 0x75b414: stur            x0, [fp, #-0x20]
    // 0x75b418: r0 = OffsetPair.fromEventPosition()
    //     0x75b418: bl              #0x75b878  ; [package:flutter/src/gestures/recognizer.dart] OffsetPair::OffsetPair.fromEventPosition
    // 0x75b41c: ldur            x0, [fp, #-0x20]
    // 0x75b420: ldur            x3, [fp, #-8]
    // 0x75b424: StoreField: r3->field_3f = r0
    //     0x75b424: stur            w0, [x3, #0x3f]
    //     0x75b428: ldurb           w16, [x3, #-1]
    //     0x75b42c: ldurb           w17, [x0, #-1]
    //     0x75b430: and             x16, x17, x16, lsr #2
    //     0x75b434: tst             x16, HEAP, lsr #32
    //     0x75b438: b.eq            #0x75b440
    //     0x75b43c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x75b440: ldur            d0, [fp, #-0x28]
    // 0x75b444: r0 = inline_Allocate_Double()
    //     0x75b444: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x75b448: add             x0, x0, #0x10
    //     0x75b44c: cmp             x1, x0
    //     0x75b450: b.ls            #0x75b690
    //     0x75b454: str             x0, [THR, #0x50]  ; THR::top
    //     0x75b458: sub             x0, x0, #0xf
    //     0x75b45c: movz            x1, #0xe15c
    //     0x75b460: movk            x1, #0x3, lsl #16
    //     0x75b464: stur            x1, [x0, #-1]
    // 0x75b468: StoreField: r0->field_7 = d0
    //     0x75b468: stur            d0, [x0, #7]
    // 0x75b46c: StoreField: r3->field_43 = r0
    //     0x75b46c: stur            w0, [x3, #0x43]
    //     0x75b470: ldurb           w16, [x3, #-1]
    //     0x75b474: ldurb           w17, [x0, #-1]
    //     0x75b478: and             x16, x17, x16, lsr #2
    //     0x75b47c: tst             x16, HEAP, lsr #32
    //     0x75b480: b.eq            #0x75b488
    //     0x75b484: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x75b488: LoadField: r0 = r3->field_47
    //     0x75b488: ldur            w0, [x3, #0x47]
    // 0x75b48c: DecompressPointer r0
    //     0x75b48c: add             x0, x0, HEAP, lsl #32
    // 0x75b490: r16 = Instance__ForceState
    //     0x75b490: add             x16, PP, #0x59, lsl #12  ; [pp+0x59100] Obj!_ForceState@e36e61
    //     0x75b494: ldr             x16, [x16, #0x100]
    // 0x75b498: cmp             w0, w16
    // 0x75b49c: b.ne            #0x75b5b0
    // 0x75b4a0: d1 = 0.400000
    //     0x75b4a0: ldr             d1, [PP, #0x64d8]  ; [pp+0x64d8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x75b4a4: fcmp            d0, d1
    // 0x75b4a8: b.le            #0x75b4cc
    // 0x75b4ac: r0 = Instance__ForceState
    //     0x75b4ac: add             x0, PP, #0x59, lsl #12  ; [pp+0x59108] Obj!_ForceState@e36e41
    //     0x75b4b0: ldr             x0, [x0, #0x108]
    // 0x75b4b4: StoreField: r3->field_47 = r0
    //     0x75b4b4: stur            w0, [x3, #0x47]
    // 0x75b4b8: mov             x1, x3
    // 0x75b4bc: r2 = Instance_GestureDisposition
    //     0x75b4bc: add             x2, PP, #0x30, lsl #12  ; [pp+0x30e00] Obj!GestureDisposition@e36ee1
    //     0x75b4c0: ldr             x2, [x2, #0xe00]
    // 0x75b4c4: r0 = resolve()
    //     0x75b4c4: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x75b4c8: b               #0x75b5b0
    // 0x75b4cc: mov             x2, x3
    // 0x75b4d0: ldur            x3, [fp, #-0x10]
    // 0x75b4d4: r0 = LoadClassIdInstr(r3)
    //     0x75b4d4: ldur            x0, [x3, #-1]
    //     0x75b4d8: ubfx            x0, x0, #0xc, #0x14
    // 0x75b4dc: mov             x1, x3
    // 0x75b4e0: r0 = GDT[cid_x0 + 0x1326d]()
    //     0x75b4e0: movz            x17, #0x326d
    //     0x75b4e4: movk            x17, #0x1, lsl #16
    //     0x75b4e8: add             lr, x0, x17
    //     0x75b4ec: ldr             lr, [x21, lr, lsl #3]
    //     0x75b4f0: blr             lr
    // 0x75b4f4: LoadField: d0 = r0->field_7
    //     0x75b4f4: ldur            d0, [x0, #7]
    // 0x75b4f8: fmul            d1, d0, d0
    // 0x75b4fc: LoadField: d0 = r0->field_f
    //     0x75b4fc: ldur            d0, [x0, #0xf]
    // 0x75b500: fmul            d2, d0, d0
    // 0x75b504: fadd            d0, d1, d2
    // 0x75b508: ldur            x2, [fp, #-0x10]
    // 0x75b50c: stur            d0, [fp, #-0x30]
    // 0x75b510: r0 = LoadClassIdInstr(r2)
    //     0x75b510: ldur            x0, [x2, #-1]
    //     0x75b514: ubfx            x0, x0, #0xc, #0x14
    // 0x75b518: mov             x1, x2
    // 0x75b51c: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x75b51c: movz            x17, #0x30b7
    //     0x75b520: movk            x17, #0x1, lsl #16
    //     0x75b524: add             lr, x0, x17
    //     0x75b528: ldr             lr, [x21, lr, lsl #3]
    //     0x75b52c: blr             lr
    // 0x75b530: mov             x1, x0
    // 0x75b534: ldur            x0, [fp, #-8]
    // 0x75b538: LoadField: r2 = r0->field_7
    //     0x75b538: ldur            w2, [x0, #7]
    // 0x75b53c: DecompressPointer r2
    //     0x75b53c: add             x2, x2, HEAP, lsl #32
    // 0x75b540: LoadField: r3 = r1->field_7
    //     0x75b540: ldur            x3, [x1, #7]
    // 0x75b544: cmp             x3, #2
    // 0x75b548: b.gt            #0x75b564
    // 0x75b54c: cmp             x3, #1
    // 0x75b550: b.gt            #0x75b564
    // 0x75b554: cmp             x3, #0
    // 0x75b558: b.le            #0x75b564
    // 0x75b55c: d1 = 1.000000
    //     0x75b55c: fmov            d1, #1.00000000
    // 0x75b560: b               #0x75b594
    // 0x75b564: cmp             w2, NULL
    // 0x75b568: b.ne            #0x75b574
    // 0x75b56c: r1 = Null
    //     0x75b56c: mov             x1, NULL
    // 0x75b570: b               #0x75b57c
    // 0x75b574: LoadField: r1 = r2->field_7
    //     0x75b574: ldur            w1, [x2, #7]
    // 0x75b578: DecompressPointer r1
    //     0x75b578: add             x1, x1, HEAP, lsl #32
    // 0x75b57c: cmp             w1, NULL
    // 0x75b580: b.ne            #0x75b58c
    // 0x75b584: d0 = 18.000000
    //     0x75b584: fmov            d0, #18.00000000
    // 0x75b588: b               #0x75b590
    // 0x75b58c: LoadField: d0 = r1->field_7
    //     0x75b58c: ldur            d0, [x1, #7]
    // 0x75b590: mov             v1.16b, v0.16b
    // 0x75b594: ldur            d0, [fp, #-0x30]
    // 0x75b598: fcmp            d0, d1
    // 0x75b59c: b.le            #0x75b5b0
    // 0x75b5a0: mov             x1, x0
    // 0x75b5a4: r2 = Instance_GestureDisposition
    //     0x75b5a4: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0x75b5a8: ldr             x2, [x2, #0xde8]
    // 0x75b5ac: r0 = resolve()
    //     0x75b5ac: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x75b5b0: ldur            d0, [fp, #-0x28]
    // 0x75b5b4: d1 = 0.400000
    //     0x75b5b4: ldr             d1, [PP, #0x64d8]  ; [pp+0x64d8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x75b5b8: fcmp            d0, d1
    // 0x75b5bc: b.le            #0x75b620
    // 0x75b5c0: ldur            x0, [fp, #-8]
    // 0x75b5c4: LoadField: r1 = r0->field_47
    //     0x75b5c4: ldur            w1, [x0, #0x47]
    // 0x75b5c8: DecompressPointer r1
    //     0x75b5c8: add             x1, x1, HEAP, lsl #32
    // 0x75b5cc: r16 = Instance__ForceState
    //     0x75b5cc: add             x16, PP, #0x59, lsl #12  ; [pp+0x59110] Obj!_ForceState@e36e21
    //     0x75b5d0: ldr             x16, [x16, #0x110]
    // 0x75b5d4: cmp             w1, w16
    // 0x75b5d8: b.ne            #0x75b620
    // 0x75b5dc: r1 = Instance__ForceState
    //     0x75b5dc: add             x1, PP, #0x59, lsl #12  ; [pp+0x59108] Obj!_ForceState@e36e41
    //     0x75b5e0: ldr             x1, [x1, #0x108]
    // 0x75b5e4: StoreField: r0->field_47 = r1
    //     0x75b5e4: stur            w1, [x0, #0x47]
    // 0x75b5e8: LoadField: r1 = r0->field_23
    //     0x75b5e8: ldur            w1, [x0, #0x23]
    // 0x75b5ec: DecompressPointer r1
    //     0x75b5ec: add             x1, x1, HEAP, lsl #32
    // 0x75b5f0: cmp             w1, NULL
    // 0x75b5f4: b.eq            #0x75b620
    // 0x75b5f8: ldur            x2, [fp, #-0x18]
    // 0x75b5fc: r1 = Function '<anonymous closure>':.
    //     0x75b5fc: add             x1, PP, #0x59, lsl #12  ; [pp+0x59118] AnonymousClosure: (0x75b998), in [package:flutter/src/gestures/force_press.dart] ForcePressGestureRecognizer::handleEvent (0x75b24c)
    //     0x75b600: ldr             x1, [x1, #0x118]
    // 0x75b604: r0 = AllocateClosure()
    //     0x75b604: bl              #0xec1630  ; AllocateClosureStub
    // 0x75b608: r16 = <void?>
    //     0x75b608: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x75b60c: ldur            lr, [fp, #-8]
    // 0x75b610: stp             lr, x16, [SP, #8]
    // 0x75b614: str             x0, [SP]
    // 0x75b618: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x75b618: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x75b61c: r0 = invokeCallback()
    //     0x75b61c: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x75b620: ldur            x1, [fp, #-8]
    // 0x75b624: ldur            x2, [fp, #-0x10]
    // 0x75b628: r0 = stopTrackingIfPointerNoLongerDown()
    //     0x75b628: bl              #0x75b718  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::stopTrackingIfPointerNoLongerDown
    // 0x75b62c: r0 = Null
    //     0x75b62c: mov             x0, NULL
    // 0x75b630: LeaveFrame
    //     0x75b630: mov             SP, fp
    //     0x75b634: ldp             fp, lr, [SP], #0x10
    // 0x75b638: ret
    //     0x75b638: ret             
    // 0x75b63c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75b63c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75b640: b               #0x75b270
    // 0x75b644: stp             q0, q1, [SP, #-0x20]!
    // 0x75b648: r0 = AllocateDouble()
    //     0x75b648: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75b64c: mov             x1, x0
    // 0x75b650: ldp             q0, q1, [SP], #0x20
    // 0x75b654: b               #0x75b3a8
    // 0x75b658: stp             q0, q1, [SP, #-0x20]!
    // 0x75b65c: SaveReg r1
    //     0x75b65c: str             x1, [SP, #-8]!
    // 0x75b660: r0 = AllocateDouble()
    //     0x75b660: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75b664: mov             x2, x0
    // 0x75b668: RestoreReg r1
    //     0x75b668: ldr             x1, [SP], #8
    // 0x75b66c: ldp             q0, q1, [SP], #0x20
    // 0x75b670: b               #0x75b3d4
    // 0x75b674: SaveReg d1
    //     0x75b674: str             q1, [SP, #-0x10]!
    // 0x75b678: stp             x1, x2, [SP, #-0x10]!
    // 0x75b67c: r0 = AllocateDouble()
    //     0x75b67c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75b680: mov             x3, x0
    // 0x75b684: ldp             x1, x2, [SP], #0x10
    // 0x75b688: RestoreReg d1
    //     0x75b688: ldr             q1, [SP], #0x10
    // 0x75b68c: b               #0x75b3fc
    // 0x75b690: SaveReg d0
    //     0x75b690: str             q0, [SP, #-0x10]!
    // 0x75b694: SaveReg r3
    //     0x75b694: str             x3, [SP, #-8]!
    // 0x75b698: r0 = AllocateDouble()
    //     0x75b698: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75b69c: RestoreReg r3
    //     0x75b69c: ldr             x3, [SP], #8
    // 0x75b6a0: RestoreReg d0
    //     0x75b6a0: ldr             q0, [SP], #0x10
    // 0x75b6a4: b               #0x75b468
  }
  [closure] static double _inverseLerp(dynamic, double, double, double) {
    // ** addr: 0x75b6a8, size: 0x70
    // 0x75b6a8: EnterFrame
    //     0x75b6a8: stp             fp, lr, [SP, #-0x10]!
    //     0x75b6ac: mov             fp, SP
    // 0x75b6b0: CheckStackOverflow
    //     0x75b6b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75b6b4: cmp             SP, x16
    //     0x75b6b8: b.ls            #0x75b700
    // 0x75b6bc: ldr             x1, [fp, #0x20]
    // 0x75b6c0: ldr             x2, [fp, #0x18]
    // 0x75b6c4: ldr             x3, [fp, #0x10]
    // 0x75b6c8: r0 = _inverseLerp()
    //     0x75b6c8: bl              #0x75b92c  ; [package:flutter/src/gestures/force_press.dart] ForcePressGestureRecognizer::_inverseLerp
    // 0x75b6cc: r0 = inline_Allocate_Double()
    //     0x75b6cc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x75b6d0: add             x0, x0, #0x10
    //     0x75b6d4: cmp             x1, x0
    //     0x75b6d8: b.ls            #0x75b708
    //     0x75b6dc: str             x0, [THR, #0x50]  ; THR::top
    //     0x75b6e0: sub             x0, x0, #0xf
    //     0x75b6e4: movz            x1, #0xe15c
    //     0x75b6e8: movk            x1, #0x3, lsl #16
    //     0x75b6ec: stur            x1, [x0, #-1]
    // 0x75b6f0: StoreField: r0->field_7 = d0
    //     0x75b6f0: stur            d0, [x0, #7]
    // 0x75b6f4: LeaveFrame
    //     0x75b6f4: mov             SP, fp
    //     0x75b6f8: ldp             fp, lr, [SP], #0x10
    // 0x75b6fc: ret
    //     0x75b6fc: ret             
    // 0x75b700: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75b700: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75b704: b               #0x75b6bc
    // 0x75b708: SaveReg d0
    //     0x75b708: str             q0, [SP, #-0x10]!
    // 0x75b70c: r0 = AllocateDouble()
    //     0x75b70c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75b710: RestoreReg d0
    //     0x75b710: ldr             q0, [SP], #0x10
    // 0x75b714: b               #0x75b6f0
  }
  static _ _inverseLerp(/* No info */) {
    // ** addr: 0x75b92c, size: 0x6c
    // 0x75b92c: LoadField: d1 = r1->field_7
    //     0x75b92c: ldur            d1, [x1, #7]
    // 0x75b930: LoadField: d2 = r3->field_7
    //     0x75b930: ldur            d2, [x3, #7]
    // 0x75b934: fsub            d3, d2, d1
    // 0x75b938: LoadField: d2 = r2->field_7
    //     0x75b938: ldur            d2, [x2, #7]
    // 0x75b93c: fsub            d4, d2, d1
    // 0x75b940: fdiv            d1, d3, d4
    // 0x75b944: fcmp            d1, d1
    // 0x75b948: b.vs            #0x75b990
    // 0x75b94c: d2 = 0.000000
    //     0x75b94c: eor             v2.16b, v2.16b, v2.16b
    // 0x75b950: fcmp            d2, d1
    // 0x75b954: b.le            #0x75b960
    // 0x75b958: d2 = 0.000000
    //     0x75b958: eor             v2.16b, v2.16b, v2.16b
    // 0x75b95c: b               #0x75b988
    // 0x75b960: d2 = 1.000000
    //     0x75b960: fmov            d2, #1.00000000
    // 0x75b964: fcmp            d1, d2
    // 0x75b968: b.le            #0x75b974
    // 0x75b96c: d2 = 1.000000
    //     0x75b96c: fmov            d2, #1.00000000
    // 0x75b970: b               #0x75b988
    // 0x75b974: fcmp            d1, d1
    // 0x75b978: b.vc            #0x75b984
    // 0x75b97c: d2 = 1.000000
    //     0x75b97c: fmov            d2, #1.00000000
    // 0x75b980: b               #0x75b988
    // 0x75b984: mov             v2.16b, v1.16b
    // 0x75b988: mov             v0.16b, v2.16b
    // 0x75b98c: b               #0x75b994
    // 0x75b990: mov             v0.16b, v1.16b
    // 0x75b994: ret
    //     0x75b994: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x75b998, size: 0xb4
    // 0x75b998: EnterFrame
    //     0x75b998: stp             fp, lr, [SP, #-0x10]!
    //     0x75b99c: mov             fp, SP
    // 0x75b9a0: AllocStack(0x10)
    //     0x75b9a0: sub             SP, SP, #0x10
    // 0x75b9a4: SetupParameters()
    //     0x75b9a4: ldr             x0, [fp, #0x10]
    //     0x75b9a8: ldur            w1, [x0, #0x17]
    //     0x75b9ac: add             x1, x1, HEAP, lsl #32
    // 0x75b9b0: CheckStackOverflow
    //     0x75b9b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75b9b4: cmp             SP, x16
    //     0x75b9b8: b.ls            #0x75ba34
    // 0x75b9bc: LoadField: r0 = r1->field_f
    //     0x75b9bc: ldur            w0, [x1, #0xf]
    // 0x75b9c0: DecompressPointer r0
    //     0x75b9c0: add             x0, x0, HEAP, lsl #32
    // 0x75b9c4: LoadField: r1 = r0->field_23
    //     0x75b9c4: ldur            w1, [x0, #0x23]
    // 0x75b9c8: DecompressPointer r1
    //     0x75b9c8: add             x1, x1, HEAP, lsl #32
    // 0x75b9cc: stur            x1, [fp, #-0x10]
    // 0x75b9d0: cmp             w1, NULL
    // 0x75b9d4: b.eq            #0x75ba3c
    // 0x75b9d8: LoadField: r2 = r0->field_3f
    //     0x75b9d8: ldur            w2, [x0, #0x3f]
    // 0x75b9dc: DecompressPointer r2
    //     0x75b9dc: add             x2, x2, HEAP, lsl #32
    // 0x75b9e0: r16 = Sentinel
    //     0x75b9e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x75b9e4: cmp             w2, w16
    // 0x75b9e8: b.eq            #0x75ba40
    // 0x75b9ec: LoadField: r0 = r2->field_b
    //     0x75b9ec: ldur            w0, [x2, #0xb]
    // 0x75b9f0: DecompressPointer r0
    //     0x75b9f0: add             x0, x0, HEAP, lsl #32
    // 0x75b9f4: stur            x0, [fp, #-8]
    // 0x75b9f8: r0 = ForcePressDetails()
    //     0x75b9f8: bl              #0x75cc30  ; AllocateForcePressDetailsStub -> ForcePressDetails (size=0xc)
    // 0x75b9fc: mov             x1, x0
    // 0x75ba00: ldur            x0, [fp, #-8]
    // 0x75ba04: StoreField: r1->field_7 = r0
    //     0x75ba04: stur            w0, [x1, #7]
    // 0x75ba08: ldur            x0, [fp, #-0x10]
    // 0x75ba0c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x75ba0c: ldur            w2, [x0, #0x17]
    // 0x75ba10: DecompressPointer r2
    //     0x75ba10: add             x2, x2, HEAP, lsl #32
    // 0x75ba14: mov             x16, x1
    // 0x75ba18: mov             x1, x2
    // 0x75ba1c: mov             x2, x16
    // 0x75ba20: r0 = _forcePressStarted()
    //     0x75ba20: bl              #0x75baac  ; [package:flutter/src/widgets/text_selection.dart] _TextSelectionGestureDetectorState::_forcePressStarted
    // 0x75ba24: r0 = Null
    //     0x75ba24: mov             x0, NULL
    // 0x75ba28: LeaveFrame
    //     0x75ba28: mov             SP, fp
    //     0x75ba2c: ldp             fp, lr, [SP], #0x10
    // 0x75ba30: ret
    //     0x75ba30: ret             
    // 0x75ba34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75ba34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75ba38: b               #0x75b9bc
    // 0x75ba3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75ba3c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x75ba40: r9 = _lastPosition
    //     0x75ba40: add             x9, PP, #0x59, lsl #12  ; [pp+0x59120] Field <ForcePressGestureRecognizer._lastPosition@423518508>: late (offset: 0x40)
    //     0x75ba44: ldr             x9, [x9, #0x120]
    // 0x75ba48: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x75ba48: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ didStopTrackingLastPointer(/* No info */) {
    // ** addr: 0x7d7210, size: 0xfc
    // 0x7d7210: EnterFrame
    //     0x7d7210: stp             fp, lr, [SP, #-0x10]!
    //     0x7d7214: mov             fp, SP
    // 0x7d7218: AllocStack(0x20)
    //     0x7d7218: sub             SP, SP, #0x20
    // 0x7d721c: SetupParameters(ForcePressGestureRecognizer this /* r1 => r1, fp-0x8 */)
    //     0x7d721c: stur            x1, [fp, #-8]
    // 0x7d7220: CheckStackOverflow
    //     0x7d7220: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d7224: cmp             SP, x16
    //     0x7d7228: b.ls            #0x7d7304
    // 0x7d722c: r1 = 1
    //     0x7d722c: movz            x1, #0x1
    // 0x7d7230: r0 = AllocateContext()
    //     0x7d7230: bl              #0xec126c  ; AllocateContextStub
    // 0x7d7234: mov             x1, x0
    // 0x7d7238: ldur            x0, [fp, #-8]
    // 0x7d723c: StoreField: r1->field_f = r0
    //     0x7d723c: stur            w0, [x1, #0xf]
    // 0x7d7240: LoadField: r2 = r0->field_47
    //     0x7d7240: ldur            w2, [x0, #0x47]
    // 0x7d7244: DecompressPointer r2
    //     0x7d7244: add             x2, x2, HEAP, lsl #32
    // 0x7d7248: r16 = Instance__ForceState
    //     0x7d7248: add             x16, PP, #0x59, lsl #12  ; [pp+0x59108] Obj!_ForceState@e36e41
    //     0x7d724c: ldr             x16, [x16, #0x108]
    // 0x7d7250: cmp             w2, w16
    // 0x7d7254: b.ne            #0x7d7260
    // 0x7d7258: r3 = true
    //     0x7d7258: add             x3, NULL, #0x20  ; true
    // 0x7d725c: b               #0x7d7278
    // 0x7d7260: r16 = Instance__ForceState
    //     0x7d7260: add             x16, PP, #0x59, lsl #12  ; [pp+0x59128] Obj!_ForceState@e36e81
    //     0x7d7264: ldr             x16, [x16, #0x128]
    // 0x7d7268: cmp             w2, w16
    // 0x7d726c: r16 = true
    //     0x7d726c: add             x16, NULL, #0x20  ; true
    // 0x7d7270: r17 = false
    //     0x7d7270: add             x17, NULL, #0x30  ; false
    // 0x7d7274: csel            x3, x16, x17, eq
    // 0x7d7278: r16 = Instance__ForceState
    //     0x7d7278: add             x16, PP, #0x59, lsl #12  ; [pp+0x59100] Obj!_ForceState@e36e61
    //     0x7d727c: ldr             x16, [x16, #0x100]
    // 0x7d7280: cmp             w2, w16
    // 0x7d7284: b.ne            #0x7d72a8
    // 0x7d7288: mov             x1, x0
    // 0x7d728c: r2 = Instance_GestureDisposition
    //     0x7d728c: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0x7d7290: ldr             x2, [x2, #0xde8]
    // 0x7d7294: r0 = resolve()
    //     0x7d7294: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x7d7298: r0 = Null
    //     0x7d7298: mov             x0, NULL
    // 0x7d729c: LeaveFrame
    //     0x7d729c: mov             SP, fp
    //     0x7d72a0: ldp             fp, lr, [SP], #0x10
    // 0x7d72a4: ret
    //     0x7d72a4: ret             
    // 0x7d72a8: tbnz            w3, #4, #0x7d72e4
    // 0x7d72ac: LoadField: r2 = r0->field_2f
    //     0x7d72ac: ldur            w2, [x0, #0x2f]
    // 0x7d72b0: DecompressPointer r2
    //     0x7d72b0: add             x2, x2, HEAP, lsl #32
    // 0x7d72b4: cmp             w2, NULL
    // 0x7d72b8: b.eq            #0x7d72e4
    // 0x7d72bc: mov             x2, x1
    // 0x7d72c0: r1 = Function '<anonymous closure>':.
    //     0x7d72c0: add             x1, PP, #0x59, lsl #12  ; [pp+0x59130] AnonymousClosure: (0x7d730c), in [package:flutter/src/gestures/force_press.dart] ForcePressGestureRecognizer::didStopTrackingLastPointer (0x7d7210)
    //     0x7d72c4: ldr             x1, [x1, #0x130]
    // 0x7d72c8: r0 = AllocateClosure()
    //     0x7d72c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x7d72cc: r16 = <void?>
    //     0x7d72cc: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x7d72d0: ldur            lr, [fp, #-8]
    // 0x7d72d4: stp             lr, x16, [SP, #8]
    // 0x7d72d8: str             x0, [SP]
    // 0x7d72dc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7d72dc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7d72e0: r0 = invokeCallback()
    //     0x7d72e0: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x7d72e4: ldur            x1, [fp, #-8]
    // 0x7d72e8: r2 = Instance__ForceState
    //     0x7d72e8: add             x2, PP, #0x56, lsl #12  ; [pp+0x564c8] Obj!_ForceState@e36ea1
    //     0x7d72ec: ldr             x2, [x2, #0x4c8]
    // 0x7d72f0: StoreField: r1->field_47 = r2
    //     0x7d72f0: stur            w2, [x1, #0x47]
    // 0x7d72f4: r0 = Null
    //     0x7d72f4: mov             x0, NULL
    // 0x7d72f8: LeaveFrame
    //     0x7d72f8: mov             SP, fp
    //     0x7d72fc: ldp             fp, lr, [SP], #0x10
    // 0x7d7300: ret
    //     0x7d7300: ret             
    // 0x7d7304: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d7304: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d7308: b               #0x7d722c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x7d730c, size: 0xb4
    // 0x7d730c: EnterFrame
    //     0x7d730c: stp             fp, lr, [SP, #-0x10]!
    //     0x7d7310: mov             fp, SP
    // 0x7d7314: AllocStack(0x10)
    //     0x7d7314: sub             SP, SP, #0x10
    // 0x7d7318: SetupParameters()
    //     0x7d7318: ldr             x0, [fp, #0x10]
    //     0x7d731c: ldur            w1, [x0, #0x17]
    //     0x7d7320: add             x1, x1, HEAP, lsl #32
    // 0x7d7324: CheckStackOverflow
    //     0x7d7324: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d7328: cmp             SP, x16
    //     0x7d732c: b.ls            #0x7d73a8
    // 0x7d7330: LoadField: r0 = r1->field_f
    //     0x7d7330: ldur            w0, [x1, #0xf]
    // 0x7d7334: DecompressPointer r0
    //     0x7d7334: add             x0, x0, HEAP, lsl #32
    // 0x7d7338: LoadField: r1 = r0->field_2f
    //     0x7d7338: ldur            w1, [x0, #0x2f]
    // 0x7d733c: DecompressPointer r1
    //     0x7d733c: add             x1, x1, HEAP, lsl #32
    // 0x7d7340: stur            x1, [fp, #-0x10]
    // 0x7d7344: cmp             w1, NULL
    // 0x7d7348: b.eq            #0x7d73b0
    // 0x7d734c: LoadField: r2 = r0->field_3f
    //     0x7d734c: ldur            w2, [x0, #0x3f]
    // 0x7d7350: DecompressPointer r2
    //     0x7d7350: add             x2, x2, HEAP, lsl #32
    // 0x7d7354: r16 = Sentinel
    //     0x7d7354: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d7358: cmp             w2, w16
    // 0x7d735c: b.eq            #0x7d73b4
    // 0x7d7360: LoadField: r0 = r2->field_b
    //     0x7d7360: ldur            w0, [x2, #0xb]
    // 0x7d7364: DecompressPointer r0
    //     0x7d7364: add             x0, x0, HEAP, lsl #32
    // 0x7d7368: stur            x0, [fp, #-8]
    // 0x7d736c: r0 = ForcePressDetails()
    //     0x7d736c: bl              #0x75cc30  ; AllocateForcePressDetailsStub -> ForcePressDetails (size=0xc)
    // 0x7d7370: mov             x1, x0
    // 0x7d7374: ldur            x0, [fp, #-8]
    // 0x7d7378: StoreField: r1->field_7 = r0
    //     0x7d7378: stur            w0, [x1, #7]
    // 0x7d737c: ldur            x0, [fp, #-0x10]
    // 0x7d7380: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x7d7380: ldur            w2, [x0, #0x17]
    // 0x7d7384: DecompressPointer r2
    //     0x7d7384: add             x2, x2, HEAP, lsl #32
    // 0x7d7388: mov             x16, x1
    // 0x7d738c: mov             x1, x2
    // 0x7d7390: mov             x2, x16
    // 0x7d7394: r0 = _forcePressEnded()
    //     0x7d7394: bl              #0x7d73fc  ; [package:flutter/src/widgets/text_selection.dart] _TextSelectionGestureDetectorState::_forcePressEnded
    // 0x7d7398: r0 = Null
    //     0x7d7398: mov             x0, NULL
    // 0x7d739c: LeaveFrame
    //     0x7d739c: mov             SP, fp
    //     0x7d73a0: ldp             fp, lr, [SP], #0x10
    // 0x7d73a4: ret
    //     0x7d73a4: ret             
    // 0x7d73a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d73a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d73ac: b               #0x7d7330
    // 0x7d73b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d73b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7d73b4: r9 = _lastPosition
    //     0x7d73b4: add             x9, PP, #0x59, lsl #12  ; [pp+0x59120] Field <ForcePressGestureRecognizer._lastPosition@423518508>: late (offset: 0x40)
    //     0x7d73b8: ldr             x9, [x9, #0x120]
    // 0x7d73bc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7d73bc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ addAllowedPointer(/* No info */) {
    // ** addr: 0x801cc8, size: 0xec
    // 0x801cc8: EnterFrame
    //     0x801cc8: stp             fp, lr, [SP, #-0x10]!
    //     0x801ccc: mov             fp, SP
    // 0x801cd0: AllocStack(0x10)
    //     0x801cd0: sub             SP, SP, #0x10
    // 0x801cd4: SetupParameters(ForcePressGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x801cd4: mov             x3, x1
    //     0x801cd8: stur            x1, [fp, #-8]
    //     0x801cdc: stur            x2, [fp, #-0x10]
    // 0x801ce0: CheckStackOverflow
    //     0x801ce0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x801ce4: cmp             SP, x16
    //     0x801ce8: b.ls            #0x801dac
    // 0x801cec: r0 = LoadClassIdInstr(r2)
    //     0x801cec: ldur            x0, [x2, #-1]
    //     0x801cf0: ubfx            x0, x0, #0xc, #0x14
    // 0x801cf4: mov             x1, x2
    // 0x801cf8: r0 = GDT[cid_x0 + 0x9e5a]()
    //     0x801cf8: movz            x17, #0x9e5a
    //     0x801cfc: add             lr, x0, x17
    //     0x801d00: ldr             lr, [x21, lr, lsl #3]
    //     0x801d04: blr             lr
    // 0x801d08: mov             v1.16b, v0.16b
    // 0x801d0c: d0 = 1.000000
    //     0x801d0c: fmov            d0, #1.00000000
    // 0x801d10: fcmp            d0, d1
    // 0x801d14: b.lt            #0x801d2c
    // 0x801d18: ldur            x1, [fp, #-8]
    // 0x801d1c: r2 = Instance_GestureDisposition
    //     0x801d1c: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0x801d20: ldr             x2, [x2, #0xde8]
    // 0x801d24: r0 = resolve()
    //     0x801d24: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x801d28: b               #0x801d9c
    // 0x801d2c: ldur            x0, [fp, #-8]
    // 0x801d30: mov             x1, x0
    // 0x801d34: ldur            x2, [fp, #-0x10]
    // 0x801d38: r0 = addAllowedPointer()
    //     0x801d38: bl              #0x802a78  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::addAllowedPointer
    // 0x801d3c: ldur            x0, [fp, #-8]
    // 0x801d40: LoadField: r1 = r0->field_47
    //     0x801d40: ldur            w1, [x0, #0x47]
    // 0x801d44: DecompressPointer r1
    //     0x801d44: add             x1, x1, HEAP, lsl #32
    // 0x801d48: r16 = Instance__ForceState
    //     0x801d48: add             x16, PP, #0x56, lsl #12  ; [pp+0x564c8] Obj!_ForceState@e36ea1
    //     0x801d4c: ldr             x16, [x16, #0x4c8]
    // 0x801d50: cmp             w1, w16
    // 0x801d54: b.ne            #0x801d9c
    // 0x801d58: r1 = Instance__ForceState
    //     0x801d58: add             x1, PP, #0x59, lsl #12  ; [pp+0x59100] Obj!_ForceState@e36e61
    //     0x801d5c: ldr             x1, [x1, #0x100]
    // 0x801d60: StoreField: r0->field_47 = r1
    //     0x801d60: stur            w1, [x0, #0x47]
    // 0x801d64: r0 = OffsetPair()
    //     0x801d64: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x801d68: mov             x1, x0
    // 0x801d6c: ldur            x2, [fp, #-0x10]
    // 0x801d70: stur            x0, [fp, #-0x10]
    // 0x801d74: r0 = OffsetPair.fromEventPosition()
    //     0x801d74: bl              #0x75b878  ; [package:flutter/src/gestures/recognizer.dart] OffsetPair::OffsetPair.fromEventPosition
    // 0x801d78: ldur            x0, [fp, #-0x10]
    // 0x801d7c: ldur            x1, [fp, #-8]
    // 0x801d80: StoreField: r1->field_3f = r0
    //     0x801d80: stur            w0, [x1, #0x3f]
    //     0x801d84: ldurb           w16, [x1, #-1]
    //     0x801d88: ldurb           w17, [x0, #-1]
    //     0x801d8c: and             x16, x17, x16, lsr #2
    //     0x801d90: tst             x16, HEAP, lsr #32
    //     0x801d94: b.eq            #0x801d9c
    //     0x801d98: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x801d9c: r0 = Null
    //     0x801d9c: mov             x0, NULL
    // 0x801da0: LeaveFrame
    //     0x801da0: mov             SP, fp
    //     0x801da4: ldp             fp, lr, [SP], #0x10
    // 0x801da8: ret
    //     0x801da8: ret             
    // 0x801dac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x801dac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x801db0: b               #0x801cec
  }
  _ acceptGesture(/* No info */) {
    // ** addr: 0x86cc00, size: 0xbc
    // 0x86cc00: EnterFrame
    //     0x86cc00: stp             fp, lr, [SP, #-0x10]!
    //     0x86cc04: mov             fp, SP
    // 0x86cc08: AllocStack(0x20)
    //     0x86cc08: sub             SP, SP, #0x20
    // 0x86cc0c: SetupParameters(ForcePressGestureRecognizer this /* r1 => r1, fp-0x8 */)
    //     0x86cc0c: stur            x1, [fp, #-8]
    // 0x86cc10: CheckStackOverflow
    //     0x86cc10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86cc14: cmp             SP, x16
    //     0x86cc18: b.ls            #0x86ccb4
    // 0x86cc1c: r1 = 1
    //     0x86cc1c: movz            x1, #0x1
    // 0x86cc20: r0 = AllocateContext()
    //     0x86cc20: bl              #0xec126c  ; AllocateContextStub
    // 0x86cc24: mov             x1, x0
    // 0x86cc28: ldur            x0, [fp, #-8]
    // 0x86cc2c: StoreField: r1->field_f = r0
    //     0x86cc2c: stur            w0, [x1, #0xf]
    // 0x86cc30: LoadField: r2 = r0->field_47
    //     0x86cc30: ldur            w2, [x0, #0x47]
    // 0x86cc34: DecompressPointer r2
    //     0x86cc34: add             x2, x2, HEAP, lsl #32
    // 0x86cc38: r16 = Instance__ForceState
    //     0x86cc38: add             x16, PP, #0x59, lsl #12  ; [pp+0x59100] Obj!_ForceState@e36e61
    //     0x86cc3c: ldr             x16, [x16, #0x100]
    // 0x86cc40: cmp             w2, w16
    // 0x86cc44: b.ne            #0x86cc5c
    // 0x86cc48: r2 = Instance__ForceState
    //     0x86cc48: add             x2, PP, #0x59, lsl #12  ; [pp+0x59110] Obj!_ForceState@e36e21
    //     0x86cc4c: ldr             x2, [x2, #0x110]
    // 0x86cc50: StoreField: r0->field_47 = r2
    //     0x86cc50: stur            w2, [x0, #0x47]
    // 0x86cc54: r2 = Instance__ForceState
    //     0x86cc54: add             x2, PP, #0x59, lsl #12  ; [pp+0x59110] Obj!_ForceState@e36e21
    //     0x86cc58: ldr             x2, [x2, #0x110]
    // 0x86cc5c: LoadField: r3 = r0->field_23
    //     0x86cc5c: ldur            w3, [x0, #0x23]
    // 0x86cc60: DecompressPointer r3
    //     0x86cc60: add             x3, x3, HEAP, lsl #32
    // 0x86cc64: cmp             w3, NULL
    // 0x86cc68: b.eq            #0x86cca4
    // 0x86cc6c: r16 = Instance__ForceState
    //     0x86cc6c: add             x16, PP, #0x59, lsl #12  ; [pp+0x59108] Obj!_ForceState@e36e41
    //     0x86cc70: ldr             x16, [x16, #0x108]
    // 0x86cc74: cmp             w2, w16
    // 0x86cc78: b.ne            #0x86cca4
    // 0x86cc7c: mov             x2, x1
    // 0x86cc80: r1 = Function '<anonymous closure>':.
    //     0x86cc80: add             x1, PP, #0x59, lsl #12  ; [pp+0x59138] AnonymousClosure: (0x86ccbc), in [package:flutter/src/gestures/force_press.dart] ForcePressGestureRecognizer::acceptGesture (0x86cc00)
    //     0x86cc84: ldr             x1, [x1, #0x138]
    // 0x86cc88: r0 = AllocateClosure()
    //     0x86cc88: bl              #0xec1630  ; AllocateClosureStub
    // 0x86cc8c: r16 = <void?>
    //     0x86cc8c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x86cc90: ldur            lr, [fp, #-8]
    // 0x86cc94: stp             lr, x16, [SP, #8]
    // 0x86cc98: str             x0, [SP]
    // 0x86cc9c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x86cc9c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x86cca0: r0 = invokeCallback()
    //     0x86cca0: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x86cca4: r0 = Null
    //     0x86cca4: mov             x0, NULL
    // 0x86cca8: LeaveFrame
    //     0x86cca8: mov             SP, fp
    //     0x86ccac: ldp             fp, lr, [SP], #0x10
    // 0x86ccb0: ret
    //     0x86ccb0: ret             
    // 0x86ccb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86ccb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86ccb8: b               #0x86cc1c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x86ccbc, size: 0xd4
    // 0x86ccbc: EnterFrame
    //     0x86ccbc: stp             fp, lr, [SP, #-0x10]!
    //     0x86ccc0: mov             fp, SP
    // 0x86ccc4: AllocStack(0x10)
    //     0x86ccc4: sub             SP, SP, #0x10
    // 0x86ccc8: SetupParameters()
    //     0x86ccc8: ldr             x0, [fp, #0x10]
    //     0x86cccc: ldur            w1, [x0, #0x17]
    //     0x86ccd0: add             x1, x1, HEAP, lsl #32
    // 0x86ccd4: CheckStackOverflow
    //     0x86ccd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86ccd8: cmp             SP, x16
    //     0x86ccdc: b.ls            #0x86cd6c
    // 0x86cce0: LoadField: r0 = r1->field_f
    //     0x86cce0: ldur            w0, [x1, #0xf]
    // 0x86cce4: DecompressPointer r0
    //     0x86cce4: add             x0, x0, HEAP, lsl #32
    // 0x86cce8: LoadField: r1 = r0->field_23
    //     0x86cce8: ldur            w1, [x0, #0x23]
    // 0x86ccec: DecompressPointer r1
    //     0x86ccec: add             x1, x1, HEAP, lsl #32
    // 0x86ccf0: stur            x1, [fp, #-0x10]
    // 0x86ccf4: cmp             w1, NULL
    // 0x86ccf8: b.eq            #0x86cd74
    // 0x86ccfc: LoadField: r2 = r0->field_43
    //     0x86ccfc: ldur            w2, [x0, #0x43]
    // 0x86cd00: DecompressPointer r2
    //     0x86cd00: add             x2, x2, HEAP, lsl #32
    // 0x86cd04: r16 = Sentinel
    //     0x86cd04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x86cd08: cmp             w2, w16
    // 0x86cd0c: b.eq            #0x86cd78
    // 0x86cd10: LoadField: r2 = r0->field_3f
    //     0x86cd10: ldur            w2, [x0, #0x3f]
    // 0x86cd14: DecompressPointer r2
    //     0x86cd14: add             x2, x2, HEAP, lsl #32
    // 0x86cd18: r16 = Sentinel
    //     0x86cd18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x86cd1c: cmp             w2, w16
    // 0x86cd20: b.eq            #0x86cd84
    // 0x86cd24: LoadField: r0 = r2->field_b
    //     0x86cd24: ldur            w0, [x2, #0xb]
    // 0x86cd28: DecompressPointer r0
    //     0x86cd28: add             x0, x0, HEAP, lsl #32
    // 0x86cd2c: stur            x0, [fp, #-8]
    // 0x86cd30: r0 = ForcePressDetails()
    //     0x86cd30: bl              #0x75cc30  ; AllocateForcePressDetailsStub -> ForcePressDetails (size=0xc)
    // 0x86cd34: mov             x1, x0
    // 0x86cd38: ldur            x0, [fp, #-8]
    // 0x86cd3c: StoreField: r1->field_7 = r0
    //     0x86cd3c: stur            w0, [x1, #7]
    // 0x86cd40: ldur            x0, [fp, #-0x10]
    // 0x86cd44: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x86cd44: ldur            w2, [x0, #0x17]
    // 0x86cd48: DecompressPointer r2
    //     0x86cd48: add             x2, x2, HEAP, lsl #32
    // 0x86cd4c: mov             x16, x1
    // 0x86cd50: mov             x1, x2
    // 0x86cd54: mov             x2, x16
    // 0x86cd58: r0 = _forcePressStarted()
    //     0x86cd58: bl              #0x75baac  ; [package:flutter/src/widgets/text_selection.dart] _TextSelectionGestureDetectorState::_forcePressStarted
    // 0x86cd5c: r0 = Null
    //     0x86cd5c: mov             x0, NULL
    // 0x86cd60: LeaveFrame
    //     0x86cd60: mov             SP, fp
    //     0x86cd64: ldp             fp, lr, [SP], #0x10
    // 0x86cd68: ret
    //     0x86cd68: ret             
    // 0x86cd6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86cd6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86cd70: b               #0x86cce0
    // 0x86cd74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x86cd74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x86cd78: r9 = _lastPressure
    //     0x86cd78: add             x9, PP, #0x59, lsl #12  ; [pp+0x59140] Field <ForcePressGestureRecognizer._lastPressure@423518508>: late (offset: 0x44)
    //     0x86cd7c: ldr             x9, [x9, #0x140]
    // 0x86cd80: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x86cd80: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x86cd84: r9 = _lastPosition
    //     0x86cd84: add             x9, PP, #0x59, lsl #12  ; [pp+0x59120] Field <ForcePressGestureRecognizer._lastPosition@423518508>: late (offset: 0x40)
    //     0x86cd88: ldr             x9, [x9, #0x120]
    // 0x86cd8c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x86cd8c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ ForcePressGestureRecognizer(/* No info */) {
    // ** addr: 0xa22798, size: 0x74
    // 0xa22798: EnterFrame
    //     0xa22798: stp             fp, lr, [SP, #-0x10]!
    //     0xa2279c: mov             fp, SP
    // 0xa227a0: AllocStack(0x10)
    //     0xa227a0: sub             SP, SP, #0x10
    // 0xa227a4: r3 = Sentinel
    //     0xa227a4: ldr             x3, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa227a8: r2 = Instance__ForceState
    //     0xa227a8: add             x2, PP, #0x56, lsl #12  ; [pp+0x564c8] Obj!_ForceState@e36ea1
    //     0xa227ac: ldr             x2, [x2, #0x4c8]
    // 0xa227b0: r0 = Closure: (double, double, double) => double from Function '_inverseLerp@423518508': static.
    //     0xa227b0: add             x0, PP, #0x56, lsl #12  ; [pp+0x564d0] Closure: (double, double, double) => double from Function '_inverseLerp@423518508': static. (0x7e54fb15b6a8)
    //     0xa227b4: ldr             x0, [x0, #0x4d0]
    // 0xa227b8: d0 = 0.400000
    //     0xa227b8: ldr             d0, [PP, #0x64d8]  ; [pp+0x64d8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa227bc: CheckStackOverflow
    //     0xa227bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa227c0: cmp             SP, x16
    //     0xa227c4: b.ls            #0xa22804
    // 0xa227c8: StoreField: r1->field_3f = r3
    //     0xa227c8: stur            w3, [x1, #0x3f]
    // 0xa227cc: StoreField: r1->field_43 = r3
    //     0xa227cc: stur            w3, [x1, #0x43]
    // 0xa227d0: StoreField: r1->field_47 = r2
    //     0xa227d0: stur            w2, [x1, #0x47]
    // 0xa227d4: StoreField: r1->field_33 = d0
    //     0xa227d4: stur            d0, [x1, #0x33]
    // 0xa227d8: StoreField: r1->field_3b = r0
    //     0xa227d8: stur            w0, [x1, #0x3b]
    // 0xa227dc: r16 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static.
    //     0xa227dc: add             x16, PP, #0x25, lsl #12  ; [pp+0x253d8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static. (0x7e54fb8bbd8c)
    //     0xa227e0: ldr             x16, [x16, #0x3d8]
    // 0xa227e4: stp             x16, NULL, [SP]
    // 0xa227e8: r4 = const [0, 0x3, 0x2, 0x1, allowedButtonsFilter, 0x2, supportedDevices, 0x1, null]
    //     0xa227e8: add             x4, PP, #0x25, lsl #12  ; [pp+0x253c0] List(9) [0, 0x3, 0x2, 0x1, "allowedButtonsFilter", 0x2, "supportedDevices", 0x1, Null]
    //     0xa227ec: ldr             x4, [x4, #0x3c0]
    // 0xa227f0: r0 = OneSequenceGestureRecognizer()
    //     0xa227f0: bl              #0x763058  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::OneSequenceGestureRecognizer
    // 0xa227f4: r0 = Null
    //     0xa227f4: mov             x0, NULL
    // 0xa227f8: LeaveFrame
    //     0xa227f8: mov             SP, fp
    //     0xa227fc: ldp             fp, lr, [SP], #0x10
    // 0xa22800: ret
    //     0xa22800: ret             
    // 0xa22804: r0 = StackOverflowSharedWithFPURegs()
    //     0xa22804: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa22808: b               #0xa227c8
  }
  _ rejectGesture(/* No info */) {
    // ** addr: 0xcceb24, size: 0x6c
    // 0xcceb24: EnterFrame
    //     0xcceb24: stp             fp, lr, [SP, #-0x10]!
    //     0xcceb28: mov             fp, SP
    // 0xcceb2c: AllocStack(0x10)
    //     0xcceb2c: sub             SP, SP, #0x10
    // 0xcceb30: SetupParameters(ForcePressGestureRecognizer this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xcceb30: mov             x4, x1
    //     0xcceb34: mov             x3, x2
    //     0xcceb38: stur            x1, [fp, #-8]
    //     0xcceb3c: stur            x2, [fp, #-0x10]
    // 0xcceb40: CheckStackOverflow
    //     0xcceb40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcceb44: cmp             SP, x16
    //     0xcceb48: b.ls            #0xcceb88
    // 0xcceb4c: r0 = BoxInt64Instr(r3)
    //     0xcceb4c: sbfiz           x0, x3, #1, #0x1f
    //     0xcceb50: cmp             x3, x0, asr #1
    //     0xcceb54: b.eq            #0xcceb60
    //     0xcceb58: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcceb5c: stur            x3, [x0, #7]
    // 0xcceb60: mov             x1, x4
    // 0xcceb64: mov             x2, x0
    // 0xcceb68: r0 = stopTrackingPointer()
    //     0xcceb68: bl              #0x7587c8  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::stopTrackingPointer
    // 0xcceb6c: ldur            x1, [fp, #-8]
    // 0xcceb70: ldur            x2, [fp, #-0x10]
    // 0xcceb74: r0 = didStopTrackingLastPointer()
    //     0xcceb74: bl              #0x7d7210  ; [package:flutter/src/gestures/force_press.dart] ForcePressGestureRecognizer::didStopTrackingLastPointer
    // 0xcceb78: r0 = Null
    //     0xcceb78: mov             x0, NULL
    // 0xcceb7c: LeaveFrame
    //     0xcceb7c: mov             SP, fp
    //     0xcceb80: ldp             fp, lr, [SP], #0x10
    // 0xcceb84: ret
    //     0xcceb84: ret             
    // 0xcceb88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcceb88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcceb8c: b               #0xcceb4c
  }
}

// class id: 7088, size: 0x14, field offset: 0x14
enum _ForceState extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4861c, size: 0x64
    // 0xc4861c: EnterFrame
    //     0xc4861c: stp             fp, lr, [SP, #-0x10]!
    //     0xc48620: mov             fp, SP
    // 0xc48624: AllocStack(0x10)
    //     0xc48624: sub             SP, SP, #0x10
    // 0xc48628: SetupParameters(_ForceState this /* r1 => r0, fp-0x8 */)
    //     0xc48628: mov             x0, x1
    //     0xc4862c: stur            x1, [fp, #-8]
    // 0xc48630: CheckStackOverflow
    //     0xc48630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc48634: cmp             SP, x16
    //     0xc48638: b.ls            #0xc48678
    // 0xc4863c: r1 = Null
    //     0xc4863c: mov             x1, NULL
    // 0xc48640: r2 = 4
    //     0xc48640: movz            x2, #0x4
    // 0xc48644: r0 = AllocateArray()
    //     0xc48644: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc48648: r16 = "_ForceState."
    //     0xc48648: add             x16, PP, #0x59, lsl #12  ; [pp+0x59148] "_ForceState."
    //     0xc4864c: ldr             x16, [x16, #0x148]
    // 0xc48650: StoreField: r0->field_f = r16
    //     0xc48650: stur            w16, [x0, #0xf]
    // 0xc48654: ldur            x1, [fp, #-8]
    // 0xc48658: LoadField: r2 = r1->field_f
    //     0xc48658: ldur            w2, [x1, #0xf]
    // 0xc4865c: DecompressPointer r2
    //     0xc4865c: add             x2, x2, HEAP, lsl #32
    // 0xc48660: StoreField: r0->field_13 = r2
    //     0xc48660: stur            w2, [x0, #0x13]
    // 0xc48664: str             x0, [SP]
    // 0xc48668: r0 = _interpolate()
    //     0xc48668: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4866c: LeaveFrame
    //     0xc4866c: mov             SP, fp
    //     0xc48670: ldp             fp, lr, [SP], #0x10
    // 0xc48674: ret
    //     0xc48674: ret             
    // 0xc48678: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc48678: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4867c: b               #0xc4863c
  }
}
