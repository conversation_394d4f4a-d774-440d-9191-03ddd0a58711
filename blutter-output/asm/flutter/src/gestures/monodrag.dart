// lib: , url: package:flutter/src/gestures/monodrag.dart

// class id: 1048835, size: 0x8
class :: {
}

// class id: 3547, size: 0x90, field offset: 0x24
abstract class DragGestureRecognizer extends OneSequenceGestureRecognizer {

  late OffsetPair _pendingDragOffset; // offset: 0x5c
  late OffsetPair _initialPosition; // offset: 0x58
  late double _globalDistanceMoved; // offset: 0x70
  late OffsetPair _lastPosition; // offset: 0x60

  dynamic handleEvent(dynamic) {
    // ** addr: 0x758aec, size: 0x24
    // 0x758aec: EnterFrame
    //     0x758aec: stp             fp, lr, [SP, #-0x10]!
    //     0x758af0: mov             fp, SP
    // 0x758af4: ldr             x2, [fp, #0x10]
    // 0x758af8: r1 = Function 'handleEvent':.
    //     0x758af8: add             x1, PP, #0x39, lsl #12  ; [pp+0x39bc0] AnonymousClosure: (0x758b10), in [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::handleEvent (0x758b4c)
    //     0x758afc: ldr             x1, [x1, #0xbc0]
    // 0x758b00: r0 = AllocateClosure()
    //     0x758b00: bl              #0xec1630  ; AllocateClosureStub
    // 0x758b04: LeaveFrame
    //     0x758b04: mov             SP, fp
    //     0x758b08: ldp             fp, lr, [SP], #0x10
    // 0x758b0c: ret
    //     0x758b0c: ret             
  }
  [closure] void handleEvent(dynamic, PointerEvent) {
    // ** addr: 0x758b10, size: 0x3c
    // 0x758b10: EnterFrame
    //     0x758b10: stp             fp, lr, [SP, #-0x10]!
    //     0x758b14: mov             fp, SP
    // 0x758b18: ldr             x0, [fp, #0x18]
    // 0x758b1c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x758b1c: ldur            w1, [x0, #0x17]
    // 0x758b20: DecompressPointer r1
    //     0x758b20: add             x1, x1, HEAP, lsl #32
    // 0x758b24: CheckStackOverflow
    //     0x758b24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x758b28: cmp             SP, x16
    //     0x758b2c: b.ls            #0x758b44
    // 0x758b30: ldr             x2, [fp, #0x10]
    // 0x758b34: r0 = handleEvent()
    //     0x758b34: bl              #0x758b4c  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::handleEvent
    // 0x758b38: LeaveFrame
    //     0x758b38: mov             SP, fp
    //     0x758b3c: ldp             fp, lr, [SP], #0x10
    // 0x758b40: ret
    //     0x758b40: ret             
    // 0x758b44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x758b44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x758b48: b               #0x758b30
  }
  _ handleEvent(/* No info */) {
    // ** addr: 0x758b4c, size: 0xe60
    // 0x758b4c: EnterFrame
    //     0x758b4c: stp             fp, lr, [SP, #-0x10]!
    //     0x758b50: mov             fp, SP
    // 0x758b54: AllocStack(0x48)
    //     0x758b54: sub             SP, SP, #0x48
    // 0x758b58: SetupParameters(DragGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x758b58: mov             x3, x1
    //     0x758b5c: stur            x1, [fp, #-8]
    //     0x758b60: stur            x2, [fp, #-0x10]
    // 0x758b64: CheckStackOverflow
    //     0x758b64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x758b68: cmp             SP, x16
    //     0x758b6c: b.ls            #0x75996c
    // 0x758b70: r0 = LoadClassIdInstr(r2)
    //     0x758b70: ldur            x0, [x2, #-1]
    //     0x758b74: ubfx            x0, x0, #0xc, #0x14
    // 0x758b78: mov             x1, x2
    // 0x758b7c: r0 = GDT[cid_x0 + 0x131e0]()
    //     0x758b7c: movz            x17, #0x31e0
    //     0x758b80: movk            x17, #0x1, lsl #16
    //     0x758b84: add             lr, x0, x17
    //     0x758b88: ldr             lr, [x21, lr, lsl #3]
    //     0x758b8c: blr             lr
    // 0x758b90: tbz             w0, #4, #0x758e38
    // 0x758b94: ldur            x0, [fp, #-0x10]
    // 0x758b98: r2 = Null
    //     0x758b98: mov             x2, NULL
    // 0x758b9c: r1 = Null
    //     0x758b9c: mov             x1, NULL
    // 0x758ba0: cmp             w0, NULL
    // 0x758ba4: b.eq            #0x758bc4
    // 0x758ba8: branchIfSmi(r0, 0x758bc4)
    //     0x758ba8: tbz             w0, #0, #0x758bc4
    // 0x758bac: r3 = LoadClassIdInstr(r0)
    //     0x758bac: ldur            x3, [x0, #-1]
    //     0x758bb0: ubfx            x3, x3, #0xc, #0x14
    // 0x758bb4: cmp             x3, #0xda7
    // 0x758bb8: b.eq            #0x758bcc
    // 0x758bbc: cmp             x3, #0xfd1
    // 0x758bc0: b.eq            #0x758bcc
    // 0x758bc4: r0 = false
    //     0x758bc4: add             x0, NULL, #0x30  ; false
    // 0x758bc8: b               #0x758bd0
    // 0x758bcc: r0 = true
    //     0x758bcc: add             x0, NULL, #0x20  ; true
    // 0x758bd0: tbz             w0, #4, #0x758c94
    // 0x758bd4: ldur            x0, [fp, #-0x10]
    // 0x758bd8: r2 = Null
    //     0x758bd8: mov             x2, NULL
    // 0x758bdc: r1 = Null
    //     0x758bdc: mov             x1, NULL
    // 0x758be0: cmp             w0, NULL
    // 0x758be4: b.eq            #0x758c04
    // 0x758be8: branchIfSmi(r0, 0x758c04)
    //     0x758be8: tbz             w0, #0, #0x758c04
    // 0x758bec: r3 = LoadClassIdInstr(r0)
    //     0x758bec: ldur            x3, [x0, #-1]
    //     0x758bf0: ubfx            x3, x3, #0xc, #0x14
    // 0x758bf4: cmp             x3, #0xda5
    // 0x758bf8: b.eq            #0x758c0c
    // 0x758bfc: cmp             x3, #0xfcf
    // 0x758c00: b.eq            #0x758c0c
    // 0x758c04: r0 = false
    //     0x758c04: add             x0, NULL, #0x30  ; false
    // 0x758c08: b               #0x758c10
    // 0x758c0c: r0 = true
    //     0x758c0c: add             x0, NULL, #0x20  ; true
    // 0x758c10: tbz             w0, #4, #0x758c94
    // 0x758c14: ldur            x0, [fp, #-0x10]
    // 0x758c18: r2 = Null
    //     0x758c18: mov             x2, NULL
    // 0x758c1c: r1 = Null
    //     0x758c1c: mov             x1, NULL
    // 0x758c20: cmp             w0, NULL
    // 0x758c24: b.eq            #0x758c44
    // 0x758c28: branchIfSmi(r0, 0x758c44)
    //     0x758c28: tbz             w0, #0, #0x758c44
    // 0x758c2c: r3 = LoadClassIdInstr(r0)
    //     0x758c2c: ldur            x3, [x0, #-1]
    //     0x758c30: ubfx            x3, x3, #0xc, #0x14
    // 0x758c34: cmp             x3, #0xd99
    // 0x758c38: b.eq            #0x758c4c
    // 0x758c3c: cmp             x3, #0xfcb
    // 0x758c40: b.eq            #0x758c4c
    // 0x758c44: r0 = false
    //     0x758c44: add             x0, NULL, #0x30  ; false
    // 0x758c48: b               #0x758c50
    // 0x758c4c: r0 = true
    //     0x758c4c: add             x0, NULL, #0x20  ; true
    // 0x758c50: tbz             w0, #4, #0x758c94
    // 0x758c54: ldur            x0, [fp, #-0x10]
    // 0x758c58: r2 = Null
    //     0x758c58: mov             x2, NULL
    // 0x758c5c: r1 = Null
    //     0x758c5c: mov             x1, NULL
    // 0x758c60: cmp             w0, NULL
    // 0x758c64: b.eq            #0x758c84
    // 0x758c68: branchIfSmi(r0, 0x758c84)
    //     0x758c68: tbz             w0, #0, #0x758c84
    // 0x758c6c: r3 = LoadClassIdInstr(r0)
    //     0x758c6c: ldur            x3, [x0, #-1]
    //     0x758c70: ubfx            x3, x3, #0xc, #0x14
    // 0x758c74: cmp             x3, #0xd97
    // 0x758c78: b.eq            #0x758c8c
    // 0x758c7c: cmp             x3, #0xfc9
    // 0x758c80: b.eq            #0x758c8c
    // 0x758c84: r0 = false
    //     0x758c84: add             x0, NULL, #0x30  ; false
    // 0x758c88: b               #0x758c90
    // 0x758c8c: r0 = true
    //     0x758c8c: add             x0, NULL, #0x20  ; true
    // 0x758c90: tbnz            w0, #4, #0x758e38
    // 0x758c94: ldur            x0, [fp, #-0x10]
    // 0x758c98: r2 = Null
    //     0x758c98: mov             x2, NULL
    // 0x758c9c: r1 = Null
    //     0x758c9c: mov             x1, NULL
    // 0x758ca0: cmp             w0, NULL
    // 0x758ca4: b.eq            #0x758cc4
    // 0x758ca8: branchIfSmi(r0, 0x758cc4)
    //     0x758ca8: tbz             w0, #0, #0x758cc4
    // 0x758cac: r3 = LoadClassIdInstr(r0)
    //     0x758cac: ldur            x3, [x0, #-1]
    //     0x758cb0: ubfx            x3, x3, #0xc, #0x14
    // 0x758cb4: cmp             x3, #0xd99
    // 0x758cb8: b.eq            #0x758ccc
    // 0x758cbc: cmp             x3, #0xfcb
    // 0x758cc0: b.eq            #0x758ccc
    // 0x758cc4: r0 = false
    //     0x758cc4: add             x0, NULL, #0x30  ; false
    // 0x758cc8: b               #0x758cd0
    // 0x758ccc: r0 = true
    //     0x758ccc: add             x0, NULL, #0x20  ; true
    // 0x758cd0: tbnz            w0, #4, #0x758cdc
    // 0x758cd4: r4 = Instance_Offset
    //     0x758cd4: ldr             x4, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x758cd8: b               #0x758d68
    // 0x758cdc: ldur            x0, [fp, #-0x10]
    // 0x758ce0: r2 = Null
    //     0x758ce0: mov             x2, NULL
    // 0x758ce4: r1 = Null
    //     0x758ce4: mov             x1, NULL
    // 0x758ce8: cmp             w0, NULL
    // 0x758cec: b.eq            #0x758d0c
    // 0x758cf0: branchIfSmi(r0, 0x758d0c)
    //     0x758cf0: tbz             w0, #0, #0x758d0c
    // 0x758cf4: r3 = LoadClassIdInstr(r0)
    //     0x758cf4: ldur            x3, [x0, #-1]
    //     0x758cf8: ubfx            x3, x3, #0xc, #0x14
    // 0x758cfc: cmp             x3, #0xd97
    // 0x758d00: b.eq            #0x758d14
    // 0x758d04: cmp             x3, #0xfc9
    // 0x758d08: b.eq            #0x758d14
    // 0x758d0c: r0 = false
    //     0x758d0c: add             x0, NULL, #0x30  ; false
    // 0x758d10: b               #0x758d18
    // 0x758d14: r0 = true
    //     0x758d14: add             x0, NULL, #0x20  ; true
    // 0x758d18: tbnz            w0, #4, #0x758d40
    // 0x758d1c: ldur            x2, [fp, #-0x10]
    // 0x758d20: r0 = LoadClassIdInstr(r2)
    //     0x758d20: ldur            x0, [x2, #-1]
    //     0x758d24: ubfx            x0, x0, #0xc, #0x14
    // 0x758d28: mov             x1, x2
    // 0x758d2c: r0 = GDT[cid_x0 + -0xfff]()
    //     0x758d2c: sub             lr, x0, #0xfff
    //     0x758d30: ldr             lr, [x21, lr, lsl #3]
    //     0x758d34: blr             lr
    // 0x758d38: mov             x4, x0
    // 0x758d3c: b               #0x758d68
    // 0x758d40: ldur            x2, [fp, #-0x10]
    // 0x758d44: r0 = LoadClassIdInstr(r2)
    //     0x758d44: ldur            x0, [x2, #-1]
    //     0x758d48: ubfx            x0, x0, #0xc, #0x14
    // 0x758d4c: mov             x1, x2
    // 0x758d50: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x758d50: movz            x17, #0x30fa
    //     0x758d54: movk            x17, #0x1, lsl #16
    //     0x758d58: add             lr, x0, x17
    //     0x758d5c: ldr             lr, [x21, lr, lsl #3]
    //     0x758d60: blr             lr
    // 0x758d64: mov             x4, x0
    // 0x758d68: ldur            x3, [fp, #-8]
    // 0x758d6c: ldur            x2, [fp, #-0x10]
    // 0x758d70: stur            x4, [fp, #-0x20]
    // 0x758d74: LoadField: r5 = r3->field_77
    //     0x758d74: ldur            w5, [x3, #0x77]
    // 0x758d78: DecompressPointer r5
    //     0x758d78: add             x5, x5, HEAP, lsl #32
    // 0x758d7c: stur            x5, [fp, #-0x18]
    // 0x758d80: r0 = LoadClassIdInstr(r2)
    //     0x758d80: ldur            x0, [x2, #-1]
    //     0x758d84: ubfx            x0, x0, #0xc, #0x14
    // 0x758d88: mov             x1, x2
    // 0x758d8c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x758d8c: sub             lr, x0, #1, lsl #12
    //     0x758d90: ldr             lr, [x21, lr, lsl #3]
    //     0x758d94: blr             lr
    // 0x758d98: mov             x2, x0
    // 0x758d9c: r0 = BoxInt64Instr(r2)
    //     0x758d9c: sbfiz           x0, x2, #1, #0x1f
    //     0x758da0: cmp             x2, x0, asr #1
    //     0x758da4: b.eq            #0x758db0
    //     0x758da8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x758dac: stur            x2, [x0, #7]
    // 0x758db0: ldur            x1, [fp, #-0x18]
    // 0x758db4: mov             x2, x0
    // 0x758db8: r0 = _getValueOrData()
    //     0x758db8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x758dbc: mov             x1, x0
    // 0x758dc0: ldur            x0, [fp, #-0x18]
    // 0x758dc4: LoadField: r2 = r0->field_f
    //     0x758dc4: ldur            w2, [x0, #0xf]
    // 0x758dc8: DecompressPointer r2
    //     0x758dc8: add             x2, x2, HEAP, lsl #32
    // 0x758dcc: cmp             w2, w1
    // 0x758dd0: b.ne            #0x758ddc
    // 0x758dd4: r3 = Null
    //     0x758dd4: mov             x3, NULL
    // 0x758dd8: b               #0x758de0
    // 0x758ddc: mov             x3, x1
    // 0x758de0: ldur            x2, [fp, #-0x10]
    // 0x758de4: stur            x3, [fp, #-0x18]
    // 0x758de8: cmp             w3, NULL
    // 0x758dec: b.eq            #0x759974
    // 0x758df0: r0 = LoadClassIdInstr(r2)
    //     0x758df0: ldur            x0, [x2, #-1]
    //     0x758df4: ubfx            x0, x0, #0xc, #0x14
    // 0x758df8: mov             x1, x2
    // 0x758dfc: r0 = GDT[cid_x0 + 0x130f9]()
    //     0x758dfc: movz            x17, #0x30f9
    //     0x758e00: movk            x17, #0x1, lsl #16
    //     0x758e04: add             lr, x0, x17
    //     0x758e08: ldr             lr, [x21, lr, lsl #3]
    //     0x758e0c: blr             lr
    // 0x758e10: ldur            x1, [fp, #-0x18]
    // 0x758e14: r2 = LoadClassIdInstr(r1)
    //     0x758e14: ldur            x2, [x1, #-1]
    //     0x758e18: ubfx            x2, x2, #0xc, #0x14
    // 0x758e1c: mov             x16, x0
    // 0x758e20: mov             x0, x2
    // 0x758e24: mov             x2, x16
    // 0x758e28: ldur            x3, [fp, #-0x20]
    // 0x758e2c: r0 = GDT[cid_x0 + -0xff8]()
    //     0x758e2c: sub             lr, x0, #0xff8
    //     0x758e30: ldr             lr, [x21, lr, lsl #3]
    //     0x758e34: blr             lr
    // 0x758e38: ldur            x0, [fp, #-0x10]
    // 0x758e3c: r2 = Null
    //     0x758e3c: mov             x2, NULL
    // 0x758e40: r1 = Null
    //     0x758e40: mov             x1, NULL
    // 0x758e44: cmp             w0, NULL
    // 0x758e48: b.eq            #0x758e68
    // 0x758e4c: branchIfSmi(r0, 0x758e68)
    //     0x758e4c: tbz             w0, #0, #0x758e68
    // 0x758e50: r3 = LoadClassIdInstr(r0)
    //     0x758e50: ldur            x3, [x0, #-1]
    //     0x758e54: ubfx            x3, x3, #0xc, #0x14
    // 0x758e58: cmp             x3, #0xda5
    // 0x758e5c: b.eq            #0x758e70
    // 0x758e60: cmp             x3, #0xfcf
    // 0x758e64: b.eq            #0x758e70
    // 0x758e68: r0 = false
    //     0x758e68: add             x0, NULL, #0x30  ; false
    // 0x758e6c: b               #0x758e74
    // 0x758e70: r0 = true
    //     0x758e70: add             x0, NULL, #0x20  ; true
    // 0x758e74: tbnz            w0, #4, #0x758f44
    // 0x758e78: ldur            x3, [fp, #-8]
    // 0x758e7c: ldur            x2, [fp, #-0x10]
    // 0x758e80: r0 = LoadClassIdInstr(r2)
    //     0x758e80: ldur            x0, [x2, #-1]
    //     0x758e84: ubfx            x0, x0, #0xc, #0x14
    // 0x758e88: mov             x1, x2
    // 0x758e8c: r0 = GDT[cid_x0 + 0x12cee]()
    //     0x758e8c: movz            x17, #0x2cee
    //     0x758e90: movk            x17, #0x1, lsl #16
    //     0x758e94: add             lr, x0, x17
    //     0x758e98: ldr             lr, [x21, lr, lsl #3]
    //     0x758e9c: blr             lr
    // 0x758ea0: mov             x3, x0
    // 0x758ea4: ldur            x2, [fp, #-8]
    // 0x758ea8: LoadField: r4 = r2->field_67
    //     0x758ea8: ldur            w4, [x2, #0x67]
    // 0x758eac: DecompressPointer r4
    //     0x758eac: add             x4, x4, HEAP, lsl #32
    // 0x758eb0: r0 = BoxInt64Instr(r3)
    //     0x758eb0: sbfiz           x0, x3, #1, #0x1f
    //     0x758eb4: cmp             x3, x0, asr #1
    //     0x758eb8: b.eq            #0x758ec4
    //     0x758ebc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x758ec0: stur            x3, [x0, #7]
    // 0x758ec4: cmp             w0, w4
    // 0x758ec8: b.eq            #0x758f3c
    // 0x758ecc: and             w16, w0, w4
    // 0x758ed0: branchIfSmi(r16, 0x758f04)
    //     0x758ed0: tbz             w16, #0, #0x758f04
    // 0x758ed4: r16 = LoadClassIdInstr(r0)
    //     0x758ed4: ldur            x16, [x0, #-1]
    //     0x758ed8: ubfx            x16, x16, #0xc, #0x14
    // 0x758edc: cmp             x16, #0x3d
    // 0x758ee0: b.ne            #0x758f04
    // 0x758ee4: r16 = LoadClassIdInstr(r4)
    //     0x758ee4: ldur            x16, [x4, #-1]
    //     0x758ee8: ubfx            x16, x16, #0xc, #0x14
    // 0x758eec: cmp             x16, #0x3d
    // 0x758ef0: b.ne            #0x758f04
    // 0x758ef4: LoadField: r16 = r0->field_7
    //     0x758ef4: ldur            x16, [x0, #7]
    // 0x758ef8: LoadField: r17 = r4->field_7
    //     0x758ef8: ldur            x17, [x4, #7]
    // 0x758efc: cmp             x16, x17
    // 0x758f00: b.eq            #0x758f3c
    // 0x758f04: ldur            x3, [fp, #-0x10]
    // 0x758f08: r0 = LoadClassIdInstr(r3)
    //     0x758f08: ldur            x0, [x3, #-1]
    //     0x758f0c: ubfx            x0, x0, #0xc, #0x14
    // 0x758f10: mov             x1, x3
    // 0x758f14: r0 = GDT[cid_x0 + -0x1000]()
    //     0x758f14: sub             lr, x0, #1, lsl #12
    //     0x758f18: ldr             lr, [x21, lr, lsl #3]
    //     0x758f1c: blr             lr
    // 0x758f20: ldur            x1, [fp, #-8]
    // 0x758f24: mov             x2, x0
    // 0x758f28: r0 = _giveUpPointer()
    //     0x758f28: bl              #0x75afec  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_giveUpPointer
    // 0x758f2c: r0 = Null
    //     0x758f2c: mov             x0, NULL
    // 0x758f30: LeaveFrame
    //     0x758f30: mov             SP, fp
    //     0x758f34: ldp             fp, lr, [SP], #0x10
    // 0x758f38: ret
    //     0x758f38: ret             
    // 0x758f3c: ldur            x3, [fp, #-0x10]
    // 0x758f40: b               #0x758f48
    // 0x758f44: ldur            x3, [fp, #-0x10]
    // 0x758f48: mov             x0, x3
    // 0x758f4c: r2 = Null
    //     0x758f4c: mov             x2, NULL
    // 0x758f50: r1 = Null
    //     0x758f50: mov             x1, NULL
    // 0x758f54: cmp             w0, NULL
    // 0x758f58: b.eq            #0x758f78
    // 0x758f5c: branchIfSmi(r0, 0x758f78)
    //     0x758f5c: tbz             w0, #0, #0x758f78
    // 0x758f60: r3 = LoadClassIdInstr(r0)
    //     0x758f60: ldur            x3, [x0, #-1]
    //     0x758f64: ubfx            x3, x3, #0xc, #0x14
    // 0x758f68: cmp             x3, #0xda5
    // 0x758f6c: b.eq            #0x758f80
    // 0x758f70: cmp             x3, #0xfcf
    // 0x758f74: b.eq            #0x758f80
    // 0x758f78: r0 = false
    //     0x758f78: add             x0, NULL, #0x30  ; false
    // 0x758f7c: b               #0x758f84
    // 0x758f80: r0 = true
    //     0x758f80: add             x0, NULL, #0x20  ; true
    // 0x758f84: tbz             w0, #4, #0x758fc8
    // 0x758f88: ldur            x0, [fp, #-0x10]
    // 0x758f8c: r2 = Null
    //     0x758f8c: mov             x2, NULL
    // 0x758f90: r1 = Null
    //     0x758f90: mov             x1, NULL
    // 0x758f94: cmp             w0, NULL
    // 0x758f98: b.eq            #0x758fb8
    // 0x758f9c: branchIfSmi(r0, 0x758fb8)
    //     0x758f9c: tbz             w0, #0, #0x758fb8
    // 0x758fa0: r3 = LoadClassIdInstr(r0)
    //     0x758fa0: ldur            x3, [x0, #-1]
    //     0x758fa4: ubfx            x3, x3, #0xc, #0x14
    // 0x758fa8: cmp             x3, #0xd97
    // 0x758fac: b.eq            #0x758fc0
    // 0x758fb0: cmp             x3, #0xfc9
    // 0x758fb4: b.eq            #0x758fc0
    // 0x758fb8: r0 = false
    //     0x758fb8: add             x0, NULL, #0x30  ; false
    // 0x758fbc: b               #0x758fc4
    // 0x758fc0: r0 = true
    //     0x758fc0: add             x0, NULL, #0x20  ; true
    // 0x758fc4: tbnz            w0, #4, #0x759878
    // 0x758fc8: ldur            x3, [fp, #-8]
    // 0x758fcc: ldur            x2, [fp, #-0x10]
    // 0x758fd0: r0 = LoadClassIdInstr(r2)
    //     0x758fd0: ldur            x0, [x2, #-1]
    //     0x758fd4: ubfx            x0, x0, #0xc, #0x14
    // 0x758fd8: mov             x1, x2
    // 0x758fdc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x758fdc: sub             lr, x0, #1, lsl #12
    //     0x758fe0: ldr             lr, [x21, lr, lsl #3]
    //     0x758fe4: blr             lr
    // 0x758fe8: ldur            x3, [fp, #-8]
    // 0x758fec: LoadField: r1 = r3->field_27
    //     0x758fec: ldur            w1, [x3, #0x27]
    // 0x758ff0: DecompressPointer r1
    //     0x758ff0: add             x1, x1, HEAP, lsl #32
    // 0x758ff4: LoadField: r2 = r1->field_7
    //     0x758ff4: ldur            x2, [x1, #7]
    // 0x758ff8: cmp             x2, #1
    // 0x758ffc: b.gt            #0x75902c
    // 0x759000: cmp             x2, #0
    // 0x759004: b.gt            #0x75902c
    // 0x759008: LoadField: r1 = r3->field_8b
    //     0x759008: ldur            w1, [x3, #0x8b]
    // 0x75900c: DecompressPointer r1
    //     0x75900c: add             x1, x1, HEAP, lsl #32
    // 0x759010: cmp             w1, NULL
    // 0x759014: b.eq            #0x75902c
    // 0x759018: r2 = LoadInt32Instr(r1)
    //     0x759018: sbfx            x2, x1, #1, #0x1f
    //     0x75901c: tbz             w1, #0, #0x759024
    //     0x759020: ldur            x2, [x1, #7]
    // 0x759024: cmp             x0, x2
    // 0x759028: b.ne            #0x759878
    // 0x75902c: ldur            x0, [fp, #-0x10]
    // 0x759030: r2 = Null
    //     0x759030: mov             x2, NULL
    // 0x759034: r1 = Null
    //     0x759034: mov             x1, NULL
    // 0x759038: cmp             w0, NULL
    // 0x75903c: b.eq            #0x75905c
    // 0x759040: branchIfSmi(r0, 0x75905c)
    //     0x759040: tbz             w0, #0, #0x75905c
    // 0x759044: r3 = LoadClassIdInstr(r0)
    //     0x759044: ldur            x3, [x0, #-1]
    //     0x759048: ubfx            x3, x3, #0xc, #0x14
    // 0x75904c: cmp             x3, #0xda5
    // 0x759050: b.eq            #0x759064
    // 0x759054: cmp             x3, #0xfcf
    // 0x759058: b.eq            #0x759064
    // 0x75905c: r0 = false
    //     0x75905c: add             x0, NULL, #0x30  ; false
    // 0x759060: b               #0x759068
    // 0x759064: r0 = true
    //     0x759064: add             x0, NULL, #0x20  ; true
    // 0x759068: tbnz            w0, #4, #0x759098
    // 0x75906c: ldur            x2, [fp, #-0x10]
    // 0x759070: r0 = LoadClassIdInstr(r2)
    //     0x759070: ldur            x0, [x2, #-1]
    //     0x759074: ubfx            x0, x0, #0xc, #0x14
    // 0x759078: mov             x1, x2
    // 0x75907c: r0 = GDT[cid_x0 + 0x1326d]()
    //     0x75907c: movz            x17, #0x326d
    //     0x759080: movk            x17, #0x1, lsl #16
    //     0x759084: add             lr, x0, x17
    //     0x759088: ldr             lr, [x21, lr, lsl #3]
    //     0x75908c: blr             lr
    // 0x759090: mov             x3, x0
    // 0x759094: b               #0x7590f4
    // 0x759098: ldur            x3, [fp, #-0x10]
    // 0x75909c: mov             x0, x3
    // 0x7590a0: r2 = Null
    //     0x7590a0: mov             x2, NULL
    // 0x7590a4: r1 = Null
    //     0x7590a4: mov             x1, NULL
    // 0x7590a8: r4 = LoadClassIdInstr(r0)
    //     0x7590a8: ldur            x4, [x0, #-1]
    //     0x7590ac: ubfx            x4, x4, #0xc, #0x14
    // 0x7590b0: cmp             x4, #0xd97
    // 0x7590b4: b.eq            #0x7590d4
    // 0x7590b8: cmp             x4, #0xfc9
    // 0x7590bc: b.eq            #0x7590d4
    // 0x7590c0: r8 = PointerPanZoomUpdateEvent
    //     0x7590c0: add             x8, PP, #0x39, lsl #12  ; [pp+0x39bc8] Type: PointerPanZoomUpdateEvent
    //     0x7590c4: ldr             x8, [x8, #0xbc8]
    // 0x7590c8: r3 = Null
    //     0x7590c8: add             x3, PP, #0x39, lsl #12  ; [pp+0x39bd0] Null
    //     0x7590cc: ldr             x3, [x3, #0xbd0]
    // 0x7590d0: r0 = DefaultTypeTest()
    //     0x7590d0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7590d4: ldur            x2, [fp, #-0x10]
    // 0x7590d8: r0 = LoadClassIdInstr(r2)
    //     0x7590d8: ldur            x0, [x2, #-1]
    //     0x7590dc: ubfx            x0, x0, #0xc, #0x14
    // 0x7590e0: mov             x1, x2
    // 0x7590e4: r0 = GDT[cid_x0 + -0x63e]()
    //     0x7590e4: sub             lr, x0, #0x63e
    //     0x7590e8: ldr             lr, [x21, lr, lsl #3]
    //     0x7590ec: blr             lr
    // 0x7590f0: mov             x3, x0
    // 0x7590f4: ldur            x0, [fp, #-0x10]
    // 0x7590f8: stur            x3, [fp, #-0x18]
    // 0x7590fc: r2 = Null
    //     0x7590fc: mov             x2, NULL
    // 0x759100: r1 = Null
    //     0x759100: mov             x1, NULL
    // 0x759104: cmp             w0, NULL
    // 0x759108: b.eq            #0x759128
    // 0x75910c: branchIfSmi(r0, 0x759128)
    //     0x75910c: tbz             w0, #0, #0x759128
    // 0x759110: r3 = LoadClassIdInstr(r0)
    //     0x759110: ldur            x3, [x0, #-1]
    //     0x759114: ubfx            x3, x3, #0xc, #0x14
    // 0x759118: cmp             x3, #0xda5
    // 0x75911c: b.eq            #0x759130
    // 0x759120: cmp             x3, #0xfcf
    // 0x759124: b.eq            #0x759130
    // 0x759128: r0 = false
    //     0x759128: add             x0, NULL, #0x30  ; false
    // 0x75912c: b               #0x759134
    // 0x759130: r0 = true
    //     0x759130: add             x0, NULL, #0x20  ; true
    // 0x759134: tbnz            w0, #4, #0x759160
    // 0x759138: ldur            x2, [fp, #-0x10]
    // 0x75913c: r0 = LoadClassIdInstr(r2)
    //     0x75913c: ldur            x0, [x2, #-1]
    //     0x759140: ubfx            x0, x0, #0xc, #0x14
    // 0x759144: mov             x1, x2
    // 0x759148: r0 = GDT[cid_x0 + 0x47d8]()
    //     0x759148: movz            x17, #0x47d8
    //     0x75914c: add             lr, x0, x17
    //     0x759150: ldr             lr, [x21, lr, lsl #3]
    //     0x759154: blr             lr
    // 0x759158: mov             x3, x0
    // 0x75915c: b               #0x7591bc
    // 0x759160: ldur            x3, [fp, #-0x10]
    // 0x759164: mov             x0, x3
    // 0x759168: r2 = Null
    //     0x759168: mov             x2, NULL
    // 0x75916c: r1 = Null
    //     0x75916c: mov             x1, NULL
    // 0x759170: r4 = LoadClassIdInstr(r0)
    //     0x759170: ldur            x4, [x0, #-1]
    //     0x759174: ubfx            x4, x4, #0xc, #0x14
    // 0x759178: cmp             x4, #0xd97
    // 0x75917c: b.eq            #0x75919c
    // 0x759180: cmp             x4, #0xfc9
    // 0x759184: b.eq            #0x75919c
    // 0x759188: r8 = PointerPanZoomUpdateEvent
    //     0x759188: add             x8, PP, #0x39, lsl #12  ; [pp+0x39bc8] Type: PointerPanZoomUpdateEvent
    //     0x75918c: ldr             x8, [x8, #0xbc8]
    // 0x759190: r3 = Null
    //     0x759190: add             x3, PP, #0x39, lsl #12  ; [pp+0x39be0] Null
    //     0x759194: ldr             x3, [x3, #0xbe0]
    // 0x759198: r0 = DefaultTypeTest()
    //     0x759198: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x75919c: ldur            x2, [fp, #-0x10]
    // 0x7591a0: r0 = LoadClassIdInstr(r2)
    //     0x7591a0: ldur            x0, [x2, #-1]
    //     0x7591a4: ubfx            x0, x0, #0xc, #0x14
    // 0x7591a8: mov             x1, x2
    // 0x7591ac: r0 = GDT[cid_x0 + -0xff3]()
    //     0x7591ac: sub             lr, x0, #0xff3
    //     0x7591b0: ldr             lr, [x21, lr, lsl #3]
    //     0x7591b4: blr             lr
    // 0x7591b8: mov             x3, x0
    // 0x7591bc: ldur            x0, [fp, #-0x10]
    // 0x7591c0: stur            x3, [fp, #-0x20]
    // 0x7591c4: r2 = Null
    //     0x7591c4: mov             x2, NULL
    // 0x7591c8: r1 = Null
    //     0x7591c8: mov             x1, NULL
    // 0x7591cc: cmp             w0, NULL
    // 0x7591d0: b.eq            #0x7591f0
    // 0x7591d4: branchIfSmi(r0, 0x7591f0)
    //     0x7591d4: tbz             w0, #0, #0x7591f0
    // 0x7591d8: r3 = LoadClassIdInstr(r0)
    //     0x7591d8: ldur            x3, [x0, #-1]
    //     0x7591dc: ubfx            x3, x3, #0xc, #0x14
    // 0x7591e0: cmp             x3, #0xda5
    // 0x7591e4: b.eq            #0x7591f8
    // 0x7591e8: cmp             x3, #0xfcf
    // 0x7591ec: b.eq            #0x7591f8
    // 0x7591f0: r0 = false
    //     0x7591f0: add             x0, NULL, #0x30  ; false
    // 0x7591f4: b               #0x7591fc
    // 0x7591f8: r0 = true
    //     0x7591f8: add             x0, NULL, #0x20  ; true
    // 0x7591fc: tbnz            w0, #4, #0x759224
    // 0x759200: ldur            x2, [fp, #-0x10]
    // 0x759204: r0 = LoadClassIdInstr(r2)
    //     0x759204: ldur            x0, [x2, #-1]
    //     0x759208: ubfx            x0, x0, #0xc, #0x14
    // 0x75920c: mov             x1, x2
    // 0x759210: r0 = GDT[cid_x0 + -0x1]()
    //     0x759210: sub             lr, x0, #1
    //     0x759214: ldr             lr, [x21, lr, lsl #3]
    //     0x759218: blr             lr
    // 0x75921c: mov             x3, x0
    // 0x759220: b               #0x7592ac
    // 0x759224: ldur            x2, [fp, #-0x10]
    // 0x759228: r0 = LoadClassIdInstr(r2)
    //     0x759228: ldur            x0, [x2, #-1]
    //     0x75922c: ubfx            x0, x0, #0xc, #0x14
    // 0x759230: mov             x1, x2
    // 0x759234: r0 = GDT[cid_x0 + -0x1]()
    //     0x759234: sub             lr, x0, #1
    //     0x759238: ldr             lr, [x21, lr, lsl #3]
    //     0x75923c: blr             lr
    // 0x759240: mov             x3, x0
    // 0x759244: ldur            x0, [fp, #-0x10]
    // 0x759248: r2 = Null
    //     0x759248: mov             x2, NULL
    // 0x75924c: r1 = Null
    //     0x75924c: mov             x1, NULL
    // 0x759250: stur            x3, [fp, #-0x28]
    // 0x759254: r4 = LoadClassIdInstr(r0)
    //     0x759254: ldur            x4, [x0, #-1]
    //     0x759258: ubfx            x4, x4, #0xc, #0x14
    // 0x75925c: cmp             x4, #0xd97
    // 0x759260: b.eq            #0x759280
    // 0x759264: cmp             x4, #0xfc9
    // 0x759268: b.eq            #0x759280
    // 0x75926c: r8 = PointerPanZoomUpdateEvent
    //     0x75926c: add             x8, PP, #0x39, lsl #12  ; [pp+0x39bc8] Type: PointerPanZoomUpdateEvent
    //     0x759270: ldr             x8, [x8, #0xbc8]
    // 0x759274: r3 = Null
    //     0x759274: add             x3, PP, #0x39, lsl #12  ; [pp+0x39bf0] Null
    //     0x759278: ldr             x3, [x3, #0xbf0]
    // 0x75927c: r0 = DefaultTypeTest()
    //     0x75927c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x759280: ldur            x2, [fp, #-0x10]
    // 0x759284: r0 = LoadClassIdInstr(r2)
    //     0x759284: ldur            x0, [x2, #-1]
    //     0x759288: ubfx            x0, x0, #0xc, #0x14
    // 0x75928c: mov             x1, x2
    // 0x759290: r0 = GDT[cid_x0 + -0xfff]()
    //     0x759290: sub             lr, x0, #0xfff
    //     0x759294: ldr             lr, [x21, lr, lsl #3]
    //     0x759298: blr             lr
    // 0x75929c: ldur            x1, [fp, #-0x28]
    // 0x7592a0: mov             x2, x0
    // 0x7592a4: r0 = +()
    //     0x7592a4: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x7592a8: mov             x3, x0
    // 0x7592ac: ldur            x0, [fp, #-0x10]
    // 0x7592b0: stur            x3, [fp, #-0x28]
    // 0x7592b4: r2 = Null
    //     0x7592b4: mov             x2, NULL
    // 0x7592b8: r1 = Null
    //     0x7592b8: mov             x1, NULL
    // 0x7592bc: cmp             w0, NULL
    // 0x7592c0: b.eq            #0x7592e0
    // 0x7592c4: branchIfSmi(r0, 0x7592e0)
    //     0x7592c4: tbz             w0, #0, #0x7592e0
    // 0x7592c8: r3 = LoadClassIdInstr(r0)
    //     0x7592c8: ldur            x3, [x0, #-1]
    //     0x7592cc: ubfx            x3, x3, #0xc, #0x14
    // 0x7592d0: cmp             x3, #0xda5
    // 0x7592d4: b.eq            #0x7592e8
    // 0x7592d8: cmp             x3, #0xfcf
    // 0x7592dc: b.eq            #0x7592e8
    // 0x7592e0: r0 = false
    //     0x7592e0: add             x0, NULL, #0x30  ; false
    // 0x7592e4: b               #0x7592ec
    // 0x7592e8: r0 = true
    //     0x7592e8: add             x0, NULL, #0x20  ; true
    // 0x7592ec: tbnz            w0, #4, #0x75931c
    // 0x7592f0: ldur            x2, [fp, #-0x10]
    // 0x7592f4: r0 = LoadClassIdInstr(r2)
    //     0x7592f4: ldur            x0, [x2, #-1]
    //     0x7592f8: ubfx            x0, x0, #0xc, #0x14
    // 0x7592fc: mov             x1, x2
    // 0x759300: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x759300: movz            x17, #0x30fa
    //     0x759304: movk            x17, #0x1, lsl #16
    //     0x759308: add             lr, x0, x17
    //     0x75930c: ldr             lr, [x21, lr, lsl #3]
    //     0x759310: blr             lr
    // 0x759314: mov             x2, x0
    // 0x759318: b               #0x7593ac
    // 0x75931c: ldur            x2, [fp, #-0x10]
    // 0x759320: r0 = LoadClassIdInstr(r2)
    //     0x759320: ldur            x0, [x2, #-1]
    //     0x759324: ubfx            x0, x0, #0xc, #0x14
    // 0x759328: mov             x1, x2
    // 0x75932c: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x75932c: movz            x17, #0x30fa
    //     0x759330: movk            x17, #0x1, lsl #16
    //     0x759334: add             lr, x0, x17
    //     0x759338: ldr             lr, [x21, lr, lsl #3]
    //     0x75933c: blr             lr
    // 0x759340: mov             x3, x0
    // 0x759344: ldur            x0, [fp, #-0x10]
    // 0x759348: r2 = Null
    //     0x759348: mov             x2, NULL
    // 0x75934c: r1 = Null
    //     0x75934c: mov             x1, NULL
    // 0x759350: stur            x3, [fp, #-0x30]
    // 0x759354: r4 = LoadClassIdInstr(r0)
    //     0x759354: ldur            x4, [x0, #-1]
    //     0x759358: ubfx            x4, x4, #0xc, #0x14
    // 0x75935c: cmp             x4, #0xd97
    // 0x759360: b.eq            #0x759380
    // 0x759364: cmp             x4, #0xfc9
    // 0x759368: b.eq            #0x759380
    // 0x75936c: r8 = PointerPanZoomUpdateEvent
    //     0x75936c: add             x8, PP, #0x39, lsl #12  ; [pp+0x39bc8] Type: PointerPanZoomUpdateEvent
    //     0x759370: ldr             x8, [x8, #0xbc8]
    // 0x759374: r3 = Null
    //     0x759374: add             x3, PP, #0x39, lsl #12  ; [pp+0x39c00] Null
    //     0x759378: ldr             x3, [x3, #0xc00]
    // 0x75937c: r0 = DefaultTypeTest()
    //     0x75937c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x759380: ldur            x2, [fp, #-0x10]
    // 0x759384: r0 = LoadClassIdInstr(r2)
    //     0x759384: ldur            x0, [x2, #-1]
    //     0x759388: ubfx            x0, x0, #0xc, #0x14
    // 0x75938c: mov             x1, x2
    // 0x759390: r0 = GDT[cid_x0 + -0xff1]()
    //     0x759390: sub             lr, x0, #0xff1
    //     0x759394: ldr             lr, [x21, lr, lsl #3]
    //     0x759398: blr             lr
    // 0x75939c: ldur            x1, [fp, #-0x30]
    // 0x7593a0: mov             x2, x0
    // 0x7593a4: r0 = +()
    //     0x7593a4: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x7593a8: mov             x2, x0
    // 0x7593ac: ldur            x0, [fp, #-8]
    // 0x7593b0: ldur            x1, [fp, #-0x10]
    // 0x7593b4: ldur            x3, [fp, #-0x28]
    // 0x7593b8: stur            x2, [fp, #-0x30]
    // 0x7593bc: r0 = OffsetPair()
    //     0x7593bc: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x7593c0: ldur            x3, [fp, #-0x30]
    // 0x7593c4: StoreField: r0->field_7 = r3
    //     0x7593c4: stur            w3, [x0, #7]
    // 0x7593c8: ldur            x2, [fp, #-0x28]
    // 0x7593cc: StoreField: r0->field_b = r2
    //     0x7593cc: stur            w2, [x0, #0xb]
    // 0x7593d0: ldur            x4, [fp, #-8]
    // 0x7593d4: StoreField: r4->field_5f = r0
    //     0x7593d4: stur            w0, [x4, #0x5f]
    //     0x7593d8: ldurb           w16, [x4, #-1]
    //     0x7593dc: ldurb           w17, [x0, #-1]
    //     0x7593e0: and             x16, x17, x16, lsr #2
    //     0x7593e4: tst             x16, HEAP, lsr #32
    //     0x7593e8: b.eq            #0x7593f0
    //     0x7593ec: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7593f0: ldur            x5, [fp, #-0x10]
    // 0x7593f4: r0 = LoadClassIdInstr(r5)
    //     0x7593f4: ldur            x0, [x5, #-1]
    //     0x7593f8: ubfx            x0, x0, #0xc, #0x14
    // 0x7593fc: mov             x1, x5
    // 0x759400: r0 = GDT[cid_x0 + -0x1000]()
    //     0x759400: sub             lr, x0, #1, lsl #12
    //     0x759404: ldr             lr, [x21, lr, lsl #3]
    //     0x759408: blr             lr
    // 0x75940c: ldur            x1, [fp, #-8]
    // 0x759410: mov             x2, x0
    // 0x759414: ldur            x3, [fp, #-0x20]
    // 0x759418: r0 = _resolveLocalDeltaForMultitouch()
    //     0x759418: bl              #0x75a534  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_resolveLocalDeltaForMultitouch
    // 0x75941c: mov             x2, x0
    // 0x759420: ldur            x1, [fp, #-8]
    // 0x759424: stur            x2, [fp, #-0x40]
    // 0x759428: LoadField: r0 = r1->field_53
    //     0x759428: ldur            w0, [x1, #0x53]
    // 0x75942c: DecompressPointer r0
    //     0x75942c: add             x0, x0, HEAP, lsl #32
    // 0x759430: r16 = Instance__DragState
    //     0x759430: add             x16, PP, #0x25, lsl #12  ; [pp+0x25390] Obj!_DragState@e36e01
    //     0x759434: ldr             x16, [x16, #0x390]
    // 0x759438: cmp             w0, w16
    // 0x75943c: b.eq            #0x759450
    // 0x759440: r16 = Instance__DragState
    //     0x759440: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c10] Obj!_DragState@e36dc1
    //     0x759444: ldr             x16, [x16, #0xc10]
    // 0x759448: cmp             w0, w16
    // 0x75944c: b.ne            #0x7597a8
    // 0x759450: ldur            x0, [fp, #-0x10]
    // 0x759454: ldur            x3, [fp, #-0x18]
    // 0x759458: ldur            x2, [fp, #-0x20]
    // 0x75945c: LoadField: r4 = r1->field_5b
    //     0x75945c: ldur            w4, [x1, #0x5b]
    // 0x759460: DecompressPointer r4
    //     0x759460: add             x4, x4, HEAP, lsl #32
    // 0x759464: r16 = Sentinel
    //     0x759464: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x759468: cmp             w4, w16
    // 0x75946c: b.eq            #0x759978
    // 0x759470: stur            x4, [fp, #-0x38]
    // 0x759474: r0 = OffsetPair()
    //     0x759474: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x759478: mov             x1, x0
    // 0x75947c: ldur            x0, [fp, #-0x20]
    // 0x759480: StoreField: r1->field_7 = r0
    //     0x759480: stur            w0, [x1, #7]
    // 0x759484: ldur            x2, [fp, #-0x18]
    // 0x759488: StoreField: r1->field_b = r2
    //     0x759488: stur            w2, [x1, #0xb]
    // 0x75948c: mov             x2, x1
    // 0x759490: ldur            x1, [fp, #-0x38]
    // 0x759494: r0 = +()
    //     0x759494: bl              #0x75a4a8  ; [package:flutter/src/gestures/recognizer.dart] OffsetPair::+
    // 0x759498: ldur            x2, [fp, #-8]
    // 0x75949c: StoreField: r2->field_5b = r0
    //     0x75949c: stur            w0, [x2, #0x5b]
    //     0x7594a0: ldurb           w16, [x2, #-1]
    //     0x7594a4: ldurb           w17, [x0, #-1]
    //     0x7594a8: and             x16, x17, x16, lsr #2
    //     0x7594ac: tst             x16, HEAP, lsr #32
    //     0x7594b0: b.eq            #0x7594b8
    //     0x7594b4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7594b8: ldur            x3, [fp, #-0x10]
    // 0x7594bc: r0 = LoadClassIdInstr(r3)
    //     0x7594bc: ldur            x0, [x3, #-1]
    //     0x7594c0: ubfx            x0, x0, #0xc, #0x14
    // 0x7594c4: mov             x1, x3
    // 0x7594c8: r0 = GDT[cid_x0 + 0x130f9]()
    //     0x7594c8: movz            x17, #0x30f9
    //     0x7594cc: movk            x17, #0x1, lsl #16
    //     0x7594d0: add             lr, x0, x17
    //     0x7594d4: ldr             lr, [x21, lr, lsl #3]
    //     0x7594d8: blr             lr
    // 0x7594dc: ldur            x2, [fp, #-8]
    // 0x7594e0: StoreField: r2->field_63 = r0
    //     0x7594e0: stur            w0, [x2, #0x63]
    //     0x7594e4: ldurb           w16, [x2, #-1]
    //     0x7594e8: ldurb           w17, [x0, #-1]
    //     0x7594ec: and             x16, x17, x16, lsr #2
    //     0x7594f0: tst             x16, HEAP, lsr #32
    //     0x7594f4: b.eq            #0x7594fc
    //     0x7594f8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7594fc: ldur            x3, [fp, #-0x10]
    // 0x759500: r0 = LoadClassIdInstr(r3)
    //     0x759500: ldur            x0, [x3, #-1]
    //     0x759504: ubfx            x0, x0, #0xc, #0x14
    // 0x759508: mov             x1, x3
    // 0x75950c: r0 = GDT[cid_x0 + 0x131b9]()
    //     0x75950c: movz            x17, #0x31b9
    //     0x759510: movk            x17, #0x1, lsl #16
    //     0x759514: add             lr, x0, x17
    //     0x759518: ldr             lr, [x21, lr, lsl #3]
    //     0x75951c: blr             lr
    // 0x759520: ldur            x3, [fp, #-8]
    // 0x759524: StoreField: r3->field_6b = r0
    //     0x759524: stur            w0, [x3, #0x6b]
    //     0x759528: ldurb           w16, [x3, #-1]
    //     0x75952c: ldurb           w17, [x0, #-1]
    //     0x759530: and             x16, x17, x16, lsr #2
    //     0x759534: tst             x16, HEAP, lsr #32
    //     0x759538: b.eq            #0x759540
    //     0x75953c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x759540: r0 = LoadClassIdInstr(r3)
    //     0x759540: ldur            x0, [x3, #-1]
    //     0x759544: ubfx            x0, x0, #0xc, #0x14
    // 0x759548: mov             x1, x3
    // 0x75954c: ldur            x2, [fp, #-0x20]
    // 0x759550: r0 = GDT[cid_x0 + -0x1000]()
    //     0x759550: sub             lr, x0, #1, lsl #12
    //     0x759554: ldr             lr, [x21, lr, lsl #3]
    //     0x759558: blr             lr
    // 0x75955c: mov             x3, x0
    // 0x759560: ldur            x2, [fp, #-0x10]
    // 0x759564: stur            x3, [fp, #-0x18]
    // 0x759568: r0 = LoadClassIdInstr(r2)
    //     0x759568: ldur            x0, [x2, #-1]
    //     0x75956c: ubfx            x0, x0, #0xc, #0x14
    // 0x759570: mov             x1, x2
    // 0x759574: r0 = GDT[cid_x0 + 0x131b9]()
    //     0x759574: movz            x17, #0x31b9
    //     0x759578: movk            x17, #0x1, lsl #16
    //     0x75957c: add             lr, x0, x17
    //     0x759580: ldr             lr, [x21, lr, lsl #3]
    //     0x759584: blr             lr
    // 0x759588: cmp             w0, NULL
    // 0x75958c: b.ne            #0x759598
    // 0x759590: r1 = Null
    //     0x759590: mov             x1, NULL
    // 0x759594: b               #0x7595d0
    // 0x759598: ldur            x2, [fp, #-0x10]
    // 0x75959c: r0 = LoadClassIdInstr(r2)
    //     0x75959c: ldur            x0, [x2, #-1]
    //     0x7595a0: ubfx            x0, x0, #0xc, #0x14
    // 0x7595a4: mov             x1, x2
    // 0x7595a8: r0 = GDT[cid_x0 + 0x131b9]()
    //     0x7595a8: movz            x17, #0x31b9
    //     0x7595ac: movk            x17, #0x1, lsl #16
    //     0x7595b0: add             lr, x0, x17
    //     0x7595b4: ldr             lr, [x21, lr, lsl #3]
    //     0x7595b8: blr             lr
    // 0x7595bc: cmp             w0, NULL
    // 0x7595c0: b.eq            #0x759984
    // 0x7595c4: mov             x1, x0
    // 0x7595c8: r0 = tryInvert()
    //     0x7595c8: bl              #0x75a428  ; [package:vector_math/vector_math_64.dart] Matrix4::tryInvert
    // 0x7595cc: mov             x1, x0
    // 0x7595d0: ldur            x0, [fp, #-8]
    // 0x7595d4: LoadField: r4 = r0->field_6f
    //     0x7595d4: ldur            w4, [x0, #0x6f]
    // 0x7595d8: DecompressPointer r4
    //     0x7595d8: add             x4, x4, HEAP, lsl #32
    // 0x7595dc: r16 = Sentinel
    //     0x7595dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7595e0: cmp             w4, w16
    // 0x7595e4: b.eq            #0x759988
    // 0x7595e8: ldur            x2, [fp, #-0x18]
    // 0x7595ec: ldur            x3, [fp, #-0x30]
    // 0x7595f0: stur            x4, [fp, #-0x38]
    // 0x7595f4: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7595f4: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7595f8: r0 = transformDeltaViaPositions()
    //     0x7595f8: bl              #0x75a350  ; [package:flutter/src/gestures/events.dart] PointerEvent::transformDeltaViaPositions
    // 0x7595fc: mov             x1, x0
    // 0x759600: r0 = distance()
    //     0x759600: bl              #0x75a334  ; [dart:ui] Offset::distance
    // 0x759604: ldur            x3, [fp, #-8]
    // 0x759608: stur            d0, [fp, #-0x48]
    // 0x75960c: r0 = LoadClassIdInstr(r3)
    //     0x75960c: ldur            x0, [x3, #-1]
    //     0x759610: ubfx            x0, x0, #0xc, #0x14
    // 0x759614: mov             x1, x3
    // 0x759618: ldur            x2, [fp, #-0x18]
    // 0x75961c: r0 = GDT[cid_x0 + -0xfec]()
    //     0x75961c: sub             lr, x0, #0xfec
    //     0x759620: ldr             lr, [x21, lr, lsl #3]
    //     0x759624: blr             lr
    // 0x759628: cmp             w0, NULL
    // 0x75962c: b.ne            #0x759638
    // 0x759630: d1 = 1.000000
    //     0x759630: fmov            d1, #1.00000000
    // 0x759634: b               #0x759640
    // 0x759638: LoadField: d0 = r0->field_7
    //     0x759638: ldur            d0, [x0, #7]
    // 0x75963c: mov             v1.16b, v0.16b
    // 0x759640: d0 = 0.000000
    //     0x759640: eor             v0.16b, v0.16b, v0.16b
    // 0x759644: fcmp            d1, d0
    // 0x759648: b.le            #0x759654
    // 0x75964c: d1 = 1.000000
    //     0x75964c: fmov            d1, #1.00000000
    // 0x759650: b               #0x759660
    // 0x759654: fcmp            d0, d1
    // 0x759658: b.le            #0x759660
    // 0x75965c: d1 = -1.000000
    //     0x75965c: fmov            d1, #-1.00000000
    // 0x759660: ldur            x2, [fp, #-8]
    // 0x759664: ldur            x3, [fp, #-0x10]
    // 0x759668: ldur            d0, [fp, #-0x48]
    // 0x75966c: ldur            x0, [fp, #-0x38]
    // 0x759670: fmul            d2, d0, d1
    // 0x759674: LoadField: d0 = r0->field_7
    //     0x759674: ldur            d0, [x0, #7]
    // 0x759678: fadd            d1, d0, d2
    // 0x75967c: r0 = inline_Allocate_Double()
    //     0x75967c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x759680: add             x0, x0, #0x10
    //     0x759684: cmp             x1, x0
    //     0x759688: b.ls            #0x759994
    //     0x75968c: str             x0, [THR, #0x50]  ; THR::top
    //     0x759690: sub             x0, x0, #0xf
    //     0x759694: movz            x1, #0xe15c
    //     0x759698: movk            x1, #0x3, lsl #16
    //     0x75969c: stur            x1, [x0, #-1]
    // 0x7596a0: StoreField: r0->field_7 = d1
    //     0x7596a0: stur            d1, [x0, #7]
    // 0x7596a4: StoreField: r2->field_6f = r0
    //     0x7596a4: stur            w0, [x2, #0x6f]
    //     0x7596a8: ldurb           w16, [x2, #-1]
    //     0x7596ac: ldurb           w17, [x0, #-1]
    //     0x7596b0: and             x16, x17, x16, lsr #2
    //     0x7596b4: tst             x16, HEAP, lsr #32
    //     0x7596b8: b.eq            #0x7596c0
    //     0x7596bc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7596c0: r0 = LoadClassIdInstr(r3)
    //     0x7596c0: ldur            x0, [x3, #-1]
    //     0x7596c4: ubfx            x0, x0, #0xc, #0x14
    // 0x7596c8: mov             x1, x3
    // 0x7596cc: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x7596cc: movz            x17, #0x30b7
    //     0x7596d0: movk            x17, #0x1, lsl #16
    //     0x7596d4: add             lr, x0, x17
    //     0x7596d8: ldr             lr, [x21, lr, lsl #3]
    //     0x7596dc: blr             lr
    // 0x7596e0: ldur            x3, [fp, #-8]
    // 0x7596e4: r1 = LoadClassIdInstr(r3)
    //     0x7596e4: ldur            x1, [x3, #-1]
    //     0x7596e8: ubfx            x1, x1, #0xc, #0x14
    // 0x7596ec: mov             x2, x0
    // 0x7596f0: mov             x0, x1
    // 0x7596f4: mov             x1, x3
    // 0x7596f8: r0 = GDT[cid_x0 + 0x28b9]()
    //     0x7596f8: movz            x17, #0x28b9
    //     0x7596fc: add             lr, x0, x17
    //     0x759700: ldr             lr, [x21, lr, lsl #3]
    //     0x759704: blr             lr
    // 0x759708: tbnz            w0, #4, #0x75984c
    // 0x75970c: ldur            x2, [fp, #-8]
    // 0x759710: ldur            x3, [fp, #-0x10]
    // 0x759714: r0 = true
    //     0x759714: add             x0, NULL, #0x20  ; true
    // 0x759718: StoreField: r2->field_73 = r0
    //     0x759718: stur            w0, [x2, #0x73]
    // 0x75971c: LoadField: r4 = r2->field_87
    //     0x75971c: ldur            w4, [x2, #0x87]
    // 0x759720: DecompressPointer r4
    //     0x759720: add             x4, x4, HEAP, lsl #32
    // 0x759724: stur            x4, [fp, #-0x18]
    // 0x759728: r0 = LoadClassIdInstr(r3)
    //     0x759728: ldur            x0, [x3, #-1]
    //     0x75972c: ubfx            x0, x0, #0xc, #0x14
    // 0x759730: mov             x1, x3
    // 0x759734: r0 = GDT[cid_x0 + -0x1000]()
    //     0x759734: sub             lr, x0, #1, lsl #12
    //     0x759738: ldr             lr, [x21, lr, lsl #3]
    //     0x75973c: blr             lr
    // 0x759740: mov             x2, x0
    // 0x759744: r0 = BoxInt64Instr(r2)
    //     0x759744: sbfiz           x0, x2, #1, #0x1f
    //     0x759748: cmp             x2, x0, asr #1
    //     0x75974c: b.eq            #0x759758
    //     0x759750: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x759754: stur            x2, [x0, #7]
    // 0x759758: ldur            x1, [fp, #-0x18]
    // 0x75975c: mov             x2, x0
    // 0x759760: r0 = contains()
    //     0x759760: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0x759764: tbnz            w0, #4, #0x759794
    // 0x759768: ldur            x2, [fp, #-0x10]
    // 0x75976c: r0 = LoadClassIdInstr(r2)
    //     0x75976c: ldur            x0, [x2, #-1]
    //     0x759770: ubfx            x0, x0, #0xc, #0x14
    // 0x759774: mov             x1, x2
    // 0x759778: r0 = GDT[cid_x0 + -0x1000]()
    //     0x759778: sub             lr, x0, #1, lsl #12
    //     0x75977c: ldr             lr, [x21, lr, lsl #3]
    //     0x759780: blr             lr
    // 0x759784: ldur            x1, [fp, #-8]
    // 0x759788: mov             x2, x0
    // 0x75978c: r0 = _checkDrag()
    //     0x75978c: bl              #0x759cf8  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_checkDrag
    // 0x759790: b               #0x75984c
    // 0x759794: ldur            x1, [fp, #-8]
    // 0x759798: r2 = Instance_GestureDisposition
    //     0x759798: add             x2, PP, #0x30, lsl #12  ; [pp+0x30e00] Obj!GestureDisposition@e36ee1
    //     0x75979c: ldr             x2, [x2, #0xe00]
    // 0x7597a0: r0 = resolve()
    //     0x7597a0: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x7597a4: b               #0x75984c
    // 0x7597a8: r16 = Instance__DragState
    //     0x7597a8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30df0] Obj!_DragState@e36de1
    //     0x7597ac: ldr             x16, [x16, #0xdf0]
    // 0x7597b0: cmp             w0, w16
    // 0x7597b4: b.ne            #0x75984c
    // 0x7597b8: ldur            x4, [fp, #-8]
    // 0x7597bc: ldur            x3, [fp, #-0x10]
    // 0x7597c0: r0 = LoadClassIdInstr(r3)
    //     0x7597c0: ldur            x0, [x3, #-1]
    //     0x7597c4: ubfx            x0, x0, #0xc, #0x14
    // 0x7597c8: mov             x1, x3
    // 0x7597cc: r0 = GDT[cid_x0 + 0x130f9]()
    //     0x7597cc: movz            x17, #0x30f9
    //     0x7597d0: movk            x17, #0x1, lsl #16
    //     0x7597d4: add             lr, x0, x17
    //     0x7597d8: ldr             lr, [x21, lr, lsl #3]
    //     0x7597dc: blr             lr
    // 0x7597e0: mov             x4, x0
    // 0x7597e4: ldur            x3, [fp, #-8]
    // 0x7597e8: stur            x4, [fp, #-0x18]
    // 0x7597ec: r0 = LoadClassIdInstr(r3)
    //     0x7597ec: ldur            x0, [x3, #-1]
    //     0x7597f0: ubfx            x0, x0, #0xc, #0x14
    // 0x7597f4: mov             x1, x3
    // 0x7597f8: ldur            x2, [fp, #-0x40]
    // 0x7597fc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7597fc: sub             lr, x0, #1, lsl #12
    //     0x759800: ldr             lr, [x21, lr, lsl #3]
    //     0x759804: blr             lr
    // 0x759808: mov             x4, x0
    // 0x75980c: ldur            x3, [fp, #-8]
    // 0x759810: stur            x4, [fp, #-0x38]
    // 0x759814: r0 = LoadClassIdInstr(r3)
    //     0x759814: ldur            x0, [x3, #-1]
    //     0x759818: ubfx            x0, x0, #0xc, #0x14
    // 0x75981c: mov             x1, x3
    // 0x759820: ldur            x2, [fp, #-0x40]
    // 0x759824: r0 = GDT[cid_x0 + -0xfec]()
    //     0x759824: sub             lr, x0, #0xfec
    //     0x759828: ldr             lr, [x21, lr, lsl #3]
    //     0x75982c: blr             lr
    // 0x759830: ldur            x1, [fp, #-8]
    // 0x759834: ldur            x2, [fp, #-0x38]
    // 0x759838: ldur            x3, [fp, #-0x28]
    // 0x75983c: ldur            x5, [fp, #-0x30]
    // 0x759840: mov             x6, x0
    // 0x759844: ldur            x7, [fp, #-0x18]
    // 0x759848: r0 = _checkUpdate()
    //     0x759848: bl              #0x759af0  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_checkUpdate
    // 0x75984c: ldur            x2, [fp, #-0x10]
    // 0x759850: r0 = LoadClassIdInstr(r2)
    //     0x759850: ldur            x0, [x2, #-1]
    //     0x759854: ubfx            x0, x0, #0xc, #0x14
    // 0x759858: mov             x1, x2
    // 0x75985c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x75985c: sub             lr, x0, #1, lsl #12
    //     0x759860: ldr             lr, [x21, lr, lsl #3]
    //     0x759864: blr             lr
    // 0x759868: ldur            x1, [fp, #-8]
    // 0x75986c: mov             x2, x0
    // 0x759870: ldur            x3, [fp, #-0x20]
    // 0x759874: r0 = _recordMoveDeltaForMultitouch()
    //     0x759874: bl              #0x7599ac  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_recordMoveDeltaForMultitouch
    // 0x759878: ldur            x0, [fp, #-0x10]
    // 0x75987c: r2 = Null
    //     0x75987c: mov             x2, NULL
    // 0x759880: r1 = Null
    //     0x759880: mov             x1, NULL
    // 0x759884: cmp             w0, NULL
    // 0x759888: b.eq            #0x7598a8
    // 0x75988c: branchIfSmi(r0, 0x7598a8)
    //     0x75988c: tbz             w0, #0, #0x7598a8
    // 0x759890: r3 = LoadClassIdInstr(r0)
    //     0x759890: ldur            x3, [x0, #-1]
    //     0x759894: ubfx            x3, x3, #0xc, #0x14
    // 0x759898: cmp             x3, #0xda3
    // 0x75989c: b.eq            #0x7598b0
    // 0x7598a0: cmp             x3, #0xfcd
    // 0x7598a4: b.eq            #0x7598b0
    // 0x7598a8: r0 = false
    //     0x7598a8: add             x0, NULL, #0x30  ; false
    // 0x7598ac: b               #0x7598b4
    // 0x7598b0: r0 = true
    //     0x7598b0: add             x0, NULL, #0x20  ; true
    // 0x7598b4: tbz             w0, #4, #0x759938
    // 0x7598b8: ldur            x0, [fp, #-0x10]
    // 0x7598bc: r2 = Null
    //     0x7598bc: mov             x2, NULL
    // 0x7598c0: r1 = Null
    //     0x7598c0: mov             x1, NULL
    // 0x7598c4: cmp             w0, NULL
    // 0x7598c8: b.eq            #0x7598e8
    // 0x7598cc: branchIfSmi(r0, 0x7598e8)
    //     0x7598cc: tbz             w0, #0, #0x7598e8
    // 0x7598d0: r3 = LoadClassIdInstr(r0)
    //     0x7598d0: ldur            x3, [x0, #-1]
    //     0x7598d4: ubfx            x3, x3, #0xc, #0x14
    // 0x7598d8: cmp             x3, #0xd93
    // 0x7598dc: b.eq            #0x7598f0
    // 0x7598e0: cmp             x3, #0xfc5
    // 0x7598e4: b.eq            #0x7598f0
    // 0x7598e8: r0 = false
    //     0x7598e8: add             x0, NULL, #0x30  ; false
    // 0x7598ec: b               #0x7598f4
    // 0x7598f0: r0 = true
    //     0x7598f0: add             x0, NULL, #0x20  ; true
    // 0x7598f4: tbz             w0, #4, #0x759938
    // 0x7598f8: ldur            x0, [fp, #-0x10]
    // 0x7598fc: r2 = Null
    //     0x7598fc: mov             x2, NULL
    // 0x759900: r1 = Null
    //     0x759900: mov             x1, NULL
    // 0x759904: cmp             w0, NULL
    // 0x759908: b.eq            #0x759928
    // 0x75990c: branchIfSmi(r0, 0x759928)
    //     0x75990c: tbz             w0, #0, #0x759928
    // 0x759910: r3 = LoadClassIdInstr(r0)
    //     0x759910: ldur            x3, [x0, #-1]
    //     0x759914: ubfx            x3, x3, #0xc, #0x14
    // 0x759918: cmp             x3, #0xd95
    // 0x75991c: b.eq            #0x759930
    // 0x759920: cmp             x3, #0xfc7
    // 0x759924: b.eq            #0x759930
    // 0x759928: r0 = false
    //     0x759928: add             x0, NULL, #0x30  ; false
    // 0x75992c: b               #0x759934
    // 0x759930: r0 = true
    //     0x759930: add             x0, NULL, #0x20  ; true
    // 0x759934: tbnz            w0, #4, #0x75995c
    // 0x759938: ldur            x1, [fp, #-0x10]
    // 0x75993c: r0 = LoadClassIdInstr(r1)
    //     0x75993c: ldur            x0, [x1, #-1]
    //     0x759940: ubfx            x0, x0, #0xc, #0x14
    // 0x759944: r0 = GDT[cid_x0 + -0x1000]()
    //     0x759944: sub             lr, x0, #1, lsl #12
    //     0x759948: ldr             lr, [x21, lr, lsl #3]
    //     0x75994c: blr             lr
    // 0x759950: ldur            x1, [fp, #-8]
    // 0x759954: mov             x2, x0
    // 0x759958: r0 = _giveUpPointer()
    //     0x759958: bl              #0x75afec  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_giveUpPointer
    // 0x75995c: r0 = Null
    //     0x75995c: mov             x0, NULL
    // 0x759960: LeaveFrame
    //     0x759960: mov             SP, fp
    //     0x759964: ldp             fp, lr, [SP], #0x10
    // 0x759968: ret
    //     0x759968: ret             
    // 0x75996c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75996c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x759970: b               #0x758b70
    // 0x759974: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x759974: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x759978: r9 = _pendingDragOffset
    //     0x759978: add             x9, PP, #0x30, lsl #12  ; [pp+0x30e08] Field <DragGestureRecognizer._pendingDragOffset@428099969>: late (offset: 0x5c)
    //     0x75997c: ldr             x9, [x9, #0xe08]
    // 0x759980: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x759980: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x759984: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x759984: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x759988: r9 = _globalDistanceMoved
    //     0x759988: add             x9, PP, #0x39, lsl #12  ; [pp+0x39c18] Field <DragGestureRecognizer._globalDistanceMoved@428099969>: late (offset: 0x70)
    //     0x75998c: ldr             x9, [x9, #0xc18]
    // 0x759990: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x759990: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x759994: SaveReg d1
    //     0x759994: str             q1, [SP, #-0x10]!
    // 0x759998: stp             x2, x3, [SP, #-0x10]!
    // 0x75999c: r0 = AllocateDouble()
    //     0x75999c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7599a0: ldp             x2, x3, [SP], #0x10
    // 0x7599a4: RestoreReg d1
    //     0x7599a4: ldr             q1, [SP], #0x10
    // 0x7599a8: b               #0x7596a0
  }
  _ _recordMoveDeltaForMultitouch(/* No info */) {
    // ** addr: 0x7599ac, size: 0x144
    // 0x7599ac: EnterFrame
    //     0x7599ac: stp             fp, lr, [SP, #-0x10]!
    //     0x7599b0: mov             fp, SP
    // 0x7599b4: AllocStack(0x30)
    //     0x7599b4: sub             SP, SP, #0x30
    // 0x7599b8: SetupParameters(DragGestureRecognizer this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0x7599b8: mov             x0, x2
    //     0x7599bc: stur            x2, [fp, #-0x10]
    //     0x7599c0: mov             x2, x3
    //     0x7599c4: stur            x1, [fp, #-8]
    //     0x7599c8: stur            x3, [fp, #-0x18]
    // 0x7599cc: CheckStackOverflow
    //     0x7599cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7599d0: cmp             SP, x16
    //     0x7599d4: b.ls            #0x759ae4
    // 0x7599d8: LoadField: r3 = r1->field_27
    //     0x7599d8: ldur            w3, [x1, #0x27]
    // 0x7599dc: DecompressPointer r3
    //     0x7599dc: add             x3, x3, HEAP, lsl #32
    // 0x7599e0: r16 = Instance_MultitouchDragStrategy
    //     0x7599e0: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c20] Obj!MultitouchDragStrategy@e36d01
    //     0x7599e4: ldr             x16, [x16, #0xc20]
    // 0x7599e8: cmp             w3, w16
    // 0x7599ec: b.eq            #0x759a00
    // 0x7599f0: r0 = Null
    //     0x7599f0: mov             x0, NULL
    // 0x7599f4: LeaveFrame
    //     0x7599f4: mov             SP, fp
    //     0x7599f8: ldp             fp, lr, [SP], #0x10
    // 0x7599fc: ret
    //     0x7599fc: ret             
    // 0x759a00: LoadField: r3 = r1->field_53
    //     0x759a00: ldur            w3, [x1, #0x53]
    // 0x759a04: DecompressPointer r3
    //     0x759a04: add             x3, x3, HEAP, lsl #32
    // 0x759a08: r16 = Instance__DragState
    //     0x759a08: add             x16, PP, #0x30, lsl #12  ; [pp+0x30df0] Obj!_DragState@e36de1
    //     0x759a0c: ldr             x16, [x16, #0xdf0]
    // 0x759a10: cmp             w3, w16
    // 0x759a14: b.ne            #0x759a28
    // 0x759a18: r16 = Instance_Offset
    //     0x759a18: ldr             x16, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x759a1c: stp             x16, x2, [SP]
    // 0x759a20: r0 = ==()
    //     0x759a20: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0x759a24: tbnz            w0, #4, #0x759a38
    // 0x759a28: r0 = Null
    //     0x759a28: mov             x0, NULL
    // 0x759a2c: LeaveFrame
    //     0x759a2c: mov             SP, fp
    //     0x759a30: ldp             fp, lr, [SP], #0x10
    // 0x759a34: ret
    //     0x759a34: ret             
    // 0x759a38: ldur            x0, [fp, #-8]
    // 0x759a3c: ldur            x2, [fp, #-0x10]
    // 0x759a40: LoadField: r3 = r0->field_7b
    //     0x759a40: ldur            w3, [x0, #0x7b]
    // 0x759a44: DecompressPointer r3
    //     0x759a44: add             x3, x3, HEAP, lsl #32
    // 0x759a48: stur            x3, [fp, #-0x20]
    // 0x759a4c: r0 = BoxInt64Instr(r2)
    //     0x759a4c: sbfiz           x0, x2, #1, #0x1f
    //     0x759a50: cmp             x2, x0, asr #1
    //     0x759a54: b.eq            #0x759a60
    //     0x759a58: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x759a5c: stur            x2, [x0, #7]
    // 0x759a60: mov             x1, x3
    // 0x759a64: mov             x2, x0
    // 0x759a68: stur            x0, [fp, #-8]
    // 0x759a6c: r0 = containsKey()
    //     0x759a6c: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x759a70: tbnz            w0, #4, #0x759ac4
    // 0x759a74: ldur            x0, [fp, #-0x20]
    // 0x759a78: mov             x1, x0
    // 0x759a7c: ldur            x2, [fp, #-8]
    // 0x759a80: r0 = _getValueOrData()
    //     0x759a80: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x759a84: mov             x1, x0
    // 0x759a88: ldur            x0, [fp, #-0x20]
    // 0x759a8c: LoadField: r2 = r0->field_f
    //     0x759a8c: ldur            w2, [x0, #0xf]
    // 0x759a90: DecompressPointer r2
    //     0x759a90: add             x2, x2, HEAP, lsl #32
    // 0x759a94: cmp             w2, w1
    // 0x759a98: b.ne            #0x759aa0
    // 0x759a9c: r1 = Null
    //     0x759a9c: mov             x1, NULL
    // 0x759aa0: cmp             w1, NULL
    // 0x759aa4: b.eq            #0x759aec
    // 0x759aa8: ldur            x2, [fp, #-0x18]
    // 0x759aac: r0 = +()
    //     0x759aac: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x759ab0: ldur            x1, [fp, #-0x20]
    // 0x759ab4: ldur            x2, [fp, #-8]
    // 0x759ab8: mov             x3, x0
    // 0x759abc: r0 = []=()
    //     0x759abc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x759ac0: b               #0x759ad4
    // 0x759ac4: ldur            x1, [fp, #-0x20]
    // 0x759ac8: ldur            x2, [fp, #-8]
    // 0x759acc: ldur            x3, [fp, #-0x18]
    // 0x759ad0: r0 = []=()
    //     0x759ad0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x759ad4: r0 = Null
    //     0x759ad4: mov             x0, NULL
    // 0x759ad8: LeaveFrame
    //     0x759ad8: mov             SP, fp
    //     0x759adc: ldp             fp, lr, [SP], #0x10
    // 0x759ae0: ret
    //     0x759ae0: ret             
    // 0x759ae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x759ae4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x759ae8: b               #0x7599d8
    // 0x759aec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x759aec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _checkUpdate(/* No info */) {
    // ** addr: 0x759af0, size: 0xe0
    // 0x759af0: EnterFrame
    //     0x759af0: stp             fp, lr, [SP, #-0x10]!
    //     0x759af4: mov             fp, SP
    // 0x759af8: AllocStack(0x50)
    //     0x759af8: sub             SP, SP, #0x50
    // 0x759afc: SetupParameters(DragGestureRecognizer this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */, dynamic _ /* r7 => r7, fp-0x30 */)
    //     0x759afc: stur            x1, [fp, #-8]
    //     0x759b00: stur            x2, [fp, #-0x10]
    //     0x759b04: stur            x3, [fp, #-0x18]
    //     0x759b08: stur            x5, [fp, #-0x20]
    //     0x759b0c: stur            x6, [fp, #-0x28]
    //     0x759b10: stur            x7, [fp, #-0x30]
    // 0x759b14: CheckStackOverflow
    //     0x759b14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x759b18: cmp             SP, x16
    //     0x759b1c: b.ls            #0x759bc8
    // 0x759b20: r1 = 2
    //     0x759b20: movz            x1, #0x2
    // 0x759b24: r0 = AllocateContext()
    //     0x759b24: bl              #0xec126c  ; AllocateContextStub
    // 0x759b28: mov             x1, x0
    // 0x759b2c: ldur            x0, [fp, #-8]
    // 0x759b30: stur            x1, [fp, #-0x38]
    // 0x759b34: StoreField: r1->field_f = r0
    //     0x759b34: stur            w0, [x1, #0xf]
    // 0x759b38: LoadField: r2 = r0->field_33
    //     0x759b38: ldur            w2, [x0, #0x33]
    // 0x759b3c: DecompressPointer r2
    //     0x759b3c: add             x2, x2, HEAP, lsl #32
    // 0x759b40: cmp             w2, NULL
    // 0x759b44: b.eq            #0x759bb8
    // 0x759b48: ldur            x6, [fp, #-0x10]
    // 0x759b4c: ldur            x5, [fp, #-0x18]
    // 0x759b50: ldur            x4, [fp, #-0x20]
    // 0x759b54: ldur            x3, [fp, #-0x28]
    // 0x759b58: ldur            x2, [fp, #-0x30]
    // 0x759b5c: r0 = DragUpdateDetails()
    //     0x759b5c: bl              #0x759c7c  ; AllocateDragUpdateDetailsStub -> DragUpdateDetails (size=0x1c)
    // 0x759b60: mov             x1, x0
    // 0x759b64: ldur            x0, [fp, #-0x30]
    // 0x759b68: StoreField: r1->field_7 = r0
    //     0x759b68: stur            w0, [x1, #7]
    // 0x759b6c: ldur            x0, [fp, #-0x10]
    // 0x759b70: StoreField: r1->field_b = r0
    //     0x759b70: stur            w0, [x1, #0xb]
    // 0x759b74: ldur            x0, [fp, #-0x28]
    // 0x759b78: StoreField: r1->field_f = r0
    //     0x759b78: stur            w0, [x1, #0xf]
    // 0x759b7c: ldur            x0, [fp, #-0x18]
    // 0x759b80: StoreField: r1->field_13 = r0
    //     0x759b80: stur            w0, [x1, #0x13]
    // 0x759b84: ldur            x0, [fp, #-0x20]
    // 0x759b88: ArrayStore: r1[0] = r0  ; List_4
    //     0x759b88: stur            w0, [x1, #0x17]
    // 0x759b8c: ldur            x2, [fp, #-0x38]
    // 0x759b90: StoreField: r2->field_13 = r1
    //     0x759b90: stur            w1, [x2, #0x13]
    // 0x759b94: r1 = Function '<anonymous closure>':.
    //     0x759b94: add             x1, PP, #0x30, lsl #12  ; [pp+0x30e30] AnonymousClosure: (0x759c88), in [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_checkUpdate (0x759af0)
    //     0x759b98: ldr             x1, [x1, #0xe30]
    // 0x759b9c: r0 = AllocateClosure()
    //     0x759b9c: bl              #0xec1630  ; AllocateClosureStub
    // 0x759ba0: r16 = <void?>
    //     0x759ba0: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x759ba4: ldur            lr, [fp, #-8]
    // 0x759ba8: stp             lr, x16, [SP, #8]
    // 0x759bac: str             x0, [SP]
    // 0x759bb0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x759bb0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x759bb4: r0 = invokeCallback()
    //     0x759bb4: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x759bb8: r0 = Null
    //     0x759bb8: mov             x0, NULL
    // 0x759bbc: LeaveFrame
    //     0x759bbc: mov             SP, fp
    //     0x759bc0: ldp             fp, lr, [SP], #0x10
    // 0x759bc4: ret
    //     0x759bc4: ret             
    // 0x759bc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x759bc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x759bcc: b               #0x759b20
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x759c88, size: 0x70
    // 0x759c88: EnterFrame
    //     0x759c88: stp             fp, lr, [SP, #-0x10]!
    //     0x759c8c: mov             fp, SP
    // 0x759c90: AllocStack(0x10)
    //     0x759c90: sub             SP, SP, #0x10
    // 0x759c94: SetupParameters()
    //     0x759c94: ldr             x0, [fp, #0x10]
    //     0x759c98: ldur            w1, [x0, #0x17]
    //     0x759c9c: add             x1, x1, HEAP, lsl #32
    // 0x759ca0: CheckStackOverflow
    //     0x759ca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x759ca4: cmp             SP, x16
    //     0x759ca8: b.ls            #0x759cec
    // 0x759cac: LoadField: r0 = r1->field_f
    //     0x759cac: ldur            w0, [x1, #0xf]
    // 0x759cb0: DecompressPointer r0
    //     0x759cb0: add             x0, x0, HEAP, lsl #32
    // 0x759cb4: LoadField: r2 = r0->field_33
    //     0x759cb4: ldur            w2, [x0, #0x33]
    // 0x759cb8: DecompressPointer r2
    //     0x759cb8: add             x2, x2, HEAP, lsl #32
    // 0x759cbc: cmp             w2, NULL
    // 0x759cc0: b.eq            #0x759cf4
    // 0x759cc4: LoadField: r0 = r1->field_13
    //     0x759cc4: ldur            w0, [x1, #0x13]
    // 0x759cc8: DecompressPointer r0
    //     0x759cc8: add             x0, x0, HEAP, lsl #32
    // 0x759ccc: stp             x0, x2, [SP]
    // 0x759cd0: mov             x0, x2
    // 0x759cd4: ClosureCall
    //     0x759cd4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x759cd8: ldur            x2, [x0, #0x1f]
    //     0x759cdc: blr             x2
    // 0x759ce0: LeaveFrame
    //     0x759ce0: mov             SP, fp
    //     0x759ce4: ldp             fp, lr, [SP], #0x10
    // 0x759ce8: ret
    //     0x759ce8: ret             
    // 0x759cec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x759cec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x759cf0: b               #0x759cac
    // 0x759cf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x759cf4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _checkDrag(/* No info */) {
    // ** addr: 0x759cf8, size: 0x29c
    // 0x759cf8: EnterFrame
    //     0x759cf8: stp             fp, lr, [SP, #-0x10]!
    //     0x759cfc: mov             fp, SP
    // 0x759d00: AllocStack(0x38)
    //     0x759d00: sub             SP, SP, #0x38
    // 0x759d04: SetupParameters(DragGestureRecognizer this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */)
    //     0x759d04: mov             x4, x1
    //     0x759d08: mov             x3, x2
    //     0x759d0c: stur            x1, [fp, #-0x18]
    //     0x759d10: stur            x2, [fp, #-0x20]
    // 0x759d14: CheckStackOverflow
    //     0x759d14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x759d18: cmp             SP, x16
    //     0x759d1c: b.ls            #0x759f68
    // 0x759d20: LoadField: r0 = r4->field_53
    //     0x759d20: ldur            w0, [x4, #0x53]
    // 0x759d24: DecompressPointer r0
    //     0x759d24: add             x0, x0, HEAP, lsl #32
    // 0x759d28: r16 = Instance__DragState
    //     0x759d28: add             x16, PP, #0x30, lsl #12  ; [pp+0x30df0] Obj!_DragState@e36de1
    //     0x759d2c: ldr             x16, [x16, #0xdf0]
    // 0x759d30: cmp             w0, w16
    // 0x759d34: b.ne            #0x759d48
    // 0x759d38: r0 = Null
    //     0x759d38: mov             x0, NULL
    // 0x759d3c: LeaveFrame
    //     0x759d3c: mov             SP, fp
    //     0x759d40: ldp             fp, lr, [SP], #0x10
    // 0x759d44: ret
    //     0x759d44: ret             
    // 0x759d48: r0 = Instance__DragState
    //     0x759d48: add             x0, PP, #0x30, lsl #12  ; [pp+0x30df0] Obj!_DragState@e36de1
    //     0x759d4c: ldr             x0, [x0, #0xdf0]
    // 0x759d50: StoreField: r4->field_53 = r0
    //     0x759d50: stur            w0, [x4, #0x53]
    // 0x759d54: LoadField: r2 = r4->field_5b
    //     0x759d54: ldur            w2, [x4, #0x5b]
    // 0x759d58: DecompressPointer r2
    //     0x759d58: add             x2, x2, HEAP, lsl #32
    // 0x759d5c: r16 = Sentinel
    //     0x759d5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x759d60: cmp             w2, w16
    // 0x759d64: b.eq            #0x759f70
    // 0x759d68: LoadField: r5 = r4->field_63
    //     0x759d68: ldur            w5, [x4, #0x63]
    // 0x759d6c: DecompressPointer r5
    //     0x759d6c: add             x5, x5, HEAP, lsl #32
    // 0x759d70: stur            x5, [fp, #-0x10]
    // 0x759d74: LoadField: r6 = r4->field_6b
    //     0x759d74: ldur            w6, [x4, #0x6b]
    // 0x759d78: DecompressPointer r6
    //     0x759d78: add             x6, x6, HEAP, lsl #32
    // 0x759d7c: stur            x6, [fp, #-8]
    // 0x759d80: LoadField: r0 = r4->field_23
    //     0x759d80: ldur            w0, [x4, #0x23]
    // 0x759d84: DecompressPointer r0
    //     0x759d84: add             x0, x0, HEAP, lsl #32
    // 0x759d88: LoadField: r1 = r0->field_7
    //     0x759d88: ldur            x1, [x0, #7]
    // 0x759d8c: cmp             x1, #0
    // 0x759d90: b.gt            #0x759dc8
    // 0x759d94: LoadField: r0 = r2->field_7
    //     0x759d94: ldur            w0, [x2, #7]
    // 0x759d98: DecompressPointer r0
    //     0x759d98: add             x0, x0, HEAP, lsl #32
    // 0x759d9c: r1 = LoadClassIdInstr(r4)
    //     0x759d9c: ldur            x1, [x4, #-1]
    //     0x759da0: ubfx            x1, x1, #0xc, #0x14
    // 0x759da4: mov             x2, x0
    // 0x759da8: mov             x0, x1
    // 0x759dac: mov             x1, x4
    // 0x759db0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x759db0: sub             lr, x0, #1, lsl #12
    //     0x759db4: ldr             lr, [x21, lr, lsl #3]
    //     0x759db8: blr             lr
    // 0x759dbc: mov             x5, x0
    // 0x759dc0: ldur            x4, [fp, #-0x18]
    // 0x759dc4: b               #0x759e08
    // 0x759dc8: mov             x0, x4
    // 0x759dcc: LoadField: r1 = r0->field_57
    //     0x759dcc: ldur            w1, [x0, #0x57]
    // 0x759dd0: DecompressPointer r1
    //     0x759dd0: add             x1, x1, HEAP, lsl #32
    // 0x759dd4: r16 = Sentinel
    //     0x759dd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x759dd8: cmp             w1, w16
    // 0x759ddc: b.eq            #0x759f7c
    // 0x759de0: r0 = +()
    //     0x759de0: bl              #0x75a4a8  ; [package:flutter/src/gestures/recognizer.dart] OffsetPair::+
    // 0x759de4: ldur            x4, [fp, #-0x18]
    // 0x759de8: StoreField: r4->field_57 = r0
    //     0x759de8: stur            w0, [x4, #0x57]
    //     0x759dec: ldurb           w16, [x4, #-1]
    //     0x759df0: ldurb           w17, [x0, #-1]
    //     0x759df4: and             x16, x17, x16, lsr #2
    //     0x759df8: tst             x16, HEAP, lsr #32
    //     0x759dfc: b.eq            #0x759e04
    //     0x759e00: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x759e04: r5 = Instance_Offset
    //     0x759e04: ldr             x5, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x759e08: r0 = Instance_OffsetPair
    //     0x759e08: add             x0, PP, #0x30, lsl #12  ; [pp+0x30df8] Obj!OffsetPair@e14a41
    //     0x759e0c: ldr             x0, [x0, #0xdf8]
    // 0x759e10: stur            x5, [fp, #-0x28]
    // 0x759e14: StoreField: r4->field_5b = r0
    //     0x759e14: stur            w0, [x4, #0x5b]
    // 0x759e18: StoreField: r4->field_63 = rNULL
    //     0x759e18: stur            NULL, [x4, #0x63]
    // 0x759e1c: StoreField: r4->field_6b = rNULL
    //     0x759e1c: stur            NULL, [x4, #0x6b]
    // 0x759e20: mov             x1, x4
    // 0x759e24: ldur            x2, [fp, #-0x10]
    // 0x759e28: ldur            x3, [fp, #-0x20]
    // 0x759e2c: r0 = _checkStart()
    //     0x759e2c: bl              #0x759f94  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_checkStart
    // 0x759e30: ldur            x16, [fp, #-0x28]
    // 0x759e34: r30 = Instance_Offset
    //     0x759e34: ldr             lr, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x759e38: stp             lr, x16, [SP]
    // 0x759e3c: r0 = ==()
    //     0x759e3c: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0x759e40: tbz             w0, #4, #0x759f48
    // 0x759e44: ldur            x0, [fp, #-0x18]
    // 0x759e48: LoadField: r1 = r0->field_33
    //     0x759e48: ldur            w1, [x0, #0x33]
    // 0x759e4c: DecompressPointer r1
    //     0x759e4c: add             x1, x1, HEAP, lsl #32
    // 0x759e50: cmp             w1, NULL
    // 0x759e54: b.eq            #0x759f48
    // 0x759e58: ldur            x1, [fp, #-8]
    // 0x759e5c: cmp             w1, NULL
    // 0x759e60: b.eq            #0x759e70
    // 0x759e64: r0 = tryInvert()
    //     0x759e64: bl              #0x75a428  ; [package:vector_math/vector_math_64.dart] Matrix4::tryInvert
    // 0x759e68: mov             x4, x0
    // 0x759e6c: b               #0x759e74
    // 0x759e70: r4 = Null
    //     0x759e70: mov             x4, NULL
    // 0x759e74: ldur            x0, [fp, #-0x18]
    // 0x759e78: ldur            x3, [fp, #-0x28]
    // 0x759e7c: stur            x4, [fp, #-8]
    // 0x759e80: LoadField: r1 = r0->field_57
    //     0x759e80: ldur            w1, [x0, #0x57]
    // 0x759e84: DecompressPointer r1
    //     0x759e84: add             x1, x1, HEAP, lsl #32
    // 0x759e88: r16 = Sentinel
    //     0x759e88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x759e8c: cmp             w1, w16
    // 0x759e90: b.eq            #0x759f88
    // 0x759e94: LoadField: r2 = r1->field_7
    //     0x759e94: ldur            w2, [x1, #7]
    // 0x759e98: DecompressPointer r2
    //     0x759e98: add             x2, x2, HEAP, lsl #32
    // 0x759e9c: mov             x1, x2
    // 0x759ea0: mov             x2, x3
    // 0x759ea4: r0 = +()
    //     0x759ea4: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x759ea8: ldur            x1, [fp, #-8]
    // 0x759eac: ldur            x2, [fp, #-0x28]
    // 0x759eb0: mov             x3, x0
    // 0x759eb4: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x759eb4: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x759eb8: r0 = transformDeltaViaPositions()
    //     0x759eb8: bl              #0x75a350  ; [package:flutter/src/gestures/events.dart] PointerEvent::transformDeltaViaPositions
    // 0x759ebc: stur            x0, [fp, #-8]
    // 0x759ec0: r0 = OffsetPair()
    //     0x759ec0: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x759ec4: mov             x1, x0
    // 0x759ec8: ldur            x0, [fp, #-0x28]
    // 0x759ecc: StoreField: r1->field_7 = r0
    //     0x759ecc: stur            w0, [x1, #7]
    // 0x759ed0: ldur            x2, [fp, #-8]
    // 0x759ed4: StoreField: r1->field_b = r2
    //     0x759ed4: stur            w2, [x1, #0xb]
    // 0x759ed8: ldur            x3, [fp, #-0x18]
    // 0x759edc: LoadField: r2 = r3->field_57
    //     0x759edc: ldur            w2, [x3, #0x57]
    // 0x759ee0: DecompressPointer r2
    //     0x759ee0: add             x2, x2, HEAP, lsl #32
    // 0x759ee4: mov             x16, x1
    // 0x759ee8: mov             x1, x2
    // 0x759eec: mov             x2, x16
    // 0x759ef0: r0 = +()
    //     0x759ef0: bl              #0x75a4a8  ; [package:flutter/src/gestures/recognizer.dart] OffsetPair::+
    // 0x759ef4: mov             x4, x0
    // 0x759ef8: ldur            x3, [fp, #-0x18]
    // 0x759efc: stur            x4, [fp, #-8]
    // 0x759f00: r0 = LoadClassIdInstr(r3)
    //     0x759f00: ldur            x0, [x3, #-1]
    //     0x759f04: ubfx            x0, x0, #0xc, #0x14
    // 0x759f08: mov             x1, x3
    // 0x759f0c: ldur            x2, [fp, #-0x28]
    // 0x759f10: r0 = GDT[cid_x0 + -0xfec]()
    //     0x759f10: sub             lr, x0, #0xfec
    //     0x759f14: ldr             lr, [x21, lr, lsl #3]
    //     0x759f18: blr             lr
    // 0x759f1c: mov             x1, x0
    // 0x759f20: ldur            x0, [fp, #-8]
    // 0x759f24: LoadField: r3 = r0->field_b
    //     0x759f24: ldur            w3, [x0, #0xb]
    // 0x759f28: DecompressPointer r3
    //     0x759f28: add             x3, x3, HEAP, lsl #32
    // 0x759f2c: LoadField: r5 = r0->field_7
    //     0x759f2c: ldur            w5, [x0, #7]
    // 0x759f30: DecompressPointer r5
    //     0x759f30: add             x5, x5, HEAP, lsl #32
    // 0x759f34: mov             x6, x1
    // 0x759f38: ldur            x1, [fp, #-0x18]
    // 0x759f3c: ldur            x2, [fp, #-0x28]
    // 0x759f40: ldur            x7, [fp, #-0x10]
    // 0x759f44: r0 = _checkUpdate()
    //     0x759f44: bl              #0x759af0  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_checkUpdate
    // 0x759f48: ldur            x1, [fp, #-0x18]
    // 0x759f4c: r2 = Instance_GestureDisposition
    //     0x759f4c: add             x2, PP, #0x30, lsl #12  ; [pp+0x30e00] Obj!GestureDisposition@e36ee1
    //     0x759f50: ldr             x2, [x2, #0xe00]
    // 0x759f54: r0 = resolve()
    //     0x759f54: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x759f58: r0 = Null
    //     0x759f58: mov             x0, NULL
    // 0x759f5c: LeaveFrame
    //     0x759f5c: mov             SP, fp
    //     0x759f60: ldp             fp, lr, [SP], #0x10
    // 0x759f64: ret
    //     0x759f64: ret             
    // 0x759f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x759f68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x759f6c: b               #0x759d20
    // 0x759f70: r9 = _pendingDragOffset
    //     0x759f70: add             x9, PP, #0x30, lsl #12  ; [pp+0x30e08] Field <DragGestureRecognizer._pendingDragOffset@428099969>: late (offset: 0x5c)
    //     0x759f74: ldr             x9, [x9, #0xe08]
    // 0x759f78: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x759f78: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x759f7c: r9 = _initialPosition
    //     0x759f7c: add             x9, PP, #0x30, lsl #12  ; [pp+0x30e10] Field <DragGestureRecognizer._initialPosition@428099969>: late (offset: 0x58)
    //     0x759f80: ldr             x9, [x9, #0xe10]
    // 0x759f84: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x759f84: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x759f88: r9 = _initialPosition
    //     0x759f88: add             x9, PP, #0x30, lsl #12  ; [pp+0x30e10] Field <DragGestureRecognizer._initialPosition@428099969>: late (offset: 0x58)
    //     0x759f8c: ldr             x9, [x9, #0xe10]
    // 0x759f90: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x759f90: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _checkStart(/* No info */) {
    // ** addr: 0x759f94, size: 0x114
    // 0x759f94: EnterFrame
    //     0x759f94: stp             fp, lr, [SP, #-0x10]!
    //     0x759f98: mov             fp, SP
    // 0x759f9c: AllocStack(0x48)
    //     0x759f9c: sub             SP, SP, #0x48
    // 0x759fa0: SetupParameters(DragGestureRecognizer this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0x759fa0: mov             x0, x2
    //     0x759fa4: stur            x2, [fp, #-0x10]
    //     0x759fa8: mov             x2, x3
    //     0x759fac: stur            x1, [fp, #-8]
    //     0x759fb0: stur            x3, [fp, #-0x18]
    // 0x759fb4: CheckStackOverflow
    //     0x759fb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x759fb8: cmp             SP, x16
    //     0x759fbc: b.ls            #0x75a094
    // 0x759fc0: r1 = 2
    //     0x759fc0: movz            x1, #0x2
    // 0x759fc4: r0 = AllocateContext()
    //     0x759fc4: bl              #0xec126c  ; AllocateContextStub
    // 0x759fc8: mov             x3, x0
    // 0x759fcc: ldur            x0, [fp, #-8]
    // 0x759fd0: stur            x3, [fp, #-0x28]
    // 0x759fd4: StoreField: r3->field_f = r0
    //     0x759fd4: stur            w0, [x3, #0xf]
    // 0x759fd8: LoadField: r1 = r0->field_2f
    //     0x759fd8: ldur            w1, [x0, #0x2f]
    // 0x759fdc: DecompressPointer r1
    //     0x759fdc: add             x1, x1, HEAP, lsl #32
    // 0x759fe0: cmp             w1, NULL
    // 0x759fe4: b.eq            #0x75a084
    // 0x759fe8: ldur            x4, [fp, #-0x10]
    // 0x759fec: LoadField: r1 = r0->field_57
    //     0x759fec: ldur            w1, [x0, #0x57]
    // 0x759ff0: DecompressPointer r1
    //     0x759ff0: add             x1, x1, HEAP, lsl #32
    // 0x759ff4: r16 = Sentinel
    //     0x759ff4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x759ff8: cmp             w1, w16
    // 0x759ffc: b.eq            #0x75a09c
    // 0x75a000: LoadField: r5 = r1->field_b
    //     0x75a000: ldur            w5, [x1, #0xb]
    // 0x75a004: DecompressPointer r5
    //     0x75a004: add             x5, x5, HEAP, lsl #32
    // 0x75a008: mov             x1, x0
    // 0x75a00c: ldur            x2, [fp, #-0x18]
    // 0x75a010: stur            x5, [fp, #-0x20]
    // 0x75a014: r0 = getKindForPointer()
    //     0x75a014: bl              #0x75a0b4  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::getKindForPointer
    // 0x75a018: stur            x0, [fp, #-0x30]
    // 0x75a01c: r0 = DragStartDetails()
    //     0x75a01c: bl              #0x75a0a8  ; AllocateDragStartDetailsStub -> DragStartDetails (size=0x14)
    // 0x75a020: mov             x1, x0
    // 0x75a024: ldur            x0, [fp, #-0x10]
    // 0x75a028: StoreField: r1->field_7 = r0
    //     0x75a028: stur            w0, [x1, #7]
    // 0x75a02c: ldur            x0, [fp, #-0x20]
    // 0x75a030: StoreField: r1->field_b = r0
    //     0x75a030: stur            w0, [x1, #0xb]
    // 0x75a034: ldur            x0, [fp, #-0x30]
    // 0x75a038: StoreField: r1->field_f = r0
    //     0x75a038: stur            w0, [x1, #0xf]
    // 0x75a03c: mov             x0, x1
    // 0x75a040: ldur            x2, [fp, #-0x28]
    // 0x75a044: StoreField: r2->field_13 = r0
    //     0x75a044: stur            w0, [x2, #0x13]
    //     0x75a048: ldurb           w16, [x2, #-1]
    //     0x75a04c: ldurb           w17, [x0, #-1]
    //     0x75a050: and             x16, x17, x16, lsr #2
    //     0x75a054: tst             x16, HEAP, lsr #32
    //     0x75a058: b.eq            #0x75a060
    //     0x75a05c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x75a060: r1 = Function '<anonymous closure>':.
    //     0x75a060: add             x1, PP, #0x30, lsl #12  ; [pp+0x30e48] AnonymousClosure: (0x75a130), in [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_checkStart (0x759f94)
    //     0x75a064: ldr             x1, [x1, #0xe48]
    // 0x75a068: r0 = AllocateClosure()
    //     0x75a068: bl              #0xec1630  ; AllocateClosureStub
    // 0x75a06c: r16 = <void?>
    //     0x75a06c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x75a070: ldur            lr, [fp, #-8]
    // 0x75a074: stp             lr, x16, [SP, #8]
    // 0x75a078: str             x0, [SP]
    // 0x75a07c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x75a07c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x75a080: r0 = invokeCallback()
    //     0x75a080: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x75a084: r0 = Null
    //     0x75a084: mov             x0, NULL
    // 0x75a088: LeaveFrame
    //     0x75a088: mov             SP, fp
    //     0x75a08c: ldp             fp, lr, [SP], #0x10
    // 0x75a090: ret
    //     0x75a090: ret             
    // 0x75a094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75a094: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75a098: b               #0x759fc0
    // 0x75a09c: r9 = _initialPosition
    //     0x75a09c: add             x9, PP, #0x30, lsl #12  ; [pp+0x30e10] Field <DragGestureRecognizer._initialPosition@428099969>: late (offset: 0x58)
    //     0x75a0a0: ldr             x9, [x9, #0xe10]
    // 0x75a0a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x75a0a4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x75a130, size: 0x70
    // 0x75a130: EnterFrame
    //     0x75a130: stp             fp, lr, [SP, #-0x10]!
    //     0x75a134: mov             fp, SP
    // 0x75a138: AllocStack(0x10)
    //     0x75a138: sub             SP, SP, #0x10
    // 0x75a13c: SetupParameters()
    //     0x75a13c: ldr             x0, [fp, #0x10]
    //     0x75a140: ldur            w1, [x0, #0x17]
    //     0x75a144: add             x1, x1, HEAP, lsl #32
    // 0x75a148: CheckStackOverflow
    //     0x75a148: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75a14c: cmp             SP, x16
    //     0x75a150: b.ls            #0x75a194
    // 0x75a154: LoadField: r0 = r1->field_f
    //     0x75a154: ldur            w0, [x1, #0xf]
    // 0x75a158: DecompressPointer r0
    //     0x75a158: add             x0, x0, HEAP, lsl #32
    // 0x75a15c: LoadField: r2 = r0->field_2f
    //     0x75a15c: ldur            w2, [x0, #0x2f]
    // 0x75a160: DecompressPointer r2
    //     0x75a160: add             x2, x2, HEAP, lsl #32
    // 0x75a164: cmp             w2, NULL
    // 0x75a168: b.eq            #0x75a19c
    // 0x75a16c: LoadField: r0 = r1->field_13
    //     0x75a16c: ldur            w0, [x1, #0x13]
    // 0x75a170: DecompressPointer r0
    //     0x75a170: add             x0, x0, HEAP, lsl #32
    // 0x75a174: stp             x0, x2, [SP]
    // 0x75a178: mov             x0, x2
    // 0x75a17c: ClosureCall
    //     0x75a17c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x75a180: ldur            x2, [x0, #0x1f]
    //     0x75a184: blr             x2
    // 0x75a188: LeaveFrame
    //     0x75a188: mov             SP, fp
    //     0x75a18c: ldp             fp, lr, [SP], #0x10
    // 0x75a190: ret
    //     0x75a190: ret             
    // 0x75a194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75a194: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75a198: b               #0x75a154
    // 0x75a19c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75a19c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _resolveLocalDeltaForMultitouch(/* No info */) {
    // ** addr: 0x75a534, size: 0x2ec
    // 0x75a534: EnterFrame
    //     0x75a534: stp             fp, lr, [SP, #-0x10]!
    //     0x75a538: mov             fp, SP
    // 0x75a53c: AllocStack(0x40)
    //     0x75a53c: sub             SP, SP, #0x40
    // 0x75a540: SetupParameters(DragGestureRecognizer this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r5, fp-0x20 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0x75a540: mov             x5, x2
    //     0x75a544: stur            x2, [fp, #-0x20]
    //     0x75a548: mov             x2, x1
    //     0x75a54c: mov             x0, x3
    //     0x75a550: stur            x1, [fp, #-8]
    //     0x75a554: stur            x3, [fp, #-0x10]
    // 0x75a558: CheckStackOverflow
    //     0x75a558: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75a55c: cmp             SP, x16
    //     0x75a560: b.ls            #0x75a814
    // 0x75a564: LoadField: r1 = r2->field_27
    //     0x75a564: ldur            w1, [x2, #0x27]
    // 0x75a568: DecompressPointer r1
    //     0x75a568: add             x1, x1, HEAP, lsl #32
    // 0x75a56c: r16 = Instance_MultitouchDragStrategy
    //     0x75a56c: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c20] Obj!MultitouchDragStrategy@e36d01
    //     0x75a570: ldr             x16, [x16, #0xc20]
    // 0x75a574: cmp             w1, w16
    // 0x75a578: b.eq            #0x75a5b8
    // 0x75a57c: LoadField: r1 = r2->field_7f
    //     0x75a57c: ldur            w1, [x2, #0x7f]
    // 0x75a580: DecompressPointer r1
    //     0x75a580: add             x1, x1, HEAP, lsl #32
    // 0x75a584: cmp             w1, NULL
    // 0x75a588: b.eq            #0x75a5a8
    // 0x75a58c: LoadField: r1 = r2->field_7b
    //     0x75a58c: ldur            w1, [x2, #0x7b]
    // 0x75a590: DecompressPointer r1
    //     0x75a590: add             x1, x1, HEAP, lsl #32
    // 0x75a594: r0 = clear()
    //     0x75a594: bl              #0x623ad0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x75a598: ldur            x1, [fp, #-8]
    // 0x75a59c: StoreField: r1->field_7f = rNULL
    //     0x75a59c: stur            NULL, [x1, #0x7f]
    // 0x75a5a0: r2 = Instance_Offset
    //     0x75a5a0: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x75a5a4: StoreField: r1->field_83 = r2
    //     0x75a5a4: stur            w2, [x1, #0x83]
    // 0x75a5a8: ldur            x0, [fp, #-0x10]
    // 0x75a5ac: LeaveFrame
    //     0x75a5ac: mov             SP, fp
    //     0x75a5b0: ldp             fp, lr, [SP], #0x10
    // 0x75a5b4: ret
    //     0x75a5b4: ret             
    // 0x75a5b8: mov             x1, x2
    // 0x75a5bc: r2 = Instance_Offset
    //     0x75a5bc: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x75a5c0: r0 = LoadStaticField(0x958)
    //     0x75a5c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x75a5c4: ldr             x0, [x0, #0x12b0]
    // 0x75a5c8: cmp             w0, NULL
    // 0x75a5cc: b.eq            #0x75a81c
    // 0x75a5d0: LoadField: r3 = r0->field_73
    //     0x75a5d0: ldur            w3, [x0, #0x73]
    // 0x75a5d4: DecompressPointer r3
    //     0x75a5d4: add             x3, x3, HEAP, lsl #32
    // 0x75a5d8: stur            x3, [fp, #-0x18]
    // 0x75a5dc: LoadField: r0 = r1->field_7f
    //     0x75a5dc: ldur            w0, [x1, #0x7f]
    // 0x75a5e0: DecompressPointer r0
    //     0x75a5e0: add             x0, x0, HEAP, lsl #32
    // 0x75a5e4: r4 = LoadClassIdInstr(r0)
    //     0x75a5e4: ldur            x4, [x0, #-1]
    //     0x75a5e8: ubfx            x4, x4, #0xc, #0x14
    // 0x75a5ec: stp             x3, x0, [SP]
    // 0x75a5f0: mov             x0, x4
    // 0x75a5f4: mov             lr, x0
    // 0x75a5f8: ldr             lr, [x21, lr, lsl #3]
    // 0x75a5fc: blr             lr
    // 0x75a600: tbz             w0, #4, #0x75a644
    // 0x75a604: ldur            x0, [fp, #-8]
    // 0x75a608: LoadField: r1 = r0->field_7b
    //     0x75a608: ldur            w1, [x0, #0x7b]
    // 0x75a60c: DecompressPointer r1
    //     0x75a60c: add             x1, x1, HEAP, lsl #32
    // 0x75a610: r0 = clear()
    //     0x75a610: bl              #0x623ad0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x75a614: ldur            x2, [fp, #-8]
    // 0x75a618: r0 = Instance_Offset
    //     0x75a618: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x75a61c: StoreField: r2->field_83 = r0
    //     0x75a61c: stur            w0, [x2, #0x83]
    // 0x75a620: ldur            x0, [fp, #-0x18]
    // 0x75a624: StoreField: r2->field_7f = r0
    //     0x75a624: stur            w0, [x2, #0x7f]
    //     0x75a628: ldurb           w16, [x2, #-1]
    //     0x75a62c: ldurb           w17, [x0, #-1]
    //     0x75a630: and             x16, x17, x16, lsr #2
    //     0x75a634: tst             x16, HEAP, lsr #32
    //     0x75a638: b.eq            #0x75a640
    //     0x75a63c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x75a640: b               #0x75a648
    // 0x75a644: ldur            x2, [fp, #-8]
    // 0x75a648: r0 = LoadClassIdInstr(r2)
    //     0x75a648: ldur            x0, [x2, #-1]
    //     0x75a64c: ubfx            x0, x0, #0xc, #0x14
    // 0x75a650: mov             x1, x2
    // 0x75a654: r0 = GDT[cid_x0 + 0x369c]()
    //     0x75a654: movz            x17, #0x369c
    //     0x75a658: add             lr, x0, x17
    //     0x75a65c: ldr             lr, [x21, lr, lsl #3]
    //     0x75a660: blr             lr
    // 0x75a664: ldur            x1, [fp, #-8]
    // 0x75a668: stur            x0, [fp, #-0x18]
    // 0x75a66c: LoadField: r2 = r1->field_53
    //     0x75a66c: ldur            w2, [x1, #0x53]
    // 0x75a670: DecompressPointer r2
    //     0x75a670: add             x2, x2, HEAP, lsl #32
    // 0x75a674: r16 = Instance__DragState
    //     0x75a674: add             x16, PP, #0x30, lsl #12  ; [pp+0x30df0] Obj!_DragState@e36de1
    //     0x75a678: ldr             x16, [x16, #0xdf0]
    // 0x75a67c: cmp             w2, w16
    // 0x75a680: b.ne            #0x75a6cc
    // 0x75a684: ldur            x16, [fp, #-0x10]
    // 0x75a688: r30 = Instance_Offset
    //     0x75a688: ldr             lr, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x75a68c: stp             lr, x16, [SP]
    // 0x75a690: r0 = ==()
    //     0x75a690: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0x75a694: tbz             w0, #4, #0x75a6cc
    // 0x75a698: ldur            x0, [fp, #-8]
    // 0x75a69c: LoadField: r1 = r0->field_7b
    //     0x75a69c: ldur            w1, [x0, #0x7b]
    // 0x75a6a0: DecompressPointer r1
    //     0x75a6a0: add             x1, x1, HEAP, lsl #32
    // 0x75a6a4: LoadField: r2 = r1->field_13
    //     0x75a6a4: ldur            w2, [x1, #0x13]
    // 0x75a6a8: r3 = LoadInt32Instr(r2)
    //     0x75a6a8: sbfx            x3, x2, #1, #0x1f
    // 0x75a6ac: asr             x2, x3, #1
    // 0x75a6b0: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x75a6b0: ldur            w3, [x1, #0x17]
    // 0x75a6b4: r1 = LoadInt32Instr(r3)
    //     0x75a6b4: sbfx            x1, x3, #1, #0x1f
    // 0x75a6b8: sub             x3, x2, x1
    // 0x75a6bc: cbnz            x3, #0x75a6dc
    // 0x75a6c0: ldur            x1, [fp, #-0x18]
    // 0x75a6c4: cmp             w1, NULL
    // 0x75a6c8: b.eq            #0x75a6e0
    // 0x75a6cc: ldur            x0, [fp, #-0x10]
    // 0x75a6d0: LeaveFrame
    //     0x75a6d0: mov             SP, fp
    //     0x75a6d4: ldp             fp, lr, [SP], #0x10
    // 0x75a6d8: ret
    //     0x75a6d8: ret             
    // 0x75a6dc: ldur            x1, [fp, #-0x18]
    // 0x75a6e0: r16 = Instance__DragDirection
    //     0x75a6e0: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c28] Obj!_DragDirection@e36da1
    //     0x75a6e4: ldr             x16, [x16, #0xc28]
    // 0x75a6e8: cmp             w1, w16
    // 0x75a6ec: b.ne            #0x75a714
    // 0x75a6f0: mov             x1, x0
    // 0x75a6f4: ldur            x3, [fp, #-0x10]
    // 0x75a6f8: ldur            x5, [fp, #-0x20]
    // 0x75a6fc: r2 = Instance__DragDirection
    //     0x75a6fc: add             x2, PP, #0x39, lsl #12  ; [pp+0x39c28] Obj!_DragDirection@e36da1
    //     0x75a700: ldr             x2, [x2, #0xc28]
    // 0x75a704: r0 = _resolveDelta()
    //     0x75a704: bl              #0x75a9c8  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_resolveDelta
    // 0x75a708: mov             v1.16b, v0.16b
    // 0x75a70c: d0 = 0.000000
    //     0x75a70c: eor             v0.16b, v0.16b, v0.16b
    // 0x75a710: b               #0x75a7ec
    // 0x75a714: r16 = Instance__DragDirection
    //     0x75a714: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c30] Obj!_DragDirection@e36d81
    //     0x75a718: ldr             x16, [x16, #0xc30]
    // 0x75a71c: cmp             w1, w16
    // 0x75a720: b.ne            #0x75a744
    // 0x75a724: mov             x1, x0
    // 0x75a728: ldur            x3, [fp, #-0x10]
    // 0x75a72c: ldur            x5, [fp, #-0x20]
    // 0x75a730: r2 = Instance__DragDirection
    //     0x75a730: add             x2, PP, #0x39, lsl #12  ; [pp+0x39c30] Obj!_DragDirection@e36d81
    //     0x75a734: ldr             x2, [x2, #0xc30]
    // 0x75a738: r0 = _resolveDelta()
    //     0x75a738: bl              #0x75a9c8  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_resolveDelta
    // 0x75a73c: d1 = 0.000000
    //     0x75a73c: eor             v1.16b, v1.16b, v1.16b
    // 0x75a740: b               #0x75a7ec
    // 0x75a744: mov             x1, x0
    // 0x75a748: ldur            x3, [fp, #-0x10]
    // 0x75a74c: r2 = Instance__DragDirection
    //     0x75a74c: add             x2, PP, #0x39, lsl #12  ; [pp+0x39c28] Obj!_DragDirection@e36da1
    //     0x75a750: ldr             x2, [x2, #0xc28]
    // 0x75a754: r0 = _resolveDeltaForPanGesture()
    //     0x75a754: bl              #0x75a820  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_resolveDeltaForPanGesture
    // 0x75a758: ldur            x1, [fp, #-8]
    // 0x75a75c: ldur            x3, [fp, #-0x10]
    // 0x75a760: r2 = Instance__DragDirection
    //     0x75a760: add             x2, PP, #0x39, lsl #12  ; [pp+0x39c30] Obj!_DragDirection@e36d81
    //     0x75a764: ldr             x2, [x2, #0xc30]
    // 0x75a768: stur            d0, [fp, #-0x28]
    // 0x75a76c: r0 = _resolveDeltaForPanGesture()
    //     0x75a76c: bl              #0x75a820  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_resolveDeltaForPanGesture
    // 0x75a770: stur            d0, [fp, #-0x30]
    // 0x75a774: r0 = Offset()
    //     0x75a774: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x75a778: ldur            d0, [fp, #-0x28]
    // 0x75a77c: StoreField: r0->field_7 = d0
    //     0x75a77c: stur            d0, [x0, #7]
    // 0x75a780: ldur            d1, [fp, #-0x30]
    // 0x75a784: StoreField: r0->field_f = d1
    //     0x75a784: stur            d1, [x0, #0xf]
    // 0x75a788: ldur            x3, [fp, #-8]
    // 0x75a78c: LoadField: r2 = r3->field_83
    //     0x75a78c: ldur            w2, [x3, #0x83]
    // 0x75a790: DecompressPointer r2
    //     0x75a790: add             x2, x2, HEAP, lsl #32
    // 0x75a794: mov             x1, x0
    // 0x75a798: r0 = -()
    //     0x75a798: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x75a79c: stur            x0, [fp, #-0x10]
    // 0x75a7a0: r0 = Offset()
    //     0x75a7a0: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x75a7a4: ldur            d0, [fp, #-0x28]
    // 0x75a7a8: StoreField: r0->field_7 = d0
    //     0x75a7a8: stur            d0, [x0, #7]
    // 0x75a7ac: ldur            d0, [fp, #-0x30]
    // 0x75a7b0: StoreField: r0->field_f = d0
    //     0x75a7b0: stur            d0, [x0, #0xf]
    // 0x75a7b4: ldur            x1, [fp, #-8]
    // 0x75a7b8: StoreField: r1->field_83 = r0
    //     0x75a7b8: stur            w0, [x1, #0x83]
    //     0x75a7bc: ldurb           w16, [x1, #-1]
    //     0x75a7c0: ldurb           w17, [x0, #-1]
    //     0x75a7c4: and             x16, x17, x16, lsr #2
    //     0x75a7c8: tst             x16, HEAP, lsr #32
    //     0x75a7cc: b.eq            #0x75a7d4
    //     0x75a7d0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x75a7d4: ldur            x0, [fp, #-0x10]
    // 0x75a7d8: LoadField: d0 = r0->field_7
    //     0x75a7d8: ldur            d0, [x0, #7]
    // 0x75a7dc: LoadField: d1 = r0->field_f
    //     0x75a7dc: ldur            d1, [x0, #0xf]
    // 0x75a7e0: mov             v31.16b, v1.16b
    // 0x75a7e4: mov             v1.16b, v0.16b
    // 0x75a7e8: mov             v0.16b, v31.16b
    // 0x75a7ec: stur            d1, [fp, #-0x28]
    // 0x75a7f0: stur            d0, [fp, #-0x30]
    // 0x75a7f4: r0 = Offset()
    //     0x75a7f4: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x75a7f8: ldur            d0, [fp, #-0x28]
    // 0x75a7fc: StoreField: r0->field_7 = d0
    //     0x75a7fc: stur            d0, [x0, #7]
    // 0x75a800: ldur            d0, [fp, #-0x30]
    // 0x75a804: StoreField: r0->field_f = d0
    //     0x75a804: stur            d0, [x0, #0xf]
    // 0x75a808: LeaveFrame
    //     0x75a808: mov             SP, fp
    //     0x75a80c: ldp             fp, lr, [SP], #0x10
    // 0x75a810: ret
    //     0x75a810: ret             
    // 0x75a814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75a814: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75a818: b               #0x75a564
    // 0x75a81c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75a81c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _resolveDeltaForPanGesture(/* No info */) {
    // ** addr: 0x75a820, size: 0x1a8
    // 0x75a820: EnterFrame
    //     0x75a820: stp             fp, lr, [SP, #-0x10]!
    //     0x75a824: mov             fp, SP
    // 0x75a828: AllocStack(0x30)
    //     0x75a828: sub             SP, SP, #0x30
    // 0x75a82c: SetupParameters(dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x75a82c: mov             x0, x2
    //     0x75a830: stur            x2, [fp, #-0x18]
    // 0x75a834: CheckStackOverflow
    //     0x75a834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75a838: cmp             SP, x16
    //     0x75a83c: b.ls            #0x75a9b8
    // 0x75a840: r16 = Instance__DragDirection
    //     0x75a840: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c28] Obj!_DragDirection@e36da1
    //     0x75a844: ldr             x16, [x16, #0xc28]
    // 0x75a848: cmp             w0, w16
    // 0x75a84c: b.ne            #0x75a858
    // 0x75a850: LoadField: d0 = r3->field_7
    //     0x75a850: ldur            d0, [x3, #7]
    // 0x75a854: b               #0x75a85c
    // 0x75a858: LoadField: d0 = r3->field_f
    //     0x75a858: ldur            d0, [x3, #0xf]
    // 0x75a85c: stur            d0, [fp, #-0x30]
    // 0x75a860: LoadField: r2 = r1->field_87
    //     0x75a860: ldur            w2, [x1, #0x87]
    // 0x75a864: DecompressPointer r2
    //     0x75a864: add             x2, x2, HEAP, lsl #32
    // 0x75a868: LoadField: r4 = r2->field_b
    //     0x75a868: ldur            w4, [x2, #0xb]
    // 0x75a86c: stur            x4, [fp, #-0x10]
    // 0x75a870: LoadField: r5 = r1->field_7b
    //     0x75a870: ldur            w5, [x1, #0x7b]
    // 0x75a874: DecompressPointer r5
    //     0x75a874: add             x5, x5, HEAP, lsl #32
    // 0x75a878: stur            x5, [fp, #-8]
    // 0x75a87c: LoadField: r2 = r5->field_7
    //     0x75a87c: ldur            w2, [x5, #7]
    // 0x75a880: DecompressPointer r2
    //     0x75a880: add             x2, x2, HEAP, lsl #32
    // 0x75a884: r1 = Null
    //     0x75a884: mov             x1, NULL
    // 0x75a888: r3 = <X1>
    //     0x75a888: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x75a88c: r0 = Null
    //     0x75a88c: mov             x0, NULL
    // 0x75a890: cmp             x2, x0
    // 0x75a894: b.eq            #0x75a8a4
    // 0x75a898: r30 = InstantiateTypeArgumentsStub
    //     0x75a898: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x75a89c: LoadField: r30 = r30->field_7
    //     0x75a89c: ldur            lr, [lr, #7]
    // 0x75a8a0: blr             lr
    // 0x75a8a4: mov             x1, x0
    // 0x75a8a8: r0 = _CompactIterable()
    //     0x75a8a8: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x75a8ac: mov             x1, x0
    // 0x75a8b0: ldur            x0, [fp, #-8]
    // 0x75a8b4: StoreField: r1->field_b = r0
    //     0x75a8b4: stur            w0, [x1, #0xb]
    // 0x75a8b8: r0 = -1
    //     0x75a8b8: movn            x0, #0
    // 0x75a8bc: StoreField: r1->field_f = r0
    //     0x75a8bc: stur            x0, [x1, #0xf]
    // 0x75a8c0: r0 = 2
    //     0x75a8c0: movz            x0, #0x2
    // 0x75a8c4: ArrayStore: r1[0] = r0  ; List_8
    //     0x75a8c4: stur            x0, [x1, #0x17]
    // 0x75a8c8: r0 = iterator()
    //     0x75a8c8: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x75a8cc: stur            x0, [fp, #-0x20]
    // 0x75a8d0: LoadField: r2 = r0->field_7
    //     0x75a8d0: ldur            w2, [x0, #7]
    // 0x75a8d4: DecompressPointer r2
    //     0x75a8d4: add             x2, x2, HEAP, lsl #32
    // 0x75a8d8: stur            x2, [fp, #-8]
    // 0x75a8dc: ldur            d0, [fp, #-0x30]
    // 0x75a8e0: ldur            x3, [fp, #-0x18]
    // 0x75a8e4: stur            d0, [fp, #-0x30]
    // 0x75a8e8: CheckStackOverflow
    //     0x75a8e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75a8ec: cmp             SP, x16
    //     0x75a8f0: b.ls            #0x75a9c0
    // 0x75a8f4: mov             x1, x0
    // 0x75a8f8: r0 = moveNext()
    //     0x75a8f8: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x75a8fc: tbnz            w0, #4, #0x75a998
    // 0x75a900: ldur            x3, [fp, #-0x20]
    // 0x75a904: LoadField: r4 = r3->field_33
    //     0x75a904: ldur            w4, [x3, #0x33]
    // 0x75a908: DecompressPointer r4
    //     0x75a908: add             x4, x4, HEAP, lsl #32
    // 0x75a90c: stur            x4, [fp, #-0x28]
    // 0x75a910: cmp             w4, NULL
    // 0x75a914: b.ne            #0x75a948
    // 0x75a918: mov             x0, x4
    // 0x75a91c: ldur            x2, [fp, #-8]
    // 0x75a920: r1 = Null
    //     0x75a920: mov             x1, NULL
    // 0x75a924: cmp             w2, NULL
    // 0x75a928: b.eq            #0x75a948
    // 0x75a92c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x75a92c: ldur            w4, [x2, #0x17]
    // 0x75a930: DecompressPointer r4
    //     0x75a930: add             x4, x4, HEAP, lsl #32
    // 0x75a934: r8 = X0
    //     0x75a934: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x75a938: LoadField: r9 = r4->field_7
    //     0x75a938: ldur            x9, [x4, #7]
    // 0x75a93c: r3 = Null
    //     0x75a93c: add             x3, PP, #0x39, lsl #12  ; [pp+0x39c38] Null
    //     0x75a940: ldr             x3, [x3, #0xc38]
    // 0x75a944: blr             x9
    // 0x75a948: ldur            x0, [fp, #-0x18]
    // 0x75a94c: r16 = Instance__DragDirection
    //     0x75a94c: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c28] Obj!_DragDirection@e36da1
    //     0x75a950: ldr             x16, [x16, #0xc28]
    // 0x75a954: cmp             w0, w16
    // 0x75a958: b.ne            #0x75a974
    // 0x75a95c: ldur            d1, [fp, #-0x30]
    // 0x75a960: ldur            x1, [fp, #-0x28]
    // 0x75a964: LoadField: d2 = r1->field_7
    //     0x75a964: ldur            d2, [x1, #7]
    // 0x75a968: fadd            d3, d1, d2
    // 0x75a96c: mov             v0.16b, v3.16b
    // 0x75a970: b               #0x75a988
    // 0x75a974: ldur            d1, [fp, #-0x30]
    // 0x75a978: ldur            x1, [fp, #-0x28]
    // 0x75a97c: LoadField: d2 = r1->field_f
    //     0x75a97c: ldur            d2, [x1, #0xf]
    // 0x75a980: fadd            d3, d1, d2
    // 0x75a984: mov             v0.16b, v3.16b
    // 0x75a988: mov             x3, x0
    // 0x75a98c: ldur            x0, [fp, #-0x20]
    // 0x75a990: ldur            x2, [fp, #-8]
    // 0x75a994: b               #0x75a8e4
    // 0x75a998: ldur            d1, [fp, #-0x30]
    // 0x75a99c: ldur            x0, [fp, #-0x10]
    // 0x75a9a0: r16 = LoadInt32Instr(r0)
    //     0x75a9a0: sbfx            x16, x0, #1, #0x1f
    // 0x75a9a4: scvtf           d2, w16
    // 0x75a9a8: fdiv            d0, d1, d2
    // 0x75a9ac: LeaveFrame
    //     0x75a9ac: mov             SP, fp
    //     0x75a9b0: ldp             fp, lr, [SP], #0x10
    // 0x75a9b4: ret
    //     0x75a9b4: ret             
    // 0x75a9b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75a9b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75a9bc: b               #0x75a840
    // 0x75a9c0: r0 = StackOverflowSharedWithFPURegs()
    //     0x75a9c0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x75a9c4: b               #0x75a8f4
  }
  _ _resolveDelta(/* No info */) {
    // ** addr: 0x75a9c8, size: 0x1d8
    // 0x75a9c8: EnterFrame
    //     0x75a9c8: stp             fp, lr, [SP, #-0x10]!
    //     0x75a9cc: mov             fp, SP
    // 0x75a9d0: AllocStack(0x30)
    //     0x75a9d0: sub             SP, SP, #0x30
    // 0x75a9d4: SetupParameters(DragGestureRecognizer this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0x75a9d4: mov             x0, x5
    //     0x75a9d8: stur            x5, [fp, #-0x20]
    //     0x75a9dc: mov             x5, x1
    //     0x75a9e0: mov             x4, x2
    //     0x75a9e4: stur            x1, [fp, #-0x10]
    //     0x75a9e8: stur            x2, [fp, #-0x18]
    // 0x75a9ec: CheckStackOverflow
    //     0x75a9ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75a9f0: cmp             SP, x16
    //     0x75a9f4: b.ls            #0x75ab94
    // 0x75a9f8: r16 = Instance__DragDirection
    //     0x75a9f8: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c28] Obj!_DragDirection@e36da1
    //     0x75a9fc: ldr             x16, [x16, #0xc28]
    // 0x75aa00: cmp             w4, w16
    // 0x75aa04: b.ne            #0x75aa28
    // 0x75aa08: d0 = 0.000000
    //     0x75aa08: eor             v0.16b, v0.16b, v0.16b
    // 0x75aa0c: LoadField: d1 = r3->field_7
    //     0x75aa0c: ldur            d1, [x3, #7]
    // 0x75aa10: fcmp            d1, d0
    // 0x75aa14: r16 = true
    //     0x75aa14: add             x16, NULL, #0x20  ; true
    // 0x75aa18: r17 = false
    //     0x75aa18: add             x17, NULL, #0x30  ; false
    // 0x75aa1c: csel            x1, x16, x17, gt
    // 0x75aa20: mov             x6, x1
    // 0x75aa24: b               #0x75aa44
    // 0x75aa28: d0 = 0.000000
    //     0x75aa28: eor             v0.16b, v0.16b, v0.16b
    // 0x75aa2c: LoadField: d1 = r3->field_f
    //     0x75aa2c: ldur            d1, [x3, #0xf]
    // 0x75aa30: fcmp            d1, d0
    // 0x75aa34: r16 = true
    //     0x75aa34: add             x16, NULL, #0x20  ; true
    // 0x75aa38: r17 = false
    //     0x75aa38: add             x17, NULL, #0x30  ; false
    // 0x75aa3c: csel            x1, x16, x17, gt
    // 0x75aa40: mov             x6, x1
    // 0x75aa44: stur            x6, [fp, #-8]
    // 0x75aa48: r16 = Instance__DragDirection
    //     0x75aa48: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c28] Obj!_DragDirection@e36da1
    //     0x75aa4c: ldr             x16, [x16, #0xc28]
    // 0x75aa50: cmp             w4, w16
    // 0x75aa54: b.ne            #0x75aa60
    // 0x75aa58: LoadField: d1 = r3->field_7
    //     0x75aa58: ldur            d1, [x3, #7]
    // 0x75aa5c: b               #0x75aa64
    // 0x75aa60: LoadField: d1 = r3->field_f
    //     0x75aa60: ldur            d1, [x3, #0xf]
    // 0x75aa64: mov             x1, x5
    // 0x75aa68: mov             x2, x4
    // 0x75aa6c: mov             x3, x6
    // 0x75aa70: stur            d1, [fp, #-0x28]
    // 0x75aa74: r0 = _getMaxSumDeltaPointer()
    //     0x75aa74: bl              #0x75ad78  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_getMaxSumDeltaPointer
    // 0x75aa78: mov             x2, x0
    // 0x75aa7c: ldur            x4, [fp, #-0x20]
    // 0x75aa80: r0 = BoxInt64Instr(r4)
    //     0x75aa80: sbfiz           x0, x4, #1, #0x1f
    //     0x75aa84: cmp             x4, x0, asr #1
    //     0x75aa88: b.eq            #0x75aa94
    //     0x75aa8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x75aa90: stur            x4, [x0, #7]
    // 0x75aa94: cmp             w2, w0
    // 0x75aa98: b.eq            #0x75aad4
    // 0x75aa9c: and             w16, w2, w0
    // 0x75aaa0: branchIfSmi(r16, 0x75aae4)
    //     0x75aaa0: tbz             w16, #0, #0x75aae4
    // 0x75aaa4: r16 = LoadClassIdInstr(r2)
    //     0x75aaa4: ldur            x16, [x2, #-1]
    //     0x75aaa8: ubfx            x16, x16, #0xc, #0x14
    // 0x75aaac: cmp             x16, #0x3d
    // 0x75aab0: b.ne            #0x75aae4
    // 0x75aab4: r16 = LoadClassIdInstr(r0)
    //     0x75aab4: ldur            x16, [x0, #-1]
    //     0x75aab8: ubfx            x16, x16, #0xc, #0x14
    // 0x75aabc: cmp             x16, #0x3d
    // 0x75aac0: b.ne            #0x75aae4
    // 0x75aac4: LoadField: r16 = r2->field_7
    //     0x75aac4: ldur            x16, [x2, #7]
    // 0x75aac8: LoadField: r17 = r0->field_7
    //     0x75aac8: ldur            x17, [x0, #7]
    // 0x75aacc: cmp             x16, x17
    // 0x75aad0: b.ne            #0x75aae4
    // 0x75aad4: ldur            d0, [fp, #-0x28]
    // 0x75aad8: LeaveFrame
    //     0x75aad8: mov             SP, fp
    //     0x75aadc: ldp             fp, lr, [SP], #0x10
    // 0x75aae0: ret
    //     0x75aae0: ret             
    // 0x75aae4: ldur            x0, [fp, #-8]
    // 0x75aae8: cmp             w2, NULL
    // 0x75aaec: b.eq            #0x75ab9c
    // 0x75aaf0: r3 = LoadInt32Instr(r2)
    //     0x75aaf0: sbfx            x3, x2, #1, #0x1f
    //     0x75aaf4: tbz             w2, #0, #0x75aafc
    //     0x75aaf8: ldur            x3, [x2, #7]
    // 0x75aafc: ldur            x1, [fp, #-0x10]
    // 0x75ab00: ldur            x2, [fp, #-0x18]
    // 0x75ab04: mov             x5, x0
    // 0x75ab08: r0 = _getSumDelta()
    //     0x75ab08: bl              #0x75aba0  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_getSumDelta
    // 0x75ab0c: ldur            x1, [fp, #-0x10]
    // 0x75ab10: ldur            x2, [fp, #-0x18]
    // 0x75ab14: ldur            x3, [fp, #-0x20]
    // 0x75ab18: ldur            x5, [fp, #-8]
    // 0x75ab1c: stur            d0, [fp, #-0x30]
    // 0x75ab20: r0 = _getSumDelta()
    //     0x75ab20: bl              #0x75aba0  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_getSumDelta
    // 0x75ab24: ldur            x0, [fp, #-8]
    // 0x75ab28: tbnz            w0, #4, #0x75ab60
    // 0x75ab2c: ldur            d2, [fp, #-0x28]
    // 0x75ab30: ldur            d1, [fp, #-0x30]
    // 0x75ab34: fadd            d3, d0, d2
    // 0x75ab38: fcmp            d3, d1
    // 0x75ab3c: b.le            #0x75ab50
    // 0x75ab40: fsub            d0, d3, d1
    // 0x75ab44: LeaveFrame
    //     0x75ab44: mov             SP, fp
    //     0x75ab48: ldp             fp, lr, [SP], #0x10
    // 0x75ab4c: ret
    //     0x75ab4c: ret             
    // 0x75ab50: d0 = 0.000000
    //     0x75ab50: eor             v0.16b, v0.16b, v0.16b
    // 0x75ab54: LeaveFrame
    //     0x75ab54: mov             SP, fp
    //     0x75ab58: ldp             fp, lr, [SP], #0x10
    // 0x75ab5c: ret
    //     0x75ab5c: ret             
    // 0x75ab60: ldur            d2, [fp, #-0x28]
    // 0x75ab64: ldur            d1, [fp, #-0x30]
    // 0x75ab68: fadd            d3, d0, d2
    // 0x75ab6c: fcmp            d1, d3
    // 0x75ab70: b.le            #0x75ab84
    // 0x75ab74: fsub            d0, d3, d1
    // 0x75ab78: LeaveFrame
    //     0x75ab78: mov             SP, fp
    //     0x75ab7c: ldp             fp, lr, [SP], #0x10
    // 0x75ab80: ret
    //     0x75ab80: ret             
    // 0x75ab84: d0 = 0.000000
    //     0x75ab84: eor             v0.16b, v0.16b, v0.16b
    // 0x75ab88: LeaveFrame
    //     0x75ab88: mov             SP, fp
    //     0x75ab8c: ldp             fp, lr, [SP], #0x10
    // 0x75ab90: ret
    //     0x75ab90: ret             
    // 0x75ab94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75ab94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75ab98: b               #0x75a9f8
    // 0x75ab9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75ab9c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _getSumDelta(/* No info */) {
    // ** addr: 0x75aba0, size: 0x1d8
    // 0x75aba0: EnterFrame
    //     0x75aba0: stp             fp, lr, [SP, #-0x10]!
    //     0x75aba4: mov             fp, SP
    // 0x75aba8: AllocStack(0x20)
    //     0x75aba8: sub             SP, SP, #0x20
    // 0x75abac: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x75abac: mov             x4, x2
    //     0x75abb0: stur            x2, [fp, #-0x18]
    //     0x75abb4: stur            x5, [fp, #-0x20]
    // 0x75abb8: CheckStackOverflow
    //     0x75abb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75abbc: cmp             SP, x16
    //     0x75abc0: b.ls            #0x75ad6c
    // 0x75abc4: LoadField: r6 = r1->field_7b
    //     0x75abc4: ldur            w6, [x1, #0x7b]
    // 0x75abc8: DecompressPointer r6
    //     0x75abc8: add             x6, x6, HEAP, lsl #32
    // 0x75abcc: stur            x6, [fp, #-0x10]
    // 0x75abd0: r0 = BoxInt64Instr(r3)
    //     0x75abd0: sbfiz           x0, x3, #1, #0x1f
    //     0x75abd4: cmp             x3, x0, asr #1
    //     0x75abd8: b.eq            #0x75abe4
    //     0x75abdc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x75abe0: stur            x3, [x0, #7]
    // 0x75abe4: mov             x1, x6
    // 0x75abe8: mov             x2, x0
    // 0x75abec: stur            x0, [fp, #-8]
    // 0x75abf0: r0 = containsKey()
    //     0x75abf0: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x75abf4: tbz             w0, #4, #0x75ac08
    // 0x75abf8: d0 = 0.000000
    //     0x75abf8: eor             v0.16b, v0.16b, v0.16b
    // 0x75abfc: LeaveFrame
    //     0x75abfc: mov             SP, fp
    //     0x75ac00: ldp             fp, lr, [SP], #0x10
    // 0x75ac04: ret
    //     0x75ac04: ret             
    // 0x75ac08: ldur            x0, [fp, #-0x10]
    // 0x75ac0c: mov             x1, x0
    // 0x75ac10: ldur            x2, [fp, #-8]
    // 0x75ac14: r0 = _getValueOrData()
    //     0x75ac14: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x75ac18: mov             x1, x0
    // 0x75ac1c: ldur            x0, [fp, #-0x10]
    // 0x75ac20: LoadField: r2 = r0->field_f
    //     0x75ac20: ldur            w2, [x0, #0xf]
    // 0x75ac24: DecompressPointer r2
    //     0x75ac24: add             x2, x2, HEAP, lsl #32
    // 0x75ac28: cmp             w2, w1
    // 0x75ac2c: b.ne            #0x75ac34
    // 0x75ac30: r1 = Null
    //     0x75ac30: mov             x1, NULL
    // 0x75ac34: ldur            x0, [fp, #-0x20]
    // 0x75ac38: cmp             w1, NULL
    // 0x75ac3c: b.eq            #0x75ad74
    // 0x75ac40: tbnz            w0, #4, #0x75acc4
    // 0x75ac44: ldur            x0, [fp, #-0x18]
    // 0x75ac48: r16 = Instance__DragDirection
    //     0x75ac48: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c30] Obj!_DragDirection@e36d81
    //     0x75ac4c: ldr             x16, [x16, #0xc30]
    // 0x75ac50: cmp             w0, w16
    // 0x75ac54: b.ne            #0x75ac8c
    // 0x75ac58: d1 = 0.000000
    //     0x75ac58: eor             v1.16b, v1.16b, v1.16b
    // 0x75ac5c: LoadField: d2 = r1->field_f
    //     0x75ac5c: ldur            d2, [x1, #0xf]
    // 0x75ac60: fcmp            d2, d1
    // 0x75ac64: b.gt            #0x75acbc
    // 0x75ac68: fcmp            d1, d2
    // 0x75ac6c: b.le            #0x75ac78
    // 0x75ac70: d2 = 0.000000
    //     0x75ac70: eor             v2.16b, v2.16b, v2.16b
    // 0x75ac74: b               #0x75acbc
    // 0x75ac78: fcmp            d2, d1
    // 0x75ac7c: b.ne            #0x75acbc
    // 0x75ac80: fadd            d3, d2, d1
    // 0x75ac84: mov             v2.16b, v3.16b
    // 0x75ac88: b               #0x75acbc
    // 0x75ac8c: d1 = 0.000000
    //     0x75ac8c: eor             v1.16b, v1.16b, v1.16b
    // 0x75ac90: LoadField: d2 = r1->field_7
    //     0x75ac90: ldur            d2, [x1, #7]
    // 0x75ac94: fcmp            d2, d1
    // 0x75ac98: b.gt            #0x75acbc
    // 0x75ac9c: fcmp            d1, d2
    // 0x75aca0: b.le            #0x75acac
    // 0x75aca4: d2 = 0.000000
    //     0x75aca4: eor             v2.16b, v2.16b, v2.16b
    // 0x75aca8: b               #0x75acbc
    // 0x75acac: fcmp            d2, d1
    // 0x75acb0: b.ne            #0x75acbc
    // 0x75acb4: fadd            d3, d2, d1
    // 0x75acb8: mov             v2.16b, v3.16b
    // 0x75acbc: mov             v0.16b, v2.16b
    // 0x75acc0: b               #0x75ad60
    // 0x75acc4: ldur            x0, [fp, #-0x18]
    // 0x75acc8: d1 = 0.000000
    //     0x75acc8: eor             v1.16b, v1.16b, v1.16b
    // 0x75accc: r16 = Instance__DragDirection
    //     0x75accc: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c30] Obj!_DragDirection@e36d81
    //     0x75acd0: ldr             x16, [x16, #0xc30]
    // 0x75acd4: cmp             w0, w16
    // 0x75acd8: b.ne            #0x75ad18
    // 0x75acdc: LoadField: d2 = r1->field_f
    //     0x75acdc: ldur            d2, [x1, #0xf]
    // 0x75ace0: fcmp            d2, d1
    // 0x75ace4: b.le            #0x75acf0
    // 0x75ace8: d2 = 0.000000
    //     0x75ace8: eor             v2.16b, v2.16b, v2.16b
    // 0x75acec: b               #0x75ad10
    // 0x75acf0: fcmp            d1, d2
    // 0x75acf4: b.gt            #0x75ad10
    // 0x75acf8: fcmp            d2, d1
    // 0x75acfc: b.ne            #0x75ad10
    // 0x75ad00: fadd            d3, d2, d1
    // 0x75ad04: fmul            d4, d3, d2
    // 0x75ad08: fmul            d3, d4, d1
    // 0x75ad0c: mov             v2.16b, v3.16b
    // 0x75ad10: mov             v1.16b, v2.16b
    // 0x75ad14: b               #0x75ad5c
    // 0x75ad18: LoadField: d2 = r1->field_7
    //     0x75ad18: ldur            d2, [x1, #7]
    // 0x75ad1c: fcmp            d2, d1
    // 0x75ad20: b.le            #0x75ad2c
    // 0x75ad24: d1 = 0.000000
    //     0x75ad24: eor             v1.16b, v1.16b, v1.16b
    // 0x75ad28: b               #0x75ad5c
    // 0x75ad2c: fcmp            d1, d2
    // 0x75ad30: b.le            #0x75ad3c
    // 0x75ad34: mov             v1.16b, v2.16b
    // 0x75ad38: b               #0x75ad5c
    // 0x75ad3c: fcmp            d2, d1
    // 0x75ad40: b.ne            #0x75ad58
    // 0x75ad44: fadd            d3, d2, d1
    // 0x75ad48: fmul            d4, d3, d2
    // 0x75ad4c: fmul            d3, d4, d1
    // 0x75ad50: mov             v1.16b, v3.16b
    // 0x75ad54: b               #0x75ad5c
    // 0x75ad58: mov             v1.16b, v2.16b
    // 0x75ad5c: mov             v0.16b, v1.16b
    // 0x75ad60: LeaveFrame
    //     0x75ad60: mov             SP, fp
    //     0x75ad64: ldp             fp, lr, [SP], #0x10
    // 0x75ad68: ret
    //     0x75ad68: ret             
    // 0x75ad6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75ad6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75ad70: b               #0x75abc4
    // 0x75ad74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75ad74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _getMaxSumDeltaPointer(/* No info */) {
    // ** addr: 0x75ad78, size: 0x268
    // 0x75ad78: EnterFrame
    //     0x75ad78: stp             fp, lr, [SP, #-0x10]!
    //     0x75ad7c: mov             fp, SP
    // 0x75ad80: AllocStack(0x48)
    //     0x75ad80: sub             SP, SP, #0x48
    // 0x75ad84: SetupParameters(DragGestureRecognizer this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r5, fp-0x20 */)
    //     0x75ad84: mov             x0, x1
    //     0x75ad88: mov             x5, x3
    //     0x75ad8c: stur            x1, [fp, #-0x10]
    //     0x75ad90: stur            x2, [fp, #-0x18]
    //     0x75ad94: stur            x3, [fp, #-0x20]
    // 0x75ad98: CheckStackOverflow
    //     0x75ad98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75ad9c: cmp             SP, x16
    //     0x75ada0: b.ls            #0x75afac
    // 0x75ada4: LoadField: r3 = r0->field_7b
    //     0x75ada4: ldur            w3, [x0, #0x7b]
    // 0x75ada8: DecompressPointer r3
    //     0x75ada8: add             x3, x3, HEAP, lsl #32
    // 0x75adac: stur            x3, [fp, #-8]
    // 0x75adb0: LoadField: r1 = r3->field_13
    //     0x75adb0: ldur            w1, [x3, #0x13]
    // 0x75adb4: r4 = LoadInt32Instr(r1)
    //     0x75adb4: sbfx            x4, x1, #1, #0x1f
    // 0x75adb8: asr             x1, x4, #1
    // 0x75adbc: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x75adbc: ldur            w4, [x3, #0x17]
    // 0x75adc0: r6 = LoadInt32Instr(r4)
    //     0x75adc0: sbfx            x6, x4, #1, #0x1f
    // 0x75adc4: sub             x4, x1, x6
    // 0x75adc8: cbnz            x4, #0x75addc
    // 0x75adcc: r0 = Null
    //     0x75adcc: mov             x0, NULL
    // 0x75add0: LeaveFrame
    //     0x75add0: mov             SP, fp
    //     0x75add4: ldp             fp, lr, [SP], #0x10
    // 0x75add8: ret
    //     0x75add8: ret             
    // 0x75addc: LoadField: r1 = r3->field_7
    //     0x75addc: ldur            w1, [x3, #7]
    // 0x75ade0: DecompressPointer r1
    //     0x75ade0: add             x1, x1, HEAP, lsl #32
    // 0x75ade4: r0 = _CompactIterable()
    //     0x75ade4: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x75ade8: mov             x1, x0
    // 0x75adec: ldur            x0, [fp, #-8]
    // 0x75adf0: StoreField: r1->field_b = r0
    //     0x75adf0: stur            w0, [x1, #0xb]
    // 0x75adf4: r0 = -2
    //     0x75adf4: orr             x0, xzr, #0xfffffffffffffffe
    // 0x75adf8: StoreField: r1->field_f = r0
    //     0x75adf8: stur            x0, [x1, #0xf]
    // 0x75adfc: r0 = 2
    //     0x75adfc: movz            x0, #0x2
    // 0x75ae00: ArrayStore: r1[0] = r0  ; List_8
    //     0x75ae00: stur            x0, [x1, #0x17]
    // 0x75ae04: r0 = iterator()
    //     0x75ae04: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x75ae08: stur            x0, [fp, #-0x38]
    // 0x75ae0c: LoadField: r2 = r0->field_7
    //     0x75ae0c: ldur            w2, [x0, #7]
    // 0x75ae10: DecompressPointer r2
    //     0x75ae10: add             x2, x2, HEAP, lsl #32
    // 0x75ae14: stur            x2, [fp, #-0x30]
    // 0x75ae18: ldur            x5, [fp, #-0x20]
    // 0x75ae1c: r4 = Null
    //     0x75ae1c: mov             x4, NULL
    // 0x75ae20: r3 = Null
    //     0x75ae20: mov             x3, NULL
    // 0x75ae24: stur            x4, [fp, #-8]
    // 0x75ae28: stur            x3, [fp, #-0x28]
    // 0x75ae2c: CheckStackOverflow
    //     0x75ae2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75ae30: cmp             SP, x16
    //     0x75ae34: b.ls            #0x75afb4
    // 0x75ae38: mov             x1, x0
    // 0x75ae3c: r0 = moveNext()
    //     0x75ae3c: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x75ae40: tbnz            w0, #4, #0x75af9c
    // 0x75ae44: ldur            x3, [fp, #-0x38]
    // 0x75ae48: LoadField: r4 = r3->field_33
    //     0x75ae48: ldur            w4, [x3, #0x33]
    // 0x75ae4c: DecompressPointer r4
    //     0x75ae4c: add             x4, x4, HEAP, lsl #32
    // 0x75ae50: stur            x4, [fp, #-0x40]
    // 0x75ae54: cmp             w4, NULL
    // 0x75ae58: b.ne            #0x75ae8c
    // 0x75ae5c: mov             x0, x4
    // 0x75ae60: ldur            x2, [fp, #-0x30]
    // 0x75ae64: r1 = Null
    //     0x75ae64: mov             x1, NULL
    // 0x75ae68: cmp             w2, NULL
    // 0x75ae6c: b.eq            #0x75ae8c
    // 0x75ae70: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x75ae70: ldur            w4, [x2, #0x17]
    // 0x75ae74: DecompressPointer r4
    //     0x75ae74: add             x4, x4, HEAP, lsl #32
    // 0x75ae78: r8 = X0
    //     0x75ae78: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x75ae7c: LoadField: r9 = r4->field_7
    //     0x75ae7c: ldur            x9, [x4, #7]
    // 0x75ae80: r3 = Null
    //     0x75ae80: add             x3, PP, #0x39, lsl #12  ; [pp+0x39c48] Null
    //     0x75ae84: ldr             x3, [x3, #0xc48]
    // 0x75ae88: blr             x9
    // 0x75ae8c: ldur            x4, [fp, #-8]
    // 0x75ae90: ldur            x0, [fp, #-0x40]
    // 0x75ae94: r6 = LoadInt32Instr(r0)
    //     0x75ae94: sbfx            x6, x0, #1, #0x1f
    //     0x75ae98: tbz             w0, #0, #0x75aea0
    //     0x75ae9c: ldur            x6, [x0, #7]
    // 0x75aea0: ldur            x1, [fp, #-0x10]
    // 0x75aea4: ldur            x2, [fp, #-0x18]
    // 0x75aea8: mov             x3, x6
    // 0x75aeac: ldur            x5, [fp, #-0x20]
    // 0x75aeb0: stur            x6, [fp, #-0x48]
    // 0x75aeb4: r0 = _getSumDelta()
    //     0x75aeb4: bl              #0x75aba0  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_getSumDelta
    // 0x75aeb8: ldur            x0, [fp, #-8]
    // 0x75aebc: cmp             w0, NULL
    // 0x75aec0: b.ne            #0x75aed0
    // 0x75aec4: ldur            x3, [fp, #-0x48]
    // 0x75aec8: ldur            x2, [fp, #-0x20]
    // 0x75aecc: b               #0x75af4c
    // 0x75aed0: ldur            x2, [fp, #-0x20]
    // 0x75aed4: tbnz            w2, #4, #0x75af14
    // 0x75aed8: ldur            x3, [fp, #-0x28]
    // 0x75aedc: cmp             w3, NULL
    // 0x75aee0: b.eq            #0x75afbc
    // 0x75aee4: LoadField: d1 = r3->field_7
    //     0x75aee4: ldur            d1, [x3, #7]
    // 0x75aee8: fcmp            d0, d1
    // 0x75aeec: b.le            #0x75aefc
    // 0x75aef0: ldur            x0, [fp, #-0x48]
    // 0x75aef4: mov             v1.16b, v0.16b
    // 0x75aef8: b               #0x75af0c
    // 0x75aefc: r1 = LoadInt32Instr(r0)
    //     0x75aefc: sbfx            x1, x0, #1, #0x1f
    //     0x75af00: tbz             w0, #0, #0x75af08
    //     0x75af04: ldur            x1, [x0, #7]
    // 0x75af08: mov             x0, x1
    // 0x75af0c: mov             v0.16b, v1.16b
    // 0x75af10: b               #0x75af48
    // 0x75af14: ldur            x3, [fp, #-0x28]
    // 0x75af18: cmp             w3, NULL
    // 0x75af1c: b.eq            #0x75afc0
    // 0x75af20: LoadField: d1 = r3->field_7
    //     0x75af20: ldur            d1, [x3, #7]
    // 0x75af24: fcmp            d1, d0
    // 0x75af28: b.le            #0x75af34
    // 0x75af2c: ldur            x0, [fp, #-0x48]
    // 0x75af30: b               #0x75af48
    // 0x75af34: r1 = LoadInt32Instr(r0)
    //     0x75af34: sbfx            x1, x0, #1, #0x1f
    //     0x75af38: tbz             w0, #0, #0x75af40
    //     0x75af3c: ldur            x1, [x0, #7]
    // 0x75af40: mov             x0, x1
    // 0x75af44: mov             v0.16b, v1.16b
    // 0x75af48: mov             x3, x0
    // 0x75af4c: r0 = BoxInt64Instr(r3)
    //     0x75af4c: sbfiz           x0, x3, #1, #0x1f
    //     0x75af50: cmp             x3, x0, asr #1
    //     0x75af54: b.eq            #0x75af60
    //     0x75af58: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0x75af5c: stur            x3, [x0, #7]
    // 0x75af60: r3 = inline_Allocate_Double()
    //     0x75af60: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0x75af64: add             x3, x3, #0x10
    //     0x75af68: cmp             x1, x3
    //     0x75af6c: b.ls            #0x75afc4
    //     0x75af70: str             x3, [THR, #0x50]  ; THR::top
    //     0x75af74: sub             x3, x3, #0xf
    //     0x75af78: movz            x1, #0xe15c
    //     0x75af7c: movk            x1, #0x3, lsl #16
    //     0x75af80: stur            x1, [x3, #-1]
    // 0x75af84: StoreField: r3->field_7 = d0
    //     0x75af84: stur            d0, [x3, #7]
    // 0x75af88: mov             x4, x0
    // 0x75af8c: mov             x5, x2
    // 0x75af90: ldur            x0, [fp, #-0x38]
    // 0x75af94: ldur            x2, [fp, #-0x30]
    // 0x75af98: b               #0x75ae24
    // 0x75af9c: ldur            x0, [fp, #-8]
    // 0x75afa0: LeaveFrame
    //     0x75afa0: mov             SP, fp
    //     0x75afa4: ldp             fp, lr, [SP], #0x10
    // 0x75afa8: ret
    //     0x75afa8: ret             
    // 0x75afac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75afac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75afb0: b               #0x75ada4
    // 0x75afb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75afb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75afb8: b               #0x75ae38
    // 0x75afbc: r0 = NullCastErrorSharedWithFPURegs()
    //     0x75afbc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x75afc0: r0 = NullCastErrorSharedWithFPURegs()
    //     0x75afc0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x75afc4: SaveReg d0
    //     0x75afc4: str             q0, [SP, #-0x10]!
    // 0x75afc8: stp             x0, x2, [SP, #-0x10]!
    // 0x75afcc: r0 = AllocateDouble()
    //     0x75afcc: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75afd0: mov             x3, x0
    // 0x75afd4: ldp             x0, x2, [SP], #0x10
    // 0x75afd8: RestoreReg d0
    //     0x75afd8: ldr             q0, [SP], #0x10
    // 0x75afdc: b               #0x75af84
  }
  _ _giveUpPointer(/* No info */) {
    // ** addr: 0x75afec, size: 0x134
    // 0x75afec: EnterFrame
    //     0x75afec: stp             fp, lr, [SP, #-0x10]!
    //     0x75aff0: mov             fp, SP
    // 0x75aff4: AllocStack(0x20)
    //     0x75aff4: sub             SP, SP, #0x20
    // 0x75aff8: SetupParameters(DragGestureRecognizer this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x75aff8: mov             x4, x1
    //     0x75affc: mov             x3, x2
    //     0x75b000: stur            x1, [fp, #-0x10]
    //     0x75b004: stur            x2, [fp, #-0x18]
    // 0x75b008: CheckStackOverflow
    //     0x75b008: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75b00c: cmp             SP, x16
    //     0x75b010: b.ls            #0x75b118
    // 0x75b014: r0 = BoxInt64Instr(r3)
    //     0x75b014: sbfiz           x0, x3, #1, #0x1f
    //     0x75b018: cmp             x3, x0, asr #1
    //     0x75b01c: b.eq            #0x75b028
    //     0x75b020: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x75b024: stur            x3, [x0, #7]
    // 0x75b028: mov             x1, x4
    // 0x75b02c: mov             x2, x0
    // 0x75b030: stur            x0, [fp, #-8]
    // 0x75b034: r0 = stopTrackingPointer()
    //     0x75b034: bl              #0x7587c8  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::stopTrackingPointer
    // 0x75b038: ldur            x0, [fp, #-0x10]
    // 0x75b03c: LoadField: r3 = r0->field_87
    //     0x75b03c: ldur            w3, [x0, #0x87]
    // 0x75b040: DecompressPointer r3
    //     0x75b040: add             x3, x3, HEAP, lsl #32
    // 0x75b044: mov             x1, x3
    // 0x75b048: ldur            x2, [fp, #-8]
    // 0x75b04c: stur            x3, [fp, #-0x20]
    // 0x75b050: r0 = remove()
    //     0x75b050: bl              #0x6ec83c  ; [dart:core] _GrowableList::remove
    // 0x75b054: tbz             w0, #4, #0x75b064
    // 0x75b058: ldur            x1, [fp, #-0x10]
    // 0x75b05c: ldur            x2, [fp, #-0x18]
    // 0x75b060: r0 = resolvePointer()
    //     0x75b060: bl              #0x75b120  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolvePointer
    // 0x75b064: ldur            x0, [fp, #-0x10]
    // 0x75b068: ldur            x3, [fp, #-8]
    // 0x75b06c: LoadField: r1 = r0->field_7b
    //     0x75b06c: ldur            w1, [x0, #0x7b]
    // 0x75b070: DecompressPointer r1
    //     0x75b070: add             x1, x1, HEAP, lsl #32
    // 0x75b074: mov             x2, x3
    // 0x75b078: r0 = remove()
    //     0x75b078: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x75b07c: ldur            x0, [fp, #-0x10]
    // 0x75b080: LoadField: r1 = r0->field_8b
    //     0x75b080: ldur            w1, [x0, #0x8b]
    // 0x75b084: DecompressPointer r1
    //     0x75b084: add             x1, x1, HEAP, lsl #32
    // 0x75b088: ldur            x2, [fp, #-8]
    // 0x75b08c: cmp             w1, w2
    // 0x75b090: b.eq            #0x75b0cc
    // 0x75b094: and             w16, w1, w2
    // 0x75b098: branchIfSmi(r16, 0x75b108)
    //     0x75b098: tbz             w16, #0, #0x75b108
    // 0x75b09c: r16 = LoadClassIdInstr(r1)
    //     0x75b09c: ldur            x16, [x1, #-1]
    //     0x75b0a0: ubfx            x16, x16, #0xc, #0x14
    // 0x75b0a4: cmp             x16, #0x3d
    // 0x75b0a8: b.ne            #0x75b108
    // 0x75b0ac: r16 = LoadClassIdInstr(r2)
    //     0x75b0ac: ldur            x16, [x2, #-1]
    //     0x75b0b0: ubfx            x16, x16, #0xc, #0x14
    // 0x75b0b4: cmp             x16, #0x3d
    // 0x75b0b8: b.ne            #0x75b108
    // 0x75b0bc: LoadField: r16 = r1->field_7
    //     0x75b0bc: ldur            x16, [x1, #7]
    // 0x75b0c0: LoadField: r17 = r2->field_7
    //     0x75b0c0: ldur            x17, [x2, #7]
    // 0x75b0c4: cmp             x16, x17
    // 0x75b0c8: b.ne            #0x75b108
    // 0x75b0cc: ldur            x1, [fp, #-0x20]
    // 0x75b0d0: LoadField: r2 = r1->field_b
    //     0x75b0d0: ldur            w2, [x1, #0xb]
    // 0x75b0d4: cbz             w2, #0x75b0e0
    // 0x75b0d8: r0 = first()
    //     0x75b0d8: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x75b0dc: b               #0x75b0e4
    // 0x75b0e0: r0 = Null
    //     0x75b0e0: mov             x0, NULL
    // 0x75b0e4: ldur            x1, [fp, #-0x10]
    // 0x75b0e8: StoreField: r1->field_8b = r0
    //     0x75b0e8: stur            w0, [x1, #0x8b]
    //     0x75b0ec: tbz             w0, #0, #0x75b108
    //     0x75b0f0: ldurb           w16, [x1, #-1]
    //     0x75b0f4: ldurb           w17, [x0, #-1]
    //     0x75b0f8: and             x16, x17, x16, lsr #2
    //     0x75b0fc: tst             x16, HEAP, lsr #32
    //     0x75b100: b.eq            #0x75b108
    //     0x75b104: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x75b108: r0 = Null
    //     0x75b108: mov             x0, NULL
    // 0x75b10c: LeaveFrame
    //     0x75b10c: mov             SP, fp
    //     0x75b110: ldp             fp, lr, [SP], #0x10
    // 0x75b114: ret
    //     0x75b114: ret             
    // 0x75b118: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75b118: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75b11c: b               #0x75b014
  }
  _ addAllowedPointerPanZoom(/* No info */) {
    // ** addr: 0x7cd058, size: 0xbc
    // 0x7cd058: EnterFrame
    //     0x7cd058: stp             fp, lr, [SP, #-0x10]!
    //     0x7cd05c: mov             fp, SP
    // 0x7cd060: AllocStack(0x18)
    //     0x7cd060: sub             SP, SP, #0x18
    // 0x7cd064: SetupParameters(DragGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x7cd064: mov             x3, x1
    //     0x7cd068: stur            x1, [fp, #-8]
    //     0x7cd06c: stur            x2, [fp, #-0x10]
    // 0x7cd070: CheckStackOverflow
    //     0x7cd070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cd074: cmp             SP, x16
    //     0x7cd078: b.ls            #0x7cd10c
    // 0x7cd07c: r0 = LoadClassIdInstr(r2)
    //     0x7cd07c: ldur            x0, [x2, #-1]
    //     0x7cd080: ubfx            x0, x0, #0xc, #0x14
    // 0x7cd084: mov             x1, x2
    // 0x7cd088: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7cd088: sub             lr, x0, #1, lsl #12
    //     0x7cd08c: ldr             lr, [x21, lr, lsl #3]
    //     0x7cd090: blr             lr
    // 0x7cd094: mov             x3, x0
    // 0x7cd098: ldur            x2, [fp, #-0x10]
    // 0x7cd09c: stur            x3, [fp, #-0x18]
    // 0x7cd0a0: r0 = LoadClassIdInstr(r2)
    //     0x7cd0a0: ldur            x0, [x2, #-1]
    //     0x7cd0a4: ubfx            x0, x0, #0xc, #0x14
    // 0x7cd0a8: mov             x1, x2
    // 0x7cd0ac: r0 = GDT[cid_x0 + 0x131b9]()
    //     0x7cd0ac: movz            x17, #0x31b9
    //     0x7cd0b0: movk            x17, #0x1, lsl #16
    //     0x7cd0b4: add             lr, x0, x17
    //     0x7cd0b8: ldr             lr, [x21, lr, lsl #3]
    //     0x7cd0bc: blr             lr
    // 0x7cd0c0: ldur            x1, [fp, #-8]
    // 0x7cd0c4: ldur            x2, [fp, #-0x18]
    // 0x7cd0c8: mov             x3, x0
    // 0x7cd0cc: r0 = startTrackingPointer()
    //     0x7cd0cc: bl              #0x7ab0d8  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::startTrackingPointer
    // 0x7cd0d0: ldur            x1, [fp, #-8]
    // 0x7cd0d4: LoadField: r0 = r1->field_53
    //     0x7cd0d4: ldur            w0, [x1, #0x53]
    // 0x7cd0d8: DecompressPointer r0
    //     0x7cd0d8: add             x0, x0, HEAP, lsl #32
    // 0x7cd0dc: r16 = Instance__DragState
    //     0x7cd0dc: add             x16, PP, #0x25, lsl #12  ; [pp+0x25390] Obj!_DragState@e36e01
    //     0x7cd0e0: ldr             x16, [x16, #0x390]
    // 0x7cd0e4: cmp             w0, w16
    // 0x7cd0e8: b.ne            #0x7cd0f4
    // 0x7cd0ec: r0 = 2
    //     0x7cd0ec: movz            x0, #0x2
    // 0x7cd0f0: StoreField: r1->field_67 = r0
    //     0x7cd0f0: stur            w0, [x1, #0x67]
    // 0x7cd0f4: ldur            x2, [fp, #-0x10]
    // 0x7cd0f8: r0 = _addPointer()
    //     0x7cd0f8: bl              #0x7cd114  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_addPointer
    // 0x7cd0fc: r0 = Null
    //     0x7cd0fc: mov             x0, NULL
    // 0x7cd100: LeaveFrame
    //     0x7cd100: mov             SP, fp
    //     0x7cd104: ldp             fp, lr, [SP], #0x10
    // 0x7cd108: ret
    //     0x7cd108: ret             
    // 0x7cd10c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cd10c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cd110: b               #0x7cd07c
  }
  _ _addPointer(/* No info */) {
    // ** addr: 0x7cd114, size: 0x234
    // 0x7cd114: EnterFrame
    //     0x7cd114: stp             fp, lr, [SP, #-0x10]!
    //     0x7cd118: mov             fp, SP
    // 0x7cd11c: AllocStack(0x38)
    //     0x7cd11c: sub             SP, SP, #0x38
    // 0x7cd120: SetupParameters(DragGestureRecognizer this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x7cd120: mov             x3, x1
    //     0x7cd124: stur            x1, [fp, #-0x10]
    //     0x7cd128: stur            x2, [fp, #-0x18]
    // 0x7cd12c: CheckStackOverflow
    //     0x7cd12c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cd130: cmp             SP, x16
    //     0x7cd134: b.ls            #0x7cd340
    // 0x7cd138: LoadField: r4 = r3->field_77
    //     0x7cd138: ldur            w4, [x3, #0x77]
    // 0x7cd13c: DecompressPointer r4
    //     0x7cd13c: add             x4, x4, HEAP, lsl #32
    // 0x7cd140: stur            x4, [fp, #-8]
    // 0x7cd144: r0 = LoadClassIdInstr(r2)
    //     0x7cd144: ldur            x0, [x2, #-1]
    //     0x7cd148: ubfx            x0, x0, #0xc, #0x14
    // 0x7cd14c: mov             x1, x2
    // 0x7cd150: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7cd150: sub             lr, x0, #1, lsl #12
    //     0x7cd154: ldr             lr, [x21, lr, lsl #3]
    //     0x7cd158: blr             lr
    // 0x7cd15c: mov             x2, x0
    // 0x7cd160: ldur            x1, [fp, #-0x10]
    // 0x7cd164: stur            x2, [fp, #-0x20]
    // 0x7cd168: LoadField: r0 = r1->field_4f
    //     0x7cd168: ldur            w0, [x1, #0x4f]
    // 0x7cd16c: DecompressPointer r0
    //     0x7cd16c: add             x0, x0, HEAP, lsl #32
    // 0x7cd170: ldur            x16, [fp, #-0x18]
    // 0x7cd174: stp             x16, x0, [SP]
    // 0x7cd178: ClosureCall
    //     0x7cd178: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x7cd17c: ldur            x2, [x0, #0x1f]
    //     0x7cd180: blr             x2
    // 0x7cd184: mov             x3, x0
    // 0x7cd188: ldur            x2, [fp, #-0x20]
    // 0x7cd18c: r0 = BoxInt64Instr(r2)
    //     0x7cd18c: sbfiz           x0, x2, #1, #0x1f
    //     0x7cd190: cmp             x2, x0, asr #1
    //     0x7cd194: b.eq            #0x7cd1a0
    //     0x7cd198: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7cd19c: stur            x2, [x0, #7]
    // 0x7cd1a0: ldur            x1, [fp, #-8]
    // 0x7cd1a4: mov             x2, x0
    // 0x7cd1a8: r0 = []=()
    //     0x7cd1a8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cd1ac: ldur            x2, [fp, #-0x10]
    // 0x7cd1b0: LoadField: r0 = r2->field_53
    //     0x7cd1b0: ldur            w0, [x2, #0x53]
    // 0x7cd1b4: DecompressPointer r0
    //     0x7cd1b4: add             x0, x0, HEAP, lsl #32
    // 0x7cd1b8: LoadField: r1 = r0->field_7
    //     0x7cd1b8: ldur            x1, [x0, #7]
    // 0x7cd1bc: cmp             x1, #1
    // 0x7cd1c0: b.gt            #0x7cd320
    // 0x7cd1c4: cmp             x1, #0
    // 0x7cd1c8: b.gt            #0x7cd330
    // 0x7cd1cc: ldur            x3, [fp, #-0x18]
    // 0x7cd1d0: r0 = Instance__DragState
    //     0x7cd1d0: add             x0, PP, #0x39, lsl #12  ; [pp+0x39c10] Obj!_DragState@e36dc1
    //     0x7cd1d4: ldr             x0, [x0, #0xc10]
    // 0x7cd1d8: StoreField: r2->field_53 = r0
    //     0x7cd1d8: stur            w0, [x2, #0x53]
    // 0x7cd1dc: r0 = LoadClassIdInstr(r3)
    //     0x7cd1dc: ldur            x0, [x3, #-1]
    //     0x7cd1e0: ubfx            x0, x0, #0xc, #0x14
    // 0x7cd1e4: mov             x1, x3
    // 0x7cd1e8: r0 = GDT[cid_x0 + -0x1]()
    //     0x7cd1e8: sub             lr, x0, #1
    //     0x7cd1ec: ldr             lr, [x21, lr, lsl #3]
    //     0x7cd1f0: blr             lr
    // 0x7cd1f4: mov             x3, x0
    // 0x7cd1f8: ldur            x2, [fp, #-0x18]
    // 0x7cd1fc: stur            x3, [fp, #-8]
    // 0x7cd200: r0 = LoadClassIdInstr(r2)
    //     0x7cd200: ldur            x0, [x2, #-1]
    //     0x7cd204: ubfx            x0, x0, #0xc, #0x14
    // 0x7cd208: mov             x1, x2
    // 0x7cd20c: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x7cd20c: movz            x17, #0x30fa
    //     0x7cd210: movk            x17, #0x1, lsl #16
    //     0x7cd214: add             lr, x0, x17
    //     0x7cd218: ldr             lr, [x21, lr, lsl #3]
    //     0x7cd21c: blr             lr
    // 0x7cd220: stur            x0, [fp, #-0x28]
    // 0x7cd224: r0 = OffsetPair()
    //     0x7cd224: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x7cd228: mov             x1, x0
    // 0x7cd22c: ldur            x0, [fp, #-0x28]
    // 0x7cd230: StoreField: r1->field_7 = r0
    //     0x7cd230: stur            w0, [x1, #7]
    // 0x7cd234: ldur            x0, [fp, #-8]
    // 0x7cd238: StoreField: r1->field_b = r0
    //     0x7cd238: stur            w0, [x1, #0xb]
    // 0x7cd23c: mov             x0, x1
    // 0x7cd240: ldur            x2, [fp, #-0x10]
    // 0x7cd244: StoreField: r2->field_57 = r0
    //     0x7cd244: stur            w0, [x2, #0x57]
    //     0x7cd248: ldurb           w16, [x2, #-1]
    //     0x7cd24c: ldurb           w17, [x0, #-1]
    //     0x7cd250: and             x16, x17, x16, lsr #2
    //     0x7cd254: tst             x16, HEAP, lsr #32
    //     0x7cd258: b.eq            #0x7cd260
    //     0x7cd25c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7cd260: mov             x0, x1
    // 0x7cd264: StoreField: r2->field_5f = r0
    //     0x7cd264: stur            w0, [x2, #0x5f]
    //     0x7cd268: ldurb           w16, [x2, #-1]
    //     0x7cd26c: ldurb           w17, [x0, #-1]
    //     0x7cd270: and             x16, x17, x16, lsr #2
    //     0x7cd274: tst             x16, HEAP, lsr #32
    //     0x7cd278: b.eq            #0x7cd280
    //     0x7cd27c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7cd280: r0 = Instance_OffsetPair
    //     0x7cd280: add             x0, PP, #0x30, lsl #12  ; [pp+0x30df8] Obj!OffsetPair@e14a41
    //     0x7cd284: ldr             x0, [x0, #0xdf8]
    // 0x7cd288: StoreField: r2->field_5b = r0
    //     0x7cd288: stur            w0, [x2, #0x5b]
    // 0x7cd28c: r0 = 0.000000
    //     0x7cd28c: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x7cd290: StoreField: r2->field_6f = r0
    //     0x7cd290: stur            w0, [x2, #0x6f]
    // 0x7cd294: ldur            x3, [fp, #-0x18]
    // 0x7cd298: r0 = LoadClassIdInstr(r3)
    //     0x7cd298: ldur            x0, [x3, #-1]
    //     0x7cd29c: ubfx            x0, x0, #0xc, #0x14
    // 0x7cd2a0: mov             x1, x3
    // 0x7cd2a4: r0 = GDT[cid_x0 + 0x130f9]()
    //     0x7cd2a4: movz            x17, #0x30f9
    //     0x7cd2a8: movk            x17, #0x1, lsl #16
    //     0x7cd2ac: add             lr, x0, x17
    //     0x7cd2b0: ldr             lr, [x21, lr, lsl #3]
    //     0x7cd2b4: blr             lr
    // 0x7cd2b8: ldur            x2, [fp, #-0x10]
    // 0x7cd2bc: StoreField: r2->field_63 = r0
    //     0x7cd2bc: stur            w0, [x2, #0x63]
    //     0x7cd2c0: ldurb           w16, [x2, #-1]
    //     0x7cd2c4: ldurb           w17, [x0, #-1]
    //     0x7cd2c8: and             x16, x17, x16, lsr #2
    //     0x7cd2cc: tst             x16, HEAP, lsr #32
    //     0x7cd2d0: b.eq            #0x7cd2d8
    //     0x7cd2d4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7cd2d8: ldur            x1, [fp, #-0x18]
    // 0x7cd2dc: r0 = LoadClassIdInstr(r1)
    //     0x7cd2dc: ldur            x0, [x1, #-1]
    //     0x7cd2e0: ubfx            x0, x0, #0xc, #0x14
    // 0x7cd2e4: r0 = GDT[cid_x0 + 0x131b9]()
    //     0x7cd2e4: movz            x17, #0x31b9
    //     0x7cd2e8: movk            x17, #0x1, lsl #16
    //     0x7cd2ec: add             lr, x0, x17
    //     0x7cd2f0: ldr             lr, [x21, lr, lsl #3]
    //     0x7cd2f4: blr             lr
    // 0x7cd2f8: ldur            x1, [fp, #-0x10]
    // 0x7cd2fc: StoreField: r1->field_6b = r0
    //     0x7cd2fc: stur            w0, [x1, #0x6b]
    //     0x7cd300: ldurb           w16, [x1, #-1]
    //     0x7cd304: ldurb           w17, [x0, #-1]
    //     0x7cd308: and             x16, x17, x16, lsr #2
    //     0x7cd30c: tst             x16, HEAP, lsr #32
    //     0x7cd310: b.eq            #0x7cd318
    //     0x7cd314: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7cd318: r0 = _checkDown()
    //     0x7cd318: bl              #0x7cd348  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_checkDown
    // 0x7cd31c: b               #0x7cd330
    // 0x7cd320: mov             x1, x2
    // 0x7cd324: r2 = Instance_GestureDisposition
    //     0x7cd324: add             x2, PP, #0x30, lsl #12  ; [pp+0x30e00] Obj!GestureDisposition@e36ee1
    //     0x7cd328: ldr             x2, [x2, #0xe00]
    // 0x7cd32c: r0 = resolve()
    //     0x7cd32c: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x7cd330: r0 = Null
    //     0x7cd330: mov             x0, NULL
    // 0x7cd334: LeaveFrame
    //     0x7cd334: mov             SP, fp
    //     0x7cd338: ldp             fp, lr, [SP], #0x10
    // 0x7cd33c: ret
    //     0x7cd33c: ret             
    // 0x7cd340: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cd340: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cd344: b               #0x7cd138
  }
  _ _checkDown(/* No info */) {
    // ** addr: 0x7cd348, size: 0xc4
    // 0x7cd348: EnterFrame
    //     0x7cd348: stp             fp, lr, [SP, #-0x10]!
    //     0x7cd34c: mov             fp, SP
    // 0x7cd350: AllocStack(0x30)
    //     0x7cd350: sub             SP, SP, #0x30
    // 0x7cd354: SetupParameters(DragGestureRecognizer this /* r1 => r1, fp-0x8 */)
    //     0x7cd354: stur            x1, [fp, #-8]
    // 0x7cd358: CheckStackOverflow
    //     0x7cd358: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cd35c: cmp             SP, x16
    //     0x7cd360: b.ls            #0x7cd3f8
    // 0x7cd364: r1 = 2
    //     0x7cd364: movz            x1, #0x2
    // 0x7cd368: r0 = AllocateContext()
    //     0x7cd368: bl              #0xec126c  ; AllocateContextStub
    // 0x7cd36c: mov             x1, x0
    // 0x7cd370: ldur            x0, [fp, #-8]
    // 0x7cd374: stur            x1, [fp, #-0x18]
    // 0x7cd378: StoreField: r1->field_f = r0
    //     0x7cd378: stur            w0, [x1, #0xf]
    // 0x7cd37c: LoadField: r2 = r0->field_2b
    //     0x7cd37c: ldur            w2, [x0, #0x2b]
    // 0x7cd380: DecompressPointer r2
    //     0x7cd380: add             x2, x2, HEAP, lsl #32
    // 0x7cd384: cmp             w2, NULL
    // 0x7cd388: b.eq            #0x7cd3e8
    // 0x7cd38c: LoadField: r2 = r0->field_57
    //     0x7cd38c: ldur            w2, [x0, #0x57]
    // 0x7cd390: DecompressPointer r2
    //     0x7cd390: add             x2, x2, HEAP, lsl #32
    // 0x7cd394: r16 = Sentinel
    //     0x7cd394: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7cd398: cmp             w2, w16
    // 0x7cd39c: b.eq            #0x7cd400
    // 0x7cd3a0: LoadField: r3 = r2->field_7
    //     0x7cd3a0: ldur            w3, [x2, #7]
    // 0x7cd3a4: DecompressPointer r3
    //     0x7cd3a4: add             x3, x3, HEAP, lsl #32
    // 0x7cd3a8: stur            x3, [fp, #-0x10]
    // 0x7cd3ac: r0 = DragDownDetails()
    //     0x7cd3ac: bl              #0x7cd40c  ; AllocateDragDownDetailsStub -> DragDownDetails (size=0xc)
    // 0x7cd3b0: mov             x1, x0
    // 0x7cd3b4: ldur            x0, [fp, #-0x10]
    // 0x7cd3b8: StoreField: r1->field_7 = r0
    //     0x7cd3b8: stur            w0, [x1, #7]
    // 0x7cd3bc: ldur            x2, [fp, #-0x18]
    // 0x7cd3c0: StoreField: r2->field_13 = r1
    //     0x7cd3c0: stur            w1, [x2, #0x13]
    // 0x7cd3c4: r1 = Function '<anonymous closure>':.
    //     0x7cd3c4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a78] AnonymousClosure: (0x7cd418), in [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_checkDown (0x7cd348)
    //     0x7cd3c8: ldr             x1, [x1, #0xa78]
    // 0x7cd3cc: r0 = AllocateClosure()
    //     0x7cd3cc: bl              #0xec1630  ; AllocateClosureStub
    // 0x7cd3d0: r16 = <void?>
    //     0x7cd3d0: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x7cd3d4: ldur            lr, [fp, #-8]
    // 0x7cd3d8: stp             lr, x16, [SP, #8]
    // 0x7cd3dc: str             x0, [SP]
    // 0x7cd3e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7cd3e0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7cd3e4: r0 = invokeCallback()
    //     0x7cd3e4: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x7cd3e8: r0 = Null
    //     0x7cd3e8: mov             x0, NULL
    // 0x7cd3ec: LeaveFrame
    //     0x7cd3ec: mov             SP, fp
    //     0x7cd3f0: ldp             fp, lr, [SP], #0x10
    // 0x7cd3f4: ret
    //     0x7cd3f4: ret             
    // 0x7cd3f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cd3f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cd3fc: b               #0x7cd364
    // 0x7cd400: r9 = _initialPosition
    //     0x7cd400: add             x9, PP, #0x30, lsl #12  ; [pp+0x30e10] Field <DragGestureRecognizer._initialPosition@428099969>: late (offset: 0x58)
    //     0x7cd404: ldr             x9, [x9, #0xe10]
    // 0x7cd408: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7cd408: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x7cd418, size: 0x70
    // 0x7cd418: EnterFrame
    //     0x7cd418: stp             fp, lr, [SP, #-0x10]!
    //     0x7cd41c: mov             fp, SP
    // 0x7cd420: AllocStack(0x10)
    //     0x7cd420: sub             SP, SP, #0x10
    // 0x7cd424: SetupParameters()
    //     0x7cd424: ldr             x0, [fp, #0x10]
    //     0x7cd428: ldur            w1, [x0, #0x17]
    //     0x7cd42c: add             x1, x1, HEAP, lsl #32
    // 0x7cd430: CheckStackOverflow
    //     0x7cd430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cd434: cmp             SP, x16
    //     0x7cd438: b.ls            #0x7cd47c
    // 0x7cd43c: LoadField: r0 = r1->field_f
    //     0x7cd43c: ldur            w0, [x1, #0xf]
    // 0x7cd440: DecompressPointer r0
    //     0x7cd440: add             x0, x0, HEAP, lsl #32
    // 0x7cd444: LoadField: r2 = r0->field_2b
    //     0x7cd444: ldur            w2, [x0, #0x2b]
    // 0x7cd448: DecompressPointer r2
    //     0x7cd448: add             x2, x2, HEAP, lsl #32
    // 0x7cd44c: cmp             w2, NULL
    // 0x7cd450: b.eq            #0x7cd484
    // 0x7cd454: LoadField: r0 = r1->field_13
    //     0x7cd454: ldur            w0, [x1, #0x13]
    // 0x7cd458: DecompressPointer r0
    //     0x7cd458: add             x0, x0, HEAP, lsl #32
    // 0x7cd45c: stp             x0, x2, [SP]
    // 0x7cd460: mov             x0, x2
    // 0x7cd464: ClosureCall
    //     0x7cd464: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x7cd468: ldur            x2, [x0, #0x1f]
    //     0x7cd46c: blr             x2
    // 0x7cd470: LeaveFrame
    //     0x7cd470: mov             SP, fp
    //     0x7cd474: ldp             fp, lr, [SP], #0x10
    // 0x7cd478: ret
    //     0x7cd478: ret             
    // 0x7cd47c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cd47c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cd480: b               #0x7cd43c
    // 0x7cd484: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7cd484: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didStopTrackingLastPointer(/* No info */) {
    // ** addr: 0x7d6b80, size: 0xa8
    // 0x7d6b80: EnterFrame
    //     0x7d6b80: stp             fp, lr, [SP, #-0x10]!
    //     0x7d6b84: mov             fp, SP
    // 0x7d6b88: AllocStack(0x8)
    //     0x7d6b88: sub             SP, SP, #8
    // 0x7d6b8c: SetupParameters(DragGestureRecognizer this /* r1 => r0, fp-0x8 */)
    //     0x7d6b8c: mov             x0, x1
    //     0x7d6b90: stur            x1, [fp, #-8]
    // 0x7d6b94: CheckStackOverflow
    //     0x7d6b94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d6b98: cmp             SP, x16
    //     0x7d6b9c: b.ls            #0x7d6c20
    // 0x7d6ba0: LoadField: r1 = r0->field_53
    //     0x7d6ba0: ldur            w1, [x0, #0x53]
    // 0x7d6ba4: DecompressPointer r1
    //     0x7d6ba4: add             x1, x1, HEAP, lsl #32
    // 0x7d6ba8: LoadField: r3 = r1->field_7
    //     0x7d6ba8: ldur            x3, [x1, #7]
    // 0x7d6bac: cmp             x3, #1
    // 0x7d6bb0: b.gt            #0x7d6bdc
    // 0x7d6bb4: cmp             x3, #0
    // 0x7d6bb8: b.le            #0x7d6be8
    // 0x7d6bbc: mov             x1, x0
    // 0x7d6bc0: r2 = Instance_GestureDisposition
    //     0x7d6bc0: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0x7d6bc4: ldr             x2, [x2, #0xde8]
    // 0x7d6bc8: r0 = resolve()
    //     0x7d6bc8: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x7d6bcc: ldur            x1, [fp, #-8]
    // 0x7d6bd0: r0 = _checkCancel()
    //     0x7d6bd0: bl              #0x7d7028  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_checkCancel
    // 0x7d6bd4: ldur            x0, [fp, #-8]
    // 0x7d6bd8: b               #0x7d6be8
    // 0x7d6bdc: ldur            x1, [fp, #-8]
    // 0x7d6be0: r0 = _checkEnd()
    //     0x7d6be0: bl              #0x7d6c28  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_checkEnd
    // 0x7d6be4: ldur            x0, [fp, #-8]
    // 0x7d6be8: r1 = false
    //     0x7d6be8: add             x1, NULL, #0x30  ; false
    // 0x7d6bec: StoreField: r0->field_73 = r1
    //     0x7d6bec: stur            w1, [x0, #0x73]
    // 0x7d6bf0: LoadField: r1 = r0->field_77
    //     0x7d6bf0: ldur            w1, [x0, #0x77]
    // 0x7d6bf4: DecompressPointer r1
    //     0x7d6bf4: add             x1, x1, HEAP, lsl #32
    // 0x7d6bf8: r0 = clear()
    //     0x7d6bf8: bl              #0x623ad0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x7d6bfc: ldur            x1, [fp, #-8]
    // 0x7d6c00: StoreField: r1->field_67 = rNULL
    //     0x7d6c00: stur            NULL, [x1, #0x67]
    // 0x7d6c04: r2 = Instance__DragState
    //     0x7d6c04: add             x2, PP, #0x25, lsl #12  ; [pp+0x25390] Obj!_DragState@e36e01
    //     0x7d6c08: ldr             x2, [x2, #0x390]
    // 0x7d6c0c: StoreField: r1->field_53 = r2
    //     0x7d6c0c: stur            w2, [x1, #0x53]
    // 0x7d6c10: r0 = Null
    //     0x7d6c10: mov             x0, NULL
    // 0x7d6c14: LeaveFrame
    //     0x7d6c14: mov             SP, fp
    //     0x7d6c18: ldp             fp, lr, [SP], #0x10
    // 0x7d6c1c: ret
    //     0x7d6c1c: ret             
    // 0x7d6c20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d6c20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d6c24: b               #0x7d6ba0
  }
  _ _checkEnd(/* No info */) {
    // ** addr: 0x7d6c28, size: 0x22c
    // 0x7d6c28: EnterFrame
    //     0x7d6c28: stp             fp, lr, [SP, #-0x10]!
    //     0x7d6c2c: mov             fp, SP
    // 0x7d6c30: AllocStack(0x38)
    //     0x7d6c30: sub             SP, SP, #0x38
    // 0x7d6c34: SetupParameters(DragGestureRecognizer this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x7d6c34: stur            x1, [fp, #-8]
    //     0x7d6c38: stur            x2, [fp, #-0x10]
    // 0x7d6c3c: CheckStackOverflow
    //     0x7d6c3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d6c40: cmp             SP, x16
    //     0x7d6c44: b.ls            #0x7d6e3c
    // 0x7d6c48: r1 = 3
    //     0x7d6c48: movz            x1, #0x3
    // 0x7d6c4c: r0 = AllocateContext()
    //     0x7d6c4c: bl              #0xec126c  ; AllocateContextStub
    // 0x7d6c50: mov             x4, x0
    // 0x7d6c54: ldur            x3, [fp, #-8]
    // 0x7d6c58: stur            x4, [fp, #-0x20]
    // 0x7d6c5c: StoreField: r4->field_f = r3
    //     0x7d6c5c: stur            w3, [x4, #0xf]
    // 0x7d6c60: LoadField: r0 = r3->field_37
    //     0x7d6c60: ldur            w0, [x3, #0x37]
    // 0x7d6c64: DecompressPointer r0
    //     0x7d6c64: add             x0, x0, HEAP, lsl #32
    // 0x7d6c68: cmp             w0, NULL
    // 0x7d6c6c: b.ne            #0x7d6c80
    // 0x7d6c70: r0 = Null
    //     0x7d6c70: mov             x0, NULL
    // 0x7d6c74: LeaveFrame
    //     0x7d6c74: mov             SP, fp
    //     0x7d6c78: ldp             fp, lr, [SP], #0x10
    // 0x7d6c7c: ret
    //     0x7d6c7c: ret             
    // 0x7d6c80: ldur            x2, [fp, #-0x10]
    // 0x7d6c84: LoadField: r5 = r3->field_77
    //     0x7d6c84: ldur            w5, [x3, #0x77]
    // 0x7d6c88: DecompressPointer r5
    //     0x7d6c88: add             x5, x5, HEAP, lsl #32
    // 0x7d6c8c: stur            x5, [fp, #-0x18]
    // 0x7d6c90: r0 = BoxInt64Instr(r2)
    //     0x7d6c90: sbfiz           x0, x2, #1, #0x1f
    //     0x7d6c94: cmp             x2, x0, asr #1
    //     0x7d6c98: b.eq            #0x7d6ca4
    //     0x7d6c9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7d6ca0: stur            x2, [x0, #7]
    // 0x7d6ca4: mov             x1, x5
    // 0x7d6ca8: mov             x2, x0
    // 0x7d6cac: r0 = _getValueOrData()
    //     0x7d6cac: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7d6cb0: mov             x1, x0
    // 0x7d6cb4: ldur            x0, [fp, #-0x18]
    // 0x7d6cb8: LoadField: r2 = r0->field_f
    //     0x7d6cb8: ldur            w2, [x0, #0xf]
    // 0x7d6cbc: DecompressPointer r2
    //     0x7d6cbc: add             x2, x2, HEAP, lsl #32
    // 0x7d6cc0: cmp             w2, w1
    // 0x7d6cc4: b.ne            #0x7d6cd0
    // 0x7d6cc8: r3 = Null
    //     0x7d6cc8: mov             x3, NULL
    // 0x7d6ccc: b               #0x7d6cd4
    // 0x7d6cd0: mov             x3, x1
    // 0x7d6cd4: ldur            x2, [fp, #-0x20]
    // 0x7d6cd8: stur            x3, [fp, #-0x18]
    // 0x7d6cdc: cmp             w3, NULL
    // 0x7d6ce0: b.eq            #0x7d6e44
    // 0x7d6ce4: r0 = LoadClassIdInstr(r3)
    //     0x7d6ce4: ldur            x0, [x3, #-1]
    //     0x7d6ce8: ubfx            x0, x0, #0xc, #0x14
    // 0x7d6cec: mov             x1, x3
    // 0x7d6cf0: r0 = GDT[cid_x0 + 0x17ef]()
    //     0x7d6cf0: movz            x17, #0x17ef
    //     0x7d6cf4: add             lr, x0, x17
    //     0x7d6cf8: ldr             lr, [x21, lr, lsl #3]
    //     0x7d6cfc: blr             lr
    // 0x7d6d00: mov             x1, x0
    // 0x7d6d04: ldur            x4, [fp, #-0x20]
    // 0x7d6d08: StoreField: r4->field_13 = r0
    //     0x7d6d08: stur            w0, [x4, #0x13]
    //     0x7d6d0c: ldurb           w16, [x4, #-1]
    //     0x7d6d10: ldurb           w17, [x0, #-1]
    //     0x7d6d14: and             x16, x17, x16, lsr #2
    //     0x7d6d18: tst             x16, HEAP, lsr #32
    //     0x7d6d1c: b.eq            #0x7d6d24
    //     0x7d6d20: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7d6d24: ArrayStore: r4[0] = rNULL  ; List_4
    //     0x7d6d24: stur            NULL, [x4, #0x17]
    // 0x7d6d28: cmp             w1, NULL
    // 0x7d6d2c: b.ne            #0x7d6d3c
    // 0x7d6d30: mov             x2, x4
    // 0x7d6d34: r0 = Null
    //     0x7d6d34: mov             x0, NULL
    // 0x7d6d38: b               #0x7d6d94
    // 0x7d6d3c: ldur            x5, [fp, #-8]
    // 0x7d6d40: ldur            x0, [fp, #-0x18]
    // 0x7d6d44: LoadField: r3 = r0->field_7
    //     0x7d6d44: ldur            w3, [x0, #7]
    // 0x7d6d48: DecompressPointer r3
    //     0x7d6d48: add             x3, x3, HEAP, lsl #32
    // 0x7d6d4c: r0 = LoadClassIdInstr(r5)
    //     0x7d6d4c: ldur            x0, [x5, #-1]
    //     0x7d6d50: ubfx            x0, x0, #0xc, #0x14
    // 0x7d6d54: mov             x2, x1
    // 0x7d6d58: mov             x1, x5
    // 0x7d6d5c: r0 = GDT[cid_x0 + 0x17f1]()
    //     0x7d6d5c: movz            x17, #0x17f1
    //     0x7d6d60: add             lr, x0, x17
    //     0x7d6d64: ldr             lr, [x21, lr, lsl #3]
    //     0x7d6d68: blr             lr
    // 0x7d6d6c: mov             x1, x0
    // 0x7d6d70: ldur            x2, [fp, #-0x20]
    // 0x7d6d74: ArrayStore: r2[0] = r0  ; List_4
    //     0x7d6d74: stur            w0, [x2, #0x17]
    //     0x7d6d78: ldurb           w16, [x2, #-1]
    //     0x7d6d7c: ldurb           w17, [x0, #-1]
    //     0x7d6d80: and             x16, x17, x16, lsr #2
    //     0x7d6d84: tst             x16, HEAP, lsr #32
    //     0x7d6d88: b.eq            #0x7d6d90
    //     0x7d6d8c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7d6d90: mov             x0, x1
    // 0x7d6d94: cmp             w0, NULL
    // 0x7d6d98: b.ne            #0x7d6e08
    // 0x7d6d9c: ldur            x0, [fp, #-8]
    // 0x7d6da0: LoadField: r1 = r0->field_5f
    //     0x7d6da0: ldur            w1, [x0, #0x5f]
    // 0x7d6da4: DecompressPointer r1
    //     0x7d6da4: add             x1, x1, HEAP, lsl #32
    // 0x7d6da8: r16 = Sentinel
    //     0x7d6da8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d6dac: cmp             w1, w16
    // 0x7d6db0: b.eq            #0x7d6e48
    // 0x7d6db4: LoadField: r3 = r1->field_b
    //     0x7d6db4: ldur            w3, [x1, #0xb]
    // 0x7d6db8: DecompressPointer r3
    //     0x7d6db8: add             x3, x3, HEAP, lsl #32
    // 0x7d6dbc: stur            x3, [fp, #-0x18]
    // 0x7d6dc0: r0 = DragEndDetails()
    //     0x7d6dc0: bl              #0x7d6e54  ; AllocateDragEndDetailsStub -> DragEndDetails (size=0x14)
    // 0x7d6dc4: mov             x1, x0
    // 0x7d6dc8: r0 = Instance_Velocity
    //     0x7d6dc8: add             x0, PP, #0x39, lsl #12  ; [pp+0x39c58] Obj!Velocity@e14a31
    //     0x7d6dcc: ldr             x0, [x0, #0xc58]
    // 0x7d6dd0: StoreField: r1->field_7 = r0
    //     0x7d6dd0: stur            w0, [x1, #7]
    // 0x7d6dd4: r0 = 0.000000
    //     0x7d6dd4: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x7d6dd8: StoreField: r1->field_b = r0
    //     0x7d6dd8: stur            w0, [x1, #0xb]
    // 0x7d6ddc: ldur            x0, [fp, #-0x18]
    // 0x7d6de0: StoreField: r1->field_f = r0
    //     0x7d6de0: stur            w0, [x1, #0xf]
    // 0x7d6de4: mov             x0, x1
    // 0x7d6de8: ldur            x2, [fp, #-0x20]
    // 0x7d6dec: ArrayStore: r2[0] = r0  ; List_4
    //     0x7d6dec: stur            w0, [x2, #0x17]
    //     0x7d6df0: ldurb           w16, [x2, #-1]
    //     0x7d6df4: ldurb           w17, [x0, #-1]
    //     0x7d6df8: and             x16, x17, x16, lsr #2
    //     0x7d6dfc: tst             x16, HEAP, lsr #32
    //     0x7d6e00: b.eq            #0x7d6e08
    //     0x7d6e04: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7d6e08: r1 = Function '<anonymous closure>':.
    //     0x7d6e08: add             x1, PP, #0x39, lsl #12  ; [pp+0x39c60] AnonymousClosure: (0x7d6e60), in [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_checkEnd (0x7d6c28)
    //     0x7d6e0c: ldr             x1, [x1, #0xc60]
    // 0x7d6e10: r0 = AllocateClosure()
    //     0x7d6e10: bl              #0xec1630  ; AllocateClosureStub
    // 0x7d6e14: r16 = <void?>
    //     0x7d6e14: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x7d6e18: ldur            lr, [fp, #-8]
    // 0x7d6e1c: stp             lr, x16, [SP, #8]
    // 0x7d6e20: str             x0, [SP]
    // 0x7d6e24: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7d6e24: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7d6e28: r0 = invokeCallback()
    //     0x7d6e28: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x7d6e2c: r0 = Null
    //     0x7d6e2c: mov             x0, NULL
    // 0x7d6e30: LeaveFrame
    //     0x7d6e30: mov             SP, fp
    //     0x7d6e34: ldp             fp, lr, [SP], #0x10
    // 0x7d6e38: ret
    //     0x7d6e38: ret             
    // 0x7d6e3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d6e3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d6e40: b               #0x7d6c48
    // 0x7d6e44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d6e44: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7d6e48: r9 = _lastPosition
    //     0x7d6e48: add             x9, PP, #0x39, lsl #12  ; [pp+0x39c68] Field <DragGestureRecognizer._lastPosition@428099969>: late (offset: 0x60)
    //     0x7d6e4c: ldr             x9, [x9, #0xc68]
    // 0x7d6e50: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7d6e50: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x7d6e60, size: 0x7c
    // 0x7d6e60: EnterFrame
    //     0x7d6e60: stp             fp, lr, [SP, #-0x10]!
    //     0x7d6e64: mov             fp, SP
    // 0x7d6e68: AllocStack(0x10)
    //     0x7d6e68: sub             SP, SP, #0x10
    // 0x7d6e6c: SetupParameters()
    //     0x7d6e6c: ldr             x0, [fp, #0x10]
    //     0x7d6e70: ldur            w1, [x0, #0x17]
    //     0x7d6e74: add             x1, x1, HEAP, lsl #32
    // 0x7d6e78: CheckStackOverflow
    //     0x7d6e78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d6e7c: cmp             SP, x16
    //     0x7d6e80: b.ls            #0x7d6ecc
    // 0x7d6e84: LoadField: r0 = r1->field_f
    //     0x7d6e84: ldur            w0, [x1, #0xf]
    // 0x7d6e88: DecompressPointer r0
    //     0x7d6e88: add             x0, x0, HEAP, lsl #32
    // 0x7d6e8c: LoadField: r2 = r0->field_37
    //     0x7d6e8c: ldur            w2, [x0, #0x37]
    // 0x7d6e90: DecompressPointer r2
    //     0x7d6e90: add             x2, x2, HEAP, lsl #32
    // 0x7d6e94: cmp             w2, NULL
    // 0x7d6e98: b.eq            #0x7d6ed4
    // 0x7d6e9c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x7d6e9c: ldur            w0, [x1, #0x17]
    // 0x7d6ea0: DecompressPointer r0
    //     0x7d6ea0: add             x0, x0, HEAP, lsl #32
    // 0x7d6ea4: cmp             w0, NULL
    // 0x7d6ea8: b.eq            #0x7d6ed8
    // 0x7d6eac: stp             x0, x2, [SP]
    // 0x7d6eb0: mov             x0, x2
    // 0x7d6eb4: ClosureCall
    //     0x7d6eb4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x7d6eb8: ldur            x2, [x0, #0x1f]
    //     0x7d6ebc: blr             x2
    // 0x7d6ec0: LeaveFrame
    //     0x7d6ec0: mov             SP, fp
    //     0x7d6ec4: ldp             fp, lr, [SP], #0x10
    // 0x7d6ec8: ret
    //     0x7d6ec8: ret             
    // 0x7d6ecc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d6ecc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d6ed0: b               #0x7d6e84
    // 0x7d6ed4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d6ed4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7d6ed8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d6ed8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _checkCancel(/* No info */) {
    // ** addr: 0x7d7028, size: 0x54
    // 0x7d7028: EnterFrame
    //     0x7d7028: stp             fp, lr, [SP, #-0x10]!
    //     0x7d702c: mov             fp, SP
    // 0x7d7030: AllocStack(0x18)
    //     0x7d7030: sub             SP, SP, #0x18
    // 0x7d7034: CheckStackOverflow
    //     0x7d7034: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d7038: cmp             SP, x16
    //     0x7d703c: b.ls            #0x7d7074
    // 0x7d7040: LoadField: r0 = r1->field_3b
    //     0x7d7040: ldur            w0, [x1, #0x3b]
    // 0x7d7044: DecompressPointer r0
    //     0x7d7044: add             x0, x0, HEAP, lsl #32
    // 0x7d7048: cmp             w0, NULL
    // 0x7d704c: b.eq            #0x7d7064
    // 0x7d7050: r16 = <void?>
    //     0x7d7050: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x7d7054: stp             x1, x16, [SP, #8]
    // 0x7d7058: str             x0, [SP]
    // 0x7d705c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7d705c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7d7060: r0 = invokeCallback()
    //     0x7d7060: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x7d7064: r0 = Null
    //     0x7d7064: mov             x0, NULL
    // 0x7d7068: LeaveFrame
    //     0x7d7068: mov             SP, fp
    //     0x7d706c: ldp             fp, lr, [SP], #0x10
    // 0x7d7070: ret
    //     0x7d7070: ret             
    // 0x7d7074: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d7074: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d7078: b               #0x7d7040
  }
  _ dispose(/* No info */) {
    // ** addr: 0x7f9180, size: 0x4c
    // 0x7f9180: EnterFrame
    //     0x7f9180: stp             fp, lr, [SP, #-0x10]!
    //     0x7f9184: mov             fp, SP
    // 0x7f9188: AllocStack(0x8)
    //     0x7f9188: sub             SP, SP, #8
    // 0x7f918c: SetupParameters(DragGestureRecognizer this /* r1 => r0, fp-0x8 */)
    //     0x7f918c: mov             x0, x1
    //     0x7f9190: stur            x1, [fp, #-8]
    // 0x7f9194: CheckStackOverflow
    //     0x7f9194: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f9198: cmp             SP, x16
    //     0x7f919c: b.ls            #0x7f91c4
    // 0x7f91a0: LoadField: r1 = r0->field_77
    //     0x7f91a0: ldur            w1, [x0, #0x77]
    // 0x7f91a4: DecompressPointer r1
    //     0x7f91a4: add             x1, x1, HEAP, lsl #32
    // 0x7f91a8: r0 = clear()
    //     0x7f91a8: bl              #0x623ad0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x7f91ac: ldur            x1, [fp, #-8]
    // 0x7f91b0: r0 = dispose()
    //     0x7f91b0: bl              #0x7f93bc  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::dispose
    // 0x7f91b4: r0 = Null
    //     0x7f91b4: mov             x0, NULL
    // 0x7f91b8: LeaveFrame
    //     0x7f91b8: mov             SP, fp
    //     0x7f91bc: ldp             fp, lr, [SP], #0x10
    // 0x7f91c0: ret
    //     0x7f91c0: ret             
    // 0x7f91c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f91c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f91c8: b               #0x7f91a0
  }
  _ addAllowedPointer(/* No info */) {
    // ** addr: 0x801bf0, size: 0xd8
    // 0x801bf0: EnterFrame
    //     0x801bf0: stp             fp, lr, [SP, #-0x10]!
    //     0x801bf4: mov             fp, SP
    // 0x801bf8: AllocStack(0x10)
    //     0x801bf8: sub             SP, SP, #0x10
    // 0x801bfc: SetupParameters(DragGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x801bfc: mov             x3, x1
    //     0x801c00: mov             x0, x2
    //     0x801c04: stur            x1, [fp, #-8]
    //     0x801c08: stur            x2, [fp, #-0x10]
    // 0x801c0c: CheckStackOverflow
    //     0x801c0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x801c10: cmp             SP, x16
    //     0x801c14: b.ls            #0x801cc0
    // 0x801c18: mov             x1, x3
    // 0x801c1c: mov             x2, x0
    // 0x801c20: r0 = addAllowedPointer()
    //     0x801c20: bl              #0x802a78  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::addAllowedPointer
    // 0x801c24: ldur            x2, [fp, #-8]
    // 0x801c28: LoadField: r0 = r2->field_53
    //     0x801c28: ldur            w0, [x2, #0x53]
    // 0x801c2c: DecompressPointer r0
    //     0x801c2c: add             x0, x0, HEAP, lsl #32
    // 0x801c30: r16 = Instance__DragState
    //     0x801c30: add             x16, PP, #0x25, lsl #12  ; [pp+0x25390] Obj!_DragState@e36e01
    //     0x801c34: ldr             x16, [x16, #0x390]
    // 0x801c38: cmp             w0, w16
    // 0x801c3c: b.ne            #0x801ca4
    // 0x801c40: ldur            x3, [fp, #-0x10]
    // 0x801c44: r0 = LoadClassIdInstr(r3)
    //     0x801c44: ldur            x0, [x3, #-1]
    //     0x801c48: ubfx            x0, x0, #0xc, #0x14
    // 0x801c4c: mov             x1, x3
    // 0x801c50: r0 = GDT[cid_x0 + 0x12cee]()
    //     0x801c50: movz            x17, #0x2cee
    //     0x801c54: movk            x17, #0x1, lsl #16
    //     0x801c58: add             lr, x0, x17
    //     0x801c5c: ldr             lr, [x21, lr, lsl #3]
    //     0x801c60: blr             lr
    // 0x801c64: mov             x2, x0
    // 0x801c68: r0 = BoxInt64Instr(r2)
    //     0x801c68: sbfiz           x0, x2, #1, #0x1f
    //     0x801c6c: cmp             x2, x0, asr #1
    //     0x801c70: b.eq            #0x801c7c
    //     0x801c74: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x801c78: stur            x2, [x0, #7]
    // 0x801c7c: ldur            x1, [fp, #-8]
    // 0x801c80: StoreField: r1->field_67 = r0
    //     0x801c80: stur            w0, [x1, #0x67]
    //     0x801c84: tbz             w0, #0, #0x801ca0
    //     0x801c88: ldurb           w16, [x1, #-1]
    //     0x801c8c: ldurb           w17, [x0, #-1]
    //     0x801c90: and             x16, x17, x16, lsr #2
    //     0x801c94: tst             x16, HEAP, lsr #32
    //     0x801c98: b.eq            #0x801ca0
    //     0x801c9c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x801ca0: b               #0x801ca8
    // 0x801ca4: mov             x1, x2
    // 0x801ca8: ldur            x2, [fp, #-0x10]
    // 0x801cac: r0 = _addPointer()
    //     0x801cac: bl              #0x7cd114  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_addPointer
    // 0x801cb0: r0 = Null
    //     0x801cb0: mov             x0, NULL
    // 0x801cb4: LeaveFrame
    //     0x801cb4: mov             SP, fp
    //     0x801cb8: ldp             fp, lr, [SP], #0x10
    // 0x801cbc: ret
    //     0x801cbc: ret             
    // 0x801cc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x801cc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x801cc4: b               #0x801c18
  }
  _ isPointerAllowed(/* No info */) {
    // ** addr: 0x8523ec, size: 0x150
    // 0x8523ec: EnterFrame
    //     0x8523ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8523f0: mov             fp, SP
    // 0x8523f4: AllocStack(0x10)
    //     0x8523f4: sub             SP, SP, #0x10
    // 0x8523f8: SetupParameters(DragGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8523f8: mov             x3, x1
    //     0x8523fc: stur            x1, [fp, #-8]
    //     0x852400: stur            x2, [fp, #-0x10]
    // 0x852404: CheckStackOverflow
    //     0x852404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x852408: cmp             SP, x16
    //     0x85240c: b.ls            #0x852534
    // 0x852410: LoadField: r0 = r3->field_67
    //     0x852410: ldur            w0, [x3, #0x67]
    // 0x852414: DecompressPointer r0
    //     0x852414: add             x0, x0, HEAP, lsl #32
    // 0x852418: cmp             w0, NULL
    // 0x85241c: b.ne            #0x852488
    // 0x852420: LoadField: r0 = r3->field_2b
    //     0x852420: ldur            w0, [x3, #0x2b]
    // 0x852424: DecompressPointer r0
    //     0x852424: add             x0, x0, HEAP, lsl #32
    // 0x852428: cmp             w0, NULL
    // 0x85242c: b.ne            #0x852480
    // 0x852430: LoadField: r0 = r3->field_2f
    //     0x852430: ldur            w0, [x3, #0x2f]
    // 0x852434: DecompressPointer r0
    //     0x852434: add             x0, x0, HEAP, lsl #32
    // 0x852438: cmp             w0, NULL
    // 0x85243c: b.ne            #0x852480
    // 0x852440: LoadField: r0 = r3->field_33
    //     0x852440: ldur            w0, [x3, #0x33]
    // 0x852444: DecompressPointer r0
    //     0x852444: add             x0, x0, HEAP, lsl #32
    // 0x852448: cmp             w0, NULL
    // 0x85244c: b.ne            #0x852480
    // 0x852450: LoadField: r0 = r3->field_37
    //     0x852450: ldur            w0, [x3, #0x37]
    // 0x852454: DecompressPointer r0
    //     0x852454: add             x0, x0, HEAP, lsl #32
    // 0x852458: cmp             w0, NULL
    // 0x85245c: b.ne            #0x852480
    // 0x852460: LoadField: r0 = r3->field_3b
    //     0x852460: ldur            w0, [x3, #0x3b]
    // 0x852464: DecompressPointer r0
    //     0x852464: add             x0, x0, HEAP, lsl #32
    // 0x852468: cmp             w0, NULL
    // 0x85246c: b.ne            #0x852480
    // 0x852470: r0 = false
    //     0x852470: add             x0, NULL, #0x30  ; false
    // 0x852474: LeaveFrame
    //     0x852474: mov             SP, fp
    //     0x852478: ldp             fp, lr, [SP], #0x10
    // 0x85247c: ret
    //     0x85247c: ret             
    // 0x852480: mov             x2, x3
    // 0x852484: b               #0x85251c
    // 0x852488: r0 = LoadClassIdInstr(r2)
    //     0x852488: ldur            x0, [x2, #-1]
    //     0x85248c: ubfx            x0, x0, #0xc, #0x14
    // 0x852490: mov             x1, x2
    // 0x852494: r0 = GDT[cid_x0 + 0x12cee]()
    //     0x852494: movz            x17, #0x2cee
    //     0x852498: movk            x17, #0x1, lsl #16
    //     0x85249c: add             lr, x0, x17
    //     0x8524a0: ldr             lr, [x21, lr, lsl #3]
    //     0x8524a4: blr             lr
    // 0x8524a8: mov             x3, x0
    // 0x8524ac: ldur            x2, [fp, #-8]
    // 0x8524b0: LoadField: r4 = r2->field_67
    //     0x8524b0: ldur            w4, [x2, #0x67]
    // 0x8524b4: DecompressPointer r4
    //     0x8524b4: add             x4, x4, HEAP, lsl #32
    // 0x8524b8: r0 = BoxInt64Instr(r3)
    //     0x8524b8: sbfiz           x0, x3, #1, #0x1f
    //     0x8524bc: cmp             x3, x0, asr #1
    //     0x8524c0: b.eq            #0x8524cc
    //     0x8524c4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8524c8: stur            x3, [x0, #7]
    // 0x8524cc: cmp             w0, w4
    // 0x8524d0: b.eq            #0x85251c
    // 0x8524d4: and             w16, w0, w4
    // 0x8524d8: branchIfSmi(r16, 0x85250c)
    //     0x8524d8: tbz             w16, #0, #0x85250c
    // 0x8524dc: r16 = LoadClassIdInstr(r0)
    //     0x8524dc: ldur            x16, [x0, #-1]
    //     0x8524e0: ubfx            x16, x16, #0xc, #0x14
    // 0x8524e4: cmp             x16, #0x3d
    // 0x8524e8: b.ne            #0x85250c
    // 0x8524ec: r16 = LoadClassIdInstr(r4)
    //     0x8524ec: ldur            x16, [x4, #-1]
    //     0x8524f0: ubfx            x16, x16, #0xc, #0x14
    // 0x8524f4: cmp             x16, #0x3d
    // 0x8524f8: b.ne            #0x85250c
    // 0x8524fc: LoadField: r16 = r0->field_7
    //     0x8524fc: ldur            x16, [x0, #7]
    // 0x852500: LoadField: r17 = r4->field_7
    //     0x852500: ldur            x17, [x4, #7]
    // 0x852504: cmp             x16, x17
    // 0x852508: b.eq            #0x85251c
    // 0x85250c: r0 = false
    //     0x85250c: add             x0, NULL, #0x30  ; false
    // 0x852510: LeaveFrame
    //     0x852510: mov             SP, fp
    //     0x852514: ldp             fp, lr, [SP], #0x10
    // 0x852518: ret
    //     0x852518: ret             
    // 0x85251c: mov             x1, x2
    // 0x852520: ldur            x2, [fp, #-0x10]
    // 0x852524: r0 = isPointerAllowed()
    //     0x852524: bl              #0x852e08  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::isPointerAllowed
    // 0x852528: LeaveFrame
    //     0x852528: mov             SP, fp
    //     0x85252c: ldp             fp, lr, [SP], #0x10
    // 0x852530: ret
    //     0x852530: ret             
    // 0x852534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x852534: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x852538: b               #0x852410
  }
  _ DragGestureRecognizer(/* No info */) {
    // ** addr: 0x85347c, size: 0x140
    // 0x85347c: EnterFrame
    //     0x85347c: stp             fp, lr, [SP, #-0x10]!
    //     0x853480: mov             fp, SP
    // 0x853484: AllocStack(0x20)
    //     0x853484: sub             SP, SP, #0x20
    // 0x853488: r5 = Instance__DragState
    //     0x853488: add             x5, PP, #0x25, lsl #12  ; [pp+0x25390] Obj!_DragState@e36e01
    //     0x85348c: ldr             x5, [x5, #0x390]
    // 0x853490: r4 = Sentinel
    //     0x853490: ldr             x4, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x853494: r3 = false
    //     0x853494: add             x3, NULL, #0x30  ; false
    // 0x853498: r0 = Instance_Offset
    //     0x853498: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x85349c: stur            x1, [fp, #-8]
    // 0x8534a0: stur            x2, [fp, #-0x10]
    // 0x8534a4: CheckStackOverflow
    //     0x8534a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8534a8: cmp             SP, x16
    //     0x8534ac: b.ls            #0x8535b4
    // 0x8534b0: StoreField: r1->field_53 = r5
    //     0x8534b0: stur            w5, [x1, #0x53]
    // 0x8534b4: StoreField: r1->field_57 = r4
    //     0x8534b4: stur            w4, [x1, #0x57]
    // 0x8534b8: StoreField: r1->field_5b = r4
    //     0x8534b8: stur            w4, [x1, #0x5b]
    // 0x8534bc: StoreField: r1->field_5f = r4
    //     0x8534bc: stur            w4, [x1, #0x5f]
    // 0x8534c0: StoreField: r1->field_6f = r4
    //     0x8534c0: stur            w4, [x1, #0x6f]
    // 0x8534c4: StoreField: r1->field_73 = r3
    //     0x8534c4: stur            w3, [x1, #0x73]
    // 0x8534c8: StoreField: r1->field_83 = r0
    //     0x8534c8: stur            w0, [x1, #0x83]
    // 0x8534cc: r16 = <int, VelocityTracker>
    //     0x8534cc: add             x16, PP, #0x25, lsl #12  ; [pp+0x25398] TypeArguments: <int, VelocityTracker>
    //     0x8534d0: ldr             x16, [x16, #0x398]
    // 0x8534d4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8534d8: stp             lr, x16, [SP]
    // 0x8534dc: r0 = Map._fromLiteral()
    //     0x8534dc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8534e0: ldur            x1, [fp, #-8]
    // 0x8534e4: StoreField: r1->field_77 = r0
    //     0x8534e4: stur            w0, [x1, #0x77]
    //     0x8534e8: ldurb           w16, [x1, #-1]
    //     0x8534ec: ldurb           w17, [x0, #-1]
    //     0x8534f0: and             x16, x17, x16, lsr #2
    //     0x8534f4: tst             x16, HEAP, lsr #32
    //     0x8534f8: b.eq            #0x853500
    //     0x8534fc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x853500: r16 = <int, Offset>
    //     0x853500: add             x16, PP, #0x25, lsl #12  ; [pp+0x253a0] TypeArguments: <int, Offset>
    //     0x853504: ldr             x16, [x16, #0x3a0]
    // 0x853508: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x85350c: stp             lr, x16, [SP]
    // 0x853510: r0 = Map._fromLiteral()
    //     0x853510: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x853514: ldur            x3, [fp, #-8]
    // 0x853518: StoreField: r3->field_7b = r0
    //     0x853518: stur            w0, [x3, #0x7b]
    //     0x85351c: ldurb           w16, [x3, #-1]
    //     0x853520: ldurb           w17, [x0, #-1]
    //     0x853524: and             x16, x17, x16, lsr #2
    //     0x853528: tst             x16, HEAP, lsr #32
    //     0x85352c: b.eq            #0x853534
    //     0x853530: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x853534: r1 = <int>
    //     0x853534: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x853538: r2 = 0
    //     0x853538: movz            x2, #0
    // 0x85353c: r0 = _GrowableList()
    //     0x85353c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x853540: ldur            x1, [fp, #-8]
    // 0x853544: StoreField: r1->field_87 = r0
    //     0x853544: stur            w0, [x1, #0x87]
    //     0x853548: ldurb           w16, [x1, #-1]
    //     0x85354c: ldurb           w17, [x0, #-1]
    //     0x853550: and             x16, x17, x16, lsr #2
    //     0x853554: tst             x16, HEAP, lsr #32
    //     0x853558: b.eq            #0x853560
    //     0x85355c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x853560: r0 = Instance_DragStartBehavior
    //     0x853560: ldr             x0, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0x853564: StoreField: r1->field_23 = r0
    //     0x853564: stur            w0, [x1, #0x23]
    // 0x853568: r0 = Instance_MultitouchDragStrategy
    //     0x853568: add             x0, PP, #0x25, lsl #12  ; [pp+0x253a8] Obj!MultitouchDragStrategy@e36d21
    //     0x85356c: ldr             x0, [x0, #0x3a8]
    // 0x853570: StoreField: r1->field_27 = r0
    //     0x853570: stur            w0, [x1, #0x27]
    // 0x853574: r0 = Closure: (PointerEvent) => VelocityTracker from Function '_defaultBuilder@428099969': static.
    //     0x853574: add             x0, PP, #0x25, lsl #12  ; [pp+0x253b0] Closure: (PointerEvent) => VelocityTracker from Function '_defaultBuilder@428099969': static. (0x7e54fb2535d4)
    //     0x853578: ldr             x0, [x0, #0x3b0]
    // 0x85357c: StoreField: r1->field_4f = r0
    //     0x85357c: stur            w0, [x1, #0x4f]
    // 0x853580: r0 = false
    //     0x853580: add             x0, NULL, #0x30  ; false
    // 0x853584: StoreField: r1->field_4b = r0
    //     0x853584: stur            w0, [x1, #0x4b]
    // 0x853588: ldur            x16, [fp, #-0x10]
    // 0x85358c: r30 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@428099969': static.
    //     0x85358c: add             lr, PP, #0x25, lsl #12  ; [pp+0x253b8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@428099969': static. (0x7e54fb2535bc)
    //     0x853590: ldr             lr, [lr, #0x3b8]
    // 0x853594: stp             lr, x16, [SP]
    // 0x853598: r4 = const [0, 0x3, 0x2, 0x1, allowedButtonsFilter, 0x2, supportedDevices, 0x1, null]
    //     0x853598: add             x4, PP, #0x25, lsl #12  ; [pp+0x253c0] List(9) [0, 0x3, 0x2, 0x1, "allowedButtonsFilter", 0x2, "supportedDevices", 0x1, Null]
    //     0x85359c: ldr             x4, [x4, #0x3c0]
    // 0x8535a0: r0 = OneSequenceGestureRecognizer()
    //     0x8535a0: bl              #0x763058  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::OneSequenceGestureRecognizer
    // 0x8535a4: r0 = Null
    //     0x8535a4: mov             x0, NULL
    // 0x8535a8: LeaveFrame
    //     0x8535a8: mov             SP, fp
    //     0x8535ac: ldp             fp, lr, [SP], #0x10
    // 0x8535b0: ret
    //     0x8535b0: ret             
    // 0x8535b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8535b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8535b8: b               #0x8534b0
  }
  [closure] static bool _defaultButtonAcceptBehavior(dynamic, int) {
    // ** addr: 0x8535bc, size: 0x18
    // 0x8535bc: ldr             x1, [SP]
    // 0x8535c0: cmp             w1, #2
    // 0x8535c4: r16 = true
    //     0x8535c4: add             x16, NULL, #0x20  ; true
    // 0x8535c8: r17 = false
    //     0x8535c8: add             x17, NULL, #0x30  ; false
    // 0x8535cc: csel            x0, x16, x17, eq
    // 0x8535d0: ret
    //     0x8535d0: ret             
  }
  [closure] static VelocityTracker _defaultBuilder(dynamic, PointerEvent) {
    // ** addr: 0x8535d4, size: 0x30
    // 0x8535d4: EnterFrame
    //     0x8535d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8535d8: mov             fp, SP
    // 0x8535dc: CheckStackOverflow
    //     0x8535dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8535e0: cmp             SP, x16
    //     0x8535e4: b.ls            #0x8535fc
    // 0x8535e8: ldr             x1, [fp, #0x10]
    // 0x8535ec: r0 = _defaultBuilder()
    //     0x8535ec: bl              #0x853604  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_defaultBuilder
    // 0x8535f0: LeaveFrame
    //     0x8535f0: mov             SP, fp
    //     0x8535f4: ldp             fp, lr, [SP], #0x10
    // 0x8535f8: ret
    //     0x8535f8: ret             
    // 0x8535fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8535fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x853600: b               #0x8535e8
  }
  static _ _defaultBuilder(/* No info */) {
    // ** addr: 0x853604, size: 0x7c
    // 0x853604: EnterFrame
    //     0x853604: stp             fp, lr, [SP, #-0x10]!
    //     0x853608: mov             fp, SP
    // 0x85360c: AllocStack(0x10)
    //     0x85360c: sub             SP, SP, #0x10
    // 0x853610: CheckStackOverflow
    //     0x853610: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x853614: cmp             SP, x16
    //     0x853618: b.ls            #0x853678
    // 0x85361c: r0 = LoadClassIdInstr(r1)
    //     0x85361c: ldur            x0, [x1, #-1]
    //     0x853620: ubfx            x0, x0, #0xc, #0x14
    // 0x853624: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x853624: movz            x17, #0x30b7
    //     0x853628: movk            x17, #0x1, lsl #16
    //     0x85362c: add             lr, x0, x17
    //     0x853630: ldr             lr, [x21, lr, lsl #3]
    //     0x853634: blr             lr
    // 0x853638: stur            x0, [fp, #-8]
    // 0x85363c: r0 = VelocityTracker()
    //     0x85363c: bl              #0x803194  ; AllocateVelocityTrackerStub -> VelocityTracker (size=0x1c)
    // 0x853640: stur            x0, [fp, #-0x10]
    // 0x853644: StoreField: r0->field_13 = rZR
    //     0x853644: stur            xzr, [x0, #0x13]
    // 0x853648: r1 = <_PointAtTime?>
    //     0x853648: add             x1, PP, #0x25, lsl #12  ; [pp+0x253c8] TypeArguments: <_PointAtTime?>
    //     0x85364c: ldr             x1, [x1, #0x3c8]
    // 0x853650: r2 = 40
    //     0x853650: movz            x2, #0x28
    // 0x853654: r0 = AllocateArray()
    //     0x853654: bl              #0xec22fc  ; AllocateArrayStub
    // 0x853658: mov             x1, x0
    // 0x85365c: ldur            x0, [fp, #-0x10]
    // 0x853660: StoreField: r0->field_f = r1
    //     0x853660: stur            w1, [x0, #0xf]
    // 0x853664: ldur            x1, [fp, #-8]
    // 0x853668: StoreField: r0->field_7 = r1
    //     0x853668: stur            w1, [x0, #7]
    // 0x85366c: LeaveFrame
    //     0x85366c: mov             SP, fp
    //     0x853670: ldp             fp, lr, [SP], #0x10
    // 0x853674: ret
    //     0x853674: ret             
    // 0x853678: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x853678: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85367c: b               #0x85361c
  }
  _ acceptGesture(/* No info */) {
    // ** addr: 0x86cae0, size: 0x120
    // 0x86cae0: EnterFrame
    //     0x86cae0: stp             fp, lr, [SP, #-0x10]!
    //     0x86cae4: mov             fp, SP
    // 0x86cae8: AllocStack(0x20)
    //     0x86cae8: sub             SP, SP, #0x20
    // 0x86caec: SetupParameters(DragGestureRecognizer this /* r1 => r0, fp-0x18 */, dynamic _ /* r2 => r2, fp-0x20 */)
    //     0x86caec: mov             x0, x1
    //     0x86caf0: stur            x1, [fp, #-0x18]
    //     0x86caf4: stur            x2, [fp, #-0x20]
    // 0x86caf8: CheckStackOverflow
    //     0x86caf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86cafc: cmp             SP, x16
    //     0x86cb00: b.ls            #0x86cbf8
    // 0x86cb04: LoadField: r3 = r0->field_87
    //     0x86cb04: ldur            w3, [x0, #0x87]
    // 0x86cb08: DecompressPointer r3
    //     0x86cb08: add             x3, x3, HEAP, lsl #32
    // 0x86cb0c: stur            x3, [fp, #-0x10]
    // 0x86cb10: LoadField: r1 = r3->field_b
    //     0x86cb10: ldur            w1, [x3, #0xb]
    // 0x86cb14: LoadField: r4 = r3->field_f
    //     0x86cb14: ldur            w4, [x3, #0xf]
    // 0x86cb18: DecompressPointer r4
    //     0x86cb18: add             x4, x4, HEAP, lsl #32
    // 0x86cb1c: LoadField: r5 = r4->field_b
    //     0x86cb1c: ldur            w5, [x4, #0xb]
    // 0x86cb20: r4 = LoadInt32Instr(r1)
    //     0x86cb20: sbfx            x4, x1, #1, #0x1f
    // 0x86cb24: stur            x4, [fp, #-8]
    // 0x86cb28: r1 = LoadInt32Instr(r5)
    //     0x86cb28: sbfx            x1, x5, #1, #0x1f
    // 0x86cb2c: cmp             x4, x1
    // 0x86cb30: b.ne            #0x86cb3c
    // 0x86cb34: mov             x1, x3
    // 0x86cb38: r0 = _growToNextCapacity()
    //     0x86cb38: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x86cb3c: ldur            x3, [fp, #-0x18]
    // 0x86cb40: ldur            x2, [fp, #-0x20]
    // 0x86cb44: ldur            x0, [fp, #-0x10]
    // 0x86cb48: ldur            x4, [fp, #-8]
    // 0x86cb4c: add             x1, x4, #1
    // 0x86cb50: lsl             x5, x1, #1
    // 0x86cb54: StoreField: r0->field_b = r5
    //     0x86cb54: stur            w5, [x0, #0xb]
    // 0x86cb58: LoadField: r5 = r0->field_f
    //     0x86cb58: ldur            w5, [x0, #0xf]
    // 0x86cb5c: DecompressPointer r5
    //     0x86cb5c: add             x5, x5, HEAP, lsl #32
    // 0x86cb60: r0 = BoxInt64Instr(r2)
    //     0x86cb60: sbfiz           x0, x2, #1, #0x1f
    //     0x86cb64: cmp             x2, x0, asr #1
    //     0x86cb68: b.eq            #0x86cb74
    //     0x86cb6c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x86cb70: stur            x2, [x0, #7]
    // 0x86cb74: mov             x1, x5
    // 0x86cb78: mov             x5, x0
    // 0x86cb7c: ArrayStore: r1[r4] = r0  ; List_4
    //     0x86cb7c: add             x25, x1, x4, lsl #2
    //     0x86cb80: add             x25, x25, #0xf
    //     0x86cb84: str             w0, [x25]
    //     0x86cb88: tbz             w0, #0, #0x86cba4
    //     0x86cb8c: ldurb           w16, [x1, #-1]
    //     0x86cb90: ldurb           w17, [x0, #-1]
    //     0x86cb94: and             x16, x17, x16, lsr #2
    //     0x86cb98: tst             x16, HEAP, lsr #32
    //     0x86cb9c: b.eq            #0x86cba4
    //     0x86cba0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x86cba4: mov             x0, x5
    // 0x86cba8: StoreField: r3->field_8b = r0
    //     0x86cba8: stur            w0, [x3, #0x8b]
    //     0x86cbac: tbz             w0, #0, #0x86cbc8
    //     0x86cbb0: ldurb           w16, [x3, #-1]
    //     0x86cbb4: ldurb           w17, [x0, #-1]
    //     0x86cbb8: and             x16, x17, x16, lsr #2
    //     0x86cbbc: tst             x16, HEAP, lsr #32
    //     0x86cbc0: b.eq            #0x86cbc8
    //     0x86cbc4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x86cbc8: LoadField: r0 = r3->field_4b
    //     0x86cbc8: ldur            w0, [x3, #0x4b]
    // 0x86cbcc: DecompressPointer r0
    //     0x86cbcc: add             x0, x0, HEAP, lsl #32
    // 0x86cbd0: tbnz            w0, #4, #0x86cbe0
    // 0x86cbd4: LoadField: r0 = r3->field_73
    //     0x86cbd4: ldur            w0, [x3, #0x73]
    // 0x86cbd8: DecompressPointer r0
    //     0x86cbd8: add             x0, x0, HEAP, lsl #32
    // 0x86cbdc: tbnz            w0, #4, #0x86cbe8
    // 0x86cbe0: mov             x1, x3
    // 0x86cbe4: r0 = _checkDrag()
    //     0x86cbe4: bl              #0x759cf8  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_checkDrag
    // 0x86cbe8: r0 = Null
    //     0x86cbe8: mov             x0, NULL
    // 0x86cbec: LeaveFrame
    //     0x86cbec: mov             SP, fp
    //     0x86cbf0: ldp             fp, lr, [SP], #0x10
    // 0x86cbf4: ret
    //     0x86cbf4: ret             
    // 0x86cbf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86cbf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86cbfc: b               #0x86cb04
  }
  _ rejectGesture(/* No info */) {
    // ** addr: 0xcce890, size: 0x30
    // 0xcce890: EnterFrame
    //     0xcce890: stp             fp, lr, [SP, #-0x10]!
    //     0xcce894: mov             fp, SP
    // 0xcce898: CheckStackOverflow
    //     0xcce898: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcce89c: cmp             SP, x16
    //     0xcce8a0: b.ls            #0xcce8b8
    // 0xcce8a4: r0 = _giveUpPointer()
    //     0xcce8a4: bl              #0x75afec  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::_giveUpPointer
    // 0xcce8a8: r0 = Null
    //     0xcce8a8: mov             x0, NULL
    // 0xcce8ac: LeaveFrame
    //     0xcce8ac: mov             SP, fp
    //     0xcce8b0: ldp             fp, lr, [SP], #0x10
    // 0xcce8b4: ret
    //     0xcce8b4: ret             
    // 0xcce8b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcce8b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcce8bc: b               #0xcce8a4
  }
}

// class id: 3548, size: 0x90, field offset: 0x90
class HorizontalDragGestureRecognizer extends DragGestureRecognizer {

  _ _getPrimaryDragAxis(/* No info */) {
    // ** addr: 0xc03508, size: 0xc
    // 0xc03508: r0 = Instance__DragDirection
    //     0xc03508: add             x0, PP, #0x39, lsl #12  ; [pp+0x39c28] Obj!_DragDirection@e36da1
    //     0xc0350c: ldr             x0, [x0, #0xc28]
    // 0xc03510: ret
    //     0xc03510: ret             
  }
  _ hasSufficientGlobalDistanceToAccept(/* No info */) {
    // ** addr: 0xc230a4, size: 0xc0
    // 0xc230a4: d0 = 0.000000
    //     0xc230a4: eor             v0.16b, v0.16b, v0.16b
    // 0xc230a8: LoadField: r3 = r1->field_6f
    //     0xc230a8: ldur            w3, [x1, #0x6f]
    // 0xc230ac: DecompressPointer r3
    //     0xc230ac: add             x3, x3, HEAP, lsl #32
    // 0xc230b0: r16 = Sentinel
    //     0xc230b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc230b4: cmp             w3, w16
    // 0xc230b8: b.eq            #0xc23150
    // 0xc230bc: LoadField: d1 = r3->field_7
    //     0xc230bc: ldur            d1, [x3, #7]
    // 0xc230c0: fcmp            d1, d0
    // 0xc230c4: b.ne            #0xc230d0
    // 0xc230c8: d0 = 0.000000
    //     0xc230c8: eor             v0.16b, v0.16b, v0.16b
    // 0xc230cc: b               #0xc230e4
    // 0xc230d0: fcmp            d0, d1
    // 0xc230d4: b.le            #0xc230e0
    // 0xc230d8: fneg            d0, d1
    // 0xc230dc: b               #0xc230e4
    // 0xc230e0: mov             v0.16b, v1.16b
    // 0xc230e4: LoadField: r3 = r1->field_7
    //     0xc230e4: ldur            w3, [x1, #7]
    // 0xc230e8: DecompressPointer r3
    //     0xc230e8: add             x3, x3, HEAP, lsl #32
    // 0xc230ec: LoadField: r1 = r2->field_7
    //     0xc230ec: ldur            x1, [x2, #7]
    // 0xc230f0: cmp             x1, #2
    // 0xc230f4: b.gt            #0xc23110
    // 0xc230f8: cmp             x1, #1
    // 0xc230fc: b.gt            #0xc23110
    // 0xc23100: cmp             x1, #0
    // 0xc23104: b.le            #0xc23110
    // 0xc23108: d1 = 1.000000
    //     0xc23108: fmov            d1, #1.00000000
    // 0xc2310c: b               #0xc2313c
    // 0xc23110: cmp             w3, NULL
    // 0xc23114: b.ne            #0xc23120
    // 0xc23118: r1 = Null
    //     0xc23118: mov             x1, NULL
    // 0xc2311c: b               #0xc23128
    // 0xc23120: LoadField: r1 = r3->field_7
    //     0xc23120: ldur            w1, [x3, #7]
    // 0xc23124: DecompressPointer r1
    //     0xc23124: add             x1, x1, HEAP, lsl #32
    // 0xc23128: cmp             w1, NULL
    // 0xc2312c: b.ne            #0xc23138
    // 0xc23130: d1 = 18.000000
    //     0xc23130: fmov            d1, #18.00000000
    // 0xc23134: b               #0xc2313c
    // 0xc23138: LoadField: d1 = r1->field_7
    //     0xc23138: ldur            d1, [x1, #7]
    // 0xc2313c: fcmp            d0, d1
    // 0xc23140: r16 = true
    //     0xc23140: add             x16, NULL, #0x20  ; true
    // 0xc23144: r17 = false
    //     0xc23144: add             x17, NULL, #0x30  ; false
    // 0xc23148: csel            x0, x16, x17, gt
    // 0xc2314c: ret
    //     0xc2314c: ret             
    // 0xc23150: EnterFrame
    //     0xc23150: stp             fp, lr, [SP, #-0x10]!
    //     0xc23154: mov             fp, SP
    // 0xc23158: r9 = _globalDistanceMoved
    //     0xc23158: add             x9, PP, #0x39, lsl #12  ; [pp+0x39c18] Field <DragGestureRecognizer._globalDistanceMoved@428099969>: late (offset: 0x70)
    //     0xc2315c: ldr             x9, [x9, #0xc18]
    // 0xc23160: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xc23160: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
  }
  _ considerFling(/* No info */) {
    // ** addr: 0xce0154, size: 0x174
    // 0xce0154: EnterFrame
    //     0xce0154: stp             fp, lr, [SP, #-0x10]!
    //     0xce0158: mov             fp, SP
    // 0xce015c: AllocStack(0x20)
    //     0xce015c: sub             SP, SP, #0x20
    // 0xce0160: SetupParameters(HorizontalDragGestureRecognizer this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xce0160: mov             x4, x1
    //     0xce0164: mov             x0, x2
    //     0xce0168: stur            x1, [fp, #-8]
    //     0xce016c: stur            x2, [fp, #-0x10]
    // 0xce0170: CheckStackOverflow
    //     0xce0170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce0174: cmp             SP, x16
    //     0xce0178: b.ls            #0xce0298
    // 0xce017c: mov             x1, x4
    // 0xce0180: mov             x2, x0
    // 0xce0184: r0 = isFlingGesture()
    //     0xce0184: bl              #0xce02c8  ; [package:flutter/src/gestures/monodrag.dart] HorizontalDragGestureRecognizer::isFlingGesture
    // 0xce0188: tbz             w0, #4, #0xce019c
    // 0xce018c: r0 = Null
    //     0xce018c: mov             x0, NULL
    // 0xce0190: LeaveFrame
    //     0xce0190: mov             SP, fp
    //     0xce0194: ldp             fp, lr, [SP], #0x10
    // 0xce0198: ret
    //     0xce0198: ret             
    // 0xce019c: ldur            x0, [fp, #-8]
    // 0xce01a0: LoadField: r1 = r0->field_47
    //     0xce01a0: ldur            w1, [x0, #0x47]
    // 0xce01a4: DecompressPointer r1
    //     0xce01a4: add             x1, x1, HEAP, lsl #32
    // 0xce01a8: cmp             w1, NULL
    // 0xce01ac: b.ne            #0xce01bc
    // 0xce01b0: d0 = 8000.000000
    //     0xce01b0: add             x17, PP, #0x44, lsl #12  ; [pp+0x44a70] IMM: double(8000) from 0x40bf400000000000
    //     0xce01b4: ldr             d0, [x17, #0xa70]
    // 0xce01b8: b               #0xce01c0
    // 0xce01bc: LoadField: d0 = r1->field_7
    //     0xce01bc: ldur            d0, [x1, #7]
    // 0xce01c0: ldur            x1, [fp, #-0x10]
    // 0xce01c4: LoadField: r2 = r1->field_7
    //     0xce01c4: ldur            w2, [x1, #7]
    // 0xce01c8: DecompressPointer r2
    //     0xce01c8: add             x2, x2, HEAP, lsl #32
    // 0xce01cc: LoadField: d1 = r2->field_7
    //     0xce01cc: ldur            d1, [x2, #7]
    // 0xce01d0: fneg            d2, d0
    // 0xce01d4: fcmp            d2, d1
    // 0xce01d8: b.le            #0xce01e4
    // 0xce01dc: mov             v0.16b, v2.16b
    // 0xce01e0: b               #0xce01f8
    // 0xce01e4: fcmp            d1, d0
    // 0xce01e8: b.gt            #0xce01f8
    // 0xce01ec: fcmp            d1, d1
    // 0xce01f0: b.vs            #0xce01f8
    // 0xce01f4: mov             v0.16b, v1.16b
    // 0xce01f8: stur            d0, [fp, #-0x20]
    // 0xce01fc: r0 = Offset()
    //     0xce01fc: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xce0200: ldur            d0, [fp, #-0x20]
    // 0xce0204: stur            x0, [fp, #-0x10]
    // 0xce0208: StoreField: r0->field_7 = d0
    //     0xce0208: stur            d0, [x0, #7]
    // 0xce020c: StoreField: r0->field_f = rZR
    //     0xce020c: stur            xzr, [x0, #0xf]
    // 0xce0210: r0 = Velocity()
    //     0xce0210: bl              #0x7d6fb8  ; AllocateVelocityStub -> Velocity (size=0xc)
    // 0xce0214: mov             x1, x0
    // 0xce0218: ldur            x0, [fp, #-0x10]
    // 0xce021c: stur            x1, [fp, #-0x18]
    // 0xce0220: StoreField: r1->field_7 = r0
    //     0xce0220: stur            w0, [x1, #7]
    // 0xce0224: ldur            x0, [fp, #-8]
    // 0xce0228: LoadField: r2 = r0->field_5f
    //     0xce0228: ldur            w2, [x0, #0x5f]
    // 0xce022c: DecompressPointer r2
    //     0xce022c: add             x2, x2, HEAP, lsl #32
    // 0xce0230: r16 = Sentinel
    //     0xce0230: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xce0234: cmp             w2, w16
    // 0xce0238: b.eq            #0xce02a0
    // 0xce023c: LoadField: r0 = r2->field_b
    //     0xce023c: ldur            w0, [x2, #0xb]
    // 0xce0240: DecompressPointer r0
    //     0xce0240: add             x0, x0, HEAP, lsl #32
    // 0xce0244: stur            x0, [fp, #-8]
    // 0xce0248: r0 = DragEndDetails()
    //     0xce0248: bl              #0x7d6e54  ; AllocateDragEndDetailsStub -> DragEndDetails (size=0x14)
    // 0xce024c: ldur            x1, [fp, #-0x18]
    // 0xce0250: StoreField: r0->field_7 = r1
    //     0xce0250: stur            w1, [x0, #7]
    // 0xce0254: ldur            d0, [fp, #-0x20]
    // 0xce0258: r1 = inline_Allocate_Double()
    //     0xce0258: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xce025c: add             x1, x1, #0x10
    //     0xce0260: cmp             x2, x1
    //     0xce0264: b.ls            #0xce02ac
    //     0xce0268: str             x1, [THR, #0x50]  ; THR::top
    //     0xce026c: sub             x1, x1, #0xf
    //     0xce0270: movz            x2, #0xe15c
    //     0xce0274: movk            x2, #0x3, lsl #16
    //     0xce0278: stur            x2, [x1, #-1]
    // 0xce027c: StoreField: r1->field_7 = d0
    //     0xce027c: stur            d0, [x1, #7]
    // 0xce0280: StoreField: r0->field_b = r1
    //     0xce0280: stur            w1, [x0, #0xb]
    // 0xce0284: ldur            x1, [fp, #-8]
    // 0xce0288: StoreField: r0->field_f = r1
    //     0xce0288: stur            w1, [x0, #0xf]
    // 0xce028c: LeaveFrame
    //     0xce028c: mov             SP, fp
    //     0xce0290: ldp             fp, lr, [SP], #0x10
    // 0xce0294: ret
    //     0xce0294: ret             
    // 0xce0298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce0298: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce029c: b               #0xce017c
    // 0xce02a0: r9 = _lastPosition
    //     0xce02a0: add             x9, PP, #0x39, lsl #12  ; [pp+0x39c68] Field <DragGestureRecognizer._lastPosition@428099969>: late (offset: 0x60)
    //     0xce02a4: ldr             x9, [x9, #0xc68]
    // 0xce02a8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xce02a8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xce02ac: SaveReg d0
    //     0xce02ac: str             q0, [SP, #-0x10]!
    // 0xce02b0: SaveReg r0
    //     0xce02b0: str             x0, [SP, #-8]!
    // 0xce02b4: r0 = AllocateDouble()
    //     0xce02b4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xce02b8: mov             x1, x0
    // 0xce02bc: RestoreReg r0
    //     0xce02bc: ldr             x0, [SP], #8
    // 0xce02c0: RestoreReg d0
    //     0xce02c0: ldr             q0, [SP], #0x10
    // 0xce02c4: b               #0xce027c
  }
  _ isFlingGesture(/* No info */) {
    // ** addr: 0xce02c8, size: 0x118
    // 0xce02c8: LoadField: r4 = r1->field_43
    //     0xce02c8: ldur            w4, [x1, #0x43]
    // 0xce02cc: DecompressPointer r4
    //     0xce02cc: add             x4, x4, HEAP, lsl #32
    // 0xce02d0: cmp             w4, NULL
    // 0xce02d4: b.ne            #0xce02e0
    // 0xce02d8: d0 = 50.000000
    //     0xce02d8: ldr             d0, [PP, #0x5ac0]  ; [pp+0x5ac0] IMM: double(50) from 0x4049000000000000
    // 0xce02dc: b               #0xce02e4
    // 0xce02e0: LoadField: d0 = r4->field_7
    //     0xce02e0: ldur            d0, [x4, #7]
    // 0xce02e4: LoadField: r4 = r1->field_3f
    //     0xce02e4: ldur            w4, [x1, #0x3f]
    // 0xce02e8: DecompressPointer r4
    //     0xce02e8: add             x4, x4, HEAP, lsl #32
    // 0xce02ec: cmp             w4, NULL
    // 0xce02f0: b.ne            #0xce0354
    // 0xce02f4: LoadField: r5 = r1->field_7
    //     0xce02f4: ldur            w5, [x1, #7]
    // 0xce02f8: DecompressPointer r5
    //     0xce02f8: add             x5, x5, HEAP, lsl #32
    // 0xce02fc: LoadField: r1 = r3->field_7
    //     0xce02fc: ldur            x1, [x3, #7]
    // 0xce0300: cmp             x1, #2
    // 0xce0304: b.gt            #0xce0320
    // 0xce0308: cmp             x1, #1
    // 0xce030c: b.gt            #0xce0320
    // 0xce0310: cmp             x1, #0
    // 0xce0314: b.le            #0xce0320
    // 0xce0318: d1 = 1.000000
    //     0xce0318: fmov            d1, #1.00000000
    // 0xce031c: b               #0xce034c
    // 0xce0320: cmp             w5, NULL
    // 0xce0324: b.ne            #0xce0330
    // 0xce0328: r1 = Null
    //     0xce0328: mov             x1, NULL
    // 0xce032c: b               #0xce0338
    // 0xce0330: LoadField: r1 = r5->field_7
    //     0xce0330: ldur            w1, [x5, #7]
    // 0xce0334: DecompressPointer r1
    //     0xce0334: add             x1, x1, HEAP, lsl #32
    // 0xce0338: cmp             w1, NULL
    // 0xce033c: b.ne            #0xce0348
    // 0xce0340: d1 = 18.000000
    //     0xce0340: fmov            d1, #18.00000000
    // 0xce0344: b               #0xce034c
    // 0xce0348: LoadField: d1 = r1->field_7
    //     0xce0348: ldur            d1, [x1, #7]
    // 0xce034c: mov             v2.16b, v1.16b
    // 0xce0350: b               #0xce035c
    // 0xce0354: LoadField: d1 = r4->field_7
    //     0xce0354: ldur            d1, [x4, #7]
    // 0xce0358: mov             v2.16b, v1.16b
    // 0xce035c: d1 = 0.000000
    //     0xce035c: eor             v1.16b, v1.16b, v1.16b
    // 0xce0360: LoadField: r1 = r2->field_7
    //     0xce0360: ldur            w1, [x2, #7]
    // 0xce0364: DecompressPointer r1
    //     0xce0364: add             x1, x1, HEAP, lsl #32
    // 0xce0368: LoadField: d3 = r1->field_7
    //     0xce0368: ldur            d3, [x1, #7]
    // 0xce036c: fcmp            d3, d1
    // 0xce0370: b.ne            #0xce037c
    // 0xce0374: d3 = 0.000000
    //     0xce0374: eor             v3.16b, v3.16b, v3.16b
    // 0xce0378: b               #0xce038c
    // 0xce037c: fcmp            d1, d3
    // 0xce0380: b.le            #0xce038c
    // 0xce0384: fneg            d4, d3
    // 0xce0388: mov             v3.16b, v4.16b
    // 0xce038c: fcmp            d3, d0
    // 0xce0390: b.le            #0xce03d8
    // 0xce0394: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xce0394: ldur            w1, [x2, #0x17]
    // 0xce0398: DecompressPointer r1
    //     0xce0398: add             x1, x1, HEAP, lsl #32
    // 0xce039c: LoadField: d0 = r1->field_7
    //     0xce039c: ldur            d0, [x1, #7]
    // 0xce03a0: fcmp            d0, d1
    // 0xce03a4: b.ne            #0xce03b0
    // 0xce03a8: d0 = 0.000000
    //     0xce03a8: eor             v0.16b, v0.16b, v0.16b
    // 0xce03ac: b               #0xce03c0
    // 0xce03b0: fcmp            d1, d0
    // 0xce03b4: b.le            #0xce03c0
    // 0xce03b8: fneg            d1, d0
    // 0xce03bc: mov             v0.16b, v1.16b
    // 0xce03c0: fcmp            d0, d2
    // 0xce03c4: r16 = true
    //     0xce03c4: add             x16, NULL, #0x20  ; true
    // 0xce03c8: r17 = false
    //     0xce03c8: add             x17, NULL, #0x30  ; false
    // 0xce03cc: csel            x1, x16, x17, gt
    // 0xce03d0: mov             x0, x1
    // 0xce03d4: b               #0xce03dc
    // 0xce03d8: r0 = false
    //     0xce03d8: add             x0, NULL, #0x30  ; false
    // 0xce03dc: ret
    //     0xce03dc: ret             
  }
  _ _getPrimaryValueFromOffset(/* No info */) {
    // ** addr: 0xd8a0a0, size: 0x50
    // 0xd8a0a0: EnterFrame
    //     0xd8a0a0: stp             fp, lr, [SP, #-0x10]!
    //     0xd8a0a4: mov             fp, SP
    // 0xd8a0a8: LoadField: d0 = r2->field_7
    //     0xd8a0a8: ldur            d0, [x2, #7]
    // 0xd8a0ac: r0 = inline_Allocate_Double()
    //     0xd8a0ac: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xd8a0b0: add             x0, x0, #0x10
    //     0xd8a0b4: cmp             x1, x0
    //     0xd8a0b8: b.ls            #0xd8a0e0
    //     0xd8a0bc: str             x0, [THR, #0x50]  ; THR::top
    //     0xd8a0c0: sub             x0, x0, #0xf
    //     0xd8a0c4: movz            x1, #0xe15c
    //     0xd8a0c8: movk            x1, #0x3, lsl #16
    //     0xd8a0cc: stur            x1, [x0, #-1]
    // 0xd8a0d0: StoreField: r0->field_7 = d0
    //     0xd8a0d0: stur            d0, [x0, #7]
    // 0xd8a0d4: LeaveFrame
    //     0xd8a0d4: mov             SP, fp
    //     0xd8a0d8: ldp             fp, lr, [SP], #0x10
    // 0xd8a0dc: ret
    //     0xd8a0dc: ret             
    // 0xd8a0e0: SaveReg d0
    //     0xd8a0e0: str             q0, [SP, #-0x10]!
    // 0xd8a0e4: r0 = AllocateDouble()
    //     0xd8a0e4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd8a0e8: RestoreReg d0
    //     0xd8a0e8: ldr             q0, [SP], #0x10
    // 0xd8a0ec: b               #0xd8a0d0
  }
  _ _getDeltaForDetails(/* No info */) {
    // ** addr: 0xd8d5b0, size: 0x30
    // 0xd8d5b0: EnterFrame
    //     0xd8d5b0: stp             fp, lr, [SP, #-0x10]!
    //     0xd8d5b4: mov             fp, SP
    // 0xd8d5b8: AllocStack(0x8)
    //     0xd8d5b8: sub             SP, SP, #8
    // 0xd8d5bc: LoadField: d0 = r2->field_7
    //     0xd8d5bc: ldur            d0, [x2, #7]
    // 0xd8d5c0: stur            d0, [fp, #-8]
    // 0xd8d5c4: r0 = Offset()
    //     0xd8d5c4: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xd8d5c8: ldur            d0, [fp, #-8]
    // 0xd8d5cc: StoreField: r0->field_7 = d0
    //     0xd8d5cc: stur            d0, [x0, #7]
    // 0xd8d5d0: StoreField: r0->field_f = rZR
    //     0xd8d5d0: stur            xzr, [x0, #0xf]
    // 0xd8d5d4: LeaveFrame
    //     0xd8d5d4: mov             SP, fp
    //     0xd8d5d8: ldp             fp, lr, [SP], #0x10
    // 0xd8d5dc: ret
    //     0xd8d5dc: ret             
  }
}

// class id: 3550, size: 0x90, field offset: 0x90
class VerticalDragGestureRecognizer extends DragGestureRecognizer {

  _ _getPrimaryDragAxis(/* No info */) {
    // ** addr: 0xc034fc, size: 0xc
    // 0xc034fc: r0 = Instance__DragDirection
    //     0xc034fc: add             x0, PP, #0x39, lsl #12  ; [pp+0x39c30] Obj!_DragDirection@e36d81
    //     0xc03500: ldr             x0, [x0, #0xc30]
    // 0xc03504: ret
    //     0xc03504: ret             
  }
  _ considerFling(/* No info */) {
    // ** addr: 0xcdfec8, size: 0x174
    // 0xcdfec8: EnterFrame
    //     0xcdfec8: stp             fp, lr, [SP, #-0x10]!
    //     0xcdfecc: mov             fp, SP
    // 0xcdfed0: AllocStack(0x20)
    //     0xcdfed0: sub             SP, SP, #0x20
    // 0xcdfed4: SetupParameters(VerticalDragGestureRecognizer this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xcdfed4: mov             x4, x1
    //     0xcdfed8: mov             x0, x2
    //     0xcdfedc: stur            x1, [fp, #-8]
    //     0xcdfee0: stur            x2, [fp, #-0x10]
    // 0xcdfee4: CheckStackOverflow
    //     0xcdfee4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcdfee8: cmp             SP, x16
    //     0xcdfeec: b.ls            #0xce000c
    // 0xcdfef0: mov             x1, x4
    // 0xcdfef4: mov             x2, x0
    // 0xcdfef8: r0 = isFlingGesture()
    //     0xcdfef8: bl              #0xce003c  ; [package:flutter/src/gestures/monodrag.dart] VerticalDragGestureRecognizer::isFlingGesture
    // 0xcdfefc: tbz             w0, #4, #0xcdff10
    // 0xcdff00: r0 = Null
    //     0xcdff00: mov             x0, NULL
    // 0xcdff04: LeaveFrame
    //     0xcdff04: mov             SP, fp
    //     0xcdff08: ldp             fp, lr, [SP], #0x10
    // 0xcdff0c: ret
    //     0xcdff0c: ret             
    // 0xcdff10: ldur            x0, [fp, #-8]
    // 0xcdff14: LoadField: r1 = r0->field_47
    //     0xcdff14: ldur            w1, [x0, #0x47]
    // 0xcdff18: DecompressPointer r1
    //     0xcdff18: add             x1, x1, HEAP, lsl #32
    // 0xcdff1c: cmp             w1, NULL
    // 0xcdff20: b.ne            #0xcdff30
    // 0xcdff24: d0 = 8000.000000
    //     0xcdff24: add             x17, PP, #0x44, lsl #12  ; [pp+0x44a70] IMM: double(8000) from 0x40bf400000000000
    //     0xcdff28: ldr             d0, [x17, #0xa70]
    // 0xcdff2c: b               #0xcdff34
    // 0xcdff30: LoadField: d0 = r1->field_7
    //     0xcdff30: ldur            d0, [x1, #7]
    // 0xcdff34: ldur            x1, [fp, #-0x10]
    // 0xcdff38: LoadField: r2 = r1->field_7
    //     0xcdff38: ldur            w2, [x1, #7]
    // 0xcdff3c: DecompressPointer r2
    //     0xcdff3c: add             x2, x2, HEAP, lsl #32
    // 0xcdff40: LoadField: d1 = r2->field_f
    //     0xcdff40: ldur            d1, [x2, #0xf]
    // 0xcdff44: fneg            d2, d0
    // 0xcdff48: fcmp            d2, d1
    // 0xcdff4c: b.le            #0xcdff58
    // 0xcdff50: mov             v0.16b, v2.16b
    // 0xcdff54: b               #0xcdff6c
    // 0xcdff58: fcmp            d1, d0
    // 0xcdff5c: b.gt            #0xcdff6c
    // 0xcdff60: fcmp            d1, d1
    // 0xcdff64: b.vs            #0xcdff6c
    // 0xcdff68: mov             v0.16b, v1.16b
    // 0xcdff6c: stur            d0, [fp, #-0x20]
    // 0xcdff70: r0 = Offset()
    //     0xcdff70: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xcdff74: stur            x0, [fp, #-0x10]
    // 0xcdff78: StoreField: r0->field_7 = rZR
    //     0xcdff78: stur            xzr, [x0, #7]
    // 0xcdff7c: ldur            d0, [fp, #-0x20]
    // 0xcdff80: StoreField: r0->field_f = d0
    //     0xcdff80: stur            d0, [x0, #0xf]
    // 0xcdff84: r0 = Velocity()
    //     0xcdff84: bl              #0x7d6fb8  ; AllocateVelocityStub -> Velocity (size=0xc)
    // 0xcdff88: mov             x1, x0
    // 0xcdff8c: ldur            x0, [fp, #-0x10]
    // 0xcdff90: stur            x1, [fp, #-0x18]
    // 0xcdff94: StoreField: r1->field_7 = r0
    //     0xcdff94: stur            w0, [x1, #7]
    // 0xcdff98: ldur            x0, [fp, #-8]
    // 0xcdff9c: LoadField: r2 = r0->field_5f
    //     0xcdff9c: ldur            w2, [x0, #0x5f]
    // 0xcdffa0: DecompressPointer r2
    //     0xcdffa0: add             x2, x2, HEAP, lsl #32
    // 0xcdffa4: r16 = Sentinel
    //     0xcdffa4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xcdffa8: cmp             w2, w16
    // 0xcdffac: b.eq            #0xce0014
    // 0xcdffb0: LoadField: r0 = r2->field_b
    //     0xcdffb0: ldur            w0, [x2, #0xb]
    // 0xcdffb4: DecompressPointer r0
    //     0xcdffb4: add             x0, x0, HEAP, lsl #32
    // 0xcdffb8: stur            x0, [fp, #-8]
    // 0xcdffbc: r0 = DragEndDetails()
    //     0xcdffbc: bl              #0x7d6e54  ; AllocateDragEndDetailsStub -> DragEndDetails (size=0x14)
    // 0xcdffc0: ldur            x1, [fp, #-0x18]
    // 0xcdffc4: StoreField: r0->field_7 = r1
    //     0xcdffc4: stur            w1, [x0, #7]
    // 0xcdffc8: ldur            d0, [fp, #-0x20]
    // 0xcdffcc: r1 = inline_Allocate_Double()
    //     0xcdffcc: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xcdffd0: add             x1, x1, #0x10
    //     0xcdffd4: cmp             x2, x1
    //     0xcdffd8: b.ls            #0xce0020
    //     0xcdffdc: str             x1, [THR, #0x50]  ; THR::top
    //     0xcdffe0: sub             x1, x1, #0xf
    //     0xcdffe4: movz            x2, #0xe15c
    //     0xcdffe8: movk            x2, #0x3, lsl #16
    //     0xcdffec: stur            x2, [x1, #-1]
    // 0xcdfff0: StoreField: r1->field_7 = d0
    //     0xcdfff0: stur            d0, [x1, #7]
    // 0xcdfff4: StoreField: r0->field_b = r1
    //     0xcdfff4: stur            w1, [x0, #0xb]
    // 0xcdfff8: ldur            x1, [fp, #-8]
    // 0xcdfffc: StoreField: r0->field_f = r1
    //     0xcdfffc: stur            w1, [x0, #0xf]
    // 0xce0000: LeaveFrame
    //     0xce0000: mov             SP, fp
    //     0xce0004: ldp             fp, lr, [SP], #0x10
    // 0xce0008: ret
    //     0xce0008: ret             
    // 0xce000c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce000c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce0010: b               #0xcdfef0
    // 0xce0014: r9 = _lastPosition
    //     0xce0014: add             x9, PP, #0x39, lsl #12  ; [pp+0x39c68] Field <DragGestureRecognizer._lastPosition@428099969>: late (offset: 0x60)
    //     0xce0018: ldr             x9, [x9, #0xc68]
    // 0xce001c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xce001c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xce0020: SaveReg d0
    //     0xce0020: str             q0, [SP, #-0x10]!
    // 0xce0024: SaveReg r0
    //     0xce0024: str             x0, [SP, #-8]!
    // 0xce0028: r0 = AllocateDouble()
    //     0xce0028: bl              #0xec2254  ; AllocateDoubleStub
    // 0xce002c: mov             x1, x0
    // 0xce0030: RestoreReg r0
    //     0xce0030: ldr             x0, [SP], #8
    // 0xce0034: RestoreReg d0
    //     0xce0034: ldr             q0, [SP], #0x10
    // 0xce0038: b               #0xcdfff0
  }
  _ isFlingGesture(/* No info */) {
    // ** addr: 0xce003c, size: 0x118
    // 0xce003c: LoadField: r4 = r1->field_43
    //     0xce003c: ldur            w4, [x1, #0x43]
    // 0xce0040: DecompressPointer r4
    //     0xce0040: add             x4, x4, HEAP, lsl #32
    // 0xce0044: cmp             w4, NULL
    // 0xce0048: b.ne            #0xce0054
    // 0xce004c: d0 = 50.000000
    //     0xce004c: ldr             d0, [PP, #0x5ac0]  ; [pp+0x5ac0] IMM: double(50) from 0x4049000000000000
    // 0xce0050: b               #0xce0058
    // 0xce0054: LoadField: d0 = r4->field_7
    //     0xce0054: ldur            d0, [x4, #7]
    // 0xce0058: LoadField: r4 = r1->field_3f
    //     0xce0058: ldur            w4, [x1, #0x3f]
    // 0xce005c: DecompressPointer r4
    //     0xce005c: add             x4, x4, HEAP, lsl #32
    // 0xce0060: cmp             w4, NULL
    // 0xce0064: b.ne            #0xce00c8
    // 0xce0068: LoadField: r5 = r1->field_7
    //     0xce0068: ldur            w5, [x1, #7]
    // 0xce006c: DecompressPointer r5
    //     0xce006c: add             x5, x5, HEAP, lsl #32
    // 0xce0070: LoadField: r1 = r3->field_7
    //     0xce0070: ldur            x1, [x3, #7]
    // 0xce0074: cmp             x1, #2
    // 0xce0078: b.gt            #0xce0094
    // 0xce007c: cmp             x1, #1
    // 0xce0080: b.gt            #0xce0094
    // 0xce0084: cmp             x1, #0
    // 0xce0088: b.le            #0xce0094
    // 0xce008c: d1 = 1.000000
    //     0xce008c: fmov            d1, #1.00000000
    // 0xce0090: b               #0xce00c0
    // 0xce0094: cmp             w5, NULL
    // 0xce0098: b.ne            #0xce00a4
    // 0xce009c: r1 = Null
    //     0xce009c: mov             x1, NULL
    // 0xce00a0: b               #0xce00ac
    // 0xce00a4: LoadField: r1 = r5->field_7
    //     0xce00a4: ldur            w1, [x5, #7]
    // 0xce00a8: DecompressPointer r1
    //     0xce00a8: add             x1, x1, HEAP, lsl #32
    // 0xce00ac: cmp             w1, NULL
    // 0xce00b0: b.ne            #0xce00bc
    // 0xce00b4: d1 = 18.000000
    //     0xce00b4: fmov            d1, #18.00000000
    // 0xce00b8: b               #0xce00c0
    // 0xce00bc: LoadField: d1 = r1->field_7
    //     0xce00bc: ldur            d1, [x1, #7]
    // 0xce00c0: mov             v2.16b, v1.16b
    // 0xce00c4: b               #0xce00d0
    // 0xce00c8: LoadField: d1 = r4->field_7
    //     0xce00c8: ldur            d1, [x4, #7]
    // 0xce00cc: mov             v2.16b, v1.16b
    // 0xce00d0: d1 = 0.000000
    //     0xce00d0: eor             v1.16b, v1.16b, v1.16b
    // 0xce00d4: LoadField: r1 = r2->field_7
    //     0xce00d4: ldur            w1, [x2, #7]
    // 0xce00d8: DecompressPointer r1
    //     0xce00d8: add             x1, x1, HEAP, lsl #32
    // 0xce00dc: LoadField: d3 = r1->field_f
    //     0xce00dc: ldur            d3, [x1, #0xf]
    // 0xce00e0: fcmp            d3, d1
    // 0xce00e4: b.ne            #0xce00f0
    // 0xce00e8: d3 = 0.000000
    //     0xce00e8: eor             v3.16b, v3.16b, v3.16b
    // 0xce00ec: b               #0xce0100
    // 0xce00f0: fcmp            d1, d3
    // 0xce00f4: b.le            #0xce0100
    // 0xce00f8: fneg            d4, d3
    // 0xce00fc: mov             v3.16b, v4.16b
    // 0xce0100: fcmp            d3, d0
    // 0xce0104: b.le            #0xce014c
    // 0xce0108: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xce0108: ldur            w1, [x2, #0x17]
    // 0xce010c: DecompressPointer r1
    //     0xce010c: add             x1, x1, HEAP, lsl #32
    // 0xce0110: LoadField: d0 = r1->field_f
    //     0xce0110: ldur            d0, [x1, #0xf]
    // 0xce0114: fcmp            d0, d1
    // 0xce0118: b.ne            #0xce0124
    // 0xce011c: d0 = 0.000000
    //     0xce011c: eor             v0.16b, v0.16b, v0.16b
    // 0xce0120: b               #0xce0134
    // 0xce0124: fcmp            d1, d0
    // 0xce0128: b.le            #0xce0134
    // 0xce012c: fneg            d1, d0
    // 0xce0130: mov             v0.16b, v1.16b
    // 0xce0134: fcmp            d0, d2
    // 0xce0138: r16 = true
    //     0xce0138: add             x16, NULL, #0x20  ; true
    // 0xce013c: r17 = false
    //     0xce013c: add             x17, NULL, #0x30  ; false
    // 0xce0140: csel            x1, x16, x17, gt
    // 0xce0144: mov             x0, x1
    // 0xce0148: b               #0xce0150
    // 0xce014c: r0 = false
    //     0xce014c: add             x0, NULL, #0x30  ; false
    // 0xce0150: ret
    //     0xce0150: ret             
  }
  _ _getPrimaryValueFromOffset(/* No info */) {
    // ** addr: 0xd8a050, size: 0x50
    // 0xd8a050: EnterFrame
    //     0xd8a050: stp             fp, lr, [SP, #-0x10]!
    //     0xd8a054: mov             fp, SP
    // 0xd8a058: LoadField: d0 = r2->field_f
    //     0xd8a058: ldur            d0, [x2, #0xf]
    // 0xd8a05c: r0 = inline_Allocate_Double()
    //     0xd8a05c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xd8a060: add             x0, x0, #0x10
    //     0xd8a064: cmp             x1, x0
    //     0xd8a068: b.ls            #0xd8a090
    //     0xd8a06c: str             x0, [THR, #0x50]  ; THR::top
    //     0xd8a070: sub             x0, x0, #0xf
    //     0xd8a074: movz            x1, #0xe15c
    //     0xd8a078: movk            x1, #0x3, lsl #16
    //     0xd8a07c: stur            x1, [x0, #-1]
    // 0xd8a080: StoreField: r0->field_7 = d0
    //     0xd8a080: stur            d0, [x0, #7]
    // 0xd8a084: LeaveFrame
    //     0xd8a084: mov             SP, fp
    //     0xd8a088: ldp             fp, lr, [SP], #0x10
    // 0xd8a08c: ret
    //     0xd8a08c: ret             
    // 0xd8a090: SaveReg d0
    //     0xd8a090: str             q0, [SP, #-0x10]!
    // 0xd8a094: r0 = AllocateDouble()
    //     0xd8a094: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd8a098: RestoreReg d0
    //     0xd8a098: ldr             q0, [SP], #0x10
    // 0xd8a09c: b               #0xd8a080
  }
  _ _getDeltaForDetails(/* No info */) {
    // ** addr: 0xd8d580, size: 0x30
    // 0xd8d580: EnterFrame
    //     0xd8d580: stp             fp, lr, [SP, #-0x10]!
    //     0xd8d584: mov             fp, SP
    // 0xd8d588: AllocStack(0x8)
    //     0xd8d588: sub             SP, SP, #8
    // 0xd8d58c: LoadField: d0 = r2->field_f
    //     0xd8d58c: ldur            d0, [x2, #0xf]
    // 0xd8d590: stur            d0, [fp, #-8]
    // 0xd8d594: r0 = Offset()
    //     0xd8d594: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xd8d598: StoreField: r0->field_7 = rZR
    //     0xd8d598: stur            xzr, [x0, #7]
    // 0xd8d59c: ldur            d0, [fp, #-8]
    // 0xd8d5a0: StoreField: r0->field_f = d0
    //     0xd8d5a0: stur            d0, [x0, #0xf]
    // 0xd8d5a4: LeaveFrame
    //     0xd8d5a4: mov             SP, fp
    //     0xd8d5a8: ldp             fp, lr, [SP], #0x10
    // 0xd8d5ac: ret
    //     0xd8d5ac: ret             
  }
}

// class id: 3552, size: 0x90, field offset: 0x90
class PanGestureRecognizer extends DragGestureRecognizer {

  _ hasSufficientGlobalDistanceToAccept(/* No info */) {
    // ** addr: 0xc22ffc, size: 0xa8
    // 0xc22ffc: EnterFrame
    //     0xc22ffc: stp             fp, lr, [SP, #-0x10]!
    //     0xc23000: mov             fp, SP
    // 0xc23004: AllocStack(0x8)
    //     0xc23004: sub             SP, SP, #8
    // 0xc23008: d0 = 0.000000
    //     0xc23008: eor             v0.16b, v0.16b, v0.16b
    // 0xc2300c: mov             x0, x1
    // 0xc23010: mov             x1, x2
    // 0xc23014: CheckStackOverflow
    //     0xc23014: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc23018: cmp             SP, x16
    //     0xc2301c: b.ls            #0xc23090
    // 0xc23020: LoadField: r2 = r0->field_6f
    //     0xc23020: ldur            w2, [x0, #0x6f]
    // 0xc23024: DecompressPointer r2
    //     0xc23024: add             x2, x2, HEAP, lsl #32
    // 0xc23028: r16 = Sentinel
    //     0xc23028: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc2302c: cmp             w2, w16
    // 0xc23030: b.eq            #0xc23098
    // 0xc23034: LoadField: d1 = r2->field_7
    //     0xc23034: ldur            d1, [x2, #7]
    // 0xc23038: fcmp            d1, d0
    // 0xc2303c: b.ne            #0xc23048
    // 0xc23040: d0 = 0.000000
    //     0xc23040: eor             v0.16b, v0.16b, v0.16b
    // 0xc23044: b               #0xc2305c
    // 0xc23048: fcmp            d0, d1
    // 0xc2304c: b.le            #0xc23058
    // 0xc23050: fneg            d0, d1
    // 0xc23054: b               #0xc2305c
    // 0xc23058: mov             v0.16b, v1.16b
    // 0xc2305c: stur            d0, [fp, #-8]
    // 0xc23060: LoadField: r2 = r0->field_7
    //     0xc23060: ldur            w2, [x0, #7]
    // 0xc23064: DecompressPointer r2
    //     0xc23064: add             x2, x2, HEAP, lsl #32
    // 0xc23068: r0 = computePanSlop()
    //     0xc23068: bl              #0x75e234  ; [package:flutter/src/gestures/events.dart] ::computePanSlop
    // 0xc2306c: mov             v1.16b, v0.16b
    // 0xc23070: ldur            d0, [fp, #-8]
    // 0xc23074: fcmp            d0, d1
    // 0xc23078: r16 = true
    //     0xc23078: add             x16, NULL, #0x20  ; true
    // 0xc2307c: r17 = false
    //     0xc2307c: add             x17, NULL, #0x30  ; false
    // 0xc23080: csel            x0, x16, x17, gt
    // 0xc23084: LeaveFrame
    //     0xc23084: mov             SP, fp
    //     0xc23088: ldp             fp, lr, [SP], #0x10
    // 0xc2308c: ret
    //     0xc2308c: ret             
    // 0xc23090: r0 = StackOverflowSharedWithFPURegs()
    //     0xc23090: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc23094: b               #0xc23020
    // 0xc23098: r9 = _globalDistanceMoved
    //     0xc23098: add             x9, PP, #0x39, lsl #12  ; [pp+0x39c18] Field <DragGestureRecognizer._globalDistanceMoved@428099969>: late (offset: 0x70)
    //     0xc2309c: ldr             x9, [x9, #0xc18]
    // 0xc230a0: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xc230a0: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
  }
  _ considerFling(/* No info */) {
    // ** addr: 0xcdfb8c, size: 0x110
    // 0xcdfb8c: EnterFrame
    //     0xcdfb8c: stp             fp, lr, [SP, #-0x10]!
    //     0xcdfb90: mov             fp, SP
    // 0xcdfb94: AllocStack(0x18)
    //     0xcdfb94: sub             SP, SP, #0x18
    // 0xcdfb98: SetupParameters(PanGestureRecognizer this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xcdfb98: mov             x4, x1
    //     0xcdfb9c: mov             x0, x2
    //     0xcdfba0: stur            x1, [fp, #-8]
    //     0xcdfba4: stur            x2, [fp, #-0x10]
    // 0xcdfba8: CheckStackOverflow
    //     0xcdfba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcdfbac: cmp             SP, x16
    //     0xcdfbb0: b.ls            #0xcdfc88
    // 0xcdfbb4: mov             x1, x4
    // 0xcdfbb8: mov             x2, x0
    // 0xcdfbbc: r0 = isFlingGesture()
    //     0xcdfbbc: bl              #0xcdfdd4  ; [package:flutter/src/gestures/monodrag.dart] PanGestureRecognizer::isFlingGesture
    // 0xcdfbc0: tbz             w0, #4, #0xcdfbd4
    // 0xcdfbc4: r0 = Null
    //     0xcdfbc4: mov             x0, NULL
    // 0xcdfbc8: LeaveFrame
    //     0xcdfbc8: mov             SP, fp
    //     0xcdfbcc: ldp             fp, lr, [SP], #0x10
    // 0xcdfbd0: ret
    //     0xcdfbd0: ret             
    // 0xcdfbd4: ldur            x1, [fp, #-8]
    // 0xcdfbd8: ldur            x0, [fp, #-0x10]
    // 0xcdfbdc: LoadField: r2 = r0->field_7
    //     0xcdfbdc: ldur            w2, [x0, #7]
    // 0xcdfbe0: DecompressPointer r2
    //     0xcdfbe0: add             x2, x2, HEAP, lsl #32
    // 0xcdfbe4: stur            x2, [fp, #-0x18]
    // 0xcdfbe8: r0 = Velocity()
    //     0xcdfbe8: bl              #0x7d6fb8  ; AllocateVelocityStub -> Velocity (size=0xc)
    // 0xcdfbec: mov             x1, x0
    // 0xcdfbf0: ldur            x0, [fp, #-0x18]
    // 0xcdfbf4: StoreField: r1->field_7 = r0
    //     0xcdfbf4: stur            w0, [x1, #7]
    // 0xcdfbf8: ldur            x0, [fp, #-8]
    // 0xcdfbfc: LoadField: r2 = r0->field_43
    //     0xcdfbfc: ldur            w2, [x0, #0x43]
    // 0xcdfc00: DecompressPointer r2
    //     0xcdfc00: add             x2, x2, HEAP, lsl #32
    // 0xcdfc04: cmp             w2, NULL
    // 0xcdfc08: b.ne            #0xcdfc14
    // 0xcdfc0c: d0 = 50.000000
    //     0xcdfc0c: ldr             d0, [PP, #0x5ac0]  ; [pp+0x5ac0] IMM: double(50) from 0x4049000000000000
    // 0xcdfc10: b               #0xcdfc18
    // 0xcdfc14: LoadField: d0 = r2->field_7
    //     0xcdfc14: ldur            d0, [x2, #7]
    // 0xcdfc18: LoadField: r2 = r0->field_47
    //     0xcdfc18: ldur            w2, [x0, #0x47]
    // 0xcdfc1c: DecompressPointer r2
    //     0xcdfc1c: add             x2, x2, HEAP, lsl #32
    // 0xcdfc20: cmp             w2, NULL
    // 0xcdfc24: b.ne            #0xcdfc34
    // 0xcdfc28: d1 = 8000.000000
    //     0xcdfc28: add             x17, PP, #0x44, lsl #12  ; [pp+0x44a70] IMM: double(8000) from 0x40bf400000000000
    //     0xcdfc2c: ldr             d1, [x17, #0xa70]
    // 0xcdfc30: b               #0xcdfc38
    // 0xcdfc34: LoadField: d1 = r2->field_7
    //     0xcdfc34: ldur            d1, [x2, #7]
    // 0xcdfc38: r0 = clampMagnitude()
    //     0xcdfc38: bl              #0xcdfc9c  ; [package:flutter/src/gestures/velocity_tracker.dart] Velocity::clampMagnitude
    // 0xcdfc3c: mov             x1, x0
    // 0xcdfc40: ldur            x0, [fp, #-8]
    // 0xcdfc44: stur            x1, [fp, #-0x10]
    // 0xcdfc48: LoadField: r2 = r0->field_5f
    //     0xcdfc48: ldur            w2, [x0, #0x5f]
    // 0xcdfc4c: DecompressPointer r2
    //     0xcdfc4c: add             x2, x2, HEAP, lsl #32
    // 0xcdfc50: r16 = Sentinel
    //     0xcdfc50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xcdfc54: cmp             w2, w16
    // 0xcdfc58: b.eq            #0xcdfc90
    // 0xcdfc5c: LoadField: r0 = r2->field_b
    //     0xcdfc5c: ldur            w0, [x2, #0xb]
    // 0xcdfc60: DecompressPointer r0
    //     0xcdfc60: add             x0, x0, HEAP, lsl #32
    // 0xcdfc64: stur            x0, [fp, #-8]
    // 0xcdfc68: r0 = DragEndDetails()
    //     0xcdfc68: bl              #0x7d6e54  ; AllocateDragEndDetailsStub -> DragEndDetails (size=0x14)
    // 0xcdfc6c: ldur            x1, [fp, #-0x10]
    // 0xcdfc70: StoreField: r0->field_7 = r1
    //     0xcdfc70: stur            w1, [x0, #7]
    // 0xcdfc74: ldur            x1, [fp, #-8]
    // 0xcdfc78: StoreField: r0->field_f = r1
    //     0xcdfc78: stur            w1, [x0, #0xf]
    // 0xcdfc7c: LeaveFrame
    //     0xcdfc7c: mov             SP, fp
    //     0xcdfc80: ldp             fp, lr, [SP], #0x10
    // 0xcdfc84: ret
    //     0xcdfc84: ret             
    // 0xcdfc88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcdfc88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcdfc8c: b               #0xcdfbb4
    // 0xcdfc90: r9 = _lastPosition
    //     0xcdfc90: add             x9, PP, #0x39, lsl #12  ; [pp+0x39c68] Field <DragGestureRecognizer._lastPosition@428099969>: late (offset: 0x60)
    //     0xcdfc94: ldr             x9, [x9, #0xc68]
    // 0xcdfc98: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xcdfc98: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ isFlingGesture(/* No info */) {
    // ** addr: 0xcdfdd4, size: 0xf4
    // 0xcdfdd4: LoadField: r4 = r1->field_43
    //     0xcdfdd4: ldur            w4, [x1, #0x43]
    // 0xcdfdd8: DecompressPointer r4
    //     0xcdfdd8: add             x4, x4, HEAP, lsl #32
    // 0xcdfddc: cmp             w4, NULL
    // 0xcdfde0: b.ne            #0xcdfdec
    // 0xcdfde4: d0 = 50.000000
    //     0xcdfde4: ldr             d0, [PP, #0x5ac0]  ; [pp+0x5ac0] IMM: double(50) from 0x4049000000000000
    // 0xcdfde8: b               #0xcdfdf0
    // 0xcdfdec: LoadField: d0 = r4->field_7
    //     0xcdfdec: ldur            d0, [x4, #7]
    // 0xcdfdf0: LoadField: r4 = r1->field_3f
    //     0xcdfdf0: ldur            w4, [x1, #0x3f]
    // 0xcdfdf4: DecompressPointer r4
    //     0xcdfdf4: add             x4, x4, HEAP, lsl #32
    // 0xcdfdf8: cmp             w4, NULL
    // 0xcdfdfc: b.ne            #0xcdfe5c
    // 0xcdfe00: LoadField: r5 = r1->field_7
    //     0xcdfe00: ldur            w5, [x1, #7]
    // 0xcdfe04: DecompressPointer r5
    //     0xcdfe04: add             x5, x5, HEAP, lsl #32
    // 0xcdfe08: LoadField: r1 = r3->field_7
    //     0xcdfe08: ldur            x1, [x3, #7]
    // 0xcdfe0c: cmp             x1, #2
    // 0xcdfe10: b.gt            #0xcdfe2c
    // 0xcdfe14: cmp             x1, #1
    // 0xcdfe18: b.gt            #0xcdfe2c
    // 0xcdfe1c: cmp             x1, #0
    // 0xcdfe20: b.le            #0xcdfe2c
    // 0xcdfe24: d1 = 1.000000
    //     0xcdfe24: fmov            d1, #1.00000000
    // 0xcdfe28: b               #0xcdfe60
    // 0xcdfe2c: cmp             w5, NULL
    // 0xcdfe30: b.ne            #0xcdfe3c
    // 0xcdfe34: r1 = Null
    //     0xcdfe34: mov             x1, NULL
    // 0xcdfe38: b               #0xcdfe44
    // 0xcdfe3c: LoadField: r1 = r5->field_7
    //     0xcdfe3c: ldur            w1, [x5, #7]
    // 0xcdfe40: DecompressPointer r1
    //     0xcdfe40: add             x1, x1, HEAP, lsl #32
    // 0xcdfe44: cmp             w1, NULL
    // 0xcdfe48: b.ne            #0xcdfe54
    // 0xcdfe4c: d1 = 18.000000
    //     0xcdfe4c: fmov            d1, #18.00000000
    // 0xcdfe50: b               #0xcdfe60
    // 0xcdfe54: LoadField: d1 = r1->field_7
    //     0xcdfe54: ldur            d1, [x1, #7]
    // 0xcdfe58: b               #0xcdfe60
    // 0xcdfe5c: LoadField: d1 = r4->field_7
    //     0xcdfe5c: ldur            d1, [x4, #7]
    // 0xcdfe60: LoadField: r1 = r2->field_7
    //     0xcdfe60: ldur            w1, [x2, #7]
    // 0xcdfe64: DecompressPointer r1
    //     0xcdfe64: add             x1, x1, HEAP, lsl #32
    // 0xcdfe68: LoadField: d2 = r1->field_7
    //     0xcdfe68: ldur            d2, [x1, #7]
    // 0xcdfe6c: fmul            d3, d2, d2
    // 0xcdfe70: LoadField: d2 = r1->field_f
    //     0xcdfe70: ldur            d2, [x1, #0xf]
    // 0xcdfe74: fmul            d4, d2, d2
    // 0xcdfe78: fadd            d2, d3, d4
    // 0xcdfe7c: fmul            d3, d0, d0
    // 0xcdfe80: fcmp            d2, d3
    // 0xcdfe84: b.le            #0xcdfec0
    // 0xcdfe88: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xcdfe88: ldur            w1, [x2, #0x17]
    // 0xcdfe8c: DecompressPointer r1
    //     0xcdfe8c: add             x1, x1, HEAP, lsl #32
    // 0xcdfe90: LoadField: d0 = r1->field_7
    //     0xcdfe90: ldur            d0, [x1, #7]
    // 0xcdfe94: fmul            d2, d0, d0
    // 0xcdfe98: LoadField: d0 = r1->field_f
    //     0xcdfe98: ldur            d0, [x1, #0xf]
    // 0xcdfe9c: fmul            d3, d0, d0
    // 0xcdfea0: fadd            d0, d2, d3
    // 0xcdfea4: fmul            d2, d1, d1
    // 0xcdfea8: fcmp            d0, d2
    // 0xcdfeac: r16 = true
    //     0xcdfeac: add             x16, NULL, #0x20  ; true
    // 0xcdfeb0: r17 = false
    //     0xcdfeb0: add             x17, NULL, #0x30  ; false
    // 0xcdfeb4: csel            x1, x16, x17, gt
    // 0xcdfeb8: mov             x0, x1
    // 0xcdfebc: b               #0xcdfec4
    // 0xcdfec0: r0 = false
    //     0xcdfec0: add             x0, NULL, #0x30  ; false
    // 0xcdfec4: ret
    //     0xcdfec4: ret             
  }
  _ _getDeltaForDetails(/* No info */) {
    // ** addr: 0xd8d578, size: 0x8
    // 0xd8d578: mov             x0, x2
    // 0xd8d57c: ret
    //     0xd8d57c: ret             
  }
}

// class id: 7086, size: 0x14, field offset: 0x14
enum _DragDirection extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc48680, size: 0x64
    // 0xc48680: EnterFrame
    //     0xc48680: stp             fp, lr, [SP, #-0x10]!
    //     0xc48684: mov             fp, SP
    // 0xc48688: AllocStack(0x10)
    //     0xc48688: sub             SP, SP, #0x10
    // 0xc4868c: SetupParameters(_DragDirection this /* r1 => r0, fp-0x8 */)
    //     0xc4868c: mov             x0, x1
    //     0xc48690: stur            x1, [fp, #-8]
    // 0xc48694: CheckStackOverflow
    //     0xc48694: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc48698: cmp             SP, x16
    //     0xc4869c: b.ls            #0xc486dc
    // 0xc486a0: r1 = Null
    //     0xc486a0: mov             x1, NULL
    // 0xc486a4: r2 = 4
    //     0xc486a4: movz            x2, #0x4
    // 0xc486a8: r0 = AllocateArray()
    //     0xc486a8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc486ac: r16 = "_DragDirection."
    //     0xc486ac: add             x16, PP, #0x44, lsl #12  ; [pp+0x44ac0] "_DragDirection."
    //     0xc486b0: ldr             x16, [x16, #0xac0]
    // 0xc486b4: StoreField: r0->field_f = r16
    //     0xc486b4: stur            w16, [x0, #0xf]
    // 0xc486b8: ldur            x1, [fp, #-8]
    // 0xc486bc: LoadField: r2 = r1->field_f
    //     0xc486bc: ldur            w2, [x1, #0xf]
    // 0xc486c0: DecompressPointer r2
    //     0xc486c0: add             x2, x2, HEAP, lsl #32
    // 0xc486c4: StoreField: r0->field_13 = r2
    //     0xc486c4: stur            w2, [x0, #0x13]
    // 0xc486c8: str             x0, [SP]
    // 0xc486cc: r0 = _interpolate()
    //     0xc486cc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc486d0: LeaveFrame
    //     0xc486d0: mov             SP, fp
    //     0xc486d4: ldp             fp, lr, [SP], #0x10
    // 0xc486d8: ret
    //     0xc486d8: ret             
    // 0xc486dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc486dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc486e0: b               #0xc486a0
  }
}

// class id: 7087, size: 0x14, field offset: 0x14
enum _DragState extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
