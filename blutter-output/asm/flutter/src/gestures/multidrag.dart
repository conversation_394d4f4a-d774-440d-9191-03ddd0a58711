// lib: , url: package:flutter/src/gestures/multidrag.dart

// class id: 1048836, size: 0x8
class :: {
}

// class id: 3448, size: 0x28, field offset: 0x8
abstract class MultiDragPointerState extends Object {

  _ _cancel(/* No info */) {
    // ** addr: 0x7f9aa4, size: 0x6c
    // 0x7f9aa4: EnterFrame
    //     0x7f9aa4: stp             fp, lr, [SP, #-0x10]!
    //     0x7f9aa8: mov             fp, SP
    // 0x7f9aac: CheckStackOverflow
    //     0x7f9aac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f9ab0: cmp             SP, x16
    //     0x7f9ab4: b.ls            #0x7f9b08
    // 0x7f9ab8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x7f9ab8: ldur            w0, [x1, #0x17]
    // 0x7f9abc: DecompressPointer r0
    //     0x7f9abc: add             x0, x0, HEAP, lsl #32
    // 0x7f9ac0: cmp             w0, NULL
    // 0x7f9ac4: b.eq            #0x7f9af0
    // 0x7f9ac8: ArrayStore: r1[0] = rNULL  ; List_4
    //     0x7f9ac8: stur            NULL, [x1, #0x17]
    // 0x7f9acc: r1 = LoadClassIdInstr(r0)
    //     0x7f9acc: ldur            x1, [x0, #-1]
    //     0x7f9ad0: ubfx            x1, x1, #0xc, #0x14
    // 0x7f9ad4: mov             x16, x0
    // 0x7f9ad8: mov             x0, x1
    // 0x7f9adc: mov             x1, x16
    // 0x7f9ae0: r0 = GDT[cid_x0 + 0xf8a]()
    //     0x7f9ae0: add             lr, x0, #0xf8a
    //     0x7f9ae4: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9ae8: blr             lr
    // 0x7f9aec: b               #0x7f9af8
    // 0x7f9af0: StoreField: r1->field_1b = rNULL
    //     0x7f9af0: stur            NULL, [x1, #0x1b]
    // 0x7f9af4: StoreField: r1->field_1f = rNULL
    //     0x7f9af4: stur            NULL, [x1, #0x1f]
    // 0x7f9af8: r0 = Null
    //     0x7f9af8: mov             x0, NULL
    // 0x7f9afc: LeaveFrame
    //     0x7f9afc: mov             SP, fp
    //     0x7f9b00: ldp             fp, lr, [SP], #0x10
    // 0x7f9b04: ret
    //     0x7f9b04: ret             
    // 0x7f9b08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f9b08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f9b0c: b               #0x7f9ab8
  }
  _ _up(/* No info */) {
    // ** addr: 0x7f9b34, size: 0xb8
    // 0x7f9b34: EnterFrame
    //     0x7f9b34: stp             fp, lr, [SP, #-0x10]!
    //     0x7f9b38: mov             fp, SP
    // 0x7f9b3c: AllocStack(0x10)
    //     0x7f9b3c: sub             SP, SP, #0x10
    // 0x7f9b40: SetupParameters(MultiDragPointerState this /* r1 => r0, fp-0x8 */)
    //     0x7f9b40: mov             x0, x1
    //     0x7f9b44: stur            x1, [fp, #-8]
    // 0x7f9b48: CheckStackOverflow
    //     0x7f9b48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f9b4c: cmp             SP, x16
    //     0x7f9b50: b.ls            #0x7f9be0
    // 0x7f9b54: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7f9b54: ldur            w1, [x0, #0x17]
    // 0x7f9b58: DecompressPointer r1
    //     0x7f9b58: add             x1, x1, HEAP, lsl #32
    // 0x7f9b5c: cmp             w1, NULL
    // 0x7f9b60: b.eq            #0x7f9bc8
    // 0x7f9b64: LoadField: r1 = r0->field_f
    //     0x7f9b64: ldur            w1, [x0, #0xf]
    // 0x7f9b68: DecompressPointer r1
    //     0x7f9b68: add             x1, x1, HEAP, lsl #32
    // 0x7f9b6c: r0 = getVelocity()
    //     0x7f9b6c: bl              #0x7f9bec  ; [package:flutter/src/gestures/velocity_tracker.dart] VelocityTracker::getVelocity
    // 0x7f9b70: stur            x0, [fp, #-0x10]
    // 0x7f9b74: r0 = DragEndDetails()
    //     0x7f9b74: bl              #0x7d6e54  ; AllocateDragEndDetailsStub -> DragEndDetails (size=0x14)
    // 0x7f9b78: mov             x1, x0
    // 0x7f9b7c: ldur            x0, [fp, #-0x10]
    // 0x7f9b80: StoreField: r1->field_7 = r0
    //     0x7f9b80: stur            w0, [x1, #7]
    // 0x7f9b84: r0 = Instance_Offset
    //     0x7f9b84: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7f9b88: StoreField: r1->field_f = r0
    //     0x7f9b88: stur            w0, [x1, #0xf]
    // 0x7f9b8c: ldur            x0, [fp, #-8]
    // 0x7f9b90: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x7f9b90: ldur            w2, [x0, #0x17]
    // 0x7f9b94: DecompressPointer r2
    //     0x7f9b94: add             x2, x2, HEAP, lsl #32
    // 0x7f9b98: cmp             w2, NULL
    // 0x7f9b9c: b.eq            #0x7f9be8
    // 0x7f9ba0: ArrayStore: r0[0] = rNULL  ; List_4
    //     0x7f9ba0: stur            NULL, [x0, #0x17]
    // 0x7f9ba4: r0 = LoadClassIdInstr(r2)
    //     0x7f9ba4: ldur            x0, [x2, #-1]
    //     0x7f9ba8: ubfx            x0, x0, #0xc, #0x14
    // 0x7f9bac: mov             x16, x1
    // 0x7f9bb0: mov             x1, x2
    // 0x7f9bb4: mov             x2, x16
    // 0x7f9bb8: r0 = GDT[cid_x0 + 0xbfa]()
    //     0x7f9bb8: add             lr, x0, #0xbfa
    //     0x7f9bbc: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9bc0: blr             lr
    // 0x7f9bc4: b               #0x7f9bd0
    // 0x7f9bc8: StoreField: r0->field_1b = rNULL
    //     0x7f9bc8: stur            NULL, [x0, #0x1b]
    // 0x7f9bcc: StoreField: r0->field_1f = rNULL
    //     0x7f9bcc: stur            NULL, [x0, #0x1f]
    // 0x7f9bd0: r0 = Null
    //     0x7f9bd0: mov             x0, NULL
    // 0x7f9bd4: LeaveFrame
    //     0x7f9bd4: mov             SP, fp
    //     0x7f9bd8: ldp             fp, lr, [SP], #0x10
    // 0x7f9bdc: ret
    //     0x7f9bdc: ret             
    // 0x7f9be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f9be0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f9be4: b               #0x7f9b54
    // 0x7f9be8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f9be8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _move(/* No info */) {
    // ** addr: 0x7f9c88, size: 0x3cc
    // 0x7f9c88: EnterFrame
    //     0x7f9c88: stp             fp, lr, [SP, #-0x10]!
    //     0x7f9c8c: mov             fp, SP
    // 0x7f9c90: AllocStack(0x30)
    //     0x7f9c90: sub             SP, SP, #0x30
    // 0x7f9c94: SetupParameters(MultiDragPointerState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x7f9c94: mov             x3, x1
    //     0x7f9c98: stur            x1, [fp, #-8]
    //     0x7f9c9c: stur            x2, [fp, #-0x10]
    // 0x7f9ca0: CheckStackOverflow
    //     0x7f9ca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f9ca4: cmp             SP, x16
    //     0x7f9ca8: b.ls            #0x7fa040
    // 0x7f9cac: r0 = LoadClassIdInstr(r2)
    //     0x7f9cac: ldur            x0, [x2, #-1]
    //     0x7f9cb0: ubfx            x0, x0, #0xc, #0x14
    // 0x7f9cb4: mov             x1, x2
    // 0x7f9cb8: r0 = GDT[cid_x0 + 0x131e0]()
    //     0x7f9cb8: movz            x17, #0x31e0
    //     0x7f9cbc: movk            x17, #0x1, lsl #16
    //     0x7f9cc0: add             lr, x0, x17
    //     0x7f9cc4: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9cc8: blr             lr
    // 0x7f9ccc: tbz             w0, #4, #0x7f9d38
    // 0x7f9cd0: ldur            x3, [fp, #-8]
    // 0x7f9cd4: ldur            x2, [fp, #-0x10]
    // 0x7f9cd8: LoadField: r4 = r3->field_f
    //     0x7f9cd8: ldur            w4, [x3, #0xf]
    // 0x7f9cdc: DecompressPointer r4
    //     0x7f9cdc: add             x4, x4, HEAP, lsl #32
    // 0x7f9ce0: stur            x4, [fp, #-0x18]
    // 0x7f9ce4: r0 = LoadClassIdInstr(r2)
    //     0x7f9ce4: ldur            x0, [x2, #-1]
    //     0x7f9ce8: ubfx            x0, x0, #0xc, #0x14
    // 0x7f9cec: mov             x1, x2
    // 0x7f9cf0: r0 = GDT[cid_x0 + 0x130f9]()
    //     0x7f9cf0: movz            x17, #0x30f9
    //     0x7f9cf4: movk            x17, #0x1, lsl #16
    //     0x7f9cf8: add             lr, x0, x17
    //     0x7f9cfc: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9d00: blr             lr
    // 0x7f9d04: mov             x3, x0
    // 0x7f9d08: ldur            x2, [fp, #-0x10]
    // 0x7f9d0c: stur            x3, [fp, #-0x20]
    // 0x7f9d10: r0 = LoadClassIdInstr(r2)
    //     0x7f9d10: ldur            x0, [x2, #-1]
    //     0x7f9d14: ubfx            x0, x0, #0xc, #0x14
    // 0x7f9d18: mov             x1, x2
    // 0x7f9d1c: r0 = GDT[cid_x0 + -0x1]()
    //     0x7f9d1c: sub             lr, x0, #1
    //     0x7f9d20: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9d24: blr             lr
    // 0x7f9d28: ldur            x1, [fp, #-0x18]
    // 0x7f9d2c: ldur            x2, [fp, #-0x20]
    // 0x7f9d30: mov             x3, x0
    // 0x7f9d34: r0 = addPosition()
    //     0x7f9d34: bl              #0xd9016c  ; [package:flutter/src/gestures/velocity_tracker.dart] VelocityTracker::addPosition
    // 0x7f9d38: ldur            x2, [fp, #-8]
    // 0x7f9d3c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x7f9d3c: ldur            w3, [x2, #0x17]
    // 0x7f9d40: DecompressPointer r3
    //     0x7f9d40: add             x3, x3, HEAP, lsl #32
    // 0x7f9d44: stur            x3, [fp, #-0x18]
    // 0x7f9d48: cmp             w3, NULL
    // 0x7f9d4c: b.eq            #0x7f9e20
    // 0x7f9d50: ldur            x2, [fp, #-0x10]
    // 0x7f9d54: r0 = LoadClassIdInstr(r2)
    //     0x7f9d54: ldur            x0, [x2, #-1]
    //     0x7f9d58: ubfx            x0, x0, #0xc, #0x14
    // 0x7f9d5c: mov             x1, x2
    // 0x7f9d60: r0 = GDT[cid_x0 + 0x130f9]()
    //     0x7f9d60: movz            x17, #0x30f9
    //     0x7f9d64: movk            x17, #0x1, lsl #16
    //     0x7f9d68: add             lr, x0, x17
    //     0x7f9d6c: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9d70: blr             lr
    // 0x7f9d74: mov             x3, x0
    // 0x7f9d78: ldur            x2, [fp, #-0x10]
    // 0x7f9d7c: stur            x3, [fp, #-0x20]
    // 0x7f9d80: r0 = LoadClassIdInstr(r2)
    //     0x7f9d80: ldur            x0, [x2, #-1]
    //     0x7f9d84: ubfx            x0, x0, #0xc, #0x14
    // 0x7f9d88: mov             x1, x2
    // 0x7f9d8c: r0 = GDT[cid_x0 + 0x1326d]()
    //     0x7f9d8c: movz            x17, #0x326d
    //     0x7f9d90: movk            x17, #0x1, lsl #16
    //     0x7f9d94: add             lr, x0, x17
    //     0x7f9d98: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9d9c: blr             lr
    // 0x7f9da0: mov             x2, x0
    // 0x7f9da4: ldur            x3, [fp, #-0x10]
    // 0x7f9da8: stur            x2, [fp, #-0x28]
    // 0x7f9dac: r0 = LoadClassIdInstr(r3)
    //     0x7f9dac: ldur            x0, [x3, #-1]
    //     0x7f9db0: ubfx            x0, x0, #0xc, #0x14
    // 0x7f9db4: mov             x1, x3
    // 0x7f9db8: r0 = GDT[cid_x0 + -0x1]()
    //     0x7f9db8: sub             lr, x0, #1
    //     0x7f9dbc: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9dc0: blr             lr
    // 0x7f9dc4: stur            x0, [fp, #-0x30]
    // 0x7f9dc8: r0 = DragUpdateDetails()
    //     0x7f9dc8: bl              #0x759c7c  ; AllocateDragUpdateDetailsStub -> DragUpdateDetails (size=0x1c)
    // 0x7f9dcc: mov             x1, x0
    // 0x7f9dd0: ldur            x0, [fp, #-0x20]
    // 0x7f9dd4: StoreField: r1->field_7 = r0
    //     0x7f9dd4: stur            w0, [x1, #7]
    // 0x7f9dd8: ldur            x0, [fp, #-0x28]
    // 0x7f9ddc: StoreField: r1->field_b = r0
    //     0x7f9ddc: stur            w0, [x1, #0xb]
    // 0x7f9de0: ldur            x0, [fp, #-0x30]
    // 0x7f9de4: StoreField: r1->field_13 = r0
    //     0x7f9de4: stur            w0, [x1, #0x13]
    // 0x7f9de8: ArrayStore: r1[0] = r0  ; List_4
    //     0x7f9de8: stur            w0, [x1, #0x17]
    // 0x7f9dec: ldur            x0, [fp, #-0x18]
    // 0x7f9df0: r2 = LoadClassIdInstr(r0)
    //     0x7f9df0: ldur            x2, [x0, #-1]
    //     0x7f9df4: ubfx            x2, x2, #0xc, #0x14
    // 0x7f9df8: mov             x16, x1
    // 0x7f9dfc: mov             x1, x2
    // 0x7f9e00: mov             x2, x16
    // 0x7f9e04: mov             x16, x0
    // 0x7f9e08: mov             x0, x1
    // 0x7f9e0c: mov             x1, x16
    // 0x7f9e10: r0 = GDT[cid_x0 + -0xfea]()
    //     0x7f9e10: sub             lr, x0, #0xfea
    //     0x7f9e14: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9e18: blr             lr
    // 0x7f9e1c: b               #0x7fa030
    // 0x7f9e20: ldur            x3, [fp, #-0x10]
    // 0x7f9e24: LoadField: r4 = r2->field_1b
    //     0x7f9e24: ldur            w4, [x2, #0x1b]
    // 0x7f9e28: DecompressPointer r4
    //     0x7f9e28: add             x4, x4, HEAP, lsl #32
    // 0x7f9e2c: stur            x4, [fp, #-0x18]
    // 0x7f9e30: cmp             w4, NULL
    // 0x7f9e34: b.eq            #0x7fa048
    // 0x7f9e38: r0 = LoadClassIdInstr(r3)
    //     0x7f9e38: ldur            x0, [x3, #-1]
    //     0x7f9e3c: ubfx            x0, x0, #0xc, #0x14
    // 0x7f9e40: mov             x1, x3
    // 0x7f9e44: r0 = GDT[cid_x0 + 0x1326d]()
    //     0x7f9e44: movz            x17, #0x326d
    //     0x7f9e48: movk            x17, #0x1, lsl #16
    //     0x7f9e4c: add             lr, x0, x17
    //     0x7f9e50: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9e54: blr             lr
    // 0x7f9e58: ldur            x1, [fp, #-0x18]
    // 0x7f9e5c: mov             x2, x0
    // 0x7f9e60: r0 = +()
    //     0x7f9e60: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x7f9e64: ldur            x2, [fp, #-8]
    // 0x7f9e68: StoreField: r2->field_1b = r0
    //     0x7f9e68: stur            w0, [x2, #0x1b]
    //     0x7f9e6c: ldurb           w16, [x2, #-1]
    //     0x7f9e70: ldurb           w17, [x0, #-1]
    //     0x7f9e74: and             x16, x17, x16, lsr #2
    //     0x7f9e78: tst             x16, HEAP, lsr #32
    //     0x7f9e7c: b.eq            #0x7f9e84
    //     0x7f9e80: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7f9e84: ldur            x1, [fp, #-0x10]
    // 0x7f9e88: r0 = LoadClassIdInstr(r1)
    //     0x7f9e88: ldur            x0, [x1, #-1]
    //     0x7f9e8c: ubfx            x0, x0, #0xc, #0x14
    // 0x7f9e90: r0 = GDT[cid_x0 + 0x130f9]()
    //     0x7f9e90: movz            x17, #0x30f9
    //     0x7f9e94: movk            x17, #0x1, lsl #16
    //     0x7f9e98: add             lr, x0, x17
    //     0x7f9e9c: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9ea0: blr             lr
    // 0x7f9ea4: ldur            x3, [fp, #-8]
    // 0x7f9ea8: StoreField: r3->field_1f = r0
    //     0x7f9ea8: stur            w0, [x3, #0x1f]
    //     0x7f9eac: ldurb           w16, [x3, #-1]
    //     0x7f9eb0: ldurb           w17, [x0, #-1]
    //     0x7f9eb4: and             x16, x17, x16, lsr #2
    //     0x7f9eb8: tst             x16, HEAP, lsr #32
    //     0x7f9ebc: b.eq            #0x7f9ec4
    //     0x7f9ec0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7f9ec4: r0 = LoadClassIdInstr(r3)
    //     0x7f9ec4: ldur            x0, [x3, #-1]
    //     0x7f9ec8: ubfx            x0, x0, #0xc, #0x14
    // 0x7f9ecc: cmp             x0, #0xd79
    // 0x7f9ed0: b.ne            #0x7f9f90
    // 0x7f9ed4: LoadField: r0 = r3->field_27
    //     0x7f9ed4: ldur            w0, [x3, #0x27]
    // 0x7f9ed8: DecompressPointer r0
    //     0x7f9ed8: add             x0, x0, HEAP, lsl #32
    // 0x7f9edc: cmp             w0, NULL
    // 0x7f9ee0: b.eq            #0x7fa030
    // 0x7f9ee4: LoadField: r0 = r3->field_1b
    //     0x7f9ee4: ldur            w0, [x3, #0x1b]
    // 0x7f9ee8: DecompressPointer r0
    //     0x7f9ee8: add             x0, x0, HEAP, lsl #32
    // 0x7f9eec: cmp             w0, NULL
    // 0x7f9ef0: b.eq            #0x7fa04c
    // 0x7f9ef4: LoadField: d0 = r0->field_7
    //     0x7f9ef4: ldur            d0, [x0, #7]
    // 0x7f9ef8: fmul            d1, d0, d0
    // 0x7f9efc: LoadField: d0 = r0->field_f
    //     0x7f9efc: ldur            d0, [x0, #0xf]
    // 0x7f9f00: fmul            d2, d0, d0
    // 0x7f9f04: fadd            d0, d1, d2
    // 0x7f9f08: fsqrt           d1, d0
    // 0x7f9f0c: LoadField: r0 = r3->field_13
    //     0x7f9f0c: ldur            w0, [x3, #0x13]
    // 0x7f9f10: DecompressPointer r0
    //     0x7f9f10: add             x0, x0, HEAP, lsl #32
    // 0x7f9f14: LoadField: r1 = r3->field_7
    //     0x7f9f14: ldur            w1, [x3, #7]
    // 0x7f9f18: DecompressPointer r1
    //     0x7f9f18: add             x1, x1, HEAP, lsl #32
    // 0x7f9f1c: LoadField: r2 = r0->field_7
    //     0x7f9f1c: ldur            x2, [x0, #7]
    // 0x7f9f20: cmp             x2, #2
    // 0x7f9f24: b.gt            #0x7f9f40
    // 0x7f9f28: cmp             x2, #1
    // 0x7f9f2c: b.gt            #0x7f9f40
    // 0x7f9f30: cmp             x2, #0
    // 0x7f9f34: b.le            #0x7f9f40
    // 0x7f9f38: d0 = 1.000000
    //     0x7f9f38: fmov            d0, #1.00000000
    // 0x7f9f3c: b               #0x7f9f6c
    // 0x7f9f40: cmp             w1, NULL
    // 0x7f9f44: b.ne            #0x7f9f50
    // 0x7f9f48: r0 = Null
    //     0x7f9f48: mov             x0, NULL
    // 0x7f9f4c: b               #0x7f9f58
    // 0x7f9f50: LoadField: r0 = r1->field_7
    //     0x7f9f50: ldur            w0, [x1, #7]
    // 0x7f9f54: DecompressPointer r0
    //     0x7f9f54: add             x0, x0, HEAP, lsl #32
    // 0x7f9f58: cmp             w0, NULL
    // 0x7f9f5c: b.ne            #0x7f9f68
    // 0x7f9f60: d0 = 18.000000
    //     0x7f9f60: fmov            d0, #18.00000000
    // 0x7f9f64: b               #0x7f9f6c
    // 0x7f9f68: LoadField: d0 = r0->field_7
    //     0x7f9f68: ldur            d0, [x0, #7]
    // 0x7f9f6c: fcmp            d1, d0
    // 0x7f9f70: b.le            #0x7fa030
    // 0x7f9f74: mov             x1, x3
    // 0x7f9f78: r2 = Instance_GestureDisposition
    //     0x7f9f78: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0x7f9f7c: ldr             x2, [x2, #0xde8]
    // 0x7f9f80: r0 = resolve()
    //     0x7f9f80: bl              #0x7fa054  ; [package:flutter/src/gestures/multidrag.dart] MultiDragPointerState::resolve
    // 0x7f9f84: ldur            x1, [fp, #-8]
    // 0x7f9f88: r0 = stopTimer()
    //     0x7f9f88: bl              #0x717cc4  ; [dart:_http] _HttpClientConnection::stopTimer
    // 0x7f9f8c: b               #0x7fa030
    // 0x7f9f90: mov             x1, x3
    // 0x7f9f94: LoadField: r0 = r1->field_1b
    //     0x7f9f94: ldur            w0, [x1, #0x1b]
    // 0x7f9f98: DecompressPointer r0
    //     0x7f9f98: add             x0, x0, HEAP, lsl #32
    // 0x7f9f9c: cmp             w0, NULL
    // 0x7f9fa0: b.eq            #0x7fa050
    // 0x7f9fa4: LoadField: d0 = r0->field_7
    //     0x7f9fa4: ldur            d0, [x0, #7]
    // 0x7f9fa8: fmul            d1, d0, d0
    // 0x7f9fac: LoadField: d0 = r0->field_f
    //     0x7f9fac: ldur            d0, [x0, #0xf]
    // 0x7f9fb0: fmul            d2, d0, d0
    // 0x7f9fb4: fadd            d0, d1, d2
    // 0x7f9fb8: fsqrt           d1, d0
    // 0x7f9fbc: LoadField: r0 = r1->field_13
    //     0x7f9fbc: ldur            w0, [x1, #0x13]
    // 0x7f9fc0: DecompressPointer r0
    //     0x7f9fc0: add             x0, x0, HEAP, lsl #32
    // 0x7f9fc4: LoadField: r2 = r1->field_7
    //     0x7f9fc4: ldur            w2, [x1, #7]
    // 0x7f9fc8: DecompressPointer r2
    //     0x7f9fc8: add             x2, x2, HEAP, lsl #32
    // 0x7f9fcc: LoadField: r3 = r0->field_7
    //     0x7f9fcc: ldur            x3, [x0, #7]
    // 0x7f9fd0: cmp             x3, #2
    // 0x7f9fd4: b.gt            #0x7f9ff0
    // 0x7f9fd8: cmp             x3, #1
    // 0x7f9fdc: b.gt            #0x7f9ff0
    // 0x7f9fe0: cmp             x3, #0
    // 0x7f9fe4: b.le            #0x7f9ff0
    // 0x7f9fe8: d0 = 1.000000
    //     0x7f9fe8: fmov            d0, #1.00000000
    // 0x7f9fec: b               #0x7fa01c
    // 0x7f9ff0: cmp             w2, NULL
    // 0x7f9ff4: b.ne            #0x7fa000
    // 0x7f9ff8: r0 = Null
    //     0x7f9ff8: mov             x0, NULL
    // 0x7f9ffc: b               #0x7fa008
    // 0x7fa000: LoadField: r0 = r2->field_7
    //     0x7fa000: ldur            w0, [x2, #7]
    // 0x7fa004: DecompressPointer r0
    //     0x7fa004: add             x0, x0, HEAP, lsl #32
    // 0x7fa008: cmp             w0, NULL
    // 0x7fa00c: b.ne            #0x7fa018
    // 0x7fa010: d0 = 18.000000
    //     0x7fa010: fmov            d0, #18.00000000
    // 0x7fa014: b               #0x7fa01c
    // 0x7fa018: LoadField: d0 = r0->field_7
    //     0x7fa018: ldur            d0, [x0, #7]
    // 0x7fa01c: fcmp            d1, d0
    // 0x7fa020: b.le            #0x7fa030
    // 0x7fa024: r2 = Instance_GestureDisposition
    //     0x7fa024: add             x2, PP, #0x30, lsl #12  ; [pp+0x30e00] Obj!GestureDisposition@e36ee1
    //     0x7fa028: ldr             x2, [x2, #0xe00]
    // 0x7fa02c: r0 = resolve()
    //     0x7fa02c: bl              #0x7fa054  ; [package:flutter/src/gestures/multidrag.dart] MultiDragPointerState::resolve
    // 0x7fa030: r0 = Null
    //     0x7fa030: mov             x0, NULL
    // 0x7fa034: LeaveFrame
    //     0x7fa034: mov             SP, fp
    //     0x7fa038: ldp             fp, lr, [SP], #0x10
    // 0x7fa03c: ret
    //     0x7fa03c: ret             
    // 0x7fa040: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fa040: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fa044: b               #0x7f9cac
    // 0x7fa048: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fa048: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7fa04c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fa04c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7fa050: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fa050: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ resolve(/* No info */) {
    // ** addr: 0x7fa054, size: 0x48
    // 0x7fa054: EnterFrame
    //     0x7fa054: stp             fp, lr, [SP, #-0x10]!
    //     0x7fa058: mov             fp, SP
    // 0x7fa05c: CheckStackOverflow
    //     0x7fa05c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fa060: cmp             SP, x16
    //     0x7fa064: b.ls            #0x7fa090
    // 0x7fa068: LoadField: r0 = r1->field_23
    //     0x7fa068: ldur            w0, [x1, #0x23]
    // 0x7fa06c: DecompressPointer r0
    //     0x7fa06c: add             x0, x0, HEAP, lsl #32
    // 0x7fa070: cmp             w0, NULL
    // 0x7fa074: b.eq            #0x7fa098
    // 0x7fa078: mov             x1, x0
    // 0x7fa07c: r0 = resolve()
    //     0x7fa07c: bl              #0xd8d8ac  ; [package:flutter/src/gestures/arena.dart] GestureArenaEntry::resolve
    // 0x7fa080: r0 = Null
    //     0x7fa080: mov             x0, NULL
    // 0x7fa084: LeaveFrame
    //     0x7fa084: mov             SP, fp
    //     0x7fa088: ldp             fp, lr, [SP], #0x10
    // 0x7fa08c: ret
    //     0x7fa08c: ret             
    // 0x7fa090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fa090: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fa094: b               #0x7fa068
    // 0x7fa098: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fa098: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ MultiDragPointerState(/* No info */) {
    // ** addr: 0x8030a4, size: 0xf0
    // 0x8030a4: EnterFrame
    //     0x8030a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8030a8: mov             fp, SP
    // 0x8030ac: AllocStack(0x18)
    //     0x8030ac: sub             SP, SP, #0x18
    // 0x8030b0: r0 = Instance_Offset
    //     0x8030b0: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x8030b4: mov             x4, x1
    // 0x8030b8: mov             x16, x3
    // 0x8030bc: mov             x3, x2
    // 0x8030c0: mov             x2, x16
    // 0x8030c4: stur            x1, [fp, #-8]
    // 0x8030c8: mov             x1, x5
    // 0x8030cc: stur            x2, [fp, #-0x10]
    // 0x8030d0: StoreField: r4->field_1b = r0
    //     0x8030d0: stur            w0, [x4, #0x1b]
    // 0x8030d4: mov             x0, x3
    // 0x8030d8: StoreField: r4->field_b = r0
    //     0x8030d8: stur            w0, [x4, #0xb]
    //     0x8030dc: ldurb           w16, [x4, #-1]
    //     0x8030e0: ldurb           w17, [x0, #-1]
    //     0x8030e4: and             x16, x17, x16, lsr #2
    //     0x8030e8: tst             x16, HEAP, lsr #32
    //     0x8030ec: b.eq            #0x8030f4
    //     0x8030f0: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8030f4: mov             x0, x2
    // 0x8030f8: StoreField: r4->field_13 = r0
    //     0x8030f8: stur            w0, [x4, #0x13]
    //     0x8030fc: ldurb           w16, [x4, #-1]
    //     0x803100: ldurb           w17, [x0, #-1]
    //     0x803104: and             x16, x17, x16, lsr #2
    //     0x803108: tst             x16, HEAP, lsr #32
    //     0x80310c: b.eq            #0x803114
    //     0x803110: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x803114: mov             x0, x1
    // 0x803118: StoreField: r4->field_7 = r0
    //     0x803118: stur            w0, [x4, #7]
    //     0x80311c: ldurb           w16, [x4, #-1]
    //     0x803120: ldurb           w17, [x0, #-1]
    //     0x803124: and             x16, x17, x16, lsr #2
    //     0x803128: tst             x16, HEAP, lsr #32
    //     0x80312c: b.eq            #0x803134
    //     0x803130: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x803134: r0 = VelocityTracker()
    //     0x803134: bl              #0x803194  ; AllocateVelocityTrackerStub -> VelocityTracker (size=0x1c)
    // 0x803138: stur            x0, [fp, #-0x18]
    // 0x80313c: StoreField: r0->field_13 = rZR
    //     0x80313c: stur            xzr, [x0, #0x13]
    // 0x803140: r1 = <_PointAtTime?>
    //     0x803140: add             x1, PP, #0x25, lsl #12  ; [pp+0x253c8] TypeArguments: <_PointAtTime?>
    //     0x803144: ldr             x1, [x1, #0x3c8]
    // 0x803148: r2 = 40
    //     0x803148: movz            x2, #0x28
    // 0x80314c: r0 = AllocateArray()
    //     0x80314c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x803150: mov             x1, x0
    // 0x803154: ldur            x0, [fp, #-0x18]
    // 0x803158: StoreField: r0->field_f = r1
    //     0x803158: stur            w1, [x0, #0xf]
    // 0x80315c: ldur            x1, [fp, #-0x10]
    // 0x803160: StoreField: r0->field_7 = r1
    //     0x803160: stur            w1, [x0, #7]
    // 0x803164: ldur            x1, [fp, #-8]
    // 0x803168: StoreField: r1->field_f = r0
    //     0x803168: stur            w0, [x1, #0xf]
    //     0x80316c: ldurb           w16, [x1, #-1]
    //     0x803170: ldurb           w17, [x0, #-1]
    //     0x803174: and             x16, x17, x16, lsr #2
    //     0x803178: tst             x16, HEAP, lsr #32
    //     0x80317c: b.eq            #0x803184
    //     0x803180: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x803184: r0 = Null
    //     0x803184: mov             x0, NULL
    // 0x803188: LeaveFrame
    //     0x803188: mov             SP, fp
    //     0x80318c: ldp             fp, lr, [SP], #0x10
    // 0x803190: ret
    //     0x803190: ret             
  }
  _ _startDrag(/* No info */) {
    // ** addr: 0x86d450, size: 0xf4
    // 0x86d450: EnterFrame
    //     0x86d450: stp             fp, lr, [SP, #-0x10]!
    //     0x86d454: mov             fp, SP
    // 0x86d458: AllocStack(0x28)
    //     0x86d458: sub             SP, SP, #0x28
    // 0x86d45c: SetupParameters(MultiDragPointerState this /* r1 => r2, fp-0x20 */, dynamic _ /* r2 => r1, fp-0x28 */)
    //     0x86d45c: stur            x1, [fp, #-0x20]
    //     0x86d460: mov             x16, x2
    //     0x86d464: mov             x2, x1
    //     0x86d468: mov             x1, x16
    //     0x86d46c: stur            x1, [fp, #-0x28]
    // 0x86d470: CheckStackOverflow
    //     0x86d470: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86d474: cmp             SP, x16
    //     0x86d478: b.ls            #0x86d538
    // 0x86d47c: mov             x0, x1
    // 0x86d480: ArrayStore: r2[0] = r0  ; List_4
    //     0x86d480: stur            w0, [x2, #0x17]
    //     0x86d484: ldurb           w16, [x2, #-1]
    //     0x86d488: ldurb           w17, [x0, #-1]
    //     0x86d48c: and             x16, x17, x16, lsr #2
    //     0x86d490: tst             x16, HEAP, lsr #32
    //     0x86d494: b.eq            #0x86d49c
    //     0x86d498: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x86d49c: LoadField: r0 = r2->field_1f
    //     0x86d49c: ldur            w0, [x2, #0x1f]
    // 0x86d4a0: DecompressPointer r0
    //     0x86d4a0: add             x0, x0, HEAP, lsl #32
    // 0x86d4a4: stur            x0, [fp, #-0x18]
    // 0x86d4a8: LoadField: r3 = r2->field_1b
    //     0x86d4a8: ldur            w3, [x2, #0x1b]
    // 0x86d4ac: DecompressPointer r3
    //     0x86d4ac: add             x3, x3, HEAP, lsl #32
    // 0x86d4b0: stur            x3, [fp, #-0x10]
    // 0x86d4b4: cmp             w3, NULL
    // 0x86d4b8: b.eq            #0x86d540
    // 0x86d4bc: LoadField: r4 = r2->field_b
    //     0x86d4bc: ldur            w4, [x2, #0xb]
    // 0x86d4c0: DecompressPointer r4
    //     0x86d4c0: add             x4, x4, HEAP, lsl #32
    // 0x86d4c4: stur            x4, [fp, #-8]
    // 0x86d4c8: r0 = DragUpdateDetails()
    //     0x86d4c8: bl              #0x759c7c  ; AllocateDragUpdateDetailsStub -> DragUpdateDetails (size=0x1c)
    // 0x86d4cc: mov             x1, x0
    // 0x86d4d0: ldur            x0, [fp, #-0x18]
    // 0x86d4d4: StoreField: r1->field_7 = r0
    //     0x86d4d4: stur            w0, [x1, #7]
    // 0x86d4d8: ldur            x0, [fp, #-0x10]
    // 0x86d4dc: StoreField: r1->field_b = r0
    //     0x86d4dc: stur            w0, [x1, #0xb]
    // 0x86d4e0: ldur            x0, [fp, #-8]
    // 0x86d4e4: StoreField: r1->field_13 = r0
    //     0x86d4e4: stur            w0, [x1, #0x13]
    // 0x86d4e8: ArrayStore: r1[0] = r0  ; List_4
    //     0x86d4e8: stur            w0, [x1, #0x17]
    // 0x86d4ec: ldur            x0, [fp, #-0x20]
    // 0x86d4f0: StoreField: r0->field_1b = rNULL
    //     0x86d4f0: stur            NULL, [x0, #0x1b]
    // 0x86d4f4: StoreField: r0->field_1f = rNULL
    //     0x86d4f4: stur            NULL, [x0, #0x1f]
    // 0x86d4f8: ldur            x0, [fp, #-0x28]
    // 0x86d4fc: r2 = LoadClassIdInstr(r0)
    //     0x86d4fc: ldur            x2, [x0, #-1]
    //     0x86d500: ubfx            x2, x2, #0xc, #0x14
    // 0x86d504: mov             x16, x1
    // 0x86d508: mov             x1, x2
    // 0x86d50c: mov             x2, x16
    // 0x86d510: mov             x16, x0
    // 0x86d514: mov             x0, x1
    // 0x86d518: mov             x1, x16
    // 0x86d51c: r0 = GDT[cid_x0 + -0xfea]()
    //     0x86d51c: sub             lr, x0, #0xfea
    //     0x86d520: ldr             lr, [x21, lr, lsl #3]
    //     0x86d524: blr             lr
    // 0x86d528: r0 = Null
    //     0x86d528: mov             x0, NULL
    // 0x86d52c: LeaveFrame
    //     0x86d52c: mov             SP, fp
    //     0x86d530: ldp             fp, lr, [SP], #0x10
    // 0x86d534: ret
    //     0x86d534: ret             
    // 0x86d538: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86d538: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86d53c: b               #0x86d47c
    // 0x86d540: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x86d540: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ rejected(/* No info */) {
    // ** addr: 0xccef68, size: 0x14
    // 0xccef68: StoreField: r1->field_1b = rNULL
    //     0xccef68: stur            NULL, [x1, #0x1b]
    // 0xccef6c: StoreField: r1->field_1f = rNULL
    //     0xccef6c: stur            NULL, [x1, #0x1f]
    // 0xccef70: StoreField: r1->field_23 = rNULL
    //     0xccef70: stur            NULL, [x1, #0x23]
    // 0xccef74: r0 = Null
    //     0xccef74: mov             x0, NULL
    // 0xccef78: ret
    //     0xccef78: ret             
  }
  _ dispose(/* No info */) {
    // ** addr: 0xd8e9f8, size: 0x64
    // 0xd8e9f8: EnterFrame
    //     0xd8e9f8: stp             fp, lr, [SP, #-0x10]!
    //     0xd8e9fc: mov             fp, SP
    // 0xd8ea00: AllocStack(0x8)
    //     0xd8ea00: sub             SP, SP, #8
    // 0xd8ea04: SetupParameters(MultiDragPointerState this /* r1 => r0, fp-0x8 */)
    //     0xd8ea04: mov             x0, x1
    //     0xd8ea08: stur            x1, [fp, #-8]
    // 0xd8ea0c: CheckStackOverflow
    //     0xd8ea0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8ea10: cmp             SP, x16
    //     0xd8ea14: b.ls            #0xd8ea54
    // 0xd8ea18: LoadField: r1 = r0->field_23
    //     0xd8ea18: ldur            w1, [x0, #0x23]
    // 0xd8ea1c: DecompressPointer r1
    //     0xd8ea1c: add             x1, x1, HEAP, lsl #32
    // 0xd8ea20: cmp             w1, NULL
    // 0xd8ea24: b.ne            #0xd8ea30
    // 0xd8ea28: mov             x1, x0
    // 0xd8ea2c: b               #0xd8ea40
    // 0xd8ea30: r2 = Instance_GestureDisposition
    //     0xd8ea30: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0xd8ea34: ldr             x2, [x2, #0xde8]
    // 0xd8ea38: r0 = resolve()
    //     0xd8ea38: bl              #0xd8d8ac  ; [package:flutter/src/gestures/arena.dart] GestureArenaEntry::resolve
    // 0xd8ea3c: ldur            x1, [fp, #-8]
    // 0xd8ea40: StoreField: r1->field_23 = rNULL
    //     0xd8ea40: stur            NULL, [x1, #0x23]
    // 0xd8ea44: r0 = Null
    //     0xd8ea44: mov             x0, NULL
    // 0xd8ea48: LeaveFrame
    //     0xd8ea48: mov             SP, fp
    //     0xd8ea4c: ldp             fp, lr, [SP], #0x10
    // 0xd8ea50: ret
    //     0xd8ea50: ret             
    // 0xd8ea54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8ea54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8ea58: b               #0xd8ea18
  }
}

// class id: 3449, size: 0x30, field offset: 0x28
class _DelayedPointerState extends MultiDragPointerState {

  _ _DelayedPointerState(/* No info */) {
    // ** addr: 0x8031d8, size: 0x80
    // 0x8031d8: EnterFrame
    //     0x8031d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8031dc: mov             fp, SP
    // 0x8031e0: AllocStack(0x8)
    //     0x8031e0: sub             SP, SP, #8
    // 0x8031e4: SetupParameters(_DelayedPointerState this /* r1 => r0, fp-0x8 */)
    //     0x8031e4: mov             x0, x1
    //     0x8031e8: stur            x1, [fp, #-8]
    // 0x8031ec: CheckStackOverflow
    //     0x8031ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8031f0: cmp             SP, x16
    //     0x8031f4: b.ls            #0x803250
    // 0x8031f8: mov             x1, x0
    // 0x8031fc: r0 = MultiDragPointerState()
    //     0x8031fc: bl              #0x8030a4  ; [package:flutter/src/gestures/multidrag.dart] MultiDragPointerState::MultiDragPointerState
    // 0x803200: ldur            x2, [fp, #-8]
    // 0x803204: r1 = Function '_delayPassed@429470698':.
    //     0x803204: add             x1, PP, #0x55, lsl #12  ; [pp+0x55298] AnonymousClosure: (0x803258), in [package:flutter/src/gestures/multidrag.dart] _DelayedPointerState::_delayPassed (0x803290)
    //     0x803208: ldr             x1, [x1, #0x298]
    // 0x80320c: r0 = AllocateClosure()
    //     0x80320c: bl              #0xec1630  ; AllocateClosureStub
    // 0x803210: mov             x3, x0
    // 0x803214: r1 = Null
    //     0x803214: mov             x1, NULL
    // 0x803218: r2 = Instance_Duration
    //     0x803218: ldr             x2, [PP, #0x4fa0]  ; [pp+0x4fa0] Obj!Duration@e3a0b1
    // 0x80321c: r0 = Timer()
    //     0x80321c: bl              #0x5f9778  ; [dart:async] Timer::Timer
    // 0x803220: ldur            x1, [fp, #-8]
    // 0x803224: StoreField: r1->field_27 = r0
    //     0x803224: stur            w0, [x1, #0x27]
    //     0x803228: ldurb           w16, [x1, #-1]
    //     0x80322c: ldurb           w17, [x0, #-1]
    //     0x803230: and             x16, x17, x16, lsr #2
    //     0x803234: tst             x16, HEAP, lsr #32
    //     0x803238: b.eq            #0x803240
    //     0x80323c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x803240: r0 = Null
    //     0x803240: mov             x0, NULL
    // 0x803244: LeaveFrame
    //     0x803244: mov             SP, fp
    //     0x803248: ldp             fp, lr, [SP], #0x10
    // 0x80324c: ret
    //     0x80324c: ret             
    // 0x803250: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x803250: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x803254: b               #0x8031f8
  }
  [closure] void _delayPassed(dynamic) {
    // ** addr: 0x803258, size: 0x38
    // 0x803258: EnterFrame
    //     0x803258: stp             fp, lr, [SP, #-0x10]!
    //     0x80325c: mov             fp, SP
    // 0x803260: ldr             x0, [fp, #0x10]
    // 0x803264: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x803264: ldur            w1, [x0, #0x17]
    // 0x803268: DecompressPointer r1
    //     0x803268: add             x1, x1, HEAP, lsl #32
    // 0x80326c: CheckStackOverflow
    //     0x80326c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x803270: cmp             SP, x16
    //     0x803274: b.ls            #0x803288
    // 0x803278: r0 = _delayPassed()
    //     0x803278: bl              #0x803290  ; [package:flutter/src/gestures/multidrag.dart] _DelayedPointerState::_delayPassed
    // 0x80327c: LeaveFrame
    //     0x80327c: mov             SP, fp
    //     0x803280: ldp             fp, lr, [SP], #0x10
    // 0x803284: ret
    //     0x803284: ret             
    // 0x803288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x803288: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80328c: b               #0x803278
  }
  _ _delayPassed(/* No info */) {
    // ** addr: 0x803290, size: 0x78
    // 0x803290: EnterFrame
    //     0x803290: stp             fp, lr, [SP, #-0x10]!
    //     0x803294: mov             fp, SP
    // 0x803298: AllocStack(0x18)
    //     0x803298: sub             SP, SP, #0x18
    // 0x80329c: SetupParameters(_DelayedPointerState this /* r1 => r1, fp-0x8 */)
    //     0x80329c: stur            x1, [fp, #-8]
    // 0x8032a0: CheckStackOverflow
    //     0x8032a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8032a4: cmp             SP, x16
    //     0x8032a8: b.ls            #0x803300
    // 0x8032ac: StoreField: r1->field_27 = rNULL
    //     0x8032ac: stur            NULL, [x1, #0x27]
    // 0x8032b0: LoadField: r0 = r1->field_2b
    //     0x8032b0: ldur            w0, [x1, #0x2b]
    // 0x8032b4: DecompressPointer r0
    //     0x8032b4: add             x0, x0, HEAP, lsl #32
    // 0x8032b8: cmp             w0, NULL
    // 0x8032bc: b.eq            #0x8032e4
    // 0x8032c0: LoadField: r2 = r1->field_b
    //     0x8032c0: ldur            w2, [x1, #0xb]
    // 0x8032c4: DecompressPointer r2
    //     0x8032c4: add             x2, x2, HEAP, lsl #32
    // 0x8032c8: stp             x2, x0, [SP]
    // 0x8032cc: ClosureCall
    //     0x8032cc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x8032d0: ldur            x2, [x0, #0x1f]
    //     0x8032d4: blr             x2
    // 0x8032d8: ldur            x1, [fp, #-8]
    // 0x8032dc: StoreField: r1->field_2b = rNULL
    //     0x8032dc: stur            NULL, [x1, #0x2b]
    // 0x8032e0: b               #0x8032f0
    // 0x8032e4: r2 = Instance_GestureDisposition
    //     0x8032e4: add             x2, PP, #0x30, lsl #12  ; [pp+0x30e00] Obj!GestureDisposition@e36ee1
    //     0x8032e8: ldr             x2, [x2, #0xe00]
    // 0x8032ec: r0 = resolve()
    //     0x8032ec: bl              #0x7fa054  ; [package:flutter/src/gestures/multidrag.dart] MultiDragPointerState::resolve
    // 0x8032f0: r0 = Null
    //     0x8032f0: mov             x0, NULL
    // 0x8032f4: LeaveFrame
    //     0x8032f4: mov             SP, fp
    //     0x8032f8: ldp             fp, lr, [SP], #0x10
    // 0x8032fc: ret
    //     0x8032fc: ret             
    // 0x803300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x803300: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x803304: b               #0x8032ac
  }
  _ accepted(/* No info */) {
    // ** addr: 0xd8e97c, size: 0x7c
    // 0xd8e97c: EnterFrame
    //     0xd8e97c: stp             fp, lr, [SP, #-0x10]!
    //     0xd8e980: mov             fp, SP
    // 0xd8e984: AllocStack(0x10)
    //     0xd8e984: sub             SP, SP, #0x10
    // 0xd8e988: SetupParameters(dynamic _ /* r2 => r0 */)
    //     0xd8e988: mov             x0, x2
    // 0xd8e98c: CheckStackOverflow
    //     0xd8e98c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8e990: cmp             SP, x16
    //     0xd8e994: b.ls            #0xd8e9f0
    // 0xd8e998: LoadField: r2 = r1->field_27
    //     0xd8e998: ldur            w2, [x1, #0x27]
    // 0xd8e99c: DecompressPointer r2
    //     0xd8e99c: add             x2, x2, HEAP, lsl #32
    // 0xd8e9a0: cmp             w2, NULL
    // 0xd8e9a4: b.ne            #0xd8e9c4
    // 0xd8e9a8: LoadField: r2 = r1->field_b
    //     0xd8e9a8: ldur            w2, [x1, #0xb]
    // 0xd8e9ac: DecompressPointer r2
    //     0xd8e9ac: add             x2, x2, HEAP, lsl #32
    // 0xd8e9b0: stp             x2, x0, [SP]
    // 0xd8e9b4: ClosureCall
    //     0xd8e9b4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xd8e9b8: ldur            x2, [x0, #0x1f]
    //     0xd8e9bc: blr             x2
    // 0xd8e9c0: b               #0xd8e9e0
    // 0xd8e9c4: StoreField: r1->field_2b = r0
    //     0xd8e9c4: stur            w0, [x1, #0x2b]
    //     0xd8e9c8: ldurb           w16, [x1, #-1]
    //     0xd8e9cc: ldurb           w17, [x0, #-1]
    //     0xd8e9d0: and             x16, x17, x16, lsr #2
    //     0xd8e9d4: tst             x16, HEAP, lsr #32
    //     0xd8e9d8: b.eq            #0xd8e9e0
    //     0xd8e9dc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xd8e9e0: r0 = Null
    //     0xd8e9e0: mov             x0, NULL
    // 0xd8e9e4: LeaveFrame
    //     0xd8e9e4: mov             SP, fp
    //     0xd8e9e8: ldp             fp, lr, [SP], #0x10
    // 0xd8e9ec: ret
    //     0xd8e9ec: ret             
    // 0xd8e9f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8e9f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8e9f4: b               #0xd8e998
  }
  _ dispose(/* No info */) {
    // ** addr: 0xd8ea5c, size: 0x48
    // 0xd8ea5c: EnterFrame
    //     0xd8ea5c: stp             fp, lr, [SP, #-0x10]!
    //     0xd8ea60: mov             fp, SP
    // 0xd8ea64: AllocStack(0x8)
    //     0xd8ea64: sub             SP, SP, #8
    // 0xd8ea68: SetupParameters(_DelayedPointerState this /* r1 => r0, fp-0x8 */)
    //     0xd8ea68: mov             x0, x1
    //     0xd8ea6c: stur            x1, [fp, #-8]
    // 0xd8ea70: CheckStackOverflow
    //     0xd8ea70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8ea74: cmp             SP, x16
    //     0xd8ea78: b.ls            #0xd8ea9c
    // 0xd8ea7c: mov             x1, x0
    // 0xd8ea80: r0 = stopTimer()
    //     0xd8ea80: bl              #0x717cc4  ; [dart:_http] _HttpClientConnection::stopTimer
    // 0xd8ea84: ldur            x1, [fp, #-8]
    // 0xd8ea88: r0 = dispose()
    //     0xd8ea88: bl              #0xd8e9f8  ; [package:flutter/src/gestures/multidrag.dart] MultiDragPointerState::dispose
    // 0xd8ea8c: r0 = Null
    //     0xd8ea8c: mov             x0, NULL
    // 0xd8ea90: LeaveFrame
    //     0xd8ea90: mov             SP, fp
    //     0xd8ea94: ldp             fp, lr, [SP], #0x10
    // 0xd8ea98: ret
    //     0xd8ea98: ret             
    // 0xd8ea9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8ea9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8eaa0: b               #0xd8ea7c
  }
}

// class id: 3450, size: 0x28, field offset: 0x28
class _ImmediatePointerState extends MultiDragPointerState {

  _ accepted(/* No info */) {
    // ** addr: 0xd8e930, size: 0x4c
    // 0xd8e930: EnterFrame
    //     0xd8e930: stp             fp, lr, [SP, #-0x10]!
    //     0xd8e934: mov             fp, SP
    // 0xd8e938: AllocStack(0x10)
    //     0xd8e938: sub             SP, SP, #0x10
    // 0xd8e93c: SetupParameters(dynamic _ /* r2 => r0 */)
    //     0xd8e93c: mov             x0, x2
    // 0xd8e940: CheckStackOverflow
    //     0xd8e940: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8e944: cmp             SP, x16
    //     0xd8e948: b.ls            #0xd8e974
    // 0xd8e94c: LoadField: r2 = r1->field_b
    //     0xd8e94c: ldur            w2, [x1, #0xb]
    // 0xd8e950: DecompressPointer r2
    //     0xd8e950: add             x2, x2, HEAP, lsl #32
    // 0xd8e954: stp             x2, x0, [SP]
    // 0xd8e958: ClosureCall
    //     0xd8e958: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xd8e95c: ldur            x2, [x0, #0x1f]
    //     0xd8e960: blr             x2
    // 0xd8e964: r0 = Null
    //     0xd8e964: mov             x0, NULL
    // 0xd8e968: LeaveFrame
    //     0xd8e968: mov             SP, fp
    //     0xd8e96c: ldp             fp, lr, [SP], #0x10
    // 0xd8e970: ret
    //     0xd8e970: ret             
    // 0xd8e974: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8e974: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8e978: b               #0xd8e94c
  }
}

// class id: 3528, size: 0x20, field offset: 0x18
abstract class MultiDragGestureRecognizer extends GestureRecognizer {

  _ dispose(/* No info */) {
    // ** addr: 0x7f95b4, size: 0x144
    // 0x7f95b4: EnterFrame
    //     0x7f95b4: stp             fp, lr, [SP, #-0x10]!
    //     0x7f95b8: mov             fp, SP
    // 0x7f95bc: AllocStack(0x20)
    //     0x7f95bc: sub             SP, SP, #0x20
    // 0x7f95c0: SetupParameters(MultiDragGestureRecognizer this /* r1 => r0, fp-0x18 */)
    //     0x7f95c0: mov             x0, x1
    //     0x7f95c4: stur            x1, [fp, #-0x18]
    // 0x7f95c8: CheckStackOverflow
    //     0x7f95c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f95cc: cmp             SP, x16
    //     0x7f95d0: b.ls            #0x7f96e0
    // 0x7f95d4: LoadField: r2 = r0->field_1b
    //     0x7f95d4: ldur            w2, [x0, #0x1b]
    // 0x7f95d8: DecompressPointer r2
    //     0x7f95d8: add             x2, x2, HEAP, lsl #32
    // 0x7f95dc: stur            x2, [fp, #-0x10]
    // 0x7f95e0: cmp             w2, NULL
    // 0x7f95e4: b.eq            #0x7f96e8
    // 0x7f95e8: LoadField: r3 = r2->field_7
    //     0x7f95e8: ldur            w3, [x2, #7]
    // 0x7f95ec: DecompressPointer r3
    //     0x7f95ec: add             x3, x3, HEAP, lsl #32
    // 0x7f95f0: mov             x1, x3
    // 0x7f95f4: stur            x3, [fp, #-8]
    // 0x7f95f8: r0 = _CompactIterable()
    //     0x7f95f8: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x7f95fc: mov             x1, x0
    // 0x7f9600: ldur            x0, [fp, #-0x10]
    // 0x7f9604: StoreField: r1->field_b = r0
    //     0x7f9604: stur            w0, [x1, #0xb]
    // 0x7f9608: r0 = -2
    //     0x7f9608: orr             x0, xzr, #0xfffffffffffffffe
    // 0x7f960c: StoreField: r1->field_f = r0
    //     0x7f960c: stur            x0, [x1, #0xf]
    // 0x7f9610: r0 = 2
    //     0x7f9610: movz            x0, #0x2
    // 0x7f9614: ArrayStore: r1[0] = r0  ; List_8
    //     0x7f9614: stur            x0, [x1, #0x17]
    // 0x7f9618: mov             x2, x1
    // 0x7f961c: ldur            x1, [fp, #-8]
    // 0x7f9620: r0 = _GrowableList.of()
    //     0x7f9620: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x7f9624: mov             x3, x0
    // 0x7f9628: stur            x3, [fp, #-0x10]
    // 0x7f962c: LoadField: r4 = r3->field_b
    //     0x7f962c: ldur            w4, [x3, #0xb]
    // 0x7f9630: stur            x4, [fp, #-8]
    // 0x7f9634: r0 = LoadInt32Instr(r4)
    //     0x7f9634: sbfx            x0, x4, #1, #0x1f
    // 0x7f9638: r5 = 0
    //     0x7f9638: movz            x5, #0
    // 0x7f963c: stur            x5, [fp, #-0x20]
    // 0x7f9640: CheckStackOverflow
    //     0x7f9640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f9644: cmp             SP, x16
    //     0x7f9648: b.ls            #0x7f96ec
    // 0x7f964c: cmp             x5, x0
    // 0x7f9650: b.ge            #0x7f96ac
    // 0x7f9654: mov             x1, x5
    // 0x7f9658: cmp             x1, x0
    // 0x7f965c: b.hs            #0x7f96f4
    // 0x7f9660: LoadField: r0 = r3->field_f
    //     0x7f9660: ldur            w0, [x3, #0xf]
    // 0x7f9664: DecompressPointer r0
    //     0x7f9664: add             x0, x0, HEAP, lsl #32
    // 0x7f9668: ArrayLoad: r2 = r0[r5]  ; Unknown_4
    //     0x7f9668: add             x16, x0, x5, lsl #2
    //     0x7f966c: ldur            w2, [x16, #0xf]
    // 0x7f9670: DecompressPointer r2
    //     0x7f9670: add             x2, x2, HEAP, lsl #32
    // 0x7f9674: ldur            x1, [fp, #-0x18]
    // 0x7f9678: r0 = _removeState()
    //     0x7f9678: bl              #0x7f96f8  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::_removeState
    // 0x7f967c: ldur            x1, [fp, #-0x10]
    // 0x7f9680: LoadField: r0 = r1->field_b
    //     0x7f9680: ldur            w0, [x1, #0xb]
    // 0x7f9684: ldur            x2, [fp, #-8]
    // 0x7f9688: cmp             w0, w2
    // 0x7f968c: b.ne            #0x7f96c4
    // 0x7f9690: ldur            x3, [fp, #-0x20]
    // 0x7f9694: add             x5, x3, #1
    // 0x7f9698: r3 = LoadInt32Instr(r0)
    //     0x7f9698: sbfx            x3, x0, #1, #0x1f
    // 0x7f969c: mov             x0, x3
    // 0x7f96a0: mov             x3, x1
    // 0x7f96a4: mov             x4, x2
    // 0x7f96a8: b               #0x7f963c
    // 0x7f96ac: ldur            x0, [fp, #-0x18]
    // 0x7f96b0: StoreField: r0->field_1b = rNULL
    //     0x7f96b0: stur            NULL, [x0, #0x1b]
    // 0x7f96b4: r0 = Null
    //     0x7f96b4: mov             x0, NULL
    // 0x7f96b8: LeaveFrame
    //     0x7f96b8: mov             SP, fp
    //     0x7f96bc: ldp             fp, lr, [SP], #0x10
    // 0x7f96c0: ret
    //     0x7f96c0: ret             
    // 0x7f96c4: r0 = ConcurrentModificationError()
    //     0x7f96c4: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x7f96c8: mov             x1, x0
    // 0x7f96cc: ldur            x0, [fp, #-0x10]
    // 0x7f96d0: StoreField: r1->field_b = r0
    //     0x7f96d0: stur            w0, [x1, #0xb]
    // 0x7f96d4: mov             x0, x1
    // 0x7f96d8: r0 = Throw()
    //     0x7f96d8: bl              #0xec04b8  ; ThrowStub
    // 0x7f96dc: brk             #0
    // 0x7f96e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f96e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f96e4: b               #0x7f95d4
    // 0x7f96e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f96e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7f96ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f96ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f96f0: b               #0x7f964c
    // 0x7f96f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7f96f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _removeState(/* No info */) {
    // ** addr: 0x7f96f8, size: 0xfc
    // 0x7f96f8: EnterFrame
    //     0x7f96f8: stp             fp, lr, [SP, #-0x10]!
    //     0x7f96fc: mov             fp, SP
    // 0x7f9700: AllocStack(0x20)
    //     0x7f9700: sub             SP, SP, #0x20
    // 0x7f9704: SetupParameters(MultiDragGestureRecognizer this /* r1 => r3, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */)
    //     0x7f9704: mov             x3, x1
    //     0x7f9708: mov             x0, x2
    //     0x7f970c: stur            x1, [fp, #-0x18]
    //     0x7f9710: stur            x2, [fp, #-0x20]
    // 0x7f9714: CheckStackOverflow
    //     0x7f9714: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f9718: cmp             SP, x16
    //     0x7f971c: b.ls            #0x7f97e0
    // 0x7f9720: LoadField: r1 = r3->field_1b
    //     0x7f9720: ldur            w1, [x3, #0x1b]
    // 0x7f9724: DecompressPointer r1
    //     0x7f9724: add             x1, x1, HEAP, lsl #32
    // 0x7f9728: cmp             w1, NULL
    // 0x7f972c: b.ne            #0x7f9740
    // 0x7f9730: r0 = Null
    //     0x7f9730: mov             x0, NULL
    // 0x7f9734: LeaveFrame
    //     0x7f9734: mov             SP, fp
    //     0x7f9738: ldp             fp, lr, [SP], #0x10
    // 0x7f973c: ret
    //     0x7f973c: ret             
    // 0x7f9740: r1 = LoadStaticField(0x968)
    //     0x7f9740: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x7f9744: ldr             x1, [x1, #0x12d0]
    // 0x7f9748: cmp             w1, NULL
    // 0x7f974c: b.eq            #0x7f97e8
    // 0x7f9750: LoadField: r4 = r1->field_13
    //     0x7f9750: ldur            w4, [x1, #0x13]
    // 0x7f9754: DecompressPointer r4
    //     0x7f9754: add             x4, x4, HEAP, lsl #32
    // 0x7f9758: stur            x4, [fp, #-0x10]
    // 0x7f975c: r5 = LoadInt32Instr(r0)
    //     0x7f975c: sbfx            x5, x0, #1, #0x1f
    //     0x7f9760: tbz             w0, #0, #0x7f9768
    //     0x7f9764: ldur            x5, [x0, #7]
    // 0x7f9768: mov             x2, x3
    // 0x7f976c: stur            x5, [fp, #-8]
    // 0x7f9770: r1 = Function '_handleEvent@429470698':.
    //     0x7f9770: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fe40] AnonymousClosure: (0x7f97f4), in [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::_handleEvent (0x7f9830)
    //     0x7f9774: ldr             x1, [x1, #0xe40]
    // 0x7f9778: r0 = AllocateClosure()
    //     0x7f9778: bl              #0xec1630  ; AllocateClosureStub
    // 0x7f977c: ldur            x1, [fp, #-0x10]
    // 0x7f9780: ldur            x2, [fp, #-8]
    // 0x7f9784: mov             x3, x0
    // 0x7f9788: r0 = removeRoute()
    //     0x7f9788: bl              #0x758954  ; [package:flutter/src/gestures/pointer_router.dart] PointerRouter::removeRoute
    // 0x7f978c: ldur            x0, [fp, #-0x18]
    // 0x7f9790: LoadField: r1 = r0->field_1b
    //     0x7f9790: ldur            w1, [x0, #0x1b]
    // 0x7f9794: DecompressPointer r1
    //     0x7f9794: add             x1, x1, HEAP, lsl #32
    // 0x7f9798: cmp             w1, NULL
    // 0x7f979c: b.eq            #0x7f97ec
    // 0x7f97a0: ldur            x2, [fp, #-0x20]
    // 0x7f97a4: r0 = remove()
    //     0x7f97a4: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x7f97a8: cmp             w0, NULL
    // 0x7f97ac: b.eq            #0x7f97f0
    // 0x7f97b0: r1 = LoadClassIdInstr(r0)
    //     0x7f97b0: ldur            x1, [x0, #-1]
    //     0x7f97b4: ubfx            x1, x1, #0xc, #0x14
    // 0x7f97b8: mov             x16, x0
    // 0x7f97bc: mov             x0, x1
    // 0x7f97c0: mov             x1, x16
    // 0x7f97c4: r0 = GDT[cid_x0 + -0xff9]()
    //     0x7f97c4: sub             lr, x0, #0xff9
    //     0x7f97c8: ldr             lr, [x21, lr, lsl #3]
    //     0x7f97cc: blr             lr
    // 0x7f97d0: r0 = Null
    //     0x7f97d0: mov             x0, NULL
    // 0x7f97d4: LeaveFrame
    //     0x7f97d4: mov             SP, fp
    //     0x7f97d8: ldp             fp, lr, [SP], #0x10
    // 0x7f97dc: ret
    //     0x7f97dc: ret             
    // 0x7f97e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f97e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f97e4: b               #0x7f9720
    // 0x7f97e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f97e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7f97ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f97ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7f97f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f97f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _handleEvent(dynamic, PointerEvent) {
    // ** addr: 0x7f97f4, size: 0x3c
    // 0x7f97f4: EnterFrame
    //     0x7f97f4: stp             fp, lr, [SP, #-0x10]!
    //     0x7f97f8: mov             fp, SP
    // 0x7f97fc: ldr             x0, [fp, #0x18]
    // 0x7f9800: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7f9800: ldur            w1, [x0, #0x17]
    // 0x7f9804: DecompressPointer r1
    //     0x7f9804: add             x1, x1, HEAP, lsl #32
    // 0x7f9808: CheckStackOverflow
    //     0x7f9808: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f980c: cmp             SP, x16
    //     0x7f9810: b.ls            #0x7f9828
    // 0x7f9814: ldr             x2, [fp, #0x10]
    // 0x7f9818: r0 = _handleEvent()
    //     0x7f9818: bl              #0x7f9830  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::_handleEvent
    // 0x7f981c: LeaveFrame
    //     0x7f981c: mov             SP, fp
    //     0x7f9820: ldp             fp, lr, [SP], #0x10
    // 0x7f9824: ret
    //     0x7f9824: ret             
    // 0x7f9828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f9828: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f982c: b               #0x7f9814
  }
  _ _handleEvent(/* No info */) {
    // ** addr: 0x7f9830, size: 0x274
    // 0x7f9830: EnterFrame
    //     0x7f9830: stp             fp, lr, [SP, #-0x10]!
    //     0x7f9834: mov             fp, SP
    // 0x7f9838: AllocStack(0x18)
    //     0x7f9838: sub             SP, SP, #0x18
    // 0x7f983c: SetupParameters(MultiDragGestureRecognizer this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x7f983c: mov             x3, x1
    //     0x7f9840: stur            x1, [fp, #-0x10]
    //     0x7f9844: stur            x2, [fp, #-0x18]
    // 0x7f9848: CheckStackOverflow
    //     0x7f9848: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f984c: cmp             SP, x16
    //     0x7f9850: b.ls            #0x7f9a94
    // 0x7f9854: LoadField: r4 = r3->field_1b
    //     0x7f9854: ldur            w4, [x3, #0x1b]
    // 0x7f9858: DecompressPointer r4
    //     0x7f9858: add             x4, x4, HEAP, lsl #32
    // 0x7f985c: stur            x4, [fp, #-8]
    // 0x7f9860: cmp             w4, NULL
    // 0x7f9864: b.eq            #0x7f9a9c
    // 0x7f9868: r0 = LoadClassIdInstr(r2)
    //     0x7f9868: ldur            x0, [x2, #-1]
    //     0x7f986c: ubfx            x0, x0, #0xc, #0x14
    // 0x7f9870: mov             x1, x2
    // 0x7f9874: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7f9874: sub             lr, x0, #1, lsl #12
    //     0x7f9878: ldr             lr, [x21, lr, lsl #3]
    //     0x7f987c: blr             lr
    // 0x7f9880: mov             x2, x0
    // 0x7f9884: r0 = BoxInt64Instr(r2)
    //     0x7f9884: sbfiz           x0, x2, #1, #0x1f
    //     0x7f9888: cmp             x2, x0, asr #1
    //     0x7f988c: b.eq            #0x7f9898
    //     0x7f9890: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7f9894: stur            x2, [x0, #7]
    // 0x7f9898: ldur            x1, [fp, #-8]
    // 0x7f989c: mov             x2, x0
    // 0x7f98a0: r0 = _getValueOrData()
    //     0x7f98a0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7f98a4: mov             x1, x0
    // 0x7f98a8: ldur            x0, [fp, #-8]
    // 0x7f98ac: LoadField: r2 = r0->field_f
    //     0x7f98ac: ldur            w2, [x0, #0xf]
    // 0x7f98b0: DecompressPointer r2
    //     0x7f98b0: add             x2, x2, HEAP, lsl #32
    // 0x7f98b4: cmp             w2, w1
    // 0x7f98b8: b.ne            #0x7f98c4
    // 0x7f98bc: r3 = Null
    //     0x7f98bc: mov             x3, NULL
    // 0x7f98c0: b               #0x7f98c8
    // 0x7f98c4: mov             x3, x1
    // 0x7f98c8: stur            x3, [fp, #-8]
    // 0x7f98cc: cmp             w3, NULL
    // 0x7f98d0: b.eq            #0x7f9aa0
    // 0x7f98d4: ldur            x0, [fp, #-0x18]
    // 0x7f98d8: r2 = Null
    //     0x7f98d8: mov             x2, NULL
    // 0x7f98dc: r1 = Null
    //     0x7f98dc: mov             x1, NULL
    // 0x7f98e0: cmp             w0, NULL
    // 0x7f98e4: b.eq            #0x7f9904
    // 0x7f98e8: branchIfSmi(r0, 0x7f9904)
    //     0x7f98e8: tbz             w0, #0, #0x7f9904
    // 0x7f98ec: r3 = LoadClassIdInstr(r0)
    //     0x7f98ec: ldur            x3, [x0, #-1]
    //     0x7f98f0: ubfx            x3, x3, #0xc, #0x14
    // 0x7f98f4: cmp             x3, #0xda5
    // 0x7f98f8: b.eq            #0x7f990c
    // 0x7f98fc: cmp             x3, #0xfcf
    // 0x7f9900: b.eq            #0x7f990c
    // 0x7f9904: r0 = false
    //     0x7f9904: add             x0, NULL, #0x30  ; false
    // 0x7f9908: b               #0x7f9910
    // 0x7f990c: r0 = true
    //     0x7f990c: add             x0, NULL, #0x20  ; true
    // 0x7f9910: tbnz            w0, #4, #0x7f9924
    // 0x7f9914: ldur            x1, [fp, #-8]
    // 0x7f9918: ldur            x2, [fp, #-0x18]
    // 0x7f991c: r0 = _move()
    //     0x7f991c: bl              #0x7f9c88  ; [package:flutter/src/gestures/multidrag.dart] MultiDragPointerState::_move
    // 0x7f9920: b               #0x7f9a84
    // 0x7f9924: ldur            x0, [fp, #-0x18]
    // 0x7f9928: r2 = Null
    //     0x7f9928: mov             x2, NULL
    // 0x7f992c: r1 = Null
    //     0x7f992c: mov             x1, NULL
    // 0x7f9930: cmp             w0, NULL
    // 0x7f9934: b.eq            #0x7f9954
    // 0x7f9938: branchIfSmi(r0, 0x7f9954)
    //     0x7f9938: tbz             w0, #0, #0x7f9954
    // 0x7f993c: r3 = LoadClassIdInstr(r0)
    //     0x7f993c: ldur            x3, [x0, #-1]
    //     0x7f9940: ubfx            x3, x3, #0xc, #0x14
    // 0x7f9944: cmp             x3, #0xda3
    // 0x7f9948: b.eq            #0x7f995c
    // 0x7f994c: cmp             x3, #0xfcd
    // 0x7f9950: b.eq            #0x7f995c
    // 0x7f9954: r0 = false
    //     0x7f9954: add             x0, NULL, #0x30  ; false
    // 0x7f9958: b               #0x7f9960
    // 0x7f995c: r0 = true
    //     0x7f995c: add             x0, NULL, #0x20  ; true
    // 0x7f9960: tbnz            w0, #4, #0x7f99b4
    // 0x7f9964: ldur            x0, [fp, #-0x18]
    // 0x7f9968: ldur            x1, [fp, #-8]
    // 0x7f996c: r0 = _up()
    //     0x7f996c: bl              #0x7f9b34  ; [package:flutter/src/gestures/multidrag.dart] MultiDragPointerState::_up
    // 0x7f9970: ldur            x3, [fp, #-0x18]
    // 0x7f9974: r0 = LoadClassIdInstr(r3)
    //     0x7f9974: ldur            x0, [x3, #-1]
    //     0x7f9978: ubfx            x0, x0, #0xc, #0x14
    // 0x7f997c: mov             x1, x3
    // 0x7f9980: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7f9980: sub             lr, x0, #1, lsl #12
    //     0x7f9984: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9988: blr             lr
    // 0x7f998c: mov             x2, x0
    // 0x7f9990: r0 = BoxInt64Instr(r2)
    //     0x7f9990: sbfiz           x0, x2, #1, #0x1f
    //     0x7f9994: cmp             x2, x0, asr #1
    //     0x7f9998: b.eq            #0x7f99a4
    //     0x7f999c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7f99a0: stur            x2, [x0, #7]
    // 0x7f99a4: ldur            x1, [fp, #-0x10]
    // 0x7f99a8: mov             x2, x0
    // 0x7f99ac: r0 = _removeState()
    //     0x7f99ac: bl              #0x7f96f8  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::_removeState
    // 0x7f99b0: b               #0x7f9a84
    // 0x7f99b4: ldur            x3, [fp, #-0x18]
    // 0x7f99b8: mov             x0, x3
    // 0x7f99bc: r2 = Null
    //     0x7f99bc: mov             x2, NULL
    // 0x7f99c0: r1 = Null
    //     0x7f99c0: mov             x1, NULL
    // 0x7f99c4: cmp             w0, NULL
    // 0x7f99c8: b.eq            #0x7f99e8
    // 0x7f99cc: branchIfSmi(r0, 0x7f99e8)
    //     0x7f99cc: tbz             w0, #0, #0x7f99e8
    // 0x7f99d0: r3 = LoadClassIdInstr(r0)
    //     0x7f99d0: ldur            x3, [x0, #-1]
    //     0x7f99d4: ubfx            x3, x3, #0xc, #0x14
    // 0x7f99d8: cmp             x3, #0xd93
    // 0x7f99dc: b.eq            #0x7f99f0
    // 0x7f99e0: cmp             x3, #0xfc5
    // 0x7f99e4: b.eq            #0x7f99f0
    // 0x7f99e8: r0 = false
    //     0x7f99e8: add             x0, NULL, #0x30  ; false
    // 0x7f99ec: b               #0x7f99f4
    // 0x7f99f0: r0 = true
    //     0x7f99f0: add             x0, NULL, #0x20  ; true
    // 0x7f99f4: tbnz            w0, #4, #0x7f9a44
    // 0x7f99f8: ldur            x0, [fp, #-0x18]
    // 0x7f99fc: ldur            x1, [fp, #-8]
    // 0x7f9a00: r0 = _cancel()
    //     0x7f9a00: bl              #0x7f9aa4  ; [package:flutter/src/gestures/multidrag.dart] MultiDragPointerState::_cancel
    // 0x7f9a04: ldur            x1, [fp, #-0x18]
    // 0x7f9a08: r0 = LoadClassIdInstr(r1)
    //     0x7f9a08: ldur            x0, [x1, #-1]
    //     0x7f9a0c: ubfx            x0, x0, #0xc, #0x14
    // 0x7f9a10: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7f9a10: sub             lr, x0, #1, lsl #12
    //     0x7f9a14: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9a18: blr             lr
    // 0x7f9a1c: mov             x2, x0
    // 0x7f9a20: r0 = BoxInt64Instr(r2)
    //     0x7f9a20: sbfiz           x0, x2, #1, #0x1f
    //     0x7f9a24: cmp             x2, x0, asr #1
    //     0x7f9a28: b.eq            #0x7f9a34
    //     0x7f9a2c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7f9a30: stur            x2, [x0, #7]
    // 0x7f9a34: ldur            x1, [fp, #-0x10]
    // 0x7f9a38: mov             x2, x0
    // 0x7f9a3c: r0 = _removeState()
    //     0x7f9a3c: bl              #0x7f96f8  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::_removeState
    // 0x7f9a40: b               #0x7f9a84
    // 0x7f9a44: ldur            x1, [fp, #-0x18]
    // 0x7f9a48: mov             x0, x1
    // 0x7f9a4c: r2 = Null
    //     0x7f9a4c: mov             x2, NULL
    // 0x7f9a50: r1 = Null
    //     0x7f9a50: mov             x1, NULL
    // 0x7f9a54: cmp             w0, NULL
    // 0x7f9a58: b.eq            #0x7f9a78
    // 0x7f9a5c: branchIfSmi(r0, 0x7f9a78)
    //     0x7f9a5c: tbz             w0, #0, #0x7f9a78
    // 0x7f9a60: r3 = LoadClassIdInstr(r0)
    //     0x7f9a60: ldur            x3, [x0, #-1]
    //     0x7f9a64: ubfx            x3, x3, #0xc, #0x14
    // 0x7f9a68: cmp             x3, #0xda7
    // 0x7f9a6c: b.eq            #0x7f9a80
    // 0x7f9a70: cmp             x3, #0xfd1
    // 0x7f9a74: b.eq            #0x7f9a80
    // 0x7f9a78: r0 = false
    //     0x7f9a78: add             x0, NULL, #0x30  ; false
    // 0x7f9a7c: b               #0x7f9a84
    // 0x7f9a80: r0 = true
    //     0x7f9a80: add             x0, NULL, #0x20  ; true
    // 0x7f9a84: r0 = Null
    //     0x7f9a84: mov             x0, NULL
    // 0x7f9a88: LeaveFrame
    //     0x7f9a88: mov             SP, fp
    //     0x7f9a8c: ldp             fp, lr, [SP], #0x10
    // 0x7f9a90: ret
    //     0x7f9a90: ret             
    // 0x7f9a94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f9a94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f9a98: b               #0x7f9854
    // 0x7f9a9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f9a9c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7f9aa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f9aa0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ addAllowedPointer(/* No info */) {
    // ** addr: 0x802e28, size: 0x27c
    // 0x802e28: EnterFrame
    //     0x802e28: stp             fp, lr, [SP, #-0x10]!
    //     0x802e2c: mov             fp, SP
    // 0x802e30: AllocStack(0x30)
    //     0x802e30: sub             SP, SP, #0x30
    // 0x802e34: SetupParameters(MultiDragGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x802e34: mov             x3, x1
    //     0x802e38: stur            x1, [fp, #-8]
    //     0x802e3c: stur            x2, [fp, #-0x10]
    // 0x802e40: CheckStackOverflow
    //     0x802e40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x802e44: cmp             SP, x16
    //     0x802e48: b.ls            #0x803090
    // 0x802e4c: r0 = LoadClassIdInstr(r3)
    //     0x802e4c: ldur            x0, [x3, #-1]
    //     0x802e50: ubfx            x0, x0, #0xc, #0x14
    // 0x802e54: cmp             x0, #0xdc9
    // 0x802e58: b.ne            #0x802ed8
    // 0x802e5c: r0 = LoadClassIdInstr(r2)
    //     0x802e5c: ldur            x0, [x2, #-1]
    //     0x802e60: ubfx            x0, x0, #0xc, #0x14
    // 0x802e64: mov             x1, x2
    // 0x802e68: r0 = GDT[cid_x0 + -0x1]()
    //     0x802e68: sub             lr, x0, #1
    //     0x802e6c: ldr             lr, [x21, lr, lsl #3]
    //     0x802e70: blr             lr
    // 0x802e74: mov             x3, x0
    // 0x802e78: ldur            x2, [fp, #-0x10]
    // 0x802e7c: stur            x3, [fp, #-0x18]
    // 0x802e80: r0 = LoadClassIdInstr(r2)
    //     0x802e80: ldur            x0, [x2, #-1]
    //     0x802e84: ubfx            x0, x0, #0xc, #0x14
    // 0x802e88: mov             x1, x2
    // 0x802e8c: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x802e8c: movz            x17, #0x30b7
    //     0x802e90: movk            x17, #0x1, lsl #16
    //     0x802e94: add             lr, x0, x17
    //     0x802e98: ldr             lr, [x21, lr, lsl #3]
    //     0x802e9c: blr             lr
    // 0x802ea0: ldur            x2, [fp, #-8]
    // 0x802ea4: stur            x0, [fp, #-0x28]
    // 0x802ea8: LoadField: r5 = r2->field_7
    //     0x802ea8: ldur            w5, [x2, #7]
    // 0x802eac: DecompressPointer r5
    //     0x802eac: add             x5, x5, HEAP, lsl #32
    // 0x802eb0: stur            x5, [fp, #-0x20]
    // 0x802eb4: r0 = _DelayedPointerState()
    //     0x802eb4: bl              #0x803308  ; Allocate_DelayedPointerStateStub -> _DelayedPointerState (size=0x30)
    // 0x802eb8: mov             x1, x0
    // 0x802ebc: ldur            x2, [fp, #-0x18]
    // 0x802ec0: ldur            x3, [fp, #-0x28]
    // 0x802ec4: ldur            x5, [fp, #-0x20]
    // 0x802ec8: stur            x0, [fp, #-0x18]
    // 0x802ecc: r0 = _DelayedPointerState()
    //     0x802ecc: bl              #0x8031d8  ; [package:flutter/src/gestures/multidrag.dart] _DelayedPointerState::_DelayedPointerState
    // 0x802ed0: ldur            x4, [fp, #-0x18]
    // 0x802ed4: b               #0x802f5c
    // 0x802ed8: mov             x16, x2
    // 0x802edc: mov             x2, x3
    // 0x802ee0: mov             x3, x16
    // 0x802ee4: r0 = LoadClassIdInstr(r3)
    //     0x802ee4: ldur            x0, [x3, #-1]
    //     0x802ee8: ubfx            x0, x0, #0xc, #0x14
    // 0x802eec: mov             x1, x3
    // 0x802ef0: r0 = GDT[cid_x0 + -0x1]()
    //     0x802ef0: sub             lr, x0, #1
    //     0x802ef4: ldr             lr, [x21, lr, lsl #3]
    //     0x802ef8: blr             lr
    // 0x802efc: mov             x3, x0
    // 0x802f00: ldur            x2, [fp, #-0x10]
    // 0x802f04: stur            x3, [fp, #-0x18]
    // 0x802f08: r0 = LoadClassIdInstr(r2)
    //     0x802f08: ldur            x0, [x2, #-1]
    //     0x802f0c: ubfx            x0, x0, #0xc, #0x14
    // 0x802f10: mov             x1, x2
    // 0x802f14: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x802f14: movz            x17, #0x30b7
    //     0x802f18: movk            x17, #0x1, lsl #16
    //     0x802f1c: add             lr, x0, x17
    //     0x802f20: ldr             lr, [x21, lr, lsl #3]
    //     0x802f24: blr             lr
    // 0x802f28: ldur            x2, [fp, #-8]
    // 0x802f2c: stur            x0, [fp, #-0x28]
    // 0x802f30: LoadField: r5 = r2->field_7
    //     0x802f30: ldur            w5, [x2, #7]
    // 0x802f34: DecompressPointer r5
    //     0x802f34: add             x5, x5, HEAP, lsl #32
    // 0x802f38: stur            x5, [fp, #-0x20]
    // 0x802f3c: r0 = _ImmediatePointerState()
    //     0x802f3c: bl              #0x8031cc  ; Allocate_ImmediatePointerStateStub -> _ImmediatePointerState (size=0x28)
    // 0x802f40: mov             x1, x0
    // 0x802f44: ldur            x2, [fp, #-0x18]
    // 0x802f48: ldur            x3, [fp, #-0x28]
    // 0x802f4c: ldur            x5, [fp, #-0x20]
    // 0x802f50: stur            x0, [fp, #-0x18]
    // 0x802f54: r0 = MultiDragPointerState()
    //     0x802f54: bl              #0x8030a4  ; [package:flutter/src/gestures/multidrag.dart] MultiDragPointerState::MultiDragPointerState
    // 0x802f58: ldur            x4, [fp, #-0x18]
    // 0x802f5c: ldur            x2, [fp, #-8]
    // 0x802f60: ldur            x3, [fp, #-0x10]
    // 0x802f64: stur            x4, [fp, #-0x20]
    // 0x802f68: LoadField: r5 = r2->field_1b
    //     0x802f68: ldur            w5, [x2, #0x1b]
    // 0x802f6c: DecompressPointer r5
    //     0x802f6c: add             x5, x5, HEAP, lsl #32
    // 0x802f70: stur            x5, [fp, #-0x18]
    // 0x802f74: cmp             w5, NULL
    // 0x802f78: b.eq            #0x803098
    // 0x802f7c: r0 = LoadClassIdInstr(r3)
    //     0x802f7c: ldur            x0, [x3, #-1]
    //     0x802f80: ubfx            x0, x0, #0xc, #0x14
    // 0x802f84: mov             x1, x3
    // 0x802f88: r0 = GDT[cid_x0 + -0x1000]()
    //     0x802f88: sub             lr, x0, #1, lsl #12
    //     0x802f8c: ldr             lr, [x21, lr, lsl #3]
    //     0x802f90: blr             lr
    // 0x802f94: mov             x2, x0
    // 0x802f98: r0 = BoxInt64Instr(r2)
    //     0x802f98: sbfiz           x0, x2, #1, #0x1f
    //     0x802f9c: cmp             x2, x0, asr #1
    //     0x802fa0: b.eq            #0x802fac
    //     0x802fa4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x802fa8: stur            x2, [x0, #7]
    // 0x802fac: ldur            x1, [fp, #-0x18]
    // 0x802fb0: mov             x2, x0
    // 0x802fb4: ldur            x3, [fp, #-0x20]
    // 0x802fb8: r0 = []=()
    //     0x802fb8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x802fbc: r0 = LoadStaticField(0x968)
    //     0x802fbc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x802fc0: ldr             x0, [x0, #0x12d0]
    // 0x802fc4: cmp             w0, NULL
    // 0x802fc8: b.eq            #0x80309c
    // 0x802fcc: LoadField: r2 = r0->field_13
    //     0x802fcc: ldur            w2, [x0, #0x13]
    // 0x802fd0: DecompressPointer r2
    //     0x802fd0: add             x2, x2, HEAP, lsl #32
    // 0x802fd4: ldur            x3, [fp, #-0x10]
    // 0x802fd8: stur            x2, [fp, #-0x18]
    // 0x802fdc: r0 = LoadClassIdInstr(r3)
    //     0x802fdc: ldur            x0, [x3, #-1]
    //     0x802fe0: ubfx            x0, x0, #0xc, #0x14
    // 0x802fe4: mov             x1, x3
    // 0x802fe8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x802fe8: sub             lr, x0, #1, lsl #12
    //     0x802fec: ldr             lr, [x21, lr, lsl #3]
    //     0x802ff0: blr             lr
    // 0x802ff4: ldur            x2, [fp, #-8]
    // 0x802ff8: r1 = Function '_handleEvent@429470698':.
    //     0x802ff8: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fe40] AnonymousClosure: (0x7f97f4), in [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::_handleEvent (0x7f9830)
    //     0x802ffc: ldr             x1, [x1, #0xe40]
    // 0x803000: stur            x0, [fp, #-0x30]
    // 0x803004: r0 = AllocateClosure()
    //     0x803004: bl              #0xec1630  ; AllocateClosureStub
    // 0x803008: ldur            x1, [fp, #-0x18]
    // 0x80300c: ldur            x2, [fp, #-0x30]
    // 0x803010: mov             x3, x0
    // 0x803014: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x803014: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x803018: r0 = addRoute()
    //     0x803018: bl              #0x7ab798  ; [package:flutter/src/gestures/pointer_router.dart] PointerRouter::addRoute
    // 0x80301c: r0 = LoadStaticField(0x968)
    //     0x80301c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x803020: ldr             x0, [x0, #0x12d0]
    // 0x803024: cmp             w0, NULL
    // 0x803028: b.eq            #0x8030a0
    // 0x80302c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x80302c: ldur            w2, [x0, #0x17]
    // 0x803030: DecompressPointer r2
    //     0x803030: add             x2, x2, HEAP, lsl #32
    // 0x803034: ldur            x1, [fp, #-0x10]
    // 0x803038: stur            x2, [fp, #-0x18]
    // 0x80303c: r0 = LoadClassIdInstr(r1)
    //     0x80303c: ldur            x0, [x1, #-1]
    //     0x803040: ubfx            x0, x0, #0xc, #0x14
    // 0x803044: r0 = GDT[cid_x0 + -0x1000]()
    //     0x803044: sub             lr, x0, #1, lsl #12
    //     0x803048: ldr             lr, [x21, lr, lsl #3]
    //     0x80304c: blr             lr
    // 0x803050: ldur            x1, [fp, #-0x18]
    // 0x803054: mov             x2, x0
    // 0x803058: ldur            x3, [fp, #-8]
    // 0x80305c: r0 = add()
    //     0x80305c: bl              #0x7ab260  ; [package:flutter/src/gestures/arena.dart] GestureArenaManager::add
    // 0x803060: ldur            x1, [fp, #-0x20]
    // 0x803064: StoreField: r1->field_23 = r0
    //     0x803064: stur            w0, [x1, #0x23]
    //     0x803068: ldurb           w16, [x1, #-1]
    //     0x80306c: ldurb           w17, [x0, #-1]
    //     0x803070: and             x16, x17, x16, lsr #2
    //     0x803074: tst             x16, HEAP, lsr #32
    //     0x803078: b.eq            #0x803080
    //     0x80307c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x803080: r0 = Null
    //     0x803080: mov             x0, NULL
    // 0x803084: LeaveFrame
    //     0x803084: mov             SP, fp
    //     0x803088: ldp             fp, lr, [SP], #0x10
    // 0x80308c: ret
    //     0x80308c: ret             
    // 0x803090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x803090: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x803094: b               #0x802e4c
    // 0x803098: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x803098: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x80309c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80309c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8030a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8030a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ acceptGesture(/* No info */) {
    // ** addr: 0x86d1b0, size: 0x104
    // 0x86d1b0: EnterFrame
    //     0x86d1b0: stp             fp, lr, [SP, #-0x10]!
    //     0x86d1b4: mov             fp, SP
    // 0x86d1b8: AllocStack(0x20)
    //     0x86d1b8: sub             SP, SP, #0x20
    // 0x86d1bc: SetupParameters(MultiDragGestureRecognizer this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x86d1bc: stur            x1, [fp, #-8]
    //     0x86d1c0: stur            x2, [fp, #-0x10]
    // 0x86d1c4: CheckStackOverflow
    //     0x86d1c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86d1c8: cmp             SP, x16
    //     0x86d1cc: b.ls            #0x86d2a8
    // 0x86d1d0: r1 = 2
    //     0x86d1d0: movz            x1, #0x2
    // 0x86d1d4: r0 = AllocateContext()
    //     0x86d1d4: bl              #0xec126c  ; AllocateContextStub
    // 0x86d1d8: mov             x3, x0
    // 0x86d1dc: ldur            x2, [fp, #-8]
    // 0x86d1e0: stur            x3, [fp, #-0x20]
    // 0x86d1e4: StoreField: r3->field_f = r2
    //     0x86d1e4: stur            w2, [x3, #0xf]
    // 0x86d1e8: ldur            x4, [fp, #-0x10]
    // 0x86d1ec: r0 = BoxInt64Instr(r4)
    //     0x86d1ec: sbfiz           x0, x4, #1, #0x1f
    //     0x86d1f0: cmp             x4, x0, asr #1
    //     0x86d1f4: b.eq            #0x86d200
    //     0x86d1f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x86d1fc: stur            x4, [x0, #7]
    // 0x86d200: StoreField: r3->field_13 = r0
    //     0x86d200: stur            w0, [x3, #0x13]
    // 0x86d204: LoadField: r4 = r2->field_1b
    //     0x86d204: ldur            w4, [x2, #0x1b]
    // 0x86d208: DecompressPointer r4
    //     0x86d208: add             x4, x4, HEAP, lsl #32
    // 0x86d20c: stur            x4, [fp, #-0x18]
    // 0x86d210: cmp             w4, NULL
    // 0x86d214: b.eq            #0x86d2b0
    // 0x86d218: mov             x1, x4
    // 0x86d21c: mov             x2, x0
    // 0x86d220: r0 = _getValueOrData()
    //     0x86d220: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x86d224: mov             x1, x0
    // 0x86d228: ldur            x0, [fp, #-0x18]
    // 0x86d22c: LoadField: r2 = r0->field_f
    //     0x86d22c: ldur            w2, [x0, #0xf]
    // 0x86d230: DecompressPointer r2
    //     0x86d230: add             x2, x2, HEAP, lsl #32
    // 0x86d234: cmp             w2, w1
    // 0x86d238: b.ne            #0x86d244
    // 0x86d23c: r0 = Null
    //     0x86d23c: mov             x0, NULL
    // 0x86d240: b               #0x86d248
    // 0x86d244: mov             x0, x1
    // 0x86d248: stur            x0, [fp, #-8]
    // 0x86d24c: cmp             w0, NULL
    // 0x86d250: b.ne            #0x86d264
    // 0x86d254: r0 = Null
    //     0x86d254: mov             x0, NULL
    // 0x86d258: LeaveFrame
    //     0x86d258: mov             SP, fp
    //     0x86d25c: ldp             fp, lr, [SP], #0x10
    // 0x86d260: ret
    //     0x86d260: ret             
    // 0x86d264: ldur            x2, [fp, #-0x20]
    // 0x86d268: r1 = Function '<anonymous closure>':.
    //     0x86d268: add             x1, PP, #0x55, lsl #12  ; [pp+0x55280] AnonymousClosure: (0x86d2b4), in [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::acceptGesture (0x86d1b0)
    //     0x86d26c: ldr             x1, [x1, #0x280]
    // 0x86d270: r0 = AllocateClosure()
    //     0x86d270: bl              #0xec1630  ; AllocateClosureStub
    // 0x86d274: ldur            x1, [fp, #-8]
    // 0x86d278: r2 = LoadClassIdInstr(r1)
    //     0x86d278: ldur            x2, [x1, #-1]
    //     0x86d27c: ubfx            x2, x2, #0xc, #0x14
    // 0x86d280: mov             x16, x0
    // 0x86d284: mov             x0, x2
    // 0x86d288: mov             x2, x16
    // 0x86d28c: r0 = GDT[cid_x0 + -0xff7]()
    //     0x86d28c: sub             lr, x0, #0xff7
    //     0x86d290: ldr             lr, [x21, lr, lsl #3]
    //     0x86d294: blr             lr
    // 0x86d298: r0 = Null
    //     0x86d298: mov             x0, NULL
    // 0x86d29c: LeaveFrame
    //     0x86d29c: mov             SP, fp
    //     0x86d2a0: ldp             fp, lr, [SP], #0x10
    // 0x86d2a4: ret
    //     0x86d2a4: ret             
    // 0x86d2a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86d2a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86d2ac: b               #0x86d1d0
    // 0x86d2b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x86d2b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Drag? <anonymous closure>(dynamic, Offset) {
    // ** addr: 0x86d2b4, size: 0x5c
    // 0x86d2b4: EnterFrame
    //     0x86d2b4: stp             fp, lr, [SP, #-0x10]!
    //     0x86d2b8: mov             fp, SP
    // 0x86d2bc: ldr             x0, [fp, #0x18]
    // 0x86d2c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x86d2c0: ldur            w1, [x0, #0x17]
    // 0x86d2c4: DecompressPointer r1
    //     0x86d2c4: add             x1, x1, HEAP, lsl #32
    // 0x86d2c8: CheckStackOverflow
    //     0x86d2c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86d2cc: cmp             SP, x16
    //     0x86d2d0: b.ls            #0x86d308
    // 0x86d2d4: LoadField: r0 = r1->field_f
    //     0x86d2d4: ldur            w0, [x1, #0xf]
    // 0x86d2d8: DecompressPointer r0
    //     0x86d2d8: add             x0, x0, HEAP, lsl #32
    // 0x86d2dc: LoadField: r2 = r1->field_13
    //     0x86d2dc: ldur            w2, [x1, #0x13]
    // 0x86d2e0: DecompressPointer r2
    //     0x86d2e0: add             x2, x2, HEAP, lsl #32
    // 0x86d2e4: r3 = LoadInt32Instr(r2)
    //     0x86d2e4: sbfx            x3, x2, #1, #0x1f
    //     0x86d2e8: tbz             w2, #0, #0x86d2f0
    //     0x86d2ec: ldur            x3, [x2, #7]
    // 0x86d2f0: mov             x1, x0
    // 0x86d2f4: ldr             x2, [fp, #0x10]
    // 0x86d2f8: r0 = _startDrag()
    //     0x86d2f8: bl              #0x86d310  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::_startDrag
    // 0x86d2fc: LeaveFrame
    //     0x86d2fc: mov             SP, fp
    //     0x86d300: ldp             fp, lr, [SP], #0x10
    // 0x86d304: ret
    //     0x86d304: ret             
    // 0x86d308: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86d308: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86d30c: b               #0x86d2d4
  }
  _ _startDrag(/* No info */) {
    // ** addr: 0x86d310, size: 0x140
    // 0x86d310: EnterFrame
    //     0x86d310: stp             fp, lr, [SP, #-0x10]!
    //     0x86d314: mov             fp, SP
    // 0x86d318: AllocStack(0x40)
    //     0x86d318: sub             SP, SP, #0x40
    // 0x86d31c: SetupParameters(MultiDragGestureRecognizer this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x86d31c: stur            x1, [fp, #-8]
    //     0x86d320: stur            x2, [fp, #-0x10]
    //     0x86d324: stur            x3, [fp, #-0x18]
    // 0x86d328: CheckStackOverflow
    //     0x86d328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86d32c: cmp             SP, x16
    //     0x86d330: b.ls            #0x86d440
    // 0x86d334: r1 = 2
    //     0x86d334: movz            x1, #0x2
    // 0x86d338: r0 = AllocateContext()
    //     0x86d338: bl              #0xec126c  ; AllocateContextStub
    // 0x86d33c: mov             x4, x0
    // 0x86d340: ldur            x3, [fp, #-8]
    // 0x86d344: stur            x4, [fp, #-0x28]
    // 0x86d348: StoreField: r4->field_f = r3
    //     0x86d348: stur            w3, [x4, #0xf]
    // 0x86d34c: ldur            x0, [fp, #-0x10]
    // 0x86d350: StoreField: r4->field_13 = r0
    //     0x86d350: stur            w0, [x4, #0x13]
    // 0x86d354: LoadField: r5 = r3->field_1b
    //     0x86d354: ldur            w5, [x3, #0x1b]
    // 0x86d358: DecompressPointer r5
    //     0x86d358: add             x5, x5, HEAP, lsl #32
    // 0x86d35c: stur            x5, [fp, #-0x20]
    // 0x86d360: cmp             w5, NULL
    // 0x86d364: b.eq            #0x86d448
    // 0x86d368: ldur            x2, [fp, #-0x18]
    // 0x86d36c: r0 = BoxInt64Instr(r2)
    //     0x86d36c: sbfiz           x0, x2, #1, #0x1f
    //     0x86d370: cmp             x2, x0, asr #1
    //     0x86d374: b.eq            #0x86d380
    //     0x86d378: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x86d37c: stur            x2, [x0, #7]
    // 0x86d380: mov             x1, x5
    // 0x86d384: mov             x2, x0
    // 0x86d388: stur            x0, [fp, #-0x10]
    // 0x86d38c: r0 = _getValueOrData()
    //     0x86d38c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x86d390: mov             x1, x0
    // 0x86d394: ldur            x0, [fp, #-0x20]
    // 0x86d398: LoadField: r2 = r0->field_f
    //     0x86d398: ldur            w2, [x0, #0xf]
    // 0x86d39c: DecompressPointer r2
    //     0x86d39c: add             x2, x2, HEAP, lsl #32
    // 0x86d3a0: cmp             w2, w1
    // 0x86d3a4: b.ne            #0x86d3b0
    // 0x86d3a8: r3 = Null
    //     0x86d3a8: mov             x3, NULL
    // 0x86d3ac: b               #0x86d3b4
    // 0x86d3b0: mov             x3, x1
    // 0x86d3b4: ldur            x0, [fp, #-8]
    // 0x86d3b8: stur            x3, [fp, #-0x20]
    // 0x86d3bc: cmp             w3, NULL
    // 0x86d3c0: b.eq            #0x86d44c
    // 0x86d3c4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x86d3c4: ldur            w1, [x0, #0x17]
    // 0x86d3c8: DecompressPointer r1
    //     0x86d3c8: add             x1, x1, HEAP, lsl #32
    // 0x86d3cc: cmp             w1, NULL
    // 0x86d3d0: b.eq            #0x86d404
    // 0x86d3d4: ldur            x2, [fp, #-0x28]
    // 0x86d3d8: r1 = Function '<anonymous closure>':.
    //     0x86d3d8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55288] AnonymousClosure: (0x86d544), in [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::_startDrag (0x86d310)
    //     0x86d3dc: ldr             x1, [x1, #0x288]
    // 0x86d3e0: r0 = AllocateClosure()
    //     0x86d3e0: bl              #0xec1630  ; AllocateClosureStub
    // 0x86d3e4: r16 = <Drag?>
    //     0x86d3e4: add             x16, PP, #0x55, lsl #12  ; [pp+0x55290] TypeArguments: <Drag?>
    //     0x86d3e8: ldr             x16, [x16, #0x290]
    // 0x86d3ec: ldur            lr, [fp, #-8]
    // 0x86d3f0: stp             lr, x16, [SP, #8]
    // 0x86d3f4: str             x0, [SP]
    // 0x86d3f8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x86d3f8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x86d3fc: r0 = invokeCallback()
    //     0x86d3fc: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x86d400: b               #0x86d408
    // 0x86d404: r0 = Null
    //     0x86d404: mov             x0, NULL
    // 0x86d408: stur            x0, [fp, #-0x28]
    // 0x86d40c: cmp             w0, NULL
    // 0x86d410: b.eq            #0x86d424
    // 0x86d414: ldur            x1, [fp, #-0x20]
    // 0x86d418: mov             x2, x0
    // 0x86d41c: r0 = _startDrag()
    //     0x86d41c: bl              #0x86d450  ; [package:flutter/src/gestures/multidrag.dart] MultiDragPointerState::_startDrag
    // 0x86d420: b               #0x86d430
    // 0x86d424: ldur            x1, [fp, #-8]
    // 0x86d428: ldur            x2, [fp, #-0x10]
    // 0x86d42c: r0 = _removeState()
    //     0x86d42c: bl              #0x7f96f8  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::_removeState
    // 0x86d430: ldur            x0, [fp, #-0x28]
    // 0x86d434: LeaveFrame
    //     0x86d434: mov             SP, fp
    //     0x86d438: ldp             fp, lr, [SP], #0x10
    // 0x86d43c: ret
    //     0x86d43c: ret             
    // 0x86d440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86d440: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86d444: b               #0x86d334
    // 0x86d448: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x86d448: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x86d44c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x86d44c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Drag? <anonymous closure>(dynamic) {
    // ** addr: 0x86d544, size: 0x70
    // 0x86d544: EnterFrame
    //     0x86d544: stp             fp, lr, [SP, #-0x10]!
    //     0x86d548: mov             fp, SP
    // 0x86d54c: AllocStack(0x10)
    //     0x86d54c: sub             SP, SP, #0x10
    // 0x86d550: SetupParameters()
    //     0x86d550: ldr             x0, [fp, #0x10]
    //     0x86d554: ldur            w1, [x0, #0x17]
    //     0x86d558: add             x1, x1, HEAP, lsl #32
    // 0x86d55c: CheckStackOverflow
    //     0x86d55c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86d560: cmp             SP, x16
    //     0x86d564: b.ls            #0x86d5a8
    // 0x86d568: LoadField: r0 = r1->field_f
    //     0x86d568: ldur            w0, [x1, #0xf]
    // 0x86d56c: DecompressPointer r0
    //     0x86d56c: add             x0, x0, HEAP, lsl #32
    // 0x86d570: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x86d570: ldur            w2, [x0, #0x17]
    // 0x86d574: DecompressPointer r2
    //     0x86d574: add             x2, x2, HEAP, lsl #32
    // 0x86d578: cmp             w2, NULL
    // 0x86d57c: b.eq            #0x86d5b0
    // 0x86d580: LoadField: r0 = r1->field_13
    //     0x86d580: ldur            w0, [x1, #0x13]
    // 0x86d584: DecompressPointer r0
    //     0x86d584: add             x0, x0, HEAP, lsl #32
    // 0x86d588: stp             x0, x2, [SP]
    // 0x86d58c: mov             x0, x2
    // 0x86d590: ClosureCall
    //     0x86d590: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x86d594: ldur            x2, [x0, #0x1f]
    //     0x86d598: blr             x2
    // 0x86d59c: LeaveFrame
    //     0x86d59c: mov             SP, fp
    //     0x86d5a0: ldp             fp, lr, [SP], #0x10
    // 0x86d5a4: ret
    //     0x86d5a4: ret             
    // 0x86d5a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86d5a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86d5ac: b               #0x86d568
    // 0x86d5b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x86d5b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ MultiDragGestureRecognizer(/* No info */) {
    // ** addr: 0xaa6b78, size: 0xa8
    // 0xaa6b78: EnterFrame
    //     0xaa6b78: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6b7c: mov             fp, SP
    // 0xaa6b80: AllocStack(0x18)
    //     0xaa6b80: sub             SP, SP, #0x18
    // 0xaa6b84: SetupParameters(MultiDragGestureRecognizer this /* r1 => r1, fp-0x8 */)
    //     0xaa6b84: stur            x1, [fp, #-8]
    // 0xaa6b88: CheckStackOverflow
    //     0xaa6b88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6b8c: cmp             SP, x16
    //     0xaa6b90: b.ls            #0xaa6c18
    // 0xaa6b94: r16 = <int, MultiDragPointerState>
    //     0xaa6b94: add             x16, PP, #0x4f, lsl #12  ; [pp+0x4fe50] TypeArguments: <int, MultiDragPointerState>
    //     0xaa6b98: ldr             x16, [x16, #0xe50]
    // 0xaa6b9c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xaa6ba0: stp             lr, x16, [SP]
    // 0xaa6ba4: r0 = Map._fromLiteral()
    //     0xaa6ba4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xaa6ba8: ldur            x1, [fp, #-8]
    // 0xaa6bac: StoreField: r1->field_1b = r0
    //     0xaa6bac: stur            w0, [x1, #0x1b]
    //     0xaa6bb0: ldurb           w16, [x1, #-1]
    //     0xaa6bb4: ldurb           w17, [x0, #-1]
    //     0xaa6bb8: and             x16, x17, x16, lsr #2
    //     0xaa6bbc: tst             x16, HEAP, lsr #32
    //     0xaa6bc0: b.eq            #0xaa6bc8
    //     0xaa6bc4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa6bc8: r16 = <int, PointerDeviceKind>
    //     0xaa6bc8: add             x16, PP, #0x25, lsl #12  ; [pp+0x253f8] TypeArguments: <int, PointerDeviceKind>
    //     0xaa6bcc: ldr             x16, [x16, #0x3f8]
    // 0xaa6bd0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xaa6bd4: stp             lr, x16, [SP]
    // 0xaa6bd8: r0 = Map._fromLiteral()
    //     0xaa6bd8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xaa6bdc: ldur            x1, [fp, #-8]
    // 0xaa6be0: StoreField: r1->field_13 = r0
    //     0xaa6be0: stur            w0, [x1, #0x13]
    //     0xaa6be4: ldurb           w16, [x1, #-1]
    //     0xaa6be8: ldurb           w17, [x0, #-1]
    //     0xaa6bec: and             x16, x17, x16, lsr #2
    //     0xaa6bf0: tst             x16, HEAP, lsr #32
    //     0xaa6bf4: b.eq            #0xaa6bfc
    //     0xaa6bf8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa6bfc: r2 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@429470698': static.
    //     0xaa6bfc: add             x2, PP, #0x4f, lsl #12  ; [pp+0x4fe58] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@429470698': static. (0x7e54fb2535bc)
    //     0xaa6c00: ldr             x2, [x2, #0xe58]
    // 0xaa6c04: StoreField: r1->field_f = r2
    //     0xaa6c04: stur            w2, [x1, #0xf]
    // 0xaa6c08: r0 = Null
    //     0xaa6c08: mov             x0, NULL
    // 0xaa6c0c: LeaveFrame
    //     0xaa6c0c: mov             SP, fp
    //     0xaa6c10: ldp             fp, lr, [SP], #0x10
    // 0xaa6c14: ret
    //     0xaa6c14: ret             
    // 0xaa6c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa6c18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6c1c: b               #0xaa6b94
  }
  _ rejectGesture(/* No info */) {
    // ** addr: 0xccee94, size: 0xd4
    // 0xccee94: EnterFrame
    //     0xccee94: stp             fp, lr, [SP, #-0x10]!
    //     0xccee98: mov             fp, SP
    // 0xccee9c: AllocStack(0x18)
    //     0xccee9c: sub             SP, SP, #0x18
    // 0xcceea0: SetupParameters(MultiDragGestureRecognizer this /* r1 => r3, fp-0x10 */)
    //     0xcceea0: mov             x3, x1
    //     0xcceea4: stur            x1, [fp, #-0x10]
    // 0xcceea8: CheckStackOverflow
    //     0xcceea8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcceeac: cmp             SP, x16
    //     0xcceeb0: b.ls            #0xccef54
    // 0xcceeb4: LoadField: r4 = r3->field_1b
    //     0xcceeb4: ldur            w4, [x3, #0x1b]
    // 0xcceeb8: DecompressPointer r4
    //     0xcceeb8: add             x4, x4, HEAP, lsl #32
    // 0xcceebc: cmp             w4, NULL
    // 0xcceec0: b.eq            #0xccef5c
    // 0xcceec4: r0 = BoxInt64Instr(r2)
    //     0xcceec4: sbfiz           x0, x2, #1, #0x1f
    //     0xcceec8: cmp             x2, x0, asr #1
    //     0xcceecc: b.eq            #0xcceed8
    //     0xcceed0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcceed4: stur            x2, [x0, #7]
    // 0xcceed8: mov             x1, x4
    // 0xcceedc: mov             x2, x0
    // 0xcceee0: stur            x0, [fp, #-8]
    // 0xcceee4: r0 = containsKey()
    //     0xcceee4: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xcceee8: tbnz            w0, #4, #0xccef44
    // 0xcceeec: ldur            x0, [fp, #-0x10]
    // 0xcceef0: LoadField: r3 = r0->field_1b
    //     0xcceef0: ldur            w3, [x0, #0x1b]
    // 0xcceef4: DecompressPointer r3
    //     0xcceef4: add             x3, x3, HEAP, lsl #32
    // 0xcceef8: stur            x3, [fp, #-0x18]
    // 0xcceefc: cmp             w3, NULL
    // 0xccef00: b.eq            #0xccef60
    // 0xccef04: mov             x1, x3
    // 0xccef08: ldur            x2, [fp, #-8]
    // 0xccef0c: r0 = _getValueOrData()
    //     0xccef0c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xccef10: mov             x1, x0
    // 0xccef14: ldur            x0, [fp, #-0x18]
    // 0xccef18: LoadField: r2 = r0->field_f
    //     0xccef18: ldur            w2, [x0, #0xf]
    // 0xccef1c: DecompressPointer r2
    //     0xccef1c: add             x2, x2, HEAP, lsl #32
    // 0xccef20: cmp             w2, w1
    // 0xccef24: b.ne            #0xccef2c
    // 0xccef28: r1 = Null
    //     0xccef28: mov             x1, NULL
    // 0xccef2c: cmp             w1, NULL
    // 0xccef30: b.eq            #0xccef64
    // 0xccef34: r0 = rejected()
    //     0xccef34: bl              #0xccef68  ; [package:flutter/src/gestures/multidrag.dart] MultiDragPointerState::rejected
    // 0xccef38: ldur            x1, [fp, #-0x10]
    // 0xccef3c: ldur            x2, [fp, #-8]
    // 0xccef40: r0 = _removeState()
    //     0xccef40: bl              #0x7f96f8  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::_removeState
    // 0xccef44: r0 = Null
    //     0xccef44: mov             x0, NULL
    // 0xccef48: LeaveFrame
    //     0xccef48: mov             SP, fp
    //     0xccef4c: ldp             fp, lr, [SP], #0x10
    // 0xccef50: ret
    //     0xccef50: ret             
    // 0xccef54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xccef54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xccef58: b               #0xcceeb4
    // 0xccef5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xccef5c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xccef60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xccef60: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xccef64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xccef64: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3529, size: 0x24, field offset: 0x20
class DelayedMultiDragGestureRecognizer extends MultiDragGestureRecognizer {

  _ DelayedMultiDragGestureRecognizer(/* No info */) {
    // ** addr: 0xaa6b34, size: 0x38
    // 0xaa6b34: EnterFrame
    //     0xaa6b34: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6b38: mov             fp, SP
    // 0xaa6b3c: r0 = Instance_Duration
    //     0xaa6b3c: ldr             x0, [PP, #0x4fa0]  ; [pp+0x4fa0] Obj!Duration@e3a0b1
    // 0xaa6b40: CheckStackOverflow
    //     0xaa6b40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6b44: cmp             SP, x16
    //     0xaa6b48: b.ls            #0xaa6b64
    // 0xaa6b4c: StoreField: r1->field_1f = r0
    //     0xaa6b4c: stur            w0, [x1, #0x1f]
    // 0xaa6b50: r0 = MultiDragGestureRecognizer()
    //     0xaa6b50: bl              #0xaa6b78  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::MultiDragGestureRecognizer
    // 0xaa6b54: r0 = Null
    //     0xaa6b54: mov             x0, NULL
    // 0xaa6b58: LeaveFrame
    //     0xaa6b58: mov             SP, fp
    //     0xaa6b5c: ldp             fp, lr, [SP], #0x10
    // 0xaa6b60: ret
    //     0xaa6b60: ret             
    // 0xaa6b64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa6b64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6b68: b               #0xaa6b4c
  }
}

// class id: 3530, size: 0x20, field offset: 0x20
class ImmediateMultiDragGestureRecognizer extends MultiDragGestureRecognizer {
}
