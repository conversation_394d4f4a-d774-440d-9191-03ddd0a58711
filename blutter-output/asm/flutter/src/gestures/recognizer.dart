// lib: , url: package:flutter/src/gestures/recognizer.dart

// class id: 1048840, size: 0x8
class :: {
}

// class id: 3443, size: 0x10, field offset: 0x8
//   const constructor, 
class OffsetPair extends Object {

  Offset field_8;
  Offset field_c;

  OffsetPair +(OffsetPair, OffsetPair) {
    // ** addr: 0x75a1b8, size: 0x84
    // 0x75a1b8: EnterFrame
    //     0x75a1b8: stp             fp, lr, [SP, #-0x10]!
    //     0x75a1bc: mov             fp, SP
    // 0x75a1c0: CheckStackOverflow
    //     0x75a1c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75a1c4: cmp             SP, x16
    //     0x75a1c8: b.ls            #0x75a21c
    // 0x75a1cc: ldr             x0, [fp, #0x10]
    // 0x75a1d0: r2 = Null
    //     0x75a1d0: mov             x2, NULL
    // 0x75a1d4: r1 = Null
    //     0x75a1d4: mov             x1, NULL
    // 0x75a1d8: r4 = 60
    //     0x75a1d8: movz            x4, #0x3c
    // 0x75a1dc: branchIfSmi(r0, 0x75a1e8)
    //     0x75a1dc: tbz             w0, #0, #0x75a1e8
    // 0x75a1e0: r4 = LoadClassIdInstr(r0)
    //     0x75a1e0: ldur            x4, [x0, #-1]
    //     0x75a1e4: ubfx            x4, x4, #0xc, #0x14
    // 0x75a1e8: cmp             x4, #0xd73
    // 0x75a1ec: b.eq            #0x75a204
    // 0x75a1f0: r8 = OffsetPair
    //     0x75a1f0: add             x8, PP, #0x39, lsl #12  ; [pp+0x39b98] Type: OffsetPair
    //     0x75a1f4: ldr             x8, [x8, #0xb98]
    // 0x75a1f8: r3 = Null
    //     0x75a1f8: add             x3, PP, #0x39, lsl #12  ; [pp+0x39bb0] Null
    //     0x75a1fc: ldr             x3, [x3, #0xbb0]
    // 0x75a200: r0 = DefaultTypeTest()
    //     0x75a200: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x75a204: ldr             x1, [fp, #0x18]
    // 0x75a208: ldr             x2, [fp, #0x10]
    // 0x75a20c: r0 = +()
    //     0x75a20c: bl              #0x75a4a8  ; [package:flutter/src/gestures/recognizer.dart] OffsetPair::+
    // 0x75a210: LeaveFrame
    //     0x75a210: mov             SP, fp
    //     0x75a214: ldp             fp, lr, [SP], #0x10
    // 0x75a218: ret
    //     0x75a218: ret             
    // 0x75a21c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75a21c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75a220: b               #0x75a1cc
  }
  OffsetPair -(OffsetPair, OffsetPair) {
    // ** addr: 0x75a23c, size: 0x84
    // 0x75a23c: EnterFrame
    //     0x75a23c: stp             fp, lr, [SP, #-0x10]!
    //     0x75a240: mov             fp, SP
    // 0x75a244: CheckStackOverflow
    //     0x75a244: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75a248: cmp             SP, x16
    //     0x75a24c: b.ls            #0x75a2a0
    // 0x75a250: ldr             x0, [fp, #0x10]
    // 0x75a254: r2 = Null
    //     0x75a254: mov             x2, NULL
    // 0x75a258: r1 = Null
    //     0x75a258: mov             x1, NULL
    // 0x75a25c: r4 = 60
    //     0x75a25c: movz            x4, #0x3c
    // 0x75a260: branchIfSmi(r0, 0x75a26c)
    //     0x75a260: tbz             w0, #0, #0x75a26c
    // 0x75a264: r4 = LoadClassIdInstr(r0)
    //     0x75a264: ldur            x4, [x0, #-1]
    //     0x75a268: ubfx            x4, x4, #0xc, #0x14
    // 0x75a26c: cmp             x4, #0xd73
    // 0x75a270: b.eq            #0x75a288
    // 0x75a274: r8 = OffsetPair
    //     0x75a274: add             x8, PP, #0x39, lsl #12  ; [pp+0x39b98] Type: OffsetPair
    //     0x75a278: ldr             x8, [x8, #0xb98]
    // 0x75a27c: r3 = Null
    //     0x75a27c: add             x3, PP, #0x39, lsl #12  ; [pp+0x39ba0] Null
    //     0x75a280: ldr             x3, [x3, #0xba0]
    // 0x75a284: r0 = DefaultTypeTest()
    //     0x75a284: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x75a288: ldr             x1, [fp, #0x18]
    // 0x75a28c: ldr             x2, [fp, #0x10]
    // 0x75a290: r0 = -()
    //     0x75a290: bl              #0x75a2a8  ; [package:flutter/src/gestures/recognizer.dart] OffsetPair::-
    // 0x75a294: LeaveFrame
    //     0x75a294: mov             SP, fp
    //     0x75a298: ldp             fp, lr, [SP], #0x10
    // 0x75a29c: ret
    //     0x75a29c: ret             
    // 0x75a2a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75a2a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75a2a4: b               #0x75a250
  }
  OffsetPair -(OffsetPair, OffsetPair) {
    // ** addr: 0x75a2a8, size: 0x8c
    // 0x75a2a8: EnterFrame
    //     0x75a2a8: stp             fp, lr, [SP, #-0x10]!
    //     0x75a2ac: mov             fp, SP
    // 0x75a2b0: AllocStack(0x18)
    //     0x75a2b0: sub             SP, SP, #0x18
    // 0x75a2b4: SetupParameters(OffsetPair this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x75a2b4: mov             x3, x1
    //     0x75a2b8: mov             x0, x2
    //     0x75a2bc: stur            x1, [fp, #-8]
    //     0x75a2c0: stur            x2, [fp, #-0x10]
    // 0x75a2c4: CheckStackOverflow
    //     0x75a2c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75a2c8: cmp             SP, x16
    //     0x75a2cc: b.ls            #0x75a32c
    // 0x75a2d0: LoadField: r1 = r3->field_7
    //     0x75a2d0: ldur            w1, [x3, #7]
    // 0x75a2d4: DecompressPointer r1
    //     0x75a2d4: add             x1, x1, HEAP, lsl #32
    // 0x75a2d8: LoadField: r2 = r0->field_7
    //     0x75a2d8: ldur            w2, [x0, #7]
    // 0x75a2dc: DecompressPointer r2
    //     0x75a2dc: add             x2, x2, HEAP, lsl #32
    // 0x75a2e0: r0 = -()
    //     0x75a2e0: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x75a2e4: mov             x3, x0
    // 0x75a2e8: ldur            x0, [fp, #-8]
    // 0x75a2ec: stur            x3, [fp, #-0x18]
    // 0x75a2f0: LoadField: r1 = r0->field_b
    //     0x75a2f0: ldur            w1, [x0, #0xb]
    // 0x75a2f4: DecompressPointer r1
    //     0x75a2f4: add             x1, x1, HEAP, lsl #32
    // 0x75a2f8: ldur            x0, [fp, #-0x10]
    // 0x75a2fc: LoadField: r2 = r0->field_b
    //     0x75a2fc: ldur            w2, [x0, #0xb]
    // 0x75a300: DecompressPointer r2
    //     0x75a300: add             x2, x2, HEAP, lsl #32
    // 0x75a304: r0 = -()
    //     0x75a304: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x75a308: stur            x0, [fp, #-8]
    // 0x75a30c: r0 = OffsetPair()
    //     0x75a30c: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x75a310: ldur            x1, [fp, #-0x18]
    // 0x75a314: StoreField: r0->field_7 = r1
    //     0x75a314: stur            w1, [x0, #7]
    // 0x75a318: ldur            x1, [fp, #-8]
    // 0x75a31c: StoreField: r0->field_b = r1
    //     0x75a31c: stur            w1, [x0, #0xb]
    // 0x75a320: LeaveFrame
    //     0x75a320: mov             SP, fp
    //     0x75a324: ldp             fp, lr, [SP], #0x10
    // 0x75a328: ret
    //     0x75a328: ret             
    // 0x75a32c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75a32c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75a330: b               #0x75a2d0
  }
  OffsetPair +(OffsetPair, OffsetPair) {
    // ** addr: 0x75a4a8, size: 0x8c
    // 0x75a4a8: EnterFrame
    //     0x75a4a8: stp             fp, lr, [SP, #-0x10]!
    //     0x75a4ac: mov             fp, SP
    // 0x75a4b0: AllocStack(0x18)
    //     0x75a4b0: sub             SP, SP, #0x18
    // 0x75a4b4: SetupParameters(OffsetPair this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x75a4b4: mov             x3, x1
    //     0x75a4b8: mov             x0, x2
    //     0x75a4bc: stur            x1, [fp, #-8]
    //     0x75a4c0: stur            x2, [fp, #-0x10]
    // 0x75a4c4: CheckStackOverflow
    //     0x75a4c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75a4c8: cmp             SP, x16
    //     0x75a4cc: b.ls            #0x75a52c
    // 0x75a4d0: LoadField: r1 = r3->field_7
    //     0x75a4d0: ldur            w1, [x3, #7]
    // 0x75a4d4: DecompressPointer r1
    //     0x75a4d4: add             x1, x1, HEAP, lsl #32
    // 0x75a4d8: LoadField: r2 = r0->field_7
    //     0x75a4d8: ldur            w2, [x0, #7]
    // 0x75a4dc: DecompressPointer r2
    //     0x75a4dc: add             x2, x2, HEAP, lsl #32
    // 0x75a4e0: r0 = +()
    //     0x75a4e0: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x75a4e4: mov             x3, x0
    // 0x75a4e8: ldur            x0, [fp, #-8]
    // 0x75a4ec: stur            x3, [fp, #-0x18]
    // 0x75a4f0: LoadField: r1 = r0->field_b
    //     0x75a4f0: ldur            w1, [x0, #0xb]
    // 0x75a4f4: DecompressPointer r1
    //     0x75a4f4: add             x1, x1, HEAP, lsl #32
    // 0x75a4f8: ldur            x0, [fp, #-0x10]
    // 0x75a4fc: LoadField: r2 = r0->field_b
    //     0x75a4fc: ldur            w2, [x0, #0xb]
    // 0x75a500: DecompressPointer r2
    //     0x75a500: add             x2, x2, HEAP, lsl #32
    // 0x75a504: r0 = +()
    //     0x75a504: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x75a508: stur            x0, [fp, #-8]
    // 0x75a50c: r0 = OffsetPair()
    //     0x75a50c: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x75a510: ldur            x1, [fp, #-0x18]
    // 0x75a514: StoreField: r0->field_7 = r1
    //     0x75a514: stur            w1, [x0, #7]
    // 0x75a518: ldur            x1, [fp, #-8]
    // 0x75a51c: StoreField: r0->field_b = r1
    //     0x75a51c: stur            w1, [x0, #0xb]
    // 0x75a520: LeaveFrame
    //     0x75a520: mov             SP, fp
    //     0x75a524: ldp             fp, lr, [SP], #0x10
    // 0x75a528: ret
    //     0x75a528: ret             
    // 0x75a52c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75a52c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75a530: b               #0x75a4d0
  }
  _ OffsetPair.fromEventPosition(/* No info */) {
    // ** addr: 0x75b878, size: 0xb4
    // 0x75b878: EnterFrame
    //     0x75b878: stp             fp, lr, [SP, #-0x10]!
    //     0x75b87c: mov             fp, SP
    // 0x75b880: AllocStack(0x10)
    //     0x75b880: sub             SP, SP, #0x10
    // 0x75b884: SetupParameters(OffsetPair this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x75b884: mov             x3, x1
    //     0x75b888: stur            x1, [fp, #-8]
    //     0x75b88c: stur            x2, [fp, #-0x10]
    // 0x75b890: CheckStackOverflow
    //     0x75b890: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75b894: cmp             SP, x16
    //     0x75b898: b.ls            #0x75b924
    // 0x75b89c: r0 = LoadClassIdInstr(r2)
    //     0x75b89c: ldur            x0, [x2, #-1]
    //     0x75b8a0: ubfx            x0, x0, #0xc, #0x14
    // 0x75b8a4: mov             x1, x2
    // 0x75b8a8: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x75b8a8: movz            x17, #0x30fa
    //     0x75b8ac: movk            x17, #0x1, lsl #16
    //     0x75b8b0: add             lr, x0, x17
    //     0x75b8b4: ldr             lr, [x21, lr, lsl #3]
    //     0x75b8b8: blr             lr
    // 0x75b8bc: ldur            x2, [fp, #-8]
    // 0x75b8c0: StoreField: r2->field_7 = r0
    //     0x75b8c0: stur            w0, [x2, #7]
    //     0x75b8c4: ldurb           w16, [x2, #-1]
    //     0x75b8c8: ldurb           w17, [x0, #-1]
    //     0x75b8cc: and             x16, x17, x16, lsr #2
    //     0x75b8d0: tst             x16, HEAP, lsr #32
    //     0x75b8d4: b.eq            #0x75b8dc
    //     0x75b8d8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x75b8dc: ldur            x1, [fp, #-0x10]
    // 0x75b8e0: r0 = LoadClassIdInstr(r1)
    //     0x75b8e0: ldur            x0, [x1, #-1]
    //     0x75b8e4: ubfx            x0, x0, #0xc, #0x14
    // 0x75b8e8: r0 = GDT[cid_x0 + -0x1]()
    //     0x75b8e8: sub             lr, x0, #1
    //     0x75b8ec: ldr             lr, [x21, lr, lsl #3]
    //     0x75b8f0: blr             lr
    // 0x75b8f4: ldur            x1, [fp, #-8]
    // 0x75b8f8: StoreField: r1->field_b = r0
    //     0x75b8f8: stur            w0, [x1, #0xb]
    //     0x75b8fc: ldurb           w16, [x1, #-1]
    //     0x75b900: ldurb           w17, [x0, #-1]
    //     0x75b904: and             x16, x17, x16, lsr #2
    //     0x75b908: tst             x16, HEAP, lsr #32
    //     0x75b90c: b.eq            #0x75b914
    //     0x75b910: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x75b914: r0 = Null
    //     0x75b914: mov             x0, NULL
    // 0x75b918: LeaveFrame
    //     0x75b918: mov             SP, fp
    //     0x75b91c: ldp             fp, lr, [SP], #0x10
    // 0x75b920: ret
    //     0x75b920: ret             
    // 0x75b924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75b924: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75b928: b               #0x75b89c
  }
}

// class id: 3525, size: 0x8, field offset: 0x8
//   transformed mixin,
abstract class _GestureRecognizer&GestureArenaMember&DiagnosticableTreeMixin extends GestureArenaMember
     with DiagnosticableTreeMixin {
}

// class id: 3526, size: 0x18, field offset: 0x8
abstract class GestureRecognizer extends _GestureRecognizer&GestureArenaMember&DiagnosticableTreeMixin {

  _ invokeCallback(/* No info */) {
    // ** addr: 0x759bd0, size: 0xac
    // 0x759bd0: EnterFrame
    //     0x759bd0: stp             fp, lr, [SP, #-0x10]!
    //     0x759bd4: mov             fp, SP
    // 0x759bd8: AllocStack(0x88)
    //     0x759bd8: sub             SP, SP, #0x88
    // 0x759bdc: CheckStackOverflow
    //     0x759bdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x759be0: cmp             SP, x16
    //     0x759be4: b.ls            #0x759c74
    // 0x759be8: ldr             x16, [fp, #0x10]
    // 0x759bec: str             x16, [SP]
    // 0x759bf0: ldr             x0, [fp, #0x10]
    // 0x759bf4: ClosureCall
    //     0x759bf4: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x759bf8: ldur            x2, [x0, #0x1f]
    //     0x759bfc: blr             x2
    // 0x759c00: b               #0x759c68
    // 0x759c04: sub             SP, fp, #0x88
    // 0x759c08: mov             x2, x0
    // 0x759c0c: stur            x0, [fp, #-0x70]
    // 0x759c10: mov             x0, x1
    // 0x759c14: stur            x1, [fp, #-0x78]
    // 0x759c18: r1 = <List<Object>>
    //     0x759c18: ldr             x1, [PP, #0x440]  ; [pp+0x440] TypeArguments: <List<Object>>
    // 0x759c1c: r0 = ErrorDescription()
    //     0x759c1c: bl              #0x64483c  ; AllocateErrorDescriptionStub -> ErrorDescription (size=0x2c)
    // 0x759c20: mov             x1, x0
    // 0x759c24: r2 = "while handling a gesture"
    //     0x759c24: add             x2, PP, #0x30, lsl #12  ; [pp+0x30e38] "while handling a gesture"
    //     0x759c28: ldr             x2, [x2, #0xe38]
    // 0x759c2c: r3 = Instance_DiagnosticLevel
    //     0x759c2c: ldr             x3, [PP, #0x450]  ; [pp+0x450] Obj!DiagnosticLevel@e37021
    // 0x759c30: stur            x0, [fp, #-0x80]
    // 0x759c34: r0 = _ErrorDiagnostic()
    //     0x759c34: bl              #0x644784  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::_ErrorDiagnostic
    // 0x759c38: r0 = FlutterErrorDetails()
    //     0x759c38: bl              #0x644b40  ; AllocateFlutterErrorDetailsStub -> FlutterErrorDetails (size=0x1c)
    // 0x759c3c: mov             x1, x0
    // 0x759c40: ldur            x0, [fp, #-0x70]
    // 0x759c44: StoreField: r1->field_7 = r0
    //     0x759c44: stur            w0, [x1, #7]
    // 0x759c48: ldur            x0, [fp, #-0x78]
    // 0x759c4c: StoreField: r1->field_b = r0
    //     0x759c4c: stur            w0, [x1, #0xb]
    // 0x759c50: ldur            x0, [fp, #-0x80]
    // 0x759c54: StoreField: r1->field_f = r0
    //     0x759c54: stur            w0, [x1, #0xf]
    // 0x759c58: r0 = false
    //     0x759c58: add             x0, NULL, #0x30  ; false
    // 0x759c5c: ArrayStore: r1[0] = r0  ; List_4
    //     0x759c5c: stur            w0, [x1, #0x17]
    // 0x759c60: r0 = reportError()
    //     0x759c60: bl              #0x63f6cc  ; [package:flutter/src/foundation/assertions.dart] FlutterError::reportError
    // 0x759c64: r0 = Null
    //     0x759c64: mov             x0, NULL
    // 0x759c68: LeaveFrame
    //     0x759c68: mov             SP, fp
    //     0x759c6c: ldp             fp, lr, [SP], #0x10
    // 0x759c70: ret
    //     0x759c70: ret             
    // 0x759c74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x759c74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x759c78: b               #0x759be8
  }
  _ getKindForPointer(/* No info */) {
    // ** addr: 0x75a0b4, size: 0x7c
    // 0x75a0b4: EnterFrame
    //     0x75a0b4: stp             fp, lr, [SP, #-0x10]!
    //     0x75a0b8: mov             fp, SP
    // 0x75a0bc: AllocStack(0x8)
    //     0x75a0bc: sub             SP, SP, #8
    // 0x75a0c0: CheckStackOverflow
    //     0x75a0c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75a0c4: cmp             SP, x16
    //     0x75a0c8: b.ls            #0x75a124
    // 0x75a0cc: LoadField: r3 = r1->field_13
    //     0x75a0cc: ldur            w3, [x1, #0x13]
    // 0x75a0d0: DecompressPointer r3
    //     0x75a0d0: add             x3, x3, HEAP, lsl #32
    // 0x75a0d4: stur            x3, [fp, #-8]
    // 0x75a0d8: r0 = BoxInt64Instr(r2)
    //     0x75a0d8: sbfiz           x0, x2, #1, #0x1f
    //     0x75a0dc: cmp             x2, x0, asr #1
    //     0x75a0e0: b.eq            #0x75a0ec
    //     0x75a0e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x75a0e8: stur            x2, [x0, #7]
    // 0x75a0ec: mov             x1, x3
    // 0x75a0f0: mov             x2, x0
    // 0x75a0f4: r0 = _getValueOrData()
    //     0x75a0f4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x75a0f8: ldur            x1, [fp, #-8]
    // 0x75a0fc: LoadField: r2 = r1->field_f
    //     0x75a0fc: ldur            w2, [x1, #0xf]
    // 0x75a100: DecompressPointer r2
    //     0x75a100: add             x2, x2, HEAP, lsl #32
    // 0x75a104: cmp             w2, w0
    // 0x75a108: b.ne            #0x75a110
    // 0x75a10c: r0 = Null
    //     0x75a10c: mov             x0, NULL
    // 0x75a110: cmp             w0, NULL
    // 0x75a114: b.eq            #0x75a12c
    // 0x75a118: LeaveFrame
    //     0x75a118: mov             SP, fp
    //     0x75a11c: ldp             fp, lr, [SP], #0x10
    // 0x75a120: ret
    //     0x75a120: ret             
    // 0x75a124: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75a124: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75a128: b               #0x75a0cc
    // 0x75a12c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75a12c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ isPointerPanZoomAllowed(/* No info */) {
    // ** addr: 0x800700, size: 0x94
    // 0x800700: EnterFrame
    //     0x800700: stp             fp, lr, [SP, #-0x10]!
    //     0x800704: mov             fp, SP
    // 0x800708: AllocStack(0x8)
    //     0x800708: sub             SP, SP, #8
    // 0x80070c: SetupParameters(GestureRecognizer this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x80070c: mov             x0, x1
    //     0x800710: mov             x1, x2
    // 0x800714: CheckStackOverflow
    //     0x800714: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x800718: cmp             SP, x16
    //     0x80071c: b.ls            #0x80078c
    // 0x800720: LoadField: r2 = r0->field_b
    //     0x800720: ldur            w2, [x0, #0xb]
    // 0x800724: DecompressPointer r2
    //     0x800724: add             x2, x2, HEAP, lsl #32
    // 0x800728: stur            x2, [fp, #-8]
    // 0x80072c: cmp             w2, NULL
    // 0x800730: b.ne            #0x80073c
    // 0x800734: r0 = true
    //     0x800734: add             x0, NULL, #0x20  ; true
    // 0x800738: b               #0x800780
    // 0x80073c: r0 = LoadClassIdInstr(r1)
    //     0x80073c: ldur            x0, [x1, #-1]
    //     0x800740: ubfx            x0, x0, #0xc, #0x14
    // 0x800744: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x800744: movz            x17, #0x30b7
    //     0x800748: movk            x17, #0x1, lsl #16
    //     0x80074c: add             lr, x0, x17
    //     0x800750: ldr             lr, [x21, lr, lsl #3]
    //     0x800754: blr             lr
    // 0x800758: ldur            x1, [fp, #-8]
    // 0x80075c: r2 = LoadClassIdInstr(r1)
    //     0x80075c: ldur            x2, [x1, #-1]
    //     0x800760: ubfx            x2, x2, #0xc, #0x14
    // 0x800764: mov             x16, x0
    // 0x800768: mov             x0, x2
    // 0x80076c: mov             x2, x16
    // 0x800770: r0 = GDT[cid_x0 + 0xf20c]()
    //     0x800770: movz            x17, #0xf20c
    //     0x800774: add             lr, x0, x17
    //     0x800778: ldr             lr, [x21, lr, lsl #3]
    //     0x80077c: blr             lr
    // 0x800780: LeaveFrame
    //     0x800780: mov             SP, fp
    //     0x800784: ldp             fp, lr, [SP], #0x10
    // 0x800788: ret
    //     0x800788: ret             
    // 0x80078c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80078c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x800790: b               #0x800720
  }
  _ addPointer(/* No info */) {
    // ** addr: 0x802d08, size: 0x120
    // 0x802d08: EnterFrame
    //     0x802d08: stp             fp, lr, [SP, #-0x10]!
    //     0x802d0c: mov             fp, SP
    // 0x802d10: AllocStack(0x20)
    //     0x802d10: sub             SP, SP, #0x20
    // 0x802d14: SetupParameters(GestureRecognizer this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x802d14: mov             x3, x1
    //     0x802d18: stur            x1, [fp, #-0x10]
    //     0x802d1c: stur            x2, [fp, #-0x18]
    // 0x802d20: CheckStackOverflow
    //     0x802d20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x802d24: cmp             SP, x16
    //     0x802d28: b.ls            #0x802e20
    // 0x802d2c: LoadField: r4 = r3->field_13
    //     0x802d2c: ldur            w4, [x3, #0x13]
    // 0x802d30: DecompressPointer r4
    //     0x802d30: add             x4, x4, HEAP, lsl #32
    // 0x802d34: stur            x4, [fp, #-8]
    // 0x802d38: r0 = LoadClassIdInstr(r2)
    //     0x802d38: ldur            x0, [x2, #-1]
    //     0x802d3c: ubfx            x0, x0, #0xc, #0x14
    // 0x802d40: mov             x1, x2
    // 0x802d44: r0 = GDT[cid_x0 + -0x1000]()
    //     0x802d44: sub             lr, x0, #1, lsl #12
    //     0x802d48: ldr             lr, [x21, lr, lsl #3]
    //     0x802d4c: blr             lr
    // 0x802d50: mov             x3, x0
    // 0x802d54: ldur            x2, [fp, #-0x18]
    // 0x802d58: stur            x3, [fp, #-0x20]
    // 0x802d5c: r0 = LoadClassIdInstr(r2)
    //     0x802d5c: ldur            x0, [x2, #-1]
    //     0x802d60: ubfx            x0, x0, #0xc, #0x14
    // 0x802d64: mov             x1, x2
    // 0x802d68: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x802d68: movz            x17, #0x30b7
    //     0x802d6c: movk            x17, #0x1, lsl #16
    //     0x802d70: add             lr, x0, x17
    //     0x802d74: ldr             lr, [x21, lr, lsl #3]
    //     0x802d78: blr             lr
    // 0x802d7c: mov             x3, x0
    // 0x802d80: ldur            x2, [fp, #-0x20]
    // 0x802d84: r0 = BoxInt64Instr(r2)
    //     0x802d84: sbfiz           x0, x2, #1, #0x1f
    //     0x802d88: cmp             x2, x0, asr #1
    //     0x802d8c: b.eq            #0x802d98
    //     0x802d90: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x802d94: stur            x2, [x0, #7]
    // 0x802d98: ldur            x1, [fp, #-8]
    // 0x802d9c: mov             x2, x0
    // 0x802da0: r0 = []=()
    //     0x802da0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x802da4: ldur            x3, [fp, #-0x10]
    // 0x802da8: r0 = LoadClassIdInstr(r3)
    //     0x802da8: ldur            x0, [x3, #-1]
    //     0x802dac: ubfx            x0, x0, #0xc, #0x14
    // 0x802db0: mov             x1, x3
    // 0x802db4: ldur            x2, [fp, #-0x18]
    // 0x802db8: r0 = GDT[cid_x0 + 0xe954]()
    //     0x802db8: movz            x17, #0xe954
    //     0x802dbc: add             lr, x0, x17
    //     0x802dc0: ldr             lr, [x21, lr, lsl #3]
    //     0x802dc4: blr             lr
    // 0x802dc8: tbnz            w0, #4, #0x802df0
    // 0x802dcc: ldur            x1, [fp, #-0x10]
    // 0x802dd0: r0 = LoadClassIdInstr(r1)
    //     0x802dd0: ldur            x0, [x1, #-1]
    //     0x802dd4: ubfx            x0, x0, #0xc, #0x14
    // 0x802dd8: ldur            x2, [fp, #-0x18]
    // 0x802ddc: r0 = GDT[cid_x0 + 0xf0b7]()
    //     0x802ddc: movz            x17, #0xf0b7
    //     0x802de0: add             lr, x0, x17
    //     0x802de4: ldr             lr, [x21, lr, lsl #3]
    //     0x802de8: blr             lr
    // 0x802dec: b               #0x802e10
    // 0x802df0: ldur            x1, [fp, #-0x10]
    // 0x802df4: r0 = LoadClassIdInstr(r1)
    //     0x802df4: ldur            x0, [x1, #-1]
    //     0x802df8: ubfx            x0, x0, #0xc, #0x14
    // 0x802dfc: ldur            x2, [fp, #-0x18]
    // 0x802e00: r0 = GDT[cid_x0 + 0xfe8f]()
    //     0x802e00: movz            x17, #0xfe8f
    //     0x802e04: add             lr, x0, x17
    //     0x802e08: ldr             lr, [x21, lr, lsl #3]
    //     0x802e0c: blr             lr
    // 0x802e10: r0 = Null
    //     0x802e10: mov             x0, NULL
    // 0x802e14: LeaveFrame
    //     0x802e14: mov             SP, fp
    //     0x802e18: ldp             fp, lr, [SP], #0x10
    // 0x802e1c: ret
    //     0x802e1c: ret             
    // 0x802e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x802e20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x802e24: b               #0x802d2c
  }
  _ isPointerAllowed(/* No info */) {
    // ** addr: 0x852e08, size: 0x108
    // 0x852e08: EnterFrame
    //     0x852e08: stp             fp, lr, [SP, #-0x10]!
    //     0x852e0c: mov             fp, SP
    // 0x852e10: AllocStack(0x28)
    //     0x852e10: sub             SP, SP, #0x28
    // 0x852e14: SetupParameters(GestureRecognizer this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x852e14: mov             x3, x1
    //     0x852e18: stur            x1, [fp, #-0x10]
    //     0x852e1c: stur            x2, [fp, #-0x18]
    // 0x852e20: CheckStackOverflow
    //     0x852e20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x852e24: cmp             SP, x16
    //     0x852e28: b.ls            #0x852f08
    // 0x852e2c: LoadField: r4 = r3->field_b
    //     0x852e2c: ldur            w4, [x3, #0xb]
    // 0x852e30: DecompressPointer r4
    //     0x852e30: add             x4, x4, HEAP, lsl #32
    // 0x852e34: stur            x4, [fp, #-8]
    // 0x852e38: cmp             w4, NULL
    // 0x852e3c: b.ne            #0x852e4c
    // 0x852e40: mov             x1, x2
    // 0x852e44: mov             x2, x3
    // 0x852e48: b               #0x852ea0
    // 0x852e4c: r0 = LoadClassIdInstr(r2)
    //     0x852e4c: ldur            x0, [x2, #-1]
    //     0x852e50: ubfx            x0, x0, #0xc, #0x14
    // 0x852e54: mov             x1, x2
    // 0x852e58: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x852e58: movz            x17, #0x30b7
    //     0x852e5c: movk            x17, #0x1, lsl #16
    //     0x852e60: add             lr, x0, x17
    //     0x852e64: ldr             lr, [x21, lr, lsl #3]
    //     0x852e68: blr             lr
    // 0x852e6c: ldur            x1, [fp, #-8]
    // 0x852e70: r2 = LoadClassIdInstr(r1)
    //     0x852e70: ldur            x2, [x1, #-1]
    //     0x852e74: ubfx            x2, x2, #0xc, #0x14
    // 0x852e78: mov             x16, x0
    // 0x852e7c: mov             x0, x2
    // 0x852e80: mov             x2, x16
    // 0x852e84: r0 = GDT[cid_x0 + 0xf20c]()
    //     0x852e84: movz            x17, #0xf20c
    //     0x852e88: add             lr, x0, x17
    //     0x852e8c: ldr             lr, [x21, lr, lsl #3]
    //     0x852e90: blr             lr
    // 0x852e94: tbnz            w0, #4, #0x852ef8
    // 0x852e98: ldur            x2, [fp, #-0x10]
    // 0x852e9c: ldur            x1, [fp, #-0x18]
    // 0x852ea0: r0 = LoadClassIdInstr(r1)
    //     0x852ea0: ldur            x0, [x1, #-1]
    //     0x852ea4: ubfx            x0, x0, #0xc, #0x14
    // 0x852ea8: r0 = GDT[cid_x0 + 0x12cee]()
    //     0x852ea8: movz            x17, #0x2cee
    //     0x852eac: movk            x17, #0x1, lsl #16
    //     0x852eb0: add             lr, x0, x17
    //     0x852eb4: ldr             lr, [x21, lr, lsl #3]
    //     0x852eb8: blr             lr
    // 0x852ebc: mov             x2, x0
    // 0x852ec0: ldur            x0, [fp, #-0x10]
    // 0x852ec4: LoadField: r3 = r0->field_f
    //     0x852ec4: ldur            w3, [x0, #0xf]
    // 0x852ec8: DecompressPointer r3
    //     0x852ec8: add             x3, x3, HEAP, lsl #32
    // 0x852ecc: r0 = BoxInt64Instr(r2)
    //     0x852ecc: sbfiz           x0, x2, #1, #0x1f
    //     0x852ed0: cmp             x2, x0, asr #1
    //     0x852ed4: b.eq            #0x852ee0
    //     0x852ed8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x852edc: stur            x2, [x0, #7]
    // 0x852ee0: stp             x0, x3, [SP]
    // 0x852ee4: mov             x0, x3
    // 0x852ee8: ClosureCall
    //     0x852ee8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x852eec: ldur            x2, [x0, #0x1f]
    //     0x852ef0: blr             x2
    // 0x852ef4: b               #0x852efc
    // 0x852ef8: r0 = false
    //     0x852ef8: add             x0, NULL, #0x30  ; false
    // 0x852efc: LeaveFrame
    //     0x852efc: mov             SP, fp
    //     0x852f00: ldp             fp, lr, [SP], #0x10
    // 0x852f04: ret
    //     0x852f04: ret             
    // 0x852f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x852f08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x852f0c: b               #0x852e2c
  }
}

// class id: 3531, size: 0x24, field offset: 0x18
abstract class OneSequenceGestureRecognizer extends GestureRecognizer {

  _ stopTrackingPointer(/* No info */) {
    // ** addr: 0x7587c8, size: 0x108
    // 0x7587c8: EnterFrame
    //     0x7587c8: stp             fp, lr, [SP, #-0x10]!
    //     0x7587cc: mov             fp, SP
    // 0x7587d0: AllocStack(0x30)
    //     0x7587d0: sub             SP, SP, #0x30
    // 0x7587d4: SetupParameters(OneSequenceGestureRecognizer this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x7587d4: mov             x3, x1
    //     0x7587d8: mov             x0, x2
    //     0x7587dc: stur            x1, [fp, #-0x10]
    //     0x7587e0: stur            x2, [fp, #-0x18]
    // 0x7587e4: CheckStackOverflow
    //     0x7587e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7587e8: cmp             SP, x16
    //     0x7587ec: b.ls            #0x7588c4
    // 0x7587f0: LoadField: r4 = r3->field_1b
    //     0x7587f0: ldur            w4, [x3, #0x1b]
    // 0x7587f4: DecompressPointer r4
    //     0x7587f4: add             x4, x4, HEAP, lsl #32
    // 0x7587f8: mov             x1, x4
    // 0x7587fc: mov             x2, x0
    // 0x758800: stur            x4, [fp, #-8]
    // 0x758804: r0 = contains()
    //     0x758804: bl              #0x7cd538  ; [dart:collection] _HashSet::contains
    // 0x758808: tbnz            w0, #4, #0x7588b4
    // 0x75880c: ldur            x1, [fp, #-0x10]
    // 0x758810: ldur            x2, [fp, #-0x18]
    // 0x758814: ldur            x3, [fp, #-8]
    // 0x758818: r0 = LoadStaticField(0x968)
    //     0x758818: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x75881c: ldr             x0, [x0, #0x12d0]
    // 0x758820: cmp             w0, NULL
    // 0x758824: b.eq            #0x7588cc
    // 0x758828: LoadField: r4 = r0->field_13
    //     0x758828: ldur            w4, [x0, #0x13]
    // 0x75882c: DecompressPointer r4
    //     0x75882c: add             x4, x4, HEAP, lsl #32
    // 0x758830: stur            x4, [fp, #-0x20]
    // 0x758834: r0 = LoadClassIdInstr(r1)
    //     0x758834: ldur            x0, [x1, #-1]
    //     0x758838: ubfx            x0, x0, #0xc, #0x14
    // 0x75883c: str             x1, [SP]
    // 0x758840: r0 = GDT[cid_x0 + 0x119bd]()
    //     0x758840: movz            x17, #0x19bd
    //     0x758844: movk            x17, #0x1, lsl #16
    //     0x758848: add             lr, x0, x17
    //     0x75884c: ldr             lr, [x21, lr, lsl #3]
    //     0x758850: blr             lr
    // 0x758854: mov             x1, x0
    // 0x758858: ldur            x0, [fp, #-0x18]
    // 0x75885c: r4 = LoadInt32Instr(r0)
    //     0x75885c: sbfx            x4, x0, #1, #0x1f
    //     0x758860: tbz             w0, #0, #0x758868
    //     0x758864: ldur            x4, [x0, #7]
    // 0x758868: mov             x3, x1
    // 0x75886c: ldur            x1, [fp, #-0x20]
    // 0x758870: mov             x2, x4
    // 0x758874: stur            x4, [fp, #-0x28]
    // 0x758878: r0 = removeRoute()
    //     0x758878: bl              #0x758954  ; [package:flutter/src/gestures/pointer_router.dart] PointerRouter::removeRoute
    // 0x75887c: ldur            x1, [fp, #-8]
    // 0x758880: ldur            x2, [fp, #-0x18]
    // 0x758884: r0 = remove()
    //     0x758884: bl              #0xd3cb58  ; [dart:collection] _HashSet::remove
    // 0x758888: ldur            x0, [fp, #-8]
    // 0x75888c: LoadField: r1 = r0->field_f
    //     0x75888c: ldur            x1, [x0, #0xf]
    // 0x758890: cbnz            x1, #0x7588b4
    // 0x758894: ldur            x1, [fp, #-0x10]
    // 0x758898: r0 = LoadClassIdInstr(r1)
    //     0x758898: ldur            x0, [x1, #-1]
    //     0x75889c: ubfx            x0, x0, #0xc, #0x14
    // 0x7588a0: ldur            x2, [fp, #-0x28]
    // 0x7588a4: r0 = GDT[cid_x0 + 0xfcd6]()
    //     0x7588a4: movz            x17, #0xfcd6
    //     0x7588a8: add             lr, x0, x17
    //     0x7588ac: ldr             lr, [x21, lr, lsl #3]
    //     0x7588b0: blr             lr
    // 0x7588b4: r0 = Null
    //     0x7588b4: mov             x0, NULL
    // 0x7588b8: LeaveFrame
    //     0x7588b8: mov             SP, fp
    //     0x7588bc: ldp             fp, lr, [SP], #0x10
    // 0x7588c0: ret
    //     0x7588c0: ret             
    // 0x7588c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7588c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7588c8: b               #0x7587f0
    // 0x7588cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7588cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void stopTrackingPointer(dynamic, int) {
    // ** addr: 0x7588d0, size: 0x3c
    // 0x7588d0: EnterFrame
    //     0x7588d0: stp             fp, lr, [SP, #-0x10]!
    //     0x7588d4: mov             fp, SP
    // 0x7588d8: ldr             x0, [fp, #0x18]
    // 0x7588dc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7588dc: ldur            w1, [x0, #0x17]
    // 0x7588e0: DecompressPointer r1
    //     0x7588e0: add             x1, x1, HEAP, lsl #32
    // 0x7588e4: CheckStackOverflow
    //     0x7588e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7588e8: cmp             SP, x16
    //     0x7588ec: b.ls            #0x758904
    // 0x7588f0: ldr             x2, [fp, #0x10]
    // 0x7588f4: r0 = stopTrackingPointer()
    //     0x7588f4: bl              #0x7587c8  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::stopTrackingPointer
    // 0x7588f8: LeaveFrame
    //     0x7588f8: mov             SP, fp
    //     0x7588fc: ldp             fp, lr, [SP], #0x10
    // 0x758900: ret
    //     0x758900: ret             
    // 0x758904: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x758904: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x758908: b               #0x7588f0
  }
  _ resolvePointer(/* No info */) {
    // ** addr: 0x75b120, size: 0xac
    // 0x75b120: EnterFrame
    //     0x75b120: stp             fp, lr, [SP, #-0x10]!
    //     0x75b124: mov             fp, SP
    // 0x75b128: AllocStack(0x18)
    //     0x75b128: sub             SP, SP, #0x18
    // 0x75b12c: CheckStackOverflow
    //     0x75b12c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75b130: cmp             SP, x16
    //     0x75b134: b.ls            #0x75b1c4
    // 0x75b138: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x75b138: ldur            w3, [x1, #0x17]
    // 0x75b13c: DecompressPointer r3
    //     0x75b13c: add             x3, x3, HEAP, lsl #32
    // 0x75b140: stur            x3, [fp, #-0x10]
    // 0x75b144: r0 = BoxInt64Instr(r2)
    //     0x75b144: sbfiz           x0, x2, #1, #0x1f
    //     0x75b148: cmp             x2, x0, asr #1
    //     0x75b14c: b.eq            #0x75b158
    //     0x75b150: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x75b154: stur            x2, [x0, #7]
    // 0x75b158: mov             x1, x3
    // 0x75b15c: mov             x2, x0
    // 0x75b160: stur            x0, [fp, #-8]
    // 0x75b164: r0 = _getValueOrData()
    //     0x75b164: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x75b168: ldur            x1, [fp, #-0x10]
    // 0x75b16c: LoadField: r2 = r1->field_f
    //     0x75b16c: ldur            w2, [x1, #0xf]
    // 0x75b170: DecompressPointer r2
    //     0x75b170: add             x2, x2, HEAP, lsl #32
    // 0x75b174: cmp             w2, w0
    // 0x75b178: b.ne            #0x75b180
    // 0x75b17c: r0 = Null
    //     0x75b17c: mov             x0, NULL
    // 0x75b180: stur            x0, [fp, #-0x18]
    // 0x75b184: cmp             w0, NULL
    // 0x75b188: b.eq            #0x75b1b4
    // 0x75b18c: ldur            x2, [fp, #-8]
    // 0x75b190: r0 = remove()
    //     0x75b190: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x75b194: ldur            x1, [fp, #-0x18]
    // 0x75b198: r0 = LoadClassIdInstr(r1)
    //     0x75b198: ldur            x0, [x1, #-1]
    //     0x75b19c: ubfx            x0, x0, #0xc, #0x14
    // 0x75b1a0: r2 = Instance_GestureDisposition
    //     0x75b1a0: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0x75b1a4: ldr             x2, [x2, #0xde8]
    // 0x75b1a8: r0 = GDT[cid_x0 + -0xffc]()
    //     0x75b1a8: sub             lr, x0, #0xffc
    //     0x75b1ac: ldr             lr, [x21, lr, lsl #3]
    //     0x75b1b0: blr             lr
    // 0x75b1b4: r0 = Null
    //     0x75b1b4: mov             x0, NULL
    // 0x75b1b8: LeaveFrame
    //     0x75b1b8: mov             SP, fp
    //     0x75b1bc: ldp             fp, lr, [SP], #0x10
    // 0x75b1c0: ret
    //     0x75b1c0: ret             
    // 0x75b1c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75b1c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75b1c8: b               #0x75b138
  }
  _ stopTrackingIfPointerNoLongerDown(/* No info */) {
    // ** addr: 0x75b718, size: 0x160
    // 0x75b718: EnterFrame
    //     0x75b718: stp             fp, lr, [SP, #-0x10]!
    //     0x75b71c: mov             fp, SP
    // 0x75b720: AllocStack(0x10)
    //     0x75b720: sub             SP, SP, #0x10
    // 0x75b724: SetupParameters(OneSequenceGestureRecognizer this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x75b724: mov             x4, x1
    //     0x75b728: mov             x3, x2
    //     0x75b72c: stur            x1, [fp, #-8]
    //     0x75b730: stur            x2, [fp, #-0x10]
    // 0x75b734: CheckStackOverflow
    //     0x75b734: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75b738: cmp             SP, x16
    //     0x75b73c: b.ls            #0x75b870
    // 0x75b740: mov             x0, x3
    // 0x75b744: r2 = Null
    //     0x75b744: mov             x2, NULL
    // 0x75b748: r1 = Null
    //     0x75b748: mov             x1, NULL
    // 0x75b74c: cmp             w0, NULL
    // 0x75b750: b.eq            #0x75b770
    // 0x75b754: branchIfSmi(r0, 0x75b770)
    //     0x75b754: tbz             w0, #0, #0x75b770
    // 0x75b758: r3 = LoadClassIdInstr(r0)
    //     0x75b758: ldur            x3, [x0, #-1]
    //     0x75b75c: ubfx            x3, x3, #0xc, #0x14
    // 0x75b760: cmp             x3, #0xda3
    // 0x75b764: b.eq            #0x75b778
    // 0x75b768: cmp             x3, #0xfcd
    // 0x75b76c: b.eq            #0x75b778
    // 0x75b770: r0 = false
    //     0x75b770: add             x0, NULL, #0x30  ; false
    // 0x75b774: b               #0x75b77c
    // 0x75b778: r0 = true
    //     0x75b778: add             x0, NULL, #0x20  ; true
    // 0x75b77c: tbz             w0, #4, #0x75b800
    // 0x75b780: ldur            x0, [fp, #-0x10]
    // 0x75b784: r2 = Null
    //     0x75b784: mov             x2, NULL
    // 0x75b788: r1 = Null
    //     0x75b788: mov             x1, NULL
    // 0x75b78c: cmp             w0, NULL
    // 0x75b790: b.eq            #0x75b7b0
    // 0x75b794: branchIfSmi(r0, 0x75b7b0)
    //     0x75b794: tbz             w0, #0, #0x75b7b0
    // 0x75b798: r3 = LoadClassIdInstr(r0)
    //     0x75b798: ldur            x3, [x0, #-1]
    //     0x75b79c: ubfx            x3, x3, #0xc, #0x14
    // 0x75b7a0: cmp             x3, #0xd93
    // 0x75b7a4: b.eq            #0x75b7b8
    // 0x75b7a8: cmp             x3, #0xfc5
    // 0x75b7ac: b.eq            #0x75b7b8
    // 0x75b7b0: r0 = false
    //     0x75b7b0: add             x0, NULL, #0x30  ; false
    // 0x75b7b4: b               #0x75b7bc
    // 0x75b7b8: r0 = true
    //     0x75b7b8: add             x0, NULL, #0x20  ; true
    // 0x75b7bc: tbz             w0, #4, #0x75b800
    // 0x75b7c0: ldur            x0, [fp, #-0x10]
    // 0x75b7c4: r2 = Null
    //     0x75b7c4: mov             x2, NULL
    // 0x75b7c8: r1 = Null
    //     0x75b7c8: mov             x1, NULL
    // 0x75b7cc: cmp             w0, NULL
    // 0x75b7d0: b.eq            #0x75b7f0
    // 0x75b7d4: branchIfSmi(r0, 0x75b7f0)
    //     0x75b7d4: tbz             w0, #0, #0x75b7f0
    // 0x75b7d8: r3 = LoadClassIdInstr(r0)
    //     0x75b7d8: ldur            x3, [x0, #-1]
    //     0x75b7dc: ubfx            x3, x3, #0xc, #0x14
    // 0x75b7e0: cmp             x3, #0xd95
    // 0x75b7e4: b.eq            #0x75b7f8
    // 0x75b7e8: cmp             x3, #0xfc7
    // 0x75b7ec: b.eq            #0x75b7f8
    // 0x75b7f0: r0 = false
    //     0x75b7f0: add             x0, NULL, #0x30  ; false
    // 0x75b7f4: b               #0x75b7fc
    // 0x75b7f8: r0 = true
    //     0x75b7f8: add             x0, NULL, #0x20  ; true
    // 0x75b7fc: tbnz            w0, #4, #0x75b860
    // 0x75b800: ldur            x2, [fp, #-8]
    // 0x75b804: ldur            x1, [fp, #-0x10]
    // 0x75b808: r0 = LoadClassIdInstr(r1)
    //     0x75b808: ldur            x0, [x1, #-1]
    //     0x75b80c: ubfx            x0, x0, #0xc, #0x14
    // 0x75b810: r0 = GDT[cid_x0 + -0x1000]()
    //     0x75b810: sub             lr, x0, #1, lsl #12
    //     0x75b814: ldr             lr, [x21, lr, lsl #3]
    //     0x75b818: blr             lr
    // 0x75b81c: mov             x2, x0
    // 0x75b820: r0 = BoxInt64Instr(r2)
    //     0x75b820: sbfiz           x0, x2, #1, #0x1f
    //     0x75b824: cmp             x2, x0, asr #1
    //     0x75b828: b.eq            #0x75b834
    //     0x75b82c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x75b830: stur            x2, [x0, #7]
    // 0x75b834: ldur            x1, [fp, #-8]
    // 0x75b838: r2 = LoadClassIdInstr(r1)
    //     0x75b838: ldur            x2, [x1, #-1]
    //     0x75b83c: ubfx            x2, x2, #0xc, #0x14
    // 0x75b840: mov             x16, x0
    // 0x75b844: mov             x0, x2
    // 0x75b848: mov             x2, x16
    // 0x75b84c: r0 = GDT[cid_x0 + 0x119da]()
    //     0x75b84c: movz            x17, #0x19da
    //     0x75b850: movk            x17, #0x1, lsl #16
    //     0x75b854: add             lr, x0, x17
    //     0x75b858: ldr             lr, [x21, lr, lsl #3]
    //     0x75b85c: blr             lr
    // 0x75b860: r0 = Null
    //     0x75b860: mov             x0, NULL
    // 0x75b864: LeaveFrame
    //     0x75b864: mov             SP, fp
    //     0x75b868: ldp             fp, lr, [SP], #0x10
    // 0x75b86c: ret
    //     0x75b86c: ret             
    // 0x75b870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75b870: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75b874: b               #0x75b740
  }
  _ OneSequenceGestureRecognizer(/* No info */) {
    // ** addr: 0x763058, size: 0x1c8
    // 0x763058: EnterFrame
    //     0x763058: stp             fp, lr, [SP, #-0x10]!
    //     0x76305c: mov             fp, SP
    // 0x763060: AllocStack(0x30)
    //     0x763060: sub             SP, SP, #0x30
    // 0x763064: SetupParameters(OneSequenceGestureRecognizer this /* r1 => r1, fp-0x18 */, {dynamic allowedButtonsFilter = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static. /* r3, fp-0x10 */, dynamic supportedDevices = Null /* r0, fp-0x8 */})
    //     0x763064: stur            x1, [fp, #-0x18]
    //     0x763068: ldur            w0, [x4, #0x13]
    //     0x76306c: ldur            w2, [x4, #0x1f]
    //     0x763070: add             x2, x2, HEAP, lsl #32
    //     0x763074: add             x16, PP, #0x25, lsl #12  ; [pp+0x253d0] "allowedButtonsFilter"
    //     0x763078: ldr             x16, [x16, #0x3d0]
    //     0x76307c: cmp             w2, w16
    //     0x763080: b.ne            #0x7630a4
    //     0x763084: ldur            w2, [x4, #0x23]
    //     0x763088: add             x2, x2, HEAP, lsl #32
    //     0x76308c: sub             w3, w0, w2
    //     0x763090: add             x2, fp, w3, sxtw #2
    //     0x763094: ldr             x2, [x2, #8]
    //     0x763098: mov             x3, x2
    //     0x76309c: movz            x2, #0x1
    //     0x7630a0: b               #0x7630b0
    //     0x7630a4: add             x3, PP, #0x25, lsl #12  ; [pp+0x253d8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static. (0x7e54fb8bbd8c)
    //     0x7630a8: ldr             x3, [x3, #0x3d8]
    //     0x7630ac: movz            x2, #0
    //     0x7630b0: stur            x3, [fp, #-0x10]
    //     0x7630b4: lsl             x5, x2, #1
    //     0x7630b8: lsl             w2, w5, #1
    //     0x7630bc: add             w5, w2, #8
    //     0x7630c0: add             x16, x4, w5, sxtw #1
    //     0x7630c4: ldur            w6, [x16, #0xf]
    //     0x7630c8: add             x6, x6, HEAP, lsl #32
    //     0x7630cc: add             x16, PP, #0x25, lsl #12  ; [pp+0x253e0] "supportedDevices"
    //     0x7630d0: ldr             x16, [x16, #0x3e0]
    //     0x7630d4: cmp             w6, w16
    //     0x7630d8: b.ne            #0x7630fc
    //     0x7630dc: add             w5, w2, #0xa
    //     0x7630e0: add             x16, x4, w5, sxtw #1
    //     0x7630e4: ldur            w2, [x16, #0xf]
    //     0x7630e8: add             x2, x2, HEAP, lsl #32
    //     0x7630ec: sub             w4, w0, w2
    //     0x7630f0: add             x0, fp, w4, sxtw #2
    //     0x7630f4: ldr             x0, [x0, #8]
    //     0x7630f8: b               #0x763100
    //     0x7630fc: mov             x0, NULL
    //     0x763100: stur            x0, [fp, #-8]
    // 0x763104: CheckStackOverflow
    //     0x763104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x763108: cmp             SP, x16
    //     0x76310c: b.ls            #0x763218
    // 0x763110: r16 = <int, GestureArenaEntry>
    //     0x763110: add             x16, PP, #0x25, lsl #12  ; [pp+0x253e8] TypeArguments: <int, GestureArenaEntry>
    //     0x763114: ldr             x16, [x16, #0x3e8]
    // 0x763118: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x76311c: stp             lr, x16, [SP]
    // 0x763120: r0 = Map._fromLiteral()
    //     0x763120: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x763124: ldur            x2, [fp, #-0x18]
    // 0x763128: ArrayStore: r2[0] = r0  ; List_4
    //     0x763128: stur            w0, [x2, #0x17]
    //     0x76312c: ldurb           w16, [x2, #-1]
    //     0x763130: ldurb           w17, [x0, #-1]
    //     0x763134: and             x16, x17, x16, lsr #2
    //     0x763138: tst             x16, HEAP, lsr #32
    //     0x76313c: b.eq            #0x763144
    //     0x763140: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x763144: r1 = <int>
    //     0x763144: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x763148: r0 = _HashSet()
    //     0x763148: bl              #0x653810  ; Allocate_HashSetStub -> _HashSet<X0> (size=0x20)
    // 0x76314c: stur            x0, [fp, #-0x20]
    // 0x763150: StoreField: r0->field_f = rZR
    //     0x763150: stur            xzr, [x0, #0xf]
    // 0x763154: ArrayStore: r0[0] = rZR  ; List_8
    //     0x763154: stur            xzr, [x0, #0x17]
    // 0x763158: r1 = <_HashSetEntry<int>?>
    //     0x763158: add             x1, PP, #0x25, lsl #12  ; [pp+0x253f0] TypeArguments: <_HashSetEntry<int>?>
    //     0x76315c: ldr             x1, [x1, #0x3f0]
    // 0x763160: r2 = 16
    //     0x763160: movz            x2, #0x10
    // 0x763164: r0 = AllocateArray()
    //     0x763164: bl              #0xec22fc  ; AllocateArrayStub
    // 0x763168: mov             x1, x0
    // 0x76316c: ldur            x0, [fp, #-0x20]
    // 0x763170: StoreField: r0->field_b = r1
    //     0x763170: stur            w1, [x0, #0xb]
    // 0x763174: ldur            x1, [fp, #-0x18]
    // 0x763178: StoreField: r1->field_1b = r0
    //     0x763178: stur            w0, [x1, #0x1b]
    //     0x76317c: ldurb           w16, [x1, #-1]
    //     0x763180: ldurb           w17, [x0, #-1]
    //     0x763184: and             x16, x17, x16, lsr #2
    //     0x763188: tst             x16, HEAP, lsr #32
    //     0x76318c: b.eq            #0x763194
    //     0x763190: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x763194: r16 = <int, PointerDeviceKind>
    //     0x763194: add             x16, PP, #0x25, lsl #12  ; [pp+0x253f8] TypeArguments: <int, PointerDeviceKind>
    //     0x763198: ldr             x16, [x16, #0x3f8]
    // 0x76319c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x7631a0: stp             lr, x16, [SP]
    // 0x7631a4: r0 = Map._fromLiteral()
    //     0x7631a4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7631a8: ldur            x1, [fp, #-0x18]
    // 0x7631ac: StoreField: r1->field_13 = r0
    //     0x7631ac: stur            w0, [x1, #0x13]
    //     0x7631b0: ldurb           w16, [x1, #-1]
    //     0x7631b4: ldurb           w17, [x0, #-1]
    //     0x7631b8: and             x16, x17, x16, lsr #2
    //     0x7631bc: tst             x16, HEAP, lsr #32
    //     0x7631c0: b.eq            #0x7631c8
    //     0x7631c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7631c8: ldur            x0, [fp, #-8]
    // 0x7631cc: StoreField: r1->field_b = r0
    //     0x7631cc: stur            w0, [x1, #0xb]
    //     0x7631d0: ldurb           w16, [x1, #-1]
    //     0x7631d4: ldurb           w17, [x0, #-1]
    //     0x7631d8: and             x16, x17, x16, lsr #2
    //     0x7631dc: tst             x16, HEAP, lsr #32
    //     0x7631e0: b.eq            #0x7631e8
    //     0x7631e4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7631e8: ldur            x0, [fp, #-0x10]
    // 0x7631ec: StoreField: r1->field_f = r0
    //     0x7631ec: stur            w0, [x1, #0xf]
    //     0x7631f0: ldurb           w16, [x1, #-1]
    //     0x7631f4: ldurb           w17, [x0, #-1]
    //     0x7631f8: and             x16, x17, x16, lsr #2
    //     0x7631fc: tst             x16, HEAP, lsr #32
    //     0x763200: b.eq            #0x763208
    //     0x763204: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x763208: r0 = Null
    //     0x763208: mov             x0, NULL
    // 0x76320c: LeaveFrame
    //     0x76320c: mov             SP, fp
    //     0x763210: ldp             fp, lr, [SP], #0x10
    // 0x763214: ret
    //     0x763214: ret             
    // 0x763218: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x763218: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76321c: b               #0x763110
  }
  _ resolve(/* No info */) {
    // ** addr: 0x7a9d28, size: 0x1a0
    // 0x7a9d28: EnterFrame
    //     0x7a9d28: stp             fp, lr, [SP, #-0x10]!
    //     0x7a9d2c: mov             fp, SP
    // 0x7a9d30: AllocStack(0x30)
    //     0x7a9d30: sub             SP, SP, #0x30
    // 0x7a9d34: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x7a9d34: mov             x0, x2
    //     0x7a9d38: stur            x2, [fp, #-0x10]
    // 0x7a9d3c: CheckStackOverflow
    //     0x7a9d3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a9d40: cmp             SP, x16
    //     0x7a9d44: b.ls            #0x7a9eb8
    // 0x7a9d48: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x7a9d48: ldur            w4, [x1, #0x17]
    // 0x7a9d4c: DecompressPointer r4
    //     0x7a9d4c: add             x4, x4, HEAP, lsl #32
    // 0x7a9d50: stur            x4, [fp, #-8]
    // 0x7a9d54: LoadField: r2 = r4->field_7
    //     0x7a9d54: ldur            w2, [x4, #7]
    // 0x7a9d58: DecompressPointer r2
    //     0x7a9d58: add             x2, x2, HEAP, lsl #32
    // 0x7a9d5c: r1 = Null
    //     0x7a9d5c: mov             x1, NULL
    // 0x7a9d60: r3 = <X1>
    //     0x7a9d60: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x7a9d64: r0 = Null
    //     0x7a9d64: mov             x0, NULL
    // 0x7a9d68: cmp             x2, x0
    // 0x7a9d6c: b.eq            #0x7a9d7c
    // 0x7a9d70: r30 = InstantiateTypeArgumentsStub
    //     0x7a9d70: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7a9d74: LoadField: r30 = r30->field_7
    //     0x7a9d74: ldur            lr, [lr, #7]
    // 0x7a9d78: blr             lr
    // 0x7a9d7c: mov             x1, x0
    // 0x7a9d80: r0 = _CompactIterable()
    //     0x7a9d80: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x7a9d84: mov             x1, x0
    // 0x7a9d88: ldur            x0, [fp, #-8]
    // 0x7a9d8c: StoreField: r1->field_b = r0
    //     0x7a9d8c: stur            w0, [x1, #0xb]
    // 0x7a9d90: r2 = -1
    //     0x7a9d90: movn            x2, #0
    // 0x7a9d94: StoreField: r1->field_f = r2
    //     0x7a9d94: stur            x2, [x1, #0xf]
    // 0x7a9d98: r2 = 2
    //     0x7a9d98: movz            x2, #0x2
    // 0x7a9d9c: ArrayStore: r1[0] = r2  ; List_8
    //     0x7a9d9c: stur            x2, [x1, #0x17]
    // 0x7a9da0: mov             x2, x1
    // 0x7a9da4: r1 = <GestureArenaEntry>
    //     0x7a9da4: add             x1, PP, #0x30, lsl #12  ; [pp+0x30e18] TypeArguments: <GestureArenaEntry>
    //     0x7a9da8: ldr             x1, [x1, #0xe18]
    // 0x7a9dac: r0 = _GrowableList.of()
    //     0x7a9dac: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x7a9db0: ldur            x1, [fp, #-8]
    // 0x7a9db4: stur            x0, [fp, #-8]
    // 0x7a9db8: r0 = clear()
    //     0x7a9db8: bl              #0x623ad0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x7a9dbc: ldur            x3, [fp, #-8]
    // 0x7a9dc0: LoadField: r4 = r3->field_7
    //     0x7a9dc0: ldur            w4, [x3, #7]
    // 0x7a9dc4: DecompressPointer r4
    //     0x7a9dc4: add             x4, x4, HEAP, lsl #32
    // 0x7a9dc8: stur            x4, [fp, #-0x30]
    // 0x7a9dcc: LoadField: r0 = r3->field_b
    //     0x7a9dcc: ldur            w0, [x3, #0xb]
    // 0x7a9dd0: r5 = LoadInt32Instr(r0)
    //     0x7a9dd0: sbfx            x5, x0, #1, #0x1f
    // 0x7a9dd4: stur            x5, [fp, #-0x28]
    // 0x7a9dd8: r0 = 0
    //     0x7a9dd8: movz            x0, #0
    // 0x7a9ddc: CheckStackOverflow
    //     0x7a9ddc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a9de0: cmp             SP, x16
    //     0x7a9de4: b.ls            #0x7a9ec0
    // 0x7a9de8: LoadField: r1 = r3->field_b
    //     0x7a9de8: ldur            w1, [x3, #0xb]
    // 0x7a9dec: r2 = LoadInt32Instr(r1)
    //     0x7a9dec: sbfx            x2, x1, #1, #0x1f
    // 0x7a9df0: cmp             x5, x2
    // 0x7a9df4: b.ne            #0x7a9e98
    // 0x7a9df8: cmp             x0, x2
    // 0x7a9dfc: b.ge            #0x7a9e88
    // 0x7a9e00: LoadField: r1 = r3->field_f
    //     0x7a9e00: ldur            w1, [x3, #0xf]
    // 0x7a9e04: DecompressPointer r1
    //     0x7a9e04: add             x1, x1, HEAP, lsl #32
    // 0x7a9e08: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0x7a9e08: add             x16, x1, x0, lsl #2
    //     0x7a9e0c: ldur            w6, [x16, #0xf]
    // 0x7a9e10: DecompressPointer r6
    //     0x7a9e10: add             x6, x6, HEAP, lsl #32
    // 0x7a9e14: stur            x6, [fp, #-0x20]
    // 0x7a9e18: add             x7, x0, #1
    // 0x7a9e1c: stur            x7, [fp, #-0x18]
    // 0x7a9e20: cmp             w6, NULL
    // 0x7a9e24: b.ne            #0x7a9e58
    // 0x7a9e28: mov             x0, x6
    // 0x7a9e2c: mov             x2, x4
    // 0x7a9e30: r1 = Null
    //     0x7a9e30: mov             x1, NULL
    // 0x7a9e34: cmp             w2, NULL
    // 0x7a9e38: b.eq            #0x7a9e58
    // 0x7a9e3c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7a9e3c: ldur            w4, [x2, #0x17]
    // 0x7a9e40: DecompressPointer r4
    //     0x7a9e40: add             x4, x4, HEAP, lsl #32
    // 0x7a9e44: r8 = X0
    //     0x7a9e44: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7a9e48: LoadField: r9 = r4->field_7
    //     0x7a9e48: ldur            x9, [x4, #7]
    // 0x7a9e4c: r3 = Null
    //     0x7a9e4c: add             x3, PP, #0x30, lsl #12  ; [pp+0x30e20] Null
    //     0x7a9e50: ldr             x3, [x3, #0xe20]
    // 0x7a9e54: blr             x9
    // 0x7a9e58: ldur            x1, [fp, #-0x20]
    // 0x7a9e5c: r0 = LoadClassIdInstr(r1)
    //     0x7a9e5c: ldur            x0, [x1, #-1]
    //     0x7a9e60: ubfx            x0, x0, #0xc, #0x14
    // 0x7a9e64: ldur            x2, [fp, #-0x10]
    // 0x7a9e68: r0 = GDT[cid_x0 + -0xffc]()
    //     0x7a9e68: sub             lr, x0, #0xffc
    //     0x7a9e6c: ldr             lr, [x21, lr, lsl #3]
    //     0x7a9e70: blr             lr
    // 0x7a9e74: ldur            x0, [fp, #-0x18]
    // 0x7a9e78: ldur            x3, [fp, #-8]
    // 0x7a9e7c: ldur            x4, [fp, #-0x30]
    // 0x7a9e80: ldur            x5, [fp, #-0x28]
    // 0x7a9e84: b               #0x7a9ddc
    // 0x7a9e88: r0 = Null
    //     0x7a9e88: mov             x0, NULL
    // 0x7a9e8c: LeaveFrame
    //     0x7a9e8c: mov             SP, fp
    //     0x7a9e90: ldp             fp, lr, [SP], #0x10
    // 0x7a9e94: ret
    //     0x7a9e94: ret             
    // 0x7a9e98: mov             x0, x3
    // 0x7a9e9c: r0 = ConcurrentModificationError()
    //     0x7a9e9c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x7a9ea0: mov             x1, x0
    // 0x7a9ea4: ldur            x0, [fp, #-8]
    // 0x7a9ea8: StoreField: r1->field_b = r0
    //     0x7a9ea8: stur            w0, [x1, #0xb]
    // 0x7a9eac: mov             x0, x1
    // 0x7a9eb0: r0 = Throw()
    //     0x7a9eb0: bl              #0xec04b8  ; ThrowStub
    // 0x7a9eb4: brk             #0
    // 0x7a9eb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a9eb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a9ebc: b               #0x7a9d48
    // 0x7a9ec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a9ec0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a9ec4: b               #0x7a9de8
  }
  _ startTrackingPointer(/* No info */) {
    // ** addr: 0x7ab0d8, size: 0xf8
    // 0x7ab0d8: EnterFrame
    //     0x7ab0d8: stp             fp, lr, [SP, #-0x10]!
    //     0x7ab0dc: mov             fp, SP
    // 0x7ab0e0: AllocStack(0x28)
    //     0x7ab0e0: sub             SP, SP, #0x28
    // 0x7ab0e4: SetupParameters(OneSequenceGestureRecognizer this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7ab0e4: stur            x1, [fp, #-0x10]
    //     0x7ab0e8: stur            x2, [fp, #-0x18]
    //     0x7ab0ec: stur            x3, [fp, #-0x20]
    // 0x7ab0f0: CheckStackOverflow
    //     0x7ab0f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ab0f4: cmp             SP, x16
    //     0x7ab0f8: b.ls            #0x7ab1c4
    // 0x7ab0fc: r0 = LoadStaticField(0x968)
    //     0x7ab0fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7ab100: ldr             x0, [x0, #0x12d0]
    // 0x7ab104: cmp             w0, NULL
    // 0x7ab108: b.eq            #0x7ab1cc
    // 0x7ab10c: LoadField: r4 = r0->field_13
    //     0x7ab10c: ldur            w4, [x0, #0x13]
    // 0x7ab110: DecompressPointer r4
    //     0x7ab110: add             x4, x4, HEAP, lsl #32
    // 0x7ab114: stur            x4, [fp, #-8]
    // 0x7ab118: r0 = LoadClassIdInstr(r1)
    //     0x7ab118: ldur            x0, [x1, #-1]
    //     0x7ab11c: ubfx            x0, x0, #0xc, #0x14
    // 0x7ab120: str             x1, [SP]
    // 0x7ab124: r0 = GDT[cid_x0 + 0x119bd]()
    //     0x7ab124: movz            x17, #0x19bd
    //     0x7ab128: movk            x17, #0x1, lsl #16
    //     0x7ab12c: add             lr, x0, x17
    //     0x7ab130: ldr             lr, [x21, lr, lsl #3]
    //     0x7ab134: blr             lr
    // 0x7ab138: ldur            x16, [fp, #-0x20]
    // 0x7ab13c: str             x16, [SP]
    // 0x7ab140: ldur            x1, [fp, #-8]
    // 0x7ab144: ldur            x2, [fp, #-0x18]
    // 0x7ab148: mov             x3, x0
    // 0x7ab14c: r4 = const [0, 0x4, 0x1, 0x4, null]
    //     0x7ab14c: add             x4, PP, #0xe, lsl #12  ; [pp+0xe5a8] List(5) [0, 0x4, 0x1, 0x4, Null]
    //     0x7ab150: ldr             x4, [x4, #0x5a8]
    // 0x7ab154: r0 = addRoute()
    //     0x7ab154: bl              #0x7ab798  ; [package:flutter/src/gestures/pointer_router.dart] PointerRouter::addRoute
    // 0x7ab158: ldur            x3, [fp, #-0x10]
    // 0x7ab15c: LoadField: r2 = r3->field_1b
    //     0x7ab15c: ldur            w2, [x3, #0x1b]
    // 0x7ab160: DecompressPointer r2
    //     0x7ab160: add             x2, x2, HEAP, lsl #32
    // 0x7ab164: ldur            x4, [fp, #-0x18]
    // 0x7ab168: r0 = BoxInt64Instr(r4)
    //     0x7ab168: sbfiz           x0, x4, #1, #0x1f
    //     0x7ab16c: cmp             x4, x0, asr #1
    //     0x7ab170: b.eq            #0x7ab17c
    //     0x7ab174: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7ab178: stur            x4, [x0, #7]
    // 0x7ab17c: mov             x1, x2
    // 0x7ab180: mov             x2, x0
    // 0x7ab184: stur            x0, [fp, #-8]
    // 0x7ab188: r0 = add()
    //     0x7ab188: bl              #0xd5b078  ; [dart:collection] _HashSet::add
    // 0x7ab18c: ldur            x1, [fp, #-0x10]
    // 0x7ab190: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x7ab190: ldur            w0, [x1, #0x17]
    // 0x7ab194: DecompressPointer r0
    //     0x7ab194: add             x0, x0, HEAP, lsl #32
    // 0x7ab198: ldur            x2, [fp, #-0x18]
    // 0x7ab19c: stur            x0, [fp, #-0x20]
    // 0x7ab1a0: r0 = _addPointerToArena()
    //     0x7ab1a0: bl              #0x7ab1d0  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::_addPointerToArena
    // 0x7ab1a4: ldur            x1, [fp, #-0x20]
    // 0x7ab1a8: ldur            x2, [fp, #-8]
    // 0x7ab1ac: mov             x3, x0
    // 0x7ab1b0: r0 = []=()
    //     0x7ab1b0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ab1b4: r0 = Null
    //     0x7ab1b4: mov             x0, NULL
    // 0x7ab1b8: LeaveFrame
    //     0x7ab1b8: mov             SP, fp
    //     0x7ab1bc: ldp             fp, lr, [SP], #0x10
    // 0x7ab1c0: ret
    //     0x7ab1c0: ret             
    // 0x7ab1c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ab1c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ab1c8: b               #0x7ab0fc
    // 0x7ab1cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7ab1cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _addPointerToArena(/* No info */) {
    // ** addr: 0x7ab1d0, size: 0x90
    // 0x7ab1d0: EnterFrame
    //     0x7ab1d0: stp             fp, lr, [SP, #-0x10]!
    //     0x7ab1d4: mov             fp, SP
    // 0x7ab1d8: AllocStack(0x10)
    //     0x7ab1d8: sub             SP, SP, #0x10
    // 0x7ab1dc: SetupParameters(OneSequenceGestureRecognizer this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x7ab1dc: mov             x4, x1
    //     0x7ab1e0: mov             x0, x2
    //     0x7ab1e4: stur            x1, [fp, #-8]
    //     0x7ab1e8: stur            x2, [fp, #-0x10]
    // 0x7ab1ec: CheckStackOverflow
    //     0x7ab1ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ab1f0: cmp             SP, x16
    //     0x7ab1f4: b.ls            #0x7ab254
    // 0x7ab1f8: LoadField: r1 = r4->field_1f
    //     0x7ab1f8: ldur            w1, [x4, #0x1f]
    // 0x7ab1fc: DecompressPointer r1
    //     0x7ab1fc: add             x1, x1, HEAP, lsl #32
    // 0x7ab200: cmp             w1, NULL
    // 0x7ab204: b.ne            #0x7ab210
    // 0x7ab208: r0 = Null
    //     0x7ab208: mov             x0, NULL
    // 0x7ab20c: b               #0x7ab21c
    // 0x7ab210: mov             x2, x0
    // 0x7ab214: mov             x3, x4
    // 0x7ab218: r0 = add()
    //     0x7ab218: bl              #0x7ab45c  ; [package:flutter/src/gestures/team.dart] GestureArenaTeam::add
    // 0x7ab21c: cmp             w0, NULL
    // 0x7ab220: b.ne            #0x7ab248
    // 0x7ab224: r0 = LoadStaticField(0x968)
    //     0x7ab224: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7ab228: ldr             x0, [x0, #0x12d0]
    // 0x7ab22c: cmp             w0, NULL
    // 0x7ab230: b.eq            #0x7ab25c
    // 0x7ab234: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7ab234: ldur            w1, [x0, #0x17]
    // 0x7ab238: DecompressPointer r1
    //     0x7ab238: add             x1, x1, HEAP, lsl #32
    // 0x7ab23c: ldur            x2, [fp, #-0x10]
    // 0x7ab240: ldur            x3, [fp, #-8]
    // 0x7ab244: r0 = add()
    //     0x7ab244: bl              #0x7ab260  ; [package:flutter/src/gestures/arena.dart] GestureArenaManager::add
    // 0x7ab248: LeaveFrame
    //     0x7ab248: mov             SP, fp
    //     0x7ab24c: ldp             fp, lr, [SP], #0x10
    // 0x7ab250: ret
    //     0x7ab250: ret             
    // 0x7ab254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ab254: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ab258: b               #0x7ab1f8
    // 0x7ab25c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7ab25c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ handleNonAllowedPointer(/* No info */) {
    // ** addr: 0x7b1c18, size: 0x50
    // 0x7b1c18: EnterFrame
    //     0x7b1c18: stp             fp, lr, [SP, #-0x10]!
    //     0x7b1c1c: mov             fp, SP
    // 0x7b1c20: CheckStackOverflow
    //     0x7b1c20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b1c24: cmp             SP, x16
    //     0x7b1c28: b.ls            #0x7b1c60
    // 0x7b1c2c: r0 = LoadClassIdInstr(r1)
    //     0x7b1c2c: ldur            x0, [x1, #-1]
    //     0x7b1c30: ubfx            x0, x0, #0xc, #0x14
    // 0x7b1c34: r2 = Instance_GestureDisposition
    //     0x7b1c34: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0x7b1c38: ldr             x2, [x2, #0xde8]
    // 0x7b1c3c: r0 = GDT[cid_x0 + 0x102f4]()
    //     0x7b1c3c: movz            x17, #0x2f4
    //     0x7b1c40: movk            x17, #0x1, lsl #16
    //     0x7b1c44: add             lr, x0, x17
    //     0x7b1c48: ldr             lr, [x21, lr, lsl #3]
    //     0x7b1c4c: blr             lr
    // 0x7b1c50: r0 = Null
    //     0x7b1c50: mov             x0, NULL
    // 0x7b1c54: LeaveFrame
    //     0x7b1c54: mov             SP, fp
    //     0x7b1c58: ldp             fp, lr, [SP], #0x10
    // 0x7b1c5c: ret
    //     0x7b1c5c: ret             
    // 0x7b1c60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b1c60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b1c64: b               #0x7b1c2c
  }
  _ dispose(/* No info */) {
    // ** addr: 0x7f93bc, size: 0x1f8
    // 0x7f93bc: EnterFrame
    //     0x7f93bc: stp             fp, lr, [SP, #-0x10]!
    //     0x7f93c0: mov             fp, SP
    // 0x7f93c4: AllocStack(0x48)
    //     0x7f93c4: sub             SP, SP, #0x48
    // 0x7f93c8: SetupParameters(OneSequenceGestureRecognizer this /* r1 => r3, fp-0x8 */)
    //     0x7f93c8: mov             x3, x1
    //     0x7f93cc: stur            x1, [fp, #-8]
    // 0x7f93d0: CheckStackOverflow
    //     0x7f93d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f93d4: cmp             SP, x16
    //     0x7f93d8: b.ls            #0x7f959c
    // 0x7f93dc: r0 = LoadClassIdInstr(r3)
    //     0x7f93dc: ldur            x0, [x3, #-1]
    //     0x7f93e0: ubfx            x0, x0, #0xc, #0x14
    // 0x7f93e4: mov             x1, x3
    // 0x7f93e8: r2 = Instance_GestureDisposition
    //     0x7f93e8: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0x7f93ec: ldr             x2, [x2, #0xde8]
    // 0x7f93f0: r0 = GDT[cid_x0 + 0x102f4]()
    //     0x7f93f0: movz            x17, #0x2f4
    //     0x7f93f4: movk            x17, #0x1, lsl #16
    //     0x7f93f8: add             lr, x0, x17
    //     0x7f93fc: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9400: blr             lr
    // 0x7f9404: ldur            x0, [fp, #-8]
    // 0x7f9408: LoadField: r2 = r0->field_1b
    //     0x7f9408: ldur            w2, [x0, #0x1b]
    // 0x7f940c: DecompressPointer r2
    //     0x7f940c: add             x2, x2, HEAP, lsl #32
    // 0x7f9410: stur            x2, [fp, #-0x18]
    // 0x7f9414: LoadField: r3 = r2->field_7
    //     0x7f9414: ldur            w3, [x2, #7]
    // 0x7f9418: DecompressPointer r3
    //     0x7f9418: add             x3, x3, HEAP, lsl #32
    // 0x7f941c: mov             x1, x3
    // 0x7f9420: stur            x3, [fp, #-0x10]
    // 0x7f9424: r0 = _HashSetIterator()
    //     0x7f9424: bl              #0x72592c  ; Allocate_HashSetIteratorStub -> _HashSetIterator<X0> (size=0x28)
    // 0x7f9428: stur            x0, [fp, #-0x20]
    // 0x7f942c: ArrayStore: r0[0] = rZR  ; List_8
    //     0x7f942c: stur            xzr, [x0, #0x17]
    // 0x7f9430: ldur            x2, [fp, #-0x18]
    // 0x7f9434: StoreField: r0->field_b = r2
    //     0x7f9434: stur            w2, [x0, #0xb]
    // 0x7f9438: ArrayLoad: r1 = r2[0]  ; List_8
    //     0x7f9438: ldur            x1, [x2, #0x17]
    // 0x7f943c: StoreField: r0->field_f = r1
    //     0x7f943c: stur            x1, [x0, #0xf]
    // 0x7f9440: ldur            x3, [fp, #-8]
    // 0x7f9444: CheckStackOverflow
    //     0x7f9444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f9448: cmp             SP, x16
    //     0x7f944c: b.ls            #0x7f95a4
    // 0x7f9450: mov             x1, x0
    // 0x7f9454: r0 = moveNext()
    //     0x7f9454: bl              #0x6885c4  ; [dart:collection] _HashSetIterator::moveNext
    // 0x7f9458: tbnz            w0, #4, #0x7f9584
    // 0x7f945c: ldur            x3, [fp, #-0x20]
    // 0x7f9460: LoadField: r4 = r3->field_23
    //     0x7f9460: ldur            w4, [x3, #0x23]
    // 0x7f9464: DecompressPointer r4
    //     0x7f9464: add             x4, x4, HEAP, lsl #32
    // 0x7f9468: stur            x4, [fp, #-0x28]
    // 0x7f946c: cmp             w4, NULL
    // 0x7f9470: b.ne            #0x7f94a4
    // 0x7f9474: mov             x0, x4
    // 0x7f9478: ldur            x2, [fp, #-0x10]
    // 0x7f947c: r1 = Null
    //     0x7f947c: mov             x1, NULL
    // 0x7f9480: cmp             w2, NULL
    // 0x7f9484: b.eq            #0x7f94a4
    // 0x7f9488: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7f9488: ldur            w4, [x2, #0x17]
    // 0x7f948c: DecompressPointer r4
    //     0x7f948c: add             x4, x4, HEAP, lsl #32
    // 0x7f9490: r8 = X0
    //     0x7f9490: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7f9494: LoadField: r9 = r4->field_7
    //     0x7f9494: ldur            x9, [x4, #7]
    // 0x7f9498: r3 = Null
    //     0x7f9498: add             x3, PP, #0x44, lsl #12  ; [pp+0x44a60] Null
    //     0x7f949c: ldr             x3, [x3, #0xa60]
    // 0x7f94a0: blr             x9
    // 0x7f94a4: ldur            x1, [fp, #-8]
    // 0x7f94a8: r0 = LoadStaticField(0x968)
    //     0x7f94a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7f94ac: ldr             x0, [x0, #0x12d0]
    // 0x7f94b0: cmp             w0, NULL
    // 0x7f94b4: b.eq            #0x7f95ac
    // 0x7f94b8: LoadField: r2 = r0->field_13
    //     0x7f94b8: ldur            w2, [x0, #0x13]
    // 0x7f94bc: DecompressPointer r2
    //     0x7f94bc: add             x2, x2, HEAP, lsl #32
    // 0x7f94c0: stur            x2, [fp, #-0x30]
    // 0x7f94c4: r0 = LoadClassIdInstr(r1)
    //     0x7f94c4: ldur            x0, [x1, #-1]
    //     0x7f94c8: ubfx            x0, x0, #0xc, #0x14
    // 0x7f94cc: str             x1, [SP]
    // 0x7f94d0: r0 = GDT[cid_x0 + 0x119bd]()
    //     0x7f94d0: movz            x17, #0x19bd
    //     0x7f94d4: movk            x17, #0x1, lsl #16
    //     0x7f94d8: add             lr, x0, x17
    //     0x7f94dc: ldr             lr, [x21, lr, lsl #3]
    //     0x7f94e0: blr             lr
    // 0x7f94e4: mov             x3, x0
    // 0x7f94e8: ldur            x0, [fp, #-0x30]
    // 0x7f94ec: stur            x3, [fp, #-0x40]
    // 0x7f94f0: LoadField: r4 = r0->field_7
    //     0x7f94f0: ldur            w4, [x0, #7]
    // 0x7f94f4: DecompressPointer r4
    //     0x7f94f4: add             x4, x4, HEAP, lsl #32
    // 0x7f94f8: mov             x1, x4
    // 0x7f94fc: ldur            x2, [fp, #-0x28]
    // 0x7f9500: stur            x4, [fp, #-0x38]
    // 0x7f9504: r0 = _getValueOrData()
    //     0x7f9504: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7f9508: ldur            x3, [fp, #-0x38]
    // 0x7f950c: LoadField: r1 = r3->field_f
    //     0x7f950c: ldur            w1, [x3, #0xf]
    // 0x7f9510: DecompressPointer r1
    //     0x7f9510: add             x1, x1, HEAP, lsl #32
    // 0x7f9514: cmp             w1, w0
    // 0x7f9518: b.ne            #0x7f9524
    // 0x7f951c: r4 = Null
    //     0x7f951c: mov             x4, NULL
    // 0x7f9520: b               #0x7f9528
    // 0x7f9524: mov             x4, x0
    // 0x7f9528: stur            x4, [fp, #-0x30]
    // 0x7f952c: cmp             w4, NULL
    // 0x7f9530: b.eq            #0x7f95b0
    // 0x7f9534: r0 = LoadClassIdInstr(r4)
    //     0x7f9534: ldur            x0, [x4, #-1]
    //     0x7f9538: ubfx            x0, x0, #0xc, #0x14
    // 0x7f953c: mov             x1, x4
    // 0x7f9540: ldur            x2, [fp, #-0x40]
    // 0x7f9544: r0 = GDT[cid_x0 + 0x725]()
    //     0x7f9544: add             lr, x0, #0x725
    //     0x7f9548: ldr             lr, [x21, lr, lsl #3]
    //     0x7f954c: blr             lr
    // 0x7f9550: ldur            x1, [fp, #-0x30]
    // 0x7f9554: r0 = LoadClassIdInstr(r1)
    //     0x7f9554: ldur            x0, [x1, #-1]
    //     0x7f9558: ubfx            x0, x0, #0xc, #0x14
    // 0x7f955c: r0 = GDT[cid_x0 + 0x667]()
    //     0x7f955c: add             lr, x0, #0x667
    //     0x7f9560: ldr             lr, [x21, lr, lsl #3]
    //     0x7f9564: blr             lr
    // 0x7f9568: tbnz            w0, #4, #0x7f9578
    // 0x7f956c: ldur            x1, [fp, #-0x38]
    // 0x7f9570: ldur            x2, [fp, #-0x28]
    // 0x7f9574: r0 = remove()
    //     0x7f9574: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x7f9578: ldur            x2, [fp, #-0x18]
    // 0x7f957c: ldur            x0, [fp, #-0x20]
    // 0x7f9580: b               #0x7f9440
    // 0x7f9584: ldur            x1, [fp, #-0x18]
    // 0x7f9588: r0 = clear()
    //     0x7f9588: bl              #0x64ab10  ; [dart:collection] _HashSet::clear
    // 0x7f958c: r0 = Null
    //     0x7f958c: mov             x0, NULL
    // 0x7f9590: LeaveFrame
    //     0x7f9590: mov             SP, fp
    //     0x7f9594: ldp             fp, lr, [SP], #0x10
    // 0x7f9598: ret
    //     0x7f9598: ret             
    // 0x7f959c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f959c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f95a0: b               #0x7f93dc
    // 0x7f95a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f95a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f95a8: b               #0x7f9450
    // 0x7f95ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f95ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7f95b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f95b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ addAllowedPointer(/* No info */) {
    // ** addr: 0x802a78, size: 0xa8
    // 0x802a78: EnterFrame
    //     0x802a78: stp             fp, lr, [SP, #-0x10]!
    //     0x802a7c: mov             fp, SP
    // 0x802a80: AllocStack(0x18)
    //     0x802a80: sub             SP, SP, #0x18
    // 0x802a84: SetupParameters(OneSequenceGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x802a84: mov             x3, x1
    //     0x802a88: stur            x1, [fp, #-8]
    //     0x802a8c: stur            x2, [fp, #-0x10]
    // 0x802a90: CheckStackOverflow
    //     0x802a90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x802a94: cmp             SP, x16
    //     0x802a98: b.ls            #0x802b18
    // 0x802a9c: r0 = LoadClassIdInstr(r2)
    //     0x802a9c: ldur            x0, [x2, #-1]
    //     0x802aa0: ubfx            x0, x0, #0xc, #0x14
    // 0x802aa4: mov             x1, x2
    // 0x802aa8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x802aa8: sub             lr, x0, #1, lsl #12
    //     0x802aac: ldr             lr, [x21, lr, lsl #3]
    //     0x802ab0: blr             lr
    // 0x802ab4: mov             x2, x0
    // 0x802ab8: ldur            x1, [fp, #-0x10]
    // 0x802abc: stur            x2, [fp, #-0x18]
    // 0x802ac0: r0 = LoadClassIdInstr(r1)
    //     0x802ac0: ldur            x0, [x1, #-1]
    //     0x802ac4: ubfx            x0, x0, #0xc, #0x14
    // 0x802ac8: r0 = GDT[cid_x0 + 0x131b9]()
    //     0x802ac8: movz            x17, #0x31b9
    //     0x802acc: movk            x17, #0x1, lsl #16
    //     0x802ad0: add             lr, x0, x17
    //     0x802ad4: ldr             lr, [x21, lr, lsl #3]
    //     0x802ad8: blr             lr
    // 0x802adc: ldur            x1, [fp, #-8]
    // 0x802ae0: r2 = LoadClassIdInstr(r1)
    //     0x802ae0: ldur            x2, [x1, #-1]
    //     0x802ae4: ubfx            x2, x2, #0xc, #0x14
    // 0x802ae8: mov             x3, x0
    // 0x802aec: mov             x0, x2
    // 0x802af0: ldur            x2, [fp, #-0x18]
    // 0x802af4: r0 = GDT[cid_x0 + 0x1022f]()
    //     0x802af4: movz            x17, #0x22f
    //     0x802af8: movk            x17, #0x1, lsl #16
    //     0x802afc: add             lr, x0, x17
    //     0x802b00: ldr             lr, [x21, lr, lsl #3]
    //     0x802b04: blr             lr
    // 0x802b08: r0 = Null
    //     0x802b08: mov             x0, NULL
    // 0x802b0c: LeaveFrame
    //     0x802b0c: mov             SP, fp
    //     0x802b10: ldp             fp, lr, [SP], #0x10
    // 0x802b14: ret
    //     0x802b14: ret             
    // 0x802b18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x802b18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x802b1c: b               #0x802a9c
  }
}

// class id: 3540, size: 0x48, field offset: 0x24
abstract class PrimaryPointerGestureRecognizer extends OneSequenceGestureRecognizer {

  dynamic handleEvent(dynamic) {
    // ** addr: 0x75cc3c, size: 0x24
    // 0x75cc3c: EnterFrame
    //     0x75cc3c: stp             fp, lr, [SP, #-0x10]!
    //     0x75cc40: mov             fp, SP
    // 0x75cc44: ldr             x2, [fp, #0x10]
    // 0x75cc48: r1 = Function 'handleEvent':.
    //     0x75cc48: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a30] AnonymousClosure: (0x75cc60), in [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::handleEvent (0x75cc9c)
    //     0x75cc4c: ldr             x1, [x1, #0xa30]
    // 0x75cc50: r0 = AllocateClosure()
    //     0x75cc50: bl              #0xec1630  ; AllocateClosureStub
    // 0x75cc54: LeaveFrame
    //     0x75cc54: mov             SP, fp
    //     0x75cc58: ldp             fp, lr, [SP], #0x10
    // 0x75cc5c: ret
    //     0x75cc5c: ret             
  }
  [closure] void handleEvent(dynamic, PointerEvent) {
    // ** addr: 0x75cc60, size: 0x3c
    // 0x75cc60: EnterFrame
    //     0x75cc60: stp             fp, lr, [SP, #-0x10]!
    //     0x75cc64: mov             fp, SP
    // 0x75cc68: ldr             x0, [fp, #0x18]
    // 0x75cc6c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x75cc6c: ldur            w1, [x0, #0x17]
    // 0x75cc70: DecompressPointer r1
    //     0x75cc70: add             x1, x1, HEAP, lsl #32
    // 0x75cc74: CheckStackOverflow
    //     0x75cc74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75cc78: cmp             SP, x16
    //     0x75cc7c: b.ls            #0x75cc94
    // 0x75cc80: ldr             x2, [fp, #0x10]
    // 0x75cc84: r0 = handleEvent()
    //     0x75cc84: bl              #0x75cc9c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::handleEvent
    // 0x75cc88: LeaveFrame
    //     0x75cc88: mov             SP, fp
    //     0x75cc8c: ldp             fp, lr, [SP], #0x10
    // 0x75cc90: ret
    //     0x75cc90: ret             
    // 0x75cc94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75cc94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75cc98: b               #0x75cc80
  }
  _ handleEvent(/* No info */) {
    // ** addr: 0x75cc9c, size: 0x28c
    // 0x75cc9c: EnterFrame
    //     0x75cc9c: stp             fp, lr, [SP, #-0x10]!
    //     0x75cca0: mov             fp, SP
    // 0x75cca4: AllocStack(0x20)
    //     0x75cca4: sub             SP, SP, #0x20
    // 0x75cca8: SetupParameters(PrimaryPointerGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x75cca8: mov             x3, x1
    //     0x75ccac: stur            x1, [fp, #-8]
    //     0x75ccb0: stur            x2, [fp, #-0x10]
    // 0x75ccb4: CheckStackOverflow
    //     0x75ccb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75ccb8: cmp             SP, x16
    //     0x75ccbc: b.ls            #0x75cf1c
    // 0x75ccc0: LoadField: r0 = r3->field_33
    //     0x75ccc0: ldur            w0, [x3, #0x33]
    // 0x75ccc4: DecompressPointer r0
    //     0x75ccc4: add             x0, x0, HEAP, lsl #32
    // 0x75ccc8: r16 = Instance_GestureRecognizerState
    //     0x75ccc8: add             x16, PP, #0x44, lsl #12  ; [pp+0x44a38] Obj!GestureRecognizerState@e36ca1
    //     0x75cccc: ldr             x16, [x16, #0xa38]
    // 0x75ccd0: cmp             w0, w16
    // 0x75ccd4: b.ne            #0x75cf00
    // 0x75ccd8: r0 = LoadClassIdInstr(r2)
    //     0x75ccd8: ldur            x0, [x2, #-1]
    //     0x75ccdc: ubfx            x0, x0, #0xc, #0x14
    // 0x75cce0: mov             x1, x2
    // 0x75cce4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x75cce4: sub             lr, x0, #1, lsl #12
    //     0x75cce8: ldr             lr, [x21, lr, lsl #3]
    //     0x75ccec: blr             lr
    // 0x75ccf0: mov             x2, x0
    // 0x75ccf4: ldur            x3, [fp, #-8]
    // 0x75ccf8: LoadField: r4 = r3->field_37
    //     0x75ccf8: ldur            w4, [x3, #0x37]
    // 0x75ccfc: DecompressPointer r4
    //     0x75ccfc: add             x4, x4, HEAP, lsl #32
    // 0x75cd00: r0 = BoxInt64Instr(r2)
    //     0x75cd00: sbfiz           x0, x2, #1, #0x1f
    //     0x75cd04: cmp             x2, x0, asr #1
    //     0x75cd08: b.eq            #0x75cd14
    //     0x75cd0c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x75cd10: stur            x2, [x0, #7]
    // 0x75cd14: cmp             w0, w4
    // 0x75cd18: b.eq            #0x75cd54
    // 0x75cd1c: and             w16, w0, w4
    // 0x75cd20: branchIfSmi(r16, 0x75cf00)
    //     0x75cd20: tbz             w16, #0, #0x75cf00
    // 0x75cd24: r16 = LoadClassIdInstr(r0)
    //     0x75cd24: ldur            x16, [x0, #-1]
    //     0x75cd28: ubfx            x16, x16, #0xc, #0x14
    // 0x75cd2c: cmp             x16, #0x3d
    // 0x75cd30: b.ne            #0x75cf00
    // 0x75cd34: r16 = LoadClassIdInstr(r4)
    //     0x75cd34: ldur            x16, [x4, #-1]
    //     0x75cd38: ubfx            x16, x16, #0xc, #0x14
    // 0x75cd3c: cmp             x16, #0x3d
    // 0x75cd40: b.ne            #0x75cf00
    // 0x75cd44: LoadField: r16 = r0->field_7
    //     0x75cd44: ldur            x16, [x0, #7]
    // 0x75cd48: LoadField: r17 = r4->field_7
    //     0x75cd48: ldur            x17, [x4, #7]
    // 0x75cd4c: cmp             x16, x17
    // 0x75cd50: b.ne            #0x75cf00
    // 0x75cd54: LoadField: r0 = r3->field_3f
    //     0x75cd54: ldur            w0, [x3, #0x3f]
    // 0x75cd58: DecompressPointer r0
    //     0x75cd58: add             x0, x0, HEAP, lsl #32
    // 0x75cd5c: tbz             w0, #4, #0x75cd8c
    // 0x75cd60: mov             x1, x3
    // 0x75cd64: ldur            x2, [fp, #-0x10]
    // 0x75cd68: r0 = _getGlobalDistance()
    //     0x75cd68: bl              #0x75d064  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::_getGlobalDistance
    // 0x75cd6c: mov             v1.16b, v0.16b
    // 0x75cd70: d0 = 18.000000
    //     0x75cd70: fmov            d0, #18.00000000
    // 0x75cd74: fcmp            d1, d0
    // 0x75cd78: r16 = true
    //     0x75cd78: add             x16, NULL, #0x20  ; true
    // 0x75cd7c: r17 = false
    //     0x75cd7c: add             x17, NULL, #0x30  ; false
    // 0x75cd80: csel            x0, x16, x17, gt
    // 0x75cd84: mov             x3, x0
    // 0x75cd88: b               #0x75cd90
    // 0x75cd8c: r3 = false
    //     0x75cd8c: add             x3, NULL, #0x30  ; false
    // 0x75cd90: ldur            x0, [fp, #-8]
    // 0x75cd94: stur            x3, [fp, #-0x20]
    // 0x75cd98: LoadField: r1 = r0->field_3f
    //     0x75cd98: ldur            w1, [x0, #0x3f]
    // 0x75cd9c: DecompressPointer r1
    //     0x75cd9c: add             x1, x1, HEAP, lsl #32
    // 0x75cda0: tbnz            w1, #4, #0x75cde4
    // 0x75cda4: LoadField: r4 = r0->field_2f
    //     0x75cda4: ldur            w4, [x0, #0x2f]
    // 0x75cda8: DecompressPointer r4
    //     0x75cda8: add             x4, x4, HEAP, lsl #32
    // 0x75cdac: stur            x4, [fp, #-0x18]
    // 0x75cdb0: cmp             w4, NULL
    // 0x75cdb4: b.eq            #0x75cde4
    // 0x75cdb8: mov             x1, x0
    // 0x75cdbc: ldur            x2, [fp, #-0x10]
    // 0x75cdc0: r0 = _getGlobalDistance()
    //     0x75cdc0: bl              #0x75d064  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::_getGlobalDistance
    // 0x75cdc4: ldur            x0, [fp, #-0x18]
    // 0x75cdc8: LoadField: d1 = r0->field_7
    //     0x75cdc8: ldur            d1, [x0, #7]
    // 0x75cdcc: fcmp            d0, d1
    // 0x75cdd0: r16 = true
    //     0x75cdd0: add             x16, NULL, #0x20  ; true
    // 0x75cdd4: r17 = false
    //     0x75cdd4: add             x17, NULL, #0x30  ; false
    // 0x75cdd8: csel            x0, x16, x17, gt
    // 0x75cddc: mov             x3, x0
    // 0x75cde0: b               #0x75cde8
    // 0x75cde4: r3 = false
    //     0x75cde4: add             x3, NULL, #0x30  ; false
    // 0x75cde8: ldur            x0, [fp, #-0x10]
    // 0x75cdec: stur            x3, [fp, #-0x18]
    // 0x75cdf0: r2 = Null
    //     0x75cdf0: mov             x2, NULL
    // 0x75cdf4: r1 = Null
    //     0x75cdf4: mov             x1, NULL
    // 0x75cdf8: cmp             w0, NULL
    // 0x75cdfc: b.eq            #0x75ce1c
    // 0x75ce00: branchIfSmi(r0, 0x75ce1c)
    //     0x75ce00: tbz             w0, #0, #0x75ce1c
    // 0x75ce04: r3 = LoadClassIdInstr(r0)
    //     0x75ce04: ldur            x3, [x0, #-1]
    //     0x75ce08: ubfx            x3, x3, #0xc, #0x14
    // 0x75ce0c: cmp             x3, #0xda5
    // 0x75ce10: b.eq            #0x75ce24
    // 0x75ce14: cmp             x3, #0xfcf
    // 0x75ce18: b.eq            #0x75ce24
    // 0x75ce1c: r0 = false
    //     0x75ce1c: add             x0, NULL, #0x30  ; false
    // 0x75ce20: b               #0x75ce28
    // 0x75ce24: r0 = true
    //     0x75ce24: add             x0, NULL, #0x20  ; true
    // 0x75ce28: tbnz            w0, #4, #0x75cedc
    // 0x75ce2c: ldur            x0, [fp, #-0x20]
    // 0x75ce30: tbz             w0, #4, #0x75ce3c
    // 0x75ce34: ldur            x0, [fp, #-0x18]
    // 0x75ce38: tbnz            w0, #4, #0x75cedc
    // 0x75ce3c: ldur            x0, [fp, #-8]
    // 0x75ce40: r1 = LoadClassIdInstr(r0)
    //     0x75ce40: ldur            x1, [x0, #-1]
    //     0x75ce44: ubfx            x1, x1, #0xc, #0x14
    // 0x75ce48: sub             x16, x1, #0xdd6
    // 0x75ce4c: cmp             x16, #2
    // 0x75ce50: b.hi            #0x75ce8c
    // 0x75ce54: LoadField: r1 = r0->field_4b
    //     0x75ce54: ldur            w1, [x0, #0x4b]
    // 0x75ce58: DecompressPointer r1
    //     0x75ce58: add             x1, x1, HEAP, lsl #32
    // 0x75ce5c: tbnz            w1, #4, #0x75ce78
    // 0x75ce60: mov             x1, x0
    // 0x75ce64: r2 = "spontaneous"
    //     0x75ce64: add             x2, PP, #0x44, lsl #12  ; [pp+0x44a40] "spontaneous"
    //     0x75ce68: ldr             x2, [x2, #0xa40]
    // 0x75ce6c: r0 = _checkCancel()
    //     0x75ce6c: bl              #0x75cffc  ; [package:flutter/src/gestures/tap.dart] BaseTapGestureRecognizer::_checkCancel
    // 0x75ce70: ldur            x1, [fp, #-8]
    // 0x75ce74: r0 = _reset()
    //     0x75ce74: bl              #0x75cfe0  ; [package:flutter/src/gestures/tap.dart] BaseTapGestureRecognizer::_reset
    // 0x75ce78: ldur            x1, [fp, #-8]
    // 0x75ce7c: r2 = Instance_GestureDisposition
    //     0x75ce7c: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0x75ce80: ldr             x2, [x2, #0xde8]
    // 0x75ce84: r0 = resolve()
    //     0x75ce84: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x75ce88: b               #0x75cebc
    // 0x75ce8c: LoadField: r1 = r0->field_47
    //     0x75ce8c: ldur            w1, [x0, #0x47]
    // 0x75ce90: DecompressPointer r1
    //     0x75ce90: add             x1, x1, HEAP, lsl #32
    // 0x75ce94: tbnz            w1, #4, #0x75cea4
    // 0x75ce98: mov             x1, x0
    // 0x75ce9c: r0 = _reset()
    //     0x75ce9c: bl              #0x75cfc4  ; [package:flutter/src/gestures/long_press.dart] LongPressGestureRecognizer::_reset
    // 0x75cea0: b               #0x75ceac
    // 0x75cea4: ldur            x1, [fp, #-8]
    // 0x75cea8: r0 = _checkLongPressCancel()
    //     0x75cea8: bl              #0x75cf48  ; [package:flutter/src/gestures/long_press.dart] LongPressGestureRecognizer::_checkLongPressCancel
    // 0x75ceac: ldur            x1, [fp, #-8]
    // 0x75ceb0: r2 = Instance_GestureDisposition
    //     0x75ceb0: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0x75ceb4: ldr             x2, [x2, #0xde8]
    // 0x75ceb8: r0 = resolve()
    //     0x75ceb8: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x75cebc: ldur            x0, [fp, #-8]
    // 0x75cec0: LoadField: r2 = r0->field_37
    //     0x75cec0: ldur            w2, [x0, #0x37]
    // 0x75cec4: DecompressPointer r2
    //     0x75cec4: add             x2, x2, HEAP, lsl #32
    // 0x75cec8: cmp             w2, NULL
    // 0x75cecc: b.eq            #0x75cf24
    // 0x75ced0: mov             x1, x0
    // 0x75ced4: r0 = stopTrackingPointer()
    //     0x75ced4: bl              #0x7587c8  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::stopTrackingPointer
    // 0x75ced8: b               #0x75cf00
    // 0x75cedc: ldur            x3, [fp, #-8]
    // 0x75cee0: r0 = LoadClassIdInstr(r3)
    //     0x75cee0: ldur            x0, [x3, #-1]
    //     0x75cee4: ubfx            x0, x0, #0xc, #0x14
    // 0x75cee8: mov             x1, x3
    // 0x75ceec: ldur            x2, [fp, #-0x10]
    // 0x75cef0: r0 = GDT[cid_x0 + 0x282c]()
    //     0x75cef0: movz            x17, #0x282c
    //     0x75cef4: add             lr, x0, x17
    //     0x75cef8: ldr             lr, [x21, lr, lsl #3]
    //     0x75cefc: blr             lr
    // 0x75cf00: ldur            x1, [fp, #-8]
    // 0x75cf04: ldur            x2, [fp, #-0x10]
    // 0x75cf08: r0 = stopTrackingIfPointerNoLongerDown()
    //     0x75cf08: bl              #0x75b718  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::stopTrackingIfPointerNoLongerDown
    // 0x75cf0c: r0 = Null
    //     0x75cf0c: mov             x0, NULL
    // 0x75cf10: LeaveFrame
    //     0x75cf10: mov             SP, fp
    //     0x75cf14: ldp             fp, lr, [SP], #0x10
    // 0x75cf18: ret
    //     0x75cf18: ret             
    // 0x75cf1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75cf1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75cf20: b               #0x75ccc0
    // 0x75cf24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75cf24: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _getGlobalDistance(/* No info */) {
    // ** addr: 0x75d064, size: 0x94
    // 0x75d064: EnterFrame
    //     0x75d064: stp             fp, lr, [SP, #-0x10]!
    //     0x75d068: mov             fp, SP
    // 0x75d06c: AllocStack(0x8)
    //     0x75d06c: sub             SP, SP, #8
    // 0x75d070: SetupParameters(PrimaryPointerGestureRecognizer this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0x75d070: stur            x1, [fp, #-8]
    //     0x75d074: mov             x16, x2
    //     0x75d078: mov             x2, x1
    //     0x75d07c: mov             x1, x16
    // 0x75d080: CheckStackOverflow
    //     0x75d080: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75d084: cmp             SP, x16
    //     0x75d088: b.ls            #0x75d0ec
    // 0x75d08c: r0 = LoadClassIdInstr(r1)
    //     0x75d08c: ldur            x0, [x1, #-1]
    //     0x75d090: ubfx            x0, x0, #0xc, #0x14
    // 0x75d094: r0 = GDT[cid_x0 + -0x1]()
    //     0x75d094: sub             lr, x0, #1
    //     0x75d098: ldr             lr, [x21, lr, lsl #3]
    //     0x75d09c: blr             lr
    // 0x75d0a0: mov             x1, x0
    // 0x75d0a4: ldur            x0, [fp, #-8]
    // 0x75d0a8: LoadField: r2 = r0->field_3b
    //     0x75d0a8: ldur            w2, [x0, #0x3b]
    // 0x75d0ac: DecompressPointer r2
    //     0x75d0ac: add             x2, x2, HEAP, lsl #32
    // 0x75d0b0: cmp             w2, NULL
    // 0x75d0b4: b.eq            #0x75d0f4
    // 0x75d0b8: LoadField: r0 = r2->field_b
    //     0x75d0b8: ldur            w0, [x2, #0xb]
    // 0x75d0bc: DecompressPointer r0
    //     0x75d0bc: add             x0, x0, HEAP, lsl #32
    // 0x75d0c0: mov             x2, x0
    // 0x75d0c4: r0 = -()
    //     0x75d0c4: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x75d0c8: LoadField: d1 = r0->field_7
    //     0x75d0c8: ldur            d1, [x0, #7]
    // 0x75d0cc: fmul            d2, d1, d1
    // 0x75d0d0: LoadField: d1 = r0->field_f
    //     0x75d0d0: ldur            d1, [x0, #0xf]
    // 0x75d0d4: fmul            d3, d1, d1
    // 0x75d0d8: fadd            d1, d2, d3
    // 0x75d0dc: fsqrt           d0, d1
    // 0x75d0e0: LeaveFrame
    //     0x75d0e0: mov             SP, fp
    //     0x75d0e4: ldp             fp, lr, [SP], #0x10
    // 0x75d0e8: ret
    //     0x75d0e8: ret             
    // 0x75d0ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75d0ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75d0f0: b               #0x75d08c
    // 0x75d0f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75d0f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ PrimaryPointerGestureRecognizer(/* No info */) {
    // ** addr: 0x762f7c, size: 0xdc
    // 0x762f7c: EnterFrame
    //     0x762f7c: stp             fp, lr, [SP, #-0x10]!
    //     0x762f80: mov             fp, SP
    // 0x762f84: AllocStack(0x10)
    //     0x762f84: sub             SP, SP, #0x10
    // 0x762f88: SetupParameters(dynamic _ /* r3 => r0 */, {dynamic postAcceptSlopTolerance = 18.000000 /* r6 */})
    //     0x762f88: mov             x0, x3
    //     0x762f8c: ldur            w3, [x4, #0x13]
    //     0x762f90: ldur            w6, [x4, #0x1f]
    //     0x762f94: add             x6, x6, HEAP, lsl #32
    //     0x762f98: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c20] "postAcceptSlopTolerance"
    //     0x762f9c: ldr             x16, [x16, #0xc20]
    //     0x762fa0: cmp             w6, w16
    //     0x762fa4: b.ne            #0x762fc4
    //     0x762fa8: ldur            w6, [x4, #0x23]
    //     0x762fac: add             x6, x6, HEAP, lsl #32
    //     0x762fb0: sub             w4, w3, w6
    //     0x762fb4: add             x3, fp, w4, sxtw #2
    //     0x762fb8: ldr             x3, [x3, #8]
    //     0x762fbc: mov             x6, x3
    //     0x762fc0: b               #0x762fcc
    //     0x762fc4: add             x6, PP, #0xb, lsl #12  ; [pp+0xb958] 18
    //     0x762fc8: ldr             x6, [x6, #0x958]
    //     0x762fcc: add             x4, PP, #0x31, lsl #12  ; [pp+0x31c28] Obj!GestureRecognizerState@e36cc1
    //     0x762fd0: ldr             x4, [x4, #0xc28]
    //     0x762fd4: add             x3, NULL, #0x30  ; false
    //     0x762fd8: fmov            d0, #18.00000000
    // 0x762fcc: r4 = Instance_GestureRecognizerState
    // 0x762fd4: r3 = false
    // 0x762fd8: d0 = 18.000000
    // 0x762fdc: CheckStackOverflow
    //     0x762fdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x762fe0: cmp             SP, x16
    //     0x762fe4: b.ls            #0x763050
    // 0x762fe8: StoreField: r1->field_33 = r4
    //     0x762fe8: stur            w4, [x1, #0x33]
    // 0x762fec: StoreField: r1->field_3f = r3
    //     0x762fec: stur            w3, [x1, #0x3f]
    // 0x762ff0: StoreField: r1->field_23 = r0
    //     0x762ff0: stur            w0, [x1, #0x23]
    //     0x762ff4: ldurb           w16, [x1, #-1]
    //     0x762ff8: ldurb           w17, [x0, #-1]
    //     0x762ffc: and             x16, x17, x16, lsr #2
    //     0x763000: tst             x16, HEAP, lsr #32
    //     0x763004: b.eq            #0x76300c
    //     0x763008: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76300c: StoreField: r1->field_27 = d0
    //     0x76300c: stur            d0, [x1, #0x27]
    // 0x763010: mov             x0, x6
    // 0x763014: StoreField: r1->field_2f = r0
    //     0x763014: stur            w0, [x1, #0x2f]
    //     0x763018: ldurb           w16, [x1, #-1]
    //     0x76301c: ldurb           w17, [x0, #-1]
    //     0x763020: and             x16, x17, x16, lsr #2
    //     0x763024: tst             x16, HEAP, lsr #32
    //     0x763028: b.eq            #0x763030
    //     0x76302c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x763030: stp             x2, x5, [SP]
    // 0x763034: r4 = const [0, 0x3, 0x2, 0x1, allowedButtonsFilter, 0x2, supportedDevices, 0x1, null]
    //     0x763034: add             x4, PP, #0x25, lsl #12  ; [pp+0x253c0] List(9) [0, 0x3, 0x2, 0x1, "allowedButtonsFilter", 0x2, "supportedDevices", 0x1, Null]
    //     0x763038: ldr             x4, [x4, #0x3c0]
    // 0x76303c: r0 = OneSequenceGestureRecognizer()
    //     0x76303c: bl              #0x763058  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::OneSequenceGestureRecognizer
    // 0x763040: r0 = Null
    //     0x763040: mov             x0, NULL
    // 0x763044: LeaveFrame
    //     0x763044: mov             SP, fp
    //     0x763048: ldp             fp, lr, [SP], #0x10
    // 0x76304c: ret
    //     0x76304c: ret             
    // 0x763050: r0 = StackOverflowSharedWithFPURegs()
    //     0x763050: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x763054: b               #0x762fe8
  }
  _ handleNonAllowedPointer(/* No info */) {
    // ** addr: 0x7b1b28, size: 0x3c
    // 0x7b1b28: EnterFrame
    //     0x7b1b28: stp             fp, lr, [SP, #-0x10]!
    //     0x7b1b2c: mov             fp, SP
    // 0x7b1b30: CheckStackOverflow
    //     0x7b1b30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b1b34: cmp             SP, x16
    //     0x7b1b38: b.ls            #0x7b1b5c
    // 0x7b1b3c: LoadField: r0 = r1->field_3f
    //     0x7b1b3c: ldur            w0, [x1, #0x3f]
    // 0x7b1b40: DecompressPointer r0
    //     0x7b1b40: add             x0, x0, HEAP, lsl #32
    // 0x7b1b44: tbz             w0, #4, #0x7b1b4c
    // 0x7b1b48: r0 = handleNonAllowedPointer()
    //     0x7b1b48: bl              #0x7b1c18  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::handleNonAllowedPointer
    // 0x7b1b4c: r0 = Null
    //     0x7b1b4c: mov             x0, NULL
    // 0x7b1b50: LeaveFrame
    //     0x7b1b50: mov             SP, fp
    //     0x7b1b54: ldp             fp, lr, [SP], #0x10
    // 0x7b1b58: ret
    //     0x7b1b58: ret             
    // 0x7b1b5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b1b5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b1b60: b               #0x7b1b3c
  }
  _ didStopTrackingLastPointer(/* No info */) {
    // ** addr: 0x7d7564, size: 0x5c
    // 0x7d7564: EnterFrame
    //     0x7d7564: stp             fp, lr, [SP, #-0x10]!
    //     0x7d7568: mov             fp, SP
    // 0x7d756c: AllocStack(0x8)
    //     0x7d756c: sub             SP, SP, #8
    // 0x7d7570: SetupParameters(PrimaryPointerGestureRecognizer this /* r1 => r0, fp-0x8 */)
    //     0x7d7570: mov             x0, x1
    //     0x7d7574: stur            x1, [fp, #-8]
    // 0x7d7578: CheckStackOverflow
    //     0x7d7578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d757c: cmp             SP, x16
    //     0x7d7580: b.ls            #0x7d75b8
    // 0x7d7584: mov             x1, x0
    // 0x7d7588: r0 = _stopTimer()
    //     0x7d7588: bl              #0x7d75c0  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::_stopTimer
    // 0x7d758c: ldur            x2, [fp, #-8]
    // 0x7d7590: r1 = Instance_GestureRecognizerState
    //     0x7d7590: add             x1, PP, #0x31, lsl #12  ; [pp+0x31c28] Obj!GestureRecognizerState@e36cc1
    //     0x7d7594: ldr             x1, [x1, #0xc28]
    // 0x7d7598: StoreField: r2->field_33 = r1
    //     0x7d7598: stur            w1, [x2, #0x33]
    // 0x7d759c: StoreField: r2->field_3b = rNULL
    //     0x7d759c: stur            NULL, [x2, #0x3b]
    // 0x7d75a0: r1 = false
    //     0x7d75a0: add             x1, NULL, #0x30  ; false
    // 0x7d75a4: StoreField: r2->field_3f = r1
    //     0x7d75a4: stur            w1, [x2, #0x3f]
    // 0x7d75a8: r0 = Null
    //     0x7d75a8: mov             x0, NULL
    // 0x7d75ac: LeaveFrame
    //     0x7d75ac: mov             SP, fp
    //     0x7d75b0: ldp             fp, lr, [SP], #0x10
    // 0x7d75b4: ret
    //     0x7d75b4: ret             
    // 0x7d75b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d75b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d75bc: b               #0x7d7584
  }
  _ _stopTimer(/* No info */) {
    // ** addr: 0x7d75c0, size: 0x54
    // 0x7d75c0: EnterFrame
    //     0x7d75c0: stp             fp, lr, [SP, #-0x10]!
    //     0x7d75c4: mov             fp, SP
    // 0x7d75c8: AllocStack(0x8)
    //     0x7d75c8: sub             SP, SP, #8
    // 0x7d75cc: SetupParameters(PrimaryPointerGestureRecognizer this /* r1 => r0, fp-0x8 */)
    //     0x7d75cc: mov             x0, x1
    //     0x7d75d0: stur            x1, [fp, #-8]
    // 0x7d75d4: CheckStackOverflow
    //     0x7d75d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d75d8: cmp             SP, x16
    //     0x7d75dc: b.ls            #0x7d760c
    // 0x7d75e0: LoadField: r1 = r0->field_43
    //     0x7d75e0: ldur            w1, [x0, #0x43]
    // 0x7d75e4: DecompressPointer r1
    //     0x7d75e4: add             x1, x1, HEAP, lsl #32
    // 0x7d75e8: cmp             w1, NULL
    // 0x7d75ec: b.eq            #0x7d75fc
    // 0x7d75f0: r0 = cancel()
    //     0x7d75f0: bl              #0x5fe740  ; [dart:isolate] _Timer::cancel
    // 0x7d75f4: ldur            x1, [fp, #-8]
    // 0x7d75f8: StoreField: r1->field_43 = rNULL
    //     0x7d75f8: stur            NULL, [x1, #0x43]
    // 0x7d75fc: r0 = Null
    //     0x7d75fc: mov             x0, NULL
    // 0x7d7600: LeaveFrame
    //     0x7d7600: mov             SP, fp
    //     0x7d7604: ldp             fp, lr, [SP], #0x10
    // 0x7d7608: ret
    //     0x7d7608: ret             
    // 0x7d760c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d760c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d7610: b               #0x7d75e0
  }
  _ dispose(/* No info */) {
    // ** addr: 0x7f9248, size: 0x48
    // 0x7f9248: EnterFrame
    //     0x7f9248: stp             fp, lr, [SP, #-0x10]!
    //     0x7f924c: mov             fp, SP
    // 0x7f9250: AllocStack(0x8)
    //     0x7f9250: sub             SP, SP, #8
    // 0x7f9254: SetupParameters(PrimaryPointerGestureRecognizer this /* r1 => r0, fp-0x8 */)
    //     0x7f9254: mov             x0, x1
    //     0x7f9258: stur            x1, [fp, #-8]
    // 0x7f925c: CheckStackOverflow
    //     0x7f925c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f9260: cmp             SP, x16
    //     0x7f9264: b.ls            #0x7f9288
    // 0x7f9268: mov             x1, x0
    // 0x7f926c: r0 = _stopTimer()
    //     0x7f926c: bl              #0x7d75c0  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::_stopTimer
    // 0x7f9270: ldur            x1, [fp, #-8]
    // 0x7f9274: r0 = dispose()
    //     0x7f9274: bl              #0x7f93bc  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::dispose
    // 0x7f9278: r0 = Null
    //     0x7f9278: mov             x0, NULL
    // 0x7f927c: LeaveFrame
    //     0x7f927c: mov             SP, fp
    //     0x7f9280: ldp             fp, lr, [SP], #0x10
    // 0x7f9284: ret
    //     0x7f9284: ret             
    // 0x7f9288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f9288: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f928c: b               #0x7f9268
  }
  _ addAllowedPointer(/* No info */) {
    // ** addr: 0x801db4, size: 0x1ac
    // 0x801db4: EnterFrame
    //     0x801db4: stp             fp, lr, [SP, #-0x10]!
    //     0x801db8: mov             fp, SP
    // 0x801dbc: AllocStack(0x20)
    //     0x801dbc: sub             SP, SP, #0x20
    // 0x801dc0: SetupParameters(PrimaryPointerGestureRecognizer this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x801dc0: stur            x1, [fp, #-8]
    //     0x801dc4: stur            x2, [fp, #-0x10]
    // 0x801dc8: CheckStackOverflow
    //     0x801dc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x801dcc: cmp             SP, x16
    //     0x801dd0: b.ls            #0x801f58
    // 0x801dd4: r1 = 1
    //     0x801dd4: movz            x1, #0x1
    // 0x801dd8: r0 = AllocateContext()
    //     0x801dd8: bl              #0xec126c  ; AllocateContextStub
    // 0x801ddc: mov             x3, x0
    // 0x801de0: ldur            x0, [fp, #-8]
    // 0x801de4: stur            x3, [fp, #-0x18]
    // 0x801de8: StoreField: r3->field_f = r0
    //     0x801de8: stur            w0, [x3, #0xf]
    // 0x801dec: mov             x1, x0
    // 0x801df0: ldur            x2, [fp, #-0x10]
    // 0x801df4: r0 = addAllowedPointer()
    //     0x801df4: bl              #0x802a78  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::addAllowedPointer
    // 0x801df8: ldur            x2, [fp, #-8]
    // 0x801dfc: LoadField: r0 = r2->field_33
    //     0x801dfc: ldur            w0, [x2, #0x33]
    // 0x801e00: DecompressPointer r0
    //     0x801e00: add             x0, x0, HEAP, lsl #32
    // 0x801e04: r16 = Instance_GestureRecognizerState
    //     0x801e04: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c28] Obj!GestureRecognizerState@e36cc1
    //     0x801e08: ldr             x16, [x16, #0xc28]
    // 0x801e0c: cmp             w0, w16
    // 0x801e10: b.ne            #0x801f48
    // 0x801e14: ldur            x3, [fp, #-0x10]
    // 0x801e18: r0 = Instance_GestureRecognizerState
    //     0x801e18: add             x0, PP, #0x44, lsl #12  ; [pp+0x44a38] Obj!GestureRecognizerState@e36ca1
    //     0x801e1c: ldr             x0, [x0, #0xa38]
    // 0x801e20: StoreField: r2->field_33 = r0
    //     0x801e20: stur            w0, [x2, #0x33]
    // 0x801e24: r0 = LoadClassIdInstr(r3)
    //     0x801e24: ldur            x0, [x3, #-1]
    //     0x801e28: ubfx            x0, x0, #0xc, #0x14
    // 0x801e2c: mov             x1, x3
    // 0x801e30: r0 = GDT[cid_x0 + -0x1000]()
    //     0x801e30: sub             lr, x0, #1, lsl #12
    //     0x801e34: ldr             lr, [x21, lr, lsl #3]
    //     0x801e38: blr             lr
    // 0x801e3c: mov             x2, x0
    // 0x801e40: r0 = BoxInt64Instr(r2)
    //     0x801e40: sbfiz           x0, x2, #1, #0x1f
    //     0x801e44: cmp             x2, x0, asr #1
    //     0x801e48: b.eq            #0x801e54
    //     0x801e4c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x801e50: stur            x2, [x0, #7]
    // 0x801e54: ldur            x2, [fp, #-8]
    // 0x801e58: StoreField: r2->field_37 = r0
    //     0x801e58: stur            w0, [x2, #0x37]
    //     0x801e5c: tbz             w0, #0, #0x801e78
    //     0x801e60: ldurb           w16, [x2, #-1]
    //     0x801e64: ldurb           w17, [x0, #-1]
    //     0x801e68: and             x16, x17, x16, lsr #2
    //     0x801e6c: tst             x16, HEAP, lsr #32
    //     0x801e70: b.eq            #0x801e78
    //     0x801e74: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x801e78: ldur            x3, [fp, #-0x10]
    // 0x801e7c: r0 = LoadClassIdInstr(r3)
    //     0x801e7c: ldur            x0, [x3, #-1]
    //     0x801e80: ubfx            x0, x0, #0xc, #0x14
    // 0x801e84: mov             x1, x3
    // 0x801e88: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x801e88: movz            x17, #0x30fa
    //     0x801e8c: movk            x17, #0x1, lsl #16
    //     0x801e90: add             lr, x0, x17
    //     0x801e94: ldr             lr, [x21, lr, lsl #3]
    //     0x801e98: blr             lr
    // 0x801e9c: mov             x2, x0
    // 0x801ea0: ldur            x1, [fp, #-0x10]
    // 0x801ea4: stur            x2, [fp, #-0x20]
    // 0x801ea8: r0 = LoadClassIdInstr(r1)
    //     0x801ea8: ldur            x0, [x1, #-1]
    //     0x801eac: ubfx            x0, x0, #0xc, #0x14
    // 0x801eb0: r0 = GDT[cid_x0 + -0x1]()
    //     0x801eb0: sub             lr, x0, #1
    //     0x801eb4: ldr             lr, [x21, lr, lsl #3]
    //     0x801eb8: blr             lr
    // 0x801ebc: stur            x0, [fp, #-0x10]
    // 0x801ec0: r0 = OffsetPair()
    //     0x801ec0: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x801ec4: mov             x1, x0
    // 0x801ec8: ldur            x0, [fp, #-0x20]
    // 0x801ecc: StoreField: r1->field_7 = r0
    //     0x801ecc: stur            w0, [x1, #7]
    // 0x801ed0: ldur            x0, [fp, #-0x10]
    // 0x801ed4: StoreField: r1->field_b = r0
    //     0x801ed4: stur            w0, [x1, #0xb]
    // 0x801ed8: mov             x0, x1
    // 0x801edc: ldur            x3, [fp, #-8]
    // 0x801ee0: StoreField: r3->field_3b = r0
    //     0x801ee0: stur            w0, [x3, #0x3b]
    //     0x801ee4: ldurb           w16, [x3, #-1]
    //     0x801ee8: ldurb           w17, [x0, #-1]
    //     0x801eec: and             x16, x17, x16, lsr #2
    //     0x801ef0: tst             x16, HEAP, lsr #32
    //     0x801ef4: b.eq            #0x801efc
    //     0x801ef8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x801efc: LoadField: r0 = r3->field_23
    //     0x801efc: ldur            w0, [x3, #0x23]
    // 0x801f00: DecompressPointer r0
    //     0x801f00: add             x0, x0, HEAP, lsl #32
    // 0x801f04: ldur            x2, [fp, #-0x18]
    // 0x801f08: stur            x0, [fp, #-0x10]
    // 0x801f0c: r1 = Function '<anonymous closure>':.
    //     0x801f0c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44a50] AnonymousClosure: (0x801f60), in [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::addAllowedPointer (0x801db4)
    //     0x801f10: ldr             x1, [x1, #0xa50]
    // 0x801f14: r0 = AllocateClosure()
    //     0x801f14: bl              #0xec1630  ; AllocateClosureStub
    // 0x801f18: ldur            x2, [fp, #-0x10]
    // 0x801f1c: mov             x3, x0
    // 0x801f20: r1 = Null
    //     0x801f20: mov             x1, NULL
    // 0x801f24: r0 = Timer()
    //     0x801f24: bl              #0x5f9778  ; [dart:async] Timer::Timer
    // 0x801f28: ldur            x1, [fp, #-8]
    // 0x801f2c: StoreField: r1->field_43 = r0
    //     0x801f2c: stur            w0, [x1, #0x43]
    //     0x801f30: ldurb           w16, [x1, #-1]
    //     0x801f34: ldurb           w17, [x0, #-1]
    //     0x801f38: and             x16, x17, x16, lsr #2
    //     0x801f3c: tst             x16, HEAP, lsr #32
    //     0x801f40: b.eq            #0x801f48
    //     0x801f44: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x801f48: r0 = Null
    //     0x801f48: mov             x0, NULL
    // 0x801f4c: LeaveFrame
    //     0x801f4c: mov             SP, fp
    //     0x801f50: ldp             fp, lr, [SP], #0x10
    // 0x801f54: ret
    //     0x801f54: ret             
    // 0x801f58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x801f58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x801f5c: b               #0x801dd4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x801f60, size: 0x48
    // 0x801f60: EnterFrame
    //     0x801f60: stp             fp, lr, [SP, #-0x10]!
    //     0x801f64: mov             fp, SP
    // 0x801f68: ldr             x0, [fp, #0x10]
    // 0x801f6c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x801f6c: ldur            w1, [x0, #0x17]
    // 0x801f70: DecompressPointer r1
    //     0x801f70: add             x1, x1, HEAP, lsl #32
    // 0x801f74: CheckStackOverflow
    //     0x801f74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x801f78: cmp             SP, x16
    //     0x801f7c: b.ls            #0x801fa0
    // 0x801f80: LoadField: r0 = r1->field_f
    //     0x801f80: ldur            w0, [x1, #0xf]
    // 0x801f84: DecompressPointer r0
    //     0x801f84: add             x0, x0, HEAP, lsl #32
    // 0x801f88: mov             x1, x0
    // 0x801f8c: r0 = didExceedDeadlineWithEvent()
    //     0x801f8c: bl              #0x801fa8  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::didExceedDeadlineWithEvent
    // 0x801f90: r0 = Null
    //     0x801f90: mov             x0, NULL
    // 0x801f94: LeaveFrame
    //     0x801f94: mov             SP, fp
    //     0x801f98: ldp             fp, lr, [SP], #0x10
    // 0x801f9c: ret
    //     0x801f9c: ret             
    // 0x801fa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x801fa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x801fa4: b               #0x801f80
  }
  _ didExceedDeadlineWithEvent(/* No info */) {
    // ** addr: 0x801fa8, size: 0xa4
    // 0x801fa8: EnterFrame
    //     0x801fa8: stp             fp, lr, [SP, #-0x10]!
    //     0x801fac: mov             fp, SP
    // 0x801fb0: AllocStack(0x8)
    //     0x801fb0: sub             SP, SP, #8
    // 0x801fb4: SetupParameters(PrimaryPointerGestureRecognizer this /* r1 => r0, fp-0x8 */)
    //     0x801fb4: mov             x0, x1
    //     0x801fb8: stur            x1, [fp, #-8]
    // 0x801fbc: CheckStackOverflow
    //     0x801fbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x801fc0: cmp             SP, x16
    //     0x801fc4: b.ls            #0x802040
    // 0x801fc8: r1 = LoadClassIdInstr(r0)
    //     0x801fc8: ldur            x1, [x0, #-1]
    //     0x801fcc: ubfx            x1, x1, #0xc, #0x14
    // 0x801fd0: sub             x16, x1, #0xdd6
    // 0x801fd4: cmp             x16, #2
    // 0x801fd8: b.hi            #0x801fe8
    // 0x801fdc: mov             x1, x0
    // 0x801fe0: r0 = _checkDown()
    //     0x801fe0: bl              #0x8020f4  ; [package:flutter/src/gestures/tap.dart] BaseTapGestureRecognizer::_checkDown
    // 0x801fe4: b               #0x802030
    // 0x801fe8: mov             x1, x0
    // 0x801fec: r2 = Instance_GestureDisposition
    //     0x801fec: add             x2, PP, #0x30, lsl #12  ; [pp+0x30e00] Obj!GestureDisposition@e36ee1
    //     0x801ff0: ldr             x2, [x2, #0xe00]
    // 0x801ff4: r0 = resolve()
    //     0x801ff4: bl              #0x7a9b94  ; [package:flutter/src/gestures/long_press.dart] LongPressGestureRecognizer::resolve
    // 0x801ff8: ldur            x0, [fp, #-8]
    // 0x801ffc: r1 = true
    //     0x801ffc: add             x1, NULL, #0x20  ; true
    // 0x802000: StoreField: r0->field_47 = r1
    //     0x802000: stur            w1, [x0, #0x47]
    // 0x802004: LoadField: r1 = r0->field_37
    //     0x802004: ldur            w1, [x0, #0x37]
    // 0x802008: DecompressPointer r1
    //     0x802008: add             x1, x1, HEAP, lsl #32
    // 0x80200c: cmp             w1, NULL
    // 0x802010: b.eq            #0x802048
    // 0x802014: r2 = LoadInt32Instr(r1)
    //     0x802014: sbfx            x2, x1, #1, #0x1f
    //     0x802018: tbz             w1, #0, #0x802020
    //     0x80201c: ldur            x2, [x1, #7]
    // 0x802020: mov             x1, x0
    // 0x802024: r0 = acceptGesture()
    //     0x802024: bl              #0x80204c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::acceptGesture
    // 0x802028: ldur            x1, [fp, #-8]
    // 0x80202c: r0 = _checkLongPressStart()
    //     0x80202c: bl              #0x7d7a74  ; [package:flutter/src/gestures/long_press.dart] LongPressGestureRecognizer::_checkLongPressStart
    // 0x802030: r0 = Null
    //     0x802030: mov             x0, NULL
    // 0x802034: LeaveFrame
    //     0x802034: mov             SP, fp
    //     0x802038: ldp             fp, lr, [SP], #0x10
    // 0x80203c: ret
    //     0x80203c: ret             
    // 0x802040: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x802040: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x802044: b               #0x801fc8
    // 0x802048: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x802048: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ acceptGesture(/* No info */) {
    // ** addr: 0x80204c, size: 0xa8
    // 0x80204c: EnterFrame
    //     0x80204c: stp             fp, lr, [SP, #-0x10]!
    //     0x802050: mov             fp, SP
    // 0x802054: AllocStack(0x8)
    //     0x802054: sub             SP, SP, #8
    // 0x802058: SetupParameters(PrimaryPointerGestureRecognizer this /* r1 => r3, fp-0x8 */)
    //     0x802058: mov             x3, x1
    //     0x80205c: stur            x1, [fp, #-8]
    // 0x802060: CheckStackOverflow
    //     0x802060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x802064: cmp             SP, x16
    //     0x802068: b.ls            #0x8020ec
    // 0x80206c: LoadField: r4 = r3->field_37
    //     0x80206c: ldur            w4, [x3, #0x37]
    // 0x802070: DecompressPointer r4
    //     0x802070: add             x4, x4, HEAP, lsl #32
    // 0x802074: r0 = BoxInt64Instr(r2)
    //     0x802074: sbfiz           x0, x2, #1, #0x1f
    //     0x802078: cmp             x2, x0, asr #1
    //     0x80207c: b.eq            #0x802088
    //     0x802080: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x802084: stur            x2, [x0, #7]
    // 0x802088: cmp             w0, w4
    // 0x80208c: b.eq            #0x8020c8
    // 0x802090: and             w16, w0, w4
    // 0x802094: branchIfSmi(r16, 0x8020dc)
    //     0x802094: tbz             w16, #0, #0x8020dc
    // 0x802098: r16 = LoadClassIdInstr(r0)
    //     0x802098: ldur            x16, [x0, #-1]
    //     0x80209c: ubfx            x16, x16, #0xc, #0x14
    // 0x8020a0: cmp             x16, #0x3d
    // 0x8020a4: b.ne            #0x8020dc
    // 0x8020a8: r16 = LoadClassIdInstr(r4)
    //     0x8020a8: ldur            x16, [x4, #-1]
    //     0x8020ac: ubfx            x16, x16, #0xc, #0x14
    // 0x8020b0: cmp             x16, #0x3d
    // 0x8020b4: b.ne            #0x8020dc
    // 0x8020b8: LoadField: r16 = r0->field_7
    //     0x8020b8: ldur            x16, [x0, #7]
    // 0x8020bc: LoadField: r17 = r4->field_7
    //     0x8020bc: ldur            x17, [x4, #7]
    // 0x8020c0: cmp             x16, x17
    // 0x8020c4: b.ne            #0x8020dc
    // 0x8020c8: mov             x1, x3
    // 0x8020cc: r0 = _stopTimer()
    //     0x8020cc: bl              #0x7d75c0  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::_stopTimer
    // 0x8020d0: ldur            x1, [fp, #-8]
    // 0x8020d4: r2 = true
    //     0x8020d4: add             x2, NULL, #0x20  ; true
    // 0x8020d8: StoreField: r1->field_3f = r2
    //     0x8020d8: stur            w2, [x1, #0x3f]
    // 0x8020dc: r0 = Null
    //     0x8020dc: mov             x0, NULL
    // 0x8020e0: LeaveFrame
    //     0x8020e0: mov             SP, fp
    //     0x8020e4: ldp             fp, lr, [SP], #0x10
    // 0x8020e8: ret
    //     0x8020e8: ret             
    // 0x8020ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8020ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8020f0: b               #0x80206c
  }
  _ rejectGesture(/* No info */) {
    // ** addr: 0xcceb90, size: 0xc4
    // 0xcceb90: EnterFrame
    //     0xcceb90: stp             fp, lr, [SP, #-0x10]!
    //     0xcceb94: mov             fp, SP
    // 0xcceb98: AllocStack(0x8)
    //     0xcceb98: sub             SP, SP, #8
    // 0xcceb9c: SetupParameters(PrimaryPointerGestureRecognizer this /* r1 => r3, fp-0x8 */)
    //     0xcceb9c: mov             x3, x1
    //     0xcceba0: stur            x1, [fp, #-8]
    // 0xcceba4: CheckStackOverflow
    //     0xcceba4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcceba8: cmp             SP, x16
    //     0xccebac: b.ls            #0xccec4c
    // 0xccebb0: LoadField: r4 = r3->field_37
    //     0xccebb0: ldur            w4, [x3, #0x37]
    // 0xccebb4: DecompressPointer r4
    //     0xccebb4: add             x4, x4, HEAP, lsl #32
    // 0xccebb8: r0 = BoxInt64Instr(r2)
    //     0xccebb8: sbfiz           x0, x2, #1, #0x1f
    //     0xccebbc: cmp             x2, x0, asr #1
    //     0xccebc0: b.eq            #0xccebcc
    //     0xccebc4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xccebc8: stur            x2, [x0, #7]
    // 0xccebcc: cmp             w0, w4
    // 0xccebd0: b.eq            #0xccec0c
    // 0xccebd4: and             w16, w0, w4
    // 0xccebd8: branchIfSmi(r16, 0xccec3c)
    //     0xccebd8: tbz             w16, #0, #0xccec3c
    // 0xccebdc: r16 = LoadClassIdInstr(r0)
    //     0xccebdc: ldur            x16, [x0, #-1]
    //     0xccebe0: ubfx            x16, x16, #0xc, #0x14
    // 0xccebe4: cmp             x16, #0x3d
    // 0xccebe8: b.ne            #0xccec3c
    // 0xccebec: r16 = LoadClassIdInstr(r4)
    //     0xccebec: ldur            x16, [x4, #-1]
    //     0xccebf0: ubfx            x16, x16, #0xc, #0x14
    // 0xccebf4: cmp             x16, #0x3d
    // 0xccebf8: b.ne            #0xccec3c
    // 0xccebfc: LoadField: r16 = r0->field_7
    //     0xccebfc: ldur            x16, [x0, #7]
    // 0xccec00: LoadField: r17 = r4->field_7
    //     0xccec00: ldur            x17, [x4, #7]
    // 0xccec04: cmp             x16, x17
    // 0xccec08: b.ne            #0xccec3c
    // 0xccec0c: LoadField: r0 = r3->field_33
    //     0xccec0c: ldur            w0, [x3, #0x33]
    // 0xccec10: DecompressPointer r0
    //     0xccec10: add             x0, x0, HEAP, lsl #32
    // 0xccec14: r16 = Instance_GestureRecognizerState
    //     0xccec14: add             x16, PP, #0x44, lsl #12  ; [pp+0x44a38] Obj!GestureRecognizerState@e36ca1
    //     0xccec18: ldr             x16, [x16, #0xa38]
    // 0xccec1c: cmp             w0, w16
    // 0xccec20: b.ne            #0xccec3c
    // 0xccec24: mov             x1, x3
    // 0xccec28: r0 = _stopTimer()
    //     0xccec28: bl              #0x7d75c0  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::_stopTimer
    // 0xccec2c: ldur            x1, [fp, #-8]
    // 0xccec30: r2 = Instance_GestureRecognizerState
    //     0xccec30: add             x2, PP, #0x44, lsl #12  ; [pp+0x44a48] Obj!GestureRecognizerState@e36ce1
    //     0xccec34: ldr             x2, [x2, #0xa48]
    // 0xccec38: StoreField: r1->field_33 = r2
    //     0xccec38: stur            w2, [x1, #0x33]
    // 0xccec3c: r0 = Null
    //     0xccec3c: mov             x0, NULL
    // 0xccec40: LeaveFrame
    //     0xccec40: mov             SP, fp
    //     0xccec44: ldp             fp, lr, [SP], #0x10
    // 0xccec48: ret
    //     0xccec48: ret             
    // 0xccec4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xccec4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xccec50: b               #0xccebb0
  }
}

// class id: 7083, size: 0x14, field offset: 0x14
enum GestureRecognizerState extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc487ac, size: 0x64
    // 0xc487ac: EnterFrame
    //     0xc487ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc487b0: mov             fp, SP
    // 0xc487b4: AllocStack(0x10)
    //     0xc487b4: sub             SP, SP, #0x10
    // 0xc487b8: SetupParameters(GestureRecognizerState this /* r1 => r0, fp-0x8 */)
    //     0xc487b8: mov             x0, x1
    //     0xc487bc: stur            x1, [fp, #-8]
    // 0xc487c0: CheckStackOverflow
    //     0xc487c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc487c4: cmp             SP, x16
    //     0xc487c8: b.ls            #0xc48808
    // 0xc487cc: r1 = Null
    //     0xc487cc: mov             x1, NULL
    // 0xc487d0: r2 = 4
    //     0xc487d0: movz            x2, #0x4
    // 0xc487d4: r0 = AllocateArray()
    //     0xc487d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc487d8: r16 = "GestureRecognizerState."
    //     0xc487d8: add             x16, PP, #0x44, lsl #12  ; [pp+0x44a28] "GestureRecognizerState."
    //     0xc487dc: ldr             x16, [x16, #0xa28]
    // 0xc487e0: StoreField: r0->field_f = r16
    //     0xc487e0: stur            w16, [x0, #0xf]
    // 0xc487e4: ldur            x1, [fp, #-8]
    // 0xc487e8: LoadField: r2 = r1->field_f
    //     0xc487e8: ldur            w2, [x1, #0xf]
    // 0xc487ec: DecompressPointer r2
    //     0xc487ec: add             x2, x2, HEAP, lsl #32
    // 0xc487f0: StoreField: r0->field_13 = r2
    //     0xc487f0: stur            w2, [x0, #0x13]
    // 0xc487f4: str             x0, [SP]
    // 0xc487f8: r0 = _interpolate()
    //     0xc487f8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc487fc: LeaveFrame
    //     0xc487fc: mov             SP, fp
    //     0xc48800: ldp             fp, lr, [SP], #0x10
    // 0xc48804: ret
    //     0xc48804: ret             
    // 0xc48808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc48808: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4880c: b               #0xc487cc
  }
}

// class id: 7084, size: 0x14, field offset: 0x14
enum MultitouchDragStrategy extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc48748, size: 0x64
    // 0xc48748: EnterFrame
    //     0xc48748: stp             fp, lr, [SP, #-0x10]!
    //     0xc4874c: mov             fp, SP
    // 0xc48750: AllocStack(0x10)
    //     0xc48750: sub             SP, SP, #0x10
    // 0xc48754: SetupParameters(MultitouchDragStrategy this /* r1 => r0, fp-0x8 */)
    //     0xc48754: mov             x0, x1
    //     0xc48758: stur            x1, [fp, #-8]
    // 0xc4875c: CheckStackOverflow
    //     0xc4875c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc48760: cmp             SP, x16
    //     0xc48764: b.ls            #0xc487a4
    // 0xc48768: r1 = Null
    //     0xc48768: mov             x1, NULL
    // 0xc4876c: r2 = 4
    //     0xc4876c: movz            x2, #0x4
    // 0xc48770: r0 = AllocateArray()
    //     0xc48770: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc48774: r16 = "MultitouchDragStrategy."
    //     0xc48774: add             x16, PP, #0x30, lsl #12  ; [pp+0x30de0] "MultitouchDragStrategy."
    //     0xc48778: ldr             x16, [x16, #0xde0]
    // 0xc4877c: StoreField: r0->field_f = r16
    //     0xc4877c: stur            w16, [x0, #0xf]
    // 0xc48780: ldur            x1, [fp, #-8]
    // 0xc48784: LoadField: r2 = r1->field_f
    //     0xc48784: ldur            w2, [x1, #0xf]
    // 0xc48788: DecompressPointer r2
    //     0xc48788: add             x2, x2, HEAP, lsl #32
    // 0xc4878c: StoreField: r0->field_13 = r2
    //     0xc4878c: stur            w2, [x0, #0x13]
    // 0xc48790: str             x0, [SP]
    // 0xc48794: r0 = _interpolate()
    //     0xc48794: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc48798: LeaveFrame
    //     0xc48798: mov             SP, fp
    //     0xc4879c: ldp             fp, lr, [SP], #0x10
    // 0xc487a0: ret
    //     0xc487a0: ret             
    // 0xc487a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc487a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc487a8: b               #0xc48768
  }
}

// class id: 7085, size: 0x14, field offset: 0x14
enum DragStartBehavior extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc486e4, size: 0x64
    // 0xc486e4: EnterFrame
    //     0xc486e4: stp             fp, lr, [SP, #-0x10]!
    //     0xc486e8: mov             fp, SP
    // 0xc486ec: AllocStack(0x10)
    //     0xc486ec: sub             SP, SP, #0x10
    // 0xc486f0: SetupParameters(DragStartBehavior this /* r1 => r0, fp-0x8 */)
    //     0xc486f0: mov             x0, x1
    //     0xc486f4: stur            x1, [fp, #-8]
    // 0xc486f8: CheckStackOverflow
    //     0xc486f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc486fc: cmp             SP, x16
    //     0xc48700: b.ls            #0xc48740
    // 0xc48704: r1 = Null
    //     0xc48704: mov             x1, NULL
    // 0xc48708: r2 = 4
    //     0xc48708: movz            x2, #0x4
    // 0xc4870c: r0 = AllocateArray()
    //     0xc4870c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc48710: r16 = "DragStartBehavior."
    //     0xc48710: add             x16, PP, #0x22, lsl #12  ; [pp+0x22158] "DragStartBehavior."
    //     0xc48714: ldr             x16, [x16, #0x158]
    // 0xc48718: StoreField: r0->field_f = r16
    //     0xc48718: stur            w16, [x0, #0xf]
    // 0xc4871c: ldur            x1, [fp, #-8]
    // 0xc48720: LoadField: r2 = r1->field_f
    //     0xc48720: ldur            w2, [x1, #0xf]
    // 0xc48724: DecompressPointer r2
    //     0xc48724: add             x2, x2, HEAP, lsl #32
    // 0xc48728: StoreField: r0->field_13 = r2
    //     0xc48728: stur            w2, [x0, #0x13]
    // 0xc4872c: str             x0, [SP]
    // 0xc48730: r0 = _interpolate()
    //     0xc48730: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc48734: LeaveFrame
    //     0xc48734: mov             SP, fp
    //     0xc48738: ldp             fp, lr, [SP], #0x10
    // 0xc4873c: ret
    //     0xc4873c: ret             
    // 0xc48740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc48740: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc48744: b               #0xc48704
  }
}
