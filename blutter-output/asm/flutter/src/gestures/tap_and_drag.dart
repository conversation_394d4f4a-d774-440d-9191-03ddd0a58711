// lib: , url: package:flutter/src/gestures/tap_and_drag.dart

// class id: 1048844, size: 0x8
class :: {

  static _ _getGlobalDistance(/* No info */) {
    // ** addr: 0x75e5c4, size: 0x7c
    // 0x75e5c4: EnterFrame
    //     0x75e5c4: stp             fp, lr, [SP, #-0x10]!
    //     0x75e5c8: mov             fp, SP
    // 0x75e5cc: AllocStack(0x8)
    //     0x75e5cc: sub             SP, SP, #8
    // 0x75e5d0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x75e5d0: stur            x2, [fp, #-8]
    // 0x75e5d4: CheckStackOverflow
    //     0x75e5d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75e5d8: cmp             SP, x16
    //     0x75e5dc: b.ls            #0x75e634
    // 0x75e5e0: r0 = LoadClassIdInstr(r1)
    //     0x75e5e0: ldur            x0, [x1, #-1]
    //     0x75e5e4: ubfx            x0, x0, #0xc, #0x14
    // 0x75e5e8: r0 = GDT[cid_x0 + -0x1]()
    //     0x75e5e8: sub             lr, x0, #1
    //     0x75e5ec: ldr             lr, [x21, lr, lsl #3]
    //     0x75e5f0: blr             lr
    // 0x75e5f4: mov             x1, x0
    // 0x75e5f8: ldur            x0, [fp, #-8]
    // 0x75e5fc: cmp             w0, NULL
    // 0x75e600: b.eq            #0x75e63c
    // 0x75e604: LoadField: r2 = r0->field_b
    //     0x75e604: ldur            w2, [x0, #0xb]
    // 0x75e608: DecompressPointer r2
    //     0x75e608: add             x2, x2, HEAP, lsl #32
    // 0x75e60c: r0 = -()
    //     0x75e60c: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x75e610: LoadField: d1 = r0->field_7
    //     0x75e610: ldur            d1, [x0, #7]
    // 0x75e614: fmul            d2, d1, d1
    // 0x75e618: LoadField: d1 = r0->field_f
    //     0x75e618: ldur            d1, [x0, #0xf]
    // 0x75e61c: fmul            d3, d1, d1
    // 0x75e620: fadd            d1, d2, d3
    // 0x75e624: fsqrt           d0, d1
    // 0x75e628: LeaveFrame
    //     0x75e628: mov             SP, fp
    //     0x75e62c: ldp             fp, lr, [SP], #0x10
    // 0x75e630: ret
    //     0x75e630: ret             
    // 0x75e634: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75e634: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75e638: b               #0x75e5e0
    // 0x75e63c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75e63c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3534, size: 0x4c, field offset: 0x24
//   transformed mixin,
abstract class _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin extends OneSequenceGestureRecognizer
     with _TapStatusTrackerMixin {

  _ handleEvent(/* No info */) {
    // ** addr: 0x75e640, size: 0x21c
    // 0x75e640: EnterFrame
    //     0x75e640: stp             fp, lr, [SP, #-0x10]!
    //     0x75e644: mov             fp, SP
    // 0x75e648: AllocStack(0x18)
    //     0x75e648: sub             SP, SP, #0x18
    // 0x75e64c: SetupParameters(_BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x75e64c: mov             x4, x1
    //     0x75e650: mov             x3, x2
    //     0x75e654: stur            x1, [fp, #-8]
    //     0x75e658: stur            x2, [fp, #-0x10]
    // 0x75e65c: CheckStackOverflow
    //     0x75e65c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75e660: cmp             SP, x16
    //     0x75e664: b.ls            #0x75e854
    // 0x75e668: mov             x0, x3
    // 0x75e66c: r2 = Null
    //     0x75e66c: mov             x2, NULL
    // 0x75e670: r1 = Null
    //     0x75e670: mov             x1, NULL
    // 0x75e674: cmp             w0, NULL
    // 0x75e678: b.eq            #0x75e698
    // 0x75e67c: branchIfSmi(r0, 0x75e698)
    //     0x75e67c: tbz             w0, #0, #0x75e698
    // 0x75e680: r3 = LoadClassIdInstr(r0)
    //     0x75e680: ldur            x3, [x0, #-1]
    //     0x75e684: ubfx            x3, x3, #0xc, #0x14
    // 0x75e688: cmp             x3, #0xda5
    // 0x75e68c: b.eq            #0x75e6a0
    // 0x75e690: cmp             x3, #0xfcf
    // 0x75e694: b.eq            #0x75e6a0
    // 0x75e698: r0 = false
    //     0x75e698: add             x0, NULL, #0x30  ; false
    // 0x75e69c: b               #0x75e6a4
    // 0x75e6a0: r0 = true
    //     0x75e6a0: add             x0, NULL, #0x20  ; true
    // 0x75e6a4: tbnz            w0, #4, #0x75e770
    // 0x75e6a8: ldur            x3, [fp, #-8]
    // 0x75e6ac: ldur            x2, [fp, #-0x10]
    // 0x75e6b0: r0 = LoadClassIdInstr(r2)
    //     0x75e6b0: ldur            x0, [x2, #-1]
    //     0x75e6b4: ubfx            x0, x0, #0xc, #0x14
    // 0x75e6b8: mov             x1, x2
    // 0x75e6bc: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x75e6bc: movz            x17, #0x30b7
    //     0x75e6c0: movk            x17, #0x1, lsl #16
    //     0x75e6c4: add             lr, x0, x17
    //     0x75e6c8: ldr             lr, [x21, lr, lsl #3]
    //     0x75e6cc: blr             lr
    // 0x75e6d0: mov             x1, x0
    // 0x75e6d4: ldur            x0, [fp, #-8]
    // 0x75e6d8: LoadField: r2 = r0->field_7
    //     0x75e6d8: ldur            w2, [x0, #7]
    // 0x75e6dc: DecompressPointer r2
    //     0x75e6dc: add             x2, x2, HEAP, lsl #32
    // 0x75e6e0: LoadField: r3 = r1->field_7
    //     0x75e6e0: ldur            x3, [x1, #7]
    // 0x75e6e4: cmp             x3, #2
    // 0x75e6e8: b.gt            #0x75e704
    // 0x75e6ec: cmp             x3, #1
    // 0x75e6f0: b.gt            #0x75e704
    // 0x75e6f4: cmp             x3, #0
    // 0x75e6f8: b.le            #0x75e704
    // 0x75e6fc: d0 = 1.000000
    //     0x75e6fc: fmov            d0, #1.00000000
    // 0x75e700: b               #0x75e730
    // 0x75e704: cmp             w2, NULL
    // 0x75e708: b.ne            #0x75e714
    // 0x75e70c: r1 = Null
    //     0x75e70c: mov             x1, NULL
    // 0x75e710: b               #0x75e71c
    // 0x75e714: LoadField: r1 = r2->field_7
    //     0x75e714: ldur            w1, [x2, #7]
    // 0x75e718: DecompressPointer r1
    //     0x75e718: add             x1, x1, HEAP, lsl #32
    // 0x75e71c: cmp             w1, NULL
    // 0x75e720: b.ne            #0x75e72c
    // 0x75e724: d0 = 18.000000
    //     0x75e724: fmov            d0, #18.00000000
    // 0x75e728: b               #0x75e730
    // 0x75e72c: LoadField: d0 = r1->field_7
    //     0x75e72c: ldur            d0, [x1, #7]
    // 0x75e730: stur            d0, [fp, #-0x18]
    // 0x75e734: LoadField: r2 = r0->field_33
    //     0x75e734: ldur            w2, [x0, #0x33]
    // 0x75e738: DecompressPointer r2
    //     0x75e738: add             x2, x2, HEAP, lsl #32
    // 0x75e73c: ldur            x1, [fp, #-0x10]
    // 0x75e740: r0 = _getGlobalDistance()
    //     0x75e740: bl              #0x75e5c4  ; [package:flutter/src/gestures/tap_and_drag.dart] ::_getGlobalDistance
    // 0x75e744: mov             v1.16b, v0.16b
    // 0x75e748: ldur            d0, [fp, #-0x18]
    // 0x75e74c: fcmp            d1, d0
    // 0x75e750: b.le            #0x75e844
    // 0x75e754: ldur            x0, [fp, #-8]
    // 0x75e758: mov             x1, x0
    // 0x75e75c: r0 = _consecutiveTapTimerStop()
    //     0x75e75c: bl              #0x75e9a4  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_consecutiveTapTimerStop
    // 0x75e760: ldur            x3, [fp, #-8]
    // 0x75e764: StoreField: r3->field_37 = rNULL
    //     0x75e764: stur            NULL, [x3, #0x37]
    // 0x75e768: StoreField: r3->field_3f = rNULL
    //     0x75e768: stur            NULL, [x3, #0x3f]
    // 0x75e76c: b               #0x75e844
    // 0x75e770: ldur            x3, [fp, #-8]
    // 0x75e774: ldur            x0, [fp, #-0x10]
    // 0x75e778: r2 = Null
    //     0x75e778: mov             x2, NULL
    // 0x75e77c: r1 = Null
    //     0x75e77c: mov             x1, NULL
    // 0x75e780: cmp             w0, NULL
    // 0x75e784: b.eq            #0x75e7a4
    // 0x75e788: branchIfSmi(r0, 0x75e7a4)
    //     0x75e788: tbz             w0, #0, #0x75e7a4
    // 0x75e78c: r3 = LoadClassIdInstr(r0)
    //     0x75e78c: ldur            x3, [x0, #-1]
    //     0x75e790: ubfx            x3, x3, #0xc, #0x14
    // 0x75e794: cmp             x3, #0xda3
    // 0x75e798: b.eq            #0x75e7ac
    // 0x75e79c: cmp             x3, #0xfcd
    // 0x75e7a0: b.eq            #0x75e7ac
    // 0x75e7a4: r0 = false
    //     0x75e7a4: add             x0, NULL, #0x30  ; false
    // 0x75e7a8: b               #0x75e7b0
    // 0x75e7ac: r0 = true
    //     0x75e7ac: add             x0, NULL, #0x20  ; true
    // 0x75e7b0: tbnz            w0, #4, #0x75e7fc
    // 0x75e7b4: ldur            x2, [fp, #-8]
    // 0x75e7b8: ldur            x0, [fp, #-0x10]
    // 0x75e7bc: StoreField: r2->field_27 = r0
    //     0x75e7bc: stur            w0, [x2, #0x27]
    //     0x75e7c0: ldurb           w16, [x2, #-1]
    //     0x75e7c4: ldurb           w17, [x0, #-1]
    //     0x75e7c8: and             x16, x17, x16, lsr #2
    //     0x75e7cc: tst             x16, HEAP, lsr #32
    //     0x75e7d0: b.eq            #0x75e7d8
    //     0x75e7d4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x75e7d8: LoadField: r0 = r2->field_23
    //     0x75e7d8: ldur            w0, [x2, #0x23]
    // 0x75e7dc: DecompressPointer r0
    //     0x75e7dc: add             x0, x0, HEAP, lsl #32
    // 0x75e7e0: cmp             w0, NULL
    // 0x75e7e4: b.eq            #0x75e844
    // 0x75e7e8: mov             x1, x2
    // 0x75e7ec: r0 = _consecutiveTapTimerStop()
    //     0x75e7ec: bl              #0x75e9a4  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_consecutiveTapTimerStop
    // 0x75e7f0: ldur            x1, [fp, #-8]
    // 0x75e7f4: r0 = _consecutiveTapTimerStart()
    //     0x75e7f4: bl              #0x75e918  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_consecutiveTapTimerStart
    // 0x75e7f8: b               #0x75e844
    // 0x75e7fc: ldur            x0, [fp, #-0x10]
    // 0x75e800: r2 = Null
    //     0x75e800: mov             x2, NULL
    // 0x75e804: r1 = Null
    //     0x75e804: mov             x1, NULL
    // 0x75e808: cmp             w0, NULL
    // 0x75e80c: b.eq            #0x75e82c
    // 0x75e810: branchIfSmi(r0, 0x75e82c)
    //     0x75e810: tbz             w0, #0, #0x75e82c
    // 0x75e814: r3 = LoadClassIdInstr(r0)
    //     0x75e814: ldur            x3, [x0, #-1]
    //     0x75e818: ubfx            x3, x3, #0xc, #0x14
    // 0x75e81c: cmp             x3, #0xd93
    // 0x75e820: b.eq            #0x75e834
    // 0x75e824: cmp             x3, #0xfc5
    // 0x75e828: b.eq            #0x75e834
    // 0x75e82c: r0 = false
    //     0x75e82c: add             x0, NULL, #0x30  ; false
    // 0x75e830: b               #0x75e838
    // 0x75e834: r0 = true
    //     0x75e834: add             x0, NULL, #0x20  ; true
    // 0x75e838: tbnz            w0, #4, #0x75e844
    // 0x75e83c: ldur            x1, [fp, #-8]
    // 0x75e840: r0 = _tapTrackerReset()
    //     0x75e840: bl              #0x75e898  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_tapTrackerReset
    // 0x75e844: r0 = Null
    //     0x75e844: mov             x0, NULL
    // 0x75e848: LeaveFrame
    //     0x75e848: mov             SP, fp
    //     0x75e84c: ldp             fp, lr, [SP], #0x10
    // 0x75e850: ret
    //     0x75e850: ret             
    // 0x75e854: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75e854: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75e858: b               #0x75e668
  }
  [closure] void handleEvent(dynamic, PointerEvent) {
    // ** addr: 0x75e85c, size: 0x3c
    // 0x75e85c: EnterFrame
    //     0x75e85c: stp             fp, lr, [SP, #-0x10]!
    //     0x75e860: mov             fp, SP
    // 0x75e864: ldr             x0, [fp, #0x18]
    // 0x75e868: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x75e868: ldur            w1, [x0, #0x17]
    // 0x75e86c: DecompressPointer r1
    //     0x75e86c: add             x1, x1, HEAP, lsl #32
    // 0x75e870: CheckStackOverflow
    //     0x75e870: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75e874: cmp             SP, x16
    //     0x75e878: b.ls            #0x75e890
    // 0x75e87c: ldr             x2, [fp, #0x10]
    // 0x75e880: r0 = handleEvent()
    //     0x75e880: bl              #0x75e640  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::handleEvent
    // 0x75e884: LeaveFrame
    //     0x75e884: mov             SP, fp
    //     0x75e888: ldp             fp, lr, [SP], #0x10
    // 0x75e88c: ret
    //     0x75e88c: ret             
    // 0x75e890: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75e890: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75e894: b               #0x75e87c
  }
  _ _tapTrackerReset(/* No info */) {
    // ** addr: 0x75e898, size: 0x80
    // 0x75e898: EnterFrame
    //     0x75e898: stp             fp, lr, [SP, #-0x10]!
    //     0x75e89c: mov             fp, SP
    // 0x75e8a0: AllocStack(0x10)
    //     0x75e8a0: sub             SP, SP, #0x10
    // 0x75e8a4: SetupParameters(_BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin this /* r1 => r0, fp-0x8 */)
    //     0x75e8a4: mov             x0, x1
    //     0x75e8a8: stur            x1, [fp, #-8]
    // 0x75e8ac: CheckStackOverflow
    //     0x75e8ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75e8b0: cmp             SP, x16
    //     0x75e8b4: b.ls            #0x75e910
    // 0x75e8b8: mov             x1, x0
    // 0x75e8bc: r0 = _consecutiveTapTimerStop()
    //     0x75e8bc: bl              #0x75e9a4  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_consecutiveTapTimerStop
    // 0x75e8c0: ldur            x0, [fp, #-8]
    // 0x75e8c4: StoreField: r0->field_37 = rNULL
    //     0x75e8c4: stur            NULL, [x0, #0x37]
    // 0x75e8c8: StoreField: r0->field_33 = rNULL
    //     0x75e8c8: stur            NULL, [x0, #0x33]
    // 0x75e8cc: StoreField: r0->field_3f = rNULL
    //     0x75e8cc: stur            NULL, [x0, #0x3f]
    // 0x75e8d0: StoreField: r0->field_2b = rZR
    //     0x75e8d0: stur            xzr, [x0, #0x2b]
    // 0x75e8d4: StoreField: r0->field_23 = rNULL
    //     0x75e8d4: stur            NULL, [x0, #0x23]
    // 0x75e8d8: StoreField: r0->field_27 = rNULL
    //     0x75e8d8: stur            NULL, [x0, #0x27]
    // 0x75e8dc: LoadField: r1 = r0->field_47
    //     0x75e8dc: ldur            w1, [x0, #0x47]
    // 0x75e8e0: DecompressPointer r1
    //     0x75e8e0: add             x1, x1, HEAP, lsl #32
    // 0x75e8e4: cmp             w1, NULL
    // 0x75e8e8: b.eq            #0x75e900
    // 0x75e8ec: str             x1, [SP]
    // 0x75e8f0: mov             x0, x1
    // 0x75e8f4: ClosureCall
    //     0x75e8f4: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x75e8f8: ldur            x2, [x0, #0x1f]
    //     0x75e8fc: blr             x2
    // 0x75e900: r0 = Null
    //     0x75e900: mov             x0, NULL
    // 0x75e904: LeaveFrame
    //     0x75e904: mov             SP, fp
    //     0x75e908: ldp             fp, lr, [SP], #0x10
    // 0x75e90c: ret
    //     0x75e90c: ret             
    // 0x75e910: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75e910: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75e914: b               #0x75e8b8
  }
  _ _consecutiveTapTimerStart(/* No info */) {
    // ** addr: 0x75e918, size: 0x8c
    // 0x75e918: EnterFrame
    //     0x75e918: stp             fp, lr, [SP, #-0x10]!
    //     0x75e91c: mov             fp, SP
    // 0x75e920: AllocStack(0x8)
    //     0x75e920: sub             SP, SP, #8
    // 0x75e924: SetupParameters(_BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin this /* r1 => r0, fp-0x8 */)
    //     0x75e924: mov             x0, x1
    //     0x75e928: stur            x1, [fp, #-8]
    // 0x75e92c: CheckStackOverflow
    //     0x75e92c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75e930: cmp             SP, x16
    //     0x75e934: b.ls            #0x75e99c
    // 0x75e938: LoadField: r1 = r0->field_3b
    //     0x75e938: ldur            w1, [x0, #0x3b]
    // 0x75e93c: DecompressPointer r1
    //     0x75e93c: add             x1, x1, HEAP, lsl #32
    // 0x75e940: cmp             w1, NULL
    // 0x75e944: b.ne            #0x75e98c
    // 0x75e948: mov             x2, x0
    // 0x75e94c: r1 = Function '_consecutiveTapTimerTimeout@437288344':.
    //     0x75e94c: add             x1, PP, #0x59, lsl #12  ; [pp+0x590c8] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x75e950: ldr             x1, [x1, #0xc8]
    // 0x75e954: r0 = AllocateClosure()
    //     0x75e954: bl              #0xec1630  ; AllocateClosureStub
    // 0x75e958: mov             x3, x0
    // 0x75e95c: r1 = Null
    //     0x75e95c: mov             x1, NULL
    // 0x75e960: r2 = Instance_Duration
    //     0x75e960: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0x75e964: ldr             x2, [x2, #0x9c0]
    // 0x75e968: r0 = Timer()
    //     0x75e968: bl              #0x5f9778  ; [dart:async] Timer::Timer
    // 0x75e96c: ldur            x1, [fp, #-8]
    // 0x75e970: StoreField: r1->field_3b = r0
    //     0x75e970: stur            w0, [x1, #0x3b]
    //     0x75e974: ldurb           w16, [x1, #-1]
    //     0x75e978: ldurb           w17, [x0, #-1]
    //     0x75e97c: and             x16, x17, x16, lsr #2
    //     0x75e980: tst             x16, HEAP, lsr #32
    //     0x75e984: b.eq            #0x75e98c
    //     0x75e988: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x75e98c: r0 = Null
    //     0x75e98c: mov             x0, NULL
    // 0x75e990: LeaveFrame
    //     0x75e990: mov             SP, fp
    //     0x75e994: ldp             fp, lr, [SP], #0x10
    // 0x75e998: ret
    //     0x75e998: ret             
    // 0x75e99c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75e99c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75e9a0: b               #0x75e938
  }
  _ _consecutiveTapTimerStop(/* No info */) {
    // ** addr: 0x75e9a4, size: 0x54
    // 0x75e9a4: EnterFrame
    //     0x75e9a4: stp             fp, lr, [SP, #-0x10]!
    //     0x75e9a8: mov             fp, SP
    // 0x75e9ac: AllocStack(0x8)
    //     0x75e9ac: sub             SP, SP, #8
    // 0x75e9b0: SetupParameters(_BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin this /* r1 => r0, fp-0x8 */)
    //     0x75e9b0: mov             x0, x1
    //     0x75e9b4: stur            x1, [fp, #-8]
    // 0x75e9b8: CheckStackOverflow
    //     0x75e9b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75e9bc: cmp             SP, x16
    //     0x75e9c0: b.ls            #0x75e9f0
    // 0x75e9c4: LoadField: r1 = r0->field_3b
    //     0x75e9c4: ldur            w1, [x0, #0x3b]
    // 0x75e9c8: DecompressPointer r1
    //     0x75e9c8: add             x1, x1, HEAP, lsl #32
    // 0x75e9cc: cmp             w1, NULL
    // 0x75e9d0: b.eq            #0x75e9e0
    // 0x75e9d4: r0 = cancel()
    //     0x75e9d4: bl              #0x5fe740  ; [dart:isolate] _Timer::cancel
    // 0x75e9d8: ldur            x1, [fp, #-8]
    // 0x75e9dc: StoreField: r1->field_3b = rNULL
    //     0x75e9dc: stur            NULL, [x1, #0x3b]
    // 0x75e9e0: r0 = Null
    //     0x75e9e0: mov             x0, NULL
    // 0x75e9e4: LeaveFrame
    //     0x75e9e4: mov             SP, fp
    //     0x75e9e8: ldp             fp, lr, [SP], #0x10
    // 0x75e9ec: ret
    //     0x75e9ec: ret             
    // 0x75e9f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75e9f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75e9f4: b               #0x75e9c4
  }
  _ dispose(/* No info */) {
    // ** addr: 0x7f9374, size: 0x48
    // 0x7f9374: EnterFrame
    //     0x7f9374: stp             fp, lr, [SP, #-0x10]!
    //     0x7f9378: mov             fp, SP
    // 0x7f937c: AllocStack(0x8)
    //     0x7f937c: sub             SP, SP, #8
    // 0x7f9380: SetupParameters(_BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin this /* r1 => r0, fp-0x8 */)
    //     0x7f9380: mov             x0, x1
    //     0x7f9384: stur            x1, [fp, #-8]
    // 0x7f9388: CheckStackOverflow
    //     0x7f9388: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f938c: cmp             SP, x16
    //     0x7f9390: b.ls            #0x7f93b4
    // 0x7f9394: mov             x1, x0
    // 0x7f9398: r0 = _tapTrackerReset()
    //     0x7f9398: bl              #0x75e898  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_tapTrackerReset
    // 0x7f939c: ldur            x1, [fp, #-8]
    // 0x7f93a0: r0 = dispose()
    //     0x7f93a0: bl              #0x7f93bc  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::dispose
    // 0x7f93a4: r0 = Null
    //     0x7f93a4: mov             x0, NULL
    // 0x7f93a8: LeaveFrame
    //     0x7f93a8: mov             SP, fp
    //     0x7f93ac: ldp             fp, lr, [SP], #0x10
    // 0x7f93b0: ret
    //     0x7f93b0: ret             
    // 0x7f93b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f93b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f93b8: b               #0x7f9394
  }
  _ addAllowedPointer(/* No info */) {
    // ** addr: 0x802408, size: 0xd4
    // 0x802408: EnterFrame
    //     0x802408: stp             fp, lr, [SP, #-0x10]!
    //     0x80240c: mov             fp, SP
    // 0x802410: AllocStack(0x10)
    //     0x802410: sub             SP, SP, #0x10
    // 0x802414: SetupParameters(_BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x802414: mov             x3, x1
    //     0x802418: mov             x0, x2
    //     0x80241c: stur            x1, [fp, #-8]
    //     0x802420: stur            x2, [fp, #-0x10]
    // 0x802424: CheckStackOverflow
    //     0x802424: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x802428: cmp             SP, x16
    //     0x80242c: b.ls            #0x8024d4
    // 0x802430: mov             x1, x3
    // 0x802434: mov             x2, x0
    // 0x802438: r0 = addAllowedPointer()
    //     0x802438: bl              #0x802a78  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::addAllowedPointer
    // 0x80243c: ldur            x0, [fp, #-8]
    // 0x802440: LoadField: r1 = r0->field_3b
    //     0x802440: ldur            w1, [x0, #0x3b]
    // 0x802444: DecompressPointer r1
    //     0x802444: add             x1, x1, HEAP, lsl #32
    // 0x802448: cmp             w1, NULL
    // 0x80244c: b.eq            #0x802468
    // 0x802450: LoadField: r2 = r1->field_7
    //     0x802450: ldur            w2, [x1, #7]
    // 0x802454: DecompressPointer r2
    //     0x802454: add             x2, x2, HEAP, lsl #32
    // 0x802458: cmp             w2, NULL
    // 0x80245c: b.ne            #0x802468
    // 0x802460: mov             x1, x0
    // 0x802464: r0 = _tapTrackerReset()
    //     0x802464: bl              #0x75e898  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_tapTrackerReset
    // 0x802468: ldur            x0, [fp, #-8]
    // 0x80246c: StoreField: r0->field_27 = rNULL
    //     0x80246c: stur            NULL, [x0, #0x27]
    // 0x802470: LoadField: r1 = r0->field_23
    //     0x802470: ldur            w1, [x0, #0x23]
    // 0x802474: DecompressPointer r1
    //     0x802474: add             x1, x1, HEAP, lsl #32
    // 0x802478: cmp             w1, NULL
    // 0x80247c: b.eq            #0x8024a4
    // 0x802480: mov             x1, x0
    // 0x802484: ldur            x2, [fp, #-0x10]
    // 0x802488: r0 = _representsSameSeries()
    //     0x802488: bl              #0x802674  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_representsSameSeries
    // 0x80248c: tbz             w0, #4, #0x8024a0
    // 0x802490: ldur            x0, [fp, #-8]
    // 0x802494: r1 = 1
    //     0x802494: movz            x1, #0x1
    // 0x802498: StoreField: r0->field_2b = r1
    //     0x802498: stur            x1, [x0, #0x2b]
    // 0x80249c: b               #0x8024b0
    // 0x8024a0: ldur            x0, [fp, #-8]
    // 0x8024a4: LoadField: r1 = r0->field_2b
    //     0x8024a4: ldur            x1, [x0, #0x2b]
    // 0x8024a8: add             x2, x1, #1
    // 0x8024ac: StoreField: r0->field_2b = r2
    //     0x8024ac: stur            x2, [x0, #0x2b]
    // 0x8024b0: mov             x1, x0
    // 0x8024b4: r0 = _consecutiveTapTimerStop()
    //     0x8024b4: bl              #0x75e9a4  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_consecutiveTapTimerStop
    // 0x8024b8: ldur            x1, [fp, #-8]
    // 0x8024bc: ldur            x2, [fp, #-0x10]
    // 0x8024c0: r0 = _trackTap()
    //     0x8024c0: bl              #0x8024dc  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_trackTap
    // 0x8024c4: r0 = Null
    //     0x8024c4: mov             x0, NULL
    // 0x8024c8: LeaveFrame
    //     0x8024c8: mov             SP, fp
    //     0x8024cc: ldp             fp, lr, [SP], #0x10
    // 0x8024d0: ret
    //     0x8024d0: ret             
    // 0x8024d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8024d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8024d8: b               #0x802430
  }
  _ _trackTap(/* No info */) {
    // ** addr: 0x8024dc, size: 0x198
    // 0x8024dc: EnterFrame
    //     0x8024dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8024e0: mov             fp, SP
    // 0x8024e4: AllocStack(0x20)
    //     0x8024e4: sub             SP, SP, #0x20
    // 0x8024e8: SetupParameters(_BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8024e8: mov             x3, x1
    //     0x8024ec: stur            x1, [fp, #-8]
    //     0x8024f0: stur            x2, [fp, #-0x10]
    // 0x8024f4: CheckStackOverflow
    //     0x8024f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8024f8: cmp             SP, x16
    //     0x8024fc: b.ls            #0x80266c
    // 0x802500: mov             x0, x2
    // 0x802504: StoreField: r3->field_23 = r0
    //     0x802504: stur            w0, [x3, #0x23]
    //     0x802508: ldurb           w16, [x3, #-1]
    //     0x80250c: ldurb           w17, [x0, #-1]
    //     0x802510: and             x16, x17, x16, lsr #2
    //     0x802514: tst             x16, HEAP, lsr #32
    //     0x802518: b.eq            #0x802520
    //     0x80251c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x802520: r0 = LoadClassIdInstr(r2)
    //     0x802520: ldur            x0, [x2, #-1]
    //     0x802524: ubfx            x0, x0, #0xc, #0x14
    // 0x802528: mov             x1, x2
    // 0x80252c: r0 = GDT[cid_x0 + 0x12cee]()
    //     0x80252c: movz            x17, #0x2cee
    //     0x802530: movk            x17, #0x1, lsl #16
    //     0x802534: add             lr, x0, x17
    //     0x802538: ldr             lr, [x21, lr, lsl #3]
    //     0x80253c: blr             lr
    // 0x802540: mov             x2, x0
    // 0x802544: r0 = BoxInt64Instr(r2)
    //     0x802544: sbfiz           x0, x2, #1, #0x1f
    //     0x802548: cmp             x2, x0, asr #1
    //     0x80254c: b.eq            #0x802558
    //     0x802550: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x802554: stur            x2, [x0, #7]
    // 0x802558: ldur            x2, [fp, #-8]
    // 0x80255c: StoreField: r2->field_37 = r0
    //     0x80255c: stur            w0, [x2, #0x37]
    //     0x802560: tbz             w0, #0, #0x80257c
    //     0x802564: ldurb           w16, [x2, #-1]
    //     0x802568: ldurb           w17, [x0, #-1]
    //     0x80256c: and             x16, x17, x16, lsr #2
    //     0x802570: tst             x16, HEAP, lsr #32
    //     0x802574: b.eq            #0x80257c
    //     0x802578: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80257c: ldur            x3, [fp, #-0x10]
    // 0x802580: r0 = LoadClassIdInstr(r3)
    //     0x802580: ldur            x0, [x3, #-1]
    //     0x802584: ubfx            x0, x0, #0xc, #0x14
    // 0x802588: mov             x1, x3
    // 0x80258c: r0 = GDT[cid_x0 + -0x1]()
    //     0x80258c: sub             lr, x0, #1
    //     0x802590: ldr             lr, [x21, lr, lsl #3]
    //     0x802594: blr             lr
    // 0x802598: ldur            x2, [fp, #-8]
    // 0x80259c: StoreField: r2->field_3f = r0
    //     0x80259c: stur            w0, [x2, #0x3f]
    //     0x8025a0: ldurb           w16, [x2, #-1]
    //     0x8025a4: ldurb           w17, [x0, #-1]
    //     0x8025a8: and             x16, x17, x16, lsr #2
    //     0x8025ac: tst             x16, HEAP, lsr #32
    //     0x8025b0: b.eq            #0x8025b8
    //     0x8025b4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8025b8: ldur            x3, [fp, #-0x10]
    // 0x8025bc: r0 = LoadClassIdInstr(r3)
    //     0x8025bc: ldur            x0, [x3, #-1]
    //     0x8025c0: ubfx            x0, x0, #0xc, #0x14
    // 0x8025c4: mov             x1, x3
    // 0x8025c8: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x8025c8: movz            x17, #0x30fa
    //     0x8025cc: movk            x17, #0x1, lsl #16
    //     0x8025d0: add             lr, x0, x17
    //     0x8025d4: ldr             lr, [x21, lr, lsl #3]
    //     0x8025d8: blr             lr
    // 0x8025dc: mov             x2, x0
    // 0x8025e0: ldur            x1, [fp, #-0x10]
    // 0x8025e4: stur            x2, [fp, #-0x18]
    // 0x8025e8: r0 = LoadClassIdInstr(r1)
    //     0x8025e8: ldur            x0, [x1, #-1]
    //     0x8025ec: ubfx            x0, x0, #0xc, #0x14
    // 0x8025f0: r0 = GDT[cid_x0 + -0x1]()
    //     0x8025f0: sub             lr, x0, #1
    //     0x8025f4: ldr             lr, [x21, lr, lsl #3]
    //     0x8025f8: blr             lr
    // 0x8025fc: stur            x0, [fp, #-0x10]
    // 0x802600: r0 = OffsetPair()
    //     0x802600: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x802604: mov             x1, x0
    // 0x802608: ldur            x0, [fp, #-0x18]
    // 0x80260c: StoreField: r1->field_7 = r0
    //     0x80260c: stur            w0, [x1, #7]
    // 0x802610: ldur            x0, [fp, #-0x10]
    // 0x802614: StoreField: r1->field_b = r0
    //     0x802614: stur            w0, [x1, #0xb]
    // 0x802618: mov             x0, x1
    // 0x80261c: ldur            x1, [fp, #-8]
    // 0x802620: StoreField: r1->field_33 = r0
    //     0x802620: stur            w0, [x1, #0x33]
    //     0x802624: ldurb           w16, [x1, #-1]
    //     0x802628: ldurb           w17, [x0, #-1]
    //     0x80262c: and             x16, x17, x16, lsr #2
    //     0x802630: tst             x16, HEAP, lsr #32
    //     0x802634: b.eq            #0x80263c
    //     0x802638: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80263c: LoadField: r0 = r1->field_43
    //     0x80263c: ldur            w0, [x1, #0x43]
    // 0x802640: DecompressPointer r0
    //     0x802640: add             x0, x0, HEAP, lsl #32
    // 0x802644: cmp             w0, NULL
    // 0x802648: b.eq            #0x80265c
    // 0x80264c: str             x0, [SP]
    // 0x802650: ClosureCall
    //     0x802650: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x802654: ldur            x2, [x0, #0x1f]
    //     0x802658: blr             x2
    // 0x80265c: r0 = Null
    //     0x80265c: mov             x0, NULL
    // 0x802660: LeaveFrame
    //     0x802660: mov             SP, fp
    //     0x802664: ldp             fp, lr, [SP], #0x10
    // 0x802668: ret
    //     0x802668: ret             
    // 0x80266c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80266c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x802670: b               #0x802500
  }
  _ _representsSameSeries(/* No info */) {
    // ** addr: 0x802674, size: 0xd8
    // 0x802674: EnterFrame
    //     0x802674: stp             fp, lr, [SP, #-0x10]!
    //     0x802678: mov             fp, SP
    // 0x80267c: AllocStack(0x10)
    //     0x80267c: sub             SP, SP, #0x10
    // 0x802680: SetupParameters(_BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x802680: mov             x3, x1
    //     0x802684: stur            x1, [fp, #-8]
    //     0x802688: stur            x2, [fp, #-0x10]
    // 0x80268c: CheckStackOverflow
    //     0x80268c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x802690: cmp             SP, x16
    //     0x802694: b.ls            #0x802740
    // 0x802698: LoadField: r0 = r3->field_3b
    //     0x802698: ldur            w0, [x3, #0x3b]
    // 0x80269c: DecompressPointer r0
    //     0x80269c: add             x0, x0, HEAP, lsl #32
    // 0x8026a0: cmp             w0, NULL
    // 0x8026a4: b.eq            #0x802730
    // 0x8026a8: r0 = LoadClassIdInstr(r2)
    //     0x8026a8: ldur            x0, [x2, #-1]
    //     0x8026ac: ubfx            x0, x0, #0xc, #0x14
    // 0x8026b0: mov             x1, x2
    // 0x8026b4: r0 = GDT[cid_x0 + -0x1]()
    //     0x8026b4: sub             lr, x0, #1
    //     0x8026b8: ldr             lr, [x21, lr, lsl #3]
    //     0x8026bc: blr             lr
    // 0x8026c0: ldur            x1, [fp, #-8]
    // 0x8026c4: mov             x2, x0
    // 0x8026c8: r0 = _isWithinConsecutiveTapTolerance()
    //     0x8026c8: bl              #0x80274c  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_isWithinConsecutiveTapTolerance
    // 0x8026cc: tbnz            w0, #4, #0x802730
    // 0x8026d0: ldur            x2, [fp, #-8]
    // 0x8026d4: ldur            x1, [fp, #-0x10]
    // 0x8026d8: r0 = LoadClassIdInstr(r1)
    //     0x8026d8: ldur            x0, [x1, #-1]
    //     0x8026dc: ubfx            x0, x0, #0xc, #0x14
    // 0x8026e0: r0 = GDT[cid_x0 + 0x12cee]()
    //     0x8026e0: movz            x17, #0x2cee
    //     0x8026e4: movk            x17, #0x1, lsl #16
    //     0x8026e8: add             lr, x0, x17
    //     0x8026ec: ldr             lr, [x21, lr, lsl #3]
    //     0x8026f0: blr             lr
    // 0x8026f4: ldur            x1, [fp, #-8]
    // 0x8026f8: LoadField: r2 = r1->field_37
    //     0x8026f8: ldur            w2, [x1, #0x37]
    // 0x8026fc: DecompressPointer r2
    //     0x8026fc: add             x2, x2, HEAP, lsl #32
    // 0x802700: cmp             w2, NULL
    // 0x802704: b.eq            #0x802748
    // 0x802708: r1 = LoadInt32Instr(r2)
    //     0x802708: sbfx            x1, x2, #1, #0x1f
    //     0x80270c: tbz             w2, #0, #0x802714
    //     0x802710: ldur            x1, [x2, #7]
    // 0x802714: cmp             x0, x1
    // 0x802718: b.ne            #0x802724
    // 0x80271c: r1 = true
    //     0x80271c: add             x1, NULL, #0x20  ; true
    // 0x802720: b               #0x802728
    // 0x802724: r1 = false
    //     0x802724: add             x1, NULL, #0x30  ; false
    // 0x802728: mov             x0, x1
    // 0x80272c: b               #0x802734
    // 0x802730: r0 = false
    //     0x802730: add             x0, NULL, #0x30  ; false
    // 0x802734: LeaveFrame
    //     0x802734: mov             SP, fp
    //     0x802738: ldp             fp, lr, [SP], #0x10
    // 0x80273c: ret
    //     0x80273c: ret             
    // 0x802740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x802740: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x802744: b               #0x802698
    // 0x802748: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x802748: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _isWithinConsecutiveTapTolerance(/* No info */) {
    // ** addr: 0x80274c, size: 0x80
    // 0x80274c: EnterFrame
    //     0x80274c: stp             fp, lr, [SP, #-0x10]!
    //     0x802750: mov             fp, SP
    // 0x802754: mov             x0, x1
    // 0x802758: mov             x1, x2
    // 0x80275c: CheckStackOverflow
    //     0x80275c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x802760: cmp             SP, x16
    //     0x802764: b.ls            #0x8027c4
    // 0x802768: LoadField: r2 = r0->field_3f
    //     0x802768: ldur            w2, [x0, #0x3f]
    // 0x80276c: DecompressPointer r2
    //     0x80276c: add             x2, x2, HEAP, lsl #32
    // 0x802770: cmp             w2, NULL
    // 0x802774: b.ne            #0x802788
    // 0x802778: r0 = false
    //     0x802778: add             x0, NULL, #0x30  ; false
    // 0x80277c: LeaveFrame
    //     0x80277c: mov             SP, fp
    //     0x802780: ldp             fp, lr, [SP], #0x10
    // 0x802784: ret
    //     0x802784: ret             
    // 0x802788: r0 = -()
    //     0x802788: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x80278c: LoadField: d0 = r0->field_7
    //     0x80278c: ldur            d0, [x0, #7]
    // 0x802790: fmul            d1, d0, d0
    // 0x802794: LoadField: d0 = r0->field_f
    //     0x802794: ldur            d0, [x0, #0xf]
    // 0x802798: fmul            d2, d0, d0
    // 0x80279c: fadd            d0, d1, d2
    // 0x8027a0: fsqrt           d1, d0
    // 0x8027a4: d0 = 100.000000
    //     0x8027a4: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0x8027a8: fcmp            d0, d1
    // 0x8027ac: r16 = true
    //     0x8027ac: add             x16, NULL, #0x20  ; true
    // 0x8027b0: r17 = false
    //     0x8027b0: add             x17, NULL, #0x30  ; false
    // 0x8027b4: csel            x0, x16, x17, ge
    // 0x8027b8: LeaveFrame
    //     0x8027b8: mov             SP, fp
    //     0x8027bc: ldp             fp, lr, [SP], #0x10
    // 0x8027c0: ret
    //     0x8027c0: ret             
    // 0x8027c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8027c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8027c8: b               #0x802768
  }
  _ rejectGesture(/* No info */) {
    // ** addr: 0xccedf4, size: 0x30
    // 0xccedf4: EnterFrame
    //     0xccedf4: stp             fp, lr, [SP, #-0x10]!
    //     0xccedf8: mov             fp, SP
    // 0xccedfc: CheckStackOverflow
    //     0xccedfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xccee00: cmp             SP, x16
    //     0xccee04: b.ls            #0xccee1c
    // 0xccee08: r0 = _tapTrackerReset()
    //     0xccee08: bl              #0x75e898  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_tapTrackerReset
    // 0xccee0c: r0 = Null
    //     0xccee0c: mov             x0, NULL
    // 0xccee10: LeaveFrame
    //     0xccee10: mov             SP, fp
    //     0xccee14: ldp             fp, lr, [SP], #0x10
    // 0xccee18: ret
    //     0xccee18: ret             
    // 0xccee1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xccee1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xccee20: b               #0xccee08
  }
}

// class id: 3535, size: 0xac, field offset: 0x4c
abstract class BaseTapAndDragGestureRecognizer extends _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin {

  late OffsetPair _initialPosition; // offset: 0x94
  late double _globalDistanceMoved; // offset: 0x98
  late double _globalDistanceMovedAllAxes; // offset: 0x9c

  dynamic handleEvent(dynamic) {
    // ** addr: 0x75d0f8, size: 0x24
    // 0x75d0f8: EnterFrame
    //     0x75d0f8: stp             fp, lr, [SP, #-0x10]!
    //     0x75d0fc: mov             fp, SP
    // 0x75d100: ldr             x2, [fp, #0x10]
    // 0x75d104: r1 = Function 'handleEvent':.
    //     0x75d104: add             x1, PP, #0x59, lsl #12  ; [pp+0x59088] AnonymousClosure: (0x75d11c), in [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::handleEvent (0x75d158)
    //     0x75d108: ldr             x1, [x1, #0x88]
    // 0x75d10c: r0 = AllocateClosure()
    //     0x75d10c: bl              #0xec1630  ; AllocateClosureStub
    // 0x75d110: LeaveFrame
    //     0x75d110: mov             SP, fp
    //     0x75d114: ldp             fp, lr, [SP], #0x10
    // 0x75d118: ret
    //     0x75d118: ret             
  }
  [closure] void handleEvent(dynamic, PointerEvent) {
    // ** addr: 0x75d11c, size: 0x3c
    // 0x75d11c: EnterFrame
    //     0x75d11c: stp             fp, lr, [SP, #-0x10]!
    //     0x75d120: mov             fp, SP
    // 0x75d124: ldr             x0, [fp, #0x18]
    // 0x75d128: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x75d128: ldur            w1, [x0, #0x17]
    // 0x75d12c: DecompressPointer r1
    //     0x75d12c: add             x1, x1, HEAP, lsl #32
    // 0x75d130: CheckStackOverflow
    //     0x75d130: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75d134: cmp             SP, x16
    //     0x75d138: b.ls            #0x75d150
    // 0x75d13c: ldr             x2, [fp, #0x10]
    // 0x75d140: r0 = handleEvent()
    //     0x75d140: bl              #0x75d158  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::handleEvent
    // 0x75d144: LeaveFrame
    //     0x75d144: mov             SP, fp
    //     0x75d148: ldp             fp, lr, [SP], #0x10
    // 0x75d14c: ret
    //     0x75d14c: ret             
    // 0x75d150: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75d150: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75d154: b               #0x75d13c
  }
  _ handleEvent(/* No info */) {
    // ** addr: 0x75d158, size: 0x3b0
    // 0x75d158: EnterFrame
    //     0x75d158: stp             fp, lr, [SP, #-0x10]!
    //     0x75d15c: mov             fp, SP
    // 0x75d160: AllocStack(0x18)
    //     0x75d160: sub             SP, SP, #0x18
    // 0x75d164: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x75d164: mov             x3, x1
    //     0x75d168: stur            x1, [fp, #-8]
    //     0x75d16c: stur            x2, [fp, #-0x10]
    // 0x75d170: CheckStackOverflow
    //     0x75d170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75d174: cmp             SP, x16
    //     0x75d178: b.ls            #0x75d4f4
    // 0x75d17c: r0 = LoadClassIdInstr(r2)
    //     0x75d17c: ldur            x0, [x2, #-1]
    //     0x75d180: ubfx            x0, x0, #0xc, #0x14
    // 0x75d184: mov             x1, x2
    // 0x75d188: r0 = GDT[cid_x0 + -0x1000]()
    //     0x75d188: sub             lr, x0, #1, lsl #12
    //     0x75d18c: ldr             lr, [x21, lr, lsl #3]
    //     0x75d190: blr             lr
    // 0x75d194: mov             x2, x0
    // 0x75d198: ldur            x3, [fp, #-8]
    // 0x75d19c: LoadField: r4 = r3->field_7f
    //     0x75d19c: ldur            w4, [x3, #0x7f]
    // 0x75d1a0: DecompressPointer r4
    //     0x75d1a0: add             x4, x4, HEAP, lsl #32
    // 0x75d1a4: r0 = BoxInt64Instr(r2)
    //     0x75d1a4: sbfiz           x0, x2, #1, #0x1f
    //     0x75d1a8: cmp             x2, x0, asr #1
    //     0x75d1ac: b.eq            #0x75d1b8
    //     0x75d1b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x75d1b4: stur            x2, [x0, #7]
    // 0x75d1b8: cmp             w0, w4
    // 0x75d1bc: b.eq            #0x75d208
    // 0x75d1c0: and             w16, w0, w4
    // 0x75d1c4: branchIfSmi(r16, 0x75d1f8)
    //     0x75d1c4: tbz             w16, #0, #0x75d1f8
    // 0x75d1c8: r16 = LoadClassIdInstr(r0)
    //     0x75d1c8: ldur            x16, [x0, #-1]
    //     0x75d1cc: ubfx            x16, x16, #0xc, #0x14
    // 0x75d1d0: cmp             x16, #0x3d
    // 0x75d1d4: b.ne            #0x75d1f8
    // 0x75d1d8: r16 = LoadClassIdInstr(r4)
    //     0x75d1d8: ldur            x16, [x4, #-1]
    //     0x75d1dc: ubfx            x16, x16, #0xc, #0x14
    // 0x75d1e0: cmp             x16, #0x3d
    // 0x75d1e4: b.ne            #0x75d1f8
    // 0x75d1e8: LoadField: r16 = r0->field_7
    //     0x75d1e8: ldur            x16, [x0, #7]
    // 0x75d1ec: LoadField: r17 = r4->field_7
    //     0x75d1ec: ldur            x17, [x4, #7]
    // 0x75d1f0: cmp             x16, x17
    // 0x75d1f4: b.eq            #0x75d208
    // 0x75d1f8: r0 = Null
    //     0x75d1f8: mov             x0, NULL
    // 0x75d1fc: LeaveFrame
    //     0x75d1fc: mov             SP, fp
    //     0x75d200: ldp             fp, lr, [SP], #0x10
    // 0x75d204: ret
    //     0x75d204: ret             
    // 0x75d208: mov             x1, x3
    // 0x75d20c: ldur            x2, [fp, #-0x10]
    // 0x75d210: r0 = handleEvent()
    //     0x75d210: bl              #0x75e640  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::handleEvent
    // 0x75d214: ldur            x0, [fp, #-0x10]
    // 0x75d218: r2 = Null
    //     0x75d218: mov             x2, NULL
    // 0x75d21c: r1 = Null
    //     0x75d21c: mov             x1, NULL
    // 0x75d220: cmp             w0, NULL
    // 0x75d224: b.eq            #0x75d244
    // 0x75d228: branchIfSmi(r0, 0x75d244)
    //     0x75d228: tbz             w0, #0, #0x75d244
    // 0x75d22c: r3 = LoadClassIdInstr(r0)
    //     0x75d22c: ldur            x3, [x0, #-1]
    //     0x75d230: ubfx            x3, x3, #0xc, #0x14
    // 0x75d234: cmp             x3, #0xda5
    // 0x75d238: b.eq            #0x75d24c
    // 0x75d23c: cmp             x3, #0xfcf
    // 0x75d240: b.eq            #0x75d24c
    // 0x75d244: r0 = false
    //     0x75d244: add             x0, NULL, #0x30  ; false
    // 0x75d248: b               #0x75d250
    // 0x75d24c: r0 = true
    //     0x75d24c: add             x0, NULL, #0x20  ; true
    // 0x75d250: tbnz            w0, #4, #0x75d3c0
    // 0x75d254: ldur            x2, [fp, #-8]
    // 0x75d258: ldur            x3, [fp, #-0x10]
    // 0x75d25c: r0 = LoadClassIdInstr(r3)
    //     0x75d25c: ldur            x0, [x3, #-1]
    //     0x75d260: ubfx            x0, x0, #0xc, #0x14
    // 0x75d264: mov             x1, x3
    // 0x75d268: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x75d268: movz            x17, #0x30b7
    //     0x75d26c: movk            x17, #0x1, lsl #16
    //     0x75d270: add             lr, x0, x17
    //     0x75d274: ldr             lr, [x21, lr, lsl #3]
    //     0x75d278: blr             lr
    // 0x75d27c: mov             x1, x0
    // 0x75d280: ldur            x0, [fp, #-8]
    // 0x75d284: LoadField: r2 = r0->field_7
    //     0x75d284: ldur            w2, [x0, #7]
    // 0x75d288: DecompressPointer r2
    //     0x75d288: add             x2, x2, HEAP, lsl #32
    // 0x75d28c: LoadField: r3 = r1->field_7
    //     0x75d28c: ldur            x3, [x1, #7]
    // 0x75d290: cmp             x3, #2
    // 0x75d294: b.gt            #0x75d2b0
    // 0x75d298: cmp             x3, #1
    // 0x75d29c: b.gt            #0x75d2b0
    // 0x75d2a0: cmp             x3, #0
    // 0x75d2a4: b.le            #0x75d2b0
    // 0x75d2a8: d0 = 1.000000
    //     0x75d2a8: fmov            d0, #1.00000000
    // 0x75d2ac: b               #0x75d2dc
    // 0x75d2b0: cmp             w2, NULL
    // 0x75d2b4: b.ne            #0x75d2c0
    // 0x75d2b8: r1 = Null
    //     0x75d2b8: mov             x1, NULL
    // 0x75d2bc: b               #0x75d2c8
    // 0x75d2c0: LoadField: r1 = r2->field_7
    //     0x75d2c0: ldur            w1, [x2, #7]
    // 0x75d2c4: DecompressPointer r1
    //     0x75d2c4: add             x1, x1, HEAP, lsl #32
    // 0x75d2c8: cmp             w1, NULL
    // 0x75d2cc: b.ne            #0x75d2d8
    // 0x75d2d0: d0 = 18.000000
    //     0x75d2d0: fmov            d0, #18.00000000
    // 0x75d2d4: b               #0x75d2dc
    // 0x75d2d8: LoadField: d0 = r1->field_7
    //     0x75d2d8: ldur            d0, [x1, #7]
    // 0x75d2dc: stur            d0, [fp, #-0x18]
    // 0x75d2e0: LoadField: r1 = r0->field_73
    //     0x75d2e0: ldur            w1, [x0, #0x73]
    // 0x75d2e4: DecompressPointer r1
    //     0x75d2e4: add             x1, x1, HEAP, lsl #32
    // 0x75d2e8: tbnz            w1, #4, #0x75d2f4
    // 0x75d2ec: r1 = true
    //     0x75d2ec: add             x1, NULL, #0x20  ; true
    // 0x75d2f0: b               #0x75d330
    // 0x75d2f4: LoadField: r2 = r0->field_93
    //     0x75d2f4: ldur            w2, [x0, #0x93]
    // 0x75d2f8: DecompressPointer r2
    //     0x75d2f8: add             x2, x2, HEAP, lsl #32
    // 0x75d2fc: r16 = Sentinel
    //     0x75d2fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x75d300: cmp             w2, w16
    // 0x75d304: b.eq            #0x75d4fc
    // 0x75d308: ldur            x1, [fp, #-0x10]
    // 0x75d30c: r0 = _getGlobalDistance()
    //     0x75d30c: bl              #0x75e5c4  ; [package:flutter/src/gestures/tap_and_drag.dart] ::_getGlobalDistance
    // 0x75d310: mov             v1.16b, v0.16b
    // 0x75d314: ldur            d0, [fp, #-0x18]
    // 0x75d318: fcmp            d1, d0
    // 0x75d31c: r16 = true
    //     0x75d31c: add             x16, NULL, #0x20  ; true
    // 0x75d320: r17 = false
    //     0x75d320: add             x17, NULL, #0x30  ; false
    // 0x75d324: csel            x0, x16, x17, gt
    // 0x75d328: mov             x1, x0
    // 0x75d32c: ldur            x0, [fp, #-8]
    // 0x75d330: StoreField: r0->field_73 = r1
    //     0x75d330: stur            w1, [x0, #0x73]
    // 0x75d334: LoadField: r1 = r0->field_8b
    //     0x75d334: ldur            w1, [x0, #0x8b]
    // 0x75d338: DecompressPointer r1
    //     0x75d338: add             x1, x1, HEAP, lsl #32
    // 0x75d33c: r16 = Instance__DragState
    //     0x75d33c: add             x16, PP, #0x59, lsl #12  ; [pp+0x59090] Obj!_DragState@e36c61
    //     0x75d340: ldr             x16, [x16, #0x90]
    // 0x75d344: cmp             w1, w16
    // 0x75d348: b.ne            #0x75d35c
    // 0x75d34c: mov             x1, x0
    // 0x75d350: ldur            x2, [fp, #-0x10]
    // 0x75d354: r0 = _checkDragUpdate()
    //     0x75d354: bl              #0x75e2f8  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkDragUpdate
    // 0x75d358: b               #0x75d4e4
    // 0x75d35c: r16 = Instance__DragState
    //     0x75d35c: add             x16, PP, #0x59, lsl #12  ; [pp+0x59098] Obj!_DragState@e36c41
    //     0x75d360: ldr             x16, [x16, #0x98]
    // 0x75d364: cmp             w1, w16
    // 0x75d368: b.ne            #0x75d4e4
    // 0x75d36c: LoadField: r1 = r0->field_8f
    //     0x75d36c: ldur            w1, [x0, #0x8f]
    // 0x75d370: DecompressPointer r1
    //     0x75d370: add             x1, x1, HEAP, lsl #32
    // 0x75d374: cmp             w1, NULL
    // 0x75d378: b.ne            #0x75d388
    // 0x75d37c: mov             x1, x0
    // 0x75d380: ldur            x2, [fp, #-0x10]
    // 0x75d384: r0 = _checkDrag()
    //     0x75d384: bl              #0x75dc3c  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkDrag
    // 0x75d388: ldur            x3, [fp, #-8]
    // 0x75d38c: LoadField: r2 = r3->field_8f
    //     0x75d38c: ldur            w2, [x3, #0x8f]
    // 0x75d390: DecompressPointer r2
    //     0x75d390: add             x2, x2, HEAP, lsl #32
    // 0x75d394: cmp             w2, NULL
    // 0x75d398: b.eq            #0x75d4e4
    // 0x75d39c: LoadField: r0 = r3->field_7b
    //     0x75d39c: ldur            w0, [x3, #0x7b]
    // 0x75d3a0: DecompressPointer r0
    //     0x75d3a0: add             x0, x0, HEAP, lsl #32
    // 0x75d3a4: tbnz            w0, #4, #0x75d4e4
    // 0x75d3a8: r0 = Instance__DragState
    //     0x75d3a8: add             x0, PP, #0x59, lsl #12  ; [pp+0x59090] Obj!_DragState@e36c61
    //     0x75d3ac: ldr             x0, [x0, #0x90]
    // 0x75d3b0: StoreField: r3->field_8b = r0
    //     0x75d3b0: stur            w0, [x3, #0x8b]
    // 0x75d3b4: mov             x1, x3
    // 0x75d3b8: r0 = _acceptDrag()
    //     0x75d3b8: bl              #0x75d590  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_acceptDrag
    // 0x75d3bc: b               #0x75d4e4
    // 0x75d3c0: ldur            x3, [fp, #-8]
    // 0x75d3c4: ldur            x0, [fp, #-0x10]
    // 0x75d3c8: r2 = Null
    //     0x75d3c8: mov             x2, NULL
    // 0x75d3cc: r1 = Null
    //     0x75d3cc: mov             x1, NULL
    // 0x75d3d0: cmp             w0, NULL
    // 0x75d3d4: b.eq            #0x75d3f4
    // 0x75d3d8: branchIfSmi(r0, 0x75d3f4)
    //     0x75d3d8: tbz             w0, #0, #0x75d3f4
    // 0x75d3dc: r3 = LoadClassIdInstr(r0)
    //     0x75d3dc: ldur            x3, [x0, #-1]
    //     0x75d3e0: ubfx            x3, x3, #0xc, #0x14
    // 0x75d3e4: cmp             x3, #0xda3
    // 0x75d3e8: b.eq            #0x75d3fc
    // 0x75d3ec: cmp             x3, #0xfcd
    // 0x75d3f0: b.eq            #0x75d3fc
    // 0x75d3f4: r0 = false
    //     0x75d3f4: add             x0, NULL, #0x30  ; false
    // 0x75d3f8: b               #0x75d400
    // 0x75d3fc: r0 = true
    //     0x75d3fc: add             x0, NULL, #0x20  ; true
    // 0x75d400: tbnz            w0, #4, #0x75d46c
    // 0x75d404: ldur            x2, [fp, #-8]
    // 0x75d408: LoadField: r0 = r2->field_8b
    //     0x75d408: ldur            w0, [x2, #0x8b]
    // 0x75d40c: DecompressPointer r0
    //     0x75d40c: add             x0, x0, HEAP, lsl #32
    // 0x75d410: r16 = Instance__DragState
    //     0x75d410: add             x16, PP, #0x59, lsl #12  ; [pp+0x59098] Obj!_DragState@e36c41
    //     0x75d414: ldr             x16, [x16, #0x98]
    // 0x75d418: cmp             w0, w16
    // 0x75d41c: b.ne            #0x75d430
    // 0x75d420: mov             x1, x2
    // 0x75d424: ldur            x2, [fp, #-0x10]
    // 0x75d428: r0 = stopTrackingIfPointerNoLongerDown()
    //     0x75d428: bl              #0x75b718  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::stopTrackingIfPointerNoLongerDown
    // 0x75d42c: b               #0x75d4e4
    // 0x75d430: r16 = Instance__DragState
    //     0x75d430: add             x16, PP, #0x59, lsl #12  ; [pp+0x59090] Obj!_DragState@e36c61
    //     0x75d434: ldr             x16, [x16, #0x90]
    // 0x75d438: cmp             w0, w16
    // 0x75d43c: b.ne            #0x75d4e4
    // 0x75d440: ldur            x3, [fp, #-0x10]
    // 0x75d444: r0 = LoadClassIdInstr(r3)
    //     0x75d444: ldur            x0, [x3, #-1]
    //     0x75d448: ubfx            x0, x0, #0xc, #0x14
    // 0x75d44c: mov             x1, x3
    // 0x75d450: r0 = GDT[cid_x0 + -0x1000]()
    //     0x75d450: sub             lr, x0, #1, lsl #12
    //     0x75d454: ldr             lr, [x21, lr, lsl #3]
    //     0x75d458: blr             lr
    // 0x75d45c: ldur            x1, [fp, #-8]
    // 0x75d460: mov             x2, x0
    // 0x75d464: r0 = _giveUpPointer()
    //     0x75d464: bl              #0x75d508  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_giveUpPointer
    // 0x75d468: b               #0x75d4e4
    // 0x75d46c: ldur            x3, [fp, #-0x10]
    // 0x75d470: mov             x0, x3
    // 0x75d474: r2 = Null
    //     0x75d474: mov             x2, NULL
    // 0x75d478: r1 = Null
    //     0x75d478: mov             x1, NULL
    // 0x75d47c: cmp             w0, NULL
    // 0x75d480: b.eq            #0x75d4a0
    // 0x75d484: branchIfSmi(r0, 0x75d4a0)
    //     0x75d484: tbz             w0, #0, #0x75d4a0
    // 0x75d488: r3 = LoadClassIdInstr(r0)
    //     0x75d488: ldur            x3, [x0, #-1]
    //     0x75d48c: ubfx            x3, x3, #0xc, #0x14
    // 0x75d490: cmp             x3, #0xd93
    // 0x75d494: b.eq            #0x75d4a8
    // 0x75d498: cmp             x3, #0xfc5
    // 0x75d49c: b.eq            #0x75d4a8
    // 0x75d4a0: r0 = false
    //     0x75d4a0: add             x0, NULL, #0x30  ; false
    // 0x75d4a4: b               #0x75d4ac
    // 0x75d4a8: r0 = true
    //     0x75d4a8: add             x0, NULL, #0x20  ; true
    // 0x75d4ac: tbnz            w0, #4, #0x75d4e4
    // 0x75d4b0: ldur            x2, [fp, #-8]
    // 0x75d4b4: ldur            x1, [fp, #-0x10]
    // 0x75d4b8: r0 = Instance__DragState
    //     0x75d4b8: add             x0, PP, #0x56, lsl #12  ; [pp+0x56518] Obj!_DragState@e36c81
    //     0x75d4bc: ldr             x0, [x0, #0x518]
    // 0x75d4c0: StoreField: r2->field_8b = r0
    //     0x75d4c0: stur            w0, [x2, #0x8b]
    // 0x75d4c4: r0 = LoadClassIdInstr(r1)
    //     0x75d4c4: ldur            x0, [x1, #-1]
    //     0x75d4c8: ubfx            x0, x0, #0xc, #0x14
    // 0x75d4cc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x75d4cc: sub             lr, x0, #1, lsl #12
    //     0x75d4d0: ldr             lr, [x21, lr, lsl #3]
    //     0x75d4d4: blr             lr
    // 0x75d4d8: ldur            x1, [fp, #-8]
    // 0x75d4dc: mov             x2, x0
    // 0x75d4e0: r0 = _giveUpPointer()
    //     0x75d4e0: bl              #0x75d508  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_giveUpPointer
    // 0x75d4e4: r0 = Null
    //     0x75d4e4: mov             x0, NULL
    // 0x75d4e8: LeaveFrame
    //     0x75d4e8: mov             SP, fp
    //     0x75d4ec: ldp             fp, lr, [SP], #0x10
    // 0x75d4f0: ret
    //     0x75d4f0: ret             
    // 0x75d4f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75d4f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75d4f8: b               #0x75d17c
    // 0x75d4fc: r9 = _initialPosition
    //     0x75d4fc: add             x9, PP, #0x59, lsl #12  ; [pp+0x590a0] Field <BaseTapAndDragGestureRecognizer._initialPosition@437288344>: late (offset: 0x94)
    //     0x75d500: ldr             x9, [x9, #0xa0]
    // 0x75d504: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x75d504: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
  }
  _ _giveUpPointer(/* No info */) {
    // ** addr: 0x75d508, size: 0x88
    // 0x75d508: EnterFrame
    //     0x75d508: stp             fp, lr, [SP, #-0x10]!
    //     0x75d50c: mov             fp, SP
    // 0x75d510: AllocStack(0x18)
    //     0x75d510: sub             SP, SP, #0x18
    // 0x75d514: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x75d514: mov             x4, x1
    //     0x75d518: mov             x3, x2
    //     0x75d51c: stur            x1, [fp, #-0x10]
    //     0x75d520: stur            x2, [fp, #-0x18]
    // 0x75d524: CheckStackOverflow
    //     0x75d524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75d528: cmp             SP, x16
    //     0x75d52c: b.ls            #0x75d588
    // 0x75d530: r0 = BoxInt64Instr(r3)
    //     0x75d530: sbfiz           x0, x3, #1, #0x1f
    //     0x75d534: cmp             x3, x0, asr #1
    //     0x75d538: b.eq            #0x75d544
    //     0x75d53c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x75d540: stur            x3, [x0, #7]
    // 0x75d544: mov             x1, x4
    // 0x75d548: mov             x2, x0
    // 0x75d54c: stur            x0, [fp, #-8]
    // 0x75d550: r0 = stopTrackingPointer()
    //     0x75d550: bl              #0x7587c8  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::stopTrackingPointer
    // 0x75d554: ldur            x0, [fp, #-0x10]
    // 0x75d558: LoadField: r1 = r0->field_a7
    //     0x75d558: ldur            w1, [x0, #0xa7]
    // 0x75d55c: DecompressPointer r1
    //     0x75d55c: add             x1, x1, HEAP, lsl #32
    // 0x75d560: ldur            x2, [fp, #-8]
    // 0x75d564: r0 = remove()
    //     0x75d564: bl              #0xd89c04  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x75d568: tbz             w0, #4, #0x75d578
    // 0x75d56c: ldur            x1, [fp, #-0x10]
    // 0x75d570: ldur            x2, [fp, #-0x18]
    // 0x75d574: r0 = resolvePointer()
    //     0x75d574: bl              #0x75b120  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolvePointer
    // 0x75d578: r0 = Null
    //     0x75d578: mov             x0, NULL
    // 0x75d57c: LeaveFrame
    //     0x75d57c: mov             SP, fp
    //     0x75d580: ldp             fp, lr, [SP], #0x10
    // 0x75d584: ret
    //     0x75d584: ret             
    // 0x75d588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75d588: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75d58c: b               #0x75d530
  }
  _ _acceptDrag(/* No info */) {
    // ** addr: 0x75d590, size: 0x300
    // 0x75d590: EnterFrame
    //     0x75d590: stp             fp, lr, [SP, #-0x10]!
    //     0x75d594: mov             fp, SP
    // 0x75d598: AllocStack(0x38)
    //     0x75d598: sub             SP, SP, #0x38
    // 0x75d59c: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x75d59c: mov             x3, x1
    //     0x75d5a0: stur            x1, [fp, #-0x10]
    //     0x75d5a4: stur            x2, [fp, #-0x18]
    // 0x75d5a8: CheckStackOverflow
    //     0x75d5a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75d5ac: cmp             SP, x16
    //     0x75d5b0: b.ls            #0x75d86c
    // 0x75d5b4: LoadField: r0 = r3->field_7b
    //     0x75d5b4: ldur            w0, [x3, #0x7b]
    // 0x75d5b8: DecompressPointer r0
    //     0x75d5b8: add             x0, x0, HEAP, lsl #32
    // 0x75d5bc: tbz             w0, #4, #0x75d5d0
    // 0x75d5c0: r0 = Null
    //     0x75d5c0: mov             x0, NULL
    // 0x75d5c4: LeaveFrame
    //     0x75d5c4: mov             SP, fp
    //     0x75d5c8: ldp             fp, lr, [SP], #0x10
    // 0x75d5cc: ret
    //     0x75d5cc: ret             
    // 0x75d5d0: LoadField: r0 = r3->field_4b
    //     0x75d5d0: ldur            w0, [x3, #0x4b]
    // 0x75d5d4: DecompressPointer r0
    //     0x75d5d4: add             x0, x0, HEAP, lsl #32
    // 0x75d5d8: r16 = Instance_DragStartBehavior
    //     0x75d5d8: ldr             x16, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0x75d5dc: cmp             w0, w16
    // 0x75d5e0: b.ne            #0x75d68c
    // 0x75d5e4: LoadField: r4 = r3->field_93
    //     0x75d5e4: ldur            w4, [x3, #0x93]
    // 0x75d5e8: DecompressPointer r4
    //     0x75d5e8: add             x4, x4, HEAP, lsl #32
    // 0x75d5ec: r16 = Sentinel
    //     0x75d5ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x75d5f0: cmp             w4, w16
    // 0x75d5f4: b.eq            #0x75d874
    // 0x75d5f8: stur            x4, [fp, #-8]
    // 0x75d5fc: r0 = LoadClassIdInstr(r2)
    //     0x75d5fc: ldur            x0, [x2, #-1]
    //     0x75d600: ubfx            x0, x0, #0xc, #0x14
    // 0x75d604: mov             x1, x2
    // 0x75d608: r0 = GDT[cid_x0 + 0x1326d]()
    //     0x75d608: movz            x17, #0x326d
    //     0x75d60c: movk            x17, #0x1, lsl #16
    //     0x75d610: add             lr, x0, x17
    //     0x75d614: ldr             lr, [x21, lr, lsl #3]
    //     0x75d618: blr             lr
    // 0x75d61c: mov             x3, x0
    // 0x75d620: ldur            x2, [fp, #-0x18]
    // 0x75d624: stur            x3, [fp, #-0x20]
    // 0x75d628: r0 = LoadClassIdInstr(r2)
    //     0x75d628: ldur            x0, [x2, #-1]
    //     0x75d62c: ubfx            x0, x0, #0xc, #0x14
    // 0x75d630: mov             x1, x2
    // 0x75d634: r0 = GDT[cid_x0 + 0x47d8]()
    //     0x75d634: movz            x17, #0x47d8
    //     0x75d638: add             lr, x0, x17
    //     0x75d63c: ldr             lr, [x21, lr, lsl #3]
    //     0x75d640: blr             lr
    // 0x75d644: stur            x0, [fp, #-0x28]
    // 0x75d648: r0 = OffsetPair()
    //     0x75d648: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x75d64c: mov             x1, x0
    // 0x75d650: ldur            x0, [fp, #-0x28]
    // 0x75d654: StoreField: r1->field_7 = r0
    //     0x75d654: stur            w0, [x1, #7]
    // 0x75d658: ldur            x0, [fp, #-0x20]
    // 0x75d65c: StoreField: r1->field_b = r0
    //     0x75d65c: stur            w0, [x1, #0xb]
    // 0x75d660: mov             x2, x1
    // 0x75d664: ldur            x1, [fp, #-8]
    // 0x75d668: r0 = +()
    //     0x75d668: bl              #0x75a4a8  ; [package:flutter/src/gestures/recognizer.dart] OffsetPair::+
    // 0x75d66c: ldur            x3, [fp, #-0x10]
    // 0x75d670: StoreField: r3->field_93 = r0
    //     0x75d670: stur            w0, [x3, #0x93]
    //     0x75d674: ldurb           w16, [x3, #-1]
    //     0x75d678: ldurb           w17, [x0, #-1]
    //     0x75d67c: and             x16, x17, x16, lsr #2
    //     0x75d680: tst             x16, HEAP, lsr #32
    //     0x75d684: b.eq            #0x75d68c
    //     0x75d688: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x75d68c: ldur            x0, [fp, #-0x18]
    // 0x75d690: mov             x1, x3
    // 0x75d694: mov             x2, x0
    // 0x75d698: r0 = _checkDragStart()
    //     0x75d698: bl              #0x75d890  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkDragStart
    // 0x75d69c: ldur            x2, [fp, #-0x18]
    // 0x75d6a0: r0 = LoadClassIdInstr(r2)
    //     0x75d6a0: ldur            x0, [x2, #-1]
    //     0x75d6a4: ubfx            x0, x0, #0xc, #0x14
    // 0x75d6a8: mov             x1, x2
    // 0x75d6ac: r0 = GDT[cid_x0 + 0x47d8]()
    //     0x75d6ac: movz            x17, #0x47d8
    //     0x75d6b0: add             lr, x0, x17
    //     0x75d6b4: ldr             lr, [x21, lr, lsl #3]
    //     0x75d6b8: blr             lr
    // 0x75d6bc: r16 = Instance_Offset
    //     0x75d6bc: ldr             x16, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x75d6c0: stp             x16, x0, [SP]
    // 0x75d6c4: r0 = ==()
    //     0x75d6c4: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0x75d6c8: tbz             w0, #4, #0x75d85c
    // 0x75d6cc: ldur            x2, [fp, #-0x18]
    // 0x75d6d0: r0 = LoadClassIdInstr(r2)
    //     0x75d6d0: ldur            x0, [x2, #-1]
    //     0x75d6d4: ubfx            x0, x0, #0xc, #0x14
    // 0x75d6d8: mov             x1, x2
    // 0x75d6dc: r0 = GDT[cid_x0 + 0x131b9]()
    //     0x75d6dc: movz            x17, #0x31b9
    //     0x75d6e0: movk            x17, #0x1, lsl #16
    //     0x75d6e4: add             lr, x0, x17
    //     0x75d6e8: ldr             lr, [x21, lr, lsl #3]
    //     0x75d6ec: blr             lr
    // 0x75d6f0: cmp             w0, NULL
    // 0x75d6f4: b.eq            #0x75d734
    // 0x75d6f8: ldur            x2, [fp, #-0x18]
    // 0x75d6fc: r0 = LoadClassIdInstr(r2)
    //     0x75d6fc: ldur            x0, [x2, #-1]
    //     0x75d700: ubfx            x0, x0, #0xc, #0x14
    // 0x75d704: mov             x1, x2
    // 0x75d708: r0 = GDT[cid_x0 + 0x131b9]()
    //     0x75d708: movz            x17, #0x31b9
    //     0x75d70c: movk            x17, #0x1, lsl #16
    //     0x75d710: add             lr, x0, x17
    //     0x75d714: ldr             lr, [x21, lr, lsl #3]
    //     0x75d718: blr             lr
    // 0x75d71c: cmp             w0, NULL
    // 0x75d720: b.eq            #0x75d880
    // 0x75d724: mov             x1, x0
    // 0x75d728: r0 = tryInvert()
    //     0x75d728: bl              #0x75a428  ; [package:vector_math/vector_math_64.dart] Matrix4::tryInvert
    // 0x75d72c: mov             x4, x0
    // 0x75d730: b               #0x75d738
    // 0x75d734: r4 = Null
    //     0x75d734: mov             x4, NULL
    // 0x75d738: ldur            x3, [fp, #-0x10]
    // 0x75d73c: ldur            x2, [fp, #-0x18]
    // 0x75d740: stur            x4, [fp, #-0x20]
    // 0x75d744: LoadField: r0 = r3->field_93
    //     0x75d744: ldur            w0, [x3, #0x93]
    // 0x75d748: DecompressPointer r0
    //     0x75d748: add             x0, x0, HEAP, lsl #32
    // 0x75d74c: r16 = Sentinel
    //     0x75d74c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x75d750: cmp             w0, w16
    // 0x75d754: b.eq            #0x75d884
    // 0x75d758: LoadField: r5 = r0->field_7
    //     0x75d758: ldur            w5, [x0, #7]
    // 0x75d75c: DecompressPointer r5
    //     0x75d75c: add             x5, x5, HEAP, lsl #32
    // 0x75d760: stur            x5, [fp, #-8]
    // 0x75d764: r0 = LoadClassIdInstr(r2)
    //     0x75d764: ldur            x0, [x2, #-1]
    //     0x75d768: ubfx            x0, x0, #0xc, #0x14
    // 0x75d76c: mov             x1, x2
    // 0x75d770: r0 = GDT[cid_x0 + 0x47d8]()
    //     0x75d770: movz            x17, #0x47d8
    //     0x75d774: add             lr, x0, x17
    //     0x75d778: ldr             lr, [x21, lr, lsl #3]
    //     0x75d77c: blr             lr
    // 0x75d780: ldur            x1, [fp, #-8]
    // 0x75d784: mov             x2, x0
    // 0x75d788: r0 = +()
    //     0x75d788: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x75d78c: mov             x3, x0
    // 0x75d790: ldur            x2, [fp, #-0x18]
    // 0x75d794: stur            x3, [fp, #-8]
    // 0x75d798: r0 = LoadClassIdInstr(r2)
    //     0x75d798: ldur            x0, [x2, #-1]
    //     0x75d79c: ubfx            x0, x0, #0xc, #0x14
    // 0x75d7a0: mov             x1, x2
    // 0x75d7a4: r0 = GDT[cid_x0 + 0x47d8]()
    //     0x75d7a4: movz            x17, #0x47d8
    //     0x75d7a8: add             lr, x0, x17
    //     0x75d7ac: ldr             lr, [x21, lr, lsl #3]
    //     0x75d7b0: blr             lr
    // 0x75d7b4: ldur            x1, [fp, #-0x20]
    // 0x75d7b8: mov             x2, x0
    // 0x75d7bc: ldur            x3, [fp, #-8]
    // 0x75d7c0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x75d7c0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x75d7c4: r0 = transformDeltaViaPositions()
    //     0x75d7c4: bl              #0x75a350  ; [package:flutter/src/gestures/events.dart] PointerEvent::transformDeltaViaPositions
    // 0x75d7c8: mov             x3, x0
    // 0x75d7cc: ldur            x2, [fp, #-0x18]
    // 0x75d7d0: stur            x3, [fp, #-8]
    // 0x75d7d4: r0 = LoadClassIdInstr(r2)
    //     0x75d7d4: ldur            x0, [x2, #-1]
    //     0x75d7d8: ubfx            x0, x0, #0xc, #0x14
    // 0x75d7dc: mov             x1, x2
    // 0x75d7e0: r0 = GDT[cid_x0 + 0x47d8]()
    //     0x75d7e0: movz            x17, #0x47d8
    //     0x75d7e4: add             lr, x0, x17
    //     0x75d7e8: ldr             lr, [x21, lr, lsl #3]
    //     0x75d7ec: blr             lr
    // 0x75d7f0: stur            x0, [fp, #-0x20]
    // 0x75d7f4: r0 = OffsetPair()
    //     0x75d7f4: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x75d7f8: mov             x1, x0
    // 0x75d7fc: ldur            x0, [fp, #-0x20]
    // 0x75d800: StoreField: r1->field_7 = r0
    //     0x75d800: stur            w0, [x1, #7]
    // 0x75d804: ldur            x0, [fp, #-8]
    // 0x75d808: StoreField: r1->field_b = r0
    //     0x75d808: stur            w0, [x1, #0xb]
    // 0x75d80c: ldur            x0, [fp, #-0x10]
    // 0x75d810: LoadField: r2 = r0->field_93
    //     0x75d810: ldur            w2, [x0, #0x93]
    // 0x75d814: DecompressPointer r2
    //     0x75d814: add             x2, x2, HEAP, lsl #32
    // 0x75d818: mov             x16, x1
    // 0x75d81c: mov             x1, x2
    // 0x75d820: mov             x2, x16
    // 0x75d824: r0 = +()
    //     0x75d824: bl              #0x75a4a8  ; [package:flutter/src/gestures/recognizer.dart] OffsetPair::+
    // 0x75d828: ldur            x3, [fp, #-0x10]
    // 0x75d82c: StoreField: r3->field_9f = r0
    //     0x75d82c: stur            w0, [x3, #0x9f]
    //     0x75d830: ldurb           w16, [x3, #-1]
    //     0x75d834: ldurb           w17, [x0, #-1]
    //     0x75d838: and             x16, x17, x16, lsr #2
    //     0x75d83c: tst             x16, HEAP, lsr #32
    //     0x75d840: b.eq            #0x75d848
    //     0x75d844: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x75d848: mov             x1, x3
    // 0x75d84c: ldur            x2, [fp, #-0x18]
    // 0x75d850: r0 = _checkDragUpdate()
    //     0x75d850: bl              #0x75e2f8  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkDragUpdate
    // 0x75d854: ldur            x1, [fp, #-0x10]
    // 0x75d858: StoreField: r1->field_9f = rNULL
    //     0x75d858: stur            NULL, [x1, #0x9f]
    // 0x75d85c: r0 = Null
    //     0x75d85c: mov             x0, NULL
    // 0x75d860: LeaveFrame
    //     0x75d860: mov             SP, fp
    //     0x75d864: ldp             fp, lr, [SP], #0x10
    // 0x75d868: ret
    //     0x75d868: ret             
    // 0x75d86c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75d86c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75d870: b               #0x75d5b4
    // 0x75d874: r9 = _initialPosition
    //     0x75d874: add             x9, PP, #0x59, lsl #12  ; [pp+0x590a0] Field <BaseTapAndDragGestureRecognizer._initialPosition@437288344>: late (offset: 0x94)
    //     0x75d878: ldr             x9, [x9, #0xa0]
    // 0x75d87c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x75d87c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x75d880: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75d880: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x75d884: r9 = _initialPosition
    //     0x75d884: add             x9, PP, #0x59, lsl #12  ; [pp+0x590a0] Field <BaseTapAndDragGestureRecognizer._initialPosition@437288344>: late (offset: 0x94)
    //     0x75d888: ldr             x9, [x9, #0xa0]
    // 0x75d88c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x75d88c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _checkDragStart(/* No info */) {
    // ** addr: 0x75d890, size: 0x164
    // 0x75d890: EnterFrame
    //     0x75d890: stp             fp, lr, [SP, #-0x10]!
    //     0x75d894: mov             fp, SP
    // 0x75d898: AllocStack(0x40)
    //     0x75d898: sub             SP, SP, #0x40
    // 0x75d89c: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x75d89c: mov             x0, x1
    //     0x75d8a0: stur            x1, [fp, #-8]
    //     0x75d8a4: mov             x1, x2
    //     0x75d8a8: stur            x2, [fp, #-0x10]
    // 0x75d8ac: CheckStackOverflow
    //     0x75d8ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75d8b0: cmp             SP, x16
    //     0x75d8b4: b.ls            #0x75d9e0
    // 0x75d8b8: r1 = 2
    //     0x75d8b8: movz            x1, #0x2
    // 0x75d8bc: r0 = AllocateContext()
    //     0x75d8bc: bl              #0xec126c  ; AllocateContextStub
    // 0x75d8c0: mov             x3, x0
    // 0x75d8c4: ldur            x2, [fp, #-8]
    // 0x75d8c8: stur            x3, [fp, #-0x18]
    // 0x75d8cc: StoreField: r3->field_f = r2
    //     0x75d8cc: stur            w2, [x3, #0xf]
    // 0x75d8d0: LoadField: r0 = r2->field_63
    //     0x75d8d0: ldur            w0, [x2, #0x63]
    // 0x75d8d4: DecompressPointer r0
    //     0x75d8d4: add             x0, x0, HEAP, lsl #32
    // 0x75d8d8: cmp             w0, NULL
    // 0x75d8dc: b.eq            #0x75d9c8
    // 0x75d8e0: ldur            x4, [fp, #-0x10]
    // 0x75d8e4: r0 = LoadClassIdInstr(r4)
    //     0x75d8e4: ldur            x0, [x4, #-1]
    //     0x75d8e8: ubfx            x0, x0, #0xc, #0x14
    // 0x75d8ec: mov             x1, x4
    // 0x75d8f0: r0 = GDT[cid_x0 + 0x130f9]()
    //     0x75d8f0: movz            x17, #0x30f9
    //     0x75d8f4: movk            x17, #0x1, lsl #16
    //     0x75d8f8: add             lr, x0, x17
    //     0x75d8fc: ldr             lr, [x21, lr, lsl #3]
    //     0x75d900: blr             lr
    // 0x75d904: ldur            x2, [fp, #-8]
    // 0x75d908: LoadField: r0 = r2->field_93
    //     0x75d908: ldur            w0, [x2, #0x93]
    // 0x75d90c: DecompressPointer r0
    //     0x75d90c: add             x0, x0, HEAP, lsl #32
    // 0x75d910: r16 = Sentinel
    //     0x75d910: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x75d914: cmp             w0, w16
    // 0x75d918: b.eq            #0x75d9e8
    // 0x75d91c: LoadField: r3 = r0->field_b
    //     0x75d91c: ldur            w3, [x0, #0xb]
    // 0x75d920: DecompressPointer r3
    //     0x75d920: add             x3, x3, HEAP, lsl #32
    // 0x75d924: ldur            x1, [fp, #-0x10]
    // 0x75d928: stur            x3, [fp, #-0x20]
    // 0x75d92c: r0 = LoadClassIdInstr(r1)
    //     0x75d92c: ldur            x0, [x1, #-1]
    //     0x75d930: ubfx            x0, x0, #0xc, #0x14
    // 0x75d934: r0 = GDT[cid_x0 + -0x1000]()
    //     0x75d934: sub             lr, x0, #1, lsl #12
    //     0x75d938: ldr             lr, [x21, lr, lsl #3]
    //     0x75d93c: blr             lr
    // 0x75d940: ldur            x1, [fp, #-8]
    // 0x75d944: mov             x2, x0
    // 0x75d948: r0 = getKindForPointer()
    //     0x75d948: bl              #0x75a0b4  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::getKindForPointer
    // 0x75d94c: mov             x1, x0
    // 0x75d950: ldur            x0, [fp, #-8]
    // 0x75d954: stur            x1, [fp, #-0x10]
    // 0x75d958: LoadField: r2 = r0->field_2b
    //     0x75d958: ldur            x2, [x0, #0x2b]
    // 0x75d95c: stur            x2, [fp, #-0x28]
    // 0x75d960: r0 = TapDragStartDetails()
    //     0x75d960: bl              #0x75d9f4  ; AllocateTapDragStartDetailsStub -> TapDragStartDetails (size=0x18)
    // 0x75d964: mov             x1, x0
    // 0x75d968: ldur            x0, [fp, #-0x20]
    // 0x75d96c: StoreField: r1->field_7 = r0
    //     0x75d96c: stur            w0, [x1, #7]
    // 0x75d970: ldur            x0, [fp, #-0x10]
    // 0x75d974: StoreField: r1->field_b = r0
    //     0x75d974: stur            w0, [x1, #0xb]
    // 0x75d978: ldur            x0, [fp, #-0x28]
    // 0x75d97c: StoreField: r1->field_f = r0
    //     0x75d97c: stur            x0, [x1, #0xf]
    // 0x75d980: mov             x0, x1
    // 0x75d984: ldur            x2, [fp, #-0x18]
    // 0x75d988: StoreField: r2->field_13 = r0
    //     0x75d988: stur            w0, [x2, #0x13]
    //     0x75d98c: ldurb           w16, [x2, #-1]
    //     0x75d990: ldurb           w17, [x0, #-1]
    //     0x75d994: and             x16, x17, x16, lsr #2
    //     0x75d998: tst             x16, HEAP, lsr #32
    //     0x75d99c: b.eq            #0x75d9a4
    //     0x75d9a0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x75d9a4: r1 = Function '<anonymous closure>':.
    //     0x75d9a4: add             x1, PP, #0x59, lsl #12  ; [pp+0x590a8] AnonymousClosure: (0x75da00), in [package:flutter/src/gestures/long_press.dart] LongPressGestureRecognizer::_checkLongPressMoveUpdate (0x75da70)
    //     0x75d9a8: ldr             x1, [x1, #0xa8]
    // 0x75d9ac: r0 = AllocateClosure()
    //     0x75d9ac: bl              #0xec1630  ; AllocateClosureStub
    // 0x75d9b0: r16 = <void?>
    //     0x75d9b0: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x75d9b4: ldur            lr, [fp, #-8]
    // 0x75d9b8: stp             lr, x16, [SP, #8]
    // 0x75d9bc: str             x0, [SP]
    // 0x75d9c0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x75d9c0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x75d9c4: r0 = invokeCallback()
    //     0x75d9c4: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x75d9c8: ldur            x1, [fp, #-8]
    // 0x75d9cc: StoreField: r1->field_8f = rNULL
    //     0x75d9cc: stur            NULL, [x1, #0x8f]
    // 0x75d9d0: r0 = Null
    //     0x75d9d0: mov             x0, NULL
    // 0x75d9d4: LeaveFrame
    //     0x75d9d4: mov             SP, fp
    //     0x75d9d8: ldp             fp, lr, [SP], #0x10
    // 0x75d9dc: ret
    //     0x75d9dc: ret             
    // 0x75d9e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75d9e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75d9e4: b               #0x75d8b8
    // 0x75d9e8: r9 = _initialPosition
    //     0x75d9e8: add             x9, PP, #0x59, lsl #12  ; [pp+0x590a0] Field <BaseTapAndDragGestureRecognizer._initialPosition@437288344>: late (offset: 0x94)
    //     0x75d9ec: ldr             x9, [x9, #0xa0]
    // 0x75d9f0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x75d9f0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _checkDrag(/* No info */) {
    // ** addr: 0x75dc3c, size: 0x5b8
    // 0x75dc3c: EnterFrame
    //     0x75dc3c: stp             fp, lr, [SP, #-0x10]!
    //     0x75dc40: mov             fp, SP
    // 0x75dc44: AllocStack(0x38)
    //     0x75dc44: sub             SP, SP, #0x38
    // 0x75dc48: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x75dc48: mov             x3, x1
    //     0x75dc4c: stur            x1, [fp, #-8]
    //     0x75dc50: stur            x2, [fp, #-0x10]
    // 0x75dc54: CheckStackOverflow
    //     0x75dc54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75dc58: cmp             SP, x16
    //     0x75dc5c: b.ls            #0x75e188
    // 0x75dc60: r0 = LoadClassIdInstr(r2)
    //     0x75dc60: ldur            x0, [x2, #-1]
    //     0x75dc64: ubfx            x0, x0, #0xc, #0x14
    // 0x75dc68: mov             x1, x2
    // 0x75dc6c: r0 = GDT[cid_x0 + 0x131b9]()
    //     0x75dc6c: movz            x17, #0x31b9
    //     0x75dc70: movk            x17, #0x1, lsl #16
    //     0x75dc74: add             lr, x0, x17
    //     0x75dc78: ldr             lr, [x21, lr, lsl #3]
    //     0x75dc7c: blr             lr
    // 0x75dc80: cmp             w0, NULL
    // 0x75dc84: b.ne            #0x75dc90
    // 0x75dc88: r4 = Null
    //     0x75dc88: mov             x4, NULL
    // 0x75dc8c: b               #0x75dcc8
    // 0x75dc90: ldur            x2, [fp, #-0x10]
    // 0x75dc94: r0 = LoadClassIdInstr(r2)
    //     0x75dc94: ldur            x0, [x2, #-1]
    //     0x75dc98: ubfx            x0, x0, #0xc, #0x14
    // 0x75dc9c: mov             x1, x2
    // 0x75dca0: r0 = GDT[cid_x0 + 0x131b9]()
    //     0x75dca0: movz            x17, #0x31b9
    //     0x75dca4: movk            x17, #0x1, lsl #16
    //     0x75dca8: add             lr, x0, x17
    //     0x75dcac: ldr             lr, [x21, lr, lsl #3]
    //     0x75dcb0: blr             lr
    // 0x75dcb4: cmp             w0, NULL
    // 0x75dcb8: b.eq            #0x75e190
    // 0x75dcbc: mov             x1, x0
    // 0x75dcc0: r0 = tryInvert()
    //     0x75dcc0: bl              #0x75a428  ; [package:vector_math/vector_math_64.dart] Matrix4::tryInvert
    // 0x75dcc4: mov             x4, x0
    // 0x75dcc8: ldur            x3, [fp, #-8]
    // 0x75dccc: ldur            x2, [fp, #-0x10]
    // 0x75dcd0: stur            x4, [fp, #-0x18]
    // 0x75dcd4: r0 = LoadClassIdInstr(r2)
    //     0x75dcd4: ldur            x0, [x2, #-1]
    //     0x75dcd8: ubfx            x0, x0, #0xc, #0x14
    // 0x75dcdc: mov             x1, x2
    // 0x75dce0: r0 = GDT[cid_x0 + 0x47d8]()
    //     0x75dce0: movz            x17, #0x47d8
    //     0x75dce4: add             lr, x0, x17
    //     0x75dce8: ldr             lr, [x21, lr, lsl #3]
    //     0x75dcec: blr             lr
    // 0x75dcf0: ldur            x1, [fp, #-8]
    // 0x75dcf4: r2 = LoadClassIdInstr(r1)
    //     0x75dcf4: ldur            x2, [x1, #-1]
    //     0x75dcf8: ubfx            x2, x2, #0xc, #0x14
    // 0x75dcfc: stur            x2, [fp, #-0x20]
    // 0x75dd00: cmp             x2, #0xdd0
    // 0x75dd04: b.ne            #0x75dd18
    // 0x75dd08: mov             x5, x0
    // 0x75dd0c: mov             x3, x2
    // 0x75dd10: mov             x2, x1
    // 0x75dd14: b               #0x75dd3c
    // 0x75dd18: LoadField: d0 = r0->field_7
    //     0x75dd18: ldur            d0, [x0, #7]
    // 0x75dd1c: stur            d0, [fp, #-0x38]
    // 0x75dd20: r0 = Offset()
    //     0x75dd20: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x75dd24: ldur            d0, [fp, #-0x38]
    // 0x75dd28: StoreField: r0->field_7 = d0
    //     0x75dd28: stur            d0, [x0, #7]
    // 0x75dd2c: StoreField: r0->field_f = rZR
    //     0x75dd2c: stur            xzr, [x0, #0xf]
    // 0x75dd30: mov             x5, x0
    // 0x75dd34: ldur            x2, [fp, #-8]
    // 0x75dd38: ldur            x3, [fp, #-0x20]
    // 0x75dd3c: ldur            x4, [fp, #-0x10]
    // 0x75dd40: stur            x5, [fp, #-0x30]
    // 0x75dd44: LoadField: r6 = r2->field_97
    //     0x75dd44: ldur            w6, [x2, #0x97]
    // 0x75dd48: DecompressPointer r6
    //     0x75dd48: add             x6, x6, HEAP, lsl #32
    // 0x75dd4c: r16 = Sentinel
    //     0x75dd4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x75dd50: cmp             w6, w16
    // 0x75dd54: b.eq            #0x75e194
    // 0x75dd58: stur            x6, [fp, #-0x28]
    // 0x75dd5c: r0 = LoadClassIdInstr(r4)
    //     0x75dd5c: ldur            x0, [x4, #-1]
    //     0x75dd60: ubfx            x0, x0, #0xc, #0x14
    // 0x75dd64: mov             x1, x4
    // 0x75dd68: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x75dd68: movz            x17, #0x30fa
    //     0x75dd6c: movk            x17, #0x1, lsl #16
    //     0x75dd70: add             lr, x0, x17
    //     0x75dd74: ldr             lr, [x21, lr, lsl #3]
    //     0x75dd78: blr             lr
    // 0x75dd7c: ldur            x1, [fp, #-0x18]
    // 0x75dd80: ldur            x2, [fp, #-0x30]
    // 0x75dd84: mov             x3, x0
    // 0x75dd88: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x75dd88: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x75dd8c: r0 = transformDeltaViaPositions()
    //     0x75dd8c: bl              #0x75a350  ; [package:flutter/src/gestures/events.dart] PointerEvent::transformDeltaViaPositions
    // 0x75dd90: LoadField: d0 = r0->field_7
    //     0x75dd90: ldur            d0, [x0, #7]
    // 0x75dd94: fmul            d1, d0, d0
    // 0x75dd98: LoadField: d0 = r0->field_f
    //     0x75dd98: ldur            d0, [x0, #0xf]
    // 0x75dd9c: fmul            d2, d0, d0
    // 0x75dda0: fadd            d0, d1, d2
    // 0x75dda4: fsqrt           d1, d0
    // 0x75dda8: ldur            x2, [fp, #-0x20]
    // 0x75ddac: cmp             x2, #0xdd0
    // 0x75ddb0: b.ne            #0x75ddbc
    // 0x75ddb4: r0 = Null
    //     0x75ddb4: mov             x0, NULL
    // 0x75ddb8: b               #0x75ddec
    // 0x75ddbc: ldur            x0, [fp, #-0x30]
    // 0x75ddc0: LoadField: d0 = r0->field_7
    //     0x75ddc0: ldur            d0, [x0, #7]
    // 0x75ddc4: r0 = inline_Allocate_Double()
    //     0x75ddc4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x75ddc8: add             x0, x0, #0x10
    //     0x75ddcc: cmp             x1, x0
    //     0x75ddd0: b.ls            #0x75e1a0
    //     0x75ddd4: str             x0, [THR, #0x50]  ; THR::top
    //     0x75ddd8: sub             x0, x0, #0xf
    //     0x75dddc: movz            x1, #0xe15c
    //     0x75dde0: movk            x1, #0x3, lsl #16
    //     0x75dde4: stur            x1, [x0, #-1]
    // 0x75dde8: StoreField: r0->field_7 = d0
    //     0x75dde8: stur            d0, [x0, #7]
    // 0x75ddec: cmp             w0, NULL
    // 0x75ddf0: b.ne            #0x75ddfc
    // 0x75ddf4: d2 = 1.000000
    //     0x75ddf4: fmov            d2, #1.00000000
    // 0x75ddf8: b               #0x75de04
    // 0x75ddfc: LoadField: d0 = r0->field_7
    //     0x75ddfc: ldur            d0, [x0, #7]
    // 0x75de00: mov             v2.16b, v0.16b
    // 0x75de04: d0 = 0.000000
    //     0x75de04: eor             v0.16b, v0.16b, v0.16b
    // 0x75de08: fcmp            d2, d0
    // 0x75de0c: b.le            #0x75de18
    // 0x75de10: d2 = 1.000000
    //     0x75de10: fmov            d2, #1.00000000
    // 0x75de14: b               #0x75de24
    // 0x75de18: fcmp            d0, d2
    // 0x75de1c: b.le            #0x75de24
    // 0x75de20: d2 = -1.000000
    //     0x75de20: fmov            d2, #-1.00000000
    // 0x75de24: ldur            x3, [fp, #-8]
    // 0x75de28: ldur            x4, [fp, #-0x10]
    // 0x75de2c: ldur            x0, [fp, #-0x28]
    // 0x75de30: fmul            d3, d1, d2
    // 0x75de34: LoadField: d1 = r0->field_7
    //     0x75de34: ldur            d1, [x0, #7]
    // 0x75de38: fadd            d2, d1, d3
    // 0x75de3c: r0 = inline_Allocate_Double()
    //     0x75de3c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x75de40: add             x0, x0, #0x10
    //     0x75de44: cmp             x1, x0
    //     0x75de48: b.ls            #0x75e1b8
    //     0x75de4c: str             x0, [THR, #0x50]  ; THR::top
    //     0x75de50: sub             x0, x0, #0xf
    //     0x75de54: movz            x1, #0xe15c
    //     0x75de58: movk            x1, #0x3, lsl #16
    //     0x75de5c: stur            x1, [x0, #-1]
    // 0x75de60: StoreField: r0->field_7 = d2
    //     0x75de60: stur            d2, [x0, #7]
    // 0x75de64: StoreField: r3->field_97 = r0
    //     0x75de64: stur            w0, [x3, #0x97]
    //     0x75de68: ldurb           w16, [x3, #-1]
    //     0x75de6c: ldurb           w17, [x0, #-1]
    //     0x75de70: and             x16, x17, x16, lsr #2
    //     0x75de74: tst             x16, HEAP, lsr #32
    //     0x75de78: b.eq            #0x75de80
    //     0x75de7c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x75de80: LoadField: r5 = r3->field_9b
    //     0x75de80: ldur            w5, [x3, #0x9b]
    // 0x75de84: DecompressPointer r5
    //     0x75de84: add             x5, x5, HEAP, lsl #32
    // 0x75de88: r16 = Sentinel
    //     0x75de88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x75de8c: cmp             w5, w16
    // 0x75de90: b.eq            #0x75e1d8
    // 0x75de94: stur            x5, [fp, #-0x28]
    // 0x75de98: r0 = LoadClassIdInstr(r4)
    //     0x75de98: ldur            x0, [x4, #-1]
    //     0x75de9c: ubfx            x0, x0, #0xc, #0x14
    // 0x75dea0: mov             x1, x4
    // 0x75dea4: r0 = GDT[cid_x0 + 0x47d8]()
    //     0x75dea4: movz            x17, #0x47d8
    //     0x75dea8: add             lr, x0, x17
    //     0x75deac: ldr             lr, [x21, lr, lsl #3]
    //     0x75deb0: blr             lr
    // 0x75deb4: mov             x3, x0
    // 0x75deb8: ldur            x2, [fp, #-0x10]
    // 0x75debc: stur            x3, [fp, #-0x30]
    // 0x75dec0: r0 = LoadClassIdInstr(r2)
    //     0x75dec0: ldur            x0, [x2, #-1]
    //     0x75dec4: ubfx            x0, x0, #0xc, #0x14
    // 0x75dec8: mov             x1, x2
    // 0x75decc: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x75decc: movz            x17, #0x30fa
    //     0x75ded0: movk            x17, #0x1, lsl #16
    //     0x75ded4: add             lr, x0, x17
    //     0x75ded8: ldr             lr, [x21, lr, lsl #3]
    //     0x75dedc: blr             lr
    // 0x75dee0: ldur            x1, [fp, #-0x18]
    // 0x75dee4: ldur            x2, [fp, #-0x30]
    // 0x75dee8: mov             x3, x0
    // 0x75deec: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x75deec: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x75def0: r0 = transformDeltaViaPositions()
    //     0x75def0: bl              #0x75a350  ; [package:flutter/src/gestures/events.dart] PointerEvent::transformDeltaViaPositions
    // 0x75def4: LoadField: d0 = r0->field_7
    //     0x75def4: ldur            d0, [x0, #7]
    // 0x75def8: fmul            d1, d0, d0
    // 0x75defc: LoadField: d0 = r0->field_f
    //     0x75defc: ldur            d0, [x0, #0xf]
    // 0x75df00: fmul            d2, d0, d0
    // 0x75df04: fadd            d0, d1, d2
    // 0x75df08: fsqrt           d1, d0
    // 0x75df0c: ldur            x0, [fp, #-0x28]
    // 0x75df10: LoadField: d0 = r0->field_7
    //     0x75df10: ldur            d0, [x0, #7]
    // 0x75df14: fadd            d2, d0, d1
    // 0x75df18: r0 = inline_Allocate_Double()
    //     0x75df18: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x75df1c: add             x0, x0, #0x10
    //     0x75df20: cmp             x1, x0
    //     0x75df24: b.ls            #0x75e1e4
    //     0x75df28: str             x0, [THR, #0x50]  ; THR::top
    //     0x75df2c: sub             x0, x0, #0xf
    //     0x75df30: movz            x1, #0xe15c
    //     0x75df34: movk            x1, #0x3, lsl #16
    //     0x75df38: stur            x1, [x0, #-1]
    // 0x75df3c: StoreField: r0->field_7 = d2
    //     0x75df3c: stur            d2, [x0, #7]
    // 0x75df40: ldur            x2, [fp, #-8]
    // 0x75df44: StoreField: r2->field_9b = r0
    //     0x75df44: stur            w0, [x2, #0x9b]
    //     0x75df48: ldurb           w16, [x2, #-1]
    //     0x75df4c: ldurb           w17, [x0, #-1]
    //     0x75df50: and             x16, x17, x16, lsr #2
    //     0x75df54: tst             x16, HEAP, lsr #32
    //     0x75df58: b.eq            #0x75df60
    //     0x75df5c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x75df60: ldur            x3, [fp, #-0x10]
    // 0x75df64: r0 = LoadClassIdInstr(r3)
    //     0x75df64: ldur            x0, [x3, #-1]
    //     0x75df68: ubfx            x0, x0, #0xc, #0x14
    // 0x75df6c: mov             x1, x3
    // 0x75df70: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x75df70: movz            x17, #0x30b7
    //     0x75df74: movk            x17, #0x1, lsl #16
    //     0x75df78: add             lr, x0, x17
    //     0x75df7c: ldr             lr, [x21, lr, lsl #3]
    //     0x75df80: blr             lr
    // 0x75df84: mov             x1, x0
    // 0x75df88: ldur            x0, [fp, #-0x20]
    // 0x75df8c: cmp             x0, #0xdd0
    // 0x75df90: b.ne            #0x75dffc
    // 0x75df94: ldur            x0, [fp, #-8]
    // 0x75df98: d0 = 0.000000
    //     0x75df98: eor             v0.16b, v0.16b, v0.16b
    // 0x75df9c: LoadField: r2 = r0->field_97
    //     0x75df9c: ldur            w2, [x0, #0x97]
    // 0x75dfa0: DecompressPointer r2
    //     0x75dfa0: add             x2, x2, HEAP, lsl #32
    // 0x75dfa4: LoadField: d1 = r2->field_7
    //     0x75dfa4: ldur            d1, [x2, #7]
    // 0x75dfa8: fcmp            d1, d0
    // 0x75dfac: b.ne            #0x75dfb8
    // 0x75dfb0: d1 = 0.000000
    //     0x75dfb0: eor             v1.16b, v1.16b, v1.16b
    // 0x75dfb4: b               #0x75dfc8
    // 0x75dfb8: fcmp            d0, d1
    // 0x75dfbc: b.le            #0x75dfc8
    // 0x75dfc0: fneg            d2, d1
    // 0x75dfc4: mov             v1.16b, v2.16b
    // 0x75dfc8: stur            d1, [fp, #-0x38]
    // 0x75dfcc: LoadField: r2 = r0->field_7
    //     0x75dfcc: ldur            w2, [x0, #7]
    // 0x75dfd0: DecompressPointer r2
    //     0x75dfd0: add             x2, x2, HEAP, lsl #32
    // 0x75dfd4: r0 = computePanSlop()
    //     0x75dfd4: bl              #0x75e234  ; [package:flutter/src/gestures/events.dart] ::computePanSlop
    // 0x75dfd8: mov             v1.16b, v0.16b
    // 0x75dfdc: ldur            d0, [fp, #-0x38]
    // 0x75dfe0: fcmp            d0, d1
    // 0x75dfe4: b.le            #0x75dff0
    // 0x75dfe8: ldur            x2, [fp, #-8]
    // 0x75dfec: b               #0x75e094
    // 0x75dff0: ldur            x2, [fp, #-8]
    // 0x75dff4: d0 = 0.000000
    //     0x75dff4: eor             v0.16b, v0.16b, v0.16b
    // 0x75dff8: b               #0x75e09c
    // 0x75dffc: ldur            x2, [fp, #-8]
    // 0x75e000: d0 = 0.000000
    //     0x75e000: eor             v0.16b, v0.16b, v0.16b
    // 0x75e004: LoadField: r0 = r2->field_97
    //     0x75e004: ldur            w0, [x2, #0x97]
    // 0x75e008: DecompressPointer r0
    //     0x75e008: add             x0, x0, HEAP, lsl #32
    // 0x75e00c: LoadField: d1 = r0->field_7
    //     0x75e00c: ldur            d1, [x0, #7]
    // 0x75e010: fcmp            d1, d0
    // 0x75e014: b.ne            #0x75e020
    // 0x75e018: d1 = 0.000000
    //     0x75e018: eor             v1.16b, v1.16b, v1.16b
    // 0x75e01c: b               #0x75e030
    // 0x75e020: fcmp            d0, d1
    // 0x75e024: b.le            #0x75e030
    // 0x75e028: fneg            d2, d1
    // 0x75e02c: mov             v1.16b, v2.16b
    // 0x75e030: LoadField: r0 = r2->field_7
    //     0x75e030: ldur            w0, [x2, #7]
    // 0x75e034: DecompressPointer r0
    //     0x75e034: add             x0, x0, HEAP, lsl #32
    // 0x75e038: LoadField: r3 = r1->field_7
    //     0x75e038: ldur            x3, [x1, #7]
    // 0x75e03c: cmp             x3, #2
    // 0x75e040: b.gt            #0x75e05c
    // 0x75e044: cmp             x3, #1
    // 0x75e048: b.gt            #0x75e05c
    // 0x75e04c: cmp             x3, #0
    // 0x75e050: b.le            #0x75e05c
    // 0x75e054: d2 = 1.000000
    //     0x75e054: fmov            d2, #1.00000000
    // 0x75e058: b               #0x75e08c
    // 0x75e05c: cmp             w0, NULL
    // 0x75e060: b.ne            #0x75e06c
    // 0x75e064: r0 = Null
    //     0x75e064: mov             x0, NULL
    // 0x75e068: b               #0x75e078
    // 0x75e06c: LoadField: r1 = r0->field_7
    //     0x75e06c: ldur            w1, [x0, #7]
    // 0x75e070: DecompressPointer r1
    //     0x75e070: add             x1, x1, HEAP, lsl #32
    // 0x75e074: mov             x0, x1
    // 0x75e078: cmp             w0, NULL
    // 0x75e07c: b.ne            #0x75e088
    // 0x75e080: d2 = 18.000000
    //     0x75e080: fmov            d2, #18.00000000
    // 0x75e084: b               #0x75e08c
    // 0x75e088: LoadField: d2 = r0->field_7
    //     0x75e088: ldur            d2, [x0, #7]
    // 0x75e08c: fcmp            d1, d2
    // 0x75e090: b.le            #0x75e09c
    // 0x75e094: mov             x1, x2
    // 0x75e098: b               #0x75e128
    // 0x75e09c: LoadField: r0 = r2->field_7b
    //     0x75e09c: ldur            w0, [x2, #0x7b]
    // 0x75e0a0: DecompressPointer r0
    //     0x75e0a0: add             x0, x0, HEAP, lsl #32
    // 0x75e0a4: tbnz            w0, #4, #0x75e178
    // 0x75e0a8: LoadField: r0 = r2->field_9b
    //     0x75e0a8: ldur            w0, [x2, #0x9b]
    // 0x75e0ac: DecompressPointer r0
    //     0x75e0ac: add             x0, x0, HEAP, lsl #32
    // 0x75e0b0: LoadField: d1 = r0->field_7
    //     0x75e0b0: ldur            d1, [x0, #7]
    // 0x75e0b4: fcmp            d1, d0
    // 0x75e0b8: b.ne            #0x75e0c4
    // 0x75e0bc: d0 = 0.000000
    //     0x75e0bc: eor             v0.16b, v0.16b, v0.16b
    // 0x75e0c0: b               #0x75e0d8
    // 0x75e0c4: fcmp            d0, d1
    // 0x75e0c8: b.le            #0x75e0d4
    // 0x75e0cc: fneg            d0, d1
    // 0x75e0d0: b               #0x75e0d8
    // 0x75e0d4: mov             v0.16b, v1.16b
    // 0x75e0d8: ldur            x3, [fp, #-0x10]
    // 0x75e0dc: stur            d0, [fp, #-0x38]
    // 0x75e0e0: r0 = LoadClassIdInstr(r3)
    //     0x75e0e0: ldur            x0, [x3, #-1]
    //     0x75e0e4: ubfx            x0, x0, #0xc, #0x14
    // 0x75e0e8: mov             x1, x3
    // 0x75e0ec: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x75e0ec: movz            x17, #0x30b7
    //     0x75e0f0: movk            x17, #0x1, lsl #16
    //     0x75e0f4: add             lr, x0, x17
    //     0x75e0f8: ldr             lr, [x21, lr, lsl #3]
    //     0x75e0fc: blr             lr
    // 0x75e100: mov             x1, x0
    // 0x75e104: ldur            x0, [fp, #-8]
    // 0x75e108: LoadField: r2 = r0->field_7
    //     0x75e108: ldur            w2, [x0, #7]
    // 0x75e10c: DecompressPointer r2
    //     0x75e10c: add             x2, x2, HEAP, lsl #32
    // 0x75e110: r0 = computePanSlop()
    //     0x75e110: bl              #0x75e234  ; [package:flutter/src/gestures/events.dart] ::computePanSlop
    // 0x75e114: mov             v1.16b, v0.16b
    // 0x75e118: ldur            d0, [fp, #-0x38]
    // 0x75e11c: fcmp            d0, d1
    // 0x75e120: b.le            #0x75e178
    // 0x75e124: ldur            x1, [fp, #-8]
    // 0x75e128: ldur            x0, [fp, #-0x10]
    // 0x75e12c: StoreField: r1->field_8f = r0
    //     0x75e12c: stur            w0, [x1, #0x8f]
    //     0x75e130: ldurb           w16, [x1, #-1]
    //     0x75e134: ldurb           w17, [x0, #-1]
    //     0x75e138: and             x16, x17, x16, lsr #2
    //     0x75e13c: tst             x16, HEAP, lsr #32
    //     0x75e140: b.eq            #0x75e148
    //     0x75e144: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x75e148: LoadField: r0 = r1->field_57
    //     0x75e148: ldur            w0, [x1, #0x57]
    // 0x75e14c: DecompressPointer r0
    //     0x75e14c: add             x0, x0, HEAP, lsl #32
    // 0x75e150: tbnz            w0, #4, #0x75e178
    // 0x75e154: r0 = Instance__DragState
    //     0x75e154: add             x0, PP, #0x59, lsl #12  ; [pp+0x59090] Obj!_DragState@e36c61
    //     0x75e158: ldr             x0, [x0, #0x90]
    // 0x75e15c: StoreField: r1->field_8b = r0
    //     0x75e15c: stur            w0, [x1, #0x8b]
    // 0x75e160: LoadField: r0 = r1->field_7b
    //     0x75e160: ldur            w0, [x1, #0x7b]
    // 0x75e164: DecompressPointer r0
    //     0x75e164: add             x0, x0, HEAP, lsl #32
    // 0x75e168: tbz             w0, #4, #0x75e178
    // 0x75e16c: r2 = Instance_GestureDisposition
    //     0x75e16c: add             x2, PP, #0x30, lsl #12  ; [pp+0x30e00] Obj!GestureDisposition@e36ee1
    //     0x75e170: ldr             x2, [x2, #0xe00]
    // 0x75e174: r0 = resolve()
    //     0x75e174: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x75e178: r0 = Null
    //     0x75e178: mov             x0, NULL
    // 0x75e17c: LeaveFrame
    //     0x75e17c: mov             SP, fp
    //     0x75e180: ldp             fp, lr, [SP], #0x10
    // 0x75e184: ret
    //     0x75e184: ret             
    // 0x75e188: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75e188: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75e18c: b               #0x75dc60
    // 0x75e190: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75e190: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x75e194: r9 = _globalDistanceMoved
    //     0x75e194: add             x9, PP, #0x59, lsl #12  ; [pp+0x590b0] Field <BaseTapAndDragGestureRecognizer._globalDistanceMoved@437288344>: late (offset: 0x98)
    //     0x75e198: ldr             x9, [x9, #0xb0]
    // 0x75e19c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x75e19c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x75e1a0: stp             q0, q1, [SP, #-0x20]!
    // 0x75e1a4: SaveReg r2
    //     0x75e1a4: str             x2, [SP, #-8]!
    // 0x75e1a8: r0 = AllocateDouble()
    //     0x75e1a8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75e1ac: RestoreReg r2
    //     0x75e1ac: ldr             x2, [SP], #8
    // 0x75e1b0: ldp             q0, q1, [SP], #0x20
    // 0x75e1b4: b               #0x75dde8
    // 0x75e1b8: stp             q0, q2, [SP, #-0x20]!
    // 0x75e1bc: stp             x3, x4, [SP, #-0x10]!
    // 0x75e1c0: SaveReg r2
    //     0x75e1c0: str             x2, [SP, #-8]!
    // 0x75e1c4: r0 = AllocateDouble()
    //     0x75e1c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75e1c8: RestoreReg r2
    //     0x75e1c8: ldr             x2, [SP], #8
    // 0x75e1cc: ldp             x3, x4, [SP], #0x10
    // 0x75e1d0: ldp             q0, q2, [SP], #0x20
    // 0x75e1d4: b               #0x75de60
    // 0x75e1d8: r9 = _globalDistanceMovedAllAxes
    //     0x75e1d8: add             x9, PP, #0x59, lsl #12  ; [pp+0x590b8] Field <BaseTapAndDragGestureRecognizer._globalDistanceMovedAllAxes@437288344>: late (offset: 0x9c)
    //     0x75e1dc: ldr             x9, [x9, #0xb8]
    // 0x75e1e0: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x75e1e0: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x75e1e4: SaveReg d2
    //     0x75e1e4: str             q2, [SP, #-0x10]!
    // 0x75e1e8: r0 = AllocateDouble()
    //     0x75e1e8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75e1ec: RestoreReg d2
    //     0x75e1ec: ldr             q2, [SP], #0x10
    // 0x75e1f0: b               #0x75df3c
  }
  _ _checkDragUpdate(/* No info */) {
    // ** addr: 0x75e2f8, size: 0x250
    // 0x75e2f8: EnterFrame
    //     0x75e2f8: stp             fp, lr, [SP, #-0x10]!
    //     0x75e2fc: mov             fp, SP
    // 0x75e300: AllocStack(0x50)
    //     0x75e300: sub             SP, SP, #0x50
    // 0x75e304: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x75e304: mov             x0, x1
    //     0x75e308: stur            x1, [fp, #-8]
    //     0x75e30c: mov             x1, x2
    //     0x75e310: stur            x2, [fp, #-0x10]
    // 0x75e314: CheckStackOverflow
    //     0x75e314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75e318: cmp             SP, x16
    //     0x75e31c: b.ls            #0x75e534
    // 0x75e320: r1 = 2
    //     0x75e320: movz            x1, #0x2
    // 0x75e324: r0 = AllocateContext()
    //     0x75e324: bl              #0xec126c  ; AllocateContextStub
    // 0x75e328: mov             x3, x0
    // 0x75e32c: ldur            x2, [fp, #-8]
    // 0x75e330: stur            x3, [fp, #-0x18]
    // 0x75e334: StoreField: r3->field_f = r2
    //     0x75e334: stur            w2, [x3, #0xf]
    // 0x75e338: LoadField: r0 = r2->field_9f
    //     0x75e338: ldur            w0, [x2, #0x9f]
    // 0x75e33c: DecompressPointer r0
    //     0x75e33c: add             x0, x0, HEAP, lsl #32
    // 0x75e340: cmp             w0, NULL
    // 0x75e344: b.eq            #0x75e358
    // 0x75e348: LoadField: r1 = r0->field_b
    //     0x75e348: ldur            w1, [x0, #0xb]
    // 0x75e34c: DecompressPointer r1
    //     0x75e34c: add             x1, x1, HEAP, lsl #32
    // 0x75e350: mov             x3, x1
    // 0x75e354: b               #0x75e37c
    // 0x75e358: ldur            x4, [fp, #-0x10]
    // 0x75e35c: r0 = LoadClassIdInstr(r4)
    //     0x75e35c: ldur            x0, [x4, #-1]
    //     0x75e360: ubfx            x0, x0, #0xc, #0x14
    // 0x75e364: mov             x1, x4
    // 0x75e368: r0 = GDT[cid_x0 + -0x1]()
    //     0x75e368: sub             lr, x0, #1
    //     0x75e36c: ldr             lr, [x21, lr, lsl #3]
    //     0x75e370: blr             lr
    // 0x75e374: mov             x3, x0
    // 0x75e378: ldur            x2, [fp, #-8]
    // 0x75e37c: stur            x3, [fp, #-0x20]
    // 0x75e380: LoadField: r0 = r2->field_9f
    //     0x75e380: ldur            w0, [x2, #0x9f]
    // 0x75e384: DecompressPointer r0
    //     0x75e384: add             x0, x0, HEAP, lsl #32
    // 0x75e388: cmp             w0, NULL
    // 0x75e38c: b.eq            #0x75e3a0
    // 0x75e390: LoadField: r1 = r0->field_7
    //     0x75e390: ldur            w1, [x0, #7]
    // 0x75e394: DecompressPointer r1
    //     0x75e394: add             x1, x1, HEAP, lsl #32
    // 0x75e398: mov             x6, x1
    // 0x75e39c: b               #0x75e3d0
    // 0x75e3a0: ldur            x4, [fp, #-0x10]
    // 0x75e3a4: r0 = LoadClassIdInstr(r4)
    //     0x75e3a4: ldur            x0, [x4, #-1]
    //     0x75e3a8: ubfx            x0, x0, #0xc, #0x14
    // 0x75e3ac: mov             x1, x4
    // 0x75e3b0: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x75e3b0: movz            x17, #0x30fa
    //     0x75e3b4: movk            x17, #0x1, lsl #16
    //     0x75e3b8: add             lr, x0, x17
    //     0x75e3bc: ldr             lr, [x21, lr, lsl #3]
    //     0x75e3c0: blr             lr
    // 0x75e3c4: mov             x6, x0
    // 0x75e3c8: ldur            x2, [fp, #-8]
    // 0x75e3cc: ldur            x3, [fp, #-0x20]
    // 0x75e3d0: ldur            x4, [fp, #-0x10]
    // 0x75e3d4: ldur            x5, [fp, #-0x18]
    // 0x75e3d8: stur            x6, [fp, #-0x28]
    // 0x75e3dc: r0 = LoadClassIdInstr(r4)
    //     0x75e3dc: ldur            x0, [x4, #-1]
    //     0x75e3e0: ubfx            x0, x0, #0xc, #0x14
    // 0x75e3e4: mov             x1, x4
    // 0x75e3e8: r0 = GDT[cid_x0 + 0x130f9]()
    //     0x75e3e8: movz            x17, #0x30f9
    //     0x75e3ec: movk            x17, #0x1, lsl #16
    //     0x75e3f0: add             lr, x0, x17
    //     0x75e3f4: ldr             lr, [x21, lr, lsl #3]
    //     0x75e3f8: blr             lr
    // 0x75e3fc: ldur            x2, [fp, #-0x10]
    // 0x75e400: r0 = LoadClassIdInstr(r2)
    //     0x75e400: ldur            x0, [x2, #-1]
    //     0x75e404: ubfx            x0, x0, #0xc, #0x14
    // 0x75e408: mov             x1, x2
    // 0x75e40c: r0 = GDT[cid_x0 + 0x47d8]()
    //     0x75e40c: movz            x17, #0x47d8
    //     0x75e410: add             lr, x0, x17
    //     0x75e414: ldr             lr, [x21, lr, lsl #3]
    //     0x75e418: blr             lr
    // 0x75e41c: ldur            x1, [fp, #-0x10]
    // 0x75e420: r0 = LoadClassIdInstr(r1)
    //     0x75e420: ldur            x0, [x1, #-1]
    //     0x75e424: ubfx            x0, x0, #0xc, #0x14
    // 0x75e428: r0 = GDT[cid_x0 + -0x1000]()
    //     0x75e428: sub             lr, x0, #1, lsl #12
    //     0x75e42c: ldr             lr, [x21, lr, lsl #3]
    //     0x75e430: blr             lr
    // 0x75e434: ldur            x1, [fp, #-8]
    // 0x75e438: mov             x2, x0
    // 0x75e43c: r0 = getKindForPointer()
    //     0x75e43c: bl              #0x75a0b4  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::getKindForPointer
    // 0x75e440: mov             x3, x0
    // 0x75e444: ldur            x0, [fp, #-8]
    // 0x75e448: stur            x3, [fp, #-0x10]
    // 0x75e44c: LoadField: r1 = r0->field_93
    //     0x75e44c: ldur            w1, [x0, #0x93]
    // 0x75e450: DecompressPointer r1
    //     0x75e450: add             x1, x1, HEAP, lsl #32
    // 0x75e454: r16 = Sentinel
    //     0x75e454: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x75e458: cmp             w1, w16
    // 0x75e45c: b.eq            #0x75e53c
    // 0x75e460: LoadField: r2 = r1->field_b
    //     0x75e460: ldur            w2, [x1, #0xb]
    // 0x75e464: DecompressPointer r2
    //     0x75e464: add             x2, x2, HEAP, lsl #32
    // 0x75e468: ldur            x1, [fp, #-0x20]
    // 0x75e46c: r0 = -()
    //     0x75e46c: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x75e470: mov             x3, x0
    // 0x75e474: ldur            x0, [fp, #-8]
    // 0x75e478: stur            x3, [fp, #-0x30]
    // 0x75e47c: LoadField: r1 = r0->field_93
    //     0x75e47c: ldur            w1, [x0, #0x93]
    // 0x75e480: DecompressPointer r1
    //     0x75e480: add             x1, x1, HEAP, lsl #32
    // 0x75e484: LoadField: r2 = r1->field_7
    //     0x75e484: ldur            w2, [x1, #7]
    // 0x75e488: DecompressPointer r2
    //     0x75e488: add             x2, x2, HEAP, lsl #32
    // 0x75e48c: ldur            x1, [fp, #-0x28]
    // 0x75e490: r0 = -()
    //     0x75e490: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x75e494: ldur            x0, [fp, #-8]
    // 0x75e498: LoadField: r1 = r0->field_2b
    //     0x75e498: ldur            x1, [x0, #0x2b]
    // 0x75e49c: stur            x1, [fp, #-0x38]
    // 0x75e4a0: r0 = TapDragUpdateDetails()
    //     0x75e4a0: bl              #0x75e548  ; AllocateTapDragUpdateDetailsStub -> TapDragUpdateDetails (size=0x1c)
    // 0x75e4a4: mov             x1, x0
    // 0x75e4a8: ldur            x0, [fp, #-0x20]
    // 0x75e4ac: StoreField: r1->field_7 = r0
    //     0x75e4ac: stur            w0, [x1, #7]
    // 0x75e4b0: ldur            x0, [fp, #-0x10]
    // 0x75e4b4: StoreField: r1->field_b = r0
    //     0x75e4b4: stur            w0, [x1, #0xb]
    // 0x75e4b8: ldur            x0, [fp, #-0x30]
    // 0x75e4bc: StoreField: r1->field_f = r0
    //     0x75e4bc: stur            w0, [x1, #0xf]
    // 0x75e4c0: ldur            x0, [fp, #-0x38]
    // 0x75e4c4: StoreField: r1->field_13 = r0
    //     0x75e4c4: stur            x0, [x1, #0x13]
    // 0x75e4c8: mov             x0, x1
    // 0x75e4cc: ldur            x2, [fp, #-0x18]
    // 0x75e4d0: StoreField: r2->field_13 = r0
    //     0x75e4d0: stur            w0, [x2, #0x13]
    //     0x75e4d4: ldurb           w16, [x2, #-1]
    //     0x75e4d8: ldurb           w17, [x0, #-1]
    //     0x75e4dc: and             x16, x17, x16, lsr #2
    //     0x75e4e0: tst             x16, HEAP, lsr #32
    //     0x75e4e4: b.eq            #0x75e4ec
    //     0x75e4e8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x75e4ec: ldur            x0, [fp, #-8]
    // 0x75e4f0: LoadField: r1 = r0->field_67
    //     0x75e4f0: ldur            w1, [x0, #0x67]
    // 0x75e4f4: DecompressPointer r1
    //     0x75e4f4: add             x1, x1, HEAP, lsl #32
    // 0x75e4f8: cmp             w1, NULL
    // 0x75e4fc: b.eq            #0x75e524
    // 0x75e500: r1 = Function '<anonymous closure>':.
    //     0x75e500: add             x1, PP, #0x59, lsl #12  ; [pp+0x590c0] AnonymousClosure: (0x75e554), in [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkDragUpdate (0x75e2f8)
    //     0x75e504: ldr             x1, [x1, #0xc0]
    // 0x75e508: r0 = AllocateClosure()
    //     0x75e508: bl              #0xec1630  ; AllocateClosureStub
    // 0x75e50c: r16 = <void?>
    //     0x75e50c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x75e510: ldur            lr, [fp, #-8]
    // 0x75e514: stp             lr, x16, [SP, #8]
    // 0x75e518: str             x0, [SP]
    // 0x75e51c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x75e51c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x75e520: r0 = invokeCallback()
    //     0x75e520: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x75e524: r0 = Null
    //     0x75e524: mov             x0, NULL
    // 0x75e528: LeaveFrame
    //     0x75e528: mov             SP, fp
    //     0x75e52c: ldp             fp, lr, [SP], #0x10
    // 0x75e530: ret
    //     0x75e530: ret             
    // 0x75e534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75e534: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75e538: b               #0x75e320
    // 0x75e53c: r9 = _initialPosition
    //     0x75e53c: add             x9, PP, #0x59, lsl #12  ; [pp+0x590a0] Field <BaseTapAndDragGestureRecognizer._initialPosition@437288344>: late (offset: 0x94)
    //     0x75e540: ldr             x9, [x9, #0xa0]
    // 0x75e544: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x75e544: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x75e554, size: 0x70
    // 0x75e554: EnterFrame
    //     0x75e554: stp             fp, lr, [SP, #-0x10]!
    //     0x75e558: mov             fp, SP
    // 0x75e55c: AllocStack(0x10)
    //     0x75e55c: sub             SP, SP, #0x10
    // 0x75e560: SetupParameters()
    //     0x75e560: ldr             x0, [fp, #0x10]
    //     0x75e564: ldur            w1, [x0, #0x17]
    //     0x75e568: add             x1, x1, HEAP, lsl #32
    // 0x75e56c: CheckStackOverflow
    //     0x75e56c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75e570: cmp             SP, x16
    //     0x75e574: b.ls            #0x75e5b8
    // 0x75e578: LoadField: r0 = r1->field_f
    //     0x75e578: ldur            w0, [x1, #0xf]
    // 0x75e57c: DecompressPointer r0
    //     0x75e57c: add             x0, x0, HEAP, lsl #32
    // 0x75e580: LoadField: r2 = r0->field_67
    //     0x75e580: ldur            w2, [x0, #0x67]
    // 0x75e584: DecompressPointer r2
    //     0x75e584: add             x2, x2, HEAP, lsl #32
    // 0x75e588: cmp             w2, NULL
    // 0x75e58c: b.eq            #0x75e5c0
    // 0x75e590: LoadField: r0 = r1->field_13
    //     0x75e590: ldur            w0, [x1, #0x13]
    // 0x75e594: DecompressPointer r0
    //     0x75e594: add             x0, x0, HEAP, lsl #32
    // 0x75e598: stp             x0, x2, [SP]
    // 0x75e59c: mov             x0, x2
    // 0x75e5a0: ClosureCall
    //     0x75e5a0: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x75e5a4: ldur            x2, [x0, #0x1f]
    //     0x75e5a8: blr             x2
    // 0x75e5ac: LeaveFrame
    //     0x75e5ac: mov             SP, fp
    //     0x75e5b0: ldp             fp, lr, [SP], #0x10
    // 0x75e5b4: ret
    //     0x75e5b4: ret             
    // 0x75e5b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75e5b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75e5bc: b               #0x75e578
    // 0x75e5c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75e5c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ handleNonAllowedPointer(/* No info */) {
    // ** addr: 0x7b1b9c, size: 0x7c
    // 0x7b1b9c: EnterFrame
    //     0x7b1b9c: stp             fp, lr, [SP, #-0x10]!
    //     0x7b1ba0: mov             fp, SP
    // 0x7b1ba4: AllocStack(0x10)
    //     0x7b1ba4: sub             SP, SP, #0x10
    // 0x7b1ba8: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x7b1ba8: mov             x3, x1
    //     0x7b1bac: stur            x1, [fp, #-8]
    //     0x7b1bb0: stur            x2, [fp, #-0x10]
    // 0x7b1bb4: CheckStackOverflow
    //     0x7b1bb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b1bb8: cmp             SP, x16
    //     0x7b1bbc: b.ls            #0x7b1c10
    // 0x7b1bc0: r0 = LoadClassIdInstr(r2)
    //     0x7b1bc0: ldur            x0, [x2, #-1]
    //     0x7b1bc4: ubfx            x0, x0, #0xc, #0x14
    // 0x7b1bc8: mov             x1, x2
    // 0x7b1bcc: r0 = GDT[cid_x0 + 0x12cee]()
    //     0x7b1bcc: movz            x17, #0x2cee
    //     0x7b1bd0: movk            x17, #0x1, lsl #16
    //     0x7b1bd4: add             lr, x0, x17
    //     0x7b1bd8: ldr             lr, [x21, lr, lsl #3]
    //     0x7b1bdc: blr             lr
    // 0x7b1be0: cmp             x0, #1
    // 0x7b1be4: b.eq            #0x7b1c00
    // 0x7b1be8: ldur            x1, [fp, #-8]
    // 0x7b1bec: LoadField: r0 = r1->field_7b
    //     0x7b1bec: ldur            w0, [x1, #0x7b]
    // 0x7b1bf0: DecompressPointer r0
    //     0x7b1bf0: add             x0, x0, HEAP, lsl #32
    // 0x7b1bf4: tbz             w0, #4, #0x7b1c00
    // 0x7b1bf8: ldur            x2, [fp, #-0x10]
    // 0x7b1bfc: r0 = handleNonAllowedPointer()
    //     0x7b1bfc: bl              #0x7b1c18  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::handleNonAllowedPointer
    // 0x7b1c00: r0 = Null
    //     0x7b1c00: mov             x0, NULL
    // 0x7b1c04: LeaveFrame
    //     0x7b1c04: mov             SP, fp
    //     0x7b1c08: ldp             fp, lr, [SP], #0x10
    // 0x7b1c0c: ret
    //     0x7b1c0c: ret             
    // 0x7b1c10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b1c10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b1c14: b               #0x7b1bc0
  }
  _ didStopTrackingLastPointer(/* No info */) {
    // ** addr: 0x7d7614, size: 0x180
    // 0x7d7614: EnterFrame
    //     0x7d7614: stp             fp, lr, [SP, #-0x10]!
    //     0x7d7618: mov             fp, SP
    // 0x7d761c: AllocStack(0x10)
    //     0x7d761c: sub             SP, SP, #0x10
    // 0x7d7620: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x7d7620: mov             x0, x1
    //     0x7d7624: mov             x3, x2
    //     0x7d7628: stur            x1, [fp, #-8]
    //     0x7d762c: stur            x2, [fp, #-0x10]
    // 0x7d7630: CheckStackOverflow
    //     0x7d7630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d7634: cmp             SP, x16
    //     0x7d7638: b.ls            #0x7d7788
    // 0x7d763c: LoadField: r1 = r0->field_8b
    //     0x7d763c: ldur            w1, [x0, #0x8b]
    // 0x7d7640: DecompressPointer r1
    //     0x7d7640: add             x1, x1, HEAP, lsl #32
    // 0x7d7644: LoadField: r2 = r1->field_7
    //     0x7d7644: ldur            x2, [x1, #7]
    // 0x7d7648: cmp             x2, #1
    // 0x7d764c: b.gt            #0x7d7748
    // 0x7d7650: cmp             x2, #0
    // 0x7d7654: b.gt            #0x7d7674
    // 0x7d7658: mov             x1, x0
    // 0x7d765c: r0 = _checkCancel()
    //     0x7d765c: bl              #0x7d7ca8  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkCancel
    // 0x7d7660: ldur            x1, [fp, #-8]
    // 0x7d7664: r2 = Instance_GestureDisposition
    //     0x7d7664: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0x7d7668: ldr             x2, [x2, #0xde8]
    // 0x7d766c: r0 = resolve()
    //     0x7d766c: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x7d7670: b               #0x7d7750
    // 0x7d7674: mov             x4, x0
    // 0x7d7678: LoadField: r0 = r4->field_73
    //     0x7d7678: ldur            w0, [x4, #0x73]
    // 0x7d767c: DecompressPointer r0
    //     0x7d767c: add             x0, x0, HEAP, lsl #32
    // 0x7d7680: tbnz            w0, #4, #0x7d7728
    // 0x7d7684: LoadField: r0 = r4->field_7b
    //     0x7d7684: ldur            w0, [x4, #0x7b]
    // 0x7d7688: DecompressPointer r0
    //     0x7d7688: add             x0, x0, HEAP, lsl #32
    // 0x7d768c: tbnz            w0, #4, #0x7d770c
    // 0x7d7690: LoadField: r0 = r4->field_23
    //     0x7d7690: ldur            w0, [x4, #0x23]
    // 0x7d7694: DecompressPointer r0
    //     0x7d7694: add             x0, x0, HEAP, lsl #32
    // 0x7d7698: cmp             w0, NULL
    // 0x7d769c: b.eq            #0x7d7750
    // 0x7d76a0: LoadField: r2 = r4->field_a7
    //     0x7d76a0: ldur            w2, [x4, #0xa7]
    // 0x7d76a4: DecompressPointer r2
    //     0x7d76a4: add             x2, x2, HEAP, lsl #32
    // 0x7d76a8: r0 = BoxInt64Instr(r3)
    //     0x7d76a8: sbfiz           x0, x3, #1, #0x1f
    //     0x7d76ac: cmp             x3, x0, asr #1
    //     0x7d76b0: b.eq            #0x7d76bc
    //     0x7d76b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7d76b8: stur            x3, [x0, #7]
    // 0x7d76bc: mov             x1, x2
    // 0x7d76c0: mov             x2, x0
    // 0x7d76c4: r0 = remove()
    //     0x7d76c4: bl              #0xd89c04  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x7d76c8: tbz             w0, #4, #0x7d76d8
    // 0x7d76cc: ldur            x1, [fp, #-8]
    // 0x7d76d0: ldur            x2, [fp, #-0x10]
    // 0x7d76d4: r0 = resolvePointer()
    //     0x7d76d4: bl              #0x75b120  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolvePointer
    // 0x7d76d8: ldur            x0, [fp, #-8]
    // 0x7d76dc: r1 = Instance__DragState
    //     0x7d76dc: add             x1, PP, #0x59, lsl #12  ; [pp+0x59090] Obj!_DragState@e36c61
    //     0x7d76e0: ldr             x1, [x1, #0x90]
    // 0x7d76e4: StoreField: r0->field_8b = r1
    //     0x7d76e4: stur            w1, [x0, #0x8b]
    // 0x7d76e8: LoadField: r2 = r0->field_23
    //     0x7d76e8: ldur            w2, [x0, #0x23]
    // 0x7d76ec: DecompressPointer r2
    //     0x7d76ec: add             x2, x2, HEAP, lsl #32
    // 0x7d76f0: cmp             w2, NULL
    // 0x7d76f4: b.eq            #0x7d7790
    // 0x7d76f8: mov             x1, x0
    // 0x7d76fc: r0 = _acceptDrag()
    //     0x7d76fc: bl              #0x75d590  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_acceptDrag
    // 0x7d7700: ldur            x1, [fp, #-8]
    // 0x7d7704: r0 = _checkDragEnd()
    //     0x7d7704: bl              #0x7d7b84  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkDragEnd
    // 0x7d7708: b               #0x7d7750
    // 0x7d770c: ldur            x1, [fp, #-8]
    // 0x7d7710: r0 = _checkCancel()
    //     0x7d7710: bl              #0x7d7ca8  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkCancel
    // 0x7d7714: ldur            x1, [fp, #-8]
    // 0x7d7718: r2 = Instance_GestureDisposition
    //     0x7d7718: add             x2, PP, #0x30, lsl #12  ; [pp+0x30de8] Obj!GestureDisposition@e36ec1
    //     0x7d771c: ldr             x2, [x2, #0xde8]
    // 0x7d7720: r0 = resolve()
    //     0x7d7720: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x7d7724: b               #0x7d7750
    // 0x7d7728: mov             x0, x4
    // 0x7d772c: LoadField: r2 = r0->field_27
    //     0x7d772c: ldur            w2, [x0, #0x27]
    // 0x7d7730: DecompressPointer r2
    //     0x7d7730: add             x2, x2, HEAP, lsl #32
    // 0x7d7734: cmp             w2, NULL
    // 0x7d7738: b.eq            #0x7d7750
    // 0x7d773c: mov             x1, x0
    // 0x7d7740: r0 = _checkTapUp()
    //     0x7d7740: bl              #0x7d77e8  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkTapUp
    // 0x7d7744: b               #0x7d7750
    // 0x7d7748: ldur            x1, [fp, #-8]
    // 0x7d774c: r0 = _checkDragEnd()
    //     0x7d774c: bl              #0x7d7b84  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkDragEnd
    // 0x7d7750: ldur            x0, [fp, #-8]
    // 0x7d7754: mov             x1, x0
    // 0x7d7758: r0 = _stopDeadlineTimer()
    //     0x7d7758: bl              #0x7d7794  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_stopDeadlineTimer
    // 0x7d775c: ldur            x1, [fp, #-8]
    // 0x7d7760: StoreField: r1->field_8f = rNULL
    //     0x7d7760: stur            NULL, [x1, #0x8f]
    // 0x7d7764: r2 = Instance__DragState
    //     0x7d7764: add             x2, PP, #0x56, lsl #12  ; [pp+0x56518] Obj!_DragState@e36c81
    //     0x7d7768: ldr             x2, [x2, #0x518]
    // 0x7d776c: StoreField: r1->field_8b = r2
    //     0x7d776c: stur            w2, [x1, #0x8b]
    // 0x7d7770: r2 = false
    //     0x7d7770: add             x2, NULL, #0x30  ; false
    // 0x7d7774: StoreField: r1->field_73 = r2
    //     0x7d7774: stur            w2, [x1, #0x73]
    // 0x7d7778: r0 = Null
    //     0x7d7778: mov             x0, NULL
    // 0x7d777c: LeaveFrame
    //     0x7d777c: mov             SP, fp
    //     0x7d7780: ldp             fp, lr, [SP], #0x10
    // 0x7d7784: ret
    //     0x7d7784: ret             
    // 0x7d7788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d7788: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d778c: b               #0x7d763c
    // 0x7d7790: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d7790: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _stopDeadlineTimer(/* No info */) {
    // ** addr: 0x7d7794, size: 0x54
    // 0x7d7794: EnterFrame
    //     0x7d7794: stp             fp, lr, [SP, #-0x10]!
    //     0x7d7798: mov             fp, SP
    // 0x7d779c: AllocStack(0x8)
    //     0x7d779c: sub             SP, SP, #8
    // 0x7d77a0: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r0, fp-0x8 */)
    //     0x7d77a0: mov             x0, x1
    //     0x7d77a4: stur            x1, [fp, #-8]
    // 0x7d77a8: CheckStackOverflow
    //     0x7d77a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d77ac: cmp             SP, x16
    //     0x7d77b0: b.ls            #0x7d77e0
    // 0x7d77b4: LoadField: r1 = r0->field_83
    //     0x7d77b4: ldur            w1, [x0, #0x83]
    // 0x7d77b8: DecompressPointer r1
    //     0x7d77b8: add             x1, x1, HEAP, lsl #32
    // 0x7d77bc: cmp             w1, NULL
    // 0x7d77c0: b.eq            #0x7d77d0
    // 0x7d77c4: r0 = cancel()
    //     0x7d77c4: bl              #0x5fe740  ; [dart:isolate] _Timer::cancel
    // 0x7d77c8: ldur            x1, [fp, #-8]
    // 0x7d77cc: StoreField: r1->field_83 = rNULL
    //     0x7d77cc: stur            NULL, [x1, #0x83]
    // 0x7d77d0: r0 = Null
    //     0x7d77d0: mov             x0, NULL
    // 0x7d77d4: LeaveFrame
    //     0x7d77d4: mov             SP, fp
    //     0x7d77d8: ldp             fp, lr, [SP], #0x10
    // 0x7d77dc: ret
    //     0x7d77dc: ret             
    // 0x7d77e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d77e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d77e4: b               #0x7d77b4
  }
  _ _checkTapUp(/* No info */) {
    // ** addr: 0x7d77e8, size: 0x1f8
    // 0x7d77e8: EnterFrame
    //     0x7d77e8: stp             fp, lr, [SP, #-0x10]!
    //     0x7d77ec: mov             fp, SP
    // 0x7d77f0: AllocStack(0x48)
    //     0x7d77f0: sub             SP, SP, #0x48
    // 0x7d77f4: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x7d77f4: mov             x0, x1
    //     0x7d77f8: stur            x1, [fp, #-8]
    //     0x7d77fc: mov             x1, x2
    //     0x7d7800: stur            x2, [fp, #-0x10]
    // 0x7d7804: CheckStackOverflow
    //     0x7d7804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d7808: cmp             SP, x16
    //     0x7d780c: b.ls            #0x7d79d8
    // 0x7d7810: r1 = 2
    //     0x7d7810: movz            x1, #0x2
    // 0x7d7814: r0 = AllocateContext()
    //     0x7d7814: bl              #0xec126c  ; AllocateContextStub
    // 0x7d7818: mov             x3, x0
    // 0x7d781c: ldur            x2, [fp, #-8]
    // 0x7d7820: stur            x3, [fp, #-0x18]
    // 0x7d7824: StoreField: r3->field_f = r2
    //     0x7d7824: stur            w2, [x3, #0xf]
    // 0x7d7828: LoadField: r0 = r2->field_7b
    //     0x7d7828: ldur            w0, [x2, #0x7b]
    // 0x7d782c: DecompressPointer r0
    //     0x7d782c: add             x0, x0, HEAP, lsl #32
    // 0x7d7830: tbz             w0, #4, #0x7d7844
    // 0x7d7834: r0 = Null
    //     0x7d7834: mov             x0, NULL
    // 0x7d7838: LeaveFrame
    //     0x7d7838: mov             SP, fp
    //     0x7d783c: ldp             fp, lr, [SP], #0x10
    // 0x7d7840: ret
    //     0x7d7840: ret             
    // 0x7d7844: ldur            x4, [fp, #-0x10]
    // 0x7d7848: r0 = LoadClassIdInstr(r4)
    //     0x7d7848: ldur            x0, [x4, #-1]
    //     0x7d784c: ubfx            x0, x0, #0xc, #0x14
    // 0x7d7850: mov             x1, x4
    // 0x7d7854: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x7d7854: movz            x17, #0x30b7
    //     0x7d7858: movk            x17, #0x1, lsl #16
    //     0x7d785c: add             lr, x0, x17
    //     0x7d7860: ldr             lr, [x21, lr, lsl #3]
    //     0x7d7864: blr             lr
    // 0x7d7868: mov             x3, x0
    // 0x7d786c: ldur            x2, [fp, #-0x10]
    // 0x7d7870: stur            x3, [fp, #-0x20]
    // 0x7d7874: r0 = LoadClassIdInstr(r2)
    //     0x7d7874: ldur            x0, [x2, #-1]
    //     0x7d7878: ubfx            x0, x0, #0xc, #0x14
    // 0x7d787c: mov             x1, x2
    // 0x7d7880: r0 = GDT[cid_x0 + -0x1]()
    //     0x7d7880: sub             lr, x0, #1
    //     0x7d7884: ldr             lr, [x21, lr, lsl #3]
    //     0x7d7888: blr             lr
    // 0x7d788c: mov             x3, x0
    // 0x7d7890: ldur            x2, [fp, #-0x10]
    // 0x7d7894: stur            x3, [fp, #-0x28]
    // 0x7d7898: r0 = LoadClassIdInstr(r2)
    //     0x7d7898: ldur            x0, [x2, #-1]
    //     0x7d789c: ubfx            x0, x0, #0xc, #0x14
    // 0x7d78a0: mov             x1, x2
    // 0x7d78a4: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x7d78a4: movz            x17, #0x30fa
    //     0x7d78a8: movk            x17, #0x1, lsl #16
    //     0x7d78ac: add             lr, x0, x17
    //     0x7d78b0: ldr             lr, [x21, lr, lsl #3]
    //     0x7d78b4: blr             lr
    // 0x7d78b8: ldur            x1, [fp, #-8]
    // 0x7d78bc: LoadField: r0 = r1->field_2b
    //     0x7d78bc: ldur            x0, [x1, #0x2b]
    // 0x7d78c0: stur            x0, [fp, #-0x30]
    // 0x7d78c4: r0 = TapDragUpDetails()
    //     0x7d78c4: bl              #0x7d79f8  ; AllocateTapDragUpDetailsStub -> TapDragUpDetails (size=0x18)
    // 0x7d78c8: mov             x1, x0
    // 0x7d78cc: ldur            x0, [fp, #-0x20]
    // 0x7d78d0: StoreField: r1->field_b = r0
    //     0x7d78d0: stur            w0, [x1, #0xb]
    // 0x7d78d4: ldur            x0, [fp, #-0x28]
    // 0x7d78d8: StoreField: r1->field_7 = r0
    //     0x7d78d8: stur            w0, [x1, #7]
    // 0x7d78dc: ldur            x0, [fp, #-0x30]
    // 0x7d78e0: StoreField: r1->field_f = r0
    //     0x7d78e0: stur            x0, [x1, #0xf]
    // 0x7d78e4: mov             x0, x1
    // 0x7d78e8: ldur            x2, [fp, #-0x18]
    // 0x7d78ec: StoreField: r2->field_13 = r0
    //     0x7d78ec: stur            w0, [x2, #0x13]
    //     0x7d78f0: ldurb           w16, [x2, #-1]
    //     0x7d78f4: ldurb           w17, [x0, #-1]
    //     0x7d78f8: and             x16, x17, x16, lsr #2
    //     0x7d78fc: tst             x16, HEAP, lsr #32
    //     0x7d7900: b.eq            #0x7d7908
    //     0x7d7904: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7d7908: ldur            x0, [fp, #-8]
    // 0x7d790c: LoadField: r1 = r0->field_5f
    //     0x7d790c: ldur            w1, [x0, #0x5f]
    // 0x7d7910: DecompressPointer r1
    //     0x7d7910: add             x1, x1, HEAP, lsl #32
    // 0x7d7914: cmp             w1, NULL
    // 0x7d7918: b.eq            #0x7d7940
    // 0x7d791c: r1 = Function '<anonymous closure>':.
    //     0x7d791c: add             x1, PP, #0x59, lsl #12  ; [pp+0x590d0] AnonymousClosure: (0x7d7a04), in [package:flutter/src/gestures/long_press.dart] LongPressGestureRecognizer::_checkLongPressStart (0x7d7a74)
    //     0x7d7920: ldr             x1, [x1, #0xd0]
    // 0x7d7924: r0 = AllocateClosure()
    //     0x7d7924: bl              #0xec1630  ; AllocateClosureStub
    // 0x7d7928: r16 = <void?>
    //     0x7d7928: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x7d792c: ldur            lr, [fp, #-8]
    // 0x7d7930: stp             lr, x16, [SP, #8]
    // 0x7d7934: str             x0, [SP]
    // 0x7d7938: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7d7938: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7d793c: r0 = invokeCallback()
    //     0x7d793c: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x7d7940: ldur            x0, [fp, #-8]
    // 0x7d7944: ldur            x2, [fp, #-0x10]
    // 0x7d7948: mov             x1, x0
    // 0x7d794c: r0 = _resetTaps()
    //     0x7d794c: bl              #0x7d79e0  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_resetTaps
    // 0x7d7950: ldur            x2, [fp, #-8]
    // 0x7d7954: LoadField: r3 = r2->field_a7
    //     0x7d7954: ldur            w3, [x2, #0xa7]
    // 0x7d7958: DecompressPointer r3
    //     0x7d7958: add             x3, x3, HEAP, lsl #32
    // 0x7d795c: ldur            x4, [fp, #-0x10]
    // 0x7d7960: stur            x3, [fp, #-0x18]
    // 0x7d7964: r0 = LoadClassIdInstr(r4)
    //     0x7d7964: ldur            x0, [x4, #-1]
    //     0x7d7968: ubfx            x0, x0, #0xc, #0x14
    // 0x7d796c: mov             x1, x4
    // 0x7d7970: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7d7970: sub             lr, x0, #1, lsl #12
    //     0x7d7974: ldr             lr, [x21, lr, lsl #3]
    //     0x7d7978: blr             lr
    // 0x7d797c: mov             x2, x0
    // 0x7d7980: r0 = BoxInt64Instr(r2)
    //     0x7d7980: sbfiz           x0, x2, #1, #0x1f
    //     0x7d7984: cmp             x2, x0, asr #1
    //     0x7d7988: b.eq            #0x7d7994
    //     0x7d798c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7d7990: stur            x2, [x0, #7]
    // 0x7d7994: ldur            x1, [fp, #-0x18]
    // 0x7d7998: mov             x2, x0
    // 0x7d799c: r0 = remove()
    //     0x7d799c: bl              #0xd89c04  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x7d79a0: tbz             w0, #4, #0x7d79c8
    // 0x7d79a4: ldur            x1, [fp, #-0x10]
    // 0x7d79a8: r0 = LoadClassIdInstr(r1)
    //     0x7d79a8: ldur            x0, [x1, #-1]
    //     0x7d79ac: ubfx            x0, x0, #0xc, #0x14
    // 0x7d79b0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7d79b0: sub             lr, x0, #1, lsl #12
    //     0x7d79b4: ldr             lr, [x21, lr, lsl #3]
    //     0x7d79b8: blr             lr
    // 0x7d79bc: ldur            x1, [fp, #-8]
    // 0x7d79c0: mov             x2, x0
    // 0x7d79c4: r0 = resolvePointer()
    //     0x7d79c4: bl              #0x75b120  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolvePointer
    // 0x7d79c8: r0 = Null
    //     0x7d79c8: mov             x0, NULL
    // 0x7d79cc: LeaveFrame
    //     0x7d79cc: mov             SP, fp
    //     0x7d79d0: ldp             fp, lr, [SP], #0x10
    // 0x7d79d4: ret
    //     0x7d79d4: ret             
    // 0x7d79d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d79d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d79dc: b               #0x7d7810
  }
  _ _resetTaps(/* No info */) {
    // ** addr: 0x7d79e0, size: 0x18
    // 0x7d79e0: r2 = false
    //     0x7d79e0: add             x2, NULL, #0x30  ; false
    // 0x7d79e4: StoreField: r1->field_77 = r2
    //     0x7d79e4: stur            w2, [x1, #0x77]
    // 0x7d79e8: StoreField: r1->field_7b = r2
    //     0x7d79e8: stur            w2, [x1, #0x7b]
    // 0x7d79ec: StoreField: r1->field_7f = rNULL
    //     0x7d79ec: stur            NULL, [x1, #0x7f]
    // 0x7d79f0: r0 = Null
    //     0x7d79f0: mov             x0, NULL
    // 0x7d79f4: ret
    //     0x7d79f4: ret             
  }
  _ _checkDragEnd(/* No info */) {
    // ** addr: 0x7d7b84, size: 0xa8
    // 0x7d7b84: EnterFrame
    //     0x7d7b84: stp             fp, lr, [SP, #-0x10]!
    //     0x7d7b88: mov             fp, SP
    // 0x7d7b8c: AllocStack(0x30)
    //     0x7d7b8c: sub             SP, SP, #0x30
    // 0x7d7b90: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r1, fp-0x8 */)
    //     0x7d7b90: stur            x1, [fp, #-8]
    // 0x7d7b94: CheckStackOverflow
    //     0x7d7b94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d7b98: cmp             SP, x16
    //     0x7d7b9c: b.ls            #0x7d7c24
    // 0x7d7ba0: r1 = 2
    //     0x7d7ba0: movz            x1, #0x2
    // 0x7d7ba4: r0 = AllocateContext()
    //     0x7d7ba4: bl              #0xec126c  ; AllocateContextStub
    // 0x7d7ba8: ldur            x1, [fp, #-8]
    // 0x7d7bac: stur            x0, [fp, #-0x18]
    // 0x7d7bb0: StoreField: r0->field_f = r1
    //     0x7d7bb0: stur            w1, [x0, #0xf]
    // 0x7d7bb4: LoadField: r2 = r1->field_2b
    //     0x7d7bb4: ldur            x2, [x1, #0x2b]
    // 0x7d7bb8: stur            x2, [fp, #-0x10]
    // 0x7d7bbc: r0 = TapDragEndDetails()
    //     0x7d7bbc: bl              #0x7d7c2c  ; AllocateTapDragEndDetailsStub -> TapDragEndDetails (size=0x10)
    // 0x7d7bc0: mov             x1, x0
    // 0x7d7bc4: ldur            x0, [fp, #-0x10]
    // 0x7d7bc8: StoreField: r1->field_7 = r0
    //     0x7d7bc8: stur            x0, [x1, #7]
    // 0x7d7bcc: ldur            x2, [fp, #-0x18]
    // 0x7d7bd0: StoreField: r2->field_13 = r1
    //     0x7d7bd0: stur            w1, [x2, #0x13]
    // 0x7d7bd4: ldur            x0, [fp, #-8]
    // 0x7d7bd8: LoadField: r1 = r0->field_6b
    //     0x7d7bd8: ldur            w1, [x0, #0x6b]
    // 0x7d7bdc: DecompressPointer r1
    //     0x7d7bdc: add             x1, x1, HEAP, lsl #32
    // 0x7d7be0: cmp             w1, NULL
    // 0x7d7be4: b.eq            #0x7d7c0c
    // 0x7d7be8: r1 = Function '<anonymous closure>':.
    //     0x7d7be8: add             x1, PP, #0x59, lsl #12  ; [pp+0x590d8] AnonymousClosure: (0x7d7c38), in [package:flutter/src/gestures/tap.dart] TapGestureRecognizer::handleTapDown (0xd82a98)
    //     0x7d7bec: ldr             x1, [x1, #0xd8]
    // 0x7d7bf0: r0 = AllocateClosure()
    //     0x7d7bf0: bl              #0xec1630  ; AllocateClosureStub
    // 0x7d7bf4: r16 = <void?>
    //     0x7d7bf4: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x7d7bf8: ldur            lr, [fp, #-8]
    // 0x7d7bfc: stp             lr, x16, [SP, #8]
    // 0x7d7c00: str             x0, [SP]
    // 0x7d7c04: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7d7c04: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7d7c08: r0 = invokeCallback()
    //     0x7d7c08: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x7d7c0c: ldur            x1, [fp, #-8]
    // 0x7d7c10: r0 = _resetTaps()
    //     0x7d7c10: bl              #0x7d79e0  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_resetTaps
    // 0x7d7c14: r0 = Null
    //     0x7d7c14: mov             x0, NULL
    // 0x7d7c18: LeaveFrame
    //     0x7d7c18: mov             SP, fp
    //     0x7d7c1c: ldp             fp, lr, [SP], #0x10
    // 0x7d7c20: ret
    //     0x7d7c20: ret             
    // 0x7d7c24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d7c24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d7c28: b               #0x7d7ba0
  }
  _ _checkCancel(/* No info */) {
    // ** addr: 0x7d7ca8, size: 0x7c
    // 0x7d7ca8: EnterFrame
    //     0x7d7ca8: stp             fp, lr, [SP, #-0x10]!
    //     0x7d7cac: mov             fp, SP
    // 0x7d7cb0: AllocStack(0x20)
    //     0x7d7cb0: sub             SP, SP, #0x20
    // 0x7d7cb4: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r1, fp-0x8 */)
    //     0x7d7cb4: stur            x1, [fp, #-8]
    // 0x7d7cb8: CheckStackOverflow
    //     0x7d7cb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d7cbc: cmp             SP, x16
    //     0x7d7cc0: b.ls            #0x7d7d1c
    // 0x7d7cc4: LoadField: r0 = r1->field_77
    //     0x7d7cc4: ldur            w0, [x1, #0x77]
    // 0x7d7cc8: DecompressPointer r0
    //     0x7d7cc8: add             x0, x0, HEAP, lsl #32
    // 0x7d7ccc: tbz             w0, #4, #0x7d7ce0
    // 0x7d7cd0: r0 = Null
    //     0x7d7cd0: mov             x0, NULL
    // 0x7d7cd4: LeaveFrame
    //     0x7d7cd4: mov             SP, fp
    //     0x7d7cd8: ldp             fp, lr, [SP], #0x10
    // 0x7d7cdc: ret
    //     0x7d7cdc: ret             
    // 0x7d7ce0: LoadField: r0 = r1->field_6f
    //     0x7d7ce0: ldur            w0, [x1, #0x6f]
    // 0x7d7ce4: DecompressPointer r0
    //     0x7d7ce4: add             x0, x0, HEAP, lsl #32
    // 0x7d7ce8: cmp             w0, NULL
    // 0x7d7cec: b.eq            #0x7d7d04
    // 0x7d7cf0: r16 = <void?>
    //     0x7d7cf0: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x7d7cf4: stp             x1, x16, [SP, #8]
    // 0x7d7cf8: str             x0, [SP]
    // 0x7d7cfc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7d7cfc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7d7d00: r0 = invokeCallback()
    //     0x7d7d00: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x7d7d04: ldur            x1, [fp, #-8]
    // 0x7d7d08: r0 = _resetTaps()
    //     0x7d7d08: bl              #0x7d79e0  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_resetTaps
    // 0x7d7d0c: r0 = Null
    //     0x7d7d0c: mov             x0, NULL
    // 0x7d7d10: LeaveFrame
    //     0x7d7d10: mov             SP, fp
    //     0x7d7d14: ldp             fp, lr, [SP], #0x10
    // 0x7d7d18: ret
    //     0x7d7d18: ret             
    // 0x7d7d1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d7d1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d7d20: b               #0x7d7cc4
  }
  _ dispose(/* No info */) {
    // ** addr: 0x7f932c, size: 0x48
    // 0x7f932c: EnterFrame
    //     0x7f932c: stp             fp, lr, [SP, #-0x10]!
    //     0x7f9330: mov             fp, SP
    // 0x7f9334: AllocStack(0x8)
    //     0x7f9334: sub             SP, SP, #8
    // 0x7f9338: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r0, fp-0x8 */)
    //     0x7f9338: mov             x0, x1
    //     0x7f933c: stur            x1, [fp, #-8]
    // 0x7f9340: CheckStackOverflow
    //     0x7f9340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f9344: cmp             SP, x16
    //     0x7f9348: b.ls            #0x7f936c
    // 0x7f934c: mov             x1, x0
    // 0x7f9350: r0 = _stopDeadlineTimer()
    //     0x7f9350: bl              #0x7d7794  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_stopDeadlineTimer
    // 0x7f9354: ldur            x1, [fp, #-8]
    // 0x7f9358: r0 = dispose()
    //     0x7f9358: bl              #0x7f9374  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::dispose
    // 0x7f935c: r0 = Null
    //     0x7f935c: mov             x0, NULL
    // 0x7f9360: LeaveFrame
    //     0x7f9360: mov             SP, fp
    //     0x7f9364: ldp             fp, lr, [SP], #0x10
    // 0x7f9368: ret
    //     0x7f9368: ret             
    // 0x7f936c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f936c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f9370: b               #0x7f934c
  }
  _ addAllowedPointer(/* No info */) {
    // ** addr: 0x80225c, size: 0x1ac
    // 0x80225c: EnterFrame
    //     0x80225c: stp             fp, lr, [SP, #-0x10]!
    //     0x802260: mov             fp, SP
    // 0x802264: AllocStack(0x20)
    //     0x802264: sub             SP, SP, #0x20
    // 0x802268: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x802268: stur            x1, [fp, #-8]
    //     0x80226c: stur            x2, [fp, #-0x10]
    // 0x802270: CheckStackOverflow
    //     0x802270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x802274: cmp             SP, x16
    //     0x802278: b.ls            #0x802400
    // 0x80227c: r1 = 1
    //     0x80227c: movz            x1, #0x1
    // 0x802280: r0 = AllocateContext()
    //     0x802280: bl              #0xec126c  ; AllocateContextStub
    // 0x802284: mov             x3, x0
    // 0x802288: ldur            x0, [fp, #-8]
    // 0x80228c: stur            x3, [fp, #-0x18]
    // 0x802290: StoreField: r3->field_f = r0
    //     0x802290: stur            w0, [x3, #0xf]
    // 0x802294: LoadField: r1 = r0->field_8b
    //     0x802294: ldur            w1, [x0, #0x8b]
    // 0x802298: DecompressPointer r1
    //     0x802298: add             x1, x1, HEAP, lsl #32
    // 0x80229c: r16 = Instance__DragState
    //     0x80229c: add             x16, PP, #0x56, lsl #12  ; [pp+0x56518] Obj!_DragState@e36c81
    //     0x8022a0: ldr             x16, [x16, #0x518]
    // 0x8022a4: cmp             w1, w16
    // 0x8022a8: b.ne            #0x8023f0
    // 0x8022ac: ldur            x4, [fp, #-0x10]
    // 0x8022b0: mov             x1, x0
    // 0x8022b4: mov             x2, x4
    // 0x8022b8: r0 = addAllowedPointer()
    //     0x8022b8: bl              #0x802408  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::addAllowedPointer
    // 0x8022bc: ldur            x2, [fp, #-0x10]
    // 0x8022c0: r0 = LoadClassIdInstr(r2)
    //     0x8022c0: ldur            x0, [x2, #-1]
    //     0x8022c4: ubfx            x0, x0, #0xc, #0x14
    // 0x8022c8: mov             x1, x2
    // 0x8022cc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8022cc: sub             lr, x0, #1, lsl #12
    //     0x8022d0: ldr             lr, [x21, lr, lsl #3]
    //     0x8022d4: blr             lr
    // 0x8022d8: mov             x2, x0
    // 0x8022dc: r0 = BoxInt64Instr(r2)
    //     0x8022dc: sbfiz           x0, x2, #1, #0x1f
    //     0x8022e0: cmp             x2, x0, asr #1
    //     0x8022e4: b.eq            #0x8022f0
    //     0x8022e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8022ec: stur            x2, [x0, #7]
    // 0x8022f0: ldur            x2, [fp, #-8]
    // 0x8022f4: StoreField: r2->field_7f = r0
    //     0x8022f4: stur            w0, [x2, #0x7f]
    //     0x8022f8: tbz             w0, #0, #0x802314
    //     0x8022fc: ldurb           w16, [x2, #-1]
    //     0x802300: ldurb           w17, [x0, #-1]
    //     0x802304: and             x16, x17, x16, lsr #2
    //     0x802308: tst             x16, HEAP, lsr #32
    //     0x80230c: b.eq            #0x802314
    //     0x802310: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x802314: r0 = 0.000000
    //     0x802314: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x802318: StoreField: r2->field_97 = r0
    //     0x802318: stur            w0, [x2, #0x97]
    // 0x80231c: StoreField: r2->field_9b = r0
    //     0x80231c: stur            w0, [x2, #0x9b]
    // 0x802320: r0 = Instance__DragState
    //     0x802320: add             x0, PP, #0x59, lsl #12  ; [pp+0x59098] Obj!_DragState@e36c41
    //     0x802324: ldr             x0, [x0, #0x98]
    // 0x802328: StoreField: r2->field_8b = r0
    //     0x802328: stur            w0, [x2, #0x8b]
    // 0x80232c: ldur            x3, [fp, #-0x10]
    // 0x802330: r0 = LoadClassIdInstr(r3)
    //     0x802330: ldur            x0, [x3, #-1]
    //     0x802334: ubfx            x0, x0, #0xc, #0x14
    // 0x802338: mov             x1, x3
    // 0x80233c: r0 = GDT[cid_x0 + -0x1]()
    //     0x80233c: sub             lr, x0, #1
    //     0x802340: ldr             lr, [x21, lr, lsl #3]
    //     0x802344: blr             lr
    // 0x802348: mov             x2, x0
    // 0x80234c: ldur            x1, [fp, #-0x10]
    // 0x802350: stur            x2, [fp, #-0x20]
    // 0x802354: r0 = LoadClassIdInstr(r1)
    //     0x802354: ldur            x0, [x1, #-1]
    //     0x802358: ubfx            x0, x0, #0xc, #0x14
    // 0x80235c: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x80235c: movz            x17, #0x30fa
    //     0x802360: movk            x17, #0x1, lsl #16
    //     0x802364: add             lr, x0, x17
    //     0x802368: ldr             lr, [x21, lr, lsl #3]
    //     0x80236c: blr             lr
    // 0x802370: stur            x0, [fp, #-0x10]
    // 0x802374: r0 = OffsetPair()
    //     0x802374: bl              #0x75afe0  ; AllocateOffsetPairStub -> OffsetPair (size=0x10)
    // 0x802378: mov             x1, x0
    // 0x80237c: ldur            x0, [fp, #-0x10]
    // 0x802380: StoreField: r1->field_7 = r0
    //     0x802380: stur            w0, [x1, #7]
    // 0x802384: ldur            x0, [fp, #-0x20]
    // 0x802388: StoreField: r1->field_b = r0
    //     0x802388: stur            w0, [x1, #0xb]
    // 0x80238c: mov             x0, x1
    // 0x802390: ldur            x3, [fp, #-8]
    // 0x802394: StoreField: r3->field_93 = r0
    //     0x802394: stur            w0, [x3, #0x93]
    //     0x802398: ldurb           w16, [x3, #-1]
    //     0x80239c: ldurb           w17, [x0, #-1]
    //     0x8023a0: and             x16, x17, x16, lsr #2
    //     0x8023a4: tst             x16, HEAP, lsr #32
    //     0x8023a8: b.eq            #0x8023b0
    //     0x8023ac: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8023b0: ldur            x2, [fp, #-0x18]
    // 0x8023b4: r1 = Function '<anonymous closure>':.
    //     0x8023b4: add             x1, PP, #0x59, lsl #12  ; [pp+0x590e8] AnonymousClosure: (0x8027cc), in [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::addAllowedPointer (0x80225c)
    //     0x8023b8: ldr             x1, [x1, #0xe8]
    // 0x8023bc: r0 = AllocateClosure()
    //     0x8023bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8023c0: mov             x3, x0
    // 0x8023c4: r1 = Null
    //     0x8023c4: mov             x1, NULL
    // 0x8023c8: r2 = Instance_Duration
    //     0x8023c8: ldr             x2, [PP, #0x6e28]  ; [pp+0x6e28] Obj!Duration@e3a0a1
    // 0x8023cc: r0 = Timer()
    //     0x8023cc: bl              #0x5f9778  ; [dart:async] Timer::Timer
    // 0x8023d0: ldur            x1, [fp, #-8]
    // 0x8023d4: StoreField: r1->field_83 = r0
    //     0x8023d4: stur            w0, [x1, #0x83]
    //     0x8023d8: ldurb           w16, [x1, #-1]
    //     0x8023dc: ldurb           w17, [x0, #-1]
    //     0x8023e0: and             x16, x17, x16, lsr #2
    //     0x8023e4: tst             x16, HEAP, lsr #32
    //     0x8023e8: b.eq            #0x8023f0
    //     0x8023ec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8023f0: r0 = Null
    //     0x8023f0: mov             x0, NULL
    // 0x8023f4: LeaveFrame
    //     0x8023f4: mov             SP, fp
    //     0x8023f8: ldp             fp, lr, [SP], #0x10
    // 0x8023fc: ret
    //     0x8023fc: ret             
    // 0x802400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x802400: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x802404: b               #0x80227c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8027cc, size: 0x48
    // 0x8027cc: EnterFrame
    //     0x8027cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8027d0: mov             fp, SP
    // 0x8027d4: ldr             x0, [fp, #0x10]
    // 0x8027d8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8027d8: ldur            w1, [x0, #0x17]
    // 0x8027dc: DecompressPointer r1
    //     0x8027dc: add             x1, x1, HEAP, lsl #32
    // 0x8027e0: CheckStackOverflow
    //     0x8027e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8027e4: cmp             SP, x16
    //     0x8027e8: b.ls            #0x80280c
    // 0x8027ec: LoadField: r0 = r1->field_f
    //     0x8027ec: ldur            w0, [x1, #0xf]
    // 0x8027f0: DecompressPointer r0
    //     0x8027f0: add             x0, x0, HEAP, lsl #32
    // 0x8027f4: mov             x1, x0
    // 0x8027f8: r0 = _didExceedDeadline()
    //     0x8027f8: bl              #0x802814  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_didExceedDeadline
    // 0x8027fc: r0 = Null
    //     0x8027fc: mov             x0, NULL
    // 0x802800: LeaveFrame
    //     0x802800: mov             SP, fp
    //     0x802804: ldp             fp, lr, [SP], #0x10
    // 0x802808: ret
    //     0x802808: ret             
    // 0x80280c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80280c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x802810: b               #0x8027ec
  }
  _ _didExceedDeadline(/* No info */) {
    // ** addr: 0x802814, size: 0x6c
    // 0x802814: EnterFrame
    //     0x802814: stp             fp, lr, [SP, #-0x10]!
    //     0x802818: mov             fp, SP
    // 0x80281c: AllocStack(0x8)
    //     0x80281c: sub             SP, SP, #8
    // 0x802820: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r0, fp-0x8 */)
    //     0x802820: mov             x0, x1
    //     0x802824: stur            x1, [fp, #-8]
    // 0x802828: CheckStackOverflow
    //     0x802828: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80282c: cmp             SP, x16
    //     0x802830: b.ls            #0x802878
    // 0x802834: LoadField: r2 = r0->field_23
    //     0x802834: ldur            w2, [x0, #0x23]
    // 0x802838: DecompressPointer r2
    //     0x802838: add             x2, x2, HEAP, lsl #32
    // 0x80283c: cmp             w2, NULL
    // 0x802840: b.eq            #0x802868
    // 0x802844: mov             x1, x0
    // 0x802848: r0 = _checkTapDown()
    //     0x802848: bl              #0x802880  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkTapDown
    // 0x80284c: ldur            x1, [fp, #-8]
    // 0x802850: LoadField: r0 = r1->field_2b
    //     0x802850: ldur            x0, [x1, #0x2b]
    // 0x802854: cmp             x0, #1
    // 0x802858: b.le            #0x802868
    // 0x80285c: r2 = Instance_GestureDisposition
    //     0x80285c: add             x2, PP, #0x30, lsl #12  ; [pp+0x30e00] Obj!GestureDisposition@e36ee1
    //     0x802860: ldr             x2, [x2, #0xe00]
    // 0x802864: r0 = resolve()
    //     0x802864: bl              #0x7a9d28  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::resolve
    // 0x802868: r0 = Null
    //     0x802868: mov             x0, NULL
    // 0x80286c: LeaveFrame
    //     0x80286c: mov             SP, fp
    //     0x802870: ldp             fp, lr, [SP], #0x10
    // 0x802874: ret
    //     0x802874: ret             
    // 0x802878: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x802878: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80287c: b               #0x802834
  }
  _ _checkTapDown(/* No info */) {
    // ** addr: 0x802880, size: 0x17c
    // 0x802880: EnterFrame
    //     0x802880: stp             fp, lr, [SP, #-0x10]!
    //     0x802884: mov             fp, SP
    // 0x802888: AllocStack(0x40)
    //     0x802888: sub             SP, SP, #0x40
    // 0x80288c: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x80288c: mov             x0, x1
    //     0x802890: stur            x1, [fp, #-8]
    //     0x802894: mov             x1, x2
    //     0x802898: stur            x2, [fp, #-0x10]
    // 0x80289c: CheckStackOverflow
    //     0x80289c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8028a0: cmp             SP, x16
    //     0x8028a4: b.ls            #0x8029f4
    // 0x8028a8: r1 = 2
    //     0x8028a8: movz            x1, #0x2
    // 0x8028ac: r0 = AllocateContext()
    //     0x8028ac: bl              #0xec126c  ; AllocateContextStub
    // 0x8028b0: mov             x3, x0
    // 0x8028b4: ldur            x2, [fp, #-8]
    // 0x8028b8: stur            x3, [fp, #-0x18]
    // 0x8028bc: StoreField: r3->field_f = r2
    //     0x8028bc: stur            w2, [x3, #0xf]
    // 0x8028c0: LoadField: r0 = r2->field_77
    //     0x8028c0: ldur            w0, [x2, #0x77]
    // 0x8028c4: DecompressPointer r0
    //     0x8028c4: add             x0, x0, HEAP, lsl #32
    // 0x8028c8: tbnz            w0, #4, #0x8028dc
    // 0x8028cc: r0 = Null
    //     0x8028cc: mov             x0, NULL
    // 0x8028d0: LeaveFrame
    //     0x8028d0: mov             SP, fp
    //     0x8028d4: ldp             fp, lr, [SP], #0x10
    // 0x8028d8: ret
    //     0x8028d8: ret             
    // 0x8028dc: ldur            x4, [fp, #-0x10]
    // 0x8028e0: r0 = LoadClassIdInstr(r4)
    //     0x8028e0: ldur            x0, [x4, #-1]
    //     0x8028e4: ubfx            x0, x0, #0xc, #0x14
    // 0x8028e8: mov             x1, x4
    // 0x8028ec: r0 = GDT[cid_x0 + -0x1]()
    //     0x8028ec: sub             lr, x0, #1
    //     0x8028f0: ldr             lr, [x21, lr, lsl #3]
    //     0x8028f4: blr             lr
    // 0x8028f8: mov             x3, x0
    // 0x8028fc: ldur            x2, [fp, #-0x10]
    // 0x802900: stur            x3, [fp, #-0x20]
    // 0x802904: r0 = LoadClassIdInstr(r2)
    //     0x802904: ldur            x0, [x2, #-1]
    //     0x802908: ubfx            x0, x0, #0xc, #0x14
    // 0x80290c: mov             x1, x2
    // 0x802910: r0 = GDT[cid_x0 + 0x130fa]()
    //     0x802910: movz            x17, #0x30fa
    //     0x802914: movk            x17, #0x1, lsl #16
    //     0x802918: add             lr, x0, x17
    //     0x80291c: ldr             lr, [x21, lr, lsl #3]
    //     0x802920: blr             lr
    // 0x802924: ldur            x1, [fp, #-0x10]
    // 0x802928: r0 = LoadClassIdInstr(r1)
    //     0x802928: ldur            x0, [x1, #-1]
    //     0x80292c: ubfx            x0, x0, #0xc, #0x14
    // 0x802930: r0 = GDT[cid_x0 + -0x1000]()
    //     0x802930: sub             lr, x0, #1, lsl #12
    //     0x802934: ldr             lr, [x21, lr, lsl #3]
    //     0x802938: blr             lr
    // 0x80293c: ldur            x1, [fp, #-8]
    // 0x802940: mov             x2, x0
    // 0x802944: r0 = getKindForPointer()
    //     0x802944: bl              #0x75a0b4  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::getKindForPointer
    // 0x802948: mov             x1, x0
    // 0x80294c: ldur            x0, [fp, #-8]
    // 0x802950: stur            x1, [fp, #-0x10]
    // 0x802954: LoadField: r2 = r0->field_2b
    //     0x802954: ldur            x2, [x0, #0x2b]
    // 0x802958: stur            x2, [fp, #-0x28]
    // 0x80295c: r0 = TapDragDownDetails()
    //     0x80295c: bl              #0x8029fc  ; AllocateTapDragDownDetailsStub -> TapDragDownDetails (size=0x18)
    // 0x802960: mov             x1, x0
    // 0x802964: ldur            x0, [fp, #-0x20]
    // 0x802968: StoreField: r1->field_7 = r0
    //     0x802968: stur            w0, [x1, #7]
    // 0x80296c: ldur            x0, [fp, #-0x10]
    // 0x802970: StoreField: r1->field_b = r0
    //     0x802970: stur            w0, [x1, #0xb]
    // 0x802974: ldur            x0, [fp, #-0x28]
    // 0x802978: StoreField: r1->field_f = r0
    //     0x802978: stur            x0, [x1, #0xf]
    // 0x80297c: mov             x0, x1
    // 0x802980: ldur            x2, [fp, #-0x18]
    // 0x802984: StoreField: r2->field_13 = r0
    //     0x802984: stur            w0, [x2, #0x13]
    //     0x802988: ldurb           w16, [x2, #-1]
    //     0x80298c: ldurb           w17, [x0, #-1]
    //     0x802990: and             x16, x17, x16, lsr #2
    //     0x802994: tst             x16, HEAP, lsr #32
    //     0x802998: b.eq            #0x8029a0
    //     0x80299c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8029a0: ldur            x0, [fp, #-8]
    // 0x8029a4: LoadField: r1 = r0->field_5b
    //     0x8029a4: ldur            w1, [x0, #0x5b]
    // 0x8029a8: DecompressPointer r1
    //     0x8029a8: add             x1, x1, HEAP, lsl #32
    // 0x8029ac: cmp             w1, NULL
    // 0x8029b0: b.eq            #0x8029d8
    // 0x8029b4: r1 = Function '<anonymous closure>':.
    //     0x8029b4: add             x1, PP, #0x59, lsl #12  ; [pp+0x590e0] AnonymousClosure: (0x802a08), in [package:flutter/src/gestures/tap.dart] TapGestureRecognizer::handleTapUp (0xd82788)
    //     0x8029b8: ldr             x1, [x1, #0xe0]
    // 0x8029bc: r0 = AllocateClosure()
    //     0x8029bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8029c0: r16 = <void?>
    //     0x8029c0: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8029c4: ldur            lr, [fp, #-8]
    // 0x8029c8: stp             lr, x16, [SP, #8]
    // 0x8029cc: str             x0, [SP]
    // 0x8029d0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8029d0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8029d4: r0 = invokeCallback()
    //     0x8029d4: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0x8029d8: ldur            x1, [fp, #-8]
    // 0x8029dc: r2 = true
    //     0x8029dc: add             x2, NULL, #0x20  ; true
    // 0x8029e0: StoreField: r1->field_77 = r2
    //     0x8029e0: stur            w2, [x1, #0x77]
    // 0x8029e4: r0 = Null
    //     0x8029e4: mov             x0, NULL
    // 0x8029e8: LeaveFrame
    //     0x8029e8: mov             SP, fp
    //     0x8029ec: ldp             fp, lr, [SP], #0x10
    // 0x8029f0: ret
    //     0x8029f0: ret             
    // 0x8029f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8029f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8029f8: b               #0x8028a8
  }
  _ isPointerAllowed(/* No info */) {
    // ** addr: 0x852c58, size: 0x1b0
    // 0x852c58: EnterFrame
    //     0x852c58: stp             fp, lr, [SP, #-0x10]!
    //     0x852c5c: mov             fp, SP
    // 0x852c60: AllocStack(0x10)
    //     0x852c60: sub             SP, SP, #0x10
    // 0x852c64: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x852c64: mov             x3, x1
    //     0x852c68: stur            x1, [fp, #-8]
    //     0x852c6c: stur            x2, [fp, #-0x10]
    // 0x852c70: CheckStackOverflow
    //     0x852c70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x852c74: cmp             SP, x16
    //     0x852c78: b.ls            #0x852e00
    // 0x852c7c: LoadField: r0 = r3->field_7f
    //     0x852c7c: ldur            w0, [x3, #0x7f]
    // 0x852c80: DecompressPointer r0
    //     0x852c80: add             x0, x0, HEAP, lsl #32
    // 0x852c84: cmp             w0, NULL
    // 0x852c88: b.ne            #0x852d50
    // 0x852c8c: r0 = LoadClassIdInstr(r2)
    //     0x852c8c: ldur            x0, [x2, #-1]
    //     0x852c90: ubfx            x0, x0, #0xc, #0x14
    // 0x852c94: mov             x1, x2
    // 0x852c98: r0 = GDT[cid_x0 + 0x12cee]()
    //     0x852c98: movz            x17, #0x2cee
    //     0x852c9c: movk            x17, #0x1, lsl #16
    //     0x852ca0: add             lr, x0, x17
    //     0x852ca4: ldr             lr, [x21, lr, lsl #3]
    //     0x852ca8: blr             lr
    // 0x852cac: mov             x2, x0
    // 0x852cb0: r0 = BoxInt64Instr(r2)
    //     0x852cb0: sbfiz           x0, x2, #1, #0x1f
    //     0x852cb4: cmp             x2, x0, asr #1
    //     0x852cb8: b.eq            #0x852cc4
    //     0x852cbc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x852cc0: stur            x2, [x0, #7]
    // 0x852cc4: cmp             w0, #2
    // 0x852cc8: b.ne            #0x852d40
    // 0x852ccc: ldur            x2, [fp, #-8]
    // 0x852cd0: LoadField: r0 = r2->field_5b
    //     0x852cd0: ldur            w0, [x2, #0x5b]
    // 0x852cd4: DecompressPointer r0
    //     0x852cd4: add             x0, x0, HEAP, lsl #32
    // 0x852cd8: cmp             w0, NULL
    // 0x852cdc: b.ne            #0x852de8
    // 0x852ce0: LoadField: r0 = r2->field_63
    //     0x852ce0: ldur            w0, [x2, #0x63]
    // 0x852ce4: DecompressPointer r0
    //     0x852ce4: add             x0, x0, HEAP, lsl #32
    // 0x852ce8: cmp             w0, NULL
    // 0x852cec: b.ne            #0x852de8
    // 0x852cf0: LoadField: r0 = r2->field_67
    //     0x852cf0: ldur            w0, [x2, #0x67]
    // 0x852cf4: DecompressPointer r0
    //     0x852cf4: add             x0, x0, HEAP, lsl #32
    // 0x852cf8: cmp             w0, NULL
    // 0x852cfc: b.ne            #0x852de8
    // 0x852d00: LoadField: r0 = r2->field_6b
    //     0x852d00: ldur            w0, [x2, #0x6b]
    // 0x852d04: DecompressPointer r0
    //     0x852d04: add             x0, x0, HEAP, lsl #32
    // 0x852d08: cmp             w0, NULL
    // 0x852d0c: b.ne            #0x852de8
    // 0x852d10: LoadField: r0 = r2->field_5f
    //     0x852d10: ldur            w0, [x2, #0x5f]
    // 0x852d14: DecompressPointer r0
    //     0x852d14: add             x0, x0, HEAP, lsl #32
    // 0x852d18: cmp             w0, NULL
    // 0x852d1c: b.ne            #0x852de8
    // 0x852d20: LoadField: r0 = r2->field_6f
    //     0x852d20: ldur            w0, [x2, #0x6f]
    // 0x852d24: DecompressPointer r0
    //     0x852d24: add             x0, x0, HEAP, lsl #32
    // 0x852d28: cmp             w0, NULL
    // 0x852d2c: b.ne            #0x852de8
    // 0x852d30: r0 = false
    //     0x852d30: add             x0, NULL, #0x30  ; false
    // 0x852d34: LeaveFrame
    //     0x852d34: mov             SP, fp
    //     0x852d38: ldp             fp, lr, [SP], #0x10
    // 0x852d3c: ret
    //     0x852d3c: ret             
    // 0x852d40: r0 = false
    //     0x852d40: add             x0, NULL, #0x30  ; false
    // 0x852d44: LeaveFrame
    //     0x852d44: mov             SP, fp
    //     0x852d48: ldp             fp, lr, [SP], #0x10
    // 0x852d4c: ret
    //     0x852d4c: ret             
    // 0x852d50: mov             x16, x2
    // 0x852d54: mov             x2, x3
    // 0x852d58: mov             x3, x16
    // 0x852d5c: r0 = LoadClassIdInstr(r3)
    //     0x852d5c: ldur            x0, [x3, #-1]
    //     0x852d60: ubfx            x0, x0, #0xc, #0x14
    // 0x852d64: mov             x1, x3
    // 0x852d68: r0 = GDT[cid_x0 + -0x1000]()
    //     0x852d68: sub             lr, x0, #1, lsl #12
    //     0x852d6c: ldr             lr, [x21, lr, lsl #3]
    //     0x852d70: blr             lr
    // 0x852d74: mov             x3, x0
    // 0x852d78: ldur            x2, [fp, #-8]
    // 0x852d7c: LoadField: r4 = r2->field_7f
    //     0x852d7c: ldur            w4, [x2, #0x7f]
    // 0x852d80: DecompressPointer r4
    //     0x852d80: add             x4, x4, HEAP, lsl #32
    // 0x852d84: r0 = BoxInt64Instr(r3)
    //     0x852d84: sbfiz           x0, x3, #1, #0x1f
    //     0x852d88: cmp             x3, x0, asr #1
    //     0x852d8c: b.eq            #0x852d98
    //     0x852d90: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x852d94: stur            x3, [x0, #7]
    // 0x852d98: cmp             w0, w4
    // 0x852d9c: b.eq            #0x852de8
    // 0x852da0: and             w16, w0, w4
    // 0x852da4: branchIfSmi(r16, 0x852dd8)
    //     0x852da4: tbz             w16, #0, #0x852dd8
    // 0x852da8: r16 = LoadClassIdInstr(r0)
    //     0x852da8: ldur            x16, [x0, #-1]
    //     0x852dac: ubfx            x16, x16, #0xc, #0x14
    // 0x852db0: cmp             x16, #0x3d
    // 0x852db4: b.ne            #0x852dd8
    // 0x852db8: r16 = LoadClassIdInstr(r4)
    //     0x852db8: ldur            x16, [x4, #-1]
    //     0x852dbc: ubfx            x16, x16, #0xc, #0x14
    // 0x852dc0: cmp             x16, #0x3d
    // 0x852dc4: b.ne            #0x852dd8
    // 0x852dc8: LoadField: r16 = r0->field_7
    //     0x852dc8: ldur            x16, [x0, #7]
    // 0x852dcc: LoadField: r17 = r4->field_7
    //     0x852dcc: ldur            x17, [x4, #7]
    // 0x852dd0: cmp             x16, x17
    // 0x852dd4: b.eq            #0x852de8
    // 0x852dd8: r0 = false
    //     0x852dd8: add             x0, NULL, #0x30  ; false
    // 0x852ddc: LeaveFrame
    //     0x852ddc: mov             SP, fp
    //     0x852de0: ldp             fp, lr, [SP], #0x10
    // 0x852de4: ret
    //     0x852de4: ret             
    // 0x852de8: mov             x1, x2
    // 0x852dec: ldur            x2, [fp, #-0x10]
    // 0x852df0: r0 = isPointerAllowed()
    //     0x852df0: bl              #0x852e08  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::isPointerAllowed
    // 0x852df4: LeaveFrame
    //     0x852df4: mov             SP, fp
    //     0x852df8: ldp             fp, lr, [SP], #0x10
    // 0x852dfc: ret
    //     0x852dfc: ret             
    // 0x852e00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x852e00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x852e04: b               #0x852c7c
  }
  _ acceptGesture(/* No info */) {
    // ** addr: 0x86cf28, size: 0x15c
    // 0x86cf28: EnterFrame
    //     0x86cf28: stp             fp, lr, [SP, #-0x10]!
    //     0x86cf2c: mov             fp, SP
    // 0x86cf30: AllocStack(0x10)
    //     0x86cf30: sub             SP, SP, #0x10
    // 0x86cf34: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r3, fp-0x10 */)
    //     0x86cf34: mov             x3, x1
    //     0x86cf38: stur            x1, [fp, #-0x10]
    // 0x86cf3c: CheckStackOverflow
    //     0x86cf3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86cf40: cmp             SP, x16
    //     0x86cf44: b.ls            #0x86d07c
    // 0x86cf48: LoadField: r4 = r3->field_7f
    //     0x86cf48: ldur            w4, [x3, #0x7f]
    // 0x86cf4c: DecompressPointer r4
    //     0x86cf4c: add             x4, x4, HEAP, lsl #32
    // 0x86cf50: r0 = BoxInt64Instr(r2)
    //     0x86cf50: sbfiz           x0, x2, #1, #0x1f
    //     0x86cf54: cmp             x2, x0, asr #1
    //     0x86cf58: b.eq            #0x86cf64
    //     0x86cf5c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x86cf60: stur            x2, [x0, #7]
    // 0x86cf64: stur            x0, [fp, #-8]
    // 0x86cf68: cmp             w0, w4
    // 0x86cf6c: b.eq            #0x86cfb8
    // 0x86cf70: and             w16, w0, w4
    // 0x86cf74: branchIfSmi(r16, 0x86cfa8)
    //     0x86cf74: tbz             w16, #0, #0x86cfa8
    // 0x86cf78: r16 = LoadClassIdInstr(r0)
    //     0x86cf78: ldur            x16, [x0, #-1]
    //     0x86cf7c: ubfx            x16, x16, #0xc, #0x14
    // 0x86cf80: cmp             x16, #0x3d
    // 0x86cf84: b.ne            #0x86cfa8
    // 0x86cf88: r16 = LoadClassIdInstr(r4)
    //     0x86cf88: ldur            x16, [x4, #-1]
    //     0x86cf8c: ubfx            x16, x16, #0xc, #0x14
    // 0x86cf90: cmp             x16, #0x3d
    // 0x86cf94: b.ne            #0x86cfa8
    // 0x86cf98: LoadField: r16 = r0->field_7
    //     0x86cf98: ldur            x16, [x0, #7]
    // 0x86cf9c: LoadField: r17 = r4->field_7
    //     0x86cf9c: ldur            x17, [x4, #7]
    // 0x86cfa0: cmp             x16, x17
    // 0x86cfa4: b.eq            #0x86cfb8
    // 0x86cfa8: r0 = Null
    //     0x86cfa8: mov             x0, NULL
    // 0x86cfac: LeaveFrame
    //     0x86cfac: mov             SP, fp
    //     0x86cfb0: ldp             fp, lr, [SP], #0x10
    // 0x86cfb4: ret
    //     0x86cfb4: ret             
    // 0x86cfb8: mov             x1, x3
    // 0x86cfbc: r0 = _stopDeadlineTimer()
    //     0x86cfbc: bl              #0x7d7794  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_stopDeadlineTimer
    // 0x86cfc0: ldur            x0, [fp, #-0x10]
    // 0x86cfc4: LoadField: r1 = r0->field_a7
    //     0x86cfc4: ldur            w1, [x0, #0xa7]
    // 0x86cfc8: DecompressPointer r1
    //     0x86cfc8: add             x1, x1, HEAP, lsl #32
    // 0x86cfcc: ldur            x2, [fp, #-8]
    // 0x86cfd0: r0 = add()
    //     0x86cfd0: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x86cfd4: ldur            x0, [fp, #-0x10]
    // 0x86cfd8: LoadField: r2 = r0->field_23
    //     0x86cfd8: ldur            w2, [x0, #0x23]
    // 0x86cfdc: DecompressPointer r2
    //     0x86cfdc: add             x2, x2, HEAP, lsl #32
    // 0x86cfe0: cmp             w2, NULL
    // 0x86cfe4: b.eq            #0x86cff0
    // 0x86cfe8: mov             x1, x0
    // 0x86cfec: r0 = _checkTapDown()
    //     0x86cfec: bl              #0x802880  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkTapDown
    // 0x86cff0: ldur            x0, [fp, #-0x10]
    // 0x86cff4: r1 = true
    //     0x86cff4: add             x1, NULL, #0x20  ; true
    // 0x86cff8: StoreField: r0->field_7b = r1
    //     0x86cff8: stur            w1, [x0, #0x7b]
    // 0x86cffc: LoadField: r2 = r0->field_8f
    //     0x86cffc: ldur            w2, [x0, #0x8f]
    // 0x86d000: DecompressPointer r2
    //     0x86d000: add             x2, x2, HEAP, lsl #32
    // 0x86d004: cmp             w2, NULL
    // 0x86d008: b.eq            #0x86d020
    // 0x86d00c: LoadField: r1 = r0->field_57
    //     0x86d00c: ldur            w1, [x0, #0x57]
    // 0x86d010: DecompressPointer r1
    //     0x86d010: add             x1, x1, HEAP, lsl #32
    // 0x86d014: tbnz            w1, #4, #0x86d020
    // 0x86d018: mov             x1, x0
    // 0x86d01c: r0 = _acceptDrag()
    //     0x86d01c: bl              #0x75d590  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_acceptDrag
    // 0x86d020: ldur            x0, [fp, #-0x10]
    // 0x86d024: LoadField: r2 = r0->field_8f
    //     0x86d024: ldur            w2, [x0, #0x8f]
    // 0x86d028: DecompressPointer r2
    //     0x86d028: add             x2, x2, HEAP, lsl #32
    // 0x86d02c: cmp             w2, NULL
    // 0x86d030: b.eq            #0x86d054
    // 0x86d034: LoadField: r1 = r0->field_57
    //     0x86d034: ldur            w1, [x0, #0x57]
    // 0x86d038: DecompressPointer r1
    //     0x86d038: add             x1, x1, HEAP, lsl #32
    // 0x86d03c: tbz             w1, #4, #0x86d054
    // 0x86d040: r1 = Instance__DragState
    //     0x86d040: add             x1, PP, #0x59, lsl #12  ; [pp+0x59090] Obj!_DragState@e36c61
    //     0x86d044: ldr             x1, [x1, #0x90]
    // 0x86d048: StoreField: r0->field_8b = r1
    //     0x86d048: stur            w1, [x0, #0x8b]
    // 0x86d04c: mov             x1, x0
    // 0x86d050: r0 = _acceptDrag()
    //     0x86d050: bl              #0x75d590  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_acceptDrag
    // 0x86d054: ldur            x1, [fp, #-0x10]
    // 0x86d058: LoadField: r2 = r1->field_27
    //     0x86d058: ldur            w2, [x1, #0x27]
    // 0x86d05c: DecompressPointer r2
    //     0x86d05c: add             x2, x2, HEAP, lsl #32
    // 0x86d060: cmp             w2, NULL
    // 0x86d064: b.eq            #0x86d06c
    // 0x86d068: r0 = _checkTapUp()
    //     0x86d068: bl              #0x7d77e8  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_checkTapUp
    // 0x86d06c: r0 = Null
    //     0x86d06c: mov             x0, NULL
    // 0x86d070: LeaveFrame
    //     0x86d070: mov             SP, fp
    //     0x86d074: ldp             fp, lr, [SP], #0x10
    // 0x86d078: ret
    //     0x86d078: ret             
    // 0x86d07c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86d07c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86d080: b               #0x86cf48
  }
  _ BaseTapAndDragGestureRecognizer(/* No info */) {
    // ** addr: 0x94a81c, size: 0x128
    // 0x94a81c: EnterFrame
    //     0x94a81c: stp             fp, lr, [SP, #-0x10]!
    //     0x94a820: mov             fp, SP
    // 0x94a824: AllocStack(0x30)
    //     0x94a824: sub             SP, SP, #0x30
    // 0x94a828: r4 = false
    //     0x94a828: add             x4, NULL, #0x30  ; false
    // 0x94a82c: r3 = Instance__DragState
    //     0x94a82c: add             x3, PP, #0x56, lsl #12  ; [pp+0x56518] Obj!_DragState@e36c81
    //     0x94a830: ldr             x3, [x3, #0x518]
    // 0x94a834: r0 = Sentinel
    //     0x94a834: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x94a838: stur            x1, [fp, #-8]
    // 0x94a83c: stur            x2, [fp, #-0x10]
    // 0x94a840: CheckStackOverflow
    //     0x94a840: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94a844: cmp             SP, x16
    //     0x94a848: b.ls            #0x94a93c
    // 0x94a84c: StoreField: r1->field_73 = r4
    //     0x94a84c: stur            w4, [x1, #0x73]
    // 0x94a850: StoreField: r1->field_77 = r4
    //     0x94a850: stur            w4, [x1, #0x77]
    // 0x94a854: StoreField: r1->field_7b = r4
    //     0x94a854: stur            w4, [x1, #0x7b]
    // 0x94a858: StoreField: r1->field_8b = r3
    //     0x94a858: stur            w3, [x1, #0x8b]
    // 0x94a85c: StoreField: r1->field_93 = r0
    //     0x94a85c: stur            w0, [x1, #0x93]
    // 0x94a860: StoreField: r1->field_97 = r0
    //     0x94a860: stur            w0, [x1, #0x97]
    // 0x94a864: StoreField: r1->field_9b = r0
    //     0x94a864: stur            w0, [x1, #0x9b]
    // 0x94a868: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x94a868: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x94a86c: ldr             x0, [x0, #0x778]
    //     0x94a870: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x94a874: cmp             w0, w16
    //     0x94a878: b.ne            #0x94a884
    //     0x94a87c: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x94a880: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x94a884: r1 = <int>
    //     0x94a884: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x94a888: stur            x0, [fp, #-0x18]
    // 0x94a88c: r0 = _Set()
    //     0x94a88c: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x94a890: mov             x1, x0
    // 0x94a894: ldur            x0, [fp, #-0x18]
    // 0x94a898: stur            x1, [fp, #-0x20]
    // 0x94a89c: StoreField: r1->field_1b = r0
    //     0x94a89c: stur            w0, [x1, #0x1b]
    // 0x94a8a0: StoreField: r1->field_b = rZR
    //     0x94a8a0: stur            wzr, [x1, #0xb]
    // 0x94a8a4: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x94a8a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x94a8a8: ldr             x0, [x0, #0x780]
    //     0x94a8ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x94a8b0: cmp             w0, w16
    //     0x94a8b4: b.ne            #0x94a8c0
    //     0x94a8b8: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x94a8bc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x94a8c0: mov             x1, x0
    // 0x94a8c4: ldur            x0, [fp, #-0x20]
    // 0x94a8c8: StoreField: r0->field_f = r1
    //     0x94a8c8: stur            w1, [x0, #0xf]
    // 0x94a8cc: StoreField: r0->field_13 = rZR
    //     0x94a8cc: stur            wzr, [x0, #0x13]
    // 0x94a8d0: ArrayStore: r0[0] = rZR  ; List_4
    //     0x94a8d0: stur            wzr, [x0, #0x17]
    // 0x94a8d4: ldur            x1, [fp, #-8]
    // 0x94a8d8: StoreField: r1->field_a7 = r0
    //     0x94a8d8: stur            w0, [x1, #0xa7]
    //     0x94a8dc: ldurb           w16, [x1, #-1]
    //     0x94a8e0: ldurb           w17, [x0, #-1]
    //     0x94a8e4: and             x16, x17, x16, lsr #2
    //     0x94a8e8: tst             x16, HEAP, lsr #32
    //     0x94a8ec: b.eq            #0x94a8f4
    //     0x94a8f0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x94a8f4: r0 = true
    //     0x94a8f4: add             x0, NULL, #0x20  ; true
    // 0x94a8f8: StoreField: r1->field_57 = r0
    //     0x94a8f8: stur            w0, [x1, #0x57]
    // 0x94a8fc: r0 = Instance_Duration
    //     0x94a8fc: ldr             x0, [PP, #0x6e28]  ; [pp+0x6e28] Obj!Duration@e3a0a1
    // 0x94a900: StoreField: r1->field_87 = r0
    //     0x94a900: stur            w0, [x1, #0x87]
    // 0x94a904: r0 = Instance_DragStartBehavior
    //     0x94a904: ldr             x0, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0x94a908: StoreField: r1->field_4b = r0
    //     0x94a908: stur            w0, [x1, #0x4b]
    // 0x94a90c: StoreField: r1->field_2b = rZR
    //     0x94a90c: stur            xzr, [x1, #0x2b]
    // 0x94a910: ldur            x16, [fp, #-0x10]
    // 0x94a914: r30 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static.
    //     0x94a914: add             lr, PP, #0x25, lsl #12  ; [pp+0x253d8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static. (0x7e54fb8bbd8c)
    //     0x94a918: ldr             lr, [lr, #0x3d8]
    // 0x94a91c: stp             lr, x16, [SP]
    // 0x94a920: r4 = const [0, 0x3, 0x2, 0x1, allowedButtonsFilter, 0x2, supportedDevices, 0x1, null]
    //     0x94a920: add             x4, PP, #0x25, lsl #12  ; [pp+0x253c0] List(9) [0, 0x3, 0x2, 0x1, "allowedButtonsFilter", 0x2, "supportedDevices", 0x1, Null]
    //     0x94a924: ldr             x4, [x4, #0x3c0]
    // 0x94a928: r0 = OneSequenceGestureRecognizer()
    //     0x94a928: bl              #0x763058  ; [package:flutter/src/gestures/recognizer.dart] OneSequenceGestureRecognizer::OneSequenceGestureRecognizer
    // 0x94a92c: r0 = Null
    //     0x94a92c: mov             x0, NULL
    // 0x94a930: LeaveFrame
    //     0x94a930: mov             SP, fp
    //     0x94a934: ldp             fp, lr, [SP], #0x10
    // 0x94a938: ret
    //     0x94a938: ret             
    // 0x94a93c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94a93c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94a940: b               #0x94a84c
  }
  _ rejectGesture(/* No info */) {
    // ** addr: 0xcced28, size: 0xcc
    // 0xcced28: EnterFrame
    //     0xcced28: stp             fp, lr, [SP, #-0x10]!
    //     0xcced2c: mov             fp, SP
    // 0xcced30: AllocStack(0x10)
    //     0xcced30: sub             SP, SP, #0x10
    // 0xcced34: SetupParameters(BaseTapAndDragGestureRecognizer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xcced34: mov             x3, x1
    //     0xcced38: stur            x1, [fp, #-8]
    //     0xcced3c: stur            x2, [fp, #-0x10]
    // 0xcced40: CheckStackOverflow
    //     0xcced40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcced44: cmp             SP, x16
    //     0xcced48: b.ls            #0xccedec
    // 0xcced4c: LoadField: r4 = r3->field_7f
    //     0xcced4c: ldur            w4, [x3, #0x7f]
    // 0xcced50: DecompressPointer r4
    //     0xcced50: add             x4, x4, HEAP, lsl #32
    // 0xcced54: r0 = BoxInt64Instr(r2)
    //     0xcced54: sbfiz           x0, x2, #1, #0x1f
    //     0xcced58: cmp             x2, x0, asr #1
    //     0xcced5c: b.eq            #0xcced68
    //     0xcced60: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcced64: stur            x2, [x0, #7]
    // 0xcced68: cmp             w0, w4
    // 0xcced6c: b.eq            #0xccedb8
    // 0xcced70: and             w16, w0, w4
    // 0xcced74: branchIfSmi(r16, 0xcceda8)
    //     0xcced74: tbz             w16, #0, #0xcceda8
    // 0xcced78: r16 = LoadClassIdInstr(r0)
    //     0xcced78: ldur            x16, [x0, #-1]
    //     0xcced7c: ubfx            x16, x16, #0xc, #0x14
    // 0xcced80: cmp             x16, #0x3d
    // 0xcced84: b.ne            #0xcceda8
    // 0xcced88: r16 = LoadClassIdInstr(r4)
    //     0xcced88: ldur            x16, [x4, #-1]
    //     0xcced8c: ubfx            x16, x16, #0xc, #0x14
    // 0xcced90: cmp             x16, #0x3d
    // 0xcced94: b.ne            #0xcceda8
    // 0xcced98: LoadField: r16 = r0->field_7
    //     0xcced98: ldur            x16, [x0, #7]
    // 0xcced9c: LoadField: r17 = r4->field_7
    //     0xcced9c: ldur            x17, [x4, #7]
    // 0xcceda0: cmp             x16, x17
    // 0xcceda4: b.eq            #0xccedb8
    // 0xcceda8: r0 = Null
    //     0xcceda8: mov             x0, NULL
    // 0xccedac: LeaveFrame
    //     0xccedac: mov             SP, fp
    //     0xccedb0: ldp             fp, lr, [SP], #0x10
    // 0xccedb4: ret
    //     0xccedb4: ret             
    // 0xccedb8: mov             x1, x3
    // 0xccedbc: r0 = _tapTrackerReset()
    //     0xccedbc: bl              #0x75e898  ; [package:flutter/src/gestures/tap_and_drag.dart] _BaseTapAndDragGestureRecognizer&OneSequenceGestureRecognizer&_TapStatusTrackerMixin::_tapTrackerReset
    // 0xccedc0: ldur            x1, [fp, #-8]
    // 0xccedc4: r0 = _stopDeadlineTimer()
    //     0xccedc4: bl              #0x7d7794  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_stopDeadlineTimer
    // 0xccedc8: ldur            x1, [fp, #-8]
    // 0xccedcc: ldur            x2, [fp, #-0x10]
    // 0xccedd0: r0 = _giveUpPointer()
    //     0xccedd0: bl              #0x75d508  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_giveUpPointer
    // 0xccedd4: ldur            x1, [fp, #-8]
    // 0xccedd8: r0 = _resetTaps()
    //     0xccedd8: bl              #0x7d79e0  ; [package:flutter/src/gestures/tap_and_drag.dart] BaseTapAndDragGestureRecognizer::_resetTaps
    // 0xcceddc: r0 = Null
    //     0xcceddc: mov             x0, NULL
    // 0xccede0: LeaveFrame
    //     0xccede0: mov             SP, fp
    //     0xccede4: ldp             fp, lr, [SP], #0x10
    // 0xccede8: ret
    //     0xccede8: ret             
    // 0xccedec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xccedec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xccedf0: b               #0xcced4c
  }
}

// class id: 3536, size: 0xac, field offset: 0xac
class TapAndPanGestureRecognizer extends BaseTapAndDragGestureRecognizer {
}

// class id: 3537, size: 0xac, field offset: 0xac
class TapAndHorizontalDragGestureRecognizer extends BaseTapAndDragGestureRecognizer {
}

// class id: 3538, size: 0x24, field offset: 0x24
abstract class _TapStatusTrackerMixin extends OneSequenceGestureRecognizer {
}

// class id: 4005, size: 0x10, field offset: 0x8
class TapDragEndDetails extends _DiagnosticableTree&Object&Diagnosticable {
}

// class id: 4006, size: 0x1c, field offset: 0x8
class TapDragUpdateDetails extends _DiagnosticableTree&Object&Diagnosticable {
}

// class id: 4007, size: 0x18, field offset: 0x8
class TapDragStartDetails extends _DiagnosticableTree&Object&Diagnosticable {
}

// class id: 4008, size: 0x18, field offset: 0x8
class TapDragUpDetails extends _DiagnosticableTree&Object&Diagnosticable {
}

// class id: 4009, size: 0x18, field offset: 0x8
class TapDragDownDetails extends _DiagnosticableTree&Object&Diagnosticable {
}

// class id: 7081, size: 0x14, field offset: 0x14
enum _DragState extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc48810, size: 0x64
    // 0xc48810: EnterFrame
    //     0xc48810: stp             fp, lr, [SP, #-0x10]!
    //     0xc48814: mov             fp, SP
    // 0xc48818: AllocStack(0x10)
    //     0xc48818: sub             SP, SP, #0x10
    // 0xc4881c: SetupParameters(_DragState this /* r1 => r0, fp-0x8 */)
    //     0xc4881c: mov             x0, x1
    //     0xc48820: stur            x1, [fp, #-8]
    // 0xc48824: CheckStackOverflow
    //     0xc48824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc48828: cmp             SP, x16
    //     0xc4882c: b.ls            #0xc4886c
    // 0xc48830: r1 = Null
    //     0xc48830: mov             x1, NULL
    // 0xc48834: r2 = 4
    //     0xc48834: movz            x2, #0x4
    // 0xc48838: r0 = AllocateArray()
    //     0xc48838: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4883c: r16 = "_DragState."
    //     0xc4883c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30e50] "_DragState."
    //     0xc48840: ldr             x16, [x16, #0xe50]
    // 0xc48844: StoreField: r0->field_f = r16
    //     0xc48844: stur            w16, [x0, #0xf]
    // 0xc48848: ldur            x1, [fp, #-8]
    // 0xc4884c: LoadField: r2 = r1->field_f
    //     0xc4884c: ldur            w2, [x1, #0xf]
    // 0xc48850: DecompressPointer r2
    //     0xc48850: add             x2, x2, HEAP, lsl #32
    // 0xc48854: StoreField: r0->field_13 = r2
    //     0xc48854: stur            w2, [x0, #0x13]
    // 0xc48858: str             x0, [SP]
    // 0xc4885c: r0 = _interpolate()
    //     0xc4885c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc48860: LeaveFrame
    //     0xc48860: mov             SP, fp
    //     0xc48864: ldp             fp, lr, [SP], #0x10
    // 0xc48868: ret
    //     0xc48868: ret             
    // 0xc4886c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4886c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc48870: b               #0xc48830
  }
}
