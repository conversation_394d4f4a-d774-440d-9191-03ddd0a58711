// lib: , url: package:flutter/src/material/adaptive_text_selection_toolbar.dart

// class id: 1048849, size: 0x8
class :: {
}

// class id: 5404, size: 0x18, field offset: 0xc
//   const constructor, 
class AdaptiveTextSelectionToolbar extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xa98958, size: 0x238
    // 0xa98958: EnterFrame
    //     0xa98958: stp             fp, lr, [SP, #-0x10]!
    //     0xa9895c: mov             fp, SP
    // 0xa98960: AllocStack(0x20)
    //     0xa98960: sub             SP, SP, #0x20
    // 0xa98964: SetupParameters(AdaptiveTextSelectionToolbar this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa98964: mov             x3, x1
    //     0xa98968: mov             x0, x2
    //     0xa9896c: stur            x1, [fp, #-8]
    //     0xa98970: stur            x2, [fp, #-0x10]
    // 0xa98974: CheckStackOverflow
    //     0xa98974: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa98978: cmp             SP, x16
    //     0xa9897c: b.ls            #0xa98b88
    // 0xa98980: LoadField: r2 = r3->field_b
    //     0xa98980: ldur            w2, [x3, #0xb]
    // 0xa98984: DecompressPointer r2
    //     0xa98984: add             x2, x2, HEAP, lsl #32
    // 0xa98988: LoadField: r1 = r2->field_b
    //     0xa98988: ldur            w1, [x2, #0xb]
    // 0xa9898c: cbnz            w1, #0xa989a0
    // 0xa98990: r0 = Instance_SizedBox
    //     0xa98990: ldr             x0, [PP, #0x4c90]  ; [pp+0x4c90] Obj!SizedBox@e1df81
    // 0xa98994: LeaveFrame
    //     0xa98994: mov             SP, fp
    //     0xa98998: ldp             fp, lr, [SP], #0x10
    // 0xa9899c: ret
    //     0xa9899c: ret             
    // 0xa989a0: mov             x1, x0
    // 0xa989a4: r0 = getAdaptiveButtons()
    //     0xa989a4: bl              #0xa98bb4  ; [package:flutter/src/material/adaptive_text_selection_toolbar.dart] AdaptiveTextSelectionToolbar::getAdaptiveButtons
    // 0xa989a8: r1 = LoadClassIdInstr(r0)
    //     0xa989a8: ldur            x1, [x0, #-1]
    //     0xa989ac: ubfx            x1, x1, #0xc, #0x14
    // 0xa989b0: mov             x16, x0
    // 0xa989b4: mov             x0, x1
    // 0xa989b8: mov             x1, x16
    // 0xa989bc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa989bc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa989c0: r0 = GDT[cid_x0 + 0xd889]()
    //     0xa989c0: movz            x17, #0xd889
    //     0xa989c4: add             lr, x0, x17
    //     0xa989c8: ldr             lr, [x21, lr, lsl #3]
    //     0xa989cc: blr             lr
    // 0xa989d0: ldur            x1, [fp, #-0x10]
    // 0xa989d4: stur            x0, [fp, #-0x10]
    // 0xa989d8: r0 = of()
    //     0xa989d8: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa989dc: LoadField: r1 = r0->field_23
    //     0xa989dc: ldur            w1, [x0, #0x23]
    // 0xa989e0: DecompressPointer r1
    //     0xa989e0: add             x1, x1, HEAP, lsl #32
    // 0xa989e4: LoadField: r0 = r1->field_7
    //     0xa989e4: ldur            x0, [x1, #7]
    // 0xa989e8: cmp             x0, #2
    // 0xa989ec: b.gt            #0xa98af4
    // 0xa989f0: cmp             x0, #1
    // 0xa989f4: b.gt            #0xa98a7c
    // 0xa989f8: cmp             x0, #0
    // 0xa989fc: b.gt            #0xa98a70
    // 0xa98a00: ldur            x1, [fp, #-8]
    // 0xa98a04: LoadField: r0 = r1->field_13
    //     0xa98a04: ldur            w0, [x1, #0x13]
    // 0xa98a08: DecompressPointer r0
    //     0xa98a08: add             x0, x0, HEAP, lsl #32
    // 0xa98a0c: LoadField: r1 = r0->field_7
    //     0xa98a0c: ldur            w1, [x0, #7]
    // 0xa98a10: DecompressPointer r1
    //     0xa98a10: add             x1, x1, HEAP, lsl #32
    // 0xa98a14: stur            x1, [fp, #-0x20]
    // 0xa98a18: LoadField: r2 = r0->field_b
    //     0xa98a18: ldur            w2, [x0, #0xb]
    // 0xa98a1c: DecompressPointer r2
    //     0xa98a1c: add             x2, x2, HEAP, lsl #32
    // 0xa98a20: cmp             w2, NULL
    // 0xa98a24: b.ne            #0xa98a2c
    // 0xa98a28: mov             x2, x1
    // 0xa98a2c: ldur            x0, [fp, #-0x10]
    // 0xa98a30: stur            x2, [fp, #-0x18]
    // 0xa98a34: r0 = TextSelectionToolbar()
    //     0xa98a34: bl              #0xa3350c  ; AllocateTextSelectionToolbarStub -> TextSelectionToolbar (size=0x1c)
    // 0xa98a38: mov             x1, x0
    // 0xa98a3c: ldur            x0, [fp, #-0x20]
    // 0xa98a40: StoreField: r1->field_b = r0
    //     0xa98a40: stur            w0, [x1, #0xb]
    // 0xa98a44: ldur            x0, [fp, #-0x18]
    // 0xa98a48: StoreField: r1->field_f = r0
    //     0xa98a48: stur            w0, [x1, #0xf]
    // 0xa98a4c: r0 = Closure: (BuildContext, Widget) => Widget from Function '_defaultToolbarBuilder@614142888': static.
    //     0xa98a4c: add             x0, PP, #0x39, lsl #12  ; [pp+0x39a48] Closure: (BuildContext, Widget) => Widget from Function '_defaultToolbarBuilder@614142888': static. (0x7e54fb40e098)
    //     0xa98a50: ldr             x0, [x0, #0xa48]
    // 0xa98a54: ArrayStore: r1[0] = r0  ; List_4
    //     0xa98a54: stur            w0, [x1, #0x17]
    // 0xa98a58: ldur            x0, [fp, #-0x10]
    // 0xa98a5c: StoreField: r1->field_13 = r0
    //     0xa98a5c: stur            w0, [x1, #0x13]
    // 0xa98a60: mov             x0, x1
    // 0xa98a64: LeaveFrame
    //     0xa98a64: mov             SP, fp
    //     0xa98a68: ldp             fp, lr, [SP], #0x10
    // 0xa98a6c: ret
    //     0xa98a6c: ret             
    // 0xa98a70: ldur            x1, [fp, #-8]
    // 0xa98a74: ldur            x0, [fp, #-0x10]
    // 0xa98a78: b               #0xa98b54
    // 0xa98a7c: ldur            x1, [fp, #-8]
    // 0xa98a80: ldur            x0, [fp, #-0x10]
    // 0xa98a84: LoadField: r2 = r1->field_13
    //     0xa98a84: ldur            w2, [x1, #0x13]
    // 0xa98a88: DecompressPointer r2
    //     0xa98a88: add             x2, x2, HEAP, lsl #32
    // 0xa98a8c: LoadField: r1 = r2->field_7
    //     0xa98a8c: ldur            w1, [x2, #7]
    // 0xa98a90: DecompressPointer r1
    //     0xa98a90: add             x1, x1, HEAP, lsl #32
    // 0xa98a94: stur            x1, [fp, #-0x20]
    // 0xa98a98: LoadField: r3 = r2->field_b
    //     0xa98a98: ldur            w3, [x2, #0xb]
    // 0xa98a9c: DecompressPointer r3
    //     0xa98a9c: add             x3, x3, HEAP, lsl #32
    // 0xa98aa0: cmp             w3, NULL
    // 0xa98aa4: b.ne            #0xa98ab0
    // 0xa98aa8: mov             x2, x1
    // 0xa98aac: b               #0xa98ab4
    // 0xa98ab0: mov             x2, x3
    // 0xa98ab4: stur            x2, [fp, #-0x18]
    // 0xa98ab8: r0 = CupertinoTextSelectionToolbar()
    //     0xa98ab8: bl              #0xa98ba8  ; AllocateCupertinoTextSelectionToolbarStub -> CupertinoTextSelectionToolbar (size=0x1c)
    // 0xa98abc: mov             x1, x0
    // 0xa98ac0: ldur            x0, [fp, #-0x20]
    // 0xa98ac4: StoreField: r1->field_b = r0
    //     0xa98ac4: stur            w0, [x1, #0xb]
    // 0xa98ac8: ldur            x0, [fp, #-0x18]
    // 0xa98acc: StoreField: r1->field_f = r0
    //     0xa98acc: stur            w0, [x1, #0xf]
    // 0xa98ad0: ldur            x2, [fp, #-0x10]
    // 0xa98ad4: StoreField: r1->field_13 = r2
    //     0xa98ad4: stur            w2, [x1, #0x13]
    // 0xa98ad8: r0 = Closure: (BuildContext, Offset, Offset, Widget) => Widget from Function '_defaultToolbarBuilder@247408280': static.
    //     0xa98ad8: add             x0, PP, #0x39, lsl #12  ; [pp+0x39a50] Closure: (BuildContext, Offset, Offset, Widget) => Widget from Function '_defaultToolbarBuilder@247408280': static. (0x7e54fb3e2edc)
    //     0xa98adc: ldr             x0, [x0, #0xa50]
    // 0xa98ae0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa98ae0: stur            w0, [x1, #0x17]
    // 0xa98ae4: mov             x0, x1
    // 0xa98ae8: LeaveFrame
    //     0xa98ae8: mov             SP, fp
    //     0xa98aec: ldp             fp, lr, [SP], #0x10
    // 0xa98af0: ret
    //     0xa98af0: ret             
    // 0xa98af4: ldur            x1, [fp, #-8]
    // 0xa98af8: ldur            x2, [fp, #-0x10]
    // 0xa98afc: cmp             x0, #4
    // 0xa98b00: b.gt            #0xa98b50
    // 0xa98b04: cmp             x0, #3
    // 0xa98b08: b.gt            #0xa98b14
    // 0xa98b0c: mov             x0, x2
    // 0xa98b10: b               #0xa98b54
    // 0xa98b14: LoadField: r0 = r1->field_13
    //     0xa98b14: ldur            w0, [x1, #0x13]
    // 0xa98b18: DecompressPointer r0
    //     0xa98b18: add             x0, x0, HEAP, lsl #32
    // 0xa98b1c: LoadField: r1 = r0->field_7
    //     0xa98b1c: ldur            w1, [x0, #7]
    // 0xa98b20: DecompressPointer r1
    //     0xa98b20: add             x1, x1, HEAP, lsl #32
    // 0xa98b24: stur            x1, [fp, #-0x18]
    // 0xa98b28: r0 = CupertinoDesktopTextSelectionToolbar()
    //     0xa98b28: bl              #0xa98b9c  ; AllocateCupertinoDesktopTextSelectionToolbarStub -> CupertinoDesktopTextSelectionToolbar (size=0x14)
    // 0xa98b2c: mov             x1, x0
    // 0xa98b30: ldur            x0, [fp, #-0x18]
    // 0xa98b34: StoreField: r1->field_b = r0
    //     0xa98b34: stur            w0, [x1, #0xb]
    // 0xa98b38: ldur            x0, [fp, #-0x10]
    // 0xa98b3c: StoreField: r1->field_f = r0
    //     0xa98b3c: stur            w0, [x1, #0xf]
    // 0xa98b40: mov             x0, x1
    // 0xa98b44: LeaveFrame
    //     0xa98b44: mov             SP, fp
    //     0xa98b48: ldp             fp, lr, [SP], #0x10
    // 0xa98b4c: ret
    //     0xa98b4c: ret             
    // 0xa98b50: mov             x0, x2
    // 0xa98b54: LoadField: r2 = r1->field_13
    //     0xa98b54: ldur            w2, [x1, #0x13]
    // 0xa98b58: DecompressPointer r2
    //     0xa98b58: add             x2, x2, HEAP, lsl #32
    // 0xa98b5c: LoadField: r1 = r2->field_7
    //     0xa98b5c: ldur            w1, [x2, #7]
    // 0xa98b60: DecompressPointer r1
    //     0xa98b60: add             x1, x1, HEAP, lsl #32
    // 0xa98b64: stur            x1, [fp, #-8]
    // 0xa98b68: r0 = DesktopTextSelectionToolbar()
    //     0xa98b68: bl              #0xa98b90  ; AllocateDesktopTextSelectionToolbarStub -> DesktopTextSelectionToolbar (size=0x14)
    // 0xa98b6c: ldur            x1, [fp, #-8]
    // 0xa98b70: StoreField: r0->field_b = r1
    //     0xa98b70: stur            w1, [x0, #0xb]
    // 0xa98b74: ldur            x1, [fp, #-0x10]
    // 0xa98b78: StoreField: r0->field_f = r1
    //     0xa98b78: stur            w1, [x0, #0xf]
    // 0xa98b7c: LeaveFrame
    //     0xa98b7c: mov             SP, fp
    //     0xa98b80: ldp             fp, lr, [SP], #0x10
    // 0xa98b84: ret
    //     0xa98b84: ret             
    // 0xa98b88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa98b88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa98b8c: b               #0xa98980
  }
  static _ getAdaptiveButtons(/* No info */) {
    // ** addr: 0xa98bb4, size: 0x32c
    // 0xa98bb4: EnterFrame
    //     0xa98bb4: stp             fp, lr, [SP, #-0x10]!
    //     0xa98bb8: mov             fp, SP
    // 0xa98bbc: AllocStack(0x70)
    //     0xa98bbc: sub             SP, SP, #0x70
    // 0xa98bc0: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa98bc0: stur            x1, [fp, #-8]
    //     0xa98bc4: stur            x2, [fp, #-0x10]
    // 0xa98bc8: CheckStackOverflow
    //     0xa98bc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa98bcc: cmp             SP, x16
    //     0xa98bd0: b.ls            #0xa98ed0
    // 0xa98bd4: r1 = 1
    //     0xa98bd4: movz            x1, #0x1
    // 0xa98bd8: r0 = AllocateContext()
    //     0xa98bd8: bl              #0xec126c  ; AllocateContextStub
    // 0xa98bdc: ldur            x1, [fp, #-8]
    // 0xa98be0: stur            x0, [fp, #-0x18]
    // 0xa98be4: StoreField: r0->field_f = r1
    //     0xa98be4: stur            w1, [x0, #0xf]
    // 0xa98be8: r0 = of()
    //     0xa98be8: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa98bec: LoadField: r1 = r0->field_23
    //     0xa98bec: ldur            w1, [x0, #0x23]
    // 0xa98bf0: DecompressPointer r1
    //     0xa98bf0: add             x1, x1, HEAP, lsl #32
    // 0xa98bf4: LoadField: r0 = r1->field_7
    //     0xa98bf4: ldur            x0, [x1, #7]
    // 0xa98bf8: cmp             x0, #2
    // 0xa98bfc: b.gt            #0xa98e58
    // 0xa98c00: cmp             x0, #1
    // 0xa98c04: b.gt            #0xa98e24
    // 0xa98c08: r1 = <Widget>
    //     0xa98c08: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa98c0c: r2 = 0
    //     0xa98c0c: movz            x2, #0
    // 0xa98c10: r0 = _GrowableList()
    //     0xa98c10: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa98c14: stur            x0, [fp, #-0x28]
    // 0xa98c18: r3 = 0
    //     0xa98c18: movz            x3, #0
    // 0xa98c1c: ldur            x1, [fp, #-0x10]
    // 0xa98c20: ldur            x2, [fp, #-0x18]
    // 0xa98c24: stur            x3, [fp, #-0x20]
    // 0xa98c28: CheckStackOverflow
    //     0xa98c28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa98c2c: cmp             SP, x16
    //     0xa98c30: b.ls            #0xa98ed8
    // 0xa98c34: LoadField: r4 = r1->field_b
    //     0xa98c34: ldur            w4, [x1, #0xb]
    // 0xa98c38: r5 = LoadInt32Instr(r4)
    //     0xa98c38: sbfx            x5, x4, #1, #0x1f
    // 0xa98c3c: cmp             x3, x5
    // 0xa98c40: b.ge            #0xa98e10
    // 0xa98c44: LoadField: r4 = r1->field_f
    //     0xa98c44: ldur            w4, [x1, #0xf]
    // 0xa98c48: DecompressPointer r4
    //     0xa98c48: add             x4, x4, HEAP, lsl #32
    // 0xa98c4c: ArrayLoad: r6 = r4[r3]  ; Unknown_4
    //     0xa98c4c: add             x16, x4, x3, lsl #2
    //     0xa98c50: ldur            w6, [x16, #0xf]
    // 0xa98c54: DecompressPointer r6
    //     0xa98c54: add             x6, x6, HEAP, lsl #32
    // 0xa98c58: stur            x6, [fp, #-8]
    // 0xa98c5c: cbnz            x3, #0xa98c80
    // 0xa98c60: cmp             x5, #1
    // 0xa98c64: b.ne            #0xa98c74
    // 0xa98c68: r4 = Instance__TextSelectionToolbarItemPosition
    //     0xa98c68: add             x4, PP, #0x39, lsl #12  ; [pp+0x39a60] Obj!_TextSelectionToolbarItemPosition@e360a1
    //     0xa98c6c: ldr             x4, [x4, #0xa60]
    // 0xa98c70: b               #0xa98ca0
    // 0xa98c74: r4 = Instance__TextSelectionToolbarItemPosition
    //     0xa98c74: add             x4, PP, #0x39, lsl #12  ; [pp+0x39a68] Obj!_TextSelectionToolbarItemPosition@e36081
    //     0xa98c78: ldr             x4, [x4, #0xa68]
    // 0xa98c7c: b               #0xa98ca0
    // 0xa98c80: sub             x4, x5, #1
    // 0xa98c84: cmp             x3, x4
    // 0xa98c88: b.ne            #0xa98c98
    // 0xa98c8c: r4 = Instance__TextSelectionToolbarItemPosition
    //     0xa98c8c: add             x4, PP, #0x39, lsl #12  ; [pp+0x39a70] Obj!_TextSelectionToolbarItemPosition@e36061
    //     0xa98c90: ldr             x4, [x4, #0xa70]
    // 0xa98c94: b               #0xa98ca0
    // 0xa98c98: r4 = Instance__TextSelectionToolbarItemPosition
    //     0xa98c98: add             x4, PP, #0x39, lsl #12  ; [pp+0x39a78] Obj!_TextSelectionToolbarItemPosition@e36041
    //     0xa98c9c: ldr             x4, [x4, #0xa78]
    // 0xa98ca0: r16 = Instance__TextSelectionToolbarItemPosition
    //     0xa98ca0: add             x16, PP, #0x39, lsl #12  ; [pp+0x39a68] Obj!_TextSelectionToolbarItemPosition@e36081
    //     0xa98ca4: ldr             x16, [x16, #0xa68]
    // 0xa98ca8: cmp             w4, w16
    // 0xa98cac: b.eq            #0xa98cc0
    // 0xa98cb0: r16 = Instance__TextSelectionToolbarItemPosition
    //     0xa98cb0: add             x16, PP, #0x39, lsl #12  ; [pp+0x39a60] Obj!_TextSelectionToolbarItemPosition@e360a1
    //     0xa98cb4: ldr             x16, [x16, #0xa60]
    // 0xa98cb8: cmp             w4, w16
    // 0xa98cbc: b.ne            #0xa98cc8
    // 0xa98cc0: d0 = 14.500000
    //     0xa98cc0: fmov            d0, #14.50000000
    // 0xa98cc4: b               #0xa98ccc
    // 0xa98cc8: d0 = 9.500000
    //     0xa98cc8: fmov            d0, #9.50000000
    // 0xa98ccc: stur            d0, [fp, #-0x58]
    // 0xa98cd0: r16 = Instance__TextSelectionToolbarItemPosition
    //     0xa98cd0: add             x16, PP, #0x39, lsl #12  ; [pp+0x39a70] Obj!_TextSelectionToolbarItemPosition@e36061
    //     0xa98cd4: ldr             x16, [x16, #0xa70]
    // 0xa98cd8: cmp             w4, w16
    // 0xa98cdc: b.eq            #0xa98cf0
    // 0xa98ce0: r16 = Instance__TextSelectionToolbarItemPosition
    //     0xa98ce0: add             x16, PP, #0x39, lsl #12  ; [pp+0x39a60] Obj!_TextSelectionToolbarItemPosition@e360a1
    //     0xa98ce4: ldr             x16, [x16, #0xa60]
    // 0xa98ce8: cmp             w4, w16
    // 0xa98cec: b.ne            #0xa98cf8
    // 0xa98cf0: d1 = 14.500000
    //     0xa98cf0: fmov            d1, #14.50000000
    // 0xa98cf4: b               #0xa98cfc
    // 0xa98cf8: d1 = 9.500000
    //     0xa98cf8: fmov            d1, #9.50000000
    // 0xa98cfc: stur            d1, [fp, #-0x50]
    // 0xa98d00: r0 = EdgeInsets()
    //     0xa98d00: bl              #0x65dd90  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xa98d04: ldur            d0, [fp, #-0x58]
    // 0xa98d08: stur            x0, [fp, #-0x38]
    // 0xa98d0c: StoreField: r0->field_7 = d0
    //     0xa98d0c: stur            d0, [x0, #7]
    // 0xa98d10: StoreField: r0->field_f = rZR
    //     0xa98d10: stur            xzr, [x0, #0xf]
    // 0xa98d14: ldur            d0, [fp, #-0x50]
    // 0xa98d18: ArrayStore: r0[0] = d0  ; List_8
    //     0xa98d18: stur            d0, [x0, #0x17]
    // 0xa98d1c: StoreField: r0->field_1f = rZR
    //     0xa98d1c: stur            xzr, [x0, #0x1f]
    // 0xa98d20: ldur            x2, [fp, #-8]
    // 0xa98d24: LoadField: r3 = r2->field_7
    //     0xa98d24: ldur            w3, [x2, #7]
    // 0xa98d28: DecompressPointer r3
    //     0xa98d28: add             x3, x3, HEAP, lsl #32
    // 0xa98d2c: ldur            x4, [fp, #-0x18]
    // 0xa98d30: stur            x3, [fp, #-0x30]
    // 0xa98d34: LoadField: r1 = r4->field_f
    //     0xa98d34: ldur            w1, [x4, #0xf]
    // 0xa98d38: DecompressPointer r1
    //     0xa98d38: add             x1, x1, HEAP, lsl #32
    // 0xa98d3c: r0 = getButtonLabel()
    //     0xa98d3c: bl              #0xa98eec  ; [package:flutter/src/material/adaptive_text_selection_toolbar.dart] AdaptiveTextSelectionToolbar::getButtonLabel
    // 0xa98d40: stur            x0, [fp, #-8]
    // 0xa98d44: r0 = Text()
    //     0xa98d44: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa98d48: mov             x1, x0
    // 0xa98d4c: ldur            x0, [fp, #-8]
    // 0xa98d50: stur            x1, [fp, #-0x40]
    // 0xa98d54: StoreField: r1->field_b = r0
    //     0xa98d54: stur            w0, [x1, #0xb]
    // 0xa98d58: r0 = TextSelectionToolbarTextButton()
    //     0xa98d58: bl              #0xa98ee0  ; AllocateTextSelectionToolbarTextButtonStub -> TextSelectionToolbarTextButton (size=0x1c)
    // 0xa98d5c: mov             x2, x0
    // 0xa98d60: ldur            x0, [fp, #-0x40]
    // 0xa98d64: stur            x2, [fp, #-8]
    // 0xa98d68: StoreField: r2->field_b = r0
    //     0xa98d68: stur            w0, [x2, #0xb]
    // 0xa98d6c: ldur            x0, [fp, #-0x38]
    // 0xa98d70: StoreField: r2->field_13 = r0
    //     0xa98d70: stur            w0, [x2, #0x13]
    // 0xa98d74: ldur            x0, [fp, #-0x30]
    // 0xa98d78: StoreField: r2->field_f = r0
    //     0xa98d78: stur            w0, [x2, #0xf]
    // 0xa98d7c: r0 = Instance_AlignmentDirectional
    //     0xa98d7c: add             x0, PP, #0x39, lsl #12  ; [pp+0x39a80] Obj!AlignmentDirectional@e13d91
    //     0xa98d80: ldr             x0, [x0, #0xa80]
    // 0xa98d84: ArrayStore: r2[0] = r0  ; List_4
    //     0xa98d84: stur            w0, [x2, #0x17]
    // 0xa98d88: ldur            x3, [fp, #-0x28]
    // 0xa98d8c: LoadField: r1 = r3->field_b
    //     0xa98d8c: ldur            w1, [x3, #0xb]
    // 0xa98d90: LoadField: r4 = r3->field_f
    //     0xa98d90: ldur            w4, [x3, #0xf]
    // 0xa98d94: DecompressPointer r4
    //     0xa98d94: add             x4, x4, HEAP, lsl #32
    // 0xa98d98: LoadField: r5 = r4->field_b
    //     0xa98d98: ldur            w5, [x4, #0xb]
    // 0xa98d9c: r4 = LoadInt32Instr(r1)
    //     0xa98d9c: sbfx            x4, x1, #1, #0x1f
    // 0xa98da0: stur            x4, [fp, #-0x48]
    // 0xa98da4: r1 = LoadInt32Instr(r5)
    //     0xa98da4: sbfx            x1, x5, #1, #0x1f
    // 0xa98da8: cmp             x4, x1
    // 0xa98dac: b.ne            #0xa98db8
    // 0xa98db0: mov             x1, x3
    // 0xa98db4: r0 = _growToNextCapacity()
    //     0xa98db4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa98db8: ldur            x2, [fp, #-0x28]
    // 0xa98dbc: ldur            x4, [fp, #-0x20]
    // 0xa98dc0: ldur            x3, [fp, #-0x48]
    // 0xa98dc4: add             x0, x3, #1
    // 0xa98dc8: lsl             x1, x0, #1
    // 0xa98dcc: StoreField: r2->field_b = r1
    //     0xa98dcc: stur            w1, [x2, #0xb]
    // 0xa98dd0: LoadField: r1 = r2->field_f
    //     0xa98dd0: ldur            w1, [x2, #0xf]
    // 0xa98dd4: DecompressPointer r1
    //     0xa98dd4: add             x1, x1, HEAP, lsl #32
    // 0xa98dd8: ldur            x0, [fp, #-8]
    // 0xa98ddc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa98ddc: add             x25, x1, x3, lsl #2
    //     0xa98de0: add             x25, x25, #0xf
    //     0xa98de4: str             w0, [x25]
    //     0xa98de8: tbz             w0, #0, #0xa98e04
    //     0xa98dec: ldurb           w16, [x1, #-1]
    //     0xa98df0: ldurb           w17, [x0, #-1]
    //     0xa98df4: and             x16, x17, x16, lsr #2
    //     0xa98df8: tst             x16, HEAP, lsr #32
    //     0xa98dfc: b.eq            #0xa98e04
    //     0xa98e00: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa98e04: add             x3, x4, #1
    // 0xa98e08: mov             x0, x2
    // 0xa98e0c: b               #0xa98c1c
    // 0xa98e10: mov             x2, x0
    // 0xa98e14: mov             x0, x2
    // 0xa98e18: LeaveFrame
    //     0xa98e18: mov             SP, fp
    //     0xa98e1c: ldp             fp, lr, [SP], #0x10
    // 0xa98e20: ret
    //     0xa98e20: ret             
    // 0xa98e24: r1 = Function '<anonymous closure>': static.
    //     0xa98e24: add             x1, PP, #0x39, lsl #12  ; [pp+0x39a88] AnonymousClosure: static (0xa99270), in [package:flutter/src/material/adaptive_text_selection_toolbar.dart] AdaptiveTextSelectionToolbar::getAdaptiveButtons (0xa98bb4)
    //     0xa98e28: ldr             x1, [x1, #0xa88]
    // 0xa98e2c: r2 = Null
    //     0xa98e2c: mov             x2, NULL
    // 0xa98e30: r0 = AllocateClosure()
    //     0xa98e30: bl              #0xec1630  ; AllocateClosureStub
    // 0xa98e34: r16 = <Widget>
    //     0xa98e34: ldr             x16, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa98e38: ldur            lr, [fp, #-0x10]
    // 0xa98e3c: stp             lr, x16, [SP, #8]
    // 0xa98e40: str             x0, [SP]
    // 0xa98e44: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa98e44: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa98e48: r0 = map()
    //     0xa98e48: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xa98e4c: LeaveFrame
    //     0xa98e4c: mov             SP, fp
    //     0xa98e50: ldp             fp, lr, [SP], #0x10
    // 0xa98e54: ret
    //     0xa98e54: ret             
    // 0xa98e58: cmp             x0, #4
    // 0xa98e5c: b.gt            #0xa98e9c
    // 0xa98e60: cmp             x0, #3
    // 0xa98e64: b.le            #0xa98e9c
    // 0xa98e68: ldur            x2, [fp, #-0x18]
    // 0xa98e6c: r1 = Function '<anonymous closure>': static.
    //     0xa98e6c: add             x1, PP, #0x39, lsl #12  ; [pp+0x39a90] AnonymousClosure: static (0xa991f4), in [package:flutter/src/material/adaptive_text_selection_toolbar.dart] AdaptiveTextSelectionToolbar::getAdaptiveButtons (0xa98bb4)
    //     0xa98e70: ldr             x1, [x1, #0xa90]
    // 0xa98e74: r0 = AllocateClosure()
    //     0xa98e74: bl              #0xec1630  ; AllocateClosureStub
    // 0xa98e78: r16 = <Widget>
    //     0xa98e78: ldr             x16, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa98e7c: ldur            lr, [fp, #-0x10]
    // 0xa98e80: stp             lr, x16, [SP, #8]
    // 0xa98e84: str             x0, [SP]
    // 0xa98e88: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa98e88: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa98e8c: r0 = map()
    //     0xa98e8c: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xa98e90: LeaveFrame
    //     0xa98e90: mov             SP, fp
    //     0xa98e94: ldp             fp, lr, [SP], #0x10
    // 0xa98e98: ret
    //     0xa98e98: ret             
    // 0xa98e9c: ldur            x2, [fp, #-0x18]
    // 0xa98ea0: r1 = Function '<anonymous closure>': static.
    //     0xa98ea0: add             x1, PP, #0x39, lsl #12  ; [pp+0x39a98] AnonymousClosure: static (0xa99070), in [package:flutter/src/material/adaptive_text_selection_toolbar.dart] AdaptiveTextSelectionToolbar::getAdaptiveButtons (0xa98bb4)
    //     0xa98ea4: ldr             x1, [x1, #0xa98]
    // 0xa98ea8: r0 = AllocateClosure()
    //     0xa98ea8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa98eac: r16 = <Widget>
    //     0xa98eac: ldr             x16, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa98eb0: ldur            lr, [fp, #-0x10]
    // 0xa98eb4: stp             lr, x16, [SP, #8]
    // 0xa98eb8: str             x0, [SP]
    // 0xa98ebc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa98ebc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa98ec0: r0 = map()
    //     0xa98ec0: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xa98ec4: LeaveFrame
    //     0xa98ec4: mov             SP, fp
    //     0xa98ec8: ldp             fp, lr, [SP], #0x10
    // 0xa98ecc: ret
    //     0xa98ecc: ret             
    // 0xa98ed0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa98ed0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa98ed4: b               #0xa98bd4
    // 0xa98ed8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa98ed8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa98edc: b               #0xa98c34
  }
  static _ getButtonLabel(/* No info */) {
    // ** addr: 0xa98eec, size: 0x184
    // 0xa98eec: EnterFrame
    //     0xa98eec: stp             fp, lr, [SP, #-0x10]!
    //     0xa98ef0: mov             fp, SP
    // 0xa98ef4: AllocStack(0x18)
    //     0xa98ef4: sub             SP, SP, #0x18
    // 0xa98ef8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa98ef8: mov             x0, x1
    //     0xa98efc: stur            x1, [fp, #-8]
    //     0xa98f00: stur            x2, [fp, #-0x10]
    // 0xa98f04: CheckStackOverflow
    //     0xa98f04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa98f08: cmp             SP, x16
    //     0xa98f0c: b.ls            #0xa99068
    // 0xa98f10: LoadField: r1 = r2->field_f
    //     0xa98f10: ldur            w1, [x2, #0xf]
    // 0xa98f14: DecompressPointer r1
    //     0xa98f14: add             x1, x1, HEAP, lsl #32
    // 0xa98f18: cmp             w1, NULL
    // 0xa98f1c: b.eq            #0xa98f30
    // 0xa98f20: mov             x0, x1
    // 0xa98f24: LeaveFrame
    //     0xa98f24: mov             SP, fp
    //     0xa98f28: ldp             fp, lr, [SP], #0x10
    // 0xa98f2c: ret
    //     0xa98f2c: ret             
    // 0xa98f30: mov             x1, x0
    // 0xa98f34: r0 = of()
    //     0xa98f34: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa98f38: LoadField: r1 = r0->field_23
    //     0xa98f38: ldur            w1, [x0, #0x23]
    // 0xa98f3c: DecompressPointer r1
    //     0xa98f3c: add             x1, x1, HEAP, lsl #32
    // 0xa98f40: LoadField: r0 = r1->field_7
    //     0xa98f40: ldur            x0, [x1, #7]
    // 0xa98f44: cmp             x0, #2
    // 0xa98f48: b.gt            #0xa98f58
    // 0xa98f4c: cmp             x0, #1
    // 0xa98f50: b.gt            #0xa98f68
    // 0xa98f54: b               #0xa98f80
    // 0xa98f58: cmp             x0, #4
    // 0xa98f5c: b.gt            #0xa98f80
    // 0xa98f60: cmp             x0, #3
    // 0xa98f64: b.le            #0xa98f80
    // 0xa98f68: ldur            x1, [fp, #-8]
    // 0xa98f6c: ldur            x2, [fp, #-0x10]
    // 0xa98f70: r0 = getButtonLabel()
    //     0xa98f70: bl              #0x9e3a40  ; [package:flutter/src/cupertino/text_selection_toolbar_button.dart] CupertinoTextSelectionToolbarButton::getButtonLabel
    // 0xa98f74: LeaveFrame
    //     0xa98f74: mov             SP, fp
    //     0xa98f78: ldp             fp, lr, [SP], #0x10
    // 0xa98f7c: ret
    //     0xa98f7c: ret             
    // 0xa98f80: ldur            x0, [fp, #-0x10]
    // 0xa98f84: ldur            x1, [fp, #-8]
    // 0xa98f88: r0 = of()
    //     0xa98f88: bl              #0x9179e4  ; [package:flutter/src/material/material_localizations.dart] MaterialLocalizations::of
    // 0xa98f8c: ldur            x0, [fp, #-0x10]
    // 0xa98f90: LoadField: r1 = r0->field_b
    //     0xa98f90: ldur            w1, [x0, #0xb]
    // 0xa98f94: DecompressPointer r1
    //     0xa98f94: add             x1, x1, HEAP, lsl #32
    // 0xa98f98: LoadField: r0 = r1->field_7
    //     0xa98f98: ldur            x0, [x1, #7]
    // 0xa98f9c: cmp             x0, #4
    // 0xa98fa0: b.gt            #0xa99008
    // 0xa98fa4: cmp             x0, #2
    // 0xa98fa8: b.gt            #0xa98fe0
    // 0xa98fac: cmp             x0, #1
    // 0xa98fb0: b.gt            #0xa98fd4
    // 0xa98fb4: cmp             x0, #0
    // 0xa98fb8: b.gt            #0xa98fc8
    // 0xa98fbc: r0 = "Cut"
    //     0xa98fbc: add             x0, PP, #0x39, lsl #12  ; [pp+0x39ab0] "Cut"
    //     0xa98fc0: ldr             x0, [x0, #0xab0]
    // 0xa98fc4: b               #0xa9905c
    // 0xa98fc8: r0 = "Copy"
    //     0xa98fc8: add             x0, PP, #0x39, lsl #12  ; [pp+0x39ab8] "Copy"
    //     0xa98fcc: ldr             x0, [x0, #0xab8]
    // 0xa98fd0: b               #0xa9905c
    // 0xa98fd4: r0 = "Paste"
    //     0xa98fd4: add             x0, PP, #0x39, lsl #12  ; [pp+0x39ac0] "Paste"
    //     0xa98fd8: ldr             x0, [x0, #0xac0]
    // 0xa98fdc: b               #0xa9905c
    // 0xa98fe0: cmp             x0, #3
    // 0xa98fe4: b.gt            #0xa98ff4
    // 0xa98fe8: r0 = "Select all"
    //     0xa98fe8: add             x0, PP, #0x39, lsl #12  ; [pp+0x39ac8] "Select all"
    //     0xa98fec: ldr             x0, [x0, #0xac8]
    // 0xa98ff0: b               #0xa9905c
    // 0xa98ff4: r16 = "Delete"
    //     0xa98ff4: add             x16, PP, #0x39, lsl #12  ; [pp+0x39ad0] "Delete"
    //     0xa98ff8: ldr             x16, [x16, #0xad0]
    // 0xa98ffc: str             x16, [SP]
    // 0xa99000: r0 = toUpperCase()
    //     0xa99000: bl              #0xebe0d0  ; [dart:core] _OneByteString::toUpperCase
    // 0xa99004: b               #0xa9905c
    // 0xa99008: cmp             x0, #7
    // 0xa9900c: b.gt            #0xa99044
    // 0xa99010: cmp             x0, #6
    // 0xa99014: b.gt            #0xa99038
    // 0xa99018: cmp             x0, #5
    // 0xa9901c: b.gt            #0xa9902c
    // 0xa99020: r0 = "Look Up"
    //     0xa99020: add             x0, PP, #0x39, lsl #12  ; [pp+0x39ad8] "Look Up"
    //     0xa99024: ldr             x0, [x0, #0xad8]
    // 0xa99028: b               #0xa9905c
    // 0xa9902c: r0 = "Search Web"
    //     0xa9902c: add             x0, PP, #0x39, lsl #12  ; [pp+0x39ae0] "Search Web"
    //     0xa99030: ldr             x0, [x0, #0xae0]
    // 0xa99034: b               #0xa9905c
    // 0xa99038: r0 = "Share"
    //     0xa99038: add             x0, PP, #0x39, lsl #12  ; [pp+0x39ae8] "Share"
    //     0xa9903c: ldr             x0, [x0, #0xae8]
    // 0xa99040: b               #0xa9905c
    // 0xa99044: cmp             x0, #8
    // 0xa99048: b.gt            #0xa99058
    // 0xa9904c: r0 = "Scan text"
    //     0xa9904c: add             x0, PP, #0x39, lsl #12  ; [pp+0x39af0] "Scan text"
    //     0xa99050: ldr             x0, [x0, #0xaf0]
    // 0xa99054: b               #0xa9905c
    // 0xa99058: r0 = ""
    //     0xa99058: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xa9905c: LeaveFrame
    //     0xa9905c: mov             SP, fp
    //     0xa99060: ldp             fp, lr, [SP], #0x10
    // 0xa99064: ret
    //     0xa99064: ret             
    // 0xa99068: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa99068: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9906c: b               #0xa98f10
  }
  [closure] static DesktopTextSelectionToolbarButton <anonymous closure>(dynamic, ContextMenuButtonItem) {
    // ** addr: 0xa99070, size: 0x80
    // 0xa99070: EnterFrame
    //     0xa99070: stp             fp, lr, [SP, #-0x10]!
    //     0xa99074: mov             fp, SP
    // 0xa99078: AllocStack(0x18)
    //     0xa99078: sub             SP, SP, #0x18
    // 0xa9907c: SetupParameters()
    //     0xa9907c: ldr             x0, [fp, #0x18]
    //     0xa99080: ldur            w1, [x0, #0x17]
    //     0xa99084: add             x1, x1, HEAP, lsl #32
    // 0xa99088: CheckStackOverflow
    //     0xa99088: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9908c: cmp             SP, x16
    //     0xa99090: b.ls            #0xa990e8
    // 0xa99094: LoadField: r0 = r1->field_f
    //     0xa99094: ldur            w0, [x1, #0xf]
    // 0xa99098: DecompressPointer r0
    //     0xa99098: add             x0, x0, HEAP, lsl #32
    // 0xa9909c: ldr             x2, [fp, #0x10]
    // 0xa990a0: stur            x0, [fp, #-0x10]
    // 0xa990a4: LoadField: r3 = r2->field_7
    //     0xa990a4: ldur            w3, [x2, #7]
    // 0xa990a8: DecompressPointer r3
    //     0xa990a8: add             x3, x3, HEAP, lsl #32
    // 0xa990ac: mov             x1, x0
    // 0xa990b0: stur            x3, [fp, #-8]
    // 0xa990b4: r0 = getButtonLabel()
    //     0xa990b4: bl              #0xa98eec  ; [package:flutter/src/material/adaptive_text_selection_toolbar.dart] AdaptiveTextSelectionToolbar::getButtonLabel
    // 0xa990b8: stur            x0, [fp, #-0x18]
    // 0xa990bc: r0 = DesktopTextSelectionToolbarButton()
    //     0xa990bc: bl              #0xa991e8  ; AllocateDesktopTextSelectionToolbarButtonStub -> DesktopTextSelectionToolbarButton (size=0x14)
    // 0xa990c0: mov             x1, x0
    // 0xa990c4: ldur            x2, [fp, #-0x10]
    // 0xa990c8: ldur            x3, [fp, #-8]
    // 0xa990cc: ldur            x5, [fp, #-0x18]
    // 0xa990d0: stur            x0, [fp, #-8]
    // 0xa990d4: r0 = DesktopTextSelectionToolbarButton.text()
    //     0xa990d4: bl              #0xa990f0  ; [package:flutter/src/material/desktop_text_selection_toolbar_button.dart] DesktopTextSelectionToolbarButton::DesktopTextSelectionToolbarButton.text
    // 0xa990d8: ldur            x0, [fp, #-8]
    // 0xa990dc: LeaveFrame
    //     0xa990dc: mov             SP, fp
    //     0xa990e0: ldp             fp, lr, [SP], #0x10
    // 0xa990e4: ret
    //     0xa990e4: ret             
    // 0xa990e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa990e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa990ec: b               #0xa99094
  }
  [closure] static CupertinoDesktopTextSelectionToolbarButton <anonymous closure>(dynamic, ContextMenuButtonItem) {
    // ** addr: 0xa991f4, size: 0x70
    // 0xa991f4: EnterFrame
    //     0xa991f4: stp             fp, lr, [SP, #-0x10]!
    //     0xa991f8: mov             fp, SP
    // 0xa991fc: AllocStack(0x10)
    //     0xa991fc: sub             SP, SP, #0x10
    // 0xa99200: SetupParameters()
    //     0xa99200: ldr             x0, [fp, #0x18]
    //     0xa99204: ldur            w1, [x0, #0x17]
    //     0xa99208: add             x1, x1, HEAP, lsl #32
    // 0xa9920c: CheckStackOverflow
    //     0xa9920c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa99210: cmp             SP, x16
    //     0xa99214: b.ls            #0xa9925c
    // 0xa99218: ldr             x2, [fp, #0x10]
    // 0xa9921c: LoadField: r0 = r2->field_7
    //     0xa9921c: ldur            w0, [x2, #7]
    // 0xa99220: DecompressPointer r0
    //     0xa99220: add             x0, x0, HEAP, lsl #32
    // 0xa99224: stur            x0, [fp, #-8]
    // 0xa99228: LoadField: r3 = r1->field_f
    //     0xa99228: ldur            w3, [x1, #0xf]
    // 0xa9922c: DecompressPointer r3
    //     0xa9922c: add             x3, x3, HEAP, lsl #32
    // 0xa99230: mov             x1, x3
    // 0xa99234: r0 = getButtonLabel()
    //     0xa99234: bl              #0xa98eec  ; [package:flutter/src/material/adaptive_text_selection_toolbar.dart] AdaptiveTextSelectionToolbar::getButtonLabel
    // 0xa99238: stur            x0, [fp, #-0x10]
    // 0xa9923c: r0 = CupertinoDesktopTextSelectionToolbarButton()
    //     0xa9923c: bl              #0xa99264  ; AllocateCupertinoDesktopTextSelectionToolbarButtonStub -> CupertinoDesktopTextSelectionToolbarButton (size=0x18)
    // 0xa99240: ldur            x1, [fp, #-8]
    // 0xa99244: StoreField: r0->field_b = r1
    //     0xa99244: stur            w1, [x0, #0xb]
    // 0xa99248: ldur            x1, [fp, #-0x10]
    // 0xa9924c: StoreField: r0->field_13 = r1
    //     0xa9924c: stur            w1, [x0, #0x13]
    // 0xa99250: LeaveFrame
    //     0xa99250: mov             SP, fp
    //     0xa99254: ldp             fp, lr, [SP], #0x10
    // 0xa99258: ret
    //     0xa99258: ret             
    // 0xa9925c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9925c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa99260: b               #0xa99218
  }
  [closure] static CupertinoTextSelectionToolbarButton <anonymous closure>(dynamic, ContextMenuButtonItem) {
    // ** addr: 0xa99270, size: 0x2c
    // 0xa99270: EnterFrame
    //     0xa99270: stp             fp, lr, [SP, #-0x10]!
    //     0xa99274: mov             fp, SP
    // 0xa99278: r0 = CupertinoTextSelectionToolbarButton()
    //     0xa99278: bl              #0x9e30e8  ; AllocateCupertinoTextSelectionToolbarButtonStub -> CupertinoTextSelectionToolbarButton (size=0x1c)
    // 0xa9927c: ldr             x1, [fp, #0x10]
    // 0xa99280: StoreField: r0->field_13 = r1
    //     0xa99280: stur            w1, [x0, #0x13]
    // 0xa99284: LoadField: r2 = r1->field_7
    //     0xa99284: ldur            w2, [x1, #7]
    // 0xa99288: DecompressPointer r2
    //     0xa99288: add             x2, x2, HEAP, lsl #32
    // 0xa9928c: StoreField: r0->field_f = r2
    //     0xa9928c: stur            w2, [x0, #0xf]
    // 0xa99290: LeaveFrame
    //     0xa99290: mov             SP, fp
    //     0xa99294: ldp             fp, lr, [SP], #0x10
    // 0xa99298: ret
    //     0xa99298: ret             
  }
}
