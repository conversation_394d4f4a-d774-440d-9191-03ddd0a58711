// lib: , url: package:flutter/src/material/app.dart

// class id: 1048850, size: 0x8
class :: {
}

// class id: 3426, size: 0x8, field offset: 0x8
//   const constructor, 
class MaterialScrollBehavior extends ScrollBehavior {

  _ buildOverscrollIndicator(/* No info */) {
    // ** addr: 0xd8e2cc, size: 0x140
    // 0xd8e2cc: EnterFrame
    //     0xd8e2cc: stp             fp, lr, [SP, #-0x10]!
    //     0xd8e2d0: mov             fp, SP
    // 0xd8e2d4: AllocStack(0x20)
    //     0xd8e2d4: sub             SP, SP, #0x20
    // 0xd8e2d8: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0xd8e2d8: mov             x0, x2
    //     0xd8e2dc: stur            x2, [fp, #-8]
    //     0xd8e2e0: stur            x3, [fp, #-0x10]
    //     0xd8e2e4: stur            x5, [fp, #-0x18]
    // 0xd8e2e8: CheckStackOverflow
    //     0xd8e2e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8e2ec: cmp             SP, x16
    //     0xd8e2f0: b.ls            #0xd8e404
    // 0xd8e2f4: mov             x1, x0
    // 0xd8e2f8: r0 = of()
    //     0xd8e2f8: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xd8e2fc: LoadField: r1 = r0->field_2f
    //     0xd8e2fc: ldur            w1, [x0, #0x2f]
    // 0xd8e300: DecompressPointer r1
    //     0xd8e300: add             x1, x1, HEAP, lsl #32
    // 0xd8e304: tbnz            w1, #4, #0xd8e314
    // 0xd8e308: r0 = Instance_AndroidOverscrollIndicator
    //     0xd8e308: add             x0, PP, #0x55, lsl #12  ; [pp+0x55188] Obj!AndroidOverscrollIndicator@e33c01
    //     0xd8e30c: ldr             x0, [x0, #0x188]
    // 0xd8e310: b               #0xd8e31c
    // 0xd8e314: r0 = Instance_AndroidOverscrollIndicator
    //     0xd8e314: add             x0, PP, #0x55, lsl #12  ; [pp+0x55190] Obj!AndroidOverscrollIndicator@e33be1
    //     0xd8e318: ldr             x0, [x0, #0x190]
    // 0xd8e31c: ldur            x1, [fp, #-8]
    // 0xd8e320: stur            x0, [fp, #-0x20]
    // 0xd8e324: r0 = of()
    //     0xd8e324: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xd8e328: ldur            x0, [fp, #-0x20]
    // 0xd8e32c: LoadField: r1 = r0->field_7
    //     0xd8e32c: ldur            x1, [x0, #7]
    // 0xd8e330: cmp             x1, #0
    // 0xd8e334: b.gt            #0xd8e38c
    // 0xd8e338: ldur            x1, [fp, #-0x10]
    // 0xd8e33c: ldur            x0, [fp, #-0x18]
    // 0xd8e340: LoadField: r2 = r0->field_7
    //     0xd8e340: ldur            w2, [x0, #7]
    // 0xd8e344: DecompressPointer r2
    //     0xd8e344: add             x2, x2, HEAP, lsl #32
    // 0xd8e348: stur            x2, [fp, #-0x20]
    // 0xd8e34c: r0 = StretchingOverscrollIndicator()
    //     0xd8e34c: bl              #0xd8e418  ; AllocateStretchingOverscrollIndicatorStub -> StretchingOverscrollIndicator (size=0x1c)
    // 0xd8e350: mov             x1, x0
    // 0xd8e354: ldur            x0, [fp, #-0x20]
    // 0xd8e358: StoreField: r1->field_b = r0
    //     0xd8e358: stur            w0, [x1, #0xb]
    // 0xd8e35c: r2 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xd8e35c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xd8e360: ldr             x2, [x2, #0xf58]
    // 0xd8e364: StoreField: r1->field_f = r2
    //     0xd8e364: stur            w2, [x1, #0xf]
    // 0xd8e368: r0 = Instance_Clip
    //     0xd8e368: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xd8e36c: ldr             x0, [x0, #0x7c0]
    // 0xd8e370: StoreField: r1->field_13 = r0
    //     0xd8e370: stur            w0, [x1, #0x13]
    // 0xd8e374: ldur            x3, [fp, #-0x10]
    // 0xd8e378: ArrayStore: r1[0] = r3  ; List_4
    //     0xd8e378: stur            w3, [x1, #0x17]
    // 0xd8e37c: mov             x0, x1
    // 0xd8e380: LeaveFrame
    //     0xd8e380: mov             SP, fp
    //     0xd8e384: ldp             fp, lr, [SP], #0x10
    // 0xd8e388: ret
    //     0xd8e388: ret             
    // 0xd8e38c: ldur            x3, [fp, #-0x10]
    // 0xd8e390: ldur            x0, [fp, #-0x18]
    // 0xd8e394: r2 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xd8e394: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xd8e398: ldr             x2, [x2, #0xf58]
    // 0xd8e39c: LoadField: r4 = r0->field_7
    //     0xd8e39c: ldur            w4, [x0, #7]
    // 0xd8e3a0: DecompressPointer r4
    //     0xd8e3a0: add             x4, x4, HEAP, lsl #32
    // 0xd8e3a4: ldur            x1, [fp, #-8]
    // 0xd8e3a8: stur            x4, [fp, #-0x20]
    // 0xd8e3ac: r0 = of()
    //     0xd8e3ac: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xd8e3b0: LoadField: r1 = r0->field_3f
    //     0xd8e3b0: ldur            w1, [x0, #0x3f]
    // 0xd8e3b4: DecompressPointer r1
    //     0xd8e3b4: add             x1, x1, HEAP, lsl #32
    // 0xd8e3b8: LoadField: r0 = r1->field_2b
    //     0xd8e3b8: ldur            w0, [x1, #0x2b]
    // 0xd8e3bc: DecompressPointer r0
    //     0xd8e3bc: add             x0, x0, HEAP, lsl #32
    // 0xd8e3c0: stur            x0, [fp, #-8]
    // 0xd8e3c4: r0 = GlowingOverscrollIndicator()
    //     0xd8e3c4: bl              #0xd8e40c  ; AllocateGlowingOverscrollIndicatorStub -> GlowingOverscrollIndicator (size=0x24)
    // 0xd8e3c8: r1 = true
    //     0xd8e3c8: add             x1, NULL, #0x20  ; true
    // 0xd8e3cc: StoreField: r0->field_b = r1
    //     0xd8e3cc: stur            w1, [x0, #0xb]
    // 0xd8e3d0: StoreField: r0->field_f = r1
    //     0xd8e3d0: stur            w1, [x0, #0xf]
    // 0xd8e3d4: ldur            x1, [fp, #-0x20]
    // 0xd8e3d8: StoreField: r0->field_13 = r1
    //     0xd8e3d8: stur            w1, [x0, #0x13]
    // 0xd8e3dc: ldur            x1, [fp, #-8]
    // 0xd8e3e0: ArrayStore: r0[0] = r1  ; List_4
    //     0xd8e3e0: stur            w1, [x0, #0x17]
    // 0xd8e3e4: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xd8e3e4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xd8e3e8: ldr             x1, [x1, #0xf58]
    // 0xd8e3ec: StoreField: r0->field_1b = r1
    //     0xd8e3ec: stur            w1, [x0, #0x1b]
    // 0xd8e3f0: ldur            x1, [fp, #-0x10]
    // 0xd8e3f4: StoreField: r0->field_1f = r1
    //     0xd8e3f4: stur            w1, [x0, #0x1f]
    // 0xd8e3f8: LeaveFrame
    //     0xd8e3f8: mov             SP, fp
    //     0xd8e3fc: ldp             fp, lr, [SP], #0x10
    // 0xd8e400: ret
    //     0xd8e400: ret             
    // 0xd8e404: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8e404: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8e408: b               #0xd8e2f4
  }
  _ buildScrollbar(/* No info */) {
    // ** addr: 0xd8e5e8, size: 0xac
    // 0xd8e5e8: EnterFrame
    //     0xd8e5e8: stp             fp, lr, [SP, #-0x10]!
    //     0xd8e5ec: mov             fp, SP
    // 0xd8e5f0: AllocStack(0x8)
    //     0xd8e5f0: sub             SP, SP, #8
    // 0xd8e5f4: SetupParameters(MaterialScrollBehavior this /* r1 => r2 */, dynamic _ /* r2 => r1 */, dynamic _ /* r3 => r0, fp-0x8 */)
    //     0xd8e5f4: mov             x16, x2
    //     0xd8e5f8: mov             x2, x1
    //     0xd8e5fc: mov             x1, x16
    //     0xd8e600: mov             x0, x3
    //     0xd8e604: stur            x3, [fp, #-8]
    // 0xd8e608: CheckStackOverflow
    //     0xd8e608: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8e60c: cmp             SP, x16
    //     0xd8e610: b.ls            #0xd8e68c
    // 0xd8e614: LoadField: r2 = r5->field_7
    //     0xd8e614: ldur            w2, [x5, #7]
    // 0xd8e618: DecompressPointer r2
    //     0xd8e618: add             x2, x2, HEAP, lsl #32
    // 0xd8e61c: r16 = Instance_AxisDirection
    //     0xd8e61c: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xd8e620: cmp             w2, w16
    // 0xd8e624: b.eq            #0xd8e634
    // 0xd8e628: r16 = Instance_AxisDirection
    //     0xd8e628: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xd8e62c: cmp             w2, w16
    // 0xd8e630: b.ne            #0xd8e63c
    // 0xd8e634: r2 = Instance_Axis
    //     0xd8e634: ldr             x2, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xd8e638: b               #0xd8e660
    // 0xd8e63c: r16 = Instance_AxisDirection
    //     0xd8e63c: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0xd8e640: cmp             w2, w16
    // 0xd8e644: b.eq            #0xd8e654
    // 0xd8e648: r16 = Instance_AxisDirection
    //     0xd8e648: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0xd8e64c: cmp             w2, w16
    // 0xd8e650: b.ne            #0xd8e65c
    // 0xd8e654: r2 = Instance_Axis
    //     0xd8e654: ldr             x2, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xd8e658: b               #0xd8e660
    // 0xd8e65c: r2 = Null
    //     0xd8e65c: mov             x2, NULL
    // 0xd8e660: LoadField: r3 = r2->field_7
    //     0xd8e660: ldur            x3, [x2, #7]
    // 0xd8e664: cmp             x3, #0
    // 0xd8e668: b.gt            #0xd8e678
    // 0xd8e66c: LeaveFrame
    //     0xd8e66c: mov             SP, fp
    //     0xd8e670: ldp             fp, lr, [SP], #0x10
    // 0xd8e674: ret
    //     0xd8e674: ret             
    // 0xd8e678: r0 = of()
    //     0xd8e678: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xd8e67c: ldur            x0, [fp, #-8]
    // 0xd8e680: LeaveFrame
    //     0xd8e680: mov             SP, fp
    //     0xd8e684: ldp             fp, lr, [SP], #0x10
    // 0xd8e688: ret
    //     0xd8e688: ret             
    // 0xd8e68c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8e68c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8e690: b               #0xd8e614
  }
  _ getPlatform(/* No info */) {
    // ** addr: 0xd9055c, size: 0x38
    // 0xd9055c: EnterFrame
    //     0xd9055c: stp             fp, lr, [SP, #-0x10]!
    //     0xd90560: mov             fp, SP
    // 0xd90564: mov             x0, x1
    // 0xd90568: mov             x1, x2
    // 0xd9056c: CheckStackOverflow
    //     0xd9056c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd90570: cmp             SP, x16
    //     0xd90574: b.ls            #0xd9058c
    // 0xd90578: r0 = of()
    //     0xd90578: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xd9057c: r0 = Instance_TargetPlatform
    //     0xd9057c: ldr             x0, [PP, #0x5110]  ; [pp+0x5110] Obj!TargetPlatform@e36f61
    // 0xd90580: LeaveFrame
    //     0xd90580: mov             SP, fp
    //     0xd90584: ldp             fp, lr, [SP], #0x10
    // 0xd90588: ret
    //     0xd90588: ret             
    // 0xd9058c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd9058c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd90590: b               #0xd90578
  }
}

// class id: 4331, size: 0x18, field offset: 0x14
class _MaterialAppState extends State<dynamic> {

  late HeroController _heroController; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x93207c, size: 0x58
    // 0x93207c: EnterFrame
    //     0x93207c: stp             fp, lr, [SP, #-0x10]!
    //     0x932080: mov             fp, SP
    // 0x932084: AllocStack(0x8)
    //     0x932084: sub             SP, SP, #8
    // 0x932088: SetupParameters(_MaterialAppState this /* r1 => r1, fp-0x8 */)
    //     0x932088: stur            x1, [fp, #-8]
    // 0x93208c: CheckStackOverflow
    //     0x93208c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x932090: cmp             SP, x16
    //     0x932094: b.ls            #0x9320cc
    // 0x932098: r0 = createMaterialHeroController()
    //     0x932098: bl              #0x9320f8  ; [package:flutter/src/material/app.dart] MaterialApp::createMaterialHeroController
    // 0x93209c: ldur            x1, [fp, #-8]
    // 0x9320a0: StoreField: r1->field_13 = r0
    //     0x9320a0: stur            w0, [x1, #0x13]
    //     0x9320a4: ldurb           w16, [x1, #-1]
    //     0x9320a8: ldurb           w17, [x0, #-1]
    //     0x9320ac: and             x16, x17, x16, lsr #2
    //     0x9320b0: tst             x16, HEAP, lsr #32
    //     0x9320b4: b.eq            #0x9320bc
    //     0x9320b8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9320bc: r0 = Null
    //     0x9320bc: mov             x0, NULL
    // 0x9320c0: LeaveFrame
    //     0x9320c0: mov             SP, fp
    //     0x9320c4: ldp             fp, lr, [SP], #0x10
    // 0x9320c8: ret
    //     0x9320c8: ret             
    // 0x9320cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9320cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9320d0: b               #0x932098
  }
  _ build(/* No info */) {
    // ** addr: 0x9e3e68, size: 0xf8
    // 0x9e3e68: EnterFrame
    //     0x9e3e68: stp             fp, lr, [SP, #-0x10]!
    //     0x9e3e6c: mov             fp, SP
    // 0x9e3e70: AllocStack(0x18)
    //     0x9e3e70: sub             SP, SP, #0x18
    // 0x9e3e74: SetupParameters(_MaterialAppState this /* r1 => r0, fp-0x8 */)
    //     0x9e3e74: mov             x0, x1
    //     0x9e3e78: stur            x1, [fp, #-8]
    // 0x9e3e7c: CheckStackOverflow
    //     0x9e3e7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e3e80: cmp             SP, x16
    //     0x9e3e84: b.ls            #0x9e3f48
    // 0x9e3e88: mov             x1, x0
    // 0x9e3e8c: r0 = _buildWidgetApp()
    //     0x9e3e8c: bl              #0x9e3f84  ; [package:flutter/src/material/app.dart] _MaterialAppState::_buildWidgetApp
    // 0x9e3e90: stur            x0, [fp, #-0x10]
    // 0x9e3e94: r0 = Focus()
    //     0x9e3e94: bl              #0x9e3f78  ; AllocateFocusStub -> Focus (size=0x40)
    // 0x9e3e98: mov             x3, x0
    // 0x9e3e9c: ldur            x0, [fp, #-0x10]
    // 0x9e3ea0: stur            x3, [fp, #-0x18]
    // 0x9e3ea4: StoreField: r3->field_f = r0
    //     0x9e3ea4: stur            w0, [x3, #0xf]
    // 0x9e3ea8: r0 = false
    //     0x9e3ea8: add             x0, NULL, #0x30  ; false
    // 0x9e3eac: ArrayStore: r3[0] = r0  ; List_4
    //     0x9e3eac: stur            w0, [x3, #0x17]
    // 0x9e3eb0: r1 = true
    //     0x9e3eb0: add             x1, NULL, #0x20  ; true
    // 0x9e3eb4: StoreField: r3->field_37 = r1
    //     0x9e3eb4: stur            w1, [x3, #0x37]
    // 0x9e3eb8: r1 = Function '<anonymous closure>':.
    //     0x9e3eb8: add             x1, PP, #0x44, lsl #12  ; [pp+0x44860] AnonymousClosure: (0x9e4b84), in [package:flutter/src/material/app.dart] _MaterialAppState::build (0x9e3e68)
    //     0x9e3ebc: ldr             x1, [x1, #0x860]
    // 0x9e3ec0: r2 = Null
    //     0x9e3ec0: mov             x2, NULL
    // 0x9e3ec4: r0 = AllocateClosure()
    //     0x9e3ec4: bl              #0xec1630  ; AllocateClosureStub
    // 0x9e3ec8: mov             x1, x0
    // 0x9e3ecc: ldur            x0, [fp, #-0x18]
    // 0x9e3ed0: StoreField: r0->field_1f = r1
    //     0x9e3ed0: stur            w1, [x0, #0x1f]
    // 0x9e3ed4: r1 = false
    //     0x9e3ed4: add             x1, NULL, #0x30  ; false
    // 0x9e3ed8: StoreField: r0->field_27 = r1
    //     0x9e3ed8: stur            w1, [x0, #0x27]
    // 0x9e3edc: ldur            x1, [fp, #-8]
    // 0x9e3ee0: LoadField: r2 = r1->field_b
    //     0x9e3ee0: ldur            w2, [x1, #0xb]
    // 0x9e3ee4: DecompressPointer r2
    //     0x9e3ee4: add             x2, x2, HEAP, lsl #32
    // 0x9e3ee8: cmp             w2, NULL
    // 0x9e3eec: b.eq            #0x9e3f50
    // 0x9e3ef0: LoadField: r2 = r1->field_13
    //     0x9e3ef0: ldur            w2, [x1, #0x13]
    // 0x9e3ef4: DecompressPointer r2
    //     0x9e3ef4: add             x2, x2, HEAP, lsl #32
    // 0x9e3ef8: r16 = Sentinel
    //     0x9e3ef8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e3efc: cmp             w2, w16
    // 0x9e3f00: b.eq            #0x9e3f54
    // 0x9e3f04: stur            x2, [fp, #-0x10]
    // 0x9e3f08: r0 = HeroControllerScope()
    //     0x9e3f08: bl              #0x9e3f6c  ; AllocateHeroControllerScopeStub -> HeroControllerScope (size=0x14)
    // 0x9e3f0c: mov             x1, x0
    // 0x9e3f10: ldur            x0, [fp, #-0x10]
    // 0x9e3f14: stur            x1, [fp, #-8]
    // 0x9e3f18: StoreField: r1->field_f = r0
    //     0x9e3f18: stur            w0, [x1, #0xf]
    // 0x9e3f1c: ldur            x0, [fp, #-0x18]
    // 0x9e3f20: StoreField: r1->field_b = r0
    //     0x9e3f20: stur            w0, [x1, #0xb]
    // 0x9e3f24: r0 = ScrollConfiguration()
    //     0x9e3f24: bl              #0x9e3f60  ; AllocateScrollConfigurationStub -> ScrollConfiguration (size=0x14)
    // 0x9e3f28: r1 = Instance_MaterialScrollBehavior
    //     0x9e3f28: add             x1, PP, #0x44, lsl #12  ; [pp+0x44868] Obj!MaterialScrollBehavior@e14a01
    //     0x9e3f2c: ldr             x1, [x1, #0x868]
    // 0x9e3f30: StoreField: r0->field_f = r1
    //     0x9e3f30: stur            w1, [x0, #0xf]
    // 0x9e3f34: ldur            x1, [fp, #-8]
    // 0x9e3f38: StoreField: r0->field_b = r1
    //     0x9e3f38: stur            w1, [x0, #0xb]
    // 0x9e3f3c: LeaveFrame
    //     0x9e3f3c: mov             SP, fp
    //     0x9e3f40: ldp             fp, lr, [SP], #0x10
    // 0x9e3f44: ret
    //     0x9e3f44: ret             
    // 0x9e3f48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e3f48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e3f4c: b               #0x9e3e88
    // 0x9e3f50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e3f50: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e3f54: r9 = _heroController
    //     0x9e3f54: add             x9, PP, #0x44, lsl #12  ; [pp+0x44870] Field <_MaterialAppState@499125171._heroController@499125171>: late (offset: 0x14)
    //     0x9e3f58: ldr             x9, [x9, #0x870]
    // 0x9e3f5c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9e3f5c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _buildWidgetApp(/* No info */) {
    // ** addr: 0x9e3f84, size: 0x174
    // 0x9e3f84: EnterFrame
    //     0x9e3f84: stp             fp, lr, [SP, #-0x10]!
    //     0x9e3f88: mov             fp, SP
    // 0x9e3f8c: AllocStack(0x90)
    //     0x9e3f8c: sub             SP, SP, #0x90
    // 0x9e3f90: SetupParameters(_MaterialAppState this /* r1 => r0, fp-0x10 */)
    //     0x9e3f90: mov             x0, x1
    //     0x9e3f94: stur            x1, [fp, #-0x10]
    // 0x9e3f98: CheckStackOverflow
    //     0x9e3f98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e3f9c: cmp             SP, x16
    //     0x9e3fa0: b.ls            #0x9e40e4
    // 0x9e3fa4: LoadField: r1 = r0->field_b
    //     0x9e3fa4: ldur            w1, [x0, #0xb]
    // 0x9e3fa8: DecompressPointer r1
    //     0x9e3fa8: add             x1, x1, HEAP, lsl #32
    // 0x9e3fac: cmp             w1, NULL
    // 0x9e3fb0: b.eq            #0x9e40ec
    // 0x9e3fb4: LoadField: r2 = r1->field_47
    //     0x9e3fb4: ldur            w2, [x1, #0x47]
    // 0x9e3fb8: DecompressPointer r2
    //     0x9e3fb8: add             x2, x2, HEAP, lsl #32
    // 0x9e3fbc: LoadField: r3 = r2->field_63
    //     0x9e3fbc: ldur            w3, [x2, #0x63]
    // 0x9e3fc0: DecompressPointer r3
    //     0x9e3fc0: add             x3, x3, HEAP, lsl #32
    // 0x9e3fc4: mov             x1, x0
    // 0x9e3fc8: stur            x3, [fp, #-8]
    // 0x9e3fcc: r0 = _usesRouter()
    //     0x9e3fcc: bl              #0x9e43ec  ; [package:flutter/src/material/app.dart] _MaterialAppState::_usesRouter
    // 0x9e3fd0: r1 = <State<StatefulWidget>>
    //     0x9e3fd0: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0x9e3fd4: r0 = GlobalObjectKey()
    //     0x9e3fd4: bl              #0x9e43e0  ; AllocateGlobalObjectKeyStub -> GlobalObjectKey<X0 bound State> (size=0x10)
    // 0x9e3fd8: mov             x2, x0
    // 0x9e3fdc: ldur            x0, [fp, #-0x10]
    // 0x9e3fe0: stur            x2, [fp, #-0x48]
    // 0x9e3fe4: StoreField: r2->field_b = r0
    //     0x9e3fe4: stur            w0, [x2, #0xb]
    // 0x9e3fe8: LoadField: r1 = r0->field_b
    //     0x9e3fe8: ldur            w1, [x0, #0xb]
    // 0x9e3fec: DecompressPointer r1
    //     0x9e3fec: add             x1, x1, HEAP, lsl #32
    // 0x9e3ff0: cmp             w1, NULL
    // 0x9e3ff4: b.eq            #0x9e40f0
    // 0x9e3ff8: LoadField: r3 = r1->field_b
    //     0x9e3ff8: ldur            w3, [x1, #0xb]
    // 0x9e3ffc: DecompressPointer r3
    //     0x9e3ffc: add             x3, x3, HEAP, lsl #32
    // 0x9e4000: stur            x3, [fp, #-0x40]
    // 0x9e4004: LoadField: r4 = r1->field_2f
    //     0x9e4004: ldur            w4, [x1, #0x2f]
    // 0x9e4008: DecompressPointer r4
    //     0x9e4008: add             x4, x4, HEAP, lsl #32
    // 0x9e400c: stur            x4, [fp, #-0x38]
    // 0x9e4010: LoadField: r5 = r1->field_1b
    //     0x9e4010: ldur            w5, [x1, #0x1b]
    // 0x9e4014: DecompressPointer r5
    //     0x9e4014: add             x5, x5, HEAP, lsl #32
    // 0x9e4018: stur            x5, [fp, #-0x30]
    // 0x9e401c: LoadField: r6 = r1->field_1f
    //     0x9e401c: ldur            w6, [x1, #0x1f]
    // 0x9e4020: DecompressPointer r6
    //     0x9e4020: add             x6, x6, HEAP, lsl #32
    // 0x9e4024: stur            x6, [fp, #-0x28]
    // 0x9e4028: LoadField: r7 = r1->field_23
    //     0x9e4028: ldur            w7, [x1, #0x23]
    // 0x9e402c: DecompressPointer r7
    //     0x9e402c: add             x7, x7, HEAP, lsl #32
    // 0x9e4030: stur            x7, [fp, #-0x20]
    // 0x9e4034: LoadField: r8 = r1->field_67
    //     0x9e4034: ldur            w8, [x1, #0x67]
    // 0x9e4038: DecompressPointer r8
    //     0x9e4038: add             x8, x8, HEAP, lsl #32
    // 0x9e403c: mov             x1, x0
    // 0x9e4040: stur            x8, [fp, #-0x18]
    // 0x9e4044: r0 = _localizationsDelegates()
    //     0x9e4044: bl              #0x9e42dc  ; [package:flutter/src/material/app.dart] _MaterialAppState::_localizationsDelegates
    // 0x9e4048: mov             x3, x0
    // 0x9e404c: ldur            x0, [fp, #-0x10]
    // 0x9e4050: stur            x3, [fp, #-0x50]
    // 0x9e4054: LoadField: r1 = r0->field_b
    //     0x9e4054: ldur            w1, [x0, #0xb]
    // 0x9e4058: DecompressPointer r1
    //     0x9e4058: add             x1, x1, HEAP, lsl #32
    // 0x9e405c: cmp             w1, NULL
    // 0x9e4060: b.eq            #0x9e40f4
    // 0x9e4064: r1 = Function '<anonymous closure>':.
    //     0x9e4064: add             x1, PP, #0x44, lsl #12  ; [pp+0x44890] AnonymousClosure: (0x9e49ec), in [package:flutter/src/material/app.dart] _MaterialAppState::_buildWidgetApp (0x9e3f84)
    //     0x9e4068: ldr             x1, [x1, #0x890]
    // 0x9e406c: r2 = Null
    //     0x9e406c: mov             x2, NULL
    // 0x9e4070: r0 = AllocateClosureGeneric()
    //     0x9e4070: bl              #0xec1550  ; AllocateClosureGenericStub
    // 0x9e4074: ldur            x2, [fp, #-0x10]
    // 0x9e4078: r1 = Function '_materialBuilder@499125171':.
    //     0x9e4078: add             x1, PP, #0x44, lsl #12  ; [pp+0x44898] AnonymousClosure: (0x9e4410), in [package:flutter/src/material/app.dart] _MaterialAppState::_materialBuilder (0x9e4450)
    //     0x9e407c: ldr             x1, [x1, #0x898]
    // 0x9e4080: stur            x0, [fp, #-0x10]
    // 0x9e4084: r0 = AllocateClosure()
    //     0x9e4084: bl              #0xec1630  ; AllocateClosureStub
    // 0x9e4088: stur            x0, [fp, #-0x58]
    // 0x9e408c: r0 = WidgetsApp()
    //     0x9e408c: bl              #0x9e42d0  ; AllocateWidgetsAppStub -> WidgetsApp (size=0x78)
    // 0x9e4090: stur            x0, [fp, #-0x60]
    // 0x9e4094: ldur            x16, [fp, #-0x50]
    // 0x9e4098: ldur            lr, [fp, #-0x40]
    // 0x9e409c: stp             lr, x16, [SP, #0x20]
    // 0x9e40a0: ldur            x16, [fp, #-0x38]
    // 0x9e40a4: ldur            lr, [fp, #-0x20]
    // 0x9e40a8: stp             lr, x16, [SP, #0x10]
    // 0x9e40ac: ldur            x16, [fp, #-0x28]
    // 0x9e40b0: ldur            lr, [fp, #-0x10]
    // 0x9e40b4: stp             lr, x16, [SP]
    // 0x9e40b8: mov             x1, x0
    // 0x9e40bc: ldur            x2, [fp, #-0x58]
    // 0x9e40c0: ldur            x3, [fp, #-8]
    // 0x9e40c4: ldur            x5, [fp, #-0x30]
    // 0x9e40c8: ldur            x6, [fp, #-0x48]
    // 0x9e40cc: ldur            x7, [fp, #-0x18]
    // 0x9e40d0: r0 = WidgetsApp()
    //     0x9e40d0: bl              #0x9e40f8  ; [package:flutter/src/widgets/app.dart] WidgetsApp::WidgetsApp
    // 0x9e40d4: ldur            x0, [fp, #-0x60]
    // 0x9e40d8: LeaveFrame
    //     0x9e40d8: mov             SP, fp
    //     0x9e40dc: ldp             fp, lr, [SP], #0x10
    // 0x9e40e0: ret
    //     0x9e40e0: ret             
    // 0x9e40e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e40e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e40e8: b               #0x9e3fa4
    // 0x9e40ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e40ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e40f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e40f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e40f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e40f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _localizationsDelegates(/* No info */) {
    // ** addr: 0x9e42dc, size: 0x104
    // 0x9e42dc: EnterFrame
    //     0x9e42dc: stp             fp, lr, [SP, #-0x10]!
    //     0x9e42e0: mov             fp, SP
    // 0x9e42e4: AllocStack(0x20)
    //     0x9e42e4: sub             SP, SP, #0x20
    // 0x9e42e8: SetupParameters(_MaterialAppState this /* r1 => r0, fp-0x8 */)
    //     0x9e42e8: mov             x0, x1
    //     0x9e42ec: stur            x1, [fp, #-8]
    // 0x9e42f0: CheckStackOverflow
    //     0x9e42f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e42f4: cmp             SP, x16
    //     0x9e42f8: b.ls            #0x9e43d4
    // 0x9e42fc: r1 = <LocalizationsDelegate>
    //     0x9e42fc: add             x1, PP, #0x44, lsl #12  ; [pp+0x448c8] TypeArguments: <LocalizationsDelegate>
    //     0x9e4300: ldr             x1, [x1, #0x8c8]
    // 0x9e4304: r2 = 0
    //     0x9e4304: movz            x2, #0
    // 0x9e4308: r0 = _GrowableList()
    //     0x9e4308: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x9e430c: mov             x2, x0
    // 0x9e4310: ldur            x0, [fp, #-8]
    // 0x9e4314: stur            x2, [fp, #-0x18]
    // 0x9e4318: LoadField: r1 = r0->field_b
    //     0x9e4318: ldur            w1, [x0, #0xb]
    // 0x9e431c: DecompressPointer r1
    //     0x9e431c: add             x1, x1, HEAP, lsl #32
    // 0x9e4320: cmp             w1, NULL
    // 0x9e4324: b.eq            #0x9e43dc
    // 0x9e4328: LoadField: r0 = r2->field_b
    //     0x9e4328: ldur            w0, [x2, #0xb]
    // 0x9e432c: LoadField: r1 = r2->field_f
    //     0x9e432c: ldur            w1, [x2, #0xf]
    // 0x9e4330: DecompressPointer r1
    //     0x9e4330: add             x1, x1, HEAP, lsl #32
    // 0x9e4334: LoadField: r3 = r1->field_b
    //     0x9e4334: ldur            w3, [x1, #0xb]
    // 0x9e4338: r4 = LoadInt32Instr(r0)
    //     0x9e4338: sbfx            x4, x0, #1, #0x1f
    // 0x9e433c: stur            x4, [fp, #-0x10]
    // 0x9e4340: r0 = LoadInt32Instr(r3)
    //     0x9e4340: sbfx            x0, x3, #1, #0x1f
    // 0x9e4344: cmp             x4, x0
    // 0x9e4348: b.ne            #0x9e4354
    // 0x9e434c: mov             x1, x2
    // 0x9e4350: r0 = _growToNextCapacity()
    //     0x9e4350: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9e4354: ldur            x0, [fp, #-0x18]
    // 0x9e4358: ldur            x1, [fp, #-0x10]
    // 0x9e435c: add             x2, x1, #1
    // 0x9e4360: stur            x2, [fp, #-0x20]
    // 0x9e4364: lsl             x3, x2, #1
    // 0x9e4368: StoreField: r0->field_b = r3
    //     0x9e4368: stur            w3, [x0, #0xb]
    // 0x9e436c: LoadField: r3 = r0->field_f
    //     0x9e436c: ldur            w3, [x0, #0xf]
    // 0x9e4370: DecompressPointer r3
    //     0x9e4370: add             x3, x3, HEAP, lsl #32
    // 0x9e4374: add             x4, x3, x1, lsl #2
    // 0x9e4378: r16 = Instance__MaterialLocalizationsDelegate
    //     0x9e4378: add             x16, PP, #0x44, lsl #12  ; [pp+0x448d0] Obj!_MaterialLocalizationsDelegate@e14ca1
    //     0x9e437c: ldr             x16, [x16, #0x8d0]
    // 0x9e4380: StoreField: r4->field_f = r16
    //     0x9e4380: stur            w16, [x4, #0xf]
    // 0x9e4384: LoadField: r1 = r3->field_b
    //     0x9e4384: ldur            w1, [x3, #0xb]
    // 0x9e4388: r3 = LoadInt32Instr(r1)
    //     0x9e4388: sbfx            x3, x1, #1, #0x1f
    // 0x9e438c: cmp             x2, x3
    // 0x9e4390: b.ne            #0x9e439c
    // 0x9e4394: mov             x1, x0
    // 0x9e4398: r0 = _growToNextCapacity()
    //     0x9e4398: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9e439c: ldur            x0, [fp, #-0x18]
    // 0x9e43a0: ldur            x1, [fp, #-0x20]
    // 0x9e43a4: add             x2, x1, #1
    // 0x9e43a8: lsl             x3, x2, #1
    // 0x9e43ac: StoreField: r0->field_b = r3
    //     0x9e43ac: stur            w3, [x0, #0xb]
    // 0x9e43b0: LoadField: r2 = r0->field_f
    //     0x9e43b0: ldur            w2, [x0, #0xf]
    // 0x9e43b4: DecompressPointer r2
    //     0x9e43b4: add             x2, x2, HEAP, lsl #32
    // 0x9e43b8: add             x3, x2, x1, lsl #2
    // 0x9e43bc: r16 = Instance__CupertinoLocalizationsDelegate
    //     0x9e43bc: add             x16, PP, #0x44, lsl #12  ; [pp+0x448d8] Obj!_CupertinoLocalizationsDelegate@e14cb1
    //     0x9e43c0: ldr             x16, [x16, #0x8d8]
    // 0x9e43c4: StoreField: r3->field_f = r16
    //     0x9e43c4: stur            w16, [x3, #0xf]
    // 0x9e43c8: LeaveFrame
    //     0x9e43c8: mov             SP, fp
    //     0x9e43cc: ldp             fp, lr, [SP], #0x10
    // 0x9e43d0: ret
    //     0x9e43d0: ret             
    // 0x9e43d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e43d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e43d8: b               #0x9e42fc
    // 0x9e43dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e43dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _usesRouter(/* No info */) {
    // ** addr: 0x9e43ec, size: 0x24
    // 0x9e43ec: LoadField: r2 = r1->field_b
    //     0x9e43ec: ldur            w2, [x1, #0xb]
    // 0x9e43f0: DecompressPointer r2
    //     0x9e43f0: add             x2, x2, HEAP, lsl #32
    // 0x9e43f4: cmp             w2, NULL
    // 0x9e43f8: b.eq            #0x9e4404
    // 0x9e43fc: r0 = false
    //     0x9e43fc: add             x0, NULL, #0x30  ; false
    // 0x9e4400: ret
    //     0x9e4400: ret             
    // 0x9e4404: EnterFrame
    //     0x9e4404: stp             fp, lr, [SP, #-0x10]!
    //     0x9e4408: mov             fp, SP
    // 0x9e440c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e440c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget _materialBuilder(dynamic, BuildContext, Widget?) {
    // ** addr: 0x9e4410, size: 0x40
    // 0x9e4410: EnterFrame
    //     0x9e4410: stp             fp, lr, [SP, #-0x10]!
    //     0x9e4414: mov             fp, SP
    // 0x9e4418: ldr             x0, [fp, #0x20]
    // 0x9e441c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9e441c: ldur            w1, [x0, #0x17]
    // 0x9e4420: DecompressPointer r1
    //     0x9e4420: add             x1, x1, HEAP, lsl #32
    // 0x9e4424: CheckStackOverflow
    //     0x9e4424: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e4428: cmp             SP, x16
    //     0x9e442c: b.ls            #0x9e4448
    // 0x9e4430: ldr             x2, [fp, #0x18]
    // 0x9e4434: ldr             x3, [fp, #0x10]
    // 0x9e4438: r0 = _materialBuilder()
    //     0x9e4438: bl              #0x9e4450  ; [package:flutter/src/material/app.dart] _MaterialAppState::_materialBuilder
    // 0x9e443c: LeaveFrame
    //     0x9e443c: mov             SP, fp
    //     0x9e4440: ldp             fp, lr, [SP], #0x10
    // 0x9e4444: ret
    //     0x9e4444: ret             
    // 0x9e4448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e4448: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e444c: b               #0x9e4430
  }
  _ _materialBuilder(/* No info */) {
    // ** addr: 0x9e4450, size: 0x198
    // 0x9e4450: EnterFrame
    //     0x9e4450: stp             fp, lr, [SP, #-0x10]!
    //     0x9e4454: mov             fp, SP
    // 0x9e4458: AllocStack(0x38)
    //     0x9e4458: sub             SP, SP, #0x38
    // 0x9e445c: SetupParameters(_MaterialAppState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x9e445c: stur            x1, [fp, #-8]
    //     0x9e4460: stur            x2, [fp, #-0x10]
    //     0x9e4464: stur            x3, [fp, #-0x18]
    // 0x9e4468: CheckStackOverflow
    //     0x9e4468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e446c: cmp             SP, x16
    //     0x9e4470: b.ls            #0x9e45d8
    // 0x9e4474: r1 = 2
    //     0x9e4474: movz            x1, #0x2
    // 0x9e4478: r0 = AllocateContext()
    //     0x9e4478: bl              #0xec126c  ; AllocateContextStub
    // 0x9e447c: mov             x3, x0
    // 0x9e4480: ldur            x0, [fp, #-8]
    // 0x9e4484: stur            x3, [fp, #-0x20]
    // 0x9e4488: StoreField: r3->field_f = r0
    //     0x9e4488: stur            w0, [x3, #0xf]
    // 0x9e448c: ldur            x1, [fp, #-0x18]
    // 0x9e4490: StoreField: r3->field_13 = r1
    //     0x9e4490: stur            w1, [x3, #0x13]
    // 0x9e4494: mov             x1, x0
    // 0x9e4498: ldur            x2, [fp, #-0x10]
    // 0x9e449c: r0 = _themeBuilder()
    //     0x9e449c: bl              #0x9e4630  ; [package:flutter/src/material/app.dart] _MaterialAppState::_themeBuilder
    // 0x9e44a0: mov             x2, x0
    // 0x9e44a4: stur            x2, [fp, #-0x18]
    // 0x9e44a8: LoadField: r0 = r2->field_3f
    //     0x9e44a8: ldur            w0, [x2, #0x3f]
    // 0x9e44ac: DecompressPointer r0
    //     0x9e44ac: add             x0, x0, HEAP, lsl #32
    // 0x9e44b0: LoadField: r3 = r0->field_b
    //     0x9e44b0: ldur            w3, [x0, #0xb]
    // 0x9e44b4: DecompressPointer r3
    //     0x9e44b4: add             x3, x3, HEAP, lsl #32
    // 0x9e44b8: stur            x3, [fp, #-0x10]
    // 0x9e44bc: r0 = LoadClassIdInstr(r3)
    //     0x9e44bc: ldur            x0, [x3, #-1]
    //     0x9e44c0: ubfx            x0, x0, #0xc, #0x14
    // 0x9e44c4: mov             x1, x3
    // 0x9e44c8: d0 = 0.400000
    //     0x9e44c8: ldr             d0, [PP, #0x64d8]  ; [pp+0x64d8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x9e44cc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x9e44cc: sub             lr, x0, #1, lsl #12
    //     0x9e44d0: ldr             lr, [x21, lr, lsl #3]
    //     0x9e44d4: blr             lr
    // 0x9e44d8: mov             x1, x0
    // 0x9e44dc: ldur            x0, [fp, #-8]
    // 0x9e44e0: stur            x1, [fp, #-0x28]
    // 0x9e44e4: LoadField: r2 = r0->field_b
    //     0x9e44e4: ldur            w2, [x0, #0xb]
    // 0x9e44e8: DecompressPointer r2
    //     0x9e44e8: add             x2, x2, HEAP, lsl #32
    // 0x9e44ec: cmp             w2, NULL
    // 0x9e44f0: b.eq            #0x9e45e0
    // 0x9e44f4: r0 = InitLateStaticField(0x99c) // [package:flutter/src/animation/animation_style.dart] AnimationStyle::noAnimation
    //     0x9e44f4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9e44f8: ldr             x0, [x0, #0x1338]
    //     0x9e44fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9e4500: cmp             w0, w16
    //     0x9e4504: b.ne            #0x9e4514
    //     0x9e4508: add             x2, PP, #0x44, lsl #12  ; [pp+0x448a0] Field <AnimationStyle.noAnimation>: static late (offset: 0x99c)
    //     0x9e450c: ldr             x2, [x2, #0x8a0]
    //     0x9e4510: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x9e4514: ldur            x0, [fp, #-8]
    // 0x9e4518: LoadField: r3 = r0->field_b
    //     0x9e4518: ldur            w3, [x0, #0xb]
    // 0x9e451c: DecompressPointer r3
    //     0x9e451c: add             x3, x3, HEAP, lsl #32
    // 0x9e4520: stur            x3, [fp, #-0x30]
    // 0x9e4524: cmp             w3, NULL
    // 0x9e4528: b.eq            #0x9e45e4
    // 0x9e452c: ldur            x2, [fp, #-0x20]
    // 0x9e4530: r1 = Function '<anonymous closure>':.
    //     0x9e4530: add             x1, PP, #0x44, lsl #12  ; [pp+0x448a8] AnonymousClosure: (0x9e47cc), in [package:flutter/src/material/app.dart] _MaterialAppState::_materialBuilder (0x9e4450)
    //     0x9e4534: ldr             x1, [x1, #0x8a8]
    // 0x9e4538: r0 = AllocateClosure()
    //     0x9e4538: bl              #0xec1630  ; AllocateClosureStub
    // 0x9e453c: stur            x0, [fp, #-8]
    // 0x9e4540: r0 = Builder()
    //     0x9e4540: bl              #0x6a5c84  ; AllocateBuilderStub -> Builder (size=0x10)
    // 0x9e4544: mov             x1, x0
    // 0x9e4548: ldur            x0, [fp, #-8]
    // 0x9e454c: stur            x1, [fp, #-0x20]
    // 0x9e4550: StoreField: r1->field_b = r0
    //     0x9e4550: stur            w0, [x1, #0xb]
    // 0x9e4554: r0 = AnimatedTheme()
    //     0x9e4554: bl              #0x9e4600  ; AllocateAnimatedThemeStub -> AnimatedTheme (size=0x20)
    // 0x9e4558: mov             x1, x0
    // 0x9e455c: ldur            x0, [fp, #-0x18]
    // 0x9e4560: stur            x1, [fp, #-0x38]
    // 0x9e4564: ArrayStore: r1[0] = r0  ; List_4
    //     0x9e4564: stur            w0, [x1, #0x17]
    // 0x9e4568: ldur            x0, [fp, #-0x20]
    // 0x9e456c: StoreField: r1->field_1b = r0
    //     0x9e456c: stur            w0, [x1, #0x1b]
    // 0x9e4570: r0 = Instance__Linear
    //     0x9e4570: ldr             x0, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0x9e4574: StoreField: r1->field_b = r0
    //     0x9e4574: stur            w0, [x1, #0xb]
    // 0x9e4578: r0 = Instance_Duration
    //     0x9e4578: add             x0, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0x9e457c: ldr             x0, [x0, #0x368]
    // 0x9e4580: StoreField: r1->field_f = r0
    //     0x9e4580: stur            w0, [x1, #0xf]
    // 0x9e4584: ldur            x0, [fp, #-0x30]
    // 0x9e4588: LoadField: r2 = r0->field_f
    //     0x9e4588: ldur            w2, [x0, #0xf]
    // 0x9e458c: DecompressPointer r2
    //     0x9e458c: add             x2, x2, HEAP, lsl #32
    // 0x9e4590: stur            x2, [fp, #-8]
    // 0x9e4594: r0 = DefaultSelectionStyle()
    //     0x9e4594: bl              #0x9e45f4  ; AllocateDefaultSelectionStyleStub -> DefaultSelectionStyle (size=0x1c)
    // 0x9e4598: mov             x1, x0
    // 0x9e459c: ldur            x0, [fp, #-0x10]
    // 0x9e45a0: stur            x1, [fp, #-0x18]
    // 0x9e45a4: StoreField: r1->field_f = r0
    //     0x9e45a4: stur            w0, [x1, #0xf]
    // 0x9e45a8: ldur            x0, [fp, #-0x28]
    // 0x9e45ac: StoreField: r1->field_13 = r0
    //     0x9e45ac: stur            w0, [x1, #0x13]
    // 0x9e45b0: ldur            x0, [fp, #-0x38]
    // 0x9e45b4: StoreField: r1->field_b = r0
    //     0x9e45b4: stur            w0, [x1, #0xb]
    // 0x9e45b8: r0 = ScaffoldMessenger()
    //     0x9e45b8: bl              #0x9e45e8  ; AllocateScaffoldMessengerStub -> ScaffoldMessenger (size=0x10)
    // 0x9e45bc: ldur            x1, [fp, #-0x18]
    // 0x9e45c0: StoreField: r0->field_b = r1
    //     0x9e45c0: stur            w1, [x0, #0xb]
    // 0x9e45c4: ldur            x1, [fp, #-8]
    // 0x9e45c8: StoreField: r0->field_7 = r1
    //     0x9e45c8: stur            w1, [x0, #7]
    // 0x9e45cc: LeaveFrame
    //     0x9e45cc: mov             SP, fp
    //     0x9e45d0: ldp             fp, lr, [SP], #0x10
    // 0x9e45d4: ret
    //     0x9e45d4: ret             
    // 0x9e45d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e45d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e45dc: b               #0x9e4474
    // 0x9e45e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e45e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e45e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e45e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _themeBuilder(/* No info */) {
    // ** addr: 0x9e4630, size: 0x164
    // 0x9e4630: EnterFrame
    //     0x9e4630: stp             fp, lr, [SP, #-0x10]!
    //     0x9e4634: mov             fp, SP
    // 0x9e4638: AllocStack(0x18)
    //     0x9e4638: sub             SP, SP, #0x18
    // 0x9e463c: SetupParameters(_MaterialAppState this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x9e463c: mov             x0, x2
    //     0x9e4640: stur            x2, [fp, #-0x18]
    //     0x9e4644: mov             x2, x1
    //     0x9e4648: stur            x1, [fp, #-0x10]
    // 0x9e464c: CheckStackOverflow
    //     0x9e464c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e4650: cmp             SP, x16
    //     0x9e4654: b.ls            #0x9e4778
    // 0x9e4658: LoadField: r1 = r2->field_b
    //     0x9e4658: ldur            w1, [x2, #0xb]
    // 0x9e465c: DecompressPointer r1
    //     0x9e465c: add             x1, x1, HEAP, lsl #32
    // 0x9e4660: cmp             w1, NULL
    // 0x9e4664: b.eq            #0x9e4780
    // 0x9e4668: LoadField: r3 = r1->field_57
    //     0x9e4668: ldur            w3, [x1, #0x57]
    // 0x9e466c: DecompressPointer r3
    //     0x9e466c: add             x3, x3, HEAP, lsl #32
    // 0x9e4670: mov             x1, x0
    // 0x9e4674: stur            x3, [fp, #-8]
    // 0x9e4678: r0 = platformBrightnessOf()
    //     0x9e4678: bl              #0x9e308c  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::platformBrightnessOf
    // 0x9e467c: mov             x1, x0
    // 0x9e4680: ldur            x0, [fp, #-8]
    // 0x9e4684: r16 = Instance_ThemeMode
    //     0x9e4684: add             x16, PP, #0x23, lsl #12  ; [pp+0x23bc0] Obj!ThemeMode@e36c21
    //     0x9e4688: ldr             x16, [x16, #0xbc0]
    // 0x9e468c: cmp             w0, w16
    // 0x9e4690: b.ne            #0x9e469c
    // 0x9e4694: r0 = true
    //     0x9e4694: add             x0, NULL, #0x20  ; true
    // 0x9e4698: b               #0x9e46c8
    // 0x9e469c: r16 = Instance_ThemeMode
    //     0x9e469c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23bd0] Obj!ThemeMode@e36c01
    //     0x9e46a0: ldr             x16, [x16, #0xbd0]
    // 0x9e46a4: cmp             w0, w16
    // 0x9e46a8: b.ne            #0x9e46c4
    // 0x9e46ac: r16 = Instance_Brightness
    //     0x9e46ac: ldr             x16, [PP, #0x5428]  ; [pp+0x5428] Obj!Brightness@e39141
    // 0x9e46b0: cmp             w1, w16
    // 0x9e46b4: r16 = true
    //     0x9e46b4: add             x16, NULL, #0x20  ; true
    // 0x9e46b8: r17 = false
    //     0x9e46b8: add             x17, NULL, #0x30  ; false
    // 0x9e46bc: csel            x0, x16, x17, eq
    // 0x9e46c0: b               #0x9e46c8
    // 0x9e46c4: r0 = false
    //     0x9e46c4: add             x0, NULL, #0x30  ; false
    // 0x9e46c8: ldur            x1, [fp, #-0x18]
    // 0x9e46cc: stur            x0, [fp, #-8]
    // 0x9e46d0: r0 = highContrastOf()
    //     0x9e46d0: bl              #0x9e4794  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::highContrastOf
    // 0x9e46d4: ldur            x1, [fp, #-8]
    // 0x9e46d8: tbnz            w1, #4, #0x9e4700
    // 0x9e46dc: tbnz            w0, #4, #0x9e46f8
    // 0x9e46e0: ldur            x2, [fp, #-0x10]
    // 0x9e46e4: LoadField: r3 = r2->field_b
    //     0x9e46e4: ldur            w3, [x2, #0xb]
    // 0x9e46e8: DecompressPointer r3
    //     0x9e46e8: add             x3, x3, HEAP, lsl #32
    // 0x9e46ec: cmp             w3, NULL
    // 0x9e46f0: b.eq            #0x9e4784
    // 0x9e46f4: b               #0x9e4704
    // 0x9e46f8: ldur            x2, [fp, #-0x10]
    // 0x9e46fc: b               #0x9e4704
    // 0x9e4700: ldur            x2, [fp, #-0x10]
    // 0x9e4704: tbnz            w1, #4, #0x9e4728
    // 0x9e4708: LoadField: r1 = r2->field_b
    //     0x9e4708: ldur            w1, [x2, #0xb]
    // 0x9e470c: DecompressPointer r1
    //     0x9e470c: add             x1, x1, HEAP, lsl #32
    // 0x9e4710: cmp             w1, NULL
    // 0x9e4714: b.eq            #0x9e4788
    // 0x9e4718: LoadField: r3 = r1->field_4b
    //     0x9e4718: ldur            w3, [x1, #0x4b]
    // 0x9e471c: DecompressPointer r3
    //     0x9e471c: add             x3, x3, HEAP, lsl #32
    // 0x9e4720: mov             x1, x3
    // 0x9e4724: b               #0x9e4740
    // 0x9e4728: tbnz            w0, #4, #0x9e473c
    // 0x9e472c: LoadField: r1 = r2->field_b
    //     0x9e472c: ldur            w1, [x2, #0xb]
    // 0x9e4730: DecompressPointer r1
    //     0x9e4730: add             x1, x1, HEAP, lsl #32
    // 0x9e4734: cmp             w1, NULL
    // 0x9e4738: b.eq            #0x9e478c
    // 0x9e473c: r1 = Null
    //     0x9e473c: mov             x1, NULL
    // 0x9e4740: cmp             w1, NULL
    // 0x9e4744: b.ne            #0x9e4768
    // 0x9e4748: LoadField: r3 = r2->field_b
    //     0x9e4748: ldur            w3, [x2, #0xb]
    // 0x9e474c: DecompressPointer r3
    //     0x9e474c: add             x3, x3, HEAP, lsl #32
    // 0x9e4750: cmp             w3, NULL
    // 0x9e4754: b.eq            #0x9e4790
    // 0x9e4758: LoadField: r2 = r3->field_47
    //     0x9e4758: ldur            w2, [x3, #0x47]
    // 0x9e475c: DecompressPointer r2
    //     0x9e475c: add             x2, x2, HEAP, lsl #32
    // 0x9e4760: mov             x0, x2
    // 0x9e4764: b               #0x9e476c
    // 0x9e4768: mov             x0, x1
    // 0x9e476c: LeaveFrame
    //     0x9e476c: mov             SP, fp
    //     0x9e4770: ldp             fp, lr, [SP], #0x10
    // 0x9e4774: ret
    //     0x9e4774: ret             
    // 0x9e4778: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e4778: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e477c: b               #0x9e4658
    // 0x9e4780: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e4780: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e4784: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e4784: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e4788: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e4788: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e478c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e478c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e4790: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e4790: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x9e47cc, size: 0x7c
    // 0x9e47cc: EnterFrame
    //     0x9e47cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9e47d0: mov             fp, SP
    // 0x9e47d4: ldr             x0, [fp, #0x18]
    // 0x9e47d8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9e47d8: ldur            w1, [x0, #0x17]
    // 0x9e47dc: DecompressPointer r1
    //     0x9e47dc: add             x1, x1, HEAP, lsl #32
    // 0x9e47e0: CheckStackOverflow
    //     0x9e47e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e47e4: cmp             SP, x16
    //     0x9e47e8: b.ls            #0x9e4838
    // 0x9e47ec: LoadField: r0 = r1->field_f
    //     0x9e47ec: ldur            w0, [x1, #0xf]
    // 0x9e47f0: DecompressPointer r0
    //     0x9e47f0: add             x0, x0, HEAP, lsl #32
    // 0x9e47f4: LoadField: r2 = r0->field_b
    //     0x9e47f4: ldur            w2, [x0, #0xb]
    // 0x9e47f8: DecompressPointer r2
    //     0x9e47f8: add             x2, x2, HEAP, lsl #32
    // 0x9e47fc: cmp             w2, NULL
    // 0x9e4800: b.eq            #0x9e4840
    // 0x9e4804: LoadField: r0 = r2->field_3b
    //     0x9e4804: ldur            w0, [x2, #0x3b]
    // 0x9e4808: DecompressPointer r0
    //     0x9e4808: add             x0, x0, HEAP, lsl #32
    // 0x9e480c: LoadField: r3 = r1->field_13
    //     0x9e480c: ldur            w3, [x1, #0x13]
    // 0x9e4810: DecompressPointer r3
    //     0x9e4810: add             x3, x3, HEAP, lsl #32
    // 0x9e4814: cmp             w0, NULL
    // 0x9e4818: b.eq            #0x9e4844
    // 0x9e481c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9e481c: ldur            w1, [x0, #0x17]
    // 0x9e4820: DecompressPointer r1
    //     0x9e4820: add             x1, x1, HEAP, lsl #32
    // 0x9e4824: ldr             x2, [fp, #0x10]
    // 0x9e4828: r0 = defaultBuilder()
    //     0x9e4828: bl              #0x9e4888  ; [package:get/get_navigation/src/root/get_material_app.dart] GetMaterialApp::defaultBuilder
    // 0x9e482c: LeaveFrame
    //     0x9e482c: mov             SP, fp
    //     0x9e4830: ldp             fp, lr, [SP], #0x10
    // 0x9e4834: ret
    //     0x9e4834: ret             
    // 0x9e4838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e4838: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e483c: b               #0x9e47ec
    // 0x9e4840: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e4840: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e4844: r0 = NullErrorSharedWithoutFPURegs()
    //     0x9e4844: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] MaterialPageRoute<Y0> <anonymous closure><Y0>(dynamic, RouteSettings, (dynamic, BuildContext) => Widget) {
    // ** addr: 0x9e49ec, size: 0x8c
    // 0x9e49ec: EnterFrame
    //     0x9e49ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9e49f0: mov             fp, SP
    // 0x9e49f4: AllocStack(0x10)
    //     0x9e49f4: sub             SP, SP, #0x10
    // 0x9e49f8: SetupParameters()
    //     0x9e49f8: ldur            w0, [x4, #0xf]
    //     0x9e49fc: cbnz            w0, #0x9e4a08
    //     0x9e4a00: mov             x1, NULL
    //     0x9e4a04: b               #0x9e4a14
    //     0x9e4a08: ldur            w0, [x4, #0x17]
    //     0x9e4a0c: add             x1, fp, w0, sxtw #2
    //     0x9e4a10: ldr             x1, [x1, #0x10]
    //     0x9e4a14: ldr             x0, [fp, #0x20]
    //     0x9e4a18: ldur            w2, [x0, #0xf]
    //     0x9e4a1c: add             x2, x2, HEAP, lsl #32
    //     0x9e4a20: ldr             x16, [THR, #0x98]  ; THR::empty_type_arguments
    //     0x9e4a24: cmp             w2, w16
    //     0x9e4a28: b.eq            #0x9e4a30
    //     0x9e4a2c: mov             x1, x2
    // 0x9e4a30: CheckStackOverflow
    //     0x9e4a30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e4a34: cmp             SP, x16
    //     0x9e4a38: b.ls            #0x9e4a70
    // 0x9e4a3c: r0 = MaterialPageRoute()
    //     0x9e4a3c: bl              #0x9e4b78  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xa4)
    // 0x9e4a40: stur            x0, [fp, #-8]
    // 0x9e4a44: ldr             x16, [fp, #0x18]
    // 0x9e4a48: str             x16, [SP]
    // 0x9e4a4c: mov             x1, x0
    // 0x9e4a50: ldr             x2, [fp, #0x10]
    // 0x9e4a54: r4 = const [0, 0x3, 0x1, 0x2, settings, 0x2, null]
    //     0x9e4a54: add             x4, PP, #0x44, lsl #12  ; [pp+0x448b0] List(7) [0, 0x3, 0x1, 0x2, "settings", 0x2, Null]
    //     0x9e4a58: ldr             x4, [x4, #0x8b0]
    // 0x9e4a5c: r0 = MaterialPageRoute()
    //     0x9e4a5c: bl              #0x9e4a78  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0x9e4a60: ldur            x0, [fp, #-8]
    // 0x9e4a64: LeaveFrame
    //     0x9e4a64: mov             SP, fp
    //     0x9e4a68: ldp             fp, lr, [SP], #0x10
    // 0x9e4a6c: ret
    //     0x9e4a6c: ret             
    // 0x9e4a70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e4a70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e4a74: b               #0x9e4a3c
  }
  [closure] KeyEventResult <anonymous closure>(dynamic, FocusNode, KeyEvent) {
    // ** addr: 0x9e4b84, size: 0xc0
    // 0x9e4b84: EnterFrame
    //     0x9e4b84: stp             fp, lr, [SP, #-0x10]!
    //     0x9e4b88: mov             fp, SP
    // 0x9e4b8c: AllocStack(0x18)
    //     0x9e4b8c: sub             SP, SP, #0x18
    // 0x9e4b90: CheckStackOverflow
    //     0x9e4b90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e4b94: cmp             SP, x16
    //     0x9e4b98: b.ls            #0x9e4c3c
    // 0x9e4b9c: ldr             x0, [fp, #0x10]
    // 0x9e4ba0: r1 = LoadClassIdInstr(r0)
    //     0x9e4ba0: ldur            x1, [x0, #-1]
    //     0x9e4ba4: ubfx            x1, x1, #0xc, #0x14
    // 0x9e4ba8: cmp             x1, #0xf10
    // 0x9e4bac: b.eq            #0x9e4bb8
    // 0x9e4bb0: cmp             x1, #0xf0e
    // 0x9e4bb4: b.ne            #0x9e4c0c
    // 0x9e4bb8: LoadField: r1 = r0->field_b
    //     0x9e4bb8: ldur            w1, [x0, #0xb]
    // 0x9e4bbc: DecompressPointer r1
    //     0x9e4bbc: add             x1, x1, HEAP, lsl #32
    // 0x9e4bc0: stur            x1, [fp, #-8]
    // 0x9e4bc4: r16 = Instance_LogicalKeyboardKey
    //     0x9e4bc4: add             x16, PP, #0x44, lsl #12  ; [pp+0x44878] Obj!LogicalKeyboardKey@e18781
    //     0x9e4bc8: ldr             x16, [x16, #0x878]
    // 0x9e4bcc: cmp             w1, w16
    // 0x9e4bd0: b.eq            #0x9e4c1c
    // 0x9e4bd4: r16 = LogicalKeyboardKey
    //     0x9e4bd4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d920] Type: LogicalKeyboardKey
    //     0x9e4bd8: ldr             x16, [x16, #0x920]
    // 0x9e4bdc: r30 = LogicalKeyboardKey
    //     0x9e4bdc: add             lr, PP, #0x1d, lsl #12  ; [pp+0x1d920] Type: LogicalKeyboardKey
    //     0x9e4be0: ldr             lr, [lr, #0x920]
    // 0x9e4be4: stp             lr, x16, [SP]
    // 0x9e4be8: r0 = ==()
    //     0x9e4be8: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0x9e4bec: tbnz            w0, #4, #0x9e4c0c
    // 0x9e4bf0: ldur            x0, [fp, #-8]
    // 0x9e4bf4: r1 = Instance_LogicalKeyboardKey
    //     0x9e4bf4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44878] Obj!LogicalKeyboardKey@e18781
    //     0x9e4bf8: ldr             x1, [x1, #0x878]
    // 0x9e4bfc: LoadField: r2 = r1->field_7
    //     0x9e4bfc: ldur            x2, [x1, #7]
    // 0x9e4c00: LoadField: r1 = r0->field_7
    //     0x9e4c00: ldur            x1, [x0, #7]
    // 0x9e4c04: cmp             x2, x1
    // 0x9e4c08: b.eq            #0x9e4c1c
    // 0x9e4c0c: r0 = Instance_KeyEventResult
    //     0x9e4c0c: ldr             x0, [PP, #0x2238]  ; [pp+0x2238] Obj!KeyEventResult@e345c1
    // 0x9e4c10: LeaveFrame
    //     0x9e4c10: mov             SP, fp
    //     0x9e4c14: ldp             fp, lr, [SP], #0x10
    // 0x9e4c18: ret
    //     0x9e4c18: ret             
    // 0x9e4c1c: r0 = dismissAllToolTips()
    //     0x9e4c1c: bl              #0x9e4c44  ; [package:flutter/src/material/tooltip.dart] Tooltip::dismissAllToolTips
    // 0x9e4c20: tbnz            w0, #4, #0x9e4c2c
    // 0x9e4c24: r0 = Instance_KeyEventResult
    //     0x9e4c24: ldr             x0, [PP, #0x2228]  ; [pp+0x2228] Obj!KeyEventResult@e34601
    // 0x9e4c28: b               #0x9e4c30
    // 0x9e4c2c: r0 = Instance_KeyEventResult
    //     0x9e4c2c: ldr             x0, [PP, #0x2238]  ; [pp+0x2238] Obj!KeyEventResult@e345c1
    // 0x9e4c30: LeaveFrame
    //     0x9e4c30: mov             SP, fp
    //     0x9e4c34: ldp             fp, lr, [SP], #0x10
    // 0x9e4c38: ret
    //     0x9e4c38: ret             
    // 0x9e4c3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e4c3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e4c40: b               #0x9e4b9c
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7be30, size: 0x54
    // 0xa7be30: EnterFrame
    //     0xa7be30: stp             fp, lr, [SP, #-0x10]!
    //     0xa7be34: mov             fp, SP
    // 0xa7be38: CheckStackOverflow
    //     0xa7be38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7be3c: cmp             SP, x16
    //     0xa7be40: b.ls            #0xa7be70
    // 0xa7be44: LoadField: r0 = r1->field_13
    //     0xa7be44: ldur            w0, [x1, #0x13]
    // 0xa7be48: DecompressPointer r0
    //     0xa7be48: add             x0, x0, HEAP, lsl #32
    // 0xa7be4c: r16 = Sentinel
    //     0xa7be4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7be50: cmp             w0, w16
    // 0xa7be54: b.eq            #0xa7be78
    // 0xa7be58: mov             x1, x0
    // 0xa7be5c: r0 = dispose()
    //     0xa7be5c: bl              #0xa7be84  ; [package:flutter/src/widgets/heroes.dart] HeroController::dispose
    // 0xa7be60: r0 = Null
    //     0xa7be60: mov             x0, NULL
    // 0xa7be64: LeaveFrame
    //     0xa7be64: mov             SP, fp
    //     0xa7be68: ldp             fp, lr, [SP], #0x10
    // 0xa7be6c: ret
    //     0xa7be6c: ret             
    // 0xa7be70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7be70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7be74: b               #0xa7be44
    // 0xa7be78: r9 = _heroController
    //     0xa7be78: add             x9, PP, #0x44, lsl #12  ; [pp+0x44870] Field <_MaterialAppState@499125171._heroController@499125171>: late (offset: 0x14)
    //     0xa7be7c: ldr             x9, [x9, #0x870]
    // 0xa7be80: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa7be80: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4880, size: 0x9c, field offset: 0xc
//   const constructor, 
class MaterialApp extends StatefulWidget {

  static _ createMaterialHeroController(/* No info */) {
    // ** addr: 0x9320f8, size: 0x74
    // 0x9320f8: EnterFrame
    //     0x9320f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9320fc: mov             fp, SP
    // 0x932100: AllocStack(0x20)
    //     0x932100: sub             SP, SP, #0x20
    // 0x932104: CheckStackOverflow
    //     0x932104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x932108: cmp             SP, x16
    //     0x93210c: b.ls            #0x932164
    // 0x932110: r16 = <Object, _HeroFlight>
    //     0x932110: add             x16, PP, #0x44, lsl #12  ; [pp+0x44968] TypeArguments: <Object, _HeroFlight>
    //     0x932114: ldr             x16, [x16, #0x968]
    // 0x932118: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x93211c: stp             lr, x16, [SP]
    // 0x932120: r0 = Map._fromLiteral()
    //     0x932120: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x932124: stur            x0, [fp, #-8]
    // 0x932128: r0 = HeroController()
    //     0x932128: bl              #0x93216c  ; AllocateHeroControllerStub -> HeroController (size=0x10)
    // 0x93212c: mov             x3, x0
    // 0x932130: ldur            x0, [fp, #-8]
    // 0x932134: stur            x3, [fp, #-0x10]
    // 0x932138: StoreField: r3->field_b = r0
    //     0x932138: stur            w0, [x3, #0xb]
    // 0x93213c: r1 = Function '<anonymous closure>': static.
    //     0x93213c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44970] AnonymousClosure: static (0x932178), in [package:flutter/src/material/app.dart] MaterialApp::createMaterialHeroController (0x9320f8)
    //     0x932140: ldr             x1, [x1, #0x970]
    // 0x932144: r2 = Null
    //     0x932144: mov             x2, NULL
    // 0x932148: r0 = AllocateClosure()
    //     0x932148: bl              #0xec1630  ; AllocateClosureStub
    // 0x93214c: mov             x1, x0
    // 0x932150: ldur            x0, [fp, #-0x10]
    // 0x932154: StoreField: r0->field_7 = r1
    //     0x932154: stur            w1, [x0, #7]
    // 0x932158: LeaveFrame
    //     0x932158: mov             SP, fp
    //     0x93215c: ldp             fp, lr, [SP], #0x10
    // 0x932160: ret
    //     0x932160: ret             
    // 0x932164: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x932164: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x932168: b               #0x932110
  }
  [closure] static MaterialRectArcTween <anonymous closure>(dynamic, Rect?, Rect?) {
    // ** addr: 0x932178, size: 0x40
    // 0x932178: EnterFrame
    //     0x932178: stp             fp, lr, [SP, #-0x10]!
    //     0x93217c: mov             fp, SP
    // 0x932180: r1 = <Rect?>
    //     0x932180: ldr             x1, [PP, #0x4a88]  ; [pp+0x4a88] TypeArguments: <Rect?>
    // 0x932184: r0 = MaterialRectArcTween()
    //     0x932184: bl              #0x9321b8  ; AllocateMaterialRectArcTweenStub -> MaterialRectArcTween (size=0x20)
    // 0x932188: r1 = true
    //     0x932188: add             x1, NULL, #0x20  ; true
    // 0x93218c: StoreField: r0->field_13 = r1
    //     0x93218c: stur            w1, [x0, #0x13]
    // 0x932190: r1 = Sentinel
    //     0x932190: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x932194: ArrayStore: r0[0] = r1  ; List_4
    //     0x932194: stur            w1, [x0, #0x17]
    // 0x932198: StoreField: r0->field_1b = r1
    //     0x932198: stur            w1, [x0, #0x1b]
    // 0x93219c: ldr             x1, [fp, #0x18]
    // 0x9321a0: StoreField: r0->field_b = r1
    //     0x9321a0: stur            w1, [x0, #0xb]
    // 0x9321a4: ldr             x1, [fp, #0x10]
    // 0x9321a8: StoreField: r0->field_f = r1
    //     0x9321a8: stur            w1, [x0, #0xf]
    // 0x9321ac: LeaveFrame
    //     0x9321ac: mov             SP, fp
    //     0x9321b0: ldp             fp, lr, [SP], #0x10
    // 0x9321b4: ret
    //     0x9321b4: ret             
  }
  _ createState(/* No info */) {
    // ** addr: 0xa8f7e8, size: 0x2c
    // 0xa8f7e8: EnterFrame
    //     0xa8f7e8: stp             fp, lr, [SP, #-0x10]!
    //     0xa8f7ec: mov             fp, SP
    // 0xa8f7f0: mov             x0, x1
    // 0xa8f7f4: r1 = <MaterialApp>
    //     0xa8f7f4: add             x1, PP, #0x39, lsl #12  ; [pp+0x39a40] TypeArguments: <MaterialApp>
    //     0xa8f7f8: ldr             x1, [x1, #0xa40]
    // 0xa8f7fc: r0 = _MaterialAppState()
    //     0xa8f7fc: bl              #0xa8f814  ; Allocate_MaterialAppStateStub -> _MaterialAppState (size=0x18)
    // 0xa8f800: r1 = Sentinel
    //     0xa8f800: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa8f804: StoreField: r0->field_13 = r1
    //     0xa8f804: stur            w1, [x0, #0x13]
    // 0xa8f808: LeaveFrame
    //     0xa8f808: mov             SP, fp
    //     0xa8f80c: ldp             fp, lr, [SP], #0x10
    // 0xa8f810: ret
    //     0xa8f810: ret             
  }
}

// class id: 7080, size: 0x14, field offset: 0x14
enum ThemeMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc48874, size: 0x64
    // 0xc48874: EnterFrame
    //     0xc48874: stp             fp, lr, [SP, #-0x10]!
    //     0xc48878: mov             fp, SP
    // 0xc4887c: AllocStack(0x10)
    //     0xc4887c: sub             SP, SP, #0x10
    // 0xc48880: SetupParameters(ThemeMode this /* r1 => r0, fp-0x8 */)
    //     0xc48880: mov             x0, x1
    //     0xc48884: stur            x1, [fp, #-8]
    // 0xc48888: CheckStackOverflow
    //     0xc48888: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4888c: cmp             SP, x16
    //     0xc48890: b.ls            #0xc488d0
    // 0xc48894: r1 = Null
    //     0xc48894: mov             x1, NULL
    // 0xc48898: r2 = 4
    //     0xc48898: movz            x2, #0x4
    // 0xc4889c: r0 = AllocateArray()
    //     0xc4889c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc488a0: r16 = "ThemeMode."
    //     0xc488a0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30d88] "ThemeMode."
    //     0xc488a4: ldr             x16, [x16, #0xd88]
    // 0xc488a8: StoreField: r0->field_f = r16
    //     0xc488a8: stur            w16, [x0, #0xf]
    // 0xc488ac: ldur            x1, [fp, #-8]
    // 0xc488b0: LoadField: r2 = r1->field_f
    //     0xc488b0: ldur            w2, [x1, #0xf]
    // 0xc488b4: DecompressPointer r2
    //     0xc488b4: add             x2, x2, HEAP, lsl #32
    // 0xc488b8: StoreField: r0->field_13 = r2
    //     0xc488b8: stur            w2, [x0, #0x13]
    // 0xc488bc: str             x0, [SP]
    // 0xc488c0: r0 = _interpolate()
    //     0xc488c0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc488c4: LeaveFrame
    //     0xc488c4: mov             SP, fp
    //     0xc488c8: ldp             fp, lr, [SP], #0x10
    // 0xc488cc: ret
    //     0xc488cc: ret             
    // 0xc488d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc488d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc488d4: b               #0xc48894
  }
}
