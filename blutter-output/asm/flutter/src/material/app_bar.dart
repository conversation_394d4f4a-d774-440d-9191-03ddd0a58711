// lib: , url: package:flutter/src/material/app_bar.dart

// class id: 1048851, size: 0x8
class :: {
}

// class id: 3091, size: 0x68, field offset: 0x68
class _RenderAppBarTitleBox extends RenderAligningShiftedBox {

  _ computeDryBaseline(/* No info */) {
    // ** addr: 0x73cf48, size: 0x174
    // 0x73cf48: EnterFrame
    //     0x73cf48: stp             fp, lr, [SP, #-0x10]!
    //     0x73cf4c: mov             fp, SP
    // 0x73cf50: AllocStack(0x30)
    //     0x73cf50: sub             SP, SP, #0x30
    // 0x73cf54: SetupParameters(_RenderAppBarTitleBox this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x73cf54: mov             x5, x1
    //     0x73cf58: mov             x4, x2
    //     0x73cf5c: stur            x1, [fp, #-8]
    //     0x73cf60: stur            x2, [fp, #-0x10]
    //     0x73cf64: stur            x3, [fp, #-0x18]
    // 0x73cf68: CheckStackOverflow
    //     0x73cf68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73cf6c: cmp             SP, x16
    //     0x73cf70: b.ls            #0x73d0a4
    // 0x73cf74: mov             x0, x4
    // 0x73cf78: r2 = Null
    //     0x73cf78: mov             x2, NULL
    // 0x73cf7c: r1 = Null
    //     0x73cf7c: mov             x1, NULL
    // 0x73cf80: r4 = 60
    //     0x73cf80: movz            x4, #0x3c
    // 0x73cf84: branchIfSmi(r0, 0x73cf90)
    //     0x73cf84: tbz             w0, #0, #0x73cf90
    // 0x73cf88: r4 = LoadClassIdInstr(r0)
    //     0x73cf88: ldur            x4, [x0, #-1]
    //     0x73cf8c: ubfx            x4, x4, #0xc, #0x14
    // 0x73cf90: sub             x4, x4, #0xc83
    // 0x73cf94: cmp             x4, #1
    // 0x73cf98: b.ls            #0x73cfac
    // 0x73cf9c: r8 = BoxConstraints
    //     0x73cf9c: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x73cfa0: r3 = Null
    //     0x73cfa0: add             x3, PP, #0x55, lsl #12  ; [pp+0x55178] Null
    //     0x73cfa4: ldr             x3, [x3, #0x178]
    // 0x73cfa8: r0 = BoxConstraints()
    //     0x73cfa8: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x73cfac: r16 = inf
    //     0x73cfac: ldr             x16, [PP, #0x49e0]  ; [pp+0x49e0] inf
    // 0x73cfb0: str             x16, [SP]
    // 0x73cfb4: ldur            x1, [fp, #-0x10]
    // 0x73cfb8: r4 = const [0, 0x2, 0x1, 0x1, maxHeight, 0x1, null]
    //     0x73cfb8: add             x4, PP, #0x55, lsl #12  ; [pp+0x55160] List(7) [0, 0x2, 0x1, 0x1, "maxHeight", 0x1, Null]
    //     0x73cfbc: ldr             x4, [x4, #0x160]
    // 0x73cfc0: r0 = copyWith()
    //     0x73cfc0: bl              #0x73d230  ; [package:flutter/src/rendering/box.dart] BoxConstraints::copyWith
    // 0x73cfc4: mov             x4, x0
    // 0x73cfc8: ldur            x0, [fp, #-8]
    // 0x73cfcc: stur            x4, [fp, #-0x28]
    // 0x73cfd0: LoadField: r5 = r0->field_57
    //     0x73cfd0: ldur            w5, [x0, #0x57]
    // 0x73cfd4: DecompressPointer r5
    //     0x73cfd4: add             x5, x5, HEAP, lsl #32
    // 0x73cfd8: stur            x5, [fp, #-0x20]
    // 0x73cfdc: cmp             w5, NULL
    // 0x73cfe0: b.ne            #0x73cff4
    // 0x73cfe4: r0 = Null
    //     0x73cfe4: mov             x0, NULL
    // 0x73cfe8: LeaveFrame
    //     0x73cfe8: mov             SP, fp
    //     0x73cfec: ldp             fp, lr, [SP], #0x10
    // 0x73cff0: ret
    //     0x73cff0: ret             
    // 0x73cff4: mov             x1, x5
    // 0x73cff8: mov             x2, x4
    // 0x73cffc: ldur            x3, [fp, #-0x18]
    // 0x73d000: r0 = getDryBaseline()
    //     0x73d000: bl              #0x730f54  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryBaseline
    // 0x73d004: stur            x0, [fp, #-0x18]
    // 0x73d008: cmp             w0, NULL
    // 0x73d00c: b.ne            #0x73d020
    // 0x73d010: r0 = Null
    //     0x73d010: mov             x0, NULL
    // 0x73d014: LeaveFrame
    //     0x73d014: mov             SP, fp
    //     0x73d018: ldp             fp, lr, [SP], #0x10
    // 0x73d01c: ret
    //     0x73d01c: ret             
    // 0x73d020: ldur            x1, [fp, #-0x20]
    // 0x73d024: ldur            x2, [fp, #-0x28]
    // 0x73d028: r0 = getDryLayout()
    //     0x73d028: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x73d02c: ldur            x1, [fp, #-8]
    // 0x73d030: stur            x0, [fp, #-0x20]
    // 0x73d034: r0 = resolvedAlignment()
    //     0x73d034: bl              #0x73d0bc  ; [package:flutter/src/rendering/shifted_box.dart] RenderAligningShiftedBox::resolvedAlignment
    // 0x73d038: ldur            x1, [fp, #-8]
    // 0x73d03c: ldur            x2, [fp, #-0x10]
    // 0x73d040: stur            x0, [fp, #-8]
    // 0x73d044: r0 = getDryLayout()
    //     0x73d044: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x73d048: mov             x1, x0
    // 0x73d04c: ldur            x2, [fp, #-0x20]
    // 0x73d050: r0 = -()
    //     0x73d050: bl              #0x618814  ; [dart:ui] Size::-
    // 0x73d054: ldur            x1, [fp, #-8]
    // 0x73d058: mov             x2, x0
    // 0x73d05c: r0 = alongOffset()
    //     0x73d05c: bl              #0x73c5c8  ; [package:flutter/src/painting/alignment.dart] Alignment::alongOffset
    // 0x73d060: LoadField: d0 = r0->field_f
    //     0x73d060: ldur            d0, [x0, #0xf]
    // 0x73d064: ldur            x1, [fp, #-0x18]
    // 0x73d068: LoadField: d1 = r1->field_7
    //     0x73d068: ldur            d1, [x1, #7]
    // 0x73d06c: fadd            d2, d1, d0
    // 0x73d070: r0 = inline_Allocate_Double()
    //     0x73d070: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x73d074: add             x0, x0, #0x10
    //     0x73d078: cmp             x1, x0
    //     0x73d07c: b.ls            #0x73d0ac
    //     0x73d080: str             x0, [THR, #0x50]  ; THR::top
    //     0x73d084: sub             x0, x0, #0xf
    //     0x73d088: movz            x1, #0xe15c
    //     0x73d08c: movk            x1, #0x3, lsl #16
    //     0x73d090: stur            x1, [x0, #-1]
    // 0x73d094: StoreField: r0->field_7 = d2
    //     0x73d094: stur            d2, [x0, #7]
    // 0x73d098: LeaveFrame
    //     0x73d098: mov             SP, fp
    //     0x73d09c: ldp             fp, lr, [SP], #0x10
    // 0x73d0a0: ret
    //     0x73d0a0: ret             
    // 0x73d0a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73d0a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73d0a8: b               #0x73cf74
    // 0x73d0ac: SaveReg d2
    //     0x73d0ac: str             q2, [SP, #-0x10]!
    // 0x73d0b0: r0 = AllocateDouble()
    //     0x73d0b0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x73d0b4: RestoreReg d2
    //     0x73d0b4: ldr             q2, [SP], #0x10
    // 0x73d0b8: b               #0x73d094
  }
  _ computeDryLayout(/* No info */) {
    // ** addr: 0x754640, size: 0x8c
    // 0x754640: EnterFrame
    //     0x754640: stp             fp, lr, [SP, #-0x10]!
    //     0x754644: mov             fp, SP
    // 0x754648: AllocStack(0x18)
    //     0x754648: sub             SP, SP, #0x18
    // 0x75464c: SetupParameters(_RenderAppBarTitleBox this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x75464c: mov             x0, x2
    //     0x754650: stur            x2, [fp, #-0x10]
    //     0x754654: mov             x2, x1
    //     0x754658: stur            x1, [fp, #-8]
    // 0x75465c: CheckStackOverflow
    //     0x75465c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x754660: cmp             SP, x16
    //     0x754664: b.ls            #0x7546c0
    // 0x754668: r16 = inf
    //     0x754668: ldr             x16, [PP, #0x49e0]  ; [pp+0x49e0] inf
    // 0x75466c: str             x16, [SP]
    // 0x754670: mov             x1, x0
    // 0x754674: r4 = const [0, 0x2, 0x1, 0x1, maxHeight, 0x1, null]
    //     0x754674: add             x4, PP, #0x55, lsl #12  ; [pp+0x55160] List(7) [0, 0x2, 0x1, 0x1, "maxHeight", 0x1, Null]
    //     0x754678: ldr             x4, [x4, #0x160]
    // 0x75467c: r0 = copyWith()
    //     0x75467c: bl              #0x73d230  ; [package:flutter/src/rendering/box.dart] BoxConstraints::copyWith
    // 0x754680: mov             x1, x0
    // 0x754684: ldur            x0, [fp, #-8]
    // 0x754688: LoadField: r2 = r0->field_57
    //     0x754688: ldur            w2, [x0, #0x57]
    // 0x75468c: DecompressPointer r2
    //     0x75468c: add             x2, x2, HEAP, lsl #32
    // 0x754690: cmp             w2, NULL
    // 0x754694: b.eq            #0x7546c8
    // 0x754698: mov             x16, x1
    // 0x75469c: mov             x1, x2
    // 0x7546a0: mov             x2, x16
    // 0x7546a4: r0 = getDryLayout()
    //     0x7546a4: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x7546a8: ldur            x1, [fp, #-0x10]
    // 0x7546ac: mov             x2, x0
    // 0x7546b0: r0 = constrain()
    //     0x7546b0: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x7546b4: LeaveFrame
    //     0x7546b4: mov             SP, fp
    //     0x7546b8: ldp             fp, lr, [SP], #0x10
    // 0x7546bc: ret
    //     0x7546bc: ret             
    // 0x7546c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7546c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7546c4: b               #0x754668
    // 0x7546c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7546c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x768fc0, size: 0x1b0
    // 0x768fc0: EnterFrame
    //     0x768fc0: stp             fp, lr, [SP, #-0x10]!
    //     0x768fc4: mov             fp, SP
    // 0x768fc8: AllocStack(0x18)
    //     0x768fc8: sub             SP, SP, #0x18
    // 0x768fcc: SetupParameters(_RenderAppBarTitleBox this /* r1 => r3, fp-0x10 */)
    //     0x768fcc: mov             x3, x1
    //     0x768fd0: stur            x1, [fp, #-0x10]
    // 0x768fd4: CheckStackOverflow
    //     0x768fd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x768fd8: cmp             SP, x16
    //     0x768fdc: b.ls            #0x769160
    // 0x768fe0: LoadField: r4 = r3->field_27
    //     0x768fe0: ldur            w4, [x3, #0x27]
    // 0x768fe4: DecompressPointer r4
    //     0x768fe4: add             x4, x4, HEAP, lsl #32
    // 0x768fe8: stur            x4, [fp, #-8]
    // 0x768fec: cmp             w4, NULL
    // 0x768ff0: b.eq            #0x769124
    // 0x768ff4: mov             x0, x4
    // 0x768ff8: r2 = Null
    //     0x768ff8: mov             x2, NULL
    // 0x768ffc: r1 = Null
    //     0x768ffc: mov             x1, NULL
    // 0x769000: r4 = LoadClassIdInstr(r0)
    //     0x769000: ldur            x4, [x0, #-1]
    //     0x769004: ubfx            x4, x4, #0xc, #0x14
    // 0x769008: sub             x4, x4, #0xc83
    // 0x76900c: cmp             x4, #1
    // 0x769010: b.ls            #0x769024
    // 0x769014: r8 = BoxConstraints
    //     0x769014: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x769018: r3 = Null
    //     0x769018: add             x3, PP, #0x55, lsl #12  ; [pp+0x55150] Null
    //     0x76901c: ldr             x3, [x3, #0x150]
    // 0x769020: r0 = BoxConstraints()
    //     0x769020: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x769024: r16 = inf
    //     0x769024: ldr             x16, [PP, #0x49e0]  ; [pp+0x49e0] inf
    // 0x769028: str             x16, [SP]
    // 0x76902c: ldur            x1, [fp, #-8]
    // 0x769030: r4 = const [0, 0x2, 0x1, 0x1, maxHeight, 0x1, null]
    //     0x769030: add             x4, PP, #0x55, lsl #12  ; [pp+0x55160] List(7) [0, 0x2, 0x1, 0x1, "maxHeight", 0x1, Null]
    //     0x769034: ldr             x4, [x4, #0x160]
    // 0x769038: r0 = copyWith()
    //     0x769038: bl              #0x73d230  ; [package:flutter/src/rendering/box.dart] BoxConstraints::copyWith
    // 0x76903c: ldur            x3, [fp, #-0x10]
    // 0x769040: LoadField: r1 = r3->field_57
    //     0x769040: ldur            w1, [x3, #0x57]
    // 0x769044: DecompressPointer r1
    //     0x769044: add             x1, x1, HEAP, lsl #32
    // 0x769048: cmp             w1, NULL
    // 0x76904c: b.eq            #0x769168
    // 0x769050: r2 = LoadClassIdInstr(r1)
    //     0x769050: ldur            x2, [x1, #-1]
    //     0x769054: ubfx            x2, x2, #0xc, #0x14
    // 0x769058: r16 = true
    //     0x769058: add             x16, NULL, #0x20  ; true
    // 0x76905c: str             x16, [SP]
    // 0x769060: mov             x16, x0
    // 0x769064: mov             x0, x2
    // 0x769068: mov             x2, x16
    // 0x76906c: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x76906c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x769070: ldr             x4, [x4, #0x5c0]
    // 0x769074: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x769074: movz            x17, #0xed1d
    //     0x769078: add             lr, x0, x17
    //     0x76907c: ldr             lr, [x21, lr, lsl #3]
    //     0x769080: blr             lr
    // 0x769084: ldur            x3, [fp, #-0x10]
    // 0x769088: LoadField: r4 = r3->field_27
    //     0x769088: ldur            w4, [x3, #0x27]
    // 0x76908c: DecompressPointer r4
    //     0x76908c: add             x4, x4, HEAP, lsl #32
    // 0x769090: stur            x4, [fp, #-8]
    // 0x769094: cmp             w4, NULL
    // 0x769098: b.eq            #0x769140
    // 0x76909c: mov             x0, x4
    // 0x7690a0: r2 = Null
    //     0x7690a0: mov             x2, NULL
    // 0x7690a4: r1 = Null
    //     0x7690a4: mov             x1, NULL
    // 0x7690a8: r4 = LoadClassIdInstr(r0)
    //     0x7690a8: ldur            x4, [x0, #-1]
    //     0x7690ac: ubfx            x4, x4, #0xc, #0x14
    // 0x7690b0: sub             x4, x4, #0xc83
    // 0x7690b4: cmp             x4, #1
    // 0x7690b8: b.ls            #0x7690cc
    // 0x7690bc: r8 = BoxConstraints
    //     0x7690bc: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x7690c0: r3 = Null
    //     0x7690c0: add             x3, PP, #0x55, lsl #12  ; [pp+0x55168] Null
    //     0x7690c4: ldr             x3, [x3, #0x168]
    // 0x7690c8: r0 = BoxConstraints()
    //     0x7690c8: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x7690cc: ldur            x0, [fp, #-0x10]
    // 0x7690d0: LoadField: r1 = r0->field_57
    //     0x7690d0: ldur            w1, [x0, #0x57]
    // 0x7690d4: DecompressPointer r1
    //     0x7690d4: add             x1, x1, HEAP, lsl #32
    // 0x7690d8: cmp             w1, NULL
    // 0x7690dc: b.eq            #0x76916c
    // 0x7690e0: r0 = size()
    //     0x7690e0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7690e4: ldur            x1, [fp, #-8]
    // 0x7690e8: mov             x2, x0
    // 0x7690ec: r0 = constrain()
    //     0x7690ec: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x7690f0: ldur            x1, [fp, #-0x10]
    // 0x7690f4: StoreField: r1->field_53 = r0
    //     0x7690f4: stur            w0, [x1, #0x53]
    //     0x7690f8: ldurb           w16, [x1, #-1]
    //     0x7690fc: ldurb           w17, [x0, #-1]
    //     0x769100: and             x16, x17, x16, lsr #2
    //     0x769104: tst             x16, HEAP, lsr #32
    //     0x769108: b.eq            #0x769110
    //     0x76910c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x769110: r0 = alignChild()
    //     0x769110: bl              #0x769170  ; [package:flutter/src/rendering/shifted_box.dart] RenderAligningShiftedBox::alignChild
    // 0x769114: r0 = Null
    //     0x769114: mov             x0, NULL
    // 0x769118: LeaveFrame
    //     0x769118: mov             SP, fp
    //     0x76911c: ldp             fp, lr, [SP], #0x10
    // 0x769120: ret
    //     0x769120: ret             
    // 0x769124: r0 = StateError()
    //     0x769124: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x769128: mov             x1, x0
    // 0x76912c: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76912c: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x769130: StoreField: r1->field_b = r0
    //     0x769130: stur            w0, [x1, #0xb]
    // 0x769134: mov             x0, x1
    // 0x769138: r0 = Throw()
    //     0x769138: bl              #0xec04b8  ; ThrowStub
    // 0x76913c: brk             #0
    // 0x769140: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x769140: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x769144: r0 = StateError()
    //     0x769144: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x769148: mov             x1, x0
    // 0x76914c: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76914c: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x769150: StoreField: r1->field_b = r0
    //     0x769150: stur            w0, [x1, #0xb]
    // 0x769154: mov             x0, x1
    // 0x769158: r0 = Throw()
    //     0x769158: bl              #0xec04b8  ; ThrowStub
    // 0x76915c: brk             #0
    // 0x769160: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x769160: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x769164: b               #0x768fe0
    // 0x769168: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x769168: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76916c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76916c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _RenderAppBarTitleBox(/* No info */) {
    // ** addr: 0x85a000, size: 0xa4
    // 0x85a000: EnterFrame
    //     0x85a000: stp             fp, lr, [SP, #-0x10]!
    //     0x85a004: mov             fp, SP
    // 0x85a008: AllocStack(0x8)
    //     0x85a008: sub             SP, SP, #8
    // 0x85a00c: r0 = Instance_Alignment
    //     0x85a00c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x85a010: ldr             x0, [x0, #0x898]
    // 0x85a014: stur            x1, [fp, #-8]
    // 0x85a018: mov             x16, x2
    // 0x85a01c: mov             x2, x1
    // 0x85a020: mov             x1, x16
    // 0x85a024: CheckStackOverflow
    //     0x85a024: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85a028: cmp             SP, x16
    //     0x85a02c: b.ls            #0x85a09c
    // 0x85a030: StoreField: r2->field_5f = r0
    //     0x85a030: stur            w0, [x2, #0x5f]
    // 0x85a034: mov             x0, x1
    // 0x85a038: StoreField: r2->field_63 = r0
    //     0x85a038: stur            w0, [x2, #0x63]
    //     0x85a03c: ldurb           w16, [x2, #-1]
    //     0x85a040: ldurb           w17, [x0, #-1]
    //     0x85a044: and             x16, x17, x16, lsr #2
    //     0x85a048: tst             x16, HEAP, lsr #32
    //     0x85a04c: b.eq            #0x85a054
    //     0x85a050: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x85a054: r0 = _LayoutCacheStorage()
    //     0x85a054: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x85a058: ldur            x2, [fp, #-8]
    // 0x85a05c: StoreField: r2->field_4f = r0
    //     0x85a05c: stur            w0, [x2, #0x4f]
    //     0x85a060: ldurb           w16, [x2, #-1]
    //     0x85a064: ldurb           w17, [x0, #-1]
    //     0x85a068: and             x16, x17, x16, lsr #2
    //     0x85a06c: tst             x16, HEAP, lsr #32
    //     0x85a070: b.eq            #0x85a078
    //     0x85a074: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x85a078: mov             x1, x2
    // 0x85a07c: r0 = RenderObject()
    //     0x85a07c: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x85a080: ldur            x1, [fp, #-8]
    // 0x85a084: r2 = Null
    //     0x85a084: mov             x2, NULL
    // 0x85a088: r0 = child=()
    //     0x85a088: bl              #0x895c88  ; [package:flutter/src/rendering/shifted_box.dart] _RenderShiftedBox&RenderBox&RenderObjectWithChildMixin::child=
    // 0x85a08c: r0 = Null
    //     0x85a08c: mov             x0, NULL
    // 0x85a090: LeaveFrame
    //     0x85a090: mov             SP, fp
    //     0x85a094: ldp             fp, lr, [SP], #0x10
    // 0x85a098: ret
    //     0x85a098: ret             
    // 0x85a09c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85a09c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85a0a0: b               #0x85a030
  }
}

// class id: 3416, size: 0xb4, field offset: 0x8
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {

  _ _SliverAppBarDelegate(/* No info */) {
    // ** addr: 0x9e9198, size: 0x124
    // 0x9e9198: EnterFrame
    //     0x9e9198: stp             fp, lr, [SP, #-0x10]!
    //     0x9e919c: mov             fp, SP
    // 0x9e91a0: r10 = true
    //     0x9e91a0: add             x10, NULL, #0x20  ; true
    // 0x9e91a4: r9 = false
    //     0x9e91a4: add             x9, NULL, #0x30  ; false
    // 0x9e91a8: r8 = Instance_Color
    //     0x9e91a8: ldr             x8, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0x9e91ac: r4 = Instance__SliverAppVariant
    //     0x9e91ac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f618] Obj!_SliverAppVariant@e36bc1
    //     0x9e91b0: ldr             x4, [x4, #0x618]
    // 0x9e91b4: d2 = 56.000000
    //     0x9e91b4: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0x9e91b8: ldr             d2, [x17, #0xf60]
    // 0x9e91bc: mov             x16, x6
    // 0x9e91c0: mov             x6, x1
    // 0x9e91c4: mov             x1, x16
    // 0x9e91c8: mov             x16, x5
    // 0x9e91cc: mov             x5, x2
    // 0x9e91d0: mov             x2, x16
    // 0x9e91d4: mov             x0, x7
    // 0x9e91d8: StoreField: r6->field_b = r10
    //     0x9e91d8: stur            w10, [x6, #0xb]
    // 0x9e91dc: StoreField: r6->field_f = r0
    //     0x9e91dc: stur            w0, [x6, #0xf]
    //     0x9e91e0: ldurb           w16, [x6, #-1]
    //     0x9e91e4: ldurb           w17, [x0, #-1]
    //     0x9e91e8: and             x16, x17, x16, lsr #2
    //     0x9e91ec: tst             x16, HEAP, lsr #32
    //     0x9e91f0: b.eq            #0x9e91f8
    //     0x9e91f4: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x9e91f8: mov             x0, x3
    // 0x9e91fc: StoreField: r6->field_13 = r0
    //     0x9e91fc: stur            w0, [x6, #0x13]
    //     0x9e9200: ldurb           w16, [x6, #-1]
    //     0x9e9204: ldurb           w17, [x0, #-1]
    //     0x9e9208: and             x16, x17, x16, lsr #2
    //     0x9e920c: tst             x16, HEAP, lsr #32
    //     0x9e9210: b.eq            #0x9e9218
    //     0x9e9214: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x9e9218: mov             x0, x1
    // 0x9e921c: ArrayStore: r6[0] = r0  ; List_4
    //     0x9e921c: stur            w0, [x6, #0x17]
    //     0x9e9220: ldurb           w16, [x6, #-1]
    //     0x9e9224: ldurb           w17, [x0, #-1]
    //     0x9e9228: and             x16, x17, x16, lsr #2
    //     0x9e922c: tst             x16, HEAP, lsr #32
    //     0x9e9230: b.eq            #0x9e9238
    //     0x9e9234: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x9e9238: StoreField: r6->field_2f = r9
    //     0x9e9238: stur            w9, [x6, #0x2f]
    // 0x9e923c: StoreField: r6->field_37 = r8
    //     0x9e923c: stur            w8, [x6, #0x37]
    // 0x9e9240: StoreField: r6->field_43 = r10
    //     0x9e9240: stur            w10, [x6, #0x43]
    // 0x9e9244: StoreField: r6->field_4b = r9
    //     0x9e9244: stur            w9, [x6, #0x4b]
    // 0x9e9248: mov             x0, x2
    // 0x9e924c: StoreField: r6->field_53 = r0
    //     0x9e924c: stur            w0, [x6, #0x53]
    //     0x9e9250: ldurb           w16, [x6, #-1]
    //     0x9e9254: ldurb           w17, [x0, #-1]
    //     0x9e9258: and             x16, x17, x16, lsr #2
    //     0x9e925c: tst             x16, HEAP, lsr #32
    //     0x9e9260: b.eq            #0x9e9268
    //     0x9e9264: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x9e9268: StoreField: r6->field_57 = d0
    //     0x9e9268: stur            d0, [x6, #0x57]
    // 0x9e926c: StoreField: r6->field_5f = d1
    //     0x9e926c: stur            d1, [x6, #0x5f]
    // 0x9e9270: StoreField: r6->field_67 = r9
    //     0x9e9270: stur            w9, [x6, #0x67]
    // 0x9e9274: StoreField: r6->field_6b = r10
    //     0x9e9274: stur            w10, [x6, #0x6b]
    // 0x9e9278: ldr             x0, [fp, #0x10]
    // 0x9e927c: StoreField: r6->field_a3 = r0
    //     0x9e927c: stur            w0, [x6, #0xa3]
    //     0x9e9280: ldurb           w16, [x6, #-1]
    //     0x9e9284: ldurb           w17, [x0, #-1]
    //     0x9e9288: and             x16, x17, x16, lsr #2
    //     0x9e928c: tst             x16, HEAP, lsr #32
    //     0x9e9290: b.eq            #0x9e9298
    //     0x9e9294: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x9e9298: StoreField: r6->field_73 = d2
    //     0x9e9298: stur            d2, [x6, #0x73]
    // 0x9e929c: StoreField: r6->field_93 = r9
    //     0x9e929c: stur            w9, [x6, #0x93]
    // 0x9e92a0: StoreField: r6->field_9b = r4
    //     0x9e92a0: stur            w4, [x6, #0x9b]
    // 0x9e92a4: StoreField: r6->field_9f = r5
    //     0x9e92a4: stur            w5, [x6, #0x9f]
    // 0x9e92a8: StoreField: r6->field_8b = rZR
    //     0x9e92a8: stur            xzr, [x6, #0x8b]
    // 0x9e92ac: r0 = Null
    //     0x9e92ac: mov             x0, NULL
    // 0x9e92b0: LeaveFrame
    //     0x9e92b0: mov             SP, fp
    //     0x9e92b4: ldp             fp, lr, [SP], #0x10
    // 0x9e92b8: ret
    //     0x9e92b8: ret             
  }
  _ shouldRebuild(/* No info */) {
    // ** addr: 0xd8eaa4, size: 0x1a0
    // 0xd8eaa4: EnterFrame
    //     0xd8eaa4: stp             fp, lr, [SP, #-0x10]!
    //     0xd8eaa8: mov             fp, SP
    // 0xd8eaac: AllocStack(0x20)
    //     0xd8eaac: sub             SP, SP, #0x20
    // 0xd8eab0: SetupParameters(_SliverAppBarDelegate this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xd8eab0: mov             x4, x1
    //     0xd8eab4: mov             x3, x2
    //     0xd8eab8: stur            x1, [fp, #-8]
    //     0xd8eabc: stur            x2, [fp, #-0x10]
    // 0xd8eac0: CheckStackOverflow
    //     0xd8eac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8eac4: cmp             SP, x16
    //     0xd8eac8: b.ls            #0xd8ec3c
    // 0xd8eacc: mov             x0, x3
    // 0xd8ead0: r2 = Null
    //     0xd8ead0: mov             x2, NULL
    // 0xd8ead4: r1 = Null
    //     0xd8ead4: mov             x1, NULL
    // 0xd8ead8: r4 = 60
    //     0xd8ead8: movz            x4, #0x3c
    // 0xd8eadc: branchIfSmi(r0, 0xd8eae8)
    //     0xd8eadc: tbz             w0, #0, #0xd8eae8
    // 0xd8eae0: r4 = LoadClassIdInstr(r0)
    //     0xd8eae0: ldur            x4, [x0, #-1]
    //     0xd8eae4: ubfx            x4, x4, #0xc, #0x14
    // 0xd8eae8: cmp             x4, #0xd58
    // 0xd8eaec: b.eq            #0xd8eb04
    // 0xd8eaf0: r8 = _SliverAppBarDelegate
    //     0xd8eaf0: add             x8, PP, #0x55, lsl #12  ; [pp+0x55138] Type: _SliverAppBarDelegate
    //     0xd8eaf4: ldr             x8, [x8, #0x138]
    // 0xd8eaf8: r3 = Null
    //     0xd8eaf8: add             x3, PP, #0x55, lsl #12  ; [pp+0x55140] Null
    //     0xd8eafc: ldr             x3, [x3, #0x140]
    // 0xd8eb00: r0 = DefaultTypeTest()
    //     0xd8eb00: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xd8eb04: ldur            x1, [fp, #-8]
    // 0xd8eb08: LoadField: r0 = r1->field_f
    //     0xd8eb08: ldur            w0, [x1, #0xf]
    // 0xd8eb0c: DecompressPointer r0
    //     0xd8eb0c: add             x0, x0, HEAP, lsl #32
    // 0xd8eb10: ldur            x2, [fp, #-0x10]
    // 0xd8eb14: LoadField: r3 = r2->field_f
    //     0xd8eb14: ldur            w3, [x2, #0xf]
    // 0xd8eb18: DecompressPointer r3
    //     0xd8eb18: add             x3, x3, HEAP, lsl #32
    // 0xd8eb1c: cmp             w0, w3
    // 0xd8eb20: b.ne            #0xd8ec04
    // 0xd8eb24: LoadField: r0 = r1->field_13
    //     0xd8eb24: ldur            w0, [x1, #0x13]
    // 0xd8eb28: DecompressPointer r0
    //     0xd8eb28: add             x0, x0, HEAP, lsl #32
    // 0xd8eb2c: LoadField: r3 = r2->field_13
    //     0xd8eb2c: ldur            w3, [x2, #0x13]
    // 0xd8eb30: DecompressPointer r3
    //     0xd8eb30: add             x3, x3, HEAP, lsl #32
    // 0xd8eb34: cmp             w0, w3
    // 0xd8eb38: b.ne            #0xd8ec04
    // 0xd8eb3c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xd8eb3c: ldur            w0, [x1, #0x17]
    // 0xd8eb40: DecompressPointer r0
    //     0xd8eb40: add             x0, x0, HEAP, lsl #32
    // 0xd8eb44: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xd8eb44: ldur            w3, [x2, #0x17]
    // 0xd8eb48: DecompressPointer r3
    //     0xd8eb48: add             x3, x3, HEAP, lsl #32
    // 0xd8eb4c: r4 = LoadClassIdInstr(r0)
    //     0xd8eb4c: ldur            x4, [x0, #-1]
    //     0xd8eb50: ubfx            x4, x4, #0xc, #0x14
    // 0xd8eb54: stp             x3, x0, [SP]
    // 0xd8eb58: mov             x0, x4
    // 0xd8eb5c: mov             lr, x0
    // 0xd8eb60: ldr             lr, [x21, lr, lsl #3]
    // 0xd8eb64: blr             lr
    // 0xd8eb68: tbnz            w0, #4, #0xd8ec04
    // 0xd8eb6c: d0 = 0.000000
    //     0xd8eb6c: eor             v0.16b, v0.16b, v0.16b
    // 0xd8eb70: fcmp            d0, d0
    // 0xd8eb74: b.ne            #0xd8ec04
    // 0xd8eb78: r16 = Instance_Color
    //     0xd8eb78: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xd8eb7c: r30 = Instance_Color
    //     0xd8eb7c: ldr             lr, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xd8eb80: stp             lr, x16, [SP]
    // 0xd8eb84: r0 = ==()
    //     0xd8eb84: bl              #0xd38bd0  ; [dart:ui] Color::==
    // 0xd8eb88: tbnz            w0, #4, #0xd8ec04
    // 0xd8eb8c: ldur            x1, [fp, #-8]
    // 0xd8eb90: ldur            x2, [fp, #-0x10]
    // 0xd8eb94: LoadField: r0 = r1->field_53
    //     0xd8eb94: ldur            w0, [x1, #0x53]
    // 0xd8eb98: DecompressPointer r0
    //     0xd8eb98: add             x0, x0, HEAP, lsl #32
    // 0xd8eb9c: LoadField: r3 = r2->field_53
    //     0xd8eb9c: ldur            w3, [x2, #0x53]
    // 0xd8eba0: DecompressPointer r3
    //     0xd8eba0: add             x3, x3, HEAP, lsl #32
    // 0xd8eba4: r4 = LoadClassIdInstr(r0)
    //     0xd8eba4: ldur            x4, [x0, #-1]
    //     0xd8eba8: ubfx            x4, x4, #0xc, #0x14
    // 0xd8ebac: stp             x3, x0, [SP]
    // 0xd8ebb0: mov             x0, x4
    // 0xd8ebb4: mov             lr, x0
    // 0xd8ebb8: ldr             lr, [x21, lr, lsl #3]
    // 0xd8ebbc: blr             lr
    // 0xd8ebc0: tbnz            w0, #4, #0xd8ec04
    // 0xd8ebc4: ldur            x1, [fp, #-8]
    // 0xd8ebc8: ldur            x2, [fp, #-0x10]
    // 0xd8ebcc: LoadField: d0 = r1->field_5f
    //     0xd8ebcc: ldur            d0, [x1, #0x5f]
    // 0xd8ebd0: LoadField: d1 = r2->field_5f
    //     0xd8ebd0: ldur            d1, [x2, #0x5f]
    // 0xd8ebd4: fcmp            d0, d1
    // 0xd8ebd8: b.ne            #0xd8ec04
    // 0xd8ebdc: LoadField: r3 = r1->field_a3
    //     0xd8ebdc: ldur            w3, [x1, #0xa3]
    // 0xd8ebe0: DecompressPointer r3
    //     0xd8ebe0: add             x3, x3, HEAP, lsl #32
    // 0xd8ebe4: LoadField: r4 = r2->field_a3
    //     0xd8ebe4: ldur            w4, [x2, #0xa3]
    // 0xd8ebe8: DecompressPointer r4
    //     0xd8ebe8: add             x4, x4, HEAP, lsl #32
    // 0xd8ebec: cmp             w3, w4
    // 0xd8ebf0: b.ne            #0xd8ec04
    // 0xd8ebf4: d0 = 56.000000
    //     0xd8ebf4: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0xd8ebf8: ldr             d0, [x17, #0xf60]
    // 0xd8ebfc: fcmp            d0, d0
    // 0xd8ec00: b.eq            #0xd8ec0c
    // 0xd8ec04: r0 = true
    //     0xd8ec04: add             x0, NULL, #0x20  ; true
    // 0xd8ec08: b               #0xd8ec30
    // 0xd8ec0c: LoadField: r3 = r1->field_9f
    //     0xd8ec0c: ldur            w3, [x1, #0x9f]
    // 0xd8ec10: DecompressPointer r3
    //     0xd8ec10: add             x3, x3, HEAP, lsl #32
    // 0xd8ec14: LoadField: r1 = r2->field_9f
    //     0xd8ec14: ldur            w1, [x2, #0x9f]
    // 0xd8ec18: DecompressPointer r1
    //     0xd8ec18: add             x1, x1, HEAP, lsl #32
    // 0xd8ec1c: cmp             w3, w1
    // 0xd8ec20: r16 = true
    //     0xd8ec20: add             x16, NULL, #0x20  ; true
    // 0xd8ec24: r17 = false
    //     0xd8ec24: add             x17, NULL, #0x30  ; false
    // 0xd8ec28: csel            x2, x16, x17, ne
    // 0xd8ec2c: mov             x0, x2
    // 0xd8ec30: LeaveFrame
    //     0xd8ec30: mov             SP, fp
    //     0xd8ec34: ldp             fp, lr, [SP], #0x10
    // 0xd8ec38: ret
    //     0xd8ec38: ret             
    // 0xd8ec3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8ec3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8ec40: b               #0xd8eacc
  }
  _ build(/* No info */) {
    // ** addr: 0xd8ec44, size: 0x25c
    // 0xd8ec44: EnterFrame
    //     0xd8ec44: stp             fp, lr, [SP, #-0x10]!
    //     0xd8ec48: mov             fp, SP
    // 0xd8ec4c: AllocStack(0xd0)
    //     0xd8ec4c: sub             SP, SP, #0xd0
    // 0xd8ec50: SetupParameters(_SliverAppBarDelegate this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x38 */)
    //     0xd8ec50: mov             x0, x1
    //     0xd8ec54: stur            x1, [fp, #-8]
    //     0xd8ec58: stur            x2, [fp, #-0x10]
    //     0xd8ec5c: stur            d0, [fp, #-0x38]
    // 0xd8ec60: CheckStackOverflow
    //     0xd8ec60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8ec64: cmp             SP, x16
    //     0xd8ec68: b.ls            #0xd8ee6c
    // 0xd8ec6c: mov             x1, x0
    // 0xd8ec70: r0 = maxExtent()
    //     0xd8ec70: bl              #0xd904e0  ; [package:flutter/src/material/app_bar.dart] _SliverAppBarDelegate::maxExtent
    // 0xd8ec74: ldur            x0, [fp, #-8]
    // 0xd8ec78: LoadField: d0 = r0->field_57
    //     0xd8ec78: ldur            d0, [x0, #0x57]
    // 0xd8ec7c: ldur            x1, [fp, #-0x10]
    // 0xd8ec80: stur            d0, [fp, #-0x40]
    // 0xd8ec84: tbnz            w1, #4, #0xd8ec94
    // 0xd8ec88: ldur            d1, [fp, #-0x38]
    // 0xd8ec8c: r2 = true
    //     0xd8ec8c: add             x2, NULL, #0x20  ; true
    // 0xd8ec90: b               #0xd8ecc8
    // 0xd8ec94: ldur            d1, [fp, #-0x38]
    // 0xd8ec98: mov             x1, x0
    // 0xd8ec9c: r0 = maxExtent()
    //     0xd8ec9c: bl              #0xd904e0  ; [package:flutter/src/material/app_bar.dart] _SliverAppBarDelegate::maxExtent
    // 0xd8eca0: mov             v1.16b, v0.16b
    // 0xd8eca4: ldur            d0, [fp, #-0x40]
    // 0xd8eca8: fsub            d2, d1, d0
    // 0xd8ecac: ldur            d1, [fp, #-0x38]
    // 0xd8ecb0: fcmp            d1, d2
    // 0xd8ecb4: r16 = true
    //     0xd8ecb4: add             x16, NULL, #0x20  ; true
    // 0xd8ecb8: r17 = false
    //     0xd8ecb8: add             x17, NULL, #0x30  ; false
    // 0xd8ecbc: csel            x0, x16, x17, gt
    // 0xd8ecc0: mov             x2, x0
    // 0xd8ecc4: ldur            x0, [fp, #-8]
    // 0xd8ecc8: stur            x2, [fp, #-0x18]
    // 0xd8eccc: LoadField: r3 = r0->field_f
    //     0xd8eccc: ldur            w3, [x0, #0xf]
    // 0xd8ecd0: DecompressPointer r3
    //     0xd8ecd0: add             x3, x3, HEAP, lsl #32
    // 0xd8ecd4: mov             x1, x0
    // 0xd8ecd8: stur            x3, [fp, #-0x10]
    // 0xd8ecdc: r0 = maxExtent()
    //     0xd8ecdc: bl              #0xd904e0  ; [package:flutter/src/material/app_bar.dart] _SliverAppBarDelegate::maxExtent
    // 0xd8ece0: ldur            x1, [fp, #-8]
    // 0xd8ece4: stur            d0, [fp, #-0x48]
    // 0xd8ece8: r0 = maxExtent()
    //     0xd8ece8: bl              #0xd904e0  ; [package:flutter/src/material/app_bar.dart] _SliverAppBarDelegate::maxExtent
    // 0xd8ecec: mov             v1.16b, v0.16b
    // 0xd8ecf0: ldur            d0, [fp, #-0x38]
    // 0xd8ecf4: fsub            d2, d1, d0
    // 0xd8ecf8: ldur            d0, [fp, #-0x40]
    // 0xd8ecfc: fcmp            d0, d2
    // 0xd8ed00: b.le            #0xd8ed0c
    // 0xd8ed04: mov             v1.16b, v0.16b
    // 0xd8ed08: b               #0xd8ed44
    // 0xd8ed0c: fcmp            d2, d0
    // 0xd8ed10: b.le            #0xd8ed1c
    // 0xd8ed14: mov             v1.16b, v2.16b
    // 0xd8ed18: b               #0xd8ed44
    // 0xd8ed1c: d1 = 0.000000
    //     0xd8ed1c: eor             v1.16b, v1.16b, v1.16b
    // 0xd8ed20: fcmp            d0, d1
    // 0xd8ed24: b.ne            #0xd8ed30
    // 0xd8ed28: fadd            d1, d0, d2
    // 0xd8ed2c: b               #0xd8ed44
    // 0xd8ed30: fcmp            d2, d2
    // 0xd8ed34: b.vc            #0xd8ed40
    // 0xd8ed38: mov             v1.16b, v2.16b
    // 0xd8ed3c: b               #0xd8ed44
    // 0xd8ed40: mov             v1.16b, v0.16b
    // 0xd8ed44: ldur            x0, [fp, #-8]
    // 0xd8ed48: ldur            x1, [fp, #-0x18]
    // 0xd8ed4c: stur            d1, [fp, #-0x38]
    // 0xd8ed50: LoadField: r2 = r0->field_13
    //     0xd8ed50: ldur            w2, [x0, #0x13]
    // 0xd8ed54: DecompressPointer r2
    //     0xd8ed54: add             x2, x2, HEAP, lsl #32
    // 0xd8ed58: stur            x2, [fp, #-0x28]
    // 0xd8ed5c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xd8ed5c: ldur            w3, [x0, #0x17]
    // 0xd8ed60: DecompressPointer r3
    //     0xd8ed60: add             x3, x3, HEAP, lsl #32
    // 0xd8ed64: stur            x3, [fp, #-0x20]
    // 0xd8ed68: tbnz            w1, #4, #0xd8ed74
    // 0xd8ed6c: r0 = Null
    //     0xd8ed6c: mov             x0, NULL
    // 0xd8ed70: b               #0xd8ed78
    // 0xd8ed74: r0 = 0.000000
    //     0xd8ed74: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xd8ed78: ldur            d2, [fp, #-0x48]
    // 0xd8ed7c: stur            x0, [fp, #-8]
    // 0xd8ed80: r0 = AppBar()
    //     0xd8ed80: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xd8ed84: stur            x0, [fp, #-0x30]
    // 0xd8ed88: ldur            x16, [fp, #-0x10]
    // 0xd8ed8c: stp             x16, NULL, [SP, #0x78]
    // 0xd8ed90: ldur            x16, [fp, #-0x28]
    // 0xd8ed94: ldur            lr, [fp, #-0x20]
    // 0xd8ed98: stp             lr, x16, [SP, #0x68]
    // 0xd8ed9c: ldur            x16, [fp, #-8]
    // 0xd8eda0: stp             x16, NULL, [SP, #0x58]
    // 0xd8eda4: stp             NULL, NULL, [SP, #0x48]
    // 0xd8eda8: r16 = Instance_Color
    //     0xd8eda8: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xd8edac: stp             x16, NULL, [SP, #0x38]
    // 0xd8edb0: stp             NULL, NULL, [SP, #0x28]
    // 0xd8edb4: stp             NULL, NULL, [SP, #0x18]
    // 0xd8edb8: r16 = 1.000000
    //     0xd8edb8: ldr             x16, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xd8edbc: r30 = 56.000000
    //     0xd8edbc: add             lr, PP, #0x27, lsl #12  ; [pp+0x27c68] 56
    //     0xd8edc0: ldr             lr, [lr, #0xc68]
    // 0xd8edc4: stp             lr, x16, [SP, #8]
    // 0xd8edc8: str             NULL, [SP]
    // 0xd8edcc: mov             x1, x0
    // 0xd8edd0: r4 = const [0, 0x12, 0x11, 0x1, actions, 0x3, backgroundColor, 0x9, bottom, 0x5, centerTitle, 0xc, elevation, 0x6, flexibleSpace, 0x4, foregroundColor, 0xa, iconTheme, 0xb, leading, 0x1, shadowColor, 0x7, shape, 0xe, surfaceTintColor, 0x8, systemOverlayStyle, 0x11, title, 0x2, titleSpacing, 0xd, toolbarHeight, 0x10, toolbarOpacity, 0xf, null]
    //     0xd8edd0: add             x4, PP, #0x59, lsl #12  ; [pp+0x59058] List(39) [0, 0x12, 0x11, 0x1, "actions", 0x3, "backgroundColor", 0x9, "bottom", 0x5, "centerTitle", 0xc, "elevation", 0x6, "flexibleSpace", 0x4, "foregroundColor", 0xa, "iconTheme", 0xb, "leading", 0x1, "shadowColor", 0x7, "shape", 0xe, "surfaceTintColor", 0x8, "systemOverlayStyle", 0x11, "title", 0x2, "titleSpacing", 0xd, "toolbarHeight", 0x10, "toolbarOpacity", 0xf, Null]
    //     0xd8edd4: ldr             x4, [x4, #0x58]
    // 0xd8edd8: r0 = AppBar()
    //     0xd8edd8: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xd8eddc: ldur            d0, [fp, #-0x40]
    // 0xd8ede0: r0 = inline_Allocate_Double()
    //     0xd8ede0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xd8ede4: add             x0, x0, #0x10
    //     0xd8ede8: cmp             x1, x0
    //     0xd8edec: b.ls            #0xd8ee74
    //     0xd8edf0: str             x0, [THR, #0x50]  ; THR::top
    //     0xd8edf4: sub             x0, x0, #0xf
    //     0xd8edf8: movz            x1, #0xe15c
    //     0xd8edfc: movk            x1, #0x3, lsl #16
    //     0xd8ee00: stur            x1, [x0, #-1]
    // 0xd8ee04: StoreField: r0->field_7 = d0
    //     0xd8ee04: stur            d0, [x0, #7]
    // 0xd8ee08: ldur            d0, [fp, #-0x48]
    // 0xd8ee0c: r1 = inline_Allocate_Double()
    //     0xd8ee0c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xd8ee10: add             x1, x1, #0x10
    //     0xd8ee14: cmp             x2, x1
    //     0xd8ee18: b.ls            #0xd8ee84
    //     0xd8ee1c: str             x1, [THR, #0x50]  ; THR::top
    //     0xd8ee20: sub             x1, x1, #0xf
    //     0xd8ee24: movz            x2, #0xe15c
    //     0xd8ee28: movk            x2, #0x3, lsl #16
    //     0xd8ee2c: stur            x2, [x1, #-1]
    // 0xd8ee30: StoreField: r1->field_7 = d0
    //     0xd8ee30: stur            d0, [x1, #7]
    // 0xd8ee34: stp             x1, x0, [SP, #0x18]
    // 0xd8ee38: r16 = 1.000000
    //     0xd8ee38: ldr             x16, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xd8ee3c: ldur            lr, [fp, #-0x18]
    // 0xd8ee40: stp             lr, x16, [SP, #8]
    // 0xd8ee44: r16 = true
    //     0xd8ee44: add             x16, NULL, #0x20  ; true
    // 0xd8ee48: str             x16, [SP]
    // 0xd8ee4c: ldur            x1, [fp, #-0x30]
    // 0xd8ee50: ldur            d0, [fp, #-0x38]
    // 0xd8ee54: r4 = const [0, 0x7, 0x5, 0x2, hasLeading, 0x6, isScrolledUnder, 0x5, maxExtent, 0x3, minExtent, 0x2, toolbarOpacity, 0x4, null]
    //     0xd8ee54: add             x4, PP, #0x59, lsl #12  ; [pp+0x59060] List(15) [0, 0x7, 0x5, 0x2, "hasLeading", 0x6, "isScrolledUnder", 0x5, "maxExtent", 0x3, "minExtent", 0x2, "toolbarOpacity", 0x4, Null]
    //     0xd8ee58: ldr             x4, [x4, #0x60]
    // 0xd8ee5c: r0 = createSettings()
    //     0xd8ee5c: bl              #0xa03008  ; [package:flutter/src/material/flexible_space_bar.dart] FlexibleSpaceBar::createSettings
    // 0xd8ee60: LeaveFrame
    //     0xd8ee60: mov             SP, fp
    //     0xd8ee64: ldp             fp, lr, [SP], #0x10
    // 0xd8ee68: ret
    //     0xd8ee68: ret             
    // 0xd8ee6c: r0 = StackOverflowSharedWithFPURegs()
    //     0xd8ee6c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd8ee70: b               #0xd8ec6c
    // 0xd8ee74: SaveReg d0
    //     0xd8ee74: str             q0, [SP, #-0x10]!
    // 0xd8ee78: r0 = AllocateDouble()
    //     0xd8ee78: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd8ee7c: RestoreReg d0
    //     0xd8ee7c: ldr             q0, [SP], #0x10
    // 0xd8ee80: b               #0xd8ee04
    // 0xd8ee84: SaveReg d0
    //     0xd8ee84: str             q0, [SP, #-0x10]!
    // 0xd8ee88: SaveReg r0
    //     0xd8ee88: str             x0, [SP, #-8]!
    // 0xd8ee8c: r0 = AllocateDouble()
    //     0xd8ee8c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd8ee90: mov             x1, x0
    // 0xd8ee94: RestoreReg r0
    //     0xd8ee94: ldr             x0, [SP], #8
    // 0xd8ee98: RestoreReg d0
    //     0xd8ee98: ldr             q0, [SP], #0x10
    // 0xd8ee9c: b               #0xd8ee30
  }
  get _ maxExtent(/* No info */) {
    // ** addr: 0xd904e0, size: 0x7c
    // 0xd904e0: LoadField: d1 = r1->field_5f
    //     0xd904e0: ldur            d1, [x1, #0x5f]
    // 0xd904e4: LoadField: r0 = r1->field_53
    //     0xd904e4: ldur            w0, [x1, #0x53]
    // 0xd904e8: DecompressPointer r0
    //     0xd904e8: add             x0, x0, HEAP, lsl #32
    // 0xd904ec: cmp             w0, NULL
    // 0xd904f0: b.ne            #0xd90500
    // 0xd904f4: d2 = 56.000000
    //     0xd904f4: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0xd904f8: ldr             d2, [x17, #0xf60]
    // 0xd904fc: b               #0xd90504
    // 0xd90500: LoadField: d2 = r0->field_7
    //     0xd90500: ldur            d2, [x0, #7]
    // 0xd90504: fadd            d3, d1, d2
    // 0xd90508: LoadField: d1 = r1->field_57
    //     0xd90508: ldur            d1, [x1, #0x57]
    // 0xd9050c: fcmp            d3, d1
    // 0xd90510: b.le            #0xd9051c
    // 0xd90514: mov             v0.16b, v3.16b
    // 0xd90518: b               #0xd90558
    // 0xd9051c: fcmp            d1, d3
    // 0xd90520: b.le            #0xd9052c
    // 0xd90524: mov             v0.16b, v1.16b
    // 0xd90528: b               #0xd90558
    // 0xd9052c: d2 = 0.000000
    //     0xd9052c: eor             v2.16b, v2.16b, v2.16b
    // 0xd90530: fcmp            d3, d2
    // 0xd90534: b.ne            #0xd90544
    // 0xd90538: fadd            d2, d3, d1
    // 0xd9053c: mov             v0.16b, v2.16b
    // 0xd90540: b               #0xd90558
    // 0xd90544: fcmp            d1, d1
    // 0xd90548: b.vc            #0xd90554
    // 0xd9054c: mov             v0.16b, v1.16b
    // 0xd90550: b               #0xd90558
    // 0xd90554: mov             v0.16b, v3.16b
    // 0xd90558: ret
    //     0xd90558: ret             
  }
}

// class id: 3424, size: 0x14, field offset: 0xc
//   const constructor, 
class _ToolbarContainerLayout extends SingleChildLayoutDelegate {

  _ getPositionForChild(/* No info */) {
    // ** addr: 0xbf6abc, size: 0x38
    // 0xbf6abc: EnterFrame
    //     0xbf6abc: stp             fp, lr, [SP, #-0x10]!
    //     0xbf6ac0: mov             fp, SP
    // 0xbf6ac4: AllocStack(0x8)
    //     0xbf6ac4: sub             SP, SP, #8
    // 0xbf6ac8: LoadField: d0 = r2->field_f
    //     0xbf6ac8: ldur            d0, [x2, #0xf]
    // 0xbf6acc: LoadField: d1 = r3->field_f
    //     0xbf6acc: ldur            d1, [x3, #0xf]
    // 0xbf6ad0: fsub            d2, d0, d1
    // 0xbf6ad4: stur            d2, [fp, #-8]
    // 0xbf6ad8: r0 = Offset()
    //     0xbf6ad8: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xbf6adc: StoreField: r0->field_7 = rZR
    //     0xbf6adc: stur            xzr, [x0, #7]
    // 0xbf6ae0: ldur            d0, [fp, #-8]
    // 0xbf6ae4: StoreField: r0->field_f = d0
    //     0xbf6ae4: stur            d0, [x0, #0xf]
    // 0xbf6ae8: LeaveFrame
    //     0xbf6ae8: mov             SP, fp
    //     0xbf6aec: ldp             fp, lr, [SP], #0x10
    // 0xbf6af0: ret
    //     0xbf6af0: ret             
  }
  _ getConstraintsForChild(/* No info */) {
    // ** addr: 0xbf723c, size: 0x88
    // 0xbf723c: EnterFrame
    //     0xbf723c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf7240: mov             fp, SP
    // 0xbf7244: AllocStack(0x8)
    //     0xbf7244: sub             SP, SP, #8
    // 0xbf7248: SetupParameters(_ToolbarContainerLayout this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0xbf7248: mov             x0, x1
    //     0xbf724c: mov             x1, x2
    // 0xbf7250: CheckStackOverflow
    //     0xbf7250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf7254: cmp             SP, x16
    //     0xbf7258: b.ls            #0xbf72a4
    // 0xbf725c: LoadField: d0 = r0->field_b
    //     0xbf725c: ldur            d0, [x0, #0xb]
    // 0xbf7260: r0 = inline_Allocate_Double()
    //     0xbf7260: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbf7264: add             x0, x0, #0x10
    //     0xbf7268: cmp             x2, x0
    //     0xbf726c: b.ls            #0xbf72ac
    //     0xbf7270: str             x0, [THR, #0x50]  ; THR::top
    //     0xbf7274: sub             x0, x0, #0xf
    //     0xbf7278: movz            x2, #0xe15c
    //     0xbf727c: movk            x2, #0x3, lsl #16
    //     0xbf7280: stur            x2, [x0, #-1]
    // 0xbf7284: StoreField: r0->field_7 = d0
    //     0xbf7284: stur            d0, [x0, #7]
    // 0xbf7288: str             x0, [SP]
    // 0xbf728c: r4 = const [0, 0x2, 0x1, 0x1, height, 0x1, null]
    //     0xbf728c: add             x4, PP, #0x35, lsl #12  ; [pp+0x35538] List(7) [0, 0x2, 0x1, 0x1, "height", 0x1, Null]
    //     0xbf7290: ldr             x4, [x4, #0x538]
    // 0xbf7294: r0 = tighten()
    //     0xbf7294: bl              #0x73bb30  ; [package:flutter/src/rendering/box.dart] BoxConstraints::tighten
    // 0xbf7298: LeaveFrame
    //     0xbf7298: mov             SP, fp
    //     0xbf729c: ldp             fp, lr, [SP], #0x10
    // 0xbf72a0: ret
    //     0xbf72a0: ret             
    // 0xbf72a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf72a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf72a8: b               #0xbf725c
    // 0xbf72ac: SaveReg d0
    //     0xbf72ac: str             q0, [SP, #-0x10]!
    // 0xbf72b0: SaveReg r1
    //     0xbf72b0: str             x1, [SP, #-8]!
    // 0xbf72b4: r0 = AllocateDouble()
    //     0xbf72b4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbf72b8: RestoreReg r1
    //     0xbf72b8: ldr             x1, [SP], #8
    // 0xbf72bc: RestoreReg d0
    //     0xbf72bc: ldr             q0, [SP], #0x10
    // 0xbf72c0: b               #0xbf7284
  }
  _ getSize(/* No info */) {
    // ** addr: 0xbf7364, size: 0x3c
    // 0xbf7364: EnterFrame
    //     0xbf7364: stp             fp, lr, [SP, #-0x10]!
    //     0xbf7368: mov             fp, SP
    // 0xbf736c: AllocStack(0x10)
    //     0xbf736c: sub             SP, SP, #0x10
    // 0xbf7370: LoadField: d0 = r2->field_f
    //     0xbf7370: ldur            d0, [x2, #0xf]
    // 0xbf7374: stur            d0, [fp, #-0x10]
    // 0xbf7378: LoadField: d1 = r1->field_b
    //     0xbf7378: ldur            d1, [x1, #0xb]
    // 0xbf737c: stur            d1, [fp, #-8]
    // 0xbf7380: r0 = Size()
    //     0xbf7380: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0xbf7384: ldur            d0, [fp, #-0x10]
    // 0xbf7388: StoreField: r0->field_7 = d0
    //     0xbf7388: stur            d0, [x0, #7]
    // 0xbf738c: ldur            d0, [fp, #-8]
    // 0xbf7390: StoreField: r0->field_f = d0
    //     0xbf7390: stur            d0, [x0, #0xf]
    // 0xbf7394: LeaveFrame
    //     0xbf7394: mov             SP, fp
    //     0xbf7398: ldp             fp, lr, [SP], #0x10
    // 0xbf739c: ret
    //     0xbf739c: ret             
  }
  _ shouldRelayout(/* No info */) {
    // ** addr: 0xbf88dc, size: 0x80
    // 0xbf88dc: EnterFrame
    //     0xbf88dc: stp             fp, lr, [SP, #-0x10]!
    //     0xbf88e0: mov             fp, SP
    // 0xbf88e4: AllocStack(0x10)
    //     0xbf88e4: sub             SP, SP, #0x10
    // 0xbf88e8: SetupParameters(_ToolbarContainerLayout this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xbf88e8: mov             x0, x2
    //     0xbf88ec: mov             x4, x1
    //     0xbf88f0: mov             x3, x2
    //     0xbf88f4: stur            x1, [fp, #-8]
    //     0xbf88f8: stur            x2, [fp, #-0x10]
    // 0xbf88fc: r2 = Null
    //     0xbf88fc: mov             x2, NULL
    // 0xbf8900: r1 = Null
    //     0xbf8900: mov             x1, NULL
    // 0xbf8904: r4 = 60
    //     0xbf8904: movz            x4, #0x3c
    // 0xbf8908: branchIfSmi(r0, 0xbf8914)
    //     0xbf8908: tbz             w0, #0, #0xbf8914
    // 0xbf890c: r4 = LoadClassIdInstr(r0)
    //     0xbf890c: ldur            x4, [x0, #-1]
    //     0xbf8910: ubfx            x4, x4, #0xc, #0x14
    // 0xbf8914: cmp             x4, #0xd60
    // 0xbf8918: b.eq            #0xbf8930
    // 0xbf891c: r8 = _ToolbarContainerLayout
    //     0xbf891c: add             x8, PP, #0x55, lsl #12  ; [pp+0x55120] Type: _ToolbarContainerLayout
    //     0xbf8920: ldr             x8, [x8, #0x120]
    // 0xbf8924: r3 = Null
    //     0xbf8924: add             x3, PP, #0x55, lsl #12  ; [pp+0x55128] Null
    //     0xbf8928: ldr             x3, [x3, #0x128]
    // 0xbf892c: r0 = DefaultTypeTest()
    //     0xbf892c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xbf8930: ldur            x1, [fp, #-8]
    // 0xbf8934: LoadField: d0 = r1->field_b
    //     0xbf8934: ldur            d0, [x1, #0xb]
    // 0xbf8938: ldur            x1, [fp, #-0x10]
    // 0xbf893c: LoadField: d1 = r1->field_b
    //     0xbf893c: ldur            d1, [x1, #0xb]
    // 0xbf8940: fcmp            d0, d1
    // 0xbf8944: r16 = true
    //     0xbf8944: add             x16, NULL, #0x20  ; true
    // 0xbf8948: r17 = false
    //     0xbf8948: add             x17, NULL, #0x30  ; false
    // 0xbf894c: csel            x0, x16, x17, ne
    // 0xbf8950: LeaveFrame
    //     0xbf8950: mov             SP, fp
    //     0xbf8954: ldp             fp, lr, [SP], #0x10
    // 0xbf8958: ret
    //     0xbf8958: ret             
  }
}

// class id: 4002, size: 0x54, field offset: 0x44
class _AppBarDefaultsM3 extends AppBarTheme {

  late final ColorScheme _colors; // offset: 0x4c
  late final TextTheme _textTheme; // offset: 0x50
  late final ThemeData _theme; // offset: 0x48

  TextTheme _textTheme(_AppBarDefaultsM3) {
    // ** addr: 0x9e87b8, size: 0x58
    // 0x9e87b8: EnterFrame
    //     0x9e87b8: stp             fp, lr, [SP, #-0x10]!
    //     0x9e87bc: mov             fp, SP
    // 0x9e87c0: CheckStackOverflow
    //     0x9e87c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e87c4: cmp             SP, x16
    //     0x9e87c8: b.ls            #0x9e8808
    // 0x9e87cc: ldr             x1, [fp, #0x10]
    // 0x9e87d0: LoadField: r0 = r1->field_47
    //     0x9e87d0: ldur            w0, [x1, #0x47]
    // 0x9e87d4: DecompressPointer r0
    //     0x9e87d4: add             x0, x0, HEAP, lsl #32
    // 0x9e87d8: r16 = Sentinel
    //     0x9e87d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e87dc: cmp             w0, w16
    // 0x9e87e0: b.ne            #0x9e87f0
    // 0x9e87e4: r2 = _theme
    //     0x9e87e4: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4f8] Field <_AppBarDefaultsM3@500187611._theme@500187611>: late final (offset: 0x48)
    //     0x9e87e8: ldr             x2, [x2, #0x4f8]
    // 0x9e87ec: r0 = InitLateFinalInstanceField()
    //     0x9e87ec: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e87f0: LoadField: r1 = r0->field_8f
    //     0x9e87f0: ldur            w1, [x0, #0x8f]
    // 0x9e87f4: DecompressPointer r1
    //     0x9e87f4: add             x1, x1, HEAP, lsl #32
    // 0x9e87f8: mov             x0, x1
    // 0x9e87fc: LeaveFrame
    //     0x9e87fc: mov             SP, fp
    //     0x9e8800: ldp             fp, lr, [SP], #0x10
    // 0x9e8804: ret
    //     0x9e8804: ret             
    // 0x9e8808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e8808: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e880c: b               #0x9e87cc
  }
  ColorScheme _colors(_AppBarDefaultsM3) {
    // ** addr: 0x9e88a0, size: 0x58
    // 0x9e88a0: EnterFrame
    //     0x9e88a0: stp             fp, lr, [SP, #-0x10]!
    //     0x9e88a4: mov             fp, SP
    // 0x9e88a8: CheckStackOverflow
    //     0x9e88a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e88ac: cmp             SP, x16
    //     0x9e88b0: b.ls            #0x9e88f0
    // 0x9e88b4: ldr             x1, [fp, #0x10]
    // 0x9e88b8: LoadField: r0 = r1->field_47
    //     0x9e88b8: ldur            w0, [x1, #0x47]
    // 0x9e88bc: DecompressPointer r0
    //     0x9e88bc: add             x0, x0, HEAP, lsl #32
    // 0x9e88c0: r16 = Sentinel
    //     0x9e88c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e88c4: cmp             w0, w16
    // 0x9e88c8: b.ne            #0x9e88d8
    // 0x9e88cc: r2 = _theme
    //     0x9e88cc: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4f8] Field <_AppBarDefaultsM3@500187611._theme@500187611>: late final (offset: 0x48)
    //     0x9e88d0: ldr             x2, [x2, #0x4f8]
    // 0x9e88d4: r0 = InitLateFinalInstanceField()
    //     0x9e88d4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e88d8: LoadField: r1 = r0->field_3f
    //     0x9e88d8: ldur            w1, [x0, #0x3f]
    // 0x9e88dc: DecompressPointer r1
    //     0x9e88dc: add             x1, x1, HEAP, lsl #32
    // 0x9e88e0: mov             x0, x1
    // 0x9e88e4: LeaveFrame
    //     0x9e88e4: mov             SP, fp
    //     0x9e88e8: ldp             fp, lr, [SP], #0x10
    // 0x9e88ec: ret
    //     0x9e88ec: ret             
    // 0x9e88f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e88f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e88f4: b               #0x9e88b4
  }
}

// class id: 4003, size: 0x50, field offset: 0x44
class _AppBarDefaultsM2 extends AppBarTheme {

  late final ColorScheme _colors; // offset: 0x4c
  late final ThemeData _theme; // offset: 0x48

  ThemeData _theme(_AppBarDefaultsM2) {
    // ** addr: 0x9e8810, size: 0x38
    // 0x9e8810: EnterFrame
    //     0x9e8810: stp             fp, lr, [SP, #-0x10]!
    //     0x9e8814: mov             fp, SP
    // 0x9e8818: CheckStackOverflow
    //     0x9e8818: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e881c: cmp             SP, x16
    //     0x9e8820: b.ls            #0x9e8840
    // 0x9e8824: ldr             x0, [fp, #0x10]
    // 0x9e8828: LoadField: r1 = r0->field_43
    //     0x9e8828: ldur            w1, [x0, #0x43]
    // 0x9e882c: DecompressPointer r1
    //     0x9e882c: add             x1, x1, HEAP, lsl #32
    // 0x9e8830: r0 = of()
    //     0x9e8830: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9e8834: LeaveFrame
    //     0x9e8834: mov             SP, fp
    //     0x9e8838: ldp             fp, lr, [SP], #0x10
    // 0x9e883c: ret
    //     0x9e883c: ret             
    // 0x9e8840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e8840: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e8844: b               #0x9e8824
  }
  ColorScheme _colors(_AppBarDefaultsM2) {
    // ** addr: 0x9e8848, size: 0x58
    // 0x9e8848: EnterFrame
    //     0x9e8848: stp             fp, lr, [SP, #-0x10]!
    //     0x9e884c: mov             fp, SP
    // 0x9e8850: CheckStackOverflow
    //     0x9e8850: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e8854: cmp             SP, x16
    //     0x9e8858: b.ls            #0x9e8898
    // 0x9e885c: ldr             x1, [fp, #0x10]
    // 0x9e8860: LoadField: r0 = r1->field_47
    //     0x9e8860: ldur            w0, [x1, #0x47]
    // 0x9e8864: DecompressPointer r0
    //     0x9e8864: add             x0, x0, HEAP, lsl #32
    // 0x9e8868: r16 = Sentinel
    //     0x9e8868: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e886c: cmp             w0, w16
    // 0x9e8870: b.ne            #0x9e8880
    // 0x9e8874: r2 = _theme
    //     0x9e8874: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4e8] Field <_AppBarDefaultsM2@500187611._theme@500187611>: late final (offset: 0x48)
    //     0x9e8878: ldr             x2, [x2, #0x4e8]
    // 0x9e887c: r0 = InitLateFinalInstanceField()
    //     0x9e887c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e8880: LoadField: r1 = r0->field_3f
    //     0x9e8880: ldur            w1, [x0, #0x3f]
    // 0x9e8884: DecompressPointer r1
    //     0x9e8884: add             x1, x1, HEAP, lsl #32
    // 0x9e8888: mov             x0, x1
    // 0x9e888c: LeaveFrame
    //     0x9e888c: mov             SP, fp
    //     0x9e8890: ldp             fp, lr, [SP], #0x10
    // 0x9e8894: ret
    //     0x9e8894: ret             
    // 0x9e8898: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e8898: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e889c: b               #0x9e885c
  }
}

// class id: 4328, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __SliverAppBarState&State&TickerProviderStateMixin extends State<dynamic>
     with TickerProviderStateMixin<X0 bound StatefulWidget> {

  _ dispose(/* No info */) {
    // ** addr: 0xa7cb48, size: 0x94
    // 0xa7cb48: EnterFrame
    //     0xa7cb48: stp             fp, lr, [SP, #-0x10]!
    //     0xa7cb4c: mov             fp, SP
    // 0xa7cb50: AllocStack(0x10)
    //     0xa7cb50: sub             SP, SP, #0x10
    // 0xa7cb54: SetupParameters(__SliverAppBarState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa7cb54: mov             x0, x1
    //     0xa7cb58: stur            x1, [fp, #-0x10]
    // 0xa7cb5c: CheckStackOverflow
    //     0xa7cb5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7cb60: cmp             SP, x16
    //     0xa7cb64: b.ls            #0xa7cbd4
    // 0xa7cb68: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa7cb68: ldur            w3, [x0, #0x17]
    // 0xa7cb6c: DecompressPointer r3
    //     0xa7cb6c: add             x3, x3, HEAP, lsl #32
    // 0xa7cb70: stur            x3, [fp, #-8]
    // 0xa7cb74: cmp             w3, NULL
    // 0xa7cb78: b.ne            #0xa7cb84
    // 0xa7cb7c: mov             x1, x0
    // 0xa7cb80: b               #0xa7cbc0
    // 0xa7cb84: mov             x2, x0
    // 0xa7cb88: r1 = Function '_updateTickers@364311458':.
    //     0xa7cb88: add             x1, PP, #0x44, lsl #12  ; [pp+0x44858] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa7cb8c: ldr             x1, [x1, #0x858]
    // 0xa7cb90: r0 = AllocateClosure()
    //     0xa7cb90: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7cb94: ldur            x1, [fp, #-8]
    // 0xa7cb98: r2 = LoadClassIdInstr(r1)
    //     0xa7cb98: ldur            x2, [x1, #-1]
    //     0xa7cb9c: ubfx            x2, x2, #0xc, #0x14
    // 0xa7cba0: mov             x16, x0
    // 0xa7cba4: mov             x0, x2
    // 0xa7cba8: mov             x2, x16
    // 0xa7cbac: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa7cbac: movz            x17, #0xbf5c
    //     0xa7cbb0: add             lr, x0, x17
    //     0xa7cbb4: ldr             lr, [x21, lr, lsl #3]
    //     0xa7cbb8: blr             lr
    // 0xa7cbbc: ldur            x1, [fp, #-0x10]
    // 0xa7cbc0: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa7cbc0: stur            NULL, [x1, #0x17]
    // 0xa7cbc4: r0 = Null
    //     0xa7cbc4: mov             x0, NULL
    // 0xa7cbc8: LeaveFrame
    //     0xa7cbc8: mov             SP, fp
    //     0xa7cbcc: ldp             fp, lr, [SP], #0x10
    // 0xa7cbd0: ret
    //     0xa7cbd0: ret             
    // 0xa7cbd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7cbd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7cbd8: b               #0xa7cb68
  }
  _ activate(/* No info */) {
    // ** addr: 0xa84c58, size: 0x30
    // 0xa84c58: EnterFrame
    //     0xa84c58: stp             fp, lr, [SP, #-0x10]!
    //     0xa84c5c: mov             fp, SP
    // 0xa84c60: CheckStackOverflow
    //     0xa84c60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa84c64: cmp             SP, x16
    //     0xa84c68: b.ls            #0xa84c80
    // 0xa84c6c: r0 = _updateTickerModeNotifier()
    //     0xa84c6c: bl              #0xa84c88  ; [package:flutter/src/material/app_bar.dart] __SliverAppBarState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa84c70: r0 = Null
    //     0xa84c70: mov             x0, NULL
    // 0xa84c74: LeaveFrame
    //     0xa84c74: mov             SP, fp
    //     0xa84c78: ldp             fp, lr, [SP], #0x10
    // 0xa84c7c: ret
    //     0xa84c7c: ret             
    // 0xa84c80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa84c80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa84c84: b               #0xa84c6c
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0xa84c88, size: 0x124
    // 0xa84c88: EnterFrame
    //     0xa84c88: stp             fp, lr, [SP, #-0x10]!
    //     0xa84c8c: mov             fp, SP
    // 0xa84c90: AllocStack(0x18)
    //     0xa84c90: sub             SP, SP, #0x18
    // 0xa84c94: SetupParameters(__SliverAppBarState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0xa84c94: mov             x2, x1
    //     0xa84c98: stur            x1, [fp, #-8]
    // 0xa84c9c: CheckStackOverflow
    //     0xa84c9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa84ca0: cmp             SP, x16
    //     0xa84ca4: b.ls            #0xa84da0
    // 0xa84ca8: LoadField: r1 = r2->field_f
    //     0xa84ca8: ldur            w1, [x2, #0xf]
    // 0xa84cac: DecompressPointer r1
    //     0xa84cac: add             x1, x1, HEAP, lsl #32
    // 0xa84cb0: cmp             w1, NULL
    // 0xa84cb4: b.eq            #0xa84da8
    // 0xa84cb8: r0 = getNotifier()
    //     0xa84cb8: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0xa84cbc: mov             x3, x0
    // 0xa84cc0: ldur            x0, [fp, #-8]
    // 0xa84cc4: stur            x3, [fp, #-0x18]
    // 0xa84cc8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa84cc8: ldur            w4, [x0, #0x17]
    // 0xa84ccc: DecompressPointer r4
    //     0xa84ccc: add             x4, x4, HEAP, lsl #32
    // 0xa84cd0: stur            x4, [fp, #-0x10]
    // 0xa84cd4: cmp             w3, w4
    // 0xa84cd8: b.ne            #0xa84cec
    // 0xa84cdc: r0 = Null
    //     0xa84cdc: mov             x0, NULL
    // 0xa84ce0: LeaveFrame
    //     0xa84ce0: mov             SP, fp
    //     0xa84ce4: ldp             fp, lr, [SP], #0x10
    // 0xa84ce8: ret
    //     0xa84ce8: ret             
    // 0xa84cec: cmp             w4, NULL
    // 0xa84cf0: b.eq            #0xa84d34
    // 0xa84cf4: mov             x2, x0
    // 0xa84cf8: r1 = Function '_updateTickers@364311458':.
    //     0xa84cf8: add             x1, PP, #0x44, lsl #12  ; [pp+0x44858] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa84cfc: ldr             x1, [x1, #0x858]
    // 0xa84d00: r0 = AllocateClosure()
    //     0xa84d00: bl              #0xec1630  ; AllocateClosureStub
    // 0xa84d04: ldur            x1, [fp, #-0x10]
    // 0xa84d08: r2 = LoadClassIdInstr(r1)
    //     0xa84d08: ldur            x2, [x1, #-1]
    //     0xa84d0c: ubfx            x2, x2, #0xc, #0x14
    // 0xa84d10: mov             x16, x0
    // 0xa84d14: mov             x0, x2
    // 0xa84d18: mov             x2, x16
    // 0xa84d1c: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa84d1c: movz            x17, #0xbf5c
    //     0xa84d20: add             lr, x0, x17
    //     0xa84d24: ldr             lr, [x21, lr, lsl #3]
    //     0xa84d28: blr             lr
    // 0xa84d2c: ldur            x0, [fp, #-8]
    // 0xa84d30: ldur            x3, [fp, #-0x18]
    // 0xa84d34: mov             x2, x0
    // 0xa84d38: r1 = Function '_updateTickers@364311458':.
    //     0xa84d38: add             x1, PP, #0x44, lsl #12  ; [pp+0x44858] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa84d3c: ldr             x1, [x1, #0x858]
    // 0xa84d40: r0 = AllocateClosure()
    //     0xa84d40: bl              #0xec1630  ; AllocateClosureStub
    // 0xa84d44: ldur            x3, [fp, #-0x18]
    // 0xa84d48: r1 = LoadClassIdInstr(r3)
    //     0xa84d48: ldur            x1, [x3, #-1]
    //     0xa84d4c: ubfx            x1, x1, #0xc, #0x14
    // 0xa84d50: mov             x2, x0
    // 0xa84d54: mov             x0, x1
    // 0xa84d58: mov             x1, x3
    // 0xa84d5c: r0 = GDT[cid_x0 + 0xc407]()
    //     0xa84d5c: movz            x17, #0xc407
    //     0xa84d60: add             lr, x0, x17
    //     0xa84d64: ldr             lr, [x21, lr, lsl #3]
    //     0xa84d68: blr             lr
    // 0xa84d6c: ldur            x0, [fp, #-0x18]
    // 0xa84d70: ldur            x1, [fp, #-8]
    // 0xa84d74: ArrayStore: r1[0] = r0  ; List_4
    //     0xa84d74: stur            w0, [x1, #0x17]
    //     0xa84d78: ldurb           w16, [x1, #-1]
    //     0xa84d7c: ldurb           w17, [x0, #-1]
    //     0xa84d80: and             x16, x17, x16, lsr #2
    //     0xa84d84: tst             x16, HEAP, lsr #32
    //     0xa84d88: b.eq            #0xa84d90
    //     0xa84d8c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa84d90: r0 = Null
    //     0xa84d90: mov             x0, NULL
    // 0xa84d94: LeaveFrame
    //     0xa84d94: mov             SP, fp
    //     0xa84d98: ldp             fp, lr, [SP], #0x10
    // 0xa84d9c: ret
    //     0xa84d9c: ret             
    // 0xa84da0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa84da0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa84da4: b               #0xa84ca8
    // 0xa84da8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa84da8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4329, size: 0x28, field offset: 0x1c
class _SliverAppBarState extends __SliverAppBarState&State&TickerProviderStateMixin {

  _ initState(/* No info */) {
    // ** addr: 0x9321e4, size: 0x48
    // 0x9321e4: EnterFrame
    //     0x9321e4: stp             fp, lr, [SP, #-0x10]!
    //     0x9321e8: mov             fp, SP
    // 0x9321ec: AllocStack(0x8)
    //     0x9321ec: sub             SP, SP, #8
    // 0x9321f0: SetupParameters(_SliverAppBarState this /* r1 => r0, fp-0x8 */)
    //     0x9321f0: mov             x0, x1
    //     0x9321f4: stur            x1, [fp, #-8]
    // 0x9321f8: CheckStackOverflow
    //     0x9321f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9321fc: cmp             SP, x16
    //     0x932200: b.ls            #0x932224
    // 0x932204: mov             x1, x0
    // 0x932208: r0 = _updateSnapConfiguration()
    //     0x932208: bl              #0x932278  ; [package:flutter/src/material/app_bar.dart] _SliverAppBarState::_updateSnapConfiguration
    // 0x93220c: ldur            x1, [fp, #-8]
    // 0x932210: r0 = _updateStretchConfiguration()
    //     0x932210: bl              #0x932250  ; [package:flutter/src/material/app_bar.dart] _SliverAppBarState::_updateStretchConfiguration
    // 0x932214: r0 = Null
    //     0x932214: mov             x0, NULL
    // 0x932218: LeaveFrame
    //     0x932218: mov             SP, fp
    //     0x93221c: ldp             fp, lr, [SP], #0x10
    // 0x932220: ret
    //     0x932220: ret             
    // 0x932224: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x932224: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x932228: b               #0x932204
  }
  _ _updateStretchConfiguration(/* No info */) {
    // ** addr: 0x932250, size: 0x28
    // 0x932250: LoadField: r2 = r1->field_b
    //     0x932250: ldur            w2, [x1, #0xb]
    // 0x932254: DecompressPointer r2
    //     0x932254: add             x2, x2, HEAP, lsl #32
    // 0x932258: cmp             w2, NULL
    // 0x93225c: b.eq            #0x93226c
    // 0x932260: StoreField: r1->field_1f = rNULL
    //     0x932260: stur            NULL, [x1, #0x1f]
    // 0x932264: r0 = Null
    //     0x932264: mov             x0, NULL
    // 0x932268: ret
    //     0x932268: ret             
    // 0x93226c: EnterFrame
    //     0x93226c: stp             fp, lr, [SP, #-0x10]!
    //     0x932270: mov             fp, SP
    // 0x932274: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x932274: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateSnapConfiguration(/* No info */) {
    // ** addr: 0x932278, size: 0x2c
    // 0x932278: LoadField: r2 = r1->field_b
    //     0x932278: ldur            w2, [x1, #0xb]
    // 0x93227c: DecompressPointer r2
    //     0x93227c: add             x2, x2, HEAP, lsl #32
    // 0x932280: cmp             w2, NULL
    // 0x932284: b.eq            #0x932298
    // 0x932288: StoreField: r1->field_1b = rNULL
    //     0x932288: stur            NULL, [x1, #0x1b]
    // 0x93228c: StoreField: r1->field_23 = rNULL
    //     0x93228c: stur            NULL, [x1, #0x23]
    // 0x932290: r0 = Null
    //     0x932290: mov             x0, NULL
    // 0x932294: ret
    //     0x932294: ret             
    // 0x932298: EnterFrame
    //     0x932298: stp             fp, lr, [SP, #-0x10]!
    //     0x93229c: mov             fp, SP
    // 0x9322a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9322a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x983f90, size: 0xbc
    // 0x983f90: EnterFrame
    //     0x983f90: stp             fp, lr, [SP, #-0x10]!
    //     0x983f94: mov             fp, SP
    // 0x983f98: AllocStack(0x10)
    //     0x983f98: sub             SP, SP, #0x10
    // 0x983f9c: SetupParameters(_SliverAppBarState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x983f9c: mov             x0, x2
    //     0x983fa0: mov             x4, x1
    //     0x983fa4: mov             x3, x2
    //     0x983fa8: stur            x1, [fp, #-8]
    //     0x983fac: stur            x2, [fp, #-0x10]
    // 0x983fb0: r2 = Null
    //     0x983fb0: mov             x2, NULL
    // 0x983fb4: r1 = Null
    //     0x983fb4: mov             x1, NULL
    // 0x983fb8: r4 = 60
    //     0x983fb8: movz            x4, #0x3c
    // 0x983fbc: branchIfSmi(r0, 0x983fc8)
    //     0x983fbc: tbz             w0, #0, #0x983fc8
    // 0x983fc0: r4 = LoadClassIdInstr(r0)
    //     0x983fc0: ldur            x4, [x0, #-1]
    //     0x983fc4: ubfx            x4, x4, #0xc, #0x14
    // 0x983fc8: r17 = 4878
    //     0x983fc8: movz            x17, #0x130e
    // 0x983fcc: cmp             x4, x17
    // 0x983fd0: b.eq            #0x983fe8
    // 0x983fd4: r8 = SliverAppBar
    //     0x983fd4: add             x8, PP, #0x44, lsl #12  ; [pp+0x44830] Type: SliverAppBar
    //     0x983fd8: ldr             x8, [x8, #0x830]
    // 0x983fdc: r3 = Null
    //     0x983fdc: add             x3, PP, #0x44, lsl #12  ; [pp+0x44838] Null
    //     0x983fe0: ldr             x3, [x3, #0x838]
    // 0x983fe4: r0 = SliverAppBar()
    //     0x983fe4: bl              #0x93222c  ; IsType_SliverAppBar_Stub
    // 0x983fe8: ldur            x3, [fp, #-8]
    // 0x983fec: LoadField: r2 = r3->field_7
    //     0x983fec: ldur            w2, [x3, #7]
    // 0x983ff0: DecompressPointer r2
    //     0x983ff0: add             x2, x2, HEAP, lsl #32
    // 0x983ff4: ldur            x0, [fp, #-0x10]
    // 0x983ff8: r1 = Null
    //     0x983ff8: mov             x1, NULL
    // 0x983ffc: cmp             w2, NULL
    // 0x984000: b.eq            #0x984024
    // 0x984004: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x984004: ldur            w4, [x2, #0x17]
    // 0x984008: DecompressPointer r4
    //     0x984008: add             x4, x4, HEAP, lsl #32
    // 0x98400c: r8 = X0 bound StatefulWidget
    //     0x98400c: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x984010: ldr             x8, [x8, #0x7f8]
    // 0x984014: LoadField: r9 = r4->field_7
    //     0x984014: ldur            x9, [x4, #7]
    // 0x984018: r3 = Null
    //     0x984018: add             x3, PP, #0x44, lsl #12  ; [pp+0x44848] Null
    //     0x98401c: ldr             x3, [x3, #0x848]
    // 0x984020: blr             x9
    // 0x984024: ldur            x1, [fp, #-8]
    // 0x984028: LoadField: r2 = r1->field_b
    //     0x984028: ldur            w2, [x1, #0xb]
    // 0x98402c: DecompressPointer r2
    //     0x98402c: add             x2, x2, HEAP, lsl #32
    // 0x984030: cmp             w2, NULL
    // 0x984034: b.eq            #0x984048
    // 0x984038: r0 = Null
    //     0x984038: mov             x0, NULL
    // 0x98403c: LeaveFrame
    //     0x98403c: mov             SP, fp
    //     0x984040: ldp             fp, lr, [SP], #0x10
    // 0x984044: ret
    //     0x984044: ret             
    // 0x984048: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x984048: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0x9e88f8, size: 0x1a8
    // 0x9e88f8: EnterFrame
    //     0x9e88f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9e88fc: mov             fp, SP
    // 0x9e8900: AllocStack(0x50)
    //     0x9e8900: sub             SP, SP, #0x50
    // 0x9e8904: SetupParameters(_SliverAppBarState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9e8904: mov             x0, x2
    //     0x9e8908: stur            x2, [fp, #-0x10]
    //     0x9e890c: mov             x2, x1
    //     0x9e8910: stur            x1, [fp, #-8]
    // 0x9e8914: CheckStackOverflow
    //     0x9e8914: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e8918: cmp             SP, x16
    //     0x9e891c: b.ls            #0x9e8a90
    // 0x9e8920: LoadField: r1 = r2->field_b
    //     0x9e8920: ldur            w1, [x2, #0xb]
    // 0x9e8924: DecompressPointer r1
    //     0x9e8924: add             x1, x1, HEAP, lsl #32
    // 0x9e8928: cmp             w1, NULL
    // 0x9e892c: b.eq            #0x9e8a98
    // 0x9e8930: mov             x1, x0
    // 0x9e8934: r0 = paddingOf()
    //     0x9e8934: bl              #0x9daabc  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::paddingOf
    // 0x9e8938: LoadField: d1 = r0->field_f
    //     0x9e8938: ldur            d1, [x0, #0xf]
    // 0x9e893c: ldur            x0, [fp, #-8]
    // 0x9e8940: stur            d1, [fp, #-0x48]
    // 0x9e8944: LoadField: r1 = r0->field_b
    //     0x9e8944: ldur            w1, [x0, #0xb]
    // 0x9e8948: DecompressPointer r1
    //     0x9e8948: add             x1, x1, HEAP, lsl #32
    // 0x9e894c: cmp             w1, NULL
    // 0x9e8950: b.eq            #0x9e8a9c
    // 0x9e8954: d0 = 56.000000
    //     0x9e8954: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0x9e8958: ldr             d0, [x17, #0xf60]
    // 0x9e895c: fadd            d2, d1, d0
    // 0x9e8960: LoadField: r2 = r1->field_9b
    //     0x9e8960: ldur            w2, [x1, #0x9b]
    // 0x9e8964: DecompressPointer r2
    //     0x9e8964: add             x2, x2, HEAP, lsl #32
    // 0x9e8968: LoadField: r3 = r2->field_7
    //     0x9e8968: ldur            x3, [x2, #7]
    // 0x9e896c: cmp             x3, #1
    // 0x9e8970: b.gt            #0x9e89a8
    // 0x9e8974: cmp             x3, #0
    // 0x9e8978: b.gt            #0x9e8990
    // 0x9e897c: LoadField: r2 = r1->field_1b
    //     0x9e897c: ldur            w2, [x1, #0x1b]
    // 0x9e8980: DecompressPointer r2
    //     0x9e8980: add             x2, x2, HEAP, lsl #32
    // 0x9e8984: mov             x6, x2
    // 0x9e8988: mov             v0.16b, v2.16b
    // 0x9e898c: b               #0x9e89bc
    // 0x9e8990: LoadField: r2 = r1->field_1b
    //     0x9e8990: ldur            w2, [x1, #0x1b]
    // 0x9e8994: DecompressPointer r2
    //     0x9e8994: add             x2, x2, HEAP, lsl #32
    // 0x9e8998: mov             x6, x2
    // 0x9e899c: d0 = 56.000000
    //     0x9e899c: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0x9e89a0: ldr             d0, [x17, #0xf60]
    // 0x9e89a4: b               #0x9e89bc
    // 0x9e89a8: LoadField: r2 = r1->field_1b
    //     0x9e89a8: ldur            w2, [x1, #0x1b]
    // 0x9e89ac: DecompressPointer r2
    //     0x9e89ac: add             x2, x2, HEAP, lsl #32
    // 0x9e89b0: mov             x6, x2
    // 0x9e89b4: d0 = 56.000000
    //     0x9e89b4: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0x9e89b8: ldr             d0, [x17, #0xf60]
    // 0x9e89bc: stur            x6, [fp, #-0x28]
    // 0x9e89c0: stur            d0, [fp, #-0x40]
    // 0x9e89c4: LoadField: r7 = r1->field_13
    //     0x9e89c4: ldur            w7, [x1, #0x13]
    // 0x9e89c8: DecompressPointer r7
    //     0x9e89c8: add             x7, x7, HEAP, lsl #32
    // 0x9e89cc: stur            x7, [fp, #-0x20]
    // 0x9e89d0: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x9e89d0: ldur            w3, [x1, #0x17]
    // 0x9e89d4: DecompressPointer r3
    //     0x9e89d4: add             x3, x3, HEAP, lsl #32
    // 0x9e89d8: ldur            x1, [fp, #-0x10]
    // 0x9e89dc: stur            x3, [fp, #-0x18]
    // 0x9e89e0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9e89e0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9e89e4: r0 = _of()
    //     0x9e89e4: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x9e89e8: LoadField: r2 = r0->field_37
    //     0x9e89e8: ldur            w2, [x0, #0x37]
    // 0x9e89ec: DecompressPointer r2
    //     0x9e89ec: add             x2, x2, HEAP, lsl #32
    // 0x9e89f0: stur            x2, [fp, #-0x30]
    // 0x9e89f4: r0 = _SliverAppBarDelegate()
    //     0x9e89f4: bl              #0x9e92bc  ; Allocate_SliverAppBarDelegateStub -> _SliverAppBarDelegate (size=0xb4)
    // 0x9e89f8: stur            x0, [fp, #-0x38]
    // 0x9e89fc: ldur            x16, [fp, #-8]
    // 0x9e8a00: str             x16, [SP]
    // 0x9e8a04: mov             x1, x0
    // 0x9e8a08: ldur            x2, [fp, #-0x30]
    // 0x9e8a0c: ldur            x3, [fp, #-0x18]
    // 0x9e8a10: ldur            d0, [fp, #-0x40]
    // 0x9e8a14: ldur            x6, [fp, #-0x28]
    // 0x9e8a18: ldur            x7, [fp, #-0x20]
    // 0x9e8a1c: ldur            d1, [fp, #-0x48]
    // 0x9e8a20: r5 = 180.000000
    //     0x9e8a20: add             x5, PP, #0x44, lsl #12  ; [pp+0x44828] 180
    //     0x9e8a24: ldr             x5, [x5, #0x828]
    // 0x9e8a28: r0 = _SliverAppBarDelegate()
    //     0x9e8a28: bl              #0x9e9198  ; [package:flutter/src/material/app_bar.dart] _SliverAppBarDelegate::_SliverAppBarDelegate
    // 0x9e8a2c: r0 = SliverPersistentHeader()
    //     0x9e8a2c: bl              #0x9e918c  ; AllocateSliverPersistentHeaderStub -> SliverPersistentHeader (size=0x18)
    // 0x9e8a30: mov             x2, x0
    // 0x9e8a34: ldur            x0, [fp, #-0x38]
    // 0x9e8a38: stur            x2, [fp, #-8]
    // 0x9e8a3c: StoreField: r2->field_b = r0
    //     0x9e8a3c: stur            w0, [x2, #0xb]
    // 0x9e8a40: r0 = true
    //     0x9e8a40: add             x0, NULL, #0x20  ; true
    // 0x9e8a44: StoreField: r2->field_f = r0
    //     0x9e8a44: stur            w0, [x2, #0xf]
    // 0x9e8a48: r0 = false
    //     0x9e8a48: add             x0, NULL, #0x30  ; false
    // 0x9e8a4c: StoreField: r2->field_13 = r0
    //     0x9e8a4c: stur            w0, [x2, #0x13]
    // 0x9e8a50: r1 = <_MediaQueryAspect>
    //     0x9e8a50: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d80] TypeArguments: <_MediaQueryAspect>
    //     0x9e8a54: ldr             x1, [x1, #0xd80]
    // 0x9e8a58: r0 = MediaQuery()
    //     0x9e8a58: bl              #0x9e6f0c  ; AllocateMediaQueryStub -> MediaQuery (size=0x18)
    // 0x9e8a5c: stur            x0, [fp, #-0x18]
    // 0x9e8a60: r16 = true
    //     0x9e8a60: add             x16, NULL, #0x20  ; true
    // 0x9e8a64: str             x16, [SP]
    // 0x9e8a68: mov             x1, x0
    // 0x9e8a6c: ldur            x2, [fp, #-8]
    // 0x9e8a70: ldur            x3, [fp, #-0x10]
    // 0x9e8a74: r4 = const [0, 0x4, 0x1, 0x3, removeBottom, 0x3, null]
    //     0x9e8a74: add             x4, PP, #0x44, lsl #12  ; [pp+0x446d0] List(7) [0, 0x4, 0x1, 0x3, "removeBottom", 0x3, Null]
    //     0x9e8a78: ldr             x4, [x4, #0x6d0]
    // 0x9e8a7c: r0 = MediaQuery.removePadding()
    //     0x9e8a7c: bl              #0x9e8aa0  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::MediaQuery.removePadding
    // 0x9e8a80: ldur            x0, [fp, #-0x18]
    // 0x9e8a84: LeaveFrame
    //     0x9e8a84: mov             SP, fp
    //     0x9e8a88: ldp             fp, lr, [SP], #0x10
    // 0x9e8a8c: ret
    //     0x9e8a8c: ret             
    // 0x9e8a90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e8a90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e8a94: b               #0x9e8920
    // 0x9e8a98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e8a98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e8a9c: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9e8a9c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
}

// class id: 4330, size: 0x1c, field offset: 0x14
class _AppBarState extends State<dynamic> {

  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9a3198, size: 0x124
    // 0x9a3198: EnterFrame
    //     0x9a3198: stp             fp, lr, [SP, #-0x10]!
    //     0x9a319c: mov             fp, SP
    // 0x9a31a0: AllocStack(0x10)
    //     0x9a31a0: sub             SP, SP, #0x10
    // 0x9a31a4: SetupParameters(_AppBarState this /* r1 => r0, fp-0x10 */)
    //     0x9a31a4: mov             x0, x1
    //     0x9a31a8: stur            x1, [fp, #-0x10]
    // 0x9a31ac: CheckStackOverflow
    //     0x9a31ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a31b0: cmp             SP, x16
    //     0x9a31b4: b.ls            #0x9a32ac
    // 0x9a31b8: LoadField: r3 = r0->field_13
    //     0x9a31b8: ldur            w3, [x0, #0x13]
    // 0x9a31bc: DecompressPointer r3
    //     0x9a31bc: add             x3, x3, HEAP, lsl #32
    // 0x9a31c0: stur            x3, [fp, #-8]
    // 0x9a31c4: cmp             w3, NULL
    // 0x9a31c8: b.ne            #0x9a31d4
    // 0x9a31cc: mov             x2, x0
    // 0x9a31d0: b               #0x9a31f4
    // 0x9a31d4: mov             x2, x0
    // 0x9a31d8: r1 = Function '_handleScrollNotification@500187611':.
    //     0x9a31d8: add             x1, PP, #0x44, lsl #12  ; [pp+0x447f8] AnonymousClosure: (0x9a33f8), in [package:flutter/src/material/app_bar.dart] _AppBarState::_handleScrollNotification (0x9a3434)
    //     0x9a31dc: ldr             x1, [x1, #0x7f8]
    // 0x9a31e0: r0 = AllocateClosure()
    //     0x9a31e0: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a31e4: ldur            x1, [fp, #-8]
    // 0x9a31e8: mov             x2, x0
    // 0x9a31ec: r0 = removeListener()
    //     0x9a31ec: bl              #0x6a3f10  ; [package:flutter/src/widgets/scroll_notification_observer.dart] ScrollNotificationObserverState::removeListener
    // 0x9a31f0: ldur            x2, [fp, #-0x10]
    // 0x9a31f4: LoadField: r1 = r2->field_f
    //     0x9a31f4: ldur            w1, [x2, #0xf]
    // 0x9a31f8: DecompressPointer r1
    //     0x9a31f8: add             x1, x1, HEAP, lsl #32
    // 0x9a31fc: cmp             w1, NULL
    // 0x9a3200: b.eq            #0x9a32b4
    // 0x9a3204: r0 = maybeOf()
    //     0x9a3204: bl              #0x9a33b8  ; [package:flutter/src/material/scaffold.dart] Scaffold::maybeOf
    // 0x9a3208: stur            x0, [fp, #-8]
    // 0x9a320c: cmp             w0, NULL
    // 0x9a3210: b.eq            #0x9a323c
    // 0x9a3214: mov             x1, x0
    // 0x9a3218: r0 = isDrawerOpen()
    //     0x9a3218: bl              #0x9a334c  ; [package:flutter/src/material/scaffold.dart] ScaffoldState::isDrawerOpen
    // 0x9a321c: tbz             w0, #4, #0x9a322c
    // 0x9a3220: ldur            x1, [fp, #-8]
    // 0x9a3224: r0 = isEndDrawerOpen()
    //     0x9a3224: bl              #0x9a32e0  ; [package:flutter/src/material/scaffold.dart] ScaffoldState::isEndDrawerOpen
    // 0x9a3228: tbnz            w0, #4, #0x9a323c
    // 0x9a322c: r0 = Null
    //     0x9a322c: mov             x0, NULL
    // 0x9a3230: LeaveFrame
    //     0x9a3230: mov             SP, fp
    //     0x9a3234: ldp             fp, lr, [SP], #0x10
    // 0x9a3238: ret
    //     0x9a3238: ret             
    // 0x9a323c: ldur            x2, [fp, #-0x10]
    // 0x9a3240: LoadField: r1 = r2->field_f
    //     0x9a3240: ldur            w1, [x2, #0xf]
    // 0x9a3244: DecompressPointer r1
    //     0x9a3244: add             x1, x1, HEAP, lsl #32
    // 0x9a3248: cmp             w1, NULL
    // 0x9a324c: b.eq            #0x9a32b8
    // 0x9a3250: r0 = maybeOf()
    //     0x9a3250: bl              #0x6a3eb8  ; [package:flutter/src/widgets/scroll_notification_observer.dart] ScrollNotificationObserver::maybeOf
    // 0x9a3254: mov             x3, x0
    // 0x9a3258: ldur            x2, [fp, #-0x10]
    // 0x9a325c: stur            x3, [fp, #-8]
    // 0x9a3260: StoreField: r2->field_13 = r0
    //     0x9a3260: stur            w0, [x2, #0x13]
    //     0x9a3264: ldurb           w16, [x2, #-1]
    //     0x9a3268: ldurb           w17, [x0, #-1]
    //     0x9a326c: and             x16, x17, x16, lsr #2
    //     0x9a3270: tst             x16, HEAP, lsr #32
    //     0x9a3274: b.eq            #0x9a327c
    //     0x9a3278: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9a327c: cmp             w3, NULL
    // 0x9a3280: b.eq            #0x9a329c
    // 0x9a3284: r1 = Function '_handleScrollNotification@500187611':.
    //     0x9a3284: add             x1, PP, #0x44, lsl #12  ; [pp+0x447f8] AnonymousClosure: (0x9a33f8), in [package:flutter/src/material/app_bar.dart] _AppBarState::_handleScrollNotification (0x9a3434)
    //     0x9a3288: ldr             x1, [x1, #0x7f8]
    // 0x9a328c: r0 = AllocateClosure()
    //     0x9a328c: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a3290: ldur            x1, [fp, #-8]
    // 0x9a3294: mov             x2, x0
    // 0x9a3298: r0 = addListener()
    //     0x9a3298: bl              #0x6a38a8  ; [package:flutter/src/widgets/scroll_notification_observer.dart] ScrollNotificationObserverState::addListener
    // 0x9a329c: r0 = Null
    //     0x9a329c: mov             x0, NULL
    // 0x9a32a0: LeaveFrame
    //     0x9a32a0: mov             SP, fp
    //     0x9a32a4: ldp             fp, lr, [SP], #0x10
    // 0x9a32a8: ret
    //     0x9a32a8: ret             
    // 0x9a32ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a32ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a32b0: b               #0x9a31b8
    // 0x9a32b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a32b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a32b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a32b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _handleScrollNotification(dynamic, ScrollNotification) {
    // ** addr: 0x9a33f8, size: 0x3c
    // 0x9a33f8: EnterFrame
    //     0x9a33f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9a33fc: mov             fp, SP
    // 0x9a3400: ldr             x0, [fp, #0x18]
    // 0x9a3404: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9a3404: ldur            w1, [x0, #0x17]
    // 0x9a3408: DecompressPointer r1
    //     0x9a3408: add             x1, x1, HEAP, lsl #32
    // 0x9a340c: CheckStackOverflow
    //     0x9a340c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a3410: cmp             SP, x16
    //     0x9a3414: b.ls            #0x9a342c
    // 0x9a3418: ldr             x2, [fp, #0x10]
    // 0x9a341c: r0 = _handleScrollNotification()
    //     0x9a341c: bl              #0x9a3434  ; [package:flutter/src/material/app_bar.dart] _AppBarState::_handleScrollNotification
    // 0x9a3420: LeaveFrame
    //     0x9a3420: mov             SP, fp
    //     0x9a3424: ldp             fp, lr, [SP], #0x10
    // 0x9a3428: ret
    //     0x9a3428: ret             
    // 0x9a342c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a342c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a3430: b               #0x9a3418
  }
  _ _handleScrollNotification(/* No info */) {
    // ** addr: 0x9a3434, size: 0x148
    // 0x9a3434: EnterFrame
    //     0x9a3434: stp             fp, lr, [SP, #-0x10]!
    //     0x9a3438: mov             fp, SP
    // 0x9a343c: AllocStack(0x18)
    //     0x9a343c: sub             SP, SP, #0x18
    // 0x9a3440: SetupParameters(_AppBarState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9a3440: mov             x0, x2
    //     0x9a3444: stur            x2, [fp, #-0x10]
    //     0x9a3448: mov             x2, x1
    //     0x9a344c: stur            x1, [fp, #-8]
    // 0x9a3450: CheckStackOverflow
    //     0x9a3450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a3454: cmp             SP, x16
    //     0x9a3458: b.ls            #0x9a3570
    // 0x9a345c: r1 = LoadClassIdInstr(r0)
    //     0x9a345c: ldur            x1, [x0, #-1]
    //     0x9a3460: ubfx            x1, x1, #0xc, #0x14
    // 0x9a3464: cmp             x1, #0xaa1
    // 0x9a3468: b.ne            #0x9a3560
    // 0x9a346c: LoadField: r1 = r2->field_b
    //     0x9a346c: ldur            w1, [x2, #0xb]
    // 0x9a3470: DecompressPointer r1
    //     0x9a3470: add             x1, x1, HEAP, lsl #32
    // 0x9a3474: cmp             w1, NULL
    // 0x9a3478: b.eq            #0x9a3578
    // 0x9a347c: mov             x1, x0
    // 0x9a3480: r0 = defaultScrollNotificationPredicate()
    //     0x9a3480: bl              #0x9a35ac  ; [package:flutter/src/widgets/scroll_notification.dart] ::defaultScrollNotificationPredicate
    // 0x9a3484: tbnz            w0, #4, #0x9a3560
    // 0x9a3488: ldur            x2, [fp, #-8]
    // 0x9a348c: ldur            x0, [fp, #-0x10]
    // 0x9a3490: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9a3490: ldur            w3, [x2, #0x17]
    // 0x9a3494: DecompressPointer r3
    //     0x9a3494: add             x3, x3, HEAP, lsl #32
    // 0x9a3498: stur            x3, [fp, #-0x18]
    // 0x9a349c: LoadField: r1 = r0->field_f
    //     0x9a349c: ldur            w1, [x0, #0xf]
    // 0x9a34a0: DecompressPointer r1
    //     0x9a34a0: add             x1, x1, HEAP, lsl #32
    // 0x9a34a4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9a34a4: ldur            w0, [x1, #0x17]
    // 0x9a34a8: DecompressPointer r0
    //     0x9a34a8: add             x0, x0, HEAP, lsl #32
    // 0x9a34ac: LoadField: r4 = r0->field_7
    //     0x9a34ac: ldur            x4, [x0, #7]
    // 0x9a34b0: cmp             x4, #1
    // 0x9a34b4: b.gt            #0x9a34f8
    // 0x9a34b8: cmp             x4, #0
    // 0x9a34bc: b.gt            #0x9a34f0
    // 0x9a34c0: r0 = extentAfter()
    //     0x9a34c0: bl              #0x7f78f8  ; [package:flutter/src/widgets/scroll_metrics.dart] _FixedScrollMetrics&Object&ScrollMetrics::extentAfter
    // 0x9a34c4: mov             v1.16b, v0.16b
    // 0x9a34c8: d0 = 0.000000
    //     0x9a34c8: eor             v0.16b, v0.16b, v0.16b
    // 0x9a34cc: fcmp            d1, d0
    // 0x9a34d0: r16 = true
    //     0x9a34d0: add             x16, NULL, #0x20  ; true
    // 0x9a34d4: r17 = false
    //     0x9a34d4: add             x17, NULL, #0x30  ; false
    // 0x9a34d8: csel            x0, x16, x17, gt
    // 0x9a34dc: ldur            x2, [fp, #-8]
    // 0x9a34e0: ArrayStore: r2[0] = r0  ; List_4
    //     0x9a34e0: stur            w0, [x2, #0x17]
    // 0x9a34e4: mov             x1, x0
    // 0x9a34e8: mov             x3, x2
    // 0x9a34ec: b               #0x9a3538
    // 0x9a34f0: mov             x3, x2
    // 0x9a34f4: b               #0x9a3534
    // 0x9a34f8: d0 = 0.000000
    //     0x9a34f8: eor             v0.16b, v0.16b, v0.16b
    // 0x9a34fc: cmp             x4, #2
    // 0x9a3500: b.gt            #0x9a3530
    // 0x9a3504: r0 = extentBefore()
    //     0x9a3504: bl              #0x7f797c  ; [package:flutter/src/widgets/scroll_metrics.dart] _FixedScrollMetrics&Object&ScrollMetrics::extentBefore
    // 0x9a3508: mov             v1.16b, v0.16b
    // 0x9a350c: d0 = 0.000000
    //     0x9a350c: eor             v0.16b, v0.16b, v0.16b
    // 0x9a3510: fcmp            d1, d0
    // 0x9a3514: r16 = true
    //     0x9a3514: add             x16, NULL, #0x20  ; true
    // 0x9a3518: r17 = false
    //     0x9a3518: add             x17, NULL, #0x30  ; false
    // 0x9a351c: csel            x0, x16, x17, gt
    // 0x9a3520: ldur            x3, [fp, #-8]
    // 0x9a3524: ArrayStore: r3[0] = r0  ; List_4
    //     0x9a3524: stur            w0, [x3, #0x17]
    // 0x9a3528: mov             x1, x0
    // 0x9a352c: b               #0x9a3538
    // 0x9a3530: mov             x3, x2
    // 0x9a3534: ldur            x1, [fp, #-0x18]
    // 0x9a3538: ldur            x0, [fp, #-0x18]
    // 0x9a353c: cmp             w1, w0
    // 0x9a3540: b.eq            #0x9a3560
    // 0x9a3544: r1 = Function '<anonymous closure>':.
    //     0x9a3544: add             x1, PP, #0x44, lsl #12  ; [pp+0x44800] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x9a3548: ldr             x1, [x1, #0x800]
    // 0x9a354c: r2 = Null
    //     0x9a354c: mov             x2, NULL
    // 0x9a3550: r0 = AllocateClosure()
    //     0x9a3550: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a3554: ldur            x1, [fp, #-8]
    // 0x9a3558: mov             x2, x0
    // 0x9a355c: r0 = setState()
    //     0x9a355c: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9a3560: r0 = Null
    //     0x9a3560: mov             x0, NULL
    // 0x9a3564: LeaveFrame
    //     0x9a3564: mov             SP, fp
    //     0x9a3568: ldp             fp, lr, [SP], #0x10
    // 0x9a356c: ret
    //     0x9a356c: ret             
    // 0x9a3570: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a3570: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a3574: b               #0x9a345c
    // 0x9a3578: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a3578: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0x9e4da0, size: 0x1bac
    // 0x9e4da0: EnterFrame
    //     0x9e4da0: stp             fp, lr, [SP, #-0x10]!
    //     0x9e4da4: mov             fp, SP
    // 0x9e4da8: AllocStack(0xd8)
    //     0x9e4da8: sub             SP, SP, #0xd8
    // 0x9e4dac: SetupParameters(_AppBarState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9e4dac: mov             x0, x2
    //     0x9e4db0: stur            x2, [fp, #-0x10]
    //     0x9e4db4: mov             x2, x1
    //     0x9e4db8: stur            x1, [fp, #-8]
    // 0x9e4dbc: CheckStackOverflow
    //     0x9e4dbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e4dc0: cmp             SP, x16
    //     0x9e4dc4: b.ls            #0x9e68b4
    // 0x9e4dc8: mov             x1, x0
    // 0x9e4dcc: r0 = of()
    //     0x9e4dcc: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9e4dd0: ldur            x1, [fp, #-0x10]
    // 0x9e4dd4: stur            x0, [fp, #-0x18]
    // 0x9e4dd8: r0 = of()
    //     0x9e4dd8: bl              #0x9e8718  ; [package:flutter/src/material/icon_button_theme.dart] IconButtonTheme::of
    // 0x9e4ddc: ldur            x1, [fp, #-0x10]
    // 0x9e4de0: stur            x0, [fp, #-0x20]
    // 0x9e4de4: r0 = of()
    //     0x9e4de4: bl              #0x9e86e0  ; [package:flutter/src/material/app_bar_theme.dart] AppBarTheme::of
    // 0x9e4de8: ldur            x2, [fp, #-0x18]
    // 0x9e4dec: stur            x0, [fp, #-0x30]
    // 0x9e4df0: LoadField: r1 = r2->field_2f
    //     0x9e4df0: ldur            w1, [x2, #0x2f]
    // 0x9e4df4: DecompressPointer r1
    //     0x9e4df4: add             x1, x1, HEAP, lsl #32
    // 0x9e4df8: stur            x1, [fp, #-0x28]
    // 0x9e4dfc: tbnz            w1, #4, #0x9e4e60
    // 0x9e4e00: ldur            x3, [fp, #-0x10]
    // 0x9e4e04: r0 = _AppBarDefaultsM3()
    //     0x9e4e04: bl              #0x9e86d4  ; Allocate_AppBarDefaultsM3Stub -> _AppBarDefaultsM3 (size=0x54)
    // 0x9e4e08: mov             x1, x0
    // 0x9e4e0c: r0 = Sentinel
    //     0x9e4e0c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e4e10: StoreField: r1->field_47 = r0
    //     0x9e4e10: stur            w0, [x1, #0x47]
    // 0x9e4e14: StoreField: r1->field_4b = r0
    //     0x9e4e14: stur            w0, [x1, #0x4b]
    // 0x9e4e18: StoreField: r1->field_4f = r0
    //     0x9e4e18: stur            w0, [x1, #0x4f]
    // 0x9e4e1c: ldur            x2, [fp, #-0x10]
    // 0x9e4e20: StoreField: r1->field_43 = r2
    //     0x9e4e20: stur            w2, [x1, #0x43]
    // 0x9e4e24: r0 = 0.000000
    //     0x9e4e24: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x9e4e28: StoreField: r1->field_f = r0
    //     0x9e4e28: stur            w0, [x1, #0xf]
    // 0x9e4e2c: r0 = 3.000000
    //     0x9e4e2c: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b380] 3
    //     0x9e4e30: ldr             x0, [x0, #0x380]
    // 0x9e4e34: StoreField: r1->field_13 = r0
    //     0x9e4e34: stur            w0, [x1, #0x13]
    // 0x9e4e38: r3 = 16.000000
    //     0x9e4e38: add             x3, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0x9e4e3c: ldr             x3, [x3, #0x80]
    // 0x9e4e40: StoreField: r1->field_2f = r3
    //     0x9e4e40: stur            w3, [x1, #0x2f]
    // 0x9e4e44: r0 = 64.000000
    //     0x9e4e44: add             x0, PP, #0x44, lsl #12  ; [pp+0x44780] 64
    //     0x9e4e48: ldr             x0, [x0, #0x780]
    // 0x9e4e4c: StoreField: r1->field_33 = r0
    //     0x9e4e4c: stur            w0, [x1, #0x33]
    // 0x9e4e50: mov             x0, x2
    // 0x9e4e54: mov             x2, x1
    // 0x9e4e58: d0 = 0.000000
    //     0x9e4e58: eor             v0.16b, v0.16b, v0.16b
    // 0x9e4e5c: b               #0x9e4ec0
    // 0x9e4e60: ldur            x2, [fp, #-0x10]
    // 0x9e4e64: r3 = 16.000000
    //     0x9e4e64: add             x3, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0x9e4e68: ldr             x3, [x3, #0x80]
    // 0x9e4e6c: r0 = Sentinel
    //     0x9e4e6c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e4e70: r0 = _AppBarDefaultsM2()
    //     0x9e4e70: bl              #0x9e86c8  ; Allocate_AppBarDefaultsM2Stub -> _AppBarDefaultsM2 (size=0x50)
    // 0x9e4e74: mov             x1, x0
    // 0x9e4e78: r0 = Sentinel
    //     0x9e4e78: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e4e7c: StoreField: r1->field_47 = r0
    //     0x9e4e7c: stur            w0, [x1, #0x47]
    // 0x9e4e80: StoreField: r1->field_4b = r0
    //     0x9e4e80: stur            w0, [x1, #0x4b]
    // 0x9e4e84: ldur            x0, [fp, #-0x10]
    // 0x9e4e88: StoreField: r1->field_43 = r0
    //     0x9e4e88: stur            w0, [x1, #0x43]
    // 0x9e4e8c: r2 = 4.000000
    //     0x9e4e8c: add             x2, PP, #0x25, lsl #12  ; [pp+0x25770] 4
    //     0x9e4e90: ldr             x2, [x2, #0x770]
    // 0x9e4e94: StoreField: r1->field_f = r2
    //     0x9e4e94: stur            w2, [x1, #0xf]
    // 0x9e4e98: r2 = Instance_Color
    //     0x9e4e98: ldr             x2, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0x9e4e9c: ArrayStore: r1[0] = r2  ; List_4
    //     0x9e4e9c: stur            w2, [x1, #0x17]
    // 0x9e4ea0: r2 = 16.000000
    //     0x9e4ea0: add             x2, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0x9e4ea4: ldr             x2, [x2, #0x80]
    // 0x9e4ea8: StoreField: r1->field_2f = r2
    //     0x9e4ea8: stur            w2, [x1, #0x2f]
    // 0x9e4eac: r2 = 56.000000
    //     0x9e4eac: add             x2, PP, #0x27, lsl #12  ; [pp+0x27c68] 56
    //     0x9e4eb0: ldr             x2, [x2, #0xc68]
    // 0x9e4eb4: StoreField: r1->field_33 = r2
    //     0x9e4eb4: stur            w2, [x1, #0x33]
    // 0x9e4eb8: mov             x2, x1
    // 0x9e4ebc: d0 = 4.000000
    //     0x9e4ebc: fmov            d0, #4.00000000
    // 0x9e4ec0: mov             x1, x0
    // 0x9e4ec4: stur            x2, [fp, #-0x38]
    // 0x9e4ec8: stur            d0, [fp, #-0xb0]
    // 0x9e4ecc: r0 = maybeOf()
    //     0x9e4ecc: bl              #0x9a33b8  ; [package:flutter/src/material/scaffold.dart] Scaffold::maybeOf
    // 0x9e4ed0: stur            x0, [fp, #-0x40]
    // 0x9e4ed4: r16 = <Object?>
    //     0x9e4ed4: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x9e4ed8: ldur            lr, [fp, #-0x10]
    // 0x9e4edc: stp             lr, x16, [SP]
    // 0x9e4ee0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9e4ee0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9e4ee4: r0 = of()
    //     0x9e4ee4: bl              #0x92c598  ; [package:flutter/src/widgets/routes.dart] ModalRoute::of
    // 0x9e4ee8: stur            x0, [fp, #-0x48]
    // 0x9e4eec: r16 = <FlexibleSpaceBarSettings>
    //     0x9e4eec: add             x16, PP, #0x44, lsl #12  ; [pp+0x44448] TypeArguments: <FlexibleSpaceBarSettings>
    //     0x9e4ef0: ldr             x16, [x16, #0x448]
    // 0x9e4ef4: ldur            lr, [fp, #-0x10]
    // 0x9e4ef8: stp             lr, x16, [SP]
    // 0x9e4efc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9e4efc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9e4f00: r0 = dependOnInheritedWidgetOfExactType()
    //     0x9e4f00: bl              #0x6396d8  ; [package:flutter/src/widgets/framework.dart] Element::dependOnInheritedWidgetOfExactType
    // 0x9e4f04: stur            x0, [fp, #-0x50]
    // 0x9e4f08: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x9e4f08: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9e4f0c: ldr             x0, [x0, #0x778]
    //     0x9e4f10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9e4f14: cmp             w0, w16
    //     0x9e4f18: b.ne            #0x9e4f24
    //     0x9e4f1c: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x9e4f20: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9e4f24: r1 = <WidgetState>
    //     0x9e4f24: add             x1, PP, #0x39, lsl #12  ; [pp+0x39868] TypeArguments: <WidgetState>
    //     0x9e4f28: ldr             x1, [x1, #0x868]
    // 0x9e4f2c: stur            x0, [fp, #-0x58]
    // 0x9e4f30: r0 = _Set()
    //     0x9e4f30: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x9e4f34: mov             x1, x0
    // 0x9e4f38: ldur            x0, [fp, #-0x58]
    // 0x9e4f3c: stur            x1, [fp, #-0x60]
    // 0x9e4f40: StoreField: r1->field_1b = r0
    //     0x9e4f40: stur            w0, [x1, #0x1b]
    // 0x9e4f44: StoreField: r1->field_b = rZR
    //     0x9e4f44: stur            wzr, [x1, #0xb]
    // 0x9e4f48: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x9e4f48: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9e4f4c: ldr             x0, [x0, #0x780]
    //     0x9e4f50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9e4f54: cmp             w0, w16
    //     0x9e4f58: b.ne            #0x9e4f64
    //     0x9e4f5c: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x9e4f60: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9e4f64: mov             x1, x0
    // 0x9e4f68: ldur            x0, [fp, #-0x60]
    // 0x9e4f6c: StoreField: r0->field_f = r1
    //     0x9e4f6c: stur            w1, [x0, #0xf]
    // 0x9e4f70: StoreField: r0->field_13 = rZR
    //     0x9e4f70: stur            wzr, [x0, #0x13]
    // 0x9e4f74: ArrayStore: r0[0] = rZR  ; List_4
    //     0x9e4f74: stur            wzr, [x0, #0x17]
    // 0x9e4f78: ldur            x1, [fp, #-0x50]
    // 0x9e4f7c: cmp             w1, NULL
    // 0x9e4f80: b.ne            #0x9e4f8c
    // 0x9e4f84: r1 = Null
    //     0x9e4f84: mov             x1, NULL
    // 0x9e4f88: b               #0x9e4f98
    // 0x9e4f8c: LoadField: r2 = r1->field_2f
    //     0x9e4f8c: ldur            w2, [x1, #0x2f]
    // 0x9e4f90: DecompressPointer r2
    //     0x9e4f90: add             x2, x2, HEAP, lsl #32
    // 0x9e4f94: mov             x1, x2
    // 0x9e4f98: cmp             w1, NULL
    // 0x9e4f9c: b.ne            #0x9e4fb4
    // 0x9e4fa0: ldur            x3, [fp, #-8]
    // 0x9e4fa4: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x9e4fa4: ldur            w1, [x3, #0x17]
    // 0x9e4fa8: DecompressPointer r1
    //     0x9e4fa8: add             x1, x1, HEAP, lsl #32
    // 0x9e4fac: tbnz            w1, #4, #0x9e4fcc
    // 0x9e4fb0: b               #0x9e4fbc
    // 0x9e4fb4: ldur            x3, [fp, #-8]
    // 0x9e4fb8: tbnz            w1, #4, #0x9e4fcc
    // 0x9e4fbc: mov             x1, x0
    // 0x9e4fc0: r2 = Instance_WidgetState
    //     0x9e4fc0: add             x2, PP, #0x44, lsl #12  ; [pp+0x44788] Obj!WidgetState@e33901
    //     0x9e4fc4: ldr             x2, [x2, #0x788]
    // 0x9e4fc8: r0 = add()
    //     0x9e4fc8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x9e4fcc: ldur            x0, [fp, #-0x40]
    // 0x9e4fd0: cmp             w0, NULL
    // 0x9e4fd4: b.ne            #0x9e4fe0
    // 0x9e4fd8: r1 = Null
    //     0x9e4fd8: mov             x1, NULL
    // 0x9e4fdc: b               #0x9e4ff4
    // 0x9e4fe0: LoadField: r1 = r0->field_b
    //     0x9e4fe0: ldur            w1, [x0, #0xb]
    // 0x9e4fe4: DecompressPointer r1
    //     0x9e4fe4: add             x1, x1, HEAP, lsl #32
    // 0x9e4fe8: cmp             w1, NULL
    // 0x9e4fec: b.eq            #0x9e68bc
    // 0x9e4ff0: r1 = false
    //     0x9e4ff0: add             x1, NULL, #0x30  ; false
    // 0x9e4ff4: cmp             w1, NULL
    // 0x9e4ff8: b.ne            #0x9e5004
    // 0x9e4ffc: r2 = false
    //     0x9e4ffc: add             x2, NULL, #0x30  ; false
    // 0x9e5000: b               #0x9e5008
    // 0x9e5004: mov             x2, x1
    // 0x9e5008: stur            x2, [fp, #-0x78]
    // 0x9e500c: cmp             w0, NULL
    // 0x9e5010: b.ne            #0x9e501c
    // 0x9e5014: r0 = Null
    //     0x9e5014: mov             x0, NULL
    // 0x9e5018: b               #0x9e5030
    // 0x9e501c: LoadField: r1 = r0->field_b
    //     0x9e501c: ldur            w1, [x0, #0xb]
    // 0x9e5020: DecompressPointer r1
    //     0x9e5020: add             x1, x1, HEAP, lsl #32
    // 0x9e5024: cmp             w1, NULL
    // 0x9e5028: b.eq            #0x9e68c0
    // 0x9e502c: r0 = false
    //     0x9e502c: add             x0, NULL, #0x30  ; false
    // 0x9e5030: cmp             w0, NULL
    // 0x9e5034: b.ne            #0x9e5040
    // 0x9e5038: r3 = false
    //     0x9e5038: add             x3, NULL, #0x30  ; false
    // 0x9e503c: b               #0x9e5044
    // 0x9e5040: mov             x3, x0
    // 0x9e5044: ldur            x0, [fp, #-0x48]
    // 0x9e5048: stur            x3, [fp, #-0x70]
    // 0x9e504c: r1 = LoadClassIdInstr(r0)
    //     0x9e504c: ldur            x1, [x0, #-1]
    //     0x9e5050: ubfx            x1, x1, #0xc, #0x14
    // 0x9e5054: sub             x16, x1, #0xa62
    // 0x9e5058: cmp             x16, #3
    // 0x9e505c: b.hi            #0x9e5070
    // 0x9e5060: LoadField: r1 = r0->field_8f
    //     0x9e5060: ldur            w1, [x0, #0x8f]
    // 0x9e5064: DecompressPointer r1
    //     0x9e5064: add             x1, x1, HEAP, lsl #32
    // 0x9e5068: mov             x5, x1
    // 0x9e506c: b               #0x9e5074
    // 0x9e5070: r5 = false
    //     0x9e5070: add             x5, NULL, #0x30  ; false
    // 0x9e5074: ldur            x4, [fp, #-8]
    // 0x9e5078: stur            x5, [fp, #-0x58]
    // 0x9e507c: LoadField: r1 = r4->field_b
    //     0x9e507c: ldur            w1, [x4, #0xb]
    // 0x9e5080: DecompressPointer r1
    //     0x9e5080: add             x1, x1, HEAP, lsl #32
    // 0x9e5084: cmp             w1, NULL
    // 0x9e5088: b.eq            #0x9e68c4
    // 0x9e508c: LoadField: r6 = r1->field_6f
    //     0x9e508c: ldur            w6, [x1, #0x6f]
    // 0x9e5090: DecompressPointer r6
    //     0x9e5090: add             x6, x6, HEAP, lsl #32
    // 0x9e5094: cmp             w6, NULL
    // 0x9e5098: b.ne            #0x9e50ac
    // 0x9e509c: ldur            x7, [fp, #-0x30]
    // 0x9e50a0: LoadField: r6 = r7->field_33
    //     0x9e50a0: ldur            w6, [x7, #0x33]
    // 0x9e50a4: DecompressPointer r6
    //     0x9e50a4: add             x6, x6, HEAP, lsl #32
    // 0x9e50a8: b               #0x9e50b0
    // 0x9e50ac: ldur            x7, [fp, #-0x30]
    // 0x9e50b0: cmp             w6, NULL
    // 0x9e50b4: b.ne            #0x9e50c4
    // 0x9e50b8: d0 = 56.000000
    //     0x9e50b8: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0x9e50bc: ldr             d0, [x17, #0xf60]
    // 0x9e50c0: b               #0x9e50c8
    // 0x9e50c4: LoadField: d0 = r6->field_7
    //     0x9e50c4: ldur            d0, [x6, #7]
    // 0x9e50c8: ldur            x6, [fp, #-0x38]
    // 0x9e50cc: stur            d0, [fp, #-0xb8]
    // 0x9e50d0: LoadField: r8 = r1->field_3b
    //     0x9e50d0: ldur            w8, [x1, #0x3b]
    // 0x9e50d4: DecompressPointer r8
    //     0x9e50d4: add             x8, x8, HEAP, lsl #32
    // 0x9e50d8: stur            x8, [fp, #-0x50]
    // 0x9e50dc: LoadField: r9 = r7->field_7
    //     0x9e50dc: ldur            w9, [x7, #7]
    // 0x9e50e0: DecompressPointer r9
    //     0x9e50e0: add             x9, x9, HEAP, lsl #32
    // 0x9e50e4: stur            x9, [fp, #-0x40]
    // 0x9e50e8: r10 = LoadClassIdInstr(r6)
    //     0x9e50e8: ldur            x10, [x6, #-1]
    //     0x9e50ec: ubfx            x10, x10, #0xc, #0x14
    // 0x9e50f0: stur            x10, [fp, #-0x68]
    // 0x9e50f4: cmp             x10, #0xfa1
    // 0x9e50f8: b.ne            #0x9e5110
    // 0x9e50fc: LoadField: r1 = r6->field_7
    //     0x9e50fc: ldur            w1, [x6, #7]
    // 0x9e5100: DecompressPointer r1
    //     0x9e5100: add             x1, x1, HEAP, lsl #32
    // 0x9e5104: mov             x6, x1
    // 0x9e5108: mov             x0, x4
    // 0x9e510c: b               #0x9e51ac
    // 0x9e5110: cmp             x10, #0xfa2
    // 0x9e5114: b.ne            #0x9e5150
    // 0x9e5118: mov             x1, x6
    // 0x9e511c: LoadField: r0 = r1->field_4b
    //     0x9e511c: ldur            w0, [x1, #0x4b]
    // 0x9e5120: DecompressPointer r0
    //     0x9e5120: add             x0, x0, HEAP, lsl #32
    // 0x9e5124: r16 = Sentinel
    //     0x9e5124: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e5128: cmp             w0, w16
    // 0x9e512c: b.ne            #0x9e513c
    // 0x9e5130: r2 = _colors
    //     0x9e5130: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4d8] Field <_AppBarDefaultsM3@500187611._colors@500187611>: late final (offset: 0x4c)
    //     0x9e5134: ldr             x2, [x2, #0x4d8]
    // 0x9e5138: r0 = InitLateFinalInstanceField()
    //     0x9e5138: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e513c: LoadField: r1 = r0->field_7b
    //     0x9e513c: ldur            w1, [x0, #0x7b]
    // 0x9e5140: DecompressPointer r1
    //     0x9e5140: add             x1, x1, HEAP, lsl #32
    // 0x9e5144: mov             x6, x1
    // 0x9e5148: ldur            x0, [fp, #-8]
    // 0x9e514c: b               #0x9e51ac
    // 0x9e5150: ldur            x1, [fp, #-0x38]
    // 0x9e5154: LoadField: r0 = r1->field_4b
    //     0x9e5154: ldur            w0, [x1, #0x4b]
    // 0x9e5158: DecompressPointer r0
    //     0x9e5158: add             x0, x0, HEAP, lsl #32
    // 0x9e515c: r16 = Sentinel
    //     0x9e515c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e5160: cmp             w0, w16
    // 0x9e5164: b.ne            #0x9e5174
    // 0x9e5168: r2 = _colors
    //     0x9e5168: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4e0] Field <_AppBarDefaultsM2@500187611._colors@500187611>: late final (offset: 0x4c)
    //     0x9e516c: ldr             x2, [x2, #0x4e0]
    // 0x9e5170: r0 = InitLateFinalInstanceField()
    //     0x9e5170: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e5174: LoadField: r1 = r0->field_7
    //     0x9e5174: ldur            w1, [x0, #7]
    // 0x9e5178: DecompressPointer r1
    //     0x9e5178: add             x1, x1, HEAP, lsl #32
    // 0x9e517c: r16 = Instance_Brightness
    //     0x9e517c: ldr             x16, [PP, #0x5428]  ; [pp+0x5428] Obj!Brightness@e39141
    // 0x9e5180: cmp             w1, w16
    // 0x9e5184: b.ne            #0x9e5198
    // 0x9e5188: LoadField: r1 = r0->field_7b
    //     0x9e5188: ldur            w1, [x0, #0x7b]
    // 0x9e518c: DecompressPointer r1
    //     0x9e518c: add             x1, x1, HEAP, lsl #32
    // 0x9e5190: mov             x0, x1
    // 0x9e5194: b               #0x9e51a4
    // 0x9e5198: LoadField: r1 = r0->field_b
    //     0x9e5198: ldur            w1, [x0, #0xb]
    // 0x9e519c: DecompressPointer r1
    //     0x9e519c: add             x1, x1, HEAP, lsl #32
    // 0x9e51a0: mov             x0, x1
    // 0x9e51a4: mov             x6, x0
    // 0x9e51a8: ldur            x0, [fp, #-8]
    // 0x9e51ac: mov             x1, x0
    // 0x9e51b0: ldur            x2, [fp, #-0x60]
    // 0x9e51b4: ldur            x3, [fp, #-0x50]
    // 0x9e51b8: ldur            x5, [fp, #-0x40]
    // 0x9e51bc: r0 = _resolveColor()
    //     0x9e51bc: bl              #0x9e8628  ; [package:flutter/src/material/app_bar.dart] _AppBarState::_resolveColor
    // 0x9e51c0: mov             x2, x0
    // 0x9e51c4: ldur            x0, [fp, #-8]
    // 0x9e51c8: stur            x2, [fp, #-0x80]
    // 0x9e51cc: LoadField: r1 = r0->field_b
    //     0x9e51cc: ldur            w1, [x0, #0xb]
    // 0x9e51d0: DecompressPointer r1
    //     0x9e51d0: add             x1, x1, HEAP, lsl #32
    // 0x9e51d4: cmp             w1, NULL
    // 0x9e51d8: b.eq            #0x9e68c8
    // 0x9e51dc: LoadField: r3 = r1->field_3b
    //     0x9e51dc: ldur            w3, [x1, #0x3b]
    // 0x9e51e0: DecompressPointer r3
    //     0x9e51e0: add             x3, x3, HEAP, lsl #32
    // 0x9e51e4: ldur            x1, [fp, #-0x10]
    // 0x9e51e8: stur            x3, [fp, #-0x50]
    // 0x9e51ec: r0 = of()
    //     0x9e51ec: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9e51f0: LoadField: r1 = r0->field_3f
    //     0x9e51f0: ldur            w1, [x0, #0x3f]
    // 0x9e51f4: DecompressPointer r1
    //     0x9e51f4: add             x1, x1, HEAP, lsl #32
    // 0x9e51f8: LoadField: r0 = r1->field_97
    //     0x9e51f8: ldur            w0, [x1, #0x97]
    // 0x9e51fc: DecompressPointer r0
    //     0x9e51fc: add             x0, x0, HEAP, lsl #32
    // 0x9e5200: cmp             w0, NULL
    // 0x9e5204: b.ne            #0x9e5218
    // 0x9e5208: LoadField: r0 = r1->field_7b
    //     0x9e5208: ldur            w0, [x1, #0x7b]
    // 0x9e520c: DecompressPointer r0
    //     0x9e520c: add             x0, x0, HEAP, lsl #32
    // 0x9e5210: mov             x6, x0
    // 0x9e5214: b               #0x9e521c
    // 0x9e5218: mov             x6, x0
    // 0x9e521c: ldur            x1, [fp, #-8]
    // 0x9e5220: ldur            x2, [fp, #-0x60]
    // 0x9e5224: ldur            x3, [fp, #-0x50]
    // 0x9e5228: ldur            x5, [fp, #-0x40]
    // 0x9e522c: r0 = _resolveColor()
    //     0x9e522c: bl              #0x9e8628  ; [package:flutter/src/material/app_bar.dart] _AppBarState::_resolveColor
    // 0x9e5230: ldur            x1, [fp, #-0x60]
    // 0x9e5234: r2 = Instance_WidgetState
    //     0x9e5234: add             x2, PP, #0x44, lsl #12  ; [pp+0x44788] Obj!WidgetState@e33901
    //     0x9e5238: ldr             x2, [x2, #0x788]
    // 0x9e523c: stur            x0, [fp, #-0x10]
    // 0x9e5240: r0 = contains()
    //     0x9e5240: bl              #0x86b148  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0x9e5244: tbnz            w0, #4, #0x9e5250
    // 0x9e5248: ldur            x2, [fp, #-0x10]
    // 0x9e524c: b               #0x9e5254
    // 0x9e5250: ldur            x2, [fp, #-0x80]
    // 0x9e5254: ldur            x0, [fp, #-8]
    // 0x9e5258: stur            x2, [fp, #-0x10]
    // 0x9e525c: LoadField: r1 = r0->field_b
    //     0x9e525c: ldur            w1, [x0, #0xb]
    // 0x9e5260: DecompressPointer r1
    //     0x9e5260: add             x1, x1, HEAP, lsl #32
    // 0x9e5264: cmp             w1, NULL
    // 0x9e5268: b.eq            #0x9e68cc
    // 0x9e526c: LoadField: r3 = r1->field_3f
    //     0x9e526c: ldur            w3, [x1, #0x3f]
    // 0x9e5270: DecompressPointer r3
    //     0x9e5270: add             x3, x3, HEAP, lsl #32
    // 0x9e5274: cmp             w3, NULL
    // 0x9e5278: b.ne            #0x9e5284
    // 0x9e527c: r1 = Null
    //     0x9e527c: mov             x1, NULL
    // 0x9e5280: b               #0x9e5288
    // 0x9e5284: mov             x1, x3
    // 0x9e5288: cmp             w1, NULL
    // 0x9e528c: b.ne            #0x9e534c
    // 0x9e5290: ldur            x3, [fp, #-0x68]
    // 0x9e5294: cmp             x3, #0xfa1
    // 0x9e5298: b.ne            #0x9e52b0
    // 0x9e529c: ldur            x4, [fp, #-0x38]
    // 0x9e52a0: LoadField: r1 = r4->field_b
    //     0x9e52a0: ldur            w1, [x4, #0xb]
    // 0x9e52a4: DecompressPointer r1
    //     0x9e52a4: add             x1, x1, HEAP, lsl #32
    // 0x9e52a8: mov             x0, x1
    // 0x9e52ac: b               #0x9e5344
    // 0x9e52b0: ldur            x4, [fp, #-0x38]
    // 0x9e52b4: cmp             x3, #0xfa2
    // 0x9e52b8: b.ne            #0x9e52f0
    // 0x9e52bc: mov             x1, x4
    // 0x9e52c0: LoadField: r0 = r1->field_4b
    //     0x9e52c0: ldur            w0, [x1, #0x4b]
    // 0x9e52c4: DecompressPointer r0
    //     0x9e52c4: add             x0, x0, HEAP, lsl #32
    // 0x9e52c8: r16 = Sentinel
    //     0x9e52c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e52cc: cmp             w0, w16
    // 0x9e52d0: b.ne            #0x9e52e0
    // 0x9e52d4: r2 = _colors
    //     0x9e52d4: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4d8] Field <_AppBarDefaultsM3@500187611._colors@500187611>: late final (offset: 0x4c)
    //     0x9e52d8: ldr             x2, [x2, #0x4d8]
    // 0x9e52dc: r0 = InitLateFinalInstanceField()
    //     0x9e52dc: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e52e0: LoadField: r1 = r0->field_7f
    //     0x9e52e0: ldur            w1, [x0, #0x7f]
    // 0x9e52e4: DecompressPointer r1
    //     0x9e52e4: add             x1, x1, HEAP, lsl #32
    // 0x9e52e8: mov             x0, x1
    // 0x9e52ec: b               #0x9e5344
    // 0x9e52f0: ldur            x1, [fp, #-0x38]
    // 0x9e52f4: LoadField: r0 = r1->field_4b
    //     0x9e52f4: ldur            w0, [x1, #0x4b]
    // 0x9e52f8: DecompressPointer r0
    //     0x9e52f8: add             x0, x0, HEAP, lsl #32
    // 0x9e52fc: r16 = Sentinel
    //     0x9e52fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e5300: cmp             w0, w16
    // 0x9e5304: b.ne            #0x9e5314
    // 0x9e5308: r2 = _colors
    //     0x9e5308: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4e0] Field <_AppBarDefaultsM2@500187611._colors@500187611>: late final (offset: 0x4c)
    //     0x9e530c: ldr             x2, [x2, #0x4e0]
    // 0x9e5310: r0 = InitLateFinalInstanceField()
    //     0x9e5310: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e5314: LoadField: r1 = r0->field_7
    //     0x9e5314: ldur            w1, [x0, #7]
    // 0x9e5318: DecompressPointer r1
    //     0x9e5318: add             x1, x1, HEAP, lsl #32
    // 0x9e531c: r16 = Instance_Brightness
    //     0x9e531c: ldr             x16, [PP, #0x5428]  ; [pp+0x5428] Obj!Brightness@e39141
    // 0x9e5320: cmp             w1, w16
    // 0x9e5324: b.ne            #0x9e5338
    // 0x9e5328: LoadField: r1 = r0->field_7f
    //     0x9e5328: ldur            w1, [x0, #0x7f]
    // 0x9e532c: DecompressPointer r1
    //     0x9e532c: add             x1, x1, HEAP, lsl #32
    // 0x9e5330: mov             x0, x1
    // 0x9e5334: b               #0x9e5344
    // 0x9e5338: LoadField: r1 = r0->field_f
    //     0x9e5338: ldur            w1, [x0, #0xf]
    // 0x9e533c: DecompressPointer r1
    //     0x9e533c: add             x1, x1, HEAP, lsl #32
    // 0x9e5340: mov             x0, x1
    // 0x9e5344: mov             x3, x0
    // 0x9e5348: b               #0x9e5350
    // 0x9e534c: mov             x3, x1
    // 0x9e5350: ldur            x0, [fp, #-8]
    // 0x9e5354: stur            x3, [fp, #-0x40]
    // 0x9e5358: LoadField: r1 = r0->field_b
    //     0x9e5358: ldur            w1, [x0, #0xb]
    // 0x9e535c: DecompressPointer r1
    //     0x9e535c: add             x1, x1, HEAP, lsl #32
    // 0x9e5360: cmp             w1, NULL
    // 0x9e5364: b.eq            #0x9e68d0
    // 0x9e5368: LoadField: r2 = r1->field_23
    //     0x9e5368: ldur            w2, [x1, #0x23]
    // 0x9e536c: DecompressPointer r2
    //     0x9e536c: add             x2, x2, HEAP, lsl #32
    // 0x9e5370: cmp             w2, NULL
    // 0x9e5374: b.ne            #0x9e5388
    // 0x9e5378: ldur            x4, [fp, #-0x30]
    // 0x9e537c: LoadField: r1 = r4->field_f
    //     0x9e537c: ldur            w1, [x4, #0xf]
    // 0x9e5380: DecompressPointer r1
    //     0x9e5380: add             x1, x1, HEAP, lsl #32
    // 0x9e5384: b               #0x9e5390
    // 0x9e5388: ldur            x4, [fp, #-0x30]
    // 0x9e538c: mov             x1, x2
    // 0x9e5390: cmp             w1, NULL
    // 0x9e5394: b.ne            #0x9e53a0
    // 0x9e5398: ldur            d0, [fp, #-0xb0]
    // 0x9e539c: b               #0x9e53a4
    // 0x9e53a0: LoadField: d0 = r1->field_7
    //     0x9e53a0: ldur            d0, [x1, #7]
    // 0x9e53a4: ldur            x1, [fp, #-0x60]
    // 0x9e53a8: stur            d0, [fp, #-0xb0]
    // 0x9e53ac: r2 = Instance_WidgetState
    //     0x9e53ac: add             x2, PP, #0x44, lsl #12  ; [pp+0x44788] Obj!WidgetState@e33901
    //     0x9e53b0: ldr             x2, [x2, #0x788]
    // 0x9e53b4: r0 = contains()
    //     0x9e53b4: bl              #0x86b148  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0x9e53b8: tbnz            w0, #4, #0x9e5410
    // 0x9e53bc: ldur            x0, [fp, #-8]
    // 0x9e53c0: ldur            x2, [fp, #-0x30]
    // 0x9e53c4: LoadField: r1 = r0->field_b
    //     0x9e53c4: ldur            w1, [x0, #0xb]
    // 0x9e53c8: DecompressPointer r1
    //     0x9e53c8: add             x1, x1, HEAP, lsl #32
    // 0x9e53cc: cmp             w1, NULL
    // 0x9e53d0: b.eq            #0x9e68d4
    // 0x9e53d4: LoadField: r1 = r2->field_13
    //     0x9e53d4: ldur            w1, [x2, #0x13]
    // 0x9e53d8: DecompressPointer r1
    //     0x9e53d8: add             x1, x1, HEAP, lsl #32
    // 0x9e53dc: cmp             w1, NULL
    // 0x9e53e0: b.ne            #0x9e53f4
    // 0x9e53e4: ldur            x3, [fp, #-0x38]
    // 0x9e53e8: LoadField: r1 = r3->field_13
    //     0x9e53e8: ldur            w1, [x3, #0x13]
    // 0x9e53ec: DecompressPointer r1
    //     0x9e53ec: add             x1, x1, HEAP, lsl #32
    // 0x9e53f0: b               #0x9e53f8
    // 0x9e53f4: ldur            x3, [fp, #-0x38]
    // 0x9e53f8: cmp             w1, NULL
    // 0x9e53fc: b.ne            #0x9e5408
    // 0x9e5400: ldur            d0, [fp, #-0xb0]
    // 0x9e5404: b               #0x9e5420
    // 0x9e5408: LoadField: d0 = r1->field_7
    //     0x9e5408: ldur            d0, [x1, #7]
    // 0x9e540c: b               #0x9e5420
    // 0x9e5410: ldur            x0, [fp, #-8]
    // 0x9e5414: ldur            x2, [fp, #-0x30]
    // 0x9e5418: ldur            x3, [fp, #-0x38]
    // 0x9e541c: ldur            d0, [fp, #-0xb0]
    // 0x9e5420: stur            d0, [fp, #-0xb0]
    // 0x9e5424: LoadField: r1 = r0->field_b
    //     0x9e5424: ldur            w1, [x0, #0xb]
    // 0x9e5428: DecompressPointer r1
    //     0x9e5428: add             x1, x1, HEAP, lsl #32
    // 0x9e542c: cmp             w1, NULL
    // 0x9e5430: b.eq            #0x9e68d8
    // 0x9e5434: LoadField: r4 = r1->field_43
    //     0x9e5434: ldur            w4, [x1, #0x43]
    // 0x9e5438: DecompressPointer r4
    //     0x9e5438: add             x4, x4, HEAP, lsl #32
    // 0x9e543c: cmp             w4, NULL
    // 0x9e5440: b.ne            #0x9e5450
    // 0x9e5444: LoadField: r1 = r2->field_23
    //     0x9e5444: ldur            w1, [x2, #0x23]
    // 0x9e5448: DecompressPointer r1
    //     0x9e5448: add             x1, x1, HEAP, lsl #32
    // 0x9e544c: b               #0x9e5454
    // 0x9e5450: mov             x1, x4
    // 0x9e5454: cmp             w1, NULL
    // 0x9e5458: b.ne            #0x9e551c
    // 0x9e545c: ldur            x4, [fp, #-0x68]
    // 0x9e5460: cmp             x4, #0xfa1
    // 0x9e5464: b.ne            #0x9e5474
    // 0x9e5468: LoadField: r1 = r3->field_23
    //     0x9e5468: ldur            w1, [x3, #0x23]
    // 0x9e546c: DecompressPointer r1
    //     0x9e546c: add             x1, x1, HEAP, lsl #32
    // 0x9e5470: b               #0x9e5500
    // 0x9e5474: cmp             x4, #0xfa2
    // 0x9e5478: b.ne            #0x9e54cc
    // 0x9e547c: mov             x1, x3
    // 0x9e5480: LoadField: r0 = r1->field_4b
    //     0x9e5480: ldur            w0, [x1, #0x4b]
    // 0x9e5484: DecompressPointer r0
    //     0x9e5484: add             x0, x0, HEAP, lsl #32
    // 0x9e5488: r16 = Sentinel
    //     0x9e5488: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e548c: cmp             w0, w16
    // 0x9e5490: b.ne            #0x9e54a0
    // 0x9e5494: r2 = _colors
    //     0x9e5494: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4d8] Field <_AppBarDefaultsM3@500187611._colors@500187611>: late final (offset: 0x4c)
    //     0x9e5498: ldr             x2, [x2, #0x4d8]
    // 0x9e549c: r0 = InitLateFinalInstanceField()
    //     0x9e549c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e54a0: LoadField: r1 = r0->field_7f
    //     0x9e54a0: ldur            w1, [x0, #0x7f]
    // 0x9e54a4: DecompressPointer r1
    //     0x9e54a4: add             x1, x1, HEAP, lsl #32
    // 0x9e54a8: stur            x1, [fp, #-0x50]
    // 0x9e54ac: r0 = IconThemeData()
    //     0x9e54ac: bl              #0x63d1c0  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x9e54b0: mov             x1, x0
    // 0x9e54b4: r0 = 24.000000
    //     0x9e54b4: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0x9e54b8: ldr             x0, [x0, #0x368]
    // 0x9e54bc: StoreField: r1->field_7 = r0
    //     0x9e54bc: stur            w0, [x1, #7]
    // 0x9e54c0: ldur            x2, [fp, #-0x50]
    // 0x9e54c4: StoreField: r1->field_1b = r2
    //     0x9e54c4: stur            w2, [x1, #0x1b]
    // 0x9e54c8: b               #0x9e5500
    // 0x9e54cc: r0 = 24.000000
    //     0x9e54cc: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0x9e54d0: ldr             x0, [x0, #0x368]
    // 0x9e54d4: ldur            x1, [fp, #-0x38]
    // 0x9e54d8: LoadField: r0 = r1->field_47
    //     0x9e54d8: ldur            w0, [x1, #0x47]
    // 0x9e54dc: DecompressPointer r0
    //     0x9e54dc: add             x0, x0, HEAP, lsl #32
    // 0x9e54e0: r16 = Sentinel
    //     0x9e54e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e54e4: cmp             w0, w16
    // 0x9e54e8: b.ne            #0x9e54f8
    // 0x9e54ec: r2 = _theme
    //     0x9e54ec: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4e8] Field <_AppBarDefaultsM2@500187611._theme@500187611>: late final (offset: 0x48)
    //     0x9e54f0: ldr             x2, [x2, #0x4e8]
    // 0x9e54f4: r0 = InitLateFinalInstanceField()
    //     0x9e54f4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e54f8: LoadField: r1 = r0->field_83
    //     0x9e54f8: ldur            w1, [x0, #0x83]
    // 0x9e54fc: DecompressPointer r1
    //     0x9e54fc: add             x1, x1, HEAP, lsl #32
    // 0x9e5500: ldur            x16, [fp, #-0x40]
    // 0x9e5504: str             x16, [SP]
    // 0x9e5508: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x9e5508: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x9e550c: ldr             x4, [x4, #0x228]
    // 0x9e5510: r0 = copyWith()
    //     0x9e5510: bl              #0xd81ff4  ; [package:flutter/src/widgets/icon_theme_data.dart] IconThemeData::copyWith
    // 0x9e5514: mov             x2, x0
    // 0x9e5518: b               #0x9e5520
    // 0x9e551c: mov             x2, x1
    // 0x9e5520: ldur            x0, [fp, #-8]
    // 0x9e5524: stur            x2, [fp, #-0x60]
    // 0x9e5528: LoadField: r1 = r0->field_b
    //     0x9e5528: ldur            w1, [x0, #0xb]
    // 0x9e552c: DecompressPointer r1
    //     0x9e552c: add             x1, x1, HEAP, lsl #32
    // 0x9e5530: cmp             w1, NULL
    // 0x9e5534: b.eq            #0x9e68dc
    // 0x9e5538: LoadField: r3 = r1->field_3f
    //     0x9e5538: ldur            w3, [x1, #0x3f]
    // 0x9e553c: DecompressPointer r3
    //     0x9e553c: add             x3, x3, HEAP, lsl #32
    // 0x9e5540: cmp             w3, NULL
    // 0x9e5544: b.ne            #0x9e5550
    // 0x9e5548: r4 = Null
    //     0x9e5548: mov             x4, NULL
    // 0x9e554c: b               #0x9e5554
    // 0x9e5550: mov             x4, x3
    // 0x9e5554: ldur            x3, [fp, #-0x30]
    // 0x9e5558: stur            x4, [fp, #-0x50]
    // 0x9e555c: LoadField: r5 = r3->field_27
    //     0x9e555c: ldur            w5, [x3, #0x27]
    // 0x9e5560: DecompressPointer r5
    //     0x9e5560: add             x5, x5, HEAP, lsl #32
    // 0x9e5564: cmp             w5, NULL
    // 0x9e5568: b.ne            #0x9e557c
    // 0x9e556c: LoadField: r5 = r1->field_43
    //     0x9e556c: ldur            w5, [x1, #0x43]
    // 0x9e5570: DecompressPointer r5
    //     0x9e5570: add             x5, x5, HEAP, lsl #32
    // 0x9e5574: mov             x1, x5
    // 0x9e5578: b               #0x9e5580
    // 0x9e557c: mov             x1, x5
    // 0x9e5580: cmp             w1, NULL
    // 0x9e5584: b.ne            #0x9e5590
    // 0x9e5588: LoadField: r1 = r3->field_23
    //     0x9e5588: ldur            w1, [x3, #0x23]
    // 0x9e558c: DecompressPointer r1
    //     0x9e558c: add             x1, x1, HEAP, lsl #32
    // 0x9e5590: cmp             w1, NULL
    // 0x9e5594: b.ne            #0x9e5664
    // 0x9e5598: ldur            x5, [fp, #-0x68]
    // 0x9e559c: cmp             x5, #0xfa1
    // 0x9e55a0: b.ne            #0x9e55b0
    // 0x9e55a4: r0 = 24.000000
    //     0x9e55a4: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0x9e55a8: ldr             x0, [x0, #0x368]
    // 0x9e55ac: b               #0x9e5630
    // 0x9e55b0: cmp             x5, #0xfa2
    // 0x9e55b4: b.ne            #0x9e5628
    // 0x9e55b8: ldur            x1, [fp, #-0x38]
    // 0x9e55bc: LoadField: r0 = r1->field_4b
    //     0x9e55bc: ldur            w0, [x1, #0x4b]
    // 0x9e55c0: DecompressPointer r0
    //     0x9e55c0: add             x0, x0, HEAP, lsl #32
    // 0x9e55c4: r16 = Sentinel
    //     0x9e55c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e55c8: cmp             w0, w16
    // 0x9e55cc: b.ne            #0x9e55dc
    // 0x9e55d0: r2 = _colors
    //     0x9e55d0: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4d8] Field <_AppBarDefaultsM3@500187611._colors@500187611>: late final (offset: 0x4c)
    //     0x9e55d4: ldr             x2, [x2, #0x4d8]
    // 0x9e55d8: r0 = InitLateFinalInstanceField()
    //     0x9e55d8: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e55dc: LoadField: r1 = r0->field_a3
    //     0x9e55dc: ldur            w1, [x0, #0xa3]
    // 0x9e55e0: DecompressPointer r1
    //     0x9e55e0: add             x1, x1, HEAP, lsl #32
    // 0x9e55e4: cmp             w1, NULL
    // 0x9e55e8: b.ne            #0x9e55fc
    // 0x9e55ec: LoadField: r1 = r0->field_7f
    //     0x9e55ec: ldur            w1, [x0, #0x7f]
    // 0x9e55f0: DecompressPointer r1
    //     0x9e55f0: add             x1, x1, HEAP, lsl #32
    // 0x9e55f4: mov             x0, x1
    // 0x9e55f8: b               #0x9e5600
    // 0x9e55fc: mov             x0, x1
    // 0x9e5600: stur            x0, [fp, #-0x88]
    // 0x9e5604: r0 = IconThemeData()
    //     0x9e5604: bl              #0x63d1c0  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x9e5608: mov             x1, x0
    // 0x9e560c: r0 = 24.000000
    //     0x9e560c: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0x9e5610: ldr             x0, [x0, #0x368]
    // 0x9e5614: StoreField: r1->field_7 = r0
    //     0x9e5614: stur            w0, [x1, #7]
    // 0x9e5618: ldur            x2, [fp, #-0x88]
    // 0x9e561c: StoreField: r1->field_1b = r2
    //     0x9e561c: stur            w2, [x1, #0x1b]
    // 0x9e5620: ldur            x2, [fp, #-0x38]
    // 0x9e5624: b               #0x9e563c
    // 0x9e5628: r0 = 24.000000
    //     0x9e5628: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0x9e562c: ldr             x0, [x0, #0x368]
    // 0x9e5630: ldur            x2, [fp, #-0x38]
    // 0x9e5634: LoadField: r1 = r2->field_27
    //     0x9e5634: ldur            w1, [x2, #0x27]
    // 0x9e5638: DecompressPointer r1
    //     0x9e5638: add             x1, x1, HEAP, lsl #32
    // 0x9e563c: cmp             w1, NULL
    // 0x9e5640: b.ne            #0x9e564c
    // 0x9e5644: r0 = Null
    //     0x9e5644: mov             x0, NULL
    // 0x9e5648: b               #0x9e5668
    // 0x9e564c: ldur            x16, [fp, #-0x50]
    // 0x9e5650: str             x16, [SP]
    // 0x9e5654: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x9e5654: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x9e5658: ldr             x4, [x4, #0x228]
    // 0x9e565c: r0 = copyWith()
    //     0x9e565c: bl              #0xd81ff4  ; [package:flutter/src/widgets/icon_theme_data.dart] IconThemeData::copyWith
    // 0x9e5660: b               #0x9e5668
    // 0x9e5664: mov             x0, x1
    // 0x9e5668: cmp             w0, NULL
    // 0x9e566c: b.ne            #0x9e5678
    // 0x9e5670: ldur            x3, [fp, #-0x60]
    // 0x9e5674: b               #0x9e567c
    // 0x9e5678: mov             x3, x0
    // 0x9e567c: ldur            x0, [fp, #-8]
    // 0x9e5680: ldur            x2, [fp, #-0x68]
    // 0x9e5684: stur            x3, [fp, #-0x50]
    // 0x9e5688: LoadField: r1 = r0->field_b
    //     0x9e5688: ldur            w1, [x0, #0xb]
    // 0x9e568c: DecompressPointer r1
    //     0x9e568c: add             x1, x1, HEAP, lsl #32
    // 0x9e5690: cmp             w1, NULL
    // 0x9e5694: b.eq            #0x9e68e0
    // 0x9e5698: cmp             x2, #0xfa1
    // 0x9e569c: b.ne            #0x9e56b0
    // 0x9e56a0: ldur            x4, [fp, #-0x38]
    // 0x9e56a4: LoadField: r1 = r4->field_37
    //     0x9e56a4: ldur            w1, [x4, #0x37]
    // 0x9e56a8: DecompressPointer r1
    //     0x9e56a8: add             x1, x1, HEAP, lsl #32
    // 0x9e56ac: b               #0x9e5724
    // 0x9e56b0: ldur            x4, [fp, #-0x38]
    // 0x9e56b4: cmp             x2, #0xfa2
    // 0x9e56b8: b.ne            #0x9e56ec
    // 0x9e56bc: mov             x1, x4
    // 0x9e56c0: LoadField: r0 = r1->field_4f
    //     0x9e56c0: ldur            w0, [x1, #0x4f]
    // 0x9e56c4: DecompressPointer r0
    //     0x9e56c4: add             x0, x0, HEAP, lsl #32
    // 0x9e56c8: r16 = Sentinel
    //     0x9e56c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e56cc: cmp             w0, w16
    // 0x9e56d0: b.ne            #0x9e56e0
    // 0x9e56d4: r2 = _textTheme
    //     0x9e56d4: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4f0] Field <_AppBarDefaultsM3@500187611._textTheme@500187611>: late final (offset: 0x50)
    //     0x9e56d8: ldr             x2, [x2, #0x4f0]
    // 0x9e56dc: r0 = InitLateFinalInstanceField()
    //     0x9e56dc: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e56e0: LoadField: r1 = r0->field_2f
    //     0x9e56e0: ldur            w1, [x0, #0x2f]
    // 0x9e56e4: DecompressPointer r1
    //     0x9e56e4: add             x1, x1, HEAP, lsl #32
    // 0x9e56e8: b               #0x9e5724
    // 0x9e56ec: ldur            x1, [fp, #-0x38]
    // 0x9e56f0: LoadField: r0 = r1->field_47
    //     0x9e56f0: ldur            w0, [x1, #0x47]
    // 0x9e56f4: DecompressPointer r0
    //     0x9e56f4: add             x0, x0, HEAP, lsl #32
    // 0x9e56f8: r16 = Sentinel
    //     0x9e56f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e56fc: cmp             w0, w16
    // 0x9e5700: b.ne            #0x9e5710
    // 0x9e5704: r2 = _theme
    //     0x9e5704: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4e8] Field <_AppBarDefaultsM2@500187611._theme@500187611>: late final (offset: 0x48)
    //     0x9e5708: ldr             x2, [x2, #0x4e8]
    // 0x9e570c: r0 = InitLateFinalInstanceField()
    //     0x9e570c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e5710: LoadField: r1 = r0->field_8f
    //     0x9e5710: ldur            w1, [x0, #0x8f]
    // 0x9e5714: DecompressPointer r1
    //     0x9e5714: add             x1, x1, HEAP, lsl #32
    // 0x9e5718: LoadField: r0 = r1->field_2f
    //     0x9e5718: ldur            w0, [x1, #0x2f]
    // 0x9e571c: DecompressPointer r0
    //     0x9e571c: add             x0, x0, HEAP, lsl #32
    // 0x9e5720: mov             x1, x0
    // 0x9e5724: cmp             w1, NULL
    // 0x9e5728: b.ne            #0x9e5734
    // 0x9e572c: r3 = Null
    //     0x9e572c: mov             x3, NULL
    // 0x9e5730: b               #0x9e574c
    // 0x9e5734: ldur            x16, [fp, #-0x40]
    // 0x9e5738: str             x16, [SP]
    // 0x9e573c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x9e573c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x9e5740: ldr             x4, [x4, #0x228]
    // 0x9e5744: r0 = copyWith()
    //     0x9e5744: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9e5748: mov             x3, x0
    // 0x9e574c: ldur            x0, [fp, #-8]
    // 0x9e5750: ldur            x2, [fp, #-0x30]
    // 0x9e5754: stur            x3, [fp, #-0x88]
    // 0x9e5758: LoadField: r1 = r0->field_b
    //     0x9e5758: ldur            w1, [x0, #0xb]
    // 0x9e575c: DecompressPointer r1
    //     0x9e575c: add             x1, x1, HEAP, lsl #32
    // 0x9e5760: cmp             w1, NULL
    // 0x9e5764: b.eq            #0x9e68e4
    // 0x9e5768: LoadField: r1 = r2->field_3b
    //     0x9e5768: ldur            w1, [x2, #0x3b]
    // 0x9e576c: DecompressPointer r1
    //     0x9e576c: add             x1, x1, HEAP, lsl #32
    // 0x9e5770: cmp             w1, NULL
    // 0x9e5774: b.ne            #0x9e5834
    // 0x9e5778: ldur            x4, [fp, #-0x68]
    // 0x9e577c: cmp             x4, #0xfa1
    // 0x9e5780: b.ne            #0x9e5794
    // 0x9e5784: ldur            x5, [fp, #-0x38]
    // 0x9e5788: LoadField: r1 = r5->field_3b
    //     0x9e5788: ldur            w1, [x5, #0x3b]
    // 0x9e578c: DecompressPointer r1
    //     0x9e578c: add             x1, x1, HEAP, lsl #32
    // 0x9e5790: b               #0x9e5808
    // 0x9e5794: ldur            x5, [fp, #-0x38]
    // 0x9e5798: cmp             x4, #0xfa2
    // 0x9e579c: b.ne            #0x9e57d0
    // 0x9e57a0: mov             x1, x5
    // 0x9e57a4: LoadField: r0 = r1->field_4f
    //     0x9e57a4: ldur            w0, [x1, #0x4f]
    // 0x9e57a8: DecompressPointer r0
    //     0x9e57a8: add             x0, x0, HEAP, lsl #32
    // 0x9e57ac: r16 = Sentinel
    //     0x9e57ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e57b0: cmp             w0, w16
    // 0x9e57b4: b.ne            #0x9e57c4
    // 0x9e57b8: r2 = _textTheme
    //     0x9e57b8: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4f0] Field <_AppBarDefaultsM3@500187611._textTheme@500187611>: late final (offset: 0x50)
    //     0x9e57bc: ldr             x2, [x2, #0x4f0]
    // 0x9e57c0: r0 = InitLateFinalInstanceField()
    //     0x9e57c0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e57c4: LoadField: r1 = r0->field_1f
    //     0x9e57c4: ldur            w1, [x0, #0x1f]
    // 0x9e57c8: DecompressPointer r1
    //     0x9e57c8: add             x1, x1, HEAP, lsl #32
    // 0x9e57cc: b               #0x9e5808
    // 0x9e57d0: ldur            x1, [fp, #-0x38]
    // 0x9e57d4: LoadField: r0 = r1->field_47
    //     0x9e57d4: ldur            w0, [x1, #0x47]
    // 0x9e57d8: DecompressPointer r0
    //     0x9e57d8: add             x0, x0, HEAP, lsl #32
    // 0x9e57dc: r16 = Sentinel
    //     0x9e57dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e57e0: cmp             w0, w16
    // 0x9e57e4: b.ne            #0x9e57f4
    // 0x9e57e8: r2 = _theme
    //     0x9e57e8: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4e8] Field <_AppBarDefaultsM2@500187611._theme@500187611>: late final (offset: 0x48)
    //     0x9e57ec: ldr             x2, [x2, #0x4e8]
    // 0x9e57f0: r0 = InitLateFinalInstanceField()
    //     0x9e57f0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e57f4: LoadField: r1 = r0->field_8f
    //     0x9e57f4: ldur            w1, [x0, #0x8f]
    // 0x9e57f8: DecompressPointer r1
    //     0x9e57f8: add             x1, x1, HEAP, lsl #32
    // 0x9e57fc: LoadField: r0 = r1->field_1f
    //     0x9e57fc: ldur            w0, [x1, #0x1f]
    // 0x9e5800: DecompressPointer r0
    //     0x9e5800: add             x0, x0, HEAP, lsl #32
    // 0x9e5804: mov             x1, x0
    // 0x9e5808: cmp             w1, NULL
    // 0x9e580c: b.ne            #0x9e5818
    // 0x9e5810: r0 = Null
    //     0x9e5810: mov             x0, NULL
    // 0x9e5814: b               #0x9e582c
    // 0x9e5818: ldur            x16, [fp, #-0x40]
    // 0x9e581c: str             x16, [SP]
    // 0x9e5820: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x9e5820: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x9e5824: ldr             x4, [x4, #0x228]
    // 0x9e5828: r0 = copyWith()
    //     0x9e5828: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9e582c: mov             x2, x0
    // 0x9e5830: b               #0x9e5838
    // 0x9e5834: mov             x2, x1
    // 0x9e5838: ldur            x0, [fp, #-8]
    // 0x9e583c: d1 = 1.000000
    //     0x9e583c: fmov            d1, #1.00000000
    // 0x9e5840: stur            x2, [fp, #-0x40]
    // 0x9e5844: LoadField: r1 = r0->field_b
    //     0x9e5844: ldur            w1, [x0, #0xb]
    // 0x9e5848: DecompressPointer r1
    //     0x9e5848: add             x1, x1, HEAP, lsl #32
    // 0x9e584c: cmp             w1, NULL
    // 0x9e5850: b.eq            #0x9e68e8
    // 0x9e5854: LoadField: d0 = r1->field_5b
    //     0x9e5854: ldur            d0, [x1, #0x5b]
    // 0x9e5858: fcmp            d0, d1
    // 0x9e585c: b.eq            #0x9e5a00
    // 0x9e5860: r1 = Instance_Interval
    //     0x9e5860: add             x1, PP, #0x44, lsl #12  ; [pp+0x44790] Obj!Interval@e15061
    //     0x9e5864: ldr             x1, [x1, #0x790]
    // 0x9e5868: r0 = transform()
    //     0x9e5868: bl              #0xcd6c40  ; [package:flutter/src/animation/curves.dart] Curve::transform
    // 0x9e586c: mov             v1.16b, v0.16b
    // 0x9e5870: ldur            x2, [fp, #-0x40]
    // 0x9e5874: stur            d1, [fp, #-0xc0]
    // 0x9e5878: cmp             w2, NULL
    // 0x9e587c: b.eq            #0x9e58c4
    // 0x9e5880: LoadField: r1 = r2->field_b
    //     0x9e5880: ldur            w1, [x2, #0xb]
    // 0x9e5884: DecompressPointer r1
    //     0x9e5884: add             x1, x1, HEAP, lsl #32
    // 0x9e5888: cmp             w1, NULL
    // 0x9e588c: b.eq            #0x9e58c4
    // 0x9e5890: r0 = LoadClassIdInstr(r1)
    //     0x9e5890: ldur            x0, [x1, #-1]
    //     0x9e5894: ubfx            x0, x0, #0xc, #0x14
    // 0x9e5898: mov             v0.16b, v1.16b
    // 0x9e589c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x9e589c: sub             lr, x0, #1, lsl #12
    //     0x9e58a0: ldr             lr, [x21, lr, lsl #3]
    //     0x9e58a4: blr             lr
    // 0x9e58a8: str             x0, [SP]
    // 0x9e58ac: ldur            x1, [fp, #-0x40]
    // 0x9e58b0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x9e58b0: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x9e58b4: ldr             x4, [x4, #0x228]
    // 0x9e58b8: r0 = copyWith()
    //     0x9e58b8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9e58bc: mov             x3, x0
    // 0x9e58c0: b               #0x9e58c8
    // 0x9e58c4: ldur            x3, [fp, #-0x40]
    // 0x9e58c8: ldur            x2, [fp, #-0x88]
    // 0x9e58cc: stur            x3, [fp, #-0x90]
    // 0x9e58d0: cmp             w2, NULL
    // 0x9e58d4: b.eq            #0x9e5918
    // 0x9e58d8: LoadField: r1 = r2->field_b
    //     0x9e58d8: ldur            w1, [x2, #0xb]
    // 0x9e58dc: DecompressPointer r1
    //     0x9e58dc: add             x1, x1, HEAP, lsl #32
    // 0x9e58e0: cmp             w1, NULL
    // 0x9e58e4: b.eq            #0x9e5918
    // 0x9e58e8: r0 = LoadClassIdInstr(r1)
    //     0x9e58e8: ldur            x0, [x1, #-1]
    //     0x9e58ec: ubfx            x0, x0, #0xc, #0x14
    // 0x9e58f0: ldur            d0, [fp, #-0xc0]
    // 0x9e58f4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x9e58f4: sub             lr, x0, #1, lsl #12
    //     0x9e58f8: ldr             lr, [x21, lr, lsl #3]
    //     0x9e58fc: blr             lr
    // 0x9e5900: str             x0, [SP]
    // 0x9e5904: ldur            x1, [fp, #-0x88]
    // 0x9e5908: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x9e5908: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x9e590c: ldr             x4, [x4, #0x228]
    // 0x9e5910: r0 = copyWith()
    //     0x9e5910: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9e5914: b               #0x9e591c
    // 0x9e5918: ldur            x0, [fp, #-0x88]
    // 0x9e591c: ldur            x1, [fp, #-0x60]
    // 0x9e5920: stur            x0, [fp, #-0x98]
    // 0x9e5924: r0 = opacity()
    //     0x9e5924: bl              #0x882b30  ; [package:flutter/src/widgets/icon_theme_data.dart] IconThemeData::opacity
    // 0x9e5928: cmp             w0, NULL
    // 0x9e592c: b.ne            #0x9e5938
    // 0x9e5930: d1 = 1.000000
    //     0x9e5930: fmov            d1, #1.00000000
    // 0x9e5934: b               #0x9e5940
    // 0x9e5938: LoadField: d0 = r0->field_7
    //     0x9e5938: ldur            d0, [x0, #7]
    // 0x9e593c: mov             v1.16b, v0.16b
    // 0x9e5940: ldur            d0, [fp, #-0xc0]
    // 0x9e5944: fmul            d2, d0, d1
    // 0x9e5948: r0 = inline_Allocate_Double()
    //     0x9e5948: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x9e594c: add             x0, x0, #0x10
    //     0x9e5950: cmp             x1, x0
    //     0x9e5954: b.ls            #0x9e68ec
    //     0x9e5958: str             x0, [THR, #0x50]  ; THR::top
    //     0x9e595c: sub             x0, x0, #0xf
    //     0x9e5960: movz            x1, #0xe15c
    //     0x9e5964: movk            x1, #0x3, lsl #16
    //     0x9e5968: stur            x1, [x0, #-1]
    // 0x9e596c: StoreField: r0->field_7 = d2
    //     0x9e596c: stur            d2, [x0, #7]
    // 0x9e5970: str             x0, [SP]
    // 0x9e5974: ldur            x1, [fp, #-0x60]
    // 0x9e5978: r4 = const [0, 0x2, 0x1, 0x1, opacity, 0x1, null]
    //     0x9e5978: add             x4, PP, #0x44, lsl #12  ; [pp+0x44798] List(7) [0, 0x2, 0x1, 0x1, "opacity", 0x1, Null]
    //     0x9e597c: ldr             x4, [x4, #0x798]
    // 0x9e5980: r0 = copyWith()
    //     0x9e5980: bl              #0xd81ff4  ; [package:flutter/src/widgets/icon_theme_data.dart] IconThemeData::copyWith
    // 0x9e5984: ldur            x1, [fp, #-0x50]
    // 0x9e5988: stur            x0, [fp, #-0xa0]
    // 0x9e598c: r0 = opacity()
    //     0x9e598c: bl              #0x882b30  ; [package:flutter/src/widgets/icon_theme_data.dart] IconThemeData::opacity
    // 0x9e5990: cmp             w0, NULL
    // 0x9e5994: b.ne            #0x9e59a0
    // 0x9e5998: d1 = 1.000000
    //     0x9e5998: fmov            d1, #1.00000000
    // 0x9e599c: b               #0x9e59a8
    // 0x9e59a0: LoadField: d0 = r0->field_7
    //     0x9e59a0: ldur            d0, [x0, #7]
    // 0x9e59a4: mov             v1.16b, v0.16b
    // 0x9e59a8: ldur            d0, [fp, #-0xc0]
    // 0x9e59ac: fmul            d2, d0, d1
    // 0x9e59b0: r0 = inline_Allocate_Double()
    //     0x9e59b0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x9e59b4: add             x0, x0, #0x10
    //     0x9e59b8: cmp             x1, x0
    //     0x9e59bc: b.ls            #0x9e68fc
    //     0x9e59c0: str             x0, [THR, #0x50]  ; THR::top
    //     0x9e59c4: sub             x0, x0, #0xf
    //     0x9e59c8: movz            x1, #0xe15c
    //     0x9e59cc: movk            x1, #0x3, lsl #16
    //     0x9e59d0: stur            x1, [x0, #-1]
    // 0x9e59d4: StoreField: r0->field_7 = d2
    //     0x9e59d4: stur            d2, [x0, #7]
    // 0x9e59d8: str             x0, [SP]
    // 0x9e59dc: ldur            x1, [fp, #-0x50]
    // 0x9e59e0: r4 = const [0, 0x2, 0x1, 0x1, opacity, 0x1, null]
    //     0x9e59e0: add             x4, PP, #0x44, lsl #12  ; [pp+0x44798] List(7) [0, 0x2, 0x1, 0x1, "opacity", 0x1, Null]
    //     0x9e59e4: ldr             x4, [x4, #0x798]
    // 0x9e59e8: r0 = copyWith()
    //     0x9e59e8: bl              #0xd81ff4  ; [package:flutter/src/widgets/icon_theme_data.dart] IconThemeData::copyWith
    // 0x9e59ec: ldur            x5, [fp, #-0xa0]
    // 0x9e59f0: mov             x4, x0
    // 0x9e59f4: ldur            x3, [fp, #-0x98]
    // 0x9e59f8: ldur            x2, [fp, #-0x90]
    // 0x9e59fc: b               #0x9e5a10
    // 0x9e5a00: ldur            x5, [fp, #-0x60]
    // 0x9e5a04: ldur            x4, [fp, #-0x50]
    // 0x9e5a08: ldur            x3, [fp, #-0x88]
    // 0x9e5a0c: ldur            x2, [fp, #-0x40]
    // 0x9e5a10: ldur            x0, [fp, #-8]
    // 0x9e5a14: stur            x5, [fp, #-0x50]
    // 0x9e5a18: stur            x4, [fp, #-0x60]
    // 0x9e5a1c: stur            x3, [fp, #-0x88]
    // 0x9e5a20: stur            x2, [fp, #-0x90]
    // 0x9e5a24: LoadField: r1 = r0->field_b
    //     0x9e5a24: ldur            w1, [x0, #0xb]
    // 0x9e5a28: DecompressPointer r1
    //     0x9e5a28: add             x1, x1, HEAP, lsl #32
    // 0x9e5a2c: cmp             w1, NULL
    // 0x9e5a30: b.eq            #0x9e690c
    // 0x9e5a34: LoadField: r6 = r1->field_b
    //     0x9e5a34: ldur            w6, [x1, #0xb]
    // 0x9e5a38: DecompressPointer r6
    //     0x9e5a38: add             x6, x6, HEAP, lsl #32
    // 0x9e5a3c: stur            x6, [fp, #-0x40]
    // 0x9e5a40: cmp             w6, NULL
    // 0x9e5a44: b.ne            #0x9e5ac4
    // 0x9e5a48: ldur            x1, [fp, #-0x78]
    // 0x9e5a4c: tbz             w1, #4, #0x9e689c
    // 0x9e5a50: ldur            x7, [fp, #-0x48]
    // 0x9e5a54: cmp             w7, NULL
    // 0x9e5a58: b.ne            #0x9e5a64
    // 0x9e5a5c: r0 = Null
    //     0x9e5a5c: mov             x0, NULL
    // 0x9e5a60: b               #0x9e5a90
    // 0x9e5a64: mov             x1, x7
    // 0x9e5a68: r0 = hasActiveRouteBelow()
    //     0x9e5a68: bl              #0x9e8564  ; [package:flutter/src/widgets/navigator.dart] Route::hasActiveRouteBelow
    // 0x9e5a6c: tbnz            w0, #4, #0x9e5a78
    // 0x9e5a70: r0 = true
    //     0x9e5a70: add             x0, NULL, #0x20  ; true
    // 0x9e5a74: b               #0x9e5a90
    // 0x9e5a78: ldur            x0, [fp, #-0x48]
    // 0x9e5a7c: LoadField: r1 = r0->field_4f
    //     0x9e5a7c: ldur            x1, [x0, #0x4f]
    // 0x9e5a80: cmp             x1, #0
    // 0x9e5a84: r16 = true
    //     0x9e5a84: add             x16, NULL, #0x20  ; true
    // 0x9e5a88: r17 = false
    //     0x9e5a88: add             x17, NULL, #0x30  ; false
    // 0x9e5a8c: csel            x0, x16, x17, gt
    // 0x9e5a90: cmp             w0, NULL
    // 0x9e5a94: b.eq            #0x9e5abc
    // 0x9e5a98: tbnz            w0, #4, #0x9e5abc
    // 0x9e5a9c: ldur            x0, [fp, #-0x58]
    // 0x9e5aa0: tbnz            w0, #4, #0x9e5ab0
    // 0x9e5aa4: r0 = Instance_CloseButton
    //     0x9e5aa4: add             x0, PP, #0x44, lsl #12  ; [pp+0x447a0] Obj!CloseButton@e25861
    //     0x9e5aa8: ldr             x0, [x0, #0x7a0]
    // 0x9e5aac: b               #0x9e5ac8
    // 0x9e5ab0: r0 = Instance_BackButton
    //     0x9e5ab0: add             x0, PP, #0x44, lsl #12  ; [pp+0x447a8] Obj!BackButton@e258d1
    //     0x9e5ab4: ldr             x0, [x0, #0x7a8]
    // 0x9e5ab8: b               #0x9e5ac8
    // 0x9e5abc: ldur            x0, [fp, #-0x40]
    // 0x9e5ac0: b               #0x9e5ac8
    // 0x9e5ac4: ldur            x0, [fp, #-0x40]
    // 0x9e5ac8: stur            x0, [fp, #-0x40]
    // 0x9e5acc: cmp             w0, NULL
    // 0x9e5ad0: b.eq            #0x9e5d38
    // 0x9e5ad4: ldur            x2, [fp, #-0x28]
    // 0x9e5ad8: tbnz            w2, #4, #0x9e5cdc
    // 0x9e5adc: ldur            x3, [fp, #-0x68]
    // 0x9e5ae0: cmp             x3, #0xfa1
    // 0x9e5ae4: b.ne            #0x9e5afc
    // 0x9e5ae8: ldur            x4, [fp, #-0x38]
    // 0x9e5aec: LoadField: r1 = r4->field_23
    //     0x9e5aec: ldur            w1, [x4, #0x23]
    // 0x9e5af0: DecompressPointer r1
    //     0x9e5af0: add             x1, x1, HEAP, lsl #32
    // 0x9e5af4: mov             x0, x1
    // 0x9e5af8: b               #0x9e5b94
    // 0x9e5afc: ldur            x4, [fp, #-0x38]
    // 0x9e5b00: cmp             x3, #0xfa2
    // 0x9e5b04: b.ne            #0x9e5b5c
    // 0x9e5b08: mov             x1, x4
    // 0x9e5b0c: LoadField: r0 = r1->field_4b
    //     0x9e5b0c: ldur            w0, [x1, #0x4b]
    // 0x9e5b10: DecompressPointer r0
    //     0x9e5b10: add             x0, x0, HEAP, lsl #32
    // 0x9e5b14: r16 = Sentinel
    //     0x9e5b14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e5b18: cmp             w0, w16
    // 0x9e5b1c: b.ne            #0x9e5b2c
    // 0x9e5b20: r2 = _colors
    //     0x9e5b20: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4d8] Field <_AppBarDefaultsM3@500187611._colors@500187611>: late final (offset: 0x4c)
    //     0x9e5b24: ldr             x2, [x2, #0x4d8]
    // 0x9e5b28: r0 = InitLateFinalInstanceField()
    //     0x9e5b28: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e5b2c: LoadField: r1 = r0->field_7f
    //     0x9e5b2c: ldur            w1, [x0, #0x7f]
    // 0x9e5b30: DecompressPointer r1
    //     0x9e5b30: add             x1, x1, HEAP, lsl #32
    // 0x9e5b34: stur            x1, [fp, #-0x48]
    // 0x9e5b38: r0 = IconThemeData()
    //     0x9e5b38: bl              #0x63d1c0  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x9e5b3c: mov             x1, x0
    // 0x9e5b40: r0 = 24.000000
    //     0x9e5b40: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0x9e5b44: ldr             x0, [x0, #0x368]
    // 0x9e5b48: StoreField: r1->field_7 = r0
    //     0x9e5b48: stur            w0, [x1, #7]
    // 0x9e5b4c: ldur            x2, [fp, #-0x48]
    // 0x9e5b50: StoreField: r1->field_1b = r2
    //     0x9e5b50: stur            w2, [x1, #0x1b]
    // 0x9e5b54: mov             x0, x1
    // 0x9e5b58: b               #0x9e5b94
    // 0x9e5b5c: r0 = 24.000000
    //     0x9e5b5c: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0x9e5b60: ldr             x0, [x0, #0x368]
    // 0x9e5b64: ldur            x1, [fp, #-0x38]
    // 0x9e5b68: LoadField: r0 = r1->field_47
    //     0x9e5b68: ldur            w0, [x1, #0x47]
    // 0x9e5b6c: DecompressPointer r0
    //     0x9e5b6c: add             x0, x0, HEAP, lsl #32
    // 0x9e5b70: r16 = Sentinel
    //     0x9e5b70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e5b74: cmp             w0, w16
    // 0x9e5b78: b.ne            #0x9e5b88
    // 0x9e5b7c: r2 = _theme
    //     0x9e5b7c: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4e8] Field <_AppBarDefaultsM2@500187611._theme@500187611>: late final (offset: 0x48)
    //     0x9e5b80: ldr             x2, [x2, #0x4e8]
    // 0x9e5b84: r0 = InitLateFinalInstanceField()
    //     0x9e5b84: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e5b88: LoadField: r1 = r0->field_83
    //     0x9e5b88: ldur            w1, [x0, #0x83]
    // 0x9e5b8c: DecompressPointer r1
    //     0x9e5b8c: add             x1, x1, HEAP, lsl #32
    // 0x9e5b90: mov             x0, x1
    // 0x9e5b94: ldur            x16, [fp, #-0x50]
    // 0x9e5b98: stp             x0, x16, [SP]
    // 0x9e5b9c: r0 = ==()
    //     0x9e5b9c: bl              #0xd48ab4  ; [package:flutter/src/widgets/icon_theme_data.dart] IconThemeData::==
    // 0x9e5ba0: tbnz            w0, #4, #0x9e5bac
    // 0x9e5ba4: ldur            x2, [fp, #-0x20]
    // 0x9e5ba8: b               #0x9e5c3c
    // 0x9e5bac: ldur            x0, [fp, #-0x20]
    // 0x9e5bb0: ldur            x2, [fp, #-0x50]
    // 0x9e5bb4: LoadField: r1 = r2->field_1b
    //     0x9e5bb4: ldur            w1, [x2, #0x1b]
    // 0x9e5bb8: DecompressPointer r1
    //     0x9e5bb8: add             x1, x1, HEAP, lsl #32
    // 0x9e5bbc: LoadField: r3 = r2->field_7
    //     0x9e5bbc: ldur            w3, [x2, #7]
    // 0x9e5bc0: DecompressPointer r3
    //     0x9e5bc0: add             x3, x3, HEAP, lsl #32
    // 0x9e5bc4: str             x3, [SP]
    // 0x9e5bc8: r4 = const [0, 0x2, 0x1, 0x1, iconSize, 0x1, null]
    //     0x9e5bc8: add             x4, PP, #0x44, lsl #12  ; [pp+0x447b0] List(7) [0, 0x2, 0x1, 0x1, "iconSize", 0x1, Null]
    //     0x9e5bcc: ldr             x4, [x4, #0x7b0]
    // 0x9e5bd0: r0 = styleFrom()
    //     0x9e5bd0: bl              #0x9e80d8  ; [package:flutter/src/material/icon_button.dart] IconButton::styleFrom
    // 0x9e5bd4: mov             x1, x0
    // 0x9e5bd8: ldur            x0, [fp, #-0x20]
    // 0x9e5bdc: LoadField: r2 = r0->field_7
    //     0x9e5bdc: ldur            w2, [x0, #7]
    // 0x9e5be0: DecompressPointer r2
    //     0x9e5be0: add             x2, x2, HEAP, lsl #32
    // 0x9e5be4: cmp             w2, NULL
    // 0x9e5be8: b.ne            #0x9e5bf4
    // 0x9e5bec: r0 = Null
    //     0x9e5bec: mov             x0, NULL
    // 0x9e5bf0: b               #0x9e5c24
    // 0x9e5bf4: LoadField: r3 = r1->field_f
    //     0x9e5bf4: ldur            w3, [x1, #0xf]
    // 0x9e5bf8: DecompressPointer r3
    //     0x9e5bf8: add             x3, x3, HEAP, lsl #32
    // 0x9e5bfc: LoadField: r4 = r1->field_13
    //     0x9e5bfc: ldur            w4, [x1, #0x13]
    // 0x9e5c00: DecompressPointer r4
    //     0x9e5c00: add             x4, x4, HEAP, lsl #32
    // 0x9e5c04: LoadField: r5 = r1->field_37
    //     0x9e5c04: ldur            w5, [x1, #0x37]
    // 0x9e5c08: DecompressPointer r5
    //     0x9e5c08: add             x5, x5, HEAP, lsl #32
    // 0x9e5c0c: stp             x4, x3, [SP, #8]
    // 0x9e5c10: str             x5, [SP]
    // 0x9e5c14: mov             x1, x2
    // 0x9e5c18: r4 = const [0, 0x4, 0x3, 0x1, foregroundColor, 0x1, iconSize, 0x3, overlayColor, 0x2, null]
    //     0x9e5c18: add             x4, PP, #0x44, lsl #12  ; [pp+0x447b8] List(11) [0, 0x4, 0x3, 0x1, "foregroundColor", 0x1, "iconSize", 0x3, "overlayColor", 0x2, Null]
    //     0x9e5c1c: ldr             x4, [x4, #0x7b8]
    // 0x9e5c20: r0 = copyWith()
    //     0x9e5c20: bl              #0x9e7350  ; [package:flutter/src/material/button_style.dart] ButtonStyle::copyWith
    // 0x9e5c24: stur            x0, [fp, #-0x48]
    // 0x9e5c28: r0 = IconButtonThemeData()
    //     0x9e5c28: bl              #0x87fb9c  ; AllocateIconButtonThemeDataStub -> IconButtonThemeData (size=0xc)
    // 0x9e5c2c: mov             x1, x0
    // 0x9e5c30: ldur            x0, [fp, #-0x48]
    // 0x9e5c34: StoreField: r1->field_7 = r0
    //     0x9e5c34: stur            w0, [x1, #7]
    // 0x9e5c38: mov             x2, x1
    // 0x9e5c3c: ldur            x1, [fp, #-8]
    // 0x9e5c40: ldur            x0, [fp, #-0x40]
    // 0x9e5c44: stur            x2, [fp, #-0x48]
    // 0x9e5c48: r0 = Center()
    //     0x9e5c48: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x9e5c4c: mov             x1, x0
    // 0x9e5c50: r0 = Instance_Alignment
    //     0x9e5c50: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x9e5c54: ldr             x0, [x0, #0x898]
    // 0x9e5c58: stur            x1, [fp, #-0x58]
    // 0x9e5c5c: StoreField: r1->field_f = r0
    //     0x9e5c5c: stur            w0, [x1, #0xf]
    // 0x9e5c60: ldur            x0, [fp, #-0x40]
    // 0x9e5c64: StoreField: r1->field_b = r0
    //     0x9e5c64: stur            w0, [x1, #0xb]
    // 0x9e5c68: r0 = IconButtonTheme()
    //     0x9e5c68: bl              #0x9e7344  ; AllocateIconButtonThemeStub -> IconButtonTheme (size=0x14)
    // 0x9e5c6c: mov             x1, x0
    // 0x9e5c70: ldur            x0, [fp, #-0x48]
    // 0x9e5c74: stur            x1, [fp, #-0x78]
    // 0x9e5c78: StoreField: r1->field_f = r0
    //     0x9e5c78: stur            w0, [x1, #0xf]
    // 0x9e5c7c: ldur            x0, [fp, #-0x58]
    // 0x9e5c80: StoreField: r1->field_b = r0
    //     0x9e5c80: stur            w0, [x1, #0xb]
    // 0x9e5c84: ldur            x0, [fp, #-8]
    // 0x9e5c88: LoadField: r2 = r0->field_b
    //     0x9e5c88: ldur            w2, [x0, #0xb]
    // 0x9e5c8c: DecompressPointer r2
    //     0x9e5c8c: add             x2, x2, HEAP, lsl #32
    // 0x9e5c90: cmp             w2, NULL
    // 0x9e5c94: b.eq            #0x9e6910
    // 0x9e5c98: r0 = BoxConstraints()
    //     0x9e5c98: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x9e5c9c: d0 = 56.000000
    //     0x9e5c9c: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0x9e5ca0: ldr             d0, [x17, #0xf60]
    // 0x9e5ca4: stur            x0, [fp, #-0x48]
    // 0x9e5ca8: StoreField: r0->field_7 = d0
    //     0x9e5ca8: stur            d0, [x0, #7]
    // 0x9e5cac: StoreField: r0->field_f = d0
    //     0x9e5cac: stur            d0, [x0, #0xf]
    // 0x9e5cb0: ArrayStore: r0[0] = rZR  ; List_8
    //     0x9e5cb0: stur            xzr, [x0, #0x17]
    // 0x9e5cb4: d0 = inf
    //     0x9e5cb4: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x9e5cb8: StoreField: r0->field_1f = d0
    //     0x9e5cb8: stur            d0, [x0, #0x1f]
    // 0x9e5cbc: r0 = ConstrainedBox()
    //     0x9e5cbc: bl              #0x9d4fe0  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x9e5cc0: mov             x1, x0
    // 0x9e5cc4: ldur            x0, [fp, #-0x48]
    // 0x9e5cc8: StoreField: r1->field_f = r0
    //     0x9e5cc8: stur            w0, [x1, #0xf]
    // 0x9e5ccc: ldur            x0, [fp, #-0x78]
    // 0x9e5cd0: StoreField: r1->field_b = r0
    //     0x9e5cd0: stur            w0, [x1, #0xb]
    // 0x9e5cd4: mov             x0, x1
    // 0x9e5cd8: b               #0x9e5d38
    // 0x9e5cdc: ldur            x1, [fp, #-8]
    // 0x9e5ce0: d0 = 56.000000
    //     0x9e5ce0: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0x9e5ce4: ldr             d0, [x17, #0xf60]
    // 0x9e5ce8: LoadField: r2 = r1->field_b
    //     0x9e5ce8: ldur            w2, [x1, #0xb]
    // 0x9e5cec: DecompressPointer r2
    //     0x9e5cec: add             x2, x2, HEAP, lsl #32
    // 0x9e5cf0: cmp             w2, NULL
    // 0x9e5cf4: b.eq            #0x9e6914
    // 0x9e5cf8: r0 = BoxConstraints()
    //     0x9e5cf8: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x9e5cfc: d0 = 56.000000
    //     0x9e5cfc: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0x9e5d00: ldr             d0, [x17, #0xf60]
    // 0x9e5d04: stur            x0, [fp, #-0x48]
    // 0x9e5d08: StoreField: r0->field_7 = d0
    //     0x9e5d08: stur            d0, [x0, #7]
    // 0x9e5d0c: StoreField: r0->field_f = d0
    //     0x9e5d0c: stur            d0, [x0, #0xf]
    // 0x9e5d10: ArrayStore: r0[0] = rZR  ; List_8
    //     0x9e5d10: stur            xzr, [x0, #0x17]
    // 0x9e5d14: d0 = inf
    //     0x9e5d14: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x9e5d18: StoreField: r0->field_1f = d0
    //     0x9e5d18: stur            d0, [x0, #0x1f]
    // 0x9e5d1c: r0 = ConstrainedBox()
    //     0x9e5d1c: bl              #0x9d4fe0  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x9e5d20: mov             x1, x0
    // 0x9e5d24: ldur            x0, [fp, #-0x48]
    // 0x9e5d28: StoreField: r1->field_f = r0
    //     0x9e5d28: stur            w0, [x1, #0xf]
    // 0x9e5d2c: ldur            x0, [fp, #-0x40]
    // 0x9e5d30: StoreField: r1->field_b = r0
    //     0x9e5d30: stur            w0, [x1, #0xb]
    // 0x9e5d34: mov             x0, x1
    // 0x9e5d38: ldur            x1, [fp, #-8]
    // 0x9e5d3c: stur            x0, [fp, #-0x48]
    // 0x9e5d40: LoadField: r2 = r1->field_b
    //     0x9e5d40: ldur            w2, [x1, #0xb]
    // 0x9e5d44: DecompressPointer r2
    //     0x9e5d44: add             x2, x2, HEAP, lsl #32
    // 0x9e5d48: cmp             w2, NULL
    // 0x9e5d4c: b.eq            #0x9e6918
    // 0x9e5d50: LoadField: r3 = r2->field_13
    //     0x9e5d50: ldur            w3, [x2, #0x13]
    // 0x9e5d54: DecompressPointer r3
    //     0x9e5d54: add             x3, x3, HEAP, lsl #32
    // 0x9e5d58: stur            x3, [fp, #-0x40]
    // 0x9e5d5c: cmp             w3, NULL
    // 0x9e5d60: b.eq            #0x9e5e00
    // 0x9e5d64: ldur            x2, [fp, #-0x90]
    // 0x9e5d68: r0 = _AppBarTitleBox()
    //     0x9e5d68: bl              #0x9e7338  ; Allocate_AppBarTitleBoxStub -> _AppBarTitleBox (size=0x10)
    // 0x9e5d6c: mov             x1, x0
    // 0x9e5d70: ldur            x0, [fp, #-0x40]
    // 0x9e5d74: stur            x1, [fp, #-0x58]
    // 0x9e5d78: StoreField: r1->field_b = r0
    //     0x9e5d78: stur            w0, [x1, #0xb]
    // 0x9e5d7c: r0 = Semantics()
    //     0x9e5d7c: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0x9e5d80: stur            x0, [fp, #-0x78]
    // 0x9e5d84: r16 = true
    //     0x9e5d84: add             x16, NULL, #0x20  ; true
    // 0x9e5d88: r30 = true
    //     0x9e5d88: add             lr, NULL, #0x20  ; true
    // 0x9e5d8c: stp             lr, x16, [SP, #8]
    // 0x9e5d90: ldur            x16, [fp, #-0x58]
    // 0x9e5d94: str             x16, [SP]
    // 0x9e5d98: mov             x1, x0
    // 0x9e5d9c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, header, 0x2, namesRoute, 0x1, null]
    //     0x9e5d9c: add             x4, PP, #0x44, lsl #12  ; [pp+0x447c0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "header", 0x2, "namesRoute", 0x1, Null]
    //     0x9e5da0: ldr             x4, [x4, #0x7c0]
    // 0x9e5da4: r0 = Semantics()
    //     0x9e5da4: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0x9e5da8: ldur            x0, [fp, #-0x90]
    // 0x9e5dac: cmp             w0, NULL
    // 0x9e5db0: b.eq            #0x9e691c
    // 0x9e5db4: r0 = DefaultTextStyle()
    //     0x9e5db4: bl              #0x9d5004  ; AllocateDefaultTextStyleStub -> DefaultTextStyle (size=0x2c)
    // 0x9e5db8: mov             x1, x0
    // 0x9e5dbc: ldur            x0, [fp, #-0x90]
    // 0x9e5dc0: StoreField: r1->field_f = r0
    //     0x9e5dc0: stur            w0, [x1, #0xf]
    // 0x9e5dc4: r0 = false
    //     0x9e5dc4: add             x0, NULL, #0x30  ; false
    // 0x9e5dc8: ArrayStore: r1[0] = r0  ; List_4
    //     0x9e5dc8: stur            w0, [x1, #0x17]
    // 0x9e5dcc: r2 = Instance_TextOverflow
    //     0x9e5dcc: add             x2, PP, #0x27, lsl #12  ; [pp+0x27888] Obj!TextOverflow@e35cc1
    //     0x9e5dd0: ldr             x2, [x2, #0x888]
    // 0x9e5dd4: StoreField: r1->field_1b = r2
    //     0x9e5dd4: stur            w2, [x1, #0x1b]
    // 0x9e5dd8: r2 = Instance_TextWidthBasis
    //     0x9e5dd8: add             x2, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0x9e5ddc: ldr             x2, [x2, #0x1d8]
    // 0x9e5de0: StoreField: r1->field_23 = r2
    //     0x9e5de0: stur            w2, [x1, #0x23]
    // 0x9e5de4: ldur            x3, [fp, #-0x78]
    // 0x9e5de8: StoreField: r1->field_b = r3
    //     0x9e5de8: stur            w3, [x1, #0xb]
    // 0x9e5dec: d0 = 1.340000
    //     0x9e5dec: add             x17, PP, #0x44, lsl #12  ; [pp+0x447c8] IMM: double(1.34) from 0x3ff570a3d70a3d71
    //     0x9e5df0: ldr             d0, [x17, #0x7c8]
    // 0x9e5df4: r0 = withClampedTextScaling()
    //     0x9e5df4: bl              #0x9e6d78  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::withClampedTextScaling
    // 0x9e5df8: mov             x3, x0
    // 0x9e5dfc: b               #0x9e5e08
    // 0x9e5e00: mov             x0, x3
    // 0x9e5e04: mov             x3, x0
    // 0x9e5e08: ldur            x2, [fp, #-8]
    // 0x9e5e0c: stur            x3, [fp, #-0x40]
    // 0x9e5e10: LoadField: r0 = r2->field_b
    //     0x9e5e10: ldur            w0, [x2, #0xb]
    // 0x9e5e14: DecompressPointer r0
    //     0x9e5e14: add             x0, x0, HEAP, lsl #32
    // 0x9e5e18: cmp             w0, NULL
    // 0x9e5e1c: b.eq            #0x9e6920
    // 0x9e5e20: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9e5e20: ldur            w1, [x0, #0x17]
    // 0x9e5e24: DecompressPointer r1
    //     0x9e5e24: add             x1, x1, HEAP, lsl #32
    // 0x9e5e28: cmp             w1, NULL
    // 0x9e5e2c: b.eq            #0x9e5f04
    // 0x9e5e30: r0 = LoadClassIdInstr(r1)
    //     0x9e5e30: ldur            x0, [x1, #-1]
    //     0x9e5e34: ubfx            x0, x0, #0xc, #0x14
    // 0x9e5e38: r0 = GDT[cid_x0 + 0xd488]()
    //     0x9e5e38: movz            x17, #0xd488
    //     0x9e5e3c: add             lr, x0, x17
    //     0x9e5e40: ldr             lr, [x21, lr, lsl #3]
    //     0x9e5e44: blr             lr
    // 0x9e5e48: tbnz            w0, #4, #0x9e5ef0
    // 0x9e5e4c: ldur            x0, [fp, #-0x28]
    // 0x9e5e50: tbnz            w0, #4, #0x9e5e60
    // 0x9e5e54: r2 = Instance_CrossAxisAlignment
    //     0x9e5e54: add             x2, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0x9e5e58: ldr             x2, [x2, #0x740]
    // 0x9e5e5c: b               #0x9e5e68
    // 0x9e5e60: r2 = Instance_CrossAxisAlignment
    //     0x9e5e60: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0x9e5e64: ldr             x2, [x2, #0xf50]
    // 0x9e5e68: ldur            x1, [fp, #-8]
    // 0x9e5e6c: stur            x2, [fp, #-0x78]
    // 0x9e5e70: LoadField: r3 = r1->field_b
    //     0x9e5e70: ldur            w3, [x1, #0xb]
    // 0x9e5e74: DecompressPointer r3
    //     0x9e5e74: add             x3, x3, HEAP, lsl #32
    // 0x9e5e78: cmp             w3, NULL
    // 0x9e5e7c: b.eq            #0x9e6924
    // 0x9e5e80: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x9e5e80: ldur            w4, [x3, #0x17]
    // 0x9e5e84: DecompressPointer r4
    //     0x9e5e84: add             x4, x4, HEAP, lsl #32
    // 0x9e5e88: stur            x4, [fp, #-0x58]
    // 0x9e5e8c: cmp             w4, NULL
    // 0x9e5e90: b.eq            #0x9e6928
    // 0x9e5e94: r0 = Row()
    //     0x9e5e94: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9e5e98: mov             x1, x0
    // 0x9e5e9c: r0 = Instance_Axis
    //     0x9e5e9c: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x9e5ea0: StoreField: r1->field_f = r0
    //     0x9e5ea0: stur            w0, [x1, #0xf]
    // 0x9e5ea4: r0 = Instance_MainAxisAlignment
    //     0x9e5ea4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0x9e5ea8: ldr             x0, [x0, #0x730]
    // 0x9e5eac: StoreField: r1->field_13 = r0
    //     0x9e5eac: stur            w0, [x1, #0x13]
    // 0x9e5eb0: r0 = Instance_MainAxisSize
    //     0x9e5eb0: add             x0, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0x9e5eb4: ldr             x0, [x0, #0xe88]
    // 0x9e5eb8: ArrayStore: r1[0] = r0  ; List_4
    //     0x9e5eb8: stur            w0, [x1, #0x17]
    // 0x9e5ebc: ldur            x0, [fp, #-0x78]
    // 0x9e5ec0: StoreField: r1->field_1b = r0
    //     0x9e5ec0: stur            w0, [x1, #0x1b]
    // 0x9e5ec4: r0 = Instance_VerticalDirection
    //     0x9e5ec4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0x9e5ec8: ldr             x0, [x0, #0x748]
    // 0x9e5ecc: StoreField: r1->field_23 = r0
    //     0x9e5ecc: stur            w0, [x1, #0x23]
    // 0x9e5ed0: r2 = Instance_Clip
    //     0x9e5ed0: add             x2, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x9e5ed4: ldr             x2, [x2, #0x750]
    // 0x9e5ed8: StoreField: r1->field_2b = r2
    //     0x9e5ed8: stur            w2, [x1, #0x2b]
    // 0x9e5edc: StoreField: r1->field_2f = rZR
    //     0x9e5edc: stur            xzr, [x1, #0x2f]
    // 0x9e5ee0: ldur            x3, [fp, #-0x58]
    // 0x9e5ee4: StoreField: r1->field_b = r3
    //     0x9e5ee4: stur            w3, [x1, #0xb]
    // 0x9e5ee8: mov             x3, x1
    // 0x9e5eec: b               #0x9e5f20
    // 0x9e5ef0: r2 = Instance_Clip
    //     0x9e5ef0: add             x2, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x9e5ef4: ldr             x2, [x2, #0x750]
    // 0x9e5ef8: r0 = Instance_VerticalDirection
    //     0x9e5ef8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0x9e5efc: ldr             x0, [x0, #0x748]
    // 0x9e5f00: b               #0x9e5f14
    // 0x9e5f04: r2 = Instance_Clip
    //     0x9e5f04: add             x2, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x9e5f08: ldr             x2, [x2, #0x750]
    // 0x9e5f0c: r0 = Instance_VerticalDirection
    //     0x9e5f0c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0x9e5f10: ldr             x0, [x0, #0x748]
    // 0x9e5f14: ldur            x1, [fp, #-0x70]
    // 0x9e5f18: tbz             w1, #4, #0x9e68a8
    // 0x9e5f1c: r3 = Null
    //     0x9e5f1c: mov             x3, NULL
    // 0x9e5f20: stur            x3, [fp, #-0x58]
    // 0x9e5f24: cmp             w3, NULL
    // 0x9e5f28: b.eq            #0x9e6098
    // 0x9e5f2c: ldur            x4, [fp, #-0x68]
    // 0x9e5f30: cmp             x4, #0xfa1
    // 0x9e5f34: b.eq            #0x9e5fb0
    // 0x9e5f38: cmp             x4, #0xfa2
    // 0x9e5f3c: b.ne            #0x9e5fb0
    // 0x9e5f40: ldur            x1, [fp, #-0x38]
    // 0x9e5f44: LoadField: r0 = r1->field_4b
    //     0x9e5f44: ldur            w0, [x1, #0x4b]
    // 0x9e5f48: DecompressPointer r0
    //     0x9e5f48: add             x0, x0, HEAP, lsl #32
    // 0x9e5f4c: r16 = Sentinel
    //     0x9e5f4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9e5f50: cmp             w0, w16
    // 0x9e5f54: b.ne            #0x9e5f64
    // 0x9e5f58: r2 = _colors
    //     0x9e5f58: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4d8] Field <_AppBarDefaultsM3@500187611._colors@500187611>: late final (offset: 0x4c)
    //     0x9e5f5c: ldr             x2, [x2, #0x4d8]
    // 0x9e5f60: r0 = InitLateFinalInstanceField()
    //     0x9e5f60: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9e5f64: LoadField: r1 = r0->field_a3
    //     0x9e5f64: ldur            w1, [x0, #0xa3]
    // 0x9e5f68: DecompressPointer r1
    //     0x9e5f68: add             x1, x1, HEAP, lsl #32
    // 0x9e5f6c: cmp             w1, NULL
    // 0x9e5f70: b.ne            #0x9e5f84
    // 0x9e5f74: LoadField: r1 = r0->field_7f
    //     0x9e5f74: ldur            w1, [x0, #0x7f]
    // 0x9e5f78: DecompressPointer r1
    //     0x9e5f78: add             x1, x1, HEAP, lsl #32
    // 0x9e5f7c: mov             x0, x1
    // 0x9e5f80: b               #0x9e5f88
    // 0x9e5f84: mov             x0, x1
    // 0x9e5f88: stur            x0, [fp, #-0x70]
    // 0x9e5f8c: r0 = IconThemeData()
    //     0x9e5f8c: bl              #0x63d1c0  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x9e5f90: mov             x1, x0
    // 0x9e5f94: r0 = 24.000000
    //     0x9e5f94: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0x9e5f98: ldr             x0, [x0, #0x368]
    // 0x9e5f9c: StoreField: r1->field_7 = r0
    //     0x9e5f9c: stur            w0, [x1, #7]
    // 0x9e5fa0: ldur            x0, [fp, #-0x70]
    // 0x9e5fa4: StoreField: r1->field_1b = r0
    //     0x9e5fa4: stur            w0, [x1, #0x1b]
    // 0x9e5fa8: ldur            x0, [fp, #-0x38]
    // 0x9e5fac: b               #0x9e5fbc
    // 0x9e5fb0: ldur            x0, [fp, #-0x38]
    // 0x9e5fb4: LoadField: r1 = r0->field_27
    //     0x9e5fb4: ldur            w1, [x0, #0x27]
    // 0x9e5fb8: DecompressPointer r1
    //     0x9e5fb8: add             x1, x1, HEAP, lsl #32
    // 0x9e5fbc: ldur            x16, [fp, #-0x60]
    // 0x9e5fc0: stp             x1, x16, [SP]
    // 0x9e5fc4: r0 = ==()
    //     0x9e5fc4: bl              #0xd48ab4  ; [package:flutter/src/widgets/icon_theme_data.dart] IconThemeData::==
    // 0x9e5fc8: tbnz            w0, #4, #0x9e5fd4
    // 0x9e5fcc: ldur            x0, [fp, #-0x20]
    // 0x9e5fd0: b               #0x9e6064
    // 0x9e5fd4: ldur            x0, [fp, #-0x20]
    // 0x9e5fd8: ldur            x2, [fp, #-0x60]
    // 0x9e5fdc: LoadField: r1 = r2->field_1b
    //     0x9e5fdc: ldur            w1, [x2, #0x1b]
    // 0x9e5fe0: DecompressPointer r1
    //     0x9e5fe0: add             x1, x1, HEAP, lsl #32
    // 0x9e5fe4: LoadField: r3 = r2->field_7
    //     0x9e5fe4: ldur            w3, [x2, #7]
    // 0x9e5fe8: DecompressPointer r3
    //     0x9e5fe8: add             x3, x3, HEAP, lsl #32
    // 0x9e5fec: str             x3, [SP]
    // 0x9e5ff0: r4 = const [0, 0x2, 0x1, 0x1, iconSize, 0x1, null]
    //     0x9e5ff0: add             x4, PP, #0x44, lsl #12  ; [pp+0x447b0] List(7) [0, 0x2, 0x1, 0x1, "iconSize", 0x1, Null]
    //     0x9e5ff4: ldr             x4, [x4, #0x7b0]
    // 0x9e5ff8: r0 = styleFrom()
    //     0x9e5ff8: bl              #0x9e80d8  ; [package:flutter/src/material/icon_button.dart] IconButton::styleFrom
    // 0x9e5ffc: mov             x1, x0
    // 0x9e6000: ldur            x0, [fp, #-0x20]
    // 0x9e6004: LoadField: r2 = r0->field_7
    //     0x9e6004: ldur            w2, [x0, #7]
    // 0x9e6008: DecompressPointer r2
    //     0x9e6008: add             x2, x2, HEAP, lsl #32
    // 0x9e600c: cmp             w2, NULL
    // 0x9e6010: b.ne            #0x9e601c
    // 0x9e6014: r0 = Null
    //     0x9e6014: mov             x0, NULL
    // 0x9e6018: b               #0x9e604c
    // 0x9e601c: LoadField: r0 = r1->field_f
    //     0x9e601c: ldur            w0, [x1, #0xf]
    // 0x9e6020: DecompressPointer r0
    //     0x9e6020: add             x0, x0, HEAP, lsl #32
    // 0x9e6024: LoadField: r3 = r1->field_13
    //     0x9e6024: ldur            w3, [x1, #0x13]
    // 0x9e6028: DecompressPointer r3
    //     0x9e6028: add             x3, x3, HEAP, lsl #32
    // 0x9e602c: LoadField: r4 = r1->field_37
    //     0x9e602c: ldur            w4, [x1, #0x37]
    // 0x9e6030: DecompressPointer r4
    //     0x9e6030: add             x4, x4, HEAP, lsl #32
    // 0x9e6034: stp             x3, x0, [SP, #8]
    // 0x9e6038: str             x4, [SP]
    // 0x9e603c: mov             x1, x2
    // 0x9e6040: r4 = const [0, 0x4, 0x3, 0x1, foregroundColor, 0x1, iconSize, 0x3, overlayColor, 0x2, null]
    //     0x9e6040: add             x4, PP, #0x44, lsl #12  ; [pp+0x447b8] List(11) [0, 0x4, 0x3, 0x1, "foregroundColor", 0x1, "iconSize", 0x3, "overlayColor", 0x2, Null]
    //     0x9e6044: ldr             x4, [x4, #0x7b8]
    // 0x9e6048: r0 = copyWith()
    //     0x9e6048: bl              #0x9e7350  ; [package:flutter/src/material/button_style.dart] ButtonStyle::copyWith
    // 0x9e604c: stur            x0, [fp, #-0x20]
    // 0x9e6050: r0 = IconButtonThemeData()
    //     0x9e6050: bl              #0x87fb9c  ; AllocateIconButtonThemeDataStub -> IconButtonThemeData (size=0xc)
    // 0x9e6054: mov             x1, x0
    // 0x9e6058: ldur            x0, [fp, #-0x20]
    // 0x9e605c: StoreField: r1->field_7 = r0
    //     0x9e605c: stur            w0, [x1, #7]
    // 0x9e6060: mov             x0, x1
    // 0x9e6064: ldur            x1, [fp, #-0x58]
    // 0x9e6068: ldur            x2, [fp, #-0x60]
    // 0x9e606c: stur            x0, [fp, #-0x20]
    // 0x9e6070: r0 = merge()
    //     0x9e6070: bl              #0x9e6b88  ; [package:flutter/src/widgets/icon_theme.dart] IconTheme::merge
    // 0x9e6074: stur            x0, [fp, #-0x60]
    // 0x9e6078: r0 = IconButtonTheme()
    //     0x9e6078: bl              #0x9e7344  ; AllocateIconButtonThemeStub -> IconButtonTheme (size=0x14)
    // 0x9e607c: mov             x1, x0
    // 0x9e6080: ldur            x0, [fp, #-0x20]
    // 0x9e6084: StoreField: r1->field_f = r0
    //     0x9e6084: stur            w0, [x1, #0xf]
    // 0x9e6088: ldur            x0, [fp, #-0x60]
    // 0x9e608c: StoreField: r1->field_b = r0
    //     0x9e608c: stur            w0, [x1, #0xb]
    // 0x9e6090: mov             x3, x1
    // 0x9e6094: b               #0x9e609c
    // 0x9e6098: ldur            x3, [fp, #-0x58]
    // 0x9e609c: ldur            x0, [fp, #-8]
    // 0x9e60a0: stur            x3, [fp, #-0x20]
    // 0x9e60a4: LoadField: r1 = r0->field_b
    //     0x9e60a4: ldur            w1, [x0, #0xb]
    // 0x9e60a8: DecompressPointer r1
    //     0x9e60a8: add             x1, x1, HEAP, lsl #32
    // 0x9e60ac: cmp             w1, NULL
    // 0x9e60b0: b.eq            #0x9e692c
    // 0x9e60b4: ldur            x2, [fp, #-0x18]
    // 0x9e60b8: r0 = _getEffectiveCenterTitle()
    //     0x9e60b8: bl              #0x9e6aa4  ; [package:flutter/src/material/app_bar.dart] AppBar::_getEffectiveCenterTitle
    // 0x9e60bc: ldur            x1, [fp, #-8]
    // 0x9e60c0: stur            x0, [fp, #-0x58]
    // 0x9e60c4: LoadField: r2 = r1->field_b
    //     0x9e60c4: ldur            w2, [x1, #0xb]
    // 0x9e60c8: DecompressPointer r2
    //     0x9e60c8: add             x2, x2, HEAP, lsl #32
    // 0x9e60cc: cmp             w2, NULL
    // 0x9e60d0: b.eq            #0x9e6930
    // 0x9e60d4: LoadField: r3 = r2->field_57
    //     0x9e60d4: ldur            w3, [x2, #0x57]
    // 0x9e60d8: DecompressPointer r3
    //     0x9e60d8: add             x3, x3, HEAP, lsl #32
    // 0x9e60dc: cmp             w3, NULL
    // 0x9e60e0: b.ne            #0x9e60f4
    // 0x9e60e4: ldur            x2, [fp, #-0x30]
    // 0x9e60e8: LoadField: r3 = r2->field_2f
    //     0x9e60e8: ldur            w3, [x2, #0x2f]
    // 0x9e60ec: DecompressPointer r3
    //     0x9e60ec: add             x3, x3, HEAP, lsl #32
    // 0x9e60f0: b               #0x9e60f8
    // 0x9e60f4: ldur            x2, [fp, #-0x30]
    // 0x9e60f8: cmp             w3, NULL
    // 0x9e60fc: b.ne            #0x9e6108
    // 0x9e6100: d1 = 16.000000
    //     0x9e6100: fmov            d1, #16.00000000
    // 0x9e6104: b               #0x9e6110
    // 0x9e6108: LoadField: d0 = r3->field_7
    //     0x9e6108: ldur            d0, [x3, #7]
    // 0x9e610c: mov             v1.16b, v0.16b
    // 0x9e6110: ldur            d0, [fp, #-0xb8]
    // 0x9e6114: ldur            x6, [fp, #-0x88]
    // 0x9e6118: ldur            x5, [fp, #-0x48]
    // 0x9e611c: ldur            x4, [fp, #-0x40]
    // 0x9e6120: ldur            x3, [fp, #-0x20]
    // 0x9e6124: stur            d1, [fp, #-0xc0]
    // 0x9e6128: r0 = NavigationToolbar()
    //     0x9e6128: bl              #0x9e6a98  ; AllocateNavigationToolbarStub -> NavigationToolbar (size=0x24)
    // 0x9e612c: mov             x1, x0
    // 0x9e6130: ldur            x0, [fp, #-0x48]
    // 0x9e6134: stur            x1, [fp, #-0x60]
    // 0x9e6138: StoreField: r1->field_b = r0
    //     0x9e6138: stur            w0, [x1, #0xb]
    // 0x9e613c: ldur            x0, [fp, #-0x40]
    // 0x9e6140: StoreField: r1->field_f = r0
    //     0x9e6140: stur            w0, [x1, #0xf]
    // 0x9e6144: ldur            x0, [fp, #-0x20]
    // 0x9e6148: StoreField: r1->field_13 = r0
    //     0x9e6148: stur            w0, [x1, #0x13]
    // 0x9e614c: ldur            x0, [fp, #-0x58]
    // 0x9e6150: ArrayStore: r1[0] = r0  ; List_4
    //     0x9e6150: stur            w0, [x1, #0x17]
    // 0x9e6154: ldur            d0, [fp, #-0xc0]
    // 0x9e6158: StoreField: r1->field_1b = d0
    //     0x9e6158: stur            d0, [x1, #0x1b]
    // 0x9e615c: r0 = _ToolbarContainerLayout()
    //     0x9e615c: bl              #0x9e6a8c  ; Allocate_ToolbarContainerLayoutStub -> _ToolbarContainerLayout (size=0x14)
    // 0x9e6160: ldur            d0, [fp, #-0xb8]
    // 0x9e6164: stur            x0, [fp, #-0x20]
    // 0x9e6168: StoreField: r0->field_b = d0
    //     0x9e6168: stur            d0, [x0, #0xb]
    // 0x9e616c: ldur            x1, [fp, #-0x88]
    // 0x9e6170: cmp             w1, NULL
    // 0x9e6174: b.eq            #0x9e6934
    // 0x9e6178: r0 = DefaultTextStyle()
    //     0x9e6178: bl              #0x9d5004  ; AllocateDefaultTextStyleStub -> DefaultTextStyle (size=0x2c)
    // 0x9e617c: mov             x1, x0
    // 0x9e6180: ldur            x0, [fp, #-0x88]
    // 0x9e6184: StoreField: r1->field_f = r0
    //     0x9e6184: stur            w0, [x1, #0xf]
    // 0x9e6188: r0 = true
    //     0x9e6188: add             x0, NULL, #0x20  ; true
    // 0x9e618c: ArrayStore: r1[0] = r0  ; List_4
    //     0x9e618c: stur            w0, [x1, #0x17]
    // 0x9e6190: r2 = Instance_TextOverflow
    //     0x9e6190: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0x9e6194: ldr             x2, [x2, #0xc60]
    // 0x9e6198: StoreField: r1->field_1b = r2
    //     0x9e6198: stur            w2, [x1, #0x1b]
    // 0x9e619c: r2 = Instance_TextWidthBasis
    //     0x9e619c: add             x2, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0x9e61a0: ldr             x2, [x2, #0x1d8]
    // 0x9e61a4: StoreField: r1->field_23 = r2
    //     0x9e61a4: stur            w2, [x1, #0x23]
    // 0x9e61a8: ldur            x2, [fp, #-0x60]
    // 0x9e61ac: StoreField: r1->field_b = r2
    //     0x9e61ac: stur            w2, [x1, #0xb]
    // 0x9e61b0: ldur            x2, [fp, #-0x50]
    // 0x9e61b4: r0 = merge()
    //     0x9e61b4: bl              #0x9e6b88  ; [package:flutter/src/widgets/icon_theme.dart] IconTheme::merge
    // 0x9e61b8: stur            x0, [fp, #-0x40]
    // 0x9e61bc: r0 = CustomSingleChildLayout()
    //     0x9e61bc: bl              #0x9e6a80  ; AllocateCustomSingleChildLayoutStub -> CustomSingleChildLayout (size=0x14)
    // 0x9e61c0: mov             x1, x0
    // 0x9e61c4: ldur            x0, [fp, #-0x20]
    // 0x9e61c8: stur            x1, [fp, #-0x48]
    // 0x9e61cc: StoreField: r1->field_f = r0
    //     0x9e61cc: stur            w0, [x1, #0xf]
    // 0x9e61d0: ldur            x0, [fp, #-0x40]
    // 0x9e61d4: StoreField: r1->field_b = r0
    //     0x9e61d4: stur            w0, [x1, #0xb]
    // 0x9e61d8: r0 = ClipRect()
    //     0x9e61d8: bl              #0x9e6a74  ; AllocateClipRectStub -> ClipRect (size=0x18)
    // 0x9e61dc: mov             x1, x0
    // 0x9e61e0: r0 = Instance_Clip
    //     0x9e61e0: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0x9e61e4: ldr             x0, [x0, #0x7c0]
    // 0x9e61e8: stur            x1, [fp, #-0x40]
    // 0x9e61ec: StoreField: r1->field_13 = r0
    //     0x9e61ec: stur            w0, [x1, #0x13]
    // 0x9e61f0: ldur            x2, [fp, #-0x48]
    // 0x9e61f4: StoreField: r1->field_b = r2
    //     0x9e61f4: stur            w2, [x1, #0xb]
    // 0x9e61f8: ldur            x2, [fp, #-8]
    // 0x9e61fc: LoadField: r3 = r2->field_b
    //     0x9e61fc: ldur            w3, [x2, #0xb]
    // 0x9e6200: DecompressPointer r3
    //     0x9e6200: add             x3, x3, HEAP, lsl #32
    // 0x9e6204: cmp             w3, NULL
    // 0x9e6208: b.eq            #0x9e6938
    // 0x9e620c: LoadField: r4 = r3->field_1f
    //     0x9e620c: ldur            w4, [x3, #0x1f]
    // 0x9e6210: DecompressPointer r4
    //     0x9e6210: add             x4, x4, HEAP, lsl #32
    // 0x9e6214: stur            x4, [fp, #-0x20]
    // 0x9e6218: cmp             w4, NULL
    // 0x9e621c: b.eq            #0x9e6450
    // 0x9e6220: ldur            d0, [fp, #-0xb8]
    // 0x9e6224: r0 = BoxConstraints()
    //     0x9e6224: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x9e6228: stur            x0, [fp, #-0x48]
    // 0x9e622c: StoreField: r0->field_7 = rZR
    //     0x9e622c: stur            xzr, [x0, #7]
    // 0x9e6230: d0 = inf
    //     0x9e6230: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x9e6234: StoreField: r0->field_f = d0
    //     0x9e6234: stur            d0, [x0, #0xf]
    // 0x9e6238: ArrayStore: r0[0] = rZR  ; List_8
    //     0x9e6238: stur            xzr, [x0, #0x17]
    // 0x9e623c: ldur            d0, [fp, #-0xb8]
    // 0x9e6240: StoreField: r0->field_1f = d0
    //     0x9e6240: stur            d0, [x0, #0x1f]
    // 0x9e6244: r0 = ConstrainedBox()
    //     0x9e6244: bl              #0x9d4fe0  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x9e6248: mov             x2, x0
    // 0x9e624c: ldur            x0, [fp, #-0x48]
    // 0x9e6250: stur            x2, [fp, #-0x50]
    // 0x9e6254: StoreField: r2->field_f = r0
    //     0x9e6254: stur            w0, [x2, #0xf]
    // 0x9e6258: ldur            x0, [fp, #-0x40]
    // 0x9e625c: StoreField: r2->field_b = r0
    //     0x9e625c: stur            w0, [x2, #0xb]
    // 0x9e6260: r1 = <FlexParentData>
    //     0x9e6260: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0x9e6264: ldr             x1, [x1, #0x720]
    // 0x9e6268: r0 = Flexible()
    //     0x9e6268: bl              #0x9e6a68  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x9e626c: mov             x3, x0
    // 0x9e6270: r0 = 1
    //     0x9e6270: movz            x0, #0x1
    // 0x9e6274: stur            x3, [fp, #-0x48]
    // 0x9e6278: StoreField: r3->field_13 = r0
    //     0x9e6278: stur            x0, [x3, #0x13]
    // 0x9e627c: r0 = Instance_FlexFit
    //     0x9e627c: add             x0, PP, #0x29, lsl #12  ; [pp+0x29d68] Obj!FlexFit@e35b61
    //     0x9e6280: ldr             x0, [x0, #0xd68]
    // 0x9e6284: StoreField: r3->field_1b = r0
    //     0x9e6284: stur            w0, [x3, #0x1b]
    // 0x9e6288: ldur            x0, [fp, #-0x50]
    // 0x9e628c: StoreField: r3->field_b = r0
    //     0x9e628c: stur            w0, [x3, #0xb]
    // 0x9e6290: r1 = Null
    //     0x9e6290: mov             x1, NULL
    // 0x9e6294: r2 = 2
    //     0x9e6294: movz            x2, #0x2
    // 0x9e6298: r0 = AllocateArray()
    //     0x9e6298: bl              #0xec22fc  ; AllocateArrayStub
    // 0x9e629c: mov             x2, x0
    // 0x9e62a0: ldur            x0, [fp, #-0x48]
    // 0x9e62a4: stur            x2, [fp, #-0x50]
    // 0x9e62a8: StoreField: r2->field_f = r0
    //     0x9e62a8: stur            w0, [x2, #0xf]
    // 0x9e62ac: r1 = <Widget>
    //     0x9e62ac: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0x9e62b0: r0 = AllocateGrowableArray()
    //     0x9e62b0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x9e62b4: mov             x2, x0
    // 0x9e62b8: ldur            x0, [fp, #-0x50]
    // 0x9e62bc: stur            x2, [fp, #-0x48]
    // 0x9e62c0: StoreField: r2->field_f = r0
    //     0x9e62c0: stur            w0, [x2, #0xf]
    // 0x9e62c4: r0 = 2
    //     0x9e62c4: movz            x0, #0x2
    // 0x9e62c8: StoreField: r2->field_b = r0
    //     0x9e62c8: stur            w0, [x2, #0xb]
    // 0x9e62cc: d0 = 1.000000
    //     0x9e62cc: fmov            d0, #1.00000000
    // 0x9e62d0: fcmp            d0, d0
    // 0x9e62d4: b.ne            #0x9e6320
    // 0x9e62d8: mov             x1, x2
    // 0x9e62dc: r0 = _growToNextCapacity()
    //     0x9e62dc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9e62e0: ldur            x2, [fp, #-0x48]
    // 0x9e62e4: r3 = 4
    //     0x9e62e4: movz            x3, #0x4
    // 0x9e62e8: StoreField: r2->field_b = r3
    //     0x9e62e8: stur            w3, [x2, #0xb]
    // 0x9e62ec: LoadField: r1 = r2->field_f
    //     0x9e62ec: ldur            w1, [x2, #0xf]
    // 0x9e62f0: DecompressPointer r1
    //     0x9e62f0: add             x1, x1, HEAP, lsl #32
    // 0x9e62f4: ldur            x0, [fp, #-0x20]
    // 0x9e62f8: ArrayStore: r1[1] = r0  ; List_4
    //     0x9e62f8: add             x25, x1, #0x13
    //     0x9e62fc: str             w0, [x25]
    //     0x9e6300: tbz             w0, #0, #0x9e631c
    //     0x9e6304: ldurb           w16, [x1, #-1]
    //     0x9e6308: ldurb           w17, [x0, #-1]
    //     0x9e630c: and             x16, x17, x16, lsr #2
    //     0x9e6310: tst             x16, HEAP, lsr #32
    //     0x9e6314: b.eq            #0x9e631c
    //     0x9e6318: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x9e631c: b               #0x9e63f0
    // 0x9e6320: ldur            x0, [fp, #-8]
    // 0x9e6324: r3 = 4
    //     0x9e6324: movz            x3, #0x4
    // 0x9e6328: r1 = Instance_Interval
    //     0x9e6328: add             x1, PP, #0x44, lsl #12  ; [pp+0x44790] Obj!Interval@e15061
    //     0x9e632c: ldr             x1, [x1, #0x790]
    // 0x9e6330: r0 = transform()
    //     0x9e6330: bl              #0xcd6c40  ; [package:flutter/src/animation/curves.dart] Curve::transform
    // 0x9e6334: ldur            x1, [fp, #-8]
    // 0x9e6338: stur            d0, [fp, #-0xb8]
    // 0x9e633c: LoadField: r0 = r1->field_b
    //     0x9e633c: ldur            w0, [x1, #0xb]
    // 0x9e6340: DecompressPointer r0
    //     0x9e6340: add             x0, x0, HEAP, lsl #32
    // 0x9e6344: cmp             w0, NULL
    // 0x9e6348: b.eq            #0x9e693c
    // 0x9e634c: LoadField: r2 = r0->field_1f
    //     0x9e634c: ldur            w2, [x0, #0x1f]
    // 0x9e6350: DecompressPointer r2
    //     0x9e6350: add             x2, x2, HEAP, lsl #32
    // 0x9e6354: stur            x2, [fp, #-0x20]
    // 0x9e6358: r0 = Opacity()
    //     0x9e6358: bl              #0x9e12d8  ; AllocateOpacityStub -> Opacity (size=0x1c)
    // 0x9e635c: ldur            d0, [fp, #-0xb8]
    // 0x9e6360: stur            x0, [fp, #-0x50]
    // 0x9e6364: StoreField: r0->field_f = d0
    //     0x9e6364: stur            d0, [x0, #0xf]
    // 0x9e6368: r2 = false
    //     0x9e6368: add             x2, NULL, #0x30  ; false
    // 0x9e636c: ArrayStore: r0[0] = r2  ; List_4
    //     0x9e636c: stur            w2, [x0, #0x17]
    // 0x9e6370: ldur            x1, [fp, #-0x20]
    // 0x9e6374: StoreField: r0->field_b = r1
    //     0x9e6374: stur            w1, [x0, #0xb]
    // 0x9e6378: ldur            x3, [fp, #-0x48]
    // 0x9e637c: LoadField: r1 = r3->field_b
    //     0x9e637c: ldur            w1, [x3, #0xb]
    // 0x9e6380: LoadField: r4 = r3->field_f
    //     0x9e6380: ldur            w4, [x3, #0xf]
    // 0x9e6384: DecompressPointer r4
    //     0x9e6384: add             x4, x4, HEAP, lsl #32
    // 0x9e6388: LoadField: r5 = r4->field_b
    //     0x9e6388: ldur            w5, [x4, #0xb]
    // 0x9e638c: r4 = LoadInt32Instr(r1)
    //     0x9e638c: sbfx            x4, x1, #1, #0x1f
    // 0x9e6390: stur            x4, [fp, #-0xa8]
    // 0x9e6394: r1 = LoadInt32Instr(r5)
    //     0x9e6394: sbfx            x1, x5, #1, #0x1f
    // 0x9e6398: cmp             x4, x1
    // 0x9e639c: b.ne            #0x9e63a8
    // 0x9e63a0: mov             x1, x3
    // 0x9e63a4: r0 = _growToNextCapacity()
    //     0x9e63a4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9e63a8: ldur            x2, [fp, #-0x48]
    // 0x9e63ac: ldur            x3, [fp, #-0xa8]
    // 0x9e63b0: add             x0, x3, #1
    // 0x9e63b4: lsl             x1, x0, #1
    // 0x9e63b8: StoreField: r2->field_b = r1
    //     0x9e63b8: stur            w1, [x2, #0xb]
    // 0x9e63bc: LoadField: r1 = r2->field_f
    //     0x9e63bc: ldur            w1, [x2, #0xf]
    // 0x9e63c0: DecompressPointer r1
    //     0x9e63c0: add             x1, x1, HEAP, lsl #32
    // 0x9e63c4: ldur            x0, [fp, #-0x50]
    // 0x9e63c8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9e63c8: add             x25, x1, x3, lsl #2
    //     0x9e63cc: add             x25, x25, #0xf
    //     0x9e63d0: str             w0, [x25]
    //     0x9e63d4: tbz             w0, #0, #0x9e63f0
    //     0x9e63d8: ldurb           w16, [x1, #-1]
    //     0x9e63dc: ldurb           w17, [x0, #-1]
    //     0x9e63e0: and             x16, x17, x16, lsr #2
    //     0x9e63e4: tst             x16, HEAP, lsr #32
    //     0x9e63e8: b.eq            #0x9e63f0
    //     0x9e63ec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x9e63f0: r0 = Column()
    //     0x9e63f0: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9e63f4: mov             x1, x0
    // 0x9e63f8: r0 = Instance_Axis
    //     0x9e63f8: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x9e63fc: StoreField: r1->field_f = r0
    //     0x9e63fc: stur            w0, [x1, #0xf]
    // 0x9e6400: r0 = Instance_MainAxisAlignment
    //     0x9e6400: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ae8] Obj!MainAxisAlignment@e35aa1
    //     0x9e6404: ldr             x0, [x0, #0xae8]
    // 0x9e6408: StoreField: r1->field_13 = r0
    //     0x9e6408: stur            w0, [x1, #0x13]
    // 0x9e640c: r0 = Instance_MainAxisSize
    //     0x9e640c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0x9e6410: ldr             x0, [x0, #0x738]
    // 0x9e6414: ArrayStore: r1[0] = r0  ; List_4
    //     0x9e6414: stur            w0, [x1, #0x17]
    // 0x9e6418: r0 = Instance_CrossAxisAlignment
    //     0x9e6418: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0x9e641c: ldr             x0, [x0, #0x740]
    // 0x9e6420: StoreField: r1->field_1b = r0
    //     0x9e6420: stur            w0, [x1, #0x1b]
    // 0x9e6424: r0 = Instance_VerticalDirection
    //     0x9e6424: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0x9e6428: ldr             x0, [x0, #0x748]
    // 0x9e642c: StoreField: r1->field_23 = r0
    //     0x9e642c: stur            w0, [x1, #0x23]
    // 0x9e6430: r2 = Instance_Clip
    //     0x9e6430: add             x2, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x9e6434: ldr             x2, [x2, #0x750]
    // 0x9e6438: StoreField: r1->field_2b = r2
    //     0x9e6438: stur            w2, [x1, #0x2b]
    // 0x9e643c: StoreField: r1->field_2f = rZR
    //     0x9e643c: stur            xzr, [x1, #0x2f]
    // 0x9e6440: ldur            x0, [fp, #-0x48]
    // 0x9e6444: StoreField: r1->field_b = r0
    //     0x9e6444: stur            w0, [x1, #0xb]
    // 0x9e6448: mov             x0, x1
    // 0x9e644c: b               #0x9e645c
    // 0x9e6450: mov             x0, x1
    // 0x9e6454: r2 = Instance_Clip
    //     0x9e6454: add             x2, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x9e6458: ldr             x2, [x2, #0x750]
    // 0x9e645c: ldur            x1, [fp, #-8]
    // 0x9e6460: stur            x0, [fp, #-0x40]
    // 0x9e6464: LoadField: r3 = r1->field_b
    //     0x9e6464: ldur            w3, [x1, #0xb]
    // 0x9e6468: DecompressPointer r3
    //     0x9e6468: add             x3, x3, HEAP, lsl #32
    // 0x9e646c: stur            x3, [fp, #-0x20]
    // 0x9e6470: cmp             w3, NULL
    // 0x9e6474: b.eq            #0x9e6940
    // 0x9e6478: r0 = SafeArea()
    //     0x9e6478: bl              #0x91a5dc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x9e647c: mov             x1, x0
    // 0x9e6480: r0 = true
    //     0x9e6480: add             x0, NULL, #0x20  ; true
    // 0x9e6484: stur            x1, [fp, #-0x48]
    // 0x9e6488: StoreField: r1->field_b = r0
    //     0x9e6488: stur            w0, [x1, #0xb]
    // 0x9e648c: StoreField: r1->field_f = r0
    //     0x9e648c: stur            w0, [x1, #0xf]
    // 0x9e6490: StoreField: r1->field_13 = r0
    //     0x9e6490: stur            w0, [x1, #0x13]
    // 0x9e6494: r2 = false
    //     0x9e6494: add             x2, NULL, #0x30  ; false
    // 0x9e6498: ArrayStore: r1[0] = r2  ; List_4
    //     0x9e6498: stur            w2, [x1, #0x17]
    // 0x9e649c: r3 = Instance_EdgeInsets
    //     0x9e649c: ldr             x3, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0x9e64a0: StoreField: r1->field_1b = r3
    //     0x9e64a0: stur            w3, [x1, #0x1b]
    // 0x9e64a4: StoreField: r1->field_1f = r2
    //     0x9e64a4: stur            w2, [x1, #0x1f]
    // 0x9e64a8: ldur            x2, [fp, #-0x40]
    // 0x9e64ac: StoreField: r1->field_23 = r2
    //     0x9e64ac: stur            w2, [x1, #0x23]
    // 0x9e64b0: r0 = Align()
    //     0x9e64b0: bl              #0x9d4ff8  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x9e64b4: mov             x1, x0
    // 0x9e64b8: r0 = Instance_Alignment
    //     0x9e64b8: add             x0, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!Alignment@e13eb1
    //     0x9e64bc: ldr             x0, [x0, #0xe0]
    // 0x9e64c0: stur            x1, [fp, #-0x50]
    // 0x9e64c4: StoreField: r1->field_f = r0
    //     0x9e64c4: stur            w0, [x1, #0xf]
    // 0x9e64c8: ldur            x0, [fp, #-0x48]
    // 0x9e64cc: StoreField: r1->field_b = r0
    //     0x9e64cc: stur            w0, [x1, #0xb]
    // 0x9e64d0: ldur            x0, [fp, #-0x20]
    // 0x9e64d4: LoadField: r2 = r0->field_1b
    //     0x9e64d4: ldur            w2, [x0, #0x1b]
    // 0x9e64d8: DecompressPointer r2
    //     0x9e64d8: add             x2, x2, HEAP, lsl #32
    // 0x9e64dc: stur            x2, [fp, #-0x40]
    // 0x9e64e0: cmp             w2, NULL
    // 0x9e64e4: b.eq            #0x9e660c
    // 0x9e64e8: r0 = Semantics()
    //     0x9e64e8: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0x9e64ec: stur            x0, [fp, #-0x20]
    // 0x9e64f0: r16 = Instance_OrdinalSortKey
    //     0x9e64f0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22318] Obj!OrdinalSortKey@e18c41
    //     0x9e64f4: ldr             x16, [x16, #0x318]
    // 0x9e64f8: r30 = true
    //     0x9e64f8: add             lr, NULL, #0x20  ; true
    // 0x9e64fc: stp             lr, x16, [SP, #8]
    // 0x9e6500: ldur            x16, [fp, #-0x40]
    // 0x9e6504: str             x16, [SP]
    // 0x9e6508: mov             x1, x0
    // 0x9e650c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, explicitChildNodes, 0x2, sortKey, 0x1, null]
    //     0x9e650c: add             x4, PP, #0x44, lsl #12  ; [pp+0x447d0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "explicitChildNodes", 0x2, "sortKey", 0x1, Null]
    //     0x9e6510: ldr             x4, [x4, #0x7d0]
    // 0x9e6514: r0 = Semantics()
    //     0x9e6514: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0x9e6518: r0 = Material()
    //     0x9e6518: bl              #0x9e6a2c  ; AllocateMaterialStub -> Material (size=0x40)
    // 0x9e651c: mov             x1, x0
    // 0x9e6520: r0 = Instance_MaterialType
    //     0x9e6520: add             x0, PP, #0x44, lsl #12  ; [pp+0x44398] Obj!MaterialType@e36521
    //     0x9e6524: ldr             x0, [x0, #0x398]
    // 0x9e6528: stur            x1, [fp, #-0x40]
    // 0x9e652c: StoreField: r1->field_f = r0
    //     0x9e652c: stur            w0, [x1, #0xf]
    // 0x9e6530: StoreField: r1->field_13 = rZR
    //     0x9e6530: stur            xzr, [x1, #0x13]
    // 0x9e6534: r0 = true
    //     0x9e6534: add             x0, NULL, #0x20  ; true
    // 0x9e6538: StoreField: r1->field_2f = r0
    //     0x9e6538: stur            w0, [x1, #0x2f]
    // 0x9e653c: r2 = Instance_Clip
    //     0x9e653c: add             x2, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x9e6540: ldr             x2, [x2, #0x750]
    // 0x9e6544: StoreField: r1->field_33 = r2
    //     0x9e6544: stur            w2, [x1, #0x33]
    // 0x9e6548: r3 = Instance_Duration
    //     0x9e6548: add             x3, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0x9e654c: ldr             x3, [x3, #0x368]
    // 0x9e6550: StoreField: r1->field_37 = r3
    //     0x9e6550: stur            w3, [x1, #0x37]
    // 0x9e6554: ldur            x4, [fp, #-0x50]
    // 0x9e6558: StoreField: r1->field_b = r4
    //     0x9e6558: stur            w4, [x1, #0xb]
    // 0x9e655c: r0 = Semantics()
    //     0x9e655c: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0x9e6560: stur            x0, [fp, #-0x48]
    // 0x9e6564: r16 = Instance_OrdinalSortKey
    //     0x9e6564: add             x16, PP, #0x22, lsl #12  ; [pp+0x22210] Obj!OrdinalSortKey@e18c21
    //     0x9e6568: ldr             x16, [x16, #0x210]
    // 0x9e656c: r30 = true
    //     0x9e656c: add             lr, NULL, #0x20  ; true
    // 0x9e6570: stp             lr, x16, [SP, #8]
    // 0x9e6574: ldur            x16, [fp, #-0x40]
    // 0x9e6578: str             x16, [SP]
    // 0x9e657c: mov             x1, x0
    // 0x9e6580: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, explicitChildNodes, 0x2, sortKey, 0x1, null]
    //     0x9e6580: add             x4, PP, #0x44, lsl #12  ; [pp+0x447d0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "explicitChildNodes", 0x2, "sortKey", 0x1, Null]
    //     0x9e6584: ldr             x4, [x4, #0x7d0]
    // 0x9e6588: r0 = Semantics()
    //     0x9e6588: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0x9e658c: r1 = Null
    //     0x9e658c: mov             x1, NULL
    // 0x9e6590: r2 = 4
    //     0x9e6590: movz            x2, #0x4
    // 0x9e6594: r0 = AllocateArray()
    //     0x9e6594: bl              #0xec22fc  ; AllocateArrayStub
    // 0x9e6598: mov             x2, x0
    // 0x9e659c: ldur            x0, [fp, #-0x20]
    // 0x9e65a0: stur            x2, [fp, #-0x40]
    // 0x9e65a4: StoreField: r2->field_f = r0
    //     0x9e65a4: stur            w0, [x2, #0xf]
    // 0x9e65a8: ldur            x0, [fp, #-0x48]
    // 0x9e65ac: StoreField: r2->field_13 = r0
    //     0x9e65ac: stur            w0, [x2, #0x13]
    // 0x9e65b0: r1 = <Widget>
    //     0x9e65b0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0x9e65b4: r0 = AllocateGrowableArray()
    //     0x9e65b4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x9e65b8: mov             x1, x0
    // 0x9e65bc: ldur            x0, [fp, #-0x40]
    // 0x9e65c0: stur            x1, [fp, #-0x20]
    // 0x9e65c4: StoreField: r1->field_f = r0
    //     0x9e65c4: stur            w0, [x1, #0xf]
    // 0x9e65c8: r0 = 4
    //     0x9e65c8: movz            x0, #0x4
    // 0x9e65cc: StoreField: r1->field_b = r0
    //     0x9e65cc: stur            w0, [x1, #0xb]
    // 0x9e65d0: r0 = Stack()
    //     0x9e65d0: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0x9e65d4: mov             x1, x0
    // 0x9e65d8: r0 = Instance_AlignmentDirectional
    //     0x9e65d8: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b0] Obj!AlignmentDirectional@e13d31
    //     0x9e65dc: ldr             x0, [x0, #0x7b0]
    // 0x9e65e0: StoreField: r1->field_f = r0
    //     0x9e65e0: stur            w0, [x1, #0xf]
    // 0x9e65e4: r0 = Instance_StackFit
    //     0x9e65e4: add             x0, PP, #0x41, lsl #12  ; [pp+0x41458] Obj!StackFit@e35441
    //     0x9e65e8: ldr             x0, [x0, #0x458]
    // 0x9e65ec: ArrayStore: r1[0] = r0  ; List_4
    //     0x9e65ec: stur            w0, [x1, #0x17]
    // 0x9e65f0: r0 = Instance_Clip
    //     0x9e65f0: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0x9e65f4: ldr             x0, [x0, #0x7c0]
    // 0x9e65f8: StoreField: r1->field_1b = r0
    //     0x9e65f8: stur            w0, [x1, #0x1b]
    // 0x9e65fc: ldur            x0, [fp, #-0x20]
    // 0x9e6600: StoreField: r1->field_b = r0
    //     0x9e6600: stur            w0, [x1, #0xb]
    // 0x9e6604: mov             x2, x1
    // 0x9e6608: b               #0x9e6614
    // 0x9e660c: mov             x4, x1
    // 0x9e6610: mov             x2, x4
    // 0x9e6614: ldur            x0, [fp, #-8]
    // 0x9e6618: stur            x2, [fp, #-0x20]
    // 0x9e661c: LoadField: r1 = r0->field_b
    //     0x9e661c: ldur            w1, [x0, #0xb]
    // 0x9e6620: DecompressPointer r1
    //     0x9e6620: add             x1, x1, HEAP, lsl #32
    // 0x9e6624: cmp             w1, NULL
    // 0x9e6628: b.eq            #0x9e6944
    // 0x9e662c: LoadField: r3 = r1->field_7f
    //     0x9e662c: ldur            w3, [x1, #0x7f]
    // 0x9e6630: DecompressPointer r3
    //     0x9e6630: add             x3, x3, HEAP, lsl #32
    // 0x9e6634: cmp             w3, NULL
    // 0x9e6638: b.ne            #0x9e6644
    // 0x9e663c: r1 = Null
    //     0x9e663c: mov             x1, NULL
    // 0x9e6640: b               #0x9e6648
    // 0x9e6644: mov             x1, x3
    // 0x9e6648: cmp             w1, NULL
    // 0x9e664c: b.ne            #0x9e6654
    // 0x9e6650: r1 = Null
    //     0x9e6650: mov             x1, NULL
    // 0x9e6654: cmp             w1, NULL
    // 0x9e6658: b.ne            #0x9e6690
    // 0x9e665c: ldur            x3, [fp, #-0x28]
    // 0x9e6660: ldur            x1, [fp, #-0x10]
    // 0x9e6664: r0 = estimateBrightnessForColor()
    //     0x9e6664: bl              #0x63de84  ; [package:flutter/src/material/theme_data.dart] ThemeData::estimateBrightnessForColor
    // 0x9e6668: mov             x1, x0
    // 0x9e666c: ldur            x0, [fp, #-0x28]
    // 0x9e6670: tbnz            w0, #4, #0x9e667c
    // 0x9e6674: r3 = Instance_Color
    //     0x9e6674: ldr             x3, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0x9e6678: b               #0x9e6680
    // 0x9e667c: r3 = Null
    //     0x9e667c: mov             x3, NULL
    // 0x9e6680: mov             x2, x1
    // 0x9e6684: ldur            x1, [fp, #-8]
    // 0x9e6688: r0 = _systemOverlayStyleForBrightness()
    //     0x9e6688: bl              #0x9e69bc  ; [package:flutter/src/material/app_bar.dart] _AppBarState::_systemOverlayStyleForBrightness
    // 0x9e668c: mov             x1, x0
    // 0x9e6690: ldur            x0, [fp, #-0x28]
    // 0x9e6694: stur            x1, [fp, #-0x48]
    // 0x9e6698: tbnz            w0, #4, #0x9e66a4
    // 0x9e669c: ldur            x3, [fp, #-0x10]
    // 0x9e66a0: b               #0x9e66a8
    // 0x9e66a4: ldur            x3, [fp, #-0x80]
    // 0x9e66a8: ldur            x2, [fp, #-8]
    // 0x9e66ac: stur            x3, [fp, #-0x40]
    // 0x9e66b0: LoadField: r4 = r2->field_b
    //     0x9e66b0: ldur            w4, [x2, #0xb]
    // 0x9e66b4: DecompressPointer r4
    //     0x9e66b4: add             x4, x4, HEAP, lsl #32
    // 0x9e66b8: cmp             w4, NULL
    // 0x9e66bc: b.eq            #0x9e6948
    // 0x9e66c0: LoadField: r2 = r4->field_2f
    //     0x9e66c0: ldur            w2, [x4, #0x2f]
    // 0x9e66c4: DecompressPointer r2
    //     0x9e66c4: add             x2, x2, HEAP, lsl #32
    // 0x9e66c8: cmp             w2, NULL
    // 0x9e66cc: b.ne            #0x9e66e0
    // 0x9e66d0: ldur            x5, [fp, #-0x30]
    // 0x9e66d4: ArrayLoad: r2 = r5[0]  ; List_4
    //     0x9e66d4: ldur            w2, [x5, #0x17]
    // 0x9e66d8: DecompressPointer r2
    //     0x9e66d8: add             x2, x2, HEAP, lsl #32
    // 0x9e66dc: b               #0x9e66e4
    // 0x9e66e0: ldur            x5, [fp, #-0x30]
    // 0x9e66e4: cmp             w2, NULL
    // 0x9e66e8: b.ne            #0x9e6718
    // 0x9e66ec: ldur            x2, [fp, #-0x68]
    // 0x9e66f0: cmp             x2, #0xfa1
    // 0x9e66f4: b.eq            #0x9e6708
    // 0x9e66f8: cmp             x2, #0xfa2
    // 0x9e66fc: b.ne            #0x9e6708
    // 0x9e6700: r2 = Instance_Color
    //     0x9e6700: ldr             x2, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0x9e6704: b               #0x9e6718
    // 0x9e6708: ldur            x2, [fp, #-0x38]
    // 0x9e670c: ArrayLoad: r6 = r2[0]  ; List_4
    //     0x9e670c: ldur            w6, [x2, #0x17]
    // 0x9e6710: DecompressPointer r6
    //     0x9e6710: add             x6, x6, HEAP, lsl #32
    // 0x9e6714: mov             x2, x6
    // 0x9e6718: stur            x2, [fp, #-0x38]
    // 0x9e671c: LoadField: r6 = r4->field_33
    //     0x9e671c: ldur            w6, [x4, #0x33]
    // 0x9e6720: DecompressPointer r6
    //     0x9e6720: add             x6, x6, HEAP, lsl #32
    // 0x9e6724: cmp             w6, NULL
    // 0x9e6728: b.ne            #0x9e673c
    // 0x9e672c: LoadField: r6 = r5->field_1b
    //     0x9e672c: ldur            w6, [x5, #0x1b]
    // 0x9e6730: DecompressPointer r6
    //     0x9e6730: add             x6, x6, HEAP, lsl #32
    // 0x9e6734: mov             x5, x6
    // 0x9e6738: b               #0x9e6740
    // 0x9e673c: mov             x5, x6
    // 0x9e6740: cmp             w5, NULL
    // 0x9e6744: b.ne            #0x9e677c
    // 0x9e6748: tbnz            w0, #4, #0x9e6774
    // 0x9e674c: ldur            x0, [fp, #-0x18]
    // 0x9e6750: LoadField: r5 = r0->field_3f
    //     0x9e6750: ldur            w5, [x0, #0x3f]
    // 0x9e6754: DecompressPointer r5
    //     0x9e6754: add             x5, x5, HEAP, lsl #32
    // 0x9e6758: LoadField: r0 = r5->field_c3
    //     0x9e6758: ldur            w0, [x5, #0xc3]
    // 0x9e675c: DecompressPointer r0
    //     0x9e675c: add             x0, x0, HEAP, lsl #32
    // 0x9e6760: cmp             w0, NULL
    // 0x9e6764: b.ne            #0x9e6780
    // 0x9e6768: LoadField: r0 = r5->field_b
    //     0x9e6768: ldur            w0, [x5, #0xb]
    // 0x9e676c: DecompressPointer r0
    //     0x9e676c: add             x0, x0, HEAP, lsl #32
    // 0x9e6770: b               #0x9e6780
    // 0x9e6774: r0 = Null
    //     0x9e6774: mov             x0, NULL
    // 0x9e6778: b               #0x9e6780
    // 0x9e677c: mov             x0, x5
    // 0x9e6780: stur            x0, [fp, #-0x10]
    // 0x9e6784: LoadField: r5 = r4->field_37
    //     0x9e6784: ldur            w5, [x4, #0x37]
    // 0x9e6788: DecompressPointer r5
    //     0x9e6788: add             x5, x5, HEAP, lsl #32
    // 0x9e678c: cmp             w5, NULL
    // 0x9e6790: b.ne            #0x9e679c
    // 0x9e6794: r4 = Null
    //     0x9e6794: mov             x4, NULL
    // 0x9e6798: b               #0x9e67a0
    // 0x9e679c: mov             x4, x5
    // 0x9e67a0: cmp             w4, NULL
    // 0x9e67a4: b.ne            #0x9e67ac
    // 0x9e67a8: r4 = Null
    //     0x9e67a8: mov             x4, NULL
    // 0x9e67ac: ldur            d0, [fp, #-0xb0]
    // 0x9e67b0: stur            x4, [fp, #-8]
    // 0x9e67b4: r0 = Semantics()
    //     0x9e67b4: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0x9e67b8: stur            x0, [fp, #-0x18]
    // 0x9e67bc: r16 = true
    //     0x9e67bc: add             x16, NULL, #0x20  ; true
    // 0x9e67c0: ldur            lr, [fp, #-0x20]
    // 0x9e67c4: stp             lr, x16, [SP]
    // 0x9e67c8: mov             x1, x0
    // 0x9e67cc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, explicitChildNodes, 0x1, null]
    //     0x9e67cc: add             x4, PP, #0x39, lsl #12  ; [pp+0x399b0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "explicitChildNodes", 0x1, Null]
    //     0x9e67d0: ldr             x4, [x4, #0x9b0]
    // 0x9e67d4: r0 = Semantics()
    //     0x9e67d4: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0x9e67d8: r0 = Material()
    //     0x9e67d8: bl              #0x9e6a2c  ; AllocateMaterialStub -> Material (size=0x40)
    // 0x9e67dc: mov             x2, x0
    // 0x9e67e0: r0 = Instance_MaterialType
    //     0x9e67e0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2ce18] Obj!MaterialType@e36501
    //     0x9e67e4: ldr             x0, [x0, #0xe18]
    // 0x9e67e8: stur            x2, [fp, #-0x20]
    // 0x9e67ec: StoreField: r2->field_f = r0
    //     0x9e67ec: stur            w0, [x2, #0xf]
    // 0x9e67f0: ldur            d0, [fp, #-0xb0]
    // 0x9e67f4: StoreField: r2->field_13 = d0
    //     0x9e67f4: stur            d0, [x2, #0x13]
    // 0x9e67f8: ldur            x0, [fp, #-0x40]
    // 0x9e67fc: StoreField: r2->field_1b = r0
    //     0x9e67fc: stur            w0, [x2, #0x1b]
    // 0x9e6800: ldur            x0, [fp, #-0x38]
    // 0x9e6804: StoreField: r2->field_1f = r0
    //     0x9e6804: stur            w0, [x2, #0x1f]
    // 0x9e6808: ldur            x0, [fp, #-0x10]
    // 0x9e680c: StoreField: r2->field_23 = r0
    //     0x9e680c: stur            w0, [x2, #0x23]
    // 0x9e6810: ldur            x0, [fp, #-8]
    // 0x9e6814: StoreField: r2->field_2b = r0
    //     0x9e6814: stur            w0, [x2, #0x2b]
    // 0x9e6818: r0 = true
    //     0x9e6818: add             x0, NULL, #0x20  ; true
    // 0x9e681c: StoreField: r2->field_2f = r0
    //     0x9e681c: stur            w0, [x2, #0x2f]
    // 0x9e6820: r1 = Instance_Clip
    //     0x9e6820: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x9e6824: ldr             x1, [x1, #0x750]
    // 0x9e6828: StoreField: r2->field_33 = r1
    //     0x9e6828: stur            w1, [x2, #0x33]
    // 0x9e682c: r1 = Instance_Duration
    //     0x9e682c: add             x1, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0x9e6830: ldr             x1, [x1, #0x368]
    // 0x9e6834: StoreField: r2->field_37 = r1
    //     0x9e6834: stur            w1, [x2, #0x37]
    // 0x9e6838: ldur            x1, [fp, #-0x18]
    // 0x9e683c: StoreField: r2->field_b = r1
    //     0x9e683c: stur            w1, [x2, #0xb]
    // 0x9e6840: r1 = <SystemUiOverlayStyle>
    //     0x9e6840: ldr             x1, [PP, #0x2ac0]  ; [pp+0x2ac0] TypeArguments: <SystemUiOverlayStyle>
    // 0x9e6844: r0 = AnnotatedRegion()
    //     0x9e6844: bl              #0x9e69b0  ; AllocateAnnotatedRegionStub -> AnnotatedRegion<X0> (size=0x1c)
    // 0x9e6848: mov             x1, x0
    // 0x9e684c: ldur            x0, [fp, #-0x48]
    // 0x9e6850: stur            x1, [fp, #-8]
    // 0x9e6854: StoreField: r1->field_13 = r0
    //     0x9e6854: stur            w0, [x1, #0x13]
    // 0x9e6858: r0 = true
    //     0x9e6858: add             x0, NULL, #0x20  ; true
    // 0x9e685c: ArrayStore: r1[0] = r0  ; List_4
    //     0x9e685c: stur            w0, [x1, #0x17]
    // 0x9e6860: ldur            x0, [fp, #-0x20]
    // 0x9e6864: StoreField: r1->field_b = r0
    //     0x9e6864: stur            w0, [x1, #0xb]
    // 0x9e6868: r0 = Semantics()
    //     0x9e6868: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0x9e686c: stur            x0, [fp, #-0x10]
    // 0x9e6870: r16 = true
    //     0x9e6870: add             x16, NULL, #0x20  ; true
    // 0x9e6874: ldur            lr, [fp, #-8]
    // 0x9e6878: stp             lr, x16, [SP]
    // 0x9e687c: mov             x1, x0
    // 0x9e6880: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, container, 0x1, null]
    //     0x9e6880: add             x4, PP, #0x39, lsl #12  ; [pp+0x399c0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "container", 0x1, Null]
    //     0x9e6884: ldr             x4, [x4, #0x9c0]
    // 0x9e6888: r0 = Semantics()
    //     0x9e6888: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0x9e688c: ldur            x0, [fp, #-0x10]
    // 0x9e6890: LeaveFrame
    //     0x9e6890: mov             SP, fp
    //     0x9e6894: ldp             fp, lr, [SP], #0x10
    // 0x9e6898: ret
    //     0x9e6898: ret             
    // 0x9e689c: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x9e689c: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x9e68a0: r0 = Throw()
    //     0x9e68a0: bl              #0xec04b8  ; ThrowStub
    // 0x9e68a4: brk             #0
    // 0x9e68a8: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x9e68a8: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x9e68ac: r0 = Throw()
    //     0x9e68ac: bl              #0xec04b8  ; ThrowStub
    // 0x9e68b0: brk             #0
    // 0x9e68b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e68b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e68b8: b               #0x9e4dc8
    // 0x9e68bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e68bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e68c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e68c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e68c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e68c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e68c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e68c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e68cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e68cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e68d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e68d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e68d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e68d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e68d8: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9e68d8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9e68dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e68dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e68e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e68e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e68e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e68e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e68e8: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9e68e8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9e68ec: stp             q0, q2, [SP, #-0x20]!
    // 0x9e68f0: r0 = AllocateDouble()
    //     0x9e68f0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9e68f4: ldp             q0, q2, [SP], #0x20
    // 0x9e68f8: b               #0x9e596c
    // 0x9e68fc: SaveReg d2
    //     0x9e68fc: str             q2, [SP, #-0x10]!
    // 0x9e6900: r0 = AllocateDouble()
    //     0x9e6900: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9e6904: RestoreReg d2
    //     0x9e6904: ldr             q2, [SP], #0x10
    // 0x9e6908: b               #0x9e59d4
    // 0x9e690c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e690c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e6910: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e6910: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e6914: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9e6914: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9e6918: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e6918: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e691c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e691c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e6920: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e6920: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e6924: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e6924: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e6928: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e6928: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e692c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e692c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e6930: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e6930: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e6934: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9e6934: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9e6938: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e6938: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e693c: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9e693c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9e6940: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e6940: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e6944: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e6944: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9e6948: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9e6948: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _systemOverlayStyleForBrightness(/* No info */) {
    // ** addr: 0x9e69bc, size: 0x70
    // 0x9e69bc: EnterFrame
    //     0x9e69bc: stp             fp, lr, [SP, #-0x10]!
    //     0x9e69c0: mov             fp, SP
    // 0x9e69c4: AllocStack(0x18)
    //     0x9e69c4: sub             SP, SP, #0x18
    // 0x9e69c8: SetupParameters(dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x9e69c8: stur            x3, [fp, #-0x18]
    // 0x9e69cc: r16 = Instance_Brightness
    //     0x9e69cc: ldr             x16, [PP, #0x5428]  ; [pp+0x5428] Obj!Brightness@e39141
    // 0x9e69d0: cmp             w2, w16
    // 0x9e69d4: b.ne            #0x9e69e4
    // 0x9e69d8: r0 = Instance_SystemUiOverlayStyle
    //     0x9e69d8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d200] Obj!SystemUiOverlayStyle@e10e61
    //     0x9e69dc: ldr             x0, [x0, #0x200]
    // 0x9e69e0: b               #0x9e69ec
    // 0x9e69e4: r0 = Instance_SystemUiOverlayStyle
    //     0x9e69e4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d208] Obj!SystemUiOverlayStyle@e10e31
    //     0x9e69e8: ldr             x0, [x0, #0x208]
    // 0x9e69ec: LoadField: r1 = r0->field_1b
    //     0x9e69ec: ldur            w1, [x0, #0x1b]
    // 0x9e69f0: DecompressPointer r1
    //     0x9e69f0: add             x1, x1, HEAP, lsl #32
    // 0x9e69f4: stur            x1, [fp, #-0x10]
    // 0x9e69f8: LoadField: r2 = r0->field_1f
    //     0x9e69f8: ldur            w2, [x0, #0x1f]
    // 0x9e69fc: DecompressPointer r2
    //     0x9e69fc: add             x2, x2, HEAP, lsl #32
    // 0x9e6a00: stur            x2, [fp, #-8]
    // 0x9e6a04: r0 = SystemUiOverlayStyle()
    //     0x9e6a04: bl              #0x6cf32c  ; AllocateSystemUiOverlayStyleStub -> SystemUiOverlayStyle (size=0x28)
    // 0x9e6a08: ldur            x1, [fp, #-0x18]
    // 0x9e6a0c: ArrayStore: r0[0] = r1  ; List_4
    //     0x9e6a0c: stur            w1, [x0, #0x17]
    // 0x9e6a10: ldur            x1, [fp, #-0x10]
    // 0x9e6a14: StoreField: r0->field_1b = r1
    //     0x9e6a14: stur            w1, [x0, #0x1b]
    // 0x9e6a18: ldur            x1, [fp, #-8]
    // 0x9e6a1c: StoreField: r0->field_1f = r1
    //     0x9e6a1c: stur            w1, [x0, #0x1f]
    // 0x9e6a20: LeaveFrame
    //     0x9e6a20: mov             SP, fp
    //     0x9e6a24: ldp             fp, lr, [SP], #0x10
    // 0x9e6a28: ret
    //     0x9e6a28: ret             
  }
  _ _resolveColor(/* No info */) {
    // ** addr: 0x9e8628, size: 0xa0
    // 0x9e8628: EnterFrame
    //     0x9e8628: stp             fp, lr, [SP, #-0x10]!
    //     0x9e862c: mov             fp, SP
    // 0x9e8630: AllocStack(0x30)
    //     0x9e8630: sub             SP, SP, #0x30
    // 0x9e8634: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r5 => r5, fp-0x10 */, dynamic _ /* r6 => r6, fp-0x18 */)
    //     0x9e8634: stur            x2, [fp, #-8]
    //     0x9e8638: stur            x5, [fp, #-0x10]
    //     0x9e863c: stur            x6, [fp, #-0x18]
    // 0x9e8640: CheckStackOverflow
    //     0x9e8640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e8644: cmp             SP, x16
    //     0x9e8648: b.ls            #0x9e86c0
    // 0x9e864c: r16 = <Color?>
    //     0x9e864c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x9e8650: ldr             x16, [x16, #0x98]
    // 0x9e8654: stp             x3, x16, [SP, #8]
    // 0x9e8658: str             x2, [SP]
    // 0x9e865c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9e865c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9e8660: r0 = resolveAs()
    //     0x9e8660: bl              #0x9d8704  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::resolveAs
    // 0x9e8664: cmp             w0, NULL
    // 0x9e8668: b.ne            #0x9e868c
    // 0x9e866c: r16 = <Color?>
    //     0x9e866c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x9e8670: ldr             x16, [x16, #0x98]
    // 0x9e8674: ldur            lr, [fp, #-0x10]
    // 0x9e8678: stp             lr, x16, [SP, #8]
    // 0x9e867c: ldur            x16, [fp, #-8]
    // 0x9e8680: str             x16, [SP]
    // 0x9e8684: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9e8684: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9e8688: r0 = resolveAs()
    //     0x9e8688: bl              #0x9d8704  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::resolveAs
    // 0x9e868c: cmp             w0, NULL
    // 0x9e8690: b.ne            #0x9e86b4
    // 0x9e8694: r16 = <Color>
    //     0x9e8694: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0x9e8698: ldr             x16, [x16, #0x158]
    // 0x9e869c: ldur            lr, [fp, #-0x18]
    // 0x9e86a0: stp             lr, x16, [SP, #8]
    // 0x9e86a4: ldur            x16, [fp, #-8]
    // 0x9e86a8: str             x16, [SP]
    // 0x9e86ac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9e86ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9e86b0: r0 = resolveAs()
    //     0x9e86b0: bl              #0x9d8704  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::resolveAs
    // 0x9e86b4: LeaveFrame
    //     0x9e86b4: mov             SP, fp
    //     0x9e86b8: ldp             fp, lr, [SP], #0x10
    // 0x9e86bc: ret
    //     0x9e86bc: ret             
    // 0x9e86c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e86c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e86c4: b               #0x9e864c
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7cad8, size: 0x70
    // 0xa7cad8: EnterFrame
    //     0xa7cad8: stp             fp, lr, [SP, #-0x10]!
    //     0xa7cadc: mov             fp, SP
    // 0xa7cae0: AllocStack(0x10)
    //     0xa7cae0: sub             SP, SP, #0x10
    // 0xa7cae4: SetupParameters(_AppBarState this /* r1 => r0, fp-0x10 */)
    //     0xa7cae4: mov             x0, x1
    //     0xa7cae8: stur            x1, [fp, #-0x10]
    // 0xa7caec: CheckStackOverflow
    //     0xa7caec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7caf0: cmp             SP, x16
    //     0xa7caf4: b.ls            #0xa7cb40
    // 0xa7caf8: LoadField: r3 = r0->field_13
    //     0xa7caf8: ldur            w3, [x0, #0x13]
    // 0xa7cafc: DecompressPointer r3
    //     0xa7cafc: add             x3, x3, HEAP, lsl #32
    // 0xa7cb00: stur            x3, [fp, #-8]
    // 0xa7cb04: cmp             w3, NULL
    // 0xa7cb08: b.eq            #0xa7cb30
    // 0xa7cb0c: mov             x2, x0
    // 0xa7cb10: r1 = Function '_handleScrollNotification@500187611':.
    //     0xa7cb10: add             x1, PP, #0x44, lsl #12  ; [pp+0x447f8] AnonymousClosure: (0x9a33f8), in [package:flutter/src/material/app_bar.dart] _AppBarState::_handleScrollNotification (0x9a3434)
    //     0xa7cb14: ldr             x1, [x1, #0x7f8]
    // 0xa7cb18: r0 = AllocateClosure()
    //     0xa7cb18: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7cb1c: ldur            x1, [fp, #-8]
    // 0xa7cb20: mov             x2, x0
    // 0xa7cb24: r0 = removeListener()
    //     0xa7cb24: bl              #0x6a3f10  ; [package:flutter/src/widgets/scroll_notification_observer.dart] ScrollNotificationObserverState::removeListener
    // 0xa7cb28: ldur            x1, [fp, #-0x10]
    // 0xa7cb2c: StoreField: r1->field_13 = rNULL
    //     0xa7cb2c: stur            NULL, [x1, #0x13]
    // 0xa7cb30: r0 = Null
    //     0xa7cb30: mov             x0, NULL
    // 0xa7cb34: LeaveFrame
    //     0xa7cb34: mov             SP, fp
    //     0xa7cb38: ldp             fp, lr, [SP], #0x10
    // 0xa7cb3c: ret
    //     0xa7cb3c: ret             
    // 0xa7cb40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7cb40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7cb44: b               #0xa7caf8
  }
}

// class id: 4560, size: 0x10, field offset: 0x10
//   const constructor, 
class _AppBarTitleBox extends SingleChildRenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x859fac, size: 0x54
    // 0x859fac: EnterFrame
    //     0x859fac: stp             fp, lr, [SP, #-0x10]!
    //     0x859fb0: mov             fp, SP
    // 0x859fb4: AllocStack(0x8)
    //     0x859fb4: sub             SP, SP, #8
    // 0x859fb8: SetupParameters(_AppBarTitleBox this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x859fb8: mov             x0, x1
    //     0x859fbc: mov             x1, x2
    // 0x859fc0: CheckStackOverflow
    //     0x859fc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x859fc4: cmp             SP, x16
    //     0x859fc8: b.ls            #0x859ff8
    // 0x859fcc: r0 = of()
    //     0x859fcc: bl              #0x6b0ae8  ; [package:flutter/src/widgets/basic.dart] Directionality::of
    // 0x859fd0: stur            x0, [fp, #-8]
    // 0x859fd4: r0 = _RenderAppBarTitleBox()
    //     0x859fd4: bl              #0x85a0a4  ; Allocate_RenderAppBarTitleBoxStub -> _RenderAppBarTitleBox (size=0x68)
    // 0x859fd8: mov             x1, x0
    // 0x859fdc: ldur            x2, [fp, #-8]
    // 0x859fe0: stur            x0, [fp, #-8]
    // 0x859fe4: r0 = _RenderAppBarTitleBox()
    //     0x859fe4: bl              #0x85a000  ; [package:flutter/src/material/app_bar.dart] _RenderAppBarTitleBox::_RenderAppBarTitleBox
    // 0x859fe8: ldur            x0, [fp, #-8]
    // 0x859fec: LeaveFrame
    //     0x859fec: mov             SP, fp
    //     0x859ff0: ldp             fp, lr, [SP], #0x10
    // 0x859ff4: ret
    //     0x859ff4: ret             
    // 0x859ff8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x859ff8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x859ffc: b               #0x859fcc
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xc6d1b4, size: 0xb0
    // 0xc6d1b4: EnterFrame
    //     0xc6d1b4: stp             fp, lr, [SP, #-0x10]!
    //     0xc6d1b8: mov             fp, SP
    // 0xc6d1bc: AllocStack(0x10)
    //     0xc6d1bc: sub             SP, SP, #0x10
    // 0xc6d1c0: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xc6d1c0: mov             x4, x2
    //     0xc6d1c4: stur            x2, [fp, #-8]
    //     0xc6d1c8: stur            x3, [fp, #-0x10]
    // 0xc6d1cc: CheckStackOverflow
    //     0xc6d1cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6d1d0: cmp             SP, x16
    //     0xc6d1d4: b.ls            #0xc6d25c
    // 0xc6d1d8: mov             x0, x3
    // 0xc6d1dc: r2 = Null
    //     0xc6d1dc: mov             x2, NULL
    // 0xc6d1e0: r1 = Null
    //     0xc6d1e0: mov             x1, NULL
    // 0xc6d1e4: r4 = 60
    //     0xc6d1e4: movz            x4, #0x3c
    // 0xc6d1e8: branchIfSmi(r0, 0xc6d1f4)
    //     0xc6d1e8: tbz             w0, #0, #0xc6d1f4
    // 0xc6d1ec: r4 = LoadClassIdInstr(r0)
    //     0xc6d1ec: ldur            x4, [x0, #-1]
    //     0xc6d1f0: ubfx            x4, x4, #0xc, #0x14
    // 0xc6d1f4: cmp             x4, #0xc13
    // 0xc6d1f8: b.eq            #0xc6d210
    // 0xc6d1fc: r8 = _RenderAppBarTitleBox
    //     0xc6d1fc: add             x8, PP, #0x4e, lsl #12  ; [pp+0x4e308] Type: _RenderAppBarTitleBox
    //     0xc6d200: ldr             x8, [x8, #0x308]
    // 0xc6d204: r3 = Null
    //     0xc6d204: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e310] Null
    //     0xc6d208: ldr             x3, [x3, #0x310]
    // 0xc6d20c: r0 = DefaultTypeTest()
    //     0xc6d20c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc6d210: ldur            x1, [fp, #-8]
    // 0xc6d214: r0 = of()
    //     0xc6d214: bl              #0x6b0ae8  ; [package:flutter/src/widgets/basic.dart] Directionality::of
    // 0xc6d218: ldur            x1, [fp, #-0x10]
    // 0xc6d21c: LoadField: r2 = r1->field_63
    //     0xc6d21c: ldur            w2, [x1, #0x63]
    // 0xc6d220: DecompressPointer r2
    //     0xc6d220: add             x2, x2, HEAP, lsl #32
    // 0xc6d224: cmp             w2, w0
    // 0xc6d228: b.eq            #0xc6d24c
    // 0xc6d22c: StoreField: r1->field_63 = r0
    //     0xc6d22c: stur            w0, [x1, #0x63]
    //     0xc6d230: ldurb           w16, [x1, #-1]
    //     0xc6d234: ldurb           w17, [x0, #-1]
    //     0xc6d238: and             x16, x17, x16, lsr #2
    //     0xc6d23c: tst             x16, HEAP, lsr #32
    //     0xc6d240: b.eq            #0xc6d248
    //     0xc6d244: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6d248: r0 = _markNeedResolution()
    //     0xc6d248: bl              #0xc6d2d4  ; [package:flutter/src/rendering/shifted_box.dart] RenderAligningShiftedBox::_markNeedResolution
    // 0xc6d24c: r0 = Null
    //     0xc6d24c: mov             x0, NULL
    // 0xc6d250: LeaveFrame
    //     0xc6d250: mov             SP, fp
    //     0xc6d254: ldp             fp, lr, [SP], #0x10
    // 0xc6d258: ret
    //     0xc6d258: ret             
    // 0xc6d25c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6d25c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6d260: b               #0xc6d1d8
  }
}

// class id: 4878, size: 0xa0, field offset: 0xc
//   const constructor, 
class SliverAppBar extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa8f858, size: 0x24
    // 0xa8f858: EnterFrame
    //     0xa8f858: stp             fp, lr, [SP, #-0x10]!
    //     0xa8f85c: mov             fp, SP
    // 0xa8f860: mov             x0, x1
    // 0xa8f864: r1 = <SliverAppBar>
    //     0xa8f864: add             x1, PP, #0x39, lsl #12  ; [pp+0x39a38] TypeArguments: <SliverAppBar>
    //     0xa8f868: ldr             x1, [x1, #0xa38]
    // 0xa8f86c: r0 = _SliverAppBarState()
    //     0xa8f86c: bl              #0xa8f87c  ; Allocate_SliverAppBarStateStub -> _SliverAppBarState (size=0x28)
    // 0xa8f870: LeaveFrame
    //     0xa8f870: mov             SP, fp
    //     0xa8f874: ldp             fp, lr, [SP], #0x10
    // 0xa8f878: ret
    //     0xa8f878: ret             
  }
}

// class id: 4879, size: 0x8c, field offset: 0xc
class AppBar extends StatefulWidget
    implements PreferredSizeWidget {

  _ _getEffectiveCenterTitle(/* No info */) {
    // ** addr: 0x9e6aa4, size: 0xe4
    // 0x9e6aa4: EnterFrame
    //     0x9e6aa4: stp             fp, lr, [SP, #-0x10]!
    //     0x9e6aa8: mov             fp, SP
    // 0x9e6aac: AllocStack(0x8)
    //     0x9e6aac: sub             SP, SP, #8
    // 0x9e6ab0: CheckStackOverflow
    //     0x9e6ab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e6ab4: cmp             SP, x16
    //     0x9e6ab8: b.ls            #0x9e6b80
    // 0x9e6abc: LoadField: r0 = r1->field_4f
    //     0x9e6abc: ldur            w0, [x1, #0x4f]
    // 0x9e6ac0: DecompressPointer r0
    //     0x9e6ac0: add             x0, x0, HEAP, lsl #32
    // 0x9e6ac4: cmp             w0, NULL
    // 0x9e6ac8: b.ne            #0x9e6ae0
    // 0x9e6acc: LoadField: r0 = r2->field_9b
    //     0x9e6acc: ldur            w0, [x2, #0x9b]
    // 0x9e6ad0: DecompressPointer r0
    //     0x9e6ad0: add             x0, x0, HEAP, lsl #32
    // 0x9e6ad4: LoadField: r3 = r0->field_2b
    //     0x9e6ad4: ldur            w3, [x0, #0x2b]
    // 0x9e6ad8: DecompressPointer r3
    //     0x9e6ad8: add             x3, x3, HEAP, lsl #32
    // 0x9e6adc: mov             x0, x3
    // 0x9e6ae0: cmp             w0, NULL
    // 0x9e6ae4: b.ne            #0x9e6b74
    // 0x9e6ae8: LoadField: r0 = r2->field_23
    //     0x9e6ae8: ldur            w0, [x2, #0x23]
    // 0x9e6aec: DecompressPointer r0
    //     0x9e6aec: add             x0, x0, HEAP, lsl #32
    // 0x9e6af0: LoadField: r2 = r0->field_7
    //     0x9e6af0: ldur            x2, [x0, #7]
    // 0x9e6af4: cmp             x2, #2
    // 0x9e6af8: b.gt            #0x9e6b08
    // 0x9e6afc: cmp             x2, #1
    // 0x9e6b00: b.gt            #0x9e6b18
    // 0x9e6b04: b               #0x9e6b6c
    // 0x9e6b08: cmp             x2, #4
    // 0x9e6b0c: b.gt            #0x9e6b6c
    // 0x9e6b10: cmp             x2, #3
    // 0x9e6b14: b.le            #0x9e6b6c
    // 0x9e6b18: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9e6b18: ldur            w0, [x1, #0x17]
    // 0x9e6b1c: DecompressPointer r0
    //     0x9e6b1c: add             x0, x0, HEAP, lsl #32
    // 0x9e6b20: cmp             w0, NULL
    // 0x9e6b24: b.ne            #0x9e6b30
    // 0x9e6b28: r1 = true
    //     0x9e6b28: add             x1, NULL, #0x20  ; true
    // 0x9e6b2c: b               #0x9e6b70
    // 0x9e6b30: r1 = LoadClassIdInstr(r0)
    //     0x9e6b30: ldur            x1, [x0, #-1]
    //     0x9e6b34: ubfx            x1, x1, #0xc, #0x14
    // 0x9e6b38: str             x0, [SP]
    // 0x9e6b3c: mov             x0, x1
    // 0x9e6b40: r0 = GDT[cid_x0 + 0xc834]()
    //     0x9e6b40: movz            x17, #0xc834
    //     0x9e6b44: add             lr, x0, x17
    //     0x9e6b48: ldr             lr, [x21, lr, lsl #3]
    //     0x9e6b4c: blr             lr
    // 0x9e6b50: r1 = LoadInt32Instr(r0)
    //     0x9e6b50: sbfx            x1, x0, #1, #0x1f
    // 0x9e6b54: cmp             x1, #2
    // 0x9e6b58: r16 = true
    //     0x9e6b58: add             x16, NULL, #0x20  ; true
    // 0x9e6b5c: r17 = false
    //     0x9e6b5c: add             x17, NULL, #0x30  ; false
    // 0x9e6b60: csel            x2, x16, x17, lt
    // 0x9e6b64: mov             x1, x2
    // 0x9e6b68: b               #0x9e6b70
    // 0x9e6b6c: r1 = false
    //     0x9e6b6c: add             x1, NULL, #0x30  ; false
    // 0x9e6b70: mov             x0, x1
    // 0x9e6b74: LeaveFrame
    //     0x9e6b74: mov             SP, fp
    //     0x9e6b78: ldp             fp, lr, [SP], #0x10
    // 0x9e6b7c: ret
    //     0x9e6b7c: ret             
    // 0x9e6b80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e6b80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e6b84: b               #0x9e6abc
  }
  static _ preferredHeightFor(/* No info */) {
    // ** addr: 0xa03264, size: 0x104
    // 0xa03264: EnterFrame
    //     0xa03264: stp             fp, lr, [SP, #-0x10]!
    //     0xa03268: mov             fp, SP
    // 0xa0326c: AllocStack(0x18)
    //     0xa0326c: sub             SP, SP, #0x18
    // 0xa03270: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xa03270: stur            x2, [fp, #-8]
    // 0xa03274: CheckStackOverflow
    //     0xa03274: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa03278: cmp             SP, x16
    //     0xa0327c: b.ls            #0xa03344
    // 0xa03280: r0 = LoadClassIdInstr(r2)
    //     0xa03280: ldur            x0, [x2, #-1]
    //     0xa03284: ubfx            x0, x0, #0xc, #0x14
    // 0xa03288: r17 = 6092
    //     0xa03288: movz            x17, #0x17cc
    // 0xa0328c: cmp             x0, x17
    // 0xa03290: b.ne            #0xa03330
    // 0xa03294: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xa03294: ldur            w0, [x2, #0x17]
    // 0xa03298: DecompressPointer r0
    //     0xa03298: add             x0, x0, HEAP, lsl #32
    // 0xa0329c: cmp             w0, NULL
    // 0xa032a0: b.ne            #0xa03328
    // 0xa032a4: r0 = of()
    //     0xa032a4: bl              #0x9e86e0  ; [package:flutter/src/material/app_bar_theme.dart] AppBarTheme::of
    // 0xa032a8: LoadField: r1 = r0->field_33
    //     0xa032a8: ldur            w1, [x0, #0x33]
    // 0xa032ac: DecompressPointer r1
    //     0xa032ac: add             x1, x1, HEAP, lsl #32
    // 0xa032b0: cmp             w1, NULL
    // 0xa032b4: b.ne            #0xa032c4
    // 0xa032b8: d0 = 56.000000
    //     0xa032b8: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0xa032bc: ldr             d0, [x17, #0xf60]
    // 0xa032c0: b               #0xa032c8
    // 0xa032c4: LoadField: d0 = r1->field_7
    //     0xa032c4: ldur            d0, [x1, #7]
    // 0xa032c8: ldur            x0, [fp, #-8]
    // 0xa032cc: LoadField: r1 = r0->field_1b
    //     0xa032cc: ldur            w1, [x0, #0x1b]
    // 0xa032d0: DecompressPointer r1
    //     0xa032d0: add             x1, x1, HEAP, lsl #32
    // 0xa032d4: cmp             w1, NULL
    // 0xa032d8: b.ne            #0xa032e4
    // 0xa032dc: r0 = 0
    //     0xa032dc: movz            x0, #0
    // 0xa032e0: b               #0xa032e8
    // 0xa032e4: mov             x0, x1
    // 0xa032e8: r1 = inline_Allocate_Double()
    //     0xa032e8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xa032ec: add             x1, x1, #0x10
    //     0xa032f0: cmp             x2, x1
    //     0xa032f4: b.ls            #0xa0334c
    //     0xa032f8: str             x1, [THR, #0x50]  ; THR::top
    //     0xa032fc: sub             x1, x1, #0xf
    //     0xa03300: movz            x2, #0xe15c
    //     0xa03304: movk            x2, #0x3, lsl #16
    //     0xa03308: stur            x2, [x1, #-1]
    // 0xa0330c: StoreField: r1->field_7 = d0
    //     0xa0330c: stur            d0, [x1, #7]
    // 0xa03310: stp             x0, x1, [SP]
    // 0xa03314: r0 = +()
    //     0xa03314: bl              #0xebf900  ; [dart:core] _Double::+
    // 0xa03318: LoadField: d0 = r0->field_7
    //     0xa03318: ldur            d0, [x0, #7]
    // 0xa0331c: LeaveFrame
    //     0xa0331c: mov             SP, fp
    //     0xa03320: ldp             fp, lr, [SP], #0x10
    // 0xa03324: ret
    //     0xa03324: ret             
    // 0xa03328: mov             x0, x2
    // 0xa0332c: b               #0xa03334
    // 0xa03330: mov             x0, x2
    // 0xa03334: LoadField: d0 = r0->field_f
    //     0xa03334: ldur            d0, [x0, #0xf]
    // 0xa03338: LeaveFrame
    //     0xa03338: mov             SP, fp
    //     0xa0333c: ldp             fp, lr, [SP], #0x10
    // 0xa03340: ret
    //     0xa03340: ret             
    // 0xa03344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa03344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa03348: b               #0xa03280
    // 0xa0334c: SaveReg d0
    //     0xa0334c: str             q0, [SP, #-0x10]!
    // 0xa03350: SaveReg r0
    //     0xa03350: str             x0, [SP, #-8]!
    // 0xa03354: r0 = AllocateDouble()
    //     0xa03354: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa03358: mov             x1, x0
    // 0xa0335c: RestoreReg r0
    //     0xa0335c: ldr             x0, [SP], #8
    // 0xa03360: RestoreReg d0
    //     0xa03360: ldr             q0, [SP], #0x10
    // 0xa03364: b               #0xa0330c
  }
  _ AppBar(/* No info */) {
    // ** addr: 0xa37554, size: 0x954
    // 0xa37554: EnterFrame
    //     0xa37554: stp             fp, lr, [SP, #-0x10]!
    //     0xa37558: mov             fp, SP
    // 0xa3755c: AllocStack(0x30)
    //     0xa3755c: sub             SP, SP, #0x30
    // 0xa37560: SetupParameters(AppBar this /* r1 => r1, fp-0x30 */, {dynamic actions = Null /* r3 */, dynamic backgroundColor = Null /* r5 */, dynamic bottom = Null /* r2, fp-0x10 */, dynamic centerTitle = Null /* r7 */, dynamic elevation = Null /* r8 */, dynamic flexibleSpace = Null /* r9 */, dynamic foregroundColor = Null /* r10 */, dynamic iconTheme = Null /* r11 */, dynamic key = Null /* fp-0x8 */, dynamic leading = Null /* r13 */, dynamic shadowColor = Null /* r14 */, dynamic shape = Null /* r19 */, dynamic surfaceTintColor = Null /* r20 */, dynamic systemOverlayStyle = Null /* fp-0x18 */, dynamic title = Null /* r6 */, dynamic titleSpacing = Null /* fp-0x20 */, dynamic toolbarHeight = Null /* r12, fp-0x28 */, _Double toolbarOpacity = 1.000000 /* d1 */})
    //     0xa37560: stur            x1, [fp, #-0x30]
    //     0xa37564: ldur            w0, [x4, #0x13]
    //     0xa37568: ldur            w2, [x4, #0x1f]
    //     0xa3756c: add             x2, x2, HEAP, lsl #32
    //     0xa37570: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f10] "actions"
    //     0xa37574: ldr             x16, [x16, #0xf10]
    //     0xa37578: cmp             w2, w16
    //     0xa3757c: b.ne            #0xa375a0
    //     0xa37580: ldur            w2, [x4, #0x23]
    //     0xa37584: add             x2, x2, HEAP, lsl #32
    //     0xa37588: sub             w3, w0, w2
    //     0xa3758c: add             x2, fp, w3, sxtw #2
    //     0xa37590: ldr             x2, [x2, #8]
    //     0xa37594: mov             x3, x2
    //     0xa37598: movz            x2, #0x1
    //     0xa3759c: b               #0xa375a8
    //     0xa375a0: mov             x3, NULL
    //     0xa375a4: movz            x2, #0
    //     0xa375a8: lsl             x5, x2, #1
    //     0xa375ac: lsl             w6, w5, #1
    //     0xa375b0: add             w7, w6, #8
    //     0xa375b4: add             x16, x4, w7, sxtw #1
    //     0xa375b8: ldur            w8, [x16, #0xf]
    //     0xa375bc: add             x8, x8, HEAP, lsl #32
    //     0xa375c0: ldr             x16, [PP, #0x5148]  ; [pp+0x5148] "backgroundColor"
    //     0xa375c4: cmp             w8, w16
    //     0xa375c8: b.ne            #0xa375fc
    //     0xa375cc: add             w2, w6, #0xa
    //     0xa375d0: add             x16, x4, w2, sxtw #1
    //     0xa375d4: ldur            w6, [x16, #0xf]
    //     0xa375d8: add             x6, x6, HEAP, lsl #32
    //     0xa375dc: sub             w2, w0, w6
    //     0xa375e0: add             x6, fp, w2, sxtw #2
    //     0xa375e4: ldr             x6, [x6, #8]
    //     0xa375e8: add             w2, w5, #2
    //     0xa375ec: sbfx            x5, x2, #1, #0x1f
    //     0xa375f0: mov             x2, x5
    //     0xa375f4: mov             x5, x6
    //     0xa375f8: b               #0xa37600
    //     0xa375fc: mov             x5, NULL
    //     0xa37600: lsl             x6, x2, #1
    //     0xa37604: lsl             w7, w6, #1
    //     0xa37608: add             w8, w7, #8
    //     0xa3760c: add             x16, x4, w8, sxtw #1
    //     0xa37610: ldur            w9, [x16, #0xf]
    //     0xa37614: add             x9, x9, HEAP, lsl #32
    //     0xa37618: ldr             x16, [PP, #0x7030]  ; [pp+0x7030] "bottom"
    //     0xa3761c: cmp             w9, w16
    //     0xa37620: b.ne            #0xa37654
    //     0xa37624: add             w2, w7, #0xa
    //     0xa37628: add             x16, x4, w2, sxtw #1
    //     0xa3762c: ldur            w7, [x16, #0xf]
    //     0xa37630: add             x7, x7, HEAP, lsl #32
    //     0xa37634: sub             w2, w0, w7
    //     0xa37638: add             x7, fp, w2, sxtw #2
    //     0xa3763c: ldr             x7, [x7, #8]
    //     0xa37640: add             w2, w6, #2
    //     0xa37644: sbfx            x6, x2, #1, #0x1f
    //     0xa37648: mov             x2, x6
    //     0xa3764c: mov             x6, x7
    //     0xa37650: b               #0xa37658
    //     0xa37654: mov             x6, NULL
    //     0xa37658: stur            x6, [fp, #-0x10]
    //     0xa3765c: lsl             x7, x2, #1
    //     0xa37660: lsl             w8, w7, #1
    //     0xa37664: add             w9, w8, #8
    //     0xa37668: add             x16, x4, w9, sxtw #1
    //     0xa3766c: ldur            w10, [x16, #0xf]
    //     0xa37670: add             x10, x10, HEAP, lsl #32
    //     0xa37674: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f18] "centerTitle"
    //     0xa37678: ldr             x16, [x16, #0xf18]
    //     0xa3767c: cmp             w10, w16
    //     0xa37680: b.ne            #0xa376b4
    //     0xa37684: add             w2, w8, #0xa
    //     0xa37688: add             x16, x4, w2, sxtw #1
    //     0xa3768c: ldur            w8, [x16, #0xf]
    //     0xa37690: add             x8, x8, HEAP, lsl #32
    //     0xa37694: sub             w2, w0, w8
    //     0xa37698: add             x8, fp, w2, sxtw #2
    //     0xa3769c: ldr             x8, [x8, #8]
    //     0xa376a0: add             w2, w7, #2
    //     0xa376a4: sbfx            x7, x2, #1, #0x1f
    //     0xa376a8: mov             x2, x7
    //     0xa376ac: mov             x7, x8
    //     0xa376b0: b               #0xa376b8
    //     0xa376b4: mov             x7, NULL
    //     0xa376b8: lsl             x8, x2, #1
    //     0xa376bc: lsl             w9, w8, #1
    //     0xa376c0: add             w10, w9, #8
    //     0xa376c4: add             x16, x4, w10, sxtw #1
    //     0xa376c8: ldur            w11, [x16, #0xf]
    //     0xa376cc: add             x11, x11, HEAP, lsl #32
    //     0xa376d0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d58] "elevation"
    //     0xa376d4: ldr             x16, [x16, #0xd58]
    //     0xa376d8: cmp             w11, w16
    //     0xa376dc: b.ne            #0xa37710
    //     0xa376e0: add             w2, w9, #0xa
    //     0xa376e4: add             x16, x4, w2, sxtw #1
    //     0xa376e8: ldur            w9, [x16, #0xf]
    //     0xa376ec: add             x9, x9, HEAP, lsl #32
    //     0xa376f0: sub             w2, w0, w9
    //     0xa376f4: add             x9, fp, w2, sxtw #2
    //     0xa376f8: ldr             x9, [x9, #8]
    //     0xa376fc: add             w2, w8, #2
    //     0xa37700: sbfx            x8, x2, #1, #0x1f
    //     0xa37704: mov             x2, x8
    //     0xa37708: mov             x8, x9
    //     0xa3770c: b               #0xa37714
    //     0xa37710: mov             x8, NULL
    //     0xa37714: lsl             x9, x2, #1
    //     0xa37718: lsl             w10, w9, #1
    //     0xa3771c: add             w11, w10, #8
    //     0xa37720: add             x16, x4, w11, sxtw #1
    //     0xa37724: ldur            w12, [x16, #0xf]
    //     0xa37728: add             x12, x12, HEAP, lsl #32
    //     0xa3772c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f20] "flexibleSpace"
    //     0xa37730: ldr             x16, [x16, #0xf20]
    //     0xa37734: cmp             w12, w16
    //     0xa37738: b.ne            #0xa3776c
    //     0xa3773c: add             w2, w10, #0xa
    //     0xa37740: add             x16, x4, w2, sxtw #1
    //     0xa37744: ldur            w10, [x16, #0xf]
    //     0xa37748: add             x10, x10, HEAP, lsl #32
    //     0xa3774c: sub             w2, w0, w10
    //     0xa37750: add             x10, fp, w2, sxtw #2
    //     0xa37754: ldr             x10, [x10, #8]
    //     0xa37758: add             w2, w9, #2
    //     0xa3775c: sbfx            x9, x2, #1, #0x1f
    //     0xa37760: mov             x2, x9
    //     0xa37764: mov             x9, x10
    //     0xa37768: b               #0xa37770
    //     0xa3776c: mov             x9, NULL
    //     0xa37770: lsl             x10, x2, #1
    //     0xa37774: lsl             w11, w10, #1
    //     0xa37778: add             w12, w11, #8
    //     0xa3777c: add             x16, x4, w12, sxtw #1
    //     0xa37780: ldur            w13, [x16, #0xf]
    //     0xa37784: add             x13, x13, HEAP, lsl #32
    //     0xa37788: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d70] "foregroundColor"
    //     0xa3778c: ldr             x16, [x16, #0xd70]
    //     0xa37790: cmp             w13, w16
    //     0xa37794: b.ne            #0xa377c8
    //     0xa37798: add             w2, w11, #0xa
    //     0xa3779c: add             x16, x4, w2, sxtw #1
    //     0xa377a0: ldur            w11, [x16, #0xf]
    //     0xa377a4: add             x11, x11, HEAP, lsl #32
    //     0xa377a8: sub             w2, w0, w11
    //     0xa377ac: add             x11, fp, w2, sxtw #2
    //     0xa377b0: ldr             x11, [x11, #8]
    //     0xa377b4: add             w2, w10, #2
    //     0xa377b8: sbfx            x10, x2, #1, #0x1f
    //     0xa377bc: mov             x2, x10
    //     0xa377c0: mov             x10, x11
    //     0xa377c4: b               #0xa377cc
    //     0xa377c8: mov             x10, NULL
    //     0xa377cc: lsl             x11, x2, #1
    //     0xa377d0: lsl             w12, w11, #1
    //     0xa377d4: add             w13, w12, #8
    //     0xa377d8: add             x16, x4, w13, sxtw #1
    //     0xa377dc: ldur            w14, [x16, #0xf]
    //     0xa377e0: add             x14, x14, HEAP, lsl #32
    //     0xa377e4: ldr             x16, [PP, #0x52a0]  ; [pp+0x52a0] "iconTheme"
    //     0xa377e8: cmp             w14, w16
    //     0xa377ec: b.ne            #0xa37820
    //     0xa377f0: add             w2, w12, #0xa
    //     0xa377f4: add             x16, x4, w2, sxtw #1
    //     0xa377f8: ldur            w12, [x16, #0xf]
    //     0xa377fc: add             x12, x12, HEAP, lsl #32
    //     0xa37800: sub             w2, w0, w12
    //     0xa37804: add             x12, fp, w2, sxtw #2
    //     0xa37808: ldr             x12, [x12, #8]
    //     0xa3780c: add             w2, w11, #2
    //     0xa37810: sbfx            x11, x2, #1, #0x1f
    //     0xa37814: mov             x2, x11
    //     0xa37818: mov             x11, x12
    //     0xa3781c: b               #0xa37824
    //     0xa37820: mov             x11, NULL
    //     0xa37824: lsl             x12, x2, #1
    //     0xa37828: lsl             w13, w12, #1
    //     0xa3782c: add             w14, w13, #8
    //     0xa37830: add             x16, x4, w14, sxtw #1
    //     0xa37834: ldur            w19, [x16, #0xf]
    //     0xa37838: add             x19, x19, HEAP, lsl #32
    //     0xa3783c: ldr             x16, [PP, #0xab8]  ; [pp+0xab8] "key"
    //     0xa37840: cmp             w19, w16
    //     0xa37844: b.ne            #0xa37878
    //     0xa37848: add             w2, w13, #0xa
    //     0xa3784c: add             x16, x4, w2, sxtw #1
    //     0xa37850: ldur            w13, [x16, #0xf]
    //     0xa37854: add             x13, x13, HEAP, lsl #32
    //     0xa37858: sub             w2, w0, w13
    //     0xa3785c: add             x13, fp, w2, sxtw #2
    //     0xa37860: ldr             x13, [x13, #8]
    //     0xa37864: add             w2, w12, #2
    //     0xa37868: sbfx            x12, x2, #1, #0x1f
    //     0xa3786c: mov             x2, x12
    //     0xa37870: mov             x12, x13
    //     0xa37874: b               #0xa3787c
    //     0xa37878: mov             x12, NULL
    //     0xa3787c: stur            x12, [fp, #-8]
    //     0xa37880: lsl             x13, x2, #1
    //     0xa37884: lsl             w14, w13, #1
    //     0xa37888: add             w19, w14, #8
    //     0xa3788c: add             x16, x4, w19, sxtw #1
    //     0xa37890: ldur            w20, [x16, #0xf]
    //     0xa37894: add             x20, x20, HEAP, lsl #32
    //     0xa37898: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f28] "leading"
    //     0xa3789c: ldr             x16, [x16, #0xf28]
    //     0xa378a0: cmp             w20, w16
    //     0xa378a4: b.ne            #0xa378d8
    //     0xa378a8: add             w2, w14, #0xa
    //     0xa378ac: add             x16, x4, w2, sxtw #1
    //     0xa378b0: ldur            w14, [x16, #0xf]
    //     0xa378b4: add             x14, x14, HEAP, lsl #32
    //     0xa378b8: sub             w2, w0, w14
    //     0xa378bc: add             x14, fp, w2, sxtw #2
    //     0xa378c0: ldr             x14, [x14, #8]
    //     0xa378c4: add             w2, w13, #2
    //     0xa378c8: sbfx            x13, x2, #1, #0x1f
    //     0xa378cc: mov             x2, x13
    //     0xa378d0: mov             x13, x14
    //     0xa378d4: b               #0xa378dc
    //     0xa378d8: mov             x13, NULL
    //     0xa378dc: lsl             x14, x2, #1
    //     0xa378e0: lsl             w19, w14, #1
    //     0xa378e4: add             w20, w19, #8
    //     0xa378e8: add             x16, x4, w20, sxtw #1
    //     0xa378ec: ldur            w23, [x16, #0xf]
    //     0xa378f0: add             x23, x23, HEAP, lsl #32
    //     0xa378f4: ldr             x16, [PP, #0x5388]  ; [pp+0x5388] "shadowColor"
    //     0xa378f8: cmp             w23, w16
    //     0xa378fc: b.ne            #0xa37930
    //     0xa37900: add             w2, w19, #0xa
    //     0xa37904: add             x16, x4, w2, sxtw #1
    //     0xa37908: ldur            w19, [x16, #0xf]
    //     0xa3790c: add             x19, x19, HEAP, lsl #32
    //     0xa37910: sub             w2, w0, w19
    //     0xa37914: add             x19, fp, w2, sxtw #2
    //     0xa37918: ldr             x19, [x19, #8]
    //     0xa3791c: add             w2, w14, #2
    //     0xa37920: sbfx            x14, x2, #1, #0x1f
    //     0xa37924: mov             x2, x14
    //     0xa37928: mov             x14, x19
    //     0xa3792c: b               #0xa37934
    //     0xa37930: mov             x14, NULL
    //     0xa37934: lsl             x19, x2, #1
    //     0xa37938: lsl             w20, w19, #1
    //     0xa3793c: add             w23, w20, #8
    //     0xa37940: add             x16, x4, w23, sxtw #1
    //     0xa37944: ldur            w24, [x16, #0xf]
    //     0xa37948: add             x24, x24, HEAP, lsl #32
    //     0xa3794c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d90] "shape"
    //     0xa37950: ldr             x16, [x16, #0xd90]
    //     0xa37954: cmp             w24, w16
    //     0xa37958: b.ne            #0xa3798c
    //     0xa3795c: add             w2, w20, #0xa
    //     0xa37960: add             x16, x4, w2, sxtw #1
    //     0xa37964: ldur            w20, [x16, #0xf]
    //     0xa37968: add             x20, x20, HEAP, lsl #32
    //     0xa3796c: sub             w2, w0, w20
    //     0xa37970: add             x20, fp, w2, sxtw #2
    //     0xa37974: ldr             x20, [x20, #8]
    //     0xa37978: add             w2, w19, #2
    //     0xa3797c: sbfx            x19, x2, #1, #0x1f
    //     0xa37980: mov             x2, x19
    //     0xa37984: mov             x19, x20
    //     0xa37988: b               #0xa37990
    //     0xa3798c: mov             x19, NULL
    //     0xa37990: lsl             x20, x2, #1
    //     0xa37994: lsl             w23, w20, #1
    //     0xa37998: add             w24, w23, #8
    //     0xa3799c: add             x16, x4, w24, sxtw #1
    //     0xa379a0: ldur            w25, [x16, #0xf]
    //     0xa379a4: add             x25, x25, HEAP, lsl #32
    //     0xa379a8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f30] "surfaceTintColor"
    //     0xa379ac: ldr             x16, [x16, #0xf30]
    //     0xa379b0: cmp             w25, w16
    //     0xa379b4: b.ne            #0xa379e8
    //     0xa379b8: add             w2, w23, #0xa
    //     0xa379bc: add             x16, x4, w2, sxtw #1
    //     0xa379c0: ldur            w23, [x16, #0xf]
    //     0xa379c4: add             x23, x23, HEAP, lsl #32
    //     0xa379c8: sub             w2, w0, w23
    //     0xa379cc: add             x23, fp, w2, sxtw #2
    //     0xa379d0: ldr             x23, [x23, #8]
    //     0xa379d4: add             w2, w20, #2
    //     0xa379d8: sbfx            x20, x2, #1, #0x1f
    //     0xa379dc: mov             x2, x20
    //     0xa379e0: mov             x20, x23
    //     0xa379e4: b               #0xa379ec
    //     0xa379e8: mov             x20, NULL
    //     0xa379ec: lsl             x23, x2, #1
    //     0xa379f0: lsl             w24, w23, #1
    //     0xa379f4: add             w25, w24, #8
    //     0xa379f8: add             x16, x4, w25, sxtw #1
    //     0xa379fc: ldur            w12, [x16, #0xf]
    //     0xa37a00: add             x12, x12, HEAP, lsl #32
    //     0xa37a04: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f38] "systemOverlayStyle"
    //     0xa37a08: ldr             x16, [x16, #0xf38]
    //     0xa37a0c: cmp             w12, w16
    //     0xa37a10: b.ne            #0xa37a40
    //     0xa37a14: add             w2, w24, #0xa
    //     0xa37a18: add             x16, x4, w2, sxtw #1
    //     0xa37a1c: ldur            w12, [x16, #0xf]
    //     0xa37a20: add             x12, x12, HEAP, lsl #32
    //     0xa37a24: sub             w2, w0, w12
    //     0xa37a28: add             x12, fp, w2, sxtw #2
    //     0xa37a2c: ldr             x12, [x12, #8]
    //     0xa37a30: add             w2, w23, #2
    //     0xa37a34: sbfx            x23, x2, #1, #0x1f
    //     0xa37a38: mov             x2, x23
    //     0xa37a3c: b               #0xa37a44
    //     0xa37a40: mov             x12, NULL
    //     0xa37a44: stur            x12, [fp, #-0x18]
    //     0xa37a48: lsl             x23, x2, #1
    //     0xa37a4c: lsl             w24, w23, #1
    //     0xa37a50: add             w25, w24, #8
    //     0xa37a54: add             x16, x4, w25, sxtw #1
    //     0xa37a58: ldur            w6, [x16, #0xf]
    //     0xa37a5c: add             x6, x6, HEAP, lsl #32
    //     0xa37a60: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xa37a64: ldr             x16, [x16, #0x748]
    //     0xa37a68: cmp             w6, w16
    //     0xa37a6c: b.ne            #0xa37a9c
    //     0xa37a70: add             w2, w24, #0xa
    //     0xa37a74: add             x16, x4, w2, sxtw #1
    //     0xa37a78: ldur            w6, [x16, #0xf]
    //     0xa37a7c: add             x6, x6, HEAP, lsl #32
    //     0xa37a80: sub             w2, w0, w6
    //     0xa37a84: add             x6, fp, w2, sxtw #2
    //     0xa37a88: ldr             x6, [x6, #8]
    //     0xa37a8c: add             w2, w23, #2
    //     0xa37a90: sbfx            x23, x2, #1, #0x1f
    //     0xa37a94: mov             x2, x23
    //     0xa37a98: b               #0xa37aa0
    //     0xa37a9c: mov             x6, NULL
    //     0xa37aa0: lsl             x23, x2, #1
    //     0xa37aa4: lsl             w24, w23, #1
    //     0xa37aa8: add             w25, w24, #8
    //     0xa37aac: add             x16, x4, w25, sxtw #1
    //     0xa37ab0: ldur            w12, [x16, #0xf]
    //     0xa37ab4: add             x12, x12, HEAP, lsl #32
    //     0xa37ab8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f40] "titleSpacing"
    //     0xa37abc: ldr             x16, [x16, #0xf40]
    //     0xa37ac0: cmp             w12, w16
    //     0xa37ac4: b.ne            #0xa37af4
    //     0xa37ac8: add             w2, w24, #0xa
    //     0xa37acc: add             x16, x4, w2, sxtw #1
    //     0xa37ad0: ldur            w12, [x16, #0xf]
    //     0xa37ad4: add             x12, x12, HEAP, lsl #32
    //     0xa37ad8: sub             w2, w0, w12
    //     0xa37adc: add             x12, fp, w2, sxtw #2
    //     0xa37ae0: ldr             x12, [x12, #8]
    //     0xa37ae4: add             w2, w23, #2
    //     0xa37ae8: sbfx            x23, x2, #1, #0x1f
    //     0xa37aec: mov             x2, x23
    //     0xa37af0: b               #0xa37af8
    //     0xa37af4: mov             x12, NULL
    //     0xa37af8: stur            x12, [fp, #-0x20]
    //     0xa37afc: lsl             x23, x2, #1
    //     0xa37b00: lsl             w24, w23, #1
    //     0xa37b04: add             w25, w24, #8
    //     0xa37b08: add             x16, x4, w25, sxtw #1
    //     0xa37b0c: ldur            w12, [x16, #0xf]
    //     0xa37b10: add             x12, x12, HEAP, lsl #32
    //     0xa37b14: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f48] "toolbarHeight"
    //     0xa37b18: ldr             x16, [x16, #0xf48]
    //     0xa37b1c: cmp             w12, w16
    //     0xa37b20: b.ne            #0xa37b50
    //     0xa37b24: add             w2, w24, #0xa
    //     0xa37b28: add             x16, x4, w2, sxtw #1
    //     0xa37b2c: ldur            w12, [x16, #0xf]
    //     0xa37b30: add             x12, x12, HEAP, lsl #32
    //     0xa37b34: sub             w2, w0, w12
    //     0xa37b38: add             x12, fp, w2, sxtw #2
    //     0xa37b3c: ldr             x12, [x12, #8]
    //     0xa37b40: add             w2, w23, #2
    //     0xa37b44: sbfx            x23, x2, #1, #0x1f
    //     0xa37b48: mov             x2, x23
    //     0xa37b4c: b               #0xa37b54
    //     0xa37b50: mov             x12, NULL
    //     0xa37b54: stur            x12, [fp, #-0x28]
    //     0xa37b58: lsl             x23, x2, #1
    //     0xa37b5c: lsl             w2, w23, #1
    //     0xa37b60: add             w23, w2, #8
    //     0xa37b64: add             x16, x4, w23, sxtw #1
    //     0xa37b68: ldur            w24, [x16, #0xf]
    //     0xa37b6c: add             x24, x24, HEAP, lsl #32
    //     0xa37b70: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f50] "toolbarOpacity"
    //     0xa37b74: ldr             x16, [x16, #0xf50]
    //     0xa37b78: cmp             w24, w16
    //     0xa37b7c: b.ne            #0xa37ba8
    //     0xa37b80: add             w23, w2, #0xa
    //     0xa37b84: add             x16, x4, w23, sxtw #1
    //     0xa37b88: ldur            w2, [x16, #0xf]
    //     0xa37b8c: add             x2, x2, HEAP, lsl #32
    //     0xa37b90: sub             w4, w0, w2
    //     0xa37b94: add             x0, fp, w4, sxtw #2
    //     0xa37b98: ldr             x0, [x0, #8]
    //     0xa37b9c: ldur            d0, [x0, #7]
    //     0xa37ba0: mov             v1.16b, v0.16b
    //     0xa37ba4: b               #0xa37bac
    //     0xa37ba8: fmov            d1, #1.00000000
    //     0xa37bac: ldur            x2, [fp, #-0x10]
    //     0xa37bb0: add             x24, NULL, #0x20  ; true
    //     0xa37bb4: add             x23, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xa37bb8: ldr             x23, [x23, #0xf58]
    //     0xa37bbc: add             x4, NULL, #0x30  ; false
    //     0xa37bc0: fmov            d0, #1.00000000
    // 0xa37bb0: r24 = true
    // 0xa37bb4: r23 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    // 0xa37bbc: r4 = false
    // 0xa37bc0: d0 = 1.000000
    // 0xa37bc4: CheckStackOverflow
    //     0xa37bc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa37bc8: cmp             SP, x16
    //     0xa37bcc: b.ls            #0xa37e88
    // 0xa37bd0: mov             x0, x13
    // 0xa37bd4: StoreField: r1->field_b = r0
    //     0xa37bd4: stur            w0, [x1, #0xb]
    //     0xa37bd8: ldurb           w16, [x1, #-1]
    //     0xa37bdc: ldurb           w17, [x0, #-1]
    //     0xa37be0: and             x16, x17, x16, lsr #2
    //     0xa37be4: tst             x16, HEAP, lsr #32
    //     0xa37be8: b.eq            #0xa37bf0
    //     0xa37bec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37bf0: StoreField: r1->field_f = r24
    //     0xa37bf0: stur            w24, [x1, #0xf]
    // 0xa37bf4: mov             x0, x6
    // 0xa37bf8: StoreField: r1->field_13 = r0
    //     0xa37bf8: stur            w0, [x1, #0x13]
    //     0xa37bfc: ldurb           w16, [x1, #-1]
    //     0xa37c00: ldurb           w17, [x0, #-1]
    //     0xa37c04: and             x16, x17, x16, lsr #2
    //     0xa37c08: tst             x16, HEAP, lsr #32
    //     0xa37c0c: b.eq            #0xa37c14
    //     0xa37c10: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37c14: mov             x0, x3
    // 0xa37c18: ArrayStore: r1[0] = r0  ; List_4
    //     0xa37c18: stur            w0, [x1, #0x17]
    //     0xa37c1c: ldurb           w16, [x1, #-1]
    //     0xa37c20: ldurb           w17, [x0, #-1]
    //     0xa37c24: and             x16, x17, x16, lsr #2
    //     0xa37c28: tst             x16, HEAP, lsr #32
    //     0xa37c2c: b.eq            #0xa37c34
    //     0xa37c30: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37c34: mov             x0, x9
    // 0xa37c38: StoreField: r1->field_1b = r0
    //     0xa37c38: stur            w0, [x1, #0x1b]
    //     0xa37c3c: ldurb           w16, [x1, #-1]
    //     0xa37c40: ldurb           w17, [x0, #-1]
    //     0xa37c44: and             x16, x17, x16, lsr #2
    //     0xa37c48: tst             x16, HEAP, lsr #32
    //     0xa37c4c: b.eq            #0xa37c54
    //     0xa37c50: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37c54: mov             x0, x2
    // 0xa37c58: StoreField: r1->field_1f = r0
    //     0xa37c58: stur            w0, [x1, #0x1f]
    //     0xa37c5c: ldurb           w16, [x1, #-1]
    //     0xa37c60: ldurb           w17, [x0, #-1]
    //     0xa37c64: and             x16, x17, x16, lsr #2
    //     0xa37c68: tst             x16, HEAP, lsr #32
    //     0xa37c6c: b.eq            #0xa37c74
    //     0xa37c70: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37c74: mov             x0, x8
    // 0xa37c78: StoreField: r1->field_23 = r0
    //     0xa37c78: stur            w0, [x1, #0x23]
    //     0xa37c7c: ldurb           w16, [x1, #-1]
    //     0xa37c80: ldurb           w17, [x0, #-1]
    //     0xa37c84: and             x16, x17, x16, lsr #2
    //     0xa37c88: tst             x16, HEAP, lsr #32
    //     0xa37c8c: b.eq            #0xa37c94
    //     0xa37c90: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37c94: StoreField: r1->field_2b = r23
    //     0xa37c94: stur            w23, [x1, #0x2b]
    // 0xa37c98: mov             x0, x14
    // 0xa37c9c: StoreField: r1->field_2f = r0
    //     0xa37c9c: stur            w0, [x1, #0x2f]
    //     0xa37ca0: ldurb           w16, [x1, #-1]
    //     0xa37ca4: ldurb           w17, [x0, #-1]
    //     0xa37ca8: and             x16, x17, x16, lsr #2
    //     0xa37cac: tst             x16, HEAP, lsr #32
    //     0xa37cb0: b.eq            #0xa37cb8
    //     0xa37cb4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37cb8: mov             x0, x20
    // 0xa37cbc: StoreField: r1->field_33 = r0
    //     0xa37cbc: stur            w0, [x1, #0x33]
    //     0xa37cc0: ldurb           w16, [x1, #-1]
    //     0xa37cc4: ldurb           w17, [x0, #-1]
    //     0xa37cc8: and             x16, x17, x16, lsr #2
    //     0xa37ccc: tst             x16, HEAP, lsr #32
    //     0xa37cd0: b.eq            #0xa37cd8
    //     0xa37cd4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37cd8: mov             x0, x19
    // 0xa37cdc: StoreField: r1->field_37 = r0
    //     0xa37cdc: stur            w0, [x1, #0x37]
    //     0xa37ce0: ldurb           w16, [x1, #-1]
    //     0xa37ce4: ldurb           w17, [x0, #-1]
    //     0xa37ce8: and             x16, x17, x16, lsr #2
    //     0xa37cec: tst             x16, HEAP, lsr #32
    //     0xa37cf0: b.eq            #0xa37cf8
    //     0xa37cf4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37cf8: mov             x0, x5
    // 0xa37cfc: StoreField: r1->field_3b = r0
    //     0xa37cfc: stur            w0, [x1, #0x3b]
    //     0xa37d00: ldurb           w16, [x1, #-1]
    //     0xa37d04: ldurb           w17, [x0, #-1]
    //     0xa37d08: and             x16, x17, x16, lsr #2
    //     0xa37d0c: tst             x16, HEAP, lsr #32
    //     0xa37d10: b.eq            #0xa37d18
    //     0xa37d14: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37d18: mov             x0, x10
    // 0xa37d1c: StoreField: r1->field_3f = r0
    //     0xa37d1c: stur            w0, [x1, #0x3f]
    //     0xa37d20: ldurb           w16, [x1, #-1]
    //     0xa37d24: ldurb           w17, [x0, #-1]
    //     0xa37d28: and             x16, x17, x16, lsr #2
    //     0xa37d2c: tst             x16, HEAP, lsr #32
    //     0xa37d30: b.eq            #0xa37d38
    //     0xa37d34: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37d38: mov             x0, x11
    // 0xa37d3c: StoreField: r1->field_43 = r0
    //     0xa37d3c: stur            w0, [x1, #0x43]
    //     0xa37d40: ldurb           w16, [x1, #-1]
    //     0xa37d44: ldurb           w17, [x0, #-1]
    //     0xa37d48: and             x16, x17, x16, lsr #2
    //     0xa37d4c: tst             x16, HEAP, lsr #32
    //     0xa37d50: b.eq            #0xa37d58
    //     0xa37d54: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37d58: StoreField: r1->field_4b = r24
    //     0xa37d58: stur            w24, [x1, #0x4b]
    // 0xa37d5c: StoreField: r1->field_4f = r7
    //     0xa37d5c: stur            w7, [x1, #0x4f]
    // 0xa37d60: StoreField: r1->field_53 = r4
    //     0xa37d60: stur            w4, [x1, #0x53]
    // 0xa37d64: ldur            x0, [fp, #-0x20]
    // 0xa37d68: StoreField: r1->field_57 = r0
    //     0xa37d68: stur            w0, [x1, #0x57]
    //     0xa37d6c: ldurb           w16, [x1, #-1]
    //     0xa37d70: ldurb           w17, [x0, #-1]
    //     0xa37d74: and             x16, x17, x16, lsr #2
    //     0xa37d78: tst             x16, HEAP, lsr #32
    //     0xa37d7c: b.eq            #0xa37d84
    //     0xa37d80: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37d84: StoreField: r1->field_5b = d1
    //     0xa37d84: stur            d1, [x1, #0x5b]
    // 0xa37d88: StoreField: r1->field_63 = d0
    //     0xa37d88: stur            d0, [x1, #0x63]
    // 0xa37d8c: mov             x0, x12
    // 0xa37d90: StoreField: r1->field_6f = r0
    //     0xa37d90: stur            w0, [x1, #0x6f]
    //     0xa37d94: ldurb           w16, [x1, #-1]
    //     0xa37d98: ldurb           w17, [x0, #-1]
    //     0xa37d9c: and             x16, x17, x16, lsr #2
    //     0xa37da0: tst             x16, HEAP, lsr #32
    //     0xa37da4: b.eq            #0xa37dac
    //     0xa37da8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37dac: ldur            x0, [fp, #-0x18]
    // 0xa37db0: StoreField: r1->field_7f = r0
    //     0xa37db0: stur            w0, [x1, #0x7f]
    //     0xa37db4: ldurb           w16, [x1, #-1]
    //     0xa37db8: ldurb           w17, [x0, #-1]
    //     0xa37dbc: and             x16, x17, x16, lsr #2
    //     0xa37dc0: tst             x16, HEAP, lsr #32
    //     0xa37dc4: b.eq            #0xa37dcc
    //     0xa37dc8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37dcc: StoreField: r1->field_83 = r4
    //     0xa37dcc: stur            w4, [x1, #0x83]
    // 0xa37dd0: cmp             w2, NULL
    // 0xa37dd4: b.ne            #0xa37de0
    // 0xa37dd8: r3 = Null
    //     0xa37dd8: mov             x3, NULL
    // 0xa37ddc: b               #0xa37e18
    // 0xa37de0: LoadField: r0 = r2->field_f
    //     0xa37de0: ldur            w0, [x2, #0xf]
    // 0xa37de4: DecompressPointer r0
    //     0xa37de4: add             x0, x0, HEAP, lsl #32
    // 0xa37de8: LoadField: d0 = r0->field_f
    //     0xa37de8: ldur            d0, [x0, #0xf]
    // 0xa37dec: r0 = inline_Allocate_Double()
    //     0xa37dec: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa37df0: add             x0, x0, #0x10
    //     0xa37df4: cmp             x2, x0
    //     0xa37df8: b.ls            #0xa37e90
    //     0xa37dfc: str             x0, [THR, #0x50]  ; THR::top
    //     0xa37e00: sub             x0, x0, #0xf
    //     0xa37e04: movz            x2, #0xe15c
    //     0xa37e08: movk            x2, #0x3, lsl #16
    //     0xa37e0c: stur            x2, [x0, #-1]
    // 0xa37e10: StoreField: r0->field_7 = d0
    //     0xa37e10: stur            d0, [x0, #7]
    // 0xa37e14: mov             x3, x0
    // 0xa37e18: stur            x3, [fp, #-0x10]
    // 0xa37e1c: r0 = _PreferredAppBarSize()
    //     0xa37e1c: bl              #0xa37fb4  ; Allocate_PreferredAppBarSizeStub -> _PreferredAppBarSize (size=0x20)
    // 0xa37e20: mov             x1, x0
    // 0xa37e24: ldur            x2, [fp, #-0x28]
    // 0xa37e28: ldur            x3, [fp, #-0x10]
    // 0xa37e2c: stur            x0, [fp, #-0x10]
    // 0xa37e30: r0 = _PreferredAppBarSize()
    //     0xa37e30: bl              #0xa37ea8  ; [package:flutter/src/material/app_bar.dart] _PreferredAppBarSize::_PreferredAppBarSize
    // 0xa37e34: ldur            x0, [fp, #-0x10]
    // 0xa37e38: ldur            x1, [fp, #-0x30]
    // 0xa37e3c: StoreField: r1->field_6b = r0
    //     0xa37e3c: stur            w0, [x1, #0x6b]
    //     0xa37e40: ldurb           w16, [x1, #-1]
    //     0xa37e44: ldurb           w17, [x0, #-1]
    //     0xa37e48: and             x16, x17, x16, lsr #2
    //     0xa37e4c: tst             x16, HEAP, lsr #32
    //     0xa37e50: b.eq            #0xa37e58
    //     0xa37e54: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37e58: ldur            x0, [fp, #-8]
    // 0xa37e5c: StoreField: r1->field_7 = r0
    //     0xa37e5c: stur            w0, [x1, #7]
    //     0xa37e60: ldurb           w16, [x1, #-1]
    //     0xa37e64: ldurb           w17, [x0, #-1]
    //     0xa37e68: and             x16, x17, x16, lsr #2
    //     0xa37e6c: tst             x16, HEAP, lsr #32
    //     0xa37e70: b.eq            #0xa37e78
    //     0xa37e74: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa37e78: r0 = Null
    //     0xa37e78: mov             x0, NULL
    // 0xa37e7c: LeaveFrame
    //     0xa37e7c: mov             SP, fp
    //     0xa37e80: ldp             fp, lr, [SP], #0x10
    // 0xa37e84: ret
    //     0xa37e84: ret             
    // 0xa37e88: r0 = StackOverflowSharedWithFPURegs()
    //     0xa37e88: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa37e8c: b               #0xa37bd0
    // 0xa37e90: SaveReg d0
    //     0xa37e90: str             q0, [SP, #-0x10]!
    // 0xa37e94: stp             x1, x12, [SP, #-0x10]!
    // 0xa37e98: r0 = AllocateDouble()
    //     0xa37e98: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa37e9c: ldp             x1, x12, [SP], #0x10
    // 0xa37ea0: RestoreReg d0
    //     0xa37ea0: ldr             q0, [SP], #0x10
    // 0xa37ea4: b               #0xa37e10
  }
  _ createState(/* No info */) {
    // ** addr: 0xa8f820, size: 0x2c
    // 0xa8f820: EnterFrame
    //     0xa8f820: stp             fp, lr, [SP, #-0x10]!
    //     0xa8f824: mov             fp, SP
    // 0xa8f828: mov             x0, x1
    // 0xa8f82c: r1 = <AppBar>
    //     0xa8f82c: add             x1, PP, #0x39, lsl #12  ; [pp+0x39a30] TypeArguments: <AppBar>
    //     0xa8f830: ldr             x1, [x1, #0xa30]
    // 0xa8f834: r0 = _AppBarState()
    //     0xa8f834: bl              #0xa8f84c  ; Allocate_AppBarStateStub -> _AppBarState (size=0x1c)
    // 0xa8f838: r1 = false
    //     0xa8f838: add             x1, NULL, #0x30  ; false
    // 0xa8f83c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa8f83c: stur            w1, [x0, #0x17]
    // 0xa8f840: LeaveFrame
    //     0xa8f840: mov             SP, fp
    //     0xa8f844: ldp             fp, lr, [SP], #0x10
    // 0xa8f848: ret
    //     0xa8f848: ret             
  }
}

// class id: 6092, size: 0x20, field offset: 0x18
class _PreferredAppBarSize extends Size {

  _ _PreferredAppBarSize(/* No info */) {
    // ** addr: 0xa37ea8, size: 0x10c
    // 0xa37ea8: EnterFrame
    //     0xa37ea8: stp             fp, lr, [SP, #-0x10]!
    //     0xa37eac: mov             fp, SP
    // 0xa37eb0: AllocStack(0x18)
    //     0xa37eb0: sub             SP, SP, #0x18
    // 0xa37eb4: SetupParameters(_PreferredAppBarSize this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r1 */)
    //     0xa37eb4: stur            x1, [fp, #-8]
    //     0xa37eb8: mov             x16, x3
    //     0xa37ebc: mov             x3, x1
    //     0xa37ec0: mov             x1, x16
    // 0xa37ec4: CheckStackOverflow
    //     0xa37ec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa37ec8: cmp             SP, x16
    //     0xa37ecc: b.ls            #0xa37f90
    // 0xa37ed0: mov             x0, x2
    // 0xa37ed4: ArrayStore: r3[0] = r0  ; List_4
    //     0xa37ed4: stur            w0, [x3, #0x17]
    //     0xa37ed8: ldurb           w16, [x3, #-1]
    //     0xa37edc: ldurb           w17, [x0, #-1]
    //     0xa37ee0: and             x16, x17, x16, lsr #2
    //     0xa37ee4: tst             x16, HEAP, lsr #32
    //     0xa37ee8: b.eq            #0xa37ef0
    //     0xa37eec: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa37ef0: mov             x0, x1
    // 0xa37ef4: StoreField: r3->field_1b = r0
    //     0xa37ef4: stur            w0, [x3, #0x1b]
    //     0xa37ef8: ldurb           w16, [x3, #-1]
    //     0xa37efc: ldurb           w17, [x0, #-1]
    //     0xa37f00: and             x16, x17, x16, lsr #2
    //     0xa37f04: tst             x16, HEAP, lsr #32
    //     0xa37f08: b.eq            #0xa37f10
    //     0xa37f0c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa37f10: cmp             w2, NULL
    // 0xa37f14: b.ne            #0xa37f24
    // 0xa37f18: d0 = 56.000000
    //     0xa37f18: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0xa37f1c: ldr             d0, [x17, #0xf60]
    // 0xa37f20: b               #0xa37f28
    // 0xa37f24: LoadField: d0 = r2->field_7
    //     0xa37f24: ldur            d0, [x2, #7]
    // 0xa37f28: cmp             w1, NULL
    // 0xa37f2c: b.ne            #0xa37f38
    // 0xa37f30: r0 = 0
    //     0xa37f30: movz            x0, #0
    // 0xa37f34: b               #0xa37f3c
    // 0xa37f38: mov             x0, x1
    // 0xa37f3c: r1 = inline_Allocate_Double()
    //     0xa37f3c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xa37f40: add             x1, x1, #0x10
    //     0xa37f44: cmp             x2, x1
    //     0xa37f48: b.ls            #0xa37f98
    //     0xa37f4c: str             x1, [THR, #0x50]  ; THR::top
    //     0xa37f50: sub             x1, x1, #0xf
    //     0xa37f54: movz            x2, #0xe15c
    //     0xa37f58: movk            x2, #0x3, lsl #16
    //     0xa37f5c: stur            x2, [x1, #-1]
    // 0xa37f60: StoreField: r1->field_7 = d0
    //     0xa37f60: stur            d0, [x1, #7]
    // 0xa37f64: stp             x0, x1, [SP]
    // 0xa37f68: r0 = +()
    //     0xa37f68: bl              #0xebf900  ; [dart:core] _Double::+
    // 0xa37f6c: ldur            x1, [fp, #-8]
    // 0xa37f70: d0 = inf
    //     0xa37f70: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xa37f74: StoreField: r1->field_7 = d0
    //     0xa37f74: stur            d0, [x1, #7]
    // 0xa37f78: LoadField: d0 = r0->field_7
    //     0xa37f78: ldur            d0, [x0, #7]
    // 0xa37f7c: StoreField: r1->field_f = d0
    //     0xa37f7c: stur            d0, [x1, #0xf]
    // 0xa37f80: r0 = Null
    //     0xa37f80: mov             x0, NULL
    // 0xa37f84: LeaveFrame
    //     0xa37f84: mov             SP, fp
    //     0xa37f88: ldp             fp, lr, [SP], #0x10
    // 0xa37f8c: ret
    //     0xa37f8c: ret             
    // 0xa37f90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa37f90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa37f94: b               #0xa37ed0
    // 0xa37f98: SaveReg d0
    //     0xa37f98: str             q0, [SP, #-0x10]!
    // 0xa37f9c: stp             x0, x3, [SP, #-0x10]!
    // 0xa37fa0: r0 = AllocateDouble()
    //     0xa37fa0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa37fa4: mov             x1, x0
    // 0xa37fa8: ldp             x0, x3, [SP], #0x10
    // 0xa37fac: RestoreReg d0
    //     0xa37fac: ldr             q0, [SP], #0x10
    // 0xa37fb0: b               #0xa37f60
  }
}

// class id: 7079, size: 0x14, field offset: 0x14
enum _SliverAppVariant extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc488d8, size: 0x64
    // 0xc488d8: EnterFrame
    //     0xc488d8: stp             fp, lr, [SP, #-0x10]!
    //     0xc488dc: mov             fp, SP
    // 0xc488e0: AllocStack(0x10)
    //     0xc488e0: sub             SP, SP, #0x10
    // 0xc488e4: SetupParameters(_SliverAppVariant this /* r1 => r0, fp-0x8 */)
    //     0xc488e4: mov             x0, x1
    //     0xc488e8: stur            x1, [fp, #-8]
    // 0xc488ec: CheckStackOverflow
    //     0xc488ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc488f0: cmp             SP, x16
    //     0xc488f4: b.ls            #0xc48934
    // 0xc488f8: r1 = Null
    //     0xc488f8: mov             x1, NULL
    // 0xc488fc: r2 = 4
    //     0xc488fc: movz            x2, #0x4
    // 0xc48900: r0 = AllocateArray()
    //     0xc48900: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc48904: r16 = "_SliverAppVariant."
    //     0xc48904: add             x16, PP, #0x39, lsl #12  ; [pp+0x39a28] "_SliverAppVariant."
    //     0xc48908: ldr             x16, [x16, #0xa28]
    // 0xc4890c: StoreField: r0->field_f = r16
    //     0xc4890c: stur            w16, [x0, #0xf]
    // 0xc48910: ldur            x1, [fp, #-8]
    // 0xc48914: LoadField: r2 = r1->field_f
    //     0xc48914: ldur            w2, [x1, #0xf]
    // 0xc48918: DecompressPointer r2
    //     0xc48918: add             x2, x2, HEAP, lsl #32
    // 0xc4891c: StoreField: r0->field_13 = r2
    //     0xc4891c: stur            w2, [x0, #0x13]
    // 0xc48920: str             x0, [SP]
    // 0xc48924: r0 = _interpolate()
    //     0xc48924: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc48928: LeaveFrame
    //     0xc48928: mov             SP, fp
    //     0xc4892c: ldp             fp, lr, [SP], #0x10
    // 0xc48930: ret
    //     0xc48930: ret             
    // 0xc48934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc48934: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc48938: b               #0xc488f8
  }
}
