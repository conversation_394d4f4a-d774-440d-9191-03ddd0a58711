// lib: , url: package:flutter/src/material/badge.dart

// class id: 1048854, size: 0x8
class :: {
}

// class id: 3153, size: 0x64, field offset: 0x5c
class _RenderIntrinsicHorizontalStadium extends RenderProxyBox {

  dynamic computeMinIntrinsicWidth(dynamic) {
    // ** addr: 0x72d418, size: 0x24
    // 0x72d418: EnterFrame
    //     0x72d418: stp             fp, lr, [SP, #-0x10]!
    //     0x72d41c: mov             fp, SP
    // 0x72d420: ldr             x2, [fp, #0x10]
    // 0x72d424: r1 = Function 'computeMinIntrinsicWidth':.
    //     0x72d424: add             x1, PP, #0x55, lsl #12  ; [pp+0x550c8] AnonymousClosure: (0x72d43c), of [package:flutter/src/material/badge.dart] _RenderIntrinsicHorizontalStadium
    //     0x72d428: ldr             x1, [x1, #0xc8]
    // 0x72d42c: r0 = AllocateClosure()
    //     0x72d42c: bl              #0xec1630  ; AllocateClosureStub
    // 0x72d430: LeaveFrame
    //     0x72d430: mov             SP, fp
    //     0x72d434: ldp             fp, lr, [SP], #0x10
    // 0x72d438: ret
    //     0x72d438: ret             
  }
  [closure] double computeMinIntrinsicWidth(dynamic, double) {
    // ** addr: 0x72d43c, size: 0x78
    // 0x72d43c: EnterFrame
    //     0x72d43c: stp             fp, lr, [SP, #-0x10]!
    //     0x72d440: mov             fp, SP
    // 0x72d444: ldr             x0, [fp, #0x18]
    // 0x72d448: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x72d448: ldur            w1, [x0, #0x17]
    // 0x72d44c: DecompressPointer r1
    //     0x72d44c: add             x1, x1, HEAP, lsl #32
    // 0x72d450: CheckStackOverflow
    //     0x72d450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72d454: cmp             SP, x16
    //     0x72d458: b.ls            #0x72d49c
    // 0x72d45c: ldr             x0, [fp, #0x10]
    // 0x72d460: LoadField: d0 = r0->field_7
    //     0x72d460: ldur            d0, [x0, #7]
    // 0x72d464: r0 = getMaxIntrinsicWidth()
    //     0x72d464: bl              #0x72d4b4  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicWidth
    // 0x72d468: r0 = inline_Allocate_Double()
    //     0x72d468: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x72d46c: add             x0, x0, #0x10
    //     0x72d470: cmp             x1, x0
    //     0x72d474: b.ls            #0x72d4a4
    //     0x72d478: str             x0, [THR, #0x50]  ; THR::top
    //     0x72d47c: sub             x0, x0, #0xf
    //     0x72d480: movz            x1, #0xe15c
    //     0x72d484: movk            x1, #0x3, lsl #16
    //     0x72d488: stur            x1, [x0, #-1]
    // 0x72d48c: StoreField: r0->field_7 = d0
    //     0x72d48c: stur            d0, [x0, #7]
    // 0x72d490: LeaveFrame
    //     0x72d490: mov             SP, fp
    //     0x72d494: ldp             fp, lr, [SP], #0x10
    // 0x72d498: ret
    //     0x72d498: ret             
    // 0x72d49c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72d49c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72d4a0: b               #0x72d45c
    // 0x72d4a4: SaveReg d0
    //     0x72d4a4: str             q0, [SP, #-0x10]!
    // 0x72d4a8: r0 = AllocateDouble()
    //     0x72d4a8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x72d4ac: RestoreReg d0
    //     0x72d4ac: ldr             q0, [SP], #0x10
    // 0x72d4b0: b               #0x72d48c
  }
  _ computeDryBaseline(/* No info */) {
    // ** addr: 0x73b8fc, size: 0xb0
    // 0x73b8fc: EnterFrame
    //     0x73b8fc: stp             fp, lr, [SP, #-0x10]!
    //     0x73b900: mov             fp, SP
    // 0x73b904: AllocStack(0x20)
    //     0x73b904: sub             SP, SP, #0x20
    // 0x73b908: SetupParameters(_RenderIntrinsicHorizontalStadium this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x73b908: mov             x5, x1
    //     0x73b90c: mov             x4, x2
    //     0x73b910: stur            x1, [fp, #-8]
    //     0x73b914: stur            x2, [fp, #-0x10]
    //     0x73b918: stur            x3, [fp, #-0x18]
    // 0x73b91c: CheckStackOverflow
    //     0x73b91c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73b920: cmp             SP, x16
    //     0x73b924: b.ls            #0x73b9a0
    // 0x73b928: mov             x0, x4
    // 0x73b92c: r2 = Null
    //     0x73b92c: mov             x2, NULL
    // 0x73b930: r1 = Null
    //     0x73b930: mov             x1, NULL
    // 0x73b934: r4 = 60
    //     0x73b934: movz            x4, #0x3c
    // 0x73b938: branchIfSmi(r0, 0x73b944)
    //     0x73b938: tbz             w0, #0, #0x73b944
    // 0x73b93c: r4 = LoadClassIdInstr(r0)
    //     0x73b93c: ldur            x4, [x0, #-1]
    //     0x73b940: ubfx            x4, x4, #0xc, #0x14
    // 0x73b944: sub             x4, x4, #0xc83
    // 0x73b948: cmp             x4, #1
    // 0x73b94c: b.ls            #0x73b960
    // 0x73b950: r8 = BoxConstraints
    //     0x73b950: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x73b954: r3 = Null
    //     0x73b954: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e268] Null
    //     0x73b958: ldr             x3, [x3, #0x268]
    // 0x73b95c: r0 = BoxConstraints()
    //     0x73b95c: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x73b960: ldur            x1, [fp, #-8]
    // 0x73b964: LoadField: r0 = r1->field_57
    //     0x73b964: ldur            w0, [x1, #0x57]
    // 0x73b968: DecompressPointer r0
    //     0x73b968: add             x0, x0, HEAP, lsl #32
    // 0x73b96c: stur            x0, [fp, #-0x20]
    // 0x73b970: cmp             w0, NULL
    // 0x73b974: b.eq            #0x73b9a8
    // 0x73b978: mov             x2, x0
    // 0x73b97c: ldur            x3, [fp, #-0x10]
    // 0x73b980: r0 = _childConstraints()
    //     0x73b980: bl              #0x73b9ac  ; [package:flutter/src/material/badge.dart] _RenderIntrinsicHorizontalStadium::_childConstraints
    // 0x73b984: ldur            x1, [fp, #-0x20]
    // 0x73b988: mov             x2, x0
    // 0x73b98c: ldur            x3, [fp, #-0x18]
    // 0x73b990: r0 = getDryBaseline()
    //     0x73b990: bl              #0x730f54  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryBaseline
    // 0x73b994: LeaveFrame
    //     0x73b994: mov             SP, fp
    //     0x73b998: ldp             fp, lr, [SP], #0x10
    // 0x73b99c: ret
    //     0x73b99c: ret             
    // 0x73b9a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73b9a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73b9a4: b               #0x73b928
    // 0x73b9a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x73b9a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _childConstraints(/* No info */) {
    // ** addr: 0x73b9ac, size: 0x184
    // 0x73b9ac: EnterFrame
    //     0x73b9ac: stp             fp, lr, [SP, #-0x10]!
    //     0x73b9b0: mov             fp, SP
    // 0x73b9b4: AllocStack(0x28)
    //     0x73b9b4: sub             SP, SP, #0x28
    // 0x73b9b8: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0x73b9b8: mov             x0, x3
    //     0x73b9bc: stur            x2, [fp, #-8]
    //     0x73b9c0: stur            x3, [fp, #-0x10]
    // 0x73b9c4: CheckStackOverflow
    //     0x73b9c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73b9c8: cmp             SP, x16
    //     0x73b9cc: b.ls            #0x73bafc
    // 0x73b9d0: LoadField: d1 = r1->field_5b
    //     0x73b9d0: ldur            d1, [x1, #0x5b]
    // 0x73b9d4: stur            d1, [fp, #-0x18]
    // 0x73b9d8: LoadField: d0 = r0->field_f
    //     0x73b9d8: ldur            d0, [x0, #0xf]
    // 0x73b9dc: mov             x1, x2
    // 0x73b9e0: r0 = getMaxIntrinsicHeight()
    //     0x73b9e0: bl              #0x72d954  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicHeight
    // 0x73b9e4: mov             v1.16b, v0.16b
    // 0x73b9e8: ldur            d0, [fp, #-0x18]
    // 0x73b9ec: fcmp            d0, d1
    // 0x73b9f0: b.le            #0x73ba00
    // 0x73b9f4: mov             v1.16b, v0.16b
    // 0x73b9f8: d2 = 0.000000
    //     0x73b9f8: eor             v2.16b, v2.16b, v2.16b
    // 0x73b9fc: b               #0x73ba34
    // 0x73ba00: fcmp            d1, d0
    // 0x73ba04: b.le            #0x73ba10
    // 0x73ba08: d2 = 0.000000
    //     0x73ba08: eor             v2.16b, v2.16b, v2.16b
    // 0x73ba0c: b               #0x73ba34
    // 0x73ba10: d2 = 0.000000
    //     0x73ba10: eor             v2.16b, v2.16b, v2.16b
    // 0x73ba14: fcmp            d0, d2
    // 0x73ba18: b.ne            #0x73ba28
    // 0x73ba1c: fadd            d3, d0, d1
    // 0x73ba20: mov             v1.16b, v3.16b
    // 0x73ba24: b               #0x73ba34
    // 0x73ba28: fcmp            d1, d1
    // 0x73ba2c: b.vs            #0x73ba34
    // 0x73ba30: mov             v1.16b, v0.16b
    // 0x73ba34: ldur            x0, [fp, #-0x10]
    // 0x73ba38: stur            d1, [fp, #-0x18]
    // 0x73ba3c: LoadField: d0 = r0->field_1f
    //     0x73ba3c: ldur            d0, [x0, #0x1f]
    // 0x73ba40: ldur            x1, [fp, #-8]
    // 0x73ba44: r0 = getMaxIntrinsicWidth()
    //     0x73ba44: bl              #0x72d4b4  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicWidth
    // 0x73ba48: mov             v1.16b, v0.16b
    // 0x73ba4c: ldur            d0, [fp, #-0x18]
    // 0x73ba50: fcmp            d1, d0
    // 0x73ba54: b.gt            #0x73ba8c
    // 0x73ba58: fcmp            d0, d1
    // 0x73ba5c: b.le            #0x73ba68
    // 0x73ba60: mov             v1.16b, v0.16b
    // 0x73ba64: b               #0x73ba8c
    // 0x73ba68: d2 = 0.000000
    //     0x73ba68: eor             v2.16b, v2.16b, v2.16b
    // 0x73ba6c: fcmp            d1, d2
    // 0x73ba70: b.ne            #0x73ba80
    // 0x73ba74: fadd            d2, d1, d0
    // 0x73ba78: mov             v1.16b, v2.16b
    // 0x73ba7c: b               #0x73ba8c
    // 0x73ba80: fcmp            d0, d0
    // 0x73ba84: b.vc            #0x73ba8c
    // 0x73ba88: mov             v1.16b, v0.16b
    // 0x73ba8c: r0 = inline_Allocate_Double()
    //     0x73ba8c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x73ba90: add             x0, x0, #0x10
    //     0x73ba94: cmp             x1, x0
    //     0x73ba98: b.ls            #0x73bb04
    //     0x73ba9c: str             x0, [THR, #0x50]  ; THR::top
    //     0x73baa0: sub             x0, x0, #0xf
    //     0x73baa4: movz            x1, #0xe15c
    //     0x73baa8: movk            x1, #0x3, lsl #16
    //     0x73baac: stur            x1, [x0, #-1]
    // 0x73bab0: StoreField: r0->field_7 = d0
    //     0x73bab0: stur            d0, [x0, #7]
    // 0x73bab4: r1 = inline_Allocate_Double()
    //     0x73bab4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x73bab8: add             x1, x1, #0x10
    //     0x73babc: cmp             x2, x1
    //     0x73bac0: b.ls            #0x73bb14
    //     0x73bac4: str             x1, [THR, #0x50]  ; THR::top
    //     0x73bac8: sub             x1, x1, #0xf
    //     0x73bacc: movz            x2, #0xe15c
    //     0x73bad0: movk            x2, #0x3, lsl #16
    //     0x73bad4: stur            x2, [x1, #-1]
    // 0x73bad8: StoreField: r1->field_7 = d1
    //     0x73bad8: stur            d1, [x1, #7]
    // 0x73badc: stp             x0, x1, [SP]
    // 0x73bae0: ldur            x1, [fp, #-0x10]
    // 0x73bae4: r4 = const [0, 0x3, 0x2, 0x1, height, 0x2, width, 0x1, null]
    //     0x73bae4: add             x4, PP, #0x4e, lsl #12  ; [pp+0x4e260] List(9) [0, 0x3, 0x2, 0x1, "height", 0x2, "width", 0x1, Null]
    //     0x73bae8: ldr             x4, [x4, #0x260]
    // 0x73baec: r0 = tighten()
    //     0x73baec: bl              #0x73bb30  ; [package:flutter/src/rendering/box.dart] BoxConstraints::tighten
    // 0x73baf0: LeaveFrame
    //     0x73baf0: mov             SP, fp
    //     0x73baf4: ldp             fp, lr, [SP], #0x10
    // 0x73baf8: ret
    //     0x73baf8: ret             
    // 0x73bafc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73bafc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73bb00: b               #0x73b9d0
    // 0x73bb04: stp             q0, q1, [SP, #-0x20]!
    // 0x73bb08: r0 = AllocateDouble()
    //     0x73bb08: bl              #0xec2254  ; AllocateDoubleStub
    // 0x73bb0c: ldp             q0, q1, [SP], #0x20
    // 0x73bb10: b               #0x73bab0
    // 0x73bb14: SaveReg d1
    //     0x73bb14: str             q1, [SP, #-0x10]!
    // 0x73bb18: SaveReg r0
    //     0x73bb18: str             x0, [SP, #-8]!
    // 0x73bb1c: r0 = AllocateDouble()
    //     0x73bb1c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x73bb20: mov             x1, x0
    // 0x73bb24: RestoreReg r0
    //     0x73bb24: ldr             x0, [SP], #8
    // 0x73bb28: RestoreReg d1
    //     0x73bb28: ldr             q1, [SP], #0x10
    // 0x73bb2c: b               #0x73bad8
  }
  dynamic computeMinIntrinsicHeight(dynamic) {
    // ** addr: 0x747884, size: 0x24
    // 0x747884: EnterFrame
    //     0x747884: stp             fp, lr, [SP, #-0x10]!
    //     0x747888: mov             fp, SP
    // 0x74788c: ldr             x2, [fp, #0x10]
    // 0x747890: r1 = Function 'computeMinIntrinsicHeight':.
    //     0x747890: add             x1, PP, #0x55, lsl #12  ; [pp+0x550b8] AnonymousClosure: (0x7478a8), of [package:flutter/src/material/badge.dart] _RenderIntrinsicHorizontalStadium
    //     0x747894: ldr             x1, [x1, #0xb8]
    // 0x747898: r0 = AllocateClosure()
    //     0x747898: bl              #0xec1630  ; AllocateClosureStub
    // 0x74789c: LeaveFrame
    //     0x74789c: mov             SP, fp
    //     0x7478a0: ldp             fp, lr, [SP], #0x10
    // 0x7478a4: ret
    //     0x7478a4: ret             
  }
  [closure] double computeMinIntrinsicHeight(dynamic, double) {
    // ** addr: 0x7478a8, size: 0x78
    // 0x7478a8: EnterFrame
    //     0x7478a8: stp             fp, lr, [SP, #-0x10]!
    //     0x7478ac: mov             fp, SP
    // 0x7478b0: ldr             x0, [fp, #0x18]
    // 0x7478b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7478b4: ldur            w1, [x0, #0x17]
    // 0x7478b8: DecompressPointer r1
    //     0x7478b8: add             x1, x1, HEAP, lsl #32
    // 0x7478bc: CheckStackOverflow
    //     0x7478bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7478c0: cmp             SP, x16
    //     0x7478c4: b.ls            #0x747908
    // 0x7478c8: ldr             x0, [fp, #0x10]
    // 0x7478cc: LoadField: d0 = r0->field_7
    //     0x7478cc: ldur            d0, [x0, #7]
    // 0x7478d0: r0 = getMaxIntrinsicHeight()
    //     0x7478d0: bl              #0x72d954  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicHeight
    // 0x7478d4: r0 = inline_Allocate_Double()
    //     0x7478d4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7478d8: add             x0, x0, #0x10
    //     0x7478dc: cmp             x1, x0
    //     0x7478e0: b.ls            #0x747910
    //     0x7478e4: str             x0, [THR, #0x50]  ; THR::top
    //     0x7478e8: sub             x0, x0, #0xf
    //     0x7478ec: movz            x1, #0xe15c
    //     0x7478f0: movk            x1, #0x3, lsl #16
    //     0x7478f4: stur            x1, [x0, #-1]
    // 0x7478f8: StoreField: r0->field_7 = d0
    //     0x7478f8: stur            d0, [x0, #7]
    // 0x7478fc: LeaveFrame
    //     0x7478fc: mov             SP, fp
    //     0x747900: ldp             fp, lr, [SP], #0x10
    // 0x747904: ret
    //     0x747904: ret             
    // 0x747908: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x747908: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74790c: b               #0x7478c8
    // 0x747910: SaveReg d0
    //     0x747910: str             q0, [SP, #-0x10]!
    // 0x747914: r0 = AllocateDouble()
    //     0x747914: bl              #0xec2254  ; AllocateDoubleStub
    // 0x747918: RestoreReg d0
    //     0x747918: ldr             q0, [SP], #0x10
    // 0x74791c: b               #0x7478f8
  }
  dynamic computeMaxIntrinsicWidth(dynamic) {
    // ** addr: 0x74dc8c, size: 0x24
    // 0x74dc8c: EnterFrame
    //     0x74dc8c: stp             fp, lr, [SP, #-0x10]!
    //     0x74dc90: mov             fp, SP
    // 0x74dc94: ldr             x2, [fp, #0x10]
    // 0x74dc98: r1 = Function 'computeMaxIntrinsicWidth':.
    //     0x74dc98: add             x1, PP, #0x55, lsl #12  ; [pp+0x550c0] AnonymousClosure: (0x74dcb0), in [package:flutter/src/material/badge.dart] _RenderIntrinsicHorizontalStadium::computeMaxIntrinsicWidth (0x74dd24)
    //     0x74dc9c: ldr             x1, [x1, #0xc0]
    // 0x74dca0: r0 = AllocateClosure()
    //     0x74dca0: bl              #0xec1630  ; AllocateClosureStub
    // 0x74dca4: LeaveFrame
    //     0x74dca4: mov             SP, fp
    //     0x74dca8: ldp             fp, lr, [SP], #0x10
    // 0x74dcac: ret
    //     0x74dcac: ret             
  }
  [closure] double computeMaxIntrinsicWidth(dynamic, double) {
    // ** addr: 0x74dcb0, size: 0x74
    // 0x74dcb0: EnterFrame
    //     0x74dcb0: stp             fp, lr, [SP, #-0x10]!
    //     0x74dcb4: mov             fp, SP
    // 0x74dcb8: ldr             x0, [fp, #0x18]
    // 0x74dcbc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x74dcbc: ldur            w1, [x0, #0x17]
    // 0x74dcc0: DecompressPointer r1
    //     0x74dcc0: add             x1, x1, HEAP, lsl #32
    // 0x74dcc4: CheckStackOverflow
    //     0x74dcc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74dcc8: cmp             SP, x16
    //     0x74dccc: b.ls            #0x74dd0c
    // 0x74dcd0: ldr             x2, [fp, #0x10]
    // 0x74dcd4: r0 = computeMaxIntrinsicWidth()
    //     0x74dcd4: bl              #0x74dd24  ; [package:flutter/src/material/badge.dart] _RenderIntrinsicHorizontalStadium::computeMaxIntrinsicWidth
    // 0x74dcd8: r0 = inline_Allocate_Double()
    //     0x74dcd8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74dcdc: add             x0, x0, #0x10
    //     0x74dce0: cmp             x1, x0
    //     0x74dce4: b.ls            #0x74dd14
    //     0x74dce8: str             x0, [THR, #0x50]  ; THR::top
    //     0x74dcec: sub             x0, x0, #0xf
    //     0x74dcf0: movz            x1, #0xe15c
    //     0x74dcf4: movk            x1, #0x3, lsl #16
    //     0x74dcf8: stur            x1, [x0, #-1]
    // 0x74dcfc: StoreField: r0->field_7 = d0
    //     0x74dcfc: stur            d0, [x0, #7]
    // 0x74dd00: LeaveFrame
    //     0x74dd00: mov             SP, fp
    //     0x74dd04: ldp             fp, lr, [SP], #0x10
    // 0x74dd08: ret
    //     0x74dd08: ret             
    // 0x74dd0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74dd0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74dd10: b               #0x74dcd0
    // 0x74dd14: SaveReg d0
    //     0x74dd14: str             q0, [SP, #-0x10]!
    // 0x74dd18: r0 = AllocateDouble()
    //     0x74dd18: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74dd1c: RestoreReg d0
    //     0x74dd1c: ldr             q0, [SP], #0x10
    // 0x74dd20: b               #0x74dcfc
  }
  _ computeMaxIntrinsicWidth(/* No info */) {
    // ** addr: 0x74dd24, size: 0x94
    // 0x74dd24: EnterFrame
    //     0x74dd24: stp             fp, lr, [SP, #-0x10]!
    //     0x74dd28: mov             fp, SP
    // 0x74dd2c: AllocStack(0x18)
    //     0x74dd2c: sub             SP, SP, #0x18
    // 0x74dd30: SetupParameters(_RenderIntrinsicHorizontalStadium this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x74dd30: mov             x0, x1
    //     0x74dd34: stur            x1, [fp, #-8]
    //     0x74dd38: stur            x2, [fp, #-0x10]
    // 0x74dd3c: CheckStackOverflow
    //     0x74dd3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74dd40: cmp             SP, x16
    //     0x74dd44: b.ls            #0x74ddb0
    // 0x74dd48: mov             x1, x0
    // 0x74dd4c: d0 = inf
    //     0x74dd4c: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x74dd50: r0 = getMaxIntrinsicHeight()
    //     0x74dd50: bl              #0x72d954  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicHeight
    // 0x74dd54: ldur            x1, [fp, #-8]
    // 0x74dd58: ldur            x2, [fp, #-0x10]
    // 0x74dd5c: stur            d0, [fp, #-0x18]
    // 0x74dd60: r0 = computeMaxIntrinsicWidth()
    //     0x74dd60: bl              #0x74db7c  ; [package:flutter/src/widgets/single_child_scroll_view.dart] _RenderSingleChildViewport::computeMaxIntrinsicWidth
    // 0x74dd64: ldur            d1, [fp, #-0x18]
    // 0x74dd68: fcmp            d1, d0
    // 0x74dd6c: b.le            #0x74dd78
    // 0x74dd70: mov             v0.16b, v1.16b
    // 0x74dd74: b               #0x74dda4
    // 0x74dd78: fcmp            d0, d1
    // 0x74dd7c: b.gt            #0x74dda4
    // 0x74dd80: d2 = 0.000000
    //     0x74dd80: eor             v2.16b, v2.16b, v2.16b
    // 0x74dd84: fcmp            d1, d2
    // 0x74dd88: b.ne            #0x74dd98
    // 0x74dd8c: fadd            d2, d1, d0
    // 0x74dd90: mov             v0.16b, v2.16b
    // 0x74dd94: b               #0x74dda4
    // 0x74dd98: fcmp            d0, d0
    // 0x74dd9c: b.vs            #0x74dda4
    // 0x74dda0: mov             v0.16b, v1.16b
    // 0x74dda4: LeaveFrame
    //     0x74dda4: mov             SP, fp
    //     0x74dda8: ldp             fp, lr, [SP], #0x10
    // 0x74ddac: ret
    //     0x74ddac: ret             
    // 0x74ddb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74ddb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74ddb4: b               #0x74dd48
  }
  dynamic computeMaxIntrinsicHeight(dynamic) {
    // ** addr: 0x750cb8, size: 0x24
    // 0x750cb8: EnterFrame
    //     0x750cb8: stp             fp, lr, [SP, #-0x10]!
    //     0x750cbc: mov             fp, SP
    // 0x750cc0: ldr             x2, [fp, #0x10]
    // 0x750cc4: r1 = Function 'computeMaxIntrinsicHeight':.
    //     0x750cc4: add             x1, PP, #0x55, lsl #12  ; [pp+0x550b0] AnonymousClosure: (0x750cdc), in [package:flutter/src/material/badge.dart] _RenderIntrinsicHorizontalStadium::computeMaxIntrinsicHeight (0x750d50)
    //     0x750cc8: ldr             x1, [x1, #0xb0]
    // 0x750ccc: r0 = AllocateClosure()
    //     0x750ccc: bl              #0xec1630  ; AllocateClosureStub
    // 0x750cd0: LeaveFrame
    //     0x750cd0: mov             SP, fp
    //     0x750cd4: ldp             fp, lr, [SP], #0x10
    // 0x750cd8: ret
    //     0x750cd8: ret             
  }
  [closure] double computeMaxIntrinsicHeight(dynamic, double) {
    // ** addr: 0x750cdc, size: 0x74
    // 0x750cdc: EnterFrame
    //     0x750cdc: stp             fp, lr, [SP, #-0x10]!
    //     0x750ce0: mov             fp, SP
    // 0x750ce4: ldr             x0, [fp, #0x18]
    // 0x750ce8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x750ce8: ldur            w1, [x0, #0x17]
    // 0x750cec: DecompressPointer r1
    //     0x750cec: add             x1, x1, HEAP, lsl #32
    // 0x750cf0: CheckStackOverflow
    //     0x750cf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x750cf4: cmp             SP, x16
    //     0x750cf8: b.ls            #0x750d38
    // 0x750cfc: ldr             x2, [fp, #0x10]
    // 0x750d00: r0 = computeMaxIntrinsicHeight()
    //     0x750d00: bl              #0x750d50  ; [package:flutter/src/material/badge.dart] _RenderIntrinsicHorizontalStadium::computeMaxIntrinsicHeight
    // 0x750d04: r0 = inline_Allocate_Double()
    //     0x750d04: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x750d08: add             x0, x0, #0x10
    //     0x750d0c: cmp             x1, x0
    //     0x750d10: b.ls            #0x750d40
    //     0x750d14: str             x0, [THR, #0x50]  ; THR::top
    //     0x750d18: sub             x0, x0, #0xf
    //     0x750d1c: movz            x1, #0xe15c
    //     0x750d20: movk            x1, #0x3, lsl #16
    //     0x750d24: stur            x1, [x0, #-1]
    // 0x750d28: StoreField: r0->field_7 = d0
    //     0x750d28: stur            d0, [x0, #7]
    // 0x750d2c: LeaveFrame
    //     0x750d2c: mov             SP, fp
    //     0x750d30: ldp             fp, lr, [SP], #0x10
    // 0x750d34: ret
    //     0x750d34: ret             
    // 0x750d38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x750d38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x750d3c: b               #0x750cfc
    // 0x750d40: SaveReg d0
    //     0x750d40: str             q0, [SP, #-0x10]!
    // 0x750d44: r0 = AllocateDouble()
    //     0x750d44: bl              #0xec2254  ; AllocateDoubleStub
    // 0x750d48: RestoreReg d0
    //     0x750d48: ldr             q0, [SP], #0x10
    // 0x750d4c: b               #0x750d28
  }
  _ computeMaxIntrinsicHeight(/* No info */) {
    // ** addr: 0x750d50, size: 0x78
    // 0x750d50: EnterFrame
    //     0x750d50: stp             fp, lr, [SP, #-0x10]!
    //     0x750d54: mov             fp, SP
    // 0x750d58: AllocStack(0x8)
    //     0x750d58: sub             SP, SP, #8
    // 0x750d5c: CheckStackOverflow
    //     0x750d5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x750d60: cmp             SP, x16
    //     0x750d64: b.ls            #0x750dc0
    // 0x750d68: LoadField: d0 = r1->field_5b
    //     0x750d68: ldur            d0, [x1, #0x5b]
    // 0x750d6c: stur            d0, [fp, #-8]
    // 0x750d70: r0 = computeMaxIntrinsicHeight()
    //     0x750d70: bl              #0x750ba8  ; [package:flutter/src/widgets/single_child_scroll_view.dart] _RenderSingleChildViewport::computeMaxIntrinsicHeight
    // 0x750d74: ldur            d1, [fp, #-8]
    // 0x750d78: fcmp            d1, d0
    // 0x750d7c: b.le            #0x750d88
    // 0x750d80: mov             v0.16b, v1.16b
    // 0x750d84: b               #0x750db4
    // 0x750d88: fcmp            d0, d1
    // 0x750d8c: b.gt            #0x750db4
    // 0x750d90: d2 = 0.000000
    //     0x750d90: eor             v2.16b, v2.16b, v2.16b
    // 0x750d94: fcmp            d1, d2
    // 0x750d98: b.ne            #0x750da8
    // 0x750d9c: fadd            d2, d1, d0
    // 0x750da0: mov             v0.16b, v2.16b
    // 0x750da4: b               #0x750db4
    // 0x750da8: fcmp            d0, d0
    // 0x750dac: b.vs            #0x750db4
    // 0x750db0: mov             v0.16b, v1.16b
    // 0x750db4: LeaveFrame
    //     0x750db4: mov             SP, fp
    //     0x750db8: ldp             fp, lr, [SP], #0x10
    // 0x750dbc: ret
    //     0x750dbc: ret             
    // 0x750dc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x750dc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x750dc4: b               #0x750d68
  }
  _ computeDryLayout(/* No info */) {
    // ** addr: 0x753e68, size: 0x34
    // 0x753e68: EnterFrame
    //     0x753e68: stp             fp, lr, [SP, #-0x10]!
    //     0x753e6c: mov             fp, SP
    // 0x753e70: CheckStackOverflow
    //     0x753e70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x753e74: cmp             SP, x16
    //     0x753e78: b.ls            #0x753e94
    // 0x753e7c: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static.
    //     0x753e7c: add             x3, PP, #0x44, lsl #12  ; [pp+0x44d20] Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static. (0x7e54fb140c14)
    //     0x753e80: ldr             x3, [x3, #0xd20]
    // 0x753e84: r0 = _computeSize()
    //     0x753e84: bl              #0x753e9c  ; [package:flutter/src/material/badge.dart] _RenderIntrinsicHorizontalStadium::_computeSize
    // 0x753e88: LeaveFrame
    //     0x753e88: mov             SP, fp
    //     0x753e8c: ldp             fp, lr, [SP], #0x10
    // 0x753e90: ret
    //     0x753e90: ret             
    // 0x753e94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x753e94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x753e98: b               #0x753e7c
  }
  _ _computeSize(/* No info */) {
    // ** addr: 0x753e9c, size: 0xa8
    // 0x753e9c: EnterFrame
    //     0x753e9c: stp             fp, lr, [SP, #-0x10]!
    //     0x753ea0: mov             fp, SP
    // 0x753ea4: AllocStack(0x30)
    //     0x753ea4: sub             SP, SP, #0x30
    // 0x753ea8: SetupParameters(dynamic _ /* r2 => r3 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0x753ea8: mov             x0, x3
    //     0x753eac: stur            x3, [fp, #-0x10]
    //     0x753eb0: mov             x3, x2
    // 0x753eb4: CheckStackOverflow
    //     0x753eb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x753eb8: cmp             SP, x16
    //     0x753ebc: b.ls            #0x753f38
    // 0x753ec0: LoadField: r4 = r1->field_57
    //     0x753ec0: ldur            w4, [x1, #0x57]
    // 0x753ec4: DecompressPointer r4
    //     0x753ec4: add             x4, x4, HEAP, lsl #32
    // 0x753ec8: stur            x4, [fp, #-8]
    // 0x753ecc: cmp             w4, NULL
    // 0x753ed0: b.eq            #0x753f40
    // 0x753ed4: mov             x2, x4
    // 0x753ed8: r0 = _childConstraints()
    //     0x753ed8: bl              #0x73b9ac  ; [package:flutter/src/material/badge.dart] _RenderIntrinsicHorizontalStadium::_childConstraints
    // 0x753edc: ldur            x16, [fp, #-0x10]
    // 0x753ee0: ldur            lr, [fp, #-8]
    // 0x753ee4: stp             lr, x16, [SP, #8]
    // 0x753ee8: str             x0, [SP]
    // 0x753eec: ldur            x0, [fp, #-0x10]
    // 0x753ef0: ClosureCall
    //     0x753ef0: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x753ef4: ldur            x2, [x0, #0x1f]
    //     0x753ef8: blr             x2
    // 0x753efc: LoadField: d0 = r0->field_f
    //     0x753efc: ldur            d0, [x0, #0xf]
    // 0x753f00: stur            d0, [fp, #-0x18]
    // 0x753f04: LoadField: d1 = r0->field_7
    //     0x753f04: ldur            d1, [x0, #7]
    // 0x753f08: fcmp            d0, d1
    // 0x753f0c: b.le            #0x753f2c
    // 0x753f10: r0 = Size()
    //     0x753f10: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x753f14: ldur            d0, [fp, #-0x18]
    // 0x753f18: StoreField: r0->field_7 = d0
    //     0x753f18: stur            d0, [x0, #7]
    // 0x753f1c: StoreField: r0->field_f = d0
    //     0x753f1c: stur            d0, [x0, #0xf]
    // 0x753f20: LeaveFrame
    //     0x753f20: mov             SP, fp
    //     0x753f24: ldp             fp, lr, [SP], #0x10
    // 0x753f28: ret
    //     0x753f28: ret             
    // 0x753f2c: LeaveFrame
    //     0x753f2c: mov             SP, fp
    //     0x753f30: ldp             fp, lr, [SP], #0x10
    // 0x753f34: ret
    //     0x753f34: ret             
    // 0x753f38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x753f38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x753f3c: b               #0x753ec0
    // 0x753f40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x753f40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x76732c, size: 0xcc
    // 0x76732c: EnterFrame
    //     0x76732c: stp             fp, lr, [SP, #-0x10]!
    //     0x767330: mov             fp, SP
    // 0x767334: AllocStack(0x10)
    //     0x767334: sub             SP, SP, #0x10
    // 0x767338: SetupParameters(_RenderIntrinsicHorizontalStadium this /* r1 => r3, fp-0x10 */)
    //     0x767338: mov             x3, x1
    //     0x76733c: stur            x1, [fp, #-0x10]
    // 0x767340: CheckStackOverflow
    //     0x767340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x767344: cmp             SP, x16
    //     0x767348: b.ls            #0x7673f0
    // 0x76734c: LoadField: r4 = r3->field_27
    //     0x76734c: ldur            w4, [x3, #0x27]
    // 0x767350: DecompressPointer r4
    //     0x767350: add             x4, x4, HEAP, lsl #32
    // 0x767354: stur            x4, [fp, #-8]
    // 0x767358: cmp             w4, NULL
    // 0x76735c: b.eq            #0x7673d4
    // 0x767360: mov             x0, x4
    // 0x767364: r2 = Null
    //     0x767364: mov             x2, NULL
    // 0x767368: r1 = Null
    //     0x767368: mov             x1, NULL
    // 0x76736c: r4 = LoadClassIdInstr(r0)
    //     0x76736c: ldur            x4, [x0, #-1]
    //     0x767370: ubfx            x4, x4, #0xc, #0x14
    // 0x767374: sub             x4, x4, #0xc83
    // 0x767378: cmp             x4, #1
    // 0x76737c: b.ls            #0x767390
    // 0x767380: r8 = BoxConstraints
    //     0x767380: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x767384: r3 = Null
    //     0x767384: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e250] Null
    //     0x767388: ldr             x3, [x3, #0x250]
    // 0x76738c: r0 = BoxConstraints()
    //     0x76738c: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x767390: ldur            x1, [fp, #-0x10]
    // 0x767394: ldur            x2, [fp, #-8]
    // 0x767398: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static.
    //     0x767398: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b28] Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static. (0x7e54fb1673f8)
    //     0x76739c: ldr             x3, [x3, #0xb28]
    // 0x7673a0: r0 = _computeSize()
    //     0x7673a0: bl              #0x753e9c  ; [package:flutter/src/material/badge.dart] _RenderIntrinsicHorizontalStadium::_computeSize
    // 0x7673a4: ldur            x1, [fp, #-0x10]
    // 0x7673a8: StoreField: r1->field_53 = r0
    //     0x7673a8: stur            w0, [x1, #0x53]
    //     0x7673ac: ldurb           w16, [x1, #-1]
    //     0x7673b0: ldurb           w17, [x0, #-1]
    //     0x7673b4: and             x16, x17, x16, lsr #2
    //     0x7673b8: tst             x16, HEAP, lsr #32
    //     0x7673bc: b.eq            #0x7673c4
    //     0x7673c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7673c4: r0 = Null
    //     0x7673c4: mov             x0, NULL
    // 0x7673c8: LeaveFrame
    //     0x7673c8: mov             SP, fp
    //     0x7673cc: ldp             fp, lr, [SP], #0x10
    // 0x7673d0: ret
    //     0x7673d0: ret             
    // 0x7673d4: r0 = StateError()
    //     0x7673d4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x7673d8: mov             x1, x0
    // 0x7673dc: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x7673dc: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x7673e0: StoreField: r1->field_b = r0
    //     0x7673e0: stur            w0, [x1, #0xb]
    // 0x7673e4: mov             x0, x1
    // 0x7673e8: r0 = Throw()
    //     0x7673e8: bl              #0xec04b8  ; ThrowStub
    // 0x7673ec: brk             #0
    // 0x7673f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7673f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7673f4: b               #0x76734c
  }
}

// class id: 4000, size: 0x34, field offset: 0x28
class _BadgeDefaultsM3 extends BadgeThemeData {

  late final ColorScheme _colors; // offset: 0x30
  late final ThemeData _theme; // offset: 0x2c

  ColorScheme _colors(_BadgeDefaultsM3) {
    // ** addr: 0xa9953c, size: 0x58
    // 0xa9953c: EnterFrame
    //     0xa9953c: stp             fp, lr, [SP, #-0x10]!
    //     0xa99540: mov             fp, SP
    // 0xa99544: CheckStackOverflow
    //     0xa99544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa99548: cmp             SP, x16
    //     0xa9954c: b.ls            #0xa9958c
    // 0xa99550: ldr             x1, [fp, #0x10]
    // 0xa99554: LoadField: r0 = r1->field_2b
    //     0xa99554: ldur            w0, [x1, #0x2b]
    // 0xa99558: DecompressPointer r0
    //     0xa99558: add             x0, x0, HEAP, lsl #32
    // 0xa9955c: r16 = Sentinel
    //     0xa9955c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa99560: cmp             w0, w16
    // 0xa99564: b.ne            #0xa99574
    // 0xa99568: r2 = _theme
    //     0xa99568: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4d0] Field <_BadgeDefaultsM3@503139524._theme@503139524>: late final (offset: 0x2c)
    //     0xa9956c: ldr             x2, [x2, #0x4d0]
    // 0xa99570: r0 = InitLateFinalInstanceField()
    //     0xa99570: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa99574: LoadField: r1 = r0->field_3f
    //     0xa99574: ldur            w1, [x0, #0x3f]
    // 0xa99578: DecompressPointer r1
    //     0xa99578: add             x1, x1, HEAP, lsl #32
    // 0xa9957c: mov             x0, x1
    // 0xa99580: LeaveFrame
    //     0xa99580: mov             SP, fp
    //     0xa99584: ldp             fp, lr, [SP], #0x10
    // 0xa99588: ret
    //     0xa99588: ret             
    // 0xa9958c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9958c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa99590: b               #0xa99550
  }
  ThemeData _theme(_BadgeDefaultsM3) {
    // ** addr: 0xa99594, size: 0x38
    // 0xa99594: EnterFrame
    //     0xa99594: stp             fp, lr, [SP, #-0x10]!
    //     0xa99598: mov             fp, SP
    // 0xa9959c: CheckStackOverflow
    //     0xa9959c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa995a0: cmp             SP, x16
    //     0xa995a4: b.ls            #0xa995c4
    // 0xa995a8: ldr             x0, [fp, #0x10]
    // 0xa995ac: LoadField: r1 = r0->field_27
    //     0xa995ac: ldur            w1, [x0, #0x27]
    // 0xa995b0: DecompressPointer r1
    //     0xa995b0: add             x1, x1, HEAP, lsl #32
    // 0xa995b4: r0 = of()
    //     0xa995b4: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa995b8: LeaveFrame
    //     0xa995b8: mov             SP, fp
    //     0xa995bc: ldp             fp, lr, [SP], #0x10
    // 0xa995c0: ret
    //     0xa995c0: ret             
    // 0xa995c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa995c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa995c8: b               #0xa995a8
  }
}

// class id: 4557, size: 0x18, field offset: 0x10
//   const constructor, 
class _IntrinsicHorizontalStadium extends SingleChildRenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x85a0b0, size: 0x4c
    // 0x85a0b0: EnterFrame
    //     0x85a0b0: stp             fp, lr, [SP, #-0x10]!
    //     0x85a0b4: mov             fp, SP
    // 0x85a0b8: AllocStack(0x10)
    //     0x85a0b8: sub             SP, SP, #0x10
    // 0x85a0bc: CheckStackOverflow
    //     0x85a0bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85a0c0: cmp             SP, x16
    //     0x85a0c4: b.ls            #0x85a0f4
    // 0x85a0c8: LoadField: d0 = r1->field_f
    //     0x85a0c8: ldur            d0, [x1, #0xf]
    // 0x85a0cc: stur            d0, [fp, #-0x10]
    // 0x85a0d0: r0 = _RenderIntrinsicHorizontalStadium()
    //     0x85a0d0: bl              #0x85a16c  ; Allocate_RenderIntrinsicHorizontalStadiumStub -> _RenderIntrinsicHorizontalStadium (size=0x64)
    // 0x85a0d4: mov             x1, x0
    // 0x85a0d8: ldur            d0, [fp, #-0x10]
    // 0x85a0dc: stur            x0, [fp, #-8]
    // 0x85a0e0: r0 = RenderAspectRatio()
    //     0x85a0e0: bl              #0x85a0fc  ; [package:flutter/src/rendering/proxy_box.dart] RenderAspectRatio::RenderAspectRatio
    // 0x85a0e4: ldur            x0, [fp, #-8]
    // 0x85a0e8: LeaveFrame
    //     0x85a0e8: mov             SP, fp
    //     0x85a0ec: ldp             fp, lr, [SP], #0x10
    // 0x85a0f0: ret
    //     0x85a0f0: ret             
    // 0x85a0f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85a0f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85a0f8: b               #0x85a0c8
  }
}

// class id: 5402, size: 0x2c, field offset: 0xc
//   const constructor, 
class Badge extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xa9929c, size: 0x208
    // 0xa9929c: EnterFrame
    //     0xa9929c: stp             fp, lr, [SP, #-0x10]!
    //     0xa992a0: mov             fp, SP
    // 0xa992a4: AllocStack(0x58)
    //     0xa992a4: sub             SP, SP, #0x58
    // 0xa992a8: SetupParameters(Badge this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa992a8: mov             x0, x2
    //     0xa992ac: stur            x2, [fp, #-0x10]
    //     0xa992b0: mov             x2, x1
    //     0xa992b4: stur            x1, [fp, #-8]
    // 0xa992b8: CheckStackOverflow
    //     0xa992b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa992bc: cmp             SP, x16
    //     0xa992c0: b.ls            #0xa99498
    // 0xa992c4: mov             x1, x0
    // 0xa992c8: r0 = of()
    //     0xa992c8: bl              #0xa994e4  ; [package:flutter/src/material/badge_theme.dart] BadgeTheme::of
    // 0xa992cc: stur            x0, [fp, #-0x18]
    // 0xa992d0: r0 = _BadgeDefaultsM3()
    //     0xa992d0: bl              #0xa994d8  ; Allocate_BadgeDefaultsM3Stub -> _BadgeDefaultsM3 (size=0x34)
    // 0xa992d4: mov             x1, x0
    // 0xa992d8: r0 = Sentinel
    //     0xa992d8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa992dc: StoreField: r1->field_2b = r0
    //     0xa992dc: stur            w0, [x1, #0x2b]
    // 0xa992e0: StoreField: r1->field_2f = r0
    //     0xa992e0: stur            w0, [x1, #0x2f]
    // 0xa992e4: ldur            x0, [fp, #-0x10]
    // 0xa992e8: StoreField: r1->field_27 = r0
    //     0xa992e8: stur            w0, [x1, #0x27]
    // 0xa992ec: r2 = 6.000000
    //     0xa992ec: add             x2, PP, #0x35, lsl #12  ; [pp+0x35d70] 6
    //     0xa992f0: ldr             x2, [x2, #0xd70]
    // 0xa992f4: StoreField: r1->field_f = r2
    //     0xa992f4: stur            w2, [x1, #0xf]
    // 0xa992f8: r2 = 16.000000
    //     0xa992f8: add             x2, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xa992fc: ldr             x2, [x2, #0x80]
    // 0xa99300: StoreField: r1->field_13 = r2
    //     0xa99300: stur            w2, [x1, #0x13]
    // 0xa99304: r2 = Instance_EdgeInsets
    //     0xa99304: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2abf0] Obj!EdgeInsets@e12461
    //     0xa99308: ldr             x2, [x2, #0xbf0]
    // 0xa9930c: StoreField: r1->field_1b = r2
    //     0xa9930c: stur            w2, [x1, #0x1b]
    // 0xa99310: r2 = Instance_AlignmentDirectional
    //     0xa99310: add             x2, PP, #0x39, lsl #12  ; [pp+0x39a08] Obj!AlignmentDirectional@e13dd1
    //     0xa99314: ldr             x2, [x2, #0xa08]
    // 0xa99318: StoreField: r1->field_1f = r2
    //     0xa99318: stur            w2, [x1, #0x1f]
    // 0xa9931c: LoadField: r0 = r1->field_2f
    //     0xa9931c: ldur            w0, [x1, #0x2f]
    // 0xa99320: DecompressPointer r0
    //     0xa99320: add             x0, x0, HEAP, lsl #32
    // 0xa99324: r16 = Sentinel
    //     0xa99324: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa99328: cmp             w0, w16
    // 0xa9932c: b.ne            #0xa9933c
    // 0xa99330: r2 = _colors
    //     0xa99330: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d4c8] Field <_BadgeDefaultsM3@503139524._colors@503139524>: late final (offset: 0x30)
    //     0xa99334: ldr             x2, [x2, #0x4c8]
    // 0xa99338: r0 = InitLateFinalInstanceField()
    //     0xa99338: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9933c: LoadField: r1 = r0->field_6b
    //     0xa9933c: ldur            w1, [x0, #0x6b]
    // 0xa99340: DecompressPointer r1
    //     0xa99340: add             x1, x1, HEAP, lsl #32
    // 0xa99344: stur            x1, [fp, #-0x20]
    // 0xa99348: r0 = ShapeDecoration()
    //     0xa99348: bl              #0x7f3e70  ; AllocateShapeDecorationStub -> ShapeDecoration (size=0x1c)
    // 0xa9934c: mov             x2, x0
    // 0xa99350: ldur            x0, [fp, #-0x20]
    // 0xa99354: stur            x2, [fp, #-0x28]
    // 0xa99358: StoreField: r2->field_7 = r0
    //     0xa99358: stur            w0, [x2, #7]
    // 0xa9935c: r0 = Instance_StadiumBorder
    //     0xa9935c: add             x0, PP, #0x39, lsl #12  ; [pp+0x39a10] Obj!StadiumBorder@e14671
    //     0xa99360: ldr             x0, [x0, #0xa10]
    // 0xa99364: ArrayStore: r2[0] = r0  ; List_4
    //     0xa99364: stur            w0, [x2, #0x17]
    // 0xa99368: ldur            x0, [fp, #-0x18]
    // 0xa9936c: LoadField: r1 = r0->field_13
    //     0xa9936c: ldur            w1, [x0, #0x13]
    // 0xa99370: DecompressPointer r1
    //     0xa99370: add             x1, x1, HEAP, lsl #32
    // 0xa99374: cmp             w1, NULL
    // 0xa99378: b.ne            #0xa99384
    // 0xa9937c: d0 = 16.000000
    //     0xa9937c: fmov            d0, #16.00000000
    // 0xa99380: b               #0xa99388
    // 0xa99384: LoadField: d0 = r1->field_7
    //     0xa99384: ldur            d0, [x1, #7]
    // 0xa99388: ldur            x1, [fp, #-0x10]
    // 0xa9938c: stur            d0, [fp, #-0x30]
    // 0xa99390: r0 = of()
    //     0xa99390: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa99394: LoadField: r1 = r0->field_8f
    //     0xa99394: ldur            w1, [x0, #0x8f]
    // 0xa99398: DecompressPointer r1
    //     0xa99398: add             x1, x1, HEAP, lsl #32
    // 0xa9939c: LoadField: r0 = r1->field_3f
    //     0xa9939c: ldur            w0, [x1, #0x3f]
    // 0xa993a0: DecompressPointer r0
    //     0xa993a0: add             x0, x0, HEAP, lsl #32
    // 0xa993a4: cmp             w0, NULL
    // 0xa993a8: b.eq            #0xa994a0
    // 0xa993ac: r16 = Instance_Color
    //     0xa993ac: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xa993b0: str             x16, [SP]
    // 0xa993b4: mov             x1, x0
    // 0xa993b8: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xa993b8: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xa993bc: ldr             x4, [x4, #0x228]
    // 0xa993c0: r0 = copyWith()
    //     0xa993c0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa993c4: mov             x1, x0
    // 0xa993c8: ldur            x0, [fp, #-0x18]
    // 0xa993cc: stur            x1, [fp, #-0x20]
    // 0xa993d0: LoadField: r2 = r0->field_1b
    //     0xa993d0: ldur            w2, [x0, #0x1b]
    // 0xa993d4: DecompressPointer r2
    //     0xa993d4: add             x2, x2, HEAP, lsl #32
    // 0xa993d8: cmp             w2, NULL
    // 0xa993dc: b.ne            #0xa993e8
    // 0xa993e0: r2 = Instance_EdgeInsets
    //     0xa993e0: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2abf0] Obj!EdgeInsets@e12461
    //     0xa993e4: ldr             x2, [x2, #0xbf0]
    // 0xa993e8: ldur            x0, [fp, #-8]
    // 0xa993ec: ldur            d0, [fp, #-0x30]
    // 0xa993f0: stur            x2, [fp, #-0x18]
    // 0xa993f4: LoadField: r3 = r0->field_1f
    //     0xa993f4: ldur            w3, [x0, #0x1f]
    // 0xa993f8: DecompressPointer r3
    //     0xa993f8: add             x3, x3, HEAP, lsl #32
    // 0xa993fc: stur            x3, [fp, #-0x10]
    // 0xa99400: r0 = Container()
    //     0xa99400: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa99404: stur            x0, [fp, #-8]
    // 0xa99408: r16 = Instance_Clip
    //     0xa99408: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d4f8] Obj!Clip@e39b21
    //     0xa9940c: ldr             x16, [x16, #0x4f8]
    // 0xa99410: ldur            lr, [fp, #-0x28]
    // 0xa99414: stp             lr, x16, [SP, #0x18]
    // 0xa99418: ldur            x16, [fp, #-0x18]
    // 0xa9941c: r30 = Instance_Alignment
    //     0xa9941c: add             lr, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xa99420: ldr             lr, [lr, #0x898]
    // 0xa99424: stp             lr, x16, [SP, #8]
    // 0xa99428: ldur            x16, [fp, #-0x10]
    // 0xa9942c: str             x16, [SP]
    // 0xa99430: mov             x1, x0
    // 0xa99434: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x4, child, 0x5, clipBehavior, 0x1, decoration, 0x2, padding, 0x3, null]
    //     0xa99434: add             x4, PP, #0x39, lsl #12  ; [pp+0x39a18] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x4, "child", 0x5, "clipBehavior", 0x1, "decoration", 0x2, "padding", 0x3, Null]
    //     0xa99438: ldr             x4, [x4, #0xa18]
    // 0xa9943c: r0 = Container()
    //     0xa9943c: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa99440: r0 = _IntrinsicHorizontalStadium()
    //     0xa99440: bl              #0xa994cc  ; Allocate_IntrinsicHorizontalStadiumStub -> _IntrinsicHorizontalStadium (size=0x18)
    // 0xa99444: ldur            d0, [fp, #-0x30]
    // 0xa99448: stur            x0, [fp, #-0x10]
    // 0xa9944c: StoreField: r0->field_f = d0
    //     0xa9944c: stur            d0, [x0, #0xf]
    // 0xa99450: ldur            x1, [fp, #-8]
    // 0xa99454: StoreField: r0->field_b = r1
    //     0xa99454: stur            w1, [x0, #0xb]
    // 0xa99458: r0 = DefaultTextStyle()
    //     0xa99458: bl              #0x9d5004  ; AllocateDefaultTextStyleStub -> DefaultTextStyle (size=0x2c)
    // 0xa9945c: ldur            x1, [fp, #-0x20]
    // 0xa99460: StoreField: r0->field_f = r1
    //     0xa99460: stur            w1, [x0, #0xf]
    // 0xa99464: r1 = true
    //     0xa99464: add             x1, NULL, #0x20  ; true
    // 0xa99468: ArrayStore: r0[0] = r1  ; List_4
    //     0xa99468: stur            w1, [x0, #0x17]
    // 0xa9946c: r1 = Instance_TextOverflow
    //     0xa9946c: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xa99470: ldr             x1, [x1, #0xc60]
    // 0xa99474: StoreField: r0->field_1b = r1
    //     0xa99474: stur            w1, [x0, #0x1b]
    // 0xa99478: r1 = Instance_TextWidthBasis
    //     0xa99478: add             x1, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0xa9947c: ldr             x1, [x1, #0x1d8]
    // 0xa99480: StoreField: r0->field_23 = r1
    //     0xa99480: stur            w1, [x0, #0x23]
    // 0xa99484: ldur            x1, [fp, #-0x10]
    // 0xa99488: StoreField: r0->field_b = r1
    //     0xa99488: stur            w1, [x0, #0xb]
    // 0xa9948c: LeaveFrame
    //     0xa9948c: mov             SP, fp
    //     0xa99490: ldp             fp, lr, [SP], #0x10
    // 0xa99494: ret
    //     0xa99494: ret             
    // 0xa99498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa99498: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9949c: b               #0xa992c4
    // 0xa994a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa994a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  const Widget? dyn:get:label(Badge) {
    // ** addr: 0xa994bc, size: 0x28
    // 0xa994bc: ldr             x1, [SP]
    // 0xa994c0: LoadField: r0 = r1->field_1f
    //     0xa994c0: ldur            w0, [x1, #0x1f]
    // 0xa994c4: DecompressPointer r0
    //     0xa994c4: add             x0, x0, HEAP, lsl #32
    // 0xa994c8: ret
    //     0xa994c8: ret             
  }
  _ Badge.count(/* No info */) {
    // ** addr: 0xae31f0, size: 0xb4
    // 0xae31f0: EnterFrame
    //     0xae31f0: stp             fp, lr, [SP, #-0x10]!
    //     0xae31f4: mov             fp, SP
    // 0xae31f8: AllocStack(0x18)
    //     0xae31f8: sub             SP, SP, #0x18
    // 0xae31fc: r3 = Instance_Color
    //     0xae31fc: ldr             x3, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xae3200: r0 = true
    //     0xae3200: add             x0, NULL, #0x20  ; true
    // 0xae3204: mov             x4, x1
    // 0xae3208: stur            x1, [fp, #-8]
    // 0xae320c: CheckStackOverflow
    //     0xae320c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3210: cmp             SP, x16
    //     0xae3214: b.ls            #0xae329c
    // 0xae3218: StoreField: r4->field_f = r3
    //     0xae3218: stur            w3, [x4, #0xf]
    // 0xae321c: StoreField: r4->field_23 = r0
    //     0xae321c: stur            w0, [x4, #0x23]
    // 0xae3220: cmp             x2, #0x3e7
    // 0xae3224: b.le            #0xae3238
    // 0xae3228: mov             x0, x4
    // 0xae322c: r1 = "999+"
    //     0xae322c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f540] "999+"
    //     0xae3230: ldr             x1, [x1, #0x540]
    // 0xae3234: b               #0xae325c
    // 0xae3238: r0 = BoxInt64Instr(r2)
    //     0xae3238: sbfiz           x0, x2, #1, #0x1f
    //     0xae323c: cmp             x2, x0, asr #1
    //     0xae3240: b.eq            #0xae324c
    //     0xae3244: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae3248: stur            x2, [x0, #7]
    // 0xae324c: str             x0, [SP]
    // 0xae3250: r0 = _interpolateSingle()
    //     0xae3250: bl              #0x5fff0c  ; [dart:core] _StringBase::_interpolateSingle
    // 0xae3254: mov             x1, x0
    // 0xae3258: ldur            x0, [fp, #-8]
    // 0xae325c: stur            x1, [fp, #-0x10]
    // 0xae3260: r0 = Text()
    //     0xae3260: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae3264: ldur            x1, [fp, #-0x10]
    // 0xae3268: StoreField: r0->field_b = r1
    //     0xae3268: stur            w1, [x0, #0xb]
    // 0xae326c: ldur            x1, [fp, #-8]
    // 0xae3270: StoreField: r1->field_1f = r0
    //     0xae3270: stur            w0, [x1, #0x1f]
    //     0xae3274: ldurb           w16, [x1, #-1]
    //     0xae3278: ldurb           w17, [x0, #-1]
    //     0xae327c: and             x16, x17, x16, lsr #2
    //     0xae3280: tst             x16, HEAP, lsr #32
    //     0xae3284: b.eq            #0xae328c
    //     0xae3288: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xae328c: r0 = Null
    //     0xae328c: mov             x0, NULL
    // 0xae3290: LeaveFrame
    //     0xae3290: mov             SP, fp
    //     0xae3294: ldp             fp, lr, [SP], #0x10
    // 0xae3298: ret
    //     0xae3298: ret             
    // 0xae329c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae329c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae32a0: b               #0xae3218
  }
}
