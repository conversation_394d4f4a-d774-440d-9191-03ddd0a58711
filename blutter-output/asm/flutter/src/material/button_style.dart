// lib: , url: package:flutter/src/material/button_style.dart

// class id: 1048865, size: 0x8
class :: {
}

// class id: 3985, size: 0x68, field offset: 0x8
//   const constructor, 
class ButtonStyle extends _DiagnosticableTree&Object&Diagnosticable {

  static _ lerp(/* No info */) {
    // ** addr: 0x87a1c8, size: 0x9bc
    // 0x87a1c8: EnterFrame
    //     0x87a1c8: stp             fp, lr, [SP, #-0x10]!
    //     0x87a1cc: mov             fp, SP
    // 0x87a1d0: AllocStack(0xe8)
    //     0x87a1d0: sub             SP, SP, #0xe8
    // 0x87a1d4: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* d0 => d0, fp-0xc0 */)
    //     0x87a1d4: mov             x0, x1
    //     0x87a1d8: stur            x1, [fp, #-8]
    //     0x87a1dc: stur            x2, [fp, #-0x10]
    //     0x87a1e0: stur            d0, [fp, #-0xc0]
    // 0x87a1e4: CheckStackOverflow
    //     0x87a1e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x87a1e8: cmp             SP, x16
    //     0x87a1ec: b.ls            #0x87ab7c
    // 0x87a1f0: cmp             w0, w2
    // 0x87a1f4: b.ne            #0x87a204
    // 0x87a1f8: LeaveFrame
    //     0x87a1f8: mov             SP, fp
    //     0x87a1fc: ldp             fp, lr, [SP], #0x10
    // 0x87a200: ret
    //     0x87a200: ret             
    // 0x87a204: cmp             w0, NULL
    // 0x87a208: b.ne            #0x87a214
    // 0x87a20c: r1 = Null
    //     0x87a20c: mov             x1, NULL
    // 0x87a210: b               #0x87a21c
    // 0x87a214: LoadField: r1 = r0->field_7
    //     0x87a214: ldur            w1, [x0, #7]
    // 0x87a218: DecompressPointer r1
    //     0x87a218: add             x1, x1, HEAP, lsl #32
    // 0x87a21c: cmp             w2, NULL
    // 0x87a220: b.ne            #0x87a22c
    // 0x87a224: r3 = Null
    //     0x87a224: mov             x3, NULL
    // 0x87a228: b               #0x87a234
    // 0x87a22c: LoadField: r3 = r2->field_7
    //     0x87a22c: ldur            w3, [x2, #7]
    // 0x87a230: DecompressPointer r3
    //     0x87a230: add             x3, x3, HEAP, lsl #32
    // 0x87a234: r16 = <TextStyle?>
    //     0x87a234: add             x16, PP, #0x43, lsl #12  ; [pp+0x43990] TypeArguments: <TextStyle?>
    //     0x87a238: ldr             x16, [x16, #0x990]
    // 0x87a23c: stp             x1, x16, [SP, #0x18]
    // 0x87a240: str             x3, [SP, #0x10]
    // 0x87a244: str             d0, [SP, #8]
    // 0x87a248: r16 = Closure: (TextStyle?, TextStyle?, double) => TextStyle? from Function 'lerp': static.
    //     0x87a248: add             x16, PP, #0x58, lsl #12  ; [pp+0x58ad0] Closure: (TextStyle?, TextStyle?, double) => TextStyle? from Function 'lerp': static. (0x7e54fb278d0c)
    //     0x87a24c: ldr             x16, [x16, #0xad0]
    // 0x87a250: str             x16, [SP]
    // 0x87a254: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a254: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a258: r0 = lerp()
    //     0x87a258: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a25c: mov             x1, x0
    // 0x87a260: ldur            x0, [fp, #-8]
    // 0x87a264: stur            x1, [fp, #-0x18]
    // 0x87a268: cmp             w0, NULL
    // 0x87a26c: b.ne            #0x87a278
    // 0x87a270: r3 = Null
    //     0x87a270: mov             x3, NULL
    // 0x87a274: b               #0x87a284
    // 0x87a278: LoadField: r2 = r0->field_b
    //     0x87a278: ldur            w2, [x0, #0xb]
    // 0x87a27c: DecompressPointer r2
    //     0x87a27c: add             x2, x2, HEAP, lsl #32
    // 0x87a280: mov             x3, x2
    // 0x87a284: ldur            x2, [fp, #-0x10]
    // 0x87a288: cmp             w2, NULL
    // 0x87a28c: b.ne            #0x87a298
    // 0x87a290: r4 = Null
    //     0x87a290: mov             x4, NULL
    // 0x87a294: b               #0x87a2a0
    // 0x87a298: LoadField: r4 = r2->field_b
    //     0x87a298: ldur            w4, [x2, #0xb]
    // 0x87a29c: DecompressPointer r4
    //     0x87a29c: add             x4, x4, HEAP, lsl #32
    // 0x87a2a0: ldur            d0, [fp, #-0xc0]
    // 0x87a2a4: r16 = <Color?>
    //     0x87a2a4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x87a2a8: ldr             x16, [x16, #0x98]
    // 0x87a2ac: stp             x3, x16, [SP, #0x18]
    // 0x87a2b0: str             x4, [SP, #0x10]
    // 0x87a2b4: str             d0, [SP, #8]
    // 0x87a2b8: r16 = Closure: (Color?, Color?, double) => Color? from Function 'lerp': static.
    //     0x87a2b8: add             x16, PP, #0x58, lsl #12  ; [pp+0x58ad8] Closure: (Color?, Color?, double) => Color? from Function 'lerp': static. (0x7e54fb1f45d4)
    //     0x87a2bc: ldr             x16, [x16, #0xad8]
    // 0x87a2c0: str             x16, [SP]
    // 0x87a2c4: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a2c4: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a2c8: r0 = lerp()
    //     0x87a2c8: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a2cc: mov             x1, x0
    // 0x87a2d0: ldur            x0, [fp, #-8]
    // 0x87a2d4: stur            x1, [fp, #-0x20]
    // 0x87a2d8: cmp             w0, NULL
    // 0x87a2dc: b.ne            #0x87a2e8
    // 0x87a2e0: r3 = Null
    //     0x87a2e0: mov             x3, NULL
    // 0x87a2e4: b               #0x87a2f4
    // 0x87a2e8: LoadField: r2 = r0->field_f
    //     0x87a2e8: ldur            w2, [x0, #0xf]
    // 0x87a2ec: DecompressPointer r2
    //     0x87a2ec: add             x2, x2, HEAP, lsl #32
    // 0x87a2f0: mov             x3, x2
    // 0x87a2f4: ldur            x2, [fp, #-0x10]
    // 0x87a2f8: cmp             w2, NULL
    // 0x87a2fc: b.ne            #0x87a308
    // 0x87a300: r4 = Null
    //     0x87a300: mov             x4, NULL
    // 0x87a304: b               #0x87a310
    // 0x87a308: LoadField: r4 = r2->field_f
    //     0x87a308: ldur            w4, [x2, #0xf]
    // 0x87a30c: DecompressPointer r4
    //     0x87a30c: add             x4, x4, HEAP, lsl #32
    // 0x87a310: ldur            d0, [fp, #-0xc0]
    // 0x87a314: r16 = <Color?>
    //     0x87a314: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x87a318: ldr             x16, [x16, #0x98]
    // 0x87a31c: stp             x3, x16, [SP, #0x18]
    // 0x87a320: str             x4, [SP, #0x10]
    // 0x87a324: str             d0, [SP, #8]
    // 0x87a328: r16 = Closure: (Color?, Color?, double) => Color? from Function 'lerp': static.
    //     0x87a328: add             x16, PP, #0x58, lsl #12  ; [pp+0x58ad8] Closure: (Color?, Color?, double) => Color? from Function 'lerp': static. (0x7e54fb1f45d4)
    //     0x87a32c: ldr             x16, [x16, #0xad8]
    // 0x87a330: str             x16, [SP]
    // 0x87a334: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a334: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a338: r0 = lerp()
    //     0x87a338: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a33c: mov             x1, x0
    // 0x87a340: ldur            x0, [fp, #-8]
    // 0x87a344: stur            x1, [fp, #-0x28]
    // 0x87a348: cmp             w0, NULL
    // 0x87a34c: b.ne            #0x87a358
    // 0x87a350: r3 = Null
    //     0x87a350: mov             x3, NULL
    // 0x87a354: b               #0x87a364
    // 0x87a358: LoadField: r2 = r0->field_13
    //     0x87a358: ldur            w2, [x0, #0x13]
    // 0x87a35c: DecompressPointer r2
    //     0x87a35c: add             x2, x2, HEAP, lsl #32
    // 0x87a360: mov             x3, x2
    // 0x87a364: ldur            x2, [fp, #-0x10]
    // 0x87a368: cmp             w2, NULL
    // 0x87a36c: b.ne            #0x87a378
    // 0x87a370: r4 = Null
    //     0x87a370: mov             x4, NULL
    // 0x87a374: b               #0x87a380
    // 0x87a378: LoadField: r4 = r2->field_13
    //     0x87a378: ldur            w4, [x2, #0x13]
    // 0x87a37c: DecompressPointer r4
    //     0x87a37c: add             x4, x4, HEAP, lsl #32
    // 0x87a380: ldur            d0, [fp, #-0xc0]
    // 0x87a384: r16 = <Color?>
    //     0x87a384: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x87a388: ldr             x16, [x16, #0x98]
    // 0x87a38c: stp             x3, x16, [SP, #0x18]
    // 0x87a390: str             x4, [SP, #0x10]
    // 0x87a394: str             d0, [SP, #8]
    // 0x87a398: r16 = Closure: (Color?, Color?, double) => Color? from Function 'lerp': static.
    //     0x87a398: add             x16, PP, #0x58, lsl #12  ; [pp+0x58ad8] Closure: (Color?, Color?, double) => Color? from Function 'lerp': static. (0x7e54fb1f45d4)
    //     0x87a39c: ldr             x16, [x16, #0xad8]
    // 0x87a3a0: str             x16, [SP]
    // 0x87a3a4: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a3a4: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a3a8: r0 = lerp()
    //     0x87a3a8: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a3ac: mov             x1, x0
    // 0x87a3b0: ldur            x0, [fp, #-8]
    // 0x87a3b4: stur            x1, [fp, #-0x30]
    // 0x87a3b8: cmp             w0, NULL
    // 0x87a3bc: b.ne            #0x87a3c8
    // 0x87a3c0: r3 = Null
    //     0x87a3c0: mov             x3, NULL
    // 0x87a3c4: b               #0x87a3d4
    // 0x87a3c8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x87a3c8: ldur            w2, [x0, #0x17]
    // 0x87a3cc: DecompressPointer r2
    //     0x87a3cc: add             x2, x2, HEAP, lsl #32
    // 0x87a3d0: mov             x3, x2
    // 0x87a3d4: ldur            x2, [fp, #-0x10]
    // 0x87a3d8: cmp             w2, NULL
    // 0x87a3dc: b.ne            #0x87a3e8
    // 0x87a3e0: r4 = Null
    //     0x87a3e0: mov             x4, NULL
    // 0x87a3e4: b               #0x87a3f0
    // 0x87a3e8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x87a3e8: ldur            w4, [x2, #0x17]
    // 0x87a3ec: DecompressPointer r4
    //     0x87a3ec: add             x4, x4, HEAP, lsl #32
    // 0x87a3f0: ldur            d0, [fp, #-0xc0]
    // 0x87a3f4: r16 = <Color?>
    //     0x87a3f4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x87a3f8: ldr             x16, [x16, #0x98]
    // 0x87a3fc: stp             x3, x16, [SP, #0x18]
    // 0x87a400: str             x4, [SP, #0x10]
    // 0x87a404: str             d0, [SP, #8]
    // 0x87a408: r16 = Closure: (Color?, Color?, double) => Color? from Function 'lerp': static.
    //     0x87a408: add             x16, PP, #0x58, lsl #12  ; [pp+0x58ad8] Closure: (Color?, Color?, double) => Color? from Function 'lerp': static. (0x7e54fb1f45d4)
    //     0x87a40c: ldr             x16, [x16, #0xad8]
    // 0x87a410: str             x16, [SP]
    // 0x87a414: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a414: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a418: r0 = lerp()
    //     0x87a418: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a41c: mov             x1, x0
    // 0x87a420: ldur            x0, [fp, #-8]
    // 0x87a424: stur            x1, [fp, #-0x38]
    // 0x87a428: cmp             w0, NULL
    // 0x87a42c: b.ne            #0x87a438
    // 0x87a430: r3 = Null
    //     0x87a430: mov             x3, NULL
    // 0x87a434: b               #0x87a444
    // 0x87a438: LoadField: r2 = r0->field_1b
    //     0x87a438: ldur            w2, [x0, #0x1b]
    // 0x87a43c: DecompressPointer r2
    //     0x87a43c: add             x2, x2, HEAP, lsl #32
    // 0x87a440: mov             x3, x2
    // 0x87a444: ldur            x2, [fp, #-0x10]
    // 0x87a448: cmp             w2, NULL
    // 0x87a44c: b.ne            #0x87a458
    // 0x87a450: r4 = Null
    //     0x87a450: mov             x4, NULL
    // 0x87a454: b               #0x87a460
    // 0x87a458: LoadField: r4 = r2->field_1b
    //     0x87a458: ldur            w4, [x2, #0x1b]
    // 0x87a45c: DecompressPointer r4
    //     0x87a45c: add             x4, x4, HEAP, lsl #32
    // 0x87a460: ldur            d0, [fp, #-0xc0]
    // 0x87a464: r16 = <Color?>
    //     0x87a464: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x87a468: ldr             x16, [x16, #0x98]
    // 0x87a46c: stp             x3, x16, [SP, #0x18]
    // 0x87a470: str             x4, [SP, #0x10]
    // 0x87a474: str             d0, [SP, #8]
    // 0x87a478: r16 = Closure: (Color?, Color?, double) => Color? from Function 'lerp': static.
    //     0x87a478: add             x16, PP, #0x58, lsl #12  ; [pp+0x58ad8] Closure: (Color?, Color?, double) => Color? from Function 'lerp': static. (0x7e54fb1f45d4)
    //     0x87a47c: ldr             x16, [x16, #0xad8]
    // 0x87a480: str             x16, [SP]
    // 0x87a484: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a484: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a488: r0 = lerp()
    //     0x87a488: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a48c: mov             x1, x0
    // 0x87a490: ldur            x0, [fp, #-8]
    // 0x87a494: stur            x1, [fp, #-0x40]
    // 0x87a498: cmp             w0, NULL
    // 0x87a49c: b.ne            #0x87a4a8
    // 0x87a4a0: r3 = Null
    //     0x87a4a0: mov             x3, NULL
    // 0x87a4a4: b               #0x87a4b4
    // 0x87a4a8: LoadField: r2 = r0->field_1f
    //     0x87a4a8: ldur            w2, [x0, #0x1f]
    // 0x87a4ac: DecompressPointer r2
    //     0x87a4ac: add             x2, x2, HEAP, lsl #32
    // 0x87a4b0: mov             x3, x2
    // 0x87a4b4: ldur            x2, [fp, #-0x10]
    // 0x87a4b8: cmp             w2, NULL
    // 0x87a4bc: b.ne            #0x87a4c8
    // 0x87a4c0: r4 = Null
    //     0x87a4c0: mov             x4, NULL
    // 0x87a4c4: b               #0x87a4d0
    // 0x87a4c8: LoadField: r4 = r2->field_1f
    //     0x87a4c8: ldur            w4, [x2, #0x1f]
    // 0x87a4cc: DecompressPointer r4
    //     0x87a4cc: add             x4, x4, HEAP, lsl #32
    // 0x87a4d0: ldur            d0, [fp, #-0xc0]
    // 0x87a4d4: r16 = <double?>
    //     0x87a4d4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b1c0] TypeArguments: <double?>
    //     0x87a4d8: ldr             x16, [x16, #0x1c0]
    // 0x87a4dc: stp             x3, x16, [SP, #0x18]
    // 0x87a4e0: str             x4, [SP, #0x10]
    // 0x87a4e4: str             d0, [SP, #8]
    // 0x87a4e8: r16 = Closure: (num?, num?, double) => double? from Function 'lerpDouble': static.
    //     0x87a4e8: add             x16, PP, #0x58, lsl #12  ; [pp+0x58ae0] Closure: (num?, num?, double) => double? from Function 'lerpDouble': static. (0x7e54fb0aa638)
    //     0x87a4ec: ldr             x16, [x16, #0xae0]
    // 0x87a4f0: str             x16, [SP]
    // 0x87a4f4: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a4f4: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a4f8: r0 = lerp()
    //     0x87a4f8: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a4fc: mov             x1, x0
    // 0x87a500: ldur            x0, [fp, #-8]
    // 0x87a504: stur            x1, [fp, #-0x48]
    // 0x87a508: cmp             w0, NULL
    // 0x87a50c: b.ne            #0x87a518
    // 0x87a510: r3 = Null
    //     0x87a510: mov             x3, NULL
    // 0x87a514: b               #0x87a524
    // 0x87a518: LoadField: r2 = r0->field_23
    //     0x87a518: ldur            w2, [x0, #0x23]
    // 0x87a51c: DecompressPointer r2
    //     0x87a51c: add             x2, x2, HEAP, lsl #32
    // 0x87a520: mov             x3, x2
    // 0x87a524: ldur            x2, [fp, #-0x10]
    // 0x87a528: cmp             w2, NULL
    // 0x87a52c: b.ne            #0x87a538
    // 0x87a530: r4 = Null
    //     0x87a530: mov             x4, NULL
    // 0x87a534: b               #0x87a540
    // 0x87a538: LoadField: r4 = r2->field_23
    //     0x87a538: ldur            w4, [x2, #0x23]
    // 0x87a53c: DecompressPointer r4
    //     0x87a53c: add             x4, x4, HEAP, lsl #32
    // 0x87a540: ldur            d0, [fp, #-0xc0]
    // 0x87a544: r16 = <EdgeInsetsGeometry?>
    //     0x87a544: add             x16, PP, #0x44, lsl #12  ; [pp+0x44550] TypeArguments: <EdgeInsetsGeometry?>
    //     0x87a548: ldr             x16, [x16, #0x550]
    // 0x87a54c: stp             x3, x16, [SP, #0x18]
    // 0x87a550: str             x4, [SP, #0x10]
    // 0x87a554: str             d0, [SP, #8]
    // 0x87a558: r16 = Closure: (EdgeInsetsGeometry?, EdgeInsetsGeometry?, double) => EdgeInsetsGeometry? from Function 'lerp': static.
    //     0x87a558: add             x16, PP, #0x58, lsl #12  ; [pp+0x58ae8] Closure: (EdgeInsetsGeometry?, EdgeInsetsGeometry?, double) => EdgeInsetsGeometry? from Function 'lerp': static. (0x7e54fb27786c)
    //     0x87a55c: ldr             x16, [x16, #0xae8]
    // 0x87a560: str             x16, [SP]
    // 0x87a564: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a564: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a568: r0 = lerp()
    //     0x87a568: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a56c: mov             x1, x0
    // 0x87a570: ldur            x0, [fp, #-8]
    // 0x87a574: stur            x1, [fp, #-0x50]
    // 0x87a578: cmp             w0, NULL
    // 0x87a57c: b.ne            #0x87a588
    // 0x87a580: r3 = Null
    //     0x87a580: mov             x3, NULL
    // 0x87a584: b               #0x87a594
    // 0x87a588: LoadField: r2 = r0->field_27
    //     0x87a588: ldur            w2, [x0, #0x27]
    // 0x87a58c: DecompressPointer r2
    //     0x87a58c: add             x2, x2, HEAP, lsl #32
    // 0x87a590: mov             x3, x2
    // 0x87a594: ldur            x2, [fp, #-0x10]
    // 0x87a598: cmp             w2, NULL
    // 0x87a59c: b.ne            #0x87a5a8
    // 0x87a5a0: r4 = Null
    //     0x87a5a0: mov             x4, NULL
    // 0x87a5a4: b               #0x87a5b0
    // 0x87a5a8: LoadField: r4 = r2->field_27
    //     0x87a5a8: ldur            w4, [x2, #0x27]
    // 0x87a5ac: DecompressPointer r4
    //     0x87a5ac: add             x4, x4, HEAP, lsl #32
    // 0x87a5b0: ldur            d0, [fp, #-0xc0]
    // 0x87a5b4: r16 = <Size?>
    //     0x87a5b4: add             x16, PP, #0x44, lsl #12  ; [pp+0x44560] TypeArguments: <Size?>
    //     0x87a5b8: ldr             x16, [x16, #0x560]
    // 0x87a5bc: stp             x3, x16, [SP, #0x18]
    // 0x87a5c0: str             x4, [SP, #0x10]
    // 0x87a5c4: str             d0, [SP, #8]
    // 0x87a5c8: r16 = Closure: (Size?, Size?, double) => Size? from Function 'lerp': static.
    //     0x87a5c8: add             x16, PP, #0x58, lsl #12  ; [pp+0x58af0] Closure: (Size?, Size?, double) => Size? from Function 'lerp': static. (0x7e54fb2743f8)
    //     0x87a5cc: ldr             x16, [x16, #0xaf0]
    // 0x87a5d0: str             x16, [SP]
    // 0x87a5d4: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a5d4: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a5d8: r0 = lerp()
    //     0x87a5d8: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a5dc: mov             x1, x0
    // 0x87a5e0: ldur            x0, [fp, #-8]
    // 0x87a5e4: stur            x1, [fp, #-0x58]
    // 0x87a5e8: cmp             w0, NULL
    // 0x87a5ec: b.ne            #0x87a5f8
    // 0x87a5f0: r3 = Null
    //     0x87a5f0: mov             x3, NULL
    // 0x87a5f4: b               #0x87a604
    // 0x87a5f8: LoadField: r2 = r0->field_2b
    //     0x87a5f8: ldur            w2, [x0, #0x2b]
    // 0x87a5fc: DecompressPointer r2
    //     0x87a5fc: add             x2, x2, HEAP, lsl #32
    // 0x87a600: mov             x3, x2
    // 0x87a604: ldur            x2, [fp, #-0x10]
    // 0x87a608: cmp             w2, NULL
    // 0x87a60c: b.ne            #0x87a618
    // 0x87a610: r4 = Null
    //     0x87a610: mov             x4, NULL
    // 0x87a614: b               #0x87a620
    // 0x87a618: LoadField: r4 = r2->field_2b
    //     0x87a618: ldur            w4, [x2, #0x2b]
    // 0x87a61c: DecompressPointer r4
    //     0x87a61c: add             x4, x4, HEAP, lsl #32
    // 0x87a620: ldur            d0, [fp, #-0xc0]
    // 0x87a624: r16 = <Size?>
    //     0x87a624: add             x16, PP, #0x44, lsl #12  ; [pp+0x44560] TypeArguments: <Size?>
    //     0x87a628: ldr             x16, [x16, #0x560]
    // 0x87a62c: stp             x3, x16, [SP, #0x18]
    // 0x87a630: str             x4, [SP, #0x10]
    // 0x87a634: str             d0, [SP, #8]
    // 0x87a638: r16 = Closure: (Size?, Size?, double) => Size? from Function 'lerp': static.
    //     0x87a638: add             x16, PP, #0x58, lsl #12  ; [pp+0x58af0] Closure: (Size?, Size?, double) => Size? from Function 'lerp': static. (0x7e54fb2743f8)
    //     0x87a63c: ldr             x16, [x16, #0xaf0]
    // 0x87a640: str             x16, [SP]
    // 0x87a644: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a644: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a648: r0 = lerp()
    //     0x87a648: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a64c: mov             x1, x0
    // 0x87a650: ldur            x0, [fp, #-8]
    // 0x87a654: stur            x1, [fp, #-0x60]
    // 0x87a658: cmp             w0, NULL
    // 0x87a65c: b.ne            #0x87a668
    // 0x87a660: r3 = Null
    //     0x87a660: mov             x3, NULL
    // 0x87a664: b               #0x87a674
    // 0x87a668: LoadField: r2 = r0->field_2f
    //     0x87a668: ldur            w2, [x0, #0x2f]
    // 0x87a66c: DecompressPointer r2
    //     0x87a66c: add             x2, x2, HEAP, lsl #32
    // 0x87a670: mov             x3, x2
    // 0x87a674: ldur            x2, [fp, #-0x10]
    // 0x87a678: cmp             w2, NULL
    // 0x87a67c: b.ne            #0x87a688
    // 0x87a680: r4 = Null
    //     0x87a680: mov             x4, NULL
    // 0x87a684: b               #0x87a690
    // 0x87a688: LoadField: r4 = r2->field_2f
    //     0x87a688: ldur            w4, [x2, #0x2f]
    // 0x87a68c: DecompressPointer r4
    //     0x87a68c: add             x4, x4, HEAP, lsl #32
    // 0x87a690: ldur            d0, [fp, #-0xc0]
    // 0x87a694: r16 = <Size?>
    //     0x87a694: add             x16, PP, #0x44, lsl #12  ; [pp+0x44560] TypeArguments: <Size?>
    //     0x87a698: ldr             x16, [x16, #0x560]
    // 0x87a69c: stp             x3, x16, [SP, #0x18]
    // 0x87a6a0: str             x4, [SP, #0x10]
    // 0x87a6a4: str             d0, [SP, #8]
    // 0x87a6a8: r16 = Closure: (Size?, Size?, double) => Size? from Function 'lerp': static.
    //     0x87a6a8: add             x16, PP, #0x58, lsl #12  ; [pp+0x58af0] Closure: (Size?, Size?, double) => Size? from Function 'lerp': static. (0x7e54fb2743f8)
    //     0x87a6ac: ldr             x16, [x16, #0xaf0]
    // 0x87a6b0: str             x16, [SP]
    // 0x87a6b4: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a6b4: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a6b8: r0 = lerp()
    //     0x87a6b8: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a6bc: mov             x1, x0
    // 0x87a6c0: ldur            x0, [fp, #-8]
    // 0x87a6c4: stur            x1, [fp, #-0x68]
    // 0x87a6c8: cmp             w0, NULL
    // 0x87a6cc: b.ne            #0x87a6d8
    // 0x87a6d0: r3 = Null
    //     0x87a6d0: mov             x3, NULL
    // 0x87a6d4: b               #0x87a6e4
    // 0x87a6d8: LoadField: r2 = r0->field_33
    //     0x87a6d8: ldur            w2, [x0, #0x33]
    // 0x87a6dc: DecompressPointer r2
    //     0x87a6dc: add             x2, x2, HEAP, lsl #32
    // 0x87a6e0: mov             x3, x2
    // 0x87a6e4: ldur            x2, [fp, #-0x10]
    // 0x87a6e8: cmp             w2, NULL
    // 0x87a6ec: b.ne            #0x87a6f8
    // 0x87a6f0: r4 = Null
    //     0x87a6f0: mov             x4, NULL
    // 0x87a6f4: b               #0x87a700
    // 0x87a6f8: LoadField: r4 = r2->field_33
    //     0x87a6f8: ldur            w4, [x2, #0x33]
    // 0x87a6fc: DecompressPointer r4
    //     0x87a6fc: add             x4, x4, HEAP, lsl #32
    // 0x87a700: ldur            d0, [fp, #-0xc0]
    // 0x87a704: r16 = <Color?>
    //     0x87a704: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x87a708: ldr             x16, [x16, #0x98]
    // 0x87a70c: stp             x3, x16, [SP, #0x18]
    // 0x87a710: str             x4, [SP, #0x10]
    // 0x87a714: str             d0, [SP, #8]
    // 0x87a718: r16 = Closure: (Color?, Color?, double) => Color? from Function 'lerp': static.
    //     0x87a718: add             x16, PP, #0x58, lsl #12  ; [pp+0x58ad8] Closure: (Color?, Color?, double) => Color? from Function 'lerp': static. (0x7e54fb1f45d4)
    //     0x87a71c: ldr             x16, [x16, #0xad8]
    // 0x87a720: str             x16, [SP]
    // 0x87a724: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a724: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a728: r0 = lerp()
    //     0x87a728: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a72c: mov             x1, x0
    // 0x87a730: ldur            x0, [fp, #-8]
    // 0x87a734: stur            x1, [fp, #-0x70]
    // 0x87a738: cmp             w0, NULL
    // 0x87a73c: b.ne            #0x87a748
    // 0x87a740: r3 = Null
    //     0x87a740: mov             x3, NULL
    // 0x87a744: b               #0x87a754
    // 0x87a748: LoadField: r2 = r0->field_37
    //     0x87a748: ldur            w2, [x0, #0x37]
    // 0x87a74c: DecompressPointer r2
    //     0x87a74c: add             x2, x2, HEAP, lsl #32
    // 0x87a750: mov             x3, x2
    // 0x87a754: ldur            x2, [fp, #-0x10]
    // 0x87a758: cmp             w2, NULL
    // 0x87a75c: b.ne            #0x87a768
    // 0x87a760: r4 = Null
    //     0x87a760: mov             x4, NULL
    // 0x87a764: b               #0x87a770
    // 0x87a768: LoadField: r4 = r2->field_37
    //     0x87a768: ldur            w4, [x2, #0x37]
    // 0x87a76c: DecompressPointer r4
    //     0x87a76c: add             x4, x4, HEAP, lsl #32
    // 0x87a770: ldur            d0, [fp, #-0xc0]
    // 0x87a774: r16 = <double?>
    //     0x87a774: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b1c0] TypeArguments: <double?>
    //     0x87a778: ldr             x16, [x16, #0x1c0]
    // 0x87a77c: stp             x3, x16, [SP, #0x18]
    // 0x87a780: str             x4, [SP, #0x10]
    // 0x87a784: str             d0, [SP, #8]
    // 0x87a788: r16 = Closure: (num?, num?, double) => double? from Function 'lerpDouble': static.
    //     0x87a788: add             x16, PP, #0x58, lsl #12  ; [pp+0x58ae0] Closure: (num?, num?, double) => double? from Function 'lerpDouble': static. (0x7e54fb0aa638)
    //     0x87a78c: ldr             x16, [x16, #0xae0]
    // 0x87a790: str             x16, [SP]
    // 0x87a794: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a794: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a798: r0 = lerp()
    //     0x87a798: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a79c: mov             x3, x0
    // 0x87a7a0: ldur            x0, [fp, #-8]
    // 0x87a7a4: stur            x3, [fp, #-0x78]
    // 0x87a7a8: cmp             w0, NULL
    // 0x87a7ac: b.ne            #0x87a7b8
    // 0x87a7b0: r1 = Null
    //     0x87a7b0: mov             x1, NULL
    // 0x87a7b4: b               #0x87a7c0
    // 0x87a7b8: LoadField: r1 = r0->field_3b
    //     0x87a7b8: ldur            w1, [x0, #0x3b]
    // 0x87a7bc: DecompressPointer r1
    //     0x87a7bc: add             x1, x1, HEAP, lsl #32
    // 0x87a7c0: ldur            x4, [fp, #-0x10]
    // 0x87a7c4: cmp             w4, NULL
    // 0x87a7c8: b.ne            #0x87a7d4
    // 0x87a7cc: r2 = Null
    //     0x87a7cc: mov             x2, NULL
    // 0x87a7d0: b               #0x87a7dc
    // 0x87a7d4: LoadField: r2 = r4->field_3b
    //     0x87a7d4: ldur            w2, [x4, #0x3b]
    // 0x87a7d8: DecompressPointer r2
    //     0x87a7d8: add             x2, x2, HEAP, lsl #32
    // 0x87a7dc: ldur            d0, [fp, #-0xc0]
    // 0x87a7e0: r0 = _lerpSides()
    //     0x87a7e0: bl              #0x87b874  ; [package:flutter/src/material/button_style.dart] ButtonStyle::_lerpSides
    // 0x87a7e4: mov             x1, x0
    // 0x87a7e8: ldur            x0, [fp, #-8]
    // 0x87a7ec: stur            x1, [fp, #-0x80]
    // 0x87a7f0: cmp             w0, NULL
    // 0x87a7f4: b.ne            #0x87a800
    // 0x87a7f8: r3 = Null
    //     0x87a7f8: mov             x3, NULL
    // 0x87a7fc: b               #0x87a80c
    // 0x87a800: LoadField: r2 = r0->field_3f
    //     0x87a800: ldur            w2, [x0, #0x3f]
    // 0x87a804: DecompressPointer r2
    //     0x87a804: add             x2, x2, HEAP, lsl #32
    // 0x87a808: mov             x3, x2
    // 0x87a80c: ldur            x2, [fp, #-0x10]
    // 0x87a810: cmp             w2, NULL
    // 0x87a814: b.ne            #0x87a820
    // 0x87a818: r4 = Null
    //     0x87a818: mov             x4, NULL
    // 0x87a81c: b               #0x87a828
    // 0x87a820: LoadField: r4 = r2->field_3f
    //     0x87a820: ldur            w4, [x2, #0x3f]
    // 0x87a824: DecompressPointer r4
    //     0x87a824: add             x4, x4, HEAP, lsl #32
    // 0x87a828: ldur            d0, [fp, #-0xc0]
    // 0x87a82c: r16 = <OutlinedBorder?>
    //     0x87a82c: add             x16, PP, #0x44, lsl #12  ; [pp+0x445a0] TypeArguments: <OutlinedBorder?>
    //     0x87a830: ldr             x16, [x16, #0x5a0]
    // 0x87a834: stp             x3, x16, [SP, #0x18]
    // 0x87a838: str             x4, [SP, #0x10]
    // 0x87a83c: str             d0, [SP, #8]
    // 0x87a840: r16 = Closure: (OutlinedBorder?, OutlinedBorder?, double) => OutlinedBorder? from Function 'lerp': static.
    //     0x87a840: add             x16, PP, #0x58, lsl #12  ; [pp+0x58af8] Closure: (OutlinedBorder?, OutlinedBorder?, double) => OutlinedBorder? from Function 'lerp': static. (0x7e54fb27b9c0)
    //     0x87a844: ldr             x16, [x16, #0xaf8]
    // 0x87a848: str             x16, [SP]
    // 0x87a84c: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x87a84c: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x87a850: r0 = lerp()
    //     0x87a850: bl              #0x87b92c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::lerp
    // 0x87a854: ldur            d1, [fp, #-0xc0]
    // 0x87a858: d2 = 0.500000
    //     0x87a858: fmov            d2, #0.50000000
    // 0x87a85c: stur            x0, [fp, #-0xb0]
    // 0x87a860: fcmp            d2, d1
    // 0x87a864: b.le            #0x87a890
    // 0x87a868: ldur            x3, [fp, #-8]
    // 0x87a86c: cmp             w3, NULL
    // 0x87a870: b.ne            #0x87a87c
    // 0x87a874: r1 = Null
    //     0x87a874: mov             x1, NULL
    // 0x87a878: b               #0x87a884
    // 0x87a87c: LoadField: r1 = r3->field_43
    //     0x87a87c: ldur            w1, [x3, #0x43]
    // 0x87a880: DecompressPointer r1
    //     0x87a880: add             x1, x1, HEAP, lsl #32
    // 0x87a884: mov             x5, x1
    // 0x87a888: ldur            x4, [fp, #-0x10]
    // 0x87a88c: b               #0x87a8b4
    // 0x87a890: ldur            x3, [fp, #-8]
    // 0x87a894: ldur            x4, [fp, #-0x10]
    // 0x87a898: cmp             w4, NULL
    // 0x87a89c: b.ne            #0x87a8a8
    // 0x87a8a0: r1 = Null
    //     0x87a8a0: mov             x1, NULL
    // 0x87a8a4: b               #0x87a8b0
    // 0x87a8a8: LoadField: r1 = r4->field_43
    //     0x87a8a8: ldur            w1, [x4, #0x43]
    // 0x87a8ac: DecompressPointer r1
    //     0x87a8ac: add             x1, x1, HEAP, lsl #32
    // 0x87a8b0: mov             x5, x1
    // 0x87a8b4: stur            x5, [fp, #-0xa8]
    // 0x87a8b8: fcmp            d2, d1
    // 0x87a8bc: b.le            #0x87a8e0
    // 0x87a8c0: cmp             w3, NULL
    // 0x87a8c4: b.ne            #0x87a8d0
    // 0x87a8c8: r1 = Null
    //     0x87a8c8: mov             x1, NULL
    // 0x87a8cc: b               #0x87a8d8
    // 0x87a8d0: LoadField: r1 = r3->field_47
    //     0x87a8d0: ldur            w1, [x3, #0x47]
    // 0x87a8d4: DecompressPointer r1
    //     0x87a8d4: add             x1, x1, HEAP, lsl #32
    // 0x87a8d8: mov             x6, x1
    // 0x87a8dc: b               #0x87a8fc
    // 0x87a8e0: cmp             w4, NULL
    // 0x87a8e4: b.ne            #0x87a8f0
    // 0x87a8e8: r1 = Null
    //     0x87a8e8: mov             x1, NULL
    // 0x87a8ec: b               #0x87a8f8
    // 0x87a8f0: LoadField: r1 = r4->field_47
    //     0x87a8f0: ldur            w1, [x4, #0x47]
    // 0x87a8f4: DecompressPointer r1
    //     0x87a8f4: add             x1, x1, HEAP, lsl #32
    // 0x87a8f8: mov             x6, x1
    // 0x87a8fc: stur            x6, [fp, #-0xa0]
    // 0x87a900: fcmp            d2, d1
    // 0x87a904: b.le            #0x87a928
    // 0x87a908: cmp             w3, NULL
    // 0x87a90c: b.ne            #0x87a918
    // 0x87a910: r1 = Null
    //     0x87a910: mov             x1, NULL
    // 0x87a914: b               #0x87a920
    // 0x87a918: LoadField: r1 = r3->field_4b
    //     0x87a918: ldur            w1, [x3, #0x4b]
    // 0x87a91c: DecompressPointer r1
    //     0x87a91c: add             x1, x1, HEAP, lsl #32
    // 0x87a920: mov             x7, x1
    // 0x87a924: b               #0x87a944
    // 0x87a928: cmp             w4, NULL
    // 0x87a92c: b.ne            #0x87a938
    // 0x87a930: r1 = Null
    //     0x87a930: mov             x1, NULL
    // 0x87a934: b               #0x87a940
    // 0x87a938: LoadField: r1 = r4->field_4b
    //     0x87a938: ldur            w1, [x4, #0x4b]
    // 0x87a93c: DecompressPointer r1
    //     0x87a93c: add             x1, x1, HEAP, lsl #32
    // 0x87a940: mov             x7, x1
    // 0x87a944: stur            x7, [fp, #-0x98]
    // 0x87a948: fcmp            d2, d1
    // 0x87a94c: b.le            #0x87a970
    // 0x87a950: cmp             w3, NULL
    // 0x87a954: b.ne            #0x87a960
    // 0x87a958: r1 = Null
    //     0x87a958: mov             x1, NULL
    // 0x87a95c: b               #0x87a968
    // 0x87a960: LoadField: r1 = r3->field_4f
    //     0x87a960: ldur            w1, [x3, #0x4f]
    // 0x87a964: DecompressPointer r1
    //     0x87a964: add             x1, x1, HEAP, lsl #32
    // 0x87a968: mov             x8, x1
    // 0x87a96c: b               #0x87a98c
    // 0x87a970: cmp             w4, NULL
    // 0x87a974: b.ne            #0x87a980
    // 0x87a978: r1 = Null
    //     0x87a978: mov             x1, NULL
    // 0x87a97c: b               #0x87a988
    // 0x87a980: LoadField: r1 = r4->field_4f
    //     0x87a980: ldur            w1, [x4, #0x4f]
    // 0x87a984: DecompressPointer r1
    //     0x87a984: add             x1, x1, HEAP, lsl #32
    // 0x87a988: mov             x8, x1
    // 0x87a98c: stur            x8, [fp, #-0x90]
    // 0x87a990: fcmp            d2, d1
    // 0x87a994: b.le            #0x87a9b8
    // 0x87a998: cmp             w3, NULL
    // 0x87a99c: b.ne            #0x87a9a8
    // 0x87a9a0: r1 = Null
    //     0x87a9a0: mov             x1, NULL
    // 0x87a9a4: b               #0x87a9b0
    // 0x87a9a8: LoadField: r1 = r3->field_53
    //     0x87a9a8: ldur            w1, [x3, #0x53]
    // 0x87a9ac: DecompressPointer r1
    //     0x87a9ac: add             x1, x1, HEAP, lsl #32
    // 0x87a9b0: mov             x9, x1
    // 0x87a9b4: b               #0x87a9d4
    // 0x87a9b8: cmp             w4, NULL
    // 0x87a9bc: b.ne            #0x87a9c8
    // 0x87a9c0: r1 = Null
    //     0x87a9c0: mov             x1, NULL
    // 0x87a9c4: b               #0x87a9d0
    // 0x87a9c8: LoadField: r1 = r4->field_53
    //     0x87a9c8: ldur            w1, [x4, #0x53]
    // 0x87a9cc: DecompressPointer r1
    //     0x87a9cc: add             x1, x1, HEAP, lsl #32
    // 0x87a9d0: mov             x9, x1
    // 0x87a9d4: stur            x9, [fp, #-0x88]
    // 0x87a9d8: cmp             w3, NULL
    // 0x87a9dc: b.ne            #0x87a9e8
    // 0x87a9e0: r1 = Null
    //     0x87a9e0: mov             x1, NULL
    // 0x87a9e4: b               #0x87a9f0
    // 0x87a9e8: LoadField: r1 = r3->field_57
    //     0x87a9e8: ldur            w1, [x3, #0x57]
    // 0x87a9ec: DecompressPointer r1
    //     0x87a9ec: add             x1, x1, HEAP, lsl #32
    // 0x87a9f0: cmp             w4, NULL
    // 0x87a9f4: b.ne            #0x87aa00
    // 0x87a9f8: r2 = Null
    //     0x87a9f8: mov             x2, NULL
    // 0x87a9fc: b               #0x87aa08
    // 0x87aa00: LoadField: r2 = r4->field_57
    //     0x87aa00: ldur            w2, [x4, #0x57]
    // 0x87aa04: DecompressPointer r2
    //     0x87aa04: add             x2, x2, HEAP, lsl #32
    // 0x87aa08: mov             v0.16b, v1.16b
    // 0x87aa0c: r0 = lerp()
    //     0x87aa0c: bl              #0x87ab90  ; [package:flutter/src/painting/alignment.dart] AlignmentGeometry::lerp
    // 0x87aa10: ldur            d0, [fp, #-0xc0]
    // 0x87aa14: d1 = 0.500000
    //     0x87aa14: fmov            d1, #0.50000000
    // 0x87aa18: stur            x0, [fp, #-0xb8]
    // 0x87aa1c: fcmp            d1, d0
    // 0x87aa20: b.le            #0x87aa4c
    // 0x87aa24: ldur            x1, [fp, #-8]
    // 0x87aa28: cmp             w1, NULL
    // 0x87aa2c: b.ne            #0x87aa38
    // 0x87aa30: r1 = Null
    //     0x87aa30: mov             x1, NULL
    // 0x87aa34: b               #0x87aa44
    // 0x87aa38: LoadField: r2 = r1->field_5b
    //     0x87aa38: ldur            w2, [x1, #0x5b]
    // 0x87aa3c: DecompressPointer r2
    //     0x87aa3c: add             x2, x2, HEAP, lsl #32
    // 0x87aa40: mov             x1, x2
    // 0x87aa44: stur            x1, [fp, #-8]
    // 0x87aa48: b               #0x87aa70
    // 0x87aa4c: ldur            x1, [fp, #-0x10]
    // 0x87aa50: cmp             w1, NULL
    // 0x87aa54: b.ne            #0x87aa60
    // 0x87aa58: r1 = Null
    //     0x87aa58: mov             x1, NULL
    // 0x87aa5c: b               #0x87aa6c
    // 0x87aa60: LoadField: r2 = r1->field_5b
    //     0x87aa60: ldur            w2, [x1, #0x5b]
    // 0x87aa64: DecompressPointer r2
    //     0x87aa64: add             x2, x2, HEAP, lsl #32
    // 0x87aa68: mov             x1, x2
    // 0x87aa6c: stur            x1, [fp, #-8]
    // 0x87aa70: ldur            x25, [fp, #-0x20]
    // 0x87aa74: ldur            x24, [fp, #-0x28]
    // 0x87aa78: ldur            x23, [fp, #-0x30]
    // 0x87aa7c: ldur            x20, [fp, #-0x38]
    // 0x87aa80: ldur            x19, [fp, #-0x40]
    // 0x87aa84: ldur            x14, [fp, #-0x48]
    // 0x87aa88: ldur            x13, [fp, #-0x50]
    // 0x87aa8c: ldur            x12, [fp, #-0x58]
    // 0x87aa90: ldur            x11, [fp, #-0x60]
    // 0x87aa94: ldur            x10, [fp, #-0x68]
    // 0x87aa98: ldur            x9, [fp, #-0x70]
    // 0x87aa9c: ldur            x8, [fp, #-0x78]
    // 0x87aaa0: ldur            x7, [fp, #-0x80]
    // 0x87aaa4: ldur            x1, [fp, #-0xb0]
    // 0x87aaa8: ldur            x2, [fp, #-0xa8]
    // 0x87aaac: ldur            x3, [fp, #-0xa0]
    // 0x87aab0: ldur            x4, [fp, #-0x98]
    // 0x87aab4: ldur            x5, [fp, #-0x90]
    // 0x87aab8: ldur            x6, [fp, #-0x88]
    // 0x87aabc: r0 = ButtonStyle()
    //     0x87aabc: bl              #0x87ab84  ; AllocateButtonStyleStub -> ButtonStyle (size=0x68)
    // 0x87aac0: ldur            x1, [fp, #-0x18]
    // 0x87aac4: StoreField: r0->field_7 = r1
    //     0x87aac4: stur            w1, [x0, #7]
    // 0x87aac8: ldur            x1, [fp, #-0x20]
    // 0x87aacc: StoreField: r0->field_b = r1
    //     0x87aacc: stur            w1, [x0, #0xb]
    // 0x87aad0: ldur            x1, [fp, #-0x28]
    // 0x87aad4: StoreField: r0->field_f = r1
    //     0x87aad4: stur            w1, [x0, #0xf]
    // 0x87aad8: ldur            x1, [fp, #-0x30]
    // 0x87aadc: StoreField: r0->field_13 = r1
    //     0x87aadc: stur            w1, [x0, #0x13]
    // 0x87aae0: ldur            x1, [fp, #-0x38]
    // 0x87aae4: ArrayStore: r0[0] = r1  ; List_4
    //     0x87aae4: stur            w1, [x0, #0x17]
    // 0x87aae8: ldur            x1, [fp, #-0x40]
    // 0x87aaec: StoreField: r0->field_1b = r1
    //     0x87aaec: stur            w1, [x0, #0x1b]
    // 0x87aaf0: ldur            x1, [fp, #-0x48]
    // 0x87aaf4: StoreField: r0->field_1f = r1
    //     0x87aaf4: stur            w1, [x0, #0x1f]
    // 0x87aaf8: ldur            x1, [fp, #-0x50]
    // 0x87aafc: StoreField: r0->field_23 = r1
    //     0x87aafc: stur            w1, [x0, #0x23]
    // 0x87ab00: ldur            x1, [fp, #-0x58]
    // 0x87ab04: StoreField: r0->field_27 = r1
    //     0x87ab04: stur            w1, [x0, #0x27]
    // 0x87ab08: ldur            x1, [fp, #-0x60]
    // 0x87ab0c: StoreField: r0->field_2b = r1
    //     0x87ab0c: stur            w1, [x0, #0x2b]
    // 0x87ab10: ldur            x1, [fp, #-0x68]
    // 0x87ab14: StoreField: r0->field_2f = r1
    //     0x87ab14: stur            w1, [x0, #0x2f]
    // 0x87ab18: ldur            x1, [fp, #-0x70]
    // 0x87ab1c: StoreField: r0->field_33 = r1
    //     0x87ab1c: stur            w1, [x0, #0x33]
    // 0x87ab20: ldur            x1, [fp, #-0x78]
    // 0x87ab24: StoreField: r0->field_37 = r1
    //     0x87ab24: stur            w1, [x0, #0x37]
    // 0x87ab28: ldur            x1, [fp, #-0x80]
    // 0x87ab2c: StoreField: r0->field_3b = r1
    //     0x87ab2c: stur            w1, [x0, #0x3b]
    // 0x87ab30: ldur            x1, [fp, #-0xb0]
    // 0x87ab34: StoreField: r0->field_3f = r1
    //     0x87ab34: stur            w1, [x0, #0x3f]
    // 0x87ab38: ldur            x1, [fp, #-0xa8]
    // 0x87ab3c: StoreField: r0->field_43 = r1
    //     0x87ab3c: stur            w1, [x0, #0x43]
    // 0x87ab40: ldur            x1, [fp, #-0xa0]
    // 0x87ab44: StoreField: r0->field_47 = r1
    //     0x87ab44: stur            w1, [x0, #0x47]
    // 0x87ab48: ldur            x1, [fp, #-0x98]
    // 0x87ab4c: StoreField: r0->field_4b = r1
    //     0x87ab4c: stur            w1, [x0, #0x4b]
    // 0x87ab50: ldur            x1, [fp, #-0x90]
    // 0x87ab54: StoreField: r0->field_4f = r1
    //     0x87ab54: stur            w1, [x0, #0x4f]
    // 0x87ab58: ldur            x1, [fp, #-0x88]
    // 0x87ab5c: StoreField: r0->field_53 = r1
    //     0x87ab5c: stur            w1, [x0, #0x53]
    // 0x87ab60: ldur            x1, [fp, #-0xb8]
    // 0x87ab64: StoreField: r0->field_57 = r1
    //     0x87ab64: stur            w1, [x0, #0x57]
    // 0x87ab68: ldur            x1, [fp, #-8]
    // 0x87ab6c: StoreField: r0->field_5b = r1
    //     0x87ab6c: stur            w1, [x0, #0x5b]
    // 0x87ab70: LeaveFrame
    //     0x87ab70: mov             SP, fp
    //     0x87ab74: ldp             fp, lr, [SP], #0x10
    // 0x87ab78: ret
    //     0x87ab78: ret             
    // 0x87ab7c: r0 = StackOverflowSharedWithFPURegs()
    //     0x87ab7c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x87ab80: b               #0x87a1f0
  }
  static _ _lerpSides(/* No info */) {
    // ** addr: 0x87b874, size: 0x4c
    // 0x87b874: EnterFrame
    //     0x87b874: stp             fp, lr, [SP, #-0x10]!
    //     0x87b878: mov             fp, SP
    // 0x87b87c: CheckStackOverflow
    //     0x87b87c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x87b880: cmp             SP, x16
    //     0x87b884: b.ls            #0x87b8b8
    // 0x87b888: cmp             w1, NULL
    // 0x87b88c: b.ne            #0x87b8a8
    // 0x87b890: cmp             w2, NULL
    // 0x87b894: b.ne            #0x87b8a8
    // 0x87b898: r0 = Null
    //     0x87b898: mov             x0, NULL
    // 0x87b89c: LeaveFrame
    //     0x87b89c: mov             SP, fp
    //     0x87b8a0: ldp             fp, lr, [SP], #0x10
    // 0x87b8a4: ret
    //     0x87b8a4: ret             
    // 0x87b8a8: r0 = lerp()
    //     0x87b8a8: bl              #0x87b8c0  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateBorderSide::lerp
    // 0x87b8ac: LeaveFrame
    //     0x87b8ac: mov             SP, fp
    //     0x87b8b0: ldp             fp, lr, [SP], #0x10
    // 0x87b8b4: ret
    //     0x87b8b4: ret             
    // 0x87b8b8: r0 = StackOverflowSharedWithFPURegs()
    //     0x87b8b8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x87b8bc: b               #0x87b888
  }
  _ copyWith(/* No info */) {
    // ** addr: 0x9e7350, size: 0xd88
    // 0x9e7350: EnterFrame
    //     0x9e7350: stp             fp, lr, [SP, #-0x10]!
    //     0x9e7354: mov             fp, SP
    // 0x9e7358: AllocStack(0xc8)
    //     0x9e7358: sub             SP, SP, #0xc8
    // 0x9e735c: SetupParameters(ButtonStyle this /* r1 => r2, fp-0xb0 */, {dynamic alignment = Null /* fp-0x8 */, dynamic animationDuration = Null /* r5, fp-0xa8 */, dynamic backgroundColor = Null /* r6, fp-0xa0 */, dynamic elevation = Null /* r7, fp-0x98 */, dynamic enableFeedback = Null /* r8, fp-0x90 */, dynamic fixedSize = Null /* r9, fp-0x88 */, dynamic foregroundColor = Null /* r10, fp-0x80 */, dynamic iconColor = Null /* r11, fp-0x78 */, dynamic iconSize = Null /* r12, fp-0x70 */, dynamic maximumSize = Null /* r13, fp-0x68 */, dynamic minimumSize = Null /* r14, fp-0x60 */, dynamic mouseCursor = Null /* r19, fp-0x58 */, dynamic overlayColor = Null /* r20, fp-0x50 */, dynamic padding = Null /* fp-0x10 */, dynamic shadowColor = Null /* fp-0x18 */, dynamic shape = Null /* fp-0x20 */, dynamic side = Null /* fp-0x28 */, dynamic splashFactory = Null /* fp-0x30 */, dynamic surfaceTintColor = Null /* fp-0x38 */, dynamic tapTargetSize = Null /* fp-0x40 */, dynamic textStyle = Null /* r3 */, dynamic visualDensity = Null /* r4, fp-0x48 */})
    //     0x9e735c: mov             x2, x1
    //     0x9e7360: stur            x1, [fp, #-0xb0]
    //     0x9e7364: ldur            w0, [x4, #0x13]
    //     0x9e7368: ldur            w1, [x4, #0x1f]
    //     0x9e736c: add             x1, x1, HEAP, lsl #32
    //     0x9e7370: ldr             x16, [PP, #0x74e0]  ; [pp+0x74e0] "alignment"
    //     0x9e7374: cmp             w1, w16
    //     0x9e7378: b.ne            #0x9e739c
    //     0x9e737c: ldur            w1, [x4, #0x23]
    //     0x9e7380: add             x1, x1, HEAP, lsl #32
    //     0x9e7384: sub             w3, w0, w1
    //     0x9e7388: add             x1, fp, w3, sxtw #2
    //     0x9e738c: ldr             x1, [x1, #8]
    //     0x9e7390: mov             x3, x1
    //     0x9e7394: movz            x1, #0x1
    //     0x9e7398: b               #0x9e73a4
    //     0x9e739c: mov             x3, NULL
    //     0x9e73a0: movz            x1, #0
    //     0x9e73a4: stur            x3, [fp, #-8]
    //     0x9e73a8: lsl             x5, x1, #1
    //     0x9e73ac: lsl             w6, w5, #1
    //     0x9e73b0: add             w7, w6, #8
    //     0x9e73b4: add             x16, x4, w7, sxtw #1
    //     0x9e73b8: ldur            w8, [x16, #0xf]
    //     0x9e73bc: add             x8, x8, HEAP, lsl #32
    //     0x9e73c0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d38] "animationDuration"
    //     0x9e73c4: ldr             x16, [x16, #0xd38]
    //     0x9e73c8: cmp             w8, w16
    //     0x9e73cc: b.ne            #0x9e7400
    //     0x9e73d0: add             w1, w6, #0xa
    //     0x9e73d4: add             x16, x4, w1, sxtw #1
    //     0x9e73d8: ldur            w6, [x16, #0xf]
    //     0x9e73dc: add             x6, x6, HEAP, lsl #32
    //     0x9e73e0: sub             w1, w0, w6
    //     0x9e73e4: add             x6, fp, w1, sxtw #2
    //     0x9e73e8: ldr             x6, [x6, #8]
    //     0x9e73ec: add             w1, w5, #2
    //     0x9e73f0: sbfx            x5, x1, #1, #0x1f
    //     0x9e73f4: mov             x1, x5
    //     0x9e73f8: mov             x5, x6
    //     0x9e73fc: b               #0x9e7404
    //     0x9e7400: mov             x5, NULL
    //     0x9e7404: stur            x5, [fp, #-0xa8]
    //     0x9e7408: lsl             x6, x1, #1
    //     0x9e740c: lsl             w7, w6, #1
    //     0x9e7410: add             w8, w7, #8
    //     0x9e7414: add             x16, x4, w8, sxtw #1
    //     0x9e7418: ldur            w9, [x16, #0xf]
    //     0x9e741c: add             x9, x9, HEAP, lsl #32
    //     0x9e7420: ldr             x16, [PP, #0x5148]  ; [pp+0x5148] "backgroundColor"
    //     0x9e7424: cmp             w9, w16
    //     0x9e7428: b.ne            #0x9e745c
    //     0x9e742c: add             w1, w7, #0xa
    //     0x9e7430: add             x16, x4, w1, sxtw #1
    //     0x9e7434: ldur            w7, [x16, #0xf]
    //     0x9e7438: add             x7, x7, HEAP, lsl #32
    //     0x9e743c: sub             w1, w0, w7
    //     0x9e7440: add             x7, fp, w1, sxtw #2
    //     0x9e7444: ldr             x7, [x7, #8]
    //     0x9e7448: add             w1, w6, #2
    //     0x9e744c: sbfx            x6, x1, #1, #0x1f
    //     0x9e7450: mov             x1, x6
    //     0x9e7454: mov             x6, x7
    //     0x9e7458: b               #0x9e7460
    //     0x9e745c: mov             x6, NULL
    //     0x9e7460: stur            x6, [fp, #-0xa0]
    //     0x9e7464: lsl             x7, x1, #1
    //     0x9e7468: lsl             w8, w7, #1
    //     0x9e746c: add             w9, w8, #8
    //     0x9e7470: add             x16, x4, w9, sxtw #1
    //     0x9e7474: ldur            w10, [x16, #0xf]
    //     0x9e7478: add             x10, x10, HEAP, lsl #32
    //     0x9e747c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d58] "elevation"
    //     0x9e7480: ldr             x16, [x16, #0xd58]
    //     0x9e7484: cmp             w10, w16
    //     0x9e7488: b.ne            #0x9e74bc
    //     0x9e748c: add             w1, w8, #0xa
    //     0x9e7490: add             x16, x4, w1, sxtw #1
    //     0x9e7494: ldur            w8, [x16, #0xf]
    //     0x9e7498: add             x8, x8, HEAP, lsl #32
    //     0x9e749c: sub             w1, w0, w8
    //     0x9e74a0: add             x8, fp, w1, sxtw #2
    //     0x9e74a4: ldr             x8, [x8, #8]
    //     0x9e74a8: add             w1, w7, #2
    //     0x9e74ac: sbfx            x7, x1, #1, #0x1f
    //     0x9e74b0: mov             x1, x7
    //     0x9e74b4: mov             x7, x8
    //     0x9e74b8: b               #0x9e74c0
    //     0x9e74bc: mov             x7, NULL
    //     0x9e74c0: stur            x7, [fp, #-0x98]
    //     0x9e74c4: lsl             x8, x1, #1
    //     0x9e74c8: lsl             w9, w8, #1
    //     0x9e74cc: add             w10, w9, #8
    //     0x9e74d0: add             x16, x4, w10, sxtw #1
    //     0x9e74d4: ldur            w11, [x16, #0xf]
    //     0x9e74d8: add             x11, x11, HEAP, lsl #32
    //     0x9e74dc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d60] "enableFeedback"
    //     0x9e74e0: ldr             x16, [x16, #0xd60]
    //     0x9e74e4: cmp             w11, w16
    //     0x9e74e8: b.ne            #0x9e751c
    //     0x9e74ec: add             w1, w9, #0xa
    //     0x9e74f0: add             x16, x4, w1, sxtw #1
    //     0x9e74f4: ldur            w9, [x16, #0xf]
    //     0x9e74f8: add             x9, x9, HEAP, lsl #32
    //     0x9e74fc: sub             w1, w0, w9
    //     0x9e7500: add             x9, fp, w1, sxtw #2
    //     0x9e7504: ldr             x9, [x9, #8]
    //     0x9e7508: add             w1, w8, #2
    //     0x9e750c: sbfx            x8, x1, #1, #0x1f
    //     0x9e7510: mov             x1, x8
    //     0x9e7514: mov             x8, x9
    //     0x9e7518: b               #0x9e7520
    //     0x9e751c: mov             x8, NULL
    //     0x9e7520: stur            x8, [fp, #-0x90]
    //     0x9e7524: lsl             x9, x1, #1
    //     0x9e7528: lsl             w10, w9, #1
    //     0x9e752c: add             w11, w10, #8
    //     0x9e7530: add             x16, x4, w11, sxtw #1
    //     0x9e7534: ldur            w12, [x16, #0xf]
    //     0x9e7538: add             x12, x12, HEAP, lsl #32
    //     0x9e753c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e10] "fixedSize"
    //     0x9e7540: ldr             x16, [x16, #0xe10]
    //     0x9e7544: cmp             w12, w16
    //     0x9e7548: b.ne            #0x9e757c
    //     0x9e754c: add             w1, w10, #0xa
    //     0x9e7550: add             x16, x4, w1, sxtw #1
    //     0x9e7554: ldur            w10, [x16, #0xf]
    //     0x9e7558: add             x10, x10, HEAP, lsl #32
    //     0x9e755c: sub             w1, w0, w10
    //     0x9e7560: add             x10, fp, w1, sxtw #2
    //     0x9e7564: ldr             x10, [x10, #8]
    //     0x9e7568: add             w1, w9, #2
    //     0x9e756c: sbfx            x9, x1, #1, #0x1f
    //     0x9e7570: mov             x1, x9
    //     0x9e7574: mov             x9, x10
    //     0x9e7578: b               #0x9e7580
    //     0x9e757c: mov             x9, NULL
    //     0x9e7580: stur            x9, [fp, #-0x88]
    //     0x9e7584: lsl             x10, x1, #1
    //     0x9e7588: lsl             w11, w10, #1
    //     0x9e758c: add             w12, w11, #8
    //     0x9e7590: add             x16, x4, w12, sxtw #1
    //     0x9e7594: ldur            w13, [x16, #0xf]
    //     0x9e7598: add             x13, x13, HEAP, lsl #32
    //     0x9e759c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d70] "foregroundColor"
    //     0x9e75a0: ldr             x16, [x16, #0xd70]
    //     0x9e75a4: cmp             w13, w16
    //     0x9e75a8: b.ne            #0x9e75dc
    //     0x9e75ac: add             w1, w11, #0xa
    //     0x9e75b0: add             x16, x4, w1, sxtw #1
    //     0x9e75b4: ldur            w11, [x16, #0xf]
    //     0x9e75b8: add             x11, x11, HEAP, lsl #32
    //     0x9e75bc: sub             w1, w0, w11
    //     0x9e75c0: add             x11, fp, w1, sxtw #2
    //     0x9e75c4: ldr             x11, [x11, #8]
    //     0x9e75c8: add             w1, w10, #2
    //     0x9e75cc: sbfx            x10, x1, #1, #0x1f
    //     0x9e75d0: mov             x1, x10
    //     0x9e75d4: mov             x10, x11
    //     0x9e75d8: b               #0x9e75e0
    //     0x9e75dc: mov             x10, NULL
    //     0x9e75e0: stur            x10, [fp, #-0x80]
    //     0x9e75e4: lsl             x11, x1, #1
    //     0x9e75e8: lsl             w12, w11, #1
    //     0x9e75ec: add             w13, w12, #8
    //     0x9e75f0: add             x16, x4, w13, sxtw #1
    //     0x9e75f4: ldur            w14, [x16, #0xf]
    //     0x9e75f8: add             x14, x14, HEAP, lsl #32
    //     0x9e75fc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e18] "iconColor"
    //     0x9e7600: ldr             x16, [x16, #0xe18]
    //     0x9e7604: cmp             w14, w16
    //     0x9e7608: b.ne            #0x9e763c
    //     0x9e760c: add             w1, w12, #0xa
    //     0x9e7610: add             x16, x4, w1, sxtw #1
    //     0x9e7614: ldur            w12, [x16, #0xf]
    //     0x9e7618: add             x12, x12, HEAP, lsl #32
    //     0x9e761c: sub             w1, w0, w12
    //     0x9e7620: add             x12, fp, w1, sxtw #2
    //     0x9e7624: ldr             x12, [x12, #8]
    //     0x9e7628: add             w1, w11, #2
    //     0x9e762c: sbfx            x11, x1, #1, #0x1f
    //     0x9e7630: mov             x1, x11
    //     0x9e7634: mov             x11, x12
    //     0x9e7638: b               #0x9e7640
    //     0x9e763c: mov             x11, NULL
    //     0x9e7640: stur            x11, [fp, #-0x78]
    //     0x9e7644: lsl             x12, x1, #1
    //     0x9e7648: lsl             w13, w12, #1
    //     0x9e764c: add             w14, w13, #8
    //     0x9e7650: add             x16, x4, w14, sxtw #1
    //     0x9e7654: ldur            w19, [x16, #0xf]
    //     0x9e7658: add             x19, x19, HEAP, lsl #32
    //     0x9e765c: add             x16, PP, #0x39, lsl #12  ; [pp+0x398d0] "iconSize"
    //     0x9e7660: ldr             x16, [x16, #0x8d0]
    //     0x9e7664: cmp             w19, w16
    //     0x9e7668: b.ne            #0x9e769c
    //     0x9e766c: add             w1, w13, #0xa
    //     0x9e7670: add             x16, x4, w1, sxtw #1
    //     0x9e7674: ldur            w13, [x16, #0xf]
    //     0x9e7678: add             x13, x13, HEAP, lsl #32
    //     0x9e767c: sub             w1, w0, w13
    //     0x9e7680: add             x13, fp, w1, sxtw #2
    //     0x9e7684: ldr             x13, [x13, #8]
    //     0x9e7688: add             w1, w12, #2
    //     0x9e768c: sbfx            x12, x1, #1, #0x1f
    //     0x9e7690: mov             x1, x12
    //     0x9e7694: mov             x12, x13
    //     0x9e7698: b               #0x9e76a0
    //     0x9e769c: mov             x12, NULL
    //     0x9e76a0: stur            x12, [fp, #-0x70]
    //     0x9e76a4: lsl             x13, x1, #1
    //     0x9e76a8: lsl             w14, w13, #1
    //     0x9e76ac: add             w19, w14, #8
    //     0x9e76b0: add             x16, x4, w19, sxtw #1
    //     0x9e76b4: ldur            w20, [x16, #0xf]
    //     0x9e76b8: add             x20, x20, HEAP, lsl #32
    //     0x9e76bc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d78] "maximumSize"
    //     0x9e76c0: ldr             x16, [x16, #0xd78]
    //     0x9e76c4: cmp             w20, w16
    //     0x9e76c8: b.ne            #0x9e76fc
    //     0x9e76cc: add             w1, w14, #0xa
    //     0x9e76d0: add             x16, x4, w1, sxtw #1
    //     0x9e76d4: ldur            w14, [x16, #0xf]
    //     0x9e76d8: add             x14, x14, HEAP, lsl #32
    //     0x9e76dc: sub             w1, w0, w14
    //     0x9e76e0: add             x14, fp, w1, sxtw #2
    //     0x9e76e4: ldr             x14, [x14, #8]
    //     0x9e76e8: add             w1, w13, #2
    //     0x9e76ec: sbfx            x13, x1, #1, #0x1f
    //     0x9e76f0: mov             x1, x13
    //     0x9e76f4: mov             x13, x14
    //     0x9e76f8: b               #0x9e7700
    //     0x9e76fc: mov             x13, NULL
    //     0x9e7700: stur            x13, [fp, #-0x68]
    //     0x9e7704: lsl             x14, x1, #1
    //     0x9e7708: lsl             w19, w14, #1
    //     0x9e770c: add             w20, w19, #8
    //     0x9e7710: add             x16, x4, w20, sxtw #1
    //     0x9e7714: ldur            w23, [x16, #0xf]
    //     0x9e7718: add             x23, x23, HEAP, lsl #32
    //     0x9e771c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d80] "minimumSize"
    //     0x9e7720: ldr             x16, [x16, #0xd80]
    //     0x9e7724: cmp             w23, w16
    //     0x9e7728: b.ne            #0x9e775c
    //     0x9e772c: add             w1, w19, #0xa
    //     0x9e7730: add             x16, x4, w1, sxtw #1
    //     0x9e7734: ldur            w19, [x16, #0xf]
    //     0x9e7738: add             x19, x19, HEAP, lsl #32
    //     0x9e773c: sub             w1, w0, w19
    //     0x9e7740: add             x19, fp, w1, sxtw #2
    //     0x9e7744: ldr             x19, [x19, #8]
    //     0x9e7748: add             w1, w14, #2
    //     0x9e774c: sbfx            x14, x1, #1, #0x1f
    //     0x9e7750: mov             x1, x14
    //     0x9e7754: mov             x14, x19
    //     0x9e7758: b               #0x9e7760
    //     0x9e775c: mov             x14, NULL
    //     0x9e7760: stur            x14, [fp, #-0x60]
    //     0x9e7764: lsl             x19, x1, #1
    //     0x9e7768: lsl             w20, w19, #1
    //     0x9e776c: add             w23, w20, #8
    //     0x9e7770: add             x16, x4, w23, sxtw #1
    //     0x9e7774: ldur            w24, [x16, #0xf]
    //     0x9e7778: add             x24, x24, HEAP, lsl #32
    //     0x9e777c: add             x16, PP, #0x44, lsl #12  ; [pp+0x447e0] "mouseCursor"
    //     0x9e7780: ldr             x16, [x16, #0x7e0]
    //     0x9e7784: cmp             w24, w16
    //     0x9e7788: b.ne            #0x9e77bc
    //     0x9e778c: add             w1, w20, #0xa
    //     0x9e7790: add             x16, x4, w1, sxtw #1
    //     0x9e7794: ldur            w20, [x16, #0xf]
    //     0x9e7798: add             x20, x20, HEAP, lsl #32
    //     0x9e779c: sub             w1, w0, w20
    //     0x9e77a0: add             x20, fp, w1, sxtw #2
    //     0x9e77a4: ldr             x20, [x20, #8]
    //     0x9e77a8: add             w1, w19, #2
    //     0x9e77ac: sbfx            x19, x1, #1, #0x1f
    //     0x9e77b0: mov             x1, x19
    //     0x9e77b4: mov             x19, x20
    //     0x9e77b8: b               #0x9e77c0
    //     0x9e77bc: mov             x19, NULL
    //     0x9e77c0: stur            x19, [fp, #-0x58]
    //     0x9e77c4: lsl             x20, x1, #1
    //     0x9e77c8: lsl             w23, w20, #1
    //     0x9e77cc: add             w24, w23, #8
    //     0x9e77d0: add             x16, x4, w24, sxtw #1
    //     0x9e77d4: ldur            w25, [x16, #0xf]
    //     0x9e77d8: add             x25, x25, HEAP, lsl #32
    //     0x9e77dc: add             x16, PP, #0x44, lsl #12  ; [pp+0x447e8] "overlayColor"
    //     0x9e77e0: ldr             x16, [x16, #0x7e8]
    //     0x9e77e4: cmp             w25, w16
    //     0x9e77e8: b.ne            #0x9e781c
    //     0x9e77ec: add             w1, w23, #0xa
    //     0x9e77f0: add             x16, x4, w1, sxtw #1
    //     0x9e77f4: ldur            w23, [x16, #0xf]
    //     0x9e77f8: add             x23, x23, HEAP, lsl #32
    //     0x9e77fc: sub             w1, w0, w23
    //     0x9e7800: add             x23, fp, w1, sxtw #2
    //     0x9e7804: ldr             x23, [x23, #8]
    //     0x9e7808: add             w1, w20, #2
    //     0x9e780c: sbfx            x20, x1, #1, #0x1f
    //     0x9e7810: mov             x1, x20
    //     0x9e7814: mov             x20, x23
    //     0x9e7818: b               #0x9e7820
    //     0x9e781c: mov             x20, NULL
    //     0x9e7820: stur            x20, [fp, #-0x50]
    //     0x9e7824: lsl             x23, x1, #1
    //     0x9e7828: lsl             w24, w23, #1
    //     0x9e782c: add             w25, w24, #8
    //     0x9e7830: add             x16, x4, w25, sxtw #1
    //     0x9e7834: ldur            w3, [x16, #0xf]
    //     0x9e7838: add             x3, x3, HEAP, lsl #32
    //     0x9e783c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d88] "padding"
    //     0x9e7840: ldr             x16, [x16, #0xd88]
    //     0x9e7844: cmp             w3, w16
    //     0x9e7848: b.ne            #0x9e7878
    //     0x9e784c: add             w1, w24, #0xa
    //     0x9e7850: add             x16, x4, w1, sxtw #1
    //     0x9e7854: ldur            w3, [x16, #0xf]
    //     0x9e7858: add             x3, x3, HEAP, lsl #32
    //     0x9e785c: sub             w1, w0, w3
    //     0x9e7860: add             x3, fp, w1, sxtw #2
    //     0x9e7864: ldr             x3, [x3, #8]
    //     0x9e7868: add             w1, w23, #2
    //     0x9e786c: sbfx            x23, x1, #1, #0x1f
    //     0x9e7870: mov             x1, x23
    //     0x9e7874: b               #0x9e787c
    //     0x9e7878: mov             x3, NULL
    //     0x9e787c: stur            x3, [fp, #-0x10]
    //     0x9e7880: lsl             x23, x1, #1
    //     0x9e7884: lsl             w24, w23, #1
    //     0x9e7888: add             w25, w24, #8
    //     0x9e788c: add             x16, x4, w25, sxtw #1
    //     0x9e7890: ldur            w3, [x16, #0xf]
    //     0x9e7894: add             x3, x3, HEAP, lsl #32
    //     0x9e7898: ldr             x16, [PP, #0x5388]  ; [pp+0x5388] "shadowColor"
    //     0x9e789c: cmp             w3, w16
    //     0x9e78a0: b.ne            #0x9e78d0
    //     0x9e78a4: add             w1, w24, #0xa
    //     0x9e78a8: add             x16, x4, w1, sxtw #1
    //     0x9e78ac: ldur            w3, [x16, #0xf]
    //     0x9e78b0: add             x3, x3, HEAP, lsl #32
    //     0x9e78b4: sub             w1, w0, w3
    //     0x9e78b8: add             x3, fp, w1, sxtw #2
    //     0x9e78bc: ldr             x3, [x3, #8]
    //     0x9e78c0: add             w1, w23, #2
    //     0x9e78c4: sbfx            x23, x1, #1, #0x1f
    //     0x9e78c8: mov             x1, x23
    //     0x9e78cc: b               #0x9e78d4
    //     0x9e78d0: mov             x3, NULL
    //     0x9e78d4: stur            x3, [fp, #-0x18]
    //     0x9e78d8: lsl             x23, x1, #1
    //     0x9e78dc: lsl             w24, w23, #1
    //     0x9e78e0: add             w25, w24, #8
    //     0x9e78e4: add             x16, x4, w25, sxtw #1
    //     0x9e78e8: ldur            w3, [x16, #0xf]
    //     0x9e78ec: add             x3, x3, HEAP, lsl #32
    //     0x9e78f0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d90] "shape"
    //     0x9e78f4: ldr             x16, [x16, #0xd90]
    //     0x9e78f8: cmp             w3, w16
    //     0x9e78fc: b.ne            #0x9e792c
    //     0x9e7900: add             w1, w24, #0xa
    //     0x9e7904: add             x16, x4, w1, sxtw #1
    //     0x9e7908: ldur            w3, [x16, #0xf]
    //     0x9e790c: add             x3, x3, HEAP, lsl #32
    //     0x9e7910: sub             w1, w0, w3
    //     0x9e7914: add             x3, fp, w1, sxtw #2
    //     0x9e7918: ldr             x3, [x3, #8]
    //     0x9e791c: add             w1, w23, #2
    //     0x9e7920: sbfx            x23, x1, #1, #0x1f
    //     0x9e7924: mov             x1, x23
    //     0x9e7928: b               #0x9e7930
    //     0x9e792c: mov             x3, NULL
    //     0x9e7930: stur            x3, [fp, #-0x20]
    //     0x9e7934: lsl             x23, x1, #1
    //     0x9e7938: lsl             w24, w23, #1
    //     0x9e793c: add             w25, w24, #8
    //     0x9e7940: add             x16, x4, w25, sxtw #1
    //     0x9e7944: ldur            w3, [x16, #0xf]
    //     0x9e7948: add             x3, x3, HEAP, lsl #32
    //     0x9e794c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d98] "side"
    //     0x9e7950: ldr             x16, [x16, #0xd98]
    //     0x9e7954: cmp             w3, w16
    //     0x9e7958: b.ne            #0x9e7988
    //     0x9e795c: add             w1, w24, #0xa
    //     0x9e7960: add             x16, x4, w1, sxtw #1
    //     0x9e7964: ldur            w3, [x16, #0xf]
    //     0x9e7968: add             x3, x3, HEAP, lsl #32
    //     0x9e796c: sub             w1, w0, w3
    //     0x9e7970: add             x3, fp, w1, sxtw #2
    //     0x9e7974: ldr             x3, [x3, #8]
    //     0x9e7978: add             w1, w23, #2
    //     0x9e797c: sbfx            x23, x1, #1, #0x1f
    //     0x9e7980: mov             x1, x23
    //     0x9e7984: b               #0x9e798c
    //     0x9e7988: mov             x3, NULL
    //     0x9e798c: stur            x3, [fp, #-0x28]
    //     0x9e7990: lsl             x23, x1, #1
    //     0x9e7994: lsl             w24, w23, #1
    //     0x9e7998: add             w25, w24, #8
    //     0x9e799c: add             x16, x4, w25, sxtw #1
    //     0x9e79a0: ldur            w3, [x16, #0xf]
    //     0x9e79a4: add             x3, x3, HEAP, lsl #32
    //     0x9e79a8: ldr             x16, [PP, #0x53a8]  ; [pp+0x53a8] "splashFactory"
    //     0x9e79ac: cmp             w3, w16
    //     0x9e79b0: b.ne            #0x9e79e0
    //     0x9e79b4: add             w1, w24, #0xa
    //     0x9e79b8: add             x16, x4, w1, sxtw #1
    //     0x9e79bc: ldur            w3, [x16, #0xf]
    //     0x9e79c0: add             x3, x3, HEAP, lsl #32
    //     0x9e79c4: sub             w1, w0, w3
    //     0x9e79c8: add             x3, fp, w1, sxtw #2
    //     0x9e79cc: ldr             x3, [x3, #8]
    //     0x9e79d0: add             w1, w23, #2
    //     0x9e79d4: sbfx            x23, x1, #1, #0x1f
    //     0x9e79d8: mov             x1, x23
    //     0x9e79dc: b               #0x9e79e4
    //     0x9e79e0: mov             x3, NULL
    //     0x9e79e4: stur            x3, [fp, #-0x30]
    //     0x9e79e8: lsl             x23, x1, #1
    //     0x9e79ec: lsl             w24, w23, #1
    //     0x9e79f0: add             w25, w24, #8
    //     0x9e79f4: add             x16, x4, w25, sxtw #1
    //     0x9e79f8: ldur            w3, [x16, #0xf]
    //     0x9e79fc: add             x3, x3, HEAP, lsl #32
    //     0x9e7a00: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f30] "surfaceTintColor"
    //     0x9e7a04: ldr             x16, [x16, #0xf30]
    //     0x9e7a08: cmp             w3, w16
    //     0x9e7a0c: b.ne            #0x9e7a3c
    //     0x9e7a10: add             w1, w24, #0xa
    //     0x9e7a14: add             x16, x4, w1, sxtw #1
    //     0x9e7a18: ldur            w3, [x16, #0xf]
    //     0x9e7a1c: add             x3, x3, HEAP, lsl #32
    //     0x9e7a20: sub             w1, w0, w3
    //     0x9e7a24: add             x3, fp, w1, sxtw #2
    //     0x9e7a28: ldr             x3, [x3, #8]
    //     0x9e7a2c: add             w1, w23, #2
    //     0x9e7a30: sbfx            x23, x1, #1, #0x1f
    //     0x9e7a34: mov             x1, x23
    //     0x9e7a38: b               #0x9e7a40
    //     0x9e7a3c: mov             x3, NULL
    //     0x9e7a40: stur            x3, [fp, #-0x38]
    //     0x9e7a44: lsl             x23, x1, #1
    //     0x9e7a48: lsl             w24, w23, #1
    //     0x9e7a4c: add             w25, w24, #8
    //     0x9e7a50: add             x16, x4, w25, sxtw #1
    //     0x9e7a54: ldur            w3, [x16, #0xf]
    //     0x9e7a58: add             x3, x3, HEAP, lsl #32
    //     0x9e7a5c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23da0] "tapTargetSize"
    //     0x9e7a60: ldr             x16, [x16, #0xda0]
    //     0x9e7a64: cmp             w3, w16
    //     0x9e7a68: b.ne            #0x9e7a98
    //     0x9e7a6c: add             w1, w24, #0xa
    //     0x9e7a70: add             x16, x4, w1, sxtw #1
    //     0x9e7a74: ldur            w3, [x16, #0xf]
    //     0x9e7a78: add             x3, x3, HEAP, lsl #32
    //     0x9e7a7c: sub             w1, w0, w3
    //     0x9e7a80: add             x3, fp, w1, sxtw #2
    //     0x9e7a84: ldr             x3, [x3, #8]
    //     0x9e7a88: add             w1, w23, #2
    //     0x9e7a8c: sbfx            x23, x1, #1, #0x1f
    //     0x9e7a90: mov             x1, x23
    //     0x9e7a94: b               #0x9e7a9c
    //     0x9e7a98: mov             x3, NULL
    //     0x9e7a9c: stur            x3, [fp, #-0x40]
    //     0x9e7aa0: lsl             x23, x1, #1
    //     0x9e7aa4: lsl             w24, w23, #1
    //     0x9e7aa8: add             w25, w24, #8
    //     0x9e7aac: add             x16, x4, w25, sxtw #1
    //     0x9e7ab0: ldur            w3, [x16, #0xf]
    //     0x9e7ab4: add             x3, x3, HEAP, lsl #32
    //     0x9e7ab8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23da8] "textStyle"
    //     0x9e7abc: ldr             x16, [x16, #0xda8]
    //     0x9e7ac0: cmp             w3, w16
    //     0x9e7ac4: b.ne            #0x9e7af4
    //     0x9e7ac8: add             w1, w24, #0xa
    //     0x9e7acc: add             x16, x4, w1, sxtw #1
    //     0x9e7ad0: ldur            w3, [x16, #0xf]
    //     0x9e7ad4: add             x3, x3, HEAP, lsl #32
    //     0x9e7ad8: sub             w1, w0, w3
    //     0x9e7adc: add             x3, fp, w1, sxtw #2
    //     0x9e7ae0: ldr             x3, [x3, #8]
    //     0x9e7ae4: add             w1, w23, #2
    //     0x9e7ae8: sbfx            x23, x1, #1, #0x1f
    //     0x9e7aec: mov             x1, x23
    //     0x9e7af0: b               #0x9e7af8
    //     0x9e7af4: mov             x3, NULL
    //     0x9e7af8: lsl             x23, x1, #1
    //     0x9e7afc: lsl             w1, w23, #1
    //     0x9e7b00: add             w23, w1, #8
    //     0x9e7b04: add             x16, x4, w23, sxtw #1
    //     0x9e7b08: ldur            w24, [x16, #0xf]
    //     0x9e7b0c: add             x24, x24, HEAP, lsl #32
    //     0x9e7b10: add             x16, PP, #0x23, lsl #12  ; [pp+0x23db0] "visualDensity"
    //     0x9e7b14: ldr             x16, [x16, #0xdb0]
    //     0x9e7b18: cmp             w24, w16
    //     0x9e7b1c: b.ne            #0x9e7b44
    //     0x9e7b20: add             w23, w1, #0xa
    //     0x9e7b24: add             x16, x4, w23, sxtw #1
    //     0x9e7b28: ldur            w1, [x16, #0xf]
    //     0x9e7b2c: add             x1, x1, HEAP, lsl #32
    //     0x9e7b30: sub             w4, w0, w1
    //     0x9e7b34: add             x0, fp, w4, sxtw #2
    //     0x9e7b38: ldr             x0, [x0, #8]
    //     0x9e7b3c: mov             x4, x0
    //     0x9e7b40: b               #0x9e7b48
    //     0x9e7b44: mov             x4, NULL
    //     0x9e7b48: stur            x4, [fp, #-0x48]
    // 0x9e7b4c: CheckStackOverflow
    //     0x9e7b4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e7b50: cmp             SP, x16
    //     0x9e7b54: b.ls            #0x9e80d0
    // 0x9e7b58: cmp             w3, NULL
    // 0x9e7b5c: b.ne            #0x9e7b80
    // 0x9e7b60: r0 = LoadClassIdInstr(r2)
    //     0x9e7b60: ldur            x0, [x2, #-1]
    //     0x9e7b64: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7b68: mov             x1, x2
    // 0x9e7b6c: r0 = GDT[cid_x0 + -0xcdd]()
    //     0x9e7b6c: sub             lr, x0, #0xcdd
    //     0x9e7b70: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7b74: blr             lr
    // 0x9e7b78: mov             x2, x0
    // 0x9e7b7c: b               #0x9e7b84
    // 0x9e7b80: mov             x2, x3
    // 0x9e7b84: ldur            x0, [fp, #-0xa0]
    // 0x9e7b88: stur            x2, [fp, #-0xb8]
    // 0x9e7b8c: cmp             w0, NULL
    // 0x9e7b90: b.ne            #0x9e7bb8
    // 0x9e7b94: ldur            x3, [fp, #-0xb0]
    // 0x9e7b98: r0 = LoadClassIdInstr(r3)
    //     0x9e7b98: ldur            x0, [x3, #-1]
    //     0x9e7b9c: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7ba0: mov             x1, x3
    // 0x9e7ba4: r0 = GDT[cid_x0 + 0xda8]()
    //     0x9e7ba4: add             lr, x0, #0xda8
    //     0x9e7ba8: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7bac: blr             lr
    // 0x9e7bb0: mov             x2, x0
    // 0x9e7bb4: b               #0x9e7bbc
    // 0x9e7bb8: mov             x2, x0
    // 0x9e7bbc: ldur            x0, [fp, #-0x80]
    // 0x9e7bc0: stur            x2, [fp, #-0xa0]
    // 0x9e7bc4: cmp             w0, NULL
    // 0x9e7bc8: b.ne            #0x9e7bf0
    // 0x9e7bcc: ldur            x3, [fp, #-0xb0]
    // 0x9e7bd0: r0 = LoadClassIdInstr(r3)
    //     0x9e7bd0: ldur            x0, [x3, #-1]
    //     0x9e7bd4: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7bd8: mov             x1, x3
    // 0x9e7bdc: r0 = GDT[cid_x0 + 0xf01]()
    //     0x9e7bdc: add             lr, x0, #0xf01
    //     0x9e7be0: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7be4: blr             lr
    // 0x9e7be8: mov             x2, x0
    // 0x9e7bec: b               #0x9e7bf4
    // 0x9e7bf0: mov             x2, x0
    // 0x9e7bf4: ldur            x0, [fp, #-0x50]
    // 0x9e7bf8: stur            x2, [fp, #-0x80]
    // 0x9e7bfc: cmp             w0, NULL
    // 0x9e7c00: b.ne            #0x9e7c28
    // 0x9e7c04: ldur            x3, [fp, #-0xb0]
    // 0x9e7c08: r0 = LoadClassIdInstr(r3)
    //     0x9e7c08: ldur            x0, [x3, #-1]
    //     0x9e7c0c: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7c10: mov             x1, x3
    // 0x9e7c14: r0 = GDT[cid_x0 + 0xf50]()
    //     0x9e7c14: add             lr, x0, #0xf50
    //     0x9e7c18: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7c1c: blr             lr
    // 0x9e7c20: mov             x2, x0
    // 0x9e7c24: b               #0x9e7c2c
    // 0x9e7c28: mov             x2, x0
    // 0x9e7c2c: ldur            x0, [fp, #-0x18]
    // 0x9e7c30: stur            x2, [fp, #-0x50]
    // 0x9e7c34: cmp             w0, NULL
    // 0x9e7c38: b.ne            #0x9e7c60
    // 0x9e7c3c: ldur            x3, [fp, #-0xb0]
    // 0x9e7c40: r0 = LoadClassIdInstr(r3)
    //     0x9e7c40: ldur            x0, [x3, #-1]
    //     0x9e7c44: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7c48: mov             x1, x3
    // 0x9e7c4c: r0 = GDT[cid_x0 + 0xf29]()
    //     0x9e7c4c: add             lr, x0, #0xf29
    //     0x9e7c50: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7c54: blr             lr
    // 0x9e7c58: mov             x2, x0
    // 0x9e7c5c: b               #0x9e7c64
    // 0x9e7c60: mov             x2, x0
    // 0x9e7c64: ldur            x0, [fp, #-0x38]
    // 0x9e7c68: stur            x2, [fp, #-0x18]
    // 0x9e7c6c: cmp             w0, NULL
    // 0x9e7c70: b.ne            #0x9e7c98
    // 0x9e7c74: ldur            x3, [fp, #-0xb0]
    // 0x9e7c78: r0 = LoadClassIdInstr(r3)
    //     0x9e7c78: ldur            x0, [x3, #-1]
    //     0x9e7c7c: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7c80: mov             x1, x3
    // 0x9e7c84: r0 = GDT[cid_x0 + 0x936]()
    //     0x9e7c84: add             lr, x0, #0x936
    //     0x9e7c88: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7c8c: blr             lr
    // 0x9e7c90: mov             x2, x0
    // 0x9e7c94: b               #0x9e7c9c
    // 0x9e7c98: mov             x2, x0
    // 0x9e7c9c: ldur            x0, [fp, #-0x98]
    // 0x9e7ca0: stur            x2, [fp, #-0x38]
    // 0x9e7ca4: cmp             w0, NULL
    // 0x9e7ca8: b.ne            #0x9e7cd0
    // 0x9e7cac: ldur            x3, [fp, #-0xb0]
    // 0x9e7cb0: r0 = LoadClassIdInstr(r3)
    //     0x9e7cb0: ldur            x0, [x3, #-1]
    //     0x9e7cb4: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7cb8: mov             x1, x3
    // 0x9e7cbc: r0 = GDT[cid_x0 + 0xef2]()
    //     0x9e7cbc: add             lr, x0, #0xef2
    //     0x9e7cc0: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7cc4: blr             lr
    // 0x9e7cc8: mov             x2, x0
    // 0x9e7ccc: b               #0x9e7cd4
    // 0x9e7cd0: mov             x2, x0
    // 0x9e7cd4: ldur            x0, [fp, #-0x10]
    // 0x9e7cd8: stur            x2, [fp, #-0x98]
    // 0x9e7cdc: cmp             w0, NULL
    // 0x9e7ce0: b.ne            #0x9e7cf8
    // 0x9e7ce4: ldur            x3, [fp, #-0xb0]
    // 0x9e7ce8: LoadField: r0 = r3->field_23
    //     0x9e7ce8: ldur            w0, [x3, #0x23]
    // 0x9e7cec: DecompressPointer r0
    //     0x9e7cec: add             x0, x0, HEAP, lsl #32
    // 0x9e7cf0: mov             x4, x0
    // 0x9e7cf4: b               #0x9e7d00
    // 0x9e7cf8: ldur            x3, [fp, #-0xb0]
    // 0x9e7cfc: mov             x4, x0
    // 0x9e7d00: ldur            x0, [fp, #-0x60]
    // 0x9e7d04: stur            x4, [fp, #-0x10]
    // 0x9e7d08: cmp             w0, NULL
    // 0x9e7d0c: b.ne            #0x9e7d30
    // 0x9e7d10: r0 = LoadClassIdInstr(r3)
    //     0x9e7d10: ldur            x0, [x3, #-1]
    //     0x9e7d14: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7d18: mov             x1, x3
    // 0x9e7d1c: r0 = GDT[cid_x0 + 0xdc0]()
    //     0x9e7d1c: add             lr, x0, #0xdc0
    //     0x9e7d20: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7d24: blr             lr
    // 0x9e7d28: mov             x2, x0
    // 0x9e7d2c: b               #0x9e7d34
    // 0x9e7d30: mov             x2, x0
    // 0x9e7d34: ldur            x0, [fp, #-0x88]
    // 0x9e7d38: stur            x2, [fp, #-0xc0]
    // 0x9e7d3c: cmp             w0, NULL
    // 0x9e7d40: b.ne            #0x9e7d58
    // 0x9e7d44: ldur            x3, [fp, #-0xb0]
    // 0x9e7d48: LoadField: r0 = r3->field_2b
    //     0x9e7d48: ldur            w0, [x3, #0x2b]
    // 0x9e7d4c: DecompressPointer r0
    //     0x9e7d4c: add             x0, x0, HEAP, lsl #32
    // 0x9e7d50: mov             x4, x0
    // 0x9e7d54: b               #0x9e7d60
    // 0x9e7d58: ldur            x3, [fp, #-0xb0]
    // 0x9e7d5c: mov             x4, x0
    // 0x9e7d60: ldur            x0, [fp, #-0x68]
    // 0x9e7d64: stur            x4, [fp, #-0x60]
    // 0x9e7d68: cmp             w0, NULL
    // 0x9e7d6c: b.ne            #0x9e7d90
    // 0x9e7d70: r0 = LoadClassIdInstr(r3)
    //     0x9e7d70: ldur            x0, [x3, #-1]
    //     0x9e7d74: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7d78: mov             x1, x3
    // 0x9e7d7c: r0 = GDT[cid_x0 + 0xe8d]()
    //     0x9e7d7c: add             lr, x0, #0xe8d
    //     0x9e7d80: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7d84: blr             lr
    // 0x9e7d88: mov             x2, x0
    // 0x9e7d8c: b               #0x9e7d94
    // 0x9e7d90: mov             x2, x0
    // 0x9e7d94: ldur            x0, [fp, #-0x78]
    // 0x9e7d98: stur            x2, [fp, #-0x68]
    // 0x9e7d9c: cmp             w0, NULL
    // 0x9e7da0: b.ne            #0x9e7dc8
    // 0x9e7da4: ldur            x3, [fp, #-0xb0]
    // 0x9e7da8: r0 = LoadClassIdInstr(r3)
    //     0x9e7da8: ldur            x0, [x3, #-1]
    //     0x9e7dac: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7db0: mov             x1, x3
    // 0x9e7db4: r0 = GDT[cid_x0 + 0xdcc]()
    //     0x9e7db4: add             lr, x0, #0xdcc
    //     0x9e7db8: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7dbc: blr             lr
    // 0x9e7dc0: mov             x2, x0
    // 0x9e7dc4: b               #0x9e7dcc
    // 0x9e7dc8: mov             x2, x0
    // 0x9e7dcc: ldur            x0, [fp, #-0x70]
    // 0x9e7dd0: stur            x2, [fp, #-0x78]
    // 0x9e7dd4: cmp             w0, NULL
    // 0x9e7dd8: b.ne            #0x9e7e00
    // 0x9e7ddc: ldur            x3, [fp, #-0xb0]
    // 0x9e7de0: r0 = LoadClassIdInstr(r3)
    //     0x9e7de0: ldur            x0, [x3, #-1]
    //     0x9e7de4: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7de8: mov             x1, x3
    // 0x9e7dec: r0 = GDT[cid_x0 + 0xe9c]()
    //     0x9e7dec: add             lr, x0, #0xe9c
    //     0x9e7df0: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7df4: blr             lr
    // 0x9e7df8: mov             x2, x0
    // 0x9e7dfc: b               #0x9e7e04
    // 0x9e7e00: mov             x2, x0
    // 0x9e7e04: ldur            x0, [fp, #-0x28]
    // 0x9e7e08: stur            x2, [fp, #-0x70]
    // 0x9e7e0c: cmp             w0, NULL
    // 0x9e7e10: b.ne            #0x9e7e38
    // 0x9e7e14: ldur            x3, [fp, #-0xb0]
    // 0x9e7e18: r0 = LoadClassIdInstr(r3)
    //     0x9e7e18: ldur            x0, [x3, #-1]
    //     0x9e7e1c: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7e20: mov             x1, x3
    // 0x9e7e24: r0 = GDT[cid_x0 + 0xeb5]()
    //     0x9e7e24: add             lr, x0, #0xeb5
    //     0x9e7e28: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7e2c: blr             lr
    // 0x9e7e30: mov             x2, x0
    // 0x9e7e34: b               #0x9e7e3c
    // 0x9e7e38: mov             x2, x0
    // 0x9e7e3c: ldur            x0, [fp, #-0x20]
    // 0x9e7e40: stur            x2, [fp, #-0x28]
    // 0x9e7e44: cmp             w0, NULL
    // 0x9e7e48: b.ne            #0x9e7e70
    // 0x9e7e4c: ldur            x3, [fp, #-0xb0]
    // 0x9e7e50: r0 = LoadClassIdInstr(r3)
    //     0x9e7e50: ldur            x0, [x3, #-1]
    //     0x9e7e54: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7e58: mov             x1, x3
    // 0x9e7e5c: r0 = GDT[cid_x0 + 0xece]()
    //     0x9e7e5c: add             lr, x0, #0xece
    //     0x9e7e60: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7e64: blr             lr
    // 0x9e7e68: mov             x2, x0
    // 0x9e7e6c: b               #0x9e7e74
    // 0x9e7e70: mov             x2, x0
    // 0x9e7e74: ldur            x0, [fp, #-0x58]
    // 0x9e7e78: stur            x2, [fp, #-0x20]
    // 0x9e7e7c: cmp             w0, NULL
    // 0x9e7e80: b.ne            #0x9e7ea8
    // 0x9e7e84: ldur            x3, [fp, #-0xb0]
    // 0x9e7e88: r0 = LoadClassIdInstr(r3)
    //     0x9e7e88: ldur            x0, [x3, #-1]
    //     0x9e7e8c: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7e90: mov             x1, x3
    // 0x9e7e94: r0 = GDT[cid_x0 + 0xf0e]()
    //     0x9e7e94: add             lr, x0, #0xf0e
    //     0x9e7e98: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7e9c: blr             lr
    // 0x9e7ea0: mov             x2, x0
    // 0x9e7ea4: b               #0x9e7eac
    // 0x9e7ea8: mov             x2, x0
    // 0x9e7eac: ldur            x0, [fp, #-0x48]
    // 0x9e7eb0: stur            x2, [fp, #-0x58]
    // 0x9e7eb4: cmp             w0, NULL
    // 0x9e7eb8: b.ne            #0x9e7ee0
    // 0x9e7ebc: ldur            x3, [fp, #-0xb0]
    // 0x9e7ec0: r0 = LoadClassIdInstr(r3)
    //     0x9e7ec0: ldur            x0, [x3, #-1]
    //     0x9e7ec4: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7ec8: mov             x1, x3
    // 0x9e7ecc: r0 = GDT[cid_x0 + 0xf66]()
    //     0x9e7ecc: add             lr, x0, #0xf66
    //     0x9e7ed0: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7ed4: blr             lr
    // 0x9e7ed8: mov             x2, x0
    // 0x9e7edc: b               #0x9e7ee4
    // 0x9e7ee0: mov             x2, x0
    // 0x9e7ee4: ldur            x0, [fp, #-0x40]
    // 0x9e7ee8: stur            x2, [fp, #-0x48]
    // 0x9e7eec: cmp             w0, NULL
    // 0x9e7ef0: b.ne            #0x9e7f18
    // 0x9e7ef4: ldur            x3, [fp, #-0xb0]
    // 0x9e7ef8: r0 = LoadClassIdInstr(r3)
    //     0x9e7ef8: ldur            x0, [x3, #-1]
    //     0x9e7efc: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7f00: mov             x1, x3
    // 0x9e7f04: r0 = GDT[cid_x0 + 0xf82]()
    //     0x9e7f04: add             lr, x0, #0xf82
    //     0x9e7f08: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7f0c: blr             lr
    // 0x9e7f10: mov             x2, x0
    // 0x9e7f14: b               #0x9e7f1c
    // 0x9e7f18: mov             x2, x0
    // 0x9e7f1c: ldur            x0, [fp, #-0xa8]
    // 0x9e7f20: stur            x2, [fp, #-0xc8]
    // 0x9e7f24: cmp             w0, NULL
    // 0x9e7f28: b.ne            #0x9e7f40
    // 0x9e7f2c: ldur            x1, [fp, #-0xb0]
    // 0x9e7f30: LoadField: r0 = r1->field_4f
    //     0x9e7f30: ldur            w0, [x1, #0x4f]
    // 0x9e7f34: DecompressPointer r0
    //     0x9e7f34: add             x0, x0, HEAP, lsl #32
    // 0x9e7f38: mov             x3, x0
    // 0x9e7f3c: b               #0x9e7f48
    // 0x9e7f40: ldur            x1, [fp, #-0xb0]
    // 0x9e7f44: mov             x3, x0
    // 0x9e7f48: ldur            x0, [fp, #-0x90]
    // 0x9e7f4c: stur            x3, [fp, #-0x88]
    // 0x9e7f50: cmp             w0, NULL
    // 0x9e7f54: b.ne            #0x9e7f68
    // 0x9e7f58: LoadField: r0 = r1->field_53
    //     0x9e7f58: ldur            w0, [x1, #0x53]
    // 0x9e7f5c: DecompressPointer r0
    //     0x9e7f5c: add             x0, x0, HEAP, lsl #32
    // 0x9e7f60: mov             x4, x0
    // 0x9e7f64: b               #0x9e7f6c
    // 0x9e7f68: mov             x4, x0
    // 0x9e7f6c: ldur            x0, [fp, #-8]
    // 0x9e7f70: stur            x4, [fp, #-0x40]
    // 0x9e7f74: cmp             w0, NULL
    // 0x9e7f78: b.ne            #0x9e7f8c
    // 0x9e7f7c: LoadField: r0 = r1->field_57
    //     0x9e7f7c: ldur            w0, [x1, #0x57]
    // 0x9e7f80: DecompressPointer r0
    //     0x9e7f80: add             x0, x0, HEAP, lsl #32
    // 0x9e7f84: mov             x5, x0
    // 0x9e7f88: b               #0x9e7f90
    // 0x9e7f8c: mov             x5, x0
    // 0x9e7f90: ldur            x0, [fp, #-0x30]
    // 0x9e7f94: stur            x5, [fp, #-8]
    // 0x9e7f98: cmp             w0, NULL
    // 0x9e7f9c: b.ne            #0x9e7fbc
    // 0x9e7fa0: r0 = LoadClassIdInstr(r1)
    //     0x9e7fa0: ldur            x0, [x1, #-1]
    //     0x9e7fa4: ubfx            x0, x0, #0xc, #0x14
    // 0x9e7fa8: r0 = GDT[cid_x0 + 0x9f4]()
    //     0x9e7fa8: add             lr, x0, #0x9f4
    //     0x9e7fac: ldr             lr, [x21, lr, lsl #3]
    //     0x9e7fb0: blr             lr
    // 0x9e7fb4: stur            x0, [fp, #-0x30]
    // 0x9e7fb8: b               #0x9e7fc0
    // 0x9e7fbc: stur            x0, [fp, #-0x30]
    // 0x9e7fc0: ldur            x25, [fp, #-0xa0]
    // 0x9e7fc4: ldur            x24, [fp, #-0x80]
    // 0x9e7fc8: ldur            x23, [fp, #-0x50]
    // 0x9e7fcc: ldur            x20, [fp, #-0x18]
    // 0x9e7fd0: ldur            x19, [fp, #-0x38]
    // 0x9e7fd4: ldur            x13, [fp, #-0x98]
    // 0x9e7fd8: ldur            x14, [fp, #-0x10]
    // 0x9e7fdc: ldur            x11, [fp, #-0xc0]
    // 0x9e7fe0: ldur            x12, [fp, #-0x60]
    // 0x9e7fe4: ldur            x10, [fp, #-0x68]
    // 0x9e7fe8: ldur            x9, [fp, #-0x78]
    // 0x9e7fec: ldur            x8, [fp, #-0x70]
    // 0x9e7ff0: ldur            x7, [fp, #-0x28]
    // 0x9e7ff4: ldur            x6, [fp, #-0x20]
    // 0x9e7ff8: ldur            x5, [fp, #-0x58]
    // 0x9e7ffc: ldur            x4, [fp, #-0x48]
    // 0x9e8000: ldur            x0, [fp, #-0xc8]
    // 0x9e8004: ldur            x1, [fp, #-0x88]
    // 0x9e8008: ldur            x2, [fp, #-0x40]
    // 0x9e800c: ldur            x3, [fp, #-8]
    // 0x9e8010: r0 = ButtonStyle()
    //     0x9e8010: bl              #0x87ab84  ; AllocateButtonStyleStub -> ButtonStyle (size=0x68)
    // 0x9e8014: ldur            x1, [fp, #-0xb8]
    // 0x9e8018: StoreField: r0->field_7 = r1
    //     0x9e8018: stur            w1, [x0, #7]
    // 0x9e801c: ldur            x1, [fp, #-0xa0]
    // 0x9e8020: StoreField: r0->field_b = r1
    //     0x9e8020: stur            w1, [x0, #0xb]
    // 0x9e8024: ldur            x1, [fp, #-0x80]
    // 0x9e8028: StoreField: r0->field_f = r1
    //     0x9e8028: stur            w1, [x0, #0xf]
    // 0x9e802c: ldur            x1, [fp, #-0x50]
    // 0x9e8030: StoreField: r0->field_13 = r1
    //     0x9e8030: stur            w1, [x0, #0x13]
    // 0x9e8034: ldur            x1, [fp, #-0x18]
    // 0x9e8038: ArrayStore: r0[0] = r1  ; List_4
    //     0x9e8038: stur            w1, [x0, #0x17]
    // 0x9e803c: ldur            x1, [fp, #-0x38]
    // 0x9e8040: StoreField: r0->field_1b = r1
    //     0x9e8040: stur            w1, [x0, #0x1b]
    // 0x9e8044: ldur            x1, [fp, #-0x98]
    // 0x9e8048: StoreField: r0->field_1f = r1
    //     0x9e8048: stur            w1, [x0, #0x1f]
    // 0x9e804c: ldur            x1, [fp, #-0x10]
    // 0x9e8050: StoreField: r0->field_23 = r1
    //     0x9e8050: stur            w1, [x0, #0x23]
    // 0x9e8054: ldur            x1, [fp, #-0xc0]
    // 0x9e8058: StoreField: r0->field_27 = r1
    //     0x9e8058: stur            w1, [x0, #0x27]
    // 0x9e805c: ldur            x1, [fp, #-0x60]
    // 0x9e8060: StoreField: r0->field_2b = r1
    //     0x9e8060: stur            w1, [x0, #0x2b]
    // 0x9e8064: ldur            x1, [fp, #-0x68]
    // 0x9e8068: StoreField: r0->field_2f = r1
    //     0x9e8068: stur            w1, [x0, #0x2f]
    // 0x9e806c: ldur            x1, [fp, #-0x78]
    // 0x9e8070: StoreField: r0->field_33 = r1
    //     0x9e8070: stur            w1, [x0, #0x33]
    // 0x9e8074: ldur            x1, [fp, #-0x70]
    // 0x9e8078: StoreField: r0->field_37 = r1
    //     0x9e8078: stur            w1, [x0, #0x37]
    // 0x9e807c: ldur            x1, [fp, #-0x28]
    // 0x9e8080: StoreField: r0->field_3b = r1
    //     0x9e8080: stur            w1, [x0, #0x3b]
    // 0x9e8084: ldur            x1, [fp, #-0x20]
    // 0x9e8088: StoreField: r0->field_3f = r1
    //     0x9e8088: stur            w1, [x0, #0x3f]
    // 0x9e808c: ldur            x1, [fp, #-0x58]
    // 0x9e8090: StoreField: r0->field_43 = r1
    //     0x9e8090: stur            w1, [x0, #0x43]
    // 0x9e8094: ldur            x1, [fp, #-0x48]
    // 0x9e8098: StoreField: r0->field_47 = r1
    //     0x9e8098: stur            w1, [x0, #0x47]
    // 0x9e809c: ldur            x1, [fp, #-0xc8]
    // 0x9e80a0: StoreField: r0->field_4b = r1
    //     0x9e80a0: stur            w1, [x0, #0x4b]
    // 0x9e80a4: ldur            x1, [fp, #-0x88]
    // 0x9e80a8: StoreField: r0->field_4f = r1
    //     0x9e80a8: stur            w1, [x0, #0x4f]
    // 0x9e80ac: ldur            x1, [fp, #-0x40]
    // 0x9e80b0: StoreField: r0->field_53 = r1
    //     0x9e80b0: stur            w1, [x0, #0x53]
    // 0x9e80b4: ldur            x1, [fp, #-8]
    // 0x9e80b8: StoreField: r0->field_57 = r1
    //     0x9e80b8: stur            w1, [x0, #0x57]
    // 0x9e80bc: ldur            x1, [fp, #-0x30]
    // 0x9e80c0: StoreField: r0->field_5b = r1
    //     0x9e80c0: stur            w1, [x0, #0x5b]
    // 0x9e80c4: LeaveFrame
    //     0x9e80c4: mov             SP, fp
    //     0x9e80c8: ldp             fp, lr, [SP], #0x10
    // 0x9e80cc: ret
    //     0x9e80cc: ret             
    // 0x9e80d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e80d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e80d4: b               #0x9e7b58
  }
  _ merge(/* No info */) {
    // ** addr: 0x9f72ec, size: 0x2bc
    // 0x9f72ec: EnterFrame
    //     0x9f72ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9f72f0: mov             fp, SP
    // 0x9f72f4: AllocStack(0xd0)
    //     0x9f72f4: sub             SP, SP, #0xd0
    // 0x9f72f8: SetupParameters(ButtonStyle this /* r1 => r0 */)
    //     0x9f72f8: mov             x0, x1
    // 0x9f72fc: CheckStackOverflow
    //     0x9f72fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f7300: cmp             SP, x16
    //     0x9f7304: b.ls            #0x9f75a0
    // 0x9f7308: cmp             w2, NULL
    // 0x9f730c: b.ne            #0x9f731c
    // 0x9f7310: LeaveFrame
    //     0x9f7310: mov             SP, fp
    //     0x9f7314: ldp             fp, lr, [SP], #0x10
    // 0x9f7318: ret
    //     0x9f7318: ret             
    // 0x9f731c: LoadField: r1 = r0->field_7
    //     0x9f731c: ldur            w1, [x0, #7]
    // 0x9f7320: DecompressPointer r1
    //     0x9f7320: add             x1, x1, HEAP, lsl #32
    // 0x9f7324: cmp             w1, NULL
    // 0x9f7328: b.ne            #0x9f7334
    // 0x9f732c: LoadField: r1 = r2->field_7
    //     0x9f732c: ldur            w1, [x2, #7]
    // 0x9f7330: DecompressPointer r1
    //     0x9f7330: add             x1, x1, HEAP, lsl #32
    // 0x9f7334: LoadField: r3 = r0->field_b
    //     0x9f7334: ldur            w3, [x0, #0xb]
    // 0x9f7338: DecompressPointer r3
    //     0x9f7338: add             x3, x3, HEAP, lsl #32
    // 0x9f733c: cmp             w3, NULL
    // 0x9f7340: b.ne            #0x9f734c
    // 0x9f7344: LoadField: r3 = r2->field_b
    //     0x9f7344: ldur            w3, [x2, #0xb]
    // 0x9f7348: DecompressPointer r3
    //     0x9f7348: add             x3, x3, HEAP, lsl #32
    // 0x9f734c: LoadField: r4 = r0->field_f
    //     0x9f734c: ldur            w4, [x0, #0xf]
    // 0x9f7350: DecompressPointer r4
    //     0x9f7350: add             x4, x4, HEAP, lsl #32
    // 0x9f7354: cmp             w4, NULL
    // 0x9f7358: b.ne            #0x9f7364
    // 0x9f735c: LoadField: r4 = r2->field_f
    //     0x9f735c: ldur            w4, [x2, #0xf]
    // 0x9f7360: DecompressPointer r4
    //     0x9f7360: add             x4, x4, HEAP, lsl #32
    // 0x9f7364: LoadField: r5 = r0->field_13
    //     0x9f7364: ldur            w5, [x0, #0x13]
    // 0x9f7368: DecompressPointer r5
    //     0x9f7368: add             x5, x5, HEAP, lsl #32
    // 0x9f736c: cmp             w5, NULL
    // 0x9f7370: b.ne            #0x9f737c
    // 0x9f7374: LoadField: r5 = r2->field_13
    //     0x9f7374: ldur            w5, [x2, #0x13]
    // 0x9f7378: DecompressPointer r5
    //     0x9f7378: add             x5, x5, HEAP, lsl #32
    // 0x9f737c: ArrayLoad: r6 = r0[0]  ; List_4
    //     0x9f737c: ldur            w6, [x0, #0x17]
    // 0x9f7380: DecompressPointer r6
    //     0x9f7380: add             x6, x6, HEAP, lsl #32
    // 0x9f7384: cmp             w6, NULL
    // 0x9f7388: b.ne            #0x9f7394
    // 0x9f738c: ArrayLoad: r6 = r2[0]  ; List_4
    //     0x9f738c: ldur            w6, [x2, #0x17]
    // 0x9f7390: DecompressPointer r6
    //     0x9f7390: add             x6, x6, HEAP, lsl #32
    // 0x9f7394: LoadField: r7 = r0->field_1b
    //     0x9f7394: ldur            w7, [x0, #0x1b]
    // 0x9f7398: DecompressPointer r7
    //     0x9f7398: add             x7, x7, HEAP, lsl #32
    // 0x9f739c: cmp             w7, NULL
    // 0x9f73a0: b.ne            #0x9f73ac
    // 0x9f73a4: LoadField: r7 = r2->field_1b
    //     0x9f73a4: ldur            w7, [x2, #0x1b]
    // 0x9f73a8: DecompressPointer r7
    //     0x9f73a8: add             x7, x7, HEAP, lsl #32
    // 0x9f73ac: LoadField: r8 = r0->field_1f
    //     0x9f73ac: ldur            w8, [x0, #0x1f]
    // 0x9f73b0: DecompressPointer r8
    //     0x9f73b0: add             x8, x8, HEAP, lsl #32
    // 0x9f73b4: cmp             w8, NULL
    // 0x9f73b8: b.ne            #0x9f73c4
    // 0x9f73bc: LoadField: r8 = r2->field_1f
    //     0x9f73bc: ldur            w8, [x2, #0x1f]
    // 0x9f73c0: DecompressPointer r8
    //     0x9f73c0: add             x8, x8, HEAP, lsl #32
    // 0x9f73c4: LoadField: r9 = r0->field_23
    //     0x9f73c4: ldur            w9, [x0, #0x23]
    // 0x9f73c8: DecompressPointer r9
    //     0x9f73c8: add             x9, x9, HEAP, lsl #32
    // 0x9f73cc: cmp             w9, NULL
    // 0x9f73d0: b.ne            #0x9f73dc
    // 0x9f73d4: LoadField: r9 = r2->field_23
    //     0x9f73d4: ldur            w9, [x2, #0x23]
    // 0x9f73d8: DecompressPointer r9
    //     0x9f73d8: add             x9, x9, HEAP, lsl #32
    // 0x9f73dc: LoadField: r10 = r0->field_27
    //     0x9f73dc: ldur            w10, [x0, #0x27]
    // 0x9f73e0: DecompressPointer r10
    //     0x9f73e0: add             x10, x10, HEAP, lsl #32
    // 0x9f73e4: cmp             w10, NULL
    // 0x9f73e8: b.ne            #0x9f73f4
    // 0x9f73ec: LoadField: r10 = r2->field_27
    //     0x9f73ec: ldur            w10, [x2, #0x27]
    // 0x9f73f0: DecompressPointer r10
    //     0x9f73f0: add             x10, x10, HEAP, lsl #32
    // 0x9f73f4: LoadField: r11 = r0->field_2b
    //     0x9f73f4: ldur            w11, [x0, #0x2b]
    // 0x9f73f8: DecompressPointer r11
    //     0x9f73f8: add             x11, x11, HEAP, lsl #32
    // 0x9f73fc: cmp             w11, NULL
    // 0x9f7400: b.ne            #0x9f740c
    // 0x9f7404: LoadField: r11 = r2->field_2b
    //     0x9f7404: ldur            w11, [x2, #0x2b]
    // 0x9f7408: DecompressPointer r11
    //     0x9f7408: add             x11, x11, HEAP, lsl #32
    // 0x9f740c: LoadField: r12 = r0->field_2f
    //     0x9f740c: ldur            w12, [x0, #0x2f]
    // 0x9f7410: DecompressPointer r12
    //     0x9f7410: add             x12, x12, HEAP, lsl #32
    // 0x9f7414: cmp             w12, NULL
    // 0x9f7418: b.ne            #0x9f7424
    // 0x9f741c: LoadField: r12 = r2->field_2f
    //     0x9f741c: ldur            w12, [x2, #0x2f]
    // 0x9f7420: DecompressPointer r12
    //     0x9f7420: add             x12, x12, HEAP, lsl #32
    // 0x9f7424: LoadField: r13 = r0->field_33
    //     0x9f7424: ldur            w13, [x0, #0x33]
    // 0x9f7428: DecompressPointer r13
    //     0x9f7428: add             x13, x13, HEAP, lsl #32
    // 0x9f742c: cmp             w13, NULL
    // 0x9f7430: b.ne            #0x9f743c
    // 0x9f7434: LoadField: r13 = r2->field_33
    //     0x9f7434: ldur            w13, [x2, #0x33]
    // 0x9f7438: DecompressPointer r13
    //     0x9f7438: add             x13, x13, HEAP, lsl #32
    // 0x9f743c: LoadField: r14 = r0->field_37
    //     0x9f743c: ldur            w14, [x0, #0x37]
    // 0x9f7440: DecompressPointer r14
    //     0x9f7440: add             x14, x14, HEAP, lsl #32
    // 0x9f7444: cmp             w14, NULL
    // 0x9f7448: b.ne            #0x9f7454
    // 0x9f744c: LoadField: r14 = r2->field_37
    //     0x9f744c: ldur            w14, [x2, #0x37]
    // 0x9f7450: DecompressPointer r14
    //     0x9f7450: add             x14, x14, HEAP, lsl #32
    // 0x9f7454: LoadField: r19 = r0->field_3b
    //     0x9f7454: ldur            w19, [x0, #0x3b]
    // 0x9f7458: DecompressPointer r19
    //     0x9f7458: add             x19, x19, HEAP, lsl #32
    // 0x9f745c: cmp             w19, NULL
    // 0x9f7460: b.ne            #0x9f746c
    // 0x9f7464: LoadField: r19 = r2->field_3b
    //     0x9f7464: ldur            w19, [x2, #0x3b]
    // 0x9f7468: DecompressPointer r19
    //     0x9f7468: add             x19, x19, HEAP, lsl #32
    // 0x9f746c: LoadField: r20 = r0->field_3f
    //     0x9f746c: ldur            w20, [x0, #0x3f]
    // 0x9f7470: DecompressPointer r20
    //     0x9f7470: add             x20, x20, HEAP, lsl #32
    // 0x9f7474: cmp             w20, NULL
    // 0x9f7478: b.ne            #0x9f7484
    // 0x9f747c: LoadField: r20 = r2->field_3f
    //     0x9f747c: ldur            w20, [x2, #0x3f]
    // 0x9f7480: DecompressPointer r20
    //     0x9f7480: add             x20, x20, HEAP, lsl #32
    // 0x9f7484: LoadField: r23 = r0->field_43
    //     0x9f7484: ldur            w23, [x0, #0x43]
    // 0x9f7488: DecompressPointer r23
    //     0x9f7488: add             x23, x23, HEAP, lsl #32
    // 0x9f748c: cmp             w23, NULL
    // 0x9f7490: b.ne            #0x9f749c
    // 0x9f7494: LoadField: r23 = r2->field_43
    //     0x9f7494: ldur            w23, [x2, #0x43]
    // 0x9f7498: DecompressPointer r23
    //     0x9f7498: add             x23, x23, HEAP, lsl #32
    // 0x9f749c: LoadField: r24 = r0->field_47
    //     0x9f749c: ldur            w24, [x0, #0x47]
    // 0x9f74a0: DecompressPointer r24
    //     0x9f74a0: add             x24, x24, HEAP, lsl #32
    // 0x9f74a4: cmp             w24, NULL
    // 0x9f74a8: b.ne            #0x9f74b4
    // 0x9f74ac: LoadField: r24 = r2->field_47
    //     0x9f74ac: ldur            w24, [x2, #0x47]
    // 0x9f74b0: DecompressPointer r24
    //     0x9f74b0: add             x24, x24, HEAP, lsl #32
    // 0x9f74b4: LoadField: r25 = r0->field_4b
    //     0x9f74b4: ldur            w25, [x0, #0x4b]
    // 0x9f74b8: DecompressPointer r25
    //     0x9f74b8: add             x25, x25, HEAP, lsl #32
    // 0x9f74bc: cmp             w25, NULL
    // 0x9f74c0: b.ne            #0x9f74cc
    // 0x9f74c4: LoadField: r25 = r2->field_4b
    //     0x9f74c4: ldur            w25, [x2, #0x4b]
    // 0x9f74c8: DecompressPointer r25
    //     0x9f74c8: add             x25, x25, HEAP, lsl #32
    // 0x9f74cc: stur            x25, [fp, #-8]
    // 0x9f74d0: LoadField: r25 = r0->field_4f
    //     0x9f74d0: ldur            w25, [x0, #0x4f]
    // 0x9f74d4: DecompressPointer r25
    //     0x9f74d4: add             x25, x25, HEAP, lsl #32
    // 0x9f74d8: cmp             w25, NULL
    // 0x9f74dc: b.ne            #0x9f74e8
    // 0x9f74e0: LoadField: r25 = r2->field_4f
    //     0x9f74e0: ldur            w25, [x2, #0x4f]
    // 0x9f74e4: DecompressPointer r25
    //     0x9f74e4: add             x25, x25, HEAP, lsl #32
    // 0x9f74e8: stur            x25, [fp, #-0x10]
    // 0x9f74ec: LoadField: r25 = r0->field_53
    //     0x9f74ec: ldur            w25, [x0, #0x53]
    // 0x9f74f0: DecompressPointer r25
    //     0x9f74f0: add             x25, x25, HEAP, lsl #32
    // 0x9f74f4: cmp             w25, NULL
    // 0x9f74f8: b.ne            #0x9f7504
    // 0x9f74fc: LoadField: r25 = r2->field_53
    //     0x9f74fc: ldur            w25, [x2, #0x53]
    // 0x9f7500: DecompressPointer r25
    //     0x9f7500: add             x25, x25, HEAP, lsl #32
    // 0x9f7504: stur            x25, [fp, #-0x18]
    // 0x9f7508: LoadField: r25 = r0->field_57
    //     0x9f7508: ldur            w25, [x0, #0x57]
    // 0x9f750c: DecompressPointer r25
    //     0x9f750c: add             x25, x25, HEAP, lsl #32
    // 0x9f7510: cmp             w25, NULL
    // 0x9f7514: b.ne            #0x9f7520
    // 0x9f7518: LoadField: r25 = r2->field_57
    //     0x9f7518: ldur            w25, [x2, #0x57]
    // 0x9f751c: DecompressPointer r25
    //     0x9f751c: add             x25, x25, HEAP, lsl #32
    // 0x9f7520: stur            x25, [fp, #-0x20]
    // 0x9f7524: LoadField: r25 = r0->field_5b
    //     0x9f7524: ldur            w25, [x0, #0x5b]
    // 0x9f7528: DecompressPointer r25
    //     0x9f7528: add             x25, x25, HEAP, lsl #32
    // 0x9f752c: cmp             w25, NULL
    // 0x9f7530: b.ne            #0x9f7544
    // 0x9f7534: LoadField: r25 = r2->field_5b
    //     0x9f7534: ldur            w25, [x2, #0x5b]
    // 0x9f7538: DecompressPointer r25
    //     0x9f7538: add             x25, x25, HEAP, lsl #32
    // 0x9f753c: mov             x2, x25
    // 0x9f7540: b               #0x9f7548
    // 0x9f7544: mov             x2, x25
    // 0x9f7548: stp             x3, x1, [SP, #0xa0]
    // 0x9f754c: stp             x5, x4, [SP, #0x90]
    // 0x9f7550: stp             x7, x6, [SP, #0x80]
    // 0x9f7554: stp             x9, x8, [SP, #0x70]
    // 0x9f7558: stp             x11, x10, [SP, #0x60]
    // 0x9f755c: stp             x13, x12, [SP, #0x50]
    // 0x9f7560: stp             x19, x14, [SP, #0x40]
    // 0x9f7564: stp             x23, x20, [SP, #0x30]
    // 0x9f7568: ldur            x16, [fp, #-8]
    // 0x9f756c: stp             x16, x24, [SP, #0x20]
    // 0x9f7570: ldur            x16, [fp, #-0x10]
    // 0x9f7574: ldur            lr, [fp, #-0x18]
    // 0x9f7578: stp             lr, x16, [SP, #0x10]
    // 0x9f757c: ldur            x16, [fp, #-0x20]
    // 0x9f7580: stp             x2, x16, [SP]
    // 0x9f7584: mov             x1, x0
    // 0x9f7588: r4 = const [0, 0x17, 0x16, 0x1, alignment, 0x15, animationDuration, 0x13, backgroundColor, 0x2, elevation, 0x7, enableFeedback, 0x14, fixedSize, 0xa, foregroundColor, 0x3, iconColor, 0xc, iconSize, 0xd, maximumSize, 0xb, minimumSize, 0x9, mouseCursor, 0x10, overlayColor, 0x4, padding, 0x8, shadowColor, 0x5, shape, 0xf, side, 0xe, splashFactory, 0x16, surfaceTintColor, 0x6, tapTargetSize, 0x12, textStyle, 0x1, visualDensity, 0x11, null]
    //     0x9f7588: add             x4, PP, #0x54, lsl #12  ; [pp+0x54e40] List(49) [0, 0x17, 0x16, 0x1, "alignment", 0x15, "animationDuration", 0x13, "backgroundColor", 0x2, "elevation", 0x7, "enableFeedback", 0x14, "fixedSize", 0xa, "foregroundColor", 0x3, "iconColor", 0xc, "iconSize", 0xd, "maximumSize", 0xb, "minimumSize", 0x9, "mouseCursor", 0x10, "overlayColor", 0x4, "padding", 0x8, "shadowColor", 0x5, "shape", 0xf, "side", 0xe, "splashFactory", 0x16, "surfaceTintColor", 0x6, "tapTargetSize", 0x12, "textStyle", 0x1, "visualDensity", 0x11, Null]
    //     0x9f758c: ldr             x4, [x4, #0xe40]
    // 0x9f7590: r0 = copyWith()
    //     0x9f7590: bl              #0x9e7350  ; [package:flutter/src/material/button_style.dart] ButtonStyle::copyWith
    // 0x9f7594: LeaveFrame
    //     0x9f7594: mov             SP, fp
    //     0x9f7598: ldp             fp, lr, [SP], #0x10
    // 0x9f759c: ret
    //     0x9f759c: ret             
    // 0x9f75a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f75a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f75a4: b               #0x9f7308
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbe2c7c, size: 0x5e4
    // 0xbe2c7c: EnterFrame
    //     0xbe2c7c: stp             fp, lr, [SP, #-0x10]!
    //     0xbe2c80: mov             fp, SP
    // 0xbe2c84: AllocStack(0x10)
    //     0xbe2c84: sub             SP, SP, #0x10
    // 0xbe2c88: CheckStackOverflow
    //     0xbe2c88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe2c8c: cmp             SP, x16
    //     0xbe2c90: b.ls            #0xbe3258
    // 0xbe2c94: ldr             x2, [fp, #0x10]
    // 0xbe2c98: r0 = LoadClassIdInstr(r2)
    //     0xbe2c98: ldur            x0, [x2, #-1]
    //     0xbe2c9c: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2ca0: mov             x1, x2
    // 0xbe2ca4: r0 = GDT[cid_x0 + -0xcdd]()
    //     0xbe2ca4: sub             lr, x0, #0xcdd
    //     0xbe2ca8: ldr             lr, [x21, lr, lsl #3]
    //     0xbe2cac: blr             lr
    // 0xbe2cb0: r1 = <Object?>
    //     0xbe2cb0: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbe2cb4: r2 = 48
    //     0xbe2cb4: movz            x2, #0x30
    // 0xbe2cb8: stur            x0, [fp, #-8]
    // 0xbe2cbc: r0 = AllocateArray()
    //     0xbe2cbc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbe2cc0: mov             x2, x0
    // 0xbe2cc4: ldur            x0, [fp, #-8]
    // 0xbe2cc8: stur            x2, [fp, #-0x10]
    // 0xbe2ccc: StoreField: r2->field_f = r0
    //     0xbe2ccc: stur            w0, [x2, #0xf]
    // 0xbe2cd0: ldr             x3, [fp, #0x10]
    // 0xbe2cd4: r0 = LoadClassIdInstr(r3)
    //     0xbe2cd4: ldur            x0, [x3, #-1]
    //     0xbe2cd8: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2cdc: mov             x1, x3
    // 0xbe2ce0: r0 = GDT[cid_x0 + 0xda8]()
    //     0xbe2ce0: add             lr, x0, #0xda8
    //     0xbe2ce4: ldr             lr, [x21, lr, lsl #3]
    //     0xbe2ce8: blr             lr
    // 0xbe2cec: ldur            x1, [fp, #-0x10]
    // 0xbe2cf0: ArrayStore: r1[1] = r0  ; List_4
    //     0xbe2cf0: add             x25, x1, #0x13
    //     0xbe2cf4: str             w0, [x25]
    //     0xbe2cf8: tbz             w0, #0, #0xbe2d14
    //     0xbe2cfc: ldurb           w16, [x1, #-1]
    //     0xbe2d00: ldurb           w17, [x0, #-1]
    //     0xbe2d04: and             x16, x17, x16, lsr #2
    //     0xbe2d08: tst             x16, HEAP, lsr #32
    //     0xbe2d0c: b.eq            #0xbe2d14
    //     0xbe2d10: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe2d14: ldr             x2, [fp, #0x10]
    // 0xbe2d18: r0 = LoadClassIdInstr(r2)
    //     0xbe2d18: ldur            x0, [x2, #-1]
    //     0xbe2d1c: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2d20: mov             x1, x2
    // 0xbe2d24: r0 = GDT[cid_x0 + 0xf01]()
    //     0xbe2d24: add             lr, x0, #0xf01
    //     0xbe2d28: ldr             lr, [x21, lr, lsl #3]
    //     0xbe2d2c: blr             lr
    // 0xbe2d30: ldur            x1, [fp, #-0x10]
    // 0xbe2d34: ArrayStore: r1[2] = r0  ; List_4
    //     0xbe2d34: add             x25, x1, #0x17
    //     0xbe2d38: str             w0, [x25]
    //     0xbe2d3c: tbz             w0, #0, #0xbe2d58
    //     0xbe2d40: ldurb           w16, [x1, #-1]
    //     0xbe2d44: ldurb           w17, [x0, #-1]
    //     0xbe2d48: and             x16, x17, x16, lsr #2
    //     0xbe2d4c: tst             x16, HEAP, lsr #32
    //     0xbe2d50: b.eq            #0xbe2d58
    //     0xbe2d54: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe2d58: ldr             x2, [fp, #0x10]
    // 0xbe2d5c: r0 = LoadClassIdInstr(r2)
    //     0xbe2d5c: ldur            x0, [x2, #-1]
    //     0xbe2d60: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2d64: mov             x1, x2
    // 0xbe2d68: r0 = GDT[cid_x0 + 0xf50]()
    //     0xbe2d68: add             lr, x0, #0xf50
    //     0xbe2d6c: ldr             lr, [x21, lr, lsl #3]
    //     0xbe2d70: blr             lr
    // 0xbe2d74: ldur            x1, [fp, #-0x10]
    // 0xbe2d78: ArrayStore: r1[3] = r0  ; List_4
    //     0xbe2d78: add             x25, x1, #0x1b
    //     0xbe2d7c: str             w0, [x25]
    //     0xbe2d80: tbz             w0, #0, #0xbe2d9c
    //     0xbe2d84: ldurb           w16, [x1, #-1]
    //     0xbe2d88: ldurb           w17, [x0, #-1]
    //     0xbe2d8c: and             x16, x17, x16, lsr #2
    //     0xbe2d90: tst             x16, HEAP, lsr #32
    //     0xbe2d94: b.eq            #0xbe2d9c
    //     0xbe2d98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe2d9c: ldr             x2, [fp, #0x10]
    // 0xbe2da0: r0 = LoadClassIdInstr(r2)
    //     0xbe2da0: ldur            x0, [x2, #-1]
    //     0xbe2da4: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2da8: mov             x1, x2
    // 0xbe2dac: r0 = GDT[cid_x0 + 0xf29]()
    //     0xbe2dac: add             lr, x0, #0xf29
    //     0xbe2db0: ldr             lr, [x21, lr, lsl #3]
    //     0xbe2db4: blr             lr
    // 0xbe2db8: ldur            x1, [fp, #-0x10]
    // 0xbe2dbc: ArrayStore: r1[4] = r0  ; List_4
    //     0xbe2dbc: add             x25, x1, #0x1f
    //     0xbe2dc0: str             w0, [x25]
    //     0xbe2dc4: tbz             w0, #0, #0xbe2de0
    //     0xbe2dc8: ldurb           w16, [x1, #-1]
    //     0xbe2dcc: ldurb           w17, [x0, #-1]
    //     0xbe2dd0: and             x16, x17, x16, lsr #2
    //     0xbe2dd4: tst             x16, HEAP, lsr #32
    //     0xbe2dd8: b.eq            #0xbe2de0
    //     0xbe2ddc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe2de0: ldr             x2, [fp, #0x10]
    // 0xbe2de4: r0 = LoadClassIdInstr(r2)
    //     0xbe2de4: ldur            x0, [x2, #-1]
    //     0xbe2de8: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2dec: mov             x1, x2
    // 0xbe2df0: r0 = GDT[cid_x0 + 0x936]()
    //     0xbe2df0: add             lr, x0, #0x936
    //     0xbe2df4: ldr             lr, [x21, lr, lsl #3]
    //     0xbe2df8: blr             lr
    // 0xbe2dfc: ldur            x1, [fp, #-0x10]
    // 0xbe2e00: ArrayStore: r1[5] = r0  ; List_4
    //     0xbe2e00: add             x25, x1, #0x23
    //     0xbe2e04: str             w0, [x25]
    //     0xbe2e08: tbz             w0, #0, #0xbe2e24
    //     0xbe2e0c: ldurb           w16, [x1, #-1]
    //     0xbe2e10: ldurb           w17, [x0, #-1]
    //     0xbe2e14: and             x16, x17, x16, lsr #2
    //     0xbe2e18: tst             x16, HEAP, lsr #32
    //     0xbe2e1c: b.eq            #0xbe2e24
    //     0xbe2e20: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe2e24: ldr             x2, [fp, #0x10]
    // 0xbe2e28: r0 = LoadClassIdInstr(r2)
    //     0xbe2e28: ldur            x0, [x2, #-1]
    //     0xbe2e2c: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2e30: mov             x1, x2
    // 0xbe2e34: r0 = GDT[cid_x0 + 0xef2]()
    //     0xbe2e34: add             lr, x0, #0xef2
    //     0xbe2e38: ldr             lr, [x21, lr, lsl #3]
    //     0xbe2e3c: blr             lr
    // 0xbe2e40: ldur            x1, [fp, #-0x10]
    // 0xbe2e44: ArrayStore: r1[6] = r0  ; List_4
    //     0xbe2e44: add             x25, x1, #0x27
    //     0xbe2e48: str             w0, [x25]
    //     0xbe2e4c: tbz             w0, #0, #0xbe2e68
    //     0xbe2e50: ldurb           w16, [x1, #-1]
    //     0xbe2e54: ldurb           w17, [x0, #-1]
    //     0xbe2e58: and             x16, x17, x16, lsr #2
    //     0xbe2e5c: tst             x16, HEAP, lsr #32
    //     0xbe2e60: b.eq            #0xbe2e68
    //     0xbe2e64: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe2e68: ldr             x2, [fp, #0x10]
    // 0xbe2e6c: r0 = LoadClassIdInstr(r2)
    //     0xbe2e6c: ldur            x0, [x2, #-1]
    //     0xbe2e70: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2e74: mov             x1, x2
    // 0xbe2e78: r0 = GDT[cid_x0 + 0xfd4]()
    //     0xbe2e78: add             lr, x0, #0xfd4
    //     0xbe2e7c: ldr             lr, [x21, lr, lsl #3]
    //     0xbe2e80: blr             lr
    // 0xbe2e84: ldur            x1, [fp, #-0x10]
    // 0xbe2e88: ArrayStore: r1[7] = r0  ; List_4
    //     0xbe2e88: add             x25, x1, #0x2b
    //     0xbe2e8c: str             w0, [x25]
    //     0xbe2e90: tbz             w0, #0, #0xbe2eac
    //     0xbe2e94: ldurb           w16, [x1, #-1]
    //     0xbe2e98: ldurb           w17, [x0, #-1]
    //     0xbe2e9c: and             x16, x17, x16, lsr #2
    //     0xbe2ea0: tst             x16, HEAP, lsr #32
    //     0xbe2ea4: b.eq            #0xbe2eac
    //     0xbe2ea8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe2eac: ldr             x2, [fp, #0x10]
    // 0xbe2eb0: r0 = LoadClassIdInstr(r2)
    //     0xbe2eb0: ldur            x0, [x2, #-1]
    //     0xbe2eb4: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2eb8: mov             x1, x2
    // 0xbe2ebc: r0 = GDT[cid_x0 + 0xdc0]()
    //     0xbe2ebc: add             lr, x0, #0xdc0
    //     0xbe2ec0: ldr             lr, [x21, lr, lsl #3]
    //     0xbe2ec4: blr             lr
    // 0xbe2ec8: ldur            x1, [fp, #-0x10]
    // 0xbe2ecc: ArrayStore: r1[8] = r0  ; List_4
    //     0xbe2ecc: add             x25, x1, #0x2f
    //     0xbe2ed0: str             w0, [x25]
    //     0xbe2ed4: tbz             w0, #0, #0xbe2ef0
    //     0xbe2ed8: ldurb           w16, [x1, #-1]
    //     0xbe2edc: ldurb           w17, [x0, #-1]
    //     0xbe2ee0: and             x16, x17, x16, lsr #2
    //     0xbe2ee4: tst             x16, HEAP, lsr #32
    //     0xbe2ee8: b.eq            #0xbe2ef0
    //     0xbe2eec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe2ef0: ldr             x2, [fp, #0x10]
    // 0xbe2ef4: LoadField: r0 = r2->field_2b
    //     0xbe2ef4: ldur            w0, [x2, #0x2b]
    // 0xbe2ef8: DecompressPointer r0
    //     0xbe2ef8: add             x0, x0, HEAP, lsl #32
    // 0xbe2efc: ldur            x1, [fp, #-0x10]
    // 0xbe2f00: ArrayStore: r1[9] = r0  ; List_4
    //     0xbe2f00: add             x25, x1, #0x33
    //     0xbe2f04: str             w0, [x25]
    //     0xbe2f08: tbz             w0, #0, #0xbe2f24
    //     0xbe2f0c: ldurb           w16, [x1, #-1]
    //     0xbe2f10: ldurb           w17, [x0, #-1]
    //     0xbe2f14: and             x16, x17, x16, lsr #2
    //     0xbe2f18: tst             x16, HEAP, lsr #32
    //     0xbe2f1c: b.eq            #0xbe2f24
    //     0xbe2f20: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe2f24: r0 = LoadClassIdInstr(r2)
    //     0xbe2f24: ldur            x0, [x2, #-1]
    //     0xbe2f28: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2f2c: mov             x1, x2
    // 0xbe2f30: r0 = GDT[cid_x0 + 0xe8d]()
    //     0xbe2f30: add             lr, x0, #0xe8d
    //     0xbe2f34: ldr             lr, [x21, lr, lsl #3]
    //     0xbe2f38: blr             lr
    // 0xbe2f3c: ldur            x1, [fp, #-0x10]
    // 0xbe2f40: ArrayStore: r1[10] = r0  ; List_4
    //     0xbe2f40: add             x25, x1, #0x37
    //     0xbe2f44: str             w0, [x25]
    //     0xbe2f48: tbz             w0, #0, #0xbe2f64
    //     0xbe2f4c: ldurb           w16, [x1, #-1]
    //     0xbe2f50: ldurb           w17, [x0, #-1]
    //     0xbe2f54: and             x16, x17, x16, lsr #2
    //     0xbe2f58: tst             x16, HEAP, lsr #32
    //     0xbe2f5c: b.eq            #0xbe2f64
    //     0xbe2f60: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe2f64: ldr             x2, [fp, #0x10]
    // 0xbe2f68: r0 = LoadClassIdInstr(r2)
    //     0xbe2f68: ldur            x0, [x2, #-1]
    //     0xbe2f6c: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2f70: mov             x1, x2
    // 0xbe2f74: r0 = GDT[cid_x0 + 0xdcc]()
    //     0xbe2f74: add             lr, x0, #0xdcc
    //     0xbe2f78: ldr             lr, [x21, lr, lsl #3]
    //     0xbe2f7c: blr             lr
    // 0xbe2f80: ldur            x1, [fp, #-0x10]
    // 0xbe2f84: ArrayStore: r1[11] = r0  ; List_4
    //     0xbe2f84: add             x25, x1, #0x3b
    //     0xbe2f88: str             w0, [x25]
    //     0xbe2f8c: tbz             w0, #0, #0xbe2fa8
    //     0xbe2f90: ldurb           w16, [x1, #-1]
    //     0xbe2f94: ldurb           w17, [x0, #-1]
    //     0xbe2f98: and             x16, x17, x16, lsr #2
    //     0xbe2f9c: tst             x16, HEAP, lsr #32
    //     0xbe2fa0: b.eq            #0xbe2fa8
    //     0xbe2fa4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe2fa8: ldr             x2, [fp, #0x10]
    // 0xbe2fac: r0 = LoadClassIdInstr(r2)
    //     0xbe2fac: ldur            x0, [x2, #-1]
    //     0xbe2fb0: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2fb4: mov             x1, x2
    // 0xbe2fb8: r0 = GDT[cid_x0 + 0xe9c]()
    //     0xbe2fb8: add             lr, x0, #0xe9c
    //     0xbe2fbc: ldr             lr, [x21, lr, lsl #3]
    //     0xbe2fc0: blr             lr
    // 0xbe2fc4: ldur            x1, [fp, #-0x10]
    // 0xbe2fc8: ArrayStore: r1[12] = r0  ; List_4
    //     0xbe2fc8: add             x25, x1, #0x3f
    //     0xbe2fcc: str             w0, [x25]
    //     0xbe2fd0: tbz             w0, #0, #0xbe2fec
    //     0xbe2fd4: ldurb           w16, [x1, #-1]
    //     0xbe2fd8: ldurb           w17, [x0, #-1]
    //     0xbe2fdc: and             x16, x17, x16, lsr #2
    //     0xbe2fe0: tst             x16, HEAP, lsr #32
    //     0xbe2fe4: b.eq            #0xbe2fec
    //     0xbe2fe8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe2fec: ldr             x2, [fp, #0x10]
    // 0xbe2ff0: r0 = LoadClassIdInstr(r2)
    //     0xbe2ff0: ldur            x0, [x2, #-1]
    //     0xbe2ff4: ubfx            x0, x0, #0xc, #0x14
    // 0xbe2ff8: mov             x1, x2
    // 0xbe2ffc: r0 = GDT[cid_x0 + 0xeb5]()
    //     0xbe2ffc: add             lr, x0, #0xeb5
    //     0xbe3000: ldr             lr, [x21, lr, lsl #3]
    //     0xbe3004: blr             lr
    // 0xbe3008: ldur            x1, [fp, #-0x10]
    // 0xbe300c: ArrayStore: r1[13] = r0  ; List_4
    //     0xbe300c: add             x25, x1, #0x43
    //     0xbe3010: str             w0, [x25]
    //     0xbe3014: tbz             w0, #0, #0xbe3030
    //     0xbe3018: ldurb           w16, [x1, #-1]
    //     0xbe301c: ldurb           w17, [x0, #-1]
    //     0xbe3020: and             x16, x17, x16, lsr #2
    //     0xbe3024: tst             x16, HEAP, lsr #32
    //     0xbe3028: b.eq            #0xbe3030
    //     0xbe302c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe3030: ldr             x2, [fp, #0x10]
    // 0xbe3034: r0 = LoadClassIdInstr(r2)
    //     0xbe3034: ldur            x0, [x2, #-1]
    //     0xbe3038: ubfx            x0, x0, #0xc, #0x14
    // 0xbe303c: mov             x1, x2
    // 0xbe3040: r0 = GDT[cid_x0 + 0xece]()
    //     0xbe3040: add             lr, x0, #0xece
    //     0xbe3044: ldr             lr, [x21, lr, lsl #3]
    //     0xbe3048: blr             lr
    // 0xbe304c: ldur            x1, [fp, #-0x10]
    // 0xbe3050: ArrayStore: r1[14] = r0  ; List_4
    //     0xbe3050: add             x25, x1, #0x47
    //     0xbe3054: str             w0, [x25]
    //     0xbe3058: tbz             w0, #0, #0xbe3074
    //     0xbe305c: ldurb           w16, [x1, #-1]
    //     0xbe3060: ldurb           w17, [x0, #-1]
    //     0xbe3064: and             x16, x17, x16, lsr #2
    //     0xbe3068: tst             x16, HEAP, lsr #32
    //     0xbe306c: b.eq            #0xbe3074
    //     0xbe3070: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe3074: ldr             x2, [fp, #0x10]
    // 0xbe3078: r0 = LoadClassIdInstr(r2)
    //     0xbe3078: ldur            x0, [x2, #-1]
    //     0xbe307c: ubfx            x0, x0, #0xc, #0x14
    // 0xbe3080: mov             x1, x2
    // 0xbe3084: r0 = GDT[cid_x0 + 0xf0e]()
    //     0xbe3084: add             lr, x0, #0xf0e
    //     0xbe3088: ldr             lr, [x21, lr, lsl #3]
    //     0xbe308c: blr             lr
    // 0xbe3090: ldur            x1, [fp, #-0x10]
    // 0xbe3094: ArrayStore: r1[15] = r0  ; List_4
    //     0xbe3094: add             x25, x1, #0x4b
    //     0xbe3098: str             w0, [x25]
    //     0xbe309c: tbz             w0, #0, #0xbe30b8
    //     0xbe30a0: ldurb           w16, [x1, #-1]
    //     0xbe30a4: ldurb           w17, [x0, #-1]
    //     0xbe30a8: and             x16, x17, x16, lsr #2
    //     0xbe30ac: tst             x16, HEAP, lsr #32
    //     0xbe30b0: b.eq            #0xbe30b8
    //     0xbe30b4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe30b8: ldr             x2, [fp, #0x10]
    // 0xbe30bc: r0 = LoadClassIdInstr(r2)
    //     0xbe30bc: ldur            x0, [x2, #-1]
    //     0xbe30c0: ubfx            x0, x0, #0xc, #0x14
    // 0xbe30c4: mov             x1, x2
    // 0xbe30c8: r0 = GDT[cid_x0 + 0xf66]()
    //     0xbe30c8: add             lr, x0, #0xf66
    //     0xbe30cc: ldr             lr, [x21, lr, lsl #3]
    //     0xbe30d0: blr             lr
    // 0xbe30d4: ldur            x1, [fp, #-0x10]
    // 0xbe30d8: ArrayStore: r1[16] = r0  ; List_4
    //     0xbe30d8: add             x25, x1, #0x4f
    //     0xbe30dc: str             w0, [x25]
    //     0xbe30e0: tbz             w0, #0, #0xbe30fc
    //     0xbe30e4: ldurb           w16, [x1, #-1]
    //     0xbe30e8: ldurb           w17, [x0, #-1]
    //     0xbe30ec: and             x16, x17, x16, lsr #2
    //     0xbe30f0: tst             x16, HEAP, lsr #32
    //     0xbe30f4: b.eq            #0xbe30fc
    //     0xbe30f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe30fc: ldr             x2, [fp, #0x10]
    // 0xbe3100: r0 = LoadClassIdInstr(r2)
    //     0xbe3100: ldur            x0, [x2, #-1]
    //     0xbe3104: ubfx            x0, x0, #0xc, #0x14
    // 0xbe3108: mov             x1, x2
    // 0xbe310c: r0 = GDT[cid_x0 + 0xf82]()
    //     0xbe310c: add             lr, x0, #0xf82
    //     0xbe3110: ldr             lr, [x21, lr, lsl #3]
    //     0xbe3114: blr             lr
    // 0xbe3118: ldur            x1, [fp, #-0x10]
    // 0xbe311c: ArrayStore: r1[17] = r0  ; List_4
    //     0xbe311c: add             x25, x1, #0x53
    //     0xbe3120: str             w0, [x25]
    //     0xbe3124: tbz             w0, #0, #0xbe3140
    //     0xbe3128: ldurb           w16, [x1, #-1]
    //     0xbe312c: ldurb           w17, [x0, #-1]
    //     0xbe3130: and             x16, x17, x16, lsr #2
    //     0xbe3134: tst             x16, HEAP, lsr #32
    //     0xbe3138: b.eq            #0xbe3140
    //     0xbe313c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe3140: ldr             x2, [fp, #0x10]
    // 0xbe3144: LoadField: r0 = r2->field_4f
    //     0xbe3144: ldur            w0, [x2, #0x4f]
    // 0xbe3148: DecompressPointer r0
    //     0xbe3148: add             x0, x0, HEAP, lsl #32
    // 0xbe314c: ldur            x1, [fp, #-0x10]
    // 0xbe3150: ArrayStore: r1[18] = r0  ; List_4
    //     0xbe3150: add             x25, x1, #0x57
    //     0xbe3154: str             w0, [x25]
    //     0xbe3158: tbz             w0, #0, #0xbe3174
    //     0xbe315c: ldurb           w16, [x1, #-1]
    //     0xbe3160: ldurb           w17, [x0, #-1]
    //     0xbe3164: and             x16, x17, x16, lsr #2
    //     0xbe3168: tst             x16, HEAP, lsr #32
    //     0xbe316c: b.eq            #0xbe3174
    //     0xbe3170: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe3174: LoadField: r0 = r2->field_53
    //     0xbe3174: ldur            w0, [x2, #0x53]
    // 0xbe3178: DecompressPointer r0
    //     0xbe3178: add             x0, x0, HEAP, lsl #32
    // 0xbe317c: ldur            x3, [fp, #-0x10]
    // 0xbe3180: StoreField: r3->field_5b = r0
    //     0xbe3180: stur            w0, [x3, #0x5b]
    // 0xbe3184: LoadField: r0 = r2->field_57
    //     0xbe3184: ldur            w0, [x2, #0x57]
    // 0xbe3188: DecompressPointer r0
    //     0xbe3188: add             x0, x0, HEAP, lsl #32
    // 0xbe318c: mov             x1, x3
    // 0xbe3190: ArrayStore: r1[20] = r0  ; List_4
    //     0xbe3190: add             x25, x1, #0x5f
    //     0xbe3194: str             w0, [x25]
    //     0xbe3198: tbz             w0, #0, #0xbe31b4
    //     0xbe319c: ldurb           w16, [x1, #-1]
    //     0xbe31a0: ldurb           w17, [x0, #-1]
    //     0xbe31a4: and             x16, x17, x16, lsr #2
    //     0xbe31a8: tst             x16, HEAP, lsr #32
    //     0xbe31ac: b.eq            #0xbe31b4
    //     0xbe31b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe31b4: r0 = LoadClassIdInstr(r2)
    //     0xbe31b4: ldur            x0, [x2, #-1]
    //     0xbe31b8: ubfx            x0, x0, #0xc, #0x14
    // 0xbe31bc: mov             x1, x2
    // 0xbe31c0: r0 = GDT[cid_x0 + 0x9f4]()
    //     0xbe31c0: add             lr, x0, #0x9f4
    //     0xbe31c4: ldr             lr, [x21, lr, lsl #3]
    //     0xbe31c8: blr             lr
    // 0xbe31cc: ldur            x1, [fp, #-0x10]
    // 0xbe31d0: ArrayStore: r1[21] = r0  ; List_4
    //     0xbe31d0: add             x25, x1, #0x63
    //     0xbe31d4: str             w0, [x25]
    //     0xbe31d8: tbz             w0, #0, #0xbe31f4
    //     0xbe31dc: ldurb           w16, [x1, #-1]
    //     0xbe31e0: ldurb           w17, [x0, #-1]
    //     0xbe31e4: and             x16, x17, x16, lsr #2
    //     0xbe31e8: tst             x16, HEAP, lsr #32
    //     0xbe31ec: b.eq            #0xbe31f4
    //     0xbe31f0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbe31f4: ldr             x0, [fp, #0x10]
    // 0xbe31f8: LoadField: r1 = r0->field_5f
    //     0xbe31f8: ldur            w1, [x0, #0x5f]
    // 0xbe31fc: DecompressPointer r1
    //     0xbe31fc: add             x1, x1, HEAP, lsl #32
    // 0xbe3200: ldur            x2, [fp, #-0x10]
    // 0xbe3204: StoreField: r2->field_67 = r1
    //     0xbe3204: stur            w1, [x2, #0x67]
    // 0xbe3208: LoadField: r1 = r0->field_63
    //     0xbe3208: ldur            w1, [x0, #0x63]
    // 0xbe320c: DecompressPointer r1
    //     0xbe320c: add             x1, x1, HEAP, lsl #32
    // 0xbe3210: StoreField: r2->field_6b = r1
    //     0xbe3210: stur            w1, [x2, #0x6b]
    // 0xbe3214: r1 = <Object?>
    //     0xbe3214: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbe3218: r0 = AllocateGrowableArray()
    //     0xbe3218: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbe321c: mov             x1, x0
    // 0xbe3220: ldur            x0, [fp, #-0x10]
    // 0xbe3224: StoreField: r1->field_f = r0
    //     0xbe3224: stur            w0, [x1, #0xf]
    // 0xbe3228: r0 = 48
    //     0xbe3228: movz            x0, #0x30
    // 0xbe322c: StoreField: r1->field_b = r0
    //     0xbe322c: stur            w0, [x1, #0xb]
    // 0xbe3230: r0 = hashAll()
    //     0xbe3230: bl              #0xbdcf60  ; [dart:core] Object::hashAll
    // 0xbe3234: mov             x2, x0
    // 0xbe3238: r0 = BoxInt64Instr(r2)
    //     0xbe3238: sbfiz           x0, x2, #1, #0x1f
    //     0xbe323c: cmp             x2, x0, asr #1
    //     0xbe3240: b.eq            #0xbe324c
    //     0xbe3244: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe3248: stur            x2, [x0, #7]
    // 0xbe324c: LeaveFrame
    //     0xbe324c: mov             SP, fp
    //     0xbe3250: ldp             fp, lr, [SP], #0x10
    // 0xbe3254: ret
    //     0xbe3254: ret             
    // 0xbe3258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe3258: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe325c: b               #0xbe2c94
  }
  const get _ visualDensity(/* No info */) {
    // ** addr: 0xd195d8, size: 0xc
    // 0xd195d8: LoadField: r0 = r1->field_47
    //     0xd195d8: ldur            w0, [x1, #0x47]
    // 0xd195dc: DecompressPointer r0
    //     0xd195dc: add             x0, x0, HEAP, lsl #32
    // 0xd195e0: ret
    //     0xd195e0: ret             
  }
  const get _ splashFactory(/* No info */) {
    // ** addr: 0xd2ffe8, size: 0xc
    // 0xd2ffe8: LoadField: r0 = r1->field_5b
    //     0xd2ffe8: ldur            w0, [x1, #0x5b]
    // 0xd2ffec: DecompressPointer r0
    //     0xd2ffec: add             x0, x0, HEAP, lsl #32
    // 0xd2fff0: ret
    //     0xd2fff0: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd4d018, size: 0x8e0
    // 0xd4d018: EnterFrame
    //     0xd4d018: stp             fp, lr, [SP, #-0x10]!
    //     0xd4d01c: mov             fp, SP
    // 0xd4d020: AllocStack(0x18)
    //     0xd4d020: sub             SP, SP, #0x18
    // 0xd4d024: CheckStackOverflow
    //     0xd4d024: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd4d028: cmp             SP, x16
    //     0xd4d02c: b.ls            #0xd4d8f0
    // 0xd4d030: ldr             x1, [fp, #0x10]
    // 0xd4d034: cmp             w1, NULL
    // 0xd4d038: b.ne            #0xd4d04c
    // 0xd4d03c: r0 = false
    //     0xd4d03c: add             x0, NULL, #0x30  ; false
    // 0xd4d040: LeaveFrame
    //     0xd4d040: mov             SP, fp
    //     0xd4d044: ldp             fp, lr, [SP], #0x10
    // 0xd4d048: ret
    //     0xd4d048: ret             
    // 0xd4d04c: ldr             x0, [fp, #0x18]
    // 0xd4d050: cmp             w0, w1
    // 0xd4d054: b.ne            #0xd4d068
    // 0xd4d058: r0 = true
    //     0xd4d058: add             x0, NULL, #0x20  ; true
    // 0xd4d05c: LeaveFrame
    //     0xd4d05c: mov             SP, fp
    //     0xd4d060: ldp             fp, lr, [SP], #0x10
    // 0xd4d064: ret
    //     0xd4d064: ret             
    // 0xd4d068: stp             x0, x1, [SP]
    // 0xd4d06c: r0 = _haveSameRuntimeType()
    //     0xd4d06c: bl              #0x6c18d0  ; [dart:core] Object::_haveSameRuntimeType
    // 0xd4d070: tbz             w0, #4, #0xd4d084
    // 0xd4d074: r0 = false
    //     0xd4d074: add             x0, NULL, #0x30  ; false
    // 0xd4d078: LeaveFrame
    //     0xd4d078: mov             SP, fp
    //     0xd4d07c: ldp             fp, lr, [SP], #0x10
    // 0xd4d080: ret
    //     0xd4d080: ret             
    // 0xd4d084: ldr             x2, [fp, #0x10]
    // 0xd4d088: r0 = 60
    //     0xd4d088: movz            x0, #0x3c
    // 0xd4d08c: branchIfSmi(r2, 0xd4d098)
    //     0xd4d08c: tbz             w2, #0, #0xd4d098
    // 0xd4d090: r0 = LoadClassIdInstr(r2)
    //     0xd4d090: ldur            x0, [x2, #-1]
    //     0xd4d094: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d098: sub             x16, x0, #0xf91
    // 0xd4d09c: cmp             x16, #7
    // 0xd4d0a0: b.hi            #0xd4d8e0
    // 0xd4d0a4: ldr             x3, [fp, #0x18]
    // 0xd4d0a8: r0 = LoadClassIdInstr(r2)
    //     0xd4d0a8: ldur            x0, [x2, #-1]
    //     0xd4d0ac: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d0b0: mov             x1, x2
    // 0xd4d0b4: r0 = GDT[cid_x0 + -0xcdd]()
    //     0xd4d0b4: sub             lr, x0, #0xcdd
    //     0xd4d0b8: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d0bc: blr             lr
    // 0xd4d0c0: mov             x3, x0
    // 0xd4d0c4: ldr             x2, [fp, #0x18]
    // 0xd4d0c8: stur            x3, [fp, #-8]
    // 0xd4d0cc: r0 = LoadClassIdInstr(r2)
    //     0xd4d0cc: ldur            x0, [x2, #-1]
    //     0xd4d0d0: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d0d4: mov             x1, x2
    // 0xd4d0d8: r0 = GDT[cid_x0 + -0xcdd]()
    //     0xd4d0d8: sub             lr, x0, #0xcdd
    //     0xd4d0dc: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d0e0: blr             lr
    // 0xd4d0e4: mov             x1, x0
    // 0xd4d0e8: ldur            x0, [fp, #-8]
    // 0xd4d0ec: r2 = LoadClassIdInstr(r0)
    //     0xd4d0ec: ldur            x2, [x0, #-1]
    //     0xd4d0f0: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d0f4: stp             x1, x0, [SP]
    // 0xd4d0f8: mov             x0, x2
    // 0xd4d0fc: mov             lr, x0
    // 0xd4d100: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d104: blr             lr
    // 0xd4d108: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d10c: ldr             x2, [fp, #0x18]
    // 0xd4d110: ldr             x3, [fp, #0x10]
    // 0xd4d114: r0 = LoadClassIdInstr(r3)
    //     0xd4d114: ldur            x0, [x3, #-1]
    //     0xd4d118: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d11c: mov             x1, x3
    // 0xd4d120: r0 = GDT[cid_x0 + 0xda8]()
    //     0xd4d120: add             lr, x0, #0xda8
    //     0xd4d124: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d128: blr             lr
    // 0xd4d12c: mov             x3, x0
    // 0xd4d130: ldr             x2, [fp, #0x18]
    // 0xd4d134: stur            x3, [fp, #-8]
    // 0xd4d138: r0 = LoadClassIdInstr(r2)
    //     0xd4d138: ldur            x0, [x2, #-1]
    //     0xd4d13c: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d140: mov             x1, x2
    // 0xd4d144: r0 = GDT[cid_x0 + 0xda8]()
    //     0xd4d144: add             lr, x0, #0xda8
    //     0xd4d148: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d14c: blr             lr
    // 0xd4d150: mov             x1, x0
    // 0xd4d154: ldur            x0, [fp, #-8]
    // 0xd4d158: r2 = LoadClassIdInstr(r0)
    //     0xd4d158: ldur            x2, [x0, #-1]
    //     0xd4d15c: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d160: stp             x1, x0, [SP]
    // 0xd4d164: mov             x0, x2
    // 0xd4d168: mov             lr, x0
    // 0xd4d16c: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d170: blr             lr
    // 0xd4d174: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d178: ldr             x2, [fp, #0x18]
    // 0xd4d17c: ldr             x3, [fp, #0x10]
    // 0xd4d180: r0 = LoadClassIdInstr(r3)
    //     0xd4d180: ldur            x0, [x3, #-1]
    //     0xd4d184: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d188: mov             x1, x3
    // 0xd4d18c: r0 = GDT[cid_x0 + 0xf01]()
    //     0xd4d18c: add             lr, x0, #0xf01
    //     0xd4d190: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d194: blr             lr
    // 0xd4d198: mov             x3, x0
    // 0xd4d19c: ldr             x2, [fp, #0x18]
    // 0xd4d1a0: stur            x3, [fp, #-8]
    // 0xd4d1a4: r0 = LoadClassIdInstr(r2)
    //     0xd4d1a4: ldur            x0, [x2, #-1]
    //     0xd4d1a8: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d1ac: mov             x1, x2
    // 0xd4d1b0: r0 = GDT[cid_x0 + 0xf01]()
    //     0xd4d1b0: add             lr, x0, #0xf01
    //     0xd4d1b4: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d1b8: blr             lr
    // 0xd4d1bc: mov             x1, x0
    // 0xd4d1c0: ldur            x0, [fp, #-8]
    // 0xd4d1c4: r2 = LoadClassIdInstr(r0)
    //     0xd4d1c4: ldur            x2, [x0, #-1]
    //     0xd4d1c8: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d1cc: stp             x1, x0, [SP]
    // 0xd4d1d0: mov             x0, x2
    // 0xd4d1d4: mov             lr, x0
    // 0xd4d1d8: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d1dc: blr             lr
    // 0xd4d1e0: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d1e4: ldr             x2, [fp, #0x18]
    // 0xd4d1e8: ldr             x3, [fp, #0x10]
    // 0xd4d1ec: r0 = LoadClassIdInstr(r3)
    //     0xd4d1ec: ldur            x0, [x3, #-1]
    //     0xd4d1f0: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d1f4: mov             x1, x3
    // 0xd4d1f8: r0 = GDT[cid_x0 + 0xf50]()
    //     0xd4d1f8: add             lr, x0, #0xf50
    //     0xd4d1fc: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d200: blr             lr
    // 0xd4d204: mov             x3, x0
    // 0xd4d208: ldr             x2, [fp, #0x18]
    // 0xd4d20c: stur            x3, [fp, #-8]
    // 0xd4d210: r0 = LoadClassIdInstr(r2)
    //     0xd4d210: ldur            x0, [x2, #-1]
    //     0xd4d214: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d218: mov             x1, x2
    // 0xd4d21c: r0 = GDT[cid_x0 + 0xf50]()
    //     0xd4d21c: add             lr, x0, #0xf50
    //     0xd4d220: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d224: blr             lr
    // 0xd4d228: mov             x1, x0
    // 0xd4d22c: ldur            x0, [fp, #-8]
    // 0xd4d230: r2 = LoadClassIdInstr(r0)
    //     0xd4d230: ldur            x2, [x0, #-1]
    //     0xd4d234: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d238: stp             x1, x0, [SP]
    // 0xd4d23c: mov             x0, x2
    // 0xd4d240: mov             lr, x0
    // 0xd4d244: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d248: blr             lr
    // 0xd4d24c: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d250: ldr             x2, [fp, #0x18]
    // 0xd4d254: ldr             x3, [fp, #0x10]
    // 0xd4d258: r0 = LoadClassIdInstr(r3)
    //     0xd4d258: ldur            x0, [x3, #-1]
    //     0xd4d25c: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d260: mov             x1, x3
    // 0xd4d264: r0 = GDT[cid_x0 + 0xf29]()
    //     0xd4d264: add             lr, x0, #0xf29
    //     0xd4d268: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d26c: blr             lr
    // 0xd4d270: mov             x3, x0
    // 0xd4d274: ldr             x2, [fp, #0x18]
    // 0xd4d278: stur            x3, [fp, #-8]
    // 0xd4d27c: r0 = LoadClassIdInstr(r2)
    //     0xd4d27c: ldur            x0, [x2, #-1]
    //     0xd4d280: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d284: mov             x1, x2
    // 0xd4d288: r0 = GDT[cid_x0 + 0xf29]()
    //     0xd4d288: add             lr, x0, #0xf29
    //     0xd4d28c: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d290: blr             lr
    // 0xd4d294: mov             x1, x0
    // 0xd4d298: ldur            x0, [fp, #-8]
    // 0xd4d29c: r2 = LoadClassIdInstr(r0)
    //     0xd4d29c: ldur            x2, [x0, #-1]
    //     0xd4d2a0: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d2a4: stp             x1, x0, [SP]
    // 0xd4d2a8: mov             x0, x2
    // 0xd4d2ac: mov             lr, x0
    // 0xd4d2b0: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d2b4: blr             lr
    // 0xd4d2b8: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d2bc: ldr             x2, [fp, #0x18]
    // 0xd4d2c0: ldr             x3, [fp, #0x10]
    // 0xd4d2c4: r0 = LoadClassIdInstr(r3)
    //     0xd4d2c4: ldur            x0, [x3, #-1]
    //     0xd4d2c8: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d2cc: mov             x1, x3
    // 0xd4d2d0: r0 = GDT[cid_x0 + 0x936]()
    //     0xd4d2d0: add             lr, x0, #0x936
    //     0xd4d2d4: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d2d8: blr             lr
    // 0xd4d2dc: mov             x3, x0
    // 0xd4d2e0: ldr             x2, [fp, #0x18]
    // 0xd4d2e4: stur            x3, [fp, #-8]
    // 0xd4d2e8: r0 = LoadClassIdInstr(r2)
    //     0xd4d2e8: ldur            x0, [x2, #-1]
    //     0xd4d2ec: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d2f0: mov             x1, x2
    // 0xd4d2f4: r0 = GDT[cid_x0 + 0x936]()
    //     0xd4d2f4: add             lr, x0, #0x936
    //     0xd4d2f8: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d2fc: blr             lr
    // 0xd4d300: mov             x1, x0
    // 0xd4d304: ldur            x0, [fp, #-8]
    // 0xd4d308: r2 = LoadClassIdInstr(r0)
    //     0xd4d308: ldur            x2, [x0, #-1]
    //     0xd4d30c: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d310: stp             x1, x0, [SP]
    // 0xd4d314: mov             x0, x2
    // 0xd4d318: mov             lr, x0
    // 0xd4d31c: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d320: blr             lr
    // 0xd4d324: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d328: ldr             x2, [fp, #0x18]
    // 0xd4d32c: ldr             x3, [fp, #0x10]
    // 0xd4d330: r0 = LoadClassIdInstr(r3)
    //     0xd4d330: ldur            x0, [x3, #-1]
    //     0xd4d334: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d338: mov             x1, x3
    // 0xd4d33c: r0 = GDT[cid_x0 + 0xef2]()
    //     0xd4d33c: add             lr, x0, #0xef2
    //     0xd4d340: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d344: blr             lr
    // 0xd4d348: mov             x3, x0
    // 0xd4d34c: ldr             x2, [fp, #0x18]
    // 0xd4d350: stur            x3, [fp, #-8]
    // 0xd4d354: r0 = LoadClassIdInstr(r2)
    //     0xd4d354: ldur            x0, [x2, #-1]
    //     0xd4d358: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d35c: mov             x1, x2
    // 0xd4d360: r0 = GDT[cid_x0 + 0xef2]()
    //     0xd4d360: add             lr, x0, #0xef2
    //     0xd4d364: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d368: blr             lr
    // 0xd4d36c: mov             x1, x0
    // 0xd4d370: ldur            x0, [fp, #-8]
    // 0xd4d374: r2 = LoadClassIdInstr(r0)
    //     0xd4d374: ldur            x2, [x0, #-1]
    //     0xd4d378: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d37c: stp             x1, x0, [SP]
    // 0xd4d380: mov             x0, x2
    // 0xd4d384: mov             lr, x0
    // 0xd4d388: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d38c: blr             lr
    // 0xd4d390: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d394: ldr             x2, [fp, #0x18]
    // 0xd4d398: ldr             x3, [fp, #0x10]
    // 0xd4d39c: r0 = LoadClassIdInstr(r3)
    //     0xd4d39c: ldur            x0, [x3, #-1]
    //     0xd4d3a0: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d3a4: mov             x1, x3
    // 0xd4d3a8: r0 = GDT[cid_x0 + 0xfd4]()
    //     0xd4d3a8: add             lr, x0, #0xfd4
    //     0xd4d3ac: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d3b0: blr             lr
    // 0xd4d3b4: mov             x3, x0
    // 0xd4d3b8: ldr             x2, [fp, #0x18]
    // 0xd4d3bc: stur            x3, [fp, #-8]
    // 0xd4d3c0: r0 = LoadClassIdInstr(r2)
    //     0xd4d3c0: ldur            x0, [x2, #-1]
    //     0xd4d3c4: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d3c8: mov             x1, x2
    // 0xd4d3cc: r0 = GDT[cid_x0 + 0xfd4]()
    //     0xd4d3cc: add             lr, x0, #0xfd4
    //     0xd4d3d0: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d3d4: blr             lr
    // 0xd4d3d8: mov             x1, x0
    // 0xd4d3dc: ldur            x0, [fp, #-8]
    // 0xd4d3e0: r2 = LoadClassIdInstr(r0)
    //     0xd4d3e0: ldur            x2, [x0, #-1]
    //     0xd4d3e4: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d3e8: stp             x1, x0, [SP]
    // 0xd4d3ec: mov             x0, x2
    // 0xd4d3f0: mov             lr, x0
    // 0xd4d3f4: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d3f8: blr             lr
    // 0xd4d3fc: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d400: ldr             x2, [fp, #0x18]
    // 0xd4d404: ldr             x3, [fp, #0x10]
    // 0xd4d408: r0 = LoadClassIdInstr(r3)
    //     0xd4d408: ldur            x0, [x3, #-1]
    //     0xd4d40c: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d410: mov             x1, x3
    // 0xd4d414: r0 = GDT[cid_x0 + 0xdc0]()
    //     0xd4d414: add             lr, x0, #0xdc0
    //     0xd4d418: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d41c: blr             lr
    // 0xd4d420: mov             x3, x0
    // 0xd4d424: ldr             x2, [fp, #0x18]
    // 0xd4d428: stur            x3, [fp, #-8]
    // 0xd4d42c: r0 = LoadClassIdInstr(r2)
    //     0xd4d42c: ldur            x0, [x2, #-1]
    //     0xd4d430: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d434: mov             x1, x2
    // 0xd4d438: r0 = GDT[cid_x0 + 0xdc0]()
    //     0xd4d438: add             lr, x0, #0xdc0
    //     0xd4d43c: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d440: blr             lr
    // 0xd4d444: mov             x1, x0
    // 0xd4d448: ldur            x0, [fp, #-8]
    // 0xd4d44c: r2 = LoadClassIdInstr(r0)
    //     0xd4d44c: ldur            x2, [x0, #-1]
    //     0xd4d450: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d454: stp             x1, x0, [SP]
    // 0xd4d458: mov             x0, x2
    // 0xd4d45c: mov             lr, x0
    // 0xd4d460: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d464: blr             lr
    // 0xd4d468: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d46c: ldr             x1, [fp, #0x18]
    // 0xd4d470: ldr             x2, [fp, #0x10]
    // 0xd4d474: LoadField: r0 = r2->field_2b
    //     0xd4d474: ldur            w0, [x2, #0x2b]
    // 0xd4d478: DecompressPointer r0
    //     0xd4d478: add             x0, x0, HEAP, lsl #32
    // 0xd4d47c: LoadField: r3 = r1->field_2b
    //     0xd4d47c: ldur            w3, [x1, #0x2b]
    // 0xd4d480: DecompressPointer r3
    //     0xd4d480: add             x3, x3, HEAP, lsl #32
    // 0xd4d484: r4 = LoadClassIdInstr(r0)
    //     0xd4d484: ldur            x4, [x0, #-1]
    //     0xd4d488: ubfx            x4, x4, #0xc, #0x14
    // 0xd4d48c: stp             x3, x0, [SP]
    // 0xd4d490: mov             x0, x4
    // 0xd4d494: mov             lr, x0
    // 0xd4d498: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d49c: blr             lr
    // 0xd4d4a0: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d4a4: ldr             x2, [fp, #0x18]
    // 0xd4d4a8: ldr             x3, [fp, #0x10]
    // 0xd4d4ac: r0 = LoadClassIdInstr(r3)
    //     0xd4d4ac: ldur            x0, [x3, #-1]
    //     0xd4d4b0: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d4b4: mov             x1, x3
    // 0xd4d4b8: r0 = GDT[cid_x0 + 0xe8d]()
    //     0xd4d4b8: add             lr, x0, #0xe8d
    //     0xd4d4bc: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d4c0: blr             lr
    // 0xd4d4c4: mov             x3, x0
    // 0xd4d4c8: ldr             x2, [fp, #0x18]
    // 0xd4d4cc: stur            x3, [fp, #-8]
    // 0xd4d4d0: r0 = LoadClassIdInstr(r2)
    //     0xd4d4d0: ldur            x0, [x2, #-1]
    //     0xd4d4d4: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d4d8: mov             x1, x2
    // 0xd4d4dc: r0 = GDT[cid_x0 + 0xe8d]()
    //     0xd4d4dc: add             lr, x0, #0xe8d
    //     0xd4d4e0: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d4e4: blr             lr
    // 0xd4d4e8: mov             x1, x0
    // 0xd4d4ec: ldur            x0, [fp, #-8]
    // 0xd4d4f0: r2 = LoadClassIdInstr(r0)
    //     0xd4d4f0: ldur            x2, [x0, #-1]
    //     0xd4d4f4: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d4f8: stp             x1, x0, [SP]
    // 0xd4d4fc: mov             x0, x2
    // 0xd4d500: mov             lr, x0
    // 0xd4d504: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d508: blr             lr
    // 0xd4d50c: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d510: ldr             x2, [fp, #0x18]
    // 0xd4d514: ldr             x3, [fp, #0x10]
    // 0xd4d518: r0 = LoadClassIdInstr(r3)
    //     0xd4d518: ldur            x0, [x3, #-1]
    //     0xd4d51c: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d520: mov             x1, x3
    // 0xd4d524: r0 = GDT[cid_x0 + 0xdcc]()
    //     0xd4d524: add             lr, x0, #0xdcc
    //     0xd4d528: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d52c: blr             lr
    // 0xd4d530: mov             x3, x0
    // 0xd4d534: ldr             x2, [fp, #0x18]
    // 0xd4d538: stur            x3, [fp, #-8]
    // 0xd4d53c: r0 = LoadClassIdInstr(r2)
    //     0xd4d53c: ldur            x0, [x2, #-1]
    //     0xd4d540: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d544: mov             x1, x2
    // 0xd4d548: r0 = GDT[cid_x0 + 0xdcc]()
    //     0xd4d548: add             lr, x0, #0xdcc
    //     0xd4d54c: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d550: blr             lr
    // 0xd4d554: mov             x1, x0
    // 0xd4d558: ldur            x0, [fp, #-8]
    // 0xd4d55c: r2 = LoadClassIdInstr(r0)
    //     0xd4d55c: ldur            x2, [x0, #-1]
    //     0xd4d560: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d564: stp             x1, x0, [SP]
    // 0xd4d568: mov             x0, x2
    // 0xd4d56c: mov             lr, x0
    // 0xd4d570: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d574: blr             lr
    // 0xd4d578: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d57c: ldr             x2, [fp, #0x18]
    // 0xd4d580: ldr             x3, [fp, #0x10]
    // 0xd4d584: r0 = LoadClassIdInstr(r3)
    //     0xd4d584: ldur            x0, [x3, #-1]
    //     0xd4d588: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d58c: mov             x1, x3
    // 0xd4d590: r0 = GDT[cid_x0 + 0xe9c]()
    //     0xd4d590: add             lr, x0, #0xe9c
    //     0xd4d594: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d598: blr             lr
    // 0xd4d59c: mov             x3, x0
    // 0xd4d5a0: ldr             x2, [fp, #0x18]
    // 0xd4d5a4: stur            x3, [fp, #-8]
    // 0xd4d5a8: r0 = LoadClassIdInstr(r2)
    //     0xd4d5a8: ldur            x0, [x2, #-1]
    //     0xd4d5ac: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d5b0: mov             x1, x2
    // 0xd4d5b4: r0 = GDT[cid_x0 + 0xe9c]()
    //     0xd4d5b4: add             lr, x0, #0xe9c
    //     0xd4d5b8: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d5bc: blr             lr
    // 0xd4d5c0: mov             x1, x0
    // 0xd4d5c4: ldur            x0, [fp, #-8]
    // 0xd4d5c8: r2 = LoadClassIdInstr(r0)
    //     0xd4d5c8: ldur            x2, [x0, #-1]
    //     0xd4d5cc: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d5d0: stp             x1, x0, [SP]
    // 0xd4d5d4: mov             x0, x2
    // 0xd4d5d8: mov             lr, x0
    // 0xd4d5dc: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d5e0: blr             lr
    // 0xd4d5e4: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d5e8: ldr             x2, [fp, #0x18]
    // 0xd4d5ec: ldr             x3, [fp, #0x10]
    // 0xd4d5f0: r0 = LoadClassIdInstr(r3)
    //     0xd4d5f0: ldur            x0, [x3, #-1]
    //     0xd4d5f4: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d5f8: mov             x1, x3
    // 0xd4d5fc: r0 = GDT[cid_x0 + 0xeb5]()
    //     0xd4d5fc: add             lr, x0, #0xeb5
    //     0xd4d600: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d604: blr             lr
    // 0xd4d608: mov             x3, x0
    // 0xd4d60c: ldr             x2, [fp, #0x18]
    // 0xd4d610: stur            x3, [fp, #-8]
    // 0xd4d614: r0 = LoadClassIdInstr(r2)
    //     0xd4d614: ldur            x0, [x2, #-1]
    //     0xd4d618: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d61c: mov             x1, x2
    // 0xd4d620: r0 = GDT[cid_x0 + 0xeb5]()
    //     0xd4d620: add             lr, x0, #0xeb5
    //     0xd4d624: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d628: blr             lr
    // 0xd4d62c: mov             x1, x0
    // 0xd4d630: ldur            x0, [fp, #-8]
    // 0xd4d634: r2 = LoadClassIdInstr(r0)
    //     0xd4d634: ldur            x2, [x0, #-1]
    //     0xd4d638: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d63c: stp             x1, x0, [SP]
    // 0xd4d640: mov             x0, x2
    // 0xd4d644: mov             lr, x0
    // 0xd4d648: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d64c: blr             lr
    // 0xd4d650: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d654: ldr             x2, [fp, #0x18]
    // 0xd4d658: ldr             x3, [fp, #0x10]
    // 0xd4d65c: r0 = LoadClassIdInstr(r3)
    //     0xd4d65c: ldur            x0, [x3, #-1]
    //     0xd4d660: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d664: mov             x1, x3
    // 0xd4d668: r0 = GDT[cid_x0 + 0xece]()
    //     0xd4d668: add             lr, x0, #0xece
    //     0xd4d66c: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d670: blr             lr
    // 0xd4d674: mov             x3, x0
    // 0xd4d678: ldr             x2, [fp, #0x18]
    // 0xd4d67c: stur            x3, [fp, #-8]
    // 0xd4d680: r0 = LoadClassIdInstr(r2)
    //     0xd4d680: ldur            x0, [x2, #-1]
    //     0xd4d684: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d688: mov             x1, x2
    // 0xd4d68c: r0 = GDT[cid_x0 + 0xece]()
    //     0xd4d68c: add             lr, x0, #0xece
    //     0xd4d690: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d694: blr             lr
    // 0xd4d698: mov             x1, x0
    // 0xd4d69c: ldur            x0, [fp, #-8]
    // 0xd4d6a0: r2 = LoadClassIdInstr(r0)
    //     0xd4d6a0: ldur            x2, [x0, #-1]
    //     0xd4d6a4: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d6a8: stp             x1, x0, [SP]
    // 0xd4d6ac: mov             x0, x2
    // 0xd4d6b0: mov             lr, x0
    // 0xd4d6b4: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d6b8: blr             lr
    // 0xd4d6bc: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d6c0: ldr             x2, [fp, #0x18]
    // 0xd4d6c4: ldr             x3, [fp, #0x10]
    // 0xd4d6c8: r0 = LoadClassIdInstr(r3)
    //     0xd4d6c8: ldur            x0, [x3, #-1]
    //     0xd4d6cc: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d6d0: mov             x1, x3
    // 0xd4d6d4: r0 = GDT[cid_x0 + 0xf0e]()
    //     0xd4d6d4: add             lr, x0, #0xf0e
    //     0xd4d6d8: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d6dc: blr             lr
    // 0xd4d6e0: mov             x3, x0
    // 0xd4d6e4: ldr             x2, [fp, #0x18]
    // 0xd4d6e8: stur            x3, [fp, #-8]
    // 0xd4d6ec: r0 = LoadClassIdInstr(r2)
    //     0xd4d6ec: ldur            x0, [x2, #-1]
    //     0xd4d6f0: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d6f4: mov             x1, x2
    // 0xd4d6f8: r0 = GDT[cid_x0 + 0xf0e]()
    //     0xd4d6f8: add             lr, x0, #0xf0e
    //     0xd4d6fc: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d700: blr             lr
    // 0xd4d704: mov             x1, x0
    // 0xd4d708: ldur            x0, [fp, #-8]
    // 0xd4d70c: r2 = LoadClassIdInstr(r0)
    //     0xd4d70c: ldur            x2, [x0, #-1]
    //     0xd4d710: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d714: stp             x1, x0, [SP]
    // 0xd4d718: mov             x0, x2
    // 0xd4d71c: mov             lr, x0
    // 0xd4d720: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d724: blr             lr
    // 0xd4d728: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d72c: ldr             x2, [fp, #0x18]
    // 0xd4d730: ldr             x3, [fp, #0x10]
    // 0xd4d734: r0 = LoadClassIdInstr(r3)
    //     0xd4d734: ldur            x0, [x3, #-1]
    //     0xd4d738: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d73c: mov             x1, x3
    // 0xd4d740: r0 = GDT[cid_x0 + 0xf66]()
    //     0xd4d740: add             lr, x0, #0xf66
    //     0xd4d744: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d748: blr             lr
    // 0xd4d74c: mov             x3, x0
    // 0xd4d750: ldr             x2, [fp, #0x18]
    // 0xd4d754: stur            x3, [fp, #-8]
    // 0xd4d758: r0 = LoadClassIdInstr(r2)
    //     0xd4d758: ldur            x0, [x2, #-1]
    //     0xd4d75c: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d760: mov             x1, x2
    // 0xd4d764: r0 = GDT[cid_x0 + 0xf66]()
    //     0xd4d764: add             lr, x0, #0xf66
    //     0xd4d768: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d76c: blr             lr
    // 0xd4d770: mov             x1, x0
    // 0xd4d774: ldur            x0, [fp, #-8]
    // 0xd4d778: r2 = LoadClassIdInstr(r0)
    //     0xd4d778: ldur            x2, [x0, #-1]
    //     0xd4d77c: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d780: stp             x1, x0, [SP]
    // 0xd4d784: mov             x0, x2
    // 0xd4d788: mov             lr, x0
    // 0xd4d78c: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d790: blr             lr
    // 0xd4d794: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d798: ldr             x2, [fp, #0x18]
    // 0xd4d79c: ldr             x3, [fp, #0x10]
    // 0xd4d7a0: r0 = LoadClassIdInstr(r3)
    //     0xd4d7a0: ldur            x0, [x3, #-1]
    //     0xd4d7a4: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d7a8: mov             x1, x3
    // 0xd4d7ac: r0 = GDT[cid_x0 + 0xf82]()
    //     0xd4d7ac: add             lr, x0, #0xf82
    //     0xd4d7b0: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d7b4: blr             lr
    // 0xd4d7b8: mov             x3, x0
    // 0xd4d7bc: ldr             x2, [fp, #0x18]
    // 0xd4d7c0: stur            x3, [fp, #-8]
    // 0xd4d7c4: r0 = LoadClassIdInstr(r2)
    //     0xd4d7c4: ldur            x0, [x2, #-1]
    //     0xd4d7c8: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d7cc: mov             x1, x2
    // 0xd4d7d0: r0 = GDT[cid_x0 + 0xf82]()
    //     0xd4d7d0: add             lr, x0, #0xf82
    //     0xd4d7d4: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d7d8: blr             lr
    // 0xd4d7dc: mov             x1, x0
    // 0xd4d7e0: ldur            x0, [fp, #-8]
    // 0xd4d7e4: cmp             w0, w1
    // 0xd4d7e8: b.ne            #0xd4d8e0
    // 0xd4d7ec: ldr             x1, [fp, #0x18]
    // 0xd4d7f0: ldr             x2, [fp, #0x10]
    // 0xd4d7f4: LoadField: r0 = r2->field_4f
    //     0xd4d7f4: ldur            w0, [x2, #0x4f]
    // 0xd4d7f8: DecompressPointer r0
    //     0xd4d7f8: add             x0, x0, HEAP, lsl #32
    // 0xd4d7fc: LoadField: r3 = r1->field_4f
    //     0xd4d7fc: ldur            w3, [x1, #0x4f]
    // 0xd4d800: DecompressPointer r3
    //     0xd4d800: add             x3, x3, HEAP, lsl #32
    // 0xd4d804: r4 = LoadClassIdInstr(r0)
    //     0xd4d804: ldur            x4, [x0, #-1]
    //     0xd4d808: ubfx            x4, x4, #0xc, #0x14
    // 0xd4d80c: stp             x3, x0, [SP]
    // 0xd4d810: mov             x0, x4
    // 0xd4d814: mov             lr, x0
    // 0xd4d818: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d81c: blr             lr
    // 0xd4d820: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d824: ldr             x1, [fp, #0x18]
    // 0xd4d828: ldr             x2, [fp, #0x10]
    // 0xd4d82c: LoadField: r0 = r2->field_53
    //     0xd4d82c: ldur            w0, [x2, #0x53]
    // 0xd4d830: DecompressPointer r0
    //     0xd4d830: add             x0, x0, HEAP, lsl #32
    // 0xd4d834: LoadField: r3 = r1->field_53
    //     0xd4d834: ldur            w3, [x1, #0x53]
    // 0xd4d838: DecompressPointer r3
    //     0xd4d838: add             x3, x3, HEAP, lsl #32
    // 0xd4d83c: cmp             w0, w3
    // 0xd4d840: b.ne            #0xd4d8e0
    // 0xd4d844: LoadField: r0 = r2->field_57
    //     0xd4d844: ldur            w0, [x2, #0x57]
    // 0xd4d848: DecompressPointer r0
    //     0xd4d848: add             x0, x0, HEAP, lsl #32
    // 0xd4d84c: LoadField: r3 = r1->field_57
    //     0xd4d84c: ldur            w3, [x1, #0x57]
    // 0xd4d850: DecompressPointer r3
    //     0xd4d850: add             x3, x3, HEAP, lsl #32
    // 0xd4d854: r4 = LoadClassIdInstr(r0)
    //     0xd4d854: ldur            x4, [x0, #-1]
    //     0xd4d858: ubfx            x4, x4, #0xc, #0x14
    // 0xd4d85c: stp             x3, x0, [SP]
    // 0xd4d860: mov             x0, x4
    // 0xd4d864: mov             lr, x0
    // 0xd4d868: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d86c: blr             lr
    // 0xd4d870: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d874: ldr             x2, [fp, #0x18]
    // 0xd4d878: ldr             x1, [fp, #0x10]
    // 0xd4d87c: r0 = LoadClassIdInstr(r1)
    //     0xd4d87c: ldur            x0, [x1, #-1]
    //     0xd4d880: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d884: r0 = GDT[cid_x0 + 0x9f4]()
    //     0xd4d884: add             lr, x0, #0x9f4
    //     0xd4d888: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d88c: blr             lr
    // 0xd4d890: mov             x2, x0
    // 0xd4d894: ldr             x1, [fp, #0x18]
    // 0xd4d898: stur            x2, [fp, #-8]
    // 0xd4d89c: r0 = LoadClassIdInstr(r1)
    //     0xd4d89c: ldur            x0, [x1, #-1]
    //     0xd4d8a0: ubfx            x0, x0, #0xc, #0x14
    // 0xd4d8a4: r0 = GDT[cid_x0 + 0x9f4]()
    //     0xd4d8a4: add             lr, x0, #0x9f4
    //     0xd4d8a8: ldr             lr, [x21, lr, lsl #3]
    //     0xd4d8ac: blr             lr
    // 0xd4d8b0: mov             x1, x0
    // 0xd4d8b4: ldur            x0, [fp, #-8]
    // 0xd4d8b8: r2 = LoadClassIdInstr(r0)
    //     0xd4d8b8: ldur            x2, [x0, #-1]
    //     0xd4d8bc: ubfx            x2, x2, #0xc, #0x14
    // 0xd4d8c0: stp             x1, x0, [SP]
    // 0xd4d8c4: mov             x0, x2
    // 0xd4d8c8: mov             lr, x0
    // 0xd4d8cc: ldr             lr, [x21, lr, lsl #3]
    // 0xd4d8d0: blr             lr
    // 0xd4d8d4: tbnz            w0, #4, #0xd4d8e0
    // 0xd4d8d8: r0 = true
    //     0xd4d8d8: add             x0, NULL, #0x20  ; true
    // 0xd4d8dc: b               #0xd4d8e4
    // 0xd4d8e0: r0 = false
    //     0xd4d8e0: add             x0, NULL, #0x30  ; false
    // 0xd4d8e4: LeaveFrame
    //     0xd4d8e4: mov             SP, fp
    //     0xd4d8e8: ldp             fp, lr, [SP], #0x10
    // 0xd4d8ec: ret
    //     0xd4d8ec: ret             
    // 0xd4d8f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd4d8f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd4d8f4: b               #0xd4d030
  }
}
