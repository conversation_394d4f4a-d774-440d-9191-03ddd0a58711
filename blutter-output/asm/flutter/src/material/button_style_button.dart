// lib: , url: package:flutter/src/material/button_style_button.dart

// class id: 1048866, size: 0x8
class :: {
}

// class id: 3080, size: 0x60, field offset: 0x5c
class _RenderInputPadding extends RenderShiftedBox {

  dynamic computeMinIntrinsicWidth(dynamic) {
    // ** addr: 0x72dec4, size: 0x24
    // 0x72dec4: EnterFrame
    //     0x72dec4: stp             fp, lr, [SP, #-0x10]!
    //     0x72dec8: mov             fp, SP
    // 0x72decc: ldr             x2, [fp, #0x10]
    // 0x72ded0: r1 = Function 'computeMinIntrinsicWidth':.
    //     0x72ded0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55050] AnonymousClosure: (0x72dee8), in [package:flutter/src/material/button_style_button.dart] _RenderInputPadding::computeMinIntrinsicWidth (0x72df5c)
    //     0x72ded4: ldr             x1, [x1, #0x50]
    // 0x72ded8: r0 = AllocateClosure()
    //     0x72ded8: bl              #0xec1630  ; AllocateClosureStub
    // 0x72dedc: LeaveFrame
    //     0x72dedc: mov             SP, fp
    //     0x72dee0: ldp             fp, lr, [SP], #0x10
    // 0x72dee4: ret
    //     0x72dee4: ret             
  }
  [closure] double computeMinIntrinsicWidth(dynamic, double) {
    // ** addr: 0x72dee8, size: 0x74
    // 0x72dee8: EnterFrame
    //     0x72dee8: stp             fp, lr, [SP, #-0x10]!
    //     0x72deec: mov             fp, SP
    // 0x72def0: ldr             x0, [fp, #0x18]
    // 0x72def4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x72def4: ldur            w1, [x0, #0x17]
    // 0x72def8: DecompressPointer r1
    //     0x72def8: add             x1, x1, HEAP, lsl #32
    // 0x72defc: CheckStackOverflow
    //     0x72defc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72df00: cmp             SP, x16
    //     0x72df04: b.ls            #0x72df44
    // 0x72df08: ldr             x2, [fp, #0x10]
    // 0x72df0c: r0 = computeMinIntrinsicWidth()
    //     0x72df0c: bl              #0x72df5c  ; [package:flutter/src/material/button_style_button.dart] _RenderInputPadding::computeMinIntrinsicWidth
    // 0x72df10: r0 = inline_Allocate_Double()
    //     0x72df10: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x72df14: add             x0, x0, #0x10
    //     0x72df18: cmp             x1, x0
    //     0x72df1c: b.ls            #0x72df4c
    //     0x72df20: str             x0, [THR, #0x50]  ; THR::top
    //     0x72df24: sub             x0, x0, #0xf
    //     0x72df28: movz            x1, #0xe15c
    //     0x72df2c: movk            x1, #0x3, lsl #16
    //     0x72df30: stur            x1, [x0, #-1]
    // 0x72df34: StoreField: r0->field_7 = d0
    //     0x72df34: stur            d0, [x0, #7]
    // 0x72df38: LeaveFrame
    //     0x72df38: mov             SP, fp
    //     0x72df3c: ldp             fp, lr, [SP], #0x10
    // 0x72df40: ret
    //     0x72df40: ret             
    // 0x72df44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72df44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72df48: b               #0x72df08
    // 0x72df4c: SaveReg d0
    //     0x72df4c: str             q0, [SP, #-0x10]!
    // 0x72df50: r0 = AllocateDouble()
    //     0x72df50: bl              #0xec2254  ; AllocateDoubleStub
    // 0x72df54: RestoreReg d0
    //     0x72df54: ldr             q0, [SP], #0x10
    // 0x72df58: b               #0x72df34
  }
  _ computeMinIntrinsicWidth(/* No info */) {
    // ** addr: 0x72df5c, size: 0xac
    // 0x72df5c: EnterFrame
    //     0x72df5c: stp             fp, lr, [SP, #-0x10]!
    //     0x72df60: mov             fp, SP
    // 0x72df64: AllocStack(0x8)
    //     0x72df64: sub             SP, SP, #8
    // 0x72df68: SetupParameters(_RenderInputPadding this /* r1 => r0, fp-0x8 */)
    //     0x72df68: mov             x0, x1
    //     0x72df6c: stur            x1, [fp, #-8]
    // 0x72df70: CheckStackOverflow
    //     0x72df70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72df74: cmp             SP, x16
    //     0x72df78: b.ls            #0x72e000
    // 0x72df7c: LoadField: r1 = r0->field_57
    //     0x72df7c: ldur            w1, [x0, #0x57]
    // 0x72df80: DecompressPointer r1
    //     0x72df80: add             x1, x1, HEAP, lsl #32
    // 0x72df84: cmp             w1, NULL
    // 0x72df88: b.eq            #0x72dfec
    // 0x72df8c: LoadField: d0 = r2->field_7
    //     0x72df8c: ldur            d0, [x2, #7]
    // 0x72df90: r0 = getMinIntrinsicWidth()
    //     0x72df90: bl              #0x72d27c  ; [package:flutter/src/rendering/box.dart] RenderBox::getMinIntrinsicWidth
    // 0x72df94: ldur            x0, [fp, #-8]
    // 0x72df98: LoadField: r1 = r0->field_5b
    //     0x72df98: ldur            w1, [x0, #0x5b]
    // 0x72df9c: DecompressPointer r1
    //     0x72df9c: add             x1, x1, HEAP, lsl #32
    // 0x72dfa0: LoadField: d1 = r1->field_7
    //     0x72dfa0: ldur            d1, [x1, #7]
    // 0x72dfa4: fcmp            d0, d1
    // 0x72dfa8: b.gt            #0x72dfe0
    // 0x72dfac: fcmp            d1, d0
    // 0x72dfb0: b.le            #0x72dfbc
    // 0x72dfb4: mov             v0.16b, v1.16b
    // 0x72dfb8: b               #0x72dfe0
    // 0x72dfbc: d2 = 0.000000
    //     0x72dfbc: eor             v2.16b, v2.16b, v2.16b
    // 0x72dfc0: fcmp            d0, d2
    // 0x72dfc4: b.ne            #0x72dfd4
    // 0x72dfc8: fadd            d3, d0, d1
    // 0x72dfcc: mov             v0.16b, v3.16b
    // 0x72dfd0: b               #0x72dfe0
    // 0x72dfd4: fcmp            d1, d1
    // 0x72dfd8: b.vc            #0x72dfe0
    // 0x72dfdc: mov             v0.16b, v1.16b
    // 0x72dfe0: LeaveFrame
    //     0x72dfe0: mov             SP, fp
    //     0x72dfe4: ldp             fp, lr, [SP], #0x10
    // 0x72dfe8: ret
    //     0x72dfe8: ret             
    // 0x72dfec: d2 = 0.000000
    //     0x72dfec: eor             v2.16b, v2.16b, v2.16b
    // 0x72dff0: mov             v0.16b, v2.16b
    // 0x72dff4: LeaveFrame
    //     0x72dff4: mov             SP, fp
    //     0x72dff8: ldp             fp, lr, [SP], #0x10
    // 0x72dffc: ret
    //     0x72dffc: ret             
    // 0x72e000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72e000: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72e004: b               #0x72df7c
  }
  _ computeDryBaseline(/* No info */) {
    // ** addr: 0x73d744, size: 0x14c
    // 0x73d744: EnterFrame
    //     0x73d744: stp             fp, lr, [SP, #-0x10]!
    //     0x73d748: mov             fp, SP
    // 0x73d74c: AllocStack(0x20)
    //     0x73d74c: sub             SP, SP, #0x20
    // 0x73d750: SetupParameters(_RenderInputPadding this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x73d750: mov             x5, x1
    //     0x73d754: mov             x4, x2
    //     0x73d758: stur            x1, [fp, #-8]
    //     0x73d75c: stur            x2, [fp, #-0x10]
    //     0x73d760: stur            x3, [fp, #-0x18]
    // 0x73d764: CheckStackOverflow
    //     0x73d764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73d768: cmp             SP, x16
    //     0x73d76c: b.ls            #0x73d878
    // 0x73d770: mov             x0, x4
    // 0x73d774: r2 = Null
    //     0x73d774: mov             x2, NULL
    // 0x73d778: r1 = Null
    //     0x73d778: mov             x1, NULL
    // 0x73d77c: r4 = 60
    //     0x73d77c: movz            x4, #0x3c
    // 0x73d780: branchIfSmi(r0, 0x73d78c)
    //     0x73d780: tbz             w0, #0, #0x73d78c
    // 0x73d784: r4 = LoadClassIdInstr(r0)
    //     0x73d784: ldur            x4, [x0, #-1]
    //     0x73d788: ubfx            x4, x4, #0xc, #0x14
    // 0x73d78c: sub             x4, x4, #0xc83
    // 0x73d790: cmp             x4, #1
    // 0x73d794: b.ls            #0x73d7a8
    // 0x73d798: r8 = BoxConstraints
    //     0x73d798: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x73d79c: r3 = Null
    //     0x73d79c: add             x3, PP, #0x55, lsl #12  ; [pp+0x55080] Null
    //     0x73d7a0: ldr             x3, [x3, #0x80]
    // 0x73d7a4: r0 = BoxConstraints()
    //     0x73d7a4: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x73d7a8: ldur            x0, [fp, #-8]
    // 0x73d7ac: LoadField: r4 = r0->field_57
    //     0x73d7ac: ldur            w4, [x0, #0x57]
    // 0x73d7b0: DecompressPointer r4
    //     0x73d7b0: add             x4, x4, HEAP, lsl #32
    // 0x73d7b4: stur            x4, [fp, #-0x20]
    // 0x73d7b8: cmp             w4, NULL
    // 0x73d7bc: b.ne            #0x73d7d0
    // 0x73d7c0: r0 = Null
    //     0x73d7c0: mov             x0, NULL
    // 0x73d7c4: LeaveFrame
    //     0x73d7c4: mov             SP, fp
    //     0x73d7c8: ldp             fp, lr, [SP], #0x10
    // 0x73d7cc: ret
    //     0x73d7cc: ret             
    // 0x73d7d0: mov             x1, x4
    // 0x73d7d4: ldur            x2, [fp, #-0x10]
    // 0x73d7d8: ldur            x3, [fp, #-0x18]
    // 0x73d7dc: r0 = getDryBaseline()
    //     0x73d7dc: bl              #0x730f54  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryBaseline
    // 0x73d7e0: stur            x0, [fp, #-0x18]
    // 0x73d7e4: cmp             w0, NULL
    // 0x73d7e8: b.ne            #0x73d7fc
    // 0x73d7ec: r0 = Null
    //     0x73d7ec: mov             x0, NULL
    // 0x73d7f0: LeaveFrame
    //     0x73d7f0: mov             SP, fp
    //     0x73d7f4: ldp             fp, lr, [SP], #0x10
    // 0x73d7f8: ret
    //     0x73d7f8: ret             
    // 0x73d7fc: ldur            x1, [fp, #-0x20]
    // 0x73d800: ldur            x2, [fp, #-0x10]
    // 0x73d804: r0 = getDryLayout()
    //     0x73d804: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x73d808: ldur            x1, [fp, #-8]
    // 0x73d80c: ldur            x2, [fp, #-0x10]
    // 0x73d810: stur            x0, [fp, #-8]
    // 0x73d814: r0 = getDryLayout()
    //     0x73d814: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x73d818: mov             x1, x0
    // 0x73d81c: ldur            x2, [fp, #-8]
    // 0x73d820: r0 = -()
    //     0x73d820: bl              #0x618814  ; [dart:ui] Size::-
    // 0x73d824: mov             x2, x0
    // 0x73d828: r1 = Instance_Alignment
    //     0x73d828: add             x1, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x73d82c: ldr             x1, [x1, #0x898]
    // 0x73d830: r0 = alongOffset()
    //     0x73d830: bl              #0x73c5c8  ; [package:flutter/src/painting/alignment.dart] Alignment::alongOffset
    // 0x73d834: LoadField: d0 = r0->field_f
    //     0x73d834: ldur            d0, [x0, #0xf]
    // 0x73d838: ldur            x1, [fp, #-0x18]
    // 0x73d83c: LoadField: d1 = r1->field_7
    //     0x73d83c: ldur            d1, [x1, #7]
    // 0x73d840: fadd            d2, d1, d0
    // 0x73d844: r0 = inline_Allocate_Double()
    //     0x73d844: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x73d848: add             x0, x0, #0x10
    //     0x73d84c: cmp             x1, x0
    //     0x73d850: b.ls            #0x73d880
    //     0x73d854: str             x0, [THR, #0x50]  ; THR::top
    //     0x73d858: sub             x0, x0, #0xf
    //     0x73d85c: movz            x1, #0xe15c
    //     0x73d860: movk            x1, #0x3, lsl #16
    //     0x73d864: stur            x1, [x0, #-1]
    // 0x73d868: StoreField: r0->field_7 = d2
    //     0x73d868: stur            d2, [x0, #7]
    // 0x73d86c: LeaveFrame
    //     0x73d86c: mov             SP, fp
    //     0x73d870: ldp             fp, lr, [SP], #0x10
    // 0x73d874: ret
    //     0x73d874: ret             
    // 0x73d878: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73d878: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73d87c: b               #0x73d770
    // 0x73d880: SaveReg d2
    //     0x73d880: str             q2, [SP, #-0x10]!
    // 0x73d884: r0 = AllocateDouble()
    //     0x73d884: bl              #0xec2254  ; AllocateDoubleStub
    // 0x73d888: RestoreReg d2
    //     0x73d888: ldr             q2, [SP], #0x10
    // 0x73d88c: b               #0x73d868
  }
  dynamic computeMinIntrinsicHeight(dynamic) {
    // ** addr: 0x7481a4, size: 0x24
    // 0x7481a4: EnterFrame
    //     0x7481a4: stp             fp, lr, [SP, #-0x10]!
    //     0x7481a8: mov             fp, SP
    // 0x7481ac: ldr             x2, [fp, #0x10]
    // 0x7481b0: r1 = Function 'computeMinIntrinsicHeight':.
    //     0x7481b0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55048] AnonymousClosure: (0x7481c8), in [package:flutter/src/material/button_style_button.dart] _RenderInputPadding::computeMinIntrinsicHeight (0x74823c)
    //     0x7481b4: ldr             x1, [x1, #0x48]
    // 0x7481b8: r0 = AllocateClosure()
    //     0x7481b8: bl              #0xec1630  ; AllocateClosureStub
    // 0x7481bc: LeaveFrame
    //     0x7481bc: mov             SP, fp
    //     0x7481c0: ldp             fp, lr, [SP], #0x10
    // 0x7481c4: ret
    //     0x7481c4: ret             
  }
  [closure] double computeMinIntrinsicHeight(dynamic, double) {
    // ** addr: 0x7481c8, size: 0x74
    // 0x7481c8: EnterFrame
    //     0x7481c8: stp             fp, lr, [SP, #-0x10]!
    //     0x7481cc: mov             fp, SP
    // 0x7481d0: ldr             x0, [fp, #0x18]
    // 0x7481d4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7481d4: ldur            w1, [x0, #0x17]
    // 0x7481d8: DecompressPointer r1
    //     0x7481d8: add             x1, x1, HEAP, lsl #32
    // 0x7481dc: CheckStackOverflow
    //     0x7481dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7481e0: cmp             SP, x16
    //     0x7481e4: b.ls            #0x748224
    // 0x7481e8: ldr             x2, [fp, #0x10]
    // 0x7481ec: r0 = computeMinIntrinsicHeight()
    //     0x7481ec: bl              #0x74823c  ; [package:flutter/src/material/button_style_button.dart] _RenderInputPadding::computeMinIntrinsicHeight
    // 0x7481f0: r0 = inline_Allocate_Double()
    //     0x7481f0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7481f4: add             x0, x0, #0x10
    //     0x7481f8: cmp             x1, x0
    //     0x7481fc: b.ls            #0x74822c
    //     0x748200: str             x0, [THR, #0x50]  ; THR::top
    //     0x748204: sub             x0, x0, #0xf
    //     0x748208: movz            x1, #0xe15c
    //     0x74820c: movk            x1, #0x3, lsl #16
    //     0x748210: stur            x1, [x0, #-1]
    // 0x748214: StoreField: r0->field_7 = d0
    //     0x748214: stur            d0, [x0, #7]
    // 0x748218: LeaveFrame
    //     0x748218: mov             SP, fp
    //     0x74821c: ldp             fp, lr, [SP], #0x10
    // 0x748220: ret
    //     0x748220: ret             
    // 0x748224: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x748224: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x748228: b               #0x7481e8
    // 0x74822c: SaveReg d0
    //     0x74822c: str             q0, [SP, #-0x10]!
    // 0x748230: r0 = AllocateDouble()
    //     0x748230: bl              #0xec2254  ; AllocateDoubleStub
    // 0x748234: RestoreReg d0
    //     0x748234: ldr             q0, [SP], #0x10
    // 0x748238: b               #0x748214
  }
  _ computeMinIntrinsicHeight(/* No info */) {
    // ** addr: 0x74823c, size: 0xac
    // 0x74823c: EnterFrame
    //     0x74823c: stp             fp, lr, [SP, #-0x10]!
    //     0x748240: mov             fp, SP
    // 0x748244: AllocStack(0x8)
    //     0x748244: sub             SP, SP, #8
    // 0x748248: SetupParameters(_RenderInputPadding this /* r1 => r0, fp-0x8 */)
    //     0x748248: mov             x0, x1
    //     0x74824c: stur            x1, [fp, #-8]
    // 0x748250: CheckStackOverflow
    //     0x748250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x748254: cmp             SP, x16
    //     0x748258: b.ls            #0x7482e0
    // 0x74825c: LoadField: r1 = r0->field_57
    //     0x74825c: ldur            w1, [x0, #0x57]
    // 0x748260: DecompressPointer r1
    //     0x748260: add             x1, x1, HEAP, lsl #32
    // 0x748264: cmp             w1, NULL
    // 0x748268: b.eq            #0x7482cc
    // 0x74826c: LoadField: d0 = r2->field_7
    //     0x74826c: ldur            d0, [x2, #7]
    // 0x748270: r0 = getMinIntrinsicHeight()
    //     0x748270: bl              #0x73933c  ; [package:flutter/src/rendering/box.dart] RenderBox::getMinIntrinsicHeight
    // 0x748274: ldur            x0, [fp, #-8]
    // 0x748278: LoadField: r1 = r0->field_5b
    //     0x748278: ldur            w1, [x0, #0x5b]
    // 0x74827c: DecompressPointer r1
    //     0x74827c: add             x1, x1, HEAP, lsl #32
    // 0x748280: LoadField: d1 = r1->field_f
    //     0x748280: ldur            d1, [x1, #0xf]
    // 0x748284: fcmp            d0, d1
    // 0x748288: b.gt            #0x7482c0
    // 0x74828c: fcmp            d1, d0
    // 0x748290: b.le            #0x74829c
    // 0x748294: mov             v0.16b, v1.16b
    // 0x748298: b               #0x7482c0
    // 0x74829c: d2 = 0.000000
    //     0x74829c: eor             v2.16b, v2.16b, v2.16b
    // 0x7482a0: fcmp            d0, d2
    // 0x7482a4: b.ne            #0x7482b4
    // 0x7482a8: fadd            d3, d0, d1
    // 0x7482ac: mov             v0.16b, v3.16b
    // 0x7482b0: b               #0x7482c0
    // 0x7482b4: fcmp            d1, d1
    // 0x7482b8: b.vc            #0x7482c0
    // 0x7482bc: mov             v0.16b, v1.16b
    // 0x7482c0: LeaveFrame
    //     0x7482c0: mov             SP, fp
    //     0x7482c4: ldp             fp, lr, [SP], #0x10
    // 0x7482c8: ret
    //     0x7482c8: ret             
    // 0x7482cc: d2 = 0.000000
    //     0x7482cc: eor             v2.16b, v2.16b, v2.16b
    // 0x7482d0: mov             v0.16b, v2.16b
    // 0x7482d4: LeaveFrame
    //     0x7482d4: mov             SP, fp
    //     0x7482d8: ldp             fp, lr, [SP], #0x10
    // 0x7482dc: ret
    //     0x7482dc: ret             
    // 0x7482e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7482e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7482e4: b               #0x74825c
  }
  dynamic computeMaxIntrinsicWidth(dynamic) {
    // ** addr: 0x74e778, size: 0x24
    // 0x74e778: EnterFrame
    //     0x74e778: stp             fp, lr, [SP, #-0x10]!
    //     0x74e77c: mov             fp, SP
    // 0x74e780: ldr             x2, [fp, #0x10]
    // 0x74e784: r1 = Function 'computeMaxIntrinsicWidth':.
    //     0x74e784: add             x1, PP, #0x55, lsl #12  ; [pp+0x55040] AnonymousClosure: (0x74e79c), in [package:flutter/src/material/button_style_button.dart] _RenderInputPadding::computeMaxIntrinsicWidth (0x74e810)
    //     0x74e788: ldr             x1, [x1, #0x40]
    // 0x74e78c: r0 = AllocateClosure()
    //     0x74e78c: bl              #0xec1630  ; AllocateClosureStub
    // 0x74e790: LeaveFrame
    //     0x74e790: mov             SP, fp
    //     0x74e794: ldp             fp, lr, [SP], #0x10
    // 0x74e798: ret
    //     0x74e798: ret             
  }
  [closure] double computeMaxIntrinsicWidth(dynamic, double) {
    // ** addr: 0x74e79c, size: 0x74
    // 0x74e79c: EnterFrame
    //     0x74e79c: stp             fp, lr, [SP, #-0x10]!
    //     0x74e7a0: mov             fp, SP
    // 0x74e7a4: ldr             x0, [fp, #0x18]
    // 0x74e7a8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x74e7a8: ldur            w1, [x0, #0x17]
    // 0x74e7ac: DecompressPointer r1
    //     0x74e7ac: add             x1, x1, HEAP, lsl #32
    // 0x74e7b0: CheckStackOverflow
    //     0x74e7b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74e7b4: cmp             SP, x16
    //     0x74e7b8: b.ls            #0x74e7f8
    // 0x74e7bc: ldr             x2, [fp, #0x10]
    // 0x74e7c0: r0 = computeMaxIntrinsicWidth()
    //     0x74e7c0: bl              #0x74e810  ; [package:flutter/src/material/button_style_button.dart] _RenderInputPadding::computeMaxIntrinsicWidth
    // 0x74e7c4: r0 = inline_Allocate_Double()
    //     0x74e7c4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74e7c8: add             x0, x0, #0x10
    //     0x74e7cc: cmp             x1, x0
    //     0x74e7d0: b.ls            #0x74e800
    //     0x74e7d4: str             x0, [THR, #0x50]  ; THR::top
    //     0x74e7d8: sub             x0, x0, #0xf
    //     0x74e7dc: movz            x1, #0xe15c
    //     0x74e7e0: movk            x1, #0x3, lsl #16
    //     0x74e7e4: stur            x1, [x0, #-1]
    // 0x74e7e8: StoreField: r0->field_7 = d0
    //     0x74e7e8: stur            d0, [x0, #7]
    // 0x74e7ec: LeaveFrame
    //     0x74e7ec: mov             SP, fp
    //     0x74e7f0: ldp             fp, lr, [SP], #0x10
    // 0x74e7f4: ret
    //     0x74e7f4: ret             
    // 0x74e7f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74e7f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74e7fc: b               #0x74e7bc
    // 0x74e800: SaveReg d0
    //     0x74e800: str             q0, [SP, #-0x10]!
    // 0x74e804: r0 = AllocateDouble()
    //     0x74e804: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74e808: RestoreReg d0
    //     0x74e808: ldr             q0, [SP], #0x10
    // 0x74e80c: b               #0x74e7e8
  }
  _ computeMaxIntrinsicWidth(/* No info */) {
    // ** addr: 0x74e810, size: 0xac
    // 0x74e810: EnterFrame
    //     0x74e810: stp             fp, lr, [SP, #-0x10]!
    //     0x74e814: mov             fp, SP
    // 0x74e818: AllocStack(0x8)
    //     0x74e818: sub             SP, SP, #8
    // 0x74e81c: SetupParameters(_RenderInputPadding this /* r1 => r0, fp-0x8 */)
    //     0x74e81c: mov             x0, x1
    //     0x74e820: stur            x1, [fp, #-8]
    // 0x74e824: CheckStackOverflow
    //     0x74e824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74e828: cmp             SP, x16
    //     0x74e82c: b.ls            #0x74e8b4
    // 0x74e830: LoadField: r1 = r0->field_57
    //     0x74e830: ldur            w1, [x0, #0x57]
    // 0x74e834: DecompressPointer r1
    //     0x74e834: add             x1, x1, HEAP, lsl #32
    // 0x74e838: cmp             w1, NULL
    // 0x74e83c: b.eq            #0x74e8a0
    // 0x74e840: LoadField: d0 = r2->field_7
    //     0x74e840: ldur            d0, [x2, #7]
    // 0x74e844: r0 = getMaxIntrinsicWidth()
    //     0x74e844: bl              #0x72d4b4  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicWidth
    // 0x74e848: ldur            x0, [fp, #-8]
    // 0x74e84c: LoadField: r1 = r0->field_5b
    //     0x74e84c: ldur            w1, [x0, #0x5b]
    // 0x74e850: DecompressPointer r1
    //     0x74e850: add             x1, x1, HEAP, lsl #32
    // 0x74e854: LoadField: d1 = r1->field_7
    //     0x74e854: ldur            d1, [x1, #7]
    // 0x74e858: fcmp            d0, d1
    // 0x74e85c: b.gt            #0x74e894
    // 0x74e860: fcmp            d1, d0
    // 0x74e864: b.le            #0x74e870
    // 0x74e868: mov             v0.16b, v1.16b
    // 0x74e86c: b               #0x74e894
    // 0x74e870: d2 = 0.000000
    //     0x74e870: eor             v2.16b, v2.16b, v2.16b
    // 0x74e874: fcmp            d0, d2
    // 0x74e878: b.ne            #0x74e888
    // 0x74e87c: fadd            d3, d0, d1
    // 0x74e880: mov             v0.16b, v3.16b
    // 0x74e884: b               #0x74e894
    // 0x74e888: fcmp            d1, d1
    // 0x74e88c: b.vc            #0x74e894
    // 0x74e890: mov             v0.16b, v1.16b
    // 0x74e894: LeaveFrame
    //     0x74e894: mov             SP, fp
    //     0x74e898: ldp             fp, lr, [SP], #0x10
    // 0x74e89c: ret
    //     0x74e89c: ret             
    // 0x74e8a0: d2 = 0.000000
    //     0x74e8a0: eor             v2.16b, v2.16b, v2.16b
    // 0x74e8a4: mov             v0.16b, v2.16b
    // 0x74e8a8: LeaveFrame
    //     0x74e8a8: mov             SP, fp
    //     0x74e8ac: ldp             fp, lr, [SP], #0x10
    // 0x74e8b0: ret
    //     0x74e8b0: ret             
    // 0x74e8b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74e8b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74e8b8: b               #0x74e830
  }
  dynamic computeMaxIntrinsicHeight(dynamic) {
    // ** addr: 0x752358, size: 0x24
    // 0x752358: EnterFrame
    //     0x752358: stp             fp, lr, [SP, #-0x10]!
    //     0x75235c: mov             fp, SP
    // 0x752360: ldr             x2, [fp, #0x10]
    // 0x752364: r1 = Function 'computeMaxIntrinsicHeight':.
    //     0x752364: add             x1, PP, #0x55, lsl #12  ; [pp+0x55038] AnonymousClosure: (0x75237c), in [package:flutter/src/material/button_style_button.dart] _RenderInputPadding::computeMaxIntrinsicHeight (0x7523f0)
    //     0x752368: ldr             x1, [x1, #0x38]
    // 0x75236c: r0 = AllocateClosure()
    //     0x75236c: bl              #0xec1630  ; AllocateClosureStub
    // 0x752370: LeaveFrame
    //     0x752370: mov             SP, fp
    //     0x752374: ldp             fp, lr, [SP], #0x10
    // 0x752378: ret
    //     0x752378: ret             
  }
  [closure] double computeMaxIntrinsicHeight(dynamic, double) {
    // ** addr: 0x75237c, size: 0x74
    // 0x75237c: EnterFrame
    //     0x75237c: stp             fp, lr, [SP, #-0x10]!
    //     0x752380: mov             fp, SP
    // 0x752384: ldr             x0, [fp, #0x18]
    // 0x752388: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x752388: ldur            w1, [x0, #0x17]
    // 0x75238c: DecompressPointer r1
    //     0x75238c: add             x1, x1, HEAP, lsl #32
    // 0x752390: CheckStackOverflow
    //     0x752390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x752394: cmp             SP, x16
    //     0x752398: b.ls            #0x7523d8
    // 0x75239c: ldr             x2, [fp, #0x10]
    // 0x7523a0: r0 = computeMaxIntrinsicHeight()
    //     0x7523a0: bl              #0x7523f0  ; [package:flutter/src/material/button_style_button.dart] _RenderInputPadding::computeMaxIntrinsicHeight
    // 0x7523a4: r0 = inline_Allocate_Double()
    //     0x7523a4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7523a8: add             x0, x0, #0x10
    //     0x7523ac: cmp             x1, x0
    //     0x7523b0: b.ls            #0x7523e0
    //     0x7523b4: str             x0, [THR, #0x50]  ; THR::top
    //     0x7523b8: sub             x0, x0, #0xf
    //     0x7523bc: movz            x1, #0xe15c
    //     0x7523c0: movk            x1, #0x3, lsl #16
    //     0x7523c4: stur            x1, [x0, #-1]
    // 0x7523c8: StoreField: r0->field_7 = d0
    //     0x7523c8: stur            d0, [x0, #7]
    // 0x7523cc: LeaveFrame
    //     0x7523cc: mov             SP, fp
    //     0x7523d0: ldp             fp, lr, [SP], #0x10
    // 0x7523d4: ret
    //     0x7523d4: ret             
    // 0x7523d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7523d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7523dc: b               #0x75239c
    // 0x7523e0: SaveReg d0
    //     0x7523e0: str             q0, [SP, #-0x10]!
    // 0x7523e4: r0 = AllocateDouble()
    //     0x7523e4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7523e8: RestoreReg d0
    //     0x7523e8: ldr             q0, [SP], #0x10
    // 0x7523ec: b               #0x7523c8
  }
  _ computeMaxIntrinsicHeight(/* No info */) {
    // ** addr: 0x7523f0, size: 0xac
    // 0x7523f0: EnterFrame
    //     0x7523f0: stp             fp, lr, [SP, #-0x10]!
    //     0x7523f4: mov             fp, SP
    // 0x7523f8: AllocStack(0x8)
    //     0x7523f8: sub             SP, SP, #8
    // 0x7523fc: SetupParameters(_RenderInputPadding this /* r1 => r0, fp-0x8 */)
    //     0x7523fc: mov             x0, x1
    //     0x752400: stur            x1, [fp, #-8]
    // 0x752404: CheckStackOverflow
    //     0x752404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x752408: cmp             SP, x16
    //     0x75240c: b.ls            #0x752494
    // 0x752410: LoadField: r1 = r0->field_57
    //     0x752410: ldur            w1, [x0, #0x57]
    // 0x752414: DecompressPointer r1
    //     0x752414: add             x1, x1, HEAP, lsl #32
    // 0x752418: cmp             w1, NULL
    // 0x75241c: b.eq            #0x752480
    // 0x752420: LoadField: d0 = r2->field_7
    //     0x752420: ldur            d0, [x2, #7]
    // 0x752424: r0 = getMaxIntrinsicHeight()
    //     0x752424: bl              #0x72d954  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicHeight
    // 0x752428: ldur            x0, [fp, #-8]
    // 0x75242c: LoadField: r1 = r0->field_5b
    //     0x75242c: ldur            w1, [x0, #0x5b]
    // 0x752430: DecompressPointer r1
    //     0x752430: add             x1, x1, HEAP, lsl #32
    // 0x752434: LoadField: d1 = r1->field_f
    //     0x752434: ldur            d1, [x1, #0xf]
    // 0x752438: fcmp            d0, d1
    // 0x75243c: b.gt            #0x752474
    // 0x752440: fcmp            d1, d0
    // 0x752444: b.le            #0x752450
    // 0x752448: mov             v0.16b, v1.16b
    // 0x75244c: b               #0x752474
    // 0x752450: d2 = 0.000000
    //     0x752450: eor             v2.16b, v2.16b, v2.16b
    // 0x752454: fcmp            d0, d2
    // 0x752458: b.ne            #0x752468
    // 0x75245c: fadd            d3, d0, d1
    // 0x752460: mov             v0.16b, v3.16b
    // 0x752464: b               #0x752474
    // 0x752468: fcmp            d1, d1
    // 0x75246c: b.vc            #0x752474
    // 0x752470: mov             v0.16b, v1.16b
    // 0x752474: LeaveFrame
    //     0x752474: mov             SP, fp
    //     0x752478: ldp             fp, lr, [SP], #0x10
    // 0x75247c: ret
    //     0x75247c: ret             
    // 0x752480: d2 = 0.000000
    //     0x752480: eor             v2.16b, v2.16b, v2.16b
    // 0x752484: mov             v0.16b, v2.16b
    // 0x752488: LeaveFrame
    //     0x752488: mov             SP, fp
    //     0x75248c: ldp             fp, lr, [SP], #0x10
    // 0x752490: ret
    //     0x752490: ret             
    // 0x752494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x752494: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x752498: b               #0x752410
  }
  _ computeDryLayout(/* No info */) {
    // ** addr: 0x754b74, size: 0x34
    // 0x754b74: EnterFrame
    //     0x754b74: stp             fp, lr, [SP, #-0x10]!
    //     0x754b78: mov             fp, SP
    // 0x754b7c: CheckStackOverflow
    //     0x754b7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x754b80: cmp             SP, x16
    //     0x754b84: b.ls            #0x754ba0
    // 0x754b88: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static.
    //     0x754b88: add             x3, PP, #0x44, lsl #12  ; [pp+0x44d20] Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static. (0x7e54fb140c14)
    //     0x754b8c: ldr             x3, [x3, #0xd20]
    // 0x754b90: r0 = _computeSize()
    //     0x754b90: bl              #0x754ba8  ; [package:flutter/src/material/button_style_button.dart] _RenderInputPadding::_computeSize
    // 0x754b94: LeaveFrame
    //     0x754b94: mov             SP, fp
    //     0x754b98: ldp             fp, lr, [SP], #0x10
    // 0x754b9c: ret
    //     0x754b9c: ret             
    // 0x754ba0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x754ba0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x754ba4: b               #0x754b88
  }
  _ _computeSize(/* No info */) {
    // ** addr: 0x754ba8, size: 0x140
    // 0x754ba8: EnterFrame
    //     0x754ba8: stp             fp, lr, [SP, #-0x10]!
    //     0x754bac: mov             fp, SP
    // 0x754bb0: AllocStack(0x38)
    //     0x754bb0: sub             SP, SP, #0x38
    // 0x754bb4: SetupParameters(_RenderInputPadding this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0 */)
    //     0x754bb4: stur            x1, [fp, #-8]
    //     0x754bb8: mov             x16, x2
    //     0x754bbc: mov             x2, x1
    //     0x754bc0: mov             x1, x16
    //     0x754bc4: mov             x0, x3
    //     0x754bc8: stur            x1, [fp, #-0x10]
    // 0x754bcc: CheckStackOverflow
    //     0x754bcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x754bd0: cmp             SP, x16
    //     0x754bd4: b.ls            #0x754ce0
    // 0x754bd8: LoadField: r3 = r2->field_57
    //     0x754bd8: ldur            w3, [x2, #0x57]
    // 0x754bdc: DecompressPointer r3
    //     0x754bdc: add             x3, x3, HEAP, lsl #32
    // 0x754be0: cmp             w3, NULL
    // 0x754be4: b.eq            #0x754ccc
    // 0x754be8: stp             x3, x0, [SP, #8]
    // 0x754bec: str             x1, [SP]
    // 0x754bf0: ClosureCall
    //     0x754bf0: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x754bf4: ldur            x2, [x0, #0x1f]
    //     0x754bf8: blr             x2
    // 0x754bfc: LoadField: d0 = r0->field_7
    //     0x754bfc: ldur            d0, [x0, #7]
    // 0x754c00: ldur            x1, [fp, #-8]
    // 0x754c04: LoadField: r2 = r1->field_5b
    //     0x754c04: ldur            w2, [x1, #0x5b]
    // 0x754c08: DecompressPointer r2
    //     0x754c08: add             x2, x2, HEAP, lsl #32
    // 0x754c0c: LoadField: d1 = r2->field_7
    //     0x754c0c: ldur            d1, [x2, #7]
    // 0x754c10: fcmp            d0, d1
    // 0x754c14: b.le            #0x754c20
    // 0x754c18: d2 = 0.000000
    //     0x754c18: eor             v2.16b, v2.16b, v2.16b
    // 0x754c1c: b               #0x754c58
    // 0x754c20: fcmp            d1, d0
    // 0x754c24: b.le            #0x754c34
    // 0x754c28: mov             v0.16b, v1.16b
    // 0x754c2c: d2 = 0.000000
    //     0x754c2c: eor             v2.16b, v2.16b, v2.16b
    // 0x754c30: b               #0x754c58
    // 0x754c34: d2 = 0.000000
    //     0x754c34: eor             v2.16b, v2.16b, v2.16b
    // 0x754c38: fcmp            d0, d2
    // 0x754c3c: b.ne            #0x754c4c
    // 0x754c40: fadd            d3, d0, d1
    // 0x754c44: mov             v0.16b, v3.16b
    // 0x754c48: b               #0x754c58
    // 0x754c4c: fcmp            d1, d1
    // 0x754c50: b.vc            #0x754c58
    // 0x754c54: mov             v0.16b, v1.16b
    // 0x754c58: stur            d0, [fp, #-0x20]
    // 0x754c5c: LoadField: d1 = r0->field_f
    //     0x754c5c: ldur            d1, [x0, #0xf]
    // 0x754c60: LoadField: d3 = r2->field_f
    //     0x754c60: ldur            d3, [x2, #0xf]
    // 0x754c64: fcmp            d1, d3
    // 0x754c68: b.gt            #0x754c9c
    // 0x754c6c: fcmp            d3, d1
    // 0x754c70: b.le            #0x754c7c
    // 0x754c74: mov             v1.16b, v3.16b
    // 0x754c78: b               #0x754c9c
    // 0x754c7c: fcmp            d1, d2
    // 0x754c80: b.ne            #0x754c90
    // 0x754c84: fadd            d2, d1, d3
    // 0x754c88: mov             v1.16b, v2.16b
    // 0x754c8c: b               #0x754c9c
    // 0x754c90: fcmp            d3, d3
    // 0x754c94: b.vc            #0x754c9c
    // 0x754c98: mov             v1.16b, v3.16b
    // 0x754c9c: stur            d1, [fp, #-0x18]
    // 0x754ca0: r0 = Size()
    //     0x754ca0: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x754ca4: ldur            d0, [fp, #-0x20]
    // 0x754ca8: StoreField: r0->field_7 = d0
    //     0x754ca8: stur            d0, [x0, #7]
    // 0x754cac: ldur            d0, [fp, #-0x18]
    // 0x754cb0: StoreField: r0->field_f = d0
    //     0x754cb0: stur            d0, [x0, #0xf]
    // 0x754cb4: ldur            x1, [fp, #-0x10]
    // 0x754cb8: mov             x2, x0
    // 0x754cbc: r0 = constrain()
    //     0x754cbc: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x754cc0: LeaveFrame
    //     0x754cc0: mov             SP, fp
    //     0x754cc4: ldp             fp, lr, [SP], #0x10
    // 0x754cc8: ret
    //     0x754cc8: ret             
    // 0x754ccc: r0 = Instance_Size
    //     0x754ccc: add             x0, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!Size@e2c001
    //     0x754cd0: ldr             x0, [x0, #0xa20]
    // 0x754cd4: LeaveFrame
    //     0x754cd4: mov             SP, fp
    //     0x754cd8: ldp             fp, lr, [SP], #0x10
    // 0x754cdc: ret
    //     0x754cdc: ret             
    // 0x754ce0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x754ce0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x754ce4: b               #0x754bd8
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x76a468, size: 0x190
    // 0x76a468: EnterFrame
    //     0x76a468: stp             fp, lr, [SP, #-0x10]!
    //     0x76a46c: mov             fp, SP
    // 0x76a470: AllocStack(0x18)
    //     0x76a470: sub             SP, SP, #0x18
    // 0x76a474: SetupParameters(_RenderInputPadding this /* r1 => r3, fp-0x10 */)
    //     0x76a474: mov             x3, x1
    //     0x76a478: stur            x1, [fp, #-0x10]
    // 0x76a47c: CheckStackOverflow
    //     0x76a47c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76a480: cmp             SP, x16
    //     0x76a484: b.ls            #0x76a5e8
    // 0x76a488: LoadField: r4 = r3->field_27
    //     0x76a488: ldur            w4, [x3, #0x27]
    // 0x76a48c: DecompressPointer r4
    //     0x76a48c: add             x4, x4, HEAP, lsl #32
    // 0x76a490: stur            x4, [fp, #-8]
    // 0x76a494: cmp             w4, NULL
    // 0x76a498: b.eq            #0x76a5cc
    // 0x76a49c: mov             x0, x4
    // 0x76a4a0: r2 = Null
    //     0x76a4a0: mov             x2, NULL
    // 0x76a4a4: r1 = Null
    //     0x76a4a4: mov             x1, NULL
    // 0x76a4a8: r4 = LoadClassIdInstr(r0)
    //     0x76a4a8: ldur            x4, [x0, #-1]
    //     0x76a4ac: ubfx            x4, x4, #0xc, #0x14
    // 0x76a4b0: sub             x4, x4, #0xc83
    // 0x76a4b4: cmp             x4, #1
    // 0x76a4b8: b.ls            #0x76a4cc
    // 0x76a4bc: r8 = BoxConstraints
    //     0x76a4bc: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76a4c0: r3 = Null
    //     0x76a4c0: add             x3, PP, #0x55, lsl #12  ; [pp+0x55060] Null
    //     0x76a4c4: ldr             x3, [x3, #0x60]
    // 0x76a4c8: r0 = BoxConstraints()
    //     0x76a4c8: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76a4cc: ldur            x1, [fp, #-0x10]
    // 0x76a4d0: ldur            x2, [fp, #-8]
    // 0x76a4d4: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static.
    //     0x76a4d4: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b28] Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static. (0x7e54fb1673f8)
    //     0x76a4d8: ldr             x3, [x3, #0xb28]
    // 0x76a4dc: r0 = _computeSize()
    //     0x76a4dc: bl              #0x754ba8  ; [package:flutter/src/material/button_style_button.dart] _RenderInputPadding::_computeSize
    // 0x76a4e0: ldur            x3, [fp, #-0x10]
    // 0x76a4e4: StoreField: r3->field_53 = r0
    //     0x76a4e4: stur            w0, [x3, #0x53]
    //     0x76a4e8: ldurb           w16, [x3, #-1]
    //     0x76a4ec: ldurb           w17, [x0, #-1]
    //     0x76a4f0: and             x16, x17, x16, lsr #2
    //     0x76a4f4: tst             x16, HEAP, lsr #32
    //     0x76a4f8: b.eq            #0x76a500
    //     0x76a4fc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x76a500: LoadField: r0 = r3->field_57
    //     0x76a500: ldur            w0, [x3, #0x57]
    // 0x76a504: DecompressPointer r0
    //     0x76a504: add             x0, x0, HEAP, lsl #32
    // 0x76a508: cmp             w0, NULL
    // 0x76a50c: b.eq            #0x76a5bc
    // 0x76a510: LoadField: r4 = r0->field_7
    //     0x76a510: ldur            w4, [x0, #7]
    // 0x76a514: DecompressPointer r4
    //     0x76a514: add             x4, x4, HEAP, lsl #32
    // 0x76a518: stur            x4, [fp, #-8]
    // 0x76a51c: cmp             w4, NULL
    // 0x76a520: b.eq            #0x76a5f0
    // 0x76a524: mov             x0, x4
    // 0x76a528: r2 = Null
    //     0x76a528: mov             x2, NULL
    // 0x76a52c: r1 = Null
    //     0x76a52c: mov             x1, NULL
    // 0x76a530: r4 = LoadClassIdInstr(r0)
    //     0x76a530: ldur            x4, [x0, #-1]
    //     0x76a534: ubfx            x4, x4, #0xc, #0x14
    // 0x76a538: sub             x4, x4, #0xc71
    // 0x76a53c: cmp             x4, #0xf
    // 0x76a540: b.ls            #0x76a558
    // 0x76a544: r8 = BoxParentData
    //     0x76a544: add             x8, PP, #0x25, lsl #12  ; [pp+0x252c8] Type: BoxParentData
    //     0x76a548: ldr             x8, [x8, #0x2c8]
    // 0x76a54c: r3 = Null
    //     0x76a54c: add             x3, PP, #0x55, lsl #12  ; [pp+0x55070] Null
    //     0x76a550: ldr             x3, [x3, #0x70]
    // 0x76a554: r0 = DefaultTypeTest()
    //     0x76a554: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x76a558: ldur            x1, [fp, #-0x10]
    // 0x76a55c: r0 = size()
    //     0x76a55c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76a560: mov             x2, x0
    // 0x76a564: ldur            x0, [fp, #-0x10]
    // 0x76a568: stur            x2, [fp, #-0x18]
    // 0x76a56c: LoadField: r1 = r0->field_57
    //     0x76a56c: ldur            w1, [x0, #0x57]
    // 0x76a570: DecompressPointer r1
    //     0x76a570: add             x1, x1, HEAP, lsl #32
    // 0x76a574: cmp             w1, NULL
    // 0x76a578: b.eq            #0x76a5f4
    // 0x76a57c: r0 = size()
    //     0x76a57c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76a580: ldur            x1, [fp, #-0x18]
    // 0x76a584: mov             x2, x0
    // 0x76a588: r0 = -()
    //     0x76a588: bl              #0x618814  ; [dart:ui] Size::-
    // 0x76a58c: mov             x2, x0
    // 0x76a590: r1 = Instance_Alignment
    //     0x76a590: add             x1, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x76a594: ldr             x1, [x1, #0x898]
    // 0x76a598: r0 = alongOffset()
    //     0x76a598: bl              #0x73c5c8  ; [package:flutter/src/painting/alignment.dart] Alignment::alongOffset
    // 0x76a59c: ldur            x1, [fp, #-8]
    // 0x76a5a0: StoreField: r1->field_7 = r0
    //     0x76a5a0: stur            w0, [x1, #7]
    //     0x76a5a4: ldurb           w16, [x1, #-1]
    //     0x76a5a8: ldurb           w17, [x0, #-1]
    //     0x76a5ac: and             x16, x17, x16, lsr #2
    //     0x76a5b0: tst             x16, HEAP, lsr #32
    //     0x76a5b4: b.eq            #0x76a5bc
    //     0x76a5b8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76a5bc: r0 = Null
    //     0x76a5bc: mov             x0, NULL
    // 0x76a5c0: LeaveFrame
    //     0x76a5c0: mov             SP, fp
    //     0x76a5c4: ldp             fp, lr, [SP], #0x10
    // 0x76a5c8: ret
    //     0x76a5c8: ret             
    // 0x76a5cc: r0 = StateError()
    //     0x76a5cc: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76a5d0: mov             x1, x0
    // 0x76a5d4: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76a5d4: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76a5d8: StoreField: r1->field_b = r0
    //     0x76a5d8: stur            w0, [x1, #0xb]
    // 0x76a5dc: mov             x0, x1
    // 0x76a5e0: r0 = Throw()
    //     0x76a5e0: bl              #0xec04b8  ; ThrowStub
    // 0x76a5e4: brk             #0
    // 0x76a5e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76a5e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76a5ec: b               #0x76a488
    // 0x76a5f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76a5f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76a5f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76a5f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ hitTest(/* No info */) {
    // ** addr: 0x89ab34, size: 0xf8
    // 0x89ab34: EnterFrame
    //     0x89ab34: stp             fp, lr, [SP, #-0x10]!
    //     0x89ab38: mov             fp, SP
    // 0x89ab3c: AllocStack(0x20)
    //     0x89ab3c: sub             SP, SP, #0x20
    // 0x89ab40: SetupParameters(_RenderInputPadding this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x89ab40: stur            x1, [fp, #-8]
    //     0x89ab44: stur            x2, [fp, #-0x10]
    //     0x89ab48: stur            x3, [fp, #-0x18]
    // 0x89ab4c: CheckStackOverflow
    //     0x89ab4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89ab50: cmp             SP, x16
    //     0x89ab54: b.ls            #0x89ac20
    // 0x89ab58: r1 = 2
    //     0x89ab58: movz            x1, #0x2
    // 0x89ab5c: r0 = AllocateContext()
    //     0x89ab5c: bl              #0xec126c  ; AllocateContextStub
    // 0x89ab60: mov             x4, x0
    // 0x89ab64: ldur            x0, [fp, #-8]
    // 0x89ab68: stur            x4, [fp, #-0x20]
    // 0x89ab6c: StoreField: r4->field_f = r0
    //     0x89ab6c: stur            w0, [x4, #0xf]
    // 0x89ab70: mov             x1, x0
    // 0x89ab74: ldur            x2, [fp, #-0x10]
    // 0x89ab78: ldur            x3, [fp, #-0x18]
    // 0x89ab7c: r0 = hitTest()
    //     0x89ab7c: bl              #0x89afdc  ; [package:flutter/src/rendering/box.dart] RenderBox::hitTest
    // 0x89ab80: tbnz            w0, #4, #0x89ab94
    // 0x89ab84: r0 = true
    //     0x89ab84: add             x0, NULL, #0x20  ; true
    // 0x89ab88: LeaveFrame
    //     0x89ab88: mov             SP, fp
    //     0x89ab8c: ldp             fp, lr, [SP], #0x10
    // 0x89ab90: ret
    //     0x89ab90: ret             
    // 0x89ab94: ldur            x0, [fp, #-8]
    // 0x89ab98: ldur            x2, [fp, #-0x20]
    // 0x89ab9c: LoadField: r1 = r0->field_57
    //     0x89ab9c: ldur            w1, [x0, #0x57]
    // 0x89aba0: DecompressPointer r1
    //     0x89aba0: add             x1, x1, HEAP, lsl #32
    // 0x89aba4: cmp             w1, NULL
    // 0x89aba8: b.eq            #0x89ac28
    // 0x89abac: r0 = size()
    //     0x89abac: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x89abb0: mov             x1, x0
    // 0x89abb4: r2 = Instance_Offset
    //     0x89abb4: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x89abb8: r0 = center()
    //     0x89abb8: bl              #0x7dc26c  ; [dart:ui] Size::center
    // 0x89abbc: mov             x3, x0
    // 0x89abc0: ldur            x2, [fp, #-0x20]
    // 0x89abc4: stur            x3, [fp, #-8]
    // 0x89abc8: StoreField: r2->field_13 = r0
    //     0x89abc8: stur            w0, [x2, #0x13]
    //     0x89abcc: ldurb           w16, [x2, #-1]
    //     0x89abd0: ldurb           w17, [x0, #-1]
    //     0x89abd4: and             x16, x17, x16, lsr #2
    //     0x89abd8: tst             x16, HEAP, lsr #32
    //     0x89abdc: b.eq            #0x89abe4
    //     0x89abe0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x89abe4: mov             x1, x3
    // 0x89abe8: r0 = forceToPoint()
    //     0x89abe8: bl              #0x89ac2c  ; [package:flutter/src/painting/matrix_utils.dart] MatrixUtils::forceToPoint
    // 0x89abec: ldur            x2, [fp, #-0x20]
    // 0x89abf0: r1 = Function '<anonymous closure>':.
    //     0x89abf0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55058] AnonymousClosure: (0x89ad0c), in [package:flutter/src/material/button_style_button.dart] _RenderInputPadding::hitTest (0x89ab34)
    //     0x89abf4: ldr             x1, [x1, #0x58]
    // 0x89abf8: stur            x0, [fp, #-0x18]
    // 0x89abfc: r0 = AllocateClosure()
    //     0x89abfc: bl              #0xec1630  ; AllocateClosureStub
    // 0x89ac00: ldur            x1, [fp, #-0x10]
    // 0x89ac04: mov             x2, x0
    // 0x89ac08: ldur            x3, [fp, #-8]
    // 0x89ac0c: ldur            x5, [fp, #-0x18]
    // 0x89ac10: r0 = addWithRawTransform()
    //     0x89ac10: bl              #0x7fc4f0  ; [package:flutter/src/rendering/box.dart] BoxHitTestResult::addWithRawTransform
    // 0x89ac14: LeaveFrame
    //     0x89ac14: mov             SP, fp
    //     0x89ac18: ldp             fp, lr, [SP], #0x10
    // 0x89ac1c: ret
    //     0x89ac1c: ret             
    // 0x89ac20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89ac20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89ac24: b               #0x89ab58
    // 0x89ac28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x89ac28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, BoxHitTestResult, Offset) {
    // ** addr: 0x89ad0c, size: 0x78
    // 0x89ad0c: EnterFrame
    //     0x89ad0c: stp             fp, lr, [SP, #-0x10]!
    //     0x89ad10: mov             fp, SP
    // 0x89ad14: ldr             x0, [fp, #0x20]
    // 0x89ad18: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x89ad18: ldur            w1, [x0, #0x17]
    // 0x89ad1c: DecompressPointer r1
    //     0x89ad1c: add             x1, x1, HEAP, lsl #32
    // 0x89ad20: CheckStackOverflow
    //     0x89ad20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89ad24: cmp             SP, x16
    //     0x89ad28: b.ls            #0x89ad78
    // 0x89ad2c: LoadField: r0 = r1->field_f
    //     0x89ad2c: ldur            w0, [x1, #0xf]
    // 0x89ad30: DecompressPointer r0
    //     0x89ad30: add             x0, x0, HEAP, lsl #32
    // 0x89ad34: LoadField: r2 = r0->field_57
    //     0x89ad34: ldur            w2, [x0, #0x57]
    // 0x89ad38: DecompressPointer r2
    //     0x89ad38: add             x2, x2, HEAP, lsl #32
    // 0x89ad3c: cmp             w2, NULL
    // 0x89ad40: b.eq            #0x89ad80
    // 0x89ad44: LoadField: r3 = r1->field_13
    //     0x89ad44: ldur            w3, [x1, #0x13]
    // 0x89ad48: DecompressPointer r3
    //     0x89ad48: add             x3, x3, HEAP, lsl #32
    // 0x89ad4c: r0 = LoadClassIdInstr(r2)
    //     0x89ad4c: ldur            x0, [x2, #-1]
    //     0x89ad50: ubfx            x0, x0, #0xc, #0x14
    // 0x89ad54: mov             x1, x2
    // 0x89ad58: ldr             x2, [fp, #0x18]
    // 0x89ad5c: r0 = GDT[cid_x0 + 0xdf93]()
    //     0x89ad5c: movz            x17, #0xdf93
    //     0x89ad60: add             lr, x0, x17
    //     0x89ad64: ldr             lr, [x21, lr, lsl #3]
    //     0x89ad68: blr             lr
    // 0x89ad6c: LeaveFrame
    //     0x89ad6c: mov             SP, fp
    //     0x89ad70: ldp             fp, lr, [SP], #0x10
    // 0x89ad74: ret
    //     0x89ad74: ret             
    // 0x89ad78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89ad78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89ad7c: b               #0x89ad2c
    // 0x89ad80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x89ad80: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  set _ minSize=(/* No info */) {
    // ** addr: 0xc6d390, size: 0x88
    // 0xc6d390: EnterFrame
    //     0xc6d390: stp             fp, lr, [SP, #-0x10]!
    //     0xc6d394: mov             fp, SP
    // 0xc6d398: mov             x0, x2
    // 0xc6d39c: CheckStackOverflow
    //     0xc6d39c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6d3a0: cmp             SP, x16
    //     0xc6d3a4: b.ls            #0xc6d410
    // 0xc6d3a8: LoadField: r2 = r1->field_5b
    //     0xc6d3a8: ldur            w2, [x1, #0x5b]
    // 0xc6d3ac: DecompressPointer r2
    //     0xc6d3ac: add             x2, x2, HEAP, lsl #32
    // 0xc6d3b0: LoadField: d0 = r0->field_7
    //     0xc6d3b0: ldur            d0, [x0, #7]
    // 0xc6d3b4: LoadField: d1 = r2->field_7
    //     0xc6d3b4: ldur            d1, [x2, #7]
    // 0xc6d3b8: fcmp            d0, d1
    // 0xc6d3bc: b.ne            #0xc6d3e0
    // 0xc6d3c0: LoadField: d0 = r0->field_f
    //     0xc6d3c0: ldur            d0, [x0, #0xf]
    // 0xc6d3c4: LoadField: d1 = r2->field_f
    //     0xc6d3c4: ldur            d1, [x2, #0xf]
    // 0xc6d3c8: fcmp            d0, d1
    // 0xc6d3cc: b.ne            #0xc6d3e0
    // 0xc6d3d0: r0 = Null
    //     0xc6d3d0: mov             x0, NULL
    // 0xc6d3d4: LeaveFrame
    //     0xc6d3d4: mov             SP, fp
    //     0xc6d3d8: ldp             fp, lr, [SP], #0x10
    // 0xc6d3dc: ret
    //     0xc6d3dc: ret             
    // 0xc6d3e0: StoreField: r1->field_5b = r0
    //     0xc6d3e0: stur            w0, [x1, #0x5b]
    //     0xc6d3e4: ldurb           w16, [x1, #-1]
    //     0xc6d3e8: ldurb           w17, [x0, #-1]
    //     0xc6d3ec: and             x16, x17, x16, lsr #2
    //     0xc6d3f0: tst             x16, HEAP, lsr #32
    //     0xc6d3f4: b.eq            #0xc6d3fc
    //     0xc6d3f8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6d3fc: r0 = markNeedsLayout()
    //     0xc6d3fc: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6d400: r0 = Null
    //     0xc6d400: mov             x0, NULL
    // 0xc6d404: LeaveFrame
    //     0xc6d404: mov             SP, fp
    //     0xc6d408: ldp             fp, lr, [SP], #0x10
    // 0xc6d40c: ret
    //     0xc6d40c: ret             
    // 0xc6d410: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6d410: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6d414: b               #0xc6d3a8
  }
}

// class id: 3984, size: 0xc, field offset: 0x8
//   const constructor, 
class _MouseCursor extends WidgetStateMouseCursor {

  _ resolve(/* No info */) {
    // ** addr: 0xd82460, size: 0x50
    // 0xd82460: EnterFrame
    //     0xd82460: stp             fp, lr, [SP, #-0x10]!
    //     0xd82464: mov             fp, SP
    // 0xd82468: AllocStack(0x10)
    //     0xd82468: sub             SP, SP, #0x10
    // 0xd8246c: CheckStackOverflow
    //     0xd8246c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd82470: cmp             SP, x16
    //     0xd82474: b.ls            #0xd824a4
    // 0xd82478: LoadField: r0 = r1->field_7
    //     0xd82478: ldur            w0, [x1, #7]
    // 0xd8247c: DecompressPointer r0
    //     0xd8247c: add             x0, x0, HEAP, lsl #32
    // 0xd82480: stp             x2, x0, [SP]
    // 0xd82484: ClosureCall
    //     0xd82484: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xd82488: ldur            x2, [x0, #0x1f]
    //     0xd8248c: blr             x2
    // 0xd82490: cmp             w0, NULL
    // 0xd82494: b.eq            #0xd824ac
    // 0xd82498: LeaveFrame
    //     0xd82498: mov             SP, fp
    //     0xd8249c: ldp             fp, lr, [SP], #0x10
    // 0xd824a0: ret
    //     0xd824a0: ret             
    // 0xd824a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd824a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd824a8: b               #0xd82478
    // 0xd824ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd824ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4320, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __ButtonStyleState&State&TickerProviderStateMixin extends State<dynamic>
     with TickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6f2f8c, size: 0x184
    // 0x6f2f8c: EnterFrame
    //     0x6f2f8c: stp             fp, lr, [SP, #-0x10]!
    //     0x6f2f90: mov             fp, SP
    // 0x6f2f94: AllocStack(0x20)
    //     0x6f2f94: sub             SP, SP, #0x20
    // 0x6f2f98: SetupParameters(__ButtonStyleState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6f2f98: mov             x0, x1
    //     0x6f2f9c: stur            x1, [fp, #-8]
    //     0x6f2fa0: stur            x2, [fp, #-0x10]
    // 0x6f2fa4: CheckStackOverflow
    //     0x6f2fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f2fa8: cmp             SP, x16
    //     0x6f2fac: b.ls            #0x6f3100
    // 0x6f2fb0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6f2fb0: ldur            w1, [x0, #0x17]
    // 0x6f2fb4: DecompressPointer r1
    //     0x6f2fb4: add             x1, x1, HEAP, lsl #32
    // 0x6f2fb8: cmp             w1, NULL
    // 0x6f2fbc: b.ne            #0x6f2fc8
    // 0x6f2fc0: mov             x1, x0
    // 0x6f2fc4: r0 = _updateTickerModeNotifier()
    //     0x6f2fc4: bl              #0x6f3138  ; [package:flutter/src/material/button_style_button.dart] __ButtonStyleState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6f2fc8: ldur            x0, [fp, #-8]
    // 0x6f2fcc: LoadField: r1 = r0->field_13
    //     0x6f2fcc: ldur            w1, [x0, #0x13]
    // 0x6f2fd0: DecompressPointer r1
    //     0x6f2fd0: add             x1, x1, HEAP, lsl #32
    // 0x6f2fd4: cmp             w1, NULL
    // 0x6f2fd8: b.ne            #0x6f3070
    // 0x6f2fdc: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x6f2fdc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6f2fe0: ldr             x0, [x0, #0x778]
    //     0x6f2fe4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6f2fe8: cmp             w0, w16
    //     0x6f2fec: b.ne            #0x6f2ff8
    //     0x6f2ff0: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x6f2ff4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6f2ff8: r1 = <_WidgetTicker>
    //     0x6f2ff8: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8c0] TypeArguments: <_WidgetTicker>
    //     0x6f2ffc: ldr             x1, [x1, #0x8c0]
    // 0x6f3000: stur            x0, [fp, #-0x18]
    // 0x6f3004: r0 = _Set()
    //     0x6f3004: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x6f3008: mov             x1, x0
    // 0x6f300c: ldur            x0, [fp, #-0x18]
    // 0x6f3010: stur            x1, [fp, #-0x20]
    // 0x6f3014: StoreField: r1->field_1b = r0
    //     0x6f3014: stur            w0, [x1, #0x1b]
    // 0x6f3018: StoreField: r1->field_b = rZR
    //     0x6f3018: stur            wzr, [x1, #0xb]
    // 0x6f301c: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x6f301c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6f3020: ldr             x0, [x0, #0x780]
    //     0x6f3024: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6f3028: cmp             w0, w16
    //     0x6f302c: b.ne            #0x6f3038
    //     0x6f3030: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x6f3034: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6f3038: mov             x1, x0
    // 0x6f303c: ldur            x0, [fp, #-0x20]
    // 0x6f3040: StoreField: r0->field_f = r1
    //     0x6f3040: stur            w1, [x0, #0xf]
    // 0x6f3044: StoreField: r0->field_13 = rZR
    //     0x6f3044: stur            wzr, [x0, #0x13]
    // 0x6f3048: ArrayStore: r0[0] = rZR  ; List_4
    //     0x6f3048: stur            wzr, [x0, #0x17]
    // 0x6f304c: ldur            x1, [fp, #-8]
    // 0x6f3050: StoreField: r1->field_13 = r0
    //     0x6f3050: stur            w0, [x1, #0x13]
    //     0x6f3054: ldurb           w16, [x1, #-1]
    //     0x6f3058: ldurb           w17, [x0, #-1]
    //     0x6f305c: and             x16, x17, x16, lsr #2
    //     0x6f3060: tst             x16, HEAP, lsr #32
    //     0x6f3064: b.eq            #0x6f306c
    //     0x6f3068: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6f306c: b               #0x6f3074
    // 0x6f3070: mov             x1, x0
    // 0x6f3074: ldur            x0, [fp, #-0x10]
    // 0x6f3078: r0 = _WidgetTicker()
    //     0x6f3078: bl              #0x6f03ec  ; Allocate_WidgetTickerStub -> _WidgetTicker (size=0x20)
    // 0x6f307c: mov             x3, x0
    // 0x6f3080: ldur            x2, [fp, #-8]
    // 0x6f3084: stur            x3, [fp, #-0x18]
    // 0x6f3088: StoreField: r3->field_1b = r2
    //     0x6f3088: stur            w2, [x3, #0x1b]
    // 0x6f308c: r0 = false
    //     0x6f308c: add             x0, NULL, #0x30  ; false
    // 0x6f3090: StoreField: r3->field_b = r0
    //     0x6f3090: stur            w0, [x3, #0xb]
    // 0x6f3094: ldur            x0, [fp, #-0x10]
    // 0x6f3098: StoreField: r3->field_13 = r0
    //     0x6f3098: stur            w0, [x3, #0x13]
    // 0x6f309c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6f309c: ldur            w1, [x2, #0x17]
    // 0x6f30a0: DecompressPointer r1
    //     0x6f30a0: add             x1, x1, HEAP, lsl #32
    // 0x6f30a4: cmp             w1, NULL
    // 0x6f30a8: b.eq            #0x6f3108
    // 0x6f30ac: r0 = LoadClassIdInstr(r1)
    //     0x6f30ac: ldur            x0, [x1, #-1]
    //     0x6f30b0: ubfx            x0, x0, #0xc, #0x14
    // 0x6f30b4: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6f30b4: movz            x17, #0x276f
    //     0x6f30b8: movk            x17, #0x1, lsl #16
    //     0x6f30bc: add             lr, x0, x17
    //     0x6f30c0: ldr             lr, [x21, lr, lsl #3]
    //     0x6f30c4: blr             lr
    // 0x6f30c8: eor             x2, x0, #0x10
    // 0x6f30cc: ldur            x1, [fp, #-0x18]
    // 0x6f30d0: r0 = muted=()
    //     0x6f30d0: bl              #0x6efbf4  ; [package:flutter/src/scheduler/ticker.dart] Ticker::muted=
    // 0x6f30d4: ldur            x0, [fp, #-8]
    // 0x6f30d8: LoadField: r1 = r0->field_13
    //     0x6f30d8: ldur            w1, [x0, #0x13]
    // 0x6f30dc: DecompressPointer r1
    //     0x6f30dc: add             x1, x1, HEAP, lsl #32
    // 0x6f30e0: cmp             w1, NULL
    // 0x6f30e4: b.eq            #0x6f310c
    // 0x6f30e8: ldur            x2, [fp, #-0x18]
    // 0x6f30ec: r0 = add()
    //     0x6f30ec: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x6f30f0: ldur            x0, [fp, #-0x18]
    // 0x6f30f4: LeaveFrame
    //     0x6f30f4: mov             SP, fp
    //     0x6f30f8: ldp             fp, lr, [SP], #0x10
    // 0x6f30fc: ret
    //     0x6f30fc: ret             
    // 0x6f3100: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f3100: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f3104: b               #0x6f2fb0
    // 0x6f3108: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f3108: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6f310c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f310c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6f3138, size: 0x124
    // 0x6f3138: EnterFrame
    //     0x6f3138: stp             fp, lr, [SP, #-0x10]!
    //     0x6f313c: mov             fp, SP
    // 0x6f3140: AllocStack(0x18)
    //     0x6f3140: sub             SP, SP, #0x18
    // 0x6f3144: SetupParameters(__ButtonStyleState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6f3144: mov             x2, x1
    //     0x6f3148: stur            x1, [fp, #-8]
    // 0x6f314c: CheckStackOverflow
    //     0x6f314c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f3150: cmp             SP, x16
    //     0x6f3154: b.ls            #0x6f3250
    // 0x6f3158: LoadField: r1 = r2->field_f
    //     0x6f3158: ldur            w1, [x2, #0xf]
    // 0x6f315c: DecompressPointer r1
    //     0x6f315c: add             x1, x1, HEAP, lsl #32
    // 0x6f3160: cmp             w1, NULL
    // 0x6f3164: b.eq            #0x6f3258
    // 0x6f3168: r0 = getNotifier()
    //     0x6f3168: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6f316c: mov             x3, x0
    // 0x6f3170: ldur            x0, [fp, #-8]
    // 0x6f3174: stur            x3, [fp, #-0x18]
    // 0x6f3178: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6f3178: ldur            w4, [x0, #0x17]
    // 0x6f317c: DecompressPointer r4
    //     0x6f317c: add             x4, x4, HEAP, lsl #32
    // 0x6f3180: stur            x4, [fp, #-0x10]
    // 0x6f3184: cmp             w3, w4
    // 0x6f3188: b.ne            #0x6f319c
    // 0x6f318c: r0 = Null
    //     0x6f318c: mov             x0, NULL
    // 0x6f3190: LeaveFrame
    //     0x6f3190: mov             SP, fp
    //     0x6f3194: ldp             fp, lr, [SP], #0x10
    // 0x6f3198: ret
    //     0x6f3198: ret             
    // 0x6f319c: cmp             w4, NULL
    // 0x6f31a0: b.eq            #0x6f31e4
    // 0x6f31a4: mov             x2, x0
    // 0x6f31a8: r1 = Function '_updateTickers@364311458':.
    //     0x6f31a8: add             x1, PP, #0x44, lsl #12  ; [pp+0x446a0] AnonymousClosure: (0x6f325c), in [package:flutter/src/material/button_style_button.dart] __ButtonStyleState&State&TickerProviderStateMixin::_updateTickers (0x6f3294)
    //     0x6f31ac: ldr             x1, [x1, #0x6a0]
    // 0x6f31b0: r0 = AllocateClosure()
    //     0x6f31b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f31b4: ldur            x1, [fp, #-0x10]
    // 0x6f31b8: r2 = LoadClassIdInstr(r1)
    //     0x6f31b8: ldur            x2, [x1, #-1]
    //     0x6f31bc: ubfx            x2, x2, #0xc, #0x14
    // 0x6f31c0: mov             x16, x0
    // 0x6f31c4: mov             x0, x2
    // 0x6f31c8: mov             x2, x16
    // 0x6f31cc: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6f31cc: movz            x17, #0xbf5c
    //     0x6f31d0: add             lr, x0, x17
    //     0x6f31d4: ldr             lr, [x21, lr, lsl #3]
    //     0x6f31d8: blr             lr
    // 0x6f31dc: ldur            x0, [fp, #-8]
    // 0x6f31e0: ldur            x3, [fp, #-0x18]
    // 0x6f31e4: mov             x2, x0
    // 0x6f31e8: r1 = Function '_updateTickers@364311458':.
    //     0x6f31e8: add             x1, PP, #0x44, lsl #12  ; [pp+0x446a0] AnonymousClosure: (0x6f325c), in [package:flutter/src/material/button_style_button.dart] __ButtonStyleState&State&TickerProviderStateMixin::_updateTickers (0x6f3294)
    //     0x6f31ec: ldr             x1, [x1, #0x6a0]
    // 0x6f31f0: r0 = AllocateClosure()
    //     0x6f31f0: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f31f4: ldur            x3, [fp, #-0x18]
    // 0x6f31f8: r1 = LoadClassIdInstr(r3)
    //     0x6f31f8: ldur            x1, [x3, #-1]
    //     0x6f31fc: ubfx            x1, x1, #0xc, #0x14
    // 0x6f3200: mov             x2, x0
    // 0x6f3204: mov             x0, x1
    // 0x6f3208: mov             x1, x3
    // 0x6f320c: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6f320c: movz            x17, #0xc407
    //     0x6f3210: add             lr, x0, x17
    //     0x6f3214: ldr             lr, [x21, lr, lsl #3]
    //     0x6f3218: blr             lr
    // 0x6f321c: ldur            x0, [fp, #-0x18]
    // 0x6f3220: ldur            x1, [fp, #-8]
    // 0x6f3224: ArrayStore: r1[0] = r0  ; List_4
    //     0x6f3224: stur            w0, [x1, #0x17]
    //     0x6f3228: ldurb           w16, [x1, #-1]
    //     0x6f322c: ldurb           w17, [x0, #-1]
    //     0x6f3230: and             x16, x17, x16, lsr #2
    //     0x6f3234: tst             x16, HEAP, lsr #32
    //     0x6f3238: b.eq            #0x6f3240
    //     0x6f323c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6f3240: r0 = Null
    //     0x6f3240: mov             x0, NULL
    // 0x6f3244: LeaveFrame
    //     0x6f3244: mov             SP, fp
    //     0x6f3248: ldp             fp, lr, [SP], #0x10
    // 0x6f324c: ret
    //     0x6f324c: ret             
    // 0x6f3250: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f3250: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f3254: b               #0x6f3158
    // 0x6f3258: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f3258: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTickers(dynamic) {
    // ** addr: 0x6f325c, size: 0x38
    // 0x6f325c: EnterFrame
    //     0x6f325c: stp             fp, lr, [SP, #-0x10]!
    //     0x6f3260: mov             fp, SP
    // 0x6f3264: ldr             x0, [fp, #0x10]
    // 0x6f3268: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6f3268: ldur            w1, [x0, #0x17]
    // 0x6f326c: DecompressPointer r1
    //     0x6f326c: add             x1, x1, HEAP, lsl #32
    // 0x6f3270: CheckStackOverflow
    //     0x6f3270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f3274: cmp             SP, x16
    //     0x6f3278: b.ls            #0x6f328c
    // 0x6f327c: r0 = _updateTickers()
    //     0x6f327c: bl              #0x6f3294  ; [package:flutter/src/material/button_style_button.dart] __ButtonStyleState&State&TickerProviderStateMixin::_updateTickers
    // 0x6f3280: LeaveFrame
    //     0x6f3280: mov             SP, fp
    //     0x6f3284: ldp             fp, lr, [SP], #0x10
    // 0x6f3288: ret
    //     0x6f3288: ret             
    // 0x6f328c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f328c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f3290: b               #0x6f327c
  }
  _ _updateTickers(/* No info */) {
    // ** addr: 0x6f3294, size: 0x164
    // 0x6f3294: EnterFrame
    //     0x6f3294: stp             fp, lr, [SP, #-0x10]!
    //     0x6f3298: mov             fp, SP
    // 0x6f329c: AllocStack(0x20)
    //     0x6f329c: sub             SP, SP, #0x20
    // 0x6f32a0: SetupParameters(__ButtonStyleState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6f32a0: mov             x2, x1
    //     0x6f32a4: stur            x1, [fp, #-8]
    // 0x6f32a8: CheckStackOverflow
    //     0x6f32a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f32ac: cmp             SP, x16
    //     0x6f32b0: b.ls            #0x6f33e0
    // 0x6f32b4: LoadField: r0 = r2->field_13
    //     0x6f32b4: ldur            w0, [x2, #0x13]
    // 0x6f32b8: DecompressPointer r0
    //     0x6f32b8: add             x0, x0, HEAP, lsl #32
    // 0x6f32bc: cmp             w0, NULL
    // 0x6f32c0: b.eq            #0x6f33d0
    // 0x6f32c4: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6f32c4: ldur            w1, [x2, #0x17]
    // 0x6f32c8: DecompressPointer r1
    //     0x6f32c8: add             x1, x1, HEAP, lsl #32
    // 0x6f32cc: cmp             w1, NULL
    // 0x6f32d0: b.eq            #0x6f33e8
    // 0x6f32d4: r0 = LoadClassIdInstr(r1)
    //     0x6f32d4: ldur            x0, [x1, #-1]
    //     0x6f32d8: ubfx            x0, x0, #0xc, #0x14
    // 0x6f32dc: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6f32dc: movz            x17, #0x276f
    //     0x6f32e0: movk            x17, #0x1, lsl #16
    //     0x6f32e4: add             lr, x0, x17
    //     0x6f32e8: ldr             lr, [x21, lr, lsl #3]
    //     0x6f32ec: blr             lr
    // 0x6f32f0: eor             x2, x0, #0x10
    // 0x6f32f4: ldur            x0, [fp, #-8]
    // 0x6f32f8: stur            x2, [fp, #-0x10]
    // 0x6f32fc: LoadField: r1 = r0->field_13
    //     0x6f32fc: ldur            w1, [x0, #0x13]
    // 0x6f3300: DecompressPointer r1
    //     0x6f3300: add             x1, x1, HEAP, lsl #32
    // 0x6f3304: cmp             w1, NULL
    // 0x6f3308: b.eq            #0x6f33ec
    // 0x6f330c: r0 = iterator()
    //     0x6f330c: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x6f3310: stur            x0, [fp, #-0x18]
    // 0x6f3314: LoadField: r2 = r0->field_7
    //     0x6f3314: ldur            w2, [x0, #7]
    // 0x6f3318: DecompressPointer r2
    //     0x6f3318: add             x2, x2, HEAP, lsl #32
    // 0x6f331c: stur            x2, [fp, #-8]
    // 0x6f3320: ldur            x3, [fp, #-0x10]
    // 0x6f3324: CheckStackOverflow
    //     0x6f3324: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f3328: cmp             SP, x16
    //     0x6f332c: b.ls            #0x6f33f0
    // 0x6f3330: mov             x1, x0
    // 0x6f3334: r0 = moveNext()
    //     0x6f3334: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x6f3338: tbnz            w0, #4, #0x6f33d0
    // 0x6f333c: ldur            x3, [fp, #-0x18]
    // 0x6f3340: LoadField: r4 = r3->field_33
    //     0x6f3340: ldur            w4, [x3, #0x33]
    // 0x6f3344: DecompressPointer r4
    //     0x6f3344: add             x4, x4, HEAP, lsl #32
    // 0x6f3348: stur            x4, [fp, #-0x20]
    // 0x6f334c: cmp             w4, NULL
    // 0x6f3350: b.ne            #0x6f3384
    // 0x6f3354: mov             x0, x4
    // 0x6f3358: ldur            x2, [fp, #-8]
    // 0x6f335c: r1 = Null
    //     0x6f335c: mov             x1, NULL
    // 0x6f3360: cmp             w2, NULL
    // 0x6f3364: b.eq            #0x6f3384
    // 0x6f3368: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6f3368: ldur            w4, [x2, #0x17]
    // 0x6f336c: DecompressPointer r4
    //     0x6f336c: add             x4, x4, HEAP, lsl #32
    // 0x6f3370: r8 = X0
    //     0x6f3370: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6f3374: LoadField: r9 = r4->field_7
    //     0x6f3374: ldur            x9, [x4, #7]
    // 0x6f3378: r3 = Null
    //     0x6f3378: add             x3, PP, #0x44, lsl #12  ; [pp+0x44690] Null
    //     0x6f337c: ldr             x3, [x3, #0x690]
    // 0x6f3380: blr             x9
    // 0x6f3384: ldur            x2, [fp, #-0x10]
    // 0x6f3388: ldur            x0, [fp, #-0x20]
    // 0x6f338c: LoadField: r1 = r0->field_b
    //     0x6f338c: ldur            w1, [x0, #0xb]
    // 0x6f3390: DecompressPointer r1
    //     0x6f3390: add             x1, x1, HEAP, lsl #32
    // 0x6f3394: cmp             w2, w1
    // 0x6f3398: b.eq            #0x6f33c4
    // 0x6f339c: StoreField: r0->field_b = r2
    //     0x6f339c: stur            w2, [x0, #0xb]
    // 0x6f33a0: tbnz            w2, #4, #0x6f33b0
    // 0x6f33a4: mov             x1, x0
    // 0x6f33a8: r0 = unscheduleTick()
    //     0x6f33a8: bl              #0x656684  ; [package:flutter/src/scheduler/ticker.dart] Ticker::unscheduleTick
    // 0x6f33ac: b               #0x6f33c4
    // 0x6f33b0: mov             x1, x0
    // 0x6f33b4: r0 = shouldScheduleTick()
    //     0x6f33b4: bl              #0x655b90  ; [package:flutter/src/scheduler/ticker.dart] Ticker::shouldScheduleTick
    // 0x6f33b8: tbnz            w0, #4, #0x6f33c4
    // 0x6f33bc: ldur            x1, [fp, #-0x20]
    // 0x6f33c0: r0 = scheduleTick()
    //     0x6f33c0: bl              #0x655924  ; [package:flutter/src/scheduler/ticker.dart] Ticker::scheduleTick
    // 0x6f33c4: ldur            x0, [fp, #-0x18]
    // 0x6f33c8: ldur            x2, [fp, #-8]
    // 0x6f33cc: b               #0x6f3320
    // 0x6f33d0: r0 = Null
    //     0x6f33d0: mov             x0, NULL
    // 0x6f33d4: LeaveFrame
    //     0x6f33d4: mov             SP, fp
    //     0x6f33d8: ldp             fp, lr, [SP], #0x10
    // 0x6f33dc: ret
    //     0x6f33dc: ret             
    // 0x6f33e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f33e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f33e4: b               #0x6f32b4
    // 0x6f33e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f33e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6f33ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f33ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6f33f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f33f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f33f4: b               #0x6f3330
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7cfac, size: 0x94
    // 0xa7cfac: EnterFrame
    //     0xa7cfac: stp             fp, lr, [SP, #-0x10]!
    //     0xa7cfb0: mov             fp, SP
    // 0xa7cfb4: AllocStack(0x10)
    //     0xa7cfb4: sub             SP, SP, #0x10
    // 0xa7cfb8: SetupParameters(__ButtonStyleState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa7cfb8: mov             x0, x1
    //     0xa7cfbc: stur            x1, [fp, #-0x10]
    // 0xa7cfc0: CheckStackOverflow
    //     0xa7cfc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7cfc4: cmp             SP, x16
    //     0xa7cfc8: b.ls            #0xa7d038
    // 0xa7cfcc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa7cfcc: ldur            w3, [x0, #0x17]
    // 0xa7cfd0: DecompressPointer r3
    //     0xa7cfd0: add             x3, x3, HEAP, lsl #32
    // 0xa7cfd4: stur            x3, [fp, #-8]
    // 0xa7cfd8: cmp             w3, NULL
    // 0xa7cfdc: b.ne            #0xa7cfe8
    // 0xa7cfe0: mov             x1, x0
    // 0xa7cfe4: b               #0xa7d024
    // 0xa7cfe8: mov             x2, x0
    // 0xa7cfec: r1 = Function '_updateTickers@364311458':.
    //     0xa7cfec: add             x1, PP, #0x44, lsl #12  ; [pp+0x446a0] AnonymousClosure: (0x6f325c), in [package:flutter/src/material/button_style_button.dart] __ButtonStyleState&State&TickerProviderStateMixin::_updateTickers (0x6f3294)
    //     0xa7cff0: ldr             x1, [x1, #0x6a0]
    // 0xa7cff4: r0 = AllocateClosure()
    //     0xa7cff4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7cff8: ldur            x1, [fp, #-8]
    // 0xa7cffc: r2 = LoadClassIdInstr(r1)
    //     0xa7cffc: ldur            x2, [x1, #-1]
    //     0xa7d000: ubfx            x2, x2, #0xc, #0x14
    // 0xa7d004: mov             x16, x0
    // 0xa7d008: mov             x0, x2
    // 0xa7d00c: mov             x2, x16
    // 0xa7d010: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa7d010: movz            x17, #0xbf5c
    //     0xa7d014: add             lr, x0, x17
    //     0xa7d018: ldr             lr, [x21, lr, lsl #3]
    //     0xa7d01c: blr             lr
    // 0xa7d020: ldur            x1, [fp, #-0x10]
    // 0xa7d024: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa7d024: stur            NULL, [x1, #0x17]
    // 0xa7d028: r0 = Null
    //     0xa7d028: mov             x0, NULL
    // 0xa7d02c: LeaveFrame
    //     0xa7d02c: mov             SP, fp
    //     0xa7d030: ldp             fp, lr, [SP], #0x10
    // 0xa7d034: ret
    //     0xa7d034: ret             
    // 0xa7d038: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7d038: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7d03c: b               #0xa7cfcc
  }
  _ activate(/* No info */) {
    // ** addr: 0xa84df4, size: 0x48
    // 0xa84df4: EnterFrame
    //     0xa84df4: stp             fp, lr, [SP, #-0x10]!
    //     0xa84df8: mov             fp, SP
    // 0xa84dfc: AllocStack(0x8)
    //     0xa84dfc: sub             SP, SP, #8
    // 0xa84e00: SetupParameters(__ButtonStyleState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa84e00: mov             x0, x1
    //     0xa84e04: stur            x1, [fp, #-8]
    // 0xa84e08: CheckStackOverflow
    //     0xa84e08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa84e0c: cmp             SP, x16
    //     0xa84e10: b.ls            #0xa84e34
    // 0xa84e14: mov             x1, x0
    // 0xa84e18: r0 = _updateTickerModeNotifier()
    //     0xa84e18: bl              #0x6f3138  ; [package:flutter/src/material/button_style_button.dart] __ButtonStyleState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa84e1c: ldur            x1, [fp, #-8]
    // 0xa84e20: r0 = _updateTickers()
    //     0xa84e20: bl              #0x6f3294  ; [package:flutter/src/material/button_style_button.dart] __ButtonStyleState&State&TickerProviderStateMixin::_updateTickers
    // 0xa84e24: r0 = Null
    //     0xa84e24: mov             x0, NULL
    // 0xa84e28: LeaveFrame
    //     0xa84e28: mov             SP, fp
    //     0xa84e2c: ldp             fp, lr, [SP], #0x10
    // 0xa84e30: ret
    //     0xa84e30: ret             
    // 0xa84e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa84e34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa84e38: b               #0xa84e14
  }
}

// class id: 4321, size: 0x2c, field offset: 0x1c
class _ButtonStyleState extends __ButtonStyleState&State&TickerProviderStateMixin {

  _ initState(/* No info */) {
    // ** addr: 0x932930, size: 0x30
    // 0x932930: EnterFrame
    //     0x932930: stp             fp, lr, [SP, #-0x10]!
    //     0x932934: mov             fp, SP
    // 0x932938: CheckStackOverflow
    //     0x932938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93293c: cmp             SP, x16
    //     0x932940: b.ls            #0x932958
    // 0x932944: r0 = initStatesController()
    //     0x932944: bl              #0x932960  ; [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::initStatesController
    // 0x932948: r0 = Null
    //     0x932948: mov             x0, NULL
    // 0x93294c: LeaveFrame
    //     0x93294c: mov             SP, fp
    //     0x932950: ldp             fp, lr, [SP], #0x10
    // 0x932954: ret
    //     0x932954: ret             
    // 0x932958: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x932958: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93295c: b               #0x932944
  }
  _ initStatesController(/* No info */) {
    // ** addr: 0x932960, size: 0x164
    // 0x932960: EnterFrame
    //     0x932960: stp             fp, lr, [SP, #-0x10]!
    //     0x932964: mov             fp, SP
    // 0x932968: AllocStack(0x10)
    //     0x932968: sub             SP, SP, #0x10
    // 0x93296c: SetupParameters(_ButtonStyleState this /* r1 => r2, fp-0x8 */)
    //     0x93296c: mov             x2, x1
    //     0x932970: stur            x1, [fp, #-8]
    // 0x932974: CheckStackOverflow
    //     0x932974: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x932978: cmp             SP, x16
    //     0x93297c: b.ls            #0x932aa8
    // 0x932980: LoadField: r0 = r2->field_b
    //     0x932980: ldur            w0, [x2, #0xb]
    // 0x932984: DecompressPointer r0
    //     0x932984: add             x0, x0, HEAP, lsl #32
    // 0x932988: cmp             w0, NULL
    // 0x93298c: b.eq            #0x932ab0
    // 0x932990: LoadField: r1 = r0->field_2b
    //     0x932990: ldur            w1, [x0, #0x2b]
    // 0x932994: DecompressPointer r1
    //     0x932994: add             x1, x1, HEAP, lsl #32
    // 0x932998: cmp             w1, NULL
    // 0x93299c: b.ne            #0x9329e0
    // 0x9329a0: r1 = <Set<WidgetState>>
    //     0x9329a0: add             x1, PP, #0x43, lsl #12  ; [pp+0x43bf8] TypeArguments: <Set<WidgetState>>
    //     0x9329a4: ldr             x1, [x1, #0xbf8]
    // 0x9329a8: r0 = WidgetStatesController()
    //     0x9329a8: bl              #0x932ce0  ; AllocateWidgetStatesControllerStub -> WidgetStatesController (size=0x2c)
    // 0x9329ac: mov             x1, x0
    // 0x9329b0: stur            x0, [fp, #-0x10]
    // 0x9329b4: r0 = WidgetStatesController()
    //     0x9329b4: bl              #0x932b84  ; [package:flutter/src/widgets/widget_state.dart] WidgetStatesController::WidgetStatesController
    // 0x9329b8: ldur            x0, [fp, #-0x10]
    // 0x9329bc: ldur            x4, [fp, #-8]
    // 0x9329c0: StoreField: r4->field_27 = r0
    //     0x9329c0: stur            w0, [x4, #0x27]
    //     0x9329c4: ldurb           w16, [x4, #-1]
    //     0x9329c8: ldurb           w17, [x0, #-1]
    //     0x9329cc: and             x16, x17, x16, lsr #2
    //     0x9329d0: tst             x16, HEAP, lsr #32
    //     0x9329d4: b.eq            #0x9329dc
    //     0x9329d8: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x9329dc: b               #0x9329e4
    // 0x9329e0: mov             x4, x2
    // 0x9329e4: LoadField: r0 = r4->field_b
    //     0x9329e4: ldur            w0, [x4, #0xb]
    // 0x9329e8: DecompressPointer r0
    //     0x9329e8: add             x0, x0, HEAP, lsl #32
    // 0x9329ec: cmp             w0, NULL
    // 0x9329f0: b.eq            #0x932ab4
    // 0x9329f4: LoadField: r1 = r0->field_2b
    //     0x9329f4: ldur            w1, [x0, #0x2b]
    // 0x9329f8: DecompressPointer r1
    //     0x9329f8: add             x1, x1, HEAP, lsl #32
    // 0x9329fc: cmp             w1, NULL
    // 0x932a00: b.ne            #0x932a14
    // 0x932a04: LoadField: r1 = r4->field_27
    //     0x932a04: ldur            w1, [x4, #0x27]
    // 0x932a08: DecompressPointer r1
    //     0x932a08: add             x1, x1, HEAP, lsl #32
    // 0x932a0c: cmp             w1, NULL
    // 0x932a10: b.eq            #0x932ab8
    // 0x932a14: LoadField: r2 = r0->field_b
    //     0x932a14: ldur            w2, [x0, #0xb]
    // 0x932a18: DecompressPointer r2
    //     0x932a18: add             x2, x2, HEAP, lsl #32
    // 0x932a1c: cmp             w2, NULL
    // 0x932a20: b.eq            #0x932a2c
    // 0x932a24: r0 = true
    //     0x932a24: add             x0, NULL, #0x20  ; true
    // 0x932a28: b               #0x932a30
    // 0x932a2c: r0 = false
    //     0x932a2c: add             x0, NULL, #0x30  ; false
    // 0x932a30: eor             x3, x0, #0x10
    // 0x932a34: r2 = Instance_WidgetState
    //     0x932a34: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d190] Obj!WidgetState@e33841
    //     0x932a38: ldr             x2, [x2, #0x190]
    // 0x932a3c: r0 = update()
    //     0x932a3c: bl              #0x932b1c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStatesController::update
    // 0x932a40: ldur            x2, [fp, #-8]
    // 0x932a44: LoadField: r0 = r2->field_b
    //     0x932a44: ldur            w0, [x2, #0xb]
    // 0x932a48: DecompressPointer r0
    //     0x932a48: add             x0, x0, HEAP, lsl #32
    // 0x932a4c: cmp             w0, NULL
    // 0x932a50: b.eq            #0x932abc
    // 0x932a54: LoadField: r1 = r0->field_2b
    //     0x932a54: ldur            w1, [x0, #0x2b]
    // 0x932a58: DecompressPointer r1
    //     0x932a58: add             x1, x1, HEAP, lsl #32
    // 0x932a5c: cmp             w1, NULL
    // 0x932a60: b.ne            #0x932a78
    // 0x932a64: LoadField: r0 = r2->field_27
    //     0x932a64: ldur            w0, [x2, #0x27]
    // 0x932a68: DecompressPointer r0
    //     0x932a68: add             x0, x0, HEAP, lsl #32
    // 0x932a6c: cmp             w0, NULL
    // 0x932a70: b.eq            #0x932ac0
    // 0x932a74: b               #0x932a7c
    // 0x932a78: mov             x0, x1
    // 0x932a7c: stur            x0, [fp, #-0x10]
    // 0x932a80: r1 = Function 'handleStatesControllerChange':.
    //     0x932a80: add             x1, PP, #0x44, lsl #12  ; [pp+0x44658] AnonymousClosure: (0x932cec), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::handleStatesControllerChange (0x932d24)
    //     0x932a84: ldr             x1, [x1, #0x658]
    // 0x932a88: r0 = AllocateClosure()
    //     0x932a88: bl              #0xec1630  ; AllocateClosureStub
    // 0x932a8c: ldur            x1, [fp, #-0x10]
    // 0x932a90: mov             x2, x0
    // 0x932a94: r0 = addListener()
    //     0x932a94: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x932a98: r0 = Null
    //     0x932a98: mov             x0, NULL
    // 0x932a9c: LeaveFrame
    //     0x932a9c: mov             SP, fp
    //     0x932aa0: ldp             fp, lr, [SP], #0x10
    // 0x932aa4: ret
    //     0x932aa4: ret             
    // 0x932aa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x932aa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x932aac: b               #0x932980
    // 0x932ab0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x932ab0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x932ab4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x932ab4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x932ab8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x932ab8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x932abc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x932abc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x932ac0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x932ac0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ statesController(/* No info */) {
    // ** addr: 0x932ac4, size: 0x58
    // 0x932ac4: EnterFrame
    //     0x932ac4: stp             fp, lr, [SP, #-0x10]!
    //     0x932ac8: mov             fp, SP
    // 0x932acc: LoadField: r2 = r1->field_b
    //     0x932acc: ldur            w2, [x1, #0xb]
    // 0x932ad0: DecompressPointer r2
    //     0x932ad0: add             x2, x2, HEAP, lsl #32
    // 0x932ad4: cmp             w2, NULL
    // 0x932ad8: b.eq            #0x932b14
    // 0x932adc: LoadField: r3 = r2->field_2b
    //     0x932adc: ldur            w3, [x2, #0x2b]
    // 0x932ae0: DecompressPointer r3
    //     0x932ae0: add             x3, x3, HEAP, lsl #32
    // 0x932ae4: cmp             w3, NULL
    // 0x932ae8: b.ne            #0x932b04
    // 0x932aec: LoadField: r2 = r1->field_27
    //     0x932aec: ldur            w2, [x1, #0x27]
    // 0x932af0: DecompressPointer r2
    //     0x932af0: add             x2, x2, HEAP, lsl #32
    // 0x932af4: cmp             w2, NULL
    // 0x932af8: b.eq            #0x932b18
    // 0x932afc: mov             x0, x2
    // 0x932b00: b               #0x932b08
    // 0x932b04: mov             x0, x3
    // 0x932b08: LeaveFrame
    //     0x932b08: mov             SP, fp
    //     0x932b0c: ldp             fp, lr, [SP], #0x10
    // 0x932b10: ret
    //     0x932b10: ret             
    // 0x932b14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x932b14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x932b18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x932b18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void handleStatesControllerChange(dynamic) {
    // ** addr: 0x932cec, size: 0x38
    // 0x932cec: EnterFrame
    //     0x932cec: stp             fp, lr, [SP, #-0x10]!
    //     0x932cf0: mov             fp, SP
    // 0x932cf4: ldr             x0, [fp, #0x10]
    // 0x932cf8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x932cf8: ldur            w1, [x0, #0x17]
    // 0x932cfc: DecompressPointer r1
    //     0x932cfc: add             x1, x1, HEAP, lsl #32
    // 0x932d00: CheckStackOverflow
    //     0x932d00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x932d04: cmp             SP, x16
    //     0x932d08: b.ls            #0x932d1c
    // 0x932d0c: r0 = handleStatesControllerChange()
    //     0x932d0c: bl              #0x932d24  ; [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::handleStatesControllerChange
    // 0x932d10: LeaveFrame
    //     0x932d10: mov             SP, fp
    //     0x932d14: ldp             fp, lr, [SP], #0x10
    // 0x932d18: ret
    //     0x932d18: ret             
    // 0x932d1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x932d1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x932d20: b               #0x932d0c
  }
  _ handleStatesControllerChange(/* No info */) {
    // ** addr: 0x932d24, size: 0x54
    // 0x932d24: EnterFrame
    //     0x932d24: stp             fp, lr, [SP, #-0x10]!
    //     0x932d28: mov             fp, SP
    // 0x932d2c: AllocStack(0x8)
    //     0x932d2c: sub             SP, SP, #8
    // 0x932d30: SetupParameters(_ButtonStyleState this /* r1 => r0, fp-0x8 */)
    //     0x932d30: mov             x0, x1
    //     0x932d34: stur            x1, [fp, #-8]
    // 0x932d38: CheckStackOverflow
    //     0x932d38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x932d3c: cmp             SP, x16
    //     0x932d40: b.ls            #0x932d70
    // 0x932d44: r1 = Function '<anonymous closure>':.
    //     0x932d44: add             x1, PP, #0x44, lsl #12  ; [pp+0x44660] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x932d48: ldr             x1, [x1, #0x660]
    // 0x932d4c: r2 = Null
    //     0x932d4c: mov             x2, NULL
    // 0x932d50: r0 = AllocateClosure()
    //     0x932d50: bl              #0xec1630  ; AllocateClosureStub
    // 0x932d54: ldur            x1, [fp, #-8]
    // 0x932d58: mov             x2, x0
    // 0x932d5c: r0 = setState()
    //     0x932d5c: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x932d60: r0 = Null
    //     0x932d60: mov             x0, NULL
    // 0x932d64: LeaveFrame
    //     0x932d64: mov             SP, fp
    //     0x932d68: ldp             fp, lr, [SP], #0x10
    // 0x932d6c: ret
    //     0x932d6c: ret             
    // 0x932d70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x932d70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x932d74: b               #0x932d44
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9843cc, size: 0x274
    // 0x9843cc: EnterFrame
    //     0x9843cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9843d0: mov             fp, SP
    // 0x9843d4: AllocStack(0x18)
    //     0x9843d4: sub             SP, SP, #0x18
    // 0x9843d8: SetupParameters(_ButtonStyleState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9843d8: mov             x4, x1
    //     0x9843dc: mov             x3, x2
    //     0x9843e0: stur            x1, [fp, #-8]
    //     0x9843e4: stur            x2, [fp, #-0x10]
    // 0x9843e8: CheckStackOverflow
    //     0x9843e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9843ec: cmp             SP, x16
    //     0x9843f0: b.ls            #0x984620
    // 0x9843f4: mov             x0, x3
    // 0x9843f8: r2 = Null
    //     0x9843f8: mov             x2, NULL
    // 0x9843fc: r1 = Null
    //     0x9843fc: mov             x1, NULL
    // 0x984400: r4 = 60
    //     0x984400: movz            x4, #0x3c
    // 0x984404: branchIfSmi(r0, 0x984410)
    //     0x984404: tbz             w0, #0, #0x984410
    // 0x984408: r4 = LoadClassIdInstr(r0)
    //     0x984408: ldur            x4, [x0, #-1]
    //     0x98440c: ubfx            x4, x4, #0xc, #0x14
    // 0x984410: r17 = -4867
    //     0x984410: movn            x17, #0x1302
    // 0x984414: add             x4, x4, x17
    // 0x984418: cmp             x4, #6
    // 0x98441c: b.ls            #0x984434
    // 0x984420: r8 = ButtonStyleButton
    //     0x984420: add             x8, PP, #0x44, lsl #12  ; [pp+0x44668] Type: ButtonStyleButton
    //     0x984424: ldr             x8, [x8, #0x668]
    // 0x984428: r3 = Null
    //     0x984428: add             x3, PP, #0x44, lsl #12  ; [pp+0x44670] Null
    //     0x98442c: ldr             x3, [x3, #0x670]
    // 0x984430: r0 = ButtonStyleButton()
    //     0x984430: bl              #0x6f3110  ; IsType_ButtonStyleButton_Stub
    // 0x984434: ldur            x3, [fp, #-8]
    // 0x984438: LoadField: r2 = r3->field_7
    //     0x984438: ldur            w2, [x3, #7]
    // 0x98443c: DecompressPointer r2
    //     0x98443c: add             x2, x2, HEAP, lsl #32
    // 0x984440: ldur            x0, [fp, #-0x10]
    // 0x984444: r1 = Null
    //     0x984444: mov             x1, NULL
    // 0x984448: cmp             w2, NULL
    // 0x98444c: b.eq            #0x984470
    // 0x984450: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x984450: ldur            w4, [x2, #0x17]
    // 0x984454: DecompressPointer r4
    //     0x984454: add             x4, x4, HEAP, lsl #32
    // 0x984458: r8 = X0 bound StatefulWidget
    //     0x984458: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x98445c: ldr             x8, [x8, #0x7f8]
    // 0x984460: LoadField: r9 = r4->field_7
    //     0x984460: ldur            x9, [x4, #7]
    // 0x984464: r3 = Null
    //     0x984464: add             x3, PP, #0x44, lsl #12  ; [pp+0x44680] Null
    //     0x984468: ldr             x3, [x3, #0x680]
    // 0x98446c: blr             x9
    // 0x984470: ldur            x0, [fp, #-8]
    // 0x984474: LoadField: r1 = r0->field_b
    //     0x984474: ldur            w1, [x0, #0xb]
    // 0x984478: DecompressPointer r1
    //     0x984478: add             x1, x1, HEAP, lsl #32
    // 0x98447c: cmp             w1, NULL
    // 0x984480: b.eq            #0x984628
    // 0x984484: LoadField: r2 = r1->field_2b
    //     0x984484: ldur            w2, [x1, #0x2b]
    // 0x984488: DecompressPointer r2
    //     0x984488: add             x2, x2, HEAP, lsl #32
    // 0x98448c: ldur            x3, [fp, #-0x10]
    // 0x984490: LoadField: r4 = r3->field_2b
    //     0x984490: ldur            w4, [x3, #0x2b]
    // 0x984494: DecompressPointer r4
    //     0x984494: add             x4, x4, HEAP, lsl #32
    // 0x984498: stur            x4, [fp, #-0x18]
    // 0x98449c: cmp             w2, w4
    // 0x9844a0: b.eq            #0x984510
    // 0x9844a4: cmp             w4, NULL
    // 0x9844a8: b.eq            #0x9844cc
    // 0x9844ac: mov             x2, x0
    // 0x9844b0: r1 = Function 'handleStatesControllerChange':.
    //     0x9844b0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44658] AnonymousClosure: (0x932cec), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::handleStatesControllerChange (0x932d24)
    //     0x9844b4: ldr             x1, [x1, #0x658]
    // 0x9844b8: r0 = AllocateClosure()
    //     0x9844b8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9844bc: ldur            x1, [fp, #-0x18]
    // 0x9844c0: mov             x2, x0
    // 0x9844c4: r0 = removeListener()
    //     0x9844c4: bl              #0xa8a5dc  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x9844c8: ldur            x0, [fp, #-8]
    // 0x9844cc: LoadField: r1 = r0->field_b
    //     0x9844cc: ldur            w1, [x0, #0xb]
    // 0x9844d0: DecompressPointer r1
    //     0x9844d0: add             x1, x1, HEAP, lsl #32
    // 0x9844d4: cmp             w1, NULL
    // 0x9844d8: b.eq            #0x98462c
    // 0x9844dc: LoadField: r2 = r1->field_2b
    //     0x9844dc: ldur            w2, [x1, #0x2b]
    // 0x9844e0: DecompressPointer r2
    //     0x9844e0: add             x2, x2, HEAP, lsl #32
    // 0x9844e4: cmp             w2, NULL
    // 0x9844e8: b.eq            #0x984508
    // 0x9844ec: LoadField: r1 = r0->field_27
    //     0x9844ec: ldur            w1, [x0, #0x27]
    // 0x9844f0: DecompressPointer r1
    //     0x9844f0: add             x1, x1, HEAP, lsl #32
    // 0x9844f4: cmp             w1, NULL
    // 0x9844f8: b.eq            #0x984504
    // 0x9844fc: r0 = dispose()
    //     0x9844fc: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0x984500: ldur            x0, [fp, #-8]
    // 0x984504: StoreField: r0->field_27 = rNULL
    //     0x984504: stur            NULL, [x0, #0x27]
    // 0x984508: mov             x1, x0
    // 0x98450c: r0 = initStatesController()
    //     0x98450c: bl              #0x932960  ; [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::initStatesController
    // 0x984510: ldur            x0, [fp, #-8]
    // 0x984514: LoadField: r1 = r0->field_b
    //     0x984514: ldur            w1, [x0, #0xb]
    // 0x984518: DecompressPointer r1
    //     0x984518: add             x1, x1, HEAP, lsl #32
    // 0x98451c: cmp             w1, NULL
    // 0x984520: b.eq            #0x984630
    // 0x984524: LoadField: r2 = r1->field_b
    //     0x984524: ldur            w2, [x1, #0xb]
    // 0x984528: DecompressPointer r2
    //     0x984528: add             x2, x2, HEAP, lsl #32
    // 0x98452c: cmp             w2, NULL
    // 0x984530: b.eq            #0x98453c
    // 0x984534: r4 = true
    //     0x984534: add             x4, NULL, #0x20  ; true
    // 0x984538: b               #0x984540
    // 0x98453c: r4 = false
    //     0x98453c: add             x4, NULL, #0x30  ; false
    // 0x984540: ldur            x3, [fp, #-0x10]
    // 0x984544: LoadField: r5 = r3->field_b
    //     0x984544: ldur            w5, [x3, #0xb]
    // 0x984548: DecompressPointer r5
    //     0x984548: add             x5, x5, HEAP, lsl #32
    // 0x98454c: cmp             w5, NULL
    // 0x984550: b.eq            #0x98455c
    // 0x984554: r3 = true
    //     0x984554: add             x3, NULL, #0x20  ; true
    // 0x984558: b               #0x984560
    // 0x98455c: r3 = false
    //     0x98455c: add             x3, NULL, #0x30  ; false
    // 0x984560: cmp             w4, w3
    // 0x984564: b.eq            #0x984610
    // 0x984568: LoadField: r3 = r1->field_2b
    //     0x984568: ldur            w3, [x1, #0x2b]
    // 0x98456c: DecompressPointer r3
    //     0x98456c: add             x3, x3, HEAP, lsl #32
    // 0x984570: cmp             w3, NULL
    // 0x984574: b.ne            #0x98458c
    // 0x984578: LoadField: r1 = r0->field_27
    //     0x984578: ldur            w1, [x0, #0x27]
    // 0x98457c: DecompressPointer r1
    //     0x98457c: add             x1, x1, HEAP, lsl #32
    // 0x984580: cmp             w1, NULL
    // 0x984584: b.eq            #0x984634
    // 0x984588: b               #0x984590
    // 0x98458c: mov             x1, x3
    // 0x984590: cmp             w2, NULL
    // 0x984594: b.eq            #0x9845a0
    // 0x984598: r2 = true
    //     0x984598: add             x2, NULL, #0x20  ; true
    // 0x98459c: b               #0x9845a4
    // 0x9845a0: r2 = false
    //     0x9845a0: add             x2, NULL, #0x30  ; false
    // 0x9845a4: eor             x3, x2, #0x10
    // 0x9845a8: r2 = Instance_WidgetState
    //     0x9845a8: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d190] Obj!WidgetState@e33841
    //     0x9845ac: ldr             x2, [x2, #0x190]
    // 0x9845b0: r0 = update()
    //     0x9845b0: bl              #0x932b1c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStatesController::update
    // 0x9845b4: ldur            x0, [fp, #-8]
    // 0x9845b8: LoadField: r1 = r0->field_b
    //     0x9845b8: ldur            w1, [x0, #0xb]
    // 0x9845bc: DecompressPointer r1
    //     0x9845bc: add             x1, x1, HEAP, lsl #32
    // 0x9845c0: cmp             w1, NULL
    // 0x9845c4: b.eq            #0x984638
    // 0x9845c8: LoadField: r2 = r1->field_b
    //     0x9845c8: ldur            w2, [x1, #0xb]
    // 0x9845cc: DecompressPointer r2
    //     0x9845cc: add             x2, x2, HEAP, lsl #32
    // 0x9845d0: cmp             w2, NULL
    // 0x9845d4: b.ne            #0x984610
    // 0x9845d8: LoadField: r2 = r1->field_2b
    //     0x9845d8: ldur            w2, [x1, #0x2b]
    // 0x9845dc: DecompressPointer r2
    //     0x9845dc: add             x2, x2, HEAP, lsl #32
    // 0x9845e0: cmp             w2, NULL
    // 0x9845e4: b.ne            #0x9845fc
    // 0x9845e8: LoadField: r1 = r0->field_27
    //     0x9845e8: ldur            w1, [x0, #0x27]
    // 0x9845ec: DecompressPointer r1
    //     0x9845ec: add             x1, x1, HEAP, lsl #32
    // 0x9845f0: cmp             w1, NULL
    // 0x9845f4: b.eq            #0x98463c
    // 0x9845f8: b               #0x984600
    // 0x9845fc: mov             x1, x2
    // 0x984600: r2 = Instance_WidgetState
    //     0x984600: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d0d8] Obj!WidgetState@e33881
    //     0x984604: ldr             x2, [x2, #0xd8]
    // 0x984608: r3 = false
    //     0x984608: add             x3, NULL, #0x30  ; false
    // 0x98460c: r0 = update()
    //     0x98460c: bl              #0x932b1c  ; [package:flutter/src/widgets/widget_state.dart] WidgetStatesController::update
    // 0x984610: r0 = Null
    //     0x984610: mov             x0, NULL
    // 0x984614: LeaveFrame
    //     0x984614: mov             SP, fp
    //     0x984618: ldp             fp, lr, [SP], #0x10
    // 0x98461c: ret
    //     0x98461c: ret             
    // 0x984620: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x984620: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x984624: b               #0x9843f4
    // 0x984628: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x984628: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98462c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98462c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x984630: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x984630: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x984634: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x984634: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x984638: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x984638: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98463c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98463c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0x9eb3cc, size: 0x1044
    // 0x9eb3cc: EnterFrame
    //     0x9eb3cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9eb3d0: mov             fp, SP
    // 0x9eb3d4: AllocStack(0x110)
    //     0x9eb3d4: sub             SP, SP, #0x110
    // 0x9eb3d8: SetupParameters(_ButtonStyleState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x9eb3d8: mov             x0, x1
    //     0x9eb3dc: stur            x1, [fp, #-8]
    //     0x9eb3e0: stur            x2, [fp, #-0x10]
    // 0x9eb3e4: CheckStackOverflow
    //     0x9eb3e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9eb3e8: cmp             SP, x16
    //     0x9eb3ec: b.ls            #0x9ec384
    // 0x9eb3f0: r1 = 5
    //     0x9eb3f0: movz            x1, #0x5
    // 0x9eb3f4: r0 = AllocateContext()
    //     0x9eb3f4: bl              #0xec126c  ; AllocateContextStub
    // 0x9eb3f8: mov             x4, x0
    // 0x9eb3fc: ldur            x3, [fp, #-8]
    // 0x9eb400: stur            x4, [fp, #-0x18]
    // 0x9eb404: StoreField: r4->field_f = r3
    //     0x9eb404: stur            w3, [x4, #0xf]
    // 0x9eb408: LoadField: r1 = r3->field_b
    //     0x9eb408: ldur            w1, [x3, #0xb]
    // 0x9eb40c: DecompressPointer r1
    //     0x9eb40c: add             x1, x1, HEAP, lsl #32
    // 0x9eb410: cmp             w1, NULL
    // 0x9eb414: b.eq            #0x9ec38c
    // 0x9eb418: LoadField: r0 = r1->field_1b
    //     0x9eb418: ldur            w0, [x1, #0x1b]
    // 0x9eb41c: DecompressPointer r0
    //     0x9eb41c: add             x0, x0, HEAP, lsl #32
    // 0x9eb420: StoreField: r4->field_13 = r0
    //     0x9eb420: stur            w0, [x4, #0x13]
    // 0x9eb424: r0 = LoadClassIdInstr(r1)
    //     0x9eb424: ldur            x0, [x1, #-1]
    //     0x9eb428: ubfx            x0, x0, #0xc, #0x14
    // 0x9eb42c: ldur            x2, [fp, #-0x10]
    // 0x9eb430: r0 = GDT[cid_x0 + 0x27aa]()
    //     0x9eb430: movz            x17, #0x27aa
    //     0x9eb434: add             lr, x0, x17
    //     0x9eb438: ldr             lr, [x21, lr, lsl #3]
    //     0x9eb43c: blr             lr
    // 0x9eb440: ldur            x3, [fp, #-0x18]
    // 0x9eb444: ArrayStore: r3[0] = r0  ; List_4
    //     0x9eb444: stur            w0, [x3, #0x17]
    //     0x9eb448: ldurb           w16, [x3, #-1]
    //     0x9eb44c: ldurb           w17, [x0, #-1]
    //     0x9eb450: and             x16, x17, x16, lsr #2
    //     0x9eb454: tst             x16, HEAP, lsr #32
    //     0x9eb458: b.eq            #0x9eb460
    //     0x9eb45c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x9eb460: ldur            x4, [fp, #-8]
    // 0x9eb464: LoadField: r1 = r4->field_b
    //     0x9eb464: ldur            w1, [x4, #0xb]
    // 0x9eb468: DecompressPointer r1
    //     0x9eb468: add             x1, x1, HEAP, lsl #32
    // 0x9eb46c: cmp             w1, NULL
    // 0x9eb470: b.eq            #0x9ec390
    // 0x9eb474: r0 = LoadClassIdInstr(r1)
    //     0x9eb474: ldur            x0, [x1, #-1]
    //     0x9eb478: ubfx            x0, x0, #0xc, #0x14
    // 0x9eb47c: ldur            x2, [fp, #-0x10]
    // 0x9eb480: r0 = GDT[cid_x0 + 0x27bb]()
    //     0x9eb480: movz            x17, #0x27bb
    //     0x9eb484: add             lr, x0, x17
    //     0x9eb488: ldr             lr, [x21, lr, lsl #3]
    //     0x9eb48c: blr             lr
    // 0x9eb490: ldur            x3, [fp, #-0x18]
    // 0x9eb494: StoreField: r3->field_1b = r0
    //     0x9eb494: stur            w0, [x3, #0x1b]
    //     0x9eb498: ldurb           w16, [x3, #-1]
    //     0x9eb49c: ldurb           w17, [x0, #-1]
    //     0x9eb4a0: and             x16, x17, x16, lsr #2
    //     0x9eb4a4: tst             x16, HEAP, lsr #32
    //     0x9eb4a8: b.eq            #0x9eb4b0
    //     0x9eb4ac: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x9eb4b0: mov             x2, x3
    // 0x9eb4b4: r1 = Function 'effectiveValue':.
    //     0x9eb4b4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44508] AnonymousClosure: (0x9ed004), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb4b8: ldr             x1, [x1, #0x508]
    // 0x9eb4bc: r0 = AllocateClosureGeneric()
    //     0x9eb4bc: bl              #0xec1550  ; AllocateClosureGenericStub
    // 0x9eb4c0: mov             x4, x0
    // 0x9eb4c4: ldur            x3, [fp, #-0x18]
    // 0x9eb4c8: stur            x4, [fp, #-0x10]
    // 0x9eb4cc: StoreField: r3->field_1f = r0
    //     0x9eb4cc: stur            w0, [x3, #0x1f]
    //     0x9eb4d0: ldurb           w16, [x3, #-1]
    //     0x9eb4d4: ldurb           w17, [x0, #-1]
    //     0x9eb4d8: and             x16, x17, x16, lsr #2
    //     0x9eb4dc: tst             x16, HEAP, lsr #32
    //     0x9eb4e0: b.eq            #0x9eb4e8
    //     0x9eb4e4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x9eb4e8: mov             x2, x3
    // 0x9eb4ec: r1 = Function 'resolve':.
    //     0x9eb4ec: add             x1, PP, #0x44, lsl #12  ; [pp+0x44510] AnonymousClosure: (0x9ece40), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb4f0: ldr             x1, [x1, #0x510]
    // 0x9eb4f4: r0 = AllocateClosureGeneric()
    //     0x9eb4f4: bl              #0xec1550  ; AllocateClosureGenericStub
    // 0x9eb4f8: r1 = Function '<anonymous closure>':.
    //     0x9eb4f8: add             x1, PP, #0x44, lsl #12  ; [pp+0x44518] AnonymousClosure: (0x9ecdf0), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb4fc: ldr             x1, [x1, #0x518]
    // 0x9eb500: r2 = Null
    //     0x9eb500: mov             x2, NULL
    // 0x9eb504: stur            x0, [fp, #-0x20]
    // 0x9eb508: r0 = AllocateClosure()
    //     0x9eb508: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb50c: r16 = <double?>
    //     0x9eb50c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b1c0] TypeArguments: <double?>
    //     0x9eb510: ldr             x16, [x16, #0x1c0]
    // 0x9eb514: ldur            lr, [fp, #-0x20]
    // 0x9eb518: stp             lr, x16, [SP, #8]
    // 0x9eb51c: str             x0, [SP]
    // 0x9eb520: ldur            x0, [fp, #-0x20]
    // 0x9eb524: ClosureCall
    //     0x9eb524: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb528: ldur            x2, [x0, #0x1f]
    //     0x9eb52c: blr             x2
    // 0x9eb530: r1 = Function '<anonymous closure>':.
    //     0x9eb530: add             x1, PP, #0x44, lsl #12  ; [pp+0x44520] AnonymousClosure: (0x9ecda0), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb534: ldr             x1, [x1, #0x520]
    // 0x9eb538: r2 = Null
    //     0x9eb538: mov             x2, NULL
    // 0x9eb53c: stur            x0, [fp, #-0x28]
    // 0x9eb540: r0 = AllocateClosure()
    //     0x9eb540: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb544: r16 = <TextStyle?>
    //     0x9eb544: add             x16, PP, #0x43, lsl #12  ; [pp+0x43990] TypeArguments: <TextStyle?>
    //     0x9eb548: ldr             x16, [x16, #0x990]
    // 0x9eb54c: ldur            lr, [fp, #-0x20]
    // 0x9eb550: stp             lr, x16, [SP, #8]
    // 0x9eb554: str             x0, [SP]
    // 0x9eb558: ldur            x0, [fp, #-0x20]
    // 0x9eb55c: ClosureCall
    //     0x9eb55c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb560: ldur            x2, [x0, #0x1f]
    //     0x9eb564: blr             x2
    // 0x9eb568: r1 = Function '<anonymous closure>':.
    //     0x9eb568: add             x1, PP, #0x44, lsl #12  ; [pp+0x44528] AnonymousClosure: (0x9ecd50), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb56c: ldr             x1, [x1, #0x528]
    // 0x9eb570: r2 = Null
    //     0x9eb570: mov             x2, NULL
    // 0x9eb574: stur            x0, [fp, #-0x30]
    // 0x9eb578: r0 = AllocateClosure()
    //     0x9eb578: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb57c: r16 = <Color?>
    //     0x9eb57c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x9eb580: ldr             x16, [x16, #0x98]
    // 0x9eb584: ldur            lr, [fp, #-0x20]
    // 0x9eb588: stp             lr, x16, [SP, #8]
    // 0x9eb58c: str             x0, [SP]
    // 0x9eb590: ldur            x0, [fp, #-0x20]
    // 0x9eb594: ClosureCall
    //     0x9eb594: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb598: ldur            x2, [x0, #0x1f]
    //     0x9eb59c: blr             x2
    // 0x9eb5a0: r1 = Function '<anonymous closure>':.
    //     0x9eb5a0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44530] AnonymousClosure: (0x9ecd00), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb5a4: ldr             x1, [x1, #0x530]
    // 0x9eb5a8: r2 = Null
    //     0x9eb5a8: mov             x2, NULL
    // 0x9eb5ac: stur            x0, [fp, #-0x38]
    // 0x9eb5b0: r0 = AllocateClosure()
    //     0x9eb5b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb5b4: r16 = <Color?>
    //     0x9eb5b4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x9eb5b8: ldr             x16, [x16, #0x98]
    // 0x9eb5bc: ldur            lr, [fp, #-0x20]
    // 0x9eb5c0: stp             lr, x16, [SP, #8]
    // 0x9eb5c4: str             x0, [SP]
    // 0x9eb5c8: ldur            x0, [fp, #-0x20]
    // 0x9eb5cc: ClosureCall
    //     0x9eb5cc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb5d0: ldur            x2, [x0, #0x1f]
    //     0x9eb5d4: blr             x2
    // 0x9eb5d8: r1 = Function '<anonymous closure>':.
    //     0x9eb5d8: add             x1, PP, #0x44, lsl #12  ; [pp+0x44538] AnonymousClosure: (0x9eccb0), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb5dc: ldr             x1, [x1, #0x538]
    // 0x9eb5e0: r2 = Null
    //     0x9eb5e0: mov             x2, NULL
    // 0x9eb5e4: stur            x0, [fp, #-0x40]
    // 0x9eb5e8: r0 = AllocateClosure()
    //     0x9eb5e8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb5ec: r16 = <Color?>
    //     0x9eb5ec: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x9eb5f0: ldr             x16, [x16, #0x98]
    // 0x9eb5f4: ldur            lr, [fp, #-0x20]
    // 0x9eb5f8: stp             lr, x16, [SP, #8]
    // 0x9eb5fc: str             x0, [SP]
    // 0x9eb600: ldur            x0, [fp, #-0x20]
    // 0x9eb604: ClosureCall
    //     0x9eb604: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb608: ldur            x2, [x0, #0x1f]
    //     0x9eb60c: blr             x2
    // 0x9eb610: r1 = Function '<anonymous closure>':.
    //     0x9eb610: add             x1, PP, #0x44, lsl #12  ; [pp+0x44540] AnonymousClosure: (0x9ecc60), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb614: ldr             x1, [x1, #0x540]
    // 0x9eb618: r2 = Null
    //     0x9eb618: mov             x2, NULL
    // 0x9eb61c: stur            x0, [fp, #-0x48]
    // 0x9eb620: r0 = AllocateClosure()
    //     0x9eb620: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb624: r16 = <Color?>
    //     0x9eb624: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x9eb628: ldr             x16, [x16, #0x98]
    // 0x9eb62c: ldur            lr, [fp, #-0x20]
    // 0x9eb630: stp             lr, x16, [SP, #8]
    // 0x9eb634: str             x0, [SP]
    // 0x9eb638: ldur            x0, [fp, #-0x20]
    // 0x9eb63c: ClosureCall
    //     0x9eb63c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb640: ldur            x2, [x0, #0x1f]
    //     0x9eb644: blr             x2
    // 0x9eb648: r1 = Function '<anonymous closure>':.
    //     0x9eb648: add             x1, PP, #0x44, lsl #12  ; [pp+0x44548] AnonymousClosure: (0x9ecc10), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb64c: ldr             x1, [x1, #0x548]
    // 0x9eb650: r2 = Null
    //     0x9eb650: mov             x2, NULL
    // 0x9eb654: stur            x0, [fp, #-0x50]
    // 0x9eb658: r0 = AllocateClosure()
    //     0x9eb658: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb65c: r16 = <EdgeInsetsGeometry?>
    //     0x9eb65c: add             x16, PP, #0x44, lsl #12  ; [pp+0x44550] TypeArguments: <EdgeInsetsGeometry?>
    //     0x9eb660: ldr             x16, [x16, #0x550]
    // 0x9eb664: ldur            lr, [fp, #-0x20]
    // 0x9eb668: stp             lr, x16, [SP, #8]
    // 0x9eb66c: str             x0, [SP]
    // 0x9eb670: ldur            x0, [fp, #-0x20]
    // 0x9eb674: ClosureCall
    //     0x9eb674: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb678: ldur            x2, [x0, #0x1f]
    //     0x9eb67c: blr             x2
    // 0x9eb680: r1 = Function '<anonymous closure>':.
    //     0x9eb680: add             x1, PP, #0x44, lsl #12  ; [pp+0x44558] AnonymousClosure: (0x9ecbc0), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb684: ldr             x1, [x1, #0x558]
    // 0x9eb688: r2 = Null
    //     0x9eb688: mov             x2, NULL
    // 0x9eb68c: stur            x0, [fp, #-0x58]
    // 0x9eb690: r0 = AllocateClosure()
    //     0x9eb690: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb694: r16 = <Size?>
    //     0x9eb694: add             x16, PP, #0x44, lsl #12  ; [pp+0x44560] TypeArguments: <Size?>
    //     0x9eb698: ldr             x16, [x16, #0x560]
    // 0x9eb69c: ldur            lr, [fp, #-0x20]
    // 0x9eb6a0: stp             lr, x16, [SP, #8]
    // 0x9eb6a4: str             x0, [SP]
    // 0x9eb6a8: ldur            x0, [fp, #-0x20]
    // 0x9eb6ac: ClosureCall
    //     0x9eb6ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb6b0: ldur            x2, [x0, #0x1f]
    //     0x9eb6b4: blr             x2
    // 0x9eb6b8: r1 = Function '<anonymous closure>':.
    //     0x9eb6b8: add             x1, PP, #0x44, lsl #12  ; [pp+0x44568] AnonymousClosure: (0x9ecb9c), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb6bc: ldr             x1, [x1, #0x568]
    // 0x9eb6c0: r2 = Null
    //     0x9eb6c0: mov             x2, NULL
    // 0x9eb6c4: stur            x0, [fp, #-0x60]
    // 0x9eb6c8: r0 = AllocateClosure()
    //     0x9eb6c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb6cc: r16 = <Size?>
    //     0x9eb6cc: add             x16, PP, #0x44, lsl #12  ; [pp+0x44560] TypeArguments: <Size?>
    //     0x9eb6d0: ldr             x16, [x16, #0x560]
    // 0x9eb6d4: ldur            lr, [fp, #-0x20]
    // 0x9eb6d8: stp             lr, x16, [SP, #8]
    // 0x9eb6dc: str             x0, [SP]
    // 0x9eb6e0: ldur            x0, [fp, #-0x20]
    // 0x9eb6e4: ClosureCall
    //     0x9eb6e4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb6e8: ldur            x2, [x0, #0x1f]
    //     0x9eb6ec: blr             x2
    // 0x9eb6f0: r1 = Function '<anonymous closure>':.
    //     0x9eb6f0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44570] AnonymousClosure: (0x9ecb4c), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb6f4: ldr             x1, [x1, #0x570]
    // 0x9eb6f8: r2 = Null
    //     0x9eb6f8: mov             x2, NULL
    // 0x9eb6fc: stur            x0, [fp, #-0x68]
    // 0x9eb700: r0 = AllocateClosure()
    //     0x9eb700: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb704: r16 = <Size?>
    //     0x9eb704: add             x16, PP, #0x44, lsl #12  ; [pp+0x44560] TypeArguments: <Size?>
    //     0x9eb708: ldr             x16, [x16, #0x560]
    // 0x9eb70c: ldur            lr, [fp, #-0x20]
    // 0x9eb710: stp             lr, x16, [SP, #8]
    // 0x9eb714: str             x0, [SP]
    // 0x9eb718: ldur            x0, [fp, #-0x20]
    // 0x9eb71c: ClosureCall
    //     0x9eb71c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb720: ldur            x2, [x0, #0x1f]
    //     0x9eb724: blr             x2
    // 0x9eb728: r1 = Function '<anonymous closure>':.
    //     0x9eb728: add             x1, PP, #0x44, lsl #12  ; [pp+0x44578] AnonymousClosure: (0x9ecafc), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb72c: ldr             x1, [x1, #0x578]
    // 0x9eb730: r2 = Null
    //     0x9eb730: mov             x2, NULL
    // 0x9eb734: stur            x0, [fp, #-0x70]
    // 0x9eb738: r0 = AllocateClosure()
    //     0x9eb738: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb73c: r16 = <Color?>
    //     0x9eb73c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x9eb740: ldr             x16, [x16, #0x98]
    // 0x9eb744: ldur            lr, [fp, #-0x20]
    // 0x9eb748: stp             lr, x16, [SP, #8]
    // 0x9eb74c: str             x0, [SP]
    // 0x9eb750: ldur            x0, [fp, #-0x20]
    // 0x9eb754: ClosureCall
    //     0x9eb754: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb758: ldur            x2, [x0, #0x1f]
    //     0x9eb75c: blr             x2
    // 0x9eb760: r1 = Function '<anonymous closure>':.
    //     0x9eb760: add             x1, PP, #0x44, lsl #12  ; [pp+0x44580] AnonymousClosure: (0x9ecaac), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb764: ldr             x1, [x1, #0x580]
    // 0x9eb768: r2 = Null
    //     0x9eb768: mov             x2, NULL
    // 0x9eb76c: stur            x0, [fp, #-0x78]
    // 0x9eb770: r0 = AllocateClosure()
    //     0x9eb770: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb774: r16 = <double?>
    //     0x9eb774: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b1c0] TypeArguments: <double?>
    //     0x9eb778: ldr             x16, [x16, #0x1c0]
    // 0x9eb77c: ldur            lr, [fp, #-0x20]
    // 0x9eb780: stp             lr, x16, [SP, #8]
    // 0x9eb784: str             x0, [SP]
    // 0x9eb788: ldur            x0, [fp, #-0x20]
    // 0x9eb78c: ClosureCall
    //     0x9eb78c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb790: ldur            x2, [x0, #0x1f]
    //     0x9eb794: blr             x2
    // 0x9eb798: r1 = Function '<anonymous closure>':.
    //     0x9eb798: add             x1, PP, #0x44, lsl #12  ; [pp+0x44588] AnonymousClosure: (0x9eca5c), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb79c: ldr             x1, [x1, #0x588]
    // 0x9eb7a0: r2 = Null
    //     0x9eb7a0: mov             x2, NULL
    // 0x9eb7a4: stur            x0, [fp, #-0x80]
    // 0x9eb7a8: r0 = AllocateClosure()
    //     0x9eb7a8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb7ac: r16 = <BorderSide?>
    //     0x9eb7ac: add             x16, PP, #0x44, lsl #12  ; [pp+0x44590] TypeArguments: <BorderSide?>
    //     0x9eb7b0: ldr             x16, [x16, #0x590]
    // 0x9eb7b4: ldur            lr, [fp, #-0x20]
    // 0x9eb7b8: stp             lr, x16, [SP, #8]
    // 0x9eb7bc: str             x0, [SP]
    // 0x9eb7c0: ldur            x0, [fp, #-0x20]
    // 0x9eb7c4: ClosureCall
    //     0x9eb7c4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb7c8: ldur            x2, [x0, #0x1f]
    //     0x9eb7cc: blr             x2
    // 0x9eb7d0: r1 = Function '<anonymous closure>':.
    //     0x9eb7d0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44598] AnonymousClosure: (0x9eca0c), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb7d4: ldr             x1, [x1, #0x598]
    // 0x9eb7d8: r2 = Null
    //     0x9eb7d8: mov             x2, NULL
    // 0x9eb7dc: stur            x0, [fp, #-0x88]
    // 0x9eb7e0: r0 = AllocateClosure()
    //     0x9eb7e0: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb7e4: r16 = <OutlinedBorder?>
    //     0x9eb7e4: add             x16, PP, #0x44, lsl #12  ; [pp+0x445a0] TypeArguments: <OutlinedBorder?>
    //     0x9eb7e8: ldr             x16, [x16, #0x5a0]
    // 0x9eb7ec: ldur            lr, [fp, #-0x20]
    // 0x9eb7f0: stp             lr, x16, [SP, #8]
    // 0x9eb7f4: str             x0, [SP]
    // 0x9eb7f8: ldur            x0, [fp, #-0x20]
    // 0x9eb7fc: ClosureCall
    //     0x9eb7fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb800: ldur            x2, [x0, #0x1f]
    //     0x9eb804: blr             x2
    // 0x9eb808: stur            x0, [fp, #-0x20]
    // 0x9eb80c: r0 = _MouseCursor()
    //     0x9eb80c: bl              #0x9ec5b4  ; Allocate_MouseCursorStub -> _MouseCursor (size=0xc)
    // 0x9eb810: ldur            x2, [fp, #-0x18]
    // 0x9eb814: r1 = Function '<anonymous closure>':.
    //     0x9eb814: add             x1, PP, #0x44, lsl #12  ; [pp+0x445a8] AnonymousClosure: (0x9ec8d0), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb818: ldr             x1, [x1, #0x5a8]
    // 0x9eb81c: stur            x0, [fp, #-0x90]
    // 0x9eb820: r0 = AllocateClosure()
    //     0x9eb820: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb824: mov             x1, x0
    // 0x9eb828: ldur            x0, [fp, #-0x90]
    // 0x9eb82c: StoreField: r0->field_7 = r1
    //     0x9eb82c: stur            w1, [x0, #7]
    // 0x9eb830: ldur            x2, [fp, #-0x18]
    // 0x9eb834: r1 = Function '<anonymous closure>':.
    //     0x9eb834: add             x1, PP, #0x44, lsl #12  ; [pp+0x445b0] AnonymousClosure: (0x9ec790), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb838: ldr             x1, [x1, #0x5b0]
    // 0x9eb83c: r0 = AllocateClosure()
    //     0x9eb83c: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb840: r16 = <Color?>
    //     0x9eb840: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x9eb844: ldr             x16, [x16, #0x98]
    // 0x9eb848: stp             x0, x16, [SP]
    // 0x9eb84c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9eb84c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9eb850: r0 = resolveWith()
    //     0x9eb850: bl              #0x9d84b0  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::resolveWith
    // 0x9eb854: r1 = Function '<anonymous closure>':.
    //     0x9eb854: add             x1, PP, #0x44, lsl #12  ; [pp+0x445b8] AnonymousClosure: (0x9ec740), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb858: ldr             x1, [x1, #0x5b8]
    // 0x9eb85c: r2 = Null
    //     0x9eb85c: mov             x2, NULL
    // 0x9eb860: stur            x0, [fp, #-0x98]
    // 0x9eb864: r0 = AllocateClosure()
    //     0x9eb864: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb868: r16 = <VisualDensity>
    //     0x9eb868: add             x16, PP, #0x44, lsl #12  ; [pp+0x445c0] TypeArguments: <VisualDensity>
    //     0x9eb86c: ldr             x16, [x16, #0x5c0]
    // 0x9eb870: ldur            lr, [fp, #-0x10]
    // 0x9eb874: stp             lr, x16, [SP, #8]
    // 0x9eb878: str             x0, [SP]
    // 0x9eb87c: ldur            x0, [fp, #-0x10]
    // 0x9eb880: ClosureCall
    //     0x9eb880: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb884: ldur            x2, [x0, #0x1f]
    //     0x9eb888: blr             x2
    // 0x9eb88c: r1 = Function '<anonymous closure>':.
    //     0x9eb88c: add             x1, PP, #0x44, lsl #12  ; [pp+0x445c8] AnonymousClosure: (0x9ec6f0), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb890: ldr             x1, [x1, #0x5c8]
    // 0x9eb894: r2 = Null
    //     0x9eb894: mov             x2, NULL
    // 0x9eb898: stur            x0, [fp, #-0xa0]
    // 0x9eb89c: r0 = AllocateClosure()
    //     0x9eb89c: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb8a0: r16 = <MaterialTapTargetSize>
    //     0x9eb8a0: add             x16, PP, #0x44, lsl #12  ; [pp+0x445d0] TypeArguments: <MaterialTapTargetSize>
    //     0x9eb8a4: ldr             x16, [x16, #0x5d0]
    // 0x9eb8a8: ldur            lr, [fp, #-0x10]
    // 0x9eb8ac: stp             lr, x16, [SP, #8]
    // 0x9eb8b0: str             x0, [SP]
    // 0x9eb8b4: ldur            x0, [fp, #-0x10]
    // 0x9eb8b8: ClosureCall
    //     0x9eb8b8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb8bc: ldur            x2, [x0, #0x1f]
    //     0x9eb8c0: blr             x2
    // 0x9eb8c4: r1 = Function '<anonymous closure>':.
    //     0x9eb8c4: add             x1, PP, #0x44, lsl #12  ; [pp+0x445d8] AnonymousClosure: (0x9ec6cc), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb8c8: ldr             x1, [x1, #0x5d8]
    // 0x9eb8cc: r2 = Null
    //     0x9eb8cc: mov             x2, NULL
    // 0x9eb8d0: stur            x0, [fp, #-0xa8]
    // 0x9eb8d4: r0 = AllocateClosure()
    //     0x9eb8d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb8d8: r16 = <Duration>
    //     0x9eb8d8: add             x16, PP, #0xc, lsl #12  ; [pp+0xc8f0] TypeArguments: <Duration>
    //     0x9eb8dc: ldr             x16, [x16, #0x8f0]
    // 0x9eb8e0: ldur            lr, [fp, #-0x10]
    // 0x9eb8e4: stp             lr, x16, [SP, #8]
    // 0x9eb8e8: str             x0, [SP]
    // 0x9eb8ec: ldur            x0, [fp, #-0x10]
    // 0x9eb8f0: ClosureCall
    //     0x9eb8f0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb8f4: ldur            x2, [x0, #0x1f]
    //     0x9eb8f8: blr             x2
    // 0x9eb8fc: r1 = Function '<anonymous closure>':.
    //     0x9eb8fc: add             x1, PP, #0x44, lsl #12  ; [pp+0x445e0] AnonymousClosure: (0x9ec6a8), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb900: ldr             x1, [x1, #0x5e0]
    // 0x9eb904: r2 = Null
    //     0x9eb904: mov             x2, NULL
    // 0x9eb908: stur            x0, [fp, #-0xb0]
    // 0x9eb90c: r0 = AllocateClosure()
    //     0x9eb90c: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb910: r16 = <bool>
    //     0x9eb910: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0x9eb914: ldur            lr, [fp, #-0x10]
    // 0x9eb918: stp             lr, x16, [SP, #8]
    // 0x9eb91c: str             x0, [SP]
    // 0x9eb920: ldur            x0, [fp, #-0x10]
    // 0x9eb924: ClosureCall
    //     0x9eb924: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb928: ldur            x2, [x0, #0x1f]
    //     0x9eb92c: blr             x2
    // 0x9eb930: r1 = Function '<anonymous closure>':.
    //     0x9eb930: add             x1, PP, #0x44, lsl #12  ; [pp+0x445e8] AnonymousClosure: (0x9ec684), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb934: ldr             x1, [x1, #0x5e8]
    // 0x9eb938: r2 = Null
    //     0x9eb938: mov             x2, NULL
    // 0x9eb93c: r0 = AllocateClosure()
    //     0x9eb93c: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb940: r16 = <AlignmentGeometry>
    //     0x9eb940: add             x16, PP, #0x44, lsl #12  ; [pp+0x445f0] TypeArguments: <AlignmentGeometry>
    //     0x9eb944: ldr             x16, [x16, #0x5f0]
    // 0x9eb948: ldur            lr, [fp, #-0x10]
    // 0x9eb94c: stp             lr, x16, [SP, #8]
    // 0x9eb950: str             x0, [SP]
    // 0x9eb954: ldur            x0, [fp, #-0x10]
    // 0x9eb958: ClosureCall
    //     0x9eb958: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb95c: ldur            x2, [x0, #0x1f]
    //     0x9eb960: blr             x2
    // 0x9eb964: mov             x2, x0
    // 0x9eb968: ldur            x0, [fp, #-0xa0]
    // 0x9eb96c: stur            x2, [fp, #-0xb8]
    // 0x9eb970: cmp             w0, NULL
    // 0x9eb974: b.eq            #0x9ec394
    // 0x9eb978: mov             x1, x0
    // 0x9eb97c: r0 = baseSizeAdjustment()
    //     0x9eb97c: bl              #0x7403f8  ; [package:flutter/src/material/theme_data.dart] VisualDensity::baseSizeAdjustment
    // 0x9eb980: r1 = Function '<anonymous closure>':.
    //     0x9eb980: add             x1, PP, #0x44, lsl #12  ; [pp+0x445f8] AnonymousClosure: (0x9ec634), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eb984: ldr             x1, [x1, #0x5f8]
    // 0x9eb988: r2 = Null
    //     0x9eb988: mov             x2, NULL
    // 0x9eb98c: stur            x0, [fp, #-0xc0]
    // 0x9eb990: r0 = AllocateClosure()
    //     0x9eb990: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb994: r16 = <InteractiveInkFeatureFactory>
    //     0x9eb994: add             x16, PP, #0x44, lsl #12  ; [pp+0x44600] TypeArguments: <InteractiveInkFeatureFactory>
    //     0x9eb998: ldr             x16, [x16, #0x600]
    // 0x9eb99c: ldur            lr, [fp, #-0x10]
    // 0x9eb9a0: stp             lr, x16, [SP, #8]
    // 0x9eb9a4: str             x0, [SP]
    // 0x9eb9a8: ldur            x0, [fp, #-0x10]
    // 0x9eb9ac: ClosureCall
    //     0x9eb9ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb9b0: ldur            x2, [x0, #0x1f]
    //     0x9eb9b4: blr             x2
    // 0x9eb9b8: r1 = Function '<anonymous closure>':.
    //     0x9eb9b8: add             x1, PP, #0x44, lsl #12  ; [pp+0x44608] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x9eb9bc: ldr             x1, [x1, #0x608]
    // 0x9eb9c0: r2 = Null
    //     0x9eb9c0: mov             x2, NULL
    // 0x9eb9c4: stur            x0, [fp, #-0xc8]
    // 0x9eb9c8: r0 = AllocateClosure()
    //     0x9eb9c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eb9cc: r16 = <(dynamic this, BuildContext, Set<WidgetState>, Widget?) => Widget>
    //     0x9eb9cc: add             x16, PP, #0x44, lsl #12  ; [pp+0x44610] TypeArguments: <(dynamic this, BuildContext, Set<WidgetState>, Widget?) => Widget>
    //     0x9eb9d0: ldr             x16, [x16, #0x610]
    // 0x9eb9d4: ldur            lr, [fp, #-0x10]
    // 0x9eb9d8: stp             lr, x16, [SP, #8]
    // 0x9eb9dc: str             x0, [SP]
    // 0x9eb9e0: ldur            x0, [fp, #-0x10]
    // 0x9eb9e4: ClosureCall
    //     0x9eb9e4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eb9e8: ldur            x2, [x0, #0x1f]
    //     0x9eb9ec: blr             x2
    // 0x9eb9f0: r1 = Function '<anonymous closure>':.
    //     0x9eb9f0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44618] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x9eb9f4: ldr             x1, [x1, #0x618]
    // 0x9eb9f8: r2 = Null
    //     0x9eb9f8: mov             x2, NULL
    // 0x9eb9fc: r0 = AllocateClosure()
    //     0x9eb9fc: bl              #0xec1630  ; AllocateClosureStub
    // 0x9eba00: r16 = <(dynamic this, BuildContext, Set<WidgetState>, Widget?) => Widget>
    //     0x9eba00: add             x16, PP, #0x44, lsl #12  ; [pp+0x44610] TypeArguments: <(dynamic this, BuildContext, Set<WidgetState>, Widget?) => Widget>
    //     0x9eba04: ldr             x16, [x16, #0x610]
    // 0x9eba08: ldur            lr, [fp, #-0x10]
    // 0x9eba0c: stp             lr, x16, [SP, #8]
    // 0x9eba10: str             x0, [SP]
    // 0x9eba14: ldur            x0, [fp, #-0x10]
    // 0x9eba18: ClosureCall
    //     0x9eba18: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9eba1c: ldur            x2, [x0, #0x1f]
    //     0x9eba20: blr             x2
    // 0x9eba24: ldur            x2, [fp, #-8]
    // 0x9eba28: LoadField: r0 = r2->field_b
    //     0x9eba28: ldur            w0, [x2, #0xb]
    // 0x9eba2c: DecompressPointer r0
    //     0x9eba2c: add             x0, x0, HEAP, lsl #32
    // 0x9eba30: cmp             w0, NULL
    // 0x9eba34: b.eq            #0x9ec398
    // 0x9eba38: LoadField: r1 = r0->field_1f
    //     0x9eba38: ldur            w1, [x0, #0x1f]
    // 0x9eba3c: DecompressPointer r1
    //     0x9eba3c: add             x1, x1, HEAP, lsl #32
    // 0x9eba40: cmp             w1, NULL
    // 0x9eba44: b.ne            #0x9eba54
    // 0x9eba48: r4 = Instance_Clip
    //     0x9eba48: add             x4, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x9eba4c: ldr             x4, [x4, #0x750]
    // 0x9eba50: b               #0x9eba58
    // 0x9eba54: mov             x4, x1
    // 0x9eba58: ldur            x3, [fp, #-0x60]
    // 0x9eba5c: ldur            x1, [fp, #-0x68]
    // 0x9eba60: ldur            x0, [fp, #-0x70]
    // 0x9eba64: stur            x4, [fp, #-0x10]
    // 0x9eba68: cmp             w3, NULL
    // 0x9eba6c: b.eq            #0x9ec39c
    // 0x9eba70: LoadField: d0 = r3->field_7
    //     0x9eba70: ldur            d0, [x3, #7]
    // 0x9eba74: stur            d0, [fp, #-0xf0]
    // 0x9eba78: LoadField: d1 = r3->field_f
    //     0x9eba78: ldur            d1, [x3, #0xf]
    // 0x9eba7c: stur            d1, [fp, #-0xe8]
    // 0x9eba80: cmp             w0, NULL
    // 0x9eba84: b.eq            #0x9ec3a0
    // 0x9eba88: LoadField: d2 = r0->field_7
    //     0x9eba88: ldur            d2, [x0, #7]
    // 0x9eba8c: stur            d2, [fp, #-0xe0]
    // 0x9eba90: LoadField: d3 = r0->field_f
    //     0x9eba90: ldur            d3, [x0, #0xf]
    // 0x9eba94: stur            d3, [fp, #-0xd8]
    // 0x9eba98: r0 = BoxConstraints()
    //     0x9eba98: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x9eba9c: ldur            d0, [fp, #-0xf0]
    // 0x9ebaa0: StoreField: r0->field_7 = d0
    //     0x9ebaa0: stur            d0, [x0, #7]
    // 0x9ebaa4: ldur            d0, [fp, #-0xe0]
    // 0x9ebaa8: StoreField: r0->field_f = d0
    //     0x9ebaa8: stur            d0, [x0, #0xf]
    // 0x9ebaac: ldur            d0, [fp, #-0xe8]
    // 0x9ebab0: ArrayStore: r0[0] = d0  ; List_8
    //     0x9ebab0: stur            d0, [x0, #0x17]
    // 0x9ebab4: ldur            d0, [fp, #-0xd8]
    // 0x9ebab8: StoreField: r0->field_1f = d0
    //     0x9ebab8: stur            d0, [x0, #0x1f]
    // 0x9ebabc: ldur            x1, [fp, #-0xa0]
    // 0x9ebac0: mov             x2, x0
    // 0x9ebac4: r0 = effectiveConstraints()
    //     0x9ebac4: bl              #0x9ec434  ; [package:flutter/src/material/theme_data.dart] VisualDensity::effectiveConstraints
    // 0x9ebac8: ldur            x2, [fp, #-0x68]
    // 0x9ebacc: stur            x0, [fp, #-0x60]
    // 0x9ebad0: cmp             w2, NULL
    // 0x9ebad4: b.eq            #0x9ebbb4
    // 0x9ebad8: mov             x1, x0
    // 0x9ebadc: r0 = constrain()
    //     0x9ebadc: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x9ebae0: stur            x0, [fp, #-0x68]
    // 0x9ebae4: LoadField: d0 = r0->field_7
    //     0x9ebae4: ldur            d0, [x0, #7]
    // 0x9ebae8: mov             x1, v0.d[0]
    // 0x9ebaec: and             x1, x1, #0x7fffffffffffffff
    // 0x9ebaf0: r17 = 9218868437227405312
    //     0x9ebaf0: orr             x17, xzr, #0x7ff0000000000000
    // 0x9ebaf4: cmp             x1, x17
    // 0x9ebaf8: b.eq            #0x9ebb44
    // 0x9ebafc: fcmp            d0, d0
    // 0x9ebb00: b.vs            #0x9ebb44
    // 0x9ebb04: r1 = inline_Allocate_Double()
    //     0x9ebb04: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x9ebb08: add             x1, x1, #0x10
    //     0x9ebb0c: cmp             x2, x1
    //     0x9ebb10: b.ls            #0x9ec3a4
    //     0x9ebb14: str             x1, [THR, #0x50]  ; THR::top
    //     0x9ebb18: sub             x1, x1, #0xf
    //     0x9ebb1c: movz            x2, #0xe15c
    //     0x9ebb20: movk            x2, #0x3, lsl #16
    //     0x9ebb24: stur            x2, [x1, #-1]
    // 0x9ebb28: StoreField: r1->field_7 = d0
    //     0x9ebb28: stur            d0, [x1, #7]
    // 0x9ebb2c: stp             x1, x1, [SP]
    // 0x9ebb30: ldur            x1, [fp, #-0x60]
    // 0x9ebb34: r4 = const [0, 0x3, 0x2, 0x1, maxWidth, 0x2, minWidth, 0x1, null]
    //     0x9ebb34: ldr             x4, [PP, #0x4970]  ; [pp+0x4970] List(9) [0, 0x3, 0x2, 0x1, "maxWidth", 0x2, "minWidth", 0x1, Null]
    // 0x9ebb38: r0 = copyWith()
    //     0x9ebb38: bl              #0x73d230  ; [package:flutter/src/rendering/box.dart] BoxConstraints::copyWith
    // 0x9ebb3c: mov             x1, x0
    // 0x9ebb40: b               #0x9ebb48
    // 0x9ebb44: ldur            x1, [fp, #-0x60]
    // 0x9ebb48: ldur            x0, [fp, #-0x68]
    // 0x9ebb4c: LoadField: d0 = r0->field_f
    //     0x9ebb4c: ldur            d0, [x0, #0xf]
    // 0x9ebb50: mov             x0, v0.d[0]
    // 0x9ebb54: and             x0, x0, #0x7fffffffffffffff
    // 0x9ebb58: r17 = 9218868437227405312
    //     0x9ebb58: orr             x17, xzr, #0x7ff0000000000000
    // 0x9ebb5c: cmp             x0, x17
    // 0x9ebb60: b.eq            #0x9ebba8
    // 0x9ebb64: fcmp            d0, d0
    // 0x9ebb68: b.vs            #0x9ebba8
    // 0x9ebb6c: r0 = inline_Allocate_Double()
    //     0x9ebb6c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x9ebb70: add             x0, x0, #0x10
    //     0x9ebb74: cmp             x2, x0
    //     0x9ebb78: b.ls            #0x9ec3c0
    //     0x9ebb7c: str             x0, [THR, #0x50]  ; THR::top
    //     0x9ebb80: sub             x0, x0, #0xf
    //     0x9ebb84: movz            x2, #0xe15c
    //     0x9ebb88: movk            x2, #0x3, lsl #16
    //     0x9ebb8c: stur            x2, [x0, #-1]
    // 0x9ebb90: StoreField: r0->field_7 = d0
    //     0x9ebb90: stur            d0, [x0, #7]
    // 0x9ebb94: stp             x0, x0, [SP]
    // 0x9ebb98: r4 = const [0, 0x3, 0x2, 0x1, maxHeight, 0x2, minHeight, 0x1, null]
    //     0x9ebb98: add             x4, PP, #0x44, lsl #12  ; [pp+0x44620] List(9) [0, 0x3, 0x2, 0x1, "maxHeight", 0x2, "minHeight", 0x1, Null]
    //     0x9ebb9c: ldr             x4, [x4, #0x620]
    // 0x9ebba0: r0 = copyWith()
    //     0x9ebba0: bl              #0x73d230  ; [package:flutter/src/rendering/box.dart] BoxConstraints::copyWith
    // 0x9ebba4: b               #0x9ebbac
    // 0x9ebba8: mov             x0, x1
    // 0x9ebbac: mov             x1, x0
    // 0x9ebbb0: b               #0x9ebbb8
    // 0x9ebbb4: ldur            x1, [fp, #-0x60]
    // 0x9ebbb8: ldur            x0, [fp, #-0xc0]
    // 0x9ebbbc: d0 = 0.000000
    //     0x9ebbbc: eor             v0.16b, v0.16b, v0.16b
    // 0x9ebbc0: stur            x1, [fp, #-0x60]
    // 0x9ebbc4: LoadField: d1 = r0->field_f
    //     0x9ebbc4: ldur            d1, [x0, #0xf]
    // 0x9ebbc8: stur            d1, [fp, #-0xe8]
    // 0x9ebbcc: LoadField: d2 = r0->field_7
    //     0x9ebbcc: ldur            d2, [x0, #7]
    // 0x9ebbd0: stur            d2, [fp, #-0xe0]
    // 0x9ebbd4: fcmp            d0, d2
    // 0x9ebbd8: b.le            #0x9ebbe4
    // 0x9ebbdc: d3 = 0.000000
    //     0x9ebbdc: eor             v3.16b, v3.16b, v3.16b
    // 0x9ebbe0: b               #0x9ebc18
    // 0x9ebbe4: fcmp            d2, d0
    // 0x9ebbe8: b.le            #0x9ebbf4
    // 0x9ebbec: mov             v3.16b, v2.16b
    // 0x9ebbf0: b               #0x9ebc18
    // 0x9ebbf4: fcmp            d0, d0
    // 0x9ebbf8: b.ne            #0x9ebc04
    // 0x9ebbfc: fadd            d3, d2, d0
    // 0x9ebc00: b               #0x9ebc18
    // 0x9ebc04: fcmp            d2, d2
    // 0x9ebc08: b.vc            #0x9ebc14
    // 0x9ebc0c: mov             v3.16b, v2.16b
    // 0x9ebc10: b               #0x9ebc18
    // 0x9ebc14: d3 = 0.000000
    //     0x9ebc14: eor             v3.16b, v3.16b, v3.16b
    // 0x9ebc18: ldur            x2, [fp, #-0x58]
    // 0x9ebc1c: ldur            x0, [fp, #-0xb0]
    // 0x9ebc20: stur            d3, [fp, #-0xd8]
    // 0x9ebc24: cmp             w2, NULL
    // 0x9ebc28: b.eq            #0x9ec3d8
    // 0x9ebc2c: r0 = EdgeInsets()
    //     0x9ebc2c: bl              #0x65dd90  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x9ebc30: ldur            d0, [fp, #-0xd8]
    // 0x9ebc34: StoreField: r0->field_7 = d0
    //     0x9ebc34: stur            d0, [x0, #7]
    // 0x9ebc38: ldur            d1, [fp, #-0xe8]
    // 0x9ebc3c: StoreField: r0->field_f = d1
    //     0x9ebc3c: stur            d1, [x0, #0xf]
    // 0x9ebc40: ArrayStore: r0[0] = d0  ; List_8
    //     0x9ebc40: stur            d0, [x0, #0x17]
    // 0x9ebc44: StoreField: r0->field_1f = d1
    //     0x9ebc44: stur            d1, [x0, #0x1f]
    // 0x9ebc48: ldur            x1, [fp, #-0x58]
    // 0x9ebc4c: r2 = LoadClassIdInstr(r1)
    //     0x9ebc4c: ldur            x2, [x1, #-1]
    //     0x9ebc50: ubfx            x2, x2, #0xc, #0x14
    // 0x9ebc54: mov             x16, x0
    // 0x9ebc58: mov             x0, x2
    // 0x9ebc5c: mov             x2, x16
    // 0x9ebc60: r0 = GDT[cid_x0 + -0xfa2]()
    //     0x9ebc60: sub             lr, x0, #0xfa2
    //     0x9ebc64: ldr             lr, [x21, lr, lsl #3]
    //     0x9ebc68: blr             lr
    // 0x9ebc6c: r1 = LoadClassIdInstr(r0)
    //     0x9ebc6c: ldur            x1, [x0, #-1]
    //     0x9ebc70: ubfx            x1, x1, #0xc, #0x14
    // 0x9ebc74: mov             x16, x0
    // 0x9ebc78: mov             x0, x1
    // 0x9ebc7c: mov             x1, x16
    // 0x9ebc80: r0 = GDT[cid_x0 + -0xf9f]()
    //     0x9ebc80: sub             lr, x0, #0xf9f
    //     0x9ebc84: ldr             lr, [x21, lr, lsl #3]
    //     0x9ebc88: blr             lr
    // 0x9ebc8c: mov             x1, x0
    // 0x9ebc90: ldur            x0, [fp, #-0xb0]
    // 0x9ebc94: stur            x1, [fp, #-0x58]
    // 0x9ebc98: cmp             w0, NULL
    // 0x9ebc9c: b.eq            #0x9ec3dc
    // 0x9ebca0: LoadField: r2 = r0->field_7
    //     0x9ebca0: ldur            x2, [x0, #7]
    // 0x9ebca4: cmp             x2, #0
    // 0x9ebca8: b.le            #0x9ebef4
    // 0x9ebcac: ldur            x2, [fp, #-8]
    // 0x9ebcb0: LoadField: r3 = r2->field_1f
    //     0x9ebcb0: ldur            w3, [x2, #0x1f]
    // 0x9ebcb4: DecompressPointer r3
    //     0x9ebcb4: add             x3, x3, HEAP, lsl #32
    // 0x9ebcb8: cmp             w3, NULL
    // 0x9ebcbc: b.eq            #0x9ebef4
    // 0x9ebcc0: LoadField: r4 = r2->field_23
    //     0x9ebcc0: ldur            w4, [x2, #0x23]
    // 0x9ebcc4: DecompressPointer r4
    //     0x9ebcc4: add             x4, x4, HEAP, lsl #32
    // 0x9ebcc8: cmp             w4, NULL
    // 0x9ebccc: b.eq            #0x9ebef4
    // 0x9ebcd0: ldur            x16, [fp, #-0x28]
    // 0x9ebcd4: stp             x16, x3, [SP]
    // 0x9ebcd8: r0 = ==()
    //     0x9ebcd8: bl              #0xd81600  ; [dart:core] _Double::==
    // 0x9ebcdc: tbz             w0, #4, #0x9ebef4
    // 0x9ebce0: ldur            x2, [fp, #-8]
    // 0x9ebce4: ldur            x3, [fp, #-0x38]
    // 0x9ebce8: LoadField: r1 = r2->field_23
    //     0x9ebce8: ldur            w1, [x2, #0x23]
    // 0x9ebcec: DecompressPointer r1
    //     0x9ebcec: add             x1, x1, HEAP, lsl #32
    // 0x9ebcf0: cmp             w1, NULL
    // 0x9ebcf4: b.eq            #0x9ec3e0
    // 0x9ebcf8: r0 = LoadClassIdInstr(r1)
    //     0x9ebcf8: ldur            x0, [x1, #-1]
    //     0x9ebcfc: ubfx            x0, x0, #0xc, #0x14
    // 0x9ebd00: r0 = GDT[cid_x0 + -0xd99]()
    //     0x9ebd00: sub             lr, x0, #0xd99
    //     0x9ebd04: ldr             lr, [x21, lr, lsl #3]
    //     0x9ebd08: blr             lr
    // 0x9ebd0c: mov             x3, x0
    // 0x9ebd10: ldur            x2, [fp, #-0x38]
    // 0x9ebd14: stur            x3, [fp, #-0xd0]
    // 0x9ebd18: cmp             w2, NULL
    // 0x9ebd1c: b.eq            #0x9ec3e4
    // 0x9ebd20: r0 = LoadClassIdInstr(r2)
    //     0x9ebd20: ldur            x0, [x2, #-1]
    //     0x9ebd24: ubfx            x0, x0, #0xc, #0x14
    // 0x9ebd28: mov             x1, x2
    // 0x9ebd2c: r0 = GDT[cid_x0 + -0xd99]()
    //     0x9ebd2c: sub             lr, x0, #0xd99
    //     0x9ebd30: ldr             lr, [x21, lr, lsl #3]
    //     0x9ebd34: blr             lr
    // 0x9ebd38: mov             x1, x0
    // 0x9ebd3c: ldur            x0, [fp, #-0xd0]
    // 0x9ebd40: cmp             x0, x1
    // 0x9ebd44: b.eq            #0x9ebef4
    // 0x9ebd48: ldur            x2, [fp, #-8]
    // 0x9ebd4c: LoadField: r1 = r2->field_23
    //     0x9ebd4c: ldur            w1, [x2, #0x23]
    // 0x9ebd50: DecompressPointer r1
    //     0x9ebd50: add             x1, x1, HEAP, lsl #32
    // 0x9ebd54: cmp             w1, NULL
    // 0x9ebd58: b.eq            #0x9ec3e8
    // 0x9ebd5c: r0 = LoadClassIdInstr(r1)
    //     0x9ebd5c: ldur            x0, [x1, #-1]
    //     0x9ebd60: ubfx            x0, x0, #0xc, #0x14
    // 0x9ebd64: r0 = GDT[cid_x0 + -0xc03]()
    //     0x9ebd64: sub             lr, x0, #0xc03
    //     0x9ebd68: ldr             lr, [x21, lr, lsl #3]
    //     0x9ebd6c: blr             lr
    // 0x9ebd70: mov             v1.16b, v0.16b
    // 0x9ebd74: d0 = 1.000000
    //     0x9ebd74: fmov            d0, #1.00000000
    // 0x9ebd78: fcmp            d1, d0
    // 0x9ebd7c: b.ne            #0x9ebef4
    // 0x9ebd80: ldur            x2, [fp, #-0x38]
    // 0x9ebd84: r0 = LoadClassIdInstr(r2)
    //     0x9ebd84: ldur            x0, [x2, #-1]
    //     0x9ebd88: ubfx            x0, x0, #0xc, #0x14
    // 0x9ebd8c: mov             x1, x2
    // 0x9ebd90: r0 = GDT[cid_x0 + -0xc03]()
    //     0x9ebd90: sub             lr, x0, #0xc03
    //     0x9ebd94: ldr             lr, [x21, lr, lsl #3]
    //     0x9ebd98: blr             lr
    // 0x9ebd9c: mov             v1.16b, v0.16b
    // 0x9ebda0: d0 = 1.000000
    //     0x9ebda0: fmov            d0, #1.00000000
    // 0x9ebda4: fcmp            d0, d1
    // 0x9ebda8: b.le            #0x9ebef4
    // 0x9ebdac: ldur            x1, [fp, #-0x28]
    // 0x9ebdb0: r0 = 60
    //     0x9ebdb0: movz            x0, #0x3c
    // 0x9ebdb4: branchIfSmi(r1, 0x9ebdc0)
    //     0x9ebdb4: tbz             w1, #0, #0x9ebdc0
    // 0x9ebdb8: r0 = LoadClassIdInstr(r1)
    //     0x9ebdb8: ldur            x0, [x1, #-1]
    //     0x9ebdbc: ubfx            x0, x0, #0xc, #0x14
    // 0x9ebdc0: stp             xzr, x1, [SP]
    // 0x9ebdc4: mov             lr, x0
    // 0x9ebdc8: ldr             lr, [x21, lr, lsl #3]
    // 0x9ebdcc: blr             lr
    // 0x9ebdd0: tbnz            w0, #4, #0x9ebef4
    // 0x9ebdd4: ldur            x2, [fp, #-8]
    // 0x9ebdd8: LoadField: r0 = r2->field_1b
    //     0x9ebdd8: ldur            w0, [x2, #0x1b]
    // 0x9ebddc: DecompressPointer r0
    //     0x9ebddc: add             x0, x0, HEAP, lsl #32
    // 0x9ebde0: cmp             w0, NULL
    // 0x9ebde4: b.ne            #0x9ebdf0
    // 0x9ebde8: r0 = Null
    //     0x9ebde8: mov             x0, NULL
    // 0x9ebdec: b               #0x9ebdfc
    // 0x9ebdf0: LoadField: r1 = r0->field_27
    //     0x9ebdf0: ldur            w1, [x0, #0x27]
    // 0x9ebdf4: DecompressPointer r1
    //     0x9ebdf4: add             x1, x1, HEAP, lsl #32
    // 0x9ebdf8: mov             x0, x1
    // 0x9ebdfc: r1 = LoadClassIdInstr(r0)
    //     0x9ebdfc: ldur            x1, [x0, #-1]
    //     0x9ebe00: ubfx            x1, x1, #0xc, #0x14
    // 0x9ebe04: ldur            x16, [fp, #-0xb0]
    // 0x9ebe08: stp             x16, x0, [SP]
    // 0x9ebe0c: mov             x0, x1
    // 0x9ebe10: mov             lr, x0
    // 0x9ebe14: ldr             lr, [x21, lr, lsl #3]
    // 0x9ebe18: blr             lr
    // 0x9ebe1c: tbz             w0, #4, #0x9ebea8
    // 0x9ebe20: ldur            x2, [fp, #-8]
    // 0x9ebe24: LoadField: r1 = r2->field_1b
    //     0x9ebe24: ldur            w1, [x2, #0x1b]
    // 0x9ebe28: DecompressPointer r1
    //     0x9ebe28: add             x1, x1, HEAP, lsl #32
    // 0x9ebe2c: cmp             w1, NULL
    // 0x9ebe30: b.eq            #0x9ebe3c
    // 0x9ebe34: r0 = dispose()
    //     0x9ebe34: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0x9ebe38: ldur            x2, [fp, #-8]
    // 0x9ebe3c: r1 = <double>
    //     0x9ebe3c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9ebe40: r0 = AnimationController()
    //     0x9ebe40: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x9ebe44: stur            x0, [fp, #-0x68]
    // 0x9ebe48: ldur            x16, [fp, #-0xb0]
    // 0x9ebe4c: str             x16, [SP]
    // 0x9ebe50: mov             x1, x0
    // 0x9ebe54: ldur            x2, [fp, #-8]
    // 0x9ebe58: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x9ebe58: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x9ebe5c: ldr             x4, [x4, #0x408]
    // 0x9ebe60: r0 = AnimationController()
    //     0x9ebe60: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x9ebe64: ldur            x2, [fp, #-0x18]
    // 0x9ebe68: r1 = Function '<anonymous closure>':.
    //     0x9ebe68: add             x1, PP, #0x44, lsl #12  ; [pp+0x44628] AnonymousClosure: (0x9ec5c0), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9ebe6c: ldr             x1, [x1, #0x628]
    // 0x9ebe70: r0 = AllocateClosure()
    //     0x9ebe70: bl              #0xec1630  ; AllocateClosureStub
    // 0x9ebe74: ldur            x1, [fp, #-0x68]
    // 0x9ebe78: mov             x2, x0
    // 0x9ebe7c: r0 = addStatusListener()
    //     0x9ebe7c: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0x9ebe80: ldur            x0, [fp, #-0x68]
    // 0x9ebe84: ldur            x2, [fp, #-8]
    // 0x9ebe88: StoreField: r2->field_1b = r0
    //     0x9ebe88: stur            w0, [x2, #0x1b]
    //     0x9ebe8c: ldurb           w16, [x2, #-1]
    //     0x9ebe90: ldurb           w17, [x0, #-1]
    //     0x9ebe94: and             x16, x17, x16, lsr #2
    //     0x9ebe98: tst             x16, HEAP, lsr #32
    //     0x9ebe9c: b.eq            #0x9ebea4
    //     0x9ebea0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9ebea4: b               #0x9ebeac
    // 0x9ebea8: ldur            x2, [fp, #-8]
    // 0x9ebeac: LoadField: r0 = r2->field_23
    //     0x9ebeac: ldur            w0, [x2, #0x23]
    // 0x9ebeb0: DecompressPointer r0
    //     0x9ebeb0: add             x0, x0, HEAP, lsl #32
    // 0x9ebeb4: stur            x0, [fp, #-0x18]
    // 0x9ebeb8: LoadField: r1 = r2->field_1b
    //     0x9ebeb8: ldur            w1, [x2, #0x1b]
    // 0x9ebebc: DecompressPointer r1
    //     0x9ebebc: add             x1, x1, HEAP, lsl #32
    // 0x9ebec0: cmp             w1, NULL
    // 0x9ebec4: b.eq            #0x9ec3ec
    // 0x9ebec8: d0 = 0.000000
    //     0x9ebec8: eor             v0.16b, v0.16b, v0.16b
    // 0x9ebecc: r0 = value=()
    //     0x9ebecc: bl              #0x6567c4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::value=
    // 0x9ebed0: ldur            x0, [fp, #-8]
    // 0x9ebed4: LoadField: r1 = r0->field_1b
    //     0x9ebed4: ldur            w1, [x0, #0x1b]
    // 0x9ebed8: DecompressPointer r1
    //     0x9ebed8: add             x1, x1, HEAP, lsl #32
    // 0x9ebedc: cmp             w1, NULL
    // 0x9ebee0: b.eq            #0x9ec3f0
    // 0x9ebee4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9ebee4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9ebee8: r0 = forward()
    //     0x9ebee8: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x9ebeec: ldur            x4, [fp, #-0x18]
    // 0x9ebef0: b               #0x9ebef8
    // 0x9ebef4: ldur            x4, [fp, #-0x38]
    // 0x9ebef8: ldur            x1, [fp, #-8]
    // 0x9ebefc: ldur            x3, [fp, #-0xb8]
    // 0x9ebf00: ldur            x2, [fp, #-0x58]
    // 0x9ebf04: ldur            x0, [fp, #-0x28]
    // 0x9ebf08: stur            x4, [fp, #-0x68]
    // 0x9ebf0c: StoreField: r1->field_1f = r0
    //     0x9ebf0c: stur            w0, [x1, #0x1f]
    //     0x9ebf10: ldurb           w16, [x1, #-1]
    //     0x9ebf14: ldurb           w17, [x0, #-1]
    //     0x9ebf18: and             x16, x17, x16, lsr #2
    //     0x9ebf1c: tst             x16, HEAP, lsr #32
    //     0x9ebf20: b.eq            #0x9ebf28
    //     0x9ebf24: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9ebf28: mov             x0, x4
    // 0x9ebf2c: StoreField: r1->field_23 = r0
    //     0x9ebf2c: stur            w0, [x1, #0x23]
    //     0x9ebf30: ldurb           w16, [x1, #-1]
    //     0x9ebf34: ldurb           w17, [x0, #-1]
    //     0x9ebf38: and             x16, x17, x16, lsr #2
    //     0x9ebf3c: tst             x16, HEAP, lsr #32
    //     0x9ebf40: b.eq            #0x9ebf48
    //     0x9ebf44: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9ebf48: cmp             w3, NULL
    // 0x9ebf4c: b.eq            #0x9ec3f4
    // 0x9ebf50: LoadField: r0 = r1->field_b
    //     0x9ebf50: ldur            w0, [x1, #0xb]
    // 0x9ebf54: DecompressPointer r0
    //     0x9ebf54: add             x0, x0, HEAP, lsl #32
    // 0x9ebf58: stur            x0, [fp, #-0x38]
    // 0x9ebf5c: cmp             w0, NULL
    // 0x9ebf60: b.eq            #0x9ec3f8
    // 0x9ebf64: LoadField: r5 = r0->field_37
    //     0x9ebf64: ldur            w5, [x0, #0x37]
    // 0x9ebf68: DecompressPointer r5
    //     0x9ebf68: add             x5, x5, HEAP, lsl #32
    // 0x9ebf6c: stur            x5, [fp, #-0x18]
    // 0x9ebf70: r0 = Align()
    //     0x9ebf70: bl              #0x9d4ff8  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x9ebf74: mov             x1, x0
    // 0x9ebf78: ldur            x0, [fp, #-0xb8]
    // 0x9ebf7c: stur            x1, [fp, #-0x70]
    // 0x9ebf80: StoreField: r1->field_f = r0
    //     0x9ebf80: stur            w0, [x1, #0xf]
    // 0x9ebf84: r0 = 1.000000
    //     0x9ebf84: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x9ebf88: StoreField: r1->field_13 = r0
    //     0x9ebf88: stur            w0, [x1, #0x13]
    // 0x9ebf8c: ArrayStore: r1[0] = r0  ; List_4
    //     0x9ebf8c: stur            w0, [x1, #0x17]
    // 0x9ebf90: ldur            x0, [fp, #-0x18]
    // 0x9ebf94: StoreField: r1->field_b = r0
    //     0x9ebf94: stur            w0, [x1, #0xb]
    // 0x9ebf98: r0 = Padding()
    //     0x9ebf98: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ebf9c: mov             x1, x0
    // 0x9ebfa0: ldur            x0, [fp, #-0x58]
    // 0x9ebfa4: stur            x1, [fp, #-0xa0]
    // 0x9ebfa8: StoreField: r1->field_f = r0
    //     0x9ebfa8: stur            w0, [x1, #0xf]
    // 0x9ebfac: ldur            x0, [fp, #-0x70]
    // 0x9ebfb0: StoreField: r1->field_b = r0
    //     0x9ebfb0: stur            w0, [x1, #0xb]
    // 0x9ebfb4: ldur            x0, [fp, #-0x38]
    // 0x9ebfb8: LoadField: r2 = r0->field_33
    //     0x9ebfb8: ldur            w2, [x0, #0x33]
    // 0x9ebfbc: DecompressPointer r2
    //     0x9ebfbc: add             x2, x2, HEAP, lsl #32
    // 0x9ebfc0: stur            x2, [fp, #-0x18]
    // 0x9ebfc4: cmp             w2, NULL
    // 0x9ebfc8: b.eq            #0x9ebff4
    // 0x9ebfcc: r0 = Tooltip()
    //     0x9ebfcc: bl              #0x9ec428  ; AllocateTooltipStub -> Tooltip (size=0x58)
    // 0x9ebfd0: mov             x1, x0
    // 0x9ebfd4: ldur            x0, [fp, #-0x18]
    // 0x9ebfd8: StoreField: r1->field_b = r0
    //     0x9ebfd8: stur            w0, [x1, #0xb]
    // 0x9ebfdc: r0 = true
    //     0x9ebfdc: add             x0, NULL, #0x20  ; true
    // 0x9ebfe0: StoreField: r1->field_47 = r0
    //     0x9ebfe0: stur            w0, [x1, #0x47]
    // 0x9ebfe4: ldur            x2, [fp, #-0xa0]
    // 0x9ebfe8: StoreField: r1->field_2b = r2
    //     0x9ebfe8: stur            w2, [x1, #0x2b]
    // 0x9ebfec: mov             x3, x1
    // 0x9ebff0: b               #0x9ec000
    // 0x9ebff4: mov             x2, x1
    // 0x9ebff8: r0 = true
    //     0x9ebff8: add             x0, NULL, #0x20  ; true
    // 0x9ebffc: mov             x3, x2
    // 0x9ec000: ldur            x2, [fp, #-0x28]
    // 0x9ec004: ldur            x1, [fp, #-0x30]
    // 0x9ec008: stur            x3, [fp, #-0x18]
    // 0x9ec00c: cmp             w2, NULL
    // 0x9ec010: b.eq            #0x9ec3fc
    // 0x9ec014: cmp             w1, NULL
    // 0x9ec018: b.ne            #0x9ec024
    // 0x9ec01c: r5 = Null
    //     0x9ec01c: mov             x5, NULL
    // 0x9ec020: b               #0x9ec03c
    // 0x9ec024: ldur            x16, [fp, #-0x40]
    // 0x9ec028: str             x16, [SP]
    // 0x9ec02c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x9ec02c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x9ec030: ldr             x4, [x4, #0x228]
    // 0x9ec034: r0 = copyWith()
    //     0x9ec034: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9ec038: mov             x5, x0
    // 0x9ec03c: ldur            x4, [fp, #-0x20]
    // 0x9ec040: ldur            x3, [fp, #-0x68]
    // 0x9ec044: stur            x5, [fp, #-0x30]
    // 0x9ec048: cmp             w4, NULL
    // 0x9ec04c: b.eq            #0x9ec400
    // 0x9ec050: r0 = LoadClassIdInstr(r4)
    //     0x9ec050: ldur            x0, [x4, #-1]
    //     0x9ec054: ubfx            x0, x0, #0xc, #0x14
    // 0x9ec058: mov             x1, x4
    // 0x9ec05c: ldur            x2, [fp, #-0x88]
    // 0x9ec060: r0 = GDT[cid_x0 + 0x3773]()
    //     0x9ec060: movz            x17, #0x3773
    //     0x9ec064: add             lr, x0, x17
    //     0x9ec068: ldr             lr, [x21, lr, lsl #3]
    //     0x9ec06c: blr             lr
    // 0x9ec070: mov             x4, x0
    // 0x9ec074: ldur            x3, [fp, #-0x68]
    // 0x9ec078: stur            x4, [fp, #-0xa0]
    // 0x9ec07c: cmp             w3, NULL
    // 0x9ec080: b.ne            #0x9ec090
    // 0x9ec084: r6 = Instance_MaterialType
    //     0x9ec084: add             x6, PP, #0x44, lsl #12  ; [pp+0x44398] Obj!MaterialType@e36521
    //     0x9ec088: ldr             x6, [x6, #0x398]
    // 0x9ec08c: b               #0x9ec098
    // 0x9ec090: r6 = Instance_MaterialType
    //     0x9ec090: add             x6, PP, #0x44, lsl #12  ; [pp+0x44388] Obj!MaterialType@e36541
    //     0x9ec094: ldr             x6, [x6, #0x388]
    // 0x9ec098: ldur            x5, [fp, #-8]
    // 0x9ec09c: stur            x6, [fp, #-0x70]
    // 0x9ec0a0: LoadField: r0 = r5->field_b
    //     0x9ec0a0: ldur            w0, [x5, #0xb]
    // 0x9ec0a4: DecompressPointer r0
    //     0x9ec0a4: add             x0, x0, HEAP, lsl #32
    // 0x9ec0a8: cmp             w0, NULL
    // 0x9ec0ac: b.eq            #0x9ec404
    // 0x9ec0b0: LoadField: r7 = r0->field_b
    //     0x9ec0b0: ldur            w7, [x0, #0xb]
    // 0x9ec0b4: DecompressPointer r7
    //     0x9ec0b4: add             x7, x7, HEAP, lsl #32
    // 0x9ec0b8: stur            x7, [fp, #-0x58]
    // 0x9ec0bc: cmp             w7, NULL
    // 0x9ec0c0: b.eq            #0x9ec0cc
    // 0x9ec0c4: r9 = true
    //     0x9ec0c4: add             x9, NULL, #0x20  ; true
    // 0x9ec0c8: b               #0x9ec0d0
    // 0x9ec0cc: r9 = false
    //     0x9ec0cc: add             x9, NULL, #0x30  ; false
    // 0x9ec0d0: ldur            x8, [fp, #-0x78]
    // 0x9ec0d4: ldur            x1, [fp, #-0x20]
    // 0x9ec0d8: stur            x9, [fp, #-0x38]
    // 0x9ec0dc: r0 = LoadClassIdInstr(r1)
    //     0x9ec0dc: ldur            x0, [x1, #-1]
    //     0x9ec0e0: ubfx            x0, x0, #0xc, #0x14
    // 0x9ec0e4: ldur            x2, [fp, #-0x88]
    // 0x9ec0e8: r0 = GDT[cid_x0 + 0x3773]()
    //     0x9ec0e8: movz            x17, #0x3773
    //     0x9ec0ec: add             lr, x0, x17
    //     0x9ec0f0: ldr             lr, [x21, lr, lsl #3]
    //     0x9ec0f4: blr             lr
    // 0x9ec0f8: ldur            x1, [fp, #-8]
    // 0x9ec0fc: stur            x0, [fp, #-0x20]
    // 0x9ec100: r0 = statesController()
    //     0x9ec100: bl              #0x932ac4  ; [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::statesController
    // 0x9ec104: mov             x1, x0
    // 0x9ec108: ldur            x0, [fp, #-0x78]
    // 0x9ec10c: stur            x1, [fp, #-0x88]
    // 0x9ec110: cmp             w0, NULL
    // 0x9ec114: b.ne            #0x9ec120
    // 0x9ec118: ldur            x24, [fp, #-0x40]
    // 0x9ec11c: b               #0x9ec124
    // 0x9ec120: mov             x24, x0
    // 0x9ec124: ldur            x8, [fp, #-0x28]
    // 0x9ec128: ldur            x23, [fp, #-0x48]
    // 0x9ec12c: ldur            x20, [fp, #-0x50]
    // 0x9ec130: ldur            x19, [fp, #-0x80]
    // 0x9ec134: ldur            x14, [fp, #-0x90]
    // 0x9ec138: ldur            x13, [fp, #-0x98]
    // 0x9ec13c: ldur            x12, [fp, #-0xa8]
    // 0x9ec140: ldur            x11, [fp, #-0xc8]
    // 0x9ec144: ldur            x10, [fp, #-0x10]
    // 0x9ec148: ldur            x9, [fp, #-0x60]
    // 0x9ec14c: ldur            x2, [fp, #-0x68]
    // 0x9ec150: ldur            x7, [fp, #-0x30]
    // 0x9ec154: ldur            x3, [fp, #-0xa0]
    // 0x9ec158: ldur            x4, [fp, #-0x70]
    // 0x9ec15c: ldur            x5, [fp, #-0x58]
    // 0x9ec160: ldur            x0, [fp, #-0x20]
    // 0x9ec164: ldur            x6, [fp, #-0x38]
    // 0x9ec168: stur            x24, [fp, #-0x40]
    // 0x9ec16c: r0 = IconThemeData()
    //     0x9ec16c: bl              #0x63d1c0  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0x9ec170: mov             x1, x0
    // 0x9ec174: ldur            x0, [fp, #-0x80]
    // 0x9ec178: StoreField: r1->field_7 = r0
    //     0x9ec178: stur            w0, [x1, #7]
    // 0x9ec17c: ldur            x0, [fp, #-0x40]
    // 0x9ec180: StoreField: r1->field_1b = r0
    //     0x9ec180: stur            w0, [x1, #0x1b]
    // 0x9ec184: mov             x2, x1
    // 0x9ec188: ldur            x1, [fp, #-0x18]
    // 0x9ec18c: r0 = merge()
    //     0x9ec18c: bl              #0x9e6b88  ; [package:flutter/src/widgets/icon_theme.dart] IconTheme::merge
    // 0x9ec190: stur            x0, [fp, #-0x18]
    // 0x9ec194: r0 = InkWell()
    //     0x9ec194: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x9ec198: mov             x1, x0
    // 0x9ec19c: ldur            x0, [fp, #-0x18]
    // 0x9ec1a0: stur            x1, [fp, #-0x40]
    // 0x9ec1a4: StoreField: r1->field_b = r0
    //     0x9ec1a4: stur            w0, [x1, #0xb]
    // 0x9ec1a8: ldur            x0, [fp, #-0x58]
    // 0x9ec1ac: StoreField: r1->field_f = r0
    //     0x9ec1ac: stur            w0, [x1, #0xf]
    // 0x9ec1b0: ldur            x0, [fp, #-0x90]
    // 0x9ec1b4: StoreField: r1->field_3f = r0
    //     0x9ec1b4: stur            w0, [x1, #0x3f]
    // 0x9ec1b8: r0 = true
    //     0x9ec1b8: add             x0, NULL, #0x20  ; true
    // 0x9ec1bc: StoreField: r1->field_43 = r0
    //     0x9ec1bc: stur            w0, [x1, #0x43]
    // 0x9ec1c0: r2 = Instance_BoxShape
    //     0x9ec1c0: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0x9ec1c4: ldr             x2, [x2, #0xca8]
    // 0x9ec1c8: StoreField: r1->field_47 = r2
    //     0x9ec1c8: stur            w2, [x1, #0x47]
    // 0x9ec1cc: ldur            x2, [fp, #-0x20]
    // 0x9ec1d0: StoreField: r1->field_53 = r2
    //     0x9ec1d0: stur            w2, [x1, #0x53]
    // 0x9ec1d4: r2 = Instance_Color
    //     0x9ec1d4: ldr             x2, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0x9ec1d8: StoreField: r1->field_5f = r2
    //     0x9ec1d8: stur            w2, [x1, #0x5f]
    // 0x9ec1dc: ldur            x2, [fp, #-0x98]
    // 0x9ec1e0: StoreField: r1->field_63 = r2
    //     0x9ec1e0: stur            w2, [x1, #0x63]
    // 0x9ec1e4: ldur            x2, [fp, #-0xc8]
    // 0x9ec1e8: StoreField: r1->field_6b = r2
    //     0x9ec1e8: stur            w2, [x1, #0x6b]
    // 0x9ec1ec: StoreField: r1->field_6f = r0
    //     0x9ec1ec: stur            w0, [x1, #0x6f]
    // 0x9ec1f0: r2 = false
    //     0x9ec1f0: add             x2, NULL, #0x30  ; false
    // 0x9ec1f4: StoreField: r1->field_73 = r2
    //     0x9ec1f4: stur            w2, [x1, #0x73]
    // 0x9ec1f8: ldur            x3, [fp, #-0x38]
    // 0x9ec1fc: StoreField: r1->field_83 = r3
    //     0x9ec1fc: stur            w3, [x1, #0x83]
    // 0x9ec200: StoreField: r1->field_7b = r2
    //     0x9ec200: stur            w2, [x1, #0x7b]
    // 0x9ec204: ldur            x2, [fp, #-0x88]
    // 0x9ec208: StoreField: r1->field_87 = r2
    //     0x9ec208: stur            w2, [x1, #0x87]
    // 0x9ec20c: r0 = Material()
    //     0x9ec20c: bl              #0x9e6a2c  ; AllocateMaterialStub -> Material (size=0x40)
    // 0x9ec210: mov             x1, x0
    // 0x9ec214: ldur            x0, [fp, #-0x70]
    // 0x9ec218: stur            x1, [fp, #-0x18]
    // 0x9ec21c: StoreField: r1->field_f = r0
    //     0x9ec21c: stur            w0, [x1, #0xf]
    // 0x9ec220: ldur            x0, [fp, #-0x28]
    // 0x9ec224: LoadField: d0 = r0->field_7
    //     0x9ec224: ldur            d0, [x0, #7]
    // 0x9ec228: StoreField: r1->field_13 = d0
    //     0x9ec228: stur            d0, [x1, #0x13]
    // 0x9ec22c: ldur            x0, [fp, #-0x68]
    // 0x9ec230: StoreField: r1->field_1b = r0
    //     0x9ec230: stur            w0, [x1, #0x1b]
    // 0x9ec234: ldur            x0, [fp, #-0x48]
    // 0x9ec238: StoreField: r1->field_1f = r0
    //     0x9ec238: stur            w0, [x1, #0x1f]
    // 0x9ec23c: ldur            x0, [fp, #-0x50]
    // 0x9ec240: StoreField: r1->field_23 = r0
    //     0x9ec240: stur            w0, [x1, #0x23]
    // 0x9ec244: ldur            x0, [fp, #-0x30]
    // 0x9ec248: StoreField: r1->field_27 = r0
    //     0x9ec248: stur            w0, [x1, #0x27]
    // 0x9ec24c: ldur            x0, [fp, #-0xa0]
    // 0x9ec250: StoreField: r1->field_2b = r0
    //     0x9ec250: stur            w0, [x1, #0x2b]
    // 0x9ec254: r0 = true
    //     0x9ec254: add             x0, NULL, #0x20  ; true
    // 0x9ec258: StoreField: r1->field_2f = r0
    //     0x9ec258: stur            w0, [x1, #0x2f]
    // 0x9ec25c: ldur            x0, [fp, #-0x10]
    // 0x9ec260: StoreField: r1->field_33 = r0
    //     0x9ec260: stur            w0, [x1, #0x33]
    // 0x9ec264: r0 = Instance_Duration
    //     0x9ec264: add             x0, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0x9ec268: ldr             x0, [x0, #0x368]
    // 0x9ec26c: StoreField: r1->field_37 = r0
    //     0x9ec26c: stur            w0, [x1, #0x37]
    // 0x9ec270: ldur            x0, [fp, #-0x40]
    // 0x9ec274: StoreField: r1->field_b = r0
    //     0x9ec274: stur            w0, [x1, #0xb]
    // 0x9ec278: r0 = ConstrainedBox()
    //     0x9ec278: bl              #0x9d4fe0  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x9ec27c: mov             x1, x0
    // 0x9ec280: ldur            x0, [fp, #-0x60]
    // 0x9ec284: stur            x1, [fp, #-0x10]
    // 0x9ec288: StoreField: r1->field_f = r0
    //     0x9ec288: stur            w0, [x1, #0xf]
    // 0x9ec28c: ldur            x0, [fp, #-0x18]
    // 0x9ec290: StoreField: r1->field_b = r0
    //     0x9ec290: stur            w0, [x1, #0xb]
    // 0x9ec294: ldur            x0, [fp, #-0xa8]
    // 0x9ec298: cmp             w0, NULL
    // 0x9ec29c: b.eq            #0x9ec408
    // 0x9ec2a0: LoadField: r2 = r0->field_7
    //     0x9ec2a0: ldur            x2, [x0, #7]
    // 0x9ec2a4: cmp             x2, #0
    // 0x9ec2a8: b.gt            #0x9ec2e4
    // 0x9ec2ac: ldur            d0, [fp, #-0xe8]
    // 0x9ec2b0: ldur            d1, [fp, #-0xe0]
    // 0x9ec2b4: d2 = 48.000000
    //     0x9ec2b4: ldr             d2, [PP, #0x6e10]  ; [pp+0x6e10] IMM: double(48) from 0x4048000000000000
    // 0x9ec2b8: fadd            d3, d1, d2
    // 0x9ec2bc: stur            d3, [fp, #-0xf0]
    // 0x9ec2c0: fadd            d1, d0, d2
    // 0x9ec2c4: stur            d1, [fp, #-0xd8]
    // 0x9ec2c8: r0 = Size()
    //     0x9ec2c8: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x9ec2cc: ldur            d0, [fp, #-0xf0]
    // 0x9ec2d0: StoreField: r0->field_7 = d0
    //     0x9ec2d0: stur            d0, [x0, #7]
    // 0x9ec2d4: ldur            d0, [fp, #-0xd8]
    // 0x9ec2d8: StoreField: r0->field_f = d0
    //     0x9ec2d8: stur            d0, [x0, #0xf]
    // 0x9ec2dc: mov             x1, x0
    // 0x9ec2e0: b               #0x9ec2ec
    // 0x9ec2e4: r1 = Instance_Size
    //     0x9ec2e4: add             x1, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!Size@e2c001
    //     0x9ec2e8: ldr             x1, [x1, #0xa20]
    // 0x9ec2ec: ldur            x0, [fp, #-8]
    // 0x9ec2f0: stur            x1, [fp, #-0x18]
    // 0x9ec2f4: LoadField: r2 = r0->field_b
    //     0x9ec2f4: ldur            w2, [x0, #0xb]
    // 0x9ec2f8: DecompressPointer r2
    //     0x9ec2f8: add             x2, x2, HEAP, lsl #32
    // 0x9ec2fc: cmp             w2, NULL
    // 0x9ec300: b.eq            #0x9ec40c
    // 0x9ec304: LoadField: r0 = r2->field_b
    //     0x9ec304: ldur            w0, [x2, #0xb]
    // 0x9ec308: DecompressPointer r0
    //     0x9ec308: add             x0, x0, HEAP, lsl #32
    // 0x9ec30c: cmp             w0, NULL
    // 0x9ec310: b.eq            #0x9ec31c
    // 0x9ec314: r2 = true
    //     0x9ec314: add             x2, NULL, #0x20  ; true
    // 0x9ec318: b               #0x9ec320
    // 0x9ec31c: r2 = false
    //     0x9ec31c: add             x2, NULL, #0x30  ; false
    // 0x9ec320: ldur            x0, [fp, #-0x10]
    // 0x9ec324: stur            x2, [fp, #-8]
    // 0x9ec328: r0 = _InputPadding()
    //     0x9ec328: bl              #0x9ec410  ; Allocate_InputPaddingStub -> _InputPadding (size=0x14)
    // 0x9ec32c: mov             x1, x0
    // 0x9ec330: ldur            x0, [fp, #-0x18]
    // 0x9ec334: stur            x1, [fp, #-0x20]
    // 0x9ec338: StoreField: r1->field_f = r0
    //     0x9ec338: stur            w0, [x1, #0xf]
    // 0x9ec33c: ldur            x0, [fp, #-0x10]
    // 0x9ec340: StoreField: r1->field_b = r0
    //     0x9ec340: stur            w0, [x1, #0xb]
    // 0x9ec344: r0 = Semantics()
    //     0x9ec344: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0x9ec348: stur            x0, [fp, #-0x10]
    // 0x9ec34c: r16 = true
    //     0x9ec34c: add             x16, NULL, #0x20  ; true
    // 0x9ec350: r30 = true
    //     0x9ec350: add             lr, NULL, #0x20  ; true
    // 0x9ec354: stp             lr, x16, [SP, #0x10]
    // 0x9ec358: ldur            x16, [fp, #-8]
    // 0x9ec35c: ldur            lr, [fp, #-0x20]
    // 0x9ec360: stp             lr, x16, [SP]
    // 0x9ec364: mov             x1, x0
    // 0x9ec368: r4 = const [0, 0x5, 0x4, 0x1, button, 0x2, child, 0x4, container, 0x1, enabled, 0x3, null]
    //     0x9ec368: add             x4, PP, #0x44, lsl #12  ; [pp+0x44630] List(13) [0, 0x5, 0x4, 0x1, "button", 0x2, "child", 0x4, "container", 0x1, "enabled", 0x3, Null]
    //     0x9ec36c: ldr             x4, [x4, #0x630]
    // 0x9ec370: r0 = Semantics()
    //     0x9ec370: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0x9ec374: ldur            x0, [fp, #-0x10]
    // 0x9ec378: LeaveFrame
    //     0x9ec378: mov             SP, fp
    //     0x9ec37c: ldp             fp, lr, [SP], #0x10
    // 0x9ec380: ret
    //     0x9ec380: ret             
    // 0x9ec384: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ec384: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ec388: b               #0x9eb3f0
    // 0x9ec38c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec38c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec390: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec390: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec394: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec394: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec398: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec398: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec39c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec39c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec3a0: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9ec3a0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9ec3a4: SaveReg d0
    //     0x9ec3a4: str             q0, [SP, #-0x10]!
    // 0x9ec3a8: SaveReg r0
    //     0x9ec3a8: str             x0, [SP, #-8]!
    // 0x9ec3ac: r0 = AllocateDouble()
    //     0x9ec3ac: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9ec3b0: mov             x1, x0
    // 0x9ec3b4: RestoreReg r0
    //     0x9ec3b4: ldr             x0, [SP], #8
    // 0x9ec3b8: RestoreReg d0
    //     0x9ec3b8: ldr             q0, [SP], #0x10
    // 0x9ec3bc: b               #0x9ebb28
    // 0x9ec3c0: SaveReg d0
    //     0x9ec3c0: str             q0, [SP, #-0x10]!
    // 0x9ec3c4: SaveReg r1
    //     0x9ec3c4: str             x1, [SP, #-8]!
    // 0x9ec3c8: r0 = AllocateDouble()
    //     0x9ec3c8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9ec3cc: RestoreReg r1
    //     0x9ec3cc: ldr             x1, [SP], #8
    // 0x9ec3d0: RestoreReg d0
    //     0x9ec3d0: ldr             q0, [SP], #0x10
    // 0x9ec3d4: b               #0x9ebb90
    // 0x9ec3d8: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9ec3d8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9ec3dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec3dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec3e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec3e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec3e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec3e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec3e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec3e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec3ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec3ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec3f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec3f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec3f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec3f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec3f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec3f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec3fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec3fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec400: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec400: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec404: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec404: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec408: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec408: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ec40c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ec40c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, AnimationStatus) {
    // ** addr: 0x9ec5c0, size: 0x74
    // 0x9ec5c0: EnterFrame
    //     0x9ec5c0: stp             fp, lr, [SP, #-0x10]!
    //     0x9ec5c4: mov             fp, SP
    // 0x9ec5c8: AllocStack(0x8)
    //     0x9ec5c8: sub             SP, SP, #8
    // 0x9ec5cc: SetupParameters()
    //     0x9ec5cc: ldr             x0, [fp, #0x18]
    //     0x9ec5d0: ldur            w1, [x0, #0x17]
    //     0x9ec5d4: add             x1, x1, HEAP, lsl #32
    // 0x9ec5d8: CheckStackOverflow
    //     0x9ec5d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ec5dc: cmp             SP, x16
    //     0x9ec5e0: b.ls            #0x9ec62c
    // 0x9ec5e4: ldr             x0, [fp, #0x10]
    // 0x9ec5e8: r16 = Instance_AnimationStatus
    //     0x9ec5e8: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0x9ec5ec: cmp             w0, w16
    // 0x9ec5f0: b.ne            #0x9ec61c
    // 0x9ec5f4: LoadField: r0 = r1->field_f
    //     0x9ec5f4: ldur            w0, [x1, #0xf]
    // 0x9ec5f8: DecompressPointer r0
    //     0x9ec5f8: add             x0, x0, HEAP, lsl #32
    // 0x9ec5fc: stur            x0, [fp, #-8]
    // 0x9ec600: r1 = Function '<anonymous closure>':.
    //     0x9ec600: add             x1, PP, #0x44, lsl #12  ; [pp+0x44638] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x9ec604: ldr             x1, [x1, #0x638]
    // 0x9ec608: r2 = Null
    //     0x9ec608: mov             x2, NULL
    // 0x9ec60c: r0 = AllocateClosure()
    //     0x9ec60c: bl              #0xec1630  ; AllocateClosureStub
    // 0x9ec610: ldur            x1, [fp, #-8]
    // 0x9ec614: mov             x2, x0
    // 0x9ec618: r0 = setState()
    //     0x9ec618: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9ec61c: r0 = Null
    //     0x9ec61c: mov             x0, NULL
    // 0x9ec620: LeaveFrame
    //     0x9ec620: mov             SP, fp
    //     0x9ec624: ldp             fp, lr, [SP], #0x10
    // 0x9ec628: ret
    //     0x9ec628: ret             
    // 0x9ec62c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ec62c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ec630: b               #0x9ec5e4
  }
  [closure] InteractiveInkFeatureFactory? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ec634, size: 0x50
    // 0x9ec634: EnterFrame
    //     0x9ec634: stp             fp, lr, [SP, #-0x10]!
    //     0x9ec638: mov             fp, SP
    // 0x9ec63c: CheckStackOverflow
    //     0x9ec63c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ec640: cmp             SP, x16
    //     0x9ec644: b.ls            #0x9ec67c
    // 0x9ec648: ldr             x1, [fp, #0x10]
    // 0x9ec64c: cmp             w1, NULL
    // 0x9ec650: b.ne            #0x9ec65c
    // 0x9ec654: r0 = Null
    //     0x9ec654: mov             x0, NULL
    // 0x9ec658: b               #0x9ec670
    // 0x9ec65c: r0 = LoadClassIdInstr(r1)
    //     0x9ec65c: ldur            x0, [x1, #-1]
    //     0x9ec660: ubfx            x0, x0, #0xc, #0x14
    // 0x9ec664: r0 = GDT[cid_x0 + 0x9f4]()
    //     0x9ec664: add             lr, x0, #0x9f4
    //     0x9ec668: ldr             lr, [x21, lr, lsl #3]
    //     0x9ec66c: blr             lr
    // 0x9ec670: LeaveFrame
    //     0x9ec670: mov             SP, fp
    //     0x9ec674: ldp             fp, lr, [SP], #0x10
    // 0x9ec678: ret
    //     0x9ec678: ret             
    // 0x9ec67c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ec67c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ec680: b               #0x9ec648
  }
  [closure] AlignmentGeometry? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ec684, size: 0x24
    // 0x9ec684: ldr             x1, [SP]
    // 0x9ec688: cmp             w1, NULL
    // 0x9ec68c: b.ne            #0x9ec698
    // 0x9ec690: r0 = Null
    //     0x9ec690: mov             x0, NULL
    // 0x9ec694: b               #0x9ec6a4
    // 0x9ec698: LoadField: r2 = r1->field_57
    //     0x9ec698: ldur            w2, [x1, #0x57]
    // 0x9ec69c: DecompressPointer r2
    //     0x9ec69c: add             x2, x2, HEAP, lsl #32
    // 0x9ec6a0: mov             x0, x2
    // 0x9ec6a4: ret
    //     0x9ec6a4: ret             
  }
  [closure] bool? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ec6a8, size: 0x24
    // 0x9ec6a8: ldr             x1, [SP]
    // 0x9ec6ac: cmp             w1, NULL
    // 0x9ec6b0: b.ne            #0x9ec6bc
    // 0x9ec6b4: r0 = Null
    //     0x9ec6b4: mov             x0, NULL
    // 0x9ec6b8: b               #0x9ec6c8
    // 0x9ec6bc: LoadField: r2 = r1->field_53
    //     0x9ec6bc: ldur            w2, [x1, #0x53]
    // 0x9ec6c0: DecompressPointer r2
    //     0x9ec6c0: add             x2, x2, HEAP, lsl #32
    // 0x9ec6c4: mov             x0, x2
    // 0x9ec6c8: ret
    //     0x9ec6c8: ret             
  }
  [closure] Duration? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ec6cc, size: 0x24
    // 0x9ec6cc: ldr             x1, [SP]
    // 0x9ec6d0: cmp             w1, NULL
    // 0x9ec6d4: b.ne            #0x9ec6e0
    // 0x9ec6d8: r0 = Null
    //     0x9ec6d8: mov             x0, NULL
    // 0x9ec6dc: b               #0x9ec6ec
    // 0x9ec6e0: LoadField: r2 = r1->field_4f
    //     0x9ec6e0: ldur            w2, [x1, #0x4f]
    // 0x9ec6e4: DecompressPointer r2
    //     0x9ec6e4: add             x2, x2, HEAP, lsl #32
    // 0x9ec6e8: mov             x0, x2
    // 0x9ec6ec: ret
    //     0x9ec6ec: ret             
  }
  [closure] MaterialTapTargetSize? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ec6f0, size: 0x50
    // 0x9ec6f0: EnterFrame
    //     0x9ec6f0: stp             fp, lr, [SP, #-0x10]!
    //     0x9ec6f4: mov             fp, SP
    // 0x9ec6f8: CheckStackOverflow
    //     0x9ec6f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ec6fc: cmp             SP, x16
    //     0x9ec700: b.ls            #0x9ec738
    // 0x9ec704: ldr             x1, [fp, #0x10]
    // 0x9ec708: cmp             w1, NULL
    // 0x9ec70c: b.ne            #0x9ec718
    // 0x9ec710: r0 = Null
    //     0x9ec710: mov             x0, NULL
    // 0x9ec714: b               #0x9ec72c
    // 0x9ec718: r0 = LoadClassIdInstr(r1)
    //     0x9ec718: ldur            x0, [x1, #-1]
    //     0x9ec71c: ubfx            x0, x0, #0xc, #0x14
    // 0x9ec720: r0 = GDT[cid_x0 + 0xf82]()
    //     0x9ec720: add             lr, x0, #0xf82
    //     0x9ec724: ldr             lr, [x21, lr, lsl #3]
    //     0x9ec728: blr             lr
    // 0x9ec72c: LeaveFrame
    //     0x9ec72c: mov             SP, fp
    //     0x9ec730: ldp             fp, lr, [SP], #0x10
    // 0x9ec734: ret
    //     0x9ec734: ret             
    // 0x9ec738: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ec738: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ec73c: b               #0x9ec704
  }
  [closure] VisualDensity? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ec740, size: 0x50
    // 0x9ec740: EnterFrame
    //     0x9ec740: stp             fp, lr, [SP, #-0x10]!
    //     0x9ec744: mov             fp, SP
    // 0x9ec748: CheckStackOverflow
    //     0x9ec748: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ec74c: cmp             SP, x16
    //     0x9ec750: b.ls            #0x9ec788
    // 0x9ec754: ldr             x1, [fp, #0x10]
    // 0x9ec758: cmp             w1, NULL
    // 0x9ec75c: b.ne            #0x9ec768
    // 0x9ec760: r0 = Null
    //     0x9ec760: mov             x0, NULL
    // 0x9ec764: b               #0x9ec77c
    // 0x9ec768: r0 = LoadClassIdInstr(r1)
    //     0x9ec768: ldur            x0, [x1, #-1]
    //     0x9ec76c: ubfx            x0, x0, #0xc, #0x14
    // 0x9ec770: r0 = GDT[cid_x0 + 0xf66]()
    //     0x9ec770: add             lr, x0, #0xf66
    //     0x9ec774: ldr             lr, [x21, lr, lsl #3]
    //     0x9ec778: blr             lr
    // 0x9ec77c: LeaveFrame
    //     0x9ec77c: mov             SP, fp
    //     0x9ec780: ldp             fp, lr, [SP], #0x10
    // 0x9ec784: ret
    //     0x9ec784: ret             
    // 0x9ec788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ec788: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ec78c: b               #0x9ec754
  }
  [closure] Color? <anonymous closure>(dynamic, Set<WidgetState>) {
    // ** addr: 0x9ec790, size: 0x98
    // 0x9ec790: EnterFrame
    //     0x9ec790: stp             fp, lr, [SP, #-0x10]!
    //     0x9ec794: mov             fp, SP
    // 0x9ec798: AllocStack(0x28)
    //     0x9ec798: sub             SP, SP, #0x28
    // 0x9ec79c: SetupParameters()
    //     0x9ec79c: ldr             x0, [fp, #0x18]
    //     0x9ec7a0: ldur            w1, [x0, #0x17]
    //     0x9ec7a4: add             x1, x1, HEAP, lsl #32
    //     0x9ec7a8: stur            x1, [fp, #-8]
    // 0x9ec7ac: CheckStackOverflow
    //     0x9ec7ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ec7b0: cmp             SP, x16
    //     0x9ec7b4: b.ls            #0x9ec820
    // 0x9ec7b8: r1 = 1
    //     0x9ec7b8: movz            x1, #0x1
    // 0x9ec7bc: r0 = AllocateContext()
    //     0x9ec7bc: bl              #0xec126c  ; AllocateContextStub
    // 0x9ec7c0: mov             x1, x0
    // 0x9ec7c4: ldur            x0, [fp, #-8]
    // 0x9ec7c8: StoreField: r1->field_b = r0
    //     0x9ec7c8: stur            w0, [x1, #0xb]
    // 0x9ec7cc: ldr             x2, [fp, #0x10]
    // 0x9ec7d0: StoreField: r1->field_f = r2
    //     0x9ec7d0: stur            w2, [x1, #0xf]
    // 0x9ec7d4: LoadField: r3 = r0->field_1f
    //     0x9ec7d4: ldur            w3, [x0, #0x1f]
    // 0x9ec7d8: DecompressPointer r3
    //     0x9ec7d8: add             x3, x3, HEAP, lsl #32
    // 0x9ec7dc: mov             x2, x1
    // 0x9ec7e0: stur            x3, [fp, #-0x10]
    // 0x9ec7e4: r1 = Function '<anonymous closure>':.
    //     0x9ec7e4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44640] AnonymousClosure: (0x9ec828), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9ec7e8: ldr             x1, [x1, #0x640]
    // 0x9ec7ec: r0 = AllocateClosure()
    //     0x9ec7ec: bl              #0xec1630  ; AllocateClosureStub
    // 0x9ec7f0: r16 = <Color>
    //     0x9ec7f0: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0x9ec7f4: ldr             x16, [x16, #0x158]
    // 0x9ec7f8: ldur            lr, [fp, #-0x10]
    // 0x9ec7fc: stp             lr, x16, [SP, #8]
    // 0x9ec800: str             x0, [SP]
    // 0x9ec804: ldur            x0, [fp, #-0x10]
    // 0x9ec808: ClosureCall
    //     0x9ec808: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9ec80c: ldur            x2, [x0, #0x1f]
    //     0x9ec810: blr             x2
    // 0x9ec814: LeaveFrame
    //     0x9ec814: mov             SP, fp
    //     0x9ec818: ldp             fp, lr, [SP], #0x10
    // 0x9ec81c: ret
    //     0x9ec81c: ret             
    // 0x9ec820: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ec820: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ec824: b               #0x9ec7b8
  }
  [closure] Color? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ec828, size: 0xa8
    // 0x9ec828: EnterFrame
    //     0x9ec828: stp             fp, lr, [SP, #-0x10]!
    //     0x9ec82c: mov             fp, SP
    // 0x9ec830: AllocStack(0x8)
    //     0x9ec830: sub             SP, SP, #8
    // 0x9ec834: SetupParameters()
    //     0x9ec834: ldr             x0, [fp, #0x18]
    //     0x9ec838: ldur            w2, [x0, #0x17]
    //     0x9ec83c: add             x2, x2, HEAP, lsl #32
    //     0x9ec840: stur            x2, [fp, #-8]
    // 0x9ec844: CheckStackOverflow
    //     0x9ec844: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ec848: cmp             SP, x16
    //     0x9ec84c: b.ls            #0x9ec8c8
    // 0x9ec850: ldr             x1, [fp, #0x10]
    // 0x9ec854: cmp             w1, NULL
    // 0x9ec858: b.ne            #0x9ec864
    // 0x9ec85c: r0 = Null
    //     0x9ec85c: mov             x0, NULL
    // 0x9ec860: b               #0x9ec8bc
    // 0x9ec864: r0 = LoadClassIdInstr(r1)
    //     0x9ec864: ldur            x0, [x1, #-1]
    //     0x9ec868: ubfx            x0, x0, #0xc, #0x14
    // 0x9ec86c: r0 = GDT[cid_x0 + 0xf50]()
    //     0x9ec86c: add             lr, x0, #0xf50
    //     0x9ec870: ldr             lr, [x21, lr, lsl #3]
    //     0x9ec874: blr             lr
    // 0x9ec878: cmp             w0, NULL
    // 0x9ec87c: b.ne            #0x9ec888
    // 0x9ec880: r1 = Null
    //     0x9ec880: mov             x1, NULL
    // 0x9ec884: b               #0x9ec8b8
    // 0x9ec888: ldur            x1, [fp, #-8]
    // 0x9ec88c: LoadField: r2 = r1->field_f
    //     0x9ec88c: ldur            w2, [x1, #0xf]
    // 0x9ec890: DecompressPointer r2
    //     0x9ec890: add             x2, x2, HEAP, lsl #32
    // 0x9ec894: r1 = LoadClassIdInstr(r0)
    //     0x9ec894: ldur            x1, [x0, #-1]
    //     0x9ec898: ubfx            x1, x1, #0xc, #0x14
    // 0x9ec89c: mov             x16, x0
    // 0x9ec8a0: mov             x0, x1
    // 0x9ec8a4: mov             x1, x16
    // 0x9ec8a8: r0 = GDT[cid_x0 + -0xfdb]()
    //     0x9ec8a8: sub             lr, x0, #0xfdb
    //     0x9ec8ac: ldr             lr, [x21, lr, lsl #3]
    //     0x9ec8b0: blr             lr
    // 0x9ec8b4: mov             x1, x0
    // 0x9ec8b8: mov             x0, x1
    // 0x9ec8bc: LeaveFrame
    //     0x9ec8bc: mov             SP, fp
    //     0x9ec8c0: ldp             fp, lr, [SP], #0x10
    // 0x9ec8c4: ret
    //     0x9ec8c4: ret             
    // 0x9ec8c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ec8c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ec8cc: b               #0x9ec850
  }
  [closure] MouseCursor? <anonymous closure>(dynamic, Set<WidgetState>) {
    // ** addr: 0x9ec8d0, size: 0x94
    // 0x9ec8d0: EnterFrame
    //     0x9ec8d0: stp             fp, lr, [SP, #-0x10]!
    //     0x9ec8d4: mov             fp, SP
    // 0x9ec8d8: AllocStack(0x28)
    //     0x9ec8d8: sub             SP, SP, #0x28
    // 0x9ec8dc: SetupParameters()
    //     0x9ec8dc: ldr             x0, [fp, #0x18]
    //     0x9ec8e0: ldur            w1, [x0, #0x17]
    //     0x9ec8e4: add             x1, x1, HEAP, lsl #32
    //     0x9ec8e8: stur            x1, [fp, #-8]
    // 0x9ec8ec: CheckStackOverflow
    //     0x9ec8ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ec8f0: cmp             SP, x16
    //     0x9ec8f4: b.ls            #0x9ec95c
    // 0x9ec8f8: r1 = 1
    //     0x9ec8f8: movz            x1, #0x1
    // 0x9ec8fc: r0 = AllocateContext()
    //     0x9ec8fc: bl              #0xec126c  ; AllocateContextStub
    // 0x9ec900: mov             x1, x0
    // 0x9ec904: ldur            x0, [fp, #-8]
    // 0x9ec908: StoreField: r1->field_b = r0
    //     0x9ec908: stur            w0, [x1, #0xb]
    // 0x9ec90c: ldr             x2, [fp, #0x10]
    // 0x9ec910: StoreField: r1->field_f = r2
    //     0x9ec910: stur            w2, [x1, #0xf]
    // 0x9ec914: LoadField: r3 = r0->field_1f
    //     0x9ec914: ldur            w3, [x0, #0x1f]
    // 0x9ec918: DecompressPointer r3
    //     0x9ec918: add             x3, x3, HEAP, lsl #32
    // 0x9ec91c: mov             x2, x1
    // 0x9ec920: stur            x3, [fp, #-0x10]
    // 0x9ec924: r1 = Function '<anonymous closure>':.
    //     0x9ec924: add             x1, PP, #0x44, lsl #12  ; [pp+0x44648] AnonymousClosure: (0x9ec964), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9ec928: ldr             x1, [x1, #0x648]
    // 0x9ec92c: r0 = AllocateClosure()
    //     0x9ec92c: bl              #0xec1630  ; AllocateClosureStub
    // 0x9ec930: r16 = <MouseCursor>
    //     0x9ec930: ldr             x16, [PP, #0x26a0]  ; [pp+0x26a0] TypeArguments: <MouseCursor>
    // 0x9ec934: ldur            lr, [fp, #-0x10]
    // 0x9ec938: stp             lr, x16, [SP, #8]
    // 0x9ec93c: str             x0, [SP]
    // 0x9ec940: ldur            x0, [fp, #-0x10]
    // 0x9ec944: ClosureCall
    //     0x9ec944: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9ec948: ldur            x2, [x0, #0x1f]
    //     0x9ec94c: blr             x2
    // 0x9ec950: LeaveFrame
    //     0x9ec950: mov             SP, fp
    //     0x9ec954: ldp             fp, lr, [SP], #0x10
    // 0x9ec958: ret
    //     0x9ec958: ret             
    // 0x9ec95c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ec95c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ec960: b               #0x9ec8f8
  }
  [closure] MouseCursor? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ec964, size: 0xa8
    // 0x9ec964: EnterFrame
    //     0x9ec964: stp             fp, lr, [SP, #-0x10]!
    //     0x9ec968: mov             fp, SP
    // 0x9ec96c: AllocStack(0x8)
    //     0x9ec96c: sub             SP, SP, #8
    // 0x9ec970: SetupParameters()
    //     0x9ec970: ldr             x0, [fp, #0x18]
    //     0x9ec974: ldur            w2, [x0, #0x17]
    //     0x9ec978: add             x2, x2, HEAP, lsl #32
    //     0x9ec97c: stur            x2, [fp, #-8]
    // 0x9ec980: CheckStackOverflow
    //     0x9ec980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ec984: cmp             SP, x16
    //     0x9ec988: b.ls            #0x9eca04
    // 0x9ec98c: ldr             x1, [fp, #0x10]
    // 0x9ec990: cmp             w1, NULL
    // 0x9ec994: b.ne            #0x9ec9a0
    // 0x9ec998: r0 = Null
    //     0x9ec998: mov             x0, NULL
    // 0x9ec99c: b               #0x9ec9f8
    // 0x9ec9a0: r0 = LoadClassIdInstr(r1)
    //     0x9ec9a0: ldur            x0, [x1, #-1]
    //     0x9ec9a4: ubfx            x0, x0, #0xc, #0x14
    // 0x9ec9a8: r0 = GDT[cid_x0 + 0xf0e]()
    //     0x9ec9a8: add             lr, x0, #0xf0e
    //     0x9ec9ac: ldr             lr, [x21, lr, lsl #3]
    //     0x9ec9b0: blr             lr
    // 0x9ec9b4: cmp             w0, NULL
    // 0x9ec9b8: b.ne            #0x9ec9c4
    // 0x9ec9bc: r1 = Null
    //     0x9ec9bc: mov             x1, NULL
    // 0x9ec9c0: b               #0x9ec9f4
    // 0x9ec9c4: ldur            x1, [fp, #-8]
    // 0x9ec9c8: LoadField: r2 = r1->field_f
    //     0x9ec9c8: ldur            w2, [x1, #0xf]
    // 0x9ec9cc: DecompressPointer r2
    //     0x9ec9cc: add             x2, x2, HEAP, lsl #32
    // 0x9ec9d0: r1 = LoadClassIdInstr(r0)
    //     0x9ec9d0: ldur            x1, [x0, #-1]
    //     0x9ec9d4: ubfx            x1, x1, #0xc, #0x14
    // 0x9ec9d8: mov             x16, x0
    // 0x9ec9dc: mov             x0, x1
    // 0x9ec9e0: mov             x1, x16
    // 0x9ec9e4: r0 = GDT[cid_x0 + -0xfdb]()
    //     0x9ec9e4: sub             lr, x0, #0xfdb
    //     0x9ec9e8: ldr             lr, [x21, lr, lsl #3]
    //     0x9ec9ec: blr             lr
    // 0x9ec9f0: mov             x1, x0
    // 0x9ec9f4: mov             x0, x1
    // 0x9ec9f8: LeaveFrame
    //     0x9ec9f8: mov             SP, fp
    //     0x9ec9fc: ldp             fp, lr, [SP], #0x10
    // 0x9eca00: ret
    //     0x9eca00: ret             
    // 0x9eca04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9eca04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9eca08: b               #0x9ec98c
  }
  [closure] WidgetStateProperty<OutlinedBorder?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9eca0c, size: 0x50
    // 0x9eca0c: EnterFrame
    //     0x9eca0c: stp             fp, lr, [SP, #-0x10]!
    //     0x9eca10: mov             fp, SP
    // 0x9eca14: CheckStackOverflow
    //     0x9eca14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9eca18: cmp             SP, x16
    //     0x9eca1c: b.ls            #0x9eca54
    // 0x9eca20: ldr             x1, [fp, #0x10]
    // 0x9eca24: cmp             w1, NULL
    // 0x9eca28: b.ne            #0x9eca34
    // 0x9eca2c: r0 = Null
    //     0x9eca2c: mov             x0, NULL
    // 0x9eca30: b               #0x9eca48
    // 0x9eca34: r0 = LoadClassIdInstr(r1)
    //     0x9eca34: ldur            x0, [x1, #-1]
    //     0x9eca38: ubfx            x0, x0, #0xc, #0x14
    // 0x9eca3c: r0 = GDT[cid_x0 + 0xece]()
    //     0x9eca3c: add             lr, x0, #0xece
    //     0x9eca40: ldr             lr, [x21, lr, lsl #3]
    //     0x9eca44: blr             lr
    // 0x9eca48: LeaveFrame
    //     0x9eca48: mov             SP, fp
    //     0x9eca4c: ldp             fp, lr, [SP], #0x10
    // 0x9eca50: ret
    //     0x9eca50: ret             
    // 0x9eca54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9eca54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9eca58: b               #0x9eca20
  }
  [closure] WidgetStateProperty<BorderSide?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9eca5c, size: 0x50
    // 0x9eca5c: EnterFrame
    //     0x9eca5c: stp             fp, lr, [SP, #-0x10]!
    //     0x9eca60: mov             fp, SP
    // 0x9eca64: CheckStackOverflow
    //     0x9eca64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9eca68: cmp             SP, x16
    //     0x9eca6c: b.ls            #0x9ecaa4
    // 0x9eca70: ldr             x1, [fp, #0x10]
    // 0x9eca74: cmp             w1, NULL
    // 0x9eca78: b.ne            #0x9eca84
    // 0x9eca7c: r0 = Null
    //     0x9eca7c: mov             x0, NULL
    // 0x9eca80: b               #0x9eca98
    // 0x9eca84: r0 = LoadClassIdInstr(r1)
    //     0x9eca84: ldur            x0, [x1, #-1]
    //     0x9eca88: ubfx            x0, x0, #0xc, #0x14
    // 0x9eca8c: r0 = GDT[cid_x0 + 0xeb5]()
    //     0x9eca8c: add             lr, x0, #0xeb5
    //     0x9eca90: ldr             lr, [x21, lr, lsl #3]
    //     0x9eca94: blr             lr
    // 0x9eca98: LeaveFrame
    //     0x9eca98: mov             SP, fp
    //     0x9eca9c: ldp             fp, lr, [SP], #0x10
    // 0x9ecaa0: ret
    //     0x9ecaa0: ret             
    // 0x9ecaa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ecaa4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ecaa8: b               #0x9eca70
  }
  [closure] WidgetStateProperty<double?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ecaac, size: 0x50
    // 0x9ecaac: EnterFrame
    //     0x9ecaac: stp             fp, lr, [SP, #-0x10]!
    //     0x9ecab0: mov             fp, SP
    // 0x9ecab4: CheckStackOverflow
    //     0x9ecab4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ecab8: cmp             SP, x16
    //     0x9ecabc: b.ls            #0x9ecaf4
    // 0x9ecac0: ldr             x1, [fp, #0x10]
    // 0x9ecac4: cmp             w1, NULL
    // 0x9ecac8: b.ne            #0x9ecad4
    // 0x9ecacc: r0 = Null
    //     0x9ecacc: mov             x0, NULL
    // 0x9ecad0: b               #0x9ecae8
    // 0x9ecad4: r0 = LoadClassIdInstr(r1)
    //     0x9ecad4: ldur            x0, [x1, #-1]
    //     0x9ecad8: ubfx            x0, x0, #0xc, #0x14
    // 0x9ecadc: r0 = GDT[cid_x0 + 0xe9c]()
    //     0x9ecadc: add             lr, x0, #0xe9c
    //     0x9ecae0: ldr             lr, [x21, lr, lsl #3]
    //     0x9ecae4: blr             lr
    // 0x9ecae8: LeaveFrame
    //     0x9ecae8: mov             SP, fp
    //     0x9ecaec: ldp             fp, lr, [SP], #0x10
    // 0x9ecaf0: ret
    //     0x9ecaf0: ret             
    // 0x9ecaf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ecaf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ecaf8: b               #0x9ecac0
  }
  [closure] WidgetStateProperty<Color?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ecafc, size: 0x50
    // 0x9ecafc: EnterFrame
    //     0x9ecafc: stp             fp, lr, [SP, #-0x10]!
    //     0x9ecb00: mov             fp, SP
    // 0x9ecb04: CheckStackOverflow
    //     0x9ecb04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ecb08: cmp             SP, x16
    //     0x9ecb0c: b.ls            #0x9ecb44
    // 0x9ecb10: ldr             x1, [fp, #0x10]
    // 0x9ecb14: cmp             w1, NULL
    // 0x9ecb18: b.ne            #0x9ecb24
    // 0x9ecb1c: r0 = Null
    //     0x9ecb1c: mov             x0, NULL
    // 0x9ecb20: b               #0x9ecb38
    // 0x9ecb24: r0 = LoadClassIdInstr(r1)
    //     0x9ecb24: ldur            x0, [x1, #-1]
    //     0x9ecb28: ubfx            x0, x0, #0xc, #0x14
    // 0x9ecb2c: r0 = GDT[cid_x0 + 0xdcc]()
    //     0x9ecb2c: add             lr, x0, #0xdcc
    //     0x9ecb30: ldr             lr, [x21, lr, lsl #3]
    //     0x9ecb34: blr             lr
    // 0x9ecb38: LeaveFrame
    //     0x9ecb38: mov             SP, fp
    //     0x9ecb3c: ldp             fp, lr, [SP], #0x10
    // 0x9ecb40: ret
    //     0x9ecb40: ret             
    // 0x9ecb44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ecb44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ecb48: b               #0x9ecb10
  }
  [closure] WidgetStateProperty<Size?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ecb4c, size: 0x50
    // 0x9ecb4c: EnterFrame
    //     0x9ecb4c: stp             fp, lr, [SP, #-0x10]!
    //     0x9ecb50: mov             fp, SP
    // 0x9ecb54: CheckStackOverflow
    //     0x9ecb54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ecb58: cmp             SP, x16
    //     0x9ecb5c: b.ls            #0x9ecb94
    // 0x9ecb60: ldr             x1, [fp, #0x10]
    // 0x9ecb64: cmp             w1, NULL
    // 0x9ecb68: b.ne            #0x9ecb74
    // 0x9ecb6c: r0 = Null
    //     0x9ecb6c: mov             x0, NULL
    // 0x9ecb70: b               #0x9ecb88
    // 0x9ecb74: r0 = LoadClassIdInstr(r1)
    //     0x9ecb74: ldur            x0, [x1, #-1]
    //     0x9ecb78: ubfx            x0, x0, #0xc, #0x14
    // 0x9ecb7c: r0 = GDT[cid_x0 + 0xe8d]()
    //     0x9ecb7c: add             lr, x0, #0xe8d
    //     0x9ecb80: ldr             lr, [x21, lr, lsl #3]
    //     0x9ecb84: blr             lr
    // 0x9ecb88: LeaveFrame
    //     0x9ecb88: mov             SP, fp
    //     0x9ecb8c: ldp             fp, lr, [SP], #0x10
    // 0x9ecb90: ret
    //     0x9ecb90: ret             
    // 0x9ecb94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ecb94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ecb98: b               #0x9ecb60
  }
  [closure] WidgetStateProperty<Size?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ecb9c, size: 0x24
    // 0x9ecb9c: ldr             x1, [SP]
    // 0x9ecba0: cmp             w1, NULL
    // 0x9ecba4: b.ne            #0x9ecbb0
    // 0x9ecba8: r0 = Null
    //     0x9ecba8: mov             x0, NULL
    // 0x9ecbac: b               #0x9ecbbc
    // 0x9ecbb0: LoadField: r2 = r1->field_2b
    //     0x9ecbb0: ldur            w2, [x1, #0x2b]
    // 0x9ecbb4: DecompressPointer r2
    //     0x9ecbb4: add             x2, x2, HEAP, lsl #32
    // 0x9ecbb8: mov             x0, x2
    // 0x9ecbbc: ret
    //     0x9ecbbc: ret             
  }
  [closure] WidgetStateProperty<Size?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ecbc0, size: 0x50
    // 0x9ecbc0: EnterFrame
    //     0x9ecbc0: stp             fp, lr, [SP, #-0x10]!
    //     0x9ecbc4: mov             fp, SP
    // 0x9ecbc8: CheckStackOverflow
    //     0x9ecbc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ecbcc: cmp             SP, x16
    //     0x9ecbd0: b.ls            #0x9ecc08
    // 0x9ecbd4: ldr             x1, [fp, #0x10]
    // 0x9ecbd8: cmp             w1, NULL
    // 0x9ecbdc: b.ne            #0x9ecbe8
    // 0x9ecbe0: r0 = Null
    //     0x9ecbe0: mov             x0, NULL
    // 0x9ecbe4: b               #0x9ecbfc
    // 0x9ecbe8: r0 = LoadClassIdInstr(r1)
    //     0x9ecbe8: ldur            x0, [x1, #-1]
    //     0x9ecbec: ubfx            x0, x0, #0xc, #0x14
    // 0x9ecbf0: r0 = GDT[cid_x0 + 0xdc0]()
    //     0x9ecbf0: add             lr, x0, #0xdc0
    //     0x9ecbf4: ldr             lr, [x21, lr, lsl #3]
    //     0x9ecbf8: blr             lr
    // 0x9ecbfc: LeaveFrame
    //     0x9ecbfc: mov             SP, fp
    //     0x9ecc00: ldp             fp, lr, [SP], #0x10
    // 0x9ecc04: ret
    //     0x9ecc04: ret             
    // 0x9ecc08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ecc08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ecc0c: b               #0x9ecbd4
  }
  [closure] WidgetStateProperty<EdgeInsetsGeometry?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ecc10, size: 0x50
    // 0x9ecc10: EnterFrame
    //     0x9ecc10: stp             fp, lr, [SP, #-0x10]!
    //     0x9ecc14: mov             fp, SP
    // 0x9ecc18: CheckStackOverflow
    //     0x9ecc18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ecc1c: cmp             SP, x16
    //     0x9ecc20: b.ls            #0x9ecc58
    // 0x9ecc24: ldr             x1, [fp, #0x10]
    // 0x9ecc28: cmp             w1, NULL
    // 0x9ecc2c: b.ne            #0x9ecc38
    // 0x9ecc30: r0 = Null
    //     0x9ecc30: mov             x0, NULL
    // 0x9ecc34: b               #0x9ecc4c
    // 0x9ecc38: r0 = LoadClassIdInstr(r1)
    //     0x9ecc38: ldur            x0, [x1, #-1]
    //     0x9ecc3c: ubfx            x0, x0, #0xc, #0x14
    // 0x9ecc40: r0 = GDT[cid_x0 + 0xfd4]()
    //     0x9ecc40: add             lr, x0, #0xfd4
    //     0x9ecc44: ldr             lr, [x21, lr, lsl #3]
    //     0x9ecc48: blr             lr
    // 0x9ecc4c: LeaveFrame
    //     0x9ecc4c: mov             SP, fp
    //     0x9ecc50: ldp             fp, lr, [SP], #0x10
    // 0x9ecc54: ret
    //     0x9ecc54: ret             
    // 0x9ecc58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ecc58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ecc5c: b               #0x9ecc24
  }
  [closure] WidgetStateProperty<Color?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ecc60, size: 0x50
    // 0x9ecc60: EnterFrame
    //     0x9ecc60: stp             fp, lr, [SP, #-0x10]!
    //     0x9ecc64: mov             fp, SP
    // 0x9ecc68: CheckStackOverflow
    //     0x9ecc68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ecc6c: cmp             SP, x16
    //     0x9ecc70: b.ls            #0x9ecca8
    // 0x9ecc74: ldr             x1, [fp, #0x10]
    // 0x9ecc78: cmp             w1, NULL
    // 0x9ecc7c: b.ne            #0x9ecc88
    // 0x9ecc80: r0 = Null
    //     0x9ecc80: mov             x0, NULL
    // 0x9ecc84: b               #0x9ecc9c
    // 0x9ecc88: r0 = LoadClassIdInstr(r1)
    //     0x9ecc88: ldur            x0, [x1, #-1]
    //     0x9ecc8c: ubfx            x0, x0, #0xc, #0x14
    // 0x9ecc90: r0 = GDT[cid_x0 + 0x936]()
    //     0x9ecc90: add             lr, x0, #0x936
    //     0x9ecc94: ldr             lr, [x21, lr, lsl #3]
    //     0x9ecc98: blr             lr
    // 0x9ecc9c: LeaveFrame
    //     0x9ecc9c: mov             SP, fp
    //     0x9ecca0: ldp             fp, lr, [SP], #0x10
    // 0x9ecca4: ret
    //     0x9ecca4: ret             
    // 0x9ecca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ecca8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9eccac: b               #0x9ecc74
  }
  [closure] WidgetStateProperty<Color?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9eccb0, size: 0x50
    // 0x9eccb0: EnterFrame
    //     0x9eccb0: stp             fp, lr, [SP, #-0x10]!
    //     0x9eccb4: mov             fp, SP
    // 0x9eccb8: CheckStackOverflow
    //     0x9eccb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9eccbc: cmp             SP, x16
    //     0x9eccc0: b.ls            #0x9eccf8
    // 0x9eccc4: ldr             x1, [fp, #0x10]
    // 0x9eccc8: cmp             w1, NULL
    // 0x9ecccc: b.ne            #0x9eccd8
    // 0x9eccd0: r0 = Null
    //     0x9eccd0: mov             x0, NULL
    // 0x9eccd4: b               #0x9eccec
    // 0x9eccd8: r0 = LoadClassIdInstr(r1)
    //     0x9eccd8: ldur            x0, [x1, #-1]
    //     0x9eccdc: ubfx            x0, x0, #0xc, #0x14
    // 0x9ecce0: r0 = GDT[cid_x0 + 0xf29]()
    //     0x9ecce0: add             lr, x0, #0xf29
    //     0x9ecce4: ldr             lr, [x21, lr, lsl #3]
    //     0x9ecce8: blr             lr
    // 0x9eccec: LeaveFrame
    //     0x9eccec: mov             SP, fp
    //     0x9eccf0: ldp             fp, lr, [SP], #0x10
    // 0x9eccf4: ret
    //     0x9eccf4: ret             
    // 0x9eccf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9eccf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9eccfc: b               #0x9eccc4
  }
  [closure] WidgetStateProperty<Color?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ecd00, size: 0x50
    // 0x9ecd00: EnterFrame
    //     0x9ecd00: stp             fp, lr, [SP, #-0x10]!
    //     0x9ecd04: mov             fp, SP
    // 0x9ecd08: CheckStackOverflow
    //     0x9ecd08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ecd0c: cmp             SP, x16
    //     0x9ecd10: b.ls            #0x9ecd48
    // 0x9ecd14: ldr             x1, [fp, #0x10]
    // 0x9ecd18: cmp             w1, NULL
    // 0x9ecd1c: b.ne            #0x9ecd28
    // 0x9ecd20: r0 = Null
    //     0x9ecd20: mov             x0, NULL
    // 0x9ecd24: b               #0x9ecd3c
    // 0x9ecd28: r0 = LoadClassIdInstr(r1)
    //     0x9ecd28: ldur            x0, [x1, #-1]
    //     0x9ecd2c: ubfx            x0, x0, #0xc, #0x14
    // 0x9ecd30: r0 = GDT[cid_x0 + 0xf01]()
    //     0x9ecd30: add             lr, x0, #0xf01
    //     0x9ecd34: ldr             lr, [x21, lr, lsl #3]
    //     0x9ecd38: blr             lr
    // 0x9ecd3c: LeaveFrame
    //     0x9ecd3c: mov             SP, fp
    //     0x9ecd40: ldp             fp, lr, [SP], #0x10
    // 0x9ecd44: ret
    //     0x9ecd44: ret             
    // 0x9ecd48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ecd48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ecd4c: b               #0x9ecd14
  }
  [closure] WidgetStateProperty<Color?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ecd50, size: 0x50
    // 0x9ecd50: EnterFrame
    //     0x9ecd50: stp             fp, lr, [SP, #-0x10]!
    //     0x9ecd54: mov             fp, SP
    // 0x9ecd58: CheckStackOverflow
    //     0x9ecd58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ecd5c: cmp             SP, x16
    //     0x9ecd60: b.ls            #0x9ecd98
    // 0x9ecd64: ldr             x1, [fp, #0x10]
    // 0x9ecd68: cmp             w1, NULL
    // 0x9ecd6c: b.ne            #0x9ecd78
    // 0x9ecd70: r0 = Null
    //     0x9ecd70: mov             x0, NULL
    // 0x9ecd74: b               #0x9ecd8c
    // 0x9ecd78: r0 = LoadClassIdInstr(r1)
    //     0x9ecd78: ldur            x0, [x1, #-1]
    //     0x9ecd7c: ubfx            x0, x0, #0xc, #0x14
    // 0x9ecd80: r0 = GDT[cid_x0 + 0xda8]()
    //     0x9ecd80: add             lr, x0, #0xda8
    //     0x9ecd84: ldr             lr, [x21, lr, lsl #3]
    //     0x9ecd88: blr             lr
    // 0x9ecd8c: LeaveFrame
    //     0x9ecd8c: mov             SP, fp
    //     0x9ecd90: ldp             fp, lr, [SP], #0x10
    // 0x9ecd94: ret
    //     0x9ecd94: ret             
    // 0x9ecd98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ecd98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ecd9c: b               #0x9ecd64
  }
  [closure] WidgetStateProperty<TextStyle?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ecda0, size: 0x50
    // 0x9ecda0: EnterFrame
    //     0x9ecda0: stp             fp, lr, [SP, #-0x10]!
    //     0x9ecda4: mov             fp, SP
    // 0x9ecda8: CheckStackOverflow
    //     0x9ecda8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ecdac: cmp             SP, x16
    //     0x9ecdb0: b.ls            #0x9ecde8
    // 0x9ecdb4: ldr             x1, [fp, #0x10]
    // 0x9ecdb8: cmp             w1, NULL
    // 0x9ecdbc: b.ne            #0x9ecdc8
    // 0x9ecdc0: r0 = Null
    //     0x9ecdc0: mov             x0, NULL
    // 0x9ecdc4: b               #0x9ecddc
    // 0x9ecdc8: r0 = LoadClassIdInstr(r1)
    //     0x9ecdc8: ldur            x0, [x1, #-1]
    //     0x9ecdcc: ubfx            x0, x0, #0xc, #0x14
    // 0x9ecdd0: r0 = GDT[cid_x0 + -0xcdd]()
    //     0x9ecdd0: sub             lr, x0, #0xcdd
    //     0x9ecdd4: ldr             lr, [x21, lr, lsl #3]
    //     0x9ecdd8: blr             lr
    // 0x9ecddc: LeaveFrame
    //     0x9ecddc: mov             SP, fp
    //     0x9ecde0: ldp             fp, lr, [SP], #0x10
    // 0x9ecde4: ret
    //     0x9ecde4: ret             
    // 0x9ecde8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ecde8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ecdec: b               #0x9ecdb4
  }
  [closure] WidgetStateProperty<double?>? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ecdf0, size: 0x50
    // 0x9ecdf0: EnterFrame
    //     0x9ecdf0: stp             fp, lr, [SP, #-0x10]!
    //     0x9ecdf4: mov             fp, SP
    // 0x9ecdf8: CheckStackOverflow
    //     0x9ecdf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ecdfc: cmp             SP, x16
    //     0x9ece00: b.ls            #0x9ece38
    // 0x9ece04: ldr             x1, [fp, #0x10]
    // 0x9ece08: cmp             w1, NULL
    // 0x9ece0c: b.ne            #0x9ece18
    // 0x9ece10: r0 = Null
    //     0x9ece10: mov             x0, NULL
    // 0x9ece14: b               #0x9ece2c
    // 0x9ece18: r0 = LoadClassIdInstr(r1)
    //     0x9ece18: ldur            x0, [x1, #-1]
    //     0x9ece1c: ubfx            x0, x0, #0xc, #0x14
    // 0x9ece20: r0 = GDT[cid_x0 + 0xef2]()
    //     0x9ece20: add             lr, x0, #0xef2
    //     0x9ece24: ldr             lr, [x21, lr, lsl #3]
    //     0x9ece28: blr             lr
    // 0x9ece2c: LeaveFrame
    //     0x9ece2c: mov             SP, fp
    //     0x9ece30: ldp             fp, lr, [SP], #0x10
    // 0x9ece34: ret
    //     0x9ece34: ret             
    // 0x9ece38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ece38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ece3c: b               #0x9ece04
  }
  [closure] Y0? resolve<Y0>(dynamic, (dynamic, ButtonStyle?) => WidgetStateProperty<Y0>?) {
    // ** addr: 0x9ece40, size: 0xdc
    // 0x9ece40: EnterFrame
    //     0x9ece40: stp             fp, lr, [SP, #-0x10]!
    //     0x9ece44: mov             fp, SP
    // 0x9ece48: AllocStack(0x30)
    //     0x9ece48: sub             SP, SP, #0x30
    // 0x9ece4c: SetupParameters()
    //     0x9ece4c: ldr             x0, [fp, #0x18]
    //     0x9ece50: ldur            w1, [x0, #0x17]
    //     0x9ece54: add             x1, x1, HEAP, lsl #32
    //     0x9ece58: stur            x1, [fp, #-0x10]
    // 0x9ece5c: LoadField: r2 = r4->field_f
    //     0x9ece5c: ldur            w2, [x4, #0xf]
    // 0x9ece60: cbnz            w2, #0x9ece6c
    // 0x9ece64: r2 = Null
    //     0x9ece64: mov             x2, NULL
    // 0x9ece68: b               #0x9ece7c
    // 0x9ece6c: ArrayLoad: r2 = r4[0]  ; List_4
    //     0x9ece6c: ldur            w2, [x4, #0x17]
    // 0x9ece70: add             x3, fp, w2, sxtw #2
    // 0x9ece74: ldr             x3, [x3, #0x10]
    // 0x9ece78: mov             x2, x3
    // 0x9ece7c: LoadField: r3 = r0->field_f
    //     0x9ece7c: ldur            w3, [x0, #0xf]
    // 0x9ece80: DecompressPointer r3
    //     0x9ece80: add             x3, x3, HEAP, lsl #32
    // 0x9ece84: ldr             x16, [THR, #0x98]  ; THR::empty_type_arguments
    // 0x9ece88: cmp             w3, w16
    // 0x9ece8c: b.eq            #0x9ece94
    // 0x9ece90: mov             x2, x3
    // 0x9ece94: ldr             x0, [fp, #0x10]
    // 0x9ece98: stur            x2, [fp, #-8]
    // 0x9ece9c: CheckStackOverflow
    //     0x9ece9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ecea0: cmp             SP, x16
    //     0x9ecea4: b.ls            #0x9ecf14
    // 0x9ecea8: r1 = 1
    //     0x9ecea8: movz            x1, #0x1
    // 0x9eceac: r0 = AllocateContext()
    //     0x9eceac: bl              #0xec126c  ; AllocateContextStub
    // 0x9eceb0: mov             x1, x0
    // 0x9eceb4: ldur            x0, [fp, #-0x10]
    // 0x9eceb8: StoreField: r1->field_b = r0
    //     0x9eceb8: stur            w0, [x1, #0xb]
    // 0x9ecebc: ldr             x2, [fp, #0x10]
    // 0x9ecec0: StoreField: r1->field_f = r2
    //     0x9ecec0: stur            w2, [x1, #0xf]
    // 0x9ecec4: LoadField: r3 = r0->field_1f
    //     0x9ecec4: ldur            w3, [x0, #0x1f]
    // 0x9ecec8: DecompressPointer r3
    //     0x9ecec8: add             x3, x3, HEAP, lsl #32
    // 0x9ececc: mov             x2, x1
    // 0x9eced0: stur            x3, [fp, #-0x18]
    // 0x9eced4: r1 = Function '<anonymous closure>':.
    //     0x9eced4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44650] AnonymousClosure: (0x9ecf1c), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::build (0x9eb3cc)
    //     0x9eced8: ldr             x1, [x1, #0x650]
    // 0x9ecedc: r0 = AllocateClosure()
    //     0x9ecedc: bl              #0xec1630  ; AllocateClosureStub
    // 0x9ecee0: mov             x1, x0
    // 0x9ecee4: ldur            x0, [fp, #-8]
    // 0x9ecee8: StoreField: r1->field_b = r0
    //     0x9ecee8: stur            w0, [x1, #0xb]
    // 0x9eceec: ldur            x16, [fp, #-0x18]
    // 0x9ecef0: stp             x16, x0, [SP, #8]
    // 0x9ecef4: str             x1, [SP]
    // 0x9ecef8: ldur            x0, [fp, #-0x18]
    // 0x9ecefc: ClosureCall
    //     0x9ecefc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    //     0x9ecf00: ldur            x2, [x0, #0x1f]
    //     0x9ecf04: blr             x2
    // 0x9ecf08: LeaveFrame
    //     0x9ecf08: mov             SP, fp
    //     0x9ecf0c: ldp             fp, lr, [SP], #0x10
    // 0x9ecf10: ret
    //     0x9ecf10: ret             
    // 0x9ecf14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ecf14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ecf18: b               #0x9ecea8
  }
  [closure] Y0? <anonymous closure>(dynamic, ButtonStyle?) {
    // ** addr: 0x9ecf1c, size: 0xe8
    // 0x9ecf1c: EnterFrame
    //     0x9ecf1c: stp             fp, lr, [SP, #-0x10]!
    //     0x9ecf20: mov             fp, SP
    // 0x9ecf24: AllocStack(0x18)
    //     0x9ecf24: sub             SP, SP, #0x18
    // 0x9ecf28: SetupParameters()
    //     0x9ecf28: ldr             x0, [fp, #0x18]
    //     0x9ecf2c: ldur            w1, [x0, #0x17]
    //     0x9ecf30: add             x1, x1, HEAP, lsl #32
    //     0x9ecf34: stur            x1, [fp, #-8]
    // 0x9ecf38: CheckStackOverflow
    //     0x9ecf38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ecf3c: cmp             SP, x16
    //     0x9ecf40: b.ls            #0x9ecff4
    // 0x9ecf44: LoadField: r0 = r1->field_f
    //     0x9ecf44: ldur            w0, [x1, #0xf]
    // 0x9ecf48: DecompressPointer r0
    //     0x9ecf48: add             x0, x0, HEAP, lsl #32
    // 0x9ecf4c: ldr             x16, [fp, #0x10]
    // 0x9ecf50: stp             x16, x0, [SP]
    // 0x9ecf54: ClosureCall
    //     0x9ecf54: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x9ecf58: ldur            x2, [x0, #0x1f]
    //     0x9ecf5c: blr             x2
    // 0x9ecf60: cmp             w0, NULL
    // 0x9ecf64: b.ne            #0x9ecf70
    // 0x9ecf68: r0 = Null
    //     0x9ecf68: mov             x0, NULL
    // 0x9ecf6c: b               #0x9ecfe8
    // 0x9ecf70: ldur            x1, [fp, #-8]
    // 0x9ecf74: LoadField: r2 = r1->field_b
    //     0x9ecf74: ldur            w2, [x1, #0xb]
    // 0x9ecf78: DecompressPointer r2
    //     0x9ecf78: add             x2, x2, HEAP, lsl #32
    // 0x9ecf7c: LoadField: r1 = r2->field_f
    //     0x9ecf7c: ldur            w1, [x2, #0xf]
    // 0x9ecf80: DecompressPointer r1
    //     0x9ecf80: add             x1, x1, HEAP, lsl #32
    // 0x9ecf84: LoadField: r2 = r1->field_b
    //     0x9ecf84: ldur            w2, [x1, #0xb]
    // 0x9ecf88: DecompressPointer r2
    //     0x9ecf88: add             x2, x2, HEAP, lsl #32
    // 0x9ecf8c: cmp             w2, NULL
    // 0x9ecf90: b.eq            #0x9ecffc
    // 0x9ecf94: LoadField: r3 = r2->field_2b
    //     0x9ecf94: ldur            w3, [x2, #0x2b]
    // 0x9ecf98: DecompressPointer r3
    //     0x9ecf98: add             x3, x3, HEAP, lsl #32
    // 0x9ecf9c: cmp             w3, NULL
    // 0x9ecfa0: b.ne            #0x9ecfbc
    // 0x9ecfa4: LoadField: r2 = r1->field_27
    //     0x9ecfa4: ldur            w2, [x1, #0x27]
    // 0x9ecfa8: DecompressPointer r2
    //     0x9ecfa8: add             x2, x2, HEAP, lsl #32
    // 0x9ecfac: cmp             w2, NULL
    // 0x9ecfb0: b.eq            #0x9ed000
    // 0x9ecfb4: mov             x1, x2
    // 0x9ecfb8: b               #0x9ecfc0
    // 0x9ecfbc: mov             x1, x3
    // 0x9ecfc0: LoadField: r2 = r1->field_27
    //     0x9ecfc0: ldur            w2, [x1, #0x27]
    // 0x9ecfc4: DecompressPointer r2
    //     0x9ecfc4: add             x2, x2, HEAP, lsl #32
    // 0x9ecfc8: r1 = LoadClassIdInstr(r0)
    //     0x9ecfc8: ldur            x1, [x0, #-1]
    //     0x9ecfcc: ubfx            x1, x1, #0xc, #0x14
    // 0x9ecfd0: mov             x16, x0
    // 0x9ecfd4: mov             x0, x1
    // 0x9ecfd8: mov             x1, x16
    // 0x9ecfdc: r0 = GDT[cid_x0 + -0xfdb]()
    //     0x9ecfdc: sub             lr, x0, #0xfdb
    //     0x9ecfe0: ldr             lr, [x21, lr, lsl #3]
    //     0x9ecfe4: blr             lr
    // 0x9ecfe8: LeaveFrame
    //     0x9ecfe8: mov             SP, fp
    //     0x9ecfec: ldp             fp, lr, [SP], #0x10
    // 0x9ecff0: ret
    //     0x9ecff0: ret             
    // 0x9ecff4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ecff4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ecff8: b               #0x9ecf44
    // 0x9ecffc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ecffc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ed000: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ed000: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Y0? effectiveValue<Y0>(dynamic, (dynamic, ButtonStyle?) => Y0?) {
    // ** addr: 0x9ed004, size: 0xd0
    // 0x9ed004: EnterFrame
    //     0x9ed004: stp             fp, lr, [SP, #-0x10]!
    //     0x9ed008: mov             fp, SP
    // 0x9ed00c: AllocStack(0x28)
    //     0x9ed00c: sub             SP, SP, #0x28
    // 0x9ed010: SetupParameters()
    //     0x9ed010: ldr             x0, [fp, #0x18]
    //     0x9ed014: ldur            w1, [x0, #0x17]
    //     0x9ed018: add             x1, x1, HEAP, lsl #32
    //     0x9ed01c: stur            x1, [fp, #-8]
    // 0x9ed020: CheckStackOverflow
    //     0x9ed020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ed024: cmp             SP, x16
    //     0x9ed028: b.ls            #0x9ed0cc
    // 0x9ed02c: LoadField: r0 = r1->field_13
    //     0x9ed02c: ldur            w0, [x1, #0x13]
    // 0x9ed030: DecompressPointer r0
    //     0x9ed030: add             x0, x0, HEAP, lsl #32
    // 0x9ed034: ldr             x16, [fp, #0x10]
    // 0x9ed038: stp             x0, x16, [SP]
    // 0x9ed03c: ldr             x0, [fp, #0x10]
    // 0x9ed040: ClosureCall
    //     0x9ed040: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x9ed044: ldur            x2, [x0, #0x1f]
    //     0x9ed048: blr             x2
    // 0x9ed04c: mov             x2, x0
    // 0x9ed050: ldur            x1, [fp, #-8]
    // 0x9ed054: stur            x2, [fp, #-0x10]
    // 0x9ed058: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9ed058: ldur            w0, [x1, #0x17]
    // 0x9ed05c: DecompressPointer r0
    //     0x9ed05c: add             x0, x0, HEAP, lsl #32
    // 0x9ed060: ldr             x16, [fp, #0x10]
    // 0x9ed064: stp             x0, x16, [SP]
    // 0x9ed068: ldr             x0, [fp, #0x10]
    // 0x9ed06c: ClosureCall
    //     0x9ed06c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x9ed070: ldur            x2, [x0, #0x1f]
    //     0x9ed074: blr             x2
    // 0x9ed078: mov             x1, x0
    // 0x9ed07c: ldur            x0, [fp, #-8]
    // 0x9ed080: stur            x1, [fp, #-0x18]
    // 0x9ed084: LoadField: r2 = r0->field_1b
    //     0x9ed084: ldur            w2, [x0, #0x1b]
    // 0x9ed088: DecompressPointer r2
    //     0x9ed088: add             x2, x2, HEAP, lsl #32
    // 0x9ed08c: ldr             x16, [fp, #0x10]
    // 0x9ed090: stp             x2, x16, [SP]
    // 0x9ed094: ldr             x0, [fp, #0x10]
    // 0x9ed098: ClosureCall
    //     0x9ed098: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x9ed09c: ldur            x2, [x0, #0x1f]
    //     0x9ed0a0: blr             x2
    // 0x9ed0a4: ldur            x1, [fp, #-0x10]
    // 0x9ed0a8: cmp             w1, NULL
    // 0x9ed0ac: b.ne            #0x9ed0b4
    // 0x9ed0b0: ldur            x1, [fp, #-0x18]
    // 0x9ed0b4: cmp             w1, NULL
    // 0x9ed0b8: b.eq            #0x9ed0c0
    // 0x9ed0bc: mov             x0, x1
    // 0x9ed0c0: LeaveFrame
    //     0x9ed0c0: mov             SP, fp
    //     0x9ed0c4: ldp             fp, lr, [SP], #0x10
    // 0x9ed0c8: ret
    //     0x9ed0c8: ret             
    // 0x9ed0cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ed0cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ed0d0: b               #0x9ed02c
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7ced8, size: 0xd4
    // 0xa7ced8: EnterFrame
    //     0xa7ced8: stp             fp, lr, [SP, #-0x10]!
    //     0xa7cedc: mov             fp, SP
    // 0xa7cee0: AllocStack(0x10)
    //     0xa7cee0: sub             SP, SP, #0x10
    // 0xa7cee4: SetupParameters(_ButtonStyleState this /* r1 => r0, fp-0x10 */)
    //     0xa7cee4: mov             x0, x1
    //     0xa7cee8: stur            x1, [fp, #-0x10]
    // 0xa7ceec: CheckStackOverflow
    //     0xa7ceec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7cef0: cmp             SP, x16
    //     0xa7cef4: b.ls            #0xa7cf9c
    // 0xa7cef8: LoadField: r1 = r0->field_b
    //     0xa7cef8: ldur            w1, [x0, #0xb]
    // 0xa7cefc: DecompressPointer r1
    //     0xa7cefc: add             x1, x1, HEAP, lsl #32
    // 0xa7cf00: cmp             w1, NULL
    // 0xa7cf04: b.eq            #0xa7cfa4
    // 0xa7cf08: LoadField: r2 = r1->field_2b
    //     0xa7cf08: ldur            w2, [x1, #0x2b]
    // 0xa7cf0c: DecompressPointer r2
    //     0xa7cf0c: add             x2, x2, HEAP, lsl #32
    // 0xa7cf10: cmp             w2, NULL
    // 0xa7cf14: b.ne            #0xa7cf30
    // 0xa7cf18: LoadField: r1 = r0->field_27
    //     0xa7cf18: ldur            w1, [x0, #0x27]
    // 0xa7cf1c: DecompressPointer r1
    //     0xa7cf1c: add             x1, x1, HEAP, lsl #32
    // 0xa7cf20: cmp             w1, NULL
    // 0xa7cf24: b.eq            #0xa7cfa8
    // 0xa7cf28: mov             x3, x1
    // 0xa7cf2c: b               #0xa7cf34
    // 0xa7cf30: mov             x3, x2
    // 0xa7cf34: mov             x2, x0
    // 0xa7cf38: stur            x3, [fp, #-8]
    // 0xa7cf3c: r1 = Function 'handleStatesControllerChange':.
    //     0xa7cf3c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44658] AnonymousClosure: (0x932cec), in [package:flutter/src/material/button_style_button.dart] _ButtonStyleState::handleStatesControllerChange (0x932d24)
    //     0xa7cf40: ldr             x1, [x1, #0x658]
    // 0xa7cf44: r0 = AllocateClosure()
    //     0xa7cf44: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7cf48: ldur            x1, [fp, #-8]
    // 0xa7cf4c: mov             x2, x0
    // 0xa7cf50: r0 = removeListener()
    //     0xa7cf50: bl              #0xa8a5dc  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xa7cf54: ldur            x0, [fp, #-0x10]
    // 0xa7cf58: LoadField: r1 = r0->field_27
    //     0xa7cf58: ldur            w1, [x0, #0x27]
    // 0xa7cf5c: DecompressPointer r1
    //     0xa7cf5c: add             x1, x1, HEAP, lsl #32
    // 0xa7cf60: cmp             w1, NULL
    // 0xa7cf64: b.eq            #0xa7cf70
    // 0xa7cf68: r0 = dispose()
    //     0xa7cf68: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0xa7cf6c: ldur            x0, [fp, #-0x10]
    // 0xa7cf70: LoadField: r1 = r0->field_1b
    //     0xa7cf70: ldur            w1, [x0, #0x1b]
    // 0xa7cf74: DecompressPointer r1
    //     0xa7cf74: add             x1, x1, HEAP, lsl #32
    // 0xa7cf78: cmp             w1, NULL
    // 0xa7cf7c: b.eq            #0xa7cf84
    // 0xa7cf80: r0 = dispose()
    //     0xa7cf80: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa7cf84: ldur            x1, [fp, #-0x10]
    // 0xa7cf88: r0 = dispose()
    //     0xa7cf88: bl              #0xa7cfac  ; [package:flutter/src/material/button_style_button.dart] __ButtonStyleState&State&TickerProviderStateMixin::dispose
    // 0xa7cf8c: r0 = Null
    //     0xa7cf8c: mov             x0, NULL
    // 0xa7cf90: LeaveFrame
    //     0xa7cf90: mov             SP, fp
    //     0xa7cf94: ldp             fp, lr, [SP], #0x10
    // 0xa7cf98: ret
    //     0xa7cf98: ret             
    // 0xa7cf9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7cf9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7cfa0: b               #0xa7cef8
    // 0xa7cfa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7cfa4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa7cfa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7cfa8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4555, size: 0x14, field offset: 0x10
//   const constructor, 
class _InputPadding extends SingleChildRenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x85a178, size: 0x74
    // 0x85a178: EnterFrame
    //     0x85a178: stp             fp, lr, [SP, #-0x10]!
    //     0x85a17c: mov             fp, SP
    // 0x85a180: AllocStack(0x10)
    //     0x85a180: sub             SP, SP, #0x10
    // 0x85a184: CheckStackOverflow
    //     0x85a184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85a188: cmp             SP, x16
    //     0x85a18c: b.ls            #0x85a1e4
    // 0x85a190: LoadField: r0 = r1->field_f
    //     0x85a190: ldur            w0, [x1, #0xf]
    // 0x85a194: DecompressPointer r0
    //     0x85a194: add             x0, x0, HEAP, lsl #32
    // 0x85a198: stur            x0, [fp, #-8]
    // 0x85a19c: r0 = _RenderInputPadding()
    //     0x85a19c: bl              #0x85a1ec  ; Allocate_RenderInputPaddingStub -> _RenderInputPadding (size=0x60)
    // 0x85a1a0: mov             x1, x0
    // 0x85a1a4: ldur            x0, [fp, #-8]
    // 0x85a1a8: stur            x1, [fp, #-0x10]
    // 0x85a1ac: StoreField: r1->field_5b = r0
    //     0x85a1ac: stur            w0, [x1, #0x5b]
    // 0x85a1b0: r0 = _LayoutCacheStorage()
    //     0x85a1b0: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x85a1b4: mov             x1, x0
    // 0x85a1b8: ldur            x0, [fp, #-0x10]
    // 0x85a1bc: StoreField: r0->field_4f = r1
    //     0x85a1bc: stur            w1, [x0, #0x4f]
    // 0x85a1c0: mov             x1, x0
    // 0x85a1c4: r0 = RenderObject()
    //     0x85a1c4: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x85a1c8: ldur            x1, [fp, #-0x10]
    // 0x85a1cc: r2 = Null
    //     0x85a1cc: mov             x2, NULL
    // 0x85a1d0: r0 = child=()
    //     0x85a1d0: bl              #0x895c88  ; [package:flutter/src/rendering/shifted_box.dart] _RenderShiftedBox&RenderBox&RenderObjectWithChildMixin::child=
    // 0x85a1d4: ldur            x0, [fp, #-0x10]
    // 0x85a1d8: LeaveFrame
    //     0x85a1d8: mov             SP, fp
    //     0x85a1dc: ldp             fp, lr, [SP], #0x10
    // 0x85a1e0: ret
    //     0x85a1e0: ret             
    // 0x85a1e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85a1e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85a1e8: b               #0x85a190
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xc6d308, size: 0x88
    // 0xc6d308: EnterFrame
    //     0xc6d308: stp             fp, lr, [SP, #-0x10]!
    //     0xc6d30c: mov             fp, SP
    // 0xc6d310: AllocStack(0x10)
    //     0xc6d310: sub             SP, SP, #0x10
    // 0xc6d314: SetupParameters(_InputPadding this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xc6d314: mov             x4, x1
    //     0xc6d318: stur            x1, [fp, #-8]
    //     0xc6d31c: stur            x3, [fp, #-0x10]
    // 0xc6d320: CheckStackOverflow
    //     0xc6d320: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6d324: cmp             SP, x16
    //     0xc6d328: b.ls            #0xc6d388
    // 0xc6d32c: mov             x0, x3
    // 0xc6d330: r2 = Null
    //     0xc6d330: mov             x2, NULL
    // 0xc6d334: r1 = Null
    //     0xc6d334: mov             x1, NULL
    // 0xc6d338: r4 = 60
    //     0xc6d338: movz            x4, #0x3c
    // 0xc6d33c: branchIfSmi(r0, 0xc6d348)
    //     0xc6d33c: tbz             w0, #0, #0xc6d348
    // 0xc6d340: r4 = LoadClassIdInstr(r0)
    //     0xc6d340: ldur            x4, [x0, #-1]
    //     0xc6d344: ubfx            x4, x4, #0xc, #0x14
    // 0xc6d348: cmp             x4, #0xc08
    // 0xc6d34c: b.eq            #0xc6d364
    // 0xc6d350: r8 = _RenderInputPadding
    //     0xc6d350: add             x8, PP, #0x4e, lsl #12  ; [pp+0x4e208] Type: _RenderInputPadding
    //     0xc6d354: ldr             x8, [x8, #0x208]
    // 0xc6d358: r3 = Null
    //     0xc6d358: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e210] Null
    //     0xc6d35c: ldr             x3, [x3, #0x210]
    // 0xc6d360: r0 = DefaultTypeTest()
    //     0xc6d360: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc6d364: ldur            x0, [fp, #-8]
    // 0xc6d368: LoadField: r2 = r0->field_f
    //     0xc6d368: ldur            w2, [x0, #0xf]
    // 0xc6d36c: DecompressPointer r2
    //     0xc6d36c: add             x2, x2, HEAP, lsl #32
    // 0xc6d370: ldur            x1, [fp, #-0x10]
    // 0xc6d374: r0 = minSize=()
    //     0xc6d374: bl              #0xc6d390  ; [package:flutter/src/material/button_style_button.dart] _RenderInputPadding::minSize=
    // 0xc6d378: r0 = Null
    //     0xc6d378: mov             x0, NULL
    // 0xc6d37c: LeaveFrame
    //     0xc6d37c: mov             SP, fp
    //     0xc6d380: ldp             fp, lr, [SP], #0x10
    // 0xc6d384: ret
    //     0xc6d384: ret             
    // 0xc6d388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6d388: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6d38c: b               #0xc6d32c
  }
}

// class id: 4866, size: 0x3c, field offset: 0xc
//   const constructor, 
abstract class ButtonStyleButton extends StatefulWidget {

  static _ allOrNull(/* No info */) {
    // ** addr: 0x9e8444, size: 0x50
    // 0x9e8444: EnterFrame
    //     0x9e8444: stp             fp, lr, [SP, #-0x10]!
    //     0x9e8448: mov             fp, SP
    // 0x9e844c: LoadField: r0 = r4->field_f
    //     0x9e844c: ldur            w0, [x4, #0xf]
    // 0x9e8450: cbnz            w0, #0x9e845c
    // 0x9e8454: r1 = Null
    //     0x9e8454: mov             x1, NULL
    // 0x9e8458: b               #0x9e8468
    // 0x9e845c: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x9e845c: ldur            w0, [x4, #0x17]
    // 0x9e8460: add             x1, fp, w0, sxtw #2
    // 0x9e8464: ldr             x1, [x1, #0x10]
    // 0x9e8468: ldr             x0, [fp, #0x10]
    // 0x9e846c: cmp             w0, NULL
    // 0x9e8470: b.ne            #0x9e847c
    // 0x9e8474: r0 = Null
    //     0x9e8474: mov             x0, NULL
    // 0x9e8478: b               #0x9e8488
    // 0x9e847c: r0 = WidgetStatePropertyAll()
    //     0x9e847c: bl              #0x9e8494  ; AllocateWidgetStatePropertyAllStub -> WidgetStatePropertyAll<X0> (size=0x10)
    // 0x9e8480: ldr             x1, [fp, #0x10]
    // 0x9e8484: StoreField: r0->field_b = r1
    //     0x9e8484: stur            w1, [x0, #0xb]
    // 0x9e8488: LeaveFrame
    //     0x9e8488: mov             SP, fp
    //     0x9e848c: ldp             fp, lr, [SP], #0x10
    // 0x9e8490: ret
    //     0x9e8490: ret             
  }
  static _ defaultColor(/* No info */) {
    // ** addr: 0x9e84a0, size: 0xb8
    // 0x9e84a0: EnterFrame
    //     0x9e84a0: stp             fp, lr, [SP, #-0x10]!
    //     0x9e84a4: mov             fp, SP
    // 0x9e84a8: AllocStack(0x20)
    //     0x9e84a8: sub             SP, SP, #0x20
    // 0x9e84ac: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9e84ac: mov             x3, x1
    //     0x9e84b0: mov             x0, x2
    //     0x9e84b4: stur            x1, [fp, #-8]
    //     0x9e84b8: stur            x2, [fp, #-0x10]
    // 0x9e84bc: CheckStackOverflow
    //     0x9e84bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e84c0: cmp             SP, x16
    //     0x9e84c4: b.ls            #0x9e8550
    // 0x9e84c8: cmp             w3, NULL
    // 0x9e84cc: b.ne            #0x9e84e8
    // 0x9e84d0: cmp             w0, NULL
    // 0x9e84d4: b.ne            #0x9e84e8
    // 0x9e84d8: r0 = Null
    //     0x9e84d8: mov             x0, NULL
    // 0x9e84dc: LeaveFrame
    //     0x9e84dc: mov             SP, fp
    //     0x9e84e0: ldp             fp, lr, [SP], #0x10
    // 0x9e84e4: ret
    //     0x9e84e4: ret             
    // 0x9e84e8: r1 = Null
    //     0x9e84e8: mov             x1, NULL
    // 0x9e84ec: r2 = 8
    //     0x9e84ec: movz            x2, #0x8
    // 0x9e84f0: r0 = AllocateArray()
    //     0x9e84f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x9e84f4: r16 = Instance_WidgetState
    //     0x9e84f4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d190] Obj!WidgetState@e33841
    //     0x9e84f8: ldr             x16, [x16, #0x190]
    // 0x9e84fc: StoreField: r0->field_f = r16
    //     0x9e84fc: stur            w16, [x0, #0xf]
    // 0x9e8500: ldur            x1, [fp, #-0x10]
    // 0x9e8504: StoreField: r0->field_13 = r1
    //     0x9e8504: stur            w1, [x0, #0x13]
    // 0x9e8508: r16 = Instance__AnyWidgetStates
    //     0x9e8508: add             x16, PP, #0x23, lsl #12  ; [pp+0x23de8] Obj!_AnyWidgetStates@e0fcd1
    //     0x9e850c: ldr             x16, [x16, #0xde8]
    // 0x9e8510: ArrayStore: r0[0] = r16  ; List_4
    //     0x9e8510: stur            w16, [x0, #0x17]
    // 0x9e8514: ldur            x1, [fp, #-8]
    // 0x9e8518: StoreField: r0->field_1b = r1
    //     0x9e8518: stur            w1, [x0, #0x1b]
    // 0x9e851c: r16 = <WidgetStatesConstraint, Color?>
    //     0x9e851c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e00] TypeArguments: <WidgetStatesConstraint, Color?>
    //     0x9e8520: ldr             x16, [x16, #0xe00]
    // 0x9e8524: stp             x0, x16, [SP]
    // 0x9e8528: r0 = Map._fromLiteral()
    //     0x9e8528: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x9e852c: r1 = <Color?>
    //     0x9e852c: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x9e8530: ldr             x1, [x1, #0x98]
    // 0x9e8534: stur            x0, [fp, #-8]
    // 0x9e8538: r0 = _WidgetStateMapper()
    //     0x9e8538: bl              #0x9e8558  ; Allocate_WidgetStateMapperStub -> _WidgetStateMapper<X0> (size=0x10)
    // 0x9e853c: ldur            x1, [fp, #-8]
    // 0x9e8540: StoreField: r0->field_b = r1
    //     0x9e8540: stur            w1, [x0, #0xb]
    // 0x9e8544: LeaveFrame
    //     0x9e8544: mov             SP, fp
    //     0x9e8548: ldp             fp, lr, [SP], #0x10
    // 0x9e854c: ret
    //     0x9e854c: ret             
    // 0x9e8550: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e8550: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e8554: b               #0x9e84c8
  }
  _ createState(/* No info */) {
    // ** addr: 0xa8fb0c, size: 0x24
    // 0xa8fb0c: EnterFrame
    //     0xa8fb0c: stp             fp, lr, [SP, #-0x10]!
    //     0xa8fb10: mov             fp, SP
    // 0xa8fb14: mov             x0, x1
    // 0xa8fb18: r1 = <ButtonStyleButton>
    //     0xa8fb18: add             x1, PP, #0x39, lsl #12  ; [pp+0x399d0] TypeArguments: <ButtonStyleButton>
    //     0xa8fb1c: ldr             x1, [x1, #0x9d0]
    // 0xa8fb20: r0 = _ButtonStyleState()
    //     0xa8fb20: bl              #0xa8fb30  ; Allocate_ButtonStyleStateStub -> _ButtonStyleState (size=0x2c)
    // 0xa8fb24: LeaveFrame
    //     0xa8fb24: mov             SP, fp
    //     0xa8fb28: ldp             fp, lr, [SP], #0x10
    // 0xa8fb2c: ret
    //     0xa8fb2c: ret             
  }
  static _ scaledPadding(/* No info */) {
    // ** addr: 0xc1f36c, size: 0x108
    // 0xc1f36c: EnterFrame
    //     0xc1f36c: stp             fp, lr, [SP, #-0x10]!
    //     0xc1f370: mov             fp, SP
    // 0xc1f374: d1 = 1.000000
    //     0xc1f374: fmov            d1, #1.00000000
    // 0xc1f378: mov             x0, x2
    // 0xc1f37c: mov             x2, x3
    // 0xc1f380: CheckStackOverflow
    //     0xc1f380: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1f384: cmp             SP, x16
    //     0xc1f388: b.ls            #0xc1f434
    // 0xc1f38c: fcmp            d1, d0
    // 0xc1f390: b.lt            #0xc1f39c
    // 0xc1f394: mov             x0, x1
    // 0xc1f398: b               #0xc1f428
    // 0xc1f39c: d2 = 2.000000
    //     0xc1f39c: fmov            d2, #2.00000000
    // 0xc1f3a0: fcmp            d2, d0
    // 0xc1f3a4: b.le            #0xc1f3e0
    // 0xc1f3a8: fsub            d2, d0, d1
    // 0xc1f3ac: r3 = inline_Allocate_Double()
    //     0xc1f3ac: ldp             x3, x2, [THR, #0x50]  ; THR::top
    //     0xc1f3b0: add             x3, x3, #0x10
    //     0xc1f3b4: cmp             x2, x3
    //     0xc1f3b8: b.ls            #0xc1f43c
    //     0xc1f3bc: str             x3, [THR, #0x50]  ; THR::top
    //     0xc1f3c0: sub             x3, x3, #0xf
    //     0xc1f3c4: movz            x2, #0xe15c
    //     0xc1f3c8: movk            x2, #0x3, lsl #16
    //     0xc1f3cc: stur            x2, [x3, #-1]
    // 0xc1f3d0: StoreField: r3->field_7 = d2
    //     0xc1f3d0: stur            d2, [x3, #7]
    // 0xc1f3d4: mov             x2, x0
    // 0xc1f3d8: r0 = lerp()
    //     0xc1f3d8: bl              #0x876de0  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::lerp
    // 0xc1f3dc: b               #0xc1f428
    // 0xc1f3e0: d1 = 3.000000
    //     0xc1f3e0: fmov            d1, #3.00000000
    // 0xc1f3e4: fcmp            d1, d0
    // 0xc1f3e8: b.le            #0xc1f424
    // 0xc1f3ec: fsub            d1, d0, d2
    // 0xc1f3f0: r3 = inline_Allocate_Double()
    //     0xc1f3f0: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xc1f3f4: add             x3, x3, #0x10
    //     0xc1f3f8: cmp             x1, x3
    //     0xc1f3fc: b.ls            #0xc1f458
    //     0xc1f400: str             x3, [THR, #0x50]  ; THR::top
    //     0xc1f404: sub             x3, x3, #0xf
    //     0xc1f408: movz            x1, #0xe15c
    //     0xc1f40c: movk            x1, #0x3, lsl #16
    //     0xc1f410: stur            x1, [x3, #-1]
    // 0xc1f414: StoreField: r3->field_7 = d1
    //     0xc1f414: stur            d1, [x3, #7]
    // 0xc1f418: mov             x1, x0
    // 0xc1f41c: r0 = lerp()
    //     0xc1f41c: bl              #0x876de0  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::lerp
    // 0xc1f420: b               #0xc1f428
    // 0xc1f424: mov             x0, x2
    // 0xc1f428: LeaveFrame
    //     0xc1f428: mov             SP, fp
    //     0xc1f42c: ldp             fp, lr, [SP], #0x10
    // 0xc1f430: ret
    //     0xc1f430: ret             
    // 0xc1f434: r0 = StackOverflowSharedWithFPURegs()
    //     0xc1f434: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc1f438: b               #0xc1f38c
    // 0xc1f43c: SaveReg d2
    //     0xc1f43c: str             q2, [SP, #-0x10]!
    // 0xc1f440: stp             x0, x1, [SP, #-0x10]!
    // 0xc1f444: r0 = AllocateDouble()
    //     0xc1f444: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc1f448: mov             x3, x0
    // 0xc1f44c: ldp             x0, x1, [SP], #0x10
    // 0xc1f450: RestoreReg d2
    //     0xc1f450: ldr             q2, [SP], #0x10
    // 0xc1f454: b               #0xc1f3d0
    // 0xc1f458: SaveReg d1
    //     0xc1f458: str             q1, [SP, #-0x10]!
    // 0xc1f45c: stp             x0, x2, [SP, #-0x10]!
    // 0xc1f460: r0 = AllocateDouble()
    //     0xc1f460: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc1f464: mov             x3, x0
    // 0xc1f468: ldp             x0, x2, [SP], #0x10
    // 0xc1f46c: RestoreReg d1
    //     0xc1f46c: ldr             q1, [SP], #0x10
    // 0xc1f470: b               #0xc1f414
  }
}

// class id: 7074, size: 0x14, field offset: 0x14
enum IconAlignment extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc48a68, size: 0x64
    // 0xc48a68: EnterFrame
    //     0xc48a68: stp             fp, lr, [SP, #-0x10]!
    //     0xc48a6c: mov             fp, SP
    // 0xc48a70: AllocStack(0x10)
    //     0xc48a70: sub             SP, SP, #0x10
    // 0xc48a74: SetupParameters(IconAlignment this /* r1 => r0, fp-0x8 */)
    //     0xc48a74: mov             x0, x1
    //     0xc48a78: stur            x1, [fp, #-8]
    // 0xc48a7c: CheckStackOverflow
    //     0xc48a7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc48a80: cmp             SP, x16
    //     0xc48a84: b.ls            #0xc48ac4
    // 0xc48a88: r1 = Null
    //     0xc48a88: mov             x1, NULL
    // 0xc48a8c: r2 = 4
    //     0xc48a8c: movz            x2, #0x4
    // 0xc48a90: r0 = AllocateArray()
    //     0xc48a90: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc48a94: r16 = "IconAlignment."
    //     0xc48a94: add             x16, PP, #0x39, lsl #12  ; [pp+0x399d8] "IconAlignment."
    //     0xc48a98: ldr             x16, [x16, #0x9d8]
    // 0xc48a9c: StoreField: r0->field_f = r16
    //     0xc48a9c: stur            w16, [x0, #0xf]
    // 0xc48aa0: ldur            x1, [fp, #-8]
    // 0xc48aa4: LoadField: r2 = r1->field_f
    //     0xc48aa4: ldur            w2, [x1, #0xf]
    // 0xc48aa8: DecompressPointer r2
    //     0xc48aa8: add             x2, x2, HEAP, lsl #32
    // 0xc48aac: StoreField: r0->field_13 = r2
    //     0xc48aac: stur            w2, [x0, #0x13]
    // 0xc48ab0: str             x0, [SP]
    // 0xc48ab4: r0 = _interpolate()
    //     0xc48ab4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc48ab8: LeaveFrame
    //     0xc48ab8: mov             SP, fp
    //     0xc48abc: ldp             fp, lr, [SP], #0x10
    // 0xc48ac0: ret
    //     0xc48ac0: ret             
    // 0xc48ac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc48ac4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc48ac8: b               #0xc48a88
  }
}
