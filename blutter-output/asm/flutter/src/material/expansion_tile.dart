// lib: , url: package:flutter/src/material/expansion_tile.dart

// class id: 1048894, size: 0x8
class :: {
}

// class id: 3404, size: 0x8, field offset: 0x8
class ExpansionTileController extends Object {
}

// class id: 3952, size: 0x48, field offset: 0x3c
class _ExpansionTileDefaultsM3 extends ExpansionTileThemeData {

  late final ColorScheme _colors; // offset: 0x44
  late final ThemeData _theme; // offset: 0x40

  ColorScheme _colors(_ExpansionTileDefaultsM3) {
    // ** addr: 0x9a3b38, size: 0x58
    // 0x9a3b38: EnterFrame
    //     0x9a3b38: stp             fp, lr, [SP, #-0x10]!
    //     0x9a3b3c: mov             fp, SP
    // 0x9a3b40: CheckStackOverflow
    //     0x9a3b40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a3b44: cmp             SP, x16
    //     0x9a3b48: b.ls            #0x9a3b88
    // 0x9a3b4c: ldr             x1, [fp, #0x10]
    // 0x9a3b50: LoadField: r0 = r1->field_3f
    //     0x9a3b50: ldur            w0, [x1, #0x3f]
    // 0x9a3b54: DecompressPointer r0
    //     0x9a3b54: add             x0, x0, HEAP, lsl #32
    // 0x9a3b58: r16 = Sentinel
    //     0x9a3b58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a3b5c: cmp             w0, w16
    // 0x9a3b60: b.ne            #0x9a3b70
    // 0x9a3b64: r2 = _theme
    //     0x9a3b64: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d3a8] Field <_ExpansionTileDefaultsM3@543392950._theme@543392950>: late final (offset: 0x40)
    //     0x9a3b68: ldr             x2, [x2, #0x3a8]
    // 0x9a3b6c: r0 = InitLateFinalInstanceField()
    //     0x9a3b6c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9a3b70: LoadField: r1 = r0->field_3f
    //     0x9a3b70: ldur            w1, [x0, #0x3f]
    // 0x9a3b74: DecompressPointer r1
    //     0x9a3b74: add             x1, x1, HEAP, lsl #32
    // 0x9a3b78: mov             x0, x1
    // 0x9a3b7c: LeaveFrame
    //     0x9a3b7c: mov             SP, fp
    //     0x9a3b80: ldp             fp, lr, [SP], #0x10
    // 0x9a3b84: ret
    //     0x9a3b84: ret             
    // 0x9a3b88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a3b88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a3b8c: b               #0x9a3b4c
  }
}

// class id: 3953, size: 0x48, field offset: 0x3c
class _ExpansionTileDefaultsM2 extends ExpansionTileThemeData {

  late final ColorScheme _colorScheme; // offset: 0x44
  late final ThemeData _theme; // offset: 0x40

  ColorScheme _colorScheme(_ExpansionTileDefaultsM2) {
    // ** addr: 0x9a3aa8, size: 0x58
    // 0x9a3aa8: EnterFrame
    //     0x9a3aa8: stp             fp, lr, [SP, #-0x10]!
    //     0x9a3aac: mov             fp, SP
    // 0x9a3ab0: CheckStackOverflow
    //     0x9a3ab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a3ab4: cmp             SP, x16
    //     0x9a3ab8: b.ls            #0x9a3af8
    // 0x9a3abc: ldr             x1, [fp, #0x10]
    // 0x9a3ac0: LoadField: r0 = r1->field_3f
    //     0x9a3ac0: ldur            w0, [x1, #0x3f]
    // 0x9a3ac4: DecompressPointer r0
    //     0x9a3ac4: add             x0, x0, HEAP, lsl #32
    // 0x9a3ac8: r16 = Sentinel
    //     0x9a3ac8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a3acc: cmp             w0, w16
    // 0x9a3ad0: b.ne            #0x9a3ae0
    // 0x9a3ad4: r2 = _theme
    //     0x9a3ad4: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d3a0] Field <_ExpansionTileDefaultsM2@543392950._theme@543392950>: late final (offset: 0x40)
    //     0x9a3ad8: ldr             x2, [x2, #0x3a0]
    // 0x9a3adc: r0 = InitLateFinalInstanceField()
    //     0x9a3adc: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9a3ae0: LoadField: r1 = r0->field_3f
    //     0x9a3ae0: ldur            w1, [x0, #0x3f]
    // 0x9a3ae4: DecompressPointer r1
    //     0x9a3ae4: add             x1, x1, HEAP, lsl #32
    // 0x9a3ae8: mov             x0, x1
    // 0x9a3aec: LeaveFrame
    //     0x9a3aec: mov             SP, fp
    //     0x9a3af0: ldp             fp, lr, [SP], #0x10
    // 0x9a3af4: ret
    //     0x9a3af4: ret             
    // 0x9a3af8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a3af8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a3afc: b               #0x9a3abc
  }
  ThemeData _theme(_ExpansionTileDefaultsM2) {
    // ** addr: 0x9a3b00, size: 0x38
    // 0x9a3b00: EnterFrame
    //     0x9a3b00: stp             fp, lr, [SP, #-0x10]!
    //     0x9a3b04: mov             fp, SP
    // 0x9a3b08: CheckStackOverflow
    //     0x9a3b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a3b0c: cmp             SP, x16
    //     0x9a3b10: b.ls            #0x9a3b30
    // 0x9a3b14: ldr             x0, [fp, #0x10]
    // 0x9a3b18: LoadField: r1 = r0->field_3b
    //     0x9a3b18: ldur            w1, [x0, #0x3b]
    // 0x9a3b1c: DecompressPointer r1
    //     0x9a3b1c: add             x1, x1, HEAP, lsl #32
    // 0x9a3b20: r0 = of()
    //     0x9a3b20: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9a3b24: LeaveFrame
    //     0x9a3b24: mov             SP, fp
    //     0x9a3b28: ldp             fp, lr, [SP], #0x10
    // 0x9a3b2c: ret
    //     0x9a3b2c: ret             
    // 0x9a3b30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a3b30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a3b34: b               #0x9a3b14
  }
}

// class id: 4313, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __ExpansionTileState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6f3860, size: 0x98
    // 0x6f3860: EnterFrame
    //     0x6f3860: stp             fp, lr, [SP, #-0x10]!
    //     0x6f3864: mov             fp, SP
    // 0x6f3868: AllocStack(0x10)
    //     0x6f3868: sub             SP, SP, #0x10
    // 0x6f386c: SetupParameters(__ExpansionTileState&State&SingleTickerProviderStateMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6f386c: stur            x1, [fp, #-8]
    //     0x6f3870: stur            x2, [fp, #-0x10]
    // 0x6f3874: CheckStackOverflow
    //     0x6f3874: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f3878: cmp             SP, x16
    //     0x6f387c: b.ls            #0x6f38ec
    // 0x6f3880: r0 = Ticker()
    //     0x6f3880: bl              #0x6efe64  ; AllocateTickerStub -> Ticker (size=0x1c)
    // 0x6f3884: mov             x1, x0
    // 0x6f3888: r0 = false
    //     0x6f3888: add             x0, NULL, #0x30  ; false
    // 0x6f388c: StoreField: r1->field_b = r0
    //     0x6f388c: stur            w0, [x1, #0xb]
    // 0x6f3890: ldur            x0, [fp, #-0x10]
    // 0x6f3894: StoreField: r1->field_13 = r0
    //     0x6f3894: stur            w0, [x1, #0x13]
    // 0x6f3898: mov             x0, x1
    // 0x6f389c: ldur            x2, [fp, #-8]
    // 0x6f38a0: StoreField: r2->field_13 = r0
    //     0x6f38a0: stur            w0, [x2, #0x13]
    //     0x6f38a4: ldurb           w16, [x2, #-1]
    //     0x6f38a8: ldurb           w17, [x0, #-1]
    //     0x6f38ac: and             x16, x17, x16, lsr #2
    //     0x6f38b0: tst             x16, HEAP, lsr #32
    //     0x6f38b4: b.eq            #0x6f38bc
    //     0x6f38b8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6f38bc: mov             x1, x2
    // 0x6f38c0: r0 = _updateTickerModeNotifier()
    //     0x6f38c0: bl              #0x6f391c  ; [package:flutter/src/material/expansion_tile.dart] __ExpansionTileState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6f38c4: ldur            x1, [fp, #-8]
    // 0x6f38c8: r0 = _updateTicker()
    //     0x6f38c8: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6f38cc: ldur            x1, [fp, #-8]
    // 0x6f38d0: LoadField: r0 = r1->field_13
    //     0x6f38d0: ldur            w0, [x1, #0x13]
    // 0x6f38d4: DecompressPointer r0
    //     0x6f38d4: add             x0, x0, HEAP, lsl #32
    // 0x6f38d8: cmp             w0, NULL
    // 0x6f38dc: b.eq            #0x6f38f4
    // 0x6f38e0: LeaveFrame
    //     0x6f38e0: mov             SP, fp
    //     0x6f38e4: ldp             fp, lr, [SP], #0x10
    // 0x6f38e8: ret
    //     0x6f38e8: ret             
    // 0x6f38ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f38ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f38f0: b               #0x6f3880
    // 0x6f38f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f38f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6f391c, size: 0x124
    // 0x6f391c: EnterFrame
    //     0x6f391c: stp             fp, lr, [SP, #-0x10]!
    //     0x6f3920: mov             fp, SP
    // 0x6f3924: AllocStack(0x18)
    //     0x6f3924: sub             SP, SP, #0x18
    // 0x6f3928: SetupParameters(__ExpansionTileState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6f3928: mov             x2, x1
    //     0x6f392c: stur            x1, [fp, #-8]
    // 0x6f3930: CheckStackOverflow
    //     0x6f3930: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f3934: cmp             SP, x16
    //     0x6f3938: b.ls            #0x6f3a34
    // 0x6f393c: LoadField: r1 = r2->field_f
    //     0x6f393c: ldur            w1, [x2, #0xf]
    // 0x6f3940: DecompressPointer r1
    //     0x6f3940: add             x1, x1, HEAP, lsl #32
    // 0x6f3944: cmp             w1, NULL
    // 0x6f3948: b.eq            #0x6f3a3c
    // 0x6f394c: r0 = getNotifier()
    //     0x6f394c: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6f3950: mov             x3, x0
    // 0x6f3954: ldur            x0, [fp, #-8]
    // 0x6f3958: stur            x3, [fp, #-0x18]
    // 0x6f395c: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6f395c: ldur            w4, [x0, #0x17]
    // 0x6f3960: DecompressPointer r4
    //     0x6f3960: add             x4, x4, HEAP, lsl #32
    // 0x6f3964: stur            x4, [fp, #-0x10]
    // 0x6f3968: cmp             w3, w4
    // 0x6f396c: b.ne            #0x6f3980
    // 0x6f3970: r0 = Null
    //     0x6f3970: mov             x0, NULL
    // 0x6f3974: LeaveFrame
    //     0x6f3974: mov             SP, fp
    //     0x6f3978: ldp             fp, lr, [SP], #0x10
    // 0x6f397c: ret
    //     0x6f397c: ret             
    // 0x6f3980: cmp             w4, NULL
    // 0x6f3984: b.eq            #0x6f39c8
    // 0x6f3988: mov             x2, x0
    // 0x6f398c: r1 = Function '_updateTicker@364311458':.
    //     0x6f398c: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d860] AnonymousClosure: (0x6f3a40), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6f3990: ldr             x1, [x1, #0x860]
    // 0x6f3994: r0 = AllocateClosure()
    //     0x6f3994: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f3998: ldur            x1, [fp, #-0x10]
    // 0x6f399c: r2 = LoadClassIdInstr(r1)
    //     0x6f399c: ldur            x2, [x1, #-1]
    //     0x6f39a0: ubfx            x2, x2, #0xc, #0x14
    // 0x6f39a4: mov             x16, x0
    // 0x6f39a8: mov             x0, x2
    // 0x6f39ac: mov             x2, x16
    // 0x6f39b0: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6f39b0: movz            x17, #0xbf5c
    //     0x6f39b4: add             lr, x0, x17
    //     0x6f39b8: ldr             lr, [x21, lr, lsl #3]
    //     0x6f39bc: blr             lr
    // 0x6f39c0: ldur            x0, [fp, #-8]
    // 0x6f39c4: ldur            x3, [fp, #-0x18]
    // 0x6f39c8: mov             x2, x0
    // 0x6f39cc: r1 = Function '_updateTicker@364311458':.
    //     0x6f39cc: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d860] AnonymousClosure: (0x6f3a40), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6f39d0: ldr             x1, [x1, #0x860]
    // 0x6f39d4: r0 = AllocateClosure()
    //     0x6f39d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f39d8: ldur            x3, [fp, #-0x18]
    // 0x6f39dc: r1 = LoadClassIdInstr(r3)
    //     0x6f39dc: ldur            x1, [x3, #-1]
    //     0x6f39e0: ubfx            x1, x1, #0xc, #0x14
    // 0x6f39e4: mov             x2, x0
    // 0x6f39e8: mov             x0, x1
    // 0x6f39ec: mov             x1, x3
    // 0x6f39f0: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6f39f0: movz            x17, #0xc407
    //     0x6f39f4: add             lr, x0, x17
    //     0x6f39f8: ldr             lr, [x21, lr, lsl #3]
    //     0x6f39fc: blr             lr
    // 0x6f3a00: ldur            x0, [fp, #-0x18]
    // 0x6f3a04: ldur            x1, [fp, #-8]
    // 0x6f3a08: ArrayStore: r1[0] = r0  ; List_4
    //     0x6f3a08: stur            w0, [x1, #0x17]
    //     0x6f3a0c: ldurb           w16, [x1, #-1]
    //     0x6f3a10: ldurb           w17, [x0, #-1]
    //     0x6f3a14: and             x16, x17, x16, lsr #2
    //     0x6f3a18: tst             x16, HEAP, lsr #32
    //     0x6f3a1c: b.eq            #0x6f3a24
    //     0x6f3a20: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6f3a24: r0 = Null
    //     0x6f3a24: mov             x0, NULL
    // 0x6f3a28: LeaveFrame
    //     0x6f3a28: mov             SP, fp
    //     0x6f3a2c: ldp             fp, lr, [SP], #0x10
    // 0x6f3a30: ret
    //     0x6f3a30: ret             
    // 0x6f3a34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f3a34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f3a38: b               #0x6f393c
    // 0x6f3a3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f3a3c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTicker(dynamic) {
    // ** addr: 0x6f3a40, size: 0x38
    // 0x6f3a40: EnterFrame
    //     0x6f3a40: stp             fp, lr, [SP, #-0x10]!
    //     0x6f3a44: mov             fp, SP
    // 0x6f3a48: ldr             x0, [fp, #0x10]
    // 0x6f3a4c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6f3a4c: ldur            w1, [x0, #0x17]
    // 0x6f3a50: DecompressPointer r1
    //     0x6f3a50: add             x1, x1, HEAP, lsl #32
    // 0x6f3a54: CheckStackOverflow
    //     0x6f3a54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f3a58: cmp             SP, x16
    //     0x6f3a5c: b.ls            #0x6f3a70
    // 0x6f3a60: r0 = _updateTicker()
    //     0x6f3a60: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6f3a64: LeaveFrame
    //     0x6f3a64: mov             SP, fp
    //     0x6f3a68: ldp             fp, lr, [SP], #0x10
    // 0x6f3a6c: ret
    //     0x6f3a6c: ret             
    // 0x6f3a70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f3a70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f3a74: b               #0x6f3a60
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7d36c, size: 0x94
    // 0xa7d36c: EnterFrame
    //     0xa7d36c: stp             fp, lr, [SP, #-0x10]!
    //     0xa7d370: mov             fp, SP
    // 0xa7d374: AllocStack(0x10)
    //     0xa7d374: sub             SP, SP, #0x10
    // 0xa7d378: SetupParameters(__ExpansionTileState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa7d378: mov             x0, x1
    //     0xa7d37c: stur            x1, [fp, #-0x10]
    // 0xa7d380: CheckStackOverflow
    //     0xa7d380: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7d384: cmp             SP, x16
    //     0xa7d388: b.ls            #0xa7d3f8
    // 0xa7d38c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa7d38c: ldur            w3, [x0, #0x17]
    // 0xa7d390: DecompressPointer r3
    //     0xa7d390: add             x3, x3, HEAP, lsl #32
    // 0xa7d394: stur            x3, [fp, #-8]
    // 0xa7d398: cmp             w3, NULL
    // 0xa7d39c: b.ne            #0xa7d3a8
    // 0xa7d3a0: mov             x1, x0
    // 0xa7d3a4: b               #0xa7d3e4
    // 0xa7d3a8: mov             x2, x0
    // 0xa7d3ac: r1 = Function '_updateTicker@364311458':.
    //     0xa7d3ac: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d860] AnonymousClosure: (0x6f3a40), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0xa7d3b0: ldr             x1, [x1, #0x860]
    // 0xa7d3b4: r0 = AllocateClosure()
    //     0xa7d3b4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7d3b8: ldur            x1, [fp, #-8]
    // 0xa7d3bc: r2 = LoadClassIdInstr(r1)
    //     0xa7d3bc: ldur            x2, [x1, #-1]
    //     0xa7d3c0: ubfx            x2, x2, #0xc, #0x14
    // 0xa7d3c4: mov             x16, x0
    // 0xa7d3c8: mov             x0, x2
    // 0xa7d3cc: mov             x2, x16
    // 0xa7d3d0: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa7d3d0: movz            x17, #0xbf5c
    //     0xa7d3d4: add             lr, x0, x17
    //     0xa7d3d8: ldr             lr, [x21, lr, lsl #3]
    //     0xa7d3dc: blr             lr
    // 0xa7d3e0: ldur            x1, [fp, #-0x10]
    // 0xa7d3e4: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa7d3e4: stur            NULL, [x1, #0x17]
    // 0xa7d3e8: r0 = Null
    //     0xa7d3e8: mov             x0, NULL
    // 0xa7d3ec: LeaveFrame
    //     0xa7d3ec: mov             SP, fp
    //     0xa7d3f0: ldp             fp, lr, [SP], #0x10
    // 0xa7d3f4: ret
    //     0xa7d3f4: ret             
    // 0xa7d3f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7d3f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7d3fc: b               #0xa7d38c
  }
  _ activate(/* No info */) {
    // ** addr: 0xa84e84, size: 0x48
    // 0xa84e84: EnterFrame
    //     0xa84e84: stp             fp, lr, [SP, #-0x10]!
    //     0xa84e88: mov             fp, SP
    // 0xa84e8c: AllocStack(0x8)
    //     0xa84e8c: sub             SP, SP, #8
    // 0xa84e90: SetupParameters(__ExpansionTileState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa84e90: mov             x0, x1
    //     0xa84e94: stur            x1, [fp, #-8]
    // 0xa84e98: CheckStackOverflow
    //     0xa84e98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa84e9c: cmp             SP, x16
    //     0xa84ea0: b.ls            #0xa84ec4
    // 0xa84ea4: mov             x1, x0
    // 0xa84ea8: r0 = _updateTickerModeNotifier()
    //     0xa84ea8: bl              #0x6f391c  ; [package:flutter/src/material/expansion_tile.dart] __ExpansionTileState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa84eac: ldur            x1, [fp, #-8]
    // 0xa84eb0: r0 = _updateTicker()
    //     0xa84eb0: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0xa84eb4: r0 = Null
    //     0xa84eb4: mov             x0, NULL
    // 0xa84eb8: LeaveFrame
    //     0xa84eb8: mov             SP, fp
    //     0xa84ebc: ldp             fp, lr, [SP], #0x10
    // 0xa84ec0: ret
    //     0xa84ec0: ret             
    // 0xa84ec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa84ec4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa84ec8: b               #0xa84ea4
  }
}

// class id: 4314, size: 0x58, field offset: 0x1c
class _ExpansionTileState extends __ExpansionTileState&State&SingleTickerProviderStateMixin {

  late AnimationController _animationController; // offset: 0x30
  late Animation<Color?> _backgroundColor; // offset: 0x48
  late Animation<ShapeBorder?> _border; // offset: 0x3c
  late Animation<Color?> _iconColor; // offset: 0x44
  late Animation<Color?> _headerColor; // offset: 0x40
  late CurvedAnimation _heightFactor; // offset: 0x38
  late Animation<double> _iconTurns; // offset: 0x34
  late ExpansionTileController _tileController; // offset: 0x50
  static late final Animatable<double> _halfTween; // offset: 0xa78
  static late final Animatable<double> _easeInTween; // offset: 0xa74
  static late final Animatable<double> _easeOutTween; // offset: 0xa70

  _ initState(/* No info */) {
    // ** addr: 0x933238, size: 0x3c4
    // 0x933238: EnterFrame
    //     0x933238: stp             fp, lr, [SP, #-0x10]!
    //     0x93323c: mov             fp, SP
    // 0x933240: AllocStack(0x28)
    //     0x933240: sub             SP, SP, #0x28
    // 0x933244: SetupParameters(_ExpansionTileState this /* r1 => r2, fp-0x8 */)
    //     0x933244: mov             x2, x1
    //     0x933248: stur            x1, [fp, #-8]
    // 0x93324c: CheckStackOverflow
    //     0x93324c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x933250: cmp             SP, x16
    //     0x933254: b.ls            #0x9335e4
    // 0x933258: r1 = <double>
    //     0x933258: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x93325c: r0 = AnimationController()
    //     0x93325c: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x933260: stur            x0, [fp, #-0x10]
    // 0x933264: r16 = Instance_Duration
    //     0x933264: add             x16, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0x933268: ldr             x16, [x16, #0x368]
    // 0x93326c: str             x16, [SP]
    // 0x933270: mov             x1, x0
    // 0x933274: ldur            x2, [fp, #-8]
    // 0x933278: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x933278: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x93327c: ldr             x4, [x4, #0x408]
    // 0x933280: r0 = AnimationController()
    //     0x933280: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x933284: ldur            x0, [fp, #-0x10]
    // 0x933288: ldur            x3, [fp, #-8]
    // 0x93328c: StoreField: r3->field_2f = r0
    //     0x93328c: stur            w0, [x3, #0x2f]
    //     0x933290: ldurb           w16, [x3, #-1]
    //     0x933294: ldurb           w17, [x0, #-1]
    //     0x933298: and             x16, x17, x16, lsr #2
    //     0x93329c: tst             x16, HEAP, lsr #32
    //     0x9332a0: b.eq            #0x9332a8
    //     0x9332a4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x9332a8: LoadField: r1 = r3->field_2b
    //     0x9332a8: ldur            w1, [x3, #0x2b]
    // 0x9332ac: DecompressPointer r1
    //     0x9332ac: add             x1, x1, HEAP, lsl #32
    // 0x9332b0: ldur            x2, [fp, #-0x10]
    // 0x9332b4: r0 = animate()
    //     0x9332b4: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x9332b8: r1 = <double>
    //     0x9332b8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9332bc: stur            x0, [fp, #-0x10]
    // 0x9332c0: r0 = CurvedAnimation()
    //     0x9332c0: bl              #0x7e34d0  ; AllocateCurvedAnimationStub -> CurvedAnimation (size=0x1c)
    // 0x9332c4: mov             x1, x0
    // 0x9332c8: ldur            x3, [fp, #-0x10]
    // 0x9332cc: r2 = Instance_Cubic
    //     0x9332cc: add             x2, PP, #0x38, lsl #12  ; [pp+0x38408] Obj!Cubic@e14e31
    //     0x9332d0: ldr             x2, [x2, #0x408]
    // 0x9332d4: stur            x0, [fp, #-0x10]
    // 0x9332d8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x9332d8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x9332dc: r0 = CurvedAnimation()
    //     0x9332dc: bl              #0x7e338c  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::CurvedAnimation
    // 0x9332e0: ldur            x0, [fp, #-0x10]
    // 0x9332e4: ldur            x1, [fp, #-8]
    // 0x9332e8: StoreField: r1->field_37 = r0
    //     0x9332e8: stur            w0, [x1, #0x37]
    //     0x9332ec: ldurb           w16, [x1, #-1]
    //     0x9332f0: ldurb           w17, [x0, #-1]
    //     0x9332f4: and             x16, x17, x16, lsr #2
    //     0x9332f8: tst             x16, HEAP, lsr #32
    //     0x9332fc: b.eq            #0x933304
    //     0x933300: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x933304: LoadField: r2 = r1->field_2f
    //     0x933304: ldur            w2, [x1, #0x2f]
    // 0x933308: DecompressPointer r2
    //     0x933308: add             x2, x2, HEAP, lsl #32
    // 0x93330c: stur            x2, [fp, #-0x10]
    // 0x933310: r0 = InitLateStaticField(0xa78) // [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_halfTween
    //     0x933310: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x933314: ldr             x0, [x0, #0x14f0]
    //     0x933318: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93331c: cmp             w0, w16
    //     0x933320: b.ne            #0x933330
    //     0x933324: add             x2, PP, #0x5d, lsl #12  ; [pp+0x5d838] Field <_ExpansionTileState@543392950._halfTween@543392950>: static late final (offset: 0xa78)
    //     0x933328: ldr             x2, [x2, #0x838]
    //     0x93332c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x933330: stur            x0, [fp, #-0x18]
    // 0x933334: r0 = InitLateStaticField(0xa74) // [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_easeInTween
    //     0x933334: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x933338: ldr             x0, [x0, #0x14e8]
    //     0x93333c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x933340: cmp             w0, w16
    //     0x933344: b.ne            #0x933354
    //     0x933348: add             x2, PP, #0x5d, lsl #12  ; [pp+0x5d840] Field <_ExpansionTileState@543392950._easeInTween@543392950>: static late final (offset: 0xa74)
    //     0x93334c: ldr             x2, [x2, #0x840]
    //     0x933350: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x933354: ldur            x1, [fp, #-0x18]
    // 0x933358: mov             x2, x0
    // 0x93335c: stur            x0, [fp, #-0x18]
    // 0x933360: r0 = chain()
    //     0x933360: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x933364: mov             x1, x0
    // 0x933368: ldur            x2, [fp, #-0x10]
    // 0x93336c: r0 = animate()
    //     0x93336c: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x933370: ldur            x1, [fp, #-8]
    // 0x933374: StoreField: r1->field_33 = r0
    //     0x933374: stur            w0, [x1, #0x33]
    //     0x933378: ldurb           w16, [x1, #-1]
    //     0x93337c: ldurb           w17, [x0, #-1]
    //     0x933380: and             x16, x17, x16, lsr #2
    //     0x933384: tst             x16, HEAP, lsr #32
    //     0x933388: b.eq            #0x933390
    //     0x93338c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x933390: LoadField: r2 = r1->field_2f
    //     0x933390: ldur            w2, [x1, #0x2f]
    // 0x933394: DecompressPointer r2
    //     0x933394: add             x2, x2, HEAP, lsl #32
    // 0x933398: stur            x2, [fp, #-0x20]
    // 0x93339c: LoadField: r0 = r1->field_1b
    //     0x93339c: ldur            w0, [x1, #0x1b]
    // 0x9333a0: DecompressPointer r0
    //     0x9333a0: add             x0, x0, HEAP, lsl #32
    // 0x9333a4: stur            x0, [fp, #-0x10]
    // 0x9333a8: r0 = InitLateStaticField(0xa70) // [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_easeOutTween
    //     0x9333a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9333ac: ldr             x0, [x0, #0x14e0]
    //     0x9333b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9333b4: cmp             w0, w16
    //     0x9333b8: b.ne            #0x9333c8
    //     0x9333bc: add             x2, PP, #0x5d, lsl #12  ; [pp+0x5d848] Field <_ExpansionTileState@543392950._easeOutTween@543392950>: static late final (offset: 0xa70)
    //     0x9333c0: ldr             x2, [x2, #0x848]
    //     0x9333c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9333c8: ldur            x1, [fp, #-0x10]
    // 0x9333cc: mov             x2, x0
    // 0x9333d0: stur            x0, [fp, #-0x10]
    // 0x9333d4: r0 = chain()
    //     0x9333d4: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x9333d8: mov             x1, x0
    // 0x9333dc: ldur            x2, [fp, #-0x20]
    // 0x9333e0: r0 = animate()
    //     0x9333e0: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x9333e4: ldur            x3, [fp, #-8]
    // 0x9333e8: StoreField: r3->field_3b = r0
    //     0x9333e8: stur            w0, [x3, #0x3b]
    //     0x9333ec: ldurb           w16, [x3, #-1]
    //     0x9333f0: ldurb           w17, [x0, #-1]
    //     0x9333f4: and             x16, x17, x16, lsr #2
    //     0x9333f8: tst             x16, HEAP, lsr #32
    //     0x9333fc: b.eq            #0x933404
    //     0x933400: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x933404: LoadField: r0 = r3->field_2f
    //     0x933404: ldur            w0, [x3, #0x2f]
    // 0x933408: DecompressPointer r0
    //     0x933408: add             x0, x0, HEAP, lsl #32
    // 0x93340c: stur            x0, [fp, #-0x20]
    // 0x933410: LoadField: r1 = r3->field_1f
    //     0x933410: ldur            w1, [x3, #0x1f]
    // 0x933414: DecompressPointer r1
    //     0x933414: add             x1, x1, HEAP, lsl #32
    // 0x933418: ldur            x2, [fp, #-0x18]
    // 0x93341c: r0 = chain()
    //     0x93341c: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x933420: mov             x1, x0
    // 0x933424: ldur            x2, [fp, #-0x20]
    // 0x933428: r0 = animate()
    //     0x933428: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x93342c: ldur            x3, [fp, #-8]
    // 0x933430: StoreField: r3->field_3f = r0
    //     0x933430: stur            w0, [x3, #0x3f]
    //     0x933434: ldurb           w16, [x3, #-1]
    //     0x933438: ldurb           w17, [x0, #-1]
    //     0x93343c: and             x16, x17, x16, lsr #2
    //     0x933440: tst             x16, HEAP, lsr #32
    //     0x933444: b.eq            #0x93344c
    //     0x933448: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x93344c: LoadField: r0 = r3->field_2f
    //     0x93344c: ldur            w0, [x3, #0x2f]
    // 0x933450: DecompressPointer r0
    //     0x933450: add             x0, x0, HEAP, lsl #32
    // 0x933454: stur            x0, [fp, #-0x20]
    // 0x933458: LoadField: r1 = r3->field_23
    //     0x933458: ldur            w1, [x3, #0x23]
    // 0x93345c: DecompressPointer r1
    //     0x93345c: add             x1, x1, HEAP, lsl #32
    // 0x933460: ldur            x2, [fp, #-0x18]
    // 0x933464: r0 = chain()
    //     0x933464: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x933468: mov             x1, x0
    // 0x93346c: ldur            x2, [fp, #-0x20]
    // 0x933470: r0 = animate()
    //     0x933470: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x933474: ldur            x3, [fp, #-8]
    // 0x933478: StoreField: r3->field_43 = r0
    //     0x933478: stur            w0, [x3, #0x43]
    //     0x93347c: ldurb           w16, [x3, #-1]
    //     0x933480: ldurb           w17, [x0, #-1]
    //     0x933484: and             x16, x17, x16, lsr #2
    //     0x933488: tst             x16, HEAP, lsr #32
    //     0x93348c: b.eq            #0x933494
    //     0x933490: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x933494: LoadField: r0 = r3->field_2f
    //     0x933494: ldur            w0, [x3, #0x2f]
    // 0x933498: DecompressPointer r0
    //     0x933498: add             x0, x0, HEAP, lsl #32
    // 0x93349c: stur            x0, [fp, #-0x18]
    // 0x9334a0: LoadField: r1 = r3->field_27
    //     0x9334a0: ldur            w1, [x3, #0x27]
    // 0x9334a4: DecompressPointer r1
    //     0x9334a4: add             x1, x1, HEAP, lsl #32
    // 0x9334a8: ldur            x2, [fp, #-0x10]
    // 0x9334ac: r0 = chain()
    //     0x9334ac: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x9334b0: mov             x1, x0
    // 0x9334b4: ldur            x2, [fp, #-0x18]
    // 0x9334b8: r0 = animate()
    //     0x9334b8: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x9334bc: ldur            x2, [fp, #-8]
    // 0x9334c0: StoreField: r2->field_47 = r0
    //     0x9334c0: stur            w0, [x2, #0x47]
    //     0x9334c4: ldurb           w16, [x2, #-1]
    //     0x9334c8: ldurb           w17, [x0, #-1]
    //     0x9334cc: and             x16, x17, x16, lsr #2
    //     0x9334d0: tst             x16, HEAP, lsr #32
    //     0x9334d4: b.eq            #0x9334dc
    //     0x9334d8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9334dc: LoadField: r1 = r2->field_f
    //     0x9334dc: ldur            w1, [x2, #0xf]
    // 0x9334e0: DecompressPointer r1
    //     0x9334e0: add             x1, x1, HEAP, lsl #32
    // 0x9334e4: cmp             w1, NULL
    // 0x9334e8: b.eq            #0x9335ec
    // 0x9334ec: r0 = maybeOf()
    //     0x9334ec: bl              #0x679fbc  ; [package:flutter/src/widgets/page_storage.dart] PageStorage::maybeOf
    // 0x9334f0: cmp             w0, NULL
    // 0x9334f4: b.ne            #0x933500
    // 0x9334f8: r3 = Null
    //     0x9334f8: mov             x3, NULL
    // 0x9334fc: b               #0x933520
    // 0x933500: ldur            x3, [fp, #-8]
    // 0x933504: LoadField: r2 = r3->field_f
    //     0x933504: ldur            w2, [x3, #0xf]
    // 0x933508: DecompressPointer r2
    //     0x933508: add             x2, x2, HEAP, lsl #32
    // 0x93350c: cmp             w2, NULL
    // 0x933510: b.eq            #0x9335f0
    // 0x933514: mov             x1, x0
    // 0x933518: r0 = readState()
    //     0x933518: bl              #0x933608  ; [package:flutter/src/widgets/page_storage.dart] PageStorageBucket::readState
    // 0x93351c: mov             x3, x0
    // 0x933520: mov             x0, x3
    // 0x933524: stur            x3, [fp, #-0x10]
    // 0x933528: r2 = Null
    //     0x933528: mov             x2, NULL
    // 0x93352c: r1 = Null
    //     0x93352c: mov             x1, NULL
    // 0x933530: r4 = 60
    //     0x933530: movz            x4, #0x3c
    // 0x933534: branchIfSmi(r0, 0x933540)
    //     0x933534: tbz             w0, #0, #0x933540
    // 0x933538: r4 = LoadClassIdInstr(r0)
    //     0x933538: ldur            x4, [x0, #-1]
    //     0x93353c: ubfx            x4, x4, #0xc, #0x14
    // 0x933540: cmp             x4, #0x3f
    // 0x933544: b.eq            #0x933558
    // 0x933548: r8 = bool?
    //     0x933548: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x93354c: r3 = Null
    //     0x93354c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d850] Null
    //     0x933550: ldr             x3, [x3, #0x850]
    // 0x933554: r0 = bool?()
    //     0x933554: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x933558: ldur            x0, [fp, #-0x10]
    // 0x93355c: cmp             w0, NULL
    // 0x933560: b.ne            #0x933580
    // 0x933564: ldur            x2, [fp, #-8]
    // 0x933568: LoadField: r0 = r2->field_b
    //     0x933568: ldur            w0, [x2, #0xb]
    // 0x93356c: DecompressPointer r0
    //     0x93356c: add             x0, x0, HEAP, lsl #32
    // 0x933570: cmp             w0, NULL
    // 0x933574: b.eq            #0x9335f4
    // 0x933578: r0 = false
    //     0x933578: add             x0, NULL, #0x30  ; false
    // 0x93357c: b               #0x933584
    // 0x933580: ldur            x2, [fp, #-8]
    // 0x933584: StoreField: r2->field_4b = r0
    //     0x933584: stur            w0, [x2, #0x4b]
    // 0x933588: tbnz            w0, #4, #0x93359c
    // 0x93358c: LoadField: r1 = r2->field_2f
    //     0x93358c: ldur            w1, [x2, #0x2f]
    // 0x933590: DecompressPointer r1
    //     0x933590: add             x1, x1, HEAP, lsl #32
    // 0x933594: d0 = 1.000000
    //     0x933594: fmov            d0, #1.00000000
    // 0x933598: r0 = value=()
    //     0x933598: bl              #0x6567c4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::value=
    // 0x93359c: ldur            x0, [fp, #-8]
    // 0x9335a0: LoadField: r1 = r0->field_b
    //     0x9335a0: ldur            w1, [x0, #0xb]
    // 0x9335a4: DecompressPointer r1
    //     0x9335a4: add             x1, x1, HEAP, lsl #32
    // 0x9335a8: cmp             w1, NULL
    // 0x9335ac: b.eq            #0x9335f8
    // 0x9335b0: r0 = ExpansionTileController()
    //     0x9335b0: bl              #0x9335fc  ; AllocateExpansionTileControllerStub -> ExpansionTileController (size=0x8)
    // 0x9335b4: ldur            x1, [fp, #-8]
    // 0x9335b8: StoreField: r1->field_4f = r0
    //     0x9335b8: stur            w0, [x1, #0x4f]
    //     0x9335bc: ldurb           w16, [x1, #-1]
    //     0x9335c0: ldurb           w17, [x0, #-1]
    //     0x9335c4: and             x16, x17, x16, lsr #2
    //     0x9335c8: tst             x16, HEAP, lsr #32
    //     0x9335cc: b.eq            #0x9335d4
    //     0x9335d0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9335d4: r0 = Null
    //     0x9335d4: mov             x0, NULL
    // 0x9335d8: LeaveFrame
    //     0x9335d8: mov             SP, fp
    //     0x9335dc: ldp             fp, lr, [SP], #0x10
    // 0x9335e0: ret
    //     0x9335e0: ret             
    // 0x9335e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9335e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9335e8: b               #0x933258
    // 0x9335ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9335ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9335f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9335f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9335f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9335f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9335f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9335f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static Animatable<double> _easeOutTween() {
    // ** addr: 0x9336c8, size: 0x28
    // 0x9336c8: EnterFrame
    //     0x9336c8: stp             fp, lr, [SP, #-0x10]!
    //     0x9336cc: mov             fp, SP
    // 0x9336d0: r1 = <double>
    //     0x9336d0: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9336d4: r0 = CurveTween()
    //     0x9336d4: bl              #0x796a9c  ; AllocateCurveTweenStub -> CurveTween (size=0x10)
    // 0x9336d8: r1 = Instance_Cubic
    //     0x9336d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!Cubic@e14e61
    //     0x9336dc: ldr             x1, [x1, #0xb28]
    // 0x9336e0: StoreField: r0->field_b = r1
    //     0x9336e0: stur            w1, [x0, #0xb]
    // 0x9336e4: LeaveFrame
    //     0x9336e4: mov             SP, fp
    //     0x9336e8: ldp             fp, lr, [SP], #0x10
    // 0x9336ec: ret
    //     0x9336ec: ret             
  }
  static Animatable<double> _easeInTween() {
    // ** addr: 0x9336f0, size: 0x28
    // 0x9336f0: EnterFrame
    //     0x9336f0: stp             fp, lr, [SP, #-0x10]!
    //     0x9336f4: mov             fp, SP
    // 0x9336f8: r1 = <double>
    //     0x9336f8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9336fc: r0 = CurveTween()
    //     0x9336fc: bl              #0x796a9c  ; AllocateCurveTweenStub -> CurveTween (size=0x10)
    // 0x933700: r1 = Instance_Cubic
    //     0x933700: add             x1, PP, #0x38, lsl #12  ; [pp+0x38408] Obj!Cubic@e14e31
    //     0x933704: ldr             x1, [x1, #0x408]
    // 0x933708: StoreField: r0->field_b = r1
    //     0x933708: stur            w1, [x0, #0xb]
    // 0x93370c: LeaveFrame
    //     0x93370c: mov             SP, fp
    //     0x933710: ldp             fp, lr, [SP], #0x10
    // 0x933714: ret
    //     0x933714: ret             
  }
  static Animatable<double> _halfTween() {
    // ** addr: 0x933718, size: 0x2c
    // 0x933718: EnterFrame
    //     0x933718: stp             fp, lr, [SP, #-0x10]!
    //     0x93371c: mov             fp, SP
    // 0x933720: r1 = <double>
    //     0x933720: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x933724: r0 = Tween()
    //     0x933724: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x933728: r1 = 0.000000
    //     0x933728: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x93372c: StoreField: r0->field_b = r1
    //     0x93372c: stur            w1, [x0, #0xb]
    // 0x933730: r1 = 0.500000
    //     0x933730: ldr             x1, [PP, #0x4928]  ; [pp+0x4928] 0.5
    // 0x933734: StoreField: r0->field_f = r1
    //     0x933734: stur            w1, [x0, #0xf]
    // 0x933738: LeaveFrame
    //     0x933738: mov             SP, fp
    //     0x93373c: ldp             fp, lr, [SP], #0x10
    // 0x933740: ret
    //     0x933740: ret             
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9847e0, size: 0x150
    // 0x9847e0: EnterFrame
    //     0x9847e0: stp             fp, lr, [SP, #-0x10]!
    //     0x9847e4: mov             fp, SP
    // 0x9847e8: AllocStack(0x10)
    //     0x9847e8: sub             SP, SP, #0x10
    // 0x9847ec: SetupParameters(_ExpansionTileState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9847ec: mov             x4, x1
    //     0x9847f0: mov             x3, x2
    //     0x9847f4: stur            x1, [fp, #-8]
    //     0x9847f8: stur            x2, [fp, #-0x10]
    // 0x9847fc: CheckStackOverflow
    //     0x9847fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x984800: cmp             SP, x16
    //     0x984804: b.ls            #0x984914
    // 0x984808: mov             x0, x3
    // 0x98480c: r2 = Null
    //     0x98480c: mov             x2, NULL
    // 0x984810: r1 = Null
    //     0x984810: mov             x1, NULL
    // 0x984814: r4 = 60
    //     0x984814: movz            x4, #0x3c
    // 0x984818: branchIfSmi(r0, 0x984824)
    //     0x984818: tbz             w0, #0, #0x984824
    // 0x98481c: r4 = LoadClassIdInstr(r0)
    //     0x98481c: ldur            x4, [x0, #-1]
    //     0x984820: ubfx            x4, x4, #0xc, #0x14
    // 0x984824: r17 = 4863
    //     0x984824: movz            x17, #0x12ff
    // 0x984828: cmp             x4, x17
    // 0x98482c: b.eq            #0x984844
    // 0x984830: r8 = ExpansionTile
    //     0x984830: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d808] Type: ExpansionTile
    //     0x984834: ldr             x8, [x8, #0x808]
    // 0x984838: r3 = Null
    //     0x984838: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d810] Null
    //     0x98483c: ldr             x3, [x3, #0x810]
    // 0x984840: r0 = ExpansionTile()
    //     0x984840: bl              #0x6f38f8  ; IsType_ExpansionTile_Stub
    // 0x984844: ldur            x3, [fp, #-8]
    // 0x984848: LoadField: r2 = r3->field_7
    //     0x984848: ldur            w2, [x3, #7]
    // 0x98484c: DecompressPointer r2
    //     0x98484c: add             x2, x2, HEAP, lsl #32
    // 0x984850: ldur            x0, [fp, #-0x10]
    // 0x984854: r1 = Null
    //     0x984854: mov             x1, NULL
    // 0x984858: cmp             w2, NULL
    // 0x98485c: b.eq            #0x984880
    // 0x984860: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x984860: ldur            w4, [x2, #0x17]
    // 0x984864: DecompressPointer r4
    //     0x984864: add             x4, x4, HEAP, lsl #32
    // 0x984868: r8 = X0 bound StatefulWidget
    //     0x984868: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x98486c: ldr             x8, [x8, #0x7f8]
    // 0x984870: LoadField: r9 = r4->field_7
    //     0x984870: ldur            x9, [x4, #7]
    // 0x984874: r3 = Null
    //     0x984874: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d820] Null
    //     0x984878: ldr             x3, [x3, #0x820]
    // 0x98487c: blr             x9
    // 0x984880: ldur            x0, [fp, #-8]
    // 0x984884: LoadField: r1 = r0->field_f
    //     0x984884: ldur            w1, [x0, #0xf]
    // 0x984888: DecompressPointer r1
    //     0x984888: add             x1, x1, HEAP, lsl #32
    // 0x98488c: cmp             w1, NULL
    // 0x984890: b.eq            #0x98491c
    // 0x984894: r0 = of()
    //     0x984894: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x984898: mov             x2, x0
    // 0x98489c: ldur            x0, [fp, #-8]
    // 0x9848a0: stur            x2, [fp, #-0x10]
    // 0x9848a4: LoadField: r1 = r0->field_f
    //     0x9848a4: ldur            w1, [x0, #0xf]
    // 0x9848a8: DecompressPointer r1
    //     0x9848a8: add             x1, x1, HEAP, lsl #32
    // 0x9848ac: cmp             w1, NULL
    // 0x9848b0: b.eq            #0x984920
    // 0x9848b4: r0 = of()
    //     0x9848b4: bl              #0x984930  ; [package:flutter/src/material/expansion_tile_theme.dart] ExpansionTileTheme::of
    // 0x9848b8: ldur            x1, [fp, #-0x10]
    // 0x9848bc: LoadField: r2 = r1->field_2f
    //     0x9848bc: ldur            w2, [x1, #0x2f]
    // 0x9848c0: DecompressPointer r2
    //     0x9848c0: add             x2, x2, HEAP, lsl #32
    // 0x9848c4: tbnz            w2, #4, #0x9848e0
    // 0x9848c8: ldur            x1, [fp, #-8]
    // 0x9848cc: LoadField: r2 = r1->field_f
    //     0x9848cc: ldur            w2, [x1, #0xf]
    // 0x9848d0: DecompressPointer r2
    //     0x9848d0: add             x2, x2, HEAP, lsl #32
    // 0x9848d4: cmp             w2, NULL
    // 0x9848d8: b.eq            #0x984924
    // 0x9848dc: b               #0x9848f4
    // 0x9848e0: ldur            x1, [fp, #-8]
    // 0x9848e4: LoadField: r2 = r1->field_f
    //     0x9848e4: ldur            w2, [x1, #0xf]
    // 0x9848e8: DecompressPointer r2
    //     0x9848e8: add             x2, x2, HEAP, lsl #32
    // 0x9848ec: cmp             w2, NULL
    // 0x9848f0: b.eq            #0x984928
    // 0x9848f4: LoadField: r2 = r1->field_b
    //     0x9848f4: ldur            w2, [x1, #0xb]
    // 0x9848f8: DecompressPointer r2
    //     0x9848f8: add             x2, x2, HEAP, lsl #32
    // 0x9848fc: cmp             w2, NULL
    // 0x984900: b.eq            #0x98492c
    // 0x984904: r0 = Null
    //     0x984904: mov             x0, NULL
    // 0x984908: LeaveFrame
    //     0x984908: mov             SP, fp
    //     0x98490c: ldp             fp, lr, [SP], #0x10
    // 0x984910: ret
    //     0x984910: ret             
    // 0x984914: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x984914: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x984918: b               #0x984808
    // 0x98491c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98491c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x984920: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x984920: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x984924: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x984924: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x984928: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x984928: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98492c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98492c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9a35c4, size: 0x160
    // 0x9a35c4: EnterFrame
    //     0x9a35c4: stp             fp, lr, [SP, #-0x10]!
    //     0x9a35c8: mov             fp, SP
    // 0x9a35cc: AllocStack(0x20)
    //     0x9a35cc: sub             SP, SP, #0x20
    // 0x9a35d0: SetupParameters(_ExpansionTileState this /* r1 => r0, fp-0x8 */)
    //     0x9a35d0: mov             x0, x1
    //     0x9a35d4: stur            x1, [fp, #-8]
    // 0x9a35d8: CheckStackOverflow
    //     0x9a35d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a35dc: cmp             SP, x16
    //     0x9a35e0: b.ls            #0x9a370c
    // 0x9a35e4: LoadField: r1 = r0->field_f
    //     0x9a35e4: ldur            w1, [x0, #0xf]
    // 0x9a35e8: DecompressPointer r1
    //     0x9a35e8: add             x1, x1, HEAP, lsl #32
    // 0x9a35ec: cmp             w1, NULL
    // 0x9a35f0: b.eq            #0x9a3714
    // 0x9a35f4: r0 = of()
    //     0x9a35f4: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9a35f8: mov             x2, x0
    // 0x9a35fc: ldur            x0, [fp, #-8]
    // 0x9a3600: stur            x2, [fp, #-0x10]
    // 0x9a3604: LoadField: r1 = r0->field_f
    //     0x9a3604: ldur            w1, [x0, #0xf]
    // 0x9a3608: DecompressPointer r1
    //     0x9a3608: add             x1, x1, HEAP, lsl #32
    // 0x9a360c: cmp             w1, NULL
    // 0x9a3610: b.eq            #0x9a3718
    // 0x9a3614: r0 = of()
    //     0x9a3614: bl              #0x984930  ; [package:flutter/src/material/expansion_tile_theme.dart] ExpansionTileTheme::of
    // 0x9a3618: ldur            x3, [fp, #-0x10]
    // 0x9a361c: stur            x0, [fp, #-0x20]
    // 0x9a3620: LoadField: r1 = r3->field_2f
    //     0x9a3620: ldur            w1, [x3, #0x2f]
    // 0x9a3624: DecompressPointer r1
    //     0x9a3624: add             x1, x1, HEAP, lsl #32
    // 0x9a3628: tbnz            w1, #4, #0x9a3668
    // 0x9a362c: ldur            x1, [fp, #-8]
    // 0x9a3630: LoadField: r2 = r1->field_f
    //     0x9a3630: ldur            w2, [x1, #0xf]
    // 0x9a3634: DecompressPointer r2
    //     0x9a3634: add             x2, x2, HEAP, lsl #32
    // 0x9a3638: stur            x2, [fp, #-0x18]
    // 0x9a363c: cmp             w2, NULL
    // 0x9a3640: b.eq            #0x9a371c
    // 0x9a3644: r0 = _ExpansionTileDefaultsM3()
    //     0x9a3644: bl              #0x9a402c  ; Allocate_ExpansionTileDefaultsM3Stub -> _ExpansionTileDefaultsM3 (size=0x48)
    // 0x9a3648: mov             x1, x0
    // 0x9a364c: r0 = Sentinel
    //     0x9a364c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a3650: StoreField: r1->field_3f = r0
    //     0x9a3650: stur            w0, [x1, #0x3f]
    // 0x9a3654: StoreField: r1->field_43 = r0
    //     0x9a3654: stur            w0, [x1, #0x43]
    // 0x9a3658: ldur            x0, [fp, #-0x18]
    // 0x9a365c: StoreField: r1->field_3b = r0
    //     0x9a365c: stur            w0, [x1, #0x3b]
    // 0x9a3660: mov             x3, x1
    // 0x9a3664: b               #0x9a36a4
    // 0x9a3668: ldur            x1, [fp, #-8]
    // 0x9a366c: r0 = Sentinel
    //     0x9a366c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a3670: LoadField: r2 = r1->field_f
    //     0x9a3670: ldur            w2, [x1, #0xf]
    // 0x9a3674: DecompressPointer r2
    //     0x9a3674: add             x2, x2, HEAP, lsl #32
    // 0x9a3678: stur            x2, [fp, #-0x18]
    // 0x9a367c: cmp             w2, NULL
    // 0x9a3680: b.eq            #0x9a3720
    // 0x9a3684: r0 = _ExpansionTileDefaultsM2()
    //     0x9a3684: bl              #0x9a4020  ; Allocate_ExpansionTileDefaultsM2Stub -> _ExpansionTileDefaultsM2 (size=0x48)
    // 0x9a3688: mov             x1, x0
    // 0x9a368c: r0 = Sentinel
    //     0x9a368c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a3690: StoreField: r1->field_3f = r0
    //     0x9a3690: stur            w0, [x1, #0x3f]
    // 0x9a3694: StoreField: r1->field_43 = r0
    //     0x9a3694: stur            w0, [x1, #0x43]
    // 0x9a3698: ldur            x0, [fp, #-0x18]
    // 0x9a369c: StoreField: r1->field_3b = r0
    //     0x9a369c: stur            w0, [x1, #0x3b]
    // 0x9a36a0: mov             x3, x1
    // 0x9a36a4: ldur            x1, [fp, #-8]
    // 0x9a36a8: ldur            x2, [fp, #-0x20]
    // 0x9a36ac: stur            x3, [fp, #-0x18]
    // 0x9a36b0: r0 = _updateAnimationDuration()
    //     0x9a36b0: bl              #0x9a3fc8  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_updateAnimationDuration
    // 0x9a36b4: ldur            x1, [fp, #-8]
    // 0x9a36b8: ldur            x2, [fp, #-0x20]
    // 0x9a36bc: ldur            x3, [fp, #-0x10]
    // 0x9a36c0: r0 = _updateShapeBorder()
    //     0x9a36c0: bl              #0x9a3e34  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_updateShapeBorder
    // 0x9a36c4: ldur            x1, [fp, #-8]
    // 0x9a36c8: ldur            x2, [fp, #-0x20]
    // 0x9a36cc: ldur            x3, [fp, #-0x18]
    // 0x9a36d0: r0 = _updateHeaderColor()
    //     0x9a36d0: bl              #0x9a3b90  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_updateHeaderColor
    // 0x9a36d4: ldur            x1, [fp, #-8]
    // 0x9a36d8: ldur            x2, [fp, #-0x20]
    // 0x9a36dc: ldur            x3, [fp, #-0x18]
    // 0x9a36e0: r0 = _updateIconColor()
    //     0x9a36e0: bl              #0x9a3804  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_updateIconColor
    // 0x9a36e4: ldur            x1, [fp, #-8]
    // 0x9a36e8: ldur            x2, [fp, #-0x20]
    // 0x9a36ec: r0 = _updateBackgroundColor()
    //     0x9a36ec: bl              #0x9a3780  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_updateBackgroundColor
    // 0x9a36f0: ldur            x1, [fp, #-8]
    // 0x9a36f4: ldur            x2, [fp, #-0x20]
    // 0x9a36f8: r0 = _updateHeightFactorCurve()
    //     0x9a36f8: bl              #0x9a3724  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_updateHeightFactorCurve
    // 0x9a36fc: r0 = Null
    //     0x9a36fc: mov             x0, NULL
    // 0x9a3700: LeaveFrame
    //     0x9a3700: mov             SP, fp
    //     0x9a3704: ldp             fp, lr, [SP], #0x10
    // 0x9a3708: ret
    //     0x9a3708: ret             
    // 0x9a370c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a370c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a3710: b               #0x9a35e4
    // 0x9a3714: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a3714: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a3718: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a3718: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a371c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a371c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a3720: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a3720: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateHeightFactorCurve(/* No info */) {
    // ** addr: 0x9a3724, size: 0x5c
    // 0x9a3724: EnterFrame
    //     0x9a3724: stp             fp, lr, [SP, #-0x10]!
    //     0x9a3728: mov             fp, SP
    // 0x9a372c: r3 = Instance_Cubic
    //     0x9a372c: add             x3, PP, #0x38, lsl #12  ; [pp+0x38408] Obj!Cubic@e14e31
    //     0x9a3730: ldr             x3, [x3, #0x408]
    // 0x9a3734: LoadField: r2 = r1->field_37
    //     0x9a3734: ldur            w2, [x1, #0x37]
    // 0x9a3738: DecompressPointer r2
    //     0x9a3738: add             x2, x2, HEAP, lsl #32
    // 0x9a373c: r16 = Sentinel
    //     0x9a373c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a3740: cmp             w2, w16
    // 0x9a3744: b.eq            #0x9a3770
    // 0x9a3748: LoadField: r4 = r1->field_b
    //     0x9a3748: ldur            w4, [x1, #0xb]
    // 0x9a374c: DecompressPointer r4
    //     0x9a374c: add             x4, x4, HEAP, lsl #32
    // 0x9a3750: cmp             w4, NULL
    // 0x9a3754: b.eq            #0x9a377c
    // 0x9a3758: StoreField: r2->field_f = r3
    //     0x9a3758: stur            w3, [x2, #0xf]
    // 0x9a375c: StoreField: r2->field_13 = rNULL
    //     0x9a375c: stur            NULL, [x2, #0x13]
    // 0x9a3760: r0 = Null
    //     0x9a3760: mov             x0, NULL
    // 0x9a3764: LeaveFrame
    //     0x9a3764: mov             SP, fp
    //     0x9a3768: ldp             fp, lr, [SP], #0x10
    // 0x9a376c: ret
    //     0x9a376c: ret             
    // 0x9a3770: r9 = _heightFactor
    //     0x9a3770: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d750] Field <_ExpansionTileState@543392950._heightFactor@543392950>: late (offset: 0x38)
    //     0x9a3774: ldr             x9, [x9, #0x750]
    // 0x9a3778: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a3778: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a377c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a377c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateBackgroundColor(/* No info */) {
    // ** addr: 0x9a3780, size: 0x84
    // 0x9a3780: EnterFrame
    //     0x9a3780: stp             fp, lr, [SP, #-0x10]!
    //     0x9a3784: mov             fp, SP
    // 0x9a3788: AllocStack(0x8)
    //     0x9a3788: sub             SP, SP, #8
    // 0x9a378c: LoadField: r3 = r1->field_27
    //     0x9a378c: ldur            w3, [x1, #0x27]
    // 0x9a3790: DecompressPointer r3
    //     0x9a3790: add             x3, x3, HEAP, lsl #32
    // 0x9a3794: stur            x3, [fp, #-8]
    // 0x9a3798: LoadField: r0 = r1->field_b
    //     0x9a3798: ldur            w0, [x1, #0xb]
    // 0x9a379c: DecompressPointer r0
    //     0x9a379c: add             x0, x0, HEAP, lsl #32
    // 0x9a37a0: cmp             w0, NULL
    // 0x9a37a4: b.eq            #0x9a3800
    // 0x9a37a8: LoadField: r2 = r3->field_7
    //     0x9a37a8: ldur            w2, [x3, #7]
    // 0x9a37ac: DecompressPointer r2
    //     0x9a37ac: add             x2, x2, HEAP, lsl #32
    // 0x9a37b0: r0 = Null
    //     0x9a37b0: mov             x0, NULL
    // 0x9a37b4: r1 = Null
    //     0x9a37b4: mov             x1, NULL
    // 0x9a37b8: cmp             w0, NULL
    // 0x9a37bc: b.eq            #0x9a37e4
    // 0x9a37c0: cmp             w2, NULL
    // 0x9a37c4: b.eq            #0x9a37e4
    // 0x9a37c8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a37c8: ldur            w4, [x2, #0x17]
    // 0x9a37cc: DecompressPointer r4
    //     0x9a37cc: add             x4, x4, HEAP, lsl #32
    // 0x9a37d0: r8 = X0?
    //     0x9a37d0: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9a37d4: LoadField: r9 = r4->field_7
    //     0x9a37d4: ldur            x9, [x4, #7]
    // 0x9a37d8: r3 = Null
    //     0x9a37d8: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d798] Null
    //     0x9a37dc: ldr             x3, [x3, #0x798]
    // 0x9a37e0: blr             x9
    // 0x9a37e4: ldur            x1, [fp, #-8]
    // 0x9a37e8: StoreField: r1->field_b = rNULL
    //     0x9a37e8: stur            NULL, [x1, #0xb]
    // 0x9a37ec: StoreField: r1->field_f = rNULL
    //     0x9a37ec: stur            NULL, [x1, #0xf]
    // 0x9a37f0: r0 = Null
    //     0x9a37f0: mov             x0, NULL
    // 0x9a37f4: LeaveFrame
    //     0x9a37f4: mov             SP, fp
    //     0x9a37f8: ldp             fp, lr, [SP], #0x10
    // 0x9a37fc: ret
    //     0x9a37fc: ret             
    // 0x9a3800: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a3800: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateIconColor(/* No info */) {
    // ** addr: 0x9a3804, size: 0x2a4
    // 0x9a3804: EnterFrame
    //     0x9a3804: stp             fp, lr, [SP, #-0x10]!
    //     0x9a3808: mov             fp, SP
    // 0x9a380c: AllocStack(0x30)
    //     0x9a380c: sub             SP, SP, #0x30
    // 0x9a3810: SetupParameters(_ExpansionTileState this /* r1 => r3, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0x9a3810: mov             x0, x3
    //     0x9a3814: stur            x3, [fp, #-0x20]
    //     0x9a3818: mov             x3, x1
    //     0x9a381c: stur            x1, [fp, #-0x18]
    // 0x9a3820: CheckStackOverflow
    //     0x9a3820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a3824: cmp             SP, x16
    //     0x9a3828: b.ls            #0x9a3a98
    // 0x9a382c: LoadField: r2 = r3->field_23
    //     0x9a382c: ldur            w2, [x3, #0x23]
    // 0x9a3830: DecompressPointer r2
    //     0x9a3830: add             x2, x2, HEAP, lsl #32
    // 0x9a3834: stur            x2, [fp, #-0x10]
    // 0x9a3838: LoadField: r1 = r3->field_b
    //     0x9a3838: ldur            w1, [x3, #0xb]
    // 0x9a383c: DecompressPointer r1
    //     0x9a383c: add             x1, x1, HEAP, lsl #32
    // 0x9a3840: cmp             w1, NULL
    // 0x9a3844: b.eq            #0x9a3aa0
    // 0x9a3848: r4 = LoadClassIdInstr(r0)
    //     0x9a3848: ldur            x4, [x0, #-1]
    //     0x9a384c: ubfx            x4, x4, #0xc, #0x14
    // 0x9a3850: stur            x4, [fp, #-8]
    // 0x9a3854: cmp             x4, #0xf6f
    // 0x9a3858: b.ne            #0x9a3874
    // 0x9a385c: LoadField: r1 = r0->field_1f
    //     0x9a385c: ldur            w1, [x0, #0x1f]
    // 0x9a3860: DecompressPointer r1
    //     0x9a3860: add             x1, x1, HEAP, lsl #32
    // 0x9a3864: mov             x6, x1
    // 0x9a3868: mov             x5, x4
    // 0x9a386c: mov             x4, x2
    // 0x9a3870: b               #0x9a3914
    // 0x9a3874: cmp             x4, #0xf70
    // 0x9a3878: b.ne            #0x9a38d8
    // 0x9a387c: mov             x1, x0
    // 0x9a3880: LoadField: r0 = r1->field_43
    //     0x9a3880: ldur            w0, [x1, #0x43]
    // 0x9a3884: DecompressPointer r0
    //     0x9a3884: add             x0, x0, HEAP, lsl #32
    // 0x9a3888: r16 = Sentinel
    //     0x9a3888: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a388c: cmp             w0, w16
    // 0x9a3890: b.ne            #0x9a38a0
    // 0x9a3894: r2 = _colors
    //     0x9a3894: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d390] Field <_ExpansionTileDefaultsM3@543392950._colors@543392950>: late final (offset: 0x44)
    //     0x9a3898: ldr             x2, [x2, #0x390]
    // 0x9a389c: r0 = InitLateFinalInstanceField()
    //     0x9a389c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9a38a0: LoadField: r1 = r0->field_a3
    //     0x9a38a0: ldur            w1, [x0, #0xa3]
    // 0x9a38a4: DecompressPointer r1
    //     0x9a38a4: add             x1, x1, HEAP, lsl #32
    // 0x9a38a8: cmp             w1, NULL
    // 0x9a38ac: b.ne            #0x9a38c0
    // 0x9a38b0: LoadField: r1 = r0->field_7f
    //     0x9a38b0: ldur            w1, [x0, #0x7f]
    // 0x9a38b4: DecompressPointer r1
    //     0x9a38b4: add             x1, x1, HEAP, lsl #32
    // 0x9a38b8: mov             x0, x1
    // 0x9a38bc: b               #0x9a38c4
    // 0x9a38c0: mov             x0, x1
    // 0x9a38c4: mov             x6, x0
    // 0x9a38c8: ldur            x3, [fp, #-0x18]
    // 0x9a38cc: ldur            x4, [fp, #-0x10]
    // 0x9a38d0: ldur            x5, [fp, #-8]
    // 0x9a38d4: b               #0x9a3914
    // 0x9a38d8: ldur            x1, [fp, #-0x20]
    // 0x9a38dc: LoadField: r0 = r1->field_3f
    //     0x9a38dc: ldur            w0, [x1, #0x3f]
    // 0x9a38e0: DecompressPointer r0
    //     0x9a38e0: add             x0, x0, HEAP, lsl #32
    // 0x9a38e4: r16 = Sentinel
    //     0x9a38e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a38e8: cmp             w0, w16
    // 0x9a38ec: b.ne            #0x9a38fc
    // 0x9a38f0: r2 = _theme
    //     0x9a38f0: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d3a0] Field <_ExpansionTileDefaultsM2@543392950._theme@543392950>: late final (offset: 0x40)
    //     0x9a38f4: ldr             x2, [x2, #0x3a0]
    // 0x9a38f8: r0 = InitLateFinalInstanceField()
    //     0x9a38f8: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9a38fc: LoadField: r1 = r0->field_7f
    //     0x9a38fc: ldur            w1, [x0, #0x7f]
    // 0x9a3900: DecompressPointer r1
    //     0x9a3900: add             x1, x1, HEAP, lsl #32
    // 0x9a3904: mov             x6, x1
    // 0x9a3908: ldur            x3, [fp, #-0x18]
    // 0x9a390c: ldur            x4, [fp, #-0x10]
    // 0x9a3910: ldur            x5, [fp, #-8]
    // 0x9a3914: stur            x6, [fp, #-0x30]
    // 0x9a3918: LoadField: r7 = r4->field_7
    //     0x9a3918: ldur            w7, [x4, #7]
    // 0x9a391c: DecompressPointer r7
    //     0x9a391c: add             x7, x7, HEAP, lsl #32
    // 0x9a3920: mov             x0, x6
    // 0x9a3924: mov             x2, x7
    // 0x9a3928: stur            x7, [fp, #-0x28]
    // 0x9a392c: r1 = Null
    //     0x9a392c: mov             x1, NULL
    // 0x9a3930: cmp             w0, NULL
    // 0x9a3934: b.eq            #0x9a395c
    // 0x9a3938: cmp             w2, NULL
    // 0x9a393c: b.eq            #0x9a395c
    // 0x9a3940: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a3940: ldur            w4, [x2, #0x17]
    // 0x9a3944: DecompressPointer r4
    //     0x9a3944: add             x4, x4, HEAP, lsl #32
    // 0x9a3948: r8 = X0?
    //     0x9a3948: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9a394c: LoadField: r9 = r4->field_7
    //     0x9a394c: ldur            x9, [x4, #7]
    // 0x9a3950: r3 = Null
    //     0x9a3950: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d7a8] Null
    //     0x9a3954: ldr             x3, [x3, #0x7a8]
    // 0x9a3958: blr             x9
    // 0x9a395c: ldur            x0, [fp, #-0x30]
    // 0x9a3960: ldur            x2, [fp, #-0x10]
    // 0x9a3964: StoreField: r2->field_b = r0
    //     0x9a3964: stur            w0, [x2, #0xb]
    //     0x9a3968: ldurb           w16, [x2, #-1]
    //     0x9a396c: ldurb           w17, [x0, #-1]
    //     0x9a3970: and             x16, x17, x16, lsr #2
    //     0x9a3974: tst             x16, HEAP, lsr #32
    //     0x9a3978: b.eq            #0x9a3980
    //     0x9a397c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9a3980: ldur            x0, [fp, #-0x18]
    // 0x9a3984: LoadField: r1 = r0->field_b
    //     0x9a3984: ldur            w1, [x0, #0xb]
    // 0x9a3988: DecompressPointer r1
    //     0x9a3988: add             x1, x1, HEAP, lsl #32
    // 0x9a398c: cmp             w1, NULL
    // 0x9a3990: b.eq            #0x9a3aa4
    // 0x9a3994: ldur            x0, [fp, #-8]
    // 0x9a3998: cmp             x0, #0xf6f
    // 0x9a399c: b.ne            #0x9a39b8
    // 0x9a39a0: ldur            x1, [fp, #-0x20]
    // 0x9a39a4: LoadField: r0 = r1->field_1b
    //     0x9a39a4: ldur            w0, [x1, #0x1b]
    // 0x9a39a8: DecompressPointer r0
    //     0x9a39a8: add             x0, x0, HEAP, lsl #32
    // 0x9a39ac: mov             x4, x0
    // 0x9a39b0: mov             x3, x2
    // 0x9a39b4: b               #0x9a3a28
    // 0x9a39b8: ldur            x1, [fp, #-0x20]
    // 0x9a39bc: cmp             x0, #0xf70
    // 0x9a39c0: b.ne            #0x9a39f8
    // 0x9a39c4: LoadField: r0 = r1->field_43
    //     0x9a39c4: ldur            w0, [x1, #0x43]
    // 0x9a39c8: DecompressPointer r0
    //     0x9a39c8: add             x0, x0, HEAP, lsl #32
    // 0x9a39cc: r16 = Sentinel
    //     0x9a39cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a39d0: cmp             w0, w16
    // 0x9a39d4: b.ne            #0x9a39e4
    // 0x9a39d8: r2 = _colors
    //     0x9a39d8: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d390] Field <_ExpansionTileDefaultsM3@543392950._colors@543392950>: late final (offset: 0x44)
    //     0x9a39dc: ldr             x2, [x2, #0x390]
    // 0x9a39e0: r0 = InitLateFinalInstanceField()
    //     0x9a39e0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9a39e4: LoadField: r1 = r0->field_b
    //     0x9a39e4: ldur            w1, [x0, #0xb]
    // 0x9a39e8: DecompressPointer r1
    //     0x9a39e8: add             x1, x1, HEAP, lsl #32
    // 0x9a39ec: mov             x4, x1
    // 0x9a39f0: ldur            x3, [fp, #-0x10]
    // 0x9a39f4: b               #0x9a3a28
    // 0x9a39f8: LoadField: r0 = r1->field_43
    //     0x9a39f8: ldur            w0, [x1, #0x43]
    // 0x9a39fc: DecompressPointer r0
    //     0x9a39fc: add             x0, x0, HEAP, lsl #32
    // 0x9a3a00: r16 = Sentinel
    //     0x9a3a00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a3a04: cmp             w0, w16
    // 0x9a3a08: b.ne            #0x9a3a18
    // 0x9a3a0c: r2 = _colorScheme
    //     0x9a3a0c: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d398] Field <_ExpansionTileDefaultsM2@543392950._colorScheme@543392950>: late final (offset: 0x44)
    //     0x9a3a10: ldr             x2, [x2, #0x398]
    // 0x9a3a14: r0 = InitLateFinalInstanceField()
    //     0x9a3a14: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9a3a18: LoadField: r1 = r0->field_b
    //     0x9a3a18: ldur            w1, [x0, #0xb]
    // 0x9a3a1c: DecompressPointer r1
    //     0x9a3a1c: add             x1, x1, HEAP, lsl #32
    // 0x9a3a20: mov             x4, x1
    // 0x9a3a24: ldur            x3, [fp, #-0x10]
    // 0x9a3a28: mov             x0, x4
    // 0x9a3a2c: ldur            x2, [fp, #-0x28]
    // 0x9a3a30: stur            x4, [fp, #-0x18]
    // 0x9a3a34: r1 = Null
    //     0x9a3a34: mov             x1, NULL
    // 0x9a3a38: cmp             w0, NULL
    // 0x9a3a3c: b.eq            #0x9a3a64
    // 0x9a3a40: cmp             w2, NULL
    // 0x9a3a44: b.eq            #0x9a3a64
    // 0x9a3a48: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a3a48: ldur            w4, [x2, #0x17]
    // 0x9a3a4c: DecompressPointer r4
    //     0x9a3a4c: add             x4, x4, HEAP, lsl #32
    // 0x9a3a50: r8 = X0?
    //     0x9a3a50: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9a3a54: LoadField: r9 = r4->field_7
    //     0x9a3a54: ldur            x9, [x4, #7]
    // 0x9a3a58: r3 = Null
    //     0x9a3a58: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d7b8] Null
    //     0x9a3a5c: ldr             x3, [x3, #0x7b8]
    // 0x9a3a60: blr             x9
    // 0x9a3a64: ldur            x0, [fp, #-0x18]
    // 0x9a3a68: ldur            x1, [fp, #-0x10]
    // 0x9a3a6c: StoreField: r1->field_f = r0
    //     0x9a3a6c: stur            w0, [x1, #0xf]
    //     0x9a3a70: ldurb           w16, [x1, #-1]
    //     0x9a3a74: ldurb           w17, [x0, #-1]
    //     0x9a3a78: and             x16, x17, x16, lsr #2
    //     0x9a3a7c: tst             x16, HEAP, lsr #32
    //     0x9a3a80: b.eq            #0x9a3a88
    //     0x9a3a84: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9a3a88: r0 = Null
    //     0x9a3a88: mov             x0, NULL
    // 0x9a3a8c: LeaveFrame
    //     0x9a3a8c: mov             SP, fp
    //     0x9a3a90: ldp             fp, lr, [SP], #0x10
    // 0x9a3a94: ret
    //     0x9a3a94: ret             
    // 0x9a3a98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a3a98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a3a9c: b               #0x9a382c
    // 0x9a3aa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a3aa0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a3aa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a3aa4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateHeaderColor(/* No info */) {
    // ** addr: 0x9a3b90, size: 0x2a4
    // 0x9a3b90: EnterFrame
    //     0x9a3b90: stp             fp, lr, [SP, #-0x10]!
    //     0x9a3b94: mov             fp, SP
    // 0x9a3b98: AllocStack(0x30)
    //     0x9a3b98: sub             SP, SP, #0x30
    // 0x9a3b9c: SetupParameters(_ExpansionTileState this /* r1 => r3, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0x9a3b9c: mov             x0, x3
    //     0x9a3ba0: stur            x3, [fp, #-0x20]
    //     0x9a3ba4: mov             x3, x1
    //     0x9a3ba8: stur            x1, [fp, #-0x18]
    // 0x9a3bac: CheckStackOverflow
    //     0x9a3bac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a3bb0: cmp             SP, x16
    //     0x9a3bb4: b.ls            #0x9a3e20
    // 0x9a3bb8: LoadField: r2 = r3->field_1f
    //     0x9a3bb8: ldur            w2, [x3, #0x1f]
    // 0x9a3bbc: DecompressPointer r2
    //     0x9a3bbc: add             x2, x2, HEAP, lsl #32
    // 0x9a3bc0: stur            x2, [fp, #-0x10]
    // 0x9a3bc4: LoadField: r1 = r3->field_b
    //     0x9a3bc4: ldur            w1, [x3, #0xb]
    // 0x9a3bc8: DecompressPointer r1
    //     0x9a3bc8: add             x1, x1, HEAP, lsl #32
    // 0x9a3bcc: cmp             w1, NULL
    // 0x9a3bd0: b.eq            #0x9a3e28
    // 0x9a3bd4: r4 = LoadClassIdInstr(r0)
    //     0x9a3bd4: ldur            x4, [x0, #-1]
    //     0x9a3bd8: ubfx            x4, x4, #0xc, #0x14
    // 0x9a3bdc: stur            x4, [fp, #-8]
    // 0x9a3be0: cmp             x4, #0xf6f
    // 0x9a3be4: b.ne            #0x9a3c00
    // 0x9a3be8: LoadField: r1 = r0->field_27
    //     0x9a3be8: ldur            w1, [x0, #0x27]
    // 0x9a3bec: DecompressPointer r1
    //     0x9a3bec: add             x1, x1, HEAP, lsl #32
    // 0x9a3bf0: mov             x6, x1
    // 0x9a3bf4: mov             x5, x4
    // 0x9a3bf8: mov             x4, x2
    // 0x9a3bfc: b               #0x9a3c9c
    // 0x9a3c00: cmp             x4, #0xf70
    // 0x9a3c04: b.ne            #0x9a3c48
    // 0x9a3c08: mov             x1, x0
    // 0x9a3c0c: LoadField: r0 = r1->field_43
    //     0x9a3c0c: ldur            w0, [x1, #0x43]
    // 0x9a3c10: DecompressPointer r0
    //     0x9a3c10: add             x0, x0, HEAP, lsl #32
    // 0x9a3c14: r16 = Sentinel
    //     0x9a3c14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a3c18: cmp             w0, w16
    // 0x9a3c1c: b.ne            #0x9a3c2c
    // 0x9a3c20: r2 = _colors
    //     0x9a3c20: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d390] Field <_ExpansionTileDefaultsM3@543392950._colors@543392950>: late final (offset: 0x44)
    //     0x9a3c24: ldr             x2, [x2, #0x390]
    // 0x9a3c28: r0 = InitLateFinalInstanceField()
    //     0x9a3c28: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9a3c2c: LoadField: r1 = r0->field_7f
    //     0x9a3c2c: ldur            w1, [x0, #0x7f]
    // 0x9a3c30: DecompressPointer r1
    //     0x9a3c30: add             x1, x1, HEAP, lsl #32
    // 0x9a3c34: mov             x6, x1
    // 0x9a3c38: ldur            x3, [fp, #-0x18]
    // 0x9a3c3c: ldur            x4, [fp, #-0x10]
    // 0x9a3c40: ldur            x5, [fp, #-8]
    // 0x9a3c44: b               #0x9a3c9c
    // 0x9a3c48: ldur            x1, [fp, #-0x20]
    // 0x9a3c4c: LoadField: r0 = r1->field_3f
    //     0x9a3c4c: ldur            w0, [x1, #0x3f]
    // 0x9a3c50: DecompressPointer r0
    //     0x9a3c50: add             x0, x0, HEAP, lsl #32
    // 0x9a3c54: r16 = Sentinel
    //     0x9a3c54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a3c58: cmp             w0, w16
    // 0x9a3c5c: b.ne            #0x9a3c6c
    // 0x9a3c60: r2 = _theme
    //     0x9a3c60: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d3a0] Field <_ExpansionTileDefaultsM2@543392950._theme@543392950>: late final (offset: 0x40)
    //     0x9a3c64: ldr             x2, [x2, #0x3a0]
    // 0x9a3c68: r0 = InitLateFinalInstanceField()
    //     0x9a3c68: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9a3c6c: LoadField: r1 = r0->field_8f
    //     0x9a3c6c: ldur            w1, [x0, #0x8f]
    // 0x9a3c70: DecompressPointer r1
    //     0x9a3c70: add             x1, x1, HEAP, lsl #32
    // 0x9a3c74: LoadField: r0 = r1->field_23
    //     0x9a3c74: ldur            w0, [x1, #0x23]
    // 0x9a3c78: DecompressPointer r0
    //     0x9a3c78: add             x0, x0, HEAP, lsl #32
    // 0x9a3c7c: cmp             w0, NULL
    // 0x9a3c80: b.eq            #0x9a3e2c
    // 0x9a3c84: LoadField: r1 = r0->field_b
    //     0x9a3c84: ldur            w1, [x0, #0xb]
    // 0x9a3c88: DecompressPointer r1
    //     0x9a3c88: add             x1, x1, HEAP, lsl #32
    // 0x9a3c8c: mov             x6, x1
    // 0x9a3c90: ldur            x3, [fp, #-0x18]
    // 0x9a3c94: ldur            x4, [fp, #-0x10]
    // 0x9a3c98: ldur            x5, [fp, #-8]
    // 0x9a3c9c: stur            x6, [fp, #-0x30]
    // 0x9a3ca0: LoadField: r7 = r4->field_7
    //     0x9a3ca0: ldur            w7, [x4, #7]
    // 0x9a3ca4: DecompressPointer r7
    //     0x9a3ca4: add             x7, x7, HEAP, lsl #32
    // 0x9a3ca8: mov             x0, x6
    // 0x9a3cac: mov             x2, x7
    // 0x9a3cb0: stur            x7, [fp, #-0x28]
    // 0x9a3cb4: r1 = Null
    //     0x9a3cb4: mov             x1, NULL
    // 0x9a3cb8: cmp             w0, NULL
    // 0x9a3cbc: b.eq            #0x9a3ce4
    // 0x9a3cc0: cmp             w2, NULL
    // 0x9a3cc4: b.eq            #0x9a3ce4
    // 0x9a3cc8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a3cc8: ldur            w4, [x2, #0x17]
    // 0x9a3ccc: DecompressPointer r4
    //     0x9a3ccc: add             x4, x4, HEAP, lsl #32
    // 0x9a3cd0: r8 = X0?
    //     0x9a3cd0: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9a3cd4: LoadField: r9 = r4->field_7
    //     0x9a3cd4: ldur            x9, [x4, #7]
    // 0x9a3cd8: r3 = Null
    //     0x9a3cd8: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d7c8] Null
    //     0x9a3cdc: ldr             x3, [x3, #0x7c8]
    // 0x9a3ce0: blr             x9
    // 0x9a3ce4: ldur            x0, [fp, #-0x30]
    // 0x9a3ce8: ldur            x2, [fp, #-0x10]
    // 0x9a3cec: StoreField: r2->field_b = r0
    //     0x9a3cec: stur            w0, [x2, #0xb]
    //     0x9a3cf0: ldurb           w16, [x2, #-1]
    //     0x9a3cf4: ldurb           w17, [x0, #-1]
    //     0x9a3cf8: and             x16, x17, x16, lsr #2
    //     0x9a3cfc: tst             x16, HEAP, lsr #32
    //     0x9a3d00: b.eq            #0x9a3d08
    //     0x9a3d04: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9a3d08: ldur            x0, [fp, #-0x18]
    // 0x9a3d0c: LoadField: r1 = r0->field_b
    //     0x9a3d0c: ldur            w1, [x0, #0xb]
    // 0x9a3d10: DecompressPointer r1
    //     0x9a3d10: add             x1, x1, HEAP, lsl #32
    // 0x9a3d14: cmp             w1, NULL
    // 0x9a3d18: b.eq            #0x9a3e30
    // 0x9a3d1c: ldur            x0, [fp, #-8]
    // 0x9a3d20: cmp             x0, #0xf6f
    // 0x9a3d24: b.ne            #0x9a3d40
    // 0x9a3d28: ldur            x1, [fp, #-0x20]
    // 0x9a3d2c: LoadField: r0 = r1->field_23
    //     0x9a3d2c: ldur            w0, [x1, #0x23]
    // 0x9a3d30: DecompressPointer r0
    //     0x9a3d30: add             x0, x0, HEAP, lsl #32
    // 0x9a3d34: mov             x4, x0
    // 0x9a3d38: mov             x3, x2
    // 0x9a3d3c: b               #0x9a3db0
    // 0x9a3d40: ldur            x1, [fp, #-0x20]
    // 0x9a3d44: cmp             x0, #0xf70
    // 0x9a3d48: b.ne            #0x9a3d80
    // 0x9a3d4c: LoadField: r0 = r1->field_43
    //     0x9a3d4c: ldur            w0, [x1, #0x43]
    // 0x9a3d50: DecompressPointer r0
    //     0x9a3d50: add             x0, x0, HEAP, lsl #32
    // 0x9a3d54: r16 = Sentinel
    //     0x9a3d54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a3d58: cmp             w0, w16
    // 0x9a3d5c: b.ne            #0x9a3d6c
    // 0x9a3d60: r2 = _colors
    //     0x9a3d60: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d390] Field <_ExpansionTileDefaultsM3@543392950._colors@543392950>: late final (offset: 0x44)
    //     0x9a3d64: ldr             x2, [x2, #0x390]
    // 0x9a3d68: r0 = InitLateFinalInstanceField()
    //     0x9a3d68: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9a3d6c: LoadField: r1 = r0->field_7f
    //     0x9a3d6c: ldur            w1, [x0, #0x7f]
    // 0x9a3d70: DecompressPointer r1
    //     0x9a3d70: add             x1, x1, HEAP, lsl #32
    // 0x9a3d74: mov             x4, x1
    // 0x9a3d78: ldur            x3, [fp, #-0x10]
    // 0x9a3d7c: b               #0x9a3db0
    // 0x9a3d80: LoadField: r0 = r1->field_43
    //     0x9a3d80: ldur            w0, [x1, #0x43]
    // 0x9a3d84: DecompressPointer r0
    //     0x9a3d84: add             x0, x0, HEAP, lsl #32
    // 0x9a3d88: r16 = Sentinel
    //     0x9a3d88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a3d8c: cmp             w0, w16
    // 0x9a3d90: b.ne            #0x9a3da0
    // 0x9a3d94: r2 = _colorScheme
    //     0x9a3d94: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d398] Field <_ExpansionTileDefaultsM2@543392950._colorScheme@543392950>: late final (offset: 0x44)
    //     0x9a3d98: ldr             x2, [x2, #0x398]
    // 0x9a3d9c: r0 = InitLateFinalInstanceField()
    //     0x9a3d9c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9a3da0: LoadField: r1 = r0->field_b
    //     0x9a3da0: ldur            w1, [x0, #0xb]
    // 0x9a3da4: DecompressPointer r1
    //     0x9a3da4: add             x1, x1, HEAP, lsl #32
    // 0x9a3da8: mov             x4, x1
    // 0x9a3dac: ldur            x3, [fp, #-0x10]
    // 0x9a3db0: mov             x0, x4
    // 0x9a3db4: ldur            x2, [fp, #-0x28]
    // 0x9a3db8: stur            x4, [fp, #-0x18]
    // 0x9a3dbc: r1 = Null
    //     0x9a3dbc: mov             x1, NULL
    // 0x9a3dc0: cmp             w0, NULL
    // 0x9a3dc4: b.eq            #0x9a3dec
    // 0x9a3dc8: cmp             w2, NULL
    // 0x9a3dcc: b.eq            #0x9a3dec
    // 0x9a3dd0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a3dd0: ldur            w4, [x2, #0x17]
    // 0x9a3dd4: DecompressPointer r4
    //     0x9a3dd4: add             x4, x4, HEAP, lsl #32
    // 0x9a3dd8: r8 = X0?
    //     0x9a3dd8: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9a3ddc: LoadField: r9 = r4->field_7
    //     0x9a3ddc: ldur            x9, [x4, #7]
    // 0x9a3de0: r3 = Null
    //     0x9a3de0: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d7d8] Null
    //     0x9a3de4: ldr             x3, [x3, #0x7d8]
    // 0x9a3de8: blr             x9
    // 0x9a3dec: ldur            x0, [fp, #-0x18]
    // 0x9a3df0: ldur            x1, [fp, #-0x10]
    // 0x9a3df4: StoreField: r1->field_f = r0
    //     0x9a3df4: stur            w0, [x1, #0xf]
    //     0x9a3df8: ldurb           w16, [x1, #-1]
    //     0x9a3dfc: ldurb           w17, [x0, #-1]
    //     0x9a3e00: and             x16, x17, x16, lsr #2
    //     0x9a3e04: tst             x16, HEAP, lsr #32
    //     0x9a3e08: b.eq            #0x9a3e10
    //     0x9a3e0c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9a3e10: r0 = Null
    //     0x9a3e10: mov             x0, NULL
    // 0x9a3e14: LeaveFrame
    //     0x9a3e14: mov             SP, fp
    //     0x9a3e18: ldp             fp, lr, [SP], #0x10
    // 0x9a3e1c: ret
    //     0x9a3e1c: ret             
    // 0x9a3e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a3e20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a3e24: b               #0x9a3bb8
    // 0x9a3e28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a3e28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a3e2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a3e2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a3e30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a3e30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateShapeBorder(/* No info */) {
    // ** addr: 0x9a3e34, size: 0x194
    // 0x9a3e34: EnterFrame
    //     0x9a3e34: stp             fp, lr, [SP, #-0x10]!
    //     0x9a3e38: mov             fp, SP
    // 0x9a3e3c: AllocStack(0x28)
    //     0x9a3e3c: sub             SP, SP, #0x28
    // 0x9a3e40: r4 = Instance_Border
    //     0x9a3e40: add             x4, PP, #0x5d, lsl #12  ; [pp+0x5d6f8] Obj!Border@e145f1
    //     0x9a3e44: ldr             x4, [x4, #0x6f8]
    // 0x9a3e48: stur            x3, [fp, #-0x18]
    // 0x9a3e4c: LoadField: r5 = r1->field_1b
    //     0x9a3e4c: ldur            w5, [x1, #0x1b]
    // 0x9a3e50: DecompressPointer r5
    //     0x9a3e50: add             x5, x5, HEAP, lsl #32
    // 0x9a3e54: stur            x5, [fp, #-0x10]
    // 0x9a3e58: LoadField: r0 = r1->field_b
    //     0x9a3e58: ldur            w0, [x1, #0xb]
    // 0x9a3e5c: DecompressPointer r0
    //     0x9a3e5c: add             x0, x0, HEAP, lsl #32
    // 0x9a3e60: cmp             w0, NULL
    // 0x9a3e64: b.eq            #0x9a3fc4
    // 0x9a3e68: LoadField: r6 = r5->field_7
    //     0x9a3e68: ldur            w6, [x5, #7]
    // 0x9a3e6c: DecompressPointer r6
    //     0x9a3e6c: add             x6, x6, HEAP, lsl #32
    // 0x9a3e70: mov             x0, x4
    // 0x9a3e74: mov             x2, x6
    // 0x9a3e78: stur            x6, [fp, #-8]
    // 0x9a3e7c: r1 = Null
    //     0x9a3e7c: mov             x1, NULL
    // 0x9a3e80: cmp             w0, NULL
    // 0x9a3e84: b.eq            #0x9a3eac
    // 0x9a3e88: cmp             w2, NULL
    // 0x9a3e8c: b.eq            #0x9a3eac
    // 0x9a3e90: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a3e90: ldur            w4, [x2, #0x17]
    // 0x9a3e94: DecompressPointer r4
    //     0x9a3e94: add             x4, x4, HEAP, lsl #32
    // 0x9a3e98: r8 = X0?
    //     0x9a3e98: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9a3e9c: LoadField: r9 = r4->field_7
    //     0x9a3e9c: ldur            x9, [x4, #7]
    // 0x9a3ea0: r3 = Null
    //     0x9a3ea0: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d7e8] Null
    //     0x9a3ea4: ldr             x3, [x3, #0x7e8]
    // 0x9a3ea8: blr             x9
    // 0x9a3eac: ldur            x1, [fp, #-0x10]
    // 0x9a3eb0: r0 = Instance_Border
    //     0x9a3eb0: add             x0, PP, #0x5d, lsl #12  ; [pp+0x5d6f8] Obj!Border@e145f1
    //     0x9a3eb4: ldr             x0, [x0, #0x6f8]
    // 0x9a3eb8: StoreField: r1->field_b = r0
    //     0x9a3eb8: stur            w0, [x1, #0xb]
    // 0x9a3ebc: ldur            x0, [fp, #-0x18]
    // 0x9a3ec0: LoadField: r2 = r0->field_4b
    //     0x9a3ec0: ldur            w2, [x0, #0x4b]
    // 0x9a3ec4: DecompressPointer r2
    //     0x9a3ec4: add             x2, x2, HEAP, lsl #32
    // 0x9a3ec8: stur            x2, [fp, #-0x20]
    // 0x9a3ecc: r0 = BorderSide()
    //     0x9a3ecc: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x9a3ed0: mov             x1, x0
    // 0x9a3ed4: ldur            x0, [fp, #-0x20]
    // 0x9a3ed8: stur            x1, [fp, #-0x18]
    // 0x9a3edc: StoreField: r1->field_7 = r0
    //     0x9a3edc: stur            w0, [x1, #7]
    // 0x9a3ee0: d0 = 1.000000
    //     0x9a3ee0: fmov            d0, #1.00000000
    // 0x9a3ee4: StoreField: r1->field_b = d0
    //     0x9a3ee4: stur            d0, [x1, #0xb]
    // 0x9a3ee8: r2 = Instance_BorderStyle
    //     0x9a3ee8: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0x9a3eec: ldr             x2, [x2, #0x2d0]
    // 0x9a3ef0: StoreField: r1->field_13 = r2
    //     0x9a3ef0: stur            w2, [x1, #0x13]
    // 0x9a3ef4: d1 = -1.000000
    //     0x9a3ef4: fmov            d1, #-1.00000000
    // 0x9a3ef8: ArrayStore: r1[0] = d1  ; List_8
    //     0x9a3ef8: stur            d1, [x1, #0x17]
    // 0x9a3efc: r0 = BorderSide()
    //     0x9a3efc: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x9a3f00: mov             x1, x0
    // 0x9a3f04: ldur            x0, [fp, #-0x20]
    // 0x9a3f08: stur            x1, [fp, #-0x28]
    // 0x9a3f0c: StoreField: r1->field_7 = r0
    //     0x9a3f0c: stur            w0, [x1, #7]
    // 0x9a3f10: d0 = 1.000000
    //     0x9a3f10: fmov            d0, #1.00000000
    // 0x9a3f14: StoreField: r1->field_b = d0
    //     0x9a3f14: stur            d0, [x1, #0xb]
    // 0x9a3f18: r0 = Instance_BorderStyle
    //     0x9a3f18: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0x9a3f1c: ldr             x0, [x0, #0x2d0]
    // 0x9a3f20: StoreField: r1->field_13 = r0
    //     0x9a3f20: stur            w0, [x1, #0x13]
    // 0x9a3f24: d0 = -1.000000
    //     0x9a3f24: fmov            d0, #-1.00000000
    // 0x9a3f28: ArrayStore: r1[0] = d0  ; List_8
    //     0x9a3f28: stur            d0, [x1, #0x17]
    // 0x9a3f2c: r0 = Border()
    //     0x9a3f2c: bl              #0x87dce8  ; AllocateBorderStub -> Border (size=0x18)
    // 0x9a3f30: mov             x3, x0
    // 0x9a3f34: ldur            x0, [fp, #-0x18]
    // 0x9a3f38: stur            x3, [fp, #-0x20]
    // 0x9a3f3c: StoreField: r3->field_7 = r0
    //     0x9a3f3c: stur            w0, [x3, #7]
    // 0x9a3f40: r0 = Instance_BorderSide
    //     0x9a3f40: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca0] Obj!BorderSide@e1c111
    //     0x9a3f44: ldr             x0, [x0, #0xca0]
    // 0x9a3f48: StoreField: r3->field_b = r0
    //     0x9a3f48: stur            w0, [x3, #0xb]
    // 0x9a3f4c: ldur            x1, [fp, #-0x28]
    // 0x9a3f50: StoreField: r3->field_f = r1
    //     0x9a3f50: stur            w1, [x3, #0xf]
    // 0x9a3f54: StoreField: r3->field_13 = r0
    //     0x9a3f54: stur            w0, [x3, #0x13]
    // 0x9a3f58: mov             x0, x3
    // 0x9a3f5c: ldur            x2, [fp, #-8]
    // 0x9a3f60: r1 = Null
    //     0x9a3f60: mov             x1, NULL
    // 0x9a3f64: cmp             w0, NULL
    // 0x9a3f68: b.eq            #0x9a3f90
    // 0x9a3f6c: cmp             w2, NULL
    // 0x9a3f70: b.eq            #0x9a3f90
    // 0x9a3f74: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a3f74: ldur            w4, [x2, #0x17]
    // 0x9a3f78: DecompressPointer r4
    //     0x9a3f78: add             x4, x4, HEAP, lsl #32
    // 0x9a3f7c: r8 = X0?
    //     0x9a3f7c: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9a3f80: LoadField: r9 = r4->field_7
    //     0x9a3f80: ldur            x9, [x4, #7]
    // 0x9a3f84: r3 = Null
    //     0x9a3f84: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d7f8] Null
    //     0x9a3f88: ldr             x3, [x3, #0x7f8]
    // 0x9a3f8c: blr             x9
    // 0x9a3f90: ldur            x0, [fp, #-0x20]
    // 0x9a3f94: ldur            x1, [fp, #-0x10]
    // 0x9a3f98: StoreField: r1->field_f = r0
    //     0x9a3f98: stur            w0, [x1, #0xf]
    //     0x9a3f9c: ldurb           w16, [x1, #-1]
    //     0x9a3fa0: ldurb           w17, [x0, #-1]
    //     0x9a3fa4: and             x16, x17, x16, lsr #2
    //     0x9a3fa8: tst             x16, HEAP, lsr #32
    //     0x9a3fac: b.eq            #0x9a3fb4
    //     0x9a3fb0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9a3fb4: r0 = Null
    //     0x9a3fb4: mov             x0, NULL
    // 0x9a3fb8: LeaveFrame
    //     0x9a3fb8: mov             SP, fp
    //     0x9a3fbc: ldp             fp, lr, [SP], #0x10
    // 0x9a3fc0: ret
    //     0x9a3fc0: ret             
    // 0x9a3fc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a3fc4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateAnimationDuration(/* No info */) {
    // ** addr: 0x9a3fc8, size: 0x58
    // 0x9a3fc8: EnterFrame
    //     0x9a3fc8: stp             fp, lr, [SP, #-0x10]!
    //     0x9a3fcc: mov             fp, SP
    // 0x9a3fd0: r3 = Instance_Duration
    //     0x9a3fd0: add             x3, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0x9a3fd4: ldr             x3, [x3, #0x368]
    // 0x9a3fd8: LoadField: r2 = r1->field_2f
    //     0x9a3fd8: ldur            w2, [x1, #0x2f]
    // 0x9a3fdc: DecompressPointer r2
    //     0x9a3fdc: add             x2, x2, HEAP, lsl #32
    // 0x9a3fe0: r16 = Sentinel
    //     0x9a3fe0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a3fe4: cmp             w2, w16
    // 0x9a3fe8: b.eq            #0x9a4010
    // 0x9a3fec: LoadField: r4 = r1->field_b
    //     0x9a3fec: ldur            w4, [x1, #0xb]
    // 0x9a3ff0: DecompressPointer r4
    //     0x9a3ff0: add             x4, x4, HEAP, lsl #32
    // 0x9a3ff4: cmp             w4, NULL
    // 0x9a3ff8: b.eq            #0x9a401c
    // 0x9a3ffc: StoreField: r2->field_27 = r3
    //     0x9a3ffc: stur            w3, [x2, #0x27]
    // 0x9a4000: r0 = Null
    //     0x9a4000: mov             x0, NULL
    // 0x9a4004: LeaveFrame
    //     0x9a4004: mov             SP, fp
    //     0x9a4008: ldp             fp, lr, [SP], #0x10
    // 0x9a400c: ret
    //     0x9a400c: ret             
    // 0x9a4010: r9 = _animationController
    //     0x9a4010: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d6f0] Field <_ExpansionTileState@543392950._animationController@543392950>: late (offset: 0x30)
    //     0x9a4014: ldr             x9, [x9, #0x6f0]
    // 0x9a4018: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a4018: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a401c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a401c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0x9f0084, size: 0x214
    // 0x9f0084: EnterFrame
    //     0x9f0084: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0088: mov             fp, SP
    // 0x9f008c: AllocStack(0x30)
    //     0x9f008c: sub             SP, SP, #0x30
    // 0x9f0090: SetupParameters(_ExpansionTileState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0x9f0090: stur            x1, [fp, #-8]
    //     0x9f0094: mov             x16, x2
    //     0x9f0098: mov             x2, x1
    //     0x9f009c: mov             x1, x16
    // 0x9f00a0: CheckStackOverflow
    //     0x9f00a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f00a4: cmp             SP, x16
    //     0x9f00a8: b.ls            #0x9f0268
    // 0x9f00ac: r0 = of()
    //     0x9f00ac: bl              #0x984930  ; [package:flutter/src/material/expansion_tile_theme.dart] ExpansionTileTheme::of
    // 0x9f00b0: ldur            x2, [fp, #-8]
    // 0x9f00b4: LoadField: r0 = r2->field_4b
    //     0x9f00b4: ldur            w0, [x2, #0x4b]
    // 0x9f00b8: DecompressPointer r0
    //     0x9f00b8: add             x0, x0, HEAP, lsl #32
    // 0x9f00bc: tbz             w0, #4, #0x9f0100
    // 0x9f00c0: LoadField: r0 = r2->field_2f
    //     0x9f00c0: ldur            w0, [x2, #0x2f]
    // 0x9f00c4: DecompressPointer r0
    //     0x9f00c4: add             x0, x0, HEAP, lsl #32
    // 0x9f00c8: r16 = Sentinel
    //     0x9f00c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9f00cc: cmp             w0, w16
    // 0x9f00d0: b.eq            #0x9f0270
    // 0x9f00d4: LoadField: r1 = r0->field_43
    //     0x9f00d4: ldur            w1, [x0, #0x43]
    // 0x9f00d8: DecompressPointer r1
    //     0x9f00d8: add             x1, x1, HEAP, lsl #32
    // 0x9f00dc: r16 = Sentinel
    //     0x9f00dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9f00e0: cmp             w1, w16
    // 0x9f00e4: b.eq            #0x9f027c
    // 0x9f00e8: r16 = Instance_AnimationStatus
    //     0x9f00e8: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0x9f00ec: cmp             w1, w16
    // 0x9f00f0: r16 = true
    //     0x9f00f0: add             x16, NULL, #0x20  ; true
    // 0x9f00f4: r17 = false
    //     0x9f00f4: add             x17, NULL, #0x30  ; false
    // 0x9f00f8: csel            x0, x16, x17, eq
    // 0x9f00fc: b               #0x9f0104
    // 0x9f0100: r0 = false
    //     0x9f0100: add             x0, NULL, #0x30  ; false
    // 0x9f0104: stur            x0, [fp, #-0x28]
    // 0x9f0108: tbnz            w0, #4, #0x9f0124
    // 0x9f010c: LoadField: r1 = r2->field_b
    //     0x9f010c: ldur            w1, [x2, #0xb]
    // 0x9f0110: DecompressPointer r1
    //     0x9f0110: add             x1, x1, HEAP, lsl #32
    // 0x9f0114: cmp             w1, NULL
    // 0x9f0118: b.eq            #0x9f0284
    // 0x9f011c: r1 = true
    //     0x9f011c: add             x1, NULL, #0x20  ; true
    // 0x9f0120: b               #0x9f0128
    // 0x9f0124: r1 = false
    //     0x9f0124: add             x1, NULL, #0x30  ; false
    // 0x9f0128: stur            x1, [fp, #-0x20]
    // 0x9f012c: eor             x3, x0, #0x10
    // 0x9f0130: stur            x3, [fp, #-0x18]
    // 0x9f0134: LoadField: r4 = r2->field_b
    //     0x9f0134: ldur            w4, [x2, #0xb]
    // 0x9f0138: DecompressPointer r4
    //     0x9f0138: add             x4, x4, HEAP, lsl #32
    // 0x9f013c: cmp             w4, NULL
    // 0x9f0140: b.eq            #0x9f0288
    // 0x9f0144: LoadField: r5 = r4->field_1b
    //     0x9f0144: ldur            w5, [x4, #0x1b]
    // 0x9f0148: DecompressPointer r5
    //     0x9f0148: add             x5, x5, HEAP, lsl #32
    // 0x9f014c: stur            x5, [fp, #-0x10]
    // 0x9f0150: r0 = Column()
    //     0x9f0150: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9f0154: mov             x1, x0
    // 0x9f0158: r0 = Instance_Axis
    //     0x9f0158: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x9f015c: stur            x1, [fp, #-0x30]
    // 0x9f0160: StoreField: r1->field_f = r0
    //     0x9f0160: stur            w0, [x1, #0xf]
    // 0x9f0164: r0 = Instance_MainAxisAlignment
    //     0x9f0164: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0x9f0168: ldr             x0, [x0, #0x730]
    // 0x9f016c: StoreField: r1->field_13 = r0
    //     0x9f016c: stur            w0, [x1, #0x13]
    // 0x9f0170: r0 = Instance_MainAxisSize
    //     0x9f0170: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0x9f0174: ldr             x0, [x0, #0x738]
    // 0x9f0178: ArrayStore: r1[0] = r0  ; List_4
    //     0x9f0178: stur            w0, [x1, #0x17]
    // 0x9f017c: r0 = Instance_CrossAxisAlignment
    //     0x9f017c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0x9f0180: ldr             x0, [x0, #0x740]
    // 0x9f0184: StoreField: r1->field_1b = r0
    //     0x9f0184: stur            w0, [x1, #0x1b]
    // 0x9f0188: r0 = Instance_VerticalDirection
    //     0x9f0188: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0x9f018c: ldr             x0, [x0, #0x748]
    // 0x9f0190: StoreField: r1->field_23 = r0
    //     0x9f0190: stur            w0, [x1, #0x23]
    // 0x9f0194: r0 = Instance_Clip
    //     0x9f0194: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x9f0198: ldr             x0, [x0, #0x750]
    // 0x9f019c: StoreField: r1->field_2b = r0
    //     0x9f019c: stur            w0, [x1, #0x2b]
    // 0x9f01a0: StoreField: r1->field_2f = rZR
    //     0x9f01a0: stur            xzr, [x1, #0x2f]
    // 0x9f01a4: ldur            x0, [fp, #-0x10]
    // 0x9f01a8: StoreField: r1->field_b = r0
    //     0x9f01a8: stur            w0, [x1, #0xb]
    // 0x9f01ac: r0 = Padding()
    //     0x9f01ac: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9f01b0: mov             x1, x0
    // 0x9f01b4: r0 = Instance_EdgeInsets
    //     0x9f01b4: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0x9f01b8: stur            x1, [fp, #-0x10]
    // 0x9f01bc: StoreField: r1->field_f = r0
    //     0x9f01bc: stur            w0, [x1, #0xf]
    // 0x9f01c0: ldur            x0, [fp, #-0x30]
    // 0x9f01c4: StoreField: r1->field_b = r0
    //     0x9f01c4: stur            w0, [x1, #0xb]
    // 0x9f01c8: r0 = TickerMode()
    //     0x9f01c8: bl              #0x9f02a4  ; AllocateTickerModeStub -> TickerMode (size=0x14)
    // 0x9f01cc: mov             x1, x0
    // 0x9f01d0: ldur            x0, [fp, #-0x18]
    // 0x9f01d4: stur            x1, [fp, #-0x30]
    // 0x9f01d8: StoreField: r1->field_b = r0
    //     0x9f01d8: stur            w0, [x1, #0xb]
    // 0x9f01dc: ldur            x0, [fp, #-0x10]
    // 0x9f01e0: StoreField: r1->field_f = r0
    //     0x9f01e0: stur            w0, [x1, #0xf]
    // 0x9f01e4: r0 = Offstage()
    //     0x9f01e4: bl              #0x9f0298  ; AllocateOffstageStub -> Offstage (size=0x14)
    // 0x9f01e8: mov             x1, x0
    // 0x9f01ec: ldur            x0, [fp, #-0x28]
    // 0x9f01f0: StoreField: r1->field_f = r0
    //     0x9f01f0: stur            w0, [x1, #0xf]
    // 0x9f01f4: ldur            x0, [fp, #-0x30]
    // 0x9f01f8: StoreField: r1->field_b = r0
    //     0x9f01f8: stur            w0, [x1, #0xb]
    // 0x9f01fc: ldur            x2, [fp, #-8]
    // 0x9f0200: LoadField: r0 = r2->field_2f
    //     0x9f0200: ldur            w0, [x2, #0x2f]
    // 0x9f0204: DecompressPointer r0
    //     0x9f0204: add             x0, x0, HEAP, lsl #32
    // 0x9f0208: r16 = Sentinel
    //     0x9f0208: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9f020c: cmp             w0, w16
    // 0x9f0210: b.eq            #0x9f028c
    // 0x9f0214: ldur            x3, [fp, #-0x20]
    // 0x9f0218: stur            x0, [fp, #-0x18]
    // 0x9f021c: tbnz            w3, #4, #0x9f0228
    // 0x9f0220: r3 = Null
    //     0x9f0220: mov             x3, NULL
    // 0x9f0224: b               #0x9f022c
    // 0x9f0228: mov             x3, x1
    // 0x9f022c: stur            x3, [fp, #-0x10]
    // 0x9f0230: r1 = Function '_buildChildren@543392950':.
    //     0x9f0230: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d6e8] AnonymousClosure: (0x9f02b0), in [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_buildChildren (0x9f02f0)
    //     0x9f0234: ldr             x1, [x1, #0x6e8]
    // 0x9f0238: r0 = AllocateClosure()
    //     0x9f0238: bl              #0xec1630  ; AllocateClosureStub
    // 0x9f023c: stur            x0, [fp, #-8]
    // 0x9f0240: r0 = AnimatedBuilder()
    //     0x9f0240: bl              #0x7e5888  ; AllocateAnimatedBuilderStub -> AnimatedBuilder (size=0x18)
    // 0x9f0244: ldur            x1, [fp, #-8]
    // 0x9f0248: StoreField: r0->field_f = r1
    //     0x9f0248: stur            w1, [x0, #0xf]
    // 0x9f024c: ldur            x1, [fp, #-0x10]
    // 0x9f0250: StoreField: r0->field_13 = r1
    //     0x9f0250: stur            w1, [x0, #0x13]
    // 0x9f0254: ldur            x1, [fp, #-0x18]
    // 0x9f0258: StoreField: r0->field_b = r1
    //     0x9f0258: stur            w1, [x0, #0xb]
    // 0x9f025c: LeaveFrame
    //     0x9f025c: mov             SP, fp
    //     0x9f0260: ldp             fp, lr, [SP], #0x10
    // 0x9f0264: ret
    //     0x9f0264: ret             
    // 0x9f0268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0268: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f026c: b               #0x9f00ac
    // 0x9f0270: r9 = _animationController
    //     0x9f0270: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d6f0] Field <_ExpansionTileState@543392950._animationController@543392950>: late (offset: 0x30)
    //     0x9f0274: ldr             x9, [x9, #0x6f0]
    // 0x9f0278: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9f0278: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9f027c: r9 = _status
    //     0x9f027c: ldr             x9, [PP, #0x4ed8]  ; [pp+0x4ed8] Field <AnimationController._status@441066280>: late (offset: 0x44)
    // 0x9f0280: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9f0280: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9f0284: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f0284: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f0288: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f0288: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f028c: r9 = _animationController
    //     0x9f028c: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d6f0] Field <_ExpansionTileState@543392950._animationController@543392950>: late (offset: 0x30)
    //     0x9f0290: ldr             x9, [x9, #0x6f0]
    // 0x9f0294: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9f0294: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Widget _buildChildren(dynamic, BuildContext, Widget?) {
    // ** addr: 0x9f02b0, size: 0x40
    // 0x9f02b0: EnterFrame
    //     0x9f02b0: stp             fp, lr, [SP, #-0x10]!
    //     0x9f02b4: mov             fp, SP
    // 0x9f02b8: ldr             x0, [fp, #0x20]
    // 0x9f02bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9f02bc: ldur            w1, [x0, #0x17]
    // 0x9f02c0: DecompressPointer r1
    //     0x9f02c0: add             x1, x1, HEAP, lsl #32
    // 0x9f02c4: CheckStackOverflow
    //     0x9f02c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f02c8: cmp             SP, x16
    //     0x9f02cc: b.ls            #0x9f02e8
    // 0x9f02d0: ldr             x2, [fp, #0x18]
    // 0x9f02d4: ldr             x3, [fp, #0x10]
    // 0x9f02d8: r0 = _buildChildren()
    //     0x9f02d8: bl              #0x9f02f0  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_buildChildren
    // 0x9f02dc: LeaveFrame
    //     0x9f02dc: mov             SP, fp
    //     0x9f02e0: ldp             fp, lr, [SP], #0x10
    // 0x9f02e4: ret
    //     0x9f02e4: ret             
    // 0x9f02e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f02e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f02ec: b               #0x9f02d0
  }
  _ _buildChildren(/* No info */) {
    // ** addr: 0x9f02f0, size: 0x530
    // 0x9f02f0: EnterFrame
    //     0x9f02f0: stp             fp, lr, [SP, #-0x10]!
    //     0x9f02f4: mov             fp, SP
    // 0x9f02f8: AllocStack(0x78)
    //     0x9f02f8: sub             SP, SP, #0x78
    // 0x9f02fc: SetupParameters(_ExpansionTileState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x9f02fc: mov             x0, x2
    //     0x9f0300: stur            x2, [fp, #-0x10]
    //     0x9f0304: mov             x2, x1
    //     0x9f0308: stur            x1, [fp, #-8]
    //     0x9f030c: stur            x3, [fp, #-0x18]
    // 0x9f0310: CheckStackOverflow
    //     0x9f0310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0314: cmp             SP, x16
    //     0x9f0318: b.ls            #0x9f07c4
    // 0x9f031c: mov             x1, x0
    // 0x9f0320: r0 = of()
    //     0x9f0320: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9f0324: ldur            x1, [fp, #-0x10]
    // 0x9f0328: stur            x0, [fp, #-0x20]
    // 0x9f032c: r0 = of()
    //     0x9f032c: bl              #0x984930  ; [package:flutter/src/material/expansion_tile_theme.dart] ExpansionTileTheme::of
    // 0x9f0330: ldur            x0, [fp, #-8]
    // 0x9f0334: LoadField: r1 = r0->field_47
    //     0x9f0334: ldur            w1, [x0, #0x47]
    // 0x9f0338: DecompressPointer r1
    //     0x9f0338: add             x1, x1, HEAP, lsl #32
    // 0x9f033c: r16 = Sentinel
    //     0x9f033c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9f0340: cmp             w1, w16
    // 0x9f0344: b.eq            #0x9f07cc
    // 0x9f0348: LoadField: r2 = r1->field_f
    //     0x9f0348: ldur            w2, [x1, #0xf]
    // 0x9f034c: DecompressPointer r2
    //     0x9f034c: add             x2, x2, HEAP, lsl #32
    // 0x9f0350: LoadField: r3 = r1->field_b
    //     0x9f0350: ldur            w3, [x1, #0xb]
    // 0x9f0354: DecompressPointer r3
    //     0x9f0354: add             x3, x3, HEAP, lsl #32
    // 0x9f0358: mov             x1, x2
    // 0x9f035c: mov             x2, x3
    // 0x9f0360: r0 = evaluate()
    //     0x9f0360: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9f0364: cmp             w0, NULL
    // 0x9f0368: b.ne            #0x9f0370
    // 0x9f036c: r0 = Null
    //     0x9f036c: mov             x0, NULL
    // 0x9f0370: cmp             w0, NULL
    // 0x9f0374: b.ne            #0x9f0380
    // 0x9f0378: r3 = Instance_Color
    //     0x9f0378: ldr             x3, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0x9f037c: b               #0x9f0384
    // 0x9f0380: mov             x3, x0
    // 0x9f0384: ldur            x0, [fp, #-8]
    // 0x9f0388: stur            x3, [fp, #-0x28]
    // 0x9f038c: LoadField: r1 = r0->field_3b
    //     0x9f038c: ldur            w1, [x0, #0x3b]
    // 0x9f0390: DecompressPointer r1
    //     0x9f0390: add             x1, x1, HEAP, lsl #32
    // 0x9f0394: r16 = Sentinel
    //     0x9f0394: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9f0398: cmp             w1, w16
    // 0x9f039c: b.eq            #0x9f07d8
    // 0x9f03a0: LoadField: r2 = r1->field_f
    //     0x9f03a0: ldur            w2, [x1, #0xf]
    // 0x9f03a4: DecompressPointer r2
    //     0x9f03a4: add             x2, x2, HEAP, lsl #32
    // 0x9f03a8: LoadField: r4 = r1->field_b
    //     0x9f03a8: ldur            w4, [x1, #0xb]
    // 0x9f03ac: DecompressPointer r4
    //     0x9f03ac: add             x4, x4, HEAP, lsl #32
    // 0x9f03b0: mov             x1, x2
    // 0x9f03b4: mov             x2, x4
    // 0x9f03b8: r0 = evaluate()
    //     0x9f03b8: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9f03bc: cmp             w0, NULL
    // 0x9f03c0: b.ne            #0x9f03d0
    // 0x9f03c4: r2 = Instance_Border
    //     0x9f03c4: add             x2, PP, #0x5d, lsl #12  ; [pp+0x5d6f8] Obj!Border@e145f1
    //     0x9f03c8: ldr             x2, [x2, #0x6f8]
    // 0x9f03cc: b               #0x9f03d4
    // 0x9f03d0: mov             x2, x0
    // 0x9f03d4: ldur            x0, [fp, #-8]
    // 0x9f03d8: stur            x2, [fp, #-0x30]
    // 0x9f03dc: LoadField: r1 = r0->field_b
    //     0x9f03dc: ldur            w1, [x0, #0xb]
    // 0x9f03e0: DecompressPointer r1
    //     0x9f03e0: add             x1, x1, HEAP, lsl #32
    // 0x9f03e4: cmp             w1, NULL
    // 0x9f03e8: b.eq            #0x9f07e4
    // 0x9f03ec: ldur            x1, [fp, #-0x10]
    // 0x9f03f0: r0 = of()
    //     0x9f03f0: bl              #0x9179e4  ; [package:flutter/src/material/material_localizations.dart] MaterialLocalizations::of
    // 0x9f03f4: ldur            x1, [fp, #-8]
    // 0x9f03f8: LoadField: r0 = r1->field_4b
    //     0x9f03f8: ldur            w0, [x1, #0x4b]
    // 0x9f03fc: DecompressPointer r0
    //     0x9f03fc: add             x0, x0, HEAP, lsl #32
    // 0x9f0400: tbnz            w0, #4, #0x9f0410
    // 0x9f0404: r3 = "Collapse"
    //     0x9f0404: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d700] "Collapse"
    //     0x9f0408: ldr             x3, [x3, #0x700]
    // 0x9f040c: b               #0x9f0418
    // 0x9f0410: r3 = "Expand for more details"
    //     0x9f0410: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d708] "Expand for more details"
    //     0x9f0414: ldr             x3, [x3, #0x708]
    // 0x9f0418: ldur            x2, [fp, #-0x20]
    // 0x9f041c: stur            x3, [fp, #-0x38]
    // 0x9f0420: LoadField: r4 = r2->field_23
    //     0x9f0420: ldur            w4, [x2, #0x23]
    // 0x9f0424: DecompressPointer r4
    //     0x9f0424: add             x4, x4, HEAP, lsl #32
    // 0x9f0428: LoadField: r2 = r4->field_7
    //     0x9f0428: ldur            x2, [x4, #7]
    // 0x9f042c: cmp             x2, #2
    // 0x9f0430: b.gt            #0x9f0440
    // 0x9f0434: cmp             x2, #1
    // 0x9f0438: b.gt            #0x9f0450
    // 0x9f043c: b               #0x9f0470
    // 0x9f0440: cmp             x2, #4
    // 0x9f0444: b.gt            #0x9f0470
    // 0x9f0448: cmp             x2, #3
    // 0x9f044c: b.le            #0x9f0470
    // 0x9f0450: tbnz            w0, #4, #0x9f0460
    // 0x9f0454: r0 = "Expanded\n double tap to collapse"
    //     0x9f0454: add             x0, PP, #0x5d, lsl #12  ; [pp+0x5d710] "Expanded\n double tap to collapse"
    //     0x9f0458: ldr             x0, [x0, #0x710]
    // 0x9f045c: b               #0x9f0468
    // 0x9f0460: r0 = "Collapsed\n double tap to expand"
    //     0x9f0460: add             x0, PP, #0x5d, lsl #12  ; [pp+0x5d718] "Collapsed\n double tap to expand"
    //     0x9f0464: ldr             x0, [x0, #0x718]
    // 0x9f0468: mov             x4, x0
    // 0x9f046c: b               #0x9f0474
    // 0x9f0470: r4 = Null
    //     0x9f0470: mov             x4, NULL
    // 0x9f0474: ldur            x2, [fp, #-0x28]
    // 0x9f0478: ldur            x0, [fp, #-0x30]
    // 0x9f047c: stur            x4, [fp, #-0x10]
    // 0x9f0480: r0 = ShapeDecoration()
    //     0x9f0480: bl              #0x7f3e70  ; AllocateShapeDecorationStub -> ShapeDecoration (size=0x1c)
    // 0x9f0484: mov             x2, x0
    // 0x9f0488: ldur            x0, [fp, #-0x28]
    // 0x9f048c: stur            x2, [fp, #-0x20]
    // 0x9f0490: StoreField: r2->field_7 = r0
    //     0x9f0490: stur            w0, [x2, #7]
    // 0x9f0494: ldur            x1, [fp, #-0x30]
    // 0x9f0498: ArrayStore: r2[0] = r1  ; List_4
    //     0x9f0498: stur            w1, [x2, #0x17]
    // 0x9f049c: r0 = LoadClassIdInstr(r1)
    //     0x9f049c: ldur            x0, [x1, #-1]
    //     0x9f04a0: ubfx            x0, x0, #0xc, #0x14
    // 0x9f04a4: r0 = GDT[cid_x0 + -0xe1c]()
    //     0x9f04a4: sub             lr, x0, #0xe1c
    //     0x9f04a8: ldr             lr, [x21, lr, lsl #3]
    //     0x9f04ac: blr             lr
    // 0x9f04b0: mov             x3, x0
    // 0x9f04b4: ldur            x0, [fp, #-8]
    // 0x9f04b8: stur            x3, [fp, #-0x28]
    // 0x9f04bc: LoadField: r1 = r0->field_43
    //     0x9f04bc: ldur            w1, [x0, #0x43]
    // 0x9f04c0: DecompressPointer r1
    //     0x9f04c0: add             x1, x1, HEAP, lsl #32
    // 0x9f04c4: r16 = Sentinel
    //     0x9f04c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9f04c8: cmp             w1, w16
    // 0x9f04cc: b.eq            #0x9f07e8
    // 0x9f04d0: LoadField: r2 = r1->field_f
    //     0x9f04d0: ldur            w2, [x1, #0xf]
    // 0x9f04d4: DecompressPointer r2
    //     0x9f04d4: add             x2, x2, HEAP, lsl #32
    // 0x9f04d8: LoadField: r4 = r1->field_b
    //     0x9f04d8: ldur            w4, [x1, #0xb]
    // 0x9f04dc: DecompressPointer r4
    //     0x9f04dc: add             x4, x4, HEAP, lsl #32
    // 0x9f04e0: mov             x1, x2
    // 0x9f04e4: mov             x2, x4
    // 0x9f04e8: r0 = evaluate()
    //     0x9f04e8: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9f04ec: cmp             w0, NULL
    // 0x9f04f0: b.ne            #0x9f04fc
    // 0x9f04f4: r6 = Null
    //     0x9f04f4: mov             x6, NULL
    // 0x9f04f8: b               #0x9f0500
    // 0x9f04fc: mov             x6, x0
    // 0x9f0500: ldur            x0, [fp, #-8]
    // 0x9f0504: ldur            x5, [fp, #-0x18]
    // 0x9f0508: ldur            x4, [fp, #-0x20]
    // 0x9f050c: ldur            x3, [fp, #-0x28]
    // 0x9f0510: stur            x6, [fp, #-0x30]
    // 0x9f0514: LoadField: r1 = r0->field_3f
    //     0x9f0514: ldur            w1, [x0, #0x3f]
    // 0x9f0518: DecompressPointer r1
    //     0x9f0518: add             x1, x1, HEAP, lsl #32
    // 0x9f051c: r16 = Sentinel
    //     0x9f051c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9f0520: cmp             w1, w16
    // 0x9f0524: b.eq            #0x9f07f4
    // 0x9f0528: LoadField: r2 = r1->field_f
    //     0x9f0528: ldur            w2, [x1, #0xf]
    // 0x9f052c: DecompressPointer r2
    //     0x9f052c: add             x2, x2, HEAP, lsl #32
    // 0x9f0530: LoadField: r7 = r1->field_b
    //     0x9f0530: ldur            w7, [x1, #0xb]
    // 0x9f0534: DecompressPointer r7
    //     0x9f0534: add             x7, x7, HEAP, lsl #32
    // 0x9f0538: mov             x1, x2
    // 0x9f053c: mov             x2, x7
    // 0x9f0540: r0 = evaluate()
    //     0x9f0540: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9f0544: mov             x2, x0
    // 0x9f0548: ldur            x0, [fp, #-8]
    // 0x9f054c: stur            x2, [fp, #-0x40]
    // 0x9f0550: LoadField: r1 = r0->field_b
    //     0x9f0550: ldur            w1, [x0, #0xb]
    // 0x9f0554: DecompressPointer r1
    //     0x9f0554: add             x1, x1, HEAP, lsl #32
    // 0x9f0558: cmp             w1, NULL
    // 0x9f055c: b.eq            #0x9f0800
    // 0x9f0560: mov             x1, x0
    // 0x9f0564: r0 = _buildLeadingIcon()
    //     0x9f0564: bl              #0x9f0f18  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_buildLeadingIcon
    // 0x9f0568: mov             x2, x0
    // 0x9f056c: ldur            x0, [fp, #-8]
    // 0x9f0570: stur            x2, [fp, #-0x50]
    // 0x9f0574: LoadField: r1 = r0->field_b
    //     0x9f0574: ldur            w1, [x0, #0xb]
    // 0x9f0578: DecompressPointer r1
    //     0x9f0578: add             x1, x1, HEAP, lsl #32
    // 0x9f057c: cmp             w1, NULL
    // 0x9f0580: b.eq            #0x9f0804
    // 0x9f0584: LoadField: r3 = r1->field_f
    //     0x9f0584: ldur            w3, [x1, #0xf]
    // 0x9f0588: DecompressPointer r3
    //     0x9f0588: add             x3, x3, HEAP, lsl #32
    // 0x9f058c: mov             x1, x0
    // 0x9f0590: stur            x3, [fp, #-0x48]
    // 0x9f0594: r0 = _buildTrailingIcon()
    //     0x9f0594: bl              #0x9f0b40  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_buildTrailingIcon
    // 0x9f0598: ldur            x2, [fp, #-8]
    // 0x9f059c: stur            x0, [fp, #-0x58]
    // 0x9f05a0: LoadField: r1 = r2->field_b
    //     0x9f05a0: ldur            w1, [x2, #0xb]
    // 0x9f05a4: DecompressPointer r1
    //     0x9f05a4: add             x1, x1, HEAP, lsl #32
    // 0x9f05a8: cmp             w1, NULL
    // 0x9f05ac: b.eq            #0x9f0808
    // 0x9f05b0: r0 = ListTile()
    //     0x9f05b0: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0x9f05b4: mov             x3, x0
    // 0x9f05b8: ldur            x0, [fp, #-0x50]
    // 0x9f05bc: stur            x3, [fp, #-0x60]
    // 0x9f05c0: StoreField: r3->field_b = r0
    //     0x9f05c0: stur            w0, [x3, #0xb]
    // 0x9f05c4: ldur            x0, [fp, #-0x48]
    // 0x9f05c8: StoreField: r3->field_f = r0
    //     0x9f05c8: stur            w0, [x3, #0xf]
    // 0x9f05cc: ldur            x0, [fp, #-0x58]
    // 0x9f05d0: ArrayStore: r3[0] = r0  ; List_4
    //     0x9f05d0: stur            w0, [x3, #0x17]
    // 0x9f05d4: r0 = false
    //     0x9f05d4: add             x0, NULL, #0x30  ; false
    // 0x9f05d8: StoreField: r3->field_1b = r0
    //     0x9f05d8: stur            w0, [x3, #0x1b]
    // 0x9f05dc: r4 = true
    //     0x9f05dc: add             x4, NULL, #0x20  ; true
    // 0x9f05e0: StoreField: r3->field_4b = r4
    //     0x9f05e0: stur            w4, [x3, #0x4b]
    // 0x9f05e4: ldur            x2, [fp, #-8]
    // 0x9f05e8: r1 = Function '_handleTap@543392950':.
    //     0x9f05e8: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d720] AnonymousClosure: (0x9f0f7c), of [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState
    //     0x9f05ec: ldr             x1, [x1, #0x720]
    // 0x9f05f0: r0 = AllocateClosure()
    //     0x9f05f0: bl              #0xec1630  ; AllocateClosureStub
    // 0x9f05f4: ldur            x1, [fp, #-0x60]
    // 0x9f05f8: StoreField: r1->field_4f = r0
    //     0x9f05f8: stur            w0, [x1, #0x4f]
    // 0x9f05fc: r0 = false
    //     0x9f05fc: add             x0, NULL, #0x30  ; false
    // 0x9f0600: StoreField: r1->field_5f = r0
    //     0x9f0600: stur            w0, [x1, #0x5f]
    // 0x9f0604: StoreField: r1->field_73 = r0
    //     0x9f0604: stur            w0, [x1, #0x73]
    // 0x9f0608: r2 = true
    //     0x9f0608: add             x2, NULL, #0x20  ; true
    // 0x9f060c: StoreField: r1->field_7f = r2
    //     0x9f060c: stur            w2, [x1, #0x7f]
    // 0x9f0610: StoreField: r1->field_97 = r0
    //     0x9f0610: stur            w0, [x1, #0x97]
    // 0x9f0614: ldur            x2, [fp, #-0x30]
    // 0x9f0618: ldur            x3, [fp, #-0x40]
    // 0x9f061c: r0 = merge()
    //     0x9f061c: bl              #0x9f0820  ; [package:flutter/src/material/list_tile_theme.dart] ListTileTheme::merge
    // 0x9f0620: stur            x0, [fp, #-0x30]
    // 0x9f0624: r0 = Semantics()
    //     0x9f0624: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0x9f0628: stur            x0, [fp, #-0x40]
    // 0x9f062c: ldur            x16, [fp, #-0x10]
    // 0x9f0630: ldur            lr, [fp, #-0x38]
    // 0x9f0634: stp             lr, x16, [SP, #8]
    // 0x9f0638: ldur            x16, [fp, #-0x30]
    // 0x9f063c: str             x16, [SP]
    // 0x9f0640: mov             x1, x0
    // 0x9f0644: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, hint, 0x1, onTapHint, 0x2, null]
    //     0x9f0644: add             x4, PP, #0x5d, lsl #12  ; [pp+0x5d728] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "hint", 0x1, "onTapHint", 0x2, Null]
    //     0x9f0648: ldr             x4, [x4, #0x728]
    // 0x9f064c: r0 = Semantics()
    //     0x9f064c: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0x9f0650: ldur            x0, [fp, #-8]
    // 0x9f0654: LoadField: r1 = r0->field_b
    //     0x9f0654: ldur            w1, [x0, #0xb]
    // 0x9f0658: DecompressPointer r1
    //     0x9f0658: add             x1, x1, HEAP, lsl #32
    // 0x9f065c: cmp             w1, NULL
    // 0x9f0660: b.eq            #0x9f080c
    // 0x9f0664: LoadField: r1 = r0->field_37
    //     0x9f0664: ldur            w1, [x0, #0x37]
    // 0x9f0668: DecompressPointer r1
    //     0x9f0668: add             x1, x1, HEAP, lsl #32
    // 0x9f066c: r16 = Sentinel
    //     0x9f066c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9f0670: cmp             w1, w16
    // 0x9f0674: b.eq            #0x9f0810
    // 0x9f0678: r0 = value()
    //     0x9f0678: bl              #0x6d4444  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::value
    // 0x9f067c: stur            x0, [fp, #-0x10]
    // 0x9f0680: r0 = Align()
    //     0x9f0680: bl              #0x9d4ff8  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x9f0684: mov             x1, x0
    // 0x9f0688: r0 = Instance_Alignment
    //     0x9f0688: add             x0, PP, #0x24, lsl #12  ; [pp+0x24b78] Obj!Alignment@e13ed1
    //     0x9f068c: ldr             x0, [x0, #0xb78]
    // 0x9f0690: stur            x1, [fp, #-0x30]
    // 0x9f0694: StoreField: r1->field_f = r0
    //     0x9f0694: stur            w0, [x1, #0xf]
    // 0x9f0698: ldur            x0, [fp, #-0x10]
    // 0x9f069c: ArrayStore: r1[0] = r0  ; List_4
    //     0x9f069c: stur            w0, [x1, #0x17]
    // 0x9f06a0: ldur            x0, [fp, #-0x18]
    // 0x9f06a4: StoreField: r1->field_b = r0
    //     0x9f06a4: stur            w0, [x1, #0xb]
    // 0x9f06a8: r0 = ClipRect()
    //     0x9f06a8: bl              #0x9e6a74  ; AllocateClipRectStub -> ClipRect (size=0x18)
    // 0x9f06ac: mov             x3, x0
    // 0x9f06b0: r0 = Instance_Clip
    //     0x9f06b0: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0x9f06b4: ldr             x0, [x0, #0x7c0]
    // 0x9f06b8: stur            x3, [fp, #-0x10]
    // 0x9f06bc: StoreField: r3->field_13 = r0
    //     0x9f06bc: stur            w0, [x3, #0x13]
    // 0x9f06c0: ldur            x0, [fp, #-0x30]
    // 0x9f06c4: StoreField: r3->field_b = r0
    //     0x9f06c4: stur            w0, [x3, #0xb]
    // 0x9f06c8: r1 = Null
    //     0x9f06c8: mov             x1, NULL
    // 0x9f06cc: r2 = 4
    //     0x9f06cc: movz            x2, #0x4
    // 0x9f06d0: r0 = AllocateArray()
    //     0x9f06d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x9f06d4: mov             x2, x0
    // 0x9f06d8: ldur            x0, [fp, #-0x40]
    // 0x9f06dc: stur            x2, [fp, #-0x18]
    // 0x9f06e0: StoreField: r2->field_f = r0
    //     0x9f06e0: stur            w0, [x2, #0xf]
    // 0x9f06e4: ldur            x0, [fp, #-0x10]
    // 0x9f06e8: StoreField: r2->field_13 = r0
    //     0x9f06e8: stur            w0, [x2, #0x13]
    // 0x9f06ec: r1 = <Widget>
    //     0x9f06ec: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0x9f06f0: r0 = AllocateGrowableArray()
    //     0x9f06f0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x9f06f4: mov             x1, x0
    // 0x9f06f8: ldur            x0, [fp, #-0x18]
    // 0x9f06fc: stur            x1, [fp, #-0x10]
    // 0x9f0700: StoreField: r1->field_f = r0
    //     0x9f0700: stur            w0, [x1, #0xf]
    // 0x9f0704: r0 = 4
    //     0x9f0704: movz            x0, #0x4
    // 0x9f0708: StoreField: r1->field_b = r0
    //     0x9f0708: stur            w0, [x1, #0xb]
    // 0x9f070c: r0 = Column()
    //     0x9f070c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9f0710: mov             x1, x0
    // 0x9f0714: r0 = Instance_Axis
    //     0x9f0714: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x9f0718: stur            x1, [fp, #-0x18]
    // 0x9f071c: StoreField: r1->field_f = r0
    //     0x9f071c: stur            w0, [x1, #0xf]
    // 0x9f0720: r0 = Instance_MainAxisAlignment
    //     0x9f0720: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0x9f0724: ldr             x0, [x0, #0x730]
    // 0x9f0728: StoreField: r1->field_13 = r0
    //     0x9f0728: stur            w0, [x1, #0x13]
    // 0x9f072c: r0 = Instance_MainAxisSize
    //     0x9f072c: add             x0, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0x9f0730: ldr             x0, [x0, #0xe88]
    // 0x9f0734: ArrayStore: r1[0] = r0  ; List_4
    //     0x9f0734: stur            w0, [x1, #0x17]
    // 0x9f0738: r0 = Instance_CrossAxisAlignment
    //     0x9f0738: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0x9f073c: ldr             x0, [x0, #0x740]
    // 0x9f0740: StoreField: r1->field_1b = r0
    //     0x9f0740: stur            w0, [x1, #0x1b]
    // 0x9f0744: r0 = Instance_VerticalDirection
    //     0x9f0744: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0x9f0748: ldr             x0, [x0, #0x748]
    // 0x9f074c: StoreField: r1->field_23 = r0
    //     0x9f074c: stur            w0, [x1, #0x23]
    // 0x9f0750: r0 = Instance_Clip
    //     0x9f0750: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x9f0754: ldr             x0, [x0, #0x750]
    // 0x9f0758: StoreField: r1->field_2b = r0
    //     0x9f0758: stur            w0, [x1, #0x2b]
    // 0x9f075c: StoreField: r1->field_2f = rZR
    //     0x9f075c: stur            xzr, [x1, #0x2f]
    // 0x9f0760: ldur            x0, [fp, #-0x10]
    // 0x9f0764: StoreField: r1->field_b = r0
    //     0x9f0764: stur            w0, [x1, #0xb]
    // 0x9f0768: r0 = Padding()
    //     0x9f0768: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9f076c: mov             x1, x0
    // 0x9f0770: ldur            x0, [fp, #-0x28]
    // 0x9f0774: stur            x1, [fp, #-0x10]
    // 0x9f0778: StoreField: r1->field_f = r0
    //     0x9f0778: stur            w0, [x1, #0xf]
    // 0x9f077c: ldur            x0, [fp, #-0x18]
    // 0x9f0780: StoreField: r1->field_b = r0
    //     0x9f0780: stur            w0, [x1, #0xb]
    // 0x9f0784: ldur            x0, [fp, #-8]
    // 0x9f0788: LoadField: r2 = r0->field_b
    //     0x9f0788: ldur            w2, [x0, #0xb]
    // 0x9f078c: DecompressPointer r2
    //     0x9f078c: add             x2, x2, HEAP, lsl #32
    // 0x9f0790: cmp             w2, NULL
    // 0x9f0794: b.eq            #0x9f081c
    // 0x9f0798: r0 = DecoratedBox()
    //     0x9f0798: bl              #0x9d4fec  ; AllocateDecoratedBoxStub -> DecoratedBox (size=0x18)
    // 0x9f079c: ldur            x1, [fp, #-0x20]
    // 0x9f07a0: StoreField: r0->field_f = r1
    //     0x9f07a0: stur            w1, [x0, #0xf]
    // 0x9f07a4: r1 = Instance_DecorationPosition
    //     0x9f07a4: add             x1, PP, #0x29, lsl #12  ; [pp+0x29b28] Obj!DecorationPosition@e35881
    //     0x9f07a8: ldr             x1, [x1, #0xb28]
    // 0x9f07ac: StoreField: r0->field_13 = r1
    //     0x9f07ac: stur            w1, [x0, #0x13]
    // 0x9f07b0: ldur            x1, [fp, #-0x10]
    // 0x9f07b4: StoreField: r0->field_b = r1
    //     0x9f07b4: stur            w1, [x0, #0xb]
    // 0x9f07b8: LeaveFrame
    //     0x9f07b8: mov             SP, fp
    //     0x9f07bc: ldp             fp, lr, [SP], #0x10
    // 0x9f07c0: ret
    //     0x9f07c0: ret             
    // 0x9f07c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f07c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f07c8: b               #0x9f031c
    // 0x9f07cc: r9 = _backgroundColor
    //     0x9f07cc: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d730] Field <_ExpansionTileState@543392950._backgroundColor@543392950>: late (offset: 0x48)
    //     0x9f07d0: ldr             x9, [x9, #0x730]
    // 0x9f07d4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9f07d4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9f07d8: r9 = _border
    //     0x9f07d8: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d738] Field <_ExpansionTileState@543392950._border@543392950>: late (offset: 0x3c)
    //     0x9f07dc: ldr             x9, [x9, #0x738]
    // 0x9f07e0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9f07e0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9f07e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f07e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f07e8: r9 = _iconColor
    //     0x9f07e8: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d740] Field <_ExpansionTileState@543392950._iconColor@543392950>: late (offset: 0x44)
    //     0x9f07ec: ldr             x9, [x9, #0x740]
    // 0x9f07f0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9f07f0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9f07f4: r9 = _headerColor
    //     0x9f07f4: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d748] Field <_ExpansionTileState@543392950._headerColor@543392950>: late (offset: 0x40)
    //     0x9f07f8: ldr             x9, [x9, #0x748]
    // 0x9f07fc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9f07fc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9f0800: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f0800: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f0804: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f0804: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f0808: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f0808: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f080c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f080c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f0810: r9 = _heightFactor
    //     0x9f0810: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d750] Field <_ExpansionTileState@543392950._heightFactor@543392950>: late (offset: 0x38)
    //     0x9f0814: ldr             x9, [x9, #0x750]
    // 0x9f0818: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9f0818: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9f081c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f081c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildTrailingIcon(/* No info */) {
    // ** addr: 0x9f0b40, size: 0x64
    // 0x9f0b40: EnterFrame
    //     0x9f0b40: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0b44: mov             fp, SP
    // 0x9f0b48: AllocStack(0x8)
    //     0x9f0b48: sub             SP, SP, #8
    // 0x9f0b4c: SetupParameters(_ExpansionTileState this /* r1 => r0, fp-0x8 */)
    //     0x9f0b4c: mov             x0, x1
    //     0x9f0b50: stur            x1, [fp, #-8]
    // 0x9f0b54: CheckStackOverflow
    //     0x9f0b54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0b58: cmp             SP, x16
    //     0x9f0b5c: b.ls            #0x9f0b9c
    // 0x9f0b60: mov             x1, x0
    // 0x9f0b64: r0 = _effectiveAffinity()
    //     0x9f0b64: bl              #0x9f0eac  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_effectiveAffinity
    // 0x9f0b68: r16 = Instance_ListTileControlAffinity
    //     0x9f0b68: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a058] Obj!ListTileControlAffinity@e366c1
    //     0x9f0b6c: ldr             x16, [x16, #0x58]
    // 0x9f0b70: cmp             w0, w16
    // 0x9f0b74: b.eq            #0x9f0b88
    // 0x9f0b78: r0 = Null
    //     0x9f0b78: mov             x0, NULL
    // 0x9f0b7c: LeaveFrame
    //     0x9f0b7c: mov             SP, fp
    //     0x9f0b80: ldp             fp, lr, [SP], #0x10
    // 0x9f0b84: ret
    //     0x9f0b84: ret             
    // 0x9f0b88: ldur            x1, [fp, #-8]
    // 0x9f0b8c: r0 = _buildIcon()
    //     0x9f0b8c: bl              #0x9f0ba4  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_buildIcon
    // 0x9f0b90: LeaveFrame
    //     0x9f0b90: mov             SP, fp
    //     0x9f0b94: ldp             fp, lr, [SP], #0x10
    // 0x9f0b98: ret
    //     0x9f0b98: ret             
    // 0x9f0b9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0b9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f0ba0: b               #0x9f0b60
  }
  _ _buildIcon(/* No info */) {
    // ** addr: 0x9f0ba4, size: 0x6c
    // 0x9f0ba4: EnterFrame
    //     0x9f0ba4: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0ba8: mov             fp, SP
    // 0x9f0bac: AllocStack(0x8)
    //     0x9f0bac: sub             SP, SP, #8
    // 0x9f0bb0: LoadField: r0 = r1->field_33
    //     0x9f0bb0: ldur            w0, [x1, #0x33]
    // 0x9f0bb4: DecompressPointer r0
    //     0x9f0bb4: add             x0, x0, HEAP, lsl #32
    // 0x9f0bb8: r16 = Sentinel
    //     0x9f0bb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9f0bbc: cmp             w0, w16
    // 0x9f0bc0: b.eq            #0x9f0c04
    // 0x9f0bc4: stur            x0, [fp, #-8]
    // 0x9f0bc8: r0 = RotationTransition()
    //     0x9f0bc8: bl              #0x9f0c10  ; AllocateRotationTransitionStub -> RotationTransition (size=0x20)
    // 0x9f0bcc: r1 = Closure: (double) => Matrix4 from Function '_handleTurnsMatrix@367170175': static.
    //     0x9f0bcc: add             x1, PP, #0x41, lsl #12  ; [pp+0x41580] Closure: (double) => Matrix4 from Function '_handleTurnsMatrix@367170175': static. (0x7e54fb3f0c1c)
    //     0x9f0bd0: ldr             x1, [x1, #0x580]
    // 0x9f0bd4: StoreField: r0->field_f = r1
    //     0x9f0bd4: stur            w1, [x0, #0xf]
    // 0x9f0bd8: r1 = Instance_Alignment
    //     0x9f0bd8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x9f0bdc: ldr             x1, [x1, #0x898]
    // 0x9f0be0: StoreField: r0->field_13 = r1
    //     0x9f0be0: stur            w1, [x0, #0x13]
    // 0x9f0be4: r1 = Instance_Icon
    //     0x9f0be4: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d780] Obj!Icon@e23ff1
    //     0x9f0be8: ldr             x1, [x1, #0x780]
    // 0x9f0bec: StoreField: r0->field_1b = r1
    //     0x9f0bec: stur            w1, [x0, #0x1b]
    // 0x9f0bf0: ldur            x1, [fp, #-8]
    // 0x9f0bf4: StoreField: r0->field_b = r1
    //     0x9f0bf4: stur            w1, [x0, #0xb]
    // 0x9f0bf8: LeaveFrame
    //     0x9f0bf8: mov             SP, fp
    //     0x9f0bfc: ldp             fp, lr, [SP], #0x10
    // 0x9f0c00: ret
    //     0x9f0c00: ret             
    // 0x9f0c04: r9 = _iconTurns
    //     0x9f0c04: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d788] Field <_ExpansionTileState@543392950._iconTurns@543392950>: late (offset: 0x34)
    //     0x9f0c08: ldr             x9, [x9, #0x788]
    // 0x9f0c0c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9f0c0c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _effectiveAffinity(/* No info */) {
    // ** addr: 0x9f0eac, size: 0x6c
    // 0x9f0eac: EnterFrame
    //     0x9f0eac: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0eb0: mov             fp, SP
    // 0x9f0eb4: AllocStack(0x8)
    //     0x9f0eb4: sub             SP, SP, #8
    // 0x9f0eb8: SetupParameters(_ExpansionTileState this /* r1 => r0, fp-0x8 */)
    //     0x9f0eb8: mov             x0, x1
    //     0x9f0ebc: stur            x1, [fp, #-8]
    // 0x9f0ec0: CheckStackOverflow
    //     0x9f0ec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0ec4: cmp             SP, x16
    //     0x9f0ec8: b.ls            #0x9f0f08
    // 0x9f0ecc: LoadField: r1 = r0->field_f
    //     0x9f0ecc: ldur            w1, [x0, #0xf]
    // 0x9f0ed0: DecompressPointer r1
    //     0x9f0ed0: add             x1, x1, HEAP, lsl #32
    // 0x9f0ed4: cmp             w1, NULL
    // 0x9f0ed8: b.eq            #0x9f0f10
    // 0x9f0edc: r0 = of()
    //     0x9f0edc: bl              #0x9f0a10  ; [package:flutter/src/material/list_tile_theme.dart] ListTileTheme::of
    // 0x9f0ee0: ldur            x1, [fp, #-8]
    // 0x9f0ee4: LoadField: r2 = r1->field_b
    //     0x9f0ee4: ldur            w2, [x1, #0xb]
    // 0x9f0ee8: DecompressPointer r2
    //     0x9f0ee8: add             x2, x2, HEAP, lsl #32
    // 0x9f0eec: cmp             w2, NULL
    // 0x9f0ef0: b.eq            #0x9f0f14
    // 0x9f0ef4: r0 = Instance_ListTileControlAffinity
    //     0x9f0ef4: add             x0, PP, #0x2a, lsl #12  ; [pp+0x2a058] Obj!ListTileControlAffinity@e366c1
    //     0x9f0ef8: ldr             x0, [x0, #0x58]
    // 0x9f0efc: LeaveFrame
    //     0x9f0efc: mov             SP, fp
    //     0x9f0f00: ldp             fp, lr, [SP], #0x10
    // 0x9f0f04: ret
    //     0x9f0f04: ret             
    // 0x9f0f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0f08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f0f0c: b               #0x9f0ecc
    // 0x9f0f10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f0f10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f0f14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f0f14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildLeadingIcon(/* No info */) {
    // ** addr: 0x9f0f18, size: 0x64
    // 0x9f0f18: EnterFrame
    //     0x9f0f18: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0f1c: mov             fp, SP
    // 0x9f0f20: AllocStack(0x8)
    //     0x9f0f20: sub             SP, SP, #8
    // 0x9f0f24: SetupParameters(_ExpansionTileState this /* r1 => r0, fp-0x8 */)
    //     0x9f0f24: mov             x0, x1
    //     0x9f0f28: stur            x1, [fp, #-8]
    // 0x9f0f2c: CheckStackOverflow
    //     0x9f0f2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0f30: cmp             SP, x16
    //     0x9f0f34: b.ls            #0x9f0f74
    // 0x9f0f38: mov             x1, x0
    // 0x9f0f3c: r0 = _effectiveAffinity()
    //     0x9f0f3c: bl              #0x9f0eac  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_effectiveAffinity
    // 0x9f0f40: r16 = Instance_ListTileControlAffinity
    //     0x9f0f40: add             x16, PP, #0x39, lsl #12  ; [pp+0x39790] Obj!ListTileControlAffinity@e366e1
    //     0x9f0f44: ldr             x16, [x16, #0x790]
    // 0x9f0f48: cmp             w0, w16
    // 0x9f0f4c: b.eq            #0x9f0f60
    // 0x9f0f50: r0 = Null
    //     0x9f0f50: mov             x0, NULL
    // 0x9f0f54: LeaveFrame
    //     0x9f0f54: mov             SP, fp
    //     0x9f0f58: ldp             fp, lr, [SP], #0x10
    // 0x9f0f5c: ret
    //     0x9f0f5c: ret             
    // 0x9f0f60: ldur            x1, [fp, #-8]
    // 0x9f0f64: r0 = _buildIcon()
    //     0x9f0f64: bl              #0x9f0ba4  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_buildIcon
    // 0x9f0f68: LeaveFrame
    //     0x9f0f68: mov             SP, fp
    //     0x9f0f6c: ldp             fp, lr, [SP], #0x10
    // 0x9f0f70: ret
    //     0x9f0f70: ret             
    // 0x9f0f74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0f74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f0f78: b               #0x9f0f38
  }
  [closure] void _handleTap(dynamic) {
    // ** addr: 0x9f0f7c, size: 0x3c
    // 0x9f0f7c: EnterFrame
    //     0x9f0f7c: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0f80: mov             fp, SP
    // 0x9f0f84: ldr             x0, [fp, #0x10]
    // 0x9f0f88: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9f0f88: ldur            w1, [x0, #0x17]
    // 0x9f0f8c: DecompressPointer r1
    //     0x9f0f8c: add             x1, x1, HEAP, lsl #32
    // 0x9f0f90: CheckStackOverflow
    //     0x9f0f90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0f94: cmp             SP, x16
    //     0x9f0f98: b.ls            #0x9f0fb0
    // 0x9f0f9c: r0 = _toggleExpansion()
    //     0x9f0f9c: bl              #0x9f0fb8  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_toggleExpansion
    // 0x9f0fa0: r0 = Null
    //     0x9f0fa0: mov             x0, NULL
    // 0x9f0fa4: LeaveFrame
    //     0x9f0fa4: mov             SP, fp
    //     0x9f0fa8: ldp             fp, lr, [SP], #0x10
    // 0x9f0fac: ret
    //     0x9f0fac: ret             
    // 0x9f0fb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0fb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f0fb4: b               #0x9f0f9c
  }
  _ _toggleExpansion(/* No info */) {
    // ** addr: 0x9f0fb8, size: 0x118
    // 0x9f0fb8: EnterFrame
    //     0x9f0fb8: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0fbc: mov             fp, SP
    // 0x9f0fc0: AllocStack(0x18)
    //     0x9f0fc0: sub             SP, SP, #0x18
    // 0x9f0fc4: SetupParameters(_ExpansionTileState this /* r1 => r1, fp-0x8 */)
    //     0x9f0fc4: stur            x1, [fp, #-8]
    // 0x9f0fc8: CheckStackOverflow
    //     0x9f0fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0fcc: cmp             SP, x16
    //     0x9f0fd0: b.ls            #0x9f10bc
    // 0x9f0fd4: r1 = 3
    //     0x9f0fd4: movz            x1, #0x3
    // 0x9f0fd8: r0 = AllocateContext()
    //     0x9f0fd8: bl              #0xec126c  ; AllocateContextStub
    // 0x9f0fdc: mov             x2, x0
    // 0x9f0fe0: ldur            x0, [fp, #-8]
    // 0x9f0fe4: stur            x2, [fp, #-0x10]
    // 0x9f0fe8: StoreField: r2->field_f = r0
    //     0x9f0fe8: stur            w0, [x2, #0xf]
    // 0x9f0fec: LoadField: r1 = r0->field_f
    //     0x9f0fec: ldur            w1, [x0, #0xf]
    // 0x9f0ff0: DecompressPointer r1
    //     0x9f0ff0: add             x1, x1, HEAP, lsl #32
    // 0x9f0ff4: cmp             w1, NULL
    // 0x9f0ff8: b.eq            #0x9f10c4
    // 0x9f0ffc: r0 = of()
    //     0x9f0ffc: bl              #0x9f11a8  ; [package:flutter/src/widgets/localizations.dart] WidgetsLocalizations::of
    // 0x9f1000: ldur            x2, [fp, #-0x10]
    // 0x9f1004: r0 = Instance_TextDirection
    //     0x9f1004: ldr             x0, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0x9f1008: StoreField: r2->field_13 = r0
    //     0x9f1008: stur            w0, [x2, #0x13]
    // 0x9f100c: ldur            x3, [fp, #-8]
    // 0x9f1010: LoadField: r1 = r3->field_f
    //     0x9f1010: ldur            w1, [x3, #0xf]
    // 0x9f1014: DecompressPointer r1
    //     0x9f1014: add             x1, x1, HEAP, lsl #32
    // 0x9f1018: cmp             w1, NULL
    // 0x9f101c: b.eq            #0x9f10c8
    // 0x9f1020: r0 = of()
    //     0x9f1020: bl              #0x9179e4  ; [package:flutter/src/material/material_localizations.dart] MaterialLocalizations::of
    // 0x9f1024: ldur            x3, [fp, #-8]
    // 0x9f1028: LoadField: r0 = r3->field_4b
    //     0x9f1028: ldur            w0, [x3, #0x4b]
    // 0x9f102c: DecompressPointer r0
    //     0x9f102c: add             x0, x0, HEAP, lsl #32
    // 0x9f1030: tbnz            w0, #4, #0x9f1040
    // 0x9f1034: r4 = "Collapsed"
    //     0x9f1034: add             x4, PP, #0x5d, lsl #12  ; [pp+0x5d758] "Collapsed"
    //     0x9f1038: ldr             x4, [x4, #0x758]
    // 0x9f103c: b               #0x9f1048
    // 0x9f1040: r4 = "Expanded"
    //     0x9f1040: add             x4, PP, #0x5d, lsl #12  ; [pp+0x5d760] "Expanded"
    //     0x9f1044: ldr             x4, [x4, #0x760]
    // 0x9f1048: ldur            x2, [fp, #-0x10]
    // 0x9f104c: mov             x0, x4
    // 0x9f1050: stur            x4, [fp, #-0x18]
    // 0x9f1054: ArrayStore: r2[0] = r0  ; List_4
    //     0x9f1054: stur            w0, [x2, #0x17]
    //     0x9f1058: ldurb           w16, [x2, #-1]
    //     0x9f105c: ldurb           w17, [x0, #-1]
    //     0x9f1060: and             x16, x17, x16, lsr #2
    //     0x9f1064: tst             x16, HEAP, lsr #32
    //     0x9f1068: b.eq            #0x9f1070
    //     0x9f106c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9f1070: r1 = Function '<anonymous closure>':.
    //     0x9f1070: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d768] AnonymousClosure: (0x9f1220), in [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_toggleExpansion (0x9f0fb8)
    //     0x9f1074: ldr             x1, [x1, #0x768]
    // 0x9f1078: r0 = AllocateClosure()
    //     0x9f1078: bl              #0xec1630  ; AllocateClosureStub
    // 0x9f107c: ldur            x1, [fp, #-8]
    // 0x9f1080: mov             x2, x0
    // 0x9f1084: r0 = setState()
    //     0x9f1084: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9f1088: ldur            x0, [fp, #-8]
    // 0x9f108c: LoadField: r1 = r0->field_b
    //     0x9f108c: ldur            w1, [x0, #0xb]
    // 0x9f1090: DecompressPointer r1
    //     0x9f1090: add             x1, x1, HEAP, lsl #32
    // 0x9f1094: cmp             w1, NULL
    // 0x9f1098: b.eq            #0x9f10cc
    // 0x9f109c: ldur            x1, [fp, #-0x18]
    // 0x9f10a0: r2 = Instance_TextDirection
    //     0x9f10a0: ldr             x2, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0x9f10a4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9f10a4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9f10a8: r0 = announce()
    //     0x9f10a8: bl              #0x9f10d0  ; [package:flutter/src/semantics/semantics_service.dart] SemanticsService::announce
    // 0x9f10ac: r0 = Null
    //     0x9f10ac: mov             x0, NULL
    // 0x9f10b0: LeaveFrame
    //     0x9f10b0: mov             SP, fp
    //     0x9f10b4: ldp             fp, lr, [SP], #0x10
    // 0x9f10b8: ret
    //     0x9f10b8: ret             
    // 0x9f10bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f10bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f10c0: b               #0x9f0fd4
    // 0x9f10c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f10c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f10c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f10c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f10cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f10cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9f1220, size: 0x140
    // 0x9f1220: EnterFrame
    //     0x9f1220: stp             fp, lr, [SP, #-0x10]!
    //     0x9f1224: mov             fp, SP
    // 0x9f1228: AllocStack(0x28)
    //     0x9f1228: sub             SP, SP, #0x28
    // 0x9f122c: SetupParameters()
    //     0x9f122c: ldr             x0, [fp, #0x10]
    //     0x9f1230: ldur            w2, [x0, #0x17]
    //     0x9f1234: add             x2, x2, HEAP, lsl #32
    //     0x9f1238: stur            x2, [fp, #-8]
    // 0x9f123c: CheckStackOverflow
    //     0x9f123c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f1240: cmp             SP, x16
    //     0x9f1244: b.ls            #0x9f1338
    // 0x9f1248: LoadField: r0 = r2->field_f
    //     0x9f1248: ldur            w0, [x2, #0xf]
    // 0x9f124c: DecompressPointer r0
    //     0x9f124c: add             x0, x0, HEAP, lsl #32
    // 0x9f1250: LoadField: r1 = r0->field_4b
    //     0x9f1250: ldur            w1, [x0, #0x4b]
    // 0x9f1254: DecompressPointer r1
    //     0x9f1254: add             x1, x1, HEAP, lsl #32
    // 0x9f1258: eor             x3, x1, #0x10
    // 0x9f125c: StoreField: r0->field_4b = r3
    //     0x9f125c: stur            w3, [x0, #0x4b]
    // 0x9f1260: tbnz            w3, #4, #0x9f1284
    // 0x9f1264: LoadField: r1 = r0->field_2f
    //     0x9f1264: ldur            w1, [x0, #0x2f]
    // 0x9f1268: DecompressPointer r1
    //     0x9f1268: add             x1, x1, HEAP, lsl #32
    // 0x9f126c: r16 = Sentinel
    //     0x9f126c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9f1270: cmp             w1, w16
    // 0x9f1274: b.eq            #0x9f1340
    // 0x9f1278: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9f1278: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9f127c: r0 = forward()
    //     0x9f127c: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x9f1280: b               #0x9f12cc
    // 0x9f1284: LoadField: r1 = r0->field_2f
    //     0x9f1284: ldur            w1, [x0, #0x2f]
    // 0x9f1288: DecompressPointer r1
    //     0x9f1288: add             x1, x1, HEAP, lsl #32
    // 0x9f128c: r16 = Sentinel
    //     0x9f128c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9f1290: cmp             w1, w16
    // 0x9f1294: b.eq            #0x9f134c
    // 0x9f1298: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9f1298: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9f129c: r0 = reverse()
    //     0x9f129c: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0x9f12a0: ldur            x2, [fp, #-8]
    // 0x9f12a4: r1 = Function '<anonymous closure>':.
    //     0x9f12a4: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d770] AnonymousClosure: (0x9f1360), in [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_toggleExpansion (0x9f0fb8)
    //     0x9f12a8: ldr             x1, [x1, #0x770]
    // 0x9f12ac: stur            x0, [fp, #-0x10]
    // 0x9f12b0: r0 = AllocateClosure()
    //     0x9f12b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x9f12b4: r16 = <void?>
    //     0x9f12b4: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x9f12b8: ldur            lr, [fp, #-0x10]
    // 0x9f12bc: stp             lr, x16, [SP, #8]
    // 0x9f12c0: str             x0, [SP]
    // 0x9f12c4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9f12c4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9f12c8: r0 = then()
    //     0x9f12c8: bl              #0xdb1ab8  ; [package:flutter/src/scheduler/ticker.dart] TickerFuture::then
    // 0x9f12cc: ldur            x0, [fp, #-8]
    // 0x9f12d0: LoadField: r1 = r0->field_f
    //     0x9f12d0: ldur            w1, [x0, #0xf]
    // 0x9f12d4: DecompressPointer r1
    //     0x9f12d4: add             x1, x1, HEAP, lsl #32
    // 0x9f12d8: LoadField: r2 = r1->field_f
    //     0x9f12d8: ldur            w2, [x1, #0xf]
    // 0x9f12dc: DecompressPointer r2
    //     0x9f12dc: add             x2, x2, HEAP, lsl #32
    // 0x9f12e0: cmp             w2, NULL
    // 0x9f12e4: b.eq            #0x9f1358
    // 0x9f12e8: mov             x1, x2
    // 0x9f12ec: r0 = maybeOf()
    //     0x9f12ec: bl              #0x679fbc  ; [package:flutter/src/widgets/page_storage.dart] PageStorage::maybeOf
    // 0x9f12f0: cmp             w0, NULL
    // 0x9f12f4: b.eq            #0x9f1328
    // 0x9f12f8: ldur            x1, [fp, #-8]
    // 0x9f12fc: LoadField: r2 = r1->field_f
    //     0x9f12fc: ldur            w2, [x1, #0xf]
    // 0x9f1300: DecompressPointer r2
    //     0x9f1300: add             x2, x2, HEAP, lsl #32
    // 0x9f1304: LoadField: r1 = r2->field_f
    //     0x9f1304: ldur            w1, [x2, #0xf]
    // 0x9f1308: DecompressPointer r1
    //     0x9f1308: add             x1, x1, HEAP, lsl #32
    // 0x9f130c: cmp             w1, NULL
    // 0x9f1310: b.eq            #0x9f135c
    // 0x9f1314: LoadField: r3 = r2->field_4b
    //     0x9f1314: ldur            w3, [x2, #0x4b]
    // 0x9f1318: DecompressPointer r3
    //     0x9f1318: add             x3, x3, HEAP, lsl #32
    // 0x9f131c: mov             x2, x1
    // 0x9f1320: mov             x1, x0
    // 0x9f1324: r0 = writeState()
    //     0x9f1324: bl              #0x679b9c  ; [package:flutter/src/widgets/page_storage.dart] PageStorageBucket::writeState
    // 0x9f1328: r0 = Null
    //     0x9f1328: mov             x0, NULL
    // 0x9f132c: LeaveFrame
    //     0x9f132c: mov             SP, fp
    //     0x9f1330: ldp             fp, lr, [SP], #0x10
    // 0x9f1334: ret
    //     0x9f1334: ret             
    // 0x9f1338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f1338: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f133c: b               #0x9f1248
    // 0x9f1340: r9 = _animationController
    //     0x9f1340: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d6f0] Field <_ExpansionTileState@543392950._animationController@543392950>: late (offset: 0x30)
    //     0x9f1344: ldr             x9, [x9, #0x6f0]
    // 0x9f1348: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9f1348: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9f134c: r9 = _animationController
    //     0x9f134c: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d6f0] Field <_ExpansionTileState@543392950._animationController@543392950>: late (offset: 0x30)
    //     0x9f1350: ldr             x9, [x9, #0x6f0]
    // 0x9f1354: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9f1354: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9f1358: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f1358: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f135c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f135c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, void) {
    // ** addr: 0x9f1360, size: 0x84
    // 0x9f1360: EnterFrame
    //     0x9f1360: stp             fp, lr, [SP, #-0x10]!
    //     0x9f1364: mov             fp, SP
    // 0x9f1368: AllocStack(0x8)
    //     0x9f1368: sub             SP, SP, #8
    // 0x9f136c: SetupParameters()
    //     0x9f136c: ldr             x0, [fp, #0x18]
    //     0x9f1370: ldur            w1, [x0, #0x17]
    //     0x9f1374: add             x1, x1, HEAP, lsl #32
    // 0x9f1378: CheckStackOverflow
    //     0x9f1378: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f137c: cmp             SP, x16
    //     0x9f1380: b.ls            #0x9f13dc
    // 0x9f1384: LoadField: r0 = r1->field_f
    //     0x9f1384: ldur            w0, [x1, #0xf]
    // 0x9f1388: DecompressPointer r0
    //     0x9f1388: add             x0, x0, HEAP, lsl #32
    // 0x9f138c: stur            x0, [fp, #-8]
    // 0x9f1390: LoadField: r1 = r0->field_f
    //     0x9f1390: ldur            w1, [x0, #0xf]
    // 0x9f1394: DecompressPointer r1
    //     0x9f1394: add             x1, x1, HEAP, lsl #32
    // 0x9f1398: cmp             w1, NULL
    // 0x9f139c: b.ne            #0x9f13b0
    // 0x9f13a0: r0 = Null
    //     0x9f13a0: mov             x0, NULL
    // 0x9f13a4: LeaveFrame
    //     0x9f13a4: mov             SP, fp
    //     0x9f13a8: ldp             fp, lr, [SP], #0x10
    // 0x9f13ac: ret
    //     0x9f13ac: ret             
    // 0x9f13b0: r1 = Function '<anonymous closure>':.
    //     0x9f13b0: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d778] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x9f13b4: ldr             x1, [x1, #0x778]
    // 0x9f13b8: r2 = Null
    //     0x9f13b8: mov             x2, NULL
    // 0x9f13bc: r0 = AllocateClosure()
    //     0x9f13bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x9f13c0: ldur            x1, [fp, #-8]
    // 0x9f13c4: mov             x2, x0
    // 0x9f13c8: r0 = setState()
    //     0x9f13c8: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9f13cc: r0 = Null
    //     0x9f13cc: mov             x0, NULL
    // 0x9f13d0: LeaveFrame
    //     0x9f13d0: mov             SP, fp
    //     0x9f13d4: ldp             fp, lr, [SP], #0x10
    // 0x9f13d8: ret
    //     0x9f13d8: ret             
    // 0x9f13dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f13dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f13e0: b               #0x9f1384
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7d29c, size: 0xd0
    // 0xa7d29c: EnterFrame
    //     0xa7d29c: stp             fp, lr, [SP, #-0x10]!
    //     0xa7d2a0: mov             fp, SP
    // 0xa7d2a4: AllocStack(0x8)
    //     0xa7d2a4: sub             SP, SP, #8
    // 0xa7d2a8: SetupParameters(_ExpansionTileState this /* r1 => r0, fp-0x8 */)
    //     0xa7d2a8: mov             x0, x1
    //     0xa7d2ac: stur            x1, [fp, #-8]
    // 0xa7d2b0: CheckStackOverflow
    //     0xa7d2b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7d2b4: cmp             SP, x16
    //     0xa7d2b8: b.ls            #0xa7d340
    // 0xa7d2bc: LoadField: r1 = r0->field_4f
    //     0xa7d2bc: ldur            w1, [x0, #0x4f]
    // 0xa7d2c0: DecompressPointer r1
    //     0xa7d2c0: add             x1, x1, HEAP, lsl #32
    // 0xa7d2c4: r16 = Sentinel
    //     0xa7d2c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7d2c8: cmp             w1, w16
    // 0xa7d2cc: b.eq            #0xa7d348
    // 0xa7d2d0: LoadField: r1 = r0->field_2f
    //     0xa7d2d0: ldur            w1, [x0, #0x2f]
    // 0xa7d2d4: DecompressPointer r1
    //     0xa7d2d4: add             x1, x1, HEAP, lsl #32
    // 0xa7d2d8: r16 = Sentinel
    //     0xa7d2d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7d2dc: cmp             w1, w16
    // 0xa7d2e0: b.eq            #0xa7d354
    // 0xa7d2e4: r0 = dispose()
    //     0xa7d2e4: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa7d2e8: ldur            x0, [fp, #-8]
    // 0xa7d2ec: LoadField: r1 = r0->field_37
    //     0xa7d2ec: ldur            w1, [x0, #0x37]
    // 0xa7d2f0: DecompressPointer r1
    //     0xa7d2f0: add             x1, x1, HEAP, lsl #32
    // 0xa7d2f4: r16 = Sentinel
    //     0xa7d2f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7d2f8: cmp             w1, w16
    // 0xa7d2fc: b.eq            #0xa7d360
    // 0xa7d300: r0 = dispose()
    //     0xa7d300: bl              #0x7a0b6c  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::dispose
    // 0xa7d304: ldur            x0, [fp, #-8]
    // 0xa7d308: LoadField: r1 = r0->field_53
    //     0xa7d308: ldur            w1, [x0, #0x53]
    // 0xa7d30c: DecompressPointer r1
    //     0xa7d30c: add             x1, x1, HEAP, lsl #32
    // 0xa7d310: cmp             w1, NULL
    // 0xa7d314: b.ne            #0xa7d320
    // 0xa7d318: mov             x1, x0
    // 0xa7d31c: b               #0xa7d328
    // 0xa7d320: r0 = cancel()
    //     0xa7d320: bl              #0x5fe740  ; [dart:isolate] _Timer::cancel
    // 0xa7d324: ldur            x1, [fp, #-8]
    // 0xa7d328: StoreField: r1->field_53 = rNULL
    //     0xa7d328: stur            NULL, [x1, #0x53]
    // 0xa7d32c: r0 = dispose()
    //     0xa7d32c: bl              #0xa7d36c  ; [package:flutter/src/material/expansion_tile.dart] __ExpansionTileState&State&SingleTickerProviderStateMixin::dispose
    // 0xa7d330: r0 = Null
    //     0xa7d330: mov             x0, NULL
    // 0xa7d334: LeaveFrame
    //     0xa7d334: mov             SP, fp
    //     0xa7d338: ldp             fp, lr, [SP], #0x10
    // 0xa7d33c: ret
    //     0xa7d33c: ret             
    // 0xa7d340: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7d340: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7d344: b               #0xa7d2bc
    // 0xa7d348: r9 = _tileController
    //     0xa7d348: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d830] Field <_ExpansionTileState@543392950._tileController@543392950>: late (offset: 0x50)
    //     0xa7d34c: ldr             x9, [x9, #0x830]
    // 0xa7d350: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa7d350: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa7d354: r9 = _animationController
    //     0xa7d354: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d6f0] Field <_ExpansionTileState@543392950._animationController@543392950>: late (offset: 0x30)
    //     0xa7d358: ldr             x9, [x9, #0x6f0]
    // 0xa7d35c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa7d35c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa7d360: r9 = _heightFactor
    //     0xa7d360: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d750] Field <_ExpansionTileState@543392950._heightFactor@543392950>: late (offset: 0x38)
    //     0xa7d364: ldr             x9, [x9, #0x750]
    // 0xa7d368: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa7d368: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _ExpansionTileState(/* No info */) {
    // ** addr: 0xa8fc78, size: 0x138
    // 0xa8fc78: EnterFrame
    //     0xa8fc78: stp             fp, lr, [SP, #-0x10]!
    //     0xa8fc7c: mov             fp, SP
    // 0xa8fc80: AllocStack(0x8)
    //     0xa8fc80: sub             SP, SP, #8
    // 0xa8fc84: r2 = Sentinel
    //     0xa8fc84: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa8fc88: r0 = false
    //     0xa8fc88: add             x0, NULL, #0x30  ; false
    // 0xa8fc8c: mov             x3, x1
    // 0xa8fc90: stur            x1, [fp, #-8]
    // 0xa8fc94: StoreField: r3->field_2f = r2
    //     0xa8fc94: stur            w2, [x3, #0x2f]
    // 0xa8fc98: StoreField: r3->field_33 = r2
    //     0xa8fc98: stur            w2, [x3, #0x33]
    // 0xa8fc9c: StoreField: r3->field_37 = r2
    //     0xa8fc9c: stur            w2, [x3, #0x37]
    // 0xa8fca0: StoreField: r3->field_3b = r2
    //     0xa8fca0: stur            w2, [x3, #0x3b]
    // 0xa8fca4: StoreField: r3->field_3f = r2
    //     0xa8fca4: stur            w2, [x3, #0x3f]
    // 0xa8fca8: StoreField: r3->field_43 = r2
    //     0xa8fca8: stur            w2, [x3, #0x43]
    // 0xa8fcac: StoreField: r3->field_47 = r2
    //     0xa8fcac: stur            w2, [x3, #0x47]
    // 0xa8fcb0: StoreField: r3->field_4b = r0
    //     0xa8fcb0: stur            w0, [x3, #0x4b]
    // 0xa8fcb4: StoreField: r3->field_4f = r2
    //     0xa8fcb4: stur            w2, [x3, #0x4f]
    // 0xa8fcb8: r1 = <ShapeBorder?>
    //     0xa8fcb8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54be8] TypeArguments: <ShapeBorder?>
    //     0xa8fcbc: ldr             x1, [x1, #0xbe8]
    // 0xa8fcc0: r0 = ShapeBorderTween()
    //     0xa8fcc0: bl              #0xa8fdb0  ; AllocateShapeBorderTweenStub -> ShapeBorderTween (size=0x14)
    // 0xa8fcc4: ldur            x2, [fp, #-8]
    // 0xa8fcc8: StoreField: r2->field_1b = r0
    //     0xa8fcc8: stur            w0, [x2, #0x1b]
    //     0xa8fccc: ldurb           w16, [x2, #-1]
    //     0xa8fcd0: ldurb           w17, [x0, #-1]
    //     0xa8fcd4: and             x16, x17, x16, lsr #2
    //     0xa8fcd8: tst             x16, HEAP, lsr #32
    //     0xa8fcdc: b.eq            #0xa8fce4
    //     0xa8fce0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa8fce4: r1 = <Color?>
    //     0xa8fce4: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa8fce8: ldr             x1, [x1, #0x98]
    // 0xa8fcec: r0 = ColorTween()
    //     0xa8fcec: bl              #0x7e3594  ; AllocateColorTweenStub -> ColorTween (size=0x14)
    // 0xa8fcf0: ldur            x2, [fp, #-8]
    // 0xa8fcf4: StoreField: r2->field_1f = r0
    //     0xa8fcf4: stur            w0, [x2, #0x1f]
    //     0xa8fcf8: ldurb           w16, [x2, #-1]
    //     0xa8fcfc: ldurb           w17, [x0, #-1]
    //     0xa8fd00: and             x16, x17, x16, lsr #2
    //     0xa8fd04: tst             x16, HEAP, lsr #32
    //     0xa8fd08: b.eq            #0xa8fd10
    //     0xa8fd0c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa8fd10: r1 = <Color?>
    //     0xa8fd10: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa8fd14: ldr             x1, [x1, #0x98]
    // 0xa8fd18: r0 = ColorTween()
    //     0xa8fd18: bl              #0x7e3594  ; AllocateColorTweenStub -> ColorTween (size=0x14)
    // 0xa8fd1c: ldur            x2, [fp, #-8]
    // 0xa8fd20: StoreField: r2->field_23 = r0
    //     0xa8fd20: stur            w0, [x2, #0x23]
    //     0xa8fd24: ldurb           w16, [x2, #-1]
    //     0xa8fd28: ldurb           w17, [x0, #-1]
    //     0xa8fd2c: and             x16, x17, x16, lsr #2
    //     0xa8fd30: tst             x16, HEAP, lsr #32
    //     0xa8fd34: b.eq            #0xa8fd3c
    //     0xa8fd38: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa8fd3c: r1 = <Color?>
    //     0xa8fd3c: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa8fd40: ldr             x1, [x1, #0x98]
    // 0xa8fd44: r0 = ColorTween()
    //     0xa8fd44: bl              #0x7e3594  ; AllocateColorTweenStub -> ColorTween (size=0x14)
    // 0xa8fd48: ldur            x2, [fp, #-8]
    // 0xa8fd4c: StoreField: r2->field_27 = r0
    //     0xa8fd4c: stur            w0, [x2, #0x27]
    //     0xa8fd50: ldurb           w16, [x2, #-1]
    //     0xa8fd54: ldurb           w17, [x0, #-1]
    //     0xa8fd58: and             x16, x17, x16, lsr #2
    //     0xa8fd5c: tst             x16, HEAP, lsr #32
    //     0xa8fd60: b.eq            #0xa8fd68
    //     0xa8fd64: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa8fd68: r1 = <double>
    //     0xa8fd68: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xa8fd6c: r0 = Tween()
    //     0xa8fd6c: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xa8fd70: r1 = 0.000000
    //     0xa8fd70: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xa8fd74: StoreField: r0->field_b = r1
    //     0xa8fd74: stur            w1, [x0, #0xb]
    // 0xa8fd78: r1 = 1.000000
    //     0xa8fd78: ldr             x1, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xa8fd7c: StoreField: r0->field_f = r1
    //     0xa8fd7c: stur            w1, [x0, #0xf]
    // 0xa8fd80: ldur            x1, [fp, #-8]
    // 0xa8fd84: StoreField: r1->field_2b = r0
    //     0xa8fd84: stur            w0, [x1, #0x2b]
    //     0xa8fd88: ldurb           w16, [x1, #-1]
    //     0xa8fd8c: ldurb           w17, [x0, #-1]
    //     0xa8fd90: and             x16, x17, x16, lsr #2
    //     0xa8fd94: tst             x16, HEAP, lsr #32
    //     0xa8fd98: b.eq            #0xa8fda0
    //     0xa8fd9c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa8fda0: r0 = Null
    //     0xa8fda0: mov             x0, NULL
    // 0xa8fda4: LeaveFrame
    //     0xa8fda4: mov             SP, fp
    //     0xa8fda8: ldp             fp, lr, [SP], #0x10
    // 0xa8fdac: ret
    //     0xa8fdac: ret             
  }
}

// class id: 4863, size: 0x88, field offset: 0xc
//   const constructor, 
class ExpansionTile extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa8fc30, size: 0x48
    // 0xa8fc30: EnterFrame
    //     0xa8fc30: stp             fp, lr, [SP, #-0x10]!
    //     0xa8fc34: mov             fp, SP
    // 0xa8fc38: AllocStack(0x8)
    //     0xa8fc38: sub             SP, SP, #8
    // 0xa8fc3c: CheckStackOverflow
    //     0xa8fc3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8fc40: cmp             SP, x16
    //     0xa8fc44: b.ls            #0xa8fc70
    // 0xa8fc48: r1 = <ExpansionTile>
    //     0xa8fc48: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bcc0] TypeArguments: <ExpansionTile>
    //     0xa8fc4c: ldr             x1, [x1, #0xcc0]
    // 0xa8fc50: r0 = _ExpansionTileState()
    //     0xa8fc50: bl              #0xa8fdbc  ; Allocate_ExpansionTileStateStub -> _ExpansionTileState (size=0x58)
    // 0xa8fc54: mov             x1, x0
    // 0xa8fc58: stur            x0, [fp, #-8]
    // 0xa8fc5c: r0 = _ExpansionTileState()
    //     0xa8fc5c: bl              #0xa8fc78  ; [package:flutter/src/material/expansion_tile.dart] _ExpansionTileState::_ExpansionTileState
    // 0xa8fc60: ldur            x0, [fp, #-8]
    // 0xa8fc64: LeaveFrame
    //     0xa8fc64: mov             SP, fp
    //     0xa8fc68: ldp             fp, lr, [SP], #0x10
    // 0xa8fc6c: ret
    //     0xa8fc6c: ret             
    // 0xa8fc70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8fc70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8fc74: b               #0xa8fc48
  }
}
