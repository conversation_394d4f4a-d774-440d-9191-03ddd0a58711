// lib: , url: package:flutter/src/material/list_tile.dart

// class id: 1048911, size: 0x8
class :: {
}

// class id: 3061, size: 0x5c, field offset: 0x58
//   transformed mixin,
abstract class __RenderListTile&RenderBox&SlottedContainerRenderObjectMixin extends RenderBox
     with SlottedContainerRenderObjectMixin<X0, X1 bound RenderObject> {

  _ attach(/* No info */) {
    // ** addr: 0x7627f4, size: 0x154
    // 0x7627f4: EnterFrame
    //     0x7627f4: stp             fp, lr, [SP, #-0x10]!
    //     0x7627f8: mov             fp, SP
    // 0x7627fc: AllocStack(0x30)
    //     0x7627fc: sub             SP, SP, #0x30
    // 0x762800: SetupParameters(__RenderListTile&RenderBox&SlottedContainerRenderObjectMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x762800: mov             x3, x1
    //     0x762804: mov             x0, x2
    //     0x762808: stur            x1, [fp, #-8]
    //     0x76280c: stur            x2, [fp, #-0x10]
    // 0x762810: CheckStackOverflow
    //     0x762810: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x762814: cmp             SP, x16
    //     0x762818: b.ls            #0x762938
    // 0x76281c: mov             x1, x3
    // 0x762820: mov             x2, x0
    // 0x762824: r0 = attach()
    //     0x762824: bl              #0x765268  ; [package:flutter/src/rendering/object.dart] RenderObject::attach
    // 0x762828: ldur            x1, [fp, #-8]
    // 0x76282c: r0 = children()
    //     0x76282c: bl              #0x762948  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::children
    // 0x762830: mov             x3, x0
    // 0x762834: stur            x3, [fp, #-0x30]
    // 0x762838: LoadField: r4 = r3->field_7
    //     0x762838: ldur            w4, [x3, #7]
    // 0x76283c: DecompressPointer r4
    //     0x76283c: add             x4, x4, HEAP, lsl #32
    // 0x762840: stur            x4, [fp, #-0x28]
    // 0x762844: LoadField: r0 = r3->field_b
    //     0x762844: ldur            w0, [x3, #0xb]
    // 0x762848: r5 = LoadInt32Instr(r0)
    //     0x762848: sbfx            x5, x0, #1, #0x1f
    // 0x76284c: stur            x5, [fp, #-0x20]
    // 0x762850: r0 = 0
    //     0x762850: movz            x0, #0
    // 0x762854: CheckStackOverflow
    //     0x762854: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x762858: cmp             SP, x16
    //     0x76285c: b.ls            #0x762940
    // 0x762860: LoadField: r1 = r3->field_b
    //     0x762860: ldur            w1, [x3, #0xb]
    // 0x762864: r2 = LoadInt32Instr(r1)
    //     0x762864: sbfx            x2, x1, #1, #0x1f
    // 0x762868: cmp             x5, x2
    // 0x76286c: b.ne            #0x762918
    // 0x762870: cmp             x0, x2
    // 0x762874: b.ge            #0x762908
    // 0x762878: LoadField: r1 = r3->field_f
    //     0x762878: ldur            w1, [x3, #0xf]
    // 0x76287c: DecompressPointer r1
    //     0x76287c: add             x1, x1, HEAP, lsl #32
    // 0x762880: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0x762880: add             x16, x1, x0, lsl #2
    //     0x762884: ldur            w6, [x16, #0xf]
    // 0x762888: DecompressPointer r6
    //     0x762888: add             x6, x6, HEAP, lsl #32
    // 0x76288c: stur            x6, [fp, #-8]
    // 0x762890: add             x7, x0, #1
    // 0x762894: stur            x7, [fp, #-0x18]
    // 0x762898: cmp             w6, NULL
    // 0x76289c: b.ne            #0x7628d0
    // 0x7628a0: mov             x0, x6
    // 0x7628a4: mov             x2, x4
    // 0x7628a8: r1 = Null
    //     0x7628a8: mov             x1, NULL
    // 0x7628ac: cmp             w2, NULL
    // 0x7628b0: b.eq            #0x7628d0
    // 0x7628b4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7628b4: ldur            w4, [x2, #0x17]
    // 0x7628b8: DecompressPointer r4
    //     0x7628b8: add             x4, x4, HEAP, lsl #32
    // 0x7628bc: r8 = X0
    //     0x7628bc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7628c0: LoadField: r9 = r4->field_7
    //     0x7628c0: ldur            x9, [x4, #7]
    // 0x7628c4: r3 = Null
    //     0x7628c4: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4ddc8] Null
    //     0x7628c8: ldr             x3, [x3, #0xdc8]
    // 0x7628cc: blr             x9
    // 0x7628d0: ldur            x1, [fp, #-8]
    // 0x7628d4: r0 = LoadClassIdInstr(r1)
    //     0x7628d4: ldur            x0, [x1, #-1]
    //     0x7628d8: ubfx            x0, x0, #0xc, #0x14
    // 0x7628dc: ldur            x2, [fp, #-0x10]
    // 0x7628e0: r0 = GDT[cid_x0 + 0x11974]()
    //     0x7628e0: movz            x17, #0x1974
    //     0x7628e4: movk            x17, #0x1, lsl #16
    //     0x7628e8: add             lr, x0, x17
    //     0x7628ec: ldr             lr, [x21, lr, lsl #3]
    //     0x7628f0: blr             lr
    // 0x7628f4: ldur            x0, [fp, #-0x18]
    // 0x7628f8: ldur            x3, [fp, #-0x30]
    // 0x7628fc: ldur            x4, [fp, #-0x28]
    // 0x762900: ldur            x5, [fp, #-0x20]
    // 0x762904: b               #0x762854
    // 0x762908: r0 = Null
    //     0x762908: mov             x0, NULL
    // 0x76290c: LeaveFrame
    //     0x76290c: mov             SP, fp
    //     0x762910: ldp             fp, lr, [SP], #0x10
    // 0x762914: ret
    //     0x762914: ret             
    // 0x762918: mov             x0, x3
    // 0x76291c: r0 = ConcurrentModificationError()
    //     0x76291c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x762920: mov             x1, x0
    // 0x762924: ldur            x0, [fp, #-0x30]
    // 0x762928: StoreField: r1->field_b = r0
    //     0x762928: stur            w0, [x1, #0xb]
    // 0x76292c: mov             x0, x1
    // 0x762930: r0 = Throw()
    //     0x762930: bl              #0xec04b8  ; ThrowStub
    // 0x762934: brk             #0
    // 0x762938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x762938: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76293c: b               #0x76281c
    // 0x762940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x762940: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x762944: b               #0x762860
  }
  _ visitChildren(/* No info */) {
    // ** addr: 0x786a5c, size: 0xfc
    // 0x786a5c: EnterFrame
    //     0x786a5c: stp             fp, lr, [SP, #-0x10]!
    //     0x786a60: mov             fp, SP
    // 0x786a64: AllocStack(0x30)
    //     0x786a64: sub             SP, SP, #0x30
    // 0x786a68: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x786a68: mov             x0, x2
    //     0x786a6c: stur            x2, [fp, #-8]
    // 0x786a70: CheckStackOverflow
    //     0x786a70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x786a74: cmp             SP, x16
    //     0x786a78: b.ls            #0x786b44
    // 0x786a7c: r0 = children()
    //     0x786a7c: bl              #0x762948  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::children
    // 0x786a80: mov             x2, x0
    // 0x786a84: stur            x2, [fp, #-0x20]
    // 0x786a88: LoadField: r3 = r2->field_b
    //     0x786a88: ldur            w3, [x2, #0xb]
    // 0x786a8c: stur            x3, [fp, #-0x18]
    // 0x786a90: r0 = LoadInt32Instr(r3)
    //     0x786a90: sbfx            x0, x3, #1, #0x1f
    // 0x786a94: r4 = 0
    //     0x786a94: movz            x4, #0
    // 0x786a98: stur            x4, [fp, #-0x10]
    // 0x786a9c: CheckStackOverflow
    //     0x786a9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x786aa0: cmp             SP, x16
    //     0x786aa4: b.ls            #0x786b4c
    // 0x786aa8: cmp             x4, x0
    // 0x786aac: b.ge            #0x786b18
    // 0x786ab0: mov             x1, x4
    // 0x786ab4: cmp             x1, x0
    // 0x786ab8: b.hs            #0x786b54
    // 0x786abc: LoadField: r0 = r2->field_f
    //     0x786abc: ldur            w0, [x2, #0xf]
    // 0x786ac0: DecompressPointer r0
    //     0x786ac0: add             x0, x0, HEAP, lsl #32
    // 0x786ac4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x786ac4: add             x16, x0, x4, lsl #2
    //     0x786ac8: ldur            w1, [x16, #0xf]
    // 0x786acc: DecompressPointer r1
    //     0x786acc: add             x1, x1, HEAP, lsl #32
    // 0x786ad0: ldur            x16, [fp, #-8]
    // 0x786ad4: stp             x1, x16, [SP]
    // 0x786ad8: ldur            x0, [fp, #-8]
    // 0x786adc: ClosureCall
    //     0x786adc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x786ae0: ldur            x2, [x0, #0x1f]
    //     0x786ae4: blr             x2
    // 0x786ae8: ldur            x1, [fp, #-0x20]
    // 0x786aec: LoadField: r0 = r1->field_b
    //     0x786aec: ldur            w0, [x1, #0xb]
    // 0x786af0: ldur            x2, [fp, #-0x18]
    // 0x786af4: cmp             w0, w2
    // 0x786af8: b.ne            #0x786b28
    // 0x786afc: ldur            x3, [fp, #-0x10]
    // 0x786b00: add             x4, x3, #1
    // 0x786b04: r3 = LoadInt32Instr(r0)
    //     0x786b04: sbfx            x3, x0, #1, #0x1f
    // 0x786b08: mov             x0, x3
    // 0x786b0c: mov             x3, x2
    // 0x786b10: mov             x2, x1
    // 0x786b14: b               #0x786a98
    // 0x786b18: r0 = Null
    //     0x786b18: mov             x0, NULL
    // 0x786b1c: LeaveFrame
    //     0x786b1c: mov             SP, fp
    //     0x786b20: ldp             fp, lr, [SP], #0x10
    // 0x786b24: ret
    //     0x786b24: ret             
    // 0x786b28: r0 = ConcurrentModificationError()
    //     0x786b28: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x786b2c: mov             x1, x0
    // 0x786b30: ldur            x0, [fp, #-0x20]
    // 0x786b34: StoreField: r1->field_b = r0
    //     0x786b34: stur            w0, [x1, #0xb]
    // 0x786b38: mov             x0, x1
    // 0x786b3c: r0 = Throw()
    //     0x786b3c: bl              #0xec04b8  ; ThrowStub
    // 0x786b40: brk             #0
    // 0x786b44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x786b44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x786b48: b               #0x786a7c
    // 0x786b4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x786b4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x786b50: b               #0x786aa8
    // 0x786b54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x786b54: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ detach(/* No info */) {
    // ** addr: 0x8063e0, size: 0x140
    // 0x8063e0: EnterFrame
    //     0x8063e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8063e4: mov             fp, SP
    // 0x8063e8: AllocStack(0x28)
    //     0x8063e8: sub             SP, SP, #0x28
    // 0x8063ec: SetupParameters(__RenderListTile&RenderBox&SlottedContainerRenderObjectMixin this /* r1 => r0, fp-0x8 */)
    //     0x8063ec: mov             x0, x1
    //     0x8063f0: stur            x1, [fp, #-8]
    // 0x8063f4: CheckStackOverflow
    //     0x8063f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8063f8: cmp             SP, x16
    //     0x8063fc: b.ls            #0x806510
    // 0x806400: mov             x1, x0
    // 0x806404: r0 = detach()
    //     0x806404: bl              #0x8083b4  ; [package:flutter/src/rendering/object.dart] RenderObject::detach
    // 0x806408: ldur            x1, [fp, #-8]
    // 0x80640c: r0 = children()
    //     0x80640c: bl              #0x762948  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::children
    // 0x806410: mov             x3, x0
    // 0x806414: stur            x3, [fp, #-0x28]
    // 0x806418: LoadField: r4 = r3->field_7
    //     0x806418: ldur            w4, [x3, #7]
    // 0x80641c: DecompressPointer r4
    //     0x80641c: add             x4, x4, HEAP, lsl #32
    // 0x806420: stur            x4, [fp, #-0x20]
    // 0x806424: LoadField: r0 = r3->field_b
    //     0x806424: ldur            w0, [x3, #0xb]
    // 0x806428: r5 = LoadInt32Instr(r0)
    //     0x806428: sbfx            x5, x0, #1, #0x1f
    // 0x80642c: stur            x5, [fp, #-0x18]
    // 0x806430: r0 = 0
    //     0x806430: movz            x0, #0
    // 0x806434: CheckStackOverflow
    //     0x806434: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x806438: cmp             SP, x16
    //     0x80643c: b.ls            #0x806518
    // 0x806440: LoadField: r1 = r3->field_b
    //     0x806440: ldur            w1, [x3, #0xb]
    // 0x806444: r2 = LoadInt32Instr(r1)
    //     0x806444: sbfx            x2, x1, #1, #0x1f
    // 0x806448: cmp             x5, x2
    // 0x80644c: b.ne            #0x8064f0
    // 0x806450: cmp             x0, x2
    // 0x806454: b.ge            #0x8064e0
    // 0x806458: LoadField: r1 = r3->field_f
    //     0x806458: ldur            w1, [x3, #0xf]
    // 0x80645c: DecompressPointer r1
    //     0x80645c: add             x1, x1, HEAP, lsl #32
    // 0x806460: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0x806460: add             x16, x1, x0, lsl #2
    //     0x806464: ldur            w6, [x16, #0xf]
    // 0x806468: DecompressPointer r6
    //     0x806468: add             x6, x6, HEAP, lsl #32
    // 0x80646c: stur            x6, [fp, #-8]
    // 0x806470: add             x7, x0, #1
    // 0x806474: stur            x7, [fp, #-0x10]
    // 0x806478: cmp             w6, NULL
    // 0x80647c: b.ne            #0x8064b0
    // 0x806480: mov             x0, x6
    // 0x806484: mov             x2, x4
    // 0x806488: r1 = Null
    //     0x806488: mov             x1, NULL
    // 0x80648c: cmp             w2, NULL
    // 0x806490: b.eq            #0x8064b0
    // 0x806494: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x806494: ldur            w4, [x2, #0x17]
    // 0x806498: DecompressPointer r4
    //     0x806498: add             x4, x4, HEAP, lsl #32
    // 0x80649c: r8 = X0
    //     0x80649c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8064a0: LoadField: r9 = r4->field_7
    //     0x8064a0: ldur            x9, [x4, #7]
    // 0x8064a4: r3 = Null
    //     0x8064a4: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4ddb8] Null
    //     0x8064a8: ldr             x3, [x3, #0xdb8]
    // 0x8064ac: blr             x9
    // 0x8064b0: ldur            x1, [fp, #-8]
    // 0x8064b4: r0 = LoadClassIdInstr(r1)
    //     0x8064b4: ldur            x0, [x1, #-1]
    //     0x8064b8: ubfx            x0, x0, #0xc, #0x14
    // 0x8064bc: r0 = GDT[cid_x0 + 0xeec9]()
    //     0x8064bc: movz            x17, #0xeec9
    //     0x8064c0: add             lr, x0, x17
    //     0x8064c4: ldr             lr, [x21, lr, lsl #3]
    //     0x8064c8: blr             lr
    // 0x8064cc: ldur            x0, [fp, #-0x10]
    // 0x8064d0: ldur            x3, [fp, #-0x28]
    // 0x8064d4: ldur            x4, [fp, #-0x20]
    // 0x8064d8: ldur            x5, [fp, #-0x18]
    // 0x8064dc: b               #0x806434
    // 0x8064e0: r0 = Null
    //     0x8064e0: mov             x0, NULL
    // 0x8064e4: LeaveFrame
    //     0x8064e4: mov             SP, fp
    //     0x8064e8: ldp             fp, lr, [SP], #0x10
    // 0x8064ec: ret
    //     0x8064ec: ret             
    // 0x8064f0: mov             x0, x3
    // 0x8064f4: r0 = ConcurrentModificationError()
    //     0x8064f4: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x8064f8: mov             x1, x0
    // 0x8064fc: ldur            x0, [fp, #-0x28]
    // 0x806500: StoreField: r1->field_b = r0
    //     0x806500: stur            w0, [x1, #0xb]
    // 0x806504: mov             x0, x1
    // 0x806508: r0 = Throw()
    //     0x806508: bl              #0xec04b8  ; ThrowStub
    // 0x80650c: brk             #0
    // 0x806510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x806510: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806514: b               #0x806400
    // 0x806518: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x806518: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80651c: b               #0x806440
  }
  _ redepthChildren(/* No info */) {
    // ** addr: 0x8088a4, size: 0xf0
    // 0x8088a4: EnterFrame
    //     0x8088a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8088a8: mov             fp, SP
    // 0x8088ac: AllocStack(0x20)
    //     0x8088ac: sub             SP, SP, #0x20
    // 0x8088b0: SetupParameters(__RenderListTile&RenderBox&SlottedContainerRenderObjectMixin this /* r1 => r0, fp-0x8 */)
    //     0x8088b0: mov             x0, x1
    //     0x8088b4: stur            x1, [fp, #-8]
    // 0x8088b8: CheckStackOverflow
    //     0x8088b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8088bc: cmp             SP, x16
    //     0x8088c0: b.ls            #0x808980
    // 0x8088c4: mov             x1, x0
    // 0x8088c8: r0 = children()
    //     0x8088c8: bl              #0x762948  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::children
    // 0x8088cc: mov             x3, x0
    // 0x8088d0: stur            x3, [fp, #-0x20]
    // 0x8088d4: LoadField: r4 = r3->field_b
    //     0x8088d4: ldur            w4, [x3, #0xb]
    // 0x8088d8: stur            x4, [fp, #-0x18]
    // 0x8088dc: r0 = LoadInt32Instr(r4)
    //     0x8088dc: sbfx            x0, x4, #1, #0x1f
    // 0x8088e0: r5 = 0
    //     0x8088e0: movz            x5, #0
    // 0x8088e4: stur            x5, [fp, #-0x10]
    // 0x8088e8: CheckStackOverflow
    //     0x8088e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8088ec: cmp             SP, x16
    //     0x8088f0: b.ls            #0x808988
    // 0x8088f4: cmp             x5, x0
    // 0x8088f8: b.ge            #0x808954
    // 0x8088fc: mov             x1, x5
    // 0x808900: cmp             x1, x0
    // 0x808904: b.hs            #0x808990
    // 0x808908: LoadField: r0 = r3->field_f
    //     0x808908: ldur            w0, [x3, #0xf]
    // 0x80890c: DecompressPointer r0
    //     0x80890c: add             x0, x0, HEAP, lsl #32
    // 0x808910: ArrayLoad: r2 = r0[r5]  ; Unknown_4
    //     0x808910: add             x16, x0, x5, lsl #2
    //     0x808914: ldur            w2, [x16, #0xf]
    // 0x808918: DecompressPointer r2
    //     0x808918: add             x2, x2, HEAP, lsl #32
    // 0x80891c: ldur            x1, [fp, #-8]
    // 0x808920: r0 = redepthChild()
    //     0x808920: bl              #0x8058b0  ; [package:flutter/src/rendering/object.dart] RenderObject::redepthChild
    // 0x808924: ldur            x1, [fp, #-0x20]
    // 0x808928: LoadField: r0 = r1->field_b
    //     0x808928: ldur            w0, [x1, #0xb]
    // 0x80892c: ldur            x2, [fp, #-0x18]
    // 0x808930: cmp             w0, w2
    // 0x808934: b.ne            #0x808964
    // 0x808938: ldur            x3, [fp, #-0x10]
    // 0x80893c: add             x5, x3, #1
    // 0x808940: r3 = LoadInt32Instr(r0)
    //     0x808940: sbfx            x3, x0, #1, #0x1f
    // 0x808944: mov             x0, x3
    // 0x808948: mov             x3, x1
    // 0x80894c: mov             x4, x2
    // 0x808950: b               #0x8088e4
    // 0x808954: r0 = Null
    //     0x808954: mov             x0, NULL
    // 0x808958: LeaveFrame
    //     0x808958: mov             SP, fp
    //     0x80895c: ldp             fp, lr, [SP], #0x10
    // 0x808960: ret
    //     0x808960: ret             
    // 0x808964: r0 = ConcurrentModificationError()
    //     0x808964: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x808968: mov             x1, x0
    // 0x80896c: ldur            x0, [fp, #-0x20]
    // 0x808970: StoreField: r1->field_b = r0
    //     0x808970: stur            w0, [x1, #0xb]
    // 0x808974: mov             x0, x1
    // 0x808978: r0 = Throw()
    //     0x808978: bl              #0xec04b8  ; ThrowStub
    // 0x80897c: brk             #0
    // 0x808980: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x808980: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x808984: b               #0x8088c4
    // 0x808988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x808988: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80898c: b               #0x8088f4
    // 0x808990: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x808990: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ __RenderListTile&RenderBox&SlottedContainerRenderObjectMixin(/* No info */) {
    // ** addr: 0x860db0, size: 0x90
    // 0x860db0: EnterFrame
    //     0x860db0: stp             fp, lr, [SP, #-0x10]!
    //     0x860db4: mov             fp, SP
    // 0x860db8: AllocStack(0x18)
    //     0x860db8: sub             SP, SP, #0x18
    // 0x860dbc: SetupParameters(__RenderListTile&RenderBox&SlottedContainerRenderObjectMixin this /* r1 => r1, fp-0x8 */)
    //     0x860dbc: stur            x1, [fp, #-8]
    // 0x860dc0: CheckStackOverflow
    //     0x860dc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x860dc4: cmp             SP, x16
    //     0x860dc8: b.ls            #0x860e38
    // 0x860dcc: r16 = <_ListTileSlot, RenderBox>
    //     0x860dcc: add             x16, PP, #0x39, lsl #12  ; [pp+0x39888] TypeArguments: <_ListTileSlot, RenderBox>
    //     0x860dd0: ldr             x16, [x16, #0x888]
    // 0x860dd4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x860dd8: stp             lr, x16, [SP]
    // 0x860ddc: r0 = Map._fromLiteral()
    //     0x860ddc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x860de0: ldur            x1, [fp, #-8]
    // 0x860de4: StoreField: r1->field_57 = r0
    //     0x860de4: stur            w0, [x1, #0x57]
    //     0x860de8: ldurb           w16, [x1, #-1]
    //     0x860dec: ldurb           w17, [x0, #-1]
    //     0x860df0: and             x16, x17, x16, lsr #2
    //     0x860df4: tst             x16, HEAP, lsr #32
    //     0x860df8: b.eq            #0x860e00
    //     0x860dfc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x860e00: r0 = _LayoutCacheStorage()
    //     0x860e00: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x860e04: ldur            x1, [fp, #-8]
    // 0x860e08: StoreField: r1->field_4f = r0
    //     0x860e08: stur            w0, [x1, #0x4f]
    //     0x860e0c: ldurb           w16, [x1, #-1]
    //     0x860e10: ldurb           w17, [x0, #-1]
    //     0x860e14: and             x16, x17, x16, lsr #2
    //     0x860e18: tst             x16, HEAP, lsr #32
    //     0x860e1c: b.eq            #0x860e24
    //     0x860e20: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x860e24: r0 = RenderObject()
    //     0x860e24: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x860e28: r0 = Null
    //     0x860e28: mov             x0, NULL
    // 0x860e2c: LeaveFrame
    //     0x860e2c: mov             SP, fp
    //     0x860e30: ldp             fp, lr, [SP], #0x10
    // 0x860e34: ret
    //     0x860e34: ret             
    // 0x860e38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x860e38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x860e3c: b               #0x860dcc
  }
  _ _moveChild(/* No info */) {
    // ** addr: 0xda4224, size: 0x18c
    // 0xda4224: EnterFrame
    //     0xda4224: stp             fp, lr, [SP, #-0x10]!
    //     0xda4228: mov             fp, SP
    // 0xda422c: AllocStack(0x38)
    //     0xda422c: sub             SP, SP, #0x38
    // 0xda4230: SetupParameters(__RenderListTile&RenderBox&SlottedContainerRenderObjectMixin this /* r1 => r6, fp-0x8 */, dynamic _ /* r2 => r5, fp-0x10 */, dynamic _ /* r3 => r4, fp-0x18 */, dynamic _ /* r5 => r3, fp-0x20 */)
    //     0xda4230: mov             x6, x1
    //     0xda4234: mov             x4, x3
    //     0xda4238: stur            x3, [fp, #-0x18]
    //     0xda423c: mov             x3, x5
    //     0xda4240: stur            x5, [fp, #-0x20]
    //     0xda4244: mov             x5, x2
    //     0xda4248: stur            x1, [fp, #-8]
    //     0xda424c: stur            x2, [fp, #-0x10]
    // 0xda4250: CheckStackOverflow
    //     0xda4250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xda4254: cmp             SP, x16
    //     0xda4258: b.ls            #0xda43a8
    // 0xda425c: mov             x0, x5
    // 0xda4260: r2 = Null
    //     0xda4260: mov             x2, NULL
    // 0xda4264: r1 = Null
    //     0xda4264: mov             x1, NULL
    // 0xda4268: r4 = 60
    //     0xda4268: movz            x4, #0x3c
    // 0xda426c: branchIfSmi(r0, 0xda4278)
    //     0xda426c: tbz             w0, #0, #0xda4278
    // 0xda4270: r4 = LoadClassIdInstr(r0)
    //     0xda4270: ldur            x4, [x0, #-1]
    //     0xda4274: ubfx            x4, x4, #0xc, #0x14
    // 0xda4278: sub             x4, x4, #0xbba
    // 0xda427c: cmp             x4, #0x9a
    // 0xda4280: b.ls            #0xda4294
    // 0xda4284: r8 = RenderBox
    //     0xda4284: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0xda4288: r3 = Null
    //     0xda4288: add             x3, PP, #0x54, lsl #12  ; [pp+0x54d28] Null
    //     0xda428c: ldr             x3, [x3, #0xd28]
    // 0xda4290: r0 = RenderBox()
    //     0xda4290: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0xda4294: ldur            x0, [fp, #-0x18]
    // 0xda4298: r2 = Null
    //     0xda4298: mov             x2, NULL
    // 0xda429c: r1 = Null
    //     0xda429c: mov             x1, NULL
    // 0xda42a0: r4 = 60
    //     0xda42a0: movz            x4, #0x3c
    // 0xda42a4: branchIfSmi(r0, 0xda42b0)
    //     0xda42a4: tbz             w0, #0, #0xda42b0
    // 0xda42a8: r4 = LoadClassIdInstr(r0)
    //     0xda42a8: ldur            x4, [x0, #-1]
    //     0xda42ac: ubfx            x4, x4, #0xc, #0x14
    // 0xda42b0: r17 = 7056
    //     0xda42b0: movz            x17, #0x1b90
    // 0xda42b4: cmp             x4, x17
    // 0xda42b8: b.eq            #0xda42d0
    // 0xda42bc: r8 = _ListTileSlot
    //     0xda42bc: add             x8, PP, #0x54, lsl #12  ; [pp+0x54ce8] Type: _ListTileSlot
    //     0xda42c0: ldr             x8, [x8, #0xce8]
    // 0xda42c4: r3 = Null
    //     0xda42c4: add             x3, PP, #0x54, lsl #12  ; [pp+0x54d38] Null
    //     0xda42c8: ldr             x3, [x3, #0xd38]
    // 0xda42cc: r0 = _ListTileSlot()
    //     0xda42cc: bl              #0x72f0d8  ; IsType__ListTileSlot_Stub
    // 0xda42d0: ldur            x0, [fp, #-0x20]
    // 0xda42d4: r2 = Null
    //     0xda42d4: mov             x2, NULL
    // 0xda42d8: r1 = Null
    //     0xda42d8: mov             x1, NULL
    // 0xda42dc: r4 = 60
    //     0xda42dc: movz            x4, #0x3c
    // 0xda42e0: branchIfSmi(r0, 0xda42ec)
    //     0xda42e0: tbz             w0, #0, #0xda42ec
    // 0xda42e4: r4 = LoadClassIdInstr(r0)
    //     0xda42e4: ldur            x4, [x0, #-1]
    //     0xda42e8: ubfx            x4, x4, #0xc, #0x14
    // 0xda42ec: r17 = 7056
    //     0xda42ec: movz            x17, #0x1b90
    // 0xda42f0: cmp             x4, x17
    // 0xda42f4: b.eq            #0xda430c
    // 0xda42f8: r8 = _ListTileSlot
    //     0xda42f8: add             x8, PP, #0x54, lsl #12  ; [pp+0x54ce8] Type: _ListTileSlot
    //     0xda42fc: ldr             x8, [x8, #0xce8]
    // 0xda4300: r3 = Null
    //     0xda4300: add             x3, PP, #0x54, lsl #12  ; [pp+0x54d48] Null
    //     0xda4304: ldr             x3, [x3, #0xd48]
    // 0xda4308: r0 = _ListTileSlot()
    //     0xda4308: bl              #0x72f0d8  ; IsType__ListTileSlot_Stub
    // 0xda430c: ldur            x0, [fp, #-8]
    // 0xda4310: LoadField: r3 = r0->field_57
    //     0xda4310: ldur            w3, [x0, #0x57]
    // 0xda4314: DecompressPointer r3
    //     0xda4314: add             x3, x3, HEAP, lsl #32
    // 0xda4318: mov             x1, x3
    // 0xda431c: ldur            x2, [fp, #-0x20]
    // 0xda4320: stur            x3, [fp, #-0x28]
    // 0xda4324: r0 = _getValueOrData()
    //     0xda4324: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xda4328: mov             x1, x0
    // 0xda432c: ldur            x0, [fp, #-0x28]
    // 0xda4330: LoadField: r2 = r0->field_f
    //     0xda4330: ldur            w2, [x0, #0xf]
    // 0xda4334: DecompressPointer r2
    //     0xda4334: add             x2, x2, HEAP, lsl #32
    // 0xda4338: cmp             w2, w1
    // 0xda433c: b.ne            #0xda4348
    // 0xda4340: r0 = Null
    //     0xda4340: mov             x0, NULL
    // 0xda4344: b               #0xda434c
    // 0xda4348: mov             x0, x1
    // 0xda434c: r1 = 60
    //     0xda434c: movz            x1, #0x3c
    // 0xda4350: branchIfSmi(r0, 0xda435c)
    //     0xda4350: tbz             w0, #0, #0xda435c
    // 0xda4354: r1 = LoadClassIdInstr(r0)
    //     0xda4354: ldur            x1, [x0, #-1]
    //     0xda4358: ubfx            x1, x1, #0xc, #0x14
    // 0xda435c: ldur            x16, [fp, #-0x10]
    // 0xda4360: stp             x16, x0, [SP]
    // 0xda4364: mov             x0, x1
    // 0xda4368: mov             lr, x0
    // 0xda436c: ldr             lr, [x21, lr, lsl #3]
    // 0xda4370: blr             lr
    // 0xda4374: tbnz            w0, #4, #0xda4388
    // 0xda4378: ldur            x1, [fp, #-8]
    // 0xda437c: ldur            x3, [fp, #-0x20]
    // 0xda4380: r2 = Null
    //     0xda4380: mov             x2, NULL
    // 0xda4384: r0 = _setChild()
    //     0xda4384: bl              #0xda73fc  ; [package:flutter/src/material/list_tile.dart] __RenderListTile&RenderBox&SlottedContainerRenderObjectMixin::_setChild
    // 0xda4388: ldur            x1, [fp, #-8]
    // 0xda438c: ldur            x2, [fp, #-0x10]
    // 0xda4390: ldur            x3, [fp, #-0x18]
    // 0xda4394: r0 = _setChild()
    //     0xda4394: bl              #0xda73fc  ; [package:flutter/src/material/list_tile.dart] __RenderListTile&RenderBox&SlottedContainerRenderObjectMixin::_setChild
    // 0xda4398: r0 = Null
    //     0xda4398: mov             x0, NULL
    // 0xda439c: LeaveFrame
    //     0xda439c: mov             SP, fp
    //     0xda43a0: ldp             fp, lr, [SP], #0x10
    // 0xda43a4: ret
    //     0xda43a4: ret             
    // 0xda43a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xda43a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xda43ac: b               #0xda425c
  }
  const get _ _slotToChild(/* No info */) {
    // ** addr: 0xda608c, size: 0xc
    // 0xda608c: LoadField: r0 = r1->field_57
    //     0xda608c: ldur            w0, [x1, #0x57]
    // 0xda6090: DecompressPointer r0
    //     0xda6090: add             x0, x0, HEAP, lsl #32
    // 0xda6094: ret
    //     0xda6094: ret             
  }
  _ _setChild(/* No info */) {
    // ** addr: 0xda73fc, size: 0x13c
    // 0xda73fc: EnterFrame
    //     0xda73fc: stp             fp, lr, [SP, #-0x10]!
    //     0xda7400: mov             fp, SP
    // 0xda7404: AllocStack(0x20)
    //     0xda7404: sub             SP, SP, #0x20
    // 0xda7408: SetupParameters(__RenderListTile&RenderBox&SlottedContainerRenderObjectMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xda7408: mov             x5, x1
    //     0xda740c: mov             x4, x2
    //     0xda7410: stur            x1, [fp, #-8]
    //     0xda7414: stur            x2, [fp, #-0x10]
    //     0xda7418: stur            x3, [fp, #-0x18]
    // 0xda741c: CheckStackOverflow
    //     0xda741c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xda7420: cmp             SP, x16
    //     0xda7424: b.ls            #0xda7530
    // 0xda7428: mov             x0, x4
    // 0xda742c: r2 = Null
    //     0xda742c: mov             x2, NULL
    // 0xda7430: r1 = Null
    //     0xda7430: mov             x1, NULL
    // 0xda7434: r4 = 60
    //     0xda7434: movz            x4, #0x3c
    // 0xda7438: branchIfSmi(r0, 0xda7444)
    //     0xda7438: tbz             w0, #0, #0xda7444
    // 0xda743c: r4 = LoadClassIdInstr(r0)
    //     0xda743c: ldur            x4, [x0, #-1]
    //     0xda7440: ubfx            x4, x4, #0xc, #0x14
    // 0xda7444: sub             x4, x4, #0xbba
    // 0xda7448: cmp             x4, #0x9a
    // 0xda744c: b.ls            #0xda7460
    // 0xda7450: r8 = RenderBox?
    //     0xda7450: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0xda7454: r3 = Null
    //     0xda7454: add             x3, PP, #0x54, lsl #12  ; [pp+0x54d58] Null
    //     0xda7458: ldr             x3, [x3, #0xd58]
    // 0xda745c: r0 = RenderBox?()
    //     0xda745c: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0xda7460: ldur            x0, [fp, #-0x18]
    // 0xda7464: r2 = Null
    //     0xda7464: mov             x2, NULL
    // 0xda7468: r1 = Null
    //     0xda7468: mov             x1, NULL
    // 0xda746c: r4 = 60
    //     0xda746c: movz            x4, #0x3c
    // 0xda7470: branchIfSmi(r0, 0xda747c)
    //     0xda7470: tbz             w0, #0, #0xda747c
    // 0xda7474: r4 = LoadClassIdInstr(r0)
    //     0xda7474: ldur            x4, [x0, #-1]
    //     0xda7478: ubfx            x4, x4, #0xc, #0x14
    // 0xda747c: r17 = 7056
    //     0xda747c: movz            x17, #0x1b90
    // 0xda7480: cmp             x4, x17
    // 0xda7484: b.eq            #0xda749c
    // 0xda7488: r8 = _ListTileSlot
    //     0xda7488: add             x8, PP, #0x54, lsl #12  ; [pp+0x54ce8] Type: _ListTileSlot
    //     0xda748c: ldr             x8, [x8, #0xce8]
    // 0xda7490: r3 = Null
    //     0xda7490: add             x3, PP, #0x54, lsl #12  ; [pp+0x54d68] Null
    //     0xda7494: ldr             x3, [x3, #0xd68]
    // 0xda7498: r0 = _ListTileSlot()
    //     0xda7498: bl              #0x72f0d8  ; IsType__ListTileSlot_Stub
    // 0xda749c: ldur            x0, [fp, #-8]
    // 0xda74a0: LoadField: r3 = r0->field_57
    //     0xda74a0: ldur            w3, [x0, #0x57]
    // 0xda74a4: DecompressPointer r3
    //     0xda74a4: add             x3, x3, HEAP, lsl #32
    // 0xda74a8: mov             x1, x3
    // 0xda74ac: ldur            x2, [fp, #-0x18]
    // 0xda74b0: stur            x3, [fp, #-0x20]
    // 0xda74b4: r0 = _getValueOrData()
    //     0xda74b4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xda74b8: mov             x1, x0
    // 0xda74bc: ldur            x0, [fp, #-0x20]
    // 0xda74c0: LoadField: r2 = r0->field_f
    //     0xda74c0: ldur            w2, [x0, #0xf]
    // 0xda74c4: DecompressPointer r2
    //     0xda74c4: add             x2, x2, HEAP, lsl #32
    // 0xda74c8: cmp             w2, w1
    // 0xda74cc: b.ne            #0xda74d8
    // 0xda74d0: r2 = Null
    //     0xda74d0: mov             x2, NULL
    // 0xda74d4: b               #0xda74dc
    // 0xda74d8: mov             x2, x1
    // 0xda74dc: cmp             w2, NULL
    // 0xda74e0: b.eq            #0xda74f8
    // 0xda74e4: ldur            x1, [fp, #-8]
    // 0xda74e8: r0 = dropChild()
    //     0xda74e8: bl              #0x8019dc  ; [package:flutter/src/rendering/object.dart] RenderObject::dropChild
    // 0xda74ec: ldur            x1, [fp, #-0x20]
    // 0xda74f0: ldur            x2, [fp, #-0x18]
    // 0xda74f4: r0 = remove()
    //     0xda74f4: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0xda74f8: ldur            x0, [fp, #-0x10]
    // 0xda74fc: cmp             w0, NULL
    // 0xda7500: b.eq            #0xda7520
    // 0xda7504: ldur            x1, [fp, #-0x20]
    // 0xda7508: ldur            x2, [fp, #-0x18]
    // 0xda750c: mov             x3, x0
    // 0xda7510: r0 = []=()
    //     0xda7510: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xda7514: ldur            x1, [fp, #-8]
    // 0xda7518: ldur            x2, [fp, #-0x10]
    // 0xda751c: r0 = adoptChild()
    //     0xda751c: bl              #0x8057b8  ; [package:flutter/src/rendering/object.dart] RenderObject::adoptChild
    // 0xda7520: r0 = Null
    //     0xda7520: mov             x0, NULL
    // 0xda7524: LeaveFrame
    //     0xda7524: mov             SP, fp
    //     0xda7528: ldp             fp, lr, [SP], #0x10
    // 0xda752c: ret
    //     0xda752c: ret             
    // 0xda7530: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xda7530: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xda7534: b               #0xda7428
  }
}

// class id: 3062, size: 0x94, field offset: 0x5c
class _RenderListTile extends __RenderListTile&RenderBox&SlottedContainerRenderObjectMixin {

  dynamic computeMinIntrinsicWidth(dynamic) {
    // ** addr: 0x72f0b4, size: 0x24
    // 0x72f0b4: EnterFrame
    //     0x72f0b4: stp             fp, lr, [SP, #-0x10]!
    //     0x72f0b8: mov             fp, SP
    // 0x72f0bc: ldr             x2, [fp, #0x10]
    // 0x72f0c0: r1 = Function 'computeMinIntrinsicWidth':.
    //     0x72f0c0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d20] AnonymousClosure: (0x72f0fc), in [package:flutter/src/material/list_tile.dart] _RenderListTile::computeMinIntrinsicWidth (0x72f170)
    //     0x72f0c4: ldr             x1, [x1, #0xd20]
    // 0x72f0c8: r0 = AllocateClosure()
    //     0x72f0c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x72f0cc: LeaveFrame
    //     0x72f0cc: mov             SP, fp
    //     0x72f0d0: ldp             fp, lr, [SP], #0x10
    // 0x72f0d4: ret
    //     0x72f0d4: ret             
  }
  [closure] double computeMinIntrinsicWidth(dynamic, double) {
    // ** addr: 0x72f0fc, size: 0x74
    // 0x72f0fc: EnterFrame
    //     0x72f0fc: stp             fp, lr, [SP, #-0x10]!
    //     0x72f100: mov             fp, SP
    // 0x72f104: ldr             x0, [fp, #0x18]
    // 0x72f108: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x72f108: ldur            w1, [x0, #0x17]
    // 0x72f10c: DecompressPointer r1
    //     0x72f10c: add             x1, x1, HEAP, lsl #32
    // 0x72f110: CheckStackOverflow
    //     0x72f110: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72f114: cmp             SP, x16
    //     0x72f118: b.ls            #0x72f158
    // 0x72f11c: ldr             x2, [fp, #0x10]
    // 0x72f120: r0 = computeMinIntrinsicWidth()
    //     0x72f120: bl              #0x72f170  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::computeMinIntrinsicWidth
    // 0x72f124: r0 = inline_Allocate_Double()
    //     0x72f124: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x72f128: add             x0, x0, #0x10
    //     0x72f12c: cmp             x1, x0
    //     0x72f130: b.ls            #0x72f160
    //     0x72f134: str             x0, [THR, #0x50]  ; THR::top
    //     0x72f138: sub             x0, x0, #0xf
    //     0x72f13c: movz            x1, #0xe15c
    //     0x72f140: movk            x1, #0x3, lsl #16
    //     0x72f144: stur            x1, [x0, #-1]
    // 0x72f148: StoreField: r0->field_7 = d0
    //     0x72f148: stur            d0, [x0, #7]
    // 0x72f14c: LeaveFrame
    //     0x72f14c: mov             SP, fp
    //     0x72f150: ldp             fp, lr, [SP], #0x10
    // 0x72f154: ret
    //     0x72f154: ret             
    // 0x72f158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72f158: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72f15c: b               #0x72f11c
    // 0x72f160: SaveReg d0
    //     0x72f160: str             q0, [SP, #-0x10]!
    // 0x72f164: r0 = AllocateDouble()
    //     0x72f164: bl              #0xec2254  ; AllocateDoubleStub
    // 0x72f168: RestoreReg d0
    //     0x72f168: ldr             q0, [SP], #0x10
    // 0x72f16c: b               #0x72f148
  }
  _ computeMinIntrinsicWidth(/* No info */) {
    // ** addr: 0x72f170, size: 0x1e0
    // 0x72f170: EnterFrame
    //     0x72f170: stp             fp, lr, [SP, #-0x10]!
    //     0x72f174: mov             fp, SP
    // 0x72f178: AllocStack(0x28)
    //     0x72f178: sub             SP, SP, #0x28
    // 0x72f17c: SetupParameters(_RenderListTile this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x72f17c: mov             x3, x1
    //     0x72f180: mov             x0, x2
    //     0x72f184: stur            x1, [fp, #-8]
    //     0x72f188: stur            x2, [fp, #-0x10]
    // 0x72f18c: CheckStackOverflow
    //     0x72f18c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72f190: cmp             SP, x16
    //     0x72f194: b.ls            #0x72f340
    // 0x72f198: mov             x1, x3
    // 0x72f19c: r2 = Instance__ListTileSlot
    //     0x72f19c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd8] Obj!_ListTileSlot@e36601
    //     0x72f1a0: ldr             x2, [x2, #0xcd8]
    // 0x72f1a4: r0 = childForSlot()
    //     0x72f1a4: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x72f1a8: cmp             w0, NULL
    // 0x72f1ac: b.eq            #0x72f258
    // 0x72f1b0: ldur            x3, [fp, #-8]
    // 0x72f1b4: ldur            x0, [fp, #-0x10]
    // 0x72f1b8: mov             x1, x3
    // 0x72f1bc: r2 = Instance__ListTileSlot
    //     0x72f1bc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd8] Obj!_ListTileSlot@e36601
    //     0x72f1c0: ldr             x2, [x2, #0xcd8]
    // 0x72f1c4: r0 = childForSlot()
    //     0x72f1c4: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x72f1c8: cmp             w0, NULL
    // 0x72f1cc: b.eq            #0x72f348
    // 0x72f1d0: ldur            x2, [fp, #-0x10]
    // 0x72f1d4: LoadField: d0 = r2->field_7
    //     0x72f1d4: ldur            d0, [x2, #7]
    // 0x72f1d8: mov             x1, x0
    // 0x72f1dc: r0 = getMinIntrinsicWidth()
    //     0x72f1dc: bl              #0x72d27c  ; [package:flutter/src/rendering/box.dart] RenderBox::getMinIntrinsicWidth
    // 0x72f1e0: ldur            x0, [fp, #-8]
    // 0x72f1e4: LoadField: d1 = r0->field_83
    //     0x72f1e4: ldur            d1, [x0, #0x83]
    // 0x72f1e8: fcmp            d0, d1
    // 0x72f1ec: b.le            #0x72f1fc
    // 0x72f1f0: mov             v1.16b, v0.16b
    // 0x72f1f4: d2 = 0.000000
    //     0x72f1f4: eor             v2.16b, v2.16b, v2.16b
    // 0x72f1f8: b               #0x72f230
    // 0x72f1fc: fcmp            d1, d0
    // 0x72f200: b.le            #0x72f20c
    // 0x72f204: d2 = 0.000000
    //     0x72f204: eor             v2.16b, v2.16b, v2.16b
    // 0x72f208: b               #0x72f230
    // 0x72f20c: d2 = 0.000000
    //     0x72f20c: eor             v2.16b, v2.16b, v2.16b
    // 0x72f210: fcmp            d0, d2
    // 0x72f214: b.ne            #0x72f224
    // 0x72f218: fadd            d3, d0, d1
    // 0x72f21c: mov             v1.16b, v3.16b
    // 0x72f220: b               #0x72f230
    // 0x72f224: fcmp            d1, d1
    // 0x72f228: b.vs            #0x72f230
    // 0x72f22c: mov             v1.16b, v0.16b
    // 0x72f230: d0 = 2.000000
    //     0x72f230: fmov            d0, #2.00000000
    // 0x72f234: LoadField: d3 = r0->field_73
    //     0x72f234: ldur            d3, [x0, #0x73]
    // 0x72f238: LoadField: r1 = r0->field_5f
    //     0x72f238: ldur            w1, [x0, #0x5f]
    // 0x72f23c: DecompressPointer r1
    //     0x72f23c: add             x1, x1, HEAP, lsl #32
    // 0x72f240: LoadField: d4 = r1->field_7
    //     0x72f240: ldur            d4, [x1, #7]
    // 0x72f244: fmul            d5, d4, d0
    // 0x72f248: fadd            d0, d3, d5
    // 0x72f24c: fadd            d3, d1, d0
    // 0x72f250: mov             v0.16b, v3.16b
    // 0x72f254: b               #0x72f264
    // 0x72f258: ldur            x0, [fp, #-8]
    // 0x72f25c: d2 = 0.000000
    //     0x72f25c: eor             v2.16b, v2.16b, v2.16b
    // 0x72f260: d0 = 0.000000
    //     0x72f260: eor             v0.16b, v0.16b, v0.16b
    // 0x72f264: ldur            x3, [fp, #-0x10]
    // 0x72f268: mov             x1, x0
    // 0x72f26c: stur            d0, [fp, #-0x18]
    // 0x72f270: r2 = Instance__ListTileSlot
    //     0x72f270: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd0] Obj!_ListTileSlot@e365e1
    //     0x72f274: ldr             x2, [x2, #0xcd0]
    // 0x72f278: r0 = childForSlot()
    //     0x72f278: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x72f27c: cmp             w0, NULL
    // 0x72f280: b.eq            #0x72f34c
    // 0x72f284: ldur            x1, [fp, #-0x10]
    // 0x72f288: LoadField: d1 = r1->field_7
    //     0x72f288: ldur            d1, [x1, #7]
    // 0x72f28c: mov             x1, x0
    // 0x72f290: mov             v0.16b, v1.16b
    // 0x72f294: stur            d1, [fp, #-0x20]
    // 0x72f298: r0 = _minWidth()
    //     0x72f298: bl              #0x72f468  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_minWidth
    // 0x72f29c: ldur            x1, [fp, #-8]
    // 0x72f2a0: r2 = Instance__ListTileSlot
    //     0x72f2a0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce0] Obj!_ListTileSlot@e365c1
    //     0x72f2a4: ldr             x2, [x2, #0xce0]
    // 0x72f2a8: stur            d0, [fp, #-0x28]
    // 0x72f2ac: r0 = childForSlot()
    //     0x72f2ac: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x72f2b0: mov             x1, x0
    // 0x72f2b4: ldur            d0, [fp, #-0x20]
    // 0x72f2b8: r0 = _minWidth()
    //     0x72f2b8: bl              #0x72f468  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_minWidth
    // 0x72f2bc: mov             v1.16b, v0.16b
    // 0x72f2c0: ldur            d0, [fp, #-0x28]
    // 0x72f2c4: fcmp            d0, d1
    // 0x72f2c8: b.le            #0x72f2d4
    // 0x72f2cc: mov             v1.16b, v0.16b
    // 0x72f2d0: b               #0x72f300
    // 0x72f2d4: fcmp            d1, d0
    // 0x72f2d8: b.gt            #0x72f300
    // 0x72f2dc: d2 = 0.000000
    //     0x72f2dc: eor             v2.16b, v2.16b, v2.16b
    // 0x72f2e0: fcmp            d0, d2
    // 0x72f2e4: b.ne            #0x72f2f4
    // 0x72f2e8: fadd            d2, d0, d1
    // 0x72f2ec: mov             v1.16b, v2.16b
    // 0x72f2f0: b               #0x72f300
    // 0x72f2f4: fcmp            d1, d1
    // 0x72f2f8: b.vs            #0x72f300
    // 0x72f2fc: mov             v1.16b, v0.16b
    // 0x72f300: ldur            d0, [fp, #-0x18]
    // 0x72f304: fadd            d2, d0, d1
    // 0x72f308: ldur            x1, [fp, #-8]
    // 0x72f30c: stur            d2, [fp, #-0x28]
    // 0x72f310: r2 = Instance__ListTileSlot
    //     0x72f310: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce8] Obj!_ListTileSlot@e365a1
    //     0x72f314: ldr             x2, [x2, #0xce8]
    // 0x72f318: r0 = childForSlot()
    //     0x72f318: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x72f31c: mov             x1, x0
    // 0x72f320: ldur            d0, [fp, #-0x20]
    // 0x72f324: r0 = _maxWidth()
    //     0x72f324: bl              #0x72f42c  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_maxWidth
    // 0x72f328: ldur            d1, [fp, #-0x28]
    // 0x72f32c: fadd            d2, d1, d0
    // 0x72f330: mov             v0.16b, v2.16b
    // 0x72f334: LeaveFrame
    //     0x72f334: mov             SP, fp
    //     0x72f338: ldp             fp, lr, [SP], #0x10
    // 0x72f33c: ret
    //     0x72f33c: ret             
    // 0x72f340: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72f340: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72f344: b               #0x72f198
    // 0x72f348: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x72f348: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x72f34c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x72f34c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ trailing(/* No info */) {
    // ** addr: 0x72f350, size: 0x34
    // 0x72f350: EnterFrame
    //     0x72f350: stp             fp, lr, [SP, #-0x10]!
    //     0x72f354: mov             fp, SP
    // 0x72f358: CheckStackOverflow
    //     0x72f358: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72f35c: cmp             SP, x16
    //     0x72f360: b.ls            #0x72f37c
    // 0x72f364: r2 = Instance__ListTileSlot
    //     0x72f364: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce8] Obj!_ListTileSlot@e365a1
    //     0x72f368: ldr             x2, [x2, #0xce8]
    // 0x72f36c: r0 = childForSlot()
    //     0x72f36c: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x72f370: LeaveFrame
    //     0x72f370: mov             SP, fp
    //     0x72f374: ldp             fp, lr, [SP], #0x10
    // 0x72f378: ret
    //     0x72f378: ret             
    // 0x72f37c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72f37c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72f380: b               #0x72f364
  }
  get _ subtitle(/* No info */) {
    // ** addr: 0x72f384, size: 0x34
    // 0x72f384: EnterFrame
    //     0x72f384: stp             fp, lr, [SP, #-0x10]!
    //     0x72f388: mov             fp, SP
    // 0x72f38c: CheckStackOverflow
    //     0x72f38c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72f390: cmp             SP, x16
    //     0x72f394: b.ls            #0x72f3b0
    // 0x72f398: r2 = Instance__ListTileSlot
    //     0x72f398: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce0] Obj!_ListTileSlot@e365c1
    //     0x72f39c: ldr             x2, [x2, #0xce0]
    // 0x72f3a0: r0 = childForSlot()
    //     0x72f3a0: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x72f3a4: LeaveFrame
    //     0x72f3a4: mov             SP, fp
    //     0x72f3a8: ldp             fp, lr, [SP], #0x10
    // 0x72f3ac: ret
    //     0x72f3ac: ret             
    // 0x72f3b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72f3b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72f3b4: b               #0x72f398
  }
  get _ title(/* No info */) {
    // ** addr: 0x72f3b8, size: 0x40
    // 0x72f3b8: EnterFrame
    //     0x72f3b8: stp             fp, lr, [SP, #-0x10]!
    //     0x72f3bc: mov             fp, SP
    // 0x72f3c0: CheckStackOverflow
    //     0x72f3c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72f3c4: cmp             SP, x16
    //     0x72f3c8: b.ls            #0x72f3ec
    // 0x72f3cc: r2 = Instance__ListTileSlot
    //     0x72f3cc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd0] Obj!_ListTileSlot@e365e1
    //     0x72f3d0: ldr             x2, [x2, #0xcd0]
    // 0x72f3d4: r0 = childForSlot()
    //     0x72f3d4: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x72f3d8: cmp             w0, NULL
    // 0x72f3dc: b.eq            #0x72f3f4
    // 0x72f3e0: LeaveFrame
    //     0x72f3e0: mov             SP, fp
    //     0x72f3e4: ldp             fp, lr, [SP], #0x10
    // 0x72f3e8: ret
    //     0x72f3e8: ret             
    // 0x72f3ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72f3ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72f3f0: b               #0x72f3cc
    // 0x72f3f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x72f3f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ leading(/* No info */) {
    // ** addr: 0x72f3f8, size: 0x34
    // 0x72f3f8: EnterFrame
    //     0x72f3f8: stp             fp, lr, [SP, #-0x10]!
    //     0x72f3fc: mov             fp, SP
    // 0x72f400: CheckStackOverflow
    //     0x72f400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72f404: cmp             SP, x16
    //     0x72f408: b.ls            #0x72f424
    // 0x72f40c: r2 = Instance__ListTileSlot
    //     0x72f40c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd8] Obj!_ListTileSlot@e36601
    //     0x72f410: ldr             x2, [x2, #0xcd8]
    // 0x72f414: r0 = childForSlot()
    //     0x72f414: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x72f418: LeaveFrame
    //     0x72f418: mov             SP, fp
    //     0x72f41c: ldp             fp, lr, [SP], #0x10
    // 0x72f420: ret
    //     0x72f420: ret             
    // 0x72f424: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72f424: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72f428: b               #0x72f40c
  }
  static _ _maxWidth(/* No info */) {
    // ** addr: 0x72f42c, size: 0x3c
    // 0x72f42c: EnterFrame
    //     0x72f42c: stp             fp, lr, [SP, #-0x10]!
    //     0x72f430: mov             fp, SP
    // 0x72f434: CheckStackOverflow
    //     0x72f434: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72f438: cmp             SP, x16
    //     0x72f43c: b.ls            #0x72f460
    // 0x72f440: cmp             w1, NULL
    // 0x72f444: b.ne            #0x72f450
    // 0x72f448: d0 = 0.000000
    //     0x72f448: eor             v0.16b, v0.16b, v0.16b
    // 0x72f44c: b               #0x72f454
    // 0x72f450: r0 = getMaxIntrinsicWidth()
    //     0x72f450: bl              #0x72d4b4  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicWidth
    // 0x72f454: LeaveFrame
    //     0x72f454: mov             SP, fp
    //     0x72f458: ldp             fp, lr, [SP], #0x10
    // 0x72f45c: ret
    //     0x72f45c: ret             
    // 0x72f460: r0 = StackOverflowSharedWithFPURegs()
    //     0x72f460: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x72f464: b               #0x72f440
  }
  static _ _minWidth(/* No info */) {
    // ** addr: 0x72f468, size: 0x3c
    // 0x72f468: EnterFrame
    //     0x72f468: stp             fp, lr, [SP, #-0x10]!
    //     0x72f46c: mov             fp, SP
    // 0x72f470: CheckStackOverflow
    //     0x72f470: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72f474: cmp             SP, x16
    //     0x72f478: b.ls            #0x72f49c
    // 0x72f47c: cmp             w1, NULL
    // 0x72f480: b.ne            #0x72f48c
    // 0x72f484: d0 = 0.000000
    //     0x72f484: eor             v0.16b, v0.16b, v0.16b
    // 0x72f488: b               #0x72f490
    // 0x72f48c: r0 = getMinIntrinsicWidth()
    //     0x72f48c: bl              #0x72d27c  ; [package:flutter/src/rendering/box.dart] RenderBox::getMinIntrinsicWidth
    // 0x72f490: LeaveFrame
    //     0x72f490: mov             SP, fp
    //     0x72f494: ldp             fp, lr, [SP], #0x10
    // 0x72f498: ret
    //     0x72f498: ret             
    // 0x72f49c: r0 = StackOverflowSharedWithFPURegs()
    //     0x72f49c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x72f4a0: b               #0x72f47c
  }
  _ computeDryBaseline(/* No info */) {
    // ** addr: 0x740d2c, size: 0xe8
    // 0x740d2c: EnterFrame
    //     0x740d2c: stp             fp, lr, [SP, #-0x10]!
    //     0x740d30: mov             fp, SP
    // 0x740d34: AllocStack(0x18)
    //     0x740d34: sub             SP, SP, #0x18
    // 0x740d38: SetupParameters(_RenderListTile this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x740d38: mov             x5, x1
    //     0x740d3c: mov             x4, x2
    //     0x740d40: stur            x1, [fp, #-8]
    //     0x740d44: stur            x2, [fp, #-0x10]
    //     0x740d48: stur            x3, [fp, #-0x18]
    // 0x740d4c: CheckStackOverflow
    //     0x740d4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x740d50: cmp             SP, x16
    //     0x740d54: b.ls            #0x740e08
    // 0x740d58: mov             x0, x4
    // 0x740d5c: r2 = Null
    //     0x740d5c: mov             x2, NULL
    // 0x740d60: r1 = Null
    //     0x740d60: mov             x1, NULL
    // 0x740d64: r4 = 60
    //     0x740d64: movz            x4, #0x3c
    // 0x740d68: branchIfSmi(r0, 0x740d74)
    //     0x740d68: tbz             w0, #0, #0x740d74
    // 0x740d6c: r4 = LoadClassIdInstr(r0)
    //     0x740d6c: ldur            x4, [x0, #-1]
    //     0x740d70: ubfx            x4, x4, #0xc, #0x14
    // 0x740d74: sub             x4, x4, #0xc83
    // 0x740d78: cmp             x4, #1
    // 0x740d7c: b.ls            #0x740d90
    // 0x740d80: r8 = BoxConstraints
    //     0x740d80: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x740d84: r3 = Null
    //     0x740d84: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4dd98] Null
    //     0x740d88: ldr             x3, [x3, #0xd98]
    // 0x740d8c: r0 = BoxConstraints()
    //     0x740d8c: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x740d90: ldur            x1, [fp, #-8]
    // 0x740d94: ldur            x5, [fp, #-0x10]
    // 0x740d98: r2 = Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getDryBaseline': static.
    //     0x740d98: add             x2, PP, #0x45, lsl #12  ; [pp+0x45748] Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getDryBaseline': static. (0x7e54fb130f1c)
    //     0x740d9c: ldr             x2, [x2, #0x748]
    // 0x740da0: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static.
    //     0x740da0: add             x3, PP, #0x44, lsl #12  ; [pp+0x44d20] Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static. (0x7e54fb140c14)
    //     0x740da4: ldr             x3, [x3, #0xd20]
    // 0x740da8: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x740da8: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x740dac: r0 = _computeSizes()
    //     0x740dac: bl              #0x740e14  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_computeSizes
    // 0x740db0: ldur            x1, [fp, #-8]
    // 0x740db4: r2 = Instance__ListTileSlot
    //     0x740db4: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd0] Obj!_ListTileSlot@e365e1
    //     0x740db8: ldr             x2, [x2, #0xcd0]
    // 0x740dbc: stur            x0, [fp, #-8]
    // 0x740dc0: r0 = childForSlot()
    //     0x740dc0: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x740dc4: cmp             w0, NULL
    // 0x740dc8: b.eq            #0x740e10
    // 0x740dcc: ldur            x4, [fp, #-8]
    // 0x740dd0: LoadField: r2 = r4->field_f
    //     0x740dd0: ldur            w2, [x4, #0xf]
    // 0x740dd4: DecompressPointer r2
    //     0x740dd4: add             x2, x2, HEAP, lsl #32
    // 0x740dd8: mov             x1, x0
    // 0x740ddc: ldur            x3, [fp, #-0x18]
    // 0x740de0: r0 = getDryBaseline()
    //     0x740de0: bl              #0x730f54  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryBaseline
    // 0x740de4: mov             x1, x0
    // 0x740de8: ldur            x0, [fp, #-8]
    // 0x740dec: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x740dec: ldur            w2, [x0, #0x17]
    // 0x740df0: DecompressPointer r2
    //     0x740df0: add             x2, x2, HEAP, lsl #32
    // 0x740df4: LoadField: d0 = r2->field_7
    //     0x740df4: ldur            d0, [x2, #7]
    // 0x740df8: r0 = BaselineOffset.+()
    //     0x740df8: bl              #0x73d964  ; [package:flutter/src/rendering/box.dart] ::BaselineOffset.+
    // 0x740dfc: LeaveFrame
    //     0x740dfc: mov             SP, fp
    //     0x740e00: ldp             fp, lr, [SP], #0x10
    // 0x740e04: ret
    //     0x740e04: ret             
    // 0x740e08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x740e08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x740e0c: b               #0x740d58
    // 0x740e10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x740e10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _computeSizes(/* No info */) {
    // ** addr: 0x740e14, size: 0xb38
    // 0x740e14: EnterFrame
    //     0x740e14: stp             fp, lr, [SP, #-0x10]!
    //     0x740e18: mov             fp, SP
    // 0x740e1c: AllocStack(0xc0)
    //     0x740e1c: sub             SP, SP, #0xc0
    // 0x740e20: SetupParameters(_RenderListTile this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */, dynamic _ /* r5 => r1 */, {dynamic positionChild = Null /* r4, fp-0x8 */})
    //     0x740e20: mov             x0, x3
    //     0x740e24: stur            x3, [fp, #-0x20]
    //     0x740e28: mov             x3, x1
    //     0x740e2c: stur            x1, [fp, #-0x10]
    //     0x740e30: mov             x1, x5
    //     0x740e34: stur            x2, [fp, #-0x18]
    //     0x740e38: ldur            w5, [x4, #0x13]
    //     0x740e3c: ldur            w6, [x4, #0x1f]
    //     0x740e40: add             x6, x6, HEAP, lsl #32
    //     0x740e44: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4dd70] "positionChild"
    //     0x740e48: ldr             x16, [x16, #0xd70]
    //     0x740e4c: cmp             w6, w16
    //     0x740e50: b.ne            #0x740e70
    //     0x740e54: ldur            w6, [x4, #0x23]
    //     0x740e58: add             x6, x6, HEAP, lsl #32
    //     0x740e5c: sub             w4, w5, w6
    //     0x740e60: add             x5, fp, w4, sxtw #2
    //     0x740e64: ldr             x5, [x5, #8]
    //     0x740e68: mov             x4, x5
    //     0x740e6c: b               #0x740e74
    //     0x740e70: mov             x4, NULL
    //     0x740e74: stur            x4, [fp, #-8]
    // 0x740e78: CheckStackOverflow
    //     0x740e78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x740e7c: cmp             SP, x16
    //     0x740e80: b.ls            #0x741840
    // 0x740e84: r0 = loosen()
    //     0x740e84: bl              #0x73cf04  ; [package:flutter/src/rendering/box.dart] BoxConstraints::loosen
    // 0x740e88: stur            x0, [fp, #-0x28]
    // 0x740e8c: LoadField: d0 = r0->field_f
    //     0x740e8c: ldur            d0, [x0, #0xf]
    // 0x740e90: ldur            x1, [fp, #-0x10]
    // 0x740e94: stur            d0, [fp, #-0x60]
    // 0x740e98: r0 = maxIconHeightConstraint()
    //     0x740e98: bl              #0x741da0  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::maxIconHeightConstraint
    // 0x740e9c: ldur            x1, [fp, #-0x28]
    // 0x740ea0: mov             x2, x0
    // 0x740ea4: r0 = enforce()
    //     0x740ea4: bl              #0x733638  ; [package:flutter/src/rendering/box.dart] BoxConstraints::enforce
    // 0x740ea8: ldur            x1, [fp, #-0x10]
    // 0x740eac: r2 = Instance__ListTileSlot
    //     0x740eac: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd8] Obj!_ListTileSlot@e36601
    //     0x740eb0: ldr             x2, [x2, #0xcd8]
    // 0x740eb4: stur            x0, [fp, #-0x30]
    // 0x740eb8: r0 = childForSlot()
    //     0x740eb8: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x740ebc: ldur            x1, [fp, #-0x10]
    // 0x740ec0: r2 = Instance__ListTileSlot
    //     0x740ec0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce8] Obj!_ListTileSlot@e365a1
    //     0x740ec4: ldr             x2, [x2, #0xce8]
    // 0x740ec8: stur            x0, [fp, #-0x38]
    // 0x740ecc: r0 = childForSlot()
    //     0x740ecc: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x740ed0: mov             x2, x0
    // 0x740ed4: ldur            x1, [fp, #-0x38]
    // 0x740ed8: stur            x2, [fp, #-0x40]
    // 0x740edc: cmp             w1, NULL
    // 0x740ee0: b.ne            #0x740ef0
    // 0x740ee4: mov             x1, x2
    // 0x740ee8: r2 = Null
    //     0x740ee8: mov             x2, NULL
    // 0x740eec: b               #0x740f18
    // 0x740ef0: ldur            x16, [fp, #-0x20]
    // 0x740ef4: stp             x1, x16, [SP, #8]
    // 0x740ef8: ldur            x16, [fp, #-0x30]
    // 0x740efc: str             x16, [SP]
    // 0x740f00: ldur            x0, [fp, #-0x20]
    // 0x740f04: ClosureCall
    //     0x740f04: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x740f08: ldur            x2, [x0, #0x1f]
    //     0x740f0c: blr             x2
    // 0x740f10: mov             x2, x0
    // 0x740f14: ldur            x1, [fp, #-0x40]
    // 0x740f18: stur            x2, [fp, #-0x48]
    // 0x740f1c: cmp             w1, NULL
    // 0x740f20: b.ne            #0x740f30
    // 0x740f24: mov             x0, x2
    // 0x740f28: r2 = Null
    //     0x740f28: mov             x2, NULL
    // 0x740f2c: b               #0x740f58
    // 0x740f30: ldur            x16, [fp, #-0x20]
    // 0x740f34: stp             x1, x16, [SP, #8]
    // 0x740f38: ldur            x16, [fp, #-0x30]
    // 0x740f3c: str             x16, [SP]
    // 0x740f40: ldur            x0, [fp, #-0x20]
    // 0x740f44: ClosureCall
    //     0x740f44: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x740f48: ldur            x2, [x0, #0x1f]
    //     0x740f4c: blr             x2
    // 0x740f50: mov             x2, x0
    // 0x740f54: ldur            x0, [fp, #-0x48]
    // 0x740f58: stur            x2, [fp, #-0x30]
    // 0x740f5c: cmp             w0, NULL
    // 0x740f60: b.ne            #0x740f78
    // 0x740f64: ldur            x3, [fp, #-0x10]
    // 0x740f68: d1 = 0.000000
    //     0x740f68: eor             v1.16b, v1.16b, v1.16b
    // 0x740f6c: d2 = 0.000000
    //     0x740f6c: eor             v2.16b, v2.16b, v2.16b
    // 0x740f70: d0 = 2.000000
    //     0x740f70: fmov            d0, #2.00000000
    // 0x740f74: b               #0x740ff0
    // 0x740f78: ldur            x3, [fp, #-0x10]
    // 0x740f7c: LoadField: d0 = r3->field_83
    //     0x740f7c: ldur            d0, [x3, #0x83]
    // 0x740f80: LoadField: d1 = r0->field_7
    //     0x740f80: ldur            d1, [x0, #7]
    // 0x740f84: fcmp            d0, d1
    // 0x740f88: b.le            #0x740f98
    // 0x740f8c: mov             v1.16b, v0.16b
    // 0x740f90: d2 = 0.000000
    //     0x740f90: eor             v2.16b, v2.16b, v2.16b
    // 0x740f94: b               #0x740fcc
    // 0x740f98: fcmp            d1, d0
    // 0x740f9c: b.le            #0x740fa8
    // 0x740fa0: d2 = 0.000000
    //     0x740fa0: eor             v2.16b, v2.16b, v2.16b
    // 0x740fa4: b               #0x740fcc
    // 0x740fa8: d2 = 0.000000
    //     0x740fa8: eor             v2.16b, v2.16b, v2.16b
    // 0x740fac: fcmp            d0, d2
    // 0x740fb0: b.ne            #0x740fc0
    // 0x740fb4: fadd            d3, d0, d1
    // 0x740fb8: mov             v1.16b, v3.16b
    // 0x740fbc: b               #0x740fcc
    // 0x740fc0: fcmp            d1, d1
    // 0x740fc4: b.vs            #0x740fcc
    // 0x740fc8: mov             v1.16b, v0.16b
    // 0x740fcc: d0 = 2.000000
    //     0x740fcc: fmov            d0, #2.00000000
    // 0x740fd0: LoadField: d3 = r3->field_73
    //     0x740fd0: ldur            d3, [x3, #0x73]
    // 0x740fd4: LoadField: r1 = r3->field_5f
    //     0x740fd4: ldur            w1, [x3, #0x5f]
    // 0x740fd8: DecompressPointer r1
    //     0x740fd8: add             x1, x1, HEAP, lsl #32
    // 0x740fdc: LoadField: d4 = r1->field_7
    //     0x740fdc: ldur            d4, [x1, #7]
    // 0x740fe0: fmul            d5, d4, d0
    // 0x740fe4: fadd            d4, d3, d5
    // 0x740fe8: fadd            d3, d1, d4
    // 0x740fec: mov             v1.16b, v3.16b
    // 0x740ff0: stur            d1, [fp, #-0x70]
    // 0x740ff4: cmp             w2, NULL
    // 0x740ff8: b.ne            #0x741010
    // 0x740ffc: mov             x1, x3
    // 0x741000: mov             v0.16b, v1.16b
    // 0x741004: mov             v1.16b, v2.16b
    // 0x741008: d3 = 0.000000
    //     0x741008: eor             v3.16b, v3.16b, v3.16b
    // 0x74100c: b               #0x741088
    // 0x741010: LoadField: d3 = r2->field_7
    //     0x741010: ldur            d3, [x2, #7]
    // 0x741014: mov             x1, x3
    // 0x741018: stur            d3, [fp, #-0x68]
    // 0x74101c: r0 = _effectiveHorizontalTitleGap()
    //     0x74101c: bl              #0x741d80  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_effectiveHorizontalTitleGap
    // 0x741020: mov             v1.16b, v0.16b
    // 0x741024: ldur            d0, [fp, #-0x68]
    // 0x741028: fadd            d2, d0, d1
    // 0x74102c: d0 = 32.000000
    //     0x74102c: add             x17, PP, #0x2c, lsl #12  ; [pp+0x2c260] IMM: double(32) from 0x4040000000000000
    //     0x741030: ldr             d0, [x17, #0x260]
    // 0x741034: fcmp            d2, d0
    // 0x741038: b.le            #0x741048
    // 0x74103c: mov             v0.16b, v2.16b
    // 0x741040: d1 = 0.000000
    //     0x741040: eor             v1.16b, v1.16b, v1.16b
    // 0x741044: b               #0x74107c
    // 0x741048: fcmp            d0, d2
    // 0x74104c: b.le            #0x741060
    // 0x741050: d0 = 32.000000
    //     0x741050: add             x17, PP, #0x2c, lsl #12  ; [pp+0x2c260] IMM: double(32) from 0x4040000000000000
    //     0x741054: ldr             d0, [x17, #0x260]
    // 0x741058: d1 = 0.000000
    //     0x741058: eor             v1.16b, v1.16b, v1.16b
    // 0x74105c: b               #0x74107c
    // 0x741060: d1 = 0.000000
    //     0x741060: eor             v1.16b, v1.16b, v1.16b
    // 0x741064: fcmp            d2, d1
    // 0x741068: b.ne            #0x741078
    // 0x74106c: fadd            d3, d2, d0
    // 0x741070: mov             v0.16b, v3.16b
    // 0x741074: b               #0x74107c
    // 0x741078: mov             v0.16b, v2.16b
    // 0x74107c: mov             v3.16b, v0.16b
    // 0x741080: ldur            x1, [fp, #-0x10]
    // 0x741084: ldur            d0, [fp, #-0x70]
    // 0x741088: ldur            d2, [fp, #-0x60]
    // 0x74108c: stur            d3, [fp, #-0x68]
    // 0x741090: fsub            d4, d2, d0
    // 0x741094: r0 = inline_Allocate_Double()
    //     0x741094: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x741098: add             x0, x0, #0x10
    //     0x74109c: cmp             x2, x0
    //     0x7410a0: b.ls            #0x741848
    //     0x7410a4: str             x0, [THR, #0x50]  ; THR::top
    //     0x7410a8: sub             x0, x0, #0xf
    //     0x7410ac: movz            x2, #0xe15c
    //     0x7410b0: movk            x2, #0x3, lsl #16
    //     0x7410b4: stur            x2, [x0, #-1]
    // 0x7410b8: StoreField: r0->field_7 = d4
    //     0x7410b8: stur            d4, [x0, #7]
    // 0x7410bc: r2 = inline_Allocate_Double()
    //     0x7410bc: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x7410c0: add             x2, x2, #0x10
    //     0x7410c4: cmp             x3, x2
    //     0x7410c8: b.ls            #0x741870
    //     0x7410cc: str             x2, [THR, #0x50]  ; THR::top
    //     0x7410d0: sub             x2, x2, #0xf
    //     0x7410d4: movz            x3, #0xe15c
    //     0x7410d8: movk            x3, #0x3, lsl #16
    //     0x7410dc: stur            x3, [x2, #-1]
    // 0x7410e0: StoreField: r2->field_7 = d3
    //     0x7410e0: stur            d3, [x2, #7]
    // 0x7410e4: stp             x2, x0, [SP]
    // 0x7410e8: r0 = -()
    //     0x7410e8: bl              #0xebf790  ; [dart:core] _Double::-
    // 0x7410ec: str             x0, [SP]
    // 0x7410f0: ldur            x1, [fp, #-0x28]
    // 0x7410f4: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0x7410f4: add             x4, PP, #0x4d, lsl #12  ; [pp+0x4dd78] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0x7410f8: ldr             x4, [x4, #0xd78]
    // 0x7410fc: r0 = tighten()
    //     0x7410fc: bl              #0x73bb30  ; [package:flutter/src/rendering/box.dart] BoxConstraints::tighten
    // 0x741100: ldur            x1, [fp, #-0x10]
    // 0x741104: r2 = Instance__ListTileSlot
    //     0x741104: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce0] Obj!_ListTileSlot@e365c1
    //     0x741108: ldr             x2, [x2, #0xce0]
    // 0x74110c: stur            x0, [fp, #-0x28]
    // 0x741110: r0 = childForSlot()
    //     0x741110: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x741114: ldur            x1, [fp, #-0x10]
    // 0x741118: r2 = Instance__ListTileSlot
    //     0x741118: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd0] Obj!_ListTileSlot@e365e1
    //     0x74111c: ldr             x2, [x2, #0xcd0]
    // 0x741120: stur            x0, [fp, #-0x50]
    // 0x741124: r0 = childForSlot()
    //     0x741124: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x741128: cmp             w0, NULL
    // 0x74112c: b.eq            #0x741894
    // 0x741130: ldur            x16, [fp, #-0x20]
    // 0x741134: stp             x0, x16, [SP, #8]
    // 0x741138: ldur            x16, [fp, #-0x28]
    // 0x74113c: str             x16, [SP]
    // 0x741140: ldur            x0, [fp, #-0x20]
    // 0x741144: ClosureCall
    //     0x741144: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x741148: ldur            x2, [x0, #0x1f]
    //     0x74114c: blr             x2
    // 0x741150: LoadField: d0 = r0->field_f
    //     0x741150: ldur            d0, [x0, #0xf]
    // 0x741154: ldur            x0, [fp, #-0x10]
    // 0x741158: stur            d0, [fp, #-0x78]
    // 0x74115c: LoadField: r1 = r0->field_67
    //     0x74115c: ldur            w1, [x0, #0x67]
    // 0x741160: DecompressPointer r1
    //     0x741160: add             x1, x1, HEAP, lsl #32
    // 0x741164: LoadField: r2 = r1->field_7
    //     0x741164: ldur            x2, [x1, #7]
    // 0x741168: cmp             x2, #0
    // 0x74116c: b.gt            #0x741178
    // 0x741170: r2 = false
    //     0x741170: add             x2, NULL, #0x30  ; false
    // 0x741174: b               #0x74117c
    // 0x741178: r2 = true
    //     0x741178: add             x2, NULL, #0x20  ; true
    // 0x74117c: ldur            x1, [fp, #-0x50]
    // 0x741180: stur            x2, [fp, #-0x58]
    // 0x741184: cmp             w1, NULL
    // 0x741188: b.ne            #0x74121c
    // 0x74118c: LoadField: r1 = r0->field_8b
    //     0x74118c: ldur            w1, [x0, #0x8b]
    // 0x741190: DecompressPointer r1
    //     0x741190: add             x1, x1, HEAP, lsl #32
    // 0x741194: cmp             w1, NULL
    // 0x741198: b.ne            #0x7411ac
    // 0x74119c: mov             x1, x0
    // 0x7411a0: r0 = _defaultTileHeight()
    //     0x7411a0: bl              #0x741cc8  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_defaultTileHeight
    // 0x7411a4: mov             v2.16b, v0.16b
    // 0x7411a8: b               #0x7411b4
    // 0x7411ac: LoadField: d0 = r1->field_7
    //     0x7411ac: ldur            d0, [x1, #7]
    // 0x7411b0: mov             v2.16b, v0.16b
    // 0x7411b4: ldur            x2, [fp, #-0x10]
    // 0x7411b8: ldur            d0, [fp, #-0x78]
    // 0x7411bc: d1 = 2.000000
    //     0x7411bc: fmov            d1, #2.00000000
    // 0x7411c0: LoadField: d3 = r2->field_7b
    //     0x7411c0: ldur            d3, [x2, #0x7b]
    // 0x7411c4: fmul            d4, d3, d1
    // 0x7411c8: fadd            d3, d0, d4
    // 0x7411cc: fcmp            d2, d3
    // 0x7411d0: b.gt            #0x741208
    // 0x7411d4: fcmp            d3, d2
    // 0x7411d8: b.le            #0x7411e4
    // 0x7411dc: mov             v2.16b, v3.16b
    // 0x7411e0: b               #0x741208
    // 0x7411e4: d4 = 0.000000
    //     0x7411e4: eor             v4.16b, v4.16b, v4.16b
    // 0x7411e8: fcmp            d2, d4
    // 0x7411ec: b.ne            #0x7411fc
    // 0x7411f0: fadd            d4, d2, d3
    // 0x7411f4: mov             v2.16b, v4.16b
    // 0x7411f8: b               #0x741208
    // 0x7411fc: fcmp            d3, d3
    // 0x741200: b.vc            #0x741208
    // 0x741204: mov             v2.16b, v3.16b
    // 0x741208: fsub            d3, d2, d0
    // 0x74120c: fdiv            d0, d3, d1
    // 0x741210: mov             v1.16b, v2.16b
    // 0x741214: mov             x0, x2
    // 0x741218: b               #0x741654
    // 0x74121c: mov             x2, x0
    // 0x741220: d4 = 0.000000
    //     0x741220: eor             v4.16b, v4.16b, v4.16b
    // 0x741224: d1 = 2.000000
    //     0x741224: fmov            d1, #2.00000000
    // 0x741228: ldur            x16, [fp, #-0x20]
    // 0x74122c: stp             x1, x16, [SP, #8]
    // 0x741230: ldur            x16, [fp, #-0x28]
    // 0x741234: str             x16, [SP]
    // 0x741238: ldur            x0, [fp, #-0x20]
    // 0x74123c: ClosureCall
    //     0x74123c: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x741240: ldur            x2, [x0, #0x1f]
    //     0x741244: blr             x2
    // 0x741248: LoadField: d0 = r0->field_f
    //     0x741248: ldur            d0, [x0, #0xf]
    // 0x74124c: ldur            x1, [fp, #-0x10]
    // 0x741250: stur            d0, [fp, #-0x80]
    // 0x741254: r2 = Instance__ListTileSlot
    //     0x741254: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd0] Obj!_ListTileSlot@e365e1
    //     0x741258: ldr             x2, [x2, #0xcd0]
    // 0x74125c: r0 = childForSlot()
    //     0x74125c: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x741260: cmp             w0, NULL
    // 0x741264: b.eq            #0x741898
    // 0x741268: ldur            x1, [fp, #-0x10]
    // 0x74126c: LoadField: r2 = r1->field_6b
    //     0x74126c: ldur            w2, [x1, #0x6b]
    // 0x741270: DecompressPointer r2
    //     0x741270: add             x2, x2, HEAP, lsl #32
    // 0x741274: ldur            x16, [fp, #-0x18]
    // 0x741278: stp             x0, x16, [SP, #0x10]
    // 0x74127c: ldur            x16, [fp, #-0x28]
    // 0x741280: stp             x2, x16, [SP]
    // 0x741284: ldur            x0, [fp, #-0x18]
    // 0x741288: ClosureCall
    //     0x741288: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0x74128c: ldur            x2, [x0, #0x1f]
    //     0x741290: blr             x2
    // 0x741294: cmp             w0, NULL
    // 0x741298: b.ne            #0x7412d0
    // 0x74129c: ldur            d0, [fp, #-0x78]
    // 0x7412a0: r0 = inline_Allocate_Double()
    //     0x7412a0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7412a4: add             x0, x0, #0x10
    //     0x7412a8: cmp             x1, x0
    //     0x7412ac: b.ls            #0x74189c
    //     0x7412b0: str             x0, [THR, #0x50]  ; THR::top
    //     0x7412b4: sub             x0, x0, #0xf
    //     0x7412b8: movz            x1, #0xe15c
    //     0x7412bc: movk            x1, #0x3, lsl #16
    //     0x7412c0: stur            x1, [x0, #-1]
    // 0x7412c4: StoreField: r0->field_7 = d0
    //     0x7412c4: stur            d0, [x0, #7]
    // 0x7412c8: mov             x2, x0
    // 0x7412cc: b               #0x7412d8
    // 0x7412d0: ldur            d0, [fp, #-0x78]
    // 0x7412d4: mov             x2, x0
    // 0x7412d8: ldur            x1, [fp, #-0x10]
    // 0x7412dc: stur            x2, [fp, #-0x20]
    // 0x7412e0: LoadField: r0 = r1->field_6f
    //     0x7412e0: ldur            w0, [x1, #0x6f]
    // 0x7412e4: DecompressPointer r0
    //     0x7412e4: add             x0, x0, HEAP, lsl #32
    // 0x7412e8: ldur            x16, [fp, #-0x18]
    // 0x7412ec: ldur            lr, [fp, #-0x50]
    // 0x7412f0: stp             lr, x16, [SP, #0x10]
    // 0x7412f4: ldur            x16, [fp, #-0x28]
    // 0x7412f8: stp             x0, x16, [SP]
    // 0x7412fc: ldur            x0, [fp, #-0x18]
    // 0x741300: ClosureCall
    //     0x741300: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0x741304: ldur            x2, [x0, #0x1f]
    //     0x741308: blr             x2
    // 0x74130c: cmp             w0, NULL
    // 0x741310: b.ne            #0x741344
    // 0x741314: ldur            d0, [fp, #-0x80]
    // 0x741318: r0 = inline_Allocate_Double()
    //     0x741318: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74131c: add             x0, x0, #0x10
    //     0x741320: cmp             x1, x0
    //     0x741324: b.ls            #0x7418ac
    //     0x741328: str             x0, [THR, #0x50]  ; THR::top
    //     0x74132c: sub             x0, x0, #0xf
    //     0x741330: movz            x1, #0xe15c
    //     0x741334: movk            x1, #0x3, lsl #16
    //     0x741338: stur            x1, [x0, #-1]
    // 0x74133c: StoreField: r0->field_7 = d0
    //     0x74133c: stur            d0, [x0, #7]
    // 0x741340: b               #0x741348
    // 0x741344: ldur            d0, [fp, #-0x80]
    // 0x741348: ldur            x1, [fp, #-0x10]
    // 0x74134c: stur            x0, [fp, #-0x18]
    // 0x741350: LoadField: r2 = r1->field_5b
    //     0x741350: ldur            w2, [x1, #0x5b]
    // 0x741354: DecompressPointer r2
    //     0x741354: add             x2, x2, HEAP, lsl #32
    // 0x741358: tbnz            w2, #4, #0x741364
    // 0x74135c: d1 = 28.000000
    //     0x74135c: fmov            d1, #28.00000000
    // 0x741360: b               #0x74136c
    // 0x741364: d1 = 32.000000
    //     0x741364: add             x17, PP, #0x2c, lsl #12  ; [pp+0x2c260] IMM: double(32) from 0x4040000000000000
    //     0x741368: ldr             d1, [x17, #0x260]
    // 0x74136c: r2 = inline_Allocate_Double()
    //     0x74136c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x741370: add             x2, x2, #0x10
    //     0x741374: cmp             x3, x2
    //     0x741378: b.ls            #0x7418bc
    //     0x74137c: str             x2, [THR, #0x50]  ; THR::top
    //     0x741380: sub             x2, x2, #0xf
    //     0x741384: movz            x3, #0xe15c
    //     0x741388: movk            x3, #0x3, lsl #16
    //     0x74138c: stur            x3, [x2, #-1]
    // 0x741390: StoreField: r2->field_7 = d1
    //     0x741390: stur            d1, [x2, #7]
    // 0x741394: ldur            x16, [fp, #-0x20]
    // 0x741398: stp             x16, x2, [SP]
    // 0x74139c: r0 = -()
    //     0x74139c: bl              #0xebf790  ; [dart:core] _Double::-
    // 0x7413a0: ldur            x1, [fp, #-0x10]
    // 0x7413a4: stur            x0, [fp, #-0x20]
    // 0x7413a8: LoadField: r2 = r1->field_5b
    //     0x7413a8: ldur            w2, [x1, #0x5b]
    // 0x7413ac: DecompressPointer r2
    //     0x7413ac: add             x2, x2, HEAP, lsl #32
    // 0x7413b0: tbnz            w2, #4, #0x7413bc
    // 0x7413b4: d2 = 48.000000
    //     0x7413b4: ldr             d2, [PP, #0x6e10]  ; [pp+0x6e10] IMM: double(48) from 0x4048000000000000
    // 0x7413b8: b               #0x7413c4
    // 0x7413bc: d2 = 52.000000
    //     0x7413bc: add             x17, PP, #0x33, lsl #12  ; [pp+0x33ce8] IMM: double(52) from 0x404a000000000000
    //     0x7413c0: ldr             d2, [x17, #0xce8]
    // 0x7413c4: ldur            d0, [fp, #-0x78]
    // 0x7413c8: d1 = 2.000000
    //     0x7413c8: fmov            d1, #2.00000000
    // 0x7413cc: LoadField: r2 = r1->field_5f
    //     0x7413cc: ldur            w2, [x1, #0x5f]
    // 0x7413d0: DecompressPointer r2
    //     0x7413d0: add             x2, x2, HEAP, lsl #32
    // 0x7413d4: LoadField: d3 = r2->field_f
    //     0x7413d4: ldur            d3, [x2, #0xf]
    // 0x7413d8: fmul            d4, d3, d1
    // 0x7413dc: fadd            d3, d2, d4
    // 0x7413e0: r2 = inline_Allocate_Double()
    //     0x7413e0: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x7413e4: add             x2, x2, #0x10
    //     0x7413e8: cmp             x3, x2
    //     0x7413ec: b.ls            #0x7418d8
    //     0x7413f0: str             x2, [THR, #0x50]  ; THR::top
    //     0x7413f4: sub             x2, x2, #0xf
    //     0x7413f8: movz            x3, #0xe15c
    //     0x7413fc: movk            x3, #0x3, lsl #16
    //     0x741400: stur            x3, [x2, #-1]
    // 0x741404: StoreField: r2->field_7 = d3
    //     0x741404: stur            d3, [x2, #7]
    // 0x741408: ldur            x16, [fp, #-0x18]
    // 0x74140c: stp             x16, x2, [SP]
    // 0x741410: r0 = -()
    //     0x741410: bl              #0xebf790  ; [dart:core] _Double::-
    // 0x741414: mov             x1, x0
    // 0x741418: ldur            x0, [fp, #-0x20]
    // 0x74141c: LoadField: d0 = r0->field_7
    //     0x74141c: ldur            d0, [x0, #7]
    // 0x741420: ldur            d1, [fp, #-0x78]
    // 0x741424: stur            d0, [fp, #-0x90]
    // 0x741428: fadd            d2, d0, d1
    // 0x74142c: LoadField: d3 = r1->field_7
    //     0x74142c: ldur            d3, [x1, #7]
    // 0x741430: stur            d3, [fp, #-0x88]
    // 0x741434: fsub            d4, d2, d3
    // 0x741438: d2 = 0.000000
    //     0x741438: eor             v2.16b, v2.16b, v2.16b
    // 0x74143c: fcmp            d4, d2
    // 0x741440: b.le            #0x741470
    // 0x741444: r0 = inline_Allocate_Double()
    //     0x741444: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x741448: add             x0, x0, #0x10
    //     0x74144c: cmp             x1, x0
    //     0x741450: b.ls            #0x7418fc
    //     0x741454: str             x0, [THR, #0x50]  ; THR::top
    //     0x741458: sub             x0, x0, #0xf
    //     0x74145c: movz            x1, #0xe15c
    //     0x741460: movk            x1, #0x3, lsl #16
    //     0x741464: stur            x1, [x0, #-1]
    // 0x741468: StoreField: r0->field_7 = d4
    //     0x741468: stur            d4, [x0, #7]
    // 0x74146c: b               #0x7414c8
    // 0x741470: fcmp            d2, d4
    // 0x741474: b.le            #0x741480
    // 0x741478: r0 = 0
    //     0x741478: movz            x0, #0
    // 0x74147c: b               #0x7414c8
    // 0x741480: fcmp            d4, #0.0
    // 0x741484: b.vs            #0x7414a0
    // 0x741488: b.ne            #0x741494
    // 0x74148c: r0 = 0.000000
    //     0x74148c: fmov            x0, d4
    // 0x741490: cmp             x0, #0
    // 0x741494: b.ge            #0x7414a0
    // 0x741498: r0 = 0
    //     0x741498: movz            x0, #0
    // 0x74149c: b               #0x7414c8
    // 0x7414a0: r0 = inline_Allocate_Double()
    //     0x7414a0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7414a4: add             x0, x0, #0x10
    //     0x7414a8: cmp             x1, x0
    //     0x7414ac: b.ls            #0x741914
    //     0x7414b0: str             x0, [THR, #0x50]  ; THR::top
    //     0x7414b4: sub             x0, x0, #0xf
    //     0x7414b8: movz            x1, #0xe15c
    //     0x7414bc: movk            x1, #0x3, lsl #16
    //     0x7414c0: stur            x1, [x0, #-1]
    // 0x7414c4: StoreField: r0->field_7 = d4
    //     0x7414c4: stur            d4, [x0, #7]
    // 0x7414c8: ldur            x1, [fp, #-0x10]
    // 0x7414cc: r2 = 60
    //     0x7414cc: movz            x2, #0x3c
    // 0x7414d0: branchIfSmi(r0, 0x7414dc)
    //     0x7414d0: tbz             w0, #0, #0x7414dc
    // 0x7414d4: r2 = LoadClassIdInstr(r0)
    //     0x7414d4: ldur            x2, [x0, #-1]
    //     0x7414d8: ubfx            x2, x2, #0xc, #0x14
    // 0x7414dc: r16 = 4
    //     0x7414dc: movz            x16, #0x4
    // 0x7414e0: stp             x16, x0, [SP]
    // 0x7414e4: mov             x0, x2
    // 0x7414e8: r0 = GDT[cid_x0 + -0xff7]()
    //     0x7414e8: sub             lr, x0, #0xff7
    //     0x7414ec: ldr             lr, [x21, lr, lsl #3]
    //     0x7414f0: blr             lr
    // 0x7414f4: LoadField: d0 = r0->field_7
    //     0x7414f4: ldur            d0, [x0, #7]
    // 0x7414f8: ldur            d1, [fp, #-0x90]
    // 0x7414fc: fsub            d2, d1, d0
    // 0x741500: ldur            d1, [fp, #-0x88]
    // 0x741504: stur            d2, [fp, #-0x98]
    // 0x741508: fadd            d3, d1, d0
    // 0x74150c: ldur            x0, [fp, #-0x10]
    // 0x741510: stur            d3, [fp, #-0x90]
    // 0x741514: LoadField: d0 = r0->field_7b
    //     0x741514: ldur            d0, [x0, #0x7b]
    // 0x741518: fcmp            d0, d2
    // 0x74151c: b.le            #0x741528
    // 0x741520: r1 = true
    //     0x741520: add             x1, NULL, #0x20  ; true
    // 0x741524: b               #0x741578
    // 0x741528: ldur            d1, [fp, #-0x80]
    // 0x74152c: fadd            d4, d3, d1
    // 0x741530: fadd            d5, d4, d0
    // 0x741534: stur            d5, [fp, #-0x88]
    // 0x741538: LoadField: r1 = r0->field_8b
    //     0x741538: ldur            w1, [x0, #0x8b]
    // 0x74153c: DecompressPointer r1
    //     0x74153c: add             x1, x1, HEAP, lsl #32
    // 0x741540: cmp             w1, NULL
    // 0x741544: b.ne            #0x741558
    // 0x741548: mov             x1, x0
    // 0x74154c: r0 = _defaultTileHeight()
    //     0x74154c: bl              #0x741cc8  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_defaultTileHeight
    // 0x741550: mov             v1.16b, v0.16b
    // 0x741554: b               #0x741560
    // 0x741558: LoadField: d0 = r1->field_7
    //     0x741558: ldur            d0, [x1, #7]
    // 0x74155c: mov             v1.16b, v0.16b
    // 0x741560: ldur            d0, [fp, #-0x88]
    // 0x741564: fcmp            d0, d1
    // 0x741568: r16 = true
    //     0x741568: add             x16, NULL, #0x20  ; true
    // 0x74156c: r17 = false
    //     0x74156c: add             x17, NULL, #0x30  ; false
    // 0x741570: csel            x0, x16, x17, gt
    // 0x741574: mov             x1, x0
    // 0x741578: ldur            x0, [fp, #-8]
    // 0x74157c: stur            x1, [fp, #-0x18]
    // 0x741580: cmp             w0, NULL
    // 0x741584: b.ne            #0x741590
    // 0x741588: mov             x0, x1
    // 0x74158c: b               #0x7415f8
    // 0x741590: ldur            x2, [fp, #-0x58]
    // 0x741594: tbnz            w2, #4, #0x7415a0
    // 0x741598: ldur            d0, [fp, #-0x70]
    // 0x74159c: b               #0x7415a4
    // 0x7415a0: ldur            d0, [fp, #-0x68]
    // 0x7415a4: stur            d0, [fp, #-0xa0]
    // 0x7415a8: tbnz            w1, #4, #0x7415c4
    // 0x7415ac: ldur            x3, [fp, #-0x10]
    // 0x7415b0: ldur            d1, [fp, #-0x78]
    // 0x7415b4: LoadField: d2 = r3->field_7b
    //     0x7415b4: ldur            d2, [x3, #0x7b]
    // 0x7415b8: fadd            d3, d2, d1
    // 0x7415bc: mov             v2.16b, v3.16b
    // 0x7415c0: b               #0x7415d0
    // 0x7415c4: ldur            x3, [fp, #-0x10]
    // 0x7415c8: ldur            d1, [fp, #-0x78]
    // 0x7415cc: ldur            d2, [fp, #-0x90]
    // 0x7415d0: stur            d2, [fp, #-0x88]
    // 0x7415d4: r0 = Offset()
    //     0x7415d4: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7415d8: ldur            d0, [fp, #-0xa0]
    // 0x7415dc: StoreField: r0->field_7 = d0
    //     0x7415dc: stur            d0, [x0, #7]
    // 0x7415e0: ldur            d0, [fp, #-0x88]
    // 0x7415e4: StoreField: r0->field_f = d0
    //     0x7415e4: stur            d0, [x0, #0xf]
    // 0x7415e8: ldur            x1, [fp, #-0x50]
    // 0x7415ec: mov             x2, x0
    // 0x7415f0: r0 = _positionBox()
    //     0x7415f0: bl              #0x741c34  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_positionBox
    // 0x7415f4: ldur            x0, [fp, #-0x18]
    // 0x7415f8: tbnz            w0, #4, #0x741620
    // 0x7415fc: ldur            x2, [fp, #-0x10]
    // 0x741600: ldur            d1, [fp, #-0x80]
    // 0x741604: ldur            d0, [fp, #-0x78]
    // 0x741608: d2 = 2.000000
    //     0x741608: fmov            d2, #2.00000000
    // 0x74160c: LoadField: d3 = r2->field_7b
    //     0x74160c: ldur            d3, [x2, #0x7b]
    // 0x741610: fmul            d4, d3, d2
    // 0x741614: fadd            d2, d4, d0
    // 0x741618: fadd            d0, d2, d1
    // 0x74161c: b               #0x741630
    // 0x741620: ldur            x2, [fp, #-0x10]
    // 0x741624: mov             x1, x2
    // 0x741628: r0 = _targetTileHeight()
    //     0x741628: bl              #0x741bec  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_targetTileHeight
    // 0x74162c: ldur            x0, [fp, #-0x18]
    // 0x741630: tbnz            w0, #4, #0x741640
    // 0x741634: ldur            x0, [fp, #-0x10]
    // 0x741638: LoadField: d1 = r0->field_7b
    //     0x741638: ldur            d1, [x0, #0x7b]
    // 0x74163c: b               #0x741648
    // 0x741640: ldur            x0, [fp, #-0x10]
    // 0x741644: ldur            d1, [fp, #-0x98]
    // 0x741648: mov             v31.16b, v0.16b
    // 0x74164c: mov             v0.16b, v1.16b
    // 0x741650: mov             v1.16b, v31.16b
    // 0x741654: ldur            x1, [fp, #-8]
    // 0x741658: stur            d0, [fp, #-0x78]
    // 0x74165c: stur            d1, [fp, #-0x80]
    // 0x741660: cmp             w1, NULL
    // 0x741664: b.eq            #0x7417d4
    // 0x741668: ldur            x3, [fp, #-0x58]
    // 0x74166c: mov             x1, x0
    // 0x741670: r2 = Instance__ListTileSlot
    //     0x741670: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd0] Obj!_ListTileSlot@e365e1
    //     0x741674: ldr             x2, [x2, #0xcd0]
    // 0x741678: r0 = childForSlot()
    //     0x741678: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x74167c: stur            x0, [fp, #-8]
    // 0x741680: cmp             w0, NULL
    // 0x741684: b.eq            #0x74192c
    // 0x741688: ldur            x1, [fp, #-0x58]
    // 0x74168c: tbnz            w1, #4, #0x741698
    // 0x741690: ldur            d1, [fp, #-0x70]
    // 0x741694: b               #0x74169c
    // 0x741698: ldur            d1, [fp, #-0x68]
    // 0x74169c: ldur            d0, [fp, #-0x78]
    // 0x7416a0: ldur            x2, [fp, #-0x38]
    // 0x7416a4: stur            d1, [fp, #-0x68]
    // 0x7416a8: r0 = Offset()
    //     0x7416a8: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7416ac: ldur            d0, [fp, #-0x68]
    // 0x7416b0: StoreField: r0->field_7 = d0
    //     0x7416b0: stur            d0, [x0, #7]
    // 0x7416b4: ldur            d0, [fp, #-0x78]
    // 0x7416b8: StoreField: r0->field_f = d0
    //     0x7416b8: stur            d0, [x0, #0xf]
    // 0x7416bc: ldur            x1, [fp, #-8]
    // 0x7416c0: mov             x2, x0
    // 0x7416c4: r0 = _positionBox()
    //     0x7416c4: bl              #0x741c34  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_positionBox
    // 0x7416c8: ldur            x0, [fp, #-0x38]
    // 0x7416cc: cmp             w0, NULL
    // 0x7416d0: b.eq            #0x741750
    // 0x7416d4: ldur            x1, [fp, #-0x48]
    // 0x7416d8: cmp             w1, NULL
    // 0x7416dc: b.eq            #0x741750
    // 0x7416e0: ldur            x4, [fp, #-0x58]
    // 0x7416e4: tbnz            w4, #4, #0x7416f4
    // 0x7416e8: ldur            d2, [fp, #-0x60]
    // 0x7416ec: d3 = 0.000000
    //     0x7416ec: eor             v3.16b, v3.16b, v3.16b
    // 0x7416f0: b               #0x741704
    // 0x7416f4: ldur            d2, [fp, #-0x60]
    // 0x7416f8: LoadField: d0 = r1->field_7
    //     0x7416f8: ldur            d0, [x1, #7]
    // 0x7416fc: fsub            d1, d2, d0
    // 0x741700: mov             v3.16b, v1.16b
    // 0x741704: ldur            x5, [fp, #-0x10]
    // 0x741708: stur            d3, [fp, #-0x68]
    // 0x74170c: LoadField: r2 = r5->field_8f
    //     0x74170c: ldur            w2, [x5, #0x8f]
    // 0x741710: DecompressPointer r2
    //     0x741710: add             x2, x2, HEAP, lsl #32
    // 0x741714: LoadField: d0 = r1->field_f
    //     0x741714: ldur            d0, [x1, #0xf]
    // 0x741718: mov             x1, x2
    // 0x74171c: ldur            d1, [fp, #-0x80]
    // 0x741720: mov             x2, x5
    // 0x741724: r3 = true
    //     0x741724: add             x3, NULL, #0x20  ; true
    // 0x741728: r0 = _yOffsetFor()
    //     0x741728: bl              #0x741980  ; [package:flutter/src/material/list_tile.dart] ListTileTitleAlignment::_yOffsetFor
    // 0x74172c: stur            d0, [fp, #-0x70]
    // 0x741730: r0 = Offset()
    //     0x741730: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x741734: ldur            d0, [fp, #-0x68]
    // 0x741738: StoreField: r0->field_7 = d0
    //     0x741738: stur            d0, [x0, #7]
    // 0x74173c: ldur            d0, [fp, #-0x70]
    // 0x741740: StoreField: r0->field_f = d0
    //     0x741740: stur            d0, [x0, #0xf]
    // 0x741744: ldur            x1, [fp, #-0x38]
    // 0x741748: mov             x2, x0
    // 0x74174c: r0 = _positionBox()
    //     0x74174c: bl              #0x741c34  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_positionBox
    // 0x741750: ldur            x0, [fp, #-0x40]
    // 0x741754: cmp             w0, NULL
    // 0x741758: b.eq            #0x7417d4
    // 0x74175c: ldur            x1, [fp, #-0x30]
    // 0x741760: cmp             w1, NULL
    // 0x741764: b.eq            #0x7417d4
    // 0x741768: ldur            x2, [fp, #-0x58]
    // 0x74176c: tbnz            w2, #4, #0x741784
    // 0x741770: ldur            d2, [fp, #-0x60]
    // 0x741774: LoadField: d0 = r1->field_7
    //     0x741774: ldur            d0, [x1, #7]
    // 0x741778: fsub            d1, d2, d0
    // 0x74177c: mov             v3.16b, v1.16b
    // 0x741780: b               #0x74178c
    // 0x741784: ldur            d2, [fp, #-0x60]
    // 0x741788: d3 = 0.000000
    //     0x741788: eor             v3.16b, v3.16b, v3.16b
    // 0x74178c: ldur            x2, [fp, #-0x10]
    // 0x741790: stur            d3, [fp, #-0x68]
    // 0x741794: LoadField: r3 = r2->field_8f
    //     0x741794: ldur            w3, [x2, #0x8f]
    // 0x741798: DecompressPointer r3
    //     0x741798: add             x3, x3, HEAP, lsl #32
    // 0x74179c: LoadField: d0 = r1->field_f
    //     0x74179c: ldur            d0, [x1, #0xf]
    // 0x7417a0: mov             x1, x3
    // 0x7417a4: ldur            d1, [fp, #-0x80]
    // 0x7417a8: r3 = false
    //     0x7417a8: add             x3, NULL, #0x30  ; false
    // 0x7417ac: r0 = _yOffsetFor()
    //     0x7417ac: bl              #0x741980  ; [package:flutter/src/material/list_tile.dart] ListTileTitleAlignment::_yOffsetFor
    // 0x7417b0: stur            d0, [fp, #-0x70]
    // 0x7417b4: r0 = Offset()
    //     0x7417b4: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7417b8: ldur            d0, [fp, #-0x68]
    // 0x7417bc: StoreField: r0->field_7 = d0
    //     0x7417bc: stur            d0, [x0, #7]
    // 0x7417c0: ldur            d0, [fp, #-0x70]
    // 0x7417c4: StoreField: r0->field_f = d0
    //     0x7417c4: stur            d0, [x0, #0xf]
    // 0x7417c8: ldur            x1, [fp, #-0x40]
    // 0x7417cc: mov             x2, x0
    // 0x7417d0: r0 = _positionBox()
    //     0x7417d0: bl              #0x741c34  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_positionBox
    // 0x7417d4: ldur            d0, [fp, #-0x60]
    // 0x7417d8: ldur            d1, [fp, #-0x78]
    // 0x7417dc: ldur            d2, [fp, #-0x80]
    // 0x7417e0: r0 = Size()
    //     0x7417e0: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x7417e4: ldur            d0, [fp, #-0x60]
    // 0x7417e8: StoreField: r0->field_7 = d0
    //     0x7417e8: stur            d0, [x0, #7]
    // 0x7417ec: ldur            d0, [fp, #-0x80]
    // 0x7417f0: StoreField: r0->field_f = d0
    //     0x7417f0: stur            d0, [x0, #0xf]
    // 0x7417f4: ldur            d0, [fp, #-0x78]
    // 0x7417f8: r4 = inline_Allocate_Double()
    //     0x7417f8: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0x7417fc: add             x4, x4, #0x10
    //     0x741800: cmp             x1, x4
    //     0x741804: b.ls            #0x741930
    //     0x741808: str             x4, [THR, #0x50]  ; THR::top
    //     0x74180c: sub             x4, x4, #0xf
    //     0x741810: movz            x1, #0xe15c
    //     0x741814: movk            x1, #0x3, lsl #16
    //     0x741818: stur            x1, [x4, #-1]
    // 0x74181c: StoreField: r4->field_7 = d0
    //     0x74181c: stur            d0, [x4, #7]
    // 0x741820: ldur            x2, [fp, #-0x28]
    // 0x741824: mov             x3, x0
    // 0x741828: r1 = 917510
    //     0x741828: movz            x1, #0x6
    //     0x74182c: movk            x1, #0xe, lsl #16
    // 0x741830: r0 = AllocateRecord3Named()
    //     0x741830: bl              #0xec0d88  ; AllocateRecord3NamedStub
    // 0x741834: LeaveFrame
    //     0x741834: mov             SP, fp
    //     0x741838: ldp             fp, lr, [SP], #0x10
    // 0x74183c: ret
    //     0x74183c: ret             
    // 0x741840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x741840: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x741844: b               #0x740e84
    // 0x741848: stp             q3, q4, [SP, #-0x20]!
    // 0x74184c: stp             q1, q2, [SP, #-0x20]!
    // 0x741850: SaveReg d0
    //     0x741850: str             q0, [SP, #-0x10]!
    // 0x741854: SaveReg r1
    //     0x741854: str             x1, [SP, #-8]!
    // 0x741858: r0 = AllocateDouble()
    //     0x741858: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74185c: RestoreReg r1
    //     0x74185c: ldr             x1, [SP], #8
    // 0x741860: RestoreReg d0
    //     0x741860: ldr             q0, [SP], #0x10
    // 0x741864: ldp             q1, q2, [SP], #0x20
    // 0x741868: ldp             q3, q4, [SP], #0x20
    // 0x74186c: b               #0x7410b8
    // 0x741870: stp             q2, q3, [SP, #-0x20]!
    // 0x741874: stp             q0, q1, [SP, #-0x20]!
    // 0x741878: stp             x0, x1, [SP, #-0x10]!
    // 0x74187c: r0 = AllocateDouble()
    //     0x74187c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x741880: mov             x2, x0
    // 0x741884: ldp             x0, x1, [SP], #0x10
    // 0x741888: ldp             q0, q1, [SP], #0x20
    // 0x74188c: ldp             q2, q3, [SP], #0x20
    // 0x741890: b               #0x7410e0
    // 0x741894: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x741894: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x741898: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x741898: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x74189c: SaveReg d0
    //     0x74189c: str             q0, [SP, #-0x10]!
    // 0x7418a0: r0 = AllocateDouble()
    //     0x7418a0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7418a4: RestoreReg d0
    //     0x7418a4: ldr             q0, [SP], #0x10
    // 0x7418a8: b               #0x7412c4
    // 0x7418ac: SaveReg d0
    //     0x7418ac: str             q0, [SP, #-0x10]!
    // 0x7418b0: r0 = AllocateDouble()
    //     0x7418b0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7418b4: RestoreReg d0
    //     0x7418b4: ldr             q0, [SP], #0x10
    // 0x7418b8: b               #0x74133c
    // 0x7418bc: stp             q0, q1, [SP, #-0x20]!
    // 0x7418c0: stp             x0, x1, [SP, #-0x10]!
    // 0x7418c4: r0 = AllocateDouble()
    //     0x7418c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7418c8: mov             x2, x0
    // 0x7418cc: ldp             x0, x1, [SP], #0x10
    // 0x7418d0: ldp             q0, q1, [SP], #0x20
    // 0x7418d4: b               #0x741390
    // 0x7418d8: stp             q1, q3, [SP, #-0x20]!
    // 0x7418dc: SaveReg d0
    //     0x7418dc: str             q0, [SP, #-0x10]!
    // 0x7418e0: stp             x0, x1, [SP, #-0x10]!
    // 0x7418e4: r0 = AllocateDouble()
    //     0x7418e4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7418e8: mov             x2, x0
    // 0x7418ec: ldp             x0, x1, [SP], #0x10
    // 0x7418f0: RestoreReg d0
    //     0x7418f0: ldr             q0, [SP], #0x10
    // 0x7418f4: ldp             q1, q3, [SP], #0x20
    // 0x7418f8: b               #0x741404
    // 0x7418fc: stp             q3, q4, [SP, #-0x20]!
    // 0x741900: stp             q0, q1, [SP, #-0x20]!
    // 0x741904: r0 = AllocateDouble()
    //     0x741904: bl              #0xec2254  ; AllocateDoubleStub
    // 0x741908: ldp             q0, q1, [SP], #0x20
    // 0x74190c: ldp             q3, q4, [SP], #0x20
    // 0x741910: b               #0x741468
    // 0x741914: stp             q3, q4, [SP, #-0x20]!
    // 0x741918: stp             q0, q1, [SP, #-0x20]!
    // 0x74191c: r0 = AllocateDouble()
    //     0x74191c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x741920: ldp             q0, q1, [SP], #0x20
    // 0x741924: ldp             q3, q4, [SP], #0x20
    // 0x741928: b               #0x7414c4
    // 0x74192c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74192c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x741930: SaveReg d0
    //     0x741930: str             q0, [SP, #-0x10]!
    // 0x741934: SaveReg r0
    //     0x741934: str             x0, [SP, #-8]!
    // 0x741938: r0 = AllocateDouble()
    //     0x741938: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74193c: mov             x4, x0
    // 0x741940: RestoreReg r0
    //     0x741940: ldr             x0, [SP], #8
    // 0x741944: RestoreReg d0
    //     0x741944: ldr             q0, [SP], #0x10
    // 0x741948: b               #0x74181c
  }
  [closure] static void _positionBox(dynamic, RenderBox, Offset) {
    // ** addr: 0x74194c, size: 0x34
    // 0x74194c: EnterFrame
    //     0x74194c: stp             fp, lr, [SP, #-0x10]!
    //     0x741950: mov             fp, SP
    // 0x741954: CheckStackOverflow
    //     0x741954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x741958: cmp             SP, x16
    //     0x74195c: b.ls            #0x741978
    // 0x741960: ldr             x1, [fp, #0x18]
    // 0x741964: ldr             x2, [fp, #0x10]
    // 0x741968: r0 = _positionBox()
    //     0x741968: bl              #0x741c34  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_positionBox
    // 0x74196c: LeaveFrame
    //     0x74196c: mov             SP, fp
    //     0x741970: ldp             fp, lr, [SP], #0x10
    // 0x741974: ret
    //     0x741974: ret             
    // 0x741978: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x741978: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74197c: b               #0x741960
  }
  get _ _targetTileHeight(/* No info */) {
    // ** addr: 0x741bec, size: 0x48
    // 0x741bec: EnterFrame
    //     0x741bec: stp             fp, lr, [SP, #-0x10]!
    //     0x741bf0: mov             fp, SP
    // 0x741bf4: CheckStackOverflow
    //     0x741bf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x741bf8: cmp             SP, x16
    //     0x741bfc: b.ls            #0x741c2c
    // 0x741c00: LoadField: r0 = r1->field_8b
    //     0x741c00: ldur            w0, [x1, #0x8b]
    // 0x741c04: DecompressPointer r0
    //     0x741c04: add             x0, x0, HEAP, lsl #32
    // 0x741c08: cmp             w0, NULL
    // 0x741c0c: b.ne            #0x741c18
    // 0x741c10: r0 = _defaultTileHeight()
    //     0x741c10: bl              #0x741cc8  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_defaultTileHeight
    // 0x741c14: b               #0x741c20
    // 0x741c18: LoadField: d1 = r0->field_7
    //     0x741c18: ldur            d1, [x0, #7]
    // 0x741c1c: mov             v0.16b, v1.16b
    // 0x741c20: LeaveFrame
    //     0x741c20: mov             SP, fp
    //     0x741c24: ldp             fp, lr, [SP], #0x10
    // 0x741c28: ret
    //     0x741c28: ret             
    // 0x741c2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x741c2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x741c30: b               #0x741c00
  }
  static _ _positionBox(/* No info */) {
    // ** addr: 0x741c34, size: 0x94
    // 0x741c34: EnterFrame
    //     0x741c34: stp             fp, lr, [SP, #-0x10]!
    //     0x741c38: mov             fp, SP
    // 0x741c3c: AllocStack(0x10)
    //     0x741c3c: sub             SP, SP, #0x10
    // 0x741c40: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x741c40: mov             x3, x2
    //     0x741c44: stur            x2, [fp, #-0x10]
    // 0x741c48: LoadField: r4 = r1->field_7
    //     0x741c48: ldur            w4, [x1, #7]
    // 0x741c4c: DecompressPointer r4
    //     0x741c4c: add             x4, x4, HEAP, lsl #32
    // 0x741c50: stur            x4, [fp, #-8]
    // 0x741c54: cmp             w4, NULL
    // 0x741c58: b.eq            #0x741cc4
    // 0x741c5c: mov             x0, x4
    // 0x741c60: r2 = Null
    //     0x741c60: mov             x2, NULL
    // 0x741c64: r1 = Null
    //     0x741c64: mov             x1, NULL
    // 0x741c68: r4 = LoadClassIdInstr(r0)
    //     0x741c68: ldur            x4, [x0, #-1]
    //     0x741c6c: ubfx            x4, x4, #0xc, #0x14
    // 0x741c70: sub             x4, x4, #0xc71
    // 0x741c74: cmp             x4, #0xf
    // 0x741c78: b.ls            #0x741c90
    // 0x741c7c: r8 = BoxParentData
    //     0x741c7c: add             x8, PP, #0x25, lsl #12  ; [pp+0x252c8] Type: BoxParentData
    //     0x741c80: ldr             x8, [x8, #0x2c8]
    // 0x741c84: r3 = Null
    //     0x741c84: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4dd60] Null
    //     0x741c88: ldr             x3, [x3, #0xd60]
    // 0x741c8c: r0 = DefaultTypeTest()
    //     0x741c8c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x741c90: ldur            x0, [fp, #-0x10]
    // 0x741c94: ldur            x1, [fp, #-8]
    // 0x741c98: StoreField: r1->field_7 = r0
    //     0x741c98: stur            w0, [x1, #7]
    //     0x741c9c: ldurb           w16, [x1, #-1]
    //     0x741ca0: ldurb           w17, [x0, #-1]
    //     0x741ca4: and             x16, x17, x16, lsr #2
    //     0x741ca8: tst             x16, HEAP, lsr #32
    //     0x741cac: b.eq            #0x741cb4
    //     0x741cb0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x741cb4: r0 = Null
    //     0x741cb4: mov             x0, NULL
    // 0x741cb8: LeaveFrame
    //     0x741cb8: mov             SP, fp
    //     0x741cbc: ldp             fp, lr, [SP], #0x10
    // 0x741cc0: ret
    //     0x741cc0: ret             
    // 0x741cc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x741cc4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _defaultTileHeight(/* No info */) {
    // ** addr: 0x741cc8, size: 0xb8
    // 0x741cc8: EnterFrame
    //     0x741cc8: stp             fp, lr, [SP, #-0x10]!
    //     0x741ccc: mov             fp, SP
    // 0x741cd0: AllocStack(0x10)
    //     0x741cd0: sub             SP, SP, #0x10
    // 0x741cd4: SetupParameters(_RenderListTile this /* r1 => r0, fp-0x8 */)
    //     0x741cd4: mov             x0, x1
    //     0x741cd8: stur            x1, [fp, #-8]
    // 0x741cdc: CheckStackOverflow
    //     0x741cdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x741ce0: cmp             SP, x16
    //     0x741ce4: b.ls            #0x741d78
    // 0x741ce8: LoadField: r1 = r0->field_5f
    //     0x741ce8: ldur            w1, [x0, #0x5f]
    // 0x741cec: DecompressPointer r1
    //     0x741cec: add             x1, x1, HEAP, lsl #32
    // 0x741cf0: r0 = baseSizeAdjustment()
    //     0x741cf0: bl              #0x7403f8  ; [package:flutter/src/material/theme_data.dart] VisualDensity::baseSizeAdjustment
    // 0x741cf4: LoadField: d0 = r0->field_f
    //     0x741cf4: ldur            d0, [x0, #0xf]
    // 0x741cf8: ldur            x1, [fp, #-8]
    // 0x741cfc: stur            d0, [fp, #-0x10]
    // 0x741d00: r2 = Instance__ListTileSlot
    //     0x741d00: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce0] Obj!_ListTileSlot@e365c1
    //     0x741d04: ldr             x2, [x2, #0xce0]
    // 0x741d08: r0 = childForSlot()
    //     0x741d08: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x741d0c: cmp             w0, NULL
    // 0x741d10: b.eq            #0x741d40
    // 0x741d14: ldur            x0, [fp, #-8]
    // 0x741d18: LoadField: r1 = r0->field_5b
    //     0x741d18: ldur            w1, [x0, #0x5b]
    // 0x741d1c: DecompressPointer r1
    //     0x741d1c: add             x1, x1, HEAP, lsl #32
    // 0x741d20: tbnz            w1, #4, #0x741d30
    // 0x741d24: d1 = 64.000000
    //     0x741d24: add             x17, PP, #0x25, lsl #12  ; [pp+0x25238] IMM: double(64) from 0x4050000000000000
    //     0x741d28: ldr             d1, [x17, #0x238]
    // 0x741d2c: b               #0x741d38
    // 0x741d30: d1 = 72.000000
    //     0x741d30: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e378] IMM: double(72) from 0x4052000000000000
    //     0x741d34: ldr             d1, [x17, #0x378]
    // 0x741d38: mov             v2.16b, v1.16b
    // 0x741d3c: b               #0x741d64
    // 0x741d40: ldur            x0, [fp, #-8]
    // 0x741d44: LoadField: r1 = r0->field_5b
    //     0x741d44: ldur            w1, [x0, #0x5b]
    // 0x741d48: DecompressPointer r1
    //     0x741d48: add             x1, x1, HEAP, lsl #32
    // 0x741d4c: tbnz            w1, #4, #0x741d58
    // 0x741d50: d1 = 48.000000
    //     0x741d50: ldr             d1, [PP, #0x6e10]  ; [pp+0x6e10] IMM: double(48) from 0x4048000000000000
    // 0x741d54: b               #0x741d60
    // 0x741d58: d1 = 56.000000
    //     0x741d58: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0x741d5c: ldr             d1, [x17, #0xf60]
    // 0x741d60: mov             v2.16b, v1.16b
    // 0x741d64: ldur            d1, [fp, #-0x10]
    // 0x741d68: fadd            d0, d1, d2
    // 0x741d6c: LeaveFrame
    //     0x741d6c: mov             SP, fp
    //     0x741d70: ldp             fp, lr, [SP], #0x10
    // 0x741d74: ret
    //     0x741d74: ret             
    // 0x741d78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x741d78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x741d7c: b               #0x741ce8
  }
  get _ _effectiveHorizontalTitleGap(/* No info */) {
    // ** addr: 0x741d80, size: 0x20
    // 0x741d80: d1 = 2.000000
    //     0x741d80: fmov            d1, #2.00000000
    // 0x741d84: LoadField: d2 = r1->field_73
    //     0x741d84: ldur            d2, [x1, #0x73]
    // 0x741d88: LoadField: r0 = r1->field_5f
    //     0x741d88: ldur            w0, [x1, #0x5f]
    // 0x741d8c: DecompressPointer r0
    //     0x741d8c: add             x0, x0, HEAP, lsl #32
    // 0x741d90: LoadField: d3 = r0->field_7
    //     0x741d90: ldur            d3, [x0, #7]
    // 0x741d94: fmul            d4, d3, d1
    // 0x741d98: fadd            d0, d2, d4
    // 0x741d9c: ret
    //     0x741d9c: ret             
  }
  get _ maxIconHeightConstraint(/* No info */) {
    // ** addr: 0x741da0, size: 0x88
    // 0x741da0: EnterFrame
    //     0x741da0: stp             fp, lr, [SP, #-0x10]!
    //     0x741da4: mov             fp, SP
    // 0x741da8: AllocStack(0x10)
    //     0x741da8: sub             SP, SP, #0x10
    // 0x741dac: CheckStackOverflow
    //     0x741dac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x741db0: cmp             SP, x16
    //     0x741db4: b.ls            #0x741e20
    // 0x741db8: LoadField: r0 = r1->field_5b
    //     0x741db8: ldur            w0, [x1, #0x5b]
    // 0x741dbc: DecompressPointer r0
    //     0x741dbc: add             x0, x0, HEAP, lsl #32
    // 0x741dc0: tbnz            w0, #4, #0x741dcc
    // 0x741dc4: d0 = 48.000000
    //     0x741dc4: ldr             d0, [PP, #0x6e10]  ; [pp+0x6e10] IMM: double(48) from 0x4048000000000000
    // 0x741dc8: b               #0x741dd4
    // 0x741dcc: d0 = 56.000000
    //     0x741dcc: add             x17, PP, #0x26, lsl #12  ; [pp+0x26f60] IMM: double(56) from 0x404c000000000000
    //     0x741dd0: ldr             d0, [x17, #0xf60]
    // 0x741dd4: stur            d0, [fp, #-8]
    // 0x741dd8: LoadField: r0 = r1->field_5f
    //     0x741dd8: ldur            w0, [x1, #0x5f]
    // 0x741ddc: DecompressPointer r0
    //     0x741ddc: add             x0, x0, HEAP, lsl #32
    // 0x741de0: mov             x1, x0
    // 0x741de4: r0 = baseSizeAdjustment()
    //     0x741de4: bl              #0x7403f8  ; [package:flutter/src/material/theme_data.dart] VisualDensity::baseSizeAdjustment
    // 0x741de8: LoadField: d0 = r0->field_f
    //     0x741de8: ldur            d0, [x0, #0xf]
    // 0x741dec: ldur            d1, [fp, #-8]
    // 0x741df0: fadd            d2, d1, d0
    // 0x741df4: stur            d2, [fp, #-0x10]
    // 0x741df8: r0 = BoxConstraints()
    //     0x741df8: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x741dfc: StoreField: r0->field_7 = rZR
    //     0x741dfc: stur            xzr, [x0, #7]
    // 0x741e00: d0 = inf
    //     0x741e00: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x741e04: StoreField: r0->field_f = d0
    //     0x741e04: stur            d0, [x0, #0xf]
    // 0x741e08: ArrayStore: r0[0] = rZR  ; List_8
    //     0x741e08: stur            xzr, [x0, #0x17]
    // 0x741e0c: ldur            d0, [fp, #-0x10]
    // 0x741e10: StoreField: r0->field_1f = d0
    //     0x741e10: stur            d0, [x0, #0x1f]
    // 0x741e14: LeaveFrame
    //     0x741e14: mov             SP, fp
    //     0x741e18: ldp             fp, lr, [SP], #0x10
    // 0x741e1c: ret
    //     0x741e1c: ret             
    // 0x741e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x741e20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x741e24: b               #0x741db8
  }
  dynamic computeMinIntrinsicHeight(dynamic) {
    // ** addr: 0x749620, size: 0x24
    // 0x749620: EnterFrame
    //     0x749620: stp             fp, lr, [SP, #-0x10]!
    //     0x749624: mov             fp, SP
    // 0x749628: ldr             x2, [fp, #0x10]
    // 0x74962c: r1 = Function 'computeMinIntrinsicHeight':.
    //     0x74962c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d10] AnonymousClosure: (0x749644), in [package:flutter/src/material/list_tile.dart] _RenderListTile::computeMinIntrinsicHeight (0x7496b8)
    //     0x749630: ldr             x1, [x1, #0xd10]
    // 0x749634: r0 = AllocateClosure()
    //     0x749634: bl              #0xec1630  ; AllocateClosureStub
    // 0x749638: LeaveFrame
    //     0x749638: mov             SP, fp
    //     0x74963c: ldp             fp, lr, [SP], #0x10
    // 0x749640: ret
    //     0x749640: ret             
  }
  [closure] double computeMinIntrinsicHeight(dynamic, double) {
    // ** addr: 0x749644, size: 0x74
    // 0x749644: EnterFrame
    //     0x749644: stp             fp, lr, [SP, #-0x10]!
    //     0x749648: mov             fp, SP
    // 0x74964c: ldr             x0, [fp, #0x18]
    // 0x749650: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x749650: ldur            w1, [x0, #0x17]
    // 0x749654: DecompressPointer r1
    //     0x749654: add             x1, x1, HEAP, lsl #32
    // 0x749658: CheckStackOverflow
    //     0x749658: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74965c: cmp             SP, x16
    //     0x749660: b.ls            #0x7496a0
    // 0x749664: ldr             x2, [fp, #0x10]
    // 0x749668: r0 = computeMinIntrinsicHeight()
    //     0x749668: bl              #0x7496b8  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::computeMinIntrinsicHeight
    // 0x74966c: r0 = inline_Allocate_Double()
    //     0x74966c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x749670: add             x0, x0, #0x10
    //     0x749674: cmp             x1, x0
    //     0x749678: b.ls            #0x7496a8
    //     0x74967c: str             x0, [THR, #0x50]  ; THR::top
    //     0x749680: sub             x0, x0, #0xf
    //     0x749684: movz            x1, #0xe15c
    //     0x749688: movk            x1, #0x3, lsl #16
    //     0x74968c: stur            x1, [x0, #-1]
    // 0x749690: StoreField: r0->field_7 = d0
    //     0x749690: stur            d0, [x0, #7]
    // 0x749694: LeaveFrame
    //     0x749694: mov             SP, fp
    //     0x749698: ldp             fp, lr, [SP], #0x10
    // 0x74969c: ret
    //     0x74969c: ret             
    // 0x7496a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7496a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7496a4: b               #0x749664
    // 0x7496a8: SaveReg d0
    //     0x7496a8: str             q0, [SP, #-0x10]!
    // 0x7496ac: r0 = AllocateDouble()
    //     0x7496ac: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7496b0: RestoreReg d0
    //     0x7496b0: ldr             q0, [SP], #0x10
    // 0x7496b4: b               #0x749690
  }
  _ computeMinIntrinsicHeight(/* No info */) {
    // ** addr: 0x7496b8, size: 0x16c
    // 0x7496b8: EnterFrame
    //     0x7496b8: stp             fp, lr, [SP, #-0x10]!
    //     0x7496bc: mov             fp, SP
    // 0x7496c0: AllocStack(0x28)
    //     0x7496c0: sub             SP, SP, #0x28
    // 0x7496c4: SetupParameters(_RenderListTile this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x7496c4: mov             x0, x1
    //     0x7496c8: stur            x1, [fp, #-8]
    //     0x7496cc: stur            x2, [fp, #-0x10]
    // 0x7496d0: CheckStackOverflow
    //     0x7496d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7496d4: cmp             SP, x16
    //     0x7496d8: b.ls            #0x749808
    // 0x7496dc: LoadField: r1 = r0->field_8b
    //     0x7496dc: ldur            w1, [x0, #0x8b]
    // 0x7496e0: DecompressPointer r1
    //     0x7496e0: add             x1, x1, HEAP, lsl #32
    // 0x7496e4: cmp             w1, NULL
    // 0x7496e8: b.ne            #0x7496f8
    // 0x7496ec: mov             x1, x0
    // 0x7496f0: r0 = _defaultTileHeight()
    //     0x7496f0: bl              #0x741cc8  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_defaultTileHeight
    // 0x7496f4: b               #0x7496fc
    // 0x7496f8: LoadField: d0 = r1->field_7
    //     0x7496f8: ldur            d0, [x1, #7]
    // 0x7496fc: ldur            x0, [fp, #-0x10]
    // 0x749700: ldur            x1, [fp, #-8]
    // 0x749704: stur            d0, [fp, #-0x18]
    // 0x749708: r2 = Instance__ListTileSlot
    //     0x749708: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd0] Obj!_ListTileSlot@e365e1
    //     0x74970c: ldr             x2, [x2, #0xcd0]
    // 0x749710: r0 = childForSlot()
    //     0x749710: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x749714: cmp             w0, NULL
    // 0x749718: b.eq            #0x749810
    // 0x74971c: ldur            x1, [fp, #-0x10]
    // 0x749720: LoadField: d1 = r1->field_7
    //     0x749720: ldur            d1, [x1, #7]
    // 0x749724: mov             x1, x0
    // 0x749728: mov             v0.16b, v1.16b
    // 0x74972c: stur            d1, [fp, #-0x20]
    // 0x749730: r0 = getMinIntrinsicHeight()
    //     0x749730: bl              #0x73933c  ; [package:flutter/src/rendering/box.dart] RenderBox::getMinIntrinsicHeight
    // 0x749734: ldur            x1, [fp, #-8]
    // 0x749738: r2 = Instance__ListTileSlot
    //     0x749738: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce0] Obj!_ListTileSlot@e365c1
    //     0x74973c: ldr             x2, [x2, #0xce0]
    // 0x749740: stur            d0, [fp, #-0x28]
    // 0x749744: r0 = childForSlot()
    //     0x749744: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x749748: cmp             w0, NULL
    // 0x74974c: b.ne            #0x749758
    // 0x749750: r0 = Null
    //     0x749750: mov             x0, NULL
    // 0x749754: b               #0x74978c
    // 0x749758: mov             x1, x0
    // 0x74975c: ldur            d0, [fp, #-0x20]
    // 0x749760: r0 = getMinIntrinsicHeight()
    //     0x749760: bl              #0x73933c  ; [package:flutter/src/rendering/box.dart] RenderBox::getMinIntrinsicHeight
    // 0x749764: r0 = inline_Allocate_Double()
    //     0x749764: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x749768: add             x0, x0, #0x10
    //     0x74976c: cmp             x1, x0
    //     0x749770: b.ls            #0x749814
    //     0x749774: str             x0, [THR, #0x50]  ; THR::top
    //     0x749778: sub             x0, x0, #0xf
    //     0x74977c: movz            x1, #0xe15c
    //     0x749780: movk            x1, #0x3, lsl #16
    //     0x749784: stur            x1, [x0, #-1]
    // 0x749788: StoreField: r0->field_7 = d0
    //     0x749788: stur            d0, [x0, #7]
    // 0x74978c: cmp             w0, NULL
    // 0x749790: b.ne            #0x74979c
    // 0x749794: d3 = 0.000000
    //     0x749794: eor             v3.16b, v3.16b, v3.16b
    // 0x749798: b               #0x7497a4
    // 0x74979c: LoadField: d1 = r0->field_7
    //     0x74979c: ldur            d1, [x0, #7]
    // 0x7497a0: mov             v3.16b, v1.16b
    // 0x7497a4: ldur            d1, [fp, #-0x28]
    // 0x7497a8: ldur            d2, [fp, #-0x18]
    // 0x7497ac: fadd            d4, d1, d3
    // 0x7497b0: fcmp            d2, d4
    // 0x7497b4: b.le            #0x7497c0
    // 0x7497b8: mov             v0.16b, v2.16b
    // 0x7497bc: b               #0x7497fc
    // 0x7497c0: fcmp            d4, d2
    // 0x7497c4: b.le            #0x7497d0
    // 0x7497c8: mov             v0.16b, v4.16b
    // 0x7497cc: b               #0x7497fc
    // 0x7497d0: d1 = 0.000000
    //     0x7497d0: eor             v1.16b, v1.16b, v1.16b
    // 0x7497d4: fcmp            d2, d1
    // 0x7497d8: b.ne            #0x7497e8
    // 0x7497dc: fadd            d1, d2, d4
    // 0x7497e0: mov             v0.16b, v1.16b
    // 0x7497e4: b               #0x7497fc
    // 0x7497e8: fcmp            d4, d4
    // 0x7497ec: b.vc            #0x7497f8
    // 0x7497f0: mov             v0.16b, v4.16b
    // 0x7497f4: b               #0x7497fc
    // 0x7497f8: mov             v0.16b, v2.16b
    // 0x7497fc: LeaveFrame
    //     0x7497fc: mov             SP, fp
    //     0x749800: ldp             fp, lr, [SP], #0x10
    // 0x749804: ret
    //     0x749804: ret             
    // 0x749808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x749808: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74980c: b               #0x7496dc
    // 0x749810: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x749810: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x749814: SaveReg d0
    //     0x749814: str             q0, [SP, #-0x10]!
    // 0x749818: r0 = AllocateDouble()
    //     0x749818: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74981c: RestoreReg d0
    //     0x74981c: ldr             q0, [SP], #0x10
    // 0x749820: b               #0x749788
  }
  _ computeDistanceToActualBaseline(/* No info */) {
    // ** addr: 0x74be9c, size: 0xe4
    // 0x74be9c: EnterFrame
    //     0x74be9c: stp             fp, lr, [SP, #-0x10]!
    //     0x74bea0: mov             fp, SP
    // 0x74bea4: AllocStack(0x18)
    //     0x74bea4: sub             SP, SP, #0x18
    // 0x74bea8: SetupParameters(_RenderListTile this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x74bea8: mov             x3, x1
    //     0x74beac: mov             x0, x2
    //     0x74beb0: stur            x1, [fp, #-8]
    //     0x74beb4: stur            x2, [fp, #-0x10]
    // 0x74beb8: CheckStackOverflow
    //     0x74beb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74bebc: cmp             SP, x16
    //     0x74bec0: b.ls            #0x74bf6c
    // 0x74bec4: mov             x1, x3
    // 0x74bec8: r2 = Instance__ListTileSlot
    //     0x74bec8: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd0] Obj!_ListTileSlot@e365e1
    //     0x74becc: ldr             x2, [x2, #0xcd0]
    // 0x74bed0: r0 = childForSlot()
    //     0x74bed0: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x74bed4: cmp             w0, NULL
    // 0x74bed8: b.eq            #0x74bf74
    // 0x74bedc: LoadField: r3 = r0->field_7
    //     0x74bedc: ldur            w3, [x0, #7]
    // 0x74bee0: DecompressPointer r3
    //     0x74bee0: add             x3, x3, HEAP, lsl #32
    // 0x74bee4: stur            x3, [fp, #-0x18]
    // 0x74bee8: cmp             w3, NULL
    // 0x74beec: b.eq            #0x74bf78
    // 0x74bef0: mov             x0, x3
    // 0x74bef4: r2 = Null
    //     0x74bef4: mov             x2, NULL
    // 0x74bef8: r1 = Null
    //     0x74bef8: mov             x1, NULL
    // 0x74befc: r4 = LoadClassIdInstr(r0)
    //     0x74befc: ldur            x4, [x0, #-1]
    //     0x74bf00: ubfx            x4, x4, #0xc, #0x14
    // 0x74bf04: sub             x4, x4, #0xc71
    // 0x74bf08: cmp             x4, #0xf
    // 0x74bf0c: b.ls            #0x74bf24
    // 0x74bf10: r8 = BoxParentData
    //     0x74bf10: add             x8, PP, #0x25, lsl #12  ; [pp+0x252c8] Type: BoxParentData
    //     0x74bf14: ldr             x8, [x8, #0x2c8]
    // 0x74bf18: r3 = Null
    //     0x74bf18: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4dda8] Null
    //     0x74bf1c: ldr             x3, [x3, #0xda8]
    // 0x74bf20: r0 = DefaultTypeTest()
    //     0x74bf20: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x74bf24: ldur            x1, [fp, #-8]
    // 0x74bf28: r2 = Instance__ListTileSlot
    //     0x74bf28: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd0] Obj!_ListTileSlot@e365e1
    //     0x74bf2c: ldr             x2, [x2, #0xcd0]
    // 0x74bf30: r0 = childForSlot()
    //     0x74bf30: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x74bf34: cmp             w0, NULL
    // 0x74bf38: b.eq            #0x74bf7c
    // 0x74bf3c: mov             x1, x0
    // 0x74bf40: ldur            x2, [fp, #-0x10]
    // 0x74bf44: r0 = getDistanceToActualBaseline()
    //     0x74bf44: bl              #0x74b4d4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDistanceToActualBaseline
    // 0x74bf48: mov             x1, x0
    // 0x74bf4c: ldur            x0, [fp, #-0x18]
    // 0x74bf50: LoadField: r2 = r0->field_7
    //     0x74bf50: ldur            w2, [x0, #7]
    // 0x74bf54: DecompressPointer r2
    //     0x74bf54: add             x2, x2, HEAP, lsl #32
    // 0x74bf58: LoadField: d0 = r2->field_f
    //     0x74bf58: ldur            d0, [x2, #0xf]
    // 0x74bf5c: r0 = BaselineOffset.+()
    //     0x74bf5c: bl              #0x73d964  ; [package:flutter/src/rendering/box.dart] ::BaselineOffset.+
    // 0x74bf60: LeaveFrame
    //     0x74bf60: mov             SP, fp
    //     0x74bf64: ldp             fp, lr, [SP], #0x10
    // 0x74bf68: ret
    //     0x74bf68: ret             
    // 0x74bf6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74bf6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74bf70: b               #0x74bec4
    // 0x74bf74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74bf74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x74bf78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74bf78: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x74bf7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74bf7c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  dynamic computeMaxIntrinsicWidth(dynamic) {
    // ** addr: 0x74f338, size: 0x24
    // 0x74f338: EnterFrame
    //     0x74f338: stp             fp, lr, [SP, #-0x10]!
    //     0x74f33c: mov             fp, SP
    // 0x74f340: ldr             x2, [fp, #0x10]
    // 0x74f344: r1 = Function 'computeMaxIntrinsicWidth':.
    //     0x74f344: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d18] AnonymousClosure: (0x74f35c), in [package:flutter/src/material/list_tile.dart] _RenderListTile::computeMaxIntrinsicWidth (0x74f3d0)
    //     0x74f348: ldr             x1, [x1, #0xd18]
    // 0x74f34c: r0 = AllocateClosure()
    //     0x74f34c: bl              #0xec1630  ; AllocateClosureStub
    // 0x74f350: LeaveFrame
    //     0x74f350: mov             SP, fp
    //     0x74f354: ldp             fp, lr, [SP], #0x10
    // 0x74f358: ret
    //     0x74f358: ret             
  }
  [closure] double computeMaxIntrinsicWidth(dynamic, double) {
    // ** addr: 0x74f35c, size: 0x74
    // 0x74f35c: EnterFrame
    //     0x74f35c: stp             fp, lr, [SP, #-0x10]!
    //     0x74f360: mov             fp, SP
    // 0x74f364: ldr             x0, [fp, #0x18]
    // 0x74f368: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x74f368: ldur            w1, [x0, #0x17]
    // 0x74f36c: DecompressPointer r1
    //     0x74f36c: add             x1, x1, HEAP, lsl #32
    // 0x74f370: CheckStackOverflow
    //     0x74f370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74f374: cmp             SP, x16
    //     0x74f378: b.ls            #0x74f3b8
    // 0x74f37c: ldr             x2, [fp, #0x10]
    // 0x74f380: r0 = computeMaxIntrinsicWidth()
    //     0x74f380: bl              #0x74f3d0  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::computeMaxIntrinsicWidth
    // 0x74f384: r0 = inline_Allocate_Double()
    //     0x74f384: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74f388: add             x0, x0, #0x10
    //     0x74f38c: cmp             x1, x0
    //     0x74f390: b.ls            #0x74f3c0
    //     0x74f394: str             x0, [THR, #0x50]  ; THR::top
    //     0x74f398: sub             x0, x0, #0xf
    //     0x74f39c: movz            x1, #0xe15c
    //     0x74f3a0: movk            x1, #0x3, lsl #16
    //     0x74f3a4: stur            x1, [x0, #-1]
    // 0x74f3a8: StoreField: r0->field_7 = d0
    //     0x74f3a8: stur            d0, [x0, #7]
    // 0x74f3ac: LeaveFrame
    //     0x74f3ac: mov             SP, fp
    //     0x74f3b0: ldp             fp, lr, [SP], #0x10
    // 0x74f3b4: ret
    //     0x74f3b4: ret             
    // 0x74f3b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74f3b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74f3bc: b               #0x74f37c
    // 0x74f3c0: SaveReg d0
    //     0x74f3c0: str             q0, [SP, #-0x10]!
    // 0x74f3c4: r0 = AllocateDouble()
    //     0x74f3c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74f3c8: RestoreReg d0
    //     0x74f3c8: ldr             q0, [SP], #0x10
    // 0x74f3cc: b               #0x74f3a8
  }
  _ computeMaxIntrinsicWidth(/* No info */) {
    // ** addr: 0x74f3d0, size: 0x1e0
    // 0x74f3d0: EnterFrame
    //     0x74f3d0: stp             fp, lr, [SP, #-0x10]!
    //     0x74f3d4: mov             fp, SP
    // 0x74f3d8: AllocStack(0x28)
    //     0x74f3d8: sub             SP, SP, #0x28
    // 0x74f3dc: SetupParameters(_RenderListTile this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x74f3dc: mov             x3, x1
    //     0x74f3e0: mov             x0, x2
    //     0x74f3e4: stur            x1, [fp, #-8]
    //     0x74f3e8: stur            x2, [fp, #-0x10]
    // 0x74f3ec: CheckStackOverflow
    //     0x74f3ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74f3f0: cmp             SP, x16
    //     0x74f3f4: b.ls            #0x74f5a0
    // 0x74f3f8: mov             x1, x3
    // 0x74f3fc: r2 = Instance__ListTileSlot
    //     0x74f3fc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd8] Obj!_ListTileSlot@e36601
    //     0x74f400: ldr             x2, [x2, #0xcd8]
    // 0x74f404: r0 = childForSlot()
    //     0x74f404: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x74f408: cmp             w0, NULL
    // 0x74f40c: b.eq            #0x74f4b8
    // 0x74f410: ldur            x3, [fp, #-8]
    // 0x74f414: ldur            x0, [fp, #-0x10]
    // 0x74f418: mov             x1, x3
    // 0x74f41c: r2 = Instance__ListTileSlot
    //     0x74f41c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd8] Obj!_ListTileSlot@e36601
    //     0x74f420: ldr             x2, [x2, #0xcd8]
    // 0x74f424: r0 = childForSlot()
    //     0x74f424: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x74f428: cmp             w0, NULL
    // 0x74f42c: b.eq            #0x74f5a8
    // 0x74f430: ldur            x2, [fp, #-0x10]
    // 0x74f434: LoadField: d0 = r2->field_7
    //     0x74f434: ldur            d0, [x2, #7]
    // 0x74f438: mov             x1, x0
    // 0x74f43c: r0 = getMaxIntrinsicWidth()
    //     0x74f43c: bl              #0x72d4b4  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicWidth
    // 0x74f440: ldur            x0, [fp, #-8]
    // 0x74f444: LoadField: d1 = r0->field_83
    //     0x74f444: ldur            d1, [x0, #0x83]
    // 0x74f448: fcmp            d0, d1
    // 0x74f44c: b.le            #0x74f45c
    // 0x74f450: mov             v1.16b, v0.16b
    // 0x74f454: d2 = 0.000000
    //     0x74f454: eor             v2.16b, v2.16b, v2.16b
    // 0x74f458: b               #0x74f490
    // 0x74f45c: fcmp            d1, d0
    // 0x74f460: b.le            #0x74f46c
    // 0x74f464: d2 = 0.000000
    //     0x74f464: eor             v2.16b, v2.16b, v2.16b
    // 0x74f468: b               #0x74f490
    // 0x74f46c: d2 = 0.000000
    //     0x74f46c: eor             v2.16b, v2.16b, v2.16b
    // 0x74f470: fcmp            d0, d2
    // 0x74f474: b.ne            #0x74f484
    // 0x74f478: fadd            d3, d0, d1
    // 0x74f47c: mov             v1.16b, v3.16b
    // 0x74f480: b               #0x74f490
    // 0x74f484: fcmp            d1, d1
    // 0x74f488: b.vs            #0x74f490
    // 0x74f48c: mov             v1.16b, v0.16b
    // 0x74f490: d0 = 2.000000
    //     0x74f490: fmov            d0, #2.00000000
    // 0x74f494: LoadField: d3 = r0->field_73
    //     0x74f494: ldur            d3, [x0, #0x73]
    // 0x74f498: LoadField: r1 = r0->field_5f
    //     0x74f498: ldur            w1, [x0, #0x5f]
    // 0x74f49c: DecompressPointer r1
    //     0x74f49c: add             x1, x1, HEAP, lsl #32
    // 0x74f4a0: LoadField: d4 = r1->field_7
    //     0x74f4a0: ldur            d4, [x1, #7]
    // 0x74f4a4: fmul            d5, d4, d0
    // 0x74f4a8: fadd            d0, d3, d5
    // 0x74f4ac: fadd            d3, d1, d0
    // 0x74f4b0: mov             v0.16b, v3.16b
    // 0x74f4b4: b               #0x74f4c4
    // 0x74f4b8: ldur            x0, [fp, #-8]
    // 0x74f4bc: d2 = 0.000000
    //     0x74f4bc: eor             v2.16b, v2.16b, v2.16b
    // 0x74f4c0: d0 = 0.000000
    //     0x74f4c0: eor             v0.16b, v0.16b, v0.16b
    // 0x74f4c4: ldur            x3, [fp, #-0x10]
    // 0x74f4c8: mov             x1, x0
    // 0x74f4cc: stur            d0, [fp, #-0x18]
    // 0x74f4d0: r2 = Instance__ListTileSlot
    //     0x74f4d0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd0] Obj!_ListTileSlot@e365e1
    //     0x74f4d4: ldr             x2, [x2, #0xcd0]
    // 0x74f4d8: r0 = childForSlot()
    //     0x74f4d8: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x74f4dc: cmp             w0, NULL
    // 0x74f4e0: b.eq            #0x74f5ac
    // 0x74f4e4: ldur            x1, [fp, #-0x10]
    // 0x74f4e8: LoadField: d1 = r1->field_7
    //     0x74f4e8: ldur            d1, [x1, #7]
    // 0x74f4ec: mov             x1, x0
    // 0x74f4f0: mov             v0.16b, v1.16b
    // 0x74f4f4: stur            d1, [fp, #-0x20]
    // 0x74f4f8: r0 = _maxWidth()
    //     0x74f4f8: bl              #0x72f42c  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_maxWidth
    // 0x74f4fc: ldur            x1, [fp, #-8]
    // 0x74f500: r2 = Instance__ListTileSlot
    //     0x74f500: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce0] Obj!_ListTileSlot@e365c1
    //     0x74f504: ldr             x2, [x2, #0xce0]
    // 0x74f508: stur            d0, [fp, #-0x28]
    // 0x74f50c: r0 = childForSlot()
    //     0x74f50c: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x74f510: mov             x1, x0
    // 0x74f514: ldur            d0, [fp, #-0x20]
    // 0x74f518: r0 = _maxWidth()
    //     0x74f518: bl              #0x72f42c  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_maxWidth
    // 0x74f51c: mov             v1.16b, v0.16b
    // 0x74f520: ldur            d0, [fp, #-0x28]
    // 0x74f524: fcmp            d0, d1
    // 0x74f528: b.le            #0x74f534
    // 0x74f52c: mov             v1.16b, v0.16b
    // 0x74f530: b               #0x74f560
    // 0x74f534: fcmp            d1, d0
    // 0x74f538: b.gt            #0x74f560
    // 0x74f53c: d2 = 0.000000
    //     0x74f53c: eor             v2.16b, v2.16b, v2.16b
    // 0x74f540: fcmp            d0, d2
    // 0x74f544: b.ne            #0x74f554
    // 0x74f548: fadd            d2, d0, d1
    // 0x74f54c: mov             v1.16b, v2.16b
    // 0x74f550: b               #0x74f560
    // 0x74f554: fcmp            d1, d1
    // 0x74f558: b.vs            #0x74f560
    // 0x74f55c: mov             v1.16b, v0.16b
    // 0x74f560: ldur            d0, [fp, #-0x18]
    // 0x74f564: fadd            d2, d0, d1
    // 0x74f568: ldur            x1, [fp, #-8]
    // 0x74f56c: stur            d2, [fp, #-0x28]
    // 0x74f570: r2 = Instance__ListTileSlot
    //     0x74f570: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce8] Obj!_ListTileSlot@e365a1
    //     0x74f574: ldr             x2, [x2, #0xce8]
    // 0x74f578: r0 = childForSlot()
    //     0x74f578: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x74f57c: mov             x1, x0
    // 0x74f580: ldur            d0, [fp, #-0x20]
    // 0x74f584: r0 = _maxWidth()
    //     0x74f584: bl              #0x72f42c  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_maxWidth
    // 0x74f588: ldur            d1, [fp, #-0x28]
    // 0x74f58c: fadd            d2, d1, d0
    // 0x74f590: mov             v0.16b, v2.16b
    // 0x74f594: LeaveFrame
    //     0x74f594: mov             SP, fp
    //     0x74f598: ldp             fp, lr, [SP], #0x10
    // 0x74f59c: ret
    //     0x74f59c: ret             
    // 0x74f5a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74f5a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74f5a4: b               #0x74f3f8
    // 0x74f5a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74f5a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x74f5ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74f5ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] double computeMaxIntrinsicHeight(dynamic, double) {
    // ** addr: 0x752b9c, size: 0x78
    // 0x752b9c: EnterFrame
    //     0x752b9c: stp             fp, lr, [SP, #-0x10]!
    //     0x752ba0: mov             fp, SP
    // 0x752ba4: ldr             x0, [fp, #0x18]
    // 0x752ba8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x752ba8: ldur            w1, [x0, #0x17]
    // 0x752bac: DecompressPointer r1
    //     0x752bac: add             x1, x1, HEAP, lsl #32
    // 0x752bb0: CheckStackOverflow
    //     0x752bb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x752bb4: cmp             SP, x16
    //     0x752bb8: b.ls            #0x752bfc
    // 0x752bbc: ldr             x0, [fp, #0x10]
    // 0x752bc0: LoadField: d0 = r0->field_7
    //     0x752bc0: ldur            d0, [x0, #7]
    // 0x752bc4: r0 = getMinIntrinsicHeight()
    //     0x752bc4: bl              #0x73933c  ; [package:flutter/src/rendering/box.dart] RenderBox::getMinIntrinsicHeight
    // 0x752bc8: r0 = inline_Allocate_Double()
    //     0x752bc8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x752bcc: add             x0, x0, #0x10
    //     0x752bd0: cmp             x1, x0
    //     0x752bd4: b.ls            #0x752c04
    //     0x752bd8: str             x0, [THR, #0x50]  ; THR::top
    //     0x752bdc: sub             x0, x0, #0xf
    //     0x752be0: movz            x1, #0xe15c
    //     0x752be4: movk            x1, #0x3, lsl #16
    //     0x752be8: stur            x1, [x0, #-1]
    // 0x752bec: StoreField: r0->field_7 = d0
    //     0x752bec: stur            d0, [x0, #7]
    // 0x752bf0: LeaveFrame
    //     0x752bf0: mov             SP, fp
    //     0x752bf4: ldp             fp, lr, [SP], #0x10
    // 0x752bf8: ret
    //     0x752bf8: ret             
    // 0x752bfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x752bfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x752c00: b               #0x752bbc
    // 0x752c04: SaveReg d0
    //     0x752c04: str             q0, [SP, #-0x10]!
    // 0x752c08: r0 = AllocateDouble()
    //     0x752c08: bl              #0xec2254  ; AllocateDoubleStub
    // 0x752c0c: RestoreReg d0
    //     0x752c0c: ldr             q0, [SP], #0x10
    // 0x752c10: b               #0x752bec
  }
  dynamic computeMaxIntrinsicHeight(dynamic) {
    // ** addr: 0x752c14, size: 0x24
    // 0x752c14: EnterFrame
    //     0x752c14: stp             fp, lr, [SP, #-0x10]!
    //     0x752c18: mov             fp, SP
    // 0x752c1c: ldr             x2, [fp, #0x10]
    // 0x752c20: r1 = Function 'computeMaxIntrinsicHeight':.
    //     0x752c20: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d08] AnonymousClosure: (0x752b9c), of [package:flutter/src/material/list_tile.dart] _RenderListTile
    //     0x752c24: ldr             x1, [x1, #0xd08]
    // 0x752c28: r0 = AllocateClosure()
    //     0x752c28: bl              #0xec1630  ; AllocateClosureStub
    // 0x752c2c: LeaveFrame
    //     0x752c2c: mov             SP, fp
    //     0x752c30: ldp             fp, lr, [SP], #0x10
    // 0x752c34: ret
    //     0x752c34: ret             
  }
  _ computeDryLayout(/* No info */) {
    // ** addr: 0x755518, size: 0x60
    // 0x755518: EnterFrame
    //     0x755518: stp             fp, lr, [SP, #-0x10]!
    //     0x75551c: mov             fp, SP
    // 0x755520: AllocStack(0x8)
    //     0x755520: sub             SP, SP, #8
    // 0x755524: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x755524: mov             x0, x2
    //     0x755528: stur            x2, [fp, #-8]
    // 0x75552c: CheckStackOverflow
    //     0x75552c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x755530: cmp             SP, x16
    //     0x755534: b.ls            #0x755570
    // 0x755538: mov             x5, x0
    // 0x75553c: r2 = Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getDryBaseline': static.
    //     0x75553c: add             x2, PP, #0x45, lsl #12  ; [pp+0x45748] Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getDryBaseline': static. (0x7e54fb130f1c)
    //     0x755540: ldr             x2, [x2, #0x748]
    // 0x755544: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static.
    //     0x755544: add             x3, PP, #0x44, lsl #12  ; [pp+0x44d20] Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static. (0x7e54fb140c14)
    //     0x755548: ldr             x3, [x3, #0xd20]
    // 0x75554c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x75554c: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x755550: r0 = _computeSizes()
    //     0x755550: bl              #0x740e14  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_computeSizes
    // 0x755554: LoadField: r2 = r0->field_13
    //     0x755554: ldur            w2, [x0, #0x13]
    // 0x755558: DecompressPointer r2
    //     0x755558: add             x2, x2, HEAP, lsl #32
    // 0x75555c: ldur            x1, [fp, #-8]
    // 0x755560: r0 = constrain()
    //     0x755560: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x755564: LeaveFrame
    //     0x755564: mov             SP, fp
    //     0x755568: ldp             fp, lr, [SP], #0x10
    // 0x75556c: ret
    //     0x75556c: ret             
    // 0x755570: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x755570: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x755574: b               #0x755538
  }
  get _ children(/* No info */) {
    // ** addr: 0x762948, size: 0x304
    // 0x762948: EnterFrame
    //     0x762948: stp             fp, lr, [SP, #-0x10]!
    //     0x76294c: mov             fp, SP
    // 0x762950: AllocStack(0x28)
    //     0x762950: sub             SP, SP, #0x28
    // 0x762954: SetupParameters(_RenderListTile this /* r1 => r0, fp-0x8 */)
    //     0x762954: mov             x0, x1
    //     0x762958: stur            x1, [fp, #-8]
    // 0x76295c: CheckStackOverflow
    //     0x76295c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x762960: cmp             SP, x16
    //     0x762964: b.ls            #0x762c38
    // 0x762968: mov             x1, x0
    // 0x76296c: r2 = Instance__ListTileSlot
    //     0x76296c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd0] Obj!_ListTileSlot@e365e1
    //     0x762970: ldr             x2, [x2, #0xcd0]
    // 0x762974: r0 = childForSlot()
    //     0x762974: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x762978: r1 = <RenderBox>
    //     0x762978: add             x1, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x76297c: ldr             x1, [x1, #0x1d8]
    // 0x762980: r2 = 0
    //     0x762980: movz            x2, #0
    // 0x762984: stur            x0, [fp, #-0x10]
    // 0x762988: r0 = _GrowableList()
    //     0x762988: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x76298c: ldur            x1, [fp, #-8]
    // 0x762990: r2 = Instance__ListTileSlot
    //     0x762990: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd8] Obj!_ListTileSlot@e36601
    //     0x762994: ldr             x2, [x2, #0xcd8]
    // 0x762998: stur            x0, [fp, #-0x18]
    // 0x76299c: r0 = childForSlot()
    //     0x76299c: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x7629a0: cmp             w0, NULL
    // 0x7629a4: b.eq            #0x762a3c
    // 0x7629a8: ldur            x0, [fp, #-0x18]
    // 0x7629ac: ldur            x1, [fp, #-8]
    // 0x7629b0: r0 = leading()
    //     0x7629b0: bl              #0x72f3f8  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::leading
    // 0x7629b4: stur            x0, [fp, #-0x28]
    // 0x7629b8: cmp             w0, NULL
    // 0x7629bc: b.eq            #0x762c40
    // 0x7629c0: ldur            x2, [fp, #-0x18]
    // 0x7629c4: LoadField: r1 = r2->field_b
    //     0x7629c4: ldur            w1, [x2, #0xb]
    // 0x7629c8: LoadField: r3 = r2->field_f
    //     0x7629c8: ldur            w3, [x2, #0xf]
    // 0x7629cc: DecompressPointer r3
    //     0x7629cc: add             x3, x3, HEAP, lsl #32
    // 0x7629d0: LoadField: r4 = r3->field_b
    //     0x7629d0: ldur            w4, [x3, #0xb]
    // 0x7629d4: r3 = LoadInt32Instr(r1)
    //     0x7629d4: sbfx            x3, x1, #1, #0x1f
    // 0x7629d8: stur            x3, [fp, #-0x20]
    // 0x7629dc: r1 = LoadInt32Instr(r4)
    //     0x7629dc: sbfx            x1, x4, #1, #0x1f
    // 0x7629e0: cmp             x3, x1
    // 0x7629e4: b.ne            #0x7629f0
    // 0x7629e8: mov             x1, x2
    // 0x7629ec: r0 = _growToNextCapacity()
    //     0x7629ec: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7629f0: ldur            x2, [fp, #-0x18]
    // 0x7629f4: ldur            x3, [fp, #-0x20]
    // 0x7629f8: add             x0, x3, #1
    // 0x7629fc: lsl             x1, x0, #1
    // 0x762a00: StoreField: r2->field_b = r1
    //     0x762a00: stur            w1, [x2, #0xb]
    // 0x762a04: LoadField: r1 = r2->field_f
    //     0x762a04: ldur            w1, [x2, #0xf]
    // 0x762a08: DecompressPointer r1
    //     0x762a08: add             x1, x1, HEAP, lsl #32
    // 0x762a0c: ldur            x0, [fp, #-0x28]
    // 0x762a10: ArrayStore: r1[r3] = r0  ; List_4
    //     0x762a10: add             x25, x1, x3, lsl #2
    //     0x762a14: add             x25, x25, #0xf
    //     0x762a18: str             w0, [x25]
    //     0x762a1c: tbz             w0, #0, #0x762a38
    //     0x762a20: ldurb           w16, [x1, #-1]
    //     0x762a24: ldurb           w17, [x0, #-1]
    //     0x762a28: and             x16, x17, x16, lsr #2
    //     0x762a2c: tst             x16, HEAP, lsr #32
    //     0x762a30: b.eq            #0x762a38
    //     0x762a34: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x762a38: b               #0x762a40
    // 0x762a3c: ldur            x2, [fp, #-0x18]
    // 0x762a40: ldur            x0, [fp, #-0x10]
    // 0x762a44: cmp             w0, NULL
    // 0x762a48: b.eq            #0x762ac4
    // 0x762a4c: LoadField: r1 = r2->field_b
    //     0x762a4c: ldur            w1, [x2, #0xb]
    // 0x762a50: LoadField: r3 = r2->field_f
    //     0x762a50: ldur            w3, [x2, #0xf]
    // 0x762a54: DecompressPointer r3
    //     0x762a54: add             x3, x3, HEAP, lsl #32
    // 0x762a58: LoadField: r4 = r3->field_b
    //     0x762a58: ldur            w4, [x3, #0xb]
    // 0x762a5c: r3 = LoadInt32Instr(r1)
    //     0x762a5c: sbfx            x3, x1, #1, #0x1f
    // 0x762a60: stur            x3, [fp, #-0x20]
    // 0x762a64: r1 = LoadInt32Instr(r4)
    //     0x762a64: sbfx            x1, x4, #1, #0x1f
    // 0x762a68: cmp             x3, x1
    // 0x762a6c: b.ne            #0x762a78
    // 0x762a70: mov             x1, x2
    // 0x762a74: r0 = _growToNextCapacity()
    //     0x762a74: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x762a78: ldur            x3, [fp, #-0x18]
    // 0x762a7c: ldur            x2, [fp, #-0x20]
    // 0x762a80: add             x0, x2, #1
    // 0x762a84: lsl             x1, x0, #1
    // 0x762a88: StoreField: r3->field_b = r1
    //     0x762a88: stur            w1, [x3, #0xb]
    // 0x762a8c: LoadField: r1 = r3->field_f
    //     0x762a8c: ldur            w1, [x3, #0xf]
    // 0x762a90: DecompressPointer r1
    //     0x762a90: add             x1, x1, HEAP, lsl #32
    // 0x762a94: ldur            x0, [fp, #-0x10]
    // 0x762a98: ArrayStore: r1[r2] = r0  ; List_4
    //     0x762a98: add             x25, x1, x2, lsl #2
    //     0x762a9c: add             x25, x25, #0xf
    //     0x762aa0: str             w0, [x25]
    //     0x762aa4: tbz             w0, #0, #0x762ac0
    //     0x762aa8: ldurb           w16, [x1, #-1]
    //     0x762aac: ldurb           w17, [x0, #-1]
    //     0x762ab0: and             x16, x17, x16, lsr #2
    //     0x762ab4: tst             x16, HEAP, lsr #32
    //     0x762ab8: b.eq            #0x762ac0
    //     0x762abc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x762ac0: b               #0x762ac8
    // 0x762ac4: mov             x3, x2
    // 0x762ac8: ldur            x1, [fp, #-8]
    // 0x762acc: r2 = Instance__ListTileSlot
    //     0x762acc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce0] Obj!_ListTileSlot@e365c1
    //     0x762ad0: ldr             x2, [x2, #0xce0]
    // 0x762ad4: r0 = childForSlot()
    //     0x762ad4: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x762ad8: cmp             w0, NULL
    // 0x762adc: b.eq            #0x762b74
    // 0x762ae0: ldur            x0, [fp, #-0x18]
    // 0x762ae4: ldur            x1, [fp, #-8]
    // 0x762ae8: r0 = subtitle()
    //     0x762ae8: bl              #0x72f384  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::subtitle
    // 0x762aec: stur            x0, [fp, #-0x10]
    // 0x762af0: cmp             w0, NULL
    // 0x762af4: b.eq            #0x762c44
    // 0x762af8: ldur            x2, [fp, #-0x18]
    // 0x762afc: LoadField: r1 = r2->field_b
    //     0x762afc: ldur            w1, [x2, #0xb]
    // 0x762b00: LoadField: r3 = r2->field_f
    //     0x762b00: ldur            w3, [x2, #0xf]
    // 0x762b04: DecompressPointer r3
    //     0x762b04: add             x3, x3, HEAP, lsl #32
    // 0x762b08: LoadField: r4 = r3->field_b
    //     0x762b08: ldur            w4, [x3, #0xb]
    // 0x762b0c: r3 = LoadInt32Instr(r1)
    //     0x762b0c: sbfx            x3, x1, #1, #0x1f
    // 0x762b10: stur            x3, [fp, #-0x20]
    // 0x762b14: r1 = LoadInt32Instr(r4)
    //     0x762b14: sbfx            x1, x4, #1, #0x1f
    // 0x762b18: cmp             x3, x1
    // 0x762b1c: b.ne            #0x762b28
    // 0x762b20: mov             x1, x2
    // 0x762b24: r0 = _growToNextCapacity()
    //     0x762b24: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x762b28: ldur            x3, [fp, #-0x18]
    // 0x762b2c: ldur            x2, [fp, #-0x20]
    // 0x762b30: add             x0, x2, #1
    // 0x762b34: lsl             x1, x0, #1
    // 0x762b38: StoreField: r3->field_b = r1
    //     0x762b38: stur            w1, [x3, #0xb]
    // 0x762b3c: LoadField: r1 = r3->field_f
    //     0x762b3c: ldur            w1, [x3, #0xf]
    // 0x762b40: DecompressPointer r1
    //     0x762b40: add             x1, x1, HEAP, lsl #32
    // 0x762b44: ldur            x0, [fp, #-0x10]
    // 0x762b48: ArrayStore: r1[r2] = r0  ; List_4
    //     0x762b48: add             x25, x1, x2, lsl #2
    //     0x762b4c: add             x25, x25, #0xf
    //     0x762b50: str             w0, [x25]
    //     0x762b54: tbz             w0, #0, #0x762b70
    //     0x762b58: ldurb           w16, [x1, #-1]
    //     0x762b5c: ldurb           w17, [x0, #-1]
    //     0x762b60: and             x16, x17, x16, lsr #2
    //     0x762b64: tst             x16, HEAP, lsr #32
    //     0x762b68: b.eq            #0x762b70
    //     0x762b6c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x762b70: b               #0x762b78
    // 0x762b74: ldur            x3, [fp, #-0x18]
    // 0x762b78: ldur            x1, [fp, #-8]
    // 0x762b7c: r2 = Instance__ListTileSlot
    //     0x762b7c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce8] Obj!_ListTileSlot@e365a1
    //     0x762b80: ldr             x2, [x2, #0xce8]
    // 0x762b84: r0 = childForSlot()
    //     0x762b84: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x762b88: cmp             w0, NULL
    // 0x762b8c: b.eq            #0x762c24
    // 0x762b90: ldur            x0, [fp, #-0x18]
    // 0x762b94: ldur            x1, [fp, #-8]
    // 0x762b98: r0 = trailing()
    //     0x762b98: bl              #0x72f350  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::trailing
    // 0x762b9c: stur            x0, [fp, #-8]
    // 0x762ba0: cmp             w0, NULL
    // 0x762ba4: b.eq            #0x762c48
    // 0x762ba8: ldur            x2, [fp, #-0x18]
    // 0x762bac: LoadField: r1 = r2->field_b
    //     0x762bac: ldur            w1, [x2, #0xb]
    // 0x762bb0: LoadField: r3 = r2->field_f
    //     0x762bb0: ldur            w3, [x2, #0xf]
    // 0x762bb4: DecompressPointer r3
    //     0x762bb4: add             x3, x3, HEAP, lsl #32
    // 0x762bb8: LoadField: r4 = r3->field_b
    //     0x762bb8: ldur            w4, [x3, #0xb]
    // 0x762bbc: r3 = LoadInt32Instr(r1)
    //     0x762bbc: sbfx            x3, x1, #1, #0x1f
    // 0x762bc0: stur            x3, [fp, #-0x20]
    // 0x762bc4: r1 = LoadInt32Instr(r4)
    //     0x762bc4: sbfx            x1, x4, #1, #0x1f
    // 0x762bc8: cmp             x3, x1
    // 0x762bcc: b.ne            #0x762bd8
    // 0x762bd0: mov             x1, x2
    // 0x762bd4: r0 = _growToNextCapacity()
    //     0x762bd4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x762bd8: ldur            x2, [fp, #-0x18]
    // 0x762bdc: ldur            x3, [fp, #-0x20]
    // 0x762be0: add             x4, x3, #1
    // 0x762be4: lsl             x5, x4, #1
    // 0x762be8: StoreField: r2->field_b = r5
    //     0x762be8: stur            w5, [x2, #0xb]
    // 0x762bec: LoadField: r1 = r2->field_f
    //     0x762bec: ldur            w1, [x2, #0xf]
    // 0x762bf0: DecompressPointer r1
    //     0x762bf0: add             x1, x1, HEAP, lsl #32
    // 0x762bf4: ldur            x0, [fp, #-8]
    // 0x762bf8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x762bf8: add             x25, x1, x3, lsl #2
    //     0x762bfc: add             x25, x25, #0xf
    //     0x762c00: str             w0, [x25]
    //     0x762c04: tbz             w0, #0, #0x762c20
    //     0x762c08: ldurb           w16, [x1, #-1]
    //     0x762c0c: ldurb           w17, [x0, #-1]
    //     0x762c10: and             x16, x17, x16, lsr #2
    //     0x762c14: tst             x16, HEAP, lsr #32
    //     0x762c18: b.eq            #0x762c20
    //     0x762c1c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x762c20: b               #0x762c28
    // 0x762c24: ldur            x2, [fp, #-0x18]
    // 0x762c28: mov             x0, x2
    // 0x762c2c: LeaveFrame
    //     0x762c2c: mov             SP, fp
    //     0x762c30: ldp             fp, lr, [SP], #0x10
    // 0x762c34: ret
    //     0x762c34: ret             
    // 0x762c38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x762c38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x762c3c: b               #0x762968
    // 0x762c40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x762c40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x762c44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x762c44: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x762c48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x762c48: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x76fb74, size: 0x168
    // 0x76fb74: EnterFrame
    //     0x76fb74: stp             fp, lr, [SP, #-0x10]!
    //     0x76fb78: mov             fp, SP
    // 0x76fb7c: AllocStack(0x20)
    //     0x76fb7c: sub             SP, SP, #0x20
    // 0x76fb80: SetupParameters(_RenderListTile this /* r1 => r3, fp-0x10 */)
    //     0x76fb80: mov             x3, x1
    //     0x76fb84: stur            x1, [fp, #-0x10]
    // 0x76fb88: CheckStackOverflow
    //     0x76fb88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76fb8c: cmp             SP, x16
    //     0x76fb90: b.ls            #0x76fcd4
    // 0x76fb94: LoadField: r4 = r3->field_27
    //     0x76fb94: ldur            w4, [x3, #0x27]
    // 0x76fb98: DecompressPointer r4
    //     0x76fb98: add             x4, x4, HEAP, lsl #32
    // 0x76fb9c: stur            x4, [fp, #-8]
    // 0x76fba0: cmp             w4, NULL
    // 0x76fba4: b.eq            #0x76fc98
    // 0x76fba8: mov             x0, x4
    // 0x76fbac: r2 = Null
    //     0x76fbac: mov             x2, NULL
    // 0x76fbb0: r1 = Null
    //     0x76fbb0: mov             x1, NULL
    // 0x76fbb4: r4 = LoadClassIdInstr(r0)
    //     0x76fbb4: ldur            x4, [x0, #-1]
    //     0x76fbb8: ubfx            x4, x4, #0xc, #0x14
    // 0x76fbbc: sub             x4, x4, #0xc83
    // 0x76fbc0: cmp             x4, #1
    // 0x76fbc4: b.ls            #0x76fbd8
    // 0x76fbc8: r8 = BoxConstraints
    //     0x76fbc8: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76fbcc: r3 = Null
    //     0x76fbcc: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4dd30] Null
    //     0x76fbd0: ldr             x3, [x3, #0xd30]
    // 0x76fbd4: r0 = BoxConstraints()
    //     0x76fbd4: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76fbd8: r16 = Closure: (RenderBox, Offset) => void from Function '_positionBox@560247952': static.
    //     0x76fbd8: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4dd40] Closure: (RenderBox, Offset) => void from Function '_positionBox@560247952': static. (0x7e54fb14194c)
    //     0x76fbdc: ldr             x16, [x16, #0xd40]
    // 0x76fbe0: str             x16, [SP]
    // 0x76fbe4: ldur            x1, [fp, #-0x10]
    // 0x76fbe8: ldur            x5, [fp, #-8]
    // 0x76fbec: r2 = Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getBaseline': static.
    //     0x76fbec: add             x2, PP, #0x45, lsl #12  ; [pp+0x45620] Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getBaseline': static. (0x7e54fb16fb2c)
    //     0x76fbf0: ldr             x2, [x2, #0x620]
    // 0x76fbf4: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static.
    //     0x76fbf4: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b28] Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static. (0x7e54fb1673f8)
    //     0x76fbf8: ldr             x3, [x3, #0xb28]
    // 0x76fbfc: r4 = const [0, 0x5, 0x1, 0x4, positionChild, 0x4, null]
    //     0x76fbfc: add             x4, PP, #0x4d, lsl #12  ; [pp+0x4dd48] List(7) [0, 0x5, 0x1, 0x4, "positionChild", 0x4, Null]
    //     0x76fc00: ldr             x4, [x4, #0xd48]
    // 0x76fc04: r0 = _computeSizes()
    //     0x76fc04: bl              #0x740e14  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_computeSizes
    // 0x76fc08: LoadField: r3 = r0->field_13
    //     0x76fc08: ldur            w3, [x0, #0x13]
    // 0x76fc0c: DecompressPointer r3
    //     0x76fc0c: add             x3, x3, HEAP, lsl #32
    // 0x76fc10: ldur            x4, [fp, #-0x10]
    // 0x76fc14: stur            x3, [fp, #-0x18]
    // 0x76fc18: LoadField: r5 = r4->field_27
    //     0x76fc18: ldur            w5, [x4, #0x27]
    // 0x76fc1c: DecompressPointer r5
    //     0x76fc1c: add             x5, x5, HEAP, lsl #32
    // 0x76fc20: stur            x5, [fp, #-8]
    // 0x76fc24: cmp             w5, NULL
    // 0x76fc28: b.eq            #0x76fcb4
    // 0x76fc2c: mov             x0, x5
    // 0x76fc30: r2 = Null
    //     0x76fc30: mov             x2, NULL
    // 0x76fc34: r1 = Null
    //     0x76fc34: mov             x1, NULL
    // 0x76fc38: r4 = LoadClassIdInstr(r0)
    //     0x76fc38: ldur            x4, [x0, #-1]
    //     0x76fc3c: ubfx            x4, x4, #0xc, #0x14
    // 0x76fc40: sub             x4, x4, #0xc83
    // 0x76fc44: cmp             x4, #1
    // 0x76fc48: b.ls            #0x76fc5c
    // 0x76fc4c: r8 = BoxConstraints
    //     0x76fc4c: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76fc50: r3 = Null
    //     0x76fc50: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4dd50] Null
    //     0x76fc54: ldr             x3, [x3, #0xd50]
    // 0x76fc58: r0 = BoxConstraints()
    //     0x76fc58: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76fc5c: ldur            x1, [fp, #-8]
    // 0x76fc60: ldur            x2, [fp, #-0x18]
    // 0x76fc64: r0 = constrain()
    //     0x76fc64: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x76fc68: ldur            x1, [fp, #-0x10]
    // 0x76fc6c: StoreField: r1->field_53 = r0
    //     0x76fc6c: stur            w0, [x1, #0x53]
    //     0x76fc70: ldurb           w16, [x1, #-1]
    //     0x76fc74: ldurb           w17, [x0, #-1]
    //     0x76fc78: and             x16, x17, x16, lsr #2
    //     0x76fc7c: tst             x16, HEAP, lsr #32
    //     0x76fc80: b.eq            #0x76fc88
    //     0x76fc84: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76fc88: r0 = Null
    //     0x76fc88: mov             x0, NULL
    // 0x76fc8c: LeaveFrame
    //     0x76fc8c: mov             SP, fp
    //     0x76fc90: ldp             fp, lr, [SP], #0x10
    // 0x76fc94: ret
    //     0x76fc94: ret             
    // 0x76fc98: r0 = StateError()
    //     0x76fc98: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76fc9c: mov             x1, x0
    // 0x76fca0: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76fca0: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76fca4: StoreField: r1->field_b = r0
    //     0x76fca4: stur            w0, [x1, #0xb]
    // 0x76fca8: mov             x0, x1
    // 0x76fcac: r0 = Throw()
    //     0x76fcac: bl              #0xec04b8  ; ThrowStub
    // 0x76fcb0: brk             #0
    // 0x76fcb4: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76fcb4: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76fcb8: r0 = StateError()
    //     0x76fcb8: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76fcbc: mov             x1, x0
    // 0x76fcc0: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76fcc0: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76fcc4: StoreField: r1->field_b = r0
    //     0x76fcc4: stur            w0, [x1, #0xb]
    // 0x76fcc8: mov             x0, x1
    // 0x76fccc: r0 = Throw()
    //     0x76fccc: bl              #0xec04b8  ; ThrowStub
    // 0x76fcd0: brk             #0
    // 0x76fcd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76fcd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76fcd8: b               #0x76fb94
  }
  _ paint(/* No info */) {
    // ** addr: 0x798344, size: 0x278
    // 0x798344: EnterFrame
    //     0x798344: stp             fp, lr, [SP, #-0x10]!
    //     0x798348: mov             fp, SP
    // 0x79834c: AllocStack(0x28)
    //     0x79834c: sub             SP, SP, #0x28
    // 0x798350: SetupParameters(_RenderListTile this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x798350: mov             x4, x1
    //     0x798354: mov             x0, x3
    //     0x798358: stur            x3, [fp, #-0x18]
    //     0x79835c: mov             x3, x2
    //     0x798360: stur            x1, [fp, #-8]
    //     0x798364: stur            x2, [fp, #-0x10]
    // 0x798368: CheckStackOverflow
    //     0x798368: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79836c: cmp             SP, x16
    //     0x798370: b.ls            #0x7985a4
    // 0x798374: mov             x1, x4
    // 0x798378: r2 = Instance__ListTileSlot
    //     0x798378: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dcd8] Obj!_ListTileSlot@e36601
    //     0x79837c: ldr             x2, [x2, #0xcd8]
    // 0x798380: r0 = childForSlot()
    //     0x798380: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x798384: mov             x3, x0
    // 0x798388: stur            x3, [fp, #-0x28]
    // 0x79838c: cmp             w3, NULL
    // 0x798390: b.eq            #0x798400
    // 0x798394: LoadField: r4 = r3->field_7
    //     0x798394: ldur            w4, [x3, #7]
    // 0x798398: DecompressPointer r4
    //     0x798398: add             x4, x4, HEAP, lsl #32
    // 0x79839c: stur            x4, [fp, #-0x20]
    // 0x7983a0: cmp             w4, NULL
    // 0x7983a4: b.eq            #0x7985ac
    // 0x7983a8: mov             x0, x4
    // 0x7983ac: r2 = Null
    //     0x7983ac: mov             x2, NULL
    // 0x7983b0: r1 = Null
    //     0x7983b0: mov             x1, NULL
    // 0x7983b4: r4 = LoadClassIdInstr(r0)
    //     0x7983b4: ldur            x4, [x0, #-1]
    //     0x7983b8: ubfx            x4, x4, #0xc, #0x14
    // 0x7983bc: sub             x4, x4, #0xc71
    // 0x7983c0: cmp             x4, #0xf
    // 0x7983c4: b.ls            #0x7983dc
    // 0x7983c8: r8 = BoxParentData
    //     0x7983c8: add             x8, PP, #0x25, lsl #12  ; [pp+0x252c8] Type: BoxParentData
    //     0x7983cc: ldr             x8, [x8, #0x2c8]
    // 0x7983d0: r3 = Null
    //     0x7983d0: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4dcf0] Null
    //     0x7983d4: ldr             x3, [x3, #0xcf0]
    // 0x7983d8: r0 = DefaultTypeTest()
    //     0x7983d8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7983dc: ldur            x0, [fp, #-0x20]
    // 0x7983e0: LoadField: r1 = r0->field_7
    //     0x7983e0: ldur            w1, [x0, #7]
    // 0x7983e4: DecompressPointer r1
    //     0x7983e4: add             x1, x1, HEAP, lsl #32
    // 0x7983e8: ldur            x2, [fp, #-0x18]
    // 0x7983ec: r0 = +()
    //     0x7983ec: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x7983f0: ldur            x1, [fp, #-0x10]
    // 0x7983f4: ldur            x2, [fp, #-0x28]
    // 0x7983f8: mov             x3, x0
    // 0x7983fc: r0 = paintChild()
    //     0x7983fc: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x798400: ldur            x1, [fp, #-8]
    // 0x798404: r0 = title()
    //     0x798404: bl              #0x72f3b8  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::title
    // 0x798408: mov             x3, x0
    // 0x79840c: stur            x3, [fp, #-0x28]
    // 0x798410: LoadField: r4 = r3->field_7
    //     0x798410: ldur            w4, [x3, #7]
    // 0x798414: DecompressPointer r4
    //     0x798414: add             x4, x4, HEAP, lsl #32
    // 0x798418: stur            x4, [fp, #-0x20]
    // 0x79841c: cmp             w4, NULL
    // 0x798420: b.eq            #0x7985b0
    // 0x798424: mov             x0, x4
    // 0x798428: r2 = Null
    //     0x798428: mov             x2, NULL
    // 0x79842c: r1 = Null
    //     0x79842c: mov             x1, NULL
    // 0x798430: r4 = LoadClassIdInstr(r0)
    //     0x798430: ldur            x4, [x0, #-1]
    //     0x798434: ubfx            x4, x4, #0xc, #0x14
    // 0x798438: sub             x4, x4, #0xc71
    // 0x79843c: cmp             x4, #0xf
    // 0x798440: b.ls            #0x798458
    // 0x798444: r8 = BoxParentData
    //     0x798444: add             x8, PP, #0x25, lsl #12  ; [pp+0x252c8] Type: BoxParentData
    //     0x798448: ldr             x8, [x8, #0x2c8]
    // 0x79844c: r3 = Null
    //     0x79844c: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4dd00] Null
    //     0x798450: ldr             x3, [x3, #0xd00]
    // 0x798454: r0 = DefaultTypeTest()
    //     0x798454: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x798458: ldur            x0, [fp, #-0x20]
    // 0x79845c: LoadField: r1 = r0->field_7
    //     0x79845c: ldur            w1, [x0, #7]
    // 0x798460: DecompressPointer r1
    //     0x798460: add             x1, x1, HEAP, lsl #32
    // 0x798464: ldur            x2, [fp, #-0x18]
    // 0x798468: r0 = +()
    //     0x798468: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x79846c: ldur            x1, [fp, #-0x10]
    // 0x798470: ldur            x2, [fp, #-0x28]
    // 0x798474: mov             x3, x0
    // 0x798478: r0 = paintChild()
    //     0x798478: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x79847c: ldur            x1, [fp, #-8]
    // 0x798480: r2 = Instance__ListTileSlot
    //     0x798480: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce0] Obj!_ListTileSlot@e365c1
    //     0x798484: ldr             x2, [x2, #0xce0]
    // 0x798488: r0 = childForSlot()
    //     0x798488: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x79848c: mov             x3, x0
    // 0x798490: stur            x3, [fp, #-0x28]
    // 0x798494: cmp             w3, NULL
    // 0x798498: b.eq            #0x798508
    // 0x79849c: LoadField: r4 = r3->field_7
    //     0x79849c: ldur            w4, [x3, #7]
    // 0x7984a0: DecompressPointer r4
    //     0x7984a0: add             x4, x4, HEAP, lsl #32
    // 0x7984a4: stur            x4, [fp, #-0x20]
    // 0x7984a8: cmp             w4, NULL
    // 0x7984ac: b.eq            #0x7985b4
    // 0x7984b0: mov             x0, x4
    // 0x7984b4: r2 = Null
    //     0x7984b4: mov             x2, NULL
    // 0x7984b8: r1 = Null
    //     0x7984b8: mov             x1, NULL
    // 0x7984bc: r4 = LoadClassIdInstr(r0)
    //     0x7984bc: ldur            x4, [x0, #-1]
    //     0x7984c0: ubfx            x4, x4, #0xc, #0x14
    // 0x7984c4: sub             x4, x4, #0xc71
    // 0x7984c8: cmp             x4, #0xf
    // 0x7984cc: b.ls            #0x7984e4
    // 0x7984d0: r8 = BoxParentData
    //     0x7984d0: add             x8, PP, #0x25, lsl #12  ; [pp+0x252c8] Type: BoxParentData
    //     0x7984d4: ldr             x8, [x8, #0x2c8]
    // 0x7984d8: r3 = Null
    //     0x7984d8: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4dd10] Null
    //     0x7984dc: ldr             x3, [x3, #0xd10]
    // 0x7984e0: r0 = DefaultTypeTest()
    //     0x7984e0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7984e4: ldur            x0, [fp, #-0x20]
    // 0x7984e8: LoadField: r1 = r0->field_7
    //     0x7984e8: ldur            w1, [x0, #7]
    // 0x7984ec: DecompressPointer r1
    //     0x7984ec: add             x1, x1, HEAP, lsl #32
    // 0x7984f0: ldur            x2, [fp, #-0x18]
    // 0x7984f4: r0 = +()
    //     0x7984f4: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x7984f8: ldur            x1, [fp, #-0x10]
    // 0x7984fc: ldur            x2, [fp, #-0x28]
    // 0x798500: mov             x3, x0
    // 0x798504: r0 = paintChild()
    //     0x798504: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x798508: ldur            x1, [fp, #-8]
    // 0x79850c: r2 = Instance__ListTileSlot
    //     0x79850c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4dce8] Obj!_ListTileSlot@e365a1
    //     0x798510: ldr             x2, [x2, #0xce8]
    // 0x798514: r0 = childForSlot()
    //     0x798514: bl              #0x72eb64  ; [package:flutter/src/material/input_decorator.dart] __RenderDecoration&RenderBox&SlottedContainerRenderObjectMixin::childForSlot
    // 0x798518: mov             x3, x0
    // 0x79851c: stur            x3, [fp, #-0x20]
    // 0x798520: cmp             w3, NULL
    // 0x798524: b.eq            #0x798594
    // 0x798528: LoadField: r4 = r3->field_7
    //     0x798528: ldur            w4, [x3, #7]
    // 0x79852c: DecompressPointer r4
    //     0x79852c: add             x4, x4, HEAP, lsl #32
    // 0x798530: stur            x4, [fp, #-8]
    // 0x798534: cmp             w4, NULL
    // 0x798538: b.eq            #0x7985b8
    // 0x79853c: mov             x0, x4
    // 0x798540: r2 = Null
    //     0x798540: mov             x2, NULL
    // 0x798544: r1 = Null
    //     0x798544: mov             x1, NULL
    // 0x798548: r4 = LoadClassIdInstr(r0)
    //     0x798548: ldur            x4, [x0, #-1]
    //     0x79854c: ubfx            x4, x4, #0xc, #0x14
    // 0x798550: sub             x4, x4, #0xc71
    // 0x798554: cmp             x4, #0xf
    // 0x798558: b.ls            #0x798570
    // 0x79855c: r8 = BoxParentData
    //     0x79855c: add             x8, PP, #0x25, lsl #12  ; [pp+0x252c8] Type: BoxParentData
    //     0x798560: ldr             x8, [x8, #0x2c8]
    // 0x798564: r3 = Null
    //     0x798564: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4dd20] Null
    //     0x798568: ldr             x3, [x3, #0xd20]
    // 0x79856c: r0 = DefaultTypeTest()
    //     0x79856c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x798570: ldur            x0, [fp, #-8]
    // 0x798574: LoadField: r1 = r0->field_7
    //     0x798574: ldur            w1, [x0, #7]
    // 0x798578: DecompressPointer r1
    //     0x798578: add             x1, x1, HEAP, lsl #32
    // 0x79857c: ldur            x2, [fp, #-0x18]
    // 0x798580: r0 = +()
    //     0x798580: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x798584: ldur            x1, [fp, #-0x10]
    // 0x798588: ldur            x2, [fp, #-0x20]
    // 0x79858c: mov             x3, x0
    // 0x798590: r0 = paintChild()
    //     0x798590: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x798594: r0 = Null
    //     0x798594: mov             x0, NULL
    // 0x798598: LeaveFrame
    //     0x798598: mov             SP, fp
    //     0x79859c: ldp             fp, lr, [SP], #0x10
    // 0x7985a0: ret
    //     0x7985a0: ret             
    // 0x7985a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7985a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7985a8: b               #0x798374
    // 0x7985ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7985ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7985b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7985b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7985b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7985b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7985b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7985b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x7fd91c, size: 0x1e8
    // 0x7fd91c: EnterFrame
    //     0x7fd91c: stp             fp, lr, [SP, #-0x10]!
    //     0x7fd920: mov             fp, SP
    // 0x7fd924: AllocStack(0x48)
    //     0x7fd924: sub             SP, SP, #0x48
    // 0x7fd928: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0x7fd928: mov             x0, x3
    //     0x7fd92c: stur            x2, [fp, #-8]
    //     0x7fd930: stur            x3, [fp, #-0x10]
    // 0x7fd934: CheckStackOverflow
    //     0x7fd934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fd938: cmp             SP, x16
    //     0x7fd93c: b.ls            #0x7fdaf0
    // 0x7fd940: r0 = children()
    //     0x7fd940: bl              #0x762948  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::children
    // 0x7fd944: mov             x3, x0
    // 0x7fd948: stur            x3, [fp, #-0x38]
    // 0x7fd94c: LoadField: r4 = r3->field_7
    //     0x7fd94c: ldur            w4, [x3, #7]
    // 0x7fd950: DecompressPointer r4
    //     0x7fd950: add             x4, x4, HEAP, lsl #32
    // 0x7fd954: stur            x4, [fp, #-0x30]
    // 0x7fd958: LoadField: r0 = r3->field_b
    //     0x7fd958: ldur            w0, [x3, #0xb]
    // 0x7fd95c: r5 = LoadInt32Instr(r0)
    //     0x7fd95c: sbfx            x5, x0, #1, #0x1f
    // 0x7fd960: stur            x5, [fp, #-0x28]
    // 0x7fd964: r0 = 0
    //     0x7fd964: movz            x0, #0
    // 0x7fd968: CheckStackOverflow
    //     0x7fd968: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fd96c: cmp             SP, x16
    //     0x7fd970: b.ls            #0x7fdaf8
    // 0x7fd974: LoadField: r1 = r3->field_b
    //     0x7fd974: ldur            w1, [x3, #0xb]
    // 0x7fd978: r2 = LoadInt32Instr(r1)
    //     0x7fd978: sbfx            x2, x1, #1, #0x1f
    // 0x7fd97c: cmp             x5, x2
    // 0x7fd980: b.ne            #0x7fdad0
    // 0x7fd984: cmp             x0, x2
    // 0x7fd988: b.ge            #0x7fdac0
    // 0x7fd98c: LoadField: r1 = r3->field_f
    //     0x7fd98c: ldur            w1, [x3, #0xf]
    // 0x7fd990: DecompressPointer r1
    //     0x7fd990: add             x1, x1, HEAP, lsl #32
    // 0x7fd994: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0x7fd994: add             x16, x1, x0, lsl #2
    //     0x7fd998: ldur            w6, [x16, #0xf]
    // 0x7fd99c: DecompressPointer r6
    //     0x7fd99c: add             x6, x6, HEAP, lsl #32
    // 0x7fd9a0: stur            x6, [fp, #-0x20]
    // 0x7fd9a4: add             x7, x0, #1
    // 0x7fd9a8: stur            x7, [fp, #-0x18]
    // 0x7fd9ac: cmp             w6, NULL
    // 0x7fd9b0: b.ne            #0x7fd9e4
    // 0x7fd9b4: mov             x0, x6
    // 0x7fd9b8: mov             x2, x4
    // 0x7fd9bc: r1 = Null
    //     0x7fd9bc: mov             x1, NULL
    // 0x7fd9c0: cmp             w2, NULL
    // 0x7fd9c4: b.eq            #0x7fd9e4
    // 0x7fd9c8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7fd9c8: ldur            w4, [x2, #0x17]
    // 0x7fd9cc: DecompressPointer r4
    //     0x7fd9cc: add             x4, x4, HEAP, lsl #32
    // 0x7fd9d0: r8 = X0
    //     0x7fd9d0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7fd9d4: LoadField: r9 = r4->field_7
    //     0x7fd9d4: ldur            x9, [x4, #7]
    // 0x7fd9d8: r3 = Null
    //     0x7fd9d8: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4dcb0] Null
    //     0x7fd9dc: ldr             x3, [x3, #0xcb0]
    // 0x7fd9e0: blr             x9
    // 0x7fd9e4: ldur            x3, [fp, #-0x20]
    // 0x7fd9e8: LoadField: r4 = r3->field_7
    //     0x7fd9e8: ldur            w4, [x3, #7]
    // 0x7fd9ec: DecompressPointer r4
    //     0x7fd9ec: add             x4, x4, HEAP, lsl #32
    // 0x7fd9f0: stur            x4, [fp, #-0x40]
    // 0x7fd9f4: cmp             w4, NULL
    // 0x7fd9f8: b.eq            #0x7fdb00
    // 0x7fd9fc: mov             x0, x4
    // 0x7fda00: r2 = Null
    //     0x7fda00: mov             x2, NULL
    // 0x7fda04: r1 = Null
    //     0x7fda04: mov             x1, NULL
    // 0x7fda08: r4 = LoadClassIdInstr(r0)
    //     0x7fda08: ldur            x4, [x0, #-1]
    //     0x7fda0c: ubfx            x4, x4, #0xc, #0x14
    // 0x7fda10: sub             x4, x4, #0xc71
    // 0x7fda14: cmp             x4, #0xf
    // 0x7fda18: b.ls            #0x7fda30
    // 0x7fda1c: r8 = BoxParentData
    //     0x7fda1c: add             x8, PP, #0x25, lsl #12  ; [pp+0x252c8] Type: BoxParentData
    //     0x7fda20: ldr             x8, [x8, #0x2c8]
    // 0x7fda24: r3 = Null
    //     0x7fda24: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4dcc0] Null
    //     0x7fda28: ldr             x3, [x3, #0xcc0]
    // 0x7fda2c: r0 = DefaultTypeTest()
    //     0x7fda2c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7fda30: ldur            x0, [fp, #-0x40]
    // 0x7fda34: LoadField: r3 = r0->field_7
    //     0x7fda34: ldur            w3, [x0, #7]
    // 0x7fda38: DecompressPointer r3
    //     0x7fda38: add             x3, x3, HEAP, lsl #32
    // 0x7fda3c: ldur            x1, [fp, #-0x10]
    // 0x7fda40: mov             x2, x3
    // 0x7fda44: stur            x3, [fp, #-0x48]
    // 0x7fda48: r0 = -()
    //     0x7fda48: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x7fda4c: ldur            x1, [fp, #-0x48]
    // 0x7fda50: stur            x0, [fp, #-0x40]
    // 0x7fda54: r0 = unary-()
    //     0x7fda54: bl              #0x6a58ac  ; [dart:ui] Offset::unary-
    // 0x7fda58: ldur            x1, [fp, #-8]
    // 0x7fda5c: mov             x2, x0
    // 0x7fda60: r0 = pushOffset()
    //     0x7fda60: bl              #0x7faa44  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::pushOffset
    // 0x7fda64: ldur            x1, [fp, #-0x20]
    // 0x7fda68: r0 = LoadClassIdInstr(r1)
    //     0x7fda68: ldur            x0, [x1, #-1]
    //     0x7fda6c: ubfx            x0, x0, #0xc, #0x14
    // 0x7fda70: ldur            x2, [fp, #-8]
    // 0x7fda74: ldur            x3, [fp, #-0x40]
    // 0x7fda78: r0 = GDT[cid_x0 + 0xdf93]()
    //     0x7fda78: movz            x17, #0xdf93
    //     0x7fda7c: add             lr, x0, x17
    //     0x7fda80: ldr             lr, [x21, lr, lsl #3]
    //     0x7fda84: blr             lr
    // 0x7fda88: ldur            x1, [fp, #-8]
    // 0x7fda8c: stur            x0, [fp, #-0x20]
    // 0x7fda90: r0 = popTransform()
    //     0x7fda90: bl              #0x7fa9a8  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::popTransform
    // 0x7fda94: ldur            x0, [fp, #-0x20]
    // 0x7fda98: tbz             w0, #4, #0x7fdab0
    // 0x7fda9c: ldur            x0, [fp, #-0x18]
    // 0x7fdaa0: ldur            x3, [fp, #-0x38]
    // 0x7fdaa4: ldur            x4, [fp, #-0x30]
    // 0x7fdaa8: ldur            x5, [fp, #-0x28]
    // 0x7fdaac: b               #0x7fd968
    // 0x7fdab0: r0 = true
    //     0x7fdab0: add             x0, NULL, #0x20  ; true
    // 0x7fdab4: LeaveFrame
    //     0x7fdab4: mov             SP, fp
    //     0x7fdab8: ldp             fp, lr, [SP], #0x10
    // 0x7fdabc: ret
    //     0x7fdabc: ret             
    // 0x7fdac0: r0 = false
    //     0x7fdac0: add             x0, NULL, #0x30  ; false
    // 0x7fdac4: LeaveFrame
    //     0x7fdac4: mov             SP, fp
    //     0x7fdac8: ldp             fp, lr, [SP], #0x10
    // 0x7fdacc: ret
    //     0x7fdacc: ret             
    // 0x7fdad0: mov             x0, x3
    // 0x7fdad4: r0 = ConcurrentModificationError()
    //     0x7fdad4: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x7fdad8: mov             x1, x0
    // 0x7fdadc: ldur            x0, [fp, #-0x38]
    // 0x7fdae0: StoreField: r1->field_b = r0
    //     0x7fdae0: stur            w0, [x1, #0xb]
    // 0x7fdae4: mov             x0, x1
    // 0x7fdae8: r0 = Throw()
    //     0x7fdae8: bl              #0xec04b8  ; ThrowStub
    // 0x7fdaec: brk             #0
    // 0x7fdaf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fdaf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fdaf4: b               #0x7fd940
    // 0x7fdaf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fdaf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fdafc: b               #0x7fd974
    // 0x7fdb00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fdb00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _RenderListTile(/* No info */) {
    // ** addr: 0x860c88, size: 0x128
    // 0x860c88: EnterFrame
    //     0x860c88: stp             fp, lr, [SP, #-0x10]!
    //     0x860c8c: mov             fp, SP
    // 0x860c90: r4 = false
    //     0x860c90: add             x4, NULL, #0x30  ; false
    // 0x860c94: mov             x0, x2
    // 0x860c98: mov             x2, x6
    // 0x860c9c: mov             x6, x1
    // 0x860ca0: mov             x16, x5
    // 0x860ca4: mov             x5, x3
    // 0x860ca8: mov             x3, x16
    // 0x860cac: mov             x1, x7
    // 0x860cb0: CheckStackOverflow
    //     0x860cb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x860cb4: cmp             SP, x16
    //     0x860cb8: b.ls            #0x860da8
    // 0x860cbc: StoreField: r6->field_5b = r0
    //     0x860cbc: stur            w0, [x6, #0x5b]
    // 0x860cc0: ldr             x0, [fp, #0x10]
    // 0x860cc4: StoreField: r6->field_5f = r0
    //     0x860cc4: stur            w0, [x6, #0x5f]
    //     0x860cc8: ldurb           w16, [x6, #-1]
    //     0x860ccc: ldurb           w17, [x0, #-1]
    //     0x860cd0: and             x16, x17, x16, lsr #2
    //     0x860cd4: tst             x16, HEAP, lsr #32
    //     0x860cd8: b.eq            #0x860ce0
    //     0x860cdc: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x860ce0: StoreField: r6->field_63 = r4
    //     0x860ce0: stur            w4, [x6, #0x63]
    // 0x860ce4: mov             x0, x2
    // 0x860ce8: StoreField: r6->field_67 = r0
    //     0x860ce8: stur            w0, [x6, #0x67]
    //     0x860cec: ldurb           w16, [x6, #-1]
    //     0x860cf0: ldurb           w17, [x0, #-1]
    //     0x860cf4: and             x16, x17, x16, lsr #2
    //     0x860cf8: tst             x16, HEAP, lsr #32
    //     0x860cfc: b.eq            #0x860d04
    //     0x860d00: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x860d04: ldr             x0, [fp, #0x18]
    // 0x860d08: StoreField: r6->field_6b = r0
    //     0x860d08: stur            w0, [x6, #0x6b]
    //     0x860d0c: ldurb           w16, [x6, #-1]
    //     0x860d10: ldurb           w17, [x0, #-1]
    //     0x860d14: and             x16, x17, x16, lsr #2
    //     0x860d18: tst             x16, HEAP, lsr #32
    //     0x860d1c: b.eq            #0x860d24
    //     0x860d20: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x860d24: mov             x0, x3
    // 0x860d28: StoreField: r6->field_6f = r0
    //     0x860d28: stur            w0, [x6, #0x6f]
    //     0x860d2c: ldurb           w16, [x6, #-1]
    //     0x860d30: ldurb           w17, [x0, #-1]
    //     0x860d34: and             x16, x17, x16, lsr #2
    //     0x860d38: tst             x16, HEAP, lsr #32
    //     0x860d3c: b.eq            #0x860d44
    //     0x860d40: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x860d44: StoreField: r6->field_73 = d0
    //     0x860d44: stur            d0, [x6, #0x73]
    // 0x860d48: StoreField: r6->field_7b = d2
    //     0x860d48: stur            d2, [x6, #0x7b]
    // 0x860d4c: StoreField: r6->field_83 = d1
    //     0x860d4c: stur            d1, [x6, #0x83]
    // 0x860d50: mov             x0, x5
    // 0x860d54: StoreField: r6->field_8b = r0
    //     0x860d54: stur            w0, [x6, #0x8b]
    //     0x860d58: ldurb           w16, [x6, #-1]
    //     0x860d5c: ldurb           w17, [x0, #-1]
    //     0x860d60: and             x16, x17, x16, lsr #2
    //     0x860d64: tst             x16, HEAP, lsr #32
    //     0x860d68: b.eq            #0x860d70
    //     0x860d6c: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x860d70: mov             x0, x1
    // 0x860d74: StoreField: r6->field_8f = r0
    //     0x860d74: stur            w0, [x6, #0x8f]
    //     0x860d78: ldurb           w16, [x6, #-1]
    //     0x860d7c: ldurb           w17, [x0, #-1]
    //     0x860d80: and             x16, x17, x16, lsr #2
    //     0x860d84: tst             x16, HEAP, lsr #32
    //     0x860d88: b.eq            #0x860d90
    //     0x860d8c: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x860d90: mov             x1, x6
    // 0x860d94: r0 = __RenderListTile&RenderBox&SlottedContainerRenderObjectMixin()
    //     0x860d94: bl              #0x860db0  ; [package:flutter/src/material/list_tile.dart] __RenderListTile&RenderBox&SlottedContainerRenderObjectMixin::__RenderListTile&RenderBox&SlottedContainerRenderObjectMixin
    // 0x860d98: r0 = Null
    //     0x860d98: mov             x0, NULL
    // 0x860d9c: LeaveFrame
    //     0x860d9c: mov             SP, fp
    //     0x860da0: ldp             fp, lr, [SP], #0x10
    // 0x860da4: ret
    //     0x860da4: ret             
    // 0x860da8: r0 = StackOverflowSharedWithFPURegs()
    //     0x860da8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x860dac: b               #0x860cbc
  }
  set _ titleAlignment=(/* No info */) {
    // ** addr: 0xc72df0, size: 0x70
    // 0xc72df0: EnterFrame
    //     0xc72df0: stp             fp, lr, [SP, #-0x10]!
    //     0xc72df4: mov             fp, SP
    // 0xc72df8: mov             x0, x2
    // 0xc72dfc: CheckStackOverflow
    //     0xc72dfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc72e00: cmp             SP, x16
    //     0xc72e04: b.ls            #0xc72e58
    // 0xc72e08: LoadField: r2 = r1->field_8f
    //     0xc72e08: ldur            w2, [x1, #0x8f]
    // 0xc72e0c: DecompressPointer r2
    //     0xc72e0c: add             x2, x2, HEAP, lsl #32
    // 0xc72e10: cmp             w2, w0
    // 0xc72e14: b.ne            #0xc72e28
    // 0xc72e18: r0 = Null
    //     0xc72e18: mov             x0, NULL
    // 0xc72e1c: LeaveFrame
    //     0xc72e1c: mov             SP, fp
    //     0xc72e20: ldp             fp, lr, [SP], #0x10
    // 0xc72e24: ret
    //     0xc72e24: ret             
    // 0xc72e28: StoreField: r1->field_8f = r0
    //     0xc72e28: stur            w0, [x1, #0x8f]
    //     0xc72e2c: ldurb           w16, [x1, #-1]
    //     0xc72e30: ldurb           w17, [x0, #-1]
    //     0xc72e34: and             x16, x17, x16, lsr #2
    //     0xc72e38: tst             x16, HEAP, lsr #32
    //     0xc72e3c: b.eq            #0xc72e44
    //     0xc72e40: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc72e44: r0 = markNeedsLayout()
    //     0xc72e44: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc72e48: r0 = Null
    //     0xc72e48: mov             x0, NULL
    // 0xc72e4c: LeaveFrame
    //     0xc72e4c: mov             SP, fp
    //     0xc72e50: ldp             fp, lr, [SP], #0x10
    // 0xc72e54: ret
    //     0xc72e54: ret             
    // 0xc72e58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc72e58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc72e5c: b               #0xc72e08
  }
  set _ minTileHeight=(/* No info */) {
    // ** addr: 0xc72e60, size: 0xa4
    // 0xc72e60: EnterFrame
    //     0xc72e60: stp             fp, lr, [SP, #-0x10]!
    //     0xc72e64: mov             fp, SP
    // 0xc72e68: AllocStack(0x20)
    //     0xc72e68: sub             SP, SP, #0x20
    // 0xc72e6c: SetupParameters(_RenderListTile this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc72e6c: stur            x1, [fp, #-8]
    //     0xc72e70: mov             x16, x2
    //     0xc72e74: mov             x2, x1
    //     0xc72e78: mov             x1, x16
    //     0xc72e7c: stur            x1, [fp, #-0x10]
    // 0xc72e80: CheckStackOverflow
    //     0xc72e80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc72e84: cmp             SP, x16
    //     0xc72e88: b.ls            #0xc72efc
    // 0xc72e8c: LoadField: r0 = r2->field_8b
    //     0xc72e8c: ldur            w0, [x2, #0x8b]
    // 0xc72e90: DecompressPointer r0
    //     0xc72e90: add             x0, x0, HEAP, lsl #32
    // 0xc72e94: r3 = LoadClassIdInstr(r0)
    //     0xc72e94: ldur            x3, [x0, #-1]
    //     0xc72e98: ubfx            x3, x3, #0xc, #0x14
    // 0xc72e9c: stp             x1, x0, [SP]
    // 0xc72ea0: mov             x0, x3
    // 0xc72ea4: mov             lr, x0
    // 0xc72ea8: ldr             lr, [x21, lr, lsl #3]
    // 0xc72eac: blr             lr
    // 0xc72eb0: tbnz            w0, #4, #0xc72ec4
    // 0xc72eb4: r0 = Null
    //     0xc72eb4: mov             x0, NULL
    // 0xc72eb8: LeaveFrame
    //     0xc72eb8: mov             SP, fp
    //     0xc72ebc: ldp             fp, lr, [SP], #0x10
    // 0xc72ec0: ret
    //     0xc72ec0: ret             
    // 0xc72ec4: ldur            x1, [fp, #-8]
    // 0xc72ec8: ldur            x0, [fp, #-0x10]
    // 0xc72ecc: StoreField: r1->field_8b = r0
    //     0xc72ecc: stur            w0, [x1, #0x8b]
    //     0xc72ed0: ldurb           w16, [x1, #-1]
    //     0xc72ed4: ldurb           w17, [x0, #-1]
    //     0xc72ed8: and             x16, x17, x16, lsr #2
    //     0xc72edc: tst             x16, HEAP, lsr #32
    //     0xc72ee0: b.eq            #0xc72ee8
    //     0xc72ee4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc72ee8: r0 = markNeedsLayout()
    //     0xc72ee8: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc72eec: r0 = Null
    //     0xc72eec: mov             x0, NULL
    // 0xc72ef0: LeaveFrame
    //     0xc72ef0: mov             SP, fp
    //     0xc72ef4: ldp             fp, lr, [SP], #0x10
    // 0xc72ef8: ret
    //     0xc72ef8: ret             
    // 0xc72efc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc72efc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc72f00: b               #0xc72e8c
  }
  set _ minLeadingWidth=(/* No info */) {
    // ** addr: 0xc72f04, size: 0x50
    // 0xc72f04: EnterFrame
    //     0xc72f04: stp             fp, lr, [SP, #-0x10]!
    //     0xc72f08: mov             fp, SP
    // 0xc72f0c: CheckStackOverflow
    //     0xc72f0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc72f10: cmp             SP, x16
    //     0xc72f14: b.ls            #0xc72f4c
    // 0xc72f18: LoadField: d1 = r1->field_83
    //     0xc72f18: ldur            d1, [x1, #0x83]
    // 0xc72f1c: fcmp            d1, d0
    // 0xc72f20: b.ne            #0xc72f34
    // 0xc72f24: r0 = Null
    //     0xc72f24: mov             x0, NULL
    // 0xc72f28: LeaveFrame
    //     0xc72f28: mov             SP, fp
    //     0xc72f2c: ldp             fp, lr, [SP], #0x10
    // 0xc72f30: ret
    //     0xc72f30: ret             
    // 0xc72f34: StoreField: r1->field_83 = d0
    //     0xc72f34: stur            d0, [x1, #0x83]
    // 0xc72f38: r0 = markNeedsLayout()
    //     0xc72f38: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc72f3c: r0 = Null
    //     0xc72f3c: mov             x0, NULL
    // 0xc72f40: LeaveFrame
    //     0xc72f40: mov             SP, fp
    //     0xc72f44: ldp             fp, lr, [SP], #0x10
    // 0xc72f48: ret
    //     0xc72f48: ret             
    // 0xc72f4c: r0 = StackOverflowSharedWithFPURegs()
    //     0xc72f4c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc72f50: b               #0xc72f18
  }
  set _ horizontalTitleGap=(/* No info */) {
    // ** addr: 0xc72f54, size: 0x50
    // 0xc72f54: EnterFrame
    //     0xc72f54: stp             fp, lr, [SP, #-0x10]!
    //     0xc72f58: mov             fp, SP
    // 0xc72f5c: CheckStackOverflow
    //     0xc72f5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc72f60: cmp             SP, x16
    //     0xc72f64: b.ls            #0xc72f9c
    // 0xc72f68: LoadField: d1 = r1->field_73
    //     0xc72f68: ldur            d1, [x1, #0x73]
    // 0xc72f6c: fcmp            d1, d0
    // 0xc72f70: b.ne            #0xc72f84
    // 0xc72f74: r0 = Null
    //     0xc72f74: mov             x0, NULL
    // 0xc72f78: LeaveFrame
    //     0xc72f78: mov             SP, fp
    //     0xc72f7c: ldp             fp, lr, [SP], #0x10
    // 0xc72f80: ret
    //     0xc72f80: ret             
    // 0xc72f84: StoreField: r1->field_73 = d0
    //     0xc72f84: stur            d0, [x1, #0x73]
    // 0xc72f88: r0 = markNeedsLayout()
    //     0xc72f88: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc72f8c: r0 = Null
    //     0xc72f8c: mov             x0, NULL
    // 0xc72f90: LeaveFrame
    //     0xc72f90: mov             SP, fp
    //     0xc72f94: ldp             fp, lr, [SP], #0x10
    // 0xc72f98: ret
    //     0xc72f98: ret             
    // 0xc72f9c: r0 = StackOverflowSharedWithFPURegs()
    //     0xc72f9c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc72fa0: b               #0xc72f68
  }
  set _ subtitleBaselineType=(/* No info */) {
    // ** addr: 0xc72fa4, size: 0x70
    // 0xc72fa4: EnterFrame
    //     0xc72fa4: stp             fp, lr, [SP, #-0x10]!
    //     0xc72fa8: mov             fp, SP
    // 0xc72fac: mov             x0, x2
    // 0xc72fb0: CheckStackOverflow
    //     0xc72fb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc72fb4: cmp             SP, x16
    //     0xc72fb8: b.ls            #0xc7300c
    // 0xc72fbc: LoadField: r2 = r1->field_6f
    //     0xc72fbc: ldur            w2, [x1, #0x6f]
    // 0xc72fc0: DecompressPointer r2
    //     0xc72fc0: add             x2, x2, HEAP, lsl #32
    // 0xc72fc4: cmp             w2, w0
    // 0xc72fc8: b.ne            #0xc72fdc
    // 0xc72fcc: r0 = Null
    //     0xc72fcc: mov             x0, NULL
    // 0xc72fd0: LeaveFrame
    //     0xc72fd0: mov             SP, fp
    //     0xc72fd4: ldp             fp, lr, [SP], #0x10
    // 0xc72fd8: ret
    //     0xc72fd8: ret             
    // 0xc72fdc: StoreField: r1->field_6f = r0
    //     0xc72fdc: stur            w0, [x1, #0x6f]
    //     0xc72fe0: ldurb           w16, [x1, #-1]
    //     0xc72fe4: ldurb           w17, [x0, #-1]
    //     0xc72fe8: and             x16, x17, x16, lsr #2
    //     0xc72fec: tst             x16, HEAP, lsr #32
    //     0xc72ff0: b.eq            #0xc72ff8
    //     0xc72ff4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc72ff8: r0 = markNeedsLayout()
    //     0xc72ff8: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc72ffc: r0 = Null
    //     0xc72ffc: mov             x0, NULL
    // 0xc73000: LeaveFrame
    //     0xc73000: mov             SP, fp
    //     0xc73004: ldp             fp, lr, [SP], #0x10
    // 0xc73008: ret
    //     0xc73008: ret             
    // 0xc7300c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7300c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc73010: b               #0xc72fbc
  }
  set _ titleBaselineType=(/* No info */) {
    // ** addr: 0xc73014, size: 0x70
    // 0xc73014: EnterFrame
    //     0xc73014: stp             fp, lr, [SP, #-0x10]!
    //     0xc73018: mov             fp, SP
    // 0xc7301c: mov             x0, x2
    // 0xc73020: CheckStackOverflow
    //     0xc73020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc73024: cmp             SP, x16
    //     0xc73028: b.ls            #0xc7307c
    // 0xc7302c: LoadField: r2 = r1->field_6b
    //     0xc7302c: ldur            w2, [x1, #0x6b]
    // 0xc73030: DecompressPointer r2
    //     0xc73030: add             x2, x2, HEAP, lsl #32
    // 0xc73034: cmp             w2, w0
    // 0xc73038: b.ne            #0xc7304c
    // 0xc7303c: r0 = Null
    //     0xc7303c: mov             x0, NULL
    // 0xc73040: LeaveFrame
    //     0xc73040: mov             SP, fp
    //     0xc73044: ldp             fp, lr, [SP], #0x10
    // 0xc73048: ret
    //     0xc73048: ret             
    // 0xc7304c: StoreField: r1->field_6b = r0
    //     0xc7304c: stur            w0, [x1, #0x6b]
    //     0xc73050: ldurb           w16, [x1, #-1]
    //     0xc73054: ldurb           w17, [x0, #-1]
    //     0xc73058: and             x16, x17, x16, lsr #2
    //     0xc7305c: tst             x16, HEAP, lsr #32
    //     0xc73060: b.eq            #0xc73068
    //     0xc73064: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc73068: r0 = markNeedsLayout()
    //     0xc73068: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc7306c: r0 = Null
    //     0xc7306c: mov             x0, NULL
    // 0xc73070: LeaveFrame
    //     0xc73070: mov             SP, fp
    //     0xc73074: ldp             fp, lr, [SP], #0x10
    // 0xc73078: ret
    //     0xc73078: ret             
    // 0xc7307c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7307c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc73080: b               #0xc7302c
  }
  set _ visualDensity=(/* No info */) {
    // ** addr: 0xc73084, size: 0xc8
    // 0xc73084: EnterFrame
    //     0xc73084: stp             fp, lr, [SP, #-0x10]!
    //     0xc73088: mov             fp, SP
    // 0xc7308c: AllocStack(0x28)
    //     0xc7308c: sub             SP, SP, #0x28
    // 0xc73090: SetupParameters(_RenderListTile this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xc73090: mov             x0, x2
    //     0xc73094: stur            x1, [fp, #-0x10]
    //     0xc73098: stur            x2, [fp, #-0x18]
    // 0xc7309c: CheckStackOverflow
    //     0xc7309c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc730a0: cmp             SP, x16
    //     0xc730a4: b.ls            #0xc73144
    // 0xc730a8: LoadField: r2 = r1->field_5f
    //     0xc730a8: ldur            w2, [x1, #0x5f]
    // 0xc730ac: DecompressPointer r2
    //     0xc730ac: add             x2, x2, HEAP, lsl #32
    // 0xc730b0: stur            x2, [fp, #-8]
    // 0xc730b4: r16 = VisualDensity
    //     0xc730b4: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d060] Type: VisualDensity
    //     0xc730b8: ldr             x16, [x16, #0x60]
    // 0xc730bc: r30 = VisualDensity
    //     0xc730bc: add             lr, PP, #0x1d, lsl #12  ; [pp+0x1d060] Type: VisualDensity
    //     0xc730c0: ldr             lr, [lr, #0x60]
    // 0xc730c4: stp             lr, x16, [SP]
    // 0xc730c8: r0 = ==()
    //     0xc730c8: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xc730cc: tbz             w0, #4, #0xc730d8
    // 0xc730d0: ldur            x0, [fp, #-0x18]
    // 0xc730d4: b               #0xc73110
    // 0xc730d8: ldur            x0, [fp, #-0x18]
    // 0xc730dc: ldur            x1, [fp, #-8]
    // 0xc730e0: LoadField: d0 = r0->field_7
    //     0xc730e0: ldur            d0, [x0, #7]
    // 0xc730e4: LoadField: d1 = r1->field_7
    //     0xc730e4: ldur            d1, [x1, #7]
    // 0xc730e8: fcmp            d0, d1
    // 0xc730ec: b.ne            #0xc73110
    // 0xc730f0: LoadField: d0 = r0->field_f
    //     0xc730f0: ldur            d0, [x0, #0xf]
    // 0xc730f4: LoadField: d1 = r1->field_f
    //     0xc730f4: ldur            d1, [x1, #0xf]
    // 0xc730f8: fcmp            d0, d1
    // 0xc730fc: b.ne            #0xc73110
    // 0xc73100: r0 = Null
    //     0xc73100: mov             x0, NULL
    // 0xc73104: LeaveFrame
    //     0xc73104: mov             SP, fp
    //     0xc73108: ldp             fp, lr, [SP], #0x10
    // 0xc7310c: ret
    //     0xc7310c: ret             
    // 0xc73110: ldur            x1, [fp, #-0x10]
    // 0xc73114: StoreField: r1->field_5f = r0
    //     0xc73114: stur            w0, [x1, #0x5f]
    //     0xc73118: ldurb           w16, [x1, #-1]
    //     0xc7311c: ldurb           w17, [x0, #-1]
    //     0xc73120: and             x16, x17, x16, lsr #2
    //     0xc73124: tst             x16, HEAP, lsr #32
    //     0xc73128: b.eq            #0xc73130
    //     0xc7312c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc73130: r0 = markNeedsLayout()
    //     0xc73130: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc73134: r0 = Null
    //     0xc73134: mov             x0, NULL
    // 0xc73138: LeaveFrame
    //     0xc73138: mov             SP, fp
    //     0xc7313c: ldp             fp, lr, [SP], #0x10
    // 0xc73140: ret
    //     0xc73140: ret             
    // 0xc73144: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc73144: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc73148: b               #0xc730a8
  }
  set _ isDense=(/* No info */) {
    // ** addr: 0xc7314c, size: 0x54
    // 0xc7314c: EnterFrame
    //     0xc7314c: stp             fp, lr, [SP, #-0x10]!
    //     0xc73150: mov             fp, SP
    // 0xc73154: CheckStackOverflow
    //     0xc73154: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc73158: cmp             SP, x16
    //     0xc7315c: b.ls            #0xc73198
    // 0xc73160: LoadField: r0 = r1->field_5b
    //     0xc73160: ldur            w0, [x1, #0x5b]
    // 0xc73164: DecompressPointer r0
    //     0xc73164: add             x0, x0, HEAP, lsl #32
    // 0xc73168: cmp             w0, w2
    // 0xc7316c: b.ne            #0xc73180
    // 0xc73170: r0 = Null
    //     0xc73170: mov             x0, NULL
    // 0xc73174: LeaveFrame
    //     0xc73174: mov             SP, fp
    //     0xc73178: ldp             fp, lr, [SP], #0x10
    // 0xc7317c: ret
    //     0xc7317c: ret             
    // 0xc73180: StoreField: r1->field_5b = r2
    //     0xc73180: stur            w2, [x1, #0x5b]
    // 0xc73184: r0 = markNeedsLayout()
    //     0xc73184: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc73188: r0 = Null
    //     0xc73188: mov             x0, NULL
    // 0xc7318c: LeaveFrame
    //     0xc7318c: mov             SP, fp
    //     0xc73190: ldp             fp, lr, [SP], #0x10
    // 0xc73194: ret
    //     0xc73194: ret             
    // 0xc73198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc73198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7319c: b               #0xc73160
  }
}

// class id: 3309, size: 0x1c, field offset: 0xc
class _IndividualOverrides extends WidgetStateProperty<dynamic> {

  _ resolve(/* No info */) {
    // ** addr: 0xd942c0, size: 0xe8
    // 0xd942c0: EnterFrame
    //     0xd942c0: stp             fp, lr, [SP, #-0x10]!
    //     0xd942c4: mov             fp, SP
    // 0xd942c8: AllocStack(0x28)
    //     0xd942c8: sub             SP, SP, #0x28
    // 0xd942cc: SetupParameters(_IndividualOverrides this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xd942cc: mov             x3, x1
    //     0xd942d0: mov             x0, x2
    //     0xd942d4: stur            x1, [fp, #-8]
    //     0xd942d8: stur            x2, [fp, #-0x10]
    // 0xd942dc: CheckStackOverflow
    //     0xd942dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd942e0: cmp             SP, x16
    //     0xd942e4: b.ls            #0xd943a0
    // 0xd942e8: LoadField: r1 = r3->field_b
    //     0xd942e8: ldur            w1, [x3, #0xb]
    // 0xd942ec: DecompressPointer r1
    //     0xd942ec: add             x1, x1, HEAP, lsl #32
    // 0xd942f0: r2 = LoadClassIdInstr(r1)
    //     0xd942f0: ldur            x2, [x1, #-1]
    //     0xd942f4: ubfx            x2, x2, #0xc, #0x14
    // 0xd942f8: r17 = 6080
    //     0xd942f8: movz            x17, #0x17c0
    // 0xd942fc: cmp             x2, x17
    // 0xd94300: b.ne            #0xd94328
    // 0xd94304: r16 = <Color?>
    //     0xd94304: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xd94308: ldr             x16, [x16, #0x98]
    // 0xd9430c: stp             x1, x16, [SP, #8]
    // 0xd94310: str             x0, [SP]
    // 0xd94314: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xd94314: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xd94318: r0 = resolveAs()
    //     0xd94318: bl              #0x9d8704  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::resolveAs
    // 0xd9431c: LeaveFrame
    //     0xd9431c: mov             SP, fp
    //     0xd94320: ldp             fp, lr, [SP], #0x10
    // 0xd94324: ret
    //     0xd94324: ret             
    // 0xd94328: mov             x1, x0
    // 0xd9432c: r2 = Instance_WidgetState
    //     0xd9432c: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d190] Obj!WidgetState@e33841
    //     0xd94330: ldr             x2, [x2, #0x190]
    // 0xd94334: r0 = contains()
    //     0xd94334: bl              #0x86b148  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0xd94338: tbnz            w0, #4, #0xd94358
    // 0xd9433c: ldur            x0, [fp, #-8]
    // 0xd94340: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd94340: ldur            w1, [x0, #0x17]
    // 0xd94344: DecompressPointer r1
    //     0xd94344: add             x1, x1, HEAP, lsl #32
    // 0xd94348: mov             x0, x1
    // 0xd9434c: LeaveFrame
    //     0xd9434c: mov             SP, fp
    //     0xd94350: ldp             fp, lr, [SP], #0x10
    // 0xd94354: ret
    //     0xd94354: ret             
    // 0xd94358: ldur            x0, [fp, #-8]
    // 0xd9435c: ldur            x1, [fp, #-0x10]
    // 0xd94360: r2 = Instance_WidgetState
    //     0xd94360: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d0d0] Obj!WidgetState@e338e1
    //     0xd94364: ldr             x2, [x2, #0xd0]
    // 0xd94368: r0 = contains()
    //     0xd94368: bl              #0x86b148  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0xd9436c: tbnz            w0, #4, #0xd94388
    // 0xd94370: ldur            x1, [fp, #-8]
    // 0xd94374: LoadField: r0 = r1->field_13
    //     0xd94374: ldur            w0, [x1, #0x13]
    // 0xd94378: DecompressPointer r0
    //     0xd94378: add             x0, x0, HEAP, lsl #32
    // 0xd9437c: LeaveFrame
    //     0xd9437c: mov             SP, fp
    //     0xd94380: ldp             fp, lr, [SP], #0x10
    // 0xd94384: ret
    //     0xd94384: ret             
    // 0xd94388: ldur            x1, [fp, #-8]
    // 0xd9438c: LoadField: r0 = r1->field_f
    //     0xd9438c: ldur            w0, [x1, #0xf]
    // 0xd94390: DecompressPointer r0
    //     0xd94390: add             x0, x0, HEAP, lsl #32
    // 0xd94394: LeaveFrame
    //     0xd94394: mov             SP, fp
    //     0xd94398: ldp             fp, lr, [SP], #0x10
    // 0xd9439c: ret
    //     0xd9439c: ret             
    // 0xd943a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd943a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd943a4: b               #0xd942e8
  }
}

// class id: 3941, size: 0x6c, field offset: 0x5c
class _LisTileDefaultsM3 extends ListTileThemeData {

  late final ColorScheme _colors; // offset: 0x64
  late final TextTheme _textTheme; // offset: 0x68
  late final ThemeData _theme; // offset: 0x60

  TextTheme _textTheme(_LisTileDefaultsM3) {
    // ** addr: 0xa9f5c8, size: 0x58
    // 0xa9f5c8: EnterFrame
    //     0xa9f5c8: stp             fp, lr, [SP, #-0x10]!
    //     0xa9f5cc: mov             fp, SP
    // 0xa9f5d0: CheckStackOverflow
    //     0xa9f5d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9f5d4: cmp             SP, x16
    //     0xa9f5d8: b.ls            #0xa9f618
    // 0xa9f5dc: ldr             x1, [fp, #0x10]
    // 0xa9f5e0: LoadField: r0 = r1->field_5f
    //     0xa9f5e0: ldur            w0, [x1, #0x5f]
    // 0xa9f5e4: DecompressPointer r0
    //     0xa9f5e4: add             x0, x0, HEAP, lsl #32
    // 0xa9f5e8: r16 = Sentinel
    //     0xa9f5e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9f5ec: cmp             w0, w16
    // 0xa9f5f0: b.ne            #0xa9f600
    // 0xa9f5f4: r2 = _theme
    //     0xa9f5f4: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d238] Field <_LisTileDefaultsM3@560247952._theme@560247952>: late final (offset: 0x60)
    //     0xa9f5f8: ldr             x2, [x2, #0x238]
    // 0xa9f5fc: r0 = InitLateFinalInstanceField()
    //     0xa9f5fc: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9f600: LoadField: r1 = r0->field_8f
    //     0xa9f600: ldur            w1, [x0, #0x8f]
    // 0xa9f604: DecompressPointer r1
    //     0xa9f604: add             x1, x1, HEAP, lsl #32
    // 0xa9f608: mov             x0, x1
    // 0xa9f60c: LeaveFrame
    //     0xa9f60c: mov             SP, fp
    //     0xa9f610: ldp             fp, lr, [SP], #0x10
    // 0xa9f614: ret
    //     0xa9f614: ret             
    // 0xa9f618: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9f618: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9f61c: b               #0xa9f5dc
  }
  ColorScheme _colors(_LisTileDefaultsM3) {
    // ** addr: 0xa9f658, size: 0x58
    // 0xa9f658: EnterFrame
    //     0xa9f658: stp             fp, lr, [SP, #-0x10]!
    //     0xa9f65c: mov             fp, SP
    // 0xa9f660: CheckStackOverflow
    //     0xa9f660: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9f664: cmp             SP, x16
    //     0xa9f668: b.ls            #0xa9f6a8
    // 0xa9f66c: ldr             x1, [fp, #0x10]
    // 0xa9f670: LoadField: r0 = r1->field_5f
    //     0xa9f670: ldur            w0, [x1, #0x5f]
    // 0xa9f674: DecompressPointer r0
    //     0xa9f674: add             x0, x0, HEAP, lsl #32
    // 0xa9f678: r16 = Sentinel
    //     0xa9f678: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9f67c: cmp             w0, w16
    // 0xa9f680: b.ne            #0xa9f690
    // 0xa9f684: r2 = _theme
    //     0xa9f684: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d238] Field <_LisTileDefaultsM3@560247952._theme@560247952>: late final (offset: 0x60)
    //     0xa9f688: ldr             x2, [x2, #0x238]
    // 0xa9f68c: r0 = InitLateFinalInstanceField()
    //     0xa9f68c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9f690: LoadField: r1 = r0->field_3f
    //     0xa9f690: ldur            w1, [x0, #0x3f]
    // 0xa9f694: DecompressPointer r1
    //     0xa9f694: add             x1, x1, HEAP, lsl #32
    // 0xa9f698: mov             x0, x1
    // 0xa9f69c: LeaveFrame
    //     0xa9f69c: mov             SP, fp
    //     0xa9f6a0: ldp             fp, lr, [SP], #0x10
    // 0xa9f6a4: ret
    //     0xa9f6a4: ret             
    // 0xa9f6a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9f6a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9f6ac: b               #0xa9f66c
  }
}

// class id: 3942, size: 0x68, field offset: 0x5c
class _LisTileDefaultsM2 extends ListTileThemeData {

  late final ThemeData _theme; // offset: 0x60
  late final TextTheme _textTheme; // offset: 0x64

  TextTheme _textTheme(_LisTileDefaultsM2) {
    // ** addr: 0xa9f570, size: 0x58
    // 0xa9f570: EnterFrame
    //     0xa9f570: stp             fp, lr, [SP, #-0x10]!
    //     0xa9f574: mov             fp, SP
    // 0xa9f578: CheckStackOverflow
    //     0xa9f578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9f57c: cmp             SP, x16
    //     0xa9f580: b.ls            #0xa9f5c0
    // 0xa9f584: ldr             x1, [fp, #0x10]
    // 0xa9f588: LoadField: r0 = r1->field_5f
    //     0xa9f588: ldur            w0, [x1, #0x5f]
    // 0xa9f58c: DecompressPointer r0
    //     0xa9f58c: add             x0, x0, HEAP, lsl #32
    // 0xa9f590: r16 = Sentinel
    //     0xa9f590: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9f594: cmp             w0, w16
    // 0xa9f598: b.ne            #0xa9f5a8
    // 0xa9f59c: r2 = _theme
    //     0xa9f59c: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d210] Field <_LisTileDefaultsM2@560247952._theme@560247952>: late final (offset: 0x60)
    //     0xa9f5a0: ldr             x2, [x2, #0x210]
    // 0xa9f5a4: r0 = InitLateFinalInstanceField()
    //     0xa9f5a4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9f5a8: LoadField: r1 = r0->field_8f
    //     0xa9f5a8: ldur            w1, [x0, #0x8f]
    // 0xa9f5ac: DecompressPointer r1
    //     0xa9f5ac: add             x1, x1, HEAP, lsl #32
    // 0xa9f5b0: mov             x0, x1
    // 0xa9f5b4: LeaveFrame
    //     0xa9f5b4: mov             SP, fp
    //     0xa9f5b8: ldp             fp, lr, [SP], #0x10
    // 0xa9f5bc: ret
    //     0xa9f5bc: ret             
    // 0xa9f5c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9f5c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9f5c4: b               #0xa9f584
  }
  ThemeData _theme(_LisTileDefaultsM2) {
    // ** addr: 0xa9f620, size: 0x38
    // 0xa9f620: EnterFrame
    //     0xa9f620: stp             fp, lr, [SP, #-0x10]!
    //     0xa9f624: mov             fp, SP
    // 0xa9f628: CheckStackOverflow
    //     0xa9f628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9f62c: cmp             SP, x16
    //     0xa9f630: b.ls            #0xa9f650
    // 0xa9f634: ldr             x0, [fp, #0x10]
    // 0xa9f638: LoadField: r1 = r0->field_5b
    //     0xa9f638: ldur            w1, [x0, #0x5b]
    // 0xa9f63c: DecompressPointer r1
    //     0xa9f63c: add             x1, x1, HEAP, lsl #32
    // 0xa9f640: r0 = of()
    //     0xa9f640: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa9f644: LeaveFrame
    //     0xa9f644: mov             SP, fp
    //     0xa9f648: ldp             fp, lr, [SP], #0x10
    // 0xa9f64c: ret
    //     0xa9f64c: ret             
    // 0xa9f650: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9f650: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9f654: b               #0xa9f634
  }
}

// class id: 4477, size: 0x58, field offset: 0x10
//   const constructor, 
class _ListTile extends SlottedMultiChildRenderObjectWidget<dynamic, dynamic> {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x860bb0, size: 0xd8
    // 0x860bb0: EnterFrame
    //     0x860bb0: stp             fp, lr, [SP, #-0x10]!
    //     0x860bb4: mov             fp, SP
    // 0x860bb8: AllocStack(0x68)
    //     0x860bb8: sub             SP, SP, #0x68
    // 0x860bbc: CheckStackOverflow
    //     0x860bbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x860bc0: cmp             SP, x16
    //     0x860bc4: b.ls            #0x860c80
    // 0x860bc8: LoadField: r2 = r1->field_23
    //     0x860bc8: ldur            w2, [x1, #0x23]
    // 0x860bcc: DecompressPointer r2
    //     0x860bcc: add             x2, x2, HEAP, lsl #32
    // 0x860bd0: stur            x2, [fp, #-0x38]
    // 0x860bd4: LoadField: r0 = r1->field_27
    //     0x860bd4: ldur            w0, [x1, #0x27]
    // 0x860bd8: DecompressPointer r0
    //     0x860bd8: add             x0, x0, HEAP, lsl #32
    // 0x860bdc: stur            x0, [fp, #-0x30]
    // 0x860be0: LoadField: r6 = r1->field_2b
    //     0x860be0: ldur            w6, [x1, #0x2b]
    // 0x860be4: DecompressPointer r6
    //     0x860be4: add             x6, x6, HEAP, lsl #32
    // 0x860be8: stur            x6, [fp, #-0x28]
    // 0x860bec: LoadField: r3 = r1->field_2f
    //     0x860bec: ldur            w3, [x1, #0x2f]
    // 0x860bf0: DecompressPointer r3
    //     0x860bf0: add             x3, x3, HEAP, lsl #32
    // 0x860bf4: stur            x3, [fp, #-0x20]
    // 0x860bf8: LoadField: r5 = r1->field_33
    //     0x860bf8: ldur            w5, [x1, #0x33]
    // 0x860bfc: DecompressPointer r5
    //     0x860bfc: add             x5, x5, HEAP, lsl #32
    // 0x860c00: stur            x5, [fp, #-0x18]
    // 0x860c04: LoadField: d0 = r1->field_37
    //     0x860c04: ldur            d0, [x1, #0x37]
    // 0x860c08: stur            d0, [fp, #-0x58]
    // 0x860c0c: LoadField: d2 = r1->field_3f
    //     0x860c0c: ldur            d2, [x1, #0x3f]
    // 0x860c10: stur            d2, [fp, #-0x50]
    // 0x860c14: LoadField: d1 = r1->field_47
    //     0x860c14: ldur            d1, [x1, #0x47]
    // 0x860c18: stur            d1, [fp, #-0x48]
    // 0x860c1c: LoadField: r4 = r1->field_4f
    //     0x860c1c: ldur            w4, [x1, #0x4f]
    // 0x860c20: DecompressPointer r4
    //     0x860c20: add             x4, x4, HEAP, lsl #32
    // 0x860c24: stur            x4, [fp, #-0x10]
    // 0x860c28: LoadField: r7 = r1->field_53
    //     0x860c28: ldur            w7, [x1, #0x53]
    // 0x860c2c: DecompressPointer r7
    //     0x860c2c: add             x7, x7, HEAP, lsl #32
    // 0x860c30: stur            x7, [fp, #-8]
    // 0x860c34: r0 = _RenderListTile()
    //     0x860c34: bl              #0x860e40  ; Allocate_RenderListTileStub -> _RenderListTile (size=0x94)
    // 0x860c38: stur            x0, [fp, #-0x40]
    // 0x860c3c: ldur            x16, [fp, #-0x20]
    // 0x860c40: ldur            lr, [fp, #-0x30]
    // 0x860c44: stp             lr, x16, [SP]
    // 0x860c48: mov             x1, x0
    // 0x860c4c: ldur            d0, [fp, #-0x58]
    // 0x860c50: ldur            x2, [fp, #-0x38]
    // 0x860c54: ldur            d1, [fp, #-0x48]
    // 0x860c58: ldur            x3, [fp, #-0x10]
    // 0x860c5c: ldur            d2, [fp, #-0x50]
    // 0x860c60: ldur            x5, [fp, #-0x18]
    // 0x860c64: ldur            x6, [fp, #-0x28]
    // 0x860c68: ldur            x7, [fp, #-8]
    // 0x860c6c: r0 = _RenderListTile()
    //     0x860c6c: bl              #0x860c88  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::_RenderListTile
    // 0x860c70: ldur            x0, [fp, #-0x40]
    // 0x860c74: LeaveFrame
    //     0x860c74: mov             SP, fp
    //     0x860c78: ldp             fp, lr, [SP], #0x10
    // 0x860c7c: ret
    //     0x860c7c: ret             
    // 0x860c80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x860c80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x860c84: b               #0x860bc8
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xc72cb4, size: 0x13c
    // 0xc72cb4: EnterFrame
    //     0xc72cb4: stp             fp, lr, [SP, #-0x10]!
    //     0xc72cb8: mov             fp, SP
    // 0xc72cbc: AllocStack(0x10)
    //     0xc72cbc: sub             SP, SP, #0x10
    // 0xc72cc0: SetupParameters(_ListTile this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xc72cc0: mov             x4, x1
    //     0xc72cc4: stur            x1, [fp, #-8]
    //     0xc72cc8: stur            x3, [fp, #-0x10]
    // 0xc72ccc: CheckStackOverflow
    //     0xc72ccc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc72cd0: cmp             SP, x16
    //     0xc72cd4: b.ls            #0xc72de8
    // 0xc72cd8: mov             x0, x3
    // 0xc72cdc: r2 = Null
    //     0xc72cdc: mov             x2, NULL
    // 0xc72ce0: r1 = Null
    //     0xc72ce0: mov             x1, NULL
    // 0xc72ce4: r4 = 60
    //     0xc72ce4: movz            x4, #0x3c
    // 0xc72ce8: branchIfSmi(r0, 0xc72cf4)
    //     0xc72ce8: tbz             w0, #0, #0xc72cf4
    // 0xc72cec: r4 = LoadClassIdInstr(r0)
    //     0xc72cec: ldur            x4, [x0, #-1]
    //     0xc72cf0: ubfx            x4, x4, #0xc, #0x14
    // 0xc72cf4: cmp             x4, #0xbf6
    // 0xc72cf8: b.eq            #0xc72d10
    // 0xc72cfc: r8 = _RenderListTile
    //     0xc72cfc: add             x8, PP, #0x44, lsl #12  ; [pp+0x443f8] Type: _RenderListTile
    //     0xc72d00: ldr             x8, [x8, #0x3f8]
    // 0xc72d04: r3 = Null
    //     0xc72d04: add             x3, PP, #0x44, lsl #12  ; [pp+0x44400] Null
    //     0xc72d08: ldr             x3, [x3, #0x400]
    // 0xc72d0c: r0 = DefaultTypeTest()
    //     0xc72d0c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc72d10: ldur            x1, [fp, #-0x10]
    // 0xc72d14: r2 = false
    //     0xc72d14: add             x2, NULL, #0x30  ; false
    // 0xc72d18: r0 = forceCompileTimeTreeShaking()
    //     0xc72d18: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xc72d1c: ldur            x0, [fp, #-8]
    // 0xc72d20: LoadField: r2 = r0->field_23
    //     0xc72d20: ldur            w2, [x0, #0x23]
    // 0xc72d24: DecompressPointer r2
    //     0xc72d24: add             x2, x2, HEAP, lsl #32
    // 0xc72d28: ldur            x1, [fp, #-0x10]
    // 0xc72d2c: r0 = isDense=()
    //     0xc72d2c: bl              #0xc7314c  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::isDense=
    // 0xc72d30: ldur            x0, [fp, #-8]
    // 0xc72d34: LoadField: r2 = r0->field_27
    //     0xc72d34: ldur            w2, [x0, #0x27]
    // 0xc72d38: DecompressPointer r2
    //     0xc72d38: add             x2, x2, HEAP, lsl #32
    // 0xc72d3c: ldur            x1, [fp, #-0x10]
    // 0xc72d40: r0 = visualDensity=()
    //     0xc72d40: bl              #0xc73084  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::visualDensity=
    // 0xc72d44: ldur            x0, [fp, #-8]
    // 0xc72d48: LoadField: r2 = r0->field_2b
    //     0xc72d48: ldur            w2, [x0, #0x2b]
    // 0xc72d4c: DecompressPointer r2
    //     0xc72d4c: add             x2, x2, HEAP, lsl #32
    // 0xc72d50: ldur            x1, [fp, #-0x10]
    // 0xc72d54: r0 = axisDirection=()
    //     0xc72d54: bl              #0xc6985c  ; [package:flutter/src/rendering/list_body.dart] RenderListBody::axisDirection=
    // 0xc72d58: ldur            x0, [fp, #-8]
    // 0xc72d5c: LoadField: r2 = r0->field_2f
    //     0xc72d5c: ldur            w2, [x0, #0x2f]
    // 0xc72d60: DecompressPointer r2
    //     0xc72d60: add             x2, x2, HEAP, lsl #32
    // 0xc72d64: ldur            x1, [fp, #-0x10]
    // 0xc72d68: r0 = titleBaselineType=()
    //     0xc72d68: bl              #0xc73014  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::titleBaselineType=
    // 0xc72d6c: ldur            x0, [fp, #-8]
    // 0xc72d70: LoadField: r2 = r0->field_33
    //     0xc72d70: ldur            w2, [x0, #0x33]
    // 0xc72d74: DecompressPointer r2
    //     0xc72d74: add             x2, x2, HEAP, lsl #32
    // 0xc72d78: ldur            x1, [fp, #-0x10]
    // 0xc72d7c: r0 = subtitleBaselineType=()
    //     0xc72d7c: bl              #0xc72fa4  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::subtitleBaselineType=
    // 0xc72d80: ldur            x0, [fp, #-8]
    // 0xc72d84: LoadField: d0 = r0->field_37
    //     0xc72d84: ldur            d0, [x0, #0x37]
    // 0xc72d88: ldur            x1, [fp, #-0x10]
    // 0xc72d8c: r0 = horizontalTitleGap=()
    //     0xc72d8c: bl              #0xc72f54  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::horizontalTitleGap=
    // 0xc72d90: ldur            x0, [fp, #-8]
    // 0xc72d94: LoadField: d0 = r0->field_47
    //     0xc72d94: ldur            d0, [x0, #0x47]
    // 0xc72d98: ldur            x1, [fp, #-0x10]
    // 0xc72d9c: r0 = minLeadingWidth=()
    //     0xc72d9c: bl              #0xc72f04  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::minLeadingWidth=
    // 0xc72da0: ldur            x0, [fp, #-8]
    // 0xc72da4: LoadField: r2 = r0->field_4f
    //     0xc72da4: ldur            w2, [x0, #0x4f]
    // 0xc72da8: DecompressPointer r2
    //     0xc72da8: add             x2, x2, HEAP, lsl #32
    // 0xc72dac: ldur            x1, [fp, #-0x10]
    // 0xc72db0: r0 = minTileHeight=()
    //     0xc72db0: bl              #0xc72e60  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::minTileHeight=
    // 0xc72db4: ldur            x0, [fp, #-8]
    // 0xc72db8: LoadField: d0 = r0->field_3f
    //     0xc72db8: ldur            d0, [x0, #0x3f]
    // 0xc72dbc: ldur            x1, [fp, #-0x10]
    // 0xc72dc0: r0 = runSpacing=()
    //     0xc72dc0: bl              #0xc69e24  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::runSpacing=
    // 0xc72dc4: ldur            x0, [fp, #-8]
    // 0xc72dc8: LoadField: r2 = r0->field_53
    //     0xc72dc8: ldur            w2, [x0, #0x53]
    // 0xc72dcc: DecompressPointer r2
    //     0xc72dcc: add             x2, x2, HEAP, lsl #32
    // 0xc72dd0: ldur            x1, [fp, #-0x10]
    // 0xc72dd4: r0 = titleAlignment=()
    //     0xc72dd4: bl              #0xc72df0  ; [package:flutter/src/material/list_tile.dart] _RenderListTile::titleAlignment=
    // 0xc72dd8: r0 = Null
    //     0xc72dd8: mov             x0, NULL
    // 0xc72ddc: LeaveFrame
    //     0xc72ddc: mov             SP, fp
    //     0xc72de0: ldp             fp, lr, [SP], #0x10
    // 0xc72de4: ret
    //     0xc72de4: ret             
    // 0xc72de8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc72de8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc72dec: b               #0xc72cd8
  }
  _ childForSlot(/* No info */) {
    // ** addr: 0xd3ca18, size: 0xcc
    // 0xd3ca18: EnterFrame
    //     0xd3ca18: stp             fp, lr, [SP, #-0x10]!
    //     0xd3ca1c: mov             fp, SP
    // 0xd3ca20: AllocStack(0x10)
    //     0xd3ca20: sub             SP, SP, #0x10
    // 0xd3ca24: SetupParameters(_ListTile this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xd3ca24: mov             x0, x2
    //     0xd3ca28: mov             x4, x1
    //     0xd3ca2c: mov             x3, x2
    //     0xd3ca30: stur            x1, [fp, #-8]
    //     0xd3ca34: stur            x2, [fp, #-0x10]
    // 0xd3ca38: r2 = Null
    //     0xd3ca38: mov             x2, NULL
    // 0xd3ca3c: r1 = Null
    //     0xd3ca3c: mov             x1, NULL
    // 0xd3ca40: r4 = 60
    //     0xd3ca40: movz            x4, #0x3c
    // 0xd3ca44: branchIfSmi(r0, 0xd3ca50)
    //     0xd3ca44: tbz             w0, #0, #0xd3ca50
    // 0xd3ca48: r4 = LoadClassIdInstr(r0)
    //     0xd3ca48: ldur            x4, [x0, #-1]
    //     0xd3ca4c: ubfx            x4, x4, #0xc, #0x14
    // 0xd3ca50: r17 = 7056
    //     0xd3ca50: movz            x17, #0x1b90
    // 0xd3ca54: cmp             x4, x17
    // 0xd3ca58: b.eq            #0xd3ca70
    // 0xd3ca5c: r8 = _ListTileSlot
    //     0xd3ca5c: add             x8, PP, #0x54, lsl #12  ; [pp+0x54ce8] Type: _ListTileSlot
    //     0xd3ca60: ldr             x8, [x8, #0xce8]
    // 0xd3ca64: r3 = Null
    //     0xd3ca64: add             x3, PP, #0x54, lsl #12  ; [pp+0x54cf0] Null
    //     0xd3ca68: ldr             x3, [x3, #0xcf0]
    // 0xd3ca6c: r0 = _ListTileSlot()
    //     0xd3ca6c: bl              #0x72f0d8  ; IsType__ListTileSlot_Stub
    // 0xd3ca70: ldur            x1, [fp, #-0x10]
    // 0xd3ca74: LoadField: r2 = r1->field_7
    //     0xd3ca74: ldur            x2, [x1, #7]
    // 0xd3ca78: cmp             x2, #1
    // 0xd3ca7c: b.gt            #0xd3cab0
    // 0xd3ca80: cmp             x2, #0
    // 0xd3ca84: b.gt            #0xd3ca9c
    // 0xd3ca88: ldur            x1, [fp, #-8]
    // 0xd3ca8c: LoadField: r3 = r1->field_f
    //     0xd3ca8c: ldur            w3, [x1, #0xf]
    // 0xd3ca90: DecompressPointer r3
    //     0xd3ca90: add             x3, x3, HEAP, lsl #32
    // 0xd3ca94: mov             x0, x3
    // 0xd3ca98: b               #0xd3cad8
    // 0xd3ca9c: ldur            x1, [fp, #-8]
    // 0xd3caa0: LoadField: r3 = r1->field_13
    //     0xd3caa0: ldur            w3, [x1, #0x13]
    // 0xd3caa4: DecompressPointer r3
    //     0xd3caa4: add             x3, x3, HEAP, lsl #32
    // 0xd3caa8: mov             x0, x3
    // 0xd3caac: b               #0xd3cad8
    // 0xd3cab0: ldur            x1, [fp, #-8]
    // 0xd3cab4: cmp             x2, #2
    // 0xd3cab8: b.gt            #0xd3cacc
    // 0xd3cabc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xd3cabc: ldur            w2, [x1, #0x17]
    // 0xd3cac0: DecompressPointer r2
    //     0xd3cac0: add             x2, x2, HEAP, lsl #32
    // 0xd3cac4: mov             x0, x2
    // 0xd3cac8: b               #0xd3cad8
    // 0xd3cacc: LoadField: r2 = r1->field_1b
    //     0xd3cacc: ldur            w2, [x1, #0x1b]
    // 0xd3cad0: DecompressPointer r2
    //     0xd3cad0: add             x2, x2, HEAP, lsl #32
    // 0xd3cad4: mov             x0, x2
    // 0xd3cad8: LeaveFrame
    //     0xd3cad8: mov             SP, fp
    //     0xd3cadc: ldp             fp, lr, [SP], #0x10
    // 0xd3cae0: ret
    //     0xd3cae0: ret             
  }
  get _ slots(/* No info */) {
    // ** addr: 0xd3ce30, size: 0xc
    // 0xd3ce30: r0 = const [Instance of '_ListTileSlot', Instance of '_ListTileSlot', Instance of '_ListTileSlot', Instance of '_ListTileSlot']
    //     0xd3ce30: add             x0, PP, #0x54, lsl #12  ; [pp+0x54d00] List<_ListTileSlot>(4)
    //     0xd3ce34: ldr             x0, [x0, #0xd00]
    // 0xd3ce38: ret
    //     0xd3ce38: ret             
  }
}

// class id: 5381, size: 0x9c, field offset: 0xc
//   const constructor, 
class ListTile extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xa9dd9c, size: 0x171c
    // 0xa9dd9c: EnterFrame
    //     0xa9dd9c: stp             fp, lr, [SP, #-0x10]!
    //     0xa9dda0: mov             fp, SP
    // 0xa9dda4: AllocStack(0x110)
    //     0xa9dda4: sub             SP, SP, #0x110
    // 0xa9dda8: SetupParameters(ListTile this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa9dda8: mov             x0, x2
    //     0xa9ddac: stur            x2, [fp, #-0x10]
    //     0xa9ddb0: mov             x2, x1
    //     0xa9ddb4: stur            x1, [fp, #-8]
    // 0xa9ddb8: CheckStackOverflow
    //     0xa9ddb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9ddbc: cmp             SP, x16
    //     0xa9ddc0: b.ls            #0xa9f468
    // 0xa9ddc4: mov             x1, x0
    // 0xa9ddc8: r0 = of()
    //     0xa9ddc8: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa9ddcc: ldur            x1, [fp, #-0x10]
    // 0xa9ddd0: stur            x0, [fp, #-0x18]
    // 0xa9ddd4: r0 = of()
    //     0xa9ddd4: bl              #0x9f0a10  ; [package:flutter/src/material/list_tile_theme.dart] ListTileTheme::of
    // 0xa9ddd8: stur            x0, [fp, #-0x28]
    // 0xa9dddc: LoadField: r1 = r0->field_f
    //     0xa9dddc: ldur            w1, [x0, #0xf]
    // 0xa9dde0: DecompressPointer r1
    //     0xa9dde0: add             x1, x1, HEAP, lsl #32
    // 0xa9dde4: cmp             w1, NULL
    // 0xa9dde8: b.eq            #0xa9ddec
    // 0xa9ddec: ldur            x2, [fp, #-0x18]
    // 0xa9ddf0: LoadField: r1 = r2->field_2f
    //     0xa9ddf0: ldur            w1, [x2, #0x2f]
    // 0xa9ddf4: DecompressPointer r1
    //     0xa9ddf4: add             x1, x1, HEAP, lsl #32
    // 0xa9ddf8: stur            x1, [fp, #-0x20]
    // 0xa9ddfc: tbnz            w1, #4, #0xa9de70
    // 0xa9de00: ldur            x3, [fp, #-0x10]
    // 0xa9de04: r0 = _LisTileDefaultsM3()
    //     0xa9de04: bl              #0xa9f564  ; Allocate_LisTileDefaultsM3Stub -> _LisTileDefaultsM3 (size=0x6c)
    // 0xa9de08: mov             x1, x0
    // 0xa9de0c: r0 = Sentinel
    //     0xa9de0c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9de10: StoreField: r1->field_5f = r0
    //     0xa9de10: stur            w0, [x1, #0x5f]
    // 0xa9de14: StoreField: r1->field_63 = r0
    //     0xa9de14: stur            w0, [x1, #0x63]
    // 0xa9de18: StoreField: r1->field_67 = r0
    //     0xa9de18: stur            w0, [x1, #0x67]
    // 0xa9de1c: ldur            x2, [fp, #-0x10]
    // 0xa9de20: StoreField: r1->field_5b = r2
    //     0xa9de20: stur            w2, [x1, #0x5b]
    // 0xa9de24: r0 = Instance_RoundedRectangleBorder
    //     0xa9de24: add             x0, PP, #0x39, lsl #12  ; [pp+0x39848] Obj!RoundedRectangleBorder@e146c1
    //     0xa9de28: ldr             x0, [x0, #0x848]
    // 0xa9de2c: StoreField: r1->field_b = r0
    //     0xa9de2c: stur            w0, [x1, #0xb]
    // 0xa9de30: r0 = Instance_EdgeInsetsDirectional
    //     0xa9de30: add             x0, PP, #0x39, lsl #12  ; [pp+0x39850] Obj!EdgeInsetsDirectional@e11dd1
    //     0xa9de34: ldr             x0, [x0, #0x850]
    // 0xa9de38: StoreField: r1->field_2b = r0
    //     0xa9de38: stur            w0, [x1, #0x2b]
    // 0xa9de3c: r0 = 8.000000
    //     0xa9de3c: add             x0, PP, #0x30, lsl #12  ; [pp+0x303a8] 8
    //     0xa9de40: ldr             x0, [x0, #0x3a8]
    // 0xa9de44: StoreField: r1->field_3b = r0
    //     0xa9de44: stur            w0, [x1, #0x3b]
    // 0xa9de48: r0 = 24.000000
    //     0xa9de48: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xa9de4c: ldr             x0, [x0, #0x368]
    // 0xa9de50: StoreField: r1->field_3f = r0
    //     0xa9de50: stur            w0, [x1, #0x3f]
    // 0xa9de54: mov             x3, x1
    // 0xa9de58: mov             x0, x2
    // 0xa9de5c: r2 = Instance_EdgeInsetsDirectional
    //     0xa9de5c: add             x2, PP, #0x39, lsl #12  ; [pp+0x39850] Obj!EdgeInsetsDirectional@e11dd1
    //     0xa9de60: ldr             x2, [x2, #0x850]
    // 0xa9de64: d1 = 8.000000
    //     0xa9de64: fmov            d1, #8.00000000
    // 0xa9de68: d0 = 24.000000
    //     0xa9de68: fmov            d0, #24.00000000
    // 0xa9de6c: b               #0xa9dee4
    // 0xa9de70: ldur            x2, [fp, #-0x10]
    // 0xa9de74: r0 = Sentinel
    //     0xa9de74: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9de78: r0 = _LisTileDefaultsM2()
    //     0xa9de78: bl              #0xa9f558  ; Allocate_LisTileDefaultsM2Stub -> _LisTileDefaultsM2 (size=0x68)
    // 0xa9de7c: mov             x1, x0
    // 0xa9de80: r0 = Sentinel
    //     0xa9de80: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9de84: StoreField: r1->field_5f = r0
    //     0xa9de84: stur            w0, [x1, #0x5f]
    // 0xa9de88: StoreField: r1->field_63 = r0
    //     0xa9de88: stur            w0, [x1, #0x63]
    // 0xa9de8c: ldur            x0, [fp, #-0x10]
    // 0xa9de90: StoreField: r1->field_5b = r0
    //     0xa9de90: stur            w0, [x1, #0x5b]
    // 0xa9de94: r2 = Instance_Border
    //     0xa9de94: add             x2, PP, #0x39, lsl #12  ; [pp+0x39858] Obj!Border@e145d1
    //     0xa9de98: ldr             x2, [x2, #0x858]
    // 0xa9de9c: StoreField: r1->field_b = r2
    //     0xa9de9c: stur            w2, [x1, #0xb]
    // 0xa9dea0: r2 = Instance_ListTileStyle
    //     0xa9dea0: add             x2, PP, #0x39, lsl #12  ; [pp+0x39860] Obj!ListTileStyle@e36721
    //     0xa9dea4: ldr             x2, [x2, #0x860]
    // 0xa9dea8: StoreField: r1->field_f = r2
    //     0xa9dea8: stur            w2, [x1, #0xf]
    // 0xa9deac: r2 = Instance_EdgeInsets
    //     0xa9deac: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d468] Obj!EdgeInsets@e12221
    //     0xa9deb0: ldr             x2, [x2, #0x468]
    // 0xa9deb4: StoreField: r1->field_2b = r2
    //     0xa9deb4: stur            w2, [x1, #0x2b]
    // 0xa9deb8: r2 = 4.000000
    //     0xa9deb8: add             x2, PP, #0x25, lsl #12  ; [pp+0x25770] 4
    //     0xa9debc: ldr             x2, [x2, #0x770]
    // 0xa9dec0: StoreField: r1->field_3b = r2
    //     0xa9dec0: stur            w2, [x1, #0x3b]
    // 0xa9dec4: r2 = 40.000000
    //     0xa9dec4: add             x2, PP, #0x29, lsl #12  ; [pp+0x291b8] 40
    //     0xa9dec8: ldr             x2, [x2, #0x1b8]
    // 0xa9decc: StoreField: r1->field_3f = r2
    //     0xa9decc: stur            w2, [x1, #0x3f]
    // 0xa9ded0: mov             x3, x1
    // 0xa9ded4: r2 = Instance_EdgeInsets
    //     0xa9ded4: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d468] Obj!EdgeInsets@e12221
    //     0xa9ded8: ldr             x2, [x2, #0x468]
    // 0xa9dedc: d1 = 4.000000
    //     0xa9dedc: fmov            d1, #4.00000000
    // 0xa9dee0: d0 = 40.000000
    //     0xa9dee0: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa9dee4: ldur            x1, [fp, #-8]
    // 0xa9dee8: stur            x3, [fp, #-0x30]
    // 0xa9deec: stur            x2, [fp, #-0x38]
    // 0xa9def0: stur            d1, [fp, #-0xd8]
    // 0xa9def4: stur            d0, [fp, #-0xe0]
    // 0xa9def8: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xa9def8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa9defc: ldr             x0, [x0, #0x778]
    //     0xa9df00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa9df04: cmp             w0, w16
    //     0xa9df08: b.ne            #0xa9df14
    //     0xa9df0c: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xa9df10: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa9df14: r1 = <WidgetState>
    //     0xa9df14: add             x1, PP, #0x39, lsl #12  ; [pp+0x39868] TypeArguments: <WidgetState>
    //     0xa9df18: ldr             x1, [x1, #0x868]
    // 0xa9df1c: stur            x0, [fp, #-0x40]
    // 0xa9df20: r0 = _Set()
    //     0xa9df20: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xa9df24: mov             x1, x0
    // 0xa9df28: ldur            x0, [fp, #-0x40]
    // 0xa9df2c: stur            x1, [fp, #-0x48]
    // 0xa9df30: StoreField: r1->field_1b = r0
    //     0xa9df30: stur            w0, [x1, #0x1b]
    // 0xa9df34: StoreField: r1->field_b = rZR
    //     0xa9df34: stur            wzr, [x1, #0xb]
    // 0xa9df38: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xa9df38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa9df3c: ldr             x0, [x0, #0x780]
    //     0xa9df40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa9df44: cmp             w0, w16
    //     0xa9df48: b.ne            #0xa9df54
    //     0xa9df4c: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xa9df50: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa9df54: mov             x3, x0
    // 0xa9df58: ldur            x0, [fp, #-0x48]
    // 0xa9df5c: stur            x3, [fp, #-0x58]
    // 0xa9df60: StoreField: r0->field_f = r3
    //     0xa9df60: stur            w3, [x0, #0xf]
    // 0xa9df64: StoreField: r0->field_13 = rZR
    //     0xa9df64: stur            wzr, [x0, #0x13]
    // 0xa9df68: ArrayStore: r0[0] = rZR  ; List_4
    //     0xa9df68: stur            wzr, [x0, #0x17]
    // 0xa9df6c: ldur            x4, [fp, #-8]
    // 0xa9df70: LoadField: r5 = r4->field_4b
    //     0xa9df70: ldur            w5, [x4, #0x4b]
    // 0xa9df74: DecompressPointer r5
    //     0xa9df74: add             x5, x5, HEAP, lsl #32
    // 0xa9df78: stur            x5, [fp, #-0x50]
    // 0xa9df7c: tbz             w5, #4, #0xa9df90
    // 0xa9df80: mov             x1, x0
    // 0xa9df84: r2 = Instance_WidgetState
    //     0xa9df84: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d190] Obj!WidgetState@e33841
    //     0xa9df88: ldr             x2, [x2, #0x190]
    // 0xa9df8c: r0 = add()
    //     0xa9df8c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xa9df90: ldur            x0, [fp, #-8]
    // 0xa9df94: LoadField: r3 = r0->field_5f
    //     0xa9df94: ldur            w3, [x0, #0x5f]
    // 0xa9df98: DecompressPointer r3
    //     0xa9df98: add             x3, x3, HEAP, lsl #32
    // 0xa9df9c: stur            x3, [fp, #-0x60]
    // 0xa9dfa0: tbnz            w3, #4, #0xa9dfb4
    // 0xa9dfa4: ldur            x1, [fp, #-0x48]
    // 0xa9dfa8: r2 = Instance_WidgetState
    //     0xa9dfa8: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d0d0] Obj!WidgetState@e338e1
    //     0xa9dfac: ldr             x2, [x2, #0xd0]
    // 0xa9dfb0: r0 = add()
    //     0xa9dfb0: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xa9dfb4: ldur            x0, [fp, #-8]
    // 0xa9dfb8: LoadField: r2 = r0->field_2f
    //     0xa9dfb8: ldur            w2, [x0, #0x2f]
    // 0xa9dfbc: DecompressPointer r2
    //     0xa9dfbc: add             x2, x2, HEAP, lsl #32
    // 0xa9dfc0: stur            x2, [fp, #-0x70]
    // 0xa9dfc4: LoadField: r3 = r0->field_2b
    //     0xa9dfc4: ldur            w3, [x0, #0x2b]
    // 0xa9dfc8: DecompressPointer r3
    //     0xa9dfc8: add             x3, x3, HEAP, lsl #32
    // 0xa9dfcc: stur            x3, [fp, #-0x68]
    // 0xa9dfd0: r1 = <Color?>
    //     0xa9dfd0: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa9dfd4: ldr             x1, [x1, #0x98]
    // 0xa9dfd8: r0 = _IndividualOverrides()
    //     0xa9dfd8: bl              #0xa9f54c  ; Allocate_IndividualOverridesStub -> _IndividualOverrides (size=0x1c)
    // 0xa9dfdc: mov             x1, x0
    // 0xa9dfe0: ldur            x0, [fp, #-0x70]
    // 0xa9dfe4: StoreField: r1->field_b = r0
    //     0xa9dfe4: stur            w0, [x1, #0xb]
    // 0xa9dfe8: StoreField: r1->field_f = r0
    //     0xa9dfe8: stur            w0, [x1, #0xf]
    // 0xa9dfec: ldur            x0, [fp, #-0x68]
    // 0xa9dff0: StoreField: r1->field_13 = r0
    //     0xa9dff0: stur            w0, [x1, #0x13]
    // 0xa9dff4: ldur            x2, [fp, #-0x48]
    // 0xa9dff8: r0 = resolve()
    //     0xa9dff8: bl              #0xd942c0  ; [package:flutter/src/material/list_tile.dart] _IndividualOverrides::resolve
    // 0xa9dffc: cmp             w0, NULL
    // 0xa9e000: b.ne            #0xa9e04c
    // 0xa9e004: ldur            x3, [fp, #-0x28]
    // 0xa9e008: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa9e008: ldur            w0, [x3, #0x17]
    // 0xa9e00c: DecompressPointer r0
    //     0xa9e00c: add             x0, x0, HEAP, lsl #32
    // 0xa9e010: stur            x0, [fp, #-0x78]
    // 0xa9e014: LoadField: r2 = r3->field_13
    //     0xa9e014: ldur            w2, [x3, #0x13]
    // 0xa9e018: DecompressPointer r2
    //     0xa9e018: add             x2, x2, HEAP, lsl #32
    // 0xa9e01c: stur            x2, [fp, #-0x70]
    // 0xa9e020: r1 = <Color?>
    //     0xa9e020: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa9e024: ldr             x1, [x1, #0x98]
    // 0xa9e028: r0 = _IndividualOverrides()
    //     0xa9e028: bl              #0xa9f54c  ; Allocate_IndividualOverridesStub -> _IndividualOverrides (size=0x1c)
    // 0xa9e02c: mov             x1, x0
    // 0xa9e030: ldur            x0, [fp, #-0x78]
    // 0xa9e034: StoreField: r1->field_b = r0
    //     0xa9e034: stur            w0, [x1, #0xb]
    // 0xa9e038: StoreField: r1->field_f = r0
    //     0xa9e038: stur            w0, [x1, #0xf]
    // 0xa9e03c: ldur            x0, [fp, #-0x70]
    // 0xa9e040: StoreField: r1->field_13 = r0
    //     0xa9e040: stur            w0, [x1, #0x13]
    // 0xa9e044: ldur            x2, [fp, #-0x48]
    // 0xa9e048: r0 = resolve()
    //     0xa9e048: bl              #0xd942c0  ; [package:flutter/src/material/list_tile.dart] _IndividualOverrides::resolve
    // 0xa9e04c: cmp             w0, NULL
    // 0xa9e050: b.ne            #0xa9e0a4
    // 0xa9e054: ldur            x2, [fp, #-0x18]
    // 0xa9e058: LoadField: r0 = r2->field_ef
    //     0xa9e058: ldur            w0, [x2, #0xef]
    // 0xa9e05c: DecompressPointer r0
    //     0xa9e05c: add             x0, x0, HEAP, lsl #32
    // 0xa9e060: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa9e060: ldur            w3, [x0, #0x17]
    // 0xa9e064: DecompressPointer r3
    //     0xa9e064: add             x3, x3, HEAP, lsl #32
    // 0xa9e068: stur            x3, [fp, #-0x78]
    // 0xa9e06c: LoadField: r4 = r0->field_13
    //     0xa9e06c: ldur            w4, [x0, #0x13]
    // 0xa9e070: DecompressPointer r4
    //     0xa9e070: add             x4, x4, HEAP, lsl #32
    // 0xa9e074: stur            x4, [fp, #-0x70]
    // 0xa9e078: r1 = <Color?>
    //     0xa9e078: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa9e07c: ldr             x1, [x1, #0x98]
    // 0xa9e080: r0 = _IndividualOverrides()
    //     0xa9e080: bl              #0xa9f54c  ; Allocate_IndividualOverridesStub -> _IndividualOverrides (size=0x1c)
    // 0xa9e084: mov             x1, x0
    // 0xa9e088: ldur            x0, [fp, #-0x78]
    // 0xa9e08c: StoreField: r1->field_b = r0
    //     0xa9e08c: stur            w0, [x1, #0xb]
    // 0xa9e090: StoreField: r1->field_f = r0
    //     0xa9e090: stur            w0, [x1, #0xf]
    // 0xa9e094: ldur            x0, [fp, #-0x70]
    // 0xa9e098: StoreField: r1->field_13 = r0
    //     0xa9e098: stur            w0, [x1, #0x13]
    // 0xa9e09c: ldur            x2, [fp, #-0x48]
    // 0xa9e0a0: r0 = resolve()
    //     0xa9e0a0: bl              #0xd942c0  ; [package:flutter/src/material/list_tile.dart] _IndividualOverrides::resolve
    // 0xa9e0a4: cmp             w0, NULL
    // 0xa9e0a8: b.ne            #0xa9e360
    // 0xa9e0ac: ldur            x0, [fp, #-0x30]
    // 0xa9e0b0: r2 = LoadClassIdInstr(r0)
    //     0xa9e0b0: ldur            x2, [x0, #-1]
    //     0xa9e0b4: ubfx            x2, x2, #0xc, #0x14
    // 0xa9e0b8: stur            x2, [fp, #-0x80]
    // 0xa9e0bc: cmp             x2, #0xf64
    // 0xa9e0c0: b.ne            #0xa9e0d8
    // 0xa9e0c4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa9e0c4: ldur            w1, [x0, #0x17]
    // 0xa9e0c8: DecompressPointer r1
    //     0xa9e0c8: add             x1, x1, HEAP, lsl #32
    // 0xa9e0cc: mov             x0, x2
    // 0xa9e0d0: mov             x2, x1
    // 0xa9e0d4: b               #0xa9e18c
    // 0xa9e0d8: cmp             x2, #0xf65
    // 0xa9e0dc: b.ne            #0xa9e134
    // 0xa9e0e0: mov             x1, x0
    // 0xa9e0e4: LoadField: r0 = r1->field_63
    //     0xa9e0e4: ldur            w0, [x1, #0x63]
    // 0xa9e0e8: DecompressPointer r0
    //     0xa9e0e8: add             x0, x0, HEAP, lsl #32
    // 0xa9e0ec: r16 = Sentinel
    //     0xa9e0ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e0f0: cmp             w0, w16
    // 0xa9e0f4: b.ne            #0xa9e104
    // 0xa9e0f8: r2 = _colors
    //     0xa9e0f8: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d208] Field <_LisTileDefaultsM3@560247952._colors@560247952>: late final (offset: 0x64)
    //     0xa9e0fc: ldr             x2, [x2, #0x208]
    // 0xa9e100: r0 = InitLateFinalInstanceField()
    //     0xa9e100: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e104: LoadField: r1 = r0->field_a3
    //     0xa9e104: ldur            w1, [x0, #0xa3]
    // 0xa9e108: DecompressPointer r1
    //     0xa9e108: add             x1, x1, HEAP, lsl #32
    // 0xa9e10c: cmp             w1, NULL
    // 0xa9e110: b.ne            #0xa9e124
    // 0xa9e114: LoadField: r1 = r0->field_7f
    //     0xa9e114: ldur            w1, [x0, #0x7f]
    // 0xa9e118: DecompressPointer r1
    //     0xa9e118: add             x1, x1, HEAP, lsl #32
    // 0xa9e11c: mov             x0, x1
    // 0xa9e120: b               #0xa9e128
    // 0xa9e124: mov             x0, x1
    // 0xa9e128: mov             x2, x0
    // 0xa9e12c: ldur            x0, [fp, #-0x80]
    // 0xa9e130: b               #0xa9e18c
    // 0xa9e134: ldur            x1, [fp, #-0x30]
    // 0xa9e138: LoadField: r0 = r1->field_5f
    //     0xa9e138: ldur            w0, [x1, #0x5f]
    // 0xa9e13c: DecompressPointer r0
    //     0xa9e13c: add             x0, x0, HEAP, lsl #32
    // 0xa9e140: r16 = Sentinel
    //     0xa9e140: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e144: cmp             w0, w16
    // 0xa9e148: b.ne            #0xa9e158
    // 0xa9e14c: r2 = _theme
    //     0xa9e14c: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d210] Field <_LisTileDefaultsM2@560247952._theme@560247952>: late final (offset: 0x60)
    //     0xa9e150: ldr             x2, [x2, #0x210]
    // 0xa9e154: r0 = InitLateFinalInstanceField()
    //     0xa9e154: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e158: LoadField: r1 = r0->field_3f
    //     0xa9e158: ldur            w1, [x0, #0x3f]
    // 0xa9e15c: DecompressPointer r1
    //     0xa9e15c: add             x1, x1, HEAP, lsl #32
    // 0xa9e160: LoadField: r0 = r1->field_7
    //     0xa9e160: ldur            w0, [x1, #7]
    // 0xa9e164: DecompressPointer r0
    //     0xa9e164: add             x0, x0, HEAP, lsl #32
    // 0xa9e168: LoadField: r1 = r0->field_7
    //     0xa9e168: ldur            x1, [x0, #7]
    // 0xa9e16c: cmp             x1, #0
    // 0xa9e170: b.gt            #0xa9e17c
    // 0xa9e174: r0 = Null
    //     0xa9e174: mov             x0, NULL
    // 0xa9e178: b               #0xa9e184
    // 0xa9e17c: r0 = Instance_Color
    //     0xa9e17c: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d218] Obj!Color@e28bc1
    //     0xa9e180: ldr             x0, [x0, #0x218]
    // 0xa9e184: mov             x2, x0
    // 0xa9e188: ldur            x0, [fp, #-0x80]
    // 0xa9e18c: stur            x2, [fp, #-0x70]
    // 0xa9e190: cmp             x0, #0xf64
    // 0xa9e194: b.ne            #0xa9e1ac
    // 0xa9e198: ldur            x3, [fp, #-0x30]
    // 0xa9e19c: LoadField: r1 = r3->field_13
    //     0xa9e19c: ldur            w1, [x3, #0x13]
    // 0xa9e1a0: DecompressPointer r1
    //     0xa9e1a0: add             x1, x1, HEAP, lsl #32
    // 0xa9e1a4: mov             x2, x1
    // 0xa9e1a8: b               #0xa9e22c
    // 0xa9e1ac: ldur            x3, [fp, #-0x30]
    // 0xa9e1b0: cmp             x0, #0xf65
    // 0xa9e1b4: b.ne            #0xa9e1f0
    // 0xa9e1b8: mov             x1, x3
    // 0xa9e1bc: LoadField: r0 = r1->field_63
    //     0xa9e1bc: ldur            w0, [x1, #0x63]
    // 0xa9e1c0: DecompressPointer r0
    //     0xa9e1c0: add             x0, x0, HEAP, lsl #32
    // 0xa9e1c4: r16 = Sentinel
    //     0xa9e1c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e1c8: cmp             w0, w16
    // 0xa9e1cc: b.ne            #0xa9e1dc
    // 0xa9e1d0: r2 = _colors
    //     0xa9e1d0: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d208] Field <_LisTileDefaultsM3@560247952._colors@560247952>: late final (offset: 0x64)
    //     0xa9e1d4: ldr             x2, [x2, #0x208]
    // 0xa9e1d8: r0 = InitLateFinalInstanceField()
    //     0xa9e1d8: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e1dc: LoadField: r1 = r0->field_b
    //     0xa9e1dc: ldur            w1, [x0, #0xb]
    // 0xa9e1e0: DecompressPointer r1
    //     0xa9e1e0: add             x1, x1, HEAP, lsl #32
    // 0xa9e1e4: mov             x2, x1
    // 0xa9e1e8: ldur            x0, [fp, #-0x80]
    // 0xa9e1ec: b               #0xa9e22c
    // 0xa9e1f0: ldur            x1, [fp, #-0x30]
    // 0xa9e1f4: LoadField: r0 = r1->field_5f
    //     0xa9e1f4: ldur            w0, [x1, #0x5f]
    // 0xa9e1f8: DecompressPointer r0
    //     0xa9e1f8: add             x0, x0, HEAP, lsl #32
    // 0xa9e1fc: r16 = Sentinel
    //     0xa9e1fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e200: cmp             w0, w16
    // 0xa9e204: b.ne            #0xa9e214
    // 0xa9e208: r2 = _theme
    //     0xa9e208: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d210] Field <_LisTileDefaultsM2@560247952._theme@560247952>: late final (offset: 0x60)
    //     0xa9e20c: ldr             x2, [x2, #0x210]
    // 0xa9e210: r0 = InitLateFinalInstanceField()
    //     0xa9e210: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e214: LoadField: r1 = r0->field_3f
    //     0xa9e214: ldur            w1, [x0, #0x3f]
    // 0xa9e218: DecompressPointer r1
    //     0xa9e218: add             x1, x1, HEAP, lsl #32
    // 0xa9e21c: LoadField: r0 = r1->field_b
    //     0xa9e21c: ldur            w0, [x1, #0xb]
    // 0xa9e220: DecompressPointer r0
    //     0xa9e220: add             x0, x0, HEAP, lsl #32
    // 0xa9e224: mov             x2, x0
    // 0xa9e228: ldur            x0, [fp, #-0x80]
    // 0xa9e22c: stur            x2, [fp, #-0x78]
    // 0xa9e230: cmp             x0, #0xf64
    // 0xa9e234: b.ne            #0xa9e250
    // 0xa9e238: ldur            x3, [fp, #-0x30]
    // 0xa9e23c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa9e23c: ldur            w0, [x3, #0x17]
    // 0xa9e240: DecompressPointer r0
    //     0xa9e240: add             x0, x0, HEAP, lsl #32
    // 0xa9e244: mov             x4, x0
    // 0xa9e248: mov             x0, x2
    // 0xa9e24c: b               #0xa9e308
    // 0xa9e250: ldur            x3, [fp, #-0x30]
    // 0xa9e254: cmp             x0, #0xf65
    // 0xa9e258: b.ne            #0xa9e2b0
    // 0xa9e25c: mov             x1, x3
    // 0xa9e260: LoadField: r0 = r1->field_63
    //     0xa9e260: ldur            w0, [x1, #0x63]
    // 0xa9e264: DecompressPointer r0
    //     0xa9e264: add             x0, x0, HEAP, lsl #32
    // 0xa9e268: r16 = Sentinel
    //     0xa9e268: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e26c: cmp             w0, w16
    // 0xa9e270: b.ne            #0xa9e280
    // 0xa9e274: r2 = _colors
    //     0xa9e274: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d208] Field <_LisTileDefaultsM3@560247952._colors@560247952>: late final (offset: 0x64)
    //     0xa9e278: ldr             x2, [x2, #0x208]
    // 0xa9e27c: r0 = InitLateFinalInstanceField()
    //     0xa9e27c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e280: LoadField: r1 = r0->field_a3
    //     0xa9e280: ldur            w1, [x0, #0xa3]
    // 0xa9e284: DecompressPointer r1
    //     0xa9e284: add             x1, x1, HEAP, lsl #32
    // 0xa9e288: cmp             w1, NULL
    // 0xa9e28c: b.ne            #0xa9e2a0
    // 0xa9e290: LoadField: r1 = r0->field_7f
    //     0xa9e290: ldur            w1, [x0, #0x7f]
    // 0xa9e294: DecompressPointer r1
    //     0xa9e294: add             x1, x1, HEAP, lsl #32
    // 0xa9e298: mov             x0, x1
    // 0xa9e29c: b               #0xa9e2a4
    // 0xa9e2a0: mov             x0, x1
    // 0xa9e2a4: mov             x4, x0
    // 0xa9e2a8: ldur            x0, [fp, #-0x78]
    // 0xa9e2ac: b               #0xa9e308
    // 0xa9e2b0: ldur            x1, [fp, #-0x30]
    // 0xa9e2b4: LoadField: r0 = r1->field_5f
    //     0xa9e2b4: ldur            w0, [x1, #0x5f]
    // 0xa9e2b8: DecompressPointer r0
    //     0xa9e2b8: add             x0, x0, HEAP, lsl #32
    // 0xa9e2bc: r16 = Sentinel
    //     0xa9e2bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e2c0: cmp             w0, w16
    // 0xa9e2c4: b.ne            #0xa9e2d4
    // 0xa9e2c8: r2 = _theme
    //     0xa9e2c8: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d210] Field <_LisTileDefaultsM2@560247952._theme@560247952>: late final (offset: 0x60)
    //     0xa9e2cc: ldr             x2, [x2, #0x210]
    // 0xa9e2d0: r0 = InitLateFinalInstanceField()
    //     0xa9e2d0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e2d4: LoadField: r1 = r0->field_3f
    //     0xa9e2d4: ldur            w1, [x0, #0x3f]
    // 0xa9e2d8: DecompressPointer r1
    //     0xa9e2d8: add             x1, x1, HEAP, lsl #32
    // 0xa9e2dc: LoadField: r0 = r1->field_7
    //     0xa9e2dc: ldur            w0, [x1, #7]
    // 0xa9e2e0: DecompressPointer r0
    //     0xa9e2e0: add             x0, x0, HEAP, lsl #32
    // 0xa9e2e4: LoadField: r1 = r0->field_7
    //     0xa9e2e4: ldur            x1, [x0, #7]
    // 0xa9e2e8: cmp             x1, #0
    // 0xa9e2ec: b.gt            #0xa9e2f8
    // 0xa9e2f0: r0 = Null
    //     0xa9e2f0: mov             x0, NULL
    // 0xa9e2f4: b               #0xa9e300
    // 0xa9e2f8: r0 = Instance_Color
    //     0xa9e2f8: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d218] Obj!Color@e28bc1
    //     0xa9e2fc: ldr             x0, [x0, #0x218]
    // 0xa9e300: mov             x4, x0
    // 0xa9e304: ldur            x0, [fp, #-0x78]
    // 0xa9e308: ldur            x3, [fp, #-0x18]
    // 0xa9e30c: ldur            x2, [fp, #-0x70]
    // 0xa9e310: stur            x4, [fp, #-0x90]
    // 0xa9e314: LoadField: r5 = r3->field_47
    //     0xa9e314: ldur            w5, [x3, #0x47]
    // 0xa9e318: DecompressPointer r5
    //     0xa9e318: add             x5, x5, HEAP, lsl #32
    // 0xa9e31c: stur            x5, [fp, #-0x88]
    // 0xa9e320: r1 = <Color?>
    //     0xa9e320: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa9e324: ldr             x1, [x1, #0x98]
    // 0xa9e328: r0 = _IndividualOverrides()
    //     0xa9e328: bl              #0xa9f54c  ; Allocate_IndividualOverridesStub -> _IndividualOverrides (size=0x1c)
    // 0xa9e32c: mov             x1, x0
    // 0xa9e330: ldur            x0, [fp, #-0x70]
    // 0xa9e334: StoreField: r1->field_b = r0
    //     0xa9e334: stur            w0, [x1, #0xb]
    // 0xa9e338: ldur            x0, [fp, #-0x90]
    // 0xa9e33c: StoreField: r1->field_f = r0
    //     0xa9e33c: stur            w0, [x1, #0xf]
    // 0xa9e340: ldur            x0, [fp, #-0x78]
    // 0xa9e344: StoreField: r1->field_13 = r0
    //     0xa9e344: stur            w0, [x1, #0x13]
    // 0xa9e348: ldur            x0, [fp, #-0x88]
    // 0xa9e34c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa9e34c: stur            w0, [x1, #0x17]
    // 0xa9e350: ldur            x2, [fp, #-0x48]
    // 0xa9e354: r0 = resolve()
    //     0xa9e354: bl              #0xd942c0  ; [package:flutter/src/material/list_tile.dart] _IndividualOverrides::resolve
    // 0xa9e358: mov             x2, x0
    // 0xa9e35c: b               #0xa9e364
    // 0xa9e360: mov             x2, x0
    // 0xa9e364: ldur            x0, [fp, #-0x68]
    // 0xa9e368: stur            x2, [fp, #-0x70]
    // 0xa9e36c: r1 = <Color?>
    //     0xa9e36c: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa9e370: ldr             x1, [x1, #0x98]
    // 0xa9e374: r0 = _IndividualOverrides()
    //     0xa9e374: bl              #0xa9f54c  ; Allocate_IndividualOverridesStub -> _IndividualOverrides (size=0x1c)
    // 0xa9e378: mov             x1, x0
    // 0xa9e37c: ldur            x0, [fp, #-0x68]
    // 0xa9e380: StoreField: r1->field_13 = r0
    //     0xa9e380: stur            w0, [x1, #0x13]
    // 0xa9e384: ldur            x2, [fp, #-0x48]
    // 0xa9e388: r0 = resolve()
    //     0xa9e388: bl              #0xd942c0  ; [package:flutter/src/material/list_tile.dart] _IndividualOverrides::resolve
    // 0xa9e38c: cmp             w0, NULL
    // 0xa9e390: b.ne            #0xa9e3dc
    // 0xa9e394: ldur            x3, [fp, #-0x28]
    // 0xa9e398: LoadField: r0 = r3->field_1b
    //     0xa9e398: ldur            w0, [x3, #0x1b]
    // 0xa9e39c: DecompressPointer r0
    //     0xa9e39c: add             x0, x0, HEAP, lsl #32
    // 0xa9e3a0: stur            x0, [fp, #-0x78]
    // 0xa9e3a4: LoadField: r2 = r3->field_13
    //     0xa9e3a4: ldur            w2, [x3, #0x13]
    // 0xa9e3a8: DecompressPointer r2
    //     0xa9e3a8: add             x2, x2, HEAP, lsl #32
    // 0xa9e3ac: stur            x2, [fp, #-0x68]
    // 0xa9e3b0: r1 = <Color?>
    //     0xa9e3b0: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa9e3b4: ldr             x1, [x1, #0x98]
    // 0xa9e3b8: r0 = _IndividualOverrides()
    //     0xa9e3b8: bl              #0xa9f54c  ; Allocate_IndividualOverridesStub -> _IndividualOverrides (size=0x1c)
    // 0xa9e3bc: mov             x1, x0
    // 0xa9e3c0: ldur            x0, [fp, #-0x78]
    // 0xa9e3c4: StoreField: r1->field_b = r0
    //     0xa9e3c4: stur            w0, [x1, #0xb]
    // 0xa9e3c8: StoreField: r1->field_f = r0
    //     0xa9e3c8: stur            w0, [x1, #0xf]
    // 0xa9e3cc: ldur            x0, [fp, #-0x68]
    // 0xa9e3d0: StoreField: r1->field_13 = r0
    //     0xa9e3d0: stur            w0, [x1, #0x13]
    // 0xa9e3d4: ldur            x2, [fp, #-0x48]
    // 0xa9e3d8: r0 = resolve()
    //     0xa9e3d8: bl              #0xd942c0  ; [package:flutter/src/material/list_tile.dart] _IndividualOverrides::resolve
    // 0xa9e3dc: cmp             w0, NULL
    // 0xa9e3e0: b.ne            #0xa9e434
    // 0xa9e3e4: ldur            x2, [fp, #-0x18]
    // 0xa9e3e8: LoadField: r0 = r2->field_ef
    //     0xa9e3e8: ldur            w0, [x2, #0xef]
    // 0xa9e3ec: DecompressPointer r0
    //     0xa9e3ec: add             x0, x0, HEAP, lsl #32
    // 0xa9e3f0: LoadField: r3 = r0->field_1b
    //     0xa9e3f0: ldur            w3, [x0, #0x1b]
    // 0xa9e3f4: DecompressPointer r3
    //     0xa9e3f4: add             x3, x3, HEAP, lsl #32
    // 0xa9e3f8: stur            x3, [fp, #-0x78]
    // 0xa9e3fc: LoadField: r4 = r0->field_13
    //     0xa9e3fc: ldur            w4, [x0, #0x13]
    // 0xa9e400: DecompressPointer r4
    //     0xa9e400: add             x4, x4, HEAP, lsl #32
    // 0xa9e404: stur            x4, [fp, #-0x68]
    // 0xa9e408: r1 = <Color?>
    //     0xa9e408: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa9e40c: ldr             x1, [x1, #0x98]
    // 0xa9e410: r0 = _IndividualOverrides()
    //     0xa9e410: bl              #0xa9f54c  ; Allocate_IndividualOverridesStub -> _IndividualOverrides (size=0x1c)
    // 0xa9e414: mov             x1, x0
    // 0xa9e418: ldur            x0, [fp, #-0x78]
    // 0xa9e41c: StoreField: r1->field_b = r0
    //     0xa9e41c: stur            w0, [x1, #0xb]
    // 0xa9e420: StoreField: r1->field_f = r0
    //     0xa9e420: stur            w0, [x1, #0xf]
    // 0xa9e424: ldur            x0, [fp, #-0x68]
    // 0xa9e428: StoreField: r1->field_13 = r0
    //     0xa9e428: stur            w0, [x1, #0x13]
    // 0xa9e42c: ldur            x2, [fp, #-0x48]
    // 0xa9e430: r0 = resolve()
    //     0xa9e430: bl              #0xd942c0  ; [package:flutter/src/material/list_tile.dart] _IndividualOverrides::resolve
    // 0xa9e434: cmp             w0, NULL
    // 0xa9e438: b.ne            #0xa9e53c
    // 0xa9e43c: ldur            x0, [fp, #-0x30]
    // 0xa9e440: LoadField: r2 = r0->field_1b
    //     0xa9e440: ldur            w2, [x0, #0x1b]
    // 0xa9e444: DecompressPointer r2
    //     0xa9e444: add             x2, x2, HEAP, lsl #32
    // 0xa9e448: stur            x2, [fp, #-0x68]
    // 0xa9e44c: r1 = LoadClassIdInstr(r0)
    //     0xa9e44c: ldur            x1, [x0, #-1]
    //     0xa9e450: ubfx            x1, x1, #0xc, #0x14
    // 0xa9e454: cmp             x1, #0xf64
    // 0xa9e458: b.ne            #0xa9e470
    // 0xa9e45c: LoadField: r1 = r0->field_13
    //     0xa9e45c: ldur            w1, [x0, #0x13]
    // 0xa9e460: DecompressPointer r1
    //     0xa9e460: add             x1, x1, HEAP, lsl #32
    // 0xa9e464: mov             x3, x1
    // 0xa9e468: mov             x0, x2
    // 0xa9e46c: b               #0xa9e4ec
    // 0xa9e470: cmp             x1, #0xf65
    // 0xa9e474: b.ne            #0xa9e4b0
    // 0xa9e478: mov             x1, x0
    // 0xa9e47c: LoadField: r0 = r1->field_63
    //     0xa9e47c: ldur            w0, [x1, #0x63]
    // 0xa9e480: DecompressPointer r0
    //     0xa9e480: add             x0, x0, HEAP, lsl #32
    // 0xa9e484: r16 = Sentinel
    //     0xa9e484: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e488: cmp             w0, w16
    // 0xa9e48c: b.ne            #0xa9e49c
    // 0xa9e490: r2 = _colors
    //     0xa9e490: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d208] Field <_LisTileDefaultsM3@560247952._colors@560247952>: late final (offset: 0x64)
    //     0xa9e494: ldr             x2, [x2, #0x208]
    // 0xa9e498: r0 = InitLateFinalInstanceField()
    //     0xa9e498: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e49c: LoadField: r1 = r0->field_b
    //     0xa9e49c: ldur            w1, [x0, #0xb]
    // 0xa9e4a0: DecompressPointer r1
    //     0xa9e4a0: add             x1, x1, HEAP, lsl #32
    // 0xa9e4a4: mov             x3, x1
    // 0xa9e4a8: ldur            x0, [fp, #-0x68]
    // 0xa9e4ac: b               #0xa9e4ec
    // 0xa9e4b0: ldur            x1, [fp, #-0x30]
    // 0xa9e4b4: LoadField: r0 = r1->field_5f
    //     0xa9e4b4: ldur            w0, [x1, #0x5f]
    // 0xa9e4b8: DecompressPointer r0
    //     0xa9e4b8: add             x0, x0, HEAP, lsl #32
    // 0xa9e4bc: r16 = Sentinel
    //     0xa9e4bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e4c0: cmp             w0, w16
    // 0xa9e4c4: b.ne            #0xa9e4d4
    // 0xa9e4c8: r2 = _theme
    //     0xa9e4c8: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d210] Field <_LisTileDefaultsM2@560247952._theme@560247952>: late final (offset: 0x60)
    //     0xa9e4cc: ldr             x2, [x2, #0x210]
    // 0xa9e4d0: r0 = InitLateFinalInstanceField()
    //     0xa9e4d0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e4d4: LoadField: r1 = r0->field_3f
    //     0xa9e4d4: ldur            w1, [x0, #0x3f]
    // 0xa9e4d8: DecompressPointer r1
    //     0xa9e4d8: add             x1, x1, HEAP, lsl #32
    // 0xa9e4dc: LoadField: r0 = r1->field_b
    //     0xa9e4dc: ldur            w0, [x1, #0xb]
    // 0xa9e4e0: DecompressPointer r0
    //     0xa9e4e0: add             x0, x0, HEAP, lsl #32
    // 0xa9e4e4: mov             x3, x0
    // 0xa9e4e8: ldur            x0, [fp, #-0x68]
    // 0xa9e4ec: ldur            x2, [fp, #-0x18]
    // 0xa9e4f0: stur            x3, [fp, #-0x88]
    // 0xa9e4f4: LoadField: r4 = r2->field_47
    //     0xa9e4f4: ldur            w4, [x2, #0x47]
    // 0xa9e4f8: DecompressPointer r4
    //     0xa9e4f8: add             x4, x4, HEAP, lsl #32
    // 0xa9e4fc: stur            x4, [fp, #-0x78]
    // 0xa9e500: r1 = <Color?>
    //     0xa9e500: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa9e504: ldr             x1, [x1, #0x98]
    // 0xa9e508: r0 = _IndividualOverrides()
    //     0xa9e508: bl              #0xa9f54c  ; Allocate_IndividualOverridesStub -> _IndividualOverrides (size=0x1c)
    // 0xa9e50c: mov             x1, x0
    // 0xa9e510: ldur            x0, [fp, #-0x68]
    // 0xa9e514: StoreField: r1->field_b = r0
    //     0xa9e514: stur            w0, [x1, #0xb]
    // 0xa9e518: StoreField: r1->field_f = r0
    //     0xa9e518: stur            w0, [x1, #0xf]
    // 0xa9e51c: ldur            x0, [fp, #-0x88]
    // 0xa9e520: StoreField: r1->field_13 = r0
    //     0xa9e520: stur            w0, [x1, #0x13]
    // 0xa9e524: ldur            x0, [fp, #-0x78]
    // 0xa9e528: ArrayStore: r1[0] = r0  ; List_4
    //     0xa9e528: stur            w0, [x1, #0x17]
    // 0xa9e52c: ldur            x2, [fp, #-0x48]
    // 0xa9e530: r0 = resolve()
    //     0xa9e530: bl              #0xd942c0  ; [package:flutter/src/material/list_tile.dart] _IndividualOverrides::resolve
    // 0xa9e534: mov             x2, x0
    // 0xa9e538: b               #0xa9e540
    // 0xa9e53c: mov             x2, x0
    // 0xa9e540: ldur            x0, [fp, #-8]
    // 0xa9e544: ldur            x1, [fp, #-0x70]
    // 0xa9e548: stur            x2, [fp, #-0x48]
    // 0xa9e54c: r0 = IconThemeData()
    //     0xa9e54c: bl              #0x63d1c0  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0xa9e550: ldur            x1, [fp, #-0x70]
    // 0xa9e554: stur            x0, [fp, #-0x68]
    // 0xa9e558: StoreField: r0->field_1b = r1
    //     0xa9e558: stur            w1, [x0, #0x1b]
    // 0xa9e55c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa9e55c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa9e560: r0 = styleFrom()
    //     0xa9e560: bl              #0x9e80d8  ; [package:flutter/src/material/icon_button.dart] IconButton::styleFrom
    // 0xa9e564: stur            x0, [fp, #-0x70]
    // 0xa9e568: r0 = IconButtonThemeData()
    //     0xa9e568: bl              #0x87fb9c  ; AllocateIconButtonThemeDataStub -> IconButtonThemeData (size=0xc)
    // 0xa9e56c: mov             x2, x0
    // 0xa9e570: ldur            x0, [fp, #-0x70]
    // 0xa9e574: stur            x2, [fp, #-0x78]
    // 0xa9e578: StoreField: r2->field_7 = r0
    //     0xa9e578: stur            w0, [x2, #7]
    // 0xa9e57c: ldur            x0, [fp, #-8]
    // 0xa9e580: LoadField: r3 = r0->field_b
    //     0xa9e580: ldur            w3, [x0, #0xb]
    // 0xa9e584: DecompressPointer r3
    //     0xa9e584: add             x3, x3, HEAP, lsl #32
    // 0xa9e588: stur            x3, [fp, #-0x70]
    // 0xa9e58c: cmp             w3, NULL
    // 0xa9e590: b.ne            #0xa9e5a4
    // 0xa9e594: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa9e594: ldur            w1, [x0, #0x17]
    // 0xa9e598: DecompressPointer r1
    //     0xa9e598: add             x1, x1, HEAP, lsl #32
    // 0xa9e59c: cmp             w1, NULL
    // 0xa9e5a0: b.eq            #0xa9e6b8
    // 0xa9e5a4: ldur            x4, [fp, #-0x30]
    // 0xa9e5a8: r1 = LoadClassIdInstr(r4)
    //     0xa9e5a8: ldur            x1, [x4, #-1]
    //     0xa9e5ac: ubfx            x1, x1, #0xc, #0x14
    // 0xa9e5b0: cmp             x1, #0xf64
    // 0xa9e5b4: b.ne            #0xa9e5c4
    // 0xa9e5b8: LoadField: r1 = r4->field_27
    //     0xa9e5b8: ldur            w1, [x4, #0x27]
    // 0xa9e5bc: DecompressPointer r1
    //     0xa9e5bc: add             x1, x1, HEAP, lsl #32
    // 0xa9e5c0: b               #0xa9e694
    // 0xa9e5c4: cmp             x1, #0xf65
    // 0xa9e5c8: b.ne            #0xa9e668
    // 0xa9e5cc: mov             x1, x4
    // 0xa9e5d0: LoadField: r0 = r1->field_67
    //     0xa9e5d0: ldur            w0, [x1, #0x67]
    // 0xa9e5d4: DecompressPointer r0
    //     0xa9e5d4: add             x0, x0, HEAP, lsl #32
    // 0xa9e5d8: r16 = Sentinel
    //     0xa9e5d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e5dc: cmp             w0, w16
    // 0xa9e5e0: b.ne            #0xa9e5f0
    // 0xa9e5e4: r2 = _textTheme
    //     0xa9e5e4: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d220] Field <_LisTileDefaultsM3@560247952._textTheme@560247952>: late final (offset: 0x68)
    //     0xa9e5e8: ldr             x2, [x2, #0x220]
    // 0xa9e5ec: r0 = InitLateFinalInstanceField()
    //     0xa9e5ec: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e5f0: LoadField: r2 = r0->field_3f
    //     0xa9e5f0: ldur            w2, [x0, #0x3f]
    // 0xa9e5f4: DecompressPointer r2
    //     0xa9e5f4: add             x2, x2, HEAP, lsl #32
    // 0xa9e5f8: stur            x2, [fp, #-0x88]
    // 0xa9e5fc: cmp             w2, NULL
    // 0xa9e600: b.eq            #0xa9f470
    // 0xa9e604: ldur            x1, [fp, #-0x30]
    // 0xa9e608: LoadField: r0 = r1->field_63
    //     0xa9e608: ldur            w0, [x1, #0x63]
    // 0xa9e60c: DecompressPointer r0
    //     0xa9e60c: add             x0, x0, HEAP, lsl #32
    // 0xa9e610: r16 = Sentinel
    //     0xa9e610: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e614: cmp             w0, w16
    // 0xa9e618: b.ne            #0xa9e628
    // 0xa9e61c: r2 = _colors
    //     0xa9e61c: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d208] Field <_LisTileDefaultsM3@560247952._colors@560247952>: late final (offset: 0x64)
    //     0xa9e620: ldr             x2, [x2, #0x208]
    // 0xa9e624: r0 = InitLateFinalInstanceField()
    //     0xa9e624: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e628: LoadField: r1 = r0->field_a3
    //     0xa9e628: ldur            w1, [x0, #0xa3]
    // 0xa9e62c: DecompressPointer r1
    //     0xa9e62c: add             x1, x1, HEAP, lsl #32
    // 0xa9e630: cmp             w1, NULL
    // 0xa9e634: b.ne            #0xa9e648
    // 0xa9e638: LoadField: r1 = r0->field_7f
    //     0xa9e638: ldur            w1, [x0, #0x7f]
    // 0xa9e63c: DecompressPointer r1
    //     0xa9e63c: add             x1, x1, HEAP, lsl #32
    // 0xa9e640: mov             x0, x1
    // 0xa9e644: b               #0xa9e64c
    // 0xa9e648: mov             x0, x1
    // 0xa9e64c: str             x0, [SP]
    // 0xa9e650: ldur            x1, [fp, #-0x88]
    // 0xa9e654: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xa9e654: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xa9e658: ldr             x4, [x4, #0x228]
    // 0xa9e65c: r0 = copyWith()
    //     0xa9e65c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa9e660: mov             x1, x0
    // 0xa9e664: b               #0xa9e694
    // 0xa9e668: ldur            x1, [fp, #-0x30]
    // 0xa9e66c: LoadField: r0 = r1->field_63
    //     0xa9e66c: ldur            w0, [x1, #0x63]
    // 0xa9e670: DecompressPointer r0
    //     0xa9e670: add             x0, x0, HEAP, lsl #32
    // 0xa9e674: r16 = Sentinel
    //     0xa9e674: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e678: cmp             w0, w16
    // 0xa9e67c: b.ne            #0xa9e68c
    // 0xa9e680: r2 = _textTheme
    //     0xa9e680: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d230] Field <_LisTileDefaultsM2@560247952._textTheme@560247952>: late final (offset: 0x64)
    //     0xa9e684: ldr             x2, [x2, #0x230]
    // 0xa9e688: r0 = InitLateFinalInstanceField()
    //     0xa9e688: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e68c: LoadField: r1 = r0->field_2f
    //     0xa9e68c: ldur            w1, [x0, #0x2f]
    // 0xa9e690: DecompressPointer r1
    //     0xa9e690: add             x1, x1, HEAP, lsl #32
    // 0xa9e694: cmp             w1, NULL
    // 0xa9e698: b.eq            #0xa9f474
    // 0xa9e69c: ldur            x16, [fp, #-0x48]
    // 0xa9e6a0: str             x16, [SP]
    // 0xa9e6a4: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xa9e6a4: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xa9e6a8: ldr             x4, [x4, #0x228]
    // 0xa9e6ac: r0 = copyWith()
    //     0xa9e6ac: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa9e6b0: mov             x1, x0
    // 0xa9e6b4: b               #0xa9e6bc
    // 0xa9e6b8: r1 = Null
    //     0xa9e6b8: mov             x1, NULL
    // 0xa9e6bc: ldur            x0, [fp, #-0x70]
    // 0xa9e6c0: stur            x1, [fp, #-0x88]
    // 0xa9e6c4: cmp             w0, NULL
    // 0xa9e6c8: b.eq            #0xa9e728
    // 0xa9e6cc: cmp             w1, NULL
    // 0xa9e6d0: b.eq            #0xa9f478
    // 0xa9e6d4: r0 = AnimatedDefaultTextStyle()
    //     0xa9e6d4: bl              #0x9dfbdc  ; AllocateAnimatedDefaultTextStyleStub -> AnimatedDefaultTextStyle (size=0x38)
    // 0xa9e6d8: mov             x1, x0
    // 0xa9e6dc: ldur            x0, [fp, #-0x70]
    // 0xa9e6e0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa9e6e0: stur            w0, [x1, #0x17]
    // 0xa9e6e4: ldur            x0, [fp, #-0x88]
    // 0xa9e6e8: StoreField: r1->field_1b = r0
    //     0xa9e6e8: stur            w0, [x1, #0x1b]
    // 0xa9e6ec: r2 = true
    //     0xa9e6ec: add             x2, NULL, #0x20  ; true
    // 0xa9e6f0: StoreField: r1->field_23 = r2
    //     0xa9e6f0: stur            w2, [x1, #0x23]
    // 0xa9e6f4: r3 = Instance_TextOverflow
    //     0xa9e6f4: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xa9e6f8: ldr             x3, [x3, #0xc60]
    // 0xa9e6fc: StoreField: r1->field_27 = r3
    //     0xa9e6fc: stur            w3, [x1, #0x27]
    // 0xa9e700: r4 = Instance_TextWidthBasis
    //     0xa9e700: add             x4, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0xa9e704: ldr             x4, [x4, #0x1d8]
    // 0xa9e708: StoreField: r1->field_2f = r4
    //     0xa9e708: stur            w4, [x1, #0x2f]
    // 0xa9e70c: r5 = Instance__Linear
    //     0xa9e70c: ldr             x5, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xa9e710: StoreField: r1->field_b = r5
    //     0xa9e710: stur            w5, [x1, #0xb]
    // 0xa9e714: r6 = Instance_Duration
    //     0xa9e714: add             x6, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xa9e718: ldr             x6, [x6, #0x368]
    // 0xa9e71c: StoreField: r1->field_f = r6
    //     0xa9e71c: stur            w6, [x1, #0xf]
    // 0xa9e720: mov             x8, x1
    // 0xa9e724: b               #0xa9e750
    // 0xa9e728: mov             x0, x1
    // 0xa9e72c: r2 = true
    //     0xa9e72c: add             x2, NULL, #0x20  ; true
    // 0xa9e730: r6 = Instance_Duration
    //     0xa9e730: add             x6, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xa9e734: ldr             x6, [x6, #0x368]
    // 0xa9e738: r5 = Instance__Linear
    //     0xa9e738: ldr             x5, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xa9e73c: r3 = Instance_TextOverflow
    //     0xa9e73c: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xa9e740: ldr             x3, [x3, #0xc60]
    // 0xa9e744: r4 = Instance_TextWidthBasis
    //     0xa9e744: add             x4, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0xa9e748: ldr             x4, [x4, #0x1d8]
    // 0xa9e74c: r8 = Null
    //     0xa9e74c: mov             x8, NULL
    // 0xa9e750: ldur            x7, [fp, #-8]
    // 0xa9e754: stur            x8, [fp, #-0x70]
    // 0xa9e758: LoadField: r1 = r7->field_37
    //     0xa9e758: ldur            w1, [x7, #0x37]
    // 0xa9e75c: DecompressPointer r1
    //     0xa9e75c: add             x1, x1, HEAP, lsl #32
    // 0xa9e760: cmp             w1, NULL
    // 0xa9e764: b.ne            #0xa9e76c
    // 0xa9e768: r1 = Null
    //     0xa9e768: mov             x1, NULL
    // 0xa9e76c: cmp             w1, NULL
    // 0xa9e770: b.ne            #0xa9e8ac
    // 0xa9e774: ldur            x9, [fp, #-0x30]
    // 0xa9e778: r1 = LoadClassIdInstr(r9)
    //     0xa9e778: ldur            x1, [x9, #-1]
    //     0xa9e77c: ubfx            x1, x1, #0xc, #0x14
    // 0xa9e780: cmp             x1, #0xf64
    // 0xa9e784: b.ne            #0xa9e798
    // 0xa9e788: LoadField: r1 = r9->field_1f
    //     0xa9e788: ldur            w1, [x9, #0x1f]
    // 0xa9e78c: DecompressPointer r1
    //     0xa9e78c: add             x1, x1, HEAP, lsl #32
    // 0xa9e790: mov             x0, x1
    // 0xa9e794: b               #0xa9e8a0
    // 0xa9e798: cmp             x1, #0xf65
    // 0xa9e79c: b.ne            #0xa9e81c
    // 0xa9e7a0: mov             x1, x9
    // 0xa9e7a4: LoadField: r0 = r1->field_67
    //     0xa9e7a4: ldur            w0, [x1, #0x67]
    // 0xa9e7a8: DecompressPointer r0
    //     0xa9e7a8: add             x0, x0, HEAP, lsl #32
    // 0xa9e7ac: r16 = Sentinel
    //     0xa9e7ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e7b0: cmp             w0, w16
    // 0xa9e7b4: b.ne            #0xa9e7c4
    // 0xa9e7b8: r2 = _textTheme
    //     0xa9e7b8: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d220] Field <_LisTileDefaultsM3@560247952._textTheme@560247952>: late final (offset: 0x68)
    //     0xa9e7bc: ldr             x2, [x2, #0x220]
    // 0xa9e7c0: r0 = InitLateFinalInstanceField()
    //     0xa9e7c0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e7c4: LoadField: r2 = r0->field_2b
    //     0xa9e7c4: ldur            w2, [x0, #0x2b]
    // 0xa9e7c8: DecompressPointer r2
    //     0xa9e7c8: add             x2, x2, HEAP, lsl #32
    // 0xa9e7cc: stur            x2, [fp, #-0x90]
    // 0xa9e7d0: cmp             w2, NULL
    // 0xa9e7d4: b.eq            #0xa9f47c
    // 0xa9e7d8: ldur            x1, [fp, #-0x30]
    // 0xa9e7dc: LoadField: r0 = r1->field_63
    //     0xa9e7dc: ldur            w0, [x1, #0x63]
    // 0xa9e7e0: DecompressPointer r0
    //     0xa9e7e0: add             x0, x0, HEAP, lsl #32
    // 0xa9e7e4: r16 = Sentinel
    //     0xa9e7e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e7e8: cmp             w0, w16
    // 0xa9e7ec: b.ne            #0xa9e7fc
    // 0xa9e7f0: r2 = _colors
    //     0xa9e7f0: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d208] Field <_LisTileDefaultsM3@560247952._colors@560247952>: late final (offset: 0x64)
    //     0xa9e7f4: ldr             x2, [x2, #0x208]
    // 0xa9e7f8: r0 = InitLateFinalInstanceField()
    //     0xa9e7f8: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e7fc: LoadField: r1 = r0->field_7f
    //     0xa9e7fc: ldur            w1, [x0, #0x7f]
    // 0xa9e800: DecompressPointer r1
    //     0xa9e800: add             x1, x1, HEAP, lsl #32
    // 0xa9e804: str             x1, [SP]
    // 0xa9e808: ldur            x1, [fp, #-0x90]
    // 0xa9e80c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xa9e80c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xa9e810: ldr             x4, [x4, #0x228]
    // 0xa9e814: r0 = copyWith()
    //     0xa9e814: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa9e818: b               #0xa9e8a0
    // 0xa9e81c: mov             x0, x9
    // 0xa9e820: LoadField: r1 = r0->field_f
    //     0xa9e820: ldur            w1, [x0, #0xf]
    // 0xa9e824: DecompressPointer r1
    //     0xa9e824: add             x1, x1, HEAP, lsl #32
    // 0xa9e828: cmp             w1, NULL
    // 0xa9e82c: b.eq            #0xa9f480
    // 0xa9e830: LoadField: r2 = r1->field_7
    //     0xa9e830: ldur            x2, [x1, #7]
    // 0xa9e834: cmp             x2, #0
    // 0xa9e838: b.gt            #0xa9e870
    // 0xa9e83c: mov             x1, x0
    // 0xa9e840: LoadField: r0 = r1->field_63
    //     0xa9e840: ldur            w0, [x1, #0x63]
    // 0xa9e844: DecompressPointer r0
    //     0xa9e844: add             x0, x0, HEAP, lsl #32
    // 0xa9e848: r16 = Sentinel
    //     0xa9e848: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e84c: cmp             w0, w16
    // 0xa9e850: b.ne            #0xa9e860
    // 0xa9e854: r2 = _textTheme
    //     0xa9e854: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d230] Field <_LisTileDefaultsM2@560247952._textTheme@560247952>: late final (offset: 0x64)
    //     0xa9e858: ldr             x2, [x2, #0x230]
    // 0xa9e85c: r0 = InitLateFinalInstanceField()
    //     0xa9e85c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e860: LoadField: r1 = r0->field_23
    //     0xa9e860: ldur            w1, [x0, #0x23]
    // 0xa9e864: DecompressPointer r1
    //     0xa9e864: add             x1, x1, HEAP, lsl #32
    // 0xa9e868: mov             x0, x1
    // 0xa9e86c: b               #0xa9e8a0
    // 0xa9e870: ldur            x1, [fp, #-0x30]
    // 0xa9e874: LoadField: r0 = r1->field_63
    //     0xa9e874: ldur            w0, [x1, #0x63]
    // 0xa9e878: DecompressPointer r0
    //     0xa9e878: add             x0, x0, HEAP, lsl #32
    // 0xa9e87c: r16 = Sentinel
    //     0xa9e87c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e880: cmp             w0, w16
    // 0xa9e884: b.ne            #0xa9e894
    // 0xa9e888: r2 = _textTheme
    //     0xa9e888: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d230] Field <_LisTileDefaultsM2@560247952._textTheme@560247952>: late final (offset: 0x64)
    //     0xa9e88c: ldr             x2, [x2, #0x230]
    // 0xa9e890: r0 = InitLateFinalInstanceField()
    //     0xa9e890: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e894: LoadField: r1 = r0->field_2b
    //     0xa9e894: ldur            w1, [x0, #0x2b]
    // 0xa9e898: DecompressPointer r1
    //     0xa9e898: add             x1, x1, HEAP, lsl #32
    // 0xa9e89c: mov             x0, x1
    // 0xa9e8a0: cmp             w0, NULL
    // 0xa9e8a4: b.eq            #0xa9f484
    // 0xa9e8a8: mov             x1, x0
    // 0xa9e8ac: ldur            x0, [fp, #-8]
    // 0xa9e8b0: LoadField: r2 = r0->field_1f
    //     0xa9e8b0: ldur            w2, [x0, #0x1f]
    // 0xa9e8b4: DecompressPointer r2
    //     0xa9e8b4: add             x2, x2, HEAP, lsl #32
    // 0xa9e8b8: stur            x2, [fp, #-0x90]
    // 0xa9e8bc: cmp             w2, NULL
    // 0xa9e8c0: b.ne            #0xa9e8cc
    // 0xa9e8c4: r3 = Null
    //     0xa9e8c4: mov             x3, NULL
    // 0xa9e8c8: b               #0xa9e8d0
    // 0xa9e8cc: mov             x3, x2
    // 0xa9e8d0: cmp             w3, NULL
    // 0xa9e8d4: b.ne            #0xa9e8dc
    // 0xa9e8d8: r3 = Null
    //     0xa9e8d8: mov             x3, NULL
    // 0xa9e8dc: cmp             w3, NULL
    // 0xa9e8e0: b.eq            #0xa9e8f4
    // 0xa9e8e4: tbnz            w3, #4, #0xa9e8f4
    // 0xa9e8e8: r3 = 13.000000
    //     0xa9e8e8: add             x3, PP, #0x33, lsl #12  ; [pp+0x33fc0] 13
    //     0xa9e8ec: ldr             x3, [x3, #0xfc0]
    // 0xa9e8f0: b               #0xa9e8f8
    // 0xa9e8f4: r3 = Null
    //     0xa9e8f4: mov             x3, NULL
    // 0xa9e8f8: ldur            x16, [fp, #-0x48]
    // 0xa9e8fc: stp             x3, x16, [SP]
    // 0xa9e900: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa9e900: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2bad8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa9e904: ldr             x4, [x4, #0xad8]
    // 0xa9e908: r0 = copyWith()
    //     0xa9e908: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa9e90c: ldur            x1, [fp, #-8]
    // 0xa9e910: stur            x0, [fp, #-0xa0]
    // 0xa9e914: LoadField: r2 = r1->field_f
    //     0xa9e914: ldur            w2, [x1, #0xf]
    // 0xa9e918: DecompressPointer r2
    //     0xa9e918: add             x2, x2, HEAP, lsl #32
    // 0xa9e91c: stur            x2, [fp, #-0x98]
    // 0xa9e920: r0 = AnimatedDefaultTextStyle()
    //     0xa9e920: bl              #0x9dfbdc  ; AllocateAnimatedDefaultTextStyleStub -> AnimatedDefaultTextStyle (size=0x38)
    // 0xa9e924: mov             x2, x0
    // 0xa9e928: ldur            x0, [fp, #-0x98]
    // 0xa9e92c: stur            x2, [fp, #-0xa8]
    // 0xa9e930: ArrayStore: r2[0] = r0  ; List_4
    //     0xa9e930: stur            w0, [x2, #0x17]
    // 0xa9e934: ldur            x0, [fp, #-0xa0]
    // 0xa9e938: StoreField: r2->field_1b = r0
    //     0xa9e938: stur            w0, [x2, #0x1b]
    // 0xa9e93c: r3 = true
    //     0xa9e93c: add             x3, NULL, #0x20  ; true
    // 0xa9e940: StoreField: r2->field_23 = r3
    //     0xa9e940: stur            w3, [x2, #0x23]
    // 0xa9e944: r4 = Instance_TextOverflow
    //     0xa9e944: add             x4, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xa9e948: ldr             x4, [x4, #0xc60]
    // 0xa9e94c: StoreField: r2->field_27 = r4
    //     0xa9e94c: stur            w4, [x2, #0x27]
    // 0xa9e950: r5 = Instance_TextWidthBasis
    //     0xa9e950: add             x5, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0xa9e954: ldr             x5, [x5, #0x1d8]
    // 0xa9e958: StoreField: r2->field_2f = r5
    //     0xa9e958: stur            w5, [x2, #0x2f]
    // 0xa9e95c: r6 = Instance__Linear
    //     0xa9e95c: ldr             x6, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xa9e960: StoreField: r2->field_b = r6
    //     0xa9e960: stur            w6, [x2, #0xb]
    // 0xa9e964: r7 = Instance_Duration
    //     0xa9e964: add             x7, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xa9e968: ldr             x7, [x7, #0x368]
    // 0xa9e96c: StoreField: r2->field_f = r7
    //     0xa9e96c: stur            w7, [x2, #0xf]
    // 0xa9e970: ldur            x8, [fp, #-8]
    // 0xa9e974: LoadField: r9 = r8->field_13
    //     0xa9e974: ldur            w9, [x8, #0x13]
    // 0xa9e978: DecompressPointer r9
    //     0xa9e978: add             x9, x9, HEAP, lsl #32
    // 0xa9e97c: stur            x9, [fp, #-0x98]
    // 0xa9e980: cmp             w9, NULL
    // 0xa9e984: b.eq            #0xa9eb78
    // 0xa9e988: LoadField: r1 = r8->field_3b
    //     0xa9e988: ldur            w1, [x8, #0x3b]
    // 0xa9e98c: DecompressPointer r1
    //     0xa9e98c: add             x1, x1, HEAP, lsl #32
    // 0xa9e990: cmp             w1, NULL
    // 0xa9e994: b.ne            #0xa9e99c
    // 0xa9e998: r1 = Null
    //     0xa9e998: mov             x1, NULL
    // 0xa9e99c: cmp             w1, NULL
    // 0xa9e9a0: b.ne            #0xa9eac8
    // 0xa9e9a4: ldur            x10, [fp, #-0x30]
    // 0xa9e9a8: r1 = LoadClassIdInstr(r10)
    //     0xa9e9a8: ldur            x1, [x10, #-1]
    //     0xa9e9ac: ubfx            x1, x1, #0xc, #0x14
    // 0xa9e9b0: cmp             x1, #0xf64
    // 0xa9e9b4: b.ne            #0xa9e9c8
    // 0xa9e9b8: LoadField: r1 = r10->field_23
    //     0xa9e9b8: ldur            w1, [x10, #0x23]
    // 0xa9e9bc: DecompressPointer r1
    //     0xa9e9bc: add             x1, x1, HEAP, lsl #32
    // 0xa9e9c0: mov             x0, x1
    // 0xa9e9c4: b               #0xa9eac4
    // 0xa9e9c8: cmp             x1, #0xf65
    // 0xa9e9cc: b.ne            #0xa9ea68
    // 0xa9e9d0: mov             x1, x10
    // 0xa9e9d4: LoadField: r0 = r1->field_67
    //     0xa9e9d4: ldur            w0, [x1, #0x67]
    // 0xa9e9d8: DecompressPointer r0
    //     0xa9e9d8: add             x0, x0, HEAP, lsl #32
    // 0xa9e9dc: r16 = Sentinel
    //     0xa9e9dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9e9e0: cmp             w0, w16
    // 0xa9e9e4: b.ne            #0xa9e9f4
    // 0xa9e9e8: r2 = _textTheme
    //     0xa9e9e8: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d220] Field <_LisTileDefaultsM3@560247952._textTheme@560247952>: late final (offset: 0x68)
    //     0xa9e9ec: ldr             x2, [x2, #0x220]
    // 0xa9e9f0: r0 = InitLateFinalInstanceField()
    //     0xa9e9f0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9e9f4: LoadField: r2 = r0->field_2f
    //     0xa9e9f4: ldur            w2, [x0, #0x2f]
    // 0xa9e9f8: DecompressPointer r2
    //     0xa9e9f8: add             x2, x2, HEAP, lsl #32
    // 0xa9e9fc: stur            x2, [fp, #-0xb0]
    // 0xa9ea00: cmp             w2, NULL
    // 0xa9ea04: b.eq            #0xa9f488
    // 0xa9ea08: ldur            x1, [fp, #-0x30]
    // 0xa9ea0c: LoadField: r0 = r1->field_63
    //     0xa9ea0c: ldur            w0, [x1, #0x63]
    // 0xa9ea10: DecompressPointer r0
    //     0xa9ea10: add             x0, x0, HEAP, lsl #32
    // 0xa9ea14: r16 = Sentinel
    //     0xa9ea14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9ea18: cmp             w0, w16
    // 0xa9ea1c: b.ne            #0xa9ea2c
    // 0xa9ea20: r2 = _colors
    //     0xa9ea20: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d208] Field <_LisTileDefaultsM3@560247952._colors@560247952>: late final (offset: 0x64)
    //     0xa9ea24: ldr             x2, [x2, #0x208]
    // 0xa9ea28: r0 = InitLateFinalInstanceField()
    //     0xa9ea28: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9ea2c: LoadField: r1 = r0->field_a3
    //     0xa9ea2c: ldur            w1, [x0, #0xa3]
    // 0xa9ea30: DecompressPointer r1
    //     0xa9ea30: add             x1, x1, HEAP, lsl #32
    // 0xa9ea34: cmp             w1, NULL
    // 0xa9ea38: b.ne            #0xa9ea4c
    // 0xa9ea3c: LoadField: r1 = r0->field_7f
    //     0xa9ea3c: ldur            w1, [x0, #0x7f]
    // 0xa9ea40: DecompressPointer r1
    //     0xa9ea40: add             x1, x1, HEAP, lsl #32
    // 0xa9ea44: mov             x0, x1
    // 0xa9ea48: b               #0xa9ea50
    // 0xa9ea4c: mov             x0, x1
    // 0xa9ea50: str             x0, [SP]
    // 0xa9ea54: ldur            x1, [fp, #-0xb0]
    // 0xa9ea58: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xa9ea58: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xa9ea5c: ldr             x4, [x4, #0x228]
    // 0xa9ea60: r0 = copyWith()
    //     0xa9ea60: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa9ea64: b               #0xa9eac4
    // 0xa9ea68: ldur            x1, [fp, #-0x30]
    // 0xa9ea6c: LoadField: r0 = r1->field_63
    //     0xa9ea6c: ldur            w0, [x1, #0x63]
    // 0xa9ea70: DecompressPointer r0
    //     0xa9ea70: add             x0, x0, HEAP, lsl #32
    // 0xa9ea74: r16 = Sentinel
    //     0xa9ea74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9ea78: cmp             w0, w16
    // 0xa9ea7c: b.ne            #0xa9ea8c
    // 0xa9ea80: r2 = _textTheme
    //     0xa9ea80: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d230] Field <_LisTileDefaultsM2@560247952._textTheme@560247952>: late final (offset: 0x64)
    //     0xa9ea84: ldr             x2, [x2, #0x230]
    // 0xa9ea88: r0 = InitLateFinalInstanceField()
    //     0xa9ea88: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9ea8c: LoadField: r1 = r0->field_2f
    //     0xa9ea8c: ldur            w1, [x0, #0x2f]
    // 0xa9ea90: DecompressPointer r1
    //     0xa9ea90: add             x1, x1, HEAP, lsl #32
    // 0xa9ea94: cmp             w1, NULL
    // 0xa9ea98: b.eq            #0xa9f48c
    // 0xa9ea9c: LoadField: r2 = r0->field_33
    //     0xa9ea9c: ldur            w2, [x0, #0x33]
    // 0xa9eaa0: DecompressPointer r2
    //     0xa9eaa0: add             x2, x2, HEAP, lsl #32
    // 0xa9eaa4: cmp             w2, NULL
    // 0xa9eaa8: b.eq            #0xa9f490
    // 0xa9eaac: LoadField: r0 = r2->field_b
    //     0xa9eaac: ldur            w0, [x2, #0xb]
    // 0xa9eab0: DecompressPointer r0
    //     0xa9eab0: add             x0, x0, HEAP, lsl #32
    // 0xa9eab4: str             x0, [SP]
    // 0xa9eab8: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xa9eab8: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xa9eabc: ldr             x4, [x4, #0x228]
    // 0xa9eac0: r0 = copyWith()
    //     0xa9eac0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa9eac4: mov             x1, x0
    // 0xa9eac8: ldur            x0, [fp, #-0x90]
    // 0xa9eacc: cmp             w0, NULL
    // 0xa9ead0: b.ne            #0xa9eadc
    // 0xa9ead4: r2 = Null
    //     0xa9ead4: mov             x2, NULL
    // 0xa9ead8: b               #0xa9eae0
    // 0xa9eadc: mov             x2, x0
    // 0xa9eae0: cmp             w2, NULL
    // 0xa9eae4: b.ne            #0xa9eaec
    // 0xa9eae8: r2 = Null
    //     0xa9eae8: mov             x2, NULL
    // 0xa9eaec: cmp             w2, NULL
    // 0xa9eaf0: b.eq            #0xa9eb04
    // 0xa9eaf4: tbnz            w2, #4, #0xa9eb04
    // 0xa9eaf8: r3 = 12.000000
    //     0xa9eaf8: add             x3, PP, #0x23, lsl #12  ; [pp+0x23c60] 12
    //     0xa9eafc: ldr             x3, [x3, #0xc60]
    // 0xa9eb00: b               #0xa9eb08
    // 0xa9eb04: r3 = Null
    //     0xa9eb04: mov             x3, NULL
    // 0xa9eb08: ldur            x2, [fp, #-0x98]
    // 0xa9eb0c: ldur            x16, [fp, #-0x48]
    // 0xa9eb10: stp             x3, x16, [SP]
    // 0xa9eb14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa9eb14: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2bad8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa9eb18: ldr             x4, [x4, #0xad8]
    // 0xa9eb1c: r0 = copyWith()
    //     0xa9eb1c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa9eb20: stur            x0, [fp, #-0x48]
    // 0xa9eb24: r0 = AnimatedDefaultTextStyle()
    //     0xa9eb24: bl              #0x9dfbdc  ; AllocateAnimatedDefaultTextStyleStub -> AnimatedDefaultTextStyle (size=0x38)
    // 0xa9eb28: mov             x1, x0
    // 0xa9eb2c: ldur            x0, [fp, #-0x98]
    // 0xa9eb30: ArrayStore: r1[0] = r0  ; List_4
    //     0xa9eb30: stur            w0, [x1, #0x17]
    // 0xa9eb34: ldur            x0, [fp, #-0x48]
    // 0xa9eb38: StoreField: r1->field_1b = r0
    //     0xa9eb38: stur            w0, [x1, #0x1b]
    // 0xa9eb3c: r2 = true
    //     0xa9eb3c: add             x2, NULL, #0x20  ; true
    // 0xa9eb40: StoreField: r1->field_23 = r2
    //     0xa9eb40: stur            w2, [x1, #0x23]
    // 0xa9eb44: r3 = Instance_TextOverflow
    //     0xa9eb44: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xa9eb48: ldr             x3, [x3, #0xc60]
    // 0xa9eb4c: StoreField: r1->field_27 = r3
    //     0xa9eb4c: stur            w3, [x1, #0x27]
    // 0xa9eb50: r4 = Instance_TextWidthBasis
    //     0xa9eb50: add             x4, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0xa9eb54: ldr             x4, [x4, #0x1d8]
    // 0xa9eb58: StoreField: r1->field_2f = r4
    //     0xa9eb58: stur            w4, [x1, #0x2f]
    // 0xa9eb5c: r5 = Instance__Linear
    //     0xa9eb5c: ldr             x5, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xa9eb60: StoreField: r1->field_b = r5
    //     0xa9eb60: stur            w5, [x1, #0xb]
    // 0xa9eb64: r6 = Instance_Duration
    //     0xa9eb64: add             x6, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xa9eb68: ldr             x6, [x6, #0x368]
    // 0xa9eb6c: StoreField: r1->field_f = r6
    //     0xa9eb6c: stur            w6, [x1, #0xf]
    // 0xa9eb70: mov             x7, x1
    // 0xa9eb74: b               #0xa9eb94
    // 0xa9eb78: mov             x2, x3
    // 0xa9eb7c: mov             x3, x4
    // 0xa9eb80: mov             x4, x5
    // 0xa9eb84: mov             x5, x6
    // 0xa9eb88: mov             x6, x7
    // 0xa9eb8c: r7 = Null
    //     0xa9eb8c: mov             x7, NULL
    // 0xa9eb90: r0 = Null
    //     0xa9eb90: mov             x0, NULL
    // 0xa9eb94: ldur            x1, [fp, #-8]
    // 0xa9eb98: stur            x7, [fp, #-0x98]
    // 0xa9eb9c: stur            x0, [fp, #-0xb0]
    // 0xa9eba0: ArrayLoad: r8 = r1[0]  ; List_4
    //     0xa9eba0: ldur            w8, [x1, #0x17]
    // 0xa9eba4: DecompressPointer r8
    //     0xa9eba4: add             x8, x8, HEAP, lsl #32
    // 0xa9eba8: stur            x8, [fp, #-0x48]
    // 0xa9ebac: cmp             w8, NULL
    // 0xa9ebb0: b.eq            #0xa9ec14
    // 0xa9ebb4: ldur            x9, [fp, #-0x88]
    // 0xa9ebb8: cmp             w9, NULL
    // 0xa9ebbc: b.eq            #0xa9f494
    // 0xa9ebc0: r0 = AnimatedDefaultTextStyle()
    //     0xa9ebc0: bl              #0x9dfbdc  ; AllocateAnimatedDefaultTextStyleStub -> AnimatedDefaultTextStyle (size=0x38)
    // 0xa9ebc4: mov             x1, x0
    // 0xa9ebc8: ldur            x0, [fp, #-0x48]
    // 0xa9ebcc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa9ebcc: stur            w0, [x1, #0x17]
    // 0xa9ebd0: ldur            x0, [fp, #-0x88]
    // 0xa9ebd4: StoreField: r1->field_1b = r0
    //     0xa9ebd4: stur            w0, [x1, #0x1b]
    // 0xa9ebd8: r0 = true
    //     0xa9ebd8: add             x0, NULL, #0x20  ; true
    // 0xa9ebdc: StoreField: r1->field_23 = r0
    //     0xa9ebdc: stur            w0, [x1, #0x23]
    // 0xa9ebe0: r2 = Instance_TextOverflow
    //     0xa9ebe0: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xa9ebe4: ldr             x2, [x2, #0xc60]
    // 0xa9ebe8: StoreField: r1->field_27 = r2
    //     0xa9ebe8: stur            w2, [x1, #0x27]
    // 0xa9ebec: r2 = Instance_TextWidthBasis
    //     0xa9ebec: add             x2, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0xa9ebf0: ldr             x2, [x2, #0x1d8]
    // 0xa9ebf4: StoreField: r1->field_2f = r2
    //     0xa9ebf4: stur            w2, [x1, #0x2f]
    // 0xa9ebf8: r2 = Instance__Linear
    //     0xa9ebf8: ldr             x2, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xa9ebfc: StoreField: r1->field_b = r2
    //     0xa9ebfc: stur            w2, [x1, #0xb]
    // 0xa9ec00: r2 = Instance_Duration
    //     0xa9ec00: add             x2, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xa9ec04: ldr             x2, [x2, #0x368]
    // 0xa9ec08: StoreField: r1->field_f = r2
    //     0xa9ec08: stur            w2, [x1, #0xf]
    // 0xa9ec0c: mov             x3, x1
    // 0xa9ec10: b               #0xa9ec1c
    // 0xa9ec14: mov             x0, x2
    // 0xa9ec18: r3 = Null
    //     0xa9ec18: mov             x3, NULL
    // 0xa9ec1c: ldur            x2, [fp, #-8]
    // 0xa9ec20: ldur            x1, [fp, #-0x10]
    // 0xa9ec24: stur            x3, [fp, #-0x48]
    // 0xa9ec28: r0 = of()
    //     0xa9ec28: bl              #0x6b0ae8  ; [package:flutter/src/widgets/basic.dart] Directionality::of
    // 0xa9ec2c: mov             x4, x0
    // 0xa9ec30: ldur            x3, [fp, #-8]
    // 0xa9ec34: stur            x4, [fp, #-0x10]
    // 0xa9ec38: LoadField: r0 = r3->field_47
    //     0xa9ec38: ldur            w0, [x3, #0x47]
    // 0xa9ec3c: DecompressPointer r0
    //     0xa9ec3c: add             x0, x0, HEAP, lsl #32
    // 0xa9ec40: cmp             w0, NULL
    // 0xa9ec44: b.ne            #0xa9ec4c
    // 0xa9ec48: r0 = Null
    //     0xa9ec48: mov             x0, NULL
    // 0xa9ec4c: cmp             w0, NULL
    // 0xa9ec50: b.ne            #0xa9eca4
    // 0xa9ec54: ldur            x5, [fp, #-0x28]
    // 0xa9ec58: LoadField: r0 = r5->field_2b
    //     0xa9ec58: ldur            w0, [x5, #0x2b]
    // 0xa9ec5c: DecompressPointer r0
    //     0xa9ec5c: add             x0, x0, HEAP, lsl #32
    // 0xa9ec60: cmp             w0, NULL
    // 0xa9ec64: b.ne            #0xa9ec70
    // 0xa9ec68: r0 = Null
    //     0xa9ec68: mov             x0, NULL
    // 0xa9ec6c: b               #0xa9eca4
    // 0xa9ec70: r1 = LoadClassIdInstr(r0)
    //     0xa9ec70: ldur            x1, [x0, #-1]
    //     0xa9ec74: ubfx            x1, x1, #0xc, #0x14
    // 0xa9ec78: cmp             x1, #0xcb7
    // 0xa9ec7c: b.eq            #0xa9eca4
    // 0xa9ec80: r1 = LoadClassIdInstr(r0)
    //     0xa9ec80: ldur            x1, [x0, #-1]
    //     0xa9ec84: ubfx            x1, x1, #0xc, #0x14
    // 0xa9ec88: mov             x16, x0
    // 0xa9ec8c: mov             x0, x1
    // 0xa9ec90: mov             x1, x16
    // 0xa9ec94: mov             x2, x4
    // 0xa9ec98: r0 = GDT[cid_x0 + -0xfdb]()
    //     0xa9ec98: sub             lr, x0, #0xfdb
    //     0xa9ec9c: ldr             lr, [x21, lr, lsl #3]
    //     0xa9eca0: blr             lr
    // 0xa9eca4: cmp             w0, NULL
    // 0xa9eca8: b.ne            #0xa9ecec
    // 0xa9ecac: ldur            x0, [fp, #-0x38]
    // 0xa9ecb0: r1 = LoadClassIdInstr(r0)
    //     0xa9ecb0: ldur            x1, [x0, #-1]
    //     0xa9ecb4: ubfx            x1, x1, #0xc, #0x14
    // 0xa9ecb8: cmp             x1, #0xcb7
    // 0xa9ecbc: b.eq            #0xa9ece4
    // 0xa9ecc0: r1 = LoadClassIdInstr(r0)
    //     0xa9ecc0: ldur            x1, [x0, #-1]
    //     0xa9ecc4: ubfx            x1, x1, #0xc, #0x14
    // 0xa9ecc8: mov             x16, x0
    // 0xa9eccc: mov             x0, x1
    // 0xa9ecd0: mov             x1, x16
    // 0xa9ecd4: ldur            x2, [fp, #-0x10]
    // 0xa9ecd8: r0 = GDT[cid_x0 + -0xfdb]()
    //     0xa9ecd8: sub             lr, x0, #0xfdb
    //     0xa9ecdc: ldr             lr, [x21, lr, lsl #3]
    //     0xa9ece0: blr             lr
    // 0xa9ece4: mov             x4, x0
    // 0xa9ece8: b               #0xa9ecf0
    // 0xa9ecec: mov             x4, x0
    // 0xa9ecf0: ldur            x2, [fp, #-0x50]
    // 0xa9ecf4: ldur            x3, [fp, #-0x40]
    // 0xa9ecf8: ldur            x0, [fp, #-0x58]
    // 0xa9ecfc: stur            x4, [fp, #-0x38]
    // 0xa9ed00: r1 = <WidgetState>
    //     0xa9ed00: add             x1, PP, #0x39, lsl #12  ; [pp+0x39868] TypeArguments: <WidgetState>
    //     0xa9ed04: ldr             x1, [x1, #0x868]
    // 0xa9ed08: r0 = _Set()
    //     0xa9ed08: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xa9ed0c: mov             x3, x0
    // 0xa9ed10: ldur            x0, [fp, #-0x40]
    // 0xa9ed14: stur            x3, [fp, #-0x88]
    // 0xa9ed18: StoreField: r3->field_1b = r0
    //     0xa9ed18: stur            w0, [x3, #0x1b]
    // 0xa9ed1c: StoreField: r3->field_b = rZR
    //     0xa9ed1c: stur            wzr, [x3, #0xb]
    // 0xa9ed20: ldur            x0, [fp, #-0x58]
    // 0xa9ed24: StoreField: r3->field_f = r0
    //     0xa9ed24: stur            w0, [x3, #0xf]
    // 0xa9ed28: StoreField: r3->field_13 = rZR
    //     0xa9ed28: stur            wzr, [x3, #0x13]
    // 0xa9ed2c: ArrayStore: r3[0] = rZR  ; List_4
    //     0xa9ed2c: stur            wzr, [x3, #0x17]
    // 0xa9ed30: ldur            x0, [fp, #-0x50]
    // 0xa9ed34: tbz             w0, #4, #0xa9ed40
    // 0xa9ed38: ldur            x4, [fp, #-8]
    // 0xa9ed3c: b               #0xa9ed54
    // 0xa9ed40: ldur            x4, [fp, #-8]
    // 0xa9ed44: LoadField: r1 = r4->field_4f
    //     0xa9ed44: ldur            w1, [x4, #0x4f]
    // 0xa9ed48: DecompressPointer r1
    //     0xa9ed48: add             x1, x1, HEAP, lsl #32
    // 0xa9ed4c: cmp             w1, NULL
    // 0xa9ed50: b.ne            #0xa9ed64
    // 0xa9ed54: mov             x1, x3
    // 0xa9ed58: r2 = Instance_WidgetState
    //     0xa9ed58: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d190] Obj!WidgetState@e33841
    //     0xa9ed5c: ldr             x2, [x2, #0x190]
    // 0xa9ed60: r0 = add()
    //     0xa9ed60: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xa9ed64: ldur            x0, [fp, #-0x20]
    // 0xa9ed68: r16 = <MouseCursor?>
    //     0xa9ed68: add             x16, PP, #0x23, lsl #12  ; [pp+0x23df8] TypeArguments: <MouseCursor?>
    //     0xa9ed6c: ldr             x16, [x16, #0xdf8]
    // 0xa9ed70: stp             NULL, x16, [SP, #8]
    // 0xa9ed74: ldur            x16, [fp, #-0x88]
    // 0xa9ed78: str             x16, [SP]
    // 0xa9ed7c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa9ed7c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa9ed80: r0 = resolveAs()
    //     0xa9ed80: bl              #0x9d8704  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::resolveAs
    // 0xa9ed84: ldur            x2, [fp, #-0x88]
    // 0xa9ed88: r1 = Instance__EnabledAndDisabledMouseCursor
    //     0xa9ed88: add             x1, PP, #0x39, lsl #12  ; [pp+0x39870] Obj!_EnabledAndDisabledMouseCursor@e1cf41
    //     0xa9ed8c: ldr             x1, [x1, #0x870]
    // 0xa9ed90: r0 = resolve()
    //     0xa9ed90: bl              #0xd824b0  ; [package:flutter/src/widgets/widget_state.dart] _EnabledAndDisabledMouseCursor::resolve
    // 0xa9ed94: mov             x4, x0
    // 0xa9ed98: ldur            x0, [fp, #-0x20]
    // 0xa9ed9c: stur            x4, [fp, #-0xc0]
    // 0xa9eda0: tbnz            w0, #4, #0xa9edb0
    // 0xa9eda4: r6 = Instance_ListTileTitleAlignment
    //     0xa9eda4: add             x6, PP, #0x39, lsl #12  ; [pp+0x39878] Obj!ListTileTitleAlignment@e366a1
    //     0xa9eda8: ldr             x6, [x6, #0x878]
    // 0xa9edac: b               #0xa9edb8
    // 0xa9edb0: r6 = Instance_ListTileTitleAlignment
    //     0xa9edb0: add             x6, PP, #0x39, lsl #12  ; [pp+0x39880] Obj!ListTileTitleAlignment@e36681
    //     0xa9edb4: ldr             x6, [x6, #0x880]
    // 0xa9edb8: ldur            x0, [fp, #-8]
    // 0xa9edbc: stur            x6, [fp, #-0xb8]
    // 0xa9edc0: LoadField: r1 = r0->field_27
    //     0xa9edc0: ldur            w1, [x0, #0x27]
    // 0xa9edc4: DecompressPointer r1
    //     0xa9edc4: add             x1, x1, HEAP, lsl #32
    // 0xa9edc8: cmp             w1, NULL
    // 0xa9edcc: b.ne            #0xa9ede4
    // 0xa9edd0: ldur            x7, [fp, #-0x28]
    // 0xa9edd4: LoadField: r2 = r7->field_b
    //     0xa9edd4: ldur            w2, [x7, #0xb]
    // 0xa9edd8: DecompressPointer r2
    //     0xa9edd8: add             x2, x2, HEAP, lsl #32
    // 0xa9eddc: mov             x9, x2
    // 0xa9ede0: b               #0xa9edec
    // 0xa9ede4: ldur            x7, [fp, #-0x28]
    // 0xa9ede8: mov             x9, x1
    // 0xa9edec: ldur            x8, [fp, #-0x50]
    // 0xa9edf0: stur            x9, [fp, #-0x88]
    // 0xa9edf4: tbnz            w8, #4, #0xa9ee08
    // 0xa9edf8: LoadField: r2 = r0->field_4f
    //     0xa9edf8: ldur            w2, [x0, #0x4f]
    // 0xa9edfc: DecompressPointer r2
    //     0xa9edfc: add             x2, x2, HEAP, lsl #32
    // 0xa9ee00: mov             x10, x2
    // 0xa9ee04: b               #0xa9ee0c
    // 0xa9ee08: r10 = Null
    //     0xa9ee08: mov             x10, NULL
    // 0xa9ee0c: stur            x10, [fp, #-0x58]
    // 0xa9ee10: LoadField: r2 = r0->field_7f
    //     0xa9ee10: ldur            w2, [x0, #0x7f]
    // 0xa9ee14: DecompressPointer r2
    //     0xa9ee14: add             x2, x2, HEAP, lsl #32
    // 0xa9ee18: cmp             w2, NULL
    // 0xa9ee1c: b.eq            #0xa9ee20
    // 0xa9ee20: LoadField: r2 = r0->field_97
    //     0xa9ee20: ldur            w2, [x0, #0x97]
    // 0xa9ee24: DecompressPointer r2
    //     0xa9ee24: add             x2, x2, HEAP, lsl #32
    // 0xa9ee28: tbnz            w2, #4, #0xa9ee4c
    // 0xa9ee2c: LoadField: r2 = r0->field_4f
    //     0xa9ee2c: ldur            w2, [x0, #0x4f]
    // 0xa9ee30: DecompressPointer r2
    //     0xa9ee30: add             x2, x2, HEAP, lsl #32
    // 0xa9ee34: cmp             w2, NULL
    // 0xa9ee38: b.eq            #0xa9ee44
    // 0xa9ee3c: r11 = true
    //     0xa9ee3c: add             x11, NULL, #0x20  ; true
    // 0xa9ee40: b               #0xa9ee50
    // 0xa9ee44: r11 = false
    //     0xa9ee44: add             x11, NULL, #0x30  ; false
    // 0xa9ee48: b               #0xa9ee50
    // 0xa9ee4c: r11 = false
    //     0xa9ee4c: add             x11, NULL, #0x30  ; false
    // 0xa9ee50: stur            x11, [fp, #-0x40]
    // 0xa9ee54: cmp             w1, NULL
    // 0xa9ee58: b.ne            #0xa9ee64
    // 0xa9ee5c: LoadField: r1 = r7->field_b
    //     0xa9ee5c: ldur            w1, [x7, #0xb]
    // 0xa9ee60: DecompressPointer r1
    //     0xa9ee60: add             x1, x1, HEAP, lsl #32
    // 0xa9ee64: cmp             w1, NULL
    // 0xa9ee68: b.ne            #0xa9ee78
    // 0xa9ee6c: r13 = Instance_Border
    //     0xa9ee6c: add             x13, PP, #0x39, lsl #12  ; [pp+0x39858] Obj!Border@e145d1
    //     0xa9ee70: ldr             x13, [x13, #0x858]
    // 0xa9ee74: b               #0xa9ee7c
    // 0xa9ee78: mov             x13, x1
    // 0xa9ee7c: ldur            x12, [fp, #-0x90]
    // 0xa9ee80: mov             x1, x0
    // 0xa9ee84: ldur            x2, [fp, #-0x18]
    // 0xa9ee88: mov             x3, x7
    // 0xa9ee8c: ldur            x5, [fp, #-0x30]
    // 0xa9ee90: stur            x13, [fp, #-0x20]
    // 0xa9ee94: r0 = _tileBackgroundColor()
    //     0xa9ee94: bl              #0xa9f4d0  ; [package:flutter/src/material/list_tile.dart] ListTile::_tileBackgroundColor
    // 0xa9ee98: stur            x0, [fp, #-0xc8]
    // 0xa9ee9c: r0 = ShapeDecoration()
    //     0xa9ee9c: bl              #0x7f3e70  ; AllocateShapeDecorationStub -> ShapeDecoration (size=0x1c)
    // 0xa9eea0: mov             x2, x0
    // 0xa9eea4: ldur            x0, [fp, #-0xc8]
    // 0xa9eea8: stur            x2, [fp, #-0xd0]
    // 0xa9eeac: StoreField: r2->field_7 = r0
    //     0xa9eeac: stur            w0, [x2, #7]
    // 0xa9eeb0: ldur            x0, [fp, #-0x20]
    // 0xa9eeb4: ArrayStore: r2[0] = r0  ; List_4
    //     0xa9eeb4: stur            w0, [x2, #0x17]
    // 0xa9eeb8: ldur            x0, [fp, #-0x90]
    // 0xa9eebc: cmp             w0, NULL
    // 0xa9eec0: b.ne            #0xa9eec8
    // 0xa9eec4: r0 = Null
    //     0xa9eec4: mov             x0, NULL
    // 0xa9eec8: cmp             w0, NULL
    // 0xa9eecc: b.ne            #0xa9eed4
    // 0xa9eed0: r0 = Null
    //     0xa9eed0: mov             x0, NULL
    // 0xa9eed4: cmp             w0, NULL
    // 0xa9eed8: b.ne            #0xa9eee4
    // 0xa9eedc: r3 = false
    //     0xa9eedc: add             x3, NULL, #0x30  ; false
    // 0xa9eee0: b               #0xa9eee8
    // 0xa9eee4: mov             x3, x0
    // 0xa9eee8: ldur            x0, [fp, #-8]
    // 0xa9eeec: stur            x3, [fp, #-0x20]
    // 0xa9eef0: LoadField: r1 = r0->field_23
    //     0xa9eef0: ldur            w1, [x0, #0x23]
    // 0xa9eef4: DecompressPointer r1
    //     0xa9eef4: add             x1, x1, HEAP, lsl #32
    // 0xa9eef8: cmp             w1, NULL
    // 0xa9eefc: b.ne            #0xa9ef04
    // 0xa9ef00: r1 = Null
    //     0xa9ef00: mov             x1, NULL
    // 0xa9ef04: cmp             w1, NULL
    // 0xa9ef08: b.ne            #0xa9ef1c
    // 0xa9ef0c: ldur            x1, [fp, #-0x18]
    // 0xa9ef10: LoadField: r4 = r1->field_33
    //     0xa9ef10: ldur            w4, [x1, #0x33]
    // 0xa9ef14: DecompressPointer r4
    //     0xa9ef14: add             x4, x4, HEAP, lsl #32
    // 0xa9ef18: b               #0xa9ef20
    // 0xa9ef1c: mov             x4, x1
    // 0xa9ef20: ldur            x1, [fp, #-0xa0]
    // 0xa9ef24: stur            x4, [fp, #-0x18]
    // 0xa9ef28: LoadField: r5 = r1->field_33
    //     0xa9ef28: ldur            w5, [x1, #0x33]
    // 0xa9ef2c: DecompressPointer r5
    //     0xa9ef2c: add             x5, x5, HEAP, lsl #32
    // 0xa9ef30: cmp             w5, NULL
    // 0xa9ef34: b.ne            #0xa9f084
    // 0xa9ef38: ldur            x5, [fp, #-0x30]
    // 0xa9ef3c: r1 = LoadClassIdInstr(r5)
    //     0xa9ef3c: ldur            x1, [x5, #-1]
    //     0xa9ef40: ubfx            x1, x1, #0xc, #0x14
    // 0xa9ef44: cmp             x1, #0xf64
    // 0xa9ef48: b.ne            #0xa9ef5c
    // 0xa9ef4c: LoadField: r1 = r5->field_1f
    //     0xa9ef4c: ldur            w1, [x5, #0x1f]
    // 0xa9ef50: DecompressPointer r1
    //     0xa9ef50: add             x1, x1, HEAP, lsl #32
    // 0xa9ef54: mov             x0, x1
    // 0xa9ef58: b               #0xa9f064
    // 0xa9ef5c: cmp             x1, #0xf65
    // 0xa9ef60: b.ne            #0xa9efe0
    // 0xa9ef64: mov             x1, x5
    // 0xa9ef68: LoadField: r0 = r1->field_67
    //     0xa9ef68: ldur            w0, [x1, #0x67]
    // 0xa9ef6c: DecompressPointer r0
    //     0xa9ef6c: add             x0, x0, HEAP, lsl #32
    // 0xa9ef70: r16 = Sentinel
    //     0xa9ef70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9ef74: cmp             w0, w16
    // 0xa9ef78: b.ne            #0xa9ef88
    // 0xa9ef7c: r2 = _textTheme
    //     0xa9ef7c: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d220] Field <_LisTileDefaultsM3@560247952._textTheme@560247952>: late final (offset: 0x68)
    //     0xa9ef80: ldr             x2, [x2, #0x220]
    // 0xa9ef84: r0 = InitLateFinalInstanceField()
    //     0xa9ef84: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9ef88: LoadField: r2 = r0->field_2b
    //     0xa9ef88: ldur            w2, [x0, #0x2b]
    // 0xa9ef8c: DecompressPointer r2
    //     0xa9ef8c: add             x2, x2, HEAP, lsl #32
    // 0xa9ef90: stur            x2, [fp, #-0x90]
    // 0xa9ef94: cmp             w2, NULL
    // 0xa9ef98: b.eq            #0xa9f498
    // 0xa9ef9c: ldur            x1, [fp, #-0x30]
    // 0xa9efa0: LoadField: r0 = r1->field_63
    //     0xa9efa0: ldur            w0, [x1, #0x63]
    // 0xa9efa4: DecompressPointer r0
    //     0xa9efa4: add             x0, x0, HEAP, lsl #32
    // 0xa9efa8: r16 = Sentinel
    //     0xa9efa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9efac: cmp             w0, w16
    // 0xa9efb0: b.ne            #0xa9efc0
    // 0xa9efb4: r2 = _colors
    //     0xa9efb4: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d208] Field <_LisTileDefaultsM3@560247952._colors@560247952>: late final (offset: 0x64)
    //     0xa9efb8: ldr             x2, [x2, #0x208]
    // 0xa9efbc: r0 = InitLateFinalInstanceField()
    //     0xa9efbc: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9efc0: LoadField: r1 = r0->field_7f
    //     0xa9efc0: ldur            w1, [x0, #0x7f]
    // 0xa9efc4: DecompressPointer r1
    //     0xa9efc4: add             x1, x1, HEAP, lsl #32
    // 0xa9efc8: str             x1, [SP]
    // 0xa9efcc: ldur            x1, [fp, #-0x90]
    // 0xa9efd0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xa9efd0: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xa9efd4: ldr             x4, [x4, #0x228]
    // 0xa9efd8: r0 = copyWith()
    //     0xa9efd8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa9efdc: b               #0xa9f064
    // 0xa9efe0: mov             x0, x5
    // 0xa9efe4: LoadField: r1 = r0->field_f
    //     0xa9efe4: ldur            w1, [x0, #0xf]
    // 0xa9efe8: DecompressPointer r1
    //     0xa9efe8: add             x1, x1, HEAP, lsl #32
    // 0xa9efec: cmp             w1, NULL
    // 0xa9eff0: b.eq            #0xa9f49c
    // 0xa9eff4: LoadField: r2 = r1->field_7
    //     0xa9eff4: ldur            x2, [x1, #7]
    // 0xa9eff8: cmp             x2, #0
    // 0xa9effc: b.gt            #0xa9f034
    // 0xa9f000: mov             x1, x0
    // 0xa9f004: LoadField: r0 = r1->field_63
    //     0xa9f004: ldur            w0, [x1, #0x63]
    // 0xa9f008: DecompressPointer r0
    //     0xa9f008: add             x0, x0, HEAP, lsl #32
    // 0xa9f00c: r16 = Sentinel
    //     0xa9f00c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9f010: cmp             w0, w16
    // 0xa9f014: b.ne            #0xa9f024
    // 0xa9f018: r2 = _textTheme
    //     0xa9f018: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d230] Field <_LisTileDefaultsM2@560247952._textTheme@560247952>: late final (offset: 0x64)
    //     0xa9f01c: ldr             x2, [x2, #0x230]
    // 0xa9f020: r0 = InitLateFinalInstanceField()
    //     0xa9f020: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9f024: LoadField: r1 = r0->field_23
    //     0xa9f024: ldur            w1, [x0, #0x23]
    // 0xa9f028: DecompressPointer r1
    //     0xa9f028: add             x1, x1, HEAP, lsl #32
    // 0xa9f02c: mov             x0, x1
    // 0xa9f030: b               #0xa9f064
    // 0xa9f034: ldur            x1, [fp, #-0x30]
    // 0xa9f038: LoadField: r0 = r1->field_63
    //     0xa9f038: ldur            w0, [x1, #0x63]
    // 0xa9f03c: DecompressPointer r0
    //     0xa9f03c: add             x0, x0, HEAP, lsl #32
    // 0xa9f040: r16 = Sentinel
    //     0xa9f040: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9f044: cmp             w0, w16
    // 0xa9f048: b.ne            #0xa9f058
    // 0xa9f04c: r2 = _textTheme
    //     0xa9f04c: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d230] Field <_LisTileDefaultsM2@560247952._textTheme@560247952>: late final (offset: 0x64)
    //     0xa9f050: ldr             x2, [x2, #0x230]
    // 0xa9f054: r0 = InitLateFinalInstanceField()
    //     0xa9f054: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9f058: LoadField: r1 = r0->field_2b
    //     0xa9f058: ldur            w1, [x0, #0x2b]
    // 0xa9f05c: DecompressPointer r1
    //     0xa9f05c: add             x1, x1, HEAP, lsl #32
    // 0xa9f060: mov             x0, x1
    // 0xa9f064: cmp             w0, NULL
    // 0xa9f068: b.eq            #0xa9f4a0
    // 0xa9f06c: LoadField: r1 = r0->field_33
    //     0xa9f06c: ldur            w1, [x0, #0x33]
    // 0xa9f070: DecompressPointer r1
    //     0xa9f070: add             x1, x1, HEAP, lsl #32
    // 0xa9f074: cmp             w1, NULL
    // 0xa9f078: b.eq            #0xa9f4a4
    // 0xa9f07c: mov             x2, x1
    // 0xa9f080: b               #0xa9f088
    // 0xa9f084: mov             x2, x5
    // 0xa9f088: ldur            x0, [fp, #-0xb0]
    // 0xa9f08c: stur            x2, [fp, #-0x90]
    // 0xa9f090: cmp             w0, NULL
    // 0xa9f094: b.ne            #0xa9f0a0
    // 0xa9f098: r0 = Null
    //     0xa9f098: mov             x0, NULL
    // 0xa9f09c: b               #0xa9f0ac
    // 0xa9f0a0: LoadField: r1 = r0->field_33
    //     0xa9f0a0: ldur            w1, [x0, #0x33]
    // 0xa9f0a4: DecompressPointer r1
    //     0xa9f0a4: add             x1, x1, HEAP, lsl #32
    // 0xa9f0a8: mov             x0, x1
    // 0xa9f0ac: cmp             w0, NULL
    // 0xa9f0b0: b.ne            #0xa9f1ec
    // 0xa9f0b4: ldur            x0, [fp, #-0x30]
    // 0xa9f0b8: r1 = LoadClassIdInstr(r0)
    //     0xa9f0b8: ldur            x1, [x0, #-1]
    //     0xa9f0bc: ubfx            x1, x1, #0xc, #0x14
    // 0xa9f0c0: cmp             x1, #0xf64
    // 0xa9f0c4: b.ne            #0xa9f0d8
    // 0xa9f0c8: LoadField: r1 = r0->field_23
    //     0xa9f0c8: ldur            w1, [x0, #0x23]
    // 0xa9f0cc: DecompressPointer r1
    //     0xa9f0cc: add             x1, x1, HEAP, lsl #32
    // 0xa9f0d0: mov             x0, x1
    // 0xa9f0d4: b               #0xa9f1d4
    // 0xa9f0d8: cmp             x1, #0xf65
    // 0xa9f0dc: b.ne            #0xa9f178
    // 0xa9f0e0: mov             x1, x0
    // 0xa9f0e4: LoadField: r0 = r1->field_67
    //     0xa9f0e4: ldur            w0, [x1, #0x67]
    // 0xa9f0e8: DecompressPointer r0
    //     0xa9f0e8: add             x0, x0, HEAP, lsl #32
    // 0xa9f0ec: r16 = Sentinel
    //     0xa9f0ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9f0f0: cmp             w0, w16
    // 0xa9f0f4: b.ne            #0xa9f104
    // 0xa9f0f8: r2 = _textTheme
    //     0xa9f0f8: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d220] Field <_LisTileDefaultsM3@560247952._textTheme@560247952>: late final (offset: 0x68)
    //     0xa9f0fc: ldr             x2, [x2, #0x220]
    // 0xa9f100: r0 = InitLateFinalInstanceField()
    //     0xa9f100: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9f104: LoadField: r2 = r0->field_2f
    //     0xa9f104: ldur            w2, [x0, #0x2f]
    // 0xa9f108: DecompressPointer r2
    //     0xa9f108: add             x2, x2, HEAP, lsl #32
    // 0xa9f10c: stur            x2, [fp, #-0xa0]
    // 0xa9f110: cmp             w2, NULL
    // 0xa9f114: b.eq            #0xa9f4a8
    // 0xa9f118: ldur            x1, [fp, #-0x30]
    // 0xa9f11c: LoadField: r0 = r1->field_63
    //     0xa9f11c: ldur            w0, [x1, #0x63]
    // 0xa9f120: DecompressPointer r0
    //     0xa9f120: add             x0, x0, HEAP, lsl #32
    // 0xa9f124: r16 = Sentinel
    //     0xa9f124: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9f128: cmp             w0, w16
    // 0xa9f12c: b.ne            #0xa9f13c
    // 0xa9f130: r2 = _colors
    //     0xa9f130: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d208] Field <_LisTileDefaultsM3@560247952._colors@560247952>: late final (offset: 0x64)
    //     0xa9f134: ldr             x2, [x2, #0x208]
    // 0xa9f138: r0 = InitLateFinalInstanceField()
    //     0xa9f138: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9f13c: LoadField: r1 = r0->field_a3
    //     0xa9f13c: ldur            w1, [x0, #0xa3]
    // 0xa9f140: DecompressPointer r1
    //     0xa9f140: add             x1, x1, HEAP, lsl #32
    // 0xa9f144: cmp             w1, NULL
    // 0xa9f148: b.ne            #0xa9f15c
    // 0xa9f14c: LoadField: r1 = r0->field_7f
    //     0xa9f14c: ldur            w1, [x0, #0x7f]
    // 0xa9f150: DecompressPointer r1
    //     0xa9f150: add             x1, x1, HEAP, lsl #32
    // 0xa9f154: mov             x0, x1
    // 0xa9f158: b               #0xa9f160
    // 0xa9f15c: mov             x0, x1
    // 0xa9f160: str             x0, [SP]
    // 0xa9f164: ldur            x1, [fp, #-0xa0]
    // 0xa9f168: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xa9f168: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xa9f16c: ldr             x4, [x4, #0x228]
    // 0xa9f170: r0 = copyWith()
    //     0xa9f170: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa9f174: b               #0xa9f1d4
    // 0xa9f178: ldur            x1, [fp, #-0x30]
    // 0xa9f17c: LoadField: r0 = r1->field_63
    //     0xa9f17c: ldur            w0, [x1, #0x63]
    // 0xa9f180: DecompressPointer r0
    //     0xa9f180: add             x0, x0, HEAP, lsl #32
    // 0xa9f184: r16 = Sentinel
    //     0xa9f184: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9f188: cmp             w0, w16
    // 0xa9f18c: b.ne            #0xa9f19c
    // 0xa9f190: r2 = _textTheme
    //     0xa9f190: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d230] Field <_LisTileDefaultsM2@560247952._textTheme@560247952>: late final (offset: 0x64)
    //     0xa9f194: ldr             x2, [x2, #0x230]
    // 0xa9f198: r0 = InitLateFinalInstanceField()
    //     0xa9f198: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa9f19c: LoadField: r1 = r0->field_2f
    //     0xa9f19c: ldur            w1, [x0, #0x2f]
    // 0xa9f1a0: DecompressPointer r1
    //     0xa9f1a0: add             x1, x1, HEAP, lsl #32
    // 0xa9f1a4: cmp             w1, NULL
    // 0xa9f1a8: b.eq            #0xa9f4ac
    // 0xa9f1ac: LoadField: r2 = r0->field_33
    //     0xa9f1ac: ldur            w2, [x0, #0x33]
    // 0xa9f1b0: DecompressPointer r2
    //     0xa9f1b0: add             x2, x2, HEAP, lsl #32
    // 0xa9f1b4: cmp             w2, NULL
    // 0xa9f1b8: b.eq            #0xa9f4b0
    // 0xa9f1bc: LoadField: r0 = r2->field_b
    //     0xa9f1bc: ldur            w0, [x2, #0xb]
    // 0xa9f1c0: DecompressPointer r0
    //     0xa9f1c0: add             x0, x0, HEAP, lsl #32
    // 0xa9f1c4: str             x0, [SP]
    // 0xa9f1c8: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xa9f1c8: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xa9f1cc: ldr             x4, [x4, #0x228]
    // 0xa9f1d0: r0 = copyWith()
    //     0xa9f1d0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa9f1d4: LoadField: r1 = r0->field_33
    //     0xa9f1d4: ldur            w1, [x0, #0x33]
    // 0xa9f1d8: DecompressPointer r1
    //     0xa9f1d8: add             x1, x1, HEAP, lsl #32
    // 0xa9f1dc: cmp             w1, NULL
    // 0xa9f1e0: b.eq            #0xa9f4b4
    // 0xa9f1e4: mov             x2, x1
    // 0xa9f1e8: b               #0xa9f1f0
    // 0xa9f1ec: mov             x2, x0
    // 0xa9f1f0: ldur            x0, [fp, #-8]
    // 0xa9f1f4: stur            x2, [fp, #-0x30]
    // 0xa9f1f8: LoadField: r1 = r0->field_83
    //     0xa9f1f8: ldur            w1, [x0, #0x83]
    // 0xa9f1fc: DecompressPointer r1
    //     0xa9f1fc: add             x1, x1, HEAP, lsl #32
    // 0xa9f200: cmp             w1, NULL
    // 0xa9f204: b.ne            #0xa9f218
    // 0xa9f208: ldur            x3, [fp, #-0x28]
    // 0xa9f20c: LoadField: r1 = r3->field_37
    //     0xa9f20c: ldur            w1, [x3, #0x37]
    // 0xa9f210: DecompressPointer r1
    //     0xa9f210: add             x1, x1, HEAP, lsl #32
    // 0xa9f214: b               #0xa9f21c
    // 0xa9f218: ldur            x3, [fp, #-0x28]
    // 0xa9f21c: cmp             w1, NULL
    // 0xa9f220: b.ne            #0xa9f22c
    // 0xa9f224: d0 = 16.000000
    //     0xa9f224: fmov            d0, #16.00000000
    // 0xa9f228: b               #0xa9f230
    // 0xa9f22c: LoadField: d0 = r1->field_7
    //     0xa9f22c: ldur            d0, [x1, #7]
    // 0xa9f230: stur            d0, [fp, #-0xf0]
    // 0xa9f234: LoadField: r1 = r3->field_3b
    //     0xa9f234: ldur            w1, [x3, #0x3b]
    // 0xa9f238: DecompressPointer r1
    //     0xa9f238: add             x1, x1, HEAP, lsl #32
    // 0xa9f23c: cmp             w1, NULL
    // 0xa9f240: b.ne            #0xa9f24c
    // 0xa9f244: ldur            d1, [fp, #-0xd8]
    // 0xa9f248: b               #0xa9f250
    // 0xa9f24c: LoadField: d1 = r1->field_7
    //     0xa9f24c: ldur            d1, [x1, #7]
    // 0xa9f250: stur            d1, [fp, #-0xe8]
    // 0xa9f254: LoadField: r1 = r0->field_8b
    //     0xa9f254: ldur            w1, [x0, #0x8b]
    // 0xa9f258: DecompressPointer r1
    //     0xa9f258: add             x1, x1, HEAP, lsl #32
    // 0xa9f25c: cmp             w1, NULL
    // 0xa9f260: b.ne            #0xa9f270
    // 0xa9f264: LoadField: r0 = r3->field_3f
    //     0xa9f264: ldur            w0, [x3, #0x3f]
    // 0xa9f268: DecompressPointer r0
    //     0xa9f268: add             x0, x0, HEAP, lsl #32
    // 0xa9f26c: b               #0xa9f274
    // 0xa9f270: mov             x0, x1
    // 0xa9f274: cmp             w0, NULL
    // 0xa9f278: b.ne            #0xa9f284
    // 0xa9f27c: ldur            d2, [fp, #-0xe0]
    // 0xa9f280: b               #0xa9f288
    // 0xa9f284: LoadField: d2 = r0->field_7
    //     0xa9f284: ldur            d2, [x0, #7]
    // 0xa9f288: ldur            x9, [fp, #-0x50]
    // 0xa9f28c: ldur            x24, [fp, #-0x78]
    // 0xa9f290: ldur            x23, [fp, #-0x70]
    // 0xa9f294: ldur            x20, [fp, #-0xa8]
    // 0xa9f298: ldur            x19, [fp, #-0x98]
    // 0xa9f29c: ldur            x14, [fp, #-0x48]
    // 0xa9f2a0: ldur            x13, [fp, #-0x10]
    // 0xa9f2a4: ldur            x12, [fp, #-0x38]
    // 0xa9f2a8: ldur            x7, [fp, #-0xc0]
    // 0xa9f2ac: ldur            x8, [fp, #-0xb8]
    // 0xa9f2b0: ldur            x10, [fp, #-0x88]
    // 0xa9f2b4: ldur            x11, [fp, #-0x58]
    // 0xa9f2b8: ldur            x4, [fp, #-0xd0]
    // 0xa9f2bc: ldur            x6, [fp, #-0x18]
    // 0xa9f2c0: ldur            x0, [fp, #-0x90]
    // 0xa9f2c4: ldur            x5, [fp, #-0x20]
    // 0xa9f2c8: stur            d2, [fp, #-0xd8]
    // 0xa9f2cc: LoadField: r25 = r3->field_43
    //     0xa9f2cc: ldur            w25, [x3, #0x43]
    // 0xa9f2d0: DecompressPointer r25
    //     0xa9f2d0: add             x25, x25, HEAP, lsl #32
    // 0xa9f2d4: stur            x25, [fp, #-8]
    // 0xa9f2d8: r1 = <_ListTileSlot, RenderBox>
    //     0xa9f2d8: add             x1, PP, #0x39, lsl #12  ; [pp+0x39888] TypeArguments: <_ListTileSlot, RenderBox>
    //     0xa9f2dc: ldr             x1, [x1, #0x888]
    // 0xa9f2e0: r0 = _ListTile()
    //     0xa9f2e0: bl              #0xa9f4c4  ; Allocate_ListTileStub -> _ListTile (size=0x58)
    // 0xa9f2e4: mov             x1, x0
    // 0xa9f2e8: ldur            x0, [fp, #-0x70]
    // 0xa9f2ec: stur            x1, [fp, #-0x28]
    // 0xa9f2f0: StoreField: r1->field_f = r0
    //     0xa9f2f0: stur            w0, [x1, #0xf]
    // 0xa9f2f4: ldur            x0, [fp, #-0xa8]
    // 0xa9f2f8: StoreField: r1->field_13 = r0
    //     0xa9f2f8: stur            w0, [x1, #0x13]
    // 0xa9f2fc: ldur            x0, [fp, #-0x98]
    // 0xa9f300: ArrayStore: r1[0] = r0  ; List_4
    //     0xa9f300: stur            w0, [x1, #0x17]
    // 0xa9f304: ldur            x0, [fp, #-0x48]
    // 0xa9f308: StoreField: r1->field_1b = r0
    //     0xa9f308: stur            w0, [x1, #0x1b]
    // 0xa9f30c: r0 = false
    //     0xa9f30c: add             x0, NULL, #0x30  ; false
    // 0xa9f310: StoreField: r1->field_1f = r0
    //     0xa9f310: stur            w0, [x1, #0x1f]
    // 0xa9f314: ldur            x2, [fp, #-0x20]
    // 0xa9f318: StoreField: r1->field_23 = r2
    //     0xa9f318: stur            w2, [x1, #0x23]
    // 0xa9f31c: ldur            x2, [fp, #-0x18]
    // 0xa9f320: StoreField: r1->field_27 = r2
    //     0xa9f320: stur            w2, [x1, #0x27]
    // 0xa9f324: ldur            x2, [fp, #-0x10]
    // 0xa9f328: StoreField: r1->field_2b = r2
    //     0xa9f328: stur            w2, [x1, #0x2b]
    // 0xa9f32c: ldur            x2, [fp, #-0x90]
    // 0xa9f330: StoreField: r1->field_2f = r2
    //     0xa9f330: stur            w2, [x1, #0x2f]
    // 0xa9f334: ldur            d0, [fp, #-0xf0]
    // 0xa9f338: StoreField: r1->field_37 = d0
    //     0xa9f338: stur            d0, [x1, #0x37]
    // 0xa9f33c: ldur            d0, [fp, #-0xe8]
    // 0xa9f340: StoreField: r1->field_3f = d0
    //     0xa9f340: stur            d0, [x1, #0x3f]
    // 0xa9f344: ldur            d0, [fp, #-0xd8]
    // 0xa9f348: StoreField: r1->field_47 = d0
    //     0xa9f348: stur            d0, [x1, #0x47]
    // 0xa9f34c: ldur            x2, [fp, #-8]
    // 0xa9f350: StoreField: r1->field_4f = r2
    //     0xa9f350: stur            w2, [x1, #0x4f]
    // 0xa9f354: ldur            x2, [fp, #-0x30]
    // 0xa9f358: StoreField: r1->field_33 = r2
    //     0xa9f358: stur            w2, [x1, #0x33]
    // 0xa9f35c: ldur            x2, [fp, #-0xb8]
    // 0xa9f360: StoreField: r1->field_53 = r2
    //     0xa9f360: stur            w2, [x1, #0x53]
    // 0xa9f364: r0 = IconButtonTheme()
    //     0xa9f364: bl              #0x9e7344  ; AllocateIconButtonThemeStub -> IconButtonTheme (size=0x14)
    // 0xa9f368: mov             x1, x0
    // 0xa9f36c: ldur            x0, [fp, #-0x78]
    // 0xa9f370: StoreField: r1->field_f = r0
    //     0xa9f370: stur            w0, [x1, #0xf]
    // 0xa9f374: ldur            x0, [fp, #-0x28]
    // 0xa9f378: StoreField: r1->field_b = r0
    //     0xa9f378: stur            w0, [x1, #0xb]
    // 0xa9f37c: ldur            x2, [fp, #-0x68]
    // 0xa9f380: r0 = merge()
    //     0xa9f380: bl              #0x9e6b88  ; [package:flutter/src/widgets/icon_theme.dart] IconTheme::merge
    // 0xa9f384: stur            x0, [fp, #-8]
    // 0xa9f388: r0 = SafeArea()
    //     0xa9f388: bl              #0x91a5dc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xa9f38c: mov             x1, x0
    // 0xa9f390: r0 = true
    //     0xa9f390: add             x0, NULL, #0x20  ; true
    // 0xa9f394: stur            x1, [fp, #-0x10]
    // 0xa9f398: StoreField: r1->field_b = r0
    //     0xa9f398: stur            w0, [x1, #0xb]
    // 0xa9f39c: r2 = false
    //     0xa9f39c: add             x2, NULL, #0x30  ; false
    // 0xa9f3a0: StoreField: r1->field_f = r2
    //     0xa9f3a0: stur            w2, [x1, #0xf]
    // 0xa9f3a4: StoreField: r1->field_13 = r0
    //     0xa9f3a4: stur            w0, [x1, #0x13]
    // 0xa9f3a8: ArrayStore: r1[0] = r2  ; List_4
    //     0xa9f3a8: stur            w2, [x1, #0x17]
    // 0xa9f3ac: ldur            x3, [fp, #-0x38]
    // 0xa9f3b0: StoreField: r1->field_1b = r3
    //     0xa9f3b0: stur            w3, [x1, #0x1b]
    // 0xa9f3b4: StoreField: r1->field_1f = r2
    //     0xa9f3b4: stur            w2, [x1, #0x1f]
    // 0xa9f3b8: ldur            x3, [fp, #-8]
    // 0xa9f3bc: StoreField: r1->field_23 = r3
    //     0xa9f3bc: stur            w3, [x1, #0x23]
    // 0xa9f3c0: r0 = Ink()
    //     0xa9f3c0: bl              #0xa9f4b8  ; AllocateInkStub -> Ink (size=0x20)
    // 0xa9f3c4: mov             x1, x0
    // 0xa9f3c8: ldur            x0, [fp, #-0x10]
    // 0xa9f3cc: stur            x1, [fp, #-8]
    // 0xa9f3d0: StoreField: r1->field_b = r0
    //     0xa9f3d0: stur            w0, [x1, #0xb]
    // 0xa9f3d4: ldur            x0, [fp, #-0xd0]
    // 0xa9f3d8: StoreField: r1->field_13 = r0
    //     0xa9f3d8: stur            w0, [x1, #0x13]
    // 0xa9f3dc: r0 = Semantics()
    //     0xa9f3dc: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0xa9f3e0: stur            x0, [fp, #-0x10]
    // 0xa9f3e4: ldur            x16, [fp, #-0x40]
    // 0xa9f3e8: ldur            lr, [fp, #-0x60]
    // 0xa9f3ec: stp             lr, x16, [SP, #0x10]
    // 0xa9f3f0: ldur            x16, [fp, #-0x50]
    // 0xa9f3f4: ldur            lr, [fp, #-8]
    // 0xa9f3f8: stp             lr, x16, [SP]
    // 0xa9f3fc: mov             x1, x0
    // 0xa9f400: r4 = const [0, 0x5, 0x4, 0x1, button, 0x1, child, 0x4, enabled, 0x3, selected, 0x2, null]
    //     0xa9f400: add             x4, PP, #0x39, lsl #12  ; [pp+0x39890] List(13) [0, 0x5, 0x4, 0x1, "button", 0x1, "child", 0x4, "enabled", 0x3, "selected", 0x2, Null]
    //     0xa9f404: ldr             x4, [x4, #0x890]
    // 0xa9f408: r0 = Semantics()
    //     0xa9f408: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0xa9f40c: r0 = InkWell()
    //     0xa9f40c: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa9f410: ldur            x1, [fp, #-0x10]
    // 0xa9f414: StoreField: r0->field_b = r1
    //     0xa9f414: stur            w1, [x0, #0xb]
    // 0xa9f418: ldur            x1, [fp, #-0x58]
    // 0xa9f41c: StoreField: r0->field_f = r1
    //     0xa9f41c: stur            w1, [x0, #0xf]
    // 0xa9f420: ldur            x1, [fp, #-0xc0]
    // 0xa9f424: StoreField: r0->field_3f = r1
    //     0xa9f424: stur            w1, [x0, #0x3f]
    // 0xa9f428: r1 = true
    //     0xa9f428: add             x1, NULL, #0x20  ; true
    // 0xa9f42c: StoreField: r0->field_43 = r1
    //     0xa9f42c: stur            w1, [x0, #0x43]
    // 0xa9f430: r2 = Instance_BoxShape
    //     0xa9f430: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa9f434: ldr             x2, [x2, #0xca8]
    // 0xa9f438: StoreField: r0->field_47 = r2
    //     0xa9f438: stur            w2, [x0, #0x47]
    // 0xa9f43c: ldur            x2, [fp, #-0x88]
    // 0xa9f440: StoreField: r0->field_53 = r2
    //     0xa9f440: stur            w2, [x0, #0x53]
    // 0xa9f444: StoreField: r0->field_6f = r1
    //     0xa9f444: stur            w1, [x0, #0x6f]
    // 0xa9f448: r1 = false
    //     0xa9f448: add             x1, NULL, #0x30  ; false
    // 0xa9f44c: StoreField: r0->field_73 = r1
    //     0xa9f44c: stur            w1, [x0, #0x73]
    // 0xa9f450: ldur            x2, [fp, #-0x50]
    // 0xa9f454: StoreField: r0->field_83 = r2
    //     0xa9f454: stur            w2, [x0, #0x83]
    // 0xa9f458: StoreField: r0->field_7b = r1
    //     0xa9f458: stur            w1, [x0, #0x7b]
    // 0xa9f45c: LeaveFrame
    //     0xa9f45c: mov             SP, fp
    //     0xa9f460: ldp             fp, lr, [SP], #0x10
    // 0xa9f464: ret
    //     0xa9f464: ret             
    // 0xa9f468: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9f468: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9f46c: b               #0xa9ddc4
    // 0xa9f470: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f470: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f474: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f474: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f478: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f478: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f47c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f47c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f480: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f480: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f484: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f484: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f488: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f488: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f48c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f48c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f490: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f490: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f494: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f494: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f498: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f498: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f49c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f49c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f4a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f4a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f4a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f4a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f4a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f4a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f4ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f4ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f4b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f4b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa9f4b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9f4b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _tileBackgroundColor(/* No info */) {
    // ** addr: 0xa9f4d0, size: 0x7c
    // 0xa9f4d0: LoadField: r4 = r1->field_5f
    //     0xa9f4d0: ldur            w4, [x1, #0x5f]
    // 0xa9f4d4: DecompressPointer r4
    //     0xa9f4d4: add             x4, x4, HEAP, lsl #32
    // 0xa9f4d8: tbnz            w4, #4, #0xa9f50c
    // 0xa9f4dc: LoadField: r4 = r3->field_33
    //     0xa9f4dc: ldur            w4, [x3, #0x33]
    // 0xa9f4e0: DecompressPointer r4
    //     0xa9f4e0: add             x4, x4, HEAP, lsl #32
    // 0xa9f4e4: cmp             w4, NULL
    // 0xa9f4e8: b.ne            #0xa9f500
    // 0xa9f4ec: LoadField: r3 = r2->field_ef
    //     0xa9f4ec: ldur            w3, [x2, #0xef]
    // 0xa9f4f0: DecompressPointer r3
    //     0xa9f4f0: add             x3, x3, HEAP, lsl #32
    // 0xa9f4f4: LoadField: r2 = r3->field_33
    //     0xa9f4f4: ldur            w2, [x3, #0x33]
    // 0xa9f4f8: DecompressPointer r2
    //     0xa9f4f8: add             x2, x2, HEAP, lsl #32
    // 0xa9f4fc: b               #0xa9f504
    // 0xa9f500: mov             x2, x4
    // 0xa9f504: mov             x1, x2
    // 0xa9f508: b               #0xa9f534
    // 0xa9f50c: LoadField: r2 = r1->field_77
    //     0xa9f50c: ldur            w2, [x1, #0x77]
    // 0xa9f510: DecompressPointer r2
    //     0xa9f510: add             x2, x2, HEAP, lsl #32
    // 0xa9f514: cmp             w2, NULL
    // 0xa9f518: b.ne            #0xa9f524
    // 0xa9f51c: r1 = Null
    //     0xa9f51c: mov             x1, NULL
    // 0xa9f520: b               #0xa9f528
    // 0xa9f524: mov             x1, x2
    // 0xa9f528: cmp             w1, NULL
    // 0xa9f52c: b.ne            #0xa9f534
    // 0xa9f530: r1 = Null
    //     0xa9f530: mov             x1, NULL
    // 0xa9f534: cmp             w1, NULL
    // 0xa9f538: b.ne            #0xa9f544
    // 0xa9f53c: r0 = Instance_Color
    //     0xa9f53c: ldr             x0, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xa9f540: b               #0xa9f548
    // 0xa9f544: mov             x0, x1
    // 0xa9f548: ret
    //     0xa9f548: ret             
  }
}

// class id: 7056, size: 0x14, field offset: 0x14
enum _ListTileSlot extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc48f7c, size: 0x64
    // 0xc48f7c: EnterFrame
    //     0xc48f7c: stp             fp, lr, [SP, #-0x10]!
    //     0xc48f80: mov             fp, SP
    // 0xc48f84: AllocStack(0x10)
    //     0xc48f84: sub             SP, SP, #0x10
    // 0xc48f88: SetupParameters(_ListTileSlot this /* r1 => r0, fp-0x8 */)
    //     0xc48f88: mov             x0, x1
    //     0xc48f8c: stur            x1, [fp, #-8]
    // 0xc48f90: CheckStackOverflow
    //     0xc48f90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc48f94: cmp             SP, x16
    //     0xc48f98: b.ls            #0xc48fd8
    // 0xc48f9c: r1 = Null
    //     0xc48f9c: mov             x1, NULL
    // 0xc48fa0: r2 = 4
    //     0xc48fa0: movz            x2, #0x4
    // 0xc48fa4: r0 = AllocateArray()
    //     0xc48fa4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc48fa8: r16 = "_ListTileSlot."
    //     0xc48fa8: add             x16, PP, #0x54, lsl #12  ; [pp+0x54ce0] "_ListTileSlot."
    //     0xc48fac: ldr             x16, [x16, #0xce0]
    // 0xc48fb0: StoreField: r0->field_f = r16
    //     0xc48fb0: stur            w16, [x0, #0xf]
    // 0xc48fb4: ldur            x1, [fp, #-8]
    // 0xc48fb8: LoadField: r2 = r1->field_f
    //     0xc48fb8: ldur            w2, [x1, #0xf]
    // 0xc48fbc: DecompressPointer r2
    //     0xc48fbc: add             x2, x2, HEAP, lsl #32
    // 0xc48fc0: StoreField: r0->field_13 = r2
    //     0xc48fc0: stur            w2, [x0, #0x13]
    // 0xc48fc4: str             x0, [SP]
    // 0xc48fc8: r0 = _interpolate()
    //     0xc48fc8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc48fcc: LeaveFrame
    //     0xc48fcc: mov             SP, fp
    //     0xc48fd0: ldp             fp, lr, [SP], #0x10
    // 0xc48fd4: ret
    //     0xc48fd4: ret             
    // 0xc48fd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc48fd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc48fdc: b               #0xc48f9c
  }
}

// class id: 7057, size: 0x14, field offset: 0x14
enum ListTileTitleAlignment extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _yOffsetFor(/* No info */) {
    // ** addr: 0x741980, size: 0x26c
    // 0x741980: EnterFrame
    //     0x741980: stp             fp, lr, [SP, #-0x10]!
    //     0x741984: mov             fp, SP
    // 0x741988: CheckStackOverflow
    //     0x741988: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74198c: cmp             SP, x16
    //     0x741990: b.ls            #0x741b94
    // 0x741994: r16 = Instance_ListTileTitleAlignment
    //     0x741994: add             x16, PP, #0x39, lsl #12  ; [pp+0x39878] Obj!ListTileTitleAlignment@e366a1
    //     0x741998: ldr             x16, [x16, #0x878]
    // 0x74199c: cmp             w1, w16
    // 0x7419a0: b.ne            #0x7419dc
    // 0x7419a4: r1 = Instance_ListTileTitleAlignment
    //     0x7419a4: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4dd80] Obj!ListTileTitleAlignment@e36661
    //     0x7419a8: ldr             x1, [x1, #0xd80]
    // 0x7419ac: r0 = _yOffsetFor()
    //     0x7419ac: bl              #0x741980  ; [package:flutter/src/material/list_tile.dart] ListTileTitleAlignment::_yOffsetFor
    // 0x7419b0: r0 = inline_Allocate_Double()
    //     0x7419b0: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0x7419b4: add             x0, x0, #0x10
    //     0x7419b8: cmp             x4, x0
    //     0x7419bc: b.ls            #0x741b9c
    //     0x7419c0: str             x0, [THR, #0x50]  ; THR::top
    //     0x7419c4: sub             x0, x0, #0xf
    //     0x7419c8: movz            x4, #0xe15c
    //     0x7419cc: movk            x4, #0x3, lsl #16
    //     0x7419d0: stur            x4, [x0, #-1]
    // 0x7419d4: StoreField: r0->field_7 = d0
    //     0x7419d4: stur            d0, [x0, #7]
    // 0x7419d8: b               #0x741b84
    // 0x7419dc: r16 = Instance_ListTileTitleAlignment
    //     0x7419dc: add             x16, PP, #0x39, lsl #12  ; [pp+0x39880] Obj!ListTileTitleAlignment@e36681
    //     0x7419e0: ldr             x16, [x16, #0x880]
    // 0x7419e4: cmp             w1, w16
    // 0x7419e8: r16 = true
    //     0x7419e8: add             x16, NULL, #0x20  ; true
    // 0x7419ec: r17 = false
    //     0x7419ec: add             x17, NULL, #0x30  ; false
    // 0x7419f0: csel            x0, x16, x17, eq
    // 0x7419f4: tbnz            w0, #4, #0x741a14
    // 0x7419f8: d2 = 72.000000
    //     0x7419f8: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e378] IMM: double(72) from 0x4052000000000000
    //     0x7419fc: ldr             d2, [x17, #0x378]
    // 0x741a00: fcmp            d1, d2
    // 0x741a04: b.le            #0x741a14
    // 0x741a08: r0 = 16.000000
    //     0x741a08: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0x741a0c: ldr             x0, [x0, #0x80]
    // 0x741a10: b               #0x741b84
    // 0x741a14: tbnz            w0, #4, #0x741ab0
    // 0x741a18: tbnz            w3, #4, #0x741a74
    // 0x741a1c: d3 = 2.000000
    //     0x741a1c: fmov            d3, #2.00000000
    // 0x741a20: d2 = 16.000000
    //     0x741a20: fmov            d2, #16.00000000
    // 0x741a24: fsub            d4, d1, d0
    // 0x741a28: fdiv            d5, d4, d3
    // 0x741a2c: fcmp            d5, d2
    // 0x741a30: b.le            #0x741a3c
    // 0x741a34: d2 = 16.000000
    //     0x741a34: fmov            d2, #16.00000000
    // 0x741a38: b               #0x741a84
    // 0x741a3c: fcmp            d2, d5
    // 0x741a40: b.le            #0x741a4c
    // 0x741a44: mov             v2.16b, v5.16b
    // 0x741a48: b               #0x741a84
    // 0x741a4c: d4 = 0.000000
    //     0x741a4c: eor             v4.16b, v4.16b, v4.16b
    // 0x741a50: fcmp            d5, d4
    // 0x741a54: b.ne            #0x741a6c
    // 0x741a58: fadd            d4, d5, d2
    // 0x741a5c: fmul            d6, d4, d5
    // 0x741a60: fmul            d4, d6, d2
    // 0x741a64: mov             v2.16b, v4.16b
    // 0x741a68: b               #0x741a84
    // 0x741a6c: mov             v2.16b, v5.16b
    // 0x741a70: b               #0x741a84
    // 0x741a74: d3 = 2.000000
    //     0x741a74: fmov            d3, #2.00000000
    // 0x741a78: fsub            d2, d1, d0
    // 0x741a7c: fdiv            d4, d2, d3
    // 0x741a80: mov             v2.16b, v4.16b
    // 0x741a84: r0 = inline_Allocate_Double()
    //     0x741a84: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x741a88: add             x0, x0, #0x10
    //     0x741a8c: cmp             x3, x0
    //     0x741a90: b.ls            #0x741bac
    //     0x741a94: str             x0, [THR, #0x50]  ; THR::top
    //     0x741a98: sub             x0, x0, #0xf
    //     0x741a9c: movz            x3, #0xe15c
    //     0x741aa0: movk            x3, #0x3, lsl #16
    //     0x741aa4: stur            x3, [x0, #-1]
    // 0x741aa8: StoreField: r0->field_7 = d2
    //     0x741aa8: stur            d2, [x0, #7]
    // 0x741aac: b               #0x741b84
    // 0x741ab0: d3 = 2.000000
    //     0x741ab0: fmov            d3, #2.00000000
    // 0x741ab4: r16 = Instance_ListTileTitleAlignment
    //     0x741ab4: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4dd88] Obj!ListTileTitleAlignment@e36641
    //     0x741ab8: ldr             x16, [x16, #0xd88]
    // 0x741abc: cmp             w1, w16
    // 0x741ac0: b.ne            #0x741af4
    // 0x741ac4: LoadField: d2 = r2->field_7b
    //     0x741ac4: ldur            d2, [x2, #0x7b]
    // 0x741ac8: r0 = inline_Allocate_Double()
    //     0x741ac8: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x741acc: add             x0, x0, #0x10
    //     0x741ad0: cmp             x3, x0
    //     0x741ad4: b.ls            #0x741bbc
    //     0x741ad8: str             x0, [THR, #0x50]  ; THR::top
    //     0x741adc: sub             x0, x0, #0xf
    //     0x741ae0: movz            x3, #0xe15c
    //     0x741ae4: movk            x3, #0x3, lsl #16
    //     0x741ae8: stur            x3, [x0, #-1]
    // 0x741aec: StoreField: r0->field_7 = d2
    //     0x741aec: stur            d2, [x0, #7]
    // 0x741af0: b               #0x741b84
    // 0x741af4: r16 = Instance_ListTileTitleAlignment
    //     0x741af4: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4dd80] Obj!ListTileTitleAlignment@e36661
    //     0x741af8: ldr             x16, [x16, #0xd80]
    // 0x741afc: cmp             w1, w16
    // 0x741b00: b.ne            #0x741b38
    // 0x741b04: fsub            d2, d1, d0
    // 0x741b08: fdiv            d4, d2, d3
    // 0x741b0c: r0 = inline_Allocate_Double()
    //     0x741b0c: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x741b10: add             x0, x0, #0x10
    //     0x741b14: cmp             x3, x0
    //     0x741b18: b.ls            #0x741bcc
    //     0x741b1c: str             x0, [THR, #0x50]  ; THR::top
    //     0x741b20: sub             x0, x0, #0xf
    //     0x741b24: movz            x3, #0xe15c
    //     0x741b28: movk            x3, #0x3, lsl #16
    //     0x741b2c: stur            x3, [x0, #-1]
    // 0x741b30: StoreField: r0->field_7 = d4
    //     0x741b30: stur            d4, [x0, #7]
    // 0x741b34: b               #0x741b84
    // 0x741b38: r16 = Instance_ListTileTitleAlignment
    //     0x741b38: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4dd90] Obj!ListTileTitleAlignment@e36621
    //     0x741b3c: ldr             x16, [x16, #0xd90]
    // 0x741b40: cmp             w1, w16
    // 0x741b44: b.ne            #0x741b80
    // 0x741b48: fsub            d2, d1, d0
    // 0x741b4c: LoadField: d1 = r2->field_7b
    //     0x741b4c: ldur            d1, [x2, #0x7b]
    // 0x741b50: fsub            d3, d2, d1
    // 0x741b54: r0 = inline_Allocate_Double()
    //     0x741b54: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x741b58: add             x0, x0, #0x10
    //     0x741b5c: cmp             x1, x0
    //     0x741b60: b.ls            #0x741bdc
    //     0x741b64: str             x0, [THR, #0x50]  ; THR::top
    //     0x741b68: sub             x0, x0, #0xf
    //     0x741b6c: movz            x1, #0xe15c
    //     0x741b70: movk            x1, #0x3, lsl #16
    //     0x741b74: stur            x1, [x0, #-1]
    // 0x741b78: StoreField: r0->field_7 = d3
    //     0x741b78: stur            d3, [x0, #7]
    // 0x741b7c: b               #0x741b84
    // 0x741b80: r0 = Null
    //     0x741b80: mov             x0, NULL
    // 0x741b84: LoadField: d0 = r0->field_7
    //     0x741b84: ldur            d0, [x0, #7]
    // 0x741b88: LeaveFrame
    //     0x741b88: mov             SP, fp
    //     0x741b8c: ldp             fp, lr, [SP], #0x10
    // 0x741b90: ret
    //     0x741b90: ret             
    // 0x741b94: r0 = StackOverflowSharedWithFPURegs()
    //     0x741b94: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x741b98: b               #0x741994
    // 0x741b9c: SaveReg d0
    //     0x741b9c: str             q0, [SP, #-0x10]!
    // 0x741ba0: r0 = AllocateDouble()
    //     0x741ba0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x741ba4: RestoreReg d0
    //     0x741ba4: ldr             q0, [SP], #0x10
    // 0x741ba8: b               #0x7419d4
    // 0x741bac: SaveReg d2
    //     0x741bac: str             q2, [SP, #-0x10]!
    // 0x741bb0: r0 = AllocateDouble()
    //     0x741bb0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x741bb4: RestoreReg d2
    //     0x741bb4: ldr             q2, [SP], #0x10
    // 0x741bb8: b               #0x741aa8
    // 0x741bbc: SaveReg d2
    //     0x741bbc: str             q2, [SP, #-0x10]!
    // 0x741bc0: r0 = AllocateDouble()
    //     0x741bc0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x741bc4: RestoreReg d2
    //     0x741bc4: ldr             q2, [SP], #0x10
    // 0x741bc8: b               #0x741aec
    // 0x741bcc: SaveReg d4
    //     0x741bcc: str             q4, [SP, #-0x10]!
    // 0x741bd0: r0 = AllocateDouble()
    //     0x741bd0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x741bd4: RestoreReg d4
    //     0x741bd4: ldr             q4, [SP], #0x10
    // 0x741bd8: b               #0x741b30
    // 0x741bdc: SaveReg d3
    //     0x741bdc: str             q3, [SP, #-0x10]!
    // 0x741be0: r0 = AllocateDouble()
    //     0x741be0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x741be4: RestoreReg d3
    //     0x741be4: ldr             q3, [SP], #0x10
    // 0x741be8: b               #0x741b78
  }
  _ _enumToString(/* No info */) {
    // ** addr: 0xc48f18, size: 0x64
    // 0xc48f18: EnterFrame
    //     0xc48f18: stp             fp, lr, [SP, #-0x10]!
    //     0xc48f1c: mov             fp, SP
    // 0xc48f20: AllocStack(0x10)
    //     0xc48f20: sub             SP, SP, #0x10
    // 0xc48f24: SetupParameters(ListTileTitleAlignment this /* r1 => r0, fp-0x8 */)
    //     0xc48f24: mov             x0, x1
    //     0xc48f28: stur            x1, [fp, #-8]
    // 0xc48f2c: CheckStackOverflow
    //     0xc48f2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc48f30: cmp             SP, x16
    //     0xc48f34: b.ls            #0xc48f74
    // 0xc48f38: r1 = Null
    //     0xc48f38: mov             x1, NULL
    // 0xc48f3c: r2 = 4
    //     0xc48f3c: movz            x2, #0x4
    // 0xc48f40: r0 = AllocateArray()
    //     0xc48f40: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc48f44: r16 = "ListTileTitleAlignment."
    //     0xc48f44: add             x16, PP, #0x44, lsl #12  ; [pp+0x44410] "ListTileTitleAlignment."
    //     0xc48f48: ldr             x16, [x16, #0x410]
    // 0xc48f4c: StoreField: r0->field_f = r16
    //     0xc48f4c: stur            w16, [x0, #0xf]
    // 0xc48f50: ldur            x1, [fp, #-8]
    // 0xc48f54: LoadField: r2 = r1->field_f
    //     0xc48f54: ldur            w2, [x1, #0xf]
    // 0xc48f58: DecompressPointer r2
    //     0xc48f58: add             x2, x2, HEAP, lsl #32
    // 0xc48f5c: StoreField: r0->field_13 = r2
    //     0xc48f5c: stur            w2, [x0, #0x13]
    // 0xc48f60: str             x0, [SP]
    // 0xc48f64: r0 = _interpolate()
    //     0xc48f64: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc48f68: LeaveFrame
    //     0xc48f68: mov             SP, fp
    //     0xc48f6c: ldp             fp, lr, [SP], #0x10
    // 0xc48f70: ret
    //     0xc48f70: ret             
    // 0xc48f74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc48f74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc48f78: b               #0xc48f38
  }
}

// class id: 7058, size: 0x14, field offset: 0x14
enum ListTileControlAffinity extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc48eb4, size: 0x64
    // 0xc48eb4: EnterFrame
    //     0xc48eb4: stp             fp, lr, [SP, #-0x10]!
    //     0xc48eb8: mov             fp, SP
    // 0xc48ebc: AllocStack(0x10)
    //     0xc48ebc: sub             SP, SP, #0x10
    // 0xc48ec0: SetupParameters(ListTileControlAffinity this /* r1 => r0, fp-0x8 */)
    //     0xc48ec0: mov             x0, x1
    //     0xc48ec4: stur            x1, [fp, #-8]
    // 0xc48ec8: CheckStackOverflow
    //     0xc48ec8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc48ecc: cmp             SP, x16
    //     0xc48ed0: b.ls            #0xc48f10
    // 0xc48ed4: r1 = Null
    //     0xc48ed4: mov             x1, NULL
    // 0xc48ed8: r2 = 4
    //     0xc48ed8: movz            x2, #0x4
    // 0xc48edc: r0 = AllocateArray()
    //     0xc48edc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc48ee0: r16 = "ListTileControlAffinity."
    //     0xc48ee0: add             x16, PP, #0x39, lsl #12  ; [pp+0x39840] "ListTileControlAffinity."
    //     0xc48ee4: ldr             x16, [x16, #0x840]
    // 0xc48ee8: StoreField: r0->field_f = r16
    //     0xc48ee8: stur            w16, [x0, #0xf]
    // 0xc48eec: ldur            x1, [fp, #-8]
    // 0xc48ef0: LoadField: r2 = r1->field_f
    //     0xc48ef0: ldur            w2, [x1, #0xf]
    // 0xc48ef4: DecompressPointer r2
    //     0xc48ef4: add             x2, x2, HEAP, lsl #32
    // 0xc48ef8: StoreField: r0->field_13 = r2
    //     0xc48ef8: stur            w2, [x0, #0x13]
    // 0xc48efc: str             x0, [SP]
    // 0xc48f00: r0 = _interpolate()
    //     0xc48f00: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc48f04: LeaveFrame
    //     0xc48f04: mov             SP, fp
    //     0xc48f08: ldp             fp, lr, [SP], #0x10
    // 0xc48f0c: ret
    //     0xc48f0c: ret             
    // 0xc48f10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc48f10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc48f14: b               #0xc48ed4
  }
}

// class id: 7059, size: 0x14, field offset: 0x14
enum ListTileStyle extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc48e50, size: 0x64
    // 0xc48e50: EnterFrame
    //     0xc48e50: stp             fp, lr, [SP, #-0x10]!
    //     0xc48e54: mov             fp, SP
    // 0xc48e58: AllocStack(0x10)
    //     0xc48e58: sub             SP, SP, #0x10
    // 0xc48e5c: SetupParameters(ListTileStyle this /* r1 => r0, fp-0x8 */)
    //     0xc48e5c: mov             x0, x1
    //     0xc48e60: stur            x1, [fp, #-8]
    // 0xc48e64: CheckStackOverflow
    //     0xc48e64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc48e68: cmp             SP, x16
    //     0xc48e6c: b.ls            #0xc48eac
    // 0xc48e70: r1 = Null
    //     0xc48e70: mov             x1, NULL
    // 0xc48e74: r2 = 4
    //     0xc48e74: movz            x2, #0x4
    // 0xc48e78: r0 = AllocateArray()
    //     0xc48e78: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc48e7c: r16 = "ListTileStyle."
    //     0xc48e7c: add             x16, PP, #0x44, lsl #12  ; [pp+0x443f0] "ListTileStyle."
    //     0xc48e80: ldr             x16, [x16, #0x3f0]
    // 0xc48e84: StoreField: r0->field_f = r16
    //     0xc48e84: stur            w16, [x0, #0xf]
    // 0xc48e88: ldur            x1, [fp, #-8]
    // 0xc48e8c: LoadField: r2 = r1->field_f
    //     0xc48e8c: ldur            w2, [x1, #0xf]
    // 0xc48e90: DecompressPointer r2
    //     0xc48e90: add             x2, x2, HEAP, lsl #32
    // 0xc48e94: StoreField: r0->field_13 = r2
    //     0xc48e94: stur            w2, [x0, #0x13]
    // 0xc48e98: str             x0, [SP]
    // 0xc48e9c: r0 = _interpolate()
    //     0xc48e9c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc48ea0: LeaveFrame
    //     0xc48ea0: mov             SP, fp
    //     0xc48ea4: ldp             fp, lr, [SP], #0x10
    // 0xc48ea8: ret
    //     0xc48ea8: ret             
    // 0xc48eac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc48eac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc48eb0: b               #0xc48e70
  }
}
