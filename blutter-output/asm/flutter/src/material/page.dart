// lib: , url: package:flutter/src/material/page.dart

// class id: 1048929, size: 0x8
class :: {
}

// class id: 2660, size: 0x9c, field offset: 0x9c
//   transformed mixin,
abstract class _MaterialPageRoute&PageRoute&MaterialRouteTransitionMixin<X0> extends PageRoute<X0>
     with MaterialRouteTransitionMixin<X0> {

  _ canTransitionFrom(/* No info */) {
    // ** addr: 0xd05068, size: 0x30
    // 0xd05068: r3 = LoadClassIdInstr(r2)
    //     0xd05068: ldur            x3, [x2, #-1]
    //     0xd0506c: ubfx            x3, x3, #0xc, #0x14
    // 0xd05070: sub             x16, x3, #0xa62
    // 0xd05074: cmp             x16, #3
    // 0xd05078: b.hi            #0xd05090
    // 0xd0507c: LoadField: r2 = r1->field_8f
    //     0xd0507c: ldur            w2, [x1, #0x8f]
    // 0xd05080: DecompressPointer r2
    //     0xd05080: add             x2, x2, HEAP, lsl #32
    // 0xd05084: eor             x1, x2, #0x10
    // 0xd05088: mov             x0, x1
    // 0xd0508c: b               #0xd05094
    // 0xd05090: r0 = false
    //     0xd05090: add             x0, NULL, #0x30  ; false
    // 0xd05094: ret
    //     0xd05094: ret             
  }
  _ buildPage(/* No info */) {
    // ** addr: 0xd336f0, size: 0x64
    // 0xd336f0: EnterFrame
    //     0xd336f0: stp             fp, lr, [SP, #-0x10]!
    //     0xd336f4: mov             fp, SP
    // 0xd336f8: AllocStack(0x28)
    //     0xd336f8: sub             SP, SP, #0x28
    // 0xd336fc: CheckStackOverflow
    //     0xd336fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd33700: cmp             SP, x16
    //     0xd33704: b.ls            #0xd3374c
    // 0xd33708: r0 = buildContent()
    //     0xd33708: bl              #0xd33754  ; [package:flutter/src/material/page.dart] MaterialPageRoute::buildContent
    // 0xd3370c: stur            x0, [fp, #-8]
    // 0xd33710: r0 = Semantics()
    //     0xd33710: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0xd33714: stur            x0, [fp, #-0x10]
    // 0xd33718: r16 = true
    //     0xd33718: add             x16, NULL, #0x20  ; true
    // 0xd3371c: r30 = true
    //     0xd3371c: add             lr, NULL, #0x20  ; true
    // 0xd33720: stp             lr, x16, [SP, #8]
    // 0xd33724: ldur            x16, [fp, #-8]
    // 0xd33728: str             x16, [SP]
    // 0xd3372c: mov             x1, x0
    // 0xd33730: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, explicitChildNodes, 0x2, scopesRoute, 0x1, null]
    //     0xd33730: add             x4, PP, #0x30, lsl #12  ; [pp+0x30c40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "explicitChildNodes", 0x2, "scopesRoute", 0x1, Null]
    //     0xd33734: ldr             x4, [x4, #0xc40]
    // 0xd33738: r0 = Semantics()
    //     0xd33738: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0xd3373c: ldur            x0, [fp, #-0x10]
    // 0xd33740: LeaveFrame
    //     0xd33740: mov             SP, fp
    //     0xd33744: ldp             fp, lr, [SP], #0x10
    // 0xd33748: ret
    //     0xd33748: ret             
    // 0xd3374c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3374c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd33750: b               #0xd33708
  }
  get _ delegatedTransition(/* No info */) {
    // ** addr: 0xdb8f24, size: 0xc
    // 0xdb8f24: r0 = Closure: (BuildContext, Animation<double>, Animation<double>, bool, Widget?) => Widget? from Function '_delegatedTransition@578331911': static.
    //     0xdb8f24: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1a728] Closure: (BuildContext, Animation<double>, Animation<double>, bool, Widget?) => Widget? from Function '_delegatedTransition@578331911': static. (0x7e54fb0545cc)
    //     0xdb8f28: ldr             x0, [x0, #0x728]
    // 0xdb8f2c: ret
    //     0xdb8f2c: ret             
  }
}

// class id: 2661, size: 0xa4, field offset: 0x9c
class MaterialPageRoute<X0> extends _MaterialPageRoute&PageRoute&MaterialRouteTransitionMixin<X0> {

  _ MaterialPageRoute(/* No info */) {
    // ** addr: 0x9e4a78, size: 0x100
    // 0x9e4a78: EnterFrame
    //     0x9e4a78: stp             fp, lr, [SP, #-0x10]!
    //     0x9e4a7c: mov             fp, SP
    // 0x9e4a80: mov             x0, x2
    // 0x9e4a84: LoadField: r2 = r4->field_13
    //     0x9e4a84: ldur            w2, [x4, #0x13]
    // 0x9e4a88: LoadField: r3 = r4->field_1f
    //     0x9e4a88: ldur            w3, [x4, #0x1f]
    // 0x9e4a8c: DecompressPointer r3
    //     0x9e4a8c: add             x3, x3, HEAP, lsl #32
    // 0x9e4a90: r16 = "fullscreenDialog"
    //     0x9e4a90: add             x16, PP, #0x44, lsl #12  ; [pp+0x448b8] "fullscreenDialog"
    //     0x9e4a94: ldr             x16, [x16, #0x8b8]
    // 0x9e4a98: cmp             w3, w16
    // 0x9e4a9c: b.ne            #0x9e4ac0
    // 0x9e4aa0: LoadField: r3 = r4->field_23
    //     0x9e4aa0: ldur            w3, [x4, #0x23]
    // 0x9e4aa4: DecompressPointer r3
    //     0x9e4aa4: add             x3, x3, HEAP, lsl #32
    // 0x9e4aa8: sub             w5, w2, w3
    // 0x9e4aac: add             x3, fp, w5, sxtw #2
    // 0x9e4ab0: ldr             x3, [x3, #8]
    // 0x9e4ab4: mov             x5, x3
    // 0x9e4ab8: r3 = 1
    //     0x9e4ab8: movz            x3, #0x1
    // 0x9e4abc: b               #0x9e4ac8
    // 0x9e4ac0: r5 = false
    //     0x9e4ac0: add             x5, NULL, #0x30  ; false
    // 0x9e4ac4: r3 = 0
    //     0x9e4ac4: movz            x3, #0
    // 0x9e4ac8: lsl             x6, x3, #1
    // 0x9e4acc: lsl             w3, w6, #1
    // 0x9e4ad0: add             w6, w3, #8
    // 0x9e4ad4: ArrayLoad: r7 = r4[r6]  ; Unknown_4
    //     0x9e4ad4: add             x16, x4, w6, sxtw #1
    //     0x9e4ad8: ldur            w7, [x16, #0xf]
    // 0x9e4adc: DecompressPointer r7
    //     0x9e4adc: add             x7, x7, HEAP, lsl #32
    // 0x9e4ae0: r16 = "settings"
    //     0x9e4ae0: add             x16, PP, #0x36, lsl #12  ; [pp+0x368e8] "settings"
    //     0x9e4ae4: ldr             x16, [x16, #0x8e8]
    // 0x9e4ae8: cmp             w7, w16
    // 0x9e4aec: b.ne            #0x9e4b14
    // 0x9e4af0: add             w6, w3, #0xa
    // 0x9e4af4: ArrayLoad: r3 = r4[r6]  ; Unknown_4
    //     0x9e4af4: add             x16, x4, w6, sxtw #1
    //     0x9e4af8: ldur            w3, [x16, #0xf]
    // 0x9e4afc: DecompressPointer r3
    //     0x9e4afc: add             x3, x3, HEAP, lsl #32
    // 0x9e4b00: sub             w4, w2, w3
    // 0x9e4b04: add             x2, fp, w4, sxtw #2
    // 0x9e4b08: ldr             x2, [x2, #8]
    // 0x9e4b0c: mov             x4, x2
    // 0x9e4b10: b               #0x9e4b18
    // 0x9e4b14: r4 = Null
    //     0x9e4b14: mov             x4, NULL
    // 0x9e4b18: r3 = false
    //     0x9e4b18: add             x3, NULL, #0x30  ; false
    // 0x9e4b1c: r2 = true
    //     0x9e4b1c: add             x2, NULL, #0x20  ; true
    // 0x9e4b20: CheckStackOverflow
    //     0x9e4b20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e4b24: cmp             SP, x16
    //     0x9e4b28: b.ls            #0x9e4b70
    // 0x9e4b2c: StoreField: r1->field_9b = r0
    //     0x9e4b2c: stur            w0, [x1, #0x9b]
    //     0x9e4b30: ldurb           w16, [x1, #-1]
    //     0x9e4b34: ldurb           w17, [x0, #-1]
    //     0x9e4b38: and             x16, x17, x16, lsr #2
    //     0x9e4b3c: tst             x16, HEAP, lsr #32
    //     0x9e4b40: b.eq            #0x9e4b48
    //     0x9e4b44: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9e4b48: StoreField: r1->field_9f = r2
    //     0x9e4b48: stur            w2, [x1, #0x9f]
    // 0x9e4b4c: StoreField: r1->field_8f = r5
    //     0x9e4b4c: stur            w5, [x1, #0x8f]
    // 0x9e4b50: StoreField: r1->field_93 = r2
    //     0x9e4b50: stur            w2, [x1, #0x93]
    // 0x9e4b54: StoreField: r1->field_97 = r3
    //     0x9e4b54: stur            w3, [x1, #0x97]
    // 0x9e4b58: mov             x2, x4
    // 0x9e4b5c: r0 = ModalRoute()
    //     0x9e4b5c: bl              #0x65a308  ; [package:flutter/src/widgets/routes.dart] ModalRoute::ModalRoute
    // 0x9e4b60: r0 = Null
    //     0x9e4b60: mov             x0, NULL
    // 0x9e4b64: LeaveFrame
    //     0x9e4b64: mov             SP, fp
    //     0x9e4b68: ldp             fp, lr, [SP], #0x10
    // 0x9e4b6c: ret
    //     0x9e4b6c: ret             
    // 0x9e4b70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e4b70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e4b74: b               #0x9e4b2c
  }
  _ buildContent(/* No info */) {
    // ** addr: 0xd33754, size: 0x44
    // 0xd33754: EnterFrame
    //     0xd33754: stp             fp, lr, [SP, #-0x10]!
    //     0xd33758: mov             fp, SP
    // 0xd3375c: AllocStack(0x10)
    //     0xd3375c: sub             SP, SP, #0x10
    // 0xd33760: CheckStackOverflow
    //     0xd33760: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd33764: cmp             SP, x16
    //     0xd33768: b.ls            #0xd33790
    // 0xd3376c: LoadField: r0 = r1->field_9b
    //     0xd3376c: ldur            w0, [x1, #0x9b]
    // 0xd33770: DecompressPointer r0
    //     0xd33770: add             x0, x0, HEAP, lsl #32
    // 0xd33774: stp             x2, x0, [SP]
    // 0xd33778: ClosureCall
    //     0xd33778: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xd3377c: ldur            x2, [x0, #0x1f]
    //     0xd33780: blr             x2
    // 0xd33784: LeaveFrame
    //     0xd33784: mov             SP, fp
    //     0xd33788: ldp             fp, lr, [SP], #0x10
    // 0xd3378c: ret
    //     0xd3378c: ret             
    // 0xd33790: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd33790: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd33794: b               #0xd3376c
  }
}

// class id: 2662, size: 0x9c, field offset: 0x9c
abstract class MaterialRouteTransitionMixin<X0> extends PageRoute<X0> {

  [closure] static Widget? _delegatedTransition(dynamic, BuildContext, Animation<double>, Animation<double>, bool, Widget?) {
    // ** addr: 0x6545cc, size: 0x40
    // 0x6545cc: EnterFrame
    //     0x6545cc: stp             fp, lr, [SP, #-0x10]!
    //     0x6545d0: mov             fp, SP
    // 0x6545d4: CheckStackOverflow
    //     0x6545d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6545d8: cmp             SP, x16
    //     0x6545dc: b.ls            #0x654604
    // 0x6545e0: ldr             x1, [fp, #0x30]
    // 0x6545e4: ldr             x2, [fp, #0x28]
    // 0x6545e8: ldr             x3, [fp, #0x20]
    // 0x6545ec: ldr             x5, [fp, #0x18]
    // 0x6545f0: ldr             x6, [fp, #0x10]
    // 0x6545f4: r0 = _delegatedTransition()
    //     0x6545f4: bl              #0x65460c  ; [package:flutter/src/material/page.dart] MaterialRouteTransitionMixin::_delegatedTransition
    // 0x6545f8: LeaveFrame
    //     0x6545f8: mov             SP, fp
    //     0x6545fc: ldp             fp, lr, [SP], #0x10
    // 0x654600: ret
    //     0x654600: ret             
    // 0x654604: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x654604: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x654608: b               #0x6545e0
  }
  static _ _delegatedTransition(/* No info */) {
    // ** addr: 0x65460c, size: 0x98
    // 0x65460c: EnterFrame
    //     0x65460c: stp             fp, lr, [SP, #-0x10]!
    //     0x654610: mov             fp, SP
    // 0x654614: AllocStack(0x58)
    //     0x654614: sub             SP, SP, #0x58
    // 0x654618: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0x654618: mov             x0, x1
    //     0x65461c: stur            x1, [fp, #-8]
    //     0x654620: stur            x2, [fp, #-0x10]
    //     0x654624: stur            x3, [fp, #-0x18]
    //     0x654628: stur            x5, [fp, #-0x20]
    //     0x65462c: stur            x6, [fp, #-0x28]
    // 0x654630: CheckStackOverflow
    //     0x654630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x654634: cmp             SP, x16
    //     0x654638: b.ls            #0x65469c
    // 0x65463c: mov             x1, x0
    // 0x654640: r0 = of()
    //     0x654640: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x654644: ldur            x1, [fp, #-8]
    // 0x654648: r0 = of()
    //     0x654648: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x65464c: r1 = Instance_PageTransitionsTheme
    //     0x65464c: ldr             x1, [PP, #0x5108]  ; [pp+0x5108] Obj!PageTransitionsTheme@e1c7f1
    // 0x654650: r0 = delegatedTransition()
    //     0x654650: bl              #0x6546a4  ; [package:flutter/src/material/page_transitions_theme.dart] PageTransitionsTheme::delegatedTransition
    // 0x654654: cmp             w0, NULL
    // 0x654658: b.eq            #0x65468c
    // 0x65465c: ldur            x16, [fp, #-8]
    // 0x654660: stp             x16, x0, [SP, #0x20]
    // 0x654664: ldur            x16, [fp, #-0x10]
    // 0x654668: ldur            lr, [fp, #-0x18]
    // 0x65466c: stp             lr, x16, [SP, #0x10]
    // 0x654670: ldur            x16, [fp, #-0x20]
    // 0x654674: ldur            lr, [fp, #-0x28]
    // 0x654678: stp             lr, x16, [SP]
    // 0x65467c: ClosureCall
    //     0x65467c: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x6, 0x6, 0x6, Null]
    //     0x654680: ldur            x2, [x0, #0x1f]
    //     0x654684: blr             x2
    // 0x654688: b               #0x654690
    // 0x65468c: r0 = Null
    //     0x65468c: mov             x0, NULL
    // 0x654690: LeaveFrame
    //     0x654690: mov             SP, fp
    //     0x654694: ldp             fp, lr, [SP], #0x10
    // 0x654698: ret
    //     0x654698: ret             
    // 0x65469c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65469c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6546a0: b               #0x65463c
  }
}
