// lib: , url: package:flutter/src/material/page_transitions_theme.dart

// class id: 1048930, size: 0x8
class :: {

  static _ _drawImageScaledAndCentered(/* No info */) {
    // ** addr: 0xd8493c, size: 0x208
    // 0xd8493c: EnterFrame
    //     0xd8493c: stp             fp, lr, [SP, #-0x10]!
    //     0xd84940: mov             fp, SP
    // 0xd84944: AllocStack(0x68)
    //     0xd84944: sub             SP, SP, #0x68
    // 0xd84948: d3 = 0.000000
    //     0xd84948: eor             v3.16b, v3.16b, v3.16b
    // 0xd8494c: stur            x1, [fp, #-8]
    // 0xd84950: stur            x2, [fp, #-0x10]
    // 0xd84954: stur            d0, [fp, #-0x40]
    // 0xd84958: stur            d1, [fp, #-0x48]
    // 0xd8495c: stur            d2, [fp, #-0x50]
    // 0xd84960: CheckStackOverflow
    //     0xd84960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd84964: cmp             SP, x16
    //     0xd84968: b.ls            #0xd84b3c
    // 0xd8496c: fcmp            d3, d0
    // 0xd84970: b.ge            #0xd8497c
    // 0xd84974: fcmp            d3, d1
    // 0xd84978: b.lt            #0xd8498c
    // 0xd8497c: r0 = Null
    //     0xd8497c: mov             x0, NULL
    // 0xd84980: LeaveFrame
    //     0xd84980: mov             SP, fp
    //     0xd84984: ldp             fp, lr, [SP], #0x10
    // 0xd84988: ret
    //     0xd84988: ret             
    // 0xd8498c: r16 = 136
    //     0xd8498c: movz            x16, #0x88
    // 0xd84990: stp             x16, NULL, [SP]
    // 0xd84994: r0 = ByteData()
    //     0xd84994: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xd84998: stur            x0, [fp, #-0x18]
    // 0xd8499c: r0 = Paint()
    //     0xd8499c: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xd849a0: mov             x1, x0
    // 0xd849a4: ldur            x0, [fp, #-0x18]
    // 0xd849a8: stur            x1, [fp, #-0x20]
    // 0xd849ac: StoreField: r1->field_7 = r0
    //     0xd849ac: stur            w0, [x1, #7]
    // 0xd849b0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xd849b0: ldur            w2, [x0, #0x17]
    // 0xd849b4: DecompressPointer r2
    //     0xd849b4: add             x2, x2, HEAP, lsl #32
    // 0xd849b8: LoadField: r0 = r2->field_7
    //     0xd849b8: ldur            x0, [x2, #7]
    // 0xd849bc: r2 = 2
    //     0xd849bc: movz            x2, #0x2
    // 0xd849c0: str             w2, [x0, #0x30]
    // 0xd849c4: r0 = Color()
    //     0xd849c4: bl              #0x62a9c4  ; AllocateColorStub -> Color (size=0x2c)
    // 0xd849c8: mov             x1, x0
    // 0xd849cc: r0 = Instance_ColorSpace
    //     0xd849cc: ldr             x0, [PP, #0x5788]  ; [pp+0x5788] Obj!ColorSpace@e39a61
    // 0xd849d0: StoreField: r1->field_27 = r0
    //     0xd849d0: stur            w0, [x1, #0x27]
    // 0xd849d4: ldur            d0, [fp, #-0x48]
    // 0xd849d8: StoreField: r1->field_7 = d0
    //     0xd849d8: stur            d0, [x1, #7]
    // 0xd849dc: StoreField: r1->field_f = rZR
    //     0xd849dc: stur            xzr, [x1, #0xf]
    // 0xd849e0: ArrayStore: r1[0] = rZR  ; List_8
    //     0xd849e0: stur            xzr, [x1, #0x17]
    // 0xd849e4: StoreField: r1->field_1f = rZR
    //     0xd849e4: stur            xzr, [x1, #0x1f]
    // 0xd849e8: mov             x2, x1
    // 0xd849ec: ldur            x1, [fp, #-0x20]
    // 0xd849f0: r0 = color=()
    //     0xd849f0: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0xd849f4: ldur            x2, [fp, #-0x10]
    // 0xd849f8: LoadField: r0 = r2->field_f
    //     0xd849f8: ldur            x0, [x2, #0xf]
    // 0xd849fc: stur            x0, [fp, #-0x30]
    // 0xd84a00: scvtf           d0, x0
    // 0xd84a04: ldur            d1, [fp, #-0x50]
    // 0xd84a08: fdiv            d2, d0, d1
    // 0xd84a0c: ArrayLoad: r1 = r2[0]  ; List_8
    //     0xd84a0c: ldur            x1, [x2, #0x17]
    // 0xd84a10: stur            x1, [fp, #-0x28]
    // 0xd84a14: scvtf           d0, x1
    // 0xd84a18: fdiv            d3, d0, d1
    // 0xd84a1c: ldur            d0, [fp, #-0x40]
    // 0xd84a20: fmul            d1, d2, d0
    // 0xd84a24: fmul            d4, d3, d0
    // 0xd84a28: fsub            d0, d2, d1
    // 0xd84a2c: d2 = 2.000000
    //     0xd84a2c: fmov            d2, #2.00000000
    // 0xd84a30: fdiv            d5, d0, d2
    // 0xd84a34: stur            d5, [fp, #-0x58]
    // 0xd84a38: fsub            d0, d3, d4
    // 0xd84a3c: fdiv            d3, d0, d2
    // 0xd84a40: stur            d3, [fp, #-0x50]
    // 0xd84a44: fadd            d0, d5, d1
    // 0xd84a48: stur            d0, [fp, #-0x48]
    // 0xd84a4c: fadd            d1, d3, d4
    // 0xd84a50: stur            d1, [fp, #-0x40]
    // 0xd84a54: r0 = Rect()
    //     0xd84a54: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0xd84a58: ldur            d0, [fp, #-0x58]
    // 0xd84a5c: stur            x0, [fp, #-0x18]
    // 0xd84a60: StoreField: r0->field_7 = d0
    //     0xd84a60: stur            d0, [x0, #7]
    // 0xd84a64: ldur            d0, [fp, #-0x50]
    // 0xd84a68: StoreField: r0->field_f = d0
    //     0xd84a68: stur            d0, [x0, #0xf]
    // 0xd84a6c: ldur            d0, [fp, #-0x48]
    // 0xd84a70: ArrayStore: r0[0] = d0  ; List_8
    //     0xd84a70: stur            d0, [x0, #0x17]
    // 0xd84a74: ldur            d0, [fp, #-0x40]
    // 0xd84a78: StoreField: r0->field_1f = d0
    //     0xd84a78: stur            d0, [x0, #0x1f]
    // 0xd84a7c: ldur            x1, [fp, #-8]
    // 0xd84a80: r0 = canvas()
    //     0xd84a80: bl              #0x789cf4  ; [package:flutter/src/rendering/object.dart] PaintingContext::canvas
    // 0xd84a84: mov             x3, x0
    // 0xd84a88: ldur            x2, [fp, #-0x30]
    // 0xd84a8c: stur            x3, [fp, #-8]
    // 0xd84a90: r0 = BoxInt64Instr(r2)
    //     0xd84a90: sbfiz           x0, x2, #1, #0x1f
    //     0xd84a94: cmp             x2, x0, asr #1
    //     0xd84a98: b.eq            #0xd84aa4
    //     0xd84a9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd84aa0: stur            x2, [x0, #7]
    // 0xd84aa4: stp             x0, NULL, [SP]
    // 0xd84aa8: r0 = _Double.fromInteger()
    //     0xd84aa8: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xd84aac: mov             x3, x0
    // 0xd84ab0: ldur            x2, [fp, #-0x28]
    // 0xd84ab4: stur            x3, [fp, #-0x38]
    // 0xd84ab8: r0 = BoxInt64Instr(r2)
    //     0xd84ab8: sbfiz           x0, x2, #1, #0x1f
    //     0xd84abc: cmp             x2, x0, asr #1
    //     0xd84ac0: b.eq            #0xd84acc
    //     0xd84ac4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd84ac8: stur            x2, [x0, #7]
    // 0xd84acc: stp             x0, NULL, [SP]
    // 0xd84ad0: r0 = _Double.fromInteger()
    //     0xd84ad0: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xd84ad4: mov             x1, x0
    // 0xd84ad8: ldur            x0, [fp, #-0x38]
    // 0xd84adc: LoadField: d0 = r0->field_7
    //     0xd84adc: ldur            d0, [x0, #7]
    // 0xd84ae0: d1 = 0.000000
    //     0xd84ae0: eor             v1.16b, v1.16b, v1.16b
    // 0xd84ae4: fadd            d2, d0, d1
    // 0xd84ae8: stur            d2, [fp, #-0x48]
    // 0xd84aec: LoadField: d0 = r1->field_7
    //     0xd84aec: ldur            d0, [x1, #7]
    // 0xd84af0: fadd            d3, d0, d1
    // 0xd84af4: stur            d3, [fp, #-0x40]
    // 0xd84af8: r0 = Rect()
    //     0xd84af8: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0xd84afc: StoreField: r0->field_7 = rZR
    //     0xd84afc: stur            xzr, [x0, #7]
    // 0xd84b00: StoreField: r0->field_f = rZR
    //     0xd84b00: stur            xzr, [x0, #0xf]
    // 0xd84b04: ldur            d0, [fp, #-0x48]
    // 0xd84b08: ArrayStore: r0[0] = d0  ; List_8
    //     0xd84b08: stur            d0, [x0, #0x17]
    // 0xd84b0c: ldur            d0, [fp, #-0x40]
    // 0xd84b10: StoreField: r0->field_1f = d0
    //     0xd84b10: stur            d0, [x0, #0x1f]
    // 0xd84b14: ldur            x1, [fp, #-8]
    // 0xd84b18: ldur            x2, [fp, #-0x10]
    // 0xd84b1c: mov             x3, x0
    // 0xd84b20: ldur            x5, [fp, #-0x18]
    // 0xd84b24: ldur            x6, [fp, #-0x20]
    // 0xd84b28: r0 = drawImageRect()
    //     0xd84b28: bl              #0x79a0a4  ; [dart:ui] _NativeCanvas::drawImageRect
    // 0xd84b2c: r0 = Null
    //     0xd84b2c: mov             x0, NULL
    // 0xd84b30: LeaveFrame
    //     0xd84b30: mov             SP, fp
    //     0xd84b34: ldp             fp, lr, [SP], #0x10
    // 0xd84b38: ret
    //     0xd84b38: ret             
    // 0xd84b3c: r0 = StackOverflowSharedWithFPURegs()
    //     0xd84b3c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd84b40: b               #0xd8496c
  }
  static _ _updateScaledTransform(/* No info */) {
    // ** addr: 0xd87978, size: 0xfc
    // 0xd87978: EnterFrame
    //     0xd87978: stp             fp, lr, [SP, #-0x10]!
    //     0xd8797c: mov             fp, SP
    // 0xd87980: AllocStack(0x20)
    //     0xd87980: sub             SP, SP, #0x20
    // 0xd87984: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x18 */)
    //     0xd87984: mov             x0, x1
    //     0xd87988: stur            x1, [fp, #-8]
    //     0xd8798c: stur            x2, [fp, #-0x10]
    //     0xd87990: stur            d0, [fp, #-0x18]
    // 0xd87994: CheckStackOverflow
    //     0xd87994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd87998: cmp             SP, x16
    //     0xd8799c: b.ls            #0xd87a50
    // 0xd879a0: mov             x1, x0
    // 0xd879a4: r0 = setIdentity()
    //     0xd879a4: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xd879a8: ldur            d1, [fp, #-0x18]
    // 0xd879ac: d0 = 1.000000
    //     0xd879ac: fmov            d0, #1.00000000
    // 0xd879b0: fcmp            d1, d0
    // 0xd879b4: b.ne            #0xd879c8
    // 0xd879b8: r0 = Null
    //     0xd879b8: mov             x0, NULL
    // 0xd879bc: LeaveFrame
    //     0xd879bc: mov             SP, fp
    //     0xd879c0: ldp             fp, lr, [SP], #0x10
    // 0xd879c4: ret
    //     0xd879c4: ret             
    // 0xd879c8: ldur            x0, [fp, #-0x10]
    // 0xd879cc: r2 = inline_Allocate_Double()
    //     0xd879cc: ldp             x2, x1, [THR, #0x50]  ; THR::top
    //     0xd879d0: add             x2, x2, #0x10
    //     0xd879d4: cmp             x1, x2
    //     0xd879d8: b.ls            #0xd87a58
    //     0xd879dc: str             x2, [THR, #0x50]  ; THR::top
    //     0xd879e0: sub             x2, x2, #0xf
    //     0xd879e4: movz            x1, #0xe15c
    //     0xd879e8: movk            x1, #0x3, lsl #16
    //     0xd879ec: stur            x1, [x2, #-1]
    // 0xd879f0: StoreField: r2->field_7 = d1
    //     0xd879f0: stur            d1, [x2, #7]
    // 0xd879f4: str             x2, [SP]
    // 0xd879f8: ldur            x1, [fp, #-8]
    // 0xd879fc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xd879fc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xd87a00: r0 = scale()
    //     0xd87a00: bl              #0x645258  ; [package:vector_math/vector_math_64.dart] Matrix4::scale
    // 0xd87a04: ldur            x0, [fp, #-0x10]
    // 0xd87a08: LoadField: d0 = r0->field_7
    //     0xd87a08: ldur            d0, [x0, #7]
    // 0xd87a0c: ldur            d1, [fp, #-0x18]
    // 0xd87a10: fmul            d2, d0, d1
    // 0xd87a14: fsub            d3, d2, d0
    // 0xd87a18: d0 = 2.000000
    //     0xd87a18: fmov            d0, #2.00000000
    // 0xd87a1c: fdiv            d2, d3, d0
    // 0xd87a20: LoadField: d3 = r0->field_f
    //     0xd87a20: ldur            d3, [x0, #0xf]
    // 0xd87a24: fmul            d4, d3, d1
    // 0xd87a28: fsub            d1, d4, d3
    // 0xd87a2c: fdiv            d3, d1, d0
    // 0xd87a30: fneg            d0, d2
    // 0xd87a34: fneg            d1, d3
    // 0xd87a38: ldur            x1, [fp, #-8]
    // 0xd87a3c: r0 = translate()
    //     0xd87a3c: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xd87a40: r0 = Null
    //     0xd87a40: mov             x0, NULL
    // 0xd87a44: LeaveFrame
    //     0xd87a44: mov             SP, fp
    //     0xd87a48: ldp             fp, lr, [SP], #0x10
    // 0xd87a4c: ret
    //     0xd87a4c: ret             
    // 0xd87a50: r0 = StackOverflowSharedWithFPURegs()
    //     0xd87a50: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd87a54: b               #0xd879a0
    // 0xd87a58: SaveReg d1
    //     0xd87a58: str             q1, [SP, #-0x10]!
    // 0xd87a5c: SaveReg r0
    //     0xd87a5c: str             x0, [SP, #-8]!
    // 0xd87a60: r0 = AllocateDouble()
    //     0xd87a60: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd87a64: mov             x2, x0
    // 0xd87a68: RestoreReg r0
    //     0xd87a68: ldr             x0, [SP], #8
    // 0xd87a6c: RestoreReg d1
    //     0xd87a6c: ldr             q1, [SP], #0x10
    // 0xd87a70: b               #0xd879f0
  }
}

// class id: 3301, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class PageTransitionsBuilder extends Object {
}

// class id: 3302, size: 0x8, field offset: 0x8
//   const constructor, 
class CupertinoPageTransitionsBuilder extends PageTransitionsBuilder {

  _ buildTransitions(/* No info */) {
    // ** addr: 0xd82da0, size: 0x70
    // 0xd82da0: EnterFrame
    //     0xd82da0: stp             fp, lr, [SP, #-0x10]!
    //     0xd82da4: mov             fp, SP
    // 0xd82da8: AllocStack(0x28)
    //     0xd82da8: sub             SP, SP, #0x28
    // 0xd82dac: SetupParameters()
    //     0xd82dac: ldur            w0, [x4, #0xf]
    //     0xd82db0: cbnz            w0, #0xd82dbc
    //     0xd82db4: mov             x0, NULL
    //     0xd82db8: b               #0xd82dcc
    //     0xd82dbc: ldur            w0, [x4, #0x17]
    //     0xd82dc0: add             x1, fp, w0, sxtw #2
    //     0xd82dc4: ldr             x1, [x1, #0x10]
    //     0xd82dc8: mov             x0, x1
    // 0xd82dcc: CheckStackOverflow
    //     0xd82dcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd82dd0: cmp             SP, x16
    //     0xd82dd4: b.ls            #0xd82e08
    // 0xd82dd8: ldr             x16, [fp, #0x28]
    // 0xd82ddc: stp             x16, x0, [SP, #0x18]
    // 0xd82de0: ldr             x16, [fp, #0x20]
    // 0xd82de4: ldr             lr, [fp, #0x18]
    // 0xd82de8: stp             lr, x16, [SP, #8]
    // 0xd82dec: ldr             x16, [fp, #0x10]
    // 0xd82df0: str             x16, [SP]
    // 0xd82df4: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0xd82df4: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0xd82df8: r0 = buildPageTransitions()
    //     0xd82df8: bl              #0xd82e10  ; [package:flutter/src/cupertino/route.dart] CupertinoRouteTransitionMixin::buildPageTransitions
    // 0xd82dfc: LeaveFrame
    //     0xd82dfc: mov             SP, fp
    //     0xd82e00: ldp             fp, lr, [SP], #0x10
    // 0xd82e04: ret
    //     0xd82e04: ret             
    // 0xd82e08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd82e08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd82e0c: b               #0xd82dd8
  }
  get _ delegatedTransition(/* No info */) {
    // ** addr: 0xd89804, size: 0xc
    // 0xd89804: r0 = Closure: (BuildContext, Animation<double>, Animation<double>, bool, Widget?) => Widget? from Function 'delegatedTransition': static.
    //     0xd89804: add             x0, PP, #0x22, lsl #12  ; [pp+0x220c8] Closure: (BuildContext, Animation<double>, Animation<double>, bool, Widget?) => Widget? from Function 'delegatedTransition': static. (0x7e54fb789810)
    //     0xd89808: ldr             x0, [x0, #0xc8]
    // 0xd8980c: ret
    //     0xd8980c: ret             
  }
}

// class id: 3303, size: 0x14, field offset: 0x8
//   const constructor, 
class ZoomPageTransitionsBuilder extends PageTransitionsBuilder {

  bool field_8;
  bool field_c;

  static _ _snapshotAwareDelegatedTransition(/* No info */) {
    // ** addr: 0xa9fc24, size: 0x118
    // 0xa9fc24: EnterFrame
    //     0xa9fc24: stp             fp, lr, [SP, #-0x10]!
    //     0xa9fc28: mov             fp, SP
    // 0xa9fc2c: AllocStack(0x30)
    //     0xa9fc2c: sub             SP, SP, #0x30
    // 0xa9fc30: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0xa9fc30: stur            x1, [fp, #-8]
    //     0xa9fc34: stur            x2, [fp, #-0x10]
    //     0xa9fc38: stur            x3, [fp, #-0x18]
    //     0xa9fc3c: stur            x5, [fp, #-0x20]
    //     0xa9fc40: stur            x6, [fp, #-0x28]
    // 0xa9fc44: CheckStackOverflow
    //     0xa9fc44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9fc48: cmp             SP, x16
    //     0xa9fc4c: b.ls            #0xa9fd34
    // 0xa9fc50: r1 = 2
    //     0xa9fc50: movz            x1, #0x2
    // 0xa9fc54: r0 = AllocateContext()
    //     0xa9fc54: bl              #0xec126c  ; AllocateContextStub
    // 0xa9fc58: mov             x2, x0
    // 0xa9fc5c: ldur            x0, [fp, #-0x20]
    // 0xa9fc60: stur            x2, [fp, #-0x30]
    // 0xa9fc64: StoreField: r2->field_f = r0
    //     0xa9fc64: stur            w0, [x2, #0xf]
    // 0xa9fc68: ldur            x0, [fp, #-0x28]
    // 0xa9fc6c: cmp             w0, NULL
    // 0xa9fc70: b.ne            #0xa9fc8c
    // 0xa9fc74: ldur            x1, [fp, #-8]
    // 0xa9fc78: r0 = of()
    //     0xa9fc78: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa9fc7c: LoadField: r1 = r0->field_3f
    //     0xa9fc7c: ldur            w1, [x0, #0x3f]
    // 0xa9fc80: DecompressPointer r1
    //     0xa9fc80: add             x1, x1, HEAP, lsl #32
    // 0xa9fc84: LoadField: r0 = r1->field_7b
    //     0xa9fc84: ldur            w0, [x1, #0x7b]
    // 0xa9fc88: DecompressPointer r0
    //     0xa9fc88: add             x0, x0, HEAP, lsl #32
    // 0xa9fc8c: ldur            x4, [fp, #-0x10]
    // 0xa9fc90: ldur            x3, [fp, #-0x18]
    // 0xa9fc94: ldur            x2, [fp, #-0x30]
    // 0xa9fc98: StoreField: r2->field_13 = r0
    //     0xa9fc98: stur            w0, [x2, #0x13]
    //     0xa9fc9c: ldurb           w16, [x2, #-1]
    //     0xa9fca0: ldurb           w17, [x0, #-1]
    //     0xa9fca4: and             x16, x17, x16, lsr #2
    //     0xa9fca8: tst             x16, HEAP, lsr #32
    //     0xa9fcac: b.eq            #0xa9fcb4
    //     0xa9fcb0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa9fcb4: r1 = <double>
    //     0xa9fcb4: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xa9fcb8: r0 = ReverseAnimation()
    //     0xa9fcb8: bl              #0x9390b4  ; AllocateReverseAnimationStub -> ReverseAnimation (size=0x1c)
    // 0xa9fcbc: mov             x2, x0
    // 0xa9fcc0: ldur            x0, [fp, #-0x10]
    // 0xa9fcc4: stur            x2, [fp, #-8]
    // 0xa9fcc8: ArrayStore: r2[0] = r0  ; List_4
    //     0xa9fcc8: stur            w0, [x2, #0x17]
    // 0xa9fccc: mov             x1, x2
    // 0xa9fcd0: r0 = _ReverseAnimation&Animation&AnimationLazyListenerMixin&AnimationLocalStatusListenersMixin()
    //     0xa9fcd0: bl              #0x939008  ; [package:flutter/src/animation/animations.dart] _ReverseAnimation&Animation&AnimationLazyListenerMixin&AnimationLocalStatusListenersMixin::_ReverseAnimation&Animation&AnimationLazyListenerMixin&AnimationLocalStatusListenersMixin
    // 0xa9fcd4: r0 = DualTransitionBuilder()
    //     0xa9fcd4: bl              #0xa9fc18  ; AllocateDualTransitionBuilderStub -> DualTransitionBuilder (size=0x1c)
    // 0xa9fcd8: mov             x3, x0
    // 0xa9fcdc: ldur            x0, [fp, #-8]
    // 0xa9fce0: stur            x3, [fp, #-0x10]
    // 0xa9fce4: StoreField: r3->field_b = r0
    //     0xa9fce4: stur            w0, [x3, #0xb]
    // 0xa9fce8: ldur            x2, [fp, #-0x30]
    // 0xa9fcec: r1 = Function '<anonymous closure>': static.
    //     0xa9fcec: add             x1, PP, #0x22, lsl #12  ; [pp+0x220b8] AnonymousClosure: static (0xa9fd9c), in [package:flutter/src/material/page_transitions_theme.dart] ZoomPageTransitionsBuilder::_snapshotAwareDelegatedTransition (0xa9fc24)
    //     0xa9fcf0: ldr             x1, [x1, #0xb8]
    // 0xa9fcf4: r0 = AllocateClosure()
    //     0xa9fcf4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa9fcf8: mov             x1, x0
    // 0xa9fcfc: ldur            x0, [fp, #-0x10]
    // 0xa9fd00: StoreField: r0->field_f = r1
    //     0xa9fd00: stur            w1, [x0, #0xf]
    // 0xa9fd04: ldur            x2, [fp, #-0x30]
    // 0xa9fd08: r1 = Function '<anonymous closure>': static.
    //     0xa9fd08: add             x1, PP, #0x22, lsl #12  ; [pp+0x220c0] AnonymousClosure: static (0xa9fd3c), in [package:flutter/src/material/page_transitions_theme.dart] ZoomPageTransitionsBuilder::_snapshotAwareDelegatedTransition (0xa9fc24)
    //     0xa9fd0c: ldr             x1, [x1, #0xc0]
    // 0xa9fd10: r0 = AllocateClosure()
    //     0xa9fd10: bl              #0xec1630  ; AllocateClosureStub
    // 0xa9fd14: mov             x1, x0
    // 0xa9fd18: ldur            x0, [fp, #-0x10]
    // 0xa9fd1c: StoreField: r0->field_13 = r1
    //     0xa9fd1c: stur            w1, [x0, #0x13]
    // 0xa9fd20: ldur            x1, [fp, #-0x18]
    // 0xa9fd24: ArrayStore: r0[0] = r1  ; List_4
    //     0xa9fd24: stur            w1, [x0, #0x17]
    // 0xa9fd28: LeaveFrame
    //     0xa9fd28: mov             SP, fp
    //     0xa9fd2c: ldp             fp, lr, [SP], #0x10
    // 0xa9fd30: ret
    //     0xa9fd30: ret             
    // 0xa9fd34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9fd34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9fd38: b               #0xa9fc50
  }
  [closure] static _ZoomExitTransition <anonymous closure>(dynamic, BuildContext, Animation<double>, Widget?) {
    // ** addr: 0xa9fd3c, size: 0x54
    // 0xa9fd3c: EnterFrame
    //     0xa9fd3c: stp             fp, lr, [SP, #-0x10]!
    //     0xa9fd40: mov             fp, SP
    // 0xa9fd44: AllocStack(0x8)
    //     0xa9fd44: sub             SP, SP, #8
    // 0xa9fd48: SetupParameters()
    //     0xa9fd48: ldr             x0, [fp, #0x28]
    //     0xa9fd4c: ldur            w1, [x0, #0x17]
    //     0xa9fd50: add             x1, x1, HEAP, lsl #32
    // 0xa9fd54: LoadField: r0 = r1->field_f
    //     0xa9fd54: ldur            w0, [x1, #0xf]
    // 0xa9fd58: DecompressPointer r0
    //     0xa9fd58: add             x0, x0, HEAP, lsl #32
    // 0xa9fd5c: stur            x0, [fp, #-8]
    // 0xa9fd60: r0 = _ZoomExitTransition()
    //     0xa9fd60: bl              #0xa9fd90  ; Allocate_ZoomExitTransitionStub -> _ZoomExitTransition (size=0x1c)
    // 0xa9fd64: ldr             x1, [fp, #0x18]
    // 0xa9fd68: StoreField: r0->field_b = r1
    //     0xa9fd68: stur            w1, [x0, #0xb]
    // 0xa9fd6c: r1 = false
    //     0xa9fd6c: add             x1, NULL, #0x30  ; false
    // 0xa9fd70: StoreField: r0->field_13 = r1
    //     0xa9fd70: stur            w1, [x0, #0x13]
    // 0xa9fd74: ldur            x1, [fp, #-8]
    // 0xa9fd78: StoreField: r0->field_f = r1
    //     0xa9fd78: stur            w1, [x0, #0xf]
    // 0xa9fd7c: ldr             x1, [fp, #0x10]
    // 0xa9fd80: ArrayStore: r0[0] = r1  ; List_4
    //     0xa9fd80: stur            w1, [x0, #0x17]
    // 0xa9fd84: LeaveFrame
    //     0xa9fd84: mov             SP, fp
    //     0xa9fd88: ldp             fp, lr, [SP], #0x10
    // 0xa9fd8c: ret
    //     0xa9fd8c: ret             
  }
  [closure] static _ZoomEnterTransition <anonymous closure>(dynamic, BuildContext, Animation<double>, Widget?) {
    // ** addr: 0xa9fd9c, size: 0x80
    // 0xa9fd9c: EnterFrame
    //     0xa9fd9c: stp             fp, lr, [SP, #-0x10]!
    //     0xa9fda0: mov             fp, SP
    // 0xa9fda4: AllocStack(0x10)
    //     0xa9fda4: sub             SP, SP, #0x10
    // 0xa9fda8: SetupParameters()
    //     0xa9fda8: ldr             x0, [fp, #0x28]
    //     0xa9fdac: ldur            w1, [x0, #0x17]
    //     0xa9fdb0: add             x1, x1, HEAP, lsl #32
    // 0xa9fdb4: LoadField: r0 = r1->field_f
    //     0xa9fdb4: ldur            w0, [x1, #0xf]
    // 0xa9fdb8: DecompressPointer r0
    //     0xa9fdb8: add             x0, x0, HEAP, lsl #32
    // 0xa9fdbc: tbnz            w0, #4, #0xa9fdc8
    // 0xa9fdc0: r3 = true
    //     0xa9fdc0: add             x3, NULL, #0x20  ; true
    // 0xa9fdc4: b               #0xa9fdcc
    // 0xa9fdc8: r3 = false
    //     0xa9fdc8: add             x3, NULL, #0x30  ; false
    // 0xa9fdcc: ldr             x2, [fp, #0x18]
    // 0xa9fdd0: ldr             x0, [fp, #0x10]
    // 0xa9fdd4: stur            x3, [fp, #-0x10]
    // 0xa9fdd8: LoadField: r4 = r1->field_13
    //     0xa9fdd8: ldur            w4, [x1, #0x13]
    // 0xa9fddc: DecompressPointer r4
    //     0xa9fddc: add             x4, x4, HEAP, lsl #32
    // 0xa9fde0: stur            x4, [fp, #-8]
    // 0xa9fde4: r0 = _ZoomEnterTransition()
    //     0xa9fde4: bl              #0xa9fe1c  ; Allocate_ZoomEnterTransitionStub -> _ZoomEnterTransition (size=0x20)
    // 0xa9fde8: ldr             x1, [fp, #0x18]
    // 0xa9fdec: StoreField: r0->field_b = r1
    //     0xa9fdec: stur            w1, [x0, #0xb]
    // 0xa9fdf0: r1 = true
    //     0xa9fdf0: add             x1, NULL, #0x20  ; true
    // 0xa9fdf4: ArrayStore: r0[0] = r1  ; List_4
    //     0xa9fdf4: stur            w1, [x0, #0x17]
    // 0xa9fdf8: ldur            x1, [fp, #-0x10]
    // 0xa9fdfc: StoreField: r0->field_13 = r1
    //     0xa9fdfc: stur            w1, [x0, #0x13]
    // 0xa9fe00: ldur            x1, [fp, #-8]
    // 0xa9fe04: StoreField: r0->field_1b = r1
    //     0xa9fe04: stur            w1, [x0, #0x1b]
    // 0xa9fe08: ldr             x1, [fp, #0x10]
    // 0xa9fe0c: StoreField: r0->field_f = r1
    //     0xa9fe0c: stur            w1, [x0, #0xf]
    // 0xa9fe10: LeaveFrame
    //     0xa9fe10: mov             SP, fp
    //     0xa9fe14: ldp             fp, lr, [SP], #0x10
    // 0xa9fe18: ret
    //     0xa9fe18: ret             
  }
  _ buildTransitions(/* No info */) {
    // ** addr: 0xd82d58, size: 0x3c
    // 0xd82d58: EnterFrame
    //     0xd82d58: stp             fp, lr, [SP, #-0x10]!
    //     0xd82d5c: mov             fp, SP
    // 0xd82d60: r0 = _ZoomPageTransition()
    //     0xd82d60: bl              #0xd82d94  ; Allocate_ZoomPageTransitionStub -> _ZoomPageTransition (size=0x24)
    // 0xd82d64: ldr             x1, [fp, #0x20]
    // 0xd82d68: StoreField: r0->field_b = r1
    //     0xd82d68: stur            w1, [x0, #0xb]
    // 0xd82d6c: ldr             x1, [fp, #0x18]
    // 0xd82d70: StoreField: r0->field_f = r1
    //     0xd82d70: stur            w1, [x0, #0xf]
    // 0xd82d74: r1 = true
    //     0xd82d74: add             x1, NULL, #0x20  ; true
    // 0xd82d78: StoreField: r0->field_13 = r1
    //     0xd82d78: stur            w1, [x0, #0x13]
    // 0xd82d7c: StoreField: r0->field_1f = r1
    //     0xd82d7c: stur            w1, [x0, #0x1f]
    // 0xd82d80: ldr             x1, [fp, #0x10]
    // 0xd82d84: StoreField: r0->field_1b = r1
    //     0xd82d84: stur            w1, [x0, #0x1b]
    // 0xd82d88: LeaveFrame
    //     0xd82d88: mov             SP, fp
    //     0xd82d8c: ldp             fp, lr, [SP], #0x10
    // 0xd82d90: ret
    //     0xd82d90: ret             
  }
  get _ delegatedTransition(/* No info */) {
    // ** addr: 0xd89774, size: 0x40
    // 0xd89774: EnterFrame
    //     0xd89774: stp             fp, lr, [SP, #-0x10]!
    //     0xd89778: mov             fp, SP
    // 0xd8977c: AllocStack(0x8)
    //     0xd8977c: sub             SP, SP, #8
    // 0xd89780: SetupParameters(ZoomPageTransitionsBuilder this /* r1 => r1, fp-0x8 */)
    //     0xd89780: stur            x1, [fp, #-8]
    // 0xd89784: r1 = 1
    //     0xd89784: movz            x1, #0x1
    // 0xd89788: r0 = AllocateContext()
    //     0xd89788: bl              #0xec126c  ; AllocateContextStub
    // 0xd8978c: mov             x1, x0
    // 0xd89790: ldur            x0, [fp, #-8]
    // 0xd89794: StoreField: r1->field_f = r0
    //     0xd89794: stur            w0, [x1, #0xf]
    // 0xd89798: mov             x2, x1
    // 0xd8979c: r1 = Function '<anonymous closure>':.
    //     0xd8979c: add             x1, PP, #0x22, lsl #12  ; [pp+0x220b0] AnonymousClosure: (0xd897b4), in [package:flutter/src/material/page_transitions_theme.dart] ZoomPageTransitionsBuilder::delegatedTransition (0xd89774)
    //     0xd897a0: ldr             x1, [x1, #0xb0]
    // 0xd897a4: r0 = AllocateClosure()
    //     0xd897a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xd897a8: LeaveFrame
    //     0xd897a8: mov             SP, fp
    //     0xd897ac: ldp             fp, lr, [SP], #0x10
    // 0xd897b0: ret
    //     0xd897b0: ret             
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, Animation<double>, Animation<double>, bool, Widget?) {
    // ** addr: 0xd897b4, size: 0x50
    // 0xd897b4: EnterFrame
    //     0xd897b4: stp             fp, lr, [SP, #-0x10]!
    //     0xd897b8: mov             fp, SP
    // 0xd897bc: CheckStackOverflow
    //     0xd897bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd897c0: cmp             SP, x16
    //     0xd897c4: b.ls            #0xd897fc
    // 0xd897c8: ldr             x0, [fp, #0x18]
    // 0xd897cc: tbnz            w0, #4, #0xd897d8
    // 0xd897d0: r5 = true
    //     0xd897d0: add             x5, NULL, #0x20  ; true
    // 0xd897d4: b               #0xd897dc
    // 0xd897d8: r5 = false
    //     0xd897d8: add             x5, NULL, #0x30  ; false
    // 0xd897dc: ldr             x1, [fp, #0x30]
    // 0xd897e0: ldr             x2, [fp, #0x20]
    // 0xd897e4: ldr             x3, [fp, #0x10]
    // 0xd897e8: r6 = Null
    //     0xd897e8: mov             x6, NULL
    // 0xd897ec: r0 = _snapshotAwareDelegatedTransition()
    //     0xd897ec: bl              #0xa9fc24  ; [package:flutter/src/material/page_transitions_theme.dart] ZoomPageTransitionsBuilder::_snapshotAwareDelegatedTransition
    // 0xd897f0: LeaveFrame
    //     0xd897f0: mov             SP, fp
    //     0xd897f4: ldp             fp, lr, [SP], #0x10
    // 0xd897f8: ret
    //     0xd897f8: ret             
    // 0xd897fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd897fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd89800: b               #0xd897c8
  }
}

// class id: 3661, size: 0x40, field offset: 0x24
class _ZoomExitTransitionPainter extends SnapshotPainter {

  [closure] void _onStatusChange(dynamic, dynamic) {
    // ** addr: 0x936d30, size: 0x3c
    // 0x936d30: EnterFrame
    //     0x936d30: stp             fp, lr, [SP, #-0x10]!
    //     0x936d34: mov             fp, SP
    // 0x936d38: ldr             x0, [fp, #0x18]
    // 0x936d3c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x936d3c: ldur            w1, [x0, #0x17]
    // 0x936d40: DecompressPointer r1
    //     0x936d40: add             x1, x1, HEAP, lsl #32
    // 0x936d44: CheckStackOverflow
    //     0x936d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x936d48: cmp             SP, x16
    //     0x936d4c: b.ls            #0x936d64
    // 0x936d50: r0 = notifyListeners()
    //     0x936d50: bl              #0x6462b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners
    // 0x936d54: r0 = Null
    //     0x936d54: mov             x0, NULL
    // 0x936d58: LeaveFrame
    //     0x936d58: mov             SP, fp
    //     0x936d5c: ldp             fp, lr, [SP], #0x10
    // 0x936d60: ret
    //     0x936d60: ret             
    // 0x936d64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x936d64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x936d68: b               #0x936d50
  }
  _ _ZoomExitTransitionPainter(/* No info */) {
    // ** addr: 0x9376c0, size: 0x208
    // 0x9376c0: EnterFrame
    //     0x9376c0: stp             fp, lr, [SP, #-0x10]!
    //     0x9376c4: mov             fp, SP
    // 0x9376c8: AllocStack(0x30)
    //     0x9376c8: sub             SP, SP, #0x30
    // 0x9376cc: SetupParameters(_ZoomExitTransitionPainter this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r0, fp-0x28 */)
    //     0x9376cc: stur            x1, [fp, #-8]
    //     0x9376d0: mov             x16, x3
    //     0x9376d4: mov             x3, x1
    //     0x9376d8: mov             x1, x16
    //     0x9376dc: mov             x0, x6
    //     0x9376e0: stur            x2, [fp, #-0x10]
    //     0x9376e4: stur            x1, [fp, #-0x18]
    //     0x9376e8: stur            x5, [fp, #-0x20]
    //     0x9376ec: stur            x6, [fp, #-0x28]
    // 0x9376f0: CheckStackOverflow
    //     0x9376f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9376f4: cmp             SP, x16
    //     0x9376f8: b.ls            #0x9378c0
    // 0x9376fc: r0 = Matrix4()
    //     0x9376fc: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0x937700: r4 = 32
    //     0x937700: movz            x4, #0x20
    // 0x937704: stur            x0, [fp, #-0x30]
    // 0x937708: r0 = AllocateFloat64Array()
    //     0x937708: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0x93770c: mov             x1, x0
    // 0x937710: ldur            x0, [fp, #-0x30]
    // 0x937714: StoreField: r0->field_7 = r1
    //     0x937714: stur            w1, [x0, #7]
    // 0x937718: ldur            x2, [fp, #-8]
    // 0x93771c: StoreField: r2->field_33 = r0
    //     0x93771c: stur            w0, [x2, #0x33]
    //     0x937720: ldurb           w16, [x2, #-1]
    //     0x937724: ldurb           w17, [x0, #-1]
    //     0x937728: and             x16, x17, x16, lsr #2
    //     0x93772c: tst             x16, HEAP, lsr #32
    //     0x937730: b.eq            #0x937738
    //     0x937734: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x937738: r1 = <OpacityLayer>
    //     0x937738: add             x1, PP, #0x24, lsl #12  ; [pp+0x24f58] TypeArguments: <OpacityLayer>
    //     0x93773c: ldr             x1, [x1, #0xf58]
    // 0x937740: r0 = LayerHandle()
    //     0x937740: bl              #0x6d28c4  ; AllocateLayerHandleStub -> LayerHandle<X0 bound Layer> (size=0x10)
    // 0x937744: ldur            x2, [fp, #-8]
    // 0x937748: StoreField: r2->field_37 = r0
    //     0x937748: stur            w0, [x2, #0x37]
    //     0x93774c: ldurb           w16, [x2, #-1]
    //     0x937750: ldurb           w17, [x0, #-1]
    //     0x937754: and             x16, x17, x16, lsr #2
    //     0x937758: tst             x16, HEAP, lsr #32
    //     0x93775c: b.eq            #0x937764
    //     0x937760: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x937764: r1 = <TransformLayer>
    //     0x937764: add             x1, PP, #0x24, lsl #12  ; [pp+0x24f60] TypeArguments: <TransformLayer>
    //     0x937768: ldr             x1, [x1, #0xf60]
    // 0x93776c: r0 = LayerHandle()
    //     0x93776c: bl              #0x6d28c4  ; AllocateLayerHandleStub -> LayerHandle<X0 bound Layer> (size=0x10)
    // 0x937770: ldur            x2, [fp, #-8]
    // 0x937774: StoreField: r2->field_3b = r0
    //     0x937774: stur            w0, [x2, #0x3b]
    //     0x937778: ldurb           w16, [x2, #-1]
    //     0x93777c: ldurb           w17, [x0, #-1]
    //     0x937780: and             x16, x17, x16, lsr #2
    //     0x937784: tst             x16, HEAP, lsr #32
    //     0x937788: b.eq            #0x937790
    //     0x93778c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x937790: ldur            x0, [fp, #-0x20]
    // 0x937794: StoreField: r2->field_23 = r0
    //     0x937794: stur            w0, [x2, #0x23]
    // 0x937798: ldur            x0, [fp, #-0x28]
    // 0x93779c: StoreField: r2->field_27 = r0
    //     0x93779c: stur            w0, [x2, #0x27]
    //     0x9377a0: ldurb           w16, [x2, #-1]
    //     0x9377a4: ldurb           w17, [x0, #-1]
    //     0x9377a8: and             x16, x17, x16, lsr #2
    //     0x9377ac: tst             x16, HEAP, lsr #32
    //     0x9377b0: b.eq            #0x9377b8
    //     0x9377b4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9377b8: ldur            x0, [fp, #-0x18]
    // 0x9377bc: StoreField: r2->field_2b = r0
    //     0x9377bc: stur            w0, [x2, #0x2b]
    //     0x9377c0: ldurb           w16, [x2, #-1]
    //     0x9377c4: ldurb           w17, [x0, #-1]
    //     0x9377c8: and             x16, x17, x16, lsr #2
    //     0x9377cc: tst             x16, HEAP, lsr #32
    //     0x9377d0: b.eq            #0x9377d8
    //     0x9377d4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9377d8: ldur            x0, [fp, #-0x10]
    // 0x9377dc: StoreField: r2->field_2f = r0
    //     0x9377dc: stur            w0, [x2, #0x2f]
    //     0x9377e0: ldurb           w16, [x2, #-1]
    //     0x9377e4: ldurb           w17, [x0, #-1]
    //     0x9377e8: and             x16, x17, x16, lsr #2
    //     0x9377ec: tst             x16, HEAP, lsr #32
    //     0x9377f0: b.eq            #0x9377f8
    //     0x9377f4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9377f8: StoreField: r2->field_7 = rZR
    //     0x9377f8: stur            xzr, [x2, #7]
    // 0x9377fc: StoreField: r2->field_13 = rZR
    //     0x9377fc: stur            xzr, [x2, #0x13]
    // 0x937800: StoreField: r2->field_1b = rZR
    //     0x937800: stur            xzr, [x2, #0x1b]
    // 0x937804: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0x937804: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x937808: ldr             x0, [x0, #0xca8]
    //     0x93780c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x937810: cmp             w0, w16
    //     0x937814: b.ne            #0x937820
    //     0x937818: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0x93781c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x937820: ldur            x3, [fp, #-8]
    // 0x937824: StoreField: r3->field_f = r0
    //     0x937824: stur            w0, [x3, #0xf]
    //     0x937828: ldurb           w16, [x3, #-1]
    //     0x93782c: ldurb           w17, [x0, #-1]
    //     0x937830: and             x16, x17, x16, lsr #2
    //     0x937834: tst             x16, HEAP, lsr #32
    //     0x937838: b.eq            #0x937840
    //     0x93783c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x937840: mov             x2, x3
    // 0x937844: r1 = Function 'notifyListeners':.
    //     0x937844: ldr             x1, [PP, #0x2608]  ; [pp+0x2608] AnonymousClosure: (0x646848), in [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners (0x6462b8)
    // 0x937848: r0 = AllocateClosure()
    //     0x937848: bl              #0xec1630  ; AllocateClosureStub
    // 0x93784c: ldur            x1, [fp, #-0x28]
    // 0x937850: mov             x2, x0
    // 0x937854: stur            x0, [fp, #-0x20]
    // 0x937858: r0 = addListener()
    //     0x937858: bl              #0xa52138  ; [package:flutter/src/animation/tween.dart] __AnimatedEvaluation&Animation&AnimationWithParentMixin::addListener
    // 0x93785c: ldur            x1, [fp, #-0x18]
    // 0x937860: r0 = LoadClassIdInstr(r1)
    //     0x937860: ldur            x0, [x1, #-1]
    //     0x937864: ubfx            x0, x0, #0xc, #0x14
    // 0x937868: ldur            x2, [fp, #-0x20]
    // 0x93786c: r0 = GDT[cid_x0 + 0xc407]()
    //     0x93786c: movz            x17, #0xc407
    //     0x937870: add             lr, x0, x17
    //     0x937874: ldr             lr, [x21, lr, lsl #3]
    //     0x937878: blr             lr
    // 0x93787c: ldur            x2, [fp, #-8]
    // 0x937880: r1 = Function '_onStatusChange@579490068':.
    //     0x937880: add             x1, PP, #0x24, lsl #12  ; [pp+0x24f28] AnonymousClosure: (0x936d30), of [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionPainter
    //     0x937884: ldr             x1, [x1, #0xf28]
    // 0x937888: r0 = AllocateClosure()
    //     0x937888: bl              #0xec1630  ; AllocateClosureStub
    // 0x93788c: ldur            x1, [fp, #-0x10]
    // 0x937890: r2 = LoadClassIdInstr(r1)
    //     0x937890: ldur            x2, [x1, #-1]
    //     0x937894: ubfx            x2, x2, #0xc, #0x14
    // 0x937898: mov             x16, x0
    // 0x93789c: mov             x0, x2
    // 0x9378a0: mov             x2, x16
    // 0x9378a4: r0 = GDT[cid_x0 + 0x7a1]()
    //     0x9378a4: add             lr, x0, #0x7a1
    //     0x9378a8: ldr             lr, [x21, lr, lsl #3]
    //     0x9378ac: blr             lr
    // 0x9378b0: r0 = Null
    //     0x9378b0: mov             x0, NULL
    // 0x9378b4: LeaveFrame
    //     0x9378b4: mov             SP, fp
    //     0x9378b8: ldp             fp, lr, [SP], #0x10
    // 0x9378bc: ret
    //     0x9378bc: ret             
    // 0x9378c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9378c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9378c4: b               #0x9376fc
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa87498, size: 0xfc
    // 0xa87498: EnterFrame
    //     0xa87498: stp             fp, lr, [SP, #-0x10]!
    //     0xa8749c: mov             fp, SP
    // 0xa874a0: AllocStack(0x10)
    //     0xa874a0: sub             SP, SP, #0x10
    // 0xa874a4: SetupParameters(_ZoomExitTransitionPainter this /* r1 => r0, fp-0x8 */)
    //     0xa874a4: mov             x0, x1
    //     0xa874a8: stur            x1, [fp, #-8]
    // 0xa874ac: CheckStackOverflow
    //     0xa874ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa874b0: cmp             SP, x16
    //     0xa874b4: b.ls            #0xa8758c
    // 0xa874b8: LoadField: r1 = r0->field_37
    //     0xa874b8: ldur            w1, [x0, #0x37]
    // 0xa874bc: DecompressPointer r1
    //     0xa874bc: add             x1, x1, HEAP, lsl #32
    // 0xa874c0: r2 = Null
    //     0xa874c0: mov             x2, NULL
    // 0xa874c4: r0 = layer=()
    //     0xa874c4: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0xa874c8: ldur            x0, [fp, #-8]
    // 0xa874cc: LoadField: r1 = r0->field_3b
    //     0xa874cc: ldur            w1, [x0, #0x3b]
    // 0xa874d0: DecompressPointer r1
    //     0xa874d0: add             x1, x1, HEAP, lsl #32
    // 0xa874d4: r2 = Null
    //     0xa874d4: mov             x2, NULL
    // 0xa874d8: r0 = layer=()
    //     0xa874d8: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0xa874dc: ldur            x0, [fp, #-8]
    // 0xa874e0: LoadField: r3 = r0->field_27
    //     0xa874e0: ldur            w3, [x0, #0x27]
    // 0xa874e4: DecompressPointer r3
    //     0xa874e4: add             x3, x3, HEAP, lsl #32
    // 0xa874e8: mov             x2, x0
    // 0xa874ec: stur            x3, [fp, #-0x10]
    // 0xa874f0: r1 = Function 'notifyListeners':.
    //     0xa874f0: ldr             x1, [PP, #0x2608]  ; [pp+0x2608] AnonymousClosure: (0x646848), in [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners (0x6462b8)
    // 0xa874f4: r0 = AllocateClosure()
    //     0xa874f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa874f8: ldur            x1, [fp, #-0x10]
    // 0xa874fc: mov             x2, x0
    // 0xa87500: stur            x0, [fp, #-0x10]
    // 0xa87504: r0 = removeListener()
    //     0xa87504: bl              #0xa59c94  ; [package:flutter/src/animation/tween.dart] __AnimatedEvaluation&Animation&AnimationWithParentMixin::removeListener
    // 0xa87508: ldur            x3, [fp, #-8]
    // 0xa8750c: LoadField: r1 = r3->field_2b
    //     0xa8750c: ldur            w1, [x3, #0x2b]
    // 0xa87510: DecompressPointer r1
    //     0xa87510: add             x1, x1, HEAP, lsl #32
    // 0xa87514: r0 = LoadClassIdInstr(r1)
    //     0xa87514: ldur            x0, [x1, #-1]
    //     0xa87518: ubfx            x0, x0, #0xc, #0x14
    // 0xa8751c: ldur            x2, [fp, #-0x10]
    // 0xa87520: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa87520: movz            x17, #0xbf5c
    //     0xa87524: add             lr, x0, x17
    //     0xa87528: ldr             lr, [x21, lr, lsl #3]
    //     0xa8752c: blr             lr
    // 0xa87530: ldur            x0, [fp, #-8]
    // 0xa87534: LoadField: r3 = r0->field_2f
    //     0xa87534: ldur            w3, [x0, #0x2f]
    // 0xa87538: DecompressPointer r3
    //     0xa87538: add             x3, x3, HEAP, lsl #32
    // 0xa8753c: mov             x2, x0
    // 0xa87540: stur            x3, [fp, #-0x10]
    // 0xa87544: r1 = Function '_onStatusChange@579490068':.
    //     0xa87544: add             x1, PP, #0x24, lsl #12  ; [pp+0x24f28] AnonymousClosure: (0x936d30), of [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionPainter
    //     0xa87548: ldr             x1, [x1, #0xf28]
    // 0xa8754c: r0 = AllocateClosure()
    //     0xa8754c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa87550: ldur            x1, [fp, #-0x10]
    // 0xa87554: r2 = LoadClassIdInstr(r1)
    //     0xa87554: ldur            x2, [x1, #-1]
    //     0xa87558: ubfx            x2, x2, #0xc, #0x14
    // 0xa8755c: mov             x16, x0
    // 0xa87560: mov             x0, x2
    // 0xa87564: mov             x2, x16
    // 0xa87568: r0 = GDT[cid_x0 + 0x30c]()
    //     0xa87568: add             lr, x0, #0x30c
    //     0xa8756c: ldr             lr, [x21, lr, lsl #3]
    //     0xa87570: blr             lr
    // 0xa87574: ldur            x1, [fp, #-8]
    // 0xa87578: r0 = dispose()
    //     0xa87578: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0xa8757c: r0 = Null
    //     0xa8757c: mov             x0, NULL
    // 0xa87580: LeaveFrame
    //     0xa87580: mov             SP, fp
    //     0xa87584: ldp             fp, lr, [SP], #0x10
    // 0xa87588: ret
    //     0xa87588: ret             
    // 0xa8758c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8758c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa87590: b               #0xa874b8
  }
  _ shouldRepaint(/* No info */) {
    // ** addr: 0xd844d0, size: 0x17c
    // 0xd844d0: EnterFrame
    //     0xd844d0: stp             fp, lr, [SP, #-0x10]!
    //     0xd844d4: mov             fp, SP
    // 0xd844d8: AllocStack(0x18)
    //     0xd844d8: sub             SP, SP, #0x18
    // 0xd844dc: SetupParameters(_ZoomExitTransitionPainter this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xd844dc: mov             x4, x1
    //     0xd844e0: mov             x3, x2
    //     0xd844e4: stur            x1, [fp, #-8]
    //     0xd844e8: stur            x2, [fp, #-0x10]
    // 0xd844ec: CheckStackOverflow
    //     0xd844ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd844f0: cmp             SP, x16
    //     0xd844f4: b.ls            #0xd84644
    // 0xd844f8: mov             x0, x3
    // 0xd844fc: r2 = Null
    //     0xd844fc: mov             x2, NULL
    // 0xd84500: r1 = Null
    //     0xd84500: mov             x1, NULL
    // 0xd84504: r4 = 60
    //     0xd84504: movz            x4, #0x3c
    // 0xd84508: branchIfSmi(r0, 0xd84514)
    //     0xd84508: tbz             w0, #0, #0xd84514
    // 0xd8450c: r4 = LoadClassIdInstr(r0)
    //     0xd8450c: ldur            x4, [x0, #-1]
    //     0xd84510: ubfx            x4, x4, #0xc, #0x14
    // 0xd84514: cmp             x4, #0xe4d
    // 0xd84518: b.eq            #0xd84530
    // 0xd8451c: r8 = _ZoomExitTransitionPainter
    //     0xd8451c: add             x8, PP, #0x39, lsl #12  ; [pp+0x39810] Type: _ZoomExitTransitionPainter
    //     0xd84520: ldr             x8, [x8, #0x810]
    // 0xd84524: r3 = Null
    //     0xd84524: add             x3, PP, #0x39, lsl #12  ; [pp+0x39818] Null
    //     0xd84528: ldr             x3, [x3, #0x818]
    // 0xd8452c: r0 = DefaultTypeTest()
    //     0xd8452c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xd84530: ldur            x2, [fp, #-0x10]
    // 0xd84534: LoadField: r0 = r2->field_23
    //     0xd84534: ldur            w0, [x2, #0x23]
    // 0xd84538: DecompressPointer r0
    //     0xd84538: add             x0, x0, HEAP, lsl #32
    // 0xd8453c: ldur            x3, [fp, #-8]
    // 0xd84540: LoadField: r1 = r3->field_23
    //     0xd84540: ldur            w1, [x3, #0x23]
    // 0xd84544: DecompressPointer r1
    //     0xd84544: add             x1, x1, HEAP, lsl #32
    // 0xd84548: cmp             w0, w1
    // 0xd8454c: b.ne            #0xd845bc
    // 0xd84550: LoadField: r1 = r2->field_2b
    //     0xd84550: ldur            w1, [x2, #0x2b]
    // 0xd84554: DecompressPointer r1
    //     0xd84554: add             x1, x1, HEAP, lsl #32
    // 0xd84558: r0 = LoadClassIdInstr(r1)
    //     0xd84558: ldur            x0, [x1, #-1]
    //     0xd8455c: ubfx            x0, x0, #0xc, #0x14
    // 0xd84560: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xd84560: movz            x17, #0x276f
    //     0xd84564: movk            x17, #0x1, lsl #16
    //     0xd84568: add             lr, x0, x17
    //     0xd8456c: ldr             lr, [x21, lr, lsl #3]
    //     0xd84570: blr             lr
    // 0xd84574: mov             x3, x0
    // 0xd84578: ldur            x2, [fp, #-8]
    // 0xd8457c: stur            x3, [fp, #-0x18]
    // 0xd84580: LoadField: r1 = r2->field_2b
    //     0xd84580: ldur            w1, [x2, #0x2b]
    // 0xd84584: DecompressPointer r1
    //     0xd84584: add             x1, x1, HEAP, lsl #32
    // 0xd84588: r0 = LoadClassIdInstr(r1)
    //     0xd84588: ldur            x0, [x1, #-1]
    //     0xd8458c: ubfx            x0, x0, #0xc, #0x14
    // 0xd84590: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xd84590: movz            x17, #0x276f
    //     0xd84594: movk            x17, #0x1, lsl #16
    //     0xd84598: add             lr, x0, x17
    //     0xd8459c: ldr             lr, [x21, lr, lsl #3]
    //     0xd845a0: blr             lr
    // 0xd845a4: mov             x1, x0
    // 0xd845a8: ldur            x0, [fp, #-0x18]
    // 0xd845ac: LoadField: d0 = r0->field_7
    //     0xd845ac: ldur            d0, [x0, #7]
    // 0xd845b0: LoadField: d1 = r1->field_7
    //     0xd845b0: ldur            d1, [x1, #7]
    // 0xd845b4: fcmp            d0, d1
    // 0xd845b8: b.eq            #0xd845c4
    // 0xd845bc: r0 = true
    //     0xd845bc: add             x0, NULL, #0x20  ; true
    // 0xd845c0: b               #0xd84638
    // 0xd845c4: ldur            x0, [fp, #-8]
    // 0xd845c8: ldur            x1, [fp, #-0x10]
    // 0xd845cc: LoadField: r2 = r1->field_27
    //     0xd845cc: ldur            w2, [x1, #0x27]
    // 0xd845d0: DecompressPointer r2
    //     0xd845d0: add             x2, x2, HEAP, lsl #32
    // 0xd845d4: LoadField: r1 = r2->field_f
    //     0xd845d4: ldur            w1, [x2, #0xf]
    // 0xd845d8: DecompressPointer r1
    //     0xd845d8: add             x1, x1, HEAP, lsl #32
    // 0xd845dc: LoadField: r3 = r2->field_b
    //     0xd845dc: ldur            w3, [x2, #0xb]
    // 0xd845e0: DecompressPointer r3
    //     0xd845e0: add             x3, x3, HEAP, lsl #32
    // 0xd845e4: mov             x2, x3
    // 0xd845e8: r0 = evaluate()
    //     0xd845e8: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xd845ec: mov             x3, x0
    // 0xd845f0: ldur            x0, [fp, #-8]
    // 0xd845f4: stur            x3, [fp, #-0x10]
    // 0xd845f8: LoadField: r1 = r0->field_27
    //     0xd845f8: ldur            w1, [x0, #0x27]
    // 0xd845fc: DecompressPointer r1
    //     0xd845fc: add             x1, x1, HEAP, lsl #32
    // 0xd84600: LoadField: r0 = r1->field_f
    //     0xd84600: ldur            w0, [x1, #0xf]
    // 0xd84604: DecompressPointer r0
    //     0xd84604: add             x0, x0, HEAP, lsl #32
    // 0xd84608: LoadField: r2 = r1->field_b
    //     0xd84608: ldur            w2, [x1, #0xb]
    // 0xd8460c: DecompressPointer r2
    //     0xd8460c: add             x2, x2, HEAP, lsl #32
    // 0xd84610: mov             x1, x0
    // 0xd84614: r0 = evaluate()
    //     0xd84614: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xd84618: ldur            x1, [fp, #-0x10]
    // 0xd8461c: LoadField: d0 = r1->field_7
    //     0xd8461c: ldur            d0, [x1, #7]
    // 0xd84620: LoadField: d1 = r0->field_7
    //     0xd84620: ldur            d1, [x0, #7]
    // 0xd84624: fcmp            d0, d1
    // 0xd84628: r16 = true
    //     0xd84628: add             x16, NULL, #0x20  ; true
    // 0xd8462c: r17 = false
    //     0xd8462c: add             x17, NULL, #0x30  ; false
    // 0xd84630: csel            x1, x16, x17, ne
    // 0xd84634: mov             x0, x1
    // 0xd84638: LeaveFrame
    //     0xd84638: mov             SP, fp
    //     0xd8463c: ldp             fp, lr, [SP], #0x10
    // 0xd84640: ret
    //     0xd84640: ret             
    // 0xd84644: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd84644: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd84648: b               #0xd844f8
  }
  _ paintSnapshot(/* No info */) {
    // ** addr: 0xd84d20, size: 0xc4
    // 0xd84d20: EnterFrame
    //     0xd84d20: stp             fp, lr, [SP, #-0x10]!
    //     0xd84d24: mov             fp, SP
    // 0xd84d28: AllocStack(0x28)
    //     0xd84d28: sub             SP, SP, #0x28
    // 0xd84d2c: SetupParameters(_ZoomExitTransitionPainter this /* r1 => r6, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r6 => r0, fp-0x18 */, dynamic _ /* d0 => d2, fp-0x28 */)
    //     0xd84d2c: mov             x0, x6
    //     0xd84d30: stur            x6, [fp, #-0x18]
    //     0xd84d34: mov             x6, x1
    //     0xd84d38: mov             x4, x2
    //     0xd84d3c: mov             v2.16b, v0.16b
    //     0xd84d40: stur            x1, [fp, #-8]
    //     0xd84d44: stur            x2, [fp, #-0x10]
    //     0xd84d48: stur            d0, [fp, #-0x28]
    // 0xd84d4c: CheckStackOverflow
    //     0xd84d4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd84d50: cmp             SP, x16
    //     0xd84d54: b.ls            #0xd84ddc
    // 0xd84d58: LoadField: r1 = r6->field_27
    //     0xd84d58: ldur            w1, [x6, #0x27]
    // 0xd84d5c: DecompressPointer r1
    //     0xd84d5c: add             x1, x1, HEAP, lsl #32
    // 0xd84d60: LoadField: r2 = r1->field_f
    //     0xd84d60: ldur            w2, [x1, #0xf]
    // 0xd84d64: DecompressPointer r2
    //     0xd84d64: add             x2, x2, HEAP, lsl #32
    // 0xd84d68: LoadField: r3 = r1->field_b
    //     0xd84d68: ldur            w3, [x1, #0xb]
    // 0xd84d6c: DecompressPointer r3
    //     0xd84d6c: add             x3, x3, HEAP, lsl #32
    // 0xd84d70: mov             x1, x2
    // 0xd84d74: mov             x2, x3
    // 0xd84d78: r0 = evaluate()
    //     0xd84d78: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xd84d7c: mov             x2, x0
    // 0xd84d80: ldur            x0, [fp, #-8]
    // 0xd84d84: stur            x2, [fp, #-0x20]
    // 0xd84d88: LoadField: r1 = r0->field_2b
    //     0xd84d88: ldur            w1, [x0, #0x2b]
    // 0xd84d8c: DecompressPointer r1
    //     0xd84d8c: add             x1, x1, HEAP, lsl #32
    // 0xd84d90: r0 = LoadClassIdInstr(r1)
    //     0xd84d90: ldur            x0, [x1, #-1]
    //     0xd84d94: ubfx            x0, x0, #0xc, #0x14
    // 0xd84d98: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xd84d98: movz            x17, #0x276f
    //     0xd84d9c: movk            x17, #0x1, lsl #16
    //     0xd84da0: add             lr, x0, x17
    //     0xd84da4: ldr             lr, [x21, lr, lsl #3]
    //     0xd84da8: blr             lr
    // 0xd84dac: mov             x1, x0
    // 0xd84db0: ldur            x0, [fp, #-0x20]
    // 0xd84db4: LoadField: d0 = r0->field_7
    //     0xd84db4: ldur            d0, [x0, #7]
    // 0xd84db8: LoadField: d1 = r1->field_7
    //     0xd84db8: ldur            d1, [x1, #7]
    // 0xd84dbc: ldur            x1, [fp, #-0x10]
    // 0xd84dc0: ldur            x2, [fp, #-0x18]
    // 0xd84dc4: ldur            d2, [fp, #-0x28]
    // 0xd84dc8: r0 = _drawImageScaledAndCentered()
    //     0xd84dc8: bl              #0xd8493c  ; [package:flutter/src/material/page_transitions_theme.dart] ::_drawImageScaledAndCentered
    // 0xd84dcc: r0 = Null
    //     0xd84dcc: mov             x0, NULL
    // 0xd84dd0: LeaveFrame
    //     0xd84dd0: mov             SP, fp
    //     0xd84dd4: ldp             fp, lr, [SP], #0x10
    // 0xd84dd8: ret
    //     0xd84dd8: ret             
    // 0xd84ddc: r0 = StackOverflowSharedWithFPURegs()
    //     0xd84ddc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd84de0: b               #0xd84d58
  }
  _ paint(/* No info */) {
    // ** addr: 0xd87bb0, size: 0x15c
    // 0xd87bb0: EnterFrame
    //     0xd87bb0: stp             fp, lr, [SP, #-0x10]!
    //     0xd87bb4: mov             fp, SP
    // 0xd87bb8: AllocStack(0x30)
    //     0xd87bb8: sub             SP, SP, #0x30
    // 0xd87bbc: SetupParameters(_ZoomExitTransitionPainter this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0xd87bbc: mov             x0, x2
    //     0xd87bc0: stur            x2, [fp, #-0x10]
    //     0xd87bc4: mov             x2, x5
    //     0xd87bc8: stur            x1, [fp, #-8]
    //     0xd87bcc: stur            x3, [fp, #-0x18]
    //     0xd87bd0: stur            x5, [fp, #-0x20]
    //     0xd87bd4: stur            x6, [fp, #-0x28]
    // 0xd87bd8: CheckStackOverflow
    //     0xd87bd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd87bdc: cmp             SP, x16
    //     0xd87be0: b.ls            #0xd87d04
    // 0xd87be4: r1 = 2
    //     0xd87be4: movz            x1, #0x2
    // 0xd87be8: r0 = AllocateContext()
    //     0xd87be8: bl              #0xec126c  ; AllocateContextStub
    // 0xd87bec: mov             x3, x0
    // 0xd87bf0: ldur            x2, [fp, #-8]
    // 0xd87bf4: stur            x3, [fp, #-0x30]
    // 0xd87bf8: StoreField: r3->field_f = r2
    //     0xd87bf8: stur            w2, [x3, #0xf]
    // 0xd87bfc: ldur            x0, [fp, #-0x28]
    // 0xd87c00: StoreField: r3->field_13 = r0
    //     0xd87c00: stur            w0, [x3, #0x13]
    // 0xd87c04: LoadField: r1 = r2->field_2f
    //     0xd87c04: ldur            w1, [x2, #0x2f]
    // 0xd87c08: DecompressPointer r1
    //     0xd87c08: add             x1, x1, HEAP, lsl #32
    // 0xd87c0c: r0 = LoadClassIdInstr(r1)
    //     0xd87c0c: ldur            x0, [x1, #-1]
    //     0xd87c10: ubfx            x0, x0, #0xc, #0x14
    // 0xd87c14: r0 = GDT[cid_x0 + 0xd2a]()
    //     0xd87c14: add             lr, x0, #0xd2a
    //     0xd87c18: ldr             lr, [x21, lr, lsl #3]
    //     0xd87c1c: blr             lr
    // 0xd87c20: tbz             w0, #4, #0xd87c58
    // 0xd87c24: ldur            x0, [fp, #-0x30]
    // 0xd87c28: LoadField: r1 = r0->field_13
    //     0xd87c28: ldur            w1, [x0, #0x13]
    // 0xd87c2c: DecompressPointer r1
    //     0xd87c2c: add             x1, x1, HEAP, lsl #32
    // 0xd87c30: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xd87c30: ldur            w0, [x1, #0x17]
    // 0xd87c34: DecompressPointer r0
    //     0xd87c34: add             x0, x0, HEAP, lsl #32
    // 0xd87c38: mov             x1, x0
    // 0xd87c3c: ldur            x2, [fp, #-0x10]
    // 0xd87c40: ldur            x3, [fp, #-0x18]
    // 0xd87c44: r0 = paint()
    //     0xd87c44: bl              #0x7928f0  ; [package:flutter/src/rendering/proxy_box.dart] _RenderProxyBox&RenderBox&RenderObjectWithChildMixin&RenderProxyBoxMixin::paint
    // 0xd87c48: r0 = Null
    //     0xd87c48: mov             x0, NULL
    // 0xd87c4c: LeaveFrame
    //     0xd87c4c: mov             SP, fp
    //     0xd87c50: ldp             fp, lr, [SP], #0x10
    // 0xd87c54: ret
    //     0xd87c54: ret             
    // 0xd87c58: ldur            x3, [fp, #-8]
    // 0xd87c5c: ldur            x0, [fp, #-0x30]
    // 0xd87c60: LoadField: r4 = r3->field_33
    //     0xd87c60: ldur            w4, [x3, #0x33]
    // 0xd87c64: DecompressPointer r4
    //     0xd87c64: add             x4, x4, HEAP, lsl #32
    // 0xd87c68: stur            x4, [fp, #-0x28]
    // 0xd87c6c: LoadField: r1 = r3->field_27
    //     0xd87c6c: ldur            w1, [x3, #0x27]
    // 0xd87c70: DecompressPointer r1
    //     0xd87c70: add             x1, x1, HEAP, lsl #32
    // 0xd87c74: LoadField: r2 = r1->field_f
    //     0xd87c74: ldur            w2, [x1, #0xf]
    // 0xd87c78: DecompressPointer r2
    //     0xd87c78: add             x2, x2, HEAP, lsl #32
    // 0xd87c7c: LoadField: r5 = r1->field_b
    //     0xd87c7c: ldur            w5, [x1, #0xb]
    // 0xd87c80: DecompressPointer r5
    //     0xd87c80: add             x5, x5, HEAP, lsl #32
    // 0xd87c84: mov             x1, x2
    // 0xd87c88: mov             x2, x5
    // 0xd87c8c: r0 = evaluate()
    //     0xd87c8c: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xd87c90: LoadField: d0 = r0->field_7
    //     0xd87c90: ldur            d0, [x0, #7]
    // 0xd87c94: ldur            x1, [fp, #-0x28]
    // 0xd87c98: ldur            x2, [fp, #-0x20]
    // 0xd87c9c: r0 = _updateScaledTransform()
    //     0xd87c9c: bl              #0xd87978  ; [package:flutter/src/material/page_transitions_theme.dart] ::_updateScaledTransform
    // 0xd87ca0: ldur            x0, [fp, #-8]
    // 0xd87ca4: LoadField: r3 = r0->field_3b
    //     0xd87ca4: ldur            w3, [x0, #0x3b]
    // 0xd87ca8: DecompressPointer r3
    //     0xd87ca8: add             x3, x3, HEAP, lsl #32
    // 0xd87cac: stur            x3, [fp, #-0x20]
    // 0xd87cb0: LoadField: r7 = r3->field_b
    //     0xd87cb0: ldur            w7, [x3, #0xb]
    // 0xd87cb4: DecompressPointer r7
    //     0xd87cb4: add             x7, x7, HEAP, lsl #32
    // 0xd87cb8: ldur            x2, [fp, #-0x30]
    // 0xd87cbc: stur            x7, [fp, #-8]
    // 0xd87cc0: r1 = Function '<anonymous closure>':.
    //     0xd87cc0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44380] AnonymousClosure: (0xd87d0c), in [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionPainter::paint (0xd87bb0)
    //     0xd87cc4: ldr             x1, [x1, #0x380]
    // 0xd87cc8: r0 = AllocateClosure()
    //     0xd87cc8: bl              #0xec1630  ; AllocateClosureStub
    // 0xd87ccc: ldur            x1, [fp, #-0x10]
    // 0xd87cd0: ldur            x3, [fp, #-0x18]
    // 0xd87cd4: ldur            x5, [fp, #-0x28]
    // 0xd87cd8: mov             x6, x0
    // 0xd87cdc: ldur            x7, [fp, #-8]
    // 0xd87ce0: r2 = true
    //     0xd87ce0: add             x2, NULL, #0x20  ; true
    // 0xd87ce4: r0 = pushTransform()
    //     0xd87ce4: bl              #0x78ffa8  ; [package:flutter/src/rendering/object.dart] PaintingContext::pushTransform
    // 0xd87ce8: ldur            x1, [fp, #-0x20]
    // 0xd87cec: mov             x2, x0
    // 0xd87cf0: r0 = layer=()
    //     0xd87cf0: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0xd87cf4: r0 = Null
    //     0xd87cf4: mov             x0, NULL
    // 0xd87cf8: LeaveFrame
    //     0xd87cf8: mov             SP, fp
    //     0xd87cfc: ldp             fp, lr, [SP], #0x10
    // 0xd87d00: ret
    //     0xd87d00: ret             
    // 0xd87d04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd87d04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd87d08: b               #0xd87be4
  }
  [closure] void <anonymous closure>(dynamic, PaintingContext, Offset) {
    // ** addr: 0xd87d0c, size: 0x13c
    // 0xd87d0c: EnterFrame
    //     0xd87d0c: stp             fp, lr, [SP, #-0x10]!
    //     0xd87d10: mov             fp, SP
    // 0xd87d14: AllocStack(0x10)
    //     0xd87d14: sub             SP, SP, #0x10
    // 0xd87d18: SetupParameters()
    //     0xd87d18: ldr             x0, [fp, #0x20]
    //     0xd87d1c: ldur            w2, [x0, #0x17]
    //     0xd87d20: add             x2, x2, HEAP, lsl #32
    //     0xd87d24: stur            x2, [fp, #-0x10]
    // 0xd87d28: CheckStackOverflow
    //     0xd87d28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd87d2c: cmp             SP, x16
    //     0xd87d30: b.ls            #0xd87e24
    // 0xd87d34: LoadField: r0 = r2->field_f
    //     0xd87d34: ldur            w0, [x2, #0xf]
    // 0xd87d38: DecompressPointer r0
    //     0xd87d38: add             x0, x0, HEAP, lsl #32
    // 0xd87d3c: LoadField: r3 = r0->field_37
    //     0xd87d3c: ldur            w3, [x0, #0x37]
    // 0xd87d40: DecompressPointer r3
    //     0xd87d40: add             x3, x3, HEAP, lsl #32
    // 0xd87d44: stur            x3, [fp, #-8]
    // 0xd87d48: LoadField: r1 = r0->field_2b
    //     0xd87d48: ldur            w1, [x0, #0x2b]
    // 0xd87d4c: DecompressPointer r1
    //     0xd87d4c: add             x1, x1, HEAP, lsl #32
    // 0xd87d50: r0 = LoadClassIdInstr(r1)
    //     0xd87d50: ldur            x0, [x1, #-1]
    //     0xd87d54: ubfx            x0, x0, #0xc, #0x14
    // 0xd87d58: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xd87d58: movz            x17, #0x276f
    //     0xd87d5c: movk            x17, #0x1, lsl #16
    //     0xd87d60: add             lr, x0, x17
    //     0xd87d64: ldr             lr, [x21, lr, lsl #3]
    //     0xd87d68: blr             lr
    // 0xd87d6c: LoadField: d0 = r0->field_7
    //     0xd87d6c: ldur            d0, [x0, #7]
    // 0xd87d70: d1 = 255.000000
    //     0xd87d70: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xd87d74: fmul            d2, d0, d1
    // 0xd87d78: mov             v0.16b, v2.16b
    // 0xd87d7c: stp             fp, lr, [SP, #-0x10]!
    // 0xd87d80: mov             fp, SP
    // 0xd87d84: CallRuntime_LibcRound(double) -> double
    //     0xd87d84: and             SP, SP, #0xfffffffffffffff0
    //     0xd87d88: mov             sp, SP
    //     0xd87d8c: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xd87d90: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xd87d94: blr             x16
    //     0xd87d98: movz            x16, #0x8
    //     0xd87d9c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xd87da0: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xd87da4: sub             sp, x16, #1, lsl #12
    //     0xd87da8: mov             SP, fp
    //     0xd87dac: ldp             fp, lr, [SP], #0x10
    // 0xd87db0: fcmp            d0, d0
    // 0xd87db4: b.vs            #0xd87e2c
    // 0xd87db8: fcvtzs          x0, d0
    // 0xd87dbc: asr             x16, x0, #0x1e
    // 0xd87dc0: cmp             x16, x0, asr #63
    // 0xd87dc4: b.ne            #0xd87e2c
    // 0xd87dc8: lsl             x0, x0, #1
    // 0xd87dcc: ldur            x1, [fp, #-0x10]
    // 0xd87dd0: LoadField: r5 = r1->field_13
    //     0xd87dd0: ldur            w5, [x1, #0x13]
    // 0xd87dd4: DecompressPointer r5
    //     0xd87dd4: add             x5, x5, HEAP, lsl #32
    // 0xd87dd8: LoadField: r2 = r1->field_f
    //     0xd87dd8: ldur            w2, [x1, #0xf]
    // 0xd87ddc: DecompressPointer r2
    //     0xd87ddc: add             x2, x2, HEAP, lsl #32
    // 0xd87de0: LoadField: r1 = r2->field_37
    //     0xd87de0: ldur            w1, [x2, #0x37]
    // 0xd87de4: DecompressPointer r1
    //     0xd87de4: add             x1, x1, HEAP, lsl #32
    // 0xd87de8: LoadField: r6 = r1->field_b
    //     0xd87de8: ldur            w6, [x1, #0xb]
    // 0xd87dec: DecompressPointer r6
    //     0xd87dec: add             x6, x6, HEAP, lsl #32
    // 0xd87df0: r3 = LoadInt32Instr(r0)
    //     0xd87df0: sbfx            x3, x0, #1, #0x1f
    //     0xd87df4: tbz             w0, #0, #0xd87dfc
    //     0xd87df8: ldur            x3, [x0, #7]
    // 0xd87dfc: ldr             x1, [fp, #0x18]
    // 0xd87e00: ldr             x2, [fp, #0x10]
    // 0xd87e04: r0 = pushOpacity()
    //     0xd87e04: bl              #0x78a828  ; [package:flutter/src/rendering/object.dart] PaintingContext::pushOpacity
    // 0xd87e08: ldur            x1, [fp, #-8]
    // 0xd87e0c: mov             x2, x0
    // 0xd87e10: r0 = layer=()
    //     0xd87e10: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0xd87e14: r0 = Null
    //     0xd87e14: mov             x0, NULL
    // 0xd87e18: LeaveFrame
    //     0xd87e18: mov             SP, fp
    //     0xd87e1c: ldp             fp, lr, [SP], #0x10
    // 0xd87e20: ret
    //     0xd87e20: ret             
    // 0xd87e24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd87e24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd87e28: b               #0xd87d34
    // 0xd87e2c: SaveReg d0
    //     0xd87e2c: str             q0, [SP, #-0x10]!
    // 0xd87e30: r0 = 74
    //     0xd87e30: movz            x0, #0x4a
    // 0xd87e34: r30 = DoubleToIntegerStub
    //     0xd87e34: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xd87e38: LoadField: r30 = r30->field_7
    //     0xd87e38: ldur            lr, [lr, #7]
    // 0xd87e3c: blr             lr
    // 0xd87e40: RestoreReg d0
    //     0xd87e40: ldr             q0, [SP], #0x10
    // 0xd87e44: b               #0xd87dcc
  }
}

// class id: 3662, size: 0x44, field offset: 0x24
class _ZoomEnterTransitionPainter extends SnapshotPainter {

  _ _ZoomEnterTransitionPainter(/* No info */) {
    // ** addr: 0x936ad4, size: 0x25c
    // 0x936ad4: EnterFrame
    //     0x936ad4: stp             fp, lr, [SP, #-0x10]!
    //     0x936ad8: mov             fp, SP
    // 0x936adc: AllocStack(0x38)
    //     0x936adc: sub             SP, SP, #0x38
    // 0x936ae0: SetupParameters(_ZoomEnterTransitionPainter this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */, dynamic _ /* r7 => r0, fp-0x30 */)
    //     0x936ae0: mov             x4, x1
    //     0x936ae4: stur            x2, [fp, #-0x10]
    //     0x936ae8: mov             x16, x3
    //     0x936aec: mov             x3, x2
    //     0x936af0: mov             x2, x16
    //     0x936af4: stur            x1, [fp, #-8]
    //     0x936af8: mov             x1, x5
    //     0x936afc: mov             x0, x7
    //     0x936b00: stur            x2, [fp, #-0x18]
    //     0x936b04: stur            x5, [fp, #-0x20]
    //     0x936b08: stur            x6, [fp, #-0x28]
    //     0x936b0c: stur            x7, [fp, #-0x30]
    // 0x936b10: CheckStackOverflow
    //     0x936b10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x936b14: cmp             SP, x16
    //     0x936b18: b.ls            #0x936d28
    // 0x936b1c: r0 = Matrix4()
    //     0x936b1c: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0x936b20: r4 = 32
    //     0x936b20: movz            x4, #0x20
    // 0x936b24: stur            x0, [fp, #-0x38]
    // 0x936b28: r0 = AllocateFloat64Array()
    //     0x936b28: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0x936b2c: mov             x1, x0
    // 0x936b30: ldur            x0, [fp, #-0x38]
    // 0x936b34: StoreField: r0->field_7 = r1
    //     0x936b34: stur            w1, [x0, #7]
    // 0x936b38: ldur            x2, [fp, #-8]
    // 0x936b3c: StoreField: r2->field_37 = r0
    //     0x936b3c: stur            w0, [x2, #0x37]
    //     0x936b40: ldurb           w16, [x2, #-1]
    //     0x936b44: ldurb           w17, [x0, #-1]
    //     0x936b48: and             x16, x17, x16, lsr #2
    //     0x936b4c: tst             x16, HEAP, lsr #32
    //     0x936b50: b.eq            #0x936b58
    //     0x936b54: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x936b58: r1 = <OpacityLayer>
    //     0x936b58: add             x1, PP, #0x24, lsl #12  ; [pp+0x24f58] TypeArguments: <OpacityLayer>
    //     0x936b5c: ldr             x1, [x1, #0xf58]
    // 0x936b60: r0 = LayerHandle()
    //     0x936b60: bl              #0x6d28c4  ; AllocateLayerHandleStub -> LayerHandle<X0 bound Layer> (size=0x10)
    // 0x936b64: ldur            x2, [fp, #-8]
    // 0x936b68: StoreField: r2->field_3b = r0
    //     0x936b68: stur            w0, [x2, #0x3b]
    //     0x936b6c: ldurb           w16, [x2, #-1]
    //     0x936b70: ldurb           w17, [x0, #-1]
    //     0x936b74: and             x16, x17, x16, lsr #2
    //     0x936b78: tst             x16, HEAP, lsr #32
    //     0x936b7c: b.eq            #0x936b84
    //     0x936b80: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x936b84: r1 = <TransformLayer>
    //     0x936b84: add             x1, PP, #0x24, lsl #12  ; [pp+0x24f60] TypeArguments: <TransformLayer>
    //     0x936b88: ldr             x1, [x1, #0xf60]
    // 0x936b8c: r0 = LayerHandle()
    //     0x936b8c: bl              #0x6d28c4  ; AllocateLayerHandleStub -> LayerHandle<X0 bound Layer> (size=0x10)
    // 0x936b90: ldur            x2, [fp, #-8]
    // 0x936b94: StoreField: r2->field_3f = r0
    //     0x936b94: stur            w0, [x2, #0x3f]
    //     0x936b98: ldurb           w16, [x2, #-1]
    //     0x936b9c: ldurb           w17, [x0, #-1]
    //     0x936ba0: and             x16, x17, x16, lsr #2
    //     0x936ba4: tst             x16, HEAP, lsr #32
    //     0x936ba8: b.eq            #0x936bb0
    //     0x936bac: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x936bb0: ldur            x0, [fp, #-0x28]
    // 0x936bb4: StoreField: r2->field_23 = r0
    //     0x936bb4: stur            w0, [x2, #0x23]
    // 0x936bb8: ldur            x0, [fp, #-0x30]
    // 0x936bbc: StoreField: r2->field_2b = r0
    //     0x936bbc: stur            w0, [x2, #0x2b]
    //     0x936bc0: ldurb           w16, [x2, #-1]
    //     0x936bc4: ldurb           w17, [x0, #-1]
    //     0x936bc8: and             x16, x17, x16, lsr #2
    //     0x936bcc: tst             x16, HEAP, lsr #32
    //     0x936bd0: b.eq            #0x936bd8
    //     0x936bd4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x936bd8: ldur            x0, [fp, #-0x20]
    // 0x936bdc: StoreField: r2->field_2f = r0
    //     0x936bdc: stur            w0, [x2, #0x2f]
    //     0x936be0: ldurb           w16, [x2, #-1]
    //     0x936be4: ldurb           w17, [x0, #-1]
    //     0x936be8: and             x16, x17, x16, lsr #2
    //     0x936bec: tst             x16, HEAP, lsr #32
    //     0x936bf0: b.eq            #0x936bf8
    //     0x936bf4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x936bf8: ldur            x0, [fp, #-0x10]
    // 0x936bfc: StoreField: r2->field_27 = r0
    //     0x936bfc: stur            w0, [x2, #0x27]
    //     0x936c00: ldurb           w16, [x2, #-1]
    //     0x936c04: ldurb           w17, [x0, #-1]
    //     0x936c08: and             x16, x17, x16, lsr #2
    //     0x936c0c: tst             x16, HEAP, lsr #32
    //     0x936c10: b.eq            #0x936c18
    //     0x936c14: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x936c18: ldur            x0, [fp, #-0x18]
    // 0x936c1c: StoreField: r2->field_33 = r0
    //     0x936c1c: stur            w0, [x2, #0x33]
    //     0x936c20: ldurb           w16, [x2, #-1]
    //     0x936c24: ldurb           w17, [x0, #-1]
    //     0x936c28: and             x16, x17, x16, lsr #2
    //     0x936c2c: tst             x16, HEAP, lsr #32
    //     0x936c30: b.eq            #0x936c38
    //     0x936c34: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x936c38: StoreField: r2->field_7 = rZR
    //     0x936c38: stur            xzr, [x2, #7]
    // 0x936c3c: StoreField: r2->field_13 = rZR
    //     0x936c3c: stur            xzr, [x2, #0x13]
    // 0x936c40: StoreField: r2->field_1b = rZR
    //     0x936c40: stur            xzr, [x2, #0x1b]
    // 0x936c44: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0x936c44: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x936c48: ldr             x0, [x0, #0xca8]
    //     0x936c4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x936c50: cmp             w0, w16
    //     0x936c54: b.ne            #0x936c60
    //     0x936c58: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0x936c5c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x936c60: ldur            x3, [fp, #-8]
    // 0x936c64: StoreField: r3->field_f = r0
    //     0x936c64: stur            w0, [x3, #0xf]
    //     0x936c68: ldurb           w16, [x3, #-1]
    //     0x936c6c: ldurb           w17, [x0, #-1]
    //     0x936c70: and             x16, x17, x16, lsr #2
    //     0x936c74: tst             x16, HEAP, lsr #32
    //     0x936c78: b.eq            #0x936c80
    //     0x936c7c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x936c80: mov             x2, x3
    // 0x936c84: r1 = Function 'notifyListeners':.
    //     0x936c84: ldr             x1, [PP, #0x2608]  ; [pp+0x2608] AnonymousClosure: (0x646848), in [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners (0x6462b8)
    // 0x936c88: r0 = AllocateClosure()
    //     0x936c88: bl              #0xec1630  ; AllocateClosureStub
    // 0x936c8c: mov             x4, x0
    // 0x936c90: ldur            x3, [fp, #-0x10]
    // 0x936c94: stur            x4, [fp, #-0x18]
    // 0x936c98: r0 = LoadClassIdInstr(r3)
    //     0x936c98: ldur            x0, [x3, #-1]
    //     0x936c9c: ubfx            x0, x0, #0xc, #0x14
    // 0x936ca0: mov             x1, x3
    // 0x936ca4: mov             x2, x4
    // 0x936ca8: r0 = GDT[cid_x0 + 0xc407]()
    //     0x936ca8: movz            x17, #0xc407
    //     0x936cac: add             lr, x0, x17
    //     0x936cb0: ldr             lr, [x21, lr, lsl #3]
    //     0x936cb4: blr             lr
    // 0x936cb8: ldur            x2, [fp, #-8]
    // 0x936cbc: r1 = Function '_onStatusChange@579490068':.
    //     0x936cbc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25018] AnonymousClosure: (0x936d30), of [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionPainter
    //     0x936cc0: ldr             x1, [x1, #0x18]
    // 0x936cc4: r0 = AllocateClosure()
    //     0x936cc4: bl              #0xec1630  ; AllocateClosureStub
    // 0x936cc8: ldur            x1, [fp, #-0x10]
    // 0x936ccc: r2 = LoadClassIdInstr(r1)
    //     0x936ccc: ldur            x2, [x1, #-1]
    //     0x936cd0: ubfx            x2, x2, #0xc, #0x14
    // 0x936cd4: mov             x16, x0
    // 0x936cd8: mov             x0, x2
    // 0x936cdc: mov             x2, x16
    // 0x936ce0: r0 = GDT[cid_x0 + 0x7a1]()
    //     0x936ce0: add             lr, x0, #0x7a1
    //     0x936ce4: ldr             lr, [x21, lr, lsl #3]
    //     0x936ce8: blr             lr
    // 0x936cec: ldur            x1, [fp, #-0x30]
    // 0x936cf0: ldur            x2, [fp, #-0x18]
    // 0x936cf4: r0 = addListener()
    //     0x936cf4: bl              #0xa52138  ; [package:flutter/src/animation/tween.dart] __AnimatedEvaluation&Animation&AnimationWithParentMixin::addListener
    // 0x936cf8: ldur            x1, [fp, #-0x20]
    // 0x936cfc: r0 = LoadClassIdInstr(r1)
    //     0x936cfc: ldur            x0, [x1, #-1]
    //     0x936d00: ubfx            x0, x0, #0xc, #0x14
    // 0x936d04: ldur            x2, [fp, #-0x18]
    // 0x936d08: r0 = GDT[cid_x0 + 0xc407]()
    //     0x936d08: movz            x17, #0xc407
    //     0x936d0c: add             lr, x0, x17
    //     0x936d10: ldr             lr, [x21, lr, lsl #3]
    //     0x936d14: blr             lr
    // 0x936d18: r0 = Null
    //     0x936d18: mov             x0, NULL
    // 0x936d1c: LeaveFrame
    //     0x936d1c: mov             SP, fp
    //     0x936d20: ldp             fp, lr, [SP], #0x10
    // 0x936d24: ret
    //     0x936d24: ret             
    // 0x936d28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x936d28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x936d2c: b               #0x936b1c
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa8737c, size: 0x11c
    // 0xa8737c: EnterFrame
    //     0xa8737c: stp             fp, lr, [SP, #-0x10]!
    //     0xa87380: mov             fp, SP
    // 0xa87384: AllocStack(0x18)
    //     0xa87384: sub             SP, SP, #0x18
    // 0xa87388: SetupParameters(_ZoomEnterTransitionPainter this /* r1 => r0, fp-0x10 */)
    //     0xa87388: mov             x0, x1
    //     0xa8738c: stur            x1, [fp, #-0x10]
    // 0xa87390: CheckStackOverflow
    //     0xa87390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa87394: cmp             SP, x16
    //     0xa87398: b.ls            #0xa87490
    // 0xa8739c: LoadField: r3 = r0->field_27
    //     0xa8739c: ldur            w3, [x0, #0x27]
    // 0xa873a0: DecompressPointer r3
    //     0xa873a0: add             x3, x3, HEAP, lsl #32
    // 0xa873a4: mov             x2, x0
    // 0xa873a8: stur            x3, [fp, #-8]
    // 0xa873ac: r1 = Function 'notifyListeners':.
    //     0xa873ac: ldr             x1, [PP, #0x2608]  ; [pp+0x2608] AnonymousClosure: (0x646848), in [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners (0x6462b8)
    // 0xa873b0: r0 = AllocateClosure()
    //     0xa873b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa873b4: mov             x4, x0
    // 0xa873b8: ldur            x3, [fp, #-8]
    // 0xa873bc: stur            x4, [fp, #-0x18]
    // 0xa873c0: r0 = LoadClassIdInstr(r3)
    //     0xa873c0: ldur            x0, [x3, #-1]
    //     0xa873c4: ubfx            x0, x0, #0xc, #0x14
    // 0xa873c8: mov             x1, x3
    // 0xa873cc: mov             x2, x4
    // 0xa873d0: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa873d0: movz            x17, #0xbf5c
    //     0xa873d4: add             lr, x0, x17
    //     0xa873d8: ldr             lr, [x21, lr, lsl #3]
    //     0xa873dc: blr             lr
    // 0xa873e0: ldur            x2, [fp, #-0x10]
    // 0xa873e4: r1 = Function '_onStatusChange@579490068':.
    //     0xa873e4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25018] AnonymousClosure: (0x936d30), of [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionPainter
    //     0xa873e8: ldr             x1, [x1, #0x18]
    // 0xa873ec: r0 = AllocateClosure()
    //     0xa873ec: bl              #0xec1630  ; AllocateClosureStub
    // 0xa873f0: ldur            x1, [fp, #-8]
    // 0xa873f4: r2 = LoadClassIdInstr(r1)
    //     0xa873f4: ldur            x2, [x1, #-1]
    //     0xa873f8: ubfx            x2, x2, #0xc, #0x14
    // 0xa873fc: mov             x16, x0
    // 0xa87400: mov             x0, x2
    // 0xa87404: mov             x2, x16
    // 0xa87408: r0 = GDT[cid_x0 + 0x30c]()
    //     0xa87408: add             lr, x0, #0x30c
    //     0xa8740c: ldr             lr, [x21, lr, lsl #3]
    //     0xa87410: blr             lr
    // 0xa87414: ldur            x0, [fp, #-0x10]
    // 0xa87418: LoadField: r1 = r0->field_2b
    //     0xa87418: ldur            w1, [x0, #0x2b]
    // 0xa8741c: DecompressPointer r1
    //     0xa8741c: add             x1, x1, HEAP, lsl #32
    // 0xa87420: ldur            x2, [fp, #-0x18]
    // 0xa87424: r0 = removeListener()
    //     0xa87424: bl              #0xa59c94  ; [package:flutter/src/animation/tween.dart] __AnimatedEvaluation&Animation&AnimationWithParentMixin::removeListener
    // 0xa87428: ldur            x3, [fp, #-0x10]
    // 0xa8742c: LoadField: r1 = r3->field_2f
    //     0xa8742c: ldur            w1, [x3, #0x2f]
    // 0xa87430: DecompressPointer r1
    //     0xa87430: add             x1, x1, HEAP, lsl #32
    // 0xa87434: r0 = LoadClassIdInstr(r1)
    //     0xa87434: ldur            x0, [x1, #-1]
    //     0xa87438: ubfx            x0, x0, #0xc, #0x14
    // 0xa8743c: ldur            x2, [fp, #-0x18]
    // 0xa87440: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa87440: movz            x17, #0xbf5c
    //     0xa87444: add             lr, x0, x17
    //     0xa87448: ldr             lr, [x21, lr, lsl #3]
    //     0xa8744c: blr             lr
    // 0xa87450: ldur            x0, [fp, #-0x10]
    // 0xa87454: LoadField: r1 = r0->field_3b
    //     0xa87454: ldur            w1, [x0, #0x3b]
    // 0xa87458: DecompressPointer r1
    //     0xa87458: add             x1, x1, HEAP, lsl #32
    // 0xa8745c: r2 = Null
    //     0xa8745c: mov             x2, NULL
    // 0xa87460: r0 = layer=()
    //     0xa87460: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0xa87464: ldur            x0, [fp, #-0x10]
    // 0xa87468: LoadField: r1 = r0->field_3f
    //     0xa87468: ldur            w1, [x0, #0x3f]
    // 0xa8746c: DecompressPointer r1
    //     0xa8746c: add             x1, x1, HEAP, lsl #32
    // 0xa87470: r2 = Null
    //     0xa87470: mov             x2, NULL
    // 0xa87474: r0 = layer=()
    //     0xa87474: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0xa87478: ldur            x1, [fp, #-0x10]
    // 0xa8747c: r0 = dispose()
    //     0xa8747c: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0xa87480: r0 = Null
    //     0xa87480: mov             x0, NULL
    // 0xa87484: LeaveFrame
    //     0xa87484: mov             SP, fp
    //     0xa87488: ldp             fp, lr, [SP], #0x10
    // 0xa8748c: ret
    //     0xa8748c: ret             
    // 0xa87490: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa87490: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa87494: b               #0xa8739c
  }
  _ shouldRepaint(/* No info */) {
    // ** addr: 0xd842d8, size: 0x1f8
    // 0xd842d8: EnterFrame
    //     0xd842d8: stp             fp, lr, [SP, #-0x10]!
    //     0xd842dc: mov             fp, SP
    // 0xd842e0: AllocStack(0x18)
    //     0xd842e0: sub             SP, SP, #0x18
    // 0xd842e4: SetupParameters(_ZoomEnterTransitionPainter this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xd842e4: mov             x4, x1
    //     0xd842e8: mov             x3, x2
    //     0xd842ec: stur            x1, [fp, #-8]
    //     0xd842f0: stur            x2, [fp, #-0x10]
    // 0xd842f4: CheckStackOverflow
    //     0xd842f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd842f8: cmp             SP, x16
    //     0xd842fc: b.ls            #0xd844c8
    // 0xd84300: mov             x0, x3
    // 0xd84304: r2 = Null
    //     0xd84304: mov             x2, NULL
    // 0xd84308: r1 = Null
    //     0xd84308: mov             x1, NULL
    // 0xd8430c: r4 = 60
    //     0xd8430c: movz            x4, #0x3c
    // 0xd84310: branchIfSmi(r0, 0xd8431c)
    //     0xd84310: tbz             w0, #0, #0xd8431c
    // 0xd84314: r4 = LoadClassIdInstr(r0)
    //     0xd84314: ldur            x4, [x0, #-1]
    //     0xd84318: ubfx            x4, x4, #0xc, #0x14
    // 0xd8431c: cmp             x4, #0xe4e
    // 0xd84320: b.eq            #0xd84338
    // 0xd84324: r8 = _ZoomEnterTransitionPainter
    //     0xd84324: add             x8, PP, #0x39, lsl #12  ; [pp+0x397f8] Type: _ZoomEnterTransitionPainter
    //     0xd84328: ldr             x8, [x8, #0x7f8]
    // 0xd8432c: r3 = Null
    //     0xd8432c: add             x3, PP, #0x39, lsl #12  ; [pp+0x39800] Null
    //     0xd84330: ldr             x3, [x3, #0x800]
    // 0xd84334: r0 = DefaultTypeTest()
    //     0xd84334: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xd84338: ldur            x2, [fp, #-0x10]
    // 0xd8433c: LoadField: r0 = r2->field_23
    //     0xd8433c: ldur            w0, [x2, #0x23]
    // 0xd84340: DecompressPointer r0
    //     0xd84340: add             x0, x0, HEAP, lsl #32
    // 0xd84344: ldur            x3, [fp, #-8]
    // 0xd84348: LoadField: r1 = r3->field_23
    //     0xd84348: ldur            w1, [x3, #0x23]
    // 0xd8434c: DecompressPointer r1
    //     0xd8434c: add             x1, x1, HEAP, lsl #32
    // 0xd84350: cmp             w0, w1
    // 0xd84354: b.ne            #0xd84438
    // 0xd84358: LoadField: r1 = r2->field_27
    //     0xd84358: ldur            w1, [x2, #0x27]
    // 0xd8435c: DecompressPointer r1
    //     0xd8435c: add             x1, x1, HEAP, lsl #32
    // 0xd84360: r0 = LoadClassIdInstr(r1)
    //     0xd84360: ldur            x0, [x1, #-1]
    //     0xd84364: ubfx            x0, x0, #0xc, #0x14
    // 0xd84368: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xd84368: movz            x17, #0x276f
    //     0xd8436c: movk            x17, #0x1, lsl #16
    //     0xd84370: add             lr, x0, x17
    //     0xd84374: ldr             lr, [x21, lr, lsl #3]
    //     0xd84378: blr             lr
    // 0xd8437c: mov             x3, x0
    // 0xd84380: ldur            x2, [fp, #-8]
    // 0xd84384: stur            x3, [fp, #-0x18]
    // 0xd84388: LoadField: r1 = r2->field_27
    //     0xd84388: ldur            w1, [x2, #0x27]
    // 0xd8438c: DecompressPointer r1
    //     0xd8438c: add             x1, x1, HEAP, lsl #32
    // 0xd84390: r0 = LoadClassIdInstr(r1)
    //     0xd84390: ldur            x0, [x1, #-1]
    //     0xd84394: ubfx            x0, x0, #0xc, #0x14
    // 0xd84398: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xd84398: movz            x17, #0x276f
    //     0xd8439c: movk            x17, #0x1, lsl #16
    //     0xd843a0: add             lr, x0, x17
    //     0xd843a4: ldr             lr, [x21, lr, lsl #3]
    //     0xd843a8: blr             lr
    // 0xd843ac: mov             x1, x0
    // 0xd843b0: ldur            x0, [fp, #-0x18]
    // 0xd843b4: LoadField: d0 = r0->field_7
    //     0xd843b4: ldur            d0, [x0, #7]
    // 0xd843b8: LoadField: d1 = r1->field_7
    //     0xd843b8: ldur            d1, [x1, #7]
    // 0xd843bc: fcmp            d0, d1
    // 0xd843c0: b.ne            #0xd84438
    // 0xd843c4: ldur            x0, [fp, #-8]
    // 0xd843c8: ldur            x3, [fp, #-0x10]
    // 0xd843cc: LoadField: r1 = r3->field_2b
    //     0xd843cc: ldur            w1, [x3, #0x2b]
    // 0xd843d0: DecompressPointer r1
    //     0xd843d0: add             x1, x1, HEAP, lsl #32
    // 0xd843d4: LoadField: r2 = r1->field_f
    //     0xd843d4: ldur            w2, [x1, #0xf]
    // 0xd843d8: DecompressPointer r2
    //     0xd843d8: add             x2, x2, HEAP, lsl #32
    // 0xd843dc: LoadField: r4 = r1->field_b
    //     0xd843dc: ldur            w4, [x1, #0xb]
    // 0xd843e0: DecompressPointer r4
    //     0xd843e0: add             x4, x4, HEAP, lsl #32
    // 0xd843e4: mov             x1, x2
    // 0xd843e8: mov             x2, x4
    // 0xd843ec: r0 = evaluate()
    //     0xd843ec: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xd843f0: mov             x3, x0
    // 0xd843f4: ldur            x0, [fp, #-8]
    // 0xd843f8: stur            x3, [fp, #-0x18]
    // 0xd843fc: LoadField: r1 = r0->field_2b
    //     0xd843fc: ldur            w1, [x0, #0x2b]
    // 0xd84400: DecompressPointer r1
    //     0xd84400: add             x1, x1, HEAP, lsl #32
    // 0xd84404: LoadField: r2 = r1->field_f
    //     0xd84404: ldur            w2, [x1, #0xf]
    // 0xd84408: DecompressPointer r2
    //     0xd84408: add             x2, x2, HEAP, lsl #32
    // 0xd8440c: LoadField: r4 = r1->field_b
    //     0xd8440c: ldur            w4, [x1, #0xb]
    // 0xd84410: DecompressPointer r4
    //     0xd84410: add             x4, x4, HEAP, lsl #32
    // 0xd84414: mov             x1, x2
    // 0xd84418: mov             x2, x4
    // 0xd8441c: r0 = evaluate()
    //     0xd8441c: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xd84420: mov             x1, x0
    // 0xd84424: ldur            x0, [fp, #-0x18]
    // 0xd84428: LoadField: d0 = r0->field_7
    //     0xd84428: ldur            d0, [x0, #7]
    // 0xd8442c: LoadField: d1 = r1->field_7
    //     0xd8442c: ldur            d1, [x1, #7]
    // 0xd84430: fcmp            d0, d1
    // 0xd84434: b.eq            #0xd84440
    // 0xd84438: r0 = true
    //     0xd84438: add             x0, NULL, #0x20  ; true
    // 0xd8443c: b               #0xd844bc
    // 0xd84440: ldur            x2, [fp, #-8]
    // 0xd84444: ldur            x0, [fp, #-0x10]
    // 0xd84448: LoadField: r1 = r0->field_2f
    //     0xd84448: ldur            w1, [x0, #0x2f]
    // 0xd8444c: DecompressPointer r1
    //     0xd8444c: add             x1, x1, HEAP, lsl #32
    // 0xd84450: r0 = LoadClassIdInstr(r1)
    //     0xd84450: ldur            x0, [x1, #-1]
    //     0xd84454: ubfx            x0, x0, #0xc, #0x14
    // 0xd84458: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xd84458: movz            x17, #0x276f
    //     0xd8445c: movk            x17, #0x1, lsl #16
    //     0xd84460: add             lr, x0, x17
    //     0xd84464: ldr             lr, [x21, lr, lsl #3]
    //     0xd84468: blr             lr
    // 0xd8446c: mov             x2, x0
    // 0xd84470: ldur            x0, [fp, #-8]
    // 0xd84474: stur            x2, [fp, #-0x10]
    // 0xd84478: LoadField: r1 = r0->field_2f
    //     0xd84478: ldur            w1, [x0, #0x2f]
    // 0xd8447c: DecompressPointer r1
    //     0xd8447c: add             x1, x1, HEAP, lsl #32
    // 0xd84480: r0 = LoadClassIdInstr(r1)
    //     0xd84480: ldur            x0, [x1, #-1]
    //     0xd84484: ubfx            x0, x0, #0xc, #0x14
    // 0xd84488: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xd84488: movz            x17, #0x276f
    //     0xd8448c: movk            x17, #0x1, lsl #16
    //     0xd84490: add             lr, x0, x17
    //     0xd84494: ldr             lr, [x21, lr, lsl #3]
    //     0xd84498: blr             lr
    // 0xd8449c: ldur            x1, [fp, #-0x10]
    // 0xd844a0: LoadField: d0 = r1->field_7
    //     0xd844a0: ldur            d0, [x1, #7]
    // 0xd844a4: LoadField: d1 = r0->field_7
    //     0xd844a4: ldur            d1, [x0, #7]
    // 0xd844a8: fcmp            d0, d1
    // 0xd844ac: r16 = true
    //     0xd844ac: add             x16, NULL, #0x20  ; true
    // 0xd844b0: r17 = false
    //     0xd844b0: add             x17, NULL, #0x30  ; false
    // 0xd844b4: csel            x1, x16, x17, ne
    // 0xd844b8: mov             x0, x1
    // 0xd844bc: LeaveFrame
    //     0xd844bc: mov             SP, fp
    //     0xd844c0: ldp             fp, lr, [SP], #0x10
    // 0xd844c4: ret
    //     0xd844c4: ret             
    // 0xd844c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd844c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd844cc: b               #0xd84300
  }
  _ paintSnapshot(/* No info */) {
    // ** addr: 0xd84868, size: 0xd4
    // 0xd84868: EnterFrame
    //     0xd84868: stp             fp, lr, [SP, #-0x10]!
    //     0xd8486c: mov             fp, SP
    // 0xd84870: AllocStack(0x28)
    //     0xd84870: sub             SP, SP, #0x28
    // 0xd84874: SetupParameters(_ZoomEnterTransitionPainter this /* r1 => r6, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r6 => r0, fp-0x18 */, dynamic _ /* d0 => d2, fp-0x28 */)
    //     0xd84874: mov             x0, x6
    //     0xd84878: stur            x6, [fp, #-0x18]
    //     0xd8487c: mov             x6, x1
    //     0xd84880: mov             x4, x2
    //     0xd84884: mov             v2.16b, v0.16b
    //     0xd84888: stur            x1, [fp, #-8]
    //     0xd8488c: stur            x2, [fp, #-0x10]
    //     0xd84890: stur            d0, [fp, #-0x28]
    // 0xd84894: CheckStackOverflow
    //     0xd84894: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd84898: cmp             SP, x16
    //     0xd8489c: b.ls            #0xd84934
    // 0xd848a0: mov             x1, x6
    // 0xd848a4: mov             x2, x4
    // 0xd848a8: r0 = _drawScrim()
    //     0xd848a8: bl              #0xd84b44  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionPainter::_drawScrim
    // 0xd848ac: ldur            x0, [fp, #-8]
    // 0xd848b0: LoadField: r1 = r0->field_2b
    //     0xd848b0: ldur            w1, [x0, #0x2b]
    // 0xd848b4: DecompressPointer r1
    //     0xd848b4: add             x1, x1, HEAP, lsl #32
    // 0xd848b8: LoadField: r2 = r1->field_f
    //     0xd848b8: ldur            w2, [x1, #0xf]
    // 0xd848bc: DecompressPointer r2
    //     0xd848bc: add             x2, x2, HEAP, lsl #32
    // 0xd848c0: LoadField: r3 = r1->field_b
    //     0xd848c0: ldur            w3, [x1, #0xb]
    // 0xd848c4: DecompressPointer r3
    //     0xd848c4: add             x3, x3, HEAP, lsl #32
    // 0xd848c8: mov             x1, x2
    // 0xd848cc: mov             x2, x3
    // 0xd848d0: r0 = evaluate()
    //     0xd848d0: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xd848d4: mov             x2, x0
    // 0xd848d8: ldur            x0, [fp, #-8]
    // 0xd848dc: stur            x2, [fp, #-0x20]
    // 0xd848e0: LoadField: r1 = r0->field_2f
    //     0xd848e0: ldur            w1, [x0, #0x2f]
    // 0xd848e4: DecompressPointer r1
    //     0xd848e4: add             x1, x1, HEAP, lsl #32
    // 0xd848e8: r0 = LoadClassIdInstr(r1)
    //     0xd848e8: ldur            x0, [x1, #-1]
    //     0xd848ec: ubfx            x0, x0, #0xc, #0x14
    // 0xd848f0: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xd848f0: movz            x17, #0x276f
    //     0xd848f4: movk            x17, #0x1, lsl #16
    //     0xd848f8: add             lr, x0, x17
    //     0xd848fc: ldr             lr, [x21, lr, lsl #3]
    //     0xd84900: blr             lr
    // 0xd84904: mov             x1, x0
    // 0xd84908: ldur            x0, [fp, #-0x20]
    // 0xd8490c: LoadField: d0 = r0->field_7
    //     0xd8490c: ldur            d0, [x0, #7]
    // 0xd84910: LoadField: d1 = r1->field_7
    //     0xd84910: ldur            d1, [x1, #7]
    // 0xd84914: ldur            x1, [fp, #-0x10]
    // 0xd84918: ldur            x2, [fp, #-0x18]
    // 0xd8491c: ldur            d2, [fp, #-0x28]
    // 0xd84920: r0 = _drawImageScaledAndCentered()
    //     0xd84920: bl              #0xd8493c  ; [package:flutter/src/material/page_transitions_theme.dart] ::_drawImageScaledAndCentered
    // 0xd84924: r0 = Null
    //     0xd84924: mov             x0, NULL
    // 0xd84928: LeaveFrame
    //     0xd84928: mov             SP, fp
    //     0xd8492c: ldp             fp, lr, [SP], #0x10
    // 0xd84930: ret
    //     0xd84930: ret             
    // 0xd84934: r0 = StackOverflowSharedWithFPURegs()
    //     0xd84934: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd84938: b               #0xd848a0
  }
  _ _drawScrim(/* No info */) {
    // ** addr: 0xd84b44, size: 0x164
    // 0xd84b44: EnterFrame
    //     0xd84b44: stp             fp, lr, [SP, #-0x10]!
    //     0xd84b48: mov             fp, SP
    // 0xd84b4c: AllocStack(0x40)
    //     0xd84b4c: sub             SP, SP, #0x40
    // 0xd84b50: SetupParameters(_ZoomEnterTransitionPainter this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r2, fp-0x28 */)
    //     0xd84b50: mov             x4, x2
    //     0xd84b54: stur            x2, [fp, #-0x18]
    //     0xd84b58: mov             x2, x5
    //     0xd84b5c: stur            x5, [fp, #-0x28]
    //     0xd84b60: mov             x5, x1
    //     0xd84b64: stur            x1, [fp, #-0x10]
    //     0xd84b68: stur            x3, [fp, #-0x20]
    // 0xd84b6c: CheckStackOverflow
    //     0xd84b6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd84b70: cmp             SP, x16
    //     0xd84b74: b.ls            #0xd84c9c
    // 0xd84b78: LoadField: r0 = r5->field_23
    //     0xd84b78: ldur            w0, [x5, #0x23]
    // 0xd84b7c: DecompressPointer r0
    //     0xd84b7c: add             x0, x0, HEAP, lsl #32
    // 0xd84b80: tbz             w0, #4, #0xd84bf4
    // 0xd84b84: LoadField: r6 = r5->field_27
    //     0xd84b84: ldur            w6, [x5, #0x27]
    // 0xd84b88: DecompressPointer r6
    //     0xd84b88: add             x6, x6, HEAP, lsl #32
    // 0xd84b8c: stur            x6, [fp, #-8]
    // 0xd84b90: r0 = LoadClassIdInstr(r6)
    //     0xd84b90: ldur            x0, [x6, #-1]
    //     0xd84b94: ubfx            x0, x0, #0xc, #0x14
    // 0xd84b98: mov             x1, x6
    // 0xd84b9c: r0 = GDT[cid_x0 + 0x2e9]()
    //     0xd84b9c: add             lr, x0, #0x2e9
    //     0xd84ba0: ldr             lr, [x21, lr, lsl #3]
    //     0xd84ba4: blr             lr
    // 0xd84ba8: r16 = Instance_AnimationStatus
    //     0xd84ba8: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0xd84bac: cmp             w0, w16
    // 0xd84bb0: b.eq            #0xd84bf4
    // 0xd84bb4: r0 = InitLateStaticField(0xae8) // [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionState::_scrimOpacityTween
    //     0xd84bb4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xd84bb8: ldr             x0, [x0, #0x15d0]
    //     0xd84bbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xd84bc0: cmp             w0, w16
    //     0xd84bc4: b.ne            #0xd84bd4
    //     0xd84bc8: add             x2, PP, #0x44, lsl #12  ; [pp+0x44368] Field <_ZoomEnterTransitionState@579490068._scrimOpacityTween@579490068>: static late final (offset: 0xae8)
    //     0xd84bcc: ldr             x2, [x2, #0x368]
    //     0xd84bd0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xd84bd4: mov             x1, x0
    // 0xd84bd8: ldur            x2, [fp, #-8]
    // 0xd84bdc: r0 = evaluate()
    //     0xd84bdc: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xd84be0: cmp             w0, NULL
    // 0xd84be4: b.eq            #0xd84ca4
    // 0xd84be8: LoadField: d0 = r0->field_7
    //     0xd84be8: ldur            d0, [x0, #7]
    // 0xd84bec: mov             v1.16b, v0.16b
    // 0xd84bf0: b               #0xd84bf8
    // 0xd84bf4: d1 = 0.000000
    //     0xd84bf4: eor             v1.16b, v1.16b, v1.16b
    // 0xd84bf8: d0 = 0.000000
    //     0xd84bf8: eor             v0.16b, v0.16b, v0.16b
    // 0xd84bfc: stur            d1, [fp, #-0x30]
    // 0xd84c00: fcmp            d1, d0
    // 0xd84c04: b.le            #0xd84c8c
    // 0xd84c08: ldur            x0, [fp, #-0x10]
    // 0xd84c0c: ldur            x1, [fp, #-0x18]
    // 0xd84c10: r0 = canvas()
    //     0xd84c10: bl              #0x789cf4  ; [package:flutter/src/rendering/object.dart] PaintingContext::canvas
    // 0xd84c14: ldur            x1, [fp, #-0x20]
    // 0xd84c18: ldur            x2, [fp, #-0x28]
    // 0xd84c1c: stur            x0, [fp, #-8]
    // 0xd84c20: r0 = &()
    //     0xd84c20: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0xd84c24: stur            x0, [fp, #-0x18]
    // 0xd84c28: r16 = 136
    //     0xd84c28: movz            x16, #0x88
    // 0xd84c2c: stp             x16, NULL, [SP]
    // 0xd84c30: r0 = ByteData()
    //     0xd84c30: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xd84c34: stur            x0, [fp, #-0x20]
    // 0xd84c38: r0 = Paint()
    //     0xd84c38: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xd84c3c: mov             x2, x0
    // 0xd84c40: ldur            x0, [fp, #-0x20]
    // 0xd84c44: stur            x2, [fp, #-0x28]
    // 0xd84c48: StoreField: r2->field_7 = r0
    //     0xd84c48: stur            w0, [x2, #7]
    // 0xd84c4c: ldur            x0, [fp, #-0x10]
    // 0xd84c50: LoadField: r1 = r0->field_33
    //     0xd84c50: ldur            w1, [x0, #0x33]
    // 0xd84c54: DecompressPointer r1
    //     0xd84c54: add             x1, x1, HEAP, lsl #32
    // 0xd84c58: r0 = LoadClassIdInstr(r1)
    //     0xd84c58: ldur            x0, [x1, #-1]
    //     0xd84c5c: ubfx            x0, x0, #0xc, #0x14
    // 0xd84c60: ldur            d0, [fp, #-0x30]
    // 0xd84c64: r0 = GDT[cid_x0 + -0x1000]()
    //     0xd84c64: sub             lr, x0, #1, lsl #12
    //     0xd84c68: ldr             lr, [x21, lr, lsl #3]
    //     0xd84c6c: blr             lr
    // 0xd84c70: ldur            x1, [fp, #-0x28]
    // 0xd84c74: mov             x2, x0
    // 0xd84c78: r0 = color=()
    //     0xd84c78: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0xd84c7c: ldur            x1, [fp, #-8]
    // 0xd84c80: ldur            x2, [fp, #-0x18]
    // 0xd84c84: ldur            x3, [fp, #-0x28]
    // 0xd84c88: r0 = drawRect()
    //     0xd84c88: bl              #0x78be20  ; [dart:ui] _NativeCanvas::drawRect
    // 0xd84c8c: r0 = Null
    //     0xd84c8c: mov             x0, NULL
    // 0xd84c90: LeaveFrame
    //     0xd84c90: mov             SP, fp
    //     0xd84c94: ldp             fp, lr, [SP], #0x10
    // 0xd84c98: ret
    //     0xd84c98: ret             
    // 0xd84c9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd84c9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd84ca0: b               #0xd84b78
    // 0xd84ca4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd84ca4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ paint(/* No info */) {
    // ** addr: 0xd8780c, size: 0x16c
    // 0xd8780c: EnterFrame
    //     0xd8780c: stp             fp, lr, [SP, #-0x10]!
    //     0xd87810: mov             fp, SP
    // 0xd87814: AllocStack(0x30)
    //     0xd87814: sub             SP, SP, #0x30
    // 0xd87818: SetupParameters(_ZoomEnterTransitionPainter this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0xd87818: stur            x1, [fp, #-8]
    //     0xd8781c: stur            x2, [fp, #-0x10]
    //     0xd87820: stur            x3, [fp, #-0x18]
    //     0xd87824: stur            x5, [fp, #-0x20]
    //     0xd87828: stur            x6, [fp, #-0x28]
    // 0xd8782c: CheckStackOverflow
    //     0xd8782c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd87830: cmp             SP, x16
    //     0xd87834: b.ls            #0xd87970
    // 0xd87838: r1 = 2
    //     0xd87838: movz            x1, #0x2
    // 0xd8783c: r0 = AllocateContext()
    //     0xd8783c: bl              #0xec126c  ; AllocateContextStub
    // 0xd87840: mov             x3, x0
    // 0xd87844: ldur            x2, [fp, #-8]
    // 0xd87848: stur            x3, [fp, #-0x30]
    // 0xd8784c: StoreField: r3->field_f = r2
    //     0xd8784c: stur            w2, [x3, #0xf]
    // 0xd87850: ldur            x0, [fp, #-0x28]
    // 0xd87854: StoreField: r3->field_13 = r0
    //     0xd87854: stur            w0, [x3, #0x13]
    // 0xd87858: LoadField: r1 = r2->field_27
    //     0xd87858: ldur            w1, [x2, #0x27]
    // 0xd8785c: DecompressPointer r1
    //     0xd8785c: add             x1, x1, HEAP, lsl #32
    // 0xd87860: r0 = LoadClassIdInstr(r1)
    //     0xd87860: ldur            x0, [x1, #-1]
    //     0xd87864: ubfx            x0, x0, #0xc, #0x14
    // 0xd87868: r0 = GDT[cid_x0 + 0xd2a]()
    //     0xd87868: add             lr, x0, #0xd2a
    //     0xd8786c: ldr             lr, [x21, lr, lsl #3]
    //     0xd87870: blr             lr
    // 0xd87874: tbz             w0, #4, #0xd878ac
    // 0xd87878: ldur            x0, [fp, #-0x30]
    // 0xd8787c: LoadField: r1 = r0->field_13
    //     0xd8787c: ldur            w1, [x0, #0x13]
    // 0xd87880: DecompressPointer r1
    //     0xd87880: add             x1, x1, HEAP, lsl #32
    // 0xd87884: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xd87884: ldur            w0, [x1, #0x17]
    // 0xd87888: DecompressPointer r0
    //     0xd87888: add             x0, x0, HEAP, lsl #32
    // 0xd8788c: mov             x1, x0
    // 0xd87890: ldur            x2, [fp, #-0x10]
    // 0xd87894: ldur            x3, [fp, #-0x18]
    // 0xd87898: r0 = paint()
    //     0xd87898: bl              #0x7928f0  ; [package:flutter/src/rendering/proxy_box.dart] _RenderProxyBox&RenderBox&RenderObjectWithChildMixin&RenderProxyBoxMixin::paint
    // 0xd8789c: r0 = Null
    //     0xd8789c: mov             x0, NULL
    // 0xd878a0: LeaveFrame
    //     0xd878a0: mov             SP, fp
    //     0xd878a4: ldp             fp, lr, [SP], #0x10
    // 0xd878a8: ret
    //     0xd878a8: ret             
    // 0xd878ac: ldur            x4, [fp, #-8]
    // 0xd878b0: ldur            x0, [fp, #-0x30]
    // 0xd878b4: mov             x1, x4
    // 0xd878b8: ldur            x2, [fp, #-0x10]
    // 0xd878bc: ldur            x3, [fp, #-0x18]
    // 0xd878c0: ldur            x5, [fp, #-0x20]
    // 0xd878c4: r0 = _drawScrim()
    //     0xd878c4: bl              #0xd84b44  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionPainter::_drawScrim
    // 0xd878c8: ldur            x0, [fp, #-8]
    // 0xd878cc: LoadField: r3 = r0->field_37
    //     0xd878cc: ldur            w3, [x0, #0x37]
    // 0xd878d0: DecompressPointer r3
    //     0xd878d0: add             x3, x3, HEAP, lsl #32
    // 0xd878d4: stur            x3, [fp, #-0x28]
    // 0xd878d8: LoadField: r1 = r0->field_2b
    //     0xd878d8: ldur            w1, [x0, #0x2b]
    // 0xd878dc: DecompressPointer r1
    //     0xd878dc: add             x1, x1, HEAP, lsl #32
    // 0xd878e0: LoadField: r2 = r1->field_f
    //     0xd878e0: ldur            w2, [x1, #0xf]
    // 0xd878e4: DecompressPointer r2
    //     0xd878e4: add             x2, x2, HEAP, lsl #32
    // 0xd878e8: LoadField: r4 = r1->field_b
    //     0xd878e8: ldur            w4, [x1, #0xb]
    // 0xd878ec: DecompressPointer r4
    //     0xd878ec: add             x4, x4, HEAP, lsl #32
    // 0xd878f0: mov             x1, x2
    // 0xd878f4: mov             x2, x4
    // 0xd878f8: r0 = evaluate()
    //     0xd878f8: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xd878fc: LoadField: d0 = r0->field_7
    //     0xd878fc: ldur            d0, [x0, #7]
    // 0xd87900: ldur            x1, [fp, #-0x28]
    // 0xd87904: ldur            x2, [fp, #-0x20]
    // 0xd87908: r0 = _updateScaledTransform()
    //     0xd87908: bl              #0xd87978  ; [package:flutter/src/material/page_transitions_theme.dart] ::_updateScaledTransform
    // 0xd8790c: ldur            x0, [fp, #-8]
    // 0xd87910: LoadField: r3 = r0->field_3f
    //     0xd87910: ldur            w3, [x0, #0x3f]
    // 0xd87914: DecompressPointer r3
    //     0xd87914: add             x3, x3, HEAP, lsl #32
    // 0xd87918: stur            x3, [fp, #-0x20]
    // 0xd8791c: LoadField: r7 = r3->field_b
    //     0xd8791c: ldur            w7, [x3, #0xb]
    // 0xd87920: DecompressPointer r7
    //     0xd87920: add             x7, x7, HEAP, lsl #32
    // 0xd87924: ldur            x2, [fp, #-0x30]
    // 0xd87928: stur            x7, [fp, #-8]
    // 0xd8792c: r1 = Function '<anonymous closure>':.
    //     0xd8792c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44378] AnonymousClosure: (0xd87a74), in [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionPainter::paint (0xd8780c)
    //     0xd87930: ldr             x1, [x1, #0x378]
    // 0xd87934: r0 = AllocateClosure()
    //     0xd87934: bl              #0xec1630  ; AllocateClosureStub
    // 0xd87938: ldur            x1, [fp, #-0x10]
    // 0xd8793c: ldur            x3, [fp, #-0x18]
    // 0xd87940: ldur            x5, [fp, #-0x28]
    // 0xd87944: mov             x6, x0
    // 0xd87948: ldur            x7, [fp, #-8]
    // 0xd8794c: r2 = true
    //     0xd8794c: add             x2, NULL, #0x20  ; true
    // 0xd87950: r0 = pushTransform()
    //     0xd87950: bl              #0x78ffa8  ; [package:flutter/src/rendering/object.dart] PaintingContext::pushTransform
    // 0xd87954: ldur            x1, [fp, #-0x20]
    // 0xd87958: mov             x2, x0
    // 0xd8795c: r0 = layer=()
    //     0xd8795c: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0xd87960: r0 = Null
    //     0xd87960: mov             x0, NULL
    // 0xd87964: LeaveFrame
    //     0xd87964: mov             SP, fp
    //     0xd87968: ldp             fp, lr, [SP], #0x10
    // 0xd8796c: ret
    //     0xd8796c: ret             
    // 0xd87970: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd87970: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd87974: b               #0xd87838
  }
  [closure] void <anonymous closure>(dynamic, PaintingContext, Offset) {
    // ** addr: 0xd87a74, size: 0x13c
    // 0xd87a74: EnterFrame
    //     0xd87a74: stp             fp, lr, [SP, #-0x10]!
    //     0xd87a78: mov             fp, SP
    // 0xd87a7c: AllocStack(0x10)
    //     0xd87a7c: sub             SP, SP, #0x10
    // 0xd87a80: SetupParameters()
    //     0xd87a80: ldr             x0, [fp, #0x20]
    //     0xd87a84: ldur            w2, [x0, #0x17]
    //     0xd87a88: add             x2, x2, HEAP, lsl #32
    //     0xd87a8c: stur            x2, [fp, #-0x10]
    // 0xd87a90: CheckStackOverflow
    //     0xd87a90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd87a94: cmp             SP, x16
    //     0xd87a98: b.ls            #0xd87b8c
    // 0xd87a9c: LoadField: r0 = r2->field_f
    //     0xd87a9c: ldur            w0, [x2, #0xf]
    // 0xd87aa0: DecompressPointer r0
    //     0xd87aa0: add             x0, x0, HEAP, lsl #32
    // 0xd87aa4: LoadField: r3 = r0->field_3b
    //     0xd87aa4: ldur            w3, [x0, #0x3b]
    // 0xd87aa8: DecompressPointer r3
    //     0xd87aa8: add             x3, x3, HEAP, lsl #32
    // 0xd87aac: stur            x3, [fp, #-8]
    // 0xd87ab0: LoadField: r1 = r0->field_2f
    //     0xd87ab0: ldur            w1, [x0, #0x2f]
    // 0xd87ab4: DecompressPointer r1
    //     0xd87ab4: add             x1, x1, HEAP, lsl #32
    // 0xd87ab8: r0 = LoadClassIdInstr(r1)
    //     0xd87ab8: ldur            x0, [x1, #-1]
    //     0xd87abc: ubfx            x0, x0, #0xc, #0x14
    // 0xd87ac0: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xd87ac0: movz            x17, #0x276f
    //     0xd87ac4: movk            x17, #0x1, lsl #16
    //     0xd87ac8: add             lr, x0, x17
    //     0xd87acc: ldr             lr, [x21, lr, lsl #3]
    //     0xd87ad0: blr             lr
    // 0xd87ad4: LoadField: d0 = r0->field_7
    //     0xd87ad4: ldur            d0, [x0, #7]
    // 0xd87ad8: d1 = 255.000000
    //     0xd87ad8: ldr             d1, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xd87adc: fmul            d2, d0, d1
    // 0xd87ae0: mov             v0.16b, v2.16b
    // 0xd87ae4: stp             fp, lr, [SP, #-0x10]!
    // 0xd87ae8: mov             fp, SP
    // 0xd87aec: CallRuntime_LibcRound(double) -> double
    //     0xd87aec: and             SP, SP, #0xfffffffffffffff0
    //     0xd87af0: mov             sp, SP
    //     0xd87af4: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xd87af8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xd87afc: blr             x16
    //     0xd87b00: movz            x16, #0x8
    //     0xd87b04: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xd87b08: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xd87b0c: sub             sp, x16, #1, lsl #12
    //     0xd87b10: mov             SP, fp
    //     0xd87b14: ldp             fp, lr, [SP], #0x10
    // 0xd87b18: fcmp            d0, d0
    // 0xd87b1c: b.vs            #0xd87b94
    // 0xd87b20: fcvtzs          x0, d0
    // 0xd87b24: asr             x16, x0, #0x1e
    // 0xd87b28: cmp             x16, x0, asr #63
    // 0xd87b2c: b.ne            #0xd87b94
    // 0xd87b30: lsl             x0, x0, #1
    // 0xd87b34: ldur            x1, [fp, #-0x10]
    // 0xd87b38: LoadField: r5 = r1->field_13
    //     0xd87b38: ldur            w5, [x1, #0x13]
    // 0xd87b3c: DecompressPointer r5
    //     0xd87b3c: add             x5, x5, HEAP, lsl #32
    // 0xd87b40: LoadField: r2 = r1->field_f
    //     0xd87b40: ldur            w2, [x1, #0xf]
    // 0xd87b44: DecompressPointer r2
    //     0xd87b44: add             x2, x2, HEAP, lsl #32
    // 0xd87b48: LoadField: r1 = r2->field_3b
    //     0xd87b48: ldur            w1, [x2, #0x3b]
    // 0xd87b4c: DecompressPointer r1
    //     0xd87b4c: add             x1, x1, HEAP, lsl #32
    // 0xd87b50: LoadField: r6 = r1->field_b
    //     0xd87b50: ldur            w6, [x1, #0xb]
    // 0xd87b54: DecompressPointer r6
    //     0xd87b54: add             x6, x6, HEAP, lsl #32
    // 0xd87b58: r3 = LoadInt32Instr(r0)
    //     0xd87b58: sbfx            x3, x0, #1, #0x1f
    //     0xd87b5c: tbz             w0, #0, #0xd87b64
    //     0xd87b60: ldur            x3, [x0, #7]
    // 0xd87b64: ldr             x1, [fp, #0x18]
    // 0xd87b68: ldr             x2, [fp, #0x10]
    // 0xd87b6c: r0 = pushOpacity()
    //     0xd87b6c: bl              #0x78a828  ; [package:flutter/src/rendering/object.dart] PaintingContext::pushOpacity
    // 0xd87b70: ldur            x1, [fp, #-8]
    // 0xd87b74: mov             x2, x0
    // 0xd87b78: r0 = layer=()
    //     0xd87b78: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0xd87b7c: r0 = Null
    //     0xd87b7c: mov             x0, NULL
    // 0xd87b80: LeaveFrame
    //     0xd87b80: mov             SP, fp
    //     0xd87b84: ldp             fp, lr, [SP], #0x10
    // 0xd87b88: ret
    //     0xd87b88: ret             
    // 0xd87b8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd87b8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd87b90: b               #0xd87a9c
    // 0xd87b94: SaveReg d0
    //     0xd87b94: str             q0, [SP, #-0x10]!
    // 0xd87b98: r0 = 74
    //     0xd87b98: movz            x0, #0x4a
    // 0xd87b9c: r30 = DoubleToIntegerStub
    //     0xd87b9c: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xd87ba0: LoadField: r30 = r30->field_7
    //     0xd87ba0: ldur            lr, [lr, #7]
    // 0xd87ba4: blr             lr
    // 0xd87ba8: RestoreReg d0
    //     0xd87ba8: ldr             q0, [SP], #0x10
    // 0xd87bac: b               #0xd87b34
  }
}

// class id: 3931, size: 0xc, field offset: 0x8
//   const constructor, 
class PageTransitionsTheme extends _DiagnosticableTree&Object&Diagnosticable {

  _ConstMap<TargetPlatform, PageTransitionsBuilder> field_8;

  _ delegatedTransition(/* No info */) {
    // ** addr: 0x6546a4, size: 0x64
    // 0x6546a4: EnterFrame
    //     0x6546a4: stp             fp, lr, [SP, #-0x10]!
    //     0x6546a8: mov             fp, SP
    // 0x6546ac: CheckStackOverflow
    //     0x6546ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6546b0: cmp             SP, x16
    //     0x6546b4: b.ls            #0x654700
    // 0x6546b8: r1 = _ConstMap len:3
    //     0x6546b8: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a730] Map<TargetPlatform, PageTransitionsBuilder>(3)
    //     0x6546bc: ldr             x1, [x1, #0x730]
    // 0x6546c0: r2 = Instance_TargetPlatform
    //     0x6546c0: ldr             x2, [PP, #0x5110]  ; [pp+0x5110] Obj!TargetPlatform@e36f61
    // 0x6546c4: r0 = []()
    //     0x6546c4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x6546c8: cmp             w0, NULL
    // 0x6546cc: b.ne            #0x6546dc
    // 0x6546d0: r1 = Instance_ZoomPageTransitionsBuilder
    //     0x6546d0: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a738] Obj!ZoomPageTransitionsBuilder@e13f71
    //     0x6546d4: ldr             x1, [x1, #0x738]
    // 0x6546d8: b               #0x6546e0
    // 0x6546dc: mov             x1, x0
    // 0x6546e0: r0 = LoadClassIdInstr(r1)
    //     0x6546e0: ldur            x0, [x1, #-1]
    //     0x6546e4: ubfx            x0, x0, #0xc, #0x14
    // 0x6546e8: r0 = GDT[cid_x0 + -0xecb]()
    //     0x6546e8: sub             lr, x0, #0xecb
    //     0x6546ec: ldr             lr, [x21, lr, lsl #3]
    //     0x6546f0: blr             lr
    // 0x6546f4: LeaveFrame
    //     0x6546f4: mov             SP, fp
    //     0x6546f8: ldp             fp, lr, [SP], #0x10
    // 0x6546fc: ret
    //     0x6546fc: ret             
    // 0x654700: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x654700: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x654704: b               #0x6546b8
  }
  _ buildTransitions(/* No info */) {
    // ** addr: 0xa1d9e4, size: 0x70
    // 0xa1d9e4: EnterFrame
    //     0xa1d9e4: stp             fp, lr, [SP, #-0x10]!
    //     0xa1d9e8: mov             fp, SP
    // 0xa1d9ec: LoadField: r0 = r4->field_f
    //     0xa1d9ec: ldur            w0, [x4, #0xf]
    // 0xa1d9f0: cbnz            w0, #0xa1d9fc
    // 0xa1d9f4: r1 = Null
    //     0xa1d9f4: mov             x1, NULL
    // 0xa1d9f8: b               #0xa1da08
    // 0xa1d9fc: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xa1d9fc: ldur            w0, [x4, #0x17]
    // 0xa1da00: add             x1, fp, w0, sxtw #2
    // 0xa1da04: ldr             x1, [x1, #0x10]
    // 0xa1da08: ldr             x4, [fp, #0x28]
    // 0xa1da0c: ldr             x3, [fp, #0x20]
    // 0xa1da10: ldr             x2, [fp, #0x18]
    // 0xa1da14: ldr             x0, [fp, #0x10]
    // 0xa1da18: r0 = _PageTransitionsThemeTransitions()
    //     0xa1da18: bl              #0xa1da54  ; Allocate_PageTransitionsThemeTransitionsStub -> _PageTransitionsThemeTransitions<X0> (size=0x24)
    // 0xa1da1c: r1 = _ConstMap len:3
    //     0xa1da1c: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a730] Map<TargetPlatform, PageTransitionsBuilder>(3)
    //     0xa1da20: ldr             x1, [x1, #0x730]
    // 0xa1da24: StoreField: r0->field_f = r1
    //     0xa1da24: stur            w1, [x0, #0xf]
    // 0xa1da28: ldr             x1, [fp, #0x28]
    // 0xa1da2c: StoreField: r0->field_13 = r1
    //     0xa1da2c: stur            w1, [x0, #0x13]
    // 0xa1da30: ldr             x1, [fp, #0x20]
    // 0xa1da34: ArrayStore: r0[0] = r1  ; List_4
    //     0xa1da34: stur            w1, [x0, #0x17]
    // 0xa1da38: ldr             x1, [fp, #0x18]
    // 0xa1da3c: StoreField: r0->field_1b = r1
    //     0xa1da3c: stur            w1, [x0, #0x1b]
    // 0xa1da40: ldr             x1, [fp, #0x10]
    // 0xa1da44: StoreField: r0->field_1f = r1
    //     0xa1da44: stur            w1, [x0, #0x1f]
    // 0xa1da48: LeaveFrame
    //     0xa1da48: mov             SP, fp
    //     0xa1da4c: ldp             fp, lr, [SP], #0x10
    // 0xa1da50: ret
    //     0xa1da50: ret             
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbe69b8, size: 0x50
    // 0xbe69b8: EnterFrame
    //     0xbe69b8: stp             fp, lr, [SP, #-0x10]!
    //     0xbe69bc: mov             fp, SP
    // 0xbe69c0: CheckStackOverflow
    //     0xbe69c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe69c4: cmp             SP, x16
    //     0xbe69c8: b.ls            #0xbe6a00
    // 0xbe69cc: ldr             x1, [fp, #0x10]
    // 0xbe69d0: r0 = _all()
    //     0xbe69d0: bl              #0xbe6a08  ; [package:flutter/src/material/page_transitions_theme.dart] PageTransitionsTheme::_all
    // 0xbe69d4: mov             x1, x0
    // 0xbe69d8: r0 = hashAll()
    //     0xbe69d8: bl              #0xbdcf60  ; [dart:core] Object::hashAll
    // 0xbe69dc: mov             x2, x0
    // 0xbe69e0: r0 = BoxInt64Instr(r2)
    //     0xbe69e0: sbfiz           x0, x2, #1, #0x1f
    //     0xbe69e4: cmp             x2, x0, asr #1
    //     0xbe69e8: b.eq            #0xbe69f4
    //     0xbe69ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe69f0: stur            x2, [x0, #7]
    // 0xbe69f4: LeaveFrame
    //     0xbe69f4: mov             SP, fp
    //     0xbe69f8: ldp             fp, lr, [SP], #0x10
    // 0xbe69fc: ret
    //     0xbe69fc: ret             
    // 0xbe6a00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe6a00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe6a04: b               #0xbe69cc
  }
  _ _all(/* No info */) {
    // ** addr: 0xbe6a08, size: 0x6c
    // 0xbe6a08: EnterFrame
    //     0xbe6a08: stp             fp, lr, [SP, #-0x10]!
    //     0xbe6a0c: mov             fp, SP
    // 0xbe6a10: AllocStack(0x18)
    //     0xbe6a10: sub             SP, SP, #0x18
    // 0xbe6a14: CheckStackOverflow
    //     0xbe6a14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe6a18: cmp             SP, x16
    //     0xbe6a1c: b.ls            #0xbe6a6c
    // 0xbe6a20: r1 = Function '<anonymous closure>':.
    //     0xbe6a20: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d1c0] AnonymousClosure: (0xbe6a74), in [package:flutter/src/material/page_transitions_theme.dart] PageTransitionsTheme::_all (0xbe6a08)
    //     0xbe6a24: ldr             x1, [x1, #0x1c0]
    // 0xbe6a28: r2 = Null
    //     0xbe6a28: mov             x2, NULL
    // 0xbe6a2c: r0 = AllocateClosure()
    //     0xbe6a2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbe6a30: r16 = <PageTransitionsBuilder?>
    //     0xbe6a30: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d1c8] TypeArguments: <PageTransitionsBuilder?>
    //     0xbe6a34: ldr             x16, [x16, #0x1c8]
    // 0xbe6a38: r30 = const [Instance of 'TargetPlatform', Instance of 'TargetPlatform', Instance of 'TargetPlatform', Instance of 'TargetPlatform', Instance of 'TargetPlatform', Instance of 'TargetPlatform']
    //     0xbe6a38: add             lr, PP, #0x1d, lsl #12  ; [pp+0x1d1d0] List<TargetPlatform>(6)
    //     0xbe6a3c: ldr             lr, [lr, #0x1d0]
    // 0xbe6a40: stp             lr, x16, [SP, #8]
    // 0xbe6a44: str             x0, [SP]
    // 0xbe6a48: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbe6a48: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbe6a4c: r0 = map()
    //     0xbe6a4c: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xbe6a50: LoadField: r1 = r0->field_7
    //     0xbe6a50: ldur            w1, [x0, #7]
    // 0xbe6a54: DecompressPointer r1
    //     0xbe6a54: add             x1, x1, HEAP, lsl #32
    // 0xbe6a58: mov             x2, x0
    // 0xbe6a5c: r0 = _GrowableList.of()
    //     0xbe6a5c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xbe6a60: LeaveFrame
    //     0xbe6a60: mov             SP, fp
    //     0xbe6a64: ldp             fp, lr, [SP], #0x10
    // 0xbe6a68: ret
    //     0xbe6a68: ret             
    // 0xbe6a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe6a6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe6a70: b               #0xbe6a20
  }
  [closure] PageTransitionsBuilder? <anonymous closure>(dynamic, TargetPlatform) {
    // ** addr: 0xbe6a74, size: 0x38
    // 0xbe6a74: EnterFrame
    //     0xbe6a74: stp             fp, lr, [SP, #-0x10]!
    //     0xbe6a78: mov             fp, SP
    // 0xbe6a7c: CheckStackOverflow
    //     0xbe6a7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe6a80: cmp             SP, x16
    //     0xbe6a84: b.ls            #0xbe6aa4
    // 0xbe6a88: ldr             x2, [fp, #0x10]
    // 0xbe6a8c: r1 = _ConstMap len:3
    //     0xbe6a8c: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a730] Map<TargetPlatform, PageTransitionsBuilder>(3)
    //     0xbe6a90: ldr             x1, [x1, #0x730]
    // 0xbe6a94: r0 = []()
    //     0xbe6a94: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xbe6a98: LeaveFrame
    //     0xbe6a98: mov             SP, fp
    //     0xbe6a9c: ldp             fp, lr, [SP], #0x10
    // 0xbe6aa0: ret
    //     0xbe6aa0: ret             
    // 0xbe6aa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe6aa4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe6aa8: b               #0xbe6a88
  }
  _ ==(/* No info */) {
    // ** addr: 0xd54888, size: 0x110
    // 0xd54888: EnterFrame
    //     0xd54888: stp             fp, lr, [SP, #-0x10]!
    //     0xd5488c: mov             fp, SP
    // 0xd54890: AllocStack(0x20)
    //     0xd54890: sub             SP, SP, #0x20
    // 0xd54894: CheckStackOverflow
    //     0xd54894: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd54898: cmp             SP, x16
    //     0xd5489c: b.ls            #0xd54990
    // 0xd548a0: ldr             x0, [fp, #0x10]
    // 0xd548a4: cmp             w0, NULL
    // 0xd548a8: b.ne            #0xd548bc
    // 0xd548ac: r0 = false
    //     0xd548ac: add             x0, NULL, #0x30  ; false
    // 0xd548b0: LeaveFrame
    //     0xd548b0: mov             SP, fp
    //     0xd548b4: ldp             fp, lr, [SP], #0x10
    // 0xd548b8: ret
    //     0xd548b8: ret             
    // 0xd548bc: ldr             x1, [fp, #0x18]
    // 0xd548c0: cmp             w1, w0
    // 0xd548c4: b.ne            #0xd548d8
    // 0xd548c8: r0 = true
    //     0xd548c8: add             x0, NULL, #0x20  ; true
    // 0xd548cc: LeaveFrame
    //     0xd548cc: mov             SP, fp
    //     0xd548d0: ldp             fp, lr, [SP], #0x10
    // 0xd548d4: ret
    //     0xd548d4: ret             
    // 0xd548d8: str             x0, [SP]
    // 0xd548dc: r0 = runtimeType()
    //     0xd548dc: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd548e0: r1 = LoadClassIdInstr(r0)
    //     0xd548e0: ldur            x1, [x0, #-1]
    //     0xd548e4: ubfx            x1, x1, #0xc, #0x14
    // 0xd548e8: r16 = PageTransitionsTheme
    //     0xd548e8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d1d8] Type: PageTransitionsTheme
    //     0xd548ec: ldr             x16, [x16, #0x1d8]
    // 0xd548f0: stp             x16, x0, [SP]
    // 0xd548f4: mov             x0, x1
    // 0xd548f8: mov             lr, x0
    // 0xd548fc: ldr             lr, [x21, lr, lsl #3]
    // 0xd54900: blr             lr
    // 0xd54904: tbz             w0, #4, #0xd54918
    // 0xd54908: r0 = false
    //     0xd54908: add             x0, NULL, #0x30  ; false
    // 0xd5490c: LeaveFrame
    //     0xd5490c: mov             SP, fp
    //     0xd54910: ldp             fp, lr, [SP], #0x10
    // 0xd54914: ret
    //     0xd54914: ret             
    // 0xd54918: ldr             x0, [fp, #0x10]
    // 0xd5491c: r1 = 60
    //     0xd5491c: movz            x1, #0x3c
    // 0xd54920: branchIfSmi(r0, 0xd5492c)
    //     0xd54920: tbz             w0, #0, #0xd5492c
    // 0xd54924: r1 = LoadClassIdInstr(r0)
    //     0xd54924: ldur            x1, [x0, #-1]
    //     0xd54928: ubfx            x1, x1, #0xc, #0x14
    // 0xd5492c: cmp             x1, #0xf5b
    // 0xd54930: b.ne            #0xd54944
    // 0xd54934: r0 = true
    //     0xd54934: add             x0, NULL, #0x20  ; true
    // 0xd54938: LeaveFrame
    //     0xd54938: mov             SP, fp
    //     0xd5493c: ldp             fp, lr, [SP], #0x10
    // 0xd54940: ret
    //     0xd54940: ret             
    // 0xd54944: cmp             x1, #0xf5b
    // 0xd54948: b.ne            #0xd54980
    // 0xd5494c: ldr             x1, [fp, #0x18]
    // 0xd54950: r0 = _all()
    //     0xd54950: bl              #0xbe6a08  ; [package:flutter/src/material/page_transitions_theme.dart] PageTransitionsTheme::_all
    // 0xd54954: ldr             x1, [fp, #0x18]
    // 0xd54958: stur            x0, [fp, #-8]
    // 0xd5495c: r0 = _all()
    //     0xd5495c: bl              #0xbe6a08  ; [package:flutter/src/material/page_transitions_theme.dart] PageTransitionsTheme::_all
    // 0xd54960: r16 = <PageTransitionsBuilder?>
    //     0xd54960: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d1c8] TypeArguments: <PageTransitionsBuilder?>
    //     0xd54964: ldr             x16, [x16, #0x1c8]
    // 0xd54968: ldur            lr, [fp, #-8]
    // 0xd5496c: stp             lr, x16, [SP, #8]
    // 0xd54970: str             x0, [SP]
    // 0xd54974: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xd54974: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xd54978: r0 = listEquals()
    //     0xd54978: bl              #0x64d5e4  ; [package:flutter/src/foundation/collections.dart] ::listEquals
    // 0xd5497c: b               #0xd54984
    // 0xd54980: r0 = false
    //     0xd54980: add             x0, NULL, #0x30  ; false
    // 0xd54984: LeaveFrame
    //     0xd54984: mov             SP, fp
    //     0xd54988: ldp             fp, lr, [SP], #0x10
    // 0xd5498c: ret
    //     0xd5498c: ret             
    // 0xd54990: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd54990: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd54994: b               #0xd548a0
  }
}

// class id: 4280, size: 0x18, field offset: 0x14
class _PageTransitionsThemeTransitionsState<C1X0> extends State<C1X0> {

  _ build(/* No info */) {
    // ** addr: 0x9fc008, size: 0x1fc
    // 0x9fc008: EnterFrame
    //     0x9fc008: stp             fp, lr, [SP, #-0x10]!
    //     0x9fc00c: mov             fp, SP
    // 0x9fc010: AllocStack(0x40)
    //     0x9fc010: sub             SP, SP, #0x40
    // 0x9fc014: SetupParameters(_PageTransitionsThemeTransitionsState<C1X0> this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0x9fc014: mov             x0, x1
    //     0x9fc018: stur            x1, [fp, #-8]
    //     0x9fc01c: mov             x1, x2
    // 0x9fc020: CheckStackOverflow
    //     0x9fc020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9fc024: cmp             SP, x16
    //     0x9fc028: b.ls            #0x9fc1f0
    // 0x9fc02c: r0 = of()
    //     0x9fc02c: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9fc030: ldur            x2, [fp, #-8]
    // 0x9fc034: LoadField: r0 = r2->field_b
    //     0x9fc034: ldur            w0, [x2, #0xb]
    // 0x9fc038: DecompressPointer r0
    //     0x9fc038: add             x0, x0, HEAP, lsl #32
    // 0x9fc03c: cmp             w0, NULL
    // 0x9fc040: b.eq            #0x9fc1f8
    // 0x9fc044: LoadField: r1 = r0->field_13
    //     0x9fc044: ldur            w1, [x0, #0x13]
    // 0x9fc048: DecompressPointer r1
    //     0x9fc048: add             x1, x1, HEAP, lsl #32
    // 0x9fc04c: r0 = LoadClassIdInstr(r1)
    //     0x9fc04c: ldur            x0, [x1, #-1]
    //     0x9fc050: ubfx            x0, x0, #0xc, #0x14
    // 0x9fc054: r0 = GDT[cid_x0 + -0xfad]()
    //     0x9fc054: sub             lr, x0, #0xfad
    //     0x9fc058: ldr             lr, [x21, lr, lsl #3]
    //     0x9fc05c: blr             lr
    // 0x9fc060: tbnz            w0, #4, #0x9fc08c
    // 0x9fc064: ldur            x0, [fp, #-8]
    // 0x9fc068: LoadField: r1 = r0->field_13
    //     0x9fc068: ldur            w1, [x0, #0x13]
    // 0x9fc06c: DecompressPointer r1
    //     0x9fc06c: add             x1, x1, HEAP, lsl #32
    // 0x9fc070: cmp             w1, NULL
    // 0x9fc074: b.ne            #0x9fc084
    // 0x9fc078: r1 = Instance_TargetPlatform
    //     0x9fc078: ldr             x1, [PP, #0x5110]  ; [pp+0x5110] Obj!TargetPlatform@e36f61
    // 0x9fc07c: StoreField: r0->field_13 = r1
    //     0x9fc07c: stur            w1, [x0, #0x13]
    // 0x9fc080: r1 = Instance_TargetPlatform
    //     0x9fc080: ldr             x1, [PP, #0x5110]  ; [pp+0x5110] Obj!TargetPlatform@e36f61
    // 0x9fc084: mov             x3, x1
    // 0x9fc088: b               #0x9fc098
    // 0x9fc08c: ldur            x0, [fp, #-8]
    // 0x9fc090: StoreField: r0->field_13 = rNULL
    //     0x9fc090: stur            NULL, [x0, #0x13]
    // 0x9fc094: r3 = Instance_TargetPlatform
    //     0x9fc094: ldr             x3, [PP, #0x5110]  ; [pp+0x5110] Obj!TargetPlatform@e36f61
    // 0x9fc098: stur            x3, [fp, #-0x10]
    // 0x9fc09c: LoadField: r1 = r0->field_b
    //     0x9fc09c: ldur            w1, [x0, #0xb]
    // 0x9fc0a0: DecompressPointer r1
    //     0x9fc0a0: add             x1, x1, HEAP, lsl #32
    // 0x9fc0a4: cmp             w1, NULL
    // 0x9fc0a8: b.eq            #0x9fc1fc
    // 0x9fc0ac: mov             x2, x3
    // 0x9fc0b0: r1 = _ConstMap len:3
    //     0x9fc0b0: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a730] Map<TargetPlatform, PageTransitionsBuilder>(3)
    //     0x9fc0b4: ldr             x1, [x1, #0x730]
    // 0x9fc0b8: r0 = []()
    //     0x9fc0b8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x9fc0bc: cmp             w0, NULL
    // 0x9fc0c0: b.ne            #0x9fc148
    // 0x9fc0c4: ldur            x0, [fp, #-0x10]
    // 0x9fc0c8: r16 = Instance_TargetPlatform
    //     0x9fc0c8: add             x16, PP, #0x39, lsl #12  ; [pp+0x397c8] Obj!TargetPlatform@e36fa1
    //     0x9fc0cc: ldr             x16, [x16, #0x7c8]
    // 0x9fc0d0: cmp             w0, w16
    // 0x9fc0d4: b.ne            #0x9fc0e4
    // 0x9fc0d8: r0 = Instance_CupertinoPageTransitionsBuilder
    //     0x9fc0d8: add             x0, PP, #0x39, lsl #12  ; [pp+0x397d0] Obj!CupertinoPageTransitionsBuilder@e13f61
    //     0x9fc0dc: ldr             x0, [x0, #0x7d0]
    // 0x9fc0e0: b               #0x9fc140
    // 0x9fc0e4: r16 = Instance_TargetPlatform
    //     0x9fc0e4: ldr             x16, [PP, #0x5110]  ; [pp+0x5110] Obj!TargetPlatform@e36f61
    // 0x9fc0e8: cmp             w0, w16
    // 0x9fc0ec: b.eq            #0x9fc130
    // 0x9fc0f0: r16 = Instance_TargetPlatform
    //     0x9fc0f0: add             x16, PP, #0x39, lsl #12  ; [pp+0x397d8] Obj!TargetPlatform@e37001
    //     0x9fc0f4: ldr             x16, [x16, #0x7d8]
    // 0x9fc0f8: cmp             w0, w16
    // 0x9fc0fc: b.eq            #0x9fc130
    // 0x9fc100: r16 = Instance_TargetPlatform
    //     0x9fc100: add             x16, PP, #0x39, lsl #12  ; [pp+0x397e0] Obj!TargetPlatform@e36fe1
    //     0x9fc104: ldr             x16, [x16, #0x7e0]
    // 0x9fc108: cmp             w0, w16
    // 0x9fc10c: b.eq            #0x9fc130
    // 0x9fc110: r16 = Instance_TargetPlatform
    //     0x9fc110: add             x16, PP, #0x39, lsl #12  ; [pp+0x397e8] Obj!TargetPlatform@e36f81
    //     0x9fc114: ldr             x16, [x16, #0x7e8]
    // 0x9fc118: cmp             w0, w16
    // 0x9fc11c: b.eq            #0x9fc130
    // 0x9fc120: r16 = Instance_TargetPlatform
    //     0x9fc120: add             x16, PP, #0x39, lsl #12  ; [pp+0x397f0] Obj!TargetPlatform@e36fc1
    //     0x9fc124: ldr             x16, [x16, #0x7f0]
    // 0x9fc128: cmp             w0, w16
    // 0x9fc12c: b.ne            #0x9fc13c
    // 0x9fc130: r0 = Instance_ZoomPageTransitionsBuilder
    //     0x9fc130: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1a738] Obj!ZoomPageTransitionsBuilder@e13f71
    //     0x9fc134: ldr             x0, [x0, #0x738]
    // 0x9fc138: b               #0x9fc140
    // 0x9fc13c: r0 = Null
    //     0x9fc13c: mov             x0, NULL
    // 0x9fc140: mov             x4, x0
    // 0x9fc144: b               #0x9fc14c
    // 0x9fc148: mov             x4, x0
    // 0x9fc14c: ldur            x0, [fp, #-8]
    // 0x9fc150: stur            x4, [fp, #-0x10]
    // 0x9fc154: LoadField: r2 = r0->field_7
    //     0x9fc154: ldur            w2, [x0, #7]
    // 0x9fc158: DecompressPointer r2
    //     0x9fc158: add             x2, x2, HEAP, lsl #32
    // 0x9fc15c: r1 = Null
    //     0x9fc15c: mov             x1, NULL
    // 0x9fc160: r3 = <C1X0>
    //     0x9fc160: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2af98] TypeArguments: <C1X0>
    //     0x9fc164: ldr             x3, [x3, #0xf98]
    // 0x9fc168: r0 = Null
    //     0x9fc168: mov             x0, NULL
    // 0x9fc16c: cmp             x2, x0
    // 0x9fc170: b.eq            #0x9fc180
    // 0x9fc174: r30 = InstantiateTypeArgumentsStub
    //     0x9fc174: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x9fc178: LoadField: r30 = r30->field_7
    //     0x9fc178: ldur            lr, [lr, #7]
    // 0x9fc17c: blr             lr
    // 0x9fc180: mov             x1, x0
    // 0x9fc184: ldur            x0, [fp, #-8]
    // 0x9fc188: LoadField: r2 = r0->field_b
    //     0x9fc188: ldur            w2, [x0, #0xb]
    // 0x9fc18c: DecompressPointer r2
    //     0x9fc18c: add             x2, x2, HEAP, lsl #32
    // 0x9fc190: cmp             w2, NULL
    // 0x9fc194: b.eq            #0x9fc200
    // 0x9fc198: LoadField: r0 = r2->field_13
    //     0x9fc198: ldur            w0, [x2, #0x13]
    // 0x9fc19c: DecompressPointer r0
    //     0x9fc19c: add             x0, x0, HEAP, lsl #32
    // 0x9fc1a0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9fc1a0: ldur            w3, [x2, #0x17]
    // 0x9fc1a4: DecompressPointer r3
    //     0x9fc1a4: add             x3, x3, HEAP, lsl #32
    // 0x9fc1a8: LoadField: r4 = r2->field_1b
    //     0x9fc1a8: ldur            w4, [x2, #0x1b]
    // 0x9fc1ac: DecompressPointer r4
    //     0x9fc1ac: add             x4, x4, HEAP, lsl #32
    // 0x9fc1b0: LoadField: r5 = r2->field_1f
    //     0x9fc1b0: ldur            w5, [x2, #0x1f]
    // 0x9fc1b4: DecompressPointer r5
    //     0x9fc1b4: add             x5, x5, HEAP, lsl #32
    // 0x9fc1b8: ldur            x2, [fp, #-0x10]
    // 0x9fc1bc: r6 = LoadClassIdInstr(r2)
    //     0x9fc1bc: ldur            x6, [x2, #-1]
    //     0x9fc1c0: ubfx            x6, x6, #0xc, #0x14
    // 0x9fc1c4: stp             x2, x1, [SP, #0x20]
    // 0x9fc1c8: stp             x3, x0, [SP, #0x10]
    // 0x9fc1cc: stp             x5, x4, [SP]
    // 0x9fc1d0: mov             x0, x6
    // 0x9fc1d4: r4 = const [0x1, 0x5, 0x5, 0x5, null]
    //     0x9fc1d4: ldr             x4, [PP, #0x1800]  ; [pp+0x1800] List(5) [0x1, 0x5, 0x5, 0x5, Null]
    // 0x9fc1d8: r0 = GDT[cid_x0 + -0xdc7]()
    //     0x9fc1d8: sub             lr, x0, #0xdc7
    //     0x9fc1dc: ldr             lr, [x21, lr, lsl #3]
    //     0x9fc1e0: blr             lr
    // 0x9fc1e4: LeaveFrame
    //     0x9fc1e4: mov             SP, fp
    //     0x9fc1e8: ldp             fp, lr, [SP], #0x10
    // 0x9fc1ec: ret
    //     0x9fc1ec: ret             
    // 0x9fc1f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9fc1f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9fc1f4: b               #0x9fc02c
    // 0x9fc1f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fc1f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fc1fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fc1fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fc200: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fc200: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4281, size: 0x20, field offset: 0x14
//   transformed mixin,
abstract class __ZoomExitTransitionState&State&_ZoomTransitionBase extends State<dynamic>
     with _ZoomTransitionBase<X0 bound StatefulWidget> {

  late Animation<double> scaleTransition; // offset: 0x1c
  late Animation<double> fadeTransition; // offset: 0x18

  [closure] void onAnimationStatusChange(dynamic, AnimationStatus) {
    // ** addr: 0x937c6c, size: 0x3c
    // 0x937c6c: EnterFrame
    //     0x937c6c: stp             fp, lr, [SP, #-0x10]!
    //     0x937c70: mov             fp, SP
    // 0x937c74: ldr             x0, [fp, #0x18]
    // 0x937c78: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x937c78: ldur            w1, [x0, #0x17]
    // 0x937c7c: DecompressPointer r1
    //     0x937c7c: add             x1, x1, HEAP, lsl #32
    // 0x937c80: CheckStackOverflow
    //     0x937c80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x937c84: cmp             SP, x16
    //     0x937c88: b.ls            #0x937ca0
    // 0x937c8c: ldr             x2, [fp, #0x10]
    // 0x937c90: r0 = onAnimationStatusChange()
    //     0x937c90: bl              #0x937ca8  ; [package:flutter/src/material/page_transitions_theme.dart] __ZoomExitTransitionState&State&_ZoomTransitionBase::onAnimationStatusChange
    // 0x937c94: LeaveFrame
    //     0x937c94: mov             SP, fp
    //     0x937c98: ldp             fp, lr, [SP], #0x10
    // 0x937c9c: ret
    //     0x937c9c: ret             
    // 0x937ca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x937ca0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937ca4: b               #0x937c8c
  }
  _ onAnimationStatusChange(/* No info */) {
    // ** addr: 0x937ca8, size: 0x84
    // 0x937ca8: EnterFrame
    //     0x937ca8: stp             fp, lr, [SP, #-0x10]!
    //     0x937cac: mov             fp, SP
    // 0x937cb0: AllocStack(0x8)
    //     0x937cb0: sub             SP, SP, #8
    // 0x937cb4: CheckStackOverflow
    //     0x937cb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x937cb8: cmp             SP, x16
    //     0x937cbc: b.ls            #0x937d24
    // 0x937cc0: LoadField: r0 = r1->field_13
    //     0x937cc0: ldur            w0, [x1, #0x13]
    // 0x937cc4: DecompressPointer r0
    //     0x937cc4: add             x0, x0, HEAP, lsl #32
    // 0x937cc8: stur            x0, [fp, #-8]
    // 0x937ccc: r16 = Instance_AnimationStatus
    //     0x937ccc: ldr             x16, [PP, #0x4eb0]  ; [pp+0x4eb0] Obj!AnimationStatus@e37301
    // 0x937cd0: cmp             w2, w16
    // 0x937cd4: b.eq            #0x937ce4
    // 0x937cd8: r16 = Instance_AnimationStatus
    //     0x937cd8: ldr             x16, [PP, #0x4eb8]  ; [pp+0x4eb8] Obj!AnimationStatus@e372a1
    // 0x937cdc: cmp             w2, w16
    // 0x937ce0: b.ne            #0x937cf0
    // 0x937ce4: r0 = build()
    //     0x937ce4: bl              #0xa1c230  ; [package:flutter/src/widgets/pop_scope.dart] _PopScopeState::build
    // 0x937ce8: mov             x2, x0
    // 0x937cec: b               #0x937d0c
    // 0x937cf0: r16 = Instance_AnimationStatus
    //     0x937cf0: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0x937cf4: cmp             w2, w16
    // 0x937cf8: b.eq            #0x937d08
    // 0x937cfc: r16 = Instance_AnimationStatus
    //     0x937cfc: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0x937d00: cmp             w2, w16
    // 0x937d04: b.eq            #0x937d08
    // 0x937d08: r2 = false
    //     0x937d08: add             x2, NULL, #0x30  ; false
    // 0x937d0c: ldur            x1, [fp, #-8]
    // 0x937d10: r0 = allowSnapshotting=()
    //     0x937d10: bl              #0x937398  ; [package:flutter/src/widgets/snapshot_widget.dart] SnapshotController::allowSnapshotting=
    // 0x937d14: r0 = Null
    //     0x937d14: mov             x0, NULL
    // 0x937d18: LeaveFrame
    //     0x937d18: mov             SP, fp
    //     0x937d1c: ldp             fp, lr, [SP], #0x10
    // 0x937d20: ret
    //     0x937d20: ret             
    // 0x937d24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x937d24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937d28: b               #0x937cc0
  }
  [closure] void onAnimationValueChange(dynamic) {
    // ** addr: 0x937d2c, size: 0x38
    // 0x937d2c: EnterFrame
    //     0x937d2c: stp             fp, lr, [SP, #-0x10]!
    //     0x937d30: mov             fp, SP
    // 0x937d34: ldr             x0, [fp, #0x10]
    // 0x937d38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x937d38: ldur            w1, [x0, #0x17]
    // 0x937d3c: DecompressPointer r1
    //     0x937d3c: add             x1, x1, HEAP, lsl #32
    // 0x937d40: CheckStackOverflow
    //     0x937d40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x937d44: cmp             SP, x16
    //     0x937d48: b.ls            #0x937d5c
    // 0x937d4c: r0 = onAnimationValueChange()
    //     0x937d4c: bl              #0x937d64  ; [package:flutter/src/material/page_transitions_theme.dart] __ZoomExitTransitionState&State&_ZoomTransitionBase::onAnimationValueChange
    // 0x937d50: LeaveFrame
    //     0x937d50: mov             SP, fp
    //     0x937d54: ldp             fp, lr, [SP], #0x10
    // 0x937d58: ret
    //     0x937d58: ret             
    // 0x937d5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x937d5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937d60: b               #0x937d4c
  }
  _ onAnimationValueChange(/* No info */) {
    // ** addr: 0x937d64, size: 0x17c
    // 0x937d64: EnterFrame
    //     0x937d64: stp             fp, lr, [SP, #-0x10]!
    //     0x937d68: mov             fp, SP
    // 0x937d6c: AllocStack(0x8)
    //     0x937d6c: sub             SP, SP, #8
    // 0x937d70: SetupParameters(__ZoomExitTransitionState&State&_ZoomTransitionBase this /* r1 => r0, fp-0x8 */)
    //     0x937d70: mov             x0, x1
    //     0x937d74: stur            x1, [fp, #-8]
    // 0x937d78: CheckStackOverflow
    //     0x937d78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x937d7c: cmp             SP, x16
    //     0x937d80: b.ls            #0x937ebc
    // 0x937d84: LoadField: r1 = r0->field_1b
    //     0x937d84: ldur            w1, [x0, #0x1b]
    // 0x937d88: DecompressPointer r1
    //     0x937d88: add             x1, x1, HEAP, lsl #32
    // 0x937d8c: r16 = Sentinel
    //     0x937d8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x937d90: cmp             w1, w16
    // 0x937d94: b.eq            #0x937ec4
    // 0x937d98: LoadField: r2 = r1->field_f
    //     0x937d98: ldur            w2, [x1, #0xf]
    // 0x937d9c: DecompressPointer r2
    //     0x937d9c: add             x2, x2, HEAP, lsl #32
    // 0x937da0: LoadField: r3 = r1->field_b
    //     0x937da0: ldur            w3, [x1, #0xb]
    // 0x937da4: DecompressPointer r3
    //     0x937da4: add             x3, x3, HEAP, lsl #32
    // 0x937da8: mov             x1, x2
    // 0x937dac: mov             x2, x3
    // 0x937db0: r0 = evaluate()
    //     0x937db0: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x937db4: LoadField: d0 = r0->field_7
    //     0x937db4: ldur            d0, [x0, #7]
    // 0x937db8: d1 = 1.000000
    //     0x937db8: fmov            d1, #1.00000000
    // 0x937dbc: fcmp            d0, d1
    // 0x937dc0: b.ne            #0x937e70
    // 0x937dc4: ldur            x2, [fp, #-8]
    // 0x937dc8: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x937dc8: ldur            w1, [x2, #0x17]
    // 0x937dcc: DecompressPointer r1
    //     0x937dcc: add             x1, x1, HEAP, lsl #32
    // 0x937dd0: r16 = Sentinel
    //     0x937dd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x937dd4: cmp             w1, w16
    // 0x937dd8: b.eq            #0x937ed0
    // 0x937ddc: r0 = LoadClassIdInstr(r1)
    //     0x937ddc: ldur            x0, [x1, #-1]
    //     0x937de0: ubfx            x0, x0, #0xc, #0x14
    // 0x937de4: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x937de4: movz            x17, #0x276f
    //     0x937de8: movk            x17, #0x1, lsl #16
    //     0x937dec: add             lr, x0, x17
    //     0x937df0: ldr             lr, [x21, lr, lsl #3]
    //     0x937df4: blr             lr
    // 0x937df8: LoadField: d0 = r0->field_7
    //     0x937df8: ldur            d0, [x0, #7]
    // 0x937dfc: d1 = 0.000000
    //     0x937dfc: eor             v1.16b, v1.16b, v1.16b
    // 0x937e00: fcmp            d0, d1
    // 0x937e04: b.eq            #0x937e40
    // 0x937e08: ldur            x2, [fp, #-8]
    // 0x937e0c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x937e0c: ldur            w1, [x2, #0x17]
    // 0x937e10: DecompressPointer r1
    //     0x937e10: add             x1, x1, HEAP, lsl #32
    // 0x937e14: r0 = LoadClassIdInstr(r1)
    //     0x937e14: ldur            x0, [x1, #-1]
    //     0x937e18: ubfx            x0, x0, #0xc, #0x14
    // 0x937e1c: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x937e1c: movz            x17, #0x276f
    //     0x937e20: movk            x17, #0x1, lsl #16
    //     0x937e24: add             lr, x0, x17
    //     0x937e28: ldr             lr, [x21, lr, lsl #3]
    //     0x937e2c: blr             lr
    // 0x937e30: LoadField: d0 = r0->field_7
    //     0x937e30: ldur            d0, [x0, #7]
    // 0x937e34: d1 = 1.000000
    //     0x937e34: fmov            d1, #1.00000000
    // 0x937e38: fcmp            d0, d1
    // 0x937e3c: b.ne            #0x937e68
    // 0x937e40: ldur            x0, [fp, #-8]
    // 0x937e44: LoadField: r1 = r0->field_13
    //     0x937e44: ldur            w1, [x0, #0x13]
    // 0x937e48: DecompressPointer r1
    //     0x937e48: add             x1, x1, HEAP, lsl #32
    // 0x937e4c: LoadField: r0 = r1->field_23
    //     0x937e4c: ldur            w0, [x1, #0x23]
    // 0x937e50: DecompressPointer r0
    //     0x937e50: add             x0, x0, HEAP, lsl #32
    // 0x937e54: tbnz            w0, #4, #0x937eac
    // 0x937e58: r0 = false
    //     0x937e58: add             x0, NULL, #0x30  ; false
    // 0x937e5c: StoreField: r1->field_23 = r0
    //     0x937e5c: stur            w0, [x1, #0x23]
    // 0x937e60: r0 = notifyListeners()
    //     0x937e60: bl              #0x6462b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners
    // 0x937e64: b               #0x937eac
    // 0x937e68: ldur            x0, [fp, #-8]
    // 0x937e6c: b               #0x937e74
    // 0x937e70: ldur            x0, [fp, #-8]
    // 0x937e74: LoadField: r1 = r0->field_13
    //     0x937e74: ldur            w1, [x0, #0x13]
    // 0x937e78: DecompressPointer r1
    //     0x937e78: add             x1, x1, HEAP, lsl #32
    // 0x937e7c: LoadField: r2 = r0->field_b
    //     0x937e7c: ldur            w2, [x0, #0xb]
    // 0x937e80: DecompressPointer r2
    //     0x937e80: add             x2, x2, HEAP, lsl #32
    // 0x937e84: cmp             w2, NULL
    // 0x937e88: b.eq            #0x937edc
    // 0x937e8c: LoadField: r0 = r2->field_f
    //     0x937e8c: ldur            w0, [x2, #0xf]
    // 0x937e90: DecompressPointer r0
    //     0x937e90: add             x0, x0, HEAP, lsl #32
    // 0x937e94: LoadField: r2 = r1->field_23
    //     0x937e94: ldur            w2, [x1, #0x23]
    // 0x937e98: DecompressPointer r2
    //     0x937e98: add             x2, x2, HEAP, lsl #32
    // 0x937e9c: cmp             w0, w2
    // 0x937ea0: b.eq            #0x937eac
    // 0x937ea4: StoreField: r1->field_23 = r0
    //     0x937ea4: stur            w0, [x1, #0x23]
    // 0x937ea8: r0 = notifyListeners()
    //     0x937ea8: bl              #0x6462b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners
    // 0x937eac: r0 = Null
    //     0x937eac: mov             x0, NULL
    // 0x937eb0: LeaveFrame
    //     0x937eb0: mov             SP, fp
    //     0x937eb4: ldp             fp, lr, [SP], #0x10
    // 0x937eb8: ret
    //     0x937eb8: ret             
    // 0x937ebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x937ebc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937ec0: b               #0x937d84
    // 0x937ec4: r9 = scaleTransition
    //     0x937ec4: add             x9, PP, #0x24, lsl #12  ; [pp+0x24f18] Field <__ZoomExitTransitionState&State&<EMAIL>>: late (offset: 0x1c)
    //     0x937ec8: ldr             x9, [x9, #0xf18]
    // 0x937ecc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x937ecc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x937ed0: r9 = fadeTransition
    //     0x937ed0: add             x9, PP, #0x24, lsl #12  ; [pp+0x24f20] Field <__ZoomExitTransitionState&State&<EMAIL>>: late (offset: 0x18)
    //     0x937ed4: ldr             x9, [x9, #0xf20]
    // 0x937ed8: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x937ed8: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x937edc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x937edc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ __ZoomExitTransitionState&State&_ZoomTransitionBase(/* No info */) {
    // ** addr: 0xa90490, size: 0xa8
    // 0xa90490: EnterFrame
    //     0xa90490: stp             fp, lr, [SP, #-0x10]!
    //     0xa90494: mov             fp, SP
    // 0xa90498: AllocStack(0x10)
    //     0xa90498: sub             SP, SP, #0x10
    // 0xa9049c: r0 = Sentinel
    //     0xa9049c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa904a0: stur            x1, [fp, #-8]
    // 0xa904a4: CheckStackOverflow
    //     0xa904a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa904a8: cmp             SP, x16
    //     0xa904ac: b.ls            #0xa90530
    // 0xa904b0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa904b0: stur            w0, [x1, #0x17]
    // 0xa904b4: StoreField: r1->field_1b = r0
    //     0xa904b4: stur            w0, [x1, #0x1b]
    // 0xa904b8: r0 = SnapshotController()
    //     0xa904b8: bl              #0xa90538  ; AllocateSnapshotControllerStub -> SnapshotController (size=0x28)
    // 0xa904bc: mov             x1, x0
    // 0xa904c0: r0 = false
    //     0xa904c0: add             x0, NULL, #0x30  ; false
    // 0xa904c4: stur            x1, [fp, #-0x10]
    // 0xa904c8: StoreField: r1->field_23 = r0
    //     0xa904c8: stur            w0, [x1, #0x23]
    // 0xa904cc: StoreField: r1->field_7 = rZR
    //     0xa904cc: stur            xzr, [x1, #7]
    // 0xa904d0: StoreField: r1->field_13 = rZR
    //     0xa904d0: stur            xzr, [x1, #0x13]
    // 0xa904d4: StoreField: r1->field_1b = rZR
    //     0xa904d4: stur            xzr, [x1, #0x1b]
    // 0xa904d8: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0xa904d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa904dc: ldr             x0, [x0, #0xca8]
    //     0xa904e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa904e4: cmp             w0, w16
    //     0xa904e8: b.ne            #0xa904f4
    //     0xa904ec: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0xa904f0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa904f4: mov             x1, x0
    // 0xa904f8: ldur            x0, [fp, #-0x10]
    // 0xa904fc: StoreField: r0->field_f = r1
    //     0xa904fc: stur            w1, [x0, #0xf]
    // 0xa90500: ldur            x1, [fp, #-8]
    // 0xa90504: StoreField: r1->field_13 = r0
    //     0xa90504: stur            w0, [x1, #0x13]
    //     0xa90508: ldurb           w16, [x1, #-1]
    //     0xa9050c: ldurb           w17, [x0, #-1]
    //     0xa90510: and             x16, x17, x16, lsr #2
    //     0xa90514: tst             x16, HEAP, lsr #32
    //     0xa90518: b.eq            #0xa90520
    //     0xa9051c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa90520: r0 = Null
    //     0xa90520: mov             x0, NULL
    // 0xa90524: LeaveFrame
    //     0xa90524: mov             SP, fp
    //     0xa90528: ldp             fp, lr, [SP], #0x10
    // 0xa9052c: ret
    //     0xa9052c: ret             
    // 0xa90530: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa90530: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa90534: b               #0xa904b0
  }
}

// class id: 4282, size: 0x24, field offset: 0x20
class _ZoomExitTransitionState extends __ZoomExitTransitionState&State&_ZoomTransitionBase {

  late _ZoomExitTransitionPainter delegate; // offset: 0x20
  static late final Animatable<double> _fadeOutTransition; // offset: 0xaec
  static late final Animatable<double> _scaleDownTransition; // offset: 0xaf4
  static late final Animatable<double> _scaleUpTransition; // offset: 0xaf0

  _ initState(/* No info */) {
    // ** addr: 0x9375a0, size: 0xfc
    // 0x9375a0: EnterFrame
    //     0x9375a0: stp             fp, lr, [SP, #-0x10]!
    //     0x9375a4: mov             fp, SP
    // 0x9375a8: AllocStack(0x28)
    //     0x9375a8: sub             SP, SP, #0x28
    // 0x9375ac: SetupParameters(_ZoomExitTransitionState this /* r1 => r0, fp-0x8 */)
    //     0x9375ac: mov             x0, x1
    //     0x9375b0: stur            x1, [fp, #-8]
    // 0x9375b4: CheckStackOverflow
    //     0x9375b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9375b8: cmp             SP, x16
    //     0x9375bc: b.ls            #0x937678
    // 0x9375c0: mov             x1, x0
    // 0x9375c4: r0 = _updateAnimations()
    //     0x9375c4: bl              #0x9378d4  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionState::_updateAnimations
    // 0x9375c8: ldur            x0, [fp, #-8]
    // 0x9375cc: LoadField: r1 = r0->field_b
    //     0x9375cc: ldur            w1, [x0, #0xb]
    // 0x9375d0: DecompressPointer r1
    //     0x9375d0: add             x1, x1, HEAP, lsl #32
    // 0x9375d4: cmp             w1, NULL
    // 0x9375d8: b.eq            #0x937680
    // 0x9375dc: LoadField: r5 = r1->field_13
    //     0x9375dc: ldur            w5, [x1, #0x13]
    // 0x9375e0: DecompressPointer r5
    //     0x9375e0: add             x5, x5, HEAP, lsl #32
    // 0x9375e4: stur            x5, [fp, #-0x28]
    // 0x9375e8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x9375e8: ldur            w3, [x0, #0x17]
    // 0x9375ec: DecompressPointer r3
    //     0x9375ec: add             x3, x3, HEAP, lsl #32
    // 0x9375f0: r16 = Sentinel
    //     0x9375f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9375f4: cmp             w3, w16
    // 0x9375f8: b.eq            #0x937684
    // 0x9375fc: stur            x3, [fp, #-0x20]
    // 0x937600: LoadField: r6 = r0->field_1b
    //     0x937600: ldur            w6, [x0, #0x1b]
    // 0x937604: DecompressPointer r6
    //     0x937604: add             x6, x6, HEAP, lsl #32
    // 0x937608: r16 = Sentinel
    //     0x937608: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x93760c: cmp             w6, w16
    // 0x937610: b.eq            #0x937690
    // 0x937614: stur            x6, [fp, #-0x18]
    // 0x937618: LoadField: r2 = r1->field_b
    //     0x937618: ldur            w2, [x1, #0xb]
    // 0x93761c: DecompressPointer r2
    //     0x93761c: add             x2, x2, HEAP, lsl #32
    // 0x937620: stur            x2, [fp, #-0x10]
    // 0x937624: r0 = _ZoomExitTransitionPainter()
    //     0x937624: bl              #0x9378c8  ; Allocate_ZoomExitTransitionPainterStub -> _ZoomExitTransitionPainter (size=0x40)
    // 0x937628: mov             x1, x0
    // 0x93762c: ldur            x2, [fp, #-0x10]
    // 0x937630: ldur            x3, [fp, #-0x20]
    // 0x937634: ldur            x5, [fp, #-0x28]
    // 0x937638: ldur            x6, [fp, #-0x18]
    // 0x93763c: stur            x0, [fp, #-0x10]
    // 0x937640: r0 = _ZoomExitTransitionPainter()
    //     0x937640: bl              #0x9376c0  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionPainter::_ZoomExitTransitionPainter
    // 0x937644: ldur            x0, [fp, #-0x10]
    // 0x937648: ldur            x1, [fp, #-8]
    // 0x93764c: StoreField: r1->field_1f = r0
    //     0x93764c: stur            w0, [x1, #0x1f]
    //     0x937650: ldurb           w16, [x1, #-1]
    //     0x937654: ldurb           w17, [x0, #-1]
    //     0x937658: and             x16, x17, x16, lsr #2
    //     0x93765c: tst             x16, HEAP, lsr #32
    //     0x937660: b.eq            #0x937668
    //     0x937664: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x937668: r0 = Null
    //     0x937668: mov             x0, NULL
    // 0x93766c: LeaveFrame
    //     0x93766c: mov             SP, fp
    //     0x937670: ldp             fp, lr, [SP], #0x10
    // 0x937674: ret
    //     0x937674: ret             
    // 0x937678: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x937678: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93767c: b               #0x9375c0
    // 0x937680: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x937680: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x937684: r9 = fadeTransition
    //     0x937684: add             x9, PP, #0x24, lsl #12  ; [pp+0x24f20] Field <__ZoomExitTransitionState&State&<EMAIL>>: late (offset: 0x18)
    //     0x937688: ldr             x9, [x9, #0xf20]
    // 0x93768c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x93768c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x937690: r9 = scaleTransition
    //     0x937690: add             x9, PP, #0x24, lsl #12  ; [pp+0x24f18] Field <__ZoomExitTransitionState&State&<EMAIL>>: late (offset: 0x1c)
    //     0x937694: ldr             x9, [x9, #0xf18]
    // 0x937698: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x937698: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _updateAnimations(/* No info */) {
    // ** addr: 0x9378d4, size: 0x230
    // 0x9378d4: EnterFrame
    //     0x9378d4: stp             fp, lr, [SP, #-0x10]!
    //     0x9378d8: mov             fp, SP
    // 0x9378dc: AllocStack(0x10)
    //     0x9378dc: sub             SP, SP, #0x10
    // 0x9378e0: SetupParameters(_ZoomExitTransitionState this /* r1 => r2, fp-0x8 */)
    //     0x9378e0: mov             x2, x1
    //     0x9378e4: stur            x1, [fp, #-8]
    // 0x9378e8: CheckStackOverflow
    //     0x9378e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9378ec: cmp             SP, x16
    //     0x9378f0: b.ls            #0x937ae4
    // 0x9378f4: LoadField: r0 = r2->field_b
    //     0x9378f4: ldur            w0, [x2, #0xb]
    // 0x9378f8: DecompressPointer r0
    //     0x9378f8: add             x0, x0, HEAP, lsl #32
    // 0x9378fc: cmp             w0, NULL
    // 0x937900: b.eq            #0x937aec
    // 0x937904: LoadField: r1 = r0->field_13
    //     0x937904: ldur            w1, [x0, #0x13]
    // 0x937908: DecompressPointer r1
    //     0x937908: add             x1, x1, HEAP, lsl #32
    // 0x93790c: tbnz            w1, #4, #0x93795c
    // 0x937910: r0 = InitLateStaticField(0xaec) // [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionState::_fadeOutTransition
    //     0x937910: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x937914: ldr             x0, [x0, #0x15d8]
    //     0x937918: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93791c: cmp             w0, w16
    //     0x937920: b.ne            #0x937930
    //     0x937924: add             x2, PP, #0x24, lsl #12  ; [pp+0x24f68] Field <_ZoomExitTransitionState@579490068._fadeOutTransition@579490068>: static late final (offset: 0xaec)
    //     0x937928: ldr             x2, [x2, #0xf68]
    //     0x93792c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x937930: mov             x1, x0
    // 0x937934: ldur            x0, [fp, #-8]
    // 0x937938: LoadField: r2 = r0->field_b
    //     0x937938: ldur            w2, [x0, #0xb]
    // 0x93793c: DecompressPointer r2
    //     0x93793c: add             x2, x2, HEAP, lsl #32
    // 0x937940: cmp             w2, NULL
    // 0x937944: b.eq            #0x937af0
    // 0x937948: LoadField: r3 = r2->field_b
    //     0x937948: ldur            w3, [x2, #0xb]
    // 0x93794c: DecompressPointer r3
    //     0x93794c: add             x3, x3, HEAP, lsl #32
    // 0x937950: mov             x2, x3
    // 0x937954: r0 = animate()
    //     0x937954: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x937958: b               #0x937964
    // 0x93795c: r0 = Instance__AlwaysCompleteAnimation
    //     0x93795c: add             x0, PP, #0x24, lsl #12  ; [pp+0x24f70] Obj!_AlwaysCompleteAnimation@e25961
    //     0x937960: ldr             x0, [x0, #0xf70]
    // 0x937964: ldur            x2, [fp, #-8]
    // 0x937968: ArrayStore: r2[0] = r0  ; List_4
    //     0x937968: stur            w0, [x2, #0x17]
    //     0x93796c: ldurb           w16, [x2, #-1]
    //     0x937970: ldurb           w17, [x0, #-1]
    //     0x937974: and             x16, x17, x16, lsr #2
    //     0x937978: tst             x16, HEAP, lsr #32
    //     0x93797c: b.eq            #0x937984
    //     0x937980: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x937984: LoadField: r0 = r2->field_b
    //     0x937984: ldur            w0, [x2, #0xb]
    // 0x937988: DecompressPointer r0
    //     0x937988: add             x0, x0, HEAP, lsl #32
    // 0x93798c: cmp             w0, NULL
    // 0x937990: b.eq            #0x937af4
    // 0x937994: LoadField: r1 = r0->field_13
    //     0x937994: ldur            w1, [x0, #0x13]
    // 0x937998: DecompressPointer r1
    //     0x937998: add             x1, x1, HEAP, lsl #32
    // 0x93799c: tbnz            w1, #4, #0x9379c8
    // 0x9379a0: r0 = InitLateStaticField(0xaf4) // [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionState::_scaleDownTransition
    //     0x9379a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9379a4: ldr             x0, [x0, #0x15e8]
    //     0x9379a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9379ac: cmp             w0, w16
    //     0x9379b0: b.ne            #0x9379c0
    //     0x9379b4: add             x2, PP, #0x24, lsl #12  ; [pp+0x24f78] Field <_ZoomExitTransitionState@579490068._scaleDownTransition@579490068>: static late final (offset: 0xaf4)
    //     0x9379b8: ldr             x2, [x2, #0xf78]
    //     0x9379bc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9379c0: mov             x1, x0
    // 0x9379c4: b               #0x9379ec
    // 0x9379c8: r0 = InitLateStaticField(0xaf0) // [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionState::_scaleUpTransition
    //     0x9379c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9379cc: ldr             x0, [x0, #0x15e0]
    //     0x9379d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9379d4: cmp             w0, w16
    //     0x9379d8: b.ne            #0x9379e8
    //     0x9379dc: add             x2, PP, #0x24, lsl #12  ; [pp+0x24f80] Field <_ZoomExitTransitionState@579490068._scaleUpTransition@579490068>: static late final (offset: 0xaf0)
    //     0x9379e0: ldr             x2, [x2, #0xf80]
    //     0x9379e4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9379e8: mov             x1, x0
    // 0x9379ec: ldur            x0, [fp, #-8]
    // 0x9379f0: LoadField: r2 = r0->field_b
    //     0x9379f0: ldur            w2, [x0, #0xb]
    // 0x9379f4: DecompressPointer r2
    //     0x9379f4: add             x2, x2, HEAP, lsl #32
    // 0x9379f8: cmp             w2, NULL
    // 0x9379fc: b.eq            #0x937af8
    // 0x937a00: LoadField: r3 = r2->field_b
    //     0x937a00: ldur            w3, [x2, #0xb]
    // 0x937a04: DecompressPointer r3
    //     0x937a04: add             x3, x3, HEAP, lsl #32
    // 0x937a08: mov             x2, x3
    // 0x937a0c: r0 = animate()
    //     0x937a0c: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x937a10: ldur            x3, [fp, #-8]
    // 0x937a14: StoreField: r3->field_1b = r0
    //     0x937a14: stur            w0, [x3, #0x1b]
    //     0x937a18: ldurb           w16, [x3, #-1]
    //     0x937a1c: ldurb           w17, [x0, #-1]
    //     0x937a20: and             x16, x17, x16, lsr #2
    //     0x937a24: tst             x16, HEAP, lsr #32
    //     0x937a28: b.eq            #0x937a30
    //     0x937a2c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x937a30: LoadField: r0 = r3->field_b
    //     0x937a30: ldur            w0, [x3, #0xb]
    // 0x937a34: DecompressPointer r0
    //     0x937a34: add             x0, x0, HEAP, lsl #32
    // 0x937a38: cmp             w0, NULL
    // 0x937a3c: b.eq            #0x937afc
    // 0x937a40: LoadField: r4 = r0->field_b
    //     0x937a40: ldur            w4, [x0, #0xb]
    // 0x937a44: DecompressPointer r4
    //     0x937a44: add             x4, x4, HEAP, lsl #32
    // 0x937a48: mov             x2, x3
    // 0x937a4c: stur            x4, [fp, #-0x10]
    // 0x937a50: r1 = Function 'onAnimationValueChange':.
    //     0x937a50: add             x1, PP, #0x24, lsl #12  ; [pp+0x24f08] AnonymousClosure: (0x937d2c), in [package:flutter/src/material/page_transitions_theme.dart] __ZoomExitTransitionState&State&_ZoomTransitionBase::onAnimationValueChange (0x937d64)
    //     0x937a54: ldr             x1, [x1, #0xf08]
    // 0x937a58: r0 = AllocateClosure()
    //     0x937a58: bl              #0xec1630  ; AllocateClosureStub
    // 0x937a5c: ldur            x1, [fp, #-0x10]
    // 0x937a60: r2 = LoadClassIdInstr(r1)
    //     0x937a60: ldur            x2, [x1, #-1]
    //     0x937a64: ubfx            x2, x2, #0xc, #0x14
    // 0x937a68: mov             x16, x0
    // 0x937a6c: mov             x0, x2
    // 0x937a70: mov             x2, x16
    // 0x937a74: r0 = GDT[cid_x0 + 0xc407]()
    //     0x937a74: movz            x17, #0xc407
    //     0x937a78: add             lr, x0, x17
    //     0x937a7c: ldr             lr, [x21, lr, lsl #3]
    //     0x937a80: blr             lr
    // 0x937a84: ldur            x2, [fp, #-8]
    // 0x937a88: LoadField: r0 = r2->field_b
    //     0x937a88: ldur            w0, [x2, #0xb]
    // 0x937a8c: DecompressPointer r0
    //     0x937a8c: add             x0, x0, HEAP, lsl #32
    // 0x937a90: cmp             w0, NULL
    // 0x937a94: b.eq            #0x937b00
    // 0x937a98: LoadField: r3 = r0->field_b
    //     0x937a98: ldur            w3, [x0, #0xb]
    // 0x937a9c: DecompressPointer r3
    //     0x937a9c: add             x3, x3, HEAP, lsl #32
    // 0x937aa0: stur            x3, [fp, #-0x10]
    // 0x937aa4: r1 = Function 'onAnimationStatusChange':.
    //     0x937aa4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24f10] AnonymousClosure: (0x937c6c), in [package:flutter/src/material/page_transitions_theme.dart] __ZoomExitTransitionState&State&_ZoomTransitionBase::onAnimationStatusChange (0x937ca8)
    //     0x937aa8: ldr             x1, [x1, #0xf10]
    // 0x937aac: r0 = AllocateClosure()
    //     0x937aac: bl              #0xec1630  ; AllocateClosureStub
    // 0x937ab0: ldur            x1, [fp, #-0x10]
    // 0x937ab4: r2 = LoadClassIdInstr(r1)
    //     0x937ab4: ldur            x2, [x1, #-1]
    //     0x937ab8: ubfx            x2, x2, #0xc, #0x14
    // 0x937abc: mov             x16, x0
    // 0x937ac0: mov             x0, x2
    // 0x937ac4: mov             x2, x16
    // 0x937ac8: r0 = GDT[cid_x0 + 0x7a1]()
    //     0x937ac8: add             lr, x0, #0x7a1
    //     0x937acc: ldr             lr, [x21, lr, lsl #3]
    //     0x937ad0: blr             lr
    // 0x937ad4: r0 = Null
    //     0x937ad4: mov             x0, NULL
    // 0x937ad8: LeaveFrame
    //     0x937ad8: mov             SP, fp
    //     0x937adc: ldp             fp, lr, [SP], #0x10
    // 0x937ae0: ret
    //     0x937ae0: ret             
    // 0x937ae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x937ae4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937ae8: b               #0x9378f4
    // 0x937aec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x937aec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x937af0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x937af0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x937af4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x937af4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x937af8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x937af8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x937afc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x937afc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x937b00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x937b00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static Animatable<double> _scaleUpTransition() {
    // ** addr: 0x937b04, size: 0x7c
    // 0x937b04: EnterFrame
    //     0x937b04: stp             fp, lr, [SP, #-0x10]!
    //     0x937b08: mov             fp, SP
    // 0x937b0c: AllocStack(0x8)
    //     0x937b0c: sub             SP, SP, #8
    // 0x937b10: CheckStackOverflow
    //     0x937b10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x937b14: cmp             SP, x16
    //     0x937b18: b.ls            #0x937b78
    // 0x937b1c: r1 = <double>
    //     0x937b1c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x937b20: r0 = Tween()
    //     0x937b20: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x937b24: mov             x1, x0
    // 0x937b28: r0 = 1.000000
    //     0x937b28: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x937b2c: stur            x1, [fp, #-8]
    // 0x937b30: StoreField: r1->field_b = r0
    //     0x937b30: stur            w0, [x1, #0xb]
    // 0x937b34: r0 = 1.050000
    //     0x937b34: add             x0, PP, #0x24, lsl #12  ; [pp+0x24f88] 1.05
    //     0x937b38: ldr             x0, [x0, #0xf88]
    // 0x937b3c: StoreField: r1->field_f = r0
    //     0x937b3c: stur            w0, [x1, #0xf]
    // 0x937b40: r0 = InitLateStaticField(0xad8) // [package:flutter/src/material/page_transitions_theme.dart] _ZoomPageTransition::_scaleCurveSequence
    //     0x937b40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x937b44: ldr             x0, [x0, #0x15b0]
    //     0x937b48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x937b4c: cmp             w0, w16
    //     0x937b50: b.ne            #0x937b60
    //     0x937b54: add             x2, PP, #0x24, lsl #12  ; [pp+0x24f90] Field <_ZoomPageTransition@579490068._scaleCurveSequence@579490068>: static late final (offset: 0xad8)
    //     0x937b58: ldr             x2, [x2, #0xf90]
    //     0x937b5c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x937b60: ldur            x1, [fp, #-8]
    // 0x937b64: mov             x2, x0
    // 0x937b68: r0 = chain()
    //     0x937b68: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x937b6c: LeaveFrame
    //     0x937b6c: mov             SP, fp
    //     0x937b70: ldp             fp, lr, [SP], #0x10
    // 0x937b74: ret
    //     0x937b74: ret             
    // 0x937b78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x937b78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937b7c: b               #0x937b1c
  }
  static Animatable<double> _scaleDownTransition() {
    // ** addr: 0x937b80, size: 0x7c
    // 0x937b80: EnterFrame
    //     0x937b80: stp             fp, lr, [SP, #-0x10]!
    //     0x937b84: mov             fp, SP
    // 0x937b88: AllocStack(0x8)
    //     0x937b88: sub             SP, SP, #8
    // 0x937b8c: CheckStackOverflow
    //     0x937b8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x937b90: cmp             SP, x16
    //     0x937b94: b.ls            #0x937bf4
    // 0x937b98: r1 = <double>
    //     0x937b98: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x937b9c: r0 = Tween()
    //     0x937b9c: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x937ba0: mov             x1, x0
    // 0x937ba4: r0 = 1.000000
    //     0x937ba4: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x937ba8: stur            x1, [fp, #-8]
    // 0x937bac: StoreField: r1->field_b = r0
    //     0x937bac: stur            w0, [x1, #0xb]
    // 0x937bb0: r0 = 0.900000
    //     0x937bb0: add             x0, PP, #0x24, lsl #12  ; [pp+0x24fe0] 0.9
    //     0x937bb4: ldr             x0, [x0, #0xfe0]
    // 0x937bb8: StoreField: r1->field_f = r0
    //     0x937bb8: stur            w0, [x1, #0xf]
    // 0x937bbc: r0 = InitLateStaticField(0xad8) // [package:flutter/src/material/page_transitions_theme.dart] _ZoomPageTransition::_scaleCurveSequence
    //     0x937bbc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x937bc0: ldr             x0, [x0, #0x15b0]
    //     0x937bc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x937bc8: cmp             w0, w16
    //     0x937bcc: b.ne            #0x937bdc
    //     0x937bd0: add             x2, PP, #0x24, lsl #12  ; [pp+0x24f90] Field <_ZoomPageTransition@579490068._scaleCurveSequence@579490068>: static late final (offset: 0xad8)
    //     0x937bd4: ldr             x2, [x2, #0xf90]
    //     0x937bd8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x937bdc: ldur            x1, [fp, #-8]
    // 0x937be0: mov             x2, x0
    // 0x937be4: r0 = chain()
    //     0x937be4: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x937be8: LeaveFrame
    //     0x937be8: mov             SP, fp
    //     0x937bec: ldp             fp, lr, [SP], #0x10
    // 0x937bf0: ret
    //     0x937bf0: ret             
    // 0x937bf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x937bf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937bf8: b               #0x937b98
  }
  static Animatable<double> _fadeOutTransition() {
    // ** addr: 0x937bfc, size: 0x70
    // 0x937bfc: EnterFrame
    //     0x937bfc: stp             fp, lr, [SP, #-0x10]!
    //     0x937c00: mov             fp, SP
    // 0x937c04: AllocStack(0x8)
    //     0x937c04: sub             SP, SP, #8
    // 0x937c08: CheckStackOverflow
    //     0x937c08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x937c0c: cmp             SP, x16
    //     0x937c10: b.ls            #0x937c64
    // 0x937c14: r1 = <double>
    //     0x937c14: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x937c18: r0 = Tween()
    //     0x937c18: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x937c1c: mov             x2, x0
    // 0x937c20: r0 = 1.000000
    //     0x937c20: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x937c24: stur            x2, [fp, #-8]
    // 0x937c28: StoreField: r2->field_b = r0
    //     0x937c28: stur            w0, [x2, #0xb]
    // 0x937c2c: r0 = 0.000000
    //     0x937c2c: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x937c30: StoreField: r2->field_f = r0
    //     0x937c30: stur            w0, [x2, #0xf]
    // 0x937c34: r1 = <double>
    //     0x937c34: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x937c38: r0 = CurveTween()
    //     0x937c38: bl              #0x796a9c  ; AllocateCurveTweenStub -> CurveTween (size=0x10)
    // 0x937c3c: mov             x1, x0
    // 0x937c40: r0 = Instance_Interval
    //     0x937c40: add             x0, PP, #0x24, lsl #12  ; [pp+0x24fe8] Obj!Interval@e14fe1
    //     0x937c44: ldr             x0, [x0, #0xfe8]
    // 0x937c48: StoreField: r1->field_b = r0
    //     0x937c48: stur            w0, [x1, #0xb]
    // 0x937c4c: mov             x2, x1
    // 0x937c50: ldur            x1, [fp, #-8]
    // 0x937c54: r0 = chain()
    //     0x937c54: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x937c58: LeaveFrame
    //     0x937c58: mov             SP, fp
    //     0x937c5c: ldp             fp, lr, [SP], #0x10
    // 0x937c60: ret
    //     0x937c60: ret             
    // 0x937c64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x937c64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937c68: b               #0x937c14
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x987f00, size: 0x26c
    // 0x987f00: EnterFrame
    //     0x987f00: stp             fp, lr, [SP, #-0x10]!
    //     0x987f04: mov             fp, SP
    // 0x987f08: AllocStack(0x30)
    //     0x987f08: sub             SP, SP, #0x30
    // 0x987f0c: SetupParameters(_ZoomExitTransitionState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x987f0c: mov             x4, x1
    //     0x987f10: mov             x3, x2
    //     0x987f14: stur            x1, [fp, #-8]
    //     0x987f18: stur            x2, [fp, #-0x10]
    // 0x987f1c: CheckStackOverflow
    //     0x987f1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x987f20: cmp             SP, x16
    //     0x987f24: b.ls            #0x988138
    // 0x987f28: mov             x0, x3
    // 0x987f2c: r2 = Null
    //     0x987f2c: mov             x2, NULL
    // 0x987f30: r1 = Null
    //     0x987f30: mov             x1, NULL
    // 0x987f34: r4 = 60
    //     0x987f34: movz            x4, #0x3c
    // 0x987f38: branchIfSmi(r0, 0x987f44)
    //     0x987f38: tbz             w0, #0, #0x987f44
    // 0x987f3c: r4 = LoadClassIdInstr(r0)
    //     0x987f3c: ldur            x4, [x0, #-1]
    //     0x987f40: ubfx            x4, x4, #0xc, #0x14
    // 0x987f44: r17 = 4828
    //     0x987f44: movz            x17, #0x12dc
    // 0x987f48: cmp             x4, x17
    // 0x987f4c: b.eq            #0x987f64
    // 0x987f50: r8 = _ZoomExitTransition
    //     0x987f50: add             x8, PP, #0x24, lsl #12  ; [pp+0x24f30] Type: _ZoomExitTransition
    //     0x987f54: ldr             x8, [x8, #0xf30]
    // 0x987f58: r3 = Null
    //     0x987f58: add             x3, PP, #0x24, lsl #12  ; [pp+0x24f38] Null
    //     0x987f5c: ldr             x3, [x3, #0xf38]
    // 0x987f60: r0 = _ZoomExitTransition()
    //     0x987f60: bl              #0x93769c  ; IsType__ZoomExitTransition_Stub
    // 0x987f64: ldur            x0, [fp, #-0x10]
    // 0x987f68: LoadField: r1 = r0->field_13
    //     0x987f68: ldur            w1, [x0, #0x13]
    // 0x987f6c: DecompressPointer r1
    //     0x987f6c: add             x1, x1, HEAP, lsl #32
    // 0x987f70: ldur            x3, [fp, #-8]
    // 0x987f74: LoadField: r2 = r3->field_b
    //     0x987f74: ldur            w2, [x3, #0xb]
    // 0x987f78: DecompressPointer r2
    //     0x987f78: add             x2, x2, HEAP, lsl #32
    // 0x987f7c: cmp             w2, NULL
    // 0x987f80: b.eq            #0x988140
    // 0x987f84: LoadField: r4 = r2->field_13
    //     0x987f84: ldur            w4, [x2, #0x13]
    // 0x987f88: DecompressPointer r4
    //     0x987f88: add             x4, x4, HEAP, lsl #32
    // 0x987f8c: cmp             w1, w4
    // 0x987f90: b.ne            #0x987fac
    // 0x987f94: LoadField: r1 = r0->field_b
    //     0x987f94: ldur            w1, [x0, #0xb]
    // 0x987f98: DecompressPointer r1
    //     0x987f98: add             x1, x1, HEAP, lsl #32
    // 0x987f9c: LoadField: r4 = r2->field_b
    //     0x987f9c: ldur            w4, [x2, #0xb]
    // 0x987fa0: DecompressPointer r4
    //     0x987fa0: add             x4, x4, HEAP, lsl #32
    // 0x987fa4: cmp             w1, w4
    // 0x987fa8: b.eq            #0x9880ec
    // 0x987fac: LoadField: r4 = r0->field_b
    //     0x987fac: ldur            w4, [x0, #0xb]
    // 0x987fb0: DecompressPointer r4
    //     0x987fb0: add             x4, x4, HEAP, lsl #32
    // 0x987fb4: mov             x2, x3
    // 0x987fb8: stur            x4, [fp, #-0x18]
    // 0x987fbc: r1 = Function 'onAnimationValueChange':.
    //     0x987fbc: add             x1, PP, #0x24, lsl #12  ; [pp+0x24f08] AnonymousClosure: (0x937d2c), in [package:flutter/src/material/page_transitions_theme.dart] __ZoomExitTransitionState&State&_ZoomTransitionBase::onAnimationValueChange (0x937d64)
    //     0x987fc0: ldr             x1, [x1, #0xf08]
    // 0x987fc4: r0 = AllocateClosure()
    //     0x987fc4: bl              #0xec1630  ; AllocateClosureStub
    // 0x987fc8: ldur            x3, [fp, #-0x18]
    // 0x987fcc: r1 = LoadClassIdInstr(r3)
    //     0x987fcc: ldur            x1, [x3, #-1]
    //     0x987fd0: ubfx            x1, x1, #0xc, #0x14
    // 0x987fd4: mov             x2, x0
    // 0x987fd8: mov             x0, x1
    // 0x987fdc: mov             x1, x3
    // 0x987fe0: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x987fe0: movz            x17, #0xbf5c
    //     0x987fe4: add             lr, x0, x17
    //     0x987fe8: ldr             lr, [x21, lr, lsl #3]
    //     0x987fec: blr             lr
    // 0x987ff0: ldur            x2, [fp, #-8]
    // 0x987ff4: r1 = Function 'onAnimationStatusChange':.
    //     0x987ff4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24f10] AnonymousClosure: (0x937c6c), in [package:flutter/src/material/page_transitions_theme.dart] __ZoomExitTransitionState&State&_ZoomTransitionBase::onAnimationStatusChange (0x937ca8)
    //     0x987ff8: ldr             x1, [x1, #0xf10]
    // 0x987ffc: r0 = AllocateClosure()
    //     0x987ffc: bl              #0xec1630  ; AllocateClosureStub
    // 0x988000: ldur            x1, [fp, #-0x18]
    // 0x988004: r2 = LoadClassIdInstr(r1)
    //     0x988004: ldur            x2, [x1, #-1]
    //     0x988008: ubfx            x2, x2, #0xc, #0x14
    // 0x98800c: mov             x16, x0
    // 0x988010: mov             x0, x2
    // 0x988014: mov             x2, x16
    // 0x988018: r0 = GDT[cid_x0 + 0x30c]()
    //     0x988018: add             lr, x0, #0x30c
    //     0x98801c: ldr             lr, [x21, lr, lsl #3]
    //     0x988020: blr             lr
    // 0x988024: ldur            x1, [fp, #-8]
    // 0x988028: r0 = _updateAnimations()
    //     0x988028: bl              #0x9378d4  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionState::_updateAnimations
    // 0x98802c: ldur            x0, [fp, #-8]
    // 0x988030: LoadField: r1 = r0->field_1f
    //     0x988030: ldur            w1, [x0, #0x1f]
    // 0x988034: DecompressPointer r1
    //     0x988034: add             x1, x1, HEAP, lsl #32
    // 0x988038: r16 = Sentinel
    //     0x988038: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x98803c: cmp             w1, w16
    // 0x988040: b.eq            #0x988144
    // 0x988044: r0 = dispose()
    //     0x988044: bl              #0xa87498  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionPainter::dispose
    // 0x988048: ldur            x0, [fp, #-8]
    // 0x98804c: LoadField: r1 = r0->field_b
    //     0x98804c: ldur            w1, [x0, #0xb]
    // 0x988050: DecompressPointer r1
    //     0x988050: add             x1, x1, HEAP, lsl #32
    // 0x988054: cmp             w1, NULL
    // 0x988058: b.eq            #0x988150
    // 0x98805c: LoadField: r5 = r1->field_13
    //     0x98805c: ldur            w5, [x1, #0x13]
    // 0x988060: DecompressPointer r5
    //     0x988060: add             x5, x5, HEAP, lsl #32
    // 0x988064: stur            x5, [fp, #-0x30]
    // 0x988068: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x988068: ldur            w3, [x0, #0x17]
    // 0x98806c: DecompressPointer r3
    //     0x98806c: add             x3, x3, HEAP, lsl #32
    // 0x988070: r16 = Sentinel
    //     0x988070: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x988074: cmp             w3, w16
    // 0x988078: b.eq            #0x988154
    // 0x98807c: stur            x3, [fp, #-0x28]
    // 0x988080: LoadField: r6 = r0->field_1b
    //     0x988080: ldur            w6, [x0, #0x1b]
    // 0x988084: DecompressPointer r6
    //     0x988084: add             x6, x6, HEAP, lsl #32
    // 0x988088: r16 = Sentinel
    //     0x988088: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x98808c: cmp             w6, w16
    // 0x988090: b.eq            #0x988160
    // 0x988094: stur            x6, [fp, #-0x20]
    // 0x988098: LoadField: r2 = r1->field_b
    //     0x988098: ldur            w2, [x1, #0xb]
    // 0x98809c: DecompressPointer r2
    //     0x98809c: add             x2, x2, HEAP, lsl #32
    // 0x9880a0: stur            x2, [fp, #-0x18]
    // 0x9880a4: r0 = _ZoomExitTransitionPainter()
    //     0x9880a4: bl              #0x9378c8  ; Allocate_ZoomExitTransitionPainterStub -> _ZoomExitTransitionPainter (size=0x40)
    // 0x9880a8: mov             x1, x0
    // 0x9880ac: ldur            x2, [fp, #-0x18]
    // 0x9880b0: ldur            x3, [fp, #-0x28]
    // 0x9880b4: ldur            x5, [fp, #-0x30]
    // 0x9880b8: ldur            x6, [fp, #-0x20]
    // 0x9880bc: stur            x0, [fp, #-0x18]
    // 0x9880c0: r0 = _ZoomExitTransitionPainter()
    //     0x9880c0: bl              #0x9376c0  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionPainter::_ZoomExitTransitionPainter
    // 0x9880c4: ldur            x0, [fp, #-0x18]
    // 0x9880c8: ldur            x1, [fp, #-8]
    // 0x9880cc: StoreField: r1->field_1f = r0
    //     0x9880cc: stur            w0, [x1, #0x1f]
    //     0x9880d0: ldurb           w16, [x1, #-1]
    //     0x9880d4: ldurb           w17, [x0, #-1]
    //     0x9880d8: and             x16, x17, x16, lsr #2
    //     0x9880dc: tst             x16, HEAP, lsr #32
    //     0x9880e0: b.eq            #0x9880e8
    //     0x9880e4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9880e8: b               #0x9880f0
    // 0x9880ec: mov             x1, x3
    // 0x9880f0: LoadField: r2 = r1->field_7
    //     0x9880f0: ldur            w2, [x1, #7]
    // 0x9880f4: DecompressPointer r2
    //     0x9880f4: add             x2, x2, HEAP, lsl #32
    // 0x9880f8: ldur            x0, [fp, #-0x10]
    // 0x9880fc: r1 = Null
    //     0x9880fc: mov             x1, NULL
    // 0x988100: cmp             w2, NULL
    // 0x988104: b.eq            #0x988128
    // 0x988108: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x988108: ldur            w4, [x2, #0x17]
    // 0x98810c: DecompressPointer r4
    //     0x98810c: add             x4, x4, HEAP, lsl #32
    // 0x988110: r8 = X0 bound StatefulWidget
    //     0x988110: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x988114: ldr             x8, [x8, #0x7f8]
    // 0x988118: LoadField: r9 = r4->field_7
    //     0x988118: ldur            x9, [x4, #7]
    // 0x98811c: r3 = Null
    //     0x98811c: add             x3, PP, #0x24, lsl #12  ; [pp+0x24f48] Null
    //     0x988120: ldr             x3, [x3, #0xf48]
    // 0x988124: blr             x9
    // 0x988128: r0 = Null
    //     0x988128: mov             x0, NULL
    // 0x98812c: LeaveFrame
    //     0x98812c: mov             SP, fp
    //     0x988130: ldp             fp, lr, [SP], #0x10
    // 0x988134: ret
    //     0x988134: ret             
    // 0x988138: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x988138: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98813c: b               #0x987f28
    // 0x988140: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x988140: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x988144: r9 = delegate
    //     0x988144: add             x9, PP, #0x24, lsl #12  ; [pp+0x24f00] Field <<EMAIL>>: late (offset: 0x20)
    //     0x988148: ldr             x9, [x9, #0xf00]
    // 0x98814c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x98814c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x988150: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x988150: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x988154: r9 = fadeTransition
    //     0x988154: add             x9, PP, #0x24, lsl #12  ; [pp+0x24f20] Field <__ZoomExitTransitionState&State&<EMAIL>>: late (offset: 0x18)
    //     0x988158: ldr             x9, [x9, #0xf20]
    // 0x98815c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x98815c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x988160: r9 = scaleTransition
    //     0x988160: add             x9, PP, #0x24, lsl #12  ; [pp+0x24f18] Field <__ZoomExitTransitionState&State&<EMAIL>>: late (offset: 0x1c)
    //     0x988164: ldr             x9, [x9, #0xf18]
    // 0x988168: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x988168: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0x9fbf70, size: 0x98
    // 0x9fbf70: EnterFrame
    //     0x9fbf70: stp             fp, lr, [SP, #-0x10]!
    //     0x9fbf74: mov             fp, SP
    // 0x9fbf78: AllocStack(0x18)
    //     0x9fbf78: sub             SP, SP, #0x18
    // 0x9fbf7c: LoadField: r0 = r1->field_1f
    //     0x9fbf7c: ldur            w0, [x1, #0x1f]
    // 0x9fbf80: DecompressPointer r0
    //     0x9fbf80: add             x0, x0, HEAP, lsl #32
    // 0x9fbf84: r16 = Sentinel
    //     0x9fbf84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9fbf88: cmp             w0, w16
    // 0x9fbf8c: b.eq            #0x9fbff8
    // 0x9fbf90: stur            x0, [fp, #-0x18]
    // 0x9fbf94: LoadField: r2 = r1->field_13
    //     0x9fbf94: ldur            w2, [x1, #0x13]
    // 0x9fbf98: DecompressPointer r2
    //     0x9fbf98: add             x2, x2, HEAP, lsl #32
    // 0x9fbf9c: stur            x2, [fp, #-0x10]
    // 0x9fbfa0: LoadField: r3 = r1->field_b
    //     0x9fbfa0: ldur            w3, [x1, #0xb]
    // 0x9fbfa4: DecompressPointer r3
    //     0x9fbfa4: add             x3, x3, HEAP, lsl #32
    // 0x9fbfa8: cmp             w3, NULL
    // 0x9fbfac: b.eq            #0x9fc004
    // 0x9fbfb0: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x9fbfb0: ldur            w1, [x3, #0x17]
    // 0x9fbfb4: DecompressPointer r1
    //     0x9fbfb4: add             x1, x1, HEAP, lsl #32
    // 0x9fbfb8: stur            x1, [fp, #-8]
    // 0x9fbfbc: r0 = SnapshotWidget()
    //     0x9fbfbc: bl              #0x9fbf64  ; AllocateSnapshotWidgetStub -> SnapshotWidget (size=0x20)
    // 0x9fbfc0: r1 = Instance_SnapshotMode
    //     0x9fbfc0: add             x1, PP, #0x24, lsl #12  ; [pp+0x24ef8] Obj!SnapshotMode@e33a61
    //     0x9fbfc4: ldr             x1, [x1, #0xef8]
    // 0x9fbfc8: StoreField: r0->field_13 = r1
    //     0x9fbfc8: stur            w1, [x0, #0x13]
    // 0x9fbfcc: ldur            x1, [fp, #-0x18]
    // 0x9fbfd0: StoreField: r0->field_1b = r1
    //     0x9fbfd0: stur            w1, [x0, #0x1b]
    // 0x9fbfd4: r1 = true
    //     0x9fbfd4: add             x1, NULL, #0x20  ; true
    // 0x9fbfd8: ArrayStore: r0[0] = r1  ; List_4
    //     0x9fbfd8: stur            w1, [x0, #0x17]
    // 0x9fbfdc: ldur            x1, [fp, #-0x10]
    // 0x9fbfe0: StoreField: r0->field_f = r1
    //     0x9fbfe0: stur            w1, [x0, #0xf]
    // 0x9fbfe4: ldur            x1, [fp, #-8]
    // 0x9fbfe8: StoreField: r0->field_b = r1
    //     0x9fbfe8: stur            w1, [x0, #0xb]
    // 0x9fbfec: LeaveFrame
    //     0x9fbfec: mov             SP, fp
    //     0x9fbff0: ldp             fp, lr, [SP], #0x10
    // 0x9fbff4: ret
    //     0x9fbff4: ret             
    // 0x9fbff8: r9 = delegate
    //     0x9fbff8: add             x9, PP, #0x24, lsl #12  ; [pp+0x24f00] Field <<EMAIL>>: late (offset: 0x20)
    //     0x9fbffc: ldr             x9, [x9, #0xf00]
    // 0x9fc000: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9fc000: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9fc004: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fc004: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7de58, size: 0x118
    // 0xa7de58: EnterFrame
    //     0xa7de58: stp             fp, lr, [SP, #-0x10]!
    //     0xa7de5c: mov             fp, SP
    // 0xa7de60: AllocStack(0x10)
    //     0xa7de60: sub             SP, SP, #0x10
    // 0xa7de64: SetupParameters(_ZoomExitTransitionState this /* r1 => r0, fp-0x10 */)
    //     0xa7de64: mov             x0, x1
    //     0xa7de68: stur            x1, [fp, #-0x10]
    // 0xa7de6c: CheckStackOverflow
    //     0xa7de6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7de70: cmp             SP, x16
    //     0xa7de74: b.ls            #0xa7df54
    // 0xa7de78: LoadField: r1 = r0->field_b
    //     0xa7de78: ldur            w1, [x0, #0xb]
    // 0xa7de7c: DecompressPointer r1
    //     0xa7de7c: add             x1, x1, HEAP, lsl #32
    // 0xa7de80: cmp             w1, NULL
    // 0xa7de84: b.eq            #0xa7df5c
    // 0xa7de88: LoadField: r3 = r1->field_b
    //     0xa7de88: ldur            w3, [x1, #0xb]
    // 0xa7de8c: DecompressPointer r3
    //     0xa7de8c: add             x3, x3, HEAP, lsl #32
    // 0xa7de90: mov             x2, x0
    // 0xa7de94: stur            x3, [fp, #-8]
    // 0xa7de98: r1 = Function 'onAnimationValueChange':.
    //     0xa7de98: add             x1, PP, #0x24, lsl #12  ; [pp+0x24f08] AnonymousClosure: (0x937d2c), in [package:flutter/src/material/page_transitions_theme.dart] __ZoomExitTransitionState&State&_ZoomTransitionBase::onAnimationValueChange (0x937d64)
    //     0xa7de9c: ldr             x1, [x1, #0xf08]
    // 0xa7dea0: r0 = AllocateClosure()
    //     0xa7dea0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7dea4: ldur            x1, [fp, #-8]
    // 0xa7dea8: r2 = LoadClassIdInstr(r1)
    //     0xa7dea8: ldur            x2, [x1, #-1]
    //     0xa7deac: ubfx            x2, x2, #0xc, #0x14
    // 0xa7deb0: mov             x16, x0
    // 0xa7deb4: mov             x0, x2
    // 0xa7deb8: mov             x2, x16
    // 0xa7debc: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa7debc: movz            x17, #0xbf5c
    //     0xa7dec0: add             lr, x0, x17
    //     0xa7dec4: ldr             lr, [x21, lr, lsl #3]
    //     0xa7dec8: blr             lr
    // 0xa7decc: ldur            x0, [fp, #-0x10]
    // 0xa7ded0: LoadField: r1 = r0->field_b
    //     0xa7ded0: ldur            w1, [x0, #0xb]
    // 0xa7ded4: DecompressPointer r1
    //     0xa7ded4: add             x1, x1, HEAP, lsl #32
    // 0xa7ded8: cmp             w1, NULL
    // 0xa7dedc: b.eq            #0xa7df60
    // 0xa7dee0: LoadField: r3 = r1->field_b
    //     0xa7dee0: ldur            w3, [x1, #0xb]
    // 0xa7dee4: DecompressPointer r3
    //     0xa7dee4: add             x3, x3, HEAP, lsl #32
    // 0xa7dee8: mov             x2, x0
    // 0xa7deec: stur            x3, [fp, #-8]
    // 0xa7def0: r1 = Function 'onAnimationStatusChange':.
    //     0xa7def0: add             x1, PP, #0x24, lsl #12  ; [pp+0x24f10] AnonymousClosure: (0x937c6c), in [package:flutter/src/material/page_transitions_theme.dart] __ZoomExitTransitionState&State&_ZoomTransitionBase::onAnimationStatusChange (0x937ca8)
    //     0xa7def4: ldr             x1, [x1, #0xf10]
    // 0xa7def8: r0 = AllocateClosure()
    //     0xa7def8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7defc: ldur            x1, [fp, #-8]
    // 0xa7df00: r2 = LoadClassIdInstr(r1)
    //     0xa7df00: ldur            x2, [x1, #-1]
    //     0xa7df04: ubfx            x2, x2, #0xc, #0x14
    // 0xa7df08: mov             x16, x0
    // 0xa7df0c: mov             x0, x2
    // 0xa7df10: mov             x2, x16
    // 0xa7df14: r0 = GDT[cid_x0 + 0x30c]()
    //     0xa7df14: add             lr, x0, #0x30c
    //     0xa7df18: ldr             lr, [x21, lr, lsl #3]
    //     0xa7df1c: blr             lr
    // 0xa7df20: ldur            x0, [fp, #-0x10]
    // 0xa7df24: LoadField: r1 = r0->field_1f
    //     0xa7df24: ldur            w1, [x0, #0x1f]
    // 0xa7df28: DecompressPointer r1
    //     0xa7df28: add             x1, x1, HEAP, lsl #32
    // 0xa7df2c: r16 = Sentinel
    //     0xa7df2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7df30: cmp             w1, w16
    // 0xa7df34: b.eq            #0xa7df64
    // 0xa7df38: r0 = dispose()
    //     0xa7df38: bl              #0xa87498  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomExitTransitionPainter::dispose
    // 0xa7df3c: ldur            x1, [fp, #-0x10]
    // 0xa7df40: r0 = dispose()
    //     0xa7df40: bl              #0xa7f8f4  ; [package:flutter/src/widgets/draggable_scrollable_sheet.dart] _DraggableScrollableActuatorState::dispose
    // 0xa7df44: r0 = Null
    //     0xa7df44: mov             x0, NULL
    // 0xa7df48: LeaveFrame
    //     0xa7df48: mov             SP, fp
    //     0xa7df4c: ldp             fp, lr, [SP], #0x10
    // 0xa7df50: ret
    //     0xa7df50: ret             
    // 0xa7df54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7df54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7df58: b               #0xa7de78
    // 0xa7df5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7df5c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa7df60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7df60: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa7df64: r9 = delegate
    //     0xa7df64: add             x9, PP, #0x24, lsl #12  ; [pp+0x24f00] Field <<EMAIL>>: late (offset: 0x20)
    //     0xa7df68: ldr             x9, [x9, #0xf00]
    // 0xa7df6c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa7df6c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4283, size: 0x20, field offset: 0x14
//   transformed mixin,
abstract class __ZoomEnterTransitionState&State&_ZoomTransitionBase extends State<dynamic>
     with _ZoomTransitionBase<X0 bound StatefulWidget> {

  late Animation<double> scaleTransition; // offset: 0x1c
  late Animation<double> fadeTransition; // offset: 0x18

  [closure] void onAnimationStatusChange(dynamic, AnimationStatus) {
    // ** addr: 0x9372c8, size: 0x3c
    // 0x9372c8: EnterFrame
    //     0x9372c8: stp             fp, lr, [SP, #-0x10]!
    //     0x9372cc: mov             fp, SP
    // 0x9372d0: ldr             x0, [fp, #0x18]
    // 0x9372d4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9372d4: ldur            w1, [x0, #0x17]
    // 0x9372d8: DecompressPointer r1
    //     0x9372d8: add             x1, x1, HEAP, lsl #32
    // 0x9372dc: CheckStackOverflow
    //     0x9372dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9372e0: cmp             SP, x16
    //     0x9372e4: b.ls            #0x9372fc
    // 0x9372e8: ldr             x2, [fp, #0x10]
    // 0x9372ec: r0 = onAnimationStatusChange()
    //     0x9372ec: bl              #0x937304  ; [package:flutter/src/material/page_transitions_theme.dart] __ZoomEnterTransitionState&State&_ZoomTransitionBase::onAnimationStatusChange
    // 0x9372f0: LeaveFrame
    //     0x9372f0: mov             SP, fp
    //     0x9372f4: ldp             fp, lr, [SP], #0x10
    // 0x9372f8: ret
    //     0x9372f8: ret             
    // 0x9372fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9372fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937300: b               #0x9372e8
  }
  _ onAnimationStatusChange(/* No info */) {
    // ** addr: 0x937304, size: 0x94
    // 0x937304: EnterFrame
    //     0x937304: stp             fp, lr, [SP, #-0x10]!
    //     0x937308: mov             fp, SP
    // 0x93730c: AllocStack(0x8)
    //     0x93730c: sub             SP, SP, #8
    // 0x937310: CheckStackOverflow
    //     0x937310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x937314: cmp             SP, x16
    //     0x937318: b.ls            #0x937390
    // 0x93731c: LoadField: r0 = r1->field_13
    //     0x93731c: ldur            w0, [x1, #0x13]
    // 0x937320: DecompressPointer r0
    //     0x937320: add             x0, x0, HEAP, lsl #32
    // 0x937324: stur            x0, [fp, #-8]
    // 0x937328: r16 = Instance_AnimationStatus
    //     0x937328: ldr             x16, [PP, #0x4eb0]  ; [pp+0x4eb0] Obj!AnimationStatus@e37301
    // 0x93732c: cmp             w2, w16
    // 0x937330: b.eq            #0x937340
    // 0x937334: r16 = Instance_AnimationStatus
    //     0x937334: ldr             x16, [PP, #0x4eb8]  ; [pp+0x4eb8] Obj!AnimationStatus@e372a1
    // 0x937338: cmp             w2, w16
    // 0x93733c: b.ne            #0x937348
    // 0x937340: r0 = build()
    //     0x937340: bl              #0xa307d4  ; [package:infinite_scroll_pagination/src/utils/listenable_listener.dart] _ListenableListenerState::build
    // 0x937344: b               #0x937364
    // 0x937348: r16 = Instance_AnimationStatus
    //     0x937348: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0x93734c: cmp             w2, w16
    // 0x937350: b.eq            #0x937360
    // 0x937354: r16 = Instance_AnimationStatus
    //     0x937354: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0x937358: cmp             w2, w16
    // 0x93735c: b.eq            #0x937360
    // 0x937360: r0 = false
    //     0x937360: add             x0, NULL, #0x30  ; false
    // 0x937364: ldur            x1, [fp, #-8]
    // 0x937368: LoadField: r2 = r1->field_23
    //     0x937368: ldur            w2, [x1, #0x23]
    // 0x93736c: DecompressPointer r2
    //     0x93736c: add             x2, x2, HEAP, lsl #32
    // 0x937370: cmp             w0, w2
    // 0x937374: b.eq            #0x937380
    // 0x937378: StoreField: r1->field_23 = r0
    //     0x937378: stur            w0, [x1, #0x23]
    // 0x93737c: r0 = notifyListeners()
    //     0x93737c: bl              #0x6462b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners
    // 0x937380: r0 = Null
    //     0x937380: mov             x0, NULL
    // 0x937384: LeaveFrame
    //     0x937384: mov             SP, fp
    //     0x937388: ldp             fp, lr, [SP], #0x10
    // 0x93738c: ret
    //     0x93738c: ret             
    // 0x937390: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x937390: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937394: b               #0x93731c
  }
  [closure] void onAnimationValueChange(dynamic) {
    // ** addr: 0x9373ec, size: 0x38
    // 0x9373ec: EnterFrame
    //     0x9373ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9373f0: mov             fp, SP
    // 0x9373f4: ldr             x0, [fp, #0x10]
    // 0x9373f8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9373f8: ldur            w1, [x0, #0x17]
    // 0x9373fc: DecompressPointer r1
    //     0x9373fc: add             x1, x1, HEAP, lsl #32
    // 0x937400: CheckStackOverflow
    //     0x937400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x937404: cmp             SP, x16
    //     0x937408: b.ls            #0x93741c
    // 0x93740c: r0 = onAnimationValueChange()
    //     0x93740c: bl              #0x937424  ; [package:flutter/src/material/page_transitions_theme.dart] __ZoomEnterTransitionState&State&_ZoomTransitionBase::onAnimationValueChange
    // 0x937410: LeaveFrame
    //     0x937410: mov             SP, fp
    //     0x937414: ldp             fp, lr, [SP], #0x10
    // 0x937418: ret
    //     0x937418: ret             
    // 0x93741c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93741c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937420: b               #0x93740c
  }
  _ onAnimationValueChange(/* No info */) {
    // ** addr: 0x937424, size: 0x17c
    // 0x937424: EnterFrame
    //     0x937424: stp             fp, lr, [SP, #-0x10]!
    //     0x937428: mov             fp, SP
    // 0x93742c: AllocStack(0x8)
    //     0x93742c: sub             SP, SP, #8
    // 0x937430: SetupParameters(__ZoomEnterTransitionState&State&_ZoomTransitionBase this /* r1 => r0, fp-0x8 */)
    //     0x937430: mov             x0, x1
    //     0x937434: stur            x1, [fp, #-8]
    // 0x937438: CheckStackOverflow
    //     0x937438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93743c: cmp             SP, x16
    //     0x937440: b.ls            #0x93757c
    // 0x937444: LoadField: r1 = r0->field_1b
    //     0x937444: ldur            w1, [x0, #0x1b]
    // 0x937448: DecompressPointer r1
    //     0x937448: add             x1, x1, HEAP, lsl #32
    // 0x93744c: r16 = Sentinel
    //     0x93744c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x937450: cmp             w1, w16
    // 0x937454: b.eq            #0x937584
    // 0x937458: LoadField: r2 = r1->field_f
    //     0x937458: ldur            w2, [x1, #0xf]
    // 0x93745c: DecompressPointer r2
    //     0x93745c: add             x2, x2, HEAP, lsl #32
    // 0x937460: LoadField: r3 = r1->field_b
    //     0x937460: ldur            w3, [x1, #0xb]
    // 0x937464: DecompressPointer r3
    //     0x937464: add             x3, x3, HEAP, lsl #32
    // 0x937468: mov             x1, x2
    // 0x93746c: mov             x2, x3
    // 0x937470: r0 = evaluate()
    //     0x937470: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x937474: LoadField: d0 = r0->field_7
    //     0x937474: ldur            d0, [x0, #7]
    // 0x937478: d1 = 1.000000
    //     0x937478: fmov            d1, #1.00000000
    // 0x93747c: fcmp            d0, d1
    // 0x937480: b.ne            #0x937530
    // 0x937484: ldur            x2, [fp, #-8]
    // 0x937488: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x937488: ldur            w1, [x2, #0x17]
    // 0x93748c: DecompressPointer r1
    //     0x93748c: add             x1, x1, HEAP, lsl #32
    // 0x937490: r16 = Sentinel
    //     0x937490: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x937494: cmp             w1, w16
    // 0x937498: b.eq            #0x937590
    // 0x93749c: r0 = LoadClassIdInstr(r1)
    //     0x93749c: ldur            x0, [x1, #-1]
    //     0x9374a0: ubfx            x0, x0, #0xc, #0x14
    // 0x9374a4: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x9374a4: movz            x17, #0x276f
    //     0x9374a8: movk            x17, #0x1, lsl #16
    //     0x9374ac: add             lr, x0, x17
    //     0x9374b0: ldr             lr, [x21, lr, lsl #3]
    //     0x9374b4: blr             lr
    // 0x9374b8: LoadField: d0 = r0->field_7
    //     0x9374b8: ldur            d0, [x0, #7]
    // 0x9374bc: d1 = 0.000000
    //     0x9374bc: eor             v1.16b, v1.16b, v1.16b
    // 0x9374c0: fcmp            d0, d1
    // 0x9374c4: b.eq            #0x937500
    // 0x9374c8: ldur            x2, [fp, #-8]
    // 0x9374cc: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x9374cc: ldur            w1, [x2, #0x17]
    // 0x9374d0: DecompressPointer r1
    //     0x9374d0: add             x1, x1, HEAP, lsl #32
    // 0x9374d4: r0 = LoadClassIdInstr(r1)
    //     0x9374d4: ldur            x0, [x1, #-1]
    //     0x9374d8: ubfx            x0, x0, #0xc, #0x14
    // 0x9374dc: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x9374dc: movz            x17, #0x276f
    //     0x9374e0: movk            x17, #0x1, lsl #16
    //     0x9374e4: add             lr, x0, x17
    //     0x9374e8: ldr             lr, [x21, lr, lsl #3]
    //     0x9374ec: blr             lr
    // 0x9374f0: LoadField: d0 = r0->field_7
    //     0x9374f0: ldur            d0, [x0, #7]
    // 0x9374f4: d1 = 1.000000
    //     0x9374f4: fmov            d1, #1.00000000
    // 0x9374f8: fcmp            d0, d1
    // 0x9374fc: b.ne            #0x937528
    // 0x937500: ldur            x0, [fp, #-8]
    // 0x937504: LoadField: r1 = r0->field_13
    //     0x937504: ldur            w1, [x0, #0x13]
    // 0x937508: DecompressPointer r1
    //     0x937508: add             x1, x1, HEAP, lsl #32
    // 0x93750c: LoadField: r0 = r1->field_23
    //     0x93750c: ldur            w0, [x1, #0x23]
    // 0x937510: DecompressPointer r0
    //     0x937510: add             x0, x0, HEAP, lsl #32
    // 0x937514: tbnz            w0, #4, #0x93756c
    // 0x937518: r0 = false
    //     0x937518: add             x0, NULL, #0x30  ; false
    // 0x93751c: StoreField: r1->field_23 = r0
    //     0x93751c: stur            w0, [x1, #0x23]
    // 0x937520: r0 = notifyListeners()
    //     0x937520: bl              #0x6462b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners
    // 0x937524: b               #0x93756c
    // 0x937528: ldur            x0, [fp, #-8]
    // 0x93752c: b               #0x937534
    // 0x937530: ldur            x0, [fp, #-8]
    // 0x937534: LoadField: r1 = r0->field_13
    //     0x937534: ldur            w1, [x0, #0x13]
    // 0x937538: DecompressPointer r1
    //     0x937538: add             x1, x1, HEAP, lsl #32
    // 0x93753c: LoadField: r2 = r0->field_b
    //     0x93753c: ldur            w2, [x0, #0xb]
    // 0x937540: DecompressPointer r2
    //     0x937540: add             x2, x2, HEAP, lsl #32
    // 0x937544: cmp             w2, NULL
    // 0x937548: b.eq            #0x93759c
    // 0x93754c: LoadField: r0 = r2->field_13
    //     0x93754c: ldur            w0, [x2, #0x13]
    // 0x937550: DecompressPointer r0
    //     0x937550: add             x0, x0, HEAP, lsl #32
    // 0x937554: LoadField: r2 = r1->field_23
    //     0x937554: ldur            w2, [x1, #0x23]
    // 0x937558: DecompressPointer r2
    //     0x937558: add             x2, x2, HEAP, lsl #32
    // 0x93755c: cmp             w0, w2
    // 0x937560: b.eq            #0x93756c
    // 0x937564: StoreField: r1->field_23 = r0
    //     0x937564: stur            w0, [x1, #0x23]
    // 0x937568: r0 = notifyListeners()
    //     0x937568: bl              #0x6462b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners
    // 0x93756c: r0 = Null
    //     0x93756c: mov             x0, NULL
    // 0x937570: LeaveFrame
    //     0x937570: mov             SP, fp
    //     0x937574: ldp             fp, lr, [SP], #0x10
    // 0x937578: ret
    //     0x937578: ret             
    // 0x93757c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93757c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937580: b               #0x937444
    // 0x937584: r9 = scaleTransition
    //     0x937584: add             x9, PP, #0x25, lsl #12  ; [pp+0x25008] Field <__ZoomEnterTransitionState&State&<EMAIL>>: late (offset: 0x1c)
    //     0x937588: ldr             x9, [x9, #8]
    // 0x93758c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x93758c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x937590: r9 = fadeTransition
    //     0x937590: add             x9, PP, #0x25, lsl #12  ; [pp+0x25010] Field <__ZoomEnterTransitionState&State&<EMAIL>>: late (offset: 0x18)
    //     0x937594: ldr             x9, [x9, #0x10]
    // 0x937598: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x937598: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x93759c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93759c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4284, size: 0x24, field offset: 0x20
class _ZoomEnterTransitionState extends __ZoomEnterTransitionState&State&_ZoomTransitionBase {

  late _ZoomEnterTransitionPainter delegate; // offset: 0x20
  static late final Animatable<double> _fadeInTransition; // offset: 0xadc
  static late final Animatable<double> _scaleDownTransition; // offset: 0xae0
  static late final Animatable<double> _scaleUpTransition; // offset: 0xae4
  static late final Animatable<double?> _scrimOpacityTween; // offset: 0xae8

  _ initState(/* No info */) {
    // ** addr: 0x9369a4, size: 0x10c
    // 0x9369a4: EnterFrame
    //     0x9369a4: stp             fp, lr, [SP, #-0x10]!
    //     0x9369a8: mov             fp, SP
    // 0x9369ac: AllocStack(0x30)
    //     0x9369ac: sub             SP, SP, #0x30
    // 0x9369b0: SetupParameters(_ZoomEnterTransitionState this /* r1 => r0, fp-0x8 */)
    //     0x9369b0: mov             x0, x1
    //     0x9369b4: stur            x1, [fp, #-8]
    // 0x9369b8: CheckStackOverflow
    //     0x9369b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9369bc: cmp             SP, x16
    //     0x9369c0: b.ls            #0x936a8c
    // 0x9369c4: mov             x1, x0
    // 0x9369c8: r0 = _updateAnimations()
    //     0x9369c8: bl              #0x936d78  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionState::_updateAnimations
    // 0x9369cc: ldur            x0, [fp, #-8]
    // 0x9369d0: LoadField: r1 = r0->field_b
    //     0x9369d0: ldur            w1, [x0, #0xb]
    // 0x9369d4: DecompressPointer r1
    //     0x9369d4: add             x1, x1, HEAP, lsl #32
    // 0x9369d8: cmp             w1, NULL
    // 0x9369dc: b.eq            #0x936a94
    // 0x9369e0: ArrayLoad: r6 = r1[0]  ; List_4
    //     0x9369e0: ldur            w6, [x1, #0x17]
    // 0x9369e4: DecompressPointer r6
    //     0x9369e4: add             x6, x6, HEAP, lsl #32
    // 0x9369e8: stur            x6, [fp, #-0x30]
    // 0x9369ec: ArrayLoad: r5 = r0[0]  ; List_4
    //     0x9369ec: ldur            w5, [x0, #0x17]
    // 0x9369f0: DecompressPointer r5
    //     0x9369f0: add             x5, x5, HEAP, lsl #32
    // 0x9369f4: r16 = Sentinel
    //     0x9369f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9369f8: cmp             w5, w16
    // 0x9369fc: b.eq            #0x936a98
    // 0x936a00: stur            x5, [fp, #-0x28]
    // 0x936a04: LoadField: r7 = r0->field_1b
    //     0x936a04: ldur            w7, [x0, #0x1b]
    // 0x936a08: DecompressPointer r7
    //     0x936a08: add             x7, x7, HEAP, lsl #32
    // 0x936a0c: r16 = Sentinel
    //     0x936a0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x936a10: cmp             w7, w16
    // 0x936a14: b.eq            #0x936aa4
    // 0x936a18: stur            x7, [fp, #-0x20]
    // 0x936a1c: LoadField: r2 = r1->field_b
    //     0x936a1c: ldur            w2, [x1, #0xb]
    // 0x936a20: DecompressPointer r2
    //     0x936a20: add             x2, x2, HEAP, lsl #32
    // 0x936a24: stur            x2, [fp, #-0x18]
    // 0x936a28: LoadField: r3 = r1->field_1b
    //     0x936a28: ldur            w3, [x1, #0x1b]
    // 0x936a2c: DecompressPointer r3
    //     0x936a2c: add             x3, x3, HEAP, lsl #32
    // 0x936a30: stur            x3, [fp, #-0x10]
    // 0x936a34: r0 = _ZoomEnterTransitionPainter()
    //     0x936a34: bl              #0x936d6c  ; Allocate_ZoomEnterTransitionPainterStub -> _ZoomEnterTransitionPainter (size=0x44)
    // 0x936a38: mov             x1, x0
    // 0x936a3c: ldur            x2, [fp, #-0x18]
    // 0x936a40: ldur            x3, [fp, #-0x10]
    // 0x936a44: ldur            x5, [fp, #-0x28]
    // 0x936a48: ldur            x6, [fp, #-0x30]
    // 0x936a4c: ldur            x7, [fp, #-0x20]
    // 0x936a50: stur            x0, [fp, #-0x10]
    // 0x936a54: r0 = _ZoomEnterTransitionPainter()
    //     0x936a54: bl              #0x936ad4  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionPainter::_ZoomEnterTransitionPainter
    // 0x936a58: ldur            x0, [fp, #-0x10]
    // 0x936a5c: ldur            x1, [fp, #-8]
    // 0x936a60: StoreField: r1->field_1f = r0
    //     0x936a60: stur            w0, [x1, #0x1f]
    //     0x936a64: ldurb           w16, [x1, #-1]
    //     0x936a68: ldurb           w17, [x0, #-1]
    //     0x936a6c: and             x16, x17, x16, lsr #2
    //     0x936a70: tst             x16, HEAP, lsr #32
    //     0x936a74: b.eq            #0x936a7c
    //     0x936a78: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x936a7c: r0 = Null
    //     0x936a7c: mov             x0, NULL
    // 0x936a80: LeaveFrame
    //     0x936a80: mov             SP, fp
    //     0x936a84: ldp             fp, lr, [SP], #0x10
    // 0x936a88: ret
    //     0x936a88: ret             
    // 0x936a8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x936a8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x936a90: b               #0x9369c4
    // 0x936a94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x936a94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x936a98: r9 = fadeTransition
    //     0x936a98: add             x9, PP, #0x25, lsl #12  ; [pp+0x25010] Field <__ZoomEnterTransitionState&State&<EMAIL>>: late (offset: 0x18)
    //     0x936a9c: ldr             x9, [x9, #0x10]
    // 0x936aa0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x936aa0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x936aa4: r9 = scaleTransition
    //     0x936aa4: add             x9, PP, #0x25, lsl #12  ; [pp+0x25008] Field <__ZoomEnterTransitionState&State&<EMAIL>>: late (offset: 0x1c)
    //     0x936aa8: ldr             x9, [x9, #8]
    // 0x936aac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x936aac: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _updateAnimations(/* No info */) {
    // ** addr: 0x936d78, size: 0x230
    // 0x936d78: EnterFrame
    //     0x936d78: stp             fp, lr, [SP, #-0x10]!
    //     0x936d7c: mov             fp, SP
    // 0x936d80: AllocStack(0x10)
    //     0x936d80: sub             SP, SP, #0x10
    // 0x936d84: SetupParameters(_ZoomEnterTransitionState this /* r1 => r2, fp-0x8 */)
    //     0x936d84: mov             x2, x1
    //     0x936d88: stur            x1, [fp, #-8]
    // 0x936d8c: CheckStackOverflow
    //     0x936d8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x936d90: cmp             SP, x16
    //     0x936d94: b.ls            #0x936f88
    // 0x936d98: LoadField: r0 = r2->field_b
    //     0x936d98: ldur            w0, [x2, #0xb]
    // 0x936d9c: DecompressPointer r0
    //     0x936d9c: add             x0, x0, HEAP, lsl #32
    // 0x936da0: cmp             w0, NULL
    // 0x936da4: b.eq            #0x936f90
    // 0x936da8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x936da8: ldur            w1, [x0, #0x17]
    // 0x936dac: DecompressPointer r1
    //     0x936dac: add             x1, x1, HEAP, lsl #32
    // 0x936db0: tbnz            w1, #4, #0x936dc0
    // 0x936db4: r0 = Instance__AlwaysCompleteAnimation
    //     0x936db4: add             x0, PP, #0x24, lsl #12  ; [pp+0x24f70] Obj!_AlwaysCompleteAnimation@e25961
    //     0x936db8: ldr             x0, [x0, #0xf70]
    // 0x936dbc: b               #0x936e0c
    // 0x936dc0: r0 = InitLateStaticField(0xadc) // [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionState::_fadeInTransition
    //     0x936dc0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x936dc4: ldr             x0, [x0, #0x15b8]
    //     0x936dc8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x936dcc: cmp             w0, w16
    //     0x936dd0: b.ne            #0x936de0
    //     0x936dd4: add             x2, PP, #0x25, lsl #12  ; [pp+0x25048] Field <_ZoomEnterTransitionState@579490068._fadeInTransition@579490068>: static late final (offset: 0xadc)
    //     0x936dd8: ldr             x2, [x2, #0x48]
    //     0x936ddc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x936de0: mov             x1, x0
    // 0x936de4: ldur            x0, [fp, #-8]
    // 0x936de8: LoadField: r2 = r0->field_b
    //     0x936de8: ldur            w2, [x0, #0xb]
    // 0x936dec: DecompressPointer r2
    //     0x936dec: add             x2, x2, HEAP, lsl #32
    // 0x936df0: cmp             w2, NULL
    // 0x936df4: b.eq            #0x936f94
    // 0x936df8: LoadField: r3 = r2->field_b
    //     0x936df8: ldur            w3, [x2, #0xb]
    // 0x936dfc: DecompressPointer r3
    //     0x936dfc: add             x3, x3, HEAP, lsl #32
    // 0x936e00: mov             x2, x3
    // 0x936e04: r0 = animate()
    //     0x936e04: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x936e08: ldur            x2, [fp, #-8]
    // 0x936e0c: ArrayStore: r2[0] = r0  ; List_4
    //     0x936e0c: stur            w0, [x2, #0x17]
    //     0x936e10: ldurb           w16, [x2, #-1]
    //     0x936e14: ldurb           w17, [x0, #-1]
    //     0x936e18: and             x16, x17, x16, lsr #2
    //     0x936e1c: tst             x16, HEAP, lsr #32
    //     0x936e20: b.eq            #0x936e28
    //     0x936e24: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x936e28: LoadField: r0 = r2->field_b
    //     0x936e28: ldur            w0, [x2, #0xb]
    // 0x936e2c: DecompressPointer r0
    //     0x936e2c: add             x0, x0, HEAP, lsl #32
    // 0x936e30: cmp             w0, NULL
    // 0x936e34: b.eq            #0x936f98
    // 0x936e38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x936e38: ldur            w1, [x0, #0x17]
    // 0x936e3c: DecompressPointer r1
    //     0x936e3c: add             x1, x1, HEAP, lsl #32
    // 0x936e40: tbnz            w1, #4, #0x936e6c
    // 0x936e44: r0 = InitLateStaticField(0xae0) // [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionState::_scaleDownTransition
    //     0x936e44: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x936e48: ldr             x0, [x0, #0x15c0]
    //     0x936e4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x936e50: cmp             w0, w16
    //     0x936e54: b.ne            #0x936e64
    //     0x936e58: add             x2, PP, #0x25, lsl #12  ; [pp+0x25050] Field <_ZoomEnterTransitionState@579490068._scaleDownTransition@579490068>: static late final (offset: 0xae0)
    //     0x936e5c: ldr             x2, [x2, #0x50]
    //     0x936e60: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x936e64: mov             x1, x0
    // 0x936e68: b               #0x936e90
    // 0x936e6c: r0 = InitLateStaticField(0xae4) // [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionState::_scaleUpTransition
    //     0x936e6c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x936e70: ldr             x0, [x0, #0x15c8]
    //     0x936e74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x936e78: cmp             w0, w16
    //     0x936e7c: b.ne            #0x936e8c
    //     0x936e80: add             x2, PP, #0x25, lsl #12  ; [pp+0x25058] Field <_ZoomEnterTransitionState@579490068._scaleUpTransition@579490068>: static late final (offset: 0xae4)
    //     0x936e84: ldr             x2, [x2, #0x58]
    //     0x936e88: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x936e8c: mov             x1, x0
    // 0x936e90: ldur            x0, [fp, #-8]
    // 0x936e94: LoadField: r2 = r0->field_b
    //     0x936e94: ldur            w2, [x0, #0xb]
    // 0x936e98: DecompressPointer r2
    //     0x936e98: add             x2, x2, HEAP, lsl #32
    // 0x936e9c: cmp             w2, NULL
    // 0x936ea0: b.eq            #0x936f9c
    // 0x936ea4: LoadField: r3 = r2->field_b
    //     0x936ea4: ldur            w3, [x2, #0xb]
    // 0x936ea8: DecompressPointer r3
    //     0x936ea8: add             x3, x3, HEAP, lsl #32
    // 0x936eac: mov             x2, x3
    // 0x936eb0: r0 = animate()
    //     0x936eb0: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x936eb4: ldur            x3, [fp, #-8]
    // 0x936eb8: StoreField: r3->field_1b = r0
    //     0x936eb8: stur            w0, [x3, #0x1b]
    //     0x936ebc: ldurb           w16, [x3, #-1]
    //     0x936ec0: ldurb           w17, [x0, #-1]
    //     0x936ec4: and             x16, x17, x16, lsr #2
    //     0x936ec8: tst             x16, HEAP, lsr #32
    //     0x936ecc: b.eq            #0x936ed4
    //     0x936ed0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x936ed4: LoadField: r0 = r3->field_b
    //     0x936ed4: ldur            w0, [x3, #0xb]
    // 0x936ed8: DecompressPointer r0
    //     0x936ed8: add             x0, x0, HEAP, lsl #32
    // 0x936edc: cmp             w0, NULL
    // 0x936ee0: b.eq            #0x936fa0
    // 0x936ee4: LoadField: r4 = r0->field_b
    //     0x936ee4: ldur            w4, [x0, #0xb]
    // 0x936ee8: DecompressPointer r4
    //     0x936ee8: add             x4, x4, HEAP, lsl #32
    // 0x936eec: mov             x2, x3
    // 0x936ef0: stur            x4, [fp, #-0x10]
    // 0x936ef4: r1 = Function 'onAnimationValueChange':.
    //     0x936ef4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24ff8] AnonymousClosure: (0x9373ec), in [package:flutter/src/material/page_transitions_theme.dart] __ZoomEnterTransitionState&State&_ZoomTransitionBase::onAnimationValueChange (0x937424)
    //     0x936ef8: ldr             x1, [x1, #0xff8]
    // 0x936efc: r0 = AllocateClosure()
    //     0x936efc: bl              #0xec1630  ; AllocateClosureStub
    // 0x936f00: ldur            x1, [fp, #-0x10]
    // 0x936f04: r2 = LoadClassIdInstr(r1)
    //     0x936f04: ldur            x2, [x1, #-1]
    //     0x936f08: ubfx            x2, x2, #0xc, #0x14
    // 0x936f0c: mov             x16, x0
    // 0x936f10: mov             x0, x2
    // 0x936f14: mov             x2, x16
    // 0x936f18: r0 = GDT[cid_x0 + 0xc407]()
    //     0x936f18: movz            x17, #0xc407
    //     0x936f1c: add             lr, x0, x17
    //     0x936f20: ldr             lr, [x21, lr, lsl #3]
    //     0x936f24: blr             lr
    // 0x936f28: ldur            x2, [fp, #-8]
    // 0x936f2c: LoadField: r0 = r2->field_b
    //     0x936f2c: ldur            w0, [x2, #0xb]
    // 0x936f30: DecompressPointer r0
    //     0x936f30: add             x0, x0, HEAP, lsl #32
    // 0x936f34: cmp             w0, NULL
    // 0x936f38: b.eq            #0x936fa4
    // 0x936f3c: LoadField: r3 = r0->field_b
    //     0x936f3c: ldur            w3, [x0, #0xb]
    // 0x936f40: DecompressPointer r3
    //     0x936f40: add             x3, x3, HEAP, lsl #32
    // 0x936f44: stur            x3, [fp, #-0x10]
    // 0x936f48: r1 = Function 'onAnimationStatusChange':.
    //     0x936f48: add             x1, PP, #0x25, lsl #12  ; [pp+0x25000] AnonymousClosure: (0x9372c8), in [package:flutter/src/material/page_transitions_theme.dart] __ZoomEnterTransitionState&State&_ZoomTransitionBase::onAnimationStatusChange (0x937304)
    //     0x936f4c: ldr             x1, [x1]
    // 0x936f50: r0 = AllocateClosure()
    //     0x936f50: bl              #0xec1630  ; AllocateClosureStub
    // 0x936f54: ldur            x1, [fp, #-0x10]
    // 0x936f58: r2 = LoadClassIdInstr(r1)
    //     0x936f58: ldur            x2, [x1, #-1]
    //     0x936f5c: ubfx            x2, x2, #0xc, #0x14
    // 0x936f60: mov             x16, x0
    // 0x936f64: mov             x0, x2
    // 0x936f68: mov             x2, x16
    // 0x936f6c: r0 = GDT[cid_x0 + 0x7a1]()
    //     0x936f6c: add             lr, x0, #0x7a1
    //     0x936f70: ldr             lr, [x21, lr, lsl #3]
    //     0x936f74: blr             lr
    // 0x936f78: r0 = Null
    //     0x936f78: mov             x0, NULL
    // 0x936f7c: LeaveFrame
    //     0x936f7c: mov             SP, fp
    //     0x936f80: ldp             fp, lr, [SP], #0x10
    // 0x936f84: ret
    //     0x936f84: ret             
    // 0x936f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x936f88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x936f8c: b               #0x936d98
    // 0x936f90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x936f90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x936f94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x936f94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x936f98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x936f98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x936f9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x936f9c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x936fa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x936fa0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x936fa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x936fa4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static Animatable<double> _scaleUpTransition() {
    // ** addr: 0x936fa8, size: 0x7c
    // 0x936fa8: EnterFrame
    //     0x936fa8: stp             fp, lr, [SP, #-0x10]!
    //     0x936fac: mov             fp, SP
    // 0x936fb0: AllocStack(0x8)
    //     0x936fb0: sub             SP, SP, #8
    // 0x936fb4: CheckStackOverflow
    //     0x936fb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x936fb8: cmp             SP, x16
    //     0x936fbc: b.ls            #0x93701c
    // 0x936fc0: r1 = <double>
    //     0x936fc0: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x936fc4: r0 = Tween()
    //     0x936fc4: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x936fc8: mov             x1, x0
    // 0x936fcc: r0 = 0.850000
    //     0x936fcc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25060] 0.85
    //     0x936fd0: ldr             x0, [x0, #0x60]
    // 0x936fd4: stur            x1, [fp, #-8]
    // 0x936fd8: StoreField: r1->field_b = r0
    //     0x936fd8: stur            w0, [x1, #0xb]
    // 0x936fdc: r0 = 1.000000
    //     0x936fdc: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x936fe0: StoreField: r1->field_f = r0
    //     0x936fe0: stur            w0, [x1, #0xf]
    // 0x936fe4: r0 = InitLateStaticField(0xad8) // [package:flutter/src/material/page_transitions_theme.dart] _ZoomPageTransition::_scaleCurveSequence
    //     0x936fe4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x936fe8: ldr             x0, [x0, #0x15b0]
    //     0x936fec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x936ff0: cmp             w0, w16
    //     0x936ff4: b.ne            #0x937004
    //     0x936ff8: add             x2, PP, #0x24, lsl #12  ; [pp+0x24f90] Field <_ZoomPageTransition@579490068._scaleCurveSequence@579490068>: static late final (offset: 0xad8)
    //     0x936ffc: ldr             x2, [x2, #0xf90]
    //     0x937000: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x937004: ldur            x1, [fp, #-8]
    // 0x937008: mov             x2, x0
    // 0x93700c: r0 = chain()
    //     0x93700c: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x937010: LeaveFrame
    //     0x937010: mov             SP, fp
    //     0x937014: ldp             fp, lr, [SP], #0x10
    // 0x937018: ret
    //     0x937018: ret             
    // 0x93701c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93701c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937020: b               #0x936fc0
  }
  static Animatable<double> _scaleDownTransition() {
    // ** addr: 0x9371dc, size: 0x7c
    // 0x9371dc: EnterFrame
    //     0x9371dc: stp             fp, lr, [SP, #-0x10]!
    //     0x9371e0: mov             fp, SP
    // 0x9371e4: AllocStack(0x8)
    //     0x9371e4: sub             SP, SP, #8
    // 0x9371e8: CheckStackOverflow
    //     0x9371e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9371ec: cmp             SP, x16
    //     0x9371f0: b.ls            #0x937250
    // 0x9371f4: r1 = <double>
    //     0x9371f4: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9371f8: r0 = Tween()
    //     0x9371f8: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x9371fc: mov             x1, x0
    // 0x937200: r0 = 1.100000
    //     0x937200: add             x0, PP, #0x25, lsl #12  ; [pp+0x25068] 1.1
    //     0x937204: ldr             x0, [x0, #0x68]
    // 0x937208: stur            x1, [fp, #-8]
    // 0x93720c: StoreField: r1->field_b = r0
    //     0x93720c: stur            w0, [x1, #0xb]
    // 0x937210: r0 = 1.000000
    //     0x937210: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x937214: StoreField: r1->field_f = r0
    //     0x937214: stur            w0, [x1, #0xf]
    // 0x937218: r0 = InitLateStaticField(0xad8) // [package:flutter/src/material/page_transitions_theme.dart] _ZoomPageTransition::_scaleCurveSequence
    //     0x937218: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93721c: ldr             x0, [x0, #0x15b0]
    //     0x937220: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x937224: cmp             w0, w16
    //     0x937228: b.ne            #0x937238
    //     0x93722c: add             x2, PP, #0x24, lsl #12  ; [pp+0x24f90] Field <_ZoomPageTransition@579490068._scaleCurveSequence@579490068>: static late final (offset: 0xad8)
    //     0x937230: ldr             x2, [x2, #0xf90]
    //     0x937234: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x937238: ldur            x1, [fp, #-8]
    // 0x93723c: mov             x2, x0
    // 0x937240: r0 = chain()
    //     0x937240: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x937244: LeaveFrame
    //     0x937244: mov             SP, fp
    //     0x937248: ldp             fp, lr, [SP], #0x10
    // 0x93724c: ret
    //     0x93724c: ret             
    // 0x937250: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x937250: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x937254: b               #0x9371f4
  }
  static Animatable<double> _fadeInTransition() {
    // ** addr: 0x937258, size: 0x70
    // 0x937258: EnterFrame
    //     0x937258: stp             fp, lr, [SP, #-0x10]!
    //     0x93725c: mov             fp, SP
    // 0x937260: AllocStack(0x8)
    //     0x937260: sub             SP, SP, #8
    // 0x937264: CheckStackOverflow
    //     0x937264: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x937268: cmp             SP, x16
    //     0x93726c: b.ls            #0x9372c0
    // 0x937270: r1 = <double>
    //     0x937270: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x937274: r0 = Tween()
    //     0x937274: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x937278: mov             x2, x0
    // 0x93727c: r0 = 0.000000
    //     0x93727c: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x937280: stur            x2, [fp, #-8]
    // 0x937284: StoreField: r2->field_b = r0
    //     0x937284: stur            w0, [x2, #0xb]
    // 0x937288: r0 = 1.000000
    //     0x937288: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x93728c: StoreField: r2->field_f = r0
    //     0x93728c: stur            w0, [x2, #0xf]
    // 0x937290: r1 = <double>
    //     0x937290: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x937294: r0 = CurveTween()
    //     0x937294: bl              #0x796a9c  ; AllocateCurveTweenStub -> CurveTween (size=0x10)
    // 0x937298: mov             x1, x0
    // 0x93729c: r0 = Instance_Interval
    //     0x93729c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25070] Obj!Interval@e14fc1
    //     0x9372a0: ldr             x0, [x0, #0x70]
    // 0x9372a4: StoreField: r1->field_b = r0
    //     0x9372a4: stur            w0, [x1, #0xb]
    // 0x9372a8: mov             x2, x1
    // 0x9372ac: ldur            x1, [fp, #-8]
    // 0x9372b0: r0 = chain()
    //     0x9372b0: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x9372b4: LeaveFrame
    //     0x9372b4: mov             SP, fp
    //     0x9372b8: ldp             fp, lr, [SP], #0x10
    // 0x9372bc: ret
    //     0x9372bc: ret             
    // 0x9372c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9372c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9372c4: b               #0x937270
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x987c84, size: 0x27c
    // 0x987c84: EnterFrame
    //     0x987c84: stp             fp, lr, [SP, #-0x10]!
    //     0x987c88: mov             fp, SP
    // 0x987c8c: AllocStack(0x38)
    //     0x987c8c: sub             SP, SP, #0x38
    // 0x987c90: SetupParameters(_ZoomEnterTransitionState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x987c90: mov             x4, x1
    //     0x987c94: mov             x3, x2
    //     0x987c98: stur            x1, [fp, #-8]
    //     0x987c9c: stur            x2, [fp, #-0x10]
    // 0x987ca0: CheckStackOverflow
    //     0x987ca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x987ca4: cmp             SP, x16
    //     0x987ca8: b.ls            #0x987ecc
    // 0x987cac: mov             x0, x3
    // 0x987cb0: r2 = Null
    //     0x987cb0: mov             x2, NULL
    // 0x987cb4: r1 = Null
    //     0x987cb4: mov             x1, NULL
    // 0x987cb8: r4 = 60
    //     0x987cb8: movz            x4, #0x3c
    // 0x987cbc: branchIfSmi(r0, 0x987cc8)
    //     0x987cbc: tbz             w0, #0, #0x987cc8
    // 0x987cc0: r4 = LoadClassIdInstr(r0)
    //     0x987cc0: ldur            x4, [x0, #-1]
    //     0x987cc4: ubfx            x4, x4, #0xc, #0x14
    // 0x987cc8: r17 = 4829
    //     0x987cc8: movz            x17, #0x12dd
    // 0x987ccc: cmp             x4, x17
    // 0x987cd0: b.eq            #0x987ce8
    // 0x987cd4: r8 = _ZoomEnterTransition
    //     0x987cd4: add             x8, PP, #0x25, lsl #12  ; [pp+0x25020] Type: _ZoomEnterTransition
    //     0x987cd8: ldr             x8, [x8, #0x20]
    // 0x987cdc: r3 = Null
    //     0x987cdc: add             x3, PP, #0x25, lsl #12  ; [pp+0x25028] Null
    //     0x987ce0: ldr             x3, [x3, #0x28]
    // 0x987ce4: r0 = _ZoomEnterTransition()
    //     0x987ce4: bl              #0x936ab0  ; IsType__ZoomEnterTransition_Stub
    // 0x987ce8: ldur            x0, [fp, #-0x10]
    // 0x987cec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x987cec: ldur            w1, [x0, #0x17]
    // 0x987cf0: DecompressPointer r1
    //     0x987cf0: add             x1, x1, HEAP, lsl #32
    // 0x987cf4: ldur            x3, [fp, #-8]
    // 0x987cf8: LoadField: r2 = r3->field_b
    //     0x987cf8: ldur            w2, [x3, #0xb]
    // 0x987cfc: DecompressPointer r2
    //     0x987cfc: add             x2, x2, HEAP, lsl #32
    // 0x987d00: cmp             w2, NULL
    // 0x987d04: b.eq            #0x987ed4
    // 0x987d08: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x987d08: ldur            w4, [x2, #0x17]
    // 0x987d0c: DecompressPointer r4
    //     0x987d0c: add             x4, x4, HEAP, lsl #32
    // 0x987d10: cmp             w1, w4
    // 0x987d14: b.ne            #0x987d30
    // 0x987d18: LoadField: r1 = r0->field_b
    //     0x987d18: ldur            w1, [x0, #0xb]
    // 0x987d1c: DecompressPointer r1
    //     0x987d1c: add             x1, x1, HEAP, lsl #32
    // 0x987d20: LoadField: r4 = r2->field_b
    //     0x987d20: ldur            w4, [x2, #0xb]
    // 0x987d24: DecompressPointer r4
    //     0x987d24: add             x4, x4, HEAP, lsl #32
    // 0x987d28: cmp             w1, w4
    // 0x987d2c: b.eq            #0x987e80
    // 0x987d30: LoadField: r4 = r0->field_b
    //     0x987d30: ldur            w4, [x0, #0xb]
    // 0x987d34: DecompressPointer r4
    //     0x987d34: add             x4, x4, HEAP, lsl #32
    // 0x987d38: mov             x2, x3
    // 0x987d3c: stur            x4, [fp, #-0x18]
    // 0x987d40: r1 = Function 'onAnimationValueChange':.
    //     0x987d40: add             x1, PP, #0x24, lsl #12  ; [pp+0x24ff8] AnonymousClosure: (0x9373ec), in [package:flutter/src/material/page_transitions_theme.dart] __ZoomEnterTransitionState&State&_ZoomTransitionBase::onAnimationValueChange (0x937424)
    //     0x987d44: ldr             x1, [x1, #0xff8]
    // 0x987d48: r0 = AllocateClosure()
    //     0x987d48: bl              #0xec1630  ; AllocateClosureStub
    // 0x987d4c: ldur            x3, [fp, #-0x18]
    // 0x987d50: r1 = LoadClassIdInstr(r3)
    //     0x987d50: ldur            x1, [x3, #-1]
    //     0x987d54: ubfx            x1, x1, #0xc, #0x14
    // 0x987d58: mov             x2, x0
    // 0x987d5c: mov             x0, x1
    // 0x987d60: mov             x1, x3
    // 0x987d64: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x987d64: movz            x17, #0xbf5c
    //     0x987d68: add             lr, x0, x17
    //     0x987d6c: ldr             lr, [x21, lr, lsl #3]
    //     0x987d70: blr             lr
    // 0x987d74: ldur            x2, [fp, #-8]
    // 0x987d78: r1 = Function 'onAnimationStatusChange':.
    //     0x987d78: add             x1, PP, #0x25, lsl #12  ; [pp+0x25000] AnonymousClosure: (0x9372c8), in [package:flutter/src/material/page_transitions_theme.dart] __ZoomEnterTransitionState&State&_ZoomTransitionBase::onAnimationStatusChange (0x937304)
    //     0x987d7c: ldr             x1, [x1]
    // 0x987d80: r0 = AllocateClosure()
    //     0x987d80: bl              #0xec1630  ; AllocateClosureStub
    // 0x987d84: ldur            x1, [fp, #-0x18]
    // 0x987d88: r2 = LoadClassIdInstr(r1)
    //     0x987d88: ldur            x2, [x1, #-1]
    //     0x987d8c: ubfx            x2, x2, #0xc, #0x14
    // 0x987d90: mov             x16, x0
    // 0x987d94: mov             x0, x2
    // 0x987d98: mov             x2, x16
    // 0x987d9c: r0 = GDT[cid_x0 + 0x30c]()
    //     0x987d9c: add             lr, x0, #0x30c
    //     0x987da0: ldr             lr, [x21, lr, lsl #3]
    //     0x987da4: blr             lr
    // 0x987da8: ldur            x1, [fp, #-8]
    // 0x987dac: r0 = _updateAnimations()
    //     0x987dac: bl              #0x936d78  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionState::_updateAnimations
    // 0x987db0: ldur            x0, [fp, #-8]
    // 0x987db4: LoadField: r1 = r0->field_1f
    //     0x987db4: ldur            w1, [x0, #0x1f]
    // 0x987db8: DecompressPointer r1
    //     0x987db8: add             x1, x1, HEAP, lsl #32
    // 0x987dbc: r16 = Sentinel
    //     0x987dbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x987dc0: cmp             w1, w16
    // 0x987dc4: b.eq            #0x987ed8
    // 0x987dc8: r0 = dispose()
    //     0x987dc8: bl              #0xa8737c  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionPainter::dispose
    // 0x987dcc: ldur            x0, [fp, #-8]
    // 0x987dd0: LoadField: r1 = r0->field_b
    //     0x987dd0: ldur            w1, [x0, #0xb]
    // 0x987dd4: DecompressPointer r1
    //     0x987dd4: add             x1, x1, HEAP, lsl #32
    // 0x987dd8: cmp             w1, NULL
    // 0x987ddc: b.eq            #0x987ee4
    // 0x987de0: ArrayLoad: r6 = r1[0]  ; List_4
    //     0x987de0: ldur            w6, [x1, #0x17]
    // 0x987de4: DecompressPointer r6
    //     0x987de4: add             x6, x6, HEAP, lsl #32
    // 0x987de8: stur            x6, [fp, #-0x38]
    // 0x987dec: ArrayLoad: r5 = r0[0]  ; List_4
    //     0x987dec: ldur            w5, [x0, #0x17]
    // 0x987df0: DecompressPointer r5
    //     0x987df0: add             x5, x5, HEAP, lsl #32
    // 0x987df4: r16 = Sentinel
    //     0x987df4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x987df8: cmp             w5, w16
    // 0x987dfc: b.eq            #0x987ee8
    // 0x987e00: stur            x5, [fp, #-0x30]
    // 0x987e04: LoadField: r7 = r0->field_1b
    //     0x987e04: ldur            w7, [x0, #0x1b]
    // 0x987e08: DecompressPointer r7
    //     0x987e08: add             x7, x7, HEAP, lsl #32
    // 0x987e0c: r16 = Sentinel
    //     0x987e0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x987e10: cmp             w7, w16
    // 0x987e14: b.eq            #0x987ef4
    // 0x987e18: stur            x7, [fp, #-0x28]
    // 0x987e1c: LoadField: r2 = r1->field_b
    //     0x987e1c: ldur            w2, [x1, #0xb]
    // 0x987e20: DecompressPointer r2
    //     0x987e20: add             x2, x2, HEAP, lsl #32
    // 0x987e24: stur            x2, [fp, #-0x20]
    // 0x987e28: LoadField: r3 = r1->field_1b
    //     0x987e28: ldur            w3, [x1, #0x1b]
    // 0x987e2c: DecompressPointer r3
    //     0x987e2c: add             x3, x3, HEAP, lsl #32
    // 0x987e30: stur            x3, [fp, #-0x18]
    // 0x987e34: r0 = _ZoomEnterTransitionPainter()
    //     0x987e34: bl              #0x936d6c  ; Allocate_ZoomEnterTransitionPainterStub -> _ZoomEnterTransitionPainter (size=0x44)
    // 0x987e38: mov             x1, x0
    // 0x987e3c: ldur            x2, [fp, #-0x20]
    // 0x987e40: ldur            x3, [fp, #-0x18]
    // 0x987e44: ldur            x5, [fp, #-0x30]
    // 0x987e48: ldur            x6, [fp, #-0x38]
    // 0x987e4c: ldur            x7, [fp, #-0x28]
    // 0x987e50: stur            x0, [fp, #-0x18]
    // 0x987e54: r0 = _ZoomEnterTransitionPainter()
    //     0x987e54: bl              #0x936ad4  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionPainter::_ZoomEnterTransitionPainter
    // 0x987e58: ldur            x0, [fp, #-0x18]
    // 0x987e5c: ldur            x1, [fp, #-8]
    // 0x987e60: StoreField: r1->field_1f = r0
    //     0x987e60: stur            w0, [x1, #0x1f]
    //     0x987e64: ldurb           w16, [x1, #-1]
    //     0x987e68: ldurb           w17, [x0, #-1]
    //     0x987e6c: and             x16, x17, x16, lsr #2
    //     0x987e70: tst             x16, HEAP, lsr #32
    //     0x987e74: b.eq            #0x987e7c
    //     0x987e78: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x987e7c: b               #0x987e84
    // 0x987e80: mov             x1, x3
    // 0x987e84: LoadField: r2 = r1->field_7
    //     0x987e84: ldur            w2, [x1, #7]
    // 0x987e88: DecompressPointer r2
    //     0x987e88: add             x2, x2, HEAP, lsl #32
    // 0x987e8c: ldur            x0, [fp, #-0x10]
    // 0x987e90: r1 = Null
    //     0x987e90: mov             x1, NULL
    // 0x987e94: cmp             w2, NULL
    // 0x987e98: b.eq            #0x987ebc
    // 0x987e9c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x987e9c: ldur            w4, [x2, #0x17]
    // 0x987ea0: DecompressPointer r4
    //     0x987ea0: add             x4, x4, HEAP, lsl #32
    // 0x987ea4: r8 = X0 bound StatefulWidget
    //     0x987ea4: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x987ea8: ldr             x8, [x8, #0x7f8]
    // 0x987eac: LoadField: r9 = r4->field_7
    //     0x987eac: ldur            x9, [x4, #7]
    // 0x987eb0: r3 = Null
    //     0x987eb0: add             x3, PP, #0x25, lsl #12  ; [pp+0x25038] Null
    //     0x987eb4: ldr             x3, [x3, #0x38]
    // 0x987eb8: blr             x9
    // 0x987ebc: r0 = Null
    //     0x987ebc: mov             x0, NULL
    // 0x987ec0: LeaveFrame
    //     0x987ec0: mov             SP, fp
    //     0x987ec4: ldp             fp, lr, [SP], #0x10
    // 0x987ec8: ret
    //     0x987ec8: ret             
    // 0x987ecc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x987ecc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x987ed0: b               #0x987cac
    // 0x987ed4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x987ed4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x987ed8: r9 = delegate
    //     0x987ed8: add             x9, PP, #0x24, lsl #12  ; [pp+0x24ff0] Field <<EMAIL>>: late (offset: 0x20)
    //     0x987edc: ldr             x9, [x9, #0xff0]
    // 0x987ee0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x987ee0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x987ee4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x987ee4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x987ee8: r9 = fadeTransition
    //     0x987ee8: add             x9, PP, #0x25, lsl #12  ; [pp+0x25010] Field <__ZoomEnterTransitionState&State&<EMAIL>>: late (offset: 0x18)
    //     0x987eec: ldr             x9, [x9, #0x10]
    // 0x987ef0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x987ef0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x987ef4: r9 = scaleTransition
    //     0x987ef4: add             x9, PP, #0x25, lsl #12  ; [pp+0x25008] Field <__ZoomEnterTransitionState&State&<EMAIL>>: late (offset: 0x1c)
    //     0x987ef8: ldr             x9, [x9, #8]
    // 0x987efc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x987efc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0x9fbecc, size: 0x98
    // 0x9fbecc: EnterFrame
    //     0x9fbecc: stp             fp, lr, [SP, #-0x10]!
    //     0x9fbed0: mov             fp, SP
    // 0x9fbed4: AllocStack(0x18)
    //     0x9fbed4: sub             SP, SP, #0x18
    // 0x9fbed8: LoadField: r0 = r1->field_1f
    //     0x9fbed8: ldur            w0, [x1, #0x1f]
    // 0x9fbedc: DecompressPointer r0
    //     0x9fbedc: add             x0, x0, HEAP, lsl #32
    // 0x9fbee0: r16 = Sentinel
    //     0x9fbee0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9fbee4: cmp             w0, w16
    // 0x9fbee8: b.eq            #0x9fbf54
    // 0x9fbeec: stur            x0, [fp, #-0x18]
    // 0x9fbef0: LoadField: r2 = r1->field_13
    //     0x9fbef0: ldur            w2, [x1, #0x13]
    // 0x9fbef4: DecompressPointer r2
    //     0x9fbef4: add             x2, x2, HEAP, lsl #32
    // 0x9fbef8: stur            x2, [fp, #-0x10]
    // 0x9fbefc: LoadField: r3 = r1->field_b
    //     0x9fbefc: ldur            w3, [x1, #0xb]
    // 0x9fbf00: DecompressPointer r3
    //     0x9fbf00: add             x3, x3, HEAP, lsl #32
    // 0x9fbf04: cmp             w3, NULL
    // 0x9fbf08: b.eq            #0x9fbf60
    // 0x9fbf0c: LoadField: r1 = r3->field_f
    //     0x9fbf0c: ldur            w1, [x3, #0xf]
    // 0x9fbf10: DecompressPointer r1
    //     0x9fbf10: add             x1, x1, HEAP, lsl #32
    // 0x9fbf14: stur            x1, [fp, #-8]
    // 0x9fbf18: r0 = SnapshotWidget()
    //     0x9fbf18: bl              #0x9fbf64  ; AllocateSnapshotWidgetStub -> SnapshotWidget (size=0x20)
    // 0x9fbf1c: r1 = Instance_SnapshotMode
    //     0x9fbf1c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24ef8] Obj!SnapshotMode@e33a61
    //     0x9fbf20: ldr             x1, [x1, #0xef8]
    // 0x9fbf24: StoreField: r0->field_13 = r1
    //     0x9fbf24: stur            w1, [x0, #0x13]
    // 0x9fbf28: ldur            x1, [fp, #-0x18]
    // 0x9fbf2c: StoreField: r0->field_1b = r1
    //     0x9fbf2c: stur            w1, [x0, #0x1b]
    // 0x9fbf30: r1 = true
    //     0x9fbf30: add             x1, NULL, #0x20  ; true
    // 0x9fbf34: ArrayStore: r0[0] = r1  ; List_4
    //     0x9fbf34: stur            w1, [x0, #0x17]
    // 0x9fbf38: ldur            x1, [fp, #-0x10]
    // 0x9fbf3c: StoreField: r0->field_f = r1
    //     0x9fbf3c: stur            w1, [x0, #0xf]
    // 0x9fbf40: ldur            x1, [fp, #-8]
    // 0x9fbf44: StoreField: r0->field_b = r1
    //     0x9fbf44: stur            w1, [x0, #0xb]
    // 0x9fbf48: LeaveFrame
    //     0x9fbf48: mov             SP, fp
    //     0x9fbf4c: ldp             fp, lr, [SP], #0x10
    // 0x9fbf50: ret
    //     0x9fbf50: ret             
    // 0x9fbf54: r9 = delegate
    //     0x9fbf54: add             x9, PP, #0x24, lsl #12  ; [pp+0x24ff0] Field <<EMAIL>>: late (offset: 0x20)
    //     0x9fbf58: ldr             x9, [x9, #0xff0]
    // 0x9fbf5c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9fbf5c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9fbf60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbf60: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7dd40, size: 0x118
    // 0xa7dd40: EnterFrame
    //     0xa7dd40: stp             fp, lr, [SP, #-0x10]!
    //     0xa7dd44: mov             fp, SP
    // 0xa7dd48: AllocStack(0x10)
    //     0xa7dd48: sub             SP, SP, #0x10
    // 0xa7dd4c: SetupParameters(_ZoomEnterTransitionState this /* r1 => r0, fp-0x10 */)
    //     0xa7dd4c: mov             x0, x1
    //     0xa7dd50: stur            x1, [fp, #-0x10]
    // 0xa7dd54: CheckStackOverflow
    //     0xa7dd54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7dd58: cmp             SP, x16
    //     0xa7dd5c: b.ls            #0xa7de3c
    // 0xa7dd60: LoadField: r1 = r0->field_b
    //     0xa7dd60: ldur            w1, [x0, #0xb]
    // 0xa7dd64: DecompressPointer r1
    //     0xa7dd64: add             x1, x1, HEAP, lsl #32
    // 0xa7dd68: cmp             w1, NULL
    // 0xa7dd6c: b.eq            #0xa7de44
    // 0xa7dd70: LoadField: r3 = r1->field_b
    //     0xa7dd70: ldur            w3, [x1, #0xb]
    // 0xa7dd74: DecompressPointer r3
    //     0xa7dd74: add             x3, x3, HEAP, lsl #32
    // 0xa7dd78: mov             x2, x0
    // 0xa7dd7c: stur            x3, [fp, #-8]
    // 0xa7dd80: r1 = Function 'onAnimationValueChange':.
    //     0xa7dd80: add             x1, PP, #0x24, lsl #12  ; [pp+0x24ff8] AnonymousClosure: (0x9373ec), in [package:flutter/src/material/page_transitions_theme.dart] __ZoomEnterTransitionState&State&_ZoomTransitionBase::onAnimationValueChange (0x937424)
    //     0xa7dd84: ldr             x1, [x1, #0xff8]
    // 0xa7dd88: r0 = AllocateClosure()
    //     0xa7dd88: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7dd8c: ldur            x1, [fp, #-8]
    // 0xa7dd90: r2 = LoadClassIdInstr(r1)
    //     0xa7dd90: ldur            x2, [x1, #-1]
    //     0xa7dd94: ubfx            x2, x2, #0xc, #0x14
    // 0xa7dd98: mov             x16, x0
    // 0xa7dd9c: mov             x0, x2
    // 0xa7dda0: mov             x2, x16
    // 0xa7dda4: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa7dda4: movz            x17, #0xbf5c
    //     0xa7dda8: add             lr, x0, x17
    //     0xa7ddac: ldr             lr, [x21, lr, lsl #3]
    //     0xa7ddb0: blr             lr
    // 0xa7ddb4: ldur            x0, [fp, #-0x10]
    // 0xa7ddb8: LoadField: r1 = r0->field_b
    //     0xa7ddb8: ldur            w1, [x0, #0xb]
    // 0xa7ddbc: DecompressPointer r1
    //     0xa7ddbc: add             x1, x1, HEAP, lsl #32
    // 0xa7ddc0: cmp             w1, NULL
    // 0xa7ddc4: b.eq            #0xa7de48
    // 0xa7ddc8: LoadField: r3 = r1->field_b
    //     0xa7ddc8: ldur            w3, [x1, #0xb]
    // 0xa7ddcc: DecompressPointer r3
    //     0xa7ddcc: add             x3, x3, HEAP, lsl #32
    // 0xa7ddd0: mov             x2, x0
    // 0xa7ddd4: stur            x3, [fp, #-8]
    // 0xa7ddd8: r1 = Function 'onAnimationStatusChange':.
    //     0xa7ddd8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25000] AnonymousClosure: (0x9372c8), in [package:flutter/src/material/page_transitions_theme.dart] __ZoomEnterTransitionState&State&_ZoomTransitionBase::onAnimationStatusChange (0x937304)
    //     0xa7dddc: ldr             x1, [x1]
    // 0xa7dde0: r0 = AllocateClosure()
    //     0xa7dde0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7dde4: ldur            x1, [fp, #-8]
    // 0xa7dde8: r2 = LoadClassIdInstr(r1)
    //     0xa7dde8: ldur            x2, [x1, #-1]
    //     0xa7ddec: ubfx            x2, x2, #0xc, #0x14
    // 0xa7ddf0: mov             x16, x0
    // 0xa7ddf4: mov             x0, x2
    // 0xa7ddf8: mov             x2, x16
    // 0xa7ddfc: r0 = GDT[cid_x0 + 0x30c]()
    //     0xa7ddfc: add             lr, x0, #0x30c
    //     0xa7de00: ldr             lr, [x21, lr, lsl #3]
    //     0xa7de04: blr             lr
    // 0xa7de08: ldur            x0, [fp, #-0x10]
    // 0xa7de0c: LoadField: r1 = r0->field_1f
    //     0xa7de0c: ldur            w1, [x0, #0x1f]
    // 0xa7de10: DecompressPointer r1
    //     0xa7de10: add             x1, x1, HEAP, lsl #32
    // 0xa7de14: r16 = Sentinel
    //     0xa7de14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7de18: cmp             w1, w16
    // 0xa7de1c: b.eq            #0xa7de4c
    // 0xa7de20: r0 = dispose()
    //     0xa7de20: bl              #0xa8737c  ; [package:flutter/src/material/page_transitions_theme.dart] _ZoomEnterTransitionPainter::dispose
    // 0xa7de24: ldur            x1, [fp, #-0x10]
    // 0xa7de28: r0 = dispose()
    //     0xa7de28: bl              #0xa7f8f4  ; [package:flutter/src/widgets/draggable_scrollable_sheet.dart] _DraggableScrollableActuatorState::dispose
    // 0xa7de2c: r0 = Null
    //     0xa7de2c: mov             x0, NULL
    // 0xa7de30: LeaveFrame
    //     0xa7de30: mov             SP, fp
    //     0xa7de34: ldp             fp, lr, [SP], #0x10
    // 0xa7de38: ret
    //     0xa7de38: ret             
    // 0xa7de3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7de3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7de40: b               #0xa7dd60
    // 0xa7de44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7de44: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa7de48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7de48: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa7de4c: r9 = delegate
    //     0xa7de4c: add             x9, PP, #0x24, lsl #12  ; [pp+0x24ff0] Field <<EMAIL>>: late (offset: 0x20)
    //     0xa7de50: ldr             x9, [x9, #0xff0]
    // 0xa7de54: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa7de54: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  static Animatable<double?> _scrimOpacityTween() {
    // ** addr: 0xd84ca8, size: 0x78
    // 0xd84ca8: EnterFrame
    //     0xd84ca8: stp             fp, lr, [SP, #-0x10]!
    //     0xd84cac: mov             fp, SP
    // 0xd84cb0: AllocStack(0x8)
    //     0xd84cb0: sub             SP, SP, #8
    // 0xd84cb4: CheckStackOverflow
    //     0xd84cb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd84cb8: cmp             SP, x16
    //     0xd84cbc: b.ls            #0xd84d18
    // 0xd84cc0: r1 = <double?>
    //     0xd84cc0: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b1c0] TypeArguments: <double?>
    //     0xd84cc4: ldr             x1, [x1, #0x1c0]
    // 0xd84cc8: r0 = Tween()
    //     0xd84cc8: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xd84ccc: mov             x2, x0
    // 0xd84cd0: r0 = 0.000000
    //     0xd84cd0: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xd84cd4: stur            x2, [fp, #-8]
    // 0xd84cd8: StoreField: r2->field_b = r0
    //     0xd84cd8: stur            w0, [x2, #0xb]
    // 0xd84cdc: r0 = 0.600000
    //     0xd84cdc: add             x0, PP, #0xd, lsl #12  ; [pp+0xdfc0] 0.6
    //     0xd84ce0: ldr             x0, [x0, #0xfc0]
    // 0xd84ce4: StoreField: r2->field_f = r0
    //     0xd84ce4: stur            w0, [x2, #0xf]
    // 0xd84ce8: r1 = <double>
    //     0xd84ce8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xd84cec: r0 = CurveTween()
    //     0xd84cec: bl              #0x796a9c  ; AllocateCurveTweenStub -> CurveTween (size=0x10)
    // 0xd84cf0: mov             x1, x0
    // 0xd84cf4: r0 = Instance_Interval
    //     0xd84cf4: add             x0, PP, #0x44, lsl #12  ; [pp+0x44370] Obj!Interval@e150e1
    //     0xd84cf8: ldr             x0, [x0, #0x370]
    // 0xd84cfc: StoreField: r1->field_b = r0
    //     0xd84cfc: stur            w0, [x1, #0xb]
    // 0xd84d00: mov             x2, x1
    // 0xd84d04: ldur            x1, [fp, #-8]
    // 0xd84d08: r0 = chain()
    //     0xd84d08: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0xd84d0c: LeaveFrame
    //     0xd84d0c: mov             SP, fp
    //     0xd84d10: ldp             fp, lr, [SP], #0x10
    // 0xd84d14: ret
    //     0xd84d14: ret             
    // 0xd84d18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd84d18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd84d1c: b               #0xd84cc0
  }
}

// class id: 4285, size: 0x14, field offset: 0x14
abstract class _ZoomTransitionBase<X0 bound StatefulWidget> extends State<X0 bound StatefulWidget> {
}

// class id: 4827, size: 0x24, field offset: 0xc
//   const constructor, 
class _PageTransitionsThemeTransitions<X0> extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa905b0, size: 0x3c
    // 0xa905b0: EnterFrame
    //     0xa905b0: stp             fp, lr, [SP, #-0x10]!
    //     0xa905b4: mov             fp, SP
    // 0xa905b8: LoadField: r2 = r1->field_b
    //     0xa905b8: ldur            w2, [x1, #0xb]
    // 0xa905bc: DecompressPointer r2
    //     0xa905bc: add             x2, x2, HEAP, lsl #32
    // 0xa905c0: r1 = Null
    //     0xa905c0: mov             x1, NULL
    // 0xa905c4: r3 = <_PageTransitionsThemeTransitions<X0>, X0>
    //     0xa905c4: add             x3, PP, #0x30, lsl #12  ; [pp+0x30d70] TypeArguments: <_PageTransitionsThemeTransitions<X0>, X0>
    //     0xa905c8: ldr             x3, [x3, #0xd70]
    // 0xa905cc: r30 = InstantiateTypeArgumentsStub
    //     0xa905cc: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xa905d0: LoadField: r30 = r30->field_7
    //     0xa905d0: ldur            lr, [lr, #7]
    // 0xa905d4: blr             lr
    // 0xa905d8: mov             x1, x0
    // 0xa905dc: r0 = _PageTransitionsThemeTransitionsState()
    //     0xa905dc: bl              #0xa905ec  ; Allocate_PageTransitionsThemeTransitionsStateStub -> _PageTransitionsThemeTransitionsState<C1X0> (size=0x18)
    // 0xa905e0: LeaveFrame
    //     0xa905e0: mov             SP, fp
    //     0xa905e4: ldp             fp, lr, [SP], #0x10
    // 0xa905e8: ret
    //     0xa905e8: ret             
  }
}

// class id: 4828, size: 0x1c, field offset: 0xc
//   const constructor, 
class _ZoomExitTransition extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa90550, size: 0x54
    // 0xa90550: EnterFrame
    //     0xa90550: stp             fp, lr, [SP, #-0x10]!
    //     0xa90554: mov             fp, SP
    // 0xa90558: AllocStack(0x8)
    //     0xa90558: sub             SP, SP, #8
    // 0xa9055c: CheckStackOverflow
    //     0xa9055c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa90560: cmp             SP, x16
    //     0xa90564: b.ls            #0xa9059c
    // 0xa90568: r1 = <_ZoomExitTransition>
    //     0xa90568: add             x1, PP, #0x23, lsl #12  ; [pp+0x237a8] TypeArguments: <_ZoomExitTransition>
    //     0xa9056c: ldr             x1, [x1, #0x7a8]
    // 0xa90570: r0 = _ZoomExitTransitionState()
    //     0xa90570: bl              #0xa905a4  ; Allocate_ZoomExitTransitionStateStub -> _ZoomExitTransitionState (size=0x24)
    // 0xa90574: mov             x2, x0
    // 0xa90578: r0 = Sentinel
    //     0xa90578: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9057c: stur            x2, [fp, #-8]
    // 0xa90580: StoreField: r2->field_1f = r0
    //     0xa90580: stur            w0, [x2, #0x1f]
    // 0xa90584: mov             x1, x2
    // 0xa90588: r0 = __ZoomExitTransitionState&State&_ZoomTransitionBase()
    //     0xa90588: bl              #0xa90490  ; [package:flutter/src/material/page_transitions_theme.dart] __ZoomExitTransitionState&State&_ZoomTransitionBase::__ZoomExitTransitionState&State&_ZoomTransitionBase
    // 0xa9058c: ldur            x0, [fp, #-8]
    // 0xa90590: LeaveFrame
    //     0xa90590: mov             SP, fp
    //     0xa90594: ldp             fp, lr, [SP], #0x10
    // 0xa90598: ret
    //     0xa90598: ret             
    // 0xa9059c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9059c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa905a0: b               #0xa90568
  }
}

// class id: 4829, size: 0x20, field offset: 0xc
//   const constructor, 
class _ZoomEnterTransition extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa9043c, size: 0x54
    // 0xa9043c: EnterFrame
    //     0xa9043c: stp             fp, lr, [SP, #-0x10]!
    //     0xa90440: mov             fp, SP
    // 0xa90444: AllocStack(0x8)
    //     0xa90444: sub             SP, SP, #8
    // 0xa90448: CheckStackOverflow
    //     0xa90448: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9044c: cmp             SP, x16
    //     0xa90450: b.ls            #0xa90488
    // 0xa90454: r1 = <_ZoomEnterTransition>
    //     0xa90454: add             x1, PP, #0x23, lsl #12  ; [pp+0x237b0] TypeArguments: <_ZoomEnterTransition>
    //     0xa90458: ldr             x1, [x1, #0x7b0]
    // 0xa9045c: r0 = _ZoomEnterTransitionState()
    //     0xa9045c: bl              #0xa90544  ; Allocate_ZoomEnterTransitionStateStub -> _ZoomEnterTransitionState (size=0x24)
    // 0xa90460: mov             x2, x0
    // 0xa90464: r0 = Sentinel
    //     0xa90464: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa90468: stur            x2, [fp, #-8]
    // 0xa9046c: StoreField: r2->field_1f = r0
    //     0xa9046c: stur            w0, [x2, #0x1f]
    // 0xa90470: mov             x1, x2
    // 0xa90474: r0 = __ZoomExitTransitionState&State&_ZoomTransitionBase()
    //     0xa90474: bl              #0xa90490  ; [package:flutter/src/material/page_transitions_theme.dart] __ZoomExitTransitionState&State&_ZoomTransitionBase::__ZoomExitTransitionState&State&_ZoomTransitionBase
    // 0xa90478: ldur            x0, [fp, #-8]
    // 0xa9047c: LeaveFrame
    //     0xa9047c: mov             SP, fp
    //     0xa90480: ldp             fp, lr, [SP], #0x10
    // 0xa90484: ret
    //     0xa90484: ret             
    // 0xa90488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa90488: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9048c: b               #0xa90454
  }
}

// class id: 5376, size: 0x24, field offset: 0xc
//   const constructor, 
class _ZoomPageTransition extends StatelessWidget {

  static late final TweenSequence<double> _scaleCurveSequence; // offset: 0xad8
  static late final List<TweenSequenceItem<double>> fastOutExtraSlowInTweenSequenceItems; // offset: 0xad4

  static TweenSequence<double> _scaleCurveSequence() {
    // ** addr: 0x937024, size: 0x6c
    // 0x937024: EnterFrame
    //     0x937024: stp             fp, lr, [SP, #-0x10]!
    //     0x937028: mov             fp, SP
    // 0x93702c: AllocStack(0x8)
    //     0x93702c: sub             SP, SP, #8
    // 0x937030: CheckStackOverflow
    //     0x937030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x937034: cmp             SP, x16
    //     0x937038: b.ls            #0x937088
    // 0x93703c: r0 = InitLateStaticField(0xad4) // [package:flutter/src/material/page_transitions_theme.dart] _ZoomPageTransition::fastOutExtraSlowInTweenSequenceItems
    //     0x93703c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x937040: ldr             x0, [x0, #0x15a8]
    //     0x937044: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x937048: cmp             w0, w16
    //     0x93704c: b.ne            #0x93705c
    //     0x937050: add             x2, PP, #0x24, lsl #12  ; [pp+0x24f98] Field <<EMAIL>>: static late final (offset: 0xad4)
    //     0x937054: ldr             x2, [x2, #0xf98]
    //     0x937058: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x93705c: r1 = <double>
    //     0x93705c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x937060: stur            x0, [fp, #-8]
    // 0x937064: r0 = TweenSequence()
    //     0x937064: bl              #0x8bf444  ; AllocateTweenSequenceStub -> TweenSequence<X0> (size=0x14)
    // 0x937068: mov             x1, x0
    // 0x93706c: ldur            x2, [fp, #-8]
    // 0x937070: stur            x0, [fp, #-8]
    // 0x937074: r0 = TweenSequence()
    //     0x937074: bl              #0x8bf1c4  ; [package:flutter/src/animation/tween_sequence.dart] TweenSequence::TweenSequence
    // 0x937078: ldur            x0, [fp, #-8]
    // 0x93707c: LeaveFrame
    //     0x93707c: mov             SP, fp
    //     0x937080: ldp             fp, lr, [SP], #0x10
    // 0x937084: ret
    //     0x937084: ret             
    // 0x937088: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x937088: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93708c: b               #0x93703c
  }
  static List<TweenSequenceItem<double>> fastOutExtraSlowInTweenSequenceItems() {
    // ** addr: 0x937090, size: 0x14c
    // 0x937090: EnterFrame
    //     0x937090: stp             fp, lr, [SP, #-0x10]!
    //     0x937094: mov             fp, SP
    // 0x937098: AllocStack(0x18)
    //     0x937098: sub             SP, SP, #0x18
    // 0x93709c: CheckStackOverflow
    //     0x93709c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9370a0: cmp             SP, x16
    //     0x9370a4: b.ls            #0x9371d4
    // 0x9370a8: r1 = <double>
    //     0x9370a8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9370ac: r0 = Tween()
    //     0x9370ac: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x9370b0: mov             x2, x0
    // 0x9370b4: r0 = 0.000000
    //     0x9370b4: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x9370b8: stur            x2, [fp, #-8]
    // 0x9370bc: StoreField: r2->field_b = r0
    //     0x9370bc: stur            w0, [x2, #0xb]
    // 0x9370c0: r0 = 0.400000
    //     0x9370c0: add             x0, PP, #0x24, lsl #12  ; [pp+0x24fb0] 0.4
    //     0x9370c4: ldr             x0, [x0, #0xfb0]
    // 0x9370c8: StoreField: r2->field_f = r0
    //     0x9370c8: stur            w0, [x2, #0xf]
    // 0x9370cc: r1 = <double>
    //     0x9370cc: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9370d0: r0 = CurveTween()
    //     0x9370d0: bl              #0x796a9c  ; AllocateCurveTweenStub -> CurveTween (size=0x10)
    // 0x9370d4: mov             x1, x0
    // 0x9370d8: r0 = Instance_Cubic
    //     0x9370d8: add             x0, PP, #0x24, lsl #12  ; [pp+0x24fb8] Obj!Cubic@e14f21
    //     0x9370dc: ldr             x0, [x0, #0xfb8]
    // 0x9370e0: StoreField: r1->field_b = r0
    //     0x9370e0: stur            w0, [x1, #0xb]
    // 0x9370e4: mov             x2, x1
    // 0x9370e8: ldur            x1, [fp, #-8]
    // 0x9370ec: r0 = chain()
    //     0x9370ec: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x9370f0: r1 = <double>
    //     0x9370f0: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9370f4: stur            x0, [fp, #-8]
    // 0x9370f8: r0 = TweenSequenceItem()
    //     0x9370f8: bl              #0x8bf450  ; AllocateTweenSequenceItemStub -> TweenSequenceItem<X0> (size=0x18)
    // 0x9370fc: mov             x2, x0
    // 0x937100: ldur            x0, [fp, #-8]
    // 0x937104: stur            x2, [fp, #-0x10]
    // 0x937108: StoreField: r2->field_b = r0
    //     0x937108: stur            w0, [x2, #0xb]
    // 0x93710c: d0 = 0.166666
    //     0x93710c: add             x17, PP, #0x24, lsl #12  ; [pp+0x24fc0] IMM: double(0.166666) from 0x3fc5554fbdad7519
    //     0x937110: ldr             d0, [x17, #0xfc0]
    // 0x937114: StoreField: r2->field_f = d0
    //     0x937114: stur            d0, [x2, #0xf]
    // 0x937118: r1 = <double>
    //     0x937118: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x93711c: r0 = Tween()
    //     0x93711c: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x937120: mov             x2, x0
    // 0x937124: r0 = 0.400000
    //     0x937124: add             x0, PP, #0x24, lsl #12  ; [pp+0x24fb0] 0.4
    //     0x937128: ldr             x0, [x0, #0xfb0]
    // 0x93712c: stur            x2, [fp, #-8]
    // 0x937130: StoreField: r2->field_b = r0
    //     0x937130: stur            w0, [x2, #0xb]
    // 0x937134: r0 = 1.000000
    //     0x937134: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x937138: StoreField: r2->field_f = r0
    //     0x937138: stur            w0, [x2, #0xf]
    // 0x93713c: r1 = <double>
    //     0x93713c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x937140: r0 = CurveTween()
    //     0x937140: bl              #0x796a9c  ; AllocateCurveTweenStub -> CurveTween (size=0x10)
    // 0x937144: mov             x1, x0
    // 0x937148: r0 = Instance_Cubic
    //     0x937148: add             x0, PP, #0x24, lsl #12  ; [pp+0x24fc8] Obj!Cubic@e14ef1
    //     0x93714c: ldr             x0, [x0, #0xfc8]
    // 0x937150: StoreField: r1->field_b = r0
    //     0x937150: stur            w0, [x1, #0xb]
    // 0x937154: mov             x2, x1
    // 0x937158: ldur            x1, [fp, #-8]
    // 0x93715c: r0 = chain()
    //     0x93715c: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x937160: r1 = <double>
    //     0x937160: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x937164: stur            x0, [fp, #-8]
    // 0x937168: r0 = TweenSequenceItem()
    //     0x937168: bl              #0x8bf450  ; AllocateTweenSequenceItemStub -> TweenSequenceItem<X0> (size=0x18)
    // 0x93716c: mov             x3, x0
    // 0x937170: ldur            x0, [fp, #-8]
    // 0x937174: stur            x3, [fp, #-0x18]
    // 0x937178: StoreField: r3->field_b = r0
    //     0x937178: stur            w0, [x3, #0xb]
    // 0x93717c: d0 = 0.833334
    //     0x93717c: add             x17, PP, #0x24, lsl #12  ; [pp+0x24fd0] IMM: double(0.833334) from 0x3feaaaac1094a2ba
    //     0x937180: ldr             d0, [x17, #0xfd0]
    // 0x937184: StoreField: r3->field_f = d0
    //     0x937184: stur            d0, [x3, #0xf]
    // 0x937188: r1 = Null
    //     0x937188: mov             x1, NULL
    // 0x93718c: r2 = 4
    //     0x93718c: movz            x2, #0x4
    // 0x937190: r0 = AllocateArray()
    //     0x937190: bl              #0xec22fc  ; AllocateArrayStub
    // 0x937194: mov             x2, x0
    // 0x937198: ldur            x0, [fp, #-0x10]
    // 0x93719c: stur            x2, [fp, #-8]
    // 0x9371a0: StoreField: r2->field_f = r0
    //     0x9371a0: stur            w0, [x2, #0xf]
    // 0x9371a4: ldur            x0, [fp, #-0x18]
    // 0x9371a8: StoreField: r2->field_13 = r0
    //     0x9371a8: stur            w0, [x2, #0x13]
    // 0x9371ac: r1 = <TweenSequenceItem<double>>
    //     0x9371ac: add             x1, PP, #0x24, lsl #12  ; [pp+0x24fd8] TypeArguments: <TweenSequenceItem<double>>
    //     0x9371b0: ldr             x1, [x1, #0xfd8]
    // 0x9371b4: r0 = AllocateGrowableArray()
    //     0x9371b4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x9371b8: ldur            x1, [fp, #-8]
    // 0x9371bc: StoreField: r0->field_f = r1
    //     0x9371bc: stur            w1, [x0, #0xf]
    // 0x9371c0: r1 = 4
    //     0x9371c0: movz            x1, #0x4
    // 0x9371c4: StoreField: r0->field_b = r1
    //     0x9371c4: stur            w1, [x0, #0xb]
    // 0x9371c8: LeaveFrame
    //     0x9371c8: mov             SP, fp
    //     0x9371cc: ldp             fp, lr, [SP], #0x10
    // 0x9371d0: ret
    //     0x9371d0: ret             
    // 0x9371d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9371d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9371d8: b               #0x9370a8
  }
  _ build(/* No info */) {
    // ** addr: 0xa9fafc, size: 0x11c
    // 0xa9fafc: EnterFrame
    //     0xa9fafc: stp             fp, lr, [SP, #-0x10]!
    //     0xa9fb00: mov             fp, SP
    // 0xa9fb04: AllocStack(0x20)
    //     0xa9fb04: sub             SP, SP, #0x20
    // 0xa9fb08: SetupParameters(_ZoomPageTransition this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa9fb08: mov             x0, x1
    //     0xa9fb0c: stur            x1, [fp, #-8]
    //     0xa9fb10: mov             x1, x2
    //     0xa9fb14: stur            x2, [fp, #-0x10]
    // 0xa9fb18: CheckStackOverflow
    //     0xa9fb18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9fb1c: cmp             SP, x16
    //     0xa9fb20: b.ls            #0xa9fc10
    // 0xa9fb24: r1 = 2
    //     0xa9fb24: movz            x1, #0x2
    // 0xa9fb28: r0 = AllocateContext()
    //     0xa9fb28: bl              #0xec126c  ; AllocateContextStub
    // 0xa9fb2c: mov             x2, x0
    // 0xa9fb30: ldur            x0, [fp, #-8]
    // 0xa9fb34: stur            x2, [fp, #-0x18]
    // 0xa9fb38: StoreField: r2->field_f = r0
    //     0xa9fb38: stur            w0, [x2, #0xf]
    // 0xa9fb3c: ldur            x1, [fp, #-0x10]
    // 0xa9fb40: r0 = of()
    //     0xa9fb40: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa9fb44: LoadField: r1 = r0->field_3f
    //     0xa9fb44: ldur            w1, [x0, #0x3f]
    // 0xa9fb48: DecompressPointer r1
    //     0xa9fb48: add             x1, x1, HEAP, lsl #32
    // 0xa9fb4c: LoadField: r2 = r1->field_7b
    //     0xa9fb4c: ldur            w2, [x1, #0x7b]
    // 0xa9fb50: DecompressPointer r2
    //     0xa9fb50: add             x2, x2, HEAP, lsl #32
    // 0xa9fb54: mov             x0, x2
    // 0xa9fb58: ldur            x4, [fp, #-0x18]
    // 0xa9fb5c: StoreField: r4->field_13 = r0
    //     0xa9fb5c: stur            w0, [x4, #0x13]
    //     0xa9fb60: ldurb           w16, [x4, #-1]
    //     0xa9fb64: ldurb           w17, [x0, #-1]
    //     0xa9fb68: and             x16, x17, x16, lsr #2
    //     0xa9fb6c: tst             x16, HEAP, lsr #32
    //     0xa9fb70: b.eq            #0xa9fb78
    //     0xa9fb74: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xa9fb78: ldur            x0, [fp, #-8]
    // 0xa9fb7c: LoadField: r7 = r0->field_b
    //     0xa9fb7c: ldur            w7, [x0, #0xb]
    // 0xa9fb80: DecompressPointer r7
    //     0xa9fb80: add             x7, x7, HEAP, lsl #32
    // 0xa9fb84: stur            x7, [fp, #-0x20]
    // 0xa9fb88: LoadField: r1 = r0->field_f
    //     0xa9fb88: ldur            w1, [x0, #0xf]
    // 0xa9fb8c: DecompressPointer r1
    //     0xa9fb8c: add             x1, x1, HEAP, lsl #32
    // 0xa9fb90: LoadField: r3 = r0->field_1b
    //     0xa9fb90: ldur            w3, [x0, #0x1b]
    // 0xa9fb94: DecompressPointer r3
    //     0xa9fb94: add             x3, x3, HEAP, lsl #32
    // 0xa9fb98: mov             x6, x2
    // 0xa9fb9c: mov             x2, x1
    // 0xa9fba0: ldur            x1, [fp, #-0x10]
    // 0xa9fba4: r5 = true
    //     0xa9fba4: add             x5, NULL, #0x20  ; true
    // 0xa9fba8: r0 = _snapshotAwareDelegatedTransition()
    //     0xa9fba8: bl              #0xa9fc24  ; [package:flutter/src/material/page_transitions_theme.dart] ZoomPageTransitionsBuilder::_snapshotAwareDelegatedTransition
    // 0xa9fbac: stur            x0, [fp, #-8]
    // 0xa9fbb0: r0 = DualTransitionBuilder()
    //     0xa9fbb0: bl              #0xa9fc18  ; AllocateDualTransitionBuilderStub -> DualTransitionBuilder (size=0x1c)
    // 0xa9fbb4: mov             x3, x0
    // 0xa9fbb8: ldur            x0, [fp, #-0x20]
    // 0xa9fbbc: stur            x3, [fp, #-0x10]
    // 0xa9fbc0: StoreField: r3->field_b = r0
    //     0xa9fbc0: stur            w0, [x3, #0xb]
    // 0xa9fbc4: ldur            x2, [fp, #-0x18]
    // 0xa9fbc8: r1 = Function '<anonymous closure>':.
    //     0xa9fbc8: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4dc48] AnonymousClosure: (0xa9fe5c), in [package:flutter/src/material/page_transitions_theme.dart] _ZoomPageTransition::build (0xa9fafc)
    //     0xa9fbcc: ldr             x1, [x1, #0xc48]
    // 0xa9fbd0: r0 = AllocateClosure()
    //     0xa9fbd0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa9fbd4: mov             x1, x0
    // 0xa9fbd8: ldur            x0, [fp, #-0x10]
    // 0xa9fbdc: StoreField: r0->field_f = r1
    //     0xa9fbdc: stur            w1, [x0, #0xf]
    // 0xa9fbe0: ldur            x2, [fp, #-0x18]
    // 0xa9fbe4: r1 = Function '<anonymous closure>':.
    //     0xa9fbe4: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4dc50] AnonymousClosure: (0xa9fe28), in [package:flutter/src/material/page_transitions_theme.dart] _ZoomPageTransition::build (0xa9fafc)
    //     0xa9fbe8: ldr             x1, [x1, #0xc50]
    // 0xa9fbec: r0 = AllocateClosure()
    //     0xa9fbec: bl              #0xec1630  ; AllocateClosureStub
    // 0xa9fbf0: mov             x1, x0
    // 0xa9fbf4: ldur            x0, [fp, #-0x10]
    // 0xa9fbf8: StoreField: r0->field_13 = r1
    //     0xa9fbf8: stur            w1, [x0, #0x13]
    // 0xa9fbfc: ldur            x1, [fp, #-8]
    // 0xa9fc00: ArrayStore: r0[0] = r1  ; List_4
    //     0xa9fc00: stur            w1, [x0, #0x17]
    // 0xa9fc04: LeaveFrame
    //     0xa9fc04: mov             SP, fp
    //     0xa9fc08: ldp             fp, lr, [SP], #0x10
    // 0xa9fc0c: ret
    //     0xa9fc0c: ret             
    // 0xa9fc10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9fc10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9fc14: b               #0xa9fb24
  }
  [closure] _ZoomExitTransition <anonymous closure>(dynamic, BuildContext, Animation<double>, Widget?) {
    // ** addr: 0xa9fe28, size: 0x34
    // 0xa9fe28: EnterFrame
    //     0xa9fe28: stp             fp, lr, [SP, #-0x10]!
    //     0xa9fe2c: mov             fp, SP
    // 0xa9fe30: r0 = _ZoomExitTransition()
    //     0xa9fe30: bl              #0xa9fd90  ; Allocate_ZoomExitTransitionStub -> _ZoomExitTransition (size=0x1c)
    // 0xa9fe34: ldr             x1, [fp, #0x18]
    // 0xa9fe38: StoreField: r0->field_b = r1
    //     0xa9fe38: stur            w1, [x0, #0xb]
    // 0xa9fe3c: r1 = true
    //     0xa9fe3c: add             x1, NULL, #0x20  ; true
    // 0xa9fe40: StoreField: r0->field_13 = r1
    //     0xa9fe40: stur            w1, [x0, #0x13]
    // 0xa9fe44: StoreField: r0->field_f = r1
    //     0xa9fe44: stur            w1, [x0, #0xf]
    // 0xa9fe48: ldr             x1, [fp, #0x10]
    // 0xa9fe4c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa9fe4c: stur            w1, [x0, #0x17]
    // 0xa9fe50: LeaveFrame
    //     0xa9fe50: mov             SP, fp
    //     0xa9fe54: ldp             fp, lr, [SP], #0x10
    // 0xa9fe58: ret
    //     0xa9fe58: ret             
  }
  [closure] _ZoomEnterTransition <anonymous closure>(dynamic, BuildContext, Animation<double>, Widget?) {
    // ** addr: 0xa9fe5c, size: 0x5c
    // 0xa9fe5c: EnterFrame
    //     0xa9fe5c: stp             fp, lr, [SP, #-0x10]!
    //     0xa9fe60: mov             fp, SP
    // 0xa9fe64: AllocStack(0x8)
    //     0xa9fe64: sub             SP, SP, #8
    // 0xa9fe68: SetupParameters()
    //     0xa9fe68: ldr             x0, [fp, #0x28]
    //     0xa9fe6c: ldur            w1, [x0, #0x17]
    //     0xa9fe70: add             x1, x1, HEAP, lsl #32
    // 0xa9fe74: LoadField: r0 = r1->field_13
    //     0xa9fe74: ldur            w0, [x1, #0x13]
    // 0xa9fe78: DecompressPointer r0
    //     0xa9fe78: add             x0, x0, HEAP, lsl #32
    // 0xa9fe7c: stur            x0, [fp, #-8]
    // 0xa9fe80: r0 = _ZoomEnterTransition()
    //     0xa9fe80: bl              #0xa9fe1c  ; Allocate_ZoomEnterTransitionStub -> _ZoomEnterTransition (size=0x20)
    // 0xa9fe84: ldr             x1, [fp, #0x18]
    // 0xa9fe88: StoreField: r0->field_b = r1
    //     0xa9fe88: stur            w1, [x0, #0xb]
    // 0xa9fe8c: r1 = false
    //     0xa9fe8c: add             x1, NULL, #0x30  ; false
    // 0xa9fe90: ArrayStore: r0[0] = r1  ; List_4
    //     0xa9fe90: stur            w1, [x0, #0x17]
    // 0xa9fe94: r1 = true
    //     0xa9fe94: add             x1, NULL, #0x20  ; true
    // 0xa9fe98: StoreField: r0->field_13 = r1
    //     0xa9fe98: stur            w1, [x0, #0x13]
    // 0xa9fe9c: ldur            x1, [fp, #-8]
    // 0xa9fea0: StoreField: r0->field_1b = r1
    //     0xa9fea0: stur            w1, [x0, #0x1b]
    // 0xa9fea4: ldr             x1, [fp, #0x10]
    // 0xa9fea8: StoreField: r0->field_f = r1
    //     0xa9fea8: stur            w1, [x0, #0xf]
    // 0xa9feac: LeaveFrame
    //     0xa9feac: mov             SP, fp
    //     0xa9feb0: ldp             fp, lr, [SP], #0x10
    // 0xa9feb4: ret
    //     0xa9feb4: ret             
  }
}
