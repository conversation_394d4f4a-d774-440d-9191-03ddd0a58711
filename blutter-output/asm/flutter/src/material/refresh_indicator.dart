// lib: , url: package:flutter/src/material/refresh_indicator.dart

// class id: 1048938, size: 0x8
class :: {
}

// class id: 4270, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class _RefreshIndicatorState&State&TickerProviderStateMixin extends State<dynamic>
     with TickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6f52d8, size: 0x184
    // 0x6f52d8: EnterFrame
    //     0x6f52d8: stp             fp, lr, [SP, #-0x10]!
    //     0x6f52dc: mov             fp, SP
    // 0x6f52e0: AllocStack(0x20)
    //     0x6f52e0: sub             SP, SP, #0x20
    // 0x6f52e4: SetupParameters(_RefreshIndicatorState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6f52e4: mov             x0, x1
    //     0x6f52e8: stur            x1, [fp, #-8]
    //     0x6f52ec: stur            x2, [fp, #-0x10]
    // 0x6f52f0: CheckStackOverflow
    //     0x6f52f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f52f4: cmp             SP, x16
    //     0x6f52f8: b.ls            #0x6f544c
    // 0x6f52fc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6f52fc: ldur            w1, [x0, #0x17]
    // 0x6f5300: DecompressPointer r1
    //     0x6f5300: add             x1, x1, HEAP, lsl #32
    // 0x6f5304: cmp             w1, NULL
    // 0x6f5308: b.ne            #0x6f5314
    // 0x6f530c: mov             x1, x0
    // 0x6f5310: r0 = _updateTickerModeNotifier()
    //     0x6f5310: bl              #0x6f5480  ; [package:flutter/src/material/refresh_indicator.dart] _RefreshIndicatorState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6f5314: ldur            x0, [fp, #-8]
    // 0x6f5318: LoadField: r1 = r0->field_13
    //     0x6f5318: ldur            w1, [x0, #0x13]
    // 0x6f531c: DecompressPointer r1
    //     0x6f531c: add             x1, x1, HEAP, lsl #32
    // 0x6f5320: cmp             w1, NULL
    // 0x6f5324: b.ne            #0x6f53bc
    // 0x6f5328: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x6f5328: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6f532c: ldr             x0, [x0, #0x778]
    //     0x6f5330: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6f5334: cmp             w0, w16
    //     0x6f5338: b.ne            #0x6f5344
    //     0x6f533c: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x6f5340: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6f5344: r1 = <_WidgetTicker>
    //     0x6f5344: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8c0] TypeArguments: <_WidgetTicker>
    //     0x6f5348: ldr             x1, [x1, #0x8c0]
    // 0x6f534c: stur            x0, [fp, #-0x18]
    // 0x6f5350: r0 = _Set()
    //     0x6f5350: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x6f5354: mov             x1, x0
    // 0x6f5358: ldur            x0, [fp, #-0x18]
    // 0x6f535c: stur            x1, [fp, #-0x20]
    // 0x6f5360: StoreField: r1->field_1b = r0
    //     0x6f5360: stur            w0, [x1, #0x1b]
    // 0x6f5364: StoreField: r1->field_b = rZR
    //     0x6f5364: stur            wzr, [x1, #0xb]
    // 0x6f5368: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x6f5368: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6f536c: ldr             x0, [x0, #0x780]
    //     0x6f5370: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6f5374: cmp             w0, w16
    //     0x6f5378: b.ne            #0x6f5384
    //     0x6f537c: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x6f5380: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6f5384: mov             x1, x0
    // 0x6f5388: ldur            x0, [fp, #-0x20]
    // 0x6f538c: StoreField: r0->field_f = r1
    //     0x6f538c: stur            w1, [x0, #0xf]
    // 0x6f5390: StoreField: r0->field_13 = rZR
    //     0x6f5390: stur            wzr, [x0, #0x13]
    // 0x6f5394: ArrayStore: r0[0] = rZR  ; List_4
    //     0x6f5394: stur            wzr, [x0, #0x17]
    // 0x6f5398: ldur            x1, [fp, #-8]
    // 0x6f539c: StoreField: r1->field_13 = r0
    //     0x6f539c: stur            w0, [x1, #0x13]
    //     0x6f53a0: ldurb           w16, [x1, #-1]
    //     0x6f53a4: ldurb           w17, [x0, #-1]
    //     0x6f53a8: and             x16, x17, x16, lsr #2
    //     0x6f53ac: tst             x16, HEAP, lsr #32
    //     0x6f53b0: b.eq            #0x6f53b8
    //     0x6f53b4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6f53b8: b               #0x6f53c0
    // 0x6f53bc: mov             x1, x0
    // 0x6f53c0: ldur            x0, [fp, #-0x10]
    // 0x6f53c4: r0 = _WidgetTicker()
    //     0x6f53c4: bl              #0x6f03ec  ; Allocate_WidgetTickerStub -> _WidgetTicker (size=0x20)
    // 0x6f53c8: mov             x3, x0
    // 0x6f53cc: ldur            x2, [fp, #-8]
    // 0x6f53d0: stur            x3, [fp, #-0x18]
    // 0x6f53d4: StoreField: r3->field_1b = r2
    //     0x6f53d4: stur            w2, [x3, #0x1b]
    // 0x6f53d8: r0 = false
    //     0x6f53d8: add             x0, NULL, #0x30  ; false
    // 0x6f53dc: StoreField: r3->field_b = r0
    //     0x6f53dc: stur            w0, [x3, #0xb]
    // 0x6f53e0: ldur            x0, [fp, #-0x10]
    // 0x6f53e4: StoreField: r3->field_13 = r0
    //     0x6f53e4: stur            w0, [x3, #0x13]
    // 0x6f53e8: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6f53e8: ldur            w1, [x2, #0x17]
    // 0x6f53ec: DecompressPointer r1
    //     0x6f53ec: add             x1, x1, HEAP, lsl #32
    // 0x6f53f0: cmp             w1, NULL
    // 0x6f53f4: b.eq            #0x6f5454
    // 0x6f53f8: r0 = LoadClassIdInstr(r1)
    //     0x6f53f8: ldur            x0, [x1, #-1]
    //     0x6f53fc: ubfx            x0, x0, #0xc, #0x14
    // 0x6f5400: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6f5400: movz            x17, #0x276f
    //     0x6f5404: movk            x17, #0x1, lsl #16
    //     0x6f5408: add             lr, x0, x17
    //     0x6f540c: ldr             lr, [x21, lr, lsl #3]
    //     0x6f5410: blr             lr
    // 0x6f5414: eor             x2, x0, #0x10
    // 0x6f5418: ldur            x1, [fp, #-0x18]
    // 0x6f541c: r0 = muted=()
    //     0x6f541c: bl              #0x6efbf4  ; [package:flutter/src/scheduler/ticker.dart] Ticker::muted=
    // 0x6f5420: ldur            x0, [fp, #-8]
    // 0x6f5424: LoadField: r1 = r0->field_13
    //     0x6f5424: ldur            w1, [x0, #0x13]
    // 0x6f5428: DecompressPointer r1
    //     0x6f5428: add             x1, x1, HEAP, lsl #32
    // 0x6f542c: cmp             w1, NULL
    // 0x6f5430: b.eq            #0x6f5458
    // 0x6f5434: ldur            x2, [fp, #-0x18]
    // 0x6f5438: r0 = add()
    //     0x6f5438: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x6f543c: ldur            x0, [fp, #-0x18]
    // 0x6f5440: LeaveFrame
    //     0x6f5440: mov             SP, fp
    //     0x6f5444: ldp             fp, lr, [SP], #0x10
    // 0x6f5448: ret
    //     0x6f5448: ret             
    // 0x6f544c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f544c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f5450: b               #0x6f52fc
    // 0x6f5454: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f5454: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6f5458: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f5458: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6f5480, size: 0x124
    // 0x6f5480: EnterFrame
    //     0x6f5480: stp             fp, lr, [SP, #-0x10]!
    //     0x6f5484: mov             fp, SP
    // 0x6f5488: AllocStack(0x18)
    //     0x6f5488: sub             SP, SP, #0x18
    // 0x6f548c: SetupParameters(_RefreshIndicatorState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6f548c: mov             x2, x1
    //     0x6f5490: stur            x1, [fp, #-8]
    // 0x6f5494: CheckStackOverflow
    //     0x6f5494: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f5498: cmp             SP, x16
    //     0x6f549c: b.ls            #0x6f5598
    // 0x6f54a0: LoadField: r1 = r2->field_f
    //     0x6f54a0: ldur            w1, [x2, #0xf]
    // 0x6f54a4: DecompressPointer r1
    //     0x6f54a4: add             x1, x1, HEAP, lsl #32
    // 0x6f54a8: cmp             w1, NULL
    // 0x6f54ac: b.eq            #0x6f55a0
    // 0x6f54b0: r0 = getNotifier()
    //     0x6f54b0: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6f54b4: mov             x3, x0
    // 0x6f54b8: ldur            x0, [fp, #-8]
    // 0x6f54bc: stur            x3, [fp, #-0x18]
    // 0x6f54c0: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6f54c0: ldur            w4, [x0, #0x17]
    // 0x6f54c4: DecompressPointer r4
    //     0x6f54c4: add             x4, x4, HEAP, lsl #32
    // 0x6f54c8: stur            x4, [fp, #-0x10]
    // 0x6f54cc: cmp             w3, w4
    // 0x6f54d0: b.ne            #0x6f54e4
    // 0x6f54d4: r0 = Null
    //     0x6f54d4: mov             x0, NULL
    // 0x6f54d8: LeaveFrame
    //     0x6f54d8: mov             SP, fp
    //     0x6f54dc: ldp             fp, lr, [SP], #0x10
    // 0x6f54e0: ret
    //     0x6f54e0: ret             
    // 0x6f54e4: cmp             w4, NULL
    // 0x6f54e8: b.eq            #0x6f552c
    // 0x6f54ec: mov             x2, x0
    // 0x6f54f0: r1 = Function '_updateTickers@364311458':.
    //     0x6f54f0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44108] AnonymousClosure: (0x6f55a4), in [package:flutter/src/material/refresh_indicator.dart] _RefreshIndicatorState&State&TickerProviderStateMixin::_updateTickers (0x6f55dc)
    //     0x6f54f4: ldr             x1, [x1, #0x108]
    // 0x6f54f8: r0 = AllocateClosure()
    //     0x6f54f8: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f54fc: ldur            x1, [fp, #-0x10]
    // 0x6f5500: r2 = LoadClassIdInstr(r1)
    //     0x6f5500: ldur            x2, [x1, #-1]
    //     0x6f5504: ubfx            x2, x2, #0xc, #0x14
    // 0x6f5508: mov             x16, x0
    // 0x6f550c: mov             x0, x2
    // 0x6f5510: mov             x2, x16
    // 0x6f5514: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6f5514: movz            x17, #0xbf5c
    //     0x6f5518: add             lr, x0, x17
    //     0x6f551c: ldr             lr, [x21, lr, lsl #3]
    //     0x6f5520: blr             lr
    // 0x6f5524: ldur            x0, [fp, #-8]
    // 0x6f5528: ldur            x3, [fp, #-0x18]
    // 0x6f552c: mov             x2, x0
    // 0x6f5530: r1 = Function '_updateTickers@364311458':.
    //     0x6f5530: add             x1, PP, #0x44, lsl #12  ; [pp+0x44108] AnonymousClosure: (0x6f55a4), in [package:flutter/src/material/refresh_indicator.dart] _RefreshIndicatorState&State&TickerProviderStateMixin::_updateTickers (0x6f55dc)
    //     0x6f5534: ldr             x1, [x1, #0x108]
    // 0x6f5538: r0 = AllocateClosure()
    //     0x6f5538: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f553c: ldur            x3, [fp, #-0x18]
    // 0x6f5540: r1 = LoadClassIdInstr(r3)
    //     0x6f5540: ldur            x1, [x3, #-1]
    //     0x6f5544: ubfx            x1, x1, #0xc, #0x14
    // 0x6f5548: mov             x2, x0
    // 0x6f554c: mov             x0, x1
    // 0x6f5550: mov             x1, x3
    // 0x6f5554: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6f5554: movz            x17, #0xc407
    //     0x6f5558: add             lr, x0, x17
    //     0x6f555c: ldr             lr, [x21, lr, lsl #3]
    //     0x6f5560: blr             lr
    // 0x6f5564: ldur            x0, [fp, #-0x18]
    // 0x6f5568: ldur            x1, [fp, #-8]
    // 0x6f556c: ArrayStore: r1[0] = r0  ; List_4
    //     0x6f556c: stur            w0, [x1, #0x17]
    //     0x6f5570: ldurb           w16, [x1, #-1]
    //     0x6f5574: ldurb           w17, [x0, #-1]
    //     0x6f5578: and             x16, x17, x16, lsr #2
    //     0x6f557c: tst             x16, HEAP, lsr #32
    //     0x6f5580: b.eq            #0x6f5588
    //     0x6f5584: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6f5588: r0 = Null
    //     0x6f5588: mov             x0, NULL
    // 0x6f558c: LeaveFrame
    //     0x6f558c: mov             SP, fp
    //     0x6f5590: ldp             fp, lr, [SP], #0x10
    // 0x6f5594: ret
    //     0x6f5594: ret             
    // 0x6f5598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f5598: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f559c: b               #0x6f54a0
    // 0x6f55a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f55a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTickers(dynamic) {
    // ** addr: 0x6f55a4, size: 0x38
    // 0x6f55a4: EnterFrame
    //     0x6f55a4: stp             fp, lr, [SP, #-0x10]!
    //     0x6f55a8: mov             fp, SP
    // 0x6f55ac: ldr             x0, [fp, #0x10]
    // 0x6f55b0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6f55b0: ldur            w1, [x0, #0x17]
    // 0x6f55b4: DecompressPointer r1
    //     0x6f55b4: add             x1, x1, HEAP, lsl #32
    // 0x6f55b8: CheckStackOverflow
    //     0x6f55b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f55bc: cmp             SP, x16
    //     0x6f55c0: b.ls            #0x6f55d4
    // 0x6f55c4: r0 = _updateTickers()
    //     0x6f55c4: bl              #0x6f55dc  ; [package:flutter/src/material/refresh_indicator.dart] _RefreshIndicatorState&State&TickerProviderStateMixin::_updateTickers
    // 0x6f55c8: LeaveFrame
    //     0x6f55c8: mov             SP, fp
    //     0x6f55cc: ldp             fp, lr, [SP], #0x10
    // 0x6f55d0: ret
    //     0x6f55d0: ret             
    // 0x6f55d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f55d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f55d8: b               #0x6f55c4
  }
  _ _updateTickers(/* No info */) {
    // ** addr: 0x6f55dc, size: 0x164
    // 0x6f55dc: EnterFrame
    //     0x6f55dc: stp             fp, lr, [SP, #-0x10]!
    //     0x6f55e0: mov             fp, SP
    // 0x6f55e4: AllocStack(0x20)
    //     0x6f55e4: sub             SP, SP, #0x20
    // 0x6f55e8: SetupParameters(_RefreshIndicatorState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6f55e8: mov             x2, x1
    //     0x6f55ec: stur            x1, [fp, #-8]
    // 0x6f55f0: CheckStackOverflow
    //     0x6f55f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f55f4: cmp             SP, x16
    //     0x6f55f8: b.ls            #0x6f5728
    // 0x6f55fc: LoadField: r0 = r2->field_13
    //     0x6f55fc: ldur            w0, [x2, #0x13]
    // 0x6f5600: DecompressPointer r0
    //     0x6f5600: add             x0, x0, HEAP, lsl #32
    // 0x6f5604: cmp             w0, NULL
    // 0x6f5608: b.eq            #0x6f5718
    // 0x6f560c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6f560c: ldur            w1, [x2, #0x17]
    // 0x6f5610: DecompressPointer r1
    //     0x6f5610: add             x1, x1, HEAP, lsl #32
    // 0x6f5614: cmp             w1, NULL
    // 0x6f5618: b.eq            #0x6f5730
    // 0x6f561c: r0 = LoadClassIdInstr(r1)
    //     0x6f561c: ldur            x0, [x1, #-1]
    //     0x6f5620: ubfx            x0, x0, #0xc, #0x14
    // 0x6f5624: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6f5624: movz            x17, #0x276f
    //     0x6f5628: movk            x17, #0x1, lsl #16
    //     0x6f562c: add             lr, x0, x17
    //     0x6f5630: ldr             lr, [x21, lr, lsl #3]
    //     0x6f5634: blr             lr
    // 0x6f5638: eor             x2, x0, #0x10
    // 0x6f563c: ldur            x0, [fp, #-8]
    // 0x6f5640: stur            x2, [fp, #-0x10]
    // 0x6f5644: LoadField: r1 = r0->field_13
    //     0x6f5644: ldur            w1, [x0, #0x13]
    // 0x6f5648: DecompressPointer r1
    //     0x6f5648: add             x1, x1, HEAP, lsl #32
    // 0x6f564c: cmp             w1, NULL
    // 0x6f5650: b.eq            #0x6f5734
    // 0x6f5654: r0 = iterator()
    //     0x6f5654: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x6f5658: stur            x0, [fp, #-0x18]
    // 0x6f565c: LoadField: r2 = r0->field_7
    //     0x6f565c: ldur            w2, [x0, #7]
    // 0x6f5660: DecompressPointer r2
    //     0x6f5660: add             x2, x2, HEAP, lsl #32
    // 0x6f5664: stur            x2, [fp, #-8]
    // 0x6f5668: ldur            x3, [fp, #-0x10]
    // 0x6f566c: CheckStackOverflow
    //     0x6f566c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f5670: cmp             SP, x16
    //     0x6f5674: b.ls            #0x6f5738
    // 0x6f5678: mov             x1, x0
    // 0x6f567c: r0 = moveNext()
    //     0x6f567c: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x6f5680: tbnz            w0, #4, #0x6f5718
    // 0x6f5684: ldur            x3, [fp, #-0x18]
    // 0x6f5688: LoadField: r4 = r3->field_33
    //     0x6f5688: ldur            w4, [x3, #0x33]
    // 0x6f568c: DecompressPointer r4
    //     0x6f568c: add             x4, x4, HEAP, lsl #32
    // 0x6f5690: stur            x4, [fp, #-0x20]
    // 0x6f5694: cmp             w4, NULL
    // 0x6f5698: b.ne            #0x6f56cc
    // 0x6f569c: mov             x0, x4
    // 0x6f56a0: ldur            x2, [fp, #-8]
    // 0x6f56a4: r1 = Null
    //     0x6f56a4: mov             x1, NULL
    // 0x6f56a8: cmp             w2, NULL
    // 0x6f56ac: b.eq            #0x6f56cc
    // 0x6f56b0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6f56b0: ldur            w4, [x2, #0x17]
    // 0x6f56b4: DecompressPointer r4
    //     0x6f56b4: add             x4, x4, HEAP, lsl #32
    // 0x6f56b8: r8 = X0
    //     0x6f56b8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6f56bc: LoadField: r9 = r4->field_7
    //     0x6f56bc: ldur            x9, [x4, #7]
    // 0x6f56c0: r3 = Null
    //     0x6f56c0: add             x3, PP, #0x44, lsl #12  ; [pp+0x440f8] Null
    //     0x6f56c4: ldr             x3, [x3, #0xf8]
    // 0x6f56c8: blr             x9
    // 0x6f56cc: ldur            x2, [fp, #-0x10]
    // 0x6f56d0: ldur            x0, [fp, #-0x20]
    // 0x6f56d4: LoadField: r1 = r0->field_b
    //     0x6f56d4: ldur            w1, [x0, #0xb]
    // 0x6f56d8: DecompressPointer r1
    //     0x6f56d8: add             x1, x1, HEAP, lsl #32
    // 0x6f56dc: cmp             w2, w1
    // 0x6f56e0: b.eq            #0x6f570c
    // 0x6f56e4: StoreField: r0->field_b = r2
    //     0x6f56e4: stur            w2, [x0, #0xb]
    // 0x6f56e8: tbnz            w2, #4, #0x6f56f8
    // 0x6f56ec: mov             x1, x0
    // 0x6f56f0: r0 = unscheduleTick()
    //     0x6f56f0: bl              #0x656684  ; [package:flutter/src/scheduler/ticker.dart] Ticker::unscheduleTick
    // 0x6f56f4: b               #0x6f570c
    // 0x6f56f8: mov             x1, x0
    // 0x6f56fc: r0 = shouldScheduleTick()
    //     0x6f56fc: bl              #0x655b90  ; [package:flutter/src/scheduler/ticker.dart] Ticker::shouldScheduleTick
    // 0x6f5700: tbnz            w0, #4, #0x6f570c
    // 0x6f5704: ldur            x1, [fp, #-0x20]
    // 0x6f5708: r0 = scheduleTick()
    //     0x6f5708: bl              #0x655924  ; [package:flutter/src/scheduler/ticker.dart] Ticker::scheduleTick
    // 0x6f570c: ldur            x0, [fp, #-0x18]
    // 0x6f5710: ldur            x2, [fp, #-8]
    // 0x6f5714: b               #0x6f5668
    // 0x6f5718: r0 = Null
    //     0x6f5718: mov             x0, NULL
    // 0x6f571c: LeaveFrame
    //     0x6f571c: mov             SP, fp
    //     0x6f5720: ldp             fp, lr, [SP], #0x10
    // 0x6f5724: ret
    //     0x6f5724: ret             
    // 0x6f5728: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f5728: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f572c: b               #0x6f55fc
    // 0x6f5730: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f5730: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6f5734: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f5734: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6f5738: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f5738: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f573c: b               #0x6f5678
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7e350, size: 0x94
    // 0xa7e350: EnterFrame
    //     0xa7e350: stp             fp, lr, [SP, #-0x10]!
    //     0xa7e354: mov             fp, SP
    // 0xa7e358: AllocStack(0x10)
    //     0xa7e358: sub             SP, SP, #0x10
    // 0xa7e35c: SetupParameters(_RefreshIndicatorState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa7e35c: mov             x0, x1
    //     0xa7e360: stur            x1, [fp, #-0x10]
    // 0xa7e364: CheckStackOverflow
    //     0xa7e364: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7e368: cmp             SP, x16
    //     0xa7e36c: b.ls            #0xa7e3dc
    // 0xa7e370: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa7e370: ldur            w3, [x0, #0x17]
    // 0xa7e374: DecompressPointer r3
    //     0xa7e374: add             x3, x3, HEAP, lsl #32
    // 0xa7e378: stur            x3, [fp, #-8]
    // 0xa7e37c: cmp             w3, NULL
    // 0xa7e380: b.ne            #0xa7e38c
    // 0xa7e384: mov             x1, x0
    // 0xa7e388: b               #0xa7e3c8
    // 0xa7e38c: mov             x2, x0
    // 0xa7e390: r1 = Function '_updateTickers@364311458':.
    //     0xa7e390: add             x1, PP, #0x44, lsl #12  ; [pp+0x44108] AnonymousClosure: (0x6f55a4), in [package:flutter/src/material/refresh_indicator.dart] _RefreshIndicatorState&State&TickerProviderStateMixin::_updateTickers (0x6f55dc)
    //     0xa7e394: ldr             x1, [x1, #0x108]
    // 0xa7e398: r0 = AllocateClosure()
    //     0xa7e398: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7e39c: ldur            x1, [fp, #-8]
    // 0xa7e3a0: r2 = LoadClassIdInstr(r1)
    //     0xa7e3a0: ldur            x2, [x1, #-1]
    //     0xa7e3a4: ubfx            x2, x2, #0xc, #0x14
    // 0xa7e3a8: mov             x16, x0
    // 0xa7e3ac: mov             x0, x2
    // 0xa7e3b0: mov             x2, x16
    // 0xa7e3b4: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa7e3b4: movz            x17, #0xbf5c
    //     0xa7e3b8: add             lr, x0, x17
    //     0xa7e3bc: ldr             lr, [x21, lr, lsl #3]
    //     0xa7e3c0: blr             lr
    // 0xa7e3c4: ldur            x1, [fp, #-0x10]
    // 0xa7e3c8: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa7e3c8: stur            NULL, [x1, #0x17]
    // 0xa7e3cc: r0 = Null
    //     0xa7e3cc: mov             x0, NULL
    // 0xa7e3d0: LeaveFrame
    //     0xa7e3d0: mov             SP, fp
    //     0xa7e3d4: ldp             fp, lr, [SP], #0x10
    // 0xa7e3d8: ret
    //     0xa7e3d8: ret             
    // 0xa7e3dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7e3dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7e3e0: b               #0xa7e370
  }
  _ activate(/* No info */) {
    // ** addr: 0xa850c4, size: 0x48
    // 0xa850c4: EnterFrame
    //     0xa850c4: stp             fp, lr, [SP, #-0x10]!
    //     0xa850c8: mov             fp, SP
    // 0xa850cc: AllocStack(0x8)
    //     0xa850cc: sub             SP, SP, #8
    // 0xa850d0: SetupParameters(_RefreshIndicatorState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa850d0: mov             x0, x1
    //     0xa850d4: stur            x1, [fp, #-8]
    // 0xa850d8: CheckStackOverflow
    //     0xa850d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa850dc: cmp             SP, x16
    //     0xa850e0: b.ls            #0xa85104
    // 0xa850e4: mov             x1, x0
    // 0xa850e8: r0 = _updateTickerModeNotifier()
    //     0xa850e8: bl              #0x6f5480  ; [package:flutter/src/material/refresh_indicator.dart] _RefreshIndicatorState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa850ec: ldur            x1, [fp, #-8]
    // 0xa850f0: r0 = _updateTickers()
    //     0xa850f0: bl              #0x6f55dc  ; [package:flutter/src/material/refresh_indicator.dart] _RefreshIndicatorState&State&TickerProviderStateMixin::_updateTickers
    // 0xa850f4: r0 = Null
    //     0xa850f4: mov             x0, NULL
    // 0xa850f8: LeaveFrame
    //     0xa850f8: mov             SP, fp
    //     0xa850fc: ldp             fp, lr, [SP], #0x10
    // 0xa85100: ret
    //     0xa85100: ret             
    // 0xa85104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85104: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85108: b               #0xa850e4
  }
}

// class id: 4271, size: 0x44, field offset: 0x1c
class RefreshIndicatorState extends _RefreshIndicatorState&State&TickerProviderStateMixin {

  late Animation<double> _positionFactor; // offset: 0x24
  late Animation<double> _scaleFactor; // offset: 0x28
  late AnimationController _positionController; // offset: 0x1c
  late Animation<double> _value; // offset: 0x2c
  late Animation<Color?> _valueColor; // offset: 0x30
  late Color _effectiveValueColor; // offset: 0x40
  late AnimationController _scaleController; // offset: 0x20
  static late final Animatable<double> _kDragSizeFactorLimitTween; // offset: 0xb24
  static late final Animatable<double> _threeQuarterTween; // offset: 0xb20
  static late final Animatable<double> _oneToZeroTween; // offset: 0xb28

  _ initState(/* No info */) {
    // ** addr: 0x938444, size: 0x1a8
    // 0x938444: EnterFrame
    //     0x938444: stp             fp, lr, [SP, #-0x10]!
    //     0x938448: mov             fp, SP
    // 0x93844c: AllocStack(0x10)
    //     0x93844c: sub             SP, SP, #0x10
    // 0x938450: SetupParameters(RefreshIndicatorState this /* r1 => r2, fp-0x8 */)
    //     0x938450: mov             x2, x1
    //     0x938454: stur            x1, [fp, #-8]
    // 0x938458: CheckStackOverflow
    //     0x938458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93845c: cmp             SP, x16
    //     0x938460: b.ls            #0x9385e4
    // 0x938464: r1 = <double>
    //     0x938464: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x938468: r0 = AnimationController()
    //     0x938468: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x93846c: mov             x1, x0
    // 0x938470: ldur            x2, [fp, #-8]
    // 0x938474: stur            x0, [fp, #-0x10]
    // 0x938478: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x938478: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93847c: r0 = AnimationController()
    //     0x93847c: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x938480: ldur            x0, [fp, #-0x10]
    // 0x938484: ldur            x2, [fp, #-8]
    // 0x938488: StoreField: r2->field_1b = r0
    //     0x938488: stur            w0, [x2, #0x1b]
    //     0x93848c: ldurb           w16, [x2, #-1]
    //     0x938490: ldurb           w17, [x0, #-1]
    //     0x938494: and             x16, x17, x16, lsr #2
    //     0x938498: tst             x16, HEAP, lsr #32
    //     0x93849c: b.eq            #0x9384a4
    //     0x9384a0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9384a4: r0 = InitLateStaticField(0xb24) // [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_kDragSizeFactorLimitTween
    //     0x9384a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9384a8: ldr             x0, [x0, #0x1648]
    //     0x9384ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9384b0: cmp             w0, w16
    //     0x9384b4: b.ne            #0x9384c4
    //     0x9384b8: add             x2, PP, #0x44, lsl #12  ; [pp+0x44210] Field <RefreshIndicatorState._kDragSizeFactorLimitTween@587083489>: static late final (offset: 0xb24)
    //     0x9384bc: ldr             x2, [x2, #0x210]
    //     0x9384c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9384c4: mov             x1, x0
    // 0x9384c8: ldur            x2, [fp, #-0x10]
    // 0x9384cc: r0 = animate()
    //     0x9384cc: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x9384d0: ldur            x2, [fp, #-8]
    // 0x9384d4: StoreField: r2->field_23 = r0
    //     0x9384d4: stur            w0, [x2, #0x23]
    //     0x9384d8: ldurb           w16, [x2, #-1]
    //     0x9384dc: ldurb           w17, [x0, #-1]
    //     0x9384e0: and             x16, x17, x16, lsr #2
    //     0x9384e4: tst             x16, HEAP, lsr #32
    //     0x9384e8: b.eq            #0x9384f0
    //     0x9384ec: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9384f0: LoadField: r0 = r2->field_1b
    //     0x9384f0: ldur            w0, [x2, #0x1b]
    // 0x9384f4: DecompressPointer r0
    //     0x9384f4: add             x0, x0, HEAP, lsl #32
    // 0x9384f8: stur            x0, [fp, #-0x10]
    // 0x9384fc: r0 = InitLateStaticField(0xb20) // [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_threeQuarterTween
    //     0x9384fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x938500: ldr             x0, [x0, #0x1640]
    //     0x938504: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x938508: cmp             w0, w16
    //     0x93850c: b.ne            #0x93851c
    //     0x938510: add             x2, PP, #0x44, lsl #12  ; [pp+0x44218] Field <RefreshIndicatorState._threeQuarterTween@587083489>: static late final (offset: 0xb20)
    //     0x938514: ldr             x2, [x2, #0x218]
    //     0x938518: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x93851c: mov             x1, x0
    // 0x938520: ldur            x2, [fp, #-0x10]
    // 0x938524: r0 = animate()
    //     0x938524: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x938528: ldur            x2, [fp, #-8]
    // 0x93852c: StoreField: r2->field_2b = r0
    //     0x93852c: stur            w0, [x2, #0x2b]
    //     0x938530: ldurb           w16, [x2, #-1]
    //     0x938534: ldurb           w17, [x0, #-1]
    //     0x938538: and             x16, x17, x16, lsr #2
    //     0x93853c: tst             x16, HEAP, lsr #32
    //     0x938540: b.eq            #0x938548
    //     0x938544: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x938548: r1 = <double>
    //     0x938548: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x93854c: r0 = AnimationController()
    //     0x93854c: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x938550: mov             x1, x0
    // 0x938554: ldur            x2, [fp, #-8]
    // 0x938558: stur            x0, [fp, #-0x10]
    // 0x93855c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x93855c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x938560: r0 = AnimationController()
    //     0x938560: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x938564: ldur            x0, [fp, #-0x10]
    // 0x938568: ldur            x1, [fp, #-8]
    // 0x93856c: StoreField: r1->field_1f = r0
    //     0x93856c: stur            w0, [x1, #0x1f]
    //     0x938570: ldurb           w16, [x1, #-1]
    //     0x938574: ldurb           w17, [x0, #-1]
    //     0x938578: and             x16, x17, x16, lsr #2
    //     0x93857c: tst             x16, HEAP, lsr #32
    //     0x938580: b.eq            #0x938588
    //     0x938584: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x938588: r0 = InitLateStaticField(0xb28) // [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_oneToZeroTween
    //     0x938588: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93858c: ldr             x0, [x0, #0x1650]
    //     0x938590: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x938594: cmp             w0, w16
    //     0x938598: b.ne            #0x9385a8
    //     0x93859c: add             x2, PP, #0x44, lsl #12  ; [pp+0x44220] Field <RefreshIndicatorState._oneToZeroTween@587083489>: static late final (offset: 0xb28)
    //     0x9385a0: ldr             x2, [x2, #0x220]
    //     0x9385a4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9385a8: mov             x1, x0
    // 0x9385ac: ldur            x2, [fp, #-0x10]
    // 0x9385b0: r0 = animate()
    //     0x9385b0: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x9385b4: ldur            x1, [fp, #-8]
    // 0x9385b8: StoreField: r1->field_27 = r0
    //     0x9385b8: stur            w0, [x1, #0x27]
    //     0x9385bc: ldurb           w16, [x1, #-1]
    //     0x9385c0: ldurb           w17, [x0, #-1]
    //     0x9385c4: and             x16, x17, x16, lsr #2
    //     0x9385c8: tst             x16, HEAP, lsr #32
    //     0x9385cc: b.eq            #0x9385d4
    //     0x9385d0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9385d4: r0 = Null
    //     0x9385d4: mov             x0, NULL
    // 0x9385d8: LeaveFrame
    //     0x9385d8: mov             SP, fp
    //     0x9385dc: ldp             fp, lr, [SP], #0x10
    // 0x9385e0: ret
    //     0x9385e0: ret             
    // 0x9385e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9385e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9385e8: b               #0x938464
  }
  static Animatable<double> _threeQuarterTween() {
    // ** addr: 0x938618, size: 0x30
    // 0x938618: EnterFrame
    //     0x938618: stp             fp, lr, [SP, #-0x10]!
    //     0x93861c: mov             fp, SP
    // 0x938620: r1 = <double>
    //     0x938620: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x938624: r0 = Tween()
    //     0x938624: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x938628: r1 = 0.000000
    //     0x938628: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x93862c: StoreField: r0->field_b = r1
    //     0x93862c: stur            w1, [x0, #0xb]
    // 0x938630: r1 = 0.750000
    //     0x938630: add             x1, PP, #0x44, lsl #12  ; [pp+0x44228] 0.75
    //     0x938634: ldr             x1, [x1, #0x228]
    // 0x938638: StoreField: r0->field_f = r1
    //     0x938638: stur            w1, [x0, #0xf]
    // 0x93863c: LeaveFrame
    //     0x93863c: mov             SP, fp
    //     0x938640: ldp             fp, lr, [SP], #0x10
    // 0x938644: ret
    //     0x938644: ret             
  }
  static Animatable<double> _kDragSizeFactorLimitTween() {
    // ** addr: 0x938648, size: 0x30
    // 0x938648: EnterFrame
    //     0x938648: stp             fp, lr, [SP, #-0x10]!
    //     0x93864c: mov             fp, SP
    // 0x938650: r1 = <double>
    //     0x938650: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x938654: r0 = Tween()
    //     0x938654: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x938658: r1 = 0.000000
    //     0x938658: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x93865c: StoreField: r0->field_b = r1
    //     0x93865c: stur            w1, [x0, #0xb]
    // 0x938660: r1 = 1.500000
    //     0x938660: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c58] 1.5
    //     0x938664: ldr             x1, [x1, #0xc58]
    // 0x938668: StoreField: r0->field_f = r1
    //     0x938668: stur            w1, [x0, #0xf]
    // 0x93866c: LeaveFrame
    //     0x93866c: mov             SP, fp
    //     0x938670: ldp             fp, lr, [SP], #0x10
    // 0x938674: ret
    //     0x938674: ret             
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9885bc, size: 0xbc
    // 0x9885bc: EnterFrame
    //     0x9885bc: stp             fp, lr, [SP, #-0x10]!
    //     0x9885c0: mov             fp, SP
    // 0x9885c4: AllocStack(0x10)
    //     0x9885c4: sub             SP, SP, #0x10
    // 0x9885c8: SetupParameters(RefreshIndicatorState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9885c8: mov             x0, x2
    //     0x9885cc: mov             x4, x1
    //     0x9885d0: mov             x3, x2
    //     0x9885d4: stur            x1, [fp, #-8]
    //     0x9885d8: stur            x2, [fp, #-0x10]
    // 0x9885dc: r2 = Null
    //     0x9885dc: mov             x2, NULL
    // 0x9885e0: r1 = Null
    //     0x9885e0: mov             x1, NULL
    // 0x9885e4: r4 = 60
    //     0x9885e4: movz            x4, #0x3c
    // 0x9885e8: branchIfSmi(r0, 0x9885f4)
    //     0x9885e8: tbz             w0, #0, #0x9885f4
    // 0x9885ec: r4 = LoadClassIdInstr(r0)
    //     0x9885ec: ldur            x4, [x0, #-1]
    //     0x9885f0: ubfx            x4, x4, #0xc, #0x14
    // 0x9885f4: r17 = 4821
    //     0x9885f4: movz            x17, #0x12d5
    // 0x9885f8: cmp             x4, x17
    // 0x9885fc: b.eq            #0x988614
    // 0x988600: r8 = RefreshIndicator
    //     0x988600: add             x8, PP, #0x44, lsl #12  ; [pp+0x441e0] Type: RefreshIndicator
    //     0x988604: ldr             x8, [x8, #0x1e0]
    // 0x988608: r3 = Null
    //     0x988608: add             x3, PP, #0x44, lsl #12  ; [pp+0x441e8] Null
    //     0x98860c: ldr             x3, [x3, #0x1e8]
    // 0x988610: r0 = RefreshIndicator()
    //     0x988610: bl              #0x6f545c  ; IsType_RefreshIndicator_Stub
    // 0x988614: ldur            x3, [fp, #-8]
    // 0x988618: LoadField: r2 = r3->field_7
    //     0x988618: ldur            w2, [x3, #7]
    // 0x98861c: DecompressPointer r2
    //     0x98861c: add             x2, x2, HEAP, lsl #32
    // 0x988620: ldur            x0, [fp, #-0x10]
    // 0x988624: r1 = Null
    //     0x988624: mov             x1, NULL
    // 0x988628: cmp             w2, NULL
    // 0x98862c: b.eq            #0x988650
    // 0x988630: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x988630: ldur            w4, [x2, #0x17]
    // 0x988634: DecompressPointer r4
    //     0x988634: add             x4, x4, HEAP, lsl #32
    // 0x988638: r8 = X0 bound StatefulWidget
    //     0x988638: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x98863c: ldr             x8, [x8, #0x7f8]
    // 0x988640: LoadField: r9 = r4->field_7
    //     0x988640: ldur            x9, [x4, #7]
    // 0x988644: r3 = Null
    //     0x988644: add             x3, PP, #0x44, lsl #12  ; [pp+0x441f8] Null
    //     0x988648: ldr             x3, [x3, #0x1f8]
    // 0x98864c: blr             x9
    // 0x988650: ldur            x1, [fp, #-8]
    // 0x988654: LoadField: r2 = r1->field_b
    //     0x988654: ldur            w2, [x1, #0xb]
    // 0x988658: DecompressPointer r2
    //     0x988658: add             x2, x2, HEAP, lsl #32
    // 0x98865c: cmp             w2, NULL
    // 0x988660: b.eq            #0x988674
    // 0x988664: r0 = Null
    //     0x988664: mov             x0, NULL
    // 0x988668: LeaveFrame
    //     0x988668: mov             SP, fp
    //     0x98866c: ldp             fp, lr, [SP], #0x10
    // 0x988670: ret
    //     0x988670: ret             
    // 0x988674: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x988674: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9a4074, size: 0x30
    // 0x9a4074: EnterFrame
    //     0x9a4074: stp             fp, lr, [SP, #-0x10]!
    //     0x9a4078: mov             fp, SP
    // 0x9a407c: CheckStackOverflow
    //     0x9a407c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a4080: cmp             SP, x16
    //     0x9a4084: b.ls            #0x9a409c
    // 0x9a4088: r0 = _setupColorTween()
    //     0x9a4088: bl              #0x9a40a4  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_setupColorTween
    // 0x9a408c: r0 = Null
    //     0x9a408c: mov             x0, NULL
    // 0x9a4090: LeaveFrame
    //     0x9a4090: mov             SP, fp
    //     0x9a4094: ldp             fp, lr, [SP], #0x10
    // 0x9a4098: ret
    //     0x9a4098: ret             
    // 0x9a409c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a409c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a40a0: b               #0x9a4088
  }
  _ _setupColorTween(/* No info */) {
    // ** addr: 0x9a40a4, size: 0x200
    // 0x9a40a4: EnterFrame
    //     0x9a40a4: stp             fp, lr, [SP, #-0x10]!
    //     0x9a40a8: mov             fp, SP
    // 0x9a40ac: AllocStack(0x28)
    //     0x9a40ac: sub             SP, SP, #0x28
    // 0x9a40b0: SetupParameters(RefreshIndicatorState this /* r1 => r0, fp-0x8 */)
    //     0x9a40b0: mov             x0, x1
    //     0x9a40b4: stur            x1, [fp, #-8]
    // 0x9a40b8: CheckStackOverflow
    //     0x9a40b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a40bc: cmp             SP, x16
    //     0x9a40c0: b.ls            #0x9a4288
    // 0x9a40c4: LoadField: r1 = r0->field_b
    //     0x9a40c4: ldur            w1, [x0, #0xb]
    // 0x9a40c8: DecompressPointer r1
    //     0x9a40c8: add             x1, x1, HEAP, lsl #32
    // 0x9a40cc: cmp             w1, NULL
    // 0x9a40d0: b.eq            #0x9a4290
    // 0x9a40d4: LoadField: r1 = r0->field_f
    //     0x9a40d4: ldur            w1, [x0, #0xf]
    // 0x9a40d8: DecompressPointer r1
    //     0x9a40d8: add             x1, x1, HEAP, lsl #32
    // 0x9a40dc: cmp             w1, NULL
    // 0x9a40e0: b.eq            #0x9a4294
    // 0x9a40e4: r0 = of()
    //     0x9a40e4: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9a40e8: LoadField: r1 = r0->field_3f
    //     0x9a40e8: ldur            w1, [x0, #0x3f]
    // 0x9a40ec: DecompressPointer r1
    //     0x9a40ec: add             x1, x1, HEAP, lsl #32
    // 0x9a40f0: LoadField: r2 = r1->field_b
    //     0x9a40f0: ldur            w2, [x1, #0xb]
    // 0x9a40f4: DecompressPointer r2
    //     0x9a40f4: add             x2, x2, HEAP, lsl #32
    // 0x9a40f8: mov             x0, x2
    // 0x9a40fc: ldur            x3, [fp, #-8]
    // 0x9a4100: stur            x2, [fp, #-0x10]
    // 0x9a4104: StoreField: r3->field_3f = r0
    //     0x9a4104: stur            w0, [x3, #0x3f]
    //     0x9a4108: ldurb           w16, [x3, #-1]
    //     0x9a410c: ldurb           w17, [x0, #-1]
    //     0x9a4110: and             x16, x17, x16, lsr #2
    //     0x9a4114: tst             x16, HEAP, lsr #32
    //     0x9a4118: b.eq            #0x9a4120
    //     0x9a411c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x9a4120: r0 = LoadClassIdInstr(r2)
    //     0x9a4120: ldur            x0, [x2, #-1]
    //     0x9a4124: ubfx            x0, x0, #0xc, #0x14
    // 0x9a4128: mov             x1, x2
    // 0x9a412c: r0 = GDT[cid_x0 + -0xcc1]()
    //     0x9a412c: sub             lr, x0, #0xcc1
    //     0x9a4130: ldr             lr, [x21, lr, lsl #3]
    //     0x9a4134: blr             lr
    // 0x9a4138: cbnz            x0, #0x9a417c
    // 0x9a413c: ldur            x2, [fp, #-8]
    // 0x9a4140: ldur            x0, [fp, #-0x10]
    // 0x9a4144: r1 = <Color>
    //     0x9a4144: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0x9a4148: ldr             x1, [x1, #0x158]
    // 0x9a414c: r0 = AlwaysStoppedAnimation()
    //     0x9a414c: bl              #0x9a42a4  ; AllocateAlwaysStoppedAnimationStub -> AlwaysStoppedAnimation<X0> (size=0x10)
    // 0x9a4150: ldur            x3, [fp, #-0x10]
    // 0x9a4154: StoreField: r0->field_b = r3
    //     0x9a4154: stur            w3, [x0, #0xb]
    // 0x9a4158: ldur            x4, [fp, #-8]
    // 0x9a415c: StoreField: r4->field_2f = r0
    //     0x9a415c: stur            w0, [x4, #0x2f]
    //     0x9a4160: ldurb           w16, [x4, #-1]
    //     0x9a4164: ldurb           w17, [x0, #-1]
    //     0x9a4168: and             x16, x17, x16, lsr #2
    //     0x9a416c: tst             x16, HEAP, lsr #32
    //     0x9a4170: b.eq            #0x9a4178
    //     0x9a4174: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x9a4178: b               #0x9a4278
    // 0x9a417c: ldur            x4, [fp, #-8]
    // 0x9a4180: ldur            x3, [fp, #-0x10]
    // 0x9a4184: LoadField: r5 = r4->field_1b
    //     0x9a4184: ldur            w5, [x4, #0x1b]
    // 0x9a4188: DecompressPointer r5
    //     0x9a4188: add             x5, x5, HEAP, lsl #32
    // 0x9a418c: r16 = Sentinel
    //     0x9a418c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a4190: cmp             w5, w16
    // 0x9a4194: b.eq            #0x9a4298
    // 0x9a4198: stur            x5, [fp, #-0x18]
    // 0x9a419c: r0 = LoadClassIdInstr(r3)
    //     0x9a419c: ldur            x0, [x3, #-1]
    //     0x9a41a0: ubfx            x0, x0, #0xc, #0x14
    // 0x9a41a4: mov             x1, x3
    // 0x9a41a8: r2 = 0
    //     0x9a41a8: movz            x2, #0
    // 0x9a41ac: r0 = GDT[cid_x0 + -0xfb6]()
    //     0x9a41ac: sub             lr, x0, #0xfb6
    //     0x9a41b0: ldr             lr, [x21, lr, lsl #3]
    //     0x9a41b4: blr             lr
    // 0x9a41b8: mov             x3, x0
    // 0x9a41bc: ldur            x2, [fp, #-0x10]
    // 0x9a41c0: stur            x3, [fp, #-0x20]
    // 0x9a41c4: r0 = LoadClassIdInstr(r2)
    //     0x9a41c4: ldur            x0, [x2, #-1]
    //     0x9a41c8: ubfx            x0, x0, #0xc, #0x14
    // 0x9a41cc: mov             x1, x2
    // 0x9a41d0: r0 = GDT[cid_x0 + -0xcc1]()
    //     0x9a41d0: sub             lr, x0, #0xcc1
    //     0x9a41d4: ldr             lr, [x21, lr, lsl #3]
    //     0x9a41d8: blr             lr
    // 0x9a41dc: ldur            x1, [fp, #-0x10]
    // 0x9a41e0: r2 = LoadClassIdInstr(r1)
    //     0x9a41e0: ldur            x2, [x1, #-1]
    //     0x9a41e4: ubfx            x2, x2, #0xc, #0x14
    // 0x9a41e8: mov             x16, x0
    // 0x9a41ec: mov             x0, x2
    // 0x9a41f0: mov             x2, x16
    // 0x9a41f4: r0 = GDT[cid_x0 + -0xfb6]()
    //     0x9a41f4: sub             lr, x0, #0xfb6
    //     0x9a41f8: ldr             lr, [x21, lr, lsl #3]
    //     0x9a41fc: blr             lr
    // 0x9a4200: r1 = <Color?>
    //     0x9a4200: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0x9a4204: ldr             x1, [x1, #0x98]
    // 0x9a4208: stur            x0, [fp, #-0x10]
    // 0x9a420c: r0 = ColorTween()
    //     0x9a420c: bl              #0x7e3594  ; AllocateColorTweenStub -> ColorTween (size=0x14)
    // 0x9a4210: mov             x2, x0
    // 0x9a4214: ldur            x0, [fp, #-0x20]
    // 0x9a4218: stur            x2, [fp, #-0x28]
    // 0x9a421c: StoreField: r2->field_b = r0
    //     0x9a421c: stur            w0, [x2, #0xb]
    // 0x9a4220: ldur            x0, [fp, #-0x10]
    // 0x9a4224: StoreField: r2->field_f = r0
    //     0x9a4224: stur            w0, [x2, #0xf]
    // 0x9a4228: r1 = <double>
    //     0x9a4228: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9a422c: r0 = CurveTween()
    //     0x9a422c: bl              #0x796a9c  ; AllocateCurveTweenStub -> CurveTween (size=0x10)
    // 0x9a4230: mov             x1, x0
    // 0x9a4234: r0 = Instance_Interval
    //     0x9a4234: add             x0, PP, #0x44, lsl #12  ; [pp+0x44208] Obj!Interval@e15041
    //     0x9a4238: ldr             x0, [x0, #0x208]
    // 0x9a423c: StoreField: r1->field_b = r0
    //     0x9a423c: stur            w0, [x1, #0xb]
    // 0x9a4240: mov             x2, x1
    // 0x9a4244: ldur            x1, [fp, #-0x28]
    // 0x9a4248: r0 = chain()
    //     0x9a4248: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x9a424c: mov             x1, x0
    // 0x9a4250: ldur            x2, [fp, #-0x18]
    // 0x9a4254: r0 = animate()
    //     0x9a4254: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x9a4258: ldur            x1, [fp, #-8]
    // 0x9a425c: StoreField: r1->field_2f = r0
    //     0x9a425c: stur            w0, [x1, #0x2f]
    //     0x9a4260: ldurb           w16, [x1, #-1]
    //     0x9a4264: ldurb           w17, [x0, #-1]
    //     0x9a4268: and             x16, x17, x16, lsr #2
    //     0x9a426c: tst             x16, HEAP, lsr #32
    //     0x9a4270: b.eq            #0x9a4278
    //     0x9a4274: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9a4278: r0 = Null
    //     0x9a4278: mov             x0, NULL
    // 0x9a427c: LeaveFrame
    //     0x9a427c: mov             SP, fp
    //     0x9a4280: ldp             fp, lr, [SP], #0x10
    // 0x9a4284: ret
    //     0x9a4284: ret             
    // 0x9a4288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a4288: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a428c: b               #0x9a40c4
    // 0x9a4290: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a4290: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a4294: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a4294: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a4298: r9 = _positionController
    //     0x9a4298: add             x9, PP, #0x44, lsl #12  ; [pp+0x44158] Field <RefreshIndicatorState._positionController@587083489>: late (offset: 0x1c)
    //     0x9a429c: ldr             x9, [x9, #0x158]
    // 0x9a42a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a42a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0x9ff9b4, size: 0x3b8
    // 0x9ff9b4: EnterFrame
    //     0x9ff9b4: stp             fp, lr, [SP, #-0x10]!
    //     0x9ff9b8: mov             fp, SP
    // 0x9ff9bc: AllocStack(0x40)
    //     0x9ff9bc: sub             SP, SP, #0x40
    // 0x9ff9c0: SetupParameters(RefreshIndicatorState this /* r1 => r0, fp-0x8 */)
    //     0x9ff9c0: mov             x0, x1
    //     0x9ff9c4: stur            x1, [fp, #-8]
    // 0x9ff9c8: CheckStackOverflow
    //     0x9ff9c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ff9cc: cmp             SP, x16
    //     0x9ff9d0: b.ls            #0x9ffd38
    // 0x9ff9d4: r1 = 2
    //     0x9ff9d4: movz            x1, #0x2
    // 0x9ff9d8: r0 = AllocateContext()
    //     0x9ff9d8: bl              #0xec126c  ; AllocateContextStub
    // 0x9ff9dc: mov             x3, x0
    // 0x9ff9e0: ldur            x0, [fp, #-8]
    // 0x9ff9e4: stur            x3, [fp, #-0x18]
    // 0x9ff9e8: StoreField: r3->field_f = r0
    //     0x9ff9e8: stur            w0, [x3, #0xf]
    // 0x9ff9ec: LoadField: r1 = r0->field_b
    //     0x9ff9ec: ldur            w1, [x0, #0xb]
    // 0x9ff9f0: DecompressPointer r1
    //     0x9ff9f0: add             x1, x1, HEAP, lsl #32
    // 0x9ff9f4: cmp             w1, NULL
    // 0x9ff9f8: b.eq            #0x9ffd40
    // 0x9ff9fc: LoadField: r4 = r1->field_b
    //     0x9ff9fc: ldur            w4, [x1, #0xb]
    // 0x9ffa00: DecompressPointer r4
    //     0x9ffa00: add             x4, x4, HEAP, lsl #32
    // 0x9ffa04: mov             x2, x0
    // 0x9ffa08: stur            x4, [fp, #-0x10]
    // 0x9ffa0c: r1 = Function '_handleIndicatorNotification@587083489':.
    //     0x9ffa0c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44110] AnonymousClosure: (0xa00fb8), in [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_handleIndicatorNotification (0xa00ff4)
    //     0x9ffa10: ldr             x1, [x1, #0x110]
    // 0x9ffa14: r0 = AllocateClosure()
    //     0x9ffa14: bl              #0xec1630  ; AllocateClosureStub
    // 0x9ffa18: r1 = <OverscrollIndicatorNotification>
    //     0x9ffa18: add             x1, PP, #0x44, lsl #12  ; [pp+0x44118] TypeArguments: <OverscrollIndicatorNotification>
    //     0x9ffa1c: ldr             x1, [x1, #0x118]
    // 0x9ffa20: stur            x0, [fp, #-0x20]
    // 0x9ffa24: r0 = NotificationListener()
    //     0x9ffa24: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0x9ffa28: mov             x3, x0
    // 0x9ffa2c: ldur            x0, [fp, #-0x20]
    // 0x9ffa30: stur            x3, [fp, #-0x28]
    // 0x9ffa34: StoreField: r3->field_13 = r0
    //     0x9ffa34: stur            w0, [x3, #0x13]
    // 0x9ffa38: ldur            x0, [fp, #-0x10]
    // 0x9ffa3c: StoreField: r3->field_b = r0
    //     0x9ffa3c: stur            w0, [x3, #0xb]
    // 0x9ffa40: ldur            x2, [fp, #-8]
    // 0x9ffa44: r1 = Function '_handleScrollNotification@587083489':.
    //     0x9ffa44: add             x1, PP, #0x44, lsl #12  ; [pp+0x44120] AnonymousClosure: (0x9fffc0), in [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_handleScrollNotification (0x9ffffc)
    //     0x9ffa48: ldr             x1, [x1, #0x120]
    // 0x9ffa4c: r0 = AllocateClosure()
    //     0x9ffa4c: bl              #0xec1630  ; AllocateClosureStub
    // 0x9ffa50: r1 = <ScrollNotification>
    //     0x9ffa50: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0x9ffa54: ldr             x1, [x1, #0x110]
    // 0x9ffa58: stur            x0, [fp, #-0x10]
    // 0x9ffa5c: r0 = NotificationListener()
    //     0x9ffa5c: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0x9ffa60: mov             x3, x0
    // 0x9ffa64: ldur            x0, [fp, #-0x10]
    // 0x9ffa68: stur            x3, [fp, #-0x20]
    // 0x9ffa6c: StoreField: r3->field_13 = r0
    //     0x9ffa6c: stur            w0, [x3, #0x13]
    // 0x9ffa70: ldur            x0, [fp, #-0x28]
    // 0x9ffa74: StoreField: r3->field_b = r0
    //     0x9ffa74: stur            w0, [x3, #0xb]
    // 0x9ffa78: ldur            x0, [fp, #-8]
    // 0x9ffa7c: LoadField: r4 = r0->field_33
    //     0x9ffa7c: ldur            w4, [x0, #0x33]
    // 0x9ffa80: DecompressPointer r4
    //     0x9ffa80: add             x4, x4, HEAP, lsl #32
    // 0x9ffa84: stur            x4, [fp, #-0x10]
    // 0x9ffa88: r16 = Instance_RefreshIndicatorStatus
    //     0x9ffa88: add             x16, PP, #0x44, lsl #12  ; [pp+0x44128] Obj!RefreshIndicatorStatus@e363c1
    //     0x9ffa8c: ldr             x16, [x16, #0x128]
    // 0x9ffa90: cmp             w4, w16
    // 0x9ffa94: b.ne            #0x9ffaa0
    // 0x9ffa98: r1 = true
    //     0x9ffa98: add             x1, NULL, #0x20  ; true
    // 0x9ffa9c: b               #0x9ffab8
    // 0x9ffaa0: r16 = Instance_RefreshIndicatorStatus
    //     0x9ffaa0: add             x16, PP, #0x44, lsl #12  ; [pp+0x44130] Obj!RefreshIndicatorStatus@e363a1
    //     0x9ffaa4: ldr             x16, [x16, #0x130]
    // 0x9ffaa8: cmp             w4, w16
    // 0x9ffaac: r16 = true
    //     0x9ffaac: add             x16, NULL, #0x20  ; true
    // 0x9ffab0: r17 = false
    //     0x9ffab0: add             x17, NULL, #0x30  ; false
    // 0x9ffab4: csel            x1, x16, x17, eq
    // 0x9ffab8: ldur            x5, [fp, #-0x18]
    // 0x9ffabc: r6 = 2
    //     0x9ffabc: movz            x6, #0x2
    // 0x9ffac0: StoreField: r5->field_13 = r1
    //     0x9ffac0: stur            w1, [x5, #0x13]
    // 0x9ffac4: mov             x2, x6
    // 0x9ffac8: r1 = Null
    //     0x9ffac8: mov             x1, NULL
    // 0x9ffacc: r0 = AllocateArray()
    //     0x9ffacc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x9ffad0: mov             x2, x0
    // 0x9ffad4: ldur            x0, [fp, #-0x20]
    // 0x9ffad8: stur            x2, [fp, #-0x28]
    // 0x9ffadc: StoreField: r2->field_f = r0
    //     0x9ffadc: stur            w0, [x2, #0xf]
    // 0x9ffae0: r1 = <Widget>
    //     0x9ffae0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0x9ffae4: r0 = AllocateGrowableArray()
    //     0x9ffae4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x9ffae8: mov             x1, x0
    // 0x9ffaec: ldur            x0, [fp, #-0x28]
    // 0x9ffaf0: stur            x1, [fp, #-0x30]
    // 0x9ffaf4: StoreField: r1->field_f = r0
    //     0x9ffaf4: stur            w0, [x1, #0xf]
    // 0x9ffaf8: r0 = 2
    //     0x9ffaf8: movz            x0, #0x2
    // 0x9ffafc: StoreField: r1->field_b = r0
    //     0x9ffafc: stur            w0, [x1, #0xb]
    // 0x9ffb00: ldur            x0, [fp, #-0x10]
    // 0x9ffb04: cmp             w0, NULL
    // 0x9ffb08: b.eq            #0x9ffce4
    // 0x9ffb0c: ldur            x0, [fp, #-8]
    // 0x9ffb10: LoadField: r2 = r0->field_37
    //     0x9ffb10: ldur            w2, [x0, #0x37]
    // 0x9ffb14: DecompressPointer r2
    //     0x9ffb14: add             x2, x2, HEAP, lsl #32
    // 0x9ffb18: cmp             w2, NULL
    // 0x9ffb1c: b.eq            #0x9ffd44
    // 0x9ffb20: tbnz            w2, #4, #0x9ffb2c
    // 0x9ffb24: r3 = 0.000000
    //     0x9ffb24: ldr             x3, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x9ffb28: b               #0x9ffb30
    // 0x9ffb2c: r3 = Null
    //     0x9ffb2c: mov             x3, NULL
    // 0x9ffb30: stur            x3, [fp, #-0x20]
    // 0x9ffb34: tbnz            w2, #4, #0x9ffd2c
    // 0x9ffb38: LoadField: r2 = r0->field_23
    //     0x9ffb38: ldur            w2, [x0, #0x23]
    // 0x9ffb3c: DecompressPointer r2
    //     0x9ffb3c: add             x2, x2, HEAP, lsl #32
    // 0x9ffb40: r16 = Sentinel
    //     0x9ffb40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9ffb44: cmp             w2, w16
    // 0x9ffb48: b.eq            #0x9ffd48
    // 0x9ffb4c: stur            x2, [fp, #-0x10]
    // 0x9ffb50: r0 = EdgeInsets()
    //     0x9ffb50: bl              #0x65dd90  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x9ffb54: stur            x0, [fp, #-0x40]
    // 0x9ffb58: StoreField: r0->field_7 = rZR
    //     0x9ffb58: stur            xzr, [x0, #7]
    // 0x9ffb5c: d0 = 40.000000
    //     0x9ffb5c: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0x9ffb60: StoreField: r0->field_f = d0
    //     0x9ffb60: stur            d0, [x0, #0xf]
    // 0x9ffb64: ArrayStore: r0[0] = rZR  ; List_8
    //     0x9ffb64: stur            xzr, [x0, #0x17]
    // 0x9ffb68: StoreField: r0->field_1f = rZR
    //     0x9ffb68: stur            xzr, [x0, #0x1f]
    // 0x9ffb6c: ldur            x1, [fp, #-8]
    // 0x9ffb70: LoadField: r3 = r1->field_27
    //     0x9ffb70: ldur            w3, [x1, #0x27]
    // 0x9ffb74: DecompressPointer r3
    //     0x9ffb74: add             x3, x3, HEAP, lsl #32
    // 0x9ffb78: r16 = Sentinel
    //     0x9ffb78: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9ffb7c: cmp             w3, w16
    // 0x9ffb80: b.eq            #0x9ffd54
    // 0x9ffb84: stur            x3, [fp, #-0x38]
    // 0x9ffb88: LoadField: r4 = r1->field_1b
    //     0x9ffb88: ldur            w4, [x1, #0x1b]
    // 0x9ffb8c: DecompressPointer r4
    //     0x9ffb8c: add             x4, x4, HEAP, lsl #32
    // 0x9ffb90: r16 = Sentinel
    //     0x9ffb90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9ffb94: cmp             w4, w16
    // 0x9ffb98: b.eq            #0x9ffd60
    // 0x9ffb9c: ldur            x2, [fp, #-0x18]
    // 0x9ffba0: stur            x4, [fp, #-0x28]
    // 0x9ffba4: r1 = Function '<anonymous closure>':.
    //     0x9ffba4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44138] AnonymousClosure: (0x9ffd78), in [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::build (0x9ff9b4)
    //     0x9ffba8: ldr             x1, [x1, #0x138]
    // 0x9ffbac: r0 = AllocateClosure()
    //     0x9ffbac: bl              #0xec1630  ; AllocateClosureStub
    // 0x9ffbb0: stur            x0, [fp, #-8]
    // 0x9ffbb4: r0 = AnimatedBuilder()
    //     0x9ffbb4: bl              #0x7e5888  ; AllocateAnimatedBuilderStub -> AnimatedBuilder (size=0x18)
    // 0x9ffbb8: mov             x1, x0
    // 0x9ffbbc: ldur            x0, [fp, #-8]
    // 0x9ffbc0: stur            x1, [fp, #-0x18]
    // 0x9ffbc4: StoreField: r1->field_f = r0
    //     0x9ffbc4: stur            w0, [x1, #0xf]
    // 0x9ffbc8: ldur            x0, [fp, #-0x28]
    // 0x9ffbcc: StoreField: r1->field_b = r0
    //     0x9ffbcc: stur            w0, [x1, #0xb]
    // 0x9ffbd0: r0 = ScaleTransition()
    //     0x9ffbd0: bl              #0x9dfc0c  ; AllocateScaleTransitionStub -> ScaleTransition (size=0x20)
    // 0x9ffbd4: mov             x1, x0
    // 0x9ffbd8: r0 = Closure: (double) => Matrix4 from Function '_handleScaleMatrix@367170175': static.
    //     0x9ffbd8: add             x0, PP, #0x44, lsl #12  ; [pp+0x44140] Closure: (double) => Matrix4 from Function '_handleScaleMatrix@367170175': static. (0x7e54fb3dfc18)
    //     0x9ffbdc: ldr             x0, [x0, #0x140]
    // 0x9ffbe0: stur            x1, [fp, #-8]
    // 0x9ffbe4: StoreField: r1->field_f = r0
    //     0x9ffbe4: stur            w0, [x1, #0xf]
    // 0x9ffbe8: r0 = Instance_Alignment
    //     0x9ffbe8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x9ffbec: ldr             x0, [x0, #0x898]
    // 0x9ffbf0: StoreField: r1->field_13 = r0
    //     0x9ffbf0: stur            w0, [x1, #0x13]
    // 0x9ffbf4: ldur            x0, [fp, #-0x18]
    // 0x9ffbf8: StoreField: r1->field_1b = r0
    //     0x9ffbf8: stur            w0, [x1, #0x1b]
    // 0x9ffbfc: ldur            x0, [fp, #-0x38]
    // 0x9ffc00: StoreField: r1->field_b = r0
    //     0x9ffc00: stur            w0, [x1, #0xb]
    // 0x9ffc04: r0 = Align()
    //     0x9ffc04: bl              #0x9d4ff8  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x9ffc08: mov             x1, x0
    // 0x9ffc0c: r0 = Instance_Alignment
    //     0x9ffc0c: add             x0, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!Alignment@e13eb1
    //     0x9ffc10: ldr             x0, [x0, #0xe0]
    // 0x9ffc14: stur            x1, [fp, #-0x18]
    // 0x9ffc18: StoreField: r1->field_f = r0
    //     0x9ffc18: stur            w0, [x1, #0xf]
    // 0x9ffc1c: ldur            x0, [fp, #-8]
    // 0x9ffc20: StoreField: r1->field_b = r0
    //     0x9ffc20: stur            w0, [x1, #0xb]
    // 0x9ffc24: r0 = Padding()
    //     0x9ffc24: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ffc28: mov             x1, x0
    // 0x9ffc2c: ldur            x0, [fp, #-0x40]
    // 0x9ffc30: stur            x1, [fp, #-8]
    // 0x9ffc34: StoreField: r1->field_f = r0
    //     0x9ffc34: stur            w0, [x1, #0xf]
    // 0x9ffc38: ldur            x0, [fp, #-0x18]
    // 0x9ffc3c: StoreField: r1->field_b = r0
    //     0x9ffc3c: stur            w0, [x1, #0xb]
    // 0x9ffc40: r0 = SizeTransition()
    //     0x9ffc40: bl              #0x9ffd6c  ; AllocateSizeTransitionStub -> SizeTransition (size=0x24)
    // 0x9ffc44: mov             x2, x0
    // 0x9ffc48: r0 = Instance_Axis
    //     0x9ffc48: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x9ffc4c: stur            x2, [fp, #-0x18]
    // 0x9ffc50: StoreField: r2->field_f = r0
    //     0x9ffc50: stur            w0, [x2, #0xf]
    // 0x9ffc54: d0 = 1.000000
    //     0x9ffc54: fmov            d0, #1.00000000
    // 0x9ffc58: StoreField: r2->field_13 = d0
    //     0x9ffc58: stur            d0, [x2, #0x13]
    // 0x9ffc5c: ldur            x0, [fp, #-8]
    // 0x9ffc60: StoreField: r2->field_1f = r0
    //     0x9ffc60: stur            w0, [x2, #0x1f]
    // 0x9ffc64: ldur            x0, [fp, #-0x10]
    // 0x9ffc68: StoreField: r2->field_b = r0
    //     0x9ffc68: stur            w0, [x2, #0xb]
    // 0x9ffc6c: r1 = <StackParentData>
    //     0x9ffc6c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25780] TypeArguments: <StackParentData>
    //     0x9ffc70: ldr             x1, [x1, #0x780]
    // 0x9ffc74: r0 = Positioned()
    //     0x9ffc74: bl              #0x9f19f8  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x9ffc78: mov             x2, x0
    // 0x9ffc7c: r0 = 0.000000
    //     0x9ffc7c: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x9ffc80: stur            x2, [fp, #-8]
    // 0x9ffc84: StoreField: r2->field_13 = r0
    //     0x9ffc84: stur            w0, [x2, #0x13]
    // 0x9ffc88: ldur            x1, [fp, #-0x20]
    // 0x9ffc8c: ArrayStore: r2[0] = r1  ; List_4
    //     0x9ffc8c: stur            w1, [x2, #0x17]
    // 0x9ffc90: StoreField: r2->field_1b = r0
    //     0x9ffc90: stur            w0, [x2, #0x1b]
    // 0x9ffc94: ldur            x0, [fp, #-0x18]
    // 0x9ffc98: StoreField: r2->field_b = r0
    //     0x9ffc98: stur            w0, [x2, #0xb]
    // 0x9ffc9c: ldur            x1, [fp, #-0x30]
    // 0x9ffca0: r0 = _growToNextCapacity()
    //     0x9ffca0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9ffca4: ldur            x2, [fp, #-0x30]
    // 0x9ffca8: r0 = 4
    //     0x9ffca8: movz            x0, #0x4
    // 0x9ffcac: StoreField: r2->field_b = r0
    //     0x9ffcac: stur            w0, [x2, #0xb]
    // 0x9ffcb0: LoadField: r1 = r2->field_f
    //     0x9ffcb0: ldur            w1, [x2, #0xf]
    // 0x9ffcb4: DecompressPointer r1
    //     0x9ffcb4: add             x1, x1, HEAP, lsl #32
    // 0x9ffcb8: ldur            x0, [fp, #-8]
    // 0x9ffcbc: ArrayStore: r1[1] = r0  ; List_4
    //     0x9ffcbc: add             x25, x1, #0x13
    //     0x9ffcc0: str             w0, [x25]
    //     0x9ffcc4: tbz             w0, #0, #0x9ffce0
    //     0x9ffcc8: ldurb           w16, [x1, #-1]
    //     0x9ffccc: ldurb           w17, [x0, #-1]
    //     0x9ffcd0: and             x16, x17, x16, lsr #2
    //     0x9ffcd4: tst             x16, HEAP, lsr #32
    //     0x9ffcd8: b.eq            #0x9ffce0
    //     0x9ffcdc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x9ffce0: b               #0x9ffce8
    // 0x9ffce4: mov             x2, x1
    // 0x9ffce8: r0 = Stack()
    //     0x9ffce8: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0x9ffcec: mov             x1, x0
    // 0x9ffcf0: r0 = Instance_AlignmentDirectional
    //     0x9ffcf0: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b0] Obj!AlignmentDirectional@e13d31
    //     0x9ffcf4: ldr             x0, [x0, #0x7b0]
    // 0x9ffcf8: StoreField: r1->field_f = r0
    //     0x9ffcf8: stur            w0, [x1, #0xf]
    // 0x9ffcfc: r0 = Instance_StackFit
    //     0x9ffcfc: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b8] Obj!StackFit@e35461
    //     0x9ffd00: ldr             x0, [x0, #0x7b8]
    // 0x9ffd04: ArrayStore: r1[0] = r0  ; List_4
    //     0x9ffd04: stur            w0, [x1, #0x17]
    // 0x9ffd08: r0 = Instance_Clip
    //     0x9ffd08: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0x9ffd0c: ldr             x0, [x0, #0x7c0]
    // 0x9ffd10: StoreField: r1->field_1b = r0
    //     0x9ffd10: stur            w0, [x1, #0x1b]
    // 0x9ffd14: ldur            x0, [fp, #-0x30]
    // 0x9ffd18: StoreField: r1->field_b = r0
    //     0x9ffd18: stur            w0, [x1, #0xb]
    // 0x9ffd1c: mov             x0, x1
    // 0x9ffd20: LeaveFrame
    //     0x9ffd20: mov             SP, fp
    //     0x9ffd24: ldp             fp, lr, [SP], #0x10
    // 0x9ffd28: ret
    //     0x9ffd28: ret             
    // 0x9ffd2c: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x9ffd2c: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x9ffd30: r0 = Throw()
    //     0x9ffd30: bl              #0xec04b8  ; ThrowStub
    // 0x9ffd34: brk             #0
    // 0x9ffd38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ffd38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ffd3c: b               #0x9ff9d4
    // 0x9ffd40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ffd40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ffd44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ffd44: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ffd48: r9 = _positionFactor
    //     0x9ffd48: add             x9, PP, #0x44, lsl #12  ; [pp+0x44148] Field <RefreshIndicatorState._positionFactor@587083489>: late (offset: 0x24)
    //     0x9ffd4c: ldr             x9, [x9, #0x148]
    // 0x9ffd50: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9ffd50: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9ffd54: r9 = _scaleFactor
    //     0x9ffd54: add             x9, PP, #0x44, lsl #12  ; [pp+0x44150] Field <RefreshIndicatorState._scaleFactor@587083489>: late (offset: 0x28)
    //     0x9ffd58: ldr             x9, [x9, #0x150]
    // 0x9ffd5c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9ffd5c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9ffd60: r9 = _positionController
    //     0x9ffd60: add             x9, PP, #0x44, lsl #12  ; [pp+0x44158] Field <RefreshIndicatorState._positionController@587083489>: late (offset: 0x1c)
    //     0x9ffd64: ldr             x9, [x9, #0x158]
    // 0x9ffd68: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9ffd68: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, Widget?) {
    // ** addr: 0x9ffd78, size: 0x23c
    // 0x9ffd78: EnterFrame
    //     0x9ffd78: stp             fp, lr, [SP, #-0x10]!
    //     0x9ffd7c: mov             fp, SP
    // 0x9ffd80: AllocStack(0x20)
    //     0x9ffd80: sub             SP, SP, #0x20
    // 0x9ffd84: SetupParameters()
    //     0x9ffd84: ldr             x0, [fp, #0x20]
    //     0x9ffd88: ldur            w2, [x0, #0x17]
    //     0x9ffd8c: add             x2, x2, HEAP, lsl #32
    //     0x9ffd90: stur            x2, [fp, #-8]
    // 0x9ffd94: CheckStackOverflow
    //     0x9ffd94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ffd98: cmp             SP, x16
    //     0x9ffd9c: b.ls            #0x9fff88
    // 0x9ffda0: LoadField: r0 = r2->field_f
    //     0x9ffda0: ldur            w0, [x2, #0xf]
    // 0x9ffda4: DecompressPointer r0
    //     0x9ffda4: add             x0, x0, HEAP, lsl #32
    // 0x9ffda8: LoadField: r1 = r0->field_b
    //     0x9ffda8: ldur            w1, [x0, #0xb]
    // 0x9ffdac: DecompressPointer r1
    //     0x9ffdac: add             x1, x1, HEAP, lsl #32
    // 0x9ffdb0: cmp             w1, NULL
    // 0x9ffdb4: b.eq            #0x9fff90
    // 0x9ffdb8: ldr             x1, [fp, #0x18]
    // 0x9ffdbc: r0 = of()
    //     0x9ffdbc: bl              #0x9179e4  ; [package:flutter/src/material/material_localizations.dart] MaterialLocalizations::of
    // 0x9ffdc0: ldur            x0, [fp, #-8]
    // 0x9ffdc4: LoadField: r1 = r0->field_f
    //     0x9ffdc4: ldur            w1, [x0, #0xf]
    // 0x9ffdc8: DecompressPointer r1
    //     0x9ffdc8: add             x1, x1, HEAP, lsl #32
    // 0x9ffdcc: LoadField: r2 = r1->field_b
    //     0x9ffdcc: ldur            w2, [x1, #0xb]
    // 0x9ffdd0: DecompressPointer r2
    //     0x9ffdd0: add             x2, x2, HEAP, lsl #32
    // 0x9ffdd4: cmp             w2, NULL
    // 0x9ffdd8: b.eq            #0x9fff94
    // 0x9ffddc: LoadField: r2 = r0->field_13
    //     0x9ffddc: ldur            w2, [x0, #0x13]
    // 0x9ffde0: DecompressPointer r2
    //     0x9ffde0: add             x2, x2, HEAP, lsl #32
    // 0x9ffde4: tbnz            w2, #4, #0x9ffdf0
    // 0x9ffde8: r1 = Null
    //     0x9ffde8: mov             x1, NULL
    // 0x9ffdec: b               #0x9ffe24
    // 0x9ffdf0: LoadField: r2 = r1->field_2b
    //     0x9ffdf0: ldur            w2, [x1, #0x2b]
    // 0x9ffdf4: DecompressPointer r2
    //     0x9ffdf4: add             x2, x2, HEAP, lsl #32
    // 0x9ffdf8: r16 = Sentinel
    //     0x9ffdf8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9ffdfc: cmp             w2, w16
    // 0x9ffe00: b.eq            #0x9fff98
    // 0x9ffe04: LoadField: r1 = r2->field_f
    //     0x9ffe04: ldur            w1, [x2, #0xf]
    // 0x9ffe08: DecompressPointer r1
    //     0x9ffe08: add             x1, x1, HEAP, lsl #32
    // 0x9ffe0c: LoadField: r3 = r2->field_b
    //     0x9ffe0c: ldur            w3, [x2, #0xb]
    // 0x9ffe10: DecompressPointer r3
    //     0x9ffe10: add             x3, x3, HEAP, lsl #32
    // 0x9ffe14: mov             x2, x3
    // 0x9ffe18: r0 = evaluate()
    //     0x9ffe18: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9ffe1c: mov             x1, x0
    // 0x9ffe20: ldur            x0, [fp, #-8]
    // 0x9ffe24: stur            x1, [fp, #-0x18]
    // 0x9ffe28: LoadField: r2 = r0->field_f
    //     0x9ffe28: ldur            w2, [x0, #0xf]
    // 0x9ffe2c: DecompressPointer r2
    //     0x9ffe2c: add             x2, x2, HEAP, lsl #32
    // 0x9ffe30: LoadField: r0 = r2->field_2f
    //     0x9ffe30: ldur            w0, [x2, #0x2f]
    // 0x9ffe34: DecompressPointer r0
    //     0x9ffe34: add             x0, x0, HEAP, lsl #32
    // 0x9ffe38: r16 = Sentinel
    //     0x9ffe38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9ffe3c: cmp             w0, w16
    // 0x9ffe40: b.eq            #0x9fffa4
    // 0x9ffe44: stur            x0, [fp, #-0x10]
    // 0x9ffe48: LoadField: r3 = r2->field_b
    //     0x9ffe48: ldur            w3, [x2, #0xb]
    // 0x9ffe4c: DecompressPointer r3
    //     0x9ffe4c: add             x3, x3, HEAP, lsl #32
    // 0x9ffe50: stur            x3, [fp, #-8]
    // 0x9ffe54: cmp             w3, NULL
    // 0x9ffe58: b.eq            #0x9fffb0
    // 0x9ffe5c: r0 = RefreshProgressIndicator()
    //     0x9ffe5c: bl              #0x9fffb4  ; AllocateRefreshProgressIndicatorStub -> RefreshProgressIndicator (size=0x4c)
    // 0x9ffe60: d0 = 2.000000
    //     0x9ffe60: fmov            d0, #2.00000000
    // 0x9ffe64: stur            x0, [fp, #-0x20]
    // 0x9ffe68: StoreField: r0->field_3b = d0
    //     0x9ffe68: stur            d0, [x0, #0x3b]
    // 0x9ffe6c: r1 = Instance_EdgeInsets
    //     0x9ffe6c: add             x1, PP, #0x31, lsl #12  ; [pp+0x31768] Obj!EdgeInsets@e12191
    //     0x9ffe70: ldr             x1, [x1, #0x768]
    // 0x9ffe74: StoreField: r0->field_43 = r1
    //     0x9ffe74: stur            w1, [x0, #0x43]
    // 0x9ffe78: r1 = Instance_EdgeInsets
    //     0x9ffe78: add             x1, PP, #0x25, lsl #12  ; [pp+0x25768] Obj!EdgeInsets@e120a1
    //     0x9ffe7c: ldr             x1, [x1, #0x768]
    // 0x9ffe80: StoreField: r0->field_47 = r1
    //     0x9ffe80: stur            w1, [x0, #0x47]
    // 0x9ffe84: d0 = 2.500000
    //     0x9ffe84: fmov            d0, #2.50000000
    // 0x9ffe88: StoreField: r0->field_27 = d0
    //     0x9ffe88: stur            d0, [x0, #0x27]
    // 0x9ffe8c: StoreField: r0->field_2f = rZR
    //     0x9ffe8c: stur            xzr, [x0, #0x2f]
    // 0x9ffe90: r1 = Instance__ActivityIndicatorType
    //     0x9ffe90: add             x1, PP, #0x44, lsl #12  ; [pp+0x44160] Obj!_ActivityIndicatorType@e364c1
    //     0x9ffe94: ldr             x1, [x1, #0x160]
    // 0x9ffe98: StoreField: r0->field_23 = r1
    //     0x9ffe98: stur            w1, [x0, #0x23]
    // 0x9ffe9c: ldur            x1, [fp, #-0x18]
    // 0x9ffea0: StoreField: r0->field_b = r1
    //     0x9ffea0: stur            w1, [x0, #0xb]
    // 0x9ffea4: ldur            x1, [fp, #-0x10]
    // 0x9ffea8: ArrayStore: r0[0] = r1  ; List_4
    //     0x9ffea8: stur            w1, [x0, #0x17]
    // 0x9ffeac: r1 = "Refresh"
    //     0x9ffeac: add             x1, PP, #0x44, lsl #12  ; [pp+0x44168] "Refresh"
    //     0x9ffeb0: ldr             x1, [x1, #0x168]
    // 0x9ffeb4: StoreField: r0->field_1b = r1
    //     0x9ffeb4: stur            w1, [x0, #0x1b]
    // 0x9ffeb8: r0 = CupertinoActivityIndicator()
    //     0x9ffeb8: bl              #0x9fd450  ; AllocateCupertinoActivityIndicatorStub -> CupertinoActivityIndicator (size=0x24)
    // 0x9ffebc: mov             x2, x0
    // 0x9ffec0: r0 = true
    //     0x9ffec0: add             x0, NULL, #0x20  ; true
    // 0x9ffec4: stur            x2, [fp, #-0x10]
    // 0x9ffec8: StoreField: r2->field_f = r0
    //     0x9ffec8: stur            w0, [x2, #0xf]
    // 0x9ffecc: d0 = 10.000000
    //     0x9ffecc: fmov            d0, #10.00000000
    // 0x9ffed0: StoreField: r2->field_13 = d0
    //     0x9ffed0: stur            d0, [x2, #0x13]
    // 0x9ffed4: d0 = 1.000000
    //     0x9ffed4: fmov            d0, #1.00000000
    // 0x9ffed8: StoreField: r2->field_1b = d0
    //     0x9ffed8: stur            d0, [x2, #0x1b]
    // 0x9ffedc: ldur            x0, [fp, #-8]
    // 0x9ffee0: LoadField: r1 = r0->field_43
    //     0x9ffee0: ldur            w1, [x0, #0x43]
    // 0x9ffee4: DecompressPointer r1
    //     0x9ffee4: add             x1, x1, HEAP, lsl #32
    // 0x9ffee8: LoadField: r0 = r1->field_7
    //     0x9ffee8: ldur            x0, [x1, #7]
    // 0x9ffeec: cmp             x0, #1
    // 0x9ffef0: b.gt            #0x9fff64
    // 0x9ffef4: cmp             x0, #0
    // 0x9ffef8: b.gt            #0x9fff0c
    // 0x9ffefc: ldur            x0, [fp, #-0x20]
    // 0x9fff00: LeaveFrame
    //     0x9fff00: mov             SP, fp
    //     0x9fff04: ldp             fp, lr, [SP], #0x10
    // 0x9fff08: ret
    //     0x9fff08: ret             
    // 0x9fff0c: ldr             x1, [fp, #0x18]
    // 0x9fff10: r0 = of()
    //     0x9fff10: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9fff14: LoadField: r1 = r0->field_23
    //     0x9fff14: ldur            w1, [x0, #0x23]
    // 0x9fff18: DecompressPointer r1
    //     0x9fff18: add             x1, x1, HEAP, lsl #32
    // 0x9fff1c: LoadField: r0 = r1->field_7
    //     0x9fff1c: ldur            x0, [x1, #7]
    // 0x9fff20: cmp             x0, #2
    // 0x9fff24: b.gt            #0x9fff34
    // 0x9fff28: cmp             x0, #1
    // 0x9fff2c: b.gt            #0x9fff44
    // 0x9fff30: b               #0x9fff54
    // 0x9fff34: cmp             x0, #4
    // 0x9fff38: b.gt            #0x9fff54
    // 0x9fff3c: cmp             x0, #3
    // 0x9fff40: b.le            #0x9fff54
    // 0x9fff44: ldur            x0, [fp, #-0x10]
    // 0x9fff48: LeaveFrame
    //     0x9fff48: mov             SP, fp
    //     0x9fff4c: ldp             fp, lr, [SP], #0x10
    // 0x9fff50: ret
    //     0x9fff50: ret             
    // 0x9fff54: ldur            x0, [fp, #-0x20]
    // 0x9fff58: LeaveFrame
    //     0x9fff58: mov             SP, fp
    //     0x9fff5c: ldp             fp, lr, [SP], #0x10
    // 0x9fff60: ret
    //     0x9fff60: ret             
    // 0x9fff64: r0 = Container()
    //     0x9fff64: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9fff68: mov             x1, x0
    // 0x9fff6c: stur            x0, [fp, #-8]
    // 0x9fff70: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9fff70: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9fff74: r0 = Container()
    //     0x9fff74: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9fff78: ldur            x0, [fp, #-8]
    // 0x9fff7c: LeaveFrame
    //     0x9fff7c: mov             SP, fp
    //     0x9fff80: ldp             fp, lr, [SP], #0x10
    // 0x9fff84: ret
    //     0x9fff84: ret             
    // 0x9fff88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9fff88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9fff8c: b               #0x9ffda0
    // 0x9fff90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fff90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fff94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fff94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fff98: r9 = _value
    //     0x9fff98: add             x9, PP, #0x44, lsl #12  ; [pp+0x44170] Field <RefreshIndicatorState._value@587083489>: late (offset: 0x2c)
    //     0x9fff9c: ldr             x9, [x9, #0x170]
    // 0x9fffa0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9fffa0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9fffa4: r9 = _valueColor
    //     0x9fffa4: add             x9, PP, #0x44, lsl #12  ; [pp+0x44178] Field <RefreshIndicatorState._valueColor@587083489>: late (offset: 0x30)
    //     0x9fffa8: ldr             x9, [x9, #0x178]
    // 0x9fffac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9fffac: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9fffb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fffb0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool _handleScrollNotification(dynamic, ScrollNotification) {
    // ** addr: 0x9fffc0, size: 0x3c
    // 0x9fffc0: EnterFrame
    //     0x9fffc0: stp             fp, lr, [SP, #-0x10]!
    //     0x9fffc4: mov             fp, SP
    // 0x9fffc8: ldr             x0, [fp, #0x18]
    // 0x9fffcc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9fffcc: ldur            w1, [x0, #0x17]
    // 0x9fffd0: DecompressPointer r1
    //     0x9fffd0: add             x1, x1, HEAP, lsl #32
    // 0x9fffd4: CheckStackOverflow
    //     0x9fffd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9fffd8: cmp             SP, x16
    //     0x9fffdc: b.ls            #0x9ffff4
    // 0x9fffe0: ldr             x2, [fp, #0x10]
    // 0x9fffe4: r0 = _handleScrollNotification()
    //     0x9fffe4: bl              #0x9ffffc  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_handleScrollNotification
    // 0x9fffe8: LeaveFrame
    //     0x9fffe8: mov             SP, fp
    //     0x9fffec: ldp             fp, lr, [SP], #0x10
    // 0x9ffff0: ret
    //     0x9ffff0: ret             
    // 0x9ffff4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ffff4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ffff8: b               #0x9fffe0
  }
  _ _handleScrollNotification(/* No info */) {
    // ** addr: 0x9ffffc, size: 0x5b8
    // 0x9ffffc: EnterFrame
    //     0x9ffffc: stp             fp, lr, [SP, #-0x10]!
    //     0xa00000: mov             fp, SP
    // 0xa00004: AllocStack(0x18)
    //     0xa00004: sub             SP, SP, #0x18
    // 0xa00008: SetupParameters(RefreshIndicatorState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa00008: mov             x0, x1
    //     0xa0000c: stur            x1, [fp, #-8]
    //     0xa00010: mov             x1, x2
    //     0xa00014: stur            x2, [fp, #-0x10]
    // 0xa00018: CheckStackOverflow
    //     0xa00018: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0001c: cmp             SP, x16
    //     0xa00020: b.ls            #0xa00504
    // 0xa00024: r1 = 1
    //     0xa00024: movz            x1, #0x1
    // 0xa00028: r0 = AllocateContext()
    //     0xa00028: bl              #0xec126c  ; AllocateContextStub
    // 0xa0002c: mov             x2, x0
    // 0xa00030: ldur            x0, [fp, #-8]
    // 0xa00034: stur            x2, [fp, #-0x18]
    // 0xa00038: StoreField: r2->field_f = r0
    //     0xa00038: stur            w0, [x2, #0xf]
    // 0xa0003c: LoadField: r1 = r0->field_b
    //     0xa0003c: ldur            w1, [x0, #0xb]
    // 0xa00040: DecompressPointer r1
    //     0xa00040: add             x1, x1, HEAP, lsl #32
    // 0xa00044: cmp             w1, NULL
    // 0xa00048: b.eq            #0xa0050c
    // 0xa0004c: ldur            x1, [fp, #-0x10]
    // 0xa00050: r0 = defaultScrollNotificationPredicate()
    //     0xa00050: bl              #0x9a35ac  ; [package:flutter/src/widgets/scroll_notification.dart] ::defaultScrollNotificationPredicate
    // 0xa00054: tbz             w0, #4, #0xa00068
    // 0xa00058: r0 = false
    //     0xa00058: add             x0, NULL, #0x30  ; false
    // 0xa0005c: LeaveFrame
    //     0xa0005c: mov             SP, fp
    //     0xa00060: ldp             fp, lr, [SP], #0x10
    // 0xa00064: ret
    //     0xa00064: ret             
    // 0xa00068: ldur            x1, [fp, #-8]
    // 0xa0006c: ldur            x2, [fp, #-0x10]
    // 0xa00070: r0 = _shouldStart()
    //     0xa00070: bl              #0xa00d90  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_shouldStart
    // 0xa00074: tbnz            w0, #4, #0xa000a4
    // 0xa00078: ldur            x2, [fp, #-0x18]
    // 0xa0007c: r1 = Function '<anonymous closure>':.
    //     0xa0007c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44180] AnonymousClosure: (0xa00f74), in [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_handleScrollNotification (0x9ffffc)
    //     0xa00080: ldr             x1, [x1, #0x180]
    // 0xa00084: r0 = AllocateClosure()
    //     0xa00084: bl              #0xec1630  ; AllocateClosureStub
    // 0xa00088: ldur            x1, [fp, #-8]
    // 0xa0008c: mov             x2, x0
    // 0xa00090: r0 = setState()
    //     0xa00090: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa00094: r0 = false
    //     0xa00094: add             x0, NULL, #0x30  ; false
    // 0xa00098: LeaveFrame
    //     0xa00098: mov             SP, fp
    //     0xa0009c: ldp             fp, lr, [SP], #0x10
    // 0xa000a0: ret
    //     0xa000a0: ret             
    // 0xa000a4: ldur            x2, [fp, #-0x10]
    // 0xa000a8: LoadField: r1 = r2->field_f
    //     0xa000a8: ldur            w1, [x2, #0xf]
    // 0xa000ac: DecompressPointer r1
    //     0xa000ac: add             x1, x1, HEAP, lsl #32
    // 0xa000b0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa000b0: ldur            w0, [x1, #0x17]
    // 0xa000b4: DecompressPointer r0
    //     0xa000b4: add             x0, x0, HEAP, lsl #32
    // 0xa000b8: r16 = Instance_AxisDirection
    //     0xa000b8: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xa000bc: cmp             w0, w16
    // 0xa000c0: b.eq            #0xa000d0
    // 0xa000c4: r16 = Instance_AxisDirection
    //     0xa000c4: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xa000c8: cmp             w0, w16
    // 0xa000cc: b.ne            #0xa000d8
    // 0xa000d0: r4 = true
    //     0xa000d0: add             x4, NULL, #0x20  ; true
    // 0xa000d4: b               #0xa000fc
    // 0xa000d8: r16 = Instance_AxisDirection
    //     0xa000d8: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0xa000dc: cmp             w0, w16
    // 0xa000e0: b.eq            #0xa000f0
    // 0xa000e4: r16 = Instance_AxisDirection
    //     0xa000e4: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0xa000e8: cmp             w0, w16
    // 0xa000ec: b.ne            #0xa000f8
    // 0xa000f0: r4 = Null
    //     0xa000f0: mov             x4, NULL
    // 0xa000f4: b               #0xa000fc
    // 0xa000f8: r4 = Null
    //     0xa000f8: mov             x4, NULL
    // 0xa000fc: ldur            x3, [fp, #-8]
    // 0xa00100: LoadField: r5 = r3->field_37
    //     0xa00100: ldur            w5, [x3, #0x37]
    // 0xa00104: DecompressPointer r5
    //     0xa00104: add             x5, x5, HEAP, lsl #32
    // 0xa00108: cmp             w4, w5
    // 0xa0010c: b.eq            #0xa0014c
    // 0xa00110: LoadField: r0 = r3->field_33
    //     0xa00110: ldur            w0, [x3, #0x33]
    // 0xa00114: DecompressPointer r0
    //     0xa00114: add             x0, x0, HEAP, lsl #32
    // 0xa00118: r16 = Instance_RefreshIndicatorStatus
    //     0xa00118: add             x16, PP, #0x44, lsl #12  ; [pp+0x44188] Obj!RefreshIndicatorStatus@e36441
    //     0xa0011c: ldr             x16, [x16, #0x188]
    // 0xa00120: cmp             w0, w16
    // 0xa00124: b.eq            #0xa00138
    // 0xa00128: r16 = Instance_RefreshIndicatorStatus
    //     0xa00128: add             x16, PP, #0x44, lsl #12  ; [pp+0x44190] Obj!RefreshIndicatorStatus@e36421
    //     0xa0012c: ldr             x16, [x16, #0x190]
    // 0xa00130: cmp             w0, w16
    // 0xa00134: b.ne            #0xa004f4
    // 0xa00138: mov             x1, x3
    // 0xa0013c: r2 = Instance_RefreshIndicatorStatus
    //     0xa0013c: add             x2, PP, #0x44, lsl #12  ; [pp+0x44198] Obj!RefreshIndicatorStatus@e36401
    //     0xa00140: ldr             x2, [x2, #0x198]
    // 0xa00144: r0 = _dismiss()
    //     0xa00144: bl              #0xa00b2c  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_dismiss
    // 0xa00148: b               #0xa004f4
    // 0xa0014c: r4 = LoadClassIdInstr(r2)
    //     0xa0014c: ldur            x4, [x2, #-1]
    //     0xa00150: ubfx            x4, x4, #0xc, #0x14
    // 0xa00154: cmp             x4, #0xaa1
    // 0xa00158: b.ne            #0xa002d8
    // 0xa0015c: LoadField: r4 = r3->field_33
    //     0xa0015c: ldur            w4, [x3, #0x33]
    // 0xa00160: DecompressPointer r4
    //     0xa00160: add             x4, x4, HEAP, lsl #32
    // 0xa00164: r16 = Instance_RefreshIndicatorStatus
    //     0xa00164: add             x16, PP, #0x44, lsl #12  ; [pp+0x44188] Obj!RefreshIndicatorStatus@e36441
    //     0xa00168: ldr             x16, [x16, #0x188]
    // 0xa0016c: cmp             w4, w16
    // 0xa00170: b.eq            #0xa00184
    // 0xa00174: r16 = Instance_RefreshIndicatorStatus
    //     0xa00174: add             x16, PP, #0x44, lsl #12  ; [pp+0x44190] Obj!RefreshIndicatorStatus@e36421
    //     0xa00178: ldr             x16, [x16, #0x190]
    // 0xa0017c: cmp             w4, w16
    // 0xa00180: b.ne            #0xa0029c
    // 0xa00184: r16 = Instance_AxisDirection
    //     0xa00184: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xa00188: cmp             w0, w16
    // 0xa0018c: b.ne            #0xa00204
    // 0xa00190: LoadField: r0 = r3->field_3b
    //     0xa00190: ldur            w0, [x3, #0x3b]
    // 0xa00194: DecompressPointer r0
    //     0xa00194: add             x0, x0, HEAP, lsl #32
    // 0xa00198: cmp             w0, NULL
    // 0xa0019c: b.eq            #0xa00510
    // 0xa001a0: LoadField: r4 = r2->field_1b
    //     0xa001a0: ldur            w4, [x2, #0x1b]
    // 0xa001a4: DecompressPointer r4
    //     0xa001a4: add             x4, x4, HEAP, lsl #32
    // 0xa001a8: cmp             w4, NULL
    // 0xa001ac: b.eq            #0xa00514
    // 0xa001b0: LoadField: d0 = r0->field_7
    //     0xa001b0: ldur            d0, [x0, #7]
    // 0xa001b4: LoadField: d1 = r4->field_7
    //     0xa001b4: ldur            d1, [x4, #7]
    // 0xa001b8: fsub            d2, d0, d1
    // 0xa001bc: r0 = inline_Allocate_Double()
    //     0xa001bc: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xa001c0: add             x0, x0, #0x10
    //     0xa001c4: cmp             x4, x0
    //     0xa001c8: b.ls            #0xa00518
    //     0xa001cc: str             x0, [THR, #0x50]  ; THR::top
    //     0xa001d0: sub             x0, x0, #0xf
    //     0xa001d4: movz            x4, #0xe15c
    //     0xa001d8: movk            x4, #0x3, lsl #16
    //     0xa001dc: stur            x4, [x0, #-1]
    // 0xa001e0: StoreField: r0->field_7 = d2
    //     0xa001e0: stur            d2, [x0, #7]
    // 0xa001e4: StoreField: r3->field_3b = r0
    //     0xa001e4: stur            w0, [x3, #0x3b]
    //     0xa001e8: ldurb           w16, [x3, #-1]
    //     0xa001ec: ldurb           w17, [x0, #-1]
    //     0xa001f0: and             x16, x17, x16, lsr #2
    //     0xa001f4: tst             x16, HEAP, lsr #32
    //     0xa001f8: b.eq            #0xa00200
    //     0xa001fc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa00200: b               #0xa00280
    // 0xa00204: r16 = Instance_AxisDirection
    //     0xa00204: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xa00208: cmp             w0, w16
    // 0xa0020c: b.ne            #0xa00280
    // 0xa00210: LoadField: r0 = r3->field_3b
    //     0xa00210: ldur            w0, [x3, #0x3b]
    // 0xa00214: DecompressPointer r0
    //     0xa00214: add             x0, x0, HEAP, lsl #32
    // 0xa00218: cmp             w0, NULL
    // 0xa0021c: b.eq            #0xa00538
    // 0xa00220: LoadField: r4 = r2->field_1b
    //     0xa00220: ldur            w4, [x2, #0x1b]
    // 0xa00224: DecompressPointer r4
    //     0xa00224: add             x4, x4, HEAP, lsl #32
    // 0xa00228: cmp             w4, NULL
    // 0xa0022c: b.eq            #0xa0053c
    // 0xa00230: LoadField: d0 = r0->field_7
    //     0xa00230: ldur            d0, [x0, #7]
    // 0xa00234: LoadField: d1 = r4->field_7
    //     0xa00234: ldur            d1, [x4, #7]
    // 0xa00238: fadd            d2, d0, d1
    // 0xa0023c: r0 = inline_Allocate_Double()
    //     0xa0023c: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xa00240: add             x0, x0, #0x10
    //     0xa00244: cmp             x4, x0
    //     0xa00248: b.ls            #0xa00540
    //     0xa0024c: str             x0, [THR, #0x50]  ; THR::top
    //     0xa00250: sub             x0, x0, #0xf
    //     0xa00254: movz            x4, #0xe15c
    //     0xa00258: movk            x4, #0x3, lsl #16
    //     0xa0025c: stur            x4, [x0, #-1]
    // 0xa00260: StoreField: r0->field_7 = d2
    //     0xa00260: stur            d2, [x0, #7]
    // 0xa00264: StoreField: r3->field_3b = r0
    //     0xa00264: stur            w0, [x3, #0x3b]
    //     0xa00268: ldurb           w16, [x3, #-1]
    //     0xa0026c: ldurb           w17, [x0, #-1]
    //     0xa00270: and             x16, x17, x16, lsr #2
    //     0xa00274: tst             x16, HEAP, lsr #32
    //     0xa00278: b.eq            #0xa00280
    //     0xa0027c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa00280: LoadField: r0 = r1->field_13
    //     0xa00280: ldur            w0, [x1, #0x13]
    // 0xa00284: DecompressPointer r0
    //     0xa00284: add             x0, x0, HEAP, lsl #32
    // 0xa00288: cmp             w0, NULL
    // 0xa0028c: b.eq            #0xa00560
    // 0xa00290: LoadField: d0 = r0->field_7
    //     0xa00290: ldur            d0, [x0, #7]
    // 0xa00294: mov             x1, x3
    // 0xa00298: r0 = _checkDragOffset()
    //     0xa00298: bl              #0xa008a4  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_checkDragOffset
    // 0xa0029c: ldur            x2, [fp, #-8]
    // 0xa002a0: LoadField: r0 = r2->field_33
    //     0xa002a0: ldur            w0, [x2, #0x33]
    // 0xa002a4: DecompressPointer r0
    //     0xa002a4: add             x0, x0, HEAP, lsl #32
    // 0xa002a8: r16 = Instance_RefreshIndicatorStatus
    //     0xa002a8: add             x16, PP, #0x44, lsl #12  ; [pp+0x44190] Obj!RefreshIndicatorStatus@e36421
    //     0xa002ac: ldr             x16, [x16, #0x190]
    // 0xa002b0: cmp             w0, w16
    // 0xa002b4: b.ne            #0xa004f4
    // 0xa002b8: ldur            x3, [fp, #-0x10]
    // 0xa002bc: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa002bc: ldur            w0, [x3, #0x17]
    // 0xa002c0: DecompressPointer r0
    //     0xa002c0: add             x0, x0, HEAP, lsl #32
    // 0xa002c4: cmp             w0, NULL
    // 0xa002c8: b.ne            #0xa004f4
    // 0xa002cc: mov             x1, x2
    // 0xa002d0: r0 = _show()
    //     0xa002d0: bl              #0xa005b4  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_show
    // 0xa002d4: b               #0xa004f4
    // 0xa002d8: mov             x16, x2
    // 0xa002dc: mov             x2, x3
    // 0xa002e0: mov             x3, x16
    // 0xa002e4: cmp             x4, #0xaa0
    // 0xa002e8: b.ne            #0xa00410
    // 0xa002ec: LoadField: r4 = r2->field_33
    //     0xa002ec: ldur            w4, [x2, #0x33]
    // 0xa002f0: DecompressPointer r4
    //     0xa002f0: add             x4, x4, HEAP, lsl #32
    // 0xa002f4: r16 = Instance_RefreshIndicatorStatus
    //     0xa002f4: add             x16, PP, #0x44, lsl #12  ; [pp+0x44188] Obj!RefreshIndicatorStatus@e36441
    //     0xa002f8: ldr             x16, [x16, #0x188]
    // 0xa002fc: cmp             w4, w16
    // 0xa00300: b.eq            #0xa00314
    // 0xa00304: r16 = Instance_RefreshIndicatorStatus
    //     0xa00304: add             x16, PP, #0x44, lsl #12  ; [pp+0x44190] Obj!RefreshIndicatorStatus@e36421
    //     0xa00308: ldr             x16, [x16, #0x190]
    // 0xa0030c: cmp             w4, w16
    // 0xa00310: b.ne            #0xa004f4
    // 0xa00314: r16 = Instance_AxisDirection
    //     0xa00314: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xa00318: cmp             w0, w16
    // 0xa0031c: b.ne            #0xa00384
    // 0xa00320: LoadField: r0 = r2->field_3b
    //     0xa00320: ldur            w0, [x2, #0x3b]
    // 0xa00324: DecompressPointer r0
    //     0xa00324: add             x0, x0, HEAP, lsl #32
    // 0xa00328: cmp             w0, NULL
    // 0xa0032c: b.eq            #0xa00564
    // 0xa00330: LoadField: d0 = r3->field_1b
    //     0xa00330: ldur            d0, [x3, #0x1b]
    // 0xa00334: LoadField: d1 = r0->field_7
    //     0xa00334: ldur            d1, [x0, #7]
    // 0xa00338: fsub            d2, d1, d0
    // 0xa0033c: r0 = inline_Allocate_Double()
    //     0xa0033c: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xa00340: add             x0, x0, #0x10
    //     0xa00344: cmp             x3, x0
    //     0xa00348: b.ls            #0xa00568
    //     0xa0034c: str             x0, [THR, #0x50]  ; THR::top
    //     0xa00350: sub             x0, x0, #0xf
    //     0xa00354: movz            x3, #0xe15c
    //     0xa00358: movk            x3, #0x3, lsl #16
    //     0xa0035c: stur            x3, [x0, #-1]
    // 0xa00360: StoreField: r0->field_7 = d2
    //     0xa00360: stur            d2, [x0, #7]
    // 0xa00364: StoreField: r2->field_3b = r0
    //     0xa00364: stur            w0, [x2, #0x3b]
    //     0xa00368: ldurb           w16, [x2, #-1]
    //     0xa0036c: ldurb           w17, [x0, #-1]
    //     0xa00370: and             x16, x17, x16, lsr #2
    //     0xa00374: tst             x16, HEAP, lsr #32
    //     0xa00378: b.eq            #0xa00380
    //     0xa0037c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa00380: b               #0xa003f0
    // 0xa00384: r16 = Instance_AxisDirection
    //     0xa00384: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xa00388: cmp             w0, w16
    // 0xa0038c: b.ne            #0xa003f0
    // 0xa00390: LoadField: r0 = r2->field_3b
    //     0xa00390: ldur            w0, [x2, #0x3b]
    // 0xa00394: DecompressPointer r0
    //     0xa00394: add             x0, x0, HEAP, lsl #32
    // 0xa00398: cmp             w0, NULL
    // 0xa0039c: b.eq            #0xa00580
    // 0xa003a0: LoadField: d0 = r3->field_1b
    //     0xa003a0: ldur            d0, [x3, #0x1b]
    // 0xa003a4: LoadField: d1 = r0->field_7
    //     0xa003a4: ldur            d1, [x0, #7]
    // 0xa003a8: fadd            d2, d1, d0
    // 0xa003ac: r0 = inline_Allocate_Double()
    //     0xa003ac: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xa003b0: add             x0, x0, #0x10
    //     0xa003b4: cmp             x3, x0
    //     0xa003b8: b.ls            #0xa00584
    //     0xa003bc: str             x0, [THR, #0x50]  ; THR::top
    //     0xa003c0: sub             x0, x0, #0xf
    //     0xa003c4: movz            x3, #0xe15c
    //     0xa003c8: movk            x3, #0x3, lsl #16
    //     0xa003cc: stur            x3, [x0, #-1]
    // 0xa003d0: StoreField: r0->field_7 = d2
    //     0xa003d0: stur            d2, [x0, #7]
    // 0xa003d4: StoreField: r2->field_3b = r0
    //     0xa003d4: stur            w0, [x2, #0x3b]
    //     0xa003d8: ldurb           w16, [x2, #-1]
    //     0xa003dc: ldurb           w17, [x0, #-1]
    //     0xa003e0: and             x16, x17, x16, lsr #2
    //     0xa003e4: tst             x16, HEAP, lsr #32
    //     0xa003e8: b.eq            #0xa003f0
    //     0xa003ec: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa003f0: LoadField: r0 = r1->field_13
    //     0xa003f0: ldur            w0, [x1, #0x13]
    // 0xa003f4: DecompressPointer r0
    //     0xa003f4: add             x0, x0, HEAP, lsl #32
    // 0xa003f8: cmp             w0, NULL
    // 0xa003fc: b.eq            #0xa0059c
    // 0xa00400: LoadField: d0 = r0->field_7
    //     0xa00400: ldur            d0, [x0, #7]
    // 0xa00404: mov             x1, x2
    // 0xa00408: r0 = _checkDragOffset()
    //     0xa00408: bl              #0xa008a4  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_checkDragOffset
    // 0xa0040c: b               #0xa004f4
    // 0xa00410: cmp             x4, #0xa9f
    // 0xa00414: b.ne            #0xa004f4
    // 0xa00418: LoadField: r0 = r2->field_33
    //     0xa00418: ldur            w0, [x2, #0x33]
    // 0xa0041c: DecompressPointer r0
    //     0xa0041c: add             x0, x0, HEAP, lsl #32
    // 0xa00420: r16 = Instance_RefreshIndicatorStatus
    //     0xa00420: add             x16, PP, #0x44, lsl #12  ; [pp+0x44190] Obj!RefreshIndicatorStatus@e36421
    //     0xa00424: ldr             x16, [x16, #0x190]
    // 0xa00428: cmp             w0, w16
    // 0xa0042c: b.ne            #0xa00488
    // 0xa00430: d0 = 1.000000
    //     0xa00430: fmov            d0, #1.00000000
    // 0xa00434: LoadField: r0 = r2->field_1b
    //     0xa00434: ldur            w0, [x2, #0x1b]
    // 0xa00438: DecompressPointer r0
    //     0xa00438: add             x0, x0, HEAP, lsl #32
    // 0xa0043c: r16 = Sentinel
    //     0xa0043c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa00440: cmp             w0, w16
    // 0xa00444: b.eq            #0xa005a0
    // 0xa00448: LoadField: r1 = r0->field_37
    //     0xa00448: ldur            w1, [x0, #0x37]
    // 0xa0044c: DecompressPointer r1
    //     0xa0044c: add             x1, x1, HEAP, lsl #32
    // 0xa00450: r16 = Sentinel
    //     0xa00450: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa00454: cmp             w1, w16
    // 0xa00458: b.eq            #0xa005ac
    // 0xa0045c: LoadField: d1 = r1->field_7
    //     0xa0045c: ldur            d1, [x1, #7]
    // 0xa00460: fcmp            d0, d1
    // 0xa00464: b.le            #0xa0047c
    // 0xa00468: mov             x1, x2
    // 0xa0046c: r2 = Instance_RefreshIndicatorStatus
    //     0xa0046c: add             x2, PP, #0x44, lsl #12  ; [pp+0x44198] Obj!RefreshIndicatorStatus@e36401
    //     0xa00470: ldr             x2, [x2, #0x198]
    // 0xa00474: r0 = _dismiss()
    //     0xa00474: bl              #0xa00b2c  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_dismiss
    // 0xa00478: b               #0xa004f4
    // 0xa0047c: mov             x1, x2
    // 0xa00480: r0 = _show()
    //     0xa00480: bl              #0xa005b4  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_show
    // 0xa00484: b               #0xa004f4
    // 0xa00488: r16 = Instance_RefreshIndicatorStatus
    //     0xa00488: add             x16, PP, #0x44, lsl #12  ; [pp+0x44188] Obj!RefreshIndicatorStatus@e36441
    //     0xa0048c: ldr             x16, [x16, #0x188]
    // 0xa00490: cmp             w0, w16
    // 0xa00494: b.ne            #0xa004ac
    // 0xa00498: mov             x1, x2
    // 0xa0049c: r2 = Instance_RefreshIndicatorStatus
    //     0xa0049c: add             x2, PP, #0x44, lsl #12  ; [pp+0x44198] Obj!RefreshIndicatorStatus@e36401
    //     0xa004a0: ldr             x2, [x2, #0x198]
    // 0xa004a4: r0 = _dismiss()
    //     0xa004a4: bl              #0xa00b2c  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_dismiss
    // 0xa004a8: b               #0xa004f4
    // 0xa004ac: r16 = Instance_RefreshIndicatorStatus
    //     0xa004ac: add             x16, PP, #0x44, lsl #12  ; [pp+0x44198] Obj!RefreshIndicatorStatus@e36401
    //     0xa004b0: ldr             x16, [x16, #0x198]
    // 0xa004b4: cmp             w0, w16
    // 0xa004b8: b.eq            #0xa004f4
    // 0xa004bc: r16 = Instance_RefreshIndicatorStatus
    //     0xa004bc: add             x16, PP, #0x44, lsl #12  ; [pp+0x44130] Obj!RefreshIndicatorStatus@e363a1
    //     0xa004c0: ldr             x16, [x16, #0x130]
    // 0xa004c4: cmp             w0, w16
    // 0xa004c8: b.eq            #0xa004f4
    // 0xa004cc: r16 = Instance_RefreshIndicatorStatus
    //     0xa004cc: add             x16, PP, #0x44, lsl #12  ; [pp+0x44128] Obj!RefreshIndicatorStatus@e363c1
    //     0xa004d0: ldr             x16, [x16, #0x128]
    // 0xa004d4: cmp             w0, w16
    // 0xa004d8: b.eq            #0xa004f4
    // 0xa004dc: r16 = Instance_RefreshIndicatorStatus
    //     0xa004dc: add             x16, PP, #0x44, lsl #12  ; [pp+0x441a0] Obj!RefreshIndicatorStatus@e363e1
    //     0xa004e0: ldr             x16, [x16, #0x1a0]
    // 0xa004e4: cmp             w0, w16
    // 0xa004e8: b.eq            #0xa004f4
    // 0xa004ec: cmp             w0, NULL
    // 0xa004f0: b.eq            #0xa004f4
    // 0xa004f4: r0 = false
    //     0xa004f4: add             x0, NULL, #0x30  ; false
    // 0xa004f8: LeaveFrame
    //     0xa004f8: mov             SP, fp
    //     0xa004fc: ldp             fp, lr, [SP], #0x10
    // 0xa00500: ret
    //     0xa00500: ret             
    // 0xa00504: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa00504: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa00508: b               #0xa00024
    // 0xa0050c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0050c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa00510: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00510: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa00514: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00514: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa00518: SaveReg d2
    //     0xa00518: str             q2, [SP, #-0x10]!
    // 0xa0051c: stp             x2, x3, [SP, #-0x10]!
    // 0xa00520: SaveReg r1
    //     0xa00520: str             x1, [SP, #-8]!
    // 0xa00524: r0 = AllocateDouble()
    //     0xa00524: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa00528: RestoreReg r1
    //     0xa00528: ldr             x1, [SP], #8
    // 0xa0052c: ldp             x2, x3, [SP], #0x10
    // 0xa00530: RestoreReg d2
    //     0xa00530: ldr             q2, [SP], #0x10
    // 0xa00534: b               #0xa001e0
    // 0xa00538: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00538: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0053c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0053c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa00540: SaveReg d2
    //     0xa00540: str             q2, [SP, #-0x10]!
    // 0xa00544: stp             x2, x3, [SP, #-0x10]!
    // 0xa00548: SaveReg r1
    //     0xa00548: str             x1, [SP, #-8]!
    // 0xa0054c: r0 = AllocateDouble()
    //     0xa0054c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa00550: RestoreReg r1
    //     0xa00550: ldr             x1, [SP], #8
    // 0xa00554: ldp             x2, x3, [SP], #0x10
    // 0xa00558: RestoreReg d2
    //     0xa00558: ldr             q2, [SP], #0x10
    // 0xa0055c: b               #0xa00260
    // 0xa00560: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00560: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa00564: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00564: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa00568: SaveReg d2
    //     0xa00568: str             q2, [SP, #-0x10]!
    // 0xa0056c: stp             x1, x2, [SP, #-0x10]!
    // 0xa00570: r0 = AllocateDouble()
    //     0xa00570: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa00574: ldp             x1, x2, [SP], #0x10
    // 0xa00578: RestoreReg d2
    //     0xa00578: ldr             q2, [SP], #0x10
    // 0xa0057c: b               #0xa00360
    // 0xa00580: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00580: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa00584: SaveReg d2
    //     0xa00584: str             q2, [SP, #-0x10]!
    // 0xa00588: stp             x1, x2, [SP, #-0x10]!
    // 0xa0058c: r0 = AllocateDouble()
    //     0xa0058c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa00590: ldp             x1, x2, [SP], #0x10
    // 0xa00594: RestoreReg d2
    //     0xa00594: ldr             q2, [SP], #0x10
    // 0xa00598: b               #0xa003d0
    // 0xa0059c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0059c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa005a0: r9 = _positionController
    //     0xa005a0: add             x9, PP, #0x44, lsl #12  ; [pp+0x44158] Field <RefreshIndicatorState._positionController@587083489>: late (offset: 0x1c)
    //     0xa005a4: ldr             x9, [x9, #0x158]
    // 0xa005a8: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xa005a8: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xa005ac: r9 = _value
    //     0xa005ac: ldr             x9, [PP, #0x4ea8]  ; [pp+0x4ea8] Field <AnimationController._value@441066280>: late (offset: 0x38)
    // 0xa005b0: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xa005b0: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
  }
  _ _show(/* No info */) {
    // ** addr: 0xa005b4, size: 0x130
    // 0xa005b4: EnterFrame
    //     0xa005b4: stp             fp, lr, [SP, #-0x10]!
    //     0xa005b8: mov             fp, SP
    // 0xa005bc: AllocStack(0x30)
    //     0xa005bc: sub             SP, SP, #0x30
    // 0xa005c0: SetupParameters(RefreshIndicatorState this /* r1 => r1, fp-0x8 */)
    //     0xa005c0: stur            x1, [fp, #-8]
    // 0xa005c4: CheckStackOverflow
    //     0xa005c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa005c8: cmp             SP, x16
    //     0xa005cc: b.ls            #0xa006cc
    // 0xa005d0: r1 = 2
    //     0xa005d0: movz            x1, #0x2
    // 0xa005d4: r0 = AllocateContext()
    //     0xa005d4: bl              #0xec126c  ; AllocateContextStub
    // 0xa005d8: mov             x2, x0
    // 0xa005dc: ldur            x0, [fp, #-8]
    // 0xa005e0: stur            x2, [fp, #-0x10]
    // 0xa005e4: StoreField: r2->field_f = r0
    //     0xa005e4: stur            w0, [x2, #0xf]
    // 0xa005e8: r1 = <void?>
    //     0xa005e8: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xa005ec: r0 = _Future()
    //     0xa005ec: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0xa005f0: stur            x0, [fp, #-0x18]
    // 0xa005f4: StoreField: r0->field_b = rZR
    //     0xa005f4: stur            xzr, [x0, #0xb]
    // 0xa005f8: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0xa005f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa005fc: ldr             x0, [x0, #0x7a0]
    //     0xa00600: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa00604: cmp             w0, w16
    //     0xa00608: b.ne            #0xa00614
    //     0xa0060c: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0xa00610: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xa00614: mov             x1, x0
    // 0xa00618: ldur            x0, [fp, #-0x18]
    // 0xa0061c: StoreField: r0->field_13 = r1
    //     0xa0061c: stur            w1, [x0, #0x13]
    // 0xa00620: r1 = <void?>
    //     0xa00620: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xa00624: r0 = _AsyncCompleter()
    //     0xa00624: bl              #0x661210  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0xa00628: mov             x1, x0
    // 0xa0062c: ldur            x0, [fp, #-0x18]
    // 0xa00630: StoreField: r1->field_b = r0
    //     0xa00630: stur            w0, [x1, #0xb]
    // 0xa00634: ldur            x2, [fp, #-0x10]
    // 0xa00638: StoreField: r2->field_13 = r1
    //     0xa00638: stur            w1, [x2, #0x13]
    // 0xa0063c: ldur            x0, [fp, #-8]
    // 0xa00640: r1 = Instance_RefreshIndicatorStatus
    //     0xa00640: add             x1, PP, #0x44, lsl #12  ; [pp+0x441a0] Obj!RefreshIndicatorStatus@e363e1
    //     0xa00644: ldr             x1, [x1, #0x1a0]
    // 0xa00648: StoreField: r0->field_33 = r1
    //     0xa00648: stur            w1, [x0, #0x33]
    // 0xa0064c: LoadField: r1 = r0->field_b
    //     0xa0064c: ldur            w1, [x0, #0xb]
    // 0xa00650: DecompressPointer r1
    //     0xa00650: add             x1, x1, HEAP, lsl #32
    // 0xa00654: cmp             w1, NULL
    // 0xa00658: b.eq            #0xa006d4
    // 0xa0065c: LoadField: r1 = r0->field_1b
    //     0xa0065c: ldur            w1, [x0, #0x1b]
    // 0xa00660: DecompressPointer r1
    //     0xa00660: add             x1, x1, HEAP, lsl #32
    // 0xa00664: r16 = Sentinel
    //     0xa00664: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa00668: cmp             w1, w16
    // 0xa0066c: b.eq            #0xa006d8
    // 0xa00670: r16 = Instance_Duration
    //     0xa00670: add             x16, PP, #0x25, lsl #12  ; [pp+0x25400] Obj!Duration@e3a171
    //     0xa00674: ldr             x16, [x16, #0x400]
    // 0xa00678: str             x16, [SP]
    // 0xa0067c: d0 = 0.666667
    //     0xa0067c: add             x17, PP, #0x25, lsl #12  ; [pp+0x25b98] IMM: double(0.6666666666666666) from 0x3fe5555555555555
    //     0xa00680: ldr             d0, [x17, #0xb98]
    // 0xa00684: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0xa00684: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0xa00688: ldr             x4, [x4, #0x408]
    // 0xa0068c: r0 = animateTo()
    //     0xa0068c: bl              #0x67ab14  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::animateTo
    // 0xa00690: ldur            x2, [fp, #-0x10]
    // 0xa00694: r1 = Function '<anonymous closure>':.
    //     0xa00694: add             x1, PP, #0x44, lsl #12  ; [pp+0x441a8] AnonymousClosure: (0xa006e4), in [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_show (0xa005b4)
    //     0xa00698: ldr             x1, [x1, #0x1a8]
    // 0xa0069c: stur            x0, [fp, #-8]
    // 0xa006a0: r0 = AllocateClosure()
    //     0xa006a0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa006a4: r16 = <void?>
    //     0xa006a4: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xa006a8: ldur            lr, [fp, #-8]
    // 0xa006ac: stp             lr, x16, [SP, #8]
    // 0xa006b0: str             x0, [SP]
    // 0xa006b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa006b4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa006b8: r0 = then()
    //     0xa006b8: bl              #0xdb1ab8  ; [package:flutter/src/scheduler/ticker.dart] TickerFuture::then
    // 0xa006bc: r0 = Null
    //     0xa006bc: mov             x0, NULL
    // 0xa006c0: LeaveFrame
    //     0xa006c0: mov             SP, fp
    //     0xa006c4: ldp             fp, lr, [SP], #0x10
    // 0xa006c8: ret
    //     0xa006c8: ret             
    // 0xa006cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa006cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa006d0: b               #0xa005d0
    // 0xa006d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa006d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa006d8: r9 = _positionController
    //     0xa006d8: add             x9, PP, #0x44, lsl #12  ; [pp+0x44158] Field <RefreshIndicatorState._positionController@587083489>: late (offset: 0x1c)
    //     0xa006dc: ldr             x9, [x9, #0x158]
    // 0xa006e0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa006e0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, void) {
    // ** addr: 0xa006e4, size: 0x100
    // 0xa006e4: EnterFrame
    //     0xa006e4: stp             fp, lr, [SP, #-0x10]!
    //     0xa006e8: mov             fp, SP
    // 0xa006ec: AllocStack(0x18)
    //     0xa006ec: sub             SP, SP, #0x18
    // 0xa006f0: SetupParameters()
    //     0xa006f0: ldr             x0, [fp, #0x18]
    //     0xa006f4: ldur            w3, [x0, #0x17]
    //     0xa006f8: add             x3, x3, HEAP, lsl #32
    //     0xa006fc: stur            x3, [fp, #-0x10]
    // 0xa00700: CheckStackOverflow
    //     0xa00700: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa00704: cmp             SP, x16
    //     0xa00708: b.ls            #0xa007d8
    // 0xa0070c: LoadField: r0 = r3->field_f
    //     0xa0070c: ldur            w0, [x3, #0xf]
    // 0xa00710: DecompressPointer r0
    //     0xa00710: add             x0, x0, HEAP, lsl #32
    // 0xa00714: stur            x0, [fp, #-8]
    // 0xa00718: LoadField: r1 = r0->field_f
    //     0xa00718: ldur            w1, [x0, #0xf]
    // 0xa0071c: DecompressPointer r1
    //     0xa0071c: add             x1, x1, HEAP, lsl #32
    // 0xa00720: cmp             w1, NULL
    // 0xa00724: b.eq            #0xa007c8
    // 0xa00728: LoadField: r1 = r0->field_33
    //     0xa00728: ldur            w1, [x0, #0x33]
    // 0xa0072c: DecompressPointer r1
    //     0xa0072c: add             x1, x1, HEAP, lsl #32
    // 0xa00730: r16 = Instance_RefreshIndicatorStatus
    //     0xa00730: add             x16, PP, #0x44, lsl #12  ; [pp+0x441a0] Obj!RefreshIndicatorStatus@e363e1
    //     0xa00734: ldr             x16, [x16, #0x1a0]
    // 0xa00738: cmp             w1, w16
    // 0xa0073c: b.ne            #0xa007c8
    // 0xa00740: mov             x2, x3
    // 0xa00744: r1 = Function '<anonymous closure>':.
    //     0xa00744: add             x1, PP, #0x44, lsl #12  ; [pp+0x441b0] AnonymousClosure: (0xa0087c), in [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_show (0xa005b4)
    //     0xa00748: ldr             x1, [x1, #0x1b0]
    // 0xa0074c: r0 = AllocateClosure()
    //     0xa0074c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa00750: ldur            x1, [fp, #-8]
    // 0xa00754: mov             x2, x0
    // 0xa00758: r0 = setState()
    //     0xa00758: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa0075c: ldur            x2, [fp, #-0x10]
    // 0xa00760: LoadField: r0 = r2->field_f
    //     0xa00760: ldur            w0, [x2, #0xf]
    // 0xa00764: DecompressPointer r0
    //     0xa00764: add             x0, x0, HEAP, lsl #32
    // 0xa00768: LoadField: r1 = r0->field_b
    //     0xa00768: ldur            w1, [x0, #0xb]
    // 0xa0076c: DecompressPointer r1
    //     0xa0076c: add             x1, x1, HEAP, lsl #32
    // 0xa00770: cmp             w1, NULL
    // 0xa00774: b.eq            #0xa007e0
    // 0xa00778: LoadField: r0 = r1->field_1f
    //     0xa00778: ldur            w0, [x1, #0x1f]
    // 0xa0077c: DecompressPointer r0
    //     0xa0077c: add             x0, x0, HEAP, lsl #32
    // 0xa00780: str             x0, [SP]
    // 0xa00784: ClosureCall
    //     0xa00784: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xa00788: ldur            x2, [x0, #0x1f]
    //     0xa0078c: blr             x2
    // 0xa00790: ldur            x2, [fp, #-0x10]
    // 0xa00794: r1 = Function '<anonymous closure>':.
    //     0xa00794: add             x1, PP, #0x44, lsl #12  ; [pp+0x441b8] AnonymousClosure: (0xa007e4), in [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_show (0xa005b4)
    //     0xa00798: ldr             x1, [x1, #0x1b8]
    // 0xa0079c: stur            x0, [fp, #-8]
    // 0xa007a0: r0 = AllocateClosure()
    //     0xa007a0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa007a4: ldur            x1, [fp, #-8]
    // 0xa007a8: r2 = LoadClassIdInstr(r1)
    //     0xa007a8: ldur            x2, [x1, #-1]
    //     0xa007ac: ubfx            x2, x2, #0xc, #0x14
    // 0xa007b0: mov             x16, x0
    // 0xa007b4: mov             x0, x2
    // 0xa007b8: mov             x2, x16
    // 0xa007bc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa007bc: sub             lr, x0, #0xffa
    //     0xa007c0: ldr             lr, [x21, lr, lsl #3]
    //     0xa007c4: blr             lr
    // 0xa007c8: r0 = Null
    //     0xa007c8: mov             x0, NULL
    // 0xa007cc: LeaveFrame
    //     0xa007cc: mov             SP, fp
    //     0xa007d0: ldp             fp, lr, [SP], #0x10
    // 0xa007d4: ret
    //     0xa007d4: ret             
    // 0xa007d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa007d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa007dc: b               #0xa0070c
    // 0xa007e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa007e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xa007e4, size: 0x98
    // 0xa007e4: EnterFrame
    //     0xa007e4: stp             fp, lr, [SP, #-0x10]!
    //     0xa007e8: mov             fp, SP
    // 0xa007ec: AllocStack(0x8)
    //     0xa007ec: sub             SP, SP, #8
    // 0xa007f0: SetupParameters()
    //     0xa007f0: ldr             x0, [fp, #0x10]
    //     0xa007f4: ldur            w2, [x0, #0x17]
    //     0xa007f8: add             x2, x2, HEAP, lsl #32
    //     0xa007fc: stur            x2, [fp, #-8]
    // 0xa00800: CheckStackOverflow
    //     0xa00800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa00804: cmp             SP, x16
    //     0xa00808: b.ls            #0xa00874
    // 0xa0080c: LoadField: r0 = r2->field_f
    //     0xa0080c: ldur            w0, [x2, #0xf]
    // 0xa00810: DecompressPointer r0
    //     0xa00810: add             x0, x0, HEAP, lsl #32
    // 0xa00814: LoadField: r1 = r0->field_f
    //     0xa00814: ldur            w1, [x0, #0xf]
    // 0xa00818: DecompressPointer r1
    //     0xa00818: add             x1, x1, HEAP, lsl #32
    // 0xa0081c: cmp             w1, NULL
    // 0xa00820: b.eq            #0xa00864
    // 0xa00824: LoadField: r1 = r0->field_33
    //     0xa00824: ldur            w1, [x0, #0x33]
    // 0xa00828: DecompressPointer r1
    //     0xa00828: add             x1, x1, HEAP, lsl #32
    // 0xa0082c: r16 = Instance_RefreshIndicatorStatus
    //     0xa0082c: add             x16, PP, #0x44, lsl #12  ; [pp+0x44128] Obj!RefreshIndicatorStatus@e363c1
    //     0xa00830: ldr             x16, [x16, #0x128]
    // 0xa00834: cmp             w1, w16
    // 0xa00838: b.ne            #0xa00864
    // 0xa0083c: LoadField: r1 = r2->field_13
    //     0xa0083c: ldur            w1, [x2, #0x13]
    // 0xa00840: DecompressPointer r1
    //     0xa00840: add             x1, x1, HEAP, lsl #32
    // 0xa00844: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa00844: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa00848: r0 = complete()
    //     0xa00848: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0xa0084c: ldur            x0, [fp, #-8]
    // 0xa00850: LoadField: r1 = r0->field_f
    //     0xa00850: ldur            w1, [x0, #0xf]
    // 0xa00854: DecompressPointer r1
    //     0xa00854: add             x1, x1, HEAP, lsl #32
    // 0xa00858: r2 = Instance_RefreshIndicatorStatus
    //     0xa00858: add             x2, PP, #0x44, lsl #12  ; [pp+0x44130] Obj!RefreshIndicatorStatus@e363a1
    //     0xa0085c: ldr             x2, [x2, #0x130]
    // 0xa00860: r0 = _dismiss()
    //     0xa00860: bl              #0xa00b2c  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_dismiss
    // 0xa00864: r0 = Null
    //     0xa00864: mov             x0, NULL
    // 0xa00868: LeaveFrame
    //     0xa00868: mov             SP, fp
    //     0xa0086c: ldp             fp, lr, [SP], #0x10
    // 0xa00870: ret
    //     0xa00870: ret             
    // 0xa00874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa00874: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa00878: b               #0xa0080c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa0087c, size: 0x28
    // 0xa0087c: r1 = Instance_RefreshIndicatorStatus
    //     0xa0087c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44128] Obj!RefreshIndicatorStatus@e363c1
    //     0xa00880: ldr             x1, [x1, #0x128]
    // 0xa00884: ldr             x2, [SP]
    // 0xa00888: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa00888: ldur            w3, [x2, #0x17]
    // 0xa0088c: DecompressPointer r3
    //     0xa0088c: add             x3, x3, HEAP, lsl #32
    // 0xa00890: LoadField: r2 = r3->field_f
    //     0xa00890: ldur            w2, [x3, #0xf]
    // 0xa00894: DecompressPointer r2
    //     0xa00894: add             x2, x2, HEAP, lsl #32
    // 0xa00898: StoreField: r2->field_33 = r1
    //     0xa00898: stur            w1, [x2, #0x33]
    // 0xa0089c: r0 = Null
    //     0xa0089c: mov             x0, NULL
    // 0xa008a0: ret
    //     0xa008a0: ret             
  }
  _ _checkDragOffset(/* No info */) {
    // ** addr: 0xa008a4, size: 0x220
    // 0xa008a4: EnterFrame
    //     0xa008a4: stp             fp, lr, [SP, #-0x10]!
    //     0xa008a8: mov             fp, SP
    // 0xa008ac: AllocStack(0x10)
    //     0xa008ac: sub             SP, SP, #0x10
    // 0xa008b0: d1 = 0.250000
    //     0xa008b0: fmov            d1, #0.25000000
    // 0xa008b4: mov             x0, x1
    // 0xa008b8: stur            x1, [fp, #-8]
    // 0xa008bc: CheckStackOverflow
    //     0xa008bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa008c0: cmp             SP, x16
    //     0xa008c4: b.ls            #0xa00a98
    // 0xa008c8: LoadField: r1 = r0->field_3b
    //     0xa008c8: ldur            w1, [x0, #0x3b]
    // 0xa008cc: DecompressPointer r1
    //     0xa008cc: add             x1, x1, HEAP, lsl #32
    // 0xa008d0: cmp             w1, NULL
    // 0xa008d4: b.eq            #0xa00aa0
    // 0xa008d8: fmul            d2, d0, d1
    // 0xa008dc: LoadField: d0 = r1->field_7
    //     0xa008dc: ldur            d0, [x1, #7]
    // 0xa008e0: fdiv            d1, d0, d2
    // 0xa008e4: LoadField: r1 = r0->field_33
    //     0xa008e4: ldur            w1, [x0, #0x33]
    // 0xa008e8: DecompressPointer r1
    //     0xa008e8: add             x1, x1, HEAP, lsl #32
    // 0xa008ec: r16 = Instance_RefreshIndicatorStatus
    //     0xa008ec: add             x16, PP, #0x44, lsl #12  ; [pp+0x44190] Obj!RefreshIndicatorStatus@e36421
    //     0xa008f0: ldr             x16, [x16, #0x190]
    // 0xa008f4: cmp             w1, w16
    // 0xa008f8: b.ne            #0xa00950
    // 0xa008fc: d0 = 0.666667
    //     0xa008fc: add             x17, PP, #0x25, lsl #12  ; [pp+0x25b98] IMM: double(0.6666666666666666) from 0x3fe5555555555555
    //     0xa00900: ldr             d0, [x17, #0xb98]
    // 0xa00904: fcmp            d1, d0
    // 0xa00908: b.le            #0xa00918
    // 0xa0090c: mov             v0.16b, v1.16b
    // 0xa00910: d2 = 0.000000
    //     0xa00910: eor             v2.16b, v2.16b, v2.16b
    // 0xa00914: b               #0xa00958
    // 0xa00918: fcmp            d0, d1
    // 0xa0091c: b.le            #0xa00930
    // 0xa00920: d0 = 0.666667
    //     0xa00920: add             x17, PP, #0x25, lsl #12  ; [pp+0x25b98] IMM: double(0.6666666666666666) from 0x3fe5555555555555
    //     0xa00924: ldr             d0, [x17, #0xb98]
    // 0xa00928: d2 = 0.000000
    //     0xa00928: eor             v2.16b, v2.16b, v2.16b
    // 0xa0092c: b               #0xa00958
    // 0xa00930: d2 = 0.000000
    //     0xa00930: eor             v2.16b, v2.16b, v2.16b
    // 0xa00934: fcmp            d1, d2
    // 0xa00938: b.ne            #0xa00948
    // 0xa0093c: fadd            d3, d1, d0
    // 0xa00940: mov             v0.16b, v3.16b
    // 0xa00944: b               #0xa00958
    // 0xa00948: mov             v0.16b, v1.16b
    // 0xa0094c: b               #0xa00958
    // 0xa00950: d2 = 0.000000
    //     0xa00950: eor             v2.16b, v2.16b, v2.16b
    // 0xa00954: mov             v0.16b, v1.16b
    // 0xa00958: LoadField: r1 = r0->field_1b
    //     0xa00958: ldur            w1, [x0, #0x1b]
    // 0xa0095c: DecompressPointer r1
    //     0xa0095c: add             x1, x1, HEAP, lsl #32
    // 0xa00960: r16 = Sentinel
    //     0xa00960: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa00964: cmp             w1, w16
    // 0xa00968: b.eq            #0xa00aa4
    // 0xa0096c: fcmp            d2, d0
    // 0xa00970: b.le            #0xa0097c
    // 0xa00974: d0 = 0.000000
    //     0xa00974: eor             v0.16b, v0.16b, v0.16b
    // 0xa00978: b               #0xa0099c
    // 0xa0097c: d1 = 1.000000
    //     0xa0097c: fmov            d1, #1.00000000
    // 0xa00980: fcmp            d0, d1
    // 0xa00984: b.le            #0xa00990
    // 0xa00988: d0 = 1.000000
    //     0xa00988: fmov            d0, #1.00000000
    // 0xa0098c: b               #0xa0099c
    // 0xa00990: fcmp            d0, d0
    // 0xa00994: b.vc            #0xa0099c
    // 0xa00998: d0 = 1.000000
    //     0xa00998: fmov            d0, #1.00000000
    // 0xa0099c: r0 = value=()
    //     0xa0099c: bl              #0x6567c4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::value=
    // 0xa009a0: ldur            x2, [fp, #-8]
    // 0xa009a4: LoadField: r0 = r2->field_33
    //     0xa009a4: ldur            w0, [x2, #0x33]
    // 0xa009a8: DecompressPointer r0
    //     0xa009a8: add             x0, x0, HEAP, lsl #32
    // 0xa009ac: r16 = Instance_RefreshIndicatorStatus
    //     0xa009ac: add             x16, PP, #0x44, lsl #12  ; [pp+0x44188] Obj!RefreshIndicatorStatus@e36441
    //     0xa009b0: ldr             x16, [x16, #0x188]
    // 0xa009b4: cmp             w0, w16
    // 0xa009b8: b.ne            #0xa00a88
    // 0xa009bc: LoadField: r1 = r2->field_2f
    //     0xa009bc: ldur            w1, [x2, #0x2f]
    // 0xa009c0: DecompressPointer r1
    //     0xa009c0: add             x1, x1, HEAP, lsl #32
    // 0xa009c4: r16 = Sentinel
    //     0xa009c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa009c8: cmp             w1, w16
    // 0xa009cc: b.eq            #0xa00ab0
    // 0xa009d0: r0 = LoadClassIdInstr(r1)
    //     0xa009d0: ldur            x0, [x1, #-1]
    //     0xa009d4: ubfx            x0, x0, #0xc, #0x14
    // 0xa009d8: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xa009d8: movz            x17, #0x276f
    //     0xa009dc: movk            x17, #0x1, lsl #16
    //     0xa009e0: add             lr, x0, x17
    //     0xa009e4: ldr             lr, [x21, lr, lsl #3]
    //     0xa009e8: blr             lr
    // 0xa009ec: cmp             w0, NULL
    // 0xa009f0: b.eq            #0xa00abc
    // 0xa009f4: r1 = LoadClassIdInstr(r0)
    //     0xa009f4: ldur            x1, [x0, #-1]
    //     0xa009f8: ubfx            x1, x1, #0xc, #0x14
    // 0xa009fc: mov             x16, x0
    // 0xa00a00: mov             x0, x1
    // 0xa00a04: mov             x1, x16
    // 0xa00a08: r0 = GDT[cid_x0 + -0xcc1]()
    //     0xa00a08: sub             lr, x0, #0xcc1
    //     0xa00a0c: ldr             lr, [x21, lr, lsl #3]
    //     0xa00a10: blr             lr
    // 0xa00a14: ldur            x1, [fp, #-8]
    // 0xa00a18: stur            x0, [fp, #-0x10]
    // 0xa00a1c: LoadField: r0 = r1->field_3f
    //     0xa00a1c: ldur            w0, [x1, #0x3f]
    // 0xa00a20: DecompressPointer r0
    //     0xa00a20: add             x0, x0, HEAP, lsl #32
    // 0xa00a24: r16 = Sentinel
    //     0xa00a24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa00a28: cmp             w0, w16
    // 0xa00a2c: b.ne            #0xa00a3c
    // 0xa00a30: r2 = _effectiveValueColor
    //     0xa00a30: add             x2, PP, #0x44, lsl #12  ; [pp+0x441c0] Field <RefreshIndicatorState._effectiveValueColor@587083489>: late (offset: 0x40)
    //     0xa00a34: ldr             x2, [x2, #0x1c0]
    // 0xa00a38: r0 = InitLateInstanceField()
    //     0xa00a38: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0xa00a3c: r1 = LoadClassIdInstr(r0)
    //     0xa00a3c: ldur            x1, [x0, #-1]
    //     0xa00a40: ubfx            x1, x1, #0xc, #0x14
    // 0xa00a44: mov             x16, x0
    // 0xa00a48: mov             x0, x1
    // 0xa00a4c: mov             x1, x16
    // 0xa00a50: r0 = GDT[cid_x0 + -0xcc1]()
    //     0xa00a50: sub             lr, x0, #0xcc1
    //     0xa00a54: ldr             lr, [x21, lr, lsl #3]
    //     0xa00a58: blr             lr
    // 0xa00a5c: ldur            x1, [fp, #-0x10]
    // 0xa00a60: cmp             x1, x0
    // 0xa00a64: b.ne            #0xa00a88
    // 0xa00a68: ldur            x1, [fp, #-8]
    // 0xa00a6c: r2 = Instance_RefreshIndicatorStatus
    //     0xa00a6c: add             x2, PP, #0x44, lsl #12  ; [pp+0x44190] Obj!RefreshIndicatorStatus@e36421
    //     0xa00a70: ldr             x2, [x2, #0x190]
    // 0xa00a74: StoreField: r1->field_33 = r2
    //     0xa00a74: stur            w2, [x1, #0x33]
    // 0xa00a78: LoadField: r2 = r1->field_b
    //     0xa00a78: ldur            w2, [x1, #0xb]
    // 0xa00a7c: DecompressPointer r2
    //     0xa00a7c: add             x2, x2, HEAP, lsl #32
    // 0xa00a80: cmp             w2, NULL
    // 0xa00a84: b.eq            #0xa00ac0
    // 0xa00a88: r0 = Null
    //     0xa00a88: mov             x0, NULL
    // 0xa00a8c: LeaveFrame
    //     0xa00a8c: mov             SP, fp
    //     0xa00a90: ldp             fp, lr, [SP], #0x10
    // 0xa00a94: ret
    //     0xa00a94: ret             
    // 0xa00a98: r0 = StackOverflowSharedWithFPURegs()
    //     0xa00a98: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa00a9c: b               #0xa008c8
    // 0xa00aa0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa00aa0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa00aa4: r9 = _positionController
    //     0xa00aa4: add             x9, PP, #0x44, lsl #12  ; [pp+0x44158] Field <RefreshIndicatorState._positionController@587083489>: late (offset: 0x1c)
    //     0xa00aa8: ldr             x9, [x9, #0x158]
    // 0xa00aac: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xa00aac: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xa00ab0: r9 = _valueColor
    //     0xa00ab0: add             x9, PP, #0x44, lsl #12  ; [pp+0x44178] Field <RefreshIndicatorState._valueColor@587083489>: late (offset: 0x30)
    //     0xa00ab4: ldr             x9, [x9, #0x178]
    // 0xa00ab8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa00ab8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa00abc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00abc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa00ac0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00ac0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  Color _effectiveValueColor(RefreshIndicatorState) {
    // ** addr: 0xa00ac4, size: 0x68
    // 0xa00ac4: EnterFrame
    //     0xa00ac4: stp             fp, lr, [SP, #-0x10]!
    //     0xa00ac8: mov             fp, SP
    // 0xa00acc: CheckStackOverflow
    //     0xa00acc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa00ad0: cmp             SP, x16
    //     0xa00ad4: b.ls            #0xa00b1c
    // 0xa00ad8: ldr             x0, [fp, #0x10]
    // 0xa00adc: LoadField: r1 = r0->field_b
    //     0xa00adc: ldur            w1, [x0, #0xb]
    // 0xa00ae0: DecompressPointer r1
    //     0xa00ae0: add             x1, x1, HEAP, lsl #32
    // 0xa00ae4: cmp             w1, NULL
    // 0xa00ae8: b.eq            #0xa00b24
    // 0xa00aec: LoadField: r1 = r0->field_f
    //     0xa00aec: ldur            w1, [x0, #0xf]
    // 0xa00af0: DecompressPointer r1
    //     0xa00af0: add             x1, x1, HEAP, lsl #32
    // 0xa00af4: cmp             w1, NULL
    // 0xa00af8: b.eq            #0xa00b28
    // 0xa00afc: r0 = of()
    //     0xa00afc: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa00b00: LoadField: r1 = r0->field_3f
    //     0xa00b00: ldur            w1, [x0, #0x3f]
    // 0xa00b04: DecompressPointer r1
    //     0xa00b04: add             x1, x1, HEAP, lsl #32
    // 0xa00b08: LoadField: r0 = r1->field_b
    //     0xa00b08: ldur            w0, [x1, #0xb]
    // 0xa00b0c: DecompressPointer r0
    //     0xa00b0c: add             x0, x0, HEAP, lsl #32
    // 0xa00b10: LeaveFrame
    //     0xa00b10: mov             SP, fp
    //     0xa00b14: ldp             fp, lr, [SP], #0x10
    // 0xa00b18: ret
    //     0xa00b18: ret             
    // 0xa00b1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa00b1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa00b20: b               #0xa00ad8
    // 0xa00b24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00b24: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa00b28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00b28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _dismiss(/* No info */) async {
    // ** addr: 0xa00b2c, size: 0x1e0
    // 0xa00b2c: EnterFrame
    //     0xa00b2c: stp             fp, lr, [SP, #-0x10]!
    //     0xa00b30: mov             fp, SP
    // 0xa00b34: AllocStack(0x28)
    //     0xa00b34: sub             SP, SP, #0x28
    // 0xa00b38: SetupParameters(RefreshIndicatorState this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xa00b38: stur            NULL, [fp, #-8]
    //     0xa00b3c: stur            x1, [fp, #-0x10]
    //     0xa00b40: stur            x2, [fp, #-0x18]
    // 0xa00b44: CheckStackOverflow
    //     0xa00b44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa00b48: cmp             SP, x16
    //     0xa00b4c: b.ls            #0xa00ce8
    // 0xa00b50: r1 = 2
    //     0xa00b50: movz            x1, #0x2
    // 0xa00b54: r0 = AllocateContext()
    //     0xa00b54: bl              #0xec126c  ; AllocateContextStub
    // 0xa00b58: mov             x2, x0
    // 0xa00b5c: ldur            x1, [fp, #-0x10]
    // 0xa00b60: stur            x2, [fp, #-0x20]
    // 0xa00b64: StoreField: r2->field_f = r1
    //     0xa00b64: stur            w1, [x2, #0xf]
    // 0xa00b68: ldur            x0, [fp, #-0x18]
    // 0xa00b6c: StoreField: r2->field_13 = r0
    //     0xa00b6c: stur            w0, [x2, #0x13]
    // 0xa00b70: InitAsync() -> Future<void?>
    //     0xa00b70: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa00b74: bl              #0x661298  ; InitAsyncStub
    // 0xa00b78: r1 = <void?>
    //     0xa00b78: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xa00b7c: r0 = _Future()
    //     0xa00b7c: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0xa00b80: stur            x0, [fp, #-0x18]
    // 0xa00b84: StoreField: r0->field_b = rZR
    //     0xa00b84: stur            xzr, [x0, #0xb]
    // 0xa00b88: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0xa00b88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa00b8c: ldr             x0, [x0, #0x7a0]
    //     0xa00b90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa00b94: cmp             w0, w16
    //     0xa00b98: b.ne            #0xa00ba4
    //     0xa00b9c: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0xa00ba0: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xa00ba4: mov             x1, x0
    // 0xa00ba8: ldur            x0, [fp, #-0x18]
    // 0xa00bac: StoreField: r0->field_13 = r1
    //     0xa00bac: stur            w1, [x0, #0x13]
    // 0xa00bb0: mov             x1, x0
    // 0xa00bb4: r2 = Null
    //     0xa00bb4: mov             x2, NULL
    // 0xa00bb8: r0 = _asyncComplete()
    //     0xa00bb8: bl              #0x5f9868  ; [dart:async] _Future::_asyncComplete
    // 0xa00bbc: ldur            x0, [fp, #-0x18]
    // 0xa00bc0: r0 = Await()
    //     0xa00bc0: bl              #0x661044  ; AwaitStub
    // 0xa00bc4: ldur            x2, [fp, #-0x20]
    // 0xa00bc8: r1 = Function '<anonymous closure>':.
    //     0xa00bc8: add             x1, PP, #0x44, lsl #12  ; [pp+0x441c8] AnonymousClosure: (0xa00d2c), in [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_dismiss (0xa00b2c)
    //     0xa00bcc: ldr             x1, [x1, #0x1c8]
    // 0xa00bd0: r0 = AllocateClosure()
    //     0xa00bd0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa00bd4: ldur            x1, [fp, #-0x10]
    // 0xa00bd8: mov             x2, x0
    // 0xa00bdc: r0 = setState()
    //     0xa00bdc: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa00be0: ldur            x0, [fp, #-0x10]
    // 0xa00be4: LoadField: r1 = r0->field_33
    //     0xa00be4: ldur            w1, [x0, #0x33]
    // 0xa00be8: DecompressPointer r1
    //     0xa00be8: add             x1, x1, HEAP, lsl #32
    // 0xa00bec: cmp             w1, NULL
    // 0xa00bf0: b.eq            #0xa00cf0
    // 0xa00bf4: LoadField: r2 = r1->field_7
    //     0xa00bf4: ldur            x2, [x1, #7]
    // 0xa00bf8: cmp             x2, #2
    // 0xa00bfc: b.le            #0xa00c94
    // 0xa00c00: cmp             x2, #4
    // 0xa00c04: b.gt            #0xa00c54
    // 0xa00c08: cmp             x2, #3
    // 0xa00c0c: b.le            #0xa00c94
    // 0xa00c10: LoadField: r1 = r0->field_1f
    //     0xa00c10: ldur            w1, [x0, #0x1f]
    // 0xa00c14: DecompressPointer r1
    //     0xa00c14: add             x1, x1, HEAP, lsl #32
    // 0xa00c18: r16 = Sentinel
    //     0xa00c18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa00c1c: cmp             w1, w16
    // 0xa00c20: b.eq            #0xa00cf4
    // 0xa00c24: r16 = Instance_Duration
    //     0xa00c24: add             x16, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xa00c28: ldr             x16, [x16, #0x368]
    // 0xa00c2c: str             x16, [SP]
    // 0xa00c30: d0 = 1.000000
    //     0xa00c30: fmov            d0, #1.00000000
    // 0xa00c34: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0xa00c34: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0xa00c38: ldr             x4, [x4, #0x408]
    // 0xa00c3c: r0 = animateTo()
    //     0xa00c3c: bl              #0x67ab14  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::animateTo
    // 0xa00c40: mov             x1, x0
    // 0xa00c44: stur            x1, [fp, #-0x18]
    // 0xa00c48: r0 = Await()
    //     0xa00c48: bl              #0x661044  ; AwaitStub
    // 0xa00c4c: ldur            x0, [fp, #-0x10]
    // 0xa00c50: b               #0xa00c94
    // 0xa00c54: LoadField: r1 = r0->field_1b
    //     0xa00c54: ldur            w1, [x0, #0x1b]
    // 0xa00c58: DecompressPointer r1
    //     0xa00c58: add             x1, x1, HEAP, lsl #32
    // 0xa00c5c: r16 = Sentinel
    //     0xa00c5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa00c60: cmp             w1, w16
    // 0xa00c64: b.eq            #0xa00d00
    // 0xa00c68: r16 = Instance_Duration
    //     0xa00c68: add             x16, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xa00c6c: ldr             x16, [x16, #0x368]
    // 0xa00c70: str             x16, [SP]
    // 0xa00c74: d0 = 0.000000
    //     0xa00c74: eor             v0.16b, v0.16b, v0.16b
    // 0xa00c78: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0xa00c78: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0xa00c7c: ldr             x4, [x4, #0x408]
    // 0xa00c80: r0 = animateTo()
    //     0xa00c80: bl              #0x67ab14  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::animateTo
    // 0xa00c84: mov             x1, x0
    // 0xa00c88: stur            x1, [fp, #-0x18]
    // 0xa00c8c: r0 = Await()
    //     0xa00c8c: bl              #0x661044  ; AwaitStub
    // 0xa00c90: ldur            x0, [fp, #-0x10]
    // 0xa00c94: LoadField: r1 = r0->field_f
    //     0xa00c94: ldur            w1, [x0, #0xf]
    // 0xa00c98: DecompressPointer r1
    //     0xa00c98: add             x1, x1, HEAP, lsl #32
    // 0xa00c9c: cmp             w1, NULL
    // 0xa00ca0: b.eq            #0xa00ce0
    // 0xa00ca4: ldur            x2, [fp, #-0x20]
    // 0xa00ca8: LoadField: r1 = r0->field_33
    //     0xa00ca8: ldur            w1, [x0, #0x33]
    // 0xa00cac: DecompressPointer r1
    //     0xa00cac: add             x1, x1, HEAP, lsl #32
    // 0xa00cb0: LoadField: r3 = r2->field_13
    //     0xa00cb0: ldur            w3, [x2, #0x13]
    // 0xa00cb4: DecompressPointer r3
    //     0xa00cb4: add             x3, x3, HEAP, lsl #32
    // 0xa00cb8: cmp             w1, w3
    // 0xa00cbc: b.ne            #0xa00ce0
    // 0xa00cc0: StoreField: r0->field_3b = rNULL
    //     0xa00cc0: stur            NULL, [x0, #0x3b]
    // 0xa00cc4: StoreField: r0->field_37 = rNULL
    //     0xa00cc4: stur            NULL, [x0, #0x37]
    // 0xa00cc8: r1 = Function '<anonymous closure>':.
    //     0xa00cc8: add             x1, PP, #0x44, lsl #12  ; [pp+0x441d0] AnonymousClosure: (0xa00d0c), in [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_dismiss (0xa00b2c)
    //     0xa00ccc: ldr             x1, [x1, #0x1d0]
    // 0xa00cd0: r0 = AllocateClosure()
    //     0xa00cd0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa00cd4: ldur            x1, [fp, #-0x10]
    // 0xa00cd8: mov             x2, x0
    // 0xa00cdc: r0 = setState()
    //     0xa00cdc: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa00ce0: r0 = Null
    //     0xa00ce0: mov             x0, NULL
    // 0xa00ce4: r0 = ReturnAsyncNotFuture()
    //     0xa00ce4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa00ce8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa00ce8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa00cec: b               #0xa00b50
    // 0xa00cf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00cf0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa00cf4: r9 = _scaleController
    //     0xa00cf4: add             x9, PP, #0x44, lsl #12  ; [pp+0x441d8] Field <RefreshIndicatorState._scaleController@587083489>: late (offset: 0x20)
    //     0xa00cf8: ldr             x9, [x9, #0x1d8]
    // 0xa00cfc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa00cfc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa00d00: r9 = _positionController
    //     0xa00d00: add             x9, PP, #0x44, lsl #12  ; [pp+0x44158] Field <RefreshIndicatorState._positionController@587083489>: late (offset: 0x1c)
    //     0xa00d04: ldr             x9, [x9, #0x158]
    // 0xa00d08: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa00d08: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa00d0c, size: 0x20
    // 0xa00d0c: ldr             x1, [SP]
    // 0xa00d10: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa00d10: ldur            w2, [x1, #0x17]
    // 0xa00d14: DecompressPointer r2
    //     0xa00d14: add             x2, x2, HEAP, lsl #32
    // 0xa00d18: LoadField: r1 = r2->field_f
    //     0xa00d18: ldur            w1, [x2, #0xf]
    // 0xa00d1c: DecompressPointer r1
    //     0xa00d1c: add             x1, x1, HEAP, lsl #32
    // 0xa00d20: StoreField: r1->field_33 = rNULL
    //     0xa00d20: stur            NULL, [x1, #0x33]
    // 0xa00d24: r0 = Null
    //     0xa00d24: mov             x0, NULL
    // 0xa00d28: ret
    //     0xa00d28: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa00d2c, size: 0x64
    // 0xa00d2c: ldr             x1, [SP]
    // 0xa00d30: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa00d30: ldur            w2, [x1, #0x17]
    // 0xa00d34: DecompressPointer r2
    //     0xa00d34: add             x2, x2, HEAP, lsl #32
    // 0xa00d38: LoadField: r1 = r2->field_f
    //     0xa00d38: ldur            w1, [x2, #0xf]
    // 0xa00d3c: DecompressPointer r1
    //     0xa00d3c: add             x1, x1, HEAP, lsl #32
    // 0xa00d40: LoadField: r0 = r2->field_13
    //     0xa00d40: ldur            w0, [x2, #0x13]
    // 0xa00d44: DecompressPointer r0
    //     0xa00d44: add             x0, x0, HEAP, lsl #32
    // 0xa00d48: StoreField: r1->field_33 = r0
    //     0xa00d48: stur            w0, [x1, #0x33]
    //     0xa00d4c: ldurb           w16, [x1, #-1]
    //     0xa00d50: ldurb           w17, [x0, #-1]
    //     0xa00d54: and             x16, x17, x16, lsr #2
    //     0xa00d58: tst             x16, HEAP, lsr #32
    //     0xa00d5c: b.eq            #0xa00d6c
    //     0xa00d60: str             lr, [SP, #-8]!
    //     0xa00d64: bl              #0xec0a28  ; WriteBarrierWrappersStub
    //     0xa00d68: ldr             lr, [SP], #8
    // 0xa00d6c: LoadField: r2 = r1->field_b
    //     0xa00d6c: ldur            w2, [x1, #0xb]
    // 0xa00d70: DecompressPointer r2
    //     0xa00d70: add             x2, x2, HEAP, lsl #32
    // 0xa00d74: cmp             w2, NULL
    // 0xa00d78: b.eq            #0xa00d84
    // 0xa00d7c: r0 = Null
    //     0xa00d7c: mov             x0, NULL
    // 0xa00d80: ret
    //     0xa00d80: ret             
    // 0xa00d84: EnterFrame
    //     0xa00d84: stp             fp, lr, [SP, #-0x10]!
    //     0xa00d88: mov             fp, SP
    // 0xa00d8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00d8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _shouldStart(/* No info */) {
    // ** addr: 0xa00d90, size: 0x114
    // 0xa00d90: EnterFrame
    //     0xa00d90: stp             fp, lr, [SP, #-0x10]!
    //     0xa00d94: mov             fp, SP
    // 0xa00d98: AllocStack(0x18)
    //     0xa00d98: sub             SP, SP, #0x18
    // 0xa00d9c: SetupParameters(RefreshIndicatorState this /* r1 => r0, fp-0x18 */)
    //     0xa00d9c: mov             x0, x1
    //     0xa00da0: stur            x1, [fp, #-0x18]
    // 0xa00da4: CheckStackOverflow
    //     0xa00da4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa00da8: cmp             SP, x16
    //     0xa00dac: b.ls            #0xa00e98
    // 0xa00db0: r1 = LoadClassIdInstr(r2)
    //     0xa00db0: ldur            x1, [x2, #-1]
    //     0xa00db4: ubfx            x1, x1, #0xc, #0x14
    // 0xa00db8: cmp             x1, #0xaa2
    // 0xa00dbc: b.ne            #0xa00e60
    // 0xa00dc0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa00dc0: ldur            w3, [x2, #0x17]
    // 0xa00dc4: DecompressPointer r3
    //     0xa00dc4: add             x3, x3, HEAP, lsl #32
    // 0xa00dc8: cmp             w3, NULL
    // 0xa00dcc: b.eq            #0xa00e60
    // 0xa00dd0: LoadField: r3 = r2->field_f
    //     0xa00dd0: ldur            w3, [x2, #0xf]
    // 0xa00dd4: DecompressPointer r3
    //     0xa00dd4: add             x3, x3, HEAP, lsl #32
    // 0xa00dd8: stur            x3, [fp, #-0x10]
    // 0xa00ddc: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xa00ddc: ldur            w2, [x3, #0x17]
    // 0xa00de0: DecompressPointer r2
    //     0xa00de0: add             x2, x2, HEAP, lsl #32
    // 0xa00de4: stur            x2, [fp, #-8]
    // 0xa00de8: r16 = Instance_AxisDirection
    //     0xa00de8: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xa00dec: cmp             w2, w16
    // 0xa00df0: b.ne            #0xa00e10
    // 0xa00df4: mov             x1, x3
    // 0xa00df8: r0 = extentAfter()
    //     0xa00df8: bl              #0x7f78f8  ; [package:flutter/src/widgets/scroll_metrics.dart] _FixedScrollMetrics&Object&ScrollMetrics::extentAfter
    // 0xa00dfc: mov             v1.16b, v0.16b
    // 0xa00e00: d0 = 0.000000
    //     0xa00e00: eor             v0.16b, v0.16b, v0.16b
    // 0xa00e04: fcmp            d1, d0
    // 0xa00e08: b.ne            #0xa00e14
    // 0xa00e0c: b               #0xa00e3c
    // 0xa00e10: d0 = 0.000000
    //     0xa00e10: eor             v0.16b, v0.16b, v0.16b
    // 0xa00e14: ldur            x2, [fp, #-8]
    // 0xa00e18: r16 = Instance_AxisDirection
    //     0xa00e18: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xa00e1c: cmp             w2, w16
    // 0xa00e20: b.ne            #0xa00e88
    // 0xa00e24: ldur            x1, [fp, #-0x10]
    // 0xa00e28: r0 = extentBefore()
    //     0xa00e28: bl              #0x7f797c  ; [package:flutter/src/widgets/scroll_metrics.dart] _FixedScrollMetrics&Object&ScrollMetrics::extentBefore
    // 0xa00e2c: mov             v1.16b, v0.16b
    // 0xa00e30: d0 = 0.000000
    //     0xa00e30: eor             v0.16b, v0.16b, v0.16b
    // 0xa00e34: fcmp            d1, d0
    // 0xa00e38: b.ne            #0xa00e88
    // 0xa00e3c: ldur            x0, [fp, #-0x18]
    // 0xa00e40: LoadField: r1 = r0->field_33
    //     0xa00e40: ldur            w1, [x0, #0x33]
    // 0xa00e44: DecompressPointer r1
    //     0xa00e44: add             x1, x1, HEAP, lsl #32
    // 0xa00e48: cmp             w1, NULL
    // 0xa00e4c: b.ne            #0xa00e88
    // 0xa00e50: mov             x1, x0
    // 0xa00e54: ldur            x2, [fp, #-8]
    // 0xa00e58: r0 = _start()
    //     0xa00e58: bl              #0xa00ea4  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_start
    // 0xa00e5c: b               #0xa00e8c
    // 0xa00e60: cmp             x1, #0xaa1
    // 0xa00e64: b.ne            #0xa00e88
    // 0xa00e68: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa00e68: ldur            w1, [x2, #0x17]
    // 0xa00e6c: DecompressPointer r1
    //     0xa00e6c: add             x1, x1, HEAP, lsl #32
    // 0xa00e70: cmp             w1, NULL
    // 0xa00e74: b.eq            #0xa00e88
    // 0xa00e78: LoadField: r1 = r0->field_b
    //     0xa00e78: ldur            w1, [x0, #0xb]
    // 0xa00e7c: DecompressPointer r1
    //     0xa00e7c: add             x1, x1, HEAP, lsl #32
    // 0xa00e80: cmp             w1, NULL
    // 0xa00e84: b.eq            #0xa00ea0
    // 0xa00e88: r0 = false
    //     0xa00e88: add             x0, NULL, #0x30  ; false
    // 0xa00e8c: LeaveFrame
    //     0xa00e8c: mov             SP, fp
    //     0xa00e90: ldp             fp, lr, [SP], #0x10
    // 0xa00e94: ret
    //     0xa00e94: ret             
    // 0xa00e98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa00e98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa00e9c: b               #0xa00db0
    // 0xa00ea0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00ea0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _start(/* No info */) {
    // ** addr: 0xa00ea4, size: 0xd0
    // 0xa00ea4: EnterFrame
    //     0xa00ea4: stp             fp, lr, [SP, #-0x10]!
    //     0xa00ea8: mov             fp, SP
    // 0xa00eac: AllocStack(0x8)
    //     0xa00eac: sub             SP, SP, #8
    // 0xa00eb0: SetupParameters(RefreshIndicatorState this /* r1 => r0, fp-0x8 */)
    //     0xa00eb0: mov             x0, x1
    //     0xa00eb4: stur            x1, [fp, #-8]
    // 0xa00eb8: CheckStackOverflow
    //     0xa00eb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa00ebc: cmp             SP, x16
    //     0xa00ec0: b.ls            #0xa00f54
    // 0xa00ec4: LoadField: r1 = r2->field_7
    //     0xa00ec4: ldur            x1, [x2, #7]
    // 0xa00ec8: cmp             x1, #1
    // 0xa00ecc: b.gt            #0xa00edc
    // 0xa00ed0: cmp             x1, #0
    // 0xa00ed4: b.gt            #0xa00f40
    // 0xa00ed8: b               #0xa00ee4
    // 0xa00edc: cmp             x1, #2
    // 0xa00ee0: b.gt            #0xa00f40
    // 0xa00ee4: r2 = true
    //     0xa00ee4: add             x2, NULL, #0x20  ; true
    // 0xa00ee8: r1 = 0.000000
    //     0xa00ee8: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xa00eec: StoreField: r0->field_37 = r2
    //     0xa00eec: stur            w2, [x0, #0x37]
    // 0xa00ef0: StoreField: r0->field_3b = r1
    //     0xa00ef0: stur            w1, [x0, #0x3b]
    // 0xa00ef4: LoadField: r1 = r0->field_1f
    //     0xa00ef4: ldur            w1, [x0, #0x1f]
    // 0xa00ef8: DecompressPointer r1
    //     0xa00ef8: add             x1, x1, HEAP, lsl #32
    // 0xa00efc: r16 = Sentinel
    //     0xa00efc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa00f00: cmp             w1, w16
    // 0xa00f04: b.eq            #0xa00f5c
    // 0xa00f08: d0 = 0.000000
    //     0xa00f08: eor             v0.16b, v0.16b, v0.16b
    // 0xa00f0c: r0 = value=()
    //     0xa00f0c: bl              #0x6567c4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::value=
    // 0xa00f10: ldur            x0, [fp, #-8]
    // 0xa00f14: LoadField: r1 = r0->field_1b
    //     0xa00f14: ldur            w1, [x0, #0x1b]
    // 0xa00f18: DecompressPointer r1
    //     0xa00f18: add             x1, x1, HEAP, lsl #32
    // 0xa00f1c: r16 = Sentinel
    //     0xa00f1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa00f20: cmp             w1, w16
    // 0xa00f24: b.eq            #0xa00f68
    // 0xa00f28: d0 = 0.000000
    //     0xa00f28: eor             v0.16b, v0.16b, v0.16b
    // 0xa00f2c: r0 = value=()
    //     0xa00f2c: bl              #0x6567c4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::value=
    // 0xa00f30: r0 = true
    //     0xa00f30: add             x0, NULL, #0x20  ; true
    // 0xa00f34: LeaveFrame
    //     0xa00f34: mov             SP, fp
    //     0xa00f38: ldp             fp, lr, [SP], #0x10
    // 0xa00f3c: ret
    //     0xa00f3c: ret             
    // 0xa00f40: StoreField: r0->field_37 = rNULL
    //     0xa00f40: stur            NULL, [x0, #0x37]
    // 0xa00f44: r0 = false
    //     0xa00f44: add             x0, NULL, #0x30  ; false
    // 0xa00f48: LeaveFrame
    //     0xa00f48: mov             SP, fp
    //     0xa00f4c: ldp             fp, lr, [SP], #0x10
    // 0xa00f50: ret
    //     0xa00f50: ret             
    // 0xa00f54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa00f54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa00f58: b               #0xa00ec4
    // 0xa00f5c: r9 = _scaleController
    //     0xa00f5c: add             x9, PP, #0x44, lsl #12  ; [pp+0x441d8] Field <RefreshIndicatorState._scaleController@587083489>: late (offset: 0x20)
    //     0xa00f60: ldr             x9, [x9, #0x1d8]
    // 0xa00f64: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa00f64: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa00f68: r9 = _positionController
    //     0xa00f68: add             x9, PP, #0x44, lsl #12  ; [pp+0x44158] Field <RefreshIndicatorState._positionController@587083489>: late (offset: 0x1c)
    //     0xa00f6c: ldr             x9, [x9, #0x158]
    // 0xa00f70: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa00f70: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa00f74, size: 0x44
    // 0xa00f74: r1 = Instance_RefreshIndicatorStatus
    //     0xa00f74: add             x1, PP, #0x44, lsl #12  ; [pp+0x44188] Obj!RefreshIndicatorStatus@e36441
    //     0xa00f78: ldr             x1, [x1, #0x188]
    // 0xa00f7c: ldr             x2, [SP]
    // 0xa00f80: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa00f80: ldur            w3, [x2, #0x17]
    // 0xa00f84: DecompressPointer r3
    //     0xa00f84: add             x3, x3, HEAP, lsl #32
    // 0xa00f88: LoadField: r2 = r3->field_f
    //     0xa00f88: ldur            w2, [x3, #0xf]
    // 0xa00f8c: DecompressPointer r2
    //     0xa00f8c: add             x2, x2, HEAP, lsl #32
    // 0xa00f90: StoreField: r2->field_33 = r1
    //     0xa00f90: stur            w1, [x2, #0x33]
    // 0xa00f94: LoadField: r1 = r2->field_b
    //     0xa00f94: ldur            w1, [x2, #0xb]
    // 0xa00f98: DecompressPointer r1
    //     0xa00f98: add             x1, x1, HEAP, lsl #32
    // 0xa00f9c: cmp             w1, NULL
    // 0xa00fa0: b.eq            #0xa00fac
    // 0xa00fa4: r0 = Null
    //     0xa00fa4: mov             x0, NULL
    // 0xa00fa8: ret
    //     0xa00fa8: ret             
    // 0xa00fac: EnterFrame
    //     0xa00fac: stp             fp, lr, [SP, #-0x10]!
    //     0xa00fb0: mov             fp, SP
    // 0xa00fb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa00fb4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool _handleIndicatorNotification(dynamic, OverscrollIndicatorNotification) {
    // ** addr: 0xa00fb8, size: 0x3c
    // 0xa00fb8: EnterFrame
    //     0xa00fb8: stp             fp, lr, [SP, #-0x10]!
    //     0xa00fbc: mov             fp, SP
    // 0xa00fc0: ldr             x0, [fp, #0x18]
    // 0xa00fc4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa00fc4: ldur            w1, [x0, #0x17]
    // 0xa00fc8: DecompressPointer r1
    //     0xa00fc8: add             x1, x1, HEAP, lsl #32
    // 0xa00fcc: CheckStackOverflow
    //     0xa00fcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa00fd0: cmp             SP, x16
    //     0xa00fd4: b.ls            #0xa00fec
    // 0xa00fd8: ldr             x2, [fp, #0x10]
    // 0xa00fdc: r0 = _handleIndicatorNotification()
    //     0xa00fdc: bl              #0xa00ff4  ; [package:flutter/src/material/refresh_indicator.dart] RefreshIndicatorState::_handleIndicatorNotification
    // 0xa00fe0: LeaveFrame
    //     0xa00fe0: mov             SP, fp
    //     0xa00fe4: ldp             fp, lr, [SP], #0x10
    // 0xa00fe8: ret
    //     0xa00fe8: ret             
    // 0xa00fec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa00fec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa00ff0: b               #0xa00fd8
  }
  _ _handleIndicatorNotification(/* No info */) {
    // ** addr: 0xa00ff4, size: 0x84
    // 0xa00ff4: EnterFrame
    //     0xa00ff4: stp             fp, lr, [SP, #-0x10]!
    //     0xa00ff8: mov             fp, SP
    // 0xa00ffc: mov             x0, x1
    // 0xa01000: mov             x1, x2
    // 0xa01004: CheckStackOverflow
    //     0xa01004: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa01008: cmp             SP, x16
    //     0xa0100c: b.ls            #0xa01070
    // 0xa01010: LoadField: r2 = r1->field_7
    //     0xa01010: ldur            x2, [x1, #7]
    // 0xa01014: cbnz            x2, #0xa01024
    // 0xa01018: LoadField: r2 = r1->field_f
    //     0xa01018: ldur            w2, [x1, #0xf]
    // 0xa0101c: DecompressPointer r2
    //     0xa0101c: add             x2, x2, HEAP, lsl #32
    // 0xa01020: tbz             w2, #4, #0xa01034
    // 0xa01024: r0 = false
    //     0xa01024: add             x0, NULL, #0x30  ; false
    // 0xa01028: LeaveFrame
    //     0xa01028: mov             SP, fp
    //     0xa0102c: ldp             fp, lr, [SP], #0x10
    // 0xa01030: ret
    //     0xa01030: ret             
    // 0xa01034: LoadField: r2 = r0->field_33
    //     0xa01034: ldur            w2, [x0, #0x33]
    // 0xa01038: DecompressPointer r2
    //     0xa01038: add             x2, x2, HEAP, lsl #32
    // 0xa0103c: r16 = Instance_RefreshIndicatorStatus
    //     0xa0103c: add             x16, PP, #0x44, lsl #12  ; [pp+0x44188] Obj!RefreshIndicatorStatus@e36441
    //     0xa01040: ldr             x16, [x16, #0x188]
    // 0xa01044: cmp             w2, w16
    // 0xa01048: b.ne            #0xa01060
    // 0xa0104c: r0 = disallowIndicator()
    //     0xa0104c: bl              #0x995e14  ; [package:flutter/src/widgets/overscroll_indicator.dart] OverscrollIndicatorNotification::disallowIndicator
    // 0xa01050: r0 = true
    //     0xa01050: add             x0, NULL, #0x20  ; true
    // 0xa01054: LeaveFrame
    //     0xa01054: mov             SP, fp
    //     0xa01058: ldp             fp, lr, [SP], #0x10
    // 0xa0105c: ret
    //     0xa0105c: ret             
    // 0xa01060: r0 = false
    //     0xa01060: add             x0, NULL, #0x30  ; false
    // 0xa01064: LeaveFrame
    //     0xa01064: mov             SP, fp
    //     0xa01068: ldp             fp, lr, [SP], #0x10
    // 0xa0106c: ret
    //     0xa0106c: ret             
    // 0xa01070: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa01070: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa01074: b               #0xa01010
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7e2c4, size: 0x8c
    // 0xa7e2c4: EnterFrame
    //     0xa7e2c4: stp             fp, lr, [SP, #-0x10]!
    //     0xa7e2c8: mov             fp, SP
    // 0xa7e2cc: AllocStack(0x8)
    //     0xa7e2cc: sub             SP, SP, #8
    // 0xa7e2d0: SetupParameters(RefreshIndicatorState this /* r1 => r0, fp-0x8 */)
    //     0xa7e2d0: mov             x0, x1
    //     0xa7e2d4: stur            x1, [fp, #-8]
    // 0xa7e2d8: CheckStackOverflow
    //     0xa7e2d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7e2dc: cmp             SP, x16
    //     0xa7e2e0: b.ls            #0xa7e330
    // 0xa7e2e4: LoadField: r1 = r0->field_1b
    //     0xa7e2e4: ldur            w1, [x0, #0x1b]
    // 0xa7e2e8: DecompressPointer r1
    //     0xa7e2e8: add             x1, x1, HEAP, lsl #32
    // 0xa7e2ec: r16 = Sentinel
    //     0xa7e2ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7e2f0: cmp             w1, w16
    // 0xa7e2f4: b.eq            #0xa7e338
    // 0xa7e2f8: r0 = dispose()
    //     0xa7e2f8: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa7e2fc: ldur            x0, [fp, #-8]
    // 0xa7e300: LoadField: r1 = r0->field_1f
    //     0xa7e300: ldur            w1, [x0, #0x1f]
    // 0xa7e304: DecompressPointer r1
    //     0xa7e304: add             x1, x1, HEAP, lsl #32
    // 0xa7e308: r16 = Sentinel
    //     0xa7e308: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7e30c: cmp             w1, w16
    // 0xa7e310: b.eq            #0xa7e344
    // 0xa7e314: r0 = dispose()
    //     0xa7e314: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa7e318: ldur            x1, [fp, #-8]
    // 0xa7e31c: r0 = dispose()
    //     0xa7e31c: bl              #0xa7e350  ; [package:flutter/src/material/refresh_indicator.dart] _RefreshIndicatorState&State&TickerProviderStateMixin::dispose
    // 0xa7e320: r0 = Null
    //     0xa7e320: mov             x0, NULL
    // 0xa7e324: LeaveFrame
    //     0xa7e324: mov             SP, fp
    //     0xa7e328: ldp             fp, lr, [SP], #0x10
    // 0xa7e32c: ret
    //     0xa7e32c: ret             
    // 0xa7e330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7e330: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7e334: b               #0xa7e2e4
    // 0xa7e338: r9 = _positionController
    //     0xa7e338: add             x9, PP, #0x44, lsl #12  ; [pp+0x44158] Field <RefreshIndicatorState._positionController@587083489>: late (offset: 0x1c)
    //     0xa7e33c: ldr             x9, [x9, #0x158]
    // 0xa7e340: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa7e340: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa7e344: r9 = _scaleController
    //     0xa7e344: add             x9, PP, #0x44, lsl #12  ; [pp+0x441d8] Field <RefreshIndicatorState._scaleController@587083489>: late (offset: 0x20)
    //     0xa7e348: ldr             x9, [x9, #0x1d8]
    // 0xa7e34c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa7e34c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4821, size: 0x54, field offset: 0xc
//   const constructor, 
class RefreshIndicator extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa90780, size: 0x44
    // 0xa90780: EnterFrame
    //     0xa90780: stp             fp, lr, [SP, #-0x10]!
    //     0xa90784: mov             fp, SP
    // 0xa90788: mov             x0, x1
    // 0xa9078c: r1 = <RefreshIndicator>
    //     0xa9078c: add             x1, PP, #0x39, lsl #12  ; [pp+0x39768] TypeArguments: <RefreshIndicator>
    //     0xa90790: ldr             x1, [x1, #0x768]
    // 0xa90794: r0 = RefreshIndicatorState()
    //     0xa90794: bl              #0xa907c4  ; AllocateRefreshIndicatorStateStub -> RefreshIndicatorState (size=0x44)
    // 0xa90798: r1 = Sentinel
    //     0xa90798: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9079c: StoreField: r0->field_1b = r1
    //     0xa9079c: stur            w1, [x0, #0x1b]
    // 0xa907a0: StoreField: r0->field_1f = r1
    //     0xa907a0: stur            w1, [x0, #0x1f]
    // 0xa907a4: StoreField: r0->field_23 = r1
    //     0xa907a4: stur            w1, [x0, #0x23]
    // 0xa907a8: StoreField: r0->field_27 = r1
    //     0xa907a8: stur            w1, [x0, #0x27]
    // 0xa907ac: StoreField: r0->field_2b = r1
    //     0xa907ac: stur            w1, [x0, #0x2b]
    // 0xa907b0: StoreField: r0->field_2f = r1
    //     0xa907b0: stur            w1, [x0, #0x2f]
    // 0xa907b4: StoreField: r0->field_3f = r1
    //     0xa907b4: stur            w1, [x0, #0x3f]
    // 0xa907b8: LeaveFrame
    //     0xa907b8: mov             SP, fp
    //     0xa907bc: ldp             fp, lr, [SP], #0x10
    // 0xa907c0: ret
    //     0xa907c0: ret             
  }
}

// class id: 7046, size: 0x14, field offset: 0x14
enum _IndicatorType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc491d4, size: 0x64
    // 0xc491d4: EnterFrame
    //     0xc491d4: stp             fp, lr, [SP, #-0x10]!
    //     0xc491d8: mov             fp, SP
    // 0xc491dc: AllocStack(0x10)
    //     0xc491dc: sub             SP, SP, #0x10
    // 0xc491e0: SetupParameters(_IndicatorType this /* r1 => r0, fp-0x8 */)
    //     0xc491e0: mov             x0, x1
    //     0xc491e4: stur            x1, [fp, #-8]
    // 0xc491e8: CheckStackOverflow
    //     0xc491e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc491ec: cmp             SP, x16
    //     0xc491f0: b.ls            #0xc49230
    // 0xc491f4: r1 = Null
    //     0xc491f4: mov             x1, NULL
    // 0xc491f8: r2 = 4
    //     0xc491f8: movz            x2, #0x4
    // 0xc491fc: r0 = AllocateArray()
    //     0xc491fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc49200: r16 = "_IndicatorType."
    //     0xc49200: add             x16, PP, #0x39, lsl #12  ; [pp+0x39770] "_IndicatorType."
    //     0xc49204: ldr             x16, [x16, #0x770]
    // 0xc49208: StoreField: r0->field_f = r16
    //     0xc49208: stur            w16, [x0, #0xf]
    // 0xc4920c: ldur            x1, [fp, #-8]
    // 0xc49210: LoadField: r2 = r1->field_f
    //     0xc49210: ldur            w2, [x1, #0xf]
    // 0xc49214: DecompressPointer r2
    //     0xc49214: add             x2, x2, HEAP, lsl #32
    // 0xc49218: StoreField: r0->field_13 = r2
    //     0xc49218: stur            w2, [x0, #0x13]
    // 0xc4921c: str             x0, [SP]
    // 0xc49220: r0 = _interpolate()
    //     0xc49220: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc49224: LeaveFrame
    //     0xc49224: mov             SP, fp
    //     0xc49228: ldp             fp, lr, [SP], #0x10
    // 0xc4922c: ret
    //     0xc4922c: ret             
    // 0xc49230: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc49230: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc49234: b               #0xc491f4
  }
}

// class id: 7047, size: 0x14, field offset: 0x14
enum RefreshIndicatorTriggerMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc49170, size: 0x64
    // 0xc49170: EnterFrame
    //     0xc49170: stp             fp, lr, [SP, #-0x10]!
    //     0xc49174: mov             fp, SP
    // 0xc49178: AllocStack(0x10)
    //     0xc49178: sub             SP, SP, #0x10
    // 0xc4917c: SetupParameters(RefreshIndicatorTriggerMode this /* r1 => r0, fp-0x8 */)
    //     0xc4917c: mov             x0, x1
    //     0xc49180: stur            x1, [fp, #-8]
    // 0xc49184: CheckStackOverflow
    //     0xc49184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc49188: cmp             SP, x16
    //     0xc4918c: b.ls            #0xc491cc
    // 0xc49190: r1 = Null
    //     0xc49190: mov             x1, NULL
    // 0xc49194: r2 = 4
    //     0xc49194: movz            x2, #0x4
    // 0xc49198: r0 = AllocateArray()
    //     0xc49198: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4919c: r16 = "RefreshIndicatorTriggerMode."
    //     0xc4919c: add             x16, PP, #0x39, lsl #12  ; [pp+0x39778] "RefreshIndicatorTriggerMode."
    //     0xc491a0: ldr             x16, [x16, #0x778]
    // 0xc491a4: StoreField: r0->field_f = r16
    //     0xc491a4: stur            w16, [x0, #0xf]
    // 0xc491a8: ldur            x1, [fp, #-8]
    // 0xc491ac: LoadField: r2 = r1->field_f
    //     0xc491ac: ldur            w2, [x1, #0xf]
    // 0xc491b0: DecompressPointer r2
    //     0xc491b0: add             x2, x2, HEAP, lsl #32
    // 0xc491b4: StoreField: r0->field_13 = r2
    //     0xc491b4: stur            w2, [x0, #0x13]
    // 0xc491b8: str             x0, [SP]
    // 0xc491bc: r0 = _interpolate()
    //     0xc491bc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc491c0: LeaveFrame
    //     0xc491c0: mov             SP, fp
    //     0xc491c4: ldp             fp, lr, [SP], #0x10
    // 0xc491c8: ret
    //     0xc491c8: ret             
    // 0xc491cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc491cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc491d0: b               #0xc49190
  }
}

// class id: 7048, size: 0x14, field offset: 0x14
enum RefreshIndicatorStatus extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4910c, size: 0x64
    // 0xc4910c: EnterFrame
    //     0xc4910c: stp             fp, lr, [SP, #-0x10]!
    //     0xc49110: mov             fp, SP
    // 0xc49114: AllocStack(0x10)
    //     0xc49114: sub             SP, SP, #0x10
    // 0xc49118: SetupParameters(RefreshIndicatorStatus this /* r1 => r0, fp-0x8 */)
    //     0xc49118: mov             x0, x1
    //     0xc4911c: stur            x1, [fp, #-8]
    // 0xc49120: CheckStackOverflow
    //     0xc49120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc49124: cmp             SP, x16
    //     0xc49128: b.ls            #0xc49168
    // 0xc4912c: r1 = Null
    //     0xc4912c: mov             x1, NULL
    // 0xc49130: r2 = 4
    //     0xc49130: movz            x2, #0x4
    // 0xc49134: r0 = AllocateArray()
    //     0xc49134: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc49138: r16 = "RefreshIndicatorStatus."
    //     0xc49138: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4dab8] "RefreshIndicatorStatus."
    //     0xc4913c: ldr             x16, [x16, #0xab8]
    // 0xc49140: StoreField: r0->field_f = r16
    //     0xc49140: stur            w16, [x0, #0xf]
    // 0xc49144: ldur            x1, [fp, #-8]
    // 0xc49148: LoadField: r2 = r1->field_f
    //     0xc49148: ldur            w2, [x1, #0xf]
    // 0xc4914c: DecompressPointer r2
    //     0xc4914c: add             x2, x2, HEAP, lsl #32
    // 0xc49150: StoreField: r0->field_13 = r2
    //     0xc49150: stur            w2, [x0, #0x13]
    // 0xc49154: str             x0, [SP]
    // 0xc49158: r0 = _interpolate()
    //     0xc49158: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4915c: LeaveFrame
    //     0xc4915c: mov             SP, fp
    //     0xc49160: ldp             fp, lr, [SP], #0x10
    // 0xc49164: ret
    //     0xc49164: ret             
    // 0xc49168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc49168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4916c: b               #0xc4912c
  }
}
