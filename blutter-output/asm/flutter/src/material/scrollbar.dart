// lib: , url: package:flutter/src/material/scrollbar.dart

// class id: 1048941, size: 0x8
class :: {
}

// class id: 4348, size: 0x70, field offset: 0x58
class _MaterialScrollbarState extends RawScrollbarState<dynamic> {

  late AnimationController _hoverAnimationController; // offset: 0x58
  late ScrollbarThemeData _scrollbarTheme; // offset: 0x68
  late bool _useAndroidScrollbar; // offset: 0x6c
  late ColorScheme _colorScheme; // offset: 0x64

  _ initState(/* No info */) {
    // ** addr: 0x92f6b8, size: 0xc0
    // 0x92f6b8: EnterFrame
    //     0x92f6b8: stp             fp, lr, [SP, #-0x10]!
    //     0x92f6bc: mov             fp, SP
    // 0x92f6c0: AllocStack(0x20)
    //     0x92f6c0: sub             SP, SP, #0x20
    // 0x92f6c4: SetupParameters(_MaterialScrollbarState this /* r1 => r1, fp-0x8 */)
    //     0x92f6c4: stur            x1, [fp, #-8]
    // 0x92f6c8: CheckStackOverflow
    //     0x92f6c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92f6cc: cmp             SP, x16
    //     0x92f6d0: b.ls            #0x92f770
    // 0x92f6d4: r1 = 1
    //     0x92f6d4: movz            x1, #0x1
    // 0x92f6d8: r0 = AllocateContext()
    //     0x92f6d8: bl              #0xec126c  ; AllocateContextStub
    // 0x92f6dc: mov             x2, x0
    // 0x92f6e0: ldur            x0, [fp, #-8]
    // 0x92f6e4: stur            x2, [fp, #-0x10]
    // 0x92f6e8: StoreField: r2->field_f = r0
    //     0x92f6e8: stur            w0, [x2, #0xf]
    // 0x92f6ec: mov             x1, x0
    // 0x92f6f0: r0 = initState()
    //     0x92f6f0: bl              #0x92f7e4  ; [package:flutter/src/widgets/scrollbar.dart] RawScrollbarState::initState
    // 0x92f6f4: r1 = <double>
    //     0x92f6f4: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x92f6f8: r0 = AnimationController()
    //     0x92f6f8: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x92f6fc: stur            x0, [fp, #-0x18]
    // 0x92f700: r16 = Instance_Duration
    //     0x92f700: add             x16, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0x92f704: ldr             x16, [x16, #0x368]
    // 0x92f708: str             x16, [SP]
    // 0x92f70c: mov             x1, x0
    // 0x92f710: ldur            x2, [fp, #-8]
    // 0x92f714: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x92f714: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x92f718: ldr             x4, [x4, #0x408]
    // 0x92f71c: r0 = AnimationController()
    //     0x92f71c: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x92f720: ldur            x0, [fp, #-0x18]
    // 0x92f724: ldur            x1, [fp, #-8]
    // 0x92f728: StoreField: r1->field_57 = r0
    //     0x92f728: stur            w0, [x1, #0x57]
    //     0x92f72c: ldurb           w16, [x1, #-1]
    //     0x92f730: ldurb           w17, [x0, #-1]
    //     0x92f734: and             x16, x17, x16, lsr #2
    //     0x92f738: tst             x16, HEAP, lsr #32
    //     0x92f73c: b.eq            #0x92f744
    //     0x92f740: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x92f744: ldur            x2, [fp, #-0x10]
    // 0x92f748: r1 = Function '<anonymous closure>':.
    //     0x92f748: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d998] AnonymousClosure: (0x92f79c), in [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::initState (0x92f6b8)
    //     0x92f74c: ldr             x1, [x1, #0x998]
    // 0x92f750: r0 = AllocateClosure()
    //     0x92f750: bl              #0xec1630  ; AllocateClosureStub
    // 0x92f754: ldur            x1, [fp, #-0x18]
    // 0x92f758: mov             x2, x0
    // 0x92f75c: r0 = addActionListener()
    //     0x92f75c: bl              #0xc680d4  ; [package:flutter/src/widgets/actions.dart] Action::addActionListener
    // 0x92f760: r0 = Null
    //     0x92f760: mov             x0, NULL
    // 0x92f764: LeaveFrame
    //     0x92f764: mov             SP, fp
    //     0x92f768: ldp             fp, lr, [SP], #0x10
    // 0x92f76c: ret
    //     0x92f76c: ret             
    // 0x92f770: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92f770: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92f774: b               #0x92f6d4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x92f79c, size: 0x48
    // 0x92f79c: EnterFrame
    //     0x92f79c: stp             fp, lr, [SP, #-0x10]!
    //     0x92f7a0: mov             fp, SP
    // 0x92f7a4: ldr             x0, [fp, #0x10]
    // 0x92f7a8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x92f7a8: ldur            w1, [x0, #0x17]
    // 0x92f7ac: DecompressPointer r1
    //     0x92f7ac: add             x1, x1, HEAP, lsl #32
    // 0x92f7b0: CheckStackOverflow
    //     0x92f7b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92f7b4: cmp             SP, x16
    //     0x92f7b8: b.ls            #0x92f7dc
    // 0x92f7bc: LoadField: r0 = r1->field_f
    //     0x92f7bc: ldur            w0, [x1, #0xf]
    // 0x92f7c0: DecompressPointer r0
    //     0x92f7c0: add             x0, x0, HEAP, lsl #32
    // 0x92f7c4: mov             x1, x0
    // 0x92f7c8: r0 = updateScrollbarPainter()
    //     0x92f7c8: bl              #0xd30044  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::updateScrollbarPainter
    // 0x92f7cc: r0 = Null
    //     0x92f7cc: mov             x0, NULL
    // 0x92f7d0: LeaveFrame
    //     0x92f7d0: mov             SP, fp
    //     0x92f7d4: ldp             fp, lr, [SP], #0x10
    // 0x92f7d8: ret
    //     0x92f7d8: ret             
    // 0x92f7dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92f7dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92f7e0: b               #0x92f7bc
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9a2bc8, size: 0xf4
    // 0x9a2bc8: EnterFrame
    //     0x9a2bc8: stp             fp, lr, [SP, #-0x10]!
    //     0x9a2bcc: mov             fp, SP
    // 0x9a2bd0: AllocStack(0x10)
    //     0x9a2bd0: sub             SP, SP, #0x10
    // 0x9a2bd4: SetupParameters(_MaterialScrollbarState this /* r1 => r0, fp-0x8 */)
    //     0x9a2bd4: mov             x0, x1
    //     0x9a2bd8: stur            x1, [fp, #-8]
    // 0x9a2bdc: CheckStackOverflow
    //     0x9a2bdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a2be0: cmp             SP, x16
    //     0x9a2be4: b.ls            #0x9a2cac
    // 0x9a2be8: LoadField: r1 = r0->field_f
    //     0x9a2be8: ldur            w1, [x0, #0xf]
    // 0x9a2bec: DecompressPointer r1
    //     0x9a2bec: add             x1, x1, HEAP, lsl #32
    // 0x9a2bf0: cmp             w1, NULL
    // 0x9a2bf4: b.eq            #0x9a2cb4
    // 0x9a2bf8: r0 = of()
    //     0x9a2bf8: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9a2bfc: mov             x2, x0
    // 0x9a2c00: stur            x2, [fp, #-0x10]
    // 0x9a2c04: LoadField: r0 = r2->field_3f
    //     0x9a2c04: ldur            w0, [x2, #0x3f]
    // 0x9a2c08: DecompressPointer r0
    //     0x9a2c08: add             x0, x0, HEAP, lsl #32
    // 0x9a2c0c: ldur            x3, [fp, #-8]
    // 0x9a2c10: StoreField: r3->field_63 = r0
    //     0x9a2c10: stur            w0, [x3, #0x63]
    //     0x9a2c14: ldurb           w16, [x3, #-1]
    //     0x9a2c18: ldurb           w17, [x0, #-1]
    //     0x9a2c1c: and             x16, x17, x16, lsr #2
    //     0x9a2c20: tst             x16, HEAP, lsr #32
    //     0x9a2c24: b.eq            #0x9a2c2c
    //     0x9a2c28: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x9a2c2c: LoadField: r1 = r3->field_f
    //     0x9a2c2c: ldur            w1, [x3, #0xf]
    // 0x9a2c30: DecompressPointer r1
    //     0x9a2c30: add             x1, x1, HEAP, lsl #32
    // 0x9a2c34: cmp             w1, NULL
    // 0x9a2c38: b.eq            #0x9a2cb8
    // 0x9a2c3c: r0 = of()
    //     0x9a2c3c: bl              #0x9a2cbc  ; [package:flutter/src/material/scrollbar_theme.dart] ScrollbarTheme::of
    // 0x9a2c40: ldur            x1, [fp, #-8]
    // 0x9a2c44: StoreField: r1->field_67 = r0
    //     0x9a2c44: stur            w0, [x1, #0x67]
    //     0x9a2c48: ldurb           w16, [x1, #-1]
    //     0x9a2c4c: ldurb           w17, [x0, #-1]
    //     0x9a2c50: and             x16, x17, x16, lsr #2
    //     0x9a2c54: tst             x16, HEAP, lsr #32
    //     0x9a2c58: b.eq            #0x9a2c60
    //     0x9a2c5c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9a2c60: ldur            x2, [fp, #-0x10]
    // 0x9a2c64: LoadField: r3 = r2->field_23
    //     0x9a2c64: ldur            w3, [x2, #0x23]
    // 0x9a2c68: DecompressPointer r3
    //     0x9a2c68: add             x3, x3, HEAP, lsl #32
    // 0x9a2c6c: LoadField: r2 = r3->field_7
    //     0x9a2c6c: ldur            x2, [x3, #7]
    // 0x9a2c70: cmp             x2, #2
    // 0x9a2c74: b.gt            #0x9a2c94
    // 0x9a2c78: cmp             x2, #1
    // 0x9a2c7c: b.gt            #0x9a2c94
    // 0x9a2c80: cmp             x2, #0
    // 0x9a2c84: b.gt            #0x9a2c94
    // 0x9a2c88: r2 = true
    //     0x9a2c88: add             x2, NULL, #0x20  ; true
    // 0x9a2c8c: StoreField: r1->field_6b = r2
    //     0x9a2c8c: stur            w2, [x1, #0x6b]
    // 0x9a2c90: b               #0x9a2c9c
    // 0x9a2c94: r2 = false
    //     0x9a2c94: add             x2, NULL, #0x30  ; false
    // 0x9a2c98: StoreField: r1->field_6b = r2
    //     0x9a2c98: stur            w2, [x1, #0x6b]
    // 0x9a2c9c: r0 = Null
    //     0x9a2c9c: mov             x0, NULL
    // 0x9a2ca0: LeaveFrame
    //     0x9a2ca0: mov             SP, fp
    //     0x9a2ca4: ldp             fp, lr, [SP], #0x10
    // 0x9a2ca8: ret
    //     0x9a2ca8: ret             
    // 0x9a2cac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a2cac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a2cb0: b               #0x9a2be8
    // 0x9a2cb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a2cb4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a2cb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a2cb8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9dbd94, size: 0x24
    // 0x9dbd94: r1 = false
    //     0x9dbd94: add             x1, NULL, #0x30  ; false
    // 0x9dbd98: ldr             x2, [SP]
    // 0x9dbd9c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9dbd9c: ldur            w3, [x2, #0x17]
    // 0x9dbda0: DecompressPointer r3
    //     0x9dbda0: add             x3, x3, HEAP, lsl #32
    // 0x9dbda4: LoadField: r2 = r3->field_f
    //     0x9dbda4: ldur            w2, [x3, #0xf]
    // 0x9dbda8: DecompressPointer r2
    //     0x9dbda8: add             x2, x2, HEAP, lsl #32
    // 0x9dbdac: StoreField: r2->field_5b = r1
    //     0x9dbdac: stur            w1, [x2, #0x5b]
    // 0x9dbdb0: r0 = Null
    //     0x9dbdb0: mov             x0, NULL
    // 0x9dbdb4: ret
    //     0x9dbdb4: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9dc858, size: 0x24
    // 0x9dc858: r1 = true
    //     0x9dc858: add             x1, NULL, #0x20  ; true
    // 0x9dc85c: ldr             x2, [SP]
    // 0x9dc860: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9dc860: ldur            w3, [x2, #0x17]
    // 0x9dc864: DecompressPointer r3
    //     0x9dc864: add             x3, x3, HEAP, lsl #32
    // 0x9dc868: LoadField: r2 = r3->field_f
    //     0x9dc868: ldur            w2, [x3, #0xf]
    // 0x9dc86c: DecompressPointer r2
    //     0x9dc86c: add             x2, x2, HEAP, lsl #32
    // 0x9dc870: StoreField: r2->field_5b = r1
    //     0x9dc870: stur            w1, [x2, #0x5b]
    // 0x9dc874: r0 = Null
    //     0x9dc874: mov             x0, NULL
    // 0x9dc878: ret
    //     0x9dc878: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9df4fc, size: 0x24
    // 0x9df4fc: r1 = false
    //     0x9df4fc: add             x1, NULL, #0x30  ; false
    // 0x9df500: ldr             x2, [SP]
    // 0x9df504: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9df504: ldur            w3, [x2, #0x17]
    // 0x9df508: DecompressPointer r3
    //     0x9df508: add             x3, x3, HEAP, lsl #32
    // 0x9df50c: LoadField: r2 = r3->field_f
    //     0x9df50c: ldur            w2, [x3, #0xf]
    // 0x9df510: DecompressPointer r2
    //     0x9df510: add             x2, x2, HEAP, lsl #32
    // 0x9df514: StoreField: r2->field_5f = r1
    //     0x9df514: stur            w1, [x2, #0x5f]
    // 0x9df518: r0 = Null
    //     0x9df518: mov             x0, NULL
    // 0x9df51c: ret
    //     0x9df51c: ret             
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7b538, size: 0x64
    // 0xa7b538: EnterFrame
    //     0xa7b538: stp             fp, lr, [SP, #-0x10]!
    //     0xa7b53c: mov             fp, SP
    // 0xa7b540: AllocStack(0x8)
    //     0xa7b540: sub             SP, SP, #8
    // 0xa7b544: SetupParameters(_MaterialScrollbarState this /* r1 => r0, fp-0x8 */)
    //     0xa7b544: mov             x0, x1
    //     0xa7b548: stur            x1, [fp, #-8]
    // 0xa7b54c: CheckStackOverflow
    //     0xa7b54c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7b550: cmp             SP, x16
    //     0xa7b554: b.ls            #0xa7b588
    // 0xa7b558: LoadField: r1 = r0->field_57
    //     0xa7b558: ldur            w1, [x0, #0x57]
    // 0xa7b55c: DecompressPointer r1
    //     0xa7b55c: add             x1, x1, HEAP, lsl #32
    // 0xa7b560: r16 = Sentinel
    //     0xa7b560: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7b564: cmp             w1, w16
    // 0xa7b568: b.eq            #0xa7b590
    // 0xa7b56c: r0 = dispose()
    //     0xa7b56c: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa7b570: ldur            x1, [fp, #-8]
    // 0xa7b574: r0 = dispose()
    //     0xa7b574: bl              #0xa7b59c  ; [package:flutter/src/widgets/scrollbar.dart] RawScrollbarState::dispose
    // 0xa7b578: r0 = Null
    //     0xa7b578: mov             x0, NULL
    // 0xa7b57c: LeaveFrame
    //     0xa7b57c: mov             SP, fp
    //     0xa7b580: ldp             fp, lr, [SP], #0x10
    // 0xa7b584: ret
    //     0xa7b584: ret             
    // 0xa7b588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7b588: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7b58c: b               #0xa7b558
    // 0xa7b590: r9 = _hoverAnimationController
    //     0xa7b590: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d988] Field <_MaterialScrollbarState@590083257._hoverAnimationController@590083257>: late (offset: 0x58)
    //     0xa7b594: ldr             x9, [x9, #0x988]
    // 0xa7b598: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa7b598: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _MaterialScrollbarState(/* No info */) {
    // ** addr: 0xa8f21c, size: 0x50
    // 0xa8f21c: EnterFrame
    //     0xa8f21c: stp             fp, lr, [SP, #-0x10]!
    //     0xa8f220: mov             fp, SP
    // 0xa8f224: r2 = Sentinel
    //     0xa8f224: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa8f228: r0 = false
    //     0xa8f228: add             x0, NULL, #0x30  ; false
    // 0xa8f22c: CheckStackOverflow
    //     0xa8f22c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8f230: cmp             SP, x16
    //     0xa8f234: b.ls            #0xa8f264
    // 0xa8f238: StoreField: r1->field_57 = r2
    //     0xa8f238: stur            w2, [x1, #0x57]
    // 0xa8f23c: StoreField: r1->field_5b = r0
    //     0xa8f23c: stur            w0, [x1, #0x5b]
    // 0xa8f240: StoreField: r1->field_5f = r0
    //     0xa8f240: stur            w0, [x1, #0x5f]
    // 0xa8f244: StoreField: r1->field_63 = r2
    //     0xa8f244: stur            w2, [x1, #0x63]
    // 0xa8f248: StoreField: r1->field_67 = r2
    //     0xa8f248: stur            w2, [x1, #0x67]
    // 0xa8f24c: StoreField: r1->field_6b = r2
    //     0xa8f24c: stur            w2, [x1, #0x6b]
    // 0xa8f250: r0 = RawScrollbarState()
    //     0xa8f250: bl              #0xa8f26c  ; [package:flutter/src/widgets/scrollbar.dart] RawScrollbarState::RawScrollbarState
    // 0xa8f254: r0 = Null
    //     0xa8f254: mov             x0, NULL
    // 0xa8f258: LeaveFrame
    //     0xa8f258: mov             SP, fp
    //     0xa8f25c: ldp             fp, lr, [SP], #0x10
    // 0xa8f260: ret
    //     0xa8f260: ret             
    // 0xa8f264: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8f264: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8f268: b               #0xa8f238
  }
  _ handleHover(/* No info */) {
    // ** addr: 0xd2d98c, size: 0x15c
    // 0xd2d98c: EnterFrame
    //     0xd2d98c: stp             fp, lr, [SP, #-0x10]!
    //     0xd2d990: mov             fp, SP
    // 0xd2d994: AllocStack(0x20)
    //     0xd2d994: sub             SP, SP, #0x20
    // 0xd2d998: SetupParameters(_MaterialScrollbarState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xd2d998: stur            x1, [fp, #-8]
    //     0xd2d99c: stur            x2, [fp, #-0x10]
    // 0xd2d9a0: CheckStackOverflow
    //     0xd2d9a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2d9a4: cmp             SP, x16
    //     0xd2d9a8: b.ls            #0xd2dac8
    // 0xd2d9ac: r1 = 1
    //     0xd2d9ac: movz            x1, #0x1
    // 0xd2d9b0: r0 = AllocateContext()
    //     0xd2d9b0: bl              #0xec126c  ; AllocateContextStub
    // 0xd2d9b4: mov             x3, x0
    // 0xd2d9b8: ldur            x0, [fp, #-8]
    // 0xd2d9bc: stur            x3, [fp, #-0x18]
    // 0xd2d9c0: StoreField: r3->field_f = r0
    //     0xd2d9c0: stur            w0, [x3, #0xf]
    // 0xd2d9c4: mov             x1, x0
    // 0xd2d9c8: ldur            x2, [fp, #-0x10]
    // 0xd2d9cc: r0 = handleHover()
    //     0xd2d9cc: bl              #0xd2dbd0  ; [package:flutter/src/widgets/scrollbar.dart] RawScrollbarState::handleHover
    // 0xd2d9d0: ldur            x2, [fp, #-0x10]
    // 0xd2d9d4: r0 = LoadClassIdInstr(r2)
    //     0xd2d9d4: ldur            x0, [x2, #-1]
    //     0xd2d9d8: ubfx            x0, x0, #0xc, #0x14
    // 0xd2d9dc: mov             x1, x2
    // 0xd2d9e0: r0 = GDT[cid_x0 + -0x1]()
    //     0xd2d9e0: sub             lr, x0, #1
    //     0xd2d9e4: ldr             lr, [x21, lr, lsl #3]
    //     0xd2d9e8: blr             lr
    // 0xd2d9ec: mov             x2, x0
    // 0xd2d9f0: ldur            x1, [fp, #-0x10]
    // 0xd2d9f4: stur            x2, [fp, #-0x20]
    // 0xd2d9f8: r0 = LoadClassIdInstr(r1)
    //     0xd2d9f8: ldur            x0, [x1, #-1]
    //     0xd2d9fc: ubfx            x0, x0, #0xc, #0x14
    // 0xd2da00: r0 = GDT[cid_x0 + 0x130b7]()
    //     0xd2da00: movz            x17, #0x30b7
    //     0xd2da04: movk            x17, #0x1, lsl #16
    //     0xd2da08: add             lr, x0, x17
    //     0xd2da0c: ldr             lr, [x21, lr, lsl #3]
    //     0xd2da10: blr             lr
    // 0xd2da14: ldur            x1, [fp, #-8]
    // 0xd2da18: ldur            x2, [fp, #-0x20]
    // 0xd2da1c: mov             x3, x0
    // 0xd2da20: r0 = isPointerOverScrollbar()
    //     0xd2da20: bl              #0xd2dae8  ; [package:flutter/src/widgets/scrollbar.dart] RawScrollbarState::isPointerOverScrollbar
    // 0xd2da24: tbnz            w0, #4, #0xd2da6c
    // 0xd2da28: ldur            x0, [fp, #-8]
    // 0xd2da2c: ldur            x2, [fp, #-0x18]
    // 0xd2da30: r1 = Function '<anonymous closure>':.
    //     0xd2da30: add             x1, PP, #0x54, lsl #12  ; [pp+0x548e0] AnonymousClosure: (0xd2dbac), in [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::handleHover (0xd2d98c)
    //     0xd2da34: ldr             x1, [x1, #0x8e0]
    // 0xd2da38: r0 = AllocateClosure()
    //     0xd2da38: bl              #0xec1630  ; AllocateClosureStub
    // 0xd2da3c: ldur            x1, [fp, #-8]
    // 0xd2da40: mov             x2, x0
    // 0xd2da44: r0 = setState()
    //     0xd2da44: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xd2da48: ldur            x0, [fp, #-8]
    // 0xd2da4c: LoadField: r1 = r0->field_57
    //     0xd2da4c: ldur            w1, [x0, #0x57]
    // 0xd2da50: DecompressPointer r1
    //     0xd2da50: add             x1, x1, HEAP, lsl #32
    // 0xd2da54: r16 = Sentinel
    //     0xd2da54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd2da58: cmp             w1, w16
    // 0xd2da5c: b.eq            #0xd2dad0
    // 0xd2da60: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xd2da60: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xd2da64: r0 = forward()
    //     0xd2da64: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0xd2da68: b               #0xd2dab8
    // 0xd2da6c: ldur            x0, [fp, #-8]
    // 0xd2da70: LoadField: r1 = r0->field_5f
    //     0xd2da70: ldur            w1, [x0, #0x5f]
    // 0xd2da74: DecompressPointer r1
    //     0xd2da74: add             x1, x1, HEAP, lsl #32
    // 0xd2da78: tbnz            w1, #4, #0xd2dab8
    // 0xd2da7c: ldur            x2, [fp, #-0x18]
    // 0xd2da80: r1 = Function '<anonymous closure>':.
    //     0xd2da80: add             x1, PP, #0x54, lsl #12  ; [pp+0x548e8] AnonymousClosure: (0x9df4fc), of [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState
    //     0xd2da84: ldr             x1, [x1, #0x8e8]
    // 0xd2da88: r0 = AllocateClosure()
    //     0xd2da88: bl              #0xec1630  ; AllocateClosureStub
    // 0xd2da8c: ldur            x1, [fp, #-8]
    // 0xd2da90: mov             x2, x0
    // 0xd2da94: r0 = setState()
    //     0xd2da94: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xd2da98: ldur            x0, [fp, #-8]
    // 0xd2da9c: LoadField: r1 = r0->field_57
    //     0xd2da9c: ldur            w1, [x0, #0x57]
    // 0xd2daa0: DecompressPointer r1
    //     0xd2daa0: add             x1, x1, HEAP, lsl #32
    // 0xd2daa4: r16 = Sentinel
    //     0xd2daa4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd2daa8: cmp             w1, w16
    // 0xd2daac: b.eq            #0xd2dadc
    // 0xd2dab0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xd2dab0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xd2dab4: r0 = reverse()
    //     0xd2dab4: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0xd2dab8: r0 = Null
    //     0xd2dab8: mov             x0, NULL
    // 0xd2dabc: LeaveFrame
    //     0xd2dabc: mov             SP, fp
    //     0xd2dac0: ldp             fp, lr, [SP], #0x10
    // 0xd2dac4: ret
    //     0xd2dac4: ret             
    // 0xd2dac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd2dac8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd2dacc: b               #0xd2d9ac
    // 0xd2dad0: r9 = _hoverAnimationController
    //     0xd2dad0: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d988] Field <_MaterialScrollbarState@590083257._hoverAnimationController@590083257>: late (offset: 0x58)
    //     0xd2dad4: ldr             x9, [x9, #0x988]
    // 0xd2dad8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd2dad8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd2dadc: r9 = _hoverAnimationController
    //     0xd2dadc: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d988] Field <_MaterialScrollbarState@590083257._hoverAnimationController@590083257>: late (offset: 0x58)
    //     0xd2dae0: ldr             x9, [x9, #0x988]
    // 0xd2dae4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd2dae4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xd2dbac, size: 0x24
    // 0xd2dbac: r1 = true
    //     0xd2dbac: add             x1, NULL, #0x20  ; true
    // 0xd2dbb0: ldr             x2, [SP]
    // 0xd2dbb4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xd2dbb4: ldur            w3, [x2, #0x17]
    // 0xd2dbb8: DecompressPointer r3
    //     0xd2dbb8: add             x3, x3, HEAP, lsl #32
    // 0xd2dbbc: LoadField: r2 = r3->field_f
    //     0xd2dbbc: ldur            w2, [x3, #0xf]
    // 0xd2dbc0: DecompressPointer r2
    //     0xd2dbc0: add             x2, x2, HEAP, lsl #32
    // 0xd2dbc4: StoreField: r2->field_5f = r1
    //     0xd2dbc4: stur            w1, [x2, #0x5f]
    // 0xd2dbc8: r0 = Null
    //     0xd2dbc8: mov             x0, NULL
    // 0xd2dbcc: ret
    //     0xd2dbcc: ret             
  }
  _ handleThumbPressEnd(/* No info */) {
    // ** addr: 0xd2dcd0, size: 0x80
    // 0xd2dcd0: EnterFrame
    //     0xd2dcd0: stp             fp, lr, [SP, #-0x10]!
    //     0xd2dcd4: mov             fp, SP
    // 0xd2dcd8: AllocStack(0x20)
    //     0xd2dcd8: sub             SP, SP, #0x20
    // 0xd2dcdc: SetupParameters(_MaterialScrollbarState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xd2dcdc: stur            x1, [fp, #-8]
    //     0xd2dce0: stur            x2, [fp, #-0x10]
    //     0xd2dce4: stur            x3, [fp, #-0x18]
    // 0xd2dce8: CheckStackOverflow
    //     0xd2dce8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2dcec: cmp             SP, x16
    //     0xd2dcf0: b.ls            #0xd2dd48
    // 0xd2dcf4: r1 = 1
    //     0xd2dcf4: movz            x1, #0x1
    // 0xd2dcf8: r0 = AllocateContext()
    //     0xd2dcf8: bl              #0xec126c  ; AllocateContextStub
    // 0xd2dcfc: mov             x4, x0
    // 0xd2dd00: ldur            x0, [fp, #-8]
    // 0xd2dd04: stur            x4, [fp, #-0x20]
    // 0xd2dd08: StoreField: r4->field_f = r0
    //     0xd2dd08: stur            w0, [x4, #0xf]
    // 0xd2dd0c: mov             x1, x0
    // 0xd2dd10: ldur            x2, [fp, #-0x10]
    // 0xd2dd14: ldur            x3, [fp, #-0x18]
    // 0xd2dd18: r0 = handleThumbPressEnd()
    //     0xd2dd18: bl              #0xd2dd50  ; [package:flutter/src/widgets/scrollbar.dart] RawScrollbarState::handleThumbPressEnd
    // 0xd2dd1c: ldur            x2, [fp, #-0x20]
    // 0xd2dd20: r1 = Function '<anonymous closure>':.
    //     0xd2dd20: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4f978] AnonymousClosure: (0x9dbd94), in [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::handleThumbPressEnd (0xd2dcd0)
    //     0xd2dd24: ldr             x1, [x1, #0x978]
    // 0xd2dd28: r0 = AllocateClosure()
    //     0xd2dd28: bl              #0xec1630  ; AllocateClosureStub
    // 0xd2dd2c: ldur            x1, [fp, #-8]
    // 0xd2dd30: mov             x2, x0
    // 0xd2dd34: r0 = setState()
    //     0xd2dd34: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xd2dd38: r0 = Null
    //     0xd2dd38: mov             x0, NULL
    // 0xd2dd3c: LeaveFrame
    //     0xd2dd3c: mov             SP, fp
    //     0xd2dd40: ldp             fp, lr, [SP], #0x10
    // 0xd2dd44: ret
    //     0xd2dd44: ret             
    // 0xd2dd48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd2dd48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd2dd4c: b               #0xd2dcf4
  }
  _ handleThumbPressStart(/* No info */) {
    // ** addr: 0xd2fa90, size: 0x78
    // 0xd2fa90: EnterFrame
    //     0xd2fa90: stp             fp, lr, [SP, #-0x10]!
    //     0xd2fa94: mov             fp, SP
    // 0xd2fa98: AllocStack(0x18)
    //     0xd2fa98: sub             SP, SP, #0x18
    // 0xd2fa9c: SetupParameters(_MaterialScrollbarState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xd2fa9c: stur            x1, [fp, #-8]
    //     0xd2faa0: stur            x2, [fp, #-0x10]
    // 0xd2faa4: CheckStackOverflow
    //     0xd2faa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2faa8: cmp             SP, x16
    //     0xd2faac: b.ls            #0xd2fb00
    // 0xd2fab0: r1 = 1
    //     0xd2fab0: movz            x1, #0x1
    // 0xd2fab4: r0 = AllocateContext()
    //     0xd2fab4: bl              #0xec126c  ; AllocateContextStub
    // 0xd2fab8: mov             x3, x0
    // 0xd2fabc: ldur            x0, [fp, #-8]
    // 0xd2fac0: stur            x3, [fp, #-0x18]
    // 0xd2fac4: StoreField: r3->field_f = r0
    //     0xd2fac4: stur            w0, [x3, #0xf]
    // 0xd2fac8: mov             x1, x0
    // 0xd2facc: ldur            x2, [fp, #-0x10]
    // 0xd2fad0: r0 = handleThumbPressStart()
    //     0xd2fad0: bl              #0xd2fb08  ; [package:flutter/src/widgets/scrollbar.dart] RawScrollbarState::handleThumbPressStart
    // 0xd2fad4: ldur            x2, [fp, #-0x18]
    // 0xd2fad8: r1 = Function '<anonymous closure>':.
    //     0xd2fad8: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4f9b8] AnonymousClosure: (0x9dc858), in [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::handleThumbPressStart (0xd2fa90)
    //     0xd2fadc: ldr             x1, [x1, #0x9b8]
    // 0xd2fae0: r0 = AllocateClosure()
    //     0xd2fae0: bl              #0xec1630  ; AllocateClosureStub
    // 0xd2fae4: ldur            x1, [fp, #-8]
    // 0xd2fae8: mov             x2, x0
    // 0xd2faec: r0 = setState()
    //     0xd2faec: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xd2faf0: r0 = Null
    //     0xd2faf0: mov             x0, NULL
    // 0xd2faf4: LeaveFrame
    //     0xd2faf4: mov             SP, fp
    //     0xd2faf8: ldp             fp, lr, [SP], #0x10
    // 0xd2fafc: ret
    //     0xd2fafc: ret             
    // 0xd2fb00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd2fb00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd2fb04: b               #0xd2fab0
  }
  _ updateScrollbarPainter(/* No info */) {
    // ** addr: 0xd30044, size: 0x30c
    // 0xd30044: EnterFrame
    //     0xd30044: stp             fp, lr, [SP, #-0x10]!
    //     0xd30048: mov             fp, SP
    // 0xd3004c: AllocStack(0x18)
    //     0xd3004c: sub             SP, SP, #0x18
    // 0xd30050: SetupParameters(_MaterialScrollbarState this /* r1 => r0, fp-0x10 */)
    //     0xd30050: mov             x0, x1
    //     0xd30054: stur            x1, [fp, #-0x10]
    // 0xd30058: CheckStackOverflow
    //     0xd30058: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3005c: cmp             SP, x16
    //     0xd30060: b.ls            #0xd302fc
    // 0xd30064: LoadField: r2 = r0->field_53
    //     0xd30064: ldur            w2, [x0, #0x53]
    // 0xd30068: DecompressPointer r2
    //     0xd30068: add             x2, x2, HEAP, lsl #32
    // 0xd3006c: r16 = Sentinel
    //     0xd3006c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd30070: cmp             w2, w16
    // 0xd30074: b.eq            #0xd30304
    // 0xd30078: mov             x1, x0
    // 0xd3007c: stur            x2, [fp, #-8]
    // 0xd30080: r0 = _thumbColor()
    //     0xd30080: bl              #0xd30e18  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_thumbColor
    // 0xd30084: ldur            x1, [fp, #-0x10]
    // 0xd30088: stur            x0, [fp, #-0x18]
    // 0xd3008c: r0 = _states()
    //     0xd3008c: bl              #0xd30d34  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_states
    // 0xd30090: ldur            x1, [fp, #-0x18]
    // 0xd30094: mov             x2, x0
    // 0xd30098: r0 = resolve()
    //     0xd30098: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xd3009c: ldur            x1, [fp, #-8]
    // 0xd300a0: mov             x2, x0
    // 0xd300a4: r0 = color=()
    //     0xd300a4: bl              #0xd30c90  ; [package:flutter/src/widgets/scrollbar.dart] ScrollbarPainter::color=
    // 0xd300a8: ldur            x1, [fp, #-0x10]
    // 0xd300ac: r0 = _trackColor()
    //     0xd300ac: bl              #0xd30bfc  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_trackColor
    // 0xd300b0: ldur            x1, [fp, #-0x10]
    // 0xd300b4: stur            x0, [fp, #-0x18]
    // 0xd300b8: r0 = _states()
    //     0xd300b8: bl              #0xd30d34  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_states
    // 0xd300bc: ldur            x1, [fp, #-0x18]
    // 0xd300c0: mov             x2, x0
    // 0xd300c4: r0 = resolve()
    //     0xd300c4: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xd300c8: ldur            x1, [fp, #-8]
    // 0xd300cc: mov             x2, x0
    // 0xd300d0: r0 = trackColor=()
    //     0xd300d0: bl              #0xd30b58  ; [package:flutter/src/widgets/scrollbar.dart] ScrollbarPainter::trackColor=
    // 0xd300d4: ldur            x1, [fp, #-0x10]
    // 0xd300d8: r0 = _trackBorderColor()
    //     0xd300d8: bl              #0xd30a7c  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_trackBorderColor
    // 0xd300dc: ldur            x1, [fp, #-0x10]
    // 0xd300e0: stur            x0, [fp, #-0x18]
    // 0xd300e4: r0 = _states()
    //     0xd300e4: bl              #0xd30d34  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_states
    // 0xd300e8: ldur            x1, [fp, #-0x18]
    // 0xd300ec: mov             x2, x0
    // 0xd300f0: r0 = resolve()
    //     0xd300f0: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xd300f4: ldur            x1, [fp, #-8]
    // 0xd300f8: mov             x2, x0
    // 0xd300fc: r0 = trackBorderColor=()
    //     0xd300fc: bl              #0xd309d8  ; [package:flutter/src/widgets/scrollbar.dart] ScrollbarPainter::trackBorderColor=
    // 0xd30100: ldur            x0, [fp, #-0x10]
    // 0xd30104: LoadField: r1 = r0->field_f
    //     0xd30104: ldur            w1, [x0, #0xf]
    // 0xd30108: DecompressPointer r1
    //     0xd30108: add             x1, x1, HEAP, lsl #32
    // 0xd3010c: cmp             w1, NULL
    // 0xd30110: b.eq            #0xd30310
    // 0xd30114: r0 = of()
    //     0xd30114: bl              #0x6b0ae8  ; [package:flutter/src/widgets/basic.dart] Directionality::of
    // 0xd30118: ldur            x1, [fp, #-8]
    // 0xd3011c: mov             x2, x0
    // 0xd30120: r0 = textDirection=()
    //     0xd30120: bl              #0xd30968  ; [package:flutter/src/widgets/scrollbar.dart] ScrollbarPainter::textDirection=
    // 0xd30124: ldur            x1, [fp, #-0x10]
    // 0xd30128: r0 = _thickness()
    //     0xd30128: bl              #0xd30610  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_thickness
    // 0xd3012c: ldur            x1, [fp, #-0x10]
    // 0xd30130: stur            x0, [fp, #-0x18]
    // 0xd30134: r0 = _states()
    //     0xd30134: bl              #0xd30d34  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_states
    // 0xd30138: ldur            x1, [fp, #-0x18]
    // 0xd3013c: mov             x2, x0
    // 0xd30140: r0 = resolve()
    //     0xd30140: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xd30144: LoadField: d0 = r0->field_7
    //     0xd30144: ldur            d0, [x0, #7]
    // 0xd30148: ldur            x1, [fp, #-8]
    // 0xd3014c: r0 = thickness=()
    //     0xd3014c: bl              #0xd305c0  ; [package:flutter/src/widgets/scrollbar.dart] ScrollbarPainter::thickness=
    // 0xd30150: ldur            x0, [fp, #-0x10]
    // 0xd30154: LoadField: r1 = r0->field_b
    //     0xd30154: ldur            w1, [x0, #0xb]
    // 0xd30158: DecompressPointer r1
    //     0xd30158: add             x1, x1, HEAP, lsl #32
    // 0xd3015c: cmp             w1, NULL
    // 0xd30160: b.eq            #0xd30314
    // 0xd30164: LoadField: r2 = r1->field_1b
    //     0xd30164: ldur            w2, [x1, #0x1b]
    // 0xd30168: DecompressPointer r2
    //     0xd30168: add             x2, x2, HEAP, lsl #32
    // 0xd3016c: cmp             w2, NULL
    // 0xd30170: b.ne            #0xd30190
    // 0xd30174: LoadField: r1 = r0->field_67
    //     0xd30174: ldur            w1, [x0, #0x67]
    // 0xd30178: DecompressPointer r1
    //     0xd30178: add             x1, x1, HEAP, lsl #32
    // 0xd3017c: r16 = Sentinel
    //     0xd3017c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd30180: cmp             w1, w16
    // 0xd30184: b.eq            #0xd30318
    // 0xd30188: r1 = Null
    //     0xd30188: mov             x1, NULL
    // 0xd3018c: b               #0xd30194
    // 0xd30190: mov             x1, x2
    // 0xd30194: cmp             w1, NULL
    // 0xd30198: b.ne            #0xd301cc
    // 0xd3019c: LoadField: r1 = r0->field_6b
    //     0xd3019c: ldur            w1, [x0, #0x6b]
    // 0xd301a0: DecompressPointer r1
    //     0xd301a0: add             x1, x1, HEAP, lsl #32
    // 0xd301a4: r16 = Sentinel
    //     0xd301a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd301a8: cmp             w1, w16
    // 0xd301ac: b.eq            #0xd30324
    // 0xd301b0: tbnz            w1, #4, #0xd301bc
    // 0xd301b4: r1 = Null
    //     0xd301b4: mov             x1, NULL
    // 0xd301b8: b               #0xd301c4
    // 0xd301bc: r1 = Instance_Radius
    //     0xd301bc: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d9a0] Obj!Radius@e2bd31
    //     0xd301c0: ldr             x1, [x1, #0x9a0]
    // 0xd301c4: mov             x2, x1
    // 0xd301c8: b               #0xd301d0
    // 0xd301cc: mov             x2, x1
    // 0xd301d0: ldur            x1, [fp, #-8]
    // 0xd301d4: r0 = radius=()
    //     0xd301d4: bl              #0xd3051c  ; [package:flutter/src/widgets/scrollbar.dart] ScrollbarPainter::radius=
    // 0xd301d8: ldur            x0, [fp, #-0x10]
    // 0xd301dc: LoadField: r1 = r0->field_67
    //     0xd301dc: ldur            w1, [x0, #0x67]
    // 0xd301e0: DecompressPointer r1
    //     0xd301e0: add             x1, x1, HEAP, lsl #32
    // 0xd301e4: r16 = Sentinel
    //     0xd301e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd301e8: cmp             w1, w16
    // 0xd301ec: b.eq            #0xd30330
    // 0xd301f0: LoadField: r2 = r1->field_27
    //     0xd301f0: ldur            w2, [x1, #0x27]
    // 0xd301f4: DecompressPointer r2
    //     0xd301f4: add             x2, x2, HEAP, lsl #32
    // 0xd301f8: cmp             w2, NULL
    // 0xd301fc: b.ne            #0xd30228
    // 0xd30200: LoadField: r1 = r0->field_6b
    //     0xd30200: ldur            w1, [x0, #0x6b]
    // 0xd30204: DecompressPointer r1
    //     0xd30204: add             x1, x1, HEAP, lsl #32
    // 0xd30208: r16 = Sentinel
    //     0xd30208: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd3020c: cmp             w1, w16
    // 0xd30210: b.eq            #0xd3033c
    // 0xd30214: tbnz            w1, #4, #0xd30220
    // 0xd30218: d0 = 0.000000
    //     0xd30218: eor             v0.16b, v0.16b, v0.16b
    // 0xd3021c: b               #0xd3022c
    // 0xd30220: d0 = 2.000000
    //     0xd30220: fmov            d0, #2.00000000
    // 0xd30224: b               #0xd3022c
    // 0xd30228: LoadField: d0 = r2->field_7
    //     0xd30228: ldur            d0, [x2, #7]
    // 0xd3022c: ldur            x1, [fp, #-8]
    // 0xd30230: r0 = crossAxisMargin=()
    //     0xd30230: bl              #0xd304cc  ; [package:flutter/src/widgets/scrollbar.dart] ScrollbarPainter::crossAxisMargin=
    // 0xd30234: ldur            x0, [fp, #-0x10]
    // 0xd30238: LoadField: r1 = r0->field_67
    //     0xd30238: ldur            w1, [x0, #0x67]
    // 0xd3023c: DecompressPointer r1
    //     0xd3023c: add             x1, x1, HEAP, lsl #32
    // 0xd30240: LoadField: r2 = r1->field_2b
    //     0xd30240: ldur            w2, [x1, #0x2b]
    // 0xd30244: DecompressPointer r2
    //     0xd30244: add             x2, x2, HEAP, lsl #32
    // 0xd30248: cmp             w2, NULL
    // 0xd3024c: b.ne            #0xd30258
    // 0xd30250: d0 = 0.000000
    //     0xd30250: eor             v0.16b, v0.16b, v0.16b
    // 0xd30254: b               #0xd3025c
    // 0xd30258: LoadField: d0 = r2->field_7
    //     0xd30258: ldur            d0, [x2, #7]
    // 0xd3025c: ldur            x1, [fp, #-8]
    // 0xd30260: r0 = mainAxisMargin=()
    //     0xd30260: bl              #0xd3047c  ; [package:flutter/src/widgets/scrollbar.dart] ScrollbarPainter::mainAxisMargin=
    // 0xd30264: ldur            x0, [fp, #-0x10]
    // 0xd30268: LoadField: r1 = r0->field_67
    //     0xd30268: ldur            w1, [x0, #0x67]
    // 0xd3026c: DecompressPointer r1
    //     0xd3026c: add             x1, x1, HEAP, lsl #32
    // 0xd30270: LoadField: r2 = r1->field_2f
    //     0xd30270: ldur            w2, [x1, #0x2f]
    // 0xd30274: DecompressPointer r2
    //     0xd30274: add             x2, x2, HEAP, lsl #32
    // 0xd30278: cmp             w2, NULL
    // 0xd3027c: b.ne            #0xd30288
    // 0xd30280: d0 = 48.000000
    //     0xd30280: ldr             d0, [PP, #0x6e10]  ; [pp+0x6e10] IMM: double(48) from 0x4048000000000000
    // 0xd30284: b               #0xd3028c
    // 0xd30288: LoadField: d0 = r2->field_7
    //     0xd30288: ldur            d0, [x2, #7]
    // 0xd3028c: ldur            x1, [fp, #-8]
    // 0xd30290: r0 = minLength=()
    //     0xd30290: bl              #0xd3042c  ; [package:flutter/src/widgets/scrollbar.dart] ScrollbarPainter::minLength=
    // 0xd30294: ldur            x0, [fp, #-0x10]
    // 0xd30298: LoadField: r1 = r0->field_f
    //     0xd30298: ldur            w1, [x0, #0xf]
    // 0xd3029c: DecompressPointer r1
    //     0xd3029c: add             x1, x1, HEAP, lsl #32
    // 0xd302a0: cmp             w1, NULL
    // 0xd302a4: b.eq            #0xd30348
    // 0xd302a8: r0 = paddingOf()
    //     0xd302a8: bl              #0x9daabc  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::paddingOf
    // 0xd302ac: ldur            x1, [fp, #-8]
    // 0xd302b0: mov             x2, x0
    // 0xd302b4: r0 = padding=()
    //     0xd302b4: bl              #0xd303a4  ; [package:flutter/src/widgets/scrollbar.dart] ScrollbarPainter::padding=
    // 0xd302b8: ldur            x0, [fp, #-0x10]
    // 0xd302bc: LoadField: r1 = r0->field_b
    //     0xd302bc: ldur            w1, [x0, #0xb]
    // 0xd302c0: DecompressPointer r1
    //     0xd302c0: add             x1, x1, HEAP, lsl #32
    // 0xd302c4: cmp             w1, NULL
    // 0xd302c8: b.eq            #0xd3034c
    // 0xd302cc: ldur            x1, [fp, #-8]
    // 0xd302d0: r2 = Null
    //     0xd302d0: mov             x2, NULL
    // 0xd302d4: r0 = forceCompileTimeTreeShaking()
    //     0xd302d4: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xd302d8: ldur            x1, [fp, #-0x10]
    // 0xd302dc: r0 = enableGestures()
    //     0xd302dc: bl              #0xd7ddb0  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::enableGestures
    // 0xd302e0: eor             x2, x0, #0x10
    // 0xd302e4: ldur            x1, [fp, #-8]
    // 0xd302e8: r0 = ignorePointer=()
    //     0xd302e8: bl              #0xd30350  ; [package:flutter/src/widgets/scrollbar.dart] ScrollbarPainter::ignorePointer=
    // 0xd302ec: r0 = Null
    //     0xd302ec: mov             x0, NULL
    // 0xd302f0: LeaveFrame
    //     0xd302f0: mov             SP, fp
    //     0xd302f4: ldp             fp, lr, [SP], #0x10
    // 0xd302f8: ret
    //     0xd302f8: ret             
    // 0xd302fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd302fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd30300: b               #0xd30064
    // 0xd30304: r9 = scrollbarPainter
    //     0xd30304: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9a8] Field <RawScrollbarState.scrollbarPainter>: late final (offset: 0x54)
    //     0xd30308: ldr             x9, [x9, #0x9a8]
    // 0xd3030c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd3030c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd30310: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd30310: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd30314: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd30314: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd30318: r9 = _scrollbarTheme
    //     0xd30318: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b0] Field <_MaterialScrollbarState@590083257._scrollbarTheme@590083257>: late (offset: 0x68)
    //     0xd3031c: ldr             x9, [x9, #0x9b0]
    // 0xd30320: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd30320: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd30324: r9 = _useAndroidScrollbar
    //     0xd30324: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b8] Field <_MaterialScrollbarState@590083257._useAndroidScrollbar@590083257>: late (offset: 0x6c)
    //     0xd30328: ldr             x9, [x9, #0x9b8]
    // 0xd3032c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd3032c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd30330: r9 = _scrollbarTheme
    //     0xd30330: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b0] Field <_MaterialScrollbarState@590083257._scrollbarTheme@590083257>: late (offset: 0x68)
    //     0xd30334: ldr             x9, [x9, #0x9b0]
    // 0xd30338: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd30338: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd3033c: r9 = _useAndroidScrollbar
    //     0xd3033c: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b8] Field <_MaterialScrollbarState@590083257._useAndroidScrollbar@590083257>: late (offset: 0x6c)
    //     0xd30340: ldr             x9, [x9, #0x9b8]
    // 0xd30344: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd30344: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd30348: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd30348: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd3034c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd3034c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _thickness(/* No info */) {
    // ** addr: 0xd30610, size: 0x64
    // 0xd30610: EnterFrame
    //     0xd30610: stp             fp, lr, [SP, #-0x10]!
    //     0xd30614: mov             fp, SP
    // 0xd30618: AllocStack(0x18)
    //     0xd30618: sub             SP, SP, #0x18
    // 0xd3061c: SetupParameters(_MaterialScrollbarState this /* r1 => r1, fp-0x8 */)
    //     0xd3061c: stur            x1, [fp, #-8]
    // 0xd30620: CheckStackOverflow
    //     0xd30620: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd30624: cmp             SP, x16
    //     0xd30628: b.ls            #0xd3066c
    // 0xd3062c: r1 = 1
    //     0xd3062c: movz            x1, #0x1
    // 0xd30630: r0 = AllocateContext()
    //     0xd30630: bl              #0xec126c  ; AllocateContextStub
    // 0xd30634: mov             x1, x0
    // 0xd30638: ldur            x0, [fp, #-8]
    // 0xd3063c: StoreField: r1->field_f = r0
    //     0xd3063c: stur            w0, [x1, #0xf]
    // 0xd30640: mov             x2, x1
    // 0xd30644: r1 = Function '<anonymous closure>':.
    //     0xd30644: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d9c0] AnonymousClosure: (0xd30674), in [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_thickness (0xd30610)
    //     0xd30648: ldr             x1, [x1, #0x9c0]
    // 0xd3064c: r0 = AllocateClosure()
    //     0xd3064c: bl              #0xec1630  ; AllocateClosureStub
    // 0xd30650: r16 = <double>
    //     0xd30650: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xd30654: stp             x0, x16, [SP]
    // 0xd30658: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xd30658: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xd3065c: r0 = resolveWith()
    //     0xd3065c: bl              #0x9d84b0  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::resolveWith
    // 0xd30660: LeaveFrame
    //     0xd30660: mov             SP, fp
    //     0xd30664: ldp             fp, lr, [SP], #0x10
    // 0xd30668: ret
    //     0xd30668: ret             
    // 0xd3066c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3066c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd30670: b               #0xd3062c
  }
  [closure] double <anonymous closure>(dynamic, Set<WidgetState>) {
    // ** addr: 0xd30674, size: 0x230
    // 0xd30674: EnterFrame
    //     0xd30674: stp             fp, lr, [SP, #-0x10]!
    //     0xd30678: mov             fp, SP
    // 0xd3067c: AllocStack(0x8)
    //     0xd3067c: sub             SP, SP, #8
    // 0xd30680: SetupParameters()
    //     0xd30680: ldr             x0, [fp, #0x18]
    //     0xd30684: ldur            w3, [x0, #0x17]
    //     0xd30688: add             x3, x3, HEAP, lsl #32
    //     0xd3068c: stur            x3, [fp, #-8]
    // 0xd30690: CheckStackOverflow
    //     0xd30690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd30694: cmp             SP, x16
    //     0xd30698: b.ls            #0xd30850
    // 0xd3069c: ldr             x4, [fp, #0x10]
    // 0xd306a0: r0 = LoadClassIdInstr(r4)
    //     0xd306a0: ldur            x0, [x4, #-1]
    //     0xd306a4: ubfx            x0, x0, #0xc, #0x14
    // 0xd306a8: mov             x1, x4
    // 0xd306ac: r2 = Instance_WidgetState
    //     0xd306ac: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d0e0] Obj!WidgetState@e33861
    //     0xd306b0: ldr             x2, [x2, #0xe0]
    // 0xd306b4: r0 = GDT[cid_x0 + 0xf20c]()
    //     0xd306b4: movz            x17, #0xf20c
    //     0xd306b8: add             lr, x0, x17
    //     0xd306bc: ldr             lr, [x21, lr, lsl #3]
    //     0xd306c0: blr             lr
    // 0xd306c4: tbnz            w0, #4, #0xd30784
    // 0xd306c8: ldur            x0, [fp, #-8]
    // 0xd306cc: LoadField: r1 = r0->field_f
    //     0xd306cc: ldur            w1, [x0, #0xf]
    // 0xd306d0: DecompressPointer r1
    //     0xd306d0: add             x1, x1, HEAP, lsl #32
    // 0xd306d4: r0 = _trackVisibility()
    //     0xd306d4: bl              #0xd308a4  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_trackVisibility
    // 0xd306d8: mov             x1, x0
    // 0xd306dc: ldr             x2, [fp, #0x10]
    // 0xd306e0: r0 = resolve()
    //     0xd306e0: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xd306e4: tbnz            w0, #4, #0xd3077c
    // 0xd306e8: ldur            x1, [fp, #-8]
    // 0xd306ec: LoadField: r2 = r1->field_f
    //     0xd306ec: ldur            w2, [x1, #0xf]
    // 0xd306f0: DecompressPointer r2
    //     0xd306f0: add             x2, x2, HEAP, lsl #32
    // 0xd306f4: LoadField: r3 = r2->field_b
    //     0xd306f4: ldur            w3, [x2, #0xb]
    // 0xd306f8: DecompressPointer r3
    //     0xd306f8: add             x3, x3, HEAP, lsl #32
    // 0xd306fc: cmp             w3, NULL
    // 0xd30700: b.eq            #0xd30858
    // 0xd30704: LoadField: r4 = r3->field_1f
    //     0xd30704: ldur            w4, [x3, #0x1f]
    // 0xd30708: DecompressPointer r4
    //     0xd30708: add             x4, x4, HEAP, lsl #32
    // 0xd3070c: cmp             w4, NULL
    // 0xd30710: b.ne            #0xd30730
    // 0xd30714: LoadField: r3 = r2->field_67
    //     0xd30714: ldur            w3, [x2, #0x67]
    // 0xd30718: DecompressPointer r3
    //     0xd30718: add             x3, x3, HEAP, lsl #32
    // 0xd3071c: r16 = Sentinel
    //     0xd3071c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd30720: cmp             w3, w16
    // 0xd30724: b.eq            #0xd3085c
    // 0xd30728: r2 = Null
    //     0xd30728: mov             x2, NULL
    // 0xd3072c: b               #0xd30734
    // 0xd30730: mov             x2, x4
    // 0xd30734: cmp             w2, NULL
    // 0xd30738: b.ne            #0xd30744
    // 0xd3073c: d0 = 12.000000
    //     0xd3073c: fmov            d0, #12.00000000
    // 0xd30740: b               #0xd30748
    // 0xd30744: LoadField: d0 = r2->field_7
    //     0xd30744: ldur            d0, [x2, #7]
    // 0xd30748: r0 = inline_Allocate_Double()
    //     0xd30748: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xd3074c: add             x0, x0, #0x10
    //     0xd30750: cmp             x2, x0
    //     0xd30754: b.ls            #0xd30868
    //     0xd30758: str             x0, [THR, #0x50]  ; THR::top
    //     0xd3075c: sub             x0, x0, #0xf
    //     0xd30760: movz            x2, #0xe15c
    //     0xd30764: movk            x2, #0x3, lsl #16
    //     0xd30768: stur            x2, [x0, #-1]
    // 0xd3076c: StoreField: r0->field_7 = d0
    //     0xd3076c: stur            d0, [x0, #7]
    // 0xd30770: LeaveFrame
    //     0xd30770: mov             SP, fp
    //     0xd30774: ldp             fp, lr, [SP], #0x10
    // 0xd30778: ret
    //     0xd30778: ret             
    // 0xd3077c: ldur            x1, [fp, #-8]
    // 0xd30780: b               #0xd30788
    // 0xd30784: ldur            x1, [fp, #-8]
    // 0xd30788: LoadField: r2 = r1->field_f
    //     0xd30788: ldur            w2, [x1, #0xf]
    // 0xd3078c: DecompressPointer r2
    //     0xd3078c: add             x2, x2, HEAP, lsl #32
    // 0xd30790: LoadField: r1 = r2->field_b
    //     0xd30790: ldur            w1, [x2, #0xb]
    // 0xd30794: DecompressPointer r1
    //     0xd30794: add             x1, x1, HEAP, lsl #32
    // 0xd30798: cmp             w1, NULL
    // 0xd3079c: b.eq            #0xd30878
    // 0xd307a0: LoadField: r3 = r1->field_1f
    //     0xd307a0: ldur            w3, [x1, #0x1f]
    // 0xd307a4: DecompressPointer r3
    //     0xd307a4: add             x3, x3, HEAP, lsl #32
    // 0xd307a8: cmp             w3, NULL
    // 0xd307ac: b.ne            #0xd307cc
    // 0xd307b0: LoadField: r1 = r2->field_67
    //     0xd307b0: ldur            w1, [x2, #0x67]
    // 0xd307b4: DecompressPointer r1
    //     0xd307b4: add             x1, x1, HEAP, lsl #32
    // 0xd307b8: r16 = Sentinel
    //     0xd307b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd307bc: cmp             w1, w16
    // 0xd307c0: b.eq            #0xd3087c
    // 0xd307c4: r1 = Null
    //     0xd307c4: mov             x1, NULL
    // 0xd307c8: b               #0xd307d0
    // 0xd307cc: mov             x1, x3
    // 0xd307d0: cmp             w1, NULL
    // 0xd307d4: b.ne            #0xd30818
    // 0xd307d8: d0 = 8.000000
    //     0xd307d8: fmov            d0, #8.00000000
    // 0xd307dc: LoadField: r3 = r2->field_6b
    //     0xd307dc: ldur            w3, [x2, #0x6b]
    // 0xd307e0: DecompressPointer r3
    //     0xd307e0: add             x3, x3, HEAP, lsl #32
    // 0xd307e4: r16 = Sentinel
    //     0xd307e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd307e8: cmp             w3, w16
    // 0xd307ec: b.eq            #0xd30888
    // 0xd307f0: tst             x3, #0x10
    // 0xd307f4: cset            x2, ne
    // 0xd307f8: sub             x2, x2, #1
    // 0xd307fc: and             x2, x2, #2
    // 0xd30800: add             x2, x2, #2
    // 0xd30804: r16 = LoadInt32Instr(r2)
    //     0xd30804: sbfx            x16, x2, #1, #0x1f
    // 0xd30808: scvtf           d1, w16
    // 0xd3080c: fdiv            d2, d0, d1
    // 0xd30810: mov             v0.16b, v2.16b
    // 0xd30814: b               #0xd3081c
    // 0xd30818: LoadField: d0 = r1->field_7
    //     0xd30818: ldur            d0, [x1, #7]
    // 0xd3081c: r0 = inline_Allocate_Double()
    //     0xd3081c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xd30820: add             x0, x0, #0x10
    //     0xd30824: cmp             x1, x0
    //     0xd30828: b.ls            #0xd30894
    //     0xd3082c: str             x0, [THR, #0x50]  ; THR::top
    //     0xd30830: sub             x0, x0, #0xf
    //     0xd30834: movz            x1, #0xe15c
    //     0xd30838: movk            x1, #0x3, lsl #16
    //     0xd3083c: stur            x1, [x0, #-1]
    // 0xd30840: StoreField: r0->field_7 = d0
    //     0xd30840: stur            d0, [x0, #7]
    // 0xd30844: LeaveFrame
    //     0xd30844: mov             SP, fp
    //     0xd30848: ldp             fp, lr, [SP], #0x10
    // 0xd3084c: ret
    //     0xd3084c: ret             
    // 0xd30850: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd30850: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd30854: b               #0xd3069c
    // 0xd30858: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd30858: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd3085c: r9 = _scrollbarTheme
    //     0xd3085c: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b0] Field <_MaterialScrollbarState@590083257._scrollbarTheme@590083257>: late (offset: 0x68)
    //     0xd30860: ldr             x9, [x9, #0x9b0]
    // 0xd30864: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd30864: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd30868: SaveReg d0
    //     0xd30868: str             q0, [SP, #-0x10]!
    // 0xd3086c: r0 = AllocateDouble()
    //     0xd3086c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd30870: RestoreReg d0
    //     0xd30870: ldr             q0, [SP], #0x10
    // 0xd30874: b               #0xd3076c
    // 0xd30878: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd30878: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd3087c: r9 = _scrollbarTheme
    //     0xd3087c: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b0] Field <_MaterialScrollbarState@590083257._scrollbarTheme@590083257>: late (offset: 0x68)
    //     0xd30880: ldr             x9, [x9, #0x9b0]
    // 0xd30884: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd30884: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd30888: r9 = _useAndroidScrollbar
    //     0xd30888: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b8] Field <_MaterialScrollbarState@590083257._useAndroidScrollbar@590083257>: late (offset: 0x6c)
    //     0xd3088c: ldr             x9, [x9, #0x9b8]
    // 0xd30890: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xd30890: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xd30894: SaveReg d0
    //     0xd30894: str             q0, [SP, #-0x10]!
    // 0xd30898: r0 = AllocateDouble()
    //     0xd30898: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd3089c: RestoreReg d0
    //     0xd3089c: ldr             q0, [SP], #0x10
    // 0xd308a0: b               #0xd30840
  }
  get _ _trackVisibility(/* No info */) {
    // ** addr: 0xd308a4, size: 0x64
    // 0xd308a4: EnterFrame
    //     0xd308a4: stp             fp, lr, [SP, #-0x10]!
    //     0xd308a8: mov             fp, SP
    // 0xd308ac: AllocStack(0x18)
    //     0xd308ac: sub             SP, SP, #0x18
    // 0xd308b0: SetupParameters(_MaterialScrollbarState this /* r1 => r1, fp-0x8 */)
    //     0xd308b0: stur            x1, [fp, #-8]
    // 0xd308b4: CheckStackOverflow
    //     0xd308b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd308b8: cmp             SP, x16
    //     0xd308bc: b.ls            #0xd30900
    // 0xd308c0: r1 = 1
    //     0xd308c0: movz            x1, #0x1
    // 0xd308c4: r0 = AllocateContext()
    //     0xd308c4: bl              #0xec126c  ; AllocateContextStub
    // 0xd308c8: mov             x1, x0
    // 0xd308cc: ldur            x0, [fp, #-8]
    // 0xd308d0: StoreField: r1->field_f = r0
    //     0xd308d0: stur            w0, [x1, #0xf]
    // 0xd308d4: mov             x2, x1
    // 0xd308d8: r1 = Function '<anonymous closure>':.
    //     0xd308d8: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d9c8] AnonymousClosure: (0xd30908), in [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_trackVisibility (0xd308a4)
    //     0xd308dc: ldr             x1, [x1, #0x9c8]
    // 0xd308e0: r0 = AllocateClosure()
    //     0xd308e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xd308e4: r16 = <bool>
    //     0xd308e4: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xd308e8: stp             x0, x16, [SP]
    // 0xd308ec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xd308ec: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xd308f0: r0 = resolveWith()
    //     0xd308f0: bl              #0x9d84b0  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::resolveWith
    // 0xd308f4: LeaveFrame
    //     0xd308f4: mov             SP, fp
    //     0xd308f8: ldp             fp, lr, [SP], #0x10
    // 0xd308fc: ret
    //     0xd308fc: ret             
    // 0xd30900: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd30900: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd30904: b               #0xd308c0
  }
  [closure] bool <anonymous closure>(dynamic, Set<WidgetState>) {
    // ** addr: 0xd30908, size: 0x60
    // 0xd30908: EnterFrame
    //     0xd30908: stp             fp, lr, [SP, #-0x10]!
    //     0xd3090c: mov             fp, SP
    // 0xd30910: ldr             x1, [fp, #0x18]
    // 0xd30914: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xd30914: ldur            w2, [x1, #0x17]
    // 0xd30918: DecompressPointer r2
    //     0xd30918: add             x2, x2, HEAP, lsl #32
    // 0xd3091c: LoadField: r1 = r2->field_f
    //     0xd3091c: ldur            w1, [x2, #0xf]
    // 0xd30920: DecompressPointer r1
    //     0xd30920: add             x1, x1, HEAP, lsl #32
    // 0xd30924: LoadField: r2 = r1->field_b
    //     0xd30924: ldur            w2, [x1, #0xb]
    // 0xd30928: DecompressPointer r2
    //     0xd30928: add             x2, x2, HEAP, lsl #32
    // 0xd3092c: cmp             w2, NULL
    // 0xd30930: b.eq            #0xd30958
    // 0xd30934: LoadField: r2 = r1->field_67
    //     0xd30934: ldur            w2, [x1, #0x67]
    // 0xd30938: DecompressPointer r2
    //     0xd30938: add             x2, x2, HEAP, lsl #32
    // 0xd3093c: r16 = Sentinel
    //     0xd3093c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd30940: cmp             w2, w16
    // 0xd30944: b.eq            #0xd3095c
    // 0xd30948: r0 = false
    //     0xd30948: add             x0, NULL, #0x30  ; false
    // 0xd3094c: LeaveFrame
    //     0xd3094c: mov             SP, fp
    //     0xd30950: ldp             fp, lr, [SP], #0x10
    // 0xd30954: ret
    //     0xd30954: ret             
    // 0xd30958: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd30958: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd3095c: r9 = _scrollbarTheme
    //     0xd3095c: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b0] Field <_MaterialScrollbarState@590083257._scrollbarTheme@590083257>: late (offset: 0x68)
    //     0xd30960: ldr             x9, [x9, #0x9b0]
    // 0xd30964: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd30964: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ _trackBorderColor(/* No info */) {
    // ** addr: 0xd30a7c, size: 0x94
    // 0xd30a7c: EnterFrame
    //     0xd30a7c: stp             fp, lr, [SP, #-0x10]!
    //     0xd30a80: mov             fp, SP
    // 0xd30a84: AllocStack(0x18)
    //     0xd30a84: sub             SP, SP, #0x18
    // 0xd30a88: SetupParameters(_MaterialScrollbarState this /* r1 => r1, fp-0x8 */)
    //     0xd30a88: stur            x1, [fp, #-8]
    // 0xd30a8c: CheckStackOverflow
    //     0xd30a8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd30a90: cmp             SP, x16
    //     0xd30a94: b.ls            #0xd30afc
    // 0xd30a98: r1 = 2
    //     0xd30a98: movz            x1, #0x2
    // 0xd30a9c: r0 = AllocateContext()
    //     0xd30a9c: bl              #0xec126c  ; AllocateContextStub
    // 0xd30aa0: mov             x1, x0
    // 0xd30aa4: ldur            x0, [fp, #-8]
    // 0xd30aa8: StoreField: r1->field_f = r0
    //     0xd30aa8: stur            w0, [x1, #0xf]
    // 0xd30aac: LoadField: r2 = r0->field_63
    //     0xd30aac: ldur            w2, [x0, #0x63]
    // 0xd30ab0: DecompressPointer r2
    //     0xd30ab0: add             x2, x2, HEAP, lsl #32
    // 0xd30ab4: r16 = Sentinel
    //     0xd30ab4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd30ab8: cmp             w2, w16
    // 0xd30abc: b.eq            #0xd30b04
    // 0xd30ac0: LoadField: r0 = r2->field_7
    //     0xd30ac0: ldur            w0, [x2, #7]
    // 0xd30ac4: DecompressPointer r0
    //     0xd30ac4: add             x0, x0, HEAP, lsl #32
    // 0xd30ac8: StoreField: r1->field_13 = r0
    //     0xd30ac8: stur            w0, [x1, #0x13]
    // 0xd30acc: mov             x2, x1
    // 0xd30ad0: r1 = Function '<anonymous closure>':.
    //     0xd30ad0: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d9d0] AnonymousClosure: (0xd30b10), in [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_trackBorderColor (0xd30a7c)
    //     0xd30ad4: ldr             x1, [x1, #0x9d0]
    // 0xd30ad8: r0 = AllocateClosure()
    //     0xd30ad8: bl              #0xec1630  ; AllocateClosureStub
    // 0xd30adc: r16 = <Color>
    //     0xd30adc: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xd30ae0: ldr             x16, [x16, #0x158]
    // 0xd30ae4: stp             x0, x16, [SP]
    // 0xd30ae8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xd30ae8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xd30aec: r0 = resolveWith()
    //     0xd30aec: bl              #0x9d84b0  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::resolveWith
    // 0xd30af0: LeaveFrame
    //     0xd30af0: mov             SP, fp
    //     0xd30af4: ldp             fp, lr, [SP], #0x10
    // 0xd30af8: ret
    //     0xd30af8: ret             
    // 0xd30afc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd30afc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd30b00: b               #0xd30a98
    // 0xd30b04: r9 = _colorScheme
    //     0xd30b04: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9d8] Field <_MaterialScrollbarState@590083257._colorScheme@590083257>: late (offset: 0x64)
    //     0xd30b08: ldr             x9, [x9, #0x9d8]
    // 0xd30b0c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd30b0c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Color <anonymous closure>(dynamic, Set<WidgetState>) {
    // ** addr: 0xd30b10, size: 0x48
    // 0xd30b10: EnterFrame
    //     0xd30b10: stp             fp, lr, [SP, #-0x10]!
    //     0xd30b14: mov             fp, SP
    // 0xd30b18: ldr             x0, [fp, #0x18]
    // 0xd30b1c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd30b1c: ldur            w1, [x0, #0x17]
    // 0xd30b20: DecompressPointer r1
    //     0xd30b20: add             x1, x1, HEAP, lsl #32
    // 0xd30b24: CheckStackOverflow
    //     0xd30b24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd30b28: cmp             SP, x16
    //     0xd30b2c: b.ls            #0xd30b50
    // 0xd30b30: LoadField: r0 = r1->field_f
    //     0xd30b30: ldur            w0, [x1, #0xf]
    // 0xd30b34: DecompressPointer r0
    //     0xd30b34: add             x0, x0, HEAP, lsl #32
    // 0xd30b38: mov             x1, x0
    // 0xd30b3c: r0 = showScrollbar()
    //     0xd30b3c: bl              #0xd6db34  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::showScrollbar
    // 0xd30b40: r0 = Instance_Color
    //     0xd30b40: ldr             x0, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xd30b44: LeaveFrame
    //     0xd30b44: mov             SP, fp
    //     0xd30b48: ldp             fp, lr, [SP], #0x10
    // 0xd30b4c: ret
    //     0xd30b4c: ret             
    // 0xd30b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd30b50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd30b54: b               #0xd30b30
  }
  get _ _trackColor(/* No info */) {
    // ** addr: 0xd30bfc, size: 0x94
    // 0xd30bfc: EnterFrame
    //     0xd30bfc: stp             fp, lr, [SP, #-0x10]!
    //     0xd30c00: mov             fp, SP
    // 0xd30c04: AllocStack(0x18)
    //     0xd30c04: sub             SP, SP, #0x18
    // 0xd30c08: SetupParameters(_MaterialScrollbarState this /* r1 => r1, fp-0x8 */)
    //     0xd30c08: stur            x1, [fp, #-8]
    // 0xd30c0c: CheckStackOverflow
    //     0xd30c0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd30c10: cmp             SP, x16
    //     0xd30c14: b.ls            #0xd30c7c
    // 0xd30c18: r1 = 2
    //     0xd30c18: movz            x1, #0x2
    // 0xd30c1c: r0 = AllocateContext()
    //     0xd30c1c: bl              #0xec126c  ; AllocateContextStub
    // 0xd30c20: mov             x1, x0
    // 0xd30c24: ldur            x0, [fp, #-8]
    // 0xd30c28: StoreField: r1->field_f = r0
    //     0xd30c28: stur            w0, [x1, #0xf]
    // 0xd30c2c: LoadField: r2 = r0->field_63
    //     0xd30c2c: ldur            w2, [x0, #0x63]
    // 0xd30c30: DecompressPointer r2
    //     0xd30c30: add             x2, x2, HEAP, lsl #32
    // 0xd30c34: r16 = Sentinel
    //     0xd30c34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd30c38: cmp             w2, w16
    // 0xd30c3c: b.eq            #0xd30c84
    // 0xd30c40: LoadField: r0 = r2->field_7
    //     0xd30c40: ldur            w0, [x2, #7]
    // 0xd30c44: DecompressPointer r0
    //     0xd30c44: add             x0, x0, HEAP, lsl #32
    // 0xd30c48: StoreField: r1->field_13 = r0
    //     0xd30c48: stur            w0, [x1, #0x13]
    // 0xd30c4c: mov             x2, x1
    // 0xd30c50: r1 = Function '<anonymous closure>':.
    //     0xd30c50: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d9e0] AnonymousClosure: (0xd30b10), in [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_trackBorderColor (0xd30a7c)
    //     0xd30c54: ldr             x1, [x1, #0x9e0]
    // 0xd30c58: r0 = AllocateClosure()
    //     0xd30c58: bl              #0xec1630  ; AllocateClosureStub
    // 0xd30c5c: r16 = <Color>
    //     0xd30c5c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xd30c60: ldr             x16, [x16, #0x158]
    // 0xd30c64: stp             x0, x16, [SP]
    // 0xd30c68: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xd30c68: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xd30c6c: r0 = resolveWith()
    //     0xd30c6c: bl              #0x9d84b0  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::resolveWith
    // 0xd30c70: LeaveFrame
    //     0xd30c70: mov             SP, fp
    //     0xd30c74: ldp             fp, lr, [SP], #0x10
    // 0xd30c78: ret
    //     0xd30c78: ret             
    // 0xd30c7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd30c7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd30c80: b               #0xd30c18
    // 0xd30c84: r9 = _colorScheme
    //     0xd30c84: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9d8] Field <_MaterialScrollbarState@590083257._colorScheme@590083257>: late (offset: 0x64)
    //     0xd30c88: ldr             x9, [x9, #0x9d8]
    // 0xd30c8c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd30c8c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ _states(/* No info */) {
    // ** addr: 0xd30d34, size: 0xe4
    // 0xd30d34: EnterFrame
    //     0xd30d34: stp             fp, lr, [SP, #-0x10]!
    //     0xd30d38: mov             fp, SP
    // 0xd30d3c: AllocStack(0x18)
    //     0xd30d3c: sub             SP, SP, #0x18
    // 0xd30d40: SetupParameters(_MaterialScrollbarState this /* r1 => r1, fp-0x8 */)
    //     0xd30d40: stur            x1, [fp, #-8]
    // 0xd30d44: CheckStackOverflow
    //     0xd30d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd30d48: cmp             SP, x16
    //     0xd30d4c: b.ls            #0xd30e10
    // 0xd30d50: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xd30d50: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xd30d54: ldr             x0, [x0, #0x778]
    //     0xd30d58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xd30d5c: cmp             w0, w16
    //     0xd30d60: b.ne            #0xd30d6c
    //     0xd30d64: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xd30d68: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xd30d6c: r1 = <WidgetState>
    //     0xd30d6c: add             x1, PP, #0x39, lsl #12  ; [pp+0x39868] TypeArguments: <WidgetState>
    //     0xd30d70: ldr             x1, [x1, #0x868]
    // 0xd30d74: stur            x0, [fp, #-0x10]
    // 0xd30d78: r0 = _Set()
    //     0xd30d78: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xd30d7c: mov             x1, x0
    // 0xd30d80: ldur            x0, [fp, #-0x10]
    // 0xd30d84: stur            x1, [fp, #-0x18]
    // 0xd30d88: StoreField: r1->field_1b = r0
    //     0xd30d88: stur            w0, [x1, #0x1b]
    // 0xd30d8c: StoreField: r1->field_b = rZR
    //     0xd30d8c: stur            wzr, [x1, #0xb]
    // 0xd30d90: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xd30d90: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xd30d94: ldr             x0, [x0, #0x780]
    //     0xd30d98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xd30d9c: cmp             w0, w16
    //     0xd30da0: b.ne            #0xd30dac
    //     0xd30da4: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xd30da8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xd30dac: mov             x1, x0
    // 0xd30db0: ldur            x0, [fp, #-0x18]
    // 0xd30db4: StoreField: r0->field_f = r1
    //     0xd30db4: stur            w1, [x0, #0xf]
    // 0xd30db8: StoreField: r0->field_13 = rZR
    //     0xd30db8: stur            wzr, [x0, #0x13]
    // 0xd30dbc: ArrayStore: r0[0] = rZR  ; List_4
    //     0xd30dbc: stur            wzr, [x0, #0x17]
    // 0xd30dc0: ldur            x3, [fp, #-8]
    // 0xd30dc4: LoadField: r1 = r3->field_5b
    //     0xd30dc4: ldur            w1, [x3, #0x5b]
    // 0xd30dc8: DecompressPointer r1
    //     0xd30dc8: add             x1, x1, HEAP, lsl #32
    // 0xd30dcc: tbnz            w1, #4, #0xd30de0
    // 0xd30dd0: mov             x1, x0
    // 0xd30dd4: r2 = Instance_WidgetState
    //     0xd30dd4: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d9e8] Obj!WidgetState@e33921
    //     0xd30dd8: ldr             x2, [x2, #0x9e8]
    // 0xd30ddc: r0 = add()
    //     0xd30ddc: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xd30de0: ldur            x0, [fp, #-8]
    // 0xd30de4: LoadField: r1 = r0->field_5f
    //     0xd30de4: ldur            w1, [x0, #0x5f]
    // 0xd30de8: DecompressPointer r1
    //     0xd30de8: add             x1, x1, HEAP, lsl #32
    // 0xd30dec: tbnz            w1, #4, #0xd30e00
    // 0xd30df0: ldur            x1, [fp, #-0x18]
    // 0xd30df4: r2 = Instance_WidgetState
    //     0xd30df4: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d0e0] Obj!WidgetState@e33861
    //     0xd30df8: ldr             x2, [x2, #0xe0]
    // 0xd30dfc: r0 = add()
    //     0xd30dfc: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xd30e00: ldur            x0, [fp, #-0x18]
    // 0xd30e04: LeaveFrame
    //     0xd30e04: mov             SP, fp
    //     0xd30e08: ldp             fp, lr, [SP], #0x10
    // 0xd30e0c: ret
    //     0xd30e0c: ret             
    // 0xd30e10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd30e10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd30e14: b               #0xd30d50
  }
  get _ _thumbColor(/* No info */) {
    // ** addr: 0xd30e18, size: 0x2fc
    // 0xd30e18: EnterFrame
    //     0xd30e18: stp             fp, lr, [SP, #-0x10]!
    //     0xd30e1c: mov             fp, SP
    // 0xd30e20: AllocStack(0x28)
    //     0xd30e20: sub             SP, SP, #0x28
    // 0xd30e24: SetupParameters(_MaterialScrollbarState this /* r1 => r1, fp-0x8 */)
    //     0xd30e24: stur            x1, [fp, #-8]
    // 0xd30e28: CheckStackOverflow
    //     0xd30e28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd30e2c: cmp             SP, x16
    //     0xd30e30: b.ls            #0xd310e0
    // 0xd30e34: r1 = 4
    //     0xd30e34: movz            x1, #0x4
    // 0xd30e38: r0 = AllocateContext()
    //     0xd30e38: bl              #0xec126c  ; AllocateContextStub
    // 0xd30e3c: mov             x3, x0
    // 0xd30e40: ldur            x2, [fp, #-8]
    // 0xd30e44: stur            x3, [fp, #-0x18]
    // 0xd30e48: StoreField: r3->field_f = r2
    //     0xd30e48: stur            w2, [x3, #0xf]
    // 0xd30e4c: LoadField: r0 = r2->field_63
    //     0xd30e4c: ldur            w0, [x2, #0x63]
    // 0xd30e50: DecompressPointer r0
    //     0xd30e50: add             x0, x0, HEAP, lsl #32
    // 0xd30e54: r16 = Sentinel
    //     0xd30e54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd30e58: cmp             w0, w16
    // 0xd30e5c: b.eq            #0xd310e8
    // 0xd30e60: LoadField: r4 = r0->field_7f
    //     0xd30e60: ldur            w4, [x0, #0x7f]
    // 0xd30e64: DecompressPointer r4
    //     0xd30e64: add             x4, x4, HEAP, lsl #32
    // 0xd30e68: stur            x4, [fp, #-0x10]
    // 0xd30e6c: LoadField: r1 = r0->field_7
    //     0xd30e6c: ldur            w1, [x0, #7]
    // 0xd30e70: DecompressPointer r1
    //     0xd30e70: add             x1, x1, HEAP, lsl #32
    // 0xd30e74: r0 = Sentinel
    //     0xd30e74: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd30e78: StoreField: r3->field_13 = r0
    //     0xd30e78: stur            w0, [x3, #0x13]
    // 0xd30e7c: ArrayStore: r3[0] = r0  ; List_4
    //     0xd30e7c: stur            w0, [x3, #0x17]
    // 0xd30e80: StoreField: r3->field_1b = r0
    //     0xd30e80: stur            w0, [x3, #0x1b]
    // 0xd30e84: LoadField: r0 = r1->field_7
    //     0xd30e84: ldur            x0, [x1, #7]
    // 0xd30e88: cmp             x0, #0
    // 0xd30e8c: b.gt            #0xd30fa0
    // 0xd30e90: r0 = LoadClassIdInstr(r4)
    //     0xd30e90: ldur            x0, [x4, #-1]
    //     0xd30e94: ubfx            x0, x0, #0xc, #0x14
    // 0xd30e98: mov             x1, x4
    // 0xd30e9c: d0 = 0.750000
    //     0xd30e9c: fmov            d0, #0.75000000
    // 0xd30ea0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xd30ea0: sub             lr, x0, #1, lsl #12
    //     0xd30ea4: ldr             lr, [x21, lr, lsl #3]
    //     0xd30ea8: blr             lr
    // 0xd30eac: ldur            x2, [fp, #-0x18]
    // 0xd30eb0: StoreField: r2->field_13 = r0
    //     0xd30eb0: stur            w0, [x2, #0x13]
    //     0xd30eb4: ldurb           w16, [x2, #-1]
    //     0xd30eb8: ldurb           w17, [x0, #-1]
    //     0xd30ebc: and             x16, x17, x16, lsr #2
    //     0xd30ec0: tst             x16, HEAP, lsr #32
    //     0xd30ec4: b.eq            #0xd30ecc
    //     0xd30ec8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xd30ecc: ldur            x3, [fp, #-0x10]
    // 0xd30ed0: r0 = LoadClassIdInstr(r3)
    //     0xd30ed0: ldur            x0, [x3, #-1]
    //     0xd30ed4: ubfx            x0, x0, #0xc, #0x14
    // 0xd30ed8: mov             x1, x3
    // 0xd30edc: d0 = 0.650000
    //     0xd30edc: add             x17, PP, #0x4d, lsl #12  ; [pp+0x4d9f0] IMM: double(0.65) from 0x3fe4cccccccccccd
    //     0xd30ee0: ldr             d0, [x17, #0x9f0]
    // 0xd30ee4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xd30ee4: sub             lr, x0, #1, lsl #12
    //     0xd30ee8: ldr             lr, [x21, lr, lsl #3]
    //     0xd30eec: blr             lr
    // 0xd30ef0: ldur            x2, [fp, #-0x18]
    // 0xd30ef4: ArrayStore: r2[0] = r0  ; List_4
    //     0xd30ef4: stur            w0, [x2, #0x17]
    //     0xd30ef8: ldurb           w16, [x2, #-1]
    //     0xd30efc: ldurb           w17, [x0, #-1]
    //     0xd30f00: and             x16, x17, x16, lsr #2
    //     0xd30f04: tst             x16, HEAP, lsr #32
    //     0xd30f08: b.eq            #0xd30f10
    //     0xd30f0c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xd30f10: ldur            x3, [fp, #-8]
    // 0xd30f14: LoadField: r0 = r3->field_6b
    //     0xd30f14: ldur            w0, [x3, #0x6b]
    // 0xd30f18: DecompressPointer r0
    //     0xd30f18: add             x0, x0, HEAP, lsl #32
    // 0xd30f1c: r16 = Sentinel
    //     0xd30f1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd30f20: cmp             w0, w16
    // 0xd30f24: b.eq            #0xd310f4
    // 0xd30f28: tbnz            w0, #4, #0xd30f54
    // 0xd30f2c: LoadField: r1 = r3->field_f
    //     0xd30f2c: ldur            w1, [x3, #0xf]
    // 0xd30f30: DecompressPointer r1
    //     0xd30f30: add             x1, x1, HEAP, lsl #32
    // 0xd30f34: cmp             w1, NULL
    // 0xd30f38: b.eq            #0xd31100
    // 0xd30f3c: r0 = of()
    //     0xd30f3c: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xd30f40: LoadField: r1 = r0->field_53
    //     0xd30f40: ldur            w1, [x0, #0x53]
    // 0xd30f44: DecompressPointer r1
    //     0xd30f44: add             x1, x1, HEAP, lsl #32
    // 0xd30f48: d0 = 1.000000
    //     0xd30f48: fmov            d0, #1.00000000
    // 0xd30f4c: r0 = withOpacity()
    //     0xd30f4c: bl              #0xd72290  ; [dart:ui] Color::withOpacity
    // 0xd30f50: b               #0xd30f78
    // 0xd30f54: ldur            x2, [fp, #-0x10]
    // 0xd30f58: r0 = LoadClassIdInstr(r2)
    //     0xd30f58: ldur            x0, [x2, #-1]
    //     0xd30f5c: ubfx            x0, x0, #0xc, #0x14
    // 0xd30f60: mov             x1, x2
    // 0xd30f64: d0 = 0.300000
    //     0xd30f64: add             x17, PP, #0x29, lsl #12  ; [pp+0x29068] IMM: double(0.3) from 0x3fd3333333333333
    //     0xd30f68: ldr             d0, [x17, #0x68]
    // 0xd30f6c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xd30f6c: sub             lr, x0, #1, lsl #12
    //     0xd30f70: ldr             lr, [x21, lr, lsl #3]
    //     0xd30f74: blr             lr
    // 0xd30f78: ldur            x4, [fp, #-0x18]
    // 0xd30f7c: StoreField: r4->field_1b = r0
    //     0xd30f7c: stur            w0, [x4, #0x1b]
    //     0xd30f80: ldurb           w16, [x4, #-1]
    //     0xd30f84: ldurb           w17, [x0, #-1]
    //     0xd30f88: and             x16, x17, x16, lsr #2
    //     0xd30f8c: tst             x16, HEAP, lsr #32
    //     0xd30f90: b.eq            #0xd30f98
    //     0xd30f94: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xd30f98: mov             x2, x4
    // 0xd30f9c: b               #0xd310b4
    // 0xd30fa0: mov             x16, x4
    // 0xd30fa4: mov             x4, x2
    // 0xd30fa8: mov             x2, x16
    // 0xd30fac: mov             x16, x3
    // 0xd30fb0: mov             x3, x4
    // 0xd30fb4: mov             x4, x16
    // 0xd30fb8: r0 = LoadClassIdInstr(r2)
    //     0xd30fb8: ldur            x0, [x2, #-1]
    //     0xd30fbc: ubfx            x0, x0, #0xc, #0x14
    // 0xd30fc0: mov             x1, x2
    // 0xd30fc4: d0 = 0.600000
    //     0xd30fc4: ldr             d0, [PP, #0x5480]  ; [pp+0x5480] IMM: double(0.6) from 0x3fe3333333333333
    // 0xd30fc8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xd30fc8: sub             lr, x0, #1, lsl #12
    //     0xd30fcc: ldr             lr, [x21, lr, lsl #3]
    //     0xd30fd0: blr             lr
    // 0xd30fd4: ldur            x2, [fp, #-0x18]
    // 0xd30fd8: StoreField: r2->field_13 = r0
    //     0xd30fd8: stur            w0, [x2, #0x13]
    //     0xd30fdc: ldurb           w16, [x2, #-1]
    //     0xd30fe0: ldurb           w17, [x0, #-1]
    //     0xd30fe4: and             x16, x17, x16, lsr #2
    //     0xd30fe8: tst             x16, HEAP, lsr #32
    //     0xd30fec: b.eq            #0xd30ff4
    //     0xd30ff0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xd30ff4: ldur            x3, [fp, #-0x10]
    // 0xd30ff8: r0 = LoadClassIdInstr(r3)
    //     0xd30ff8: ldur            x0, [x3, #-1]
    //     0xd30ffc: ubfx            x0, x0, #0xc, #0x14
    // 0xd31000: mov             x1, x3
    // 0xd31004: d0 = 0.500000
    //     0xd31004: fmov            d0, #0.50000000
    // 0xd31008: r0 = GDT[cid_x0 + -0x1000]()
    //     0xd31008: sub             lr, x0, #1, lsl #12
    //     0xd3100c: ldr             lr, [x21, lr, lsl #3]
    //     0xd31010: blr             lr
    // 0xd31014: ldur            x2, [fp, #-0x18]
    // 0xd31018: ArrayStore: r2[0] = r0  ; List_4
    //     0xd31018: stur            w0, [x2, #0x17]
    //     0xd3101c: ldurb           w16, [x2, #-1]
    //     0xd31020: ldurb           w17, [x0, #-1]
    //     0xd31024: and             x16, x17, x16, lsr #2
    //     0xd31028: tst             x16, HEAP, lsr #32
    //     0xd3102c: b.eq            #0xd31034
    //     0xd31030: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xd31034: ldur            x0, [fp, #-8]
    // 0xd31038: LoadField: r1 = r0->field_6b
    //     0xd31038: ldur            w1, [x0, #0x6b]
    // 0xd3103c: DecompressPointer r1
    //     0xd3103c: add             x1, x1, HEAP, lsl #32
    // 0xd31040: r16 = Sentinel
    //     0xd31040: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd31044: cmp             w1, w16
    // 0xd31048: b.eq            #0xd31104
    // 0xd3104c: tbnz            w1, #4, #0xd31078
    // 0xd31050: LoadField: r1 = r0->field_f
    //     0xd31050: ldur            w1, [x0, #0xf]
    // 0xd31054: DecompressPointer r1
    //     0xd31054: add             x1, x1, HEAP, lsl #32
    // 0xd31058: cmp             w1, NULL
    // 0xd3105c: b.eq            #0xd31110
    // 0xd31060: r0 = of()
    //     0xd31060: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xd31064: LoadField: r1 = r0->field_53
    //     0xd31064: ldur            w1, [x0, #0x53]
    // 0xd31068: DecompressPointer r1
    //     0xd31068: add             x1, x1, HEAP, lsl #32
    // 0xd3106c: d0 = 1.000000
    //     0xd3106c: fmov            d0, #1.00000000
    // 0xd31070: r0 = withOpacity()
    //     0xd31070: bl              #0xd72290  ; [dart:ui] Color::withOpacity
    // 0xd31074: b               #0xd31094
    // 0xd31078: ldur            x1, [fp, #-0x10]
    // 0xd3107c: r0 = LoadClassIdInstr(r1)
    //     0xd3107c: ldur            x0, [x1, #-1]
    //     0xd31080: ubfx            x0, x0, #0xc, #0x14
    // 0xd31084: d0 = 0.100000
    //     0xd31084: ldr             d0, [PP, #0x5ad0]  ; [pp+0x5ad0] IMM: double(0.1) from 0x3fb999999999999a
    // 0xd31088: r0 = GDT[cid_x0 + -0x1000]()
    //     0xd31088: sub             lr, x0, #1, lsl #12
    //     0xd3108c: ldr             lr, [x21, lr, lsl #3]
    //     0xd31090: blr             lr
    // 0xd31094: ldur            x2, [fp, #-0x18]
    // 0xd31098: StoreField: r2->field_1b = r0
    //     0xd31098: stur            w0, [x2, #0x1b]
    //     0xd3109c: ldurb           w16, [x2, #-1]
    //     0xd310a0: ldurb           w17, [x0, #-1]
    //     0xd310a4: and             x16, x17, x16, lsr #2
    //     0xd310a8: tst             x16, HEAP, lsr #32
    //     0xd310ac: b.eq            #0xd310b4
    //     0xd310b0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xd310b4: r1 = Function '<anonymous closure>':.
    //     0xd310b4: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d9f8] AnonymousClosure: (0xd31114), in [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_thumbColor (0xd30e18)
    //     0xd310b8: ldr             x1, [x1, #0x9f8]
    // 0xd310bc: r0 = AllocateClosure()
    //     0xd310bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xd310c0: r16 = <Color>
    //     0xd310c0: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xd310c4: ldr             x16, [x16, #0x158]
    // 0xd310c8: stp             x0, x16, [SP]
    // 0xd310cc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xd310cc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xd310d0: r0 = resolveWith()
    //     0xd310d0: bl              #0x9d84b0  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::resolveWith
    // 0xd310d4: LeaveFrame
    //     0xd310d4: mov             SP, fp
    //     0xd310d8: ldp             fp, lr, [SP], #0x10
    // 0xd310dc: ret
    //     0xd310dc: ret             
    // 0xd310e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd310e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd310e4: b               #0xd30e34
    // 0xd310e8: r9 = _colorScheme
    //     0xd310e8: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9d8] Field <_MaterialScrollbarState@590083257._colorScheme@590083257>: late (offset: 0x64)
    //     0xd310ec: ldr             x9, [x9, #0x9d8]
    // 0xd310f0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd310f0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd310f4: r9 = _useAndroidScrollbar
    //     0xd310f4: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b8] Field <_MaterialScrollbarState@590083257._useAndroidScrollbar@590083257>: late (offset: 0x6c)
    //     0xd310f8: ldr             x9, [x9, #0x9b8]
    // 0xd310fc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd310fc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd31100: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd31100: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd31104: r9 = _useAndroidScrollbar
    //     0xd31104: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b8] Field <_MaterialScrollbarState@590083257._useAndroidScrollbar@590083257>: late (offset: 0x6c)
    //     0xd31108: ldr             x9, [x9, #0x9b8]
    // 0xd3110c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd3110c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd31110: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd31110: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Color <anonymous closure>(dynamic, Set<WidgetState>) {
    // ** addr: 0xd31114, size: 0x290
    // 0xd31114: EnterFrame
    //     0xd31114: stp             fp, lr, [SP, #-0x10]!
    //     0xd31118: mov             fp, SP
    // 0xd3111c: AllocStack(0x20)
    //     0xd3111c: sub             SP, SP, #0x20
    // 0xd31120: SetupParameters()
    //     0xd31120: ldr             x0, [fp, #0x18]
    //     0xd31124: ldur            w3, [x0, #0x17]
    //     0xd31128: add             x3, x3, HEAP, lsl #32
    //     0xd3112c: stur            x3, [fp, #-8]
    // 0xd31130: CheckStackOverflow
    //     0xd31130: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd31134: cmp             SP, x16
    //     0xd31138: b.ls            #0xd31358
    // 0xd3113c: ldr             x4, [fp, #0x10]
    // 0xd31140: r0 = LoadClassIdInstr(r4)
    //     0xd31140: ldur            x0, [x4, #-1]
    //     0xd31144: ubfx            x0, x0, #0xc, #0x14
    // 0xd31148: mov             x1, x4
    // 0xd3114c: r2 = Instance_WidgetState
    //     0xd3114c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d9e8] Obj!WidgetState@e33921
    //     0xd31150: ldr             x2, [x2, #0x9e8]
    // 0xd31154: r0 = GDT[cid_x0 + 0xf20c]()
    //     0xd31154: movz            x17, #0xf20c
    //     0xd31158: add             lr, x0, x17
    //     0xd3115c: ldr             lr, [x21, lr, lsl #3]
    //     0xd31160: blr             lr
    // 0xd31164: tbnz            w0, #4, #0xd311c8
    // 0xd31168: ldur            x0, [fp, #-8]
    // 0xd3116c: LoadField: r1 = r0->field_f
    //     0xd3116c: ldur            w1, [x0, #0xf]
    // 0xd31170: DecompressPointer r1
    //     0xd31170: add             x1, x1, HEAP, lsl #32
    // 0xd31174: LoadField: r2 = r1->field_67
    //     0xd31174: ldur            w2, [x1, #0x67]
    // 0xd31178: DecompressPointer r2
    //     0xd31178: add             x2, x2, HEAP, lsl #32
    // 0xd3117c: r16 = Sentinel
    //     0xd3117c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd31180: cmp             w2, w16
    // 0xd31184: b.eq            #0xd31360
    // 0xd31188: LoadField: r1 = r0->field_13
    //     0xd31188: ldur            w1, [x0, #0x13]
    // 0xd3118c: DecompressPointer r1
    //     0xd3118c: add             x1, x1, HEAP, lsl #32
    // 0xd31190: r16 = Sentinel
    //     0xd31190: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd31194: cmp             w1, w16
    // 0xd31198: b.ne            #0xd311ac
    // 0xd3119c: r16 = "dragColor"
    //     0xd3119c: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4da00] "dragColor"
    //     0xd311a0: ldr             x16, [x16, #0xa00]
    // 0xd311a4: str             x16, [SP]
    // 0xd311a8: r0 = _throwLocalNotInitialized()
    //     0xd311a8: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xd311ac: ldur            x0, [fp, #-8]
    // 0xd311b0: LoadField: r1 = r0->field_13
    //     0xd311b0: ldur            w1, [x0, #0x13]
    // 0xd311b4: DecompressPointer r1
    //     0xd311b4: add             x1, x1, HEAP, lsl #32
    // 0xd311b8: mov             x0, x1
    // 0xd311bc: LeaveFrame
    //     0xd311bc: mov             SP, fp
    //     0xd311c0: ldp             fp, lr, [SP], #0x10
    // 0xd311c4: ret
    //     0xd311c4: ret             
    // 0xd311c8: ldur            x0, [fp, #-8]
    // 0xd311cc: LoadField: r1 = r0->field_f
    //     0xd311cc: ldur            w1, [x0, #0xf]
    // 0xd311d0: DecompressPointer r1
    //     0xd311d0: add             x1, x1, HEAP, lsl #32
    // 0xd311d4: stur            x1, [fp, #-0x10]
    // 0xd311d8: r1 = 1
    //     0xd311d8: movz            x1, #0x1
    // 0xd311dc: r0 = AllocateContext()
    //     0xd311dc: bl              #0xec126c  ; AllocateContextStub
    // 0xd311e0: mov             x1, x0
    // 0xd311e4: ldur            x0, [fp, #-0x10]
    // 0xd311e8: StoreField: r1->field_f = r0
    //     0xd311e8: stur            w0, [x1, #0xf]
    // 0xd311ec: mov             x2, x1
    // 0xd311f0: r1 = Function '<anonymous closure>':.
    //     0xd311f0: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d9c8] AnonymousClosure: (0xd30908), in [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_trackVisibility (0xd308a4)
    //     0xd311f4: ldr             x1, [x1, #0x9c8]
    // 0xd311f8: r0 = AllocateClosure()
    //     0xd311f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xd311fc: r16 = <bool>
    //     0xd311fc: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xd31200: stp             x0, x16, [SP]
    // 0xd31204: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xd31204: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xd31208: r0 = resolveWith()
    //     0xd31208: bl              #0x9d84b0  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::resolveWith
    // 0xd3120c: mov             x1, x0
    // 0xd31210: ldr             x2, [fp, #0x10]
    // 0xd31214: r0 = resolve()
    //     0xd31214: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xd31218: tbnz            w0, #4, #0xd31278
    // 0xd3121c: ldur            x0, [fp, #-8]
    // 0xd31220: LoadField: r1 = r0->field_f
    //     0xd31220: ldur            w1, [x0, #0xf]
    // 0xd31224: DecompressPointer r1
    //     0xd31224: add             x1, x1, HEAP, lsl #32
    // 0xd31228: LoadField: r2 = r1->field_67
    //     0xd31228: ldur            w2, [x1, #0x67]
    // 0xd3122c: DecompressPointer r2
    //     0xd3122c: add             x2, x2, HEAP, lsl #32
    // 0xd31230: r16 = Sentinel
    //     0xd31230: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd31234: cmp             w2, w16
    // 0xd31238: b.eq            #0xd3136c
    // 0xd3123c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd3123c: ldur            w1, [x0, #0x17]
    // 0xd31240: DecompressPointer r1
    //     0xd31240: add             x1, x1, HEAP, lsl #32
    // 0xd31244: r16 = Sentinel
    //     0xd31244: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd31248: cmp             w1, w16
    // 0xd3124c: b.ne            #0xd3125c
    // 0xd31250: r16 = "hoverColor"
    //     0xd31250: ldr             x16, [PP, #0x5290]  ; [pp+0x5290] "hoverColor"
    // 0xd31254: str             x16, [SP]
    // 0xd31258: r0 = _throwLocalNotInitialized()
    //     0xd31258: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xd3125c: ldur            x0, [fp, #-8]
    // 0xd31260: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd31260: ldur            w1, [x0, #0x17]
    // 0xd31264: DecompressPointer r1
    //     0xd31264: add             x1, x1, HEAP, lsl #32
    // 0xd31268: mov             x0, x1
    // 0xd3126c: LeaveFrame
    //     0xd3126c: mov             SP, fp
    //     0xd31270: ldp             fp, lr, [SP], #0x10
    // 0xd31274: ret
    //     0xd31274: ret             
    // 0xd31278: ldur            x0, [fp, #-8]
    // 0xd3127c: LoadField: r1 = r0->field_f
    //     0xd3127c: ldur            w1, [x0, #0xf]
    // 0xd31280: DecompressPointer r1
    //     0xd31280: add             x1, x1, HEAP, lsl #32
    // 0xd31284: LoadField: r2 = r1->field_67
    //     0xd31284: ldur            w2, [x1, #0x67]
    // 0xd31288: DecompressPointer r2
    //     0xd31288: add             x2, x2, HEAP, lsl #32
    // 0xd3128c: r16 = Sentinel
    //     0xd3128c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd31290: cmp             w2, w16
    // 0xd31294: b.eq            #0xd31378
    // 0xd31298: LoadField: r1 = r0->field_1b
    //     0xd31298: ldur            w1, [x0, #0x1b]
    // 0xd3129c: DecompressPointer r1
    //     0xd3129c: add             x1, x1, HEAP, lsl #32
    // 0xd312a0: r16 = Sentinel
    //     0xd312a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd312a4: cmp             w1, w16
    // 0xd312a8: b.ne            #0xd312bc
    // 0xd312ac: r16 = "idleColor"
    //     0xd312ac: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4da08] "idleColor"
    //     0xd312b0: ldr             x16, [x16, #0xa08]
    // 0xd312b4: str             x16, [SP]
    // 0xd312b8: r0 = _throwLocalNotInitialized()
    //     0xd312b8: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xd312bc: ldur            x0, [fp, #-8]
    // 0xd312c0: LoadField: r1 = r0->field_1b
    //     0xd312c0: ldur            w1, [x0, #0x1b]
    // 0xd312c4: DecompressPointer r1
    //     0xd312c4: add             x1, x1, HEAP, lsl #32
    // 0xd312c8: stur            x1, [fp, #-0x10]
    // 0xd312cc: LoadField: r2 = r0->field_f
    //     0xd312cc: ldur            w2, [x0, #0xf]
    // 0xd312d0: DecompressPointer r2
    //     0xd312d0: add             x2, x2, HEAP, lsl #32
    // 0xd312d4: LoadField: r3 = r2->field_67
    //     0xd312d4: ldur            w3, [x2, #0x67]
    // 0xd312d8: DecompressPointer r3
    //     0xd312d8: add             x3, x3, HEAP, lsl #32
    // 0xd312dc: r16 = Sentinel
    //     0xd312dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd312e0: cmp             w3, w16
    // 0xd312e4: b.eq            #0xd31384
    // 0xd312e8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xd312e8: ldur            w2, [x0, #0x17]
    // 0xd312ec: DecompressPointer r2
    //     0xd312ec: add             x2, x2, HEAP, lsl #32
    // 0xd312f0: r16 = Sentinel
    //     0xd312f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd312f4: cmp             w2, w16
    // 0xd312f8: b.ne            #0xd31308
    // 0xd312fc: r16 = "hoverColor"
    //     0xd312fc: ldr             x16, [PP, #0x5290]  ; [pp+0x5290] "hoverColor"
    // 0xd31300: str             x16, [SP]
    // 0xd31304: r0 = _throwLocalNotInitialized()
    //     0xd31304: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xd31308: ldur            x0, [fp, #-8]
    // 0xd3130c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xd3130c: ldur            w2, [x0, #0x17]
    // 0xd31310: DecompressPointer r2
    //     0xd31310: add             x2, x2, HEAP, lsl #32
    // 0xd31314: LoadField: r1 = r0->field_f
    //     0xd31314: ldur            w1, [x0, #0xf]
    // 0xd31318: DecompressPointer r1
    //     0xd31318: add             x1, x1, HEAP, lsl #32
    // 0xd3131c: LoadField: r0 = r1->field_57
    //     0xd3131c: ldur            w0, [x1, #0x57]
    // 0xd31320: DecompressPointer r0
    //     0xd31320: add             x0, x0, HEAP, lsl #32
    // 0xd31324: r16 = Sentinel
    //     0xd31324: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd31328: cmp             w0, w16
    // 0xd3132c: b.eq            #0xd31390
    // 0xd31330: LoadField: r3 = r0->field_37
    //     0xd31330: ldur            w3, [x0, #0x37]
    // 0xd31334: DecompressPointer r3
    //     0xd31334: add             x3, x3, HEAP, lsl #32
    // 0xd31338: r16 = Sentinel
    //     0xd31338: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd3133c: cmp             w3, w16
    // 0xd31340: b.eq            #0xd3139c
    // 0xd31344: ldur            x1, [fp, #-0x10]
    // 0xd31348: r0 = lerp()
    //     0xd31348: bl              #0x7f4280  ; [dart:ui] Color::lerp
    // 0xd3134c: LeaveFrame
    //     0xd3134c: mov             SP, fp
    //     0xd31350: ldp             fp, lr, [SP], #0x10
    // 0xd31354: ret
    //     0xd31354: ret             
    // 0xd31358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd31358: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd3135c: b               #0xd3113c
    // 0xd31360: r9 = _scrollbarTheme
    //     0xd31360: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b0] Field <_MaterialScrollbarState@590083257._scrollbarTheme@590083257>: late (offset: 0x68)
    //     0xd31364: ldr             x9, [x9, #0x9b0]
    // 0xd31368: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd31368: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd3136c: r9 = _scrollbarTheme
    //     0xd3136c: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b0] Field <_MaterialScrollbarState@590083257._scrollbarTheme@590083257>: late (offset: 0x68)
    //     0xd31370: ldr             x9, [x9, #0x9b0]
    // 0xd31374: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd31374: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd31378: r9 = _scrollbarTheme
    //     0xd31378: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b0] Field <_MaterialScrollbarState@590083257._scrollbarTheme@590083257>: late (offset: 0x68)
    //     0xd3137c: ldr             x9, [x9, #0x9b0]
    // 0xd31380: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd31380: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd31384: r9 = _scrollbarTheme
    //     0xd31384: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b0] Field <_MaterialScrollbarState@590083257._scrollbarTheme@590083257>: late (offset: 0x68)
    //     0xd31388: ldr             x9, [x9, #0x9b0]
    // 0xd3138c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd3138c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd31390: r9 = _hoverAnimationController
    //     0xd31390: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d988] Field <_MaterialScrollbarState@590083257._hoverAnimationController@590083257>: late (offset: 0x58)
    //     0xd31394: ldr             x9, [x9, #0x988]
    // 0xd31398: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd31398: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd3139c: r9 = _value
    //     0xd3139c: ldr             x9, [PP, #0x4ea8]  ; [pp+0x4ea8] Field <AnimationController._value@441066280>: late (offset: 0x38)
    // 0xd313a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd313a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ showScrollbar(/* No info */) {
    // ** addr: 0xd6db34, size: 0x78
    // 0xd6db34: EnterFrame
    //     0xd6db34: stp             fp, lr, [SP, #-0x10]!
    //     0xd6db38: mov             fp, SP
    // 0xd6db3c: LoadField: r2 = r1->field_b
    //     0xd6db3c: ldur            w2, [x1, #0xb]
    // 0xd6db40: DecompressPointer r2
    //     0xd6db40: add             x2, x2, HEAP, lsl #32
    // 0xd6db44: cmp             w2, NULL
    // 0xd6db48: b.eq            #0xd6db9c
    // 0xd6db4c: LoadField: r3 = r2->field_13
    //     0xd6db4c: ldur            w3, [x2, #0x13]
    // 0xd6db50: DecompressPointer r3
    //     0xd6db50: add             x3, x3, HEAP, lsl #32
    // 0xd6db54: cmp             w3, NULL
    // 0xd6db58: b.ne            #0xd6db78
    // 0xd6db5c: LoadField: r2 = r1->field_67
    //     0xd6db5c: ldur            w2, [x1, #0x67]
    // 0xd6db60: DecompressPointer r2
    //     0xd6db60: add             x2, x2, HEAP, lsl #32
    // 0xd6db64: r16 = Sentinel
    //     0xd6db64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd6db68: cmp             w2, w16
    // 0xd6db6c: b.eq            #0xd6dba0
    // 0xd6db70: r1 = Null
    //     0xd6db70: mov             x1, NULL
    // 0xd6db74: b               #0xd6db7c
    // 0xd6db78: mov             x1, x3
    // 0xd6db7c: cmp             w1, NULL
    // 0xd6db80: b.ne            #0xd6db8c
    // 0xd6db84: r0 = false
    //     0xd6db84: add             x0, NULL, #0x30  ; false
    // 0xd6db88: b               #0xd6db90
    // 0xd6db8c: mov             x0, x1
    // 0xd6db90: LeaveFrame
    //     0xd6db90: mov             SP, fp
    //     0xd6db94: ldp             fp, lr, [SP], #0x10
    // 0xd6db98: ret
    //     0xd6db98: ret             
    // 0xd6db9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd6db9c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd6dba0: r9 = _scrollbarTheme
    //     0xd6dba0: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b0] Field <_MaterialScrollbarState@590083257._scrollbarTheme@590083257>: late (offset: 0x68)
    //     0xd6dba4: ldr             x9, [x9, #0x9b0]
    // 0xd6dba8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd6dba8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ enableGestures(/* No info */) {
    // ** addr: 0xd7ddb0, size: 0x6c
    // 0xd7ddb0: EnterFrame
    //     0xd7ddb0: stp             fp, lr, [SP, #-0x10]!
    //     0xd7ddb4: mov             fp, SP
    // 0xd7ddb8: LoadField: r2 = r1->field_b
    //     0xd7ddb8: ldur            w2, [x1, #0xb]
    // 0xd7ddbc: DecompressPointer r2
    //     0xd7ddbc: add             x2, x2, HEAP, lsl #32
    // 0xd7ddc0: cmp             w2, NULL
    // 0xd7ddc4: b.eq            #0xd7de00
    // 0xd7ddc8: LoadField: r2 = r1->field_67
    //     0xd7ddc8: ldur            w2, [x1, #0x67]
    // 0xd7ddcc: DecompressPointer r2
    //     0xd7ddcc: add             x2, x2, HEAP, lsl #32
    // 0xd7ddd0: r16 = Sentinel
    //     0xd7ddd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd7ddd4: cmp             w2, w16
    // 0xd7ddd8: b.eq            #0xd7de04
    // 0xd7dddc: LoadField: r2 = r1->field_6b
    //     0xd7dddc: ldur            w2, [x1, #0x6b]
    // 0xd7dde0: DecompressPointer r2
    //     0xd7dde0: add             x2, x2, HEAP, lsl #32
    // 0xd7dde4: r16 = Sentinel
    //     0xd7dde4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd7dde8: cmp             w2, w16
    // 0xd7ddec: b.eq            #0xd7de10
    // 0xd7ddf0: eor             x0, x2, #0x10
    // 0xd7ddf4: LeaveFrame
    //     0xd7ddf4: mov             SP, fp
    //     0xd7ddf8: ldp             fp, lr, [SP], #0x10
    // 0xd7ddfc: ret
    //     0xd7ddfc: ret             
    // 0xd7de00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd7de00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd7de04: r9 = _scrollbarTheme
    //     0xd7de04: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b0] Field <_MaterialScrollbarState@590083257._scrollbarTheme@590083257>: late (offset: 0x68)
    //     0xd7de08: ldr             x9, [x9, #0x9b0]
    // 0xd7de0c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd7de0c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd7de10: r9 = _useAndroidScrollbar
    //     0xd7de10: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9b8] Field <_MaterialScrollbarState@590083257._useAndroidScrollbar@590083257>: late (offset: 0x6c)
    //     0xd7de14: ldr             x9, [x9, #0x9b8]
    // 0xd7de18: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd7de18: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4889, size: 0x64, field offset: 0x64
//   const constructor, 
class _MaterialScrollbar extends RawScrollbar {

  _ createState(/* No info */) {
    // ** addr: 0xa8f1d4, size: 0x48
    // 0xa8f1d4: EnterFrame
    //     0xa8f1d4: stp             fp, lr, [SP, #-0x10]!
    //     0xa8f1d8: mov             fp, SP
    // 0xa8f1dc: AllocStack(0x8)
    //     0xa8f1dc: sub             SP, SP, #8
    // 0xa8f1e0: CheckStackOverflow
    //     0xa8f1e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8f1e4: cmp             SP, x16
    //     0xa8f1e8: b.ls            #0xa8f214
    // 0xa8f1ec: r1 = <_MaterialScrollbar>
    //     0xa8f1ec: add             x1, PP, #0x43, lsl #12  ; [pp+0x43e60] TypeArguments: <_MaterialScrollbar>
    //     0xa8f1f0: ldr             x1, [x1, #0xe60]
    // 0xa8f1f4: r0 = _MaterialScrollbarState()
    //     0xa8f1f4: bl              #0xa8f300  ; Allocate_MaterialScrollbarStateStub -> _MaterialScrollbarState (size=0x70)
    // 0xa8f1f8: mov             x1, x0
    // 0xa8f1fc: stur            x0, [fp, #-8]
    // 0xa8f200: r0 = _MaterialScrollbarState()
    //     0xa8f200: bl              #0xa8f21c  ; [package:flutter/src/material/scrollbar.dart] _MaterialScrollbarState::_MaterialScrollbarState
    // 0xa8f204: ldur            x0, [fp, #-8]
    // 0xa8f208: LeaveFrame
    //     0xa8f208: mov             SP, fp
    //     0xa8f20c: ldp             fp, lr, [SP], #0x10
    // 0xa8f210: ret
    //     0xa8f210: ret             
    // 0xa8f214: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8f214: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8f218: b               #0xa8f1ec
  }
}

// class id: 5373, size: 0x30, field offset: 0xc
//   const constructor, 
class Scrollbar extends StatelessWidget {

  _Tahlil field_c;

  _ build(/* No info */) {
    // ** addr: 0xaa05e4, size: 0xa0
    // 0xaa05e4: EnterFrame
    //     0xaa05e4: stp             fp, lr, [SP, #-0x10]!
    //     0xaa05e8: mov             fp, SP
    // 0xaa05ec: AllocStack(0x18)
    //     0xaa05ec: sub             SP, SP, #0x18
    // 0xaa05f0: SetupParameters(Scrollbar this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0xaa05f0: mov             x0, x1
    //     0xaa05f4: stur            x1, [fp, #-8]
    //     0xaa05f8: mov             x1, x2
    // 0xaa05fc: CheckStackOverflow
    //     0xaa05fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa0600: cmp             SP, x16
    //     0xaa0604: b.ls            #0xaa067c
    // 0xaa0608: r0 = of()
    //     0xaa0608: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaa060c: ldur            x0, [fp, #-8]
    // 0xaa0610: LoadField: r1 = r0->field_f
    //     0xaa0610: ldur            w1, [x0, #0xf]
    // 0xaa0614: DecompressPointer r1
    //     0xaa0614: add             x1, x1, HEAP, lsl #32
    // 0xaa0618: stur            x1, [fp, #-0x18]
    // 0xaa061c: LoadField: r2 = r0->field_b
    //     0xaa061c: ldur            w2, [x0, #0xb]
    // 0xaa0620: DecompressPointer r2
    //     0xaa0620: add             x2, x2, HEAP, lsl #32
    // 0xaa0624: stur            x2, [fp, #-0x10]
    // 0xaa0628: r0 = _MaterialScrollbar()
    //     0xaa0628: bl              #0xaa0684  ; Allocate_MaterialScrollbarStub -> _MaterialScrollbar (size=0x64)
    // 0xaa062c: ldur            x1, [fp, #-0x10]
    // 0xaa0630: StoreField: r0->field_b = r1
    //     0xaa0630: stur            w1, [x0, #0xb]
    // 0xaa0634: ldur            x1, [fp, #-0x18]
    // 0xaa0638: StoreField: r0->field_f = r1
    //     0xaa0638: stur            w1, [x0, #0xf]
    // 0xaa063c: d0 = 18.000000
    //     0xaa063c: fmov            d0, #18.00000000
    // 0xaa0640: StoreField: r0->field_27 = d0
    //     0xaa0640: stur            d0, [x0, #0x27]
    // 0xaa0644: r1 = Instance_Duration
    //     0xaa0644: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0xaa0648: ldr             x1, [x1, #0x9c0]
    // 0xaa064c: StoreField: r0->field_3b = r1
    //     0xaa064c: stur            w1, [x0, #0x3b]
    // 0xaa0650: r1 = Instance_Duration
    //     0xaa0650: add             x1, PP, #0x39, lsl #12  ; [pp+0x39740] Obj!Duration@e3a241
    //     0xaa0654: ldr             x1, [x1, #0x740]
    // 0xaa0658: StoreField: r0->field_3f = r1
    //     0xaa0658: stur            w1, [x0, #0x3f]
    // 0xaa065c: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xaa065c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xaa0660: ldr             x1, [x1, #0xf58]
    // 0xaa0664: StoreField: r0->field_43 = r1
    //     0xaa0664: stur            w1, [x0, #0x43]
    // 0xaa0668: StoreField: r0->field_4f = rZR
    //     0xaa0668: stur            xzr, [x0, #0x4f]
    // 0xaa066c: StoreField: r0->field_57 = rZR
    //     0xaa066c: stur            xzr, [x0, #0x57]
    // 0xaa0670: LeaveFrame
    //     0xaa0670: mov             SP, fp
    //     0xaa0674: ldp             fp, lr, [SP], #0x10
    // 0xaa0678: ret
    //     0xaa0678: ret             
    // 0xaa067c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa067c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa0680: b               #0xaa0608
  }
}
