// lib: , url: package:flutter/src/material/selection_area.dart

// class id: 1048947, size: 0x8
class :: {
}

// class id: 4259, size: 0x1c, field offset: 0x14
class SelectionAreaState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xa04160, size: 0x10c
    // 0xa04160: EnterFrame
    //     0xa04160: stp             fp, lr, [SP, #-0x10]!
    //     0xa04164: mov             fp, SP
    // 0xa04168: AllocStack(0x28)
    //     0xa04168: sub             SP, SP, #0x28
    // 0xa0416c: SetupParameters(SelectionAreaState this /* r1 => r0, fp-0x18 */)
    //     0xa0416c: mov             x0, x1
    //     0xa04170: stur            x1, [fp, #-0x18]
    // 0xa04174: CheckStackOverflow
    //     0xa04174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa04178: cmp             SP, x16
    //     0xa0417c: b.ls            #0xa04258
    // 0xa04180: LoadField: r1 = r0->field_b
    //     0xa04180: ldur            w1, [x0, #0xb]
    // 0xa04184: DecompressPointer r1
    //     0xa04184: add             x1, x1, HEAP, lsl #32
    // 0xa04188: cmp             w1, NULL
    // 0xa0418c: b.eq            #0xa04260
    // 0xa04190: LoadField: r2 = r1->field_13
    //     0xa04190: ldur            w2, [x1, #0x13]
    // 0xa04194: DecompressPointer r2
    //     0xa04194: add             x2, x2, HEAP, lsl #32
    // 0xa04198: stur            x2, [fp, #-0x10]
    // 0xa0419c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa0419c: ldur            w3, [x0, #0x17]
    // 0xa041a0: DecompressPointer r3
    //     0xa041a0: add             x3, x3, HEAP, lsl #32
    // 0xa041a4: mov             x1, x0
    // 0xa041a8: stur            x3, [fp, #-8]
    // 0xa041ac: r0 = _effectiveFocusNode()
    //     0xa041ac: bl              #0xa0429c  ; [package:flutter/src/material/selection_area.dart] SelectionAreaState::_effectiveFocusNode
    // 0xa041b0: mov             x1, x0
    // 0xa041b4: ldur            x0, [fp, #-0x18]
    // 0xa041b8: stur            x1, [fp, #-0x20]
    // 0xa041bc: LoadField: r2 = r0->field_b
    //     0xa041bc: ldur            w2, [x0, #0xb]
    // 0xa041c0: DecompressPointer r2
    //     0xa041c0: add             x2, x2, HEAP, lsl #32
    // 0xa041c4: cmp             w2, NULL
    // 0xa041c8: b.eq            #0xa04264
    // 0xa041cc: r0 = InitLateStaticField(0xac0) // [package:flutter/src/material/magnifier.dart] TextMagnifier::adaptiveMagnifierConfiguration
    //     0xa041cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa041d0: ldr             x0, [x0, #0x1580]
    //     0xa041d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa041d8: cmp             w0, w16
    //     0xa041dc: b.ne            #0xa041ec
    //     0xa041e0: add             x2, PP, #0x43, lsl #12  ; [pp+0x439e8] Field <TextMagnifier.adaptiveMagnifierConfiguration>: static late (offset: 0xac0)
    //     0xa041e4: ldr             x2, [x2, #0x9e8]
    //     0xa041e8: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xa041ec: mov             x1, x0
    // 0xa041f0: ldur            x0, [fp, #-0x18]
    // 0xa041f4: stur            x1, [fp, #-0x28]
    // 0xa041f8: LoadField: r2 = r0->field_b
    //     0xa041f8: ldur            w2, [x0, #0xb]
    // 0xa041fc: DecompressPointer r2
    //     0xa041fc: add             x2, x2, HEAP, lsl #32
    // 0xa04200: cmp             w2, NULL
    // 0xa04204: b.eq            #0xa04268
    // 0xa04208: LoadField: r0 = r2->field_1f
    //     0xa04208: ldur            w0, [x2, #0x1f]
    // 0xa0420c: DecompressPointer r0
    //     0xa0420c: add             x0, x0, HEAP, lsl #32
    // 0xa04210: stur            x0, [fp, #-0x18]
    // 0xa04214: r0 = SelectableRegion()
    //     0xa04214: bl              #0xa04290  ; AllocateSelectableRegionStub -> SelectableRegion (size=0x24)
    // 0xa04218: r1 = Closure: (BuildContext, SelectableRegionState) => Widget from Function '_defaultContextMenuBuilder@596476716': static.
    //     0xa04218: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b68] Closure: (BuildContext, SelectableRegionState) => Widget from Function '_defaultContextMenuBuilder@596476716': static. (0x7e54fb40432c)
    //     0xa0421c: ldr             x1, [x1, #0xb68]
    // 0xa04220: ArrayStore: r0[0] = r1  ; List_4
    //     0xa04220: stur            w1, [x0, #0x17]
    // 0xa04224: ldur            x1, [fp, #-0x20]
    // 0xa04228: StoreField: r0->field_f = r1
    //     0xa04228: stur            w1, [x0, #0xf]
    // 0xa0422c: ldur            x1, [fp, #-0x10]
    // 0xa04230: StoreField: r0->field_1b = r1
    //     0xa04230: stur            w1, [x0, #0x1b]
    // 0xa04234: ldur            x1, [fp, #-0x18]
    // 0xa04238: StoreField: r0->field_13 = r1
    //     0xa04238: stur            w1, [x0, #0x13]
    // 0xa0423c: ldur            x1, [fp, #-0x28]
    // 0xa04240: StoreField: r0->field_b = r1
    //     0xa04240: stur            w1, [x0, #0xb]
    // 0xa04244: ldur            x1, [fp, #-8]
    // 0xa04248: StoreField: r0->field_7 = r1
    //     0xa04248: stur            w1, [x0, #7]
    // 0xa0424c: LeaveFrame
    //     0xa0424c: mov             SP, fp
    //     0xa04250: ldp             fp, lr, [SP], #0x10
    // 0xa04254: ret
    //     0xa04254: ret             
    // 0xa04258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa04258: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0425c: b               #0xa04180
    // 0xa04260: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa04260: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa04264: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa04264: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa04268: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa04268: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _effectiveFocusNode(/* No info */) {
    // ** addr: 0xa0429c, size: 0x90
    // 0xa0429c: EnterFrame
    //     0xa0429c: stp             fp, lr, [SP, #-0x10]!
    //     0xa042a0: mov             fp, SP
    // 0xa042a4: AllocStack(0x10)
    //     0xa042a4: sub             SP, SP, #0x10
    // 0xa042a8: SetupParameters(SelectionAreaState this /* r1 => r1, fp-0x8 */)
    //     0xa042a8: stur            x1, [fp, #-8]
    // 0xa042ac: CheckStackOverflow
    //     0xa042ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa042b0: cmp             SP, x16
    //     0xa042b4: b.ls            #0xa04320
    // 0xa042b8: LoadField: r0 = r1->field_b
    //     0xa042b8: ldur            w0, [x1, #0xb]
    // 0xa042bc: DecompressPointer r0
    //     0xa042bc: add             x0, x0, HEAP, lsl #32
    // 0xa042c0: cmp             w0, NULL
    // 0xa042c4: b.eq            #0xa04328
    // 0xa042c8: LoadField: r0 = r1->field_13
    //     0xa042c8: ldur            w0, [x1, #0x13]
    // 0xa042cc: DecompressPointer r0
    //     0xa042cc: add             x0, x0, HEAP, lsl #32
    // 0xa042d0: cmp             w0, NULL
    // 0xa042d4: b.ne            #0xa04314
    // 0xa042d8: r0 = FocusNode()
    //     0xa042d8: bl              #0x811904  ; AllocateFocusNodeStub -> FocusNode (size=0x68)
    // 0xa042dc: mov             x1, x0
    // 0xa042e0: stur            x0, [fp, #-0x10]
    // 0xa042e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa042e4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa042e8: r0 = FocusNode()
    //     0xa042e8: bl              #0x693dec  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::FocusNode
    // 0xa042ec: ldur            x0, [fp, #-0x10]
    // 0xa042f0: ldur            x1, [fp, #-8]
    // 0xa042f4: StoreField: r1->field_13 = r0
    //     0xa042f4: stur            w0, [x1, #0x13]
    //     0xa042f8: ldurb           w16, [x1, #-1]
    //     0xa042fc: ldurb           w17, [x0, #-1]
    //     0xa04300: and             x16, x17, x16, lsr #2
    //     0xa04304: tst             x16, HEAP, lsr #32
    //     0xa04308: b.eq            #0xa04310
    //     0xa0430c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa04310: ldur            x0, [fp, #-0x10]
    // 0xa04314: LeaveFrame
    //     0xa04314: mov             SP, fp
    //     0xa04318: ldp             fp, lr, [SP], #0x10
    // 0xa0431c: ret
    //     0xa0431c: ret             
    // 0xa04320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa04320: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa04324: b               #0xa042b8
    // 0xa04328: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa04328: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7e894, size: 0x44
    // 0xa7e894: EnterFrame
    //     0xa7e894: stp             fp, lr, [SP, #-0x10]!
    //     0xa7e898: mov             fp, SP
    // 0xa7e89c: CheckStackOverflow
    //     0xa7e89c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7e8a0: cmp             SP, x16
    //     0xa7e8a4: b.ls            #0xa7e8d0
    // 0xa7e8a8: LoadField: r0 = r1->field_13
    //     0xa7e8a8: ldur            w0, [x1, #0x13]
    // 0xa7e8ac: DecompressPointer r0
    //     0xa7e8ac: add             x0, x0, HEAP, lsl #32
    // 0xa7e8b0: cmp             w0, NULL
    // 0xa7e8b4: b.eq            #0xa7e8c0
    // 0xa7e8b8: mov             x1, x0
    // 0xa7e8bc: r0 = dispose()
    //     0xa7e8bc: bl              #0xa8b4f0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::dispose
    // 0xa7e8c0: r0 = Null
    //     0xa7e8c0: mov             x0, NULL
    // 0xa7e8c4: LeaveFrame
    //     0xa7e8c4: mov             SP, fp
    //     0xa7e8c8: ldp             fp, lr, [SP], #0x10
    // 0xa7e8cc: ret
    //     0xa7e8cc: ret             
    // 0xa7e8d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7e8d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7e8d4: b               #0xa7e8a8
  }
}

// class id: 4815, size: 0x24, field offset: 0xc
//   const constructor, 
class SelectionArea extends StatefulWidget {

  [closure] static Widget _defaultContextMenuBuilder(dynamic, BuildContext, SelectableRegionState) {
    // ** addr: 0xa0432c, size: 0x34
    // 0xa0432c: EnterFrame
    //     0xa0432c: stp             fp, lr, [SP, #-0x10]!
    //     0xa04330: mov             fp, SP
    // 0xa04334: CheckStackOverflow
    //     0xa04334: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa04338: cmp             SP, x16
    //     0xa0433c: b.ls            #0xa04358
    // 0xa04340: ldr             x1, [fp, #0x18]
    // 0xa04344: ldr             x2, [fp, #0x10]
    // 0xa04348: r0 = _defaultContextMenuBuilder()
    //     0xa04348: bl              #0xa04360  ; [package:flutter/src/material/selection_area.dart] SelectionArea::_defaultContextMenuBuilder
    // 0xa0434c: LeaveFrame
    //     0xa0434c: mov             SP, fp
    //     0xa04350: ldp             fp, lr, [SP], #0x10
    // 0xa04354: ret
    //     0xa04354: ret             
    // 0xa04358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa04358: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0435c: b               #0xa04340
  }
  static _ _defaultContextMenuBuilder(/* No info */) {
    // ** addr: 0xa04360, size: 0x80
    // 0xa04360: EnterFrame
    //     0xa04360: stp             fp, lr, [SP, #-0x10]!
    //     0xa04364: mov             fp, SP
    // 0xa04368: AllocStack(0x18)
    //     0xa04368: sub             SP, SP, #0x18
    // 0xa0436c: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xa0436c: mov             x0, x2
    //     0xa04370: stur            x2, [fp, #-8]
    // 0xa04374: CheckStackOverflow
    //     0xa04374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa04378: cmp             SP, x16
    //     0xa0437c: b.ls            #0xa043d8
    // 0xa04380: mov             x1, x0
    // 0xa04384: r0 = contextMenuButtonItems()
    //     0xa04384: bl              #0xa04990  ; [package:flutter/src/widgets/selectable_region.dart] SelectableRegionState::contextMenuButtonItems
    // 0xa04388: stur            x0, [fp, #-0x10]
    // 0xa0438c: r0 = AdaptiveTextSelectionToolbar()
    //     0xa0438c: bl              #0xa04984  ; AllocateAdaptiveTextSelectionToolbarStub -> AdaptiveTextSelectionToolbar (size=0x18)
    // 0xa04390: mov             x2, x0
    // 0xa04394: ldur            x0, [fp, #-0x10]
    // 0xa04398: stur            x2, [fp, #-0x18]
    // 0xa0439c: StoreField: r2->field_b = r0
    //     0xa0439c: stur            w0, [x2, #0xb]
    // 0xa043a0: ldur            x1, [fp, #-8]
    // 0xa043a4: r0 = contextMenuAnchors()
    //     0xa043a4: bl              #0xa043e0  ; [package:flutter/src/widgets/selectable_region.dart] SelectableRegionState::contextMenuAnchors
    // 0xa043a8: ldur            x1, [fp, #-0x18]
    // 0xa043ac: StoreField: r1->field_13 = r0
    //     0xa043ac: stur            w0, [x1, #0x13]
    //     0xa043b0: ldurb           w16, [x1, #-1]
    //     0xa043b4: ldurb           w17, [x0, #-1]
    //     0xa043b8: and             x16, x17, x16, lsr #2
    //     0xa043bc: tst             x16, HEAP, lsr #32
    //     0xa043c0: b.eq            #0xa043c8
    //     0xa043c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa043c8: mov             x0, x1
    // 0xa043cc: LeaveFrame
    //     0xa043cc: mov             SP, fp
    //     0xa043d0: ldp             fp, lr, [SP], #0x10
    // 0xa043d4: ret
    //     0xa043d4: ret             
    // 0xa043d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa043d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa043dc: b               #0xa04380
  }
  _ createState(/* No info */) {
    // ** addr: 0xa90e14, size: 0x44
    // 0xa90e14: EnterFrame
    //     0xa90e14: stp             fp, lr, [SP, #-0x10]!
    //     0xa90e18: mov             fp, SP
    // 0xa90e1c: AllocStack(0x8)
    //     0xa90e1c: sub             SP, SP, #8
    // 0xa90e20: SetupParameters(SelectionArea this /* r1 => r0 */)
    //     0xa90e20: mov             x0, x1
    // 0xa90e24: r1 = <SelectionArea>
    //     0xa90e24: add             x1, PP, #0x43, lsl #12  ; [pp+0x43e50] TypeArguments: <SelectionArea>
    //     0xa90e28: ldr             x1, [x1, #0xe50]
    // 0xa90e2c: r0 = SelectionAreaState()
    //     0xa90e2c: bl              #0xa90e58  ; AllocateSelectionAreaStateStub -> SelectionAreaState (size=0x1c)
    // 0xa90e30: r1 = <SelectableRegionState>
    //     0xa90e30: add             x1, PP, #0x43, lsl #12  ; [pp+0x43e58] TypeArguments: <SelectableRegionState>
    //     0xa90e34: ldr             x1, [x1, #0xe58]
    // 0xa90e38: stur            x0, [fp, #-8]
    // 0xa90e3c: r0 = LabeledGlobalKey()
    //     0xa90e3c: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xa90e40: mov             x1, x0
    // 0xa90e44: ldur            x0, [fp, #-8]
    // 0xa90e48: ArrayStore: r0[0] = r1  ; List_4
    //     0xa90e48: stur            w1, [x0, #0x17]
    // 0xa90e4c: LeaveFrame
    //     0xa90e4c: mov             SP, fp
    //     0xa90e50: ldp             fp, lr, [SP], #0x10
    // 0xa90e54: ret
    //     0xa90e54: ret             
  }
}
