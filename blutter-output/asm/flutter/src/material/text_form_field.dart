// lib: , url: package:flutter/src/material/text_form_field.dart

// class id: 1048962, size: 0x8
class :: {
}

// class id: 4252, size: 0x38, field offset: 0x34
class _TextFormFieldState extends FormFieldState<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x93abf8, size: 0xc8
    // 0x93abf8: EnterFrame
    //     0x93abf8: stp             fp, lr, [SP, #-0x10]!
    //     0x93abfc: mov             fp, SP
    // 0x93ac00: AllocStack(0x18)
    //     0x93ac00: sub             SP, SP, #0x18
    // 0x93ac04: SetupParameters(_TextFormFieldState this /* r1 => r0, fp-0x8 */)
    //     0x93ac04: mov             x0, x1
    //     0x93ac08: stur            x1, [fp, #-8]
    // 0x93ac0c: CheckStackOverflow
    //     0x93ac0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93ac10: cmp             SP, x16
    //     0x93ac14: b.ls            #0x93acb4
    // 0x93ac18: mov             x1, x0
    // 0x93ac1c: r0 = initState()
    //     0x93ac1c: bl              #0x93af1c  ; [package:flutter/src/widgets/form.dart] FormFieldState::initState
    // 0x93ac20: ldur            x1, [fp, #-8]
    // 0x93ac24: LoadField: r0 = r1->field_b
    //     0x93ac24: ldur            w0, [x1, #0xb]
    // 0x93ac28: DecompressPointer r0
    //     0x93ac28: add             x0, x0, HEAP, lsl #32
    // 0x93ac2c: cmp             w0, NULL
    // 0x93ac30: b.eq            #0x93acbc
    // 0x93ac34: LoadField: r3 = r0->field_2b
    //     0x93ac34: ldur            w3, [x0, #0x2b]
    // 0x93ac38: DecompressPointer r3
    //     0x93ac38: add             x3, x3, HEAP, lsl #32
    // 0x93ac3c: stur            x3, [fp, #-0x18]
    // 0x93ac40: cmp             w3, NULL
    // 0x93ac44: b.ne            #0x93ac88
    // 0x93ac48: LoadField: r2 = r0->field_1b
    //     0x93ac48: ldur            w2, [x0, #0x1b]
    // 0x93ac4c: DecompressPointer r2
    //     0x93ac4c: add             x2, x2, HEAP, lsl #32
    // 0x93ac50: stur            x2, [fp, #-0x10]
    // 0x93ac54: r0 = TextEditingValue()
    //     0x93ac54: bl              #0x6aef08  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93ac58: mov             x1, x0
    // 0x93ac5c: ldur            x0, [fp, #-0x10]
    // 0x93ac60: StoreField: r1->field_7 = r0
    //     0x93ac60: stur            w0, [x1, #7]
    // 0x93ac64: r0 = Instance_TextSelection
    //     0x93ac64: add             x0, PP, #0x24, lsl #12  ; [pp+0x24d68] Obj!TextSelection@e263b1
    //     0x93ac68: ldr             x0, [x0, #0xd68]
    // 0x93ac6c: StoreField: r1->field_b = r0
    //     0x93ac6c: stur            w0, [x1, #0xb]
    // 0x93ac70: r0 = Instance_TextRange
    //     0x93ac70: ldr             x0, [PP, #0x7268]  ; [pp+0x7268] Obj!TextRange@e26391
    // 0x93ac74: StoreField: r1->field_f = r0
    //     0x93ac74: stur            w0, [x1, #0xf]
    // 0x93ac78: mov             x2, x1
    // 0x93ac7c: ldur            x1, [fp, #-8]
    // 0x93ac80: r0 = _createLocalController()
    //     0x93ac80: bl              #0x93acc0  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_createLocalController
    // 0x93ac84: b               #0x93aca4
    // 0x93ac88: ldur            x2, [fp, #-8]
    // 0x93ac8c: r1 = Function '_handleControllerChanged@611147271':.
    //     0x93ac8c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43920] AnonymousClosure: (0x93b00c), in [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_handleControllerChanged (0x93b044)
    //     0x93ac90: ldr             x1, [x1, #0x920]
    // 0x93ac94: r0 = AllocateClosure()
    //     0x93ac94: bl              #0xec1630  ; AllocateClosureStub
    // 0x93ac98: ldur            x1, [fp, #-0x18]
    // 0x93ac9c: mov             x2, x0
    // 0x93aca0: r0 = addListener()
    //     0x93aca0: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x93aca4: r0 = Null
    //     0x93aca4: mov             x0, NULL
    // 0x93aca8: LeaveFrame
    //     0x93aca8: mov             SP, fp
    //     0x93acac: ldp             fp, lr, [SP], #0x10
    // 0x93acb0: ret
    //     0x93acb0: ret             
    // 0x93acb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93acb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93acb8: b               #0x93ac18
    // 0x93acbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93acbc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _createLocalController(/* No info */) {
    // ** addr: 0x93acc0, size: 0xc8
    // 0x93acc0: EnterFrame
    //     0x93acc0: stp             fp, lr, [SP, #-0x10]!
    //     0x93acc4: mov             fp, SP
    // 0x93acc8: AllocStack(0x18)
    //     0x93acc8: sub             SP, SP, #0x18
    // 0x93accc: SetupParameters(_TextFormFieldState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x93accc: mov             x0, x1
    //     0x93acd0: stur            x1, [fp, #-8]
    //     0x93acd4: stur            x2, [fp, #-0x10]
    // 0x93acd8: CheckStackOverflow
    //     0x93acd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93acdc: cmp             SP, x16
    //     0x93ace0: b.ls            #0x93ad80
    // 0x93ace4: r1 = <TextEditingController>
    //     0x93ace4: add             x1, PP, #0x43, lsl #12  ; [pp+0x43948] TypeArguments: <TextEditingController>
    //     0x93ace8: ldr             x1, [x1, #0x948]
    // 0x93acec: r0 = RestorableTextEditingController()
    //     0x93acec: bl              #0x93aa7c  ; AllocateRestorableTextEditingControllerStub -> RestorableTextEditingController (size=0x3c)
    // 0x93acf0: mov             x1, x0
    // 0x93acf4: ldur            x0, [fp, #-0x10]
    // 0x93acf8: stur            x1, [fp, #-0x18]
    // 0x93acfc: StoreField: r1->field_37 = r0
    //     0x93acfc: stur            w0, [x1, #0x37]
    // 0x93ad00: r0 = false
    //     0x93ad00: add             x0, NULL, #0x30  ; false
    // 0x93ad04: StoreField: r1->field_27 = r0
    //     0x93ad04: stur            w0, [x1, #0x27]
    // 0x93ad08: StoreField: r1->field_7 = rZR
    //     0x93ad08: stur            xzr, [x1, #7]
    // 0x93ad0c: StoreField: r1->field_13 = rZR
    //     0x93ad0c: stur            xzr, [x1, #0x13]
    // 0x93ad10: StoreField: r1->field_1b = rZR
    //     0x93ad10: stur            xzr, [x1, #0x1b]
    // 0x93ad14: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0x93ad14: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93ad18: ldr             x0, [x0, #0xca8]
    //     0x93ad1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93ad20: cmp             w0, w16
    //     0x93ad24: b.ne            #0x93ad30
    //     0x93ad28: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0x93ad2c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x93ad30: mov             x1, x0
    // 0x93ad34: ldur            x0, [fp, #-0x18]
    // 0x93ad38: StoreField: r0->field_f = r1
    //     0x93ad38: stur            w1, [x0, #0xf]
    // 0x93ad3c: ldur            x2, [fp, #-8]
    // 0x93ad40: StoreField: r2->field_33 = r0
    //     0x93ad40: stur            w0, [x2, #0x33]
    //     0x93ad44: ldurb           w16, [x2, #-1]
    //     0x93ad48: ldurb           w17, [x0, #-1]
    //     0x93ad4c: and             x16, x17, x16, lsr #2
    //     0x93ad50: tst             x16, HEAP, lsr #32
    //     0x93ad54: b.eq            #0x93ad5c
    //     0x93ad58: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x93ad5c: mov             x1, x2
    // 0x93ad60: r0 = restorePending()
    //     0x93ad60: bl              #0x93aee4  ; [package:flutter/src/widgets/form.dart] _FormFieldState&State&RestorationMixin::restorePending
    // 0x93ad64: tbz             w0, #4, #0x93ad70
    // 0x93ad68: ldur            x1, [fp, #-8]
    // 0x93ad6c: r0 = _registerController()
    //     0x93ad6c: bl              #0x93ad88  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_registerController
    // 0x93ad70: r0 = Null
    //     0x93ad70: mov             x0, NULL
    // 0x93ad74: LeaveFrame
    //     0x93ad74: mov             SP, fp
    //     0x93ad78: ldp             fp, lr, [SP], #0x10
    // 0x93ad7c: ret
    //     0x93ad7c: ret             
    // 0x93ad80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93ad80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93ad84: b               #0x93ace4
  }
  _ _registerController(/* No info */) {
    // ** addr: 0x93ad88, size: 0x48
    // 0x93ad88: EnterFrame
    //     0x93ad88: stp             fp, lr, [SP, #-0x10]!
    //     0x93ad8c: mov             fp, SP
    // 0x93ad90: CheckStackOverflow
    //     0x93ad90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93ad94: cmp             SP, x16
    //     0x93ad98: b.ls            #0x93adc4
    // 0x93ad9c: LoadField: r2 = r1->field_33
    //     0x93ad9c: ldur            w2, [x1, #0x33]
    // 0x93ada0: DecompressPointer r2
    //     0x93ada0: add             x2, x2, HEAP, lsl #32
    // 0x93ada4: cmp             w2, NULL
    // 0x93ada8: b.eq            #0x93adcc
    // 0x93adac: r3 = "controller"
    //     0x93adac: ldr             x3, [PP, #0x32c8]  ; [pp+0x32c8] "controller"
    // 0x93adb0: r0 = registerForRestoration()
    //     0x93adb0: bl              #0x93add0  ; [package:flutter/src/widgets/form.dart] _FormFieldState&State&RestorationMixin::registerForRestoration
    // 0x93adb4: r0 = Null
    //     0x93adb4: mov             x0, NULL
    // 0x93adb8: LeaveFrame
    //     0x93adb8: mov             SP, fp
    //     0x93adbc: ldp             fp, lr, [SP], #0x10
    // 0x93adc0: ret
    //     0x93adc0: ret             
    // 0x93adc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93adc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93adc8: b               #0x93ad9c
    // 0x93adcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93adcc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _handleControllerChanged(dynamic) {
    // ** addr: 0x93b00c, size: 0x38
    // 0x93b00c: EnterFrame
    //     0x93b00c: stp             fp, lr, [SP, #-0x10]!
    //     0x93b010: mov             fp, SP
    // 0x93b014: ldr             x0, [fp, #0x10]
    // 0x93b018: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x93b018: ldur            w1, [x0, #0x17]
    // 0x93b01c: DecompressPointer r1
    //     0x93b01c: add             x1, x1, HEAP, lsl #32
    // 0x93b020: CheckStackOverflow
    //     0x93b020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b024: cmp             SP, x16
    //     0x93b028: b.ls            #0x93b03c
    // 0x93b02c: r0 = _handleControllerChanged()
    //     0x93b02c: bl              #0x93b044  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_handleControllerChanged
    // 0x93b030: LeaveFrame
    //     0x93b030: mov             SP, fp
    //     0x93b034: ldp             fp, lr, [SP], #0x10
    // 0x93b038: ret
    //     0x93b038: ret             
    // 0x93b03c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b03c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b040: b               #0x93b02c
  }
  _ _handleControllerChanged(/* No info */) {
    // ** addr: 0x93b044, size: 0xc0
    // 0x93b044: EnterFrame
    //     0x93b044: stp             fp, lr, [SP, #-0x10]!
    //     0x93b048: mov             fp, SP
    // 0x93b04c: AllocStack(0x20)
    //     0x93b04c: sub             SP, SP, #0x20
    // 0x93b050: SetupParameters(_TextFormFieldState this /* r1 => r0, fp-0x8 */)
    //     0x93b050: mov             x0, x1
    //     0x93b054: stur            x1, [fp, #-8]
    // 0x93b058: CheckStackOverflow
    //     0x93b058: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b05c: cmp             SP, x16
    //     0x93b060: b.ls            #0x93b0fc
    // 0x93b064: mov             x1, x0
    // 0x93b068: r0 = _effectiveController()
    //     0x93b068: bl              #0x93b454  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_effectiveController
    // 0x93b06c: LoadField: r1 = r0->field_27
    //     0x93b06c: ldur            w1, [x0, #0x27]
    // 0x93b070: DecompressPointer r1
    //     0x93b070: add             x1, x1, HEAP, lsl #32
    // 0x93b074: LoadField: r0 = r1->field_7
    //     0x93b074: ldur            w0, [x1, #7]
    // 0x93b078: DecompressPointer r0
    //     0x93b078: add             x0, x0, HEAP, lsl #32
    // 0x93b07c: ldur            x1, [fp, #-8]
    // 0x93b080: stur            x0, [fp, #-0x10]
    // 0x93b084: LoadField: r0 = r1->field_23
    //     0x93b084: ldur            w0, [x1, #0x23]
    // 0x93b088: DecompressPointer r0
    //     0x93b088: add             x0, x0, HEAP, lsl #32
    // 0x93b08c: r16 = Sentinel
    //     0x93b08c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x93b090: cmp             w0, w16
    // 0x93b094: b.ne            #0x93b0a4
    // 0x93b098: r2 = _value
    //     0x93b098: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fef8] Field <FormFieldState._value@282032539>: late (offset: 0x24)
    //     0x93b09c: ldr             x2, [x2, #0xef8]
    // 0x93b0a0: r0 = InitLateInstanceField()
    //     0x93b0a0: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0x93b0a4: mov             x1, x0
    // 0x93b0a8: ldur            x0, [fp, #-0x10]
    // 0x93b0ac: r2 = LoadClassIdInstr(r0)
    //     0x93b0ac: ldur            x2, [x0, #-1]
    //     0x93b0b0: ubfx            x2, x2, #0xc, #0x14
    // 0x93b0b4: stp             x1, x0, [SP]
    // 0x93b0b8: mov             x0, x2
    // 0x93b0bc: mov             lr, x0
    // 0x93b0c0: ldr             lr, [x21, lr, lsl #3]
    // 0x93b0c4: blr             lr
    // 0x93b0c8: tbz             w0, #4, #0x93b0ec
    // 0x93b0cc: ldur            x1, [fp, #-8]
    // 0x93b0d0: r0 = _effectiveController()
    //     0x93b0d0: bl              #0x93b454  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_effectiveController
    // 0x93b0d4: LoadField: r1 = r0->field_27
    //     0x93b0d4: ldur            w1, [x0, #0x27]
    // 0x93b0d8: DecompressPointer r1
    //     0x93b0d8: add             x1, x1, HEAP, lsl #32
    // 0x93b0dc: LoadField: r2 = r1->field_7
    //     0x93b0dc: ldur            w2, [x1, #7]
    // 0x93b0e0: DecompressPointer r2
    //     0x93b0e0: add             x2, x2, HEAP, lsl #32
    // 0x93b0e4: ldur            x1, [fp, #-8]
    // 0x93b0e8: r0 = didChange()
    //     0x93b0e8: bl              #0x93b104  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::didChange
    // 0x93b0ec: r0 = Null
    //     0x93b0ec: mov             x0, NULL
    // 0x93b0f0: LeaveFrame
    //     0x93b0f0: mov             SP, fp
    //     0x93b0f4: ldp             fp, lr, [SP], #0x10
    // 0x93b0f8: ret
    //     0x93b0f8: ret             
    // 0x93b0fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b0fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b100: b               #0x93b064
  }
  _ didChange(/* No info */) {
    // ** addr: 0x93b104, size: 0x9c
    // 0x93b104: EnterFrame
    //     0x93b104: stp             fp, lr, [SP, #-0x10]!
    //     0x93b108: mov             fp, SP
    // 0x93b10c: AllocStack(0x20)
    //     0x93b10c: sub             SP, SP, #0x20
    // 0x93b110: SetupParameters(_TextFormFieldState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x93b110: mov             x3, x1
    //     0x93b114: mov             x0, x2
    //     0x93b118: stur            x1, [fp, #-8]
    //     0x93b11c: stur            x2, [fp, #-0x10]
    // 0x93b120: CheckStackOverflow
    //     0x93b120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b124: cmp             SP, x16
    //     0x93b128: b.ls            #0x93b198
    // 0x93b12c: mov             x1, x3
    // 0x93b130: mov             x2, x0
    // 0x93b134: r0 = didChange()
    //     0x93b134: bl              #0x93b1a0  ; [package:flutter/src/widgets/form.dart] FormFieldState::didChange
    // 0x93b138: ldur            x1, [fp, #-8]
    // 0x93b13c: r0 = _effectiveController()
    //     0x93b13c: bl              #0x93b454  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_effectiveController
    // 0x93b140: LoadField: r1 = r0->field_27
    //     0x93b140: ldur            w1, [x0, #0x27]
    // 0x93b144: DecompressPointer r1
    //     0x93b144: add             x1, x1, HEAP, lsl #32
    // 0x93b148: LoadField: r0 = r1->field_7
    //     0x93b148: ldur            w0, [x1, #7]
    // 0x93b14c: DecompressPointer r0
    //     0x93b14c: add             x0, x0, HEAP, lsl #32
    // 0x93b150: r1 = LoadClassIdInstr(r0)
    //     0x93b150: ldur            x1, [x0, #-1]
    //     0x93b154: ubfx            x1, x1, #0xc, #0x14
    // 0x93b158: ldur            x16, [fp, #-0x10]
    // 0x93b15c: stp             x16, x0, [SP]
    // 0x93b160: mov             x0, x1
    // 0x93b164: mov             lr, x0
    // 0x93b168: ldr             lr, [x21, lr, lsl #3]
    // 0x93b16c: blr             lr
    // 0x93b170: tbz             w0, #4, #0x93b188
    // 0x93b174: ldur            x1, [fp, #-8]
    // 0x93b178: r0 = _effectiveController()
    //     0x93b178: bl              #0x93b454  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_effectiveController
    // 0x93b17c: mov             x1, x0
    // 0x93b180: ldur            x2, [fp, #-0x10]
    // 0x93b184: r0 = text=()
    //     0x93b184: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0x93b188: r0 = Null
    //     0x93b188: mov             x0, NULL
    // 0x93b18c: LeaveFrame
    //     0x93b18c: mov             SP, fp
    //     0x93b190: ldp             fp, lr, [SP], #0x10
    // 0x93b194: ret
    //     0x93b194: ret             
    // 0x93b198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b19c: b               #0x93b12c
  }
  get _ _effectiveController(/* No info */) {
    // ** addr: 0x93b454, size: 0x6c
    // 0x93b454: EnterFrame
    //     0x93b454: stp             fp, lr, [SP, #-0x10]!
    //     0x93b458: mov             fp, SP
    // 0x93b45c: LoadField: r2 = r1->field_b
    //     0x93b45c: ldur            w2, [x1, #0xb]
    // 0x93b460: DecompressPointer r2
    //     0x93b460: add             x2, x2, HEAP, lsl #32
    // 0x93b464: cmp             w2, NULL
    // 0x93b468: b.eq            #0x93b4b4
    // 0x93b46c: LoadField: r3 = r2->field_2b
    //     0x93b46c: ldur            w3, [x2, #0x2b]
    // 0x93b470: DecompressPointer r3
    //     0x93b470: add             x3, x3, HEAP, lsl #32
    // 0x93b474: cmp             w3, NULL
    // 0x93b478: b.ne            #0x93b4a4
    // 0x93b47c: LoadField: r2 = r1->field_33
    //     0x93b47c: ldur            w2, [x1, #0x33]
    // 0x93b480: DecompressPointer r2
    //     0x93b480: add             x2, x2, HEAP, lsl #32
    // 0x93b484: cmp             w2, NULL
    // 0x93b488: b.eq            #0x93b4b8
    // 0x93b48c: LoadField: r1 = r2->field_33
    //     0x93b48c: ldur            w1, [x2, #0x33]
    // 0x93b490: DecompressPointer r1
    //     0x93b490: add             x1, x1, HEAP, lsl #32
    // 0x93b494: cmp             w1, NULL
    // 0x93b498: b.eq            #0x93b4bc
    // 0x93b49c: mov             x0, x1
    // 0x93b4a0: b               #0x93b4a8
    // 0x93b4a4: mov             x0, x3
    // 0x93b4a8: LeaveFrame
    //     0x93b4a8: mov             SP, fp
    //     0x93b4ac: ldp             fp, lr, [SP], #0x10
    // 0x93b4b0: ret
    //     0x93b4b0: ret             
    // 0x93b4b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93b4b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93b4b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93b4b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93b4bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93b4bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x98d19c, size: 0x214
    // 0x98d19c: EnterFrame
    //     0x98d19c: stp             fp, lr, [SP, #-0x10]!
    //     0x98d1a0: mov             fp, SP
    // 0x98d1a4: AllocStack(0x18)
    //     0x98d1a4: sub             SP, SP, #0x18
    // 0x98d1a8: SetupParameters(_TextFormFieldState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x98d1a8: mov             x4, x1
    //     0x98d1ac: mov             x3, x2
    //     0x98d1b0: stur            x1, [fp, #-8]
    //     0x98d1b4: stur            x2, [fp, #-0x10]
    // 0x98d1b8: CheckStackOverflow
    //     0x98d1b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98d1bc: cmp             SP, x16
    //     0x98d1c0: b.ls            #0x98d390
    // 0x98d1c4: mov             x0, x3
    // 0x98d1c8: r2 = Null
    //     0x98d1c8: mov             x2, NULL
    // 0x98d1cc: r1 = Null
    //     0x98d1cc: mov             x1, NULL
    // 0x98d1d0: r4 = 60
    //     0x98d1d0: movz            x4, #0x3c
    // 0x98d1d4: branchIfSmi(r0, 0x98d1e0)
    //     0x98d1d4: tbz             w0, #0, #0x98d1e0
    // 0x98d1d8: r4 = LoadClassIdInstr(r0)
    //     0x98d1d8: ldur            x4, [x0, #-1]
    //     0x98d1dc: ubfx            x4, x4, #0xc, #0x14
    // 0x98d1e0: r17 = 4808
    //     0x98d1e0: movz            x17, #0x12c8
    // 0x98d1e4: cmp             x4, x17
    // 0x98d1e8: b.eq            #0x98d200
    // 0x98d1ec: r8 = TextFormField
    //     0x98d1ec: add             x8, PP, #0x43, lsl #12  ; [pp+0x43930] Type: TextFormField
    //     0x98d1f0: ldr             x8, [x8, #0x930]
    // 0x98d1f4: r3 = Null
    //     0x98d1f4: add             x3, PP, #0x43, lsl #12  ; [pp+0x43938] Null
    //     0x98d1f8: ldr             x3, [x3, #0x938]
    // 0x98d1fc: r0 = DefaultTypeTest()
    //     0x98d1fc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x98d200: ldur            x1, [fp, #-8]
    // 0x98d204: ldur            x2, [fp, #-0x10]
    // 0x98d208: r0 = didUpdateWidget()
    //     0x98d208: bl              #0x98d3e0  ; [package:flutter/src/widgets/form.dart] FormFieldState::didUpdateWidget
    // 0x98d20c: ldur            x0, [fp, #-8]
    // 0x98d210: LoadField: r1 = r0->field_b
    //     0x98d210: ldur            w1, [x0, #0xb]
    // 0x98d214: DecompressPointer r1
    //     0x98d214: add             x1, x1, HEAP, lsl #32
    // 0x98d218: cmp             w1, NULL
    // 0x98d21c: b.eq            #0x98d398
    // 0x98d220: LoadField: r2 = r1->field_2b
    //     0x98d220: ldur            w2, [x1, #0x2b]
    // 0x98d224: DecompressPointer r2
    //     0x98d224: add             x2, x2, HEAP, lsl #32
    // 0x98d228: ldur            x1, [fp, #-0x10]
    // 0x98d22c: LoadField: r3 = r1->field_2b
    //     0x98d22c: ldur            w3, [x1, #0x2b]
    // 0x98d230: DecompressPointer r3
    //     0x98d230: add             x3, x3, HEAP, lsl #32
    // 0x98d234: stur            x3, [fp, #-0x18]
    // 0x98d238: cmp             w2, w3
    // 0x98d23c: b.eq            #0x98d380
    // 0x98d240: cmp             w3, NULL
    // 0x98d244: b.eq            #0x98d268
    // 0x98d248: mov             x2, x0
    // 0x98d24c: r1 = Function '_handleControllerChanged@611147271':.
    //     0x98d24c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43920] AnonymousClosure: (0x93b00c), in [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_handleControllerChanged (0x93b044)
    //     0x98d250: ldr             x1, [x1, #0x920]
    // 0x98d254: r0 = AllocateClosure()
    //     0x98d254: bl              #0xec1630  ; AllocateClosureStub
    // 0x98d258: ldur            x1, [fp, #-0x18]
    // 0x98d25c: mov             x2, x0
    // 0x98d260: r0 = removeListener()
    //     0x98d260: bl              #0xa8a5dc  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x98d264: ldur            x0, [fp, #-8]
    // 0x98d268: LoadField: r1 = r0->field_b
    //     0x98d268: ldur            w1, [x0, #0xb]
    // 0x98d26c: DecompressPointer r1
    //     0x98d26c: add             x1, x1, HEAP, lsl #32
    // 0x98d270: cmp             w1, NULL
    // 0x98d274: b.eq            #0x98d39c
    // 0x98d278: LoadField: r3 = r1->field_2b
    //     0x98d278: ldur            w3, [x1, #0x2b]
    // 0x98d27c: DecompressPointer r3
    //     0x98d27c: add             x3, x3, HEAP, lsl #32
    // 0x98d280: stur            x3, [fp, #-0x10]
    // 0x98d284: cmp             w3, NULL
    // 0x98d288: b.eq            #0x98d2a8
    // 0x98d28c: mov             x2, x0
    // 0x98d290: r1 = Function '_handleControllerChanged@611147271':.
    //     0x98d290: add             x1, PP, #0x43, lsl #12  ; [pp+0x43920] AnonymousClosure: (0x93b00c), in [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_handleControllerChanged (0x93b044)
    //     0x98d294: ldr             x1, [x1, #0x920]
    // 0x98d298: r0 = AllocateClosure()
    //     0x98d298: bl              #0xec1630  ; AllocateClosureStub
    // 0x98d29c: ldur            x1, [fp, #-0x10]
    // 0x98d2a0: mov             x2, x0
    // 0x98d2a4: r0 = addListener()
    //     0x98d2a4: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x98d2a8: ldur            x0, [fp, #-0x18]
    // 0x98d2ac: cmp             w0, NULL
    // 0x98d2b0: b.eq            #0x98d2e8
    // 0x98d2b4: ldur            x3, [fp, #-8]
    // 0x98d2b8: LoadField: r1 = r3->field_b
    //     0x98d2b8: ldur            w1, [x3, #0xb]
    // 0x98d2bc: DecompressPointer r1
    //     0x98d2bc: add             x1, x1, HEAP, lsl #32
    // 0x98d2c0: cmp             w1, NULL
    // 0x98d2c4: b.eq            #0x98d3a0
    // 0x98d2c8: LoadField: r2 = r1->field_2b
    //     0x98d2c8: ldur            w2, [x1, #0x2b]
    // 0x98d2cc: DecompressPointer r2
    //     0x98d2cc: add             x2, x2, HEAP, lsl #32
    // 0x98d2d0: cmp             w2, NULL
    // 0x98d2d4: b.ne            #0x98d2e8
    // 0x98d2d8: LoadField: r2 = r0->field_27
    //     0x98d2d8: ldur            w2, [x0, #0x27]
    // 0x98d2dc: DecompressPointer r2
    //     0x98d2dc: add             x2, x2, HEAP, lsl #32
    // 0x98d2e0: mov             x1, x3
    // 0x98d2e4: r0 = _createLocalController()
    //     0x98d2e4: bl              #0x93acc0  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_createLocalController
    // 0x98d2e8: ldur            x3, [fp, #-8]
    // 0x98d2ec: LoadField: r0 = r3->field_b
    //     0x98d2ec: ldur            w0, [x3, #0xb]
    // 0x98d2f0: DecompressPointer r0
    //     0x98d2f0: add             x0, x0, HEAP, lsl #32
    // 0x98d2f4: cmp             w0, NULL
    // 0x98d2f8: b.eq            #0x98d3a4
    // 0x98d2fc: LoadField: r1 = r0->field_2b
    //     0x98d2fc: ldur            w1, [x0, #0x2b]
    // 0x98d300: DecompressPointer r1
    //     0x98d300: add             x1, x1, HEAP, lsl #32
    // 0x98d304: cmp             w1, NULL
    // 0x98d308: b.eq            #0x98d380
    // 0x98d30c: ldur            x2, [fp, #-0x18]
    // 0x98d310: LoadField: r0 = r1->field_27
    //     0x98d310: ldur            w0, [x1, #0x27]
    // 0x98d314: DecompressPointer r0
    //     0x98d314: add             x0, x0, HEAP, lsl #32
    // 0x98d318: LoadField: r1 = r0->field_7
    //     0x98d318: ldur            w1, [x0, #7]
    // 0x98d31c: DecompressPointer r1
    //     0x98d31c: add             x1, x1, HEAP, lsl #32
    // 0x98d320: mov             x0, x1
    // 0x98d324: StoreField: r3->field_23 = r0
    //     0x98d324: stur            w0, [x3, #0x23]
    //     0x98d328: ldurb           w16, [x3, #-1]
    //     0x98d32c: ldurb           w17, [x0, #-1]
    //     0x98d330: and             x16, x17, x16, lsr #2
    //     0x98d334: tst             x16, HEAP, lsr #32
    //     0x98d338: b.eq            #0x98d340
    //     0x98d33c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x98d340: cmp             w2, NULL
    // 0x98d344: b.ne            #0x98d380
    // 0x98d348: LoadField: r2 = r3->field_33
    //     0x98d348: ldur            w2, [x3, #0x33]
    // 0x98d34c: DecompressPointer r2
    //     0x98d34c: add             x2, x2, HEAP, lsl #32
    // 0x98d350: cmp             w2, NULL
    // 0x98d354: b.eq            #0x98d3a8
    // 0x98d358: mov             x1, x3
    // 0x98d35c: r0 = unregisterFromRestoration()
    //     0x98d35c: bl              #0x98d3b0  ; [package:flutter/src/widgets/form.dart] _FormFieldState&State&RestorationMixin::unregisterFromRestoration
    // 0x98d360: ldur            x0, [fp, #-8]
    // 0x98d364: LoadField: r1 = r0->field_33
    //     0x98d364: ldur            w1, [x0, #0x33]
    // 0x98d368: DecompressPointer r1
    //     0x98d368: add             x1, x1, HEAP, lsl #32
    // 0x98d36c: cmp             w1, NULL
    // 0x98d370: b.eq            #0x98d3ac
    // 0x98d374: r0 = dispose()
    //     0x98d374: bl              #0xa87904  ; [package:flutter/src/widgets/restoration_properties.dart] RestorableChangeNotifier::dispose
    // 0x98d378: ldur            x1, [fp, #-8]
    // 0x98d37c: StoreField: r1->field_33 = rNULL
    //     0x98d37c: stur            NULL, [x1, #0x33]
    // 0x98d380: r0 = Null
    //     0x98d380: mov             x0, NULL
    // 0x98d384: LeaveFrame
    //     0x98d384: mov             SP, fp
    //     0x98d388: ldp             fp, lr, [SP], #0x10
    // 0x98d38c: ret
    //     0x98d38c: ret             
    // 0x98d390: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98d390: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98d394: b               #0x98d1c4
    // 0x98d398: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98d398: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98d39c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98d39c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98d3a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98d3a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98d3a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98d3a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98d3a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98d3a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98d3ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98d3ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ restoreState(/* No info */) {
    // ** addr: 0x9a4f74, size: 0x98
    // 0x9a4f74: EnterFrame
    //     0x9a4f74: stp             fp, lr, [SP, #-0x10]!
    //     0x9a4f78: mov             fp, SP
    // 0x9a4f7c: AllocStack(0x8)
    //     0x9a4f7c: sub             SP, SP, #8
    // 0x9a4f80: SetupParameters(_TextFormFieldState this /* r1 => r0, fp-0x8 */)
    //     0x9a4f80: mov             x0, x1
    //     0x9a4f84: stur            x1, [fp, #-8]
    // 0x9a4f88: CheckStackOverflow
    //     0x9a4f88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a4f8c: cmp             SP, x16
    //     0x9a4f90: b.ls            #0x9a5004
    // 0x9a4f94: mov             x1, x0
    // 0x9a4f98: r0 = restoreState()
    //     0x9a4f98: bl              #0x9a500c  ; [package:flutter/src/widgets/form.dart] FormFieldState::restoreState
    // 0x9a4f9c: ldur            x0, [fp, #-8]
    // 0x9a4fa0: LoadField: r1 = r0->field_33
    //     0x9a4fa0: ldur            w1, [x0, #0x33]
    // 0x9a4fa4: DecompressPointer r1
    //     0x9a4fa4: add             x1, x1, HEAP, lsl #32
    // 0x9a4fa8: cmp             w1, NULL
    // 0x9a4fac: b.eq            #0x9a4fb8
    // 0x9a4fb0: mov             x1, x0
    // 0x9a4fb4: r0 = _registerController()
    //     0x9a4fb4: bl              #0x93ad88  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_registerController
    // 0x9a4fb8: ldur            x0, [fp, #-8]
    // 0x9a4fbc: mov             x1, x0
    // 0x9a4fc0: r0 = _effectiveController()
    //     0x9a4fc0: bl              #0x93b454  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_effectiveController
    // 0x9a4fc4: LoadField: r1 = r0->field_27
    //     0x9a4fc4: ldur            w1, [x0, #0x27]
    // 0x9a4fc8: DecompressPointer r1
    //     0x9a4fc8: add             x1, x1, HEAP, lsl #32
    // 0x9a4fcc: LoadField: r0 = r1->field_7
    //     0x9a4fcc: ldur            w0, [x1, #7]
    // 0x9a4fd0: DecompressPointer r0
    //     0x9a4fd0: add             x0, x0, HEAP, lsl #32
    // 0x9a4fd4: ldur            x1, [fp, #-8]
    // 0x9a4fd8: StoreField: r1->field_23 = r0
    //     0x9a4fd8: stur            w0, [x1, #0x23]
    //     0x9a4fdc: ldurb           w16, [x1, #-1]
    //     0x9a4fe0: ldurb           w17, [x0, #-1]
    //     0x9a4fe4: and             x16, x17, x16, lsr #2
    //     0x9a4fe8: tst             x16, HEAP, lsr #32
    //     0x9a4fec: b.eq            #0x9a4ff4
    //     0x9a4ff0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9a4ff4: r0 = Null
    //     0x9a4ff4: mov             x0, NULL
    // 0x9a4ff8: LeaveFrame
    //     0x9a4ff8: mov             SP, fp
    //     0x9a4ffc: ldp             fp, lr, [SP], #0x10
    // 0x9a5000: ret
    //     0x9a5000: ret             
    // 0x9a5004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a5004: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a5008: b               #0x9a4f94
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7ed0c, size: 0x9c
    // 0xa7ed0c: EnterFrame
    //     0xa7ed0c: stp             fp, lr, [SP, #-0x10]!
    //     0xa7ed10: mov             fp, SP
    // 0xa7ed14: AllocStack(0x10)
    //     0xa7ed14: sub             SP, SP, #0x10
    // 0xa7ed18: SetupParameters(_TextFormFieldState this /* r1 => r0, fp-0x10 */)
    //     0xa7ed18: mov             x0, x1
    //     0xa7ed1c: stur            x1, [fp, #-0x10]
    // 0xa7ed20: CheckStackOverflow
    //     0xa7ed20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7ed24: cmp             SP, x16
    //     0xa7ed28: b.ls            #0xa7ed9c
    // 0xa7ed2c: LoadField: r1 = r0->field_b
    //     0xa7ed2c: ldur            w1, [x0, #0xb]
    // 0xa7ed30: DecompressPointer r1
    //     0xa7ed30: add             x1, x1, HEAP, lsl #32
    // 0xa7ed34: cmp             w1, NULL
    // 0xa7ed38: b.eq            #0xa7eda4
    // 0xa7ed3c: LoadField: r3 = r1->field_2b
    //     0xa7ed3c: ldur            w3, [x1, #0x2b]
    // 0xa7ed40: DecompressPointer r3
    //     0xa7ed40: add             x3, x3, HEAP, lsl #32
    // 0xa7ed44: stur            x3, [fp, #-8]
    // 0xa7ed48: cmp             w3, NULL
    // 0xa7ed4c: b.eq            #0xa7ed70
    // 0xa7ed50: mov             x2, x0
    // 0xa7ed54: r1 = Function '_handleControllerChanged@611147271':.
    //     0xa7ed54: add             x1, PP, #0x43, lsl #12  ; [pp+0x43920] AnonymousClosure: (0x93b00c), in [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_handleControllerChanged (0x93b044)
    //     0xa7ed58: ldr             x1, [x1, #0x920]
    // 0xa7ed5c: r0 = AllocateClosure()
    //     0xa7ed5c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7ed60: ldur            x1, [fp, #-8]
    // 0xa7ed64: mov             x2, x0
    // 0xa7ed68: r0 = removeListener()
    //     0xa7ed68: bl              #0xa8a5dc  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xa7ed6c: ldur            x0, [fp, #-0x10]
    // 0xa7ed70: LoadField: r1 = r0->field_33
    //     0xa7ed70: ldur            w1, [x0, #0x33]
    // 0xa7ed74: DecompressPointer r1
    //     0xa7ed74: add             x1, x1, HEAP, lsl #32
    // 0xa7ed78: cmp             w1, NULL
    // 0xa7ed7c: b.eq            #0xa7ed84
    // 0xa7ed80: r0 = dispose()
    //     0xa7ed80: bl              #0xa87904  ; [package:flutter/src/widgets/restoration_properties.dart] RestorableChangeNotifier::dispose
    // 0xa7ed84: ldur            x1, [fp, #-0x10]
    // 0xa7ed88: r0 = dispose()
    //     0xa7ed88: bl              #0xa7eda8  ; [package:flutter/src/widgets/form.dart] FormFieldState::dispose
    // 0xa7ed8c: r0 = Null
    //     0xa7ed8c: mov             x0, NULL
    // 0xa7ed90: LeaveFrame
    //     0xa7ed90: mov             SP, fp
    //     0xa7ed94: ldp             fp, lr, [SP], #0x10
    // 0xa7ed98: ret
    //     0xa7ed98: ret             
    // 0xa7ed9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7ed9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7eda0: b               #0xa7ed2c
    // 0xa7eda4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7eda4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4808, size: 0x34, field offset: 0x2c
class TextFormField extends FormField<dynamic> {

  _ TextFormField(/* No info */) {
    // ** addr: 0xa3d5e0, size: 0x710
    // 0xa3d5e0: EnterFrame
    //     0xa3d5e0: stp             fp, lr, [SP, #-0x10]!
    //     0xa3d5e4: mov             fp, SP
    // 0xa3d5e8: AllocStack(0x88)
    //     0xa3d5e8: sub             SP, SP, #0x88
    // 0xa3d5ec: SetupParameters(TextFormField this /* r1 => fp-0x8 */, dynamic _ /* r2 => r2, fp-0x88 */, {dynamic autocorrect = true /* r3, fp-0x80 */, dynamic controller = Null /* r6, fp-0x78 */, dynamic focusNode = Null /* r5, fp-0x70 */, dynamic initialValue = Null /* r8, fp-0x68 */, dynamic inputFormatters = Null /* r7, fp-0x60 */, dynamic key = Null /* r10, fp-0x58 */, dynamic keyboardType = Null /* r9, fp-0x50 */, dynamic maxLength = Null /* r11, fp-0x48 */, dynamic onChanged = Null /* r12, fp-0x40 */, dynamic onTap = Null /* r13, fp-0x38 */, dynamic readOnly = false /* r14, fp-0x30 */, dynamic style = Null /* r19, fp-0x28 */, dynamic textAlign = Instance_TextAlign /* fp-0x10 */, dynamic textDirection = Null /* r1, fp-0x20 */, dynamic validator = Null /* r0, fp-0x18 */})
    //     0xa3d5ec: stur            x1, [fp, #-8]
    //     0xa3d5f0: stur            x2, [fp, #-0x88]
    //     0xa3d5f4: ldur            w0, [x4, #0x13]
    //     0xa3d5f8: ldur            w3, [x4, #0x1f]
    //     0xa3d5fc: add             x3, x3, HEAP, lsl #32
    //     0xa3d600: ldr             x16, [PP, #0x7a80]  ; [pp+0x7a80] "autocorrect"
    //     0xa3d604: cmp             w3, w16
    //     0xa3d608: b.ne            #0xa3d628
    //     0xa3d60c: ldur            w3, [x4, #0x23]
    //     0xa3d610: add             x3, x3, HEAP, lsl #32
    //     0xa3d614: sub             w5, w0, w3
    //     0xa3d618: add             x3, fp, w5, sxtw #2
    //     0xa3d61c: ldr             x3, [x3, #8]
    //     0xa3d620: movz            x5, #0x1
    //     0xa3d624: b               #0xa3d630
    //     0xa3d628: movz            x5, #0
    //     0xa3d62c: add             x3, NULL, #0x20  ; true
    //     0xa3d630: stur            x3, [fp, #-0x80]
    //     0xa3d634: lsl             x6, x5, #1
    //     0xa3d638: lsl             w7, w6, #1
    //     0xa3d63c: add             w8, w7, #8
    //     0xa3d640: add             x16, x4, w8, sxtw #1
    //     0xa3d644: ldur            w9, [x16, #0xf]
    //     0xa3d648: add             x9, x9, HEAP, lsl #32
    //     0xa3d64c: ldr             x16, [PP, #0x32c8]  ; [pp+0x32c8] "controller"
    //     0xa3d650: cmp             w9, w16
    //     0xa3d654: b.ne            #0xa3d688
    //     0xa3d658: add             w5, w7, #0xa
    //     0xa3d65c: add             x16, x4, w5, sxtw #1
    //     0xa3d660: ldur            w7, [x16, #0xf]
    //     0xa3d664: add             x7, x7, HEAP, lsl #32
    //     0xa3d668: sub             w5, w0, w7
    //     0xa3d66c: add             x7, fp, w5, sxtw #2
    //     0xa3d670: ldr             x7, [x7, #8]
    //     0xa3d674: add             w5, w6, #2
    //     0xa3d678: sbfx            x6, x5, #1, #0x1f
    //     0xa3d67c: mov             x5, x6
    //     0xa3d680: mov             x6, x7
    //     0xa3d684: b               #0xa3d68c
    //     0xa3d688: mov             x6, NULL
    //     0xa3d68c: stur            x6, [fp, #-0x78]
    //     0xa3d690: lsl             x7, x5, #1
    //     0xa3d694: lsl             w8, w7, #1
    //     0xa3d698: add             w9, w8, #8
    //     0xa3d69c: add             x16, x4, w9, sxtw #1
    //     0xa3d6a0: ldur            w10, [x16, #0xf]
    //     0xa3d6a4: add             x10, x10, HEAP, lsl #32
    //     0xa3d6a8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30078] "focusNode"
    //     0xa3d6ac: ldr             x16, [x16, #0x78]
    //     0xa3d6b0: cmp             w10, w16
    //     0xa3d6b4: b.ne            #0xa3d6e4
    //     0xa3d6b8: add             w5, w8, #0xa
    //     0xa3d6bc: add             x16, x4, w5, sxtw #1
    //     0xa3d6c0: ldur            w8, [x16, #0xf]
    //     0xa3d6c4: add             x8, x8, HEAP, lsl #32
    //     0xa3d6c8: sub             w5, w0, w8
    //     0xa3d6cc: add             x8, fp, w5, sxtw #2
    //     0xa3d6d0: ldr             x8, [x8, #8]
    //     0xa3d6d4: add             w5, w7, #2
    //     0xa3d6d8: sbfx            x7, x5, #1, #0x1f
    //     0xa3d6dc: mov             x5, x8
    //     0xa3d6e0: b               #0xa3d6ec
    //     0xa3d6e4: mov             x7, x5
    //     0xa3d6e8: mov             x5, NULL
    //     0xa3d6ec: stur            x5, [fp, #-0x70]
    //     0xa3d6f0: lsl             x8, x7, #1
    //     0xa3d6f4: lsl             w9, w8, #1
    //     0xa3d6f8: add             w10, w9, #8
    //     0xa3d6fc: add             x16, x4, w10, sxtw #1
    //     0xa3d700: ldur            w11, [x16, #0xf]
    //     0xa3d704: add             x11, x11, HEAP, lsl #32
    //     0xa3d708: add             x16, PP, #0x30, lsl #12  ; [pp+0x30080] "initialValue"
    //     0xa3d70c: ldr             x16, [x16, #0x80]
    //     0xa3d710: cmp             w11, w16
    //     0xa3d714: b.ne            #0xa3d748
    //     0xa3d718: add             w7, w9, #0xa
    //     0xa3d71c: add             x16, x4, w7, sxtw #1
    //     0xa3d720: ldur            w9, [x16, #0xf]
    //     0xa3d724: add             x9, x9, HEAP, lsl #32
    //     0xa3d728: sub             w7, w0, w9
    //     0xa3d72c: add             x9, fp, w7, sxtw #2
    //     0xa3d730: ldr             x9, [x9, #8]
    //     0xa3d734: add             w7, w8, #2
    //     0xa3d738: sbfx            x8, x7, #1, #0x1f
    //     0xa3d73c: mov             x7, x8
    //     0xa3d740: mov             x8, x9
    //     0xa3d744: b               #0xa3d74c
    //     0xa3d748: mov             x8, NULL
    //     0xa3d74c: stur            x8, [fp, #-0x68]
    //     0xa3d750: lsl             x9, x7, #1
    //     0xa3d754: lsl             w10, w9, #1
    //     0xa3d758: add             w11, w10, #8
    //     0xa3d75c: add             x16, x4, w11, sxtw #1
    //     0xa3d760: ldur            w12, [x16, #0xf]
    //     0xa3d764: add             x12, x12, HEAP, lsl #32
    //     0xa3d768: add             x16, PP, #0x30, lsl #12  ; [pp+0x30088] "inputFormatters"
    //     0xa3d76c: ldr             x16, [x16, #0x88]
    //     0xa3d770: cmp             w12, w16
    //     0xa3d774: b.ne            #0xa3d7a4
    //     0xa3d778: add             w7, w10, #0xa
    //     0xa3d77c: add             x16, x4, w7, sxtw #1
    //     0xa3d780: ldur            w10, [x16, #0xf]
    //     0xa3d784: add             x10, x10, HEAP, lsl #32
    //     0xa3d788: sub             w7, w0, w10
    //     0xa3d78c: add             x10, fp, w7, sxtw #2
    //     0xa3d790: ldr             x10, [x10, #8]
    //     0xa3d794: add             w7, w9, #2
    //     0xa3d798: sbfx            x9, x7, #1, #0x1f
    //     0xa3d79c: mov             x7, x10
    //     0xa3d7a0: b               #0xa3d7ac
    //     0xa3d7a4: mov             x9, x7
    //     0xa3d7a8: mov             x7, NULL
    //     0xa3d7ac: stur            x7, [fp, #-0x60]
    //     0xa3d7b0: lsl             x10, x9, #1
    //     0xa3d7b4: lsl             w11, w10, #1
    //     0xa3d7b8: add             w12, w11, #8
    //     0xa3d7bc: add             x16, x4, w12, sxtw #1
    //     0xa3d7c0: ldur            w13, [x16, #0xf]
    //     0xa3d7c4: add             x13, x13, HEAP, lsl #32
    //     0xa3d7c8: ldr             x16, [PP, #0xab8]  ; [pp+0xab8] "key"
    //     0xa3d7cc: cmp             w13, w16
    //     0xa3d7d0: b.ne            #0xa3d804
    //     0xa3d7d4: add             w9, w11, #0xa
    //     0xa3d7d8: add             x16, x4, w9, sxtw #1
    //     0xa3d7dc: ldur            w11, [x16, #0xf]
    //     0xa3d7e0: add             x11, x11, HEAP, lsl #32
    //     0xa3d7e4: sub             w9, w0, w11
    //     0xa3d7e8: add             x11, fp, w9, sxtw #2
    //     0xa3d7ec: ldr             x11, [x11, #8]
    //     0xa3d7f0: add             w9, w10, #2
    //     0xa3d7f4: sbfx            x10, x9, #1, #0x1f
    //     0xa3d7f8: mov             x9, x10
    //     0xa3d7fc: mov             x10, x11
    //     0xa3d800: b               #0xa3d808
    //     0xa3d804: mov             x10, NULL
    //     0xa3d808: stur            x10, [fp, #-0x58]
    //     0xa3d80c: lsl             x11, x9, #1
    //     0xa3d810: lsl             w12, w11, #1
    //     0xa3d814: add             w13, w12, #8
    //     0xa3d818: add             x16, x4, w13, sxtw #1
    //     0xa3d81c: ldur            w14, [x16, #0xf]
    //     0xa3d820: add             x14, x14, HEAP, lsl #32
    //     0xa3d824: add             x16, PP, #0x30, lsl #12  ; [pp+0x30090] "keyboardType"
    //     0xa3d828: ldr             x16, [x16, #0x90]
    //     0xa3d82c: cmp             w14, w16
    //     0xa3d830: b.ne            #0xa3d860
    //     0xa3d834: add             w9, w12, #0xa
    //     0xa3d838: add             x16, x4, w9, sxtw #1
    //     0xa3d83c: ldur            w12, [x16, #0xf]
    //     0xa3d840: add             x12, x12, HEAP, lsl #32
    //     0xa3d844: sub             w9, w0, w12
    //     0xa3d848: add             x12, fp, w9, sxtw #2
    //     0xa3d84c: ldr             x12, [x12, #8]
    //     0xa3d850: add             w9, w11, #2
    //     0xa3d854: sbfx            x11, x9, #1, #0x1f
    //     0xa3d858: mov             x9, x12
    //     0xa3d85c: b               #0xa3d868
    //     0xa3d860: mov             x11, x9
    //     0xa3d864: mov             x9, NULL
    //     0xa3d868: stur            x9, [fp, #-0x50]
    //     0xa3d86c: lsl             x12, x11, #1
    //     0xa3d870: lsl             w13, w12, #1
    //     0xa3d874: add             w14, w13, #8
    //     0xa3d878: add             x16, x4, w14, sxtw #1
    //     0xa3d87c: ldur            w19, [x16, #0xf]
    //     0xa3d880: add             x19, x19, HEAP, lsl #32
    //     0xa3d884: add             x16, PP, #0x30, lsl #12  ; [pp+0x30098] "maxLength"
    //     0xa3d888: ldr             x16, [x16, #0x98]
    //     0xa3d88c: cmp             w19, w16
    //     0xa3d890: b.ne            #0xa3d8c0
    //     0xa3d894: add             w11, w13, #0xa
    //     0xa3d898: add             x16, x4, w11, sxtw #1
    //     0xa3d89c: ldur            w13, [x16, #0xf]
    //     0xa3d8a0: add             x13, x13, HEAP, lsl #32
    //     0xa3d8a4: sub             w11, w0, w13
    //     0xa3d8a8: add             x13, fp, w11, sxtw #2
    //     0xa3d8ac: ldr             x13, [x13, #8]
    //     0xa3d8b0: add             w11, w12, #2
    //     0xa3d8b4: sbfx            x12, x11, #1, #0x1f
    //     0xa3d8b8: mov             x11, x13
    //     0xa3d8bc: b               #0xa3d8c8
    //     0xa3d8c0: mov             x12, x11
    //     0xa3d8c4: mov             x11, NULL
    //     0xa3d8c8: stur            x11, [fp, #-0x48]
    //     0xa3d8cc: lsl             x13, x12, #1
    //     0xa3d8d0: lsl             w14, w13, #1
    //     0xa3d8d4: add             w19, w14, #8
    //     0xa3d8d8: add             x16, x4, w19, sxtw #1
    //     0xa3d8dc: ldur            w20, [x16, #0xf]
    //     0xa3d8e0: add             x20, x20, HEAP, lsl #32
    //     0xa3d8e4: add             x16, PP, #0x30, lsl #12  ; [pp+0x300a0] "onChanged"
    //     0xa3d8e8: ldr             x16, [x16, #0xa0]
    //     0xa3d8ec: cmp             w20, w16
    //     0xa3d8f0: b.ne            #0xa3d920
    //     0xa3d8f4: add             w12, w14, #0xa
    //     0xa3d8f8: add             x16, x4, w12, sxtw #1
    //     0xa3d8fc: ldur            w14, [x16, #0xf]
    //     0xa3d900: add             x14, x14, HEAP, lsl #32
    //     0xa3d904: sub             w12, w0, w14
    //     0xa3d908: add             x14, fp, w12, sxtw #2
    //     0xa3d90c: ldr             x14, [x14, #8]
    //     0xa3d910: add             w12, w13, #2
    //     0xa3d914: sbfx            x13, x12, #1, #0x1f
    //     0xa3d918: mov             x12, x14
    //     0xa3d91c: b               #0xa3d928
    //     0xa3d920: mov             x13, x12
    //     0xa3d924: mov             x12, NULL
    //     0xa3d928: stur            x12, [fp, #-0x40]
    //     0xa3d92c: lsl             x14, x13, #1
    //     0xa3d930: lsl             w19, w14, #1
    //     0xa3d934: add             w20, w19, #8
    //     0xa3d938: add             x16, x4, w20, sxtw #1
    //     0xa3d93c: ldur            w23, [x16, #0xf]
    //     0xa3d940: add             x23, x23, HEAP, lsl #32
    //     0xa3d944: add             x16, PP, #0x22, lsl #12  ; [pp+0x222d8] "onTap"
    //     0xa3d948: ldr             x16, [x16, #0x2d8]
    //     0xa3d94c: cmp             w23, w16
    //     0xa3d950: b.ne            #0xa3d980
    //     0xa3d954: add             w13, w19, #0xa
    //     0xa3d958: add             x16, x4, w13, sxtw #1
    //     0xa3d95c: ldur            w19, [x16, #0xf]
    //     0xa3d960: add             x19, x19, HEAP, lsl #32
    //     0xa3d964: sub             w13, w0, w19
    //     0xa3d968: add             x19, fp, w13, sxtw #2
    //     0xa3d96c: ldr             x19, [x19, #8]
    //     0xa3d970: add             w13, w14, #2
    //     0xa3d974: sbfx            x14, x13, #1, #0x1f
    //     0xa3d978: mov             x13, x19
    //     0xa3d97c: b               #0xa3d988
    //     0xa3d980: mov             x14, x13
    //     0xa3d984: mov             x13, NULL
    //     0xa3d988: stur            x13, [fp, #-0x38]
    //     0xa3d98c: lsl             x19, x14, #1
    //     0xa3d990: lsl             w20, w19, #1
    //     0xa3d994: add             w23, w20, #8
    //     0xa3d998: add             x16, x4, w23, sxtw #1
    //     0xa3d99c: ldur            w24, [x16, #0xf]
    //     0xa3d9a0: add             x24, x24, HEAP, lsl #32
    //     0xa3d9a4: ldr             x16, [PP, #0x7a70]  ; [pp+0x7a70] "readOnly"
    //     0xa3d9a8: cmp             w24, w16
    //     0xa3d9ac: b.ne            #0xa3d9dc
    //     0xa3d9b0: add             w14, w20, #0xa
    //     0xa3d9b4: add             x16, x4, w14, sxtw #1
    //     0xa3d9b8: ldur            w20, [x16, #0xf]
    //     0xa3d9bc: add             x20, x20, HEAP, lsl #32
    //     0xa3d9c0: sub             w14, w0, w20
    //     0xa3d9c4: add             x20, fp, w14, sxtw #2
    //     0xa3d9c8: ldr             x20, [x20, #8]
    //     0xa3d9cc: add             w14, w19, #2
    //     0xa3d9d0: sbfx            x19, x14, #1, #0x1f
    //     0xa3d9d4: mov             x14, x20
    //     0xa3d9d8: b               #0xa3d9e4
    //     0xa3d9dc: mov             x19, x14
    //     0xa3d9e0: add             x14, NULL, #0x30  ; false
    //     0xa3d9e4: stur            x14, [fp, #-0x30]
    //     0xa3d9e8: lsl             x20, x19, #1
    //     0xa3d9ec: lsl             w23, w20, #1
    //     0xa3d9f0: add             w24, w23, #8
    //     0xa3d9f4: add             x16, x4, w24, sxtw #1
    //     0xa3d9f8: ldur            w25, [x16, #0xf]
    //     0xa3d9fc: add             x25, x25, HEAP, lsl #32
    //     0xa3da00: add             x16, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xa3da04: ldr             x16, [x16, #0x9b8]
    //     0xa3da08: cmp             w25, w16
    //     0xa3da0c: b.ne            #0xa3da3c
    //     0xa3da10: add             w19, w23, #0xa
    //     0xa3da14: add             x16, x4, w19, sxtw #1
    //     0xa3da18: ldur            w23, [x16, #0xf]
    //     0xa3da1c: add             x23, x23, HEAP, lsl #32
    //     0xa3da20: sub             w19, w0, w23
    //     0xa3da24: add             x23, fp, w19, sxtw #2
    //     0xa3da28: ldr             x23, [x23, #8]
    //     0xa3da2c: add             w19, w20, #2
    //     0xa3da30: sbfx            x20, x19, #1, #0x1f
    //     0xa3da34: mov             x19, x23
    //     0xa3da38: b               #0xa3da44
    //     0xa3da3c: mov             x20, x19
    //     0xa3da40: mov             x19, NULL
    //     0xa3da44: stur            x19, [fp, #-0x28]
    //     0xa3da48: lsl             x23, x20, #1
    //     0xa3da4c: lsl             w24, w23, #1
    //     0xa3da50: add             w25, w24, #8
    //     0xa3da54: add             x16, x4, w25, sxtw #1
    //     0xa3da58: ldur            w1, [x16, #0xf]
    //     0xa3da5c: add             x1, x1, HEAP, lsl #32
    //     0xa3da60: ldr             x16, [PP, #0x4828]  ; [pp+0x4828] "textAlign"
    //     0xa3da64: cmp             w1, w16
    //     0xa3da68: b.ne            #0xa3da9c
    //     0xa3da6c: add             w1, w24, #0xa
    //     0xa3da70: add             x16, x4, w1, sxtw #1
    //     0xa3da74: ldur            w20, [x16, #0xf]
    //     0xa3da78: add             x20, x20, HEAP, lsl #32
    //     0xa3da7c: sub             w1, w0, w20
    //     0xa3da80: add             x20, fp, w1, sxtw #2
    //     0xa3da84: ldr             x20, [x20, #8]
    //     0xa3da88: add             w1, w23, #2
    //     0xa3da8c: sbfx            x23, x1, #1, #0x1f
    //     0xa3da90: mov             x1, x20
    //     0xa3da94: mov             x20, x23
    //     0xa3da98: b               #0xa3daa0
    //     0xa3da9c: ldr             x1, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    //     0xa3daa0: stur            x1, [fp, #-0x10]
    //     0xa3daa4: lsl             x23, x20, #1
    //     0xa3daa8: lsl             w24, w23, #1
    //     0xa3daac: add             w25, w24, #8
    //     0xa3dab0: add             x16, x4, w25, sxtw #1
    //     0xa3dab4: ldur            w1, [x16, #0xf]
    //     0xa3dab8: add             x1, x1, HEAP, lsl #32
    //     0xa3dabc: ldr             x16, [PP, #0x4830]  ; [pp+0x4830] "textDirection"
    //     0xa3dac0: cmp             w1, w16
    //     0xa3dac4: b.ne            #0xa3daf8
    //     0xa3dac8: add             w1, w24, #0xa
    //     0xa3dacc: add             x16, x4, w1, sxtw #1
    //     0xa3dad0: ldur            w20, [x16, #0xf]
    //     0xa3dad4: add             x20, x20, HEAP, lsl #32
    //     0xa3dad8: sub             w1, w0, w20
    //     0xa3dadc: add             x20, fp, w1, sxtw #2
    //     0xa3dae0: ldr             x20, [x20, #8]
    //     0xa3dae4: add             w1, w23, #2
    //     0xa3dae8: sbfx            x23, x1, #1, #0x1f
    //     0xa3daec: mov             x1, x20
    //     0xa3daf0: mov             x20, x23
    //     0xa3daf4: b               #0xa3dafc
    //     0xa3daf8: mov             x1, NULL
    //     0xa3dafc: stur            x1, [fp, #-0x20]
    //     0xa3db00: lsl             x23, x20, #1
    //     0xa3db04: lsl             w20, w23, #1
    //     0xa3db08: add             w23, w20, #8
    //     0xa3db0c: add             x16, x4, w23, sxtw #1
    //     0xa3db10: ldur            w24, [x16, #0xf]
    //     0xa3db14: add             x24, x24, HEAP, lsl #32
    //     0xa3db18: add             x16, PP, #0x30, lsl #12  ; [pp+0x300a8] "validator"
    //     0xa3db1c: ldr             x16, [x16, #0xa8]
    //     0xa3db20: cmp             w24, w16
    //     0xa3db24: b.ne            #0xa3db48
    //     0xa3db28: add             w23, w20, #0xa
    //     0xa3db2c: add             x16, x4, w23, sxtw #1
    //     0xa3db30: ldur            w20, [x16, #0xf]
    //     0xa3db34: add             x20, x20, HEAP, lsl #32
    //     0xa3db38: sub             w4, w0, w20
    //     0xa3db3c: add             x0, fp, w4, sxtw #2
    //     0xa3db40: ldr             x0, [x0, #8]
    //     0xa3db44: b               #0xa3db4c
    //     0xa3db48: mov             x0, NULL
    //     0xa3db4c: stur            x0, [fp, #-0x18]
    // 0xa3db50: r1 = 12
    //     0xa3db50: movz            x1, #0xc
    // 0xa3db54: r0 = AllocateContext()
    //     0xa3db54: bl              #0xec126c  ; AllocateContextStub
    // 0xa3db58: mov             x2, x0
    // 0xa3db5c: ldur            x1, [fp, #-0x88]
    // 0xa3db60: StoreField: r2->field_f = r1
    //     0xa3db60: stur            w1, [x2, #0xf]
    // 0xa3db64: ldur            x0, [fp, #-0x80]
    // 0xa3db68: StoreField: r2->field_13 = r0
    //     0xa3db68: stur            w0, [x2, #0x13]
    // 0xa3db6c: ldur            x0, [fp, #-0x70]
    // 0xa3db70: ArrayStore: r2[0] = r0  ; List_4
    //     0xa3db70: stur            w0, [x2, #0x17]
    // 0xa3db74: ldur            x0, [fp, #-0x60]
    // 0xa3db78: StoreField: r2->field_1b = r0
    //     0xa3db78: stur            w0, [x2, #0x1b]
    // 0xa3db7c: ldur            x0, [fp, #-0x50]
    // 0xa3db80: StoreField: r2->field_1f = r0
    //     0xa3db80: stur            w0, [x2, #0x1f]
    // 0xa3db84: ldur            x0, [fp, #-0x48]
    // 0xa3db88: StoreField: r2->field_23 = r0
    //     0xa3db88: stur            w0, [x2, #0x23]
    // 0xa3db8c: ldur            x3, [fp, #-0x40]
    // 0xa3db90: StoreField: r2->field_27 = r3
    //     0xa3db90: stur            w3, [x2, #0x27]
    // 0xa3db94: ldur            x0, [fp, #-0x38]
    // 0xa3db98: StoreField: r2->field_2b = r0
    //     0xa3db98: stur            w0, [x2, #0x2b]
    // 0xa3db9c: ldur            x0, [fp, #-0x30]
    // 0xa3dba0: StoreField: r2->field_2f = r0
    //     0xa3dba0: stur            w0, [x2, #0x2f]
    // 0xa3dba4: ldur            x0, [fp, #-0x28]
    // 0xa3dba8: StoreField: r2->field_33 = r0
    //     0xa3dba8: stur            w0, [x2, #0x33]
    // 0xa3dbac: ldur            x0, [fp, #-0x10]
    // 0xa3dbb0: StoreField: r2->field_37 = r0
    //     0xa3dbb0: stur            w0, [x2, #0x37]
    // 0xa3dbb4: ldur            x0, [fp, #-0x20]
    // 0xa3dbb8: StoreField: r2->field_3b = r0
    //     0xa3dbb8: stur            w0, [x2, #0x3b]
    // 0xa3dbbc: ldur            x0, [fp, #-0x78]
    // 0xa3dbc0: ldur            x4, [fp, #-8]
    // 0xa3dbc4: StoreField: r4->field_2b = r0
    //     0xa3dbc4: stur            w0, [x4, #0x2b]
    //     0xa3dbc8: ldurb           w16, [x4, #-1]
    //     0xa3dbcc: ldurb           w17, [x0, #-1]
    //     0xa3dbd0: and             x16, x17, x16, lsr #2
    //     0xa3dbd4: tst             x16, HEAP, lsr #32
    //     0xa3dbd8: b.eq            #0xa3dbe0
    //     0xa3dbdc: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xa3dbe0: mov             x0, x3
    // 0xa3dbe4: StoreField: r4->field_2f = r0
    //     0xa3dbe4: stur            w0, [x4, #0x2f]
    //     0xa3dbe8: ldurb           w16, [x4, #-1]
    //     0xa3dbec: ldurb           w17, [x0, #-1]
    //     0xa3dbf0: and             x16, x17, x16, lsr #2
    //     0xa3dbf4: tst             x16, HEAP, lsr #32
    //     0xa3dbf8: b.eq            #0xa3dc00
    //     0xa3dbfc: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xa3dc00: ldur            x0, [fp, #-0x78]
    // 0xa3dc04: cmp             w0, NULL
    // 0xa3dc08: b.eq            #0xa3dc20
    // 0xa3dc0c: LoadField: r3 = r0->field_27
    //     0xa3dc0c: ldur            w3, [x0, #0x27]
    // 0xa3dc10: DecompressPointer r3
    //     0xa3dc10: add             x3, x3, HEAP, lsl #32
    // 0xa3dc14: LoadField: r0 = r3->field_7
    //     0xa3dc14: ldur            w0, [x3, #7]
    // 0xa3dc18: DecompressPointer r0
    //     0xa3dc18: add             x0, x0, HEAP, lsl #32
    // 0xa3dc1c: b               #0xa3dc30
    // 0xa3dc20: ldur            x0, [fp, #-0x68]
    // 0xa3dc24: cmp             w0, NULL
    // 0xa3dc28: b.ne            #0xa3dc30
    // 0xa3dc2c: r0 = ""
    //     0xa3dc2c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xa3dc30: stur            x0, [fp, #-0x20]
    // 0xa3dc34: LoadField: r3 = r1->field_cf
    //     0xa3dc34: ldur            w3, [x1, #0xcf]
    // 0xa3dc38: DecompressPointer r3
    //     0xa3dc38: add             x3, x3, HEAP, lsl #32
    // 0xa3dc3c: stur            x3, [fp, #-0x10]
    // 0xa3dc40: r1 = Function '<anonymous closure>':.
    //     0xa3dc40: add             x1, PP, #0x30, lsl #12  ; [pp+0x300b0] AnonymousClosure: (0xa3dcf0), in [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField (0xa3d5e0)
    //     0xa3dc44: ldr             x1, [x1, #0xb0]
    // 0xa3dc48: r0 = AllocateClosure()
    //     0xa3dc48: bl              #0xec1630  ; AllocateClosureStub
    // 0xa3dc4c: ldur            x1, [fp, #-8]
    // 0xa3dc50: ArrayStore: r1[0] = r0  ; List_4
    //     0xa3dc50: stur            w0, [x1, #0x17]
    //     0xa3dc54: ldurb           w16, [x1, #-1]
    //     0xa3dc58: ldurb           w17, [x0, #-1]
    //     0xa3dc5c: and             x16, x17, x16, lsr #2
    //     0xa3dc60: tst             x16, HEAP, lsr #32
    //     0xa3dc64: b.eq            #0xa3dc6c
    //     0xa3dc68: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa3dc6c: ldur            x0, [fp, #-0x18]
    // 0xa3dc70: StoreField: r1->field_13 = r0
    //     0xa3dc70: stur            w0, [x1, #0x13]
    //     0xa3dc74: ldurb           w16, [x1, #-1]
    //     0xa3dc78: ldurb           w17, [x0, #-1]
    //     0xa3dc7c: and             x16, x17, x16, lsr #2
    //     0xa3dc80: tst             x16, HEAP, lsr #32
    //     0xa3dc84: b.eq            #0xa3dc8c
    //     0xa3dc88: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa3dc8c: ldur            x0, [fp, #-0x20]
    // 0xa3dc90: StoreField: r1->field_1b = r0
    //     0xa3dc90: stur            w0, [x1, #0x1b]
    //     0xa3dc94: ldurb           w16, [x1, #-1]
    //     0xa3dc98: ldurb           w17, [x0, #-1]
    //     0xa3dc9c: and             x16, x17, x16, lsr #2
    //     0xa3dca0: tst             x16, HEAP, lsr #32
    //     0xa3dca4: b.eq            #0xa3dcac
    //     0xa3dca8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa3dcac: ldur            x2, [fp, #-0x10]
    // 0xa3dcb0: StoreField: r1->field_1f = r2
    //     0xa3dcb0: stur            w2, [x1, #0x1f]
    // 0xa3dcb4: r2 = Instance_AutovalidateMode
    //     0xa3dcb4: add             x2, PP, #0x30, lsl #12  ; [pp+0x300b8] Obj!AutovalidateMode@e34421
    //     0xa3dcb8: ldr             x2, [x2, #0xb8]
    // 0xa3dcbc: StoreField: r1->field_23 = r2
    //     0xa3dcbc: stur            w2, [x1, #0x23]
    // 0xa3dcc0: ldur            x0, [fp, #-0x58]
    // 0xa3dcc4: StoreField: r1->field_7 = r0
    //     0xa3dcc4: stur            w0, [x1, #7]
    //     0xa3dcc8: ldurb           w16, [x1, #-1]
    //     0xa3dccc: ldurb           w17, [x0, #-1]
    //     0xa3dcd0: and             x16, x17, x16, lsr #2
    //     0xa3dcd4: tst             x16, HEAP, lsr #32
    //     0xa3dcd8: b.eq            #0xa3dce0
    //     0xa3dcdc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa3dce0: r0 = Null
    //     0xa3dce0: mov             x0, NULL
    // 0xa3dce4: LeaveFrame
    //     0xa3dce4: mov             SP, fp
    //     0xa3dce8: ldp             fp, lr, [SP], #0x10
    // 0xa3dcec: ret
    //     0xa3dcec: ret             
  }
  [closure] UnmanagedRestorationScope <anonymous closure>(dynamic, FormFieldState<String>) {
    // ** addr: 0xa3dcf0, size: 0x334
    // 0xa3dcf0: EnterFrame
    //     0xa3dcf0: stp             fp, lr, [SP, #-0x10]!
    //     0xa3dcf4: mov             fp, SP
    // 0xa3dcf8: AllocStack(0x80)
    //     0xa3dcf8: sub             SP, SP, #0x80
    // 0xa3dcfc: SetupParameters()
    //     0xa3dcfc: ldr             x0, [fp, #0x18]
    //     0xa3dd00: ldur            w1, [x0, #0x17]
    //     0xa3dd04: add             x1, x1, HEAP, lsl #32
    //     0xa3dd08: stur            x1, [fp, #-8]
    // 0xa3dd0c: CheckStackOverflow
    //     0xa3dd0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3dd10: cmp             SP, x16
    //     0xa3dd14: b.ls            #0xa3e00c
    // 0xa3dd18: r1 = 1
    //     0xa3dd18: movz            x1, #0x1
    // 0xa3dd1c: r0 = AllocateContext()
    //     0xa3dd1c: bl              #0xec126c  ; AllocateContextStub
    // 0xa3dd20: mov             x2, x0
    // 0xa3dd24: ldur            x0, [fp, #-8]
    // 0xa3dd28: stur            x2, [fp, #-0x18]
    // 0xa3dd2c: StoreField: r2->field_b = r0
    //     0xa3dd2c: stur            w0, [x2, #0xb]
    // 0xa3dd30: ldr             x3, [fp, #0x10]
    // 0xa3dd34: StoreField: r2->field_f = r3
    //     0xa3dd34: stur            w3, [x2, #0xf]
    // 0xa3dd38: LoadField: r4 = r0->field_f
    //     0xa3dd38: ldur            w4, [x0, #0xf]
    // 0xa3dd3c: DecompressPointer r4
    //     0xa3dd3c: add             x4, x4, HEAP, lsl #32
    // 0xa3dd40: stur            x4, [fp, #-0x10]
    // 0xa3dd44: LoadField: r1 = r3->field_f
    //     0xa3dd44: ldur            w1, [x3, #0xf]
    // 0xa3dd48: DecompressPointer r1
    //     0xa3dd48: add             x1, x1, HEAP, lsl #32
    // 0xa3dd4c: cmp             w1, NULL
    // 0xa3dd50: b.eq            #0xa3e014
    // 0xa3dd54: r0 = of()
    //     0xa3dd54: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa3dd58: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xa3dd58: ldur            w2, [x0, #0x17]
    // 0xa3dd5c: DecompressPointer r2
    //     0xa3dd5c: add             x2, x2, HEAP, lsl #32
    // 0xa3dd60: ldur            x1, [fp, #-0x10]
    // 0xa3dd64: r0 = applyDefaults()
    //     0xa3dd64: bl              #0x985dc4  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::applyDefaults
    // 0xa3dd68: ldr             x1, [fp, #0x10]
    // 0xa3dd6c: stur            x0, [fp, #-0x10]
    // 0xa3dd70: r0 = _effectiveController()
    //     0xa3dd70: bl              #0x93b454  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::_effectiveController
    // 0xa3dd74: mov             x4, x0
    // 0xa3dd78: ldur            x3, [fp, #-8]
    // 0xa3dd7c: stur            x4, [fp, #-0x30]
    // 0xa3dd80: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xa3dd80: ldur            w5, [x3, #0x17]
    // 0xa3dd84: DecompressPointer r5
    //     0xa3dd84: add             x5, x5, HEAP, lsl #32
    // 0xa3dd88: ldr             x0, [fp, #0x10]
    // 0xa3dd8c: stur            x5, [fp, #-0x28]
    // 0xa3dd90: LoadField: r1 = r0->field_27
    //     0xa3dd90: ldur            w1, [x0, #0x27]
    // 0xa3dd94: DecompressPointer r1
    //     0xa3dd94: add             x1, x1, HEAP, lsl #32
    // 0xa3dd98: r16 = Sentinel
    //     0xa3dd98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa3dd9c: cmp             w1, w16
    // 0xa3dda0: b.eq            #0xa3e018
    // 0xa3dda4: LoadField: r6 = r1->field_33
    //     0xa3dda4: ldur            w6, [x1, #0x33]
    // 0xa3dda8: DecompressPointer r6
    //     0xa3dda8: add             x6, x6, HEAP, lsl #32
    // 0xa3ddac: stur            x6, [fp, #-0x20]
    // 0xa3ddb0: cmp             w6, NULL
    // 0xa3ddb4: b.ne            #0xa3ddec
    // 0xa3ddb8: LoadField: r2 = r1->field_23
    //     0xa3ddb8: ldur            w2, [x1, #0x23]
    // 0xa3ddbc: DecompressPointer r2
    //     0xa3ddbc: add             x2, x2, HEAP, lsl #32
    // 0xa3ddc0: mov             x0, x6
    // 0xa3ddc4: r1 = Null
    //     0xa3ddc4: mov             x1, NULL
    // 0xa3ddc8: cmp             w2, NULL
    // 0xa3ddcc: b.eq            #0xa3ddec
    // 0xa3ddd0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa3ddd0: ldur            w4, [x2, #0x17]
    // 0xa3ddd4: DecompressPointer r4
    //     0xa3ddd4: add             x4, x4, HEAP, lsl #32
    // 0xa3ddd8: r8 = X0
    //     0xa3ddd8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xa3dddc: LoadField: r9 = r4->field_7
    //     0xa3dddc: ldur            x9, [x4, #7]
    // 0xa3dde0: r3 = Null
    //     0xa3dde0: add             x3, PP, #0x30, lsl #12  ; [pp+0x300c0] Null
    //     0xa3dde4: ldr             x3, [x3, #0xc0]
    // 0xa3dde8: blr             x9
    // 0xa3ddec: ldur            x0, [fp, #-8]
    // 0xa3ddf0: ldur            x2, [fp, #-0x30]
    // 0xa3ddf4: ldur            x3, [fp, #-0x28]
    // 0xa3ddf8: ldur            x16, [fp, #-0x20]
    // 0xa3ddfc: str             x16, [SP]
    // 0xa3de00: ldur            x1, [fp, #-0x10]
    // 0xa3de04: r4 = const [0, 0x2, 0x1, 0x1, errorText, 0x1, null]
    //     0xa3de04: add             x4, PP, #0x30, lsl #12  ; [pp+0x300d0] List(7) [0, 0x2, 0x1, 0x1, "errorText", 0x1, Null]
    //     0xa3de08: ldr             x4, [x4, #0xd0]
    // 0xa3de0c: r0 = copyWith()
    //     0xa3de0c: bl              #0x98615c  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xa3de10: mov             x1, x0
    // 0xa3de14: ldur            x0, [fp, #-8]
    // 0xa3de18: stur            x1, [fp, #-0x70]
    // 0xa3de1c: LoadField: r2 = r0->field_1f
    //     0xa3de1c: ldur            w2, [x0, #0x1f]
    // 0xa3de20: DecompressPointer r2
    //     0xa3de20: add             x2, x2, HEAP, lsl #32
    // 0xa3de24: stur            x2, [fp, #-0x68]
    // 0xa3de28: LoadField: r3 = r0->field_33
    //     0xa3de28: ldur            w3, [x0, #0x33]
    // 0xa3de2c: DecompressPointer r3
    //     0xa3de2c: add             x3, x3, HEAP, lsl #32
    // 0xa3de30: stur            x3, [fp, #-0x60]
    // 0xa3de34: LoadField: r4 = r0->field_37
    //     0xa3de34: ldur            w4, [x0, #0x37]
    // 0xa3de38: DecompressPointer r4
    //     0xa3de38: add             x4, x4, HEAP, lsl #32
    // 0xa3de3c: stur            x4, [fp, #-0x58]
    // 0xa3de40: LoadField: r5 = r0->field_3b
    //     0xa3de40: ldur            w5, [x0, #0x3b]
    // 0xa3de44: DecompressPointer r5
    //     0xa3de44: add             x5, x5, HEAP, lsl #32
    // 0xa3de48: stur            x5, [fp, #-0x50]
    // 0xa3de4c: LoadField: r6 = r0->field_2f
    //     0xa3de4c: ldur            w6, [x0, #0x2f]
    // 0xa3de50: DecompressPointer r6
    //     0xa3de50: add             x6, x6, HEAP, lsl #32
    // 0xa3de54: stur            x6, [fp, #-0x48]
    // 0xa3de58: LoadField: r7 = r0->field_13
    //     0xa3de58: ldur            w7, [x0, #0x13]
    // 0xa3de5c: DecompressPointer r7
    //     0xa3de5c: add             x7, x7, HEAP, lsl #32
    // 0xa3de60: stur            x7, [fp, #-0x40]
    // 0xa3de64: LoadField: r8 = r0->field_23
    //     0xa3de64: ldur            w8, [x0, #0x23]
    // 0xa3de68: DecompressPointer r8
    //     0xa3de68: add             x8, x8, HEAP, lsl #32
    // 0xa3de6c: stur            x8, [fp, #-0x38]
    // 0xa3de70: LoadField: r9 = r0->field_2b
    //     0xa3de70: ldur            w9, [x0, #0x2b]
    // 0xa3de74: DecompressPointer r9
    //     0xa3de74: add             x9, x9, HEAP, lsl #32
    // 0xa3de78: stur            x9, [fp, #-0x20]
    // 0xa3de7c: LoadField: r10 = r0->field_1b
    //     0xa3de7c: ldur            w10, [x0, #0x1b]
    // 0xa3de80: DecompressPointer r10
    //     0xa3de80: add             x10, x10, HEAP, lsl #32
    // 0xa3de84: stur            x10, [fp, #-0x10]
    // 0xa3de88: LoadField: r11 = r0->field_f
    //     0xa3de88: ldur            w11, [x0, #0xf]
    // 0xa3de8c: DecompressPointer r11
    //     0xa3de8c: add             x11, x11, HEAP, lsl #32
    // 0xa3de90: LoadField: r0 = r11->field_cf
    //     0xa3de90: ldur            w0, [x11, #0xcf]
    // 0xa3de94: DecompressPointer r0
    //     0xa3de94: add             x0, x0, HEAP, lsl #32
    // 0xa3de98: stur            x0, [fp, #-8]
    // 0xa3de9c: r0 = TextField()
    //     0xa3de9c: bl              #0xa3e024  ; AllocateTextFieldStub -> TextField (size=0x11c)
    // 0xa3dea0: mov             x3, x0
    // 0xa3dea4: r0 = EditableText
    //     0xa3dea4: ldr             x0, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xa3dea8: stur            x3, [fp, #-0x78]
    // 0xa3deac: StoreField: r3->field_f = r0
    //     0xa3deac: stur            w0, [x3, #0xf]
    // 0xa3deb0: ldur            x0, [fp, #-0x30]
    // 0xa3deb4: StoreField: r3->field_13 = r0
    //     0xa3deb4: stur            w0, [x3, #0x13]
    // 0xa3deb8: ldur            x0, [fp, #-0x28]
    // 0xa3debc: ArrayStore: r3[0] = r0  ; List_4
    //     0xa3debc: stur            w0, [x3, #0x17]
    // 0xa3dec0: ldur            x0, [fp, #-0x70]
    // 0xa3dec4: StoreField: r3->field_1b = r0
    //     0xa3dec4: stur            w0, [x3, #0x1b]
    // 0xa3dec8: r0 = Instance_TextCapitalization
    //     0xa3dec8: ldr             x0, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xa3decc: StoreField: r3->field_27 = r0
    //     0xa3decc: stur            w0, [x3, #0x27]
    // 0xa3ded0: ldur            x0, [fp, #-0x60]
    // 0xa3ded4: StoreField: r3->field_2b = r0
    //     0xa3ded4: stur            w0, [x3, #0x2b]
    // 0xa3ded8: ldur            x0, [fp, #-0x58]
    // 0xa3dedc: StoreField: r3->field_33 = r0
    //     0xa3dedc: stur            w0, [x3, #0x33]
    // 0xa3dee0: ldur            x0, [fp, #-0x50]
    // 0xa3dee4: StoreField: r3->field_3b = r0
    //     0xa3dee4: stur            w0, [x3, #0x3b]
    // 0xa3dee8: ldur            x0, [fp, #-0x48]
    // 0xa3deec: StoreField: r3->field_6f = r0
    //     0xa3deec: stur            w0, [x3, #0x6f]
    // 0xa3def0: r0 = false
    //     0xa3def0: add             x0, NULL, #0x30  ; false
    // 0xa3def4: StoreField: r3->field_3f = r0
    //     0xa3def4: stur            w0, [x3, #0x3f]
    // 0xa3def8: r1 = "•"
    //     0xa3def8: add             x1, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xa3defc: ldr             x1, [x1, #0x4c8]
    // 0xa3df00: StoreField: r3->field_47 = r1
    //     0xa3df00: stur            w1, [x3, #0x47]
    // 0xa3df04: StoreField: r3->field_4b = r0
    //     0xa3df04: stur            w0, [x3, #0x4b]
    // 0xa3df08: ldur            x1, [fp, #-0x40]
    // 0xa3df0c: StoreField: r3->field_4f = r1
    //     0xa3df0c: stur            w1, [x3, #0x4f]
    // 0xa3df10: r4 = true
    //     0xa3df10: add             x4, NULL, #0x20  ; true
    // 0xa3df14: StoreField: r3->field_5b = r4
    //     0xa3df14: stur            w4, [x3, #0x5b]
    // 0xa3df18: r1 = 1
    //     0xa3df18: movz            x1, #0x1
    // 0xa3df1c: StoreField: r3->field_5f = r1
    //     0xa3df1c: stur            x1, [x3, #0x5f]
    // 0xa3df20: StoreField: r3->field_6b = r0
    //     0xa3df20: stur            w0, [x3, #0x6b]
    // 0xa3df24: ldur            x1, [fp, #-0x38]
    // 0xa3df28: StoreField: r3->field_7b = r1
    //     0xa3df28: stur            w1, [x3, #0x7b]
    // 0xa3df2c: ldur            x2, [fp, #-0x18]
    // 0xa3df30: r1 = Function 'onChangedHandler':.
    //     0xa3df30: add             x1, PP, #0x30, lsl #12  ; [pp+0x300d8] AnonymousClosure: (0xa40590), in [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField (0xa3d5e0)
    //     0xa3df34: ldr             x1, [x1, #0xd8]
    // 0xa3df38: r0 = AllocateClosure()
    //     0xa3df38: bl              #0xec1630  ; AllocateClosureStub
    // 0xa3df3c: mov             x1, x0
    // 0xa3df40: ldur            x0, [fp, #-0x78]
    // 0xa3df44: StoreField: r0->field_83 = r1
    //     0xa3df44: stur            w1, [x0, #0x83]
    // 0xa3df48: ldur            x1, [fp, #-0x10]
    // 0xa3df4c: StoreField: r0->field_93 = r1
    //     0xa3df4c: stur            w1, [x0, #0x93]
    // 0xa3df50: ldur            x1, [fp, #-8]
    // 0xa3df54: StoreField: r0->field_97 = r1
    //     0xa3df54: stur            w1, [x0, #0x97]
    // 0xa3df58: d0 = 2.000000
    //     0xa3df58: fmov            d0, #2.00000000
    // 0xa3df5c: StoreField: r0->field_9f = d0
    //     0xa3df5c: stur            d0, [x0, #0x9f]
    // 0xa3df60: r1 = Instance_BoxHeightStyle
    //     0xa3df60: ldr             x1, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xa3df64: StoreField: r0->field_bb = r1
    //     0xa3df64: stur            w1, [x0, #0xbb]
    // 0xa3df68: r1 = Instance_BoxWidthStyle
    //     0xa3df68: ldr             x1, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xa3df6c: StoreField: r0->field_bf = r1
    //     0xa3df6c: stur            w1, [x0, #0xbf]
    // 0xa3df70: r1 = Instance_EdgeInsets
    //     0xa3df70: ldr             x1, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xa3df74: StoreField: r0->field_c7 = r1
    //     0xa3df74: stur            w1, [x0, #0xc7]
    // 0xa3df78: r1 = Instance_DragStartBehavior
    //     0xa3df78: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xa3df7c: StoreField: r0->field_d3 = r1
    //     0xa3df7c: stur            w1, [x0, #0xd3]
    // 0xa3df80: ldur            x1, [fp, #-0x20]
    // 0xa3df84: StoreField: r0->field_d7 = r1
    //     0xa3df84: stur            w1, [x0, #0xd7]
    // 0xa3df88: r1 = false
    //     0xa3df88: add             x1, NULL, #0x30  ; false
    // 0xa3df8c: StoreField: r0->field_db = r1
    //     0xa3df8c: stur            w1, [x0, #0xdb]
    // 0xa3df90: r1 = Instance_Clip
    //     0xa3df90: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa3df94: ldr             x1, [x1, #0x7c0]
    // 0xa3df98: StoreField: r0->field_f7 = r1
    //     0xa3df98: stur            w1, [x0, #0xf7]
    // 0xa3df9c: r1 = true
    //     0xa3df9c: add             x1, NULL, #0x20  ; true
    // 0xa3dfa0: StoreField: r0->field_ff = r1
    //     0xa3dfa0: stur            w1, [x0, #0xff]
    // 0xa3dfa4: r17 = 259
    //     0xa3dfa4: movz            x17, #0x103
    // 0xa3dfa8: str             w1, [x0, x17]
    // 0xa3dfac: r2 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@611147271': static.
    //     0xa3dfac: add             x2, PP, #0x30, lsl #12  ; [pp+0x300e0] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@611147271': static. (0x7e54fb43e038)
    //     0xa3dfb0: ldr             x2, [x2, #0xe0]
    // 0xa3dfb4: r17 = 267
    //     0xa3dfb4: movz            x17, #0x10b
    // 0xa3dfb8: str             w2, [x0, x17]
    // 0xa3dfbc: r17 = 271
    //     0xa3dfbc: movz            x17, #0x10f
    // 0xa3dfc0: str             w1, [x0, x17]
    // 0xa3dfc4: r2 = Instance_SmartDashesType
    //     0xa3dfc4: ldr             x2, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xa3dfc8: StoreField: r0->field_53 = r2
    //     0xa3dfc8: stur            w2, [x0, #0x53]
    // 0xa3dfcc: r2 = Instance_SmartQuotesType
    //     0xa3dfcc: add             x2, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xa3dfd0: ldr             x2, [x2, #0x4e0]
    // 0xa3dfd4: StoreField: r0->field_57 = r2
    //     0xa3dfd4: stur            w2, [x0, #0x57]
    // 0xa3dfd8: ldur            x2, [fp, #-0x68]
    // 0xa3dfdc: cmp             w2, NULL
    // 0xa3dfe0: b.ne            #0xa3dfec
    // 0xa3dfe4: r2 = Instance_TextInputType
    //     0xa3dfe4: add             x2, PP, #0x27, lsl #12  ; [pp+0x274e8] Obj!TextInputType@e10db1
    //     0xa3dfe8: ldr             x2, [x2, #0x4e8]
    // 0xa3dfec: StoreField: r0->field_1f = r2
    //     0xa3dfec: stur            w2, [x0, #0x1f]
    // 0xa3dff0: StoreField: r0->field_cb = r1
    //     0xa3dff0: stur            w1, [x0, #0xcb]
    // 0xa3dff4: r0 = UnmanagedRestorationScope()
    //     0xa3dff4: bl              #0xa0c188  ; AllocateUnmanagedRestorationScopeStub -> UnmanagedRestorationScope (size=0x14)
    // 0xa3dff8: ldur            x1, [fp, #-0x78]
    // 0xa3dffc: StoreField: r0->field_b = r1
    //     0xa3dffc: stur            w1, [x0, #0xb]
    // 0xa3e000: LeaveFrame
    //     0xa3e000: mov             SP, fp
    //     0xa3e004: ldp             fp, lr, [SP], #0x10
    // 0xa3e008: ret
    //     0xa3e008: ret             
    // 0xa3e00c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3e00c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3e010: b               #0xa3dd18
    // 0xa3e014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3e014: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3e018: r9 = _errorText
    //     0xa3e018: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fed8] Field <FormFieldState._errorText@282032539>: late final (offset: 0x28)
    //     0xa3e01c: ldr             x9, [x9, #0xed8]
    // 0xa3e020: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa3e020: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] static Widget _defaultContextMenuBuilder(dynamic, BuildContext, EditableTextState) {
    // ** addr: 0xa3e038, size: 0x34
    // 0xa3e038: EnterFrame
    //     0xa3e038: stp             fp, lr, [SP, #-0x10]!
    //     0xa3e03c: mov             fp, SP
    // 0xa3e040: CheckStackOverflow
    //     0xa3e040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3e044: cmp             SP, x16
    //     0xa3e048: b.ls            #0xa3e064
    // 0xa3e04c: ldr             x1, [fp, #0x18]
    // 0xa3e050: ldr             x2, [fp, #0x10]
    // 0xa3e054: r0 = _defaultContextMenuBuilder()
    //     0xa3e054: bl              #0xa3e06c  ; [package:flutter/src/material/text_field.dart] TextField::_defaultContextMenuBuilder
    // 0xa3e058: LeaveFrame
    //     0xa3e058: mov             SP, fp
    //     0xa3e05c: ldp             fp, lr, [SP], #0x10
    // 0xa3e060: ret
    //     0xa3e060: ret             
    // 0xa3e064: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3e064: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3e068: b               #0xa3e04c
  }
  [closure] void onChangedHandler(dynamic, String) {
    // ** addr: 0xa40590, size: 0x80
    // 0xa40590: EnterFrame
    //     0xa40590: stp             fp, lr, [SP, #-0x10]!
    //     0xa40594: mov             fp, SP
    // 0xa40598: AllocStack(0x18)
    //     0xa40598: sub             SP, SP, #0x18
    // 0xa4059c: SetupParameters()
    //     0xa4059c: ldr             x0, [fp, #0x18]
    //     0xa405a0: ldur            w3, [x0, #0x17]
    //     0xa405a4: add             x3, x3, HEAP, lsl #32
    //     0xa405a8: stur            x3, [fp, #-8]
    // 0xa405ac: CheckStackOverflow
    //     0xa405ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa405b0: cmp             SP, x16
    //     0xa405b4: b.ls            #0xa40608
    // 0xa405b8: LoadField: r1 = r3->field_f
    //     0xa405b8: ldur            w1, [x3, #0xf]
    // 0xa405bc: DecompressPointer r1
    //     0xa405bc: add             x1, x1, HEAP, lsl #32
    // 0xa405c0: ldr             x2, [fp, #0x10]
    // 0xa405c4: r0 = didChange()
    //     0xa405c4: bl              #0x93b104  ; [package:flutter/src/material/text_form_field.dart] _TextFormFieldState::didChange
    // 0xa405c8: ldur            x0, [fp, #-8]
    // 0xa405cc: LoadField: r1 = r0->field_b
    //     0xa405cc: ldur            w1, [x0, #0xb]
    // 0xa405d0: DecompressPointer r1
    //     0xa405d0: add             x1, x1, HEAP, lsl #32
    // 0xa405d4: LoadField: r0 = r1->field_27
    //     0xa405d4: ldur            w0, [x1, #0x27]
    // 0xa405d8: DecompressPointer r0
    //     0xa405d8: add             x0, x0, HEAP, lsl #32
    // 0xa405dc: cmp             w0, NULL
    // 0xa405e0: b.eq            #0xa405f8
    // 0xa405e4: ldr             x16, [fp, #0x10]
    // 0xa405e8: stp             x16, x0, [SP]
    // 0xa405ec: ClosureCall
    //     0xa405ec: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa405f0: ldur            x2, [x0, #0x1f]
    //     0xa405f4: blr             x2
    // 0xa405f8: r0 = Null
    //     0xa405f8: mov             x0, NULL
    // 0xa405fc: LeaveFrame
    //     0xa405fc: mov             SP, fp
    //     0xa40600: ldp             fp, lr, [SP], #0x10
    // 0xa40604: ret
    //     0xa40604: ret             
    // 0xa40608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa40608: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4060c: b               #0xa405b8
  }
  _ createState(/* No info */) {
    // ** addr: 0xa91028, size: 0x48
    // 0xa91028: EnterFrame
    //     0xa91028: stp             fp, lr, [SP, #-0x10]!
    //     0xa9102c: mov             fp, SP
    // 0xa91030: AllocStack(0x8)
    //     0xa91030: sub             SP, SP, #8
    // 0xa91034: CheckStackOverflow
    //     0xa91034: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa91038: cmp             SP, x16
    //     0xa9103c: b.ls            #0xa91068
    // 0xa91040: r1 = <FormField<String>, String>
    //     0xa91040: add             x1, PP, #0x39, lsl #12  ; [pp+0x39700] TypeArguments: <FormField<String>, String>
    //     0xa91044: ldr             x1, [x1, #0x700]
    // 0xa91048: r0 = _TextFormFieldState()
    //     0xa91048: bl              #0xa91198  ; Allocate_TextFormFieldStateStub -> _TextFormFieldState (size=0x38)
    // 0xa9104c: mov             x1, x0
    // 0xa91050: stur            x0, [fp, #-8]
    // 0xa91054: r0 = FormFieldState()
    //     0xa91054: bl              #0xa91070  ; [package:flutter/src/widgets/form.dart] FormFieldState::FormFieldState
    // 0xa91058: ldur            x0, [fp, #-8]
    // 0xa9105c: LeaveFrame
    //     0xa9105c: mov             SP, fp
    //     0xa91060: ldp             fp, lr, [SP], #0x10
    // 0xa91064: ret
    //     0xa91064: ret             
    // 0xa91068: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa91068: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9106c: b               #0xa91040
  }
}
