// lib: , url: package:flutter/src/material/text_selection_toolbar.dart

// class id: 1048965, size: 0x8
class :: {
}

// class id: 3065, size: 0x68, field offset: 0x58
//   transformed mixin,
abstract class __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin extends RenderBox
     with ContainerRenderObjectMixin<X0 bound RenderObject, X1 bound ContainerParentDataMixin> {

  _ attach(/* No info */) {
    // ** addr: 0x761d38, size: 0xfc
    // 0x761d38: EnterFrame
    //     0x761d38: stp             fp, lr, [SP, #-0x10]!
    //     0x761d3c: mov             fp, SP
    // 0x761d40: AllocStack(0x18)
    //     0x761d40: sub             SP, SP, #0x18
    // 0x761d44: SetupParameters(__RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x761d44: mov             x3, x1
    //     0x761d48: mov             x0, x2
    //     0x761d4c: stur            x1, [fp, #-8]
    //     0x761d50: stur            x2, [fp, #-0x10]
    // 0x761d54: CheckStackOverflow
    //     0x761d54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x761d58: cmp             SP, x16
    //     0x761d5c: b.ls            #0x761e20
    // 0x761d60: mov             x1, x3
    // 0x761d64: mov             x2, x0
    // 0x761d68: r0 = attach()
    //     0x761d68: bl              #0x765268  ; [package:flutter/src/rendering/object.dart] RenderObject::attach
    // 0x761d6c: ldur            x0, [fp, #-8]
    // 0x761d70: LoadField: r1 = r0->field_5f
    //     0x761d70: ldur            w1, [x0, #0x5f]
    // 0x761d74: DecompressPointer r1
    //     0x761d74: add             x1, x1, HEAP, lsl #32
    // 0x761d78: mov             x3, x1
    // 0x761d7c: stur            x3, [fp, #-8]
    // 0x761d80: CheckStackOverflow
    //     0x761d80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x761d84: cmp             SP, x16
    //     0x761d88: b.ls            #0x761e28
    // 0x761d8c: cmp             w3, NULL
    // 0x761d90: b.eq            #0x761e10
    // 0x761d94: r0 = LoadClassIdInstr(r3)
    //     0x761d94: ldur            x0, [x3, #-1]
    //     0x761d98: ubfx            x0, x0, #0xc, #0x14
    // 0x761d9c: mov             x1, x3
    // 0x761da0: ldur            x2, [fp, #-0x10]
    // 0x761da4: r0 = GDT[cid_x0 + 0x11974]()
    //     0x761da4: movz            x17, #0x1974
    //     0x761da8: movk            x17, #0x1, lsl #16
    //     0x761dac: add             lr, x0, x17
    //     0x761db0: ldr             lr, [x21, lr, lsl #3]
    //     0x761db4: blr             lr
    // 0x761db8: ldur            x0, [fp, #-8]
    // 0x761dbc: LoadField: r3 = r0->field_7
    //     0x761dbc: ldur            w3, [x0, #7]
    // 0x761dc0: DecompressPointer r3
    //     0x761dc0: add             x3, x3, HEAP, lsl #32
    // 0x761dc4: stur            x3, [fp, #-0x18]
    // 0x761dc8: cmp             w3, NULL
    // 0x761dcc: b.eq            #0x761e30
    // 0x761dd0: mov             x0, x3
    // 0x761dd4: r2 = Null
    //     0x761dd4: mov             x2, NULL
    // 0x761dd8: r1 = Null
    //     0x761dd8: mov             x1, NULL
    // 0x761ddc: r4 = LoadClassIdInstr(r0)
    //     0x761ddc: ldur            x4, [x0, #-1]
    //     0x761de0: ubfx            x4, x4, #0xc, #0x14
    // 0x761de4: cmp             x4, #0xc77
    // 0x761de8: b.eq            #0x761e00
    // 0x761dec: r8 = ToolbarItemsParentData
    //     0x761dec: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x761df0: ldr             x8, [x8, #0x490]
    // 0x761df4: r3 = Null
    //     0x761df4: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b640] Null
    //     0x761df8: ldr             x3, [x3, #0x640]
    // 0x761dfc: r0 = DefaultTypeTest()
    //     0x761dfc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x761e00: ldur            x1, [fp, #-0x18]
    // 0x761e04: LoadField: r3 = r1->field_13
    //     0x761e04: ldur            w3, [x1, #0x13]
    // 0x761e08: DecompressPointer r3
    //     0x761e08: add             x3, x3, HEAP, lsl #32
    // 0x761e0c: b               #0x761d7c
    // 0x761e10: r0 = Null
    //     0x761e10: mov             x0, NULL
    // 0x761e14: LeaveFrame
    //     0x761e14: mov             SP, fp
    //     0x761e18: ldp             fp, lr, [SP], #0x10
    // 0x761e1c: ret
    //     0x761e1c: ret             
    // 0x761e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x761e20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x761e24: b               #0x761d60
    // 0x761e28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x761e28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x761e2c: b               #0x761d8c
    // 0x761e30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x761e30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitChildren(/* No info */) {
    // ** addr: 0x786888, size: 0xd8
    // 0x786888: EnterFrame
    //     0x786888: stp             fp, lr, [SP, #-0x10]!
    //     0x78688c: mov             fp, SP
    // 0x786890: AllocStack(0x28)
    //     0x786890: sub             SP, SP, #0x28
    // 0x786894: SetupParameters(__RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x786894: mov             x0, x1
    //     0x786898: mov             x1, x2
    //     0x78689c: stur            x2, [fp, #-0x10]
    // 0x7868a0: CheckStackOverflow
    //     0x7868a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7868a4: cmp             SP, x16
    //     0x7868a8: b.ls            #0x78694c
    // 0x7868ac: LoadField: r2 = r0->field_5f
    //     0x7868ac: ldur            w2, [x0, #0x5f]
    // 0x7868b0: DecompressPointer r2
    //     0x7868b0: add             x2, x2, HEAP, lsl #32
    // 0x7868b4: stur            x2, [fp, #-8]
    // 0x7868b8: CheckStackOverflow
    //     0x7868b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7868bc: cmp             SP, x16
    //     0x7868c0: b.ls            #0x786954
    // 0x7868c4: cmp             w2, NULL
    // 0x7868c8: b.eq            #0x78693c
    // 0x7868cc: stp             x2, x1, [SP]
    // 0x7868d0: mov             x0, x1
    // 0x7868d4: ClosureCall
    //     0x7868d4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x7868d8: ldur            x2, [x0, #0x1f]
    //     0x7868dc: blr             x2
    // 0x7868e0: ldur            x0, [fp, #-8]
    // 0x7868e4: LoadField: r3 = r0->field_7
    //     0x7868e4: ldur            w3, [x0, #7]
    // 0x7868e8: DecompressPointer r3
    //     0x7868e8: add             x3, x3, HEAP, lsl #32
    // 0x7868ec: stur            x3, [fp, #-0x18]
    // 0x7868f0: cmp             w3, NULL
    // 0x7868f4: b.eq            #0x78695c
    // 0x7868f8: mov             x0, x3
    // 0x7868fc: r2 = Null
    //     0x7868fc: mov             x2, NULL
    // 0x786900: r1 = Null
    //     0x786900: mov             x1, NULL
    // 0x786904: r4 = LoadClassIdInstr(r0)
    //     0x786904: ldur            x4, [x0, #-1]
    //     0x786908: ubfx            x4, x4, #0xc, #0x14
    // 0x78690c: cmp             x4, #0xc77
    // 0x786910: b.eq            #0x786928
    // 0x786914: r8 = ToolbarItemsParentData
    //     0x786914: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x786918: ldr             x8, [x8, #0x490]
    // 0x78691c: r3 = Null
    //     0x78691c: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b610] Null
    //     0x786920: ldr             x3, [x3, #0x610]
    // 0x786924: r0 = DefaultTypeTest()
    //     0x786924: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x786928: ldur            x1, [fp, #-0x18]
    // 0x78692c: LoadField: r2 = r1->field_13
    //     0x78692c: ldur            w2, [x1, #0x13]
    // 0x786930: DecompressPointer r2
    //     0x786930: add             x2, x2, HEAP, lsl #32
    // 0x786934: ldur            x1, [fp, #-0x10]
    // 0x786938: b               #0x7868b4
    // 0x78693c: r0 = Null
    //     0x78693c: mov             x0, NULL
    // 0x786940: LeaveFrame
    //     0x786940: mov             SP, fp
    //     0x786944: ldp             fp, lr, [SP], #0x10
    // 0x786948: ret
    //     0x786948: ret             
    // 0x78694c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x78694c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x786950: b               #0x7868ac
    // 0x786954: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x786954: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x786958: b               #0x7868c4
    // 0x78695c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x78695c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ insert(/* No info */) {
    // ** addr: 0x7b0abc, size: 0xd0
    // 0x7b0abc: EnterFrame
    //     0x7b0abc: stp             fp, lr, [SP, #-0x10]!
    //     0x7b0ac0: mov             fp, SP
    // 0x7b0ac4: AllocStack(0x18)
    //     0x7b0ac4: sub             SP, SP, #0x18
    // 0x7b0ac8: SetupParameters(__RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7b0ac8: mov             x5, x1
    //     0x7b0acc: mov             x4, x2
    //     0x7b0ad0: stur            x1, [fp, #-8]
    //     0x7b0ad4: stur            x2, [fp, #-0x10]
    //     0x7b0ad8: stur            x3, [fp, #-0x18]
    // 0x7b0adc: CheckStackOverflow
    //     0x7b0adc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b0ae0: cmp             SP, x16
    //     0x7b0ae4: b.ls            #0x7b0b84
    // 0x7b0ae8: mov             x0, x4
    // 0x7b0aec: r2 = Null
    //     0x7b0aec: mov             x2, NULL
    // 0x7b0af0: r1 = Null
    //     0x7b0af0: mov             x1, NULL
    // 0x7b0af4: r4 = 60
    //     0x7b0af4: movz            x4, #0x3c
    // 0x7b0af8: branchIfSmi(r0, 0x7b0b04)
    //     0x7b0af8: tbz             w0, #0, #0x7b0b04
    // 0x7b0afc: r4 = LoadClassIdInstr(r0)
    //     0x7b0afc: ldur            x4, [x0, #-1]
    //     0x7b0b00: ubfx            x4, x4, #0xc, #0x14
    // 0x7b0b04: sub             x4, x4, #0xbba
    // 0x7b0b08: cmp             x4, #0x9a
    // 0x7b0b0c: b.ls            #0x7b0b20
    // 0x7b0b10: r8 = RenderBox
    //     0x7b0b10: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7b0b14: r3 = Null
    //     0x7b0b14: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b7b0] Null
    //     0x7b0b18: ldr             x3, [x3, #0x7b0]
    // 0x7b0b1c: r0 = RenderBox()
    //     0x7b0b1c: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7b0b20: ldur            x0, [fp, #-0x18]
    // 0x7b0b24: r2 = Null
    //     0x7b0b24: mov             x2, NULL
    // 0x7b0b28: r1 = Null
    //     0x7b0b28: mov             x1, NULL
    // 0x7b0b2c: r4 = 60
    //     0x7b0b2c: movz            x4, #0x3c
    // 0x7b0b30: branchIfSmi(r0, 0x7b0b3c)
    //     0x7b0b30: tbz             w0, #0, #0x7b0b3c
    // 0x7b0b34: r4 = LoadClassIdInstr(r0)
    //     0x7b0b34: ldur            x4, [x0, #-1]
    //     0x7b0b38: ubfx            x4, x4, #0xc, #0x14
    // 0x7b0b3c: sub             x4, x4, #0xbba
    // 0x7b0b40: cmp             x4, #0x9a
    // 0x7b0b44: b.ls            #0x7b0b58
    // 0x7b0b48: r8 = RenderBox?
    //     0x7b0b48: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x7b0b4c: r3 = Null
    //     0x7b0b4c: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b7c0] Null
    //     0x7b0b50: ldr             x3, [x3, #0x7c0]
    // 0x7b0b54: r0 = RenderBox?()
    //     0x7b0b54: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x7b0b58: ldur            x1, [fp, #-8]
    // 0x7b0b5c: ldur            x2, [fp, #-0x10]
    // 0x7b0b60: r0 = adoptChild()
    //     0x7b0b60: bl              #0x8057b8  ; [package:flutter/src/rendering/object.dart] RenderObject::adoptChild
    // 0x7b0b64: ldur            x1, [fp, #-8]
    // 0x7b0b68: ldur            x2, [fp, #-0x10]
    // 0x7b0b6c: ldur            x3, [fp, #-0x18]
    // 0x7b0b70: r0 = _insertIntoChildList()
    //     0x7b0b70: bl              #0xda37fc  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::_insertIntoChildList
    // 0x7b0b74: r0 = Null
    //     0x7b0b74: mov             x0, NULL
    // 0x7b0b78: LeaveFrame
    //     0x7b0b78: mov             SP, fp
    //     0x7b0b7c: ldp             fp, lr, [SP], #0x10
    // 0x7b0b80: ret
    //     0x7b0b80: ret             
    // 0x7b0b84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b0b84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b0b88: b               #0x7b0ae8
  }
  _ remove(/* No info */) {
    // ** addr: 0x7b1f90, size: 0x90
    // 0x7b1f90: EnterFrame
    //     0x7b1f90: stp             fp, lr, [SP, #-0x10]!
    //     0x7b1f94: mov             fp, SP
    // 0x7b1f98: AllocStack(0x10)
    //     0x7b1f98: sub             SP, SP, #0x10
    // 0x7b1f9c: SetupParameters(__RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x7b1f9c: mov             x4, x1
    //     0x7b1fa0: mov             x3, x2
    //     0x7b1fa4: stur            x1, [fp, #-8]
    //     0x7b1fa8: stur            x2, [fp, #-0x10]
    // 0x7b1fac: CheckStackOverflow
    //     0x7b1fac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b1fb0: cmp             SP, x16
    //     0x7b1fb4: b.ls            #0x7b2018
    // 0x7b1fb8: mov             x0, x3
    // 0x7b1fbc: r2 = Null
    //     0x7b1fbc: mov             x2, NULL
    // 0x7b1fc0: r1 = Null
    //     0x7b1fc0: mov             x1, NULL
    // 0x7b1fc4: r4 = 60
    //     0x7b1fc4: movz            x4, #0x3c
    // 0x7b1fc8: branchIfSmi(r0, 0x7b1fd4)
    //     0x7b1fc8: tbz             w0, #0, #0x7b1fd4
    // 0x7b1fcc: r4 = LoadClassIdInstr(r0)
    //     0x7b1fcc: ldur            x4, [x0, #-1]
    //     0x7b1fd0: ubfx            x4, x4, #0xc, #0x14
    // 0x7b1fd4: sub             x4, x4, #0xbba
    // 0x7b1fd8: cmp             x4, #0x9a
    // 0x7b1fdc: b.ls            #0x7b1ff0
    // 0x7b1fe0: r8 = RenderBox
    //     0x7b1fe0: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7b1fe4: r3 = Null
    //     0x7b1fe4: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b7a0] Null
    //     0x7b1fe8: ldr             x3, [x3, #0x7a0]
    // 0x7b1fec: r0 = RenderBox()
    //     0x7b1fec: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7b1ff0: ldur            x1, [fp, #-8]
    // 0x7b1ff4: ldur            x2, [fp, #-0x10]
    // 0x7b1ff8: r0 = _removeFromChildList()
    //     0x7b1ff8: bl              #0x7b2020  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::_removeFromChildList
    // 0x7b1ffc: ldur            x1, [fp, #-8]
    // 0x7b2000: ldur            x2, [fp, #-0x10]
    // 0x7b2004: r0 = dropChild()
    //     0x7b2004: bl              #0x8019dc  ; [package:flutter/src/rendering/object.dart] RenderObject::dropChild
    // 0x7b2008: r0 = Null
    //     0x7b2008: mov             x0, NULL
    // 0x7b200c: LeaveFrame
    //     0x7b200c: mov             SP, fp
    //     0x7b2010: ldp             fp, lr, [SP], #0x10
    // 0x7b2014: ret
    //     0x7b2014: ret             
    // 0x7b2018: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b2018: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b201c: b               #0x7b1fb8
  }
  _ _removeFromChildList(/* No info */) {
    // ** addr: 0x7b2020, size: 0x2c8
    // 0x7b2020: EnterFrame
    //     0x7b2020: stp             fp, lr, [SP, #-0x10]!
    //     0x7b2024: mov             fp, SP
    // 0x7b2028: AllocStack(0x28)
    //     0x7b2028: sub             SP, SP, #0x28
    // 0x7b202c: SetupParameters(__RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin this /* r1 => r3, fp-0x10 */)
    //     0x7b202c: mov             x3, x1
    //     0x7b2030: stur            x1, [fp, #-0x10]
    // 0x7b2034: LoadField: r4 = r2->field_7
    //     0x7b2034: ldur            w4, [x2, #7]
    // 0x7b2038: DecompressPointer r4
    //     0x7b2038: add             x4, x4, HEAP, lsl #32
    // 0x7b203c: stur            x4, [fp, #-8]
    // 0x7b2040: cmp             w4, NULL
    // 0x7b2044: b.eq            #0x7b22dc
    // 0x7b2048: mov             x0, x4
    // 0x7b204c: r2 = Null
    //     0x7b204c: mov             x2, NULL
    // 0x7b2050: r1 = Null
    //     0x7b2050: mov             x1, NULL
    // 0x7b2054: r4 = LoadClassIdInstr(r0)
    //     0x7b2054: ldur            x4, [x0, #-1]
    //     0x7b2058: ubfx            x4, x4, #0xc, #0x14
    // 0x7b205c: cmp             x4, #0xc77
    // 0x7b2060: b.eq            #0x7b2078
    // 0x7b2064: r8 = ToolbarItemsParentData
    //     0x7b2064: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x7b2068: ldr             x8, [x8, #0x490]
    // 0x7b206c: r3 = Null
    //     0x7b206c: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b740] Null
    //     0x7b2070: ldr             x3, [x3, #0x740]
    // 0x7b2074: r0 = DefaultTypeTest()
    //     0x7b2074: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b2078: ldur            x3, [fp, #-8]
    // 0x7b207c: LoadField: r4 = r3->field_f
    //     0x7b207c: ldur            w4, [x3, #0xf]
    // 0x7b2080: DecompressPointer r4
    //     0x7b2080: add             x4, x4, HEAP, lsl #32
    // 0x7b2084: stur            x4, [fp, #-0x20]
    // 0x7b2088: cmp             w4, NULL
    // 0x7b208c: b.ne            #0x7b20bc
    // 0x7b2090: ldur            x5, [fp, #-0x10]
    // 0x7b2094: LoadField: r0 = r3->field_13
    //     0x7b2094: ldur            w0, [x3, #0x13]
    // 0x7b2098: DecompressPointer r0
    //     0x7b2098: add             x0, x0, HEAP, lsl #32
    // 0x7b209c: StoreField: r5->field_5f = r0
    //     0x7b209c: stur            w0, [x5, #0x5f]
    //     0x7b20a0: ldurb           w16, [x5, #-1]
    //     0x7b20a4: ldurb           w17, [x0, #-1]
    //     0x7b20a8: and             x16, x17, x16, lsr #2
    //     0x7b20ac: tst             x16, HEAP, lsr #32
    //     0x7b20b0: b.eq            #0x7b20b8
    //     0x7b20b4: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x7b20b8: b               #0x7b2180
    // 0x7b20bc: ldur            x5, [fp, #-0x10]
    // 0x7b20c0: LoadField: r6 = r4->field_7
    //     0x7b20c0: ldur            w6, [x4, #7]
    // 0x7b20c4: DecompressPointer r6
    //     0x7b20c4: add             x6, x6, HEAP, lsl #32
    // 0x7b20c8: stur            x6, [fp, #-0x18]
    // 0x7b20cc: cmp             w6, NULL
    // 0x7b20d0: b.eq            #0x7b22e0
    // 0x7b20d4: mov             x0, x6
    // 0x7b20d8: r2 = Null
    //     0x7b20d8: mov             x2, NULL
    // 0x7b20dc: r1 = Null
    //     0x7b20dc: mov             x1, NULL
    // 0x7b20e0: r4 = LoadClassIdInstr(r0)
    //     0x7b20e0: ldur            x4, [x0, #-1]
    //     0x7b20e4: ubfx            x4, x4, #0xc, #0x14
    // 0x7b20e8: cmp             x4, #0xc77
    // 0x7b20ec: b.eq            #0x7b2104
    // 0x7b20f0: r8 = ToolbarItemsParentData
    //     0x7b20f0: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x7b20f4: ldr             x8, [x8, #0x490]
    // 0x7b20f8: r3 = Null
    //     0x7b20f8: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b750] Null
    //     0x7b20fc: ldr             x3, [x3, #0x750]
    // 0x7b2100: r0 = DefaultTypeTest()
    //     0x7b2100: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b2104: ldur            x3, [fp, #-8]
    // 0x7b2108: LoadField: r4 = r3->field_13
    //     0x7b2108: ldur            w4, [x3, #0x13]
    // 0x7b210c: DecompressPointer r4
    //     0x7b210c: add             x4, x4, HEAP, lsl #32
    // 0x7b2110: ldur            x5, [fp, #-0x18]
    // 0x7b2114: stur            x4, [fp, #-0x28]
    // 0x7b2118: LoadField: r2 = r5->field_b
    //     0x7b2118: ldur            w2, [x5, #0xb]
    // 0x7b211c: DecompressPointer r2
    //     0x7b211c: add             x2, x2, HEAP, lsl #32
    // 0x7b2120: mov             x0, x4
    // 0x7b2124: r1 = Null
    //     0x7b2124: mov             x1, NULL
    // 0x7b2128: cmp             w0, NULL
    // 0x7b212c: b.eq            #0x7b2158
    // 0x7b2130: cmp             w2, NULL
    // 0x7b2134: b.eq            #0x7b2158
    // 0x7b2138: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b2138: ldur            w4, [x2, #0x17]
    // 0x7b213c: DecompressPointer r4
    //     0x7b213c: add             x4, x4, HEAP, lsl #32
    // 0x7b2140: r8 = X0? bound RenderObject
    //     0x7b2140: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b2144: ldr             x8, [x8, #0x1a8]
    // 0x7b2148: LoadField: r9 = r4->field_7
    //     0x7b2148: ldur            x9, [x4, #7]
    // 0x7b214c: r3 = Null
    //     0x7b214c: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b760] Null
    //     0x7b2150: ldr             x3, [x3, #0x760]
    // 0x7b2154: blr             x9
    // 0x7b2158: ldur            x0, [fp, #-0x28]
    // 0x7b215c: ldur            x1, [fp, #-0x18]
    // 0x7b2160: StoreField: r1->field_13 = r0
    //     0x7b2160: stur            w0, [x1, #0x13]
    //     0x7b2164: ldurb           w16, [x1, #-1]
    //     0x7b2168: ldurb           w17, [x0, #-1]
    //     0x7b216c: and             x16, x17, x16, lsr #2
    //     0x7b2170: tst             x16, HEAP, lsr #32
    //     0x7b2174: b.eq            #0x7b217c
    //     0x7b2178: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b217c: ldur            x3, [fp, #-8]
    // 0x7b2180: LoadField: r0 = r3->field_13
    //     0x7b2180: ldur            w0, [x3, #0x13]
    // 0x7b2184: DecompressPointer r0
    //     0x7b2184: add             x0, x0, HEAP, lsl #32
    // 0x7b2188: cmp             w0, NULL
    // 0x7b218c: b.ne            #0x7b21b8
    // 0x7b2190: ldur            x4, [fp, #-0x10]
    // 0x7b2194: ldur            x0, [fp, #-0x20]
    // 0x7b2198: StoreField: r4->field_63 = r0
    //     0x7b2198: stur            w0, [x4, #0x63]
    //     0x7b219c: ldurb           w16, [x4, #-1]
    //     0x7b21a0: ldurb           w17, [x0, #-1]
    //     0x7b21a4: and             x16, x17, x16, lsr #2
    //     0x7b21a8: tst             x16, HEAP, lsr #32
    //     0x7b21ac: b.eq            #0x7b21b4
    //     0x7b21b0: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7b21b4: b               #0x7b2270
    // 0x7b21b8: ldur            x4, [fp, #-0x10]
    // 0x7b21bc: LoadField: r5 = r0->field_7
    //     0x7b21bc: ldur            w5, [x0, #7]
    // 0x7b21c0: DecompressPointer r5
    //     0x7b21c0: add             x5, x5, HEAP, lsl #32
    // 0x7b21c4: stur            x5, [fp, #-0x18]
    // 0x7b21c8: cmp             w5, NULL
    // 0x7b21cc: b.eq            #0x7b22e4
    // 0x7b21d0: mov             x0, x5
    // 0x7b21d4: r2 = Null
    //     0x7b21d4: mov             x2, NULL
    // 0x7b21d8: r1 = Null
    //     0x7b21d8: mov             x1, NULL
    // 0x7b21dc: r4 = LoadClassIdInstr(r0)
    //     0x7b21dc: ldur            x4, [x0, #-1]
    //     0x7b21e0: ubfx            x4, x4, #0xc, #0x14
    // 0x7b21e4: cmp             x4, #0xc77
    // 0x7b21e8: b.eq            #0x7b2200
    // 0x7b21ec: r8 = ToolbarItemsParentData
    //     0x7b21ec: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x7b21f0: ldr             x8, [x8, #0x490]
    // 0x7b21f4: r3 = Null
    //     0x7b21f4: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b770] Null
    //     0x7b21f8: ldr             x3, [x3, #0x770]
    // 0x7b21fc: r0 = DefaultTypeTest()
    //     0x7b21fc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b2200: ldur            x3, [fp, #-0x18]
    // 0x7b2204: LoadField: r2 = r3->field_b
    //     0x7b2204: ldur            w2, [x3, #0xb]
    // 0x7b2208: DecompressPointer r2
    //     0x7b2208: add             x2, x2, HEAP, lsl #32
    // 0x7b220c: ldur            x0, [fp, #-0x20]
    // 0x7b2210: r1 = Null
    //     0x7b2210: mov             x1, NULL
    // 0x7b2214: cmp             w0, NULL
    // 0x7b2218: b.eq            #0x7b2244
    // 0x7b221c: cmp             w2, NULL
    // 0x7b2220: b.eq            #0x7b2244
    // 0x7b2224: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b2224: ldur            w4, [x2, #0x17]
    // 0x7b2228: DecompressPointer r4
    //     0x7b2228: add             x4, x4, HEAP, lsl #32
    // 0x7b222c: r8 = X0? bound RenderObject
    //     0x7b222c: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b2230: ldr             x8, [x8, #0x1a8]
    // 0x7b2234: LoadField: r9 = r4->field_7
    //     0x7b2234: ldur            x9, [x4, #7]
    // 0x7b2238: r3 = Null
    //     0x7b2238: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b780] Null
    //     0x7b223c: ldr             x3, [x3, #0x780]
    // 0x7b2240: blr             x9
    // 0x7b2244: ldur            x0, [fp, #-0x20]
    // 0x7b2248: ldur            x1, [fp, #-0x18]
    // 0x7b224c: StoreField: r1->field_f = r0
    //     0x7b224c: stur            w0, [x1, #0xf]
    //     0x7b2250: ldurb           w16, [x1, #-1]
    //     0x7b2254: ldurb           w17, [x0, #-1]
    //     0x7b2258: and             x16, x17, x16, lsr #2
    //     0x7b225c: tst             x16, HEAP, lsr #32
    //     0x7b2260: b.eq            #0x7b2268
    //     0x7b2264: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b2268: ldur            x4, [fp, #-0x10]
    // 0x7b226c: ldur            x3, [fp, #-8]
    // 0x7b2270: LoadField: r2 = r3->field_b
    //     0x7b2270: ldur            w2, [x3, #0xb]
    // 0x7b2274: DecompressPointer r2
    //     0x7b2274: add             x2, x2, HEAP, lsl #32
    // 0x7b2278: r0 = Null
    //     0x7b2278: mov             x0, NULL
    // 0x7b227c: r1 = Null
    //     0x7b227c: mov             x1, NULL
    // 0x7b2280: cmp             w0, NULL
    // 0x7b2284: b.eq            #0x7b22b0
    // 0x7b2288: cmp             w2, NULL
    // 0x7b228c: b.eq            #0x7b22b0
    // 0x7b2290: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b2290: ldur            w4, [x2, #0x17]
    // 0x7b2294: DecompressPointer r4
    //     0x7b2294: add             x4, x4, HEAP, lsl #32
    // 0x7b2298: r8 = X0? bound RenderObject
    //     0x7b2298: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b229c: ldr             x8, [x8, #0x1a8]
    // 0x7b22a0: LoadField: r9 = r4->field_7
    //     0x7b22a0: ldur            x9, [x4, #7]
    // 0x7b22a4: r3 = Null
    //     0x7b22a4: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b790] Null
    //     0x7b22a8: ldr             x3, [x3, #0x790]
    // 0x7b22ac: blr             x9
    // 0x7b22b0: ldur            x1, [fp, #-8]
    // 0x7b22b4: StoreField: r1->field_f = rNULL
    //     0x7b22b4: stur            NULL, [x1, #0xf]
    // 0x7b22b8: StoreField: r1->field_13 = rNULL
    //     0x7b22b8: stur            NULL, [x1, #0x13]
    // 0x7b22bc: ldur            x1, [fp, #-0x10]
    // 0x7b22c0: LoadField: r2 = r1->field_57
    //     0x7b22c0: ldur            x2, [x1, #0x57]
    // 0x7b22c4: sub             x3, x2, #1
    // 0x7b22c8: StoreField: r1->field_57 = r3
    //     0x7b22c8: stur            x3, [x1, #0x57]
    // 0x7b22cc: r0 = Null
    //     0x7b22cc: mov             x0, NULL
    // 0x7b22d0: LeaveFrame
    //     0x7b22d0: mov             SP, fp
    //     0x7b22d4: ldp             fp, lr, [SP], #0x10
    // 0x7b22d8: ret
    //     0x7b22d8: ret             
    // 0x7b22dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b22dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7b22e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b22e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7b22e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b22e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ move(/* No info */) {
    // ** addr: 0x7cdb78, size: 0x160
    // 0x7cdb78: EnterFrame
    //     0x7cdb78: stp             fp, lr, [SP, #-0x10]!
    //     0x7cdb7c: mov             fp, SP
    // 0x7cdb80: AllocStack(0x30)
    //     0x7cdb80: sub             SP, SP, #0x30
    // 0x7cdb84: SetupParameters(__RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7cdb84: mov             x5, x1
    //     0x7cdb88: mov             x4, x2
    //     0x7cdb8c: stur            x1, [fp, #-8]
    //     0x7cdb90: stur            x2, [fp, #-0x10]
    //     0x7cdb94: stur            x3, [fp, #-0x18]
    // 0x7cdb98: CheckStackOverflow
    //     0x7cdb98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cdb9c: cmp             SP, x16
    //     0x7cdba0: b.ls            #0x7cdccc
    // 0x7cdba4: mov             x0, x4
    // 0x7cdba8: r2 = Null
    //     0x7cdba8: mov             x2, NULL
    // 0x7cdbac: r1 = Null
    //     0x7cdbac: mov             x1, NULL
    // 0x7cdbb0: r4 = 60
    //     0x7cdbb0: movz            x4, #0x3c
    // 0x7cdbb4: branchIfSmi(r0, 0x7cdbc0)
    //     0x7cdbb4: tbz             w0, #0, #0x7cdbc0
    // 0x7cdbb8: r4 = LoadClassIdInstr(r0)
    //     0x7cdbb8: ldur            x4, [x0, #-1]
    //     0x7cdbbc: ubfx            x4, x4, #0xc, #0x14
    // 0x7cdbc0: sub             x4, x4, #0xbba
    // 0x7cdbc4: cmp             x4, #0x9a
    // 0x7cdbc8: b.ls            #0x7cdbdc
    // 0x7cdbcc: r8 = RenderBox
    //     0x7cdbcc: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7cdbd0: r3 = Null
    //     0x7cdbd0: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b650] Null
    //     0x7cdbd4: ldr             x3, [x3, #0x650]
    // 0x7cdbd8: r0 = RenderBox()
    //     0x7cdbd8: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7cdbdc: ldur            x0, [fp, #-0x18]
    // 0x7cdbe0: r2 = Null
    //     0x7cdbe0: mov             x2, NULL
    // 0x7cdbe4: r1 = Null
    //     0x7cdbe4: mov             x1, NULL
    // 0x7cdbe8: r4 = 60
    //     0x7cdbe8: movz            x4, #0x3c
    // 0x7cdbec: branchIfSmi(r0, 0x7cdbf8)
    //     0x7cdbec: tbz             w0, #0, #0x7cdbf8
    // 0x7cdbf0: r4 = LoadClassIdInstr(r0)
    //     0x7cdbf0: ldur            x4, [x0, #-1]
    //     0x7cdbf4: ubfx            x4, x4, #0xc, #0x14
    // 0x7cdbf8: sub             x4, x4, #0xbba
    // 0x7cdbfc: cmp             x4, #0x9a
    // 0x7cdc00: b.ls            #0x7cdc14
    // 0x7cdc04: r8 = RenderBox?
    //     0x7cdc04: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x7cdc08: r3 = Null
    //     0x7cdc08: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b660] Null
    //     0x7cdc0c: ldr             x3, [x3, #0x660]
    // 0x7cdc10: r0 = RenderBox?()
    //     0x7cdc10: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x7cdc14: ldur            x3, [fp, #-0x10]
    // 0x7cdc18: LoadField: r4 = r3->field_7
    //     0x7cdc18: ldur            w4, [x3, #7]
    // 0x7cdc1c: DecompressPointer r4
    //     0x7cdc1c: add             x4, x4, HEAP, lsl #32
    // 0x7cdc20: stur            x4, [fp, #-0x20]
    // 0x7cdc24: cmp             w4, NULL
    // 0x7cdc28: b.eq            #0x7cdcd4
    // 0x7cdc2c: mov             x0, x4
    // 0x7cdc30: r2 = Null
    //     0x7cdc30: mov             x2, NULL
    // 0x7cdc34: r1 = Null
    //     0x7cdc34: mov             x1, NULL
    // 0x7cdc38: r4 = LoadClassIdInstr(r0)
    //     0x7cdc38: ldur            x4, [x0, #-1]
    //     0x7cdc3c: ubfx            x4, x4, #0xc, #0x14
    // 0x7cdc40: cmp             x4, #0xc77
    // 0x7cdc44: b.eq            #0x7cdc5c
    // 0x7cdc48: r8 = ToolbarItemsParentData
    //     0x7cdc48: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x7cdc4c: ldr             x8, [x8, #0x490]
    // 0x7cdc50: r3 = Null
    //     0x7cdc50: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b670] Null
    //     0x7cdc54: ldr             x3, [x3, #0x670]
    // 0x7cdc58: r0 = DefaultTypeTest()
    //     0x7cdc58: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7cdc5c: ldur            x0, [fp, #-0x20]
    // 0x7cdc60: LoadField: r1 = r0->field_f
    //     0x7cdc60: ldur            w1, [x0, #0xf]
    // 0x7cdc64: DecompressPointer r1
    //     0x7cdc64: add             x1, x1, HEAP, lsl #32
    // 0x7cdc68: r0 = LoadClassIdInstr(r1)
    //     0x7cdc68: ldur            x0, [x1, #-1]
    //     0x7cdc6c: ubfx            x0, x0, #0xc, #0x14
    // 0x7cdc70: ldur            x16, [fp, #-0x18]
    // 0x7cdc74: stp             x16, x1, [SP]
    // 0x7cdc78: mov             lr, x0
    // 0x7cdc7c: ldr             lr, [x21, lr, lsl #3]
    // 0x7cdc80: blr             lr
    // 0x7cdc84: tbnz            w0, #4, #0x7cdc98
    // 0x7cdc88: r0 = Null
    //     0x7cdc88: mov             x0, NULL
    // 0x7cdc8c: LeaveFrame
    //     0x7cdc8c: mov             SP, fp
    //     0x7cdc90: ldp             fp, lr, [SP], #0x10
    // 0x7cdc94: ret
    //     0x7cdc94: ret             
    // 0x7cdc98: ldur            x1, [fp, #-8]
    // 0x7cdc9c: ldur            x2, [fp, #-0x10]
    // 0x7cdca0: r0 = _removeFromChildList()
    //     0x7cdca0: bl              #0x7b2020  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::_removeFromChildList
    // 0x7cdca4: ldur            x1, [fp, #-8]
    // 0x7cdca8: ldur            x2, [fp, #-0x10]
    // 0x7cdcac: ldur            x3, [fp, #-0x18]
    // 0x7cdcb0: r0 = _insertIntoChildList()
    //     0x7cdcb0: bl              #0xda37fc  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::_insertIntoChildList
    // 0x7cdcb4: ldur            x1, [fp, #-8]
    // 0x7cdcb8: r0 = markNeedsLayout()
    //     0x7cdcb8: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0x7cdcbc: r0 = Null
    //     0x7cdcbc: mov             x0, NULL
    // 0x7cdcc0: LeaveFrame
    //     0x7cdcc0: mov             SP, fp
    //     0x7cdcc4: ldp             fp, lr, [SP], #0x10
    // 0x7cdcc8: ret
    //     0x7cdcc8: ret             
    // 0x7cdccc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cdccc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cdcd0: b               #0x7cdba4
    // 0x7cdcd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7cdcd4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ detach(/* No info */) {
    // ** addr: 0x8061b8, size: 0xe8
    // 0x8061b8: EnterFrame
    //     0x8061b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8061bc: mov             fp, SP
    // 0x8061c0: AllocStack(0x10)
    //     0x8061c0: sub             SP, SP, #0x10
    // 0x8061c4: SetupParameters(__RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin this /* r1 => r0, fp-0x8 */)
    //     0x8061c4: mov             x0, x1
    //     0x8061c8: stur            x1, [fp, #-8]
    // 0x8061cc: CheckStackOverflow
    //     0x8061cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8061d0: cmp             SP, x16
    //     0x8061d4: b.ls            #0x80628c
    // 0x8061d8: mov             x1, x0
    // 0x8061dc: r0 = detach()
    //     0x8061dc: bl              #0x8083b4  ; [package:flutter/src/rendering/object.dart] RenderObject::detach
    // 0x8061e0: ldur            x0, [fp, #-8]
    // 0x8061e4: LoadField: r1 = r0->field_5f
    //     0x8061e4: ldur            w1, [x0, #0x5f]
    // 0x8061e8: DecompressPointer r1
    //     0x8061e8: add             x1, x1, HEAP, lsl #32
    // 0x8061ec: mov             x2, x1
    // 0x8061f0: stur            x2, [fp, #-8]
    // 0x8061f4: CheckStackOverflow
    //     0x8061f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8061f8: cmp             SP, x16
    //     0x8061fc: b.ls            #0x806294
    // 0x806200: cmp             w2, NULL
    // 0x806204: b.eq            #0x80627c
    // 0x806208: r0 = LoadClassIdInstr(r2)
    //     0x806208: ldur            x0, [x2, #-1]
    //     0x80620c: ubfx            x0, x0, #0xc, #0x14
    // 0x806210: mov             x1, x2
    // 0x806214: r0 = GDT[cid_x0 + 0xeec9]()
    //     0x806214: movz            x17, #0xeec9
    //     0x806218: add             lr, x0, x17
    //     0x80621c: ldr             lr, [x21, lr, lsl #3]
    //     0x806220: blr             lr
    // 0x806224: ldur            x0, [fp, #-8]
    // 0x806228: LoadField: r3 = r0->field_7
    //     0x806228: ldur            w3, [x0, #7]
    // 0x80622c: DecompressPointer r3
    //     0x80622c: add             x3, x3, HEAP, lsl #32
    // 0x806230: stur            x3, [fp, #-0x10]
    // 0x806234: cmp             w3, NULL
    // 0x806238: b.eq            #0x80629c
    // 0x80623c: mov             x0, x3
    // 0x806240: r2 = Null
    //     0x806240: mov             x2, NULL
    // 0x806244: r1 = Null
    //     0x806244: mov             x1, NULL
    // 0x806248: r4 = LoadClassIdInstr(r0)
    //     0x806248: ldur            x4, [x0, #-1]
    //     0x80624c: ubfx            x4, x4, #0xc, #0x14
    // 0x806250: cmp             x4, #0xc77
    // 0x806254: b.eq            #0x80626c
    // 0x806258: r8 = ToolbarItemsParentData
    //     0x806258: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x80625c: ldr             x8, [x8, #0x490]
    // 0x806260: r3 = Null
    //     0x806260: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b630] Null
    //     0x806264: ldr             x3, [x3, #0x630]
    // 0x806268: r0 = DefaultTypeTest()
    //     0x806268: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x80626c: ldur            x1, [fp, #-0x10]
    // 0x806270: LoadField: r2 = r1->field_13
    //     0x806270: ldur            w2, [x1, #0x13]
    // 0x806274: DecompressPointer r2
    //     0x806274: add             x2, x2, HEAP, lsl #32
    // 0x806278: b               #0x8061f0
    // 0x80627c: r0 = Null
    //     0x80627c: mov             x0, NULL
    // 0x806280: LeaveFrame
    //     0x806280: mov             SP, fp
    //     0x806284: ldp             fp, lr, [SP], #0x10
    // 0x806288: ret
    //     0x806288: ret             
    // 0x80628c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80628c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806290: b               #0x8061d8
    // 0x806294: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x806294: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806298: b               #0x806200
    // 0x80629c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80629c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ redepthChildren(/* No info */) {
    // ** addr: 0x8086bc, size: 0xf8
    // 0x8086bc: EnterFrame
    //     0x8086bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8086c0: mov             fp, SP
    // 0x8086c4: AllocStack(0x18)
    //     0x8086c4: sub             SP, SP, #0x18
    // 0x8086c8: SetupParameters(__RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin this /* r1 => r2, fp-0x10 */)
    //     0x8086c8: mov             x2, x1
    //     0x8086cc: stur            x1, [fp, #-0x10]
    // 0x8086d0: CheckStackOverflow
    //     0x8086d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8086d4: cmp             SP, x16
    //     0x8086d8: b.ls            #0x8087a0
    // 0x8086dc: LoadField: r0 = r2->field_5f
    //     0x8086dc: ldur            w0, [x2, #0x5f]
    // 0x8086e0: DecompressPointer r0
    //     0x8086e0: add             x0, x0, HEAP, lsl #32
    // 0x8086e4: mov             x3, x0
    // 0x8086e8: stur            x3, [fp, #-8]
    // 0x8086ec: CheckStackOverflow
    //     0x8086ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8086f0: cmp             SP, x16
    //     0x8086f4: b.ls            #0x8087a8
    // 0x8086f8: cmp             w3, NULL
    // 0x8086fc: b.eq            #0x808790
    // 0x808700: LoadField: r0 = r3->field_b
    //     0x808700: ldur            x0, [x3, #0xb]
    // 0x808704: LoadField: r1 = r2->field_b
    //     0x808704: ldur            x1, [x2, #0xb]
    // 0x808708: cmp             x0, x1
    // 0x80870c: b.gt            #0x808734
    // 0x808710: add             x0, x1, #1
    // 0x808714: StoreField: r3->field_b = r0
    //     0x808714: stur            x0, [x3, #0xb]
    // 0x808718: r0 = LoadClassIdInstr(r3)
    //     0x808718: ldur            x0, [x3, #-1]
    //     0x80871c: ubfx            x0, x0, #0xc, #0x14
    // 0x808720: mov             x1, x3
    // 0x808724: r0 = GDT[cid_x0 + 0xedec]()
    //     0x808724: movz            x17, #0xedec
    //     0x808728: add             lr, x0, x17
    //     0x80872c: ldr             lr, [x21, lr, lsl #3]
    //     0x808730: blr             lr
    // 0x808734: ldur            x0, [fp, #-8]
    // 0x808738: LoadField: r3 = r0->field_7
    //     0x808738: ldur            w3, [x0, #7]
    // 0x80873c: DecompressPointer r3
    //     0x80873c: add             x3, x3, HEAP, lsl #32
    // 0x808740: stur            x3, [fp, #-0x18]
    // 0x808744: cmp             w3, NULL
    // 0x808748: b.eq            #0x8087b0
    // 0x80874c: mov             x0, x3
    // 0x808750: r2 = Null
    //     0x808750: mov             x2, NULL
    // 0x808754: r1 = Null
    //     0x808754: mov             x1, NULL
    // 0x808758: r4 = LoadClassIdInstr(r0)
    //     0x808758: ldur            x4, [x0, #-1]
    //     0x80875c: ubfx            x4, x4, #0xc, #0x14
    // 0x808760: cmp             x4, #0xc77
    // 0x808764: b.eq            #0x80877c
    // 0x808768: r8 = ToolbarItemsParentData
    //     0x808768: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x80876c: ldr             x8, [x8, #0x490]
    // 0x808770: r3 = Null
    //     0x808770: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b620] Null
    //     0x808774: ldr             x3, [x3, #0x620]
    // 0x808778: r0 = DefaultTypeTest()
    //     0x808778: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x80877c: ldur            x1, [fp, #-0x18]
    // 0x808780: LoadField: r3 = r1->field_13
    //     0x808780: ldur            w3, [x1, #0x13]
    // 0x808784: DecompressPointer r3
    //     0x808784: add             x3, x3, HEAP, lsl #32
    // 0x808788: ldur            x2, [fp, #-0x10]
    // 0x80878c: b               #0x8086e8
    // 0x808790: r0 = Null
    //     0x808790: mov             x0, NULL
    // 0x808794: LeaveFrame
    //     0x808794: mov             SP, fp
    //     0x808798: ldp             fp, lr, [SP], #0x10
    // 0x80879c: ret
    //     0x80879c: ret             
    // 0x8087a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8087a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8087a4: b               #0x8086dc
    // 0x8087a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8087a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8087ac: b               #0x8086f8
    // 0x8087b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8087b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _insertIntoChildList(/* No info */) {
    // ** addr: 0xda37fc, size: 0x570
    // 0xda37fc: EnterFrame
    //     0xda37fc: stp             fp, lr, [SP, #-0x10]!
    //     0xda3800: mov             fp, SP
    // 0xda3804: AllocStack(0x30)
    //     0xda3804: sub             SP, SP, #0x30
    // 0xda3808: SetupParameters(__RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xda3808: mov             x5, x1
    //     0xda380c: mov             x4, x2
    //     0xda3810: stur            x1, [fp, #-0x10]
    //     0xda3814: stur            x2, [fp, #-0x18]
    //     0xda3818: stur            x3, [fp, #-0x20]
    // 0xda381c: LoadField: r6 = r4->field_7
    //     0xda381c: ldur            w6, [x4, #7]
    // 0xda3820: DecompressPointer r6
    //     0xda3820: add             x6, x6, HEAP, lsl #32
    // 0xda3824: stur            x6, [fp, #-8]
    // 0xda3828: cmp             w6, NULL
    // 0xda382c: b.eq            #0xda3d5c
    // 0xda3830: mov             x0, x6
    // 0xda3834: r2 = Null
    //     0xda3834: mov             x2, NULL
    // 0xda3838: r1 = Null
    //     0xda3838: mov             x1, NULL
    // 0xda383c: r4 = LoadClassIdInstr(r0)
    //     0xda383c: ldur            x4, [x0, #-1]
    //     0xda3840: ubfx            x4, x4, #0xc, #0x14
    // 0xda3844: cmp             x4, #0xc77
    // 0xda3848: b.eq            #0xda3860
    // 0xda384c: r8 = ToolbarItemsParentData
    //     0xda384c: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0xda3850: ldr             x8, [x8, #0x490]
    // 0xda3854: r3 = Null
    //     0xda3854: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b680] Null
    //     0xda3858: ldr             x3, [x3, #0x680]
    // 0xda385c: r0 = DefaultTypeTest()
    //     0xda385c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda3860: ldur            x3, [fp, #-0x10]
    // 0xda3864: LoadField: r0 = r3->field_57
    //     0xda3864: ldur            x0, [x3, #0x57]
    // 0xda3868: add             x1, x0, #1
    // 0xda386c: StoreField: r3->field_57 = r1
    //     0xda386c: stur            x1, [x3, #0x57]
    // 0xda3870: ldur            x4, [fp, #-0x20]
    // 0xda3874: cmp             w4, NULL
    // 0xda3878: b.ne            #0xda3a00
    // 0xda387c: ldur            x4, [fp, #-8]
    // 0xda3880: LoadField: r5 = r3->field_5f
    //     0xda3880: ldur            w5, [x3, #0x5f]
    // 0xda3884: DecompressPointer r5
    //     0xda3884: add             x5, x5, HEAP, lsl #32
    // 0xda3888: stur            x5, [fp, #-0x28]
    // 0xda388c: LoadField: r2 = r4->field_b
    //     0xda388c: ldur            w2, [x4, #0xb]
    // 0xda3890: DecompressPointer r2
    //     0xda3890: add             x2, x2, HEAP, lsl #32
    // 0xda3894: mov             x0, x5
    // 0xda3898: r1 = Null
    //     0xda3898: mov             x1, NULL
    // 0xda389c: cmp             w0, NULL
    // 0xda38a0: b.eq            #0xda38cc
    // 0xda38a4: cmp             w2, NULL
    // 0xda38a8: b.eq            #0xda38cc
    // 0xda38ac: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda38ac: ldur            w4, [x2, #0x17]
    // 0xda38b0: DecompressPointer r4
    //     0xda38b0: add             x4, x4, HEAP, lsl #32
    // 0xda38b4: r8 = X0? bound RenderObject
    //     0xda38b4: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda38b8: ldr             x8, [x8, #0x1a8]
    // 0xda38bc: LoadField: r9 = r4->field_7
    //     0xda38bc: ldur            x9, [x4, #7]
    // 0xda38c0: r3 = Null
    //     0xda38c0: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b690] Null
    //     0xda38c4: ldr             x3, [x3, #0x690]
    // 0xda38c8: blr             x9
    // 0xda38cc: ldur            x0, [fp, #-0x28]
    // 0xda38d0: ldur            x3, [fp, #-8]
    // 0xda38d4: StoreField: r3->field_13 = r0
    //     0xda38d4: stur            w0, [x3, #0x13]
    //     0xda38d8: ldurb           w16, [x3, #-1]
    //     0xda38dc: ldurb           w17, [x0, #-1]
    //     0xda38e0: and             x16, x17, x16, lsr #2
    //     0xda38e4: tst             x16, HEAP, lsr #32
    //     0xda38e8: b.eq            #0xda38f0
    //     0xda38ec: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda38f0: ldur            x0, [fp, #-0x28]
    // 0xda38f4: cmp             w0, NULL
    // 0xda38f8: b.eq            #0xda39a8
    // 0xda38fc: LoadField: r3 = r0->field_7
    //     0xda38fc: ldur            w3, [x0, #7]
    // 0xda3900: DecompressPointer r3
    //     0xda3900: add             x3, x3, HEAP, lsl #32
    // 0xda3904: stur            x3, [fp, #-0x30]
    // 0xda3908: cmp             w3, NULL
    // 0xda390c: b.eq            #0xda3d60
    // 0xda3910: mov             x0, x3
    // 0xda3914: r2 = Null
    //     0xda3914: mov             x2, NULL
    // 0xda3918: r1 = Null
    //     0xda3918: mov             x1, NULL
    // 0xda391c: r4 = LoadClassIdInstr(r0)
    //     0xda391c: ldur            x4, [x0, #-1]
    //     0xda3920: ubfx            x4, x4, #0xc, #0x14
    // 0xda3924: cmp             x4, #0xc77
    // 0xda3928: b.eq            #0xda3940
    // 0xda392c: r8 = ToolbarItemsParentData
    //     0xda392c: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0xda3930: ldr             x8, [x8, #0x490]
    // 0xda3934: r3 = Null
    //     0xda3934: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b6a0] Null
    //     0xda3938: ldr             x3, [x3, #0x6a0]
    // 0xda393c: r0 = DefaultTypeTest()
    //     0xda393c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda3940: ldur            x3, [fp, #-0x30]
    // 0xda3944: LoadField: r2 = r3->field_b
    //     0xda3944: ldur            w2, [x3, #0xb]
    // 0xda3948: DecompressPointer r2
    //     0xda3948: add             x2, x2, HEAP, lsl #32
    // 0xda394c: ldur            x0, [fp, #-0x18]
    // 0xda3950: r1 = Null
    //     0xda3950: mov             x1, NULL
    // 0xda3954: cmp             w0, NULL
    // 0xda3958: b.eq            #0xda3984
    // 0xda395c: cmp             w2, NULL
    // 0xda3960: b.eq            #0xda3984
    // 0xda3964: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda3964: ldur            w4, [x2, #0x17]
    // 0xda3968: DecompressPointer r4
    //     0xda3968: add             x4, x4, HEAP, lsl #32
    // 0xda396c: r8 = X0? bound RenderObject
    //     0xda396c: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda3970: ldr             x8, [x8, #0x1a8]
    // 0xda3974: LoadField: r9 = r4->field_7
    //     0xda3974: ldur            x9, [x4, #7]
    // 0xda3978: r3 = Null
    //     0xda3978: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b6b0] Null
    //     0xda397c: ldr             x3, [x3, #0x6b0]
    // 0xda3980: blr             x9
    // 0xda3984: ldur            x0, [fp, #-0x18]
    // 0xda3988: ldur            x1, [fp, #-0x30]
    // 0xda398c: StoreField: r1->field_f = r0
    //     0xda398c: stur            w0, [x1, #0xf]
    //     0xda3990: ldurb           w16, [x1, #-1]
    //     0xda3994: ldurb           w17, [x0, #-1]
    //     0xda3998: and             x16, x17, x16, lsr #2
    //     0xda399c: tst             x16, HEAP, lsr #32
    //     0xda39a0: b.eq            #0xda39a8
    //     0xda39a4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda39a8: ldur            x5, [fp, #-0x10]
    // 0xda39ac: ldur            x0, [fp, #-0x18]
    // 0xda39b0: StoreField: r5->field_5f = r0
    //     0xda39b0: stur            w0, [x5, #0x5f]
    //     0xda39b4: ldurb           w16, [x5, #-1]
    //     0xda39b8: ldurb           w17, [x0, #-1]
    //     0xda39bc: and             x16, x17, x16, lsr #2
    //     0xda39c0: tst             x16, HEAP, lsr #32
    //     0xda39c4: b.eq            #0xda39cc
    //     0xda39c8: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda39cc: LoadField: r0 = r5->field_63
    //     0xda39cc: ldur            w0, [x5, #0x63]
    // 0xda39d0: DecompressPointer r0
    //     0xda39d0: add             x0, x0, HEAP, lsl #32
    // 0xda39d4: cmp             w0, NULL
    // 0xda39d8: b.ne            #0xda3d4c
    // 0xda39dc: ldur            x0, [fp, #-0x18]
    // 0xda39e0: StoreField: r5->field_63 = r0
    //     0xda39e0: stur            w0, [x5, #0x63]
    //     0xda39e4: ldurb           w16, [x5, #-1]
    //     0xda39e8: ldurb           w17, [x0, #-1]
    //     0xda39ec: and             x16, x17, x16, lsr #2
    //     0xda39f0: tst             x16, HEAP, lsr #32
    //     0xda39f4: b.eq            #0xda39fc
    //     0xda39f8: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda39fc: b               #0xda3d4c
    // 0xda3a00: mov             x5, x3
    // 0xda3a04: ldur            x3, [fp, #-8]
    // 0xda3a08: LoadField: r6 = r4->field_7
    //     0xda3a08: ldur            w6, [x4, #7]
    // 0xda3a0c: DecompressPointer r6
    //     0xda3a0c: add             x6, x6, HEAP, lsl #32
    // 0xda3a10: stur            x6, [fp, #-0x28]
    // 0xda3a14: cmp             w6, NULL
    // 0xda3a18: b.eq            #0xda3d64
    // 0xda3a1c: mov             x0, x6
    // 0xda3a20: r2 = Null
    //     0xda3a20: mov             x2, NULL
    // 0xda3a24: r1 = Null
    //     0xda3a24: mov             x1, NULL
    // 0xda3a28: r4 = LoadClassIdInstr(r0)
    //     0xda3a28: ldur            x4, [x0, #-1]
    //     0xda3a2c: ubfx            x4, x4, #0xc, #0x14
    // 0xda3a30: cmp             x4, #0xc77
    // 0xda3a34: b.eq            #0xda3a4c
    // 0xda3a38: r8 = ToolbarItemsParentData
    //     0xda3a38: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0xda3a3c: ldr             x8, [x8, #0x490]
    // 0xda3a40: r3 = Null
    //     0xda3a40: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b6c0] Null
    //     0xda3a44: ldr             x3, [x3, #0x6c0]
    // 0xda3a48: r0 = DefaultTypeTest()
    //     0xda3a48: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda3a4c: ldur            x3, [fp, #-0x28]
    // 0xda3a50: LoadField: r4 = r3->field_13
    //     0xda3a50: ldur            w4, [x3, #0x13]
    // 0xda3a54: DecompressPointer r4
    //     0xda3a54: add             x4, x4, HEAP, lsl #32
    // 0xda3a58: stur            x4, [fp, #-0x30]
    // 0xda3a5c: cmp             w4, NULL
    // 0xda3a60: b.ne            #0xda3b60
    // 0xda3a64: ldur            x5, [fp, #-0x10]
    // 0xda3a68: ldur            x4, [fp, #-8]
    // 0xda3a6c: LoadField: r2 = r4->field_b
    //     0xda3a6c: ldur            w2, [x4, #0xb]
    // 0xda3a70: DecompressPointer r2
    //     0xda3a70: add             x2, x2, HEAP, lsl #32
    // 0xda3a74: ldur            x0, [fp, #-0x20]
    // 0xda3a78: r1 = Null
    //     0xda3a78: mov             x1, NULL
    // 0xda3a7c: cmp             w0, NULL
    // 0xda3a80: b.eq            #0xda3aac
    // 0xda3a84: cmp             w2, NULL
    // 0xda3a88: b.eq            #0xda3aac
    // 0xda3a8c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda3a8c: ldur            w4, [x2, #0x17]
    // 0xda3a90: DecompressPointer r4
    //     0xda3a90: add             x4, x4, HEAP, lsl #32
    // 0xda3a94: r8 = X0? bound RenderObject
    //     0xda3a94: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda3a98: ldr             x8, [x8, #0x1a8]
    // 0xda3a9c: LoadField: r9 = r4->field_7
    //     0xda3a9c: ldur            x9, [x4, #7]
    // 0xda3aa0: r3 = Null
    //     0xda3aa0: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b6d0] Null
    //     0xda3aa4: ldr             x3, [x3, #0x6d0]
    // 0xda3aa8: blr             x9
    // 0xda3aac: ldur            x0, [fp, #-0x20]
    // 0xda3ab0: ldur            x3, [fp, #-8]
    // 0xda3ab4: StoreField: r3->field_f = r0
    //     0xda3ab4: stur            w0, [x3, #0xf]
    //     0xda3ab8: ldurb           w16, [x3, #-1]
    //     0xda3abc: ldurb           w17, [x0, #-1]
    //     0xda3ac0: and             x16, x17, x16, lsr #2
    //     0xda3ac4: tst             x16, HEAP, lsr #32
    //     0xda3ac8: b.eq            #0xda3ad0
    //     0xda3acc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda3ad0: ldur            x3, [fp, #-0x28]
    // 0xda3ad4: LoadField: r2 = r3->field_b
    //     0xda3ad4: ldur            w2, [x3, #0xb]
    // 0xda3ad8: DecompressPointer r2
    //     0xda3ad8: add             x2, x2, HEAP, lsl #32
    // 0xda3adc: ldur            x0, [fp, #-0x18]
    // 0xda3ae0: r1 = Null
    //     0xda3ae0: mov             x1, NULL
    // 0xda3ae4: cmp             w0, NULL
    // 0xda3ae8: b.eq            #0xda3b14
    // 0xda3aec: cmp             w2, NULL
    // 0xda3af0: b.eq            #0xda3b14
    // 0xda3af4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda3af4: ldur            w4, [x2, #0x17]
    // 0xda3af8: DecompressPointer r4
    //     0xda3af8: add             x4, x4, HEAP, lsl #32
    // 0xda3afc: r8 = X0? bound RenderObject
    //     0xda3afc: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda3b00: ldr             x8, [x8, #0x1a8]
    // 0xda3b04: LoadField: r9 = r4->field_7
    //     0xda3b04: ldur            x9, [x4, #7]
    // 0xda3b08: r3 = Null
    //     0xda3b08: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b6e0] Null
    //     0xda3b0c: ldr             x3, [x3, #0x6e0]
    // 0xda3b10: blr             x9
    // 0xda3b14: ldur            x0, [fp, #-0x18]
    // 0xda3b18: ldur            x5, [fp, #-0x28]
    // 0xda3b1c: StoreField: r5->field_13 = r0
    //     0xda3b1c: stur            w0, [x5, #0x13]
    //     0xda3b20: ldurb           w16, [x5, #-1]
    //     0xda3b24: ldurb           w17, [x0, #-1]
    //     0xda3b28: and             x16, x17, x16, lsr #2
    //     0xda3b2c: tst             x16, HEAP, lsr #32
    //     0xda3b30: b.eq            #0xda3b38
    //     0xda3b34: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda3b38: ldur            x0, [fp, #-0x18]
    // 0xda3b3c: ldur            x1, [fp, #-0x10]
    // 0xda3b40: StoreField: r1->field_63 = r0
    //     0xda3b40: stur            w0, [x1, #0x63]
    //     0xda3b44: ldurb           w16, [x1, #-1]
    //     0xda3b48: ldurb           w17, [x0, #-1]
    //     0xda3b4c: and             x16, x17, x16, lsr #2
    //     0xda3b50: tst             x16, HEAP, lsr #32
    //     0xda3b54: b.eq            #0xda3b5c
    //     0xda3b58: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda3b5c: b               #0xda3d4c
    // 0xda3b60: mov             x5, x3
    // 0xda3b64: ldur            x3, [fp, #-8]
    // 0xda3b68: LoadField: r6 = r3->field_b
    //     0xda3b68: ldur            w6, [x3, #0xb]
    // 0xda3b6c: DecompressPointer r6
    //     0xda3b6c: add             x6, x6, HEAP, lsl #32
    // 0xda3b70: mov             x0, x4
    // 0xda3b74: mov             x2, x6
    // 0xda3b78: stur            x6, [fp, #-0x10]
    // 0xda3b7c: r1 = Null
    //     0xda3b7c: mov             x1, NULL
    // 0xda3b80: cmp             w0, NULL
    // 0xda3b84: b.eq            #0xda3bb0
    // 0xda3b88: cmp             w2, NULL
    // 0xda3b8c: b.eq            #0xda3bb0
    // 0xda3b90: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda3b90: ldur            w4, [x2, #0x17]
    // 0xda3b94: DecompressPointer r4
    //     0xda3b94: add             x4, x4, HEAP, lsl #32
    // 0xda3b98: r8 = X0? bound RenderObject
    //     0xda3b98: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda3b9c: ldr             x8, [x8, #0x1a8]
    // 0xda3ba0: LoadField: r9 = r4->field_7
    //     0xda3ba0: ldur            x9, [x4, #7]
    // 0xda3ba4: r3 = Null
    //     0xda3ba4: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b6f0] Null
    //     0xda3ba8: ldr             x3, [x3, #0x6f0]
    // 0xda3bac: blr             x9
    // 0xda3bb0: ldur            x0, [fp, #-0x30]
    // 0xda3bb4: ldur            x3, [fp, #-8]
    // 0xda3bb8: StoreField: r3->field_13 = r0
    //     0xda3bb8: stur            w0, [x3, #0x13]
    //     0xda3bbc: ldurb           w16, [x3, #-1]
    //     0xda3bc0: ldurb           w17, [x0, #-1]
    //     0xda3bc4: and             x16, x17, x16, lsr #2
    //     0xda3bc8: tst             x16, HEAP, lsr #32
    //     0xda3bcc: b.eq            #0xda3bd4
    //     0xda3bd0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda3bd4: ldur            x0, [fp, #-0x20]
    // 0xda3bd8: ldur            x2, [fp, #-0x10]
    // 0xda3bdc: r1 = Null
    //     0xda3bdc: mov             x1, NULL
    // 0xda3be0: cmp             w0, NULL
    // 0xda3be4: b.eq            #0xda3c10
    // 0xda3be8: cmp             w2, NULL
    // 0xda3bec: b.eq            #0xda3c10
    // 0xda3bf0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda3bf0: ldur            w4, [x2, #0x17]
    // 0xda3bf4: DecompressPointer r4
    //     0xda3bf4: add             x4, x4, HEAP, lsl #32
    // 0xda3bf8: r8 = X0? bound RenderObject
    //     0xda3bf8: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda3bfc: ldr             x8, [x8, #0x1a8]
    // 0xda3c00: LoadField: r9 = r4->field_7
    //     0xda3c00: ldur            x9, [x4, #7]
    // 0xda3c04: r3 = Null
    //     0xda3c04: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b700] Null
    //     0xda3c08: ldr             x3, [x3, #0x700]
    // 0xda3c0c: blr             x9
    // 0xda3c10: ldur            x0, [fp, #-0x20]
    // 0xda3c14: ldur            x1, [fp, #-8]
    // 0xda3c18: StoreField: r1->field_f = r0
    //     0xda3c18: stur            w0, [x1, #0xf]
    //     0xda3c1c: ldurb           w16, [x1, #-1]
    //     0xda3c20: ldurb           w17, [x0, #-1]
    //     0xda3c24: and             x16, x17, x16, lsr #2
    //     0xda3c28: tst             x16, HEAP, lsr #32
    //     0xda3c2c: b.eq            #0xda3c34
    //     0xda3c30: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda3c34: ldur            x0, [fp, #-0x30]
    // 0xda3c38: LoadField: r3 = r0->field_7
    //     0xda3c38: ldur            w3, [x0, #7]
    // 0xda3c3c: DecompressPointer r3
    //     0xda3c3c: add             x3, x3, HEAP, lsl #32
    // 0xda3c40: stur            x3, [fp, #-8]
    // 0xda3c44: cmp             w3, NULL
    // 0xda3c48: b.eq            #0xda3d68
    // 0xda3c4c: mov             x0, x3
    // 0xda3c50: r2 = Null
    //     0xda3c50: mov             x2, NULL
    // 0xda3c54: r1 = Null
    //     0xda3c54: mov             x1, NULL
    // 0xda3c58: r4 = LoadClassIdInstr(r0)
    //     0xda3c58: ldur            x4, [x0, #-1]
    //     0xda3c5c: ubfx            x4, x4, #0xc, #0x14
    // 0xda3c60: cmp             x4, #0xc77
    // 0xda3c64: b.eq            #0xda3c7c
    // 0xda3c68: r8 = ToolbarItemsParentData
    //     0xda3c68: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0xda3c6c: ldr             x8, [x8, #0x490]
    // 0xda3c70: r3 = Null
    //     0xda3c70: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b710] Null
    //     0xda3c74: ldr             x3, [x3, #0x710]
    // 0xda3c78: r0 = DefaultTypeTest()
    //     0xda3c78: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda3c7c: ldur            x3, [fp, #-0x28]
    // 0xda3c80: LoadField: r2 = r3->field_b
    //     0xda3c80: ldur            w2, [x3, #0xb]
    // 0xda3c84: DecompressPointer r2
    //     0xda3c84: add             x2, x2, HEAP, lsl #32
    // 0xda3c88: ldur            x0, [fp, #-0x18]
    // 0xda3c8c: r1 = Null
    //     0xda3c8c: mov             x1, NULL
    // 0xda3c90: cmp             w0, NULL
    // 0xda3c94: b.eq            #0xda3cc0
    // 0xda3c98: cmp             w2, NULL
    // 0xda3c9c: b.eq            #0xda3cc0
    // 0xda3ca0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda3ca0: ldur            w4, [x2, #0x17]
    // 0xda3ca4: DecompressPointer r4
    //     0xda3ca4: add             x4, x4, HEAP, lsl #32
    // 0xda3ca8: r8 = X0? bound RenderObject
    //     0xda3ca8: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda3cac: ldr             x8, [x8, #0x1a8]
    // 0xda3cb0: LoadField: r9 = r4->field_7
    //     0xda3cb0: ldur            x9, [x4, #7]
    // 0xda3cb4: r3 = Null
    //     0xda3cb4: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b720] Null
    //     0xda3cb8: ldr             x3, [x3, #0x720]
    // 0xda3cbc: blr             x9
    // 0xda3cc0: ldur            x0, [fp, #-0x18]
    // 0xda3cc4: ldur            x1, [fp, #-0x28]
    // 0xda3cc8: StoreField: r1->field_13 = r0
    //     0xda3cc8: stur            w0, [x1, #0x13]
    //     0xda3ccc: ldurb           w16, [x1, #-1]
    //     0xda3cd0: ldurb           w17, [x0, #-1]
    //     0xda3cd4: and             x16, x17, x16, lsr #2
    //     0xda3cd8: tst             x16, HEAP, lsr #32
    //     0xda3cdc: b.eq            #0xda3ce4
    //     0xda3ce0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda3ce4: ldur            x3, [fp, #-8]
    // 0xda3ce8: LoadField: r2 = r3->field_b
    //     0xda3ce8: ldur            w2, [x3, #0xb]
    // 0xda3cec: DecompressPointer r2
    //     0xda3cec: add             x2, x2, HEAP, lsl #32
    // 0xda3cf0: ldur            x0, [fp, #-0x18]
    // 0xda3cf4: r1 = Null
    //     0xda3cf4: mov             x1, NULL
    // 0xda3cf8: cmp             w0, NULL
    // 0xda3cfc: b.eq            #0xda3d28
    // 0xda3d00: cmp             w2, NULL
    // 0xda3d04: b.eq            #0xda3d28
    // 0xda3d08: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda3d08: ldur            w4, [x2, #0x17]
    // 0xda3d0c: DecompressPointer r4
    //     0xda3d0c: add             x4, x4, HEAP, lsl #32
    // 0xda3d10: r8 = X0? bound RenderObject
    //     0xda3d10: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda3d14: ldr             x8, [x8, #0x1a8]
    // 0xda3d18: LoadField: r9 = r4->field_7
    //     0xda3d18: ldur            x9, [x4, #7]
    // 0xda3d1c: r3 = Null
    //     0xda3d1c: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b730] Null
    //     0xda3d20: ldr             x3, [x3, #0x730]
    // 0xda3d24: blr             x9
    // 0xda3d28: ldur            x0, [fp, #-0x18]
    // 0xda3d2c: ldur            x1, [fp, #-8]
    // 0xda3d30: StoreField: r1->field_f = r0
    //     0xda3d30: stur            w0, [x1, #0xf]
    //     0xda3d34: ldurb           w16, [x1, #-1]
    //     0xda3d38: ldurb           w17, [x0, #-1]
    //     0xda3d3c: and             x16, x17, x16, lsr #2
    //     0xda3d40: tst             x16, HEAP, lsr #32
    //     0xda3d44: b.eq            #0xda3d4c
    //     0xda3d48: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda3d4c: r0 = Null
    //     0xda3d4c: mov             x0, NULL
    // 0xda3d50: LeaveFrame
    //     0xda3d50: mov             SP, fp
    //     0xda3d54: ldp             fp, lr, [SP], #0x10
    // 0xda3d58: ret
    //     0xda3d58: ret             
    // 0xda3d5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda3d5c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda3d60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda3d60: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda3d64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda3d64: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda3d68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda3d68: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3066, size: 0x78, field offset: 0x68
class _RenderTextSelectionToolbarItemsLayout extends __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin {

  _ performLayout(/* No info */) {
    // ** addr: 0x76c890, size: 0x104
    // 0x76c890: EnterFrame
    //     0x76c890: stp             fp, lr, [SP, #-0x10]!
    //     0x76c894: mov             fp, SP
    // 0x76c898: AllocStack(0x10)
    //     0x76c898: sub             SP, SP, #0x10
    // 0x76c89c: r0 = -1
    //     0x76c89c: movn            x0, #0
    // 0x76c8a0: mov             x3, x1
    // 0x76c8a4: stur            x1, [fp, #-0x10]
    // 0x76c8a8: CheckStackOverflow
    //     0x76c8a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76c8ac: cmp             SP, x16
    //     0x76c8b0: b.ls            #0x76c98c
    // 0x76c8b4: StoreField: r3->field_67 = r0
    //     0x76c8b4: stur            x0, [x3, #0x67]
    // 0x76c8b8: LoadField: r0 = r3->field_5f
    //     0x76c8b8: ldur            w0, [x3, #0x5f]
    // 0x76c8bc: DecompressPointer r0
    //     0x76c8bc: add             x0, x0, HEAP, lsl #32
    // 0x76c8c0: cmp             w0, NULL
    // 0x76c8c4: b.ne            #0x76c944
    // 0x76c8c8: LoadField: r4 = r3->field_27
    //     0x76c8c8: ldur            w4, [x3, #0x27]
    // 0x76c8cc: DecompressPointer r4
    //     0x76c8cc: add             x4, x4, HEAP, lsl #32
    // 0x76c8d0: stur            x4, [fp, #-8]
    // 0x76c8d4: cmp             w4, NULL
    // 0x76c8d8: b.eq            #0x76c970
    // 0x76c8dc: mov             x0, x4
    // 0x76c8e0: r2 = Null
    //     0x76c8e0: mov             x2, NULL
    // 0x76c8e4: r1 = Null
    //     0x76c8e4: mov             x1, NULL
    // 0x76c8e8: r4 = LoadClassIdInstr(r0)
    //     0x76c8e8: ldur            x4, [x0, #-1]
    //     0x76c8ec: ubfx            x4, x4, #0xc, #0x14
    // 0x76c8f0: sub             x4, x4, #0xc83
    // 0x76c8f4: cmp             x4, #1
    // 0x76c8f8: b.ls            #0x76c90c
    // 0x76c8fc: r8 = BoxConstraints
    //     0x76c8fc: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76c900: r3 = Null
    //     0x76c900: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b4f0] Null
    //     0x76c904: ldr             x3, [x3, #0x4f0]
    // 0x76c908: r0 = BoxConstraints()
    //     0x76c908: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76c90c: ldur            x1, [fp, #-8]
    // 0x76c910: r0 = smallest()
    //     0x76c910: bl              #0x7335c0  ; [package:flutter/src/rendering/box.dart] BoxConstraints::smallest
    // 0x76c914: ldur            x2, [fp, #-0x10]
    // 0x76c918: StoreField: r2->field_53 = r0
    //     0x76c918: stur            w0, [x2, #0x53]
    //     0x76c91c: ldurb           w16, [x2, #-1]
    //     0x76c920: ldurb           w17, [x0, #-1]
    //     0x76c924: and             x16, x17, x16, lsr #2
    //     0x76c928: tst             x16, HEAP, lsr #32
    //     0x76c92c: b.eq            #0x76c934
    //     0x76c930: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x76c934: r0 = Null
    //     0x76c934: mov             x0, NULL
    // 0x76c938: LeaveFrame
    //     0x76c938: mov             SP, fp
    //     0x76c93c: ldp             fp, lr, [SP], #0x10
    // 0x76c940: ret
    //     0x76c940: ret             
    // 0x76c944: mov             x2, x3
    // 0x76c948: mov             x1, x2
    // 0x76c94c: r0 = _layoutChildren()
    //     0x76c94c: bl              #0x76d444  ; [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::_layoutChildren
    // 0x76c950: ldur            x1, [fp, #-0x10]
    // 0x76c954: r0 = _placeChildren()
    //     0x76c954: bl              #0x76cc6c  ; [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::_placeChildren
    // 0x76c958: ldur            x1, [fp, #-0x10]
    // 0x76c95c: r0 = _resizeChildrenWhenOverflow()
    //     0x76c95c: bl              #0x76c994  ; [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::_resizeChildrenWhenOverflow
    // 0x76c960: r0 = Null
    //     0x76c960: mov             x0, NULL
    // 0x76c964: LeaveFrame
    //     0x76c964: mov             SP, fp
    //     0x76c968: ldp             fp, lr, [SP], #0x10
    // 0x76c96c: ret
    //     0x76c96c: ret             
    // 0x76c970: r0 = StateError()
    //     0x76c970: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76c974: mov             x1, x0
    // 0x76c978: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76c978: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76c97c: StoreField: r1->field_b = r0
    //     0x76c97c: stur            w0, [x1, #0xb]
    // 0x76c980: mov             x0, x1
    // 0x76c984: r0 = Throw()
    //     0x76c984: bl              #0xec04b8  ; ThrowStub
    // 0x76c988: brk             #0
    // 0x76c98c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76c98c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76c990: b               #0x76c8b4
  }
  _ _resizeChildrenWhenOverflow(/* No info */) {
    // ** addr: 0x76c994, size: 0xa0
    // 0x76c994: EnterFrame
    //     0x76c994: stp             fp, lr, [SP, #-0x10]!
    //     0x76c998: mov             fp, SP
    // 0x76c99c: AllocStack(0x8)
    //     0x76c99c: sub             SP, SP, #8
    // 0x76c9a0: SetupParameters(_RenderTextSelectionToolbarItemsLayout this /* r1 => r1, fp-0x8 */)
    //     0x76c9a0: stur            x1, [fp, #-8]
    // 0x76c9a4: CheckStackOverflow
    //     0x76c9a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76c9a8: cmp             SP, x16
    //     0x76c9ac: b.ls            #0x76ca28
    // 0x76c9b0: r1 = 3
    //     0x76c9b0: movz            x1, #0x3
    // 0x76c9b4: r0 = AllocateContext()
    //     0x76c9b4: bl              #0xec126c  ; AllocateContextStub
    // 0x76c9b8: mov             x1, x0
    // 0x76c9bc: ldur            x0, [fp, #-8]
    // 0x76c9c0: StoreField: r1->field_f = r0
    //     0x76c9c0: stur            w0, [x1, #0xf]
    // 0x76c9c4: LoadField: r2 = r0->field_73
    //     0x76c9c4: ldur            w2, [x0, #0x73]
    // 0x76c9c8: DecompressPointer r2
    //     0x76c9c8: add             x2, x2, HEAP, lsl #32
    // 0x76c9cc: tbz             w2, #4, #0x76c9e0
    // 0x76c9d0: r0 = Null
    //     0x76c9d0: mov             x0, NULL
    // 0x76c9d4: LeaveFrame
    //     0x76c9d4: mov             SP, fp
    //     0x76c9d8: ldp             fp, lr, [SP], #0x10
    // 0x76c9dc: ret
    //     0x76c9dc: ret             
    // 0x76c9e0: r2 = -2
    //     0x76c9e0: orr             x2, xzr, #0xfffffffffffffffe
    // 0x76c9e4: LoadField: r3 = r0->field_5f
    //     0x76c9e4: ldur            w3, [x0, #0x5f]
    // 0x76c9e8: DecompressPointer r3
    //     0x76c9e8: add             x3, x3, HEAP, lsl #32
    // 0x76c9ec: cmp             w3, NULL
    // 0x76c9f0: b.eq            #0x76ca30
    // 0x76c9f4: StoreField: r1->field_13 = r3
    //     0x76c9f4: stur            w3, [x1, #0x13]
    // 0x76c9f8: ArrayStore: r1[0] = r2  ; List_4
    //     0x76c9f8: stur            w2, [x1, #0x17]
    // 0x76c9fc: mov             x2, x1
    // 0x76ca00: r1 = Function '<anonymous closure>':.
    //     0x76ca00: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5b500] AnonymousClosure: (0x76ca34), in [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::_resizeChildrenWhenOverflow (0x76c994)
    //     0x76ca04: ldr             x1, [x1, #0x500]
    // 0x76ca08: r0 = AllocateClosure()
    //     0x76ca08: bl              #0xec1630  ; AllocateClosureStub
    // 0x76ca0c: ldur            x1, [fp, #-8]
    // 0x76ca10: mov             x2, x0
    // 0x76ca14: r0 = visitChildren()
    //     0x76ca14: bl              #0x786888  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::visitChildren
    // 0x76ca18: r0 = Null
    //     0x76ca18: mov             x0, NULL
    // 0x76ca1c: LeaveFrame
    //     0x76ca1c: mov             SP, fp
    //     0x76ca20: ldp             fp, lr, [SP], #0x10
    // 0x76ca24: ret
    //     0x76ca24: ret             
    // 0x76ca28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76ca28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76ca2c: b               #0x76c9b0
    // 0x76ca30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76ca30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x76ca34, size: 0x1d0
    // 0x76ca34: EnterFrame
    //     0x76ca34: stp             fp, lr, [SP, #-0x10]!
    //     0x76ca38: mov             fp, SP
    // 0x76ca3c: AllocStack(0x20)
    //     0x76ca3c: sub             SP, SP, #0x20
    // 0x76ca40: SetupParameters()
    //     0x76ca40: ldr             x0, [fp, #0x18]
    //     0x76ca44: ldur            w3, [x0, #0x17]
    //     0x76ca48: add             x3, x3, HEAP, lsl #32
    //     0x76ca4c: stur            x3, [fp, #-8]
    // 0x76ca50: CheckStackOverflow
    //     0x76ca50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76ca54: cmp             SP, x16
    //     0x76ca58: b.ls            #0x76cbf8
    // 0x76ca5c: ldr             x0, [fp, #0x10]
    // 0x76ca60: r2 = Null
    //     0x76ca60: mov             x2, NULL
    // 0x76ca64: r1 = Null
    //     0x76ca64: mov             x1, NULL
    // 0x76ca68: r4 = LoadClassIdInstr(r0)
    //     0x76ca68: ldur            x4, [x0, #-1]
    //     0x76ca6c: ubfx            x4, x4, #0xc, #0x14
    // 0x76ca70: sub             x4, x4, #0xbba
    // 0x76ca74: cmp             x4, #0x9a
    // 0x76ca78: b.ls            #0x76ca8c
    // 0x76ca7c: r8 = RenderBox
    //     0x76ca7c: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x76ca80: r3 = Null
    //     0x76ca80: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b508] Null
    //     0x76ca84: ldr             x3, [x3, #0x508]
    // 0x76ca88: r0 = RenderBox()
    //     0x76ca88: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x76ca8c: ldr             x3, [fp, #0x10]
    // 0x76ca90: LoadField: r4 = r3->field_7
    //     0x76ca90: ldur            w4, [x3, #7]
    // 0x76ca94: DecompressPointer r4
    //     0x76ca94: add             x4, x4, HEAP, lsl #32
    // 0x76ca98: stur            x4, [fp, #-0x10]
    // 0x76ca9c: cmp             w4, NULL
    // 0x76caa0: b.eq            #0x76cc00
    // 0x76caa4: mov             x0, x4
    // 0x76caa8: r2 = Null
    //     0x76caa8: mov             x2, NULL
    // 0x76caac: r1 = Null
    //     0x76caac: mov             x1, NULL
    // 0x76cab0: r4 = LoadClassIdInstr(r0)
    //     0x76cab0: ldur            x4, [x0, #-1]
    //     0x76cab4: ubfx            x4, x4, #0xc, #0x14
    // 0x76cab8: cmp             x4, #0xc77
    // 0x76cabc: b.eq            #0x76cad4
    // 0x76cac0: r8 = ToolbarItemsParentData
    //     0x76cac0: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x76cac4: ldr             x8, [x8, #0x490]
    // 0x76cac8: r3 = Null
    //     0x76cac8: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b518] Null
    //     0x76cacc: ldr             x3, [x3, #0x518]
    // 0x76cad0: r0 = DefaultTypeTest()
    //     0x76cad0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x76cad4: ldur            x4, [fp, #-8]
    // 0x76cad8: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x76cad8: ldur            w0, [x4, #0x17]
    // 0x76cadc: DecompressPointer r0
    //     0x76cadc: add             x0, x0, HEAP, lsl #32
    // 0x76cae0: r1 = LoadInt32Instr(r0)
    //     0x76cae0: sbfx            x1, x0, #1, #0x1f
    //     0x76cae4: tbz             w0, #0, #0x76caec
    //     0x76cae8: ldur            x1, [x0, #7]
    // 0x76caec: add             x3, x1, #1
    // 0x76caf0: r0 = BoxInt64Instr(r3)
    //     0x76caf0: sbfiz           x0, x3, #1, #0x1f
    //     0x76caf4: cmp             x3, x0, asr #1
    //     0x76caf8: b.eq            #0x76cb04
    //     0x76cafc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x76cb00: stur            x3, [x0, #7]
    // 0x76cb04: ArrayStore: r4[0] = r0  ; List_4
    //     0x76cb04: stur            w0, [x4, #0x17]
    //     0x76cb08: tbz             w0, #0, #0x76cb24
    //     0x76cb0c: ldurb           w16, [x4, #-1]
    //     0x76cb10: ldurb           w17, [x0, #-1]
    //     0x76cb14: and             x16, x17, x16, lsr #2
    //     0x76cb18: tst             x16, HEAP, lsr #32
    //     0x76cb1c: b.eq            #0x76cb24
    //     0x76cb20: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x76cb24: LoadField: r0 = r4->field_13
    //     0x76cb24: ldur            w0, [x4, #0x13]
    // 0x76cb28: DecompressPointer r0
    //     0x76cb28: add             x0, x0, HEAP, lsl #32
    // 0x76cb2c: ldr             x5, [fp, #0x10]
    // 0x76cb30: cmp             w5, w0
    // 0x76cb34: b.ne            #0x76cb48
    // 0x76cb38: r0 = Null
    //     0x76cb38: mov             x0, NULL
    // 0x76cb3c: LeaveFrame
    //     0x76cb3c: mov             SP, fp
    //     0x76cb40: ldp             fp, lr, [SP], #0x10
    // 0x76cb44: ret
    //     0x76cb44: ret             
    // 0x76cb48: LoadField: r1 = r4->field_f
    //     0x76cb48: ldur            w1, [x4, #0xf]
    // 0x76cb4c: DecompressPointer r1
    //     0x76cb4c: add             x1, x1, HEAP, lsl #32
    // 0x76cb50: mov             x2, x5
    // 0x76cb54: r0 = _shouldPaintChild()
    //     0x76cb54: bl              #0x76cc04  ; [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::_shouldPaintChild
    // 0x76cb58: tbz             w0, #4, #0x76cb78
    // 0x76cb5c: ldur            x0, [fp, #-0x10]
    // 0x76cb60: r1 = false
    //     0x76cb60: add             x1, NULL, #0x30  ; false
    // 0x76cb64: ArrayStore: r0[0] = r1  ; List_4
    //     0x76cb64: stur            w1, [x0, #0x17]
    // 0x76cb68: r0 = Null
    //     0x76cb68: mov             x0, NULL
    // 0x76cb6c: LeaveFrame
    //     0x76cb6c: mov             SP, fp
    //     0x76cb70: ldp             fp, lr, [SP], #0x10
    // 0x76cb74: ret
    //     0x76cb74: ret             
    // 0x76cb78: ldr             x2, [fp, #0x10]
    // 0x76cb7c: ldur            x0, [fp, #-8]
    // 0x76cb80: LoadField: r1 = r0->field_f
    //     0x76cb80: ldur            w1, [x0, #0xf]
    // 0x76cb84: DecompressPointer r1
    //     0x76cb84: add             x1, x1, HEAP, lsl #32
    // 0x76cb88: r0 = size()
    //     0x76cb88: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76cb8c: LoadField: d0 = r0->field_7
    //     0x76cb8c: ldur            d0, [x0, #7]
    // 0x76cb90: stur            d0, [fp, #-0x18]
    // 0x76cb94: r0 = BoxConstraints()
    //     0x76cb94: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x76cb98: ldur            d0, [fp, #-0x18]
    // 0x76cb9c: StoreField: r0->field_7 = d0
    //     0x76cb9c: stur            d0, [x0, #7]
    // 0x76cba0: StoreField: r0->field_f = d0
    //     0x76cba0: stur            d0, [x0, #0xf]
    // 0x76cba4: ArrayStore: r0[0] = rZR  ; List_8
    //     0x76cba4: stur            xzr, [x0, #0x17]
    // 0x76cba8: d0 = inf
    //     0x76cba8: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x76cbac: StoreField: r0->field_1f = d0
    //     0x76cbac: stur            d0, [x0, #0x1f]
    // 0x76cbb0: ldr             x1, [fp, #0x10]
    // 0x76cbb4: r2 = LoadClassIdInstr(r1)
    //     0x76cbb4: ldur            x2, [x1, #-1]
    //     0x76cbb8: ubfx            x2, x2, #0xc, #0x14
    // 0x76cbbc: r16 = true
    //     0x76cbbc: add             x16, NULL, #0x20  ; true
    // 0x76cbc0: str             x16, [SP]
    // 0x76cbc4: mov             x16, x0
    // 0x76cbc8: mov             x0, x2
    // 0x76cbcc: mov             x2, x16
    // 0x76cbd0: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x76cbd0: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x76cbd4: ldr             x4, [x4, #0x5c0]
    // 0x76cbd8: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x76cbd8: movz            x17, #0xed1d
    //     0x76cbdc: add             lr, x0, x17
    //     0x76cbe0: ldr             lr, [x21, lr, lsl #3]
    //     0x76cbe4: blr             lr
    // 0x76cbe8: r0 = Null
    //     0x76cbe8: mov             x0, NULL
    // 0x76cbec: LeaveFrame
    //     0x76cbec: mov             SP, fp
    //     0x76cbf0: ldp             fp, lr, [SP], #0x10
    // 0x76cbf4: ret
    //     0x76cbf4: ret             
    // 0x76cbf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76cbf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76cbfc: b               #0x76ca5c
    // 0x76cc00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76cc00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _shouldPaintChild(/* No info */) {
    // ** addr: 0x76cc04, size: 0x68
    // 0x76cc04: LoadField: r4 = r1->field_5f
    //     0x76cc04: ldur            w4, [x1, #0x5f]
    // 0x76cc08: DecompressPointer r4
    //     0x76cc08: add             x4, x4, HEAP, lsl #32
    // 0x76cc0c: cmp             w2, w4
    // 0x76cc10: b.ne            #0x76cc2c
    // 0x76cc14: LoadField: r2 = r1->field_67
    //     0x76cc14: ldur            x2, [x1, #0x67]
    // 0x76cc18: cmn             x2, #1
    // 0x76cc1c: r16 = true
    //     0x76cc1c: add             x16, NULL, #0x20  ; true
    // 0x76cc20: r17 = false
    //     0x76cc20: add             x17, NULL, #0x30  ; false
    // 0x76cc24: csel            x0, x16, x17, ne
    // 0x76cc28: ret
    //     0x76cc28: ret             
    // 0x76cc2c: LoadField: r2 = r1->field_67
    //     0x76cc2c: ldur            x2, [x1, #0x67]
    // 0x76cc30: cmn             x2, #1
    // 0x76cc34: b.ne            #0x76cc40
    // 0x76cc38: r0 = true
    //     0x76cc38: add             x0, NULL, #0x20  ; true
    // 0x76cc3c: ret
    //     0x76cc3c: ret             
    // 0x76cc40: cmp             x3, x2
    // 0x76cc44: r16 = true
    //     0x76cc44: add             x16, NULL, #0x20  ; true
    // 0x76cc48: r17 = false
    //     0x76cc48: add             x17, NULL, #0x30  ; false
    // 0x76cc4c: csel            x4, x16, x17, gt
    // 0x76cc50: LoadField: r2 = r1->field_73
    //     0x76cc50: ldur            w2, [x1, #0x73]
    // 0x76cc54: DecompressPointer r2
    //     0x76cc54: add             x2, x2, HEAP, lsl #32
    // 0x76cc58: cmp             w4, w2
    // 0x76cc5c: r16 = true
    //     0x76cc5c: add             x16, NULL, #0x20  ; true
    // 0x76cc60: r17 = false
    //     0x76cc60: add             x17, NULL, #0x30  ; false
    // 0x76cc64: csel            x0, x16, x17, eq
    // 0x76cc68: ret
    //     0x76cc68: ret             
  }
  _ _placeChildren(/* No info */) {
    // ** addr: 0x76cc6c, size: 0x3a8
    // 0x76cc6c: EnterFrame
    //     0x76cc6c: stp             fp, lr, [SP, #-0x10]!
    //     0x76cc70: mov             fp, SP
    // 0x76cc74: AllocStack(0x40)
    //     0x76cc74: sub             SP, SP, #0x40
    // 0x76cc78: SetupParameters(_RenderTextSelectionToolbarItemsLayout this /* r1 => r1, fp-0x8 */)
    //     0x76cc78: stur            x1, [fp, #-8]
    // 0x76cc7c: CheckStackOverflow
    //     0x76cc7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76cc80: cmp             SP, x16
    //     0x76cc84: b.ls            #0x76cfe0
    // 0x76cc88: r1 = 6
    //     0x76cc88: movz            x1, #0x6
    // 0x76cc8c: r0 = AllocateContext()
    //     0x76cc8c: bl              #0xec126c  ; AllocateContextStub
    // 0x76cc90: mov             x2, x0
    // 0x76cc94: ldur            x0, [fp, #-8]
    // 0x76cc98: stur            x2, [fp, #-0x18]
    // 0x76cc9c: StoreField: r2->field_f = r0
    //     0x76cc9c: stur            w0, [x2, #0xf]
    // 0x76cca0: r1 = -2
    //     0x76cca0: orr             x1, xzr, #0xfffffffffffffffe
    // 0x76cca4: StoreField: r2->field_13 = r1
    //     0x76cca4: stur            w1, [x2, #0x13]
    // 0x76cca8: r1 = Instance_Size
    //     0x76cca8: add             x1, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!Size@e2c001
    //     0x76ccac: ldr             x1, [x1, #0xa20]
    // 0x76ccb0: ArrayStore: r2[0] = r1  ; List_4
    //     0x76ccb0: stur            w1, [x2, #0x17]
    // 0x76ccb4: r1 = 0.000000
    //     0x76ccb4: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x76ccb8: StoreField: r2->field_1b = r1
    //     0x76ccb8: stur            w1, [x2, #0x1b]
    // 0x76ccbc: LoadField: r3 = r0->field_5f
    //     0x76ccbc: ldur            w3, [x0, #0x5f]
    // 0x76ccc0: DecompressPointer r3
    //     0x76ccc0: add             x3, x3, HEAP, lsl #32
    // 0x76ccc4: stur            x3, [fp, #-0x10]
    // 0x76ccc8: cmp             w3, NULL
    // 0x76cccc: b.eq            #0x76cfe8
    // 0x76ccd0: StoreField: r2->field_1f = r3
    //     0x76ccd0: stur            w3, [x2, #0x1f]
    // 0x76ccd4: LoadField: r1 = r0->field_73
    //     0x76ccd4: ldur            w1, [x0, #0x73]
    // 0x76ccd8: DecompressPointer r1
    //     0x76ccd8: add             x1, x1, HEAP, lsl #32
    // 0x76ccdc: tbnz            w1, #4, #0x76ccfc
    // 0x76cce0: LoadField: r1 = r0->field_6f
    //     0x76cce0: ldur            w1, [x0, #0x6f]
    // 0x76cce4: DecompressPointer r1
    //     0x76cce4: add             x1, x1, HEAP, lsl #32
    // 0x76cce8: tbz             w1, #4, #0x76ccfc
    // 0x76ccec: mov             x1, x3
    // 0x76ccf0: r0 = size()
    //     0x76ccf0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76ccf4: LoadField: d0 = r0->field_f
    //     0x76ccf4: ldur            d0, [x0, #0xf]
    // 0x76ccf8: b               #0x76cd00
    // 0x76ccfc: d0 = 0.000000
    //     0x76ccfc: eor             v0.16b, v0.16b, v0.16b
    // 0x76cd00: ldur            x3, [fp, #-8]
    // 0x76cd04: ldur            x4, [fp, #-0x18]
    // 0x76cd08: ldur            x5, [fp, #-0x10]
    // 0x76cd0c: r0 = inline_Allocate_Double()
    //     0x76cd0c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x76cd10: add             x0, x0, #0x10
    //     0x76cd14: cmp             x1, x0
    //     0x76cd18: b.ls            #0x76cfec
    //     0x76cd1c: str             x0, [THR, #0x50]  ; THR::top
    //     0x76cd20: sub             x0, x0, #0xf
    //     0x76cd24: movz            x1, #0xe15c
    //     0x76cd28: movk            x1, #0x3, lsl #16
    //     0x76cd2c: stur            x1, [x0, #-1]
    // 0x76cd30: StoreField: r0->field_7 = d0
    //     0x76cd30: stur            d0, [x0, #7]
    // 0x76cd34: StoreField: r4->field_23 = r0
    //     0x76cd34: stur            w0, [x4, #0x23]
    //     0x76cd38: ldurb           w16, [x4, #-1]
    //     0x76cd3c: ldurb           w17, [x0, #-1]
    //     0x76cd40: and             x16, x17, x16, lsr #2
    //     0x76cd44: tst             x16, HEAP, lsr #32
    //     0x76cd48: b.eq            #0x76cd50
    //     0x76cd4c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x76cd50: mov             x2, x4
    // 0x76cd54: r1 = Function '<anonymous closure>':.
    //     0x76cd54: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5b528] AnonymousClosure: (0x76d014), in [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::_placeChildren (0x76cc6c)
    //     0x76cd58: ldr             x1, [x1, #0x528]
    // 0x76cd5c: r0 = AllocateClosure()
    //     0x76cd5c: bl              #0xec1630  ; AllocateClosureStub
    // 0x76cd60: ldur            x1, [fp, #-8]
    // 0x76cd64: mov             x2, x0
    // 0x76cd68: r0 = visitChildren()
    //     0x76cd68: bl              #0x786888  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::visitChildren
    // 0x76cd6c: ldur            x3, [fp, #-0x10]
    // 0x76cd70: LoadField: r4 = r3->field_7
    //     0x76cd70: ldur            w4, [x3, #7]
    // 0x76cd74: DecompressPointer r4
    //     0x76cd74: add             x4, x4, HEAP, lsl #32
    // 0x76cd78: stur            x4, [fp, #-0x20]
    // 0x76cd7c: cmp             w4, NULL
    // 0x76cd80: b.eq            #0x76d00c
    // 0x76cd84: mov             x0, x4
    // 0x76cd88: r2 = Null
    //     0x76cd88: mov             x2, NULL
    // 0x76cd8c: r1 = Null
    //     0x76cd8c: mov             x1, NULL
    // 0x76cd90: r4 = LoadClassIdInstr(r0)
    //     0x76cd90: ldur            x4, [x0, #-1]
    //     0x76cd94: ubfx            x4, x4, #0xc, #0x14
    // 0x76cd98: cmp             x4, #0xc77
    // 0x76cd9c: b.eq            #0x76cdb4
    // 0x76cda0: r8 = ToolbarItemsParentData
    //     0x76cda0: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x76cda4: ldr             x8, [x8, #0x490]
    // 0x76cda8: r3 = Null
    //     0x76cda8: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b530] Null
    //     0x76cdac: ldr             x3, [x3, #0x530]
    // 0x76cdb0: r0 = DefaultTypeTest()
    //     0x76cdb0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x76cdb4: ldur            x0, [fp, #-8]
    // 0x76cdb8: LoadField: r2 = r0->field_5f
    //     0x76cdb8: ldur            w2, [x0, #0x5f]
    // 0x76cdbc: DecompressPointer r2
    //     0x76cdbc: add             x2, x2, HEAP, lsl #32
    // 0x76cdc0: cmp             w2, NULL
    // 0x76cdc4: b.eq            #0x76d010
    // 0x76cdc8: mov             x1, x0
    // 0x76cdcc: r3 = 0
    //     0x76cdcc: movz            x3, #0
    // 0x76cdd0: r0 = _shouldPaintChild()
    //     0x76cdd0: bl              #0x76cc04  ; [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::_shouldPaintChild
    // 0x76cdd4: tbnz            w0, #4, #0x76cf98
    // 0x76cdd8: ldur            x0, [fp, #-8]
    // 0x76cddc: ldur            x1, [fp, #-0x20]
    // 0x76cde0: r2 = true
    //     0x76cde0: add             x2, NULL, #0x20  ; true
    // 0x76cde4: ArrayStore: r1[0] = r2  ; List_4
    //     0x76cde4: stur            w2, [x1, #0x17]
    // 0x76cde8: LoadField: r2 = r0->field_73
    //     0x76cde8: ldur            w2, [x0, #0x73]
    // 0x76cdec: DecompressPointer r2
    //     0x76cdec: add             x2, x2, HEAP, lsl #32
    // 0x76cdf0: tbnz            w2, #4, #0x76cedc
    // 0x76cdf4: LoadField: r2 = r0->field_6f
    //     0x76cdf4: ldur            w2, [x0, #0x6f]
    // 0x76cdf8: DecompressPointer r2
    //     0x76cdf8: add             x2, x2, HEAP, lsl #32
    // 0x76cdfc: stur            x2, [fp, #-0x30]
    // 0x76ce00: tbnz            w2, #4, #0x76ce2c
    // 0x76ce04: ldur            x3, [fp, #-0x18]
    // 0x76ce08: LoadField: r4 = r3->field_23
    //     0x76ce08: ldur            w4, [x3, #0x23]
    // 0x76ce0c: DecompressPointer r4
    //     0x76ce0c: add             x4, x4, HEAP, lsl #32
    // 0x76ce10: stur            x4, [fp, #-0x28]
    // 0x76ce14: r0 = Offset()
    //     0x76ce14: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x76ce18: StoreField: r0->field_7 = rZR
    //     0x76ce18: stur            xzr, [x0, #7]
    // 0x76ce1c: ldur            x1, [fp, #-0x28]
    // 0x76ce20: LoadField: d0 = r1->field_7
    //     0x76ce20: ldur            d0, [x1, #7]
    // 0x76ce24: StoreField: r0->field_f = d0
    //     0x76ce24: stur            d0, [x0, #0xf]
    // 0x76ce28: b               #0x76ce30
    // 0x76ce2c: r0 = Instance_Offset
    //     0x76ce2c: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x76ce30: ldur            x3, [fp, #-0x18]
    // 0x76ce34: ldur            x1, [fp, #-0x20]
    // 0x76ce38: ldur            x2, [fp, #-0x30]
    // 0x76ce3c: StoreField: r1->field_7 = r0
    //     0x76ce3c: stur            w0, [x1, #7]
    //     0x76ce40: ldurb           w16, [x1, #-1]
    //     0x76ce44: ldurb           w17, [x0, #-1]
    //     0x76ce48: and             x16, x17, x16, lsr #2
    //     0x76ce4c: tst             x16, HEAP, lsr #32
    //     0x76ce50: b.eq            #0x76ce58
    //     0x76ce54: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76ce58: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x76ce58: ldur            w0, [x3, #0x17]
    // 0x76ce5c: DecompressPointer r0
    //     0x76ce5c: add             x0, x0, HEAP, lsl #32
    // 0x76ce60: LoadField: d0 = r0->field_7
    //     0x76ce60: ldur            d0, [x0, #7]
    // 0x76ce64: stur            d0, [fp, #-0x40]
    // 0x76ce68: tbnz            w2, #4, #0x76ce90
    // 0x76ce6c: LoadField: d1 = r0->field_f
    //     0x76ce6c: ldur            d1, [x0, #0xf]
    // 0x76ce70: ldur            x1, [fp, #-0x10]
    // 0x76ce74: stur            d1, [fp, #-0x38]
    // 0x76ce78: r0 = size()
    //     0x76ce78: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76ce7c: LoadField: d0 = r0->field_f
    //     0x76ce7c: ldur            d0, [x0, #0xf]
    // 0x76ce80: ldur            d1, [fp, #-0x38]
    // 0x76ce84: fadd            d2, d1, d0
    // 0x76ce88: mov             v1.16b, v2.16b
    // 0x76ce8c: b               #0x76ce98
    // 0x76ce90: LoadField: d0 = r0->field_f
    //     0x76ce90: ldur            d0, [x0, #0xf]
    // 0x76ce94: mov             v1.16b, v0.16b
    // 0x76ce98: ldur            x0, [fp, #-0x18]
    // 0x76ce9c: ldur            d0, [fp, #-0x40]
    // 0x76cea0: stur            d1, [fp, #-0x38]
    // 0x76cea4: r0 = Size()
    //     0x76cea4: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x76cea8: ldur            d0, [fp, #-0x40]
    // 0x76ceac: StoreField: r0->field_7 = d0
    //     0x76ceac: stur            d0, [x0, #7]
    // 0x76ceb0: ldur            d0, [fp, #-0x38]
    // 0x76ceb4: StoreField: r0->field_f = d0
    //     0x76ceb4: stur            d0, [x0, #0xf]
    // 0x76ceb8: ldur            x2, [fp, #-0x18]
    // 0x76cebc: ArrayStore: r2[0] = r0  ; List_4
    //     0x76cebc: stur            w0, [x2, #0x17]
    //     0x76cec0: ldurb           w16, [x2, #-1]
    //     0x76cec4: ldurb           w17, [x0, #-1]
    //     0x76cec8: and             x16, x17, x16, lsr #2
    //     0x76cecc: tst             x16, HEAP, lsr #32
    //     0x76ced0: b.eq            #0x76ced8
    //     0x76ced4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x76ced8: b               #0x76cfa8
    // 0x76cedc: ldur            x2, [fp, #-0x18]
    // 0x76cee0: LoadField: r0 = r2->field_1b
    //     0x76cee0: ldur            w0, [x2, #0x1b]
    // 0x76cee4: DecompressPointer r0
    //     0x76cee4: add             x0, x0, HEAP, lsl #32
    // 0x76cee8: LoadField: d0 = r0->field_7
    //     0x76cee8: ldur            d0, [x0, #7]
    // 0x76ceec: stur            d0, [fp, #-0x38]
    // 0x76cef0: r0 = Offset()
    //     0x76cef0: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x76cef4: ldur            d0, [fp, #-0x38]
    // 0x76cef8: StoreField: r0->field_7 = d0
    //     0x76cef8: stur            d0, [x0, #7]
    // 0x76cefc: StoreField: r0->field_f = rZR
    //     0x76cefc: stur            xzr, [x0, #0xf]
    // 0x76cf00: ldur            x1, [fp, #-0x20]
    // 0x76cf04: StoreField: r1->field_7 = r0
    //     0x76cf04: stur            w0, [x1, #7]
    //     0x76cf08: ldurb           w16, [x1, #-1]
    //     0x76cf0c: ldurb           w17, [x0, #-1]
    //     0x76cf10: and             x16, x17, x16, lsr #2
    //     0x76cf14: tst             x16, HEAP, lsr #32
    //     0x76cf18: b.eq            #0x76cf20
    //     0x76cf1c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76cf20: ldur            x0, [fp, #-0x18]
    // 0x76cf24: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x76cf24: ldur            w1, [x0, #0x17]
    // 0x76cf28: DecompressPointer r1
    //     0x76cf28: add             x1, x1, HEAP, lsl #32
    // 0x76cf2c: LoadField: d0 = r1->field_7
    //     0x76cf2c: ldur            d0, [x1, #7]
    // 0x76cf30: ldur            x1, [fp, #-0x10]
    // 0x76cf34: stur            d0, [fp, #-0x38]
    // 0x76cf38: r0 = size()
    //     0x76cf38: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76cf3c: LoadField: d0 = r0->field_7
    //     0x76cf3c: ldur            d0, [x0, #7]
    // 0x76cf40: ldur            d1, [fp, #-0x38]
    // 0x76cf44: fadd            d2, d1, d0
    // 0x76cf48: ldur            x0, [fp, #-0x18]
    // 0x76cf4c: stur            d2, [fp, #-0x40]
    // 0x76cf50: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x76cf50: ldur            w1, [x0, #0x17]
    // 0x76cf54: DecompressPointer r1
    //     0x76cf54: add             x1, x1, HEAP, lsl #32
    // 0x76cf58: LoadField: d0 = r1->field_f
    //     0x76cf58: ldur            d0, [x1, #0xf]
    // 0x76cf5c: stur            d0, [fp, #-0x38]
    // 0x76cf60: r0 = Size()
    //     0x76cf60: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x76cf64: ldur            d0, [fp, #-0x40]
    // 0x76cf68: StoreField: r0->field_7 = d0
    //     0x76cf68: stur            d0, [x0, #7]
    // 0x76cf6c: ldur            d0, [fp, #-0x38]
    // 0x76cf70: StoreField: r0->field_f = d0
    //     0x76cf70: stur            d0, [x0, #0xf]
    // 0x76cf74: ldur            x2, [fp, #-0x18]
    // 0x76cf78: ArrayStore: r2[0] = r0  ; List_4
    //     0x76cf78: stur            w0, [x2, #0x17]
    //     0x76cf7c: ldurb           w16, [x2, #-1]
    //     0x76cf80: ldurb           w17, [x0, #-1]
    //     0x76cf84: and             x16, x17, x16, lsr #2
    //     0x76cf88: tst             x16, HEAP, lsr #32
    //     0x76cf8c: b.eq            #0x76cf94
    //     0x76cf90: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x76cf94: b               #0x76cfa8
    // 0x76cf98: ldur            x2, [fp, #-0x18]
    // 0x76cf9c: ldur            x1, [fp, #-0x20]
    // 0x76cfa0: r3 = false
    //     0x76cfa0: add             x3, NULL, #0x30  ; false
    // 0x76cfa4: ArrayStore: r1[0] = r3  ; List_4
    //     0x76cfa4: stur            w3, [x1, #0x17]
    // 0x76cfa8: ldur            x1, [fp, #-8]
    // 0x76cfac: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x76cfac: ldur            w0, [x2, #0x17]
    // 0x76cfb0: DecompressPointer r0
    //     0x76cfb0: add             x0, x0, HEAP, lsl #32
    // 0x76cfb4: StoreField: r1->field_53 = r0
    //     0x76cfb4: stur            w0, [x1, #0x53]
    //     0x76cfb8: ldurb           w16, [x1, #-1]
    //     0x76cfbc: ldurb           w17, [x0, #-1]
    //     0x76cfc0: and             x16, x17, x16, lsr #2
    //     0x76cfc4: tst             x16, HEAP, lsr #32
    //     0x76cfc8: b.eq            #0x76cfd0
    //     0x76cfcc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76cfd0: r0 = Null
    //     0x76cfd0: mov             x0, NULL
    // 0x76cfd4: LeaveFrame
    //     0x76cfd4: mov             SP, fp
    //     0x76cfd8: ldp             fp, lr, [SP], #0x10
    // 0x76cfdc: ret
    //     0x76cfdc: ret             
    // 0x76cfe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76cfe0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76cfe4: b               #0x76cc88
    // 0x76cfe8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76cfe8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76cfec: SaveReg d0
    //     0x76cfec: str             q0, [SP, #-0x10]!
    // 0x76cff0: stp             x4, x5, [SP, #-0x10]!
    // 0x76cff4: SaveReg r3
    //     0x76cff4: str             x3, [SP, #-8]!
    // 0x76cff8: r0 = AllocateDouble()
    //     0x76cff8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x76cffc: RestoreReg r3
    //     0x76cffc: ldr             x3, [SP], #8
    // 0x76d000: ldp             x4, x5, [SP], #0x10
    // 0x76d004: RestoreReg d0
    //     0x76d004: ldr             q0, [SP], #0x10
    // 0x76d008: b               #0x76cd30
    // 0x76d00c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76d00c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76d010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76d010: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x76d014, size: 0x430
    // 0x76d014: EnterFrame
    //     0x76d014: stp             fp, lr, [SP, #-0x10]!
    //     0x76d018: mov             fp, SP
    // 0x76d01c: AllocStack(0x30)
    //     0x76d01c: sub             SP, SP, #0x30
    // 0x76d020: SetupParameters()
    //     0x76d020: ldr             x0, [fp, #0x18]
    //     0x76d024: ldur            w3, [x0, #0x17]
    //     0x76d028: add             x3, x3, HEAP, lsl #32
    //     0x76d02c: stur            x3, [fp, #-0x10]
    // 0x76d030: CheckStackOverflow
    //     0x76d030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76d034: cmp             SP, x16
    //     0x76d038: b.ls            #0x76d418
    // 0x76d03c: LoadField: r0 = r3->field_13
    //     0x76d03c: ldur            w0, [x3, #0x13]
    // 0x76d040: DecompressPointer r0
    //     0x76d040: add             x0, x0, HEAP, lsl #32
    // 0x76d044: r1 = LoadInt32Instr(r0)
    //     0x76d044: sbfx            x1, x0, #1, #0x1f
    //     0x76d048: tbz             w0, #0, #0x76d050
    //     0x76d04c: ldur            x1, [x0, #7]
    // 0x76d050: add             x4, x1, #1
    // 0x76d054: stur            x4, [fp, #-8]
    // 0x76d058: r0 = BoxInt64Instr(r4)
    //     0x76d058: sbfiz           x0, x4, #1, #0x1f
    //     0x76d05c: cmp             x4, x0, asr #1
    //     0x76d060: b.eq            #0x76d06c
    //     0x76d064: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x76d068: stur            x4, [x0, #7]
    // 0x76d06c: StoreField: r3->field_13 = r0
    //     0x76d06c: stur            w0, [x3, #0x13]
    //     0x76d070: tbz             w0, #0, #0x76d08c
    //     0x76d074: ldurb           w16, [x3, #-1]
    //     0x76d078: ldurb           w17, [x0, #-1]
    //     0x76d07c: and             x16, x17, x16, lsr #2
    //     0x76d080: tst             x16, HEAP, lsr #32
    //     0x76d084: b.eq            #0x76d08c
    //     0x76d088: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x76d08c: ldr             x0, [fp, #0x10]
    // 0x76d090: r2 = Null
    //     0x76d090: mov             x2, NULL
    // 0x76d094: r1 = Null
    //     0x76d094: mov             x1, NULL
    // 0x76d098: r4 = LoadClassIdInstr(r0)
    //     0x76d098: ldur            x4, [x0, #-1]
    //     0x76d09c: ubfx            x4, x4, #0xc, #0x14
    // 0x76d0a0: sub             x4, x4, #0xbba
    // 0x76d0a4: cmp             x4, #0x9a
    // 0x76d0a8: b.ls            #0x76d0bc
    // 0x76d0ac: r8 = RenderBox
    //     0x76d0ac: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x76d0b0: r3 = Null
    //     0x76d0b0: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b540] Null
    //     0x76d0b4: ldr             x3, [x3, #0x540]
    // 0x76d0b8: r0 = RenderBox()
    //     0x76d0b8: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x76d0bc: ldr             x3, [fp, #0x10]
    // 0x76d0c0: LoadField: r4 = r3->field_7
    //     0x76d0c0: ldur            w4, [x3, #7]
    // 0x76d0c4: DecompressPointer r4
    //     0x76d0c4: add             x4, x4, HEAP, lsl #32
    // 0x76d0c8: stur            x4, [fp, #-0x18]
    // 0x76d0cc: cmp             w4, NULL
    // 0x76d0d0: b.eq            #0x76d420
    // 0x76d0d4: mov             x0, x4
    // 0x76d0d8: r2 = Null
    //     0x76d0d8: mov             x2, NULL
    // 0x76d0dc: r1 = Null
    //     0x76d0dc: mov             x1, NULL
    // 0x76d0e0: r4 = LoadClassIdInstr(r0)
    //     0x76d0e0: ldur            x4, [x0, #-1]
    //     0x76d0e4: ubfx            x4, x4, #0xc, #0x14
    // 0x76d0e8: cmp             x4, #0xc77
    // 0x76d0ec: b.eq            #0x76d104
    // 0x76d0f0: r8 = ToolbarItemsParentData
    //     0x76d0f0: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x76d0f4: ldr             x8, [x8, #0x490]
    // 0x76d0f8: r3 = Null
    //     0x76d0f8: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b550] Null
    //     0x76d0fc: ldr             x3, [x3, #0x550]
    // 0x76d100: r0 = DefaultTypeTest()
    //     0x76d100: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x76d104: ldur            x0, [fp, #-0x10]
    // 0x76d108: LoadField: r1 = r0->field_1f
    //     0x76d108: ldur            w1, [x0, #0x1f]
    // 0x76d10c: DecompressPointer r1
    //     0x76d10c: add             x1, x1, HEAP, lsl #32
    // 0x76d110: ldr             x4, [fp, #0x10]
    // 0x76d114: cmp             w4, w1
    // 0x76d118: b.ne            #0x76d12c
    // 0x76d11c: r0 = Null
    //     0x76d11c: mov             x0, NULL
    // 0x76d120: LeaveFrame
    //     0x76d120: mov             SP, fp
    //     0x76d124: ldp             fp, lr, [SP], #0x10
    // 0x76d128: ret
    //     0x76d128: ret             
    // 0x76d12c: LoadField: r1 = r0->field_f
    //     0x76d12c: ldur            w1, [x0, #0xf]
    // 0x76d130: DecompressPointer r1
    //     0x76d130: add             x1, x1, HEAP, lsl #32
    // 0x76d134: mov             x2, x4
    // 0x76d138: ldur            x3, [fp, #-8]
    // 0x76d13c: r0 = _shouldPaintChild()
    //     0x76d13c: bl              #0x76cc04  ; [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::_shouldPaintChild
    // 0x76d140: tbz             w0, #4, #0x76d160
    // 0x76d144: ldur            x0, [fp, #-0x18]
    // 0x76d148: r1 = false
    //     0x76d148: add             x1, NULL, #0x30  ; false
    // 0x76d14c: ArrayStore: r0[0] = r1  ; List_4
    //     0x76d14c: stur            w1, [x0, #0x17]
    // 0x76d150: r0 = Null
    //     0x76d150: mov             x0, NULL
    // 0x76d154: LeaveFrame
    //     0x76d154: mov             SP, fp
    //     0x76d158: ldp             fp, lr, [SP], #0x10
    // 0x76d15c: ret
    //     0x76d15c: ret             
    // 0x76d160: ldur            x1, [fp, #-0x10]
    // 0x76d164: ldur            x0, [fp, #-0x18]
    // 0x76d168: r2 = true
    //     0x76d168: add             x2, NULL, #0x20  ; true
    // 0x76d16c: ArrayStore: r0[0] = r2  ; List_4
    //     0x76d16c: stur            w2, [x0, #0x17]
    // 0x76d170: LoadField: r2 = r1->field_f
    //     0x76d170: ldur            w2, [x1, #0xf]
    // 0x76d174: DecompressPointer r2
    //     0x76d174: add             x2, x2, HEAP, lsl #32
    // 0x76d178: LoadField: r3 = r2->field_73
    //     0x76d178: ldur            w3, [x2, #0x73]
    // 0x76d17c: DecompressPointer r3
    //     0x76d17c: add             x3, x3, HEAP, lsl #32
    // 0x76d180: tbz             w3, #4, #0x76d2bc
    // 0x76d184: LoadField: r2 = r1->field_1b
    //     0x76d184: ldur            w2, [x1, #0x1b]
    // 0x76d188: DecompressPointer r2
    //     0x76d188: add             x2, x2, HEAP, lsl #32
    // 0x76d18c: LoadField: d0 = r2->field_7
    //     0x76d18c: ldur            d0, [x2, #7]
    // 0x76d190: stur            d0, [fp, #-0x28]
    // 0x76d194: r0 = Offset()
    //     0x76d194: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x76d198: ldur            d0, [fp, #-0x28]
    // 0x76d19c: StoreField: r0->field_7 = d0
    //     0x76d19c: stur            d0, [x0, #7]
    // 0x76d1a0: StoreField: r0->field_f = rZR
    //     0x76d1a0: stur            xzr, [x0, #0xf]
    // 0x76d1a4: ldur            x1, [fp, #-0x18]
    // 0x76d1a8: StoreField: r1->field_7 = r0
    //     0x76d1a8: stur            w0, [x1, #7]
    //     0x76d1ac: ldurb           w16, [x1, #-1]
    //     0x76d1b0: ldurb           w17, [x0, #-1]
    //     0x76d1b4: and             x16, x17, x16, lsr #2
    //     0x76d1b8: tst             x16, HEAP, lsr #32
    //     0x76d1bc: b.eq            #0x76d1c4
    //     0x76d1c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76d1c4: ldr             x1, [fp, #0x10]
    // 0x76d1c8: r0 = size()
    //     0x76d1c8: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76d1cc: LoadField: d0 = r0->field_7
    //     0x76d1cc: ldur            d0, [x0, #7]
    // 0x76d1d0: ldur            d1, [fp, #-0x28]
    // 0x76d1d4: fadd            d2, d1, d0
    // 0x76d1d8: stur            d2, [fp, #-0x30]
    // 0x76d1dc: r0 = inline_Allocate_Double()
    //     0x76d1dc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x76d1e0: add             x0, x0, #0x10
    //     0x76d1e4: cmp             x1, x0
    //     0x76d1e8: b.ls            #0x76d424
    //     0x76d1ec: str             x0, [THR, #0x50]  ; THR::top
    //     0x76d1f0: sub             x0, x0, #0xf
    //     0x76d1f4: movz            x1, #0xe15c
    //     0x76d1f8: movk            x1, #0x3, lsl #16
    //     0x76d1fc: stur            x1, [x0, #-1]
    // 0x76d200: StoreField: r0->field_7 = d2
    //     0x76d200: stur            d2, [x0, #7]
    // 0x76d204: ldur            x2, [fp, #-0x10]
    // 0x76d208: StoreField: r2->field_1b = r0
    //     0x76d208: stur            w0, [x2, #0x1b]
    //     0x76d20c: ldurb           w16, [x2, #-1]
    //     0x76d210: ldurb           w17, [x0, #-1]
    //     0x76d214: and             x16, x17, x16, lsr #2
    //     0x76d218: tst             x16, HEAP, lsr #32
    //     0x76d21c: b.eq            #0x76d224
    //     0x76d220: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x76d224: ldr             x1, [fp, #0x10]
    // 0x76d228: r0 = size()
    //     0x76d228: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76d22c: LoadField: d0 = r0->field_f
    //     0x76d22c: ldur            d0, [x0, #0xf]
    // 0x76d230: ldur            x0, [fp, #-0x10]
    // 0x76d234: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x76d234: ldur            w1, [x0, #0x17]
    // 0x76d238: DecompressPointer r1
    //     0x76d238: add             x1, x1, HEAP, lsl #32
    // 0x76d23c: LoadField: d1 = r1->field_f
    //     0x76d23c: ldur            d1, [x1, #0xf]
    // 0x76d240: fcmp            d0, d1
    // 0x76d244: b.le            #0x76d250
    // 0x76d248: mov             v1.16b, v0.16b
    // 0x76d24c: b               #0x76d27c
    // 0x76d250: fcmp            d1, d0
    // 0x76d254: b.gt            #0x76d27c
    // 0x76d258: d2 = 0.000000
    //     0x76d258: eor             v2.16b, v2.16b, v2.16b
    // 0x76d25c: fcmp            d0, d2
    // 0x76d260: b.ne            #0x76d270
    // 0x76d264: fadd            d2, d0, d1
    // 0x76d268: mov             v1.16b, v2.16b
    // 0x76d26c: b               #0x76d27c
    // 0x76d270: fcmp            d1, d1
    // 0x76d274: b.vs            #0x76d27c
    // 0x76d278: mov             v1.16b, v0.16b
    // 0x76d27c: ldur            d0, [fp, #-0x30]
    // 0x76d280: stur            d1, [fp, #-0x28]
    // 0x76d284: r0 = Size()
    //     0x76d284: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x76d288: ldur            d0, [fp, #-0x30]
    // 0x76d28c: StoreField: r0->field_7 = d0
    //     0x76d28c: stur            d0, [x0, #7]
    // 0x76d290: ldur            d0, [fp, #-0x28]
    // 0x76d294: StoreField: r0->field_f = d0
    //     0x76d294: stur            d0, [x0, #0xf]
    // 0x76d298: ldur            x2, [fp, #-0x10]
    // 0x76d29c: ArrayStore: r2[0] = r0  ; List_4
    //     0x76d29c: stur            w0, [x2, #0x17]
    //     0x76d2a0: ldurb           w16, [x2, #-1]
    //     0x76d2a4: ldurb           w17, [x0, #-1]
    //     0x76d2a8: and             x16, x17, x16, lsr #2
    //     0x76d2ac: tst             x16, HEAP, lsr #32
    //     0x76d2b0: b.eq            #0x76d2b8
    //     0x76d2b4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x76d2b8: b               #0x76d408
    // 0x76d2bc: mov             x2, x1
    // 0x76d2c0: mov             x1, x0
    // 0x76d2c4: d2 = 0.000000
    //     0x76d2c4: eor             v2.16b, v2.16b, v2.16b
    // 0x76d2c8: LoadField: r0 = r2->field_23
    //     0x76d2c8: ldur            w0, [x2, #0x23]
    // 0x76d2cc: DecompressPointer r0
    //     0x76d2cc: add             x0, x0, HEAP, lsl #32
    // 0x76d2d0: stur            x0, [fp, #-0x20]
    // 0x76d2d4: r0 = Offset()
    //     0x76d2d4: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x76d2d8: StoreField: r0->field_7 = rZR
    //     0x76d2d8: stur            xzr, [x0, #7]
    // 0x76d2dc: ldur            x1, [fp, #-0x20]
    // 0x76d2e0: LoadField: d0 = r1->field_7
    //     0x76d2e0: ldur            d0, [x1, #7]
    // 0x76d2e4: stur            d0, [fp, #-0x28]
    // 0x76d2e8: StoreField: r0->field_f = d0
    //     0x76d2e8: stur            d0, [x0, #0xf]
    // 0x76d2ec: ldur            x1, [fp, #-0x18]
    // 0x76d2f0: StoreField: r1->field_7 = r0
    //     0x76d2f0: stur            w0, [x1, #7]
    //     0x76d2f4: ldurb           w16, [x1, #-1]
    //     0x76d2f8: ldurb           w17, [x0, #-1]
    //     0x76d2fc: and             x16, x17, x16, lsr #2
    //     0x76d300: tst             x16, HEAP, lsr #32
    //     0x76d304: b.eq            #0x76d30c
    //     0x76d308: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76d30c: ldr             x1, [fp, #0x10]
    // 0x76d310: r0 = size()
    //     0x76d310: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76d314: LoadField: d0 = r0->field_f
    //     0x76d314: ldur            d0, [x0, #0xf]
    // 0x76d318: ldur            d1, [fp, #-0x28]
    // 0x76d31c: fadd            d2, d1, d0
    // 0x76d320: r0 = inline_Allocate_Double()
    //     0x76d320: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x76d324: add             x0, x0, #0x10
    //     0x76d328: cmp             x1, x0
    //     0x76d32c: b.ls            #0x76d434
    //     0x76d330: str             x0, [THR, #0x50]  ; THR::top
    //     0x76d334: sub             x0, x0, #0xf
    //     0x76d338: movz            x1, #0xe15c
    //     0x76d33c: movk            x1, #0x3, lsl #16
    //     0x76d340: stur            x1, [x0, #-1]
    // 0x76d344: StoreField: r0->field_7 = d2
    //     0x76d344: stur            d2, [x0, #7]
    // 0x76d348: ldur            x2, [fp, #-0x10]
    // 0x76d34c: StoreField: r2->field_23 = r0
    //     0x76d34c: stur            w0, [x2, #0x23]
    //     0x76d350: ldurb           w16, [x2, #-1]
    //     0x76d354: ldurb           w17, [x0, #-1]
    //     0x76d358: and             x16, x17, x16, lsr #2
    //     0x76d35c: tst             x16, HEAP, lsr #32
    //     0x76d360: b.eq            #0x76d368
    //     0x76d364: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x76d368: ldr             x1, [fp, #0x10]
    // 0x76d36c: r0 = size()
    //     0x76d36c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76d370: LoadField: d0 = r0->field_7
    //     0x76d370: ldur            d0, [x0, #7]
    // 0x76d374: ldur            x0, [fp, #-0x10]
    // 0x76d378: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x76d378: ldur            w1, [x0, #0x17]
    // 0x76d37c: DecompressPointer r1
    //     0x76d37c: add             x1, x1, HEAP, lsl #32
    // 0x76d380: LoadField: d1 = r1->field_7
    //     0x76d380: ldur            d1, [x1, #7]
    // 0x76d384: fcmp            d0, d1
    // 0x76d388: b.gt            #0x76d3c0
    // 0x76d38c: fcmp            d1, d0
    // 0x76d390: b.le            #0x76d39c
    // 0x76d394: mov             v0.16b, v1.16b
    // 0x76d398: b               #0x76d3c0
    // 0x76d39c: d2 = 0.000000
    //     0x76d39c: eor             v2.16b, v2.16b, v2.16b
    // 0x76d3a0: fcmp            d0, d2
    // 0x76d3a4: b.ne            #0x76d3b4
    // 0x76d3a8: fadd            d2, d0, d1
    // 0x76d3ac: mov             v0.16b, v2.16b
    // 0x76d3b0: b               #0x76d3c0
    // 0x76d3b4: fcmp            d1, d1
    // 0x76d3b8: b.vc            #0x76d3c0
    // 0x76d3bc: mov             v0.16b, v1.16b
    // 0x76d3c0: stur            d0, [fp, #-0x28]
    // 0x76d3c4: LoadField: r1 = r0->field_23
    //     0x76d3c4: ldur            w1, [x0, #0x23]
    // 0x76d3c8: DecompressPointer r1
    //     0x76d3c8: add             x1, x1, HEAP, lsl #32
    // 0x76d3cc: stur            x1, [fp, #-0x18]
    // 0x76d3d0: r0 = Size()
    //     0x76d3d0: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x76d3d4: ldur            d0, [fp, #-0x28]
    // 0x76d3d8: StoreField: r0->field_7 = d0
    //     0x76d3d8: stur            d0, [x0, #7]
    // 0x76d3dc: ldur            x1, [fp, #-0x18]
    // 0x76d3e0: LoadField: d0 = r1->field_7
    //     0x76d3e0: ldur            d0, [x1, #7]
    // 0x76d3e4: StoreField: r0->field_f = d0
    //     0x76d3e4: stur            d0, [x0, #0xf]
    // 0x76d3e8: ldur            x1, [fp, #-0x10]
    // 0x76d3ec: ArrayStore: r1[0] = r0  ; List_4
    //     0x76d3ec: stur            w0, [x1, #0x17]
    //     0x76d3f0: ldurb           w16, [x1, #-1]
    //     0x76d3f4: ldurb           w17, [x0, #-1]
    //     0x76d3f8: and             x16, x17, x16, lsr #2
    //     0x76d3fc: tst             x16, HEAP, lsr #32
    //     0x76d400: b.eq            #0x76d408
    //     0x76d404: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76d408: r0 = Null
    //     0x76d408: mov             x0, NULL
    // 0x76d40c: LeaveFrame
    //     0x76d40c: mov             SP, fp
    //     0x76d410: ldp             fp, lr, [SP], #0x10
    // 0x76d414: ret
    //     0x76d414: ret             
    // 0x76d418: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76d418: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76d41c: b               #0x76d03c
    // 0x76d420: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76d420: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76d424: SaveReg d2
    //     0x76d424: str             q2, [SP, #-0x10]!
    // 0x76d428: r0 = AllocateDouble()
    //     0x76d428: bl              #0xec2254  ; AllocateDoubleStub
    // 0x76d42c: RestoreReg d2
    //     0x76d42c: ldr             q2, [SP], #0x10
    // 0x76d430: b               #0x76d200
    // 0x76d434: SaveReg d2
    //     0x76d434: str             q2, [SP, #-0x10]!
    // 0x76d438: r0 = AllocateDouble()
    //     0x76d438: bl              #0xec2254  ; AllocateDoubleStub
    // 0x76d43c: RestoreReg d2
    //     0x76d43c: ldr             q2, [SP], #0x10
    // 0x76d440: b               #0x76d344
  }
  _ _layoutChildren(/* No info */) {
    // ** addr: 0x76d444, size: 0x208
    // 0x76d444: EnterFrame
    //     0x76d444: stp             fp, lr, [SP, #-0x10]!
    //     0x76d448: mov             fp, SP
    // 0x76d44c: AllocStack(0x28)
    //     0x76d44c: sub             SP, SP, #0x28
    // 0x76d450: SetupParameters(_RenderTextSelectionToolbarItemsLayout this /* r1 => r1, fp-0x8 */)
    //     0x76d450: stur            x1, [fp, #-8]
    // 0x76d454: CheckStackOverflow
    //     0x76d454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76d458: cmp             SP, x16
    //     0x76d45c: b.ls            #0x76d640
    // 0x76d460: r1 = 4
    //     0x76d460: movz            x1, #0x4
    // 0x76d464: r0 = AllocateContext()
    //     0x76d464: bl              #0xec126c  ; AllocateContextStub
    // 0x76d468: mov             x4, x0
    // 0x76d46c: ldur            x3, [fp, #-8]
    // 0x76d470: stur            x4, [fp, #-0x18]
    // 0x76d474: StoreField: r4->field_f = r3
    //     0x76d474: stur            w3, [x4, #0xf]
    // 0x76d478: LoadField: r0 = r3->field_73
    //     0x76d478: ldur            w0, [x3, #0x73]
    // 0x76d47c: DecompressPointer r0
    //     0x76d47c: add             x0, x0, HEAP, lsl #32
    // 0x76d480: tbnz            w0, #4, #0x76d4d0
    // 0x76d484: LoadField: r5 = r3->field_27
    //     0x76d484: ldur            w5, [x3, #0x27]
    // 0x76d488: DecompressPointer r5
    //     0x76d488: add             x5, x5, HEAP, lsl #32
    // 0x76d48c: stur            x5, [fp, #-0x10]
    // 0x76d490: cmp             w5, NULL
    // 0x76d494: b.eq            #0x76d604
    // 0x76d498: mov             x0, x5
    // 0x76d49c: r2 = Null
    //     0x76d49c: mov             x2, NULL
    // 0x76d4a0: r1 = Null
    //     0x76d4a0: mov             x1, NULL
    // 0x76d4a4: r4 = LoadClassIdInstr(r0)
    //     0x76d4a4: ldur            x4, [x0, #-1]
    //     0x76d4a8: ubfx            x4, x4, #0xc, #0x14
    // 0x76d4ac: sub             x4, x4, #0xc83
    // 0x76d4b0: cmp             x4, #1
    // 0x76d4b4: b.ls            #0x76d4c8
    // 0x76d4b8: r8 = BoxConstraints
    //     0x76d4b8: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76d4bc: r3 = Null
    //     0x76d4bc: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b560] Null
    //     0x76d4c0: ldr             x3, [x3, #0x560]
    // 0x76d4c4: r0 = BoxConstraints()
    //     0x76d4c4: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76d4c8: ldur            x4, [fp, #-0x10]
    // 0x76d4cc: b               #0x76d544
    // 0x76d4d0: LoadField: r4 = r3->field_27
    //     0x76d4d0: ldur            w4, [x3, #0x27]
    // 0x76d4d4: DecompressPointer r4
    //     0x76d4d4: add             x4, x4, HEAP, lsl #32
    // 0x76d4d8: stur            x4, [fp, #-0x10]
    // 0x76d4dc: cmp             w4, NULL
    // 0x76d4e0: b.eq            #0x76d620
    // 0x76d4e4: mov             x0, x4
    // 0x76d4e8: r2 = Null
    //     0x76d4e8: mov             x2, NULL
    // 0x76d4ec: r1 = Null
    //     0x76d4ec: mov             x1, NULL
    // 0x76d4f0: r4 = LoadClassIdInstr(r0)
    //     0x76d4f0: ldur            x4, [x0, #-1]
    //     0x76d4f4: ubfx            x4, x4, #0xc, #0x14
    // 0x76d4f8: sub             x4, x4, #0xc83
    // 0x76d4fc: cmp             x4, #1
    // 0x76d500: b.ls            #0x76d514
    // 0x76d504: r8 = BoxConstraints
    //     0x76d504: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76d508: r3 = Null
    //     0x76d508: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b570] Null
    //     0x76d50c: ldr             x3, [x3, #0x570]
    // 0x76d510: r0 = BoxConstraints()
    //     0x76d510: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76d514: ldur            x0, [fp, #-0x10]
    // 0x76d518: LoadField: d0 = r0->field_f
    //     0x76d518: ldur            d0, [x0, #0xf]
    // 0x76d51c: stur            d0, [fp, #-0x28]
    // 0x76d520: r0 = BoxConstraints()
    //     0x76d520: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x76d524: StoreField: r0->field_7 = rZR
    //     0x76d524: stur            xzr, [x0, #7]
    // 0x76d528: ldur            d0, [fp, #-0x28]
    // 0x76d52c: StoreField: r0->field_f = d0
    //     0x76d52c: stur            d0, [x0, #0xf]
    // 0x76d530: ArrayStore: r0[0] = rZR  ; List_8
    //     0x76d530: stur            xzr, [x0, #0x17]
    // 0x76d534: d0 = 44.000000
    //     0x76d534: add             x17, PP, #0x43, lsl #12  ; [pp+0x438d0] IMM: double(44) from 0x4046000000000000
    //     0x76d538: ldr             d0, [x17, #0x8d0]
    // 0x76d53c: StoreField: r0->field_1f = d0
    //     0x76d53c: stur            d0, [x0, #0x1f]
    // 0x76d540: mov             x4, x0
    // 0x76d544: ldur            x0, [fp, #-8]
    // 0x76d548: ldur            x3, [fp, #-0x18]
    // 0x76d54c: r2 = 0.000000
    //     0x76d54c: ldr             x2, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x76d550: r1 = -2
    //     0x76d550: orr             x1, xzr, #0xfffffffffffffffe
    // 0x76d554: stur            x4, [fp, #-0x10]
    // 0x76d558: StoreField: r3->field_13 = r4
    //     0x76d558: stur            w4, [x3, #0x13]
    // 0x76d55c: ArrayStore: r3[0] = r1  ; List_4
    //     0x76d55c: stur            w1, [x3, #0x17]
    // 0x76d560: StoreField: r3->field_1b = r2
    //     0x76d560: stur            w2, [x3, #0x1b]
    // 0x76d564: mov             x2, x3
    // 0x76d568: r1 = Function '<anonymous closure>':.
    //     0x76d568: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5b580] AnonymousClosure: (0x76d64c), in [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::_layoutChildren (0x76d444)
    //     0x76d56c: ldr             x1, [x1, #0x580]
    // 0x76d570: r0 = AllocateClosure()
    //     0x76d570: bl              #0xec1630  ; AllocateClosureStub
    // 0x76d574: ldur            x1, [fp, #-8]
    // 0x76d578: mov             x2, x0
    // 0x76d57c: r0 = visitChildren()
    //     0x76d57c: bl              #0x786888  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::visitChildren
    // 0x76d580: ldur            x0, [fp, #-8]
    // 0x76d584: LoadField: r1 = r0->field_5f
    //     0x76d584: ldur            w1, [x0, #0x5f]
    // 0x76d588: DecompressPointer r1
    //     0x76d588: add             x1, x1, HEAP, lsl #32
    // 0x76d58c: cmp             w1, NULL
    // 0x76d590: b.eq            #0x76d648
    // 0x76d594: LoadField: r2 = r0->field_67
    //     0x76d594: ldur            x2, [x0, #0x67]
    // 0x76d598: cmn             x2, #1
    // 0x76d59c: b.eq            #0x76d5f4
    // 0x76d5a0: LoadField: r3 = r0->field_57
    //     0x76d5a0: ldur            x3, [x0, #0x57]
    // 0x76d5a4: sub             x4, x3, #2
    // 0x76d5a8: cmp             x2, x4
    // 0x76d5ac: b.ne            #0x76d5f4
    // 0x76d5b0: ldur            x2, [fp, #-0x18]
    // 0x76d5b4: ldur            x3, [fp, #-0x10]
    // 0x76d5b8: LoadField: r4 = r2->field_1b
    //     0x76d5b8: ldur            w4, [x2, #0x1b]
    // 0x76d5bc: DecompressPointer r4
    //     0x76d5bc: add             x4, x4, HEAP, lsl #32
    // 0x76d5c0: stur            x4, [fp, #-0x20]
    // 0x76d5c4: r0 = size()
    //     0x76d5c4: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76d5c8: LoadField: d0 = r0->field_7
    //     0x76d5c8: ldur            d0, [x0, #7]
    // 0x76d5cc: ldur            x0, [fp, #-0x20]
    // 0x76d5d0: LoadField: d1 = r0->field_7
    //     0x76d5d0: ldur            d1, [x0, #7]
    // 0x76d5d4: fsub            d2, d1, d0
    // 0x76d5d8: ldur            x0, [fp, #-0x10]
    // 0x76d5dc: LoadField: d0 = r0->field_f
    //     0x76d5dc: ldur            d0, [x0, #0xf]
    // 0x76d5e0: fcmp            d0, d2
    // 0x76d5e4: b.lt            #0x76d5f4
    // 0x76d5e8: ldur            x0, [fp, #-8]
    // 0x76d5ec: r1 = -1
    //     0x76d5ec: movn            x1, #0
    // 0x76d5f0: StoreField: r0->field_67 = r1
    //     0x76d5f0: stur            x1, [x0, #0x67]
    // 0x76d5f4: r0 = Null
    //     0x76d5f4: mov             x0, NULL
    // 0x76d5f8: LeaveFrame
    //     0x76d5f8: mov             SP, fp
    //     0x76d5fc: ldp             fp, lr, [SP], #0x10
    // 0x76d600: ret
    //     0x76d600: ret             
    // 0x76d604: r0 = StateError()
    //     0x76d604: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76d608: mov             x1, x0
    // 0x76d60c: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76d60c: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76d610: StoreField: r1->field_b = r0
    //     0x76d610: stur            w0, [x1, #0xb]
    // 0x76d614: mov             x0, x1
    // 0x76d618: r0 = Throw()
    //     0x76d618: bl              #0xec04b8  ; ThrowStub
    // 0x76d61c: brk             #0
    // 0x76d620: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76d620: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76d624: r0 = StateError()
    //     0x76d624: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76d628: mov             x1, x0
    // 0x76d62c: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76d62c: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76d630: StoreField: r1->field_b = r0
    //     0x76d630: stur            w0, [x1, #0xb]
    // 0x76d634: mov             x0, x1
    // 0x76d638: r0 = Throw()
    //     0x76d638: bl              #0xec04b8  ; ThrowStub
    // 0x76d63c: brk             #0
    // 0x76d640: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76d640: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76d644: b               #0x76d460
    // 0x76d648: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76d648: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x76d64c, size: 0x200
    // 0x76d64c: EnterFrame
    //     0x76d64c: stp             fp, lr, [SP, #-0x10]!
    //     0x76d650: mov             fp, SP
    // 0x76d654: AllocStack(0x20)
    //     0x76d654: sub             SP, SP, #0x20
    // 0x76d658: SetupParameters()
    //     0x76d658: ldr             x0, [fp, #0x18]
    //     0x76d65c: ldur            w3, [x0, #0x17]
    //     0x76d660: add             x3, x3, HEAP, lsl #32
    //     0x76d664: stur            x3, [fp, #-8]
    // 0x76d668: CheckStackOverflow
    //     0x76d668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76d66c: cmp             SP, x16
    //     0x76d670: b.ls            #0x76d834
    // 0x76d674: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x76d674: ldur            w0, [x3, #0x17]
    // 0x76d678: DecompressPointer r0
    //     0x76d678: add             x0, x0, HEAP, lsl #32
    // 0x76d67c: r1 = LoadInt32Instr(r0)
    //     0x76d67c: sbfx            x1, x0, #1, #0x1f
    //     0x76d680: tbz             w0, #0, #0x76d688
    //     0x76d684: ldur            x1, [x0, #7]
    // 0x76d688: add             x2, x1, #1
    // 0x76d68c: r0 = BoxInt64Instr(r2)
    //     0x76d68c: sbfiz           x0, x2, #1, #0x1f
    //     0x76d690: cmp             x2, x0, asr #1
    //     0x76d694: b.eq            #0x76d6a0
    //     0x76d698: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x76d69c: stur            x2, [x0, #7]
    // 0x76d6a0: ArrayStore: r3[0] = r0  ; List_4
    //     0x76d6a0: stur            w0, [x3, #0x17]
    //     0x76d6a4: tbz             w0, #0, #0x76d6c0
    //     0x76d6a8: ldurb           w16, [x3, #-1]
    //     0x76d6ac: ldurb           w17, [x0, #-1]
    //     0x76d6b0: and             x16, x17, x16, lsr #2
    //     0x76d6b4: tst             x16, HEAP, lsr #32
    //     0x76d6b8: b.eq            #0x76d6c0
    //     0x76d6bc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x76d6c0: LoadField: r0 = r3->field_f
    //     0x76d6c0: ldur            w0, [x3, #0xf]
    // 0x76d6c4: DecompressPointer r0
    //     0x76d6c4: add             x0, x0, HEAP, lsl #32
    // 0x76d6c8: LoadField: r1 = r0->field_67
    //     0x76d6c8: ldur            x1, [x0, #0x67]
    // 0x76d6cc: cmn             x1, #1
    // 0x76d6d0: b.eq            #0x76d6f0
    // 0x76d6d4: LoadField: r1 = r0->field_73
    //     0x76d6d4: ldur            w1, [x0, #0x73]
    // 0x76d6d8: DecompressPointer r1
    //     0x76d6d8: add             x1, x1, HEAP, lsl #32
    // 0x76d6dc: tbz             w1, #4, #0x76d6f0
    // 0x76d6e0: r0 = Null
    //     0x76d6e0: mov             x0, NULL
    // 0x76d6e4: LeaveFrame
    //     0x76d6e4: mov             SP, fp
    //     0x76d6e8: ldp             fp, lr, [SP], #0x10
    // 0x76d6ec: ret
    //     0x76d6ec: ret             
    // 0x76d6f0: ldr             x4, [fp, #0x10]
    // 0x76d6f4: mov             x0, x4
    // 0x76d6f8: r2 = Null
    //     0x76d6f8: mov             x2, NULL
    // 0x76d6fc: r1 = Null
    //     0x76d6fc: mov             x1, NULL
    // 0x76d700: r4 = LoadClassIdInstr(r0)
    //     0x76d700: ldur            x4, [x0, #-1]
    //     0x76d704: ubfx            x4, x4, #0xc, #0x14
    // 0x76d708: sub             x4, x4, #0xbba
    // 0x76d70c: cmp             x4, #0x9a
    // 0x76d710: b.ls            #0x76d724
    // 0x76d714: r8 = RenderBox
    //     0x76d714: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x76d718: r3 = Null
    //     0x76d718: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b588] Null
    //     0x76d71c: ldr             x3, [x3, #0x588]
    // 0x76d720: r0 = RenderBox()
    //     0x76d720: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x76d724: ldur            x0, [fp, #-8]
    // 0x76d728: LoadField: r2 = r0->field_13
    //     0x76d728: ldur            w2, [x0, #0x13]
    // 0x76d72c: DecompressPointer r2
    //     0x76d72c: add             x2, x2, HEAP, lsl #32
    // 0x76d730: mov             x1, x2
    // 0x76d734: stur            x2, [fp, #-0x10]
    // 0x76d738: r0 = loosen()
    //     0x76d738: bl              #0x73cf04  ; [package:flutter/src/rendering/box.dart] BoxConstraints::loosen
    // 0x76d73c: ldr             x3, [fp, #0x10]
    // 0x76d740: r1 = LoadClassIdInstr(r3)
    //     0x76d740: ldur            x1, [x3, #-1]
    //     0x76d744: ubfx            x1, x1, #0xc, #0x14
    // 0x76d748: r16 = true
    //     0x76d748: add             x16, NULL, #0x20  ; true
    // 0x76d74c: str             x16, [SP]
    // 0x76d750: mov             x2, x0
    // 0x76d754: mov             x0, x1
    // 0x76d758: mov             x1, x3
    // 0x76d75c: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x76d75c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x76d760: ldr             x4, [x4, #0x5c0]
    // 0x76d764: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x76d764: movz            x17, #0xed1d
    //     0x76d768: add             lr, x0, x17
    //     0x76d76c: ldr             lr, [x21, lr, lsl #3]
    //     0x76d770: blr             lr
    // 0x76d774: ldur            x0, [fp, #-8]
    // 0x76d778: LoadField: r2 = r0->field_1b
    //     0x76d778: ldur            w2, [x0, #0x1b]
    // 0x76d77c: DecompressPointer r2
    //     0x76d77c: add             x2, x2, HEAP, lsl #32
    // 0x76d780: ldr             x1, [fp, #0x10]
    // 0x76d784: stur            x2, [fp, #-0x18]
    // 0x76d788: r0 = size()
    //     0x76d788: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76d78c: LoadField: d0 = r0->field_7
    //     0x76d78c: ldur            d0, [x0, #7]
    // 0x76d790: ldur            x1, [fp, #-0x18]
    // 0x76d794: LoadField: d1 = r1->field_7
    //     0x76d794: ldur            d1, [x1, #7]
    // 0x76d798: fadd            d2, d1, d0
    // 0x76d79c: r0 = inline_Allocate_Double()
    //     0x76d79c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x76d7a0: add             x0, x0, #0x10
    //     0x76d7a4: cmp             x1, x0
    //     0x76d7a8: b.ls            #0x76d83c
    //     0x76d7ac: str             x0, [THR, #0x50]  ; THR::top
    //     0x76d7b0: sub             x0, x0, #0xf
    //     0x76d7b4: movz            x1, #0xe15c
    //     0x76d7b8: movk            x1, #0x3, lsl #16
    //     0x76d7bc: stur            x1, [x0, #-1]
    // 0x76d7c0: StoreField: r0->field_7 = d2
    //     0x76d7c0: stur            d2, [x0, #7]
    // 0x76d7c4: ldur            x1, [fp, #-8]
    // 0x76d7c8: StoreField: r1->field_1b = r0
    //     0x76d7c8: stur            w0, [x1, #0x1b]
    //     0x76d7cc: ldurb           w16, [x1, #-1]
    //     0x76d7d0: ldurb           w17, [x0, #-1]
    //     0x76d7d4: and             x16, x17, x16, lsr #2
    //     0x76d7d8: tst             x16, HEAP, lsr #32
    //     0x76d7dc: b.eq            #0x76d7e4
    //     0x76d7e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76d7e4: ldur            x2, [fp, #-0x10]
    // 0x76d7e8: LoadField: d0 = r2->field_f
    //     0x76d7e8: ldur            d0, [x2, #0xf]
    // 0x76d7ec: fcmp            d2, d0
    // 0x76d7f0: b.le            #0x76d824
    // 0x76d7f4: LoadField: r2 = r1->field_f
    //     0x76d7f4: ldur            w2, [x1, #0xf]
    // 0x76d7f8: DecompressPointer r2
    //     0x76d7f8: add             x2, x2, HEAP, lsl #32
    // 0x76d7fc: LoadField: r3 = r2->field_67
    //     0x76d7fc: ldur            x3, [x2, #0x67]
    // 0x76d800: cmn             x3, #1
    // 0x76d804: b.ne            #0x76d824
    // 0x76d808: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x76d808: ldur            w3, [x1, #0x17]
    // 0x76d80c: DecompressPointer r3
    //     0x76d80c: add             x3, x3, HEAP, lsl #32
    // 0x76d810: r1 = LoadInt32Instr(r3)
    //     0x76d810: sbfx            x1, x3, #1, #0x1f
    //     0x76d814: tbz             w3, #0, #0x76d81c
    //     0x76d818: ldur            x1, [x3, #7]
    // 0x76d81c: sub             x3, x1, #1
    // 0x76d820: StoreField: r2->field_67 = r3
    //     0x76d820: stur            x3, [x2, #0x67]
    // 0x76d824: r0 = Null
    //     0x76d824: mov             x0, NULL
    // 0x76d828: LeaveFrame
    //     0x76d828: mov             SP, fp
    //     0x76d82c: ldp             fp, lr, [SP], #0x10
    // 0x76d830: ret
    //     0x76d830: ret             
    // 0x76d834: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76d834: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76d838: b               #0x76d674
    // 0x76d83c: SaveReg d2
    //     0x76d83c: str             q2, [SP, #-0x10]!
    // 0x76d840: r0 = AllocateDouble()
    //     0x76d840: bl              #0xec2254  ; AllocateDoubleStub
    // 0x76d844: RestoreReg d2
    //     0x76d844: ldr             q2, [SP], #0x10
    // 0x76d848: b               #0x76d7c0
  }
  _ setupParentData(/* No info */) {
    // ** addr: 0x787e24, size: 0xb8
    // 0x787e24: EnterFrame
    //     0x787e24: stp             fp, lr, [SP, #-0x10]!
    //     0x787e28: mov             fp, SP
    // 0x787e2c: AllocStack(0x8)
    //     0x787e2c: sub             SP, SP, #8
    // 0x787e30: SetupParameters(_RenderTextSelectionToolbarItemsLayout this /* r1 => r4 */, dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x787e30: mov             x0, x2
    //     0x787e34: mov             x4, x1
    //     0x787e38: mov             x3, x2
    //     0x787e3c: stur            x2, [fp, #-8]
    // 0x787e40: r2 = Null
    //     0x787e40: mov             x2, NULL
    // 0x787e44: r1 = Null
    //     0x787e44: mov             x1, NULL
    // 0x787e48: r4 = 60
    //     0x787e48: movz            x4, #0x3c
    // 0x787e4c: branchIfSmi(r0, 0x787e58)
    //     0x787e4c: tbz             w0, #0, #0x787e58
    // 0x787e50: r4 = LoadClassIdInstr(r0)
    //     0x787e50: ldur            x4, [x0, #-1]
    //     0x787e54: ubfx            x4, x4, #0xc, #0x14
    // 0x787e58: sub             x4, x4, #0xbba
    // 0x787e5c: cmp             x4, #0x9a
    // 0x787e60: b.ls            #0x787e74
    // 0x787e64: r8 = RenderBox
    //     0x787e64: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x787e68: r3 = Null
    //     0x787e68: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b4b8] Null
    //     0x787e6c: ldr             x3, [x3, #0x4b8]
    // 0x787e70: r0 = RenderBox()
    //     0x787e70: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x787e74: ldur            x0, [fp, #-8]
    // 0x787e78: LoadField: r1 = r0->field_7
    //     0x787e78: ldur            w1, [x0, #7]
    // 0x787e7c: DecompressPointer r1
    //     0x787e7c: add             x1, x1, HEAP, lsl #32
    // 0x787e80: r2 = LoadClassIdInstr(r1)
    //     0x787e80: ldur            x2, [x1, #-1]
    //     0x787e84: ubfx            x2, x2, #0xc, #0x14
    // 0x787e88: cmp             x2, #0xc77
    // 0x787e8c: b.eq            #0x787ecc
    // 0x787e90: r1 = <RenderBox>
    //     0x787e90: add             x1, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x787e94: ldr             x1, [x1, #0x1d8]
    // 0x787e98: r0 = ToolbarItemsParentData()
    //     0x787e98: bl              #0x787a88  ; AllocateToolbarItemsParentDataStub -> ToolbarItemsParentData (size=0x1c)
    // 0x787e9c: r1 = false
    //     0x787e9c: add             x1, NULL, #0x30  ; false
    // 0x787ea0: ArrayStore: r0[0] = r1  ; List_4
    //     0x787ea0: stur            w1, [x0, #0x17]
    // 0x787ea4: r1 = Instance_Offset
    //     0x787ea4: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x787ea8: StoreField: r0->field_7 = r1
    //     0x787ea8: stur            w1, [x0, #7]
    // 0x787eac: ldur            x1, [fp, #-8]
    // 0x787eb0: StoreField: r1->field_7 = r0
    //     0x787eb0: stur            w0, [x1, #7]
    //     0x787eb4: ldurb           w16, [x1, #-1]
    //     0x787eb8: ldurb           w17, [x0, #-1]
    //     0x787ebc: and             x16, x17, x16, lsr #2
    //     0x787ec0: tst             x16, HEAP, lsr #32
    //     0x787ec4: b.eq            #0x787ecc
    //     0x787ec8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x787ecc: r0 = Null
    //     0x787ecc: mov             x0, NULL
    // 0x787ed0: LeaveFrame
    //     0x787ed0: mov             SP, fp
    //     0x787ed4: ldp             fp, lr, [SP], #0x10
    // 0x787ed8: ret
    //     0x787ed8: ret             
  }
  _ paint(/* No info */) {
    // ** addr: 0x7973dc, size: 0x74
    // 0x7973dc: EnterFrame
    //     0x7973dc: stp             fp, lr, [SP, #-0x10]!
    //     0x7973e0: mov             fp, SP
    // 0x7973e4: AllocStack(0x18)
    //     0x7973e4: sub             SP, SP, #0x18
    // 0x7973e8: SetupParameters(_RenderTextSelectionToolbarItemsLayout this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7973e8: stur            x1, [fp, #-8]
    //     0x7973ec: stur            x2, [fp, #-0x10]
    //     0x7973f0: stur            x3, [fp, #-0x18]
    // 0x7973f4: CheckStackOverflow
    //     0x7973f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7973f8: cmp             SP, x16
    //     0x7973fc: b.ls            #0x797448
    // 0x797400: r1 = 2
    //     0x797400: movz            x1, #0x2
    // 0x797404: r0 = AllocateContext()
    //     0x797404: bl              #0xec126c  ; AllocateContextStub
    // 0x797408: mov             x1, x0
    // 0x79740c: ldur            x0, [fp, #-0x10]
    // 0x797410: StoreField: r1->field_f = r0
    //     0x797410: stur            w0, [x1, #0xf]
    // 0x797414: ldur            x0, [fp, #-0x18]
    // 0x797418: StoreField: r1->field_13 = r0
    //     0x797418: stur            w0, [x1, #0x13]
    // 0x79741c: mov             x2, x1
    // 0x797420: r1 = Function '<anonymous closure>':.
    //     0x797420: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5b4c8] AnonymousClosure: (0x797450), in [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::paint (0x7973dc)
    //     0x797424: ldr             x1, [x1, #0x4c8]
    // 0x797428: r0 = AllocateClosure()
    //     0x797428: bl              #0xec1630  ; AllocateClosureStub
    // 0x79742c: ldur            x1, [fp, #-8]
    // 0x797430: mov             x2, x0
    // 0x797434: r0 = visitChildren()
    //     0x797434: bl              #0x786888  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::visitChildren
    // 0x797438: r0 = Null
    //     0x797438: mov             x0, NULL
    // 0x79743c: LeaveFrame
    //     0x79743c: mov             SP, fp
    //     0x797440: ldp             fp, lr, [SP], #0x10
    // 0x797444: ret
    //     0x797444: ret             
    // 0x797448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x797448: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x79744c: b               #0x797400
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x797450, size: 0x118
    // 0x797450: EnterFrame
    //     0x797450: stp             fp, lr, [SP, #-0x10]!
    //     0x797454: mov             fp, SP
    // 0x797458: AllocStack(0x18)
    //     0x797458: sub             SP, SP, #0x18
    // 0x79745c: SetupParameters()
    //     0x79745c: ldr             x0, [fp, #0x18]
    //     0x797460: ldur            w3, [x0, #0x17]
    //     0x797464: add             x3, x3, HEAP, lsl #32
    //     0x797468: stur            x3, [fp, #-8]
    // 0x79746c: CheckStackOverflow
    //     0x79746c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x797470: cmp             SP, x16
    //     0x797474: b.ls            #0x79755c
    // 0x797478: ldr             x0, [fp, #0x10]
    // 0x79747c: r2 = Null
    //     0x79747c: mov             x2, NULL
    // 0x797480: r1 = Null
    //     0x797480: mov             x1, NULL
    // 0x797484: r4 = LoadClassIdInstr(r0)
    //     0x797484: ldur            x4, [x0, #-1]
    //     0x797488: ubfx            x4, x4, #0xc, #0x14
    // 0x79748c: sub             x4, x4, #0xbba
    // 0x797490: cmp             x4, #0x9a
    // 0x797494: b.ls            #0x7974a8
    // 0x797498: r8 = RenderBox
    //     0x797498: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x79749c: r3 = Null
    //     0x79749c: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b4d0] Null
    //     0x7974a0: ldr             x3, [x3, #0x4d0]
    // 0x7974a4: r0 = RenderBox()
    //     0x7974a4: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7974a8: ldr             x3, [fp, #0x10]
    // 0x7974ac: LoadField: r4 = r3->field_7
    //     0x7974ac: ldur            w4, [x3, #7]
    // 0x7974b0: DecompressPointer r4
    //     0x7974b0: add             x4, x4, HEAP, lsl #32
    // 0x7974b4: stur            x4, [fp, #-0x10]
    // 0x7974b8: cmp             w4, NULL
    // 0x7974bc: b.eq            #0x797564
    // 0x7974c0: mov             x0, x4
    // 0x7974c4: r2 = Null
    //     0x7974c4: mov             x2, NULL
    // 0x7974c8: r1 = Null
    //     0x7974c8: mov             x1, NULL
    // 0x7974cc: r4 = LoadClassIdInstr(r0)
    //     0x7974cc: ldur            x4, [x0, #-1]
    //     0x7974d0: ubfx            x4, x4, #0xc, #0x14
    // 0x7974d4: cmp             x4, #0xc77
    // 0x7974d8: b.eq            #0x7974f0
    // 0x7974dc: r8 = ToolbarItemsParentData
    //     0x7974dc: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x7974e0: ldr             x8, [x8, #0x490]
    // 0x7974e4: r3 = Null
    //     0x7974e4: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b4e0] Null
    //     0x7974e8: ldr             x3, [x3, #0x4e0]
    // 0x7974ec: r0 = DefaultTypeTest()
    //     0x7974ec: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7974f0: ldur            x0, [fp, #-0x10]
    // 0x7974f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7974f4: ldur            w1, [x0, #0x17]
    // 0x7974f8: DecompressPointer r1
    //     0x7974f8: add             x1, x1, HEAP, lsl #32
    // 0x7974fc: tbz             w1, #4, #0x797510
    // 0x797500: r0 = Null
    //     0x797500: mov             x0, NULL
    // 0x797504: LeaveFrame
    //     0x797504: mov             SP, fp
    //     0x797508: ldp             fp, lr, [SP], #0x10
    // 0x79750c: ret
    //     0x79750c: ret             
    // 0x797510: ldur            x1, [fp, #-8]
    // 0x797514: LoadField: r3 = r1->field_f
    //     0x797514: ldur            w3, [x1, #0xf]
    // 0x797518: DecompressPointer r3
    //     0x797518: add             x3, x3, HEAP, lsl #32
    // 0x79751c: stur            x3, [fp, #-0x18]
    // 0x797520: LoadField: r2 = r0->field_7
    //     0x797520: ldur            w2, [x0, #7]
    // 0x797524: DecompressPointer r2
    //     0x797524: add             x2, x2, HEAP, lsl #32
    // 0x797528: LoadField: r0 = r1->field_13
    //     0x797528: ldur            w0, [x1, #0x13]
    // 0x79752c: DecompressPointer r0
    //     0x79752c: add             x0, x0, HEAP, lsl #32
    // 0x797530: mov             x1, x2
    // 0x797534: mov             x2, x0
    // 0x797538: r0 = +()
    //     0x797538: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x79753c: ldur            x1, [fp, #-0x18]
    // 0x797540: ldr             x2, [fp, #0x10]
    // 0x797544: mov             x3, x0
    // 0x797548: r0 = paintChild()
    //     0x797548: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x79754c: r0 = Null
    //     0x79754c: mov             x0, NULL
    // 0x797550: LeaveFrame
    //     0x797550: mov             SP, fp
    //     0x797554: ldp             fp, lr, [SP], #0x10
    // 0x797558: ret
    //     0x797558: ret             
    // 0x79755c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x79755c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x797560: b               #0x797478
    // 0x797564: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x797564: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x7fd5cc, size: 0x168
    // 0x7fd5cc: EnterFrame
    //     0x7fd5cc: stp             fp, lr, [SP, #-0x10]!
    //     0x7fd5d0: mov             fp, SP
    // 0x7fd5d4: AllocStack(0x28)
    //     0x7fd5d4: sub             SP, SP, #0x28
    // 0x7fd5d8: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7fd5d8: mov             x4, x2
    //     0x7fd5dc: stur            x2, [fp, #-0x18]
    //     0x7fd5e0: stur            x3, [fp, #-0x20]
    // 0x7fd5e4: CheckStackOverflow
    //     0x7fd5e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fd5e8: cmp             SP, x16
    //     0x7fd5ec: b.ls            #0x7fd720
    // 0x7fd5f0: LoadField: r0 = r1->field_63
    //     0x7fd5f0: ldur            w0, [x1, #0x63]
    // 0x7fd5f4: DecompressPointer r0
    //     0x7fd5f4: add             x0, x0, HEAP, lsl #32
    // 0x7fd5f8: mov             x5, x0
    // 0x7fd5fc: stur            x5, [fp, #-0x10]
    // 0x7fd600: CheckStackOverflow
    //     0x7fd600: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fd604: cmp             SP, x16
    //     0x7fd608: b.ls            #0x7fd728
    // 0x7fd60c: cmp             w5, NULL
    // 0x7fd610: b.eq            #0x7fd710
    // 0x7fd614: LoadField: r6 = r5->field_7
    //     0x7fd614: ldur            w6, [x5, #7]
    // 0x7fd618: DecompressPointer r6
    //     0x7fd618: add             x6, x6, HEAP, lsl #32
    // 0x7fd61c: stur            x6, [fp, #-8]
    // 0x7fd620: cmp             w6, NULL
    // 0x7fd624: b.eq            #0x7fd730
    // 0x7fd628: mov             x0, x6
    // 0x7fd62c: r2 = Null
    //     0x7fd62c: mov             x2, NULL
    // 0x7fd630: r1 = Null
    //     0x7fd630: mov             x1, NULL
    // 0x7fd634: r4 = LoadClassIdInstr(r0)
    //     0x7fd634: ldur            x4, [x0, #-1]
    //     0x7fd638: ubfx            x4, x4, #0xc, #0x14
    // 0x7fd63c: cmp             x4, #0xc77
    // 0x7fd640: b.eq            #0x7fd658
    // 0x7fd644: r8 = ToolbarItemsParentData
    //     0x7fd644: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x7fd648: ldr             x8, [x8, #0x490]
    // 0x7fd64c: r3 = Null
    //     0x7fd64c: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b4a8] Null
    //     0x7fd650: ldr             x3, [x3, #0x4a8]
    // 0x7fd654: r0 = DefaultTypeTest()
    //     0x7fd654: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7fd658: ldur            x0, [fp, #-8]
    // 0x7fd65c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7fd65c: ldur            w1, [x0, #0x17]
    // 0x7fd660: DecompressPointer r1
    //     0x7fd660: add             x1, x1, HEAP, lsl #32
    // 0x7fd664: tbz             w1, #4, #0x7fd678
    // 0x7fd668: LoadField: r1 = r0->field_f
    //     0x7fd668: ldur            w1, [x0, #0xf]
    // 0x7fd66c: DecompressPointer r1
    //     0x7fd66c: add             x1, x1, HEAP, lsl #32
    // 0x7fd670: mov             x5, x1
    // 0x7fd674: b               #0x7fd6f4
    // 0x7fd678: ldur            x3, [fp, #-0x10]
    // 0x7fd67c: LoadField: r4 = r0->field_7
    //     0x7fd67c: ldur            w4, [x0, #7]
    // 0x7fd680: DecompressPointer r4
    //     0x7fd680: add             x4, x4, HEAP, lsl #32
    // 0x7fd684: ldur            x1, [fp, #-0x20]
    // 0x7fd688: mov             x2, x4
    // 0x7fd68c: stur            x4, [fp, #-0x28]
    // 0x7fd690: r0 = -()
    //     0x7fd690: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x7fd694: ldur            x1, [fp, #-0x28]
    // 0x7fd698: stur            x0, [fp, #-0x28]
    // 0x7fd69c: r0 = unary-()
    //     0x7fd69c: bl              #0x6a58ac  ; [dart:ui] Offset::unary-
    // 0x7fd6a0: ldur            x1, [fp, #-0x18]
    // 0x7fd6a4: mov             x2, x0
    // 0x7fd6a8: r0 = pushOffset()
    //     0x7fd6a8: bl              #0x7faa44  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::pushOffset
    // 0x7fd6ac: ldur            x1, [fp, #-0x10]
    // 0x7fd6b0: r0 = LoadClassIdInstr(r1)
    //     0x7fd6b0: ldur            x0, [x1, #-1]
    //     0x7fd6b4: ubfx            x0, x0, #0xc, #0x14
    // 0x7fd6b8: ldur            x2, [fp, #-0x18]
    // 0x7fd6bc: ldur            x3, [fp, #-0x28]
    // 0x7fd6c0: r0 = GDT[cid_x0 + 0xdf93]()
    //     0x7fd6c0: movz            x17, #0xdf93
    //     0x7fd6c4: add             lr, x0, x17
    //     0x7fd6c8: ldr             lr, [x21, lr, lsl #3]
    //     0x7fd6cc: blr             lr
    // 0x7fd6d0: ldur            x1, [fp, #-0x18]
    // 0x7fd6d4: stur            x0, [fp, #-0x10]
    // 0x7fd6d8: r0 = popTransform()
    //     0x7fd6d8: bl              #0x7fa9a8  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::popTransform
    // 0x7fd6dc: ldur            x1, [fp, #-0x10]
    // 0x7fd6e0: tbz             w1, #4, #0x7fd700
    // 0x7fd6e4: ldur            x1, [fp, #-8]
    // 0x7fd6e8: LoadField: r2 = r1->field_f
    //     0x7fd6e8: ldur            w2, [x1, #0xf]
    // 0x7fd6ec: DecompressPointer r2
    //     0x7fd6ec: add             x2, x2, HEAP, lsl #32
    // 0x7fd6f0: mov             x5, x2
    // 0x7fd6f4: ldur            x4, [fp, #-0x18]
    // 0x7fd6f8: ldur            x3, [fp, #-0x20]
    // 0x7fd6fc: b               #0x7fd5fc
    // 0x7fd700: r0 = true
    //     0x7fd700: add             x0, NULL, #0x20  ; true
    // 0x7fd704: LeaveFrame
    //     0x7fd704: mov             SP, fp
    //     0x7fd708: ldp             fp, lr, [SP], #0x10
    // 0x7fd70c: ret
    //     0x7fd70c: ret             
    // 0x7fd710: r0 = false
    //     0x7fd710: add             x0, NULL, #0x30  ; false
    // 0x7fd714: LeaveFrame
    //     0x7fd714: mov             SP, fp
    //     0x7fd718: ldp             fp, lr, [SP], #0x10
    // 0x7fd71c: ret
    //     0x7fd71c: ret             
    // 0x7fd720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fd720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fd724: b               #0x7fd5f0
    // 0x7fd728: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fd728: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fd72c: b               #0x7fd60c
    // 0x7fd730: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fd730: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitChildrenForSemantics(/* No info */) {
    // ** addr: 0x800b54, size: 0x68
    // 0x800b54: EnterFrame
    //     0x800b54: stp             fp, lr, [SP, #-0x10]!
    //     0x800b58: mov             fp, SP
    // 0x800b5c: AllocStack(0x10)
    //     0x800b5c: sub             SP, SP, #0x10
    // 0x800b60: SetupParameters(_RenderTextSelectionToolbarItemsLayout this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x800b60: stur            x1, [fp, #-8]
    //     0x800b64: stur            x2, [fp, #-0x10]
    // 0x800b68: CheckStackOverflow
    //     0x800b68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x800b6c: cmp             SP, x16
    //     0x800b70: b.ls            #0x800bb4
    // 0x800b74: r1 = 1
    //     0x800b74: movz            x1, #0x1
    // 0x800b78: r0 = AllocateContext()
    //     0x800b78: bl              #0xec126c  ; AllocateContextStub
    // 0x800b7c: mov             x1, x0
    // 0x800b80: ldur            x0, [fp, #-0x10]
    // 0x800b84: StoreField: r1->field_f = r0
    //     0x800b84: stur            w0, [x1, #0xf]
    // 0x800b88: mov             x2, x1
    // 0x800b8c: r1 = Function '<anonymous closure>':.
    //     0x800b8c: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5b478] AnonymousClosure: (0x800bbc), in [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::visitChildrenForSemantics (0x800b54)
    //     0x800b90: ldr             x1, [x1, #0x478]
    // 0x800b94: r0 = AllocateClosure()
    //     0x800b94: bl              #0xec1630  ; AllocateClosureStub
    // 0x800b98: ldur            x1, [fp, #-8]
    // 0x800b9c: mov             x2, x0
    // 0x800ba0: r0 = visitChildren()
    //     0x800ba0: bl              #0x786888  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::visitChildren
    // 0x800ba4: r0 = Null
    //     0x800ba4: mov             x0, NULL
    // 0x800ba8: LeaveFrame
    //     0x800ba8: mov             SP, fp
    //     0x800bac: ldp             fp, lr, [SP], #0x10
    // 0x800bb0: ret
    //     0x800bb0: ret             
    // 0x800bb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x800bb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x800bb8: b               #0x800b74
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x800bbc, size: 0xf0
    // 0x800bbc: EnterFrame
    //     0x800bbc: stp             fp, lr, [SP, #-0x10]!
    //     0x800bc0: mov             fp, SP
    // 0x800bc4: AllocStack(0x20)
    //     0x800bc4: sub             SP, SP, #0x20
    // 0x800bc8: SetupParameters()
    //     0x800bc8: ldr             x0, [fp, #0x18]
    //     0x800bcc: ldur            w3, [x0, #0x17]
    //     0x800bd0: add             x3, x3, HEAP, lsl #32
    //     0x800bd4: stur            x3, [fp, #-8]
    // 0x800bd8: CheckStackOverflow
    //     0x800bd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x800bdc: cmp             SP, x16
    //     0x800be0: b.ls            #0x800ca0
    // 0x800be4: ldr             x0, [fp, #0x10]
    // 0x800be8: r2 = Null
    //     0x800be8: mov             x2, NULL
    // 0x800bec: r1 = Null
    //     0x800bec: mov             x1, NULL
    // 0x800bf0: r4 = LoadClassIdInstr(r0)
    //     0x800bf0: ldur            x4, [x0, #-1]
    //     0x800bf4: ubfx            x4, x4, #0xc, #0x14
    // 0x800bf8: sub             x4, x4, #0xbba
    // 0x800bfc: cmp             x4, #0x9a
    // 0x800c00: b.ls            #0x800c14
    // 0x800c04: r8 = RenderBox
    //     0x800c04: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x800c08: r3 = Null
    //     0x800c08: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b480] Null
    //     0x800c0c: ldr             x3, [x3, #0x480]
    // 0x800c10: r0 = RenderBox()
    //     0x800c10: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x800c14: ldr             x3, [fp, #0x10]
    // 0x800c18: LoadField: r4 = r3->field_7
    //     0x800c18: ldur            w4, [x3, #7]
    // 0x800c1c: DecompressPointer r4
    //     0x800c1c: add             x4, x4, HEAP, lsl #32
    // 0x800c20: stur            x4, [fp, #-0x10]
    // 0x800c24: cmp             w4, NULL
    // 0x800c28: b.eq            #0x800ca8
    // 0x800c2c: mov             x0, x4
    // 0x800c30: r2 = Null
    //     0x800c30: mov             x2, NULL
    // 0x800c34: r1 = Null
    //     0x800c34: mov             x1, NULL
    // 0x800c38: r4 = LoadClassIdInstr(r0)
    //     0x800c38: ldur            x4, [x0, #-1]
    //     0x800c3c: ubfx            x4, x4, #0xc, #0x14
    // 0x800c40: cmp             x4, #0xc77
    // 0x800c44: b.eq            #0x800c5c
    // 0x800c48: r8 = ToolbarItemsParentData
    //     0x800c48: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x800c4c: ldr             x8, [x8, #0x490]
    // 0x800c50: r3 = Null
    //     0x800c50: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b498] Null
    //     0x800c54: ldr             x3, [x3, #0x498]
    // 0x800c58: r0 = DefaultTypeTest()
    //     0x800c58: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x800c5c: ldur            x0, [fp, #-0x10]
    // 0x800c60: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x800c60: ldur            w1, [x0, #0x17]
    // 0x800c64: DecompressPointer r1
    //     0x800c64: add             x1, x1, HEAP, lsl #32
    // 0x800c68: tbnz            w1, #4, #0x800c90
    // 0x800c6c: ldur            x0, [fp, #-8]
    // 0x800c70: LoadField: r1 = r0->field_f
    //     0x800c70: ldur            w1, [x0, #0xf]
    // 0x800c74: DecompressPointer r1
    //     0x800c74: add             x1, x1, HEAP, lsl #32
    // 0x800c78: ldr             x16, [fp, #0x10]
    // 0x800c7c: stp             x16, x1, [SP]
    // 0x800c80: mov             x0, x1
    // 0x800c84: ClosureCall
    //     0x800c84: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x800c88: ldur            x2, [x0, #0x1f]
    //     0x800c8c: blr             x2
    // 0x800c90: r0 = Null
    //     0x800c90: mov             x0, NULL
    // 0x800c94: LeaveFrame
    //     0x800c94: mov             SP, fp
    //     0x800c98: ldp             fp, lr, [SP], #0x10
    // 0x800c9c: ret
    //     0x800c9c: ret             
    // 0x800ca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x800ca0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x800ca4: b               #0x800be4
    // 0x800ca8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x800ca8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _RenderTextSelectionToolbarItemsLayout(/* No info */) {
    // ** addr: 0x856684, size: 0x70
    // 0x856684: EnterFrame
    //     0x856684: stp             fp, lr, [SP, #-0x10]!
    //     0x856688: mov             fp, SP
    // 0x85668c: AllocStack(0x8)
    //     0x85668c: sub             SP, SP, #8
    // 0x856690: r0 = -1
    //     0x856690: movn            x0, #0
    // 0x856694: stur            x1, [fp, #-8]
    // 0x856698: CheckStackOverflow
    //     0x856698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85669c: cmp             SP, x16
    //     0x8566a0: b.ls            #0x8566ec
    // 0x8566a4: StoreField: r1->field_67 = r0
    //     0x8566a4: stur            x0, [x1, #0x67]
    // 0x8566a8: StoreField: r1->field_6f = r2
    //     0x8566a8: stur            w2, [x1, #0x6f]
    // 0x8566ac: StoreField: r1->field_73 = r3
    //     0x8566ac: stur            w3, [x1, #0x73]
    // 0x8566b0: StoreField: r1->field_57 = rZR
    //     0x8566b0: stur            xzr, [x1, #0x57]
    // 0x8566b4: r0 = _LayoutCacheStorage()
    //     0x8566b4: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x8566b8: ldur            x1, [fp, #-8]
    // 0x8566bc: StoreField: r1->field_4f = r0
    //     0x8566bc: stur            w0, [x1, #0x4f]
    //     0x8566c0: ldurb           w16, [x1, #-1]
    //     0x8566c4: ldurb           w17, [x0, #-1]
    //     0x8566c8: and             x16, x17, x16, lsr #2
    //     0x8566cc: tst             x16, HEAP, lsr #32
    //     0x8566d0: b.eq            #0x8566d8
    //     0x8566d4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8566d8: r0 = RenderObject()
    //     0x8566d8: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x8566dc: r0 = Null
    //     0x8566dc: mov             x0, NULL
    // 0x8566e0: LeaveFrame
    //     0x8566e0: mov             SP, fp
    //     0x8566e4: ldp             fp, lr, [SP], #0x10
    // 0x8566e8: ret
    //     0x8566e8: ret             
    // 0x8566ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8566ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8566f0: b               #0x8566a4
  }
  set _ overflowOpen=(/* No info */) {
    // ** addr: 0xc69548, size: 0x54
    // 0xc69548: EnterFrame
    //     0xc69548: stp             fp, lr, [SP, #-0x10]!
    //     0xc6954c: mov             fp, SP
    // 0xc69550: CheckStackOverflow
    //     0xc69550: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc69554: cmp             SP, x16
    //     0xc69558: b.ls            #0xc69594
    // 0xc6955c: LoadField: r0 = r1->field_73
    //     0xc6955c: ldur            w0, [x1, #0x73]
    // 0xc69560: DecompressPointer r0
    //     0xc69560: add             x0, x0, HEAP, lsl #32
    // 0xc69564: cmp             w2, w0
    // 0xc69568: b.ne            #0xc6957c
    // 0xc6956c: r0 = Null
    //     0xc6956c: mov             x0, NULL
    // 0xc69570: LeaveFrame
    //     0xc69570: mov             SP, fp
    //     0xc69574: ldp             fp, lr, [SP], #0x10
    // 0xc69578: ret
    //     0xc69578: ret             
    // 0xc6957c: StoreField: r1->field_73 = r2
    //     0xc6957c: stur            w2, [x1, #0x73]
    // 0xc69580: r0 = markNeedsLayout()
    //     0xc69580: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc69584: r0 = Null
    //     0xc69584: mov             x0, NULL
    // 0xc69588: LeaveFrame
    //     0xc69588: mov             SP, fp
    //     0xc6958c: ldp             fp, lr, [SP], #0x10
    // 0xc69590: ret
    //     0xc69590: ret             
    // 0xc69594: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc69594: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc69598: b               #0xc6955c
  }
  set _ isAbove=(/* No info */) {
    // ** addr: 0xc6959c, size: 0x54
    // 0xc6959c: EnterFrame
    //     0xc6959c: stp             fp, lr, [SP, #-0x10]!
    //     0xc695a0: mov             fp, SP
    // 0xc695a4: CheckStackOverflow
    //     0xc695a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc695a8: cmp             SP, x16
    //     0xc695ac: b.ls            #0xc695e8
    // 0xc695b0: LoadField: r0 = r1->field_6f
    //     0xc695b0: ldur            w0, [x1, #0x6f]
    // 0xc695b4: DecompressPointer r0
    //     0xc695b4: add             x0, x0, HEAP, lsl #32
    // 0xc695b8: cmp             w2, w0
    // 0xc695bc: b.ne            #0xc695d0
    // 0xc695c0: r0 = Null
    //     0xc695c0: mov             x0, NULL
    // 0xc695c4: LeaveFrame
    //     0xc695c4: mov             SP, fp
    //     0xc695c8: ldp             fp, lr, [SP], #0x10
    // 0xc695cc: ret
    //     0xc695cc: ret             
    // 0xc695d0: StoreField: r1->field_6f = r2
    //     0xc695d0: stur            w2, [x1, #0x6f]
    // 0xc695d4: r0 = markNeedsLayout()
    //     0xc695d4: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc695d8: r0 = Null
    //     0xc695d8: mov             x0, NULL
    // 0xc695dc: LeaveFrame
    //     0xc695dc: mov             SP, fp
    //     0xc695e0: ldp             fp, lr, [SP], #0x10
    // 0xc695e4: ret
    //     0xc695e4: ret             
    // 0xc695e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc695e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc695ec: b               #0xc695b0
  }
}

// class id: 3149, size: 0x68, field offset: 0x5c
class _TextSelectionToolbarTrailingEdgeAlignRenderBox extends RenderProxyBox {

  _ performLayout(/* No info */) {
    // ** addr: 0x767494, size: 0x3c8
    // 0x767494: EnterFrame
    //     0x767494: stp             fp, lr, [SP, #-0x10]!
    //     0x767498: mov             fp, SP
    // 0x76749c: AllocStack(0x30)
    //     0x76749c: sub             SP, SP, #0x30
    // 0x7674a0: SetupParameters(_TextSelectionToolbarTrailingEdgeAlignRenderBox this /* r1 => r3, fp-0x18 */)
    //     0x7674a0: mov             x3, x1
    //     0x7674a4: stur            x1, [fp, #-0x18]
    // 0x7674a8: CheckStackOverflow
    //     0x7674a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7674ac: cmp             SP, x16
    //     0x7674b0: b.ls            #0x767820
    // 0x7674b4: LoadField: r4 = r3->field_57
    //     0x7674b4: ldur            w4, [x3, #0x57]
    // 0x7674b8: DecompressPointer r4
    //     0x7674b8: add             x4, x4, HEAP, lsl #32
    // 0x7674bc: stur            x4, [fp, #-0x10]
    // 0x7674c0: cmp             w4, NULL
    // 0x7674c4: b.eq            #0x767828
    // 0x7674c8: LoadField: r5 = r3->field_27
    //     0x7674c8: ldur            w5, [x3, #0x27]
    // 0x7674cc: DecompressPointer r5
    //     0x7674cc: add             x5, x5, HEAP, lsl #32
    // 0x7674d0: stur            x5, [fp, #-8]
    // 0x7674d4: cmp             w5, NULL
    // 0x7674d8: b.eq            #0x7677e4
    // 0x7674dc: mov             x0, x5
    // 0x7674e0: r2 = Null
    //     0x7674e0: mov             x2, NULL
    // 0x7674e4: r1 = Null
    //     0x7674e4: mov             x1, NULL
    // 0x7674e8: r4 = LoadClassIdInstr(r0)
    //     0x7674e8: ldur            x4, [x0, #-1]
    //     0x7674ec: ubfx            x4, x4, #0xc, #0x14
    // 0x7674f0: sub             x4, x4, #0xc83
    // 0x7674f4: cmp             x4, #1
    // 0x7674f8: b.ls            #0x76750c
    // 0x7674fc: r8 = BoxConstraints
    //     0x7674fc: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x767500: r3 = Null
    //     0x767500: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b5e0] Null
    //     0x767504: ldr             x3, [x3, #0x5e0]
    // 0x767508: r0 = BoxConstraints()
    //     0x767508: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76750c: ldur            x1, [fp, #-8]
    // 0x767510: r0 = loosen()
    //     0x767510: bl              #0x73cf04  ; [package:flutter/src/rendering/box.dart] BoxConstraints::loosen
    // 0x767514: ldur            x1, [fp, #-0x10]
    // 0x767518: r2 = LoadClassIdInstr(r1)
    //     0x767518: ldur            x2, [x1, #-1]
    //     0x76751c: ubfx            x2, x2, #0xc, #0x14
    // 0x767520: r16 = true
    //     0x767520: add             x16, NULL, #0x20  ; true
    // 0x767524: str             x16, [SP]
    // 0x767528: mov             x16, x0
    // 0x76752c: mov             x0, x2
    // 0x767530: mov             x2, x16
    // 0x767534: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x767534: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x767538: ldr             x4, [x4, #0x5c0]
    // 0x76753c: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x76753c: movz            x17, #0xed1d
    //     0x767540: add             lr, x0, x17
    //     0x767544: ldr             lr, [x21, lr, lsl #3]
    //     0x767548: blr             lr
    // 0x76754c: ldur            x0, [fp, #-0x18]
    // 0x767550: LoadField: r1 = r0->field_5f
    //     0x767550: ldur            w1, [x0, #0x5f]
    // 0x767554: DecompressPointer r1
    //     0x767554: add             x1, x1, HEAP, lsl #32
    // 0x767558: tbz             w1, #4, #0x7675d8
    // 0x76755c: LoadField: r1 = r0->field_5b
    //     0x76755c: ldur            w1, [x0, #0x5b]
    // 0x767560: DecompressPointer r1
    //     0x767560: add             x1, x1, HEAP, lsl #32
    // 0x767564: cmp             w1, NULL
    // 0x767568: b.ne            #0x7675d0
    // 0x76756c: LoadField: r1 = r0->field_57
    //     0x76756c: ldur            w1, [x0, #0x57]
    // 0x767570: DecompressPointer r1
    //     0x767570: add             x1, x1, HEAP, lsl #32
    // 0x767574: cmp             w1, NULL
    // 0x767578: b.eq            #0x76782c
    // 0x76757c: r0 = size()
    //     0x76757c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x767580: LoadField: d0 = r0->field_7
    //     0x767580: ldur            d0, [x0, #7]
    // 0x767584: r0 = inline_Allocate_Double()
    //     0x767584: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x767588: add             x0, x0, #0x10
    //     0x76758c: cmp             x1, x0
    //     0x767590: b.ls            #0x767830
    //     0x767594: str             x0, [THR, #0x50]  ; THR::top
    //     0x767598: sub             x0, x0, #0xf
    //     0x76759c: movz            x1, #0xe15c
    //     0x7675a0: movk            x1, #0x3, lsl #16
    //     0x7675a4: stur            x1, [x0, #-1]
    // 0x7675a8: StoreField: r0->field_7 = d0
    //     0x7675a8: stur            d0, [x0, #7]
    // 0x7675ac: ldur            x3, [fp, #-0x18]
    // 0x7675b0: StoreField: r3->field_5b = r0
    //     0x7675b0: stur            w0, [x3, #0x5b]
    //     0x7675b4: ldurb           w16, [x3, #-1]
    //     0x7675b8: ldurb           w17, [x0, #-1]
    //     0x7675bc: and             x16, x17, x16, lsr #2
    //     0x7675c0: tst             x16, HEAP, lsr #32
    //     0x7675c4: b.eq            #0x7675cc
    //     0x7675c8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7675cc: b               #0x7675dc
    // 0x7675d0: mov             x3, x0
    // 0x7675d4: b               #0x7675dc
    // 0x7675d8: mov             x3, x0
    // 0x7675dc: LoadField: r4 = r3->field_27
    //     0x7675dc: ldur            w4, [x3, #0x27]
    // 0x7675e0: DecompressPointer r4
    //     0x7675e0: add             x4, x4, HEAP, lsl #32
    // 0x7675e4: stur            x4, [fp, #-8]
    // 0x7675e8: cmp             w4, NULL
    // 0x7675ec: b.eq            #0x767800
    // 0x7675f0: mov             x0, x4
    // 0x7675f4: r2 = Null
    //     0x7675f4: mov             x2, NULL
    // 0x7675f8: r1 = Null
    //     0x7675f8: mov             x1, NULL
    // 0x7675fc: r4 = LoadClassIdInstr(r0)
    //     0x7675fc: ldur            x4, [x0, #-1]
    //     0x767600: ubfx            x4, x4, #0xc, #0x14
    // 0x767604: sub             x4, x4, #0xc83
    // 0x767608: cmp             x4, #1
    // 0x76760c: b.ls            #0x767620
    // 0x767610: r8 = BoxConstraints
    //     0x767610: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x767614: r3 = Null
    //     0x767614: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b5f0] Null
    //     0x767618: ldr             x3, [x3, #0x5f0]
    // 0x76761c: r0 = BoxConstraints()
    //     0x76761c: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x767620: ldur            x0, [fp, #-0x18]
    // 0x767624: LoadField: r1 = r0->field_5b
    //     0x767624: ldur            w1, [x0, #0x5b]
    // 0x767628: DecompressPointer r1
    //     0x767628: add             x1, x1, HEAP, lsl #32
    // 0x76762c: cmp             w1, NULL
    // 0x767630: b.eq            #0x76766c
    // 0x767634: LoadField: r1 = r0->field_57
    //     0x767634: ldur            w1, [x0, #0x57]
    // 0x767638: DecompressPointer r1
    //     0x767638: add             x1, x1, HEAP, lsl #32
    // 0x76763c: cmp             w1, NULL
    // 0x767640: b.eq            #0x767840
    // 0x767644: r0 = size()
    //     0x767644: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x767648: LoadField: d0 = r0->field_7
    //     0x767648: ldur            d0, [x0, #7]
    // 0x76764c: ldur            x0, [fp, #-0x18]
    // 0x767650: LoadField: r1 = r0->field_5b
    //     0x767650: ldur            w1, [x0, #0x5b]
    // 0x767654: DecompressPointer r1
    //     0x767654: add             x1, x1, HEAP, lsl #32
    // 0x767658: cmp             w1, NULL
    // 0x76765c: b.eq            #0x767844
    // 0x767660: LoadField: d1 = r1->field_7
    //     0x767660: ldur            d1, [x1, #7]
    // 0x767664: fcmp            d0, d1
    // 0x767668: b.le            #0x767688
    // 0x76766c: LoadField: r1 = r0->field_57
    //     0x76766c: ldur            w1, [x0, #0x57]
    // 0x767670: DecompressPointer r1
    //     0x767670: add             x1, x1, HEAP, lsl #32
    // 0x767674: cmp             w1, NULL
    // 0x767678: b.eq            #0x767848
    // 0x76767c: r0 = size()
    //     0x76767c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x767680: LoadField: d0 = r0->field_7
    //     0x767680: ldur            d0, [x0, #7]
    // 0x767684: b               #0x76768c
    // 0x767688: mov             v0.16b, v1.16b
    // 0x76768c: ldur            x0, [fp, #-0x18]
    // 0x767690: stur            d0, [fp, #-0x20]
    // 0x767694: LoadField: r1 = r0->field_57
    //     0x767694: ldur            w1, [x0, #0x57]
    // 0x767698: DecompressPointer r1
    //     0x767698: add             x1, x1, HEAP, lsl #32
    // 0x76769c: cmp             w1, NULL
    // 0x7676a0: b.eq            #0x76784c
    // 0x7676a4: r0 = size()
    //     0x7676a4: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7676a8: LoadField: d0 = r0->field_f
    //     0x7676a8: ldur            d0, [x0, #0xf]
    // 0x7676ac: stur            d0, [fp, #-0x28]
    // 0x7676b0: r0 = Size()
    //     0x7676b0: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x7676b4: ldur            d0, [fp, #-0x20]
    // 0x7676b8: StoreField: r0->field_7 = d0
    //     0x7676b8: stur            d0, [x0, #7]
    // 0x7676bc: ldur            d0, [fp, #-0x28]
    // 0x7676c0: StoreField: r0->field_f = d0
    //     0x7676c0: stur            d0, [x0, #0xf]
    // 0x7676c4: ldur            x1, [fp, #-8]
    // 0x7676c8: mov             x2, x0
    // 0x7676cc: r0 = constrain()
    //     0x7676cc: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x7676d0: ldur            x3, [fp, #-0x18]
    // 0x7676d4: StoreField: r3->field_53 = r0
    //     0x7676d4: stur            w0, [x3, #0x53]
    //     0x7676d8: ldurb           w16, [x3, #-1]
    //     0x7676dc: ldurb           w17, [x0, #-1]
    //     0x7676e0: and             x16, x17, x16, lsr #2
    //     0x7676e4: tst             x16, HEAP, lsr #32
    //     0x7676e8: b.eq            #0x7676f0
    //     0x7676ec: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7676f0: LoadField: r0 = r3->field_57
    //     0x7676f0: ldur            w0, [x3, #0x57]
    // 0x7676f4: DecompressPointer r0
    //     0x7676f4: add             x0, x0, HEAP, lsl #32
    // 0x7676f8: cmp             w0, NULL
    // 0x7676fc: b.eq            #0x767850
    // 0x767700: LoadField: r4 = r0->field_7
    //     0x767700: ldur            w4, [x0, #7]
    // 0x767704: DecompressPointer r4
    //     0x767704: add             x4, x4, HEAP, lsl #32
    // 0x767708: stur            x4, [fp, #-8]
    // 0x76770c: cmp             w4, NULL
    // 0x767710: b.eq            #0x767854
    // 0x767714: mov             x0, x4
    // 0x767718: r2 = Null
    //     0x767718: mov             x2, NULL
    // 0x76771c: r1 = Null
    //     0x76771c: mov             x1, NULL
    // 0x767720: r4 = LoadClassIdInstr(r0)
    //     0x767720: ldur            x4, [x0, #-1]
    //     0x767724: ubfx            x4, x4, #0xc, #0x14
    // 0x767728: cmp             x4, #0xc77
    // 0x76772c: b.eq            #0x767744
    // 0x767730: r8 = ToolbarItemsParentData
    //     0x767730: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x767734: ldr             x8, [x8, #0x490]
    // 0x767738: r3 = Null
    //     0x767738: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b600] Null
    //     0x76773c: ldr             x3, [x3, #0x600]
    // 0x767740: r0 = DefaultTypeTest()
    //     0x767740: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x767744: ldur            x0, [fp, #-0x18]
    // 0x767748: LoadField: r1 = r0->field_63
    //     0x767748: ldur            w1, [x0, #0x63]
    // 0x76774c: DecompressPointer r1
    //     0x76774c: add             x1, x1, HEAP, lsl #32
    // 0x767750: r16 = Instance_TextDirection
    //     0x767750: ldr             x16, [PP, #0x2898]  ; [pp+0x2898] Obj!TextDirection@e392e1
    // 0x767754: cmp             w1, w16
    // 0x767758: b.ne            #0x767764
    // 0x76775c: d0 = 0.000000
    //     0x76775c: eor             v0.16b, v0.16b, v0.16b
    // 0x767760: b               #0x76779c
    // 0x767764: mov             x1, x0
    // 0x767768: r0 = size()
    //     0x767768: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76776c: LoadField: d0 = r0->field_7
    //     0x76776c: ldur            d0, [x0, #7]
    // 0x767770: ldur            x0, [fp, #-0x18]
    // 0x767774: stur            d0, [fp, #-0x20]
    // 0x767778: LoadField: r1 = r0->field_57
    //     0x767778: ldur            w1, [x0, #0x57]
    // 0x76777c: DecompressPointer r1
    //     0x76777c: add             x1, x1, HEAP, lsl #32
    // 0x767780: cmp             w1, NULL
    // 0x767784: b.eq            #0x767858
    // 0x767788: r0 = size()
    //     0x767788: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76778c: LoadField: d0 = r0->field_7
    //     0x76778c: ldur            d0, [x0, #7]
    // 0x767790: ldur            d1, [fp, #-0x20]
    // 0x767794: fsub            d2, d1, d0
    // 0x767798: mov             v0.16b, v2.16b
    // 0x76779c: ldur            x0, [fp, #-8]
    // 0x7677a0: stur            d0, [fp, #-0x20]
    // 0x7677a4: r0 = Offset()
    //     0x7677a4: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7677a8: ldur            d0, [fp, #-0x20]
    // 0x7677ac: StoreField: r0->field_7 = d0
    //     0x7677ac: stur            d0, [x0, #7]
    // 0x7677b0: StoreField: r0->field_f = rZR
    //     0x7677b0: stur            xzr, [x0, #0xf]
    // 0x7677b4: ldur            x1, [fp, #-8]
    // 0x7677b8: StoreField: r1->field_7 = r0
    //     0x7677b8: stur            w0, [x1, #7]
    //     0x7677bc: ldurb           w16, [x1, #-1]
    //     0x7677c0: ldurb           w17, [x0, #-1]
    //     0x7677c4: and             x16, x17, x16, lsr #2
    //     0x7677c8: tst             x16, HEAP, lsr #32
    //     0x7677cc: b.eq            #0x7677d4
    //     0x7677d0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7677d4: r0 = Null
    //     0x7677d4: mov             x0, NULL
    // 0x7677d8: LeaveFrame
    //     0x7677d8: mov             SP, fp
    //     0x7677dc: ldp             fp, lr, [SP], #0x10
    // 0x7677e0: ret
    //     0x7677e0: ret             
    // 0x7677e4: r0 = StateError()
    //     0x7677e4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x7677e8: mov             x1, x0
    // 0x7677ec: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x7677ec: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x7677f0: StoreField: r1->field_b = r0
    //     0x7677f0: stur            w0, [x1, #0xb]
    // 0x7677f4: mov             x0, x1
    // 0x7677f8: r0 = Throw()
    //     0x7677f8: bl              #0xec04b8  ; ThrowStub
    // 0x7677fc: brk             #0
    // 0x767800: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x767800: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x767804: r0 = StateError()
    //     0x767804: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x767808: mov             x1, x0
    // 0x76780c: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76780c: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x767810: StoreField: r1->field_b = r0
    //     0x767810: stur            w0, [x1, #0xb]
    // 0x767814: mov             x0, x1
    // 0x767818: r0 = Throw()
    //     0x767818: bl              #0xec04b8  ; ThrowStub
    // 0x76781c: brk             #0
    // 0x767820: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x767820: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x767824: b               #0x7674b4
    // 0x767828: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x767828: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76782c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76782c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x767830: SaveReg d0
    //     0x767830: str             q0, [SP, #-0x10]!
    // 0x767834: r0 = AllocateDouble()
    //     0x767834: bl              #0xec2254  ; AllocateDoubleStub
    // 0x767838: RestoreReg d0
    //     0x767838: ldr             q0, [SP], #0x10
    // 0x76783c: b               #0x7675a8
    // 0x767840: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x767840: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x767844: r0 = NullCastErrorSharedWithFPURegs()
    //     0x767844: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x767848: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x767848: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x76784c: r0 = NullCastErrorSharedWithFPURegs()
    //     0x76784c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x767850: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x767850: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x767854: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x767854: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x767858: r0 = NullCastErrorSharedWithFPURegs()
    //     0x767858: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ setupParentData(/* No info */) {
    // ** addr: 0x7879d0, size: 0xb8
    // 0x7879d0: EnterFrame
    //     0x7879d0: stp             fp, lr, [SP, #-0x10]!
    //     0x7879d4: mov             fp, SP
    // 0x7879d8: AllocStack(0x8)
    //     0x7879d8: sub             SP, SP, #8
    // 0x7879dc: SetupParameters(_TextSelectionToolbarTrailingEdgeAlignRenderBox this /* r1 => r4 */, dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x7879dc: mov             x0, x2
    //     0x7879e0: mov             x4, x1
    //     0x7879e4: mov             x3, x2
    //     0x7879e8: stur            x2, [fp, #-8]
    // 0x7879ec: r2 = Null
    //     0x7879ec: mov             x2, NULL
    // 0x7879f0: r1 = Null
    //     0x7879f0: mov             x1, NULL
    // 0x7879f4: r4 = 60
    //     0x7879f4: movz            x4, #0x3c
    // 0x7879f8: branchIfSmi(r0, 0x787a04)
    //     0x7879f8: tbz             w0, #0, #0x787a04
    // 0x7879fc: r4 = LoadClassIdInstr(r0)
    //     0x7879fc: ldur            x4, [x0, #-1]
    //     0x787a00: ubfx            x4, x4, #0xc, #0x14
    // 0x787a04: sub             x4, x4, #0xbba
    // 0x787a08: cmp             x4, #0x9a
    // 0x787a0c: b.ls            #0x787a20
    // 0x787a10: r8 = RenderBox
    //     0x787a10: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x787a14: r3 = Null
    //     0x787a14: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b5a8] Null
    //     0x787a18: ldr             x3, [x3, #0x5a8]
    // 0x787a1c: r0 = RenderBox()
    //     0x787a1c: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x787a20: ldur            x0, [fp, #-8]
    // 0x787a24: LoadField: r1 = r0->field_7
    //     0x787a24: ldur            w1, [x0, #7]
    // 0x787a28: DecompressPointer r1
    //     0x787a28: add             x1, x1, HEAP, lsl #32
    // 0x787a2c: r2 = LoadClassIdInstr(r1)
    //     0x787a2c: ldur            x2, [x1, #-1]
    //     0x787a30: ubfx            x2, x2, #0xc, #0x14
    // 0x787a34: cmp             x2, #0xc77
    // 0x787a38: b.eq            #0x787a78
    // 0x787a3c: r1 = <RenderBox>
    //     0x787a3c: add             x1, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x787a40: ldr             x1, [x1, #0x1d8]
    // 0x787a44: r0 = ToolbarItemsParentData()
    //     0x787a44: bl              #0x787a88  ; AllocateToolbarItemsParentDataStub -> ToolbarItemsParentData (size=0x1c)
    // 0x787a48: r1 = false
    //     0x787a48: add             x1, NULL, #0x30  ; false
    // 0x787a4c: ArrayStore: r0[0] = r1  ; List_4
    //     0x787a4c: stur            w1, [x0, #0x17]
    // 0x787a50: r1 = Instance_Offset
    //     0x787a50: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x787a54: StoreField: r0->field_7 = r1
    //     0x787a54: stur            w1, [x0, #7]
    // 0x787a58: ldur            x1, [fp, #-8]
    // 0x787a5c: StoreField: r1->field_7 = r0
    //     0x787a5c: stur            w0, [x1, #7]
    //     0x787a60: ldurb           w16, [x1, #-1]
    //     0x787a64: ldurb           w17, [x0, #-1]
    //     0x787a68: and             x16, x17, x16, lsr #2
    //     0x787a6c: tst             x16, HEAP, lsr #32
    //     0x787a70: b.eq            #0x787a78
    //     0x787a74: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x787a78: r0 = Null
    //     0x787a78: mov             x0, NULL
    // 0x787a7c: LeaveFrame
    //     0x787a7c: mov             SP, fp
    //     0x787a80: ldp             fp, lr, [SP], #0x10
    // 0x787a84: ret
    //     0x787a84: ret             
  }
  _ paint(/* No info */) {
    // ** addr: 0x78ba80, size: 0xc0
    // 0x78ba80: EnterFrame
    //     0x78ba80: stp             fp, lr, [SP, #-0x10]!
    //     0x78ba84: mov             fp, SP
    // 0x78ba88: AllocStack(0x20)
    //     0x78ba88: sub             SP, SP, #0x20
    // 0x78ba8c: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x78ba8c: mov             x4, x2
    //     0x78ba90: stur            x2, [fp, #-0x18]
    //     0x78ba94: stur            x3, [fp, #-0x20]
    // 0x78ba98: CheckStackOverflow
    //     0x78ba98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x78ba9c: cmp             SP, x16
    //     0x78baa0: b.ls            #0x78bb30
    // 0x78baa4: LoadField: r5 = r1->field_57
    //     0x78baa4: ldur            w5, [x1, #0x57]
    // 0x78baa8: DecompressPointer r5
    //     0x78baa8: add             x5, x5, HEAP, lsl #32
    // 0x78baac: stur            x5, [fp, #-0x10]
    // 0x78bab0: cmp             w5, NULL
    // 0x78bab4: b.eq            #0x78bb38
    // 0x78bab8: LoadField: r6 = r5->field_7
    //     0x78bab8: ldur            w6, [x5, #7]
    // 0x78babc: DecompressPointer r6
    //     0x78babc: add             x6, x6, HEAP, lsl #32
    // 0x78bac0: stur            x6, [fp, #-8]
    // 0x78bac4: cmp             w6, NULL
    // 0x78bac8: b.eq            #0x78bb3c
    // 0x78bacc: mov             x0, x6
    // 0x78bad0: r2 = Null
    //     0x78bad0: mov             x2, NULL
    // 0x78bad4: r1 = Null
    //     0x78bad4: mov             x1, NULL
    // 0x78bad8: r4 = LoadClassIdInstr(r0)
    //     0x78bad8: ldur            x4, [x0, #-1]
    //     0x78badc: ubfx            x4, x4, #0xc, #0x14
    // 0x78bae0: cmp             x4, #0xc77
    // 0x78bae4: b.eq            #0x78bafc
    // 0x78bae8: r8 = ToolbarItemsParentData
    //     0x78bae8: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x78baec: ldr             x8, [x8, #0x490]
    // 0x78baf0: r3 = Null
    //     0x78baf0: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b5d0] Null
    //     0x78baf4: ldr             x3, [x3, #0x5d0]
    // 0x78baf8: r0 = DefaultTypeTest()
    //     0x78baf8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x78bafc: ldur            x0, [fp, #-8]
    // 0x78bb00: LoadField: r1 = r0->field_7
    //     0x78bb00: ldur            w1, [x0, #7]
    // 0x78bb04: DecompressPointer r1
    //     0x78bb04: add             x1, x1, HEAP, lsl #32
    // 0x78bb08: ldur            x2, [fp, #-0x20]
    // 0x78bb0c: r0 = +()
    //     0x78bb0c: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x78bb10: ldur            x1, [fp, #-0x18]
    // 0x78bb14: ldur            x2, [fp, #-0x10]
    // 0x78bb18: mov             x3, x0
    // 0x78bb1c: r0 = paintChild()
    //     0x78bb1c: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x78bb20: r0 = Null
    //     0x78bb20: mov             x0, NULL
    // 0x78bb24: LeaveFrame
    //     0x78bb24: mov             SP, fp
    //     0x78bb28: ldp             fp, lr, [SP], #0x10
    // 0x78bb2c: ret
    //     0x78bb2c: ret             
    // 0x78bb30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x78bb30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x78bb34: b               #0x78baa4
    // 0x78bb38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x78bb38: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x78bb3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x78bb3c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x7fc10c, size: 0xec
    // 0x7fc10c: EnterFrame
    //     0x7fc10c: stp             fp, lr, [SP, #-0x10]!
    //     0x7fc110: mov             fp, SP
    // 0x7fc114: AllocStack(0x28)
    //     0x7fc114: sub             SP, SP, #0x28
    // 0x7fc118: SetupParameters(_TextSelectionToolbarTrailingEdgeAlignRenderBox this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */)
    //     0x7fc118: mov             x0, x1
    //     0x7fc11c: stur            x1, [fp, #-8]
    //     0x7fc120: mov             x1, x2
    //     0x7fc124: mov             x5, x3
    //     0x7fc128: stur            x2, [fp, #-0x10]
    //     0x7fc12c: stur            x3, [fp, #-0x18]
    // 0x7fc130: CheckStackOverflow
    //     0x7fc130: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fc134: cmp             SP, x16
    //     0x7fc138: b.ls            #0x7fc1e8
    // 0x7fc13c: r1 = 1
    //     0x7fc13c: movz            x1, #0x1
    // 0x7fc140: r0 = AllocateContext()
    //     0x7fc140: bl              #0xec126c  ; AllocateContextStub
    // 0x7fc144: mov             x3, x0
    // 0x7fc148: ldur            x0, [fp, #-8]
    // 0x7fc14c: stur            x3, [fp, #-0x20]
    // 0x7fc150: StoreField: r3->field_f = r0
    //     0x7fc150: stur            w0, [x3, #0xf]
    // 0x7fc154: LoadField: r1 = r0->field_57
    //     0x7fc154: ldur            w1, [x0, #0x57]
    // 0x7fc158: DecompressPointer r1
    //     0x7fc158: add             x1, x1, HEAP, lsl #32
    // 0x7fc15c: cmp             w1, NULL
    // 0x7fc160: b.eq            #0x7fc1f0
    // 0x7fc164: LoadField: r4 = r1->field_7
    //     0x7fc164: ldur            w4, [x1, #7]
    // 0x7fc168: DecompressPointer r4
    //     0x7fc168: add             x4, x4, HEAP, lsl #32
    // 0x7fc16c: stur            x4, [fp, #-8]
    // 0x7fc170: cmp             w4, NULL
    // 0x7fc174: b.eq            #0x7fc1f4
    // 0x7fc178: mov             x0, x4
    // 0x7fc17c: r2 = Null
    //     0x7fc17c: mov             x2, NULL
    // 0x7fc180: r1 = Null
    //     0x7fc180: mov             x1, NULL
    // 0x7fc184: r4 = LoadClassIdInstr(r0)
    //     0x7fc184: ldur            x4, [x0, #-1]
    //     0x7fc188: ubfx            x4, x4, #0xc, #0x14
    // 0x7fc18c: cmp             x4, #0xc77
    // 0x7fc190: b.eq            #0x7fc1a8
    // 0x7fc194: r8 = ToolbarItemsParentData
    //     0x7fc194: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x7fc198: ldr             x8, [x8, #0x490]
    // 0x7fc19c: r3 = Null
    //     0x7fc19c: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b5b8] Null
    //     0x7fc1a0: ldr             x3, [x3, #0x5b8]
    // 0x7fc1a4: r0 = DefaultTypeTest()
    //     0x7fc1a4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7fc1a8: ldur            x0, [fp, #-8]
    // 0x7fc1ac: LoadField: r3 = r0->field_7
    //     0x7fc1ac: ldur            w3, [x0, #7]
    // 0x7fc1b0: DecompressPointer r3
    //     0x7fc1b0: add             x3, x3, HEAP, lsl #32
    // 0x7fc1b4: ldur            x2, [fp, #-0x20]
    // 0x7fc1b8: stur            x3, [fp, #-0x28]
    // 0x7fc1bc: r1 = Function '<anonymous closure>':.
    //     0x7fc1bc: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5b5c8] AnonymousClosure: (0x7fc2c8), in [package:flutter/src/widgets/single_child_scroll_view.dart] _RenderSingleChildViewport::hitTestChildren (0x7fceb4)
    //     0x7fc1c0: ldr             x1, [x1, #0x5c8]
    // 0x7fc1c4: r0 = AllocateClosure()
    //     0x7fc1c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7fc1c8: ldur            x1, [fp, #-0x10]
    // 0x7fc1cc: mov             x2, x0
    // 0x7fc1d0: ldur            x3, [fp, #-0x28]
    // 0x7fc1d4: ldur            x5, [fp, #-0x18]
    // 0x7fc1d8: r0 = addWithPaintOffset()
    //     0x7fc1d8: bl              #0x7fc1f8  ; [package:flutter/src/rendering/box.dart] BoxHitTestResult::addWithPaintOffset
    // 0x7fc1dc: LeaveFrame
    //     0x7fc1dc: mov             SP, fp
    //     0x7fc1e0: ldp             fp, lr, [SP], #0x10
    // 0x7fc1e4: ret
    //     0x7fc1e4: ret             
    // 0x7fc1e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fc1e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fc1ec: b               #0x7fc13c
    // 0x7fc1f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fc1f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7fc1f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fc1f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ applyPaintTransform(/* No info */) {
    // ** addr: 0x80417c, size: 0x98
    // 0x80417c: EnterFrame
    //     0x80417c: stp             fp, lr, [SP, #-0x10]!
    //     0x804180: mov             fp, SP
    // 0x804184: AllocStack(0x10)
    //     0x804184: sub             SP, SP, #0x10
    // 0x804188: SetupParameters(dynamic _ /* r3 => r3, fp-0x10 */)
    //     0x804188: stur            x3, [fp, #-0x10]
    // 0x80418c: CheckStackOverflow
    //     0x80418c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x804190: cmp             SP, x16
    //     0x804194: b.ls            #0x804208
    // 0x804198: LoadField: r4 = r2->field_7
    //     0x804198: ldur            w4, [x2, #7]
    // 0x80419c: DecompressPointer r4
    //     0x80419c: add             x4, x4, HEAP, lsl #32
    // 0x8041a0: stur            x4, [fp, #-8]
    // 0x8041a4: cmp             w4, NULL
    // 0x8041a8: b.eq            #0x804210
    // 0x8041ac: mov             x0, x4
    // 0x8041b0: r2 = Null
    //     0x8041b0: mov             x2, NULL
    // 0x8041b4: r1 = Null
    //     0x8041b4: mov             x1, NULL
    // 0x8041b8: r4 = LoadClassIdInstr(r0)
    //     0x8041b8: ldur            x4, [x0, #-1]
    //     0x8041bc: ubfx            x4, x4, #0xc, #0x14
    // 0x8041c0: cmp             x4, #0xc77
    // 0x8041c4: b.eq            #0x8041dc
    // 0x8041c8: r8 = ToolbarItemsParentData
    //     0x8041c8: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b490] Type: ToolbarItemsParentData
    //     0x8041cc: ldr             x8, [x8, #0x490]
    // 0x8041d0: r3 = Null
    //     0x8041d0: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b598] Null
    //     0x8041d4: ldr             x3, [x3, #0x598]
    // 0x8041d8: r0 = DefaultTypeTest()
    //     0x8041d8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8041dc: ldur            x0, [fp, #-8]
    // 0x8041e0: LoadField: r1 = r0->field_7
    //     0x8041e0: ldur            w1, [x0, #7]
    // 0x8041e4: DecompressPointer r1
    //     0x8041e4: add             x1, x1, HEAP, lsl #32
    // 0x8041e8: LoadField: d0 = r1->field_7
    //     0x8041e8: ldur            d0, [x1, #7]
    // 0x8041ec: LoadField: d1 = r1->field_f
    //     0x8041ec: ldur            d1, [x1, #0xf]
    // 0x8041f0: ldur            x1, [fp, #-0x10]
    // 0x8041f4: r0 = translate()
    //     0x8041f4: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0x8041f8: r0 = Null
    //     0x8041f8: mov             x0, NULL
    // 0x8041fc: LeaveFrame
    //     0x8041fc: mov             SP, fp
    //     0x804200: ldp             fp, lr, [SP], #0x10
    // 0x804204: ret
    //     0x804204: ret             
    // 0x804208: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x804208: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80420c: b               #0x804198
    // 0x804210: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x804210: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _TextSelectionToolbarTrailingEdgeAlignRenderBox(/* No info */) {
    // ** addr: 0x85a46c, size: 0x90
    // 0x85a46c: EnterFrame
    //     0x85a46c: stp             fp, lr, [SP, #-0x10]!
    //     0x85a470: mov             fp, SP
    // 0x85a474: AllocStack(0x8)
    //     0x85a474: sub             SP, SP, #8
    // 0x85a478: SetupParameters(_TextSelectionToolbarTrailingEdgeAlignRenderBox this /* r1 => r1, fp-0x8 */, dynamic _ /* r3 => r0 */)
    //     0x85a478: mov             x0, x3
    //     0x85a47c: stur            x1, [fp, #-8]
    // 0x85a480: CheckStackOverflow
    //     0x85a480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85a484: cmp             SP, x16
    //     0x85a488: b.ls            #0x85a4f4
    // 0x85a48c: StoreField: r1->field_63 = r0
    //     0x85a48c: stur            w0, [x1, #0x63]
    //     0x85a490: ldurb           w16, [x1, #-1]
    //     0x85a494: ldurb           w17, [x0, #-1]
    //     0x85a498: and             x16, x17, x16, lsr #2
    //     0x85a49c: tst             x16, HEAP, lsr #32
    //     0x85a4a0: b.eq            #0x85a4a8
    //     0x85a4a4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x85a4a8: StoreField: r1->field_5f = r2
    //     0x85a4a8: stur            w2, [x1, #0x5f]
    // 0x85a4ac: r0 = _LayoutCacheStorage()
    //     0x85a4ac: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x85a4b0: ldur            x2, [fp, #-8]
    // 0x85a4b4: StoreField: r2->field_4f = r0
    //     0x85a4b4: stur            w0, [x2, #0x4f]
    //     0x85a4b8: ldurb           w16, [x2, #-1]
    //     0x85a4bc: ldurb           w17, [x0, #-1]
    //     0x85a4c0: and             x16, x17, x16, lsr #2
    //     0x85a4c4: tst             x16, HEAP, lsr #32
    //     0x85a4c8: b.eq            #0x85a4d0
    //     0x85a4cc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x85a4d0: mov             x1, x2
    // 0x85a4d4: r0 = RenderObject()
    //     0x85a4d4: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x85a4d8: ldur            x1, [fp, #-8]
    // 0x85a4dc: r2 = Null
    //     0x85a4dc: mov             x2, NULL
    // 0x85a4e0: r0 = child=()
    //     0x85a4e0: bl              #0x895c88  ; [package:flutter/src/rendering/shifted_box.dart] _RenderShiftedBox&RenderBox&RenderObjectWithChildMixin::child=
    // 0x85a4e4: r0 = Null
    //     0x85a4e4: mov             x0, NULL
    // 0x85a4e8: LeaveFrame
    //     0x85a4e8: mov             SP, fp
    //     0x85a4ec: ldp             fp, lr, [SP], #0x10
    // 0x85a4f0: ret
    //     0x85a4f0: ret             
    // 0x85a4f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85a4f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85a4f8: b               #0x85a48c
  }
  set _ textDirection=(/* No info */) {
    // ** addr: 0xc6d74c, size: 0x70
    // 0xc6d74c: EnterFrame
    //     0xc6d74c: stp             fp, lr, [SP, #-0x10]!
    //     0xc6d750: mov             fp, SP
    // 0xc6d754: mov             x0, x2
    // 0xc6d758: CheckStackOverflow
    //     0xc6d758: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6d75c: cmp             SP, x16
    //     0xc6d760: b.ls            #0xc6d7b4
    // 0xc6d764: LoadField: r2 = r1->field_63
    //     0xc6d764: ldur            w2, [x1, #0x63]
    // 0xc6d768: DecompressPointer r2
    //     0xc6d768: add             x2, x2, HEAP, lsl #32
    // 0xc6d76c: cmp             w0, w2
    // 0xc6d770: b.ne            #0xc6d784
    // 0xc6d774: r0 = Null
    //     0xc6d774: mov             x0, NULL
    // 0xc6d778: LeaveFrame
    //     0xc6d778: mov             SP, fp
    //     0xc6d77c: ldp             fp, lr, [SP], #0x10
    // 0xc6d780: ret
    //     0xc6d780: ret             
    // 0xc6d784: StoreField: r1->field_63 = r0
    //     0xc6d784: stur            w0, [x1, #0x63]
    //     0xc6d788: ldurb           w16, [x1, #-1]
    //     0xc6d78c: ldurb           w17, [x0, #-1]
    //     0xc6d790: and             x16, x17, x16, lsr #2
    //     0xc6d794: tst             x16, HEAP, lsr #32
    //     0xc6d798: b.eq            #0xc6d7a0
    //     0xc6d79c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6d7a0: r0 = markNeedsLayout()
    //     0xc6d7a0: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6d7a4: r0 = Null
    //     0xc6d7a4: mov             x0, NULL
    // 0xc6d7a8: LeaveFrame
    //     0xc6d7a8: mov             SP, fp
    //     0xc6d7ac: ldp             fp, lr, [SP], #0x10
    // 0xc6d7b0: ret
    //     0xc6d7b0: ret             
    // 0xc6d7b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6d7b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6d7b8: b               #0xc6d764
  }
  set _ overflowOpen=(/* No info */) {
    // ** addr: 0xc6d7bc, size: 0x54
    // 0xc6d7bc: EnterFrame
    //     0xc6d7bc: stp             fp, lr, [SP, #-0x10]!
    //     0xc6d7c0: mov             fp, SP
    // 0xc6d7c4: CheckStackOverflow
    //     0xc6d7c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6d7c8: cmp             SP, x16
    //     0xc6d7cc: b.ls            #0xc6d808
    // 0xc6d7d0: LoadField: r0 = r1->field_5f
    //     0xc6d7d0: ldur            w0, [x1, #0x5f]
    // 0xc6d7d4: DecompressPointer r0
    //     0xc6d7d4: add             x0, x0, HEAP, lsl #32
    // 0xc6d7d8: cmp             w2, w0
    // 0xc6d7dc: b.ne            #0xc6d7f0
    // 0xc6d7e0: r0 = Null
    //     0xc6d7e0: mov             x0, NULL
    // 0xc6d7e4: LeaveFrame
    //     0xc6d7e4: mov             SP, fp
    //     0xc6d7e8: ldp             fp, lr, [SP], #0x10
    // 0xc6d7ec: ret
    //     0xc6d7ec: ret             
    // 0xc6d7f0: StoreField: r1->field_5f = r2
    //     0xc6d7f0: stur            w2, [x1, #0x5f]
    // 0xc6d7f4: r0 = markNeedsLayout()
    //     0xc6d7f4: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6d7f8: r0 = Null
    //     0xc6d7f8: mov             x0, NULL
    // 0xc6d7fc: LeaveFrame
    //     0xc6d7fc: mov             SP, fp
    //     0xc6d800: ldp             fp, lr, [SP], #0x10
    // 0xc6d804: ret
    //     0xc6d804: ret             
    // 0xc6d808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6d808: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6d80c: b               #0xc6d7d0
  }
}

// class id: 4248, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __TextSelectionToolbarOverflowableState&State&TickerProviderStateMixin extends State<dynamic>
     with TickerProviderStateMixin<X0 bound StatefulWidget> {

  _ dispose(/* No info */) {
    // ** addr: 0xa7ee94, size: 0x94
    // 0xa7ee94: EnterFrame
    //     0xa7ee94: stp             fp, lr, [SP, #-0x10]!
    //     0xa7ee98: mov             fp, SP
    // 0xa7ee9c: AllocStack(0x10)
    //     0xa7ee9c: sub             SP, SP, #0x10
    // 0xa7eea0: SetupParameters(__TextSelectionToolbarOverflowableState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa7eea0: mov             x0, x1
    //     0xa7eea4: stur            x1, [fp, #-0x10]
    // 0xa7eea8: CheckStackOverflow
    //     0xa7eea8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7eeac: cmp             SP, x16
    //     0xa7eeb0: b.ls            #0xa7ef20
    // 0xa7eeb4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa7eeb4: ldur            w3, [x0, #0x17]
    // 0xa7eeb8: DecompressPointer r3
    //     0xa7eeb8: add             x3, x3, HEAP, lsl #32
    // 0xa7eebc: stur            x3, [fp, #-8]
    // 0xa7eec0: cmp             w3, NULL
    // 0xa7eec4: b.ne            #0xa7eed0
    // 0xa7eec8: mov             x1, x0
    // 0xa7eecc: b               #0xa7ef0c
    // 0xa7eed0: mov             x2, x0
    // 0xa7eed4: r1 = Function '_updateTickers@364311458':.
    //     0xa7eed4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54858] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa7eed8: ldr             x1, [x1, #0x858]
    // 0xa7eedc: r0 = AllocateClosure()
    //     0xa7eedc: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7eee0: ldur            x1, [fp, #-8]
    // 0xa7eee4: r2 = LoadClassIdInstr(r1)
    //     0xa7eee4: ldur            x2, [x1, #-1]
    //     0xa7eee8: ubfx            x2, x2, #0xc, #0x14
    // 0xa7eeec: mov             x16, x0
    // 0xa7eef0: mov             x0, x2
    // 0xa7eef4: mov             x2, x16
    // 0xa7eef8: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa7eef8: movz            x17, #0xbf5c
    //     0xa7eefc: add             lr, x0, x17
    //     0xa7ef00: ldr             lr, [x21, lr, lsl #3]
    //     0xa7ef04: blr             lr
    // 0xa7ef08: ldur            x1, [fp, #-0x10]
    // 0xa7ef0c: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa7ef0c: stur            NULL, [x1, #0x17]
    // 0xa7ef10: r0 = Null
    //     0xa7ef10: mov             x0, NULL
    // 0xa7ef14: LeaveFrame
    //     0xa7ef14: mov             SP, fp
    //     0xa7ef18: ldp             fp, lr, [SP], #0x10
    // 0xa7ef1c: ret
    //     0xa7ef1c: ret             
    // 0xa7ef20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7ef20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7ef24: b               #0xa7eeb4
  }
  _ activate(/* No info */) {
    // ** addr: 0xa85338, size: 0x30
    // 0xa85338: EnterFrame
    //     0xa85338: stp             fp, lr, [SP, #-0x10]!
    //     0xa8533c: mov             fp, SP
    // 0xa85340: CheckStackOverflow
    //     0xa85340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85344: cmp             SP, x16
    //     0xa85348: b.ls            #0xa85360
    // 0xa8534c: r0 = _updateTickerModeNotifier()
    //     0xa8534c: bl              #0xa85368  ; [package:flutter/src/material/text_selection_toolbar.dart] __TextSelectionToolbarOverflowableState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa85350: r0 = Null
    //     0xa85350: mov             x0, NULL
    // 0xa85354: LeaveFrame
    //     0xa85354: mov             SP, fp
    //     0xa85358: ldp             fp, lr, [SP], #0x10
    // 0xa8535c: ret
    //     0xa8535c: ret             
    // 0xa85360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85360: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85364: b               #0xa8534c
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0xa85368, size: 0x124
    // 0xa85368: EnterFrame
    //     0xa85368: stp             fp, lr, [SP, #-0x10]!
    //     0xa8536c: mov             fp, SP
    // 0xa85370: AllocStack(0x18)
    //     0xa85370: sub             SP, SP, #0x18
    // 0xa85374: SetupParameters(__TextSelectionToolbarOverflowableState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0xa85374: mov             x2, x1
    //     0xa85378: stur            x1, [fp, #-8]
    // 0xa8537c: CheckStackOverflow
    //     0xa8537c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85380: cmp             SP, x16
    //     0xa85384: b.ls            #0xa85480
    // 0xa85388: LoadField: r1 = r2->field_f
    //     0xa85388: ldur            w1, [x2, #0xf]
    // 0xa8538c: DecompressPointer r1
    //     0xa8538c: add             x1, x1, HEAP, lsl #32
    // 0xa85390: cmp             w1, NULL
    // 0xa85394: b.eq            #0xa85488
    // 0xa85398: r0 = getNotifier()
    //     0xa85398: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0xa8539c: mov             x3, x0
    // 0xa853a0: ldur            x0, [fp, #-8]
    // 0xa853a4: stur            x3, [fp, #-0x18]
    // 0xa853a8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa853a8: ldur            w4, [x0, #0x17]
    // 0xa853ac: DecompressPointer r4
    //     0xa853ac: add             x4, x4, HEAP, lsl #32
    // 0xa853b0: stur            x4, [fp, #-0x10]
    // 0xa853b4: cmp             w3, w4
    // 0xa853b8: b.ne            #0xa853cc
    // 0xa853bc: r0 = Null
    //     0xa853bc: mov             x0, NULL
    // 0xa853c0: LeaveFrame
    //     0xa853c0: mov             SP, fp
    //     0xa853c4: ldp             fp, lr, [SP], #0x10
    // 0xa853c8: ret
    //     0xa853c8: ret             
    // 0xa853cc: cmp             w4, NULL
    // 0xa853d0: b.eq            #0xa85414
    // 0xa853d4: mov             x2, x0
    // 0xa853d8: r1 = Function '_updateTickers@364311458':.
    //     0xa853d8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54858] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa853dc: ldr             x1, [x1, #0x858]
    // 0xa853e0: r0 = AllocateClosure()
    //     0xa853e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa853e4: ldur            x1, [fp, #-0x10]
    // 0xa853e8: r2 = LoadClassIdInstr(r1)
    //     0xa853e8: ldur            x2, [x1, #-1]
    //     0xa853ec: ubfx            x2, x2, #0xc, #0x14
    // 0xa853f0: mov             x16, x0
    // 0xa853f4: mov             x0, x2
    // 0xa853f8: mov             x2, x16
    // 0xa853fc: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa853fc: movz            x17, #0xbf5c
    //     0xa85400: add             lr, x0, x17
    //     0xa85404: ldr             lr, [x21, lr, lsl #3]
    //     0xa85408: blr             lr
    // 0xa8540c: ldur            x0, [fp, #-8]
    // 0xa85410: ldur            x3, [fp, #-0x18]
    // 0xa85414: mov             x2, x0
    // 0xa85418: r1 = Function '_updateTickers@364311458':.
    //     0xa85418: add             x1, PP, #0x54, lsl #12  ; [pp+0x54858] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa8541c: ldr             x1, [x1, #0x858]
    // 0xa85420: r0 = AllocateClosure()
    //     0xa85420: bl              #0xec1630  ; AllocateClosureStub
    // 0xa85424: ldur            x3, [fp, #-0x18]
    // 0xa85428: r1 = LoadClassIdInstr(r3)
    //     0xa85428: ldur            x1, [x3, #-1]
    //     0xa8542c: ubfx            x1, x1, #0xc, #0x14
    // 0xa85430: mov             x2, x0
    // 0xa85434: mov             x0, x1
    // 0xa85438: mov             x1, x3
    // 0xa8543c: r0 = GDT[cid_x0 + 0xc407]()
    //     0xa8543c: movz            x17, #0xc407
    //     0xa85440: add             lr, x0, x17
    //     0xa85444: ldr             lr, [x21, lr, lsl #3]
    //     0xa85448: blr             lr
    // 0xa8544c: ldur            x0, [fp, #-0x18]
    // 0xa85450: ldur            x1, [fp, #-8]
    // 0xa85454: ArrayStore: r1[0] = r0  ; List_4
    //     0xa85454: stur            w0, [x1, #0x17]
    //     0xa85458: ldurb           w16, [x1, #-1]
    //     0xa8545c: ldurb           w17, [x0, #-1]
    //     0xa85460: and             x16, x17, x16, lsr #2
    //     0xa85464: tst             x16, HEAP, lsr #32
    //     0xa85468: b.eq            #0xa85470
    //     0xa8546c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa85470: r0 = Null
    //     0xa85470: mov             x0, NULL
    // 0xa85474: LeaveFrame
    //     0xa85474: mov             SP, fp
    //     0xa85478: ldp             fp, lr, [SP], #0x10
    // 0xa8547c: ret
    //     0xa8547c: ret             
    // 0xa85480: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85480: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85484: b               #0xa85388
    // 0xa85488: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa85488: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4249, size: 0x24, field offset: 0x1c
class _TextSelectionToolbarOverflowableState extends __TextSelectionToolbarOverflowableState&State&TickerProviderStateMixin {

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x98d5ac, size: 0x104
    // 0x98d5ac: EnterFrame
    //     0x98d5ac: stp             fp, lr, [SP, #-0x10]!
    //     0x98d5b0: mov             fp, SP
    // 0x98d5b4: AllocStack(0x28)
    //     0x98d5b4: sub             SP, SP, #0x28
    // 0x98d5b8: SetupParameters(_TextSelectionToolbarOverflowableState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x98d5b8: mov             x4, x1
    //     0x98d5bc: mov             x3, x2
    //     0x98d5c0: stur            x1, [fp, #-8]
    //     0x98d5c4: stur            x2, [fp, #-0x10]
    // 0x98d5c8: CheckStackOverflow
    //     0x98d5c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98d5cc: cmp             SP, x16
    //     0x98d5d0: b.ls            #0x98d6a4
    // 0x98d5d4: mov             x0, x3
    // 0x98d5d8: r2 = Null
    //     0x98d5d8: mov             x2, NULL
    // 0x98d5dc: r1 = Null
    //     0x98d5dc: mov             x1, NULL
    // 0x98d5e0: r4 = 60
    //     0x98d5e0: movz            x4, #0x3c
    // 0x98d5e4: branchIfSmi(r0, 0x98d5f0)
    //     0x98d5e4: tbz             w0, #0, #0x98d5f0
    // 0x98d5e8: r4 = LoadClassIdInstr(r0)
    //     0x98d5e8: ldur            x4, [x0, #-1]
    //     0x98d5ec: ubfx            x4, x4, #0xc, #0x14
    // 0x98d5f0: r17 = 4806
    //     0x98d5f0: movz            x17, #0x12c6
    // 0x98d5f4: cmp             x4, x17
    // 0x98d5f8: b.eq            #0x98d610
    // 0x98d5fc: r8 = _TextSelectionToolbarOverflowable
    //     0x98d5fc: add             x8, PP, #0x54, lsl #12  ; [pp+0x54830] Type: _TextSelectionToolbarOverflowable
    //     0x98d600: ldr             x8, [x8, #0x830]
    // 0x98d604: r3 = Null
    //     0x98d604: add             x3, PP, #0x54, lsl #12  ; [pp+0x54838] Null
    //     0x98d608: ldr             x3, [x3, #0x838]
    // 0x98d60c: r0 = _TextSelectionToolbarOverflowable()
    //     0x98d60c: bl              #0x98d6fc  ; IsType__TextSelectionToolbarOverflowable_Stub
    // 0x98d610: ldur            x3, [fp, #-8]
    // 0x98d614: LoadField: r2 = r3->field_7
    //     0x98d614: ldur            w2, [x3, #7]
    // 0x98d618: DecompressPointer r2
    //     0x98d618: add             x2, x2, HEAP, lsl #32
    // 0x98d61c: ldur            x0, [fp, #-0x10]
    // 0x98d620: r1 = Null
    //     0x98d620: mov             x1, NULL
    // 0x98d624: cmp             w2, NULL
    // 0x98d628: b.eq            #0x98d64c
    // 0x98d62c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x98d62c: ldur            w4, [x2, #0x17]
    // 0x98d630: DecompressPointer r4
    //     0x98d630: add             x4, x4, HEAP, lsl #32
    // 0x98d634: r8 = X0 bound StatefulWidget
    //     0x98d634: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x98d638: ldr             x8, [x8, #0x7f8]
    // 0x98d63c: LoadField: r9 = r4->field_7
    //     0x98d63c: ldur            x9, [x4, #7]
    // 0x98d640: r3 = Null
    //     0x98d640: add             x3, PP, #0x54, lsl #12  ; [pp+0x54848] Null
    //     0x98d644: ldr             x3, [x3, #0x848]
    // 0x98d648: blr             x9
    // 0x98d64c: ldur            x1, [fp, #-8]
    // 0x98d650: LoadField: r0 = r1->field_b
    //     0x98d650: ldur            w0, [x1, #0xb]
    // 0x98d654: DecompressPointer r0
    //     0x98d654: add             x0, x0, HEAP, lsl #32
    // 0x98d658: cmp             w0, NULL
    // 0x98d65c: b.eq            #0x98d6ac
    // 0x98d660: LoadField: r2 = r0->field_b
    //     0x98d660: ldur            w2, [x0, #0xb]
    // 0x98d664: DecompressPointer r2
    //     0x98d664: add             x2, x2, HEAP, lsl #32
    // 0x98d668: ldur            x0, [fp, #-0x10]
    // 0x98d66c: LoadField: r3 = r0->field_b
    //     0x98d66c: ldur            w3, [x0, #0xb]
    // 0x98d670: DecompressPointer r3
    //     0x98d670: add             x3, x3, HEAP, lsl #32
    // 0x98d674: r16 = <Widget>
    //     0x98d674: ldr             x16, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0x98d678: stp             x2, x16, [SP, #8]
    // 0x98d67c: str             x3, [SP]
    // 0x98d680: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x98d680: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x98d684: r0 = listEquals()
    //     0x98d684: bl              #0x64d5e4  ; [package:flutter/src/foundation/collections.dart] ::listEquals
    // 0x98d688: tbz             w0, #4, #0x98d694
    // 0x98d68c: ldur            x1, [fp, #-8]
    // 0x98d690: r0 = _reset()
    //     0x98d690: bl              #0x98d6b0  ; [package:flutter/src/material/text_selection_toolbar.dart] _TextSelectionToolbarOverflowableState::_reset
    // 0x98d694: r0 = Null
    //     0x98d694: mov             x0, NULL
    // 0x98d698: LeaveFrame
    //     0x98d698: mov             SP, fp
    //     0x98d69c: ldp             fp, lr, [SP], #0x10
    // 0x98d6a0: ret
    //     0x98d6a0: ret             
    // 0x98d6a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98d6a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98d6a8: b               #0x98d5d4
    // 0x98d6ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98d6ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _reset(/* No info */) {
    // ** addr: 0x98d6b0, size: 0x4c
    // 0x98d6b0: EnterFrame
    //     0x98d6b0: stp             fp, lr, [SP, #-0x10]!
    //     0x98d6b4: mov             fp, SP
    // 0x98d6b8: AllocStack(0x8)
    //     0x98d6b8: sub             SP, SP, #8
    // 0x98d6bc: SetupParameters(_TextSelectionToolbarOverflowableState this /* r1 => r1, fp-0x8 */)
    //     0x98d6bc: stur            x1, [fp, #-8]
    // 0x98d6c0: r0 = UniqueKey()
    //     0x98d6c0: bl              #0x7a4a30  ; AllocateUniqueKeyStub -> UniqueKey (size=0x8)
    // 0x98d6c4: ldur            x1, [fp, #-8]
    // 0x98d6c8: StoreField: r1->field_1f = r0
    //     0x98d6c8: stur            w0, [x1, #0x1f]
    //     0x98d6cc: ldurb           w16, [x1, #-1]
    //     0x98d6d0: ldurb           w17, [x0, #-1]
    //     0x98d6d4: and             x16, x17, x16, lsr #2
    //     0x98d6d8: tst             x16, HEAP, lsr #32
    //     0x98d6dc: b.eq            #0x98d6e4
    //     0x98d6e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x98d6e4: r2 = false
    //     0x98d6e4: add             x2, NULL, #0x30  ; false
    // 0x98d6e8: StoreField: r1->field_1b = r2
    //     0x98d6e8: stur            w2, [x1, #0x1b]
    // 0x98d6ec: r0 = Null
    //     0x98d6ec: mov             x0, NULL
    // 0x98d6f0: LeaveFrame
    //     0x98d6f0: mov             SP, fp
    //     0x98d6f4: ldp             fp, lr, [SP], #0x10
    // 0x98d6f8: ret
    //     0x98d6f8: ret             
  }
  _ build(/* No info */) {
    // ** addr: 0xa0de08, size: 0x290
    // 0xa0de08: EnterFrame
    //     0xa0de08: stp             fp, lr, [SP, #-0x10]!
    //     0xa0de0c: mov             fp, SP
    // 0xa0de10: AllocStack(0x60)
    //     0xa0de10: sub             SP, SP, #0x60
    // 0xa0de14: SetupParameters(_TextSelectionToolbarOverflowableState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa0de14: mov             x0, x1
    //     0xa0de18: stur            x1, [fp, #-8]
    //     0xa0de1c: mov             x1, x2
    //     0xa0de20: stur            x2, [fp, #-0x10]
    // 0xa0de24: CheckStackOverflow
    //     0xa0de24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0de28: cmp             SP, x16
    //     0xa0de2c: b.ls            #0xa0e08c
    // 0xa0de30: r1 = 1
    //     0xa0de30: movz            x1, #0x1
    // 0xa0de34: r0 = AllocateContext()
    //     0xa0de34: bl              #0xec126c  ; AllocateContextStub
    // 0xa0de38: mov             x2, x0
    // 0xa0de3c: ldur            x0, [fp, #-8]
    // 0xa0de40: stur            x2, [fp, #-0x18]
    // 0xa0de44: StoreField: r2->field_f = r0
    //     0xa0de44: stur            w0, [x2, #0xf]
    // 0xa0de48: ldur            x1, [fp, #-0x10]
    // 0xa0de4c: r0 = of()
    //     0xa0de4c: bl              #0x9179e4  ; [package:flutter/src/material/material_localizations.dart] MaterialLocalizations::of
    // 0xa0de50: ldur            x0, [fp, #-8]
    // 0xa0de54: LoadField: r2 = r0->field_1f
    //     0xa0de54: ldur            w2, [x0, #0x1f]
    // 0xa0de58: DecompressPointer r2
    //     0xa0de58: add             x2, x2, HEAP, lsl #32
    // 0xa0de5c: stur            x2, [fp, #-0x28]
    // 0xa0de60: LoadField: r3 = r0->field_1b
    //     0xa0de60: ldur            w3, [x0, #0x1b]
    // 0xa0de64: DecompressPointer r3
    //     0xa0de64: add             x3, x3, HEAP, lsl #32
    // 0xa0de68: ldur            x1, [fp, #-0x10]
    // 0xa0de6c: stur            x3, [fp, #-0x20]
    // 0xa0de70: r0 = of()
    //     0xa0de70: bl              #0x6b0ae8  ; [package:flutter/src/widgets/basic.dart] Directionality::of
    // 0xa0de74: mov             x2, x0
    // 0xa0de78: ldur            x0, [fp, #-8]
    // 0xa0de7c: stur            x2, [fp, #-0x48]
    // 0xa0de80: LoadField: r3 = r0->field_b
    //     0xa0de80: ldur            w3, [x0, #0xb]
    // 0xa0de84: DecompressPointer r3
    //     0xa0de84: add             x3, x3, HEAP, lsl #32
    // 0xa0de88: stur            x3, [fp, #-0x40]
    // 0xa0de8c: cmp             w3, NULL
    // 0xa0de90: b.eq            #0xa0e094
    // 0xa0de94: LoadField: r4 = r3->field_f
    //     0xa0de94: ldur            w4, [x3, #0xf]
    // 0xa0de98: DecompressPointer r4
    //     0xa0de98: add             x4, x4, HEAP, lsl #32
    // 0xa0de9c: stur            x4, [fp, #-0x38]
    // 0xa0dea0: LoadField: r5 = r0->field_1b
    //     0xa0dea0: ldur            w5, [x0, #0x1b]
    // 0xa0dea4: DecompressPointer r5
    //     0xa0dea4: add             x5, x5, HEAP, lsl #32
    // 0xa0dea8: stur            x5, [fp, #-0x30]
    // 0xa0deac: tbnz            w5, #4, #0xa0ded0
    // 0xa0deb0: r1 = <StandardComponentType>
    //     0xa0deb0: add             x1, PP, #0x4e, lsl #12  ; [pp+0x4e388] TypeArguments: <StandardComponentType>
    //     0xa0deb4: ldr             x1, [x1, #0x388]
    // 0xa0deb8: r0 = ValueKey()
    //     0xa0deb8: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xa0debc: mov             x1, x0
    // 0xa0dec0: r0 = Instance_StandardComponentType
    //     0xa0dec0: add             x0, PP, #0x54, lsl #12  ; [pp+0x547f8] Obj!StandardComponentType@e33a01
    //     0xa0dec4: ldr             x0, [x0, #0x7f8]
    // 0xa0dec8: StoreField: r1->field_b = r0
    //     0xa0dec8: stur            w0, [x1, #0xb]
    // 0xa0decc: b               #0xa0deec
    // 0xa0ded0: r1 = <StandardComponentType>
    //     0xa0ded0: add             x1, PP, #0x4e, lsl #12  ; [pp+0x4e388] TypeArguments: <StandardComponentType>
    //     0xa0ded4: ldr             x1, [x1, #0x388]
    // 0xa0ded8: r0 = ValueKey()
    //     0xa0ded8: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xa0dedc: mov             x1, x0
    // 0xa0dee0: r0 = Instance_StandardComponentType
    //     0xa0dee0: add             x0, PP, #0x54, lsl #12  ; [pp+0x54800] Obj!StandardComponentType@e33a41
    //     0xa0dee4: ldr             x0, [x0, #0x800]
    // 0xa0dee8: StoreField: r1->field_b = r0
    //     0xa0dee8: stur            w0, [x1, #0xb]
    // 0xa0deec: ldur            x0, [fp, #-0x30]
    // 0xa0def0: stur            x1, [fp, #-0x50]
    // 0xa0def4: tbnz            w0, #4, #0xa0df04
    // 0xa0def8: r2 = Instance_IconData
    //     0xa0def8: add             x2, PP, #0x4e, lsl #12  ; [pp+0x4e348] Obj!IconData@e0fff1
    //     0xa0defc: ldr             x2, [x2, #0x348]
    // 0xa0df00: b               #0xa0df0c
    // 0xa0df04: r2 = Instance_IconData
    //     0xa0df04: add             x2, PP, #0x54, lsl #12  ; [pp+0x54808] Obj!IconData@e0ffd1
    //     0xa0df08: ldr             x2, [x2, #0x808]
    // 0xa0df0c: stur            x2, [fp, #-8]
    // 0xa0df10: r0 = Icon()
    //     0xa0df10: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xa0df14: mov             x1, x0
    // 0xa0df18: ldur            x0, [fp, #-8]
    // 0xa0df1c: stur            x1, [fp, #-0x58]
    // 0xa0df20: StoreField: r1->field_b = r0
    //     0xa0df20: stur            w0, [x1, #0xb]
    // 0xa0df24: ldur            x0, [fp, #-0x30]
    // 0xa0df28: tbnz            w0, #4, #0xa0df38
    // 0xa0df2c: r8 = "Back"
    //     0xa0df2c: add             x8, PP, #0x4e, lsl #12  ; [pp+0x4e338] "Back"
    //     0xa0df30: ldr             x8, [x8, #0x338]
    // 0xa0df34: b               #0xa0df40
    // 0xa0df38: r8 = "More"
    //     0xa0df38: add             x8, PP, #0x54, lsl #12  ; [pp+0x54810] "More"
    //     0xa0df3c: ldr             x8, [x8, #0x810]
    // 0xa0df40: ldur            x6, [fp, #-0x28]
    // 0xa0df44: ldur            x7, [fp, #-0x20]
    // 0xa0df48: ldur            x3, [fp, #-0x48]
    // 0xa0df4c: ldur            x5, [fp, #-0x38]
    // 0xa0df50: ldur            x2, [fp, #-0x50]
    // 0xa0df54: ldur            x4, [fp, #-0x40]
    // 0xa0df58: stur            x8, [fp, #-8]
    // 0xa0df5c: r0 = _TextSelectionToolbarOverflowButton()
    //     0xa0df5c: bl              #0xa0e14c  ; Allocate_TextSelectionToolbarOverflowButtonStub -> _TextSelectionToolbarOverflowButton (size=0x18)
    // 0xa0df60: mov             x3, x0
    // 0xa0df64: ldur            x0, [fp, #-0x58]
    // 0xa0df68: stur            x3, [fp, #-0x60]
    // 0xa0df6c: StoreField: r3->field_b = r0
    //     0xa0df6c: stur            w0, [x3, #0xb]
    // 0xa0df70: ldur            x2, [fp, #-0x18]
    // 0xa0df74: r1 = Function '<anonymous closure>':.
    //     0xa0df74: add             x1, PP, #0x54, lsl #12  ; [pp+0x54818] AnonymousClosure: (0xa0e158), in [package:flutter/src/material/text_selection_toolbar.dart] _TextSelectionToolbarOverflowableState::build (0xa0de08)
    //     0xa0df78: ldr             x1, [x1, #0x818]
    // 0xa0df7c: r0 = AllocateClosure()
    //     0xa0df7c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0df80: mov             x1, x0
    // 0xa0df84: ldur            x0, [fp, #-0x60]
    // 0xa0df88: StoreField: r0->field_f = r1
    //     0xa0df88: stur            w1, [x0, #0xf]
    // 0xa0df8c: ldur            x1, [fp, #-8]
    // 0xa0df90: StoreField: r0->field_13 = r1
    //     0xa0df90: stur            w1, [x0, #0x13]
    // 0xa0df94: ldur            x1, [fp, #-0x50]
    // 0xa0df98: StoreField: r0->field_7 = r1
    //     0xa0df98: stur            w1, [x0, #7]
    // 0xa0df9c: r1 = Null
    //     0xa0df9c: mov             x1, NULL
    // 0xa0dfa0: r2 = 2
    //     0xa0dfa0: movz            x2, #0x2
    // 0xa0dfa4: r0 = AllocateArray()
    //     0xa0dfa4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa0dfa8: mov             x2, x0
    // 0xa0dfac: ldur            x0, [fp, #-0x60]
    // 0xa0dfb0: stur            x2, [fp, #-8]
    // 0xa0dfb4: StoreField: r2->field_f = r0
    //     0xa0dfb4: stur            w0, [x2, #0xf]
    // 0xa0dfb8: r1 = <Widget>
    //     0xa0dfb8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa0dfbc: r0 = AllocateGrowableArray()
    //     0xa0dfbc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa0dfc0: mov             x3, x0
    // 0xa0dfc4: ldur            x0, [fp, #-8]
    // 0xa0dfc8: stur            x3, [fp, #-0x18]
    // 0xa0dfcc: StoreField: r3->field_f = r0
    //     0xa0dfcc: stur            w0, [x3, #0xf]
    // 0xa0dfd0: r0 = 2
    //     0xa0dfd0: movz            x0, #0x2
    // 0xa0dfd4: StoreField: r3->field_b = r0
    //     0xa0dfd4: stur            w0, [x3, #0xb]
    // 0xa0dfd8: ldur            x0, [fp, #-0x40]
    // 0xa0dfdc: LoadField: r2 = r0->field_b
    //     0xa0dfdc: ldur            w2, [x0, #0xb]
    // 0xa0dfe0: DecompressPointer r2
    //     0xa0dfe0: add             x2, x2, HEAP, lsl #32
    // 0xa0dfe4: mov             x1, x3
    // 0xa0dfe8: r0 = addAll()
    //     0xa0dfe8: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xa0dfec: r0 = _TextSelectionToolbarItemsLayout()
    //     0xa0dfec: bl              #0xa0e140  ; Allocate_TextSelectionToolbarItemsLayoutStub -> _TextSelectionToolbarItemsLayout (size=0x18)
    // 0xa0dff0: mov             x1, x0
    // 0xa0dff4: ldur            x0, [fp, #-0x38]
    // 0xa0dff8: StoreField: r1->field_f = r0
    //     0xa0dff8: stur            w0, [x1, #0xf]
    // 0xa0dffc: ldur            x0, [fp, #-0x30]
    // 0xa0e000: StoreField: r1->field_13 = r0
    //     0xa0e000: stur            w0, [x1, #0x13]
    // 0xa0e004: ldur            x0, [fp, #-0x18]
    // 0xa0e008: StoreField: r1->field_b = r0
    //     0xa0e008: stur            w0, [x1, #0xb]
    // 0xa0e00c: mov             x2, x1
    // 0xa0e010: ldur            x1, [fp, #-0x10]
    // 0xa0e014: r0 = _defaultToolbarBuilder()
    //     0xa0e014: bl              #0xa0e10c  ; [package:flutter/src/material/text_selection_toolbar.dart] TextSelectionToolbar::_defaultToolbarBuilder
    // 0xa0e018: stur            x0, [fp, #-8]
    // 0xa0e01c: r0 = AnimatedSize()
    //     0xa0e01c: bl              #0x9e30c4  ; AllocateAnimatedSizeStub -> AnimatedSize (size=0x28)
    // 0xa0e020: mov             x1, x0
    // 0xa0e024: ldur            x0, [fp, #-8]
    // 0xa0e028: stur            x1, [fp, #-0x10]
    // 0xa0e02c: StoreField: r1->field_b = r0
    //     0xa0e02c: stur            w0, [x1, #0xb]
    // 0xa0e030: r0 = Instance_Alignment
    //     0xa0e030: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xa0e034: ldr             x0, [x0, #0x898]
    // 0xa0e038: StoreField: r1->field_f = r0
    //     0xa0e038: stur            w0, [x1, #0xf]
    // 0xa0e03c: r0 = Instance__Linear
    //     0xa0e03c: ldr             x0, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xa0e040: StoreField: r1->field_13 = r0
    //     0xa0e040: stur            w0, [x1, #0x13]
    // 0xa0e044: r0 = Instance_Duration
    //     0xa0e044: add             x0, PP, #0x54, lsl #12  ; [pp+0x54820] Obj!Duration@e3a2b1
    //     0xa0e048: ldr             x0, [x0, #0x820]
    // 0xa0e04c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa0e04c: stur            w0, [x1, #0x17]
    // 0xa0e050: r0 = Instance_Clip
    //     0xa0e050: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa0e054: ldr             x0, [x0, #0x7c0]
    // 0xa0e058: StoreField: r1->field_1f = r0
    //     0xa0e058: stur            w0, [x1, #0x1f]
    // 0xa0e05c: r0 = _TextSelectionToolbarTrailingEdgeAlign()
    //     0xa0e05c: bl              #0xa0e100  ; Allocate_TextSelectionToolbarTrailingEdgeAlignStub -> _TextSelectionToolbarTrailingEdgeAlign (size=0x18)
    // 0xa0e060: ldur            x1, [fp, #-0x20]
    // 0xa0e064: StoreField: r0->field_f = r1
    //     0xa0e064: stur            w1, [x0, #0xf]
    // 0xa0e068: ldur            x1, [fp, #-0x48]
    // 0xa0e06c: StoreField: r0->field_13 = r1
    //     0xa0e06c: stur            w1, [x0, #0x13]
    // 0xa0e070: ldur            x1, [fp, #-0x10]
    // 0xa0e074: StoreField: r0->field_b = r1
    //     0xa0e074: stur            w1, [x0, #0xb]
    // 0xa0e078: ldur            x1, [fp, #-0x28]
    // 0xa0e07c: StoreField: r0->field_7 = r1
    //     0xa0e07c: stur            w1, [x0, #7]
    // 0xa0e080: LeaveFrame
    //     0xa0e080: mov             SP, fp
    //     0xa0e084: ldp             fp, lr, [SP], #0x10
    // 0xa0e088: ret
    //     0xa0e088: ret             
    // 0xa0e08c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0e08c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0e090: b               #0xa0de30
    // 0xa0e094: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e094: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa0e158, size: 0x60
    // 0xa0e158: EnterFrame
    //     0xa0e158: stp             fp, lr, [SP, #-0x10]!
    //     0xa0e15c: mov             fp, SP
    // 0xa0e160: AllocStack(0x8)
    //     0xa0e160: sub             SP, SP, #8
    // 0xa0e164: SetupParameters()
    //     0xa0e164: ldr             x0, [fp, #0x10]
    //     0xa0e168: ldur            w2, [x0, #0x17]
    //     0xa0e16c: add             x2, x2, HEAP, lsl #32
    // 0xa0e170: CheckStackOverflow
    //     0xa0e170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0e174: cmp             SP, x16
    //     0xa0e178: b.ls            #0xa0e1b0
    // 0xa0e17c: LoadField: r0 = r2->field_f
    //     0xa0e17c: ldur            w0, [x2, #0xf]
    // 0xa0e180: DecompressPointer r0
    //     0xa0e180: add             x0, x0, HEAP, lsl #32
    // 0xa0e184: stur            x0, [fp, #-8]
    // 0xa0e188: r1 = Function '<anonymous closure>':.
    //     0xa0e188: add             x1, PP, #0x54, lsl #12  ; [pp+0x54828] AnonymousClosure: (0xa0e1b8), in [package:flutter/src/material/text_selection_toolbar.dart] _TextSelectionToolbarOverflowableState::build (0xa0de08)
    //     0xa0e18c: ldr             x1, [x1, #0x828]
    // 0xa0e190: r0 = AllocateClosure()
    //     0xa0e190: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0e194: ldur            x1, [fp, #-8]
    // 0xa0e198: mov             x2, x0
    // 0xa0e19c: r0 = setState()
    //     0xa0e19c: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa0e1a0: r0 = Null
    //     0xa0e1a0: mov             x0, NULL
    // 0xa0e1a4: LeaveFrame
    //     0xa0e1a4: mov             SP, fp
    //     0xa0e1a8: ldp             fp, lr, [SP], #0x10
    // 0xa0e1ac: ret
    //     0xa0e1ac: ret             
    // 0xa0e1b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0e1b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0e1b4: b               #0xa0e17c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa0e1b8, size: 0x2c
    // 0xa0e1b8: ldr             x1, [SP]
    // 0xa0e1bc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa0e1bc: ldur            w2, [x1, #0x17]
    // 0xa0e1c0: DecompressPointer r2
    //     0xa0e1c0: add             x2, x2, HEAP, lsl #32
    // 0xa0e1c4: LoadField: r1 = r2->field_f
    //     0xa0e1c4: ldur            w1, [x2, #0xf]
    // 0xa0e1c8: DecompressPointer r1
    //     0xa0e1c8: add             x1, x1, HEAP, lsl #32
    // 0xa0e1cc: LoadField: r2 = r1->field_1b
    //     0xa0e1cc: ldur            w2, [x1, #0x1b]
    // 0xa0e1d0: DecompressPointer r2
    //     0xa0e1d0: add             x2, x2, HEAP, lsl #32
    // 0xa0e1d4: eor             x3, x2, #0x10
    // 0xa0e1d8: StoreField: r1->field_1b = r3
    //     0xa0e1d8: stur            w3, [x1, #0x1b]
    // 0xa0e1dc: r0 = Null
    //     0xa0e1dc: mov             x0, NULL
    // 0xa0e1e0: ret
    //     0xa0e1e0: ret             
  }
}

// class id: 4429, size: 0x4c, field offset: 0x4c
class _TextSelectionToolbarItemsLayoutElement extends MultiChildRenderObjectElement {
}

// class id: 4551, size: 0x18, field offset: 0x10
//   const constructor, 
class _TextSelectionToolbarTrailingEdgeAlign extends SingleChildRenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x85a40c, size: 0x60
    // 0x85a40c: EnterFrame
    //     0x85a40c: stp             fp, lr, [SP, #-0x10]!
    //     0x85a410: mov             fp, SP
    // 0x85a414: AllocStack(0x10)
    //     0x85a414: sub             SP, SP, #0x10
    // 0x85a418: CheckStackOverflow
    //     0x85a418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85a41c: cmp             SP, x16
    //     0x85a420: b.ls            #0x85a464
    // 0x85a424: LoadField: r2 = r1->field_f
    //     0x85a424: ldur            w2, [x1, #0xf]
    // 0x85a428: DecompressPointer r2
    //     0x85a428: add             x2, x2, HEAP, lsl #32
    // 0x85a42c: stur            x2, [fp, #-0x10]
    // 0x85a430: LoadField: r3 = r1->field_13
    //     0x85a430: ldur            w3, [x1, #0x13]
    // 0x85a434: DecompressPointer r3
    //     0x85a434: add             x3, x3, HEAP, lsl #32
    // 0x85a438: stur            x3, [fp, #-8]
    // 0x85a43c: r0 = _TextSelectionToolbarTrailingEdgeAlignRenderBox()
    //     0x85a43c: bl              #0x85a4fc  ; Allocate_TextSelectionToolbarTrailingEdgeAlignRenderBoxStub -> _TextSelectionToolbarTrailingEdgeAlignRenderBox (size=0x68)
    // 0x85a440: mov             x1, x0
    // 0x85a444: ldur            x2, [fp, #-0x10]
    // 0x85a448: ldur            x3, [fp, #-8]
    // 0x85a44c: stur            x0, [fp, #-8]
    // 0x85a450: r0 = _TextSelectionToolbarTrailingEdgeAlignRenderBox()
    //     0x85a450: bl              #0x85a46c  ; [package:flutter/src/material/text_selection_toolbar.dart] _TextSelectionToolbarTrailingEdgeAlignRenderBox::_TextSelectionToolbarTrailingEdgeAlignRenderBox
    // 0x85a454: ldur            x0, [fp, #-8]
    // 0x85a458: LeaveFrame
    //     0x85a458: mov             SP, fp
    //     0x85a45c: ldp             fp, lr, [SP], #0x10
    // 0x85a460: ret
    //     0x85a460: ret             
    // 0x85a464: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85a464: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85a468: b               #0x85a424
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xc6d6b0, size: 0x9c
    // 0xc6d6b0: EnterFrame
    //     0xc6d6b0: stp             fp, lr, [SP, #-0x10]!
    //     0xc6d6b4: mov             fp, SP
    // 0xc6d6b8: AllocStack(0x10)
    //     0xc6d6b8: sub             SP, SP, #0x10
    // 0xc6d6bc: SetupParameters(_TextSelectionToolbarTrailingEdgeAlign this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xc6d6bc: mov             x4, x1
    //     0xc6d6c0: stur            x1, [fp, #-8]
    //     0xc6d6c4: stur            x3, [fp, #-0x10]
    // 0xc6d6c8: CheckStackOverflow
    //     0xc6d6c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6d6cc: cmp             SP, x16
    //     0xc6d6d0: b.ls            #0xc6d744
    // 0xc6d6d4: mov             x0, x3
    // 0xc6d6d8: r2 = Null
    //     0xc6d6d8: mov             x2, NULL
    // 0xc6d6dc: r1 = Null
    //     0xc6d6dc: mov             x1, NULL
    // 0xc6d6e0: r4 = 60
    //     0xc6d6e0: movz            x4, #0x3c
    // 0xc6d6e4: branchIfSmi(r0, 0xc6d6f0)
    //     0xc6d6e4: tbz             w0, #0, #0xc6d6f0
    // 0xc6d6e8: r4 = LoadClassIdInstr(r0)
    //     0xc6d6e8: ldur            x4, [x0, #-1]
    //     0xc6d6ec: ubfx            x4, x4, #0xc, #0x14
    // 0xc6d6f0: cmp             x4, #0xc4d
    // 0xc6d6f4: b.eq            #0xc6d70c
    // 0xc6d6f8: r8 = _TextSelectionToolbarTrailingEdgeAlignRenderBox
    //     0xc6d6f8: add             x8, PP, #0x58, lsl #12  ; [pp+0x58b40] Type: _TextSelectionToolbarTrailingEdgeAlignRenderBox
    //     0xc6d6fc: ldr             x8, [x8, #0xb40]
    // 0xc6d700: r3 = Null
    //     0xc6d700: add             x3, PP, #0x58, lsl #12  ; [pp+0x58b48] Null
    //     0xc6d704: ldr             x3, [x3, #0xb48]
    // 0xc6d708: r0 = DefaultTypeTest()
    //     0xc6d708: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc6d70c: ldur            x0, [fp, #-8]
    // 0xc6d710: LoadField: r2 = r0->field_f
    //     0xc6d710: ldur            w2, [x0, #0xf]
    // 0xc6d714: DecompressPointer r2
    //     0xc6d714: add             x2, x2, HEAP, lsl #32
    // 0xc6d718: ldur            x1, [fp, #-0x10]
    // 0xc6d71c: r0 = overflowOpen=()
    //     0xc6d71c: bl              #0xc6d7bc  ; [package:flutter/src/material/text_selection_toolbar.dart] _TextSelectionToolbarTrailingEdgeAlignRenderBox::overflowOpen=
    // 0xc6d720: ldur            x0, [fp, #-8]
    // 0xc6d724: LoadField: r2 = r0->field_13
    //     0xc6d724: ldur            w2, [x0, #0x13]
    // 0xc6d728: DecompressPointer r2
    //     0xc6d728: add             x2, x2, HEAP, lsl #32
    // 0xc6d72c: ldur            x1, [fp, #-0x10]
    // 0xc6d730: r0 = textDirection=()
    //     0xc6d730: bl              #0xc6d74c  ; [package:flutter/src/material/text_selection_toolbar.dart] _TextSelectionToolbarTrailingEdgeAlignRenderBox::textDirection=
    // 0xc6d734: r0 = Null
    //     0xc6d734: mov             x0, NULL
    // 0xc6d738: LeaveFrame
    //     0xc6d738: mov             SP, fp
    //     0xc6d73c: ldp             fp, lr, [SP], #0x10
    // 0xc6d740: ret
    //     0xc6d740: ret             
    // 0xc6d744: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6d744: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6d748: b               #0xc6d6d4
  }
}

// class id: 4581, size: 0x18, field offset: 0x10
//   const constructor, 
class _TextSelectionToolbarItemsLayout extends MultiChildRenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x856624, size: 0x60
    // 0x856624: EnterFrame
    //     0x856624: stp             fp, lr, [SP, #-0x10]!
    //     0x856628: mov             fp, SP
    // 0x85662c: AllocStack(0x10)
    //     0x85662c: sub             SP, SP, #0x10
    // 0x856630: CheckStackOverflow
    //     0x856630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x856634: cmp             SP, x16
    //     0x856638: b.ls            #0x85667c
    // 0x85663c: LoadField: r2 = r1->field_f
    //     0x85663c: ldur            w2, [x1, #0xf]
    // 0x856640: DecompressPointer r2
    //     0x856640: add             x2, x2, HEAP, lsl #32
    // 0x856644: stur            x2, [fp, #-0x10]
    // 0x856648: LoadField: r3 = r1->field_13
    //     0x856648: ldur            w3, [x1, #0x13]
    // 0x85664c: DecompressPointer r3
    //     0x85664c: add             x3, x3, HEAP, lsl #32
    // 0x856650: stur            x3, [fp, #-8]
    // 0x856654: r0 = _RenderTextSelectionToolbarItemsLayout()
    //     0x856654: bl              #0x8566f4  ; Allocate_RenderTextSelectionToolbarItemsLayoutStub -> _RenderTextSelectionToolbarItemsLayout (size=0x78)
    // 0x856658: mov             x1, x0
    // 0x85665c: ldur            x2, [fp, #-0x10]
    // 0x856660: ldur            x3, [fp, #-8]
    // 0x856664: stur            x0, [fp, #-8]
    // 0x856668: r0 = _RenderTextSelectionToolbarItemsLayout()
    //     0x856668: bl              #0x856684  ; [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::_RenderTextSelectionToolbarItemsLayout
    // 0x85666c: ldur            x0, [fp, #-8]
    // 0x856670: LeaveFrame
    //     0x856670: mov             SP, fp
    //     0x856674: ldp             fp, lr, [SP], #0x10
    // 0x856678: ret
    //     0x856678: ret             
    // 0x85667c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85667c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x856680: b               #0x85663c
  }
  _ createElement(/* No info */) {
    // ** addr: 0xbbda24, size: 0x4c
    // 0xbbda24: EnterFrame
    //     0xbbda24: stp             fp, lr, [SP, #-0x10]!
    //     0xbbda28: mov             fp, SP
    // 0xbbda2c: AllocStack(0x8)
    //     0xbbda2c: sub             SP, SP, #8
    // 0xbbda30: SetupParameters(_TextSelectionToolbarItemsLayout this /* r1 => r2, fp-0x8 */)
    //     0xbbda30: mov             x2, x1
    //     0xbbda34: stur            x1, [fp, #-8]
    // 0xbbda38: CheckStackOverflow
    //     0xbbda38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbda3c: cmp             SP, x16
    //     0xbbda40: b.ls            #0xbbda68
    // 0xbbda44: r0 = _TextSelectionToolbarItemsLayoutElement()
    //     0xbbda44: bl              #0xbbdbc4  ; Allocate_TextSelectionToolbarItemsLayoutElementStub -> _TextSelectionToolbarItemsLayoutElement (size=0x4c)
    // 0xbbda48: mov             x1, x0
    // 0xbbda4c: ldur            x2, [fp, #-8]
    // 0xbbda50: stur            x0, [fp, #-8]
    // 0xbbda54: r0 = MultiChildRenderObjectElement()
    //     0xbbda54: bl              #0xbbda70  ; [package:flutter/src/widgets/framework.dart] MultiChildRenderObjectElement::MultiChildRenderObjectElement
    // 0xbbda58: ldur            x0, [fp, #-8]
    // 0xbbda5c: LeaveFrame
    //     0xbbda5c: mov             SP, fp
    //     0xbbda60: ldp             fp, lr, [SP], #0x10
    // 0xbbda64: ret
    //     0xbbda64: ret             
    // 0xbbda68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbda68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbda6c: b               #0xbbda44
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xc694ac, size: 0x9c
    // 0xc694ac: EnterFrame
    //     0xc694ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc694b0: mov             fp, SP
    // 0xc694b4: AllocStack(0x10)
    //     0xc694b4: sub             SP, SP, #0x10
    // 0xc694b8: SetupParameters(_TextSelectionToolbarItemsLayout this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xc694b8: mov             x4, x1
    //     0xc694bc: stur            x1, [fp, #-8]
    //     0xc694c0: stur            x3, [fp, #-0x10]
    // 0xc694c4: CheckStackOverflow
    //     0xc694c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc694c8: cmp             SP, x16
    //     0xc694cc: b.ls            #0xc69540
    // 0xc694d0: mov             x0, x3
    // 0xc694d4: r2 = Null
    //     0xc694d4: mov             x2, NULL
    // 0xc694d8: r1 = Null
    //     0xc694d8: mov             x1, NULL
    // 0xc694dc: r4 = 60
    //     0xc694dc: movz            x4, #0x3c
    // 0xc694e0: branchIfSmi(r0, 0xc694ec)
    //     0xc694e0: tbz             w0, #0, #0xc694ec
    // 0xc694e4: r4 = LoadClassIdInstr(r0)
    //     0xc694e4: ldur            x4, [x0, #-1]
    //     0xc694e8: ubfx            x4, x4, #0xc, #0x14
    // 0xc694ec: cmp             x4, #0xbfa
    // 0xc694f0: b.eq            #0xc69508
    // 0xc694f4: r8 = _RenderTextSelectionToolbarItemsLayout
    //     0xc694f4: add             x8, PP, #0x58, lsl #12  ; [pp+0x58b58] Type: _RenderTextSelectionToolbarItemsLayout
    //     0xc694f8: ldr             x8, [x8, #0xb58]
    // 0xc694fc: r3 = Null
    //     0xc694fc: add             x3, PP, #0x58, lsl #12  ; [pp+0x58b60] Null
    //     0xc69500: ldr             x3, [x3, #0xb60]
    // 0xc69504: r0 = DefaultTypeTest()
    //     0xc69504: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc69508: ldur            x0, [fp, #-8]
    // 0xc6950c: LoadField: r2 = r0->field_f
    //     0xc6950c: ldur            w2, [x0, #0xf]
    // 0xc69510: DecompressPointer r2
    //     0xc69510: add             x2, x2, HEAP, lsl #32
    // 0xc69514: ldur            x1, [fp, #-0x10]
    // 0xc69518: r0 = isAbove=()
    //     0xc69518: bl              #0xc6959c  ; [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::isAbove=
    // 0xc6951c: ldur            x0, [fp, #-8]
    // 0xc69520: LoadField: r2 = r0->field_13
    //     0xc69520: ldur            w2, [x0, #0x13]
    // 0xc69524: DecompressPointer r2
    //     0xc69524: add             x2, x2, HEAP, lsl #32
    // 0xc69528: ldur            x1, [fp, #-0x10]
    // 0xc6952c: r0 = overflowOpen=()
    //     0xc6952c: bl              #0xc69548  ; [package:flutter/src/material/text_selection_toolbar.dart] _RenderTextSelectionToolbarItemsLayout::overflowOpen=
    // 0xc69530: r0 = Null
    //     0xc69530: mov             x0, NULL
    // 0xc69534: LeaveFrame
    //     0xc69534: mov             SP, fp
    //     0xc69538: ldp             fp, lr, [SP], #0x10
    // 0xc6953c: ret
    //     0xc6953c: ret             
    // 0xc69540: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc69540: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc69544: b               #0xc694d0
  }
}

// class id: 4806, size: 0x18, field offset: 0xc
//   const constructor, 
class _TextSelectionToolbarOverflowable extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa911a4, size: 0x48
    // 0xa911a4: EnterFrame
    //     0xa911a4: stp             fp, lr, [SP, #-0x10]!
    //     0xa911a8: mov             fp, SP
    // 0xa911ac: AllocStack(0x8)
    //     0xa911ac: sub             SP, SP, #8
    // 0xa911b0: SetupParameters(_TextSelectionToolbarOverflowable this /* r1 => r0 */)
    //     0xa911b0: mov             x0, x1
    // 0xa911b4: r1 = <_TextSelectionToolbarOverflowable>
    //     0xa911b4: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d820] TypeArguments: <_TextSelectionToolbarOverflowable>
    //     0xa911b8: ldr             x1, [x1, #0x820]
    // 0xa911bc: r0 = _TextSelectionToolbarOverflowableState()
    //     0xa911bc: bl              #0xa911ec  ; Allocate_TextSelectionToolbarOverflowableStateStub -> _TextSelectionToolbarOverflowableState (size=0x24)
    // 0xa911c0: mov             x1, x0
    // 0xa911c4: r0 = false
    //     0xa911c4: add             x0, NULL, #0x30  ; false
    // 0xa911c8: stur            x1, [fp, #-8]
    // 0xa911cc: StoreField: r1->field_1b = r0
    //     0xa911cc: stur            w0, [x1, #0x1b]
    // 0xa911d0: r0 = UniqueKey()
    //     0xa911d0: bl              #0x7a4a30  ; AllocateUniqueKeyStub -> UniqueKey (size=0x8)
    // 0xa911d4: mov             x1, x0
    // 0xa911d8: ldur            x0, [fp, #-8]
    // 0xa911dc: StoreField: r0->field_1f = r1
    //     0xa911dc: stur            w1, [x0, #0x1f]
    // 0xa911e0: LeaveFrame
    //     0xa911e0: mov             SP, fp
    //     0xa911e4: ldp             fp, lr, [SP], #0x10
    // 0xa911e8: ret
    //     0xa911e8: ret             
  }
}

// class id: 5366, size: 0x18, field offset: 0xc
//   const constructor, 
class _TextSelectionToolbarOverflowButton extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xaa0d80, size: 0xb8
    // 0xaa0d80: EnterFrame
    //     0xaa0d80: stp             fp, lr, [SP, #-0x10]!
    //     0xaa0d84: mov             fp, SP
    // 0xaa0d88: AllocStack(0x20)
    //     0xaa0d88: sub             SP, SP, #0x20
    // 0xaa0d8c: LoadField: r0 = r1->field_b
    //     0xaa0d8c: ldur            w0, [x1, #0xb]
    // 0xaa0d90: DecompressPointer r0
    //     0xaa0d90: add             x0, x0, HEAP, lsl #32
    // 0xaa0d94: stur            x0, [fp, #-0x18]
    // 0xaa0d98: LoadField: r2 = r1->field_f
    //     0xaa0d98: ldur            w2, [x1, #0xf]
    // 0xaa0d9c: DecompressPointer r2
    //     0xaa0d9c: add             x2, x2, HEAP, lsl #32
    // 0xaa0da0: stur            x2, [fp, #-0x10]
    // 0xaa0da4: LoadField: r3 = r1->field_13
    //     0xaa0da4: ldur            w3, [x1, #0x13]
    // 0xaa0da8: DecompressPointer r3
    //     0xaa0da8: add             x3, x3, HEAP, lsl #32
    // 0xaa0dac: stur            x3, [fp, #-8]
    // 0xaa0db0: r0 = IconButton()
    //     0xaa0db0: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xaa0db4: mov             x1, x0
    // 0xaa0db8: ldur            x0, [fp, #-0x10]
    // 0xaa0dbc: stur            x1, [fp, #-0x20]
    // 0xaa0dc0: StoreField: r1->field_3b = r0
    //     0xaa0dc0: stur            w0, [x1, #0x3b]
    // 0xaa0dc4: r0 = false
    //     0xaa0dc4: add             x0, NULL, #0x30  ; false
    // 0xaa0dc8: StoreField: r1->field_47 = r0
    //     0xaa0dc8: stur            w0, [x1, #0x47]
    // 0xaa0dcc: ldur            x0, [fp, #-8]
    // 0xaa0dd0: StoreField: r1->field_4b = r0
    //     0xaa0dd0: stur            w0, [x1, #0x4b]
    // 0xaa0dd4: ldur            x0, [fp, #-0x18]
    // 0xaa0dd8: StoreField: r1->field_1f = r0
    //     0xaa0dd8: stur            w0, [x1, #0x1f]
    // 0xaa0ddc: r0 = Instance__IconButtonVariant
    //     0xaa0ddc: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xaa0de0: ldr             x0, [x0, #0xf78]
    // 0xaa0de4: StoreField: r1->field_63 = r0
    //     0xaa0de4: stur            w0, [x1, #0x63]
    // 0xaa0de8: r0 = Material()
    //     0xaa0de8: bl              #0x9e6a2c  ; AllocateMaterialStub -> Material (size=0x40)
    // 0xaa0dec: r1 = Instance_MaterialType
    //     0xaa0dec: add             x1, PP, #0x39, lsl #12  ; [pp+0x399b8] Obj!MaterialType@e36581
    //     0xaa0df0: ldr             x1, [x1, #0x9b8]
    // 0xaa0df4: StoreField: r0->field_f = r1
    //     0xaa0df4: stur            w1, [x0, #0xf]
    // 0xaa0df8: StoreField: r0->field_13 = rZR
    //     0xaa0df8: stur            xzr, [x0, #0x13]
    // 0xaa0dfc: r1 = Instance_Color
    //     0xaa0dfc: ldr             x1, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xaa0e00: StoreField: r0->field_1b = r1
    //     0xaa0e00: stur            w1, [x0, #0x1b]
    // 0xaa0e04: r1 = true
    //     0xaa0e04: add             x1, NULL, #0x20  ; true
    // 0xaa0e08: StoreField: r0->field_2f = r1
    //     0xaa0e08: stur            w1, [x0, #0x2f]
    // 0xaa0e0c: r1 = Instance_Clip
    //     0xaa0e0c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xaa0e10: ldr             x1, [x1, #0x750]
    // 0xaa0e14: StoreField: r0->field_33 = r1
    //     0xaa0e14: stur            w1, [x0, #0x33]
    // 0xaa0e18: r1 = Instance_Duration
    //     0xaa0e18: add             x1, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xaa0e1c: ldr             x1, [x1, #0x368]
    // 0xaa0e20: StoreField: r0->field_37 = r1
    //     0xaa0e20: stur            w1, [x0, #0x37]
    // 0xaa0e24: ldur            x1, [fp, #-0x20]
    // 0xaa0e28: StoreField: r0->field_b = r1
    //     0xaa0e28: stur            w1, [x0, #0xb]
    // 0xaa0e2c: LeaveFrame
    //     0xaa0e2c: mov             SP, fp
    //     0xaa0e30: ldp             fp, lr, [SP], #0x10
    // 0xaa0e34: ret
    //     0xaa0e34: ret             
  }
}

// class id: 5367, size: 0x10, field offset: 0xc
//   const constructor, 
class _TextSelectionToolbarContainer extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xaa0ba0, size: 0xb4
    // 0xaa0ba0: EnterFrame
    //     0xaa0ba0: stp             fp, lr, [SP, #-0x10]!
    //     0xaa0ba4: mov             fp, SP
    // 0xaa0ba8: AllocStack(0x18)
    //     0xaa0ba8: sub             SP, SP, #0x18
    // 0xaa0bac: SetupParameters(_TextSelectionToolbarContainer this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0xaa0bac: mov             x0, x1
    //     0xaa0bb0: stur            x1, [fp, #-8]
    //     0xaa0bb4: mov             x1, x2
    // 0xaa0bb8: CheckStackOverflow
    //     0xaa0bb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa0bbc: cmp             SP, x16
    //     0xaa0bc0: b.ls            #0xaa0c4c
    // 0xaa0bc4: r0 = of()
    //     0xaa0bc4: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaa0bc8: LoadField: r1 = r0->field_3f
    //     0xaa0bc8: ldur            w1, [x0, #0x3f]
    // 0xaa0bcc: DecompressPointer r1
    //     0xaa0bcc: add             x1, x1, HEAP, lsl #32
    // 0xaa0bd0: r0 = _getColor()
    //     0xaa0bd0: bl              #0xaa0c54  ; [package:flutter/src/material/text_selection_toolbar.dart] _TextSelectionToolbarContainer::_getColor
    // 0xaa0bd4: mov             x1, x0
    // 0xaa0bd8: ldur            x0, [fp, #-8]
    // 0xaa0bdc: stur            x1, [fp, #-0x18]
    // 0xaa0be0: LoadField: r2 = r0->field_b
    //     0xaa0be0: ldur            w2, [x0, #0xb]
    // 0xaa0be4: DecompressPointer r2
    //     0xaa0be4: add             x2, x2, HEAP, lsl #32
    // 0xaa0be8: stur            x2, [fp, #-0x10]
    // 0xaa0bec: r0 = Material()
    //     0xaa0bec: bl              #0x9e6a2c  ; AllocateMaterialStub -> Material (size=0x40)
    // 0xaa0bf0: r1 = Instance_MaterialType
    //     0xaa0bf0: add             x1, PP, #0x39, lsl #12  ; [pp+0x399b8] Obj!MaterialType@e36581
    //     0xaa0bf4: ldr             x1, [x1, #0x9b8]
    // 0xaa0bf8: StoreField: r0->field_f = r1
    //     0xaa0bf8: stur            w1, [x0, #0xf]
    // 0xaa0bfc: d0 = 1.000000
    //     0xaa0bfc: fmov            d0, #1.00000000
    // 0xaa0c00: StoreField: r0->field_13 = d0
    //     0xaa0c00: stur            d0, [x0, #0x13]
    // 0xaa0c04: ldur            x1, [fp, #-0x18]
    // 0xaa0c08: StoreField: r0->field_1b = r1
    //     0xaa0c08: stur            w1, [x0, #0x1b]
    // 0xaa0c0c: r1 = Instance_BorderRadius
    //     0xaa0c0c: add             x1, PP, #0x43, lsl #12  ; [pp+0x438d8] Obj!BorderRadius@e13bf1
    //     0xaa0c10: ldr             x1, [x1, #0x8d8]
    // 0xaa0c14: StoreField: r0->field_3b = r1
    //     0xaa0c14: stur            w1, [x0, #0x3b]
    // 0xaa0c18: r1 = true
    //     0xaa0c18: add             x1, NULL, #0x20  ; true
    // 0xaa0c1c: StoreField: r0->field_2f = r1
    //     0xaa0c1c: stur            w1, [x0, #0x2f]
    // 0xaa0c20: r1 = Instance_Clip
    //     0xaa0c20: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d4f8] Obj!Clip@e39b21
    //     0xaa0c24: ldr             x1, [x1, #0x4f8]
    // 0xaa0c28: StoreField: r0->field_33 = r1
    //     0xaa0c28: stur            w1, [x0, #0x33]
    // 0xaa0c2c: r1 = Instance_Duration
    //     0xaa0c2c: add             x1, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xaa0c30: ldr             x1, [x1, #0x368]
    // 0xaa0c34: StoreField: r0->field_37 = r1
    //     0xaa0c34: stur            w1, [x0, #0x37]
    // 0xaa0c38: ldur            x1, [fp, #-0x10]
    // 0xaa0c3c: StoreField: r0->field_b = r1
    //     0xaa0c3c: stur            w1, [x0, #0xb]
    // 0xaa0c40: LeaveFrame
    //     0xaa0c40: mov             SP, fp
    //     0xaa0c44: ldp             fp, lr, [SP], #0x10
    // 0xaa0c48: ret
    //     0xaa0c48: ret             
    // 0xaa0c4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa0c4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa0c50: b               #0xaa0bc4
  }
  static _ _getColor(/* No info */) {
    // ** addr: 0xaa0c54, size: 0xec
    // 0xaa0c54: EnterFrame
    //     0xaa0c54: stp             fp, lr, [SP, #-0x10]!
    //     0xaa0c58: mov             fp, SP
    // 0xaa0c5c: AllocStack(0x20)
    //     0xaa0c5c: sub             SP, SP, #0x20
    // 0xaa0c60: SetupParameters(dynamic _ /* r1 => r0, fp-0x10 */)
    //     0xaa0c60: mov             x0, x1
    //     0xaa0c64: stur            x1, [fp, #-0x10]
    // 0xaa0c68: CheckStackOverflow
    //     0xaa0c68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa0c6c: cmp             SP, x16
    //     0xaa0c70: b.ls            #0xaa0d38
    // 0xaa0c74: LoadField: r1 = r0->field_7
    //     0xaa0c74: ldur            w1, [x0, #7]
    // 0xaa0c78: DecompressPointer r1
    //     0xaa0c78: add             x1, x1, HEAP, lsl #32
    // 0xaa0c7c: LoadField: r2 = r1->field_7
    //     0xaa0c7c: ldur            x2, [x1, #7]
    // 0xaa0c80: stur            x2, [fp, #-8]
    // 0xaa0c84: cmp             x2, #0
    // 0xaa0c88: b.gt            #0xaa0ccc
    // 0xaa0c8c: r16 = Instance_Brightness
    //     0xaa0c8c: ldr             x16, [PP, #0x5428]  ; [pp+0x5428] Obj!Brightness@e39141
    // 0xaa0c90: stp             NULL, x16, [SP]
    // 0xaa0c94: r1 = Null
    //     0xaa0c94: mov             x1, NULL
    // 0xaa0c98: r4 = const [0, 0x3, 0x2, 0x1, brightness, 0x1, useMaterial3, 0x2, null]
    //     0xaa0c98: ldr             x4, [PP, #0x6bf0]  ; [pp+0x6bf0] List(9) [0, 0x3, 0x2, 0x1, "brightness", 0x1, "useMaterial3", 0x2, Null]
    // 0xaa0c9c: r0 = ThemeData()
    //     0xaa0c9c: bl              #0x63a538  ; [package:flutter/src/material/theme_data.dart] ThemeData::ThemeData
    // 0xaa0ca0: LoadField: r1 = r0->field_3f
    //     0xaa0ca0: ldur            w1, [x0, #0x3f]
    // 0xaa0ca4: DecompressPointer r1
    //     0xaa0ca4: add             x1, x1, HEAP, lsl #32
    // 0xaa0ca8: LoadField: r0 = r1->field_7b
    //     0xaa0ca8: ldur            w0, [x1, #0x7b]
    // 0xaa0cac: DecompressPointer r0
    //     0xaa0cac: add             x0, x0, HEAP, lsl #32
    // 0xaa0cb0: ldur            x2, [fp, #-0x10]
    // 0xaa0cb4: LoadField: r1 = r2->field_7b
    //     0xaa0cb4: ldur            w1, [x2, #0x7b]
    // 0xaa0cb8: DecompressPointer r1
    //     0xaa0cb8: add             x1, x1, HEAP, lsl #32
    // 0xaa0cbc: cmp             w0, w1
    // 0xaa0cc0: b.eq            #0xaa0d10
    // 0xaa0cc4: mov             x0, x1
    // 0xaa0cc8: b               #0xaa0d04
    // 0xaa0ccc: mov             x2, x0
    // 0xaa0cd0: r1 = Null
    //     0xaa0cd0: mov             x1, NULL
    // 0xaa0cd4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaa0cd4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaa0cd8: r0 = ThemeData()
    //     0xaa0cd8: bl              #0x63a538  ; [package:flutter/src/material/theme_data.dart] ThemeData::ThemeData
    // 0xaa0cdc: LoadField: r1 = r0->field_3f
    //     0xaa0cdc: ldur            w1, [x0, #0x3f]
    // 0xaa0ce0: DecompressPointer r1
    //     0xaa0ce0: add             x1, x1, HEAP, lsl #32
    // 0xaa0ce4: LoadField: r2 = r1->field_7b
    //     0xaa0ce4: ldur            w2, [x1, #0x7b]
    // 0xaa0ce8: DecompressPointer r2
    //     0xaa0ce8: add             x2, x2, HEAP, lsl #32
    // 0xaa0cec: ldur            x1, [fp, #-0x10]
    // 0xaa0cf0: LoadField: r3 = r1->field_7b
    //     0xaa0cf0: ldur            w3, [x1, #0x7b]
    // 0xaa0cf4: DecompressPointer r3
    //     0xaa0cf4: add             x3, x3, HEAP, lsl #32
    // 0xaa0cf8: cmp             w2, w3
    // 0xaa0cfc: b.eq            #0xaa0d10
    // 0xaa0d00: mov             x0, x3
    // 0xaa0d04: LeaveFrame
    //     0xaa0d04: mov             SP, fp
    //     0xaa0d08: ldp             fp, lr, [SP], #0x10
    // 0xaa0d0c: ret
    //     0xaa0d0c: ret             
    // 0xaa0d10: ldur            x1, [fp, #-8]
    // 0xaa0d14: cmp             x1, #0
    // 0xaa0d18: b.gt            #0xaa0d28
    // 0xaa0d1c: r0 = Instance_Color
    //     0xaa0d1c: add             x0, PP, #0x43, lsl #12  ; [pp+0x438e0] Obj!Color@e27361
    //     0xaa0d20: ldr             x0, [x0, #0x8e0]
    // 0xaa0d24: b               #0xaa0d2c
    // 0xaa0d28: r0 = Instance_Color
    //     0xaa0d28: ldr             x0, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xaa0d2c: LeaveFrame
    //     0xaa0d2c: mov             SP, fp
    //     0xaa0d30: ldp             fp, lr, [SP], #0x10
    // 0xaa0d34: ret
    //     0xaa0d34: ret             
    // 0xaa0d38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa0d38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa0d3c: b               #0xaa0c74
  }
}

// class id: 5368, size: 0x1c, field offset: 0xc
//   const constructor, 
class TextSelectionToolbar extends StatelessWidget {

  [closure] static Widget _defaultToolbarBuilder(dynamic, BuildContext, Widget) {
    // ** addr: 0xa0e098, size: 0x34
    // 0xa0e098: EnterFrame
    //     0xa0e098: stp             fp, lr, [SP, #-0x10]!
    //     0xa0e09c: mov             fp, SP
    // 0xa0e0a0: CheckStackOverflow
    //     0xa0e0a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0e0a4: cmp             SP, x16
    //     0xa0e0a8: b.ls            #0xa0e0c4
    // 0xa0e0ac: ldr             x1, [fp, #0x18]
    // 0xa0e0b0: ldr             x2, [fp, #0x10]
    // 0xa0e0b4: r0 = _defaultToolbarBuilder()
    //     0xa0e0b4: bl              #0xa0e10c  ; [package:flutter/src/material/text_selection_toolbar.dart] TextSelectionToolbar::_defaultToolbarBuilder
    // 0xa0e0b8: LeaveFrame
    //     0xa0e0b8: mov             SP, fp
    //     0xa0e0bc: ldp             fp, lr, [SP], #0x10
    // 0xa0e0c0: ret
    //     0xa0e0c0: ret             
    // 0xa0e0c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0e0c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0e0c8: b               #0xa0e0ac
  }
  static _ _defaultToolbarBuilder(/* No info */) {
    // ** addr: 0xa0e10c, size: 0x28
    // 0xa0e10c: EnterFrame
    //     0xa0e10c: stp             fp, lr, [SP, #-0x10]!
    //     0xa0e110: mov             fp, SP
    // 0xa0e114: AllocStack(0x8)
    //     0xa0e114: sub             SP, SP, #8
    // 0xa0e118: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xa0e118: stur            x2, [fp, #-8]
    // 0xa0e11c: r0 = _TextSelectionToolbarContainer()
    //     0xa0e11c: bl              #0xa0e134  ; Allocate_TextSelectionToolbarContainerStub -> _TextSelectionToolbarContainer (size=0x10)
    // 0xa0e120: ldur            x1, [fp, #-8]
    // 0xa0e124: StoreField: r0->field_b = r1
    //     0xa0e124: stur            w1, [x0, #0xb]
    // 0xa0e128: LeaveFrame
    //     0xa0e128: mov             SP, fp
    //     0xa0e12c: ldp             fp, lr, [SP], #0x10
    // 0xa0e130: ret
    //     0xa0e130: ret             
  }
  _ build(/* No info */) {
    // ** addr: 0xaa09f8, size: 0x19c
    // 0xaa09f8: EnterFrame
    //     0xaa09f8: stp             fp, lr, [SP, #-0x10]!
    //     0xaa09fc: mov             fp, SP
    // 0xaa0a00: AllocStack(0x38)
    //     0xaa0a00: sub             SP, SP, #0x38
    // 0xaa0a04: SetupParameters(TextSelectionToolbar this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xaa0a04: mov             x3, x1
    //     0xaa0a08: mov             x0, x2
    //     0xaa0a0c: stur            x1, [fp, #-8]
    //     0xaa0a10: stur            x2, [fp, #-0x10]
    // 0xaa0a14: CheckStackOverflow
    //     0xaa0a14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa0a18: cmp             SP, x16
    //     0xaa0a1c: b.ls            #0xaa0b8c
    // 0xaa0a20: LoadField: r1 = r3->field_b
    //     0xaa0a20: ldur            w1, [x3, #0xb]
    // 0xaa0a24: DecompressPointer r1
    //     0xaa0a24: add             x1, x1, HEAP, lsl #32
    // 0xaa0a28: r2 = Instance_Offset
    //     0xaa0a28: add             x2, PP, #0x43, lsl #12  ; [pp+0x438c0] Obj!Offset@e2c881
    //     0xaa0a2c: ldr             x2, [x2, #0x8c0]
    // 0xaa0a30: r0 = -()
    //     0xaa0a30: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xaa0a34: mov             x3, x0
    // 0xaa0a38: ldur            x0, [fp, #-8]
    // 0xaa0a3c: stur            x3, [fp, #-0x18]
    // 0xaa0a40: LoadField: r1 = r0->field_f
    //     0xaa0a40: ldur            w1, [x0, #0xf]
    // 0xaa0a44: DecompressPointer r1
    //     0xaa0a44: add             x1, x1, HEAP, lsl #32
    // 0xaa0a48: r2 = Instance_Offset
    //     0xaa0a48: add             x2, PP, #0x43, lsl #12  ; [pp+0x438c8] Obj!Offset@e2c861
    //     0xaa0a4c: ldr             x2, [x2, #0x8c8]
    // 0xaa0a50: r0 = +()
    //     0xaa0a50: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0xaa0a54: ldur            x1, [fp, #-0x10]
    // 0xaa0a58: stur            x0, [fp, #-0x10]
    // 0xaa0a5c: r0 = paddingOf()
    //     0xaa0a5c: bl              #0x9daabc  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::paddingOf
    // 0xaa0a60: LoadField: d0 = r0->field_f
    //     0xaa0a60: ldur            d0, [x0, #0xf]
    // 0xaa0a64: d1 = 8.000000
    //     0xaa0a64: fmov            d1, #8.00000000
    // 0xaa0a68: fadd            d2, d0, d1
    // 0xaa0a6c: ldur            x1, [fp, #-0x18]
    // 0xaa0a70: stur            d2, [fp, #-0x38]
    // 0xaa0a74: LoadField: d0 = r1->field_f
    //     0xaa0a74: ldur            d0, [x1, #0xf]
    // 0xaa0a78: fsub            d3, d0, d1
    // 0xaa0a7c: fsub            d0, d3, d2
    // 0xaa0a80: d3 = 44.000000
    //     0xaa0a80: add             x17, PP, #0x43, lsl #12  ; [pp+0x438d0] IMM: double(44) from 0x4046000000000000
    //     0xaa0a84: ldr             d3, [x17, #0x8d0]
    // 0xaa0a88: fcmp            d0, d3
    // 0xaa0a8c: r16 = true
    //     0xaa0a8c: add             x16, NULL, #0x20  ; true
    // 0xaa0a90: r17 = false
    //     0xaa0a90: add             x17, NULL, #0x30  ; false
    // 0xaa0a94: csel            x0, x16, x17, ge
    // 0xaa0a98: stur            x0, [fp, #-0x20]
    // 0xaa0a9c: r0 = Offset()
    //     0xaa0a9c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xaa0aa0: d0 = 8.000000
    //     0xaa0aa0: fmov            d0, #8.00000000
    // 0xaa0aa4: stur            x0, [fp, #-0x28]
    // 0xaa0aa8: StoreField: r0->field_7 = d0
    //     0xaa0aa8: stur            d0, [x0, #7]
    // 0xaa0aac: ldur            d1, [fp, #-0x38]
    // 0xaa0ab0: StoreField: r0->field_f = d1
    //     0xaa0ab0: stur            d1, [x0, #0xf]
    // 0xaa0ab4: r0 = EdgeInsets()
    //     0xaa0ab4: bl              #0x65dd90  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xaa0ab8: d0 = 8.000000
    //     0xaa0ab8: fmov            d0, #8.00000000
    // 0xaa0abc: stur            x0, [fp, #-0x30]
    // 0xaa0ac0: StoreField: r0->field_7 = d0
    //     0xaa0ac0: stur            d0, [x0, #7]
    // 0xaa0ac4: ldur            d1, [fp, #-0x38]
    // 0xaa0ac8: StoreField: r0->field_f = d1
    //     0xaa0ac8: stur            d1, [x0, #0xf]
    // 0xaa0acc: ArrayStore: r0[0] = d0  ; List_8
    //     0xaa0acc: stur            d0, [x0, #0x17]
    // 0xaa0ad0: StoreField: r0->field_1f = d0
    //     0xaa0ad0: stur            d0, [x0, #0x1f]
    // 0xaa0ad4: ldur            x1, [fp, #-0x18]
    // 0xaa0ad8: ldur            x2, [fp, #-0x28]
    // 0xaa0adc: r0 = -()
    //     0xaa0adc: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xaa0ae0: ldur            x1, [fp, #-0x10]
    // 0xaa0ae4: ldur            x2, [fp, #-0x28]
    // 0xaa0ae8: stur            x0, [fp, #-0x10]
    // 0xaa0aec: r0 = -()
    //     0xaa0aec: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xaa0af0: stur            x0, [fp, #-0x18]
    // 0xaa0af4: r0 = TextSelectionToolbarLayoutDelegate()
    //     0xaa0af4: bl              #0xa97ae4  ; AllocateTextSelectionToolbarLayoutDelegateStub -> TextSelectionToolbarLayoutDelegate (size=0x18)
    // 0xaa0af8: mov             x1, x0
    // 0xaa0afc: ldur            x0, [fp, #-0x10]
    // 0xaa0b00: stur            x1, [fp, #-0x28]
    // 0xaa0b04: StoreField: r1->field_b = r0
    //     0xaa0b04: stur            w0, [x1, #0xb]
    // 0xaa0b08: ldur            x0, [fp, #-0x18]
    // 0xaa0b0c: StoreField: r1->field_f = r0
    //     0xaa0b0c: stur            w0, [x1, #0xf]
    // 0xaa0b10: ldur            x0, [fp, #-0x20]
    // 0xaa0b14: StoreField: r1->field_13 = r0
    //     0xaa0b14: stur            w0, [x1, #0x13]
    // 0xaa0b18: ldur            x2, [fp, #-8]
    // 0xaa0b1c: LoadField: r3 = r2->field_13
    //     0xaa0b1c: ldur            w3, [x2, #0x13]
    // 0xaa0b20: DecompressPointer r3
    //     0xaa0b20: add             x3, x3, HEAP, lsl #32
    // 0xaa0b24: stur            x3, [fp, #-0x10]
    // 0xaa0b28: r0 = _TextSelectionToolbarOverflowable()
    //     0xaa0b28: bl              #0xaa0b94  ; Allocate_TextSelectionToolbarOverflowableStub -> _TextSelectionToolbarOverflowable (size=0x18)
    // 0xaa0b2c: mov             x1, x0
    // 0xaa0b30: ldur            x0, [fp, #-0x20]
    // 0xaa0b34: stur            x1, [fp, #-8]
    // 0xaa0b38: StoreField: r1->field_f = r0
    //     0xaa0b38: stur            w0, [x1, #0xf]
    // 0xaa0b3c: r0 = Closure: (BuildContext, Widget) => Widget from Function '_defaultToolbarBuilder@614142888': static.
    //     0xaa0b3c: add             x0, PP, #0x39, lsl #12  ; [pp+0x39a48] Closure: (BuildContext, Widget) => Widget from Function '_defaultToolbarBuilder@614142888': static. (0x7e54fb40e098)
    //     0xaa0b40: ldr             x0, [x0, #0xa48]
    // 0xaa0b44: StoreField: r1->field_13 = r0
    //     0xaa0b44: stur            w0, [x1, #0x13]
    // 0xaa0b48: ldur            x0, [fp, #-0x10]
    // 0xaa0b4c: StoreField: r1->field_b = r0
    //     0xaa0b4c: stur            w0, [x1, #0xb]
    // 0xaa0b50: r0 = CustomSingleChildLayout()
    //     0xaa0b50: bl              #0x9e6a80  ; AllocateCustomSingleChildLayoutStub -> CustomSingleChildLayout (size=0x14)
    // 0xaa0b54: mov             x1, x0
    // 0xaa0b58: ldur            x0, [fp, #-0x28]
    // 0xaa0b5c: stur            x1, [fp, #-0x10]
    // 0xaa0b60: StoreField: r1->field_f = r0
    //     0xaa0b60: stur            w0, [x1, #0xf]
    // 0xaa0b64: ldur            x0, [fp, #-8]
    // 0xaa0b68: StoreField: r1->field_b = r0
    //     0xaa0b68: stur            w0, [x1, #0xb]
    // 0xaa0b6c: r0 = Padding()
    //     0xaa0b6c: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaa0b70: ldur            x1, [fp, #-0x30]
    // 0xaa0b74: StoreField: r0->field_f = r1
    //     0xaa0b74: stur            w1, [x0, #0xf]
    // 0xaa0b78: ldur            x1, [fp, #-0x10]
    // 0xaa0b7c: StoreField: r0->field_b = r1
    //     0xaa0b7c: stur            w1, [x0, #0xb]
    // 0xaa0b80: LeaveFrame
    //     0xaa0b80: mov             SP, fp
    //     0xaa0b84: ldp             fp, lr, [SP], #0x10
    // 0xaa0b88: ret
    //     0xaa0b88: ret             
    // 0xaa0b8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa0b8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa0b90: b               #0xaa0a20
  }
}
