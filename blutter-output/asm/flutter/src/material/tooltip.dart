// lib: , url: package:flutter/src/material/tooltip.dart

// class id: 1048972, size: 0x8
class :: {
}

// class id: 3148, size: 0x78, field offset: 0x78
class _RenderExclusiveMouseRegion extends RenderMouseRegion {

  _ hitTest(/* No info */) {
    // ** addr: 0x89a100, size: 0x144
    // 0x89a100: EnterFrame
    //     0x89a100: stp             fp, lr, [SP, #-0x10]!
    //     0x89a104: mov             fp, SP
    // 0x89a108: AllocStack(0x20)
    //     0x89a108: sub             SP, SP, #0x20
    // 0x89a10c: r0 = false
    //     0x89a10c: add             x0, NULL, #0x30  ; false
    // 0x89a110: mov             x4, x1
    // 0x89a114: stur            x2, [fp, #-0x18]
    // 0x89a118: mov             x16, x3
    // 0x89a11c: mov             x3, x2
    // 0x89a120: mov             x2, x16
    // 0x89a124: stur            x1, [fp, #-0x10]
    // 0x89a128: stur            x2, [fp, #-0x20]
    // 0x89a12c: CheckStackOverflow
    //     0x89a12c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89a130: cmp             SP, x16
    //     0x89a134: b.ls            #0x89a23c
    // 0x89a138: r5 = LoadStaticField(0xb64)
    //     0x89a138: ldr             x5, [THR, #0x68]  ; THR::field_table_values
    //     0x89a13c: ldr             x5, [x5, #0x16c8]
    // 0x89a140: stur            x5, [fp, #-8]
    // 0x89a144: StoreStaticField(0xb64, r0)
    //     0x89a144: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x89a148: str             x0, [x1, #0x16c8]
    // 0x89a14c: mov             x1, x4
    // 0x89a150: r0 = size()
    //     0x89a150: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x89a154: mov             x1, x0
    // 0x89a158: ldur            x2, [fp, #-0x20]
    // 0x89a15c: r0 = contains()
    //     0x89a15c: bl              #0x89a250  ; [dart:ui] Size::contains
    // 0x89a160: tbnz            w0, #4, #0x89a20c
    // 0x89a164: ldur            x1, [fp, #-0x10]
    // 0x89a168: ldur            x2, [fp, #-0x18]
    // 0x89a16c: ldur            x3, [fp, #-0x20]
    // 0x89a170: r0 = hitTestChildren()
    //     0x89a170: bl              #0x7fce38  ; [package:flutter/src/widgets/layout_builder.dart] _RenderLayoutBuilder::hitTestChildren
    // 0x89a174: tbnz            w0, #4, #0x89a184
    // 0x89a178: ldur            x0, [fp, #-0x10]
    // 0x89a17c: r2 = true
    //     0x89a17c: add             x2, NULL, #0x20  ; true
    // 0x89a180: b               #0x89a1a8
    // 0x89a184: ldur            x0, [fp, #-0x10]
    // 0x89a188: LoadField: r1 = r0->field_5b
    //     0x89a188: ldur            w1, [x0, #0x5b]
    // 0x89a18c: DecompressPointer r1
    //     0x89a18c: add             x1, x1, HEAP, lsl #32
    // 0x89a190: r16 = Instance_HitTestBehavior
    //     0x89a190: add             x16, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0x89a194: ldr             x16, [x16, #0x1c8]
    // 0x89a198: cmp             w1, w16
    // 0x89a19c: r16 = true
    //     0x89a19c: add             x16, NULL, #0x20  ; true
    // 0x89a1a0: r17 = false
    //     0x89a1a0: add             x17, NULL, #0x30  ; false
    // 0x89a1a4: csel            x2, x16, x17, eq
    // 0x89a1a8: stur            x2, [fp, #-0x20]
    // 0x89a1ac: tbz             w2, #4, #0x89a1c8
    // 0x89a1b0: LoadField: r1 = r0->field_5b
    //     0x89a1b0: ldur            w1, [x0, #0x5b]
    // 0x89a1b4: DecompressPointer r1
    //     0x89a1b4: add             x1, x1, HEAP, lsl #32
    // 0x89a1b8: r16 = Instance_HitTestBehavior
    //     0x89a1b8: add             x16, PP, #0x25, lsl #12  ; [pp+0x251d0] Obj!HitTestBehavior@e358e1
    //     0x89a1bc: ldr             x16, [x16, #0x1d0]
    // 0x89a1c0: cmp             w1, w16
    // 0x89a1c4: b.ne            #0x89a204
    // 0x89a1c8: r1 = LoadStaticField(0xb68)
    //     0x89a1c8: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x89a1cc: ldr             x1, [x1, #0x16d0]
    // 0x89a1d0: tbz             w1, #4, #0x89a204
    // 0x89a1d4: r3 = true
    //     0x89a1d4: add             x3, NULL, #0x20  ; true
    // 0x89a1d8: StoreStaticField(0xb68, r3)
    //     0x89a1d8: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x89a1dc: str             x3, [x1, #0x16d0]
    // 0x89a1e0: r1 = <RenderBox>
    //     0x89a1e0: add             x1, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x89a1e4: ldr             x1, [x1, #0x1d8]
    // 0x89a1e8: r0 = BoxHitTestEntry()
    //     0x89a1e8: bl              #0x89a244  ; AllocateBoxHitTestEntryStub -> BoxHitTestEntry (size=0x14)
    // 0x89a1ec: mov             x1, x0
    // 0x89a1f0: ldur            x0, [fp, #-0x10]
    // 0x89a1f4: StoreField: r1->field_b = r0
    //     0x89a1f4: stur            w0, [x1, #0xb]
    // 0x89a1f8: mov             x2, x1
    // 0x89a1fc: ldur            x1, [fp, #-0x18]
    // 0x89a200: r0 = add()
    //     0x89a200: bl              #0x648e20  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::add
    // 0x89a204: ldur            x0, [fp, #-0x20]
    // 0x89a208: b               #0x89a210
    // 0x89a20c: r0 = false
    //     0x89a20c: add             x0, NULL, #0x30  ; false
    // 0x89a210: ldur            x1, [fp, #-8]
    // 0x89a214: tbnz            w1, #4, #0x89a230
    // 0x89a218: r2 = false
    //     0x89a218: add             x2, NULL, #0x30  ; false
    // 0x89a21c: r1 = true
    //     0x89a21c: add             x1, NULL, #0x20  ; true
    // 0x89a220: StoreStaticField(0xb64, r1)
    //     0x89a220: ldr             x3, [THR, #0x68]  ; THR::field_table_values
    //     0x89a224: str             x1, [x3, #0x16c8]
    // 0x89a228: StoreStaticField(0xb68, r2)
    //     0x89a228: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x89a22c: str             x2, [x1, #0x16d0]
    // 0x89a230: LeaveFrame
    //     0x89a230: mov             SP, fp
    //     0x89a234: ldp             fp, lr, [SP], #0x10
    // 0x89a238: ret
    //     0x89a238: ret             
    // 0x89a23c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89a23c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89a240: b               #0x89a138
  }
}

// class id: 3422, size: 0x1c, field offset: 0xc
class _TooltipPositionDelegate extends SingleChildLayoutDelegate {

  _ getPositionForChild(/* No info */) {
    // ** addr: 0xbf6af4, size: 0x40
    // 0xbf6af4: EnterFrame
    //     0xbf6af4: stp             fp, lr, [SP, #-0x10]!
    //     0xbf6af8: mov             fp, SP
    // 0xbf6afc: mov             x0, x1
    // 0xbf6b00: mov             x1, x3
    // 0xbf6b04: CheckStackOverflow
    //     0xbf6b04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf6b08: cmp             SP, x16
    //     0xbf6b0c: b.ls            #0xbf6b2c
    // 0xbf6b10: LoadField: r3 = r0->field_b
    //     0xbf6b10: ldur            w3, [x0, #0xb]
    // 0xbf6b14: DecompressPointer r3
    //     0xbf6b14: add             x3, x3, HEAP, lsl #32
    // 0xbf6b18: LoadField: d0 = r0->field_f
    //     0xbf6b18: ldur            d0, [x0, #0xf]
    // 0xbf6b1c: r0 = positionDependentBox()
    //     0xbf6b1c: bl              #0xbf6b34  ; [package:flutter/src/painting/geometry.dart] ::positionDependentBox
    // 0xbf6b20: LeaveFrame
    //     0xbf6b20: mov             SP, fp
    //     0xbf6b24: ldp             fp, lr, [SP], #0x10
    // 0xbf6b28: ret
    //     0xbf6b28: ret             
    // 0xbf6b2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf6b2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf6b30: b               #0xbf6b10
  }
  _ shouldRelayout(/* No info */) {
    // ** addr: 0xbf895c, size: 0xbc
    // 0xbf895c: EnterFrame
    //     0xbf895c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf8960: mov             fp, SP
    // 0xbf8964: AllocStack(0x20)
    //     0xbf8964: sub             SP, SP, #0x20
    // 0xbf8968: SetupParameters(_TooltipPositionDelegate this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xbf8968: mov             x4, x1
    //     0xbf896c: mov             x3, x2
    //     0xbf8970: stur            x1, [fp, #-8]
    //     0xbf8974: stur            x2, [fp, #-0x10]
    // 0xbf8978: CheckStackOverflow
    //     0xbf8978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf897c: cmp             SP, x16
    //     0xbf8980: b.ls            #0xbf8a10
    // 0xbf8984: mov             x0, x3
    // 0xbf8988: r2 = Null
    //     0xbf8988: mov             x2, NULL
    // 0xbf898c: r1 = Null
    //     0xbf898c: mov             x1, NULL
    // 0xbf8990: r4 = 60
    //     0xbf8990: movz            x4, #0x3c
    // 0xbf8994: branchIfSmi(r0, 0xbf89a0)
    //     0xbf8994: tbz             w0, #0, #0xbf89a0
    // 0xbf8998: r4 = LoadClassIdInstr(r0)
    //     0xbf8998: ldur            x4, [x0, #-1]
    //     0xbf899c: ubfx            x4, x4, #0xc, #0x14
    // 0xbf89a0: cmp             x4, #0xd5e
    // 0xbf89a4: b.eq            #0xbf89bc
    // 0xbf89a8: r8 = _TooltipPositionDelegate
    //     0xbf89a8: add             x8, PP, #0x58, lsl #12  ; [pp+0x58ab8] Type: _TooltipPositionDelegate
    //     0xbf89ac: ldr             x8, [x8, #0xab8]
    // 0xbf89b0: r3 = Null
    //     0xbf89b0: add             x3, PP, #0x58, lsl #12  ; [pp+0x58ac0] Null
    //     0xbf89b4: ldr             x3, [x3, #0xac0]
    // 0xbf89b8: r0 = DefaultTypeTest()
    //     0xbf89b8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xbf89bc: ldur            x0, [fp, #-8]
    // 0xbf89c0: LoadField: r1 = r0->field_b
    //     0xbf89c0: ldur            w1, [x0, #0xb]
    // 0xbf89c4: DecompressPointer r1
    //     0xbf89c4: add             x1, x1, HEAP, lsl #32
    // 0xbf89c8: ldur            x2, [fp, #-0x10]
    // 0xbf89cc: LoadField: r3 = r2->field_b
    //     0xbf89cc: ldur            w3, [x2, #0xb]
    // 0xbf89d0: DecompressPointer r3
    //     0xbf89d0: add             x3, x3, HEAP, lsl #32
    // 0xbf89d4: stp             x3, x1, [SP]
    // 0xbf89d8: r0 = ==()
    //     0xbf89d8: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0xbf89dc: tbnz            w0, #4, #0xbf89f8
    // 0xbf89e0: ldur            x1, [fp, #-8]
    // 0xbf89e4: ldur            x2, [fp, #-0x10]
    // 0xbf89e8: LoadField: d0 = r1->field_f
    //     0xbf89e8: ldur            d0, [x1, #0xf]
    // 0xbf89ec: LoadField: d1 = r2->field_f
    //     0xbf89ec: ldur            d1, [x2, #0xf]
    // 0xbf89f0: fcmp            d0, d1
    // 0xbf89f4: b.eq            #0xbf8a00
    // 0xbf89f8: r0 = true
    //     0xbf89f8: add             x0, NULL, #0x20  ; true
    // 0xbf89fc: b               #0xbf8a04
    // 0xbf8a00: r0 = false
    //     0xbf8a00: add             x0, NULL, #0x30  ; false
    // 0xbf8a04: LeaveFrame
    //     0xbf8a04: mov             SP, fp
    //     0xbf8a08: ldp             fp, lr, [SP], #0x10
    // 0xbf8a0c: ret
    //     0xbf8a0c: ret             
    // 0xbf8a10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf8a10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf8a14: b               #0xbf8984
  }
}

// class id: 4246, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class _TooltipState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6f6228, size: 0x98
    // 0x6f6228: EnterFrame
    //     0x6f6228: stp             fp, lr, [SP, #-0x10]!
    //     0x6f622c: mov             fp, SP
    // 0x6f6230: AllocStack(0x10)
    //     0x6f6230: sub             SP, SP, #0x10
    // 0x6f6234: SetupParameters(_TooltipState&State&SingleTickerProviderStateMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6f6234: stur            x1, [fp, #-8]
    //     0x6f6238: stur            x2, [fp, #-0x10]
    // 0x6f623c: CheckStackOverflow
    //     0x6f623c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f6240: cmp             SP, x16
    //     0x6f6244: b.ls            #0x6f62b4
    // 0x6f6248: r0 = Ticker()
    //     0x6f6248: bl              #0x6efe64  ; AllocateTickerStub -> Ticker (size=0x1c)
    // 0x6f624c: mov             x1, x0
    // 0x6f6250: r0 = false
    //     0x6f6250: add             x0, NULL, #0x30  ; false
    // 0x6f6254: StoreField: r1->field_b = r0
    //     0x6f6254: stur            w0, [x1, #0xb]
    // 0x6f6258: ldur            x0, [fp, #-0x10]
    // 0x6f625c: StoreField: r1->field_13 = r0
    //     0x6f625c: stur            w0, [x1, #0x13]
    // 0x6f6260: mov             x0, x1
    // 0x6f6264: ldur            x2, [fp, #-8]
    // 0x6f6268: StoreField: r2->field_13 = r0
    //     0x6f6268: stur            w0, [x2, #0x13]
    //     0x6f626c: ldurb           w16, [x2, #-1]
    //     0x6f6270: ldurb           w17, [x0, #-1]
    //     0x6f6274: and             x16, x17, x16, lsr #2
    //     0x6f6278: tst             x16, HEAP, lsr #32
    //     0x6f627c: b.eq            #0x6f6284
    //     0x6f6280: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6f6284: mov             x1, x2
    // 0x6f6288: r0 = _updateTickerModeNotifier()
    //     0x6f6288: bl              #0x6f62e4  ; [package:flutter/src/material/tooltip.dart] _TooltipState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6f628c: ldur            x1, [fp, #-8]
    // 0x6f6290: r0 = _updateTicker()
    //     0x6f6290: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6f6294: ldur            x1, [fp, #-8]
    // 0x6f6298: LoadField: r0 = r1->field_13
    //     0x6f6298: ldur            w0, [x1, #0x13]
    // 0x6f629c: DecompressPointer r0
    //     0x6f629c: add             x0, x0, HEAP, lsl #32
    // 0x6f62a0: cmp             w0, NULL
    // 0x6f62a4: b.eq            #0x6f62bc
    // 0x6f62a8: LeaveFrame
    //     0x6f62a8: mov             SP, fp
    //     0x6f62ac: ldp             fp, lr, [SP], #0x10
    // 0x6f62b0: ret
    //     0x6f62b0: ret             
    // 0x6f62b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f62b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f62b8: b               #0x6f6248
    // 0x6f62bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f62bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6f62e4, size: 0x124
    // 0x6f62e4: EnterFrame
    //     0x6f62e4: stp             fp, lr, [SP, #-0x10]!
    //     0x6f62e8: mov             fp, SP
    // 0x6f62ec: AllocStack(0x18)
    //     0x6f62ec: sub             SP, SP, #0x18
    // 0x6f62f0: SetupParameters(_TooltipState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6f62f0: mov             x2, x1
    //     0x6f62f4: stur            x1, [fp, #-8]
    // 0x6f62f8: CheckStackOverflow
    //     0x6f62f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f62fc: cmp             SP, x16
    //     0x6f6300: b.ls            #0x6f63fc
    // 0x6f6304: LoadField: r1 = r2->field_f
    //     0x6f6304: ldur            w1, [x2, #0xf]
    // 0x6f6308: DecompressPointer r1
    //     0x6f6308: add             x1, x1, HEAP, lsl #32
    // 0x6f630c: cmp             w1, NULL
    // 0x6f6310: b.eq            #0x6f6404
    // 0x6f6314: r0 = getNotifier()
    //     0x6f6314: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6f6318: mov             x3, x0
    // 0x6f631c: ldur            x0, [fp, #-8]
    // 0x6f6320: stur            x3, [fp, #-0x18]
    // 0x6f6324: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6f6324: ldur            w4, [x0, #0x17]
    // 0x6f6328: DecompressPointer r4
    //     0x6f6328: add             x4, x4, HEAP, lsl #32
    // 0x6f632c: stur            x4, [fp, #-0x10]
    // 0x6f6330: cmp             w3, w4
    // 0x6f6334: b.ne            #0x6f6348
    // 0x6f6338: r0 = Null
    //     0x6f6338: mov             x0, NULL
    // 0x6f633c: LeaveFrame
    //     0x6f633c: mov             SP, fp
    //     0x6f6340: ldp             fp, lr, [SP], #0x10
    // 0x6f6344: ret
    //     0x6f6344: ret             
    // 0x6f6348: cmp             w4, NULL
    // 0x6f634c: b.eq            #0x6f6390
    // 0x6f6350: mov             x2, x0
    // 0x6f6354: r1 = Function '_updateTicker@364311458':.
    //     0x6f6354: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d730] AnonymousClosure: (0x6f6408), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6f6358: ldr             x1, [x1, #0x730]
    // 0x6f635c: r0 = AllocateClosure()
    //     0x6f635c: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f6360: ldur            x1, [fp, #-0x10]
    // 0x6f6364: r2 = LoadClassIdInstr(r1)
    //     0x6f6364: ldur            x2, [x1, #-1]
    //     0x6f6368: ubfx            x2, x2, #0xc, #0x14
    // 0x6f636c: mov             x16, x0
    // 0x6f6370: mov             x0, x2
    // 0x6f6374: mov             x2, x16
    // 0x6f6378: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6f6378: movz            x17, #0xbf5c
    //     0x6f637c: add             lr, x0, x17
    //     0x6f6380: ldr             lr, [x21, lr, lsl #3]
    //     0x6f6384: blr             lr
    // 0x6f6388: ldur            x0, [fp, #-8]
    // 0x6f638c: ldur            x3, [fp, #-0x18]
    // 0x6f6390: mov             x2, x0
    // 0x6f6394: r1 = Function '_updateTicker@364311458':.
    //     0x6f6394: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d730] AnonymousClosure: (0x6f6408), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6f6398: ldr             x1, [x1, #0x730]
    // 0x6f639c: r0 = AllocateClosure()
    //     0x6f639c: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f63a0: ldur            x3, [fp, #-0x18]
    // 0x6f63a4: r1 = LoadClassIdInstr(r3)
    //     0x6f63a4: ldur            x1, [x3, #-1]
    //     0x6f63a8: ubfx            x1, x1, #0xc, #0x14
    // 0x6f63ac: mov             x2, x0
    // 0x6f63b0: mov             x0, x1
    // 0x6f63b4: mov             x1, x3
    // 0x6f63b8: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6f63b8: movz            x17, #0xc407
    //     0x6f63bc: add             lr, x0, x17
    //     0x6f63c0: ldr             lr, [x21, lr, lsl #3]
    //     0x6f63c4: blr             lr
    // 0x6f63c8: ldur            x0, [fp, #-0x18]
    // 0x6f63cc: ldur            x1, [fp, #-8]
    // 0x6f63d0: ArrayStore: r1[0] = r0  ; List_4
    //     0x6f63d0: stur            w0, [x1, #0x17]
    //     0x6f63d4: ldurb           w16, [x1, #-1]
    //     0x6f63d8: ldurb           w17, [x0, #-1]
    //     0x6f63dc: and             x16, x17, x16, lsr #2
    //     0x6f63e0: tst             x16, HEAP, lsr #32
    //     0x6f63e4: b.eq            #0x6f63ec
    //     0x6f63e8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6f63ec: r0 = Null
    //     0x6f63ec: mov             x0, NULL
    // 0x6f63f0: LeaveFrame
    //     0x6f63f0: mov             SP, fp
    //     0x6f63f4: ldp             fp, lr, [SP], #0x10
    // 0x6f63f8: ret
    //     0x6f63f8: ret             
    // 0x6f63fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f63fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f6400: b               #0x6f6304
    // 0x6f6404: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f6404: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTicker(dynamic) {
    // ** addr: 0x6f6408, size: 0x38
    // 0x6f6408: EnterFrame
    //     0x6f6408: stp             fp, lr, [SP, #-0x10]!
    //     0x6f640c: mov             fp, SP
    // 0x6f6410: ldr             x0, [fp, #0x10]
    // 0x6f6414: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6f6414: ldur            w1, [x0, #0x17]
    // 0x6f6418: DecompressPointer r1
    //     0x6f6418: add             x1, x1, HEAP, lsl #32
    // 0x6f641c: CheckStackOverflow
    //     0x6f641c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f6420: cmp             SP, x16
    //     0x6f6424: b.ls            #0x6f6438
    // 0x6f6428: r0 = _updateTicker()
    //     0x6f6428: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6f642c: LeaveFrame
    //     0x6f642c: mov             SP, fp
    //     0x6f6430: ldp             fp, lr, [SP], #0x10
    // 0x6f6434: ret
    //     0x6f6434: ret             
    // 0x6f6438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f6438: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f643c: b               #0x6f6428
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7f060, size: 0x94
    // 0xa7f060: EnterFrame
    //     0xa7f060: stp             fp, lr, [SP, #-0x10]!
    //     0xa7f064: mov             fp, SP
    // 0xa7f068: AllocStack(0x10)
    //     0xa7f068: sub             SP, SP, #0x10
    // 0xa7f06c: SetupParameters(_TooltipState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa7f06c: mov             x0, x1
    //     0xa7f070: stur            x1, [fp, #-0x10]
    // 0xa7f074: CheckStackOverflow
    //     0xa7f074: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7f078: cmp             SP, x16
    //     0xa7f07c: b.ls            #0xa7f0ec
    // 0xa7f080: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa7f080: ldur            w3, [x0, #0x17]
    // 0xa7f084: DecompressPointer r3
    //     0xa7f084: add             x3, x3, HEAP, lsl #32
    // 0xa7f088: stur            x3, [fp, #-8]
    // 0xa7f08c: cmp             w3, NULL
    // 0xa7f090: b.ne            #0xa7f09c
    // 0xa7f094: mov             x1, x0
    // 0xa7f098: b               #0xa7f0d8
    // 0xa7f09c: mov             x2, x0
    // 0xa7f0a0: r1 = Function '_updateTicker@364311458':.
    //     0xa7f0a0: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d730] AnonymousClosure: (0x6f6408), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0xa7f0a4: ldr             x1, [x1, #0x730]
    // 0xa7f0a8: r0 = AllocateClosure()
    //     0xa7f0a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7f0ac: ldur            x1, [fp, #-8]
    // 0xa7f0b0: r2 = LoadClassIdInstr(r1)
    //     0xa7f0b0: ldur            x2, [x1, #-1]
    //     0xa7f0b4: ubfx            x2, x2, #0xc, #0x14
    // 0xa7f0b8: mov             x16, x0
    // 0xa7f0bc: mov             x0, x2
    // 0xa7f0c0: mov             x2, x16
    // 0xa7f0c4: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa7f0c4: movz            x17, #0xbf5c
    //     0xa7f0c8: add             lr, x0, x17
    //     0xa7f0cc: ldr             lr, [x21, lr, lsl #3]
    //     0xa7f0d0: blr             lr
    // 0xa7f0d4: ldur            x1, [fp, #-0x10]
    // 0xa7f0d8: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa7f0d8: stur            NULL, [x1, #0x17]
    // 0xa7f0dc: r0 = Null
    //     0xa7f0dc: mov             x0, NULL
    // 0xa7f0e0: LeaveFrame
    //     0xa7f0e0: mov             SP, fp
    //     0xa7f0e4: ldp             fp, lr, [SP], #0x10
    // 0xa7f0e8: ret
    //     0xa7f0e8: ret             
    // 0xa7f0ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7f0ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7f0f0: b               #0xa7f080
  }
  _ activate(/* No info */) {
    // ** addr: 0xa8548c, size: 0x48
    // 0xa8548c: EnterFrame
    //     0xa8548c: stp             fp, lr, [SP, #-0x10]!
    //     0xa85490: mov             fp, SP
    // 0xa85494: AllocStack(0x8)
    //     0xa85494: sub             SP, SP, #8
    // 0xa85498: SetupParameters(_TooltipState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa85498: mov             x0, x1
    //     0xa8549c: stur            x1, [fp, #-8]
    // 0xa854a0: CheckStackOverflow
    //     0xa854a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa854a4: cmp             SP, x16
    //     0xa854a8: b.ls            #0xa854cc
    // 0xa854ac: mov             x1, x0
    // 0xa854b0: r0 = _updateTickerModeNotifier()
    //     0xa854b0: bl              #0x6f62e4  ; [package:flutter/src/material/tooltip.dart] _TooltipState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa854b4: ldur            x1, [fp, #-8]
    // 0xa854b8: r0 = _updateTicker()
    //     0xa854b8: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0xa854bc: r0 = Null
    //     0xa854bc: mov             x0, NULL
    // 0xa854c0: LeaveFrame
    //     0xa854c0: mov             SP, fp
    //     0xa854c4: ldp             fp, lr, [SP], #0x10
    // 0xa854c8: ret
    //     0xa854c8: ret             
    // 0xa854cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa854cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa854d0: b               #0xa854ac
  }
}

// class id: 4247, size: 0x44, field offset: 0x1c
class TooltipState extends _TooltipState&State&SingleTickerProviderStateMixin {

  late TooltipThemeData _tooltipTheme; // offset: 0x24
  late bool _visible; // offset: 0x20

  [closure] void _handlePressUp(dynamic) {
    // ** addr: 0x85e5c0, size: 0x38
    // 0x85e5c0: EnterFrame
    //     0x85e5c0: stp             fp, lr, [SP, #-0x10]!
    //     0x85e5c4: mov             fp, SP
    // 0x85e5c8: ldr             x0, [fp, #0x10]
    // 0x85e5cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x85e5cc: ldur            w1, [x0, #0x17]
    // 0x85e5d0: DecompressPointer r1
    //     0x85e5d0: add             x1, x1, HEAP, lsl #32
    // 0x85e5d4: CheckStackOverflow
    //     0x85e5d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85e5d8: cmp             SP, x16
    //     0x85e5dc: b.ls            #0x85e5f0
    // 0x85e5e0: r0 = _handlePressUp()
    //     0x85e5e0: bl              #0x85e61c  ; [package:flutter/src/material/tooltip.dart] TooltipState::_handlePressUp
    // 0x85e5e4: LeaveFrame
    //     0x85e5e4: mov             SP, fp
    //     0x85e5e8: ldp             fp, lr, [SP], #0x10
    // 0x85e5ec: ret
    //     0x85e5ec: ret             
    // 0x85e5f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85e5f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85e5f4: b               #0x85e5e0
  }
  _ _handlePressUp(/* No info */) {
    // ** addr: 0x85e61c, size: 0x80
    // 0x85e61c: EnterFrame
    //     0x85e61c: stp             fp, lr, [SP, #-0x10]!
    //     0x85e620: mov             fp, SP
    // 0x85e624: AllocStack(0x8)
    //     0x85e624: sub             SP, SP, #8
    // 0x85e628: SetupParameters(TooltipState this /* r1 => r0, fp-0x8 */)
    //     0x85e628: mov             x0, x1
    //     0x85e62c: stur            x1, [fp, #-8]
    // 0x85e630: CheckStackOverflow
    //     0x85e630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85e634: cmp             SP, x16
    //     0x85e638: b.ls            #0x85e694
    // 0x85e63c: LoadField: r1 = r0->field_3b
    //     0x85e63c: ldur            w1, [x0, #0x3b]
    // 0x85e640: DecompressPointer r1
    //     0x85e640: add             x1, x1, HEAP, lsl #32
    // 0x85e644: LoadField: r2 = r1->field_13
    //     0x85e644: ldur            w2, [x1, #0x13]
    // 0x85e648: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x85e648: ldur            w3, [x1, #0x17]
    // 0x85e64c: r1 = LoadInt32Instr(r2)
    //     0x85e64c: sbfx            x1, x2, #1, #0x1f
    // 0x85e650: r2 = LoadInt32Instr(r3)
    //     0x85e650: sbfx            x2, x3, #1, #0x1f
    // 0x85e654: sub             x3, x1, x2
    // 0x85e658: cbz             x3, #0x85e66c
    // 0x85e65c: r0 = Null
    //     0x85e65c: mov             x0, NULL
    // 0x85e660: LeaveFrame
    //     0x85e660: mov             SP, fp
    //     0x85e664: ldp             fp, lr, [SP], #0x10
    // 0x85e668: ret
    //     0x85e668: ret             
    // 0x85e66c: mov             x1, x0
    // 0x85e670: r0 = _showDuration()
    //     0x85e670: bl              #0x85efbc  ; [package:flutter/src/material/tooltip.dart] TooltipState::_showDuration
    // 0x85e674: ldur            x1, [fp, #-8]
    // 0x85e678: r2 = Instance_Duration
    //     0x85e678: add             x2, PP, #0x38, lsl #12  ; [pp+0x38308] Obj!Duration@e3a181
    //     0x85e67c: ldr             x2, [x2, #0x308]
    // 0x85e680: r0 = _scheduleDismissTooltip()
    //     0x85e680: bl              #0x85e69c  ; [package:flutter/src/material/tooltip.dart] TooltipState::_scheduleDismissTooltip
    // 0x85e684: r0 = Null
    //     0x85e684: mov             x0, NULL
    // 0x85e688: LeaveFrame
    //     0x85e688: mov             SP, fp
    //     0x85e68c: ldp             fp, lr, [SP], #0x10
    // 0x85e690: ret
    //     0x85e690: ret             
    // 0x85e694: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85e694: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85e698: b               #0x85e63c
  }
  _ _scheduleDismissTooltip(/* No info */) {
    // ** addr: 0x85e69c, size: 0xf4
    // 0x85e69c: EnterFrame
    //     0x85e69c: stp             fp, lr, [SP, #-0x10]!
    //     0x85e6a0: mov             fp, SP
    // 0x85e6a4: AllocStack(0x10)
    //     0x85e6a4: sub             SP, SP, #0x10
    // 0x85e6a8: SetupParameters(TooltipState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x85e6a8: mov             x0, x1
    //     0x85e6ac: stur            x1, [fp, #-8]
    //     0x85e6b0: stur            x2, [fp, #-0x10]
    // 0x85e6b4: CheckStackOverflow
    //     0x85e6b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85e6b8: cmp             SP, x16
    //     0x85e6bc: b.ls            #0x85e788
    // 0x85e6c0: LoadField: r1 = r0->field_27
    //     0x85e6c0: ldur            w1, [x0, #0x27]
    // 0x85e6c4: DecompressPointer r1
    //     0x85e6c4: add             x1, x1, HEAP, lsl #32
    // 0x85e6c8: cmp             w1, NULL
    // 0x85e6cc: b.eq            #0x85e6d8
    // 0x85e6d0: r0 = cancel()
    //     0x85e6d0: bl              #0x5fe740  ; [dart:isolate] _Timer::cancel
    // 0x85e6d4: ldur            x0, [fp, #-8]
    // 0x85e6d8: StoreField: r0->field_27 = rNULL
    //     0x85e6d8: stur            NULL, [x0, #0x27]
    // 0x85e6dc: LoadField: r1 = r0->field_2b
    //     0x85e6dc: ldur            w1, [x0, #0x2b]
    // 0x85e6e0: DecompressPointer r1
    //     0x85e6e0: add             x1, x1, HEAP, lsl #32
    // 0x85e6e4: cmp             w1, NULL
    // 0x85e6e8: b.ne            #0x85e6f4
    // 0x85e6ec: r0 = Null
    //     0x85e6ec: mov             x0, NULL
    // 0x85e6f0: b               #0x85e6f8
    // 0x85e6f4: r0 = isForwardOrCompleted()
    //     0x85e6f4: bl              #0x85ef3c  ; [package:flutter/src/animation/animation.dart] Animation::isForwardOrCompleted
    // 0x85e6f8: cmp             w0, NULL
    // 0x85e6fc: b.eq            #0x85e778
    // 0x85e700: tbnz            w0, #4, #0x85e778
    // 0x85e704: ldur            x2, [fp, #-0x10]
    // 0x85e708: LoadField: r0 = r2->field_7
    //     0x85e708: ldur            x0, [x2, #7]
    // 0x85e70c: cmp             x0, #0
    // 0x85e710: b.le            #0x85e764
    // 0x85e714: ldur            x0, [fp, #-8]
    // 0x85e718: mov             x1, x0
    // 0x85e71c: r0 = _controller()
    //     0x85e71c: bl              #0x85e790  ; [package:flutter/src/material/tooltip.dart] TooltipState::_controller
    // 0x85e720: mov             x2, x0
    // 0x85e724: r1 = Function 'reverse':.
    //     0x85e724: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a270] AnonymousClosure: (0x655170), in [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse (0x6550d4)
    //     0x85e728: ldr             x1, [x1, #0x270]
    // 0x85e72c: r0 = AllocateClosure()
    //     0x85e72c: bl              #0xec1630  ; AllocateClosureStub
    // 0x85e730: ldur            x2, [fp, #-0x10]
    // 0x85e734: mov             x3, x0
    // 0x85e738: r1 = Null
    //     0x85e738: mov             x1, NULL
    // 0x85e73c: r0 = Timer()
    //     0x85e73c: bl              #0x5f9778  ; [dart:async] Timer::Timer
    // 0x85e740: ldur            x1, [fp, #-8]
    // 0x85e744: StoreField: r1->field_27 = r0
    //     0x85e744: stur            w0, [x1, #0x27]
    //     0x85e748: ldurb           w16, [x1, #-1]
    //     0x85e74c: ldurb           w17, [x0, #-1]
    //     0x85e750: and             x16, x17, x16, lsr #2
    //     0x85e754: tst             x16, HEAP, lsr #32
    //     0x85e758: b.eq            #0x85e760
    //     0x85e75c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x85e760: b               #0x85e778
    // 0x85e764: ldur            x1, [fp, #-8]
    // 0x85e768: r0 = _controller()
    //     0x85e768: bl              #0x85e790  ; [package:flutter/src/material/tooltip.dart] TooltipState::_controller
    // 0x85e76c: mov             x1, x0
    // 0x85e770: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x85e770: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x85e774: r0 = reverse()
    //     0x85e774: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0x85e778: r0 = Null
    //     0x85e778: mov             x0, NULL
    // 0x85e77c: LeaveFrame
    //     0x85e77c: mov             SP, fp
    //     0x85e780: ldp             fp, lr, [SP], #0x10
    // 0x85e784: ret
    //     0x85e784: ret             
    // 0x85e788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85e788: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85e78c: b               #0x85e6c0
  }
  get _ _controller(/* No info */) {
    // ** addr: 0x85e790, size: 0xbc
    // 0x85e790: EnterFrame
    //     0x85e790: stp             fp, lr, [SP, #-0x10]!
    //     0x85e794: mov             fp, SP
    // 0x85e798: AllocStack(0x20)
    //     0x85e798: sub             SP, SP, #0x20
    // 0x85e79c: SetupParameters(TooltipState this /* r1 => r2, fp-0x8 */)
    //     0x85e79c: mov             x2, x1
    //     0x85e7a0: stur            x1, [fp, #-8]
    // 0x85e7a4: CheckStackOverflow
    //     0x85e7a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85e7a8: cmp             SP, x16
    //     0x85e7ac: b.ls            #0x85e844
    // 0x85e7b0: LoadField: r0 = r2->field_2b
    //     0x85e7b0: ldur            w0, [x2, #0x2b]
    // 0x85e7b4: DecompressPointer r0
    //     0x85e7b4: add             x0, x0, HEAP, lsl #32
    // 0x85e7b8: cmp             w0, NULL
    // 0x85e7bc: b.ne            #0x85e838
    // 0x85e7c0: r1 = <double>
    //     0x85e7c0: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x85e7c4: r0 = AnimationController()
    //     0x85e7c4: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x85e7c8: stur            x0, [fp, #-0x10]
    // 0x85e7cc: r16 = Instance_Duration
    //     0x85e7cc: add             x16, PP, #0x25, lsl #12  ; [pp+0x25400] Obj!Duration@e3a171
    //     0x85e7d0: ldr             x16, [x16, #0x400]
    // 0x85e7d4: r30 = Instance_Duration
    //     0x85e7d4: add             lr, PP, #0x3a, lsl #12  ; [pp+0x3a280] Obj!Duration@e3a161
    //     0x85e7d8: ldr             lr, [lr, #0x280]
    // 0x85e7dc: stp             lr, x16, [SP]
    // 0x85e7e0: mov             x1, x0
    // 0x85e7e4: ldur            x2, [fp, #-8]
    // 0x85e7e8: r4 = const [0, 0x4, 0x2, 0x2, duration, 0x2, reverseDuration, 0x3, null]
    //     0x85e7e8: add             x4, PP, #0x22, lsl #12  ; [pp+0x22378] List(9) [0, 0x4, 0x2, 0x2, "duration", 0x2, "reverseDuration", 0x3, Null]
    //     0x85e7ec: ldr             x4, [x4, #0x378]
    // 0x85e7f0: r0 = AnimationController()
    //     0x85e7f0: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x85e7f4: ldur            x2, [fp, #-8]
    // 0x85e7f8: r1 = Function '_handleStatusChanged@621220820':.
    //     0x85e7f8: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a288] AnonymousClosure: (0x85e84c), in [package:flutter/src/material/tooltip.dart] TooltipState::_handleStatusChanged (0x85e888)
    //     0x85e7fc: ldr             x1, [x1, #0x288]
    // 0x85e800: r0 = AllocateClosure()
    //     0x85e800: bl              #0xec1630  ; AllocateClosureStub
    // 0x85e804: ldur            x1, [fp, #-0x10]
    // 0x85e808: mov             x2, x0
    // 0x85e80c: r0 = addStatusListener()
    //     0x85e80c: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0x85e810: ldur            x0, [fp, #-0x10]
    // 0x85e814: ldur            x1, [fp, #-8]
    // 0x85e818: StoreField: r1->field_2b = r0
    //     0x85e818: stur            w0, [x1, #0x2b]
    //     0x85e81c: ldurb           w16, [x1, #-1]
    //     0x85e820: ldurb           w17, [x0, #-1]
    //     0x85e824: and             x16, x17, x16, lsr #2
    //     0x85e828: tst             x16, HEAP, lsr #32
    //     0x85e82c: b.eq            #0x85e834
    //     0x85e830: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x85e834: ldur            x0, [fp, #-0x10]
    // 0x85e838: LeaveFrame
    //     0x85e838: mov             SP, fp
    //     0x85e83c: ldp             fp, lr, [SP], #0x10
    // 0x85e840: ret
    //     0x85e840: ret             
    // 0x85e844: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85e844: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85e848: b               #0x85e7b0
  }
  [closure] void _handleStatusChanged(dynamic, AnimationStatus) {
    // ** addr: 0x85e84c, size: 0x3c
    // 0x85e84c: EnterFrame
    //     0x85e84c: stp             fp, lr, [SP, #-0x10]!
    //     0x85e850: mov             fp, SP
    // 0x85e854: ldr             x0, [fp, #0x18]
    // 0x85e858: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x85e858: ldur            w1, [x0, #0x17]
    // 0x85e85c: DecompressPointer r1
    //     0x85e85c: add             x1, x1, HEAP, lsl #32
    // 0x85e860: CheckStackOverflow
    //     0x85e860: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85e864: cmp             SP, x16
    //     0x85e868: b.ls            #0x85e880
    // 0x85e86c: ldr             x2, [fp, #0x10]
    // 0x85e870: r0 = _handleStatusChanged()
    //     0x85e870: bl              #0x85e888  ; [package:flutter/src/material/tooltip.dart] TooltipState::_handleStatusChanged
    // 0x85e874: LeaveFrame
    //     0x85e874: mov             SP, fp
    //     0x85e878: ldp             fp, lr, [SP], #0x10
    // 0x85e87c: ret
    //     0x85e87c: ret             
    // 0x85e880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85e880: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85e884: b               #0x85e86c
  }
  _ _handleStatusChanged(/* No info */) {
    // ** addr: 0x85e888, size: 0x2a8
    // 0x85e888: EnterFrame
    //     0x85e888: stp             fp, lr, [SP, #-0x10]!
    //     0x85e88c: mov             fp, SP
    // 0x85e890: AllocStack(0x20)
    //     0x85e890: sub             SP, SP, #0x20
    // 0x85e894: SetupParameters(TooltipState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x85e894: mov             x0, x2
    //     0x85e898: stur            x2, [fp, #-0x10]
    //     0x85e89c: mov             x2, x1
    //     0x85e8a0: stur            x1, [fp, #-8]
    // 0x85e8a4: CheckStackOverflow
    //     0x85e8a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85e8a8: cmp             SP, x16
    //     0x85e8ac: b.ls            #0x85eb28
    // 0x85e8b0: LoadField: r1 = r2->field_3f
    //     0x85e8b0: ldur            w1, [x2, #0x3f]
    // 0x85e8b4: DecompressPointer r1
    //     0x85e8b4: add             x1, x1, HEAP, lsl #32
    // 0x85e8b8: r16 = Instance_AnimationStatus
    //     0x85e8b8: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0x85e8bc: cmp             w1, w16
    // 0x85e8c0: r16 = true
    //     0x85e8c0: add             x16, NULL, #0x20  ; true
    // 0x85e8c4: r17 = false
    //     0x85e8c4: add             x17, NULL, #0x30  ; false
    // 0x85e8c8: csel            x3, x16, x17, eq
    // 0x85e8cc: r16 = Instance_AnimationStatus
    //     0x85e8cc: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0x85e8d0: cmp             w0, w16
    // 0x85e8d4: r16 = true
    //     0x85e8d4: add             x16, NULL, #0x20  ; true
    // 0x85e8d8: r17 = false
    //     0x85e8d8: add             x17, NULL, #0x30  ; false
    // 0x85e8dc: csel            x1, x16, x17, eq
    // 0x85e8e0: tbnz            w3, #4, #0x85e8ec
    // 0x85e8e4: r4 = false
    //     0x85e8e4: add             x4, NULL, #0x30  ; false
    // 0x85e8e8: b               #0x85e8f0
    // 0x85e8ec: r4 = true
    //     0x85e8ec: add             x4, NULL, #0x20  ; true
    // 0x85e8f0: tbnz            w4, #4, #0x85e94c
    // 0x85e8f4: tbnz            w1, #4, #0x85e938
    // 0x85e8f8: r0 = InitLateStaticField(0xb60) // [package:flutter/src/material/tooltip.dart] Tooltip::_openedTooltips
    //     0x85e8f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x85e8fc: ldr             x0, [x0, #0x16c0]
    //     0x85e900: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x85e904: cmp             w0, w16
    //     0x85e908: b.ne            #0x85e918
    //     0x85e90c: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a290] Field <Tooltip._openedTooltips@621220820>: static late final (offset: 0xb60)
    //     0x85e910: ldr             x2, [x2, #0x290]
    //     0x85e914: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x85e918: mov             x1, x0
    // 0x85e91c: ldur            x2, [fp, #-8]
    // 0x85e920: r0 = remove()
    //     0x85e920: bl              #0x6ec83c  ; [dart:core] _GrowableList::remove
    // 0x85e924: ldur            x0, [fp, #-8]
    // 0x85e928: LoadField: r1 = r0->field_1b
    //     0x85e928: ldur            w1, [x0, #0x1b]
    // 0x85e92c: DecompressPointer r1
    //     0x85e92c: add             x1, x1, HEAP, lsl #32
    // 0x85e930: r0 = hide()
    //     0x85e930: bl              #0x85ee2c  ; [package:flutter/src/widgets/overlay.dart] OverlayPortalController::hide
    // 0x85e934: b               #0x85eaf4
    // 0x85e938: mov             x6, x1
    // 0x85e93c: mov             x2, x1
    // 0x85e940: r5 = true
    //     0x85e940: add             x5, NULL, #0x20  ; true
    // 0x85e944: r0 = true
    //     0x85e944: add             x0, NULL, #0x20  ; true
    // 0x85e948: b               #0x85e95c
    // 0x85e94c: r6 = Null
    //     0x85e94c: mov             x6, NULL
    // 0x85e950: r5 = false
    //     0x85e950: add             x5, NULL, #0x30  ; false
    // 0x85e954: r2 = Null
    //     0x85e954: mov             x2, NULL
    // 0x85e958: r0 = false
    //     0x85e958: add             x0, NULL, #0x30  ; false
    // 0x85e95c: tbnz            w3, #4, #0x85ea58
    // 0x85e960: tbnz            w0, #4, #0x85e96c
    // 0x85e964: mov             x0, x2
    // 0x85e968: b               #0x85e974
    // 0x85e96c: mov             x2, x1
    // 0x85e970: mov             x0, x1
    // 0x85e974: r16 = false
    //     0x85e974: add             x16, NULL, #0x30  ; false
    // 0x85e978: cmp             w2, w16
    // 0x85e97c: r16 = true
    //     0x85e97c: add             x16, NULL, #0x20  ; true
    // 0x85e980: r17 = false
    //     0x85e980: add             x17, NULL, #0x30  ; false
    // 0x85e984: csel            x7, x16, x17, eq
    // 0x85e988: tbnz            w7, #4, #0x85ea48
    // 0x85e98c: ldur            x0, [fp, #-8]
    // 0x85e990: LoadField: r1 = r0->field_1b
    //     0x85e990: ldur            w1, [x0, #0x1b]
    // 0x85e994: DecompressPointer r1
    //     0x85e994: add             x1, x1, HEAP, lsl #32
    // 0x85e998: r0 = show()
    //     0x85e998: bl              #0x85ec08  ; [package:flutter/src/widgets/overlay.dart] OverlayPortalController::show
    // 0x85e99c: r0 = InitLateStaticField(0xb60) // [package:flutter/src/material/tooltip.dart] Tooltip::_openedTooltips
    //     0x85e99c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x85e9a0: ldr             x0, [x0, #0x16c0]
    //     0x85e9a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x85e9a8: cmp             w0, w16
    //     0x85e9ac: b.ne            #0x85e9bc
    //     0x85e9b0: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a290] Field <Tooltip._openedTooltips@621220820>: static late final (offset: 0xb60)
    //     0x85e9b4: ldr             x2, [x2, #0x290]
    //     0x85e9b8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x85e9bc: stur            x0, [fp, #-0x20]
    // 0x85e9c0: LoadField: r1 = r0->field_b
    //     0x85e9c0: ldur            w1, [x0, #0xb]
    // 0x85e9c4: LoadField: r2 = r0->field_f
    //     0x85e9c4: ldur            w2, [x0, #0xf]
    // 0x85e9c8: DecompressPointer r2
    //     0x85e9c8: add             x2, x2, HEAP, lsl #32
    // 0x85e9cc: LoadField: r3 = r2->field_b
    //     0x85e9cc: ldur            w3, [x2, #0xb]
    // 0x85e9d0: r2 = LoadInt32Instr(r1)
    //     0x85e9d0: sbfx            x2, x1, #1, #0x1f
    // 0x85e9d4: stur            x2, [fp, #-0x18]
    // 0x85e9d8: r1 = LoadInt32Instr(r3)
    //     0x85e9d8: sbfx            x1, x3, #1, #0x1f
    // 0x85e9dc: cmp             x2, x1
    // 0x85e9e0: b.ne            #0x85e9ec
    // 0x85e9e4: mov             x1, x0
    // 0x85e9e8: r0 = _growToNextCapacity()
    //     0x85e9e8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x85e9ec: ldur            x0, [fp, #-0x20]
    // 0x85e9f0: ldur            x2, [fp, #-0x18]
    // 0x85e9f4: add             x1, x2, #1
    // 0x85e9f8: lsl             x3, x1, #1
    // 0x85e9fc: StoreField: r0->field_b = r3
    //     0x85e9fc: stur            w3, [x0, #0xb]
    // 0x85ea00: LoadField: r1 = r0->field_f
    //     0x85ea00: ldur            w1, [x0, #0xf]
    // 0x85ea04: DecompressPointer r1
    //     0x85ea04: add             x1, x1, HEAP, lsl #32
    // 0x85ea08: ldur            x0, [fp, #-8]
    // 0x85ea0c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x85ea0c: add             x25, x1, x2, lsl #2
    //     0x85ea10: add             x25, x25, #0xf
    //     0x85ea14: str             w0, [x25]
    //     0x85ea18: tbz             w0, #0, #0x85ea34
    //     0x85ea1c: ldurb           w16, [x1, #-1]
    //     0x85ea20: ldurb           w17, [x0, #-1]
    //     0x85ea24: and             x16, x17, x16, lsr #2
    //     0x85ea28: tst             x16, HEAP, lsr #32
    //     0x85ea2c: b.eq            #0x85ea34
    //     0x85ea30: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x85ea34: ldur            x1, [fp, #-8]
    // 0x85ea38: r0 = _tooltipMessage()
    //     0x85ea38: bl              #0x85ebb0  ; [package:flutter/src/material/tooltip.dart] TooltipState::_tooltipMessage
    // 0x85ea3c: mov             x1, x0
    // 0x85ea40: r0 = tooltip()
    //     0x85ea40: bl              #0x85eb30  ; [package:flutter/src/semantics/semantics_service.dart] SemanticsService::tooltip
    // 0x85ea44: b               #0x85eaf4
    // 0x85ea48: mov             x9, x0
    // 0x85ea4c: r8 = true
    //     0x85ea4c: add             x8, NULL, #0x20  ; true
    // 0x85ea50: r2 = true
    //     0x85ea50: add             x2, NULL, #0x20  ; true
    // 0x85ea54: b               #0x85ea68
    // 0x85ea58: mov             x9, x2
    // 0x85ea5c: mov             x8, x0
    // 0x85ea60: r7 = Null
    //     0x85ea60: mov             x7, NULL
    // 0x85ea64: r2 = false
    //     0x85ea64: add             x2, NULL, #0x30  ; false
    // 0x85ea68: tbnz            w3, #4, #0x85eac0
    // 0x85ea6c: tbnz            w5, #4, #0x85ea7c
    // 0x85ea70: mov             x5, x9
    // 0x85ea74: mov             x3, x8
    // 0x85ea78: b               #0x85eab0
    // 0x85ea7c: tbnz            w8, #4, #0x85ea8c
    // 0x85ea80: mov             x5, x9
    // 0x85ea84: mov             x3, x9
    // 0x85ea88: b               #0x85ea94
    // 0x85ea8c: mov             x5, x1
    // 0x85ea90: mov             x3, x1
    // 0x85ea94: r16 = true
    //     0x85ea94: add             x16, NULL, #0x20  ; true
    // 0x85ea98: cmp             w5, w16
    // 0x85ea9c: r16 = true
    //     0x85ea9c: add             x16, NULL, #0x20  ; true
    // 0x85eaa0: r17 = false
    //     0x85eaa0: add             x17, NULL, #0x30  ; false
    // 0x85eaa4: csel            x6, x16, x17, eq
    // 0x85eaa8: mov             x5, x3
    // 0x85eaac: r3 = true
    //     0x85eaac: add             x3, NULL, #0x20  ; true
    // 0x85eab0: r16 = true
    //     0x85eab0: add             x16, NULL, #0x20  ; true
    // 0x85eab4: cmp             w6, w16
    // 0x85eab8: b.ne            #0x85eac8
    // 0x85eabc: b               #0x85eaf4
    // 0x85eac0: mov             x5, x9
    // 0x85eac4: mov             x3, x8
    // 0x85eac8: tbnz            w4, #4, #0x85eaf4
    // 0x85eacc: tbnz            w2, #4, #0x85eae0
    // 0x85ead0: r16 = true
    //     0x85ead0: add             x16, NULL, #0x20  ; true
    // 0x85ead4: cmp             w7, w16
    // 0x85ead8: b.ne            #0x85eaf4
    // 0x85eadc: b               #0x85eaf4
    // 0x85eae0: tbnz            w3, #4, #0x85eae8
    // 0x85eae4: mov             x1, x5
    // 0x85eae8: r16 = false
    //     0x85eae8: add             x16, NULL, #0x30  ; false
    // 0x85eaec: cmp             w1, w16
    // 0x85eaf0: b.eq            #0x85eaf4
    // 0x85eaf4: ldur            x1, [fp, #-8]
    // 0x85eaf8: ldur            x0, [fp, #-0x10]
    // 0x85eafc: StoreField: r1->field_3f = r0
    //     0x85eafc: stur            w0, [x1, #0x3f]
    //     0x85eb00: ldurb           w16, [x1, #-1]
    //     0x85eb04: ldurb           w17, [x0, #-1]
    //     0x85eb08: and             x16, x17, x16, lsr #2
    //     0x85eb0c: tst             x16, HEAP, lsr #32
    //     0x85eb10: b.eq            #0x85eb18
    //     0x85eb14: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x85eb18: r0 = Null
    //     0x85eb18: mov             x0, NULL
    // 0x85eb1c: LeaveFrame
    //     0x85eb1c: mov             SP, fp
    //     0x85eb20: ldp             fp, lr, [SP], #0x10
    // 0x85eb24: ret
    //     0x85eb24: ret             
    // 0x85eb28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85eb28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85eb2c: b               #0x85e8b0
  }
  get _ _tooltipMessage(/* No info */) {
    // ** addr: 0x85ebb0, size: 0x58
    // 0x85ebb0: EnterFrame
    //     0x85ebb0: stp             fp, lr, [SP, #-0x10]!
    //     0x85ebb4: mov             fp, SP
    // 0x85ebb8: LoadField: r0 = r1->field_b
    //     0x85ebb8: ldur            w0, [x1, #0xb]
    // 0x85ebbc: DecompressPointer r0
    //     0x85ebbc: add             x0, x0, HEAP, lsl #32
    // 0x85ebc0: cmp             w0, NULL
    // 0x85ebc4: b.eq            #0x85ec00
    // 0x85ebc8: LoadField: r1 = r0->field_b
    //     0x85ebc8: ldur            w1, [x0, #0xb]
    // 0x85ebcc: DecompressPointer r1
    //     0x85ebcc: add             x1, x1, HEAP, lsl #32
    // 0x85ebd0: cmp             w1, NULL
    // 0x85ebd4: b.eq            #0x85ebe8
    // 0x85ebd8: mov             x0, x1
    // 0x85ebdc: LeaveFrame
    //     0x85ebdc: mov             SP, fp
    //     0x85ebe0: ldp             fp, lr, [SP], #0x10
    // 0x85ebe4: ret
    //     0x85ebe4: ret             
    // 0x85ebe8: r0 = Null
    //     0x85ebe8: mov             x0, NULL
    // 0x85ebec: cmp             w0, NULL
    // 0x85ebf0: b.eq            #0x85ec04
    // 0x85ebf4: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x85ebf4: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x85ebf8: r0 = Throw()
    //     0x85ebf8: bl              #0xec04b8  ; ThrowStub
    // 0x85ebfc: brk             #0
    // 0x85ec00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x85ec00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x85ec04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x85ec04: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _showDuration(/* No info */) {
    // ** addr: 0x85efbc, size: 0x50
    // 0x85efbc: EnterFrame
    //     0x85efbc: stp             fp, lr, [SP, #-0x10]!
    //     0x85efc0: mov             fp, SP
    // 0x85efc4: LoadField: r2 = r1->field_b
    //     0x85efc4: ldur            w2, [x1, #0xb]
    // 0x85efc8: DecompressPointer r2
    //     0x85efc8: add             x2, x2, HEAP, lsl #32
    // 0x85efcc: cmp             w2, NULL
    // 0x85efd0: b.eq            #0x85effc
    // 0x85efd4: LoadField: r2 = r1->field_23
    //     0x85efd4: ldur            w2, [x1, #0x23]
    // 0x85efd8: DecompressPointer r2
    //     0x85efd8: add             x2, x2, HEAP, lsl #32
    // 0x85efdc: r16 = Sentinel
    //     0x85efdc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x85efe0: cmp             w2, w16
    // 0x85efe4: b.eq            #0x85f000
    // 0x85efe8: r0 = Instance_Duration
    //     0x85efe8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38308] Obj!Duration@e3a181
    //     0x85efec: ldr             x0, [x0, #0x308]
    // 0x85eff0: LeaveFrame
    //     0x85eff0: mov             SP, fp
    //     0x85eff4: ldp             fp, lr, [SP], #0x10
    // 0x85eff8: ret
    //     0x85eff8: ret             
    // 0x85effc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x85effc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x85f000: r9 = _tooltipTheme
    //     0x85f000: add             x9, PP, #0x3a, lsl #12  ; [pp+0x3a2c0] Field <TooltipState._tooltipTheme@621220820>: late (offset: 0x24)
    //     0x85f004: ldr             x9, [x9, #0x2c0]
    // 0x85f008: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x85f008: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x93b4ec, size: 0x6c
    // 0x93b4ec: EnterFrame
    //     0x93b4ec: stp             fp, lr, [SP, #-0x10]!
    //     0x93b4f0: mov             fp, SP
    // 0x93b4f4: AllocStack(0x8)
    //     0x93b4f4: sub             SP, SP, #8
    // 0x93b4f8: SetupParameters(TooltipState this /* r1 => r2 */)
    //     0x93b4f8: mov             x2, x1
    // 0x93b4fc: CheckStackOverflow
    //     0x93b4fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b500: cmp             SP, x16
    //     0x93b504: b.ls            #0x93b54c
    // 0x93b508: r0 = LoadStaticField(0x968)
    //     0x93b508: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93b50c: ldr             x0, [x0, #0x12d0]
    // 0x93b510: cmp             w0, NULL
    // 0x93b514: b.eq            #0x93b554
    // 0x93b518: LoadField: r3 = r0->field_13
    //     0x93b518: ldur            w3, [x0, #0x13]
    // 0x93b51c: DecompressPointer r3
    //     0x93b51c: add             x3, x3, HEAP, lsl #32
    // 0x93b520: stur            x3, [fp, #-8]
    // 0x93b524: r1 = Function '_handleGlobalPointerEvent@621220820':.
    //     0x93b524: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d7e8] AnonymousClosure: (0x93b558), in [package:flutter/src/material/tooltip.dart] TooltipState::_handleGlobalPointerEvent (0x93b594)
    //     0x93b528: ldr             x1, [x1, #0x7e8]
    // 0x93b52c: r0 = AllocateClosure()
    //     0x93b52c: bl              #0xec1630  ; AllocateClosureStub
    // 0x93b530: ldur            x1, [fp, #-8]
    // 0x93b534: mov             x2, x0
    // 0x93b538: r0 = addGlobalRoute()
    //     0x93b538: bl              #0x692728  ; [package:flutter/src/gestures/pointer_router.dart] PointerRouter::addGlobalRoute
    // 0x93b53c: r0 = Null
    //     0x93b53c: mov             x0, NULL
    // 0x93b540: LeaveFrame
    //     0x93b540: mov             SP, fp
    //     0x93b544: ldp             fp, lr, [SP], #0x10
    // 0x93b548: ret
    //     0x93b548: ret             
    // 0x93b54c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b54c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b550: b               #0x93b508
    // 0x93b554: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93b554: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _handleGlobalPointerEvent(dynamic, PointerEvent) {
    // ** addr: 0x93b558, size: 0x3c
    // 0x93b558: EnterFrame
    //     0x93b558: stp             fp, lr, [SP, #-0x10]!
    //     0x93b55c: mov             fp, SP
    // 0x93b560: ldr             x0, [fp, #0x18]
    // 0x93b564: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x93b564: ldur            w1, [x0, #0x17]
    // 0x93b568: DecompressPointer r1
    //     0x93b568: add             x1, x1, HEAP, lsl #32
    // 0x93b56c: CheckStackOverflow
    //     0x93b56c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b570: cmp             SP, x16
    //     0x93b574: b.ls            #0x93b58c
    // 0x93b578: ldr             x2, [fp, #0x10]
    // 0x93b57c: r0 = _handleGlobalPointerEvent()
    //     0x93b57c: bl              #0x93b594  ; [package:flutter/src/material/tooltip.dart] TooltipState::_handleGlobalPointerEvent
    // 0x93b580: LeaveFrame
    //     0x93b580: mov             SP, fp
    //     0x93b584: ldp             fp, lr, [SP], #0x10
    // 0x93b588: ret
    //     0x93b588: ret             
    // 0x93b58c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b58c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b590: b               #0x93b578
  }
  _ _handleGlobalPointerEvent(/* No info */) {
    // ** addr: 0x93b594, size: 0x230
    // 0x93b594: EnterFrame
    //     0x93b594: stp             fp, lr, [SP, #-0x10]!
    //     0x93b598: mov             fp, SP
    // 0x93b59c: AllocStack(0x18)
    //     0x93b59c: sub             SP, SP, #0x18
    // 0x93b5a0: SetupParameters(TooltipState this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x93b5a0: mov             x3, x1
    //     0x93b5a4: stur            x1, [fp, #-0x10]
    //     0x93b5a8: stur            x2, [fp, #-0x18]
    // 0x93b5ac: CheckStackOverflow
    //     0x93b5ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b5b0: cmp             SP, x16
    //     0x93b5b4: b.ls            #0x93b7b4
    // 0x93b5b8: LoadField: r0 = r3->field_37
    //     0x93b5b8: ldur            w0, [x3, #0x37]
    // 0x93b5bc: DecompressPointer r0
    //     0x93b5bc: add             x0, x0, HEAP, lsl #32
    // 0x93b5c0: cmp             w0, NULL
    // 0x93b5c4: b.ne            #0x93b5d0
    // 0x93b5c8: r4 = Null
    //     0x93b5c8: mov             x4, NULL
    // 0x93b5cc: b               #0x93b5dc
    // 0x93b5d0: LoadField: r1 = r0->field_37
    //     0x93b5d0: ldur            w1, [x0, #0x37]
    // 0x93b5d4: DecompressPointer r1
    //     0x93b5d4: add             x1, x1, HEAP, lsl #32
    // 0x93b5d8: mov             x4, x1
    // 0x93b5dc: stur            x4, [fp, #-8]
    // 0x93b5e0: r0 = LoadClassIdInstr(r2)
    //     0x93b5e0: ldur            x0, [x2, #-1]
    //     0x93b5e4: ubfx            x0, x0, #0xc, #0x14
    // 0x93b5e8: mov             x1, x2
    // 0x93b5ec: r0 = GDT[cid_x0 + -0x1000]()
    //     0x93b5ec: sub             lr, x0, #1, lsl #12
    //     0x93b5f0: ldr             lr, [x21, lr, lsl #3]
    //     0x93b5f4: blr             lr
    // 0x93b5f8: mov             x2, x0
    // 0x93b5fc: r0 = BoxInt64Instr(r2)
    //     0x93b5fc: sbfiz           x0, x2, #1, #0x1f
    //     0x93b600: cmp             x2, x0, asr #1
    //     0x93b604: b.eq            #0x93b610
    //     0x93b608: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x93b60c: stur            x2, [x0, #7]
    // 0x93b610: mov             x1, x0
    // 0x93b614: ldur            x0, [fp, #-8]
    // 0x93b618: cmp             w0, w1
    // 0x93b61c: b.eq            #0x93b700
    // 0x93b620: and             w16, w0, w1
    // 0x93b624: branchIfSmi(r16, 0x93b658)
    //     0x93b624: tbz             w16, #0, #0x93b658
    // 0x93b628: r16 = LoadClassIdInstr(r0)
    //     0x93b628: ldur            x16, [x0, #-1]
    //     0x93b62c: ubfx            x16, x16, #0xc, #0x14
    // 0x93b630: cmp             x16, #0x3d
    // 0x93b634: b.ne            #0x93b658
    // 0x93b638: r16 = LoadClassIdInstr(r1)
    //     0x93b638: ldur            x16, [x1, #-1]
    //     0x93b63c: ubfx            x16, x16, #0xc, #0x14
    // 0x93b640: cmp             x16, #0x3d
    // 0x93b644: b.ne            #0x93b658
    // 0x93b648: LoadField: r16 = r0->field_7
    //     0x93b648: ldur            x16, [x0, #7]
    // 0x93b64c: LoadField: r17 = r1->field_7
    //     0x93b64c: ldur            x17, [x1, #7]
    // 0x93b650: cmp             x16, x17
    // 0x93b654: b.eq            #0x93b700
    // 0x93b658: ldur            x2, [fp, #-0x10]
    // 0x93b65c: LoadField: r0 = r2->field_33
    //     0x93b65c: ldur            w0, [x2, #0x33]
    // 0x93b660: DecompressPointer r0
    //     0x93b660: add             x0, x0, HEAP, lsl #32
    // 0x93b664: cmp             w0, NULL
    // 0x93b668: b.ne            #0x93b674
    // 0x93b66c: r4 = Null
    //     0x93b66c: mov             x4, NULL
    // 0x93b670: b               #0x93b680
    // 0x93b674: LoadField: r1 = r0->field_37
    //     0x93b674: ldur            w1, [x0, #0x37]
    // 0x93b678: DecompressPointer r1
    //     0x93b678: add             x1, x1, HEAP, lsl #32
    // 0x93b67c: mov             x4, x1
    // 0x93b680: ldur            x3, [fp, #-0x18]
    // 0x93b684: stur            x4, [fp, #-8]
    // 0x93b688: r0 = LoadClassIdInstr(r3)
    //     0x93b688: ldur            x0, [x3, #-1]
    //     0x93b68c: ubfx            x0, x0, #0xc, #0x14
    // 0x93b690: mov             x1, x3
    // 0x93b694: r0 = GDT[cid_x0 + -0x1000]()
    //     0x93b694: sub             lr, x0, #1, lsl #12
    //     0x93b698: ldr             lr, [x21, lr, lsl #3]
    //     0x93b69c: blr             lr
    // 0x93b6a0: mov             x2, x0
    // 0x93b6a4: r0 = BoxInt64Instr(r2)
    //     0x93b6a4: sbfiz           x0, x2, #1, #0x1f
    //     0x93b6a8: cmp             x2, x0, asr #1
    //     0x93b6ac: b.eq            #0x93b6b8
    //     0x93b6b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x93b6b4: stur            x2, [x0, #7]
    // 0x93b6b8: mov             x1, x0
    // 0x93b6bc: ldur            x0, [fp, #-8]
    // 0x93b6c0: cmp             w0, w1
    // 0x93b6c4: b.eq            #0x93b700
    // 0x93b6c8: and             w16, w0, w1
    // 0x93b6cc: branchIfSmi(r16, 0x93b710)
    //     0x93b6cc: tbz             w16, #0, #0x93b710
    // 0x93b6d0: r16 = LoadClassIdInstr(r0)
    //     0x93b6d0: ldur            x16, [x0, #-1]
    //     0x93b6d4: ubfx            x16, x16, #0xc, #0x14
    // 0x93b6d8: cmp             x16, #0x3d
    // 0x93b6dc: b.ne            #0x93b710
    // 0x93b6e0: r16 = LoadClassIdInstr(r1)
    //     0x93b6e0: ldur            x16, [x1, #-1]
    //     0x93b6e4: ubfx            x16, x16, #0xc, #0x14
    // 0x93b6e8: cmp             x16, #0x3d
    // 0x93b6ec: b.ne            #0x93b710
    // 0x93b6f0: LoadField: r16 = r0->field_7
    //     0x93b6f0: ldur            x16, [x0, #7]
    // 0x93b6f4: LoadField: r17 = r1->field_7
    //     0x93b6f4: ldur            x17, [x1, #7]
    // 0x93b6f8: cmp             x16, x17
    // 0x93b6fc: b.ne            #0x93b710
    // 0x93b700: r0 = Null
    //     0x93b700: mov             x0, NULL
    // 0x93b704: LeaveFrame
    //     0x93b704: mov             SP, fp
    //     0x93b708: ldp             fp, lr, [SP], #0x10
    // 0x93b70c: ret
    //     0x93b70c: ret             
    // 0x93b710: ldur            x0, [fp, #-0x10]
    // 0x93b714: LoadField: r1 = r0->field_27
    //     0x93b714: ldur            w1, [x0, #0x27]
    // 0x93b718: DecompressPointer r1
    //     0x93b718: add             x1, x1, HEAP, lsl #32
    // 0x93b71c: cmp             w1, NULL
    // 0x93b720: b.ne            #0x93b74c
    // 0x93b724: mov             x1, x0
    // 0x93b728: r0 = _controller()
    //     0x93b728: bl              #0x85e790  ; [package:flutter/src/material/tooltip.dart] TooltipState::_controller
    // 0x93b72c: LoadField: r1 = r0->field_43
    //     0x93b72c: ldur            w1, [x0, #0x43]
    // 0x93b730: DecompressPointer r1
    //     0x93b730: add             x1, x1, HEAP, lsl #32
    // 0x93b734: r16 = Sentinel
    //     0x93b734: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x93b738: cmp             w1, w16
    // 0x93b73c: b.eq            #0x93b7bc
    // 0x93b740: r16 = Instance_AnimationStatus
    //     0x93b740: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0x93b744: cmp             w1, w16
    // 0x93b748: b.eq            #0x93b78c
    // 0x93b74c: ldur            x0, [fp, #-0x18]
    // 0x93b750: r2 = Null
    //     0x93b750: mov             x2, NULL
    // 0x93b754: r1 = Null
    //     0x93b754: mov             x1, NULL
    // 0x93b758: cmp             w0, NULL
    // 0x93b75c: b.eq            #0x93b77c
    // 0x93b760: branchIfSmi(r0, 0x93b77c)
    //     0x93b760: tbz             w0, #0, #0x93b77c
    // 0x93b764: r3 = LoadClassIdInstr(r0)
    //     0x93b764: ldur            x3, [x0, #-1]
    //     0x93b768: ubfx            x3, x3, #0xc, #0x14
    // 0x93b76c: cmp             x3, #0xda7
    // 0x93b770: b.eq            #0x93b784
    // 0x93b774: cmp             x3, #0xfd1
    // 0x93b778: b.eq            #0x93b784
    // 0x93b77c: r0 = false
    //     0x93b77c: add             x0, NULL, #0x30  ; false
    // 0x93b780: b               #0x93b788
    // 0x93b784: r0 = true
    //     0x93b784: add             x0, NULL, #0x20  ; true
    // 0x93b788: tbz             w0, #4, #0x93b79c
    // 0x93b78c: r0 = Null
    //     0x93b78c: mov             x0, NULL
    // 0x93b790: LeaveFrame
    //     0x93b790: mov             SP, fp
    //     0x93b794: ldp             fp, lr, [SP], #0x10
    // 0x93b798: ret
    //     0x93b798: ret             
    // 0x93b79c: ldur            x1, [fp, #-0x10]
    // 0x93b7a0: r0 = _handleTapToDismiss()
    //     0x93b7a0: bl              #0x93b7c4  ; [package:flutter/src/material/tooltip.dart] TooltipState::_handleTapToDismiss
    // 0x93b7a4: r0 = Null
    //     0x93b7a4: mov             x0, NULL
    // 0x93b7a8: LeaveFrame
    //     0x93b7a8: mov             SP, fp
    //     0x93b7ac: ldp             fp, lr, [SP], #0x10
    // 0x93b7b0: ret
    //     0x93b7b0: ret             
    // 0x93b7b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b7b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b7b8: b               #0x93b5b8
    // 0x93b7bc: r9 = _status
    //     0x93b7bc: ldr             x9, [PP, #0x4ed8]  ; [pp+0x4ed8] Field <AnimationController._status@441066280>: late (offset: 0x44)
    // 0x93b7c0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x93b7c0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _handleTapToDismiss(/* No info */) {
    // ** addr: 0x93b7c4, size: 0x68
    // 0x93b7c4: EnterFrame
    //     0x93b7c4: stp             fp, lr, [SP, #-0x10]!
    //     0x93b7c8: mov             fp, SP
    // 0x93b7cc: AllocStack(0x8)
    //     0x93b7cc: sub             SP, SP, #8
    // 0x93b7d0: SetupParameters(TooltipState this /* r1 => r0, fp-0x8 */)
    //     0x93b7d0: mov             x0, x1
    //     0x93b7d4: stur            x1, [fp, #-8]
    // 0x93b7d8: CheckStackOverflow
    //     0x93b7d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b7dc: cmp             SP, x16
    //     0x93b7e0: b.ls            #0x93b820
    // 0x93b7e4: LoadField: r1 = r0->field_b
    //     0x93b7e4: ldur            w1, [x0, #0xb]
    // 0x93b7e8: DecompressPointer r1
    //     0x93b7e8: add             x1, x1, HEAP, lsl #32
    // 0x93b7ec: cmp             w1, NULL
    // 0x93b7f0: b.eq            #0x93b828
    // 0x93b7f4: mov             x1, x0
    // 0x93b7f8: r2 = Instance_Duration
    //     0x93b7f8: ldr             x2, [PP, #0x1d38]  ; [pp+0x1d38] Obj!Duration@e3a081
    // 0x93b7fc: r0 = _scheduleDismissTooltip()
    //     0x93b7fc: bl              #0x85e69c  ; [package:flutter/src/material/tooltip.dart] TooltipState::_scheduleDismissTooltip
    // 0x93b800: ldur            x0, [fp, #-8]
    // 0x93b804: LoadField: r1 = r0->field_3b
    //     0x93b804: ldur            w1, [x0, #0x3b]
    // 0x93b808: DecompressPointer r1
    //     0x93b808: add             x1, x1, HEAP, lsl #32
    // 0x93b80c: r0 = clear()
    //     0x93b80c: bl              #0x67a480  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::clear
    // 0x93b810: r0 = Null
    //     0x93b810: mov             x0, NULL
    // 0x93b814: LeaveFrame
    //     0x93b814: mov             SP, fp
    //     0x93b818: ldp             fp, lr, [SP], #0x10
    // 0x93b81c: ret
    //     0x93b81c: ret             
    // 0x93b820: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b820: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b824: b               #0x93b7e4
    // 0x93b828: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93b828: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _handleTapToDismiss(dynamic) {
    // ** addr: 0x93b82c, size: 0x38
    // 0x93b82c: EnterFrame
    //     0x93b82c: stp             fp, lr, [SP, #-0x10]!
    //     0x93b830: mov             fp, SP
    // 0x93b834: ldr             x0, [fp, #0x10]
    // 0x93b838: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x93b838: ldur            w1, [x0, #0x17]
    // 0x93b83c: DecompressPointer r1
    //     0x93b83c: add             x1, x1, HEAP, lsl #32
    // 0x93b840: CheckStackOverflow
    //     0x93b840: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b844: cmp             SP, x16
    //     0x93b848: b.ls            #0x93b85c
    // 0x93b84c: r0 = _handleTapToDismiss()
    //     0x93b84c: bl              #0x93b7c4  ; [package:flutter/src/material/tooltip.dart] TooltipState::_handleTapToDismiss
    // 0x93b850: LeaveFrame
    //     0x93b850: mov             SP, fp
    //     0x93b854: ldp             fp, lr, [SP], #0x10
    // 0x93b858: ret
    //     0x93b858: ret             
    // 0x93b85c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b85c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b860: b               #0x93b84c
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9a50d0, size: 0x94
    // 0x9a50d0: EnterFrame
    //     0x9a50d0: stp             fp, lr, [SP, #-0x10]!
    //     0x9a50d4: mov             fp, SP
    // 0x9a50d8: AllocStack(0x8)
    //     0x9a50d8: sub             SP, SP, #8
    // 0x9a50dc: SetupParameters(TooltipState this /* r1 => r0, fp-0x8 */)
    //     0x9a50dc: mov             x0, x1
    //     0x9a50e0: stur            x1, [fp, #-8]
    // 0x9a50e4: CheckStackOverflow
    //     0x9a50e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a50e8: cmp             SP, x16
    //     0x9a50ec: b.ls            #0x9a5154
    // 0x9a50f0: LoadField: r1 = r0->field_f
    //     0x9a50f0: ldur            w1, [x0, #0xf]
    // 0x9a50f4: DecompressPointer r1
    //     0x9a50f4: add             x1, x1, HEAP, lsl #32
    // 0x9a50f8: cmp             w1, NULL
    // 0x9a50fc: b.eq            #0x9a515c
    // 0x9a5100: r0 = of()
    //     0x9a5100: bl              #0x9a51c0  ; [package:flutter/src/material/tooltip_visibility.dart] TooltipVisibility::of
    // 0x9a5104: ldur            x2, [fp, #-8]
    // 0x9a5108: r0 = true
    //     0x9a5108: add             x0, NULL, #0x20  ; true
    // 0x9a510c: StoreField: r2->field_1f = r0
    //     0x9a510c: stur            w0, [x2, #0x1f]
    // 0x9a5110: LoadField: r1 = r2->field_f
    //     0x9a5110: ldur            w1, [x2, #0xf]
    // 0x9a5114: DecompressPointer r1
    //     0x9a5114: add             x1, x1, HEAP, lsl #32
    // 0x9a5118: cmp             w1, NULL
    // 0x9a511c: b.eq            #0x9a5160
    // 0x9a5120: r0 = of()
    //     0x9a5120: bl              #0x9a5164  ; [package:flutter/src/material/tooltip_theme.dart] TooltipTheme::of
    // 0x9a5124: ldur            x1, [fp, #-8]
    // 0x9a5128: StoreField: r1->field_23 = r0
    //     0x9a5128: stur            w0, [x1, #0x23]
    //     0x9a512c: ldurb           w16, [x1, #-1]
    //     0x9a5130: ldurb           w17, [x0, #-1]
    //     0x9a5134: and             x16, x17, x16, lsr #2
    //     0x9a5138: tst             x16, HEAP, lsr #32
    //     0x9a513c: b.eq            #0x9a5144
    //     0x9a5140: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9a5144: r0 = Null
    //     0x9a5144: mov             x0, NULL
    // 0x9a5148: LeaveFrame
    //     0x9a5148: mov             SP, fp
    //     0x9a514c: ldp             fp, lr, [SP], #0x10
    // 0x9a5150: ret
    //     0x9a5150: ret             
    // 0x9a5154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a5154: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a5158: b               #0x9a50f0
    // 0x9a515c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a515c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a5160: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a5160: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa0e1e4, size: 0x1ec
    // 0xa0e1e4: EnterFrame
    //     0xa0e1e4: stp             fp, lr, [SP, #-0x10]!
    //     0xa0e1e8: mov             fp, SP
    // 0xa0e1ec: AllocStack(0x30)
    //     0xa0e1ec: sub             SP, SP, #0x30
    // 0xa0e1f0: SetupParameters(TooltipState this /* r1 => r0, fp-0x18 */)
    //     0xa0e1f0: mov             x0, x1
    //     0xa0e1f4: stur            x1, [fp, #-0x18]
    // 0xa0e1f8: CheckStackOverflow
    //     0xa0e1f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0e1fc: cmp             SP, x16
    //     0xa0e200: b.ls            #0xa0e3a8
    // 0xa0e204: LoadField: r1 = r0->field_b
    //     0xa0e204: ldur            w1, [x0, #0xb]
    // 0xa0e208: DecompressPointer r1
    //     0xa0e208: add             x1, x1, HEAP, lsl #32
    // 0xa0e20c: cmp             w1, NULL
    // 0xa0e210: b.eq            #0xa0e3b0
    // 0xa0e214: LoadField: r2 = r1->field_b
    //     0xa0e214: ldur            w2, [x1, #0xb]
    // 0xa0e218: DecompressPointer r2
    //     0xa0e218: add             x2, x2, HEAP, lsl #32
    // 0xa0e21c: stur            x2, [fp, #-0x10]
    // 0xa0e220: cmp             w2, NULL
    // 0xa0e224: b.eq            #0xa0e390
    // 0xa0e228: LoadField: r3 = r2->field_7
    //     0xa0e228: ldur            w3, [x2, #7]
    // 0xa0e22c: cbnz            w3, #0xa0e244
    // 0xa0e230: LoadField: r0 = r1->field_2b
    //     0xa0e230: ldur            w0, [x1, #0x2b]
    // 0xa0e234: DecompressPointer r0
    //     0xa0e234: add             x0, x0, HEAP, lsl #32
    // 0xa0e238: LeaveFrame
    //     0xa0e238: mov             SP, fp
    //     0xa0e23c: ldp             fp, lr, [SP], #0x10
    // 0xa0e240: ret
    //     0xa0e240: ret             
    // 0xa0e244: LoadField: r3 = r0->field_23
    //     0xa0e244: ldur            w3, [x0, #0x23]
    // 0xa0e248: DecompressPointer r3
    //     0xa0e248: add             x3, x3, HEAP, lsl #32
    // 0xa0e24c: r16 = Sentinel
    //     0xa0e24c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0e250: cmp             w3, w16
    // 0xa0e254: b.eq            #0xa0e3b4
    // 0xa0e258: LoadField: r3 = r1->field_2b
    //     0xa0e258: ldur            w3, [x1, #0x2b]
    // 0xa0e25c: DecompressPointer r3
    //     0xa0e25c: add             x3, x3, HEAP, lsl #32
    // 0xa0e260: stur            x3, [fp, #-8]
    // 0xa0e264: r0 = Semantics()
    //     0xa0e264: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0xa0e268: stur            x0, [fp, #-0x20]
    // 0xa0e26c: ldur            x16, [fp, #-0x10]
    // 0xa0e270: ldur            lr, [fp, #-8]
    // 0xa0e274: stp             lr, x16, [SP]
    // 0xa0e278: mov             x1, x0
    // 0xa0e27c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, tooltip, 0x1, null]
    //     0xa0e27c: add             x4, PP, #0x4d, lsl #12  ; [pp+0x4d738] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "tooltip", 0x1, Null]
    //     0xa0e280: ldr             x4, [x4, #0x738]
    // 0xa0e284: r0 = Semantics()
    //     0xa0e284: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0xa0e288: ldur            x2, [fp, #-0x18]
    // 0xa0e28c: LoadField: r0 = r2->field_1f
    //     0xa0e28c: ldur            w0, [x2, #0x1f]
    // 0xa0e290: DecompressPointer r0
    //     0xa0e290: add             x0, x0, HEAP, lsl #32
    // 0xa0e294: r16 = Sentinel
    //     0xa0e294: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0e298: cmp             w0, w16
    // 0xa0e29c: b.eq            #0xa0e3c0
    // 0xa0e2a0: r0 = Listener()
    //     0xa0e2a0: bl              #0x9daab0  ; AllocateListenerStub -> Listener (size=0x38)
    // 0xa0e2a4: ldur            x2, [fp, #-0x18]
    // 0xa0e2a8: r1 = Function '_handlePointerDown@621220820':.
    //     0xa0e2a8: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d740] AnonymousClosure: (0xa0f088), in [package:flutter/src/material/tooltip.dart] TooltipState::_handlePointerDown (0xa0f0c4)
    //     0xa0e2ac: ldr             x1, [x1, #0x740]
    // 0xa0e2b0: stur            x0, [fp, #-8]
    // 0xa0e2b4: r0 = AllocateClosure()
    //     0xa0e2b4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0e2b8: mov             x1, x0
    // 0xa0e2bc: ldur            x0, [fp, #-8]
    // 0xa0e2c0: StoreField: r0->field_f = r1
    //     0xa0e2c0: stur            w1, [x0, #0xf]
    // 0xa0e2c4: r1 = Instance_HitTestBehavior
    //     0xa0e2c4: add             x1, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xa0e2c8: ldr             x1, [x1, #0x1c8]
    // 0xa0e2cc: StoreField: r0->field_33 = r1
    //     0xa0e2cc: stur            w1, [x0, #0x33]
    // 0xa0e2d0: ldur            x1, [fp, #-0x20]
    // 0xa0e2d4: StoreField: r0->field_b = r1
    //     0xa0e2d4: stur            w1, [x0, #0xb]
    // 0xa0e2d8: ldur            x2, [fp, #-0x18]
    // 0xa0e2dc: r1 = Function '_handleMouseEnter@621220820':.
    //     0xa0e2dc: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d748] AnonymousClosure: (0xa0ec00), in [package:flutter/src/material/tooltip.dart] TooltipState::_handleMouseEnter (0xa0ec3c)
    //     0xa0e2e0: ldr             x1, [x1, #0x748]
    // 0xa0e2e4: r0 = AllocateClosure()
    //     0xa0e2e4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0e2e8: stur            x0, [fp, #-0x10]
    // 0xa0e2ec: r0 = _ExclusiveMouseRegion()
    //     0xa0e2ec: bl              #0xa0e3dc  ; Allocate_ExclusiveMouseRegionStub -> _ExclusiveMouseRegion (size=0x28)
    // 0xa0e2f0: mov             x3, x0
    // 0xa0e2f4: ldur            x0, [fp, #-0x10]
    // 0xa0e2f8: stur            x3, [fp, #-0x20]
    // 0xa0e2fc: StoreField: r3->field_f = r0
    //     0xa0e2fc: stur            w0, [x3, #0xf]
    // 0xa0e300: ldur            x2, [fp, #-0x18]
    // 0xa0e304: r1 = Function '_handleMouseExit@621220820':.
    //     0xa0e304: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d750] AnonymousClosure: (0xa0ea94), in [package:flutter/src/material/tooltip.dart] TooltipState::_handleMouseExit (0xa0ead0)
    //     0xa0e308: ldr             x1, [x1, #0x750]
    // 0xa0e30c: r0 = AllocateClosure()
    //     0xa0e30c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0e310: mov             x1, x0
    // 0xa0e314: ldur            x0, [fp, #-0x20]
    // 0xa0e318: ArrayStore: r0[0] = r1  ; List_4
    //     0xa0e318: stur            w1, [x0, #0x17]
    // 0xa0e31c: r1 = Instance__DeferringMouseCursor
    //     0xa0e31c: ldr             x1, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xa0e320: StoreField: r0->field_1b = r1
    //     0xa0e320: stur            w1, [x0, #0x1b]
    // 0xa0e324: r1 = true
    //     0xa0e324: add             x1, NULL, #0x20  ; true
    // 0xa0e328: StoreField: r0->field_1f = r1
    //     0xa0e328: stur            w1, [x0, #0x1f]
    // 0xa0e32c: ldur            x1, [fp, #-8]
    // 0xa0e330: StoreField: r0->field_b = r1
    //     0xa0e330: stur            w1, [x0, #0xb]
    // 0xa0e334: ldur            x2, [fp, #-0x18]
    // 0xa0e338: LoadField: r1 = r2->field_1b
    //     0xa0e338: ldur            w1, [x2, #0x1b]
    // 0xa0e33c: DecompressPointer r1
    //     0xa0e33c: add             x1, x1, HEAP, lsl #32
    // 0xa0e340: stur            x1, [fp, #-8]
    // 0xa0e344: r0 = OverlayPortal()
    //     0xa0e344: bl              #0xa0e3d0  ; AllocateOverlayPortalStub -> OverlayPortal (size=0x1c)
    // 0xa0e348: mov             x3, x0
    // 0xa0e34c: ldur            x0, [fp, #-8]
    // 0xa0e350: stur            x3, [fp, #-0x10]
    // 0xa0e354: StoreField: r3->field_b = r0
    //     0xa0e354: stur            w0, [x3, #0xb]
    // 0xa0e358: ldur            x2, [fp, #-0x18]
    // 0xa0e35c: r1 = Function '_buildTooltipOverlay@621220820':.
    //     0xa0e35c: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d758] AnonymousClosure: (0xa0e3e8), in [package:flutter/src/material/tooltip.dart] TooltipState::_buildTooltipOverlay (0xa0e424)
    //     0xa0e360: ldr             x1, [x1, #0x758]
    // 0xa0e364: r0 = AllocateClosure()
    //     0xa0e364: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0e368: mov             x1, x0
    // 0xa0e36c: ldur            x0, [fp, #-0x10]
    // 0xa0e370: StoreField: r0->field_f = r1
    //     0xa0e370: stur            w1, [x0, #0xf]
    // 0xa0e374: ldur            x1, [fp, #-0x20]
    // 0xa0e378: StoreField: r0->field_13 = r1
    //     0xa0e378: stur            w1, [x0, #0x13]
    // 0xa0e37c: r1 = false
    //     0xa0e37c: add             x1, NULL, #0x30  ; false
    // 0xa0e380: ArrayStore: r0[0] = r1  ; List_4
    //     0xa0e380: stur            w1, [x0, #0x17]
    // 0xa0e384: LeaveFrame
    //     0xa0e384: mov             SP, fp
    //     0xa0e388: ldp             fp, lr, [SP], #0x10
    // 0xa0e38c: ret
    //     0xa0e38c: ret             
    // 0xa0e390: r0 = Null
    //     0xa0e390: mov             x0, NULL
    // 0xa0e394: cmp             w0, NULL
    // 0xa0e398: b.eq            #0xa0e3cc
    // 0xa0e39c: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0xa0e39c: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0xa0e3a0: r0 = Throw()
    //     0xa0e3a0: bl              #0xec04b8  ; ThrowStub
    // 0xa0e3a4: brk             #0
    // 0xa0e3a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0e3a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0e3ac: b               #0xa0e204
    // 0xa0e3b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e3b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0e3b4: r9 = _tooltipTheme
    //     0xa0e3b4: add             x9, PP, #0x3a, lsl #12  ; [pp+0x3a2c0] Field <TooltipState._tooltipTheme@621220820>: late (offset: 0x24)
    //     0xa0e3b8: ldr             x9, [x9, #0x2c0]
    // 0xa0e3bc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0e3bc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa0e3c0: r9 = _visible
    //     0xa0e3c0: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d760] Field <TooltipState._visible@621220820>: late (offset: 0x20)
    //     0xa0e3c4: ldr             x9, [x9, #0x760]
    // 0xa0e3c8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0e3c8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa0e3cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e3cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget _buildTooltipOverlay(dynamic, BuildContext) {
    // ** addr: 0xa0e3e8, size: 0x3c
    // 0xa0e3e8: EnterFrame
    //     0xa0e3e8: stp             fp, lr, [SP, #-0x10]!
    //     0xa0e3ec: mov             fp, SP
    // 0xa0e3f0: ldr             x0, [fp, #0x18]
    // 0xa0e3f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa0e3f4: ldur            w1, [x0, #0x17]
    // 0xa0e3f8: DecompressPointer r1
    //     0xa0e3f8: add             x1, x1, HEAP, lsl #32
    // 0xa0e3fc: CheckStackOverflow
    //     0xa0e3fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0e400: cmp             SP, x16
    //     0xa0e404: b.ls            #0xa0e41c
    // 0xa0e408: ldr             x2, [fp, #0x10]
    // 0xa0e40c: r0 = _buildTooltipOverlay()
    //     0xa0e40c: bl              #0xa0e424  ; [package:flutter/src/material/tooltip.dart] TooltipState::_buildTooltipOverlay
    // 0xa0e410: LeaveFrame
    //     0xa0e410: mov             SP, fp
    //     0xa0e414: ldp             fp, lr, [SP], #0x10
    // 0xa0e418: ret
    //     0xa0e418: ret             
    // 0xa0e41c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0e41c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0e420: b               #0xa0e408
  }
  _ _buildTooltipOverlay(/* No info */) {
    // ** addr: 0xa0e424, size: 0x518
    // 0xa0e424: EnterFrame
    //     0xa0e424: stp             fp, lr, [SP, #-0x10]!
    //     0xa0e428: mov             fp, SP
    // 0xa0e42c: AllocStack(0x68)
    //     0xa0e42c: sub             SP, SP, #0x68
    // 0xa0e430: SetupParameters(TooltipState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa0e430: mov             x0, x2
    //     0xa0e434: stur            x2, [fp, #-0x10]
    //     0xa0e438: mov             x2, x1
    //     0xa0e43c: stur            x1, [fp, #-8]
    // 0xa0e440: CheckStackOverflow
    //     0xa0e440: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0e444: cmp             SP, x16
    //     0xa0e448: b.ls            #0xa0e8dc
    // 0xa0e44c: LoadField: r1 = r2->field_b
    //     0xa0e44c: ldur            w1, [x2, #0xb]
    // 0xa0e450: DecompressPointer r1
    //     0xa0e450: add             x1, x1, HEAP, lsl #32
    // 0xa0e454: cmp             w1, NULL
    // 0xa0e458: b.eq            #0xa0e8e4
    // 0xa0e45c: mov             x1, x0
    // 0xa0e460: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa0e460: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa0e464: r0 = of()
    //     0xa0e464: bl              #0x6a5014  ; [package:flutter/src/widgets/overlay.dart] Overlay::of
    // 0xa0e468: mov             x2, x0
    // 0xa0e46c: ldur            x0, [fp, #-8]
    // 0xa0e470: stur            x2, [fp, #-0x18]
    // 0xa0e474: LoadField: r1 = r0->field_f
    //     0xa0e474: ldur            w1, [x0, #0xf]
    // 0xa0e478: DecompressPointer r1
    //     0xa0e478: add             x1, x1, HEAP, lsl #32
    // 0xa0e47c: cmp             w1, NULL
    // 0xa0e480: b.eq            #0xa0e8e8
    // 0xa0e484: r0 = renderObject()
    //     0xa0e484: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xa0e488: mov             x3, x0
    // 0xa0e48c: stur            x3, [fp, #-0x20]
    // 0xa0e490: cmp             w3, NULL
    // 0xa0e494: b.eq            #0xa0e8ec
    // 0xa0e498: mov             x0, x3
    // 0xa0e49c: r2 = Null
    //     0xa0e49c: mov             x2, NULL
    // 0xa0e4a0: r1 = Null
    //     0xa0e4a0: mov             x1, NULL
    // 0xa0e4a4: r4 = LoadClassIdInstr(r0)
    //     0xa0e4a4: ldur            x4, [x0, #-1]
    //     0xa0e4a8: ubfx            x4, x4, #0xc, #0x14
    // 0xa0e4ac: sub             x4, x4, #0xbba
    // 0xa0e4b0: cmp             x4, #0x9a
    // 0xa0e4b4: b.ls            #0xa0e4c8
    // 0xa0e4b8: r8 = RenderBox
    //     0xa0e4b8: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0xa0e4bc: r3 = Null
    //     0xa0e4bc: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d768] Null
    //     0xa0e4c0: ldr             x3, [x3, #0x768]
    // 0xa0e4c4: r0 = RenderBox()
    //     0xa0e4c4: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0xa0e4c8: ldur            x1, [fp, #-0x20]
    // 0xa0e4cc: r0 = size()
    //     0xa0e4cc: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xa0e4d0: mov             x1, x0
    // 0xa0e4d4: r2 = Instance_Offset
    //     0xa0e4d4: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xa0e4d8: r0 = center()
    //     0xa0e4d8: bl              #0x7dc26c  ; [dart:ui] Size::center
    // 0xa0e4dc: mov             x2, x0
    // 0xa0e4e0: ldur            x0, [fp, #-0x18]
    // 0xa0e4e4: stur            x2, [fp, #-0x28]
    // 0xa0e4e8: LoadField: r1 = r0->field_f
    //     0xa0e4e8: ldur            w1, [x0, #0xf]
    // 0xa0e4ec: DecompressPointer r1
    //     0xa0e4ec: add             x1, x1, HEAP, lsl #32
    // 0xa0e4f0: cmp             w1, NULL
    // 0xa0e4f4: b.eq            #0xa0e8f0
    // 0xa0e4f8: r0 = renderObject()
    //     0xa0e4f8: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xa0e4fc: str             x0, [SP]
    // 0xa0e500: ldur            x1, [fp, #-0x20]
    // 0xa0e504: ldur            x2, [fp, #-0x28]
    // 0xa0e508: r4 = const [0, 0x3, 0x1, 0x2, ancestor, 0x2, null]
    //     0xa0e508: add             x4, PP, #0x44, lsl #12  ; [pp+0x44950] List(7) [0, 0x3, 0x1, 0x2, "ancestor", 0x2, Null]
    //     0xa0e50c: ldr             x4, [x4, #0x950]
    // 0xa0e510: r0 = localToGlobal()
    //     0xa0e510: bl              #0x67fb20  ; [package:flutter/src/rendering/box.dart] RenderBox::localToGlobal
    // 0xa0e514: ldur            x1, [fp, #-0x10]
    // 0xa0e518: stur            x0, [fp, #-0x18]
    // 0xa0e51c: r0 = of()
    //     0xa0e51c: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa0e520: LoadField: r1 = r0->field_3f
    //     0xa0e520: ldur            w1, [x0, #0x3f]
    // 0xa0e524: DecompressPointer r1
    //     0xa0e524: add             x1, x1, HEAP, lsl #32
    // 0xa0e528: LoadField: r2 = r1->field_7
    //     0xa0e528: ldur            w2, [x1, #7]
    // 0xa0e52c: DecompressPointer r2
    //     0xa0e52c: add             x2, x2, HEAP, lsl #32
    // 0xa0e530: r16 = Instance_Brightness
    //     0xa0e530: ldr             x16, [PP, #0x5428]  ; [pp+0x5428] Obj!Brightness@e39141
    // 0xa0e534: cmp             w2, w16
    // 0xa0e538: b.ne            #0xa0e5e8
    // 0xa0e53c: LoadField: r1 = r0->field_8f
    //     0xa0e53c: ldur            w1, [x0, #0x8f]
    // 0xa0e540: DecompressPointer r1
    //     0xa0e540: add             x1, x1, HEAP, lsl #32
    // 0xa0e544: LoadField: r0 = r1->field_2f
    //     0xa0e544: ldur            w0, [x1, #0x2f]
    // 0xa0e548: DecompressPointer r0
    //     0xa0e548: add             x0, x0, HEAP, lsl #32
    // 0xa0e54c: stur            x0, [fp, #-0x20]
    // 0xa0e550: cmp             w0, NULL
    // 0xa0e554: b.eq            #0xa0e8f4
    // 0xa0e558: r0 = _getDefaultFontSize()
    //     0xa0e558: bl              #0xa0ea8c  ; [package:flutter/src/material/tooltip.dart] TooltipState::_getDefaultFontSize
    // 0xa0e55c: r0 = inline_Allocate_Double()
    //     0xa0e55c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa0e560: add             x0, x0, #0x10
    //     0xa0e564: cmp             x1, x0
    //     0xa0e568: b.ls            #0xa0e8f8
    //     0xa0e56c: str             x0, [THR, #0x50]  ; THR::top
    //     0xa0e570: sub             x0, x0, #0xf
    //     0xa0e574: movz            x1, #0xe15c
    //     0xa0e578: movk            x1, #0x3, lsl #16
    //     0xa0e57c: stur            x1, [x0, #-1]
    // 0xa0e580: StoreField: r0->field_7 = d0
    //     0xa0e580: stur            d0, [x0, #7]
    // 0xa0e584: r16 = Instance_Color
    //     0xa0e584: ldr             x16, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0xa0e588: stp             x0, x16, [SP]
    // 0xa0e58c: ldur            x1, [fp, #-0x20]
    // 0xa0e590: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa0e590: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2bad8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa0e594: ldr             x4, [x4, #0xad8]
    // 0xa0e598: r0 = copyWith()
    //     0xa0e598: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa0e59c: r1 = Instance_Color
    //     0xa0e59c: ldr             x1, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xa0e5a0: d0 = 0.900000
    //     0xa0e5a0: ldr             d0, [PP, #0x5a88]  ; [pp+0x5a88] IMM: double(0.9) from 0x3feccccccccccccd
    // 0xa0e5a4: stur            x0, [fp, #-0x20]
    // 0xa0e5a8: r0 = withOpacity()
    //     0xa0e5a8: bl              #0xd72290  ; [dart:ui] Color::withOpacity
    // 0xa0e5ac: stur            x0, [fp, #-0x28]
    // 0xa0e5b0: r0 = BoxDecoration()
    //     0xa0e5b0: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa0e5b4: mov             x1, x0
    // 0xa0e5b8: ldur            x0, [fp, #-0x28]
    // 0xa0e5bc: StoreField: r1->field_7 = r0
    //     0xa0e5bc: stur            w0, [x1, #7]
    // 0xa0e5c0: r3 = Instance_BorderRadius
    //     0xa0e5c0: add             x3, PP, #0x38, lsl #12  ; [pp+0x38288] Obj!BorderRadius@e13a91
    //     0xa0e5c4: ldr             x3, [x3, #0x288]
    // 0xa0e5c8: StoreField: r1->field_13 = r3
    //     0xa0e5c8: stur            w3, [x1, #0x13]
    // 0xa0e5cc: r4 = Instance_BoxShape
    //     0xa0e5cc: add             x4, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa0e5d0: ldr             x4, [x4, #0xca8]
    // 0xa0e5d4: StoreField: r1->field_23 = r4
    //     0xa0e5d4: stur            w4, [x1, #0x23]
    // 0xa0e5d8: ldur            x2, [fp, #-0x20]
    // 0xa0e5dc: mov             x3, x1
    // 0xa0e5e0: r0 = AllocateRecord2()
    //     0xa0e5e0: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0xa0e5e4: b               #0xa0e6e0
    // 0xa0e5e8: r3 = Instance_BorderRadius
    //     0xa0e5e8: add             x3, PP, #0x38, lsl #12  ; [pp+0x38288] Obj!BorderRadius@e13a91
    //     0xa0e5ec: ldr             x3, [x3, #0x288]
    // 0xa0e5f0: r4 = Instance_BoxShape
    //     0xa0e5f0: add             x4, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa0e5f4: ldr             x4, [x4, #0xca8]
    // 0xa0e5f8: r16 = Instance_Brightness
    //     0xa0e5f8: ldr             x16, [PP, #0x5420]  ; [pp+0x5420] Obj!Brightness@e39121
    // 0xa0e5fc: cmp             w2, w16
    // 0xa0e600: b.ne            #0xa0e6dc
    // 0xa0e604: LoadField: r1 = r0->field_8f
    //     0xa0e604: ldur            w1, [x0, #0x8f]
    // 0xa0e608: DecompressPointer r1
    //     0xa0e608: add             x1, x1, HEAP, lsl #32
    // 0xa0e60c: LoadField: r0 = r1->field_2f
    //     0xa0e60c: ldur            w0, [x1, #0x2f]
    // 0xa0e610: DecompressPointer r0
    //     0xa0e610: add             x0, x0, HEAP, lsl #32
    // 0xa0e614: stur            x0, [fp, #-0x20]
    // 0xa0e618: cmp             w0, NULL
    // 0xa0e61c: b.eq            #0xa0e908
    // 0xa0e620: r0 = _getDefaultFontSize()
    //     0xa0e620: bl              #0xa0ea8c  ; [package:flutter/src/material/tooltip.dart] TooltipState::_getDefaultFontSize
    // 0xa0e624: r0 = inline_Allocate_Double()
    //     0xa0e624: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa0e628: add             x0, x0, #0x10
    //     0xa0e62c: cmp             x1, x0
    //     0xa0e630: b.ls            #0xa0e90c
    //     0xa0e634: str             x0, [THR, #0x50]  ; THR::top
    //     0xa0e638: sub             x0, x0, #0xf
    //     0xa0e63c: movz            x1, #0xe15c
    //     0xa0e640: movk            x1, #0x3, lsl #16
    //     0xa0e644: stur            x1, [x0, #-1]
    // 0xa0e648: StoreField: r0->field_7 = d0
    //     0xa0e648: stur            d0, [x0, #7]
    // 0xa0e64c: r16 = Instance_Color
    //     0xa0e64c: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xa0e650: stp             x0, x16, [SP]
    // 0xa0e654: ldur            x1, [fp, #-0x20]
    // 0xa0e658: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa0e658: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2bad8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa0e65c: ldr             x4, [x4, #0xad8]
    // 0xa0e660: r0 = copyWith()
    //     0xa0e660: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa0e664: r1 = _ConstMap len:12
    //     0xa0e664: ldr             x1, [PP, #0x5440]  ; [pp+0x5440] Map<int, Color>(12)
    // 0xa0e668: r2 = 1400
    //     0xa0e668: movz            x2, #0x578
    // 0xa0e66c: stur            x0, [fp, #-0x20]
    // 0xa0e670: r0 = []()
    //     0xa0e670: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xa0e674: cmp             w0, NULL
    // 0xa0e678: b.eq            #0xa0e91c
    // 0xa0e67c: r1 = LoadClassIdInstr(r0)
    //     0xa0e67c: ldur            x1, [x0, #-1]
    //     0xa0e680: ubfx            x1, x1, #0xc, #0x14
    // 0xa0e684: mov             x16, x0
    // 0xa0e688: mov             x0, x1
    // 0xa0e68c: mov             x1, x16
    // 0xa0e690: d0 = 0.900000
    //     0xa0e690: ldr             d0, [PP, #0x5a88]  ; [pp+0x5a88] IMM: double(0.9) from 0x3feccccccccccccd
    // 0xa0e694: r0 = GDT[cid_x0 + -0x1000]()
    //     0xa0e694: sub             lr, x0, #1, lsl #12
    //     0xa0e698: ldr             lr, [x21, lr, lsl #3]
    //     0xa0e69c: blr             lr
    // 0xa0e6a0: stur            x0, [fp, #-0x28]
    // 0xa0e6a4: r0 = BoxDecoration()
    //     0xa0e6a4: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa0e6a8: mov             x1, x0
    // 0xa0e6ac: ldur            x0, [fp, #-0x28]
    // 0xa0e6b0: StoreField: r1->field_7 = r0
    //     0xa0e6b0: stur            w0, [x1, #7]
    // 0xa0e6b4: r0 = Instance_BorderRadius
    //     0xa0e6b4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38288] Obj!BorderRadius@e13a91
    //     0xa0e6b8: ldr             x0, [x0, #0x288]
    // 0xa0e6bc: StoreField: r1->field_13 = r0
    //     0xa0e6bc: stur            w0, [x1, #0x13]
    // 0xa0e6c0: r0 = Instance_BoxShape
    //     0xa0e6c0: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa0e6c4: ldr             x0, [x0, #0xca8]
    // 0xa0e6c8: StoreField: r1->field_23 = r0
    //     0xa0e6c8: stur            w0, [x1, #0x23]
    // 0xa0e6cc: ldur            x2, [fp, #-0x20]
    // 0xa0e6d0: mov             x3, x1
    // 0xa0e6d4: r0 = AllocateRecord2()
    //     0xa0e6d4: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0xa0e6d8: b               #0xa0e6e0
    // 0xa0e6dc: r0 = Null
    //     0xa0e6dc: mov             x0, NULL
    // 0xa0e6e0: ldur            x1, [fp, #-8]
    // 0xa0e6e4: LoadField: r2 = r0->field_f
    //     0xa0e6e4: ldur            w2, [x0, #0xf]
    // 0xa0e6e8: DecompressPointer r2
    //     0xa0e6e8: add             x2, x2, HEAP, lsl #32
    // 0xa0e6ec: stur            x2, [fp, #-0x38]
    // 0xa0e6f0: LoadField: r3 = r0->field_13
    //     0xa0e6f0: ldur            w3, [x0, #0x13]
    // 0xa0e6f4: DecompressPointer r3
    //     0xa0e6f4: add             x3, x3, HEAP, lsl #32
    // 0xa0e6f8: stur            x3, [fp, #-0x30]
    // 0xa0e6fc: LoadField: r0 = r1->field_23
    //     0xa0e6fc: ldur            w0, [x1, #0x23]
    // 0xa0e700: DecompressPointer r0
    //     0xa0e700: add             x0, x0, HEAP, lsl #32
    // 0xa0e704: r16 = Sentinel
    //     0xa0e704: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0e708: cmp             w0, w16
    // 0xa0e70c: b.eq            #0xa0e920
    // 0xa0e710: stur            x0, [fp, #-0x28]
    // 0xa0e714: LoadField: r4 = r1->field_b
    //     0xa0e714: ldur            w4, [x1, #0xb]
    // 0xa0e718: DecompressPointer r4
    //     0xa0e718: add             x4, x4, HEAP, lsl #32
    // 0xa0e71c: cmp             w4, NULL
    // 0xa0e720: b.eq            #0xa0e92c
    // 0xa0e724: LoadField: r5 = r4->field_b
    //     0xa0e724: ldur            w5, [x4, #0xb]
    // 0xa0e728: DecompressPointer r5
    //     0xa0e728: add             x5, x5, HEAP, lsl #32
    // 0xa0e72c: stur            x5, [fp, #-0x20]
    // 0xa0e730: r0 = TextSpan()
    //     0xa0e730: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xa0e734: mov             x2, x0
    // 0xa0e738: ldur            x0, [fp, #-0x20]
    // 0xa0e73c: stur            x2, [fp, #-0x40]
    // 0xa0e740: StoreField: r2->field_b = r0
    //     0xa0e740: stur            w0, [x2, #0xb]
    // 0xa0e744: r0 = Instance__DeferringMouseCursor
    //     0xa0e744: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xa0e748: ArrayStore: r2[0] = r0  ; List_4
    //     0xa0e748: stur            w0, [x2, #0x17]
    // 0xa0e74c: ldur            x0, [fp, #-0x28]
    // 0xa0e750: LoadField: r1 = r0->field_7
    //     0xa0e750: ldur            w1, [x0, #7]
    // 0xa0e754: DecompressPointer r1
    //     0xa0e754: add             x1, x1, HEAP, lsl #32
    // 0xa0e758: cmp             w1, NULL
    // 0xa0e75c: b.ne            #0xa0e76c
    // 0xa0e760: ldur            x1, [fp, #-8]
    // 0xa0e764: r0 = _getDefaultTooltipHeight()
    //     0xa0e764: bl              #0xa0ea40  ; [package:flutter/src/material/tooltip.dart] TooltipState::_getDefaultTooltipHeight
    // 0xa0e768: b               #0xa0e770
    // 0xa0e76c: LoadField: d0 = r1->field_7
    //     0xa0e76c: ldur            d0, [x1, #7]
    // 0xa0e770: ldur            x2, [fp, #-8]
    // 0xa0e774: ldur            x0, [fp, #-0x28]
    // 0xa0e778: stur            d0, [fp, #-0x50]
    // 0xa0e77c: LoadField: r1 = r2->field_b
    //     0xa0e77c: ldur            w1, [x2, #0xb]
    // 0xa0e780: DecompressPointer r1
    //     0xa0e780: add             x1, x1, HEAP, lsl #32
    // 0xa0e784: cmp             w1, NULL
    // 0xa0e788: b.eq            #0xa0e930
    // 0xa0e78c: mov             x1, x2
    // 0xa0e790: r0 = _getDefaultPadding()
    //     0xa0e790: bl              #0xa0e9f4  ; [package:flutter/src/material/tooltip.dart] TooltipState::_getDefaultPadding
    // 0xa0e794: mov             x2, x0
    // 0xa0e798: ldur            x0, [fp, #-8]
    // 0xa0e79c: stur            x2, [fp, #-0x20]
    // 0xa0e7a0: LoadField: r1 = r0->field_b
    //     0xa0e7a0: ldur            w1, [x0, #0xb]
    // 0xa0e7a4: DecompressPointer r1
    //     0xa0e7a4: add             x1, x1, HEAP, lsl #32
    // 0xa0e7a8: cmp             w1, NULL
    // 0xa0e7ac: b.eq            #0xa0e934
    // 0xa0e7b0: mov             x1, x0
    // 0xa0e7b4: r0 = _overlayAnimation()
    //     0xa0e7b4: bl              #0xa0e954  ; [package:flutter/src/material/tooltip.dart] TooltipState::_overlayAnimation
    // 0xa0e7b8: ldur            x2, [fp, #-8]
    // 0xa0e7bc: stur            x0, [fp, #-0x48]
    // 0xa0e7c0: LoadField: r1 = r2->field_b
    //     0xa0e7c0: ldur            w1, [x2, #0xb]
    // 0xa0e7c4: DecompressPointer r1
    //     0xa0e7c4: add             x1, x1, HEAP, lsl #32
    // 0xa0e7c8: cmp             w1, NULL
    // 0xa0e7cc: b.eq            #0xa0e938
    // 0xa0e7d0: ldur            x1, [fp, #-0x28]
    // 0xa0e7d4: LoadField: r3 = r1->field_13
    //     0xa0e7d4: ldur            w3, [x1, #0x13]
    // 0xa0e7d8: DecompressPointer r3
    //     0xa0e7d8: add             x3, x3, HEAP, lsl #32
    // 0xa0e7dc: cmp             w3, NULL
    // 0xa0e7e0: b.ne            #0xa0e7ec
    // 0xa0e7e4: d1 = 24.000000
    //     0xa0e7e4: fmov            d1, #24.00000000
    // 0xa0e7e8: b               #0xa0e7f4
    // 0xa0e7ec: LoadField: d0 = r3->field_7
    //     0xa0e7ec: ldur            d0, [x3, #7]
    // 0xa0e7f0: mov             v1.16b, v0.16b
    // 0xa0e7f4: ldur            x6, [fp, #-0x18]
    // 0xa0e7f8: ldur            x4, [fp, #-0x38]
    // 0xa0e7fc: ldur            x5, [fp, #-0x30]
    // 0xa0e800: ldur            x3, [fp, #-0x40]
    // 0xa0e804: ldur            d0, [fp, #-0x50]
    // 0xa0e808: ldur            x1, [fp, #-0x20]
    // 0xa0e80c: stur            d1, [fp, #-0x58]
    // 0xa0e810: r0 = _TooltipOverlay()
    //     0xa0e810: bl              #0xa0e948  ; Allocate_TooltipOverlayStub -> _TooltipOverlay (size=0x48)
    // 0xa0e814: ldur            d0, [fp, #-0x50]
    // 0xa0e818: stur            x0, [fp, #-0x28]
    // 0xa0e81c: StoreField: r0->field_f = d0
    //     0xa0e81c: stur            d0, [x0, #0xf]
    // 0xa0e820: ldur            x1, [fp, #-0x40]
    // 0xa0e824: StoreField: r0->field_b = r1
    //     0xa0e824: stur            w1, [x0, #0xb]
    // 0xa0e828: ldur            x1, [fp, #-0x20]
    // 0xa0e82c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa0e82c: stur            w1, [x0, #0x17]
    // 0xa0e830: r1 = Instance_EdgeInsets
    //     0xa0e830: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa0e834: StoreField: r0->field_1b = r1
    //     0xa0e834: stur            w1, [x0, #0x1b]
    // 0xa0e838: ldur            x1, [fp, #-0x30]
    // 0xa0e83c: StoreField: r0->field_1f = r1
    //     0xa0e83c: stur            w1, [x0, #0x1f]
    // 0xa0e840: ldur            x1, [fp, #-0x38]
    // 0xa0e844: StoreField: r0->field_23 = r1
    //     0xa0e844: stur            w1, [x0, #0x23]
    // 0xa0e848: r1 = Instance_TextAlign
    //     0xa0e848: ldr             x1, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xa0e84c: StoreField: r0->field_27 = r1
    //     0xa0e84c: stur            w1, [x0, #0x27]
    // 0xa0e850: ldur            x1, [fp, #-0x48]
    // 0xa0e854: StoreField: r0->field_2b = r1
    //     0xa0e854: stur            w1, [x0, #0x2b]
    // 0xa0e858: ldur            x1, [fp, #-0x18]
    // 0xa0e85c: StoreField: r0->field_2f = r1
    //     0xa0e85c: stur            w1, [x0, #0x2f]
    // 0xa0e860: ldur            d0, [fp, #-0x58]
    // 0xa0e864: StoreField: r0->field_33 = d0
    //     0xa0e864: stur            d0, [x0, #0x33]
    // 0xa0e868: r1 = true
    //     0xa0e868: add             x1, NULL, #0x20  ; true
    // 0xa0e86c: StoreField: r0->field_3b = r1
    //     0xa0e86c: stur            w1, [x0, #0x3b]
    // 0xa0e870: ldur            x2, [fp, #-8]
    // 0xa0e874: r1 = Function '_handleMouseEnter@621220820':.
    //     0xa0e874: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d748] AnonymousClosure: (0xa0ec00), in [package:flutter/src/material/tooltip.dart] TooltipState::_handleMouseEnter (0xa0ec3c)
    //     0xa0e878: ldr             x1, [x1, #0x748]
    // 0xa0e87c: r0 = AllocateClosure()
    //     0xa0e87c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0e880: mov             x1, x0
    // 0xa0e884: ldur            x0, [fp, #-0x28]
    // 0xa0e888: StoreField: r0->field_3f = r1
    //     0xa0e888: stur            w1, [x0, #0x3f]
    // 0xa0e88c: ldur            x2, [fp, #-8]
    // 0xa0e890: r1 = Function '_handleMouseExit@621220820':.
    //     0xa0e890: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d750] AnonymousClosure: (0xa0ea94), in [package:flutter/src/material/tooltip.dart] TooltipState::_handleMouseExit (0xa0ead0)
    //     0xa0e894: ldr             x1, [x1, #0x750]
    // 0xa0e898: r0 = AllocateClosure()
    //     0xa0e898: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0e89c: mov             x1, x0
    // 0xa0e8a0: ldur            x0, [fp, #-0x28]
    // 0xa0e8a4: StoreField: r0->field_43 = r1
    //     0xa0e8a4: stur            w1, [x0, #0x43]
    // 0xa0e8a8: ldur            x1, [fp, #-0x10]
    // 0xa0e8ac: r0 = maybeOf()
    //     0xa0e8ac: bl              #0x9a8148  ; [package:flutter/src/widgets/selection_container.dart] SelectionContainer::maybeOf
    // 0xa0e8b0: cmp             w0, NULL
    // 0xa0e8b4: b.ne            #0xa0e8c0
    // 0xa0e8b8: ldur            x0, [fp, #-0x28]
    // 0xa0e8bc: b               #0xa0e8d0
    // 0xa0e8c0: ldur            x0, [fp, #-0x28]
    // 0xa0e8c4: r0 = SelectionContainer()
    //     0xa0e8c4: bl              #0xa0e93c  ; AllocateSelectionContainerStub -> SelectionContainer (size=0x18)
    // 0xa0e8c8: ldur            x1, [fp, #-0x28]
    // 0xa0e8cc: StoreField: r0->field_f = r1
    //     0xa0e8cc: stur            w1, [x0, #0xf]
    // 0xa0e8d0: LeaveFrame
    //     0xa0e8d0: mov             SP, fp
    //     0xa0e8d4: ldp             fp, lr, [SP], #0x10
    // 0xa0e8d8: ret
    //     0xa0e8d8: ret             
    // 0xa0e8dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0e8dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0e8e0: b               #0xa0e44c
    // 0xa0e8e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e8e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0e8e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e8e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0e8ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e8ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0e8f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e8f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0e8f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e8f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0e8f8: SaveReg d0
    //     0xa0e8f8: str             q0, [SP, #-0x10]!
    // 0xa0e8fc: r0 = AllocateDouble()
    //     0xa0e8fc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa0e900: RestoreReg d0
    //     0xa0e900: ldr             q0, [SP], #0x10
    // 0xa0e904: b               #0xa0e580
    // 0xa0e908: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e908: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0e90c: SaveReg d0
    //     0xa0e90c: str             q0, [SP, #-0x10]!
    // 0xa0e910: r0 = AllocateDouble()
    //     0xa0e910: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa0e914: RestoreReg d0
    //     0xa0e914: ldr             q0, [SP], #0x10
    // 0xa0e918: b               #0xa0e648
    // 0xa0e91c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e91c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0e920: r9 = _tooltipTheme
    //     0xa0e920: add             x9, PP, #0x3a, lsl #12  ; [pp+0x3a2c0] Field <TooltipState._tooltipTheme@621220820>: late (offset: 0x24)
    //     0xa0e924: ldr             x9, [x9, #0x2c0]
    // 0xa0e928: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0e928: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa0e92c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e92c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0e930: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa0e930: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa0e934: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e934: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0e938: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0e938: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _overlayAnimation(/* No info */) {
    // ** addr: 0xa0e954, size: 0xa0
    // 0xa0e954: EnterFrame
    //     0xa0e954: stp             fp, lr, [SP, #-0x10]!
    //     0xa0e958: mov             fp, SP
    // 0xa0e95c: AllocStack(0x10)
    //     0xa0e95c: sub             SP, SP, #0x10
    // 0xa0e960: SetupParameters(TooltipState this /* r1 => r0, fp-0x8 */)
    //     0xa0e960: mov             x0, x1
    //     0xa0e964: stur            x1, [fp, #-8]
    // 0xa0e968: CheckStackOverflow
    //     0xa0e968: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0e96c: cmp             SP, x16
    //     0xa0e970: b.ls            #0xa0e9ec
    // 0xa0e974: LoadField: r1 = r0->field_2f
    //     0xa0e974: ldur            w1, [x0, #0x2f]
    // 0xa0e978: DecompressPointer r1
    //     0xa0e978: add             x1, x1, HEAP, lsl #32
    // 0xa0e97c: cmp             w1, NULL
    // 0xa0e980: b.ne            #0xa0e9dc
    // 0xa0e984: mov             x1, x0
    // 0xa0e988: r0 = _controller()
    //     0xa0e988: bl              #0x85e790  ; [package:flutter/src/material/tooltip.dart] TooltipState::_controller
    // 0xa0e98c: r1 = <double>
    //     0xa0e98c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xa0e990: stur            x0, [fp, #-0x10]
    // 0xa0e994: r0 = CurvedAnimation()
    //     0xa0e994: bl              #0x7e34d0  ; AllocateCurvedAnimationStub -> CurvedAnimation (size=0x1c)
    // 0xa0e998: mov             x1, x0
    // 0xa0e99c: ldur            x3, [fp, #-0x10]
    // 0xa0e9a0: r2 = Instance_Cubic
    //     0xa0e9a0: ldr             x2, [PP, #0x6e20]  ; [pp+0x6e20] Obj!Cubic@e14d41
    // 0xa0e9a4: stur            x0, [fp, #-0x10]
    // 0xa0e9a8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xa0e9a8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xa0e9ac: r0 = CurvedAnimation()
    //     0xa0e9ac: bl              #0x7e338c  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::CurvedAnimation
    // 0xa0e9b0: ldur            x0, [fp, #-0x10]
    // 0xa0e9b4: ldur            x2, [fp, #-8]
    // 0xa0e9b8: StoreField: r2->field_2f = r0
    //     0xa0e9b8: stur            w0, [x2, #0x2f]
    //     0xa0e9bc: ldurb           w16, [x2, #-1]
    //     0xa0e9c0: ldurb           w17, [x0, #-1]
    //     0xa0e9c4: and             x16, x17, x16, lsr #2
    //     0xa0e9c8: tst             x16, HEAP, lsr #32
    //     0xa0e9cc: b.eq            #0xa0e9d4
    //     0xa0e9d0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa0e9d4: ldur            x0, [fp, #-0x10]
    // 0xa0e9d8: b               #0xa0e9e0
    // 0xa0e9dc: mov             x0, x1
    // 0xa0e9e0: LeaveFrame
    //     0xa0e9e0: mov             SP, fp
    //     0xa0e9e4: ldp             fp, lr, [SP], #0x10
    // 0xa0e9e8: ret
    //     0xa0e9e8: ret             
    // 0xa0e9ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0e9ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0e9f0: b               #0xa0e974
  }
  _ _getDefaultPadding(/* No info */) {
    // ** addr: 0xa0e9f4, size: 0x4c
    // 0xa0e9f4: EnterFrame
    //     0xa0e9f4: stp             fp, lr, [SP, #-0x10]!
    //     0xa0e9f8: mov             fp, SP
    // 0xa0e9fc: CheckStackOverflow
    //     0xa0e9fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0ea00: cmp             SP, x16
    //     0xa0ea04: b.ls            #0xa0ea34
    // 0xa0ea08: LoadField: r0 = r1->field_f
    //     0xa0ea08: ldur            w0, [x1, #0xf]
    // 0xa0ea0c: DecompressPointer r0
    //     0xa0ea0c: add             x0, x0, HEAP, lsl #32
    // 0xa0ea10: cmp             w0, NULL
    // 0xa0ea14: b.eq            #0xa0ea3c
    // 0xa0ea18: mov             x1, x0
    // 0xa0ea1c: r0 = of()
    //     0xa0ea1c: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa0ea20: r0 = Instance_EdgeInsets
    //     0xa0ea20: add             x0, PP, #0x31, lsl #12  ; [pp+0x31530] Obj!EdgeInsets@e12251
    //     0xa0ea24: ldr             x0, [x0, #0x530]
    // 0xa0ea28: LeaveFrame
    //     0xa0ea28: mov             SP, fp
    //     0xa0ea2c: ldp             fp, lr, [SP], #0x10
    // 0xa0ea30: ret
    //     0xa0ea30: ret             
    // 0xa0ea34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0ea34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0ea38: b               #0xa0ea08
    // 0xa0ea3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0ea3c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _getDefaultTooltipHeight(/* No info */) {
    // ** addr: 0xa0ea40, size: 0x4c
    // 0xa0ea40: EnterFrame
    //     0xa0ea40: stp             fp, lr, [SP, #-0x10]!
    //     0xa0ea44: mov             fp, SP
    // 0xa0ea48: CheckStackOverflow
    //     0xa0ea48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0ea4c: cmp             SP, x16
    //     0xa0ea50: b.ls            #0xa0ea80
    // 0xa0ea54: LoadField: r0 = r1->field_f
    //     0xa0ea54: ldur            w0, [x1, #0xf]
    // 0xa0ea58: DecompressPointer r0
    //     0xa0ea58: add             x0, x0, HEAP, lsl #32
    // 0xa0ea5c: cmp             w0, NULL
    // 0xa0ea60: b.eq            #0xa0ea88
    // 0xa0ea64: mov             x1, x0
    // 0xa0ea68: r0 = of()
    //     0xa0ea68: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa0ea6c: d0 = 32.000000
    //     0xa0ea6c: add             x17, PP, #0x2c, lsl #12  ; [pp+0x2c260] IMM: double(32) from 0x4040000000000000
    //     0xa0ea70: ldr             d0, [x17, #0x260]
    // 0xa0ea74: LeaveFrame
    //     0xa0ea74: mov             SP, fp
    //     0xa0ea78: ldp             fp, lr, [SP], #0x10
    // 0xa0ea7c: ret
    //     0xa0ea7c: ret             
    // 0xa0ea80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0ea80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0ea84: b               #0xa0ea54
    // 0xa0ea88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0ea88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static double _getDefaultFontSize() {
    // ** addr: 0xa0ea8c, size: 0x8
    // 0xa0ea8c: d0 = 14.000000
    //     0xa0ea8c: fmov            d0, #14.00000000
    // 0xa0ea90: ret
    //     0xa0ea90: ret             
  }
  [closure] void _handleMouseExit(dynamic, PointerExitEvent) {
    // ** addr: 0xa0ea94, size: 0x3c
    // 0xa0ea94: EnterFrame
    //     0xa0ea94: stp             fp, lr, [SP, #-0x10]!
    //     0xa0ea98: mov             fp, SP
    // 0xa0ea9c: ldr             x0, [fp, #0x18]
    // 0xa0eaa0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa0eaa0: ldur            w1, [x0, #0x17]
    // 0xa0eaa4: DecompressPointer r1
    //     0xa0eaa4: add             x1, x1, HEAP, lsl #32
    // 0xa0eaa8: CheckStackOverflow
    //     0xa0eaa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0eaac: cmp             SP, x16
    //     0xa0eab0: b.ls            #0xa0eac8
    // 0xa0eab4: ldr             x2, [fp, #0x10]
    // 0xa0eab8: r0 = _handleMouseExit()
    //     0xa0eab8: bl              #0xa0ead0  ; [package:flutter/src/material/tooltip.dart] TooltipState::_handleMouseExit
    // 0xa0eabc: LeaveFrame
    //     0xa0eabc: mov             SP, fp
    //     0xa0eac0: ldp             fp, lr, [SP], #0x10
    // 0xa0eac4: ret
    //     0xa0eac4: ret             
    // 0xa0eac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0eac8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0eacc: b               #0xa0eab4
  }
  _ _handleMouseExit(/* No info */) {
    // ** addr: 0xa0ead0, size: 0xe4
    // 0xa0ead0: EnterFrame
    //     0xa0ead0: stp             fp, lr, [SP, #-0x10]!
    //     0xa0ead4: mov             fp, SP
    // 0xa0ead8: AllocStack(0x10)
    //     0xa0ead8: sub             SP, SP, #0x10
    // 0xa0eadc: SetupParameters(TooltipState this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1 */)
    //     0xa0eadc: stur            x1, [fp, #-0x10]
    //     0xa0eae0: mov             x16, x2
    //     0xa0eae4: mov             x2, x1
    //     0xa0eae8: mov             x1, x16
    // 0xa0eaec: CheckStackOverflow
    //     0xa0eaec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0eaf0: cmp             SP, x16
    //     0xa0eaf4: b.ls            #0xa0ebac
    // 0xa0eaf8: LoadField: r3 = r2->field_3b
    //     0xa0eaf8: ldur            w3, [x2, #0x3b]
    // 0xa0eafc: DecompressPointer r3
    //     0xa0eafc: add             x3, x3, HEAP, lsl #32
    // 0xa0eb00: stur            x3, [fp, #-8]
    // 0xa0eb04: LoadField: r0 = r3->field_13
    //     0xa0eb04: ldur            w0, [x3, #0x13]
    // 0xa0eb08: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xa0eb08: ldur            w4, [x3, #0x17]
    // 0xa0eb0c: r5 = LoadInt32Instr(r0)
    //     0xa0eb0c: sbfx            x5, x0, #1, #0x1f
    // 0xa0eb10: r0 = LoadInt32Instr(r4)
    //     0xa0eb10: sbfx            x0, x4, #1, #0x1f
    // 0xa0eb14: sub             x4, x5, x0
    // 0xa0eb18: cbnz            x4, #0xa0eb2c
    // 0xa0eb1c: r0 = Null
    //     0xa0eb1c: mov             x0, NULL
    // 0xa0eb20: LeaveFrame
    //     0xa0eb20: mov             SP, fp
    //     0xa0eb24: ldp             fp, lr, [SP], #0x10
    // 0xa0eb28: ret
    //     0xa0eb28: ret             
    // 0xa0eb2c: r0 = LoadClassIdInstr(r1)
    //     0xa0eb2c: ldur            x0, [x1, #-1]
    //     0xa0eb30: ubfx            x0, x0, #0xc, #0x14
    // 0xa0eb34: r0 = GDT[cid_x0 + 0x1316b]()
    //     0xa0eb34: movz            x17, #0x316b
    //     0xa0eb38: movk            x17, #0x1, lsl #16
    //     0xa0eb3c: add             lr, x0, x17
    //     0xa0eb40: ldr             lr, [x21, lr, lsl #3]
    //     0xa0eb44: blr             lr
    // 0xa0eb48: mov             x2, x0
    // 0xa0eb4c: r0 = BoxInt64Instr(r2)
    //     0xa0eb4c: sbfiz           x0, x2, #1, #0x1f
    //     0xa0eb50: cmp             x2, x0, asr #1
    //     0xa0eb54: b.eq            #0xa0eb60
    //     0xa0eb58: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa0eb5c: stur            x2, [x0, #7]
    // 0xa0eb60: ldur            x1, [fp, #-8]
    // 0xa0eb64: mov             x2, x0
    // 0xa0eb68: r0 = remove()
    //     0xa0eb68: bl              #0xd89c04  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0xa0eb6c: ldur            x0, [fp, #-8]
    // 0xa0eb70: LoadField: r1 = r0->field_13
    //     0xa0eb70: ldur            w1, [x0, #0x13]
    // 0xa0eb74: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xa0eb74: ldur            w2, [x0, #0x17]
    // 0xa0eb78: r0 = LoadInt32Instr(r1)
    //     0xa0eb78: sbfx            x0, x1, #1, #0x1f
    // 0xa0eb7c: r1 = LoadInt32Instr(r2)
    //     0xa0eb7c: sbfx            x1, x2, #1, #0x1f
    // 0xa0eb80: sub             x2, x0, x1
    // 0xa0eb84: cbnz            x2, #0xa0eb9c
    // 0xa0eb88: ldur            x1, [fp, #-0x10]
    // 0xa0eb8c: r0 = _hoverExitDuration()
    //     0xa0eb8c: bl              #0xa0ebb4  ; [package:flutter/src/material/tooltip.dart] TooltipState::_hoverExitDuration
    // 0xa0eb90: ldur            x1, [fp, #-0x10]
    // 0xa0eb94: r2 = Instance_Duration
    //     0xa0eb94: ldr             x2, [PP, #0x6e28]  ; [pp+0x6e28] Obj!Duration@e3a0a1
    // 0xa0eb98: r0 = _scheduleDismissTooltip()
    //     0xa0eb98: bl              #0x85e69c  ; [package:flutter/src/material/tooltip.dart] TooltipState::_scheduleDismissTooltip
    // 0xa0eb9c: r0 = Null
    //     0xa0eb9c: mov             x0, NULL
    // 0xa0eba0: LeaveFrame
    //     0xa0eba0: mov             SP, fp
    //     0xa0eba4: ldp             fp, lr, [SP], #0x10
    // 0xa0eba8: ret
    //     0xa0eba8: ret             
    // 0xa0ebac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0ebac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0ebb0: b               #0xa0eaf8
  }
  get _ _hoverExitDuration(/* No info */) {
    // ** addr: 0xa0ebb4, size: 0x4c
    // 0xa0ebb4: EnterFrame
    //     0xa0ebb4: stp             fp, lr, [SP, #-0x10]!
    //     0xa0ebb8: mov             fp, SP
    // 0xa0ebbc: LoadField: r2 = r1->field_b
    //     0xa0ebbc: ldur            w2, [x1, #0xb]
    // 0xa0ebc0: DecompressPointer r2
    //     0xa0ebc0: add             x2, x2, HEAP, lsl #32
    // 0xa0ebc4: cmp             w2, NULL
    // 0xa0ebc8: b.eq            #0xa0ebf0
    // 0xa0ebcc: LoadField: r2 = r1->field_23
    //     0xa0ebcc: ldur            w2, [x1, #0x23]
    // 0xa0ebd0: DecompressPointer r2
    //     0xa0ebd0: add             x2, x2, HEAP, lsl #32
    // 0xa0ebd4: r16 = Sentinel
    //     0xa0ebd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0ebd8: cmp             w2, w16
    // 0xa0ebdc: b.eq            #0xa0ebf4
    // 0xa0ebe0: r0 = Instance_Duration
    //     0xa0ebe0: ldr             x0, [PP, #0x6e28]  ; [pp+0x6e28] Obj!Duration@e3a0a1
    // 0xa0ebe4: LeaveFrame
    //     0xa0ebe4: mov             SP, fp
    //     0xa0ebe8: ldp             fp, lr, [SP], #0x10
    // 0xa0ebec: ret
    //     0xa0ebec: ret             
    // 0xa0ebf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0ebf0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0ebf4: r9 = _tooltipTheme
    //     0xa0ebf4: add             x9, PP, #0x3a, lsl #12  ; [pp+0x3a2c0] Field <TooltipState._tooltipTheme@621220820>: late (offset: 0x24)
    //     0xa0ebf8: ldr             x9, [x9, #0x2c0]
    // 0xa0ebfc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0ebfc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void _handleMouseEnter(dynamic, PointerEnterEvent) {
    // ** addr: 0xa0ec00, size: 0x3c
    // 0xa0ec00: EnterFrame
    //     0xa0ec00: stp             fp, lr, [SP, #-0x10]!
    //     0xa0ec04: mov             fp, SP
    // 0xa0ec08: ldr             x0, [fp, #0x18]
    // 0xa0ec0c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa0ec0c: ldur            w1, [x0, #0x17]
    // 0xa0ec10: DecompressPointer r1
    //     0xa0ec10: add             x1, x1, HEAP, lsl #32
    // 0xa0ec14: CheckStackOverflow
    //     0xa0ec14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0ec18: cmp             SP, x16
    //     0xa0ec1c: b.ls            #0xa0ec34
    // 0xa0ec20: ldr             x2, [fp, #0x10]
    // 0xa0ec24: r0 = _handleMouseEnter()
    //     0xa0ec24: bl              #0xa0ec3c  ; [package:flutter/src/material/tooltip.dart] TooltipState::_handleMouseEnter
    // 0xa0ec28: LeaveFrame
    //     0xa0ec28: mov             SP, fp
    //     0xa0ec2c: ldp             fp, lr, [SP], #0x10
    // 0xa0ec30: ret
    //     0xa0ec30: ret             
    // 0xa0ec34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0ec34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0ec38: b               #0xa0ec20
  }
  _ _handleMouseEnter(/* No info */) {
    // ** addr: 0xa0ec3c, size: 0x1dc
    // 0xa0ec3c: EnterFrame
    //     0xa0ec3c: stp             fp, lr, [SP, #-0x10]!
    //     0xa0ec40: mov             fp, SP
    // 0xa0ec44: AllocStack(0x30)
    //     0xa0ec44: sub             SP, SP, #0x30
    // 0xa0ec48: SetupParameters(TooltipState this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1 */)
    //     0xa0ec48: stur            x1, [fp, #-0x10]
    //     0xa0ec4c: mov             x16, x2
    //     0xa0ec50: mov             x2, x1
    //     0xa0ec54: mov             x1, x16
    // 0xa0ec58: CheckStackOverflow
    //     0xa0ec58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0ec5c: cmp             SP, x16
    //     0xa0ec60: b.ls            #0xa0ee08
    // 0xa0ec64: LoadField: r3 = r2->field_3b
    //     0xa0ec64: ldur            w3, [x2, #0x3b]
    // 0xa0ec68: DecompressPointer r3
    //     0xa0ec68: add             x3, x3, HEAP, lsl #32
    // 0xa0ec6c: stur            x3, [fp, #-8]
    // 0xa0ec70: r0 = LoadClassIdInstr(r1)
    //     0xa0ec70: ldur            x0, [x1, #-1]
    //     0xa0ec74: ubfx            x0, x0, #0xc, #0x14
    // 0xa0ec78: r0 = GDT[cid_x0 + 0x1316b]()
    //     0xa0ec78: movz            x17, #0x316b
    //     0xa0ec7c: movk            x17, #0x1, lsl #16
    //     0xa0ec80: add             lr, x0, x17
    //     0xa0ec84: ldr             lr, [x21, lr, lsl #3]
    //     0xa0ec88: blr             lr
    // 0xa0ec8c: mov             x2, x0
    // 0xa0ec90: r0 = BoxInt64Instr(r2)
    //     0xa0ec90: sbfiz           x0, x2, #1, #0x1f
    //     0xa0ec94: cmp             x2, x0, asr #1
    //     0xa0ec98: b.eq            #0xa0eca4
    //     0xa0ec9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa0eca0: stur            x2, [x0, #7]
    // 0xa0eca4: ldur            x1, [fp, #-8]
    // 0xa0eca8: mov             x2, x0
    // 0xa0ecac: r0 = add()
    //     0xa0ecac: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xa0ecb0: r0 = InitLateStaticField(0xb60) // [package:flutter/src/material/tooltip.dart] Tooltip::_openedTooltips
    //     0xa0ecb0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa0ecb4: ldr             x0, [x0, #0x16c0]
    //     0xa0ecb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa0ecbc: cmp             w0, w16
    //     0xa0ecc0: b.ne            #0xa0ecd0
    //     0xa0ecc4: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a290] Field <Tooltip._openedTooltips@621220820>: static late final (offset: 0xb60)
    //     0xa0ecc8: ldr             x2, [x2, #0x290]
    //     0xa0eccc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa0ecd0: r1 = Function '<anonymous closure>':.
    //     0xa0ecd0: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d778] AnonymousClosure: (0xa0f054), in [package:flutter/src/material/tooltip.dart] TooltipState::_handleMouseEnter (0xa0ec3c)
    //     0xa0ecd4: ldr             x1, [x1, #0x778]
    // 0xa0ecd8: r2 = Null
    //     0xa0ecd8: mov             x2, NULL
    // 0xa0ecdc: stur            x0, [fp, #-8]
    // 0xa0ece0: r0 = AllocateClosure()
    //     0xa0ece0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0ece4: ldur            x1, [fp, #-8]
    // 0xa0ece8: mov             x2, x0
    // 0xa0ecec: r0 = where()
    //     0xa0ecec: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xa0ecf0: LoadField: r1 = r0->field_7
    //     0xa0ecf0: ldur            w1, [x0, #7]
    // 0xa0ecf4: DecompressPointer r1
    //     0xa0ecf4: add             x1, x1, HEAP, lsl #32
    // 0xa0ecf8: mov             x2, x0
    // 0xa0ecfc: r0 = _GrowableList.of()
    //     0xa0ecfc: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xa0ed00: mov             x3, x0
    // 0xa0ed04: stur            x3, [fp, #-0x30]
    // 0xa0ed08: LoadField: r4 = r3->field_7
    //     0xa0ed08: ldur            w4, [x3, #7]
    // 0xa0ed0c: DecompressPointer r4
    //     0xa0ed0c: add             x4, x4, HEAP, lsl #32
    // 0xa0ed10: stur            x4, [fp, #-0x28]
    // 0xa0ed14: LoadField: r0 = r3->field_b
    //     0xa0ed14: ldur            w0, [x3, #0xb]
    // 0xa0ed18: r5 = LoadInt32Instr(r0)
    //     0xa0ed18: sbfx            x5, x0, #1, #0x1f
    // 0xa0ed1c: stur            x5, [fp, #-0x20]
    // 0xa0ed20: r0 = 0
    //     0xa0ed20: movz            x0, #0
    // 0xa0ed24: CheckStackOverflow
    //     0xa0ed24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0ed28: cmp             SP, x16
    //     0xa0ed2c: b.ls            #0xa0ee10
    // 0xa0ed30: LoadField: r1 = r3->field_b
    //     0xa0ed30: ldur            w1, [x3, #0xb]
    // 0xa0ed34: r2 = LoadInt32Instr(r1)
    //     0xa0ed34: sbfx            x2, x1, #1, #0x1f
    // 0xa0ed38: cmp             x5, x2
    // 0xa0ed3c: b.ne            #0xa0ede8
    // 0xa0ed40: cmp             x0, x2
    // 0xa0ed44: b.ge            #0xa0edc0
    // 0xa0ed48: LoadField: r1 = r3->field_f
    //     0xa0ed48: ldur            w1, [x3, #0xf]
    // 0xa0ed4c: DecompressPointer r1
    //     0xa0ed4c: add             x1, x1, HEAP, lsl #32
    // 0xa0ed50: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0xa0ed50: add             x16, x1, x0, lsl #2
    //     0xa0ed54: ldur            w6, [x16, #0xf]
    // 0xa0ed58: DecompressPointer r6
    //     0xa0ed58: add             x6, x6, HEAP, lsl #32
    // 0xa0ed5c: stur            x6, [fp, #-8]
    // 0xa0ed60: add             x7, x0, #1
    // 0xa0ed64: stur            x7, [fp, #-0x18]
    // 0xa0ed68: cmp             w6, NULL
    // 0xa0ed6c: b.ne            #0xa0eda0
    // 0xa0ed70: mov             x0, x6
    // 0xa0ed74: mov             x2, x4
    // 0xa0ed78: r1 = Null
    //     0xa0ed78: mov             x1, NULL
    // 0xa0ed7c: cmp             w2, NULL
    // 0xa0ed80: b.eq            #0xa0eda0
    // 0xa0ed84: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa0ed84: ldur            w4, [x2, #0x17]
    // 0xa0ed88: DecompressPointer r4
    //     0xa0ed88: add             x4, x4, HEAP, lsl #32
    // 0xa0ed8c: r8 = X0
    //     0xa0ed8c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xa0ed90: LoadField: r9 = r4->field_7
    //     0xa0ed90: ldur            x9, [x4, #7]
    // 0xa0ed94: r3 = Null
    //     0xa0ed94: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d780] Null
    //     0xa0ed98: ldr             x3, [x3, #0x780]
    // 0xa0ed9c: blr             x9
    // 0xa0eda0: ldur            x1, [fp, #-8]
    // 0xa0eda4: r2 = Instance_Duration
    //     0xa0eda4: ldr             x2, [PP, #0x1d38]  ; [pp+0x1d38] Obj!Duration@e3a081
    // 0xa0eda8: r0 = _scheduleDismissTooltip()
    //     0xa0eda8: bl              #0x85e69c  ; [package:flutter/src/material/tooltip.dart] TooltipState::_scheduleDismissTooltip
    // 0xa0edac: ldur            x0, [fp, #-0x18]
    // 0xa0edb0: ldur            x4, [fp, #-0x28]
    // 0xa0edb4: ldur            x3, [fp, #-0x30]
    // 0xa0edb8: ldur            x5, [fp, #-0x20]
    // 0xa0edbc: b               #0xa0ed24
    // 0xa0edc0: cbnz            x2, #0xa0edcc
    // 0xa0edc4: ldur            x1, [fp, #-0x10]
    // 0xa0edc8: r0 = _waitDuration()
    //     0xa0edc8: bl              #0xa0f008  ; [package:flutter/src/material/tooltip.dart] TooltipState::_waitDuration
    // 0xa0edcc: ldur            x1, [fp, #-0x10]
    // 0xa0edd0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa0edd0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa0edd4: r0 = _scheduleShowTooltip()
    //     0xa0edd4: bl              #0xa0ee18  ; [package:flutter/src/material/tooltip.dart] TooltipState::_scheduleShowTooltip
    // 0xa0edd8: r0 = Null
    //     0xa0edd8: mov             x0, NULL
    // 0xa0eddc: LeaveFrame
    //     0xa0eddc: mov             SP, fp
    //     0xa0ede0: ldp             fp, lr, [SP], #0x10
    // 0xa0ede4: ret
    //     0xa0ede4: ret             
    // 0xa0ede8: mov             x0, x3
    // 0xa0edec: r0 = ConcurrentModificationError()
    //     0xa0edec: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xa0edf0: mov             x1, x0
    // 0xa0edf4: ldur            x0, [fp, #-0x30]
    // 0xa0edf8: StoreField: r1->field_b = r0
    //     0xa0edf8: stur            w0, [x1, #0xb]
    // 0xa0edfc: mov             x0, x1
    // 0xa0ee00: r0 = Throw()
    //     0xa0ee00: bl              #0xec04b8  ; ThrowStub
    // 0xa0ee04: brk             #0
    // 0xa0ee08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0ee08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0ee0c: b               #0xa0ec64
    // 0xa0ee10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0ee10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0ee14: b               #0xa0ed30
  }
  _ _scheduleShowTooltip(/* No info */) {
    // ** addr: 0xa0ee18, size: 0xdc
    // 0xa0ee18: EnterFrame
    //     0xa0ee18: stp             fp, lr, [SP, #-0x10]!
    //     0xa0ee1c: mov             fp, SP
    // 0xa0ee20: AllocStack(0x18)
    //     0xa0ee20: sub             SP, SP, #0x18
    // 0xa0ee24: SetupParameters(TooltipState this /* r1 => r1, fp-0x10 */, {dynamic showDuration = Null /* r0, fp-0x8 */})
    //     0xa0ee24: stur            x1, [fp, #-0x10]
    //     0xa0ee28: ldur            w0, [x4, #0x13]
    //     0xa0ee2c: ldur            w2, [x4, #0x1f]
    //     0xa0ee30: add             x2, x2, HEAP, lsl #32
    //     0xa0ee34: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4d790] "showDuration"
    //     0xa0ee38: ldr             x16, [x16, #0x790]
    //     0xa0ee3c: cmp             w2, w16
    //     0xa0ee40: b.ne            #0xa0ee5c
    //     0xa0ee44: ldur            w2, [x4, #0x23]
    //     0xa0ee48: add             x2, x2, HEAP, lsl #32
    //     0xa0ee4c: sub             w3, w0, w2
    //     0xa0ee50: add             x0, fp, w3, sxtw #2
    //     0xa0ee54: ldr             x0, [x0, #8]
    //     0xa0ee58: b               #0xa0ee60
    //     0xa0ee5c: mov             x0, NULL
    //     0xa0ee60: stur            x0, [fp, #-8]
    // 0xa0ee64: CheckStackOverflow
    //     0xa0ee64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0ee68: cmp             SP, x16
    //     0xa0ee6c: b.ls            #0xa0eee4
    // 0xa0ee70: r1 = 2
    //     0xa0ee70: movz            x1, #0x2
    // 0xa0ee74: r0 = AllocateContext()
    //     0xa0ee74: bl              #0xec126c  ; AllocateContextStub
    // 0xa0ee78: mov             x1, x0
    // 0xa0ee7c: ldur            x0, [fp, #-0x10]
    // 0xa0ee80: StoreField: r1->field_f = r0
    //     0xa0ee80: stur            w0, [x1, #0xf]
    // 0xa0ee84: ldur            x2, [fp, #-8]
    // 0xa0ee88: StoreField: r1->field_13 = r2
    //     0xa0ee88: stur            w2, [x1, #0x13]
    // 0xa0ee8c: mov             x2, x1
    // 0xa0ee90: r1 = Function 'show':.
    //     0xa0ee90: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d798] AnonymousClosure: (0xa0eef4), in [package:flutter/src/material/tooltip.dart] TooltipState::_scheduleShowTooltip (0xa0ee18)
    //     0xa0ee94: ldr             x1, [x1, #0x798]
    // 0xa0ee98: r0 = AllocateClosure()
    //     0xa0ee98: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0ee9c: ldur            x1, [fp, #-0x10]
    // 0xa0eea0: stur            x0, [fp, #-8]
    // 0xa0eea4: r0 = _controller()
    //     0xa0eea4: bl              #0x85e790  ; [package:flutter/src/material/tooltip.dart] TooltipState::_controller
    // 0xa0eea8: LoadField: r1 = r0->field_43
    //     0xa0eea8: ldur            w1, [x0, #0x43]
    // 0xa0eeac: DecompressPointer r1
    //     0xa0eeac: add             x1, x1, HEAP, lsl #32
    // 0xa0eeb0: r16 = Sentinel
    //     0xa0eeb0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0eeb4: cmp             w1, w16
    // 0xa0eeb8: b.eq            #0xa0eeec
    // 0xa0eebc: ldur            x16, [fp, #-8]
    // 0xa0eec0: str             x16, [SP]
    // 0xa0eec4: ldur            x0, [fp, #-8]
    // 0xa0eec8: ClosureCall
    //     0xa0eec8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xa0eecc: ldur            x2, [x0, #0x1f]
    //     0xa0eed0: blr             x2
    // 0xa0eed4: r0 = Null
    //     0xa0eed4: mov             x0, NULL
    // 0xa0eed8: LeaveFrame
    //     0xa0eed8: mov             SP, fp
    //     0xa0eedc: ldp             fp, lr, [SP], #0x10
    // 0xa0eee0: ret
    //     0xa0eee0: ret             
    // 0xa0eee4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0eee4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0eee8: b               #0xa0ee70
    // 0xa0eeec: r9 = _status
    //     0xa0eeec: ldr             x9, [PP, #0x4ed8]  ; [pp+0x4ed8] Field <AnimationController._status@441066280>: late (offset: 0x44)
    // 0xa0eef0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0eef0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void show(dynamic) {
    // ** addr: 0xa0eef4, size: 0x114
    // 0xa0eef4: EnterFrame
    //     0xa0eef4: stp             fp, lr, [SP, #-0x10]!
    //     0xa0eef8: mov             fp, SP
    // 0xa0eefc: AllocStack(0x18)
    //     0xa0eefc: sub             SP, SP, #0x18
    // 0xa0ef00: SetupParameters()
    //     0xa0ef00: ldr             x0, [fp, #0x10]
    //     0xa0ef04: ldur            w2, [x0, #0x17]
    //     0xa0ef08: add             x2, x2, HEAP, lsl #32
    //     0xa0ef0c: stur            x2, [fp, #-8]
    // 0xa0ef10: CheckStackOverflow
    //     0xa0ef10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0ef14: cmp             SP, x16
    //     0xa0ef18: b.ls            #0xa0eff4
    // 0xa0ef1c: LoadField: r1 = r2->field_f
    //     0xa0ef1c: ldur            w1, [x2, #0xf]
    // 0xa0ef20: DecompressPointer r1
    //     0xa0ef20: add             x1, x1, HEAP, lsl #32
    // 0xa0ef24: LoadField: r0 = r1->field_1f
    //     0xa0ef24: ldur            w0, [x1, #0x1f]
    // 0xa0ef28: DecompressPointer r0
    //     0xa0ef28: add             x0, x0, HEAP, lsl #32
    // 0xa0ef2c: r16 = Sentinel
    //     0xa0ef2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0ef30: cmp             w0, w16
    // 0xa0ef34: b.eq            #0xa0effc
    // 0xa0ef38: r0 = _controller()
    //     0xa0ef38: bl              #0x85e790  ; [package:flutter/src/material/tooltip.dart] TooltipState::_controller
    // 0xa0ef3c: mov             x1, x0
    // 0xa0ef40: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa0ef40: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa0ef44: r0 = forward()
    //     0xa0ef44: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0xa0ef48: ldur            x0, [fp, #-8]
    // 0xa0ef4c: LoadField: r1 = r0->field_f
    //     0xa0ef4c: ldur            w1, [x0, #0xf]
    // 0xa0ef50: DecompressPointer r1
    //     0xa0ef50: add             x1, x1, HEAP, lsl #32
    // 0xa0ef54: LoadField: r2 = r1->field_27
    //     0xa0ef54: ldur            w2, [x1, #0x27]
    // 0xa0ef58: DecompressPointer r2
    //     0xa0ef58: add             x2, x2, HEAP, lsl #32
    // 0xa0ef5c: cmp             w2, NULL
    // 0xa0ef60: b.eq            #0xa0ef70
    // 0xa0ef64: mov             x1, x2
    // 0xa0ef68: r0 = cancel()
    //     0xa0ef68: bl              #0x5fe740  ; [dart:isolate] _Timer::cancel
    // 0xa0ef6c: ldur            x0, [fp, #-8]
    // 0xa0ef70: LoadField: r2 = r0->field_f
    //     0xa0ef70: ldur            w2, [x0, #0xf]
    // 0xa0ef74: DecompressPointer r2
    //     0xa0ef74: add             x2, x2, HEAP, lsl #32
    // 0xa0ef78: stur            x2, [fp, #-0x18]
    // 0xa0ef7c: LoadField: r3 = r0->field_13
    //     0xa0ef7c: ldur            w3, [x0, #0x13]
    // 0xa0ef80: DecompressPointer r3
    //     0xa0ef80: add             x3, x3, HEAP, lsl #32
    // 0xa0ef84: stur            x3, [fp, #-0x10]
    // 0xa0ef88: cmp             w3, NULL
    // 0xa0ef8c: b.ne            #0xa0ef9c
    // 0xa0ef90: mov             x1, x2
    // 0xa0ef94: r0 = Null
    //     0xa0ef94: mov             x0, NULL
    // 0xa0ef98: b               #0xa0efc8
    // 0xa0ef9c: mov             x1, x2
    // 0xa0efa0: r0 = _controller()
    //     0xa0efa0: bl              #0x85e790  ; [package:flutter/src/material/tooltip.dart] TooltipState::_controller
    // 0xa0efa4: mov             x2, x0
    // 0xa0efa8: r1 = Function 'reverse':.
    //     0xa0efa8: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a270] AnonymousClosure: (0x655170), in [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse (0x6550d4)
    //     0xa0efac: ldr             x1, [x1, #0x270]
    // 0xa0efb0: r0 = AllocateClosure()
    //     0xa0efb0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0efb4: ldur            x2, [fp, #-0x10]
    // 0xa0efb8: mov             x3, x0
    // 0xa0efbc: r1 = Null
    //     0xa0efbc: mov             x1, NULL
    // 0xa0efc0: r0 = Timer()
    //     0xa0efc0: bl              #0x5f9778  ; [dart:async] Timer::Timer
    // 0xa0efc4: ldur            x1, [fp, #-0x18]
    // 0xa0efc8: StoreField: r1->field_27 = r0
    //     0xa0efc8: stur            w0, [x1, #0x27]
    //     0xa0efcc: ldurb           w16, [x1, #-1]
    //     0xa0efd0: ldurb           w17, [x0, #-1]
    //     0xa0efd4: and             x16, x17, x16, lsr #2
    //     0xa0efd8: tst             x16, HEAP, lsr #32
    //     0xa0efdc: b.eq            #0xa0efe4
    //     0xa0efe0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa0efe4: r0 = Null
    //     0xa0efe4: mov             x0, NULL
    // 0xa0efe8: LeaveFrame
    //     0xa0efe8: mov             SP, fp
    //     0xa0efec: ldp             fp, lr, [SP], #0x10
    // 0xa0eff0: ret
    //     0xa0eff0: ret             
    // 0xa0eff4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0eff4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0eff8: b               #0xa0ef1c
    // 0xa0effc: r9 = _visible
    //     0xa0effc: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d760] Field <TooltipState._visible@621220820>: late (offset: 0x20)
    //     0xa0f000: ldr             x9, [x9, #0x760]
    // 0xa0f004: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0f004: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ _waitDuration(/* No info */) {
    // ** addr: 0xa0f008, size: 0x4c
    // 0xa0f008: EnterFrame
    //     0xa0f008: stp             fp, lr, [SP, #-0x10]!
    //     0xa0f00c: mov             fp, SP
    // 0xa0f010: LoadField: r2 = r1->field_b
    //     0xa0f010: ldur            w2, [x1, #0xb]
    // 0xa0f014: DecompressPointer r2
    //     0xa0f014: add             x2, x2, HEAP, lsl #32
    // 0xa0f018: cmp             w2, NULL
    // 0xa0f01c: b.eq            #0xa0f044
    // 0xa0f020: LoadField: r2 = r1->field_23
    //     0xa0f020: ldur            w2, [x1, #0x23]
    // 0xa0f024: DecompressPointer r2
    //     0xa0f024: add             x2, x2, HEAP, lsl #32
    // 0xa0f028: r16 = Sentinel
    //     0xa0f028: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0f02c: cmp             w2, w16
    // 0xa0f030: b.eq            #0xa0f048
    // 0xa0f034: r0 = Instance_Duration
    //     0xa0f034: ldr             x0, [PP, #0x1d38]  ; [pp+0x1d38] Obj!Duration@e3a081
    // 0xa0f038: LeaveFrame
    //     0xa0f038: mov             SP, fp
    //     0xa0f03c: ldp             fp, lr, [SP], #0x10
    // 0xa0f040: ret
    //     0xa0f040: ret             
    // 0xa0f044: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0f044: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0f048: r9 = _tooltipTheme
    //     0xa0f048: add             x9, PP, #0x3a, lsl #12  ; [pp+0x3a2c0] Field <TooltipState._tooltipTheme@621220820>: late (offset: 0x24)
    //     0xa0f04c: ldr             x9, [x9, #0x2c0]
    // 0xa0f050: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0f050: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, TooltipState) {
    // ** addr: 0xa0f054, size: 0x34
    // 0xa0f054: ldr             x1, [SP]
    // 0xa0f058: LoadField: r2 = r1->field_3b
    //     0xa0f058: ldur            w2, [x1, #0x3b]
    // 0xa0f05c: DecompressPointer r2
    //     0xa0f05c: add             x2, x2, HEAP, lsl #32
    // 0xa0f060: LoadField: r1 = r2->field_13
    //     0xa0f060: ldur            w1, [x2, #0x13]
    // 0xa0f064: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa0f064: ldur            w3, [x2, #0x17]
    // 0xa0f068: r2 = LoadInt32Instr(r1)
    //     0xa0f068: sbfx            x2, x1, #1, #0x1f
    // 0xa0f06c: r1 = LoadInt32Instr(r3)
    //     0xa0f06c: sbfx            x1, x3, #1, #0x1f
    // 0xa0f070: sub             x3, x2, x1
    // 0xa0f074: cbz             x3, #0xa0f080
    // 0xa0f078: r0 = false
    //     0xa0f078: add             x0, NULL, #0x30  ; false
    // 0xa0f07c: b               #0xa0f084
    // 0xa0f080: r0 = true
    //     0xa0f080: add             x0, NULL, #0x20  ; true
    // 0xa0f084: ret
    //     0xa0f084: ret             
  }
  [closure] void _handlePointerDown(dynamic, PointerDownEvent) {
    // ** addr: 0xa0f088, size: 0x3c
    // 0xa0f088: EnterFrame
    //     0xa0f088: stp             fp, lr, [SP, #-0x10]!
    //     0xa0f08c: mov             fp, SP
    // 0xa0f090: ldr             x0, [fp, #0x18]
    // 0xa0f094: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa0f094: ldur            w1, [x0, #0x17]
    // 0xa0f098: DecompressPointer r1
    //     0xa0f098: add             x1, x1, HEAP, lsl #32
    // 0xa0f09c: CheckStackOverflow
    //     0xa0f09c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0f0a0: cmp             SP, x16
    //     0xa0f0a4: b.ls            #0xa0f0bc
    // 0xa0f0a8: ldr             x2, [fp, #0x10]
    // 0xa0f0ac: r0 = _handlePointerDown()
    //     0xa0f0ac: bl              #0xa0f0c4  ; [package:flutter/src/material/tooltip.dart] TooltipState::_handlePointerDown
    // 0xa0f0b0: LeaveFrame
    //     0xa0f0b0: mov             SP, fp
    //     0xa0f0b4: ldp             fp, lr, [SP], #0x10
    // 0xa0f0b8: ret
    //     0xa0f0b8: ret             
    // 0xa0f0bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0f0bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0f0c0: b               #0xa0f0a8
  }
  _ _handlePointerDown(/* No info */) {
    // ** addr: 0xa0f0c4, size: 0x268
    // 0xa0f0c4: EnterFrame
    //     0xa0f0c4: stp             fp, lr, [SP, #-0x10]!
    //     0xa0f0c8: mov             fp, SP
    // 0xa0f0cc: AllocStack(0x20)
    //     0xa0f0cc: sub             SP, SP, #0x20
    // 0xa0f0d0: SetupParameters(TooltipState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa0f0d0: mov             x0, x1
    //     0xa0f0d4: stur            x1, [fp, #-8]
    //     0xa0f0d8: stur            x2, [fp, #-0x10]
    // 0xa0f0dc: CheckStackOverflow
    //     0xa0f0dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0f0e0: cmp             SP, x16
    //     0xa0f0e4: b.ls            #0xa0f324
    // 0xa0f0e8: mov             x1, x0
    // 0xa0f0ec: r0 = _triggerMode()
    //     0xa0f0ec: bl              #0xa0f32c  ; [package:flutter/src/material/tooltip.dart] TooltipState::_triggerMode
    // 0xa0f0f0: LoadField: r1 = r0->field_7
    //     0xa0f0f0: ldur            x1, [x0, #7]
    // 0xa0f0f4: cmp             x1, #1
    // 0xa0f0f8: b.gt            #0xa0f224
    // 0xa0f0fc: cmp             x1, #0
    // 0xa0f100: b.le            #0xa0f314
    // 0xa0f104: ldur            x2, [fp, #-8]
    // 0xa0f108: LoadField: r0 = r2->field_33
    //     0xa0f108: ldur            w0, [x2, #0x33]
    // 0xa0f10c: DecompressPointer r0
    //     0xa0f10c: add             x0, x0, HEAP, lsl #32
    // 0xa0f110: cmp             w0, NULL
    // 0xa0f114: b.ne            #0xa0f180
    // 0xa0f118: r0 = LongPressGestureRecognizer()
    //     0xa0f118: bl              #0x762f70  ; AllocateLongPressGestureRecognizerStub -> LongPressGestureRecognizer (size=0xac)
    // 0xa0f11c: mov             x4, x0
    // 0xa0f120: r0 = false
    //     0xa0f120: add             x0, NULL, #0x30  ; false
    // 0xa0f124: stur            x4, [fp, #-0x18]
    // 0xa0f128: StoreField: r4->field_47 = r0
    //     0xa0f128: stur            w0, [x4, #0x47]
    // 0xa0f12c: str             NULL, [SP]
    // 0xa0f130: mov             x1, x4
    // 0xa0f134: r2 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@426232524': static.
    //     0xa0f134: add             x2, PP, #0x33, lsl #12  ; [pp+0x330a8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@426232524': static. (0x7e54fb163834)
    //     0xa0f138: ldr             x2, [x2, #0xa8]
    // 0xa0f13c: r3 = Instance_Duration
    //     0xa0f13c: ldr             x3, [PP, #0x4fa0]  ; [pp+0x4fa0] Obj!Duration@e3a0b1
    // 0xa0f140: r5 = _ConstSet len:5
    //     0xa0f140: add             x5, PP, #0x4d, lsl #12  ; [pp+0x4d7a0] Set<PointerDeviceKind>(5)
    //     0xa0f144: ldr             x5, [x5, #0x7a0]
    // 0xa0f148: r4 = const [0, 0x5, 0x1, 0x4, postAcceptSlopTolerance, 0x4, null]
    //     0xa0f148: add             x4, PP, #0x33, lsl #12  ; [pp+0x330b0] List(7) [0, 0x5, 0x1, 0x4, "postAcceptSlopTolerance", 0x4, Null]
    //     0xa0f14c: ldr             x4, [x4, #0xb0]
    // 0xa0f150: r0 = PrimaryPointerGestureRecognizer()
    //     0xa0f150: bl              #0x762f7c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::PrimaryPointerGestureRecognizer
    // 0xa0f154: ldur            x0, [fp, #-0x18]
    // 0xa0f158: ldur            x3, [fp, #-8]
    // 0xa0f15c: StoreField: r3->field_33 = r0
    //     0xa0f15c: stur            w0, [x3, #0x33]
    //     0xa0f160: ldurb           w16, [x3, #-1]
    //     0xa0f164: ldurb           w17, [x0, #-1]
    //     0xa0f168: and             x16, x17, x16, lsr #2
    //     0xa0f16c: tst             x16, HEAP, lsr #32
    //     0xa0f170: b.eq            #0xa0f178
    //     0xa0f174: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa0f178: ldur            x0, [fp, #-0x18]
    // 0xa0f17c: b               #0xa0f184
    // 0xa0f180: mov             x3, x2
    // 0xa0f184: mov             x2, x3
    // 0xa0f188: stur            x0, [fp, #-0x18]
    // 0xa0f18c: r1 = Function '_handleTapToDismiss@621220820':.
    //     0xa0f18c: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d7a8] AnonymousClosure: (0x93b82c), in [package:flutter/src/material/tooltip.dart] TooltipState::_handleTapToDismiss (0x93b7c4)
    //     0xa0f190: ldr             x1, [x1, #0x7a8]
    // 0xa0f194: r0 = AllocateClosure()
    //     0xa0f194: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0f198: ldur            x3, [fp, #-0x18]
    // 0xa0f19c: StoreField: r3->field_57 = r0
    //     0xa0f19c: stur            w0, [x3, #0x57]
    //     0xa0f1a0: ldurb           w16, [x3, #-1]
    //     0xa0f1a4: ldurb           w17, [x0, #-1]
    //     0xa0f1a8: and             x16, x17, x16, lsr #2
    //     0xa0f1ac: tst             x16, HEAP, lsr #32
    //     0xa0f1b0: b.eq            #0xa0f1b8
    //     0xa0f1b4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa0f1b8: ldur            x2, [fp, #-8]
    // 0xa0f1bc: r1 = Function '_handleLongPress@621220820':.
    //     0xa0f1bc: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d7b0] AnonymousClosure: (0xa0f528), in [package:flutter/src/material/tooltip.dart] TooltipState::_handleLongPress (0xa0f560)
    //     0xa0f1c0: ldr             x1, [x1, #0x7b0]
    // 0xa0f1c4: r0 = AllocateClosure()
    //     0xa0f1c4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0f1c8: ldur            x3, [fp, #-0x18]
    // 0xa0f1cc: StoreField: r3->field_5b = r0
    //     0xa0f1cc: stur            w0, [x3, #0x5b]
    //     0xa0f1d0: ldurb           w16, [x3, #-1]
    //     0xa0f1d4: ldurb           w17, [x0, #-1]
    //     0xa0f1d8: and             x16, x17, x16, lsr #2
    //     0xa0f1dc: tst             x16, HEAP, lsr #32
    //     0xa0f1e0: b.eq            #0xa0f1e8
    //     0xa0f1e4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa0f1e8: ldur            x2, [fp, #-8]
    // 0xa0f1ec: r1 = Function '_handlePressUp@621220820':.
    //     0xa0f1ec: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d7b8] AnonymousClosure: (0x85e5c0), in [package:flutter/src/material/tooltip.dart] TooltipState::_handlePressUp (0x85e61c)
    //     0xa0f1f0: ldr             x1, [x1, #0x7b8]
    // 0xa0f1f4: r0 = AllocateClosure()
    //     0xa0f1f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0f1f8: ldur            x1, [fp, #-0x18]
    // 0xa0f1fc: StoreField: r1->field_67 = r0
    //     0xa0f1fc: stur            w0, [x1, #0x67]
    //     0xa0f200: ldurb           w16, [x1, #-1]
    //     0xa0f204: ldurb           w17, [x0, #-1]
    //     0xa0f208: and             x16, x17, x16, lsr #2
    //     0xa0f20c: tst             x16, HEAP, lsr #32
    //     0xa0f210: b.eq            #0xa0f218
    //     0xa0f214: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa0f218: ldur            x2, [fp, #-0x10]
    // 0xa0f21c: r0 = addPointer()
    //     0xa0f21c: bl              #0x802d08  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::addPointer
    // 0xa0f220: b               #0xa0f314
    // 0xa0f224: ldur            x2, [fp, #-8]
    // 0xa0f228: r0 = false
    //     0xa0f228: add             x0, NULL, #0x30  ; false
    // 0xa0f22c: LoadField: r1 = r2->field_37
    //     0xa0f22c: ldur            w1, [x2, #0x37]
    // 0xa0f230: DecompressPointer r1
    //     0xa0f230: add             x1, x1, HEAP, lsl #32
    // 0xa0f234: cmp             w1, NULL
    // 0xa0f238: b.ne            #0xa0f2a0
    // 0xa0f23c: r0 = TapGestureRecognizer()
    //     0xa0f23c: bl              #0x7632dc  ; AllocateTapGestureRecognizerStub -> TapGestureRecognizer (size=0x84)
    // 0xa0f240: mov             x4, x0
    // 0xa0f244: r0 = false
    //     0xa0f244: add             x0, NULL, #0x30  ; false
    // 0xa0f248: stur            x4, [fp, #-0x18]
    // 0xa0f24c: StoreField: r4->field_47 = r0
    //     0xa0f24c: stur            w0, [x4, #0x47]
    // 0xa0f250: StoreField: r4->field_4b = r0
    //     0xa0f250: stur            w0, [x4, #0x4b]
    // 0xa0f254: mov             x1, x4
    // 0xa0f258: r2 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static.
    //     0xa0f258: add             x2, PP, #0x25, lsl #12  ; [pp+0x253d8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static. (0x7e54fb8bbd8c)
    //     0xa0f25c: ldr             x2, [x2, #0x3d8]
    // 0xa0f260: r3 = Instance_Duration
    //     0xa0f260: ldr             x3, [PP, #0x6e28]  ; [pp+0x6e28] Obj!Duration@e3a0a1
    // 0xa0f264: r5 = _ConstSet len:5
    //     0xa0f264: add             x5, PP, #0x4d, lsl #12  ; [pp+0x4d7a0] Set<PointerDeviceKind>(5)
    //     0xa0f268: ldr             x5, [x5, #0x7a0]
    // 0xa0f26c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xa0f26c: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xa0f270: r0 = PrimaryPointerGestureRecognizer()
    //     0xa0f270: bl              #0x762f7c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::PrimaryPointerGestureRecognizer
    // 0xa0f274: ldur            x0, [fp, #-0x18]
    // 0xa0f278: ldur            x3, [fp, #-8]
    // 0xa0f27c: StoreField: r3->field_37 = r0
    //     0xa0f27c: stur            w0, [x3, #0x37]
    //     0xa0f280: ldurb           w16, [x3, #-1]
    //     0xa0f284: ldurb           w17, [x0, #-1]
    //     0xa0f288: and             x16, x17, x16, lsr #2
    //     0xa0f28c: tst             x16, HEAP, lsr #32
    //     0xa0f290: b.eq            #0xa0f298
    //     0xa0f294: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa0f298: ldur            x0, [fp, #-0x18]
    // 0xa0f29c: b               #0xa0f2a8
    // 0xa0f2a0: mov             x3, x2
    // 0xa0f2a4: mov             x0, x1
    // 0xa0f2a8: mov             x2, x3
    // 0xa0f2ac: stur            x0, [fp, #-0x18]
    // 0xa0f2b0: r1 = Function '_handleTapToDismiss@621220820':.
    //     0xa0f2b0: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d7a8] AnonymousClosure: (0x93b82c), in [package:flutter/src/material/tooltip.dart] TooltipState::_handleTapToDismiss (0x93b7c4)
    //     0xa0f2b4: ldr             x1, [x1, #0x7a8]
    // 0xa0f2b8: r0 = AllocateClosure()
    //     0xa0f2b8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0f2bc: ldur            x3, [fp, #-0x18]
    // 0xa0f2c0: StoreField: r3->field_63 = r0
    //     0xa0f2c0: stur            w0, [x3, #0x63]
    //     0xa0f2c4: ldurb           w16, [x3, #-1]
    //     0xa0f2c8: ldurb           w17, [x0, #-1]
    //     0xa0f2cc: and             x16, x17, x16, lsr #2
    //     0xa0f2d0: tst             x16, HEAP, lsr #32
    //     0xa0f2d4: b.eq            #0xa0f2dc
    //     0xa0f2d8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa0f2dc: ldur            x2, [fp, #-8]
    // 0xa0f2e0: r1 = Function '_handleTap@621220820':.
    //     0xa0f2e0: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d7c0] AnonymousClosure: (0xa0f37c), in [package:flutter/src/material/tooltip.dart] TooltipState::_handleTap (0xa0f3b4)
    //     0xa0f2e4: ldr             x1, [x1, #0x7c0]
    // 0xa0f2e8: r0 = AllocateClosure()
    //     0xa0f2e8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa0f2ec: ldur            x1, [fp, #-0x18]
    // 0xa0f2f0: StoreField: r1->field_5f = r0
    //     0xa0f2f0: stur            w0, [x1, #0x5f]
    //     0xa0f2f4: ldurb           w16, [x1, #-1]
    //     0xa0f2f8: ldurb           w17, [x0, #-1]
    //     0xa0f2fc: and             x16, x17, x16, lsr #2
    //     0xa0f300: tst             x16, HEAP, lsr #32
    //     0xa0f304: b.eq            #0xa0f30c
    //     0xa0f308: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa0f30c: ldur            x2, [fp, #-0x10]
    // 0xa0f310: r0 = addPointer()
    //     0xa0f310: bl              #0x802d08  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::addPointer
    // 0xa0f314: r0 = Null
    //     0xa0f314: mov             x0, NULL
    // 0xa0f318: LeaveFrame
    //     0xa0f318: mov             SP, fp
    //     0xa0f31c: ldp             fp, lr, [SP], #0x10
    // 0xa0f320: ret
    //     0xa0f320: ret             
    // 0xa0f324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0f324: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0f328: b               #0xa0f0e8
  }
  get _ _triggerMode(/* No info */) {
    // ** addr: 0xa0f32c, size: 0x50
    // 0xa0f32c: EnterFrame
    //     0xa0f32c: stp             fp, lr, [SP, #-0x10]!
    //     0xa0f330: mov             fp, SP
    // 0xa0f334: LoadField: r2 = r1->field_b
    //     0xa0f334: ldur            w2, [x1, #0xb]
    // 0xa0f338: DecompressPointer r2
    //     0xa0f338: add             x2, x2, HEAP, lsl #32
    // 0xa0f33c: cmp             w2, NULL
    // 0xa0f340: b.eq            #0xa0f36c
    // 0xa0f344: LoadField: r2 = r1->field_23
    //     0xa0f344: ldur            w2, [x1, #0x23]
    // 0xa0f348: DecompressPointer r2
    //     0xa0f348: add             x2, x2, HEAP, lsl #32
    // 0xa0f34c: r16 = Sentinel
    //     0xa0f34c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0f350: cmp             w2, w16
    // 0xa0f354: b.eq            #0xa0f370
    // 0xa0f358: r0 = Instance_TooltipTriggerMode
    //     0xa0f358: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4d7e0] Obj!TooltipTriggerMode@e35fe1
    //     0xa0f35c: ldr             x0, [x0, #0x7e0]
    // 0xa0f360: LeaveFrame
    //     0xa0f360: mov             SP, fp
    //     0xa0f364: ldp             fp, lr, [SP], #0x10
    // 0xa0f368: ret
    //     0xa0f368: ret             
    // 0xa0f36c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0f36c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0f370: r9 = _tooltipTheme
    //     0xa0f370: add             x9, PP, #0x3a, lsl #12  ; [pp+0x3a2c0] Field <TooltipState._tooltipTheme@621220820>: late (offset: 0x24)
    //     0xa0f374: ldr             x9, [x9, #0x2c0]
    // 0xa0f378: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0f378: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void _handleTap(dynamic) {
    // ** addr: 0xa0f37c, size: 0x38
    // 0xa0f37c: EnterFrame
    //     0xa0f37c: stp             fp, lr, [SP, #-0x10]!
    //     0xa0f380: mov             fp, SP
    // 0xa0f384: ldr             x0, [fp, #0x10]
    // 0xa0f388: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa0f388: ldur            w1, [x0, #0x17]
    // 0xa0f38c: DecompressPointer r1
    //     0xa0f38c: add             x1, x1, HEAP, lsl #32
    // 0xa0f390: CheckStackOverflow
    //     0xa0f390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0f394: cmp             SP, x16
    //     0xa0f398: b.ls            #0xa0f3ac
    // 0xa0f39c: r0 = _handleTap()
    //     0xa0f39c: bl              #0xa0f3b4  ; [package:flutter/src/material/tooltip.dart] TooltipState::_handleTap
    // 0xa0f3a0: LeaveFrame
    //     0xa0f3a0: mov             SP, fp
    //     0xa0f3a4: ldp             fp, lr, [SP], #0x10
    // 0xa0f3a8: ret
    //     0xa0f3a8: ret             
    // 0xa0f3ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0f3ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0f3b0: b               #0xa0f39c
  }
  _ _handleTap(/* No info */) {
    // ** addr: 0xa0f3b4, size: 0x128
    // 0xa0f3b4: EnterFrame
    //     0xa0f3b4: stp             fp, lr, [SP, #-0x10]!
    //     0xa0f3b8: mov             fp, SP
    // 0xa0f3bc: AllocStack(0x10)
    //     0xa0f3bc: sub             SP, SP, #0x10
    // 0xa0f3c0: SetupParameters(TooltipState this /* r1 => r0, fp-0x8 */)
    //     0xa0f3c0: mov             x0, x1
    //     0xa0f3c4: stur            x1, [fp, #-8]
    // 0xa0f3c8: CheckStackOverflow
    //     0xa0f3c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0f3cc: cmp             SP, x16
    //     0xa0f3d0: b.ls            #0xa0f4ac
    // 0xa0f3d4: LoadField: r1 = r0->field_1f
    //     0xa0f3d4: ldur            w1, [x0, #0x1f]
    // 0xa0f3d8: DecompressPointer r1
    //     0xa0f3d8: add             x1, x1, HEAP, lsl #32
    // 0xa0f3dc: r16 = Sentinel
    //     0xa0f3dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0f3e0: cmp             w1, w16
    // 0xa0f3e4: b.eq            #0xa0f4b4
    // 0xa0f3e8: mov             x1, x0
    // 0xa0f3ec: r0 = _controller()
    //     0xa0f3ec: bl              #0x85e790  ; [package:flutter/src/material/tooltip.dart] TooltipState::_controller
    // 0xa0f3f0: LoadField: r1 = r0->field_43
    //     0xa0f3f0: ldur            w1, [x0, #0x43]
    // 0xa0f3f4: DecompressPointer r1
    //     0xa0f3f4: add             x1, x1, HEAP, lsl #32
    // 0xa0f3f8: r16 = Sentinel
    //     0xa0f3f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0f3fc: cmp             w1, w16
    // 0xa0f400: b.eq            #0xa0f4c0
    // 0xa0f404: r16 = Instance_AnimationStatus
    //     0xa0f404: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0xa0f408: cmp             w1, w16
    // 0xa0f40c: b.ne            #0xa0f434
    // 0xa0f410: ldur            x0, [fp, #-8]
    // 0xa0f414: mov             x1, x0
    // 0xa0f418: r0 = _enableFeedback()
    //     0xa0f418: bl              #0xa0f4dc  ; [package:flutter/src/material/tooltip.dart] TooltipState::_enableFeedback
    // 0xa0f41c: ldur            x0, [fp, #-8]
    // 0xa0f420: LoadField: r1 = r0->field_f
    //     0xa0f420: ldur            w1, [x0, #0xf]
    // 0xa0f424: DecompressPointer r1
    //     0xa0f424: add             x1, x1, HEAP, lsl #32
    // 0xa0f428: cmp             w1, NULL
    // 0xa0f42c: b.eq            #0xa0f4c8
    // 0xa0f430: r0 = forTap()
    //     0xa0f430: bl              #0x9f3464  ; [package:flutter/src/widgets/feedback.dart] Feedback::forTap
    // 0xa0f434: ldur            x1, [fp, #-8]
    // 0xa0f438: LoadField: r0 = r1->field_b
    //     0xa0f438: ldur            w0, [x1, #0xb]
    // 0xa0f43c: DecompressPointer r0
    //     0xa0f43c: add             x0, x0, HEAP, lsl #32
    // 0xa0f440: cmp             w0, NULL
    // 0xa0f444: b.eq            #0xa0f4cc
    // 0xa0f448: LoadField: r0 = r1->field_3b
    //     0xa0f448: ldur            w0, [x1, #0x3b]
    // 0xa0f44c: DecompressPointer r0
    //     0xa0f44c: add             x0, x0, HEAP, lsl #32
    // 0xa0f450: LoadField: r2 = r0->field_13
    //     0xa0f450: ldur            w2, [x0, #0x13]
    // 0xa0f454: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa0f454: ldur            w3, [x0, #0x17]
    // 0xa0f458: r0 = LoadInt32Instr(r2)
    //     0xa0f458: sbfx            x0, x2, #1, #0x1f
    // 0xa0f45c: r2 = LoadInt32Instr(r3)
    //     0xa0f45c: sbfx            x2, x3, #1, #0x1f
    // 0xa0f460: sub             x3, x0, x2
    // 0xa0f464: cbnz            x3, #0xa0f488
    // 0xa0f468: LoadField: r0 = r1->field_23
    //     0xa0f468: ldur            w0, [x1, #0x23]
    // 0xa0f46c: DecompressPointer r0
    //     0xa0f46c: add             x0, x0, HEAP, lsl #32
    // 0xa0f470: r16 = Sentinel
    //     0xa0f470: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0f474: cmp             w0, w16
    // 0xa0f478: b.eq            #0xa0f4d0
    // 0xa0f47c: r0 = Instance_Duration
    //     0xa0f47c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38308] Obj!Duration@e3a181
    //     0xa0f480: ldr             x0, [x0, #0x308]
    // 0xa0f484: b               #0xa0f48c
    // 0xa0f488: r0 = Null
    //     0xa0f488: mov             x0, NULL
    // 0xa0f48c: str             x0, [SP]
    // 0xa0f490: r4 = const [0, 0x2, 0x1, 0x1, showDuration, 0x1, null]
    //     0xa0f490: add             x4, PP, #0x4d, lsl #12  ; [pp+0x4d7c8] List(7) [0, 0x2, 0x1, 0x1, "showDuration", 0x1, Null]
    //     0xa0f494: ldr             x4, [x4, #0x7c8]
    // 0xa0f498: r0 = _scheduleShowTooltip()
    //     0xa0f498: bl              #0xa0ee18  ; [package:flutter/src/material/tooltip.dart] TooltipState::_scheduleShowTooltip
    // 0xa0f49c: r0 = Null
    //     0xa0f49c: mov             x0, NULL
    // 0xa0f4a0: LeaveFrame
    //     0xa0f4a0: mov             SP, fp
    //     0xa0f4a4: ldp             fp, lr, [SP], #0x10
    // 0xa0f4a8: ret
    //     0xa0f4a8: ret             
    // 0xa0f4ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0f4ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0f4b0: b               #0xa0f3d4
    // 0xa0f4b4: r9 = _visible
    //     0xa0f4b4: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d760] Field <TooltipState._visible@621220820>: late (offset: 0x20)
    //     0xa0f4b8: ldr             x9, [x9, #0x760]
    // 0xa0f4bc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0f4bc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa0f4c0: r9 = _status
    //     0xa0f4c0: ldr             x9, [PP, #0x4ed8]  ; [pp+0x4ed8] Field <AnimationController._status@441066280>: late (offset: 0x44)
    // 0xa0f4c4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0f4c4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa0f4c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0f4c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0f4cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0f4cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0f4d0: r9 = _tooltipTheme
    //     0xa0f4d0: add             x9, PP, #0x3a, lsl #12  ; [pp+0x3a2c0] Field <TooltipState._tooltipTheme@621220820>: late (offset: 0x24)
    //     0xa0f4d4: ldr             x9, [x9, #0x2c0]
    // 0xa0f4d8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0f4d8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ _enableFeedback(/* No info */) {
    // ** addr: 0xa0f4dc, size: 0x4c
    // 0xa0f4dc: EnterFrame
    //     0xa0f4dc: stp             fp, lr, [SP, #-0x10]!
    //     0xa0f4e0: mov             fp, SP
    // 0xa0f4e4: LoadField: r2 = r1->field_b
    //     0xa0f4e4: ldur            w2, [x1, #0xb]
    // 0xa0f4e8: DecompressPointer r2
    //     0xa0f4e8: add             x2, x2, HEAP, lsl #32
    // 0xa0f4ec: cmp             w2, NULL
    // 0xa0f4f0: b.eq            #0xa0f518
    // 0xa0f4f4: LoadField: r2 = r1->field_23
    //     0xa0f4f4: ldur            w2, [x1, #0x23]
    // 0xa0f4f8: DecompressPointer r2
    //     0xa0f4f8: add             x2, x2, HEAP, lsl #32
    // 0xa0f4fc: r16 = Sentinel
    //     0xa0f4fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0f500: cmp             w2, w16
    // 0xa0f504: b.eq            #0xa0f51c
    // 0xa0f508: r0 = true
    //     0xa0f508: add             x0, NULL, #0x20  ; true
    // 0xa0f50c: LeaveFrame
    //     0xa0f50c: mov             SP, fp
    //     0xa0f510: ldp             fp, lr, [SP], #0x10
    // 0xa0f514: ret
    //     0xa0f514: ret             
    // 0xa0f518: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0f518: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0f51c: r9 = _tooltipTheme
    //     0xa0f51c: add             x9, PP, #0x3a, lsl #12  ; [pp+0x3a2c0] Field <TooltipState._tooltipTheme@621220820>: late (offset: 0x24)
    //     0xa0f520: ldr             x9, [x9, #0x2c0]
    // 0xa0f524: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0f524: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void _handleLongPress(dynamic) {
    // ** addr: 0xa0f528, size: 0x38
    // 0xa0f528: EnterFrame
    //     0xa0f528: stp             fp, lr, [SP, #-0x10]!
    //     0xa0f52c: mov             fp, SP
    // 0xa0f530: ldr             x0, [fp, #0x10]
    // 0xa0f534: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa0f534: ldur            w1, [x0, #0x17]
    // 0xa0f538: DecompressPointer r1
    //     0xa0f538: add             x1, x1, HEAP, lsl #32
    // 0xa0f53c: CheckStackOverflow
    //     0xa0f53c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0f540: cmp             SP, x16
    //     0xa0f544: b.ls            #0xa0f558
    // 0xa0f548: r0 = _handleLongPress()
    //     0xa0f548: bl              #0xa0f560  ; [package:flutter/src/material/tooltip.dart] TooltipState::_handleLongPress
    // 0xa0f54c: LeaveFrame
    //     0xa0f54c: mov             SP, fp
    //     0xa0f550: ldp             fp, lr, [SP], #0x10
    // 0xa0f554: ret
    //     0xa0f554: ret             
    // 0xa0f558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0f558: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0f55c: b               #0xa0f548
  }
  _ _handleLongPress(/* No info */) {
    // ** addr: 0xa0f560, size: 0xf8
    // 0xa0f560: EnterFrame
    //     0xa0f560: stp             fp, lr, [SP, #-0x10]!
    //     0xa0f564: mov             fp, SP
    // 0xa0f568: AllocStack(0x8)
    //     0xa0f568: sub             SP, SP, #8
    // 0xa0f56c: SetupParameters(TooltipState this /* r1 => r0, fp-0x8 */)
    //     0xa0f56c: mov             x0, x1
    //     0xa0f570: stur            x1, [fp, #-8]
    // 0xa0f574: CheckStackOverflow
    //     0xa0f574: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0f578: cmp             SP, x16
    //     0xa0f57c: b.ls            #0xa0f624
    // 0xa0f580: LoadField: r1 = r0->field_1f
    //     0xa0f580: ldur            w1, [x0, #0x1f]
    // 0xa0f584: DecompressPointer r1
    //     0xa0f584: add             x1, x1, HEAP, lsl #32
    // 0xa0f588: r16 = Sentinel
    //     0xa0f588: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0f58c: cmp             w1, w16
    // 0xa0f590: b.eq            #0xa0f62c
    // 0xa0f594: mov             x1, x0
    // 0xa0f598: r0 = _controller()
    //     0xa0f598: bl              #0x85e790  ; [package:flutter/src/material/tooltip.dart] TooltipState::_controller
    // 0xa0f59c: LoadField: r1 = r0->field_43
    //     0xa0f59c: ldur            w1, [x0, #0x43]
    // 0xa0f5a0: DecompressPointer r1
    //     0xa0f5a0: add             x1, x1, HEAP, lsl #32
    // 0xa0f5a4: r16 = Sentinel
    //     0xa0f5a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0f5a8: cmp             w1, w16
    // 0xa0f5ac: b.eq            #0xa0f638
    // 0xa0f5b0: r16 = Instance_AnimationStatus
    //     0xa0f5b0: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0xa0f5b4: cmp             w1, w16
    // 0xa0f5b8: b.ne            #0xa0f5f8
    // 0xa0f5bc: ldur            x0, [fp, #-8]
    // 0xa0f5c0: LoadField: r1 = r0->field_b
    //     0xa0f5c0: ldur            w1, [x0, #0xb]
    // 0xa0f5c4: DecompressPointer r1
    //     0xa0f5c4: add             x1, x1, HEAP, lsl #32
    // 0xa0f5c8: cmp             w1, NULL
    // 0xa0f5cc: b.eq            #0xa0f640
    // 0xa0f5d0: LoadField: r1 = r0->field_23
    //     0xa0f5d0: ldur            w1, [x0, #0x23]
    // 0xa0f5d4: DecompressPointer r1
    //     0xa0f5d4: add             x1, x1, HEAP, lsl #32
    // 0xa0f5d8: r16 = Sentinel
    //     0xa0f5d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0f5dc: cmp             w1, w16
    // 0xa0f5e0: b.eq            #0xa0f644
    // 0xa0f5e4: LoadField: r1 = r0->field_f
    //     0xa0f5e4: ldur            w1, [x0, #0xf]
    // 0xa0f5e8: DecompressPointer r1
    //     0xa0f5e8: add             x1, x1, HEAP, lsl #32
    // 0xa0f5ec: cmp             w1, NULL
    // 0xa0f5f0: b.eq            #0xa0f650
    // 0xa0f5f4: r0 = forLongPress()
    //     0xa0f5f4: bl              #0xa0b754  ; [package:flutter/src/widgets/feedback.dart] Feedback::forLongPress
    // 0xa0f5f8: ldur            x1, [fp, #-8]
    // 0xa0f5fc: LoadField: r0 = r1->field_b
    //     0xa0f5fc: ldur            w0, [x1, #0xb]
    // 0xa0f600: DecompressPointer r0
    //     0xa0f600: add             x0, x0, HEAP, lsl #32
    // 0xa0f604: cmp             w0, NULL
    // 0xa0f608: b.eq            #0xa0f654
    // 0xa0f60c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa0f60c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa0f610: r0 = _scheduleShowTooltip()
    //     0xa0f610: bl              #0xa0ee18  ; [package:flutter/src/material/tooltip.dart] TooltipState::_scheduleShowTooltip
    // 0xa0f614: r0 = Null
    //     0xa0f614: mov             x0, NULL
    // 0xa0f618: LeaveFrame
    //     0xa0f618: mov             SP, fp
    //     0xa0f61c: ldp             fp, lr, [SP], #0x10
    // 0xa0f620: ret
    //     0xa0f620: ret             
    // 0xa0f624: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0f624: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0f628: b               #0xa0f580
    // 0xa0f62c: r9 = _visible
    //     0xa0f62c: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d760] Field <TooltipState._visible@621220820>: late (offset: 0x20)
    //     0xa0f630: ldr             x9, [x9, #0x760]
    // 0xa0f634: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0f634: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa0f638: r9 = _status
    //     0xa0f638: ldr             x9, [PP, #0x4ed8]  ; [pp+0x4ed8] Field <AnimationController._status@441066280>: late (offset: 0x44)
    // 0xa0f63c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0f63c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa0f640: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0f640: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0f644: r9 = _tooltipTheme
    //     0xa0f644: add             x9, PP, #0x3a, lsl #12  ; [pp+0x3a2c0] Field <TooltipState._tooltipTheme@621220820>: late (offset: 0x24)
    //     0xa0f648: ldr             x9, [x9, #0x2c0]
    // 0xa0f64c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0f64c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa0f650: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0f650: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0f654: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0f654: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7ef28, size: 0x138
    // 0xa7ef28: EnterFrame
    //     0xa7ef28: stp             fp, lr, [SP, #-0x10]!
    //     0xa7ef2c: mov             fp, SP
    // 0xa7ef30: AllocStack(0x10)
    //     0xa7ef30: sub             SP, SP, #0x10
    // 0xa7ef34: SetupParameters(TooltipState this /* r1 => r0, fp-0x10 */)
    //     0xa7ef34: mov             x0, x1
    //     0xa7ef38: stur            x1, [fp, #-0x10]
    // 0xa7ef3c: CheckStackOverflow
    //     0xa7ef3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7ef40: cmp             SP, x16
    //     0xa7ef44: b.ls            #0xa7f054
    // 0xa7ef48: r1 = LoadStaticField(0x968)
    //     0xa7ef48: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xa7ef4c: ldr             x1, [x1, #0x12d0]
    // 0xa7ef50: cmp             w1, NULL
    // 0xa7ef54: b.eq            #0xa7f05c
    // 0xa7ef58: LoadField: r3 = r1->field_13
    //     0xa7ef58: ldur            w3, [x1, #0x13]
    // 0xa7ef5c: DecompressPointer r3
    //     0xa7ef5c: add             x3, x3, HEAP, lsl #32
    // 0xa7ef60: mov             x2, x0
    // 0xa7ef64: stur            x3, [fp, #-8]
    // 0xa7ef68: r1 = Function '_handleGlobalPointerEvent@621220820':.
    //     0xa7ef68: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d7e8] AnonymousClosure: (0x93b558), in [package:flutter/src/material/tooltip.dart] TooltipState::_handleGlobalPointerEvent (0x93b594)
    //     0xa7ef6c: ldr             x1, [x1, #0x7e8]
    // 0xa7ef70: r0 = AllocateClosure()
    //     0xa7ef70: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7ef74: ldur            x1, [fp, #-8]
    // 0xa7ef78: mov             x2, x0
    // 0xa7ef7c: r0 = invalidateScopeData()
    //     0xa7ef7c: bl              #0x6b8c9c  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::invalidateScopeData
    // 0xa7ef80: r0 = InitLateStaticField(0xb60) // [package:flutter/src/material/tooltip.dart] Tooltip::_openedTooltips
    //     0xa7ef80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa7ef84: ldr             x0, [x0, #0x16c0]
    //     0xa7ef88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa7ef8c: cmp             w0, w16
    //     0xa7ef90: b.ne            #0xa7efa0
    //     0xa7ef94: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a290] Field <Tooltip._openedTooltips@621220820>: static late final (offset: 0xb60)
    //     0xa7ef98: ldr             x2, [x2, #0x290]
    //     0xa7ef9c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa7efa0: mov             x1, x0
    // 0xa7efa4: ldur            x2, [fp, #-0x10]
    // 0xa7efa8: r0 = remove()
    //     0xa7efa8: bl              #0x6ec83c  ; [dart:core] _GrowableList::remove
    // 0xa7efac: ldur            x0, [fp, #-0x10]
    // 0xa7efb0: LoadField: r1 = r0->field_33
    //     0xa7efb0: ldur            w1, [x0, #0x33]
    // 0xa7efb4: DecompressPointer r1
    //     0xa7efb4: add             x1, x1, HEAP, lsl #32
    // 0xa7efb8: cmp             w1, NULL
    // 0xa7efbc: b.eq            #0xa7efc4
    // 0xa7efc0: StoreField: r1->field_57 = rNULL
    //     0xa7efc0: stur            NULL, [x1, #0x57]
    // 0xa7efc4: cmp             w1, NULL
    // 0xa7efc8: b.eq            #0xa7efd4
    // 0xa7efcc: r0 = dispose()
    //     0xa7efcc: bl              #0x7f9248  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::dispose
    // 0xa7efd0: ldur            x0, [fp, #-0x10]
    // 0xa7efd4: LoadField: r1 = r0->field_37
    //     0xa7efd4: ldur            w1, [x0, #0x37]
    // 0xa7efd8: DecompressPointer r1
    //     0xa7efd8: add             x1, x1, HEAP, lsl #32
    // 0xa7efdc: cmp             w1, NULL
    // 0xa7efe0: b.eq            #0xa7efe8
    // 0xa7efe4: StoreField: r1->field_63 = rNULL
    //     0xa7efe4: stur            NULL, [x1, #0x63]
    // 0xa7efe8: cmp             w1, NULL
    // 0xa7efec: b.eq            #0xa7eff8
    // 0xa7eff0: r0 = dispose()
    //     0xa7eff0: bl              #0x7f9248  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::dispose
    // 0xa7eff4: ldur            x0, [fp, #-0x10]
    // 0xa7eff8: LoadField: r1 = r0->field_27
    //     0xa7eff8: ldur            w1, [x0, #0x27]
    // 0xa7effc: DecompressPointer r1
    //     0xa7effc: add             x1, x1, HEAP, lsl #32
    // 0xa7f000: cmp             w1, NULL
    // 0xa7f004: b.eq            #0xa7f010
    // 0xa7f008: r0 = cancel()
    //     0xa7f008: bl              #0x5fe740  ; [dart:isolate] _Timer::cancel
    // 0xa7f00c: ldur            x0, [fp, #-0x10]
    // 0xa7f010: LoadField: r1 = r0->field_2b
    //     0xa7f010: ldur            w1, [x0, #0x2b]
    // 0xa7f014: DecompressPointer r1
    //     0xa7f014: add             x1, x1, HEAP, lsl #32
    // 0xa7f018: cmp             w1, NULL
    // 0xa7f01c: b.eq            #0xa7f028
    // 0xa7f020: r0 = dispose()
    //     0xa7f020: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa7f024: ldur            x0, [fp, #-0x10]
    // 0xa7f028: LoadField: r1 = r0->field_2f
    //     0xa7f028: ldur            w1, [x0, #0x2f]
    // 0xa7f02c: DecompressPointer r1
    //     0xa7f02c: add             x1, x1, HEAP, lsl #32
    // 0xa7f030: cmp             w1, NULL
    // 0xa7f034: b.eq            #0xa7f03c
    // 0xa7f038: r0 = dispose()
    //     0xa7f038: bl              #0x7a0b6c  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::dispose
    // 0xa7f03c: ldur            x1, [fp, #-0x10]
    // 0xa7f040: r0 = dispose()
    //     0xa7f040: bl              #0xa7f060  ; [package:flutter/src/material/tooltip.dart] _TooltipState&State&SingleTickerProviderStateMixin::dispose
    // 0xa7f044: r0 = Null
    //     0xa7f044: mov             x0, NULL
    // 0xa7f048: LeaveFrame
    //     0xa7f048: mov             SP, fp
    //     0xa7f04c: ldp             fp, lr, [SP], #0x10
    // 0xa7f050: ret
    //     0xa7f050: ret             
    // 0xa7f054: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7f054: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7f058: b               #0xa7ef48
    // 0xa7f05c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7f05c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ TooltipState(/* No info */) {
    // ** addr: 0xa91240, size: 0xf8
    // 0xa91240: EnterFrame
    //     0xa91240: stp             fp, lr, [SP, #-0x10]!
    //     0xa91244: mov             fp, SP
    // 0xa91248: AllocStack(0x18)
    //     0xa91248: sub             SP, SP, #0x18
    // 0xa9124c: r2 = Sentinel
    //     0xa9124c: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa91250: r0 = Instance_AnimationStatus
    //     0xa91250: ldr             x0, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0xa91254: stur            x1, [fp, #-8]
    // 0xa91258: CheckStackOverflow
    //     0xa91258: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9125c: cmp             SP, x16
    //     0xa91260: b.ls            #0xa91330
    // 0xa91264: StoreField: r1->field_1f = r2
    //     0xa91264: stur            w2, [x1, #0x1f]
    // 0xa91268: StoreField: r1->field_23 = r2
    //     0xa91268: stur            w2, [x1, #0x23]
    // 0xa9126c: StoreField: r1->field_3f = r0
    //     0xa9126c: stur            w0, [x1, #0x3f]
    // 0xa91270: r0 = OverlayPortalController()
    //     0xa91270: bl              #0xa91338  ; AllocateOverlayPortalControllerStub -> OverlayPortalController (size=0x10)
    // 0xa91274: ldur            x1, [fp, #-8]
    // 0xa91278: StoreField: r1->field_1b = r0
    //     0xa91278: stur            w0, [x1, #0x1b]
    //     0xa9127c: ldurb           w16, [x1, #-1]
    //     0xa91280: ldurb           w17, [x0, #-1]
    //     0xa91284: and             x16, x17, x16, lsr #2
    //     0xa91288: tst             x16, HEAP, lsr #32
    //     0xa9128c: b.eq            #0xa91294
    //     0xa91290: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa91294: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xa91294: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa91298: ldr             x0, [x0, #0x778]
    //     0xa9129c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa912a0: cmp             w0, w16
    //     0xa912a4: b.ne            #0xa912b0
    //     0xa912a8: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xa912ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa912b0: r1 = <int>
    //     0xa912b0: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xa912b4: stur            x0, [fp, #-0x10]
    // 0xa912b8: r0 = _Set()
    //     0xa912b8: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xa912bc: mov             x1, x0
    // 0xa912c0: ldur            x0, [fp, #-0x10]
    // 0xa912c4: stur            x1, [fp, #-0x18]
    // 0xa912c8: StoreField: r1->field_1b = r0
    //     0xa912c8: stur            w0, [x1, #0x1b]
    // 0xa912cc: StoreField: r1->field_b = rZR
    //     0xa912cc: stur            wzr, [x1, #0xb]
    // 0xa912d0: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xa912d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa912d4: ldr             x0, [x0, #0x780]
    //     0xa912d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa912dc: cmp             w0, w16
    //     0xa912e0: b.ne            #0xa912ec
    //     0xa912e4: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xa912e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa912ec: mov             x1, x0
    // 0xa912f0: ldur            x0, [fp, #-0x18]
    // 0xa912f4: StoreField: r0->field_f = r1
    //     0xa912f4: stur            w1, [x0, #0xf]
    // 0xa912f8: StoreField: r0->field_13 = rZR
    //     0xa912f8: stur            wzr, [x0, #0x13]
    // 0xa912fc: ArrayStore: r0[0] = rZR  ; List_4
    //     0xa912fc: stur            wzr, [x0, #0x17]
    // 0xa91300: ldur            x1, [fp, #-8]
    // 0xa91304: StoreField: r1->field_3b = r0
    //     0xa91304: stur            w0, [x1, #0x3b]
    //     0xa91308: ldurb           w16, [x1, #-1]
    //     0xa9130c: ldurb           w17, [x0, #-1]
    //     0xa91310: and             x16, x17, x16, lsr #2
    //     0xa91314: tst             x16, HEAP, lsr #32
    //     0xa91318: b.eq            #0xa91320
    //     0xa9131c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa91320: r0 = Null
    //     0xa91320: mov             x0, NULL
    // 0xa91324: LeaveFrame
    //     0xa91324: mov             SP, fp
    //     0xa91328: ldp             fp, lr, [SP], #0x10
    // 0xa9132c: ret
    //     0xa9132c: ret             
    // 0xa91330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa91330: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa91334: b               #0xa91264
  }
}

// class id: 4550, size: 0x28, field offset: 0x28
//   const constructor, 
class _ExclusiveMouseRegion extends MouseRegion {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x85a508, size: 0x64
    // 0x85a508: EnterFrame
    //     0x85a508: stp             fp, lr, [SP, #-0x10]!
    //     0x85a50c: mov             fp, SP
    // 0x85a510: AllocStack(0x10)
    //     0x85a510: sub             SP, SP, #0x10
    // 0x85a514: CheckStackOverflow
    //     0x85a514: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85a518: cmp             SP, x16
    //     0x85a51c: b.ls            #0x85a564
    // 0x85a520: LoadField: r2 = r1->field_f
    //     0x85a520: ldur            w2, [x1, #0xf]
    // 0x85a524: DecompressPointer r2
    //     0x85a524: add             x2, x2, HEAP, lsl #32
    // 0x85a528: stur            x2, [fp, #-0x10]
    // 0x85a52c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x85a52c: ldur            w3, [x1, #0x17]
    // 0x85a530: DecompressPointer r3
    //     0x85a530: add             x3, x3, HEAP, lsl #32
    // 0x85a534: stur            x3, [fp, #-8]
    // 0x85a538: r0 = _RenderExclusiveMouseRegion()
    //     0x85a538: bl              #0x85a810  ; Allocate_RenderExclusiveMouseRegionStub -> _RenderExclusiveMouseRegion (size=0x78)
    // 0x85a53c: mov             x1, x0
    // 0x85a540: ldur            x2, [fp, #-0x10]
    // 0x85a544: ldur            x3, [fp, #-8]
    // 0x85a548: stur            x0, [fp, #-8]
    // 0x85a54c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x85a54c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x85a550: r0 = RenderMouseRegion()
    //     0x85a550: bl              #0x85a56c  ; [package:flutter/src/rendering/proxy_box.dart] RenderMouseRegion::RenderMouseRegion
    // 0x85a554: ldur            x0, [fp, #-8]
    // 0x85a558: LeaveFrame
    //     0x85a558: mov             SP, fp
    //     0x85a55c: ldp             fp, lr, [SP], #0x10
    // 0x85a560: ret
    //     0x85a560: ret             
    // 0x85a564: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85a564: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85a568: b               #0x85a520
  }
}

// class id: 4805, size: 0x58, field offset: 0xc
//   const constructor, 
class Tooltip extends StatefulWidget {

  static late final List<TooltipState> _openedTooltips; // offset: 0xb60

  static List<TooltipState> _openedTooltips() {
    // ** addr: 0x85ef04, size: 0x38
    // 0x85ef04: EnterFrame
    //     0x85ef04: stp             fp, lr, [SP, #-0x10]!
    //     0x85ef08: mov             fp, SP
    // 0x85ef0c: CheckStackOverflow
    //     0x85ef0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85ef10: cmp             SP, x16
    //     0x85ef14: b.ls            #0x85ef34
    // 0x85ef18: r1 = <TooltipState>
    //     0x85ef18: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a2b8] TypeArguments: <TooltipState>
    //     0x85ef1c: ldr             x1, [x1, #0x2b8]
    // 0x85ef20: r2 = 0
    //     0x85ef20: movz            x2, #0
    // 0x85ef24: r0 = _GrowableList()
    //     0x85ef24: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x85ef28: LeaveFrame
    //     0x85ef28: mov             SP, fp
    //     0x85ef2c: ldp             fp, lr, [SP], #0x10
    // 0x85ef30: ret
    //     0x85ef30: ret             
    // 0x85ef34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85ef34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85ef38: b               #0x85ef18
  }
  static bool dismissAllToolTips() {
    // ** addr: 0x9e4c44, size: 0x15c
    // 0x9e4c44: EnterFrame
    //     0x9e4c44: stp             fp, lr, [SP, #-0x10]!
    //     0x9e4c48: mov             fp, SP
    // 0x9e4c4c: AllocStack(0x28)
    //     0x9e4c4c: sub             SP, SP, #0x28
    // 0x9e4c50: CheckStackOverflow
    //     0x9e4c50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e4c54: cmp             SP, x16
    //     0x9e4c58: b.ls            #0x9e4d90
    // 0x9e4c5c: r0 = InitLateStaticField(0xb60) // [package:flutter/src/material/tooltip.dart] Tooltip::_openedTooltips
    //     0x9e4c5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9e4c60: ldr             x0, [x0, #0x16c0]
    //     0x9e4c64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9e4c68: cmp             w0, w16
    //     0x9e4c6c: b.ne            #0x9e4c7c
    //     0x9e4c70: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a290] Field <Tooltip._openedTooltips@621220820>: static late final (offset: 0xb60)
    //     0x9e4c74: ldr             x2, [x2, #0x290]
    //     0x9e4c78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9e4c7c: LoadField: r1 = r0->field_b
    //     0x9e4c7c: ldur            w1, [x0, #0xb]
    // 0x9e4c80: cbz             w1, #0x9e4d60
    // 0x9e4c84: mov             x1, x0
    // 0x9e4c88: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9e4c88: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9e4c8c: r0 = toList()
    //     0x9e4c8c: bl              #0xa52dc8  ; [dart:core] _GrowableList::toList
    // 0x9e4c90: mov             x3, x0
    // 0x9e4c94: stur            x3, [fp, #-0x28]
    // 0x9e4c98: LoadField: r4 = r3->field_7
    //     0x9e4c98: ldur            w4, [x3, #7]
    // 0x9e4c9c: DecompressPointer r4
    //     0x9e4c9c: add             x4, x4, HEAP, lsl #32
    // 0x9e4ca0: stur            x4, [fp, #-0x20]
    // 0x9e4ca4: LoadField: r0 = r3->field_b
    //     0x9e4ca4: ldur            w0, [x3, #0xb]
    // 0x9e4ca8: r5 = LoadInt32Instr(r0)
    //     0x9e4ca8: sbfx            x5, x0, #1, #0x1f
    // 0x9e4cac: stur            x5, [fp, #-0x18]
    // 0x9e4cb0: r0 = 0
    //     0x9e4cb0: movz            x0, #0
    // 0x9e4cb4: CheckStackOverflow
    //     0x9e4cb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e4cb8: cmp             SP, x16
    //     0x9e4cbc: b.ls            #0x9e4d98
    // 0x9e4cc0: LoadField: r1 = r3->field_b
    //     0x9e4cc0: ldur            w1, [x3, #0xb]
    // 0x9e4cc4: r2 = LoadInt32Instr(r1)
    //     0x9e4cc4: sbfx            x2, x1, #1, #0x1f
    // 0x9e4cc8: cmp             x5, x2
    // 0x9e4ccc: b.ne            #0x9e4d70
    // 0x9e4cd0: cmp             x0, x2
    // 0x9e4cd4: b.ge            #0x9e4d50
    // 0x9e4cd8: LoadField: r1 = r3->field_f
    //     0x9e4cd8: ldur            w1, [x3, #0xf]
    // 0x9e4cdc: DecompressPointer r1
    //     0x9e4cdc: add             x1, x1, HEAP, lsl #32
    // 0x9e4ce0: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0x9e4ce0: add             x16, x1, x0, lsl #2
    //     0x9e4ce4: ldur            w6, [x16, #0xf]
    // 0x9e4ce8: DecompressPointer r6
    //     0x9e4ce8: add             x6, x6, HEAP, lsl #32
    // 0x9e4cec: stur            x6, [fp, #-0x10]
    // 0x9e4cf0: add             x7, x0, #1
    // 0x9e4cf4: stur            x7, [fp, #-8]
    // 0x9e4cf8: cmp             w6, NULL
    // 0x9e4cfc: b.ne            #0x9e4d30
    // 0x9e4d00: mov             x0, x6
    // 0x9e4d04: mov             x2, x4
    // 0x9e4d08: r1 = Null
    //     0x9e4d08: mov             x1, NULL
    // 0x9e4d0c: cmp             w2, NULL
    // 0x9e4d10: b.eq            #0x9e4d30
    // 0x9e4d14: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9e4d14: ldur            w4, [x2, #0x17]
    // 0x9e4d18: DecompressPointer r4
    //     0x9e4d18: add             x4, x4, HEAP, lsl #32
    // 0x9e4d1c: r8 = X0
    //     0x9e4d1c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x9e4d20: LoadField: r9 = r4->field_7
    //     0x9e4d20: ldur            x9, [x4, #7]
    // 0x9e4d24: r3 = Null
    //     0x9e4d24: add             x3, PP, #0x44, lsl #12  ; [pp+0x44880] Null
    //     0x9e4d28: ldr             x3, [x3, #0x880]
    // 0x9e4d2c: blr             x9
    // 0x9e4d30: ldur            x1, [fp, #-0x10]
    // 0x9e4d34: r2 = Instance_Duration
    //     0x9e4d34: ldr             x2, [PP, #0x1d38]  ; [pp+0x1d38] Obj!Duration@e3a081
    // 0x9e4d38: r0 = _scheduleDismissTooltip()
    //     0x9e4d38: bl              #0x85e69c  ; [package:flutter/src/material/tooltip.dart] TooltipState::_scheduleDismissTooltip
    // 0x9e4d3c: ldur            x0, [fp, #-8]
    // 0x9e4d40: ldur            x3, [fp, #-0x28]
    // 0x9e4d44: ldur            x4, [fp, #-0x20]
    // 0x9e4d48: ldur            x5, [fp, #-0x18]
    // 0x9e4d4c: b               #0x9e4cb4
    // 0x9e4d50: r0 = true
    //     0x9e4d50: add             x0, NULL, #0x20  ; true
    // 0x9e4d54: LeaveFrame
    //     0x9e4d54: mov             SP, fp
    //     0x9e4d58: ldp             fp, lr, [SP], #0x10
    // 0x9e4d5c: ret
    //     0x9e4d5c: ret             
    // 0x9e4d60: r0 = false
    //     0x9e4d60: add             x0, NULL, #0x30  ; false
    // 0x9e4d64: LeaveFrame
    //     0x9e4d64: mov             SP, fp
    //     0x9e4d68: ldp             fp, lr, [SP], #0x10
    // 0x9e4d6c: ret
    //     0x9e4d6c: ret             
    // 0x9e4d70: mov             x0, x3
    // 0x9e4d74: r0 = ConcurrentModificationError()
    //     0x9e4d74: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x9e4d78: mov             x1, x0
    // 0x9e4d7c: ldur            x0, [fp, #-0x28]
    // 0x9e4d80: StoreField: r1->field_b = r0
    //     0x9e4d80: stur            w0, [x1, #0xb]
    // 0x9e4d84: mov             x0, x1
    // 0x9e4d88: r0 = Throw()
    //     0x9e4d88: bl              #0xec04b8  ; ThrowStub
    // 0x9e4d8c: brk             #0
    // 0x9e4d90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e4d90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e4d94: b               #0x9e4c5c
    // 0x9e4d98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e4d98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e4d9c: b               #0x9e4cc0
  }
  _ createState(/* No info */) {
    // ** addr: 0xa911f8, size: 0x48
    // 0xa911f8: EnterFrame
    //     0xa911f8: stp             fp, lr, [SP, #-0x10]!
    //     0xa911fc: mov             fp, SP
    // 0xa91200: AllocStack(0x8)
    //     0xa91200: sub             SP, SP, #8
    // 0xa91204: CheckStackOverflow
    //     0xa91204: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa91208: cmp             SP, x16
    //     0xa9120c: b.ls            #0xa91238
    // 0xa91210: r1 = <Tooltip>
    //     0xa91210: add             x1, PP, #0x43, lsl #12  ; [pp+0x43898] TypeArguments: <Tooltip>
    //     0xa91214: ldr             x1, [x1, #0x898]
    // 0xa91218: r0 = TooltipState()
    //     0xa91218: bl              #0xa91344  ; AllocateTooltipStateStub -> TooltipState (size=0x44)
    // 0xa9121c: mov             x1, x0
    // 0xa91220: stur            x0, [fp, #-8]
    // 0xa91224: r0 = TooltipState()
    //     0xa91224: bl              #0xa91240  ; [package:flutter/src/material/tooltip.dart] TooltipState::TooltipState
    // 0xa91228: ldur            x0, [fp, #-8]
    // 0xa9122c: LeaveFrame
    //     0xa9122c: mov             SP, fp
    //     0xa91230: ldp             fp, lr, [SP], #0x10
    // 0xa91234: ret
    //     0xa91234: ret             
    // 0xa91238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa91238: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9123c: b               #0xa91210
  }
}

// class id: 5362, size: 0x48, field offset: 0xc
//   const constructor, 
class _TooltipOverlay extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xaa1318, size: 0x374
    // 0xaa1318: EnterFrame
    //     0xaa1318: stp             fp, lr, [SP, #-0x10]!
    //     0xaa131c: mov             fp, SP
    // 0xaa1320: AllocStack(0x80)
    //     0xaa1320: sub             SP, SP, #0x80
    // 0xaa1324: SetupParameters(_TooltipOverlay this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xaa1324: mov             x0, x1
    //     0xaa1328: stur            x1, [fp, #-0x10]
    //     0xaa132c: mov             x1, x2
    //     0xaa1330: stur            x2, [fp, #-0x18]
    // 0xaa1334: CheckStackOverflow
    //     0xaa1334: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa1338: cmp             SP, x16
    //     0xaa133c: b.ls            #0xaa1654
    // 0xaa1340: LoadField: r2 = r0->field_2b
    //     0xaa1340: ldur            w2, [x0, #0x2b]
    // 0xaa1344: DecompressPointer r2
    //     0xaa1344: add             x2, x2, HEAP, lsl #32
    // 0xaa1348: stur            x2, [fp, #-8]
    // 0xaa134c: LoadField: d0 = r0->field_f
    //     0xaa134c: ldur            d0, [x0, #0xf]
    // 0xaa1350: stur            d0, [fp, #-0x58]
    // 0xaa1354: r0 = BoxConstraints()
    //     0xaa1354: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xaa1358: stur            x0, [fp, #-0x20]
    // 0xaa135c: StoreField: r0->field_7 = rZR
    //     0xaa135c: stur            xzr, [x0, #7]
    // 0xaa1360: d0 = inf
    //     0xaa1360: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xaa1364: StoreField: r0->field_f = d0
    //     0xaa1364: stur            d0, [x0, #0xf]
    // 0xaa1368: ldur            d1, [fp, #-0x58]
    // 0xaa136c: ArrayStore: r0[0] = d1  ; List_8
    //     0xaa136c: stur            d1, [x0, #0x17]
    // 0xaa1370: StoreField: r0->field_1f = d0
    //     0xaa1370: stur            d0, [x0, #0x1f]
    // 0xaa1374: ldur            x1, [fp, #-0x18]
    // 0xaa1378: r0 = of()
    //     0xaa1378: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaa137c: LoadField: r1 = r0->field_8f
    //     0xaa137c: ldur            w1, [x0, #0x8f]
    // 0xaa1380: DecompressPointer r1
    //     0xaa1380: add             x1, x1, HEAP, lsl #32
    // 0xaa1384: LoadField: r0 = r1->field_2f
    //     0xaa1384: ldur            w0, [x1, #0x2f]
    // 0xaa1388: DecompressPointer r0
    //     0xaa1388: add             x0, x0, HEAP, lsl #32
    // 0xaa138c: stur            x0, [fp, #-0x48]
    // 0xaa1390: cmp             w0, NULL
    // 0xaa1394: b.eq            #0xaa165c
    // 0xaa1398: ldur            x1, [fp, #-0x10]
    // 0xaa139c: LoadField: r2 = r1->field_1f
    //     0xaa139c: ldur            w2, [x1, #0x1f]
    // 0xaa13a0: DecompressPointer r2
    //     0xaa13a0: add             x2, x2, HEAP, lsl #32
    // 0xaa13a4: stur            x2, [fp, #-0x40]
    // 0xaa13a8: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xaa13a8: ldur            w3, [x1, #0x17]
    // 0xaa13ac: DecompressPointer r3
    //     0xaa13ac: add             x3, x3, HEAP, lsl #32
    // 0xaa13b0: stur            x3, [fp, #-0x38]
    // 0xaa13b4: LoadField: r4 = r1->field_b
    //     0xaa13b4: ldur            w4, [x1, #0xb]
    // 0xaa13b8: DecompressPointer r4
    //     0xaa13b8: add             x4, x4, HEAP, lsl #32
    // 0xaa13bc: stur            x4, [fp, #-0x30]
    // 0xaa13c0: LoadField: r5 = r1->field_23
    //     0xaa13c0: ldur            w5, [x1, #0x23]
    // 0xaa13c4: DecompressPointer r5
    //     0xaa13c4: add             x5, x5, HEAP, lsl #32
    // 0xaa13c8: stur            x5, [fp, #-0x28]
    // 0xaa13cc: r0 = Text()
    //     0xaa13cc: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaa13d0: mov             x1, x0
    // 0xaa13d4: ldur            x0, [fp, #-0x30]
    // 0xaa13d8: stur            x1, [fp, #-0x50]
    // 0xaa13dc: StoreField: r1->field_f = r0
    //     0xaa13dc: stur            w0, [x1, #0xf]
    // 0xaa13e0: ldur            x0, [fp, #-0x28]
    // 0xaa13e4: StoreField: r1->field_13 = r0
    //     0xaa13e4: stur            w0, [x1, #0x13]
    // 0xaa13e8: r0 = Instance_TextAlign
    //     0xaa13e8: ldr             x0, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xaa13ec: StoreField: r1->field_1b = r0
    //     0xaa13ec: stur            w0, [x1, #0x1b]
    // 0xaa13f0: r0 = Center()
    //     0xaa13f0: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaa13f4: mov             x1, x0
    // 0xaa13f8: r0 = Instance_Alignment
    //     0xaa13f8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xaa13fc: ldr             x0, [x0, #0x898]
    // 0xaa1400: stur            x1, [fp, #-0x28]
    // 0xaa1404: StoreField: r1->field_f = r0
    //     0xaa1404: stur            w0, [x1, #0xf]
    // 0xaa1408: r0 = 1.000000
    //     0xaa1408: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xaa140c: StoreField: r1->field_13 = r0
    //     0xaa140c: stur            w0, [x1, #0x13]
    // 0xaa1410: ArrayStore: r1[0] = r0  ; List_4
    //     0xaa1410: stur            w0, [x1, #0x17]
    // 0xaa1414: ldur            x0, [fp, #-0x50]
    // 0xaa1418: StoreField: r1->field_b = r0
    //     0xaa1418: stur            w0, [x1, #0xb]
    // 0xaa141c: r0 = Container()
    //     0xaa141c: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaa1420: stur            x0, [fp, #-0x30]
    // 0xaa1424: ldur            x16, [fp, #-0x40]
    // 0xaa1428: ldur            lr, [fp, #-0x38]
    // 0xaa142c: stp             lr, x16, [SP, #0x10]
    // 0xaa1430: r16 = Instance_EdgeInsets
    //     0xaa1430: ldr             x16, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xaa1434: ldur            lr, [fp, #-0x28]
    // 0xaa1438: stp             lr, x16, [SP]
    // 0xaa143c: mov             x1, x0
    // 0xaa1440: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x1, margin, 0x3, padding, 0x2, null]
    //     0xaa1440: add             x4, PP, #0x54, lsl #12  ; [pp+0x547d0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x1, "margin", 0x3, "padding", 0x2, Null]
    //     0xaa1444: ldr             x4, [x4, #0x7d0]
    // 0xaa1448: r0 = Container()
    //     0xaa1448: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaa144c: r0 = Semantics()
    //     0xaa144c: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0xaa1450: stur            x0, [fp, #-0x28]
    // 0xaa1454: r16 = true
    //     0xaa1454: add             x16, NULL, #0x20  ; true
    // 0xaa1458: ldur            lr, [fp, #-0x30]
    // 0xaa145c: stp             lr, x16, [SP]
    // 0xaa1460: mov             x1, x0
    // 0xaa1464: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, container, 0x1, null]
    //     0xaa1464: add             x4, PP, #0x39, lsl #12  ; [pp+0x399c0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "container", 0x1, Null]
    //     0xaa1468: ldr             x4, [x4, #0x9c0]
    // 0xaa146c: r0 = Semantics()
    //     0xaa146c: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0xaa1470: r0 = DefaultTextStyle()
    //     0xaa1470: bl              #0x9d5004  ; AllocateDefaultTextStyleStub -> DefaultTextStyle (size=0x2c)
    // 0xaa1474: mov             x1, x0
    // 0xaa1478: ldur            x0, [fp, #-0x48]
    // 0xaa147c: stur            x1, [fp, #-0x30]
    // 0xaa1480: StoreField: r1->field_f = r0
    //     0xaa1480: stur            w0, [x1, #0xf]
    // 0xaa1484: r0 = true
    //     0xaa1484: add             x0, NULL, #0x20  ; true
    // 0xaa1488: ArrayStore: r1[0] = r0  ; List_4
    //     0xaa1488: stur            w0, [x1, #0x17]
    // 0xaa148c: r2 = Instance_TextOverflow
    //     0xaa148c: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xaa1490: ldr             x2, [x2, #0xc60]
    // 0xaa1494: StoreField: r1->field_1b = r2
    //     0xaa1494: stur            w2, [x1, #0x1b]
    // 0xaa1498: r2 = Instance_TextWidthBasis
    //     0xaa1498: add             x2, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0xaa149c: ldr             x2, [x2, #0x1d8]
    // 0xaa14a0: StoreField: r1->field_23 = r2
    //     0xaa14a0: stur            w2, [x1, #0x23]
    // 0xaa14a4: ldur            x2, [fp, #-0x28]
    // 0xaa14a8: StoreField: r1->field_b = r2
    //     0xaa14a8: stur            w2, [x1, #0xb]
    // 0xaa14ac: r0 = ConstrainedBox()
    //     0xaa14ac: bl              #0x9d4fe0  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xaa14b0: mov             x1, x0
    // 0xaa14b4: ldur            x0, [fp, #-0x20]
    // 0xaa14b8: stur            x1, [fp, #-0x28]
    // 0xaa14bc: StoreField: r1->field_f = r0
    //     0xaa14bc: stur            w0, [x1, #0xf]
    // 0xaa14c0: ldur            x0, [fp, #-0x30]
    // 0xaa14c4: StoreField: r1->field_b = r0
    //     0xaa14c4: stur            w0, [x1, #0xb]
    // 0xaa14c8: r0 = FadeTransition()
    //     0xaa14c8: bl              #0x91a518  ; AllocateFadeTransitionStub -> FadeTransition (size=0x18)
    // 0xaa14cc: mov             x1, x0
    // 0xaa14d0: ldur            x0, [fp, #-8]
    // 0xaa14d4: stur            x1, [fp, #-0x30]
    // 0xaa14d8: StoreField: r1->field_f = r0
    //     0xaa14d8: stur            w0, [x1, #0xf]
    // 0xaa14dc: r0 = false
    //     0xaa14dc: add             x0, NULL, #0x30  ; false
    // 0xaa14e0: StoreField: r1->field_13 = r0
    //     0xaa14e0: stur            w0, [x1, #0x13]
    // 0xaa14e4: ldur            x0, [fp, #-0x28]
    // 0xaa14e8: StoreField: r1->field_b = r0
    //     0xaa14e8: stur            w0, [x1, #0xb]
    // 0xaa14ec: ldur            x0, [fp, #-0x10]
    // 0xaa14f0: LoadField: r2 = r0->field_3f
    //     0xaa14f0: ldur            w2, [x0, #0x3f]
    // 0xaa14f4: DecompressPointer r2
    //     0xaa14f4: add             x2, x2, HEAP, lsl #32
    // 0xaa14f8: stur            x2, [fp, #-0x20]
    // 0xaa14fc: LoadField: r3 = r0->field_43
    //     0xaa14fc: ldur            w3, [x0, #0x43]
    // 0xaa1500: DecompressPointer r3
    //     0xaa1500: add             x3, x3, HEAP, lsl #32
    // 0xaa1504: stur            x3, [fp, #-8]
    // 0xaa1508: r0 = _ExclusiveMouseRegion()
    //     0xaa1508: bl              #0xa0e3dc  ; Allocate_ExclusiveMouseRegionStub -> _ExclusiveMouseRegion (size=0x28)
    // 0xaa150c: mov             x2, x0
    // 0xaa1510: ldur            x0, [fp, #-0x20]
    // 0xaa1514: stur            x2, [fp, #-0x28]
    // 0xaa1518: StoreField: r2->field_f = r0
    //     0xaa1518: stur            w0, [x2, #0xf]
    // 0xaa151c: ldur            x0, [fp, #-8]
    // 0xaa1520: ArrayStore: r2[0] = r0  ; List_4
    //     0xaa1520: stur            w0, [x2, #0x17]
    // 0xaa1524: r0 = Instance__DeferringMouseCursor
    //     0xaa1524: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xaa1528: StoreField: r2->field_1b = r0
    //     0xaa1528: stur            w0, [x2, #0x1b]
    // 0xaa152c: r0 = true
    //     0xaa152c: add             x0, NULL, #0x20  ; true
    // 0xaa1530: StoreField: r2->field_1f = r0
    //     0xaa1530: stur            w0, [x2, #0x1f]
    // 0xaa1534: ldur            x1, [fp, #-0x30]
    // 0xaa1538: StoreField: r2->field_b = r1
    //     0xaa1538: stur            w1, [x2, #0xb]
    // 0xaa153c: ldur            x1, [fp, #-0x18]
    // 0xaa1540: r0 = maybeViewInsetsOf()
    //     0xaa1540: bl              #0xaa1698  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::maybeViewInsetsOf
    // 0xaa1544: cmp             w0, NULL
    // 0xaa1548: b.ne            #0xaa1554
    // 0xaa154c: r0 = Null
    //     0xaa154c: mov             x0, NULL
    // 0xaa1550: b               #0xaa1580
    // 0xaa1554: LoadField: d0 = r0->field_1f
    //     0xaa1554: ldur            d0, [x0, #0x1f]
    // 0xaa1558: r0 = inline_Allocate_Double()
    //     0xaa1558: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaa155c: add             x0, x0, #0x10
    //     0xaa1560: cmp             x1, x0
    //     0xaa1564: b.ls            #0xaa1660
    //     0xaa1568: str             x0, [THR, #0x50]  ; THR::top
    //     0xaa156c: sub             x0, x0, #0xf
    //     0xaa1570: movz            x1, #0xe15c
    //     0xaa1574: movk            x1, #0x3, lsl #16
    //     0xaa1578: stur            x1, [x0, #-1]
    // 0xaa157c: StoreField: r0->field_7 = d0
    //     0xaa157c: stur            d0, [x0, #7]
    // 0xaa1580: cmp             w0, NULL
    // 0xaa1584: b.ne            #0xaa1590
    // 0xaa1588: d0 = 0.000000
    //     0xaa1588: eor             v0.16b, v0.16b, v0.16b
    // 0xaa158c: b               #0xaa1594
    // 0xaa1590: LoadField: d0 = r0->field_7
    //     0xaa1590: ldur            d0, [x0, #7]
    // 0xaa1594: ldur            x1, [fp, #-0x10]
    // 0xaa1598: ldur            x0, [fp, #-0x28]
    // 0xaa159c: stur            d0, [fp, #-0x60]
    // 0xaa15a0: LoadField: r2 = r1->field_2f
    //     0xaa15a0: ldur            w2, [x1, #0x2f]
    // 0xaa15a4: DecompressPointer r2
    //     0xaa15a4: add             x2, x2, HEAP, lsl #32
    // 0xaa15a8: stur            x2, [fp, #-8]
    // 0xaa15ac: LoadField: d1 = r1->field_33
    //     0xaa15ac: ldur            d1, [x1, #0x33]
    // 0xaa15b0: stur            d1, [fp, #-0x58]
    // 0xaa15b4: r0 = _TooltipPositionDelegate()
    //     0xaa15b4: bl              #0xaa168c  ; Allocate_TooltipPositionDelegateStub -> _TooltipPositionDelegate (size=0x1c)
    // 0xaa15b8: mov             x1, x0
    // 0xaa15bc: ldur            x0, [fp, #-8]
    // 0xaa15c0: stur            x1, [fp, #-0x10]
    // 0xaa15c4: StoreField: r1->field_b = r0
    //     0xaa15c4: stur            w0, [x1, #0xb]
    // 0xaa15c8: ldur            d0, [fp, #-0x58]
    // 0xaa15cc: StoreField: r1->field_f = d0
    //     0xaa15cc: stur            d0, [x1, #0xf]
    // 0xaa15d0: r0 = true
    //     0xaa15d0: add             x0, NULL, #0x20  ; true
    // 0xaa15d4: ArrayStore: r1[0] = r0  ; List_4
    //     0xaa15d4: stur            w0, [x1, #0x17]
    // 0xaa15d8: r0 = CustomSingleChildLayout()
    //     0xaa15d8: bl              #0x9e6a80  ; AllocateCustomSingleChildLayoutStub -> CustomSingleChildLayout (size=0x14)
    // 0xaa15dc: mov             x2, x0
    // 0xaa15e0: ldur            x0, [fp, #-0x10]
    // 0xaa15e4: stur            x2, [fp, #-8]
    // 0xaa15e8: StoreField: r2->field_f = r0
    //     0xaa15e8: stur            w0, [x2, #0xf]
    // 0xaa15ec: ldur            x0, [fp, #-0x28]
    // 0xaa15f0: StoreField: r2->field_b = r0
    //     0xaa15f0: stur            w0, [x2, #0xb]
    // 0xaa15f4: r1 = <StackParentData>
    //     0xaa15f4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25780] TypeArguments: <StackParentData>
    //     0xaa15f8: ldr             x1, [x1, #0x780]
    // 0xaa15fc: r0 = Positioned()
    //     0xaa15fc: bl              #0x9f19f8  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xaa1600: r1 = 0.000000
    //     0xaa1600: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xaa1604: StoreField: r0->field_13 = r1
    //     0xaa1604: stur            w1, [x0, #0x13]
    // 0xaa1608: ArrayStore: r0[0] = r1  ; List_4
    //     0xaa1608: stur            w1, [x0, #0x17]
    // 0xaa160c: StoreField: r0->field_1b = r1
    //     0xaa160c: stur            w1, [x0, #0x1b]
    // 0xaa1610: ldur            d0, [fp, #-0x60]
    // 0xaa1614: r1 = inline_Allocate_Double()
    //     0xaa1614: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xaa1618: add             x1, x1, #0x10
    //     0xaa161c: cmp             x2, x1
    //     0xaa1620: b.ls            #0xaa1670
    //     0xaa1624: str             x1, [THR, #0x50]  ; THR::top
    //     0xaa1628: sub             x1, x1, #0xf
    //     0xaa162c: movz            x2, #0xe15c
    //     0xaa1630: movk            x2, #0x3, lsl #16
    //     0xaa1634: stur            x2, [x1, #-1]
    // 0xaa1638: StoreField: r1->field_7 = d0
    //     0xaa1638: stur            d0, [x1, #7]
    // 0xaa163c: StoreField: r0->field_1f = r1
    //     0xaa163c: stur            w1, [x0, #0x1f]
    // 0xaa1640: ldur            x1, [fp, #-8]
    // 0xaa1644: StoreField: r0->field_b = r1
    //     0xaa1644: stur            w1, [x0, #0xb]
    // 0xaa1648: LeaveFrame
    //     0xaa1648: mov             SP, fp
    //     0xaa164c: ldp             fp, lr, [SP], #0x10
    // 0xaa1650: ret
    //     0xaa1650: ret             
    // 0xaa1654: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa1654: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa1658: b               #0xaa1340
    // 0xaa165c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa165c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa1660: SaveReg d0
    //     0xaa1660: str             q0, [SP, #-0x10]!
    // 0xaa1664: r0 = AllocateDouble()
    //     0xaa1664: bl              #0xec2254  ; AllocateDoubleStub
    // 0xaa1668: RestoreReg d0
    //     0xaa1668: ldr             q0, [SP], #0x10
    // 0xaa166c: b               #0xaa157c
    // 0xaa1670: SaveReg d0
    //     0xaa1670: str             q0, [SP, #-0x10]!
    // 0xaa1674: SaveReg r0
    //     0xaa1674: str             x0, [SP, #-8]!
    // 0xaa1678: r0 = AllocateDouble()
    //     0xaa1678: bl              #0xec2254  ; AllocateDoubleStub
    // 0xaa167c: mov             x1, x0
    // 0xaa1680: RestoreReg r0
    //     0xaa1680: ldr             x0, [SP], #8
    // 0xaa1684: RestoreReg d0
    //     0xaa1684: ldr             q0, [SP], #0x10
    // 0xaa1688: b               #0xaa1638
  }
}
