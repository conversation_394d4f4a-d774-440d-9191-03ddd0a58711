// lib: , url: package:flutter/src/painting/text_painter.dart

// class id: 1049008, size: 0x8
class :: {
}

// class id: 3218, size: 0x48, field offset: 0x8
class TextPainter extends Object {

  late _LineCaretMetrics _caretMetrics; // offset: 0x44

  _ getBoxesForSelection(/* No info */) {
    // ** addr: 0x67b8c8, size: 0x1f4
    // 0x67b8c8: EnterFrame
    //     0x67b8c8: stp             fp, lr, [SP, #-0x10]!
    //     0x67b8cc: mov             fp, SP
    // 0x67b8d0: AllocStack(0x60)
    //     0x67b8d0: sub             SP, SP, #0x60
    // 0x67b8d4: SetupParameters(dynamic _ /* r2 => r2, fp-0x18 */, {dynamic boxHeightStyle = Instance_BoxHeightStyle /* r0, fp-0x10 */})
    //     0x67b8d4: stur            x2, [fp, #-0x18]
    //     0x67b8d8: ldur            w0, [x4, #0x13]
    //     0x67b8dc: ldur            w3, [x4, #0x1f]
    //     0x67b8e0: add             x3, x3, HEAP, lsl #32
    //     0x67b8e4: ldr             x16, [PP, #0x49f8]  ; [pp+0x49f8] "boxHeightStyle"
    //     0x67b8e8: cmp             w3, w16
    //     0x67b8ec: b.ne            #0x67b908
    //     0x67b8f0: ldur            w3, [x4, #0x23]
    //     0x67b8f4: add             x3, x3, HEAP, lsl #32
    //     0x67b8f8: sub             w4, w0, w3
    //     0x67b8fc: add             x0, fp, w4, sxtw #2
    //     0x67b900: ldr             x0, [x0, #8]
    //     0x67b904: b               #0x67b90c
    //     0x67b908: ldr             x0, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    //     0x67b90c: stur            x0, [fp, #-0x10]
    // 0x67b910: CheckStackOverflow
    //     0x67b910: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67b914: cmp             SP, x16
    //     0x67b918: b.ls            #0x67baac
    // 0x67b91c: LoadField: r3 = r1->field_7
    //     0x67b91c: ldur            w3, [x1, #7]
    // 0x67b920: DecompressPointer r3
    //     0x67b920: add             x3, x3, HEAP, lsl #32
    // 0x67b924: stur            x3, [fp, #-8]
    // 0x67b928: cmp             w3, NULL
    // 0x67b92c: b.eq            #0x67bab4
    // 0x67b930: mov             x1, x3
    // 0x67b934: r0 = paintOffset()
    //     0x67b934: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0x67b938: stur            x0, [fp, #-0x20]
    // 0x67b93c: r1 = 1
    //     0x67b93c: movz            x1, #0x1
    // 0x67b940: r0 = AllocateContext()
    //     0x67b940: bl              #0xec126c  ; AllocateContextStub
    // 0x67b944: mov             x1, x0
    // 0x67b948: ldur            x0, [fp, #-0x20]
    // 0x67b94c: stur            x1, [fp, #-0x48]
    // 0x67b950: StoreField: r1->field_f = r0
    //     0x67b950: stur            w0, [x1, #0xf]
    // 0x67b954: LoadField: d0 = r0->field_7
    //     0x67b954: ldur            d0, [x0, #7]
    // 0x67b958: mov             x2, v0.d[0]
    // 0x67b95c: and             x2, x2, #0x7fffffffffffffff
    // 0x67b960: r17 = 9218868437227405312
    //     0x67b960: orr             x17, xzr, #0x7ff0000000000000
    // 0x67b964: cmp             x2, x17
    // 0x67b968: b.eq            #0x67ba94
    // 0x67b96c: fcmp            d0, d0
    // 0x67b970: b.vs            #0x67ba94
    // 0x67b974: LoadField: d0 = r0->field_f
    //     0x67b974: ldur            d0, [x0, #0xf]
    // 0x67b978: mov             x2, v0.d[0]
    // 0x67b97c: and             x2, x2, #0x7fffffffffffffff
    // 0x67b980: r17 = 9218868437227405312
    //     0x67b980: orr             x17, xzr, #0x7ff0000000000000
    // 0x67b984: cmp             x2, x17
    // 0x67b988: b.eq            #0x67ba94
    // 0x67b98c: fcmp            d0, d0
    // 0x67b990: b.vs            #0x67ba94
    // 0x67b994: ldur            x2, [fp, #-0x18]
    // 0x67b998: ldur            x3, [fp, #-0x10]
    // 0x67b99c: ldur            x4, [fp, #-8]
    // 0x67b9a0: LoadField: r5 = r4->field_7
    //     0x67b9a0: ldur            w5, [x4, #7]
    // 0x67b9a4: DecompressPointer r5
    //     0x67b9a4: add             x5, x5, HEAP, lsl #32
    // 0x67b9a8: LoadField: r4 = r5->field_f
    //     0x67b9a8: ldur            w4, [x5, #0xf]
    // 0x67b9ac: DecompressPointer r4
    //     0x67b9ac: add             x4, x4, HEAP, lsl #32
    // 0x67b9b0: stur            x4, [fp, #-8]
    // 0x67b9b4: LoadField: r5 = r2->field_7
    //     0x67b9b4: ldur            x5, [x2, #7]
    // 0x67b9b8: stur            x5, [fp, #-0x40]
    // 0x67b9bc: LoadField: r6 = r2->field_f
    //     0x67b9bc: ldur            x6, [x2, #0xf]
    // 0x67b9c0: stur            x6, [fp, #-0x38]
    // 0x67b9c4: LoadField: r2 = r3->field_7
    //     0x67b9c4: ldur            x2, [x3, #7]
    // 0x67b9c8: stur            x2, [fp, #-0x30]
    // 0x67b9cc: LoadField: r3 = r4->field_7
    //     0x67b9cc: ldur            w3, [x4, #7]
    // 0x67b9d0: DecompressPointer r3
    //     0x67b9d0: add             x3, x3, HEAP, lsl #32
    // 0x67b9d4: cmp             w3, NULL
    // 0x67b9d8: b.eq            #0x67bab8
    // 0x67b9dc: LoadField: r7 = r3->field_7
    //     0x67b9dc: ldur            x7, [x3, #7]
    // 0x67b9e0: ldr             x3, [x7]
    // 0x67b9e4: stur            x3, [fp, #-0x28]
    // 0x67b9e8: cbnz            x3, #0x67b9f8
    // 0x67b9ec: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x67b9ec: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x67b9f0: str             x16, [SP]
    // 0x67b9f4: r0 = _throwNew()
    //     0x67b9f4: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x67b9f8: ldur            x0, [fp, #-0x28]
    // 0x67b9fc: stur            x0, [fp, #-0x28]
    // 0x67ba00: r1 = <Never>
    //     0x67ba00: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x67ba04: r0 = Pointer()
    //     0x67ba04: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x67ba08: mov             x1, x0
    // 0x67ba0c: ldur            x0, [fp, #-0x28]
    // 0x67ba10: StoreField: r1->field_7 = r0
    //     0x67ba10: stur            x0, [x1, #7]
    // 0x67ba14: ldur            x2, [fp, #-0x40]
    // 0x67ba18: ldur            x3, [fp, #-0x38]
    // 0x67ba1c: ldur            x5, [fp, #-0x30]
    // 0x67ba20: r6 = 0
    //     0x67ba20: movz            x6, #0
    // 0x67ba24: r0 = __getBoxesForRange$Method$FfiNative()
    //     0x67ba24: bl              #0x67bdcc  ; [dart:ui] _NativeParagraph::__getBoxesForRange$Method$FfiNative
    // 0x67ba28: ldur            x1, [fp, #-8]
    // 0x67ba2c: mov             x2, x0
    // 0x67ba30: r0 = _decodeTextBoxes()
    //     0x67ba30: bl              #0x67badc  ; [dart:ui] _NativeParagraph::_decodeTextBoxes
    // 0x67ba34: stur            x0, [fp, #-8]
    // 0x67ba38: ldur            x16, [fp, #-0x20]
    // 0x67ba3c: r30 = Instance_Offset
    //     0x67ba3c: ldr             lr, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x67ba40: stp             lr, x16, [SP]
    // 0x67ba44: r0 = ==()
    //     0x67ba44: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0x67ba48: tbnz            w0, #4, #0x67ba54
    // 0x67ba4c: ldur            x0, [fp, #-8]
    // 0x67ba50: b               #0x67ba88
    // 0x67ba54: ldur            x2, [fp, #-0x48]
    // 0x67ba58: r1 = Function '<anonymous closure>':.
    //     0x67ba58: ldr             x1, [PP, #0x4a08]  ; [pp+0x4a08] AnonymousClosure: (0x67c26c), in [package:flutter/src/painting/text_painter.dart] TextPainter::getBoxesForSelection (0x67b8c8)
    // 0x67ba5c: r0 = AllocateClosure()
    //     0x67ba5c: bl              #0xec1630  ; AllocateClosureStub
    // 0x67ba60: r16 = <TextBox>
    //     0x67ba60: ldr             x16, [PP, #0x4858]  ; [pp+0x4858] TypeArguments: <TextBox>
    // 0x67ba64: ldur            lr, [fp, #-8]
    // 0x67ba68: stp             lr, x16, [SP, #8]
    // 0x67ba6c: str             x0, [SP]
    // 0x67ba70: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x67ba70: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x67ba74: r0 = map()
    //     0x67ba74: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x67ba78: LoadField: r1 = r0->field_7
    //     0x67ba78: ldur            w1, [x0, #7]
    // 0x67ba7c: DecompressPointer r1
    //     0x67ba7c: add             x1, x1, HEAP, lsl #32
    // 0x67ba80: mov             x2, x0
    // 0x67ba84: r0 = _List.of()
    //     0x67ba84: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0x67ba88: LeaveFrame
    //     0x67ba88: mov             SP, fp
    //     0x67ba8c: ldp             fp, lr, [SP], #0x10
    // 0x67ba90: ret
    //     0x67ba90: ret             
    // 0x67ba94: r1 = <TextBox>
    //     0x67ba94: ldr             x1, [PP, #0x4858]  ; [pp+0x4858] TypeArguments: <TextBox>
    // 0x67ba98: r2 = 0
    //     0x67ba98: movz            x2, #0
    // 0x67ba9c: r0 = _GrowableList()
    //     0x67ba9c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x67baa0: LeaveFrame
    //     0x67baa0: mov             SP, fp
    //     0x67baa4: ldp             fp, lr, [SP], #0x10
    // 0x67baa8: ret
    //     0x67baa8: ret             
    // 0x67baac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67baac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67bab0: b               #0x67b91c
    // 0x67bab4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67bab4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67bab8: r0 = NullErrorSharedWithoutFPURegs()
    //     0x67bab8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] TextBox <anonymous closure>(dynamic, TextBox) {
    // ** addr: 0x67c26c, size: 0x44
    // 0x67c26c: EnterFrame
    //     0x67c26c: stp             fp, lr, [SP, #-0x10]!
    //     0x67c270: mov             fp, SP
    // 0x67c274: ldr             x0, [fp, #0x18]
    // 0x67c278: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x67c278: ldur            w1, [x0, #0x17]
    // 0x67c27c: DecompressPointer r1
    //     0x67c27c: add             x1, x1, HEAP, lsl #32
    // 0x67c280: CheckStackOverflow
    //     0x67c280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67c284: cmp             SP, x16
    //     0x67c288: b.ls            #0x67c2a8
    // 0x67c28c: LoadField: r2 = r1->field_f
    //     0x67c28c: ldur            w2, [x1, #0xf]
    // 0x67c290: DecompressPointer r2
    //     0x67c290: add             x2, x2, HEAP, lsl #32
    // 0x67c294: ldr             x1, [fp, #0x10]
    // 0x67c298: r0 = _shiftTextBox()
    //     0x67c298: bl              #0x67c2b0  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_shiftTextBox
    // 0x67c29c: LeaveFrame
    //     0x67c29c: mov             SP, fp
    //     0x67c2a0: ldp             fp, lr, [SP], #0x10
    // 0x67c2a4: ret
    //     0x67c2a4: ret             
    // 0x67c2a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67c2a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67c2ac: b               #0x67c28c
  }
  static _ _shiftTextBox(/* No info */) {
    // ** addr: 0x67c2b0, size: 0x88
    // 0x67c2b0: EnterFrame
    //     0x67c2b0: stp             fp, lr, [SP, #-0x10]!
    //     0x67c2b4: mov             fp, SP
    // 0x67c2b8: AllocStack(0x28)
    //     0x67c2b8: sub             SP, SP, #0x28
    // 0x67c2bc: LoadField: d0 = r1->field_7
    //     0x67c2bc: ldur            d0, [x1, #7]
    // 0x67c2c0: LoadField: d1 = r2->field_7
    //     0x67c2c0: ldur            d1, [x2, #7]
    // 0x67c2c4: fadd            d2, d0, d1
    // 0x67c2c8: stur            d2, [fp, #-0x28]
    // 0x67c2cc: LoadField: d0 = r1->field_f
    //     0x67c2cc: ldur            d0, [x1, #0xf]
    // 0x67c2d0: LoadField: d3 = r2->field_f
    //     0x67c2d0: ldur            d3, [x2, #0xf]
    // 0x67c2d4: fadd            d4, d0, d3
    // 0x67c2d8: stur            d4, [fp, #-0x20]
    // 0x67c2dc: ArrayLoad: d0 = r1[0]  ; List_8
    //     0x67c2dc: ldur            d0, [x1, #0x17]
    // 0x67c2e0: fadd            d5, d0, d1
    // 0x67c2e4: stur            d5, [fp, #-0x18]
    // 0x67c2e8: LoadField: d0 = r1->field_1f
    //     0x67c2e8: ldur            d0, [x1, #0x1f]
    // 0x67c2ec: fadd            d1, d0, d3
    // 0x67c2f0: stur            d1, [fp, #-0x10]
    // 0x67c2f4: LoadField: r0 = r1->field_27
    //     0x67c2f4: ldur            w0, [x1, #0x27]
    // 0x67c2f8: DecompressPointer r0
    //     0x67c2f8: add             x0, x0, HEAP, lsl #32
    // 0x67c2fc: stur            x0, [fp, #-8]
    // 0x67c300: r0 = TextBox()
    //     0x67c300: bl              #0x67bd9c  ; AllocateTextBoxStub -> TextBox (size=0x2c)
    // 0x67c304: ldur            d0, [fp, #-0x28]
    // 0x67c308: StoreField: r0->field_7 = d0
    //     0x67c308: stur            d0, [x0, #7]
    // 0x67c30c: ldur            d0, [fp, #-0x20]
    // 0x67c310: StoreField: r0->field_f = d0
    //     0x67c310: stur            d0, [x0, #0xf]
    // 0x67c314: ldur            d0, [fp, #-0x18]
    // 0x67c318: ArrayStore: r0[0] = d0  ; List_8
    //     0x67c318: stur            d0, [x0, #0x17]
    // 0x67c31c: ldur            d0, [fp, #-0x10]
    // 0x67c320: StoreField: r0->field_1f = d0
    //     0x67c320: stur            d0, [x0, #0x1f]
    // 0x67c324: ldur            x1, [fp, #-8]
    // 0x67c328: StoreField: r0->field_27 = r1
    //     0x67c328: stur            w1, [x0, #0x27]
    // 0x67c32c: LeaveFrame
    //     0x67c32c: mov             SP, fp
    //     0x67c330: ldp             fp, lr, [SP], #0x10
    // 0x67c334: ret
    //     0x67c334: ret             
  }
  _ layout(/* No info */) {
    // ** addr: 0x67c4a4, size: 0x550
    // 0x67c4a4: EnterFrame
    //     0x67c4a4: stp             fp, lr, [SP, #-0x10]!
    //     0x67c4a8: mov             fp, SP
    // 0x67c4ac: AllocStack(0x58)
    //     0x67c4ac: sub             SP, SP, #0x58
    // 0x67c4b0: SetupParameters(TextPainter this /* r1 => r0, fp-0x10 */, {_Double maxWidth = inf /* d2, fp-0x40 */, _Double minWidth = 0.000000 /* d3, fp-0x38 */})
    //     0x67c4b0: mov             x0, x1
    //     0x67c4b4: stur            x1, [fp, #-0x10]
    //     0x67c4b8: ldur            w1, [x4, #0x13]
    //     0x67c4bc: ldur            w2, [x4, #0x1f]
    //     0x67c4c0: add             x2, x2, HEAP, lsl #32
    //     0x67c4c4: ldr             x16, [PP, #0x4978]  ; [pp+0x4978] "maxWidth"
    //     0x67c4c8: cmp             w2, w16
    //     0x67c4cc: b.ne            #0x67c4f4
    //     0x67c4d0: ldur            w2, [x4, #0x23]
    //     0x67c4d4: add             x2, x2, HEAP, lsl #32
    //     0x67c4d8: sub             w3, w1, w2
    //     0x67c4dc: add             x2, fp, w3, sxtw #2
    //     0x67c4e0: ldr             x2, [x2, #8]
    //     0x67c4e4: ldur            d0, [x2, #7]
    //     0x67c4e8: mov             v2.16b, v0.16b
    //     0x67c4ec: movz            x2, #0x1
    //     0x67c4f0: b               #0x67c4fc
    //     0x67c4f4: ldr             d2, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    //     0x67c4f8: movz            x2, #0
    //     0x67c4fc: stur            d2, [fp, #-0x40]
    //     0x67c500: lsl             x3, x2, #1
    //     0x67c504: lsl             w2, w3, #1
    //     0x67c508: add             w3, w2, #8
    //     0x67c50c: add             x16, x4, w3, sxtw #1
    //     0x67c510: ldur            w5, [x16, #0xf]
    //     0x67c514: add             x5, x5, HEAP, lsl #32
    //     0x67c518: ldr             x16, [PP, #0x4980]  ; [pp+0x4980] "minWidth"
    //     0x67c51c: cmp             w5, w16
    //     0x67c520: b.ne            #0x67c54c
    //     0x67c524: add             w3, w2, #0xa
    //     0x67c528: add             x16, x4, w3, sxtw #1
    //     0x67c52c: ldur            w2, [x16, #0xf]
    //     0x67c530: add             x2, x2, HEAP, lsl #32
    //     0x67c534: sub             w3, w1, w2
    //     0x67c538: add             x1, fp, w3, sxtw #2
    //     0x67c53c: ldr             x1, [x1, #8]
    //     0x67c540: ldur            d0, [x1, #7]
    //     0x67c544: mov             v3.16b, v0.16b
    //     0x67c548: b               #0x67c550
    //     0x67c54c: eor             v3.16b, v3.16b, v3.16b
    //     0x67c550: stur            d3, [fp, #-0x38]
    // 0x67c554: CheckStackOverflow
    //     0x67c554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67c558: cmp             SP, x16
    //     0x67c55c: b.ls            #0x67c9bc
    // 0x67c560: LoadField: r2 = r0->field_7
    //     0x67c560: ldur            w2, [x0, #7]
    // 0x67c564: DecompressPointer r2
    //     0x67c564: add             x2, x2, HEAP, lsl #32
    // 0x67c568: stur            x2, [fp, #-8]
    // 0x67c56c: cmp             w2, NULL
    // 0x67c570: b.eq            #0x67c598
    // 0x67c574: mov             x1, x2
    // 0x67c578: mov             v0.16b, v3.16b
    // 0x67c57c: mov             v1.16b, v2.16b
    // 0x67c580: r0 = _resizeToFit()
    //     0x67c580: bl              #0x67e620  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::_resizeToFit
    // 0x67c584: tbnz            w0, #4, #0x67c598
    // 0x67c588: r0 = Null
    //     0x67c588: mov             x0, NULL
    // 0x67c58c: LeaveFrame
    //     0x67c58c: mov             SP, fp
    //     0x67c590: ldp             fp, lr, [SP], #0x10
    // 0x67c594: ret
    //     0x67c594: ret             
    // 0x67c598: ldur            x0, [fp, #-0x10]
    // 0x67c59c: LoadField: r3 = r0->field_f
    //     0x67c59c: ldur            w3, [x0, #0xf]
    // 0x67c5a0: DecompressPointer r3
    //     0x67c5a0: add             x3, x3, HEAP, lsl #32
    // 0x67c5a4: stur            x3, [fp, #-0x20]
    // 0x67c5a8: cmp             w3, NULL
    // 0x67c5ac: b.eq            #0x67c984
    // 0x67c5b0: LoadField: r4 = r0->field_1b
    //     0x67c5b0: ldur            w4, [x0, #0x1b]
    // 0x67c5b4: DecompressPointer r4
    //     0x67c5b4: add             x4, x4, HEAP, lsl #32
    // 0x67c5b8: stur            x4, [fp, #-0x18]
    // 0x67c5bc: cmp             w4, NULL
    // 0x67c5c0: b.eq            #0x67c9a0
    // 0x67c5c4: ldur            d0, [fp, #-0x40]
    // 0x67c5c8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x67c5c8: ldur            w1, [x0, #0x17]
    // 0x67c5cc: DecompressPointer r1
    //     0x67c5cc: add             x1, x1, HEAP, lsl #32
    // 0x67c5d0: mov             x2, x4
    // 0x67c5d4: r0 = _computePaintOffsetFraction()
    //     0x67c5d4: bl              #0x67e3ec  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_computePaintOffsetFraction
    // 0x67c5d8: mov             v1.16b, v0.16b
    // 0x67c5dc: ldur            d0, [fp, #-0x40]
    // 0x67c5e0: stur            d1, [fp, #-0x48]
    // 0x67c5e4: mov             x0, v0.d[0]
    // 0x67c5e8: and             x0, x0, #0x7fffffffffffffff
    // 0x67c5ec: r17 = 9218868437227405312
    //     0x67c5ec: orr             x17, xzr, #0x7ff0000000000000
    // 0x67c5f0: cmp             x0, x17
    // 0x67c5f4: b.eq            #0x67c600
    // 0x67c5f8: fcmp            d0, d0
    // 0x67c5fc: b.vc            #0x67c60c
    // 0x67c600: d2 = 0.000000
    //     0x67c600: eor             v2.16b, v2.16b, v2.16b
    // 0x67c604: fcmp            d1, d2
    // 0x67c608: b.ne            #0x67c638
    // 0x67c60c: r0 = inline_Allocate_Double()
    //     0x67c60c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x67c610: add             x0, x0, #0x10
    //     0x67c614: cmp             x1, x0
    //     0x67c618: b.ls            #0x67c9c4
    //     0x67c61c: str             x0, [THR, #0x50]  ; THR::top
    //     0x67c620: sub             x0, x0, #0xf
    //     0x67c624: movz            x1, #0xe15c
    //     0x67c628: movk            x1, #0x3, lsl #16
    //     0x67c62c: stur            x1, [x0, #-1]
    // 0x67c630: StoreField: r0->field_7 = d0
    //     0x67c630: stur            d0, [x0, #7]
    // 0x67c634: b               #0x67c6d4
    // 0x67c638: ldur            x0, [fp, #-8]
    // 0x67c63c: cmp             w0, NULL
    // 0x67c640: b.ne            #0x67c64c
    // 0x67c644: r0 = Null
    //     0x67c644: mov             x0, NULL
    // 0x67c648: b               #0x67c6d4
    // 0x67c64c: LoadField: r1 = r0->field_7
    //     0x67c64c: ldur            w1, [x0, #7]
    // 0x67c650: DecompressPointer r1
    //     0x67c650: add             x1, x1, HEAP, lsl #32
    // 0x67c654: LoadField: r2 = r1->field_f
    //     0x67c654: ldur            w2, [x1, #0xf]
    // 0x67c658: DecompressPointer r2
    //     0x67c658: add             x2, x2, HEAP, lsl #32
    // 0x67c65c: stur            x2, [fp, #-0x30]
    // 0x67c660: LoadField: r1 = r2->field_7
    //     0x67c660: ldur            w1, [x2, #7]
    // 0x67c664: DecompressPointer r1
    //     0x67c664: add             x1, x1, HEAP, lsl #32
    // 0x67c668: cmp             w1, NULL
    // 0x67c66c: b.eq            #0x67c9d4
    // 0x67c670: LoadField: r3 = r1->field_7
    //     0x67c670: ldur            x3, [x1, #7]
    // 0x67c674: ldr             x1, [x3]
    // 0x67c678: stur            x1, [fp, #-0x28]
    // 0x67c67c: cbnz            x1, #0x67c68c
    // 0x67c680: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x67c680: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x67c684: str             x16, [SP]
    // 0x67c688: r0 = _throwNew()
    //     0x67c688: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x67c68c: ldur            x0, [fp, #-0x28]
    // 0x67c690: stur            x0, [fp, #-0x28]
    // 0x67c694: r1 = <Never>
    //     0x67c694: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x67c698: r0 = Pointer()
    //     0x67c698: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x67c69c: mov             x1, x0
    // 0x67c6a0: ldur            x0, [fp, #-0x28]
    // 0x67c6a4: StoreField: r1->field_7 = r0
    //     0x67c6a4: stur            x0, [x1, #7]
    // 0x67c6a8: r0 = _maxIntrinsicWidth$Getter$FfiNative()
    //     0x67c6a8: bl              #0x67e368  ; [dart:ui] _NativeParagraph::_maxIntrinsicWidth$Getter$FfiNative
    // 0x67c6ac: r0 = inline_Allocate_Double()
    //     0x67c6ac: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x67c6b0: add             x0, x0, #0x10
    //     0x67c6b4: cmp             x1, x0
    //     0x67c6b8: b.ls            #0x67c9d8
    //     0x67c6bc: str             x0, [THR, #0x50]  ; THR::top
    //     0x67c6c0: sub             x0, x0, #0xf
    //     0x67c6c4: movz            x1, #0xe15c
    //     0x67c6c8: movk            x1, #0x3, lsl #16
    //     0x67c6cc: stur            x1, [x0, #-1]
    // 0x67c6d0: StoreField: r0->field_7 = d0
    //     0x67c6d0: stur            d0, [x0, #7]
    // 0x67c6d4: stur            x0, [fp, #-0x30]
    // 0x67c6d8: cmp             w0, NULL
    // 0x67c6dc: b.ne            #0x67c6e8
    // 0x67c6e0: ldur            d0, [fp, #-0x40]
    // 0x67c6e4: b               #0x67c6ec
    // 0x67c6e8: LoadField: d0 = r0->field_7
    //     0x67c6e8: ldur            d0, [x0, #7]
    // 0x67c6ec: ldur            x1, [fp, #-8]
    // 0x67c6f0: stur            d0, [fp, #-0x50]
    // 0x67c6f4: cmp             w1, NULL
    // 0x67c6f8: b.ne            #0x67c704
    // 0x67c6fc: r1 = Null
    //     0x67c6fc: mov             x1, NULL
    // 0x67c700: b               #0x67c714
    // 0x67c704: LoadField: r2 = r1->field_7
    //     0x67c704: ldur            w2, [x1, #7]
    // 0x67c708: DecompressPointer r2
    //     0x67c708: add             x2, x2, HEAP, lsl #32
    // 0x67c70c: LoadField: r1 = r2->field_f
    //     0x67c70c: ldur            w1, [x2, #0xf]
    // 0x67c710: DecompressPointer r1
    //     0x67c710: add             x1, x1, HEAP, lsl #32
    // 0x67c714: cmp             w1, NULL
    // 0x67c718: b.ne            #0x67c72c
    // 0x67c71c: ldur            x1, [fp, #-0x10]
    // 0x67c720: ldur            x2, [fp, #-0x20]
    // 0x67c724: r0 = _createParagraph()
    //     0x67c724: bl              #0x67cb64  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_createParagraph
    // 0x67c728: b               #0x67c730
    // 0x67c72c: mov             x0, x1
    // 0x67c730: stur            x0, [fp, #-8]
    // 0x67c734: LoadField: r1 = r0->field_7
    //     0x67c734: ldur            w1, [x0, #7]
    // 0x67c738: DecompressPointer r1
    //     0x67c738: add             x1, x1, HEAP, lsl #32
    // 0x67c73c: cmp             w1, NULL
    // 0x67c740: b.eq            #0x67c9e8
    // 0x67c744: LoadField: r2 = r1->field_7
    //     0x67c744: ldur            x2, [x1, #7]
    // 0x67c748: ldr             x1, [x2]
    // 0x67c74c: stur            x1, [fp, #-0x28]
    // 0x67c750: cbnz            x1, #0x67c760
    // 0x67c754: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x67c754: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x67c758: str             x16, [SP]
    // 0x67c75c: r0 = _throwNew()
    //     0x67c75c: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x67c760: ldur            x3, [fp, #-0x10]
    // 0x67c764: ldur            x2, [fp, #-0x30]
    // 0x67c768: ldur            x0, [fp, #-8]
    // 0x67c76c: ldur            x4, [fp, #-0x18]
    // 0x67c770: ldur            x5, [fp, #-0x28]
    // 0x67c774: stur            x5, [fp, #-0x28]
    // 0x67c778: r1 = <Never>
    //     0x67c778: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x67c77c: r0 = Pointer()
    //     0x67c77c: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x67c780: mov             x1, x0
    // 0x67c784: ldur            x0, [fp, #-0x28]
    // 0x67c788: StoreField: r1->field_7 = r0
    //     0x67c788: stur            x0, [x1, #7]
    // 0x67c78c: ldur            d0, [fp, #-0x50]
    // 0x67c790: r0 = __layout$Method$FfiNative()
    //     0x67c790: bl              #0x67cad4  ; [dart:ui] _NativeParagraph::__layout$Method$FfiNative
    // 0x67c794: r0 = _TextLayout()
    //     0x67c794: bl              #0x67cac8  ; Allocate_TextLayoutStub -> _TextLayout (size=0x18)
    // 0x67c798: mov             x2, x0
    // 0x67c79c: r0 = Sentinel
    //     0x67c79c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x67c7a0: stur            x2, [fp, #-0x20]
    // 0x67c7a4: StoreField: r2->field_13 = r0
    //     0x67c7a4: stur            w0, [x2, #0x13]
    // 0x67c7a8: ldur            x0, [fp, #-8]
    // 0x67c7ac: StoreField: r2->field_f = r0
    //     0x67c7ac: stur            w0, [x2, #0xf]
    // 0x67c7b0: ldur            x1, [fp, #-0x18]
    // 0x67c7b4: StoreField: r2->field_7 = r1
    //     0x67c7b4: stur            w1, [x2, #7]
    // 0x67c7b8: ldur            x3, [fp, #-0x10]
    // 0x67c7bc: StoreField: r2->field_b = r3
    //     0x67c7bc: stur            w3, [x2, #0xb]
    // 0x67c7c0: mov             x1, x2
    // 0x67c7c4: ldur            d0, [fp, #-0x38]
    // 0x67c7c8: ldur            d1, [fp, #-0x40]
    // 0x67c7cc: r0 = _contentWidthFor()
    //     0x67c7cc: bl              #0x67ca00  ; [package:flutter/src/painting/text_painter.dart] _TextLayout::_contentWidthFor
    // 0x67c7d0: ldur            x0, [fp, #-0x30]
    // 0x67c7d4: stur            d0, [fp, #-0x40]
    // 0x67c7d8: cmp             w0, NULL
    // 0x67c7dc: b.ne            #0x67c918
    // 0x67c7e0: ldur            d1, [fp, #-0x38]
    // 0x67c7e4: mov             x0, v1.d[0]
    // 0x67c7e8: and             x0, x0, #0x7fffffffffffffff
    // 0x67c7ec: r17 = 9218868437227405312
    //     0x67c7ec: orr             x17, xzr, #0x7ff0000000000000
    // 0x67c7f0: cmp             x0, x17
    // 0x67c7f4: b.eq            #0x67c908
    // 0x67c7f8: fcmp            d1, d1
    // 0x67c7fc: b.vs            #0x67c8f8
    // 0x67c800: ldur            x0, [fp, #-0x20]
    // 0x67c804: LoadField: r1 = r0->field_f
    //     0x67c804: ldur            w1, [x0, #0xf]
    // 0x67c808: DecompressPointer r1
    //     0x67c808: add             x1, x1, HEAP, lsl #32
    // 0x67c80c: stur            x1, [fp, #-0x18]
    // 0x67c810: LoadField: r2 = r1->field_7
    //     0x67c810: ldur            w2, [x1, #7]
    // 0x67c814: DecompressPointer r2
    //     0x67c814: add             x2, x2, HEAP, lsl #32
    // 0x67c818: cmp             w2, NULL
    // 0x67c81c: b.eq            #0x67c9ec
    // 0x67c820: LoadField: r3 = r2->field_7
    //     0x67c820: ldur            x3, [x2, #7]
    // 0x67c824: ldr             x2, [x3]
    // 0x67c828: stur            x2, [fp, #-0x28]
    // 0x67c82c: cbnz            x2, #0x67c83c
    // 0x67c830: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x67c830: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x67c834: str             x16, [SP]
    // 0x67c838: r0 = _throwNew()
    //     0x67c838: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x67c83c: ldur            x0, [fp, #-8]
    // 0x67c840: ldur            x2, [fp, #-0x28]
    // 0x67c844: stur            x2, [fp, #-0x28]
    // 0x67c848: r1 = <Never>
    //     0x67c848: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x67c84c: r0 = Pointer()
    //     0x67c84c: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x67c850: mov             x1, x0
    // 0x67c854: ldur            x0, [fp, #-0x28]
    // 0x67c858: StoreField: r1->field_7 = r0
    //     0x67c858: stur            x0, [x1, #7]
    // 0x67c85c: r0 = _maxIntrinsicWidth$Getter$FfiNative()
    //     0x67c85c: bl              #0x67e368  ; [dart:ui] _NativeParagraph::_maxIntrinsicWidth$Getter$FfiNative
    // 0x67c860: stur            d0, [fp, #-0x38]
    // 0x67c864: ldur            x0, [fp, #-8]
    // 0x67c868: LoadField: r1 = r0->field_7
    //     0x67c868: ldur            w1, [x0, #7]
    // 0x67c86c: DecompressPointer r1
    //     0x67c86c: add             x1, x1, HEAP, lsl #32
    // 0x67c870: cmp             w1, NULL
    // 0x67c874: b.eq            #0x67c9f0
    // 0x67c878: LoadField: r2 = r1->field_7
    //     0x67c878: ldur            x2, [x1, #7]
    // 0x67c87c: ldr             x1, [x2]
    // 0x67c880: stur            x1, [fp, #-0x28]
    // 0x67c884: cbnz            x1, #0x67c894
    // 0x67c888: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x67c888: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x67c88c: str             x16, [SP]
    // 0x67c890: r0 = _throwNew()
    //     0x67c890: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x67c894: ldur            d2, [fp, #-0x48]
    // 0x67c898: ldur            x0, [fp, #-0x20]
    // 0x67c89c: ldur            d1, [fp, #-0x40]
    // 0x67c8a0: ldur            d0, [fp, #-0x38]
    // 0x67c8a4: ldur            x2, [fp, #-0x28]
    // 0x67c8a8: stur            x2, [fp, #-0x28]
    // 0x67c8ac: r1 = <Never>
    //     0x67c8ac: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x67c8b0: r0 = Pointer()
    //     0x67c8b0: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x67c8b4: mov             x1, x0
    // 0x67c8b8: ldur            x0, [fp, #-0x28]
    // 0x67c8bc: StoreField: r1->field_7 = r0
    //     0x67c8bc: stur            x0, [x1, #7]
    // 0x67c8c0: ldur            d0, [fp, #-0x38]
    // 0x67c8c4: r0 = __layout$Method$FfiNative()
    //     0x67c8c4: bl              #0x67cad4  ; [dart:ui] _NativeParagraph::__layout$Method$FfiNative
    // 0x67c8c8: r0 = _TextPainterLayoutCacheWithOffset()
    //     0x67c8c8: bl              #0x67c9f4  ; Allocate_TextPainterLayoutCacheWithOffsetStub -> _TextPainterLayoutCacheWithOffset (size=0x30)
    // 0x67c8cc: mov             x1, x0
    // 0x67c8d0: ldur            x0, [fp, #-0x20]
    // 0x67c8d4: StoreField: r1->field_7 = r0
    //     0x67c8d4: stur            w0, [x1, #7]
    // 0x67c8d8: ldur            d0, [fp, #-0x48]
    // 0x67c8dc: StoreField: r1->field_1b = d0
    //     0x67c8dc: stur            d0, [x1, #0x1b]
    // 0x67c8e0: ldur            d0, [fp, #-0x38]
    // 0x67c8e4: StoreField: r1->field_b = d0
    //     0x67c8e4: stur            d0, [x1, #0xb]
    // 0x67c8e8: ldur            d1, [fp, #-0x40]
    // 0x67c8ec: StoreField: r1->field_13 = d1
    //     0x67c8ec: stur            d1, [x1, #0x13]
    // 0x67c8f0: mov             x0, x1
    // 0x67c8f4: b               #0x67c954
    // 0x67c8f8: mov             v1.16b, v0.16b
    // 0x67c8fc: ldur            d0, [fp, #-0x48]
    // 0x67c900: ldur            x0, [fp, #-0x20]
    // 0x67c904: b               #0x67c924
    // 0x67c908: mov             v1.16b, v0.16b
    // 0x67c90c: ldur            d0, [fp, #-0x48]
    // 0x67c910: ldur            x0, [fp, #-0x20]
    // 0x67c914: b               #0x67c924
    // 0x67c918: mov             v1.16b, v0.16b
    // 0x67c91c: ldur            d0, [fp, #-0x48]
    // 0x67c920: ldur            x0, [fp, #-0x20]
    // 0x67c924: ldur            d2, [fp, #-0x50]
    // 0x67c928: r0 = _TextPainterLayoutCacheWithOffset()
    //     0x67c928: bl              #0x67c9f4  ; Allocate_TextPainterLayoutCacheWithOffsetStub -> _TextPainterLayoutCacheWithOffset (size=0x30)
    // 0x67c92c: mov             x1, x0
    // 0x67c930: ldur            x0, [fp, #-0x20]
    // 0x67c934: StoreField: r1->field_7 = r0
    //     0x67c934: stur            w0, [x1, #7]
    // 0x67c938: ldur            d0, [fp, #-0x48]
    // 0x67c93c: StoreField: r1->field_1b = d0
    //     0x67c93c: stur            d0, [x1, #0x1b]
    // 0x67c940: ldur            d0, [fp, #-0x50]
    // 0x67c944: StoreField: r1->field_b = d0
    //     0x67c944: stur            d0, [x1, #0xb]
    // 0x67c948: ldur            d0, [fp, #-0x40]
    // 0x67c94c: StoreField: r1->field_13 = d0
    //     0x67c94c: stur            d0, [x1, #0x13]
    // 0x67c950: mov             x0, x1
    // 0x67c954: ldur            x1, [fp, #-0x10]
    // 0x67c958: StoreField: r1->field_7 = r0
    //     0x67c958: stur            w0, [x1, #7]
    //     0x67c95c: ldurb           w16, [x1, #-1]
    //     0x67c960: ldurb           w17, [x0, #-1]
    //     0x67c964: and             x16, x17, x16, lsr #2
    //     0x67c968: tst             x16, HEAP, lsr #32
    //     0x67c96c: b.eq            #0x67c974
    //     0x67c970: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x67c974: r0 = Null
    //     0x67c974: mov             x0, NULL
    // 0x67c978: LeaveFrame
    //     0x67c978: mov             SP, fp
    //     0x67c97c: ldp             fp, lr, [SP], #0x10
    // 0x67c980: ret
    //     0x67c980: ret             
    // 0x67c984: r0 = StateError()
    //     0x67c984: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x67c988: mov             x1, x0
    // 0x67c98c: r0 = "TextPainter.text must be set to a non-null value before using the TextPainter."
    //     0x67c98c: ldr             x0, [PP, #0x4988]  ; [pp+0x4988] "TextPainter.text must be set to a non-null value before using the TextPainter."
    // 0x67c990: StoreField: r1->field_b = r0
    //     0x67c990: stur            w0, [x1, #0xb]
    // 0x67c994: mov             x0, x1
    // 0x67c998: r0 = Throw()
    //     0x67c998: bl              #0xec04b8  ; ThrowStub
    // 0x67c99c: brk             #0
    // 0x67c9a0: r0 = StateError()
    //     0x67c9a0: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x67c9a4: mov             x1, x0
    // 0x67c9a8: r0 = "TextPainter.textDirection must be set to a non-null value before using the TextPainter."
    //     0x67c9a8: ldr             x0, [PP, #0x4990]  ; [pp+0x4990] "TextPainter.textDirection must be set to a non-null value before using the TextPainter."
    // 0x67c9ac: StoreField: r1->field_b = r0
    //     0x67c9ac: stur            w0, [x1, #0xb]
    // 0x67c9b0: mov             x0, x1
    // 0x67c9b4: r0 = Throw()
    //     0x67c9b4: bl              #0xec04b8  ; ThrowStub
    // 0x67c9b8: brk             #0
    // 0x67c9bc: r0 = StackOverflowSharedWithFPURegs()
    //     0x67c9bc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x67c9c0: b               #0x67c560
    // 0x67c9c4: stp             q0, q1, [SP, #-0x20]!
    // 0x67c9c8: r0 = AllocateDouble()
    //     0x67c9c8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x67c9cc: ldp             q0, q1, [SP], #0x20
    // 0x67c9d0: b               #0x67c630
    // 0x67c9d4: r0 = NullErrorSharedWithFPURegs()
    //     0x67c9d4: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0x67c9d8: SaveReg d0
    //     0x67c9d8: str             q0, [SP, #-0x10]!
    // 0x67c9dc: r0 = AllocateDouble()
    //     0x67c9dc: bl              #0xec2254  ; AllocateDoubleStub
    // 0x67c9e0: RestoreReg d0
    //     0x67c9e0: ldr             q0, [SP], #0x10
    // 0x67c9e4: b               #0x67c6d0
    // 0x67c9e8: r0 = NullErrorSharedWithoutFPURegs()
    //     0x67c9e8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x67c9ec: r0 = NullErrorSharedWithFPURegs()
    //     0x67c9ec: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0x67c9f0: r0 = NullErrorSharedWithFPURegs()
    //     0x67c9f0: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
  }
  _ _createParagraph(/* No info */) {
    // ** addr: 0x67cb64, size: 0x94
    // 0x67cb64: EnterFrame
    //     0x67cb64: stp             fp, lr, [SP, #-0x10]!
    //     0x67cb68: mov             fp, SP
    // 0x67cb6c: AllocStack(0x18)
    //     0x67cb6c: sub             SP, SP, #0x18
    // 0x67cb70: SetupParameters(TextPainter this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x67cb70: mov             x0, x2
    //     0x67cb74: stur            x2, [fp, #-0x10]
    //     0x67cb78: mov             x2, x1
    //     0x67cb7c: stur            x1, [fp, #-8]
    // 0x67cb80: CheckStackOverflow
    //     0x67cb80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67cb84: cmp             SP, x16
    //     0x67cb88: b.ls            #0x67cbf0
    // 0x67cb8c: mov             x1, x2
    // 0x67cb90: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x67cb90: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x67cb94: r0 = _createParagraphStyle()
    //     0x67cb94: bl              #0x67d360  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_createParagraphStyle
    // 0x67cb98: stur            x0, [fp, #-0x18]
    // 0x67cb9c: r0 = _NativeParagraphBuilder()
    //     0x67cb9c: bl              #0x67d354  ; Allocate_NativeParagraphBuilderStub -> _NativeParagraphBuilder (size=0x1c)
    // 0x67cba0: mov             x1, x0
    // 0x67cba4: ldur            x2, [fp, #-0x18]
    // 0x67cba8: stur            x0, [fp, #-0x18]
    // 0x67cbac: r0 = _NativeParagraphBuilder()
    //     0x67cbac: bl              #0x67ce44  ; [dart:ui] _NativeParagraphBuilder::_NativeParagraphBuilder
    // 0x67cbb0: ldur            x0, [fp, #-8]
    // 0x67cbb4: LoadField: r5 = r0->field_1f
    //     0x67cbb4: ldur            w5, [x0, #0x1f]
    // 0x67cbb8: DecompressPointer r5
    //     0x67cbb8: add             x5, x5, HEAP, lsl #32
    // 0x67cbbc: LoadField: r3 = r0->field_3b
    //     0x67cbbc: ldur            w3, [x0, #0x3b]
    // 0x67cbc0: DecompressPointer r3
    //     0x67cbc0: add             x3, x3, HEAP, lsl #32
    // 0x67cbc4: ldur            x1, [fp, #-0x10]
    // 0x67cbc8: ldur            x2, [fp, #-0x18]
    // 0x67cbcc: r0 = build()
    //     0x67cbcc: bl              #0xd21e90  ; [package:flutter/src/painting/text_span.dart] TextSpan::build
    // 0x67cbd0: ldur            x0, [fp, #-8]
    // 0x67cbd4: r1 = false
    //     0x67cbd4: add             x1, NULL, #0x30  ; false
    // 0x67cbd8: StoreField: r0->field_b = r1
    //     0x67cbd8: stur            w1, [x0, #0xb]
    // 0x67cbdc: ldur            x1, [fp, #-0x18]
    // 0x67cbe0: r0 = build()
    //     0x67cbe0: bl              #0x67cbf8  ; [dart:ui] _NativeParagraphBuilder::build
    // 0x67cbe4: LeaveFrame
    //     0x67cbe4: mov             SP, fp
    //     0x67cbe8: ldp             fp, lr, [SP], #0x10
    // 0x67cbec: ret
    //     0x67cbec: ret             
    // 0x67cbf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67cbf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67cbf4: b               #0x67cb8c
  }
  _ _createParagraphStyle(/* No info */) {
    // ** addr: 0x67d360, size: 0xe0
    // 0x67d360: EnterFrame
    //     0x67d360: stp             fp, lr, [SP, #-0x10]!
    //     0x67d364: mov             fp, SP
    // 0x67d368: AllocStack(0x10)
    //     0x67d368: sub             SP, SP, #0x10
    // 0x67d36c: SetupParameters([dynamic _ = Null /* r0 */])
    //     0x67d36c: ldur            w0, [x4, #0x13]
    //     0x67d370: sub             x2, x0, #2
    //     0x67d374: cmp             w2, #2
    //     0x67d378: b.lt            #0x67d388
    //     0x67d37c: add             x0, fp, w2, sxtw #2
    //     0x67d380: ldr             x0, [x0, #8]
    //     0x67d384: b               #0x67d38c
    //     0x67d388: mov             x0, NULL
    // 0x67d38c: CheckStackOverflow
    //     0x67d38c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67d390: cmp             SP, x16
    //     0x67d394: b.ls            #0x67d438
    // 0x67d398: LoadField: r2 = r1->field_f
    //     0x67d398: ldur            w2, [x1, #0xf]
    // 0x67d39c: DecompressPointer r2
    //     0x67d39c: add             x2, x2, HEAP, lsl #32
    // 0x67d3a0: cmp             w2, NULL
    // 0x67d3a4: b.ne            #0x67d3b0
    // 0x67d3a8: r2 = Null
    //     0x67d3a8: mov             x2, NULL
    // 0x67d3ac: b               #0x67d3bc
    // 0x67d3b0: LoadField: r3 = r2->field_7
    //     0x67d3b0: ldur            w3, [x2, #7]
    // 0x67d3b4: DecompressPointer r3
    //     0x67d3b4: add             x3, x3, HEAP, lsl #32
    // 0x67d3b8: mov             x2, x3
    // 0x67d3bc: cmp             w2, NULL
    // 0x67d3c0: b.ne            #0x67d3c8
    // 0x67d3c4: r2 = Instance_TextStyle
    //     0x67d3c4: ldr             x2, [PP, #0x47e0]  ; [pp+0x47e0] Obj!TextStyle@e1ad91
    // 0x67d3c8: cmp             w0, NULL
    // 0x67d3cc: b.ne            #0x67d3e0
    // 0x67d3d0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x67d3d0: ldur            w0, [x1, #0x17]
    // 0x67d3d4: DecompressPointer r0
    //     0x67d3d4: add             x0, x0, HEAP, lsl #32
    // 0x67d3d8: mov             x7, x0
    // 0x67d3dc: b               #0x67d3e4
    // 0x67d3e0: mov             x7, x0
    // 0x67d3e4: LoadField: r0 = r1->field_1b
    //     0x67d3e4: ldur            w0, [x1, #0x1b]
    // 0x67d3e8: DecompressPointer r0
    //     0x67d3e8: add             x0, x0, HEAP, lsl #32
    // 0x67d3ec: LoadField: r3 = r1->field_1f
    //     0x67d3ec: ldur            w3, [x1, #0x1f]
    // 0x67d3f0: DecompressPointer r3
    //     0x67d3f0: add             x3, x3, HEAP, lsl #32
    // 0x67d3f4: LoadField: r5 = r1->field_2b
    //     0x67d3f4: ldur            w5, [x1, #0x2b]
    // 0x67d3f8: DecompressPointer r5
    //     0x67d3f8: add             x5, x5, HEAP, lsl #32
    // 0x67d3fc: LoadField: r4 = r1->field_23
    //     0x67d3fc: ldur            w4, [x1, #0x23]
    // 0x67d400: DecompressPointer r4
    //     0x67d400: add             x4, x4, HEAP, lsl #32
    // 0x67d404: LoadField: r6 = r1->field_27
    //     0x67d404: ldur            w6, [x1, #0x27]
    // 0x67d408: DecompressPointer r6
    //     0x67d408: add             x6, x6, HEAP, lsl #32
    // 0x67d40c: LoadField: r8 = r1->field_2f
    //     0x67d40c: ldur            w8, [x1, #0x2f]
    // 0x67d410: DecompressPointer r8
    //     0x67d410: add             x8, x8, HEAP, lsl #32
    // 0x67d414: stp             x3, x0, [SP]
    // 0x67d418: mov             x1, x2
    // 0x67d41c: mov             x2, x4
    // 0x67d420: mov             x3, x6
    // 0x67d424: mov             x6, x8
    // 0x67d428: r0 = getParagraphStyle()
    //     0x67d428: bl              #0x67d440  ; [package:flutter/src/painting/text_style.dart] TextStyle::getParagraphStyle
    // 0x67d42c: LeaveFrame
    //     0x67d42c: mov             SP, fp
    //     0x67d430: ldp             fp, lr, [SP], #0x10
    // 0x67d434: ret
    //     0x67d434: ret             
    // 0x67d438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67d438: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67d43c: b               #0x67d398
  }
  static _ _computePaintOffsetFraction(/* No info */) {
    // ** addr: 0x67e3ec, size: 0x234
    // 0x67e3ec: r16 = Instance_TextAlign
    //     0x67e3ec: ldr             x16, [PP, #0x4690]  ; [pp+0x4690] Obj!TextAlign@e39421
    // 0x67e3f0: cmp             w1, w16
    // 0x67e3f4: b.ne            #0x67e400
    // 0x67e3f8: r0 = 0.000000
    //     0x67e3f8: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x67e3fc: b               #0x67e618
    // 0x67e400: r16 = Instance_TextAlign
    //     0x67e400: ldr             x16, [PP, #0x4910]  ; [pp+0x4910] Obj!TextAlign@e394c1
    // 0x67e404: cmp             w1, w16
    // 0x67e408: b.ne            #0x67e414
    // 0x67e40c: r0 = 1.000000
    //     0x67e40c: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x67e410: b               #0x67e618
    // 0x67e414: r16 = Instance_TextAlign
    //     0x67e414: ldr             x16, [PP, #0x4920]  ; [pp+0x4920] Obj!TextAlign@e39441
    // 0x67e418: cmp             w1, w16
    // 0x67e41c: b.ne            #0x67e428
    // 0x67e420: r0 = 0.500000
    //     0x67e420: ldr             x0, [PP, #0x4928]  ; [pp+0x4928] 0.5
    // 0x67e424: b               #0x67e618
    // 0x67e428: r16 = Instance_TextAlign
    //     0x67e428: ldr             x16, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0x67e42c: cmp             w1, w16
    // 0x67e430: r16 = true
    //     0x67e430: add             x16, NULL, #0x20  ; true
    // 0x67e434: r17 = false
    //     0x67e434: add             x17, NULL, #0x30  ; false
    // 0x67e438: csel            x0, x16, x17, eq
    // 0x67e43c: tbnz            w0, #4, #0x67e44c
    // 0x67e440: r5 = Null
    //     0x67e440: mov             x5, NULL
    // 0x67e444: r4 = false
    //     0x67e444: add             x4, NULL, #0x30  ; false
    // 0x67e448: b               #0x67e46c
    // 0x67e44c: r16 = Instance_TextAlign
    //     0x67e44c: ldr             x16, [PP, #0x4938]  ; [pp+0x4938] Obj!TextAlign@e39481
    // 0x67e450: cmp             w1, w16
    // 0x67e454: r16 = true
    //     0x67e454: add             x16, NULL, #0x20  ; true
    // 0x67e458: r17 = false
    //     0x67e458: add             x17, NULL, #0x30  ; false
    // 0x67e45c: csel            x3, x16, x17, eq
    // 0x67e460: tbnz            w3, #4, #0x67e4b4
    // 0x67e464: mov             x5, x3
    // 0x67e468: r4 = true
    //     0x67e468: add             x4, NULL, #0x20  ; true
    // 0x67e46c: r16 = Instance_TextDirection
    //     0x67e46c: ldr             x16, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0x67e470: cmp             w2, w16
    // 0x67e474: r16 = true
    //     0x67e474: add             x16, NULL, #0x20  ; true
    // 0x67e478: r17 = false
    //     0x67e478: add             x17, NULL, #0x30  ; false
    // 0x67e47c: csel            x6, x16, x17, eq
    // 0x67e480: tbnz            w6, #4, #0x67e48c
    // 0x67e484: r0 = 0.000000
    //     0x67e484: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x67e488: b               #0x67e618
    // 0x67e48c: mov             x8, x2
    // 0x67e490: mov             x16, x4
    // 0x67e494: mov             x4, x5
    // 0x67e498: mov             x5, x16
    // 0x67e49c: mov             x16, x6
    // 0x67e4a0: mov             x6, x4
    // 0x67e4a4: mov             x4, x16
    // 0x67e4a8: r7 = true
    //     0x67e4a8: add             x7, NULL, #0x20  ; true
    // 0x67e4ac: r3 = true
    //     0x67e4ac: add             x3, NULL, #0x20  ; true
    // 0x67e4b0: b               #0x67e4cc
    // 0x67e4b4: mov             x6, x3
    // 0x67e4b8: r8 = Null
    //     0x67e4b8: mov             x8, NULL
    // 0x67e4bc: r7 = false
    //     0x67e4bc: add             x7, NULL, #0x30  ; false
    // 0x67e4c0: r5 = true
    //     0x67e4c0: add             x5, NULL, #0x20  ; true
    // 0x67e4c4: r4 = Null
    //     0x67e4c4: mov             x4, NULL
    // 0x67e4c8: r3 = false
    //     0x67e4c8: add             x3, NULL, #0x30  ; false
    // 0x67e4cc: tbz             w0, #4, #0x67e4fc
    // 0x67e4d0: tbnz            w5, #4, #0x67e4dc
    // 0x67e4d4: mov             x0, x6
    // 0x67e4d8: b               #0x67e4f0
    // 0x67e4dc: r16 = Instance_TextAlign
    //     0x67e4dc: ldr             x16, [PP, #0x4938]  ; [pp+0x4938] Obj!TextAlign@e39481
    // 0x67e4e0: cmp             w1, w16
    // 0x67e4e4: r16 = true
    //     0x67e4e4: add             x16, NULL, #0x20  ; true
    // 0x67e4e8: r17 = false
    //     0x67e4e8: add             x17, NULL, #0x30  ; false
    // 0x67e4ec: csel            x0, x16, x17, eq
    // 0x67e4f0: r16 = true
    //     0x67e4f0: add             x16, NULL, #0x20  ; true
    // 0x67e4f4: cmp             w0, w16
    // 0x67e4f8: b.ne            #0x67e548
    // 0x67e4fc: tbnz            w7, #4, #0x67e50c
    // 0x67e500: mov             x5, x8
    // 0x67e504: mov             x0, x8
    // 0x67e508: b               #0x67e514
    // 0x67e50c: mov             x5, x2
    // 0x67e510: mov             x0, x2
    // 0x67e514: r16 = Instance_TextDirection
    //     0x67e514: ldr             x16, [PP, #0x2898]  ; [pp+0x2898] Obj!TextDirection@e392e1
    // 0x67e518: cmp             w5, w16
    // 0x67e51c: r16 = true
    //     0x67e51c: add             x16, NULL, #0x20  ; true
    // 0x67e520: r17 = false
    //     0x67e520: add             x17, NULL, #0x30  ; false
    // 0x67e524: csel            x6, x16, x17, eq
    // 0x67e528: tbnz            w6, #4, #0x67e534
    // 0x67e52c: r0 = 1.000000
    //     0x67e52c: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x67e530: b               #0x67e618
    // 0x67e534: mov             x7, x6
    // 0x67e538: mov             x5, x0
    // 0x67e53c: r6 = true
    //     0x67e53c: add             x6, NULL, #0x20  ; true
    // 0x67e540: r0 = true
    //     0x67e540: add             x0, NULL, #0x20  ; true
    // 0x67e544: b               #0x67e558
    // 0x67e548: mov             x5, x8
    // 0x67e54c: mov             x0, x7
    // 0x67e550: r7 = Null
    //     0x67e550: mov             x7, NULL
    // 0x67e554: r6 = false
    //     0x67e554: add             x6, NULL, #0x30  ; false
    // 0x67e558: r16 = Instance_TextAlign
    //     0x67e558: ldr             x16, [PP, #0x4940]  ; [pp+0x4940] Obj!TextAlign@e39461
    // 0x67e55c: cmp             w1, w16
    // 0x67e560: r16 = true
    //     0x67e560: add             x16, NULL, #0x20  ; true
    // 0x67e564: r17 = false
    //     0x67e564: add             x17, NULL, #0x30  ; false
    // 0x67e568: csel            x8, x16, x17, eq
    // 0x67e56c: tbnz            w8, #4, #0x67e5d4
    // 0x67e570: tbnz            w3, #4, #0x67e580
    // 0x67e574: mov             x3, x5
    // 0x67e578: mov             x1, x0
    // 0x67e57c: b               #0x67e5b4
    // 0x67e580: tbnz            w0, #4, #0x67e590
    // 0x67e584: mov             x3, x5
    // 0x67e588: mov             x1, x5
    // 0x67e58c: b               #0x67e598
    // 0x67e590: mov             x3, x2
    // 0x67e594: mov             x1, x2
    // 0x67e598: r16 = Instance_TextDirection
    //     0x67e598: ldr             x16, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0x67e59c: cmp             w3, w16
    // 0x67e5a0: r16 = true
    //     0x67e5a0: add             x16, NULL, #0x20  ; true
    // 0x67e5a4: r17 = false
    //     0x67e5a4: add             x17, NULL, #0x30  ; false
    // 0x67e5a8: csel            x4, x16, x17, eq
    // 0x67e5ac: mov             x3, x1
    // 0x67e5b0: r1 = true
    //     0x67e5b0: add             x1, NULL, #0x20  ; true
    // 0x67e5b4: r16 = true
    //     0x67e5b4: add             x16, NULL, #0x20  ; true
    // 0x67e5b8: cmp             w4, w16
    // 0x67e5bc: b.ne            #0x67e5c8
    // 0x67e5c0: r0 = 1.000000
    //     0x67e5c0: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x67e5c4: b               #0x67e618
    // 0x67e5c8: mov             x0, x1
    // 0x67e5cc: mov             x1, x3
    // 0x67e5d0: b               #0x67e5d8
    // 0x67e5d4: mov             x1, x5
    // 0x67e5d8: tbnz            w8, #4, #0x67e614
    // 0x67e5dc: tbnz            w6, #4, #0x67e5f0
    // 0x67e5e0: r16 = true
    //     0x67e5e0: add             x16, NULL, #0x20  ; true
    // 0x67e5e4: cmp             w7, w16
    // 0x67e5e8: b.ne            #0x67e614
    // 0x67e5ec: b               #0x67e60c
    // 0x67e5f0: tbnz            w0, #4, #0x67e5fc
    // 0x67e5f4: mov             x0, x1
    // 0x67e5f8: b               #0x67e600
    // 0x67e5fc: mov             x0, x2
    // 0x67e600: r16 = Instance_TextDirection
    //     0x67e600: ldr             x16, [PP, #0x2898]  ; [pp+0x2898] Obj!TextDirection@e392e1
    // 0x67e604: cmp             w0, w16
    // 0x67e608: b.ne            #0x67e614
    // 0x67e60c: r0 = 0.000000
    //     0x67e60c: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x67e610: b               #0x67e618
    // 0x67e614: r0 = Null
    //     0x67e614: mov             x0, NULL
    // 0x67e618: LoadField: d0 = r0->field_7
    //     0x67e618: ldur            d0, [x0, #7]
    // 0x67e61c: ret
    //     0x67e61c: ret             
  }
  get _ width(/* No info */) {
    // ** addr: 0x67f8f4, size: 0x24
    // 0x67f8f4: LoadField: r0 = r1->field_7
    //     0x67f8f4: ldur            w0, [x1, #7]
    // 0x67f8f8: DecompressPointer r0
    //     0x67f8f8: add             x0, x0, HEAP, lsl #32
    // 0x67f8fc: cmp             w0, NULL
    // 0x67f900: b.eq            #0x67f90c
    // 0x67f904: LoadField: d0 = r0->field_13
    //     0x67f904: ldur            d0, [x0, #0x13]
    // 0x67f908: ret
    //     0x67f908: ret             
    // 0x67f90c: EnterFrame
    //     0x67f90c: stp             fp, lr, [SP, #-0x10]!
    //     0x67f910: mov             fp, SP
    // 0x67f914: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67f914: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ getFullHeightForCaret(/* No info */) {
    // ** addr: 0x680f64, size: 0x1a4
    // 0x680f64: EnterFrame
    //     0x680f64: stp             fp, lr, [SP, #-0x10]!
    //     0x680f68: mov             fp, SP
    // 0x680f6c: AllocStack(0x28)
    //     0x680f6c: sub             SP, SP, #0x28
    // 0x680f70: SetupParameters(TextPainter this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x680f70: stur            x1, [fp, #-8]
    //     0x680f74: stur            x2, [fp, #-0x10]
    // 0x680f78: CheckStackOverflow
    //     0x680f78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x680f7c: cmp             SP, x16
    //     0x680f80: b.ls            #0x6810ec
    // 0x680f84: LoadField: r0 = r1->field_2f
    //     0x680f84: ldur            w0, [x1, #0x2f]
    // 0x680f88: DecompressPointer r0
    //     0x680f88: add             x0, x0, HEAP, lsl #32
    // 0x680f8c: cmp             w0, NULL
    // 0x680f90: b.eq            #0x680ff0
    // 0x680f94: r16 = Instance_StrutStyle
    //     0x680f94: ldr             x16, [PP, #0x4848]  ; [pp+0x4848] Obj!StrutStyle@e1c0d1
    // 0x680f98: stp             x16, x0, [SP]
    // 0x680f9c: r0 = ==()
    //     0x680f9c: bl              #0xd585e8  ; [package:flutter/src/painting/strut_style.dart] StrutStyle::==
    // 0x680fa0: tbz             w0, #4, #0x680ff0
    // 0x680fa4: ldur            x1, [fp, #-8]
    // 0x680fa8: LoadField: r0 = r1->field_2f
    //     0x680fa8: ldur            w0, [x1, #0x2f]
    // 0x680fac: DecompressPointer r0
    //     0x680fac: add             x0, x0, HEAP, lsl #32
    // 0x680fb0: cmp             w0, NULL
    // 0x680fb4: b.ne            #0x680fc0
    // 0x680fb8: r0 = Null
    //     0x680fb8: mov             x0, NULL
    // 0x680fbc: b               #0x680fcc
    // 0x680fc0: LoadField: r2 = r0->field_13
    //     0x680fc0: ldur            w2, [x0, #0x13]
    // 0x680fc4: DecompressPointer r2
    //     0x680fc4: add             x2, x2, HEAP, lsl #32
    // 0x680fc8: mov             x0, x2
    // 0x680fcc: r2 = LoadClassIdInstr(r0)
    //     0x680fcc: ldur            x2, [x0, #-1]
    //     0x680fd0: ubfx            x2, x2, #0xc, #0x14
    // 0x680fd4: r16 = 0.000000
    //     0x680fd4: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x680fd8: stp             x16, x0, [SP]
    // 0x680fdc: mov             x0, x2
    // 0x680fe0: mov             lr, x0
    // 0x680fe4: ldr             lr, [x21, lr, lsl #3]
    // 0x680fe8: blr             lr
    // 0x680fec: tbnz            w0, #4, #0x681050
    // 0x680ff0: ldur            x1, [fp, #-8]
    // 0x680ff4: ldur            x2, [fp, #-0x10]
    // 0x680ff8: r0 = _computeCaretMetrics()
    //     0x680ff8: bl              #0x683454  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_computeCaretMetrics
    // 0x680ffc: cmp             w0, NULL
    // 0x681000: b.ne            #0x68100c
    // 0x681004: r0 = Null
    //     0x681004: mov             x0, NULL
    // 0x681008: b               #0x681038
    // 0x68100c: LoadField: d0 = r0->field_f
    //     0x68100c: ldur            d0, [x0, #0xf]
    // 0x681010: r0 = inline_Allocate_Double()
    //     0x681010: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x681014: add             x0, x0, #0x10
    //     0x681018: cmp             x1, x0
    //     0x68101c: b.ls            #0x6810f4
    //     0x681020: str             x0, [THR, #0x50]  ; THR::top
    //     0x681024: sub             x0, x0, #0xf
    //     0x681028: movz            x1, #0xe15c
    //     0x68102c: movk            x1, #0x3, lsl #16
    //     0x681030: stur            x1, [x0, #-1]
    // 0x681034: StoreField: r0->field_7 = d0
    //     0x681034: stur            d0, [x0, #7]
    // 0x681038: cmp             w0, NULL
    // 0x68103c: b.eq            #0x681050
    // 0x681040: LoadField: d0 = r0->field_7
    //     0x681040: ldur            d0, [x0, #7]
    // 0x681044: LeaveFrame
    //     0x681044: mov             SP, fp
    //     0x681048: ldp             fp, lr, [SP], #0x10
    // 0x68104c: ret
    //     0x68104c: ret             
    // 0x681050: ldur            x1, [fp, #-8]
    // 0x681054: r0 = _getOrCreateLayoutTemplate()
    //     0x681054: bl              #0x681108  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_getOrCreateLayoutTemplate
    // 0x681058: stur            x0, [fp, #-8]
    // 0x68105c: LoadField: r1 = r0->field_7
    //     0x68105c: ldur            w1, [x0, #7]
    // 0x681060: DecompressPointer r1
    //     0x681060: add             x1, x1, HEAP, lsl #32
    // 0x681064: cmp             w1, NULL
    // 0x681068: b.eq            #0x681104
    // 0x68106c: LoadField: r2 = r1->field_7
    //     0x68106c: ldur            x2, [x1, #7]
    // 0x681070: ldr             x1, [x2]
    // 0x681074: stur            x1, [fp, #-0x18]
    // 0x681078: cbnz            x1, #0x681088
    // 0x68107c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x68107c: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x681080: str             x16, [SP]
    // 0x681084: r0 = _throwNew()
    //     0x681084: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x681088: ldur            x0, [fp, #-0x18]
    // 0x68108c: stur            x0, [fp, #-0x18]
    // 0x681090: r1 = <Never>
    //     0x681090: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x681094: r0 = Pointer()
    //     0x681094: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x681098: mov             x1, x0
    // 0x68109c: ldur            x0, [fp, #-0x18]
    // 0x6810a0: StoreField: r1->field_7 = r0
    //     0x6810a0: stur            x0, [x1, #7]
    // 0x6810a4: r2 = 0
    //     0x6810a4: movz            x2, #0
    // 0x6810a8: r3 = 1
    //     0x6810a8: movz            x3, #0x1
    // 0x6810ac: r5 = 5
    //     0x6810ac: movz            x5, #0x5
    // 0x6810b0: r6 = 0
    //     0x6810b0: movz            x6, #0
    // 0x6810b4: r0 = __getBoxesForRange$Method$FfiNative()
    //     0x6810b4: bl              #0x67bdcc  ; [dart:ui] _NativeParagraph::__getBoxesForRange$Method$FfiNative
    // 0x6810b8: ldur            x1, [fp, #-8]
    // 0x6810bc: mov             x2, x0
    // 0x6810c0: r0 = _decodeTextBoxes()
    //     0x6810c0: bl              #0x67badc  ; [dart:ui] _NativeParagraph::_decodeTextBoxes
    // 0x6810c4: mov             x1, x0
    // 0x6810c8: r0 = single()
    //     0x6810c8: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x6810cc: mov             x1, x0
    // 0x6810d0: r0 = outerRect()
    //     0x6810d0: bl              #0x67b7a4  ; [dart:ui] RRect::outerRect
    // 0x6810d4: LoadField: d1 = r0->field_1f
    //     0x6810d4: ldur            d1, [x0, #0x1f]
    // 0x6810d8: LoadField: d2 = r0->field_f
    //     0x6810d8: ldur            d2, [x0, #0xf]
    // 0x6810dc: fsub            d0, d1, d2
    // 0x6810e0: LeaveFrame
    //     0x6810e0: mov             SP, fp
    //     0x6810e4: ldp             fp, lr, [SP], #0x10
    // 0x6810e8: ret
    //     0x6810e8: ret             
    // 0x6810ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6810ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6810f0: b               #0x680f84
    // 0x6810f4: SaveReg d0
    //     0x6810f4: str             q0, [SP, #-0x10]!
    // 0x6810f8: r0 = AllocateDouble()
    //     0x6810f8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x6810fc: RestoreReg d0
    //     0x6810fc: ldr             q0, [SP], #0x10
    // 0x681100: b               #0x681034
    // 0x681104: r0 = NullErrorSharedWithoutFPURegs()
    //     0x681104: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _getOrCreateLayoutTemplate(/* No info */) {
    // ** addr: 0x681108, size: 0x7c
    // 0x681108: EnterFrame
    //     0x681108: stp             fp, lr, [SP, #-0x10]!
    //     0x68110c: mov             fp, SP
    // 0x681110: AllocStack(0x8)
    //     0x681110: sub             SP, SP, #8
    // 0x681114: SetupParameters(TextPainter this /* r1 => r0, fp-0x8 */)
    //     0x681114: mov             x0, x1
    //     0x681118: stur            x1, [fp, #-8]
    // 0x68111c: CheckStackOverflow
    //     0x68111c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x681120: cmp             SP, x16
    //     0x681124: b.ls            #0x68117c
    // 0x681128: LoadField: r1 = r0->field_3f
    //     0x681128: ldur            w1, [x0, #0x3f]
    // 0x68112c: DecompressPointer r1
    //     0x68112c: add             x1, x1, HEAP, lsl #32
    // 0x681130: cmp             w1, NULL
    // 0x681134: b.ne            #0x68116c
    // 0x681138: mov             x1, x0
    // 0x68113c: r0 = _createLayoutTemplate()
    //     0x68113c: bl              #0x681184  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_createLayoutTemplate
    // 0x681140: mov             x1, x0
    // 0x681144: ldur            x2, [fp, #-8]
    // 0x681148: StoreField: r2->field_3f = r0
    //     0x681148: stur            w0, [x2, #0x3f]
    //     0x68114c: ldurb           w16, [x2, #-1]
    //     0x681150: ldurb           w17, [x0, #-1]
    //     0x681154: and             x16, x17, x16, lsr #2
    //     0x681158: tst             x16, HEAP, lsr #32
    //     0x68115c: b.eq            #0x681164
    //     0x681160: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x681164: mov             x0, x1
    // 0x681168: b               #0x681170
    // 0x68116c: mov             x0, x1
    // 0x681170: LeaveFrame
    //     0x681170: mov             SP, fp
    //     0x681174: ldp             fp, lr, [SP], #0x10
    // 0x681178: ret
    //     0x681178: ret             
    // 0x68117c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68117c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x681180: b               #0x681128
  }
  _ _createLayoutTemplate(/* No info */) {
    // ** addr: 0x681184, size: 0x140
    // 0x681184: EnterFrame
    //     0x681184: stp             fp, lr, [SP, #-0x10]!
    //     0x681188: mov             fp, SP
    // 0x68118c: AllocStack(0x28)
    //     0x68118c: sub             SP, SP, #0x28
    // 0x681190: SetupParameters(TextPainter this /* r1 => r0, fp-0x8 */)
    //     0x681190: mov             x0, x1
    //     0x681194: stur            x1, [fp, #-8]
    // 0x681198: CheckStackOverflow
    //     0x681198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68119c: cmp             SP, x16
    //     0x6811a0: b.ls            #0x6812b8
    // 0x6811a4: r16 = Instance_TextAlign
    //     0x6811a4: ldr             x16, [PP, #0x4690]  ; [pp+0x4690] Obj!TextAlign@e39421
    // 0x6811a8: str             x16, [SP]
    // 0x6811ac: mov             x1, x0
    // 0x6811b0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x6811b0: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x6811b4: r0 = _createParagraphStyle()
    //     0x6811b4: bl              #0x67d360  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_createParagraphStyle
    // 0x6811b8: stur            x0, [fp, #-0x10]
    // 0x6811bc: r0 = _NativeParagraphBuilder()
    //     0x6811bc: bl              #0x67d354  ; Allocate_NativeParagraphBuilderStub -> _NativeParagraphBuilder (size=0x1c)
    // 0x6811c0: mov             x1, x0
    // 0x6811c4: ldur            x2, [fp, #-0x10]
    // 0x6811c8: stur            x0, [fp, #-0x10]
    // 0x6811cc: r0 = _NativeParagraphBuilder()
    //     0x6811cc: bl              #0x67ce44  ; [dart:ui] _NativeParagraphBuilder::_NativeParagraphBuilder
    // 0x6811d0: ldur            x0, [fp, #-8]
    // 0x6811d4: LoadField: r1 = r0->field_f
    //     0x6811d4: ldur            w1, [x0, #0xf]
    // 0x6811d8: DecompressPointer r1
    //     0x6811d8: add             x1, x1, HEAP, lsl #32
    // 0x6811dc: cmp             w1, NULL
    // 0x6811e0: b.ne            #0x6811ec
    // 0x6811e4: r2 = Null
    //     0x6811e4: mov             x2, NULL
    // 0x6811e8: b               #0x681220
    // 0x6811ec: LoadField: r2 = r1->field_7
    //     0x6811ec: ldur            w2, [x1, #7]
    // 0x6811f0: DecompressPointer r2
    //     0x6811f0: add             x2, x2, HEAP, lsl #32
    // 0x6811f4: cmp             w2, NULL
    // 0x6811f8: b.ne            #0x681204
    // 0x6811fc: r0 = Null
    //     0x6811fc: mov             x0, NULL
    // 0x681200: b               #0x68121c
    // 0x681204: LoadField: r1 = r0->field_1f
    //     0x681204: ldur            w1, [x0, #0x1f]
    // 0x681208: DecompressPointer r1
    //     0x681208: add             x1, x1, HEAP, lsl #32
    // 0x68120c: mov             x16, x1
    // 0x681210: mov             x1, x2
    // 0x681214: mov             x2, x16
    // 0x681218: r0 = getTextStyle()
    //     0x681218: bl              #0x682270  ; [package:flutter/src/painting/text_style.dart] TextStyle::getTextStyle
    // 0x68121c: mov             x2, x0
    // 0x681220: cmp             w2, NULL
    // 0x681224: b.eq            #0x681230
    // 0x681228: ldur            x1, [fp, #-0x10]
    // 0x68122c: r0 = pushStyle()
    //     0x68122c: bl              #0x681554  ; [dart:ui] _NativeParagraphBuilder::pushStyle
    // 0x681230: ldur            x1, [fp, #-0x10]
    // 0x681234: r2 = " "
    //     0x681234: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x681238: r0 = addText()
    //     0x681238: bl              #0x6812c4  ; [dart:ui] _NativeParagraphBuilder::addText
    // 0x68123c: ldur            x1, [fp, #-0x10]
    // 0x681240: r0 = build()
    //     0x681240: bl              #0x67cbf8  ; [dart:ui] _NativeParagraphBuilder::build
    // 0x681244: mov             x1, x0
    // 0x681248: r0 = Instance_ParagraphConstraints
    //     0x681248: ldr             x0, [PP, #0x4698]  ; [pp+0x4698] Obj!ParagraphConstraints@e26381
    // 0x68124c: stur            x1, [fp, #-8]
    // 0x681250: LoadField: d0 = r0->field_7
    //     0x681250: ldur            d0, [x0, #7]
    // 0x681254: stur            d0, [fp, #-0x20]
    // 0x681258: LoadField: r0 = r1->field_7
    //     0x681258: ldur            w0, [x1, #7]
    // 0x68125c: DecompressPointer r0
    //     0x68125c: add             x0, x0, HEAP, lsl #32
    // 0x681260: cmp             w0, NULL
    // 0x681264: b.eq            #0x6812c0
    // 0x681268: LoadField: r2 = r0->field_7
    //     0x681268: ldur            x2, [x0, #7]
    // 0x68126c: ldr             x0, [x2]
    // 0x681270: stur            x0, [fp, #-0x18]
    // 0x681274: cbnz            x0, #0x681284
    // 0x681278: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x681278: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x68127c: str             x16, [SP]
    // 0x681280: r0 = _throwNew()
    //     0x681280: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x681284: ldur            x0, [fp, #-0x18]
    // 0x681288: stur            x0, [fp, #-0x18]
    // 0x68128c: r1 = <Never>
    //     0x68128c: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x681290: r0 = Pointer()
    //     0x681290: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x681294: mov             x1, x0
    // 0x681298: ldur            x0, [fp, #-0x18]
    // 0x68129c: StoreField: r1->field_7 = r0
    //     0x68129c: stur            x0, [x1, #7]
    // 0x6812a0: ldur            d0, [fp, #-0x20]
    // 0x6812a4: r0 = __layout$Method$FfiNative()
    //     0x6812a4: bl              #0x67cad4  ; [dart:ui] _NativeParagraph::__layout$Method$FfiNative
    // 0x6812a8: ldur            x0, [fp, #-8]
    // 0x6812ac: LeaveFrame
    //     0x6812ac: mov             SP, fp
    //     0x6812b0: ldp             fp, lr, [SP], #0x10
    // 0x6812b4: ret
    //     0x6812b4: ret             
    // 0x6812b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6812b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6812bc: b               #0x6811a4
    // 0x6812c0: r0 = NullErrorSharedWithFPURegs()
    //     0x6812c0: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
  }
  _ _computeCaretMetrics(/* No info */) {
    // ** addr: 0x683454, size: 0x6cc
    // 0x683454: EnterFrame
    //     0x683454: stp             fp, lr, [SP, #-0x10]!
    //     0x683458: mov             fp, SP
    // 0x68345c: AllocStack(0x78)
    //     0x68345c: sub             SP, SP, #0x78
    // 0x683460: SetupParameters(TextPainter this /* r1 => r1, fp-0x28 */, dynamic _ /* r2 => r2, fp-0x30 */)
    //     0x683460: stur            x1, [fp, #-0x28]
    //     0x683464: stur            x2, [fp, #-0x30]
    // 0x683468: CheckStackOverflow
    //     0x683468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68346c: cmp             SP, x16
    //     0x683470: b.ls            #0x683af8
    // 0x683474: LoadField: r0 = r1->field_7
    //     0x683474: ldur            w0, [x1, #7]
    // 0x683478: DecompressPointer r0
    //     0x683478: add             x0, x0, HEAP, lsl #32
    // 0x68347c: stur            x0, [fp, #-0x20]
    // 0x683480: cmp             w0, NULL
    // 0x683484: b.eq            #0x683b00
    // 0x683488: LoadField: r3 = r0->field_7
    //     0x683488: ldur            w3, [x0, #7]
    // 0x68348c: DecompressPointer r3
    //     0x68348c: add             x3, x3, HEAP, lsl #32
    // 0x683490: stur            x3, [fp, #-0x18]
    // 0x683494: LoadField: r4 = r3->field_f
    //     0x683494: ldur            w4, [x3, #0xf]
    // 0x683498: DecompressPointer r4
    //     0x683498: add             x4, x4, HEAP, lsl #32
    // 0x68349c: stur            x4, [fp, #-0x10]
    // 0x6834a0: LoadField: r5 = r4->field_7
    //     0x6834a0: ldur            w5, [x4, #7]
    // 0x6834a4: DecompressPointer r5
    //     0x6834a4: add             x5, x5, HEAP, lsl #32
    // 0x6834a8: cmp             w5, NULL
    // 0x6834ac: b.eq            #0x683b04
    // 0x6834b0: LoadField: r6 = r5->field_7
    //     0x6834b0: ldur            x6, [x5, #7]
    // 0x6834b4: ldr             x5, [x6]
    // 0x6834b8: stur            x5, [fp, #-8]
    // 0x6834bc: cbnz            x5, #0x6834cc
    // 0x6834c0: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x6834c0: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x6834c4: str             x16, [SP]
    // 0x6834c8: r0 = _throwNew()
    //     0x6834c8: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x6834cc: ldur            x0, [fp, #-8]
    // 0x6834d0: stur            x0, [fp, #-8]
    // 0x6834d4: r1 = <Never>
    //     0x6834d4: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x6834d8: r0 = Pointer()
    //     0x6834d8: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x6834dc: mov             x1, x0
    // 0x6834e0: ldur            x0, [fp, #-8]
    // 0x6834e4: StoreField: r1->field_7 = r0
    //     0x6834e4: stur            x0, [x1, #7]
    // 0x6834e8: r0 = _numberOfLines$Getter$FfiNative()
    //     0x6834e8: bl              #0x684250  ; [dart:ui] _NativeParagraph::_numberOfLines$Getter$FfiNative
    // 0x6834ec: cmp             x0, #1
    // 0x6834f0: b.lt            #0x683504
    // 0x6834f4: ldur            x1, [fp, #-0x28]
    // 0x6834f8: r0 = plainText()
    //     0x6834f8: bl              #0x6840f0  ; [package:flutter/src/painting/text_painter.dart] TextPainter::plainText
    // 0x6834fc: LoadField: r1 = r0->field_7
    //     0x6834fc: ldur            w1, [x0, #7]
    // 0x683500: cbnz            w1, #0x683514
    // 0x683504: r0 = Null
    //     0x683504: mov             x0, NULL
    // 0x683508: LeaveFrame
    //     0x683508: mov             SP, fp
    //     0x68350c: ldp             fp, lr, [SP], #0x10
    // 0x683510: ret
    //     0x683510: ret             
    // 0x683514: ldur            x2, [fp, #-0x30]
    // 0x683518: LoadField: r3 = r2->field_7
    //     0x683518: ldur            x3, [x2, #7]
    // 0x68351c: stur            x3, [fp, #-8]
    // 0x683520: r0 = BoxInt64Instr(r3)
    //     0x683520: sbfiz           x0, x3, #1, #0x1f
    //     0x683524: cmp             x3, x0, asr #1
    //     0x683528: b.eq            #0x683534
    //     0x68352c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x683530: stur            x3, [x0, #7]
    // 0x683534: stur            x0, [fp, #-0x38]
    // 0x683538: cbnz            w0, #0x68354c
    // 0x68353c: r2 = 0
    //     0x68353c: movz            x2, #0
    // 0x683540: r3 = true
    //     0x683540: add             x3, NULL, #0x20  ; true
    // 0x683544: r0 = AllocateRecord2()
    //     0x683544: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x683548: b               #0x6835e4
    // 0x68354c: LoadField: r1 = r2->field_f
    //     0x68354c: ldur            w1, [x2, #0xf]
    // 0x683550: DecompressPointer r1
    //     0x683550: add             x1, x1, HEAP, lsl #32
    // 0x683554: r16 = Instance_TextAffinity
    //     0x683554: ldr             x16, [PP, #0x4878]  ; [pp+0x4878] Obj!TextAffinity@e392a1
    // 0x683558: cmp             w1, w16
    // 0x68355c: b.ne            #0x683570
    // 0x683560: mov             x2, x0
    // 0x683564: r3 = true
    //     0x683564: add             x3, NULL, #0x20  ; true
    // 0x683568: r0 = AllocateRecord2()
    //     0x683568: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x68356c: b               #0x6835e4
    // 0x683570: r16 = Instance_TextAffinity
    //     0x683570: ldr             x16, [PP, #0x4880]  ; [pp+0x4880] Obj!TextAffinity@e39281
    // 0x683574: cmp             w1, w16
    // 0x683578: r16 = true
    //     0x683578: add             x16, NULL, #0x20  ; true
    // 0x68357c: r17 = false
    //     0x68357c: add             x17, NULL, #0x30  ; false
    // 0x683580: csel            x4, x16, x17, eq
    // 0x683584: stur            x4, [fp, #-0x10]
    // 0x683588: tbnz            w4, #4, #0x6835ac
    // 0x68358c: sub             x2, x3, #1
    // 0x683590: ldur            x1, [fp, #-0x28]
    // 0x683594: r0 = _isNewlineAtOffset()
    //     0x683594: bl              #0x683fe8  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_isNewlineAtOffset
    // 0x683598: tbnz            w0, #4, #0x6835ac
    // 0x68359c: ldur            x2, [fp, #-0x38]
    // 0x6835a0: r3 = true
    //     0x6835a0: add             x3, NULL, #0x20  ; true
    // 0x6835a4: r0 = AllocateRecord2()
    //     0x6835a4: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x6835a8: b               #0x6835e4
    // 0x6835ac: ldur            x0, [fp, #-0x10]
    // 0x6835b0: tbnz            w0, #4, #0x6835e0
    // 0x6835b4: ldur            x0, [fp, #-8]
    // 0x6835b8: sub             x2, x0, #1
    // 0x6835bc: r0 = BoxInt64Instr(r2)
    //     0x6835bc: sbfiz           x0, x2, #1, #0x1f
    //     0x6835c0: cmp             x2, x0, asr #1
    //     0x6835c4: b.eq            #0x6835d0
    //     0x6835c8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6835cc: stur            x2, [x0, #7]
    // 0x6835d0: mov             x2, x0
    // 0x6835d4: r3 = false
    //     0x6835d4: add             x3, NULL, #0x30  ; false
    // 0x6835d8: r0 = AllocateRecord2()
    //     0x6835d8: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x6835dc: b               #0x6835e4
    // 0x6835e0: r0 = Null
    //     0x6835e0: mov             x0, NULL
    // 0x6835e4: LoadField: r2 = r0->field_f
    //     0x6835e4: ldur            w2, [x0, #0xf]
    // 0x6835e8: DecompressPointer r2
    //     0x6835e8: add             x2, x2, HEAP, lsl #32
    // 0x6835ec: stur            x2, [fp, #-0x40]
    // 0x6835f0: LoadField: r3 = r0->field_13
    //     0x6835f0: ldur            w3, [x0, #0x13]
    // 0x6835f4: DecompressPointer r3
    //     0x6835f4: add             x3, x3, HEAP, lsl #32
    // 0x6835f8: stur            x3, [fp, #-0x38]
    // 0x6835fc: tbnz            w3, #4, #0x683614
    // 0x683600: r0 = LoadInt32Instr(r2)
    //     0x683600: sbfx            x0, x2, #1, #0x1f
    //     0x683604: tbz             w2, #0, #0x68360c
    //     0x683608: ldur            x0, [x2, #7]
    // 0x68360c: mov             x5, x0
    // 0x683610: b               #0x68362c
    // 0x683614: r0 = LoadInt32Instr(r2)
    //     0x683614: sbfx            x0, x2, #1, #0x1f
    //     0x683618: tbz             w2, #0, #0x683620
    //     0x68361c: ldur            x0, [x2, #7]
    // 0x683620: neg             x1, x0
    // 0x683624: sub             x0, x1, #1
    // 0x683628: mov             x5, x0
    // 0x68362c: ldur            x4, [fp, #-0x20]
    // 0x683630: LoadField: r6 = r4->field_2b
    //     0x683630: ldur            w6, [x4, #0x2b]
    // 0x683634: DecompressPointer r6
    //     0x683634: add             x6, x6, HEAP, lsl #32
    // 0x683638: r0 = BoxInt64Instr(r5)
    //     0x683638: sbfiz           x0, x5, #1, #0x1f
    //     0x68363c: cmp             x5, x0, asr #1
    //     0x683640: b.eq            #0x68364c
    //     0x683644: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x683648: stur            x5, [x0, #7]
    // 0x68364c: stur            x0, [fp, #-0x30]
    // 0x683650: cmp             w0, w6
    // 0x683654: b.eq            #0x683690
    // 0x683658: and             w16, w0, w6
    // 0x68365c: branchIfSmi(r16, 0x6836b4)
    //     0x68365c: tbz             w16, #0, #0x6836b4
    // 0x683660: r16 = LoadClassIdInstr(r0)
    //     0x683660: ldur            x16, [x0, #-1]
    //     0x683664: ubfx            x16, x16, #0xc, #0x14
    // 0x683668: cmp             x16, #0x3d
    // 0x68366c: b.ne            #0x6836b4
    // 0x683670: r16 = LoadClassIdInstr(r6)
    //     0x683670: ldur            x16, [x6, #-1]
    //     0x683674: ubfx            x16, x16, #0xc, #0x14
    // 0x683678: cmp             x16, #0x3d
    // 0x68367c: b.ne            #0x6836b4
    // 0x683680: LoadField: r16 = r0->field_7
    //     0x683680: ldur            x16, [x0, #7]
    // 0x683684: LoadField: r17 = r6->field_7
    //     0x683684: ldur            x17, [x6, #7]
    // 0x683688: cmp             x16, x17
    // 0x68368c: b.ne            #0x6836b4
    // 0x683690: ldur            x1, [fp, #-0x28]
    // 0x683694: LoadField: r0 = r1->field_43
    //     0x683694: ldur            w0, [x1, #0x43]
    // 0x683698: DecompressPointer r0
    //     0x683698: add             x0, x0, HEAP, lsl #32
    // 0x68369c: r16 = Sentinel
    //     0x68369c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6836a0: cmp             w0, w16
    // 0x6836a4: b.eq            #0x683b08
    // 0x6836a8: LeaveFrame
    //     0x6836a8: mov             SP, fp
    //     0x6836ac: ldp             fp, lr, [SP], #0x10
    // 0x6836b0: ret
    //     0x6836b0: ret             
    // 0x6836b4: ldur            x1, [fp, #-0x28]
    // 0x6836b8: ldur            x5, [fp, #-0x18]
    // 0x6836bc: LoadField: r6 = r5->field_f
    //     0x6836bc: ldur            w6, [x5, #0xf]
    // 0x6836c0: DecompressPointer r6
    //     0x6836c0: add             x6, x6, HEAP, lsl #32
    // 0x6836c4: stur            x6, [fp, #-0x10]
    // 0x6836c8: LoadField: r7 = r6->field_7
    //     0x6836c8: ldur            w7, [x6, #7]
    // 0x6836cc: DecompressPointer r7
    //     0x6836cc: add             x7, x7, HEAP, lsl #32
    // 0x6836d0: cmp             w7, NULL
    // 0x6836d4: b.eq            #0x683b10
    // 0x6836d8: LoadField: r8 = r7->field_7
    //     0x6836d8: ldur            x8, [x7, #7]
    // 0x6836dc: ldr             x7, [x8]
    // 0x6836e0: stur            x7, [fp, #-8]
    // 0x6836e4: cbnz            x7, #0x6836f4
    // 0x6836e8: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x6836e8: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x6836ec: str             x16, [SP]
    // 0x6836f0: r0 = _throwNew()
    //     0x6836f0: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x6836f4: ldur            x0, [fp, #-0x40]
    // 0x6836f8: ldur            x2, [fp, #-8]
    // 0x6836fc: stur            x2, [fp, #-8]
    // 0x683700: r1 = <Never>
    //     0x683700: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x683704: r0 = Pointer()
    //     0x683704: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x683708: mov             x1, x0
    // 0x68370c: ldur            x0, [fp, #-8]
    // 0x683710: StoreField: r1->field_7 = r0
    //     0x683710: stur            x0, [x1, #7]
    // 0x683714: ldur            x0, [fp, #-0x40]
    // 0x683718: r4 = LoadInt32Instr(r0)
    //     0x683718: sbfx            x4, x0, #1, #0x1f
    //     0x68371c: tbz             w0, #0, #0x683724
    //     0x683720: ldur            x4, [x0, #7]
    // 0x683724: mov             x2, x4
    // 0x683728: stur            x4, [fp, #-8]
    // 0x68372c: r3 = Closure: (double, double, double, double, int, int, bool) => GlyphInfo from Function 'GlyphInfo._@17065589': static.
    //     0x68372c: ldr             x3, [PP, #0x4888]  ; [pp+0x4888] Closure: (double, double, double, double, int, int, bool) => GlyphInfo from Function 'GlyphInfo._@17065589': static. (0x7e54fb0847a4)
    // 0x683730: r0 = __getGlyphInfoAt$Method$FfiNative()
    //     0x683730: bl              #0x683df4  ; [dart:ui] _NativeParagraph::__getGlyphInfoAt$Method$FfiNative
    // 0x683734: stur            x0, [fp, #-0x40]
    // 0x683738: cmp             w0, NULL
    // 0x68373c: b.ne            #0x683808
    // 0x683740: ldur            x1, [fp, #-0x28]
    // 0x683744: r0 = _getOrCreateLayoutTemplate()
    //     0x683744: bl              #0x681108  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_getOrCreateLayoutTemplate
    // 0x683748: stur            x0, [fp, #-0x10]
    // 0x68374c: LoadField: r1 = r0->field_7
    //     0x68374c: ldur            w1, [x0, #7]
    // 0x683750: DecompressPointer r1
    //     0x683750: add             x1, x1, HEAP, lsl #32
    // 0x683754: cmp             w1, NULL
    // 0x683758: b.eq            #0x683b14
    // 0x68375c: LoadField: r2 = r1->field_7
    //     0x68375c: ldur            x2, [x1, #7]
    // 0x683760: ldr             x1, [x2]
    // 0x683764: stur            x1, [fp, #-0x48]
    // 0x683768: cbnz            x1, #0x683778
    // 0x68376c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x68376c: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x683770: str             x16, [SP]
    // 0x683774: r0 = _throwNew()
    //     0x683774: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x683778: ldur            x0, [fp, #-0x48]
    // 0x68377c: stur            x0, [fp, #-0x48]
    // 0x683780: r1 = <Never>
    //     0x683780: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x683784: r0 = Pointer()
    //     0x683784: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x683788: mov             x1, x0
    // 0x68378c: ldur            x0, [fp, #-0x48]
    // 0x683790: StoreField: r1->field_7 = r0
    //     0x683790: stur            x0, [x1, #7]
    // 0x683794: r2 = 0
    //     0x683794: movz            x2, #0
    // 0x683798: r3 = Closure: (bool, double, double, double, double, double, double, double, int) => LineMetrics from Function 'LineMetrics._@17065589': static.
    //     0x683798: ldr             x3, [PP, #0x4890]  ; [pp+0x4890] Closure: (bool, double, double, double, double, double, double, double, int) => LineMetrics from Function 'LineMetrics._@17065589': static. (0x7e54fb0846ec)
    // 0x68379c: r0 = __getLineMetricsAt$Method$FfiNative()
    //     0x68379c: bl              #0x683c00  ; [dart:ui] _NativeParagraph::__getLineMetricsAt$Method$FfiNative
    // 0x6837a0: cmp             w0, NULL
    // 0x6837a4: b.eq            #0x683b18
    // 0x6837a8: LoadField: d0 = r0->field_3b
    //     0x6837a8: ldur            d0, [x0, #0x3b]
    // 0x6837ac: ldur            x1, [fp, #-0x18]
    // 0x6837b0: stur            d0, [fp, #-0x68]
    // 0x6837b4: LoadField: r0 = r1->field_13
    //     0x6837b4: ldur            w0, [x1, #0x13]
    // 0x6837b8: DecompressPointer r0
    //     0x6837b8: add             x0, x0, HEAP, lsl #32
    // 0x6837bc: r16 = Sentinel
    //     0x6837bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6837c0: cmp             w0, w16
    // 0x6837c4: b.ne            #0x6837d0
    // 0x6837c8: r2 = _endOfTextCaretMetrics
    //     0x6837c8: ldr             x2, [PP, #0x4898]  ; [pp+0x4898] Field <_TextLayout@663105366._endOfTextCaretMetrics@663105366>: late final (offset: 0x14)
    // 0x6837cc: r0 = InitLateFinalInstanceField()
    //     0x6837cc: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x6837d0: ldur            d0, [fp, #-0x68]
    // 0x6837d4: stur            x0, [fp, #-0x10]
    // 0x6837d8: fneg            d1, d0
    // 0x6837dc: stur            d1, [fp, #-0x70]
    // 0x6837e0: r0 = Offset()
    //     0x6837e0: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x6837e4: StoreField: r0->field_7 = rZR
    //     0x6837e4: stur            xzr, [x0, #7]
    // 0x6837e8: ldur            d0, [fp, #-0x70]
    // 0x6837ec: StoreField: r0->field_f = d0
    //     0x6837ec: stur            d0, [x0, #0xf]
    // 0x6837f0: ldur            x1, [fp, #-0x10]
    // 0x6837f4: mov             x2, x0
    // 0x6837f8: r0 = shift()
    //     0x6837f8: bl              #0x683b5c  ; [package:flutter/src/painting/text_painter.dart] _LineCaretMetrics::shift
    // 0x6837fc: LeaveFrame
    //     0x6837fc: mov             SP, fp
    //     0x683800: ldp             fp, lr, [SP], #0x10
    // 0x683804: ret
    //     0x683804: ret             
    // 0x683808: LoadField: r1 = r0->field_b
    //     0x683808: ldur            w1, [x0, #0xb]
    // 0x68380c: DecompressPointer r1
    //     0x68380c: add             x1, x1, HEAP, lsl #32
    // 0x683810: LoadField: r2 = r1->field_7
    //     0x683810: ldur            x2, [x1, #7]
    // 0x683814: stur            x2, [fp, #-0x58]
    // 0x683818: LoadField: r3 = r1->field_f
    //     0x683818: ldur            x3, [x1, #0xf]
    // 0x68381c: stur            x3, [fp, #-0x50]
    // 0x683820: cmp             x2, x3
    // 0x683824: b.ne            #0x683864
    // 0x683828: ldur            x1, [fp, #-8]
    // 0x68382c: add             x0, x1, #1
    // 0x683830: stur            x0, [fp, #-0x48]
    // 0x683834: r0 = TextPosition()
    //     0x683834: bl              #0x683b2c  ; AllocateTextPositionStub -> TextPosition (size=0x14)
    // 0x683838: mov             x1, x0
    // 0x68383c: ldur            x0, [fp, #-0x48]
    // 0x683840: StoreField: r1->field_7 = r0
    //     0x683840: stur            x0, [x1, #7]
    // 0x683844: r4 = Instance_TextAffinity
    //     0x683844: ldr             x4, [PP, #0x4878]  ; [pp+0x4878] Obj!TextAffinity@e392a1
    // 0x683848: StoreField: r1->field_f = r4
    //     0x683848: stur            w4, [x1, #0xf]
    // 0x68384c: mov             x2, x1
    // 0x683850: ldur            x1, [fp, #-0x28]
    // 0x683854: r0 = _computeCaretMetrics()
    //     0x683854: bl              #0x683454  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_computeCaretMetrics
    // 0x683858: LeaveFrame
    //     0x683858: mov             SP, fp
    //     0x68385c: ldp             fp, lr, [SP], #0x10
    // 0x683860: ret
    //     0x683860: ret             
    // 0x683864: ldur            x5, [fp, #-0x38]
    // 0x683868: ldur            x1, [fp, #-8]
    // 0x68386c: r4 = Instance_TextAffinity
    //     0x68386c: ldr             x4, [PP, #0x4878]  ; [pp+0x4878] Obj!TextAffinity@e392a1
    // 0x683870: tbnz            w5, #4, #0x6838a8
    // 0x683874: cmp             x2, x1
    // 0x683878: b.eq            #0x6838a8
    // 0x68387c: r0 = TextPosition()
    //     0x68387c: bl              #0x683b2c  ; AllocateTextPositionStub -> TextPosition (size=0x14)
    // 0x683880: ldur            x3, [fp, #-0x50]
    // 0x683884: StoreField: r0->field_7 = r3
    //     0x683884: stur            x3, [x0, #7]
    // 0x683888: r1 = Instance_TextAffinity
    //     0x683888: ldr             x1, [PP, #0x4878]  ; [pp+0x4878] Obj!TextAffinity@e392a1
    // 0x68388c: StoreField: r0->field_f = r1
    //     0x68388c: stur            w1, [x0, #0xf]
    // 0x683890: ldur            x1, [fp, #-0x28]
    // 0x683894: mov             x2, x0
    // 0x683898: r0 = _computeCaretMetrics()
    //     0x683898: bl              #0x683454  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_computeCaretMetrics
    // 0x68389c: LeaveFrame
    //     0x68389c: mov             SP, fp
    //     0x6838a0: ldp             fp, lr, [SP], #0x10
    // 0x6838a4: ret
    //     0x6838a4: ret             
    // 0x6838a8: ldur            x1, [fp, #-0x18]
    // 0x6838ac: LoadField: r4 = r1->field_f
    //     0x6838ac: ldur            w4, [x1, #0xf]
    // 0x6838b0: DecompressPointer r4
    //     0x6838b0: add             x4, x4, HEAP, lsl #32
    // 0x6838b4: stur            x4, [fp, #-0x10]
    // 0x6838b8: LoadField: r1 = r4->field_7
    //     0x6838b8: ldur            w1, [x4, #7]
    // 0x6838bc: DecompressPointer r1
    //     0x6838bc: add             x1, x1, HEAP, lsl #32
    // 0x6838c0: cmp             w1, NULL
    // 0x6838c4: b.eq            #0x683b1c
    // 0x6838c8: LoadField: r6 = r1->field_7
    //     0x6838c8: ldur            x6, [x1, #7]
    // 0x6838cc: ldr             x1, [x6]
    // 0x6838d0: stur            x1, [fp, #-8]
    // 0x6838d4: cbnz            x1, #0x6838e4
    // 0x6838d8: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x6838d8: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x6838dc: str             x16, [SP]
    // 0x6838e0: r0 = _throwNew()
    //     0x6838e0: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x6838e4: ldur            x0, [fp, #-8]
    // 0x6838e8: stur            x0, [fp, #-8]
    // 0x6838ec: r1 = <Never>
    //     0x6838ec: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x6838f0: r0 = Pointer()
    //     0x6838f0: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x6838f4: mov             x1, x0
    // 0x6838f8: ldur            x0, [fp, #-8]
    // 0x6838fc: StoreField: r1->field_7 = r0
    //     0x6838fc: stur            x0, [x1, #7]
    // 0x683900: ldur            x2, [fp, #-0x58]
    // 0x683904: ldur            x3, [fp, #-0x50]
    // 0x683908: r5 = 5
    //     0x683908: movz            x5, #0x5
    // 0x68390c: r6 = 0
    //     0x68390c: movz            x6, #0
    // 0x683910: r0 = __getBoxesForRange$Method$FfiNative()
    //     0x683910: bl              #0x67bdcc  ; [dart:ui] _NativeParagraph::__getBoxesForRange$Method$FfiNative
    // 0x683914: ldur            x1, [fp, #-0x10]
    // 0x683918: mov             x2, x0
    // 0x68391c: r0 = _decodeTextBoxes()
    //     0x68391c: bl              #0x67badc  ; [dart:ui] _NativeParagraph::_decodeTextBoxes
    // 0x683920: LoadField: r1 = r0->field_b
    //     0x683920: ldur            w1, [x0, #0xb]
    // 0x683924: cbz             w1, #0x6839f8
    // 0x683928: ldur            x1, [fp, #-0x40]
    // 0x68392c: LoadField: r2 = r1->field_f
    //     0x68392c: ldur            w2, [x1, #0xf]
    // 0x683930: DecompressPointer r2
    //     0x683930: add             x2, x2, HEAP, lsl #32
    // 0x683934: LoadField: r1 = r2->field_7
    //     0x683934: ldur            x1, [x2, #7]
    // 0x683938: cmp             x1, #0
    // 0x68393c: b.gt            #0x683950
    // 0x683940: ldur            x2, [fp, #-0x38]
    // 0x683944: eor             x1, x2, #0x10
    // 0x683948: mov             x2, x1
    // 0x68394c: b               #0x683954
    // 0x683950: ldur            x2, [fp, #-0x38]
    // 0x683954: stur            x2, [fp, #-0x10]
    // 0x683958: tbnz            w2, #4, #0x68396c
    // 0x68395c: mov             x1, x0
    // 0x683960: r0 = first()
    //     0x683960: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x683964: mov             x1, x0
    // 0x683968: b               #0x683978
    // 0x68396c: mov             x1, x0
    // 0x683970: r0 = last()
    //     0x683970: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x683974: mov             x1, x0
    // 0x683978: ldur            x0, [fp, #-0x10]
    // 0x68397c: stur            x1, [fp, #-0x18]
    // 0x683980: tbnz            w0, #4, #0x68398c
    // 0x683984: LoadField: d0 = r1->field_7
    //     0x683984: ldur            d0, [x1, #7]
    // 0x683988: b               #0x683990
    // 0x68398c: ArrayLoad: d0 = r1[0]  ; List_8
    //     0x68398c: ldur            d0, [x1, #0x17]
    // 0x683990: stur            d0, [fp, #-0x70]
    // 0x683994: LoadField: d1 = r1->field_f
    //     0x683994: ldur            d1, [x1, #0xf]
    // 0x683998: stur            d1, [fp, #-0x68]
    // 0x68399c: r0 = Offset()
    //     0x68399c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x6839a0: ldur            d0, [fp, #-0x70]
    // 0x6839a4: stur            x0, [fp, #-0x60]
    // 0x6839a8: StoreField: r0->field_7 = d0
    //     0x6839a8: stur            d0, [x0, #7]
    // 0x6839ac: ldur            d0, [fp, #-0x68]
    // 0x6839b0: StoreField: r0->field_f = d0
    //     0x6839b0: stur            d0, [x0, #0xf]
    // 0x6839b4: ldur            x1, [fp, #-0x18]
    // 0x6839b8: LoadField: r2 = r1->field_27
    //     0x6839b8: ldur            w2, [x1, #0x27]
    // 0x6839bc: DecompressPointer r2
    //     0x6839bc: add             x2, x2, HEAP, lsl #32
    // 0x6839c0: stur            x2, [fp, #-0x10]
    // 0x6839c4: LoadField: d1 = r1->field_1f
    //     0x6839c4: ldur            d1, [x1, #0x1f]
    // 0x6839c8: fsub            d2, d1, d0
    // 0x6839cc: stur            d2, [fp, #-0x70]
    // 0x6839d0: r0 = _LineCaretMetrics()
    //     0x6839d0: bl              #0x683b20  ; Allocate_LineCaretMetricsStub -> _LineCaretMetrics (size=0x18)
    // 0x6839d4: mov             x1, x0
    // 0x6839d8: ldur            x0, [fp, #-0x60]
    // 0x6839dc: StoreField: r1->field_7 = r0
    //     0x6839dc: stur            w0, [x1, #7]
    // 0x6839e0: ldur            x0, [fp, #-0x10]
    // 0x6839e4: StoreField: r1->field_b = r0
    //     0x6839e4: stur            w0, [x1, #0xb]
    // 0x6839e8: ldur            d0, [fp, #-0x70]
    // 0x6839ec: StoreField: r1->field_f = d0
    //     0x6839ec: stur            d0, [x1, #0xf]
    // 0x6839f0: mov             x3, x1
    // 0x6839f4: b               #0x683a9c
    // 0x6839f8: ldur            x2, [fp, #-0x38]
    // 0x6839fc: ldur            x1, [fp, #-0x40]
    // 0x683a00: LoadField: r0 = r1->field_7
    //     0x683a00: ldur            w0, [x1, #7]
    // 0x683a04: DecompressPointer r0
    //     0x683a04: add             x0, x0, HEAP, lsl #32
    // 0x683a08: stur            x0, [fp, #-0x18]
    // 0x683a0c: LoadField: r3 = r1->field_f
    //     0x683a0c: ldur            w3, [x1, #0xf]
    // 0x683a10: DecompressPointer r3
    //     0x683a10: add             x3, x3, HEAP, lsl #32
    // 0x683a14: stur            x3, [fp, #-0x10]
    // 0x683a18: LoadField: r1 = r3->field_7
    //     0x683a18: ldur            x1, [x3, #7]
    // 0x683a1c: cmp             x1, #0
    // 0x683a20: b.gt            #0x683a38
    // 0x683a24: tbnz            w2, #4, #0x683a30
    // 0x683a28: ArrayLoad: d0 = r0[0]  ; List_8
    //     0x683a28: ldur            d0, [x0, #0x17]
    // 0x683a2c: b               #0x683a48
    // 0x683a30: LoadField: d0 = r0->field_7
    //     0x683a30: ldur            d0, [x0, #7]
    // 0x683a34: b               #0x683a48
    // 0x683a38: tbnz            w2, #4, #0x683a44
    // 0x683a3c: LoadField: d0 = r0->field_7
    //     0x683a3c: ldur            d0, [x0, #7]
    // 0x683a40: b               #0x683a48
    // 0x683a44: ArrayLoad: d0 = r0[0]  ; List_8
    //     0x683a44: ldur            d0, [x0, #0x17]
    // 0x683a48: stur            d0, [fp, #-0x70]
    // 0x683a4c: LoadField: d1 = r0->field_f
    //     0x683a4c: ldur            d1, [x0, #0xf]
    // 0x683a50: stur            d1, [fp, #-0x68]
    // 0x683a54: r0 = Offset()
    //     0x683a54: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x683a58: ldur            d0, [fp, #-0x70]
    // 0x683a5c: stur            x0, [fp, #-0x38]
    // 0x683a60: StoreField: r0->field_7 = d0
    //     0x683a60: stur            d0, [x0, #7]
    // 0x683a64: ldur            d0, [fp, #-0x68]
    // 0x683a68: StoreField: r0->field_f = d0
    //     0x683a68: stur            d0, [x0, #0xf]
    // 0x683a6c: ldur            x1, [fp, #-0x18]
    // 0x683a70: LoadField: d1 = r1->field_1f
    //     0x683a70: ldur            d1, [x1, #0x1f]
    // 0x683a74: fsub            d2, d1, d0
    // 0x683a78: stur            d2, [fp, #-0x70]
    // 0x683a7c: r0 = _LineCaretMetrics()
    //     0x683a7c: bl              #0x683b20  ; Allocate_LineCaretMetricsStub -> _LineCaretMetrics (size=0x18)
    // 0x683a80: ldur            x1, [fp, #-0x38]
    // 0x683a84: StoreField: r0->field_7 = r1
    //     0x683a84: stur            w1, [x0, #7]
    // 0x683a88: ldur            x1, [fp, #-0x10]
    // 0x683a8c: StoreField: r0->field_b = r1
    //     0x683a8c: stur            w1, [x0, #0xb]
    // 0x683a90: ldur            d0, [fp, #-0x70]
    // 0x683a94: StoreField: r0->field_f = d0
    //     0x683a94: stur            d0, [x0, #0xf]
    // 0x683a98: mov             x3, x0
    // 0x683a9c: ldur            x2, [fp, #-0x28]
    // 0x683aa0: ldur            x1, [fp, #-0x20]
    // 0x683aa4: ldur            x0, [fp, #-0x30]
    // 0x683aa8: StoreField: r1->field_2b = r0
    //     0x683aa8: stur            w0, [x1, #0x2b]
    //     0x683aac: tbz             w0, #0, #0x683ac8
    //     0x683ab0: ldurb           w16, [x1, #-1]
    //     0x683ab4: ldurb           w17, [x0, #-1]
    //     0x683ab8: and             x16, x17, x16, lsr #2
    //     0x683abc: tst             x16, HEAP, lsr #32
    //     0x683ac0: b.eq            #0x683ac8
    //     0x683ac4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x683ac8: mov             x0, x3
    // 0x683acc: StoreField: r2->field_43 = r0
    //     0x683acc: stur            w0, [x2, #0x43]
    //     0x683ad0: ldurb           w16, [x2, #-1]
    //     0x683ad4: ldurb           w17, [x0, #-1]
    //     0x683ad8: and             x16, x17, x16, lsr #2
    //     0x683adc: tst             x16, HEAP, lsr #32
    //     0x683ae0: b.eq            #0x683ae8
    //     0x683ae4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x683ae8: mov             x0, x3
    // 0x683aec: LeaveFrame
    //     0x683aec: mov             SP, fp
    //     0x683af0: ldp             fp, lr, [SP], #0x10
    // 0x683af4: ret
    //     0x683af4: ret             
    // 0x683af8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x683af8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x683afc: b               #0x683474
    // 0x683b00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x683b00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x683b04: r0 = NullErrorSharedWithoutFPURegs()
    //     0x683b04: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x683b08: r9 = _caretMetrics
    //     0x683b08: ldr             x9, [PP, #0x48a0]  ; [pp+0x48a0] Field <TextPainter._caretMetrics@663105366>: late (offset: 0x44)
    // 0x683b0c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x683b0c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x683b10: r0 = NullErrorSharedWithoutFPURegs()
    //     0x683b10: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x683b14: r0 = NullErrorSharedWithoutFPURegs()
    //     0x683b14: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x683b18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x683b18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x683b1c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x683b1c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _isNewlineAtOffset(/* No info */) {
    // ** addr: 0x683fe8, size: 0x108
    // 0x683fe8: EnterFrame
    //     0x683fe8: stp             fp, lr, [SP, #-0x10]!
    //     0x683fec: mov             fp, SP
    // 0x683ff0: AllocStack(0x10)
    //     0x683ff0: sub             SP, SP, #0x10
    // 0x683ff4: SetupParameters(TextPainter this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x683ff4: mov             x0, x2
    //     0x683ff8: stur            x2, [fp, #-0x10]
    //     0x683ffc: mov             x2, x1
    //     0x684000: stur            x1, [fp, #-8]
    // 0x684004: CheckStackOverflow
    //     0x684004: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x684008: cmp             SP, x16
    //     0x68400c: b.ls            #0x6840e4
    // 0x684010: tbnz            x0, #0x3f, #0x6840d4
    // 0x684014: mov             x1, x2
    // 0x684018: r0 = plainText()
    //     0x684018: bl              #0x6840f0  ; [package:flutter/src/painting/text_painter.dart] TextPainter::plainText
    // 0x68401c: LoadField: r1 = r0->field_7
    //     0x68401c: ldur            w1, [x0, #7]
    // 0x684020: r0 = LoadInt32Instr(r1)
    //     0x684020: sbfx            x0, x1, #1, #0x1f
    // 0x684024: ldur            x2, [fp, #-0x10]
    // 0x684028: cmp             x2, x0
    // 0x68402c: b.ge            #0x6840d4
    // 0x684030: ldur            x1, [fp, #-8]
    // 0x684034: r0 = plainText()
    //     0x684034: bl              #0x6840f0  ; [package:flutter/src/painting/text_painter.dart] TextPainter::plainText
    // 0x684038: mov             x2, x0
    // 0x68403c: LoadField: r3 = r2->field_7
    //     0x68403c: ldur            w3, [x2, #7]
    // 0x684040: r0 = LoadInt32Instr(r3)
    //     0x684040: sbfx            x0, x3, #1, #0x1f
    // 0x684044: ldur            x1, [fp, #-0x10]
    // 0x684048: cmp             x1, x0
    // 0x68404c: b.hs            #0x6840ec
    // 0x684050: r1 = LoadClassIdInstr(r2)
    //     0x684050: ldur            x1, [x2, #-1]
    //     0x684054: ubfx            x1, x1, #0xc, #0x14
    // 0x684058: lsl             x1, x1, #1
    // 0x68405c: cmp             w1, #0xbc
    // 0x684060: b.ne            #0x684078
    // 0x684064: ldur            x1, [fp, #-0x10]
    // 0x684068: ArrayLoad: r3 = r2[r1]  ; TypedUnsigned_1
    //     0x684068: add             x16, x2, x1
    //     0x68406c: ldrb            w3, [x16, #0xf]
    // 0x684070: mov             x1, x3
    // 0x684074: b               #0x684088
    // 0x684078: ldur            x1, [fp, #-0x10]
    // 0x68407c: add             x16, x2, x1, lsl #1
    // 0x684080: ldurh           w3, [x16, #0xf]
    // 0x684084: mov             x1, x3
    // 0x684088: cmp             x1, #0xa
    // 0x68408c: b.eq            #0x6840c0
    // 0x684090: cmp             x1, #0x85
    // 0x684094: b.eq            #0x6840c0
    // 0x684098: cmp             x1, #0xb
    // 0x68409c: b.eq            #0x6840c0
    // 0x6840a0: cmp             x1, #0xc
    // 0x6840a4: b.eq            #0x6840c0
    // 0x6840a8: r17 = 8232
    //     0x6840a8: movz            x17, #0x2028
    // 0x6840ac: cmp             x1, x17
    // 0x6840b0: b.eq            #0x6840c0
    // 0x6840b4: r17 = 8233
    //     0x6840b4: movz            x17, #0x2029
    // 0x6840b8: cmp             x1, x17
    // 0x6840bc: b.ne            #0x6840c8
    // 0x6840c0: r1 = true
    //     0x6840c0: add             x1, NULL, #0x20  ; true
    // 0x6840c4: b               #0x6840cc
    // 0x6840c8: r1 = false
    //     0x6840c8: add             x1, NULL, #0x30  ; false
    // 0x6840cc: mov             x0, x1
    // 0x6840d0: b               #0x6840d8
    // 0x6840d4: r0 = false
    //     0x6840d4: add             x0, NULL, #0x30  ; false
    // 0x6840d8: LeaveFrame
    //     0x6840d8: mov             SP, fp
    //     0x6840dc: ldp             fp, lr, [SP], #0x10
    // 0x6840e0: ret
    //     0x6840e0: ret             
    // 0x6840e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6840e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6840e8: b               #0x684010
    // 0x6840ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6840ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ plainText(/* No info */) {
    // ** addr: 0x6840f0, size: 0xa0
    // 0x6840f0: EnterFrame
    //     0x6840f0: stp             fp, lr, [SP, #-0x10]!
    //     0x6840f4: mov             fp, SP
    // 0x6840f8: AllocStack(0x8)
    //     0x6840f8: sub             SP, SP, #8
    // 0x6840fc: SetupParameters(TextPainter this /* r1 => r0, fp-0x8 */)
    //     0x6840fc: mov             x0, x1
    //     0x684100: stur            x1, [fp, #-8]
    // 0x684104: CheckStackOverflow
    //     0x684104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x684108: cmp             SP, x16
    //     0x68410c: b.ls            #0x684188
    // 0x684110: LoadField: r1 = r0->field_13
    //     0x684110: ldur            w1, [x0, #0x13]
    // 0x684114: DecompressPointer r1
    //     0x684114: add             x1, x1, HEAP, lsl #32
    // 0x684118: cmp             w1, NULL
    // 0x68411c: b.ne            #0x684168
    // 0x684120: LoadField: r1 = r0->field_f
    //     0x684120: ldur            w1, [x0, #0xf]
    // 0x684124: DecompressPointer r1
    //     0x684124: add             x1, x1, HEAP, lsl #32
    // 0x684128: cmp             w1, NULL
    // 0x68412c: b.ne            #0x68413c
    // 0x684130: mov             x2, x0
    // 0x684134: r1 = Null
    //     0x684134: mov             x1, NULL
    // 0x684138: b               #0x684148
    // 0x68413c: r0 = toPlainText()
    //     0x68413c: bl              #0x684190  ; [package:flutter/src/painting/inline_span.dart] InlineSpan::toPlainText
    // 0x684140: mov             x1, x0
    // 0x684144: ldur            x2, [fp, #-8]
    // 0x684148: mov             x0, x1
    // 0x68414c: StoreField: r2->field_13 = r0
    //     0x68414c: stur            w0, [x2, #0x13]
    //     0x684150: ldurb           w16, [x2, #-1]
    //     0x684154: ldurb           w17, [x0, #-1]
    //     0x684158: and             x16, x17, x16, lsr #2
    //     0x68415c: tst             x16, HEAP, lsr #32
    //     0x684160: b.eq            #0x684168
    //     0x684164: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x684168: cmp             w1, NULL
    // 0x68416c: b.ne            #0x684178
    // 0x684170: r0 = ""
    //     0x684170: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x684174: b               #0x68417c
    // 0x684178: mov             x0, x1
    // 0x68417c: LeaveFrame
    //     0x68417c: mov             SP, fp
    //     0x684180: ldp             fp, lr, [SP], #0x10
    // 0x684184: ret
    //     0x684184: ret             
    // 0x684188: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x684188: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68418c: b               #0x684110
  }
  _ getOffsetForCaret(/* No info */) {
    // ** addr: 0x684b7c, size: 0x1e0
    // 0x684b7c: EnterFrame
    //     0x684b7c: stp             fp, lr, [SP, #-0x10]!
    //     0x684b80: mov             fp, SP
    // 0x684b84: AllocStack(0x30)
    //     0x684b84: sub             SP, SP, #0x30
    // 0x684b88: SetupParameters(TextPainter this /* r1 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x684b88: mov             x0, x1
    //     0x684b8c: stur            x1, [fp, #-0x10]
    //     0x684b90: stur            x3, [fp, #-0x18]
    // 0x684b94: CheckStackOverflow
    //     0x684b94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x684b98: cmp             SP, x16
    //     0x684b9c: b.ls            #0x684d4c
    // 0x684ba0: LoadField: r4 = r0->field_7
    //     0x684ba0: ldur            w4, [x0, #7]
    // 0x684ba4: DecompressPointer r4
    //     0x684ba4: add             x4, x4, HEAP, lsl #32
    // 0x684ba8: stur            x4, [fp, #-8]
    // 0x684bac: cmp             w4, NULL
    // 0x684bb0: b.eq            #0x684d54
    // 0x684bb4: mov             x1, x0
    // 0x684bb8: r0 = _computeCaretMetrics()
    //     0x684bb8: bl              #0x683454  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_computeCaretMetrics
    // 0x684bbc: cmp             w0, NULL
    // 0x684bc0: b.ne            #0x684c2c
    // 0x684bc4: ldur            x0, [fp, #-0x10]
    // 0x684bc8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x684bc8: ldur            w1, [x0, #0x17]
    // 0x684bcc: DecompressPointer r1
    //     0x684bcc: add             x1, x1, HEAP, lsl #32
    // 0x684bd0: LoadField: r2 = r0->field_1b
    //     0x684bd0: ldur            w2, [x0, #0x1b]
    // 0x684bd4: DecompressPointer r2
    //     0x684bd4: add             x2, x2, HEAP, lsl #32
    // 0x684bd8: cmp             w2, NULL
    // 0x684bdc: b.eq            #0x684d58
    // 0x684be0: r0 = _computePaintOffsetFraction()
    //     0x684be0: bl              #0x67e3ec  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_computePaintOffsetFraction
    // 0x684be4: mov             v1.16b, v0.16b
    // 0x684be8: d0 = 0.000000
    //     0x684be8: eor             v0.16b, v0.16b, v0.16b
    // 0x684bec: fcmp            d1, d0
    // 0x684bf0: b.ne            #0x684bfc
    // 0x684bf4: d0 = 0.000000
    //     0x684bf4: eor             v0.16b, v0.16b, v0.16b
    // 0x684bf8: b               #0x684c0c
    // 0x684bfc: ldur            x1, [fp, #-8]
    // 0x684c00: LoadField: d0 = r1->field_13
    //     0x684c00: ldur            d0, [x1, #0x13]
    // 0x684c04: fmul            d2, d1, d0
    // 0x684c08: mov             v0.16b, v2.16b
    // 0x684c0c: stur            d0, [fp, #-0x20]
    // 0x684c10: r0 = Offset()
    //     0x684c10: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x684c14: ldur            d0, [fp, #-0x20]
    // 0x684c18: StoreField: r0->field_7 = d0
    //     0x684c18: stur            d0, [x0, #7]
    // 0x684c1c: StoreField: r0->field_f = rZR
    //     0x684c1c: stur            xzr, [x0, #0xf]
    // 0x684c20: LeaveFrame
    //     0x684c20: mov             SP, fp
    //     0x684c24: ldp             fp, lr, [SP], #0x10
    // 0x684c28: ret
    //     0x684c28: ret             
    // 0x684c2c: ldur            x1, [fp, #-8]
    // 0x684c30: d0 = 0.000000
    //     0x684c30: eor             v0.16b, v0.16b, v0.16b
    // 0x684c34: LoadField: r2 = r0->field_b
    //     0x684c34: ldur            w2, [x0, #0xb]
    // 0x684c38: DecompressPointer r2
    //     0x684c38: add             x2, x2, HEAP, lsl #32
    // 0x684c3c: r16 = Instance_TextDirection
    //     0x684c3c: ldr             x16, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0x684c40: cmp             w2, w16
    // 0x684c44: b.ne            #0x684c58
    // 0x684c48: LoadField: r2 = r0->field_7
    //     0x684c48: ldur            w2, [x0, #7]
    // 0x684c4c: DecompressPointer r2
    //     0x684c4c: add             x2, x2, HEAP, lsl #32
    // 0x684c50: mov             x0, x1
    // 0x684c54: b               #0x684cb8
    // 0x684c58: r16 = Instance_TextDirection
    //     0x684c58: ldr             x16, [PP, #0x2898]  ; [pp+0x2898] Obj!TextDirection@e392e1
    // 0x684c5c: cmp             w2, w16
    // 0x684c60: b.ne            #0x684cb0
    // 0x684c64: ldur            x2, [fp, #-0x18]
    // 0x684c68: LoadField: r3 = r0->field_7
    //     0x684c68: ldur            w3, [x0, #7]
    // 0x684c6c: DecompressPointer r3
    //     0x684c6c: add             x3, x3, HEAP, lsl #32
    // 0x684c70: LoadField: d1 = r3->field_7
    //     0x684c70: ldur            d1, [x3, #7]
    // 0x684c74: ArrayLoad: d2 = r2[0]  ; List_8
    //     0x684c74: ldur            d2, [x2, #0x17]
    // 0x684c78: LoadField: d3 = r2->field_7
    //     0x684c78: ldur            d3, [x2, #7]
    // 0x684c7c: fsub            d4, d2, d3
    // 0x684c80: fsub            d2, d1, d4
    // 0x684c84: stur            d2, [fp, #-0x28]
    // 0x684c88: LoadField: d1 = r3->field_f
    //     0x684c88: ldur            d1, [x3, #0xf]
    // 0x684c8c: stur            d1, [fp, #-0x20]
    // 0x684c90: r0 = Offset()
    //     0x684c90: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x684c94: ldur            d0, [fp, #-0x28]
    // 0x684c98: StoreField: r0->field_7 = d0
    //     0x684c98: stur            d0, [x0, #7]
    // 0x684c9c: ldur            d0, [fp, #-0x20]
    // 0x684ca0: StoreField: r0->field_f = d0
    //     0x684ca0: stur            d0, [x0, #0xf]
    // 0x684ca4: mov             x2, x0
    // 0x684ca8: ldur            x0, [fp, #-8]
    // 0x684cac: b               #0x684cb8
    // 0x684cb0: ldur            x0, [fp, #-8]
    // 0x684cb4: r2 = Null
    //     0x684cb4: mov             x2, NULL
    // 0x684cb8: stur            x2, [fp, #-0x10]
    // 0x684cbc: LoadField: d0 = r2->field_7
    //     0x684cbc: ldur            d0, [x2, #7]
    // 0x684cc0: mov             x1, x0
    // 0x684cc4: stur            d0, [fp, #-0x20]
    // 0x684cc8: r0 = paintOffset()
    //     0x684cc8: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0x684ccc: LoadField: d0 = r0->field_7
    //     0x684ccc: ldur            d0, [x0, #7]
    // 0x684cd0: ldur            d1, [fp, #-0x20]
    // 0x684cd4: fadd            d2, d1, d0
    // 0x684cd8: ldur            x1, [fp, #-8]
    // 0x684cdc: LoadField: d0 = r1->field_13
    //     0x684cdc: ldur            d0, [x1, #0x13]
    // 0x684ce0: d1 = 0.000000
    //     0x684ce0: eor             v1.16b, v1.16b, v1.16b
    // 0x684ce4: fcmp            d1, d2
    // 0x684ce8: b.le            #0x684cf4
    // 0x684cec: d0 = 0.000000
    //     0x684cec: eor             v0.16b, v0.16b, v0.16b
    // 0x684cf0: b               #0x684d08
    // 0x684cf4: fcmp            d2, d0
    // 0x684cf8: b.gt            #0x684d08
    // 0x684cfc: fcmp            d2, d2
    // 0x684d00: b.vs            #0x684d08
    // 0x684d04: mov             v0.16b, v2.16b
    // 0x684d08: ldur            x0, [fp, #-0x10]
    // 0x684d0c: stur            d0, [fp, #-0x28]
    // 0x684d10: LoadField: d1 = r0->field_f
    //     0x684d10: ldur            d1, [x0, #0xf]
    // 0x684d14: stur            d1, [fp, #-0x20]
    // 0x684d18: r0 = paintOffset()
    //     0x684d18: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0x684d1c: LoadField: d0 = r0->field_f
    //     0x684d1c: ldur            d0, [x0, #0xf]
    // 0x684d20: ldur            d1, [fp, #-0x20]
    // 0x684d24: fadd            d2, d1, d0
    // 0x684d28: stur            d2, [fp, #-0x30]
    // 0x684d2c: r0 = Offset()
    //     0x684d2c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x684d30: ldur            d0, [fp, #-0x28]
    // 0x684d34: StoreField: r0->field_7 = d0
    //     0x684d34: stur            d0, [x0, #7]
    // 0x684d38: ldur            d0, [fp, #-0x30]
    // 0x684d3c: StoreField: r0->field_f = d0
    //     0x684d3c: stur            d0, [x0, #0xf]
    // 0x684d40: LeaveFrame
    //     0x684d40: mov             SP, fp
    //     0x684d44: ldp             fp, lr, [SP], #0x10
    // 0x684d48: ret
    //     0x684d48: ret             
    // 0x684d4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x684d4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x684d50: b               #0x684ba0
    // 0x684d54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x684d54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x684d58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x684d58: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ preferredLineHeight(/* No info */) {
    // ** addr: 0x684fa8, size: 0x84
    // 0x684fa8: EnterFrame
    //     0x684fa8: stp             fp, lr, [SP, #-0x10]!
    //     0x684fac: mov             fp, SP
    // 0x684fb0: AllocStack(0x18)
    //     0x684fb0: sub             SP, SP, #0x18
    // 0x684fb4: CheckStackOverflow
    //     0x684fb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x684fb8: cmp             SP, x16
    //     0x684fbc: b.ls            #0x685020
    // 0x684fc0: r0 = _getOrCreateLayoutTemplate()
    //     0x684fc0: bl              #0x681108  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_getOrCreateLayoutTemplate
    // 0x684fc4: stur            x0, [fp, #-0x10]
    // 0x684fc8: LoadField: r1 = r0->field_7
    //     0x684fc8: ldur            w1, [x0, #7]
    // 0x684fcc: DecompressPointer r1
    //     0x684fcc: add             x1, x1, HEAP, lsl #32
    // 0x684fd0: cmp             w1, NULL
    // 0x684fd4: b.eq            #0x685028
    // 0x684fd8: LoadField: r2 = r1->field_7
    //     0x684fd8: ldur            x2, [x1, #7]
    // 0x684fdc: ldr             x1, [x2]
    // 0x684fe0: stur            x1, [fp, #-8]
    // 0x684fe4: cbnz            x1, #0x684ff4
    // 0x684fe8: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x684fe8: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x684fec: str             x16, [SP]
    // 0x684ff0: r0 = _throwNew()
    //     0x684ff0: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x684ff4: ldur            x0, [fp, #-8]
    // 0x684ff8: stur            x0, [fp, #-8]
    // 0x684ffc: r1 = <Never>
    //     0x684ffc: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x685000: r0 = Pointer()
    //     0x685000: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x685004: mov             x1, x0
    // 0x685008: ldur            x0, [fp, #-8]
    // 0x68500c: StoreField: r1->field_7 = r0
    //     0x68500c: stur            x0, [x1, #7]
    // 0x685010: r0 = _height$Getter$FfiNative()
    //     0x685010: bl              #0x68502c  ; [dart:ui] _NativeParagraph::_height$Getter$FfiNative
    // 0x685014: LeaveFrame
    //     0x685014: mov             SP, fp
    //     0x685018: ldp             fp, lr, [SP], #0x10
    // 0x68501c: ret
    //     0x68501c: ret             
    // 0x685020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x685020: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x685024: b               #0x684fc0
    // 0x685028: r0 = NullErrorSharedWithoutFPURegs()
    //     0x685028: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  get _ size(/* No info */) {
    // ** addr: 0x686144, size: 0x64
    // 0x686144: EnterFrame
    //     0x686144: stp             fp, lr, [SP, #-0x10]!
    //     0x686148: mov             fp, SP
    // 0x68614c: AllocStack(0x10)
    //     0x68614c: sub             SP, SP, #0x10
    // 0x686150: CheckStackOverflow
    //     0x686150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x686154: cmp             SP, x16
    //     0x686158: b.ls            #0x68619c
    // 0x68615c: LoadField: r0 = r1->field_7
    //     0x68615c: ldur            w0, [x1, #7]
    // 0x686160: DecompressPointer r0
    //     0x686160: add             x0, x0, HEAP, lsl #32
    // 0x686164: cmp             w0, NULL
    // 0x686168: b.eq            #0x6861a4
    // 0x68616c: LoadField: d0 = r0->field_13
    //     0x68616c: ldur            d0, [x0, #0x13]
    // 0x686170: stur            d0, [fp, #-8]
    // 0x686174: r0 = height()
    //     0x686174: bl              #0x6861a8  ; [package:flutter/src/painting/text_painter.dart] TextPainter::height
    // 0x686178: stur            d0, [fp, #-0x10]
    // 0x68617c: r0 = Size()
    //     0x68617c: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x686180: ldur            d0, [fp, #-8]
    // 0x686184: StoreField: r0->field_7 = d0
    //     0x686184: stur            d0, [x0, #7]
    // 0x686188: ldur            d0, [fp, #-0x10]
    // 0x68618c: StoreField: r0->field_f = d0
    //     0x68618c: stur            d0, [x0, #0xf]
    // 0x686190: LeaveFrame
    //     0x686190: mov             SP, fp
    //     0x686194: ldp             fp, lr, [SP], #0x10
    // 0x686198: ret
    //     0x686198: ret             
    // 0x68619c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68619c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6861a0: b               #0x68615c
    // 0x6861a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6861a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ height(/* No info */) {
    // ** addr: 0x6861a8, size: 0xa4
    // 0x6861a8: EnterFrame
    //     0x6861a8: stp             fp, lr, [SP, #-0x10]!
    //     0x6861ac: mov             fp, SP
    // 0x6861b0: AllocStack(0x18)
    //     0x6861b0: sub             SP, SP, #0x18
    // 0x6861b4: CheckStackOverflow
    //     0x6861b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6861b8: cmp             SP, x16
    //     0x6861bc: b.ls            #0x68623c
    // 0x6861c0: LoadField: r0 = r1->field_7
    //     0x6861c0: ldur            w0, [x1, #7]
    // 0x6861c4: DecompressPointer r0
    //     0x6861c4: add             x0, x0, HEAP, lsl #32
    // 0x6861c8: cmp             w0, NULL
    // 0x6861cc: b.eq            #0x686244
    // 0x6861d0: LoadField: r1 = r0->field_7
    //     0x6861d0: ldur            w1, [x0, #7]
    // 0x6861d4: DecompressPointer r1
    //     0x6861d4: add             x1, x1, HEAP, lsl #32
    // 0x6861d8: LoadField: r0 = r1->field_f
    //     0x6861d8: ldur            w0, [x1, #0xf]
    // 0x6861dc: DecompressPointer r0
    //     0x6861dc: add             x0, x0, HEAP, lsl #32
    // 0x6861e0: stur            x0, [fp, #-0x10]
    // 0x6861e4: LoadField: r1 = r0->field_7
    //     0x6861e4: ldur            w1, [x0, #7]
    // 0x6861e8: DecompressPointer r1
    //     0x6861e8: add             x1, x1, HEAP, lsl #32
    // 0x6861ec: cmp             w1, NULL
    // 0x6861f0: b.eq            #0x686248
    // 0x6861f4: LoadField: r2 = r1->field_7
    //     0x6861f4: ldur            x2, [x1, #7]
    // 0x6861f8: ldr             x1, [x2]
    // 0x6861fc: stur            x1, [fp, #-8]
    // 0x686200: cbnz            x1, #0x686210
    // 0x686204: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x686204: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x686208: str             x16, [SP]
    // 0x68620c: r0 = _throwNew()
    //     0x68620c: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x686210: ldur            x0, [fp, #-8]
    // 0x686214: stur            x0, [fp, #-8]
    // 0x686218: r1 = <Never>
    //     0x686218: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x68621c: r0 = Pointer()
    //     0x68621c: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x686220: mov             x1, x0
    // 0x686224: ldur            x0, [fp, #-8]
    // 0x686228: StoreField: r1->field_7 = r0
    //     0x686228: stur            x0, [x1, #7]
    // 0x68622c: r0 = _height$Getter$FfiNative()
    //     0x68622c: bl              #0x68502c  ; [dart:ui] _NativeParagraph::_height$Getter$FfiNative
    // 0x686230: LeaveFrame
    //     0x686230: mov             SP, fp
    //     0x686234: ldp             fp, lr, [SP], #0x10
    // 0x686238: ret
    //     0x686238: ret             
    // 0x68623c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68623c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x686240: b               #0x6861c0
    // 0x686244: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x686244: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x686248: r0 = NullErrorSharedWithoutFPURegs()
    //     0x686248: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ getPositionForOffset(/* No info */) {
    // ** addr: 0x6a9664, size: 0x7c
    // 0x6a9664: EnterFrame
    //     0x6a9664: stp             fp, lr, [SP, #-0x10]!
    //     0x6a9668: mov             fp, SP
    // 0x6a966c: AllocStack(0x10)
    //     0x6a966c: sub             SP, SP, #0x10
    // 0x6a9670: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x6a9670: mov             x0, x2
    //     0x6a9674: stur            x2, [fp, #-0x10]
    // 0x6a9678: CheckStackOverflow
    //     0x6a9678: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a967c: cmp             SP, x16
    //     0x6a9680: b.ls            #0x6a96d4
    // 0x6a9684: LoadField: r2 = r1->field_7
    //     0x6a9684: ldur            w2, [x1, #7]
    // 0x6a9688: DecompressPointer r2
    //     0x6a9688: add             x2, x2, HEAP, lsl #32
    // 0x6a968c: cmp             w2, NULL
    // 0x6a9690: b.eq            #0x6a96dc
    // 0x6a9694: LoadField: r1 = r2->field_7
    //     0x6a9694: ldur            w1, [x2, #7]
    // 0x6a9698: DecompressPointer r1
    //     0x6a9698: add             x1, x1, HEAP, lsl #32
    // 0x6a969c: LoadField: r3 = r1->field_f
    //     0x6a969c: ldur            w3, [x1, #0xf]
    // 0x6a96a0: DecompressPointer r3
    //     0x6a96a0: add             x3, x3, HEAP, lsl #32
    // 0x6a96a4: mov             x1, x2
    // 0x6a96a8: stur            x3, [fp, #-8]
    // 0x6a96ac: r0 = paintOffset()
    //     0x6a96ac: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0x6a96b0: ldur            x1, [fp, #-0x10]
    // 0x6a96b4: mov             x2, x0
    // 0x6a96b8: r0 = -()
    //     0x6a96b8: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x6a96bc: ldur            x1, [fp, #-8]
    // 0x6a96c0: mov             x2, x0
    // 0x6a96c4: r0 = getPositionForOffset()
    //     0x6a96c4: bl              #0x6a96e0  ; [dart:ui] _NativeParagraph::getPositionForOffset
    // 0x6a96c8: LeaveFrame
    //     0x6a96c8: mov             SP, fp
    //     0x6a96cc: ldp             fp, lr, [SP], #0x10
    // 0x6a96d0: ret
    //     0x6a96d0: ret             
    // 0x6a96d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a96d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a96d8: b               #0x6a9684
    // 0x6a96dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a96dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ minIntrinsicWidth(/* No info */) {
    // ** addr: 0x72f5f0, size: 0xa4
    // 0x72f5f0: EnterFrame
    //     0x72f5f0: stp             fp, lr, [SP, #-0x10]!
    //     0x72f5f4: mov             fp, SP
    // 0x72f5f8: AllocStack(0x18)
    //     0x72f5f8: sub             SP, SP, #0x18
    // 0x72f5fc: CheckStackOverflow
    //     0x72f5fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72f600: cmp             SP, x16
    //     0x72f604: b.ls            #0x72f684
    // 0x72f608: LoadField: r0 = r1->field_7
    //     0x72f608: ldur            w0, [x1, #7]
    // 0x72f60c: DecompressPointer r0
    //     0x72f60c: add             x0, x0, HEAP, lsl #32
    // 0x72f610: cmp             w0, NULL
    // 0x72f614: b.eq            #0x72f68c
    // 0x72f618: LoadField: r1 = r0->field_7
    //     0x72f618: ldur            w1, [x0, #7]
    // 0x72f61c: DecompressPointer r1
    //     0x72f61c: add             x1, x1, HEAP, lsl #32
    // 0x72f620: LoadField: r0 = r1->field_f
    //     0x72f620: ldur            w0, [x1, #0xf]
    // 0x72f624: DecompressPointer r0
    //     0x72f624: add             x0, x0, HEAP, lsl #32
    // 0x72f628: stur            x0, [fp, #-0x10]
    // 0x72f62c: LoadField: r1 = r0->field_7
    //     0x72f62c: ldur            w1, [x0, #7]
    // 0x72f630: DecompressPointer r1
    //     0x72f630: add             x1, x1, HEAP, lsl #32
    // 0x72f634: cmp             w1, NULL
    // 0x72f638: b.eq            #0x72f690
    // 0x72f63c: LoadField: r2 = r1->field_7
    //     0x72f63c: ldur            x2, [x1, #7]
    // 0x72f640: ldr             x1, [x2]
    // 0x72f644: stur            x1, [fp, #-8]
    // 0x72f648: cbnz            x1, #0x72f658
    // 0x72f64c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x72f64c: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x72f650: str             x16, [SP]
    // 0x72f654: r0 = _throwNew()
    //     0x72f654: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x72f658: ldur            x0, [fp, #-8]
    // 0x72f65c: stur            x0, [fp, #-8]
    // 0x72f660: r1 = <Never>
    //     0x72f660: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x72f664: r0 = Pointer()
    //     0x72f664: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x72f668: mov             x1, x0
    // 0x72f66c: ldur            x0, [fp, #-8]
    // 0x72f670: StoreField: r1->field_7 = r0
    //     0x72f670: stur            x0, [x1, #7]
    // 0x72f674: r0 = _minIntrinsicWidth$Getter$FfiNative()
    //     0x72f674: bl              #0x72f694  ; [dart:ui] _NativeParagraph::_minIntrinsicWidth$Getter$FfiNative
    // 0x72f678: LeaveFrame
    //     0x72f678: mov             SP, fp
    //     0x72f67c: ldp             fp, lr, [SP], #0x10
    // 0x72f680: ret
    //     0x72f680: ret             
    // 0x72f684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72f684: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72f688: b               #0x72f608
    // 0x72f68c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x72f68c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x72f690: r0 = NullErrorSharedWithoutFPURegs()
    //     0x72f690: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ setPlaceholderDimensions(/* No info */) {
    // ** addr: 0x72f724, size: 0xa8
    // 0x72f724: EnterFrame
    //     0x72f724: stp             fp, lr, [SP, #-0x10]!
    //     0x72f728: mov             fp, SP
    // 0x72f72c: AllocStack(0x28)
    //     0x72f72c: sub             SP, SP, #0x28
    // 0x72f730: SetupParameters(TextPainter this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x72f730: mov             x0, x2
    //     0x72f734: stur            x1, [fp, #-8]
    //     0x72f738: stur            x2, [fp, #-0x10]
    // 0x72f73c: CheckStackOverflow
    //     0x72f73c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72f740: cmp             SP, x16
    //     0x72f744: b.ls            #0x72f7c4
    // 0x72f748: cmp             w0, NULL
    // 0x72f74c: b.eq            #0x72f77c
    // 0x72f750: LoadField: r2 = r0->field_b
    //     0x72f750: ldur            w2, [x0, #0xb]
    // 0x72f754: cbz             w2, #0x72f77c
    // 0x72f758: LoadField: r2 = r1->field_3b
    //     0x72f758: ldur            w2, [x1, #0x3b]
    // 0x72f75c: DecompressPointer r2
    //     0x72f75c: add             x2, x2, HEAP, lsl #32
    // 0x72f760: r16 = <PlaceholderDimensions>
    //     0x72f760: add             x16, PP, #0x46, lsl #12  ; [pp+0x46598] TypeArguments: <PlaceholderDimensions>
    //     0x72f764: ldr             x16, [x16, #0x598]
    // 0x72f768: stp             x0, x16, [SP, #8]
    // 0x72f76c: str             x2, [SP]
    // 0x72f770: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x72f770: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x72f774: r0 = listEquals()
    //     0x72f774: bl              #0x64d5e4  ; [package:flutter/src/foundation/collections.dart] ::listEquals
    // 0x72f778: tbnz            w0, #4, #0x72f78c
    // 0x72f77c: r0 = Null
    //     0x72f77c: mov             x0, NULL
    // 0x72f780: LeaveFrame
    //     0x72f780: mov             SP, fp
    //     0x72f784: ldp             fp, lr, [SP], #0x10
    // 0x72f788: ret
    //     0x72f788: ret             
    // 0x72f78c: ldur            x1, [fp, #-8]
    // 0x72f790: ldur            x0, [fp, #-0x10]
    // 0x72f794: StoreField: r1->field_3b = r0
    //     0x72f794: stur            w0, [x1, #0x3b]
    //     0x72f798: ldurb           w16, [x1, #-1]
    //     0x72f79c: ldurb           w17, [x0, #-1]
    //     0x72f7a0: and             x16, x17, x16, lsr #2
    //     0x72f7a4: tst             x16, HEAP, lsr #32
    //     0x72f7a8: b.eq            #0x72f7b0
    //     0x72f7ac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x72f7b0: r0 = markNeedsLayout()
    //     0x72f7b0: bl              #0x72f7cc  ; [package:flutter/src/painting/text_painter.dart] TextPainter::markNeedsLayout
    // 0x72f7b4: r0 = Null
    //     0x72f7b4: mov             x0, NULL
    // 0x72f7b8: LeaveFrame
    //     0x72f7b8: mov             SP, fp
    //     0x72f7bc: ldp             fp, lr, [SP], #0x10
    // 0x72f7c0: ret
    //     0x72f7c0: ret             
    // 0x72f7c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72f7c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72f7c8: b               #0x72f748
  }
  _ markNeedsLayout(/* No info */) {
    // ** addr: 0x72f7cc, size: 0xb0
    // 0x72f7cc: EnterFrame
    //     0x72f7cc: stp             fp, lr, [SP, #-0x10]!
    //     0x72f7d0: mov             fp, SP
    // 0x72f7d4: AllocStack(0x20)
    //     0x72f7d4: sub             SP, SP, #0x20
    // 0x72f7d8: SetupParameters(TextPainter this /* r1 => r1, fp-0x18 */)
    //     0x72f7d8: stur            x1, [fp, #-0x18]
    // 0x72f7dc: CheckStackOverflow
    //     0x72f7dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72f7e0: cmp             SP, x16
    //     0x72f7e4: b.ls            #0x72f870
    // 0x72f7e8: LoadField: r0 = r1->field_7
    //     0x72f7e8: ldur            w0, [x1, #7]
    // 0x72f7ec: DecompressPointer r0
    //     0x72f7ec: add             x0, x0, HEAP, lsl #32
    // 0x72f7f0: cmp             w0, NULL
    // 0x72f7f4: b.eq            #0x72f85c
    // 0x72f7f8: LoadField: r2 = r0->field_7
    //     0x72f7f8: ldur            w2, [x0, #7]
    // 0x72f7fc: DecompressPointer r2
    //     0x72f7fc: add             x2, x2, HEAP, lsl #32
    // 0x72f800: LoadField: r0 = r2->field_f
    //     0x72f800: ldur            w0, [x2, #0xf]
    // 0x72f804: DecompressPointer r0
    //     0x72f804: add             x0, x0, HEAP, lsl #32
    // 0x72f808: stur            x0, [fp, #-0x10]
    // 0x72f80c: LoadField: r2 = r0->field_7
    //     0x72f80c: ldur            w2, [x0, #7]
    // 0x72f810: DecompressPointer r2
    //     0x72f810: add             x2, x2, HEAP, lsl #32
    // 0x72f814: cmp             w2, NULL
    // 0x72f818: b.eq            #0x72f878
    // 0x72f81c: LoadField: r3 = r2->field_7
    //     0x72f81c: ldur            x3, [x2, #7]
    // 0x72f820: ldr             x2, [x3]
    // 0x72f824: stur            x2, [fp, #-8]
    // 0x72f828: cbnz            x2, #0x72f838
    // 0x72f82c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x72f82c: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x72f830: str             x16, [SP]
    // 0x72f834: r0 = _throwNew()
    //     0x72f834: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x72f838: ldur            x0, [fp, #-8]
    // 0x72f83c: stur            x0, [fp, #-8]
    // 0x72f840: r1 = <Never>
    //     0x72f840: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x72f844: r0 = Pointer()
    //     0x72f844: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x72f848: mov             x1, x0
    // 0x72f84c: ldur            x0, [fp, #-8]
    // 0x72f850: StoreField: r1->field_7 = r0
    //     0x72f850: stur            x0, [x1, #7]
    // 0x72f854: r0 = __dispose$Method$FfiNative()
    //     0x72f854: bl              #0x72f87c  ; [dart:ui] _NativeParagraph::__dispose$Method$FfiNative
    // 0x72f858: ldur            x1, [fp, #-0x18]
    // 0x72f85c: StoreField: r1->field_7 = rNULL
    //     0x72f85c: stur            NULL, [x1, #7]
    // 0x72f860: r0 = Null
    //     0x72f860: mov             x0, NULL
    // 0x72f864: LeaveFrame
    //     0x72f864: mov             SP, fp
    //     0x72f868: ldp             fp, lr, [SP], #0x10
    // 0x72f86c: ret
    //     0x72f86c: ret             
    // 0x72f870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72f870: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72f874: b               #0x72f7e8
    // 0x72f878: r0 = NullErrorSharedWithoutFPURegs()
    //     0x72f878: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  set _ maxLines=(/* No info */) {
    // ** addr: 0x72fb3c, size: 0xac
    // 0x72fb3c: EnterFrame
    //     0x72fb3c: stp             fp, lr, [SP, #-0x10]!
    //     0x72fb40: mov             fp, SP
    // 0x72fb44: mov             x0, x2
    // 0x72fb48: CheckStackOverflow
    //     0x72fb48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72fb4c: cmp             SP, x16
    //     0x72fb50: b.ls            #0x72fbe0
    // 0x72fb54: LoadField: r2 = r1->field_2b
    //     0x72fb54: ldur            w2, [x1, #0x2b]
    // 0x72fb58: DecompressPointer r2
    //     0x72fb58: add             x2, x2, HEAP, lsl #32
    // 0x72fb5c: cmp             w2, w0
    // 0x72fb60: b.eq            #0x72fb9c
    // 0x72fb64: and             w16, w2, w0
    // 0x72fb68: branchIfSmi(r16, 0x72fbac)
    //     0x72fb68: tbz             w16, #0, #0x72fbac
    // 0x72fb6c: r16 = LoadClassIdInstr(r2)
    //     0x72fb6c: ldur            x16, [x2, #-1]
    //     0x72fb70: ubfx            x16, x16, #0xc, #0x14
    // 0x72fb74: cmp             x16, #0x3d
    // 0x72fb78: b.ne            #0x72fbac
    // 0x72fb7c: r16 = LoadClassIdInstr(r0)
    //     0x72fb7c: ldur            x16, [x0, #-1]
    //     0x72fb80: ubfx            x16, x16, #0xc, #0x14
    // 0x72fb84: cmp             x16, #0x3d
    // 0x72fb88: b.ne            #0x72fbac
    // 0x72fb8c: LoadField: r16 = r2->field_7
    //     0x72fb8c: ldur            x16, [x2, #7]
    // 0x72fb90: LoadField: r17 = r0->field_7
    //     0x72fb90: ldur            x17, [x0, #7]
    // 0x72fb94: cmp             x16, x17
    // 0x72fb98: b.ne            #0x72fbac
    // 0x72fb9c: r0 = Null
    //     0x72fb9c: mov             x0, NULL
    // 0x72fba0: LeaveFrame
    //     0x72fba0: mov             SP, fp
    //     0x72fba4: ldp             fp, lr, [SP], #0x10
    // 0x72fba8: ret
    //     0x72fba8: ret             
    // 0x72fbac: StoreField: r1->field_2b = r0
    //     0x72fbac: stur            w0, [x1, #0x2b]
    //     0x72fbb0: tbz             w0, #0, #0x72fbcc
    //     0x72fbb4: ldurb           w16, [x1, #-1]
    //     0x72fbb8: ldurb           w17, [x0, #-1]
    //     0x72fbbc: and             x16, x17, x16, lsr #2
    //     0x72fbc0: tst             x16, HEAP, lsr #32
    //     0x72fbc4: b.eq            #0x72fbcc
    //     0x72fbc8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x72fbcc: r0 = markNeedsLayout()
    //     0x72fbcc: bl              #0x72f7cc  ; [package:flutter/src/painting/text_painter.dart] TextPainter::markNeedsLayout
    // 0x72fbd0: r0 = Null
    //     0x72fbd0: mov             x0, NULL
    // 0x72fbd4: LeaveFrame
    //     0x72fbd4: mov             SP, fp
    //     0x72fbd8: ldp             fp, lr, [SP], #0x10
    // 0x72fbdc: ret
    //     0x72fbdc: ret             
    // 0x72fbe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72fbe0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72fbe4: b               #0x72fb54
  }
  set _ textAlign=(/* No info */) {
    // ** addr: 0x72fbe8, size: 0x70
    // 0x72fbe8: EnterFrame
    //     0x72fbe8: stp             fp, lr, [SP, #-0x10]!
    //     0x72fbec: mov             fp, SP
    // 0x72fbf0: mov             x0, x2
    // 0x72fbf4: CheckStackOverflow
    //     0x72fbf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72fbf8: cmp             SP, x16
    //     0x72fbfc: b.ls            #0x72fc50
    // 0x72fc00: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x72fc00: ldur            w2, [x1, #0x17]
    // 0x72fc04: DecompressPointer r2
    //     0x72fc04: add             x2, x2, HEAP, lsl #32
    // 0x72fc08: cmp             w2, w0
    // 0x72fc0c: b.ne            #0x72fc20
    // 0x72fc10: r0 = Null
    //     0x72fc10: mov             x0, NULL
    // 0x72fc14: LeaveFrame
    //     0x72fc14: mov             SP, fp
    //     0x72fc18: ldp             fp, lr, [SP], #0x10
    // 0x72fc1c: ret
    //     0x72fc1c: ret             
    // 0x72fc20: ArrayStore: r1[0] = r0  ; List_4
    //     0x72fc20: stur            w0, [x1, #0x17]
    //     0x72fc24: ldurb           w16, [x1, #-1]
    //     0x72fc28: ldurb           w17, [x0, #-1]
    //     0x72fc2c: and             x16, x17, x16, lsr #2
    //     0x72fc30: tst             x16, HEAP, lsr #32
    //     0x72fc34: b.eq            #0x72fc3c
    //     0x72fc38: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x72fc3c: r0 = markNeedsLayout()
    //     0x72fc3c: bl              #0x72f7cc  ; [package:flutter/src/painting/text_painter.dart] TextPainter::markNeedsLayout
    // 0x72fc40: r0 = Null
    //     0x72fc40: mov             x0, NULL
    // 0x72fc44: LeaveFrame
    //     0x72fc44: mov             SP, fp
    //     0x72fc48: ldp             fp, lr, [SP], #0x10
    // 0x72fc4c: ret
    //     0x72fc4c: ret             
    // 0x72fc50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72fc50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72fc54: b               #0x72fc00
  }
  set _ strutStyle=(/* No info */) {
    // ** addr: 0x72fc58, size: 0xa4
    // 0x72fc58: EnterFrame
    //     0x72fc58: stp             fp, lr, [SP, #-0x10]!
    //     0x72fc5c: mov             fp, SP
    // 0x72fc60: AllocStack(0x20)
    //     0x72fc60: sub             SP, SP, #0x20
    // 0x72fc64: SetupParameters(TextPainter this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x72fc64: stur            x1, [fp, #-8]
    //     0x72fc68: mov             x16, x2
    //     0x72fc6c: mov             x2, x1
    //     0x72fc70: mov             x1, x16
    //     0x72fc74: stur            x1, [fp, #-0x10]
    // 0x72fc78: CheckStackOverflow
    //     0x72fc78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72fc7c: cmp             SP, x16
    //     0x72fc80: b.ls            #0x72fcf4
    // 0x72fc84: LoadField: r0 = r2->field_2f
    //     0x72fc84: ldur            w0, [x2, #0x2f]
    // 0x72fc88: DecompressPointer r0
    //     0x72fc88: add             x0, x0, HEAP, lsl #32
    // 0x72fc8c: r3 = LoadClassIdInstr(r0)
    //     0x72fc8c: ldur            x3, [x0, #-1]
    //     0x72fc90: ubfx            x3, x3, #0xc, #0x14
    // 0x72fc94: stp             x1, x0, [SP]
    // 0x72fc98: mov             x0, x3
    // 0x72fc9c: mov             lr, x0
    // 0x72fca0: ldr             lr, [x21, lr, lsl #3]
    // 0x72fca4: blr             lr
    // 0x72fca8: tbnz            w0, #4, #0x72fcbc
    // 0x72fcac: r0 = Null
    //     0x72fcac: mov             x0, NULL
    // 0x72fcb0: LeaveFrame
    //     0x72fcb0: mov             SP, fp
    //     0x72fcb4: ldp             fp, lr, [SP], #0x10
    // 0x72fcb8: ret
    //     0x72fcb8: ret             
    // 0x72fcbc: ldur            x1, [fp, #-8]
    // 0x72fcc0: ldur            x0, [fp, #-0x10]
    // 0x72fcc4: StoreField: r1->field_2f = r0
    //     0x72fcc4: stur            w0, [x1, #0x2f]
    //     0x72fcc8: ldurb           w16, [x1, #-1]
    //     0x72fccc: ldurb           w17, [x0, #-1]
    //     0x72fcd0: and             x16, x17, x16, lsr #2
    //     0x72fcd4: tst             x16, HEAP, lsr #32
    //     0x72fcd8: b.eq            #0x72fce0
    //     0x72fcdc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x72fce0: r0 = markNeedsLayout()
    //     0x72fce0: bl              #0x72f7cc  ; [package:flutter/src/painting/text_painter.dart] TextPainter::markNeedsLayout
    // 0x72fce4: r0 = Null
    //     0x72fce4: mov             x0, NULL
    // 0x72fce8: LeaveFrame
    //     0x72fce8: mov             SP, fp
    //     0x72fcec: ldp             fp, lr, [SP], #0x10
    // 0x72fcf0: ret
    //     0x72fcf0: ret             
    // 0x72fcf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72fcf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72fcf8: b               #0x72fc84
  }
  set _ locale=(/* No info */) {
    // ** addr: 0x72fcfc, size: 0xa4
    // 0x72fcfc: EnterFrame
    //     0x72fcfc: stp             fp, lr, [SP, #-0x10]!
    //     0x72fd00: mov             fp, SP
    // 0x72fd04: AllocStack(0x20)
    //     0x72fd04: sub             SP, SP, #0x20
    // 0x72fd08: SetupParameters(TextPainter this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x72fd08: stur            x1, [fp, #-8]
    //     0x72fd0c: mov             x16, x2
    //     0x72fd10: mov             x2, x1
    //     0x72fd14: mov             x1, x16
    //     0x72fd18: stur            x1, [fp, #-0x10]
    // 0x72fd1c: CheckStackOverflow
    //     0x72fd1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72fd20: cmp             SP, x16
    //     0x72fd24: b.ls            #0x72fd98
    // 0x72fd28: LoadField: r0 = r2->field_27
    //     0x72fd28: ldur            w0, [x2, #0x27]
    // 0x72fd2c: DecompressPointer r0
    //     0x72fd2c: add             x0, x0, HEAP, lsl #32
    // 0x72fd30: r3 = LoadClassIdInstr(r0)
    //     0x72fd30: ldur            x3, [x0, #-1]
    //     0x72fd34: ubfx            x3, x3, #0xc, #0x14
    // 0x72fd38: stp             x1, x0, [SP]
    // 0x72fd3c: mov             x0, x3
    // 0x72fd40: mov             lr, x0
    // 0x72fd44: ldr             lr, [x21, lr, lsl #3]
    // 0x72fd48: blr             lr
    // 0x72fd4c: tbnz            w0, #4, #0x72fd60
    // 0x72fd50: r0 = Null
    //     0x72fd50: mov             x0, NULL
    // 0x72fd54: LeaveFrame
    //     0x72fd54: mov             SP, fp
    //     0x72fd58: ldp             fp, lr, [SP], #0x10
    // 0x72fd5c: ret
    //     0x72fd5c: ret             
    // 0x72fd60: ldur            x1, [fp, #-8]
    // 0x72fd64: ldur            x0, [fp, #-0x10]
    // 0x72fd68: StoreField: r1->field_27 = r0
    //     0x72fd68: stur            w0, [x1, #0x27]
    //     0x72fd6c: ldurb           w16, [x1, #-1]
    //     0x72fd70: ldurb           w17, [x0, #-1]
    //     0x72fd74: and             x16, x17, x16, lsr #2
    //     0x72fd78: tst             x16, HEAP, lsr #32
    //     0x72fd7c: b.eq            #0x72fd84
    //     0x72fd80: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x72fd84: r0 = markNeedsLayout()
    //     0x72fd84: bl              #0x72f7cc  ; [package:flutter/src/painting/text_painter.dart] TextPainter::markNeedsLayout
    // 0x72fd88: r0 = Null
    //     0x72fd88: mov             x0, NULL
    // 0x72fd8c: LeaveFrame
    //     0x72fd8c: mov             SP, fp
    //     0x72fd90: ldp             fp, lr, [SP], #0x10
    // 0x72fd94: ret
    //     0x72fd94: ret             
    // 0x72fd98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72fd98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72fd9c: b               #0x72fd28
  }
  set _ ellipsis=(/* No info */) {
    // ** addr: 0x72fda0, size: 0xa4
    // 0x72fda0: EnterFrame
    //     0x72fda0: stp             fp, lr, [SP, #-0x10]!
    //     0x72fda4: mov             fp, SP
    // 0x72fda8: AllocStack(0x20)
    //     0x72fda8: sub             SP, SP, #0x20
    // 0x72fdac: SetupParameters(TextPainter this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x72fdac: stur            x1, [fp, #-8]
    //     0x72fdb0: mov             x16, x2
    //     0x72fdb4: mov             x2, x1
    //     0x72fdb8: mov             x1, x16
    //     0x72fdbc: stur            x1, [fp, #-0x10]
    // 0x72fdc0: CheckStackOverflow
    //     0x72fdc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72fdc4: cmp             SP, x16
    //     0x72fdc8: b.ls            #0x72fe3c
    // 0x72fdcc: LoadField: r0 = r2->field_23
    //     0x72fdcc: ldur            w0, [x2, #0x23]
    // 0x72fdd0: DecompressPointer r0
    //     0x72fdd0: add             x0, x0, HEAP, lsl #32
    // 0x72fdd4: r3 = LoadClassIdInstr(r0)
    //     0x72fdd4: ldur            x3, [x0, #-1]
    //     0x72fdd8: ubfx            x3, x3, #0xc, #0x14
    // 0x72fddc: stp             x1, x0, [SP]
    // 0x72fde0: mov             x0, x3
    // 0x72fde4: mov             lr, x0
    // 0x72fde8: ldr             lr, [x21, lr, lsl #3]
    // 0x72fdec: blr             lr
    // 0x72fdf0: tbnz            w0, #4, #0x72fe04
    // 0x72fdf4: r0 = Null
    //     0x72fdf4: mov             x0, NULL
    // 0x72fdf8: LeaveFrame
    //     0x72fdf8: mov             SP, fp
    //     0x72fdfc: ldp             fp, lr, [SP], #0x10
    // 0x72fe00: ret
    //     0x72fe00: ret             
    // 0x72fe04: ldur            x1, [fp, #-8]
    // 0x72fe08: ldur            x0, [fp, #-0x10]
    // 0x72fe0c: StoreField: r1->field_23 = r0
    //     0x72fe0c: stur            w0, [x1, #0x23]
    //     0x72fe10: ldurb           w16, [x1, #-1]
    //     0x72fe14: ldurb           w17, [x0, #-1]
    //     0x72fe18: and             x16, x17, x16, lsr #2
    //     0x72fe1c: tst             x16, HEAP, lsr #32
    //     0x72fe20: b.eq            #0x72fe28
    //     0x72fe24: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x72fe28: r0 = markNeedsLayout()
    //     0x72fe28: bl              #0x72f7cc  ; [package:flutter/src/painting/text_painter.dart] TextPainter::markNeedsLayout
    // 0x72fe2c: r0 = Null
    //     0x72fe2c: mov             x0, NULL
    // 0x72fe30: LeaveFrame
    //     0x72fe30: mov             SP, fp
    //     0x72fe34: ldp             fp, lr, [SP], #0x10
    // 0x72fe38: ret
    //     0x72fe38: ret             
    // 0x72fe3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72fe3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72fe40: b               #0x72fdcc
  }
  set _ textScaler=(/* No info */) {
    // ** addr: 0x72fe44, size: 0x120
    // 0x72fe44: EnterFrame
    //     0x72fe44: stp             fp, lr, [SP, #-0x10]!
    //     0x72fe48: mov             fp, SP
    // 0x72fe4c: AllocStack(0x28)
    //     0x72fe4c: sub             SP, SP, #0x28
    // 0x72fe50: SetupParameters(TextPainter this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x72fe50: stur            x1, [fp, #-8]
    //     0x72fe54: mov             x16, x2
    //     0x72fe58: mov             x2, x1
    //     0x72fe5c: mov             x1, x16
    //     0x72fe60: stur            x1, [fp, #-0x10]
    // 0x72fe64: CheckStackOverflow
    //     0x72fe64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72fe68: cmp             SP, x16
    //     0x72fe6c: b.ls            #0x72ff58
    // 0x72fe70: LoadField: r0 = r2->field_1f
    //     0x72fe70: ldur            w0, [x2, #0x1f]
    // 0x72fe74: DecompressPointer r0
    //     0x72fe74: add             x0, x0, HEAP, lsl #32
    // 0x72fe78: r3 = LoadClassIdInstr(r1)
    //     0x72fe78: ldur            x3, [x1, #-1]
    //     0x72fe7c: ubfx            x3, x3, #0xc, #0x14
    // 0x72fe80: stp             x0, x1, [SP]
    // 0x72fe84: mov             x0, x3
    // 0x72fe88: mov             lr, x0
    // 0x72fe8c: ldr             lr, [x21, lr, lsl #3]
    // 0x72fe90: blr             lr
    // 0x72fe94: tbnz            w0, #4, #0x72fea8
    // 0x72fe98: r0 = Null
    //     0x72fe98: mov             x0, NULL
    // 0x72fe9c: LeaveFrame
    //     0x72fe9c: mov             SP, fp
    //     0x72fea0: ldp             fp, lr, [SP], #0x10
    // 0x72fea4: ret
    //     0x72fea4: ret             
    // 0x72fea8: ldur            x2, [fp, #-8]
    // 0x72feac: ldur            x0, [fp, #-0x10]
    // 0x72feb0: StoreField: r2->field_1f = r0
    //     0x72feb0: stur            w0, [x2, #0x1f]
    //     0x72feb4: ldurb           w16, [x2, #-1]
    //     0x72feb8: ldurb           w17, [x0, #-1]
    //     0x72febc: and             x16, x17, x16, lsr #2
    //     0x72fec0: tst             x16, HEAP, lsr #32
    //     0x72fec4: b.eq            #0x72fecc
    //     0x72fec8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x72fecc: mov             x1, x2
    // 0x72fed0: r0 = markNeedsLayout()
    //     0x72fed0: bl              #0x72f7cc  ; [package:flutter/src/painting/text_painter.dart] TextPainter::markNeedsLayout
    // 0x72fed4: ldur            x0, [fp, #-8]
    // 0x72fed8: LoadField: r1 = r0->field_3f
    //     0x72fed8: ldur            w1, [x0, #0x3f]
    // 0x72fedc: DecompressPointer r1
    //     0x72fedc: add             x1, x1, HEAP, lsl #32
    // 0x72fee0: stur            x1, [fp, #-0x10]
    // 0x72fee4: cmp             w1, NULL
    // 0x72fee8: b.ne            #0x72fef4
    // 0x72feec: mov             x1, x0
    // 0x72fef0: b               #0x72ff44
    // 0x72fef4: LoadField: r2 = r1->field_7
    //     0x72fef4: ldur            w2, [x1, #7]
    // 0x72fef8: DecompressPointer r2
    //     0x72fef8: add             x2, x2, HEAP, lsl #32
    // 0x72fefc: cmp             w2, NULL
    // 0x72ff00: b.eq            #0x72ff60
    // 0x72ff04: LoadField: r3 = r2->field_7
    //     0x72ff04: ldur            x3, [x2, #7]
    // 0x72ff08: ldr             x2, [x3]
    // 0x72ff0c: stur            x2, [fp, #-0x18]
    // 0x72ff10: cbnz            x2, #0x72ff20
    // 0x72ff14: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x72ff14: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x72ff18: str             x16, [SP]
    // 0x72ff1c: r0 = _throwNew()
    //     0x72ff1c: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x72ff20: ldur            x0, [fp, #-0x18]
    // 0x72ff24: stur            x0, [fp, #-0x18]
    // 0x72ff28: r1 = <Never>
    //     0x72ff28: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x72ff2c: r0 = Pointer()
    //     0x72ff2c: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x72ff30: mov             x1, x0
    // 0x72ff34: ldur            x0, [fp, #-0x18]
    // 0x72ff38: StoreField: r1->field_7 = r0
    //     0x72ff38: stur            x0, [x1, #7]
    // 0x72ff3c: r0 = __dispose$Method$FfiNative()
    //     0x72ff3c: bl              #0x72f87c  ; [dart:ui] _NativeParagraph::__dispose$Method$FfiNative
    // 0x72ff40: ldur            x1, [fp, #-8]
    // 0x72ff44: StoreField: r1->field_3f = rNULL
    //     0x72ff44: stur            NULL, [x1, #0x3f]
    // 0x72ff48: r0 = Null
    //     0x72ff48: mov             x0, NULL
    // 0x72ff4c: LeaveFrame
    //     0x72ff4c: mov             SP, fp
    //     0x72ff50: ldp             fp, lr, [SP], #0x10
    // 0x72ff54: ret
    //     0x72ff54: ret             
    // 0x72ff58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72ff58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72ff5c: b               #0x72fe70
    // 0x72ff60: r0 = NullErrorSharedWithoutFPURegs()
    //     0x72ff60: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  set _ textDirection=(/* No info */) {
    // ** addr: 0x72ff64, size: 0xf8
    // 0x72ff64: EnterFrame
    //     0x72ff64: stp             fp, lr, [SP, #-0x10]!
    //     0x72ff68: mov             fp, SP
    // 0x72ff6c: AllocStack(0x20)
    //     0x72ff6c: sub             SP, SP, #0x20
    // 0x72ff70: SetupParameters(TextPainter this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0 */)
    //     0x72ff70: mov             x0, x2
    //     0x72ff74: mov             x2, x1
    //     0x72ff78: stur            x1, [fp, #-8]
    // 0x72ff7c: CheckStackOverflow
    //     0x72ff7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72ff80: cmp             SP, x16
    //     0x72ff84: b.ls            #0x730050
    // 0x72ff88: LoadField: r1 = r2->field_1b
    //     0x72ff88: ldur            w1, [x2, #0x1b]
    // 0x72ff8c: DecompressPointer r1
    //     0x72ff8c: add             x1, x1, HEAP, lsl #32
    // 0x72ff90: cmp             w1, w0
    // 0x72ff94: b.ne            #0x72ffa8
    // 0x72ff98: r0 = Null
    //     0x72ff98: mov             x0, NULL
    // 0x72ff9c: LeaveFrame
    //     0x72ff9c: mov             SP, fp
    //     0x72ffa0: ldp             fp, lr, [SP], #0x10
    // 0x72ffa4: ret
    //     0x72ffa4: ret             
    // 0x72ffa8: StoreField: r2->field_1b = r0
    //     0x72ffa8: stur            w0, [x2, #0x1b]
    //     0x72ffac: ldurb           w16, [x2, #-1]
    //     0x72ffb0: ldurb           w17, [x0, #-1]
    //     0x72ffb4: and             x16, x17, x16, lsr #2
    //     0x72ffb8: tst             x16, HEAP, lsr #32
    //     0x72ffbc: b.eq            #0x72ffc4
    //     0x72ffc0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x72ffc4: mov             x1, x2
    // 0x72ffc8: r0 = markNeedsLayout()
    //     0x72ffc8: bl              #0x72f7cc  ; [package:flutter/src/painting/text_painter.dart] TextPainter::markNeedsLayout
    // 0x72ffcc: ldur            x0, [fp, #-8]
    // 0x72ffd0: LoadField: r1 = r0->field_3f
    //     0x72ffd0: ldur            w1, [x0, #0x3f]
    // 0x72ffd4: DecompressPointer r1
    //     0x72ffd4: add             x1, x1, HEAP, lsl #32
    // 0x72ffd8: stur            x1, [fp, #-0x18]
    // 0x72ffdc: cmp             w1, NULL
    // 0x72ffe0: b.ne            #0x72ffec
    // 0x72ffe4: mov             x1, x0
    // 0x72ffe8: b               #0x73003c
    // 0x72ffec: LoadField: r2 = r1->field_7
    //     0x72ffec: ldur            w2, [x1, #7]
    // 0x72fff0: DecompressPointer r2
    //     0x72fff0: add             x2, x2, HEAP, lsl #32
    // 0x72fff4: cmp             w2, NULL
    // 0x72fff8: b.eq            #0x730058
    // 0x72fffc: LoadField: r3 = r2->field_7
    //     0x72fffc: ldur            x3, [x2, #7]
    // 0x730000: ldr             x2, [x3]
    // 0x730004: stur            x2, [fp, #-0x10]
    // 0x730008: cbnz            x2, #0x730018
    // 0x73000c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x73000c: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x730010: str             x16, [SP]
    // 0x730014: r0 = _throwNew()
    //     0x730014: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x730018: ldur            x0, [fp, #-0x10]
    // 0x73001c: stur            x0, [fp, #-0x10]
    // 0x730020: r1 = <Never>
    //     0x730020: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x730024: r0 = Pointer()
    //     0x730024: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x730028: mov             x1, x0
    // 0x73002c: ldur            x0, [fp, #-0x10]
    // 0x730030: StoreField: r1->field_7 = r0
    //     0x730030: stur            x0, [x1, #7]
    // 0x730034: r0 = __dispose$Method$FfiNative()
    //     0x730034: bl              #0x72f87c  ; [dart:ui] _NativeParagraph::__dispose$Method$FfiNative
    // 0x730038: ldur            x1, [fp, #-8]
    // 0x73003c: StoreField: r1->field_3f = rNULL
    //     0x73003c: stur            NULL, [x1, #0x3f]
    // 0x730040: r0 = Null
    //     0x730040: mov             x0, NULL
    // 0x730044: LeaveFrame
    //     0x730044: mov             SP, fp
    //     0x730048: ldp             fp, lr, [SP], #0x10
    // 0x73004c: ret
    //     0x73004c: ret             
    // 0x730050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x730050: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x730054: b               #0x72ff88
    // 0x730058: r0 = NullErrorSharedWithoutFPURegs()
    //     0x730058: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  set _ text=(/* No info */) {
    // ** addr: 0x73005c, size: 0x1f0
    // 0x73005c: EnterFrame
    //     0x73005c: stp             fp, lr, [SP, #-0x10]!
    //     0x730060: mov             fp, SP
    // 0x730064: AllocStack(0x30)
    //     0x730064: sub             SP, SP, #0x30
    // 0x730068: SetupParameters(TextPainter this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x730068: stur            x1, [fp, #-8]
    //     0x73006c: stur            x2, [fp, #-0x10]
    // 0x730070: CheckStackOverflow
    //     0x730070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x730074: cmp             SP, x16
    //     0x730078: b.ls            #0x730240
    // 0x73007c: LoadField: r0 = r1->field_f
    //     0x73007c: ldur            w0, [x1, #0xf]
    // 0x730080: DecompressPointer r0
    //     0x730080: add             x0, x0, HEAP, lsl #32
    // 0x730084: r3 = LoadClassIdInstr(r0)
    //     0x730084: ldur            x3, [x0, #-1]
    //     0x730088: ubfx            x3, x3, #0xc, #0x14
    // 0x73008c: stp             x2, x0, [SP]
    // 0x730090: mov             x0, x3
    // 0x730094: mov             lr, x0
    // 0x730098: ldr             lr, [x21, lr, lsl #3]
    // 0x73009c: blr             lr
    // 0x7300a0: tbnz            w0, #4, #0x7300b4
    // 0x7300a4: r0 = Null
    //     0x7300a4: mov             x0, NULL
    // 0x7300a8: LeaveFrame
    //     0x7300a8: mov             SP, fp
    //     0x7300ac: ldp             fp, lr, [SP], #0x10
    // 0x7300b0: ret
    //     0x7300b0: ret             
    // 0x7300b4: ldur            x1, [fp, #-8]
    // 0x7300b8: LoadField: r0 = r1->field_f
    //     0x7300b8: ldur            w0, [x1, #0xf]
    // 0x7300bc: DecompressPointer r0
    //     0x7300bc: add             x0, x0, HEAP, lsl #32
    // 0x7300c0: cmp             w0, NULL
    // 0x7300c4: b.ne            #0x7300d0
    // 0x7300c8: r0 = Null
    //     0x7300c8: mov             x0, NULL
    // 0x7300cc: b               #0x7300dc
    // 0x7300d0: LoadField: r2 = r0->field_7
    //     0x7300d0: ldur            w2, [x0, #7]
    // 0x7300d4: DecompressPointer r2
    //     0x7300d4: add             x2, x2, HEAP, lsl #32
    // 0x7300d8: mov             x0, x2
    // 0x7300dc: ldur            x2, [fp, #-0x10]
    // 0x7300e0: cmp             w2, NULL
    // 0x7300e4: b.ne            #0x7300f0
    // 0x7300e8: r3 = Null
    //     0x7300e8: mov             x3, NULL
    // 0x7300ec: b               #0x7300f8
    // 0x7300f0: LoadField: r3 = r2->field_7
    //     0x7300f0: ldur            w3, [x2, #7]
    // 0x7300f4: DecompressPointer r3
    //     0x7300f4: add             x3, x3, HEAP, lsl #32
    // 0x7300f8: r4 = LoadClassIdInstr(r0)
    //     0x7300f8: ldur            x4, [x0, #-1]
    //     0x7300fc: ubfx            x4, x4, #0xc, #0x14
    // 0x730100: stp             x3, x0, [SP]
    // 0x730104: mov             x0, x4
    // 0x730108: mov             lr, x0
    // 0x73010c: ldr             lr, [x21, lr, lsl #3]
    // 0x730110: blr             lr
    // 0x730114: tbz             w0, #4, #0x730190
    // 0x730118: ldur            x1, [fp, #-8]
    // 0x73011c: LoadField: r0 = r1->field_3f
    //     0x73011c: ldur            w0, [x1, #0x3f]
    // 0x730120: DecompressPointer r0
    //     0x730120: add             x0, x0, HEAP, lsl #32
    // 0x730124: stur            x0, [fp, #-0x20]
    // 0x730128: cmp             w0, NULL
    // 0x73012c: b.ne            #0x730138
    // 0x730130: mov             x0, x1
    // 0x730134: b               #0x730188
    // 0x730138: LoadField: r2 = r0->field_7
    //     0x730138: ldur            w2, [x0, #7]
    // 0x73013c: DecompressPointer r2
    //     0x73013c: add             x2, x2, HEAP, lsl #32
    // 0x730140: cmp             w2, NULL
    // 0x730144: b.eq            #0x730248
    // 0x730148: LoadField: r3 = r2->field_7
    //     0x730148: ldur            x3, [x2, #7]
    // 0x73014c: ldr             x2, [x3]
    // 0x730150: stur            x2, [fp, #-0x18]
    // 0x730154: cbnz            x2, #0x730164
    // 0x730158: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x730158: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x73015c: str             x16, [SP]
    // 0x730160: r0 = _throwNew()
    //     0x730160: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x730164: ldur            x0, [fp, #-0x18]
    // 0x730168: stur            x0, [fp, #-0x18]
    // 0x73016c: r1 = <Never>
    //     0x73016c: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x730170: r0 = Pointer()
    //     0x730170: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x730174: mov             x1, x0
    // 0x730178: ldur            x0, [fp, #-0x18]
    // 0x73017c: StoreField: r1->field_7 = r0
    //     0x73017c: stur            x0, [x1, #7]
    // 0x730180: r0 = __dispose$Method$FfiNative()
    //     0x730180: bl              #0x72f87c  ; [dart:ui] _NativeParagraph::__dispose$Method$FfiNative
    // 0x730184: ldur            x0, [fp, #-8]
    // 0x730188: StoreField: r0->field_3f = rNULL
    //     0x730188: stur            NULL, [x0, #0x3f]
    // 0x73018c: b               #0x730194
    // 0x730190: ldur            x0, [fp, #-8]
    // 0x730194: ldur            x3, [fp, #-0x10]
    // 0x730198: cmp             w3, NULL
    // 0x73019c: b.ne            #0x7301b0
    // 0x7301a0: mov             x1, x0
    // 0x7301a4: r2 = Instance_RenderComparison
    //     0x7301a4: add             x2, PP, #0x46, lsl #12  ; [pp+0x465c8] Obj!RenderComparison@e35f61
    //     0x7301a8: ldr             x2, [x2, #0x5c8]
    // 0x7301ac: b               #0x7301e8
    // 0x7301b0: LoadField: r1 = r0->field_f
    //     0x7301b0: ldur            w1, [x0, #0xf]
    // 0x7301b4: DecompressPointer r1
    //     0x7301b4: add             x1, x1, HEAP, lsl #32
    // 0x7301b8: cmp             w1, NULL
    // 0x7301bc: b.ne            #0x7301c8
    // 0x7301c0: r0 = Null
    //     0x7301c0: mov             x0, NULL
    // 0x7301c4: b               #0x7301d0
    // 0x7301c8: mov             x2, x3
    // 0x7301cc: r0 = compareTo()
    //     0x7301cc: bl              #0xd210e4  ; [package:flutter/src/painting/text_span.dart] TextSpan::compareTo
    // 0x7301d0: cmp             w0, NULL
    // 0x7301d4: b.ne            #0x7301e0
    // 0x7301d8: r0 = Instance_RenderComparison
    //     0x7301d8: add             x0, PP, #0x46, lsl #12  ; [pp+0x465c8] Obj!RenderComparison@e35f61
    //     0x7301dc: ldr             x0, [x0, #0x5c8]
    // 0x7301e0: mov             x2, x0
    // 0x7301e4: ldur            x1, [fp, #-8]
    // 0x7301e8: ldur            x0, [fp, #-0x10]
    // 0x7301ec: StoreField: r1->field_f = r0
    //     0x7301ec: stur            w0, [x1, #0xf]
    //     0x7301f0: ldurb           w16, [x1, #-1]
    //     0x7301f4: ldurb           w17, [x0, #-1]
    //     0x7301f8: and             x16, x17, x16, lsr #2
    //     0x7301fc: tst             x16, HEAP, lsr #32
    //     0x730200: b.eq            #0x730208
    //     0x730204: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x730208: StoreField: r1->field_13 = rNULL
    //     0x730208: stur            NULL, [x1, #0x13]
    // 0x73020c: LoadField: r0 = r2->field_7
    //     0x73020c: ldur            x0, [x2, #7]
    // 0x730210: cmp             x0, #3
    // 0x730214: b.lt            #0x730220
    // 0x730218: r0 = markNeedsLayout()
    //     0x730218: bl              #0x72f7cc  ; [package:flutter/src/painting/text_painter.dart] TextPainter::markNeedsLayout
    // 0x73021c: b               #0x730230
    // 0x730220: cmp             x0, #2
    // 0x730224: b.lt            #0x730230
    // 0x730228: r2 = true
    //     0x730228: add             x2, NULL, #0x20  ; true
    // 0x73022c: StoreField: r1->field_b = r2
    //     0x73022c: stur            w2, [x1, #0xb]
    // 0x730230: r0 = Null
    //     0x730230: mov             x0, NULL
    // 0x730234: LeaveFrame
    //     0x730234: mov             SP, fp
    //     0x730238: ldp             fp, lr, [SP], #0x10
    // 0x73023c: ret
    //     0x73023c: ret             
    // 0x730240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x730240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x730244: b               #0x73007c
    // 0x730248: r0 = NullErrorSharedWithoutFPURegs()
    //     0x730248: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ TextPainter(/* No info */) {
    // ** addr: 0x73024c, size: 0x43c
    // 0x73024c: EnterFrame
    //     0x73024c: stp             fp, lr, [SP, #-0x10]!
    //     0x730250: mov             fp, SP
    // 0x730254: AllocStack(0x40)
    //     0x730254: sub             SP, SP, #0x40
    // 0x730258: SetupParameters(TextPainter this /* r1 => r1, fp-0x30 */, {dynamic ellipsis = Null /* r3, fp-0x28 */, dynamic locale = Null /* r5, fp-0x20 */, dynamic maxLines = Null /* r6, fp-0x18 */, dynamic strutStyle = Null /* r7, fp-0x10 */, dynamic text = Null /* r8 */, dynamic textAlign = Instance_TextAlign /* r9 */, dynamic textDirection = Null /* r10 */, dynamic textScaler = Instance__LinearTextScaler /* r4, fp-0x8 */})
    //     0x730258: stur            x1, [fp, #-0x30]
    //     0x73025c: ldur            w0, [x4, #0x13]
    //     0x730260: ldur            w2, [x4, #0x1f]
    //     0x730264: add             x2, x2, HEAP, lsl #32
    //     0x730268: ldr             x16, [PP, #0x47f8]  ; [pp+0x47f8] "ellipsis"
    //     0x73026c: cmp             w2, w16
    //     0x730270: b.ne            #0x730294
    //     0x730274: ldur            w2, [x4, #0x23]
    //     0x730278: add             x2, x2, HEAP, lsl #32
    //     0x73027c: sub             w3, w0, w2
    //     0x730280: add             x2, fp, w3, sxtw #2
    //     0x730284: ldr             x2, [x2, #8]
    //     0x730288: mov             x3, x2
    //     0x73028c: movz            x2, #0x1
    //     0x730290: b               #0x73029c
    //     0x730294: mov             x3, NULL
    //     0x730298: movz            x2, #0
    //     0x73029c: stur            x3, [fp, #-0x28]
    //     0x7302a0: lsl             x5, x2, #1
    //     0x7302a4: lsl             w6, w5, #1
    //     0x7302a8: add             w7, w6, #8
    //     0x7302ac: add             x16, x4, w7, sxtw #1
    //     0x7302b0: ldur            w8, [x16, #0xf]
    //     0x7302b4: add             x8, x8, HEAP, lsl #32
    //     0x7302b8: ldr             x16, [PP, #0x4810]  ; [pp+0x4810] "locale"
    //     0x7302bc: cmp             w8, w16
    //     0x7302c0: b.ne            #0x7302f4
    //     0x7302c4: add             w2, w6, #0xa
    //     0x7302c8: add             x16, x4, w2, sxtw #1
    //     0x7302cc: ldur            w6, [x16, #0xf]
    //     0x7302d0: add             x6, x6, HEAP, lsl #32
    //     0x7302d4: sub             w2, w0, w6
    //     0x7302d8: add             x6, fp, w2, sxtw #2
    //     0x7302dc: ldr             x6, [x6, #8]
    //     0x7302e0: add             w2, w5, #2
    //     0x7302e4: sbfx            x5, x2, #1, #0x1f
    //     0x7302e8: mov             x2, x5
    //     0x7302ec: mov             x5, x6
    //     0x7302f0: b               #0x7302f8
    //     0x7302f4: mov             x5, NULL
    //     0x7302f8: stur            x5, [fp, #-0x20]
    //     0x7302fc: lsl             x6, x2, #1
    //     0x730300: lsl             w7, w6, #1
    //     0x730304: add             w8, w7, #8
    //     0x730308: add             x16, x4, w8, sxtw #1
    //     0x73030c: ldur            w9, [x16, #0xf]
    //     0x730310: add             x9, x9, HEAP, lsl #32
    //     0x730314: ldr             x16, [PP, #0x4818]  ; [pp+0x4818] "maxLines"
    //     0x730318: cmp             w9, w16
    //     0x73031c: b.ne            #0x730350
    //     0x730320: add             w2, w7, #0xa
    //     0x730324: add             x16, x4, w2, sxtw #1
    //     0x730328: ldur            w7, [x16, #0xf]
    //     0x73032c: add             x7, x7, HEAP, lsl #32
    //     0x730330: sub             w2, w0, w7
    //     0x730334: add             x7, fp, w2, sxtw #2
    //     0x730338: ldr             x7, [x7, #8]
    //     0x73033c: add             w2, w6, #2
    //     0x730340: sbfx            x6, x2, #1, #0x1f
    //     0x730344: mov             x2, x6
    //     0x730348: mov             x6, x7
    //     0x73034c: b               #0x730354
    //     0x730350: mov             x6, NULL
    //     0x730354: stur            x6, [fp, #-0x18]
    //     0x730358: lsl             x7, x2, #1
    //     0x73035c: lsl             w8, w7, #1
    //     0x730360: add             w9, w8, #8
    //     0x730364: add             x16, x4, w9, sxtw #1
    //     0x730368: ldur            w10, [x16, #0xf]
    //     0x73036c: add             x10, x10, HEAP, lsl #32
    //     0x730370: ldr             x16, [PP, #0x4820]  ; [pp+0x4820] "strutStyle"
    //     0x730374: cmp             w10, w16
    //     0x730378: b.ne            #0x7303ac
    //     0x73037c: add             w2, w8, #0xa
    //     0x730380: add             x16, x4, w2, sxtw #1
    //     0x730384: ldur            w8, [x16, #0xf]
    //     0x730388: add             x8, x8, HEAP, lsl #32
    //     0x73038c: sub             w2, w0, w8
    //     0x730390: add             x8, fp, w2, sxtw #2
    //     0x730394: ldr             x8, [x8, #8]
    //     0x730398: add             w2, w7, #2
    //     0x73039c: sbfx            x7, x2, #1, #0x1f
    //     0x7303a0: mov             x2, x7
    //     0x7303a4: mov             x7, x8
    //     0x7303a8: b               #0x7303b0
    //     0x7303ac: mov             x7, NULL
    //     0x7303b0: stur            x7, [fp, #-0x10]
    //     0x7303b4: lsl             x8, x2, #1
    //     0x7303b8: lsl             w9, w8, #1
    //     0x7303bc: add             w10, w9, #8
    //     0x7303c0: add             x16, x4, w10, sxtw #1
    //     0x7303c4: ldur            w11, [x16, #0xf]
    //     0x7303c8: add             x11, x11, HEAP, lsl #32
    //     0x7303cc: ldr             x16, [PP, #0x7060]  ; [pp+0x7060] "text"
    //     0x7303d0: cmp             w11, w16
    //     0x7303d4: b.ne            #0x730408
    //     0x7303d8: add             w2, w9, #0xa
    //     0x7303dc: add             x16, x4, w2, sxtw #1
    //     0x7303e0: ldur            w9, [x16, #0xf]
    //     0x7303e4: add             x9, x9, HEAP, lsl #32
    //     0x7303e8: sub             w2, w0, w9
    //     0x7303ec: add             x9, fp, w2, sxtw #2
    //     0x7303f0: ldr             x9, [x9, #8]
    //     0x7303f4: add             w2, w8, #2
    //     0x7303f8: sbfx            x8, x2, #1, #0x1f
    //     0x7303fc: mov             x2, x8
    //     0x730400: mov             x8, x9
    //     0x730404: b               #0x73040c
    //     0x730408: mov             x8, NULL
    //     0x73040c: lsl             x9, x2, #1
    //     0x730410: lsl             w10, w9, #1
    //     0x730414: add             w11, w10, #8
    //     0x730418: add             x16, x4, w11, sxtw #1
    //     0x73041c: ldur            w12, [x16, #0xf]
    //     0x730420: add             x12, x12, HEAP, lsl #32
    //     0x730424: ldr             x16, [PP, #0x4828]  ; [pp+0x4828] "textAlign"
    //     0x730428: cmp             w12, w16
    //     0x73042c: b.ne            #0x730460
    //     0x730430: add             w2, w10, #0xa
    //     0x730434: add             x16, x4, w2, sxtw #1
    //     0x730438: ldur            w10, [x16, #0xf]
    //     0x73043c: add             x10, x10, HEAP, lsl #32
    //     0x730440: sub             w2, w0, w10
    //     0x730444: add             x10, fp, w2, sxtw #2
    //     0x730448: ldr             x10, [x10, #8]
    //     0x73044c: add             w2, w9, #2
    //     0x730450: sbfx            x9, x2, #1, #0x1f
    //     0x730454: mov             x2, x9
    //     0x730458: mov             x9, x10
    //     0x73045c: b               #0x730464
    //     0x730460: ldr             x9, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    //     0x730464: lsl             x10, x2, #1
    //     0x730468: lsl             w11, w10, #1
    //     0x73046c: add             w12, w11, #8
    //     0x730470: add             x16, x4, w12, sxtw #1
    //     0x730474: ldur            w13, [x16, #0xf]
    //     0x730478: add             x13, x13, HEAP, lsl #32
    //     0x73047c: ldr             x16, [PP, #0x4830]  ; [pp+0x4830] "textDirection"
    //     0x730480: cmp             w13, w16
    //     0x730484: b.ne            #0x7304b8
    //     0x730488: add             w2, w11, #0xa
    //     0x73048c: add             x16, x4, w2, sxtw #1
    //     0x730490: ldur            w11, [x16, #0xf]
    //     0x730494: add             x11, x11, HEAP, lsl #32
    //     0x730498: sub             w2, w0, w11
    //     0x73049c: add             x11, fp, w2, sxtw #2
    //     0x7304a0: ldr             x11, [x11, #8]
    //     0x7304a4: add             w2, w10, #2
    //     0x7304a8: sbfx            x10, x2, #1, #0x1f
    //     0x7304ac: mov             x2, x10
    //     0x7304b0: mov             x10, x11
    //     0x7304b4: b               #0x7304bc
    //     0x7304b8: mov             x10, NULL
    //     0x7304bc: lsl             x11, x2, #1
    //     0x7304c0: lsl             w2, w11, #1
    //     0x7304c4: add             w11, w2, #8
    //     0x7304c8: add             x16, x4, w11, sxtw #1
    //     0x7304cc: ldur            w12, [x16, #0xf]
    //     0x7304d0: add             x12, x12, HEAP, lsl #32
    //     0x7304d4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34000] "textScaler"
    //     0x7304d8: ldr             x16, [x16]
    //     0x7304dc: cmp             w12, w16
    //     0x7304e0: b.ne            #0x730508
    //     0x7304e4: add             w11, w2, #0xa
    //     0x7304e8: add             x16, x4, w11, sxtw #1
    //     0x7304ec: ldur            w2, [x16, #0xf]
    //     0x7304f0: add             x2, x2, HEAP, lsl #32
    //     0x7304f4: sub             w4, w0, w2
    //     0x7304f8: add             x0, fp, w4, sxtw #2
    //     0x7304fc: ldr             x0, [x0, #8]
    //     0x730500: mov             x4, x0
    //     0x730504: b               #0x73050c
    //     0x730508: ldr             x4, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    //     0x73050c: add             x2, NULL, #0x20  ; true
    //     0x730510: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x730514: stur            x4, [fp, #-8]
    // 0x73050c: r2 = true
    // 0x730510: r0 = Sentinel
    // 0x730518: CheckStackOverflow
    //     0x730518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73051c: cmp             SP, x16
    //     0x730520: b.ls            #0x730680
    // 0x730524: StoreField: r1->field_b = r2
    //     0x730524: stur            w2, [x1, #0xb]
    // 0x730528: StoreField: r1->field_43 = r0
    //     0x730528: stur            w0, [x1, #0x43]
    // 0x73052c: mov             x0, x8
    // 0x730530: StoreField: r1->field_f = r0
    //     0x730530: stur            w0, [x1, #0xf]
    //     0x730534: ldurb           w16, [x1, #-1]
    //     0x730538: ldurb           w17, [x0, #-1]
    //     0x73053c: and             x16, x17, x16, lsr #2
    //     0x730540: tst             x16, HEAP, lsr #32
    //     0x730544: b.eq            #0x73054c
    //     0x730548: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x73054c: mov             x0, x9
    // 0x730550: ArrayStore: r1[0] = r0  ; List_4
    //     0x730550: stur            w0, [x1, #0x17]
    //     0x730554: ldurb           w16, [x1, #-1]
    //     0x730558: ldurb           w17, [x0, #-1]
    //     0x73055c: and             x16, x17, x16, lsr #2
    //     0x730560: tst             x16, HEAP, lsr #32
    //     0x730564: b.eq            #0x73056c
    //     0x730568: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x73056c: mov             x0, x10
    // 0x730570: StoreField: r1->field_1b = r0
    //     0x730570: stur            w0, [x1, #0x1b]
    //     0x730574: ldurb           w16, [x1, #-1]
    //     0x730578: ldurb           w17, [x0, #-1]
    //     0x73057c: and             x16, x17, x16, lsr #2
    //     0x730580: tst             x16, HEAP, lsr #32
    //     0x730584: b.eq            #0x73058c
    //     0x730588: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x73058c: r0 = LoadClassIdInstr(r4)
    //     0x73058c: ldur            x0, [x4, #-1]
    //     0x730590: ubfx            x0, x0, #0xc, #0x14
    // 0x730594: r16 = Instance__LinearTextScaler
    //     0x730594: ldr             x16, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0x730598: stp             x16, x4, [SP]
    // 0x73059c: mov             lr, x0
    // 0x7305a0: ldr             lr, [x21, lr, lsl #3]
    // 0x7305a4: blr             lr
    // 0x7305a8: tbnz            w0, #4, #0x7305bc
    // 0x7305ac: r0 = _LinearTextScaler()
    //     0x7305ac: bl              #0x6880c0  ; Allocate_LinearTextScalerStub -> _LinearTextScaler (size=0x10)
    // 0x7305b0: d0 = 1.000000
    //     0x7305b0: fmov            d0, #1.00000000
    // 0x7305b4: StoreField: r0->field_7 = d0
    //     0x7305b4: stur            d0, [x0, #7]
    // 0x7305b8: b               #0x7305c0
    // 0x7305bc: ldur            x0, [fp, #-8]
    // 0x7305c0: ldur            x1, [fp, #-0x30]
    // 0x7305c4: r2 = Instance_TextWidthBasis
    //     0x7305c4: add             x2, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0x7305c8: ldr             x2, [x2, #0x1d8]
    // 0x7305cc: StoreField: r1->field_1f = r0
    //     0x7305cc: stur            w0, [x1, #0x1f]
    //     0x7305d0: ldurb           w16, [x1, #-1]
    //     0x7305d4: ldurb           w17, [x0, #-1]
    //     0x7305d8: and             x16, x17, x16, lsr #2
    //     0x7305dc: tst             x16, HEAP, lsr #32
    //     0x7305e0: b.eq            #0x7305e8
    //     0x7305e4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7305e8: ldur            x0, [fp, #-0x18]
    // 0x7305ec: StoreField: r1->field_2b = r0
    //     0x7305ec: stur            w0, [x1, #0x2b]
    //     0x7305f0: tbz             w0, #0, #0x73060c
    //     0x7305f4: ldurb           w16, [x1, #-1]
    //     0x7305f8: ldurb           w17, [x0, #-1]
    //     0x7305fc: and             x16, x17, x16, lsr #2
    //     0x730600: tst             x16, HEAP, lsr #32
    //     0x730604: b.eq            #0x73060c
    //     0x730608: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x73060c: ldur            x0, [fp, #-0x28]
    // 0x730610: StoreField: r1->field_23 = r0
    //     0x730610: stur            w0, [x1, #0x23]
    //     0x730614: ldurb           w16, [x1, #-1]
    //     0x730618: ldurb           w17, [x0, #-1]
    //     0x73061c: and             x16, x17, x16, lsr #2
    //     0x730620: tst             x16, HEAP, lsr #32
    //     0x730624: b.eq            #0x73062c
    //     0x730628: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x73062c: ldur            x0, [fp, #-0x20]
    // 0x730630: StoreField: r1->field_27 = r0
    //     0x730630: stur            w0, [x1, #0x27]
    //     0x730634: ldurb           w16, [x1, #-1]
    //     0x730638: ldurb           w17, [x0, #-1]
    //     0x73063c: and             x16, x17, x16, lsr #2
    //     0x730640: tst             x16, HEAP, lsr #32
    //     0x730644: b.eq            #0x73064c
    //     0x730648: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x73064c: ldur            x0, [fp, #-0x10]
    // 0x730650: StoreField: r1->field_2f = r0
    //     0x730650: stur            w0, [x1, #0x2f]
    //     0x730654: ldurb           w16, [x1, #-1]
    //     0x730658: ldurb           w17, [x0, #-1]
    //     0x73065c: and             x16, x17, x16, lsr #2
    //     0x730660: tst             x16, HEAP, lsr #32
    //     0x730664: b.eq            #0x73066c
    //     0x730668: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x73066c: StoreField: r1->field_33 = r2
    //     0x73066c: stur            w2, [x1, #0x33]
    // 0x730670: r0 = Null
    //     0x730670: mov             x0, NULL
    // 0x730674: LeaveFrame
    //     0x730674: mov             SP, fp
    //     0x730678: ldp             fp, lr, [SP], #0x10
    // 0x73067c: ret
    //     0x73067c: ret             
    // 0x730680: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x730680: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x730684: b               #0x730524
  }
  _ computeDistanceToActualBaseline(/* No info */) {
    // ** addr: 0x741fe4, size: 0x110
    // 0x741fe4: EnterFrame
    //     0x741fe4: stp             fp, lr, [SP, #-0x10]!
    //     0x741fe8: mov             fp, SP
    // 0x741fec: AllocStack(0x18)
    //     0x741fec: sub             SP, SP, #0x18
    // 0x741ff0: CheckStackOverflow
    //     0x741ff0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x741ff4: cmp             SP, x16
    //     0x741ff8: b.ls            #0x7420e0
    // 0x741ffc: LoadField: r0 = r1->field_7
    //     0x741ffc: ldur            w0, [x1, #7]
    // 0x742000: DecompressPointer r0
    //     0x742000: add             x0, x0, HEAP, lsl #32
    // 0x742004: cmp             w0, NULL
    // 0x742008: b.eq            #0x7420e8
    // 0x74200c: LoadField: r1 = r0->field_7
    //     0x74200c: ldur            w1, [x0, #7]
    // 0x742010: DecompressPointer r1
    //     0x742010: add             x1, x1, HEAP, lsl #32
    // 0x742014: LoadField: r0 = r2->field_7
    //     0x742014: ldur            x0, [x2, #7]
    // 0x742018: cmp             x0, #0
    // 0x74201c: b.gt            #0x74207c
    // 0x742020: LoadField: r0 = r1->field_f
    //     0x742020: ldur            w0, [x1, #0xf]
    // 0x742024: DecompressPointer r0
    //     0x742024: add             x0, x0, HEAP, lsl #32
    // 0x742028: stur            x0, [fp, #-0x10]
    // 0x74202c: LoadField: r1 = r0->field_7
    //     0x74202c: ldur            w1, [x0, #7]
    // 0x742030: DecompressPointer r1
    //     0x742030: add             x1, x1, HEAP, lsl #32
    // 0x742034: cmp             w1, NULL
    // 0x742038: b.eq            #0x7420ec
    // 0x74203c: LoadField: r2 = r1->field_7
    //     0x74203c: ldur            x2, [x1, #7]
    // 0x742040: ldr             x1, [x2]
    // 0x742044: stur            x1, [fp, #-8]
    // 0x742048: cbnz            x1, #0x742058
    // 0x74204c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x74204c: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x742050: str             x16, [SP]
    // 0x742054: r0 = _throwNew()
    //     0x742054: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x742058: ldur            x0, [fp, #-8]
    // 0x74205c: stur            x0, [fp, #-8]
    // 0x742060: r1 = <Never>
    //     0x742060: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x742064: r0 = Pointer()
    //     0x742064: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x742068: mov             x1, x0
    // 0x74206c: ldur            x0, [fp, #-8]
    // 0x742070: StoreField: r1->field_7 = r0
    //     0x742070: stur            x0, [x1, #7]
    // 0x742074: r0 = _alphabeticBaseline$Getter$FfiNative()
    //     0x742074: bl              #0x742184  ; [dart:ui] _NativeParagraph::_alphabeticBaseline$Getter$FfiNative
    // 0x742078: b               #0x7420d4
    // 0x74207c: LoadField: r0 = r1->field_f
    //     0x74207c: ldur            w0, [x1, #0xf]
    // 0x742080: DecompressPointer r0
    //     0x742080: add             x0, x0, HEAP, lsl #32
    // 0x742084: stur            x0, [fp, #-0x10]
    // 0x742088: LoadField: r1 = r0->field_7
    //     0x742088: ldur            w1, [x0, #7]
    // 0x74208c: DecompressPointer r1
    //     0x74208c: add             x1, x1, HEAP, lsl #32
    // 0x742090: cmp             w1, NULL
    // 0x742094: b.eq            #0x7420f0
    // 0x742098: LoadField: r2 = r1->field_7
    //     0x742098: ldur            x2, [x1, #7]
    // 0x74209c: ldr             x1, [x2]
    // 0x7420a0: stur            x1, [fp, #-8]
    // 0x7420a4: cbnz            x1, #0x7420b4
    // 0x7420a8: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7420a8: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7420ac: str             x16, [SP]
    // 0x7420b0: r0 = _throwNew()
    //     0x7420b0: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x7420b4: ldur            x0, [fp, #-8]
    // 0x7420b8: stur            x0, [fp, #-8]
    // 0x7420bc: r1 = <Never>
    //     0x7420bc: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7420c0: r0 = Pointer()
    //     0x7420c0: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7420c4: mov             x1, x0
    // 0x7420c8: ldur            x0, [fp, #-8]
    // 0x7420cc: StoreField: r1->field_7 = r0
    //     0x7420cc: stur            x0, [x1, #7]
    // 0x7420d0: r0 = _ideographicBaseline$Getter$FfiNative()
    //     0x7420d0: bl              #0x7420f4  ; [dart:ui] _NativeParagraph::_ideographicBaseline$Getter$FfiNative
    // 0x7420d4: LeaveFrame
    //     0x7420d4: mov             SP, fp
    //     0x7420d8: ldp             fp, lr, [SP], #0x10
    // 0x7420dc: ret
    //     0x7420dc: ret             
    // 0x7420e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7420e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7420e4: b               #0x741ffc
    // 0x7420e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7420e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7420ec: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7420ec: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7420f0: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7420f0: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  get _ maxIntrinsicWidth(/* No info */) {
    // ** addr: 0x74f774, size: 0xa4
    // 0x74f774: EnterFrame
    //     0x74f774: stp             fp, lr, [SP, #-0x10]!
    //     0x74f778: mov             fp, SP
    // 0x74f77c: AllocStack(0x18)
    //     0x74f77c: sub             SP, SP, #0x18
    // 0x74f780: CheckStackOverflow
    //     0x74f780: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74f784: cmp             SP, x16
    //     0x74f788: b.ls            #0x74f808
    // 0x74f78c: LoadField: r0 = r1->field_7
    //     0x74f78c: ldur            w0, [x1, #7]
    // 0x74f790: DecompressPointer r0
    //     0x74f790: add             x0, x0, HEAP, lsl #32
    // 0x74f794: cmp             w0, NULL
    // 0x74f798: b.eq            #0x74f810
    // 0x74f79c: LoadField: r1 = r0->field_7
    //     0x74f79c: ldur            w1, [x0, #7]
    // 0x74f7a0: DecompressPointer r1
    //     0x74f7a0: add             x1, x1, HEAP, lsl #32
    // 0x74f7a4: LoadField: r0 = r1->field_f
    //     0x74f7a4: ldur            w0, [x1, #0xf]
    // 0x74f7a8: DecompressPointer r0
    //     0x74f7a8: add             x0, x0, HEAP, lsl #32
    // 0x74f7ac: stur            x0, [fp, #-0x10]
    // 0x74f7b0: LoadField: r1 = r0->field_7
    //     0x74f7b0: ldur            w1, [x0, #7]
    // 0x74f7b4: DecompressPointer r1
    //     0x74f7b4: add             x1, x1, HEAP, lsl #32
    // 0x74f7b8: cmp             w1, NULL
    // 0x74f7bc: b.eq            #0x74f814
    // 0x74f7c0: LoadField: r2 = r1->field_7
    //     0x74f7c0: ldur            x2, [x1, #7]
    // 0x74f7c4: ldr             x1, [x2]
    // 0x74f7c8: stur            x1, [fp, #-8]
    // 0x74f7cc: cbnz            x1, #0x74f7dc
    // 0x74f7d0: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x74f7d0: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x74f7d4: str             x16, [SP]
    // 0x74f7d8: r0 = _throwNew()
    //     0x74f7d8: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x74f7dc: ldur            x0, [fp, #-8]
    // 0x74f7e0: stur            x0, [fp, #-8]
    // 0x74f7e4: r1 = <Never>
    //     0x74f7e4: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x74f7e8: r0 = Pointer()
    //     0x74f7e8: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x74f7ec: mov             x1, x0
    // 0x74f7f0: ldur            x0, [fp, #-8]
    // 0x74f7f4: StoreField: r1->field_7 = r0
    //     0x74f7f4: stur            x0, [x1, #7]
    // 0x74f7f8: r0 = _maxIntrinsicWidth$Getter$FfiNative()
    //     0x74f7f8: bl              #0x67e368  ; [dart:ui] _NativeParagraph::_maxIntrinsicWidth$Getter$FfiNative
    // 0x74f7fc: LeaveFrame
    //     0x74f7fc: mov             SP, fp
    //     0x74f800: ldp             fp, lr, [SP], #0x10
    // 0x74f804: ret
    //     0x74f804: ret             
    // 0x74f808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74f808: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74f80c: b               #0x74f78c
    // 0x74f810: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74f810: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x74f814: r0 = NullErrorSharedWithoutFPURegs()
    //     0x74f814: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ getWordBoundary(/* No info */) {
    // ** addr: 0x75cb80, size: 0x54
    // 0x75cb80: EnterFrame
    //     0x75cb80: stp             fp, lr, [SP, #-0x10]!
    //     0x75cb84: mov             fp, SP
    // 0x75cb88: CheckStackOverflow
    //     0x75cb88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75cb8c: cmp             SP, x16
    //     0x75cb90: b.ls            #0x75cbc8
    // 0x75cb94: LoadField: r0 = r1->field_7
    //     0x75cb94: ldur            w0, [x1, #7]
    // 0x75cb98: DecompressPointer r0
    //     0x75cb98: add             x0, x0, HEAP, lsl #32
    // 0x75cb9c: cmp             w0, NULL
    // 0x75cba0: b.eq            #0x75cbd0
    // 0x75cba4: LoadField: r1 = r0->field_7
    //     0x75cba4: ldur            w1, [x0, #7]
    // 0x75cba8: DecompressPointer r1
    //     0x75cba8: add             x1, x1, HEAP, lsl #32
    // 0x75cbac: LoadField: r0 = r1->field_f
    //     0x75cbac: ldur            w0, [x1, #0xf]
    // 0x75cbb0: DecompressPointer r0
    //     0x75cbb0: add             x0, x0, HEAP, lsl #32
    // 0x75cbb4: mov             x1, x0
    // 0x75cbb8: r0 = getWordBoundary()
    //     0x75cbb8: bl              #0x75c5a0  ; [dart:ui] _NativeParagraph::getWordBoundary
    // 0x75cbbc: LeaveFrame
    //     0x75cbbc: mov             SP, fp
    //     0x75cbc0: ldp             fp, lr, [SP], #0x10
    // 0x75cbc4: ret
    //     0x75cbc4: ret             
    // 0x75cbc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75cbc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75cbcc: b               #0x75cb94
    // 0x75cbd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75cbd0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ inlinePlaceholderBoxes(/* No info */) {
    // ** addr: 0x770680, size: 0x134
    // 0x770680: EnterFrame
    //     0x770680: stp             fp, lr, [SP, #-0x10]!
    //     0x770684: mov             fp, SP
    // 0x770688: AllocStack(0x30)
    //     0x770688: sub             SP, SP, #0x30
    // 0x77068c: CheckStackOverflow
    //     0x77068c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x770690: cmp             SP, x16
    //     0x770694: b.ls            #0x7707ac
    // 0x770698: LoadField: r0 = r1->field_7
    //     0x770698: ldur            w0, [x1, #7]
    // 0x77069c: DecompressPointer r0
    //     0x77069c: add             x0, x0, HEAP, lsl #32
    // 0x7706a0: stur            x0, [fp, #-8]
    // 0x7706a4: cmp             w0, NULL
    // 0x7706a8: b.ne            #0x7706bc
    // 0x7706ac: r0 = Null
    //     0x7706ac: mov             x0, NULL
    // 0x7706b0: LeaveFrame
    //     0x7706b0: mov             SP, fp
    //     0x7706b4: ldp             fp, lr, [SP], #0x10
    // 0x7706b8: ret
    //     0x7706b8: ret             
    // 0x7706bc: mov             x1, x0
    // 0x7706c0: r0 = paintOffset()
    //     0x7706c0: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0x7706c4: stur            x0, [fp, #-0x10]
    // 0x7706c8: r1 = 1
    //     0x7706c8: movz            x1, #0x1
    // 0x7706cc: r0 = AllocateContext()
    //     0x7706cc: bl              #0xec126c  ; AllocateContextStub
    // 0x7706d0: mov             x2, x0
    // 0x7706d4: ldur            x0, [fp, #-0x10]
    // 0x7706d8: stur            x2, [fp, #-0x18]
    // 0x7706dc: StoreField: r2->field_f = r0
    //     0x7706dc: stur            w0, [x2, #0xf]
    // 0x7706e0: LoadField: d0 = r0->field_7
    //     0x7706e0: ldur            d0, [x0, #7]
    // 0x7706e4: mov             x1, v0.d[0]
    // 0x7706e8: and             x1, x1, #0x7fffffffffffffff
    // 0x7706ec: r17 = 9218868437227405312
    //     0x7706ec: orr             x17, xzr, #0x7ff0000000000000
    // 0x7706f0: cmp             x1, x17
    // 0x7706f4: b.eq            #0x770794
    // 0x7706f8: fcmp            d0, d0
    // 0x7706fc: b.vs            #0x770794
    // 0x770700: LoadField: d0 = r0->field_f
    //     0x770700: ldur            d0, [x0, #0xf]
    // 0x770704: mov             x1, v0.d[0]
    // 0x770708: and             x1, x1, #0x7fffffffffffffff
    // 0x77070c: r17 = 9218868437227405312
    //     0x77070c: orr             x17, xzr, #0x7ff0000000000000
    // 0x770710: cmp             x1, x17
    // 0x770714: b.eq            #0x770794
    // 0x770718: fcmp            d0, d0
    // 0x77071c: b.vs            #0x770794
    // 0x770720: ldur            x1, [fp, #-8]
    // 0x770724: r0 = inlinePlaceholderBoxes()
    //     0x770724: bl              #0x7707b4  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::inlinePlaceholderBoxes
    // 0x770728: stur            x0, [fp, #-8]
    // 0x77072c: ldur            x16, [fp, #-0x10]
    // 0x770730: r30 = Instance_Offset
    //     0x770730: ldr             lr, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x770734: stp             lr, x16, [SP]
    // 0x770738: r0 = ==()
    //     0x770738: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0x77073c: tbnz            w0, #4, #0x770750
    // 0x770740: ldur            x0, [fp, #-8]
    // 0x770744: LeaveFrame
    //     0x770744: mov             SP, fp
    //     0x770748: ldp             fp, lr, [SP], #0x10
    // 0x77074c: ret
    //     0x77074c: ret             
    // 0x770750: ldur            x2, [fp, #-0x18]
    // 0x770754: r1 = Function '<anonymous closure>':.
    //     0x770754: add             x1, PP, #0x4e, lsl #12  ; [pp+0x4ed98] AnonymousClosure: (0x67c26c), in [package:flutter/src/painting/text_painter.dart] TextPainter::getBoxesForSelection (0x67b8c8)
    //     0x770758: ldr             x1, [x1, #0xd98]
    // 0x77075c: r0 = AllocateClosure()
    //     0x77075c: bl              #0xec1630  ; AllocateClosureStub
    // 0x770760: r16 = <TextBox>
    //     0x770760: ldr             x16, [PP, #0x4858]  ; [pp+0x4858] TypeArguments: <TextBox>
    // 0x770764: ldur            lr, [fp, #-8]
    // 0x770768: stp             lr, x16, [SP, #8]
    // 0x77076c: str             x0, [SP]
    // 0x770770: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x770770: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x770774: r0 = map()
    //     0x770774: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x770778: LoadField: r1 = r0->field_7
    //     0x770778: ldur            w1, [x0, #7]
    // 0x77077c: DecompressPointer r1
    //     0x77077c: add             x1, x1, HEAP, lsl #32
    // 0x770780: mov             x2, x0
    // 0x770784: r0 = _List.of()
    //     0x770784: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0x770788: LeaveFrame
    //     0x770788: mov             SP, fp
    //     0x77078c: ldp             fp, lr, [SP], #0x10
    // 0x770790: ret
    //     0x770790: ret             
    // 0x770794: r1 = <TextBox>
    //     0x770794: ldr             x1, [PP, #0x4858]  ; [pp+0x4858] TypeArguments: <TextBox>
    // 0x770798: r2 = 0
    //     0x770798: movz            x2, #0
    // 0x77079c: r0 = _GrowableList()
    //     0x77079c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7707a0: LeaveFrame
    //     0x7707a0: mov             SP, fp
    //     0x7707a4: ldp             fp, lr, [SP], #0x10
    // 0x7707a8: ret
    //     0x7707a8: ret             
    // 0x7707ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7707ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7707b0: b               #0x770698
  }
  _ dispose(/* No info */) {
    // ** addr: 0x772e34, size: 0x130
    // 0x772e34: EnterFrame
    //     0x772e34: stp             fp, lr, [SP, #-0x10]!
    //     0x772e38: mov             fp, SP
    // 0x772e3c: AllocStack(0x20)
    //     0x772e3c: sub             SP, SP, #0x20
    // 0x772e40: SetupParameters(TextPainter this /* r1 => r1, fp-0x18 */)
    //     0x772e40: stur            x1, [fp, #-0x18]
    // 0x772e44: CheckStackOverflow
    //     0x772e44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x772e48: cmp             SP, x16
    //     0x772e4c: b.ls            #0x772f54
    // 0x772e50: LoadField: r0 = r1->field_3f
    //     0x772e50: ldur            w0, [x1, #0x3f]
    // 0x772e54: DecompressPointer r0
    //     0x772e54: add             x0, x0, HEAP, lsl #32
    // 0x772e58: stur            x0, [fp, #-0x10]
    // 0x772e5c: cmp             w0, NULL
    // 0x772e60: b.ne            #0x772e6c
    // 0x772e64: mov             x0, x1
    // 0x772e68: b               #0x772ebc
    // 0x772e6c: LoadField: r2 = r0->field_7
    //     0x772e6c: ldur            w2, [x0, #7]
    // 0x772e70: DecompressPointer r2
    //     0x772e70: add             x2, x2, HEAP, lsl #32
    // 0x772e74: cmp             w2, NULL
    // 0x772e78: b.eq            #0x772f5c
    // 0x772e7c: LoadField: r3 = r2->field_7
    //     0x772e7c: ldur            x3, [x2, #7]
    // 0x772e80: ldr             x2, [x3]
    // 0x772e84: stur            x2, [fp, #-8]
    // 0x772e88: cbnz            x2, #0x772e98
    // 0x772e8c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x772e8c: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x772e90: str             x16, [SP]
    // 0x772e94: r0 = _throwNew()
    //     0x772e94: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x772e98: ldur            x0, [fp, #-8]
    // 0x772e9c: stur            x0, [fp, #-8]
    // 0x772ea0: r1 = <Never>
    //     0x772ea0: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x772ea4: r0 = Pointer()
    //     0x772ea4: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x772ea8: mov             x1, x0
    // 0x772eac: ldur            x0, [fp, #-8]
    // 0x772eb0: StoreField: r1->field_7 = r0
    //     0x772eb0: stur            x0, [x1, #7]
    // 0x772eb4: r0 = __dispose$Method$FfiNative()
    //     0x772eb4: bl              #0x72f87c  ; [dart:ui] _NativeParagraph::__dispose$Method$FfiNative
    // 0x772eb8: ldur            x0, [fp, #-0x18]
    // 0x772ebc: StoreField: r0->field_3f = rNULL
    //     0x772ebc: stur            NULL, [x0, #0x3f]
    // 0x772ec0: LoadField: r1 = r0->field_7
    //     0x772ec0: ldur            w1, [x0, #7]
    // 0x772ec4: DecompressPointer r1
    //     0x772ec4: add             x1, x1, HEAP, lsl #32
    // 0x772ec8: cmp             w1, NULL
    // 0x772ecc: b.ne            #0x772ed8
    // 0x772ed0: mov             x1, x0
    // 0x772ed4: b               #0x772f3c
    // 0x772ed8: LoadField: r2 = r1->field_7
    //     0x772ed8: ldur            w2, [x1, #7]
    // 0x772edc: DecompressPointer r2
    //     0x772edc: add             x2, x2, HEAP, lsl #32
    // 0x772ee0: LoadField: r1 = r2->field_f
    //     0x772ee0: ldur            w1, [x2, #0xf]
    // 0x772ee4: DecompressPointer r1
    //     0x772ee4: add             x1, x1, HEAP, lsl #32
    // 0x772ee8: stur            x1, [fp, #-0x10]
    // 0x772eec: LoadField: r2 = r1->field_7
    //     0x772eec: ldur            w2, [x1, #7]
    // 0x772ef0: DecompressPointer r2
    //     0x772ef0: add             x2, x2, HEAP, lsl #32
    // 0x772ef4: cmp             w2, NULL
    // 0x772ef8: b.eq            #0x772f60
    // 0x772efc: LoadField: r3 = r2->field_7
    //     0x772efc: ldur            x3, [x2, #7]
    // 0x772f00: ldr             x2, [x3]
    // 0x772f04: stur            x2, [fp, #-8]
    // 0x772f08: cbnz            x2, #0x772f18
    // 0x772f0c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x772f0c: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x772f10: str             x16, [SP]
    // 0x772f14: r0 = _throwNew()
    //     0x772f14: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x772f18: ldur            x0, [fp, #-8]
    // 0x772f1c: stur            x0, [fp, #-8]
    // 0x772f20: r1 = <Never>
    //     0x772f20: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x772f24: r0 = Pointer()
    //     0x772f24: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x772f28: mov             x1, x0
    // 0x772f2c: ldur            x0, [fp, #-8]
    // 0x772f30: StoreField: r1->field_7 = r0
    //     0x772f30: stur            x0, [x1, #7]
    // 0x772f34: r0 = __dispose$Method$FfiNative()
    //     0x772f34: bl              #0x72f87c  ; [dart:ui] _NativeParagraph::__dispose$Method$FfiNative
    // 0x772f38: ldur            x1, [fp, #-0x18]
    // 0x772f3c: StoreField: r1->field_7 = rNULL
    //     0x772f3c: stur            NULL, [x1, #7]
    // 0x772f40: StoreField: r1->field_f = rNULL
    //     0x772f40: stur            NULL, [x1, #0xf]
    // 0x772f44: r0 = Null
    //     0x772f44: mov             x0, NULL
    // 0x772f48: LeaveFrame
    //     0x772f48: mov             SP, fp
    //     0x772f4c: ldp             fp, lr, [SP], #0x10
    // 0x772f50: ret
    //     0x772f50: ret             
    // 0x772f54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x772f54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x772f58: b               #0x772e50
    // 0x772f5c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x772f5c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x772f60: r0 = NullErrorSharedWithoutFPURegs()
    //     0x772f60: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  get _ didExceedMaxLines(/* No info */) {
    // ** addr: 0x773ad4, size: 0xa4
    // 0x773ad4: EnterFrame
    //     0x773ad4: stp             fp, lr, [SP, #-0x10]!
    //     0x773ad8: mov             fp, SP
    // 0x773adc: AllocStack(0x18)
    //     0x773adc: sub             SP, SP, #0x18
    // 0x773ae0: CheckStackOverflow
    //     0x773ae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x773ae4: cmp             SP, x16
    //     0x773ae8: b.ls            #0x773b68
    // 0x773aec: LoadField: r0 = r1->field_7
    //     0x773aec: ldur            w0, [x1, #7]
    // 0x773af0: DecompressPointer r0
    //     0x773af0: add             x0, x0, HEAP, lsl #32
    // 0x773af4: cmp             w0, NULL
    // 0x773af8: b.eq            #0x773b70
    // 0x773afc: LoadField: r1 = r0->field_7
    //     0x773afc: ldur            w1, [x0, #7]
    // 0x773b00: DecompressPointer r1
    //     0x773b00: add             x1, x1, HEAP, lsl #32
    // 0x773b04: LoadField: r0 = r1->field_f
    //     0x773b04: ldur            w0, [x1, #0xf]
    // 0x773b08: DecompressPointer r0
    //     0x773b08: add             x0, x0, HEAP, lsl #32
    // 0x773b0c: stur            x0, [fp, #-0x10]
    // 0x773b10: LoadField: r1 = r0->field_7
    //     0x773b10: ldur            w1, [x0, #7]
    // 0x773b14: DecompressPointer r1
    //     0x773b14: add             x1, x1, HEAP, lsl #32
    // 0x773b18: cmp             w1, NULL
    // 0x773b1c: b.eq            #0x773b74
    // 0x773b20: LoadField: r2 = r1->field_7
    //     0x773b20: ldur            x2, [x1, #7]
    // 0x773b24: ldr             x1, [x2]
    // 0x773b28: stur            x1, [fp, #-8]
    // 0x773b2c: cbnz            x1, #0x773b3c
    // 0x773b30: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x773b30: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x773b34: str             x16, [SP]
    // 0x773b38: r0 = _throwNew()
    //     0x773b38: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x773b3c: ldur            x0, [fp, #-8]
    // 0x773b40: stur            x0, [fp, #-8]
    // 0x773b44: r1 = <Never>
    //     0x773b44: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x773b48: r0 = Pointer()
    //     0x773b48: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x773b4c: mov             x1, x0
    // 0x773b50: ldur            x0, [fp, #-8]
    // 0x773b54: StoreField: r1->field_7 = r0
    //     0x773b54: stur            x0, [x1, #7]
    // 0x773b58: r0 = _didExceedMaxLines$Getter$FfiNative()
    //     0x773b58: bl              #0x773b78  ; [dart:ui] _NativeParagraph::_didExceedMaxLines$Getter$FfiNative
    // 0x773b5c: LeaveFrame
    //     0x773b5c: mov             SP, fp
    //     0x773b60: ldp             fp, lr, [SP], #0x10
    // 0x773b64: ret
    //     0x773b64: ret             
    // 0x773b68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x773b68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x773b6c: b               #0x773aec
    // 0x773b70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x773b70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x773b74: r0 = NullErrorSharedWithoutFPURegs()
    //     0x773b74: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ paint(/* No info */) {
    // ** addr: 0x798cf0, size: 0x238
    // 0x798cf0: EnterFrame
    //     0x798cf0: stp             fp, lr, [SP, #-0x10]!
    //     0x798cf4: mov             fp, SP
    // 0x798cf8: AllocStack(0x48)
    //     0x798cf8: sub             SP, SP, #0x48
    // 0x798cfc: SetupParameters(TextPainter this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0x798cfc: mov             x0, x3
    //     0x798d00: stur            x3, [fp, #-0x20]
    //     0x798d04: mov             x3, x1
    //     0x798d08: stur            x1, [fp, #-0x10]
    //     0x798d0c: stur            x2, [fp, #-0x18]
    // 0x798d10: CheckStackOverflow
    //     0x798d10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x798d14: cmp             SP, x16
    //     0x798d18: b.ls            #0x798f14
    // 0x798d1c: LoadField: r4 = r3->field_7
    //     0x798d1c: ldur            w4, [x3, #7]
    // 0x798d20: DecompressPointer r4
    //     0x798d20: add             x4, x4, HEAP, lsl #32
    // 0x798d24: stur            x4, [fp, #-8]
    // 0x798d28: cmp             w4, NULL
    // 0x798d2c: b.eq            #0x798ef4
    // 0x798d30: mov             x1, x4
    // 0x798d34: r0 = paintOffset()
    //     0x798d34: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0x798d38: LoadField: d0 = r0->field_7
    //     0x798d38: ldur            d0, [x0, #7]
    // 0x798d3c: mov             x0, v0.d[0]
    // 0x798d40: and             x0, x0, #0x7fffffffffffffff
    // 0x798d44: r17 = 9218868437227405312
    //     0x798d44: orr             x17, xzr, #0x7ff0000000000000
    // 0x798d48: cmp             x0, x17
    // 0x798d4c: b.eq            #0x798ee4
    // 0x798d50: fcmp            d0, d0
    // 0x798d54: b.vs            #0x798ee4
    // 0x798d58: ldur            x1, [fp, #-8]
    // 0x798d5c: r0 = paintOffset()
    //     0x798d5c: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0x798d60: LoadField: d0 = r0->field_f
    //     0x798d60: ldur            d0, [x0, #0xf]
    // 0x798d64: mov             x0, v0.d[0]
    // 0x798d68: and             x0, x0, #0x7fffffffffffffff
    // 0x798d6c: r17 = 9218868437227405312
    //     0x798d6c: orr             x17, xzr, #0x7ff0000000000000
    // 0x798d70: cmp             x0, x17
    // 0x798d74: b.eq            #0x798ee4
    // 0x798d78: fcmp            d0, d0
    // 0x798d7c: b.vs            #0x798ee4
    // 0x798d80: ldur            x1, [fp, #-0x10]
    // 0x798d84: LoadField: r0 = r1->field_b
    //     0x798d84: ldur            w0, [x1, #0xb]
    // 0x798d88: DecompressPointer r0
    //     0x798d88: add             x0, x0, HEAP, lsl #32
    // 0x798d8c: tbnz            w0, #4, #0x798e9c
    // 0x798d90: ldur            x0, [fp, #-8]
    // 0x798d94: LoadField: r3 = r0->field_7
    //     0x798d94: ldur            w3, [x0, #7]
    // 0x798d98: DecompressPointer r3
    //     0x798d98: add             x3, x3, HEAP, lsl #32
    // 0x798d9c: stur            x3, [fp, #-0x30]
    // 0x798da0: LoadField: r4 = r3->field_f
    //     0x798da0: ldur            w4, [x3, #0xf]
    // 0x798da4: DecompressPointer r4
    //     0x798da4: add             x4, x4, HEAP, lsl #32
    // 0x798da8: stur            x4, [fp, #-0x28]
    // 0x798dac: LoadField: r2 = r1->field_f
    //     0x798dac: ldur            w2, [x1, #0xf]
    // 0x798db0: DecompressPointer r2
    //     0x798db0: add             x2, x2, HEAP, lsl #32
    // 0x798db4: cmp             w2, NULL
    // 0x798db8: b.eq            #0x798f1c
    // 0x798dbc: r0 = _createParagraph()
    //     0x798dbc: bl              #0x67cb64  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_createParagraph
    // 0x798dc0: ldur            x1, [fp, #-8]
    // 0x798dc4: stur            x0, [fp, #-0x10]
    // 0x798dc8: LoadField: d0 = r1->field_b
    //     0x798dc8: ldur            d0, [x1, #0xb]
    // 0x798dcc: stur            d0, [fp, #-0x40]
    // 0x798dd0: LoadField: r2 = r0->field_7
    //     0x798dd0: ldur            w2, [x0, #7]
    // 0x798dd4: DecompressPointer r2
    //     0x798dd4: add             x2, x2, HEAP, lsl #32
    // 0x798dd8: cmp             w2, NULL
    // 0x798ddc: b.eq            #0x798f20
    // 0x798de0: LoadField: r3 = r2->field_7
    //     0x798de0: ldur            x3, [x2, #7]
    // 0x798de4: ldr             x2, [x3]
    // 0x798de8: stur            x2, [fp, #-0x38]
    // 0x798dec: cbnz            x2, #0x798dfc
    // 0x798df0: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x798df0: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x798df4: str             x16, [SP]
    // 0x798df8: r0 = _throwNew()
    //     0x798df8: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x798dfc: ldur            x0, [fp, #-0x30]
    // 0x798e00: ldur            x2, [fp, #-0x28]
    // 0x798e04: ldur            x3, [fp, #-0x38]
    // 0x798e08: stur            x3, [fp, #-0x38]
    // 0x798e0c: r1 = <Never>
    //     0x798e0c: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x798e10: r0 = Pointer()
    //     0x798e10: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x798e14: mov             x1, x0
    // 0x798e18: ldur            x0, [fp, #-0x38]
    // 0x798e1c: StoreField: r1->field_7 = r0
    //     0x798e1c: stur            x0, [x1, #7]
    // 0x798e20: ldur            d0, [fp, #-0x40]
    // 0x798e24: r0 = __layout$Method$FfiNative()
    //     0x798e24: bl              #0x67cad4  ; [dart:ui] _NativeParagraph::__layout$Method$FfiNative
    // 0x798e28: ldur            x0, [fp, #-0x10]
    // 0x798e2c: ldur            x1, [fp, #-0x30]
    // 0x798e30: StoreField: r1->field_f = r0
    //     0x798e30: stur            w0, [x1, #0xf]
    //     0x798e34: ldurb           w16, [x1, #-1]
    //     0x798e38: ldurb           w17, [x0, #-1]
    //     0x798e3c: and             x16, x17, x16, lsr #2
    //     0x798e40: tst             x16, HEAP, lsr #32
    //     0x798e44: b.eq            #0x798e4c
    //     0x798e48: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x798e4c: ldur            x0, [fp, #-0x28]
    // 0x798e50: LoadField: r1 = r0->field_7
    //     0x798e50: ldur            w1, [x0, #7]
    // 0x798e54: DecompressPointer r1
    //     0x798e54: add             x1, x1, HEAP, lsl #32
    // 0x798e58: cmp             w1, NULL
    // 0x798e5c: b.eq            #0x798f24
    // 0x798e60: LoadField: r2 = r1->field_7
    //     0x798e60: ldur            x2, [x1, #7]
    // 0x798e64: ldr             x1, [x2]
    // 0x798e68: stur            x1, [fp, #-0x38]
    // 0x798e6c: cbnz            x1, #0x798e7c
    // 0x798e70: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x798e70: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x798e74: str             x16, [SP]
    // 0x798e78: r0 = _throwNew()
    //     0x798e78: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x798e7c: ldur            x0, [fp, #-0x38]
    // 0x798e80: stur            x0, [fp, #-0x38]
    // 0x798e84: r1 = <Never>
    //     0x798e84: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x798e88: r0 = Pointer()
    //     0x798e88: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x798e8c: mov             x1, x0
    // 0x798e90: ldur            x0, [fp, #-0x38]
    // 0x798e94: StoreField: r1->field_7 = r0
    //     0x798e94: stur            x0, [x1, #7]
    // 0x798e98: r0 = __dispose$Method$FfiNative()
    //     0x798e98: bl              #0x72f87c  ; [dart:ui] _NativeParagraph::__dispose$Method$FfiNative
    // 0x798e9c: ldur            x1, [fp, #-8]
    // 0x798ea0: LoadField: r0 = r1->field_7
    //     0x798ea0: ldur            w0, [x1, #7]
    // 0x798ea4: DecompressPointer r0
    //     0x798ea4: add             x0, x0, HEAP, lsl #32
    // 0x798ea8: LoadField: r2 = r0->field_f
    //     0x798ea8: ldur            w2, [x0, #0xf]
    // 0x798eac: DecompressPointer r2
    //     0x798eac: add             x2, x2, HEAP, lsl #32
    // 0x798eb0: stur            x2, [fp, #-0x10]
    // 0x798eb4: r0 = paintOffset()
    //     0x798eb4: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0x798eb8: ldur            x1, [fp, #-0x20]
    // 0x798ebc: mov             x2, x0
    // 0x798ec0: r0 = +()
    //     0x798ec0: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x798ec4: ldur            x1, [fp, #-0x18]
    // 0x798ec8: ldur            x2, [fp, #-0x10]
    // 0x798ecc: mov             x3, x0
    // 0x798ed0: r0 = drawParagraph()
    //     0x798ed0: bl              #0x798f28  ; [dart:ui] _NativeCanvas::drawParagraph
    // 0x798ed4: r0 = Null
    //     0x798ed4: mov             x0, NULL
    // 0x798ed8: LeaveFrame
    //     0x798ed8: mov             SP, fp
    //     0x798edc: ldp             fp, lr, [SP], #0x10
    // 0x798ee0: ret
    //     0x798ee0: ret             
    // 0x798ee4: r0 = Null
    //     0x798ee4: mov             x0, NULL
    // 0x798ee8: LeaveFrame
    //     0x798ee8: mov             SP, fp
    //     0x798eec: ldp             fp, lr, [SP], #0x10
    // 0x798ef0: ret
    //     0x798ef0: ret             
    // 0x798ef4: r0 = StateError()
    //     0x798ef4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x798ef8: mov             x1, x0
    // 0x798efc: r0 = "TextPainter.paint called when text geometry was not yet calculated.\nPlease call layout() before paint() to position the text before painting it."
    //     0x798efc: add             x0, PP, #0x4e, lsl #12  ; [pp+0x4ed10] "TextPainter.paint called when text geometry was not yet calculated.\nPlease call layout() before paint() to position the text before painting it."
    //     0x798f00: ldr             x0, [x0, #0xd10]
    // 0x798f04: StoreField: r1->field_b = r0
    //     0x798f04: stur            w0, [x1, #0xb]
    // 0x798f08: mov             x0, x1
    // 0x798f0c: r0 = Throw()
    //     0x798f0c: bl              #0xec04b8  ; ThrowStub
    // 0x798f10: brk             #0
    // 0x798f14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x798f14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x798f18: b               #0x798d1c
    // 0x798f1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x798f1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x798f20: r0 = NullErrorSharedWithFPURegs()
    //     0x798f20: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0x798f24: r0 = NullErrorSharedWithoutFPURegs()
    //     0x798f24: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ getOffsetAfter(/* No info */) {
    // ** addr: 0x7df784, size: 0xb8
    // 0x7df784: EnterFrame
    //     0x7df784: stp             fp, lr, [SP, #-0x10]!
    //     0x7df788: mov             fp, SP
    // 0x7df78c: AllocStack(0x8)
    //     0x7df78c: sub             SP, SP, #8
    // 0x7df790: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x7df790: mov             x0, x2
    //     0x7df794: stur            x2, [fp, #-8]
    // 0x7df798: CheckStackOverflow
    //     0x7df798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7df79c: cmp             SP, x16
    //     0x7df7a0: b.ls            #0x7df830
    // 0x7df7a4: LoadField: r2 = r1->field_f
    //     0x7df7a4: ldur            w2, [x1, #0xf]
    // 0x7df7a8: DecompressPointer r2
    //     0x7df7a8: add             x2, x2, HEAP, lsl #32
    // 0x7df7ac: cmp             w2, NULL
    // 0x7df7b0: b.eq            #0x7df838
    // 0x7df7b4: mov             x1, x2
    // 0x7df7b8: mov             x2, x0
    // 0x7df7bc: r0 = codeUnitAt()
    //     0x7df7bc: bl              #0x75c4d8  ; [package:flutter/src/painting/inline_span.dart] InlineSpan::codeUnitAt
    // 0x7df7c0: cmp             w0, NULL
    // 0x7df7c4: b.ne            #0x7df7d8
    // 0x7df7c8: r0 = Null
    //     0x7df7c8: mov             x0, NULL
    // 0x7df7cc: LeaveFrame
    //     0x7df7cc: mov             SP, fp
    //     0x7df7d0: ldp             fp, lr, [SP], #0x10
    // 0x7df7d4: ret
    //     0x7df7d4: ret             
    // 0x7df7d8: r2 = 64512
    //     0x7df7d8: orr             x2, xzr, #0xfc00
    // 0x7df7dc: r3 = LoadInt32Instr(r0)
    //     0x7df7dc: sbfx            x3, x0, #1, #0x1f
    // 0x7df7e0: and             x4, x3, x2
    // 0x7df7e4: ubfx            x4, x4, #0, #0x20
    // 0x7df7e8: r17 = 55296
    //     0x7df7e8: movz            x17, #0xd800
    // 0x7df7ec: cmp             x4, x17
    // 0x7df7f0: b.ne            #0x7df804
    // 0x7df7f4: ldur            x2, [fp, #-8]
    // 0x7df7f8: add             x3, x2, #2
    // 0x7df7fc: mov             x2, x3
    // 0x7df800: b               #0x7df810
    // 0x7df804: ldur            x2, [fp, #-8]
    // 0x7df808: add             x3, x2, #1
    // 0x7df80c: mov             x2, x3
    // 0x7df810: r0 = BoxInt64Instr(r2)
    //     0x7df810: sbfiz           x0, x2, #1, #0x1f
    //     0x7df814: cmp             x2, x0, asr #1
    //     0x7df818: b.eq            #0x7df824
    //     0x7df81c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7df820: stur            x2, [x0, #7]
    // 0x7df824: LeaveFrame
    //     0x7df824: mov             SP, fp
    //     0x7df828: ldp             fp, lr, [SP], #0x10
    // 0x7df82c: ret
    //     0x7df82c: ret             
    // 0x7df830: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7df830: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7df834: b               #0x7df7a4
    // 0x7df838: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7df838: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ getOffsetBefore(/* No info */) {
    // ** addr: 0x7dfc9c, size: 0xb8
    // 0x7dfc9c: EnterFrame
    //     0x7dfc9c: stp             fp, lr, [SP, #-0x10]!
    //     0x7dfca0: mov             fp, SP
    // 0x7dfca4: AllocStack(0x10)
    //     0x7dfca4: sub             SP, SP, #0x10
    // 0x7dfca8: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x7dfca8: mov             x0, x2
    //     0x7dfcac: stur            x2, [fp, #-0x10]
    // 0x7dfcb0: CheckStackOverflow
    //     0x7dfcb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7dfcb4: cmp             SP, x16
    //     0x7dfcb8: b.ls            #0x7dfd48
    // 0x7dfcbc: LoadField: r2 = r1->field_f
    //     0x7dfcbc: ldur            w2, [x1, #0xf]
    // 0x7dfcc0: DecompressPointer r2
    //     0x7dfcc0: add             x2, x2, HEAP, lsl #32
    // 0x7dfcc4: cmp             w2, NULL
    // 0x7dfcc8: b.eq            #0x7dfd50
    // 0x7dfccc: sub             x3, x0, #1
    // 0x7dfcd0: mov             x1, x2
    // 0x7dfcd4: mov             x2, x3
    // 0x7dfcd8: stur            x3, [fp, #-8]
    // 0x7dfcdc: r0 = codeUnitAt()
    //     0x7dfcdc: bl              #0x75c4d8  ; [package:flutter/src/painting/inline_span.dart] InlineSpan::codeUnitAt
    // 0x7dfce0: cmp             w0, NULL
    // 0x7dfce4: b.ne            #0x7dfcf8
    // 0x7dfce8: r0 = Null
    //     0x7dfce8: mov             x0, NULL
    // 0x7dfcec: LeaveFrame
    //     0x7dfcec: mov             SP, fp
    //     0x7dfcf0: ldp             fp, lr, [SP], #0x10
    // 0x7dfcf4: ret
    //     0x7dfcf4: ret             
    // 0x7dfcf8: r2 = 64512
    //     0x7dfcf8: orr             x2, xzr, #0xfc00
    // 0x7dfcfc: r3 = LoadInt32Instr(r0)
    //     0x7dfcfc: sbfx            x3, x0, #1, #0x1f
    // 0x7dfd00: and             x4, x3, x2
    // 0x7dfd04: ubfx            x4, x4, #0, #0x20
    // 0x7dfd08: r17 = 56320
    //     0x7dfd08: movz            x17, #0xdc00
    // 0x7dfd0c: cmp             x4, x17
    // 0x7dfd10: b.ne            #0x7dfd24
    // 0x7dfd14: ldur            x2, [fp, #-0x10]
    // 0x7dfd18: sub             x3, x2, #2
    // 0x7dfd1c: mov             x2, x3
    // 0x7dfd20: b               #0x7dfd28
    // 0x7dfd24: ldur            x2, [fp, #-8]
    // 0x7dfd28: r0 = BoxInt64Instr(r2)
    //     0x7dfd28: sbfiz           x0, x2, #1, #0x1f
    //     0x7dfd2c: cmp             x2, x0, asr #1
    //     0x7dfd30: b.eq            #0x7dfd3c
    //     0x7dfd34: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7dfd38: stur            x2, [x0, #7]
    // 0x7dfd3c: LeaveFrame
    //     0x7dfd3c: mov             SP, fp
    //     0x7dfd40: ldp             fp, lr, [SP], #0x10
    // 0x7dfd44: ret
    //     0x7dfd44: ret             
    // 0x7dfd48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7dfd48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7dfd4c: b               #0x7dfcbc
    // 0x7dfd50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7dfd50: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ getClosestGlyphForOffset(/* No info */) {
    // ** addr: 0x7fdfc4, size: 0x118
    // 0x7fdfc4: EnterFrame
    //     0x7fdfc4: stp             fp, lr, [SP, #-0x10]!
    //     0x7fdfc8: mov             fp, SP
    // 0x7fdfcc: AllocStack(0x30)
    //     0x7fdfcc: sub             SP, SP, #0x30
    // 0x7fdfd0: SetupParameters(dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x7fdfd0: mov             x0, x2
    //     0x7fdfd4: stur            x2, [fp, #-0x18]
    // 0x7fdfd8: CheckStackOverflow
    //     0x7fdfd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fdfdc: cmp             SP, x16
    //     0x7fdfe0: b.ls            #0x7fe0d0
    // 0x7fdfe4: LoadField: r2 = r1->field_7
    //     0x7fdfe4: ldur            w2, [x1, #7]
    // 0x7fdfe8: DecompressPointer r2
    //     0x7fdfe8: add             x2, x2, HEAP, lsl #32
    // 0x7fdfec: stur            x2, [fp, #-0x10]
    // 0x7fdff0: cmp             w2, NULL
    // 0x7fdff4: b.eq            #0x7fe0d8
    // 0x7fdff8: LoadField: r1 = r2->field_7
    //     0x7fdff8: ldur            w1, [x2, #7]
    // 0x7fdffc: DecompressPointer r1
    //     0x7fdffc: add             x1, x1, HEAP, lsl #32
    // 0x7fe000: LoadField: r3 = r1->field_f
    //     0x7fe000: ldur            w3, [x1, #0xf]
    // 0x7fe004: DecompressPointer r3
    //     0x7fe004: add             x3, x3, HEAP, lsl #32
    // 0x7fe008: mov             x1, x2
    // 0x7fe00c: stur            x3, [fp, #-8]
    // 0x7fe010: r0 = paintOffset()
    //     0x7fe010: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0x7fe014: ldur            x1, [fp, #-0x18]
    // 0x7fe018: mov             x2, x0
    // 0x7fe01c: r0 = -()
    //     0x7fe01c: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x7fe020: ldur            x1, [fp, #-8]
    // 0x7fe024: mov             x2, x0
    // 0x7fe028: r0 = getClosestGlyphInfoForOffset()
    //     0x7fe028: bl              #0x7fe0dc  ; [dart:ui] _NativeParagraph::getClosestGlyphInfoForOffset
    // 0x7fe02c: stur            x0, [fp, #-8]
    // 0x7fe030: cmp             w0, NULL
    // 0x7fe034: b.eq            #0x7fe050
    // 0x7fe038: ldur            x1, [fp, #-0x10]
    // 0x7fe03c: r0 = paintOffset()
    //     0x7fe03c: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0x7fe040: r16 = Instance_Offset
    //     0x7fe040: ldr             x16, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7fe044: stp             x16, x0, [SP]
    // 0x7fe048: r0 = ==()
    //     0x7fe048: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0x7fe04c: tbnz            w0, #4, #0x7fe060
    // 0x7fe050: ldur            x0, [fp, #-8]
    // 0x7fe054: LeaveFrame
    //     0x7fe054: mov             SP, fp
    //     0x7fe058: ldp             fp, lr, [SP], #0x10
    // 0x7fe05c: ret
    //     0x7fe05c: ret             
    // 0x7fe060: ldur            x0, [fp, #-8]
    // 0x7fe064: LoadField: r2 = r0->field_7
    //     0x7fe064: ldur            w2, [x0, #7]
    // 0x7fe068: DecompressPointer r2
    //     0x7fe068: add             x2, x2, HEAP, lsl #32
    // 0x7fe06c: ldur            x1, [fp, #-0x10]
    // 0x7fe070: stur            x2, [fp, #-0x18]
    // 0x7fe074: r0 = paintOffset()
    //     0x7fe074: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0x7fe078: ldur            x1, [fp, #-0x18]
    // 0x7fe07c: mov             x2, x0
    // 0x7fe080: r0 = shift()
    //     0x7fe080: bl              #0x67ef20  ; [dart:ui] Rect::shift
    // 0x7fe084: mov             x1, x0
    // 0x7fe088: ldur            x0, [fp, #-8]
    // 0x7fe08c: stur            x1, [fp, #-0x20]
    // 0x7fe090: LoadField: r2 = r0->field_b
    //     0x7fe090: ldur            w2, [x0, #0xb]
    // 0x7fe094: DecompressPointer r2
    //     0x7fe094: add             x2, x2, HEAP, lsl #32
    // 0x7fe098: stur            x2, [fp, #-0x18]
    // 0x7fe09c: LoadField: r3 = r0->field_f
    //     0x7fe09c: ldur            w3, [x0, #0xf]
    // 0x7fe0a0: DecompressPointer r3
    //     0x7fe0a0: add             x3, x3, HEAP, lsl #32
    // 0x7fe0a4: stur            x3, [fp, #-0x10]
    // 0x7fe0a8: r0 = GlyphInfo()
    //     0x7fe0a8: bl              #0x684910  ; AllocateGlyphInfoStub -> GlyphInfo (size=0x14)
    // 0x7fe0ac: ldur            x1, [fp, #-0x20]
    // 0x7fe0b0: StoreField: r0->field_7 = r1
    //     0x7fe0b0: stur            w1, [x0, #7]
    // 0x7fe0b4: ldur            x1, [fp, #-0x18]
    // 0x7fe0b8: StoreField: r0->field_b = r1
    //     0x7fe0b8: stur            w1, [x0, #0xb]
    // 0x7fe0bc: ldur            x1, [fp, #-0x10]
    // 0x7fe0c0: StoreField: r0->field_f = r1
    //     0x7fe0c0: stur            w1, [x0, #0xf]
    // 0x7fe0c4: LeaveFrame
    //     0x7fe0c4: mov             SP, fp
    //     0x7fe0c8: ldp             fp, lr, [SP], #0x10
    // 0x7fe0cc: ret
    //     0x7fe0cc: ret             
    // 0x7fe0d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fe0d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fe0d4: b               #0x7fdfe4
    // 0x7fe0d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fe0d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ computeLineMetrics(/* No info */) {
    // ** addr: 0xa55b78, size: 0x120
    // 0xa55b78: EnterFrame
    //     0xa55b78: stp             fp, lr, [SP, #-0x10]!
    //     0xa55b7c: mov             fp, SP
    // 0xa55b80: AllocStack(0x30)
    //     0xa55b80: sub             SP, SP, #0x30
    // 0xa55b84: CheckStackOverflow
    //     0xa55b84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa55b88: cmp             SP, x16
    //     0xa55b8c: b.ls            #0xa55c8c
    // 0xa55b90: LoadField: r0 = r1->field_7
    //     0xa55b90: ldur            w0, [x1, #7]
    // 0xa55b94: DecompressPointer r0
    //     0xa55b94: add             x0, x0, HEAP, lsl #32
    // 0xa55b98: stur            x0, [fp, #-8]
    // 0xa55b9c: cmp             w0, NULL
    // 0xa55ba0: b.eq            #0xa55c94
    // 0xa55ba4: mov             x1, x0
    // 0xa55ba8: r0 = paintOffset()
    //     0xa55ba8: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0xa55bac: stur            x0, [fp, #-0x10]
    // 0xa55bb0: r1 = 1
    //     0xa55bb0: movz            x1, #0x1
    // 0xa55bb4: r0 = AllocateContext()
    //     0xa55bb4: bl              #0xec126c  ; AllocateContextStub
    // 0xa55bb8: mov             x2, x0
    // 0xa55bbc: ldur            x0, [fp, #-0x10]
    // 0xa55bc0: stur            x2, [fp, #-0x18]
    // 0xa55bc4: StoreField: r2->field_f = r0
    //     0xa55bc4: stur            w0, [x2, #0xf]
    // 0xa55bc8: LoadField: d0 = r0->field_7
    //     0xa55bc8: ldur            d0, [x0, #7]
    // 0xa55bcc: mov             x1, v0.d[0]
    // 0xa55bd0: and             x1, x1, #0x7fffffffffffffff
    // 0xa55bd4: r17 = 9218868437227405312
    //     0xa55bd4: orr             x17, xzr, #0x7ff0000000000000
    // 0xa55bd8: cmp             x1, x17
    // 0xa55bdc: b.eq            #0xa55c78
    // 0xa55be0: fcmp            d0, d0
    // 0xa55be4: b.vs            #0xa55c78
    // 0xa55be8: LoadField: d0 = r0->field_f
    //     0xa55be8: ldur            d0, [x0, #0xf]
    // 0xa55bec: mov             x1, v0.d[0]
    // 0xa55bf0: and             x1, x1, #0x7fffffffffffffff
    // 0xa55bf4: r17 = 9218868437227405312
    //     0xa55bf4: orr             x17, xzr, #0x7ff0000000000000
    // 0xa55bf8: cmp             x1, x17
    // 0xa55bfc: b.eq            #0xa55c78
    // 0xa55c00: fcmp            d0, d0
    // 0xa55c04: b.vs            #0xa55c78
    // 0xa55c08: ldur            x1, [fp, #-8]
    // 0xa55c0c: r0 = lineMetrics()
    //     0xa55c0c: bl              #0xa55c98  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::lineMetrics
    // 0xa55c10: stur            x0, [fp, #-8]
    // 0xa55c14: ldur            x16, [fp, #-0x10]
    // 0xa55c18: r30 = Instance_Offset
    //     0xa55c18: ldr             lr, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xa55c1c: stp             lr, x16, [SP]
    // 0xa55c20: r0 = ==()
    //     0xa55c20: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0xa55c24: tbnz            w0, #4, #0xa55c30
    // 0xa55c28: ldur            x0, [fp, #-8]
    // 0xa55c2c: b               #0xa55c6c
    // 0xa55c30: ldur            x2, [fp, #-0x18]
    // 0xa55c34: r1 = Function '<anonymous closure>':.
    //     0xa55c34: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a4d0] AnonymousClosure: (0xa562c0), in [package:flutter/src/painting/text_painter.dart] TextPainter::computeLineMetrics (0xa55b78)
    //     0xa55c38: ldr             x1, [x1, #0x4d0]
    // 0xa55c3c: r0 = AllocateClosure()
    //     0xa55c3c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa55c40: r16 = <LineMetrics>
    //     0xa55c40: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3a4d8] TypeArguments: <LineMetrics>
    //     0xa55c44: ldr             x16, [x16, #0x4d8]
    // 0xa55c48: ldur            lr, [fp, #-8]
    // 0xa55c4c: stp             lr, x16, [SP, #8]
    // 0xa55c50: str             x0, [SP]
    // 0xa55c54: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa55c54: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa55c58: r0 = map()
    //     0xa55c58: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xa55c5c: LoadField: r1 = r0->field_7
    //     0xa55c5c: ldur            w1, [x0, #7]
    // 0xa55c60: DecompressPointer r1
    //     0xa55c60: add             x1, x1, HEAP, lsl #32
    // 0xa55c64: mov             x2, x0
    // 0xa55c68: r0 = _List.of()
    //     0xa55c68: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0xa55c6c: LeaveFrame
    //     0xa55c6c: mov             SP, fp
    //     0xa55c70: ldp             fp, lr, [SP], #0x10
    // 0xa55c74: ret
    //     0xa55c74: ret             
    // 0xa55c78: r0 = const []
    //     0xa55c78: add             x0, PP, #0x3a, lsl #12  ; [pp+0x3a4e0] List<LineMetrics>(0)
    //     0xa55c7c: ldr             x0, [x0, #0x4e0]
    // 0xa55c80: LeaveFrame
    //     0xa55c80: mov             SP, fp
    //     0xa55c84: ldp             fp, lr, [SP], #0x10
    // 0xa55c88: ret
    //     0xa55c88: ret             
    // 0xa55c8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa55c8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa55c90: b               #0xa55b90
    // 0xa55c94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa55c94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] LineMetrics <anonymous closure>(dynamic, LineMetrics) {
    // ** addr: 0xa562c0, size: 0x44
    // 0xa562c0: EnterFrame
    //     0xa562c0: stp             fp, lr, [SP, #-0x10]!
    //     0xa562c4: mov             fp, SP
    // 0xa562c8: ldr             x0, [fp, #0x18]
    // 0xa562cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa562cc: ldur            w1, [x0, #0x17]
    // 0xa562d0: DecompressPointer r1
    //     0xa562d0: add             x1, x1, HEAP, lsl #32
    // 0xa562d4: CheckStackOverflow
    //     0xa562d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa562d8: cmp             SP, x16
    //     0xa562dc: b.ls            #0xa562fc
    // 0xa562e0: LoadField: r2 = r1->field_f
    //     0xa562e0: ldur            w2, [x1, #0xf]
    // 0xa562e4: DecompressPointer r2
    //     0xa562e4: add             x2, x2, HEAP, lsl #32
    // 0xa562e8: ldr             x1, [fp, #0x10]
    // 0xa562ec: r0 = _shiftLineMetrics()
    //     0xa562ec: bl              #0xa56304  ; [package:flutter/src/painting/text_painter.dart] TextPainter::_shiftLineMetrics
    // 0xa562f0: LeaveFrame
    //     0xa562f0: mov             SP, fp
    //     0xa562f4: ldp             fp, lr, [SP], #0x10
    // 0xa562f8: ret
    //     0xa562f8: ret             
    // 0xa562fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa562fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa56300: b               #0xa562e0
  }
  static _ _shiftLineMetrics(/* No info */) {
    // ** addr: 0xa56304, size: 0xc0
    // 0xa56304: EnterFrame
    //     0xa56304: stp             fp, lr, [SP, #-0x10]!
    //     0xa56308: mov             fp, SP
    // 0xa5630c: AllocStack(0x48)
    //     0xa5630c: sub             SP, SP, #0x48
    // 0xa56310: LoadField: r0 = r1->field_7
    //     0xa56310: ldur            w0, [x1, #7]
    // 0xa56314: DecompressPointer r0
    //     0xa56314: add             x0, x0, HEAP, lsl #32
    // 0xa56318: stur            x0, [fp, #-0x10]
    // 0xa5631c: LoadField: d0 = r1->field_b
    //     0xa5631c: ldur            d0, [x1, #0xb]
    // 0xa56320: stur            d0, [fp, #-0x48]
    // 0xa56324: LoadField: d1 = r1->field_13
    //     0xa56324: ldur            d1, [x1, #0x13]
    // 0xa56328: stur            d1, [fp, #-0x40]
    // 0xa5632c: LoadField: d2 = r1->field_1b
    //     0xa5632c: ldur            d2, [x1, #0x1b]
    // 0xa56330: stur            d2, [fp, #-0x38]
    // 0xa56334: LoadField: d3 = r1->field_23
    //     0xa56334: ldur            d3, [x1, #0x23]
    // 0xa56338: stur            d3, [fp, #-0x30]
    // 0xa5633c: LoadField: d4 = r1->field_2b
    //     0xa5633c: ldur            d4, [x1, #0x2b]
    // 0xa56340: stur            d4, [fp, #-0x28]
    // 0xa56344: LoadField: d5 = r1->field_33
    //     0xa56344: ldur            d5, [x1, #0x33]
    // 0xa56348: LoadField: d6 = r2->field_7
    //     0xa56348: ldur            d6, [x2, #7]
    // 0xa5634c: fadd            d7, d5, d6
    // 0xa56350: stur            d7, [fp, #-0x20]
    // 0xa56354: LoadField: d5 = r1->field_3b
    //     0xa56354: ldur            d5, [x1, #0x3b]
    // 0xa56358: LoadField: d6 = r2->field_f
    //     0xa56358: ldur            d6, [x2, #0xf]
    // 0xa5635c: fadd            d8, d5, d6
    // 0xa56360: stur            d8, [fp, #-0x18]
    // 0xa56364: LoadField: r2 = r1->field_43
    //     0xa56364: ldur            x2, [x1, #0x43]
    // 0xa56368: stur            x2, [fp, #-8]
    // 0xa5636c: r0 = LineMetrics()
    //     0xa5636c: bl              #0x684774  ; AllocateLineMetricsStub -> LineMetrics (size=0x4c)
    // 0xa56370: ldur            x1, [fp, #-0x10]
    // 0xa56374: StoreField: r0->field_7 = r1
    //     0xa56374: stur            w1, [x0, #7]
    // 0xa56378: ldur            d0, [fp, #-0x48]
    // 0xa5637c: StoreField: r0->field_b = d0
    //     0xa5637c: stur            d0, [x0, #0xb]
    // 0xa56380: ldur            d0, [fp, #-0x40]
    // 0xa56384: StoreField: r0->field_13 = d0
    //     0xa56384: stur            d0, [x0, #0x13]
    // 0xa56388: ldur            d0, [fp, #-0x38]
    // 0xa5638c: StoreField: r0->field_1b = d0
    //     0xa5638c: stur            d0, [x0, #0x1b]
    // 0xa56390: ldur            d0, [fp, #-0x30]
    // 0xa56394: StoreField: r0->field_23 = d0
    //     0xa56394: stur            d0, [x0, #0x23]
    // 0xa56398: ldur            d0, [fp, #-0x28]
    // 0xa5639c: StoreField: r0->field_2b = d0
    //     0xa5639c: stur            d0, [x0, #0x2b]
    // 0xa563a0: ldur            d0, [fp, #-0x20]
    // 0xa563a4: StoreField: r0->field_33 = d0
    //     0xa563a4: stur            d0, [x0, #0x33]
    // 0xa563a8: ldur            d0, [fp, #-0x18]
    // 0xa563ac: StoreField: r0->field_3b = d0
    //     0xa563ac: stur            d0, [x0, #0x3b]
    // 0xa563b0: ldur            x1, [fp, #-8]
    // 0xa563b4: StoreField: r0->field_43 = r1
    //     0xa563b4: stur            x1, [x0, #0x43]
    // 0xa563b8: LeaveFrame
    //     0xa563b8: mov             SP, fp
    //     0xa563bc: ldp             fp, lr, [SP], #0x10
    // 0xa563c0: ret
    //     0xa563c0: ret             
  }
  get _ wordBoundaries(/* No info */) {
    // ** addr: 0xd8044c, size: 0x74
    // 0xd8044c: EnterFrame
    //     0xd8044c: stp             fp, lr, [SP, #-0x10]!
    //     0xd80450: mov             fp, SP
    // 0xd80454: AllocStack(0x10)
    //     0xd80454: sub             SP, SP, #0x10
    // 0xd80458: LoadField: r0 = r1->field_f
    //     0xd80458: ldur            w0, [x1, #0xf]
    // 0xd8045c: DecompressPointer r0
    //     0xd8045c: add             x0, x0, HEAP, lsl #32
    // 0xd80460: stur            x0, [fp, #-0x10]
    // 0xd80464: cmp             w0, NULL
    // 0xd80468: b.eq            #0xd804b8
    // 0xd8046c: LoadField: r2 = r1->field_7
    //     0xd8046c: ldur            w2, [x1, #7]
    // 0xd80470: DecompressPointer r2
    //     0xd80470: add             x2, x2, HEAP, lsl #32
    // 0xd80474: cmp             w2, NULL
    // 0xd80478: b.eq            #0xd804bc
    // 0xd8047c: LoadField: r1 = r2->field_7
    //     0xd8047c: ldur            w1, [x2, #7]
    // 0xd80480: DecompressPointer r1
    //     0xd80480: add             x1, x1, HEAP, lsl #32
    // 0xd80484: LoadField: r2 = r1->field_f
    //     0xd80484: ldur            w2, [x1, #0xf]
    // 0xd80488: DecompressPointer r2
    //     0xd80488: add             x2, x2, HEAP, lsl #32
    // 0xd8048c: stur            x2, [fp, #-8]
    // 0xd80490: r0 = WordBoundary()
    //     0xd80490: bl              #0xd804c0  ; AllocateWordBoundaryStub -> WordBoundary (size=0x14)
    // 0xd80494: r1 = Sentinel
    //     0xd80494: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd80498: StoreField: r0->field_f = r1
    //     0xd80498: stur            w1, [x0, #0xf]
    // 0xd8049c: ldur            x1, [fp, #-0x10]
    // 0xd804a0: StoreField: r0->field_7 = r1
    //     0xd804a0: stur            w1, [x0, #7]
    // 0xd804a4: ldur            x1, [fp, #-8]
    // 0xd804a8: StoreField: r0->field_b = r1
    //     0xd804a8: stur            w1, [x0, #0xb]
    // 0xd804ac: LeaveFrame
    //     0xd804ac: mov             SP, fp
    //     0xd804b0: ldp             fp, lr, [SP], #0x10
    // 0xd804b4: ret
    //     0xd804b4: ret             
    // 0xd804b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd804b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd804bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd804bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ getLineBoundary(/* No info */) {
    // ** addr: 0xda558c, size: 0x54
    // 0xda558c: EnterFrame
    //     0xda558c: stp             fp, lr, [SP, #-0x10]!
    //     0xda5590: mov             fp, SP
    // 0xda5594: CheckStackOverflow
    //     0xda5594: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xda5598: cmp             SP, x16
    //     0xda559c: b.ls            #0xda55d4
    // 0xda55a0: LoadField: r0 = r1->field_7
    //     0xda55a0: ldur            w0, [x1, #7]
    // 0xda55a4: DecompressPointer r0
    //     0xda55a4: add             x0, x0, HEAP, lsl #32
    // 0xda55a8: cmp             w0, NULL
    // 0xda55ac: b.eq            #0xda55dc
    // 0xda55b0: LoadField: r1 = r0->field_7
    //     0xda55b0: ldur            w1, [x0, #7]
    // 0xda55b4: DecompressPointer r1
    //     0xda55b4: add             x1, x1, HEAP, lsl #32
    // 0xda55b8: LoadField: r0 = r1->field_f
    //     0xda55b8: ldur            w0, [x1, #0xf]
    // 0xda55bc: DecompressPointer r0
    //     0xda55bc: add             x0, x0, HEAP, lsl #32
    // 0xda55c0: mov             x1, x0
    // 0xda55c4: r0 = getLineBoundary()
    //     0xda55c4: bl              #0xda55e0  ; [dart:ui] _NativeParagraph::getLineBoundary
    // 0xda55c8: LeaveFrame
    //     0xda55c8: mov             SP, fp
    //     0xda55cc: ldp             fp, lr, [SP], #0x10
    // 0xda55d0: ret
    //     0xda55d0: ret             
    // 0xda55d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xda55d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xda55d8: b               #0xda55a0
    // 0xda55dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda55dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3219, size: 0x18, field offset: 0x8
//   const constructor, 
class _LineCaretMetrics extends Object {

  _ shift(/* No info */) {
    // ** addr: 0x683b5c, size: 0xa4
    // 0x683b5c: EnterFrame
    //     0x683b5c: stp             fp, lr, [SP, #-0x10]!
    //     0x683b60: mov             fp, SP
    // 0x683b64: AllocStack(0x30)
    //     0x683b64: sub             SP, SP, #0x30
    // 0x683b68: SetupParameters(_LineCaretMetrics this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x683b68: mov             x0, x1
    //     0x683b6c: stur            x1, [fp, #-8]
    //     0x683b70: mov             x1, x2
    //     0x683b74: stur            x2, [fp, #-0x10]
    // 0x683b78: CheckStackOverflow
    //     0x683b78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x683b7c: cmp             SP, x16
    //     0x683b80: b.ls            #0x683bf8
    // 0x683b84: r16 = Instance_Offset
    //     0x683b84: ldr             x16, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x683b88: stp             x16, x1, [SP]
    // 0x683b8c: r0 = ==()
    //     0x683b8c: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0x683b90: tbnz            w0, #4, #0x683b9c
    // 0x683b94: ldur            x0, [fp, #-8]
    // 0x683b98: b               #0x683bec
    // 0x683b9c: ldur            x0, [fp, #-8]
    // 0x683ba0: LoadField: r2 = r0->field_7
    //     0x683ba0: ldur            w2, [x0, #7]
    // 0x683ba4: DecompressPointer r2
    //     0x683ba4: add             x2, x2, HEAP, lsl #32
    // 0x683ba8: ldur            x1, [fp, #-0x10]
    // 0x683bac: r0 = +()
    //     0x683bac: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x683bb0: mov             x1, x0
    // 0x683bb4: ldur            x0, [fp, #-8]
    // 0x683bb8: stur            x1, [fp, #-0x18]
    // 0x683bbc: LoadField: r2 = r0->field_b
    //     0x683bbc: ldur            w2, [x0, #0xb]
    // 0x683bc0: DecompressPointer r2
    //     0x683bc0: add             x2, x2, HEAP, lsl #32
    // 0x683bc4: stur            x2, [fp, #-0x10]
    // 0x683bc8: LoadField: d0 = r0->field_f
    //     0x683bc8: ldur            d0, [x0, #0xf]
    // 0x683bcc: stur            d0, [fp, #-0x20]
    // 0x683bd0: r0 = _LineCaretMetrics()
    //     0x683bd0: bl              #0x683b20  ; Allocate_LineCaretMetricsStub -> _LineCaretMetrics (size=0x18)
    // 0x683bd4: ldur            x1, [fp, #-0x18]
    // 0x683bd8: StoreField: r0->field_7 = r1
    //     0x683bd8: stur            w1, [x0, #7]
    // 0x683bdc: ldur            x1, [fp, #-0x10]
    // 0x683be0: StoreField: r0->field_b = r1
    //     0x683be0: stur            w1, [x0, #0xb]
    // 0x683be4: ldur            d0, [fp, #-0x20]
    // 0x683be8: StoreField: r0->field_f = d0
    //     0x683be8: stur            d0, [x0, #0xf]
    // 0x683bec: LeaveFrame
    //     0x683bec: mov             SP, fp
    //     0x683bf0: ldp             fp, lr, [SP], #0x10
    // 0x683bf4: ret
    //     0x683bf4: ret             
    // 0x683bf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x683bf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x683bfc: b               #0x683b84
  }
}

// class id: 3220, size: 0x30, field offset: 0x8
class _TextPainterLayoutCacheWithOffset extends Object {

  get _ paintOffset(/* No info */) {
    // ** addr: 0x67bfe8, size: 0x180
    // 0x67bfe8: EnterFrame
    //     0x67bfe8: stp             fp, lr, [SP, #-0x10]!
    //     0x67bfec: mov             fp, SP
    // 0x67bff0: AllocStack(0x38)
    //     0x67bff0: sub             SP, SP, #0x38
    // 0x67bff4: d0 = 0.000000
    //     0x67bff4: eor             v0.16b, v0.16b, v0.16b
    // 0x67bff8: stur            x1, [fp, #-0x20]
    // 0x67bffc: CheckStackOverflow
    //     0x67bffc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67c000: cmp             SP, x16
    //     0x67c004: b.ls            #0x67c158
    // 0x67c008: LoadField: d1 = r1->field_1b
    //     0x67c008: ldur            d1, [x1, #0x1b]
    // 0x67c00c: stur            d1, [fp, #-0x28]
    // 0x67c010: fcmp            d1, d0
    // 0x67c014: b.ne            #0x67c028
    // 0x67c018: r0 = Instance_Offset
    //     0x67c018: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x67c01c: LeaveFrame
    //     0x67c01c: mov             SP, fp
    //     0x67c020: ldp             fp, lr, [SP], #0x10
    // 0x67c024: ret
    //     0x67c024: ret             
    // 0x67c028: LoadField: r0 = r1->field_7
    //     0x67c028: ldur            w0, [x1, #7]
    // 0x67c02c: DecompressPointer r0
    //     0x67c02c: add             x0, x0, HEAP, lsl #32
    // 0x67c030: stur            x0, [fp, #-0x18]
    // 0x67c034: LoadField: r2 = r0->field_f
    //     0x67c034: ldur            w2, [x0, #0xf]
    // 0x67c038: DecompressPointer r2
    //     0x67c038: add             x2, x2, HEAP, lsl #32
    // 0x67c03c: stur            x2, [fp, #-0x10]
    // 0x67c040: LoadField: r3 = r2->field_7
    //     0x67c040: ldur            w3, [x2, #7]
    // 0x67c044: DecompressPointer r3
    //     0x67c044: add             x3, x3, HEAP, lsl #32
    // 0x67c048: cmp             w3, NULL
    // 0x67c04c: b.eq            #0x67c160
    // 0x67c050: LoadField: r4 = r3->field_7
    //     0x67c050: ldur            x4, [x3, #7]
    // 0x67c054: ldr             x3, [x4]
    // 0x67c058: stur            x3, [fp, #-8]
    // 0x67c05c: cbnz            x3, #0x67c06c
    // 0x67c060: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x67c060: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x67c064: str             x16, [SP]
    // 0x67c068: r0 = _throwNew()
    //     0x67c068: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x67c06c: ldur            x0, [fp, #-8]
    // 0x67c070: stur            x0, [fp, #-8]
    // 0x67c074: r1 = <Never>
    //     0x67c074: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x67c078: r0 = Pointer()
    //     0x67c078: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x67c07c: mov             x1, x0
    // 0x67c080: ldur            x0, [fp, #-8]
    // 0x67c084: StoreField: r1->field_7 = r0
    //     0x67c084: stur            x0, [x1, #7]
    // 0x67c088: r0 = _width$Getter$FfiNative()
    //     0x67c088: bl              #0x67c1e8  ; [dart:ui] _NativeParagraph::_width$Getter$FfiNative
    // 0x67c08c: mov             x0, v0.d[0]
    // 0x67c090: and             x0, x0, #0x7fffffffffffffff
    // 0x67c094: r17 = 9218868437227405312
    //     0x67c094: orr             x17, xzr, #0x7ff0000000000000
    // 0x67c098: cmp             x0, x17
    // 0x67c09c: b.eq            #0x67c148
    // 0x67c0a0: fcmp            d0, d0
    // 0x67c0a4: b.vs            #0x67c148
    // 0x67c0a8: ldur            x0, [fp, #-0x20]
    // 0x67c0ac: ldur            x1, [fp, #-0x18]
    // 0x67c0b0: LoadField: d0 = r0->field_13
    //     0x67c0b0: ldur            d0, [x0, #0x13]
    // 0x67c0b4: stur            d0, [fp, #-0x30]
    // 0x67c0b8: LoadField: r0 = r1->field_f
    //     0x67c0b8: ldur            w0, [x1, #0xf]
    // 0x67c0bc: DecompressPointer r0
    //     0x67c0bc: add             x0, x0, HEAP, lsl #32
    // 0x67c0c0: stur            x0, [fp, #-0x10]
    // 0x67c0c4: LoadField: r1 = r0->field_7
    //     0x67c0c4: ldur            w1, [x0, #7]
    // 0x67c0c8: DecompressPointer r1
    //     0x67c0c8: add             x1, x1, HEAP, lsl #32
    // 0x67c0cc: cmp             w1, NULL
    // 0x67c0d0: b.eq            #0x67c164
    // 0x67c0d4: LoadField: r2 = r1->field_7
    //     0x67c0d4: ldur            x2, [x1, #7]
    // 0x67c0d8: ldr             x1, [x2]
    // 0x67c0dc: stur            x1, [fp, #-8]
    // 0x67c0e0: cbnz            x1, #0x67c0f0
    // 0x67c0e4: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x67c0e4: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x67c0e8: str             x16, [SP]
    // 0x67c0ec: r0 = _throwNew()
    //     0x67c0ec: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x67c0f0: ldur            d1, [fp, #-0x28]
    // 0x67c0f4: ldur            d0, [fp, #-0x30]
    // 0x67c0f8: ldur            x0, [fp, #-8]
    // 0x67c0fc: stur            x0, [fp, #-8]
    // 0x67c100: r1 = <Never>
    //     0x67c100: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x67c104: r0 = Pointer()
    //     0x67c104: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x67c108: mov             x1, x0
    // 0x67c10c: ldur            x0, [fp, #-8]
    // 0x67c110: StoreField: r1->field_7 = r0
    //     0x67c110: stur            x0, [x1, #7]
    // 0x67c114: r0 = _width$Getter$FfiNative()
    //     0x67c114: bl              #0x67c1e8  ; [dart:ui] _NativeParagraph::_width$Getter$FfiNative
    // 0x67c118: ldur            d1, [fp, #-0x30]
    // 0x67c11c: fsub            d2, d1, d0
    // 0x67c120: ldur            d0, [fp, #-0x28]
    // 0x67c124: fmul            d1, d0, d2
    // 0x67c128: stur            d1, [fp, #-0x30]
    // 0x67c12c: r0 = Offset()
    //     0x67c12c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x67c130: ldur            d0, [fp, #-0x30]
    // 0x67c134: StoreField: r0->field_7 = d0
    //     0x67c134: stur            d0, [x0, #7]
    // 0x67c138: StoreField: r0->field_f = rZR
    //     0x67c138: stur            xzr, [x0, #0xf]
    // 0x67c13c: LeaveFrame
    //     0x67c13c: mov             SP, fp
    //     0x67c140: ldp             fp, lr, [SP], #0x10
    // 0x67c144: ret
    //     0x67c144: ret             
    // 0x67c148: r0 = Instance_Offset
    //     0x67c148: ldr             x0, [PP, #0x48f8]  ; [pp+0x48f8] Obj!Offset@e2c361
    // 0x67c14c: LeaveFrame
    //     0x67c14c: mov             SP, fp
    //     0x67c150: ldp             fp, lr, [SP], #0x10
    // 0x67c154: ret
    //     0x67c154: ret             
    // 0x67c158: r0 = StackOverflowSharedWithFPURegs()
    //     0x67c158: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x67c15c: b               #0x67c008
    // 0x67c160: r0 = NullErrorSharedWithFPURegs()
    //     0x67c160: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0x67c164: r0 = NullErrorSharedWithFPURegs()
    //     0x67c164: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
  }
  _ _resizeToFit(/* No info */) {
    // ** addr: 0x67e620, size: 0x2a4
    // 0x67e620: EnterFrame
    //     0x67e620: stp             fp, lr, [SP, #-0x10]!
    //     0x67e624: mov             fp, SP
    // 0x67e628: AllocStack(0x40)
    //     0x67e628: sub             SP, SP, #0x40
    // 0x67e62c: SetupParameters(_TextPainterLayoutCacheWithOffset this /* r1 => r0, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x28 */, dynamic _ /* d1 => d1, fp-0x30 */)
    //     0x67e62c: mov             x0, x1
    //     0x67e630: stur            x1, [fp, #-8]
    //     0x67e634: stur            d0, [fp, #-0x28]
    //     0x67e638: stur            d1, [fp, #-0x30]
    // 0x67e63c: CheckStackOverflow
    //     0x67e63c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67e640: cmp             SP, x16
    //     0x67e644: b.ls            #0x67e8b0
    // 0x67e648: LoadField: d2 = r0->field_13
    //     0x67e648: ldur            d2, [x0, #0x13]
    // 0x67e64c: fcmp            d1, d2
    // 0x67e650: b.ne            #0x67e680
    // 0x67e654: fcmp            d0, d2
    // 0x67e658: b.ne            #0x67e680
    // 0x67e65c: LoadField: r1 = r0->field_7
    //     0x67e65c: ldur            w1, [x0, #7]
    // 0x67e660: DecompressPointer r1
    //     0x67e660: add             x1, x1, HEAP, lsl #32
    // 0x67e664: r0 = _contentWidthFor()
    //     0x67e664: bl              #0x67ca00  ; [package:flutter/src/painting/text_painter.dart] _TextLayout::_contentWidthFor
    // 0x67e668: ldur            x0, [fp, #-8]
    // 0x67e66c: StoreField: r0->field_13 = d0
    //     0x67e66c: stur            d0, [x0, #0x13]
    // 0x67e670: r0 = true
    //     0x67e670: add             x0, NULL, #0x20  ; true
    // 0x67e674: LeaveFrame
    //     0x67e674: mov             SP, fp
    //     0x67e678: ldp             fp, lr, [SP], #0x10
    // 0x67e67c: ret
    //     0x67e67c: ret             
    // 0x67e680: mov             x1, x0
    // 0x67e684: r0 = paintOffset()
    //     0x67e684: bl              #0x67bfe8  ; [package:flutter/src/painting/text_painter.dart] _TextPainterLayoutCacheWithOffset::paintOffset
    // 0x67e688: LoadField: d0 = r0->field_7
    //     0x67e688: ldur            d0, [x0, #7]
    // 0x67e68c: mov             x0, v0.d[0]
    // 0x67e690: and             x0, x0, #0x7fffffffffffffff
    // 0x67e694: r17 = 9218868437227405312
    //     0x67e694: orr             x17, xzr, #0x7ff0000000000000
    // 0x67e698: cmp             x0, x17
    // 0x67e69c: b.eq            #0x67e6b0
    // 0x67e6a0: fcmp            d0, d0
    // 0x67e6a4: b.vs            #0x67e6b0
    // 0x67e6a8: ldur            d0, [fp, #-0x28]
    // 0x67e6ac: b               #0x67e768
    // 0x67e6b0: ldur            x0, [fp, #-8]
    // 0x67e6b4: LoadField: r1 = r0->field_7
    //     0x67e6b4: ldur            w1, [x0, #7]
    // 0x67e6b8: DecompressPointer r1
    //     0x67e6b8: add             x1, x1, HEAP, lsl #32
    // 0x67e6bc: LoadField: r2 = r1->field_f
    //     0x67e6bc: ldur            w2, [x1, #0xf]
    // 0x67e6c0: DecompressPointer r2
    //     0x67e6c0: add             x2, x2, HEAP, lsl #32
    // 0x67e6c4: stur            x2, [fp, #-0x18]
    // 0x67e6c8: LoadField: r1 = r2->field_7
    //     0x67e6c8: ldur            w1, [x2, #7]
    // 0x67e6cc: DecompressPointer r1
    //     0x67e6cc: add             x1, x1, HEAP, lsl #32
    // 0x67e6d0: cmp             w1, NULL
    // 0x67e6d4: b.eq            #0x67e8b8
    // 0x67e6d8: LoadField: r3 = r1->field_7
    //     0x67e6d8: ldur            x3, [x1, #7]
    // 0x67e6dc: ldr             x1, [x3]
    // 0x67e6e0: stur            x1, [fp, #-0x10]
    // 0x67e6e4: cbnz            x1, #0x67e6f4
    // 0x67e6e8: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x67e6e8: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x67e6ec: str             x16, [SP]
    // 0x67e6f0: r0 = _throwNew()
    //     0x67e6f0: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x67e6f4: ldur            x0, [fp, #-0x10]
    // 0x67e6f8: stur            x0, [fp, #-0x10]
    // 0x67e6fc: r1 = <Never>
    //     0x67e6fc: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x67e700: r0 = Pointer()
    //     0x67e700: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x67e704: mov             x1, x0
    // 0x67e708: ldur            x0, [fp, #-0x10]
    // 0x67e70c: StoreField: r1->field_7 = r0
    //     0x67e70c: stur            x0, [x1, #7]
    // 0x67e710: r0 = _width$Getter$FfiNative()
    //     0x67e710: bl              #0x67c1e8  ; [dart:ui] _NativeParagraph::_width$Getter$FfiNative
    // 0x67e714: mov             x0, v0.d[0]
    // 0x67e718: and             x0, x0, #0x7fffffffffffffff
    // 0x67e71c: r17 = 9218868437227405312
    //     0x67e71c: orr             x17, xzr, #0x7ff0000000000000
    // 0x67e720: cmp             x0, x17
    // 0x67e724: b.eq            #0x67e738
    // 0x67e728: fcmp            d0, d0
    // 0x67e72c: b.vs            #0x67e738
    // 0x67e730: ldur            d0, [fp, #-0x28]
    // 0x67e734: b               #0x67e768
    // 0x67e738: ldur            d0, [fp, #-0x28]
    // 0x67e73c: mov             x0, v0.d[0]
    // 0x67e740: and             x0, x0, #0x7fffffffffffffff
    // 0x67e744: r17 = 9218868437227405312
    //     0x67e744: orr             x17, xzr, #0x7ff0000000000000
    // 0x67e748: cmp             x0, x17
    // 0x67e74c: b.eq            #0x67e768
    // 0x67e750: fcmp            d0, d0
    // 0x67e754: b.vs            #0x67e768
    // 0x67e758: r0 = false
    //     0x67e758: add             x0, NULL, #0x30  ; false
    // 0x67e75c: LeaveFrame
    //     0x67e75c: mov             SP, fp
    //     0x67e760: ldp             fp, lr, [SP], #0x10
    // 0x67e764: ret
    //     0x67e764: ret             
    // 0x67e768: ldur            x0, [fp, #-8]
    // 0x67e76c: LoadField: r1 = r0->field_7
    //     0x67e76c: ldur            w1, [x0, #7]
    // 0x67e770: DecompressPointer r1
    //     0x67e770: add             x1, x1, HEAP, lsl #32
    // 0x67e774: stur            x1, [fp, #-0x20]
    // 0x67e778: LoadField: r2 = r1->field_f
    //     0x67e778: ldur            w2, [x1, #0xf]
    // 0x67e77c: DecompressPointer r2
    //     0x67e77c: add             x2, x2, HEAP, lsl #32
    // 0x67e780: stur            x2, [fp, #-0x18]
    // 0x67e784: LoadField: r3 = r2->field_7
    //     0x67e784: ldur            w3, [x2, #7]
    // 0x67e788: DecompressPointer r3
    //     0x67e788: add             x3, x3, HEAP, lsl #32
    // 0x67e78c: cmp             w3, NULL
    // 0x67e790: b.eq            #0x67e8bc
    // 0x67e794: LoadField: r4 = r3->field_7
    //     0x67e794: ldur            x4, [x3, #7]
    // 0x67e798: ldr             x3, [x4]
    // 0x67e79c: stur            x3, [fp, #-0x10]
    // 0x67e7a0: cbnz            x3, #0x67e7b0
    // 0x67e7a4: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x67e7a4: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x67e7a8: str             x16, [SP]
    // 0x67e7ac: r0 = _throwNew()
    //     0x67e7ac: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x67e7b0: ldur            x0, [fp, #-8]
    // 0x67e7b4: ldur            d1, [fp, #-0x30]
    // 0x67e7b8: ldur            x2, [fp, #-0x10]
    // 0x67e7bc: stur            x2, [fp, #-0x10]
    // 0x67e7c0: r1 = <Never>
    //     0x67e7c0: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x67e7c4: r0 = Pointer()
    //     0x67e7c4: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x67e7c8: mov             x1, x0
    // 0x67e7cc: ldur            x0, [fp, #-0x10]
    // 0x67e7d0: StoreField: r1->field_7 = r0
    //     0x67e7d0: stur            x0, [x1, #7]
    // 0x67e7d4: r0 = _maxIntrinsicWidth$Getter$FfiNative()
    //     0x67e7d4: bl              #0x67e368  ; [dart:ui] _NativeParagraph::_maxIntrinsicWidth$Getter$FfiNative
    // 0x67e7d8: stur            d0, [fp, #-0x38]
    // 0x67e7dc: ldur            x0, [fp, #-8]
    // 0x67e7e0: LoadField: d1 = r0->field_b
    //     0x67e7e0: ldur            d1, [x0, #0xb]
    // 0x67e7e4: ldur            d2, [fp, #-0x30]
    // 0x67e7e8: fcmp            d2, d1
    // 0x67e7ec: b.eq            #0x67e878
    // 0x67e7f0: ldur            x1, [fp, #-0x20]
    // 0x67e7f4: LoadField: r2 = r1->field_f
    //     0x67e7f4: ldur            w2, [x1, #0xf]
    // 0x67e7f8: DecompressPointer r2
    //     0x67e7f8: add             x2, x2, HEAP, lsl #32
    // 0x67e7fc: stur            x2, [fp, #-0x18]
    // 0x67e800: LoadField: r3 = r2->field_7
    //     0x67e800: ldur            w3, [x2, #7]
    // 0x67e804: DecompressPointer r3
    //     0x67e804: add             x3, x3, HEAP, lsl #32
    // 0x67e808: cmp             w3, NULL
    // 0x67e80c: b.eq            #0x67e8c0
    // 0x67e810: LoadField: r4 = r3->field_7
    //     0x67e810: ldur            x4, [x3, #7]
    // 0x67e814: ldr             x3, [x4]
    // 0x67e818: stur            x3, [fp, #-0x10]
    // 0x67e81c: cbnz            x3, #0x67e82c
    // 0x67e820: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x67e820: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x67e824: str             x16, [SP]
    // 0x67e828: r0 = _throwNew()
    //     0x67e828: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x67e82c: ldur            d0, [fp, #-0x38]
    // 0x67e830: ldur            x0, [fp, #-0x10]
    // 0x67e834: stur            x0, [fp, #-0x10]
    // 0x67e838: r1 = <Never>
    //     0x67e838: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x67e83c: r0 = Pointer()
    //     0x67e83c: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x67e840: mov             x1, x0
    // 0x67e844: ldur            x0, [fp, #-0x10]
    // 0x67e848: StoreField: r1->field_7 = r0
    //     0x67e848: stur            x0, [x1, #7]
    // 0x67e84c: r0 = _width$Getter$FfiNative()
    //     0x67e84c: bl              #0x67c1e8  ; [dart:ui] _NativeParagraph::_width$Getter$FfiNative
    // 0x67e850: ldur            d1, [fp, #-0x38]
    // 0x67e854: fsub            d2, d0, d1
    // 0x67e858: d0 = -0.000000
    //     0x67e858: ldr             d0, [PP, #0x49d8]  ; [pp+0x49d8] IMM: double(-1e-10) from 0xbddb7cdfd9d7bdbb
    // 0x67e85c: fcmp            d2, d0
    // 0x67e860: b.le            #0x67e8a0
    // 0x67e864: ldur            d2, [fp, #-0x30]
    // 0x67e868: fsub            d3, d2, d1
    // 0x67e86c: fcmp            d3, d0
    // 0x67e870: b.le            #0x67e8a0
    // 0x67e874: ldur            x0, [fp, #-8]
    // 0x67e878: ldur            x1, [fp, #-0x20]
    // 0x67e87c: ldur            d0, [fp, #-0x28]
    // 0x67e880: mov             v1.16b, v2.16b
    // 0x67e884: r0 = _contentWidthFor()
    //     0x67e884: bl              #0x67ca00  ; [package:flutter/src/painting/text_painter.dart] _TextLayout::_contentWidthFor
    // 0x67e888: ldur            x1, [fp, #-8]
    // 0x67e88c: StoreField: r1->field_13 = d0
    //     0x67e88c: stur            d0, [x1, #0x13]
    // 0x67e890: r0 = true
    //     0x67e890: add             x0, NULL, #0x20  ; true
    // 0x67e894: LeaveFrame
    //     0x67e894: mov             SP, fp
    //     0x67e898: ldp             fp, lr, [SP], #0x10
    // 0x67e89c: ret
    //     0x67e89c: ret             
    // 0x67e8a0: r0 = false
    //     0x67e8a0: add             x0, NULL, #0x30  ; false
    // 0x67e8a4: LeaveFrame
    //     0x67e8a4: mov             SP, fp
    //     0x67e8a8: ldp             fp, lr, [SP], #0x10
    // 0x67e8ac: ret
    //     0x67e8ac: ret             
    // 0x67e8b0: r0 = StackOverflowSharedWithFPURegs()
    //     0x67e8b0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x67e8b4: b               #0x67e648
    // 0x67e8b8: r0 = NullErrorSharedWithoutFPURegs()
    //     0x67e8b8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x67e8bc: r0 = NullErrorSharedWithFPURegs()
    //     0x67e8bc: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0x67e8c0: r0 = NullErrorSharedWithFPURegs()
    //     0x67e8c0: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
  }
  get _ inlinePlaceholderBoxes(/* No info */) {
    // ** addr: 0x7707b4, size: 0x8c
    // 0x7707b4: EnterFrame
    //     0x7707b4: stp             fp, lr, [SP, #-0x10]!
    //     0x7707b8: mov             fp, SP
    // 0x7707bc: AllocStack(0x8)
    //     0x7707bc: sub             SP, SP, #8
    // 0x7707c0: SetupParameters(_TextPainterLayoutCacheWithOffset this /* r1 => r0, fp-0x8 */)
    //     0x7707c0: mov             x0, x1
    //     0x7707c4: stur            x1, [fp, #-8]
    // 0x7707c8: CheckStackOverflow
    //     0x7707c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7707cc: cmp             SP, x16
    //     0x7707d0: b.ls            #0x770838
    // 0x7707d4: LoadField: r1 = r0->field_23
    //     0x7707d4: ldur            w1, [x0, #0x23]
    // 0x7707d8: DecompressPointer r1
    //     0x7707d8: add             x1, x1, HEAP, lsl #32
    // 0x7707dc: cmp             w1, NULL
    // 0x7707e0: b.ne            #0x770828
    // 0x7707e4: LoadField: r1 = r0->field_7
    //     0x7707e4: ldur            w1, [x0, #7]
    // 0x7707e8: DecompressPointer r1
    //     0x7707e8: add             x1, x1, HEAP, lsl #32
    // 0x7707ec: LoadField: r2 = r1->field_f
    //     0x7707ec: ldur            w2, [x1, #0xf]
    // 0x7707f0: DecompressPointer r2
    //     0x7707f0: add             x2, x2, HEAP, lsl #32
    // 0x7707f4: mov             x1, x2
    // 0x7707f8: r0 = getBoxesForPlaceholders()
    //     0x7707f8: bl              #0x770840  ; [dart:ui] _NativeParagraph::getBoxesForPlaceholders
    // 0x7707fc: mov             x1, x0
    // 0x770800: ldur            x2, [fp, #-8]
    // 0x770804: StoreField: r2->field_23 = r0
    //     0x770804: stur            w0, [x2, #0x23]
    //     0x770808: ldurb           w16, [x2, #-1]
    //     0x77080c: ldurb           w17, [x0, #-1]
    //     0x770810: and             x16, x17, x16, lsr #2
    //     0x770814: tst             x16, HEAP, lsr #32
    //     0x770818: b.eq            #0x770820
    //     0x77081c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x770820: mov             x0, x1
    // 0x770824: b               #0x77082c
    // 0x770828: mov             x0, x1
    // 0x77082c: LeaveFrame
    //     0x77082c: mov             SP, fp
    //     0x770830: ldp             fp, lr, [SP], #0x10
    // 0x770834: ret
    //     0x770834: ret             
    // 0x770838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x770838: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x77083c: b               #0x7707d4
  }
  get _ lineMetrics(/* No info */) {
    // ** addr: 0xa55c98, size: 0x8c
    // 0xa55c98: EnterFrame
    //     0xa55c98: stp             fp, lr, [SP, #-0x10]!
    //     0xa55c9c: mov             fp, SP
    // 0xa55ca0: AllocStack(0x8)
    //     0xa55ca0: sub             SP, SP, #8
    // 0xa55ca4: SetupParameters(_TextPainterLayoutCacheWithOffset this /* r1 => r0, fp-0x8 */)
    //     0xa55ca4: mov             x0, x1
    //     0xa55ca8: stur            x1, [fp, #-8]
    // 0xa55cac: CheckStackOverflow
    //     0xa55cac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa55cb0: cmp             SP, x16
    //     0xa55cb4: b.ls            #0xa55d1c
    // 0xa55cb8: LoadField: r1 = r0->field_27
    //     0xa55cb8: ldur            w1, [x0, #0x27]
    // 0xa55cbc: DecompressPointer r1
    //     0xa55cbc: add             x1, x1, HEAP, lsl #32
    // 0xa55cc0: cmp             w1, NULL
    // 0xa55cc4: b.ne            #0xa55d0c
    // 0xa55cc8: LoadField: r1 = r0->field_7
    //     0xa55cc8: ldur            w1, [x0, #7]
    // 0xa55ccc: DecompressPointer r1
    //     0xa55ccc: add             x1, x1, HEAP, lsl #32
    // 0xa55cd0: LoadField: r2 = r1->field_f
    //     0xa55cd0: ldur            w2, [x1, #0xf]
    // 0xa55cd4: DecompressPointer r2
    //     0xa55cd4: add             x2, x2, HEAP, lsl #32
    // 0xa55cd8: mov             x1, x2
    // 0xa55cdc: r0 = computeLineMetrics()
    //     0xa55cdc: bl              #0xa55d24  ; [dart:ui] _NativeParagraph::computeLineMetrics
    // 0xa55ce0: mov             x1, x0
    // 0xa55ce4: ldur            x2, [fp, #-8]
    // 0xa55ce8: StoreField: r2->field_27 = r0
    //     0xa55ce8: stur            w0, [x2, #0x27]
    //     0xa55cec: ldurb           w16, [x2, #-1]
    //     0xa55cf0: ldurb           w17, [x0, #-1]
    //     0xa55cf4: and             x16, x17, x16, lsr #2
    //     0xa55cf8: tst             x16, HEAP, lsr #32
    //     0xa55cfc: b.eq            #0xa55d04
    //     0xa55d00: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa55d04: mov             x0, x1
    // 0xa55d08: b               #0xa55d10
    // 0xa55d0c: mov             x0, x1
    // 0xa55d10: LeaveFrame
    //     0xa55d10: mov             SP, fp
    //     0xa55d14: ldp             fp, lr, [SP], #0x10
    // 0xa55d18: ret
    //     0xa55d18: ret             
    // 0xa55d1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa55d1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa55d20: b               #0xa55cb8
  }
}

// class id: 3221, size: 0x18, field offset: 0x8
class _TextLayout extends Object {

  late final _LineCaretMetrics _endOfTextCaretMetrics; // offset: 0x14
  static late final RegExp _regExpSpaceSeparators; // offset: 0xbb8

  _ _contentWidthFor(/* No info */) {
    // ** addr: 0x67ca00, size: 0xc8
    // 0x67ca00: EnterFrame
    //     0x67ca00: stp             fp, lr, [SP, #-0x10]!
    //     0x67ca04: mov             fp, SP
    // 0x67ca08: AllocStack(0x28)
    //     0x67ca08: sub             SP, SP, #0x28
    // 0x67ca0c: SetupParameters(dynamic _ /* d0 => d0, fp-0x18 */, dynamic _ /* d1 => d1, fp-0x20 */)
    //     0x67ca0c: stur            d0, [fp, #-0x18]
    //     0x67ca10: stur            d1, [fp, #-0x20]
    // 0x67ca14: CheckStackOverflow
    //     0x67ca14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67ca18: cmp             SP, x16
    //     0x67ca1c: b.ls            #0x67cabc
    // 0x67ca20: LoadField: r0 = r1->field_f
    //     0x67ca20: ldur            w0, [x1, #0xf]
    // 0x67ca24: DecompressPointer r0
    //     0x67ca24: add             x0, x0, HEAP, lsl #32
    // 0x67ca28: stur            x0, [fp, #-0x10]
    // 0x67ca2c: LoadField: r1 = r0->field_7
    //     0x67ca2c: ldur            w1, [x0, #7]
    // 0x67ca30: DecompressPointer r1
    //     0x67ca30: add             x1, x1, HEAP, lsl #32
    // 0x67ca34: cmp             w1, NULL
    // 0x67ca38: b.eq            #0x67cac4
    // 0x67ca3c: LoadField: r2 = r1->field_7
    //     0x67ca3c: ldur            x2, [x1, #7]
    // 0x67ca40: ldr             x1, [x2]
    // 0x67ca44: stur            x1, [fp, #-8]
    // 0x67ca48: cbnz            x1, #0x67ca58
    // 0x67ca4c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x67ca4c: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x67ca50: str             x16, [SP]
    // 0x67ca54: r0 = _throwNew()
    //     0x67ca54: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x67ca58: ldur            d0, [fp, #-0x18]
    // 0x67ca5c: ldur            x0, [fp, #-8]
    // 0x67ca60: stur            x0, [fp, #-8]
    // 0x67ca64: r1 = <Never>
    //     0x67ca64: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x67ca68: r0 = Pointer()
    //     0x67ca68: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x67ca6c: mov             x1, x0
    // 0x67ca70: ldur            x0, [fp, #-8]
    // 0x67ca74: StoreField: r1->field_7 = r0
    //     0x67ca74: stur            x0, [x1, #7]
    // 0x67ca78: r0 = _maxIntrinsicWidth$Getter$FfiNative()
    //     0x67ca78: bl              #0x67e368  ; [dart:ui] _NativeParagraph::_maxIntrinsicWidth$Getter$FfiNative
    // 0x67ca7c: ldur            d1, [fp, #-0x18]
    // 0x67ca80: fcmp            d1, d0
    // 0x67ca84: b.le            #0x67ca90
    // 0x67ca88: mov             v0.16b, v1.16b
    // 0x67ca8c: b               #0x67cab0
    // 0x67ca90: ldur            d1, [fp, #-0x20]
    // 0x67ca94: fcmp            d0, d1
    // 0x67ca98: b.le            #0x67caa4
    // 0x67ca9c: mov             v0.16b, v1.16b
    // 0x67caa0: b               #0x67cab0
    // 0x67caa4: fcmp            d0, d0
    // 0x67caa8: b.vc            #0x67cab0
    // 0x67caac: mov             v0.16b, v1.16b
    // 0x67cab0: LeaveFrame
    //     0x67cab0: mov             SP, fp
    //     0x67cab4: ldp             fp, lr, [SP], #0x10
    // 0x67cab8: ret
    //     0x67cab8: ret             
    // 0x67cabc: r0 = StackOverflowSharedWithFPURegs()
    //     0x67cabc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x67cac0: b               #0x67ca20
    // 0x67cac4: r0 = NullErrorSharedWithFPURegs()
    //     0x67cac4: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
  }
  _LineCaretMetrics _endOfTextCaretMetrics(_TextLayout) {
    // ** addr: 0x684308, size: 0x30
    // 0x684308: EnterFrame
    //     0x684308: stp             fp, lr, [SP, #-0x10]!
    //     0x68430c: mov             fp, SP
    // 0x684310: CheckStackOverflow
    //     0x684310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x684314: cmp             SP, x16
    //     0x684318: b.ls            #0x684330
    // 0x68431c: ldr             x1, [fp, #0x10]
    // 0x684320: r0 = _computeEndOfTextCaretAnchorOffset()
    //     0x684320: bl              #0x684338  ; [package:flutter/src/painting/text_painter.dart] _TextLayout::_computeEndOfTextCaretAnchorOffset
    // 0x684324: LeaveFrame
    //     0x684324: mov             SP, fp
    //     0x684328: ldp             fp, lr, [SP], #0x10
    // 0x68432c: ret
    //     0x68432c: ret             
    // 0x684330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x684330: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x684334: b               #0x68431c
  }
  _LineCaretMetrics _computeEndOfTextCaretAnchorOffset(_TextLayout) {
    // ** addr: 0x684338, size: 0x360
    // 0x684338: EnterFrame
    //     0x684338: stp             fp, lr, [SP, #-0x10]!
    //     0x68433c: mov             fp, SP
    // 0x684340: AllocStack(0x60)
    //     0x684340: sub             SP, SP, #0x60
    // 0x684344: SetupParameters(_TextLayout this /* r1 => r0, fp-0x8 */)
    //     0x684344: mov             x0, x1
    //     0x684348: stur            x1, [fp, #-8]
    // 0x68434c: CheckStackOverflow
    //     0x68434c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x684350: cmp             SP, x16
    //     0x684354: b.ls            #0x68467c
    // 0x684358: LoadField: r1 = r0->field_b
    //     0x684358: ldur            w1, [x0, #0xb]
    // 0x68435c: DecompressPointer r1
    //     0x68435c: add             x1, x1, HEAP, lsl #32
    // 0x684360: r0 = plainText()
    //     0x684360: bl              #0x6840f0  ; [package:flutter/src/painting/text_painter.dart] TextPainter::plainText
    // 0x684364: mov             x1, x0
    // 0x684368: ldur            x0, [fp, #-8]
    // 0x68436c: stur            x1, [fp, #-0x20]
    // 0x684370: LoadField: r2 = r0->field_f
    //     0x684370: ldur            w2, [x0, #0xf]
    // 0x684374: DecompressPointer r2
    //     0x684374: add             x2, x2, HEAP, lsl #32
    // 0x684378: stur            x2, [fp, #-0x18]
    // 0x68437c: LoadField: r3 = r2->field_7
    //     0x68437c: ldur            w3, [x2, #7]
    // 0x684380: DecompressPointer r3
    //     0x684380: add             x3, x3, HEAP, lsl #32
    // 0x684384: cmp             w3, NULL
    // 0x684388: b.eq            #0x684684
    // 0x68438c: LoadField: r4 = r3->field_7
    //     0x68438c: ldur            x4, [x3, #7]
    // 0x684390: ldr             x3, [x4]
    // 0x684394: stur            x3, [fp, #-0x10]
    // 0x684398: cbnz            x3, #0x6843a8
    // 0x68439c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x68439c: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x6843a0: str             x16, [SP]
    // 0x6843a4: r0 = _throwNew()
    //     0x6843a4: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x6843a8: ldur            x0, [fp, #-8]
    // 0x6843ac: ldur            x2, [fp, #-0x10]
    // 0x6843b0: stur            x2, [fp, #-0x10]
    // 0x6843b4: r1 = <Never>
    //     0x6843b4: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x6843b8: r0 = Pointer()
    //     0x6843b8: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x6843bc: mov             x1, x0
    // 0x6843c0: ldur            x0, [fp, #-0x10]
    // 0x6843c4: StoreField: r1->field_7 = r0
    //     0x6843c4: stur            x0, [x1, #7]
    // 0x6843c8: r0 = _numberOfLines$Getter$FfiNative()
    //     0x6843c8: bl              #0x684250  ; [dart:ui] _NativeParagraph::_numberOfLines$Getter$FfiNative
    // 0x6843cc: sub             x2, x0, #1
    // 0x6843d0: ldur            x0, [fp, #-8]
    // 0x6843d4: stur            x2, [fp, #-0x28]
    // 0x6843d8: LoadField: r1 = r0->field_f
    //     0x6843d8: ldur            w1, [x0, #0xf]
    // 0x6843dc: DecompressPointer r1
    //     0x6843dc: add             x1, x1, HEAP, lsl #32
    // 0x6843e0: stur            x1, [fp, #-0x18]
    // 0x6843e4: LoadField: r3 = r1->field_7
    //     0x6843e4: ldur            w3, [x1, #7]
    // 0x6843e8: DecompressPointer r3
    //     0x6843e8: add             x3, x3, HEAP, lsl #32
    // 0x6843ec: cmp             w3, NULL
    // 0x6843f0: b.eq            #0x684688
    // 0x6843f4: LoadField: r4 = r3->field_7
    //     0x6843f4: ldur            x4, [x3, #7]
    // 0x6843f8: ldr             x3, [x4]
    // 0x6843fc: stur            x3, [fp, #-0x10]
    // 0x684400: cbnz            x3, #0x684410
    // 0x684404: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x684404: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x684408: str             x16, [SP]
    // 0x68440c: r0 = _throwNew()
    //     0x68440c: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x684410: ldur            x0, [fp, #-0x20]
    // 0x684414: ldur            x2, [fp, #-0x10]
    // 0x684418: stur            x2, [fp, #-0x10]
    // 0x68441c: r1 = <Never>
    //     0x68441c: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x684420: r0 = Pointer()
    //     0x684420: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x684424: mov             x1, x0
    // 0x684428: ldur            x0, [fp, #-0x10]
    // 0x68442c: StoreField: r1->field_7 = r0
    //     0x68442c: stur            x0, [x1, #7]
    // 0x684430: ldur            x2, [fp, #-0x28]
    // 0x684434: r3 = Closure: (bool, double, double, double, double, double, double, double, int) => LineMetrics from Function 'LineMetrics._@17065589': static.
    //     0x684434: ldr             x3, [PP, #0x4890]  ; [pp+0x4890] Closure: (bool, double, double, double, double, double, double, double, int) => LineMetrics from Function 'LineMetrics._@17065589': static. (0x7e54fb0846ec)
    // 0x684438: r0 = __getLineMetricsAt$Method$FfiNative()
    //     0x684438: bl              #0x683c00  ; [dart:ui] _NativeParagraph::__getLineMetricsAt$Method$FfiNative
    // 0x68443c: stur            x0, [fp, #-0x30]
    // 0x684440: cmp             w0, NULL
    // 0x684444: b.eq            #0x68468c
    // 0x684448: ldur            x1, [fp, #-0x20]
    // 0x68444c: LoadField: r2 = r1->field_7
    //     0x68444c: ldur            w2, [x1, #7]
    // 0x684450: r3 = LoadInt32Instr(r2)
    //     0x684450: sbfx            x3, x2, #1, #0x1f
    // 0x684454: sub             x2, x3, #1
    // 0x684458: stur            x2, [fp, #-0x10]
    // 0x68445c: lsl             x3, x2, #1
    // 0x684460: stp             x3, x1, [SP]
    // 0x684464: r0 = []()
    //     0x684464: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0x684468: mov             x2, x0
    // 0x68446c: stur            x2, [fp, #-0x18]
    // 0x684470: LoadField: r0 = r2->field_7
    //     0x684470: ldur            w0, [x2, #7]
    // 0x684474: r1 = LoadInt32Instr(r0)
    //     0x684474: sbfx            x1, x0, #1, #0x1f
    // 0x684478: mov             x0, x1
    // 0x68447c: r1 = 0
    //     0x68447c: movz            x1, #0
    // 0x684480: cmp             x1, x0
    // 0x684484: b.hs            #0x684690
    // 0x684488: r0 = LoadClassIdInstr(r2)
    //     0x684488: ldur            x0, [x2, #-1]
    //     0x68448c: ubfx            x0, x0, #0xc, #0x14
    // 0x684490: lsl             x0, x0, #1
    // 0x684494: cmp             w0, #0xbc
    // 0x684498: b.ne            #0x6844a4
    // 0x68449c: ArrayLoad: r0 = r2[-8]  ; TypedUnsigned_1
    //     0x68449c: ldrb            w0, [x2, #0xf]
    // 0x6844a0: b               #0x6844a8
    // 0x6844a4: ldurh           w0, [x2, #0xf]
    // 0x6844a8: cmp             x0, #9
    // 0x6844ac: b.ne            #0x6844b8
    // 0x6844b0: r1 = true
    //     0x6844b0: add             x1, NULL, #0x20  ; true
    // 0x6844b4: b               #0x684524
    // 0x6844b8: cmp             x0, #0xa0
    // 0x6844bc: b.eq            #0x6844d8
    // 0x6844c0: r17 = 8199
    //     0x6844c0: movz            x17, #0x2007
    // 0x6844c4: cmp             x0, x17
    // 0x6844c8: b.eq            #0x6844d8
    // 0x6844cc: r17 = 8239
    //     0x6844cc: movz            x17, #0x202f
    // 0x6844d0: cmp             x0, x17
    // 0x6844d4: b.ne            #0x6844e0
    // 0x6844d8: r1 = false
    //     0x6844d8: add             x1, NULL, #0x30  ; false
    // 0x6844dc: b               #0x684524
    // 0x6844e0: r0 = InitLateStaticField(0xbb8) // [package:flutter/src/painting/text_painter.dart] _TextLayout::_regExpSpaceSeparators
    //     0x6844e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6844e4: ldr             x0, [x0, #0x1770]
    //     0x6844e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6844ec: cmp             w0, w16
    //     0x6844f0: b.ne            #0x6844fc
    //     0x6844f4: ldr             x2, [PP, #0x48a8]  ; [pp+0x48a8] Field <_TextLayout@663105366._regExpSpaceSeparators@663105366>: static late final (offset: 0xbb8)
    //     0x6844f8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6844fc: ldur            x16, [fp, #-0x18]
    // 0x684500: stp             x16, x0, [SP, #8]
    // 0x684504: str             xzr, [SP]
    // 0x684508: r0 = _ExecuteMatch()
    //     0x684508: bl              #0x644494  ; [dart:core] _RegExp::_ExecuteMatch
    // 0x68450c: cmp             w0, NULL
    // 0x684510: b.ne            #0x68451c
    // 0x684514: r0 = false
    //     0x684514: add             x0, NULL, #0x30  ; false
    // 0x684518: b               #0x684520
    // 0x68451c: r0 = true
    //     0x68451c: add             x0, NULL, #0x20  ; true
    // 0x684520: mov             x1, x0
    // 0x684524: ldur            x0, [fp, #-0x30]
    // 0x684528: LoadField: d0 = r0->field_3b
    //     0x684528: ldur            d0, [x0, #0x3b]
    // 0x68452c: stur            d0, [fp, #-0x38]
    // 0x684530: tbnz            w1, #4, #0x6845ec
    // 0x684534: ldur            x1, [fp, #-8]
    // 0x684538: LoadField: r2 = r1->field_f
    //     0x684538: ldur            w2, [x1, #0xf]
    // 0x68453c: DecompressPointer r2
    //     0x68453c: add             x2, x2, HEAP, lsl #32
    // 0x684540: stur            x2, [fp, #-0x18]
    // 0x684544: LoadField: r3 = r2->field_7
    //     0x684544: ldur            w3, [x2, #7]
    // 0x684548: DecompressPointer r3
    //     0x684548: add             x3, x3, HEAP, lsl #32
    // 0x68454c: cmp             w3, NULL
    // 0x684550: b.eq            #0x684694
    // 0x684554: LoadField: r4 = r3->field_7
    //     0x684554: ldur            x4, [x3, #7]
    // 0x684558: ldr             x3, [x4]
    // 0x68455c: stur            x3, [fp, #-0x28]
    // 0x684560: cbnz            x3, #0x684570
    // 0x684564: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x684564: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x684568: str             x16, [SP]
    // 0x68456c: r0 = _throwNew()
    //     0x68456c: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x684570: ldur            x0, [fp, #-0x28]
    // 0x684574: stur            x0, [fp, #-0x28]
    // 0x684578: r1 = <Never>
    //     0x684578: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x68457c: r0 = Pointer()
    //     0x68457c: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x684580: mov             x1, x0
    // 0x684584: ldur            x0, [fp, #-0x28]
    // 0x684588: StoreField: r1->field_7 = r0
    //     0x684588: stur            x0, [x1, #7]
    // 0x68458c: ldur            x2, [fp, #-0x10]
    // 0x684590: r3 = Closure: (double, double, double, double, int, int, bool) => GlyphInfo from Function 'GlyphInfo._@17065589': static.
    //     0x684590: ldr             x3, [PP, #0x4888]  ; [pp+0x4888] Closure: (double, double, double, double, int, int, bool) => GlyphInfo from Function 'GlyphInfo._@17065589': static. (0x7e54fb0847a4)
    // 0x684594: r0 = __getGlyphInfoAt$Method$FfiNative()
    //     0x684594: bl              #0x683df4  ; [dart:ui] _NativeParagraph::__getGlyphInfoAt$Method$FfiNative
    // 0x684598: cmp             w0, NULL
    // 0x68459c: b.eq            #0x6845e4
    // 0x6845a0: ldur            x1, [fp, #-8]
    // 0x6845a4: LoadField: r2 = r0->field_7
    //     0x6845a4: ldur            w2, [x0, #7]
    // 0x6845a8: DecompressPointer r2
    //     0x6845a8: add             x2, x2, HEAP, lsl #32
    // 0x6845ac: LoadField: r0 = r1->field_7
    //     0x6845ac: ldur            w0, [x1, #7]
    // 0x6845b0: DecompressPointer r0
    //     0x6845b0: add             x0, x0, HEAP, lsl #32
    // 0x6845b4: LoadField: r1 = r0->field_7
    //     0x6845b4: ldur            x1, [x0, #7]
    // 0x6845b8: cmp             x1, #0
    // 0x6845bc: b.gt            #0x6845c8
    // 0x6845c0: LoadField: d0 = r2->field_7
    //     0x6845c0: ldur            d0, [x2, #7]
    // 0x6845c4: b               #0x6845cc
    // 0x6845c8: ArrayLoad: d0 = r2[0]  ; List_8
    //     0x6845c8: ldur            d0, [x2, #0x17]
    // 0x6845cc: LoadField: d1 = r2->field_1f
    //     0x6845cc: ldur            d1, [x2, #0x1f]
    // 0x6845d0: LoadField: d2 = r2->field_f
    //     0x6845d0: ldur            d2, [x2, #0xf]
    // 0x6845d4: fsub            d3, d1, d2
    // 0x6845d8: mov             v2.16b, v0.16b
    // 0x6845dc: mov             v1.16b, v3.16b
    // 0x6845e0: b               #0x68462c
    // 0x6845e4: ldur            x1, [fp, #-8]
    // 0x6845e8: b               #0x6845f0
    // 0x6845ec: ldur            x1, [fp, #-8]
    // 0x6845f0: LoadField: r0 = r1->field_7
    //     0x6845f0: ldur            w0, [x1, #7]
    // 0x6845f4: DecompressPointer r0
    //     0x6845f4: add             x0, x0, HEAP, lsl #32
    // 0x6845f8: LoadField: r1 = r0->field_7
    //     0x6845f8: ldur            x1, [x0, #7]
    // 0x6845fc: cmp             x1, #0
    // 0x684600: b.gt            #0x684610
    // 0x684604: ldur            x1, [fp, #-0x30]
    // 0x684608: LoadField: d0 = r1->field_33
    //     0x684608: ldur            d0, [x1, #0x33]
    // 0x68460c: b               #0x684624
    // 0x684610: ldur            x1, [fp, #-0x30]
    // 0x684614: LoadField: d0 = r1->field_33
    //     0x684614: ldur            d0, [x1, #0x33]
    // 0x684618: LoadField: d1 = r1->field_2b
    //     0x684618: ldur            d1, [x1, #0x2b]
    // 0x68461c: fadd            d2, d0, d1
    // 0x684620: mov             v0.16b, v2.16b
    // 0x684624: LoadField: d1 = r1->field_23
    //     0x684624: ldur            d1, [x1, #0x23]
    // 0x684628: mov             v2.16b, v0.16b
    // 0x68462c: ldur            d0, [fp, #-0x38]
    // 0x684630: stur            x0, [fp, #-8]
    // 0x684634: stur            d2, [fp, #-0x40]
    // 0x684638: stur            d1, [fp, #-0x48]
    // 0x68463c: r0 = Offset()
    //     0x68463c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x684640: ldur            d0, [fp, #-0x40]
    // 0x684644: stur            x0, [fp, #-0x18]
    // 0x684648: StoreField: r0->field_7 = d0
    //     0x684648: stur            d0, [x0, #7]
    // 0x68464c: ldur            d0, [fp, #-0x38]
    // 0x684650: StoreField: r0->field_f = d0
    //     0x684650: stur            d0, [x0, #0xf]
    // 0x684654: r0 = _LineCaretMetrics()
    //     0x684654: bl              #0x683b20  ; Allocate_LineCaretMetricsStub -> _LineCaretMetrics (size=0x18)
    // 0x684658: ldur            x1, [fp, #-0x18]
    // 0x68465c: StoreField: r0->field_7 = r1
    //     0x68465c: stur            w1, [x0, #7]
    // 0x684660: ldur            x1, [fp, #-8]
    // 0x684664: StoreField: r0->field_b = r1
    //     0x684664: stur            w1, [x0, #0xb]
    // 0x684668: ldur            d0, [fp, #-0x48]
    // 0x68466c: StoreField: r0->field_f = d0
    //     0x68466c: stur            d0, [x0, #0xf]
    // 0x684670: LeaveFrame
    //     0x684670: mov             SP, fp
    //     0x684674: ldp             fp, lr, [SP], #0x10
    // 0x684678: ret
    //     0x684678: ret             
    // 0x68467c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68467c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x684680: b               #0x684358
    // 0x684684: r0 = NullErrorSharedWithoutFPURegs()
    //     0x684684: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x684688: r0 = NullErrorSharedWithoutFPURegs()
    //     0x684688: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x68468c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68468c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x684690: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x684690: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x684694: r0 = NullErrorSharedWithFPURegs()
    //     0x684694: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
  }
  static RegExp _regExpSpaceSeparators() {
    // ** addr: 0x684698, size: 0x54
    // 0x684698: EnterFrame
    //     0x684698: stp             fp, lr, [SP, #-0x10]!
    //     0x68469c: mov             fp, SP
    // 0x6846a0: AllocStack(0x30)
    //     0x6846a0: sub             SP, SP, #0x30
    // 0x6846a4: CheckStackOverflow
    //     0x6846a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6846a8: cmp             SP, x16
    //     0x6846ac: b.ls            #0x6846e4
    // 0x6846b0: r16 = "\\p{Space_Separator}"
    //     0x6846b0: ldr             x16, [PP, #0x48b0]  ; [pp+0x48b0] "\\p{Space_Separator}"
    // 0x6846b4: stp             x16, NULL, [SP, #0x20]
    // 0x6846b8: r16 = false
    //     0x6846b8: add             x16, NULL, #0x30  ; false
    // 0x6846bc: r30 = true
    //     0x6846bc: add             lr, NULL, #0x20  ; true
    // 0x6846c0: stp             lr, x16, [SP, #0x10]
    // 0x6846c4: r16 = true
    //     0x6846c4: add             x16, NULL, #0x20  ; true
    // 0x6846c8: r30 = false
    //     0x6846c8: add             lr, NULL, #0x30  ; false
    // 0x6846cc: stp             lr, x16, [SP]
    // 0x6846d0: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x6846d0: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x6846d4: r0 = _RegExp()
    //     0x6846d4: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x6846d8: LeaveFrame
    //     0x6846d8: mov             SP, fp
    //     0x6846dc: ldp             fp, lr, [SP], #0x10
    // 0x6846e0: ret
    //     0x6846e0: ret             
    // 0x6846e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6846e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6846e8: b               #0x6846b0
  }
}

// class id: 3228, size: 0x10, field offset: 0x8
//   const constructor, 
class _UntilTextBoundary extends TextBoundary {

  _ getLeadingTextBoundaryAt(/* No info */) {
    // ** addr: 0xd8da60, size: 0xac
    // 0xd8da60: EnterFrame
    //     0xd8da60: stp             fp, lr, [SP, #-0x10]!
    //     0xd8da64: mov             fp, SP
    // 0xd8da68: AllocStack(0x10)
    //     0xd8da68: sub             SP, SP, #0x10
    // 0xd8da6c: SetupParameters(_UntilTextBoundary this /* r1 => r0, fp-0x8 */)
    //     0xd8da6c: mov             x0, x1
    //     0xd8da70: stur            x1, [fp, #-8]
    // 0xd8da74: CheckStackOverflow
    //     0xd8da74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8da78: cmp             SP, x16
    //     0xd8da7c: b.ls            #0xd8db04
    // 0xd8da80: tbz             x2, #0x3f, #0xd8da94
    // 0xd8da84: r0 = Null
    //     0xd8da84: mov             x0, NULL
    // 0xd8da88: LeaveFrame
    //     0xd8da88: mov             SP, fp
    //     0xd8da8c: ldp             fp, lr, [SP], #0x10
    // 0xd8da90: ret
    //     0xd8da90: ret             
    // 0xd8da94: LoadField: r1 = r0->field_b
    //     0xd8da94: ldur            w1, [x0, #0xb]
    // 0xd8da98: DecompressPointer r1
    //     0xd8da98: add             x1, x1, HEAP, lsl #32
    // 0xd8da9c: r0 = getLeadingTextBoundaryAt()
    //     0xd8da9c: bl              #0xd8e21c  ; [package:flutter/src/services/text_boundary.dart] TextBoundary::getLeadingTextBoundaryAt
    // 0xd8daa0: stur            x0, [fp, #-0x10]
    // 0xd8daa4: cmp             w0, NULL
    // 0xd8daa8: b.eq            #0xd8dad4
    // 0xd8daac: ldur            x4, [fp, #-8]
    // 0xd8dab0: LoadField: r1 = r4->field_7
    //     0xd8dab0: ldur            w1, [x4, #7]
    // 0xd8dab4: DecompressPointer r1
    //     0xd8dab4: add             x1, x1, HEAP, lsl #32
    // 0xd8dab8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xd8dab8: ldur            w2, [x1, #0x17]
    // 0xd8dabc: DecompressPointer r2
    //     0xd8dabc: add             x2, x2, HEAP, lsl #32
    // 0xd8dac0: mov             x1, x2
    // 0xd8dac4: mov             x2, x0
    // 0xd8dac8: r3 = false
    //     0xd8dac8: add             x3, NULL, #0x30  ; false
    // 0xd8dacc: r0 = _skipSpacesAndPunctuations()
    //     0xd8dacc: bl              #0xd8db4c  ; [package:flutter/src/painting/text_painter.dart] WordBoundary::_skipSpacesAndPunctuations
    // 0xd8dad0: tbnz            w0, #4, #0xd8dadc
    // 0xd8dad4: ldur            x0, [fp, #-0x10]
    // 0xd8dad8: b               #0xd8daf8
    // 0xd8dadc: ldur            x0, [fp, #-0x10]
    // 0xd8dae0: r1 = LoadInt32Instr(r0)
    //     0xd8dae0: sbfx            x1, x0, #1, #0x1f
    //     0xd8dae4: tbz             w0, #0, #0xd8daec
    //     0xd8dae8: ldur            x1, [x0, #7]
    // 0xd8daec: sub             x2, x1, #1
    // 0xd8daf0: ldur            x1, [fp, #-8]
    // 0xd8daf4: r0 = getLeadingTextBoundaryAt()
    //     0xd8daf4: bl              #0xd8da60  ; [package:flutter/src/painting/text_painter.dart] _UntilTextBoundary::getLeadingTextBoundaryAt
    // 0xd8daf8: LeaveFrame
    //     0xd8daf8: mov             SP, fp
    //     0xd8dafc: ldp             fp, lr, [SP], #0x10
    // 0xd8db00: ret
    //     0xd8db00: ret             
    // 0xd8db04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8db04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8db08: b               #0xd8da80
  }
  _ getTrailingTextBoundaryAt(/* No info */) {
    // ** addr: 0xd97b24, size: 0xfc
    // 0xd97b24: EnterFrame
    //     0xd97b24: stp             fp, lr, [SP, #-0x10]!
    //     0xd97b28: mov             fp, SP
    // 0xd97b2c: AllocStack(0x20)
    //     0xd97b2c: sub             SP, SP, #0x20
    // 0xd97b30: SetupParameters(_UntilTextBoundary this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xd97b30: mov             x3, x1
    //     0xd97b34: stur            x1, [fp, #-0x10]
    //     0xd97b38: stur            x2, [fp, #-0x18]
    // 0xd97b3c: CheckStackOverflow
    //     0xd97b3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd97b40: cmp             SP, x16
    //     0xd97b44: b.ls            #0xd97c18
    // 0xd97b48: LoadField: r4 = r3->field_b
    //     0xd97b48: ldur            w4, [x3, #0xb]
    // 0xd97b4c: DecompressPointer r4
    //     0xd97b4c: add             x4, x4, HEAP, lsl #32
    // 0xd97b50: stur            x4, [fp, #-8]
    // 0xd97b54: cmp             x2, #0
    // 0xd97b58: b.gt            #0xd97bb0
    // 0xd97b5c: tbz             x2, #0x3f, #0xd97b68
    // 0xd97b60: r2 = 0
    //     0xd97b60: movz            x2, #0
    // 0xd97b64: b               #0xd97bb0
    // 0xd97b68: r0 = BoxInt64Instr(r2)
    //     0xd97b68: sbfiz           x0, x2, #1, #0x1f
    //     0xd97b6c: cmp             x2, x0, asr #1
    //     0xd97b70: b.eq            #0xd97b7c
    //     0xd97b74: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd97b78: stur            x2, [x0, #7]
    // 0xd97b7c: r1 = 60
    //     0xd97b7c: movz            x1, #0x3c
    // 0xd97b80: branchIfSmi(r0, 0xd97b8c)
    //     0xd97b80: tbz             w0, #0, #0xd97b8c
    // 0xd97b84: r1 = LoadClassIdInstr(r0)
    //     0xd97b84: ldur            x1, [x0, #-1]
    //     0xd97b88: ubfx            x1, x1, #0xc, #0x14
    // 0xd97b8c: str             x0, [SP]
    // 0xd97b90: mov             x0, x1
    // 0xd97b94: r0 = GDT[cid_x0 + -0xfb8]()
    //     0xd97b94: sub             lr, x0, #0xfb8
    //     0xd97b98: ldr             lr, [x21, lr, lsl #3]
    //     0xd97b9c: blr             lr
    // 0xd97ba0: tbnz            w0, #4, #0xd97bac
    // 0xd97ba4: r2 = 0
    //     0xd97ba4: movz            x2, #0
    // 0xd97ba8: b               #0xd97bb0
    // 0xd97bac: ldur            x2, [fp, #-0x18]
    // 0xd97bb0: ldur            x1, [fp, #-8]
    // 0xd97bb4: r0 = getTrailingTextBoundaryAt()
    //     0xd97bb4: bl              #0xd97f1c  ; [package:flutter/src/services/text_boundary.dart] TextBoundary::getTrailingTextBoundaryAt
    // 0xd97bb8: stur            x0, [fp, #-8]
    // 0xd97bbc: cmp             w0, NULL
    // 0xd97bc0: b.eq            #0xd97bec
    // 0xd97bc4: ldur            x4, [fp, #-0x10]
    // 0xd97bc8: LoadField: r1 = r4->field_7
    //     0xd97bc8: ldur            w1, [x4, #7]
    // 0xd97bcc: DecompressPointer r1
    //     0xd97bcc: add             x1, x1, HEAP, lsl #32
    // 0xd97bd0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xd97bd0: ldur            w2, [x1, #0x17]
    // 0xd97bd4: DecompressPointer r2
    //     0xd97bd4: add             x2, x2, HEAP, lsl #32
    // 0xd97bd8: mov             x1, x2
    // 0xd97bdc: mov             x2, x0
    // 0xd97be0: r3 = true
    //     0xd97be0: add             x3, NULL, #0x20  ; true
    // 0xd97be4: r0 = _skipSpacesAndPunctuations()
    //     0xd97be4: bl              #0xd8db4c  ; [package:flutter/src/painting/text_painter.dart] WordBoundary::_skipSpacesAndPunctuations
    // 0xd97be8: tbnz            w0, #4, #0xd97bf4
    // 0xd97bec: ldur            x0, [fp, #-8]
    // 0xd97bf0: b               #0xd97c0c
    // 0xd97bf4: ldur            x0, [fp, #-8]
    // 0xd97bf8: r2 = LoadInt32Instr(r0)
    //     0xd97bf8: sbfx            x2, x0, #1, #0x1f
    //     0xd97bfc: tbz             w0, #0, #0xd97c04
    //     0xd97c00: ldur            x2, [x0, #7]
    // 0xd97c04: ldur            x1, [fp, #-0x10]
    // 0xd97c08: r0 = getTrailingTextBoundaryAt()
    //     0xd97c08: bl              #0xd97b24  ; [package:flutter/src/painting/text_painter.dart] _UntilTextBoundary::getTrailingTextBoundaryAt
    // 0xd97c0c: LeaveFrame
    //     0xd97c0c: mov             SP, fp
    //     0xd97c10: ldp             fp, lr, [SP], #0x10
    // 0xd97c14: ret
    //     0xd97c14: ret             
    // 0xd97c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd97c18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd97c1c: b               #0xd97b48
  }
}

// class id: 3229, size: 0x14, field offset: 0x8
class WordBoundary extends TextBoundary {

  late final TextBoundary moveByWordBoundary; // offset: 0x10
  static late final RegExp _regExpSpaceSeparatorOrPunctuaion; // offset: 0xbac

  TextBoundary moveByWordBoundary(WordBoundary) {
    // ** addr: 0xd804cc, size: 0x68
    // 0xd804cc: EnterFrame
    //     0xd804cc: stp             fp, lr, [SP, #-0x10]!
    //     0xd804d0: mov             fp, SP
    // 0xd804d4: AllocStack(0x10)
    //     0xd804d4: sub             SP, SP, #0x10
    // 0xd804d8: CheckStackOverflow
    //     0xd804d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd804dc: cmp             SP, x16
    //     0xd804e0: b.ls            #0xd8052c
    // 0xd804e4: ldr             x1, [fp, #0x10]
    // 0xd804e8: r0 = 60
    //     0xd804e8: movz            x0, #0x3c
    // 0xd804ec: branchIfSmi(r1, 0xd804f8)
    //     0xd804ec: tbz             w1, #0, #0xd804f8
    // 0xd804f0: r0 = LoadClassIdInstr(r1)
    //     0xd804f0: ldur            x0, [x1, #-1]
    //     0xd804f4: ubfx            x0, x0, #0xc, #0x14
    // 0xd804f8: str             x1, [SP]
    // 0xd804fc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xd804fc: sub             lr, x0, #1, lsl #12
    //     0xd80500: ldr             lr, [x21, lr, lsl #3]
    //     0xd80504: blr             lr
    // 0xd80508: stur            x0, [fp, #-8]
    // 0xd8050c: r0 = _UntilTextBoundary()
    //     0xd8050c: bl              #0xd80534  ; Allocate_UntilTextBoundaryStub -> _UntilTextBoundary (size=0x10)
    // 0xd80510: ldr             x1, [fp, #0x10]
    // 0xd80514: StoreField: r0->field_b = r1
    //     0xd80514: stur            w1, [x0, #0xb]
    // 0xd80518: ldur            x1, [fp, #-8]
    // 0xd8051c: StoreField: r0->field_7 = r1
    //     0xd8051c: stur            w1, [x0, #7]
    // 0xd80520: LeaveFrame
    //     0xd80520: mov             SP, fp
    //     0xd80524: ldp             fp, lr, [SP], #0x10
    // 0xd80528: ret
    //     0xd80528: ret             
    // 0xd8052c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8052c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd80530: b               #0xd804e4
  }
  [closure] bool _skipSpacesAndPunctuations(dynamic, int, bool) {
    // ** addr: 0xd8db0c, size: 0x40
    // 0xd8db0c: EnterFrame
    //     0xd8db0c: stp             fp, lr, [SP, #-0x10]!
    //     0xd8db10: mov             fp, SP
    // 0xd8db14: ldr             x0, [fp, #0x20]
    // 0xd8db18: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd8db18: ldur            w1, [x0, #0x17]
    // 0xd8db1c: DecompressPointer r1
    //     0xd8db1c: add             x1, x1, HEAP, lsl #32
    // 0xd8db20: CheckStackOverflow
    //     0xd8db20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8db24: cmp             SP, x16
    //     0xd8db28: b.ls            #0xd8db44
    // 0xd8db2c: ldr             x2, [fp, #0x18]
    // 0xd8db30: ldr             x3, [fp, #0x10]
    // 0xd8db34: r0 = _skipSpacesAndPunctuations()
    //     0xd8db34: bl              #0xd8db4c  ; [package:flutter/src/painting/text_painter.dart] WordBoundary::_skipSpacesAndPunctuations
    // 0xd8db38: LeaveFrame
    //     0xd8db38: mov             SP, fp
    //     0xd8db3c: ldp             fp, lr, [SP], #0x10
    // 0xd8db40: ret
    //     0xd8db40: ret             
    // 0xd8db44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8db44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8db48: b               #0xd8db2c
  }
  _ _skipSpacesAndPunctuations(/* No info */) {
    // ** addr: 0xd8db4c, size: 0x1b4
    // 0xd8db4c: EnterFrame
    //     0xd8db4c: stp             fp, lr, [SP, #-0x10]!
    //     0xd8db50: mov             fp, SP
    // 0xd8db54: AllocStack(0x38)
    //     0xd8db54: sub             SP, SP, #0x38
    // 0xd8db58: SetupParameters(WordBoundary this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xd8db58: mov             x4, x1
    //     0xd8db5c: mov             x0, x2
    //     0xd8db60: stur            x1, [fp, #-8]
    //     0xd8db64: stur            x2, [fp, #-0x10]
    //     0xd8db68: stur            x3, [fp, #-0x18]
    // 0xd8db6c: CheckStackOverflow
    //     0xd8db6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8db70: cmp             SP, x16
    //     0xd8db74: b.ls            #0xd8dcf8
    // 0xd8db78: tbnz            w3, #4, #0xd8db90
    // 0xd8db7c: r1 = LoadInt32Instr(r0)
    //     0xd8db7c: sbfx            x1, x0, #1, #0x1f
    //     0xd8db80: tbz             w0, #0, #0xd8db88
    //     0xd8db84: ldur            x1, [x0, #7]
    // 0xd8db88: sub             x2, x1, #1
    // 0xd8db8c: b               #0xd8dba0
    // 0xd8db90: r1 = LoadInt32Instr(r0)
    //     0xd8db90: sbfx            x1, x0, #1, #0x1f
    //     0xd8db94: tbz             w0, #0, #0xd8db9c
    //     0xd8db98: ldur            x1, [x0, #7]
    // 0xd8db9c: mov             x2, x1
    // 0xd8dba0: mov             x1, x4
    // 0xd8dba4: r0 = _codePointAt()
    //     0xd8dba4: bl              #0xd8dd00  ; [package:flutter/src/painting/text_painter.dart] WordBoundary::_codePointAt
    // 0xd8dba8: mov             x3, x0
    // 0xd8dbac: ldur            x0, [fp, #-8]
    // 0xd8dbb0: stur            x3, [fp, #-0x20]
    // 0xd8dbb4: LoadField: r1 = r0->field_7
    //     0xd8dbb4: ldur            w1, [x0, #7]
    // 0xd8dbb8: DecompressPointer r1
    //     0xd8dbb8: add             x1, x1, HEAP, lsl #32
    // 0xd8dbbc: ldur            x0, [fp, #-0x18]
    // 0xd8dbc0: tbnz            w0, #4, #0xd8dbd8
    // 0xd8dbc4: ldur            x0, [fp, #-0x10]
    // 0xd8dbc8: r2 = LoadInt32Instr(r0)
    //     0xd8dbc8: sbfx            x2, x0, #1, #0x1f
    //     0xd8dbcc: tbz             w0, #0, #0xd8dbd4
    //     0xd8dbd0: ldur            x2, [x0, #7]
    // 0xd8dbd4: b               #0xd8dbf0
    // 0xd8dbd8: ldur            x0, [fp, #-0x10]
    // 0xd8dbdc: r2 = LoadInt32Instr(r0)
    //     0xd8dbdc: sbfx            x2, x0, #1, #0x1f
    //     0xd8dbe0: tbz             w0, #0, #0xd8dbe8
    //     0xd8dbe4: ldur            x2, [x0, #7]
    // 0xd8dbe8: sub             x0, x2, #1
    // 0xd8dbec: mov             x2, x0
    // 0xd8dbf0: r0 = codeUnitAt()
    //     0xd8dbf0: bl              #0x75c4d8  ; [package:flutter/src/painting/inline_span.dart] InlineSpan::codeUnitAt
    // 0xd8dbf4: ldur            x2, [fp, #-0x20]
    // 0xd8dbf8: cmp             w2, NULL
    // 0xd8dbfc: b.eq            #0xd8dc88
    // 0xd8dc00: cmp             w0, NULL
    // 0xd8dc04: b.eq            #0xd8dc88
    // 0xd8dc08: r1 = LoadInt32Instr(r2)
    //     0xd8dc08: sbfx            x1, x2, #1, #0x1f
    //     0xd8dc0c: tbz             w2, #0, #0xd8dc14
    //     0xd8dc10: ldur            x1, [x2, #7]
    // 0xd8dc14: cmp             x1, #0xa
    // 0xd8dc18: b.eq            #0xd8dc88
    // 0xd8dc1c: cmp             x1, #0x85
    // 0xd8dc20: b.eq            #0xd8dc88
    // 0xd8dc24: cmp             x1, #0xb
    // 0xd8dc28: b.eq            #0xd8dc88
    // 0xd8dc2c: cmp             x1, #0xc
    // 0xd8dc30: b.eq            #0xd8dc88
    // 0xd8dc34: r17 = 8232
    //     0xd8dc34: movz            x17, #0x2028
    // 0xd8dc38: cmp             x1, x17
    // 0xd8dc3c: b.eq            #0xd8dc88
    // 0xd8dc40: r17 = 8233
    //     0xd8dc40: movz            x17, #0x2029
    // 0xd8dc44: cmp             x1, x17
    // 0xd8dc48: b.eq            #0xd8dc88
    // 0xd8dc4c: r1 = LoadInt32Instr(r0)
    //     0xd8dc4c: sbfx            x1, x0, #1, #0x1f
    // 0xd8dc50: cmp             x1, #0xa
    // 0xd8dc54: b.eq            #0xd8dc88
    // 0xd8dc58: cmp             x1, #0x85
    // 0xd8dc5c: b.eq            #0xd8dc88
    // 0xd8dc60: cmp             x1, #0xb
    // 0xd8dc64: b.eq            #0xd8dc88
    // 0xd8dc68: cmp             x1, #0xc
    // 0xd8dc6c: b.eq            #0xd8dc88
    // 0xd8dc70: r17 = 8232
    //     0xd8dc70: movz            x17, #0x2028
    // 0xd8dc74: cmp             x1, x17
    // 0xd8dc78: b.eq            #0xd8dc88
    // 0xd8dc7c: r17 = 8233
    //     0xd8dc7c: movz            x17, #0x2029
    // 0xd8dc80: cmp             x1, x17
    // 0xd8dc84: b.ne            #0xd8dc90
    // 0xd8dc88: r0 = true
    //     0xd8dc88: add             x0, NULL, #0x20  ; true
    // 0xd8dc8c: b               #0xd8dcec
    // 0xd8dc90: r0 = InitLateStaticField(0xbac) // [package:flutter/src/painting/text_painter.dart] WordBoundary::_regExpSpaceSeparatorOrPunctuaion
    //     0xd8dc90: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xd8dc94: ldr             x0, [x0, #0x1758]
    //     0xd8dc98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xd8dc9c: cmp             w0, w16
    //     0xd8dca0: b.ne            #0xd8dcb0
    //     0xd8dca4: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b448] Field <WordBoundary._regExpSpaceSeparatorOrPunctuaion@663105366>: static late final (offset: 0xbac)
    //     0xd8dca8: ldr             x2, [x2, #0x448]
    //     0xd8dcac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xd8dcb0: ldur            x2, [fp, #-0x20]
    // 0xd8dcb4: r1 = Null
    //     0xd8dcb4: mov             x1, NULL
    // 0xd8dcb8: stur            x0, [fp, #-8]
    // 0xd8dcbc: r0 = String.fromCharCode()
    //     0xd8dcbc: bl              #0x602bac  ; [dart:core] String::String.fromCharCode
    // 0xd8dcc0: ldur            x16, [fp, #-8]
    // 0xd8dcc4: stp             x0, x16, [SP, #8]
    // 0xd8dcc8: str             xzr, [SP]
    // 0xd8dccc: r0 = _ExecuteMatch()
    //     0xd8dccc: bl              #0x644494  ; [dart:core] _RegExp::_ExecuteMatch
    // 0xd8dcd0: cmp             w0, NULL
    // 0xd8dcd4: b.ne            #0xd8dce0
    // 0xd8dcd8: r1 = false
    //     0xd8dcd8: add             x1, NULL, #0x30  ; false
    // 0xd8dcdc: b               #0xd8dce4
    // 0xd8dce0: r1 = true
    //     0xd8dce0: add             x1, NULL, #0x20  ; true
    // 0xd8dce4: eor             x2, x1, #0x10
    // 0xd8dce8: mov             x0, x2
    // 0xd8dcec: LeaveFrame
    //     0xd8dcec: mov             SP, fp
    //     0xd8dcf0: ldp             fp, lr, [SP], #0x10
    // 0xd8dcf4: ret
    //     0xd8dcf4: ret             
    // 0xd8dcf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8dcf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8dcfc: b               #0xd8db78
  }
  _ _codePointAt(/* No info */) {
    // ** addr: 0xd8dd00, size: 0x138
    // 0xd8dd00: EnterFrame
    //     0xd8dd00: stp             fp, lr, [SP, #-0x10]!
    //     0xd8dd04: mov             fp, SP
    // 0xd8dd08: AllocStack(0x18)
    //     0xd8dd08: sub             SP, SP, #0x18
    // 0xd8dd0c: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xd8dd0c: mov             x0, x2
    //     0xd8dd10: stur            x2, [fp, #-0x10]
    // 0xd8dd14: CheckStackOverflow
    //     0xd8dd14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8dd18: cmp             SP, x16
    //     0xd8dd1c: b.ls            #0xd8de28
    // 0xd8dd20: LoadField: r3 = r1->field_7
    //     0xd8dd20: ldur            w3, [x1, #7]
    // 0xd8dd24: DecompressPointer r3
    //     0xd8dd24: add             x3, x3, HEAP, lsl #32
    // 0xd8dd28: mov             x1, x3
    // 0xd8dd2c: mov             x2, x0
    // 0xd8dd30: stur            x3, [fp, #-8]
    // 0xd8dd34: r0 = codeUnitAt()
    //     0xd8dd34: bl              #0x75c4d8  ; [package:flutter/src/painting/inline_span.dart] InlineSpan::codeUnitAt
    // 0xd8dd38: cmp             w0, NULL
    // 0xd8dd3c: b.ne            #0xd8dd50
    // 0xd8dd40: r0 = Null
    //     0xd8dd40: mov             x0, NULL
    // 0xd8dd44: LeaveFrame
    //     0xd8dd44: mov             SP, fp
    //     0xd8dd48: ldp             fp, lr, [SP], #0x10
    // 0xd8dd4c: ret
    //     0xd8dd4c: ret             
    // 0xd8dd50: r1 = 64512
    //     0xd8dd50: orr             x1, xzr, #0xfc00
    // 0xd8dd54: r3 = LoadInt32Instr(r0)
    //     0xd8dd54: sbfx            x3, x0, #1, #0x1f
    // 0xd8dd58: stur            x3, [fp, #-0x18]
    // 0xd8dd5c: mov             x0, x3
    // 0xd8dd60: ubfx            x0, x0, #0, #0x20
    // 0xd8dd64: and             x2, x0, x1
    // 0xd8dd68: mov             x0, x2
    // 0xd8dd6c: ubfx            x0, x0, #0, #0x20
    // 0xd8dd70: r17 = 55296
    //     0xd8dd70: movz            x17, #0xd800
    // 0xd8dd74: cmp             x0, x17
    // 0xd8dd78: b.ne            #0xd8ddb8
    // 0xd8dd7c: ldur            x0, [fp, #-0x10]
    // 0xd8dd80: add             x2, x0, #1
    // 0xd8dd84: ldur            x1, [fp, #-8]
    // 0xd8dd88: r0 = codeUnitAt()
    //     0xd8dd88: bl              #0x75c4d8  ; [package:flutter/src/painting/inline_span.dart] InlineSpan::codeUnitAt
    // 0xd8dd8c: cmp             w0, NULL
    // 0xd8dd90: b.eq            #0xd8de30
    // 0xd8dd94: ldur            x3, [fp, #-0x18]
    // 0xd8dd98: lsl             x1, x3, #0xa
    // 0xd8dd9c: r2 = LoadInt32Instr(r0)
    //     0xd8dd9c: sbfx            x2, x0, #1, #0x1f
    // 0xd8dda0: add             x0, x1, x2
    // 0xd8dda4: r17 = -56557569
    //     0xd8dda4: movn            x17, #0x35f, lsl #16
    // 0xd8dda8: movk            x17, #0x2400
    // 0xd8ddac: add             x1, x0, x17
    // 0xd8ddb0: mov             x2, x1
    // 0xd8ddb4: b               #0xd8de08
    // 0xd8ddb8: ldur            x0, [fp, #-0x10]
    // 0xd8ddbc: ubfx            x2, x2, #0, #0x20
    // 0xd8ddc0: r17 = 56320
    //     0xd8ddc0: movz            x17, #0xdc00
    // 0xd8ddc4: cmp             x2, x17
    // 0xd8ddc8: b.ne            #0xd8de04
    // 0xd8ddcc: sub             x2, x0, #1
    // 0xd8ddd0: ldur            x1, [fp, #-8]
    // 0xd8ddd4: r0 = codeUnitAt()
    //     0xd8ddd4: bl              #0x75c4d8  ; [package:flutter/src/painting/inline_span.dart] InlineSpan::codeUnitAt
    // 0xd8ddd8: cmp             w0, NULL
    // 0xd8dddc: b.eq            #0xd8de34
    // 0xd8dde0: r2 = LoadInt32Instr(r0)
    //     0xd8dde0: sbfx            x2, x0, #1, #0x1f
    // 0xd8dde4: lsl             x3, x2, #0xa
    // 0xd8dde8: ldur            x2, [fp, #-0x18]
    // 0xd8ddec: add             x4, x3, x2
    // 0xd8ddf0: r17 = -56557569
    //     0xd8ddf0: movn            x17, #0x35f, lsl #16
    // 0xd8ddf4: movk            x17, #0x2400
    // 0xd8ddf8: add             x3, x4, x17
    // 0xd8ddfc: mov             x2, x3
    // 0xd8de00: b               #0xd8de08
    // 0xd8de04: mov             x2, x3
    // 0xd8de08: r0 = BoxInt64Instr(r2)
    //     0xd8de08: sbfiz           x0, x2, #1, #0x1f
    //     0xd8de0c: cmp             x2, x0, asr #1
    //     0xd8de10: b.eq            #0xd8de1c
    //     0xd8de14: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd8de18: stur            x2, [x0, #7]
    // 0xd8de1c: LeaveFrame
    //     0xd8de1c: mov             SP, fp
    //     0xd8de20: ldp             fp, lr, [SP], #0x10
    // 0xd8de24: ret
    //     0xd8de24: ret             
    // 0xd8de28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8de28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8de2c: b               #0xd8dd20
    // 0xd8de30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd8de30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd8de34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd8de34: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static RegExp _regExpSpaceSeparatorOrPunctuaion() {
    // ** addr: 0xd8de38, size: 0x58
    // 0xd8de38: EnterFrame
    //     0xd8de38: stp             fp, lr, [SP, #-0x10]!
    //     0xd8de3c: mov             fp, SP
    // 0xd8de40: AllocStack(0x30)
    //     0xd8de40: sub             SP, SP, #0x30
    // 0xd8de44: CheckStackOverflow
    //     0xd8de44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8de48: cmp             SP, x16
    //     0xd8de4c: b.ls            #0xd8de88
    // 0xd8de50: r16 = "[\\p{Space_Separator}\\p{Punctuation}]"
    //     0xd8de50: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b450] "[\\p{Space_Separator}\\p{Punctuation}]"
    //     0xd8de54: ldr             x16, [x16, #0x450]
    // 0xd8de58: stp             x16, NULL, [SP, #0x20]
    // 0xd8de5c: r16 = false
    //     0xd8de5c: add             x16, NULL, #0x30  ; false
    // 0xd8de60: r30 = true
    //     0xd8de60: add             lr, NULL, #0x20  ; true
    // 0xd8de64: stp             lr, x16, [SP, #0x10]
    // 0xd8de68: r16 = true
    //     0xd8de68: add             x16, NULL, #0x20  ; true
    // 0xd8de6c: r30 = false
    //     0xd8de6c: add             lr, NULL, #0x30  ; false
    // 0xd8de70: stp             lr, x16, [SP]
    // 0xd8de74: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xd8de74: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xd8de78: r0 = _RegExp()
    //     0xd8de78: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xd8de7c: LeaveFrame
    //     0xd8de7c: mov             SP, fp
    //     0xd8de80: ldp             fp, lr, [SP], #0x10
    // 0xd8de84: ret
    //     0xd8de84: ret             
    // 0xd8de88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8de88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8de8c: b               #0xd8de50
  }
  _ getTextBoundaryAt(/* No info */) {
    // ** addr: 0xd98000, size: 0xc8
    // 0xd98000: EnterFrame
    //     0xd98000: stp             fp, lr, [SP, #-0x10]!
    //     0xd98004: mov             fp, SP
    // 0xd98008: AllocStack(0x18)
    //     0xd98008: sub             SP, SP, #0x18
    // 0xd9800c: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xd9800c: stur            x2, [fp, #-0x10]
    // 0xd98010: CheckStackOverflow
    //     0xd98010: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd98014: cmp             SP, x16
    //     0xd98018: b.ls            #0xd980c0
    // 0xd9801c: LoadField: r3 = r1->field_b
    //     0xd9801c: ldur            w3, [x1, #0xb]
    // 0xd98020: DecompressPointer r3
    //     0xd98020: add             x3, x3, HEAP, lsl #32
    // 0xd98024: stur            x3, [fp, #-8]
    // 0xd98028: cmp             x2, #0
    // 0xd9802c: b.le            #0xd98038
    // 0xd98030: mov             x0, x2
    // 0xd98034: b               #0xd9808c
    // 0xd98038: tbz             x2, #0x3f, #0xd98044
    // 0xd9803c: r0 = 0
    //     0xd9803c: movz            x0, #0
    // 0xd98040: b               #0xd9808c
    // 0xd98044: r0 = BoxInt64Instr(r2)
    //     0xd98044: sbfiz           x0, x2, #1, #0x1f
    //     0xd98048: cmp             x2, x0, asr #1
    //     0xd9804c: b.eq            #0xd98058
    //     0xd98050: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98054: stur            x2, [x0, #7]
    // 0xd98058: r1 = 60
    //     0xd98058: movz            x1, #0x3c
    // 0xd9805c: branchIfSmi(r0, 0xd98068)
    //     0xd9805c: tbz             w0, #0, #0xd98068
    // 0xd98060: r1 = LoadClassIdInstr(r0)
    //     0xd98060: ldur            x1, [x0, #-1]
    //     0xd98064: ubfx            x1, x1, #0xc, #0x14
    // 0xd98068: str             x0, [SP]
    // 0xd9806c: mov             x0, x1
    // 0xd98070: r0 = GDT[cid_x0 + -0xfb8]()
    //     0xd98070: sub             lr, x0, #0xfb8
    //     0xd98074: ldr             lr, [x21, lr, lsl #3]
    //     0xd98078: blr             lr
    // 0xd9807c: tbnz            w0, #4, #0xd98088
    // 0xd98080: r0 = 0
    //     0xd98080: movz            x0, #0
    // 0xd98084: b               #0xd9808c
    // 0xd98088: ldur            x0, [fp, #-0x10]
    // 0xd9808c: stur            x0, [fp, #-0x10]
    // 0xd98090: r0 = TextPosition()
    //     0xd98090: bl              #0x683b2c  ; AllocateTextPositionStub -> TextPosition (size=0x14)
    // 0xd98094: mov             x1, x0
    // 0xd98098: ldur            x0, [fp, #-0x10]
    // 0xd9809c: StoreField: r1->field_7 = r0
    //     0xd9809c: stur            x0, [x1, #7]
    // 0xd980a0: r0 = Instance_TextAffinity
    //     0xd980a0: ldr             x0, [PP, #0x4878]  ; [pp+0x4878] Obj!TextAffinity@e392a1
    // 0xd980a4: StoreField: r1->field_f = r0
    //     0xd980a4: stur            w0, [x1, #0xf]
    // 0xd980a8: mov             x2, x1
    // 0xd980ac: ldur            x1, [fp, #-8]
    // 0xd980b0: r0 = getWordBoundary()
    //     0xd980b0: bl              #0x75c5a0  ; [dart:ui] _NativeParagraph::getWordBoundary
    // 0xd980b4: LeaveFrame
    //     0xd980b4: mov             SP, fp
    //     0xd980b8: ldp             fp, lr, [SP], #0x10
    // 0xd980bc: ret
    //     0xd980bc: ret             
    // 0xd980c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd980c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd980c4: b               #0xd9801c
  }
  dynamic _skipSpacesAndPunctuations(dynamic) {
    // ** addr: 0xd980c8, size: 0x24
    // 0xd980c8: EnterFrame
    //     0xd980c8: stp             fp, lr, [SP, #-0x10]!
    //     0xd980cc: mov             fp, SP
    // 0xd980d0: ldr             x2, [fp, #0x10]
    // 0xd980d4: r1 = Function '_skipSpacesAndPunctuations@663105366':.
    //     0xd980d4: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5b458] AnonymousClosure: (0xd8db0c), in [package:flutter/src/painting/text_painter.dart] WordBoundary::_skipSpacesAndPunctuations (0xd8db4c)
    //     0xd980d8: ldr             x1, [x1, #0x458]
    // 0xd980dc: r0 = AllocateClosure()
    //     0xd980dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xd980e0: LeaveFrame
    //     0xd980e0: mov             SP, fp
    //     0xd980e4: ldp             fp, lr, [SP], #0x10
    // 0xd980e8: ret
    //     0xd980e8: ret             
  }
}

// class id: 3230, size: 0x18, field offset: 0x8
//   const constructor, 
class PlaceholderDimensions extends Object {

  Size field_8;
  PlaceholderAlignment field_c;

  get _ hashCode(/* No info */) {
    // ** addr: 0xbec804, size: 0x78
    // 0xbec804: EnterFrame
    //     0xbec804: stp             fp, lr, [SP, #-0x10]!
    //     0xbec808: mov             fp, SP
    // 0xbec80c: AllocStack(0x10)
    //     0xbec80c: sub             SP, SP, #0x10
    // 0xbec810: CheckStackOverflow
    //     0xbec810: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbec814: cmp             SP, x16
    //     0xbec818: b.ls            #0xbec874
    // 0xbec81c: ldr             x0, [fp, #0x10]
    // 0xbec820: LoadField: r1 = r0->field_7
    //     0xbec820: ldur            w1, [x0, #7]
    // 0xbec824: DecompressPointer r1
    //     0xbec824: add             x1, x1, HEAP, lsl #32
    // 0xbec828: LoadField: r2 = r0->field_b
    //     0xbec828: ldur            w2, [x0, #0xb]
    // 0xbec82c: DecompressPointer r2
    //     0xbec82c: add             x2, x2, HEAP, lsl #32
    // 0xbec830: LoadField: r3 = r0->field_13
    //     0xbec830: ldur            w3, [x0, #0x13]
    // 0xbec834: DecompressPointer r3
    //     0xbec834: add             x3, x3, HEAP, lsl #32
    // 0xbec838: LoadField: r4 = r0->field_f
    //     0xbec838: ldur            w4, [x0, #0xf]
    // 0xbec83c: DecompressPointer r4
    //     0xbec83c: add             x4, x4, HEAP, lsl #32
    // 0xbec840: stp             x4, x3, [SP]
    // 0xbec844: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0xbec844: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0xbec848: ldr             x4, [x4, #0xe00]
    // 0xbec84c: r0 = hash()
    //     0xbec84c: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbec850: mov             x2, x0
    // 0xbec854: r0 = BoxInt64Instr(r2)
    //     0xbec854: sbfiz           x0, x2, #1, #0x1f
    //     0xbec858: cmp             x2, x0, asr #1
    //     0xbec85c: b.eq            #0xbec868
    //     0xbec860: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbec864: stur            x2, [x0, #7]
    // 0xbec868: LeaveFrame
    //     0xbec868: mov             SP, fp
    //     0xbec86c: ldp             fp, lr, [SP], #0x10
    // 0xbec870: ret
    //     0xbec870: ret             
    // 0xbec874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbec874: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbec878: b               #0xbec81c
  }
  _ ==(/* No info */) {
    // ** addr: 0xd61e20, size: 0x110
    // 0xd61e20: EnterFrame
    //     0xd61e20: stp             fp, lr, [SP, #-0x10]!
    //     0xd61e24: mov             fp, SP
    // 0xd61e28: AllocStack(0x10)
    //     0xd61e28: sub             SP, SP, #0x10
    // 0xd61e2c: CheckStackOverflow
    //     0xd61e2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd61e30: cmp             SP, x16
    //     0xd61e34: b.ls            #0xd61f28
    // 0xd61e38: ldr             x0, [fp, #0x10]
    // 0xd61e3c: cmp             w0, NULL
    // 0xd61e40: b.ne            #0xd61e54
    // 0xd61e44: r0 = false
    //     0xd61e44: add             x0, NULL, #0x30  ; false
    // 0xd61e48: LeaveFrame
    //     0xd61e48: mov             SP, fp
    //     0xd61e4c: ldp             fp, lr, [SP], #0x10
    // 0xd61e50: ret
    //     0xd61e50: ret             
    // 0xd61e54: ldr             x1, [fp, #0x18]
    // 0xd61e58: cmp             w1, w0
    // 0xd61e5c: b.ne            #0xd61e70
    // 0xd61e60: r0 = true
    //     0xd61e60: add             x0, NULL, #0x20  ; true
    // 0xd61e64: LeaveFrame
    //     0xd61e64: mov             SP, fp
    //     0xd61e68: ldp             fp, lr, [SP], #0x10
    // 0xd61e6c: ret
    //     0xd61e6c: ret             
    // 0xd61e70: r2 = 60
    //     0xd61e70: movz            x2, #0x3c
    // 0xd61e74: branchIfSmi(r0, 0xd61e80)
    //     0xd61e74: tbz             w0, #0, #0xd61e80
    // 0xd61e78: r2 = LoadClassIdInstr(r0)
    //     0xd61e78: ldur            x2, [x0, #-1]
    //     0xd61e7c: ubfx            x2, x2, #0xc, #0x14
    // 0xd61e80: cmp             x2, #0xc9e
    // 0xd61e84: b.ne            #0xd61f18
    // 0xd61e88: LoadField: r2 = r0->field_7
    //     0xd61e88: ldur            w2, [x0, #7]
    // 0xd61e8c: DecompressPointer r2
    //     0xd61e8c: add             x2, x2, HEAP, lsl #32
    // 0xd61e90: LoadField: r3 = r1->field_7
    //     0xd61e90: ldur            w3, [x1, #7]
    // 0xd61e94: DecompressPointer r3
    //     0xd61e94: add             x3, x3, HEAP, lsl #32
    // 0xd61e98: LoadField: d0 = r3->field_7
    //     0xd61e98: ldur            d0, [x3, #7]
    // 0xd61e9c: LoadField: d1 = r2->field_7
    //     0xd61e9c: ldur            d1, [x2, #7]
    // 0xd61ea0: fcmp            d0, d1
    // 0xd61ea4: b.ne            #0xd61f18
    // 0xd61ea8: LoadField: d0 = r3->field_f
    //     0xd61ea8: ldur            d0, [x3, #0xf]
    // 0xd61eac: LoadField: d1 = r2->field_f
    //     0xd61eac: ldur            d1, [x2, #0xf]
    // 0xd61eb0: fcmp            d0, d1
    // 0xd61eb4: b.ne            #0xd61f18
    // 0xd61eb8: LoadField: r2 = r0->field_b
    //     0xd61eb8: ldur            w2, [x0, #0xb]
    // 0xd61ebc: DecompressPointer r2
    //     0xd61ebc: add             x2, x2, HEAP, lsl #32
    // 0xd61ec0: LoadField: r3 = r1->field_b
    //     0xd61ec0: ldur            w3, [x1, #0xb]
    // 0xd61ec4: DecompressPointer r3
    //     0xd61ec4: add             x3, x3, HEAP, lsl #32
    // 0xd61ec8: cmp             w2, w3
    // 0xd61ecc: b.ne            #0xd61f18
    // 0xd61ed0: LoadField: r2 = r0->field_13
    //     0xd61ed0: ldur            w2, [x0, #0x13]
    // 0xd61ed4: DecompressPointer r2
    //     0xd61ed4: add             x2, x2, HEAP, lsl #32
    // 0xd61ed8: LoadField: r3 = r1->field_13
    //     0xd61ed8: ldur            w3, [x1, #0x13]
    // 0xd61edc: DecompressPointer r3
    //     0xd61edc: add             x3, x3, HEAP, lsl #32
    // 0xd61ee0: cmp             w2, w3
    // 0xd61ee4: b.ne            #0xd61f18
    // 0xd61ee8: LoadField: r2 = r0->field_f
    //     0xd61ee8: ldur            w2, [x0, #0xf]
    // 0xd61eec: DecompressPointer r2
    //     0xd61eec: add             x2, x2, HEAP, lsl #32
    // 0xd61ef0: LoadField: r0 = r1->field_f
    //     0xd61ef0: ldur            w0, [x1, #0xf]
    // 0xd61ef4: DecompressPointer r0
    //     0xd61ef4: add             x0, x0, HEAP, lsl #32
    // 0xd61ef8: r1 = LoadClassIdInstr(r2)
    //     0xd61ef8: ldur            x1, [x2, #-1]
    //     0xd61efc: ubfx            x1, x1, #0xc, #0x14
    // 0xd61f00: stp             x0, x2, [SP]
    // 0xd61f04: mov             x0, x1
    // 0xd61f08: mov             lr, x0
    // 0xd61f0c: ldr             lr, [x21, lr, lsl #3]
    // 0xd61f10: blr             lr
    // 0xd61f14: b               #0xd61f1c
    // 0xd61f18: r0 = false
    //     0xd61f18: add             x0, NULL, #0x30  ; false
    // 0xd61f1c: LeaveFrame
    //     0xd61f1c: mov             SP, fp
    //     0xd61f20: ldp             fp, lr, [SP], #0x10
    // 0xd61f24: ret
    //     0xd61f24: ret             
    // 0xd61f28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd61f28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd61f2c: b               #0xd61e38
  }
}

// class id: 7022, size: 0x14, field offset: 0x14
enum TextWidthBasis extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc49620, size: 0x64
    // 0xc49620: EnterFrame
    //     0xc49620: stp             fp, lr, [SP, #-0x10]!
    //     0xc49624: mov             fp, SP
    // 0xc49628: AllocStack(0x10)
    //     0xc49628: sub             SP, SP, #0x10
    // 0xc4962c: SetupParameters(TextWidthBasis this /* r1 => r0, fp-0x8 */)
    //     0xc4962c: mov             x0, x1
    //     0xc49630: stur            x1, [fp, #-8]
    // 0xc49634: CheckStackOverflow
    //     0xc49634: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc49638: cmp             SP, x16
    //     0xc4963c: b.ls            #0xc4967c
    // 0xc49640: r1 = Null
    //     0xc49640: mov             x1, NULL
    // 0xc49644: r2 = 4
    //     0xc49644: movz            x2, #0x4
    // 0xc49648: r0 = AllocateArray()
    //     0xc49648: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4964c: r16 = "TextWidthBasis."
    //     0xc4964c: add             x16, PP, #0x43, lsl #12  ; [pp+0x43878] "TextWidthBasis."
    //     0xc49650: ldr             x16, [x16, #0x878]
    // 0xc49654: StoreField: r0->field_f = r16
    //     0xc49654: stur            w16, [x0, #0xf]
    // 0xc49658: ldur            x1, [fp, #-8]
    // 0xc4965c: LoadField: r2 = r1->field_f
    //     0xc4965c: ldur            w2, [x1, #0xf]
    // 0xc49660: DecompressPointer r2
    //     0xc49660: add             x2, x2, HEAP, lsl #32
    // 0xc49664: StoreField: r0->field_13 = r2
    //     0xc49664: stur            w2, [x0, #0x13]
    // 0xc49668: str             x0, [SP]
    // 0xc4966c: r0 = _interpolate()
    //     0xc4966c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc49670: LeaveFrame
    //     0xc49670: mov             SP, fp
    //     0xc49674: ldp             fp, lr, [SP], #0x10
    // 0xc49678: ret
    //     0xc49678: ret             
    // 0xc4967c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4967c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc49680: b               #0xc49640
  }
}

// class id: 7023, size: 0x14, field offset: 0x14
enum TextOverflow extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
