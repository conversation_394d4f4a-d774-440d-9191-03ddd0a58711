// lib: , url: package:flutter/src/rendering/flex.dart

// class id: 1049025, size: 0x8
class :: {

  static _ _AxisSize.applyConstraints(/* No info */) {
    // ** addr: 0x7322f0, size: 0x5c
    // 0x7322f0: EnterFrame
    //     0x7322f0: stp             fp, lr, [SP, #-0x10]!
    //     0x7322f4: mov             fp, SP
    // 0x7322f8: AllocStack(0x8)
    //     0x7322f8: sub             SP, SP, #8
    // 0x7322fc: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x7322fc: mov             x0, x1
    //     0x732300: stur            x1, [fp, #-8]
    // 0x732304: CheckStackOverflow
    //     0x732304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x732308: cmp             SP, x16
    //     0x73230c: b.ls            #0x732344
    // 0x732310: LoadField: r1 = r3->field_7
    //     0x732310: ldur            x1, [x3, #7]
    // 0x732314: cmp             x1, #0
    // 0x732318: b.gt            #0x732324
    // 0x73231c: mov             x1, x2
    // 0x732320: b               #0x732330
    // 0x732324: mov             x1, x2
    // 0x732328: r0 = flipped()
    //     0x732328: bl              #0x73234c  ; [package:flutter/src/rendering/box.dart] BoxConstraints::flipped
    // 0x73232c: mov             x1, x0
    // 0x732330: ldur            x2, [fp, #-8]
    // 0x732334: r0 = constrain()
    //     0x732334: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x732338: LeaveFrame
    //     0x732338: mov             SP, fp
    //     0x73233c: ldp             fp, lr, [SP], #0x10
    // 0x732340: ret
    //     0x732340: ret             
    // 0x732344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x732344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x732348: b               #0x732310
  }
  static _ _AxisSize.+(/* No info */) {
    // ** addr: 0x7323a8, size: 0x84
    // 0x7323a8: EnterFrame
    //     0x7323a8: stp             fp, lr, [SP, #-0x10]!
    //     0x7323ac: mov             fp, SP
    // 0x7323b0: AllocStack(0x10)
    //     0x7323b0: sub             SP, SP, #0x10
    // 0x7323b4: LoadField: d0 = r1->field_7
    //     0x7323b4: ldur            d0, [x1, #7]
    // 0x7323b8: LoadField: d1 = r2->field_7
    //     0x7323b8: ldur            d1, [x2, #7]
    // 0x7323bc: fadd            d2, d0, d1
    // 0x7323c0: stur            d2, [fp, #-0x10]
    // 0x7323c4: LoadField: d0 = r1->field_f
    //     0x7323c4: ldur            d0, [x1, #0xf]
    // 0x7323c8: LoadField: d1 = r2->field_f
    //     0x7323c8: ldur            d1, [x2, #0xf]
    // 0x7323cc: fcmp            d0, d1
    // 0x7323d0: b.gt            #0x732408
    // 0x7323d4: fcmp            d1, d0
    // 0x7323d8: b.le            #0x7323e4
    // 0x7323dc: mov             v0.16b, v1.16b
    // 0x7323e0: b               #0x732408
    // 0x7323e4: d3 = 0.000000
    //     0x7323e4: eor             v3.16b, v3.16b, v3.16b
    // 0x7323e8: fcmp            d0, d3
    // 0x7323ec: b.ne            #0x7323fc
    // 0x7323f0: fadd            d3, d0, d1
    // 0x7323f4: mov             v0.16b, v3.16b
    // 0x7323f8: b               #0x732408
    // 0x7323fc: fcmp            d1, d1
    // 0x732400: b.vc            #0x732408
    // 0x732404: mov             v0.16b, v1.16b
    // 0x732408: stur            d0, [fp, #-8]
    // 0x73240c: r0 = Size()
    //     0x73240c: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x732410: ldur            d0, [fp, #-0x10]
    // 0x732414: StoreField: r0->field_7 = d0
    //     0x732414: stur            d0, [x0, #7]
    // 0x732418: ldur            d0, [fp, #-8]
    // 0x73241c: StoreField: r0->field_f = d0
    //     0x73241c: stur            d0, [x0, #0xf]
    // 0x732420: LeaveFrame
    //     0x732420: mov             SP, fp
    //     0x732424: ldp             fp, lr, [SP], #0x10
    // 0x732428: ret
    //     0x732428: ret             
  }
  static _ _AxisSize.(/* No info */) {
    // ** addr: 0x73242c, size: 0x34
    // 0x73242c: EnterFrame
    //     0x73242c: stp             fp, lr, [SP, #-0x10]!
    //     0x732430: mov             fp, SP
    // 0x732434: AllocStack(0x10)
    //     0x732434: sub             SP, SP, #0x10
    // 0x732438: SetupParameters(dynamic _ /* d0 => d0, fp-0x8 */, dynamic _ /* d1 => d1, fp-0x10 */)
    //     0x732438: stur            d0, [fp, #-8]
    //     0x73243c: stur            d1, [fp, #-0x10]
    // 0x732440: r0 = Size()
    //     0x732440: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x732444: ldur            d0, [fp, #-0x10]
    // 0x732448: StoreField: r0->field_7 = d0
    //     0x732448: stur            d0, [x0, #7]
    // 0x73244c: ldur            d0, [fp, #-8]
    // 0x732450: StoreField: r0->field_f = d0
    //     0x732450: stur            d0, [x0, #0xf]
    // 0x732454: LeaveFrame
    //     0x732454: mov             SP, fp
    //     0x732458: ldp             fp, lr, [SP], #0x10
    // 0x73245c: ret
    //     0x73245c: ret             
  }
  static _ _AscentDescent.+(/* No info */) {
    // ** addr: 0x732640, size: 0x3e8
    // 0x732640: EnterFrame
    //     0x732640: stp             fp, lr, [SP, #-0x10]!
    //     0x732644: mov             fp, SP
    // 0x732648: AllocStack(0x30)
    //     0x732648: sub             SP, SP, #0x30
    // 0x73264c: CheckStackOverflow
    //     0x73264c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x732650: cmp             SP, x16
    //     0x732654: b.ls            #0x732a00
    // 0x732658: cmp             w1, NULL
    // 0x73265c: b.ne            #0x7326c8
    // 0x732660: cmp             w2, NULL
    // 0x732664: b.eq            #0x7326b4
    // 0x732668: LoadField: r0 = r2->field_b
    //     0x732668: ldur            w0, [x2, #0xb]
    // 0x73266c: cmp             w0, #4
    // 0x732670: b.ne            #0x7326bc
    // 0x732674: LoadField: r0 = r2->field_f
    //     0x732674: ldur            w0, [x2, #0xf]
    // 0x732678: DecompressPointer r0
    //     0x732678: add             x0, x0, HEAP, lsl #32
    // 0x73267c: r3 = 60
    //     0x73267c: movz            x3, #0x3c
    // 0x732680: branchIfSmi(r0, 0x73268c)
    //     0x732680: tbz             w0, #0, #0x73268c
    // 0x732684: r3 = LoadClassIdInstr(r0)
    //     0x732684: ldur            x3, [x0, #-1]
    //     0x732688: ubfx            x3, x3, #0xc, #0x14
    // 0x73268c: cmp             x3, #0x3e
    // 0x732690: b.ne            #0x7326bc
    // 0x732694: LoadField: r0 = r2->field_13
    //     0x732694: ldur            w0, [x2, #0x13]
    // 0x732698: DecompressPointer r0
    //     0x732698: add             x0, x0, HEAP, lsl #32
    // 0x73269c: r3 = 60
    //     0x73269c: movz            x3, #0x3c
    // 0x7326a0: branchIfSmi(r0, 0x7326ac)
    //     0x7326a0: tbz             w0, #0, #0x7326ac
    // 0x7326a4: r3 = LoadClassIdInstr(r0)
    //     0x7326a4: ldur            x3, [x0, #-1]
    //     0x7326a8: ubfx            x3, x3, #0xc, #0x14
    // 0x7326ac: cmp             x3, #0x3e
    // 0x7326b0: b.ne            #0x7326bc
    // 0x7326b4: mov             x0, x2
    // 0x7326b8: b               #0x7329f4
    // 0x7326bc: mov             x3, x2
    // 0x7326c0: r0 = true
    //     0x7326c0: add             x0, NULL, #0x20  ; true
    // 0x7326c4: b               #0x7326d0
    // 0x7326c8: r3 = Null
    //     0x7326c8: mov             x3, NULL
    // 0x7326cc: r0 = false
    //     0x7326cc: add             x0, NULL, #0x30  ; false
    // 0x7326d0: cmp             w1, NULL
    // 0x7326d4: b.eq            #0x732724
    // 0x7326d8: LoadField: r4 = r1->field_b
    //     0x7326d8: ldur            w4, [x1, #0xb]
    // 0x7326dc: cmp             w4, #4
    // 0x7326e0: b.ne            #0x732750
    // 0x7326e4: LoadField: r4 = r1->field_f
    //     0x7326e4: ldur            w4, [x1, #0xf]
    // 0x7326e8: DecompressPointer r4
    //     0x7326e8: add             x4, x4, HEAP, lsl #32
    // 0x7326ec: r5 = 60
    //     0x7326ec: movz            x5, #0x3c
    // 0x7326f0: branchIfSmi(r4, 0x7326fc)
    //     0x7326f0: tbz             w4, #0, #0x7326fc
    // 0x7326f4: r5 = LoadClassIdInstr(r4)
    //     0x7326f4: ldur            x5, [x4, #-1]
    //     0x7326f8: ubfx            x5, x5, #0xc, #0x14
    // 0x7326fc: cmp             x5, #0x3e
    // 0x732700: b.ne            #0x732750
    // 0x732704: LoadField: r4 = r1->field_13
    //     0x732704: ldur            w4, [x1, #0x13]
    // 0x732708: DecompressPointer r4
    //     0x732708: add             x4, x4, HEAP, lsl #32
    // 0x73270c: r5 = 60
    //     0x73270c: movz            x5, #0x3c
    // 0x732710: branchIfSmi(r4, 0x73271c)
    //     0x732710: tbz             w4, #0, #0x73271c
    // 0x732714: r5 = LoadClassIdInstr(r4)
    //     0x732714: ldur            x5, [x4, #-1]
    //     0x732718: ubfx            x5, x5, #0xc, #0x14
    // 0x73271c: cmp             x5, #0x3e
    // 0x732720: b.ne            #0x732750
    // 0x732724: tbnz            w0, #4, #0x732730
    // 0x732728: mov             x0, x3
    // 0x73272c: b               #0x732738
    // 0x732730: mov             x3, x2
    // 0x732734: mov             x0, x2
    // 0x732738: cmp             w3, NULL
    // 0x73273c: b.ne            #0x732748
    // 0x732740: mov             x0, x1
    // 0x732744: b               #0x7329f4
    // 0x732748: mov             x3, x0
    // 0x73274c: r0 = true
    //     0x73274c: add             x0, NULL, #0x20  ; true
    // 0x732750: r4 = LoadClassIdInstr(r1)
    //     0x732750: ldur            x4, [x1, #-1]
    //     0x732754: ubfx            x4, x4, #0xc, #0x14
    // 0x732758: lsl             x4, x4, #1
    // 0x73275c: cmp             w4, #0x86
    // 0x732760: b.ne            #0x7329f0
    // 0x732764: LoadField: r4 = r1->field_b
    //     0x732764: ldur            w4, [x1, #0xb]
    // 0x732768: cmp             w4, #4
    // 0x73276c: b.ne            #0x7329f0
    // 0x732770: LoadField: r4 = r1->field_f
    //     0x732770: ldur            w4, [x1, #0xf]
    // 0x732774: DecompressPointer r4
    //     0x732774: add             x4, x4, HEAP, lsl #32
    // 0x732778: stur            x4, [fp, #-0x20]
    // 0x73277c: r5 = 60
    //     0x73277c: movz            x5, #0x3c
    // 0x732780: branchIfSmi(r4, 0x73278c)
    //     0x732780: tbz             w4, #0, #0x73278c
    // 0x732784: r5 = LoadClassIdInstr(r4)
    //     0x732784: ldur            x5, [x4, #-1]
    //     0x732788: ubfx            x5, x5, #0xc, #0x14
    // 0x73278c: cmp             x5, #0x3e
    // 0x732790: b.ne            #0x7329f0
    // 0x732794: LoadField: r5 = r1->field_13
    //     0x732794: ldur            w5, [x1, #0x13]
    // 0x732798: DecompressPointer r5
    //     0x732798: add             x5, x5, HEAP, lsl #32
    // 0x73279c: stur            x5, [fp, #-0x18]
    // 0x7327a0: r1 = 60
    //     0x7327a0: movz            x1, #0x3c
    // 0x7327a4: branchIfSmi(r5, 0x7327b0)
    //     0x7327a4: tbz             w5, #0, #0x7327b0
    // 0x7327a8: r1 = LoadClassIdInstr(r5)
    //     0x7327a8: ldur            x1, [x5, #-1]
    //     0x7327ac: ubfx            x1, x1, #0xc, #0x14
    // 0x7327b0: cmp             x1, #0x3e
    // 0x7327b4: b.ne            #0x7329f0
    // 0x7327b8: tbnz            w0, #4, #0x7327c8
    // 0x7327bc: mov             x1, x3
    // 0x7327c0: mov             x0, x3
    // 0x7327c4: b               #0x7327d0
    // 0x7327c8: mov             x1, x2
    // 0x7327cc: mov             x0, x2
    // 0x7327d0: r2 = LoadClassIdInstr(r1)
    //     0x7327d0: ldur            x2, [x1, #-1]
    //     0x7327d4: ubfx            x2, x2, #0xc, #0x14
    // 0x7327d8: lsl             x2, x2, #1
    // 0x7327dc: cmp             w2, #0x86
    // 0x7327e0: b.ne            #0x7329f0
    // 0x7327e4: LoadField: r2 = r1->field_b
    //     0x7327e4: ldur            w2, [x1, #0xb]
    // 0x7327e8: cmp             w2, #4
    // 0x7327ec: b.ne            #0x7329f0
    // 0x7327f0: LoadField: r1 = r0->field_f
    //     0x7327f0: ldur            w1, [x0, #0xf]
    // 0x7327f4: DecompressPointer r1
    //     0x7327f4: add             x1, x1, HEAP, lsl #32
    // 0x7327f8: stur            x1, [fp, #-0x10]
    // 0x7327fc: r2 = 60
    //     0x7327fc: movz            x2, #0x3c
    // 0x732800: branchIfSmi(r1, 0x73280c)
    //     0x732800: tbz             w1, #0, #0x73280c
    // 0x732804: r2 = LoadClassIdInstr(r1)
    //     0x732804: ldur            x2, [x1, #-1]
    //     0x732808: ubfx            x2, x2, #0xc, #0x14
    // 0x73280c: cmp             x2, #0x3e
    // 0x732810: b.ne            #0x7329f0
    // 0x732814: LoadField: r2 = r0->field_13
    //     0x732814: ldur            w2, [x0, #0x13]
    // 0x732818: DecompressPointer r2
    //     0x732818: add             x2, x2, HEAP, lsl #32
    // 0x73281c: stur            x2, [fp, #-8]
    // 0x732820: r0 = 60
    //     0x732820: movz            x0, #0x3c
    // 0x732824: branchIfSmi(r2, 0x732830)
    //     0x732824: tbz             w2, #0, #0x732830
    // 0x732828: r0 = LoadClassIdInstr(r2)
    //     0x732828: ldur            x0, [x2, #-1]
    //     0x73282c: ubfx            x0, x0, #0xc, #0x14
    // 0x732830: cmp             x0, #0x3e
    // 0x732834: b.ne            #0x7329f0
    // 0x732838: r0 = 60
    //     0x732838: movz            x0, #0x3c
    // 0x73283c: branchIfSmi(r4, 0x732848)
    //     0x73283c: tbz             w4, #0, #0x732848
    // 0x732840: r0 = LoadClassIdInstr(r4)
    //     0x732840: ldur            x0, [x4, #-1]
    //     0x732844: ubfx            x0, x0, #0xc, #0x14
    // 0x732848: stp             x1, x4, [SP]
    // 0x73284c: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x73284c: sub             lr, x0, #0xfe3
    //     0x732850: ldr             lr, [x21, lr, lsl #3]
    //     0x732854: blr             lr
    // 0x732858: tbnz            w0, #4, #0x732868
    // 0x73285c: ldur            x2, [fp, #-0x20]
    // 0x732860: d0 = 0.000000
    //     0x732860: eor             v0.16b, v0.16b, v0.16b
    // 0x732864: b               #0x73290c
    // 0x732868: ldur            x1, [fp, #-0x20]
    // 0x73286c: r0 = 60
    //     0x73286c: movz            x0, #0x3c
    // 0x732870: branchIfSmi(r1, 0x73287c)
    //     0x732870: tbz             w1, #0, #0x73287c
    // 0x732874: r0 = LoadClassIdInstr(r1)
    //     0x732874: ldur            x0, [x1, #-1]
    //     0x732878: ubfx            x0, x0, #0xc, #0x14
    // 0x73287c: ldur            x16, [fp, #-0x10]
    // 0x732880: stp             x16, x1, [SP]
    // 0x732884: r0 = GDT[cid_x0 + -0xfd2]()
    //     0x732884: sub             lr, x0, #0xfd2
    //     0x732888: ldr             lr, [x21, lr, lsl #3]
    //     0x73288c: blr             lr
    // 0x732890: tbnz            w0, #4, #0x7328a0
    // 0x732894: ldur            x2, [fp, #-0x10]
    // 0x732898: d0 = 0.000000
    //     0x732898: eor             v0.16b, v0.16b, v0.16b
    // 0x73289c: b               #0x73290c
    // 0x7328a0: ldur            x0, [fp, #-0x20]
    // 0x7328a4: d0 = 0.000000
    //     0x7328a4: eor             v0.16b, v0.16b, v0.16b
    // 0x7328a8: LoadField: d1 = r0->field_7
    //     0x7328a8: ldur            d1, [x0, #7]
    // 0x7328ac: fcmp            d1, d0
    // 0x7328b0: b.ne            #0x7328f0
    // 0x7328b4: ldur            x1, [fp, #-0x10]
    // 0x7328b8: LoadField: d2 = r1->field_7
    //     0x7328b8: ldur            d2, [x1, #7]
    // 0x7328bc: fadd            d3, d1, d2
    // 0x7328c0: r0 = inline_Allocate_Double()
    //     0x7328c0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7328c4: add             x0, x0, #0x10
    //     0x7328c8: cmp             x1, x0
    //     0x7328cc: b.ls            #0x732a08
    //     0x7328d0: str             x0, [THR, #0x50]  ; THR::top
    //     0x7328d4: sub             x0, x0, #0xf
    //     0x7328d8: movz            x1, #0xe15c
    //     0x7328dc: movk            x1, #0x3, lsl #16
    //     0x7328e0: stur            x1, [x0, #-1]
    // 0x7328e4: StoreField: r0->field_7 = d3
    //     0x7328e4: stur            d3, [x0, #7]
    // 0x7328e8: mov             x2, x0
    // 0x7328ec: b               #0x73290c
    // 0x7328f0: ldur            x1, [fp, #-0x10]
    // 0x7328f4: LoadField: d1 = r1->field_7
    //     0x7328f4: ldur            d1, [x1, #7]
    // 0x7328f8: fcmp            d1, d1
    // 0x7328fc: b.vc            #0x732908
    // 0x732900: mov             x2, x1
    // 0x732904: b               #0x73290c
    // 0x732908: mov             x2, x0
    // 0x73290c: ldur            x1, [fp, #-0x18]
    // 0x732910: stur            x2, [fp, #-0x10]
    // 0x732914: r0 = 60
    //     0x732914: movz            x0, #0x3c
    // 0x732918: branchIfSmi(r1, 0x732924)
    //     0x732918: tbz             w1, #0, #0x732924
    // 0x73291c: r0 = LoadClassIdInstr(r1)
    //     0x73291c: ldur            x0, [x1, #-1]
    //     0x732920: ubfx            x0, x0, #0xc, #0x14
    // 0x732924: ldur            x16, [fp, #-8]
    // 0x732928: stp             x16, x1, [SP]
    // 0x73292c: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x73292c: sub             lr, x0, #0xfe3
    //     0x732930: ldr             lr, [x21, lr, lsl #3]
    //     0x732934: blr             lr
    // 0x732938: tbnz            w0, #4, #0x732944
    // 0x73293c: ldur            x3, [fp, #-0x18]
    // 0x732940: b               #0x7329e4
    // 0x732944: ldur            x1, [fp, #-0x18]
    // 0x732948: r0 = 60
    //     0x732948: movz            x0, #0x3c
    // 0x73294c: branchIfSmi(r1, 0x732958)
    //     0x73294c: tbz             w1, #0, #0x732958
    // 0x732950: r0 = LoadClassIdInstr(r1)
    //     0x732950: ldur            x0, [x1, #-1]
    //     0x732954: ubfx            x0, x0, #0xc, #0x14
    // 0x732958: ldur            x16, [fp, #-8]
    // 0x73295c: stp             x16, x1, [SP]
    // 0x732960: r0 = GDT[cid_x0 + -0xfd2]()
    //     0x732960: sub             lr, x0, #0xfd2
    //     0x732964: ldr             lr, [x21, lr, lsl #3]
    //     0x732968: blr             lr
    // 0x73296c: tbnz            w0, #4, #0x732978
    // 0x732970: ldur            x3, [fp, #-8]
    // 0x732974: b               #0x7329e4
    // 0x732978: ldur            x0, [fp, #-0x18]
    // 0x73297c: d0 = 0.000000
    //     0x73297c: eor             v0.16b, v0.16b, v0.16b
    // 0x732980: LoadField: d1 = r0->field_7
    //     0x732980: ldur            d1, [x0, #7]
    // 0x732984: fcmp            d1, d0
    // 0x732988: b.ne            #0x7329c8
    // 0x73298c: ldur            x1, [fp, #-8]
    // 0x732990: LoadField: d0 = r1->field_7
    //     0x732990: ldur            d0, [x1, #7]
    // 0x732994: fadd            d2, d1, d0
    // 0x732998: r0 = inline_Allocate_Double()
    //     0x732998: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x73299c: add             x0, x0, #0x10
    //     0x7329a0: cmp             x1, x0
    //     0x7329a4: b.ls            #0x732a18
    //     0x7329a8: str             x0, [THR, #0x50]  ; THR::top
    //     0x7329ac: sub             x0, x0, #0xf
    //     0x7329b0: movz            x1, #0xe15c
    //     0x7329b4: movk            x1, #0x3, lsl #16
    //     0x7329b8: stur            x1, [x0, #-1]
    // 0x7329bc: StoreField: r0->field_7 = d2
    //     0x7329bc: stur            d2, [x0, #7]
    // 0x7329c0: mov             x3, x0
    // 0x7329c4: b               #0x7329e4
    // 0x7329c8: ldur            x1, [fp, #-8]
    // 0x7329cc: LoadField: d0 = r1->field_7
    //     0x7329cc: ldur            d0, [x1, #7]
    // 0x7329d0: fcmp            d0, d0
    // 0x7329d4: b.vc            #0x7329e0
    // 0x7329d8: mov             x3, x1
    // 0x7329dc: b               #0x7329e4
    // 0x7329e0: mov             x3, x0
    // 0x7329e4: ldur            x2, [fp, #-0x10]
    // 0x7329e8: r0 = AllocateRecord2()
    //     0x7329e8: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x7329ec: b               #0x7329f4
    // 0x7329f0: r0 = Null
    //     0x7329f0: mov             x0, NULL
    // 0x7329f4: LeaveFrame
    //     0x7329f4: mov             SP, fp
    //     0x7329f8: ldp             fp, lr, [SP], #0x10
    // 0x7329fc: ret
    //     0x7329fc: ret             
    // 0x732a00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x732a00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x732a04: b               #0x732658
    // 0x732a08: stp             q0, q3, [SP, #-0x20]!
    // 0x732a0c: r0 = AllocateDouble()
    //     0x732a0c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x732a10: ldp             q0, q3, [SP], #0x20
    // 0x732a14: b               #0x7328e4
    // 0x732a18: SaveReg d2
    //     0x732a18: str             q2, [SP, #-0x10]!
    // 0x732a1c: r0 = AllocateDouble()
    //     0x732a1c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x732a20: RestoreReg d2
    //     0x732a20: ldr             q2, [SP], #0x10
    // 0x732a24: b               #0x7329bc
  }
  static _ _AxisSize._convert(/* No info */) {
    // ** addr: 0x732a28, size: 0x50
    // 0x732a28: EnterFrame
    //     0x732a28: stp             fp, lr, [SP, #-0x10]!
    //     0x732a2c: mov             fp, SP
    // 0x732a30: AllocStack(0x10)
    //     0x732a30: sub             SP, SP, #0x10
    // 0x732a34: LoadField: r0 = r2->field_7
    //     0x732a34: ldur            x0, [x2, #7]
    // 0x732a38: cmp             x0, #0
    // 0x732a3c: b.gt            #0x732a48
    // 0x732a40: mov             x0, x1
    // 0x732a44: b               #0x732a6c
    // 0x732a48: LoadField: d0 = r1->field_f
    //     0x732a48: ldur            d0, [x1, #0xf]
    // 0x732a4c: stur            d0, [fp, #-0x10]
    // 0x732a50: LoadField: d1 = r1->field_7
    //     0x732a50: ldur            d1, [x1, #7]
    // 0x732a54: stur            d1, [fp, #-8]
    // 0x732a58: r0 = Size()
    //     0x732a58: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x732a5c: ldur            d0, [fp, #-0x10]
    // 0x732a60: StoreField: r0->field_7 = d0
    //     0x732a60: stur            d0, [x0, #7]
    // 0x732a64: ldur            d0, [fp, #-8]
    // 0x732a68: StoreField: r0->field_f = d0
    //     0x732a68: stur            d0, [x0, #0xf]
    // 0x732a6c: LeaveFrame
    //     0x732a6c: mov             SP, fp
    //     0x732a70: ldp             fp, lr, [SP], #0x10
    // 0x732a74: ret
    //     0x732a74: ret             
  }
}

// class id: 3052, size: 0x68, field offset: 0x58
//   transformed mixin,
abstract class _RenderFlex&RenderBox&ContainerRenderObjectMixin extends RenderBox
     with ContainerRenderObjectMixin<X0 bound RenderObject, X1 bound ContainerParentDataMixin> {

  [closure] RenderBox? childAfter(dynamic, Object?) {
    // ** addr: 0x73172c, size: 0xa8
    // 0x73172c: EnterFrame
    //     0x73172c: stp             fp, lr, [SP, #-0x10]!
    //     0x731730: mov             fp, SP
    // 0x731734: AllocStack(0x8)
    //     0x731734: sub             SP, SP, #8
    // 0x731738: ldr             x0, [fp, #0x10]
    // 0x73173c: r2 = Null
    //     0x73173c: mov             x2, NULL
    // 0x731740: r1 = Null
    //     0x731740: mov             x1, NULL
    // 0x731744: r4 = 60
    //     0x731744: movz            x4, #0x3c
    // 0x731748: branchIfSmi(r0, 0x731754)
    //     0x731748: tbz             w0, #0, #0x731754
    // 0x73174c: r4 = LoadClassIdInstr(r0)
    //     0x73174c: ldur            x4, [x0, #-1]
    //     0x731750: ubfx            x4, x4, #0xc, #0x14
    // 0x731754: sub             x4, x4, #0xbba
    // 0x731758: cmp             x4, #0x9a
    // 0x73175c: b.ls            #0x731770
    // 0x731760: r8 = RenderBox
    //     0x731760: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x731764: r3 = Null
    //     0x731764: add             x3, PP, #0x45, lsl #12  ; [pp+0x45668] Null
    //     0x731768: ldr             x3, [x3, #0x668]
    // 0x73176c: r0 = RenderBox()
    //     0x73176c: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x731770: ldr             x0, [fp, #0x10]
    // 0x731774: LoadField: r3 = r0->field_7
    //     0x731774: ldur            w3, [x0, #7]
    // 0x731778: DecompressPointer r3
    //     0x731778: add             x3, x3, HEAP, lsl #32
    // 0x73177c: stur            x3, [fp, #-8]
    // 0x731780: cmp             w3, NULL
    // 0x731784: b.eq            #0x7317d0
    // 0x731788: mov             x0, x3
    // 0x73178c: r2 = Null
    //     0x73178c: mov             x2, NULL
    // 0x731790: r1 = Null
    //     0x731790: mov             x1, NULL
    // 0x731794: r4 = LoadClassIdInstr(r0)
    //     0x731794: ldur            x4, [x0, #-1]
    //     0x731798: ubfx            x4, x4, #0xc, #0x14
    // 0x73179c: cmp             x4, #0xc7e
    // 0x7317a0: b.eq            #0x7317b8
    // 0x7317a4: r8 = FlexParentData
    //     0x7317a4: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x7317a8: ldr             x8, [x8, #0x590]
    // 0x7317ac: r3 = Null
    //     0x7317ac: add             x3, PP, #0x45, lsl #12  ; [pp+0x45678] Null
    //     0x7317b0: ldr             x3, [x3, #0x678]
    // 0x7317b4: r0 = DefaultTypeTest()
    //     0x7317b4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7317b8: ldur            x1, [fp, #-8]
    // 0x7317bc: LoadField: r0 = r1->field_13
    //     0x7317bc: ldur            w0, [x1, #0x13]
    // 0x7317c0: DecompressPointer r0
    //     0x7317c0: add             x0, x0, HEAP, lsl #32
    // 0x7317c4: LeaveFrame
    //     0x7317c4: mov             SP, fp
    //     0x7317c8: ldp             fp, lr, [SP], #0x10
    // 0x7317cc: ret
    //     0x7317cc: ret             
    // 0x7317d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7317d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ attach(/* No info */) {
    // ** addr: 0x763874, size: 0xfc
    // 0x763874: EnterFrame
    //     0x763874: stp             fp, lr, [SP, #-0x10]!
    //     0x763878: mov             fp, SP
    // 0x76387c: AllocStack(0x18)
    //     0x76387c: sub             SP, SP, #0x18
    // 0x763880: SetupParameters(_RenderFlex&RenderBox&ContainerRenderObjectMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x763880: mov             x3, x1
    //     0x763884: mov             x0, x2
    //     0x763888: stur            x1, [fp, #-8]
    //     0x76388c: stur            x2, [fp, #-0x10]
    // 0x763890: CheckStackOverflow
    //     0x763890: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x763894: cmp             SP, x16
    //     0x763898: b.ls            #0x76395c
    // 0x76389c: mov             x1, x3
    // 0x7638a0: mov             x2, x0
    // 0x7638a4: r0 = attach()
    //     0x7638a4: bl              #0x765268  ; [package:flutter/src/rendering/object.dart] RenderObject::attach
    // 0x7638a8: ldur            x0, [fp, #-8]
    // 0x7638ac: LoadField: r1 = r0->field_5f
    //     0x7638ac: ldur            w1, [x0, #0x5f]
    // 0x7638b0: DecompressPointer r1
    //     0x7638b0: add             x1, x1, HEAP, lsl #32
    // 0x7638b4: mov             x3, x1
    // 0x7638b8: stur            x3, [fp, #-8]
    // 0x7638bc: CheckStackOverflow
    //     0x7638bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7638c0: cmp             SP, x16
    //     0x7638c4: b.ls            #0x763964
    // 0x7638c8: cmp             w3, NULL
    // 0x7638cc: b.eq            #0x76394c
    // 0x7638d0: r0 = LoadClassIdInstr(r3)
    //     0x7638d0: ldur            x0, [x3, #-1]
    //     0x7638d4: ubfx            x0, x0, #0xc, #0x14
    // 0x7638d8: mov             x1, x3
    // 0x7638dc: ldur            x2, [fp, #-0x10]
    // 0x7638e0: r0 = GDT[cid_x0 + 0x11974]()
    //     0x7638e0: movz            x17, #0x1974
    //     0x7638e4: movk            x17, #0x1, lsl #16
    //     0x7638e8: add             lr, x0, x17
    //     0x7638ec: ldr             lr, [x21, lr, lsl #3]
    //     0x7638f0: blr             lr
    // 0x7638f4: ldur            x0, [fp, #-8]
    // 0x7638f8: LoadField: r3 = r0->field_7
    //     0x7638f8: ldur            w3, [x0, #7]
    // 0x7638fc: DecompressPointer r3
    //     0x7638fc: add             x3, x3, HEAP, lsl #32
    // 0x763900: stur            x3, [fp, #-0x18]
    // 0x763904: cmp             w3, NULL
    // 0x763908: b.eq            #0x76396c
    // 0x76390c: mov             x0, x3
    // 0x763910: r2 = Null
    //     0x763910: mov             x2, NULL
    // 0x763914: r1 = Null
    //     0x763914: mov             x1, NULL
    // 0x763918: r4 = LoadClassIdInstr(r0)
    //     0x763918: ldur            x4, [x0, #-1]
    //     0x76391c: ubfx            x4, x4, #0xc, #0x14
    // 0x763920: cmp             x4, #0xc7e
    // 0x763924: b.eq            #0x76393c
    // 0x763928: r8 = FlexParentData
    //     0x763928: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x76392c: ldr             x8, [x8, #0x590]
    // 0x763930: r3 = Null
    //     0x763930: add             x3, PP, #0x45, lsl #12  ; [pp+0x45798] Null
    //     0x763934: ldr             x3, [x3, #0x798]
    // 0x763938: r0 = DefaultTypeTest()
    //     0x763938: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x76393c: ldur            x1, [fp, #-0x18]
    // 0x763940: LoadField: r3 = r1->field_13
    //     0x763940: ldur            w3, [x1, #0x13]
    // 0x763944: DecompressPointer r3
    //     0x763944: add             x3, x3, HEAP, lsl #32
    // 0x763948: b               #0x7638b8
    // 0x76394c: r0 = Null
    //     0x76394c: mov             x0, NULL
    // 0x763950: LeaveFrame
    //     0x763950: mov             SP, fp
    //     0x763954: ldp             fp, lr, [SP], #0x10
    // 0x763958: ret
    //     0x763958: ret             
    // 0x76395c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76395c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x763960: b               #0x76389c
    // 0x763964: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x763964: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x763968: b               #0x7638c8
    // 0x76396c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76396c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RenderBox? childBefore(dynamic, Object?) {
    // ** addr: 0x771608, size: 0xa8
    // 0x771608: EnterFrame
    //     0x771608: stp             fp, lr, [SP, #-0x10]!
    //     0x77160c: mov             fp, SP
    // 0x771610: AllocStack(0x8)
    //     0x771610: sub             SP, SP, #8
    // 0x771614: ldr             x0, [fp, #0x10]
    // 0x771618: r2 = Null
    //     0x771618: mov             x2, NULL
    // 0x77161c: r1 = Null
    //     0x77161c: mov             x1, NULL
    // 0x771620: r4 = 60
    //     0x771620: movz            x4, #0x3c
    // 0x771624: branchIfSmi(r0, 0x771630)
    //     0x771624: tbz             w0, #0, #0x771630
    // 0x771628: r4 = LoadClassIdInstr(r0)
    //     0x771628: ldur            x4, [x0, #-1]
    //     0x77162c: ubfx            x4, x4, #0xc, #0x14
    // 0x771630: sub             x4, x4, #0xbba
    // 0x771634: cmp             x4, #0x9a
    // 0x771638: b.ls            #0x77164c
    // 0x77163c: r8 = RenderBox
    //     0x77163c: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x771640: r3 = Null
    //     0x771640: add             x3, PP, #0x45, lsl #12  ; [pp+0x45688] Null
    //     0x771644: ldr             x3, [x3, #0x688]
    // 0x771648: r0 = RenderBox()
    //     0x771648: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x77164c: ldr             x0, [fp, #0x10]
    // 0x771650: LoadField: r3 = r0->field_7
    //     0x771650: ldur            w3, [x0, #7]
    // 0x771654: DecompressPointer r3
    //     0x771654: add             x3, x3, HEAP, lsl #32
    // 0x771658: stur            x3, [fp, #-8]
    // 0x77165c: cmp             w3, NULL
    // 0x771660: b.eq            #0x7716ac
    // 0x771664: mov             x0, x3
    // 0x771668: r2 = Null
    //     0x771668: mov             x2, NULL
    // 0x77166c: r1 = Null
    //     0x77166c: mov             x1, NULL
    // 0x771670: r4 = LoadClassIdInstr(r0)
    //     0x771670: ldur            x4, [x0, #-1]
    //     0x771674: ubfx            x4, x4, #0xc, #0x14
    // 0x771678: cmp             x4, #0xc7e
    // 0x77167c: b.eq            #0x771694
    // 0x771680: r8 = FlexParentData
    //     0x771680: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x771684: ldr             x8, [x8, #0x590]
    // 0x771688: r3 = Null
    //     0x771688: add             x3, PP, #0x45, lsl #12  ; [pp+0x45698] Null
    //     0x77168c: ldr             x3, [x3, #0x698]
    // 0x771690: r0 = DefaultTypeTest()
    //     0x771690: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x771694: ldur            x1, [fp, #-8]
    // 0x771698: LoadField: r0 = r1->field_f
    //     0x771698: ldur            w0, [x1, #0xf]
    // 0x77169c: DecompressPointer r0
    //     0x77169c: add             x0, x0, HEAP, lsl #32
    // 0x7716a0: LeaveFrame
    //     0x7716a0: mov             SP, fp
    //     0x7716a4: ldp             fp, lr, [SP], #0x10
    // 0x7716a8: ret
    //     0x7716a8: ret             
    // 0x7716ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7716ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitChildren(/* No info */) {
    // ** addr: 0x786cd4, size: 0xd8
    // 0x786cd4: EnterFrame
    //     0x786cd4: stp             fp, lr, [SP, #-0x10]!
    //     0x786cd8: mov             fp, SP
    // 0x786cdc: AllocStack(0x28)
    //     0x786cdc: sub             SP, SP, #0x28
    // 0x786ce0: SetupParameters(_RenderFlex&RenderBox&ContainerRenderObjectMixin this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x786ce0: mov             x0, x1
    //     0x786ce4: mov             x1, x2
    //     0x786ce8: stur            x2, [fp, #-0x10]
    // 0x786cec: CheckStackOverflow
    //     0x786cec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x786cf0: cmp             SP, x16
    //     0x786cf4: b.ls            #0x786d98
    // 0x786cf8: LoadField: r2 = r0->field_5f
    //     0x786cf8: ldur            w2, [x0, #0x5f]
    // 0x786cfc: DecompressPointer r2
    //     0x786cfc: add             x2, x2, HEAP, lsl #32
    // 0x786d00: stur            x2, [fp, #-8]
    // 0x786d04: CheckStackOverflow
    //     0x786d04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x786d08: cmp             SP, x16
    //     0x786d0c: b.ls            #0x786da0
    // 0x786d10: cmp             w2, NULL
    // 0x786d14: b.eq            #0x786d88
    // 0x786d18: stp             x2, x1, [SP]
    // 0x786d1c: mov             x0, x1
    // 0x786d20: ClosureCall
    //     0x786d20: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x786d24: ldur            x2, [x0, #0x1f]
    //     0x786d28: blr             x2
    // 0x786d2c: ldur            x0, [fp, #-8]
    // 0x786d30: LoadField: r3 = r0->field_7
    //     0x786d30: ldur            w3, [x0, #7]
    // 0x786d34: DecompressPointer r3
    //     0x786d34: add             x3, x3, HEAP, lsl #32
    // 0x786d38: stur            x3, [fp, #-0x18]
    // 0x786d3c: cmp             w3, NULL
    // 0x786d40: b.eq            #0x786da8
    // 0x786d44: mov             x0, x3
    // 0x786d48: r2 = Null
    //     0x786d48: mov             x2, NULL
    // 0x786d4c: r1 = Null
    //     0x786d4c: mov             x1, NULL
    // 0x786d50: r4 = LoadClassIdInstr(r0)
    //     0x786d50: ldur            x4, [x0, #-1]
    //     0x786d54: ubfx            x4, x4, #0xc, #0x14
    // 0x786d58: cmp             x4, #0xc7e
    // 0x786d5c: b.eq            #0x786d74
    // 0x786d60: r8 = FlexParentData
    //     0x786d60: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x786d64: ldr             x8, [x8, #0x590]
    // 0x786d68: r3 = Null
    //     0x786d68: add             x3, PP, #0x45, lsl #12  ; [pp+0x45768] Null
    //     0x786d6c: ldr             x3, [x3, #0x768]
    // 0x786d70: r0 = DefaultTypeTest()
    //     0x786d70: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x786d74: ldur            x1, [fp, #-0x18]
    // 0x786d78: LoadField: r2 = r1->field_13
    //     0x786d78: ldur            w2, [x1, #0x13]
    // 0x786d7c: DecompressPointer r2
    //     0x786d7c: add             x2, x2, HEAP, lsl #32
    // 0x786d80: ldur            x1, [fp, #-0x10]
    // 0x786d84: b               #0x786d00
    // 0x786d88: r0 = Null
    //     0x786d88: mov             x0, NULL
    // 0x786d8c: LeaveFrame
    //     0x786d8c: mov             SP, fp
    //     0x786d90: ldp             fp, lr, [SP], #0x10
    // 0x786d94: ret
    //     0x786d94: ret             
    // 0x786d98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x786d98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x786d9c: b               #0x786cf8
    // 0x786da0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x786da0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x786da4: b               #0x786d10
    // 0x786da8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x786da8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ insert(/* No info */) {
    // ** addr: 0x7b0ea4, size: 0xd0
    // 0x7b0ea4: EnterFrame
    //     0x7b0ea4: stp             fp, lr, [SP, #-0x10]!
    //     0x7b0ea8: mov             fp, SP
    // 0x7b0eac: AllocStack(0x18)
    //     0x7b0eac: sub             SP, SP, #0x18
    // 0x7b0eb0: SetupParameters(_RenderFlex&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7b0eb0: mov             x5, x1
    //     0x7b0eb4: mov             x4, x2
    //     0x7b0eb8: stur            x1, [fp, #-8]
    //     0x7b0ebc: stur            x2, [fp, #-0x10]
    //     0x7b0ec0: stur            x3, [fp, #-0x18]
    // 0x7b0ec4: CheckStackOverflow
    //     0x7b0ec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b0ec8: cmp             SP, x16
    //     0x7b0ecc: b.ls            #0x7b0f6c
    // 0x7b0ed0: mov             x0, x4
    // 0x7b0ed4: r2 = Null
    //     0x7b0ed4: mov             x2, NULL
    // 0x7b0ed8: r1 = Null
    //     0x7b0ed8: mov             x1, NULL
    // 0x7b0edc: r4 = 60
    //     0x7b0edc: movz            x4, #0x3c
    // 0x7b0ee0: branchIfSmi(r0, 0x7b0eec)
    //     0x7b0ee0: tbz             w0, #0, #0x7b0eec
    // 0x7b0ee4: r4 = LoadClassIdInstr(r0)
    //     0x7b0ee4: ldur            x4, [x0, #-1]
    //     0x7b0ee8: ubfx            x4, x4, #0xc, #0x14
    // 0x7b0eec: sub             x4, x4, #0xbba
    // 0x7b0ef0: cmp             x4, #0x9a
    // 0x7b0ef4: b.ls            #0x7b0f08
    // 0x7b0ef8: r8 = RenderBox
    //     0x7b0ef8: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7b0efc: r3 = Null
    //     0x7b0efc: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f0f0] Null
    //     0x7b0f00: ldr             x3, [x3, #0xf0]
    // 0x7b0f04: r0 = RenderBox()
    //     0x7b0f04: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7b0f08: ldur            x0, [fp, #-0x18]
    // 0x7b0f0c: r2 = Null
    //     0x7b0f0c: mov             x2, NULL
    // 0x7b0f10: r1 = Null
    //     0x7b0f10: mov             x1, NULL
    // 0x7b0f14: r4 = 60
    //     0x7b0f14: movz            x4, #0x3c
    // 0x7b0f18: branchIfSmi(r0, 0x7b0f24)
    //     0x7b0f18: tbz             w0, #0, #0x7b0f24
    // 0x7b0f1c: r4 = LoadClassIdInstr(r0)
    //     0x7b0f1c: ldur            x4, [x0, #-1]
    //     0x7b0f20: ubfx            x4, x4, #0xc, #0x14
    // 0x7b0f24: sub             x4, x4, #0xbba
    // 0x7b0f28: cmp             x4, #0x9a
    // 0x7b0f2c: b.ls            #0x7b0f40
    // 0x7b0f30: r8 = RenderBox?
    //     0x7b0f30: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x7b0f34: r3 = Null
    //     0x7b0f34: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f100] Null
    //     0x7b0f38: ldr             x3, [x3, #0x100]
    // 0x7b0f3c: r0 = RenderBox?()
    //     0x7b0f3c: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x7b0f40: ldur            x1, [fp, #-8]
    // 0x7b0f44: ldur            x2, [fp, #-0x10]
    // 0x7b0f48: r0 = adoptChild()
    //     0x7b0f48: bl              #0x8057b8  ; [package:flutter/src/rendering/object.dart] RenderObject::adoptChild
    // 0x7b0f4c: ldur            x1, [fp, #-8]
    // 0x7b0f50: ldur            x2, [fp, #-0x10]
    // 0x7b0f54: ldur            x3, [fp, #-0x18]
    // 0x7b0f58: r0 = _insertIntoChildList()
    //     0x7b0f58: bl              #0xda43b0  ; [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin::_insertIntoChildList
    // 0x7b0f5c: r0 = Null
    //     0x7b0f5c: mov             x0, NULL
    // 0x7b0f60: LeaveFrame
    //     0x7b0f60: mov             SP, fp
    //     0x7b0f64: ldp             fp, lr, [SP], #0x10
    // 0x7b0f68: ret
    //     0x7b0f68: ret             
    // 0x7b0f6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b0f6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b0f70: b               #0x7b0ed0
  }
  _ remove(/* No info */) {
    // ** addr: 0x7b2924, size: 0x90
    // 0x7b2924: EnterFrame
    //     0x7b2924: stp             fp, lr, [SP, #-0x10]!
    //     0x7b2928: mov             fp, SP
    // 0x7b292c: AllocStack(0x10)
    //     0x7b292c: sub             SP, SP, #0x10
    // 0x7b2930: SetupParameters(_RenderFlex&RenderBox&ContainerRenderObjectMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x7b2930: mov             x4, x1
    //     0x7b2934: mov             x3, x2
    //     0x7b2938: stur            x1, [fp, #-8]
    //     0x7b293c: stur            x2, [fp, #-0x10]
    // 0x7b2940: CheckStackOverflow
    //     0x7b2940: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b2944: cmp             SP, x16
    //     0x7b2948: b.ls            #0x7b29ac
    // 0x7b294c: mov             x0, x3
    // 0x7b2950: r2 = Null
    //     0x7b2950: mov             x2, NULL
    // 0x7b2954: r1 = Null
    //     0x7b2954: mov             x1, NULL
    // 0x7b2958: r4 = 60
    //     0x7b2958: movz            x4, #0x3c
    // 0x7b295c: branchIfSmi(r0, 0x7b2968)
    //     0x7b295c: tbz             w0, #0, #0x7b2968
    // 0x7b2960: r4 = LoadClassIdInstr(r0)
    //     0x7b2960: ldur            x4, [x0, #-1]
    //     0x7b2964: ubfx            x4, x4, #0xc, #0x14
    // 0x7b2968: sub             x4, x4, #0xbba
    // 0x7b296c: cmp             x4, #0x9a
    // 0x7b2970: b.ls            #0x7b2984
    // 0x7b2974: r8 = RenderBox
    //     0x7b2974: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7b2978: r3 = Null
    //     0x7b2978: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f0e0] Null
    //     0x7b297c: ldr             x3, [x3, #0xe0]
    // 0x7b2980: r0 = RenderBox()
    //     0x7b2980: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7b2984: ldur            x1, [fp, #-8]
    // 0x7b2988: ldur            x2, [fp, #-0x10]
    // 0x7b298c: r0 = _removeFromChildList()
    //     0x7b298c: bl              #0x7b29b4  ; [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin::_removeFromChildList
    // 0x7b2990: ldur            x1, [fp, #-8]
    // 0x7b2994: ldur            x2, [fp, #-0x10]
    // 0x7b2998: r0 = dropChild()
    //     0x7b2998: bl              #0x8019dc  ; [package:flutter/src/rendering/object.dart] RenderObject::dropChild
    // 0x7b299c: r0 = Null
    //     0x7b299c: mov             x0, NULL
    // 0x7b29a0: LeaveFrame
    //     0x7b29a0: mov             SP, fp
    //     0x7b29a4: ldp             fp, lr, [SP], #0x10
    // 0x7b29a8: ret
    //     0x7b29a8: ret             
    // 0x7b29ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b29ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b29b0: b               #0x7b294c
  }
  _ _removeFromChildList(/* No info */) {
    // ** addr: 0x7b29b4, size: 0x2c8
    // 0x7b29b4: EnterFrame
    //     0x7b29b4: stp             fp, lr, [SP, #-0x10]!
    //     0x7b29b8: mov             fp, SP
    // 0x7b29bc: AllocStack(0x28)
    //     0x7b29bc: sub             SP, SP, #0x28
    // 0x7b29c0: SetupParameters(_RenderFlex&RenderBox&ContainerRenderObjectMixin this /* r1 => r3, fp-0x10 */)
    //     0x7b29c0: mov             x3, x1
    //     0x7b29c4: stur            x1, [fp, #-0x10]
    // 0x7b29c8: LoadField: r4 = r2->field_7
    //     0x7b29c8: ldur            w4, [x2, #7]
    // 0x7b29cc: DecompressPointer r4
    //     0x7b29cc: add             x4, x4, HEAP, lsl #32
    // 0x7b29d0: stur            x4, [fp, #-8]
    // 0x7b29d4: cmp             w4, NULL
    // 0x7b29d8: b.eq            #0x7b2c70
    // 0x7b29dc: mov             x0, x4
    // 0x7b29e0: r2 = Null
    //     0x7b29e0: mov             x2, NULL
    // 0x7b29e4: r1 = Null
    //     0x7b29e4: mov             x1, NULL
    // 0x7b29e8: r4 = LoadClassIdInstr(r0)
    //     0x7b29e8: ldur            x4, [x0, #-1]
    //     0x7b29ec: ubfx            x4, x4, #0xc, #0x14
    // 0x7b29f0: cmp             x4, #0xc7e
    // 0x7b29f4: b.eq            #0x7b2a0c
    // 0x7b29f8: r8 = FlexParentData
    //     0x7b29f8: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x7b29fc: ldr             x8, [x8, #0x590]
    // 0x7b2a00: r3 = Null
    //     0x7b2a00: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f080] Null
    //     0x7b2a04: ldr             x3, [x3, #0x80]
    // 0x7b2a08: r0 = DefaultTypeTest()
    //     0x7b2a08: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b2a0c: ldur            x3, [fp, #-8]
    // 0x7b2a10: LoadField: r4 = r3->field_f
    //     0x7b2a10: ldur            w4, [x3, #0xf]
    // 0x7b2a14: DecompressPointer r4
    //     0x7b2a14: add             x4, x4, HEAP, lsl #32
    // 0x7b2a18: stur            x4, [fp, #-0x20]
    // 0x7b2a1c: cmp             w4, NULL
    // 0x7b2a20: b.ne            #0x7b2a50
    // 0x7b2a24: ldur            x5, [fp, #-0x10]
    // 0x7b2a28: LoadField: r0 = r3->field_13
    //     0x7b2a28: ldur            w0, [x3, #0x13]
    // 0x7b2a2c: DecompressPointer r0
    //     0x7b2a2c: add             x0, x0, HEAP, lsl #32
    // 0x7b2a30: StoreField: r5->field_5f = r0
    //     0x7b2a30: stur            w0, [x5, #0x5f]
    //     0x7b2a34: ldurb           w16, [x5, #-1]
    //     0x7b2a38: ldurb           w17, [x0, #-1]
    //     0x7b2a3c: and             x16, x17, x16, lsr #2
    //     0x7b2a40: tst             x16, HEAP, lsr #32
    //     0x7b2a44: b.eq            #0x7b2a4c
    //     0x7b2a48: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x7b2a4c: b               #0x7b2b14
    // 0x7b2a50: ldur            x5, [fp, #-0x10]
    // 0x7b2a54: LoadField: r6 = r4->field_7
    //     0x7b2a54: ldur            w6, [x4, #7]
    // 0x7b2a58: DecompressPointer r6
    //     0x7b2a58: add             x6, x6, HEAP, lsl #32
    // 0x7b2a5c: stur            x6, [fp, #-0x18]
    // 0x7b2a60: cmp             w6, NULL
    // 0x7b2a64: b.eq            #0x7b2c74
    // 0x7b2a68: mov             x0, x6
    // 0x7b2a6c: r2 = Null
    //     0x7b2a6c: mov             x2, NULL
    // 0x7b2a70: r1 = Null
    //     0x7b2a70: mov             x1, NULL
    // 0x7b2a74: r4 = LoadClassIdInstr(r0)
    //     0x7b2a74: ldur            x4, [x0, #-1]
    //     0x7b2a78: ubfx            x4, x4, #0xc, #0x14
    // 0x7b2a7c: cmp             x4, #0xc7e
    // 0x7b2a80: b.eq            #0x7b2a98
    // 0x7b2a84: r8 = FlexParentData
    //     0x7b2a84: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x7b2a88: ldr             x8, [x8, #0x590]
    // 0x7b2a8c: r3 = Null
    //     0x7b2a8c: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f090] Null
    //     0x7b2a90: ldr             x3, [x3, #0x90]
    // 0x7b2a94: r0 = DefaultTypeTest()
    //     0x7b2a94: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b2a98: ldur            x3, [fp, #-8]
    // 0x7b2a9c: LoadField: r4 = r3->field_13
    //     0x7b2a9c: ldur            w4, [x3, #0x13]
    // 0x7b2aa0: DecompressPointer r4
    //     0x7b2aa0: add             x4, x4, HEAP, lsl #32
    // 0x7b2aa4: ldur            x5, [fp, #-0x18]
    // 0x7b2aa8: stur            x4, [fp, #-0x28]
    // 0x7b2aac: LoadField: r2 = r5->field_b
    //     0x7b2aac: ldur            w2, [x5, #0xb]
    // 0x7b2ab0: DecompressPointer r2
    //     0x7b2ab0: add             x2, x2, HEAP, lsl #32
    // 0x7b2ab4: mov             x0, x4
    // 0x7b2ab8: r1 = Null
    //     0x7b2ab8: mov             x1, NULL
    // 0x7b2abc: cmp             w0, NULL
    // 0x7b2ac0: b.eq            #0x7b2aec
    // 0x7b2ac4: cmp             w2, NULL
    // 0x7b2ac8: b.eq            #0x7b2aec
    // 0x7b2acc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b2acc: ldur            w4, [x2, #0x17]
    // 0x7b2ad0: DecompressPointer r4
    //     0x7b2ad0: add             x4, x4, HEAP, lsl #32
    // 0x7b2ad4: r8 = X0? bound RenderObject
    //     0x7b2ad4: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b2ad8: ldr             x8, [x8, #0x1a8]
    // 0x7b2adc: LoadField: r9 = r4->field_7
    //     0x7b2adc: ldur            x9, [x4, #7]
    // 0x7b2ae0: r3 = Null
    //     0x7b2ae0: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f0a0] Null
    //     0x7b2ae4: ldr             x3, [x3, #0xa0]
    // 0x7b2ae8: blr             x9
    // 0x7b2aec: ldur            x0, [fp, #-0x28]
    // 0x7b2af0: ldur            x1, [fp, #-0x18]
    // 0x7b2af4: StoreField: r1->field_13 = r0
    //     0x7b2af4: stur            w0, [x1, #0x13]
    //     0x7b2af8: ldurb           w16, [x1, #-1]
    //     0x7b2afc: ldurb           w17, [x0, #-1]
    //     0x7b2b00: and             x16, x17, x16, lsr #2
    //     0x7b2b04: tst             x16, HEAP, lsr #32
    //     0x7b2b08: b.eq            #0x7b2b10
    //     0x7b2b0c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b2b10: ldur            x3, [fp, #-8]
    // 0x7b2b14: LoadField: r0 = r3->field_13
    //     0x7b2b14: ldur            w0, [x3, #0x13]
    // 0x7b2b18: DecompressPointer r0
    //     0x7b2b18: add             x0, x0, HEAP, lsl #32
    // 0x7b2b1c: cmp             w0, NULL
    // 0x7b2b20: b.ne            #0x7b2b4c
    // 0x7b2b24: ldur            x4, [fp, #-0x10]
    // 0x7b2b28: ldur            x0, [fp, #-0x20]
    // 0x7b2b2c: StoreField: r4->field_63 = r0
    //     0x7b2b2c: stur            w0, [x4, #0x63]
    //     0x7b2b30: ldurb           w16, [x4, #-1]
    //     0x7b2b34: ldurb           w17, [x0, #-1]
    //     0x7b2b38: and             x16, x17, x16, lsr #2
    //     0x7b2b3c: tst             x16, HEAP, lsr #32
    //     0x7b2b40: b.eq            #0x7b2b48
    //     0x7b2b44: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7b2b48: b               #0x7b2c04
    // 0x7b2b4c: ldur            x4, [fp, #-0x10]
    // 0x7b2b50: LoadField: r5 = r0->field_7
    //     0x7b2b50: ldur            w5, [x0, #7]
    // 0x7b2b54: DecompressPointer r5
    //     0x7b2b54: add             x5, x5, HEAP, lsl #32
    // 0x7b2b58: stur            x5, [fp, #-0x18]
    // 0x7b2b5c: cmp             w5, NULL
    // 0x7b2b60: b.eq            #0x7b2c78
    // 0x7b2b64: mov             x0, x5
    // 0x7b2b68: r2 = Null
    //     0x7b2b68: mov             x2, NULL
    // 0x7b2b6c: r1 = Null
    //     0x7b2b6c: mov             x1, NULL
    // 0x7b2b70: r4 = LoadClassIdInstr(r0)
    //     0x7b2b70: ldur            x4, [x0, #-1]
    //     0x7b2b74: ubfx            x4, x4, #0xc, #0x14
    // 0x7b2b78: cmp             x4, #0xc7e
    // 0x7b2b7c: b.eq            #0x7b2b94
    // 0x7b2b80: r8 = FlexParentData
    //     0x7b2b80: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x7b2b84: ldr             x8, [x8, #0x590]
    // 0x7b2b88: r3 = Null
    //     0x7b2b88: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f0b0] Null
    //     0x7b2b8c: ldr             x3, [x3, #0xb0]
    // 0x7b2b90: r0 = DefaultTypeTest()
    //     0x7b2b90: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b2b94: ldur            x3, [fp, #-0x18]
    // 0x7b2b98: LoadField: r2 = r3->field_b
    //     0x7b2b98: ldur            w2, [x3, #0xb]
    // 0x7b2b9c: DecompressPointer r2
    //     0x7b2b9c: add             x2, x2, HEAP, lsl #32
    // 0x7b2ba0: ldur            x0, [fp, #-0x20]
    // 0x7b2ba4: r1 = Null
    //     0x7b2ba4: mov             x1, NULL
    // 0x7b2ba8: cmp             w0, NULL
    // 0x7b2bac: b.eq            #0x7b2bd8
    // 0x7b2bb0: cmp             w2, NULL
    // 0x7b2bb4: b.eq            #0x7b2bd8
    // 0x7b2bb8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b2bb8: ldur            w4, [x2, #0x17]
    // 0x7b2bbc: DecompressPointer r4
    //     0x7b2bbc: add             x4, x4, HEAP, lsl #32
    // 0x7b2bc0: r8 = X0? bound RenderObject
    //     0x7b2bc0: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b2bc4: ldr             x8, [x8, #0x1a8]
    // 0x7b2bc8: LoadField: r9 = r4->field_7
    //     0x7b2bc8: ldur            x9, [x4, #7]
    // 0x7b2bcc: r3 = Null
    //     0x7b2bcc: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f0c0] Null
    //     0x7b2bd0: ldr             x3, [x3, #0xc0]
    // 0x7b2bd4: blr             x9
    // 0x7b2bd8: ldur            x0, [fp, #-0x20]
    // 0x7b2bdc: ldur            x1, [fp, #-0x18]
    // 0x7b2be0: StoreField: r1->field_f = r0
    //     0x7b2be0: stur            w0, [x1, #0xf]
    //     0x7b2be4: ldurb           w16, [x1, #-1]
    //     0x7b2be8: ldurb           w17, [x0, #-1]
    //     0x7b2bec: and             x16, x17, x16, lsr #2
    //     0x7b2bf0: tst             x16, HEAP, lsr #32
    //     0x7b2bf4: b.eq            #0x7b2bfc
    //     0x7b2bf8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b2bfc: ldur            x4, [fp, #-0x10]
    // 0x7b2c00: ldur            x3, [fp, #-8]
    // 0x7b2c04: LoadField: r2 = r3->field_b
    //     0x7b2c04: ldur            w2, [x3, #0xb]
    // 0x7b2c08: DecompressPointer r2
    //     0x7b2c08: add             x2, x2, HEAP, lsl #32
    // 0x7b2c0c: r0 = Null
    //     0x7b2c0c: mov             x0, NULL
    // 0x7b2c10: r1 = Null
    //     0x7b2c10: mov             x1, NULL
    // 0x7b2c14: cmp             w0, NULL
    // 0x7b2c18: b.eq            #0x7b2c44
    // 0x7b2c1c: cmp             w2, NULL
    // 0x7b2c20: b.eq            #0x7b2c44
    // 0x7b2c24: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b2c24: ldur            w4, [x2, #0x17]
    // 0x7b2c28: DecompressPointer r4
    //     0x7b2c28: add             x4, x4, HEAP, lsl #32
    // 0x7b2c2c: r8 = X0? bound RenderObject
    //     0x7b2c2c: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b2c30: ldr             x8, [x8, #0x1a8]
    // 0x7b2c34: LoadField: r9 = r4->field_7
    //     0x7b2c34: ldur            x9, [x4, #7]
    // 0x7b2c38: r3 = Null
    //     0x7b2c38: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f0d0] Null
    //     0x7b2c3c: ldr             x3, [x3, #0xd0]
    // 0x7b2c40: blr             x9
    // 0x7b2c44: ldur            x1, [fp, #-8]
    // 0x7b2c48: StoreField: r1->field_f = rNULL
    //     0x7b2c48: stur            NULL, [x1, #0xf]
    // 0x7b2c4c: StoreField: r1->field_13 = rNULL
    //     0x7b2c4c: stur            NULL, [x1, #0x13]
    // 0x7b2c50: ldur            x1, [fp, #-0x10]
    // 0x7b2c54: LoadField: r2 = r1->field_57
    //     0x7b2c54: ldur            x2, [x1, #0x57]
    // 0x7b2c58: sub             x3, x2, #1
    // 0x7b2c5c: StoreField: r1->field_57 = r3
    //     0x7b2c5c: stur            x3, [x1, #0x57]
    // 0x7b2c60: r0 = Null
    //     0x7b2c60: mov             x0, NULL
    // 0x7b2c64: LeaveFrame
    //     0x7b2c64: mov             SP, fp
    //     0x7b2c68: ldp             fp, lr, [SP], #0x10
    // 0x7b2c6c: ret
    //     0x7b2c6c: ret             
    // 0x7b2c70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b2c70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7b2c74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b2c74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7b2c78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b2c78: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ move(/* No info */) {
    // ** addr: 0x7cde38, size: 0x160
    // 0x7cde38: EnterFrame
    //     0x7cde38: stp             fp, lr, [SP, #-0x10]!
    //     0x7cde3c: mov             fp, SP
    // 0x7cde40: AllocStack(0x30)
    //     0x7cde40: sub             SP, SP, #0x30
    // 0x7cde44: SetupParameters(_RenderFlex&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7cde44: mov             x5, x1
    //     0x7cde48: mov             x4, x2
    //     0x7cde4c: stur            x1, [fp, #-8]
    //     0x7cde50: stur            x2, [fp, #-0x10]
    //     0x7cde54: stur            x3, [fp, #-0x18]
    // 0x7cde58: CheckStackOverflow
    //     0x7cde58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cde5c: cmp             SP, x16
    //     0x7cde60: b.ls            #0x7cdf8c
    // 0x7cde64: mov             x0, x4
    // 0x7cde68: r2 = Null
    //     0x7cde68: mov             x2, NULL
    // 0x7cde6c: r1 = Null
    //     0x7cde6c: mov             x1, NULL
    // 0x7cde70: r4 = 60
    //     0x7cde70: movz            x4, #0x3c
    // 0x7cde74: branchIfSmi(r0, 0x7cde80)
    //     0x7cde74: tbz             w0, #0, #0x7cde80
    // 0x7cde78: r4 = LoadClassIdInstr(r0)
    //     0x7cde78: ldur            x4, [x0, #-1]
    //     0x7cde7c: ubfx            x4, x4, #0xc, #0x14
    // 0x7cde80: sub             x4, x4, #0xbba
    // 0x7cde84: cmp             x4, #0x9a
    // 0x7cde88: b.ls            #0x7cde9c
    // 0x7cde8c: r8 = RenderBox
    //     0x7cde8c: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7cde90: r3 = Null
    //     0x7cde90: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4ef90] Null
    //     0x7cde94: ldr             x3, [x3, #0xf90]
    // 0x7cde98: r0 = RenderBox()
    //     0x7cde98: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7cde9c: ldur            x0, [fp, #-0x18]
    // 0x7cdea0: r2 = Null
    //     0x7cdea0: mov             x2, NULL
    // 0x7cdea4: r1 = Null
    //     0x7cdea4: mov             x1, NULL
    // 0x7cdea8: r4 = 60
    //     0x7cdea8: movz            x4, #0x3c
    // 0x7cdeac: branchIfSmi(r0, 0x7cdeb8)
    //     0x7cdeac: tbz             w0, #0, #0x7cdeb8
    // 0x7cdeb0: r4 = LoadClassIdInstr(r0)
    //     0x7cdeb0: ldur            x4, [x0, #-1]
    //     0x7cdeb4: ubfx            x4, x4, #0xc, #0x14
    // 0x7cdeb8: sub             x4, x4, #0xbba
    // 0x7cdebc: cmp             x4, #0x9a
    // 0x7cdec0: b.ls            #0x7cded4
    // 0x7cdec4: r8 = RenderBox?
    //     0x7cdec4: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x7cdec8: r3 = Null
    //     0x7cdec8: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4efa0] Null
    //     0x7cdecc: ldr             x3, [x3, #0xfa0]
    // 0x7cded0: r0 = RenderBox?()
    //     0x7cded0: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x7cded4: ldur            x3, [fp, #-0x10]
    // 0x7cded8: LoadField: r4 = r3->field_7
    //     0x7cded8: ldur            w4, [x3, #7]
    // 0x7cdedc: DecompressPointer r4
    //     0x7cdedc: add             x4, x4, HEAP, lsl #32
    // 0x7cdee0: stur            x4, [fp, #-0x20]
    // 0x7cdee4: cmp             w4, NULL
    // 0x7cdee8: b.eq            #0x7cdf94
    // 0x7cdeec: mov             x0, x4
    // 0x7cdef0: r2 = Null
    //     0x7cdef0: mov             x2, NULL
    // 0x7cdef4: r1 = Null
    //     0x7cdef4: mov             x1, NULL
    // 0x7cdef8: r4 = LoadClassIdInstr(r0)
    //     0x7cdef8: ldur            x4, [x0, #-1]
    //     0x7cdefc: ubfx            x4, x4, #0xc, #0x14
    // 0x7cdf00: cmp             x4, #0xc7e
    // 0x7cdf04: b.eq            #0x7cdf1c
    // 0x7cdf08: r8 = FlexParentData
    //     0x7cdf08: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x7cdf0c: ldr             x8, [x8, #0x590]
    // 0x7cdf10: r3 = Null
    //     0x7cdf10: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4efb0] Null
    //     0x7cdf14: ldr             x3, [x3, #0xfb0]
    // 0x7cdf18: r0 = DefaultTypeTest()
    //     0x7cdf18: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7cdf1c: ldur            x0, [fp, #-0x20]
    // 0x7cdf20: LoadField: r1 = r0->field_f
    //     0x7cdf20: ldur            w1, [x0, #0xf]
    // 0x7cdf24: DecompressPointer r1
    //     0x7cdf24: add             x1, x1, HEAP, lsl #32
    // 0x7cdf28: r0 = LoadClassIdInstr(r1)
    //     0x7cdf28: ldur            x0, [x1, #-1]
    //     0x7cdf2c: ubfx            x0, x0, #0xc, #0x14
    // 0x7cdf30: ldur            x16, [fp, #-0x18]
    // 0x7cdf34: stp             x16, x1, [SP]
    // 0x7cdf38: mov             lr, x0
    // 0x7cdf3c: ldr             lr, [x21, lr, lsl #3]
    // 0x7cdf40: blr             lr
    // 0x7cdf44: tbnz            w0, #4, #0x7cdf58
    // 0x7cdf48: r0 = Null
    //     0x7cdf48: mov             x0, NULL
    // 0x7cdf4c: LeaveFrame
    //     0x7cdf4c: mov             SP, fp
    //     0x7cdf50: ldp             fp, lr, [SP], #0x10
    // 0x7cdf54: ret
    //     0x7cdf54: ret             
    // 0x7cdf58: ldur            x1, [fp, #-8]
    // 0x7cdf5c: ldur            x2, [fp, #-0x10]
    // 0x7cdf60: r0 = _removeFromChildList()
    //     0x7cdf60: bl              #0x7b29b4  ; [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin::_removeFromChildList
    // 0x7cdf64: ldur            x1, [fp, #-8]
    // 0x7cdf68: ldur            x2, [fp, #-0x10]
    // 0x7cdf6c: ldur            x3, [fp, #-0x18]
    // 0x7cdf70: r0 = _insertIntoChildList()
    //     0x7cdf70: bl              #0xda43b0  ; [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin::_insertIntoChildList
    // 0x7cdf74: ldur            x1, [fp, #-8]
    // 0x7cdf78: r0 = markNeedsLayout()
    //     0x7cdf78: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0x7cdf7c: r0 = Null
    //     0x7cdf7c: mov             x0, NULL
    // 0x7cdf80: LeaveFrame
    //     0x7cdf80: mov             SP, fp
    //     0x7cdf84: ldp             fp, lr, [SP], #0x10
    // 0x7cdf88: ret
    //     0x7cdf88: ret             
    // 0x7cdf8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cdf8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cdf90: b               #0x7cde64
    // 0x7cdf94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7cdf94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ detach(/* No info */) {
    // ** addr: 0x8067a0, size: 0xe8
    // 0x8067a0: EnterFrame
    //     0x8067a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8067a4: mov             fp, SP
    // 0x8067a8: AllocStack(0x10)
    //     0x8067a8: sub             SP, SP, #0x10
    // 0x8067ac: SetupParameters(_RenderFlex&RenderBox&ContainerRenderObjectMixin this /* r1 => r0, fp-0x8 */)
    //     0x8067ac: mov             x0, x1
    //     0x8067b0: stur            x1, [fp, #-8]
    // 0x8067b4: CheckStackOverflow
    //     0x8067b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8067b8: cmp             SP, x16
    //     0x8067bc: b.ls            #0x806874
    // 0x8067c0: mov             x1, x0
    // 0x8067c4: r0 = detach()
    //     0x8067c4: bl              #0x8083b4  ; [package:flutter/src/rendering/object.dart] RenderObject::detach
    // 0x8067c8: ldur            x0, [fp, #-8]
    // 0x8067cc: LoadField: r1 = r0->field_5f
    //     0x8067cc: ldur            w1, [x0, #0x5f]
    // 0x8067d0: DecompressPointer r1
    //     0x8067d0: add             x1, x1, HEAP, lsl #32
    // 0x8067d4: mov             x2, x1
    // 0x8067d8: stur            x2, [fp, #-8]
    // 0x8067dc: CheckStackOverflow
    //     0x8067dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8067e0: cmp             SP, x16
    //     0x8067e4: b.ls            #0x80687c
    // 0x8067e8: cmp             w2, NULL
    // 0x8067ec: b.eq            #0x806864
    // 0x8067f0: r0 = LoadClassIdInstr(r2)
    //     0x8067f0: ldur            x0, [x2, #-1]
    //     0x8067f4: ubfx            x0, x0, #0xc, #0x14
    // 0x8067f8: mov             x1, x2
    // 0x8067fc: r0 = GDT[cid_x0 + 0xeec9]()
    //     0x8067fc: movz            x17, #0xeec9
    //     0x806800: add             lr, x0, x17
    //     0x806804: ldr             lr, [x21, lr, lsl #3]
    //     0x806808: blr             lr
    // 0x80680c: ldur            x0, [fp, #-8]
    // 0x806810: LoadField: r3 = r0->field_7
    //     0x806810: ldur            w3, [x0, #7]
    // 0x806814: DecompressPointer r3
    //     0x806814: add             x3, x3, HEAP, lsl #32
    // 0x806818: stur            x3, [fp, #-0x10]
    // 0x80681c: cmp             w3, NULL
    // 0x806820: b.eq            #0x806884
    // 0x806824: mov             x0, x3
    // 0x806828: r2 = Null
    //     0x806828: mov             x2, NULL
    // 0x80682c: r1 = Null
    //     0x80682c: mov             x1, NULL
    // 0x806830: r4 = LoadClassIdInstr(r0)
    //     0x806830: ldur            x4, [x0, #-1]
    //     0x806834: ubfx            x4, x4, #0xc, #0x14
    // 0x806838: cmp             x4, #0xc7e
    // 0x80683c: b.eq            #0x806854
    // 0x806840: r8 = FlexParentData
    //     0x806840: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x806844: ldr             x8, [x8, #0x590]
    // 0x806848: r3 = Null
    //     0x806848: add             x3, PP, #0x45, lsl #12  ; [pp+0x45788] Null
    //     0x80684c: ldr             x3, [x3, #0x788]
    // 0x806850: r0 = DefaultTypeTest()
    //     0x806850: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x806854: ldur            x1, [fp, #-0x10]
    // 0x806858: LoadField: r2 = r1->field_13
    //     0x806858: ldur            w2, [x1, #0x13]
    // 0x80685c: DecompressPointer r2
    //     0x80685c: add             x2, x2, HEAP, lsl #32
    // 0x806860: b               #0x8067d8
    // 0x806864: r0 = Null
    //     0x806864: mov             x0, NULL
    // 0x806868: LeaveFrame
    //     0x806868: mov             SP, fp
    //     0x80686c: ldp             fp, lr, [SP], #0x10
    // 0x806870: ret
    //     0x806870: ret             
    // 0x806874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x806874: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806878: b               #0x8067c0
    // 0x80687c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80687c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806880: b               #0x8067e8
    // 0x806884: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x806884: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ redepthChildren(/* No info */) {
    // ** addr: 0x808b04, size: 0xf8
    // 0x808b04: EnterFrame
    //     0x808b04: stp             fp, lr, [SP, #-0x10]!
    //     0x808b08: mov             fp, SP
    // 0x808b0c: AllocStack(0x18)
    //     0x808b0c: sub             SP, SP, #0x18
    // 0x808b10: SetupParameters(_RenderFlex&RenderBox&ContainerRenderObjectMixin this /* r1 => r2, fp-0x10 */)
    //     0x808b10: mov             x2, x1
    //     0x808b14: stur            x1, [fp, #-0x10]
    // 0x808b18: CheckStackOverflow
    //     0x808b18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x808b1c: cmp             SP, x16
    //     0x808b20: b.ls            #0x808be8
    // 0x808b24: LoadField: r0 = r2->field_5f
    //     0x808b24: ldur            w0, [x2, #0x5f]
    // 0x808b28: DecompressPointer r0
    //     0x808b28: add             x0, x0, HEAP, lsl #32
    // 0x808b2c: mov             x3, x0
    // 0x808b30: stur            x3, [fp, #-8]
    // 0x808b34: CheckStackOverflow
    //     0x808b34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x808b38: cmp             SP, x16
    //     0x808b3c: b.ls            #0x808bf0
    // 0x808b40: cmp             w3, NULL
    // 0x808b44: b.eq            #0x808bd8
    // 0x808b48: LoadField: r0 = r3->field_b
    //     0x808b48: ldur            x0, [x3, #0xb]
    // 0x808b4c: LoadField: r1 = r2->field_b
    //     0x808b4c: ldur            x1, [x2, #0xb]
    // 0x808b50: cmp             x0, x1
    // 0x808b54: b.gt            #0x808b7c
    // 0x808b58: add             x0, x1, #1
    // 0x808b5c: StoreField: r3->field_b = r0
    //     0x808b5c: stur            x0, [x3, #0xb]
    // 0x808b60: r0 = LoadClassIdInstr(r3)
    //     0x808b60: ldur            x0, [x3, #-1]
    //     0x808b64: ubfx            x0, x0, #0xc, #0x14
    // 0x808b68: mov             x1, x3
    // 0x808b6c: r0 = GDT[cid_x0 + 0xedec]()
    //     0x808b6c: movz            x17, #0xedec
    //     0x808b70: add             lr, x0, x17
    //     0x808b74: ldr             lr, [x21, lr, lsl #3]
    //     0x808b78: blr             lr
    // 0x808b7c: ldur            x0, [fp, #-8]
    // 0x808b80: LoadField: r3 = r0->field_7
    //     0x808b80: ldur            w3, [x0, #7]
    // 0x808b84: DecompressPointer r3
    //     0x808b84: add             x3, x3, HEAP, lsl #32
    // 0x808b88: stur            x3, [fp, #-0x18]
    // 0x808b8c: cmp             w3, NULL
    // 0x808b90: b.eq            #0x808bf8
    // 0x808b94: mov             x0, x3
    // 0x808b98: r2 = Null
    //     0x808b98: mov             x2, NULL
    // 0x808b9c: r1 = Null
    //     0x808b9c: mov             x1, NULL
    // 0x808ba0: r4 = LoadClassIdInstr(r0)
    //     0x808ba0: ldur            x4, [x0, #-1]
    //     0x808ba4: ubfx            x4, x4, #0xc, #0x14
    // 0x808ba8: cmp             x4, #0xc7e
    // 0x808bac: b.eq            #0x808bc4
    // 0x808bb0: r8 = FlexParentData
    //     0x808bb0: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x808bb4: ldr             x8, [x8, #0x590]
    // 0x808bb8: r3 = Null
    //     0x808bb8: add             x3, PP, #0x45, lsl #12  ; [pp+0x45778] Null
    //     0x808bbc: ldr             x3, [x3, #0x778]
    // 0x808bc0: r0 = DefaultTypeTest()
    //     0x808bc0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x808bc4: ldur            x1, [fp, #-0x18]
    // 0x808bc8: LoadField: r3 = r1->field_13
    //     0x808bc8: ldur            w3, [x1, #0x13]
    // 0x808bcc: DecompressPointer r3
    //     0x808bcc: add             x3, x3, HEAP, lsl #32
    // 0x808bd0: ldur            x2, [fp, #-0x10]
    // 0x808bd4: b               #0x808b30
    // 0x808bd8: r0 = Null
    //     0x808bd8: mov             x0, NULL
    // 0x808bdc: LeaveFrame
    //     0x808bdc: mov             SP, fp
    //     0x808be0: ldp             fp, lr, [SP], #0x10
    // 0x808be4: ret
    //     0x808be4: ret             
    // 0x808be8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x808be8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x808bec: b               #0x808b24
    // 0x808bf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x808bf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x808bf4: b               #0x808b40
    // 0x808bf8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x808bf8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _insertIntoChildList(/* No info */) {
    // ** addr: 0xda43b0, size: 0x570
    // 0xda43b0: EnterFrame
    //     0xda43b0: stp             fp, lr, [SP, #-0x10]!
    //     0xda43b4: mov             fp, SP
    // 0xda43b8: AllocStack(0x30)
    //     0xda43b8: sub             SP, SP, #0x30
    // 0xda43bc: SetupParameters(_RenderFlex&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xda43bc: mov             x5, x1
    //     0xda43c0: mov             x4, x2
    //     0xda43c4: stur            x1, [fp, #-0x10]
    //     0xda43c8: stur            x2, [fp, #-0x18]
    //     0xda43cc: stur            x3, [fp, #-0x20]
    // 0xda43d0: LoadField: r6 = r4->field_7
    //     0xda43d0: ldur            w6, [x4, #7]
    // 0xda43d4: DecompressPointer r6
    //     0xda43d4: add             x6, x6, HEAP, lsl #32
    // 0xda43d8: stur            x6, [fp, #-8]
    // 0xda43dc: cmp             w6, NULL
    // 0xda43e0: b.eq            #0xda4910
    // 0xda43e4: mov             x0, x6
    // 0xda43e8: r2 = Null
    //     0xda43e8: mov             x2, NULL
    // 0xda43ec: r1 = Null
    //     0xda43ec: mov             x1, NULL
    // 0xda43f0: r4 = LoadClassIdInstr(r0)
    //     0xda43f0: ldur            x4, [x0, #-1]
    //     0xda43f4: ubfx            x4, x4, #0xc, #0x14
    // 0xda43f8: cmp             x4, #0xc7e
    // 0xda43fc: b.eq            #0xda4414
    // 0xda4400: r8 = FlexParentData
    //     0xda4400: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0xda4404: ldr             x8, [x8, #0x590]
    // 0xda4408: r3 = Null
    //     0xda4408: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4efc0] Null
    //     0xda440c: ldr             x3, [x3, #0xfc0]
    // 0xda4410: r0 = DefaultTypeTest()
    //     0xda4410: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda4414: ldur            x3, [fp, #-0x10]
    // 0xda4418: LoadField: r0 = r3->field_57
    //     0xda4418: ldur            x0, [x3, #0x57]
    // 0xda441c: add             x1, x0, #1
    // 0xda4420: StoreField: r3->field_57 = r1
    //     0xda4420: stur            x1, [x3, #0x57]
    // 0xda4424: ldur            x4, [fp, #-0x20]
    // 0xda4428: cmp             w4, NULL
    // 0xda442c: b.ne            #0xda45b4
    // 0xda4430: ldur            x4, [fp, #-8]
    // 0xda4434: LoadField: r5 = r3->field_5f
    //     0xda4434: ldur            w5, [x3, #0x5f]
    // 0xda4438: DecompressPointer r5
    //     0xda4438: add             x5, x5, HEAP, lsl #32
    // 0xda443c: stur            x5, [fp, #-0x28]
    // 0xda4440: LoadField: r2 = r4->field_b
    //     0xda4440: ldur            w2, [x4, #0xb]
    // 0xda4444: DecompressPointer r2
    //     0xda4444: add             x2, x2, HEAP, lsl #32
    // 0xda4448: mov             x0, x5
    // 0xda444c: r1 = Null
    //     0xda444c: mov             x1, NULL
    // 0xda4450: cmp             w0, NULL
    // 0xda4454: b.eq            #0xda4480
    // 0xda4458: cmp             w2, NULL
    // 0xda445c: b.eq            #0xda4480
    // 0xda4460: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda4460: ldur            w4, [x2, #0x17]
    // 0xda4464: DecompressPointer r4
    //     0xda4464: add             x4, x4, HEAP, lsl #32
    // 0xda4468: r8 = X0? bound RenderObject
    //     0xda4468: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda446c: ldr             x8, [x8, #0x1a8]
    // 0xda4470: LoadField: r9 = r4->field_7
    //     0xda4470: ldur            x9, [x4, #7]
    // 0xda4474: r3 = Null
    //     0xda4474: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4efd0] Null
    //     0xda4478: ldr             x3, [x3, #0xfd0]
    // 0xda447c: blr             x9
    // 0xda4480: ldur            x0, [fp, #-0x28]
    // 0xda4484: ldur            x3, [fp, #-8]
    // 0xda4488: StoreField: r3->field_13 = r0
    //     0xda4488: stur            w0, [x3, #0x13]
    //     0xda448c: ldurb           w16, [x3, #-1]
    //     0xda4490: ldurb           w17, [x0, #-1]
    //     0xda4494: and             x16, x17, x16, lsr #2
    //     0xda4498: tst             x16, HEAP, lsr #32
    //     0xda449c: b.eq            #0xda44a4
    //     0xda44a0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda44a4: ldur            x0, [fp, #-0x28]
    // 0xda44a8: cmp             w0, NULL
    // 0xda44ac: b.eq            #0xda455c
    // 0xda44b0: LoadField: r3 = r0->field_7
    //     0xda44b0: ldur            w3, [x0, #7]
    // 0xda44b4: DecompressPointer r3
    //     0xda44b4: add             x3, x3, HEAP, lsl #32
    // 0xda44b8: stur            x3, [fp, #-0x30]
    // 0xda44bc: cmp             w3, NULL
    // 0xda44c0: b.eq            #0xda4914
    // 0xda44c4: mov             x0, x3
    // 0xda44c8: r2 = Null
    //     0xda44c8: mov             x2, NULL
    // 0xda44cc: r1 = Null
    //     0xda44cc: mov             x1, NULL
    // 0xda44d0: r4 = LoadClassIdInstr(r0)
    //     0xda44d0: ldur            x4, [x0, #-1]
    //     0xda44d4: ubfx            x4, x4, #0xc, #0x14
    // 0xda44d8: cmp             x4, #0xc7e
    // 0xda44dc: b.eq            #0xda44f4
    // 0xda44e0: r8 = FlexParentData
    //     0xda44e0: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0xda44e4: ldr             x8, [x8, #0x590]
    // 0xda44e8: r3 = Null
    //     0xda44e8: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4efe0] Null
    //     0xda44ec: ldr             x3, [x3, #0xfe0]
    // 0xda44f0: r0 = DefaultTypeTest()
    //     0xda44f0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda44f4: ldur            x3, [fp, #-0x30]
    // 0xda44f8: LoadField: r2 = r3->field_b
    //     0xda44f8: ldur            w2, [x3, #0xb]
    // 0xda44fc: DecompressPointer r2
    //     0xda44fc: add             x2, x2, HEAP, lsl #32
    // 0xda4500: ldur            x0, [fp, #-0x18]
    // 0xda4504: r1 = Null
    //     0xda4504: mov             x1, NULL
    // 0xda4508: cmp             w0, NULL
    // 0xda450c: b.eq            #0xda4538
    // 0xda4510: cmp             w2, NULL
    // 0xda4514: b.eq            #0xda4538
    // 0xda4518: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda4518: ldur            w4, [x2, #0x17]
    // 0xda451c: DecompressPointer r4
    //     0xda451c: add             x4, x4, HEAP, lsl #32
    // 0xda4520: r8 = X0? bound RenderObject
    //     0xda4520: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda4524: ldr             x8, [x8, #0x1a8]
    // 0xda4528: LoadField: r9 = r4->field_7
    //     0xda4528: ldur            x9, [x4, #7]
    // 0xda452c: r3 = Null
    //     0xda452c: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4eff0] Null
    //     0xda4530: ldr             x3, [x3, #0xff0]
    // 0xda4534: blr             x9
    // 0xda4538: ldur            x0, [fp, #-0x18]
    // 0xda453c: ldur            x1, [fp, #-0x30]
    // 0xda4540: StoreField: r1->field_f = r0
    //     0xda4540: stur            w0, [x1, #0xf]
    //     0xda4544: ldurb           w16, [x1, #-1]
    //     0xda4548: ldurb           w17, [x0, #-1]
    //     0xda454c: and             x16, x17, x16, lsr #2
    //     0xda4550: tst             x16, HEAP, lsr #32
    //     0xda4554: b.eq            #0xda455c
    //     0xda4558: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda455c: ldur            x5, [fp, #-0x10]
    // 0xda4560: ldur            x0, [fp, #-0x18]
    // 0xda4564: StoreField: r5->field_5f = r0
    //     0xda4564: stur            w0, [x5, #0x5f]
    //     0xda4568: ldurb           w16, [x5, #-1]
    //     0xda456c: ldurb           w17, [x0, #-1]
    //     0xda4570: and             x16, x17, x16, lsr #2
    //     0xda4574: tst             x16, HEAP, lsr #32
    //     0xda4578: b.eq            #0xda4580
    //     0xda457c: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda4580: LoadField: r0 = r5->field_63
    //     0xda4580: ldur            w0, [x5, #0x63]
    // 0xda4584: DecompressPointer r0
    //     0xda4584: add             x0, x0, HEAP, lsl #32
    // 0xda4588: cmp             w0, NULL
    // 0xda458c: b.ne            #0xda4900
    // 0xda4590: ldur            x0, [fp, #-0x18]
    // 0xda4594: StoreField: r5->field_63 = r0
    //     0xda4594: stur            w0, [x5, #0x63]
    //     0xda4598: ldurb           w16, [x5, #-1]
    //     0xda459c: ldurb           w17, [x0, #-1]
    //     0xda45a0: and             x16, x17, x16, lsr #2
    //     0xda45a4: tst             x16, HEAP, lsr #32
    //     0xda45a8: b.eq            #0xda45b0
    //     0xda45ac: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda45b0: b               #0xda4900
    // 0xda45b4: mov             x5, x3
    // 0xda45b8: ldur            x3, [fp, #-8]
    // 0xda45bc: LoadField: r6 = r4->field_7
    //     0xda45bc: ldur            w6, [x4, #7]
    // 0xda45c0: DecompressPointer r6
    //     0xda45c0: add             x6, x6, HEAP, lsl #32
    // 0xda45c4: stur            x6, [fp, #-0x28]
    // 0xda45c8: cmp             w6, NULL
    // 0xda45cc: b.eq            #0xda4918
    // 0xda45d0: mov             x0, x6
    // 0xda45d4: r2 = Null
    //     0xda45d4: mov             x2, NULL
    // 0xda45d8: r1 = Null
    //     0xda45d8: mov             x1, NULL
    // 0xda45dc: r4 = LoadClassIdInstr(r0)
    //     0xda45dc: ldur            x4, [x0, #-1]
    //     0xda45e0: ubfx            x4, x4, #0xc, #0x14
    // 0xda45e4: cmp             x4, #0xc7e
    // 0xda45e8: b.eq            #0xda4600
    // 0xda45ec: r8 = FlexParentData
    //     0xda45ec: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0xda45f0: ldr             x8, [x8, #0x590]
    // 0xda45f4: r3 = Null
    //     0xda45f4: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f000] Null
    //     0xda45f8: ldr             x3, [x3]
    // 0xda45fc: r0 = DefaultTypeTest()
    //     0xda45fc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda4600: ldur            x3, [fp, #-0x28]
    // 0xda4604: LoadField: r4 = r3->field_13
    //     0xda4604: ldur            w4, [x3, #0x13]
    // 0xda4608: DecompressPointer r4
    //     0xda4608: add             x4, x4, HEAP, lsl #32
    // 0xda460c: stur            x4, [fp, #-0x30]
    // 0xda4610: cmp             w4, NULL
    // 0xda4614: b.ne            #0xda4714
    // 0xda4618: ldur            x5, [fp, #-0x10]
    // 0xda461c: ldur            x4, [fp, #-8]
    // 0xda4620: LoadField: r2 = r4->field_b
    //     0xda4620: ldur            w2, [x4, #0xb]
    // 0xda4624: DecompressPointer r2
    //     0xda4624: add             x2, x2, HEAP, lsl #32
    // 0xda4628: ldur            x0, [fp, #-0x20]
    // 0xda462c: r1 = Null
    //     0xda462c: mov             x1, NULL
    // 0xda4630: cmp             w0, NULL
    // 0xda4634: b.eq            #0xda4660
    // 0xda4638: cmp             w2, NULL
    // 0xda463c: b.eq            #0xda4660
    // 0xda4640: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda4640: ldur            w4, [x2, #0x17]
    // 0xda4644: DecompressPointer r4
    //     0xda4644: add             x4, x4, HEAP, lsl #32
    // 0xda4648: r8 = X0? bound RenderObject
    //     0xda4648: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda464c: ldr             x8, [x8, #0x1a8]
    // 0xda4650: LoadField: r9 = r4->field_7
    //     0xda4650: ldur            x9, [x4, #7]
    // 0xda4654: r3 = Null
    //     0xda4654: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f010] Null
    //     0xda4658: ldr             x3, [x3, #0x10]
    // 0xda465c: blr             x9
    // 0xda4660: ldur            x0, [fp, #-0x20]
    // 0xda4664: ldur            x3, [fp, #-8]
    // 0xda4668: StoreField: r3->field_f = r0
    //     0xda4668: stur            w0, [x3, #0xf]
    //     0xda466c: ldurb           w16, [x3, #-1]
    //     0xda4670: ldurb           w17, [x0, #-1]
    //     0xda4674: and             x16, x17, x16, lsr #2
    //     0xda4678: tst             x16, HEAP, lsr #32
    //     0xda467c: b.eq            #0xda4684
    //     0xda4680: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda4684: ldur            x3, [fp, #-0x28]
    // 0xda4688: LoadField: r2 = r3->field_b
    //     0xda4688: ldur            w2, [x3, #0xb]
    // 0xda468c: DecompressPointer r2
    //     0xda468c: add             x2, x2, HEAP, lsl #32
    // 0xda4690: ldur            x0, [fp, #-0x18]
    // 0xda4694: r1 = Null
    //     0xda4694: mov             x1, NULL
    // 0xda4698: cmp             w0, NULL
    // 0xda469c: b.eq            #0xda46c8
    // 0xda46a0: cmp             w2, NULL
    // 0xda46a4: b.eq            #0xda46c8
    // 0xda46a8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda46a8: ldur            w4, [x2, #0x17]
    // 0xda46ac: DecompressPointer r4
    //     0xda46ac: add             x4, x4, HEAP, lsl #32
    // 0xda46b0: r8 = X0? bound RenderObject
    //     0xda46b0: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda46b4: ldr             x8, [x8, #0x1a8]
    // 0xda46b8: LoadField: r9 = r4->field_7
    //     0xda46b8: ldur            x9, [x4, #7]
    // 0xda46bc: r3 = Null
    //     0xda46bc: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f020] Null
    //     0xda46c0: ldr             x3, [x3, #0x20]
    // 0xda46c4: blr             x9
    // 0xda46c8: ldur            x0, [fp, #-0x18]
    // 0xda46cc: ldur            x5, [fp, #-0x28]
    // 0xda46d0: StoreField: r5->field_13 = r0
    //     0xda46d0: stur            w0, [x5, #0x13]
    //     0xda46d4: ldurb           w16, [x5, #-1]
    //     0xda46d8: ldurb           w17, [x0, #-1]
    //     0xda46dc: and             x16, x17, x16, lsr #2
    //     0xda46e0: tst             x16, HEAP, lsr #32
    //     0xda46e4: b.eq            #0xda46ec
    //     0xda46e8: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda46ec: ldur            x0, [fp, #-0x18]
    // 0xda46f0: ldur            x1, [fp, #-0x10]
    // 0xda46f4: StoreField: r1->field_63 = r0
    //     0xda46f4: stur            w0, [x1, #0x63]
    //     0xda46f8: ldurb           w16, [x1, #-1]
    //     0xda46fc: ldurb           w17, [x0, #-1]
    //     0xda4700: and             x16, x17, x16, lsr #2
    //     0xda4704: tst             x16, HEAP, lsr #32
    //     0xda4708: b.eq            #0xda4710
    //     0xda470c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda4710: b               #0xda4900
    // 0xda4714: mov             x5, x3
    // 0xda4718: ldur            x3, [fp, #-8]
    // 0xda471c: LoadField: r6 = r3->field_b
    //     0xda471c: ldur            w6, [x3, #0xb]
    // 0xda4720: DecompressPointer r6
    //     0xda4720: add             x6, x6, HEAP, lsl #32
    // 0xda4724: mov             x0, x4
    // 0xda4728: mov             x2, x6
    // 0xda472c: stur            x6, [fp, #-0x10]
    // 0xda4730: r1 = Null
    //     0xda4730: mov             x1, NULL
    // 0xda4734: cmp             w0, NULL
    // 0xda4738: b.eq            #0xda4764
    // 0xda473c: cmp             w2, NULL
    // 0xda4740: b.eq            #0xda4764
    // 0xda4744: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda4744: ldur            w4, [x2, #0x17]
    // 0xda4748: DecompressPointer r4
    //     0xda4748: add             x4, x4, HEAP, lsl #32
    // 0xda474c: r8 = X0? bound RenderObject
    //     0xda474c: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda4750: ldr             x8, [x8, #0x1a8]
    // 0xda4754: LoadField: r9 = r4->field_7
    //     0xda4754: ldur            x9, [x4, #7]
    // 0xda4758: r3 = Null
    //     0xda4758: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f030] Null
    //     0xda475c: ldr             x3, [x3, #0x30]
    // 0xda4760: blr             x9
    // 0xda4764: ldur            x0, [fp, #-0x30]
    // 0xda4768: ldur            x3, [fp, #-8]
    // 0xda476c: StoreField: r3->field_13 = r0
    //     0xda476c: stur            w0, [x3, #0x13]
    //     0xda4770: ldurb           w16, [x3, #-1]
    //     0xda4774: ldurb           w17, [x0, #-1]
    //     0xda4778: and             x16, x17, x16, lsr #2
    //     0xda477c: tst             x16, HEAP, lsr #32
    //     0xda4780: b.eq            #0xda4788
    //     0xda4784: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda4788: ldur            x0, [fp, #-0x20]
    // 0xda478c: ldur            x2, [fp, #-0x10]
    // 0xda4790: r1 = Null
    //     0xda4790: mov             x1, NULL
    // 0xda4794: cmp             w0, NULL
    // 0xda4798: b.eq            #0xda47c4
    // 0xda479c: cmp             w2, NULL
    // 0xda47a0: b.eq            #0xda47c4
    // 0xda47a4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda47a4: ldur            w4, [x2, #0x17]
    // 0xda47a8: DecompressPointer r4
    //     0xda47a8: add             x4, x4, HEAP, lsl #32
    // 0xda47ac: r8 = X0? bound RenderObject
    //     0xda47ac: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda47b0: ldr             x8, [x8, #0x1a8]
    // 0xda47b4: LoadField: r9 = r4->field_7
    //     0xda47b4: ldur            x9, [x4, #7]
    // 0xda47b8: r3 = Null
    //     0xda47b8: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f040] Null
    //     0xda47bc: ldr             x3, [x3, #0x40]
    // 0xda47c0: blr             x9
    // 0xda47c4: ldur            x0, [fp, #-0x20]
    // 0xda47c8: ldur            x1, [fp, #-8]
    // 0xda47cc: StoreField: r1->field_f = r0
    //     0xda47cc: stur            w0, [x1, #0xf]
    //     0xda47d0: ldurb           w16, [x1, #-1]
    //     0xda47d4: ldurb           w17, [x0, #-1]
    //     0xda47d8: and             x16, x17, x16, lsr #2
    //     0xda47dc: tst             x16, HEAP, lsr #32
    //     0xda47e0: b.eq            #0xda47e8
    //     0xda47e4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda47e8: ldur            x0, [fp, #-0x30]
    // 0xda47ec: LoadField: r3 = r0->field_7
    //     0xda47ec: ldur            w3, [x0, #7]
    // 0xda47f0: DecompressPointer r3
    //     0xda47f0: add             x3, x3, HEAP, lsl #32
    // 0xda47f4: stur            x3, [fp, #-8]
    // 0xda47f8: cmp             w3, NULL
    // 0xda47fc: b.eq            #0xda491c
    // 0xda4800: mov             x0, x3
    // 0xda4804: r2 = Null
    //     0xda4804: mov             x2, NULL
    // 0xda4808: r1 = Null
    //     0xda4808: mov             x1, NULL
    // 0xda480c: r4 = LoadClassIdInstr(r0)
    //     0xda480c: ldur            x4, [x0, #-1]
    //     0xda4810: ubfx            x4, x4, #0xc, #0x14
    // 0xda4814: cmp             x4, #0xc7e
    // 0xda4818: b.eq            #0xda4830
    // 0xda481c: r8 = FlexParentData
    //     0xda481c: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0xda4820: ldr             x8, [x8, #0x590]
    // 0xda4824: r3 = Null
    //     0xda4824: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f050] Null
    //     0xda4828: ldr             x3, [x3, #0x50]
    // 0xda482c: r0 = DefaultTypeTest()
    //     0xda482c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda4830: ldur            x3, [fp, #-0x28]
    // 0xda4834: LoadField: r2 = r3->field_b
    //     0xda4834: ldur            w2, [x3, #0xb]
    // 0xda4838: DecompressPointer r2
    //     0xda4838: add             x2, x2, HEAP, lsl #32
    // 0xda483c: ldur            x0, [fp, #-0x18]
    // 0xda4840: r1 = Null
    //     0xda4840: mov             x1, NULL
    // 0xda4844: cmp             w0, NULL
    // 0xda4848: b.eq            #0xda4874
    // 0xda484c: cmp             w2, NULL
    // 0xda4850: b.eq            #0xda4874
    // 0xda4854: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda4854: ldur            w4, [x2, #0x17]
    // 0xda4858: DecompressPointer r4
    //     0xda4858: add             x4, x4, HEAP, lsl #32
    // 0xda485c: r8 = X0? bound RenderObject
    //     0xda485c: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda4860: ldr             x8, [x8, #0x1a8]
    // 0xda4864: LoadField: r9 = r4->field_7
    //     0xda4864: ldur            x9, [x4, #7]
    // 0xda4868: r3 = Null
    //     0xda4868: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f060] Null
    //     0xda486c: ldr             x3, [x3, #0x60]
    // 0xda4870: blr             x9
    // 0xda4874: ldur            x0, [fp, #-0x18]
    // 0xda4878: ldur            x1, [fp, #-0x28]
    // 0xda487c: StoreField: r1->field_13 = r0
    //     0xda487c: stur            w0, [x1, #0x13]
    //     0xda4880: ldurb           w16, [x1, #-1]
    //     0xda4884: ldurb           w17, [x0, #-1]
    //     0xda4888: and             x16, x17, x16, lsr #2
    //     0xda488c: tst             x16, HEAP, lsr #32
    //     0xda4890: b.eq            #0xda4898
    //     0xda4894: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda4898: ldur            x3, [fp, #-8]
    // 0xda489c: LoadField: r2 = r3->field_b
    //     0xda489c: ldur            w2, [x3, #0xb]
    // 0xda48a0: DecompressPointer r2
    //     0xda48a0: add             x2, x2, HEAP, lsl #32
    // 0xda48a4: ldur            x0, [fp, #-0x18]
    // 0xda48a8: r1 = Null
    //     0xda48a8: mov             x1, NULL
    // 0xda48ac: cmp             w0, NULL
    // 0xda48b0: b.eq            #0xda48dc
    // 0xda48b4: cmp             w2, NULL
    // 0xda48b8: b.eq            #0xda48dc
    // 0xda48bc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda48bc: ldur            w4, [x2, #0x17]
    // 0xda48c0: DecompressPointer r4
    //     0xda48c0: add             x4, x4, HEAP, lsl #32
    // 0xda48c4: r8 = X0? bound RenderObject
    //     0xda48c4: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda48c8: ldr             x8, [x8, #0x1a8]
    // 0xda48cc: LoadField: r9 = r4->field_7
    //     0xda48cc: ldur            x9, [x4, #7]
    // 0xda48d0: r3 = Null
    //     0xda48d0: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f070] Null
    //     0xda48d4: ldr             x3, [x3, #0x70]
    // 0xda48d8: blr             x9
    // 0xda48dc: ldur            x0, [fp, #-0x18]
    // 0xda48e0: ldur            x1, [fp, #-8]
    // 0xda48e4: StoreField: r1->field_f = r0
    //     0xda48e4: stur            w0, [x1, #0xf]
    //     0xda48e8: ldurb           w16, [x1, #-1]
    //     0xda48ec: ldurb           w17, [x0, #-1]
    //     0xda48f0: and             x16, x17, x16, lsr #2
    //     0xda48f4: tst             x16, HEAP, lsr #32
    //     0xda48f8: b.eq            #0xda4900
    //     0xda48fc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda4900: r0 = Null
    //     0xda4900: mov             x0, NULL
    // 0xda4904: LeaveFrame
    //     0xda4904: mov             SP, fp
    //     0xda4908: ldp             fp, lr, [SP], #0x10
    // 0xda490c: ret
    //     0xda490c: ret             
    // 0xda4910: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda4910: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda4914: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda4914: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda4918: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda4918: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda491c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda491c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3053, size: 0x68, field offset: 0x68
//   transformed mixin,
abstract class _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin extends _RenderFlex&RenderBox&ContainerRenderObjectMixin
     with RenderBoxContainerDefaultsMixin<X0 bound RenderBox, X1 bound ContainerBoxParentData> {

  _ defaultComputeDistanceToFirstActualBaseline(/* No info */) {
    // ** addr: 0x74c054, size: 0x1e8
    // 0x74c054: EnterFrame
    //     0x74c054: stp             fp, lr, [SP, #-0x10]!
    //     0x74c058: mov             fp, SP
    // 0x74c05c: AllocStack(0x50)
    //     0x74c05c: sub             SP, SP, #0x50
    // 0x74c060: SetupParameters(dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x74c060: mov             x3, x2
    //     0x74c064: stur            x2, [fp, #-0x18]
    // 0x74c068: CheckStackOverflow
    //     0x74c068: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74c06c: cmp             SP, x16
    //     0x74c070: b.ls            #0x74c218
    // 0x74c074: LoadField: r0 = r1->field_5f
    //     0x74c074: ldur            w0, [x1, #0x5f]
    // 0x74c078: DecompressPointer r0
    //     0x74c078: add             x0, x0, HEAP, lsl #32
    // 0x74c07c: mov             x4, x0
    // 0x74c080: stur            x4, [fp, #-0x10]
    // 0x74c084: CheckStackOverflow
    //     0x74c084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74c088: cmp             SP, x16
    //     0x74c08c: b.ls            #0x74c220
    // 0x74c090: cmp             w4, NULL
    // 0x74c094: b.eq            #0x74c1ec
    // 0x74c098: LoadField: r5 = r4->field_7
    //     0x74c098: ldur            w5, [x4, #7]
    // 0x74c09c: DecompressPointer r5
    //     0x74c09c: add             x5, x5, HEAP, lsl #32
    // 0x74c0a0: stur            x5, [fp, #-8]
    // 0x74c0a4: cmp             w5, NULL
    // 0x74c0a8: b.eq            #0x74c228
    // 0x74c0ac: mov             x0, x5
    // 0x74c0b0: r2 = Null
    //     0x74c0b0: mov             x2, NULL
    // 0x74c0b4: r1 = Null
    //     0x74c0b4: mov             x1, NULL
    // 0x74c0b8: r4 = LoadClassIdInstr(r0)
    //     0x74c0b8: ldur            x4, [x0, #-1]
    //     0x74c0bc: ubfx            x4, x4, #0xc, #0x14
    // 0x74c0c0: cmp             x4, #0xc7e
    // 0x74c0c4: b.eq            #0x74c0dc
    // 0x74c0c8: r8 = FlexParentData
    //     0x74c0c8: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x74c0cc: ldr             x8, [x8, #0x590]
    // 0x74c0d0: r3 = Null
    //     0x74c0d0: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4ef50] Null
    //     0x74c0d4: ldr             x3, [x3, #0xf50]
    // 0x74c0d8: r0 = DefaultTypeTest()
    //     0x74c0d8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x74c0dc: r1 = 1
    //     0x74c0dc: movz            x1, #0x1
    // 0x74c0e0: r0 = AllocateContext()
    //     0x74c0e0: bl              #0xec126c  ; AllocateContextStub
    // 0x74c0e4: mov             x4, x0
    // 0x74c0e8: ldur            x3, [fp, #-0x10]
    // 0x74c0ec: stur            x4, [fp, #-0x28]
    // 0x74c0f0: StoreField: r4->field_f = r3
    //     0x74c0f0: stur            w3, [x4, #0xf]
    // 0x74c0f4: LoadField: r5 = r3->field_27
    //     0x74c0f4: ldur            w5, [x3, #0x27]
    // 0x74c0f8: DecompressPointer r5
    //     0x74c0f8: add             x5, x5, HEAP, lsl #32
    // 0x74c0fc: stur            x5, [fp, #-0x20]
    // 0x74c100: cmp             w5, NULL
    // 0x74c104: b.eq            #0x74c1fc
    // 0x74c108: mov             x0, x5
    // 0x74c10c: r2 = Null
    //     0x74c10c: mov             x2, NULL
    // 0x74c110: r1 = Null
    //     0x74c110: mov             x1, NULL
    // 0x74c114: r4 = LoadClassIdInstr(r0)
    //     0x74c114: ldur            x4, [x0, #-1]
    //     0x74c118: ubfx            x4, x4, #0xc, #0x14
    // 0x74c11c: sub             x4, x4, #0xc83
    // 0x74c120: cmp             x4, #1
    // 0x74c124: b.ls            #0x74c138
    // 0x74c128: r8 = BoxConstraints
    //     0x74c128: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x74c12c: r3 = Null
    //     0x74c12c: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4ef60] Null
    //     0x74c130: ldr             x3, [x3, #0xf60]
    // 0x74c134: r0 = BoxConstraints()
    //     0x74c134: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x74c138: ldur            x2, [fp, #-0x20]
    // 0x74c13c: ldur            x3, [fp, #-0x18]
    // 0x74c140: r0 = AllocateRecord2()
    //     0x74c140: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x74c144: ldur            x2, [fp, #-0x28]
    // 0x74c148: r1 = Function '<anonymous closure>':.
    //     0x74c148: add             x1, PP, #0x45, lsl #12  ; [pp+0x456d0] AnonymousClosure: (0x74b7cc), in [package:flutter/src/rendering/box.dart] RenderBox::getDistanceToActualBaseline (0x74b4d4)
    //     0x74c14c: ldr             x1, [x1, #0x6d0]
    // 0x74c150: stur            x0, [fp, #-0x20]
    // 0x74c154: r0 = AllocateClosure()
    //     0x74c154: bl              #0xec1630  ; AllocateClosureStub
    // 0x74c158: r16 = <(BoxConstraints, TextBaseline), double?>
    //     0x74c158: add             x16, PP, #0x45, lsl #12  ; [pp+0x456d8] TypeArguments: <(BoxConstraints, TextBaseline), double?>
    //     0x74c15c: ldr             x16, [x16, #0x6d8]
    // 0x74c160: ldur            lr, [fp, #-0x10]
    // 0x74c164: stp             lr, x16, [SP, #0x18]
    // 0x74c168: r16 = Instance__Baseline
    //     0x74c168: add             x16, PP, #0x45, lsl #12  ; [pp+0x456e0] Obj!_Baseline@e117c1
    //     0x74c16c: ldr             x16, [x16, #0x6e0]
    // 0x74c170: ldur            lr, [fp, #-0x20]
    // 0x74c174: stp             lr, x16, [SP, #8]
    // 0x74c178: str             x0, [SP]
    // 0x74c17c: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x74c17c: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x74c180: r0 = _computeIntrinsics()
    //     0x74c180: bl              #0x72d344  ; [package:flutter/src/rendering/box.dart] RenderBox::_computeIntrinsics
    // 0x74c184: cmp             w0, NULL
    // 0x74c188: b.ne            #0x74c1a0
    // 0x74c18c: ldur            x1, [fp, #-8]
    // 0x74c190: LoadField: r4 = r1->field_13
    //     0x74c190: ldur            w4, [x1, #0x13]
    // 0x74c194: DecompressPointer r4
    //     0x74c194: add             x4, x4, HEAP, lsl #32
    // 0x74c198: ldur            x3, [fp, #-0x18]
    // 0x74c19c: b               #0x74c080
    // 0x74c1a0: ldur            x1, [fp, #-8]
    // 0x74c1a4: LoadField: r2 = r1->field_7
    //     0x74c1a4: ldur            w2, [x1, #7]
    // 0x74c1a8: DecompressPointer r2
    //     0x74c1a8: add             x2, x2, HEAP, lsl #32
    // 0x74c1ac: LoadField: d0 = r2->field_f
    //     0x74c1ac: ldur            d0, [x2, #0xf]
    // 0x74c1b0: LoadField: d1 = r0->field_7
    //     0x74c1b0: ldur            d1, [x0, #7]
    // 0x74c1b4: fadd            d2, d1, d0
    // 0x74c1b8: r0 = inline_Allocate_Double()
    //     0x74c1b8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74c1bc: add             x0, x0, #0x10
    //     0x74c1c0: cmp             x1, x0
    //     0x74c1c4: b.ls            #0x74c22c
    //     0x74c1c8: str             x0, [THR, #0x50]  ; THR::top
    //     0x74c1cc: sub             x0, x0, #0xf
    //     0x74c1d0: movz            x1, #0xe15c
    //     0x74c1d4: movk            x1, #0x3, lsl #16
    //     0x74c1d8: stur            x1, [x0, #-1]
    // 0x74c1dc: StoreField: r0->field_7 = d2
    //     0x74c1dc: stur            d2, [x0, #7]
    // 0x74c1e0: LeaveFrame
    //     0x74c1e0: mov             SP, fp
    //     0x74c1e4: ldp             fp, lr, [SP], #0x10
    // 0x74c1e8: ret
    //     0x74c1e8: ret             
    // 0x74c1ec: r0 = Null
    //     0x74c1ec: mov             x0, NULL
    // 0x74c1f0: LeaveFrame
    //     0x74c1f0: mov             SP, fp
    //     0x74c1f4: ldp             fp, lr, [SP], #0x10
    // 0x74c1f8: ret
    //     0x74c1f8: ret             
    // 0x74c1fc: r0 = StateError()
    //     0x74c1fc: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x74c200: mov             x1, x0
    // 0x74c204: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x74c204: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x74c208: StoreField: r1->field_b = r0
    //     0x74c208: stur            w0, [x1, #0xb]
    // 0x74c20c: mov             x0, x1
    // 0x74c210: r0 = Throw()
    //     0x74c210: bl              #0xec04b8  ; ThrowStub
    // 0x74c214: brk             #0
    // 0x74c218: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74c218: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74c21c: b               #0x74c074
    // 0x74c220: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74c220: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74c224: b               #0x74c090
    // 0x74c228: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74c228: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x74c22c: SaveReg d2
    //     0x74c22c: str             q2, [SP, #-0x10]!
    // 0x74c230: r0 = AllocateDouble()
    //     0x74c230: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74c234: RestoreReg d2
    //     0x74c234: ldr             q2, [SP], #0x10
    // 0x74c238: b               #0x74c1dc
  }
  _ defaultComputeDistanceToHighestActualBaseline(/* No info */) {
    // ** addr: 0x74c23c, size: 0x2f0
    // 0x74c23c: EnterFrame
    //     0x74c23c: stp             fp, lr, [SP, #-0x10]!
    //     0x74c240: mov             fp, SP
    // 0x74c244: AllocStack(0x58)
    //     0x74c244: sub             SP, SP, #0x58
    // 0x74c248: SetupParameters(dynamic _ /* r2 => r3, fp-0x20 */)
    //     0x74c248: mov             x3, x2
    //     0x74c24c: stur            x2, [fp, #-0x20]
    // 0x74c250: CheckStackOverflow
    //     0x74c250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74c254: cmp             SP, x16
    //     0x74c258: b.ls            #0x74c4e0
    // 0x74c25c: LoadField: r0 = r1->field_5f
    //     0x74c25c: ldur            w0, [x1, #0x5f]
    // 0x74c260: DecompressPointer r0
    //     0x74c260: add             x0, x0, HEAP, lsl #32
    // 0x74c264: mov             x4, x0
    // 0x74c268: r5 = Null
    //     0x74c268: mov             x5, NULL
    // 0x74c26c: stur            x5, [fp, #-0x10]
    // 0x74c270: stur            x4, [fp, #-0x18]
    // 0x74c274: CheckStackOverflow
    //     0x74c274: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74c278: cmp             SP, x16
    //     0x74c27c: b.ls            #0x74c4e8
    // 0x74c280: cmp             w4, NULL
    // 0x74c284: b.eq            #0x74c4b0
    // 0x74c288: LoadField: r6 = r4->field_7
    //     0x74c288: ldur            w6, [x4, #7]
    // 0x74c28c: DecompressPointer r6
    //     0x74c28c: add             x6, x6, HEAP, lsl #32
    // 0x74c290: stur            x6, [fp, #-8]
    // 0x74c294: cmp             w6, NULL
    // 0x74c298: b.eq            #0x74c4f0
    // 0x74c29c: mov             x0, x6
    // 0x74c2a0: r2 = Null
    //     0x74c2a0: mov             x2, NULL
    // 0x74c2a4: r1 = Null
    //     0x74c2a4: mov             x1, NULL
    // 0x74c2a8: r4 = LoadClassIdInstr(r0)
    //     0x74c2a8: ldur            x4, [x0, #-1]
    //     0x74c2ac: ubfx            x4, x4, #0xc, #0x14
    // 0x74c2b0: cmp             x4, #0xc7e
    // 0x74c2b4: b.eq            #0x74c2cc
    // 0x74c2b8: r8 = FlexParentData
    //     0x74c2b8: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x74c2bc: ldr             x8, [x8, #0x590]
    // 0x74c2c0: r3 = Null
    //     0x74c2c0: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4ef70] Null
    //     0x74c2c4: ldr             x3, [x3, #0xf70]
    // 0x74c2c8: r0 = DefaultTypeTest()
    //     0x74c2c8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x74c2cc: r1 = 1
    //     0x74c2cc: movz            x1, #0x1
    // 0x74c2d0: r0 = AllocateContext()
    //     0x74c2d0: bl              #0xec126c  ; AllocateContextStub
    // 0x74c2d4: mov             x4, x0
    // 0x74c2d8: ldur            x3, [fp, #-0x18]
    // 0x74c2dc: stur            x4, [fp, #-0x30]
    // 0x74c2e0: StoreField: r4->field_f = r3
    //     0x74c2e0: stur            w3, [x4, #0xf]
    // 0x74c2e4: LoadField: r5 = r3->field_27
    //     0x74c2e4: ldur            w5, [x3, #0x27]
    // 0x74c2e8: DecompressPointer r5
    //     0x74c2e8: add             x5, x5, HEAP, lsl #32
    // 0x74c2ec: stur            x5, [fp, #-0x28]
    // 0x74c2f0: cmp             w5, NULL
    // 0x74c2f4: b.eq            #0x74c4c4
    // 0x74c2f8: ldur            x6, [fp, #-8]
    // 0x74c2fc: mov             x0, x5
    // 0x74c300: r2 = Null
    //     0x74c300: mov             x2, NULL
    // 0x74c304: r1 = Null
    //     0x74c304: mov             x1, NULL
    // 0x74c308: r4 = LoadClassIdInstr(r0)
    //     0x74c308: ldur            x4, [x0, #-1]
    //     0x74c30c: ubfx            x4, x4, #0xc, #0x14
    // 0x74c310: sub             x4, x4, #0xc83
    // 0x74c314: cmp             x4, #1
    // 0x74c318: b.ls            #0x74c32c
    // 0x74c31c: r8 = BoxConstraints
    //     0x74c31c: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x74c320: r3 = Null
    //     0x74c320: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4ef80] Null
    //     0x74c324: ldr             x3, [x3, #0xf80]
    // 0x74c328: r0 = BoxConstraints()
    //     0x74c328: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x74c32c: ldur            x2, [fp, #-0x28]
    // 0x74c330: ldur            x3, [fp, #-0x20]
    // 0x74c334: r0 = AllocateRecord2()
    //     0x74c334: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x74c338: ldur            x2, [fp, #-0x30]
    // 0x74c33c: r1 = Function '<anonymous closure>':.
    //     0x74c33c: add             x1, PP, #0x45, lsl #12  ; [pp+0x456d0] AnonymousClosure: (0x74b7cc), in [package:flutter/src/rendering/box.dart] RenderBox::getDistanceToActualBaseline (0x74b4d4)
    //     0x74c340: ldr             x1, [x1, #0x6d0]
    // 0x74c344: stur            x0, [fp, #-0x28]
    // 0x74c348: r0 = AllocateClosure()
    //     0x74c348: bl              #0xec1630  ; AllocateClosureStub
    // 0x74c34c: r16 = <(BoxConstraints, TextBaseline), double?>
    //     0x74c34c: add             x16, PP, #0x45, lsl #12  ; [pp+0x456d8] TypeArguments: <(BoxConstraints, TextBaseline), double?>
    //     0x74c350: ldr             x16, [x16, #0x6d8]
    // 0x74c354: ldur            lr, [fp, #-0x18]
    // 0x74c358: stp             lr, x16, [SP, #0x18]
    // 0x74c35c: r16 = Instance__Baseline
    //     0x74c35c: add             x16, PP, #0x45, lsl #12  ; [pp+0x456e0] Obj!_Baseline@e117c1
    //     0x74c360: ldr             x16, [x16, #0x6e0]
    // 0x74c364: ldur            lr, [fp, #-0x28]
    // 0x74c368: stp             lr, x16, [SP, #8]
    // 0x74c36c: str             x0, [SP]
    // 0x74c370: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x74c370: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x74c374: r0 = _computeIntrinsics()
    //     0x74c374: bl              #0x72d344  ; [package:flutter/src/rendering/box.dart] RenderBox::_computeIntrinsics
    // 0x74c378: mov             x1, x0
    // 0x74c37c: ldur            x0, [fp, #-8]
    // 0x74c380: LoadField: r2 = r0->field_7
    //     0x74c380: ldur            w2, [x0, #7]
    // 0x74c384: DecompressPointer r2
    //     0x74c384: add             x2, x2, HEAP, lsl #32
    // 0x74c388: LoadField: d0 = r2->field_f
    //     0x74c388: ldur            d0, [x2, #0xf]
    // 0x74c38c: cmp             w1, NULL
    // 0x74c390: b.ne            #0x74c39c
    // 0x74c394: r2 = Null
    //     0x74c394: mov             x2, NULL
    // 0x74c398: b               #0x74c3d0
    // 0x74c39c: LoadField: d1 = r1->field_7
    //     0x74c39c: ldur            d1, [x1, #7]
    // 0x74c3a0: fadd            d2, d1, d0
    // 0x74c3a4: r1 = inline_Allocate_Double()
    //     0x74c3a4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x74c3a8: add             x1, x1, #0x10
    //     0x74c3ac: cmp             x2, x1
    //     0x74c3b0: b.ls            #0x74c4f4
    //     0x74c3b4: str             x1, [THR, #0x50]  ; THR::top
    //     0x74c3b8: sub             x1, x1, #0xf
    //     0x74c3bc: movz            x2, #0xe15c
    //     0x74c3c0: movk            x2, #0x3, lsl #16
    //     0x74c3c4: stur            x2, [x1, #-1]
    // 0x74c3c8: StoreField: r1->field_7 = d2
    //     0x74c3c8: stur            d2, [x1, #7]
    // 0x74c3cc: mov             x2, x1
    // 0x74c3d0: ldur            x1, [fp, #-0x10]
    // 0x74c3d4: cmp             w1, NULL
    // 0x74c3d8: b.eq            #0x74c438
    // 0x74c3dc: cmp             w2, NULL
    // 0x74c3e0: b.eq            #0x74c430
    // 0x74c3e4: LoadField: d0 = r1->field_7
    //     0x74c3e4: ldur            d0, [x1, #7]
    // 0x74c3e8: LoadField: d1 = r2->field_7
    //     0x74c3e8: ldur            d1, [x2, #7]
    // 0x74c3ec: fcmp            d0, d1
    // 0x74c3f0: b.lt            #0x74c3fc
    // 0x74c3f4: LoadField: d0 = r2->field_7
    //     0x74c3f4: ldur            d0, [x2, #7]
    // 0x74c3f8: b               #0x74c400
    // 0x74c3fc: LoadField: d0 = r1->field_7
    //     0x74c3fc: ldur            d0, [x1, #7]
    // 0x74c400: r1 = inline_Allocate_Double()
    //     0x74c400: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x74c404: add             x1, x1, #0x10
    //     0x74c408: cmp             x2, x1
    //     0x74c40c: b.ls            #0x74c510
    //     0x74c410: str             x1, [THR, #0x50]  ; THR::top
    //     0x74c414: sub             x1, x1, #0xf
    //     0x74c418: movz            x2, #0xe15c
    //     0x74c41c: movk            x2, #0x3, lsl #16
    //     0x74c420: stur            x2, [x1, #-1]
    // 0x74c424: StoreField: r1->field_7 = d0
    //     0x74c424: stur            d0, [x1, #7]
    // 0x74c428: mov             x5, x1
    // 0x74c42c: b               #0x74c4a0
    // 0x74c430: r3 = true
    //     0x74c430: add             x3, NULL, #0x20  ; true
    // 0x74c434: b               #0x74c43c
    // 0x74c438: r3 = false
    //     0x74c438: add             x3, NULL, #0x30  ; false
    // 0x74c43c: cmp             w1, NULL
    // 0x74c440: b.eq            #0x74c478
    // 0x74c444: tbnz            w3, #4, #0x74c454
    // 0x74c448: r4 = Null
    //     0x74c448: mov             x4, NULL
    // 0x74c44c: r3 = Null
    //     0x74c44c: mov             x3, NULL
    // 0x74c450: b               #0x74c45c
    // 0x74c454: mov             x4, x2
    // 0x74c458: mov             x3, x2
    // 0x74c45c: cmp             w4, NULL
    // 0x74c460: b.ne            #0x74c46c
    // 0x74c464: mov             x5, x1
    // 0x74c468: b               #0x74c4a0
    // 0x74c46c: mov             x5, x3
    // 0x74c470: r3 = true
    //     0x74c470: add             x3, NULL, #0x20  ; true
    // 0x74c474: b               #0x74c47c
    // 0x74c478: r5 = Null
    //     0x74c478: mov             x5, NULL
    // 0x74c47c: cmp             w1, NULL
    // 0x74c480: b.ne            #0x74c49c
    // 0x74c484: tbnz            w3, #4, #0x74c490
    // 0x74c488: mov             x1, x5
    // 0x74c48c: b               #0x74c494
    // 0x74c490: mov             x1, x2
    // 0x74c494: mov             x5, x1
    // 0x74c498: b               #0x74c4a0
    // 0x74c49c: r5 = Null
    //     0x74c49c: mov             x5, NULL
    // 0x74c4a0: LoadField: r4 = r0->field_13
    //     0x74c4a0: ldur            w4, [x0, #0x13]
    // 0x74c4a4: DecompressPointer r4
    //     0x74c4a4: add             x4, x4, HEAP, lsl #32
    // 0x74c4a8: ldur            x3, [fp, #-0x20]
    // 0x74c4ac: b               #0x74c26c
    // 0x74c4b0: mov             x1, x5
    // 0x74c4b4: mov             x0, x1
    // 0x74c4b8: LeaveFrame
    //     0x74c4b8: mov             SP, fp
    //     0x74c4bc: ldp             fp, lr, [SP], #0x10
    // 0x74c4c0: ret
    //     0x74c4c0: ret             
    // 0x74c4c4: r0 = StateError()
    //     0x74c4c4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x74c4c8: mov             x1, x0
    // 0x74c4cc: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x74c4cc: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x74c4d0: StoreField: r1->field_b = r0
    //     0x74c4d0: stur            w0, [x1, #0xb]
    // 0x74c4d4: mov             x0, x1
    // 0x74c4d8: r0 = Throw()
    //     0x74c4d8: bl              #0xec04b8  ; ThrowStub
    // 0x74c4dc: brk             #0
    // 0x74c4e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74c4e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74c4e4: b               #0x74c25c
    // 0x74c4e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74c4e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74c4ec: b               #0x74c280
    // 0x74c4f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74c4f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x74c4f4: SaveReg d2
    //     0x74c4f4: str             q2, [SP, #-0x10]!
    // 0x74c4f8: SaveReg r0
    //     0x74c4f8: str             x0, [SP, #-8]!
    // 0x74c4fc: r0 = AllocateDouble()
    //     0x74c4fc: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74c500: mov             x1, x0
    // 0x74c504: RestoreReg r0
    //     0x74c504: ldr             x0, [SP], #8
    // 0x74c508: RestoreReg d2
    //     0x74c508: ldr             q2, [SP], #0x10
    // 0x74c50c: b               #0x74c3c8
    // 0x74c510: SaveReg d0
    //     0x74c510: str             q0, [SP, #-0x10]!
    // 0x74c514: SaveReg r0
    //     0x74c514: str             x0, [SP, #-8]!
    // 0x74c518: r0 = AllocateDouble()
    //     0x74c518: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74c51c: mov             x1, x0
    // 0x74c520: RestoreReg r0
    //     0x74c520: ldr             x0, [SP], #8
    // 0x74c524: RestoreReg d0
    //     0x74c524: ldr             q0, [SP], #0x10
    // 0x74c528: b               #0x74c424
  }
  _ defaultPaint(/* No info */) {
    // ** addr: 0x799544, size: 0x128
    // 0x799544: EnterFrame
    //     0x799544: stp             fp, lr, [SP, #-0x10]!
    //     0x799548: mov             fp, SP
    // 0x79954c: AllocStack(0x38)
    //     0x79954c: sub             SP, SP, #0x38
    // 0x799550: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */)
    //     0x799550: mov             x4, x2
    //     0x799554: stur            x2, [fp, #-0x18]
    // 0x799558: CheckStackOverflow
    //     0x799558: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79955c: cmp             SP, x16
    //     0x799560: b.ls            #0x799658
    // 0x799564: LoadField: r0 = r1->field_5f
    //     0x799564: ldur            w0, [x1, #0x5f]
    // 0x799568: DecompressPointer r0
    //     0x799568: add             x0, x0, HEAP, lsl #32
    // 0x79956c: LoadField: d0 = r3->field_7
    //     0x79956c: ldur            d0, [x3, #7]
    // 0x799570: stur            d0, [fp, #-0x28]
    // 0x799574: LoadField: d1 = r3->field_f
    //     0x799574: ldur            d1, [x3, #0xf]
    // 0x799578: stur            d1, [fp, #-0x20]
    // 0x79957c: mov             x3, x0
    // 0x799580: stur            x3, [fp, #-0x10]
    // 0x799584: CheckStackOverflow
    //     0x799584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x799588: cmp             SP, x16
    //     0x79958c: b.ls            #0x799660
    // 0x799590: cmp             w3, NULL
    // 0x799594: b.eq            #0x799648
    // 0x799598: LoadField: r5 = r3->field_7
    //     0x799598: ldur            w5, [x3, #7]
    // 0x79959c: DecompressPointer r5
    //     0x79959c: add             x5, x5, HEAP, lsl #32
    // 0x7995a0: stur            x5, [fp, #-8]
    // 0x7995a4: cmp             w5, NULL
    // 0x7995a8: b.eq            #0x799668
    // 0x7995ac: mov             x0, x5
    // 0x7995b0: r2 = Null
    //     0x7995b0: mov             x2, NULL
    // 0x7995b4: r1 = Null
    //     0x7995b4: mov             x1, NULL
    // 0x7995b8: r4 = LoadClassIdInstr(r0)
    //     0x7995b8: ldur            x4, [x0, #-1]
    //     0x7995bc: ubfx            x4, x4, #0xc, #0x14
    // 0x7995c0: cmp             x4, #0xc7e
    // 0x7995c4: b.eq            #0x7995dc
    // 0x7995c8: r8 = FlexParentData
    //     0x7995c8: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x7995cc: ldr             x8, [x8, #0x590]
    // 0x7995d0: r3 = Null
    //     0x7995d0: add             x3, PP, #0x45, lsl #12  ; [pp+0x455f0] Null
    //     0x7995d4: ldr             x3, [x3, #0x5f0]
    // 0x7995d8: r0 = DefaultTypeTest()
    //     0x7995d8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7995dc: ldur            x0, [fp, #-8]
    // 0x7995e0: LoadField: r1 = r0->field_7
    //     0x7995e0: ldur            w1, [x0, #7]
    // 0x7995e4: DecompressPointer r1
    //     0x7995e4: add             x1, x1, HEAP, lsl #32
    // 0x7995e8: LoadField: d0 = r1->field_7
    //     0x7995e8: ldur            d0, [x1, #7]
    // 0x7995ec: ldur            d1, [fp, #-0x28]
    // 0x7995f0: fadd            d2, d0, d1
    // 0x7995f4: stur            d2, [fp, #-0x38]
    // 0x7995f8: LoadField: d0 = r1->field_f
    //     0x7995f8: ldur            d0, [x1, #0xf]
    // 0x7995fc: ldur            d3, [fp, #-0x20]
    // 0x799600: fadd            d4, d0, d3
    // 0x799604: stur            d4, [fp, #-0x30]
    // 0x799608: r0 = Offset()
    //     0x799608: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x79960c: ldur            d0, [fp, #-0x38]
    // 0x799610: StoreField: r0->field_7 = d0
    //     0x799610: stur            d0, [x0, #7]
    // 0x799614: ldur            d0, [fp, #-0x30]
    // 0x799618: StoreField: r0->field_f = d0
    //     0x799618: stur            d0, [x0, #0xf]
    // 0x79961c: ldur            x1, [fp, #-0x18]
    // 0x799620: ldur            x2, [fp, #-0x10]
    // 0x799624: mov             x3, x0
    // 0x799628: r0 = paintChild()
    //     0x799628: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x79962c: ldur            x1, [fp, #-8]
    // 0x799630: LoadField: r3 = r1->field_13
    //     0x799630: ldur            w3, [x1, #0x13]
    // 0x799634: DecompressPointer r3
    //     0x799634: add             x3, x3, HEAP, lsl #32
    // 0x799638: ldur            x4, [fp, #-0x18]
    // 0x79963c: ldur            d0, [fp, #-0x28]
    // 0x799640: ldur            d1, [fp, #-0x20]
    // 0x799644: b               #0x799580
    // 0x799648: r0 = Null
    //     0x799648: mov             x0, NULL
    // 0x79964c: LeaveFrame
    //     0x79964c: mov             SP, fp
    //     0x799650: ldp             fp, lr, [SP], #0x10
    // 0x799654: ret
    //     0x799654: ret             
    // 0x799658: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x799658: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x79965c: b               #0x799564
    // 0x799660: r0 = StackOverflowSharedWithFPURegs()
    //     0x799660: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x799664: b               #0x799590
    // 0x799668: r0 = NullCastErrorSharedWithFPURegs()
    //     0x799668: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  [closure] void defaultPaint(dynamic, PaintingContext, Offset) {
    // ** addr: 0x79966c, size: 0x40
    // 0x79966c: EnterFrame
    //     0x79966c: stp             fp, lr, [SP, #-0x10]!
    //     0x799670: mov             fp, SP
    // 0x799674: ldr             x0, [fp, #0x20]
    // 0x799678: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x799678: ldur            w1, [x0, #0x17]
    // 0x79967c: DecompressPointer r1
    //     0x79967c: add             x1, x1, HEAP, lsl #32
    // 0x799680: CheckStackOverflow
    //     0x799680: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x799684: cmp             SP, x16
    //     0x799688: b.ls            #0x7996a4
    // 0x79968c: ldr             x2, [fp, #0x18]
    // 0x799690: ldr             x3, [fp, #0x10]
    // 0x799694: r0 = defaultPaint()
    //     0x799694: bl              #0x799544  ; [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultPaint
    // 0x799698: LeaveFrame
    //     0x799698: mov             SP, fp
    //     0x79969c: ldp             fp, lr, [SP], #0x10
    // 0x7996a0: ret
    //     0x7996a0: ret             
    // 0x7996a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7996a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7996a8: b               #0x79968c
  }
  _ defaultHitTestChildren(/* No info */) {
    // ** addr: 0x7fe3a0, size: 0x144
    // 0x7fe3a0: EnterFrame
    //     0x7fe3a0: stp             fp, lr, [SP, #-0x10]!
    //     0x7fe3a4: mov             fp, SP
    // 0x7fe3a8: AllocStack(0x28)
    //     0x7fe3a8: sub             SP, SP, #0x28
    // 0x7fe3ac: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7fe3ac: mov             x4, x2
    //     0x7fe3b0: stur            x2, [fp, #-0x18]
    //     0x7fe3b4: stur            x3, [fp, #-0x20]
    // 0x7fe3b8: CheckStackOverflow
    //     0x7fe3b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fe3bc: cmp             SP, x16
    //     0x7fe3c0: b.ls            #0x7fe4d0
    // 0x7fe3c4: LoadField: r0 = r1->field_63
    //     0x7fe3c4: ldur            w0, [x1, #0x63]
    // 0x7fe3c8: DecompressPointer r0
    //     0x7fe3c8: add             x0, x0, HEAP, lsl #32
    // 0x7fe3cc: mov             x5, x0
    // 0x7fe3d0: stur            x5, [fp, #-0x10]
    // 0x7fe3d4: CheckStackOverflow
    //     0x7fe3d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fe3d8: cmp             SP, x16
    //     0x7fe3dc: b.ls            #0x7fe4d8
    // 0x7fe3e0: cmp             w5, NULL
    // 0x7fe3e4: b.eq            #0x7fe4c0
    // 0x7fe3e8: LoadField: r6 = r5->field_7
    //     0x7fe3e8: ldur            w6, [x5, #7]
    // 0x7fe3ec: DecompressPointer r6
    //     0x7fe3ec: add             x6, x6, HEAP, lsl #32
    // 0x7fe3f0: stur            x6, [fp, #-8]
    // 0x7fe3f4: cmp             w6, NULL
    // 0x7fe3f8: b.eq            #0x7fe4e0
    // 0x7fe3fc: mov             x0, x6
    // 0x7fe400: r2 = Null
    //     0x7fe400: mov             x2, NULL
    // 0x7fe404: r1 = Null
    //     0x7fe404: mov             x1, NULL
    // 0x7fe408: r4 = LoadClassIdInstr(r0)
    //     0x7fe408: ldur            x4, [x0, #-1]
    //     0x7fe40c: ubfx            x4, x4, #0xc, #0x14
    // 0x7fe410: cmp             x4, #0xc7e
    // 0x7fe414: b.eq            #0x7fe42c
    // 0x7fe418: r8 = FlexParentData
    //     0x7fe418: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x7fe41c: ldr             x8, [x8, #0x590]
    // 0x7fe420: r3 = Null
    //     0x7fe420: add             x3, PP, #0x45, lsl #12  ; [pp+0x45600] Null
    //     0x7fe424: ldr             x3, [x3, #0x600]
    // 0x7fe428: r0 = DefaultTypeTest()
    //     0x7fe428: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7fe42c: ldur            x0, [fp, #-8]
    // 0x7fe430: LoadField: r3 = r0->field_7
    //     0x7fe430: ldur            w3, [x0, #7]
    // 0x7fe434: DecompressPointer r3
    //     0x7fe434: add             x3, x3, HEAP, lsl #32
    // 0x7fe438: ldur            x1, [fp, #-0x20]
    // 0x7fe43c: mov             x2, x3
    // 0x7fe440: stur            x3, [fp, #-0x28]
    // 0x7fe444: r0 = -()
    //     0x7fe444: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x7fe448: ldur            x1, [fp, #-0x28]
    // 0x7fe44c: stur            x0, [fp, #-0x28]
    // 0x7fe450: r0 = unary-()
    //     0x7fe450: bl              #0x6a58ac  ; [dart:ui] Offset::unary-
    // 0x7fe454: ldur            x1, [fp, #-0x18]
    // 0x7fe458: mov             x2, x0
    // 0x7fe45c: r0 = pushOffset()
    //     0x7fe45c: bl              #0x7faa44  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::pushOffset
    // 0x7fe460: ldur            x1, [fp, #-0x10]
    // 0x7fe464: r0 = LoadClassIdInstr(r1)
    //     0x7fe464: ldur            x0, [x1, #-1]
    //     0x7fe468: ubfx            x0, x0, #0xc, #0x14
    // 0x7fe46c: ldur            x2, [fp, #-0x18]
    // 0x7fe470: ldur            x3, [fp, #-0x28]
    // 0x7fe474: r0 = GDT[cid_x0 + 0xdf93]()
    //     0x7fe474: movz            x17, #0xdf93
    //     0x7fe478: add             lr, x0, x17
    //     0x7fe47c: ldr             lr, [x21, lr, lsl #3]
    //     0x7fe480: blr             lr
    // 0x7fe484: ldur            x1, [fp, #-0x18]
    // 0x7fe488: stur            x0, [fp, #-0x10]
    // 0x7fe48c: r0 = popTransform()
    //     0x7fe48c: bl              #0x7fa9a8  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::popTransform
    // 0x7fe490: ldur            x1, [fp, #-0x10]
    // 0x7fe494: tbz             w1, #4, #0x7fe4b0
    // 0x7fe498: ldur            x1, [fp, #-8]
    // 0x7fe49c: LoadField: r5 = r1->field_f
    //     0x7fe49c: ldur            w5, [x1, #0xf]
    // 0x7fe4a0: DecompressPointer r5
    //     0x7fe4a0: add             x5, x5, HEAP, lsl #32
    // 0x7fe4a4: ldur            x4, [fp, #-0x18]
    // 0x7fe4a8: ldur            x3, [fp, #-0x20]
    // 0x7fe4ac: b               #0x7fe3d0
    // 0x7fe4b0: r0 = true
    //     0x7fe4b0: add             x0, NULL, #0x20  ; true
    // 0x7fe4b4: LeaveFrame
    //     0x7fe4b4: mov             SP, fp
    //     0x7fe4b8: ldp             fp, lr, [SP], #0x10
    // 0x7fe4bc: ret
    //     0x7fe4bc: ret             
    // 0x7fe4c0: r0 = false
    //     0x7fe4c0: add             x0, NULL, #0x30  ; false
    // 0x7fe4c4: LeaveFrame
    //     0x7fe4c4: mov             SP, fp
    //     0x7fe4c8: ldp             fp, lr, [SP], #0x10
    // 0x7fe4cc: ret
    //     0x7fe4cc: ret             
    // 0x7fe4d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fe4d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fe4d4: b               #0x7fe3c4
    // 0x7fe4d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fe4d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fe4dc: b               #0x7fe3e0
    // 0x7fe4e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fe4e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3054, size: 0x6c, field offset: 0x68
//   transformed mixin,
abstract class _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin extends _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin
     with DebugOverflowIndicatorMixin {

  _ _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin(/* No info */) {
    // ** addr: 0x856340, size: 0x120
    // 0x856340: EnterFrame
    //     0x856340: stp             fp, lr, [SP, #-0x10]!
    //     0x856344: mov             fp, SP
    // 0x856348: AllocStack(0x28)
    //     0x856348: sub             SP, SP, #0x28
    // 0x85634c: SetupParameters(_RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin this /* r1 => r0, fp-0x8 */)
    //     0x85634c: mov             x0, x1
    //     0x856350: stur            x1, [fp, #-8]
    // 0x856354: CheckStackOverflow
    //     0x856354: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x856358: cmp             SP, x16
    //     0x85635c: b.ls            #0x856450
    // 0x856360: r1 = <TextPainter>
    //     0x856360: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a580] TypeArguments: <TextPainter>
    //     0x856364: ldr             x1, [x1, #0x580]
    // 0x856368: r2 = 8
    //     0x856368: movz            x2, #0x8
    // 0x85636c: r0 = AllocateArray()
    //     0x85636c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x856370: stur            x0, [fp, #-0x18]
    // 0x856374: r1 = 0
    //     0x856374: movz            x1, #0
    // 0x856378: stur            x1, [fp, #-0x10]
    // 0x85637c: CheckStackOverflow
    //     0x85637c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x856380: cmp             SP, x16
    //     0x856384: b.ls            #0x856458
    // 0x856388: cmp             x1, #4
    // 0x85638c: b.ge            #0x8563f0
    // 0x856390: r0 = TextPainter()
    //     0x856390: bl              #0x730688  ; AllocateTextPainterStub -> TextPainter (size=0x48)
    // 0x856394: stur            x0, [fp, #-0x20]
    // 0x856398: r16 = Instance_TextDirection
    //     0x856398: ldr             x16, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0x85639c: str             x16, [SP]
    // 0x8563a0: mov             x1, x0
    // 0x8563a4: r4 = const [0, 0x2, 0x1, 0x1, textDirection, 0x1, null]
    //     0x8563a4: add             x4, PP, #0x3a, lsl #12  ; [pp+0x3a588] List(7) [0, 0x2, 0x1, 0x1, "textDirection", 0x1, Null]
    //     0x8563a8: ldr             x4, [x4, #0x588]
    // 0x8563ac: r0 = TextPainter()
    //     0x8563ac: bl              #0x73024c  ; [package:flutter/src/painting/text_painter.dart] TextPainter::TextPainter
    // 0x8563b0: ldur            x1, [fp, #-0x18]
    // 0x8563b4: ldur            x0, [fp, #-0x20]
    // 0x8563b8: ldur            x2, [fp, #-0x10]
    // 0x8563bc: ArrayStore: r1[r2] = r0  ; List_4
    //     0x8563bc: add             x25, x1, x2, lsl #2
    //     0x8563c0: add             x25, x25, #0xf
    //     0x8563c4: str             w0, [x25]
    //     0x8563c8: tbz             w0, #0, #0x8563e4
    //     0x8563cc: ldurb           w16, [x1, #-1]
    //     0x8563d0: ldurb           w17, [x0, #-1]
    //     0x8563d4: and             x16, x17, x16, lsr #2
    //     0x8563d8: tst             x16, HEAP, lsr #32
    //     0x8563dc: b.eq            #0x8563e4
    //     0x8563e0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8563e4: add             x1, x2, #1
    // 0x8563e8: ldur            x0, [fp, #-0x18]
    // 0x8563ec: b               #0x856378
    // 0x8563f0: ldur            x1, [fp, #-8]
    // 0x8563f4: ldur            x0, [fp, #-0x18]
    // 0x8563f8: StoreField: r1->field_67 = r0
    //     0x8563f8: stur            w0, [x1, #0x67]
    //     0x8563fc: ldurb           w16, [x1, #-1]
    //     0x856400: ldurb           w17, [x0, #-1]
    //     0x856404: and             x16, x17, x16, lsr #2
    //     0x856408: tst             x16, HEAP, lsr #32
    //     0x85640c: b.eq            #0x856414
    //     0x856410: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856414: StoreField: r1->field_57 = rZR
    //     0x856414: stur            xzr, [x1, #0x57]
    // 0x856418: r0 = _LayoutCacheStorage()
    //     0x856418: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x85641c: ldur            x1, [fp, #-8]
    // 0x856420: StoreField: r1->field_4f = r0
    //     0x856420: stur            w0, [x1, #0x4f]
    //     0x856424: ldurb           w16, [x1, #-1]
    //     0x856428: ldurb           w17, [x0, #-1]
    //     0x85642c: and             x16, x17, x16, lsr #2
    //     0x856430: tst             x16, HEAP, lsr #32
    //     0x856434: b.eq            #0x85643c
    //     0x856438: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x85643c: r0 = RenderObject()
    //     0x85643c: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x856440: r0 = Null
    //     0x856440: mov             x0, NULL
    // 0x856444: LeaveFrame
    //     0x856444: mov             SP, fp
    //     0x856448: ldp             fp, lr, [SP], #0x10
    // 0x85644c: ret
    //     0x85644c: ret             
    // 0x856450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x856450: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x856454: b               #0x856360
    // 0x856458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x856458: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85645c: b               #0x856388
  }
}

// class id: 3055, size: 0xa0, field offset: 0x6c
class RenderFlex extends _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin {

  dynamic computeMinIntrinsicWidth(dynamic) {
    // ** addr: 0x7310d4, size: 0x24
    // 0x7310d4: EnterFrame
    //     0x7310d4: stp             fp, lr, [SP, #-0x10]!
    //     0x7310d8: mov             fp, SP
    // 0x7310dc: ldr             x2, [fp, #0x10]
    // 0x7310e0: r1 = Function 'computeMinIntrinsicWidth':.
    //     0x7310e0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55fb8] AnonymousClosure: (0x7310f8), in [package:flutter/src/rendering/flex.dart] RenderFlex::computeMinIntrinsicWidth (0x73116c)
    //     0x7310e4: ldr             x1, [x1, #0xfb8]
    // 0x7310e8: r0 = AllocateClosure()
    //     0x7310e8: bl              #0xec1630  ; AllocateClosureStub
    // 0x7310ec: LeaveFrame
    //     0x7310ec: mov             SP, fp
    //     0x7310f0: ldp             fp, lr, [SP], #0x10
    // 0x7310f4: ret
    //     0x7310f4: ret             
  }
  [closure] double computeMinIntrinsicWidth(dynamic, double) {
    // ** addr: 0x7310f8, size: 0x74
    // 0x7310f8: EnterFrame
    //     0x7310f8: stp             fp, lr, [SP, #-0x10]!
    //     0x7310fc: mov             fp, SP
    // 0x731100: ldr             x0, [fp, #0x18]
    // 0x731104: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x731104: ldur            w1, [x0, #0x17]
    // 0x731108: DecompressPointer r1
    //     0x731108: add             x1, x1, HEAP, lsl #32
    // 0x73110c: CheckStackOverflow
    //     0x73110c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x731110: cmp             SP, x16
    //     0x731114: b.ls            #0x731154
    // 0x731118: ldr             x2, [fp, #0x10]
    // 0x73111c: r0 = computeMinIntrinsicWidth()
    //     0x73111c: bl              #0x73116c  ; [package:flutter/src/rendering/flex.dart] RenderFlex::computeMinIntrinsicWidth
    // 0x731120: r0 = inline_Allocate_Double()
    //     0x731120: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x731124: add             x0, x0, #0x10
    //     0x731128: cmp             x1, x0
    //     0x73112c: b.ls            #0x73115c
    //     0x731130: str             x0, [THR, #0x50]  ; THR::top
    //     0x731134: sub             x0, x0, #0xf
    //     0x731138: movz            x1, #0xe15c
    //     0x73113c: movk            x1, #0x3, lsl #16
    //     0x731140: stur            x1, [x0, #-1]
    // 0x731144: StoreField: r0->field_7 = d0
    //     0x731144: stur            d0, [x0, #7]
    // 0x731148: LeaveFrame
    //     0x731148: mov             SP, fp
    //     0x73114c: ldp             fp, lr, [SP], #0x10
    // 0x731150: ret
    //     0x731150: ret             
    // 0x731154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x731154: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x731158: b               #0x731118
    // 0x73115c: SaveReg d0
    //     0x73115c: str             q0, [SP, #-0x10]!
    // 0x731160: r0 = AllocateDouble()
    //     0x731160: bl              #0xec2254  ; AllocateDoubleStub
    // 0x731164: RestoreReg d0
    //     0x731164: ldr             q0, [SP], #0x10
    // 0x731168: b               #0x731144
  }
  _ computeMinIntrinsicWidth(/* No info */) {
    // ** addr: 0x73116c, size: 0x60
    // 0x73116c: EnterFrame
    //     0x73116c: stp             fp, lr, [SP, #-0x10]!
    //     0x731170: mov             fp, SP
    // 0x731174: AllocStack(0x10)
    //     0x731174: sub             SP, SP, #0x10
    // 0x731178: SetupParameters(RenderFlex this /* r1 => r0, fp-0x8 */)
    //     0x731178: mov             x0, x1
    //     0x73117c: stur            x1, [fp, #-8]
    // 0x731180: CheckStackOverflow
    //     0x731180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x731184: cmp             SP, x16
    //     0x731188: b.ls            #0x7311c4
    // 0x73118c: LoadField: d0 = r2->field_7
    //     0x73118c: ldur            d0, [x2, #7]
    // 0x731190: stur            d0, [fp, #-0x10]
    // 0x731194: r1 = Function '<anonymous closure>':.
    //     0x731194: add             x1, PP, #0x55, lsl #12  ; [pp+0x55fc0] AnonymousClosure: (0x732e84), in [package:flutter/src/rendering/flex.dart] RenderFlex::computeMinIntrinsicWidth (0x73116c)
    //     0x731198: ldr             x1, [x1, #0xfc0]
    // 0x73119c: r2 = Null
    //     0x73119c: mov             x2, NULL
    // 0x7311a0: r0 = AllocateClosure()
    //     0x7311a0: bl              #0xec1630  ; AllocateClosureStub
    // 0x7311a4: ldur            x1, [fp, #-8]
    // 0x7311a8: mov             x2, x0
    // 0x7311ac: ldur            d0, [fp, #-0x10]
    // 0x7311b0: r3 = Instance_Axis
    //     0x7311b0: ldr             x3, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x7311b4: r0 = _getIntrinsicSize()
    //     0x7311b4: bl              #0x7311cc  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_getIntrinsicSize
    // 0x7311b8: LeaveFrame
    //     0x7311b8: mov             SP, fp
    //     0x7311bc: ldp             fp, lr, [SP], #0x10
    // 0x7311c0: ret
    //     0x7311c0: ret             
    // 0x7311c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7311c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7311c8: b               #0x73118c
  }
  _ _getIntrinsicSize(/* No info */) {
    // ** addr: 0x7311cc, size: 0x560
    // 0x7311cc: EnterFrame
    //     0x7311cc: stp             fp, lr, [SP, #-0x10]!
    //     0x7311d0: mov             fp, SP
    // 0x7311d4: AllocStack(0x88)
    //     0x7311d4: sub             SP, SP, #0x88
    // 0x7311d8: SetupParameters(RenderFlex this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x48 */)
    //     0x7311d8: stur            x1, [fp, #-8]
    //     0x7311dc: stur            x2, [fp, #-0x10]
    //     0x7311e0: stur            x3, [fp, #-0x18]
    //     0x7311e4: stur            d0, [fp, #-0x48]
    // 0x7311e8: CheckStackOverflow
    //     0x7311e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7311ec: cmp             SP, x16
    //     0x7311f0: b.ls            #0x731680
    // 0x7311f4: r1 = 2
    //     0x7311f4: movz            x1, #0x2
    // 0x7311f8: r0 = AllocateContext()
    //     0x7311f8: bl              #0xec126c  ; AllocateContextStub
    // 0x7311fc: mov             x3, x0
    // 0x731200: ldur            x0, [fp, #-0x10]
    // 0x731204: stur            x3, [fp, #-0x38]
    // 0x731208: StoreField: r3->field_f = r0
    //     0x731208: stur            w0, [x3, #0xf]
    // 0x73120c: ldur            x1, [fp, #-8]
    // 0x731210: LoadField: r0 = r1->field_6b
    //     0x731210: ldur            w0, [x1, #0x6b]
    // 0x731214: DecompressPointer r0
    //     0x731214: add             x0, x0, HEAP, lsl #32
    // 0x731218: ldur            x2, [fp, #-0x18]
    // 0x73121c: cmp             w0, w2
    // 0x731220: b.ne            #0x7315d4
    // 0x731224: ldur            d0, [fp, #-0x48]
    // 0x731228: d1 = 0.000000
    //     0x731228: eor             v1.16b, v1.16b, v1.16b
    // 0x73122c: LoadField: r0 = r1->field_57
    //     0x73122c: ldur            x0, [x1, #0x57]
    // 0x731230: sub             x2, x0, #1
    // 0x731234: scvtf           d2, x2
    // 0x731238: fmul            d3, d2, d1
    // 0x73123c: LoadField: r0 = r1->field_5f
    //     0x73123c: ldur            w0, [x1, #0x5f]
    // 0x731240: DecompressPointer r0
    //     0x731240: add             x0, x0, HEAP, lsl #32
    // 0x731244: r4 = inline_Allocate_Double()
    //     0x731244: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0x731248: add             x4, x4, #0x10
    //     0x73124c: cmp             x1, x4
    //     0x731250: b.ls            #0x731688
    //     0x731254: str             x4, [THR, #0x50]  ; THR::top
    //     0x731258: sub             x4, x4, #0xf
    //     0x73125c: movz            x1, #0xe15c
    //     0x731260: movk            x1, #0x3, lsl #16
    //     0x731264: stur            x1, [x4, #-1]
    // 0x731268: StoreField: r4->field_7 = d0
    //     0x731268: stur            d0, [x4, #7]
    // 0x73126c: stur            x4, [fp, #-0x30]
    // 0x731270: r5 = inline_Allocate_Double()
    //     0x731270: ldp             x5, x1, [THR, #0x50]  ; THR::top
    //     0x731274: add             x5, x5, #0x10
    //     0x731278: cmp             x1, x5
    //     0x73127c: b.ls            #0x7316ac
    //     0x731280: str             x5, [THR, #0x50]  ; THR::top
    //     0x731284: sub             x5, x5, #0xf
    //     0x731288: movz            x1, #0xe15c
    //     0x73128c: movk            x1, #0x3, lsl #16
    //     0x731290: stur            x1, [x5, #-1]
    // 0x731294: StoreField: r5->field_7 = d0
    //     0x731294: stur            d0, [x5, #7]
    // 0x731298: stur            x5, [fp, #-0x28]
    // 0x73129c: mov             v0.16b, v3.16b
    // 0x7312a0: mov             x6, x0
    // 0x7312a4: d2 = 0.000000
    //     0x7312a4: eor             v2.16b, v2.16b, v2.16b
    // 0x7312a8: r7 = 0.000000
    //     0x7312a8: ldr             x7, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x7312ac: stur            x7, [fp, #-0x18]
    // 0x7312b0: stur            x6, [fp, #-0x20]
    // 0x7312b4: stur            d2, [fp, #-0x50]
    // 0x7312b8: stur            d0, [fp, #-0x58]
    // 0x7312bc: CheckStackOverflow
    //     0x7312bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7312c0: cmp             SP, x16
    //     0x7312c4: b.ls            #0x7316d8
    // 0x7312c8: cmp             w6, NULL
    // 0x7312cc: b.eq            #0x7315b0
    // 0x7312d0: LoadField: r8 = r6->field_7
    //     0x7312d0: ldur            w8, [x6, #7]
    // 0x7312d4: DecompressPointer r8
    //     0x7312d4: add             x8, x8, HEAP, lsl #32
    // 0x7312d8: stur            x8, [fp, #-0x10]
    // 0x7312dc: cmp             w8, NULL
    // 0x7312e0: b.eq            #0x7316e0
    // 0x7312e4: mov             x0, x8
    // 0x7312e8: r2 = Null
    //     0x7312e8: mov             x2, NULL
    // 0x7312ec: r1 = Null
    //     0x7312ec: mov             x1, NULL
    // 0x7312f0: r4 = LoadClassIdInstr(r0)
    //     0x7312f0: ldur            x4, [x0, #-1]
    //     0x7312f4: ubfx            x4, x4, #0xc, #0x14
    // 0x7312f8: cmp             x4, #0xc7e
    // 0x7312fc: b.eq            #0x731314
    // 0x731300: r8 = FlexParentData
    //     0x731300: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x731304: ldr             x8, [x8, #0x590]
    // 0x731308: r3 = Null
    //     0x731308: add             x3, PP, #0x55, lsl #12  ; [pp+0x55f70] Null
    //     0x73130c: ldr             x3, [x3, #0xf70]
    // 0x731310: r0 = DefaultTypeTest()
    //     0x731310: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x731314: ldur            x0, [fp, #-0x10]
    // 0x731318: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x731318: ldur            w1, [x0, #0x17]
    // 0x73131c: DecompressPointer r1
    //     0x73131c: add             x1, x1, HEAP, lsl #32
    // 0x731320: cmp             w1, NULL
    // 0x731324: b.ne            #0x731330
    // 0x731328: r0 = 0
    //     0x731328: movz            x0, #0
    // 0x73132c: b               #0x73133c
    // 0x731330: r0 = LoadInt32Instr(r1)
    //     0x731330: sbfx            x0, x1, #1, #0x1f
    //     0x731334: tbz             w1, #0, #0x73133c
    //     0x731338: ldur            x0, [x1, #7]
    // 0x73133c: ldur            d0, [fp, #-0x50]
    // 0x731340: scvtf           d1, x0
    // 0x731344: stur            d1, [fp, #-0x68]
    // 0x731348: fadd            d2, d0, d1
    // 0x73134c: stur            d2, [fp, #-0x60]
    // 0x731350: cmp             x0, #0
    // 0x731354: b.le            #0x7314c4
    // 0x731358: ldur            x2, [fp, #-0x38]
    // 0x73135c: ldur            x1, [fp, #-0x18]
    // 0x731360: LoadField: r0 = r2->field_f
    //     0x731360: ldur            w0, [x2, #0xf]
    // 0x731364: DecompressPointer r0
    //     0x731364: add             x0, x0, HEAP, lsl #32
    // 0x731368: ldur            x16, [fp, #-0x20]
    // 0x73136c: stp             x16, x0, [SP, #8]
    // 0x731370: ldur            x16, [fp, #-0x28]
    // 0x731374: str             x16, [SP]
    // 0x731378: ClosureCall
    //     0x731378: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x73137c: ldur            x2, [x0, #0x1f]
    //     0x731380: blr             x2
    // 0x731384: cmp             w0, NULL
    // 0x731388: b.eq            #0x7316e4
    // 0x73138c: LoadField: d0 = r0->field_7
    //     0x73138c: ldur            d0, [x0, #7]
    // 0x731390: ldur            d1, [fp, #-0x68]
    // 0x731394: fdiv            d2, d0, d1
    // 0x731398: stur            d2, [fp, #-0x70]
    // 0x73139c: r1 = inline_Allocate_Double()
    //     0x73139c: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0x7313a0: add             x1, x1, #0x10
    //     0x7313a4: cmp             x0, x1
    //     0x7313a8: b.ls            #0x7316e8
    //     0x7313ac: str             x1, [THR, #0x50]  ; THR::top
    //     0x7313b0: sub             x1, x1, #0xf
    //     0x7313b4: movz            x0, #0xe15c
    //     0x7313b8: movk            x0, #0x3, lsl #16
    //     0x7313bc: stur            x0, [x1, #-1]
    // 0x7313c0: StoreField: r1->field_7 = d2
    //     0x7313c0: stur            d2, [x1, #7]
    // 0x7313c4: ldur            x2, [fp, #-0x18]
    // 0x7313c8: stur            x1, [fp, #-0x10]
    // 0x7313cc: r0 = 60
    //     0x7313cc: movz            x0, #0x3c
    // 0x7313d0: branchIfSmi(r2, 0x7313dc)
    //     0x7313d0: tbz             w2, #0, #0x7313dc
    // 0x7313d4: r0 = LoadClassIdInstr(r2)
    //     0x7313d4: ldur            x0, [x2, #-1]
    //     0x7313d8: ubfx            x0, x0, #0xc, #0x14
    // 0x7313dc: stp             x1, x2, [SP]
    // 0x7313e0: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x7313e0: sub             lr, x0, #0xfe3
    //     0x7313e4: ldr             lr, [x21, lr, lsl #3]
    //     0x7313e8: blr             lr
    // 0x7313ec: tbnz            w0, #4, #0x7313fc
    // 0x7313f0: ldur            x0, [fp, #-0x18]
    // 0x7313f4: d0 = 0.000000
    //     0x7313f4: eor             v0.16b, v0.16b, v0.16b
    // 0x7313f8: b               #0x7314b8
    // 0x7313fc: ldur            x1, [fp, #-0x18]
    // 0x731400: r0 = 60
    //     0x731400: movz            x0, #0x3c
    // 0x731404: branchIfSmi(r1, 0x731410)
    //     0x731404: tbz             w1, #0, #0x731410
    // 0x731408: r0 = LoadClassIdInstr(r1)
    //     0x731408: ldur            x0, [x1, #-1]
    //     0x73140c: ubfx            x0, x0, #0xc, #0x14
    // 0x731410: ldur            x16, [fp, #-0x10]
    // 0x731414: stp             x16, x1, [SP]
    // 0x731418: r0 = GDT[cid_x0 + -0xfd2]()
    //     0x731418: sub             lr, x0, #0xfd2
    //     0x73141c: ldr             lr, [x21, lr, lsl #3]
    //     0x731420: blr             lr
    // 0x731424: tbnz            w0, #4, #0x731434
    // 0x731428: ldur            x0, [fp, #-0x10]
    // 0x73142c: d0 = 0.000000
    //     0x73142c: eor             v0.16b, v0.16b, v0.16b
    // 0x731430: b               #0x7314b8
    // 0x731434: ldur            x1, [fp, #-0x18]
    // 0x731438: r0 = 60
    //     0x731438: movz            x0, #0x3c
    // 0x73143c: branchIfSmi(r1, 0x731448)
    //     0x73143c: tbz             w1, #0, #0x731448
    // 0x731440: r0 = LoadClassIdInstr(r1)
    //     0x731440: ldur            x0, [x1, #-1]
    //     0x731444: ubfx            x0, x0, #0xc, #0x14
    // 0x731448: cmp             x0, #0x3e
    // 0x73144c: b.ne            #0x73149c
    // 0x731450: d0 = 0.000000
    //     0x731450: eor             v0.16b, v0.16b, v0.16b
    // 0x731454: LoadField: d1 = r1->field_7
    //     0x731454: ldur            d1, [x1, #7]
    // 0x731458: fcmp            d1, d0
    // 0x73145c: b.ne            #0x731494
    // 0x731460: ldur            d2, [fp, #-0x70]
    // 0x731464: fadd            d3, d1, d2
    // 0x731468: r0 = inline_Allocate_Double()
    //     0x731468: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x73146c: add             x0, x0, #0x10
    //     0x731470: cmp             x1, x0
    //     0x731474: b.ls            #0x7316fc
    //     0x731478: str             x0, [THR, #0x50]  ; THR::top
    //     0x73147c: sub             x0, x0, #0xf
    //     0x731480: movz            x1, #0xe15c
    //     0x731484: movk            x1, #0x3, lsl #16
    //     0x731488: stur            x1, [x0, #-1]
    // 0x73148c: StoreField: r0->field_7 = d3
    //     0x73148c: stur            d3, [x0, #7]
    // 0x731490: b               #0x7314b8
    // 0x731494: ldur            d2, [fp, #-0x70]
    // 0x731498: b               #0x7314a4
    // 0x73149c: ldur            d2, [fp, #-0x70]
    // 0x7314a0: d0 = 0.000000
    //     0x7314a0: eor             v0.16b, v0.16b, v0.16b
    // 0x7314a4: fcmp            d2, d2
    // 0x7314a8: b.vc            #0x7314b4
    // 0x7314ac: ldur            x0, [fp, #-0x10]
    // 0x7314b0: b               #0x7314b8
    // 0x7314b4: mov             x0, x1
    // 0x7314b8: ldur            d0, [fp, #-0x58]
    // 0x7314bc: mov             x7, x0
    // 0x7314c0: b               #0x731534
    // 0x7314c4: ldur            x2, [fp, #-0x38]
    // 0x7314c8: ldur            d1, [fp, #-0x58]
    // 0x7314cc: ldur            x1, [fp, #-0x18]
    // 0x7314d0: d0 = 0.000000
    //     0x7314d0: eor             v0.16b, v0.16b, v0.16b
    // 0x7314d4: LoadField: r0 = r2->field_f
    //     0x7314d4: ldur            w0, [x2, #0xf]
    // 0x7314d8: DecompressPointer r0
    //     0x7314d8: add             x0, x0, HEAP, lsl #32
    // 0x7314dc: ldur            x16, [fp, #-0x20]
    // 0x7314e0: stp             x16, x0, [SP, #8]
    // 0x7314e4: ldur            x16, [fp, #-0x30]
    // 0x7314e8: str             x16, [SP]
    // 0x7314ec: ClosureCall
    //     0x7314ec: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x7314f0: ldur            x2, [x0, #0x1f]
    //     0x7314f4: blr             x2
    // 0x7314f8: ldur            d1, [fp, #-0x58]
    // 0x7314fc: r1 = inline_Allocate_Double()
    //     0x7314fc: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x731500: add             x1, x1, #0x10
    //     0x731504: cmp             x2, x1
    //     0x731508: b.ls            #0x73170c
    //     0x73150c: str             x1, [THR, #0x50]  ; THR::top
    //     0x731510: sub             x1, x1, #0xf
    //     0x731514: movz            x2, #0xe15c
    //     0x731518: movk            x2, #0x3, lsl #16
    //     0x73151c: stur            x2, [x1, #-1]
    // 0x731520: StoreField: r1->field_7 = d1
    //     0x731520: stur            d1, [x1, #7]
    // 0x731524: stp             x0, x1, [SP]
    // 0x731528: r0 = +()
    //     0x731528: bl              #0xebf900  ; [dart:core] _Double::+
    // 0x73152c: LoadField: d0 = r0->field_7
    //     0x73152c: ldur            d0, [x0, #7]
    // 0x731530: ldur            x7, [fp, #-0x18]
    // 0x731534: ldur            x0, [fp, #-0x20]
    // 0x731538: stur            x7, [fp, #-0x40]
    // 0x73153c: stur            d0, [fp, #-0x68]
    // 0x731540: LoadField: r3 = r0->field_7
    //     0x731540: ldur            w3, [x0, #7]
    // 0x731544: DecompressPointer r3
    //     0x731544: add             x3, x3, HEAP, lsl #32
    // 0x731548: stur            x3, [fp, #-0x10]
    // 0x73154c: cmp             w3, NULL
    // 0x731550: b.eq            #0x731728
    // 0x731554: mov             x0, x3
    // 0x731558: r2 = Null
    //     0x731558: mov             x2, NULL
    // 0x73155c: r1 = Null
    //     0x73155c: mov             x1, NULL
    // 0x731560: r4 = LoadClassIdInstr(r0)
    //     0x731560: ldur            x4, [x0, #-1]
    //     0x731564: ubfx            x4, x4, #0xc, #0x14
    // 0x731568: cmp             x4, #0xc7e
    // 0x73156c: b.eq            #0x731584
    // 0x731570: r8 = FlexParentData
    //     0x731570: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x731574: ldr             x8, [x8, #0x590]
    // 0x731578: r3 = Null
    //     0x731578: add             x3, PP, #0x55, lsl #12  ; [pp+0x55f80] Null
    //     0x73157c: ldr             x3, [x3, #0xf80]
    // 0x731580: r0 = DefaultTypeTest()
    //     0x731580: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x731584: ldur            x0, [fp, #-0x10]
    // 0x731588: LoadField: r6 = r0->field_13
    //     0x731588: ldur            w6, [x0, #0x13]
    // 0x73158c: DecompressPointer r6
    //     0x73158c: add             x6, x6, HEAP, lsl #32
    // 0x731590: ldur            d2, [fp, #-0x60]
    // 0x731594: ldur            d0, [fp, #-0x68]
    // 0x731598: ldur            x7, [fp, #-0x40]
    // 0x73159c: ldur            x3, [fp, #-0x38]
    // 0x7315a0: ldur            x5, [fp, #-0x28]
    // 0x7315a4: ldur            x4, [fp, #-0x30]
    // 0x7315a8: d1 = 0.000000
    //     0x7315a8: eor             v1.16b, v1.16b, v1.16b
    // 0x7315ac: b               #0x7312ac
    // 0x7315b0: mov             v1.16b, v0.16b
    // 0x7315b4: mov             v0.16b, v2.16b
    // 0x7315b8: mov             x0, x7
    // 0x7315bc: LoadField: d2 = r0->field_7
    //     0x7315bc: ldur            d2, [x0, #7]
    // 0x7315c0: fmul            d3, d2, d0
    // 0x7315c4: fadd            d0, d3, d1
    // 0x7315c8: LeaveFrame
    //     0x7315c8: mov             SP, fp
    //     0x7315cc: ldp             fp, lr, [SP], #0x10
    // 0x7315d0: ret
    //     0x7315d0: ret             
    // 0x7315d4: ldur            d0, [fp, #-0x48]
    // 0x7315d8: LoadField: r2 = r0->field_7
    //     0x7315d8: ldur            x2, [x0, #7]
    // 0x7315dc: cmp             x2, #0
    // 0x7315e0: b.gt            #0x7315ec
    // 0x7315e4: r0 = true
    //     0x7315e4: add             x0, NULL, #0x20  ; true
    // 0x7315e8: b               #0x7315f0
    // 0x7315ec: r0 = false
    //     0x7315ec: add             x0, NULL, #0x30  ; false
    // 0x7315f0: ldur            x2, [fp, #-0x38]
    // 0x7315f4: StoreField: r2->field_13 = r0
    //     0x7315f4: stur            w0, [x2, #0x13]
    // 0x7315f8: tbnz            w0, #4, #0x73161c
    // 0x7315fc: r0 = BoxConstraints()
    //     0x7315fc: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x731600: StoreField: r0->field_7 = rZR
    //     0x731600: stur            xzr, [x0, #7]
    // 0x731604: ldur            d0, [fp, #-0x48]
    // 0x731608: StoreField: r0->field_f = d0
    //     0x731608: stur            d0, [x0, #0xf]
    // 0x73160c: ArrayStore: r0[0] = rZR  ; List_8
    //     0x73160c: stur            xzr, [x0, #0x17]
    // 0x731610: d1 = inf
    //     0x731610: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x731614: StoreField: r0->field_1f = d1
    //     0x731614: stur            d1, [x0, #0x1f]
    // 0x731618: b               #0x73163c
    // 0x73161c: d1 = inf
    //     0x73161c: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x731620: r0 = BoxConstraints()
    //     0x731620: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x731624: StoreField: r0->field_7 = rZR
    //     0x731624: stur            xzr, [x0, #7]
    // 0x731628: d0 = inf
    //     0x731628: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x73162c: StoreField: r0->field_f = d0
    //     0x73162c: stur            d0, [x0, #0xf]
    // 0x731630: ArrayStore: r0[0] = rZR  ; List_8
    //     0x731630: stur            xzr, [x0, #0x17]
    // 0x731634: ldur            d0, [fp, #-0x48]
    // 0x731638: StoreField: r0->field_1f = d0
    //     0x731638: stur            d0, [x0, #0x1f]
    // 0x73163c: ldur            x2, [fp, #-0x38]
    // 0x731640: stur            x0, [fp, #-0x10]
    // 0x731644: r1 = Function 'layoutChild':.
    //     0x731644: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f90] AnonymousClosure: (0x732cd0), in [package:flutter/src/rendering/flex.dart] RenderFlex::_getIntrinsicSize (0x7311cc)
    //     0x731648: ldr             x1, [x1, #0xf90]
    // 0x73164c: r0 = AllocateClosure()
    //     0x73164c: bl              #0xec1630  ; AllocateClosureStub
    // 0x731650: ldur            x1, [fp, #-8]
    // 0x731654: ldur            x2, [fp, #-0x10]
    // 0x731658: mov             x5, x0
    // 0x73165c: r3 = Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getDryBaseline': static.
    //     0x73165c: add             x3, PP, #0x45, lsl #12  ; [pp+0x45748] Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getDryBaseline': static. (0x7e54fb130f1c)
    //     0x731660: ldr             x3, [x3, #0x748]
    // 0x731664: r0 = _computeSizes()
    //     0x731664: bl              #0x7317d4  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_computeSizes
    // 0x731668: LoadField: r1 = r0->field_7
    //     0x731668: ldur            w1, [x0, #7]
    // 0x73166c: DecompressPointer r1
    //     0x73166c: add             x1, x1, HEAP, lsl #32
    // 0x731670: LoadField: d0 = r1->field_f
    //     0x731670: ldur            d0, [x1, #0xf]
    // 0x731674: LeaveFrame
    //     0x731674: mov             SP, fp
    //     0x731678: ldp             fp, lr, [SP], #0x10
    // 0x73167c: ret
    //     0x73167c: ret             
    // 0x731680: r0 = StackOverflowSharedWithFPURegs()
    //     0x731680: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x731684: b               #0x7311f4
    // 0x731688: stp             q1, q3, [SP, #-0x20]!
    // 0x73168c: SaveReg d0
    //     0x73168c: str             q0, [SP, #-0x10]!
    // 0x731690: stp             x0, x3, [SP, #-0x10]!
    // 0x731694: r0 = AllocateDouble()
    //     0x731694: bl              #0xec2254  ; AllocateDoubleStub
    // 0x731698: mov             x4, x0
    // 0x73169c: ldp             x0, x3, [SP], #0x10
    // 0x7316a0: RestoreReg d0
    //     0x7316a0: ldr             q0, [SP], #0x10
    // 0x7316a4: ldp             q1, q3, [SP], #0x20
    // 0x7316a8: b               #0x731268
    // 0x7316ac: stp             q1, q3, [SP, #-0x20]!
    // 0x7316b0: SaveReg d0
    //     0x7316b0: str             q0, [SP, #-0x10]!
    // 0x7316b4: stp             x3, x4, [SP, #-0x10]!
    // 0x7316b8: SaveReg r0
    //     0x7316b8: str             x0, [SP, #-8]!
    // 0x7316bc: r0 = AllocateDouble()
    //     0x7316bc: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7316c0: mov             x5, x0
    // 0x7316c4: RestoreReg r0
    //     0x7316c4: ldr             x0, [SP], #8
    // 0x7316c8: ldp             x3, x4, [SP], #0x10
    // 0x7316cc: RestoreReg d0
    //     0x7316cc: ldr             q0, [SP], #0x10
    // 0x7316d0: ldp             q1, q3, [SP], #0x20
    // 0x7316d4: b               #0x731294
    // 0x7316d8: r0 = StackOverflowSharedWithFPURegs()
    //     0x7316d8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7316dc: b               #0x7312c8
    // 0x7316e0: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7316e0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x7316e4: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7316e4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7316e8: SaveReg d2
    //     0x7316e8: str             q2, [SP, #-0x10]!
    // 0x7316ec: r0 = AllocateDouble()
    //     0x7316ec: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7316f0: mov             x1, x0
    // 0x7316f4: RestoreReg d2
    //     0x7316f4: ldr             q2, [SP], #0x10
    // 0x7316f8: b               #0x7313c0
    // 0x7316fc: stp             q0, q3, [SP, #-0x20]!
    // 0x731700: r0 = AllocateDouble()
    //     0x731700: bl              #0xec2254  ; AllocateDoubleStub
    // 0x731704: ldp             q0, q3, [SP], #0x20
    // 0x731708: b               #0x73148c
    // 0x73170c: SaveReg d1
    //     0x73170c: str             q1, [SP, #-0x10]!
    // 0x731710: SaveReg r0
    //     0x731710: str             x0, [SP, #-8]!
    // 0x731714: r0 = AllocateDouble()
    //     0x731714: bl              #0xec2254  ; AllocateDoubleStub
    // 0x731718: mov             x1, x0
    // 0x73171c: RestoreReg r0
    //     0x73171c: ldr             x0, [SP], #8
    // 0x731720: RestoreReg d1
    //     0x731720: ldr             q1, [SP], #0x10
    // 0x731724: b               #0x731520
    // 0x731728: r0 = NullCastErrorSharedWithFPURegs()
    //     0x731728: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ _computeSizes(/* No info */) {
    // ** addr: 0x7317d4, size: 0xaa8
    // 0x7317d4: EnterFrame
    //     0x7317d4: stp             fp, lr, [SP, #-0x10]!
    //     0x7317d8: mov             fp, SP
    // 0x7317dc: AllocStack(0xd8)
    //     0x7317dc: sub             SP, SP, #0xd8
    // 0x7317e0: SetupParameters(RenderFlex this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0x7317e0: mov             x4, x1
    //     0x7317e4: stur            x2, [fp, #-0x10]
    //     0x7317e8: mov             x16, x3
    //     0x7317ec: mov             x3, x2
    //     0x7317f0: mov             x2, x16
    //     0x7317f4: mov             x0, x5
    //     0x7317f8: stur            x1, [fp, #-8]
    //     0x7317fc: stur            x2, [fp, #-0x18]
    //     0x731800: stur            x5, [fp, #-0x20]
    // 0x731804: CheckStackOverflow
    //     0x731804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x731808: cmp             SP, x16
    //     0x73180c: b.ls            #0x7321c8
    // 0x731810: mov             x1, x3
    // 0x731814: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x731814: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x731818: r0 = constrainWidth()
    //     0x731818: bl              #0x6cea58  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainWidth
    // 0x73181c: ldur            x1, [fp, #-0x10]
    // 0x731820: stur            d0, [fp, #-0x90]
    // 0x731824: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x731824: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x731828: r0 = constrainHeight()
    //     0x731828: bl              #0x6ce9e4  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainHeight
    // 0x73182c: stur            d0, [fp, #-0x98]
    // 0x731830: r0 = Size()
    //     0x731830: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x731834: ldur            d0, [fp, #-0x90]
    // 0x731838: StoreField: r0->field_7 = d0
    //     0x731838: stur            d0, [x0, #7]
    // 0x73183c: ldur            d0, [fp, #-0x98]
    // 0x731840: StoreField: r0->field_f = d0
    //     0x731840: stur            d0, [x0, #0xf]
    // 0x731844: ldur            x1, [fp, #-8]
    // 0x731848: mov             x2, x0
    // 0x73184c: r0 = _getMainSize()
    //     0x73184c: bl              #0x732ca4  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_getMainSize
    // 0x731850: stur            d0, [fp, #-0x90]
    // 0x731854: mov             x0, v0.d[0]
    // 0x731858: and             x0, x0, #0x7fffffffffffffff
    // 0x73185c: r17 = 9218868437227405312
    //     0x73185c: orr             x17, xzr, #0x7ff0000000000000
    // 0x731860: cmp             x0, x17
    // 0x731864: b.eq            #0x73187c
    // 0x731868: fcmp            d0, d0
    // 0x73186c: r16 = true
    //     0x73186c: add             x16, NULL, #0x20  ; true
    // 0x731870: r17 = false
    //     0x731870: add             x17, NULL, #0x30  ; false
    // 0x731874: csel            x0, x16, x17, vc
    // 0x731878: b               #0x731880
    // 0x73187c: r0 = false
    //     0x73187c: add             x0, NULL, #0x30  ; false
    // 0x731880: ldur            x1, [fp, #-8]
    // 0x731884: ldur            x2, [fp, #-0x10]
    // 0x731888: stur            x0, [fp, #-0x28]
    // 0x73188c: r0 = _constraintsForNonFlexChild()
    //     0x73188c: bl              #0x732b44  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_constraintsForNonFlexChild
    // 0x731890: ldur            x1, [fp, #-8]
    // 0x731894: stur            x0, [fp, #-0x30]
    // 0x731898: r0 = _isBaselineAligned()
    //     0x731898: bl              #0x732ab4  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_isBaselineAligned
    // 0x73189c: tbnz            w0, #4, #0x7318b8
    // 0x7318a0: ldur            x1, [fp, #-8]
    // 0x7318a4: LoadField: r0 = r1->field_83
    //     0x7318a4: ldur            w0, [x1, #0x83]
    // 0x7318a8: DecompressPointer r0
    //     0x7318a8: add             x0, x0, HEAP, lsl #32
    // 0x7318ac: cmp             w0, NULL
    // 0x7318b0: b.ne            #0x7318c0
    // 0x7318b4: b               #0x7321b0
    // 0x7318b8: ldur            x1, [fp, #-8]
    // 0x7318bc: r0 = Null
    //     0x7318bc: mov             x0, NULL
    // 0x7318c0: d1 = 0.000000
    //     0x7318c0: eor             v1.16b, v1.16b, v1.16b
    // 0x7318c4: stur            x0, [fp, #-0x38]
    // 0x7318c8: LoadField: r2 = r1->field_57
    //     0x7318c8: ldur            x2, [x1, #0x57]
    // 0x7318cc: sub             x3, x2, #1
    // 0x7318d0: scvtf           d0, x3
    // 0x7318d4: fmul            d2, d0, d1
    // 0x7318d8: stur            d2, [fp, #-0x98]
    // 0x7318dc: r0 = Size()
    //     0x7318dc: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x7318e0: ldur            d0, [fp, #-0x98]
    // 0x7318e4: StoreField: r0->field_7 = d0
    //     0x7318e4: stur            d0, [x0, #7]
    // 0x7318e8: StoreField: r0->field_f = rZR
    //     0x7318e8: stur            xzr, [x0, #0xf]
    // 0x7318ec: ldur            x3, [fp, #-8]
    // 0x7318f0: LoadField: r1 = r3->field_5f
    //     0x7318f0: ldur            w1, [x3, #0x5f]
    // 0x7318f4: DecompressPointer r1
    //     0x7318f4: add             x1, x1, HEAP, lsl #32
    // 0x7318f8: mov             x7, x0
    // 0x7318fc: mov             x6, x1
    // 0x731900: mov             v1.16b, v0.16b
    // 0x731904: r10 = 0
    //     0x731904: movz            x10, #0
    // 0x731908: r9 = Null
    //     0x731908: mov             x9, NULL
    // 0x73190c: r8 = Null
    //     0x73190c: mov             x8, NULL
    // 0x731910: d0 = 0.000000
    //     0x731910: eor             v0.16b, v0.16b, v0.16b
    // 0x731914: ldur            x4, [fp, #-0x38]
    // 0x731918: ldur            x5, [fp, #-0x28]
    // 0x73191c: stur            x10, [fp, #-0x48]
    // 0x731920: stur            x9, [fp, #-0x50]
    // 0x731924: stur            x8, [fp, #-0x58]
    // 0x731928: stur            x7, [fp, #-0x60]
    // 0x73192c: stur            x6, [fp, #-0x68]
    // 0x731930: stur            d1, [fp, #-0x98]
    // 0x731934: stur            d0, [fp, #-0xa0]
    // 0x731938: CheckStackOverflow
    //     0x731938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73193c: cmp             SP, x16
    //     0x731940: b.ls            #0x7321d0
    // 0x731944: cmp             w6, NULL
    // 0x731948: b.eq            #0x731c14
    // 0x73194c: tbnz            w5, #4, #0x731a0c
    // 0x731950: LoadField: r11 = r6->field_7
    //     0x731950: ldur            w11, [x6, #7]
    // 0x731954: DecompressPointer r11
    //     0x731954: add             x11, x11, HEAP, lsl #32
    // 0x731958: stur            x11, [fp, #-0x40]
    // 0x73195c: cmp             w11, NULL
    // 0x731960: b.eq            #0x7321d8
    // 0x731964: mov             x0, x11
    // 0x731968: r2 = Null
    //     0x731968: mov             x2, NULL
    // 0x73196c: r1 = Null
    //     0x73196c: mov             x1, NULL
    // 0x731970: r4 = LoadClassIdInstr(r0)
    //     0x731970: ldur            x4, [x0, #-1]
    //     0x731974: ubfx            x4, x4, #0xc, #0x14
    // 0x731978: cmp             x4, #0xc7e
    // 0x73197c: b.eq            #0x731994
    // 0x731980: r8 = FlexParentData
    //     0x731980: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x731984: ldr             x8, [x8, #0x590]
    // 0x731988: r3 = Null
    //     0x731988: add             x3, PP, #0x45, lsl #12  ; [pp+0x456f0] Null
    //     0x73198c: ldr             x3, [x3, #0x6f0]
    // 0x731990: r0 = DefaultTypeTest()
    //     0x731990: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x731994: ldur            x0, [fp, #-0x40]
    // 0x731998: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x731998: ldur            w1, [x0, #0x17]
    // 0x73199c: DecompressPointer r1
    //     0x73199c: add             x1, x1, HEAP, lsl #32
    // 0x7319a0: cmp             w1, NULL
    // 0x7319a4: b.ne            #0x7319b0
    // 0x7319a8: r0 = 0
    //     0x7319a8: movz            x0, #0
    // 0x7319ac: b               #0x7319bc
    // 0x7319b0: r0 = LoadInt32Instr(r1)
    //     0x7319b0: sbfx            x0, x1, #1, #0x1f
    //     0x7319b4: tbz             w1, #0, #0x7319bc
    //     0x7319b8: ldur            x0, [x1, #7]
    // 0x7319bc: cmp             x0, #0
    // 0x7319c0: b.le            #0x731a00
    // 0x7319c4: ldur            x2, [fp, #-0x48]
    // 0x7319c8: ldur            x1, [fp, #-0x50]
    // 0x7319cc: add             x3, x2, x0
    // 0x7319d0: cmp             w1, NULL
    // 0x7319d4: b.ne            #0x7319e0
    // 0x7319d8: ldur            x0, [fp, #-0x68]
    // 0x7319dc: b               #0x7319e4
    // 0x7319e0: mov             x0, x1
    // 0x7319e4: mov             x10, x3
    // 0x7319e8: mov             x9, x0
    // 0x7319ec: ldur            x8, [fp, #-0x58]
    // 0x7319f0: ldur            x7, [fp, #-0x60]
    // 0x7319f4: ldur            d1, [fp, #-0x98]
    // 0x7319f8: ldur            d0, [fp, #-0xa0]
    // 0x7319fc: b               #0x731b88
    // 0x731a00: ldur            x2, [fp, #-0x48]
    // 0x731a04: ldur            x1, [fp, #-0x50]
    // 0x731a08: b               #0x731a14
    // 0x731a0c: mov             x2, x10
    // 0x731a10: mov             x1, x9
    // 0x731a14: ldur            x3, [fp, #-8]
    // 0x731a18: ldur            d1, [fp, #-0x98]
    // 0x731a1c: ldur            d0, [fp, #-0xa0]
    // 0x731a20: ldur            x16, [fp, #-0x20]
    // 0x731a24: ldur            lr, [fp, #-0x68]
    // 0x731a28: stp             lr, x16, [SP, #8]
    // 0x731a2c: ldur            x16, [fp, #-0x30]
    // 0x731a30: str             x16, [SP]
    // 0x731a34: ldur            x0, [fp, #-0x20]
    // 0x731a38: ClosureCall
    //     0x731a38: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x731a3c: ldur            x2, [x0, #0x1f]
    //     0x731a40: blr             x2
    // 0x731a44: mov             x1, x0
    // 0x731a48: ldur            x0, [fp, #-8]
    // 0x731a4c: LoadField: r2 = r0->field_6b
    //     0x731a4c: ldur            w2, [x0, #0x6b]
    // 0x731a50: DecompressPointer r2
    //     0x731a50: add             x2, x2, HEAP, lsl #32
    // 0x731a54: r0 = _AxisSize._convert()
    //     0x731a54: bl              #0x732a28  ; [package:flutter/src/rendering/flex.dart] ::_AxisSize._convert
    // 0x731a58: LoadField: d0 = r0->field_7
    //     0x731a58: ldur            d0, [x0, #7]
    // 0x731a5c: ldur            d1, [fp, #-0x98]
    // 0x731a60: fadd            d2, d1, d0
    // 0x731a64: stur            d2, [fp, #-0xb8]
    // 0x731a68: LoadField: d0 = r0->field_f
    //     0x731a68: ldur            d0, [x0, #0xf]
    // 0x731a6c: ldur            d3, [fp, #-0xa0]
    // 0x731a70: stur            d0, [fp, #-0xb0]
    // 0x731a74: fcmp            d3, d0
    // 0x731a78: b.le            #0x731a84
    // 0x731a7c: d1 = 0.000000
    //     0x731a7c: eor             v1.16b, v1.16b, v1.16b
    // 0x731a80: b               #0x731abc
    // 0x731a84: fcmp            d0, d3
    // 0x731a88: b.le            #0x731a98
    // 0x731a8c: mov             v3.16b, v0.16b
    // 0x731a90: d1 = 0.000000
    //     0x731a90: eor             v1.16b, v1.16b, v1.16b
    // 0x731a94: b               #0x731abc
    // 0x731a98: d1 = 0.000000
    //     0x731a98: eor             v1.16b, v1.16b, v1.16b
    // 0x731a9c: fcmp            d3, d1
    // 0x731aa0: b.ne            #0x731ab0
    // 0x731aa4: fadd            d4, d3, d0
    // 0x731aa8: mov             v3.16b, v4.16b
    // 0x731aac: b               #0x731abc
    // 0x731ab0: fcmp            d0, d0
    // 0x731ab4: b.vc            #0x731abc
    // 0x731ab8: mov             v3.16b, v0.16b
    // 0x731abc: ldur            x0, [fp, #-0x38]
    // 0x731ac0: stur            d3, [fp, #-0xa8]
    // 0x731ac4: r0 = Size()
    //     0x731ac4: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x731ac8: mov             x1, x0
    // 0x731acc: ldur            d0, [fp, #-0xb8]
    // 0x731ad0: stur            x1, [fp, #-0x40]
    // 0x731ad4: StoreField: r1->field_7 = d0
    //     0x731ad4: stur            d0, [x1, #7]
    // 0x731ad8: ldur            d1, [fp, #-0xa8]
    // 0x731adc: StoreField: r1->field_f = d1
    //     0x731adc: stur            d1, [x1, #0xf]
    // 0x731ae0: ldur            x2, [fp, #-0x38]
    // 0x731ae4: cmp             w2, NULL
    // 0x731ae8: b.ne            #0x731af4
    // 0x731aec: r2 = Null
    //     0x731aec: mov             x2, NULL
    // 0x731af0: b               #0x731b1c
    // 0x731af4: ldur            x16, [fp, #-0x18]
    // 0x731af8: ldur            lr, [fp, #-0x68]
    // 0x731afc: stp             lr, x16, [SP, #0x10]
    // 0x731b00: ldur            x16, [fp, #-0x30]
    // 0x731b04: stp             x2, x16, [SP]
    // 0x731b08: ldur            x0, [fp, #-0x18]
    // 0x731b0c: ClosureCall
    //     0x731b0c: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0x731b10: ldur            x2, [x0, #0x1f]
    //     0x731b14: blr             x2
    // 0x731b18: mov             x2, x0
    // 0x731b1c: cmp             w2, NULL
    // 0x731b20: b.ne            #0x731b2c
    // 0x731b24: r2 = Null
    //     0x731b24: mov             x2, NULL
    // 0x731b28: b               #0x731b68
    // 0x731b2c: ldur            d0, [fp, #-0xb0]
    // 0x731b30: LoadField: d1 = r2->field_7
    //     0x731b30: ldur            d1, [x2, #7]
    // 0x731b34: fsub            d2, d0, d1
    // 0x731b38: r3 = inline_Allocate_Double()
    //     0x731b38: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x731b3c: add             x3, x3, #0x10
    //     0x731b40: cmp             x0, x3
    //     0x731b44: b.ls            #0x7321dc
    //     0x731b48: str             x3, [THR, #0x50]  ; THR::top
    //     0x731b4c: sub             x3, x3, #0xf
    //     0x731b50: movz            x0, #0xe15c
    //     0x731b54: movk            x0, #0x3, lsl #16
    //     0x731b58: stur            x0, [x3, #-1]
    // 0x731b5c: StoreField: r3->field_7 = d2
    //     0x731b5c: stur            d2, [x3, #7]
    // 0x731b60: r0 = AllocateRecord2()
    //     0x731b60: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x731b64: mov             x2, x0
    // 0x731b68: ldur            x1, [fp, #-0x58]
    // 0x731b6c: r0 = _AscentDescent.+()
    //     0x731b6c: bl              #0x732640  ; [package:flutter/src/rendering/flex.dart] ::_AscentDescent.+
    // 0x731b70: ldur            x10, [fp, #-0x48]
    // 0x731b74: ldur            x9, [fp, #-0x50]
    // 0x731b78: mov             x8, x0
    // 0x731b7c: ldur            x7, [fp, #-0x40]
    // 0x731b80: ldur            d1, [fp, #-0xb8]
    // 0x731b84: ldur            d0, [fp, #-0xa8]
    // 0x731b88: ldur            x0, [fp, #-0x68]
    // 0x731b8c: stur            x10, [fp, #-0x70]
    // 0x731b90: stur            x9, [fp, #-0x78]
    // 0x731b94: stur            x8, [fp, #-0x80]
    // 0x731b98: stur            x7, [fp, #-0x88]
    // 0x731b9c: stur            d1, [fp, #-0xa8]
    // 0x731ba0: stur            d0, [fp, #-0xb0]
    // 0x731ba4: LoadField: r3 = r0->field_7
    //     0x731ba4: ldur            w3, [x0, #7]
    // 0x731ba8: DecompressPointer r3
    //     0x731ba8: add             x3, x3, HEAP, lsl #32
    // 0x731bac: stur            x3, [fp, #-0x40]
    // 0x731bb0: cmp             w3, NULL
    // 0x731bb4: b.eq            #0x7321f8
    // 0x731bb8: mov             x0, x3
    // 0x731bbc: r2 = Null
    //     0x731bbc: mov             x2, NULL
    // 0x731bc0: r1 = Null
    //     0x731bc0: mov             x1, NULL
    // 0x731bc4: r4 = LoadClassIdInstr(r0)
    //     0x731bc4: ldur            x4, [x0, #-1]
    //     0x731bc8: ubfx            x4, x4, #0xc, #0x14
    // 0x731bcc: cmp             x4, #0xc7e
    // 0x731bd0: b.eq            #0x731be8
    // 0x731bd4: r8 = FlexParentData
    //     0x731bd4: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x731bd8: ldr             x8, [x8, #0x590]
    // 0x731bdc: r3 = Null
    //     0x731bdc: add             x3, PP, #0x45, lsl #12  ; [pp+0x45700] Null
    //     0x731be0: ldr             x3, [x3, #0x700]
    // 0x731be4: r0 = DefaultTypeTest()
    //     0x731be4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x731be8: ldur            x0, [fp, #-0x40]
    // 0x731bec: LoadField: r6 = r0->field_13
    //     0x731bec: ldur            w6, [x0, #0x13]
    // 0x731bf0: DecompressPointer r6
    //     0x731bf0: add             x6, x6, HEAP, lsl #32
    // 0x731bf4: ldur            x10, [fp, #-0x70]
    // 0x731bf8: ldur            x9, [fp, #-0x78]
    // 0x731bfc: ldur            x8, [fp, #-0x80]
    // 0x731c00: ldur            x7, [fp, #-0x88]
    // 0x731c04: ldur            d1, [fp, #-0xa8]
    // 0x731c08: ldur            d0, [fp, #-0xb0]
    // 0x731c0c: ldur            x3, [fp, #-8]
    // 0x731c10: b               #0x731914
    // 0x731c14: ldur            d2, [fp, #-0x90]
    // 0x731c18: mov             v3.16b, v0.16b
    // 0x731c1c: d0 = 0.000000
    //     0x731c1c: eor             v0.16b, v0.16b, v0.16b
    // 0x731c20: fsub            d4, d2, d1
    // 0x731c24: fcmp            d0, d4
    // 0x731c28: b.le            #0x731c34
    // 0x731c2c: d4 = 0.000000
    //     0x731c2c: eor             v4.16b, v4.16b, v4.16b
    // 0x731c30: b               #0x731c5c
    // 0x731c34: fcmp            d4, d0
    // 0x731c38: b.gt            #0x731c5c
    // 0x731c3c: fcmp            d0, d0
    // 0x731c40: b.ne            #0x731c50
    // 0x731c44: fadd            d5, d4, d0
    // 0x731c48: mov             v4.16b, v5.16b
    // 0x731c4c: b               #0x731c5c
    // 0x731c50: fcmp            d4, d4
    // 0x731c54: b.vs            #0x731c5c
    // 0x731c58: d4 = 0.000000
    //     0x731c58: eor             v4.16b, v4.16b, v4.16b
    // 0x731c5c: ldur            x0, [fp, #-0x48]
    // 0x731c60: scvtf           d5, x0
    // 0x731c64: fdiv            d6, d4, d5
    // 0x731c68: stur            d6, [fp, #-0xa8]
    // 0x731c6c: mov             x8, x0
    // 0x731c70: ldur            x7, [fp, #-0x58]
    // 0x731c74: ldur            x6, [fp, #-0x60]
    // 0x731c78: ldur            x5, [fp, #-0x50]
    // 0x731c7c: mov             v31.16b, v3.16b
    // 0x731c80: mov             v3.16b, v1.16b
    // 0x731c84: mov             v1.16b, v31.16b
    // 0x731c88: ldur            x4, [fp, #-8]
    // 0x731c8c: ldur            x3, [fp, #-0x38]
    // 0x731c90: stur            x8, [fp, #-0x48]
    // 0x731c94: stur            x7, [fp, #-0x30]
    // 0x731c98: stur            x6, [fp, #-0x40]
    // 0x731c9c: stur            x5, [fp, #-0x58]
    // 0x731ca0: stur            d3, [fp, #-0x98]
    // 0x731ca4: stur            d1, [fp, #-0xa0]
    // 0x731ca8: CheckStackOverflow
    //     0x731ca8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x731cac: cmp             SP, x16
    //     0x731cb0: b.ls            #0x7321fc
    // 0x731cb4: cmp             w5, NULL
    // 0x731cb8: b.eq            #0x731f78
    // 0x731cbc: cmp             x8, #0
    // 0x731cc0: b.le            #0x731f78
    // 0x731cc4: LoadField: r9 = r5->field_7
    //     0x731cc4: ldur            w9, [x5, #7]
    // 0x731cc8: DecompressPointer r9
    //     0x731cc8: add             x9, x9, HEAP, lsl #32
    // 0x731ccc: stur            x9, [fp, #-0x28]
    // 0x731cd0: cmp             w9, NULL
    // 0x731cd4: b.eq            #0x732204
    // 0x731cd8: mov             x0, x9
    // 0x731cdc: r2 = Null
    //     0x731cdc: mov             x2, NULL
    // 0x731ce0: r1 = Null
    //     0x731ce0: mov             x1, NULL
    // 0x731ce4: r4 = LoadClassIdInstr(r0)
    //     0x731ce4: ldur            x4, [x0, #-1]
    //     0x731ce8: ubfx            x4, x4, #0xc, #0x14
    // 0x731cec: cmp             x4, #0xc7e
    // 0x731cf0: b.eq            #0x731d08
    // 0x731cf4: r8 = FlexParentData
    //     0x731cf4: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x731cf8: ldr             x8, [x8, #0x590]
    // 0x731cfc: r3 = Null
    //     0x731cfc: add             x3, PP, #0x45, lsl #12  ; [pp+0x45710] Null
    //     0x731d00: ldr             x3, [x3, #0x710]
    // 0x731d04: r0 = DefaultTypeTest()
    //     0x731d04: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x731d08: ldur            x0, [fp, #-0x28]
    // 0x731d0c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x731d0c: ldur            w1, [x0, #0x17]
    // 0x731d10: DecompressPointer r1
    //     0x731d10: add             x1, x1, HEAP, lsl #32
    // 0x731d14: cmp             w1, NULL
    // 0x731d18: b.ne            #0x731d24
    // 0x731d1c: r0 = 0
    //     0x731d1c: movz            x0, #0
    // 0x731d20: b               #0x731d30
    // 0x731d24: r0 = LoadInt32Instr(r1)
    //     0x731d24: sbfx            x0, x1, #1, #0x1f
    //     0x731d28: tbz             w1, #0, #0x731d30
    //     0x731d2c: ldur            x0, [x1, #7]
    // 0x731d30: cbnz            x0, #0x731d4c
    // 0x731d34: ldur            x8, [fp, #-0x48]
    // 0x731d38: ldur            x7, [fp, #-0x30]
    // 0x731d3c: ldur            x6, [fp, #-0x40]
    // 0x731d40: ldur            d3, [fp, #-0x98]
    // 0x731d44: ldur            d1, [fp, #-0xa0]
    // 0x731d48: b               #0x731eec
    // 0x731d4c: ldur            x4, [fp, #-8]
    // 0x731d50: ldur            d1, [fp, #-0xa8]
    // 0x731d54: ldur            x1, [fp, #-0x48]
    // 0x731d58: ldur            d3, [fp, #-0x98]
    // 0x731d5c: ldur            d2, [fp, #-0xa0]
    // 0x731d60: sub             x5, x1, x0
    // 0x731d64: stur            x5, [fp, #-0x70]
    // 0x731d68: scvtf           d0, x0
    // 0x731d6c: fmul            d4, d1, d0
    // 0x731d70: mov             x1, x4
    // 0x731d74: ldur            x2, [fp, #-0x58]
    // 0x731d78: ldur            x3, [fp, #-0x10]
    // 0x731d7c: mov             v0.16b, v4.16b
    // 0x731d80: r0 = _constraintsForFlexChild()
    //     0x731d80: bl              #0x732460  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_constraintsForFlexChild
    // 0x731d84: mov             x1, x0
    // 0x731d88: stur            x1, [fp, #-0x28]
    // 0x731d8c: ldur            x16, [fp, #-0x20]
    // 0x731d90: ldur            lr, [fp, #-0x58]
    // 0x731d94: stp             lr, x16, [SP, #8]
    // 0x731d98: str             x1, [SP]
    // 0x731d9c: ldur            x0, [fp, #-0x20]
    // 0x731da0: ClosureCall
    //     0x731da0: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x731da4: ldur            x2, [x0, #0x1f]
    //     0x731da8: blr             x2
    // 0x731dac: mov             x1, x0
    // 0x731db0: ldur            x0, [fp, #-8]
    // 0x731db4: LoadField: r2 = r0->field_6b
    //     0x731db4: ldur            w2, [x0, #0x6b]
    // 0x731db8: DecompressPointer r2
    //     0x731db8: add             x2, x2, HEAP, lsl #32
    // 0x731dbc: r0 = _AxisSize._convert()
    //     0x731dbc: bl              #0x732a28  ; [package:flutter/src/rendering/flex.dart] ::_AxisSize._convert
    // 0x731dc0: LoadField: d0 = r0->field_7
    //     0x731dc0: ldur            d0, [x0, #7]
    // 0x731dc4: ldur            d2, [fp, #-0x98]
    // 0x731dc8: fadd            d1, d2, d0
    // 0x731dcc: stur            d1, [fp, #-0xb8]
    // 0x731dd0: LoadField: d0 = r0->field_f
    //     0x731dd0: ldur            d0, [x0, #0xf]
    // 0x731dd4: ldur            d2, [fp, #-0xa0]
    // 0x731dd8: stur            d0, [fp, #-0xb0]
    // 0x731ddc: fcmp            d2, d0
    // 0x731de0: b.le            #0x731dec
    // 0x731de4: d3 = 0.000000
    //     0x731de4: eor             v3.16b, v3.16b, v3.16b
    // 0x731de8: b               #0x731e24
    // 0x731dec: fcmp            d0, d2
    // 0x731df0: b.le            #0x731e00
    // 0x731df4: mov             v2.16b, v0.16b
    // 0x731df8: d3 = 0.000000
    //     0x731df8: eor             v3.16b, v3.16b, v3.16b
    // 0x731dfc: b               #0x731e24
    // 0x731e00: d3 = 0.000000
    //     0x731e00: eor             v3.16b, v3.16b, v3.16b
    // 0x731e04: fcmp            d2, d3
    // 0x731e08: b.ne            #0x731e18
    // 0x731e0c: fadd            d4, d2, d0
    // 0x731e10: mov             v2.16b, v4.16b
    // 0x731e14: b               #0x731e24
    // 0x731e18: fcmp            d0, d0
    // 0x731e1c: b.vc            #0x731e24
    // 0x731e20: mov             v2.16b, v0.16b
    // 0x731e24: ldur            x0, [fp, #-0x38]
    // 0x731e28: stur            d2, [fp, #-0x98]
    // 0x731e2c: r0 = Size()
    //     0x731e2c: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x731e30: mov             x1, x0
    // 0x731e34: ldur            d0, [fp, #-0xb8]
    // 0x731e38: stur            x1, [fp, #-0x60]
    // 0x731e3c: StoreField: r1->field_7 = d0
    //     0x731e3c: stur            d0, [x1, #7]
    // 0x731e40: ldur            d1, [fp, #-0x98]
    // 0x731e44: StoreField: r1->field_f = d1
    //     0x731e44: stur            d1, [x1, #0xf]
    // 0x731e48: ldur            x2, [fp, #-0x38]
    // 0x731e4c: cmp             w2, NULL
    // 0x731e50: b.ne            #0x731e5c
    // 0x731e54: r2 = Null
    //     0x731e54: mov             x2, NULL
    // 0x731e58: b               #0x731e84
    // 0x731e5c: ldur            x16, [fp, #-0x18]
    // 0x731e60: ldur            lr, [fp, #-0x58]
    // 0x731e64: stp             lr, x16, [SP, #0x10]
    // 0x731e68: ldur            x16, [fp, #-0x28]
    // 0x731e6c: stp             x2, x16, [SP]
    // 0x731e70: ldur            x0, [fp, #-0x18]
    // 0x731e74: ClosureCall
    //     0x731e74: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0x731e78: ldur            x2, [x0, #0x1f]
    //     0x731e7c: blr             x2
    // 0x731e80: mov             x2, x0
    // 0x731e84: cmp             w2, NULL
    // 0x731e88: b.ne            #0x731e94
    // 0x731e8c: r2 = Null
    //     0x731e8c: mov             x2, NULL
    // 0x731e90: b               #0x731ed0
    // 0x731e94: ldur            d0, [fp, #-0xb0]
    // 0x731e98: LoadField: d1 = r2->field_7
    //     0x731e98: ldur            d1, [x2, #7]
    // 0x731e9c: fsub            d2, d0, d1
    // 0x731ea0: r3 = inline_Allocate_Double()
    //     0x731ea0: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x731ea4: add             x3, x3, #0x10
    //     0x731ea8: cmp             x0, x3
    //     0x731eac: b.ls            #0x732208
    //     0x731eb0: str             x3, [THR, #0x50]  ; THR::top
    //     0x731eb4: sub             x3, x3, #0xf
    //     0x731eb8: movz            x0, #0xe15c
    //     0x731ebc: movk            x0, #0x3, lsl #16
    //     0x731ec0: stur            x0, [x3, #-1]
    // 0x731ec4: StoreField: r3->field_7 = d2
    //     0x731ec4: stur            d2, [x3, #7]
    // 0x731ec8: r0 = AllocateRecord2()
    //     0x731ec8: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x731ecc: mov             x2, x0
    // 0x731ed0: ldur            x1, [fp, #-0x30]
    // 0x731ed4: r0 = _AscentDescent.+()
    //     0x731ed4: bl              #0x732640  ; [package:flutter/src/rendering/flex.dart] ::_AscentDescent.+
    // 0x731ed8: ldur            x8, [fp, #-0x70]
    // 0x731edc: mov             x7, x0
    // 0x731ee0: ldur            x6, [fp, #-0x60]
    // 0x731ee4: ldur            d3, [fp, #-0xb8]
    // 0x731ee8: ldur            d1, [fp, #-0x98]
    // 0x731eec: ldur            x0, [fp, #-0x58]
    // 0x731ef0: stur            x8, [fp, #-0x48]
    // 0x731ef4: stur            x7, [fp, #-0x60]
    // 0x731ef8: stur            x6, [fp, #-0x68]
    // 0x731efc: stur            d3, [fp, #-0x98]
    // 0x731f00: stur            d1, [fp, #-0xa0]
    // 0x731f04: LoadField: r3 = r0->field_7
    //     0x731f04: ldur            w3, [x0, #7]
    // 0x731f08: DecompressPointer r3
    //     0x731f08: add             x3, x3, HEAP, lsl #32
    // 0x731f0c: stur            x3, [fp, #-0x28]
    // 0x731f10: cmp             w3, NULL
    // 0x731f14: b.eq            #0x732224
    // 0x731f18: mov             x0, x3
    // 0x731f1c: r2 = Null
    //     0x731f1c: mov             x2, NULL
    // 0x731f20: r1 = Null
    //     0x731f20: mov             x1, NULL
    // 0x731f24: r4 = LoadClassIdInstr(r0)
    //     0x731f24: ldur            x4, [x0, #-1]
    //     0x731f28: ubfx            x4, x4, #0xc, #0x14
    // 0x731f2c: cmp             x4, #0xc7e
    // 0x731f30: b.eq            #0x731f48
    // 0x731f34: r8 = FlexParentData
    //     0x731f34: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x731f38: ldr             x8, [x8, #0x590]
    // 0x731f3c: r3 = Null
    //     0x731f3c: add             x3, PP, #0x45, lsl #12  ; [pp+0x45720] Null
    //     0x731f40: ldr             x3, [x3, #0x720]
    // 0x731f44: r0 = DefaultTypeTest()
    //     0x731f44: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x731f48: ldur            x0, [fp, #-0x28]
    // 0x731f4c: LoadField: r5 = r0->field_13
    //     0x731f4c: ldur            w5, [x0, #0x13]
    // 0x731f50: DecompressPointer r5
    //     0x731f50: add             x5, x5, HEAP, lsl #32
    // 0x731f54: ldur            x8, [fp, #-0x48]
    // 0x731f58: ldur            x7, [fp, #-0x60]
    // 0x731f5c: ldur            x6, [fp, #-0x68]
    // 0x731f60: ldur            d3, [fp, #-0x98]
    // 0x731f64: ldur            d1, [fp, #-0xa0]
    // 0x731f68: ldur            d2, [fp, #-0x90]
    // 0x731f6c: ldur            d6, [fp, #-0xa8]
    // 0x731f70: d0 = 0.000000
    //     0x731f70: eor             v0.16b, v0.16b, v0.16b
    // 0x731f74: b               #0x731c88
    // 0x731f78: ldur            x0, [fp, #-0x30]
    // 0x731f7c: cmp             w0, NULL
    // 0x731f80: b.ne            #0x731f90
    // 0x731f84: r2 = Instance_Size
    //     0x731f84: add             x2, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!Size@e2c001
    //     0x731f88: ldr             x2, [x2, #0xa20]
    // 0x731f8c: b               #0x731ffc
    // 0x731f90: LoadField: r1 = r0->field_b
    //     0x731f90: ldur            w1, [x0, #0xb]
    // 0x731f94: cmp             w1, #4
    // 0x731f98: b.ne            #0x731ff8
    // 0x731f9c: LoadField: r1 = r0->field_f
    //     0x731f9c: ldur            w1, [x0, #0xf]
    // 0x731fa0: DecompressPointer r1
    //     0x731fa0: add             x1, x1, HEAP, lsl #32
    // 0x731fa4: r2 = 60
    //     0x731fa4: movz            x2, #0x3c
    // 0x731fa8: branchIfSmi(r1, 0x731fb4)
    //     0x731fa8: tbz             w1, #0, #0x731fb4
    // 0x731fac: r2 = LoadClassIdInstr(r1)
    //     0x731fac: ldur            x2, [x1, #-1]
    //     0x731fb0: ubfx            x2, x2, #0xc, #0x14
    // 0x731fb4: cmp             x2, #0x3e
    // 0x731fb8: b.ne            #0x731ff8
    // 0x731fbc: LoadField: r2 = r0->field_13
    //     0x731fbc: ldur            w2, [x0, #0x13]
    // 0x731fc0: DecompressPointer r2
    //     0x731fc0: add             x2, x2, HEAP, lsl #32
    // 0x731fc4: r3 = 60
    //     0x731fc4: movz            x3, #0x3c
    // 0x731fc8: branchIfSmi(r2, 0x731fd4)
    //     0x731fc8: tbz             w2, #0, #0x731fd4
    // 0x731fcc: r3 = LoadClassIdInstr(r2)
    //     0x731fcc: ldur            x3, [x2, #-1]
    //     0x731fd0: ubfx            x3, x3, #0xc, #0x14
    // 0x731fd4: cmp             x3, #0x3e
    // 0x731fd8: b.ne            #0x731ff8
    // 0x731fdc: stp             x2, x1, [SP]
    // 0x731fe0: r0 = +()
    //     0x731fe0: bl              #0xebf900  ; [dart:core] _Double::+
    // 0x731fe4: LoadField: d0 = r0->field_7
    //     0x731fe4: ldur            d0, [x0, #7]
    // 0x731fe8: d1 = 0.000000
    //     0x731fe8: eor             v1.16b, v1.16b, v1.16b
    // 0x731fec: r0 = _AxisSize.()
    //     0x731fec: bl              #0x73242c  ; [package:flutter/src/rendering/flex.dart] ::_AxisSize.
    // 0x731ff0: mov             x2, x0
    // 0x731ff4: b               #0x731ffc
    // 0x731ff8: r2 = Null
    //     0x731ff8: mov             x2, NULL
    // 0x731ffc: ldur            x0, [fp, #-8]
    // 0x732000: ldur            x1, [fp, #-0x40]
    // 0x732004: r0 = _AxisSize.+()
    //     0x732004: bl              #0x7323a8  ; [package:flutter/src/rendering/flex.dart] ::_AxisSize.+
    // 0x732008: mov             x1, x0
    // 0x73200c: ldur            x0, [fp, #-8]
    // 0x732010: stur            x1, [fp, #-0x18]
    // 0x732014: LoadField: r2 = r0->field_73
    //     0x732014: ldur            w2, [x0, #0x73]
    // 0x732018: DecompressPointer r2
    //     0x732018: add             x2, x2, HEAP, lsl #32
    // 0x73201c: r16 = Instance_MainAxisSize
    //     0x73201c: add             x16, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0x732020: ldr             x16, [x16, #0x738]
    // 0x732024: cmp             w2, w16
    // 0x732028: r16 = true
    //     0x732028: add             x16, NULL, #0x20  ; true
    // 0x73202c: r17 = false
    //     0x73202c: add             x17, NULL, #0x30  ; false
    // 0x732030: csel            x3, x16, x17, eq
    // 0x732034: tbnz            w3, #4, #0x732088
    // 0x732038: ldur            d0, [fp, #-0x90]
    // 0x73203c: mov             x4, v0.d[0]
    // 0x732040: and             x4, x4, #0x7fffffffffffffff
    // 0x732044: r17 = 9218868437227405312
    //     0x732044: orr             x17, xzr, #0x7ff0000000000000
    // 0x732048: cmp             x4, x17
    // 0x73204c: b.eq            #0x732088
    // 0x732050: fcmp            d0, d0
    // 0x732054: b.vs            #0x732088
    // 0x732058: r2 = inline_Allocate_Double()
    //     0x732058: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x73205c: add             x2, x2, #0x10
    //     0x732060: cmp             x3, x2
    //     0x732064: b.ls            #0x732228
    //     0x732068: str             x2, [THR, #0x50]  ; THR::top
    //     0x73206c: sub             x2, x2, #0xf
    //     0x732070: movz            x3, #0xe15c
    //     0x732074: movk            x3, #0x3, lsl #16
    //     0x732078: stur            x3, [x2, #-1]
    // 0x73207c: StoreField: r2->field_7 = d0
    //     0x73207c: stur            d0, [x2, #7]
    // 0x732080: mov             x3, x2
    // 0x732084: b               #0x7320d4
    // 0x732088: tbz             w3, #4, #0x73209c
    // 0x73208c: r16 = Instance_MainAxisSize
    //     0x73208c: add             x16, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0x732090: ldr             x16, [x16, #0xe88]
    // 0x732094: cmp             w2, w16
    // 0x732098: b.ne            #0x7320d0
    // 0x73209c: LoadField: d0 = r1->field_7
    //     0x73209c: ldur            d0, [x1, #7]
    // 0x7320a0: r2 = inline_Allocate_Double()
    //     0x7320a0: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x7320a4: add             x2, x2, #0x10
    //     0x7320a8: cmp             x3, x2
    //     0x7320ac: b.ls            #0x732244
    //     0x7320b0: str             x2, [THR, #0x50]  ; THR::top
    //     0x7320b4: sub             x2, x2, #0xf
    //     0x7320b8: movz            x3, #0xe15c
    //     0x7320bc: movk            x3, #0x3, lsl #16
    //     0x7320c0: stur            x3, [x2, #-1]
    // 0x7320c4: StoreField: r2->field_7 = d0
    //     0x7320c4: stur            d0, [x2, #7]
    // 0x7320c8: mov             x3, x2
    // 0x7320cc: b               #0x7320d4
    // 0x7320d0: r3 = Null
    //     0x7320d0: mov             x3, NULL
    // 0x7320d4: ldur            x2, [fp, #-0x30]
    // 0x7320d8: LoadField: d0 = r1->field_f
    //     0x7320d8: ldur            d0, [x1, #0xf]
    // 0x7320dc: LoadField: d1 = r3->field_7
    //     0x7320dc: ldur            d1, [x3, #7]
    // 0x7320e0: r0 = _AxisSize.()
    //     0x7320e0: bl              #0x73242c  ; [package:flutter/src/rendering/flex.dart] ::_AxisSize.
    // 0x7320e4: mov             x1, x0
    // 0x7320e8: ldur            x0, [fp, #-8]
    // 0x7320ec: LoadField: r3 = r0->field_6b
    //     0x7320ec: ldur            w3, [x0, #0x6b]
    // 0x7320f0: DecompressPointer r3
    //     0x7320f0: add             x3, x3, HEAP, lsl #32
    // 0x7320f4: ldur            x2, [fp, #-0x10]
    // 0x7320f8: r0 = _AxisSize.applyConstraints()
    //     0x7320f8: bl              #0x7322f0  ; [package:flutter/src/rendering/flex.dart] ::_AxisSize.applyConstraints
    // 0x7320fc: stur            x0, [fp, #-0x20]
    // 0x732100: LoadField: d0 = r0->field_7
    //     0x732100: ldur            d0, [x0, #7]
    // 0x732104: ldur            x1, [fp, #-0x18]
    // 0x732108: LoadField: d1 = r1->field_7
    //     0x732108: ldur            d1, [x1, #7]
    // 0x73210c: fsub            d2, d0, d1
    // 0x732110: ldur            x1, [fp, #-0x30]
    // 0x732114: stur            d2, [fp, #-0x90]
    // 0x732118: cmp             w1, NULL
    // 0x73211c: b.ne            #0x732128
    // 0x732120: r2 = Null
    //     0x732120: mov             x2, NULL
    // 0x732124: b               #0x732130
    // 0x732128: LoadField: r2 = r1->field_f
    //     0x732128: ldur            w2, [x1, #0xf]
    // 0x73212c: DecompressPointer r2
    //     0x73212c: add             x2, x2, HEAP, lsl #32
    // 0x732130: ldur            x1, [fp, #-0x50]
    // 0x732134: stur            x2, [fp, #-0x10]
    // 0x732138: cmp             w1, NULL
    // 0x73213c: b.ne            #0x732148
    // 0x732140: r1 = Null
    //     0x732140: mov             x1, NULL
    // 0x732144: b               #0x732174
    // 0x732148: ldur            d0, [fp, #-0xa8]
    // 0x73214c: r1 = inline_Allocate_Double()
    //     0x73214c: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0x732150: add             x1, x1, #0x10
    //     0x732154: cmp             x3, x1
    //     0x732158: b.ls            #0x732260
    //     0x73215c: str             x1, [THR, #0x50]  ; THR::top
    //     0x732160: sub             x1, x1, #0xf
    //     0x732164: movz            x3, #0xe15c
    //     0x732168: movk            x3, #0x3, lsl #16
    //     0x73216c: stur            x3, [x1, #-1]
    // 0x732170: StoreField: r1->field_7 = d0
    //     0x732170: stur            d0, [x1, #7]
    // 0x732174: stur            x1, [fp, #-8]
    // 0x732178: r0 = _LayoutSizes()
    //     0x732178: bl              #0x7322e4  ; Allocate_LayoutSizesStub -> _LayoutSizes (size=0x1c)
    // 0x73217c: mov             x1, x0
    // 0x732180: ldur            x0, [fp, #-0x20]
    // 0x732184: StoreField: r1->field_7 = r0
    //     0x732184: stur            w0, [x1, #7]
    // 0x732188: ldur            x0, [fp, #-0x10]
    // 0x73218c: StoreField: r1->field_13 = r0
    //     0x73218c: stur            w0, [x1, #0x13]
    // 0x732190: ldur            d0, [fp, #-0x90]
    // 0x732194: StoreField: r1->field_b = d0
    //     0x732194: stur            d0, [x1, #0xb]
    // 0x732198: ldur            x0, [fp, #-8]
    // 0x73219c: ArrayStore: r1[0] = r0  ; List_4
    //     0x73219c: stur            w0, [x1, #0x17]
    // 0x7321a0: mov             x0, x1
    // 0x7321a4: LeaveFrame
    //     0x7321a4: mov             SP, fp
    //     0x7321a8: ldp             fp, lr, [SP], #0x10
    // 0x7321ac: ret
    //     0x7321ac: ret             
    // 0x7321b0: r1 = Null
    //     0x7321b0: mov             x1, NULL
    // 0x7321b4: r2 = "To use CrossAxisAlignment.baseline, you must also specify which baseline to use using the \"textBaseline\" argument."
    //     0x7321b4: add             x2, PP, #0x45, lsl #12  ; [pp+0x45730] "To use CrossAxisAlignment.baseline, you must also specify which baseline to use using the \"textBaseline\" argument."
    //     0x7321b8: ldr             x2, [x2, #0x730]
    // 0x7321bc: r0 = FlutterError()
    //     0x7321bc: bl              #0x6803c0  ; [package:flutter/src/foundation/assertions.dart] FlutterError::FlutterError
    // 0x7321c0: r0 = Throw()
    //     0x7321c0: bl              #0xec04b8  ; ThrowStub
    // 0x7321c4: brk             #0
    // 0x7321c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7321c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7321cc: b               #0x731810
    // 0x7321d0: r0 = StackOverflowSharedWithFPURegs()
    //     0x7321d0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7321d4: b               #0x731944
    // 0x7321d8: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7321d8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x7321dc: SaveReg d2
    //     0x7321dc: str             q2, [SP, #-0x10]!
    // 0x7321e0: SaveReg r2
    //     0x7321e0: str             x2, [SP, #-8]!
    // 0x7321e4: r0 = AllocateDouble()
    //     0x7321e4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7321e8: mov             x3, x0
    // 0x7321ec: RestoreReg r2
    //     0x7321ec: ldr             x2, [SP], #8
    // 0x7321f0: RestoreReg d2
    //     0x7321f0: ldr             q2, [SP], #0x10
    // 0x7321f4: b               #0x731b5c
    // 0x7321f8: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7321f8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x7321fc: r0 = StackOverflowSharedWithFPURegs()
    //     0x7321fc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x732200: b               #0x731cb4
    // 0x732204: r0 = NullCastErrorSharedWithFPURegs()
    //     0x732204: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x732208: SaveReg d2
    //     0x732208: str             q2, [SP, #-0x10]!
    // 0x73220c: SaveReg r2
    //     0x73220c: str             x2, [SP, #-8]!
    // 0x732210: r0 = AllocateDouble()
    //     0x732210: bl              #0xec2254  ; AllocateDoubleStub
    // 0x732214: mov             x3, x0
    // 0x732218: RestoreReg r2
    //     0x732218: ldr             x2, [SP], #8
    // 0x73221c: RestoreReg d2
    //     0x73221c: ldr             q2, [SP], #0x10
    // 0x732220: b               #0x731ec4
    // 0x732224: r0 = NullCastErrorSharedWithFPURegs()
    //     0x732224: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x732228: SaveReg d0
    //     0x732228: str             q0, [SP, #-0x10]!
    // 0x73222c: stp             x0, x1, [SP, #-0x10]!
    // 0x732230: r0 = AllocateDouble()
    //     0x732230: bl              #0xec2254  ; AllocateDoubleStub
    // 0x732234: mov             x2, x0
    // 0x732238: ldp             x0, x1, [SP], #0x10
    // 0x73223c: RestoreReg d0
    //     0x73223c: ldr             q0, [SP], #0x10
    // 0x732240: b               #0x73207c
    // 0x732244: SaveReg d0
    //     0x732244: str             q0, [SP, #-0x10]!
    // 0x732248: stp             x0, x1, [SP, #-0x10]!
    // 0x73224c: r0 = AllocateDouble()
    //     0x73224c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x732250: mov             x2, x0
    // 0x732254: ldp             x0, x1, [SP], #0x10
    // 0x732258: RestoreReg d0
    //     0x732258: ldr             q0, [SP], #0x10
    // 0x73225c: b               #0x7320c4
    // 0x732260: stp             q0, q2, [SP, #-0x20]!
    // 0x732264: stp             x0, x2, [SP, #-0x10]!
    // 0x732268: r0 = AllocateDouble()
    //     0x732268: bl              #0xec2254  ; AllocateDoubleStub
    // 0x73226c: mov             x1, x0
    // 0x732270: ldp             x0, x2, [SP], #0x10
    // 0x732274: ldp             q0, q2, [SP], #0x20
    // 0x732278: b               #0x732170
  }
  _ _constraintsForFlexChild(/* No info */) {
    // ** addr: 0x732460, size: 0x1e0
    // 0x732460: EnterFrame
    //     0x732460: stp             fp, lr, [SP, #-0x10]!
    //     0x732464: mov             fp, SP
    // 0x732468: AllocStack(0x38)
    //     0x732468: sub             SP, SP, #0x38
    // 0x73246c: SetupParameters(RenderFlex this /* r1 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x20 */)
    //     0x73246c: mov             x4, x1
    //     0x732470: stur            x1, [fp, #-0x10]
    //     0x732474: stur            x3, [fp, #-0x18]
    //     0x732478: stur            d0, [fp, #-0x20]
    // 0x73247c: LoadField: r5 = r2->field_7
    //     0x73247c: ldur            w5, [x2, #7]
    // 0x732480: DecompressPointer r5
    //     0x732480: add             x5, x5, HEAP, lsl #32
    // 0x732484: stur            x5, [fp, #-8]
    // 0x732488: cmp             w5, NULL
    // 0x73248c: b.eq            #0x73263c
    // 0x732490: mov             x0, x5
    // 0x732494: r2 = Null
    //     0x732494: mov             x2, NULL
    // 0x732498: r1 = Null
    //     0x732498: mov             x1, NULL
    // 0x73249c: r4 = LoadClassIdInstr(r0)
    //     0x73249c: ldur            x4, [x0, #-1]
    //     0x7324a0: ubfx            x4, x4, #0xc, #0x14
    // 0x7324a4: cmp             x4, #0xc7e
    // 0x7324a8: b.eq            #0x7324c0
    // 0x7324ac: r8 = FlexParentData
    //     0x7324ac: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x7324b0: ldr             x8, [x8, #0x590]
    // 0x7324b4: r3 = Null
    //     0x7324b4: add             x3, PP, #0x45, lsl #12  ; [pp+0x45738] Null
    //     0x7324b8: ldr             x3, [x3, #0x738]
    // 0x7324bc: r0 = DefaultTypeTest()
    //     0x7324bc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7324c0: ldur            x0, [fp, #-8]
    // 0x7324c4: LoadField: r1 = r0->field_1b
    //     0x7324c4: ldur            w1, [x0, #0x1b]
    // 0x7324c8: DecompressPointer r1
    //     0x7324c8: add             x1, x1, HEAP, lsl #32
    // 0x7324cc: cmp             w1, NULL
    // 0x7324d0: b.ne            #0x7324e0
    // 0x7324d4: r0 = Instance_FlexFit
    //     0x7324d4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0x7324d8: ldr             x0, [x0, #0x728]
    // 0x7324dc: b               #0x7324e4
    // 0x7324e0: mov             x0, x1
    // 0x7324e4: LoadField: r1 = r0->field_7
    //     0x7324e4: ldur            x1, [x0, #7]
    // 0x7324e8: cmp             x1, #0
    // 0x7324ec: b.gt            #0x7324f8
    // 0x7324f0: ldur            d0, [fp, #-0x20]
    // 0x7324f4: b               #0x7324fc
    // 0x7324f8: d0 = 0.000000
    //     0x7324f8: eor             v0.16b, v0.16b, v0.16b
    // 0x7324fc: ldur            x0, [fp, #-0x10]
    // 0x732500: stur            d0, [fp, #-0x38]
    // 0x732504: LoadField: r1 = r0->field_77
    //     0x732504: ldur            w1, [x0, #0x77]
    // 0x732508: DecompressPointer r1
    //     0x732508: add             x1, x1, HEAP, lsl #32
    // 0x73250c: r16 = Instance_CrossAxisAlignment
    //     0x73250c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0x732510: ldr             x16, [x16, #0xf50]
    // 0x732514: cmp             w1, w16
    // 0x732518: b.ne            #0x732524
    // 0x73251c: r1 = true
    //     0x73251c: add             x1, NULL, #0x20  ; true
    // 0x732520: b               #0x732570
    // 0x732524: r16 = Instance_CrossAxisAlignment
    //     0x732524: add             x16, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0x732528: ldr             x16, [x16, #0x68]
    // 0x73252c: cmp             w1, w16
    // 0x732530: b.eq            #0x732564
    // 0x732534: r16 = Instance_CrossAxisAlignment
    //     0x732534: add             x16, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0x732538: ldr             x16, [x16, #0x740]
    // 0x73253c: cmp             w1, w16
    // 0x732540: b.eq            #0x732564
    // 0x732544: r16 = Instance_CrossAxisAlignment
    //     0x732544: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fa8] Obj!CrossAxisAlignment@e359c1
    //     0x732548: ldr             x16, [x16, #0xfa8]
    // 0x73254c: cmp             w1, w16
    // 0x732550: b.eq            #0x732564
    // 0x732554: r16 = Instance_CrossAxisAlignment
    //     0x732554: add             x16, PP, #0x45, lsl #12  ; [pp+0x456b8] Obj!CrossAxisAlignment@e359a1
    //     0x732558: ldr             x16, [x16, #0x6b8]
    // 0x73255c: cmp             w1, w16
    // 0x732560: b.ne            #0x73256c
    // 0x732564: r1 = false
    //     0x732564: add             x1, NULL, #0x30  ; false
    // 0x732568: b               #0x732570
    // 0x73256c: r1 = Null
    //     0x73256c: mov             x1, NULL
    // 0x732570: LoadField: r2 = r0->field_6b
    //     0x732570: ldur            w2, [x0, #0x6b]
    // 0x732574: DecompressPointer r2
    //     0x732574: add             x2, x2, HEAP, lsl #32
    // 0x732578: LoadField: r0 = r2->field_7
    //     0x732578: ldur            x0, [x2, #7]
    // 0x73257c: cmp             x0, #0
    // 0x732580: b.gt            #0x7325e0
    // 0x732584: r16 = true
    //     0x732584: add             x16, NULL, #0x20  ; true
    // 0x732588: cmp             w1, w16
    // 0x73258c: b.ne            #0x7325a0
    // 0x732590: ldur            x0, [fp, #-0x18]
    // 0x732594: LoadField: d1 = r0->field_1f
    //     0x732594: ldur            d1, [x0, #0x1f]
    // 0x732598: mov             v2.16b, v1.16b
    // 0x73259c: b               #0x7325a8
    // 0x7325a0: ldur            x0, [fp, #-0x18]
    // 0x7325a4: d2 = 0.000000
    //     0x7325a4: eor             v2.16b, v2.16b, v2.16b
    // 0x7325a8: ldur            d1, [fp, #-0x20]
    // 0x7325ac: stur            d2, [fp, #-0x30]
    // 0x7325b0: LoadField: d3 = r0->field_1f
    //     0x7325b0: ldur            d3, [x0, #0x1f]
    // 0x7325b4: stur            d3, [fp, #-0x28]
    // 0x7325b8: r0 = BoxConstraints()
    //     0x7325b8: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x7325bc: ldur            d0, [fp, #-0x38]
    // 0x7325c0: StoreField: r0->field_7 = d0
    //     0x7325c0: stur            d0, [x0, #7]
    // 0x7325c4: ldur            d1, [fp, #-0x20]
    // 0x7325c8: StoreField: r0->field_f = d1
    //     0x7325c8: stur            d1, [x0, #0xf]
    // 0x7325cc: ldur            d0, [fp, #-0x30]
    // 0x7325d0: ArrayStore: r0[0] = d0  ; List_8
    //     0x7325d0: stur            d0, [x0, #0x17]
    // 0x7325d4: ldur            d0, [fp, #-0x28]
    // 0x7325d8: StoreField: r0->field_1f = d0
    //     0x7325d8: stur            d0, [x0, #0x1f]
    // 0x7325dc: b               #0x732630
    // 0x7325e0: ldur            x0, [fp, #-0x18]
    // 0x7325e4: ldur            d1, [fp, #-0x20]
    // 0x7325e8: r16 = true
    //     0x7325e8: add             x16, NULL, #0x20  ; true
    // 0x7325ec: cmp             w1, w16
    // 0x7325f0: b.ne            #0x7325fc
    // 0x7325f4: LoadField: d2 = r0->field_f
    //     0x7325f4: ldur            d2, [x0, #0xf]
    // 0x7325f8: b               #0x732600
    // 0x7325fc: d2 = 0.000000
    //     0x7325fc: eor             v2.16b, v2.16b, v2.16b
    // 0x732600: stur            d2, [fp, #-0x30]
    // 0x732604: LoadField: d3 = r0->field_f
    //     0x732604: ldur            d3, [x0, #0xf]
    // 0x732608: stur            d3, [fp, #-0x28]
    // 0x73260c: r0 = BoxConstraints()
    //     0x73260c: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x732610: ldur            d0, [fp, #-0x30]
    // 0x732614: StoreField: r0->field_7 = d0
    //     0x732614: stur            d0, [x0, #7]
    // 0x732618: ldur            d0, [fp, #-0x28]
    // 0x73261c: StoreField: r0->field_f = d0
    //     0x73261c: stur            d0, [x0, #0xf]
    // 0x732620: ldur            d0, [fp, #-0x38]
    // 0x732624: ArrayStore: r0[0] = d0  ; List_8
    //     0x732624: stur            d0, [x0, #0x17]
    // 0x732628: ldur            d0, [fp, #-0x20]
    // 0x73262c: StoreField: r0->field_1f = d0
    //     0x73262c: stur            d0, [x0, #0x1f]
    // 0x732630: LeaveFrame
    //     0x732630: mov             SP, fp
    //     0x732634: ldp             fp, lr, [SP], #0x10
    // 0x732638: ret
    //     0x732638: ret             
    // 0x73263c: r0 = NullCastErrorSharedWithFPURegs()
    //     0x73263c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  get _ _isBaselineAligned(/* No info */) {
    // ** addr: 0x732ab4, size: 0x90
    // 0x732ab4: LoadField: r2 = r1->field_77
    //     0x732ab4: ldur            w2, [x1, #0x77]
    // 0x732ab8: DecompressPointer r2
    //     0x732ab8: add             x2, x2, HEAP, lsl #32
    // 0x732abc: r16 = Instance_CrossAxisAlignment
    //     0x732abc: add             x16, PP, #0x45, lsl #12  ; [pp+0x456b8] Obj!CrossAxisAlignment@e359a1
    //     0x732ac0: ldr             x16, [x16, #0x6b8]
    // 0x732ac4: cmp             w2, w16
    // 0x732ac8: b.ne            #0x732af4
    // 0x732acc: LoadField: r3 = r1->field_6b
    //     0x732acc: ldur            w3, [x1, #0x6b]
    // 0x732ad0: DecompressPointer r3
    //     0x732ad0: add             x3, x3, HEAP, lsl #32
    // 0x732ad4: LoadField: r1 = r3->field_7
    //     0x732ad4: ldur            x1, [x3, #7]
    // 0x732ad8: cmp             x1, #0
    // 0x732adc: b.gt            #0x732ae8
    // 0x732ae0: r1 = true
    //     0x732ae0: add             x1, NULL, #0x20  ; true
    // 0x732ae4: b               #0x732aec
    // 0x732ae8: r1 = false
    //     0x732ae8: add             x1, NULL, #0x30  ; false
    // 0x732aec: mov             x0, x1
    // 0x732af0: b               #0x732b40
    // 0x732af4: r16 = Instance_CrossAxisAlignment
    //     0x732af4: add             x16, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0x732af8: ldr             x16, [x16, #0x68]
    // 0x732afc: cmp             w2, w16
    // 0x732b00: b.eq            #0x732b34
    // 0x732b04: r16 = Instance_CrossAxisAlignment
    //     0x732b04: add             x16, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0x732b08: ldr             x16, [x16, #0x740]
    // 0x732b0c: cmp             w2, w16
    // 0x732b10: b.eq            #0x732b34
    // 0x732b14: r16 = Instance_CrossAxisAlignment
    //     0x732b14: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fa8] Obj!CrossAxisAlignment@e359c1
    //     0x732b18: ldr             x16, [x16, #0xfa8]
    // 0x732b1c: cmp             w2, w16
    // 0x732b20: b.eq            #0x732b34
    // 0x732b24: r16 = Instance_CrossAxisAlignment
    //     0x732b24: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0x732b28: ldr             x16, [x16, #0xf50]
    // 0x732b2c: cmp             w2, w16
    // 0x732b30: b.ne            #0x732b3c
    // 0x732b34: r0 = false
    //     0x732b34: add             x0, NULL, #0x30  ; false
    // 0x732b38: b               #0x732b40
    // 0x732b3c: r0 = Null
    //     0x732b3c: mov             x0, NULL
    // 0x732b40: ret
    //     0x732b40: ret             
  }
  _ _constraintsForNonFlexChild(/* No info */) {
    // ** addr: 0x732b44, size: 0x160
    // 0x732b44: EnterFrame
    //     0x732b44: stp             fp, lr, [SP, #-0x10]!
    //     0x732b48: mov             fp, SP
    // 0x732b4c: AllocStack(0x8)
    //     0x732b4c: sub             SP, SP, #8
    // 0x732b50: LoadField: r0 = r1->field_77
    //     0x732b50: ldur            w0, [x1, #0x77]
    // 0x732b54: DecompressPointer r0
    //     0x732b54: add             x0, x0, HEAP, lsl #32
    // 0x732b58: r16 = Instance_CrossAxisAlignment
    //     0x732b58: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0x732b5c: ldr             x16, [x16, #0xf50]
    // 0x732b60: cmp             w0, w16
    // 0x732b64: b.ne            #0x732b70
    // 0x732b68: r0 = true
    //     0x732b68: add             x0, NULL, #0x20  ; true
    // 0x732b6c: b               #0x732bbc
    // 0x732b70: r16 = Instance_CrossAxisAlignment
    //     0x732b70: add             x16, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0x732b74: ldr             x16, [x16, #0x68]
    // 0x732b78: cmp             w0, w16
    // 0x732b7c: b.eq            #0x732bb0
    // 0x732b80: r16 = Instance_CrossAxisAlignment
    //     0x732b80: add             x16, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0x732b84: ldr             x16, [x16, #0x740]
    // 0x732b88: cmp             w0, w16
    // 0x732b8c: b.eq            #0x732bb0
    // 0x732b90: r16 = Instance_CrossAxisAlignment
    //     0x732b90: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fa8] Obj!CrossAxisAlignment@e359c1
    //     0x732b94: ldr             x16, [x16, #0xfa8]
    // 0x732b98: cmp             w0, w16
    // 0x732b9c: b.eq            #0x732bb0
    // 0x732ba0: r16 = Instance_CrossAxisAlignment
    //     0x732ba0: add             x16, PP, #0x45, lsl #12  ; [pp+0x456b8] Obj!CrossAxisAlignment@e359a1
    //     0x732ba4: ldr             x16, [x16, #0x6b8]
    // 0x732ba8: cmp             w0, w16
    // 0x732bac: b.ne            #0x732bb8
    // 0x732bb0: r0 = false
    //     0x732bb0: add             x0, NULL, #0x30  ; false
    // 0x732bb4: b               #0x732bbc
    // 0x732bb8: r0 = Null
    //     0x732bb8: mov             x0, NULL
    // 0x732bbc: LoadField: r3 = r1->field_6b
    //     0x732bbc: ldur            w3, [x1, #0x6b]
    // 0x732bc0: DecompressPointer r3
    //     0x732bc0: add             x3, x3, HEAP, lsl #32
    // 0x732bc4: LoadField: r1 = r3->field_7
    //     0x732bc4: ldur            x1, [x3, #7]
    // 0x732bc8: cmp             x1, #0
    // 0x732bcc: b.gt            #0x732c30
    // 0x732bd0: r16 = true
    //     0x732bd0: add             x16, NULL, #0x20  ; true
    // 0x732bd4: cmp             w0, w16
    // 0x732bd8: b.ne            #0x732c04
    // 0x732bdc: LoadField: d0 = r2->field_1f
    //     0x732bdc: ldur            d0, [x2, #0x1f]
    // 0x732be0: stur            d0, [fp, #-8]
    // 0x732be4: r0 = BoxConstraints()
    //     0x732be4: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x732be8: StoreField: r0->field_7 = rZR
    //     0x732be8: stur            xzr, [x0, #7]
    // 0x732bec: d0 = inf
    //     0x732bec: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x732bf0: StoreField: r0->field_f = d0
    //     0x732bf0: stur            d0, [x0, #0xf]
    // 0x732bf4: ldur            d0, [fp, #-8]
    // 0x732bf8: ArrayStore: r0[0] = d0  ; List_8
    //     0x732bf8: stur            d0, [x0, #0x17]
    // 0x732bfc: StoreField: r0->field_1f = d0
    //     0x732bfc: stur            d0, [x0, #0x1f]
    // 0x732c00: b               #0x732c98
    // 0x732c04: d0 = inf
    //     0x732c04: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x732c08: LoadField: d1 = r2->field_1f
    //     0x732c08: ldur            d1, [x2, #0x1f]
    // 0x732c0c: stur            d1, [fp, #-8]
    // 0x732c10: r0 = BoxConstraints()
    //     0x732c10: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x732c14: StoreField: r0->field_7 = rZR
    //     0x732c14: stur            xzr, [x0, #7]
    // 0x732c18: d0 = inf
    //     0x732c18: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x732c1c: StoreField: r0->field_f = d0
    //     0x732c1c: stur            d0, [x0, #0xf]
    // 0x732c20: ArrayStore: r0[0] = rZR  ; List_8
    //     0x732c20: stur            xzr, [x0, #0x17]
    // 0x732c24: ldur            d0, [fp, #-8]
    // 0x732c28: StoreField: r0->field_1f = d0
    //     0x732c28: stur            d0, [x0, #0x1f]
    // 0x732c2c: b               #0x732c98
    // 0x732c30: d0 = inf
    //     0x732c30: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x732c34: r16 = true
    //     0x732c34: add             x16, NULL, #0x20  ; true
    // 0x732c38: cmp             w0, w16
    // 0x732c3c: b.ne            #0x732c6c
    // 0x732c40: LoadField: d1 = r2->field_f
    //     0x732c40: ldur            d1, [x2, #0xf]
    // 0x732c44: stur            d1, [fp, #-8]
    // 0x732c48: r0 = BoxConstraints()
    //     0x732c48: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x732c4c: ldur            d0, [fp, #-8]
    // 0x732c50: StoreField: r0->field_7 = d0
    //     0x732c50: stur            d0, [x0, #7]
    // 0x732c54: StoreField: r0->field_f = d0
    //     0x732c54: stur            d0, [x0, #0xf]
    // 0x732c58: ArrayStore: r0[0] = rZR  ; List_8
    //     0x732c58: stur            xzr, [x0, #0x17]
    // 0x732c5c: d0 = inf
    //     0x732c5c: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x732c60: StoreField: r0->field_1f = d0
    //     0x732c60: stur            d0, [x0, #0x1f]
    // 0x732c64: mov             x1, x0
    // 0x732c68: b               #0x732c94
    // 0x732c6c: LoadField: d1 = r2->field_f
    //     0x732c6c: ldur            d1, [x2, #0xf]
    // 0x732c70: stur            d1, [fp, #-8]
    // 0x732c74: r0 = BoxConstraints()
    //     0x732c74: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x732c78: StoreField: r0->field_7 = rZR
    //     0x732c78: stur            xzr, [x0, #7]
    // 0x732c7c: ldur            d0, [fp, #-8]
    // 0x732c80: StoreField: r0->field_f = d0
    //     0x732c80: stur            d0, [x0, #0xf]
    // 0x732c84: ArrayStore: r0[0] = rZR  ; List_8
    //     0x732c84: stur            xzr, [x0, #0x17]
    // 0x732c88: d0 = inf
    //     0x732c88: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x732c8c: StoreField: r0->field_1f = d0
    //     0x732c8c: stur            d0, [x0, #0x1f]
    // 0x732c90: mov             x1, x0
    // 0x732c94: mov             x0, x1
    // 0x732c98: LeaveFrame
    //     0x732c98: mov             SP, fp
    //     0x732c9c: ldp             fp, lr, [SP], #0x10
    // 0x732ca0: ret
    //     0x732ca0: ret             
  }
  _ _getMainSize(/* No info */) {
    // ** addr: 0x732ca4, size: 0x2c
    // 0x732ca4: LoadField: r0 = r1->field_6b
    //     0x732ca4: ldur            w0, [x1, #0x6b]
    // 0x732ca8: DecompressPointer r0
    //     0x732ca8: add             x0, x0, HEAP, lsl #32
    // 0x732cac: LoadField: r1 = r0->field_7
    //     0x732cac: ldur            x1, [x0, #7]
    // 0x732cb0: cmp             x1, #0
    // 0x732cb4: b.gt            #0x732cc4
    // 0x732cb8: LoadField: d1 = r2->field_7
    //     0x732cb8: ldur            d1, [x2, #7]
    // 0x732cbc: mov             v0.16b, v1.16b
    // 0x732cc0: b               #0x732ccc
    // 0x732cc4: LoadField: d1 = r2->field_f
    //     0x732cc4: ldur            d1, [x2, #0xf]
    // 0x732cc8: mov             v0.16b, v1.16b
    // 0x732ccc: ret
    //     0x732ccc: ret             
  }
  [closure] Size layoutChild(dynamic, RenderBox, BoxConstraints) {
    // ** addr: 0x732cd0, size: 0x1b4
    // 0x732cd0: EnterFrame
    //     0x732cd0: stp             fp, lr, [SP, #-0x10]!
    //     0x732cd4: mov             fp, SP
    // 0x732cd8: AllocStack(0x38)
    //     0x732cd8: sub             SP, SP, #0x38
    // 0x732cdc: SetupParameters()
    //     0x732cdc: ldr             x0, [fp, #0x20]
    //     0x732ce0: ldur            w2, [x0, #0x17]
    //     0x732ce4: add             x2, x2, HEAP, lsl #32
    //     0x732ce8: stur            x2, [fp, #-0x10]
    // 0x732cec: CheckStackOverflow
    //     0x732cec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x732cf0: cmp             SP, x16
    //     0x732cf4: b.ls            #0x732e4c
    // 0x732cf8: LoadField: r0 = r2->field_13
    //     0x732cf8: ldur            w0, [x2, #0x13]
    // 0x732cfc: DecompressPointer r0
    //     0x732cfc: add             x0, x0, HEAP, lsl #32
    // 0x732d00: stur            x0, [fp, #-8]
    // 0x732d04: tbnz            w0, #4, #0x732d14
    // 0x732d08: ldr             x1, [fp, #0x10]
    // 0x732d0c: LoadField: d0 = r1->field_f
    //     0x732d0c: ldur            d0, [x1, #0xf]
    // 0x732d10: b               #0x732d1c
    // 0x732d14: ldr             x1, [fp, #0x10]
    // 0x732d18: LoadField: d0 = r1->field_1f
    //     0x732d18: ldur            d0, [x1, #0x1f]
    // 0x732d1c: mov             x1, v0.d[0]
    // 0x732d20: and             x1, x1, #0x7fffffffffffffff
    // 0x732d24: r17 = 9218868437227405312
    //     0x732d24: orr             x17, xzr, #0x7ff0000000000000
    // 0x732d28: cmp             x1, x17
    // 0x732d2c: b.eq            #0x732d38
    // 0x732d30: fcmp            d0, d0
    // 0x732d34: b.vc            #0x732d5c
    // 0x732d38: tbnz            w0, #4, #0x732d4c
    // 0x732d3c: ldr             x1, [fp, #0x18]
    // 0x732d40: d0 = inf
    //     0x732d40: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x732d44: r0 = getMaxIntrinsicWidth()
    //     0x732d44: bl              #0x72d4b4  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicWidth
    // 0x732d48: b               #0x732d58
    // 0x732d4c: ldr             x1, [fp, #0x18]
    // 0x732d50: d0 = inf
    //     0x732d50: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x732d54: r0 = getMaxIntrinsicHeight()
    //     0x732d54: bl              #0x72d954  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicHeight
    // 0x732d58: ldur            x0, [fp, #-8]
    // 0x732d5c: stur            d0, [fp, #-0x18]
    // 0x732d60: tbnz            w0, #4, #0x732dd4
    // 0x732d64: ldur            x0, [fp, #-0x10]
    // 0x732d68: LoadField: r1 = r0->field_f
    //     0x732d68: ldur            w1, [x0, #0xf]
    // 0x732d6c: DecompressPointer r1
    //     0x732d6c: add             x1, x1, HEAP, lsl #32
    // 0x732d70: r0 = inline_Allocate_Double()
    //     0x732d70: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x732d74: add             x0, x0, #0x10
    //     0x732d78: cmp             x2, x0
    //     0x732d7c: b.ls            #0x732e54
    //     0x732d80: str             x0, [THR, #0x50]  ; THR::top
    //     0x732d84: sub             x0, x0, #0xf
    //     0x732d88: movz            x2, #0xe15c
    //     0x732d8c: movk            x2, #0x3, lsl #16
    //     0x732d90: stur            x2, [x0, #-1]
    // 0x732d94: StoreField: r0->field_7 = d0
    //     0x732d94: stur            d0, [x0, #7]
    // 0x732d98: ldr             x16, [fp, #0x18]
    // 0x732d9c: stp             x16, x1, [SP, #8]
    // 0x732da0: str             x0, [SP]
    // 0x732da4: mov             x0, x1
    // 0x732da8: ClosureCall
    //     0x732da8: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x732dac: ldur            x2, [x0, #0x1f]
    //     0x732db0: blr             x2
    // 0x732db4: stur            x0, [fp, #-8]
    // 0x732db8: r0 = Size()
    //     0x732db8: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x732dbc: ldur            d0, [fp, #-0x18]
    // 0x732dc0: StoreField: r0->field_7 = d0
    //     0x732dc0: stur            d0, [x0, #7]
    // 0x732dc4: ldur            x1, [fp, #-8]
    // 0x732dc8: LoadField: d0 = r1->field_7
    //     0x732dc8: ldur            d0, [x1, #7]
    // 0x732dcc: StoreField: r0->field_f = d0
    //     0x732dcc: stur            d0, [x0, #0xf]
    // 0x732dd0: b               #0x732e40
    // 0x732dd4: ldur            x0, [fp, #-0x10]
    // 0x732dd8: LoadField: r1 = r0->field_f
    //     0x732dd8: ldur            w1, [x0, #0xf]
    // 0x732ddc: DecompressPointer r1
    //     0x732ddc: add             x1, x1, HEAP, lsl #32
    // 0x732de0: r0 = inline_Allocate_Double()
    //     0x732de0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x732de4: add             x0, x0, #0x10
    //     0x732de8: cmp             x2, x0
    //     0x732dec: b.ls            #0x732e6c
    //     0x732df0: str             x0, [THR, #0x50]  ; THR::top
    //     0x732df4: sub             x0, x0, #0xf
    //     0x732df8: movz            x2, #0xe15c
    //     0x732dfc: movk            x2, #0x3, lsl #16
    //     0x732e00: stur            x2, [x0, #-1]
    // 0x732e04: StoreField: r0->field_7 = d0
    //     0x732e04: stur            d0, [x0, #7]
    // 0x732e08: ldr             x16, [fp, #0x18]
    // 0x732e0c: stp             x16, x1, [SP, #8]
    // 0x732e10: str             x0, [SP]
    // 0x732e14: mov             x0, x1
    // 0x732e18: ClosureCall
    //     0x732e18: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x732e1c: ldur            x2, [x0, #0x1f]
    //     0x732e20: blr             x2
    // 0x732e24: LoadField: d0 = r0->field_7
    //     0x732e24: ldur            d0, [x0, #7]
    // 0x732e28: stur            d0, [fp, #-0x20]
    // 0x732e2c: r0 = Size()
    //     0x732e2c: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x732e30: ldur            d0, [fp, #-0x20]
    // 0x732e34: StoreField: r0->field_7 = d0
    //     0x732e34: stur            d0, [x0, #7]
    // 0x732e38: ldur            d0, [fp, #-0x18]
    // 0x732e3c: StoreField: r0->field_f = d0
    //     0x732e3c: stur            d0, [x0, #0xf]
    // 0x732e40: LeaveFrame
    //     0x732e40: mov             SP, fp
    //     0x732e44: ldp             fp, lr, [SP], #0x10
    // 0x732e48: ret
    //     0x732e48: ret             
    // 0x732e4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x732e4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x732e50: b               #0x732cf8
    // 0x732e54: SaveReg d0
    //     0x732e54: str             q0, [SP, #-0x10]!
    // 0x732e58: SaveReg r1
    //     0x732e58: str             x1, [SP, #-8]!
    // 0x732e5c: r0 = AllocateDouble()
    //     0x732e5c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x732e60: RestoreReg r1
    //     0x732e60: ldr             x1, [SP], #8
    // 0x732e64: RestoreReg d0
    //     0x732e64: ldr             q0, [SP], #0x10
    // 0x732e68: b               #0x732d94
    // 0x732e6c: SaveReg d0
    //     0x732e6c: str             q0, [SP, #-0x10]!
    // 0x732e70: SaveReg r1
    //     0x732e70: str             x1, [SP, #-8]!
    // 0x732e74: r0 = AllocateDouble()
    //     0x732e74: bl              #0xec2254  ; AllocateDoubleStub
    // 0x732e78: RestoreReg r1
    //     0x732e78: ldr             x1, [SP], #8
    // 0x732e7c: RestoreReg d0
    //     0x732e7c: ldr             q0, [SP], #0x10
    // 0x732e80: b               #0x732e04
  }
  [closure] double <anonymous closure>(dynamic, RenderBox, double) {
    // ** addr: 0x732e84, size: 0x70
    // 0x732e84: EnterFrame
    //     0x732e84: stp             fp, lr, [SP, #-0x10]!
    //     0x732e88: mov             fp, SP
    // 0x732e8c: CheckStackOverflow
    //     0x732e8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x732e90: cmp             SP, x16
    //     0x732e94: b.ls            #0x732edc
    // 0x732e98: ldr             x0, [fp, #0x10]
    // 0x732e9c: LoadField: d0 = r0->field_7
    //     0x732e9c: ldur            d0, [x0, #7]
    // 0x732ea0: ldr             x1, [fp, #0x18]
    // 0x732ea4: r0 = getMinIntrinsicWidth()
    //     0x732ea4: bl              #0x72d27c  ; [package:flutter/src/rendering/box.dart] RenderBox::getMinIntrinsicWidth
    // 0x732ea8: r0 = inline_Allocate_Double()
    //     0x732ea8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x732eac: add             x0, x0, #0x10
    //     0x732eb0: cmp             x1, x0
    //     0x732eb4: b.ls            #0x732ee4
    //     0x732eb8: str             x0, [THR, #0x50]  ; THR::top
    //     0x732ebc: sub             x0, x0, #0xf
    //     0x732ec0: movz            x1, #0xe15c
    //     0x732ec4: movk            x1, #0x3, lsl #16
    //     0x732ec8: stur            x1, [x0, #-1]
    // 0x732ecc: StoreField: r0->field_7 = d0
    //     0x732ecc: stur            d0, [x0, #7]
    // 0x732ed0: LeaveFrame
    //     0x732ed0: mov             SP, fp
    //     0x732ed4: ldp             fp, lr, [SP], #0x10
    // 0x732ed8: ret
    //     0x732ed8: ret             
    // 0x732edc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x732edc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x732ee0: b               #0x732e98
    // 0x732ee4: SaveReg d0
    //     0x732ee4: str             q0, [SP, #-0x10]!
    // 0x732ee8: r0 = AllocateDouble()
    //     0x732ee8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x732eec: RestoreReg d0
    //     0x732eec: ldr             q0, [SP], #0x10
    // 0x732ef0: b               #0x732ecc
  }
  _ computeDryBaseline(/* No info */) {
    // ** addr: 0x742214, size: 0x89c
    // 0x742214: EnterFrame
    //     0x742214: stp             fp, lr, [SP, #-0x10]!
    //     0x742218: mov             fp, SP
    // 0x74221c: AllocStack(0x98)
    //     0x74221c: sub             SP, SP, #0x98
    // 0x742220: SetupParameters(RenderFlex this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x742220: mov             x5, x1
    //     0x742224: mov             x4, x2
    //     0x742228: stur            x1, [fp, #-8]
    //     0x74222c: stur            x2, [fp, #-0x10]
    //     0x742230: stur            x3, [fp, #-0x18]
    // 0x742234: CheckStackOverflow
    //     0x742234: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x742238: cmp             SP, x16
    //     0x74223c: b.ls            #0x742a2c
    // 0x742240: mov             x0, x4
    // 0x742244: r2 = Null
    //     0x742244: mov             x2, NULL
    // 0x742248: r1 = Null
    //     0x742248: mov             x1, NULL
    // 0x74224c: r4 = 60
    //     0x74224c: movz            x4, #0x3c
    // 0x742250: branchIfSmi(r0, 0x74225c)
    //     0x742250: tbz             w0, #0, #0x74225c
    // 0x742254: r4 = LoadClassIdInstr(r0)
    //     0x742254: ldur            x4, [x0, #-1]
    //     0x742258: ubfx            x4, x4, #0xc, #0x14
    // 0x74225c: sub             x4, x4, #0xc83
    // 0x742260: cmp             x4, #1
    // 0x742264: b.ls            #0x742278
    // 0x742268: r8 = BoxConstraints
    //     0x742268: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x74226c: r3 = Null
    //     0x74226c: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4ef00] Null
    //     0x742270: ldr             x3, [x3, #0xf00]
    // 0x742274: r0 = BoxConstraints()
    //     0x742274: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x742278: ldur            x1, [fp, #-8]
    // 0x74227c: ldur            x2, [fp, #-0x10]
    // 0x742280: r3 = Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getDryBaseline': static.
    //     0x742280: add             x3, PP, #0x45, lsl #12  ; [pp+0x45748] Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getDryBaseline': static. (0x7e54fb130f1c)
    //     0x742284: ldr             x3, [x3, #0x748]
    // 0x742288: r5 = Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static.
    //     0x742288: add             x5, PP, #0x44, lsl #12  ; [pp+0x44d20] Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static. (0x7e54fb140c14)
    //     0x74228c: ldr             x5, [x5, #0xd20]
    // 0x742290: r0 = _computeSizes()
    //     0x742290: bl              #0x7317d4  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_computeSizes
    // 0x742294: ldur            x1, [fp, #-8]
    // 0x742298: stur            x0, [fp, #-0x20]
    // 0x74229c: r0 = _isBaselineAligned()
    //     0x74229c: bl              #0x732ab4  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_isBaselineAligned
    // 0x7422a0: tbnz            w0, #4, #0x7422c0
    // 0x7422a4: ldur            x0, [fp, #-0x20]
    // 0x7422a8: LoadField: r1 = r0->field_13
    //     0x7422a8: ldur            w1, [x0, #0x13]
    // 0x7422ac: DecompressPointer r1
    //     0x7422ac: add             x1, x1, HEAP, lsl #32
    // 0x7422b0: mov             x0, x1
    // 0x7422b4: LeaveFrame
    //     0x7422b4: mov             SP, fp
    //     0x7422b8: ldp             fp, lr, [SP], #0x10
    // 0x7422bc: ret
    //     0x7422bc: ret             
    // 0x7422c0: ldur            x3, [fp, #-8]
    // 0x7422c4: ldur            x0, [fp, #-0x20]
    // 0x7422c8: mov             x1, x3
    // 0x7422cc: ldur            x2, [fp, #-0x10]
    // 0x7422d0: r0 = _constraintsForNonFlexChild()
    //     0x7422d0: bl              #0x732b44  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_constraintsForNonFlexChild
    // 0x7422d4: mov             x2, x0
    // 0x7422d8: ldur            x0, [fp, #-8]
    // 0x7422dc: stur            x2, [fp, #-0x28]
    // 0x7422e0: LoadField: r1 = r0->field_6b
    //     0x7422e0: ldur            w1, [x0, #0x6b]
    // 0x7422e4: DecompressPointer r1
    //     0x7422e4: add             x1, x1, HEAP, lsl #32
    // 0x7422e8: LoadField: r3 = r1->field_7
    //     0x7422e8: ldur            x3, [x1, #7]
    // 0x7422ec: cmp             x3, #0
    // 0x7422f0: b.gt            #0x74264c
    // 0x7422f4: ldur            x3, [fp, #-0x20]
    // 0x7422f8: mov             x1, x0
    // 0x7422fc: r0 = _flipCrossAxis()
    //     0x7422fc: bl              #0x74300c  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_flipCrossAxis
    // 0x742300: mov             x4, x0
    // 0x742304: ldur            x3, [fp, #-8]
    // 0x742308: stur            x4, [fp, #-0x50]
    // 0x74230c: LoadField: r0 = r3->field_5f
    //     0x74230c: ldur            w0, [x3, #0x5f]
    // 0x742310: DecompressPointer r0
    //     0x742310: add             x0, x0, HEAP, lsl #32
    // 0x742314: ldur            x2, [fp, #-0x20]
    // 0x742318: ArrayLoad: r5 = r2[0]  ; List_4
    //     0x742318: ldur            w5, [x2, #0x17]
    // 0x74231c: DecompressPointer r5
    //     0x74231c: add             x5, x5, HEAP, lsl #32
    // 0x742320: stur            x5, [fp, #-0x48]
    // 0x742324: LoadField: r1 = r2->field_7
    //     0x742324: ldur            w1, [x2, #7]
    // 0x742328: DecompressPointer r1
    //     0x742328: add             x1, x1, HEAP, lsl #32
    // 0x74232c: LoadField: d0 = r1->field_f
    //     0x74232c: ldur            d0, [x1, #0xf]
    // 0x742330: stur            d0, [fp, #-0x60]
    // 0x742334: mov             x6, x0
    // 0x742338: r7 = Null
    //     0x742338: mov             x7, NULL
    // 0x74233c: stur            x7, [fp, #-0x38]
    // 0x742340: stur            x6, [fp, #-0x40]
    // 0x742344: CheckStackOverflow
    //     0x742344: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x742348: cmp             SP, x16
    //     0x74234c: b.ls            #0x742a34
    // 0x742350: cmp             w6, NULL
    // 0x742354: b.eq            #0x742644
    // 0x742358: cmp             w5, NULL
    // 0x74235c: b.eq            #0x7423fc
    // 0x742360: LoadField: r8 = r6->field_7
    //     0x742360: ldur            w8, [x6, #7]
    // 0x742364: DecompressPointer r8
    //     0x742364: add             x8, x8, HEAP, lsl #32
    // 0x742368: stur            x8, [fp, #-0x30]
    // 0x74236c: cmp             w8, NULL
    // 0x742370: b.eq            #0x742a3c
    // 0x742374: mov             x0, x8
    // 0x742378: r2 = Null
    //     0x742378: mov             x2, NULL
    // 0x74237c: r1 = Null
    //     0x74237c: mov             x1, NULL
    // 0x742380: r4 = LoadClassIdInstr(r0)
    //     0x742380: ldur            x4, [x0, #-1]
    //     0x742384: ubfx            x4, x4, #0xc, #0x14
    // 0x742388: cmp             x4, #0xc7e
    // 0x74238c: b.eq            #0x7423a4
    // 0x742390: r8 = FlexParentData
    //     0x742390: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x742394: ldr             x8, [x8, #0x590]
    // 0x742398: r3 = Null
    //     0x742398: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4ef10] Null
    //     0x74239c: ldr             x3, [x3, #0xf10]
    // 0x7423a0: r0 = DefaultTypeTest()
    //     0x7423a0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7423a4: ldur            x0, [fp, #-0x30]
    // 0x7423a8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7423a8: ldur            w1, [x0, #0x17]
    // 0x7423ac: DecompressPointer r1
    //     0x7423ac: add             x1, x1, HEAP, lsl #32
    // 0x7423b0: cmp             w1, NULL
    // 0x7423b4: b.ne            #0x7423c0
    // 0x7423b8: r0 = 0
    //     0x7423b8: movz            x0, #0
    // 0x7423bc: b               #0x7423cc
    // 0x7423c0: r0 = LoadInt32Instr(r1)
    //     0x7423c0: sbfx            x0, x1, #1, #0x1f
    //     0x7423c4: tbz             w1, #0, #0x7423cc
    //     0x7423c8: ldur            x0, [x1, #7]
    // 0x7423cc: cmp             x0, #0
    // 0x7423d0: b.le            #0x7423fc
    // 0x7423d4: ldur            x4, [fp, #-0x48]
    // 0x7423d8: scvtf           d0, x0
    // 0x7423dc: LoadField: d1 = r4->field_7
    //     0x7423dc: ldur            d1, [x4, #7]
    // 0x7423e0: fmul            d2, d0, d1
    // 0x7423e4: ldur            x1, [fp, #-8]
    // 0x7423e8: ldur            x2, [fp, #-0x40]
    // 0x7423ec: ldur            x3, [fp, #-0x10]
    // 0x7423f0: mov             v0.16b, v2.16b
    // 0x7423f4: r0 = _constraintsForFlexChild()
    //     0x7423f4: bl              #0x732460  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_constraintsForFlexChild
    // 0x7423f8: b               #0x742400
    // 0x7423fc: ldur            x0, [fp, #-0x28]
    // 0x742400: ldur            x1, [fp, #-8]
    // 0x742404: ldur            d0, [fp, #-0x60]
    // 0x742408: mov             x2, x0
    // 0x74240c: ldur            x3, [fp, #-0x18]
    // 0x742410: stur            x0, [fp, #-0x30]
    // 0x742414: r0 = AllocateRecord2()
    //     0x742414: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x742418: ldur            x2, [fp, #-0x40]
    // 0x74241c: r1 = Function '_computeDryBaseline@378392247':.
    //     0x74241c: add             x1, PP, #0x45, lsl #12  ; [pp+0x45750] AnonymousClosure: (0x730fcc), in [package:flutter/src/rendering/box.dart] RenderBox::_computeDryBaseline (0x731008)
    //     0x742420: ldr             x1, [x1, #0x750]
    // 0x742424: stur            x0, [fp, #-0x58]
    // 0x742428: r0 = AllocateClosure()
    //     0x742428: bl              #0xec1630  ; AllocateClosureStub
    // 0x74242c: r16 = <(BoxConstraints, TextBaseline), double?>
    //     0x74242c: add             x16, PP, #0x45, lsl #12  ; [pp+0x456d8] TypeArguments: <(BoxConstraints, TextBaseline), double?>
    //     0x742430: ldr             x16, [x16, #0x6d8]
    // 0x742434: ldur            lr, [fp, #-0x40]
    // 0x742438: stp             lr, x16, [SP, #0x18]
    // 0x74243c: r16 = Instance__Baseline
    //     0x74243c: add             x16, PP, #0x45, lsl #12  ; [pp+0x456e0] Obj!_Baseline@e117c1
    //     0x742440: ldr             x16, [x16, #0x6e0]
    // 0x742444: ldur            lr, [fp, #-0x58]
    // 0x742448: stp             lr, x16, [SP, #8]
    // 0x74244c: str             x0, [SP]
    // 0x742450: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x742450: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x742454: r0 = _computeIntrinsics()
    //     0x742454: bl              #0x72d344  ; [package:flutter/src/rendering/box.dart] RenderBox::_computeIntrinsics
    // 0x742458: ldur            x2, [fp, #-0x40]
    // 0x74245c: r1 = Function '_computeDryLayout@378392247':.
    //     0x74245c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30e58] AnonymousClosure: (0x73be5c), in [package:flutter/src/rendering/box.dart] RenderBox::_computeDryLayout (0x73be98)
    //     0x742460: ldr             x1, [x1, #0xe58]
    // 0x742464: stur            x0, [fp, #-0x58]
    // 0x742468: r0 = AllocateClosure()
    //     0x742468: bl              #0xec1630  ; AllocateClosureStub
    // 0x74246c: r16 = <BoxConstraints, Size>
    //     0x74246c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30e60] TypeArguments: <BoxConstraints, Size>
    //     0x742470: ldr             x16, [x16, #0xe60]
    // 0x742474: ldur            lr, [fp, #-0x40]
    // 0x742478: stp             lr, x16, [SP, #0x18]
    // 0x74247c: r16 = Instance__DryLayout
    //     0x74247c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30e68] Obj!_DryLayout@e117d1
    //     0x742480: ldr             x16, [x16, #0xe68]
    // 0x742484: ldur            lr, [fp, #-0x30]
    // 0x742488: stp             lr, x16, [SP, #8]
    // 0x74248c: str             x0, [SP]
    // 0x742490: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x742490: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x742494: r0 = _computeIntrinsics()
    //     0x742494: bl              #0x72d344  ; [package:flutter/src/rendering/box.dart] RenderBox::_computeIntrinsics
    // 0x742498: LoadField: d0 = r0->field_f
    //     0x742498: ldur            d0, [x0, #0xf]
    // 0x74249c: ldur            d1, [fp, #-0x60]
    // 0x7424a0: fsub            d2, d1, d0
    // 0x7424a4: ldur            x0, [fp, #-8]
    // 0x7424a8: LoadField: r1 = r0->field_77
    //     0x7424a8: ldur            w1, [x0, #0x77]
    // 0x7424ac: DecompressPointer r1
    //     0x7424ac: add             x1, x1, HEAP, lsl #32
    // 0x7424b0: mov             v0.16b, v2.16b
    // 0x7424b4: ldur            x2, [fp, #-0x50]
    // 0x7424b8: r0 = _getChildCrossAxisOffset()
    //     0x7424b8: bl              #0x742eac  ; [package:flutter/src/rendering/flex.dart] CrossAxisAlignment::_getChildCrossAxisOffset
    // 0x7424bc: ldur            x0, [fp, #-0x58]
    // 0x7424c0: cmp             w0, NULL
    // 0x7424c4: b.ne            #0x7424d0
    // 0x7424c8: r1 = Null
    //     0x7424c8: mov             x1, NULL
    // 0x7424cc: b               #0x742504
    // 0x7424d0: LoadField: d1 = r0->field_7
    //     0x7424d0: ldur            d1, [x0, #7]
    // 0x7424d4: fadd            d2, d1, d0
    // 0x7424d8: r0 = inline_Allocate_Double()
    //     0x7424d8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7424dc: add             x0, x0, #0x10
    //     0x7424e0: cmp             x1, x0
    //     0x7424e4: b.ls            #0x742a40
    //     0x7424e8: str             x0, [THR, #0x50]  ; THR::top
    //     0x7424ec: sub             x0, x0, #0xf
    //     0x7424f0: movz            x1, #0xe15c
    //     0x7424f4: movk            x1, #0x3, lsl #16
    //     0x7424f8: stur            x1, [x0, #-1]
    // 0x7424fc: StoreField: r0->field_7 = d2
    //     0x7424fc: stur            d2, [x0, #7]
    // 0x742500: mov             x1, x0
    // 0x742504: ldur            x0, [fp, #-0x38]
    // 0x742508: cmp             w0, NULL
    // 0x74250c: b.eq            #0x74256c
    // 0x742510: cmp             w1, NULL
    // 0x742514: b.eq            #0x742564
    // 0x742518: LoadField: d0 = r0->field_7
    //     0x742518: ldur            d0, [x0, #7]
    // 0x74251c: LoadField: d1 = r1->field_7
    //     0x74251c: ldur            d1, [x1, #7]
    // 0x742520: fcmp            d0, d1
    // 0x742524: b.lt            #0x742530
    // 0x742528: LoadField: d0 = r1->field_7
    //     0x742528: ldur            d0, [x1, #7]
    // 0x74252c: b               #0x742534
    // 0x742530: LoadField: d0 = r0->field_7
    //     0x742530: ldur            d0, [x0, #7]
    // 0x742534: r0 = inline_Allocate_Double()
    //     0x742534: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x742538: add             x0, x0, #0x10
    //     0x74253c: cmp             x1, x0
    //     0x742540: b.ls            #0x742a50
    //     0x742544: str             x0, [THR, #0x50]  ; THR::top
    //     0x742548: sub             x0, x0, #0xf
    //     0x74254c: movz            x1, #0xe15c
    //     0x742550: movk            x1, #0x3, lsl #16
    //     0x742554: stur            x1, [x0, #-1]
    // 0x742558: StoreField: r0->field_7 = d0
    //     0x742558: stur            d0, [x0, #7]
    // 0x74255c: mov             x7, x0
    // 0x742560: b               #0x7425d4
    // 0x742564: r2 = true
    //     0x742564: add             x2, NULL, #0x20  ; true
    // 0x742568: b               #0x742570
    // 0x74256c: r2 = false
    //     0x74256c: add             x2, NULL, #0x30  ; false
    // 0x742570: cmp             w0, NULL
    // 0x742574: b.eq            #0x7425ac
    // 0x742578: tbnz            w2, #4, #0x742588
    // 0x74257c: r3 = Null
    //     0x74257c: mov             x3, NULL
    // 0x742580: r2 = Null
    //     0x742580: mov             x2, NULL
    // 0x742584: b               #0x742590
    // 0x742588: mov             x3, x1
    // 0x74258c: mov             x2, x1
    // 0x742590: cmp             w3, NULL
    // 0x742594: b.ne            #0x7425a0
    // 0x742598: mov             x7, x0
    // 0x74259c: b               #0x7425d4
    // 0x7425a0: mov             x3, x2
    // 0x7425a4: r2 = true
    //     0x7425a4: add             x2, NULL, #0x20  ; true
    // 0x7425a8: b               #0x7425b0
    // 0x7425ac: r3 = Null
    //     0x7425ac: mov             x3, NULL
    // 0x7425b0: cmp             w0, NULL
    // 0x7425b4: b.ne            #0x7425d0
    // 0x7425b8: tbnz            w2, #4, #0x7425c4
    // 0x7425bc: mov             x0, x3
    // 0x7425c0: b               #0x7425c8
    // 0x7425c4: mov             x0, x1
    // 0x7425c8: mov             x7, x0
    // 0x7425cc: b               #0x7425d4
    // 0x7425d0: r7 = Null
    //     0x7425d0: mov             x7, NULL
    // 0x7425d4: ldur            x0, [fp, #-0x40]
    // 0x7425d8: stur            x7, [fp, #-0x58]
    // 0x7425dc: LoadField: r3 = r0->field_7
    //     0x7425dc: ldur            w3, [x0, #7]
    // 0x7425e0: DecompressPointer r3
    //     0x7425e0: add             x3, x3, HEAP, lsl #32
    // 0x7425e4: stur            x3, [fp, #-0x30]
    // 0x7425e8: cmp             w3, NULL
    // 0x7425ec: b.eq            #0x742a60
    // 0x7425f0: mov             x0, x3
    // 0x7425f4: r2 = Null
    //     0x7425f4: mov             x2, NULL
    // 0x7425f8: r1 = Null
    //     0x7425f8: mov             x1, NULL
    // 0x7425fc: r4 = LoadClassIdInstr(r0)
    //     0x7425fc: ldur            x4, [x0, #-1]
    //     0x742600: ubfx            x4, x4, #0xc, #0x14
    // 0x742604: cmp             x4, #0xc7e
    // 0x742608: b.eq            #0x742620
    // 0x74260c: r8 = FlexParentData
    //     0x74260c: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x742610: ldr             x8, [x8, #0x590]
    // 0x742614: r3 = Null
    //     0x742614: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4ef20] Null
    //     0x742618: ldr             x3, [x3, #0xf20]
    // 0x74261c: r0 = DefaultTypeTest()
    //     0x74261c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x742620: ldur            x0, [fp, #-0x30]
    // 0x742624: LoadField: r6 = r0->field_13
    //     0x742624: ldur            w6, [x0, #0x13]
    // 0x742628: DecompressPointer r6
    //     0x742628: add             x6, x6, HEAP, lsl #32
    // 0x74262c: ldur            x7, [fp, #-0x58]
    // 0x742630: ldur            x3, [fp, #-8]
    // 0x742634: ldur            x4, [fp, #-0x50]
    // 0x742638: ldur            x5, [fp, #-0x48]
    // 0x74263c: ldur            d0, [fp, #-0x60]
    // 0x742640: b               #0x74233c
    // 0x742644: mov             x0, x7
    // 0x742648: b               #0x742a20
    // 0x74264c: ldur            x2, [fp, #-0x20]
    // 0x742650: d0 = 0.000000
    //     0x742650: eor             v0.16b, v0.16b, v0.16b
    // 0x742654: LoadField: d1 = r2->field_b
    //     0x742654: ldur            d1, [x2, #0xb]
    // 0x742658: stur            d1, [fp, #-0x68]
    // 0x74265c: fcmp            d0, d1
    // 0x742660: b.le            #0x74266c
    // 0x742664: d0 = 0.000000
    //     0x742664: eor             v0.16b, v0.16b, v0.16b
    // 0x742668: b               #0x7426a4
    // 0x74266c: fcmp            d1, d0
    // 0x742670: b.le            #0x74267c
    // 0x742674: mov             v0.16b, v1.16b
    // 0x742678: b               #0x7426a4
    // 0x74267c: fcmp            d0, d0
    // 0x742680: b.ne            #0x742690
    // 0x742684: fadd            d2, d1, d0
    // 0x742688: mov             v0.16b, v2.16b
    // 0x74268c: b               #0x7426a4
    // 0x742690: fcmp            d1, d1
    // 0x742694: b.vc            #0x7426a0
    // 0x742698: mov             v0.16b, v1.16b
    // 0x74269c: b               #0x7426a4
    // 0x7426a0: d0 = 0.000000
    //     0x7426a0: eor             v0.16b, v0.16b, v0.16b
    // 0x7426a4: ldur            x0, [fp, #-8]
    // 0x7426a8: mov             x1, x0
    // 0x7426ac: stur            d0, [fp, #-0x60]
    // 0x7426b0: r0 = _flipMainAxis()
    //     0x7426b0: bl              #0x742e34  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_flipMainAxis
    // 0x7426b4: mov             x4, x0
    // 0x7426b8: ldur            x0, [fp, #-8]
    // 0x7426bc: stur            x4, [fp, #-0x30]
    // 0x7426c0: LoadField: r1 = r0->field_6f
    //     0x7426c0: ldur            w1, [x0, #0x6f]
    // 0x7426c4: DecompressPointer r1
    //     0x7426c4: add             x1, x1, HEAP, lsl #32
    // 0x7426c8: LoadField: r2 = r0->field_57
    //     0x7426c8: ldur            x2, [x0, #0x57]
    // 0x7426cc: ldur            d0, [fp, #-0x60]
    // 0x7426d0: mov             x3, x4
    // 0x7426d4: r0 = _distributeSpace()
    //     0x7426d4: bl              #0x742ab0  ; [package:flutter/src/rendering/flex.dart] MainAxisAlignment::_distributeSpace
    // 0x7426d8: mov             x2, x0
    // 0x7426dc: mov             x3, x1
    // 0x7426e0: ldur            x4, [fp, #-0x30]
    // 0x7426e4: stur            x3, [fp, #-0x48]
    // 0x7426e8: tbnz            w4, #4, #0x74272c
    // 0x7426ec: ldur            x5, [fp, #-8]
    // 0x7426f0: ldur            x0, [fp, #-0x20]
    // 0x7426f4: ldur            d0, [fp, #-0x68]
    // 0x7426f8: LoadField: r1 = r5->field_57
    //     0x7426f8: ldur            x1, [x5, #0x57]
    // 0x7426fc: sub             x6, x1, #1
    // 0x742700: scvtf           d1, x6
    // 0x742704: LoadField: d2 = r3->field_7
    //     0x742704: ldur            d2, [x3, #7]
    // 0x742708: fmul            d3, d1, d2
    // 0x74270c: LoadField: d1 = r2->field_7
    //     0x74270c: ldur            d1, [x2, #7]
    // 0x742710: fadd            d2, d1, d3
    // 0x742714: LoadField: r1 = r0->field_7
    //     0x742714: ldur            w1, [x0, #7]
    // 0x742718: DecompressPointer r1
    //     0x742718: add             x1, x1, HEAP, lsl #32
    // 0x74271c: LoadField: d1 = r1->field_7
    //     0x74271c: ldur            d1, [x1, #7]
    // 0x742720: fsub            d3, d1, d0
    // 0x742724: fadd            d0, d2, d3
    // 0x742728: b               #0x742738
    // 0x74272c: ldur            x5, [fp, #-8]
    // 0x742730: ldur            x0, [fp, #-0x20]
    // 0x742734: LoadField: d0 = r2->field_7
    //     0x742734: ldur            d0, [x2, #7]
    // 0x742738: tbnz            w4, #4, #0x742744
    // 0x74273c: d1 = -1.000000
    //     0x74273c: fmov            d1, #-1.00000000
    // 0x742740: b               #0x742748
    // 0x742744: d1 = 1.000000
    //     0x742744: fmov            d1, #1.00000000
    // 0x742748: stur            d1, [fp, #-0x68]
    // 0x74274c: LoadField: r1 = r5->field_5f
    //     0x74274c: ldur            w1, [x5, #0x5f]
    // 0x742750: DecompressPointer r1
    //     0x742750: add             x1, x1, HEAP, lsl #32
    // 0x742754: ArrayLoad: r6 = r0[0]  ; List_4
    //     0x742754: ldur            w6, [x0, #0x17]
    // 0x742758: DecompressPointer r6
    //     0x742758: add             x6, x6, HEAP, lsl #32
    // 0x74275c: stur            x6, [fp, #-0x40]
    // 0x742760: mov             x7, x1
    // 0x742764: r0 = Null
    //     0x742764: mov             x0, NULL
    // 0x742768: stur            x7, [fp, #-0x38]
    // 0x74276c: stur            d0, [fp, #-0x60]
    // 0x742770: CheckStackOverflow
    //     0x742770: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x742774: cmp             SP, x16
    //     0x742778: b.ls            #0x742a64
    // 0x74277c: cmp             w0, NULL
    // 0x742780: b.ne            #0x742a20
    // 0x742784: cmp             w7, NULL
    // 0x742788: b.eq            #0x742a20
    // 0x74278c: cmp             w6, NULL
    // 0x742790: b.eq            #0x74283c
    // 0x742794: LoadField: r8 = r7->field_7
    //     0x742794: ldur            w8, [x7, #7]
    // 0x742798: DecompressPointer r8
    //     0x742798: add             x8, x8, HEAP, lsl #32
    // 0x74279c: stur            x8, [fp, #-0x20]
    // 0x7427a0: cmp             w8, NULL
    // 0x7427a4: b.eq            #0x742a6c
    // 0x7427a8: mov             x0, x8
    // 0x7427ac: r2 = Null
    //     0x7427ac: mov             x2, NULL
    // 0x7427b0: r1 = Null
    //     0x7427b0: mov             x1, NULL
    // 0x7427b4: r4 = LoadClassIdInstr(r0)
    //     0x7427b4: ldur            x4, [x0, #-1]
    //     0x7427b8: ubfx            x4, x4, #0xc, #0x14
    // 0x7427bc: cmp             x4, #0xc7e
    // 0x7427c0: b.eq            #0x7427d8
    // 0x7427c4: r8 = FlexParentData
    //     0x7427c4: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x7427c8: ldr             x8, [x8, #0x590]
    // 0x7427cc: r3 = Null
    //     0x7427cc: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4ef30] Null
    //     0x7427d0: ldr             x3, [x3, #0xf30]
    // 0x7427d4: r0 = DefaultTypeTest()
    //     0x7427d4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7427d8: ldur            x0, [fp, #-0x20]
    // 0x7427dc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7427dc: ldur            w1, [x0, #0x17]
    // 0x7427e0: DecompressPointer r1
    //     0x7427e0: add             x1, x1, HEAP, lsl #32
    // 0x7427e4: cmp             w1, NULL
    // 0x7427e8: b.ne            #0x7427f4
    // 0x7427ec: r0 = 0
    //     0x7427ec: movz            x0, #0
    // 0x7427f0: b               #0x742804
    // 0x7427f4: r2 = LoadInt32Instr(r1)
    //     0x7427f4: sbfx            x2, x1, #1, #0x1f
    //     0x7427f8: tbz             w1, #0, #0x742800
    //     0x7427fc: ldur            x2, [x1, #7]
    // 0x742800: mov             x0, x2
    // 0x742804: cmp             x0, #0
    // 0x742808: b.le            #0x74283c
    // 0x74280c: ldur            x4, [fp, #-0x40]
    // 0x742810: scvtf           d0, x0
    // 0x742814: LoadField: d1 = r4->field_7
    //     0x742814: ldur            d1, [x4, #7]
    // 0x742818: fmul            d2, d0, d1
    // 0x74281c: ldur            x1, [fp, #-8]
    // 0x742820: ldur            x2, [fp, #-0x38]
    // 0x742824: ldur            x3, [fp, #-0x10]
    // 0x742828: mov             v0.16b, v2.16b
    // 0x74282c: r0 = _constraintsForFlexChild()
    //     0x74282c: bl              #0x732460  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_constraintsForFlexChild
    // 0x742830: mov             x1, x0
    // 0x742834: mov             x3, x1
    // 0x742838: b               #0x742840
    // 0x74283c: ldur            x3, [fp, #-0x28]
    // 0x742840: ldur            x0, [fp, #-0x30]
    // 0x742844: ldur            x2, [fp, #-0x38]
    // 0x742848: stur            x3, [fp, #-0x20]
    // 0x74284c: r1 = Function '_computeDryLayout@378392247':.
    //     0x74284c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30e58] AnonymousClosure: (0x73be5c), in [package:flutter/src/rendering/box.dart] RenderBox::_computeDryLayout (0x73be98)
    //     0x742850: ldr             x1, [x1, #0xe58]
    // 0x742854: r0 = AllocateClosure()
    //     0x742854: bl              #0xec1630  ; AllocateClosureStub
    // 0x742858: r16 = <BoxConstraints, Size>
    //     0x742858: add             x16, PP, #0x30, lsl #12  ; [pp+0x30e60] TypeArguments: <BoxConstraints, Size>
    //     0x74285c: ldr             x16, [x16, #0xe60]
    // 0x742860: ldur            lr, [fp, #-0x38]
    // 0x742864: stp             lr, x16, [SP, #0x18]
    // 0x742868: r16 = Instance__DryLayout
    //     0x742868: add             x16, PP, #0x30, lsl #12  ; [pp+0x30e68] Obj!_DryLayout@e117d1
    //     0x74286c: ldr             x16, [x16, #0xe68]
    // 0x742870: ldur            lr, [fp, #-0x20]
    // 0x742874: stp             lr, x16, [SP, #8]
    // 0x742878: str             x0, [SP]
    // 0x74287c: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x74287c: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x742880: r0 = _computeIntrinsics()
    //     0x742880: bl              #0x72d344  ; [package:flutter/src/rendering/box.dart] RenderBox::_computeIntrinsics
    // 0x742884: ldur            x2, [fp, #-0x20]
    // 0x742888: ldur            x3, [fp, #-0x18]
    // 0x74288c: stur            x0, [fp, #-0x20]
    // 0x742890: r0 = AllocateRecord2()
    //     0x742890: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x742894: ldur            x2, [fp, #-0x38]
    // 0x742898: r1 = Function '_computeDryBaseline@378392247':.
    //     0x742898: add             x1, PP, #0x45, lsl #12  ; [pp+0x45750] AnonymousClosure: (0x730fcc), in [package:flutter/src/rendering/box.dart] RenderBox::_computeDryBaseline (0x731008)
    //     0x74289c: ldr             x1, [x1, #0x750]
    // 0x7428a0: stur            x0, [fp, #-0x50]
    // 0x7428a4: r0 = AllocateClosure()
    //     0x7428a4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7428a8: r16 = <(BoxConstraints, TextBaseline), double?>
    //     0x7428a8: add             x16, PP, #0x45, lsl #12  ; [pp+0x456d8] TypeArguments: <(BoxConstraints, TextBaseline), double?>
    //     0x7428ac: ldr             x16, [x16, #0x6d8]
    // 0x7428b0: ldur            lr, [fp, #-0x38]
    // 0x7428b4: stp             lr, x16, [SP, #0x18]
    // 0x7428b8: r16 = Instance__Baseline
    //     0x7428b8: add             x16, PP, #0x45, lsl #12  ; [pp+0x456e0] Obj!_Baseline@e117c1
    //     0x7428bc: ldr             x16, [x16, #0x6e0]
    // 0x7428c0: ldur            lr, [fp, #-0x50]
    // 0x7428c4: stp             lr, x16, [SP, #8]
    // 0x7428c8: str             x0, [SP]
    // 0x7428cc: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x7428cc: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x7428d0: r0 = _computeIntrinsics()
    //     0x7428d0: bl              #0x72d344  ; [package:flutter/src/rendering/box.dart] RenderBox::_computeIntrinsics
    // 0x7428d4: ldur            x3, [fp, #-0x30]
    // 0x7428d8: tbnz            w3, #4, #0x7428f0
    // 0x7428dc: ldur            x1, [fp, #-0x20]
    // 0x7428e0: LoadField: d0 = r1->field_f
    //     0x7428e0: ldur            d0, [x1, #0xf]
    // 0x7428e4: fneg            d1, d0
    // 0x7428e8: mov             v0.16b, v1.16b
    // 0x7428ec: b               #0x7428f8
    // 0x7428f0: ldur            x1, [fp, #-0x20]
    // 0x7428f4: d0 = 0.000000
    //     0x7428f4: eor             v0.16b, v0.16b, v0.16b
    // 0x7428f8: cmp             w0, NULL
    // 0x7428fc: b.ne            #0x74290c
    // 0x742900: ldur            d1, [fp, #-0x60]
    // 0x742904: r0 = Null
    //     0x742904: mov             x0, NULL
    // 0x742908: b               #0x742944
    // 0x74290c: ldur            d1, [fp, #-0x60]
    // 0x742910: LoadField: d2 = r0->field_7
    //     0x742910: ldur            d2, [x0, #7]
    // 0x742914: fadd            d3, d2, d1
    // 0x742918: r2 = inline_Allocate_Double()
    //     0x742918: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x74291c: add             x2, x2, #0x10
    //     0x742920: cmp             x0, x2
    //     0x742924: b.ls            #0x742a70
    //     0x742928: str             x2, [THR, #0x50]  ; THR::top
    //     0x74292c: sub             x2, x2, #0xf
    //     0x742930: movz            x0, #0xe15c
    //     0x742934: movk            x0, #0x3, lsl #16
    //     0x742938: stur            x0, [x2, #-1]
    // 0x74293c: StoreField: r2->field_7 = d3
    //     0x74293c: stur            d3, [x2, #7]
    // 0x742940: mov             x0, x2
    // 0x742944: cmp             w0, NULL
    // 0x742948: b.ne            #0x742954
    // 0x74294c: r5 = Null
    //     0x74294c: mov             x5, NULL
    // 0x742950: b               #0x742988
    // 0x742954: LoadField: d2 = r0->field_7
    //     0x742954: ldur            d2, [x0, #7]
    // 0x742958: fadd            d3, d2, d0
    // 0x74295c: r0 = inline_Allocate_Double()
    //     0x74295c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x742960: add             x0, x0, #0x10
    //     0x742964: cmp             x2, x0
    //     0x742968: b.ls            #0x742a94
    //     0x74296c: str             x0, [THR, #0x50]  ; THR::top
    //     0x742970: sub             x0, x0, #0xf
    //     0x742974: movz            x2, #0xe15c
    //     0x742978: movk            x2, #0x3, lsl #16
    //     0x74297c: stur            x2, [x0, #-1]
    // 0x742980: StoreField: r0->field_7 = d3
    //     0x742980: stur            d3, [x0, #7]
    // 0x742984: mov             x5, x0
    // 0x742988: ldur            d0, [fp, #-0x68]
    // 0x74298c: ldur            x0, [fp, #-0x38]
    // 0x742990: ldur            x4, [fp, #-0x48]
    // 0x742994: stur            x5, [fp, #-0x50]
    // 0x742998: LoadField: d2 = r1->field_f
    //     0x742998: ldur            d2, [x1, #0xf]
    // 0x74299c: LoadField: d3 = r4->field_7
    //     0x74299c: ldur            d3, [x4, #7]
    // 0x7429a0: fadd            d4, d3, d2
    // 0x7429a4: fmul            d2, d0, d4
    // 0x7429a8: fadd            d3, d1, d2
    // 0x7429ac: stur            d3, [fp, #-0x70]
    // 0x7429b0: LoadField: r6 = r0->field_7
    //     0x7429b0: ldur            w6, [x0, #7]
    // 0x7429b4: DecompressPointer r6
    //     0x7429b4: add             x6, x6, HEAP, lsl #32
    // 0x7429b8: stur            x6, [fp, #-0x20]
    // 0x7429bc: cmp             w6, NULL
    // 0x7429c0: b.eq            #0x742aac
    // 0x7429c4: mov             x0, x6
    // 0x7429c8: r2 = Null
    //     0x7429c8: mov             x2, NULL
    // 0x7429cc: r1 = Null
    //     0x7429cc: mov             x1, NULL
    // 0x7429d0: r4 = LoadClassIdInstr(r0)
    //     0x7429d0: ldur            x4, [x0, #-1]
    //     0x7429d4: ubfx            x4, x4, #0xc, #0x14
    // 0x7429d8: cmp             x4, #0xc7e
    // 0x7429dc: b.eq            #0x7429f4
    // 0x7429e0: r8 = FlexParentData
    //     0x7429e0: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x7429e4: ldr             x8, [x8, #0x590]
    // 0x7429e8: r3 = Null
    //     0x7429e8: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4ef40] Null
    //     0x7429ec: ldr             x3, [x3, #0xf40]
    // 0x7429f0: r0 = DefaultTypeTest()
    //     0x7429f0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7429f4: ldur            x1, [fp, #-0x20]
    // 0x7429f8: LoadField: r7 = r1->field_13
    //     0x7429f8: ldur            w7, [x1, #0x13]
    // 0x7429fc: DecompressPointer r7
    //     0x7429fc: add             x7, x7, HEAP, lsl #32
    // 0x742a00: ldur            x0, [fp, #-0x50]
    // 0x742a04: ldur            d0, [fp, #-0x70]
    // 0x742a08: ldur            x5, [fp, #-8]
    // 0x742a0c: ldur            x4, [fp, #-0x30]
    // 0x742a10: ldur            d1, [fp, #-0x68]
    // 0x742a14: ldur            x6, [fp, #-0x40]
    // 0x742a18: ldur            x3, [fp, #-0x48]
    // 0x742a1c: b               #0x742768
    // 0x742a20: LeaveFrame
    //     0x742a20: mov             SP, fp
    //     0x742a24: ldp             fp, lr, [SP], #0x10
    // 0x742a28: ret
    //     0x742a28: ret             
    // 0x742a2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x742a2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x742a30: b               #0x742240
    // 0x742a34: r0 = StackOverflowSharedWithFPURegs()
    //     0x742a34: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x742a38: b               #0x742350
    // 0x742a3c: r0 = NullCastErrorSharedWithFPURegs()
    //     0x742a3c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x742a40: SaveReg d2
    //     0x742a40: str             q2, [SP, #-0x10]!
    // 0x742a44: r0 = AllocateDouble()
    //     0x742a44: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742a48: RestoreReg d2
    //     0x742a48: ldr             q2, [SP], #0x10
    // 0x742a4c: b               #0x7424fc
    // 0x742a50: SaveReg d0
    //     0x742a50: str             q0, [SP, #-0x10]!
    // 0x742a54: r0 = AllocateDouble()
    //     0x742a54: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742a58: RestoreReg d0
    //     0x742a58: ldr             q0, [SP], #0x10
    // 0x742a5c: b               #0x742558
    // 0x742a60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x742a60: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x742a64: r0 = StackOverflowSharedWithFPURegs()
    //     0x742a64: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x742a68: b               #0x74277c
    // 0x742a6c: r0 = NullCastErrorSharedWithFPURegs()
    //     0x742a6c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x742a70: stp             q1, q3, [SP, #-0x20]!
    // 0x742a74: SaveReg d0
    //     0x742a74: str             q0, [SP, #-0x10]!
    // 0x742a78: stp             x1, x3, [SP, #-0x10]!
    // 0x742a7c: r0 = AllocateDouble()
    //     0x742a7c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742a80: mov             x2, x0
    // 0x742a84: ldp             x1, x3, [SP], #0x10
    // 0x742a88: RestoreReg d0
    //     0x742a88: ldr             q0, [SP], #0x10
    // 0x742a8c: ldp             q1, q3, [SP], #0x20
    // 0x742a90: b               #0x74293c
    // 0x742a94: stp             q1, q3, [SP, #-0x20]!
    // 0x742a98: stp             x1, x3, [SP, #-0x10]!
    // 0x742a9c: r0 = AllocateDouble()
    //     0x742a9c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742aa0: ldp             x1, x3, [SP], #0x10
    // 0x742aa4: ldp             q1, q3, [SP], #0x20
    // 0x742aa8: b               #0x742980
    // 0x742aac: r0 = NullCastErrorSharedWithFPURegs()
    //     0x742aac: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  get _ _flipMainAxis(/* No info */) {
    // ** addr: 0x742e34, size: 0x78
    // 0x742e34: LoadField: r2 = r1->field_5f
    //     0x742e34: ldur            w2, [x1, #0x5f]
    // 0x742e38: DecompressPointer r2
    //     0x742e38: add             x2, x2, HEAP, lsl #32
    // 0x742e3c: cmp             w2, NULL
    // 0x742e40: b.eq            #0x742ea4
    // 0x742e44: LoadField: r2 = r1->field_6b
    //     0x742e44: ldur            w2, [x1, #0x6b]
    // 0x742e48: DecompressPointer r2
    //     0x742e48: add             x2, x2, HEAP, lsl #32
    // 0x742e4c: LoadField: r3 = r2->field_7
    //     0x742e4c: ldur            x3, [x2, #7]
    // 0x742e50: cmp             x3, #0
    // 0x742e54: b.gt            #0x742e98
    // 0x742e58: LoadField: r2 = r1->field_7b
    //     0x742e58: ldur            w2, [x1, #0x7b]
    // 0x742e5c: DecompressPointer r2
    //     0x742e5c: add             x2, x2, HEAP, lsl #32
    // 0x742e60: cmp             w2, NULL
    // 0x742e64: b.eq            #0x742e74
    // 0x742e68: r16 = Instance_TextDirection
    //     0x742e68: ldr             x16, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0x742e6c: cmp             w2, w16
    // 0x742e70: b.ne            #0x742e7c
    // 0x742e74: r1 = false
    //     0x742e74: add             x1, NULL, #0x30  ; false
    // 0x742e78: b               #0x742e9c
    // 0x742e7c: r16 = Instance_TextDirection
    //     0x742e7c: ldr             x16, [PP, #0x2898]  ; [pp+0x2898] Obj!TextDirection@e392e1
    // 0x742e80: cmp             w2, w16
    // 0x742e84: b.ne            #0x742e90
    // 0x742e88: r1 = true
    //     0x742e88: add             x1, NULL, #0x20  ; true
    // 0x742e8c: b               #0x742e9c
    // 0x742e90: r1 = Null
    //     0x742e90: mov             x1, NULL
    // 0x742e94: b               #0x742e9c
    // 0x742e98: r1 = false
    //     0x742e98: add             x1, NULL, #0x30  ; false
    // 0x742e9c: mov             x0, x1
    // 0x742ea0: b               #0x742ea8
    // 0x742ea4: r0 = false
    //     0x742ea4: add             x0, NULL, #0x30  ; false
    // 0x742ea8: ret
    //     0x742ea8: ret             
  }
  get _ _flipCrossAxis(/* No info */) {
    // ** addr: 0x74300c, size: 0x78
    // 0x74300c: LoadField: r2 = r1->field_5f
    //     0x74300c: ldur            w2, [x1, #0x5f]
    // 0x743010: DecompressPointer r2
    //     0x743010: add             x2, x2, HEAP, lsl #32
    // 0x743014: cmp             w2, NULL
    // 0x743018: b.eq            #0x74307c
    // 0x74301c: LoadField: r2 = r1->field_6b
    //     0x74301c: ldur            w2, [x1, #0x6b]
    // 0x743020: DecompressPointer r2
    //     0x743020: add             x2, x2, HEAP, lsl #32
    // 0x743024: LoadField: r3 = r2->field_7
    //     0x743024: ldur            x3, [x2, #7]
    // 0x743028: cmp             x3, #0
    // 0x74302c: b.gt            #0x743038
    // 0x743030: r1 = false
    //     0x743030: add             x1, NULL, #0x30  ; false
    // 0x743034: b               #0x743074
    // 0x743038: LoadField: r2 = r1->field_7b
    //     0x743038: ldur            w2, [x1, #0x7b]
    // 0x74303c: DecompressPointer r2
    //     0x74303c: add             x2, x2, HEAP, lsl #32
    // 0x743040: cmp             w2, NULL
    // 0x743044: b.eq            #0x743054
    // 0x743048: r16 = Instance_TextDirection
    //     0x743048: ldr             x16, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0x74304c: cmp             w2, w16
    // 0x743050: b.ne            #0x74305c
    // 0x743054: r1 = false
    //     0x743054: add             x1, NULL, #0x30  ; false
    // 0x743058: b               #0x743074
    // 0x74305c: r16 = Instance_TextDirection
    //     0x74305c: ldr             x16, [PP, #0x2898]  ; [pp+0x2898] Obj!TextDirection@e392e1
    // 0x743060: cmp             w2, w16
    // 0x743064: b.ne            #0x743070
    // 0x743068: r1 = true
    //     0x743068: add             x1, NULL, #0x20  ; true
    // 0x74306c: b               #0x743074
    // 0x743070: r1 = Null
    //     0x743070: mov             x1, NULL
    // 0x743074: mov             x0, x1
    // 0x743078: b               #0x743080
    // 0x74307c: r0 = false
    //     0x74307c: add             x0, NULL, #0x30  ; false
    // 0x743080: ret
    //     0x743080: ret             
  }
  dynamic computeMinIntrinsicHeight(dynamic) {
    // ** addr: 0x749848, size: 0x24
    // 0x749848: EnterFrame
    //     0x749848: stp             fp, lr, [SP, #-0x10]!
    //     0x74984c: mov             fp, SP
    // 0x749850: ldr             x2, [fp, #0x10]
    // 0x749854: r1 = Function 'computeMinIntrinsicHeight':.
    //     0x749854: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f98] AnonymousClosure: (0x74986c), in [package:flutter/src/rendering/flex.dart] RenderFlex::computeMinIntrinsicHeight (0x7498e0)
    //     0x749858: ldr             x1, [x1, #0xf98]
    // 0x74985c: r0 = AllocateClosure()
    //     0x74985c: bl              #0xec1630  ; AllocateClosureStub
    // 0x749860: LeaveFrame
    //     0x749860: mov             SP, fp
    //     0x749864: ldp             fp, lr, [SP], #0x10
    // 0x749868: ret
    //     0x749868: ret             
  }
  [closure] double computeMinIntrinsicHeight(dynamic, double) {
    // ** addr: 0x74986c, size: 0x74
    // 0x74986c: EnterFrame
    //     0x74986c: stp             fp, lr, [SP, #-0x10]!
    //     0x749870: mov             fp, SP
    // 0x749874: ldr             x0, [fp, #0x18]
    // 0x749878: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x749878: ldur            w1, [x0, #0x17]
    // 0x74987c: DecompressPointer r1
    //     0x74987c: add             x1, x1, HEAP, lsl #32
    // 0x749880: CheckStackOverflow
    //     0x749880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x749884: cmp             SP, x16
    //     0x749888: b.ls            #0x7498c8
    // 0x74988c: ldr             x2, [fp, #0x10]
    // 0x749890: r0 = computeMinIntrinsicHeight()
    //     0x749890: bl              #0x7498e0  ; [package:flutter/src/rendering/flex.dart] RenderFlex::computeMinIntrinsicHeight
    // 0x749894: r0 = inline_Allocate_Double()
    //     0x749894: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x749898: add             x0, x0, #0x10
    //     0x74989c: cmp             x1, x0
    //     0x7498a0: b.ls            #0x7498d0
    //     0x7498a4: str             x0, [THR, #0x50]  ; THR::top
    //     0x7498a8: sub             x0, x0, #0xf
    //     0x7498ac: movz            x1, #0xe15c
    //     0x7498b0: movk            x1, #0x3, lsl #16
    //     0x7498b4: stur            x1, [x0, #-1]
    // 0x7498b8: StoreField: r0->field_7 = d0
    //     0x7498b8: stur            d0, [x0, #7]
    // 0x7498bc: LeaveFrame
    //     0x7498bc: mov             SP, fp
    //     0x7498c0: ldp             fp, lr, [SP], #0x10
    // 0x7498c4: ret
    //     0x7498c4: ret             
    // 0x7498c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7498c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7498cc: b               #0x74988c
    // 0x7498d0: SaveReg d0
    //     0x7498d0: str             q0, [SP, #-0x10]!
    // 0x7498d4: r0 = AllocateDouble()
    //     0x7498d4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7498d8: RestoreReg d0
    //     0x7498d8: ldr             q0, [SP], #0x10
    // 0x7498dc: b               #0x7498b8
  }
  _ computeMinIntrinsicHeight(/* No info */) {
    // ** addr: 0x7498e0, size: 0x60
    // 0x7498e0: EnterFrame
    //     0x7498e0: stp             fp, lr, [SP, #-0x10]!
    //     0x7498e4: mov             fp, SP
    // 0x7498e8: AllocStack(0x10)
    //     0x7498e8: sub             SP, SP, #0x10
    // 0x7498ec: SetupParameters(RenderFlex this /* r1 => r0, fp-0x8 */)
    //     0x7498ec: mov             x0, x1
    //     0x7498f0: stur            x1, [fp, #-8]
    // 0x7498f4: CheckStackOverflow
    //     0x7498f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7498f8: cmp             SP, x16
    //     0x7498fc: b.ls            #0x749938
    // 0x749900: LoadField: d0 = r2->field_7
    //     0x749900: ldur            d0, [x2, #7]
    // 0x749904: stur            d0, [fp, #-0x10]
    // 0x749908: r1 = Function '<anonymous closure>':.
    //     0x749908: add             x1, PP, #0x55, lsl #12  ; [pp+0x55fa0] AnonymousClosure: (0x749940), in [package:flutter/src/rendering/flex.dart] RenderFlex::computeMinIntrinsicHeight (0x7498e0)
    //     0x74990c: ldr             x1, [x1, #0xfa0]
    // 0x749910: r2 = Null
    //     0x749910: mov             x2, NULL
    // 0x749914: r0 = AllocateClosure()
    //     0x749914: bl              #0xec1630  ; AllocateClosureStub
    // 0x749918: ldur            x1, [fp, #-8]
    // 0x74991c: mov             x2, x0
    // 0x749920: ldur            d0, [fp, #-0x10]
    // 0x749924: r3 = Instance_Axis
    //     0x749924: ldr             x3, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x749928: r0 = _getIntrinsicSize()
    //     0x749928: bl              #0x7311cc  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_getIntrinsicSize
    // 0x74992c: LeaveFrame
    //     0x74992c: mov             SP, fp
    //     0x749930: ldp             fp, lr, [SP], #0x10
    // 0x749934: ret
    //     0x749934: ret             
    // 0x749938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x749938: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74993c: b               #0x749900
  }
  [closure] double <anonymous closure>(dynamic, RenderBox, double) {
    // ** addr: 0x749940, size: 0x70
    // 0x749940: EnterFrame
    //     0x749940: stp             fp, lr, [SP, #-0x10]!
    //     0x749944: mov             fp, SP
    // 0x749948: CheckStackOverflow
    //     0x749948: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74994c: cmp             SP, x16
    //     0x749950: b.ls            #0x749998
    // 0x749954: ldr             x0, [fp, #0x10]
    // 0x749958: LoadField: d0 = r0->field_7
    //     0x749958: ldur            d0, [x0, #7]
    // 0x74995c: ldr             x1, [fp, #0x18]
    // 0x749960: r0 = getMinIntrinsicHeight()
    //     0x749960: bl              #0x73933c  ; [package:flutter/src/rendering/box.dart] RenderBox::getMinIntrinsicHeight
    // 0x749964: r0 = inline_Allocate_Double()
    //     0x749964: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x749968: add             x0, x0, #0x10
    //     0x74996c: cmp             x1, x0
    //     0x749970: b.ls            #0x7499a0
    //     0x749974: str             x0, [THR, #0x50]  ; THR::top
    //     0x749978: sub             x0, x0, #0xf
    //     0x74997c: movz            x1, #0xe15c
    //     0x749980: movk            x1, #0x3, lsl #16
    //     0x749984: stur            x1, [x0, #-1]
    // 0x749988: StoreField: r0->field_7 = d0
    //     0x749988: stur            d0, [x0, #7]
    // 0x74998c: LeaveFrame
    //     0x74998c: mov             SP, fp
    //     0x749990: ldp             fp, lr, [SP], #0x10
    // 0x749994: ret
    //     0x749994: ret             
    // 0x749998: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x749998: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74999c: b               #0x749954
    // 0x7499a0: SaveReg d0
    //     0x7499a0: str             q0, [SP, #-0x10]!
    // 0x7499a4: r0 = AllocateDouble()
    //     0x7499a4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7499a8: RestoreReg d0
    //     0x7499a8: ldr             q0, [SP], #0x10
    // 0x7499ac: b               #0x749988
  }
  _ computeDistanceToActualBaseline(/* No info */) {
    // ** addr: 0x74c00c, size: 0x48
    // 0x74c00c: EnterFrame
    //     0x74c00c: stp             fp, lr, [SP, #-0x10]!
    //     0x74c010: mov             fp, SP
    // 0x74c014: CheckStackOverflow
    //     0x74c014: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74c018: cmp             SP, x16
    //     0x74c01c: b.ls            #0x74c04c
    // 0x74c020: LoadField: r0 = r1->field_6b
    //     0x74c020: ldur            w0, [x1, #0x6b]
    // 0x74c024: DecompressPointer r0
    //     0x74c024: add             x0, x0, HEAP, lsl #32
    // 0x74c028: LoadField: r3 = r0->field_7
    //     0x74c028: ldur            x3, [x0, #7]
    // 0x74c02c: cmp             x3, #0
    // 0x74c030: b.gt            #0x74c03c
    // 0x74c034: r0 = defaultComputeDistanceToHighestActualBaseline()
    //     0x74c034: bl              #0x74c23c  ; [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultComputeDistanceToHighestActualBaseline
    // 0x74c038: b               #0x74c040
    // 0x74c03c: r0 = defaultComputeDistanceToFirstActualBaseline()
    //     0x74c03c: bl              #0x74c054  ; [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultComputeDistanceToFirstActualBaseline
    // 0x74c040: LeaveFrame
    //     0x74c040: mov             SP, fp
    //     0x74c044: ldp             fp, lr, [SP], #0x10
    // 0x74c048: ret
    //     0x74c048: ret             
    // 0x74c04c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74c04c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74c050: b               #0x74c020
  }
  dynamic computeMaxIntrinsicWidth(dynamic) {
    // ** addr: 0x74f964, size: 0x24
    // 0x74f964: EnterFrame
    //     0x74f964: stp             fp, lr, [SP, #-0x10]!
    //     0x74f968: mov             fp, SP
    // 0x74f96c: ldr             x2, [fp, #0x10]
    // 0x74f970: r1 = Function 'computeMaxIntrinsicWidth':.
    //     0x74f970: add             x1, PP, #0x55, lsl #12  ; [pp+0x55fa8] AnonymousClosure: (0x74f988), in [package:flutter/src/rendering/flex.dart] RenderFlex::computeMaxIntrinsicWidth (0x74f9fc)
    //     0x74f974: ldr             x1, [x1, #0xfa8]
    // 0x74f978: r0 = AllocateClosure()
    //     0x74f978: bl              #0xec1630  ; AllocateClosureStub
    // 0x74f97c: LeaveFrame
    //     0x74f97c: mov             SP, fp
    //     0x74f980: ldp             fp, lr, [SP], #0x10
    // 0x74f984: ret
    //     0x74f984: ret             
  }
  [closure] double computeMaxIntrinsicWidth(dynamic, double) {
    // ** addr: 0x74f988, size: 0x74
    // 0x74f988: EnterFrame
    //     0x74f988: stp             fp, lr, [SP, #-0x10]!
    //     0x74f98c: mov             fp, SP
    // 0x74f990: ldr             x0, [fp, #0x18]
    // 0x74f994: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x74f994: ldur            w1, [x0, #0x17]
    // 0x74f998: DecompressPointer r1
    //     0x74f998: add             x1, x1, HEAP, lsl #32
    // 0x74f99c: CheckStackOverflow
    //     0x74f99c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74f9a0: cmp             SP, x16
    //     0x74f9a4: b.ls            #0x74f9e4
    // 0x74f9a8: ldr             x2, [fp, #0x10]
    // 0x74f9ac: r0 = computeMaxIntrinsicWidth()
    //     0x74f9ac: bl              #0x74f9fc  ; [package:flutter/src/rendering/flex.dart] RenderFlex::computeMaxIntrinsicWidth
    // 0x74f9b0: r0 = inline_Allocate_Double()
    //     0x74f9b0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74f9b4: add             x0, x0, #0x10
    //     0x74f9b8: cmp             x1, x0
    //     0x74f9bc: b.ls            #0x74f9ec
    //     0x74f9c0: str             x0, [THR, #0x50]  ; THR::top
    //     0x74f9c4: sub             x0, x0, #0xf
    //     0x74f9c8: movz            x1, #0xe15c
    //     0x74f9cc: movk            x1, #0x3, lsl #16
    //     0x74f9d0: stur            x1, [x0, #-1]
    // 0x74f9d4: StoreField: r0->field_7 = d0
    //     0x74f9d4: stur            d0, [x0, #7]
    // 0x74f9d8: LeaveFrame
    //     0x74f9d8: mov             SP, fp
    //     0x74f9dc: ldp             fp, lr, [SP], #0x10
    // 0x74f9e0: ret
    //     0x74f9e0: ret             
    // 0x74f9e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74f9e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74f9e8: b               #0x74f9a8
    // 0x74f9ec: SaveReg d0
    //     0x74f9ec: str             q0, [SP, #-0x10]!
    // 0x74f9f0: r0 = AllocateDouble()
    //     0x74f9f0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74f9f4: RestoreReg d0
    //     0x74f9f4: ldr             q0, [SP], #0x10
    // 0x74f9f8: b               #0x74f9d4
  }
  _ computeMaxIntrinsicWidth(/* No info */) {
    // ** addr: 0x74f9fc, size: 0x60
    // 0x74f9fc: EnterFrame
    //     0x74f9fc: stp             fp, lr, [SP, #-0x10]!
    //     0x74fa00: mov             fp, SP
    // 0x74fa04: AllocStack(0x10)
    //     0x74fa04: sub             SP, SP, #0x10
    // 0x74fa08: SetupParameters(RenderFlex this /* r1 => r0, fp-0x8 */)
    //     0x74fa08: mov             x0, x1
    //     0x74fa0c: stur            x1, [fp, #-8]
    // 0x74fa10: CheckStackOverflow
    //     0x74fa10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74fa14: cmp             SP, x16
    //     0x74fa18: b.ls            #0x74fa54
    // 0x74fa1c: LoadField: d0 = r2->field_7
    //     0x74fa1c: ldur            d0, [x2, #7]
    // 0x74fa20: stur            d0, [fp, #-0x10]
    // 0x74fa24: r1 = Function '<anonymous closure>':.
    //     0x74fa24: add             x1, PP, #0x55, lsl #12  ; [pp+0x55fb0] AnonymousClosure: (0x74fa5c), in [package:flutter/src/rendering/flex.dart] RenderFlex::computeMaxIntrinsicWidth (0x74f9fc)
    //     0x74fa28: ldr             x1, [x1, #0xfb0]
    // 0x74fa2c: r2 = Null
    //     0x74fa2c: mov             x2, NULL
    // 0x74fa30: r0 = AllocateClosure()
    //     0x74fa30: bl              #0xec1630  ; AllocateClosureStub
    // 0x74fa34: ldur            x1, [fp, #-8]
    // 0x74fa38: mov             x2, x0
    // 0x74fa3c: ldur            d0, [fp, #-0x10]
    // 0x74fa40: r3 = Instance_Axis
    //     0x74fa40: ldr             x3, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x74fa44: r0 = _getIntrinsicSize()
    //     0x74fa44: bl              #0x7311cc  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_getIntrinsicSize
    // 0x74fa48: LeaveFrame
    //     0x74fa48: mov             SP, fp
    //     0x74fa4c: ldp             fp, lr, [SP], #0x10
    // 0x74fa50: ret
    //     0x74fa50: ret             
    // 0x74fa54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74fa54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74fa58: b               #0x74fa1c
  }
  [closure] double <anonymous closure>(dynamic, RenderBox, double) {
    // ** addr: 0x74fa5c, size: 0x70
    // 0x74fa5c: EnterFrame
    //     0x74fa5c: stp             fp, lr, [SP, #-0x10]!
    //     0x74fa60: mov             fp, SP
    // 0x74fa64: CheckStackOverflow
    //     0x74fa64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74fa68: cmp             SP, x16
    //     0x74fa6c: b.ls            #0x74fab4
    // 0x74fa70: ldr             x0, [fp, #0x10]
    // 0x74fa74: LoadField: d0 = r0->field_7
    //     0x74fa74: ldur            d0, [x0, #7]
    // 0x74fa78: ldr             x1, [fp, #0x18]
    // 0x74fa7c: r0 = getMaxIntrinsicWidth()
    //     0x74fa7c: bl              #0x72d4b4  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicWidth
    // 0x74fa80: r0 = inline_Allocate_Double()
    //     0x74fa80: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74fa84: add             x0, x0, #0x10
    //     0x74fa88: cmp             x1, x0
    //     0x74fa8c: b.ls            #0x74fabc
    //     0x74fa90: str             x0, [THR, #0x50]  ; THR::top
    //     0x74fa94: sub             x0, x0, #0xf
    //     0x74fa98: movz            x1, #0xe15c
    //     0x74fa9c: movk            x1, #0x3, lsl #16
    //     0x74faa0: stur            x1, [x0, #-1]
    // 0x74faa4: StoreField: r0->field_7 = d0
    //     0x74faa4: stur            d0, [x0, #7]
    // 0x74faa8: LeaveFrame
    //     0x74faa8: mov             SP, fp
    //     0x74faac: ldp             fp, lr, [SP], #0x10
    // 0x74fab0: ret
    //     0x74fab0: ret             
    // 0x74fab4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74fab4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74fab8: b               #0x74fa70
    // 0x74fabc: SaveReg d0
    //     0x74fabc: str             q0, [SP, #-0x10]!
    // 0x74fac0: r0 = AllocateDouble()
    //     0x74fac0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74fac4: RestoreReg d0
    //     0x74fac4: ldr             q0, [SP], #0x10
    // 0x74fac8: b               #0x74faa4
  }
  dynamic computeMaxIntrinsicHeight(dynamic) {
    // ** addr: 0x752e28, size: 0x24
    // 0x752e28: EnterFrame
    //     0x752e28: stp             fp, lr, [SP, #-0x10]!
    //     0x752e2c: mov             fp, SP
    // 0x752e30: ldr             x2, [fp, #0x10]
    // 0x752e34: r1 = Function 'computeMaxIntrinsicHeight':.
    //     0x752e34: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f60] AnonymousClosure: (0x752e4c), in [package:flutter/src/rendering/flex.dart] RenderFlex::computeMaxIntrinsicHeight (0x752ec0)
    //     0x752e38: ldr             x1, [x1, #0xf60]
    // 0x752e3c: r0 = AllocateClosure()
    //     0x752e3c: bl              #0xec1630  ; AllocateClosureStub
    // 0x752e40: LeaveFrame
    //     0x752e40: mov             SP, fp
    //     0x752e44: ldp             fp, lr, [SP], #0x10
    // 0x752e48: ret
    //     0x752e48: ret             
  }
  [closure] double computeMaxIntrinsicHeight(dynamic, double) {
    // ** addr: 0x752e4c, size: 0x74
    // 0x752e4c: EnterFrame
    //     0x752e4c: stp             fp, lr, [SP, #-0x10]!
    //     0x752e50: mov             fp, SP
    // 0x752e54: ldr             x0, [fp, #0x18]
    // 0x752e58: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x752e58: ldur            w1, [x0, #0x17]
    // 0x752e5c: DecompressPointer r1
    //     0x752e5c: add             x1, x1, HEAP, lsl #32
    // 0x752e60: CheckStackOverflow
    //     0x752e60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x752e64: cmp             SP, x16
    //     0x752e68: b.ls            #0x752ea8
    // 0x752e6c: ldr             x2, [fp, #0x10]
    // 0x752e70: r0 = computeMaxIntrinsicHeight()
    //     0x752e70: bl              #0x752ec0  ; [package:flutter/src/rendering/flex.dart] RenderFlex::computeMaxIntrinsicHeight
    // 0x752e74: r0 = inline_Allocate_Double()
    //     0x752e74: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x752e78: add             x0, x0, #0x10
    //     0x752e7c: cmp             x1, x0
    //     0x752e80: b.ls            #0x752eb0
    //     0x752e84: str             x0, [THR, #0x50]  ; THR::top
    //     0x752e88: sub             x0, x0, #0xf
    //     0x752e8c: movz            x1, #0xe15c
    //     0x752e90: movk            x1, #0x3, lsl #16
    //     0x752e94: stur            x1, [x0, #-1]
    // 0x752e98: StoreField: r0->field_7 = d0
    //     0x752e98: stur            d0, [x0, #7]
    // 0x752e9c: LeaveFrame
    //     0x752e9c: mov             SP, fp
    //     0x752ea0: ldp             fp, lr, [SP], #0x10
    // 0x752ea4: ret
    //     0x752ea4: ret             
    // 0x752ea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x752ea8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x752eac: b               #0x752e6c
    // 0x752eb0: SaveReg d0
    //     0x752eb0: str             q0, [SP, #-0x10]!
    // 0x752eb4: r0 = AllocateDouble()
    //     0x752eb4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x752eb8: RestoreReg d0
    //     0x752eb8: ldr             q0, [SP], #0x10
    // 0x752ebc: b               #0x752e98
  }
  _ computeMaxIntrinsicHeight(/* No info */) {
    // ** addr: 0x752ec0, size: 0x60
    // 0x752ec0: EnterFrame
    //     0x752ec0: stp             fp, lr, [SP, #-0x10]!
    //     0x752ec4: mov             fp, SP
    // 0x752ec8: AllocStack(0x10)
    //     0x752ec8: sub             SP, SP, #0x10
    // 0x752ecc: SetupParameters(RenderFlex this /* r1 => r0, fp-0x8 */)
    //     0x752ecc: mov             x0, x1
    //     0x752ed0: stur            x1, [fp, #-8]
    // 0x752ed4: CheckStackOverflow
    //     0x752ed4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x752ed8: cmp             SP, x16
    //     0x752edc: b.ls            #0x752f18
    // 0x752ee0: LoadField: d0 = r2->field_7
    //     0x752ee0: ldur            d0, [x2, #7]
    // 0x752ee4: stur            d0, [fp, #-0x10]
    // 0x752ee8: r1 = Function '<anonymous closure>':.
    //     0x752ee8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f68] AnonymousClosure: (0x752f20), in [package:flutter/src/rendering/flex.dart] RenderFlex::computeMaxIntrinsicHeight (0x752ec0)
    //     0x752eec: ldr             x1, [x1, #0xf68]
    // 0x752ef0: r2 = Null
    //     0x752ef0: mov             x2, NULL
    // 0x752ef4: r0 = AllocateClosure()
    //     0x752ef4: bl              #0xec1630  ; AllocateClosureStub
    // 0x752ef8: ldur            x1, [fp, #-8]
    // 0x752efc: mov             x2, x0
    // 0x752f00: ldur            d0, [fp, #-0x10]
    // 0x752f04: r3 = Instance_Axis
    //     0x752f04: ldr             x3, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x752f08: r0 = _getIntrinsicSize()
    //     0x752f08: bl              #0x7311cc  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_getIntrinsicSize
    // 0x752f0c: LeaveFrame
    //     0x752f0c: mov             SP, fp
    //     0x752f10: ldp             fp, lr, [SP], #0x10
    // 0x752f14: ret
    //     0x752f14: ret             
    // 0x752f18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x752f18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x752f1c: b               #0x752ee0
  }
  [closure] double <anonymous closure>(dynamic, RenderBox, double) {
    // ** addr: 0x752f20, size: 0x70
    // 0x752f20: EnterFrame
    //     0x752f20: stp             fp, lr, [SP, #-0x10]!
    //     0x752f24: mov             fp, SP
    // 0x752f28: CheckStackOverflow
    //     0x752f28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x752f2c: cmp             SP, x16
    //     0x752f30: b.ls            #0x752f78
    // 0x752f34: ldr             x0, [fp, #0x10]
    // 0x752f38: LoadField: d0 = r0->field_7
    //     0x752f38: ldur            d0, [x0, #7]
    // 0x752f3c: ldr             x1, [fp, #0x18]
    // 0x752f40: r0 = getMaxIntrinsicHeight()
    //     0x752f40: bl              #0x72d954  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicHeight
    // 0x752f44: r0 = inline_Allocate_Double()
    //     0x752f44: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x752f48: add             x0, x0, #0x10
    //     0x752f4c: cmp             x1, x0
    //     0x752f50: b.ls            #0x752f80
    //     0x752f54: str             x0, [THR, #0x50]  ; THR::top
    //     0x752f58: sub             x0, x0, #0xf
    //     0x752f5c: movz            x1, #0xe15c
    //     0x752f60: movk            x1, #0x3, lsl #16
    //     0x752f64: stur            x1, [x0, #-1]
    // 0x752f68: StoreField: r0->field_7 = d0
    //     0x752f68: stur            d0, [x0, #7]
    // 0x752f6c: LeaveFrame
    //     0x752f6c: mov             SP, fp
    //     0x752f70: ldp             fp, lr, [SP], #0x10
    // 0x752f74: ret
    //     0x752f74: ret             
    // 0x752f78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x752f78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x752f7c: b               #0x752f34
    // 0x752f80: SaveReg d0
    //     0x752f80: str             q0, [SP, #-0x10]!
    // 0x752f84: r0 = AllocateDouble()
    //     0x752f84: bl              #0xec2254  ; AllocateDoubleStub
    // 0x752f88: RestoreReg d0
    //     0x752f88: ldr             q0, [SP], #0x10
    // 0x752f8c: b               #0x752f68
  }
  _ computeDryLayout(/* No info */) {
    // ** addr: 0x7557c4, size: 0x64
    // 0x7557c4: EnterFrame
    //     0x7557c4: stp             fp, lr, [SP, #-0x10]!
    //     0x7557c8: mov             fp, SP
    // 0x7557cc: AllocStack(0x8)
    //     0x7557cc: sub             SP, SP, #8
    // 0x7557d0: SetupParameters(RenderFlex this /* r1 => r0, fp-0x8 */)
    //     0x7557d0: mov             x0, x1
    //     0x7557d4: stur            x1, [fp, #-8]
    // 0x7557d8: CheckStackOverflow
    //     0x7557d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7557dc: cmp             SP, x16
    //     0x7557e0: b.ls            #0x755820
    // 0x7557e4: mov             x1, x0
    // 0x7557e8: r3 = Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getDryBaseline': static.
    //     0x7557e8: add             x3, PP, #0x45, lsl #12  ; [pp+0x45748] Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getDryBaseline': static. (0x7e54fb130f1c)
    //     0x7557ec: ldr             x3, [x3, #0x748]
    // 0x7557f0: r5 = Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static.
    //     0x7557f0: add             x5, PP, #0x44, lsl #12  ; [pp+0x44d20] Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static. (0x7e54fb140c14)
    //     0x7557f4: ldr             x5, [x5, #0xd20]
    // 0x7557f8: r0 = _computeSizes()
    //     0x7557f8: bl              #0x7317d4  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_computeSizes
    // 0x7557fc: LoadField: r1 = r0->field_7
    //     0x7557fc: ldur            w1, [x0, #7]
    // 0x755800: DecompressPointer r1
    //     0x755800: add             x1, x1, HEAP, lsl #32
    // 0x755804: ldur            x0, [fp, #-8]
    // 0x755808: LoadField: r2 = r0->field_6b
    //     0x755808: ldur            w2, [x0, #0x6b]
    // 0x75580c: DecompressPointer r2
    //     0x75580c: add             x2, x2, HEAP, lsl #32
    // 0x755810: r0 = _AxisSize._convert()
    //     0x755810: bl              #0x732a28  ; [package:flutter/src/rendering/flex.dart] ::_AxisSize._convert
    // 0x755814: LeaveFrame
    //     0x755814: mov             SP, fp
    //     0x755818: ldp             fp, lr, [SP], #0x10
    // 0x75581c: ret
    //     0x75581c: ret             
    // 0x755820: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x755820: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x755824: b               #0x7557e4
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x770f68, size: 0x6a0
    // 0x770f68: EnterFrame
    //     0x770f68: stp             fp, lr, [SP, #-0x10]!
    //     0x770f6c: mov             fp, SP
    // 0x770f70: AllocStack(0x70)
    //     0x770f70: sub             SP, SP, #0x70
    // 0x770f74: SetupParameters(RenderFlex this /* r1 => r3, fp-0x10 */)
    //     0x770f74: mov             x3, x1
    //     0x770f78: stur            x1, [fp, #-0x10]
    // 0x770f7c: CheckStackOverflow
    //     0x770f7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x770f80: cmp             SP, x16
    //     0x770f84: b.ls            #0x7715e8
    // 0x770f88: LoadField: r4 = r3->field_27
    //     0x770f88: ldur            w4, [x3, #0x27]
    // 0x770f8c: DecompressPointer r4
    //     0x770f8c: add             x4, x4, HEAP, lsl #32
    // 0x770f90: stur            x4, [fp, #-8]
    // 0x770f94: cmp             w4, NULL
    // 0x770f98: b.eq            #0x771444
    // 0x770f9c: mov             x0, x4
    // 0x770fa0: r2 = Null
    //     0x770fa0: mov             x2, NULL
    // 0x770fa4: r1 = Null
    //     0x770fa4: mov             x1, NULL
    // 0x770fa8: r4 = LoadClassIdInstr(r0)
    //     0x770fa8: ldur            x4, [x0, #-1]
    //     0x770fac: ubfx            x4, x4, #0xc, #0x14
    // 0x770fb0: sub             x4, x4, #0xc83
    // 0x770fb4: cmp             x4, #1
    // 0x770fb8: b.ls            #0x770fcc
    // 0x770fbc: r8 = BoxConstraints
    //     0x770fbc: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x770fc0: r3 = Null
    //     0x770fc0: add             x3, PP, #0x45, lsl #12  ; [pp+0x45610] Null
    //     0x770fc4: ldr             x3, [x3, #0x610]
    // 0x770fc8: r0 = BoxConstraints()
    //     0x770fc8: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x770fcc: ldur            x1, [fp, #-0x10]
    // 0x770fd0: ldur            x2, [fp, #-8]
    // 0x770fd4: r3 = Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getBaseline': static.
    //     0x770fd4: add             x3, PP, #0x45, lsl #12  ; [pp+0x45620] Closure: (RenderBox, BoxConstraints, TextBaseline) => double? from Function 'getBaseline': static. (0x7e54fb16fb2c)
    //     0x770fd8: ldr             x3, [x3, #0x620]
    // 0x770fdc: r5 = Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static.
    //     0x770fdc: add             x5, PP, #0x44, lsl #12  ; [pp+0x44b28] Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static. (0x7e54fb1673f8)
    //     0x770fe0: ldr             x5, [x5, #0xb28]
    // 0x770fe4: r0 = _computeSizes()
    //     0x770fe4: bl              #0x7317d4  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_computeSizes
    // 0x770fe8: stur            x0, [fp, #-8]
    // 0x770fec: LoadField: r1 = r0->field_7
    //     0x770fec: ldur            w1, [x0, #7]
    // 0x770ff0: DecompressPointer r1
    //     0x770ff0: add             x1, x1, HEAP, lsl #32
    // 0x770ff4: LoadField: d0 = r1->field_f
    //     0x770ff4: ldur            d0, [x1, #0xf]
    // 0x770ff8: ldur            x3, [fp, #-0x10]
    // 0x770ffc: stur            d0, [fp, #-0x48]
    // 0x771000: LoadField: r2 = r3->field_6b
    //     0x771000: ldur            w2, [x3, #0x6b]
    // 0x771004: DecompressPointer r2
    //     0x771004: add             x2, x2, HEAP, lsl #32
    // 0x771008: r0 = _AxisSize._convert()
    //     0x771008: bl              #0x732a28  ; [package:flutter/src/rendering/flex.dart] ::_AxisSize._convert
    // 0x77100c: ldur            x2, [fp, #-0x10]
    // 0x771010: StoreField: r2->field_53 = r0
    //     0x771010: stur            w0, [x2, #0x53]
    //     0x771014: ldurb           w16, [x2, #-1]
    //     0x771018: ldurb           w17, [x0, #-1]
    //     0x77101c: and             x16, x17, x16, lsr #2
    //     0x771020: tst             x16, HEAP, lsr #32
    //     0x771024: b.eq            #0x77102c
    //     0x771028: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x77102c: ldur            x0, [fp, #-8]
    // 0x771030: LoadField: d0 = r0->field_b
    //     0x771030: ldur            d0, [x0, #0xb]
    // 0x771034: fneg            d1, d0
    // 0x771038: d2 = 0.000000
    //     0x771038: eor             v2.16b, v2.16b, v2.16b
    // 0x77103c: fcmp            d2, d1
    // 0x771040: b.le            #0x77104c
    // 0x771044: d1 = 0.000000
    //     0x771044: eor             v1.16b, v1.16b, v1.16b
    // 0x771048: b               #0x771074
    // 0x77104c: fcmp            d1, d2
    // 0x771050: b.gt            #0x771074
    // 0x771054: fcmp            d2, d2
    // 0x771058: b.ne            #0x771068
    // 0x77105c: fadd            d3, d1, d2
    // 0x771060: mov             v1.16b, v3.16b
    // 0x771064: b               #0x771074
    // 0x771068: fcmp            d1, d1
    // 0x77106c: b.vs            #0x771074
    // 0x771070: d1 = 0.000000
    //     0x771070: eor             v1.16b, v1.16b, v1.16b
    // 0x771074: StoreField: r2->field_87 = d1
    //     0x771074: stur            d1, [x2, #0x87]
    // 0x771078: fcmp            d2, d0
    // 0x77107c: b.le            #0x771088
    // 0x771080: d0 = 0.000000
    //     0x771080: eor             v0.16b, v0.16b, v0.16b
    // 0x771084: b               #0x7710b0
    // 0x771088: fcmp            d0, d2
    // 0x77108c: b.gt            #0x7710b0
    // 0x771090: fcmp            d2, d2
    // 0x771094: b.ne            #0x7710a4
    // 0x771098: fadd            d1, d0, d2
    // 0x77109c: mov             v0.16b, v1.16b
    // 0x7710a0: b               #0x7710b0
    // 0x7710a4: fcmp            d0, d0
    // 0x7710a8: b.vs            #0x7710b0
    // 0x7710ac: d0 = 0.000000
    //     0x7710ac: eor             v0.16b, v0.16b, v0.16b
    // 0x7710b0: mov             x1, x2
    // 0x7710b4: stur            d0, [fp, #-0x50]
    // 0x7710b8: r0 = _flipMainAxis()
    //     0x7710b8: bl              #0x742e34  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_flipMainAxis
    // 0x7710bc: ldur            x1, [fp, #-0x10]
    // 0x7710c0: stur            x0, [fp, #-0x18]
    // 0x7710c4: r0 = _flipCrossAxis()
    //     0x7710c4: bl              #0x74300c  ; [package:flutter/src/rendering/flex.dart] RenderFlex::_flipCrossAxis
    // 0x7710c8: mov             x4, x0
    // 0x7710cc: ldur            x0, [fp, #-0x10]
    // 0x7710d0: stur            x4, [fp, #-0x20]
    // 0x7710d4: LoadField: r1 = r0->field_6f
    //     0x7710d4: ldur            w1, [x0, #0x6f]
    // 0x7710d8: DecompressPointer r1
    //     0x7710d8: add             x1, x1, HEAP, lsl #32
    // 0x7710dc: LoadField: r2 = r0->field_57
    //     0x7710dc: ldur            x2, [x0, #0x57]
    // 0x7710e0: ldur            d0, [fp, #-0x50]
    // 0x7710e4: ldur            x3, [fp, #-0x18]
    // 0x7710e8: r0 = _distributeSpace()
    //     0x7710e8: bl              #0x742ab0  ; [package:flutter/src/rendering/flex.dart] MainAxisAlignment::_distributeSpace
    // 0x7710ec: mov             x3, x0
    // 0x7710f0: stur            x3, [fp, #-0x30]
    // 0x7710f4: mov             x4, x1
    // 0x7710f8: ldur            x0, [fp, #-0x18]
    // 0x7710fc: stur            x4, [fp, #-0x28]
    // 0x771100: tbnz            w0, #4, #0x771138
    // 0x771104: ldur            x0, [fp, #-0x10]
    // 0x771108: LoadField: r5 = r0->field_63
    //     0x771108: ldur            w5, [x0, #0x63]
    // 0x77110c: DecompressPointer r5
    //     0x77110c: add             x5, x5, HEAP, lsl #32
    // 0x771110: mov             x2, x0
    // 0x771114: stur            x5, [fp, #-0x18]
    // 0x771118: r1 = Function 'childBefore':.
    //     0x771118: add             x1, PP, #0x45, lsl #12  ; [pp+0x45628] AnonymousClosure: (0x771608), of [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin
    //     0x77111c: ldr             x1, [x1, #0x628]
    // 0x771120: r0 = AllocateClosure()
    //     0x771120: bl              #0xec1630  ; AllocateClosureStub
    // 0x771124: mov             x2, x0
    // 0x771128: ldur            x3, [fp, #-0x18]
    // 0x77112c: r0 = AllocateRecord2()
    //     0x77112c: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x771130: mov             x3, x0
    // 0x771134: b               #0x771168
    // 0x771138: ldur            x0, [fp, #-0x10]
    // 0x77113c: LoadField: r3 = r0->field_5f
    //     0x77113c: ldur            w3, [x0, #0x5f]
    // 0x771140: DecompressPointer r3
    //     0x771140: add             x3, x3, HEAP, lsl #32
    // 0x771144: mov             x2, x0
    // 0x771148: stur            x3, [fp, #-0x18]
    // 0x77114c: r1 = Function 'childAfter':.
    //     0x77114c: add             x1, PP, #0x45, lsl #12  ; [pp+0x45630] AnonymousClosure: (0x73172c), of [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin
    //     0x771150: ldr             x1, [x1, #0x630]
    // 0x771154: r0 = AllocateClosure()
    //     0x771154: bl              #0xec1630  ; AllocateClosureStub
    // 0x771158: mov             x2, x0
    // 0x77115c: ldur            x3, [fp, #-0x18]
    // 0x771160: r0 = AllocateRecord2()
    //     0x771160: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x771164: mov             x3, x0
    // 0x771168: stur            x3, [fp, #-0x38]
    // 0x77116c: LoadField: r4 = r3->field_f
    //     0x77116c: ldur            w4, [x3, #0xf]
    // 0x771170: DecompressPointer r4
    //     0x771170: add             x4, x4, HEAP, lsl #32
    // 0x771174: mov             x0, x4
    // 0x771178: stur            x4, [fp, #-0x18]
    // 0x77117c: r2 = Null
    //     0x77117c: mov             x2, NULL
    // 0x771180: r1 = Null
    //     0x771180: mov             x1, NULL
    // 0x771184: cmp             w0, NULL
    // 0x771188: b.eq            #0x7711d4
    // 0x77118c: branchIfSmi(r0, 0x7711d4)
    //     0x77118c: tbz             w0, #0, #0x7711d4
    // 0x771190: r3 = SubtypeTestCache
    //     0x771190: add             x3, PP, #0x45, lsl #12  ; [pp+0x45638] SubtypeTestCache
    //     0x771194: ldr             x3, [x3, #0x638]
    // 0x771198: r30 = Subtype6TestCacheStub
    //     0x771198: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27cc)
    // 0x77119c: LoadField: r30 = r30->field_7
    //     0x77119c: ldur            lr, [lr, #7]
    // 0x7711a0: blr             lr
    // 0x7711a4: cmp             w7, NULL
    // 0x7711a8: b.eq            #0x7711b4
    // 0x7711ac: tbnz            w7, #4, #0x7711d4
    // 0x7711b0: b               #0x7711dc
    // 0x7711b4: r8 = (dynamic this, RenderBox) => RenderBox?
    //     0x7711b4: add             x8, PP, #0x45, lsl #12  ; [pp+0x45640] FunctionType: (dynamic this, RenderBox) => RenderBox?
    //     0x7711b8: ldr             x8, [x8, #0x640]
    // 0x7711bc: r3 = SubtypeTestCache
    //     0x7711bc: add             x3, PP, #0x45, lsl #12  ; [pp+0x45648] SubtypeTestCache
    //     0x7711c0: ldr             x3, [x3, #0x648]
    // 0x7711c4: r30 = InstanceOfStub
    //     0x7711c4: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x7711c8: LoadField: r30 = r30->field_7
    //     0x7711c8: ldur            lr, [lr, #7]
    // 0x7711cc: blr             lr
    // 0x7711d0: b               #0x7711e0
    // 0x7711d4: r0 = false
    //     0x7711d4: add             x0, NULL, #0x30  ; false
    // 0x7711d8: b               #0x7711e0
    // 0x7711dc: r0 = true
    //     0x7711dc: add             x0, NULL, #0x20  ; true
    // 0x7711e0: tbnz            w0, #4, #0x7715c8
    // 0x7711e4: ldur            x3, [fp, #-8]
    // 0x7711e8: ldur            x0, [fp, #-0x38]
    // 0x7711ec: ldur            x1, [fp, #-0x30]
    // 0x7711f0: ldur            x2, [fp, #-0x28]
    // 0x7711f4: LoadField: r4 = r0->field_13
    //     0x7711f4: ldur            w4, [x0, #0x13]
    // 0x7711f8: DecompressPointer r4
    //     0x7711f8: add             x4, x4, HEAP, lsl #32
    // 0x7711fc: LoadField: r0 = r3->field_13
    //     0x7711fc: ldur            w0, [x3, #0x13]
    // 0x771200: DecompressPointer r0
    //     0x771200: add             x0, x0, HEAP, lsl #32
    // 0x771204: stur            x0, [fp, #-0x38]
    // 0x771208: LoadField: d0 = r1->field_7
    //     0x771208: ldur            d0, [x1, #7]
    // 0x77120c: LoadField: d1 = r2->field_7
    //     0x77120c: ldur            d1, [x2, #7]
    // 0x771210: stur            d1, [fp, #-0x58]
    // 0x771214: mov             v2.16b, v0.16b
    // 0x771218: ldur            x3, [fp, #-0x10]
    // 0x77121c: ldur            d0, [fp, #-0x48]
    // 0x771220: stur            x4, [fp, #-8]
    // 0x771224: stur            d2, [fp, #-0x50]
    // 0x771228: CheckStackOverflow
    //     0x771228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x77122c: cmp             SP, x16
    //     0x771230: b.ls            #0x7715f0
    // 0x771234: cmp             w4, NULL
    // 0x771238: b.eq            #0x771434
    // 0x77123c: cmp             w0, NULL
    // 0x771240: b.eq            #0x771270
    // 0x771244: LoadField: r2 = r3->field_83
    //     0x771244: ldur            w2, [x3, #0x83]
    // 0x771248: DecompressPointer r2
    //     0x771248: add             x2, x2, HEAP, lsl #32
    // 0x77124c: cmp             w2, NULL
    // 0x771250: b.eq            #0x7715f8
    // 0x771254: mov             x1, x4
    // 0x771258: r0 = getDistanceToActualBaseline()
    //     0x771258: bl              #0x74b4d4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDistanceToActualBaseline
    // 0x77125c: cmp             w0, NULL
    // 0x771260: r16 = true
    //     0x771260: add             x16, NULL, #0x20  ; true
    // 0x771264: r17 = false
    //     0x771264: add             x17, NULL, #0x30  ; false
    // 0x771268: csel            x1, x16, x17, ne
    // 0x77126c: b               #0x771278
    // 0x771270: r1 = false
    //     0x771270: add             x1, NULL, #0x30  ; false
    // 0x771274: r0 = Null
    //     0x771274: mov             x0, NULL
    // 0x771278: tbnz            w1, #4, #0x7712a4
    // 0x77127c: ldur            x3, [fp, #-0x38]
    // 0x771280: cmp             w0, NULL
    // 0x771284: b.eq            #0x7715fc
    // 0x771288: cmp             w3, NULL
    // 0x77128c: b.eq            #0x771600
    // 0x771290: LoadField: d0 = r0->field_7
    //     0x771290: ldur            d0, [x0, #7]
    // 0x771294: LoadField: d1 = r3->field_7
    //     0x771294: ldur            d1, [x3, #7]
    // 0x771298: fsub            d3, d1, d0
    // 0x77129c: mov             v0.16b, v3.16b
    // 0x7712a0: b               #0x7712fc
    // 0x7712a4: ldur            x0, [fp, #-0x10]
    // 0x7712a8: ldur            x3, [fp, #-0x38]
    // 0x7712ac: ldur            x4, [fp, #-8]
    // 0x7712b0: LoadField: r1 = r0->field_77
    //     0x7712b0: ldur            w1, [x0, #0x77]
    // 0x7712b4: DecompressPointer r1
    //     0x7712b4: add             x1, x1, HEAP, lsl #32
    // 0x7712b8: LoadField: r2 = r4->field_53
    //     0x7712b8: ldur            w2, [x4, #0x53]
    // 0x7712bc: DecompressPointer r2
    //     0x7712bc: add             x2, x2, HEAP, lsl #32
    // 0x7712c0: cmp             w2, NULL
    // 0x7712c4: b.eq            #0x771514
    // 0x7712c8: LoadField: r5 = r0->field_6b
    //     0x7712c8: ldur            w5, [x0, #0x6b]
    // 0x7712cc: DecompressPointer r5
    //     0x7712cc: add             x5, x5, HEAP, lsl #32
    // 0x7712d0: LoadField: r6 = r5->field_7
    //     0x7712d0: ldur            x6, [x5, #7]
    // 0x7712d4: cmp             x6, #0
    // 0x7712d8: b.gt            #0x7712e4
    // 0x7712dc: LoadField: d0 = r2->field_f
    //     0x7712dc: ldur            d0, [x2, #0xf]
    // 0x7712e0: b               #0x7712e8
    // 0x7712e4: LoadField: d0 = r2->field_7
    //     0x7712e4: ldur            d0, [x2, #7]
    // 0x7712e8: ldur            d1, [fp, #-0x48]
    // 0x7712ec: fsub            d2, d1, d0
    // 0x7712f0: mov             v0.16b, v2.16b
    // 0x7712f4: ldur            x2, [fp, #-0x20]
    // 0x7712f8: r0 = _getChildCrossAxisOffset()
    //     0x7712f8: bl              #0x742eac  ; [package:flutter/src/rendering/flex.dart] CrossAxisAlignment::_getChildCrossAxisOffset
    // 0x7712fc: ldur            x3, [fp, #-0x10]
    // 0x771300: ldur            x4, [fp, #-8]
    // 0x771304: stur            d0, [fp, #-0x60]
    // 0x771308: LoadField: r5 = r4->field_7
    //     0x771308: ldur            w5, [x4, #7]
    // 0x77130c: DecompressPointer r5
    //     0x77130c: add             x5, x5, HEAP, lsl #32
    // 0x771310: stur            x5, [fp, #-0x28]
    // 0x771314: cmp             w5, NULL
    // 0x771318: b.eq            #0x771604
    // 0x77131c: mov             x0, x5
    // 0x771320: r2 = Null
    //     0x771320: mov             x2, NULL
    // 0x771324: r1 = Null
    //     0x771324: mov             x1, NULL
    // 0x771328: r4 = LoadClassIdInstr(r0)
    //     0x771328: ldur            x4, [x0, #-1]
    //     0x77132c: ubfx            x4, x4, #0xc, #0x14
    // 0x771330: cmp             x4, #0xc7e
    // 0x771334: b.eq            #0x77134c
    // 0x771338: r8 = FlexParentData
    //     0x771338: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a590] Type: FlexParentData
    //     0x77133c: ldr             x8, [x8, #0x590]
    // 0x771340: r3 = Null
    //     0x771340: add             x3, PP, #0x45, lsl #12  ; [pp+0x45650] Null
    //     0x771344: ldr             x3, [x3, #0x650]
    // 0x771348: r0 = DefaultTypeTest()
    //     0x771348: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x77134c: ldur            x0, [fp, #-0x10]
    // 0x771350: LoadField: r1 = r0->field_6b
    //     0x771350: ldur            w1, [x0, #0x6b]
    // 0x771354: DecompressPointer r1
    //     0x771354: add             x1, x1, HEAP, lsl #32
    // 0x771358: LoadField: r2 = r1->field_7
    //     0x771358: ldur            x2, [x1, #7]
    // 0x77135c: stur            x2, [fp, #-0x40]
    // 0x771360: cmp             x2, #0
    // 0x771364: b.gt            #0x771388
    // 0x771368: ldur            d1, [fp, #-0x50]
    // 0x77136c: ldur            d0, [fp, #-0x60]
    // 0x771370: r0 = Offset()
    //     0x771370: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x771374: ldur            d0, [fp, #-0x50]
    // 0x771378: StoreField: r0->field_7 = d0
    //     0x771378: stur            d0, [x0, #7]
    // 0x77137c: ldur            d1, [fp, #-0x60]
    // 0x771380: StoreField: r0->field_f = d1
    //     0x771380: stur            d1, [x0, #0xf]
    // 0x771384: b               #0x7713a4
    // 0x771388: ldur            d0, [fp, #-0x50]
    // 0x77138c: ldur            d1, [fp, #-0x60]
    // 0x771390: r0 = Offset()
    //     0x771390: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x771394: ldur            d0, [fp, #-0x60]
    // 0x771398: StoreField: r0->field_7 = d0
    //     0x771398: stur            d0, [x0, #7]
    // 0x77139c: ldur            d0, [fp, #-0x50]
    // 0x7713a0: StoreField: r0->field_f = d0
    //     0x7713a0: stur            d0, [x0, #0xf]
    // 0x7713a4: ldur            x3, [fp, #-8]
    // 0x7713a8: ldur            x1, [fp, #-0x28]
    // 0x7713ac: StoreField: r1->field_7 = r0
    //     0x7713ac: stur            w0, [x1, #7]
    //     0x7713b0: ldurb           w16, [x1, #-1]
    //     0x7713b4: ldurb           w17, [x0, #-1]
    //     0x7713b8: and             x16, x17, x16, lsr #2
    //     0x7713bc: tst             x16, HEAP, lsr #32
    //     0x7713c0: b.eq            #0x7713c8
    //     0x7713c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7713c8: LoadField: r0 = r3->field_53
    //     0x7713c8: ldur            w0, [x3, #0x53]
    // 0x7713cc: DecompressPointer r0
    //     0x7713cc: add             x0, x0, HEAP, lsl #32
    // 0x7713d0: cmp             w0, NULL
    // 0x7713d4: b.eq            #0x771460
    // 0x7713d8: ldur            x1, [fp, #-0x40]
    // 0x7713dc: cmp             x1, #0
    // 0x7713e0: b.gt            #0x7713f0
    // 0x7713e4: LoadField: d1 = r0->field_7
    //     0x7713e4: ldur            d1, [x0, #7]
    // 0x7713e8: mov             v2.16b, v1.16b
    // 0x7713ec: b               #0x7713f8
    // 0x7713f0: LoadField: d1 = r0->field_f
    //     0x7713f0: ldur            d1, [x0, #0xf]
    // 0x7713f4: mov             v2.16b, v1.16b
    // 0x7713f8: ldur            d1, [fp, #-0x58]
    // 0x7713fc: fadd            d3, d2, d1
    // 0x771400: fadd            d2, d0, d3
    // 0x771404: stur            d2, [fp, #-0x60]
    // 0x771408: ldur            x16, [fp, #-0x18]
    // 0x77140c: stp             x3, x16, [SP]
    // 0x771410: ldur            x0, [fp, #-0x18]
    // 0x771414: ClosureCall
    //     0x771414: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x771418: ldur            x2, [x0, #0x1f]
    //     0x77141c: blr             x2
    // 0x771420: ldur            d2, [fp, #-0x60]
    // 0x771424: mov             x4, x0
    // 0x771428: ldur            x0, [fp, #-0x38]
    // 0x77142c: ldur            d1, [fp, #-0x58]
    // 0x771430: b               #0x771218
    // 0x771434: r0 = Null
    //     0x771434: mov             x0, NULL
    // 0x771438: LeaveFrame
    //     0x771438: mov             SP, fp
    //     0x77143c: ldp             fp, lr, [SP], #0x10
    // 0x771440: ret
    //     0x771440: ret             
    // 0x771444: r0 = StateError()
    //     0x771444: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x771448: mov             x1, x0
    // 0x77144c: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x77144c: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x771450: StoreField: r1->field_b = r0
    //     0x771450: stur            w0, [x1, #0xb]
    // 0x771454: mov             x0, x1
    // 0x771458: r0 = Throw()
    //     0x771458: bl              #0xec04b8  ; ThrowStub
    // 0x77145c: brk             #0
    // 0x771460: r1 = Null
    //     0x771460: mov             x1, NULL
    // 0x771464: r2 = 8
    //     0x771464: movz            x2, #0x8
    // 0x771468: r0 = AllocateArray()
    //     0x771468: bl              #0xec22fc  ; AllocateArrayStub
    // 0x77146c: stur            x0, [fp, #-0x10]
    // 0x771470: r16 = "RenderBox was not laid out: "
    //     0x771470: ldr             x16, [PP, #0x44c0]  ; [pp+0x44c0] "RenderBox was not laid out: "
    // 0x771474: StoreField: r0->field_f = r16
    //     0x771474: stur            w16, [x0, #0xf]
    // 0x771478: ldur            x16, [fp, #-8]
    // 0x77147c: str             x16, [SP]
    // 0x771480: r0 = runtimeType()
    //     0x771480: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0x771484: ldur            x1, [fp, #-0x10]
    // 0x771488: ArrayStore: r1[1] = r0  ; List_4
    //     0x771488: add             x25, x1, #0x13
    //     0x77148c: str             w0, [x25]
    //     0x771490: tbz             w0, #0, #0x7714ac
    //     0x771494: ldurb           w16, [x1, #-1]
    //     0x771498: ldurb           w17, [x0, #-1]
    //     0x77149c: and             x16, x17, x16, lsr #2
    //     0x7714a0: tst             x16, HEAP, lsr #32
    //     0x7714a4: b.eq            #0x7714ac
    //     0x7714a8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7714ac: ldur            x0, [fp, #-0x10]
    // 0x7714b0: r16 = "#"
    //     0x7714b0: ldr             x16, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0x7714b4: ArrayStore: r0[0] = r16  ; List_4
    //     0x7714b4: stur            w16, [x0, #0x17]
    // 0x7714b8: ldur            x1, [fp, #-8]
    // 0x7714bc: r0 = shortHash()
    //     0x7714bc: bl              #0x67f178  ; [package:flutter/src/foundation/diagnostics.dart] ::shortHash
    // 0x7714c0: ldur            x1, [fp, #-0x10]
    // 0x7714c4: ArrayStore: r1[3] = r0  ; List_4
    //     0x7714c4: add             x25, x1, #0x1b
    //     0x7714c8: str             w0, [x25]
    //     0x7714cc: tbz             w0, #0, #0x7714e8
    //     0x7714d0: ldurb           w16, [x1, #-1]
    //     0x7714d4: ldurb           w17, [x0, #-1]
    //     0x7714d8: and             x16, x17, x16, lsr #2
    //     0x7714dc: tst             x16, HEAP, lsr #32
    //     0x7714e0: b.eq            #0x7714e8
    //     0x7714e4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7714e8: ldur            x16, [fp, #-0x10]
    // 0x7714ec: str             x16, [SP]
    // 0x7714f0: r0 = _interpolate()
    //     0x7714f0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7714f4: stur            x0, [fp, #-0x10]
    // 0x7714f8: r0 = StateError()
    //     0x7714f8: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x7714fc: mov             x1, x0
    // 0x771500: ldur            x0, [fp, #-0x10]
    // 0x771504: StoreField: r1->field_b = r0
    //     0x771504: stur            w0, [x1, #0xb]
    // 0x771508: mov             x0, x1
    // 0x77150c: r0 = Throw()
    //     0x77150c: bl              #0xec04b8  ; ThrowStub
    // 0x771510: brk             #0
    // 0x771514: r1 = Null
    //     0x771514: mov             x1, NULL
    // 0x771518: r2 = 8
    //     0x771518: movz            x2, #0x8
    // 0x77151c: r0 = AllocateArray()
    //     0x77151c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x771520: stur            x0, [fp, #-0x10]
    // 0x771524: r16 = "RenderBox was not laid out: "
    //     0x771524: ldr             x16, [PP, #0x44c0]  ; [pp+0x44c0] "RenderBox was not laid out: "
    // 0x771528: StoreField: r0->field_f = r16
    //     0x771528: stur            w16, [x0, #0xf]
    // 0x77152c: ldur            x16, [fp, #-8]
    // 0x771530: str             x16, [SP]
    // 0x771534: r0 = runtimeType()
    //     0x771534: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0x771538: ldur            x1, [fp, #-0x10]
    // 0x77153c: ArrayStore: r1[1] = r0  ; List_4
    //     0x77153c: add             x25, x1, #0x13
    //     0x771540: str             w0, [x25]
    //     0x771544: tbz             w0, #0, #0x771560
    //     0x771548: ldurb           w16, [x1, #-1]
    //     0x77154c: ldurb           w17, [x0, #-1]
    //     0x771550: and             x16, x17, x16, lsr #2
    //     0x771554: tst             x16, HEAP, lsr #32
    //     0x771558: b.eq            #0x771560
    //     0x77155c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x771560: ldur            x0, [fp, #-0x10]
    // 0x771564: r16 = "#"
    //     0x771564: ldr             x16, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0x771568: ArrayStore: r0[0] = r16  ; List_4
    //     0x771568: stur            w16, [x0, #0x17]
    // 0x77156c: ldur            x1, [fp, #-8]
    // 0x771570: r0 = shortHash()
    //     0x771570: bl              #0x67f178  ; [package:flutter/src/foundation/diagnostics.dart] ::shortHash
    // 0x771574: ldur            x1, [fp, #-0x10]
    // 0x771578: ArrayStore: r1[3] = r0  ; List_4
    //     0x771578: add             x25, x1, #0x1b
    //     0x77157c: str             w0, [x25]
    //     0x771580: tbz             w0, #0, #0x77159c
    //     0x771584: ldurb           w16, [x1, #-1]
    //     0x771588: ldurb           w17, [x0, #-1]
    //     0x77158c: and             x16, x17, x16, lsr #2
    //     0x771590: tst             x16, HEAP, lsr #32
    //     0x771594: b.eq            #0x77159c
    //     0x771598: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x77159c: ldur            x16, [fp, #-0x10]
    // 0x7715a0: str             x16, [SP]
    // 0x7715a4: r0 = _interpolate()
    //     0x7715a4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7715a8: stur            x0, [fp, #-8]
    // 0x7715ac: r0 = StateError()
    //     0x7715ac: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x7715b0: mov             x1, x0
    // 0x7715b4: ldur            x0, [fp, #-8]
    // 0x7715b8: StoreField: r1->field_b = r0
    //     0x7715b8: stur            w0, [x1, #0xb]
    // 0x7715bc: mov             x0, x1
    // 0x7715c0: r0 = Throw()
    //     0x7715c0: bl              #0xec04b8  ; ThrowStub
    // 0x7715c4: brk             #0
    // 0x7715c8: r0 = StateError()
    //     0x7715c8: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x7715cc: mov             x1, x0
    // 0x7715d0: r0 = "Pattern matching error"
    //     0x7715d0: add             x0, PP, #0x45, lsl #12  ; [pp+0x45660] "Pattern matching error"
    //     0x7715d4: ldr             x0, [x0, #0x660]
    // 0x7715d8: StoreField: r1->field_b = r0
    //     0x7715d8: stur            w0, [x1, #0xb]
    // 0x7715dc: mov             x0, x1
    // 0x7715e0: r0 = Throw()
    //     0x7715e0: bl              #0xec04b8  ; ThrowStub
    // 0x7715e4: brk             #0
    // 0x7715e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7715e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7715ec: b               #0x770f88
    // 0x7715f0: r0 = StackOverflowSharedWithFPURegs()
    //     0x7715f0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7715f4: b               #0x771234
    // 0x7715f8: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7715f8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x7715fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7715fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x771600: r0 = NullErrorSharedWithoutFPURegs()
    //     0x771600: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x771604: r0 = NullCastErrorSharedWithFPURegs()
    //     0x771604: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ setupParentData(/* No info */) {
    // ** addr: 0x787f88, size: 0xb0
    // 0x787f88: EnterFrame
    //     0x787f88: stp             fp, lr, [SP, #-0x10]!
    //     0x787f8c: mov             fp, SP
    // 0x787f90: AllocStack(0x8)
    //     0x787f90: sub             SP, SP, #8
    // 0x787f94: SetupParameters(RenderFlex this /* r1 => r4 */, dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x787f94: mov             x0, x2
    //     0x787f98: mov             x4, x1
    //     0x787f9c: mov             x3, x2
    //     0x787fa0: stur            x2, [fp, #-8]
    // 0x787fa4: r2 = Null
    //     0x787fa4: mov             x2, NULL
    // 0x787fa8: r1 = Null
    //     0x787fa8: mov             x1, NULL
    // 0x787fac: r4 = 60
    //     0x787fac: movz            x4, #0x3c
    // 0x787fb0: branchIfSmi(r0, 0x787fbc)
    //     0x787fb0: tbz             w0, #0, #0x787fbc
    // 0x787fb4: r4 = LoadClassIdInstr(r0)
    //     0x787fb4: ldur            x4, [x0, #-1]
    //     0x787fb8: ubfx            x4, x4, #0xc, #0x14
    // 0x787fbc: sub             x4, x4, #0xbba
    // 0x787fc0: cmp             x4, #0x9a
    // 0x787fc4: b.ls            #0x787fd8
    // 0x787fc8: r8 = RenderBox
    //     0x787fc8: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x787fcc: r3 = Null
    //     0x787fcc: add             x3, PP, #0x45, lsl #12  ; [pp+0x45758] Null
    //     0x787fd0: ldr             x3, [x3, #0x758]
    // 0x787fd4: r0 = RenderBox()
    //     0x787fd4: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x787fd8: ldur            x0, [fp, #-8]
    // 0x787fdc: LoadField: r1 = r0->field_7
    //     0x787fdc: ldur            w1, [x0, #7]
    // 0x787fe0: DecompressPointer r1
    //     0x787fe0: add             x1, x1, HEAP, lsl #32
    // 0x787fe4: r2 = LoadClassIdInstr(r1)
    //     0x787fe4: ldur            x2, [x1, #-1]
    //     0x787fe8: ubfx            x2, x2, #0xc, #0x14
    // 0x787fec: cmp             x2, #0xc7e
    // 0x787ff0: b.eq            #0x788028
    // 0x787ff4: r1 = <RenderBox>
    //     0x787ff4: add             x1, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x787ff8: ldr             x1, [x1, #0x1d8]
    // 0x787ffc: r0 = FlexParentData()
    //     0x787ffc: bl              #0x788038  ; AllocateFlexParentDataStub -> FlexParentData (size=0x20)
    // 0x788000: r1 = Instance_Offset
    //     0x788000: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x788004: StoreField: r0->field_7 = r1
    //     0x788004: stur            w1, [x0, #7]
    // 0x788008: ldur            x1, [fp, #-8]
    // 0x78800c: StoreField: r1->field_7 = r0
    //     0x78800c: stur            w0, [x1, #7]
    //     0x788010: ldurb           w16, [x1, #-1]
    //     0x788014: ldurb           w17, [x0, #-1]
    //     0x788018: and             x16, x17, x16, lsr #2
    //     0x78801c: tst             x16, HEAP, lsr #32
    //     0x788020: b.eq            #0x788028
    //     0x788024: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x788028: r0 = Null
    //     0x788028: mov             x0, NULL
    // 0x78802c: LeaveFrame
    //     0x78802c: mov             SP, fp
    //     0x788030: ldp             fp, lr, [SP], #0x10
    // 0x788034: ret
    //     0x788034: ret             
  }
  _ paint(/* No info */) {
    // ** addr: 0x7993f4, size: 0x150
    // 0x7993f4: EnterFrame
    //     0x7993f4: stp             fp, lr, [SP, #-0x10]!
    //     0x7993f8: mov             fp, SP
    // 0x7993fc: AllocStack(0x40)
    //     0x7993fc: sub             SP, SP, #0x40
    // 0x799400: d0 = 0.000000
    //     0x799400: ldr             d0, [PP, #0x6fc8]  ; [pp+0x6fc8] IMM: double(1e-10) from 0x3ddb7cdfd9d7bdbb
    // 0x799404: mov             x0, x1
    // 0x799408: stur            x1, [fp, #-8]
    // 0x79940c: stur            x2, [fp, #-0x10]
    // 0x799410: stur            x3, [fp, #-0x18]
    // 0x799414: CheckStackOverflow
    //     0x799414: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x799418: cmp             SP, x16
    //     0x79941c: b.ls            #0x799534
    // 0x799420: LoadField: d1 = r0->field_87
    //     0x799420: ldur            d1, [x0, #0x87]
    // 0x799424: fcmp            d1, d0
    // 0x799428: r16 = true
    //     0x799428: add             x16, NULL, #0x20  ; true
    // 0x79942c: r17 = false
    //     0x79942c: add             x17, NULL, #0x30  ; false
    // 0x799430: csel            x1, x16, x17, gt
    // 0x799434: tbz             w1, #4, #0x799450
    // 0x799438: mov             x1, x0
    // 0x79943c: r0 = defaultPaint()
    //     0x79943c: bl              #0x799544  ; [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultPaint
    // 0x799440: r0 = Null
    //     0x799440: mov             x0, NULL
    // 0x799444: LeaveFrame
    //     0x799444: mov             SP, fp
    //     0x799448: ldp             fp, lr, [SP], #0x10
    // 0x79944c: ret
    //     0x79944c: ret             
    // 0x799450: mov             x1, x0
    // 0x799454: r0 = size()
    //     0x799454: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x799458: LoadField: d0 = r0->field_7
    //     0x799458: ldur            d0, [x0, #7]
    // 0x79945c: d1 = 0.000000
    //     0x79945c: eor             v1.16b, v1.16b, v1.16b
    // 0x799460: fcmp            d1, d0
    // 0x799464: b.ge            #0x799474
    // 0x799468: LoadField: d0 = r0->field_f
    //     0x799468: ldur            d0, [x0, #0xf]
    // 0x79946c: fcmp            d1, d0
    // 0x799470: b.lt            #0x799484
    // 0x799474: r0 = Null
    //     0x799474: mov             x0, NULL
    // 0x799478: LeaveFrame
    //     0x799478: mov             SP, fp
    //     0x79947c: ldp             fp, lr, [SP], #0x10
    // 0x799480: ret
    //     0x799480: ret             
    // 0x799484: ldur            x0, [fp, #-8]
    // 0x799488: LoadField: r2 = r0->field_9b
    //     0x799488: ldur            w2, [x0, #0x9b]
    // 0x79948c: DecompressPointer r2
    //     0x79948c: add             x2, x2, HEAP, lsl #32
    // 0x799490: stur            x2, [fp, #-0x28]
    // 0x799494: LoadField: r3 = r0->field_37
    //     0x799494: ldur            w3, [x0, #0x37]
    // 0x799498: DecompressPointer r3
    //     0x799498: add             x3, x3, HEAP, lsl #32
    // 0x79949c: r16 = Sentinel
    //     0x79949c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7994a0: cmp             w3, w16
    // 0x7994a4: b.eq            #0x79953c
    // 0x7994a8: mov             x1, x0
    // 0x7994ac: stur            x3, [fp, #-0x20]
    // 0x7994b0: r0 = size()
    //     0x7994b0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7994b4: mov             x2, x0
    // 0x7994b8: r1 = Instance_Offset
    //     0x7994b8: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7994bc: r0 = &()
    //     0x7994bc: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7994c0: mov             x3, x0
    // 0x7994c4: ldur            x0, [fp, #-0x28]
    // 0x7994c8: stur            x3, [fp, #-0x38]
    // 0x7994cc: LoadField: r7 = r0->field_b
    //     0x7994cc: ldur            w7, [x0, #0xb]
    // 0x7994d0: DecompressPointer r7
    //     0x7994d0: add             x7, x7, HEAP, lsl #32
    // 0x7994d4: ldur            x2, [fp, #-8]
    // 0x7994d8: stur            x7, [fp, #-0x30]
    // 0x7994dc: r1 = Function 'defaultPaint':.
    //     0x7994dc: add             x1, PP, #0x45, lsl #12  ; [pp+0x455e8] AnonymousClosure: (0x79966c), in [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultPaint (0x799544)
    //     0x7994e0: ldr             x1, [x1, #0x5e8]
    // 0x7994e4: r0 = AllocateClosure()
    //     0x7994e4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7994e8: r16 = Instance_Clip
    //     0x7994e8: add             x16, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x7994ec: ldr             x16, [x16, #0x750]
    // 0x7994f0: str             x16, [SP]
    // 0x7994f4: ldur            x1, [fp, #-0x10]
    // 0x7994f8: ldur            x2, [fp, #-0x20]
    // 0x7994fc: ldur            x3, [fp, #-0x18]
    // 0x799500: ldur            x5, [fp, #-0x38]
    // 0x799504: mov             x6, x0
    // 0x799508: ldur            x7, [fp, #-0x30]
    // 0x79950c: r4 = const [0, 0x7, 0x1, 0x6, clipBehavior, 0x6, null]
    //     0x79950c: add             x4, PP, #0x44, lsl #12  ; [pp+0x44c68] List(7) [0, 0x7, 0x1, 0x6, "clipBehavior", 0x6, Null]
    //     0x799510: ldr             x4, [x4, #0xc68]
    // 0x799514: r0 = pushClipRect()
    //     0x799514: bl              #0x78c920  ; [package:flutter/src/rendering/object.dart] PaintingContext::pushClipRect
    // 0x799518: ldur            x1, [fp, #-0x28]
    // 0x79951c: mov             x2, x0
    // 0x799520: r0 = layer=()
    //     0x799520: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0x799524: r0 = Null
    //     0x799524: mov             x0, NULL
    // 0x799528: LeaveFrame
    //     0x799528: mov             SP, fp
    //     0x79952c: ldp             fp, lr, [SP], #0x10
    // 0x799530: ret
    //     0x799530: ret             
    // 0x799534: r0 = StackOverflowSharedWithFPURegs()
    //     0x799534: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x799538: b               #0x799420
    // 0x79953c: r9 = _needsCompositing
    //     0x79953c: ldr             x9, [PP, #0x2c58]  ; [pp+0x2c58] Field <RenderObject._needsCompositing@390266271>: late (offset: 0x38)
    // 0x799540: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x799540: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0x7a0f40, size: 0x50
    // 0x7a0f40: EnterFrame
    //     0x7a0f40: stp             fp, lr, [SP, #-0x10]!
    //     0x7a0f44: mov             fp, SP
    // 0x7a0f48: AllocStack(0x8)
    //     0x7a0f48: sub             SP, SP, #8
    // 0x7a0f4c: SetupParameters(RenderFlex this /* r1 => r0, fp-0x8 */)
    //     0x7a0f4c: mov             x0, x1
    //     0x7a0f50: stur            x1, [fp, #-8]
    // 0x7a0f54: CheckStackOverflow
    //     0x7a0f54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a0f58: cmp             SP, x16
    //     0x7a0f5c: b.ls            #0x7a0f88
    // 0x7a0f60: LoadField: r1 = r0->field_9b
    //     0x7a0f60: ldur            w1, [x0, #0x9b]
    // 0x7a0f64: DecompressPointer r1
    //     0x7a0f64: add             x1, x1, HEAP, lsl #32
    // 0x7a0f68: r2 = Null
    //     0x7a0f68: mov             x2, NULL
    // 0x7a0f6c: r0 = layer=()
    //     0x7a0f6c: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0x7a0f70: ldur            x1, [fp, #-8]
    // 0x7a0f74: r0 = dispose()
    //     0x7a0f74: bl              #0x7a1568  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin::dispose
    // 0x7a0f78: r0 = Null
    //     0x7a0f78: mov             x0, NULL
    // 0x7a0f7c: LeaveFrame
    //     0x7a0f7c: mov             SP, fp
    //     0x7a0f80: ldp             fp, lr, [SP], #0x10
    // 0x7a0f84: ret
    //     0x7a0f84: ret             
    // 0x7a0f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a0f88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a0f8c: b               #0x7a0f60
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x7fe374, size: 0x2c
    // 0x7fe374: EnterFrame
    //     0x7fe374: stp             fp, lr, [SP, #-0x10]!
    //     0x7fe378: mov             fp, SP
    // 0x7fe37c: CheckStackOverflow
    //     0x7fe37c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fe380: cmp             SP, x16
    //     0x7fe384: b.ls            #0x7fe398
    // 0x7fe388: r0 = defaultHitTestChildren()
    //     0x7fe388: bl              #0x7fe3a0  ; [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultHitTestChildren
    // 0x7fe38c: LeaveFrame
    //     0x7fe38c: mov             SP, fp
    //     0x7fe390: ldp             fp, lr, [SP], #0x10
    // 0x7fe394: ret
    //     0x7fe394: ret             
    // 0x7fe398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fe398: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fe39c: b               #0x7fe388
  }
  _ RenderFlex(/* No info */) {
    // ** addr: 0x85618c, size: 0x1b4
    // 0x85618c: EnterFrame
    //     0x85618c: stp             fp, lr, [SP, #-0x10]!
    //     0x856190: mov             fp, SP
    // 0x856194: AllocStack(0x38)
    //     0x856194: sub             SP, SP, #0x38
    // 0x856198: SetupParameters(RenderFlex this /* r1 => r7, fp-0x10 */, dynamic _ /* r2 => r6, fp-0x18 */, dynamic _ /* r3 => r5, fp-0x20 */, dynamic _ /* r5 => r3, fp-0x28 */, dynamic _ /* r6 => r2, fp-0x30 */, dynamic _ /* r7 => r0, fp-0x38 */, {dynamic textBaseline = Null /* r4, fp-0x8 */})
    //     0x856198: mov             x0, x7
    //     0x85619c: stur            x7, [fp, #-0x38]
    //     0x8561a0: mov             x7, x1
    //     0x8561a4: stur            x2, [fp, #-0x18]
    //     0x8561a8: mov             x16, x6
    //     0x8561ac: mov             x6, x2
    //     0x8561b0: mov             x2, x16
    //     0x8561b4: stur            x3, [fp, #-0x20]
    //     0x8561b8: mov             x16, x5
    //     0x8561bc: mov             x5, x3
    //     0x8561c0: mov             x3, x16
    //     0x8561c4: stur            x1, [fp, #-0x10]
    //     0x8561c8: stur            x3, [fp, #-0x28]
    //     0x8561cc: stur            x2, [fp, #-0x30]
    //     0x8561d0: ldur            w1, [x4, #0x13]
    //     0x8561d4: ldur            w8, [x4, #0x1f]
    //     0x8561d8: add             x8, x8, HEAP, lsl #32
    //     0x8561dc: ldr             x16, [PP, #0x4798]  ; [pp+0x4798] "textBaseline"
    //     0x8561e0: cmp             w8, w16
    //     0x8561e4: b.ne            #0x856204
    //     0x8561e8: ldur            w8, [x4, #0x23]
    //     0x8561ec: add             x8, x8, HEAP, lsl #32
    //     0x8561f0: sub             w4, w1, w8
    //     0x8561f4: add             x1, fp, w4, sxtw #2
    //     0x8561f8: ldr             x1, [x1, #8]
    //     0x8561fc: mov             x4, x1
    //     0x856200: b               #0x856208
    //     0x856204: mov             x4, NULL
    //     0x856208: stur            x4, [fp, #-8]
    // 0x85620c: CheckStackOverflow
    //     0x85620c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x856210: cmp             SP, x16
    //     0x856214: b.ls            #0x856338
    // 0x856218: StoreField: r7->field_87 = rZR
    //     0x856218: stur            xzr, [x7, #0x87]
    // 0x85621c: r1 = <ClipRectLayer>
    //     0x85621c: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a558] TypeArguments: <ClipRectLayer>
    //     0x856220: ldr             x1, [x1, #0x558]
    // 0x856224: r0 = LayerHandle()
    //     0x856224: bl              #0x6d28c4  ; AllocateLayerHandleStub -> LayerHandle<X0 bound Layer> (size=0x10)
    // 0x856228: ldur            x1, [fp, #-0x10]
    // 0x85622c: StoreField: r1->field_9b = r0
    //     0x85622c: stur            w0, [x1, #0x9b]
    //     0x856230: ldurb           w16, [x1, #-1]
    //     0x856234: ldurb           w17, [x0, #-1]
    //     0x856238: and             x16, x17, x16, lsr #2
    //     0x85623c: tst             x16, HEAP, lsr #32
    //     0x856240: b.eq            #0x856248
    //     0x856244: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856248: ldur            x0, [fp, #-0x20]
    // 0x85624c: StoreField: r1->field_6b = r0
    //     0x85624c: stur            w0, [x1, #0x6b]
    //     0x856250: ldurb           w16, [x1, #-1]
    //     0x856254: ldurb           w17, [x0, #-1]
    //     0x856258: and             x16, x17, x16, lsr #2
    //     0x85625c: tst             x16, HEAP, lsr #32
    //     0x856260: b.eq            #0x856268
    //     0x856264: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856268: ldur            x0, [fp, #-0x28]
    // 0x85626c: StoreField: r1->field_6f = r0
    //     0x85626c: stur            w0, [x1, #0x6f]
    //     0x856270: ldurb           w16, [x1, #-1]
    //     0x856274: ldurb           w17, [x0, #-1]
    //     0x856278: and             x16, x17, x16, lsr #2
    //     0x85627c: tst             x16, HEAP, lsr #32
    //     0x856280: b.eq            #0x856288
    //     0x856284: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856288: ldur            x0, [fp, #-0x30]
    // 0x85628c: StoreField: r1->field_73 = r0
    //     0x85628c: stur            w0, [x1, #0x73]
    //     0x856290: ldurb           w16, [x1, #-1]
    //     0x856294: ldurb           w17, [x0, #-1]
    //     0x856298: and             x16, x17, x16, lsr #2
    //     0x85629c: tst             x16, HEAP, lsr #32
    //     0x8562a0: b.eq            #0x8562a8
    //     0x8562a4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8562a8: ldur            x0, [fp, #-0x18]
    // 0x8562ac: StoreField: r1->field_77 = r0
    //     0x8562ac: stur            w0, [x1, #0x77]
    //     0x8562b0: ldurb           w16, [x1, #-1]
    //     0x8562b4: ldurb           w17, [x0, #-1]
    //     0x8562b8: and             x16, x17, x16, lsr #2
    //     0x8562bc: tst             x16, HEAP, lsr #32
    //     0x8562c0: b.eq            #0x8562c8
    //     0x8562c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8562c8: ldur            x0, [fp, #-0x38]
    // 0x8562cc: StoreField: r1->field_7b = r0
    //     0x8562cc: stur            w0, [x1, #0x7b]
    //     0x8562d0: ldurb           w16, [x1, #-1]
    //     0x8562d4: ldurb           w17, [x0, #-1]
    //     0x8562d8: and             x16, x17, x16, lsr #2
    //     0x8562dc: tst             x16, HEAP, lsr #32
    //     0x8562e0: b.eq            #0x8562e8
    //     0x8562e4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8562e8: r0 = Instance_VerticalDirection
    //     0x8562e8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0x8562ec: ldr             x0, [x0, #0x748]
    // 0x8562f0: StoreField: r1->field_7f = r0
    //     0x8562f0: stur            w0, [x1, #0x7f]
    // 0x8562f4: ldur            x0, [fp, #-8]
    // 0x8562f8: StoreField: r1->field_83 = r0
    //     0x8562f8: stur            w0, [x1, #0x83]
    //     0x8562fc: ldurb           w16, [x1, #-1]
    //     0x856300: ldurb           w17, [x0, #-1]
    //     0x856304: and             x16, x17, x16, lsr #2
    //     0x856308: tst             x16, HEAP, lsr #32
    //     0x85630c: b.eq            #0x856314
    //     0x856310: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856314: r0 = Instance_Clip
    //     0x856314: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x856318: ldr             x0, [x0, #0x750]
    // 0x85631c: StoreField: r1->field_8f = r0
    //     0x85631c: stur            w0, [x1, #0x8f]
    // 0x856320: StoreField: r1->field_93 = rZR
    //     0x856320: stur            xzr, [x1, #0x93]
    // 0x856324: r0 = _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin()
    //     0x856324: bl              #0x856340  ; [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin::_RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin
    // 0x856328: r0 = Null
    //     0x856328: mov             x0, NULL
    // 0x85632c: LeaveFrame
    //     0x85632c: mov             SP, fp
    //     0x856330: ldp             fp, lr, [SP], #0x10
    // 0x856334: ret
    //     0x856334: ret             
    // 0x856338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x856338: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85633c: b               #0x856218
  }
  set _ spacing=(/* No info */) {
    // ** addr: 0xc6921c, size: 0x50
    // 0xc6921c: EnterFrame
    //     0xc6921c: stp             fp, lr, [SP, #-0x10]!
    //     0xc69220: mov             fp, SP
    // 0xc69224: d1 = 0.000000
    //     0xc69224: eor             v1.16b, v1.16b, v1.16b
    // 0xc69228: CheckStackOverflow
    //     0xc69228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6922c: cmp             SP, x16
    //     0xc69230: b.ls            #0xc69264
    // 0xc69234: fcmp            d1, d1
    // 0xc69238: b.ne            #0xc6924c
    // 0xc6923c: r0 = Null
    //     0xc6923c: mov             x0, NULL
    // 0xc69240: LeaveFrame
    //     0xc69240: mov             SP, fp
    //     0xc69244: ldp             fp, lr, [SP], #0x10
    // 0xc69248: ret
    //     0xc69248: ret             
    // 0xc6924c: StoreField: r1->field_93 = rZR
    //     0xc6924c: stur            xzr, [x1, #0x93]
    // 0xc69250: r0 = markNeedsLayout()
    //     0xc69250: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc69254: r0 = Null
    //     0xc69254: mov             x0, NULL
    // 0xc69258: LeaveFrame
    //     0xc69258: mov             SP, fp
    //     0xc6925c: ldp             fp, lr, [SP], #0x10
    // 0xc69260: ret
    //     0xc69260: ret             
    // 0xc69264: r0 = StackOverflowSharedWithFPURegs()
    //     0xc69264: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc69268: b               #0xc69234
  }
  set _ textBaseline=(/* No info */) {
    // ** addr: 0xc6926c, size: 0x60
    // 0xc6926c: EnterFrame
    //     0xc6926c: stp             fp, lr, [SP, #-0x10]!
    //     0xc69270: mov             fp, SP
    // 0xc69274: mov             x0, x2
    // 0xc69278: CheckStackOverflow
    //     0xc69278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6927c: cmp             SP, x16
    //     0xc69280: b.ls            #0xc692c4
    // 0xc69284: LoadField: r2 = r1->field_83
    //     0xc69284: ldur            w2, [x1, #0x83]
    // 0xc69288: DecompressPointer r2
    //     0xc69288: add             x2, x2, HEAP, lsl #32
    // 0xc6928c: cmp             w2, w0
    // 0xc69290: b.eq            #0xc692b4
    // 0xc69294: StoreField: r1->field_83 = r0
    //     0xc69294: stur            w0, [x1, #0x83]
    //     0xc69298: ldurb           w16, [x1, #-1]
    //     0xc6929c: ldurb           w17, [x0, #-1]
    //     0xc692a0: and             x16, x17, x16, lsr #2
    //     0xc692a4: tst             x16, HEAP, lsr #32
    //     0xc692a8: b.eq            #0xc692b0
    //     0xc692ac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc692b0: r0 = markNeedsLayout()
    //     0xc692b0: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc692b4: r0 = Null
    //     0xc692b4: mov             x0, NULL
    // 0xc692b8: LeaveFrame
    //     0xc692b8: mov             SP, fp
    //     0xc692bc: ldp             fp, lr, [SP], #0x10
    // 0xc692c0: ret
    //     0xc692c0: ret             
    // 0xc692c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc692c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc692c8: b               #0xc69284
  }
  set _ textDirection=(/* No info */) {
    // ** addr: 0xc692cc, size: 0x60
    // 0xc692cc: EnterFrame
    //     0xc692cc: stp             fp, lr, [SP, #-0x10]!
    //     0xc692d0: mov             fp, SP
    // 0xc692d4: mov             x0, x2
    // 0xc692d8: CheckStackOverflow
    //     0xc692d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc692dc: cmp             SP, x16
    //     0xc692e0: b.ls            #0xc69324
    // 0xc692e4: LoadField: r2 = r1->field_7b
    //     0xc692e4: ldur            w2, [x1, #0x7b]
    // 0xc692e8: DecompressPointer r2
    //     0xc692e8: add             x2, x2, HEAP, lsl #32
    // 0xc692ec: cmp             w2, w0
    // 0xc692f0: b.eq            #0xc69314
    // 0xc692f4: StoreField: r1->field_7b = r0
    //     0xc692f4: stur            w0, [x1, #0x7b]
    //     0xc692f8: ldurb           w16, [x1, #-1]
    //     0xc692fc: ldurb           w17, [x0, #-1]
    //     0xc69300: and             x16, x17, x16, lsr #2
    //     0xc69304: tst             x16, HEAP, lsr #32
    //     0xc69308: b.eq            #0xc69310
    //     0xc6930c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc69310: r0 = markNeedsLayout()
    //     0xc69310: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc69314: r0 = Null
    //     0xc69314: mov             x0, NULL
    // 0xc69318: LeaveFrame
    //     0xc69318: mov             SP, fp
    //     0xc6931c: ldp             fp, lr, [SP], #0x10
    // 0xc69320: ret
    //     0xc69320: ret             
    // 0xc69324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc69324: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc69328: b               #0xc692e4
  }
  set _ crossAxisAlignment=(/* No info */) {
    // ** addr: 0xc6932c, size: 0x60
    // 0xc6932c: EnterFrame
    //     0xc6932c: stp             fp, lr, [SP, #-0x10]!
    //     0xc69330: mov             fp, SP
    // 0xc69334: mov             x0, x2
    // 0xc69338: CheckStackOverflow
    //     0xc69338: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6933c: cmp             SP, x16
    //     0xc69340: b.ls            #0xc69384
    // 0xc69344: LoadField: r2 = r1->field_77
    //     0xc69344: ldur            w2, [x1, #0x77]
    // 0xc69348: DecompressPointer r2
    //     0xc69348: add             x2, x2, HEAP, lsl #32
    // 0xc6934c: cmp             w2, w0
    // 0xc69350: b.eq            #0xc69374
    // 0xc69354: StoreField: r1->field_77 = r0
    //     0xc69354: stur            w0, [x1, #0x77]
    //     0xc69358: ldurb           w16, [x1, #-1]
    //     0xc6935c: ldurb           w17, [x0, #-1]
    //     0xc69360: and             x16, x17, x16, lsr #2
    //     0xc69364: tst             x16, HEAP, lsr #32
    //     0xc69368: b.eq            #0xc69370
    //     0xc6936c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc69370: r0 = markNeedsLayout()
    //     0xc69370: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc69374: r0 = Null
    //     0xc69374: mov             x0, NULL
    // 0xc69378: LeaveFrame
    //     0xc69378: mov             SP, fp
    //     0xc6937c: ldp             fp, lr, [SP], #0x10
    // 0xc69380: ret
    //     0xc69380: ret             
    // 0xc69384: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc69384: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc69388: b               #0xc69344
  }
  set _ mainAxisSize=(/* No info */) {
    // ** addr: 0xc6938c, size: 0x60
    // 0xc6938c: EnterFrame
    //     0xc6938c: stp             fp, lr, [SP, #-0x10]!
    //     0xc69390: mov             fp, SP
    // 0xc69394: mov             x0, x2
    // 0xc69398: CheckStackOverflow
    //     0xc69398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6939c: cmp             SP, x16
    //     0xc693a0: b.ls            #0xc693e4
    // 0xc693a4: LoadField: r2 = r1->field_73
    //     0xc693a4: ldur            w2, [x1, #0x73]
    // 0xc693a8: DecompressPointer r2
    //     0xc693a8: add             x2, x2, HEAP, lsl #32
    // 0xc693ac: cmp             w2, w0
    // 0xc693b0: b.eq            #0xc693d4
    // 0xc693b4: StoreField: r1->field_73 = r0
    //     0xc693b4: stur            w0, [x1, #0x73]
    //     0xc693b8: ldurb           w16, [x1, #-1]
    //     0xc693bc: ldurb           w17, [x0, #-1]
    //     0xc693c0: and             x16, x17, x16, lsr #2
    //     0xc693c4: tst             x16, HEAP, lsr #32
    //     0xc693c8: b.eq            #0xc693d0
    //     0xc693cc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc693d0: r0 = markNeedsLayout()
    //     0xc693d0: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc693d4: r0 = Null
    //     0xc693d4: mov             x0, NULL
    // 0xc693d8: LeaveFrame
    //     0xc693d8: mov             SP, fp
    //     0xc693dc: ldp             fp, lr, [SP], #0x10
    // 0xc693e0: ret
    //     0xc693e0: ret             
    // 0xc693e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc693e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc693e8: b               #0xc693a4
  }
  set _ mainAxisAlignment=(/* No info */) {
    // ** addr: 0xc693ec, size: 0x60
    // 0xc693ec: EnterFrame
    //     0xc693ec: stp             fp, lr, [SP, #-0x10]!
    //     0xc693f0: mov             fp, SP
    // 0xc693f4: mov             x0, x2
    // 0xc693f8: CheckStackOverflow
    //     0xc693f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc693fc: cmp             SP, x16
    //     0xc69400: b.ls            #0xc69444
    // 0xc69404: LoadField: r2 = r1->field_6f
    //     0xc69404: ldur            w2, [x1, #0x6f]
    // 0xc69408: DecompressPointer r2
    //     0xc69408: add             x2, x2, HEAP, lsl #32
    // 0xc6940c: cmp             w2, w0
    // 0xc69410: b.eq            #0xc69434
    // 0xc69414: StoreField: r1->field_6f = r0
    //     0xc69414: stur            w0, [x1, #0x6f]
    //     0xc69418: ldurb           w16, [x1, #-1]
    //     0xc6941c: ldurb           w17, [x0, #-1]
    //     0xc69420: and             x16, x17, x16, lsr #2
    //     0xc69424: tst             x16, HEAP, lsr #32
    //     0xc69428: b.eq            #0xc69430
    //     0xc6942c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc69430: r0 = markNeedsLayout()
    //     0xc69430: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc69434: r0 = Null
    //     0xc69434: mov             x0, NULL
    // 0xc69438: LeaveFrame
    //     0xc69438: mov             SP, fp
    //     0xc6943c: ldp             fp, lr, [SP], #0x10
    // 0xc69440: ret
    //     0xc69440: ret             
    // 0xc69444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc69444: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc69448: b               #0xc69404
  }
  set _ direction=(/* No info */) {
    // ** addr: 0xc6944c, size: 0x60
    // 0xc6944c: EnterFrame
    //     0xc6944c: stp             fp, lr, [SP, #-0x10]!
    //     0xc69450: mov             fp, SP
    // 0xc69454: mov             x0, x2
    // 0xc69458: CheckStackOverflow
    //     0xc69458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6945c: cmp             SP, x16
    //     0xc69460: b.ls            #0xc694a4
    // 0xc69464: LoadField: r2 = r1->field_6b
    //     0xc69464: ldur            w2, [x1, #0x6b]
    // 0xc69468: DecompressPointer r2
    //     0xc69468: add             x2, x2, HEAP, lsl #32
    // 0xc6946c: cmp             w2, w0
    // 0xc69470: b.eq            #0xc69494
    // 0xc69474: StoreField: r1->field_6b = r0
    //     0xc69474: stur            w0, [x1, #0x6b]
    //     0xc69478: ldurb           w16, [x1, #-1]
    //     0xc6947c: ldurb           w17, [x0, #-1]
    //     0xc69480: and             x16, x17, x16, lsr #2
    //     0xc69484: tst             x16, HEAP, lsr #32
    //     0xc69488: b.eq            #0xc69490
    //     0xc6948c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc69490: r0 = markNeedsLayout()
    //     0xc69490: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc69494: r0 = Null
    //     0xc69494: mov             x0, NULL
    // 0xc69498: LeaveFrame
    //     0xc69498: mov             SP, fp
    //     0xc6949c: ldp             fp, lr, [SP], #0x10
    // 0xc694a0: ret
    //     0xc694a0: ret             
    // 0xc694a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc694a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc694a8: b               #0xc69464
  }
}

// class id: 3159, size: 0x1c, field offset: 0x8
class _LayoutSizes extends Object {
}

// class id: 3198, size: 0x20, field offset: 0x18
class FlexParentData extends ContainerBoxParentData<dynamic> {
}

// class id: 7014, size: 0x14, field offset: 0x14
enum CrossAxisAlignment extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _getChildCrossAxisOffset(/* No info */) {
    // ** addr: 0x742eac, size: 0x160
    // 0x742eac: EnterFrame
    //     0x742eac: stp             fp, lr, [SP, #-0x10]!
    //     0x742eb0: mov             fp, SP
    // 0x742eb4: CheckStackOverflow
    //     0x742eb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x742eb8: cmp             SP, x16
    //     0x742ebc: b.ls            #0x742fd4
    // 0x742ec0: r16 = Instance_CrossAxisAlignment
    //     0x742ec0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0x742ec4: ldr             x16, [x16, #0xf50]
    // 0x742ec8: cmp             w1, w16
    // 0x742ecc: b.eq            #0x742ee0
    // 0x742ed0: r16 = Instance_CrossAxisAlignment
    //     0x742ed0: add             x16, PP, #0x45, lsl #12  ; [pp+0x456b8] Obj!CrossAxisAlignment@e359a1
    //     0x742ed4: ldr             x16, [x16, #0x6b8]
    // 0x742ed8: cmp             w1, w16
    // 0x742edc: b.ne            #0x742ee8
    // 0x742ee0: r0 = 0.000000
    //     0x742ee0: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x742ee4: b               #0x742fc4
    // 0x742ee8: r16 = Instance_CrossAxisAlignment
    //     0x742ee8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0x742eec: ldr             x16, [x16, #0x68]
    // 0x742ef0: cmp             w1, w16
    // 0x742ef4: b.ne            #0x742f2c
    // 0x742ef8: tbz             w2, #4, #0x742f00
    // 0x742efc: d0 = 0.000000
    //     0x742efc: eor             v0.16b, v0.16b, v0.16b
    // 0x742f00: r0 = inline_Allocate_Double()
    //     0x742f00: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x742f04: add             x0, x0, #0x10
    //     0x742f08: cmp             x1, x0
    //     0x742f0c: b.ls            #0x742fdc
    //     0x742f10: str             x0, [THR, #0x50]  ; THR::top
    //     0x742f14: sub             x0, x0, #0xf
    //     0x742f18: movz            x1, #0xe15c
    //     0x742f1c: movk            x1, #0x3, lsl #16
    //     0x742f20: stur            x1, [x0, #-1]
    // 0x742f24: StoreField: r0->field_7 = d0
    //     0x742f24: stur            d0, [x0, #7]
    // 0x742f28: b               #0x742fc4
    // 0x742f2c: r16 = Instance_CrossAxisAlignment
    //     0x742f2c: add             x16, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0x742f30: ldr             x16, [x16, #0x740]
    // 0x742f34: cmp             w1, w16
    // 0x742f38: b.ne            #0x742f70
    // 0x742f3c: d1 = 2.000000
    //     0x742f3c: fmov            d1, #2.00000000
    // 0x742f40: fdiv            d2, d0, d1
    // 0x742f44: r0 = inline_Allocate_Double()
    //     0x742f44: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x742f48: add             x0, x0, #0x10
    //     0x742f4c: cmp             x1, x0
    //     0x742f50: b.ls            #0x742fec
    //     0x742f54: str             x0, [THR, #0x50]  ; THR::top
    //     0x742f58: sub             x0, x0, #0xf
    //     0x742f5c: movz            x1, #0xe15c
    //     0x742f60: movk            x1, #0x3, lsl #16
    //     0x742f64: stur            x1, [x0, #-1]
    // 0x742f68: StoreField: r0->field_7 = d2
    //     0x742f68: stur            d2, [x0, #7]
    // 0x742f6c: b               #0x742fc4
    // 0x742f70: r16 = Instance_CrossAxisAlignment
    //     0x742f70: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fa8] Obj!CrossAxisAlignment@e359c1
    //     0x742f74: ldr             x16, [x16, #0xfa8]
    // 0x742f78: cmp             w1, w16
    // 0x742f7c: b.ne            #0x742fc0
    // 0x742f80: eor             x0, x2, #0x10
    // 0x742f84: mov             x2, x0
    // 0x742f88: r1 = Instance_CrossAxisAlignment
    //     0x742f88: add             x1, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0x742f8c: ldr             x1, [x1, #0x68]
    // 0x742f90: r0 = _getChildCrossAxisOffset()
    //     0x742f90: bl              #0x742eac  ; [package:flutter/src/rendering/flex.dart] CrossAxisAlignment::_getChildCrossAxisOffset
    // 0x742f94: r0 = inline_Allocate_Double()
    //     0x742f94: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x742f98: add             x0, x0, #0x10
    //     0x742f9c: cmp             x1, x0
    //     0x742fa0: b.ls            #0x742ffc
    //     0x742fa4: str             x0, [THR, #0x50]  ; THR::top
    //     0x742fa8: sub             x0, x0, #0xf
    //     0x742fac: movz            x1, #0xe15c
    //     0x742fb0: movk            x1, #0x3, lsl #16
    //     0x742fb4: stur            x1, [x0, #-1]
    // 0x742fb8: StoreField: r0->field_7 = d0
    //     0x742fb8: stur            d0, [x0, #7]
    // 0x742fbc: b               #0x742fc4
    // 0x742fc0: r0 = Null
    //     0x742fc0: mov             x0, NULL
    // 0x742fc4: LoadField: d0 = r0->field_7
    //     0x742fc4: ldur            d0, [x0, #7]
    // 0x742fc8: LeaveFrame
    //     0x742fc8: mov             SP, fp
    //     0x742fcc: ldp             fp, lr, [SP], #0x10
    // 0x742fd0: ret
    //     0x742fd0: ret             
    // 0x742fd4: r0 = StackOverflowSharedWithFPURegs()
    //     0x742fd4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x742fd8: b               #0x742ec0
    // 0x742fdc: SaveReg d0
    //     0x742fdc: str             q0, [SP, #-0x10]!
    // 0x742fe0: r0 = AllocateDouble()
    //     0x742fe0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742fe4: RestoreReg d0
    //     0x742fe4: ldr             q0, [SP], #0x10
    // 0x742fe8: b               #0x742f24
    // 0x742fec: SaveReg d2
    //     0x742fec: str             q2, [SP, #-0x10]!
    // 0x742ff0: r0 = AllocateDouble()
    //     0x742ff0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742ff4: RestoreReg d2
    //     0x742ff4: ldr             q2, [SP], #0x10
    // 0x742ff8: b               #0x742f68
    // 0x742ffc: SaveReg d0
    //     0x742ffc: str             q0, [SP, #-0x10]!
    // 0x743000: r0 = AllocateDouble()
    //     0x743000: bl              #0xec2254  ; AllocateDoubleStub
    // 0x743004: RestoreReg d0
    //     0x743004: ldr             q0, [SP], #0x10
    // 0x743008: b               #0x742fb8
  }
}

// class id: 7015, size: 0x14, field offset: 0x14
enum MainAxisAlignment extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _distributeSpace(/* No info */) {
    // ** addr: 0x742ab0, size: 0x384
    // 0x742ab0: EnterFrame
    //     0x742ab0: stp             fp, lr, [SP, #-0x10]!
    //     0x742ab4: mov             fp, SP
    // 0x742ab8: CheckStackOverflow
    //     0x742ab8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x742abc: cmp             SP, x16
    //     0x742ac0: b.ls            #0x742d90
    // 0x742ac4: r16 = Instance_MainAxisAlignment
    //     0x742ac4: add             x16, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0x742ac8: ldr             x16, [x16, #0x730]
    // 0x742acc: cmp             w1, w16
    // 0x742ad0: b.ne            #0x742b20
    // 0x742ad4: tbnz            w3, #4, #0x742b0c
    // 0x742ad8: r2 = inline_Allocate_Double()
    //     0x742ad8: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x742adc: add             x2, x2, #0x10
    //     0x742ae0: cmp             x0, x2
    //     0x742ae4: b.ls            #0x742d98
    //     0x742ae8: str             x2, [THR, #0x50]  ; THR::top
    //     0x742aec: sub             x2, x2, #0xf
    //     0x742af0: movz            x0, #0xe15c
    //     0x742af4: movk            x0, #0x3, lsl #16
    //     0x742af8: stur            x0, [x2, #-1]
    // 0x742afc: StoreField: r2->field_7 = d0
    //     0x742afc: stur            d0, [x2, #7]
    // 0x742b00: r3 = 0.000000
    //     0x742b00: ldr             x3, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x742b04: r0 = AllocateRecord2()
    //     0x742b04: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x742b08: b               #0x742b18
    // 0x742b0c: r2 = 0.000000
    //     0x742b0c: ldr             x2, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x742b10: r3 = 0.000000
    //     0x742b10: ldr             x3, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x742b14: r0 = AllocateRecord2()
    //     0x742b14: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x742b18: mov             x2, x0
    // 0x742b1c: b               #0x742d74
    // 0x742b20: r16 = Instance_MainAxisAlignment
    //     0x742b20: add             x16, PP, #0x38, lsl #12  ; [pp+0x38330] Obj!MainAxisAlignment@e35ac1
    //     0x742b24: ldr             x16, [x16, #0x330]
    // 0x742b28: cmp             w1, w16
    // 0x742b2c: b.ne            #0x742b58
    // 0x742b30: eor             x0, x3, #0x10
    // 0x742b34: mov             x3, x0
    // 0x742b38: r1 = Instance_MainAxisAlignment
    //     0x742b38: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0x742b3c: ldr             x1, [x1, #0x730]
    // 0x742b40: r0 = _distributeSpace()
    //     0x742b40: bl              #0x742ab0  ; [package:flutter/src/rendering/flex.dart] MainAxisAlignment::_distributeSpace
    // 0x742b44: mov             x2, x0
    // 0x742b48: mov             x3, x1
    // 0x742b4c: r0 = AllocateRecord2()
    //     0x742b4c: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x742b50: mov             x2, x0
    // 0x742b54: b               #0x742d74
    // 0x742b58: r16 = Instance_MainAxisAlignment
    //     0x742b58: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ae8] Obj!MainAxisAlignment@e35aa1
    //     0x742b5c: ldr             x16, [x16, #0xae8]
    // 0x742b60: cmp             w1, w16
    // 0x742b64: r16 = true
    //     0x742b64: add             x16, NULL, #0x20  ; true
    // 0x742b68: r17 = false
    //     0x742b68: add             x17, NULL, #0x30  ; false
    // 0x742b6c: csel            x0, x16, x17, eq
    // 0x742b70: tbnz            w0, #4, #0x742b9c
    // 0x742b74: cmp             x2, #2
    // 0x742b78: b.ge            #0x742b9c
    // 0x742b7c: r1 = Instance_MainAxisAlignment
    //     0x742b7c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0x742b80: ldr             x1, [x1, #0x730]
    // 0x742b84: r0 = _distributeSpace()
    //     0x742b84: bl              #0x742ab0  ; [package:flutter/src/rendering/flex.dart] MainAxisAlignment::_distributeSpace
    // 0x742b88: mov             x2, x0
    // 0x742b8c: mov             x3, x1
    // 0x742b90: r0 = AllocateRecord2()
    //     0x742b90: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x742b94: mov             x2, x0
    // 0x742b98: b               #0x742d74
    // 0x742b9c: r16 = Instance_MainAxisAlignment
    //     0x742b9c: add             x16, PP, #0x45, lsl #12  ; [pp+0x456e8] Obj!MainAxisAlignment@e35a41
    //     0x742ba0: ldr             x16, [x16, #0x6e8]
    // 0x742ba4: cmp             w1, w16
    // 0x742ba8: r16 = true
    //     0x742ba8: add             x16, NULL, #0x20  ; true
    // 0x742bac: r17 = false
    //     0x742bac: add             x17, NULL, #0x30  ; false
    // 0x742bb0: csel            x4, x16, x17, eq
    // 0x742bb4: tbnz            w4, #4, #0x742bdc
    // 0x742bb8: cbnz            x2, #0x742bdc
    // 0x742bbc: r1 = Instance_MainAxisAlignment
    //     0x742bbc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0x742bc0: ldr             x1, [x1, #0x730]
    // 0x742bc4: r0 = _distributeSpace()
    //     0x742bc4: bl              #0x742ab0  ; [package:flutter/src/rendering/flex.dart] MainAxisAlignment::_distributeSpace
    // 0x742bc8: mov             x2, x0
    // 0x742bcc: mov             x3, x1
    // 0x742bd0: r0 = AllocateRecord2()
    //     0x742bd0: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x742bd4: mov             x2, x0
    // 0x742bd8: b               #0x742d74
    // 0x742bdc: r16 = Instance_MainAxisAlignment
    //     0x742bdc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c290] Obj!MainAxisAlignment@e35a81
    //     0x742be0: ldr             x16, [x16, #0x290]
    // 0x742be4: cmp             w1, w16
    // 0x742be8: b.ne            #0x742c2c
    // 0x742bec: d1 = 2.000000
    //     0x742bec: fmov            d1, #2.00000000
    // 0x742bf0: fdiv            d2, d0, d1
    // 0x742bf4: r2 = inline_Allocate_Double()
    //     0x742bf4: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x742bf8: add             x2, x2, #0x10
    //     0x742bfc: cmp             x0, x2
    //     0x742c00: b.ls            #0x742dac
    //     0x742c04: str             x2, [THR, #0x50]  ; THR::top
    //     0x742c08: sub             x2, x2, #0xf
    //     0x742c0c: movz            x0, #0xe15c
    //     0x742c10: movk            x0, #0x3, lsl #16
    //     0x742c14: stur            x0, [x2, #-1]
    // 0x742c18: StoreField: r2->field_7 = d2
    //     0x742c18: stur            d2, [x2, #7]
    // 0x742c1c: r3 = 0.000000
    //     0x742c1c: ldr             x3, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x742c20: r0 = AllocateRecord2()
    //     0x742c20: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x742c24: mov             x2, x0
    // 0x742c28: b               #0x742d74
    // 0x742c2c: d1 = 2.000000
    //     0x742c2c: fmov            d1, #2.00000000
    // 0x742c30: tbnz            w0, #4, #0x742c80
    // 0x742c34: d2 = 0.000000
    //     0x742c34: eor             v2.16b, v2.16b, v2.16b
    // 0x742c38: sub             x0, x2, #1
    // 0x742c3c: scvtf           d1, x0
    // 0x742c40: fdiv            d3, d0, d1
    // 0x742c44: fadd            d0, d3, d2
    // 0x742c48: r3 = inline_Allocate_Double()
    //     0x742c48: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x742c4c: add             x3, x3, #0x10
    //     0x742c50: cmp             x0, x3
    //     0x742c54: b.ls            #0x742dc0
    //     0x742c58: str             x3, [THR, #0x50]  ; THR::top
    //     0x742c5c: sub             x3, x3, #0xf
    //     0x742c60: movz            x0, #0xe15c
    //     0x742c64: movk            x0, #0x3, lsl #16
    //     0x742c68: stur            x0, [x3, #-1]
    // 0x742c6c: StoreField: r3->field_7 = d0
    //     0x742c6c: stur            d0, [x3, #7]
    // 0x742c70: r2 = 0.000000
    //     0x742c70: ldr             x2, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x742c74: r0 = AllocateRecord2()
    //     0x742c74: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x742c78: mov             x2, x0
    // 0x742c7c: b               #0x742d74
    // 0x742c80: d2 = 0.000000
    //     0x742c80: eor             v2.16b, v2.16b, v2.16b
    // 0x742c84: tbnz            w4, #4, #0x742cf4
    // 0x742c88: scvtf           d3, x2
    // 0x742c8c: fdiv            d4, d0, d3
    // 0x742c90: fdiv            d0, d4, d1
    // 0x742c94: fadd            d1, d4, d2
    // 0x742c98: r2 = inline_Allocate_Double()
    //     0x742c98: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x742c9c: add             x2, x2, #0x10
    //     0x742ca0: cmp             x0, x2
    //     0x742ca4: b.ls            #0x742dd4
    //     0x742ca8: str             x2, [THR, #0x50]  ; THR::top
    //     0x742cac: sub             x2, x2, #0xf
    //     0x742cb0: movz            x0, #0xe15c
    //     0x742cb4: movk            x0, #0x3, lsl #16
    //     0x742cb8: stur            x0, [x2, #-1]
    // 0x742cbc: StoreField: r2->field_7 = d0
    //     0x742cbc: stur            d0, [x2, #7]
    // 0x742cc0: r3 = inline_Allocate_Double()
    //     0x742cc0: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x742cc4: add             x3, x3, #0x10
    //     0x742cc8: cmp             x0, x3
    //     0x742ccc: b.ls            #0x742de8
    //     0x742cd0: str             x3, [THR, #0x50]  ; THR::top
    //     0x742cd4: sub             x3, x3, #0xf
    //     0x742cd8: movz            x0, #0xe15c
    //     0x742cdc: movk            x0, #0x3, lsl #16
    //     0x742ce0: stur            x0, [x3, #-1]
    // 0x742ce4: StoreField: r3->field_7 = d1
    //     0x742ce4: stur            d1, [x3, #7]
    // 0x742ce8: r0 = AllocateRecord2()
    //     0x742ce8: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x742cec: mov             x2, x0
    // 0x742cf0: b               #0x742d74
    // 0x742cf4: r16 = Instance_MainAxisAlignment
    //     0x742cf4: add             x16, PP, #0x28, lsl #12  ; [pp+0x28558] Obj!MainAxisAlignment@e35a61
    //     0x742cf8: ldr             x16, [x16, #0x558]
    // 0x742cfc: cmp             w1, w16
    // 0x742d00: b.ne            #0x742d70
    // 0x742d04: add             x0, x2, #1
    // 0x742d08: scvtf           d1, x0
    // 0x742d0c: fdiv            d3, d0, d1
    // 0x742d10: fadd            d0, d3, d2
    // 0x742d14: r2 = inline_Allocate_Double()
    //     0x742d14: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x742d18: add             x2, x2, #0x10
    //     0x742d1c: cmp             x0, x2
    //     0x742d20: b.ls            #0x742e04
    //     0x742d24: str             x2, [THR, #0x50]  ; THR::top
    //     0x742d28: sub             x2, x2, #0xf
    //     0x742d2c: movz            x0, #0xe15c
    //     0x742d30: movk            x0, #0x3, lsl #16
    //     0x742d34: stur            x0, [x2, #-1]
    // 0x742d38: StoreField: r2->field_7 = d3
    //     0x742d38: stur            d3, [x2, #7]
    // 0x742d3c: r3 = inline_Allocate_Double()
    //     0x742d3c: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x742d40: add             x3, x3, #0x10
    //     0x742d44: cmp             x0, x3
    //     0x742d48: b.ls            #0x742e18
    //     0x742d4c: str             x3, [THR, #0x50]  ; THR::top
    //     0x742d50: sub             x3, x3, #0xf
    //     0x742d54: movz            x0, #0xe15c
    //     0x742d58: movk            x0, #0x3, lsl #16
    //     0x742d5c: stur            x0, [x3, #-1]
    // 0x742d60: StoreField: r3->field_7 = d0
    //     0x742d60: stur            d0, [x3, #7]
    // 0x742d64: r0 = AllocateRecord2()
    //     0x742d64: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x742d68: mov             x2, x0
    // 0x742d6c: b               #0x742d74
    // 0x742d70: r2 = Null
    //     0x742d70: mov             x2, NULL
    // 0x742d74: LoadField: r0 = r2->field_f
    //     0x742d74: ldur            w0, [x2, #0xf]
    // 0x742d78: DecompressPointer r0
    //     0x742d78: add             x0, x0, HEAP, lsl #32
    // 0x742d7c: LoadField: r1 = r2->field_13
    //     0x742d7c: ldur            w1, [x2, #0x13]
    // 0x742d80: DecompressPointer r1
    //     0x742d80: add             x1, x1, HEAP, lsl #32
    // 0x742d84: LeaveFrame
    //     0x742d84: mov             SP, fp
    //     0x742d88: ldp             fp, lr, [SP], #0x10
    // 0x742d8c: ret
    //     0x742d8c: ret             
    // 0x742d90: r0 = StackOverflowSharedWithFPURegs()
    //     0x742d90: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x742d94: b               #0x742ac4
    // 0x742d98: SaveReg d0
    //     0x742d98: str             q0, [SP, #-0x10]!
    // 0x742d9c: r0 = AllocateDouble()
    //     0x742d9c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742da0: mov             x2, x0
    // 0x742da4: RestoreReg d0
    //     0x742da4: ldr             q0, [SP], #0x10
    // 0x742da8: b               #0x742afc
    // 0x742dac: SaveReg d2
    //     0x742dac: str             q2, [SP, #-0x10]!
    // 0x742db0: r0 = AllocateDouble()
    //     0x742db0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742db4: mov             x2, x0
    // 0x742db8: RestoreReg d2
    //     0x742db8: ldr             q2, [SP], #0x10
    // 0x742dbc: b               #0x742c18
    // 0x742dc0: SaveReg d0
    //     0x742dc0: str             q0, [SP, #-0x10]!
    // 0x742dc4: r0 = AllocateDouble()
    //     0x742dc4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742dc8: mov             x3, x0
    // 0x742dcc: RestoreReg d0
    //     0x742dcc: ldr             q0, [SP], #0x10
    // 0x742dd0: b               #0x742c6c
    // 0x742dd4: stp             q0, q1, [SP, #-0x20]!
    // 0x742dd8: r0 = AllocateDouble()
    //     0x742dd8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742ddc: mov             x2, x0
    // 0x742de0: ldp             q0, q1, [SP], #0x20
    // 0x742de4: b               #0x742cbc
    // 0x742de8: SaveReg d1
    //     0x742de8: str             q1, [SP, #-0x10]!
    // 0x742dec: SaveReg r2
    //     0x742dec: str             x2, [SP, #-8]!
    // 0x742df0: r0 = AllocateDouble()
    //     0x742df0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742df4: mov             x3, x0
    // 0x742df8: RestoreReg r2
    //     0x742df8: ldr             x2, [SP], #8
    // 0x742dfc: RestoreReg d1
    //     0x742dfc: ldr             q1, [SP], #0x10
    // 0x742e00: b               #0x742ce4
    // 0x742e04: stp             q0, q3, [SP, #-0x20]!
    // 0x742e08: r0 = AllocateDouble()
    //     0x742e08: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742e0c: mov             x2, x0
    // 0x742e10: ldp             q0, q3, [SP], #0x20
    // 0x742e14: b               #0x742d38
    // 0x742e18: SaveReg d0
    //     0x742e18: str             q0, [SP, #-0x10]!
    // 0x742e1c: SaveReg r2
    //     0x742e1c: str             x2, [SP, #-8]!
    // 0x742e20: r0 = AllocateDouble()
    //     0x742e20: bl              #0xec2254  ; AllocateDoubleStub
    // 0x742e24: mov             x3, x0
    // 0x742e28: RestoreReg r2
    //     0x742e28: ldr             x2, [SP], #8
    // 0x742e2c: RestoreReg d0
    //     0x742e2c: ldr             q0, [SP], #0x10
    // 0x742e30: b               #0x742d60
  }
}

// class id: 7016, size: 0x14, field offset: 0x14
enum MainAxisSize extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 7017, size: 0x14, field offset: 0x14
enum FlexFit extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc496e8, size: 0x64
    // 0xc496e8: EnterFrame
    //     0xc496e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc496ec: mov             fp, SP
    // 0xc496f0: AllocStack(0x10)
    //     0xc496f0: sub             SP, SP, #0x10
    // 0xc496f4: SetupParameters(FlexFit this /* r1 => r0, fp-0x8 */)
    //     0xc496f4: mov             x0, x1
    //     0xc496f8: stur            x1, [fp, #-8]
    // 0xc496fc: CheckStackOverflow
    //     0xc496fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc49700: cmp             SP, x16
    //     0xc49704: b.ls            #0xc49744
    // 0xc49708: r1 = Null
    //     0xc49708: mov             x1, NULL
    // 0xc4970c: r2 = 4
    //     0xc4970c: movz            x2, #0x4
    // 0xc49710: r0 = AllocateArray()
    //     0xc49710: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc49714: r16 = "FlexFit."
    //     0xc49714: add             x16, PP, #0x39, lsl #12  ; [pp+0x39dd0] "FlexFit."
    //     0xc49718: ldr             x16, [x16, #0xdd0]
    // 0xc4971c: StoreField: r0->field_f = r16
    //     0xc4971c: stur            w16, [x0, #0xf]
    // 0xc49720: ldur            x1, [fp, #-8]
    // 0xc49724: LoadField: r2 = r1->field_f
    //     0xc49724: ldur            w2, [x1, #0xf]
    // 0xc49728: DecompressPointer r2
    //     0xc49728: add             x2, x2, HEAP, lsl #32
    // 0xc4972c: StoreField: r0->field_13 = r2
    //     0xc4972c: stur            w2, [x0, #0x13]
    // 0xc49730: str             x0, [SP]
    // 0xc49734: r0 = _interpolate()
    //     0xc49734: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc49738: LeaveFrame
    //     0xc49738: mov             SP, fp
    //     0xc4973c: ldp             fp, lr, [SP], #0x10
    // 0xc49740: ret
    //     0xc49740: ret             
    // 0xc49744: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc49744: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc49748: b               #0xc49708
  }
}
