// lib: , url: package:flutter/src/rendering/stack.dart

// class id: 1049047, size: 0x8
class :: {
}

// class id: 2863, size: 0x28, field offset: 0x8
//   const constructor, 
class RelativeRect extends Object {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbecea0, size: 0x184
    // 0xbecea0: EnterFrame
    //     0xbecea0: stp             fp, lr, [SP, #-0x10]!
    //     0xbecea4: mov             fp, SP
    // 0xbecea8: AllocStack(0x10)
    //     0xbecea8: sub             SP, SP, #0x10
    // 0xbeceac: CheckStackOverflow
    //     0xbeceac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeceb0: cmp             SP, x16
    //     0xbeceb4: b.ls            #0xbecfa0
    // 0xbeceb8: ldr             x0, [fp, #0x10]
    // 0xbecebc: LoadField: d0 = r0->field_7
    //     0xbecebc: ldur            d0, [x0, #7]
    // 0xbecec0: LoadField: d1 = r0->field_f
    //     0xbecec0: ldur            d1, [x0, #0xf]
    // 0xbecec4: ArrayLoad: d2 = r0[0]  ; List_8
    //     0xbecec4: ldur            d2, [x0, #0x17]
    // 0xbecec8: LoadField: d3 = r0->field_1f
    //     0xbecec8: ldur            d3, [x0, #0x1f]
    // 0xbececc: r1 = inline_Allocate_Double()
    //     0xbececc: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xbeced0: add             x1, x1, #0x10
    //     0xbeced4: cmp             x0, x1
    //     0xbeced8: b.ls            #0xbecfa8
    //     0xbecedc: str             x1, [THR, #0x50]  ; THR::top
    //     0xbecee0: sub             x1, x1, #0xf
    //     0xbecee4: movz            x0, #0xe15c
    //     0xbecee8: movk            x0, #0x3, lsl #16
    //     0xbeceec: stur            x0, [x1, #-1]
    // 0xbecef0: StoreField: r1->field_7 = d0
    //     0xbecef0: stur            d0, [x1, #7]
    // 0xbecef4: r2 = inline_Allocate_Double()
    //     0xbecef4: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xbecef8: add             x2, x2, #0x10
    //     0xbecefc: cmp             x0, x2
    //     0xbecf00: b.ls            #0xbecfc4
    //     0xbecf04: str             x2, [THR, #0x50]  ; THR::top
    //     0xbecf08: sub             x2, x2, #0xf
    //     0xbecf0c: movz            x0, #0xe15c
    //     0xbecf10: movk            x0, #0x3, lsl #16
    //     0xbecf14: stur            x0, [x2, #-1]
    // 0xbecf18: StoreField: r2->field_7 = d1
    //     0xbecf18: stur            d1, [x2, #7]
    // 0xbecf1c: r0 = inline_Allocate_Double()
    //     0xbecf1c: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xbecf20: add             x0, x0, #0x10
    //     0xbecf24: cmp             x3, x0
    //     0xbecf28: b.ls            #0xbecfe8
    //     0xbecf2c: str             x0, [THR, #0x50]  ; THR::top
    //     0xbecf30: sub             x0, x0, #0xf
    //     0xbecf34: movz            x3, #0xe15c
    //     0xbecf38: movk            x3, #0x3, lsl #16
    //     0xbecf3c: stur            x3, [x0, #-1]
    // 0xbecf40: StoreField: r0->field_7 = d2
    //     0xbecf40: stur            d2, [x0, #7]
    // 0xbecf44: r3 = inline_Allocate_Double()
    //     0xbecf44: ldp             x3, x4, [THR, #0x50]  ; THR::top
    //     0xbecf48: add             x3, x3, #0x10
    //     0xbecf4c: cmp             x4, x3
    //     0xbecf50: b.ls            #0xbed000
    //     0xbecf54: str             x3, [THR, #0x50]  ; THR::top
    //     0xbecf58: sub             x3, x3, #0xf
    //     0xbecf5c: movz            x4, #0xe15c
    //     0xbecf60: movk            x4, #0x3, lsl #16
    //     0xbecf64: stur            x4, [x3, #-1]
    // 0xbecf68: StoreField: r3->field_7 = d3
    //     0xbecf68: stur            d3, [x3, #7]
    // 0xbecf6c: stp             x3, x0, [SP]
    // 0xbecf70: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0xbecf70: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0xbecf74: ldr             x4, [x4, #0xe00]
    // 0xbecf78: r0 = hash()
    //     0xbecf78: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbecf7c: mov             x2, x0
    // 0xbecf80: r0 = BoxInt64Instr(r2)
    //     0xbecf80: sbfiz           x0, x2, #1, #0x1f
    //     0xbecf84: cmp             x2, x0, asr #1
    //     0xbecf88: b.eq            #0xbecf94
    //     0xbecf8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbecf90: stur            x2, [x0, #7]
    // 0xbecf94: LeaveFrame
    //     0xbecf94: mov             SP, fp
    //     0xbecf98: ldp             fp, lr, [SP], #0x10
    // 0xbecf9c: ret
    //     0xbecf9c: ret             
    // 0xbecfa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbecfa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbecfa4: b               #0xbeceb8
    // 0xbecfa8: stp             q2, q3, [SP, #-0x20]!
    // 0xbecfac: stp             q0, q1, [SP, #-0x20]!
    // 0xbecfb0: r0 = AllocateDouble()
    //     0xbecfb0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbecfb4: mov             x1, x0
    // 0xbecfb8: ldp             q0, q1, [SP], #0x20
    // 0xbecfbc: ldp             q2, q3, [SP], #0x20
    // 0xbecfc0: b               #0xbecef0
    // 0xbecfc4: stp             q2, q3, [SP, #-0x20]!
    // 0xbecfc8: SaveReg d1
    //     0xbecfc8: str             q1, [SP, #-0x10]!
    // 0xbecfcc: SaveReg r1
    //     0xbecfcc: str             x1, [SP, #-8]!
    // 0xbecfd0: r0 = AllocateDouble()
    //     0xbecfd0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbecfd4: mov             x2, x0
    // 0xbecfd8: RestoreReg r1
    //     0xbecfd8: ldr             x1, [SP], #8
    // 0xbecfdc: RestoreReg d1
    //     0xbecfdc: ldr             q1, [SP], #0x10
    // 0xbecfe0: ldp             q2, q3, [SP], #0x20
    // 0xbecfe4: b               #0xbecf18
    // 0xbecfe8: stp             q2, q3, [SP, #-0x20]!
    // 0xbecfec: stp             x1, x2, [SP, #-0x10]!
    // 0xbecff0: r0 = AllocateDouble()
    //     0xbecff0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbecff4: ldp             x1, x2, [SP], #0x10
    // 0xbecff8: ldp             q2, q3, [SP], #0x20
    // 0xbecffc: b               #0xbecf40
    // 0xbed000: SaveReg d3
    //     0xbed000: str             q3, [SP, #-0x10]!
    // 0xbed004: stp             x1, x2, [SP, #-0x10]!
    // 0xbed008: SaveReg r0
    //     0xbed008: str             x0, [SP, #-8]!
    // 0xbed00c: r0 = AllocateDouble()
    //     0xbed00c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbed010: mov             x3, x0
    // 0xbed014: RestoreReg r0
    //     0xbed014: ldr             x0, [SP], #8
    // 0xbed018: ldp             x1, x2, [SP], #0x10
    // 0xbed01c: RestoreReg d3
    //     0xbed01c: ldr             q3, [SP], #0x10
    // 0xbed020: b               #0xbecf68
  }
  _ ==(/* No info */) {
    // ** addr: 0xd64620, size: 0x98
    // 0xd64620: ldr             x1, [SP]
    // 0xd64624: cmp             w1, NULL
    // 0xd64628: b.ne            #0xd64634
    // 0xd6462c: r0 = false
    //     0xd6462c: add             x0, NULL, #0x30  ; false
    // 0xd64630: ret
    //     0xd64630: ret             
    // 0xd64634: ldr             x2, [SP, #8]
    // 0xd64638: cmp             w2, w1
    // 0xd6463c: b.ne            #0xd64648
    // 0xd64640: r0 = true
    //     0xd64640: add             x0, NULL, #0x20  ; true
    // 0xd64644: ret
    //     0xd64644: ret             
    // 0xd64648: r3 = 60
    //     0xd64648: movz            x3, #0x3c
    // 0xd6464c: branchIfSmi(r1, 0xd64658)
    //     0xd6464c: tbz             w1, #0, #0xd64658
    // 0xd64650: r3 = LoadClassIdInstr(r1)
    //     0xd64650: ldur            x3, [x1, #-1]
    //     0xd64654: ubfx            x3, x3, #0xc, #0x14
    // 0xd64658: cmp             x3, #0xb2f
    // 0xd6465c: b.ne            #0xd646b0
    // 0xd64660: LoadField: d0 = r1->field_7
    //     0xd64660: ldur            d0, [x1, #7]
    // 0xd64664: LoadField: d1 = r2->field_7
    //     0xd64664: ldur            d1, [x2, #7]
    // 0xd64668: fcmp            d0, d1
    // 0xd6466c: b.ne            #0xd646b0
    // 0xd64670: LoadField: d0 = r1->field_f
    //     0xd64670: ldur            d0, [x1, #0xf]
    // 0xd64674: LoadField: d1 = r2->field_f
    //     0xd64674: ldur            d1, [x2, #0xf]
    // 0xd64678: fcmp            d0, d1
    // 0xd6467c: b.ne            #0xd646b0
    // 0xd64680: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xd64680: ldur            d0, [x1, #0x17]
    // 0xd64684: ArrayLoad: d1 = r2[0]  ; List_8
    //     0xd64684: ldur            d1, [x2, #0x17]
    // 0xd64688: fcmp            d0, d1
    // 0xd6468c: b.ne            #0xd646b0
    // 0xd64690: LoadField: d0 = r1->field_1f
    //     0xd64690: ldur            d0, [x1, #0x1f]
    // 0xd64694: LoadField: d1 = r2->field_1f
    //     0xd64694: ldur            d1, [x2, #0x1f]
    // 0xd64698: fcmp            d0, d1
    // 0xd6469c: r16 = true
    //     0xd6469c: add             x16, NULL, #0x20  ; true
    // 0xd646a0: r17 = false
    //     0xd646a0: add             x17, NULL, #0x30  ; false
    // 0xd646a4: csel            x1, x16, x17, eq
    // 0xd646a8: mov             x0, x1
    // 0xd646ac: b               #0xd646b4
    // 0xd646b0: r0 = false
    //     0xd646b0: add             x0, NULL, #0x30  ; false
    // 0xd646b4: ret
    //     0xd646b4: ret             
  }
  _ RelativeRect.fromSize(/* No info */) {
    // ** addr: 0xdb6c54, size: 0x38
    // 0xdb6c54: LoadField: d0 = r2->field_7
    //     0xdb6c54: ldur            d0, [x2, #7]
    // 0xdb6c58: StoreField: r1->field_7 = d0
    //     0xdb6c58: stur            d0, [x1, #7]
    // 0xdb6c5c: LoadField: d0 = r2->field_f
    //     0xdb6c5c: ldur            d0, [x2, #0xf]
    // 0xdb6c60: StoreField: r1->field_f = d0
    //     0xdb6c60: stur            d0, [x1, #0xf]
    // 0xdb6c64: LoadField: d0 = r3->field_7
    //     0xdb6c64: ldur            d0, [x3, #7]
    // 0xdb6c68: ArrayLoad: d1 = r2[0]  ; List_8
    //     0xdb6c68: ldur            d1, [x2, #0x17]
    // 0xdb6c6c: fsub            d2, d0, d1
    // 0xdb6c70: ArrayStore: r1[0] = d2  ; List_8
    //     0xdb6c70: stur            d2, [x1, #0x17]
    // 0xdb6c74: LoadField: d0 = r3->field_f
    //     0xdb6c74: ldur            d0, [x3, #0xf]
    // 0xdb6c78: LoadField: d1 = r2->field_1f
    //     0xdb6c78: ldur            d1, [x2, #0x1f]
    // 0xdb6c7c: fsub            d2, d0, d1
    // 0xdb6c80: StoreField: r1->field_1f = d2
    //     0xdb6c80: stur            d2, [x1, #0x1f]
    // 0xdb6c84: r0 = Null
    //     0xdb6c84: mov             x0, NULL
    // 0xdb6c88: ret
    //     0xdb6c88: ret             
  }
}

// class id: 3029, size: 0x68, field offset: 0x68
//   transformed mixin,
abstract class _RenderStack&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin extends __RenderTheater&RenderBox&ContainerRenderObjectMixin
     with RenderBoxContainerDefaultsMixin<X0 bound RenderBox, X1 bound ContainerBoxParentData> {

  _ defaultComputeDistanceToHighestActualBaseline(/* No info */) {
    // ** addr: 0x74ca28, size: 0x2f4
    // 0x74ca28: EnterFrame
    //     0x74ca28: stp             fp, lr, [SP, #-0x10]!
    //     0x74ca2c: mov             fp, SP
    // 0x74ca30: AllocStack(0x58)
    //     0x74ca30: sub             SP, SP, #0x58
    // 0x74ca34: SetupParameters(dynamic _ /* r2 => r3, fp-0x20 */)
    //     0x74ca34: mov             x3, x2
    //     0x74ca38: stur            x2, [fp, #-0x20]
    // 0x74ca3c: CheckStackOverflow
    //     0x74ca3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74ca40: cmp             SP, x16
    //     0x74ca44: b.ls            #0x74ccd0
    // 0x74ca48: LoadField: r0 = r1->field_5f
    //     0x74ca48: ldur            w0, [x1, #0x5f]
    // 0x74ca4c: DecompressPointer r0
    //     0x74ca4c: add             x0, x0, HEAP, lsl #32
    // 0x74ca50: mov             x4, x0
    // 0x74ca54: r5 = Null
    //     0x74ca54: mov             x5, NULL
    // 0x74ca58: stur            x5, [fp, #-0x10]
    // 0x74ca5c: stur            x4, [fp, #-0x18]
    // 0x74ca60: CheckStackOverflow
    //     0x74ca60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74ca64: cmp             SP, x16
    //     0x74ca68: b.ls            #0x74ccd8
    // 0x74ca6c: cmp             w4, NULL
    // 0x74ca70: b.eq            #0x74cca0
    // 0x74ca74: LoadField: r6 = r4->field_7
    //     0x74ca74: ldur            w6, [x4, #7]
    // 0x74ca78: DecompressPointer r6
    //     0x74ca78: add             x6, x6, HEAP, lsl #32
    // 0x74ca7c: stur            x6, [fp, #-8]
    // 0x74ca80: cmp             w6, NULL
    // 0x74ca84: b.eq            #0x74cce0
    // 0x74ca88: mov             x0, x6
    // 0x74ca8c: r2 = Null
    //     0x74ca8c: mov             x2, NULL
    // 0x74ca90: r1 = Null
    //     0x74ca90: mov             x1, NULL
    // 0x74ca94: r4 = LoadClassIdInstr(r0)
    //     0x74ca94: ldur            x4, [x0, #-1]
    //     0x74ca98: ubfx            x4, x4, #0xc, #0x14
    // 0x74ca9c: sub             x4, x4, #0xc7a
    // 0x74caa0: cmp             x4, #2
    // 0x74caa4: b.ls            #0x74cabc
    // 0x74caa8: r8 = StackParentData
    //     0x74caa8: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0x74caac: ldr             x8, [x8, #0x5c0]
    // 0x74cab0: r3 = Null
    //     0x74cab0: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e7c8] Null
    //     0x74cab4: ldr             x3, [x3, #0x7c8]
    // 0x74cab8: r0 = DefaultTypeTest()
    //     0x74cab8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x74cabc: r1 = 1
    //     0x74cabc: movz            x1, #0x1
    // 0x74cac0: r0 = AllocateContext()
    //     0x74cac0: bl              #0xec126c  ; AllocateContextStub
    // 0x74cac4: mov             x4, x0
    // 0x74cac8: ldur            x3, [fp, #-0x18]
    // 0x74cacc: stur            x4, [fp, #-0x30]
    // 0x74cad0: StoreField: r4->field_f = r3
    //     0x74cad0: stur            w3, [x4, #0xf]
    // 0x74cad4: LoadField: r5 = r3->field_27
    //     0x74cad4: ldur            w5, [x3, #0x27]
    // 0x74cad8: DecompressPointer r5
    //     0x74cad8: add             x5, x5, HEAP, lsl #32
    // 0x74cadc: stur            x5, [fp, #-0x28]
    // 0x74cae0: cmp             w5, NULL
    // 0x74cae4: b.eq            #0x74ccb4
    // 0x74cae8: ldur            x6, [fp, #-8]
    // 0x74caec: mov             x0, x5
    // 0x74caf0: r2 = Null
    //     0x74caf0: mov             x2, NULL
    // 0x74caf4: r1 = Null
    //     0x74caf4: mov             x1, NULL
    // 0x74caf8: r4 = LoadClassIdInstr(r0)
    //     0x74caf8: ldur            x4, [x0, #-1]
    //     0x74cafc: ubfx            x4, x4, #0xc, #0x14
    // 0x74cb00: sub             x4, x4, #0xc83
    // 0x74cb04: cmp             x4, #1
    // 0x74cb08: b.ls            #0x74cb1c
    // 0x74cb0c: r8 = BoxConstraints
    //     0x74cb0c: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x74cb10: r3 = Null
    //     0x74cb10: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e7d8] Null
    //     0x74cb14: ldr             x3, [x3, #0x7d8]
    // 0x74cb18: r0 = BoxConstraints()
    //     0x74cb18: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x74cb1c: ldur            x2, [fp, #-0x28]
    // 0x74cb20: ldur            x3, [fp, #-0x20]
    // 0x74cb24: r0 = AllocateRecord2()
    //     0x74cb24: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x74cb28: ldur            x2, [fp, #-0x30]
    // 0x74cb2c: r1 = Function '<anonymous closure>':.
    //     0x74cb2c: add             x1, PP, #0x45, lsl #12  ; [pp+0x456d0] AnonymousClosure: (0x74b7cc), in [package:flutter/src/rendering/box.dart] RenderBox::getDistanceToActualBaseline (0x74b4d4)
    //     0x74cb30: ldr             x1, [x1, #0x6d0]
    // 0x74cb34: stur            x0, [fp, #-0x28]
    // 0x74cb38: r0 = AllocateClosure()
    //     0x74cb38: bl              #0xec1630  ; AllocateClosureStub
    // 0x74cb3c: r16 = <(BoxConstraints, TextBaseline), double?>
    //     0x74cb3c: add             x16, PP, #0x45, lsl #12  ; [pp+0x456d8] TypeArguments: <(BoxConstraints, TextBaseline), double?>
    //     0x74cb40: ldr             x16, [x16, #0x6d8]
    // 0x74cb44: ldur            lr, [fp, #-0x18]
    // 0x74cb48: stp             lr, x16, [SP, #0x18]
    // 0x74cb4c: r16 = Instance__Baseline
    //     0x74cb4c: add             x16, PP, #0x45, lsl #12  ; [pp+0x456e0] Obj!_Baseline@e117c1
    //     0x74cb50: ldr             x16, [x16, #0x6e0]
    // 0x74cb54: ldur            lr, [fp, #-0x28]
    // 0x74cb58: stp             lr, x16, [SP, #8]
    // 0x74cb5c: str             x0, [SP]
    // 0x74cb60: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x74cb60: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x74cb64: r0 = _computeIntrinsics()
    //     0x74cb64: bl              #0x72d344  ; [package:flutter/src/rendering/box.dart] RenderBox::_computeIntrinsics
    // 0x74cb68: mov             x1, x0
    // 0x74cb6c: ldur            x0, [fp, #-8]
    // 0x74cb70: LoadField: r2 = r0->field_7
    //     0x74cb70: ldur            w2, [x0, #7]
    // 0x74cb74: DecompressPointer r2
    //     0x74cb74: add             x2, x2, HEAP, lsl #32
    // 0x74cb78: LoadField: d0 = r2->field_f
    //     0x74cb78: ldur            d0, [x2, #0xf]
    // 0x74cb7c: cmp             w1, NULL
    // 0x74cb80: b.ne            #0x74cb8c
    // 0x74cb84: r2 = Null
    //     0x74cb84: mov             x2, NULL
    // 0x74cb88: b               #0x74cbc0
    // 0x74cb8c: LoadField: d1 = r1->field_7
    //     0x74cb8c: ldur            d1, [x1, #7]
    // 0x74cb90: fadd            d2, d1, d0
    // 0x74cb94: r1 = inline_Allocate_Double()
    //     0x74cb94: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x74cb98: add             x1, x1, #0x10
    //     0x74cb9c: cmp             x2, x1
    //     0x74cba0: b.ls            #0x74cce4
    //     0x74cba4: str             x1, [THR, #0x50]  ; THR::top
    //     0x74cba8: sub             x1, x1, #0xf
    //     0x74cbac: movz            x2, #0xe15c
    //     0x74cbb0: movk            x2, #0x3, lsl #16
    //     0x74cbb4: stur            x2, [x1, #-1]
    // 0x74cbb8: StoreField: r1->field_7 = d2
    //     0x74cbb8: stur            d2, [x1, #7]
    // 0x74cbbc: mov             x2, x1
    // 0x74cbc0: ldur            x1, [fp, #-0x10]
    // 0x74cbc4: cmp             w1, NULL
    // 0x74cbc8: b.eq            #0x74cc28
    // 0x74cbcc: cmp             w2, NULL
    // 0x74cbd0: b.eq            #0x74cc20
    // 0x74cbd4: LoadField: d0 = r1->field_7
    //     0x74cbd4: ldur            d0, [x1, #7]
    // 0x74cbd8: LoadField: d1 = r2->field_7
    //     0x74cbd8: ldur            d1, [x2, #7]
    // 0x74cbdc: fcmp            d0, d1
    // 0x74cbe0: b.lt            #0x74cbec
    // 0x74cbe4: LoadField: d0 = r2->field_7
    //     0x74cbe4: ldur            d0, [x2, #7]
    // 0x74cbe8: b               #0x74cbf0
    // 0x74cbec: LoadField: d0 = r1->field_7
    //     0x74cbec: ldur            d0, [x1, #7]
    // 0x74cbf0: r1 = inline_Allocate_Double()
    //     0x74cbf0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x74cbf4: add             x1, x1, #0x10
    //     0x74cbf8: cmp             x2, x1
    //     0x74cbfc: b.ls            #0x74cd00
    //     0x74cc00: str             x1, [THR, #0x50]  ; THR::top
    //     0x74cc04: sub             x1, x1, #0xf
    //     0x74cc08: movz            x2, #0xe15c
    //     0x74cc0c: movk            x2, #0x3, lsl #16
    //     0x74cc10: stur            x2, [x1, #-1]
    // 0x74cc14: StoreField: r1->field_7 = d0
    //     0x74cc14: stur            d0, [x1, #7]
    // 0x74cc18: mov             x5, x1
    // 0x74cc1c: b               #0x74cc90
    // 0x74cc20: r3 = true
    //     0x74cc20: add             x3, NULL, #0x20  ; true
    // 0x74cc24: b               #0x74cc2c
    // 0x74cc28: r3 = false
    //     0x74cc28: add             x3, NULL, #0x30  ; false
    // 0x74cc2c: cmp             w1, NULL
    // 0x74cc30: b.eq            #0x74cc68
    // 0x74cc34: tbnz            w3, #4, #0x74cc44
    // 0x74cc38: r4 = Null
    //     0x74cc38: mov             x4, NULL
    // 0x74cc3c: r3 = Null
    //     0x74cc3c: mov             x3, NULL
    // 0x74cc40: b               #0x74cc4c
    // 0x74cc44: mov             x4, x2
    // 0x74cc48: mov             x3, x2
    // 0x74cc4c: cmp             w4, NULL
    // 0x74cc50: b.ne            #0x74cc5c
    // 0x74cc54: mov             x5, x1
    // 0x74cc58: b               #0x74cc90
    // 0x74cc5c: mov             x5, x3
    // 0x74cc60: r3 = true
    //     0x74cc60: add             x3, NULL, #0x20  ; true
    // 0x74cc64: b               #0x74cc6c
    // 0x74cc68: r5 = Null
    //     0x74cc68: mov             x5, NULL
    // 0x74cc6c: cmp             w1, NULL
    // 0x74cc70: b.ne            #0x74cc8c
    // 0x74cc74: tbnz            w3, #4, #0x74cc80
    // 0x74cc78: mov             x1, x5
    // 0x74cc7c: b               #0x74cc84
    // 0x74cc80: mov             x1, x2
    // 0x74cc84: mov             x5, x1
    // 0x74cc88: b               #0x74cc90
    // 0x74cc8c: r5 = Null
    //     0x74cc8c: mov             x5, NULL
    // 0x74cc90: LoadField: r4 = r0->field_13
    //     0x74cc90: ldur            w4, [x0, #0x13]
    // 0x74cc94: DecompressPointer r4
    //     0x74cc94: add             x4, x4, HEAP, lsl #32
    // 0x74cc98: ldur            x3, [fp, #-0x20]
    // 0x74cc9c: b               #0x74ca58
    // 0x74cca0: mov             x1, x5
    // 0x74cca4: mov             x0, x1
    // 0x74cca8: LeaveFrame
    //     0x74cca8: mov             SP, fp
    //     0x74ccac: ldp             fp, lr, [SP], #0x10
    // 0x74ccb0: ret
    //     0x74ccb0: ret             
    // 0x74ccb4: r0 = StateError()
    //     0x74ccb4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x74ccb8: mov             x1, x0
    // 0x74ccbc: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x74ccbc: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x74ccc0: StoreField: r1->field_b = r0
    //     0x74ccc0: stur            w0, [x1, #0xb]
    // 0x74ccc4: mov             x0, x1
    // 0x74ccc8: r0 = Throw()
    //     0x74ccc8: bl              #0xec04b8  ; ThrowStub
    // 0x74cccc: brk             #0
    // 0x74ccd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74ccd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74ccd4: b               #0x74ca48
    // 0x74ccd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74ccd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74ccdc: b               #0x74ca6c
    // 0x74cce0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74cce0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x74cce4: SaveReg d2
    //     0x74cce4: str             q2, [SP, #-0x10]!
    // 0x74cce8: SaveReg r0
    //     0x74cce8: str             x0, [SP, #-8]!
    // 0x74ccec: r0 = AllocateDouble()
    //     0x74ccec: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74ccf0: mov             x1, x0
    // 0x74ccf4: RestoreReg r0
    //     0x74ccf4: ldr             x0, [SP], #8
    // 0x74ccf8: RestoreReg d2
    //     0x74ccf8: ldr             q2, [SP], #0x10
    // 0x74ccfc: b               #0x74cbb8
    // 0x74cd00: SaveReg d0
    //     0x74cd00: str             q0, [SP, #-0x10]!
    // 0x74cd04: SaveReg r0
    //     0x74cd04: str             x0, [SP, #-8]!
    // 0x74cd08: r0 = AllocateDouble()
    //     0x74cd08: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74cd0c: mov             x1, x0
    // 0x74cd10: RestoreReg r0
    //     0x74cd10: ldr             x0, [SP], #8
    // 0x74cd14: RestoreReg d0
    //     0x74cd14: ldr             q0, [SP], #0x10
    // 0x74cd18: b               #0x74cc14
  }
  _ defaultPaint(/* No info */) {
    // ** addr: 0x79ba6c, size: 0x12c
    // 0x79ba6c: EnterFrame
    //     0x79ba6c: stp             fp, lr, [SP, #-0x10]!
    //     0x79ba70: mov             fp, SP
    // 0x79ba74: AllocStack(0x38)
    //     0x79ba74: sub             SP, SP, #0x38
    // 0x79ba78: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */)
    //     0x79ba78: mov             x4, x2
    //     0x79ba7c: stur            x2, [fp, #-0x18]
    // 0x79ba80: CheckStackOverflow
    //     0x79ba80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79ba84: cmp             SP, x16
    //     0x79ba88: b.ls            #0x79bb84
    // 0x79ba8c: LoadField: r0 = r1->field_5f
    //     0x79ba8c: ldur            w0, [x1, #0x5f]
    // 0x79ba90: DecompressPointer r0
    //     0x79ba90: add             x0, x0, HEAP, lsl #32
    // 0x79ba94: LoadField: d0 = r3->field_7
    //     0x79ba94: ldur            d0, [x3, #7]
    // 0x79ba98: stur            d0, [fp, #-0x28]
    // 0x79ba9c: LoadField: d1 = r3->field_f
    //     0x79ba9c: ldur            d1, [x3, #0xf]
    // 0x79baa0: stur            d1, [fp, #-0x20]
    // 0x79baa4: mov             x3, x0
    // 0x79baa8: stur            x3, [fp, #-0x10]
    // 0x79baac: CheckStackOverflow
    //     0x79baac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79bab0: cmp             SP, x16
    //     0x79bab4: b.ls            #0x79bb8c
    // 0x79bab8: cmp             w3, NULL
    // 0x79babc: b.eq            #0x79bb74
    // 0x79bac0: LoadField: r5 = r3->field_7
    //     0x79bac0: ldur            w5, [x3, #7]
    // 0x79bac4: DecompressPointer r5
    //     0x79bac4: add             x5, x5, HEAP, lsl #32
    // 0x79bac8: stur            x5, [fp, #-8]
    // 0x79bacc: cmp             w5, NULL
    // 0x79bad0: b.eq            #0x79bb94
    // 0x79bad4: mov             x0, x5
    // 0x79bad8: r2 = Null
    //     0x79bad8: mov             x2, NULL
    // 0x79badc: r1 = Null
    //     0x79badc: mov             x1, NULL
    // 0x79bae0: r4 = LoadClassIdInstr(r0)
    //     0x79bae0: ldur            x4, [x0, #-1]
    //     0x79bae4: ubfx            x4, x4, #0xc, #0x14
    // 0x79bae8: sub             x4, x4, #0xc7a
    // 0x79baec: cmp             x4, #2
    // 0x79baf0: b.ls            #0x79bb08
    // 0x79baf4: r8 = StackParentData
    //     0x79baf4: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0x79baf8: ldr             x8, [x8, #0x5c0]
    // 0x79bafc: r3 = Null
    //     0x79bafc: add             x3, PP, #0x44, lsl #12  ; [pp+0x44ca0] Null
    //     0x79bb00: ldr             x3, [x3, #0xca0]
    // 0x79bb04: r0 = DefaultTypeTest()
    //     0x79bb04: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x79bb08: ldur            x0, [fp, #-8]
    // 0x79bb0c: LoadField: r1 = r0->field_7
    //     0x79bb0c: ldur            w1, [x0, #7]
    // 0x79bb10: DecompressPointer r1
    //     0x79bb10: add             x1, x1, HEAP, lsl #32
    // 0x79bb14: LoadField: d0 = r1->field_7
    //     0x79bb14: ldur            d0, [x1, #7]
    // 0x79bb18: ldur            d1, [fp, #-0x28]
    // 0x79bb1c: fadd            d2, d0, d1
    // 0x79bb20: stur            d2, [fp, #-0x38]
    // 0x79bb24: LoadField: d0 = r1->field_f
    //     0x79bb24: ldur            d0, [x1, #0xf]
    // 0x79bb28: ldur            d3, [fp, #-0x20]
    // 0x79bb2c: fadd            d4, d0, d3
    // 0x79bb30: stur            d4, [fp, #-0x30]
    // 0x79bb34: r0 = Offset()
    //     0x79bb34: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x79bb38: ldur            d0, [fp, #-0x38]
    // 0x79bb3c: StoreField: r0->field_7 = d0
    //     0x79bb3c: stur            d0, [x0, #7]
    // 0x79bb40: ldur            d0, [fp, #-0x30]
    // 0x79bb44: StoreField: r0->field_f = d0
    //     0x79bb44: stur            d0, [x0, #0xf]
    // 0x79bb48: ldur            x1, [fp, #-0x18]
    // 0x79bb4c: ldur            x2, [fp, #-0x10]
    // 0x79bb50: mov             x3, x0
    // 0x79bb54: r0 = paintChild()
    //     0x79bb54: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x79bb58: ldur            x1, [fp, #-8]
    // 0x79bb5c: LoadField: r3 = r1->field_13
    //     0x79bb5c: ldur            w3, [x1, #0x13]
    // 0x79bb60: DecompressPointer r3
    //     0x79bb60: add             x3, x3, HEAP, lsl #32
    // 0x79bb64: ldur            x4, [fp, #-0x18]
    // 0x79bb68: ldur            d0, [fp, #-0x28]
    // 0x79bb6c: ldur            d1, [fp, #-0x20]
    // 0x79bb70: b               #0x79baa8
    // 0x79bb74: r0 = Null
    //     0x79bb74: mov             x0, NULL
    // 0x79bb78: LeaveFrame
    //     0x79bb78: mov             SP, fp
    //     0x79bb7c: ldp             fp, lr, [SP], #0x10
    // 0x79bb80: ret
    //     0x79bb80: ret             
    // 0x79bb84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x79bb84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x79bb88: b               #0x79ba8c
    // 0x79bb8c: r0 = StackOverflowSharedWithFPURegs()
    //     0x79bb8c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x79bb90: b               #0x79bab8
    // 0x79bb94: r0 = NullCastErrorSharedWithFPURegs()
    //     0x79bb94: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ defaultHitTestChildren(/* No info */) {
    // ** addr: 0x7fedb0, size: 0x148
    // 0x7fedb0: EnterFrame
    //     0x7fedb0: stp             fp, lr, [SP, #-0x10]!
    //     0x7fedb4: mov             fp, SP
    // 0x7fedb8: AllocStack(0x28)
    //     0x7fedb8: sub             SP, SP, #0x28
    // 0x7fedbc: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7fedbc: mov             x4, x2
    //     0x7fedc0: stur            x2, [fp, #-0x18]
    //     0x7fedc4: stur            x3, [fp, #-0x20]
    // 0x7fedc8: CheckStackOverflow
    //     0x7fedc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fedcc: cmp             SP, x16
    //     0x7fedd0: b.ls            #0x7feee4
    // 0x7fedd4: LoadField: r0 = r1->field_63
    //     0x7fedd4: ldur            w0, [x1, #0x63]
    // 0x7fedd8: DecompressPointer r0
    //     0x7fedd8: add             x0, x0, HEAP, lsl #32
    // 0x7feddc: mov             x5, x0
    // 0x7fede0: stur            x5, [fp, #-0x10]
    // 0x7fede4: CheckStackOverflow
    //     0x7fede4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fede8: cmp             SP, x16
    //     0x7fedec: b.ls            #0x7feeec
    // 0x7fedf0: cmp             w5, NULL
    // 0x7fedf4: b.eq            #0x7feed4
    // 0x7fedf8: LoadField: r6 = r5->field_7
    //     0x7fedf8: ldur            w6, [x5, #7]
    // 0x7fedfc: DecompressPointer r6
    //     0x7fedfc: add             x6, x6, HEAP, lsl #32
    // 0x7fee00: stur            x6, [fp, #-8]
    // 0x7fee04: cmp             w6, NULL
    // 0x7fee08: b.eq            #0x7feef4
    // 0x7fee0c: mov             x0, x6
    // 0x7fee10: r2 = Null
    //     0x7fee10: mov             x2, NULL
    // 0x7fee14: r1 = Null
    //     0x7fee14: mov             x1, NULL
    // 0x7fee18: r4 = LoadClassIdInstr(r0)
    //     0x7fee18: ldur            x4, [x0, #-1]
    //     0x7fee1c: ubfx            x4, x4, #0xc, #0x14
    // 0x7fee20: sub             x4, x4, #0xc7a
    // 0x7fee24: cmp             x4, #2
    // 0x7fee28: b.ls            #0x7fee40
    // 0x7fee2c: r8 = StackParentData
    //     0x7fee2c: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0x7fee30: ldr             x8, [x8, #0x5c0]
    // 0x7fee34: r3 = Null
    //     0x7fee34: add             x3, PP, #0x44, lsl #12  ; [pp+0x44ce0] Null
    //     0x7fee38: ldr             x3, [x3, #0xce0]
    // 0x7fee3c: r0 = DefaultTypeTest()
    //     0x7fee3c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7fee40: ldur            x0, [fp, #-8]
    // 0x7fee44: LoadField: r3 = r0->field_7
    //     0x7fee44: ldur            w3, [x0, #7]
    // 0x7fee48: DecompressPointer r3
    //     0x7fee48: add             x3, x3, HEAP, lsl #32
    // 0x7fee4c: ldur            x1, [fp, #-0x20]
    // 0x7fee50: mov             x2, x3
    // 0x7fee54: stur            x3, [fp, #-0x28]
    // 0x7fee58: r0 = -()
    //     0x7fee58: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x7fee5c: ldur            x1, [fp, #-0x28]
    // 0x7fee60: stur            x0, [fp, #-0x28]
    // 0x7fee64: r0 = unary-()
    //     0x7fee64: bl              #0x6a58ac  ; [dart:ui] Offset::unary-
    // 0x7fee68: ldur            x1, [fp, #-0x18]
    // 0x7fee6c: mov             x2, x0
    // 0x7fee70: r0 = pushOffset()
    //     0x7fee70: bl              #0x7faa44  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::pushOffset
    // 0x7fee74: ldur            x1, [fp, #-0x10]
    // 0x7fee78: r0 = LoadClassIdInstr(r1)
    //     0x7fee78: ldur            x0, [x1, #-1]
    //     0x7fee7c: ubfx            x0, x0, #0xc, #0x14
    // 0x7fee80: ldur            x2, [fp, #-0x18]
    // 0x7fee84: ldur            x3, [fp, #-0x28]
    // 0x7fee88: r0 = GDT[cid_x0 + 0xdf93]()
    //     0x7fee88: movz            x17, #0xdf93
    //     0x7fee8c: add             lr, x0, x17
    //     0x7fee90: ldr             lr, [x21, lr, lsl #3]
    //     0x7fee94: blr             lr
    // 0x7fee98: ldur            x1, [fp, #-0x18]
    // 0x7fee9c: stur            x0, [fp, #-0x10]
    // 0x7feea0: r0 = popTransform()
    //     0x7feea0: bl              #0x7fa9a8  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::popTransform
    // 0x7feea4: ldur            x1, [fp, #-0x10]
    // 0x7feea8: tbz             w1, #4, #0x7feec4
    // 0x7feeac: ldur            x1, [fp, #-8]
    // 0x7feeb0: LoadField: r5 = r1->field_f
    //     0x7feeb0: ldur            w5, [x1, #0xf]
    // 0x7feeb4: DecompressPointer r5
    //     0x7feeb4: add             x5, x5, HEAP, lsl #32
    // 0x7feeb8: ldur            x4, [fp, #-0x18]
    // 0x7feebc: ldur            x3, [fp, #-0x20]
    // 0x7feec0: b               #0x7fede0
    // 0x7feec4: r0 = true
    //     0x7feec4: add             x0, NULL, #0x20  ; true
    // 0x7feec8: LeaveFrame
    //     0x7feec8: mov             SP, fp
    //     0x7feecc: ldp             fp, lr, [SP], #0x10
    // 0x7feed0: ret
    //     0x7feed0: ret             
    // 0x7feed4: r0 = false
    //     0x7feed4: add             x0, NULL, #0x30  ; false
    // 0x7feed8: LeaveFrame
    //     0x7feed8: mov             SP, fp
    //     0x7feedc: ldp             fp, lr, [SP], #0x10
    // 0x7feee0: ret
    //     0x7feee0: ret             
    // 0x7feee4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7feee4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7feee8: b               #0x7fedd4
    // 0x7feeec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7feeec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7feef0: b               #0x7fedf0
    // 0x7feef4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7feef4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3030, size: 0x84, field offset: 0x68
class RenderStack extends _RenderStack&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin {

  [closure] double <anonymous closure>(dynamic, RenderBox) {
    // ** addr: 0x733c7c, size: 0x80
    // 0x733c7c: EnterFrame
    //     0x733c7c: stp             fp, lr, [SP, #-0x10]!
    //     0x733c80: mov             fp, SP
    // 0x733c84: ldr             x0, [fp, #0x18]
    // 0x733c88: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x733c88: ldur            w1, [x0, #0x17]
    // 0x733c8c: DecompressPointer r1
    //     0x733c8c: add             x1, x1, HEAP, lsl #32
    // 0x733c90: CheckStackOverflow
    //     0x733c90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x733c94: cmp             SP, x16
    //     0x733c98: b.ls            #0x733ce4
    // 0x733c9c: LoadField: r0 = r1->field_f
    //     0x733c9c: ldur            w0, [x1, #0xf]
    // 0x733ca0: DecompressPointer r0
    //     0x733ca0: add             x0, x0, HEAP, lsl #32
    // 0x733ca4: LoadField: d0 = r0->field_7
    //     0x733ca4: ldur            d0, [x0, #7]
    // 0x733ca8: ldr             x1, [fp, #0x10]
    // 0x733cac: r0 = getMinIntrinsicWidth()
    //     0x733cac: bl              #0x72d27c  ; [package:flutter/src/rendering/box.dart] RenderBox::getMinIntrinsicWidth
    // 0x733cb0: r0 = inline_Allocate_Double()
    //     0x733cb0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x733cb4: add             x0, x0, #0x10
    //     0x733cb8: cmp             x1, x0
    //     0x733cbc: b.ls            #0x733cec
    //     0x733cc0: str             x0, [THR, #0x50]  ; THR::top
    //     0x733cc4: sub             x0, x0, #0xf
    //     0x733cc8: movz            x1, #0xe15c
    //     0x733ccc: movk            x1, #0x3, lsl #16
    //     0x733cd0: stur            x1, [x0, #-1]
    // 0x733cd4: StoreField: r0->field_7 = d0
    //     0x733cd4: stur            d0, [x0, #7]
    // 0x733cd8: LeaveFrame
    //     0x733cd8: mov             SP, fp
    //     0x733cdc: ldp             fp, lr, [SP], #0x10
    // 0x733ce0: ret
    //     0x733ce0: ret             
    // 0x733ce4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x733ce4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x733ce8: b               #0x733c9c
    // 0x733cec: SaveReg d0
    //     0x733cec: str             q0, [SP, #-0x10]!
    // 0x733cf0: r0 = AllocateDouble()
    //     0x733cf0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x733cf4: RestoreReg d0
    //     0x733cf4: ldr             q0, [SP], #0x10
    // 0x733cf8: b               #0x733cd4
  }
  _ computeMinIntrinsicWidth(/* No info */) {
    // ** addr: 0x733cfc, size: 0x74
    // 0x733cfc: EnterFrame
    //     0x733cfc: stp             fp, lr, [SP, #-0x10]!
    //     0x733d00: mov             fp, SP
    // 0x733d04: AllocStack(0x10)
    //     0x733d04: sub             SP, SP, #0x10
    // 0x733d08: SetupParameters(RenderStack this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x733d08: stur            x1, [fp, #-8]
    //     0x733d0c: stur            x2, [fp, #-0x10]
    // 0x733d10: CheckStackOverflow
    //     0x733d10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x733d14: cmp             SP, x16
    //     0x733d18: b.ls            #0x733d68
    // 0x733d1c: r1 = 1
    //     0x733d1c: movz            x1, #0x1
    // 0x733d20: r0 = AllocateContext()
    //     0x733d20: bl              #0xec126c  ; AllocateContextStub
    // 0x733d24: mov             x1, x0
    // 0x733d28: ldur            x0, [fp, #-0x10]
    // 0x733d2c: StoreField: r1->field_f = r0
    //     0x733d2c: stur            w0, [x1, #0xf]
    // 0x733d30: ldur            x0, [fp, #-8]
    // 0x733d34: LoadField: r3 = r0->field_5f
    //     0x733d34: ldur            w3, [x0, #0x5f]
    // 0x733d38: DecompressPointer r3
    //     0x733d38: add             x3, x3, HEAP, lsl #32
    // 0x733d3c: mov             x2, x1
    // 0x733d40: stur            x3, [fp, #-0x10]
    // 0x733d44: r1 = Function '<anonymous closure>':.
    //     0x733d44: add             x1, PP, #0x55, lsl #12  ; [pp+0x556f8] AnonymousClosure: (0x733c7c), in [package:flutter/src/rendering/stack.dart] RenderStack::computeMinIntrinsicWidth (0x733cfc)
    //     0x733d48: ldr             x1, [x1, #0x6f8]
    // 0x733d4c: r0 = AllocateClosure()
    //     0x733d4c: bl              #0xec1630  ; AllocateClosureStub
    // 0x733d50: ldur            x1, [fp, #-0x10]
    // 0x733d54: mov             x2, x0
    // 0x733d58: r0 = getIntrinsicDimension()
    //     0x733d58: bl              #0x733d70  ; [package:flutter/src/rendering/stack.dart] RenderStack::getIntrinsicDimension
    // 0x733d5c: LeaveFrame
    //     0x733d5c: mov             SP, fp
    //     0x733d60: ldp             fp, lr, [SP], #0x10
    // 0x733d64: ret
    //     0x733d64: ret             
    // 0x733d68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x733d68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x733d6c: b               #0x733d1c
  }
  static _ getIntrinsicDimension(/* No info */) {
    // ** addr: 0x733d70, size: 0x2d0
    // 0x733d70: EnterFrame
    //     0x733d70: stp             fp, lr, [SP, #-0x10]!
    //     0x733d74: mov             fp, SP
    // 0x733d78: AllocStack(0x30)
    //     0x733d78: sub             SP, SP, #0x30
    // 0x733d7c: SetupParameters(dynamic _ /* r2 => r3, fp-0x20 */)
    //     0x733d7c: mov             x3, x2
    //     0x733d80: stur            x2, [fp, #-0x20]
    // 0x733d84: CheckStackOverflow
    //     0x733d84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x733d88: cmp             SP, x16
    //     0x733d8c: b.ls            #0x73401c
    // 0x733d90: mov             x4, x1
    // 0x733d94: r5 = 0.000000
    //     0x733d94: ldr             x5, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x733d98: stur            x5, [fp, #-0x10]
    // 0x733d9c: stur            x4, [fp, #-0x18]
    // 0x733da0: CheckStackOverflow
    //     0x733da0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x733da4: cmp             SP, x16
    //     0x733da8: b.ls            #0x734024
    // 0x733dac: cmp             w4, NULL
    // 0x733db0: b.eq            #0x734008
    // 0x733db4: LoadField: r6 = r4->field_7
    //     0x733db4: ldur            w6, [x4, #7]
    // 0x733db8: DecompressPointer r6
    //     0x733db8: add             x6, x6, HEAP, lsl #32
    // 0x733dbc: stur            x6, [fp, #-8]
    // 0x733dc0: cmp             w6, NULL
    // 0x733dc4: b.eq            #0x73402c
    // 0x733dc8: mov             x0, x6
    // 0x733dcc: r2 = Null
    //     0x733dcc: mov             x2, NULL
    // 0x733dd0: r1 = Null
    //     0x733dd0: mov             x1, NULL
    // 0x733dd4: r4 = LoadClassIdInstr(r0)
    //     0x733dd4: ldur            x4, [x0, #-1]
    //     0x733dd8: ubfx            x4, x4, #0xc, #0x14
    // 0x733ddc: sub             x4, x4, #0xc7a
    // 0x733de0: cmp             x4, #2
    // 0x733de4: b.ls            #0x733dfc
    // 0x733de8: r8 = StackParentData
    //     0x733de8: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0x733dec: ldr             x8, [x8, #0x5c0]
    // 0x733df0: r3 = Null
    //     0x733df0: add             x3, PP, #0x55, lsl #12  ; [pp+0x556c0] Null
    //     0x733df4: ldr             x3, [x3, #0x6c0]
    // 0x733df8: r0 = DefaultTypeTest()
    //     0x733df8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x733dfc: ldur            x1, [fp, #-8]
    // 0x733e00: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x733e00: ldur            w0, [x1, #0x17]
    // 0x733e04: DecompressPointer r0
    //     0x733e04: add             x0, x0, HEAP, lsl #32
    // 0x733e08: cmp             w0, NULL
    // 0x733e0c: b.ne            #0x733ff0
    // 0x733e10: LoadField: r0 = r1->field_1b
    //     0x733e10: ldur            w0, [x1, #0x1b]
    // 0x733e14: DecompressPointer r0
    //     0x733e14: add             x0, x0, HEAP, lsl #32
    // 0x733e18: cmp             w0, NULL
    // 0x733e1c: b.ne            #0x733ff0
    // 0x733e20: LoadField: r0 = r1->field_1f
    //     0x733e20: ldur            w0, [x1, #0x1f]
    // 0x733e24: DecompressPointer r0
    //     0x733e24: add             x0, x0, HEAP, lsl #32
    // 0x733e28: cmp             w0, NULL
    // 0x733e2c: b.ne            #0x733ff0
    // 0x733e30: LoadField: r0 = r1->field_23
    //     0x733e30: ldur            w0, [x1, #0x23]
    // 0x733e34: DecompressPointer r0
    //     0x733e34: add             x0, x0, HEAP, lsl #32
    // 0x733e38: cmp             w0, NULL
    // 0x733e3c: b.ne            #0x733ff0
    // 0x733e40: LoadField: r0 = r1->field_27
    //     0x733e40: ldur            w0, [x1, #0x27]
    // 0x733e44: DecompressPointer r0
    //     0x733e44: add             x0, x0, HEAP, lsl #32
    // 0x733e48: cmp             w0, NULL
    // 0x733e4c: b.ne            #0x733ff0
    // 0x733e50: LoadField: r0 = r1->field_2b
    //     0x733e50: ldur            w0, [x1, #0x2b]
    // 0x733e54: DecompressPointer r0
    //     0x733e54: add             x0, x0, HEAP, lsl #32
    // 0x733e58: cmp             w0, NULL
    // 0x733e5c: b.ne            #0x733ff0
    // 0x733e60: ldur            x2, [fp, #-0x10]
    // 0x733e64: ldur            x16, [fp, #-0x20]
    // 0x733e68: ldur            lr, [fp, #-0x18]
    // 0x733e6c: stp             lr, x16, [SP]
    // 0x733e70: ldur            x0, [fp, #-0x20]
    // 0x733e74: ClosureCall
    //     0x733e74: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x733e78: ldur            x2, [x0, #0x1f]
    //     0x733e7c: blr             x2
    // 0x733e80: mov             x2, x0
    // 0x733e84: ldur            x1, [fp, #-0x10]
    // 0x733e88: stur            x2, [fp, #-0x18]
    // 0x733e8c: r0 = 60
    //     0x733e8c: movz            x0, #0x3c
    // 0x733e90: branchIfSmi(r1, 0x733e9c)
    //     0x733e90: tbz             w1, #0, #0x733e9c
    // 0x733e94: r0 = LoadClassIdInstr(r1)
    //     0x733e94: ldur            x0, [x1, #-1]
    //     0x733e98: ubfx            x0, x0, #0xc, #0x14
    // 0x733e9c: stp             x2, x1, [SP]
    // 0x733ea0: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x733ea0: sub             lr, x0, #0xfe3
    //     0x733ea4: ldr             lr, [x21, lr, lsl #3]
    //     0x733ea8: blr             lr
    // 0x733eac: tbnz            w0, #4, #0x733eb8
    // 0x733eb0: ldur            x0, [fp, #-0x10]
    // 0x733eb4: b               #0x733fe8
    // 0x733eb8: ldur            x1, [fp, #-0x10]
    // 0x733ebc: r0 = 60
    //     0x733ebc: movz            x0, #0x3c
    // 0x733ec0: branchIfSmi(r1, 0x733ecc)
    //     0x733ec0: tbz             w1, #0, #0x733ecc
    // 0x733ec4: r0 = LoadClassIdInstr(r1)
    //     0x733ec4: ldur            x0, [x1, #-1]
    //     0x733ec8: ubfx            x0, x0, #0xc, #0x14
    // 0x733ecc: ldur            x16, [fp, #-0x18]
    // 0x733ed0: stp             x16, x1, [SP]
    // 0x733ed4: r0 = GDT[cid_x0 + -0xfd2]()
    //     0x733ed4: sub             lr, x0, #0xfd2
    //     0x733ed8: ldr             lr, [x21, lr, lsl #3]
    //     0x733edc: blr             lr
    // 0x733ee0: tbnz            w0, #4, #0x733eec
    // 0x733ee4: ldur            x0, [fp, #-0x18]
    // 0x733ee8: b               #0x733fe8
    // 0x733eec: ldur            x1, [fp, #-0x18]
    // 0x733ef0: r0 = 60
    //     0x733ef0: movz            x0, #0x3c
    // 0x733ef4: branchIfSmi(r1, 0x733f00)
    //     0x733ef4: tbz             w1, #0, #0x733f00
    // 0x733ef8: r0 = LoadClassIdInstr(r1)
    //     0x733ef8: ldur            x0, [x1, #-1]
    //     0x733efc: ubfx            x0, x0, #0xc, #0x14
    // 0x733f00: cmp             x0, #0x3e
    // 0x733f04: b.ne            #0x733f88
    // 0x733f08: ldur            x2, [fp, #-0x10]
    // 0x733f0c: r0 = 60
    //     0x733f0c: movz            x0, #0x3c
    // 0x733f10: branchIfSmi(r2, 0x733f1c)
    //     0x733f10: tbz             w2, #0, #0x733f1c
    // 0x733f14: r0 = LoadClassIdInstr(r2)
    //     0x733f14: ldur            x0, [x2, #-1]
    //     0x733f18: ubfx            x0, x0, #0xc, #0x14
    // 0x733f1c: cmp             x0, #0x3e
    // 0x733f20: b.ne            #0x733f68
    // 0x733f24: d0 = 0.000000
    //     0x733f24: eor             v0.16b, v0.16b, v0.16b
    // 0x733f28: LoadField: d1 = r2->field_7
    //     0x733f28: ldur            d1, [x2, #7]
    // 0x733f2c: fcmp            d1, d0
    // 0x733f30: b.ne            #0x733f6c
    // 0x733f34: LoadField: d2 = r1->field_7
    //     0x733f34: ldur            d2, [x1, #7]
    // 0x733f38: fadd            d3, d1, d2
    // 0x733f3c: r0 = inline_Allocate_Double()
    //     0x733f3c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x733f40: add             x0, x0, #0x10
    //     0x733f44: cmp             x1, x0
    //     0x733f48: b.ls            #0x734030
    //     0x733f4c: str             x0, [THR, #0x50]  ; THR::top
    //     0x733f50: sub             x0, x0, #0xf
    //     0x733f54: movz            x1, #0xe15c
    //     0x733f58: movk            x1, #0x3, lsl #16
    //     0x733f5c: stur            x1, [x0, #-1]
    // 0x733f60: StoreField: r0->field_7 = d3
    //     0x733f60: stur            d3, [x0, #7]
    // 0x733f64: b               #0x733fe8
    // 0x733f68: d0 = 0.000000
    //     0x733f68: eor             v0.16b, v0.16b, v0.16b
    // 0x733f6c: LoadField: d1 = r1->field_7
    //     0x733f6c: ldur            d1, [x1, #7]
    // 0x733f70: fcmp            d1, d1
    // 0x733f74: b.vc            #0x733f80
    // 0x733f78: mov             x0, x1
    // 0x733f7c: b               #0x733fe8
    // 0x733f80: mov             x0, x2
    // 0x733f84: b               #0x733fe8
    // 0x733f88: ldur            x2, [fp, #-0x10]
    // 0x733f8c: d0 = 0.000000
    //     0x733f8c: eor             v0.16b, v0.16b, v0.16b
    // 0x733f90: r0 = 60
    //     0x733f90: movz            x0, #0x3c
    // 0x733f94: branchIfSmi(r1, 0x733fa0)
    //     0x733f94: tbz             w1, #0, #0x733fa0
    // 0x733f98: r0 = LoadClassIdInstr(r1)
    //     0x733f98: ldur            x0, [x1, #-1]
    //     0x733f9c: ubfx            x0, x0, #0xc, #0x14
    // 0x733fa0: stp             xzr, x1, [SP]
    // 0x733fa4: mov             lr, x0
    // 0x733fa8: ldr             lr, [x21, lr, lsl #3]
    // 0x733fac: blr             lr
    // 0x733fb0: tbnz            w0, #4, #0x733fe4
    // 0x733fb4: ldur            x1, [fp, #-0x10]
    // 0x733fb8: r0 = 60
    //     0x733fb8: movz            x0, #0x3c
    // 0x733fbc: branchIfSmi(r1, 0x733fc8)
    //     0x733fbc: tbz             w1, #0, #0x733fc8
    // 0x733fc0: r0 = LoadClassIdInstr(r1)
    //     0x733fc0: ldur            x0, [x1, #-1]
    //     0x733fc4: ubfx            x0, x0, #0xc, #0x14
    // 0x733fc8: str             x1, [SP]
    // 0x733fcc: r0 = GDT[cid_x0 + -0xfb8]()
    //     0x733fcc: sub             lr, x0, #0xfb8
    //     0x733fd0: ldr             lr, [x21, lr, lsl #3]
    //     0x733fd4: blr             lr
    // 0x733fd8: tbnz            w0, #4, #0x733fe4
    // 0x733fdc: ldur            x0, [fp, #-0x18]
    // 0x733fe0: b               #0x733fe8
    // 0x733fe4: ldur            x0, [fp, #-0x10]
    // 0x733fe8: mov             x5, x0
    // 0x733fec: b               #0x733ff4
    // 0x733ff0: ldur            x5, [fp, #-0x10]
    // 0x733ff4: ldur            x0, [fp, #-8]
    // 0x733ff8: LoadField: r4 = r0->field_13
    //     0x733ff8: ldur            w4, [x0, #0x13]
    // 0x733ffc: DecompressPointer r4
    //     0x733ffc: add             x4, x4, HEAP, lsl #32
    // 0x734000: ldur            x3, [fp, #-0x20]
    // 0x734004: b               #0x733d98
    // 0x734008: mov             x0, x5
    // 0x73400c: LoadField: d0 = r0->field_7
    //     0x73400c: ldur            d0, [x0, #7]
    // 0x734010: LeaveFrame
    //     0x734010: mov             SP, fp
    //     0x734014: ldp             fp, lr, [SP], #0x10
    // 0x734018: ret
    //     0x734018: ret             
    // 0x73401c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73401c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x734020: b               #0x733d90
    // 0x734024: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x734024: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x734028: b               #0x733dac
    // 0x73402c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x73402c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x734030: stp             q0, q3, [SP, #-0x20]!
    // 0x734034: r0 = AllocateDouble()
    //     0x734034: bl              #0xec2254  ; AllocateDoubleStub
    // 0x734038: ldp             q0, q3, [SP], #0x20
    // 0x73403c: b               #0x733f60
  }
  [closure] double computeMinIntrinsicWidth(dynamic, double) {
    // ** addr: 0x734040, size: 0x74
    // 0x734040: EnterFrame
    //     0x734040: stp             fp, lr, [SP, #-0x10]!
    //     0x734044: mov             fp, SP
    // 0x734048: ldr             x0, [fp, #0x18]
    // 0x73404c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x73404c: ldur            w1, [x0, #0x17]
    // 0x734050: DecompressPointer r1
    //     0x734050: add             x1, x1, HEAP, lsl #32
    // 0x734054: CheckStackOverflow
    //     0x734054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734058: cmp             SP, x16
    //     0x73405c: b.ls            #0x73409c
    // 0x734060: ldr             x2, [fp, #0x10]
    // 0x734064: r0 = computeMinIntrinsicWidth()
    //     0x734064: bl              #0x733cfc  ; [package:flutter/src/rendering/stack.dart] RenderStack::computeMinIntrinsicWidth
    // 0x734068: r0 = inline_Allocate_Double()
    //     0x734068: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x73406c: add             x0, x0, #0x10
    //     0x734070: cmp             x1, x0
    //     0x734074: b.ls            #0x7340a4
    //     0x734078: str             x0, [THR, #0x50]  ; THR::top
    //     0x73407c: sub             x0, x0, #0xf
    //     0x734080: movz            x1, #0xe15c
    //     0x734084: movk            x1, #0x3, lsl #16
    //     0x734088: stur            x1, [x0, #-1]
    // 0x73408c: StoreField: r0->field_7 = d0
    //     0x73408c: stur            d0, [x0, #7]
    // 0x734090: LeaveFrame
    //     0x734090: mov             SP, fp
    //     0x734094: ldp             fp, lr, [SP], #0x10
    // 0x734098: ret
    //     0x734098: ret             
    // 0x73409c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73409c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7340a0: b               #0x734060
    // 0x7340a4: SaveReg d0
    //     0x7340a4: str             q0, [SP, #-0x10]!
    // 0x7340a8: r0 = AllocateDouble()
    //     0x7340a8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7340ac: RestoreReg d0
    //     0x7340ac: ldr             q0, [SP], #0x10
    // 0x7340b0: b               #0x73408c
  }
  dynamic computeMinIntrinsicWidth(dynamic) {
    // ** addr: 0x7340d8, size: 0x24
    // 0x7340d8: EnterFrame
    //     0x7340d8: stp             fp, lr, [SP, #-0x10]!
    //     0x7340dc: mov             fp, SP
    // 0x7340e0: ldr             x2, [fp, #0x10]
    // 0x7340e4: r1 = Function 'computeMinIntrinsicWidth':.
    //     0x7340e4: add             x1, PP, #0x55, lsl #12  ; [pp+0x556f0] AnonymousClosure: (0x734040), in [package:flutter/src/rendering/stack.dart] RenderStack::computeMinIntrinsicWidth (0x733cfc)
    //     0x7340e8: ldr             x1, [x1, #0x6f0]
    // 0x7340ec: r0 = AllocateClosure()
    //     0x7340ec: bl              #0xec1630  ; AllocateClosureStub
    // 0x7340f0: LeaveFrame
    //     0x7340f0: mov             SP, fp
    //     0x7340f4: ldp             fp, lr, [SP], #0x10
    // 0x7340f8: ret
    //     0x7340f8: ret             
  }
  static _ _baselineForChild(/* No info */) {
    // ** addr: 0x743984, size: 0x220
    // 0x743984: EnterFrame
    //     0x743984: stp             fp, lr, [SP, #-0x10]!
    //     0x743988: mov             fp, SP
    // 0x74398c: AllocStack(0x38)
    //     0x74398c: sub             SP, SP, #0x38
    // 0x743990: SetupParameters(dynamic _ /* r1 => r7, fp-0x10 */, dynamic _ /* r2 => r6, fp-0x18 */, dynamic _ /* r3 => r5, fp-0x20 */, dynamic _ /* r5 => r4, fp-0x28 */, dynamic _ /* r6 => r3, fp-0x30 */)
    //     0x743990: mov             x7, x1
    //     0x743994: mov             x4, x5
    //     0x743998: stur            x5, [fp, #-0x28]
    //     0x74399c: mov             x5, x3
    //     0x7439a0: stur            x3, [fp, #-0x20]
    //     0x7439a4: mov             x3, x6
    //     0x7439a8: stur            x6, [fp, #-0x30]
    //     0x7439ac: mov             x6, x2
    //     0x7439b0: stur            x1, [fp, #-0x10]
    //     0x7439b4: stur            x2, [fp, #-0x18]
    // 0x7439b8: CheckStackOverflow
    //     0x7439b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7439bc: cmp             SP, x16
    //     0x7439c0: b.ls            #0x743b88
    // 0x7439c4: LoadField: r8 = r7->field_7
    //     0x7439c4: ldur            w8, [x7, #7]
    // 0x7439c8: DecompressPointer r8
    //     0x7439c8: add             x8, x8, HEAP, lsl #32
    // 0x7439cc: stur            x8, [fp, #-8]
    // 0x7439d0: cmp             w8, NULL
    // 0x7439d4: b.eq            #0x743b90
    // 0x7439d8: mov             x0, x8
    // 0x7439dc: r2 = Null
    //     0x7439dc: mov             x2, NULL
    // 0x7439e0: r1 = Null
    //     0x7439e0: mov             x1, NULL
    // 0x7439e4: r4 = LoadClassIdInstr(r0)
    //     0x7439e4: ldur            x4, [x0, #-1]
    //     0x7439e8: ubfx            x4, x4, #0xc, #0x14
    // 0x7439ec: sub             x4, x4, #0xc7a
    // 0x7439f0: cmp             x4, #2
    // 0x7439f4: b.ls            #0x743a0c
    // 0x7439f8: r8 = StackParentData
    //     0x7439f8: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0x7439fc: ldr             x8, [x8, #0x5c0]
    // 0x743a00: r3 = Null
    //     0x743a00: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e7b8] Null
    //     0x743a04: ldr             x3, [x3, #0x7b8]
    // 0x743a08: r0 = DefaultTypeTest()
    //     0x743a08: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x743a0c: ldur            x0, [fp, #-8]
    // 0x743a10: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x743a10: ldur            w1, [x0, #0x17]
    // 0x743a14: DecompressPointer r1
    //     0x743a14: add             x1, x1, HEAP, lsl #32
    // 0x743a18: cmp             w1, NULL
    // 0x743a1c: b.ne            #0x743a70
    // 0x743a20: LoadField: r1 = r0->field_1b
    //     0x743a20: ldur            w1, [x0, #0x1b]
    // 0x743a24: DecompressPointer r1
    //     0x743a24: add             x1, x1, HEAP, lsl #32
    // 0x743a28: cmp             w1, NULL
    // 0x743a2c: b.ne            #0x743a70
    // 0x743a30: LoadField: r1 = r0->field_1f
    //     0x743a30: ldur            w1, [x0, #0x1f]
    // 0x743a34: DecompressPointer r1
    //     0x743a34: add             x1, x1, HEAP, lsl #32
    // 0x743a38: cmp             w1, NULL
    // 0x743a3c: b.ne            #0x743a70
    // 0x743a40: LoadField: r1 = r0->field_23
    //     0x743a40: ldur            w1, [x0, #0x23]
    // 0x743a44: DecompressPointer r1
    //     0x743a44: add             x1, x1, HEAP, lsl #32
    // 0x743a48: cmp             w1, NULL
    // 0x743a4c: b.ne            #0x743a70
    // 0x743a50: LoadField: r1 = r0->field_27
    //     0x743a50: ldur            w1, [x0, #0x27]
    // 0x743a54: DecompressPointer r1
    //     0x743a54: add             x1, x1, HEAP, lsl #32
    // 0x743a58: cmp             w1, NULL
    // 0x743a5c: b.ne            #0x743a70
    // 0x743a60: LoadField: r1 = r0->field_2b
    //     0x743a60: ldur            w1, [x0, #0x2b]
    // 0x743a64: DecompressPointer r1
    //     0x743a64: add             x1, x1, HEAP, lsl #32
    // 0x743a68: cmp             w1, NULL
    // 0x743a6c: b.eq            #0x743a80
    // 0x743a70: mov             x1, x0
    // 0x743a74: ldur            x2, [fp, #-0x18]
    // 0x743a78: r0 = positionedChildConstraints()
    //     0x743a78: bl              #0x73c628  ; [package:flutter/src/rendering/stack.dart] StackParentData::positionedChildConstraints
    // 0x743a7c: b               #0x743a84
    // 0x743a80: ldur            x0, [fp, #-0x20]
    // 0x743a84: ldur            x1, [fp, #-0x10]
    // 0x743a88: mov             x2, x0
    // 0x743a8c: ldur            x3, [fp, #-0x30]
    // 0x743a90: stur            x0, [fp, #-0x20]
    // 0x743a94: r0 = getDryBaseline()
    //     0x743a94: bl              #0x730f54  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryBaseline
    // 0x743a98: stur            x0, [fp, #-0x30]
    // 0x743a9c: cmp             w0, NULL
    // 0x743aa0: b.ne            #0x743ab4
    // 0x743aa4: r0 = Null
    //     0x743aa4: mov             x0, NULL
    // 0x743aa8: LeaveFrame
    //     0x743aa8: mov             SP, fp
    //     0x743aac: ldp             fp, lr, [SP], #0x10
    // 0x743ab0: ret
    //     0x743ab0: ret             
    // 0x743ab4: ldur            x1, [fp, #-8]
    // 0x743ab8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x743ab8: ldur            w2, [x1, #0x17]
    // 0x743abc: DecompressPointer r2
    //     0x743abc: add             x2, x2, HEAP, lsl #32
    // 0x743ac0: cmp             w2, NULL
    // 0x743ac4: b.eq            #0x743ad4
    // 0x743ac8: LoadField: d0 = r2->field_7
    //     0x743ac8: ldur            d0, [x2, #7]
    // 0x743acc: mov             x1, x0
    // 0x743ad0: b               #0x743b4c
    // 0x743ad4: LoadField: r2 = r1->field_1f
    //     0x743ad4: ldur            w2, [x1, #0x1f]
    // 0x743ad8: DecompressPointer r2
    //     0x743ad8: add             x2, x2, HEAP, lsl #32
    // 0x743adc: cmp             w2, NULL
    // 0x743ae0: b.eq            #0x743b1c
    // 0x743ae4: ldur            x3, [fp, #-0x18]
    // 0x743ae8: LoadField: d0 = r3->field_f
    //     0x743ae8: ldur            d0, [x3, #0xf]
    // 0x743aec: LoadField: d1 = r2->field_7
    //     0x743aec: ldur            d1, [x2, #7]
    // 0x743af0: fsub            d2, d0, d1
    // 0x743af4: ldur            x1, [fp, #-0x10]
    // 0x743af8: ldur            x2, [fp, #-0x20]
    // 0x743afc: stur            d2, [fp, #-0x38]
    // 0x743b00: r0 = getDryLayout()
    //     0x743b00: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x743b04: LoadField: d0 = r0->field_f
    //     0x743b04: ldur            d0, [x0, #0xf]
    // 0x743b08: ldur            d1, [fp, #-0x38]
    // 0x743b0c: fsub            d2, d1, d0
    // 0x743b10: mov             v0.16b, v2.16b
    // 0x743b14: ldur            x1, [fp, #-0x30]
    // 0x743b18: b               #0x743b4c
    // 0x743b1c: ldur            x3, [fp, #-0x18]
    // 0x743b20: ldur            x1, [fp, #-0x10]
    // 0x743b24: ldur            x2, [fp, #-0x20]
    // 0x743b28: r0 = getDryLayout()
    //     0x743b28: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x743b2c: ldur            x1, [fp, #-0x18]
    // 0x743b30: mov             x2, x0
    // 0x743b34: r0 = -()
    //     0x743b34: bl              #0x618814  ; [dart:ui] Size::-
    // 0x743b38: ldur            x1, [fp, #-0x28]
    // 0x743b3c: mov             x2, x0
    // 0x743b40: r0 = alongOffset()
    //     0x743b40: bl              #0x73c5c8  ; [package:flutter/src/painting/alignment.dart] Alignment::alongOffset
    // 0x743b44: LoadField: d0 = r0->field_f
    //     0x743b44: ldur            d0, [x0, #0xf]
    // 0x743b48: ldur            x1, [fp, #-0x30]
    // 0x743b4c: LoadField: d1 = r1->field_7
    //     0x743b4c: ldur            d1, [x1, #7]
    // 0x743b50: fadd            d2, d1, d0
    // 0x743b54: r0 = inline_Allocate_Double()
    //     0x743b54: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x743b58: add             x0, x0, #0x10
    //     0x743b5c: cmp             x1, x0
    //     0x743b60: b.ls            #0x743b94
    //     0x743b64: str             x0, [THR, #0x50]  ; THR::top
    //     0x743b68: sub             x0, x0, #0xf
    //     0x743b6c: movz            x1, #0xe15c
    //     0x743b70: movk            x1, #0x3, lsl #16
    //     0x743b74: stur            x1, [x0, #-1]
    // 0x743b78: StoreField: r0->field_7 = d2
    //     0x743b78: stur            d2, [x0, #7]
    // 0x743b7c: LeaveFrame
    //     0x743b7c: mov             SP, fp
    //     0x743b80: ldp             fp, lr, [SP], #0x10
    // 0x743b84: ret
    //     0x743b84: ret             
    // 0x743b88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x743b88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x743b8c: b               #0x7439c4
    // 0x743b90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x743b90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x743b94: SaveReg d2
    //     0x743b94: str             q2, [SP, #-0x10]!
    // 0x743b98: r0 = AllocateDouble()
    //     0x743b98: bl              #0xec2254  ; AllocateDoubleStub
    // 0x743b9c: RestoreReg d2
    //     0x743b9c: ldr             q2, [SP], #0x10
    // 0x743ba0: b               #0x743b78
  }
  get _ _resolvedAlignment(/* No info */) {
    // ** addr: 0x743ba4, size: 0x174
    // 0x743ba4: EnterFrame
    //     0x743ba4: stp             fp, lr, [SP, #-0x10]!
    //     0x743ba8: mov             fp, SP
    // 0x743bac: AllocStack(0x18)
    //     0x743bac: sub             SP, SP, #0x18
    // 0x743bb0: SetupParameters(RenderStack this /* r1 => r1, fp-0x8 */)
    //     0x743bb0: stur            x1, [fp, #-8]
    // 0x743bb4: LoadField: r0 = r1->field_6b
    //     0x743bb4: ldur            w0, [x1, #0x6b]
    // 0x743bb8: DecompressPointer r0
    //     0x743bb8: add             x0, x0, HEAP, lsl #32
    // 0x743bbc: cmp             w0, NULL
    // 0x743bc0: b.ne            #0x743d04
    // 0x743bc4: LoadField: r0 = r1->field_6f
    //     0x743bc4: ldur            w0, [x1, #0x6f]
    // 0x743bc8: DecompressPointer r0
    //     0x743bc8: add             x0, x0, HEAP, lsl #32
    // 0x743bcc: LoadField: r2 = r1->field_73
    //     0x743bcc: ldur            w2, [x1, #0x73]
    // 0x743bd0: DecompressPointer r2
    //     0x743bd0: add             x2, x2, HEAP, lsl #32
    // 0x743bd4: r3 = LoadClassIdInstr(r0)
    //     0x743bd4: ldur            x3, [x0, #-1]
    //     0x743bd8: ubfx            x3, x3, #0xc, #0x14
    // 0x743bdc: cmp             x3, #0xcc8
    // 0x743be0: b.ne            #0x743c5c
    // 0x743be4: cmp             w2, NULL
    // 0x743be8: b.eq            #0x743d10
    // 0x743bec: LoadField: r3 = r2->field_7
    //     0x743bec: ldur            x3, [x2, #7]
    // 0x743bf0: cmp             x3, #0
    // 0x743bf4: b.gt            #0x743c28
    // 0x743bf8: LoadField: d0 = r0->field_7
    //     0x743bf8: ldur            d0, [x0, #7]
    // 0x743bfc: LoadField: d1 = r0->field_f
    //     0x743bfc: ldur            d1, [x0, #0xf]
    // 0x743c00: fsub            d2, d0, d1
    // 0x743c04: stur            d2, [fp, #-0x18]
    // 0x743c08: ArrayLoad: d0 = r0[0]  ; List_8
    //     0x743c08: ldur            d0, [x0, #0x17]
    // 0x743c0c: stur            d0, [fp, #-0x10]
    // 0x743c10: r0 = Alignment()
    //     0x743c10: bl              #0x65e000  ; AllocateAlignmentStub -> Alignment (size=0x18)
    // 0x743c14: ldur            d0, [fp, #-0x18]
    // 0x743c18: StoreField: r0->field_7 = d0
    //     0x743c18: stur            d0, [x0, #7]
    // 0x743c1c: ldur            d0, [fp, #-0x10]
    // 0x743c20: StoreField: r0->field_f = d0
    //     0x743c20: stur            d0, [x0, #0xf]
    // 0x743c24: b               #0x743c54
    // 0x743c28: LoadField: d0 = r0->field_7
    //     0x743c28: ldur            d0, [x0, #7]
    // 0x743c2c: LoadField: d1 = r0->field_f
    //     0x743c2c: ldur            d1, [x0, #0xf]
    // 0x743c30: fadd            d2, d0, d1
    // 0x743c34: stur            d2, [fp, #-0x18]
    // 0x743c38: ArrayLoad: d0 = r0[0]  ; List_8
    //     0x743c38: ldur            d0, [x0, #0x17]
    // 0x743c3c: stur            d0, [fp, #-0x10]
    // 0x743c40: r0 = Alignment()
    //     0x743c40: bl              #0x65e000  ; AllocateAlignmentStub -> Alignment (size=0x18)
    // 0x743c44: ldur            d0, [fp, #-0x18]
    // 0x743c48: StoreField: r0->field_7 = d0
    //     0x743c48: stur            d0, [x0, #7]
    // 0x743c4c: ldur            d0, [fp, #-0x10]
    // 0x743c50: StoreField: r0->field_f = d0
    //     0x743c50: stur            d0, [x0, #0xf]
    // 0x743c54: mov             x2, x0
    // 0x743c58: b               #0x743cdc
    // 0x743c5c: cmp             x3, #0xcc9
    // 0x743c60: b.ne            #0x743cd8
    // 0x743c64: cmp             w2, NULL
    // 0x743c68: b.eq            #0x743d14
    // 0x743c6c: LoadField: r1 = r2->field_7
    //     0x743c6c: ldur            x1, [x2, #7]
    // 0x743c70: cmp             x1, #0
    // 0x743c74: b.gt            #0x743ca8
    // 0x743c78: LoadField: d0 = r0->field_7
    //     0x743c78: ldur            d0, [x0, #7]
    // 0x743c7c: fneg            d1, d0
    // 0x743c80: stur            d1, [fp, #-0x18]
    // 0x743c84: LoadField: d0 = r0->field_f
    //     0x743c84: ldur            d0, [x0, #0xf]
    // 0x743c88: stur            d0, [fp, #-0x10]
    // 0x743c8c: r0 = Alignment()
    //     0x743c8c: bl              #0x65e000  ; AllocateAlignmentStub -> Alignment (size=0x18)
    // 0x743c90: ldur            d0, [fp, #-0x18]
    // 0x743c94: StoreField: r0->field_7 = d0
    //     0x743c94: stur            d0, [x0, #7]
    // 0x743c98: ldur            d0, [fp, #-0x10]
    // 0x743c9c: StoreField: r0->field_f = d0
    //     0x743c9c: stur            d0, [x0, #0xf]
    // 0x743ca0: mov             x1, x0
    // 0x743ca4: b               #0x743cd0
    // 0x743ca8: LoadField: d0 = r0->field_7
    //     0x743ca8: ldur            d0, [x0, #7]
    // 0x743cac: stur            d0, [fp, #-0x18]
    // 0x743cb0: LoadField: d1 = r0->field_f
    //     0x743cb0: ldur            d1, [x0, #0xf]
    // 0x743cb4: stur            d1, [fp, #-0x10]
    // 0x743cb8: r0 = Alignment()
    //     0x743cb8: bl              #0x65e000  ; AllocateAlignmentStub -> Alignment (size=0x18)
    // 0x743cbc: ldur            d0, [fp, #-0x18]
    // 0x743cc0: StoreField: r0->field_7 = d0
    //     0x743cc0: stur            d0, [x0, #7]
    // 0x743cc4: ldur            d0, [fp, #-0x10]
    // 0x743cc8: StoreField: r0->field_f = d0
    //     0x743cc8: stur            d0, [x0, #0xf]
    // 0x743ccc: mov             x1, x0
    // 0x743cd0: mov             x2, x1
    // 0x743cd4: b               #0x743cdc
    // 0x743cd8: mov             x2, x0
    // 0x743cdc: ldur            x1, [fp, #-8]
    // 0x743ce0: mov             x0, x2
    // 0x743ce4: StoreField: r1->field_6b = r0
    //     0x743ce4: stur            w0, [x1, #0x6b]
    //     0x743ce8: ldurb           w16, [x1, #-1]
    //     0x743cec: ldurb           w17, [x0, #-1]
    //     0x743cf0: and             x16, x17, x16, lsr #2
    //     0x743cf4: tst             x16, HEAP, lsr #32
    //     0x743cf8: b.eq            #0x743d00
    //     0x743cfc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x743d00: mov             x0, x2
    // 0x743d04: LeaveFrame
    //     0x743d04: mov             SP, fp
    //     0x743d08: ldp             fp, lr, [SP], #0x10
    // 0x743d0c: ret
    //     0x743d0c: ret             
    // 0x743d10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x743d10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x743d14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x743d14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ computeDryBaseline(/* No info */) {
    // ** addr: 0x743dd4, size: 0x2c8
    // 0x743dd4: EnterFrame
    //     0x743dd4: stp             fp, lr, [SP, #-0x10]!
    //     0x743dd8: mov             fp, SP
    // 0x743ddc: AllocStack(0x50)
    //     0x743ddc: sub             SP, SP, #0x50
    // 0x743de0: SetupParameters(RenderStack this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r6, fp-0x18 */)
    //     0x743de0: mov             x4, x1
    //     0x743de4: mov             x6, x3
    //     0x743de8: stur            x3, [fp, #-0x18]
    //     0x743dec: mov             x3, x2
    //     0x743df0: stur            x1, [fp, #-8]
    //     0x743df4: stur            x2, [fp, #-0x10]
    // 0x743df8: CheckStackOverflow
    //     0x743df8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x743dfc: cmp             SP, x16
    //     0x743e00: b.ls            #0x744074
    // 0x743e04: mov             x0, x3
    // 0x743e08: r2 = Null
    //     0x743e08: mov             x2, NULL
    // 0x743e0c: r1 = Null
    //     0x743e0c: mov             x1, NULL
    // 0x743e10: r4 = 60
    //     0x743e10: movz            x4, #0x3c
    // 0x743e14: branchIfSmi(r0, 0x743e20)
    //     0x743e14: tbz             w0, #0, #0x743e20
    // 0x743e18: r4 = LoadClassIdInstr(r0)
    //     0x743e18: ldur            x4, [x0, #-1]
    //     0x743e1c: ubfx            x4, x4, #0xc, #0x14
    // 0x743e20: sub             x4, x4, #0xc83
    // 0x743e24: cmp             x4, #1
    // 0x743e28: b.ls            #0x743e3c
    // 0x743e2c: r8 = BoxConstraints
    //     0x743e2c: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x743e30: r3 = Null
    //     0x743e30: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e798] Null
    //     0x743e34: ldr             x3, [x3, #0x798]
    // 0x743e38: r0 = BoxConstraints()
    //     0x743e38: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x743e3c: ldur            x0, [fp, #-8]
    // 0x743e40: LoadField: r1 = r0->field_77
    //     0x743e40: ldur            w1, [x0, #0x77]
    // 0x743e44: DecompressPointer r1
    //     0x743e44: add             x1, x1, HEAP, lsl #32
    // 0x743e48: LoadField: r2 = r1->field_7
    //     0x743e48: ldur            x2, [x1, #7]
    // 0x743e4c: cmp             x2, #1
    // 0x743e50: b.gt            #0x743eb0
    // 0x743e54: cmp             x2, #0
    // 0x743e58: b.gt            #0x743e6c
    // 0x743e5c: ldur            x1, [fp, #-0x10]
    // 0x743e60: r0 = loosen()
    //     0x743e60: bl              #0x73cf04  ; [package:flutter/src/rendering/box.dart] BoxConstraints::loosen
    // 0x743e64: mov             x3, x0
    // 0x743e68: b               #0x743eb4
    // 0x743e6c: ldur            x1, [fp, #-0x10]
    // 0x743e70: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x743e70: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x743e74: r0 = constrainWidth()
    //     0x743e74: bl              #0x6cea58  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainWidth
    // 0x743e78: ldur            x1, [fp, #-0x10]
    // 0x743e7c: stur            d0, [fp, #-0x48]
    // 0x743e80: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x743e80: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x743e84: r0 = constrainHeight()
    //     0x743e84: bl              #0x6ce9e4  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainHeight
    // 0x743e88: stur            d0, [fp, #-0x50]
    // 0x743e8c: r0 = BoxConstraints()
    //     0x743e8c: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x743e90: ldur            d0, [fp, #-0x48]
    // 0x743e94: StoreField: r0->field_7 = d0
    //     0x743e94: stur            d0, [x0, #7]
    // 0x743e98: StoreField: r0->field_f = d0
    //     0x743e98: stur            d0, [x0, #0xf]
    // 0x743e9c: ldur            d0, [fp, #-0x50]
    // 0x743ea0: ArrayStore: r0[0] = d0  ; List_8
    //     0x743ea0: stur            d0, [x0, #0x17]
    // 0x743ea4: StoreField: r0->field_1f = d0
    //     0x743ea4: stur            d0, [x0, #0x1f]
    // 0x743ea8: mov             x3, x0
    // 0x743eac: b               #0x743eb4
    // 0x743eb0: ldur            x3, [fp, #-0x10]
    // 0x743eb4: ldur            x0, [fp, #-8]
    // 0x743eb8: mov             x1, x0
    // 0x743ebc: stur            x3, [fp, #-0x20]
    // 0x743ec0: r0 = _resolvedAlignment()
    //     0x743ec0: bl              #0x743ba4  ; [package:flutter/src/rendering/stack.dart] RenderStack::_resolvedAlignment
    // 0x743ec4: ldur            x1, [fp, #-8]
    // 0x743ec8: ldur            x2, [fp, #-0x10]
    // 0x743ecc: stur            x0, [fp, #-0x10]
    // 0x743ed0: r0 = getDryLayout()
    //     0x743ed0: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x743ed4: mov             x4, x0
    // 0x743ed8: ldur            x0, [fp, #-8]
    // 0x743edc: stur            x4, [fp, #-0x30]
    // 0x743ee0: LoadField: r1 = r0->field_5f
    //     0x743ee0: ldur            w1, [x0, #0x5f]
    // 0x743ee4: DecompressPointer r1
    //     0x743ee4: add             x1, x1, HEAP, lsl #32
    // 0x743ee8: mov             x0, x1
    // 0x743eec: r7 = Null
    //     0x743eec: mov             x7, NULL
    // 0x743ef0: stur            x7, [fp, #-8]
    // 0x743ef4: stur            x0, [fp, #-0x28]
    // 0x743ef8: CheckStackOverflow
    //     0x743ef8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x743efc: cmp             SP, x16
    //     0x743f00: b.ls            #0x74407c
    // 0x743f04: cmp             w0, NULL
    // 0x743f08: b.eq            #0x744060
    // 0x743f0c: mov             x1, x0
    // 0x743f10: mov             x2, x4
    // 0x743f14: ldur            x3, [fp, #-0x20]
    // 0x743f18: ldur            x5, [fp, #-0x10]
    // 0x743f1c: ldur            x6, [fp, #-0x18]
    // 0x743f20: r0 = _baselineForChild()
    //     0x743f20: bl              #0x743984  ; [package:flutter/src/rendering/stack.dart] RenderStack::_baselineForChild
    // 0x743f24: mov             x2, x0
    // 0x743f28: ldur            x1, [fp, #-8]
    // 0x743f2c: cmp             w1, NULL
    // 0x743f30: b.eq            #0x743f90
    // 0x743f34: cmp             w2, NULL
    // 0x743f38: b.eq            #0x743f88
    // 0x743f3c: LoadField: d0 = r1->field_7
    //     0x743f3c: ldur            d0, [x1, #7]
    // 0x743f40: LoadField: d1 = r2->field_7
    //     0x743f40: ldur            d1, [x2, #7]
    // 0x743f44: fcmp            d0, d1
    // 0x743f48: b.lt            #0x743f54
    // 0x743f4c: LoadField: d0 = r2->field_7
    //     0x743f4c: ldur            d0, [x2, #7]
    // 0x743f50: b               #0x743f58
    // 0x743f54: LoadField: d0 = r1->field_7
    //     0x743f54: ldur            d0, [x1, #7]
    // 0x743f58: r1 = inline_Allocate_Double()
    //     0x743f58: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0x743f5c: add             x1, x1, #0x10
    //     0x743f60: cmp             x0, x1
    //     0x743f64: b.ls            #0x744084
    //     0x743f68: str             x1, [THR, #0x50]  ; THR::top
    //     0x743f6c: sub             x1, x1, #0xf
    //     0x743f70: movz            x0, #0xe15c
    //     0x743f74: movk            x0, #0x3, lsl #16
    //     0x743f78: stur            x0, [x1, #-1]
    // 0x743f7c: StoreField: r1->field_7 = d0
    //     0x743f7c: stur            d0, [x1, #7]
    // 0x743f80: mov             x7, x1
    // 0x743f84: b               #0x743ff8
    // 0x743f88: r3 = true
    //     0x743f88: add             x3, NULL, #0x20  ; true
    // 0x743f8c: b               #0x743f94
    // 0x743f90: r3 = false
    //     0x743f90: add             x3, NULL, #0x30  ; false
    // 0x743f94: cmp             w1, NULL
    // 0x743f98: b.eq            #0x743fcc
    // 0x743f9c: tbnz            w3, #4, #0x743fac
    // 0x743fa0: r0 = Null
    //     0x743fa0: mov             x0, NULL
    // 0x743fa4: r3 = Null
    //     0x743fa4: mov             x3, NULL
    // 0x743fa8: b               #0x743fb4
    // 0x743fac: mov             x0, x2
    // 0x743fb0: mov             x3, x2
    // 0x743fb4: cmp             w0, NULL
    // 0x743fb8: b.ne            #0x743fc4
    // 0x743fbc: mov             x7, x1
    // 0x743fc0: b               #0x743ff8
    // 0x743fc4: r0 = true
    //     0x743fc4: add             x0, NULL, #0x20  ; true
    // 0x743fc8: b               #0x743fd4
    // 0x743fcc: mov             x0, x3
    // 0x743fd0: r3 = Null
    //     0x743fd0: mov             x3, NULL
    // 0x743fd4: cmp             w1, NULL
    // 0x743fd8: b.ne            #0x743ff4
    // 0x743fdc: tbnz            w0, #4, #0x743fe8
    // 0x743fe0: mov             x1, x3
    // 0x743fe4: b               #0x743fec
    // 0x743fe8: mov             x1, x2
    // 0x743fec: mov             x7, x1
    // 0x743ff0: b               #0x743ff8
    // 0x743ff4: r7 = Null
    //     0x743ff4: mov             x7, NULL
    // 0x743ff8: ldur            x0, [fp, #-0x28]
    // 0x743ffc: stur            x7, [fp, #-0x40]
    // 0x744000: LoadField: r3 = r0->field_7
    //     0x744000: ldur            w3, [x0, #7]
    // 0x744004: DecompressPointer r3
    //     0x744004: add             x3, x3, HEAP, lsl #32
    // 0x744008: stur            x3, [fp, #-0x38]
    // 0x74400c: cmp             w3, NULL
    // 0x744010: b.eq            #0x744098
    // 0x744014: mov             x0, x3
    // 0x744018: r2 = Null
    //     0x744018: mov             x2, NULL
    // 0x74401c: r1 = Null
    //     0x74401c: mov             x1, NULL
    // 0x744020: r4 = LoadClassIdInstr(r0)
    //     0x744020: ldur            x4, [x0, #-1]
    //     0x744024: ubfx            x4, x4, #0xc, #0x14
    // 0x744028: sub             x4, x4, #0xc7a
    // 0x74402c: cmp             x4, #2
    // 0x744030: b.ls            #0x744048
    // 0x744034: r8 = StackParentData
    //     0x744034: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0x744038: ldr             x8, [x8, #0x5c0]
    // 0x74403c: r3 = Null
    //     0x74403c: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e7a8] Null
    //     0x744040: ldr             x3, [x3, #0x7a8]
    // 0x744044: r0 = DefaultTypeTest()
    //     0x744044: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x744048: ldur            x2, [fp, #-0x38]
    // 0x74404c: LoadField: r0 = r2->field_13
    //     0x74404c: ldur            w0, [x2, #0x13]
    // 0x744050: DecompressPointer r0
    //     0x744050: add             x0, x0, HEAP, lsl #32
    // 0x744054: ldur            x7, [fp, #-0x40]
    // 0x744058: ldur            x4, [fp, #-0x30]
    // 0x74405c: b               #0x743ef0
    // 0x744060: mov             x1, x7
    // 0x744064: mov             x0, x1
    // 0x744068: LeaveFrame
    //     0x744068: mov             SP, fp
    //     0x74406c: ldp             fp, lr, [SP], #0x10
    // 0x744070: ret
    //     0x744070: ret             
    // 0x744074: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x744074: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x744078: b               #0x743e04
    // 0x74407c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74407c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x744080: b               #0x743f04
    // 0x744084: SaveReg d0
    //     0x744084: str             q0, [SP, #-0x10]!
    // 0x744088: r0 = AllocateDouble()
    //     0x744088: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74408c: mov             x1, x0
    // 0x744090: RestoreReg d0
    //     0x744090: ldr             q0, [SP], #0x10
    // 0x744094: b               #0x743f7c
    // 0x744098: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x744098: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] double <anonymous closure>(dynamic, RenderBox) {
    // ** addr: 0x749db8, size: 0x80
    // 0x749db8: EnterFrame
    //     0x749db8: stp             fp, lr, [SP, #-0x10]!
    //     0x749dbc: mov             fp, SP
    // 0x749dc0: ldr             x0, [fp, #0x18]
    // 0x749dc4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x749dc4: ldur            w1, [x0, #0x17]
    // 0x749dc8: DecompressPointer r1
    //     0x749dc8: add             x1, x1, HEAP, lsl #32
    // 0x749dcc: CheckStackOverflow
    //     0x749dcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x749dd0: cmp             SP, x16
    //     0x749dd4: b.ls            #0x749e20
    // 0x749dd8: LoadField: r0 = r1->field_f
    //     0x749dd8: ldur            w0, [x1, #0xf]
    // 0x749ddc: DecompressPointer r0
    //     0x749ddc: add             x0, x0, HEAP, lsl #32
    // 0x749de0: LoadField: d0 = r0->field_7
    //     0x749de0: ldur            d0, [x0, #7]
    // 0x749de4: ldr             x1, [fp, #0x10]
    // 0x749de8: r0 = getMinIntrinsicHeight()
    //     0x749de8: bl              #0x73933c  ; [package:flutter/src/rendering/box.dart] RenderBox::getMinIntrinsicHeight
    // 0x749dec: r0 = inline_Allocate_Double()
    //     0x749dec: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x749df0: add             x0, x0, #0x10
    //     0x749df4: cmp             x1, x0
    //     0x749df8: b.ls            #0x749e28
    //     0x749dfc: str             x0, [THR, #0x50]  ; THR::top
    //     0x749e00: sub             x0, x0, #0xf
    //     0x749e04: movz            x1, #0xe15c
    //     0x749e08: movk            x1, #0x3, lsl #16
    //     0x749e0c: stur            x1, [x0, #-1]
    // 0x749e10: StoreField: r0->field_7 = d0
    //     0x749e10: stur            d0, [x0, #7]
    // 0x749e14: LeaveFrame
    //     0x749e14: mov             SP, fp
    //     0x749e18: ldp             fp, lr, [SP], #0x10
    // 0x749e1c: ret
    //     0x749e1c: ret             
    // 0x749e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x749e20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x749e24: b               #0x749dd8
    // 0x749e28: SaveReg d0
    //     0x749e28: str             q0, [SP, #-0x10]!
    // 0x749e2c: r0 = AllocateDouble()
    //     0x749e2c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x749e30: RestoreReg d0
    //     0x749e30: ldr             q0, [SP], #0x10
    // 0x749e34: b               #0x749e10
  }
  _ computeMinIntrinsicHeight(/* No info */) {
    // ** addr: 0x749e38, size: 0x74
    // 0x749e38: EnterFrame
    //     0x749e38: stp             fp, lr, [SP, #-0x10]!
    //     0x749e3c: mov             fp, SP
    // 0x749e40: AllocStack(0x10)
    //     0x749e40: sub             SP, SP, #0x10
    // 0x749e44: SetupParameters(RenderStack this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x749e44: stur            x1, [fp, #-8]
    //     0x749e48: stur            x2, [fp, #-0x10]
    // 0x749e4c: CheckStackOverflow
    //     0x749e4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x749e50: cmp             SP, x16
    //     0x749e54: b.ls            #0x749ea4
    // 0x749e58: r1 = 1
    //     0x749e58: movz            x1, #0x1
    // 0x749e5c: r0 = AllocateContext()
    //     0x749e5c: bl              #0xec126c  ; AllocateContextStub
    // 0x749e60: mov             x1, x0
    // 0x749e64: ldur            x0, [fp, #-0x10]
    // 0x749e68: StoreField: r1->field_f = r0
    //     0x749e68: stur            w0, [x1, #0xf]
    // 0x749e6c: ldur            x0, [fp, #-8]
    // 0x749e70: LoadField: r3 = r0->field_5f
    //     0x749e70: ldur            w3, [x0, #0x5f]
    // 0x749e74: DecompressPointer r3
    //     0x749e74: add             x3, x3, HEAP, lsl #32
    // 0x749e78: mov             x2, x1
    // 0x749e7c: stur            x3, [fp, #-0x10]
    // 0x749e80: r1 = Function '<anonymous closure>':.
    //     0x749e80: add             x1, PP, #0x55, lsl #12  ; [pp+0x556d8] AnonymousClosure: (0x749db8), in [package:flutter/src/rendering/stack.dart] RenderStack::computeMinIntrinsicHeight (0x749e38)
    //     0x749e84: ldr             x1, [x1, #0x6d8]
    // 0x749e88: r0 = AllocateClosure()
    //     0x749e88: bl              #0xec1630  ; AllocateClosureStub
    // 0x749e8c: ldur            x1, [fp, #-0x10]
    // 0x749e90: mov             x2, x0
    // 0x749e94: r0 = getIntrinsicDimension()
    //     0x749e94: bl              #0x733d70  ; [package:flutter/src/rendering/stack.dart] RenderStack::getIntrinsicDimension
    // 0x749e98: LeaveFrame
    //     0x749e98: mov             SP, fp
    //     0x749e9c: ldp             fp, lr, [SP], #0x10
    // 0x749ea0: ret
    //     0x749ea0: ret             
    // 0x749ea4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x749ea4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x749ea8: b               #0x749e58
  }
  [closure] double computeMinIntrinsicHeight(dynamic, double) {
    // ** addr: 0x749eac, size: 0x74
    // 0x749eac: EnterFrame
    //     0x749eac: stp             fp, lr, [SP, #-0x10]!
    //     0x749eb0: mov             fp, SP
    // 0x749eb4: ldr             x0, [fp, #0x18]
    // 0x749eb8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x749eb8: ldur            w1, [x0, #0x17]
    // 0x749ebc: DecompressPointer r1
    //     0x749ebc: add             x1, x1, HEAP, lsl #32
    // 0x749ec0: CheckStackOverflow
    //     0x749ec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x749ec4: cmp             SP, x16
    //     0x749ec8: b.ls            #0x749f08
    // 0x749ecc: ldr             x2, [fp, #0x10]
    // 0x749ed0: r0 = computeMinIntrinsicHeight()
    //     0x749ed0: bl              #0x749e38  ; [package:flutter/src/rendering/stack.dart] RenderStack::computeMinIntrinsicHeight
    // 0x749ed4: r0 = inline_Allocate_Double()
    //     0x749ed4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x749ed8: add             x0, x0, #0x10
    //     0x749edc: cmp             x1, x0
    //     0x749ee0: b.ls            #0x749f10
    //     0x749ee4: str             x0, [THR, #0x50]  ; THR::top
    //     0x749ee8: sub             x0, x0, #0xf
    //     0x749eec: movz            x1, #0xe15c
    //     0x749ef0: movk            x1, #0x3, lsl #16
    //     0x749ef4: stur            x1, [x0, #-1]
    // 0x749ef8: StoreField: r0->field_7 = d0
    //     0x749ef8: stur            d0, [x0, #7]
    // 0x749efc: LeaveFrame
    //     0x749efc: mov             SP, fp
    //     0x749f00: ldp             fp, lr, [SP], #0x10
    // 0x749f04: ret
    //     0x749f04: ret             
    // 0x749f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x749f08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x749f0c: b               #0x749ecc
    // 0x749f10: SaveReg d0
    //     0x749f10: str             q0, [SP, #-0x10]!
    // 0x749f14: r0 = AllocateDouble()
    //     0x749f14: bl              #0xec2254  ; AllocateDoubleStub
    // 0x749f18: RestoreReg d0
    //     0x749f18: ldr             q0, [SP], #0x10
    // 0x749f1c: b               #0x749ef8
  }
  dynamic computeMinIntrinsicHeight(dynamic) {
    // ** addr: 0x74a0d0, size: 0x24
    // 0x74a0d0: EnterFrame
    //     0x74a0d0: stp             fp, lr, [SP, #-0x10]!
    //     0x74a0d4: mov             fp, SP
    // 0x74a0d8: ldr             x2, [fp, #0x10]
    // 0x74a0dc: r1 = Function 'computeMinIntrinsicHeight':.
    //     0x74a0dc: add             x1, PP, #0x55, lsl #12  ; [pp+0x556d0] AnonymousClosure: (0x749eac), in [package:flutter/src/rendering/stack.dart] RenderStack::computeMinIntrinsicHeight (0x749e38)
    //     0x74a0e0: ldr             x1, [x1, #0x6d0]
    // 0x74a0e4: r0 = AllocateClosure()
    //     0x74a0e4: bl              #0xec1630  ; AllocateClosureStub
    // 0x74a0e8: LeaveFrame
    //     0x74a0e8: mov             SP, fp
    //     0x74a0ec: ldp             fp, lr, [SP], #0x10
    // 0x74a0f0: ret
    //     0x74a0f0: ret             
  }
  _ computeDistanceToActualBaseline(/* No info */) {
    // ** addr: 0x74c9fc, size: 0x2c
    // 0x74c9fc: EnterFrame
    //     0x74c9fc: stp             fp, lr, [SP, #-0x10]!
    //     0x74ca00: mov             fp, SP
    // 0x74ca04: CheckStackOverflow
    //     0x74ca04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74ca08: cmp             SP, x16
    //     0x74ca0c: b.ls            #0x74ca20
    // 0x74ca10: r0 = defaultComputeDistanceToHighestActualBaseline()
    //     0x74ca10: bl              #0x74ca28  ; [package:flutter/src/rendering/stack.dart] _RenderStack&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultComputeDistanceToHighestActualBaseline
    // 0x74ca14: LeaveFrame
    //     0x74ca14: mov             SP, fp
    //     0x74ca18: ldp             fp, lr, [SP], #0x10
    // 0x74ca1c: ret
    //     0x74ca1c: ret             
    // 0x74ca20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74ca20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74ca24: b               #0x74ca10
  }
  [closure] double <anonymous closure>(dynamic, RenderBox) {
    // ** addr: 0x74fe0c, size: 0x80
    // 0x74fe0c: EnterFrame
    //     0x74fe0c: stp             fp, lr, [SP, #-0x10]!
    //     0x74fe10: mov             fp, SP
    // 0x74fe14: ldr             x0, [fp, #0x18]
    // 0x74fe18: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x74fe18: ldur            w1, [x0, #0x17]
    // 0x74fe1c: DecompressPointer r1
    //     0x74fe1c: add             x1, x1, HEAP, lsl #32
    // 0x74fe20: CheckStackOverflow
    //     0x74fe20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74fe24: cmp             SP, x16
    //     0x74fe28: b.ls            #0x74fe74
    // 0x74fe2c: LoadField: r0 = r1->field_f
    //     0x74fe2c: ldur            w0, [x1, #0xf]
    // 0x74fe30: DecompressPointer r0
    //     0x74fe30: add             x0, x0, HEAP, lsl #32
    // 0x74fe34: LoadField: d0 = r0->field_7
    //     0x74fe34: ldur            d0, [x0, #7]
    // 0x74fe38: ldr             x1, [fp, #0x10]
    // 0x74fe3c: r0 = getMaxIntrinsicWidth()
    //     0x74fe3c: bl              #0x72d4b4  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicWidth
    // 0x74fe40: r0 = inline_Allocate_Double()
    //     0x74fe40: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74fe44: add             x0, x0, #0x10
    //     0x74fe48: cmp             x1, x0
    //     0x74fe4c: b.ls            #0x74fe7c
    //     0x74fe50: str             x0, [THR, #0x50]  ; THR::top
    //     0x74fe54: sub             x0, x0, #0xf
    //     0x74fe58: movz            x1, #0xe15c
    //     0x74fe5c: movk            x1, #0x3, lsl #16
    //     0x74fe60: stur            x1, [x0, #-1]
    // 0x74fe64: StoreField: r0->field_7 = d0
    //     0x74fe64: stur            d0, [x0, #7]
    // 0x74fe68: LeaveFrame
    //     0x74fe68: mov             SP, fp
    //     0x74fe6c: ldp             fp, lr, [SP], #0x10
    // 0x74fe70: ret
    //     0x74fe70: ret             
    // 0x74fe74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74fe74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74fe78: b               #0x74fe2c
    // 0x74fe7c: SaveReg d0
    //     0x74fe7c: str             q0, [SP, #-0x10]!
    // 0x74fe80: r0 = AllocateDouble()
    //     0x74fe80: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74fe84: RestoreReg d0
    //     0x74fe84: ldr             q0, [SP], #0x10
    // 0x74fe88: b               #0x74fe64
  }
  _ computeMaxIntrinsicWidth(/* No info */) {
    // ** addr: 0x74fe8c, size: 0x74
    // 0x74fe8c: EnterFrame
    //     0x74fe8c: stp             fp, lr, [SP, #-0x10]!
    //     0x74fe90: mov             fp, SP
    // 0x74fe94: AllocStack(0x10)
    //     0x74fe94: sub             SP, SP, #0x10
    // 0x74fe98: SetupParameters(RenderStack this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x74fe98: stur            x1, [fp, #-8]
    //     0x74fe9c: stur            x2, [fp, #-0x10]
    // 0x74fea0: CheckStackOverflow
    //     0x74fea0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74fea4: cmp             SP, x16
    //     0x74fea8: b.ls            #0x74fef8
    // 0x74feac: r1 = 1
    //     0x74feac: movz            x1, #0x1
    // 0x74feb0: r0 = AllocateContext()
    //     0x74feb0: bl              #0xec126c  ; AllocateContextStub
    // 0x74feb4: mov             x1, x0
    // 0x74feb8: ldur            x0, [fp, #-0x10]
    // 0x74febc: StoreField: r1->field_f = r0
    //     0x74febc: stur            w0, [x1, #0xf]
    // 0x74fec0: ldur            x0, [fp, #-8]
    // 0x74fec4: LoadField: r3 = r0->field_5f
    //     0x74fec4: ldur            w3, [x0, #0x5f]
    // 0x74fec8: DecompressPointer r3
    //     0x74fec8: add             x3, x3, HEAP, lsl #32
    // 0x74fecc: mov             x2, x1
    // 0x74fed0: stur            x3, [fp, #-0x10]
    // 0x74fed4: r1 = Function '<anonymous closure>':.
    //     0x74fed4: add             x1, PP, #0x55, lsl #12  ; [pp+0x556e8] AnonymousClosure: (0x74fe0c), in [package:flutter/src/rendering/stack.dart] RenderStack::computeMaxIntrinsicWidth (0x74fe8c)
    //     0x74fed8: ldr             x1, [x1, #0x6e8]
    // 0x74fedc: r0 = AllocateClosure()
    //     0x74fedc: bl              #0xec1630  ; AllocateClosureStub
    // 0x74fee0: ldur            x1, [fp, #-0x10]
    // 0x74fee4: mov             x2, x0
    // 0x74fee8: r0 = getIntrinsicDimension()
    //     0x74fee8: bl              #0x733d70  ; [package:flutter/src/rendering/stack.dart] RenderStack::getIntrinsicDimension
    // 0x74feec: LeaveFrame
    //     0x74feec: mov             SP, fp
    //     0x74fef0: ldp             fp, lr, [SP], #0x10
    // 0x74fef4: ret
    //     0x74fef4: ret             
    // 0x74fef8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74fef8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74fefc: b               #0x74feac
  }
  [closure] double computeMaxIntrinsicWidth(dynamic, double) {
    // ** addr: 0x74ff00, size: 0x74
    // 0x74ff00: EnterFrame
    //     0x74ff00: stp             fp, lr, [SP, #-0x10]!
    //     0x74ff04: mov             fp, SP
    // 0x74ff08: ldr             x0, [fp, #0x18]
    // 0x74ff0c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x74ff0c: ldur            w1, [x0, #0x17]
    // 0x74ff10: DecompressPointer r1
    //     0x74ff10: add             x1, x1, HEAP, lsl #32
    // 0x74ff14: CheckStackOverflow
    //     0x74ff14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74ff18: cmp             SP, x16
    //     0x74ff1c: b.ls            #0x74ff5c
    // 0x74ff20: ldr             x2, [fp, #0x10]
    // 0x74ff24: r0 = computeMaxIntrinsicWidth()
    //     0x74ff24: bl              #0x74fe8c  ; [package:flutter/src/rendering/stack.dart] RenderStack::computeMaxIntrinsicWidth
    // 0x74ff28: r0 = inline_Allocate_Double()
    //     0x74ff28: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74ff2c: add             x0, x0, #0x10
    //     0x74ff30: cmp             x1, x0
    //     0x74ff34: b.ls            #0x74ff64
    //     0x74ff38: str             x0, [THR, #0x50]  ; THR::top
    //     0x74ff3c: sub             x0, x0, #0xf
    //     0x74ff40: movz            x1, #0xe15c
    //     0x74ff44: movk            x1, #0x3, lsl #16
    //     0x74ff48: stur            x1, [x0, #-1]
    // 0x74ff4c: StoreField: r0->field_7 = d0
    //     0x74ff4c: stur            d0, [x0, #7]
    // 0x74ff50: LeaveFrame
    //     0x74ff50: mov             SP, fp
    //     0x74ff54: ldp             fp, lr, [SP], #0x10
    // 0x74ff58: ret
    //     0x74ff58: ret             
    // 0x74ff5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74ff5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74ff60: b               #0x74ff20
    // 0x74ff64: SaveReg d0
    //     0x74ff64: str             q0, [SP, #-0x10]!
    // 0x74ff68: r0 = AllocateDouble()
    //     0x74ff68: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74ff6c: RestoreReg d0
    //     0x74ff6c: ldr             q0, [SP], #0x10
    // 0x74ff70: b               #0x74ff4c
  }
  dynamic computeMaxIntrinsicWidth(dynamic) {
    // ** addr: 0x74ff98, size: 0x24
    // 0x74ff98: EnterFrame
    //     0x74ff98: stp             fp, lr, [SP, #-0x10]!
    //     0x74ff9c: mov             fp, SP
    // 0x74ffa0: ldr             x2, [fp, #0x10]
    // 0x74ffa4: r1 = Function 'computeMaxIntrinsicWidth':.
    //     0x74ffa4: add             x1, PP, #0x55, lsl #12  ; [pp+0x556e0] AnonymousClosure: (0x74ff00), in [package:flutter/src/rendering/stack.dart] RenderStack::computeMaxIntrinsicWidth (0x74fe8c)
    //     0x74ffa8: ldr             x1, [x1, #0x6e0]
    // 0x74ffac: r0 = AllocateClosure()
    //     0x74ffac: bl              #0xec1630  ; AllocateClosureStub
    // 0x74ffb0: LeaveFrame
    //     0x74ffb0: mov             SP, fp
    //     0x74ffb4: ldp             fp, lr, [SP], #0x10
    // 0x74ffb8: ret
    //     0x74ffb8: ret             
  }
  [closure] double <anonymous closure>(dynamic, RenderBox) {
    // ** addr: 0x7532bc, size: 0x80
    // 0x7532bc: EnterFrame
    //     0x7532bc: stp             fp, lr, [SP, #-0x10]!
    //     0x7532c0: mov             fp, SP
    // 0x7532c4: ldr             x0, [fp, #0x18]
    // 0x7532c8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7532c8: ldur            w1, [x0, #0x17]
    // 0x7532cc: DecompressPointer r1
    //     0x7532cc: add             x1, x1, HEAP, lsl #32
    // 0x7532d0: CheckStackOverflow
    //     0x7532d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7532d4: cmp             SP, x16
    //     0x7532d8: b.ls            #0x753324
    // 0x7532dc: LoadField: r0 = r1->field_f
    //     0x7532dc: ldur            w0, [x1, #0xf]
    // 0x7532e0: DecompressPointer r0
    //     0x7532e0: add             x0, x0, HEAP, lsl #32
    // 0x7532e4: LoadField: d0 = r0->field_7
    //     0x7532e4: ldur            d0, [x0, #7]
    // 0x7532e8: ldr             x1, [fp, #0x10]
    // 0x7532ec: r0 = getMaxIntrinsicHeight()
    //     0x7532ec: bl              #0x72d954  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicHeight
    // 0x7532f0: r0 = inline_Allocate_Double()
    //     0x7532f0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7532f4: add             x0, x0, #0x10
    //     0x7532f8: cmp             x1, x0
    //     0x7532fc: b.ls            #0x75332c
    //     0x753300: str             x0, [THR, #0x50]  ; THR::top
    //     0x753304: sub             x0, x0, #0xf
    //     0x753308: movz            x1, #0xe15c
    //     0x75330c: movk            x1, #0x3, lsl #16
    //     0x753310: stur            x1, [x0, #-1]
    // 0x753314: StoreField: r0->field_7 = d0
    //     0x753314: stur            d0, [x0, #7]
    // 0x753318: LeaveFrame
    //     0x753318: mov             SP, fp
    //     0x75331c: ldp             fp, lr, [SP], #0x10
    // 0x753320: ret
    //     0x753320: ret             
    // 0x753324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x753324: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x753328: b               #0x7532dc
    // 0x75332c: SaveReg d0
    //     0x75332c: str             q0, [SP, #-0x10]!
    // 0x753330: r0 = AllocateDouble()
    //     0x753330: bl              #0xec2254  ; AllocateDoubleStub
    // 0x753334: RestoreReg d0
    //     0x753334: ldr             q0, [SP], #0x10
    // 0x753338: b               #0x753314
  }
  _ computeMaxIntrinsicHeight(/* No info */) {
    // ** addr: 0x75333c, size: 0x74
    // 0x75333c: EnterFrame
    //     0x75333c: stp             fp, lr, [SP, #-0x10]!
    //     0x753340: mov             fp, SP
    // 0x753344: AllocStack(0x10)
    //     0x753344: sub             SP, SP, #0x10
    // 0x753348: SetupParameters(RenderStack this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x753348: stur            x1, [fp, #-8]
    //     0x75334c: stur            x2, [fp, #-0x10]
    // 0x753350: CheckStackOverflow
    //     0x753350: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x753354: cmp             SP, x16
    //     0x753358: b.ls            #0x7533a8
    // 0x75335c: r1 = 1
    //     0x75335c: movz            x1, #0x1
    // 0x753360: r0 = AllocateContext()
    //     0x753360: bl              #0xec126c  ; AllocateContextStub
    // 0x753364: mov             x1, x0
    // 0x753368: ldur            x0, [fp, #-0x10]
    // 0x75336c: StoreField: r1->field_f = r0
    //     0x75336c: stur            w0, [x1, #0xf]
    // 0x753370: ldur            x0, [fp, #-8]
    // 0x753374: LoadField: r3 = r0->field_5f
    //     0x753374: ldur            w3, [x0, #0x5f]
    // 0x753378: DecompressPointer r3
    //     0x753378: add             x3, x3, HEAP, lsl #32
    // 0x75337c: mov             x2, x1
    // 0x753380: stur            x3, [fp, #-0x10]
    // 0x753384: r1 = Function '<anonymous closure>':.
    //     0x753384: add             x1, PP, #0x55, lsl #12  ; [pp+0x556b8] AnonymousClosure: (0x7532bc), in [package:flutter/src/rendering/stack.dart] RenderStack::computeMaxIntrinsicHeight (0x75333c)
    //     0x753388: ldr             x1, [x1, #0x6b8]
    // 0x75338c: r0 = AllocateClosure()
    //     0x75338c: bl              #0xec1630  ; AllocateClosureStub
    // 0x753390: ldur            x1, [fp, #-0x10]
    // 0x753394: mov             x2, x0
    // 0x753398: r0 = getIntrinsicDimension()
    //     0x753398: bl              #0x733d70  ; [package:flutter/src/rendering/stack.dart] RenderStack::getIntrinsicDimension
    // 0x75339c: LeaveFrame
    //     0x75339c: mov             SP, fp
    //     0x7533a0: ldp             fp, lr, [SP], #0x10
    // 0x7533a4: ret
    //     0x7533a4: ret             
    // 0x7533a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7533a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7533ac: b               #0x75335c
  }
  [closure] double computeMaxIntrinsicHeight(dynamic, double) {
    // ** addr: 0x7533b0, size: 0x74
    // 0x7533b0: EnterFrame
    //     0x7533b0: stp             fp, lr, [SP, #-0x10]!
    //     0x7533b4: mov             fp, SP
    // 0x7533b8: ldr             x0, [fp, #0x18]
    // 0x7533bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7533bc: ldur            w1, [x0, #0x17]
    // 0x7533c0: DecompressPointer r1
    //     0x7533c0: add             x1, x1, HEAP, lsl #32
    // 0x7533c4: CheckStackOverflow
    //     0x7533c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7533c8: cmp             SP, x16
    //     0x7533cc: b.ls            #0x75340c
    // 0x7533d0: ldr             x2, [fp, #0x10]
    // 0x7533d4: r0 = computeMaxIntrinsicHeight()
    //     0x7533d4: bl              #0x75333c  ; [package:flutter/src/rendering/stack.dart] RenderStack::computeMaxIntrinsicHeight
    // 0x7533d8: r0 = inline_Allocate_Double()
    //     0x7533d8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7533dc: add             x0, x0, #0x10
    //     0x7533e0: cmp             x1, x0
    //     0x7533e4: b.ls            #0x753414
    //     0x7533e8: str             x0, [THR, #0x50]  ; THR::top
    //     0x7533ec: sub             x0, x0, #0xf
    //     0x7533f0: movz            x1, #0xe15c
    //     0x7533f4: movk            x1, #0x3, lsl #16
    //     0x7533f8: stur            x1, [x0, #-1]
    // 0x7533fc: StoreField: r0->field_7 = d0
    //     0x7533fc: stur            d0, [x0, #7]
    // 0x753400: LeaveFrame
    //     0x753400: mov             SP, fp
    //     0x753404: ldp             fp, lr, [SP], #0x10
    // 0x753408: ret
    //     0x753408: ret             
    // 0x75340c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75340c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x753410: b               #0x7533d0
    // 0x753414: SaveReg d0
    //     0x753414: str             q0, [SP, #-0x10]!
    // 0x753418: r0 = AllocateDouble()
    //     0x753418: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75341c: RestoreReg d0
    //     0x75341c: ldr             q0, [SP], #0x10
    // 0x753420: b               #0x7533fc
  }
  dynamic computeMaxIntrinsicHeight(dynamic) {
    // ** addr: 0x753448, size: 0x24
    // 0x753448: EnterFrame
    //     0x753448: stp             fp, lr, [SP, #-0x10]!
    //     0x75344c: mov             fp, SP
    // 0x753450: ldr             x2, [fp, #0x10]
    // 0x753454: r1 = Function 'computeMaxIntrinsicHeight':.
    //     0x753454: add             x1, PP, #0x55, lsl #12  ; [pp+0x556b0] AnonymousClosure: (0x7533b0), in [package:flutter/src/rendering/stack.dart] RenderStack::computeMaxIntrinsicHeight (0x75333c)
    //     0x753458: ldr             x1, [x1, #0x6b0]
    // 0x75345c: r0 = AllocateClosure()
    //     0x75345c: bl              #0xec1630  ; AllocateClosureStub
    // 0x753460: LeaveFrame
    //     0x753460: mov             SP, fp
    //     0x753464: ldp             fp, lr, [SP], #0x10
    // 0x753468: ret
    //     0x753468: ret             
  }
  _ computeDryLayout(/* No info */) {
    // ** addr: 0x755d30, size: 0x34
    // 0x755d30: EnterFrame
    //     0x755d30: stp             fp, lr, [SP, #-0x10]!
    //     0x755d34: mov             fp, SP
    // 0x755d38: CheckStackOverflow
    //     0x755d38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x755d3c: cmp             SP, x16
    //     0x755d40: b.ls            #0x755d5c
    // 0x755d44: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static.
    //     0x755d44: add             x3, PP, #0x44, lsl #12  ; [pp+0x44d20] Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static. (0x7e54fb140c14)
    //     0x755d48: ldr             x3, [x3, #0xd20]
    // 0x755d4c: r0 = _computeSize()
    //     0x755d4c: bl              #0x755d64  ; [package:flutter/src/rendering/stack.dart] RenderStack::_computeSize
    // 0x755d50: LeaveFrame
    //     0x755d50: mov             SP, fp
    //     0x755d54: ldp             fp, lr, [SP], #0x10
    // 0x755d58: ret
    //     0x755d58: ret             
    // 0x755d5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x755d5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x755d60: b               #0x755d44
  }
  _ _computeSize(/* No info */) {
    // ** addr: 0x755d64, size: 0x678
    // 0x755d64: EnterFrame
    //     0x755d64: stp             fp, lr, [SP, #-0x10]!
    //     0x755d68: mov             fp, SP
    // 0x755d6c: AllocStack(0x88)
    //     0x755d6c: sub             SP, SP, #0x88
    // 0x755d70: SetupParameters(RenderStack this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x755d70: mov             x0, x3
    //     0x755d74: stur            x3, [fp, #-0x18]
    //     0x755d78: mov             x3, x1
    //     0x755d7c: stur            x2, [fp, #-8]
    //     0x755d80: stur            x1, [fp, #-0x10]
    // 0x755d84: CheckStackOverflow
    //     0x755d84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x755d88: cmp             SP, x16
    //     0x755d8c: b.ls            #0x756334
    // 0x755d90: LoadField: r1 = r3->field_57
    //     0x755d90: ldur            x1, [x3, #0x57]
    // 0x755d94: cbnz            x1, #0x755e24
    // 0x755d98: mov             x1, x2
    // 0x755d9c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x755d9c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x755da0: r0 = constrainWidth()
    //     0x755da0: bl              #0x6cea58  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainWidth
    // 0x755da4: ldur            x1, [fp, #-8]
    // 0x755da8: stur            d0, [fp, #-0x58]
    // 0x755dac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x755dac: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x755db0: r0 = constrainHeight()
    //     0x755db0: bl              #0x6ce9e4  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainHeight
    // 0x755db4: stur            d0, [fp, #-0x60]
    // 0x755db8: r0 = Size()
    //     0x755db8: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x755dbc: ldur            d0, [fp, #-0x58]
    // 0x755dc0: StoreField: r0->field_7 = d0
    //     0x755dc0: stur            d0, [x0, #7]
    // 0x755dc4: ldur            d0, [fp, #-0x60]
    // 0x755dc8: StoreField: r0->field_f = d0
    //     0x755dc8: stur            d0, [x0, #0xf]
    // 0x755dcc: mov             x1, x0
    // 0x755dd0: r0 = isFinite()
    //     0x755dd0: bl              #0x744ba4  ; [dart:ui] OffsetBase::isFinite
    // 0x755dd4: tbnz            w0, #4, #0x755e10
    // 0x755dd8: ldur            x1, [fp, #-8]
    // 0x755ddc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x755ddc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x755de0: r0 = constrainWidth()
    //     0x755de0: bl              #0x6cea58  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainWidth
    // 0x755de4: ldur            x1, [fp, #-8]
    // 0x755de8: stur            d0, [fp, #-0x58]
    // 0x755dec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x755dec: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x755df0: r0 = constrainHeight()
    //     0x755df0: bl              #0x6ce9e4  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainHeight
    // 0x755df4: stur            d0, [fp, #-0x60]
    // 0x755df8: r0 = Size()
    //     0x755df8: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x755dfc: ldur            d0, [fp, #-0x58]
    // 0x755e00: StoreField: r0->field_7 = d0
    //     0x755e00: stur            d0, [x0, #7]
    // 0x755e04: ldur            d0, [fp, #-0x60]
    // 0x755e08: StoreField: r0->field_f = d0
    //     0x755e08: stur            d0, [x0, #0xf]
    // 0x755e0c: b               #0x755e18
    // 0x755e10: ldur            x1, [fp, #-8]
    // 0x755e14: r0 = smallest()
    //     0x755e14: bl              #0x7335c0  ; [package:flutter/src/rendering/box.dart] BoxConstraints::smallest
    // 0x755e18: LeaveFrame
    //     0x755e18: mov             SP, fp
    //     0x755e1c: ldp             fp, lr, [SP], #0x10
    // 0x755e20: ret
    //     0x755e20: ret             
    // 0x755e24: LoadField: d0 = r2->field_7
    //     0x755e24: ldur            d0, [x2, #7]
    // 0x755e28: stur            d0, [fp, #-0x60]
    // 0x755e2c: ArrayLoad: d1 = r2[0]  ; List_8
    //     0x755e2c: ldur            d1, [x2, #0x17]
    // 0x755e30: stur            d1, [fp, #-0x58]
    // 0x755e34: LoadField: r1 = r3->field_77
    //     0x755e34: ldur            w1, [x3, #0x77]
    // 0x755e38: DecompressPointer r1
    //     0x755e38: add             x1, x1, HEAP, lsl #32
    // 0x755e3c: LoadField: r4 = r1->field_7
    //     0x755e3c: ldur            x4, [x1, #7]
    // 0x755e40: cmp             x4, #1
    // 0x755e44: b.gt            #0x755ea4
    // 0x755e48: cmp             x4, #0
    // 0x755e4c: b.gt            #0x755e60
    // 0x755e50: mov             x1, x2
    // 0x755e54: r0 = loosen()
    //     0x755e54: bl              #0x73cf04  ; [package:flutter/src/rendering/box.dart] BoxConstraints::loosen
    // 0x755e58: mov             x3, x0
    // 0x755e5c: b               #0x755ea8
    // 0x755e60: ldur            x1, [fp, #-8]
    // 0x755e64: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x755e64: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x755e68: r0 = constrainWidth()
    //     0x755e68: bl              #0x6cea58  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainWidth
    // 0x755e6c: ldur            x1, [fp, #-8]
    // 0x755e70: stur            d0, [fp, #-0x68]
    // 0x755e74: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x755e74: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x755e78: r0 = constrainHeight()
    //     0x755e78: bl              #0x6ce9e4  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainHeight
    // 0x755e7c: stur            d0, [fp, #-0x70]
    // 0x755e80: r0 = BoxConstraints()
    //     0x755e80: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x755e84: ldur            d0, [fp, #-0x68]
    // 0x755e88: StoreField: r0->field_7 = d0
    //     0x755e88: stur            d0, [x0, #7]
    // 0x755e8c: StoreField: r0->field_f = d0
    //     0x755e8c: stur            d0, [x0, #0xf]
    // 0x755e90: ldur            d0, [fp, #-0x70]
    // 0x755e94: ArrayStore: r0[0] = d0  ; List_8
    //     0x755e94: stur            d0, [x0, #0x17]
    // 0x755e98: StoreField: r0->field_1f = d0
    //     0x755e98: stur            d0, [x0, #0x1f]
    // 0x755e9c: mov             x3, x0
    // 0x755ea0: b               #0x755ea8
    // 0x755ea4: ldur            x3, [fp, #-8]
    // 0x755ea8: ldur            x0, [fp, #-0x10]
    // 0x755eac: ldur            d0, [fp, #-0x60]
    // 0x755eb0: ldur            d1, [fp, #-0x58]
    // 0x755eb4: stur            x3, [fp, #-0x40]
    // 0x755eb8: LoadField: r1 = r0->field_5f
    //     0x755eb8: ldur            w1, [x0, #0x5f]
    // 0x755ebc: DecompressPointer r1
    //     0x755ebc: add             x1, x1, HEAP, lsl #32
    // 0x755ec0: r0 = inline_Allocate_Double()
    //     0x755ec0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x755ec4: add             x0, x0, #0x10
    //     0x755ec8: cmp             x2, x0
    //     0x755ecc: b.ls            #0x75633c
    //     0x755ed0: str             x0, [THR, #0x50]  ; THR::top
    //     0x755ed4: sub             x0, x0, #0xf
    //     0x755ed8: movz            x2, #0xe15c
    //     0x755edc: movk            x2, #0x3, lsl #16
    //     0x755ee0: stur            x2, [x0, #-1]
    // 0x755ee4: StoreField: r0->field_7 = d0
    //     0x755ee4: stur            d0, [x0, #7]
    // 0x755ee8: r2 = inline_Allocate_Double()
    //     0x755ee8: ldp             x2, x4, [THR, #0x50]  ; THR::top
    //     0x755eec: add             x2, x2, #0x10
    //     0x755ef0: cmp             x4, x2
    //     0x755ef4: b.ls            #0x756354
    //     0x755ef8: str             x2, [THR, #0x50]  ; THR::top
    //     0x755efc: sub             x2, x2, #0xf
    //     0x755f00: movz            x4, #0xe15c
    //     0x755f04: movk            x4, #0x3, lsl #16
    //     0x755f08: stur            x4, [x2, #-1]
    // 0x755f0c: StoreField: r2->field_7 = d1
    //     0x755f0c: stur            d1, [x2, #7]
    // 0x755f10: mov             x6, x0
    // 0x755f14: mov             x5, x2
    // 0x755f18: mov             x4, x1
    // 0x755f1c: r7 = false
    //     0x755f1c: add             x7, NULL, #0x30  ; false
    // 0x755f20: stur            x7, [fp, #-0x20]
    // 0x755f24: stur            x6, [fp, #-0x28]
    // 0x755f28: stur            x5, [fp, #-0x30]
    // 0x755f2c: stur            x4, [fp, #-0x38]
    // 0x755f30: CheckStackOverflow
    //     0x755f30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x755f34: cmp             SP, x16
    //     0x755f38: b.ls            #0x756378
    // 0x755f3c: cmp             w4, NULL
    // 0x755f40: b.eq            #0x7562c0
    // 0x755f44: LoadField: r8 = r4->field_7
    //     0x755f44: ldur            w8, [x4, #7]
    // 0x755f48: DecompressPointer r8
    //     0x755f48: add             x8, x8, HEAP, lsl #32
    // 0x755f4c: stur            x8, [fp, #-0x10]
    // 0x755f50: cmp             w8, NULL
    // 0x755f54: b.eq            #0x756380
    // 0x755f58: mov             x0, x8
    // 0x755f5c: r2 = Null
    //     0x755f5c: mov             x2, NULL
    // 0x755f60: r1 = Null
    //     0x755f60: mov             x1, NULL
    // 0x755f64: r4 = LoadClassIdInstr(r0)
    //     0x755f64: ldur            x4, [x0, #-1]
    //     0x755f68: ubfx            x4, x4, #0xc, #0x14
    // 0x755f6c: sub             x4, x4, #0xc7a
    // 0x755f70: cmp             x4, #2
    // 0x755f74: b.ls            #0x755f8c
    // 0x755f78: r8 = StackParentData
    //     0x755f78: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0x755f7c: ldr             x8, [x8, #0x5c0]
    // 0x755f80: r3 = Null
    //     0x755f80: add             x3, PP, #0x44, lsl #12  ; [pp+0x44d10] Null
    //     0x755f84: ldr             x3, [x3, #0xd10]
    // 0x755f88: r0 = DefaultTypeTest()
    //     0x755f88: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x755f8c: ldur            x1, [fp, #-0x10]
    // 0x755f90: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x755f90: ldur            w0, [x1, #0x17]
    // 0x755f94: DecompressPointer r0
    //     0x755f94: add             x0, x0, HEAP, lsl #32
    // 0x755f98: cmp             w0, NULL
    // 0x755f9c: b.ne            #0x755fe0
    // 0x755fa0: LoadField: r0 = r1->field_1b
    //     0x755fa0: ldur            w0, [x1, #0x1b]
    // 0x755fa4: DecompressPointer r0
    //     0x755fa4: add             x0, x0, HEAP, lsl #32
    // 0x755fa8: cmp             w0, NULL
    // 0x755fac: b.ne            #0x755fe0
    // 0x755fb0: LoadField: r0 = r1->field_1f
    //     0x755fb0: ldur            w0, [x1, #0x1f]
    // 0x755fb4: DecompressPointer r0
    //     0x755fb4: add             x0, x0, HEAP, lsl #32
    // 0x755fb8: cmp             w0, NULL
    // 0x755fbc: b.ne            #0x755fe0
    // 0x755fc0: LoadField: r0 = r1->field_23
    //     0x755fc0: ldur            w0, [x1, #0x23]
    // 0x755fc4: DecompressPointer r0
    //     0x755fc4: add             x0, x0, HEAP, lsl #32
    // 0x755fc8: cmp             w0, NULL
    // 0x755fcc: b.ne            #0x755fe0
    // 0x755fd0: LoadField: r0 = r1->field_27
    //     0x755fd0: ldur            w0, [x1, #0x27]
    // 0x755fd4: DecompressPointer r0
    //     0x755fd4: add             x0, x0, HEAP, lsl #32
    // 0x755fd8: cmp             w0, NULL
    // 0x755fdc: b.eq            #0x755ff0
    // 0x755fe0: ldur            x0, [fp, #-0x28]
    // 0x755fe4: ldur            x1, [fp, #-0x30]
    // 0x755fe8: d0 = 0.000000
    //     0x755fe8: eor             v0.16b, v0.16b, v0.16b
    // 0x755fec: b               #0x7562a0
    // 0x755ff0: LoadField: r0 = r1->field_2b
    //     0x755ff0: ldur            w0, [x1, #0x2b]
    // 0x755ff4: DecompressPointer r0
    //     0x755ff4: add             x0, x0, HEAP, lsl #32
    // 0x755ff8: cmp             w0, NULL
    // 0x755ffc: b.ne            #0x756294
    // 0x756000: ldur            x2, [fp, #-0x28]
    // 0x756004: ldur            x16, [fp, #-0x18]
    // 0x756008: ldur            lr, [fp, #-0x38]
    // 0x75600c: stp             lr, x16, [SP, #8]
    // 0x756010: ldur            x16, [fp, #-0x40]
    // 0x756014: str             x16, [SP]
    // 0x756018: ldur            x0, [fp, #-0x18]
    // 0x75601c: ClosureCall
    //     0x75601c: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x756020: ldur            x2, [x0, #0x1f]
    //     0x756024: blr             x2
    // 0x756028: mov             x1, x0
    // 0x75602c: stur            x1, [fp, #-0x48]
    // 0x756030: LoadField: d0 = r1->field_7
    //     0x756030: ldur            d0, [x1, #7]
    // 0x756034: stur            d0, [fp, #-0x58]
    // 0x756038: r2 = inline_Allocate_Double()
    //     0x756038: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x75603c: add             x2, x2, #0x10
    //     0x756040: cmp             x0, x2
    //     0x756044: b.ls            #0x756384
    //     0x756048: str             x2, [THR, #0x50]  ; THR::top
    //     0x75604c: sub             x2, x2, #0xf
    //     0x756050: movz            x0, #0xe15c
    //     0x756054: movk            x0, #0x3, lsl #16
    //     0x756058: stur            x0, [x2, #-1]
    // 0x75605c: StoreField: r2->field_7 = d0
    //     0x75605c: stur            d0, [x2, #7]
    // 0x756060: ldur            x3, [fp, #-0x28]
    // 0x756064: stur            x2, [fp, #-0x38]
    // 0x756068: r0 = 60
    //     0x756068: movz            x0, #0x3c
    // 0x75606c: branchIfSmi(r3, 0x756078)
    //     0x75606c: tbz             w3, #0, #0x756078
    // 0x756070: r0 = LoadClassIdInstr(r3)
    //     0x756070: ldur            x0, [x3, #-1]
    //     0x756074: ubfx            x0, x0, #0xc, #0x14
    // 0x756078: stp             x2, x3, [SP]
    // 0x75607c: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x75607c: sub             lr, x0, #0xfe3
    //     0x756080: ldr             lr, [x21, lr, lsl #3]
    //     0x756084: blr             lr
    // 0x756088: tbnz            w0, #4, #0x756098
    // 0x75608c: ldur            x2, [fp, #-0x28]
    // 0x756090: d0 = 0.000000
    //     0x756090: eor             v0.16b, v0.16b, v0.16b
    // 0x756094: b               #0x756158
    // 0x756098: ldur            x1, [fp, #-0x28]
    // 0x75609c: r0 = 60
    //     0x75609c: movz            x0, #0x3c
    // 0x7560a0: branchIfSmi(r1, 0x7560ac)
    //     0x7560a0: tbz             w1, #0, #0x7560ac
    // 0x7560a4: r0 = LoadClassIdInstr(r1)
    //     0x7560a4: ldur            x0, [x1, #-1]
    //     0x7560a8: ubfx            x0, x0, #0xc, #0x14
    // 0x7560ac: ldur            x16, [fp, #-0x38]
    // 0x7560b0: stp             x16, x1, [SP]
    // 0x7560b4: r0 = GDT[cid_x0 + -0xfd2]()
    //     0x7560b4: sub             lr, x0, #0xfd2
    //     0x7560b8: ldr             lr, [x21, lr, lsl #3]
    //     0x7560bc: blr             lr
    // 0x7560c0: tbnz            w0, #4, #0x7560d0
    // 0x7560c4: ldur            x2, [fp, #-0x38]
    // 0x7560c8: d0 = 0.000000
    //     0x7560c8: eor             v0.16b, v0.16b, v0.16b
    // 0x7560cc: b               #0x756158
    // 0x7560d0: ldur            x0, [fp, #-0x28]
    // 0x7560d4: r1 = 60
    //     0x7560d4: movz            x1, #0x3c
    // 0x7560d8: branchIfSmi(r0, 0x7560e4)
    //     0x7560d8: tbz             w0, #0, #0x7560e4
    // 0x7560dc: r1 = LoadClassIdInstr(r0)
    //     0x7560dc: ldur            x1, [x0, #-1]
    //     0x7560e0: ubfx            x1, x1, #0xc, #0x14
    // 0x7560e4: cmp             x1, #0x3e
    // 0x7560e8: b.ne            #0x75613c
    // 0x7560ec: d0 = 0.000000
    //     0x7560ec: eor             v0.16b, v0.16b, v0.16b
    // 0x7560f0: LoadField: d1 = r0->field_7
    //     0x7560f0: ldur            d1, [x0, #7]
    // 0x7560f4: fcmp            d1, d0
    // 0x7560f8: b.ne            #0x756134
    // 0x7560fc: ldur            d2, [fp, #-0x58]
    // 0x756100: fadd            d3, d1, d2
    // 0x756104: r0 = inline_Allocate_Double()
    //     0x756104: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x756108: add             x0, x0, #0x10
    //     0x75610c: cmp             x1, x0
    //     0x756110: b.ls            #0x7563a0
    //     0x756114: str             x0, [THR, #0x50]  ; THR::top
    //     0x756118: sub             x0, x0, #0xf
    //     0x75611c: movz            x1, #0xe15c
    //     0x756120: movk            x1, #0x3, lsl #16
    //     0x756124: stur            x1, [x0, #-1]
    // 0x756128: StoreField: r0->field_7 = d3
    //     0x756128: stur            d3, [x0, #7]
    // 0x75612c: mov             x2, x0
    // 0x756130: b               #0x756158
    // 0x756134: ldur            d2, [fp, #-0x58]
    // 0x756138: b               #0x756144
    // 0x75613c: ldur            d2, [fp, #-0x58]
    // 0x756140: d0 = 0.000000
    //     0x756140: eor             v0.16b, v0.16b, v0.16b
    // 0x756144: fcmp            d2, d2
    // 0x756148: b.vc            #0x756154
    // 0x75614c: ldur            x2, [fp, #-0x38]
    // 0x756150: b               #0x756158
    // 0x756154: mov             x2, x0
    // 0x756158: ldur            x1, [fp, #-0x30]
    // 0x75615c: ldur            x0, [fp, #-0x48]
    // 0x756160: stur            x2, [fp, #-0x50]
    // 0x756164: LoadField: d1 = r0->field_f
    //     0x756164: ldur            d1, [x0, #0xf]
    // 0x756168: stur            d1, [fp, #-0x58]
    // 0x75616c: r3 = inline_Allocate_Double()
    //     0x75616c: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x756170: add             x3, x3, #0x10
    //     0x756174: cmp             x0, x3
    //     0x756178: b.ls            #0x7563b0
    //     0x75617c: str             x3, [THR, #0x50]  ; THR::top
    //     0x756180: sub             x3, x3, #0xf
    //     0x756184: movz            x0, #0xe15c
    //     0x756188: movk            x0, #0x3, lsl #16
    //     0x75618c: stur            x0, [x3, #-1]
    // 0x756190: StoreField: r3->field_7 = d1
    //     0x756190: stur            d1, [x3, #7]
    // 0x756194: stur            x3, [fp, #-0x38]
    // 0x756198: r0 = 60
    //     0x756198: movz            x0, #0x3c
    // 0x75619c: branchIfSmi(r1, 0x7561a8)
    //     0x75619c: tbz             w1, #0, #0x7561a8
    // 0x7561a0: r0 = LoadClassIdInstr(r1)
    //     0x7561a0: ldur            x0, [x1, #-1]
    //     0x7561a4: ubfx            x0, x0, #0xc, #0x14
    // 0x7561a8: stp             x3, x1, [SP]
    // 0x7561ac: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x7561ac: sub             lr, x0, #0xfe3
    //     0x7561b0: ldr             lr, [x21, lr, lsl #3]
    //     0x7561b4: blr             lr
    // 0x7561b8: tbnz            w0, #4, #0x7561c8
    // 0x7561bc: ldur            x0, [fp, #-0x30]
    // 0x7561c0: d0 = 0.000000
    //     0x7561c0: eor             v0.16b, v0.16b, v0.16b
    // 0x7561c4: b               #0x756284
    // 0x7561c8: ldur            x1, [fp, #-0x30]
    // 0x7561cc: r0 = 60
    //     0x7561cc: movz            x0, #0x3c
    // 0x7561d0: branchIfSmi(r1, 0x7561dc)
    //     0x7561d0: tbz             w1, #0, #0x7561dc
    // 0x7561d4: r0 = LoadClassIdInstr(r1)
    //     0x7561d4: ldur            x0, [x1, #-1]
    //     0x7561d8: ubfx            x0, x0, #0xc, #0x14
    // 0x7561dc: ldur            x16, [fp, #-0x38]
    // 0x7561e0: stp             x16, x1, [SP]
    // 0x7561e4: r0 = GDT[cid_x0 + -0xfd2]()
    //     0x7561e4: sub             lr, x0, #0xfd2
    //     0x7561e8: ldr             lr, [x21, lr, lsl #3]
    //     0x7561ec: blr             lr
    // 0x7561f0: tbnz            w0, #4, #0x756200
    // 0x7561f4: ldur            x0, [fp, #-0x38]
    // 0x7561f8: d0 = 0.000000
    //     0x7561f8: eor             v0.16b, v0.16b, v0.16b
    // 0x7561fc: b               #0x756284
    // 0x756200: ldur            x1, [fp, #-0x30]
    // 0x756204: r0 = 60
    //     0x756204: movz            x0, #0x3c
    // 0x756208: branchIfSmi(r1, 0x756214)
    //     0x756208: tbz             w1, #0, #0x756214
    // 0x75620c: r0 = LoadClassIdInstr(r1)
    //     0x75620c: ldur            x0, [x1, #-1]
    //     0x756210: ubfx            x0, x0, #0xc, #0x14
    // 0x756214: cmp             x0, #0x3e
    // 0x756218: b.ne            #0x756268
    // 0x75621c: d0 = 0.000000
    //     0x75621c: eor             v0.16b, v0.16b, v0.16b
    // 0x756220: LoadField: d1 = r1->field_7
    //     0x756220: ldur            d1, [x1, #7]
    // 0x756224: fcmp            d1, d0
    // 0x756228: b.ne            #0x756260
    // 0x75622c: ldur            d2, [fp, #-0x58]
    // 0x756230: fadd            d3, d1, d2
    // 0x756234: r0 = inline_Allocate_Double()
    //     0x756234: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x756238: add             x0, x0, #0x10
    //     0x75623c: cmp             x1, x0
    //     0x756240: b.ls            #0x7563cc
    //     0x756244: str             x0, [THR, #0x50]  ; THR::top
    //     0x756248: sub             x0, x0, #0xf
    //     0x75624c: movz            x1, #0xe15c
    //     0x756250: movk            x1, #0x3, lsl #16
    //     0x756254: stur            x1, [x0, #-1]
    // 0x756258: StoreField: r0->field_7 = d3
    //     0x756258: stur            d3, [x0, #7]
    // 0x75625c: b               #0x756284
    // 0x756260: ldur            d2, [fp, #-0x58]
    // 0x756264: b               #0x756270
    // 0x756268: ldur            d2, [fp, #-0x58]
    // 0x75626c: d0 = 0.000000
    //     0x75626c: eor             v0.16b, v0.16b, v0.16b
    // 0x756270: fcmp            d2, d2
    // 0x756274: b.vc            #0x756280
    // 0x756278: ldur            x0, [fp, #-0x38]
    // 0x75627c: b               #0x756284
    // 0x756280: mov             x0, x1
    // 0x756284: ldur            x6, [fp, #-0x50]
    // 0x756288: mov             x5, x0
    // 0x75628c: r7 = true
    //     0x75628c: add             x7, NULL, #0x20  ; true
    // 0x756290: b               #0x7562ac
    // 0x756294: ldur            x0, [fp, #-0x28]
    // 0x756298: ldur            x1, [fp, #-0x30]
    // 0x75629c: d0 = 0.000000
    //     0x75629c: eor             v0.16b, v0.16b, v0.16b
    // 0x7562a0: ldur            x7, [fp, #-0x20]
    // 0x7562a4: mov             x6, x0
    // 0x7562a8: mov             x5, x1
    // 0x7562ac: ldur            x0, [fp, #-0x10]
    // 0x7562b0: LoadField: r4 = r0->field_13
    //     0x7562b0: ldur            w4, [x0, #0x13]
    // 0x7562b4: DecompressPointer r4
    //     0x7562b4: add             x4, x4, HEAP, lsl #32
    // 0x7562b8: ldur            x3, [fp, #-0x40]
    // 0x7562bc: b               #0x755f20
    // 0x7562c0: mov             x2, x7
    // 0x7562c4: mov             x0, x6
    // 0x7562c8: mov             x1, x5
    // 0x7562cc: tbnz            w2, #4, #0x7562f4
    // 0x7562d0: LoadField: d0 = r0->field_7
    //     0x7562d0: ldur            d0, [x0, #7]
    // 0x7562d4: stur            d0, [fp, #-0x58]
    // 0x7562d8: r0 = Size()
    //     0x7562d8: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x7562dc: ldur            d0, [fp, #-0x58]
    // 0x7562e0: StoreField: r0->field_7 = d0
    //     0x7562e0: stur            d0, [x0, #7]
    // 0x7562e4: ldur            x1, [fp, #-0x30]
    // 0x7562e8: LoadField: d0 = r1->field_7
    //     0x7562e8: ldur            d0, [x1, #7]
    // 0x7562ec: StoreField: r0->field_f = d0
    //     0x7562ec: stur            d0, [x0, #0xf]
    // 0x7562f0: b               #0x756328
    // 0x7562f4: ldur            x1, [fp, #-8]
    // 0x7562f8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7562f8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7562fc: r0 = constrainWidth()
    //     0x7562fc: bl              #0x6cea58  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainWidth
    // 0x756300: ldur            x1, [fp, #-8]
    // 0x756304: stur            d0, [fp, #-0x58]
    // 0x756308: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x756308: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x75630c: r0 = constrainHeight()
    //     0x75630c: bl              #0x6ce9e4  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainHeight
    // 0x756310: stur            d0, [fp, #-0x60]
    // 0x756314: r0 = Size()
    //     0x756314: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x756318: ldur            d0, [fp, #-0x58]
    // 0x75631c: StoreField: r0->field_7 = d0
    //     0x75631c: stur            d0, [x0, #7]
    // 0x756320: ldur            d0, [fp, #-0x60]
    // 0x756324: StoreField: r0->field_f = d0
    //     0x756324: stur            d0, [x0, #0xf]
    // 0x756328: LeaveFrame
    //     0x756328: mov             SP, fp
    //     0x75632c: ldp             fp, lr, [SP], #0x10
    // 0x756330: ret
    //     0x756330: ret             
    // 0x756334: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x756334: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x756338: b               #0x755d90
    // 0x75633c: stp             q0, q1, [SP, #-0x20]!
    // 0x756340: stp             x1, x3, [SP, #-0x10]!
    // 0x756344: r0 = AllocateDouble()
    //     0x756344: bl              #0xec2254  ; AllocateDoubleStub
    // 0x756348: ldp             x1, x3, [SP], #0x10
    // 0x75634c: ldp             q0, q1, [SP], #0x20
    // 0x756350: b               #0x755ee4
    // 0x756354: SaveReg d1
    //     0x756354: str             q1, [SP, #-0x10]!
    // 0x756358: stp             x1, x3, [SP, #-0x10]!
    // 0x75635c: SaveReg r0
    //     0x75635c: str             x0, [SP, #-8]!
    // 0x756360: r0 = AllocateDouble()
    //     0x756360: bl              #0xec2254  ; AllocateDoubleStub
    // 0x756364: mov             x2, x0
    // 0x756368: RestoreReg r0
    //     0x756368: ldr             x0, [SP], #8
    // 0x75636c: ldp             x1, x3, [SP], #0x10
    // 0x756370: RestoreReg d1
    //     0x756370: ldr             q1, [SP], #0x10
    // 0x756374: b               #0x755f0c
    // 0x756378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x756378: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75637c: b               #0x755f3c
    // 0x756380: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x756380: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x756384: SaveReg d0
    //     0x756384: str             q0, [SP, #-0x10]!
    // 0x756388: SaveReg r1
    //     0x756388: str             x1, [SP, #-8]!
    // 0x75638c: r0 = AllocateDouble()
    //     0x75638c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x756390: mov             x2, x0
    // 0x756394: RestoreReg r1
    //     0x756394: ldr             x1, [SP], #8
    // 0x756398: RestoreReg d0
    //     0x756398: ldr             q0, [SP], #0x10
    // 0x75639c: b               #0x75605c
    // 0x7563a0: stp             q0, q3, [SP, #-0x20]!
    // 0x7563a4: r0 = AllocateDouble()
    //     0x7563a4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7563a8: ldp             q0, q3, [SP], #0x20
    // 0x7563ac: b               #0x756128
    // 0x7563b0: stp             q0, q1, [SP, #-0x20]!
    // 0x7563b4: stp             x1, x2, [SP, #-0x10]!
    // 0x7563b8: r0 = AllocateDouble()
    //     0x7563b8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7563bc: mov             x3, x0
    // 0x7563c0: ldp             x1, x2, [SP], #0x10
    // 0x7563c4: ldp             q0, q1, [SP], #0x20
    // 0x7563c8: b               #0x756190
    // 0x7563cc: stp             q0, q3, [SP, #-0x20]!
    // 0x7563d0: r0 = AllocateDouble()
    //     0x7563d0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7563d4: ldp             q0, q3, [SP], #0x20
    // 0x7563d8: b               #0x756258
  }
  static _ layoutPositionedChild(/* No info */) {
    // ** addr: 0x7683e8, size: 0x25c
    // 0x7683e8: EnterFrame
    //     0x7683e8: stp             fp, lr, [SP, #-0x10]!
    //     0x7683ec: mov             fp, SP
    // 0x7683f0: AllocStack(0x38)
    //     0x7683f0: sub             SP, SP, #0x38
    // 0x7683f4: SetupParameters(dynamic _ /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0x7683f4: mov             x0, x5
    //     0x7683f8: stur            x5, [fp, #-0x20]
    //     0x7683fc: mov             x5, x1
    //     0x768400: mov             x4, x2
    //     0x768404: stur            x1, [fp, #-8]
    //     0x768408: stur            x2, [fp, #-0x10]
    //     0x76840c: stur            x3, [fp, #-0x18]
    // 0x768410: CheckStackOverflow
    //     0x768410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x768414: cmp             SP, x16
    //     0x768418: b.ls            #0x76863c
    // 0x76841c: mov             x1, x4
    // 0x768420: mov             x2, x3
    // 0x768424: r0 = positionedChildConstraints()
    //     0x768424: bl              #0x73c628  ; [package:flutter/src/rendering/stack.dart] StackParentData::positionedChildConstraints
    // 0x768428: ldur            x3, [fp, #-8]
    // 0x76842c: r1 = LoadClassIdInstr(r3)
    //     0x76842c: ldur            x1, [x3, #-1]
    //     0x768430: ubfx            x1, x1, #0xc, #0x14
    // 0x768434: r16 = true
    //     0x768434: add             x16, NULL, #0x20  ; true
    // 0x768438: str             x16, [SP]
    // 0x76843c: mov             x2, x0
    // 0x768440: mov             x0, x1
    // 0x768444: mov             x1, x3
    // 0x768448: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x768448: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x76844c: ldr             x4, [x4, #0x5c0]
    // 0x768450: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x768450: movz            x17, #0xed1d
    //     0x768454: add             lr, x0, x17
    //     0x768458: ldr             lr, [x21, lr, lsl #3]
    //     0x76845c: blr             lr
    // 0x768460: ldur            x0, [fp, #-0x10]
    // 0x768464: LoadField: r1 = r0->field_23
    //     0x768464: ldur            w1, [x0, #0x23]
    // 0x768468: DecompressPointer r1
    //     0x768468: add             x1, x1, HEAP, lsl #32
    // 0x76846c: cmp             w1, NULL
    // 0x768470: b.eq            #0x76847c
    // 0x768474: LoadField: d0 = r1->field_7
    //     0x768474: ldur            d0, [x1, #7]
    // 0x768478: b               #0x7684e8
    // 0x76847c: LoadField: r1 = r0->field_1b
    //     0x76847c: ldur            w1, [x0, #0x1b]
    // 0x768480: DecompressPointer r1
    //     0x768480: add             x1, x1, HEAP, lsl #32
    // 0x768484: cmp             w1, NULL
    // 0x768488: b.eq            #0x7684c0
    // 0x76848c: ldur            x2, [fp, #-0x18]
    // 0x768490: LoadField: d0 = r2->field_7
    //     0x768490: ldur            d0, [x2, #7]
    // 0x768494: LoadField: d1 = r1->field_7
    //     0x768494: ldur            d1, [x1, #7]
    // 0x768498: fsub            d2, d0, d1
    // 0x76849c: ldur            x1, [fp, #-8]
    // 0x7684a0: stur            d2, [fp, #-0x28]
    // 0x7684a4: r0 = size()
    //     0x7684a4: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7684a8: LoadField: d0 = r0->field_7
    //     0x7684a8: ldur            d0, [x0, #7]
    // 0x7684ac: ldur            d1, [fp, #-0x28]
    // 0x7684b0: fsub            d2, d1, d0
    // 0x7684b4: mov             v0.16b, v2.16b
    // 0x7684b8: ldur            x0, [fp, #-0x10]
    // 0x7684bc: b               #0x7684e8
    // 0x7684c0: ldur            x1, [fp, #-8]
    // 0x7684c4: r0 = size()
    //     0x7684c4: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7684c8: ldur            x1, [fp, #-0x18]
    // 0x7684cc: mov             x2, x0
    // 0x7684d0: r0 = -()
    //     0x7684d0: bl              #0x618814  ; [dart:ui] Size::-
    // 0x7684d4: ldur            x1, [fp, #-0x20]
    // 0x7684d8: mov             x2, x0
    // 0x7684dc: r0 = alongOffset()
    //     0x7684dc: bl              #0x73c5c8  ; [package:flutter/src/painting/alignment.dart] Alignment::alongOffset
    // 0x7684e0: LoadField: d0 = r0->field_7
    //     0x7684e0: ldur            d0, [x0, #7]
    // 0x7684e4: ldur            x0, [fp, #-0x10]
    // 0x7684e8: stur            d0, [fp, #-0x30]
    // 0x7684ec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7684ec: ldur            w1, [x0, #0x17]
    // 0x7684f0: DecompressPointer r1
    //     0x7684f0: add             x1, x1, HEAP, lsl #32
    // 0x7684f4: cmp             w1, NULL
    // 0x7684f8: b.eq            #0x768504
    // 0x7684fc: LoadField: d1 = r1->field_7
    //     0x7684fc: ldur            d1, [x1, #7]
    // 0x768500: b               #0x76857c
    // 0x768504: LoadField: r1 = r0->field_1f
    //     0x768504: ldur            w1, [x0, #0x1f]
    // 0x768508: DecompressPointer r1
    //     0x768508: add             x1, x1, HEAP, lsl #32
    // 0x76850c: cmp             w1, NULL
    // 0x768510: b.eq            #0x76854c
    // 0x768514: ldur            x2, [fp, #-0x18]
    // 0x768518: LoadField: d1 = r2->field_f
    //     0x768518: ldur            d1, [x2, #0xf]
    // 0x76851c: LoadField: d2 = r1->field_7
    //     0x76851c: ldur            d2, [x1, #7]
    // 0x768520: fsub            d3, d1, d2
    // 0x768524: ldur            x1, [fp, #-8]
    // 0x768528: stur            d3, [fp, #-0x28]
    // 0x76852c: r0 = size()
    //     0x76852c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x768530: LoadField: d0 = r0->field_f
    //     0x768530: ldur            d0, [x0, #0xf]
    // 0x768534: ldur            d1, [fp, #-0x28]
    // 0x768538: fsub            d2, d1, d0
    // 0x76853c: mov             v1.16b, v2.16b
    // 0x768540: ldur            x0, [fp, #-0x10]
    // 0x768544: ldur            d0, [fp, #-0x30]
    // 0x768548: b               #0x76857c
    // 0x76854c: ldur            x1, [fp, #-8]
    // 0x768550: r0 = size()
    //     0x768550: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x768554: ldur            x1, [fp, #-0x18]
    // 0x768558: mov             x2, x0
    // 0x76855c: r0 = -()
    //     0x76855c: bl              #0x618814  ; [dart:ui] Size::-
    // 0x768560: ldur            x1, [fp, #-0x20]
    // 0x768564: mov             x2, x0
    // 0x768568: r0 = alongOffset()
    //     0x768568: bl              #0x73c5c8  ; [package:flutter/src/painting/alignment.dart] Alignment::alongOffset
    // 0x76856c: LoadField: d0 = r0->field_f
    //     0x76856c: ldur            d0, [x0, #0xf]
    // 0x768570: mov             v1.16b, v0.16b
    // 0x768574: ldur            x0, [fp, #-0x10]
    // 0x768578: ldur            d0, [fp, #-0x30]
    // 0x76857c: stur            d1, [fp, #-0x28]
    // 0x768580: r0 = Offset()
    //     0x768580: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x768584: ldur            d0, [fp, #-0x30]
    // 0x768588: StoreField: r0->field_7 = d0
    //     0x768588: stur            d0, [x0, #7]
    // 0x76858c: ldur            d1, [fp, #-0x28]
    // 0x768590: StoreField: r0->field_f = d1
    //     0x768590: stur            d1, [x0, #0xf]
    // 0x768594: ldur            x1, [fp, #-0x10]
    // 0x768598: StoreField: r1->field_7 = r0
    //     0x768598: stur            w0, [x1, #7]
    //     0x76859c: ldurb           w16, [x1, #-1]
    //     0x7685a0: ldurb           w17, [x0, #-1]
    //     0x7685a4: and             x16, x17, x16, lsr #2
    //     0x7685a8: tst             x16, HEAP, lsr #32
    //     0x7685ac: b.eq            #0x7685b4
    //     0x7685b0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7685b4: d2 = 0.000000
    //     0x7685b4: eor             v2.16b, v2.16b, v2.16b
    // 0x7685b8: fcmp            d2, d0
    // 0x7685bc: b.gt            #0x7685f8
    // 0x7685c0: ldur            x0, [fp, #-0x18]
    // 0x7685c4: ldur            x1, [fp, #-8]
    // 0x7685c8: r0 = size()
    //     0x7685c8: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7685cc: LoadField: d0 = r0->field_7
    //     0x7685cc: ldur            d0, [x0, #7]
    // 0x7685d0: ldur            d1, [fp, #-0x30]
    // 0x7685d4: fadd            d2, d1, d0
    // 0x7685d8: ldur            x0, [fp, #-0x18]
    // 0x7685dc: LoadField: d0 = r0->field_7
    //     0x7685dc: ldur            d0, [x0, #7]
    // 0x7685e0: fcmp            d2, d0
    // 0x7685e4: b.gt            #0x7685f8
    // 0x7685e8: ldur            d0, [fp, #-0x28]
    // 0x7685ec: d1 = 0.000000
    //     0x7685ec: eor             v1.16b, v1.16b, v1.16b
    // 0x7685f0: fcmp            d1, d0
    // 0x7685f4: b.le            #0x768600
    // 0x7685f8: r0 = true
    //     0x7685f8: add             x0, NULL, #0x20  ; true
    // 0x7685fc: b               #0x768630
    // 0x768600: ldur            x1, [fp, #-8]
    // 0x768604: r0 = size()
    //     0x768604: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x768608: LoadField: d0 = r0->field_f
    //     0x768608: ldur            d0, [x0, #0xf]
    // 0x76860c: ldur            d1, [fp, #-0x28]
    // 0x768610: fadd            d2, d1, d0
    // 0x768614: ldur            x1, [fp, #-0x18]
    // 0x768618: LoadField: d0 = r1->field_f
    //     0x768618: ldur            d0, [x1, #0xf]
    // 0x76861c: fcmp            d2, d0
    // 0x768620: r16 = true
    //     0x768620: add             x16, NULL, #0x20  ; true
    // 0x768624: r17 = false
    //     0x768624: add             x17, NULL, #0x30  ; false
    // 0x768628: csel            x1, x16, x17, gt
    // 0x76862c: mov             x0, x1
    // 0x768630: LeaveFrame
    //     0x768630: mov             SP, fp
    //     0x768634: ldp             fp, lr, [SP], #0x10
    // 0x768638: ret
    //     0x768638: ret             
    // 0x76863c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76863c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x768640: b               #0x76841c
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x773e84, size: 0x524
    // 0x773e84: EnterFrame
    //     0x773e84: stp             fp, lr, [SP, #-0x10]!
    //     0x773e88: mov             fp, SP
    // 0x773e8c: AllocStack(0x48)
    //     0x773e8c: sub             SP, SP, #0x48
    // 0x773e90: SetupParameters(RenderStack this /* r1 => r3, fp-0x10 */)
    //     0x773e90: mov             x3, x1
    //     0x773e94: stur            x1, [fp, #-0x10]
    // 0x773e98: CheckStackOverflow
    //     0x773e98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x773e9c: cmp             SP, x16
    //     0x773ea0: b.ls            #0x774394
    // 0x773ea4: LoadField: r4 = r3->field_27
    //     0x773ea4: ldur            w4, [x3, #0x27]
    // 0x773ea8: DecompressPointer r4
    //     0x773ea8: add             x4, x4, HEAP, lsl #32
    // 0x773eac: stur            x4, [fp, #-8]
    // 0x773eb0: cmp             w4, NULL
    // 0x773eb4: b.eq            #0x774158
    // 0x773eb8: mov             x0, x4
    // 0x773ebc: r2 = Null
    //     0x773ebc: mov             x2, NULL
    // 0x773ec0: r1 = Null
    //     0x773ec0: mov             x1, NULL
    // 0x773ec4: r4 = LoadClassIdInstr(r0)
    //     0x773ec4: ldur            x4, [x0, #-1]
    //     0x773ec8: ubfx            x4, x4, #0xc, #0x14
    // 0x773ecc: sub             x4, x4, #0xc83
    // 0x773ed0: cmp             x4, #1
    // 0x773ed4: b.ls            #0x773ee8
    // 0x773ed8: r8 = BoxConstraints
    //     0x773ed8: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x773edc: r3 = Null
    //     0x773edc: add             x3, PP, #0x44, lsl #12  ; [pp+0x44cf0] Null
    //     0x773ee0: ldr             x3, [x3, #0xcf0]
    // 0x773ee4: r0 = BoxConstraints()
    //     0x773ee4: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x773ee8: ldur            x0, [fp, #-0x10]
    // 0x773eec: r1 = false
    //     0x773eec: add             x1, NULL, #0x30  ; false
    // 0x773ef0: StoreField: r0->field_67 = r1
    //     0x773ef0: stur            w1, [x0, #0x67]
    // 0x773ef4: mov             x1, x0
    // 0x773ef8: ldur            x2, [fp, #-8]
    // 0x773efc: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static.
    //     0x773efc: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b28] Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static. (0x7e54fb1673f8)
    //     0x773f00: ldr             x3, [x3, #0xb28]
    // 0x773f04: r0 = _computeSize()
    //     0x773f04: bl              #0x755d64  ; [package:flutter/src/rendering/stack.dart] RenderStack::_computeSize
    // 0x773f08: ldur            x2, [fp, #-0x10]
    // 0x773f0c: StoreField: r2->field_53 = r0
    //     0x773f0c: stur            w0, [x2, #0x53]
    //     0x773f10: ldurb           w16, [x2, #-1]
    //     0x773f14: ldurb           w17, [x0, #-1]
    //     0x773f18: and             x16, x17, x16, lsr #2
    //     0x773f1c: tst             x16, HEAP, lsr #32
    //     0x773f20: b.eq            #0x773f28
    //     0x773f24: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x773f28: mov             x1, x2
    // 0x773f2c: r0 = _resolvedAlignment()
    //     0x773f2c: bl              #0x743ba4  ; [package:flutter/src/rendering/stack.dart] RenderStack::_resolvedAlignment
    // 0x773f30: mov             x4, x0
    // 0x773f34: ldur            x3, [fp, #-0x10]
    // 0x773f38: stur            x4, [fp, #-0x20]
    // 0x773f3c: LoadField: r0 = r3->field_5f
    //     0x773f3c: ldur            w0, [x3, #0x5f]
    // 0x773f40: DecompressPointer r0
    //     0x773f40: add             x0, x0, HEAP, lsl #32
    // 0x773f44: LoadField: d0 = r4->field_7
    //     0x773f44: ldur            d0, [x4, #7]
    // 0x773f48: stur            d0, [fp, #-0x30]
    // 0x773f4c: LoadField: d1 = r4->field_f
    //     0x773f4c: ldur            d1, [x4, #0xf]
    // 0x773f50: stur            d1, [fp, #-0x28]
    // 0x773f54: mov             x5, x0
    // 0x773f58: stur            x5, [fp, #-0x18]
    // 0x773f5c: CheckStackOverflow
    //     0x773f5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x773f60: cmp             SP, x16
    //     0x773f64: b.ls            #0x77439c
    // 0x773f68: cmp             w5, NULL
    // 0x773f6c: b.eq            #0x774148
    // 0x773f70: LoadField: r6 = r5->field_7
    //     0x773f70: ldur            w6, [x5, #7]
    // 0x773f74: DecompressPointer r6
    //     0x773f74: add             x6, x6, HEAP, lsl #32
    // 0x773f78: stur            x6, [fp, #-8]
    // 0x773f7c: cmp             w6, NULL
    // 0x773f80: b.eq            #0x7743a4
    // 0x773f84: mov             x0, x6
    // 0x773f88: r2 = Null
    //     0x773f88: mov             x2, NULL
    // 0x773f8c: r1 = Null
    //     0x773f8c: mov             x1, NULL
    // 0x773f90: r4 = LoadClassIdInstr(r0)
    //     0x773f90: ldur            x4, [x0, #-1]
    //     0x773f94: ubfx            x4, x4, #0xc, #0x14
    // 0x773f98: sub             x4, x4, #0xc7a
    // 0x773f9c: cmp             x4, #2
    // 0x773fa0: b.ls            #0x773fb8
    // 0x773fa4: r8 = StackParentData
    //     0x773fa4: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0x773fa8: ldr             x8, [x8, #0x5c0]
    // 0x773fac: r3 = Null
    //     0x773fac: add             x3, PP, #0x44, lsl #12  ; [pp+0x44d00] Null
    //     0x773fb0: ldr             x3, [x3, #0xd00]
    // 0x773fb4: r0 = DefaultTypeTest()
    //     0x773fb4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x773fb8: ldur            x2, [fp, #-8]
    // 0x773fbc: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x773fbc: ldur            w0, [x2, #0x17]
    // 0x773fc0: DecompressPointer r0
    //     0x773fc0: add             x0, x0, HEAP, lsl #32
    // 0x773fc4: cmp             w0, NULL
    // 0x773fc8: b.ne            #0x77400c
    // 0x773fcc: LoadField: r0 = r2->field_1b
    //     0x773fcc: ldur            w0, [x2, #0x1b]
    // 0x773fd0: DecompressPointer r0
    //     0x773fd0: add             x0, x0, HEAP, lsl #32
    // 0x773fd4: cmp             w0, NULL
    // 0x773fd8: b.ne            #0x77400c
    // 0x773fdc: LoadField: r0 = r2->field_1f
    //     0x773fdc: ldur            w0, [x2, #0x1f]
    // 0x773fe0: DecompressPointer r0
    //     0x773fe0: add             x0, x0, HEAP, lsl #32
    // 0x773fe4: cmp             w0, NULL
    // 0x773fe8: b.ne            #0x77400c
    // 0x773fec: LoadField: r0 = r2->field_23
    //     0x773fec: ldur            w0, [x2, #0x23]
    // 0x773ff0: DecompressPointer r0
    //     0x773ff0: add             x0, x0, HEAP, lsl #32
    // 0x773ff4: cmp             w0, NULL
    // 0x773ff8: b.ne            #0x77400c
    // 0x773ffc: LoadField: r0 = r2->field_27
    //     0x773ffc: ldur            w0, [x2, #0x27]
    // 0x774000: DecompressPointer r0
    //     0x774000: add             x0, x0, HEAP, lsl #32
    // 0x774004: cmp             w0, NULL
    // 0x774008: b.eq            #0x774018
    // 0x77400c: ldur            x3, [fp, #-0x18]
    // 0x774010: mov             x4, x2
    // 0x774014: b               #0x7740dc
    // 0x774018: LoadField: r0 = r2->field_2b
    //     0x774018: ldur            w0, [x2, #0x2b]
    // 0x77401c: DecompressPointer r0
    //     0x77401c: add             x0, x0, HEAP, lsl #32
    // 0x774020: cmp             w0, NULL
    // 0x774024: b.ne            #0x7740d4
    // 0x774028: ldur            x1, [fp, #-0x10]
    // 0x77402c: LoadField: r0 = r1->field_53
    //     0x77402c: ldur            w0, [x1, #0x53]
    // 0x774030: DecompressPointer r0
    //     0x774030: add             x0, x0, HEAP, lsl #32
    // 0x774034: cmp             w0, NULL
    // 0x774038: b.eq            #0x774228
    // 0x77403c: ldur            x3, [fp, #-0x18]
    // 0x774040: LoadField: r4 = r3->field_53
    //     0x774040: ldur            w4, [x3, #0x53]
    // 0x774044: DecompressPointer r4
    //     0x774044: add             x4, x4, HEAP, lsl #32
    // 0x774048: cmp             w4, NULL
    // 0x77404c: b.eq            #0x774174
    // 0x774050: ldur            d0, [fp, #-0x30]
    // 0x774054: ldur            d1, [fp, #-0x28]
    // 0x774058: d2 = 2.000000
    //     0x774058: fmov            d2, #2.00000000
    // 0x77405c: LoadField: d3 = r0->field_7
    //     0x77405c: ldur            d3, [x0, #7]
    // 0x774060: LoadField: d4 = r4->field_7
    //     0x774060: ldur            d4, [x4, #7]
    // 0x774064: fsub            d5, d3, d4
    // 0x774068: LoadField: d3 = r0->field_f
    //     0x774068: ldur            d3, [x0, #0xf]
    // 0x77406c: LoadField: d4 = r4->field_f
    //     0x77406c: ldur            d4, [x4, #0xf]
    // 0x774070: fsub            d6, d3, d4
    // 0x774074: fdiv            d3, d5, d2
    // 0x774078: fdiv            d4, d6, d2
    // 0x77407c: fmul            d5, d0, d3
    // 0x774080: fadd            d6, d3, d5
    // 0x774084: stur            d6, [fp, #-0x40]
    // 0x774088: fmul            d3, d1, d4
    // 0x77408c: fadd            d5, d4, d3
    // 0x774090: stur            d5, [fp, #-0x38]
    // 0x774094: r0 = Offset()
    //     0x774094: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x774098: ldur            d0, [fp, #-0x40]
    // 0x77409c: StoreField: r0->field_7 = d0
    //     0x77409c: stur            d0, [x0, #7]
    // 0x7740a0: ldur            d0, [fp, #-0x38]
    // 0x7740a4: StoreField: r0->field_f = d0
    //     0x7740a4: stur            d0, [x0, #0xf]
    // 0x7740a8: ldur            x4, [fp, #-8]
    // 0x7740ac: StoreField: r4->field_7 = r0
    //     0x7740ac: stur            w0, [x4, #7]
    //     0x7740b0: ldurb           w16, [x4, #-1]
    //     0x7740b4: ldurb           w17, [x0, #-1]
    //     0x7740b8: and             x16, x17, x16, lsr #2
    //     0x7740bc: tst             x16, HEAP, lsr #32
    //     0x7740c0: b.eq            #0x7740c8
    //     0x7740c4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7740c8: ldur            x0, [fp, #-0x10]
    // 0x7740cc: mov             x1, x4
    // 0x7740d0: b               #0x77412c
    // 0x7740d4: ldur            x3, [fp, #-0x18]
    // 0x7740d8: mov             x4, x2
    // 0x7740dc: ldur            x0, [fp, #-0x10]
    // 0x7740e0: LoadField: r1 = r0->field_53
    //     0x7740e0: ldur            w1, [x0, #0x53]
    // 0x7740e4: DecompressPointer r1
    //     0x7740e4: add             x1, x1, HEAP, lsl #32
    // 0x7740e8: cmp             w1, NULL
    // 0x7740ec: b.eq            #0x7742e0
    // 0x7740f0: mov             x16, x1
    // 0x7740f4: mov             x1, x3
    // 0x7740f8: mov             x3, x16
    // 0x7740fc: mov             x2, x4
    // 0x774100: ldur            x5, [fp, #-0x20]
    // 0x774104: r0 = layoutPositionedChild()
    //     0x774104: bl              #0x7683e8  ; [package:flutter/src/rendering/stack.dart] RenderStack::layoutPositionedChild
    // 0x774108: tbnz            w0, #4, #0x774118
    // 0x77410c: ldur            x0, [fp, #-0x10]
    // 0x774110: r1 = true
    //     0x774110: add             x1, NULL, #0x20  ; true
    // 0x774114: b               #0x774124
    // 0x774118: ldur            x0, [fp, #-0x10]
    // 0x77411c: LoadField: r1 = r0->field_67
    //     0x77411c: ldur            w1, [x0, #0x67]
    // 0x774120: DecompressPointer r1
    //     0x774120: add             x1, x1, HEAP, lsl #32
    // 0x774124: StoreField: r0->field_67 = r1
    //     0x774124: stur            w1, [x0, #0x67]
    // 0x774128: ldur            x1, [fp, #-8]
    // 0x77412c: LoadField: r5 = r1->field_13
    //     0x77412c: ldur            w5, [x1, #0x13]
    // 0x774130: DecompressPointer r5
    //     0x774130: add             x5, x5, HEAP, lsl #32
    // 0x774134: mov             x3, x0
    // 0x774138: ldur            x4, [fp, #-0x20]
    // 0x77413c: ldur            d0, [fp, #-0x30]
    // 0x774140: ldur            d1, [fp, #-0x28]
    // 0x774144: b               #0x773f58
    // 0x774148: r0 = Null
    //     0x774148: mov             x0, NULL
    // 0x77414c: LeaveFrame
    //     0x77414c: mov             SP, fp
    //     0x774150: ldp             fp, lr, [SP], #0x10
    // 0x774154: ret
    //     0x774154: ret             
    // 0x774158: r0 = StateError()
    //     0x774158: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x77415c: mov             x1, x0
    // 0x774160: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x774160: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x774164: StoreField: r1->field_b = r0
    //     0x774164: stur            w0, [x1, #0xb]
    // 0x774168: mov             x0, x1
    // 0x77416c: r0 = Throw()
    //     0x77416c: bl              #0xec04b8  ; ThrowStub
    // 0x774170: brk             #0
    // 0x774174: r1 = Null
    //     0x774174: mov             x1, NULL
    // 0x774178: r2 = 8
    //     0x774178: movz            x2, #0x8
    // 0x77417c: r0 = AllocateArray()
    //     0x77417c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x774180: stur            x0, [fp, #-8]
    // 0x774184: r16 = "RenderBox was not laid out: "
    //     0x774184: ldr             x16, [PP, #0x44c0]  ; [pp+0x44c0] "RenderBox was not laid out: "
    // 0x774188: StoreField: r0->field_f = r16
    //     0x774188: stur            w16, [x0, #0xf]
    // 0x77418c: ldur            x16, [fp, #-0x18]
    // 0x774190: str             x16, [SP]
    // 0x774194: r0 = runtimeType()
    //     0x774194: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0x774198: ldur            x1, [fp, #-8]
    // 0x77419c: ArrayStore: r1[1] = r0  ; List_4
    //     0x77419c: add             x25, x1, #0x13
    //     0x7741a0: str             w0, [x25]
    //     0x7741a4: tbz             w0, #0, #0x7741c0
    //     0x7741a8: ldurb           w16, [x1, #-1]
    //     0x7741ac: ldurb           w17, [x0, #-1]
    //     0x7741b0: and             x16, x17, x16, lsr #2
    //     0x7741b4: tst             x16, HEAP, lsr #32
    //     0x7741b8: b.eq            #0x7741c0
    //     0x7741bc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7741c0: ldur            x0, [fp, #-8]
    // 0x7741c4: r16 = "#"
    //     0x7741c4: ldr             x16, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0x7741c8: ArrayStore: r0[0] = r16  ; List_4
    //     0x7741c8: stur            w16, [x0, #0x17]
    // 0x7741cc: ldur            x1, [fp, #-0x18]
    // 0x7741d0: r0 = shortHash()
    //     0x7741d0: bl              #0x67f178  ; [package:flutter/src/foundation/diagnostics.dart] ::shortHash
    // 0x7741d4: ldur            x1, [fp, #-8]
    // 0x7741d8: ArrayStore: r1[3] = r0  ; List_4
    //     0x7741d8: add             x25, x1, #0x1b
    //     0x7741dc: str             w0, [x25]
    //     0x7741e0: tbz             w0, #0, #0x7741fc
    //     0x7741e4: ldurb           w16, [x1, #-1]
    //     0x7741e8: ldurb           w17, [x0, #-1]
    //     0x7741ec: and             x16, x17, x16, lsr #2
    //     0x7741f0: tst             x16, HEAP, lsr #32
    //     0x7741f4: b.eq            #0x7741fc
    //     0x7741f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7741fc: ldur            x16, [fp, #-8]
    // 0x774200: str             x16, [SP]
    // 0x774204: r0 = _interpolate()
    //     0x774204: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x774208: stur            x0, [fp, #-8]
    // 0x77420c: r0 = StateError()
    //     0x77420c: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x774210: mov             x1, x0
    // 0x774214: ldur            x0, [fp, #-8]
    // 0x774218: StoreField: r1->field_b = r0
    //     0x774218: stur            w0, [x1, #0xb]
    // 0x77421c: mov             x0, x1
    // 0x774220: r0 = Throw()
    //     0x774220: bl              #0xec04b8  ; ThrowStub
    // 0x774224: brk             #0
    // 0x774228: mov             x0, x1
    // 0x77422c: r1 = Null
    //     0x77422c: mov             x1, NULL
    // 0x774230: r2 = 8
    //     0x774230: movz            x2, #0x8
    // 0x774234: r0 = AllocateArray()
    //     0x774234: bl              #0xec22fc  ; AllocateArrayStub
    // 0x774238: stur            x0, [fp, #-8]
    // 0x77423c: r16 = "RenderBox was not laid out: "
    //     0x77423c: ldr             x16, [PP, #0x44c0]  ; [pp+0x44c0] "RenderBox was not laid out: "
    // 0x774240: StoreField: r0->field_f = r16
    //     0x774240: stur            w16, [x0, #0xf]
    // 0x774244: ldur            x16, [fp, #-0x10]
    // 0x774248: str             x16, [SP]
    // 0x77424c: r0 = runtimeType()
    //     0x77424c: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0x774250: ldur            x1, [fp, #-8]
    // 0x774254: ArrayStore: r1[1] = r0  ; List_4
    //     0x774254: add             x25, x1, #0x13
    //     0x774258: str             w0, [x25]
    //     0x77425c: tbz             w0, #0, #0x774278
    //     0x774260: ldurb           w16, [x1, #-1]
    //     0x774264: ldurb           w17, [x0, #-1]
    //     0x774268: and             x16, x17, x16, lsr #2
    //     0x77426c: tst             x16, HEAP, lsr #32
    //     0x774270: b.eq            #0x774278
    //     0x774274: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x774278: ldur            x0, [fp, #-8]
    // 0x77427c: r16 = "#"
    //     0x77427c: ldr             x16, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0x774280: ArrayStore: r0[0] = r16  ; List_4
    //     0x774280: stur            w16, [x0, #0x17]
    // 0x774284: ldur            x1, [fp, #-0x10]
    // 0x774288: r0 = shortHash()
    //     0x774288: bl              #0x67f178  ; [package:flutter/src/foundation/diagnostics.dart] ::shortHash
    // 0x77428c: ldur            x1, [fp, #-8]
    // 0x774290: ArrayStore: r1[3] = r0  ; List_4
    //     0x774290: add             x25, x1, #0x1b
    //     0x774294: str             w0, [x25]
    //     0x774298: tbz             w0, #0, #0x7742b4
    //     0x77429c: ldurb           w16, [x1, #-1]
    //     0x7742a0: ldurb           w17, [x0, #-1]
    //     0x7742a4: and             x16, x17, x16, lsr #2
    //     0x7742a8: tst             x16, HEAP, lsr #32
    //     0x7742ac: b.eq            #0x7742b4
    //     0x7742b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7742b4: ldur            x16, [fp, #-8]
    // 0x7742b8: str             x16, [SP]
    // 0x7742bc: r0 = _interpolate()
    //     0x7742bc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7742c0: stur            x0, [fp, #-8]
    // 0x7742c4: r0 = StateError()
    //     0x7742c4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x7742c8: mov             x1, x0
    // 0x7742cc: ldur            x0, [fp, #-8]
    // 0x7742d0: StoreField: r1->field_b = r0
    //     0x7742d0: stur            w0, [x1, #0xb]
    // 0x7742d4: mov             x0, x1
    // 0x7742d8: r0 = Throw()
    //     0x7742d8: bl              #0xec04b8  ; ThrowStub
    // 0x7742dc: brk             #0
    // 0x7742e0: r1 = Null
    //     0x7742e0: mov             x1, NULL
    // 0x7742e4: r2 = 8
    //     0x7742e4: movz            x2, #0x8
    // 0x7742e8: r0 = AllocateArray()
    //     0x7742e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7742ec: stur            x0, [fp, #-8]
    // 0x7742f0: r16 = "RenderBox was not laid out: "
    //     0x7742f0: ldr             x16, [PP, #0x44c0]  ; [pp+0x44c0] "RenderBox was not laid out: "
    // 0x7742f4: StoreField: r0->field_f = r16
    //     0x7742f4: stur            w16, [x0, #0xf]
    // 0x7742f8: ldur            x16, [fp, #-0x10]
    // 0x7742fc: str             x16, [SP]
    // 0x774300: r0 = runtimeType()
    //     0x774300: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0x774304: ldur            x1, [fp, #-8]
    // 0x774308: ArrayStore: r1[1] = r0  ; List_4
    //     0x774308: add             x25, x1, #0x13
    //     0x77430c: str             w0, [x25]
    //     0x774310: tbz             w0, #0, #0x77432c
    //     0x774314: ldurb           w16, [x1, #-1]
    //     0x774318: ldurb           w17, [x0, #-1]
    //     0x77431c: and             x16, x17, x16, lsr #2
    //     0x774320: tst             x16, HEAP, lsr #32
    //     0x774324: b.eq            #0x77432c
    //     0x774328: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x77432c: ldur            x0, [fp, #-8]
    // 0x774330: r16 = "#"
    //     0x774330: ldr             x16, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0x774334: ArrayStore: r0[0] = r16  ; List_4
    //     0x774334: stur            w16, [x0, #0x17]
    // 0x774338: ldur            x1, [fp, #-0x10]
    // 0x77433c: r0 = shortHash()
    //     0x77433c: bl              #0x67f178  ; [package:flutter/src/foundation/diagnostics.dart] ::shortHash
    // 0x774340: ldur            x1, [fp, #-8]
    // 0x774344: ArrayStore: r1[3] = r0  ; List_4
    //     0x774344: add             x25, x1, #0x1b
    //     0x774348: str             w0, [x25]
    //     0x77434c: tbz             w0, #0, #0x774368
    //     0x774350: ldurb           w16, [x1, #-1]
    //     0x774354: ldurb           w17, [x0, #-1]
    //     0x774358: and             x16, x17, x16, lsr #2
    //     0x77435c: tst             x16, HEAP, lsr #32
    //     0x774360: b.eq            #0x774368
    //     0x774364: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x774368: ldur            x16, [fp, #-8]
    // 0x77436c: str             x16, [SP]
    // 0x774370: r0 = _interpolate()
    //     0x774370: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x774374: stur            x0, [fp, #-8]
    // 0x774378: r0 = StateError()
    //     0x774378: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x77437c: mov             x1, x0
    // 0x774380: ldur            x0, [fp, #-8]
    // 0x774384: StoreField: r1->field_b = r0
    //     0x774384: stur            w0, [x1, #0xb]
    // 0x774388: mov             x0, x1
    // 0x77438c: r0 = Throw()
    //     0x77438c: bl              #0xec04b8  ; ThrowStub
    // 0x774390: brk             #0
    // 0x774394: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x774394: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x774398: b               #0x773ea4
    // 0x77439c: r0 = StackOverflowSharedWithFPURegs()
    //     0x77439c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7743a0: b               #0x773f68
    // 0x7743a4: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7743a4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ setupParentData(/* No info */) {
    // ** addr: 0x78825c, size: 0xb4
    // 0x78825c: EnterFrame
    //     0x78825c: stp             fp, lr, [SP, #-0x10]!
    //     0x788260: mov             fp, SP
    // 0x788264: AllocStack(0x8)
    //     0x788264: sub             SP, SP, #8
    // 0x788268: SetupParameters(RenderStack this /* r1 => r4 */, dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x788268: mov             x0, x2
    //     0x78826c: mov             x4, x1
    //     0x788270: mov             x3, x2
    //     0x788274: stur            x2, [fp, #-8]
    // 0x788278: r2 = Null
    //     0x788278: mov             x2, NULL
    // 0x78827c: r1 = Null
    //     0x78827c: mov             x1, NULL
    // 0x788280: r4 = 60
    //     0x788280: movz            x4, #0x3c
    // 0x788284: branchIfSmi(r0, 0x788290)
    //     0x788284: tbz             w0, #0, #0x788290
    // 0x788288: r4 = LoadClassIdInstr(r0)
    //     0x788288: ldur            x4, [x0, #-1]
    //     0x78828c: ubfx            x4, x4, #0xc, #0x14
    // 0x788290: sub             x4, x4, #0xbba
    // 0x788294: cmp             x4, #0x9a
    // 0x788298: b.ls            #0x7882ac
    // 0x78829c: r8 = RenderBox
    //     0x78829c: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7882a0: r3 = Null
    //     0x7882a0: add             x3, PP, #0x44, lsl #12  ; [pp+0x44d28] Null
    //     0x7882a4: ldr             x3, [x3, #0xd28]
    // 0x7882a8: r0 = RenderBox()
    //     0x7882a8: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7882ac: ldur            x0, [fp, #-8]
    // 0x7882b0: LoadField: r1 = r0->field_7
    //     0x7882b0: ldur            w1, [x0, #7]
    // 0x7882b4: DecompressPointer r1
    //     0x7882b4: add             x1, x1, HEAP, lsl #32
    // 0x7882b8: r2 = LoadClassIdInstr(r1)
    //     0x7882b8: ldur            x2, [x1, #-1]
    //     0x7882bc: ubfx            x2, x2, #0xc, #0x14
    // 0x7882c0: sub             x16, x2, #0xc7a
    // 0x7882c4: cmp             x16, #2
    // 0x7882c8: b.ls            #0x788300
    // 0x7882cc: r1 = <RenderBox>
    //     0x7882cc: add             x1, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x7882d0: ldr             x1, [x1, #0x1d8]
    // 0x7882d4: r0 = StackParentData()
    //     0x7882d4: bl              #0x787b48  ; AllocateStackParentDataStub -> StackParentData (size=0x30)
    // 0x7882d8: r1 = Instance_Offset
    //     0x7882d8: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7882dc: StoreField: r0->field_7 = r1
    //     0x7882dc: stur            w1, [x0, #7]
    // 0x7882e0: ldur            x1, [fp, #-8]
    // 0x7882e4: StoreField: r1->field_7 = r0
    //     0x7882e4: stur            w0, [x1, #7]
    //     0x7882e8: ldurb           w16, [x1, #-1]
    //     0x7882ec: ldurb           w17, [x0, #-1]
    //     0x7882f0: and             x16, x17, x16, lsr #2
    //     0x7882f4: tst             x16, HEAP, lsr #32
    //     0x7882f8: b.eq            #0x788300
    //     0x7882fc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x788300: r0 = Null
    //     0x788300: mov             x0, NULL
    // 0x788304: LeaveFrame
    //     0x788304: mov             SP, fp
    //     0x788308: ldp             fp, lr, [SP], #0x10
    // 0x78830c: ret
    //     0x78830c: ret             
  }
  _ paint(/* No info */) {
    // ** addr: 0x79b870, size: 0x1fc
    // 0x79b870: EnterFrame
    //     0x79b870: stp             fp, lr, [SP, #-0x10]!
    //     0x79b874: mov             fp, SP
    // 0x79b878: AllocStack(0x38)
    //     0x79b878: sub             SP, SP, #0x38
    // 0x79b87c: SetupParameters(RenderStack this /* r1 => r2, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */, dynamic _ /* r3 => r3, fp-0x28 */)
    //     0x79b87c: mov             x0, x2
    //     0x79b880: stur            x2, [fp, #-0x20]
    //     0x79b884: mov             x2, x1
    //     0x79b888: stur            x1, [fp, #-0x18]
    //     0x79b88c: stur            x3, [fp, #-0x28]
    // 0x79b890: CheckStackOverflow
    //     0x79b890: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79b894: cmp             SP, x16
    //     0x79b898: b.ls            #0x79ba58
    // 0x79b89c: LoadField: r1 = r2->field_7b
    //     0x79b89c: ldur            w1, [x2, #0x7b]
    // 0x79b8a0: DecompressPointer r1
    //     0x79b8a0: add             x1, x1, HEAP, lsl #32
    // 0x79b8a4: r16 = Instance_Clip
    //     0x79b8a4: add             x16, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x79b8a8: ldr             x16, [x16, #0x750]
    // 0x79b8ac: cmp             w1, w16
    // 0x79b8b0: b.eq            #0x79b990
    // 0x79b8b4: LoadField: r1 = r2->field_67
    //     0x79b8b4: ldur            w1, [x2, #0x67]
    // 0x79b8b8: DecompressPointer r1
    //     0x79b8b8: add             x1, x1, HEAP, lsl #32
    // 0x79b8bc: tbnz            w1, #4, #0x79b988
    // 0x79b8c0: LoadField: r4 = r2->field_7f
    //     0x79b8c0: ldur            w4, [x2, #0x7f]
    // 0x79b8c4: DecompressPointer r4
    //     0x79b8c4: add             x4, x4, HEAP, lsl #32
    // 0x79b8c8: stur            x4, [fp, #-0x10]
    // 0x79b8cc: LoadField: r5 = r2->field_37
    //     0x79b8cc: ldur            w5, [x2, #0x37]
    // 0x79b8d0: DecompressPointer r5
    //     0x79b8d0: add             x5, x5, HEAP, lsl #32
    // 0x79b8d4: r16 = Sentinel
    //     0x79b8d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x79b8d8: cmp             w5, w16
    // 0x79b8dc: b.eq            #0x79ba60
    // 0x79b8e0: mov             x1, x2
    // 0x79b8e4: stur            x5, [fp, #-8]
    // 0x79b8e8: r0 = size()
    //     0x79b8e8: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x79b8ec: mov             x2, x0
    // 0x79b8f0: r1 = Instance_Offset
    //     0x79b8f0: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x79b8f4: r0 = &()
    //     0x79b8f4: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x79b8f8: mov             x3, x0
    // 0x79b8fc: ldur            x0, [fp, #-0x18]
    // 0x79b900: stur            x3, [fp, #-0x30]
    // 0x79b904: r1 = LoadClassIdInstr(r0)
    //     0x79b904: ldur            x1, [x0, #-1]
    //     0x79b908: ubfx            x1, x1, #0xc, #0x14
    // 0x79b90c: cmp             x1, #0xbd6
    // 0x79b910: b.ne            #0x79b92c
    // 0x79b914: mov             x2, x0
    // 0x79b918: r1 = Function 'paintStack':.
    //     0x79b918: add             x1, PP, #0x44, lsl #12  ; [pp+0x44c58] AnonymousClosure: (0x79bbd8), of [package:flutter/src/rendering/stack.dart] RenderStack
    //     0x79b91c: ldr             x1, [x1, #0xc58]
    // 0x79b920: r0 = AllocateClosure()
    //     0x79b920: bl              #0xec1630  ; AllocateClosureStub
    // 0x79b924: mov             x6, x0
    // 0x79b928: b               #0x79b940
    // 0x79b92c: ldur            x2, [fp, #-0x18]
    // 0x79b930: r1 = Function 'paintStack':.
    //     0x79b930: add             x1, PP, #0x44, lsl #12  ; [pp+0x44c60] AnonymousClosure: (0x79bb98), in [package:flutter/src/rendering/stack.dart] RenderIndexedStack::paintStack (0xda995c)
    //     0x79b934: ldr             x1, [x1, #0xc60]
    // 0x79b938: r0 = AllocateClosure()
    //     0x79b938: bl              #0xec1630  ; AllocateClosureStub
    // 0x79b93c: mov             x6, x0
    // 0x79b940: ldur            x0, [fp, #-0x18]
    // 0x79b944: ldur            x4, [fp, #-0x10]
    // 0x79b948: LoadField: r1 = r0->field_7b
    //     0x79b948: ldur            w1, [x0, #0x7b]
    // 0x79b94c: DecompressPointer r1
    //     0x79b94c: add             x1, x1, HEAP, lsl #32
    // 0x79b950: LoadField: r7 = r4->field_b
    //     0x79b950: ldur            w7, [x4, #0xb]
    // 0x79b954: DecompressPointer r7
    //     0x79b954: add             x7, x7, HEAP, lsl #32
    // 0x79b958: str             x1, [SP]
    // 0x79b95c: ldur            x1, [fp, #-0x20]
    // 0x79b960: ldur            x2, [fp, #-8]
    // 0x79b964: ldur            x3, [fp, #-0x28]
    // 0x79b968: ldur            x5, [fp, #-0x30]
    // 0x79b96c: r4 = const [0, 0x7, 0x1, 0x6, clipBehavior, 0x6, null]
    //     0x79b96c: add             x4, PP, #0x44, lsl #12  ; [pp+0x44c68] List(7) [0, 0x7, 0x1, 0x6, "clipBehavior", 0x6, Null]
    //     0x79b970: ldr             x4, [x4, #0xc68]
    // 0x79b974: r0 = pushClipRect()
    //     0x79b974: bl              #0x78c920  ; [package:flutter/src/rendering/object.dart] PaintingContext::pushClipRect
    // 0x79b978: ldur            x1, [fp, #-0x10]
    // 0x79b97c: mov             x2, x0
    // 0x79b980: r0 = layer=()
    //     0x79b980: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0x79b984: b               #0x79ba48
    // 0x79b988: mov             x0, x2
    // 0x79b98c: b               #0x79b994
    // 0x79b990: mov             x0, x2
    // 0x79b994: LoadField: r1 = r0->field_7f
    //     0x79b994: ldur            w1, [x0, #0x7f]
    // 0x79b998: DecompressPointer r1
    //     0x79b998: add             x1, x1, HEAP, lsl #32
    // 0x79b99c: r2 = Null
    //     0x79b99c: mov             x2, NULL
    // 0x79b9a0: r0 = layer=()
    //     0x79b9a0: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0x79b9a4: ldur            x1, [fp, #-0x18]
    // 0x79b9a8: r0 = LoadClassIdInstr(r1)
    //     0x79b9a8: ldur            x0, [x1, #-1]
    //     0x79b9ac: ubfx            x0, x0, #0xc, #0x14
    // 0x79b9b0: cmp             x0, #0xbd6
    // 0x79b9b4: b.ne            #0x79b9c8
    // 0x79b9b8: ldur            x2, [fp, #-0x20]
    // 0x79b9bc: ldur            x3, [fp, #-0x28]
    // 0x79b9c0: r0 = defaultPaint()
    //     0x79b9c0: bl              #0x79ba6c  ; [package:flutter/src/rendering/stack.dart] _RenderStack&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultPaint
    // 0x79b9c4: b               #0x79ba48
    // 0x79b9c8: r0 = _childAtIndex()
    //     0x79b9c8: bl              #0x743d18  ; [package:flutter/src/rendering/stack.dart] RenderIndexedStack::_childAtIndex
    // 0x79b9cc: mov             x3, x0
    // 0x79b9d0: stur            x3, [fp, #-0x10]
    // 0x79b9d4: cmp             w3, NULL
    // 0x79b9d8: b.eq            #0x79ba48
    // 0x79b9dc: LoadField: r4 = r3->field_7
    //     0x79b9dc: ldur            w4, [x3, #7]
    // 0x79b9e0: DecompressPointer r4
    //     0x79b9e0: add             x4, x4, HEAP, lsl #32
    // 0x79b9e4: stur            x4, [fp, #-8]
    // 0x79b9e8: cmp             w4, NULL
    // 0x79b9ec: b.eq            #0x79ba68
    // 0x79b9f0: mov             x0, x4
    // 0x79b9f4: r2 = Null
    //     0x79b9f4: mov             x2, NULL
    // 0x79b9f8: r1 = Null
    //     0x79b9f8: mov             x1, NULL
    // 0x79b9fc: r4 = LoadClassIdInstr(r0)
    //     0x79b9fc: ldur            x4, [x0, #-1]
    //     0x79ba00: ubfx            x4, x4, #0xc, #0x14
    // 0x79ba04: sub             x4, x4, #0xc7a
    // 0x79ba08: cmp             x4, #2
    // 0x79ba0c: b.ls            #0x79ba24
    // 0x79ba10: r8 = StackParentData
    //     0x79ba10: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0x79ba14: ldr             x8, [x8, #0x5c0]
    // 0x79ba18: r3 = Null
    //     0x79ba18: add             x3, PP, #0x44, lsl #12  ; [pp+0x44c70] Null
    //     0x79ba1c: ldr             x3, [x3, #0xc70]
    // 0x79ba20: r0 = DefaultTypeTest()
    //     0x79ba20: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x79ba24: ldur            x0, [fp, #-8]
    // 0x79ba28: LoadField: r1 = r0->field_7
    //     0x79ba28: ldur            w1, [x0, #7]
    // 0x79ba2c: DecompressPointer r1
    //     0x79ba2c: add             x1, x1, HEAP, lsl #32
    // 0x79ba30: ldur            x2, [fp, #-0x28]
    // 0x79ba34: r0 = +()
    //     0x79ba34: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x79ba38: ldur            x1, [fp, #-0x20]
    // 0x79ba3c: ldur            x2, [fp, #-0x10]
    // 0x79ba40: mov             x3, x0
    // 0x79ba44: r0 = paintChild()
    //     0x79ba44: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x79ba48: r0 = Null
    //     0x79ba48: mov             x0, NULL
    // 0x79ba4c: LeaveFrame
    //     0x79ba4c: mov             SP, fp
    //     0x79ba50: ldp             fp, lr, [SP], #0x10
    // 0x79ba54: ret
    //     0x79ba54: ret             
    // 0x79ba58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x79ba58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x79ba5c: b               #0x79b89c
    // 0x79ba60: r9 = _needsCompositing
    //     0x79ba60: ldr             x9, [PP, #0x2c58]  ; [pp+0x2c58] Field <RenderObject._needsCompositing@390266271>: late (offset: 0x38)
    // 0x79ba64: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x79ba64: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x79ba68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x79ba68: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void paintStack(dynamic, PaintingContext, Offset) {
    // ** addr: 0x79bbd8, size: 0x44
    // 0x79bbd8: EnterFrame
    //     0x79bbd8: stp             fp, lr, [SP, #-0x10]!
    //     0x79bbdc: mov             fp, SP
    // 0x79bbe0: ldr             x0, [fp, #0x20]
    // 0x79bbe4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x79bbe4: ldur            w1, [x0, #0x17]
    // 0x79bbe8: DecompressPointer r1
    //     0x79bbe8: add             x1, x1, HEAP, lsl #32
    // 0x79bbec: CheckStackOverflow
    //     0x79bbec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79bbf0: cmp             SP, x16
    //     0x79bbf4: b.ls            #0x79bc14
    // 0x79bbf8: ldr             x2, [fp, #0x18]
    // 0x79bbfc: ldr             x3, [fp, #0x10]
    // 0x79bc00: r0 = defaultPaint()
    //     0x79bc00: bl              #0x79ba6c  ; [package:flutter/src/rendering/stack.dart] _RenderStack&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultPaint
    // 0x79bc04: r0 = Null
    //     0x79bc04: mov             x0, NULL
    // 0x79bc08: LeaveFrame
    //     0x79bc08: mov             SP, fp
    //     0x79bc0c: ldp             fp, lr, [SP], #0x10
    // 0x79bc10: ret
    //     0x79bc10: ret             
    // 0x79bc14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x79bc14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x79bc18: b               #0x79bbf8
  }
  _ dispose(/* No info */) {
    // ** addr: 0x7a1428, size: 0x50
    // 0x7a1428: EnterFrame
    //     0x7a1428: stp             fp, lr, [SP, #-0x10]!
    //     0x7a142c: mov             fp, SP
    // 0x7a1430: AllocStack(0x8)
    //     0x7a1430: sub             SP, SP, #8
    // 0x7a1434: SetupParameters(RenderStack this /* r1 => r0, fp-0x8 */)
    //     0x7a1434: mov             x0, x1
    //     0x7a1438: stur            x1, [fp, #-8]
    // 0x7a143c: CheckStackOverflow
    //     0x7a143c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a1440: cmp             SP, x16
    //     0x7a1444: b.ls            #0x7a1470
    // 0x7a1448: LoadField: r1 = r0->field_7f
    //     0x7a1448: ldur            w1, [x0, #0x7f]
    // 0x7a144c: DecompressPointer r1
    //     0x7a144c: add             x1, x1, HEAP, lsl #32
    // 0x7a1450: r2 = Null
    //     0x7a1450: mov             x2, NULL
    // 0x7a1454: r0 = layer=()
    //     0x7a1454: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0x7a1458: ldur            x1, [fp, #-8]
    // 0x7a145c: r0 = dispose()
    //     0x7a145c: bl              #0x7a17a8  ; [package:flutter/src/rendering/object.dart] RenderObject::dispose
    // 0x7a1460: r0 = Null
    //     0x7a1460: mov             x0, NULL
    // 0x7a1464: LeaveFrame
    //     0x7a1464: mov             SP, fp
    //     0x7a1468: ldp             fp, lr, [SP], #0x10
    // 0x7a146c: ret
    //     0x7a146c: ret             
    // 0x7a1470: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a1470: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a1474: b               #0x7a1448
  }
  _ describeApproximatePaintClip(/* No info */) {
    // ** addr: 0x7d7fa4, size: 0x78
    // 0x7d7fa4: EnterFrame
    //     0x7d7fa4: stp             fp, lr, [SP, #-0x10]!
    //     0x7d7fa8: mov             fp, SP
    // 0x7d7fac: CheckStackOverflow
    //     0x7d7fac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d7fb0: cmp             SP, x16
    //     0x7d7fb4: b.ls            #0x7d8014
    // 0x7d7fb8: LoadField: r0 = r1->field_7b
    //     0x7d7fb8: ldur            w0, [x1, #0x7b]
    // 0x7d7fbc: DecompressPointer r0
    //     0x7d7fbc: add             x0, x0, HEAP, lsl #32
    // 0x7d7fc0: LoadField: r2 = r0->field_7
    //     0x7d7fc0: ldur            x2, [x0, #7]
    // 0x7d7fc4: cmp             x2, #1
    // 0x7d7fc8: b.gt            #0x7d7fe4
    // 0x7d7fcc: cmp             x2, #0
    // 0x7d7fd0: b.gt            #0x7d7fe4
    // 0x7d7fd4: r0 = Null
    //     0x7d7fd4: mov             x0, NULL
    // 0x7d7fd8: LeaveFrame
    //     0x7d7fd8: mov             SP, fp
    //     0x7d7fdc: ldp             fp, lr, [SP], #0x10
    // 0x7d7fe0: ret
    //     0x7d7fe0: ret             
    // 0x7d7fe4: LoadField: r0 = r1->field_67
    //     0x7d7fe4: ldur            w0, [x1, #0x67]
    // 0x7d7fe8: DecompressPointer r0
    //     0x7d7fe8: add             x0, x0, HEAP, lsl #32
    // 0x7d7fec: tbnz            w0, #4, #0x7d8004
    // 0x7d7ff0: r0 = size()
    //     0x7d7ff0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7d7ff4: mov             x2, x0
    // 0x7d7ff8: r1 = Instance_Offset
    //     0x7d7ff8: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7d7ffc: r0 = &()
    //     0x7d7ffc: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7d8000: b               #0x7d8008
    // 0x7d8004: r0 = Null
    //     0x7d8004: mov             x0, NULL
    // 0x7d8008: LeaveFrame
    //     0x7d8008: mov             SP, fp
    //     0x7d800c: ldp             fp, lr, [SP], #0x10
    // 0x7d8010: ret
    //     0x7d8010: ret             
    // 0x7d8014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d8014: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d8018: b               #0x7d7fb8
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x7fed84, size: 0x2c
    // 0x7fed84: EnterFrame
    //     0x7fed84: stp             fp, lr, [SP, #-0x10]!
    //     0x7fed88: mov             fp, SP
    // 0x7fed8c: CheckStackOverflow
    //     0x7fed8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fed90: cmp             SP, x16
    //     0x7fed94: b.ls            #0x7feda8
    // 0x7fed98: r0 = defaultHitTestChildren()
    //     0x7fed98: bl              #0x7fedb0  ; [package:flutter/src/rendering/stack.dart] _RenderStack&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultHitTestChildren
    // 0x7fed9c: LeaveFrame
    //     0x7fed9c: mov             SP, fp
    //     0x7feda0: ldp             fp, lr, [SP], #0x10
    // 0x7feda4: ret
    //     0x7feda4: ret             
    // 0x7feda8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7feda8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fedac: b               #0x7fed98
  }
  _ RenderStack(/* No info */) {
    // ** addr: 0x85688c, size: 0x138
    // 0x85688c: EnterFrame
    //     0x85688c: stp             fp, lr, [SP, #-0x10]!
    //     0x856890: mov             fp, SP
    // 0x856894: AllocStack(0x28)
    //     0x856894: sub             SP, SP, #0x28
    // 0x856898: r0 = false
    //     0x856898: add             x0, NULL, #0x30  ; false
    // 0x85689c: mov             x4, x3
    // 0x8568a0: stur            x3, [fp, #-0x18]
    // 0x8568a4: mov             x3, x5
    // 0x8568a8: stur            x5, [fp, #-0x20]
    // 0x8568ac: mov             x5, x2
    // 0x8568b0: stur            x2, [fp, #-0x10]
    // 0x8568b4: mov             x2, x6
    // 0x8568b8: stur            x6, [fp, #-0x28]
    // 0x8568bc: mov             x6, x1
    // 0x8568c0: stur            x1, [fp, #-8]
    // 0x8568c4: CheckStackOverflow
    //     0x8568c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8568c8: cmp             SP, x16
    //     0x8568cc: b.ls            #0x8569bc
    // 0x8568d0: StoreField: r6->field_67 = r0
    //     0x8568d0: stur            w0, [x6, #0x67]
    // 0x8568d4: r1 = <ClipRectLayer>
    //     0x8568d4: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a558] TypeArguments: <ClipRectLayer>
    //     0x8568d8: ldr             x1, [x1, #0x558]
    // 0x8568dc: r0 = LayerHandle()
    //     0x8568dc: bl              #0x6d28c4  ; AllocateLayerHandleStub -> LayerHandle<X0 bound Layer> (size=0x10)
    // 0x8568e0: ldur            x1, [fp, #-8]
    // 0x8568e4: StoreField: r1->field_7f = r0
    //     0x8568e4: stur            w0, [x1, #0x7f]
    //     0x8568e8: ldurb           w16, [x1, #-1]
    //     0x8568ec: ldurb           w17, [x0, #-1]
    //     0x8568f0: and             x16, x17, x16, lsr #2
    //     0x8568f4: tst             x16, HEAP, lsr #32
    //     0x8568f8: b.eq            #0x856900
    //     0x8568fc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856900: ldur            x0, [fp, #-0x10]
    // 0x856904: StoreField: r1->field_6f = r0
    //     0x856904: stur            w0, [x1, #0x6f]
    //     0x856908: ldurb           w16, [x1, #-1]
    //     0x85690c: ldurb           w17, [x0, #-1]
    //     0x856910: and             x16, x17, x16, lsr #2
    //     0x856914: tst             x16, HEAP, lsr #32
    //     0x856918: b.eq            #0x856920
    //     0x85691c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856920: ldur            x0, [fp, #-0x28]
    // 0x856924: StoreField: r1->field_73 = r0
    //     0x856924: stur            w0, [x1, #0x73]
    //     0x856928: ldurb           w16, [x1, #-1]
    //     0x85692c: ldurb           w17, [x0, #-1]
    //     0x856930: and             x16, x17, x16, lsr #2
    //     0x856934: tst             x16, HEAP, lsr #32
    //     0x856938: b.eq            #0x856940
    //     0x85693c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856940: ldur            x0, [fp, #-0x20]
    // 0x856944: StoreField: r1->field_77 = r0
    //     0x856944: stur            w0, [x1, #0x77]
    //     0x856948: ldurb           w16, [x1, #-1]
    //     0x85694c: ldurb           w17, [x0, #-1]
    //     0x856950: and             x16, x17, x16, lsr #2
    //     0x856954: tst             x16, HEAP, lsr #32
    //     0x856958: b.eq            #0x856960
    //     0x85695c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856960: ldur            x0, [fp, #-0x18]
    // 0x856964: StoreField: r1->field_7b = r0
    //     0x856964: stur            w0, [x1, #0x7b]
    //     0x856968: ldurb           w16, [x1, #-1]
    //     0x85696c: ldurb           w17, [x0, #-1]
    //     0x856970: and             x16, x17, x16, lsr #2
    //     0x856974: tst             x16, HEAP, lsr #32
    //     0x856978: b.eq            #0x856980
    //     0x85697c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856980: StoreField: r1->field_57 = rZR
    //     0x856980: stur            xzr, [x1, #0x57]
    // 0x856984: r0 = _LayoutCacheStorage()
    //     0x856984: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x856988: ldur            x1, [fp, #-8]
    // 0x85698c: StoreField: r1->field_4f = r0
    //     0x85698c: stur            w0, [x1, #0x4f]
    //     0x856990: ldurb           w16, [x1, #-1]
    //     0x856994: ldurb           w17, [x0, #-1]
    //     0x856998: and             x16, x17, x16, lsr #2
    //     0x85699c: tst             x16, HEAP, lsr #32
    //     0x8569a0: b.eq            #0x8569a8
    //     0x8569a4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8569a8: r0 = RenderObject()
    //     0x8569a8: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x8569ac: r0 = Null
    //     0x8569ac: mov             x0, NULL
    // 0x8569b0: LeaveFrame
    //     0x8569b0: mov             SP, fp
    //     0x8569b4: ldp             fp, lr, [SP], #0x10
    // 0x8569b8: ret
    //     0x8569b8: ret             
    // 0x8569bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8569bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8569c0: b               #0x8568d0
  }
  set _ textDirection=(/* No info */) {
    // ** addr: 0xc69a04, size: 0x70
    // 0xc69a04: EnterFrame
    //     0xc69a04: stp             fp, lr, [SP, #-0x10]!
    //     0xc69a08: mov             fp, SP
    // 0xc69a0c: mov             x0, x2
    // 0xc69a10: CheckStackOverflow
    //     0xc69a10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc69a14: cmp             SP, x16
    //     0xc69a18: b.ls            #0xc69a6c
    // 0xc69a1c: LoadField: r2 = r1->field_73
    //     0xc69a1c: ldur            w2, [x1, #0x73]
    // 0xc69a20: DecompressPointer r2
    //     0xc69a20: add             x2, x2, HEAP, lsl #32
    // 0xc69a24: cmp             w2, w0
    // 0xc69a28: b.ne            #0xc69a3c
    // 0xc69a2c: r0 = Null
    //     0xc69a2c: mov             x0, NULL
    // 0xc69a30: LeaveFrame
    //     0xc69a30: mov             SP, fp
    //     0xc69a34: ldp             fp, lr, [SP], #0x10
    // 0xc69a38: ret
    //     0xc69a38: ret             
    // 0xc69a3c: StoreField: r1->field_73 = r0
    //     0xc69a3c: stur            w0, [x1, #0x73]
    //     0xc69a40: ldurb           w16, [x1, #-1]
    //     0xc69a44: ldurb           w17, [x0, #-1]
    //     0xc69a48: and             x16, x17, x16, lsr #2
    //     0xc69a4c: tst             x16, HEAP, lsr #32
    //     0xc69a50: b.eq            #0xc69a58
    //     0xc69a54: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc69a58: r0 = _markNeedResolution()
    //     0xc69a58: bl              #0xc69a74  ; [package:flutter/src/rendering/stack.dart] RenderStack::_markNeedResolution
    // 0xc69a5c: r0 = Null
    //     0xc69a5c: mov             x0, NULL
    // 0xc69a60: LeaveFrame
    //     0xc69a60: mov             SP, fp
    //     0xc69a64: ldp             fp, lr, [SP], #0x10
    // 0xc69a68: ret
    //     0xc69a68: ret             
    // 0xc69a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc69a6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc69a70: b               #0xc69a1c
  }
  _ _markNeedResolution(/* No info */) {
    // ** addr: 0xc69a74, size: 0x34
    // 0xc69a74: EnterFrame
    //     0xc69a74: stp             fp, lr, [SP, #-0x10]!
    //     0xc69a78: mov             fp, SP
    // 0xc69a7c: CheckStackOverflow
    //     0xc69a7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc69a80: cmp             SP, x16
    //     0xc69a84: b.ls            #0xc69aa0
    // 0xc69a88: StoreField: r1->field_6b = rNULL
    //     0xc69a88: stur            NULL, [x1, #0x6b]
    // 0xc69a8c: r0 = markNeedsLayout()
    //     0xc69a8c: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc69a90: r0 = Null
    //     0xc69a90: mov             x0, NULL
    // 0xc69a94: LeaveFrame
    //     0xc69a94: mov             SP, fp
    //     0xc69a98: ldp             fp, lr, [SP], #0x10
    // 0xc69a9c: ret
    //     0xc69a9c: ret             
    // 0xc69aa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc69aa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc69aa4: b               #0xc69a88
  }
  set _ alignment=(/* No info */) {
    // ** addr: 0xc69aa8, size: 0x88
    // 0xc69aa8: EnterFrame
    //     0xc69aa8: stp             fp, lr, [SP, #-0x10]!
    //     0xc69aac: mov             fp, SP
    // 0xc69ab0: AllocStack(0x20)
    //     0xc69ab0: sub             SP, SP, #0x20
    // 0xc69ab4: SetupParameters(RenderStack this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc69ab4: mov             x0, x2
    //     0xc69ab8: stur            x1, [fp, #-8]
    //     0xc69abc: stur            x2, [fp, #-0x10]
    // 0xc69ac0: CheckStackOverflow
    //     0xc69ac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc69ac4: cmp             SP, x16
    //     0xc69ac8: b.ls            #0xc69b28
    // 0xc69acc: LoadField: r2 = r1->field_6f
    //     0xc69acc: ldur            w2, [x1, #0x6f]
    // 0xc69ad0: DecompressPointer r2
    //     0xc69ad0: add             x2, x2, HEAP, lsl #32
    // 0xc69ad4: stp             x0, x2, [SP]
    // 0xc69ad8: r0 = ==()
    //     0xc69ad8: bl              #0xd5fe90  ; [package:flutter/src/painting/alignment.dart] AlignmentGeometry::==
    // 0xc69adc: tbnz            w0, #4, #0xc69af0
    // 0xc69ae0: r0 = Null
    //     0xc69ae0: mov             x0, NULL
    // 0xc69ae4: LeaveFrame
    //     0xc69ae4: mov             SP, fp
    //     0xc69ae8: ldp             fp, lr, [SP], #0x10
    // 0xc69aec: ret
    //     0xc69aec: ret             
    // 0xc69af0: ldur            x1, [fp, #-8]
    // 0xc69af4: ldur            x0, [fp, #-0x10]
    // 0xc69af8: StoreField: r1->field_6f = r0
    //     0xc69af8: stur            w0, [x1, #0x6f]
    //     0xc69afc: ldurb           w16, [x1, #-1]
    //     0xc69b00: ldurb           w17, [x0, #-1]
    //     0xc69b04: and             x16, x17, x16, lsr #2
    //     0xc69b08: tst             x16, HEAP, lsr #32
    //     0xc69b0c: b.eq            #0xc69b14
    //     0xc69b10: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc69b14: r0 = _markNeedResolution()
    //     0xc69b14: bl              #0xc69a74  ; [package:flutter/src/rendering/stack.dart] RenderStack::_markNeedResolution
    // 0xc69b18: r0 = Null
    //     0xc69b18: mov             x0, NULL
    // 0xc69b1c: LeaveFrame
    //     0xc69b1c: mov             SP, fp
    //     0xc69b20: ldp             fp, lr, [SP], #0x10
    // 0xc69b24: ret
    //     0xc69b24: ret             
    // 0xc69b28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc69b28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc69b2c: b               #0xc69acc
  }
  set _ clipBehavior=(/* No info */) {
    // ** addr: 0xc69b30, size: 0x78
    // 0xc69b30: EnterFrame
    //     0xc69b30: stp             fp, lr, [SP, #-0x10]!
    //     0xc69b34: mov             fp, SP
    // 0xc69b38: AllocStack(0x8)
    //     0xc69b38: sub             SP, SP, #8
    // 0xc69b3c: SetupParameters(RenderStack this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0 */)
    //     0xc69b3c: mov             x0, x2
    //     0xc69b40: mov             x2, x1
    //     0xc69b44: stur            x1, [fp, #-8]
    // 0xc69b48: CheckStackOverflow
    //     0xc69b48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc69b4c: cmp             SP, x16
    //     0xc69b50: b.ls            #0xc69ba0
    // 0xc69b54: LoadField: r1 = r2->field_7b
    //     0xc69b54: ldur            w1, [x2, #0x7b]
    // 0xc69b58: DecompressPointer r1
    //     0xc69b58: add             x1, x1, HEAP, lsl #32
    // 0xc69b5c: cmp             w0, w1
    // 0xc69b60: b.eq            #0xc69b90
    // 0xc69b64: StoreField: r2->field_7b = r0
    //     0xc69b64: stur            w0, [x2, #0x7b]
    //     0xc69b68: ldurb           w16, [x2, #-1]
    //     0xc69b6c: ldurb           w17, [x0, #-1]
    //     0xc69b70: and             x16, x17, x16, lsr #2
    //     0xc69b74: tst             x16, HEAP, lsr #32
    //     0xc69b78: b.eq            #0xc69b80
    //     0xc69b7c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc69b80: mov             x1, x2
    // 0xc69b84: r0 = markNeedsPaint()
    //     0xc69b84: bl              #0x786214  ; [package:flutter/src/rendering/object.dart] RenderObject::markNeedsPaint
    // 0xc69b88: ldur            x1, [fp, #-8]
    // 0xc69b8c: r0 = markNeedsSemanticsUpdate()
    //     0xc69b8c: bl              #0x649b5c  ; [package:flutter/src/rendering/object.dart] RenderObject::markNeedsSemanticsUpdate
    // 0xc69b90: r0 = Null
    //     0xc69b90: mov             x0, NULL
    // 0xc69b94: LeaveFrame
    //     0xc69b94: mov             SP, fp
    //     0xc69b98: ldp             fp, lr, [SP], #0x10
    // 0xc69b9c: ret
    //     0xc69b9c: ret             
    // 0xc69ba0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc69ba0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc69ba4: b               #0xc69b54
  }
}

// class id: 3031, size: 0x8c, field offset: 0x84
class RenderIndexedStack extends RenderStack {

  _ computeDryBaseline(/* No info */) {
    // ** addr: 0x743838, size: 0x14c
    // 0x743838: EnterFrame
    //     0x743838: stp             fp, lr, [SP, #-0x10]!
    //     0x74383c: mov             fp, SP
    // 0x743840: AllocStack(0x38)
    //     0x743840: sub             SP, SP, #0x38
    // 0x743844: SetupParameters(RenderIndexedStack this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r6, fp-0x18 */)
    //     0x743844: mov             x4, x1
    //     0x743848: mov             x6, x3
    //     0x74384c: stur            x3, [fp, #-0x18]
    //     0x743850: mov             x3, x2
    //     0x743854: stur            x1, [fp, #-8]
    //     0x743858: stur            x2, [fp, #-0x10]
    // 0x74385c: CheckStackOverflow
    //     0x74385c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x743860: cmp             SP, x16
    //     0x743864: b.ls            #0x74397c
    // 0x743868: mov             x0, x3
    // 0x74386c: r2 = Null
    //     0x74386c: mov             x2, NULL
    // 0x743870: r1 = Null
    //     0x743870: mov             x1, NULL
    // 0x743874: r4 = 60
    //     0x743874: movz            x4, #0x3c
    // 0x743878: branchIfSmi(r0, 0x743884)
    //     0x743878: tbz             w0, #0, #0x743884
    // 0x74387c: r4 = LoadClassIdInstr(r0)
    //     0x74387c: ldur            x4, [x0, #-1]
    //     0x743880: ubfx            x4, x4, #0xc, #0x14
    // 0x743884: sub             x4, x4, #0xc83
    // 0x743888: cmp             x4, #1
    // 0x74388c: b.ls            #0x7438a0
    // 0x743890: r8 = BoxConstraints
    //     0x743890: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x743894: r3 = Null
    //     0x743894: add             x3, PP, #0x59, lsl #12  ; [pp+0x592a8] Null
    //     0x743898: ldr             x3, [x3, #0x2a8]
    // 0x74389c: r0 = BoxConstraints()
    //     0x74389c: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x7438a0: ldur            x1, [fp, #-8]
    // 0x7438a4: r0 = _childAtIndex()
    //     0x7438a4: bl              #0x743d18  ; [package:flutter/src/rendering/stack.dart] RenderIndexedStack::_childAtIndex
    // 0x7438a8: stur            x0, [fp, #-0x20]
    // 0x7438ac: cmp             w0, NULL
    // 0x7438b0: b.ne            #0x7438c4
    // 0x7438b4: r0 = Null
    //     0x7438b4: mov             x0, NULL
    // 0x7438b8: LeaveFrame
    //     0x7438b8: mov             SP, fp
    //     0x7438bc: ldp             fp, lr, [SP], #0x10
    // 0x7438c0: ret
    //     0x7438c0: ret             
    // 0x7438c4: ldur            x2, [fp, #-8]
    // 0x7438c8: LoadField: r1 = r2->field_77
    //     0x7438c8: ldur            w1, [x2, #0x77]
    // 0x7438cc: DecompressPointer r1
    //     0x7438cc: add             x1, x1, HEAP, lsl #32
    // 0x7438d0: LoadField: r3 = r1->field_7
    //     0x7438d0: ldur            x3, [x1, #7]
    // 0x7438d4: cmp             x3, #1
    // 0x7438d8: b.gt            #0x743938
    // 0x7438dc: cmp             x3, #0
    // 0x7438e0: b.gt            #0x7438f4
    // 0x7438e4: ldur            x1, [fp, #-0x10]
    // 0x7438e8: r0 = loosen()
    //     0x7438e8: bl              #0x73cf04  ; [package:flutter/src/rendering/box.dart] BoxConstraints::loosen
    // 0x7438ec: mov             x3, x0
    // 0x7438f0: b               #0x74393c
    // 0x7438f4: ldur            x1, [fp, #-0x10]
    // 0x7438f8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7438f8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7438fc: r0 = constrainWidth()
    //     0x7438fc: bl              #0x6cea58  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainWidth
    // 0x743900: ldur            x1, [fp, #-0x10]
    // 0x743904: stur            d0, [fp, #-0x30]
    // 0x743908: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x743908: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x74390c: r0 = constrainHeight()
    //     0x74390c: bl              #0x6ce9e4  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainHeight
    // 0x743910: stur            d0, [fp, #-0x38]
    // 0x743914: r0 = BoxConstraints()
    //     0x743914: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x743918: ldur            d0, [fp, #-0x30]
    // 0x74391c: StoreField: r0->field_7 = d0
    //     0x74391c: stur            d0, [x0, #7]
    // 0x743920: StoreField: r0->field_f = d0
    //     0x743920: stur            d0, [x0, #0xf]
    // 0x743924: ldur            d0, [fp, #-0x38]
    // 0x743928: ArrayStore: r0[0] = d0  ; List_8
    //     0x743928: stur            d0, [x0, #0x17]
    // 0x74392c: StoreField: r0->field_1f = d0
    //     0x74392c: stur            d0, [x0, #0x1f]
    // 0x743930: mov             x3, x0
    // 0x743934: b               #0x74393c
    // 0x743938: ldur            x3, [fp, #-0x10]
    // 0x74393c: ldur            x1, [fp, #-8]
    // 0x743940: stur            x3, [fp, #-0x28]
    // 0x743944: r0 = _resolvedAlignment()
    //     0x743944: bl              #0x743ba4  ; [package:flutter/src/rendering/stack.dart] RenderStack::_resolvedAlignment
    // 0x743948: ldur            x1, [fp, #-8]
    // 0x74394c: ldur            x2, [fp, #-0x10]
    // 0x743950: stur            x0, [fp, #-8]
    // 0x743954: r0 = getDryLayout()
    //     0x743954: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x743958: ldur            x1, [fp, #-0x20]
    // 0x74395c: mov             x2, x0
    // 0x743960: ldur            x3, [fp, #-0x28]
    // 0x743964: ldur            x5, [fp, #-8]
    // 0x743968: ldur            x6, [fp, #-0x18]
    // 0x74396c: r0 = _baselineForChild()
    //     0x74396c: bl              #0x743984  ; [package:flutter/src/rendering/stack.dart] RenderStack::_baselineForChild
    // 0x743970: LeaveFrame
    //     0x743970: mov             SP, fp
    //     0x743974: ldp             fp, lr, [SP], #0x10
    // 0x743978: ret
    //     0x743978: ret             
    // 0x74397c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74397c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x743980: b               #0x743868
  }
  _ _childAtIndex(/* No info */) {
    // ** addr: 0x743d18, size: 0xbc
    // 0x743d18: EnterFrame
    //     0x743d18: stp             fp, lr, [SP, #-0x10]!
    //     0x743d1c: mov             fp, SP
    // 0x743d20: AllocStack(0x18)
    //     0x743d20: sub             SP, SP, #0x18
    // 0x743d24: LoadField: r3 = r1->field_83
    //     0x743d24: ldur            x3, [x1, #0x83]
    // 0x743d28: stur            x3, [fp, #-0x18]
    // 0x743d2c: LoadField: r0 = r1->field_5f
    //     0x743d2c: ldur            w0, [x1, #0x5f]
    // 0x743d30: DecompressPointer r0
    //     0x743d30: add             x0, x0, HEAP, lsl #32
    // 0x743d34: r4 = 0
    //     0x743d34: movz            x4, #0
    // 0x743d38: stur            x4, [fp, #-0x10]
    // 0x743d3c: CheckStackOverflow
    //     0x743d3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x743d40: cmp             SP, x16
    //     0x743d44: b.ls            #0x743dc8
    // 0x743d48: cmp             x4, x3
    // 0x743d4c: b.ge            #0x743dbc
    // 0x743d50: cmp             w0, NULL
    // 0x743d54: b.eq            #0x743dbc
    // 0x743d58: LoadField: r5 = r0->field_7
    //     0x743d58: ldur            w5, [x0, #7]
    // 0x743d5c: DecompressPointer r5
    //     0x743d5c: add             x5, x5, HEAP, lsl #32
    // 0x743d60: stur            x5, [fp, #-8]
    // 0x743d64: cmp             w5, NULL
    // 0x743d68: b.eq            #0x743dd0
    // 0x743d6c: mov             x0, x5
    // 0x743d70: r2 = Null
    //     0x743d70: mov             x2, NULL
    // 0x743d74: r1 = Null
    //     0x743d74: mov             x1, NULL
    // 0x743d78: r4 = LoadClassIdInstr(r0)
    //     0x743d78: ldur            x4, [x0, #-1]
    //     0x743d7c: ubfx            x4, x4, #0xc, #0x14
    // 0x743d80: sub             x4, x4, #0xc7a
    // 0x743d84: cmp             x4, #2
    // 0x743d88: b.ls            #0x743da0
    // 0x743d8c: r8 = StackParentData
    //     0x743d8c: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0x743d90: ldr             x8, [x8, #0x5c0]
    // 0x743d94: r3 = Null
    //     0x743d94: add             x3, PP, #0x44, lsl #12  ; [pp+0x44c90] Null
    //     0x743d98: ldr             x3, [x3, #0xc90]
    // 0x743d9c: r0 = DefaultTypeTest()
    //     0x743d9c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x743da0: ldur            x1, [fp, #-8]
    // 0x743da4: LoadField: r0 = r1->field_13
    //     0x743da4: ldur            w0, [x1, #0x13]
    // 0x743da8: DecompressPointer r0
    //     0x743da8: add             x0, x0, HEAP, lsl #32
    // 0x743dac: ldur            x1, [fp, #-0x10]
    // 0x743db0: add             x4, x1, #1
    // 0x743db4: ldur            x3, [fp, #-0x18]
    // 0x743db8: b               #0x743d38
    // 0x743dbc: LeaveFrame
    //     0x743dbc: mov             SP, fp
    //     0x743dc0: ldp             fp, lr, [SP], #0x10
    // 0x743dc4: ret
    //     0x743dc4: ret             
    // 0x743dc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x743dc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x743dcc: b               #0x743d48
    // 0x743dd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x743dd0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ computeDistanceToActualBaseline(/* No info */) {
    // ** addr: 0x74c938, size: 0xc4
    // 0x74c938: EnterFrame
    //     0x74c938: stp             fp, lr, [SP, #-0x10]!
    //     0x74c93c: mov             fp, SP
    // 0x74c940: AllocStack(0x18)
    //     0x74c940: sub             SP, SP, #0x18
    // 0x74c944: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x74c944: stur            x2, [fp, #-8]
    // 0x74c948: CheckStackOverflow
    //     0x74c948: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74c94c: cmp             SP, x16
    //     0x74c950: b.ls            #0x74c9f0
    // 0x74c954: r0 = _childAtIndex()
    //     0x74c954: bl              #0x743d18  ; [package:flutter/src/rendering/stack.dart] RenderIndexedStack::_childAtIndex
    // 0x74c958: mov             x3, x0
    // 0x74c95c: stur            x3, [fp, #-0x18]
    // 0x74c960: cmp             w3, NULL
    // 0x74c964: b.ne            #0x74c978
    // 0x74c968: r0 = Null
    //     0x74c968: mov             x0, NULL
    // 0x74c96c: LeaveFrame
    //     0x74c96c: mov             SP, fp
    //     0x74c970: ldp             fp, lr, [SP], #0x10
    // 0x74c974: ret
    //     0x74c974: ret             
    // 0x74c978: LoadField: r4 = r3->field_7
    //     0x74c978: ldur            w4, [x3, #7]
    // 0x74c97c: DecompressPointer r4
    //     0x74c97c: add             x4, x4, HEAP, lsl #32
    // 0x74c980: stur            x4, [fp, #-0x10]
    // 0x74c984: cmp             w4, NULL
    // 0x74c988: b.eq            #0x74c9f8
    // 0x74c98c: mov             x0, x4
    // 0x74c990: r2 = Null
    //     0x74c990: mov             x2, NULL
    // 0x74c994: r1 = Null
    //     0x74c994: mov             x1, NULL
    // 0x74c998: r4 = LoadClassIdInstr(r0)
    //     0x74c998: ldur            x4, [x0, #-1]
    //     0x74c99c: ubfx            x4, x4, #0xc, #0x14
    // 0x74c9a0: sub             x4, x4, #0xc7a
    // 0x74c9a4: cmp             x4, #2
    // 0x74c9a8: b.ls            #0x74c9c0
    // 0x74c9ac: r8 = StackParentData
    //     0x74c9ac: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0x74c9b0: ldr             x8, [x8, #0x5c0]
    // 0x74c9b4: r3 = Null
    //     0x74c9b4: add             x3, PP, #0x59, lsl #12  ; [pp+0x592b8] Null
    //     0x74c9b8: ldr             x3, [x3, #0x2b8]
    // 0x74c9bc: r0 = DefaultTypeTest()
    //     0x74c9bc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x74c9c0: ldur            x1, [fp, #-0x18]
    // 0x74c9c4: ldur            x2, [fp, #-8]
    // 0x74c9c8: r0 = getDistanceToActualBaseline()
    //     0x74c9c8: bl              #0x74b4d4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDistanceToActualBaseline
    // 0x74c9cc: mov             x1, x0
    // 0x74c9d0: ldur            x0, [fp, #-0x10]
    // 0x74c9d4: LoadField: r2 = r0->field_7
    //     0x74c9d4: ldur            w2, [x0, #7]
    // 0x74c9d8: DecompressPointer r2
    //     0x74c9d8: add             x2, x2, HEAP, lsl #32
    // 0x74c9dc: LoadField: d0 = r2->field_f
    //     0x74c9dc: ldur            d0, [x2, #0xf]
    // 0x74c9e0: r0 = BaselineOffset.+()
    //     0x74c9e0: bl              #0x73d964  ; [package:flutter/src/rendering/box.dart] ::BaselineOffset.+
    // 0x74c9e4: LeaveFrame
    //     0x74c9e4: mov             SP, fp
    //     0x74c9e8: ldp             fp, lr, [SP], #0x10
    // 0x74c9ec: ret
    //     0x74c9ec: ret             
    // 0x74c9f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74c9f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74c9f4: b               #0x74c954
    // 0x74c9f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74c9f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void paintStack(dynamic, PaintingContext, Offset) {
    // ** addr: 0x79bb98, size: 0x40
    // 0x79bb98: EnterFrame
    //     0x79bb98: stp             fp, lr, [SP, #-0x10]!
    //     0x79bb9c: mov             fp, SP
    // 0x79bba0: ldr             x0, [fp, #0x20]
    // 0x79bba4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x79bba4: ldur            w1, [x0, #0x17]
    // 0x79bba8: DecompressPointer r1
    //     0x79bba8: add             x1, x1, HEAP, lsl #32
    // 0x79bbac: CheckStackOverflow
    //     0x79bbac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79bbb0: cmp             SP, x16
    //     0x79bbb4: b.ls            #0x79bbd0
    // 0x79bbb8: ldr             x2, [fp, #0x18]
    // 0x79bbbc: ldr             x3, [fp, #0x10]
    // 0x79bbc0: r0 = paintStack()
    //     0x79bbc0: bl              #0xda995c  ; [package:flutter/src/rendering/stack.dart] RenderIndexedStack::paintStack
    // 0x79bbc4: LeaveFrame
    //     0x79bbc4: mov             SP, fp
    //     0x79bbc8: ldp             fp, lr, [SP], #0x10
    // 0x79bbcc: ret
    //     0x79bbcc: ret             
    // 0x79bbd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x79bbd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x79bbd4: b               #0x79bbb8
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x7fec90, size: 0xf4
    // 0x7fec90: EnterFrame
    //     0x7fec90: stp             fp, lr, [SP, #-0x10]!
    //     0x7fec94: mov             fp, SP
    // 0x7fec98: AllocStack(0x28)
    //     0x7fec98: sub             SP, SP, #0x28
    // 0x7fec9c: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r5, fp-0x10 */)
    //     0x7fec9c: mov             x0, x2
    //     0x7feca0: mov             x5, x3
    //     0x7feca4: stur            x2, [fp, #-8]
    //     0x7feca8: stur            x3, [fp, #-0x10]
    // 0x7fecac: CheckStackOverflow
    //     0x7fecac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fecb0: cmp             SP, x16
    //     0x7fecb4: b.ls            #0x7fed78
    // 0x7fecb8: r0 = _childAtIndex()
    //     0x7fecb8: bl              #0x743d18  ; [package:flutter/src/rendering/stack.dart] RenderIndexedStack::_childAtIndex
    // 0x7fecbc: stur            x0, [fp, #-0x18]
    // 0x7fecc0: r1 = 1
    //     0x7fecc0: movz            x1, #0x1
    // 0x7fecc4: r0 = AllocateContext()
    //     0x7fecc4: bl              #0xec126c  ; AllocateContextStub
    // 0x7fecc8: mov             x3, x0
    // 0x7feccc: ldur            x0, [fp, #-0x18]
    // 0x7fecd0: stur            x3, [fp, #-0x28]
    // 0x7fecd4: StoreField: r3->field_f = r0
    //     0x7fecd4: stur            w0, [x3, #0xf]
    // 0x7fecd8: cmp             w0, NULL
    // 0x7fecdc: b.ne            #0x7fecf0
    // 0x7fece0: r0 = false
    //     0x7fece0: add             x0, NULL, #0x30  ; false
    // 0x7fece4: LeaveFrame
    //     0x7fece4: mov             SP, fp
    //     0x7fece8: ldp             fp, lr, [SP], #0x10
    // 0x7fecec: ret
    //     0x7fecec: ret             
    // 0x7fecf0: LoadField: r4 = r0->field_7
    //     0x7fecf0: ldur            w4, [x0, #7]
    // 0x7fecf4: DecompressPointer r4
    //     0x7fecf4: add             x4, x4, HEAP, lsl #32
    // 0x7fecf8: stur            x4, [fp, #-0x20]
    // 0x7fecfc: cmp             w4, NULL
    // 0x7fed00: b.eq            #0x7fed80
    // 0x7fed04: mov             x0, x4
    // 0x7fed08: r2 = Null
    //     0x7fed08: mov             x2, NULL
    // 0x7fed0c: r1 = Null
    //     0x7fed0c: mov             x1, NULL
    // 0x7fed10: r4 = LoadClassIdInstr(r0)
    //     0x7fed10: ldur            x4, [x0, #-1]
    //     0x7fed14: ubfx            x4, x4, #0xc, #0x14
    // 0x7fed18: sub             x4, x4, #0xc7a
    // 0x7fed1c: cmp             x4, #2
    // 0x7fed20: b.ls            #0x7fed38
    // 0x7fed24: r8 = StackParentData
    //     0x7fed24: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0x7fed28: ldr             x8, [x8, #0x5c0]
    // 0x7fed2c: r3 = Null
    //     0x7fed2c: add             x3, PP, #0x59, lsl #12  ; [pp+0x59290] Null
    //     0x7fed30: ldr             x3, [x3, #0x290]
    // 0x7fed34: r0 = DefaultTypeTest()
    //     0x7fed34: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7fed38: ldur            x0, [fp, #-0x20]
    // 0x7fed3c: LoadField: r3 = r0->field_7
    //     0x7fed3c: ldur            w3, [x0, #7]
    // 0x7fed40: DecompressPointer r3
    //     0x7fed40: add             x3, x3, HEAP, lsl #32
    // 0x7fed44: ldur            x2, [fp, #-0x28]
    // 0x7fed48: stur            x3, [fp, #-0x18]
    // 0x7fed4c: r1 = Function '<anonymous closure>':.
    //     0x7fed4c: add             x1, PP, #0x59, lsl #12  ; [pp+0x592a0] AnonymousClosure: (0x7fcdd0), in [package:flutter/src/rendering/shifted_box.dart] RenderShiftedBox::hitTestChildren (0x7fccd4)
    //     0x7fed50: ldr             x1, [x1, #0x2a0]
    // 0x7fed54: r0 = AllocateClosure()
    //     0x7fed54: bl              #0xec1630  ; AllocateClosureStub
    // 0x7fed58: ldur            x1, [fp, #-8]
    // 0x7fed5c: mov             x2, x0
    // 0x7fed60: ldur            x3, [fp, #-0x18]
    // 0x7fed64: ldur            x5, [fp, #-0x10]
    // 0x7fed68: r0 = addWithPaintOffset()
    //     0x7fed68: bl              #0x7fc1f8  ; [package:flutter/src/rendering/box.dart] BoxHitTestResult::addWithPaintOffset
    // 0x7fed6c: LeaveFrame
    //     0x7fed6c: mov             SP, fp
    //     0x7fed70: ldp             fp, lr, [SP], #0x10
    // 0x7fed74: ret
    //     0x7fed74: ret             
    // 0x7fed78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fed78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fed7c: b               #0x7fecb8
    // 0x7fed80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fed80: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitChildrenForSemantics(/* No info */) {
    // ** addr: 0x801078, size: 0x5c
    // 0x801078: EnterFrame
    //     0x801078: stp             fp, lr, [SP, #-0x10]!
    //     0x80107c: mov             fp, SP
    // 0x801080: AllocStack(0x18)
    //     0x801080: sub             SP, SP, #0x18
    // 0x801084: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x801084: mov             x0, x2
    //     0x801088: stur            x2, [fp, #-8]
    // 0x80108c: CheckStackOverflow
    //     0x80108c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x801090: cmp             SP, x16
    //     0x801094: b.ls            #0x8010cc
    // 0x801098: r0 = _childAtIndex()
    //     0x801098: bl              #0x743d18  ; [package:flutter/src/rendering/stack.dart] RenderIndexedStack::_childAtIndex
    // 0x80109c: cmp             w0, NULL
    // 0x8010a0: b.eq            #0x8010bc
    // 0x8010a4: ldur            x16, [fp, #-8]
    // 0x8010a8: stp             x0, x16, [SP]
    // 0x8010ac: ldur            x0, [fp, #-8]
    // 0x8010b0: ClosureCall
    //     0x8010b0: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x8010b4: ldur            x2, [x0, #0x1f]
    //     0x8010b8: blr             x2
    // 0x8010bc: r0 = Null
    //     0x8010bc: mov             x0, NULL
    // 0x8010c0: LeaveFrame
    //     0x8010c0: mov             SP, fp
    //     0x8010c4: ldp             fp, lr, [SP], #0x10
    // 0x8010c8: ret
    //     0x8010c8: ret             
    // 0x8010cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8010cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8010d0: b               #0x801098
  }
  set _ index=(/* No info */) {
    // ** addr: 0xc69ba8, size: 0x40
    // 0xc69ba8: EnterFrame
    //     0xc69ba8: stp             fp, lr, [SP, #-0x10]!
    //     0xc69bac: mov             fp, SP
    // 0xc69bb0: CheckStackOverflow
    //     0xc69bb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc69bb4: cmp             SP, x16
    //     0xc69bb8: b.ls            #0xc69be0
    // 0xc69bbc: LoadField: r0 = r1->field_83
    //     0xc69bbc: ldur            x0, [x1, #0x83]
    // 0xc69bc0: cmp             x0, x2
    // 0xc69bc4: b.eq            #0xc69bd0
    // 0xc69bc8: StoreField: r1->field_83 = r2
    //     0xc69bc8: stur            x2, [x1, #0x83]
    // 0xc69bcc: r0 = markNeedsLayout()
    //     0xc69bcc: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc69bd0: r0 = Null
    //     0xc69bd0: mov             x0, NULL
    // 0xc69bd4: LeaveFrame
    //     0xc69bd4: mov             SP, fp
    //     0xc69bd8: ldp             fp, lr, [SP], #0x10
    // 0xc69bdc: ret
    //     0xc69bdc: ret             
    // 0xc69be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc69be0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc69be4: b               #0xc69bbc
  }
  _ paintStack(/* No info */) {
    // ** addr: 0xda995c, size: 0xd4
    // 0xda995c: EnterFrame
    //     0xda995c: stp             fp, lr, [SP, #-0x10]!
    //     0xda9960: mov             fp, SP
    // 0xda9964: AllocStack(0x20)
    //     0xda9964: sub             SP, SP, #0x20
    // 0xda9968: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r2, fp-0x10 */)
    //     0xda9968: mov             x0, x2
    //     0xda996c: stur            x2, [fp, #-8]
    //     0xda9970: mov             x2, x3
    //     0xda9974: stur            x3, [fp, #-0x10]
    // 0xda9978: CheckStackOverflow
    //     0xda9978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xda997c: cmp             SP, x16
    //     0xda9980: b.ls            #0xda9a24
    // 0xda9984: r0 = _childAtIndex()
    //     0xda9984: bl              #0x743d18  ; [package:flutter/src/rendering/stack.dart] RenderIndexedStack::_childAtIndex
    // 0xda9988: mov             x3, x0
    // 0xda998c: stur            x3, [fp, #-0x20]
    // 0xda9990: cmp             w3, NULL
    // 0xda9994: b.ne            #0xda99a8
    // 0xda9998: r0 = Null
    //     0xda9998: mov             x0, NULL
    // 0xda999c: LeaveFrame
    //     0xda999c: mov             SP, fp
    //     0xda99a0: ldp             fp, lr, [SP], #0x10
    // 0xda99a4: ret
    //     0xda99a4: ret             
    // 0xda99a8: LoadField: r4 = r3->field_7
    //     0xda99a8: ldur            w4, [x3, #7]
    // 0xda99ac: DecompressPointer r4
    //     0xda99ac: add             x4, x4, HEAP, lsl #32
    // 0xda99b0: stur            x4, [fp, #-0x18]
    // 0xda99b4: cmp             w4, NULL
    // 0xda99b8: b.eq            #0xda9a2c
    // 0xda99bc: mov             x0, x4
    // 0xda99c0: r2 = Null
    //     0xda99c0: mov             x2, NULL
    // 0xda99c4: r1 = Null
    //     0xda99c4: mov             x1, NULL
    // 0xda99c8: r4 = LoadClassIdInstr(r0)
    //     0xda99c8: ldur            x4, [x0, #-1]
    //     0xda99cc: ubfx            x4, x4, #0xc, #0x14
    // 0xda99d0: sub             x4, x4, #0xc7a
    // 0xda99d4: cmp             x4, #2
    // 0xda99d8: b.ls            #0xda99f0
    // 0xda99dc: r8 = StackParentData
    //     0xda99dc: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5c0] Type: StackParentData
    //     0xda99e0: ldr             x8, [x8, #0x5c0]
    // 0xda99e4: r3 = Null
    //     0xda99e4: add             x3, PP, #0x44, lsl #12  ; [pp+0x44c80] Null
    //     0xda99e8: ldr             x3, [x3, #0xc80]
    // 0xda99ec: r0 = DefaultTypeTest()
    //     0xda99ec: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda99f0: ldur            x0, [fp, #-0x18]
    // 0xda99f4: LoadField: r1 = r0->field_7
    //     0xda99f4: ldur            w1, [x0, #7]
    // 0xda99f8: DecompressPointer r1
    //     0xda99f8: add             x1, x1, HEAP, lsl #32
    // 0xda99fc: ldur            x2, [fp, #-0x10]
    // 0xda9a00: r0 = +()
    //     0xda9a00: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0xda9a04: ldur            x1, [fp, #-8]
    // 0xda9a08: ldur            x2, [fp, #-0x20]
    // 0xda9a0c: mov             x3, x0
    // 0xda9a10: r0 = paintChild()
    //     0xda9a10: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0xda9a14: r0 = Null
    //     0xda9a14: mov             x0, NULL
    // 0xda9a18: LeaveFrame
    //     0xda9a18: mov             SP, fp
    //     0xda9a1c: ldp             fp, lr, [SP], #0x10
    // 0xda9a20: ret
    //     0xda9a20: ret             
    // 0xda9a24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xda9a24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xda9a28: b               #0xda9984
    // 0xda9a2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda9a2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3194, size: 0x30, field offset: 0x18
class StackParentData extends ContainerBoxParentData<dynamic> {

  _ positionedChildConstraints(/* No info */) {
    // ** addr: 0x73c628, size: 0x2e8
    // 0x73c628: EnterFrame
    //     0x73c628: stp             fp, lr, [SP, #-0x10]!
    //     0x73c62c: mov             fp, SP
    // 0x73c630: AllocStack(0x18)
    //     0x73c630: sub             SP, SP, #0x18
    // 0x73c634: LoadField: r0 = r1->field_23
    //     0x73c634: ldur            w0, [x1, #0x23]
    // 0x73c638: DecompressPointer r0
    //     0x73c638: add             x0, x0, HEAP, lsl #32
    // 0x73c63c: LoadField: r3 = r1->field_1b
    //     0x73c63c: ldur            w3, [x1, #0x1b]
    // 0x73c640: DecompressPointer r3
    //     0x73c640: add             x3, x3, HEAP, lsl #32
    // 0x73c644: cmp             w0, NULL
    // 0x73c648: b.eq            #0x73c694
    // 0x73c64c: cmp             w3, NULL
    // 0x73c650: b.eq            #0x73c694
    // 0x73c654: LoadField: d0 = r2->field_7
    //     0x73c654: ldur            d0, [x2, #7]
    // 0x73c658: LoadField: d1 = r3->field_7
    //     0x73c658: ldur            d1, [x3, #7]
    // 0x73c65c: fsub            d2, d0, d1
    // 0x73c660: LoadField: d0 = r0->field_7
    //     0x73c660: ldur            d0, [x0, #7]
    // 0x73c664: fsub            d1, d2, d0
    // 0x73c668: r0 = inline_Allocate_Double()
    //     0x73c668: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x73c66c: add             x0, x0, #0x10
    //     0x73c670: cmp             x3, x0
    //     0x73c674: b.ls            #0x73c8a8
    //     0x73c678: str             x0, [THR, #0x50]  ; THR::top
    //     0x73c67c: sub             x0, x0, #0xf
    //     0x73c680: movz            x3, #0xe15c
    //     0x73c684: movk            x3, #0x3, lsl #16
    //     0x73c688: stur            x3, [x0, #-1]
    // 0x73c68c: StoreField: r0->field_7 = d1
    //     0x73c68c: stur            d1, [x0, #7]
    // 0x73c690: b               #0x73c69c
    // 0x73c694: LoadField: r0 = r1->field_27
    //     0x73c694: ldur            w0, [x1, #0x27]
    // 0x73c698: DecompressPointer r0
    //     0x73c698: add             x0, x0, HEAP, lsl #32
    // 0x73c69c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x73c69c: ldur            w3, [x1, #0x17]
    // 0x73c6a0: DecompressPointer r3
    //     0x73c6a0: add             x3, x3, HEAP, lsl #32
    // 0x73c6a4: LoadField: r4 = r1->field_1f
    //     0x73c6a4: ldur            w4, [x1, #0x1f]
    // 0x73c6a8: DecompressPointer r4
    //     0x73c6a8: add             x4, x4, HEAP, lsl #32
    // 0x73c6ac: cmp             w3, NULL
    // 0x73c6b0: b.eq            #0x73c6fc
    // 0x73c6b4: cmp             w4, NULL
    // 0x73c6b8: b.eq            #0x73c6fc
    // 0x73c6bc: LoadField: d0 = r2->field_f
    //     0x73c6bc: ldur            d0, [x2, #0xf]
    // 0x73c6c0: LoadField: d1 = r4->field_7
    //     0x73c6c0: ldur            d1, [x4, #7]
    // 0x73c6c4: fsub            d2, d0, d1
    // 0x73c6c8: LoadField: d0 = r3->field_7
    //     0x73c6c8: ldur            d0, [x3, #7]
    // 0x73c6cc: fsub            d1, d2, d0
    // 0x73c6d0: r1 = inline_Allocate_Double()
    //     0x73c6d0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x73c6d4: add             x1, x1, #0x10
    //     0x73c6d8: cmp             x2, x1
    //     0x73c6dc: b.ls            #0x73c8c0
    //     0x73c6e0: str             x1, [THR, #0x50]  ; THR::top
    //     0x73c6e4: sub             x1, x1, #0xf
    //     0x73c6e8: movz            x2, #0xe15c
    //     0x73c6ec: movk            x2, #0x3, lsl #16
    //     0x73c6f0: stur            x2, [x1, #-1]
    // 0x73c6f4: StoreField: r1->field_7 = d1
    //     0x73c6f4: stur            d1, [x1, #7]
    // 0x73c6f8: b               #0x73c708
    // 0x73c6fc: LoadField: r2 = r1->field_2b
    //     0x73c6fc: ldur            w2, [x1, #0x2b]
    // 0x73c700: DecompressPointer r2
    //     0x73c700: add             x2, x2, HEAP, lsl #32
    // 0x73c704: mov             x1, x2
    // 0x73c708: cmp             w0, NULL
    // 0x73c70c: b.ne            #0x73c71c
    // 0x73c710: r0 = Null
    //     0x73c710: mov             x0, NULL
    // 0x73c714: d0 = 0.000000
    //     0x73c714: eor             v0.16b, v0.16b, v0.16b
    // 0x73c718: b               #0x73c798
    // 0x73c71c: d0 = 0.000000
    //     0x73c71c: eor             v0.16b, v0.16b, v0.16b
    // 0x73c720: LoadField: d1 = r0->field_7
    //     0x73c720: ldur            d1, [x0, #7]
    // 0x73c724: fcmp            d0, d1
    // 0x73c728: b.le            #0x73c734
    // 0x73c72c: d1 = 0.000000
    //     0x73c72c: eor             v1.16b, v1.16b, v1.16b
    // 0x73c730: b               #0x73c770
    // 0x73c734: fcmp            d1, d0
    // 0x73c738: b.le            #0x73c744
    // 0x73c73c: LoadField: d1 = r0->field_7
    //     0x73c73c: ldur            d1, [x0, #7]
    // 0x73c740: b               #0x73c770
    // 0x73c744: fcmp            d0, d0
    // 0x73c748: b.ne            #0x73c758
    // 0x73c74c: fadd            d2, d1, d0
    // 0x73c750: mov             v1.16b, v2.16b
    // 0x73c754: b               #0x73c770
    // 0x73c758: LoadField: d1 = r0->field_7
    //     0x73c758: ldur            d1, [x0, #7]
    // 0x73c75c: fcmp            d1, d1
    // 0x73c760: b.vc            #0x73c76c
    // 0x73c764: LoadField: d1 = r0->field_7
    //     0x73c764: ldur            d1, [x0, #7]
    // 0x73c768: b               #0x73c770
    // 0x73c76c: d1 = 0.000000
    //     0x73c76c: eor             v1.16b, v1.16b, v1.16b
    // 0x73c770: r0 = inline_Allocate_Double()
    //     0x73c770: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x73c774: add             x0, x0, #0x10
    //     0x73c778: cmp             x2, x0
    //     0x73c77c: b.ls            #0x73c8dc
    //     0x73c780: str             x0, [THR, #0x50]  ; THR::top
    //     0x73c784: sub             x0, x0, #0xf
    //     0x73c788: movz            x2, #0xe15c
    //     0x73c78c: movk            x2, #0x3, lsl #16
    //     0x73c790: stur            x2, [x0, #-1]
    // 0x73c794: StoreField: r0->field_7 = d1
    //     0x73c794: stur            d1, [x0, #7]
    // 0x73c798: stur            x0, [fp, #-0x10]
    // 0x73c79c: cmp             w1, NULL
    // 0x73c7a0: b.ne            #0x73c7ac
    // 0x73c7a4: r1 = Null
    //     0x73c7a4: mov             x1, NULL
    // 0x73c7a8: b               #0x73c824
    // 0x73c7ac: LoadField: d1 = r1->field_7
    //     0x73c7ac: ldur            d1, [x1, #7]
    // 0x73c7b0: fcmp            d0, d1
    // 0x73c7b4: b.le            #0x73c7c0
    // 0x73c7b8: d0 = 0.000000
    //     0x73c7b8: eor             v0.16b, v0.16b, v0.16b
    // 0x73c7bc: b               #0x73c7fc
    // 0x73c7c0: fcmp            d1, d0
    // 0x73c7c4: b.le            #0x73c7d0
    // 0x73c7c8: LoadField: d0 = r1->field_7
    //     0x73c7c8: ldur            d0, [x1, #7]
    // 0x73c7cc: b               #0x73c7fc
    // 0x73c7d0: fcmp            d0, d0
    // 0x73c7d4: b.ne            #0x73c7e4
    // 0x73c7d8: fadd            d2, d1, d0
    // 0x73c7dc: mov             v0.16b, v2.16b
    // 0x73c7e0: b               #0x73c7fc
    // 0x73c7e4: LoadField: d0 = r1->field_7
    //     0x73c7e4: ldur            d0, [x1, #7]
    // 0x73c7e8: fcmp            d0, d0
    // 0x73c7ec: b.vc            #0x73c7f8
    // 0x73c7f0: LoadField: d0 = r1->field_7
    //     0x73c7f0: ldur            d0, [x1, #7]
    // 0x73c7f4: b               #0x73c7fc
    // 0x73c7f8: d0 = 0.000000
    //     0x73c7f8: eor             v0.16b, v0.16b, v0.16b
    // 0x73c7fc: r1 = inline_Allocate_Double()
    //     0x73c7fc: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x73c800: add             x1, x1, #0x10
    //     0x73c804: cmp             x2, x1
    //     0x73c808: b.ls            #0x73c8f4
    //     0x73c80c: str             x1, [THR, #0x50]  ; THR::top
    //     0x73c810: sub             x1, x1, #0xf
    //     0x73c814: movz            x2, #0xe15c
    //     0x73c818: movk            x2, #0x3, lsl #16
    //     0x73c81c: stur            x2, [x1, #-1]
    // 0x73c820: StoreField: r1->field_7 = d0
    //     0x73c820: stur            d0, [x1, #7]
    // 0x73c824: stur            x1, [fp, #-8]
    // 0x73c828: cmp             w0, NULL
    // 0x73c82c: b.ne            #0x73c838
    // 0x73c830: d0 = 0.000000
    //     0x73c830: eor             v0.16b, v0.16b, v0.16b
    // 0x73c834: b               #0x73c83c
    // 0x73c838: LoadField: d0 = r0->field_7
    //     0x73c838: ldur            d0, [x0, #7]
    // 0x73c83c: stur            d0, [fp, #-0x18]
    // 0x73c840: r0 = BoxConstraints()
    //     0x73c840: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x73c844: ldur            d0, [fp, #-0x18]
    // 0x73c848: StoreField: r0->field_7 = d0
    //     0x73c848: stur            d0, [x0, #7]
    // 0x73c84c: ldur            x1, [fp, #-0x10]
    // 0x73c850: cmp             w1, NULL
    // 0x73c854: b.ne            #0x73c860
    // 0x73c858: d0 = inf
    //     0x73c858: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x73c85c: b               #0x73c864
    // 0x73c860: LoadField: d0 = r1->field_7
    //     0x73c860: ldur            d0, [x1, #7]
    // 0x73c864: ldur            x1, [fp, #-8]
    // 0x73c868: StoreField: r0->field_f = d0
    //     0x73c868: stur            d0, [x0, #0xf]
    // 0x73c86c: cmp             w1, NULL
    // 0x73c870: b.ne            #0x73c87c
    // 0x73c874: d0 = 0.000000
    //     0x73c874: eor             v0.16b, v0.16b, v0.16b
    // 0x73c878: b               #0x73c880
    // 0x73c87c: LoadField: d0 = r1->field_7
    //     0x73c87c: ldur            d0, [x1, #7]
    // 0x73c880: ArrayStore: r0[0] = d0  ; List_8
    //     0x73c880: stur            d0, [x0, #0x17]
    // 0x73c884: cmp             w1, NULL
    // 0x73c888: b.ne            #0x73c894
    // 0x73c88c: d0 = inf
    //     0x73c88c: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x73c890: b               #0x73c898
    // 0x73c894: LoadField: d0 = r1->field_7
    //     0x73c894: ldur            d0, [x1, #7]
    // 0x73c898: StoreField: r0->field_1f = d0
    //     0x73c898: stur            d0, [x0, #0x1f]
    // 0x73c89c: LeaveFrame
    //     0x73c89c: mov             SP, fp
    //     0x73c8a0: ldp             fp, lr, [SP], #0x10
    // 0x73c8a4: ret
    //     0x73c8a4: ret             
    // 0x73c8a8: SaveReg d1
    //     0x73c8a8: str             q1, [SP, #-0x10]!
    // 0x73c8ac: stp             x1, x2, [SP, #-0x10]!
    // 0x73c8b0: r0 = AllocateDouble()
    //     0x73c8b0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x73c8b4: ldp             x1, x2, [SP], #0x10
    // 0x73c8b8: RestoreReg d1
    //     0x73c8b8: ldr             q1, [SP], #0x10
    // 0x73c8bc: b               #0x73c68c
    // 0x73c8c0: SaveReg d1
    //     0x73c8c0: str             q1, [SP, #-0x10]!
    // 0x73c8c4: SaveReg r0
    //     0x73c8c4: str             x0, [SP, #-8]!
    // 0x73c8c8: r0 = AllocateDouble()
    //     0x73c8c8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x73c8cc: mov             x1, x0
    // 0x73c8d0: RestoreReg r0
    //     0x73c8d0: ldr             x0, [SP], #8
    // 0x73c8d4: RestoreReg d1
    //     0x73c8d4: ldr             q1, [SP], #0x10
    // 0x73c8d8: b               #0x73c6f4
    // 0x73c8dc: stp             q0, q1, [SP, #-0x20]!
    // 0x73c8e0: SaveReg r1
    //     0x73c8e0: str             x1, [SP, #-8]!
    // 0x73c8e4: r0 = AllocateDouble()
    //     0x73c8e4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x73c8e8: RestoreReg r1
    //     0x73c8e8: ldr             x1, [SP], #8
    // 0x73c8ec: ldp             q0, q1, [SP], #0x20
    // 0x73c8f0: b               #0x73c794
    // 0x73c8f4: SaveReg d0
    //     0x73c8f4: str             q0, [SP, #-0x10]!
    // 0x73c8f8: SaveReg r0
    //     0x73c8f8: str             x0, [SP, #-8]!
    // 0x73c8fc: r0 = AllocateDouble()
    //     0x73c8fc: bl              #0xec2254  ; AllocateDoubleStub
    // 0x73c900: mov             x1, x0
    // 0x73c904: RestoreReg r0
    //     0x73c904: ldr             x0, [SP], #8
    // 0x73c908: RestoreReg d0
    //     0x73c908: ldr             q0, [SP], #0x10
    // 0x73c90c: b               #0x73c820
  }
}

// class id: 7000, size: 0x14, field offset: 0x14
enum StackFit extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc49bfc, size: 0x64
    // 0xc49bfc: EnterFrame
    //     0xc49bfc: stp             fp, lr, [SP, #-0x10]!
    //     0xc49c00: mov             fp, SP
    // 0xc49c04: AllocStack(0x10)
    //     0xc49c04: sub             SP, SP, #0x10
    // 0xc49c08: SetupParameters(StackFit this /* r1 => r0, fp-0x8 */)
    //     0xc49c08: mov             x0, x1
    //     0xc49c0c: stur            x1, [fp, #-8]
    // 0xc49c10: CheckStackOverflow
    //     0xc49c10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc49c14: cmp             SP, x16
    //     0xc49c18: b.ls            #0xc49c58
    // 0xc49c1c: r1 = Null
    //     0xc49c1c: mov             x1, NULL
    // 0xc49c20: r2 = 4
    //     0xc49c20: movz            x2, #0x4
    // 0xc49c24: r0 = AllocateArray()
    //     0xc49c24: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc49c28: r16 = "StackFit."
    //     0xc49c28: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c88] "StackFit."
    //     0xc49c2c: ldr             x16, [x16, #0xc88]
    // 0xc49c30: StoreField: r0->field_f = r16
    //     0xc49c30: stur            w16, [x0, #0xf]
    // 0xc49c34: ldur            x1, [fp, #-8]
    // 0xc49c38: LoadField: r2 = r1->field_f
    //     0xc49c38: ldur            w2, [x1, #0xf]
    // 0xc49c3c: DecompressPointer r2
    //     0xc49c3c: add             x2, x2, HEAP, lsl #32
    // 0xc49c40: StoreField: r0->field_13 = r2
    //     0xc49c40: stur            w2, [x0, #0x13]
    // 0xc49c44: str             x0, [SP]
    // 0xc49c48: r0 = _interpolate()
    //     0xc49c48: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc49c4c: LeaveFrame
    //     0xc49c4c: mov             SP, fp
    //     0xc49c50: ldp             fp, lr, [SP], #0x10
    // 0xc49c54: ret
    //     0xc49c54: ret             
    // 0xc49c58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc49c58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc49c5c: b               #0xc49c1c
  }
}
