// lib: , url: package:flutter/src/rendering/wrap.dart

// class id: 1049054, size: 0x8
class :: {

  static _ _AxisSize.applyConstraints(/* No info */) {
    // ** addr: 0x7466e8, size: 0x38
    // 0x7466e8: EnterFrame
    //     0x7466e8: stp             fp, lr, [SP, #-0x10]!
    //     0x7466ec: mov             fp, SP
    // 0x7466f0: mov             x16, x2
    // 0x7466f4: mov             x2, x1
    // 0x7466f8: mov             x1, x16
    // 0x7466fc: CheckStackOverflow
    //     0x7466fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x746700: cmp             SP, x16
    //     0x746704: b.ls            #0x746718
    // 0x746708: r0 = constrain()
    //     0x746708: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x74670c: LeaveFrame
    //     0x74670c: mov             SP, fp
    //     0x746710: ldp             fp, lr, [SP], #0x10
    // 0x746714: ret
    //     0x746714: ret             
    // 0x746718: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x746718: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74671c: b               #0x746708
  }
  static _ _AxisSize.flipped(/* No info */) {
    // ** addr: 0x746ab4, size: 0x2c
    // 0x746ab4: EnterFrame
    //     0x746ab4: stp             fp, lr, [SP, #-0x10]!
    //     0x746ab8: mov             fp, SP
    // 0x746abc: CheckStackOverflow
    //     0x746abc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x746ac0: cmp             SP, x16
    //     0x746ac4: b.ls            #0x746ad8
    // 0x746ac8: r0 = flipped()
    //     0x746ac8: bl              #0x732a78  ; [dart:ui] Size::flipped
    // 0x746acc: LeaveFrame
    //     0x746acc: mov             SP, fp
    //     0x746ad0: ldp             fp, lr, [SP], #0x10
    // 0x746ad4: ret
    //     0x746ad4: ret             
    // 0x746ad8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x746ad8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x746adc: b               #0x746ac8
  }
  static _ _AxisSize.(/* No info */) {
    // ** addr: 0x746ae0, size: 0x2c
    // 0x746ae0: EnterFrame
    //     0x746ae0: stp             fp, lr, [SP, #-0x10]!
    //     0x746ae4: mov             fp, SP
    // 0x746ae8: AllocStack(0x8)
    //     0x746ae8: sub             SP, SP, #8
    // 0x746aec: SetupParameters(dynamic _ /* d0 => d0, fp-0x8 */)
    //     0x746aec: stur            d0, [fp, #-8]
    // 0x746af0: r0 = Size()
    //     0x746af0: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x746af4: ldur            d0, [fp, #-8]
    // 0x746af8: StoreField: r0->field_7 = d0
    //     0x746af8: stur            d0, [x0, #7]
    // 0x746afc: StoreField: r0->field_f = rZR
    //     0x746afc: stur            xzr, [x0, #0xf]
    // 0x746b00: LeaveFrame
    //     0x746b00: mov             SP, fp
    //     0x746b04: ldp             fp, lr, [SP], #0x10
    // 0x746b08: ret
    //     0x746b08: ret             
  }
  static _ _AxisSize.-(/* No info */) {
    // ** addr: 0x77911c, size: 0x4c
    // 0x77911c: EnterFrame
    //     0x77911c: stp             fp, lr, [SP, #-0x10]!
    //     0x779120: mov             fp, SP
    // 0x779124: AllocStack(0x10)
    //     0x779124: sub             SP, SP, #0x10
    // 0x779128: LoadField: d0 = r1->field_7
    //     0x779128: ldur            d0, [x1, #7]
    // 0x77912c: LoadField: d1 = r2->field_7
    //     0x77912c: ldur            d1, [x2, #7]
    // 0x779130: fsub            d2, d0, d1
    // 0x779134: stur            d2, [fp, #-0x10]
    // 0x779138: LoadField: d0 = r1->field_f
    //     0x779138: ldur            d0, [x1, #0xf]
    // 0x77913c: LoadField: d1 = r2->field_f
    //     0x77913c: ldur            d1, [x2, #0xf]
    // 0x779140: fsub            d3, d0, d1
    // 0x779144: stur            d3, [fp, #-8]
    // 0x779148: r0 = Size()
    //     0x779148: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x77914c: ldur            d0, [fp, #-0x10]
    // 0x779150: StoreField: r0->field_7 = d0
    //     0x779150: stur            d0, [x0, #7]
    // 0x779154: ldur            d0, [fp, #-8]
    // 0x779158: StoreField: r0->field_f = d0
    //     0x779158: stur            d0, [x0, #0xf]
    // 0x77915c: LeaveFrame
    //     0x77915c: mov             SP, fp
    //     0x779160: ldp             fp, lr, [SP], #0x10
    // 0x779164: ret
    //     0x779164: ret             
  }
}

// class id: 2856, size: 0x18, field offset: 0x8
class _RunMetrics extends Object {

  _ tryAddingNewChild(/* No info */) {
    // ** addr: 0x746b0c, size: 0x11c
    // 0x746b0c: EnterFrame
    //     0x746b0c: stp             fp, lr, [SP, #-0x10]!
    //     0x746b10: mov             fp, SP
    // 0x746b14: AllocStack(0x28)
    //     0x746b14: sub             SP, SP, #0x28
    // 0x746b18: d2 = 0.000000
    //     0x746b18: ldr             d2, [PP, #0x6fc8]  ; [pp+0x6fc8] IMM: double(1e-10) from 0x3ddb7cdfd9d7bdbb
    // 0x746b1c: mov             x0, x2
    // 0x746b20: stur            x2, [fp, #-8]
    // 0x746b24: mov             x2, x1
    // 0x746b28: stur            x1, [fp, #-0x20]
    // 0x746b2c: mov             x1, x3
    // 0x746b30: stur            x3, [fp, #-0x10]
    // 0x746b34: stur            x5, [fp, #-0x28]
    // 0x746b38: CheckStackOverflow
    //     0x746b38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x746b3c: cmp             SP, x16
    //     0x746b40: b.ls            #0x746c20
    // 0x746b44: LoadField: r3 = r2->field_7
    //     0x746b44: ldur            w3, [x2, #7]
    // 0x746b48: DecompressPointer r3
    //     0x746b48: add             x3, x3, HEAP, lsl #32
    // 0x746b4c: stur            x3, [fp, #-0x18]
    // 0x746b50: LoadField: d3 = r3->field_7
    //     0x746b50: ldur            d3, [x3, #7]
    // 0x746b54: LoadField: d4 = r1->field_7
    //     0x746b54: ldur            d4, [x1, #7]
    // 0x746b58: fadd            d5, d3, d4
    // 0x746b5c: fadd            d3, d5, d0
    // 0x746b60: fsub            d4, d3, d1
    // 0x746b64: fcmp            d4, d2
    // 0x746b68: b.le            #0x746b9c
    // 0x746b6c: r0 = _RunMetrics()
    //     0x746b6c: bl              #0x746c48  ; Allocate_RunMetricsStub -> _RunMetrics (size=0x18)
    // 0x746b70: mov             x1, x0
    // 0x746b74: r0 = 1
    //     0x746b74: movz            x0, #0x1
    // 0x746b78: StoreField: r1->field_b = r0
    //     0x746b78: stur            x0, [x1, #0xb]
    // 0x746b7c: ldur            x0, [fp, #-8]
    // 0x746b80: StoreField: r1->field_13 = r0
    //     0x746b80: stur            w0, [x1, #0x13]
    // 0x746b84: ldur            x4, [fp, #-0x10]
    // 0x746b88: StoreField: r1->field_7 = r4
    //     0x746b88: stur            w4, [x1, #7]
    // 0x746b8c: mov             x0, x1
    // 0x746b90: LeaveFrame
    //     0x746b90: mov             SP, fp
    //     0x746b94: ldp             fp, lr, [SP], #0x10
    // 0x746b98: ret
    //     0x746b98: ret             
    // 0x746b9c: mov             x4, x1
    // 0x746ba0: r0 = _AxisSize.()
    //     0x746ba0: bl              #0x746ae0  ; [package:flutter/src/rendering/wrap.dart] ::_AxisSize.
    // 0x746ba4: ldur            x1, [fp, #-0x10]
    // 0x746ba8: mov             x2, x0
    // 0x746bac: r0 = _AxisSize.+()
    //     0x746bac: bl              #0x7323a8  ; [package:flutter/src/rendering/flex.dart] ::_AxisSize.+
    // 0x746bb0: ldur            x1, [fp, #-0x18]
    // 0x746bb4: mov             x2, x0
    // 0x746bb8: r0 = _AxisSize.+()
    //     0x746bb8: bl              #0x7323a8  ; [package:flutter/src/rendering/flex.dart] ::_AxisSize.+
    // 0x746bbc: ldur            x1, [fp, #-0x20]
    // 0x746bc0: StoreField: r1->field_7 = r0
    //     0x746bc0: stur            w0, [x1, #7]
    //     0x746bc4: ldurb           w16, [x1, #-1]
    //     0x746bc8: ldurb           w17, [x0, #-1]
    //     0x746bcc: and             x16, x17, x16, lsr #2
    //     0x746bd0: tst             x16, HEAP, lsr #32
    //     0x746bd4: b.eq            #0x746bdc
    //     0x746bd8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x746bdc: LoadField: r2 = r1->field_b
    //     0x746bdc: ldur            x2, [x1, #0xb]
    // 0x746be0: add             x3, x2, #1
    // 0x746be4: StoreField: r1->field_b = r3
    //     0x746be4: stur            x3, [x1, #0xb]
    // 0x746be8: ldur            x2, [fp, #-0x28]
    // 0x746bec: tbnz            w2, #4, #0x746c10
    // 0x746bf0: ldur            x0, [fp, #-8]
    // 0x746bf4: StoreField: r1->field_13 = r0
    //     0x746bf4: stur            w0, [x1, #0x13]
    //     0x746bf8: ldurb           w16, [x1, #-1]
    //     0x746bfc: ldurb           w17, [x0, #-1]
    //     0x746c00: and             x16, x17, x16, lsr #2
    //     0x746c04: tst             x16, HEAP, lsr #32
    //     0x746c08: b.eq            #0x746c10
    //     0x746c0c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x746c10: r0 = Null
    //     0x746c10: mov             x0, NULL
    // 0x746c14: LeaveFrame
    //     0x746c14: mov             SP, fp
    //     0x746c18: ldp             fp, lr, [SP], #0x10
    // 0x746c1c: ret
    //     0x746c1c: ret             
    // 0x746c20: r0 = StackOverflowSharedWithFPURegs()
    //     0x746c20: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x746c24: b               #0x746b44
  }
}

// class id: 3014, size: 0x68, field offset: 0x58
//   transformed mixin,
abstract class _RenderWrap&RenderBox&ContainerRenderObjectMixin extends RenderBox
     with ContainerRenderObjectMixin<X0 bound RenderObject, X1 bound ContainerParentDataMixin> {

  [closure] RenderBox? childAfter(dynamic, Object?) {
    // ** addr: 0x734b64, size: 0xa8
    // 0x734b64: EnterFrame
    //     0x734b64: stp             fp, lr, [SP, #-0x10]!
    //     0x734b68: mov             fp, SP
    // 0x734b6c: AllocStack(0x8)
    //     0x734b6c: sub             SP, SP, #8
    // 0x734b70: ldr             x0, [fp, #0x10]
    // 0x734b74: r2 = Null
    //     0x734b74: mov             x2, NULL
    // 0x734b78: r1 = Null
    //     0x734b78: mov             x1, NULL
    // 0x734b7c: r4 = 60
    //     0x734b7c: movz            x4, #0x3c
    // 0x734b80: branchIfSmi(r0, 0x734b8c)
    //     0x734b80: tbz             w0, #0, #0x734b8c
    // 0x734b84: r4 = LoadClassIdInstr(r0)
    //     0x734b84: ldur            x4, [x0, #-1]
    //     0x734b88: ubfx            x4, x4, #0xc, #0x14
    // 0x734b8c: sub             x4, x4, #0xbba
    // 0x734b90: cmp             x4, #0x9a
    // 0x734b94: b.ls            #0x734ba8
    // 0x734b98: r8 = RenderBox
    //     0x734b98: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x734b9c: r3 = Null
    //     0x734b9c: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b60] Null
    //     0x734ba0: ldr             x3, [x3, #0xb60]
    // 0x734ba4: r0 = RenderBox()
    //     0x734ba4: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x734ba8: ldr             x0, [fp, #0x10]
    // 0x734bac: LoadField: r3 = r0->field_7
    //     0x734bac: ldur            w3, [x0, #7]
    // 0x734bb0: DecompressPointer r3
    //     0x734bb0: add             x3, x3, HEAP, lsl #32
    // 0x734bb4: stur            x3, [fp, #-8]
    // 0x734bb8: cmp             w3, NULL
    // 0x734bbc: b.eq            #0x734c08
    // 0x734bc0: mov             x0, x3
    // 0x734bc4: r2 = Null
    //     0x734bc4: mov             x2, NULL
    // 0x734bc8: r1 = Null
    //     0x734bc8: mov             x1, NULL
    // 0x734bcc: r4 = LoadClassIdInstr(r0)
    //     0x734bcc: ldur            x4, [x0, #-1]
    //     0x734bd0: ubfx            x4, x4, #0xc, #0x14
    // 0x734bd4: cmp             x4, #0xc79
    // 0x734bd8: b.eq            #0x734bf0
    // 0x734bdc: r8 = WrapParentData
    //     0x734bdc: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x734be0: ldr             x8, [x8, #0xaf0]
    // 0x734be4: r3 = Null
    //     0x734be4: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b70] Null
    //     0x734be8: ldr             x3, [x3, #0xb70]
    // 0x734bec: r0 = DefaultTypeTest()
    //     0x734bec: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x734bf0: ldur            x1, [fp, #-8]
    // 0x734bf4: LoadField: r0 = r1->field_13
    //     0x734bf4: ldur            w0, [x1, #0x13]
    // 0x734bf8: DecompressPointer r0
    //     0x734bf8: add             x0, x0, HEAP, lsl #32
    // 0x734bfc: LeaveFrame
    //     0x734bfc: mov             SP, fp
    //     0x734c00: ldp             fp, lr, [SP], #0x10
    // 0x734c04: ret
    //     0x734c04: ret             
    // 0x734c08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x734c08: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RenderBox? childBefore(dynamic, Object?) {
    // ** addr: 0x746640, size: 0xa8
    // 0x746640: EnterFrame
    //     0x746640: stp             fp, lr, [SP, #-0x10]!
    //     0x746644: mov             fp, SP
    // 0x746648: AllocStack(0x8)
    //     0x746648: sub             SP, SP, #8
    // 0x74664c: ldr             x0, [fp, #0x10]
    // 0x746650: r2 = Null
    //     0x746650: mov             x2, NULL
    // 0x746654: r1 = Null
    //     0x746654: mov             x1, NULL
    // 0x746658: r4 = 60
    //     0x746658: movz            x4, #0x3c
    // 0x74665c: branchIfSmi(r0, 0x746668)
    //     0x74665c: tbz             w0, #0, #0x746668
    // 0x746660: r4 = LoadClassIdInstr(r0)
    //     0x746660: ldur            x4, [x0, #-1]
    //     0x746664: ubfx            x4, x4, #0xc, #0x14
    // 0x746668: sub             x4, x4, #0xbba
    // 0x74666c: cmp             x4, #0x9a
    // 0x746670: b.ls            #0x746684
    // 0x746674: r8 = RenderBox
    //     0x746674: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x746678: r3 = Null
    //     0x746678: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b80] Null
    //     0x74667c: ldr             x3, [x3, #0xb80]
    // 0x746680: r0 = RenderBox()
    //     0x746680: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x746684: ldr             x0, [fp, #0x10]
    // 0x746688: LoadField: r3 = r0->field_7
    //     0x746688: ldur            w3, [x0, #7]
    // 0x74668c: DecompressPointer r3
    //     0x74668c: add             x3, x3, HEAP, lsl #32
    // 0x746690: stur            x3, [fp, #-8]
    // 0x746694: cmp             w3, NULL
    // 0x746698: b.eq            #0x7466e4
    // 0x74669c: mov             x0, x3
    // 0x7466a0: r2 = Null
    //     0x7466a0: mov             x2, NULL
    // 0x7466a4: r1 = Null
    //     0x7466a4: mov             x1, NULL
    // 0x7466a8: r4 = LoadClassIdInstr(r0)
    //     0x7466a8: ldur            x4, [x0, #-1]
    //     0x7466ac: ubfx            x4, x4, #0xc, #0x14
    // 0x7466b0: cmp             x4, #0xc79
    // 0x7466b4: b.eq            #0x7466cc
    // 0x7466b8: r8 = WrapParentData
    //     0x7466b8: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x7466bc: ldr             x8, [x8, #0xaf0]
    // 0x7466c0: r3 = Null
    //     0x7466c0: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b90] Null
    //     0x7466c4: ldr             x3, [x3, #0xb90]
    // 0x7466c8: r0 = DefaultTypeTest()
    //     0x7466c8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7466cc: ldur            x1, [fp, #-8]
    // 0x7466d0: LoadField: r0 = r1->field_f
    //     0x7466d0: ldur            w0, [x1, #0xf]
    // 0x7466d4: DecompressPointer r0
    //     0x7466d4: add             x0, x0, HEAP, lsl #32
    // 0x7466d8: LeaveFrame
    //     0x7466d8: mov             SP, fp
    //     0x7466dc: ldp             fp, lr, [SP], #0x10
    // 0x7466e0: ret
    //     0x7466e0: ret             
    // 0x7466e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7466e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ attach(/* No info */) {
    // ** addr: 0x764570, size: 0xfc
    // 0x764570: EnterFrame
    //     0x764570: stp             fp, lr, [SP, #-0x10]!
    //     0x764574: mov             fp, SP
    // 0x764578: AllocStack(0x18)
    //     0x764578: sub             SP, SP, #0x18
    // 0x76457c: SetupParameters(_RenderWrap&RenderBox&ContainerRenderObjectMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x76457c: mov             x3, x1
    //     0x764580: mov             x0, x2
    //     0x764584: stur            x1, [fp, #-8]
    //     0x764588: stur            x2, [fp, #-0x10]
    // 0x76458c: CheckStackOverflow
    //     0x76458c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x764590: cmp             SP, x16
    //     0x764594: b.ls            #0x764658
    // 0x764598: mov             x1, x3
    // 0x76459c: mov             x2, x0
    // 0x7645a0: r0 = attach()
    //     0x7645a0: bl              #0x765268  ; [package:flutter/src/rendering/object.dart] RenderObject::attach
    // 0x7645a4: ldur            x0, [fp, #-8]
    // 0x7645a8: LoadField: r1 = r0->field_5f
    //     0x7645a8: ldur            w1, [x0, #0x5f]
    // 0x7645ac: DecompressPointer r1
    //     0x7645ac: add             x1, x1, HEAP, lsl #32
    // 0x7645b0: mov             x3, x1
    // 0x7645b4: stur            x3, [fp, #-8]
    // 0x7645b8: CheckStackOverflow
    //     0x7645b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7645bc: cmp             SP, x16
    //     0x7645c0: b.ls            #0x764660
    // 0x7645c4: cmp             w3, NULL
    // 0x7645c8: b.eq            #0x764648
    // 0x7645cc: r0 = LoadClassIdInstr(r3)
    //     0x7645cc: ldur            x0, [x3, #-1]
    //     0x7645d0: ubfx            x0, x0, #0xc, #0x14
    // 0x7645d4: mov             x1, x3
    // 0x7645d8: ldur            x2, [fp, #-0x10]
    // 0x7645dc: r0 = GDT[cid_x0 + 0x11974]()
    //     0x7645dc: movz            x17, #0x1974
    //     0x7645e0: movk            x17, #0x1, lsl #16
    //     0x7645e4: add             lr, x0, x17
    //     0x7645e8: ldr             lr, [x21, lr, lsl #3]
    //     0x7645ec: blr             lr
    // 0x7645f0: ldur            x0, [fp, #-8]
    // 0x7645f4: LoadField: r3 = r0->field_7
    //     0x7645f4: ldur            w3, [x0, #7]
    // 0x7645f8: DecompressPointer r3
    //     0x7645f8: add             x3, x3, HEAP, lsl #32
    // 0x7645fc: stur            x3, [fp, #-0x18]
    // 0x764600: cmp             w3, NULL
    // 0x764604: b.eq            #0x764668
    // 0x764608: mov             x0, x3
    // 0x76460c: r2 = Null
    //     0x76460c: mov             x2, NULL
    // 0x764610: r1 = Null
    //     0x764610: mov             x1, NULL
    // 0x764614: r4 = LoadClassIdInstr(r0)
    //     0x764614: ldur            x4, [x0, #-1]
    //     0x764618: ubfx            x4, x4, #0xc, #0x14
    // 0x76461c: cmp             x4, #0xc79
    // 0x764620: b.eq            #0x764638
    // 0x764624: r8 = WrapParentData
    //     0x764624: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x764628: ldr             x8, [x8, #0xaf0]
    // 0x76462c: r3 = Null
    //     0x76462c: add             x3, PP, #0x44, lsl #12  ; [pp+0x44c40] Null
    //     0x764630: ldr             x3, [x3, #0xc40]
    // 0x764634: r0 = DefaultTypeTest()
    //     0x764634: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x764638: ldur            x1, [fp, #-0x18]
    // 0x76463c: LoadField: r3 = r1->field_13
    //     0x76463c: ldur            w3, [x1, #0x13]
    // 0x764640: DecompressPointer r3
    //     0x764640: add             x3, x3, HEAP, lsl #32
    // 0x764644: b               #0x7645b4
    // 0x764648: r0 = Null
    //     0x764648: mov             x0, NULL
    // 0x76464c: LeaveFrame
    //     0x76464c: mov             SP, fp
    //     0x764650: ldp             fp, lr, [SP], #0x10
    // 0x764654: ret
    //     0x764654: ret             
    // 0x764658: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x764658: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76465c: b               #0x764598
    // 0x764660: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x764660: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x764664: b               #0x7645c4
    // 0x764668: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x764668: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitChildren(/* No info */) {
    // ** addr: 0x78743c, size: 0xd8
    // 0x78743c: EnterFrame
    //     0x78743c: stp             fp, lr, [SP, #-0x10]!
    //     0x787440: mov             fp, SP
    // 0x787444: AllocStack(0x28)
    //     0x787444: sub             SP, SP, #0x28
    // 0x787448: SetupParameters(_RenderWrap&RenderBox&ContainerRenderObjectMixin this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x787448: mov             x0, x1
    //     0x78744c: mov             x1, x2
    //     0x787450: stur            x2, [fp, #-0x10]
    // 0x787454: CheckStackOverflow
    //     0x787454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x787458: cmp             SP, x16
    //     0x78745c: b.ls            #0x787500
    // 0x787460: LoadField: r2 = r0->field_5f
    //     0x787460: ldur            w2, [x0, #0x5f]
    // 0x787464: DecompressPointer r2
    //     0x787464: add             x2, x2, HEAP, lsl #32
    // 0x787468: stur            x2, [fp, #-8]
    // 0x78746c: CheckStackOverflow
    //     0x78746c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x787470: cmp             SP, x16
    //     0x787474: b.ls            #0x787508
    // 0x787478: cmp             w2, NULL
    // 0x78747c: b.eq            #0x7874f0
    // 0x787480: stp             x2, x1, [SP]
    // 0x787484: mov             x0, x1
    // 0x787488: ClosureCall
    //     0x787488: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x78748c: ldur            x2, [x0, #0x1f]
    //     0x787490: blr             x2
    // 0x787494: ldur            x0, [fp, #-8]
    // 0x787498: LoadField: r3 = r0->field_7
    //     0x787498: ldur            w3, [x0, #7]
    // 0x78749c: DecompressPointer r3
    //     0x78749c: add             x3, x3, HEAP, lsl #32
    // 0x7874a0: stur            x3, [fp, #-0x18]
    // 0x7874a4: cmp             w3, NULL
    // 0x7874a8: b.eq            #0x787510
    // 0x7874ac: mov             x0, x3
    // 0x7874b0: r2 = Null
    //     0x7874b0: mov             x2, NULL
    // 0x7874b4: r1 = Null
    //     0x7874b4: mov             x1, NULL
    // 0x7874b8: r4 = LoadClassIdInstr(r0)
    //     0x7874b8: ldur            x4, [x0, #-1]
    //     0x7874bc: ubfx            x4, x4, #0xc, #0x14
    // 0x7874c0: cmp             x4, #0xc79
    // 0x7874c4: b.eq            #0x7874dc
    // 0x7874c8: r8 = WrapParentData
    //     0x7874c8: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x7874cc: ldr             x8, [x8, #0xaf0]
    // 0x7874d0: r3 = Null
    //     0x7874d0: add             x3, PP, #0x44, lsl #12  ; [pp+0x44c10] Null
    //     0x7874d4: ldr             x3, [x3, #0xc10]
    // 0x7874d8: r0 = DefaultTypeTest()
    //     0x7874d8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7874dc: ldur            x1, [fp, #-0x18]
    // 0x7874e0: LoadField: r2 = r1->field_13
    //     0x7874e0: ldur            w2, [x1, #0x13]
    // 0x7874e4: DecompressPointer r2
    //     0x7874e4: add             x2, x2, HEAP, lsl #32
    // 0x7874e8: ldur            x1, [fp, #-0x10]
    // 0x7874ec: b               #0x787468
    // 0x7874f0: r0 = Null
    //     0x7874f0: mov             x0, NULL
    // 0x7874f4: LeaveFrame
    //     0x7874f4: mov             SP, fp
    //     0x7874f8: ldp             fp, lr, [SP], #0x10
    // 0x7874fc: ret
    //     0x7874fc: ret             
    // 0x787500: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x787500: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x787504: b               #0x787460
    // 0x787508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x787508: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x78750c: b               #0x787478
    // 0x787510: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x787510: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ insert(/* No info */) {
    // ** addr: 0x7b138c, size: 0xd0
    // 0x7b138c: EnterFrame
    //     0x7b138c: stp             fp, lr, [SP, #-0x10]!
    //     0x7b1390: mov             fp, SP
    // 0x7b1394: AllocStack(0x18)
    //     0x7b1394: sub             SP, SP, #0x18
    // 0x7b1398: SetupParameters(_RenderWrap&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7b1398: mov             x5, x1
    //     0x7b139c: mov             x4, x2
    //     0x7b13a0: stur            x1, [fp, #-8]
    //     0x7b13a4: stur            x2, [fp, #-0x10]
    //     0x7b13a8: stur            x3, [fp, #-0x18]
    // 0x7b13ac: CheckStackOverflow
    //     0x7b13ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b13b0: cmp             SP, x16
    //     0x7b13b4: b.ls            #0x7b1454
    // 0x7b13b8: mov             x0, x4
    // 0x7b13bc: r2 = Null
    //     0x7b13bc: mov             x2, NULL
    // 0x7b13c0: r1 = Null
    //     0x7b13c0: mov             x1, NULL
    // 0x7b13c4: r4 = 60
    //     0x7b13c4: movz            x4, #0x3c
    // 0x7b13c8: branchIfSmi(r0, 0x7b13d4)
    //     0x7b13c8: tbz             w0, #0, #0x7b13d4
    // 0x7b13cc: r4 = LoadClassIdInstr(r0)
    //     0x7b13cc: ldur            x4, [x0, #-1]
    //     0x7b13d0: ubfx            x4, x4, #0xc, #0x14
    // 0x7b13d4: sub             x4, x4, #0xbba
    // 0x7b13d8: cmp             x4, #0x9a
    // 0x7b13dc: b.ls            #0x7b13f0
    // 0x7b13e0: r8 = RenderBox
    //     0x7b13e0: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7b13e4: r3 = Null
    //     0x7b13e4: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e5c8] Null
    //     0x7b13e8: ldr             x3, [x3, #0x5c8]
    // 0x7b13ec: r0 = RenderBox()
    //     0x7b13ec: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7b13f0: ldur            x0, [fp, #-0x18]
    // 0x7b13f4: r2 = Null
    //     0x7b13f4: mov             x2, NULL
    // 0x7b13f8: r1 = Null
    //     0x7b13f8: mov             x1, NULL
    // 0x7b13fc: r4 = 60
    //     0x7b13fc: movz            x4, #0x3c
    // 0x7b1400: branchIfSmi(r0, 0x7b140c)
    //     0x7b1400: tbz             w0, #0, #0x7b140c
    // 0x7b1404: r4 = LoadClassIdInstr(r0)
    //     0x7b1404: ldur            x4, [x0, #-1]
    //     0x7b1408: ubfx            x4, x4, #0xc, #0x14
    // 0x7b140c: sub             x4, x4, #0xbba
    // 0x7b1410: cmp             x4, #0x9a
    // 0x7b1414: b.ls            #0x7b1428
    // 0x7b1418: r8 = RenderBox?
    //     0x7b1418: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x7b141c: r3 = Null
    //     0x7b141c: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e5d8] Null
    //     0x7b1420: ldr             x3, [x3, #0x5d8]
    // 0x7b1424: r0 = RenderBox?()
    //     0x7b1424: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x7b1428: ldur            x1, [fp, #-8]
    // 0x7b142c: ldur            x2, [fp, #-0x10]
    // 0x7b1430: r0 = adoptChild()
    //     0x7b1430: bl              #0x8057b8  ; [package:flutter/src/rendering/object.dart] RenderObject::adoptChild
    // 0x7b1434: ldur            x1, [fp, #-8]
    // 0x7b1438: ldur            x2, [fp, #-0x10]
    // 0x7b143c: ldur            x3, [fp, #-0x18]
    // 0x7b1440: r0 = _insertIntoChildList()
    //     0x7b1440: bl              #0xda6e8c  ; [package:flutter/src/rendering/wrap.dart] _RenderWrap&RenderBox&ContainerRenderObjectMixin::_insertIntoChildList
    // 0x7b1444: r0 = Null
    //     0x7b1444: mov             x0, NULL
    // 0x7b1448: LeaveFrame
    //     0x7b1448: mov             SP, fp
    //     0x7b144c: ldp             fp, lr, [SP], #0x10
    // 0x7b1450: ret
    //     0x7b1450: ret             
    // 0x7b1454: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b1454: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b1458: b               #0x7b13b8
  }
  _ remove(/* No info */) {
    // ** addr: 0x7b3ccc, size: 0x90
    // 0x7b3ccc: EnterFrame
    //     0x7b3ccc: stp             fp, lr, [SP, #-0x10]!
    //     0x7b3cd0: mov             fp, SP
    // 0x7b3cd4: AllocStack(0x10)
    //     0x7b3cd4: sub             SP, SP, #0x10
    // 0x7b3cd8: SetupParameters(_RenderWrap&RenderBox&ContainerRenderObjectMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x7b3cd8: mov             x4, x1
    //     0x7b3cdc: mov             x3, x2
    //     0x7b3ce0: stur            x1, [fp, #-8]
    //     0x7b3ce4: stur            x2, [fp, #-0x10]
    // 0x7b3ce8: CheckStackOverflow
    //     0x7b3ce8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b3cec: cmp             SP, x16
    //     0x7b3cf0: b.ls            #0x7b3d54
    // 0x7b3cf4: mov             x0, x3
    // 0x7b3cf8: r2 = Null
    //     0x7b3cf8: mov             x2, NULL
    // 0x7b3cfc: r1 = Null
    //     0x7b3cfc: mov             x1, NULL
    // 0x7b3d00: r4 = 60
    //     0x7b3d00: movz            x4, #0x3c
    // 0x7b3d04: branchIfSmi(r0, 0x7b3d10)
    //     0x7b3d04: tbz             w0, #0, #0x7b3d10
    // 0x7b3d08: r4 = LoadClassIdInstr(r0)
    //     0x7b3d08: ldur            x4, [x0, #-1]
    //     0x7b3d0c: ubfx            x4, x4, #0xc, #0x14
    // 0x7b3d10: sub             x4, x4, #0xbba
    // 0x7b3d14: cmp             x4, #0x9a
    // 0x7b3d18: b.ls            #0x7b3d2c
    // 0x7b3d1c: r8 = RenderBox
    //     0x7b3d1c: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7b3d20: r3 = Null
    //     0x7b3d20: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e5b8] Null
    //     0x7b3d24: ldr             x3, [x3, #0x5b8]
    // 0x7b3d28: r0 = RenderBox()
    //     0x7b3d28: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7b3d2c: ldur            x1, [fp, #-8]
    // 0x7b3d30: ldur            x2, [fp, #-0x10]
    // 0x7b3d34: r0 = _removeFromChildList()
    //     0x7b3d34: bl              #0x7b3d5c  ; [package:flutter/src/rendering/wrap.dart] _RenderWrap&RenderBox&ContainerRenderObjectMixin::_removeFromChildList
    // 0x7b3d38: ldur            x1, [fp, #-8]
    // 0x7b3d3c: ldur            x2, [fp, #-0x10]
    // 0x7b3d40: r0 = dropChild()
    //     0x7b3d40: bl              #0x8019dc  ; [package:flutter/src/rendering/object.dart] RenderObject::dropChild
    // 0x7b3d44: r0 = Null
    //     0x7b3d44: mov             x0, NULL
    // 0x7b3d48: LeaveFrame
    //     0x7b3d48: mov             SP, fp
    //     0x7b3d4c: ldp             fp, lr, [SP], #0x10
    // 0x7b3d50: ret
    //     0x7b3d50: ret             
    // 0x7b3d54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b3d54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b3d58: b               #0x7b3cf4
  }
  _ _removeFromChildList(/* No info */) {
    // ** addr: 0x7b3d5c, size: 0x2c8
    // 0x7b3d5c: EnterFrame
    //     0x7b3d5c: stp             fp, lr, [SP, #-0x10]!
    //     0x7b3d60: mov             fp, SP
    // 0x7b3d64: AllocStack(0x28)
    //     0x7b3d64: sub             SP, SP, #0x28
    // 0x7b3d68: SetupParameters(_RenderWrap&RenderBox&ContainerRenderObjectMixin this /* r1 => r3, fp-0x10 */)
    //     0x7b3d68: mov             x3, x1
    //     0x7b3d6c: stur            x1, [fp, #-0x10]
    // 0x7b3d70: LoadField: r4 = r2->field_7
    //     0x7b3d70: ldur            w4, [x2, #7]
    // 0x7b3d74: DecompressPointer r4
    //     0x7b3d74: add             x4, x4, HEAP, lsl #32
    // 0x7b3d78: stur            x4, [fp, #-8]
    // 0x7b3d7c: cmp             w4, NULL
    // 0x7b3d80: b.eq            #0x7b4018
    // 0x7b3d84: mov             x0, x4
    // 0x7b3d88: r2 = Null
    //     0x7b3d88: mov             x2, NULL
    // 0x7b3d8c: r1 = Null
    //     0x7b3d8c: mov             x1, NULL
    // 0x7b3d90: r4 = LoadClassIdInstr(r0)
    //     0x7b3d90: ldur            x4, [x0, #-1]
    //     0x7b3d94: ubfx            x4, x4, #0xc, #0x14
    // 0x7b3d98: cmp             x4, #0xc79
    // 0x7b3d9c: b.eq            #0x7b3db4
    // 0x7b3da0: r8 = WrapParentData
    //     0x7b3da0: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x7b3da4: ldr             x8, [x8, #0xaf0]
    // 0x7b3da8: r3 = Null
    //     0x7b3da8: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e558] Null
    //     0x7b3dac: ldr             x3, [x3, #0x558]
    // 0x7b3db0: r0 = DefaultTypeTest()
    //     0x7b3db0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b3db4: ldur            x3, [fp, #-8]
    // 0x7b3db8: LoadField: r4 = r3->field_f
    //     0x7b3db8: ldur            w4, [x3, #0xf]
    // 0x7b3dbc: DecompressPointer r4
    //     0x7b3dbc: add             x4, x4, HEAP, lsl #32
    // 0x7b3dc0: stur            x4, [fp, #-0x20]
    // 0x7b3dc4: cmp             w4, NULL
    // 0x7b3dc8: b.ne            #0x7b3df8
    // 0x7b3dcc: ldur            x5, [fp, #-0x10]
    // 0x7b3dd0: LoadField: r0 = r3->field_13
    //     0x7b3dd0: ldur            w0, [x3, #0x13]
    // 0x7b3dd4: DecompressPointer r0
    //     0x7b3dd4: add             x0, x0, HEAP, lsl #32
    // 0x7b3dd8: StoreField: r5->field_5f = r0
    //     0x7b3dd8: stur            w0, [x5, #0x5f]
    //     0x7b3ddc: ldurb           w16, [x5, #-1]
    //     0x7b3de0: ldurb           w17, [x0, #-1]
    //     0x7b3de4: and             x16, x17, x16, lsr #2
    //     0x7b3de8: tst             x16, HEAP, lsr #32
    //     0x7b3dec: b.eq            #0x7b3df4
    //     0x7b3df0: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x7b3df4: b               #0x7b3ebc
    // 0x7b3df8: ldur            x5, [fp, #-0x10]
    // 0x7b3dfc: LoadField: r6 = r4->field_7
    //     0x7b3dfc: ldur            w6, [x4, #7]
    // 0x7b3e00: DecompressPointer r6
    //     0x7b3e00: add             x6, x6, HEAP, lsl #32
    // 0x7b3e04: stur            x6, [fp, #-0x18]
    // 0x7b3e08: cmp             w6, NULL
    // 0x7b3e0c: b.eq            #0x7b401c
    // 0x7b3e10: mov             x0, x6
    // 0x7b3e14: r2 = Null
    //     0x7b3e14: mov             x2, NULL
    // 0x7b3e18: r1 = Null
    //     0x7b3e18: mov             x1, NULL
    // 0x7b3e1c: r4 = LoadClassIdInstr(r0)
    //     0x7b3e1c: ldur            x4, [x0, #-1]
    //     0x7b3e20: ubfx            x4, x4, #0xc, #0x14
    // 0x7b3e24: cmp             x4, #0xc79
    // 0x7b3e28: b.eq            #0x7b3e40
    // 0x7b3e2c: r8 = WrapParentData
    //     0x7b3e2c: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x7b3e30: ldr             x8, [x8, #0xaf0]
    // 0x7b3e34: r3 = Null
    //     0x7b3e34: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e568] Null
    //     0x7b3e38: ldr             x3, [x3, #0x568]
    // 0x7b3e3c: r0 = DefaultTypeTest()
    //     0x7b3e3c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b3e40: ldur            x3, [fp, #-8]
    // 0x7b3e44: LoadField: r4 = r3->field_13
    //     0x7b3e44: ldur            w4, [x3, #0x13]
    // 0x7b3e48: DecompressPointer r4
    //     0x7b3e48: add             x4, x4, HEAP, lsl #32
    // 0x7b3e4c: ldur            x5, [fp, #-0x18]
    // 0x7b3e50: stur            x4, [fp, #-0x28]
    // 0x7b3e54: LoadField: r2 = r5->field_b
    //     0x7b3e54: ldur            w2, [x5, #0xb]
    // 0x7b3e58: DecompressPointer r2
    //     0x7b3e58: add             x2, x2, HEAP, lsl #32
    // 0x7b3e5c: mov             x0, x4
    // 0x7b3e60: r1 = Null
    //     0x7b3e60: mov             x1, NULL
    // 0x7b3e64: cmp             w0, NULL
    // 0x7b3e68: b.eq            #0x7b3e94
    // 0x7b3e6c: cmp             w2, NULL
    // 0x7b3e70: b.eq            #0x7b3e94
    // 0x7b3e74: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b3e74: ldur            w4, [x2, #0x17]
    // 0x7b3e78: DecompressPointer r4
    //     0x7b3e78: add             x4, x4, HEAP, lsl #32
    // 0x7b3e7c: r8 = X0? bound RenderObject
    //     0x7b3e7c: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b3e80: ldr             x8, [x8, #0x1a8]
    // 0x7b3e84: LoadField: r9 = r4->field_7
    //     0x7b3e84: ldur            x9, [x4, #7]
    // 0x7b3e88: r3 = Null
    //     0x7b3e88: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e578] Null
    //     0x7b3e8c: ldr             x3, [x3, #0x578]
    // 0x7b3e90: blr             x9
    // 0x7b3e94: ldur            x0, [fp, #-0x28]
    // 0x7b3e98: ldur            x1, [fp, #-0x18]
    // 0x7b3e9c: StoreField: r1->field_13 = r0
    //     0x7b3e9c: stur            w0, [x1, #0x13]
    //     0x7b3ea0: ldurb           w16, [x1, #-1]
    //     0x7b3ea4: ldurb           w17, [x0, #-1]
    //     0x7b3ea8: and             x16, x17, x16, lsr #2
    //     0x7b3eac: tst             x16, HEAP, lsr #32
    //     0x7b3eb0: b.eq            #0x7b3eb8
    //     0x7b3eb4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b3eb8: ldur            x3, [fp, #-8]
    // 0x7b3ebc: LoadField: r0 = r3->field_13
    //     0x7b3ebc: ldur            w0, [x3, #0x13]
    // 0x7b3ec0: DecompressPointer r0
    //     0x7b3ec0: add             x0, x0, HEAP, lsl #32
    // 0x7b3ec4: cmp             w0, NULL
    // 0x7b3ec8: b.ne            #0x7b3ef4
    // 0x7b3ecc: ldur            x4, [fp, #-0x10]
    // 0x7b3ed0: ldur            x0, [fp, #-0x20]
    // 0x7b3ed4: StoreField: r4->field_63 = r0
    //     0x7b3ed4: stur            w0, [x4, #0x63]
    //     0x7b3ed8: ldurb           w16, [x4, #-1]
    //     0x7b3edc: ldurb           w17, [x0, #-1]
    //     0x7b3ee0: and             x16, x17, x16, lsr #2
    //     0x7b3ee4: tst             x16, HEAP, lsr #32
    //     0x7b3ee8: b.eq            #0x7b3ef0
    //     0x7b3eec: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7b3ef0: b               #0x7b3fac
    // 0x7b3ef4: ldur            x4, [fp, #-0x10]
    // 0x7b3ef8: LoadField: r5 = r0->field_7
    //     0x7b3ef8: ldur            w5, [x0, #7]
    // 0x7b3efc: DecompressPointer r5
    //     0x7b3efc: add             x5, x5, HEAP, lsl #32
    // 0x7b3f00: stur            x5, [fp, #-0x18]
    // 0x7b3f04: cmp             w5, NULL
    // 0x7b3f08: b.eq            #0x7b4020
    // 0x7b3f0c: mov             x0, x5
    // 0x7b3f10: r2 = Null
    //     0x7b3f10: mov             x2, NULL
    // 0x7b3f14: r1 = Null
    //     0x7b3f14: mov             x1, NULL
    // 0x7b3f18: r4 = LoadClassIdInstr(r0)
    //     0x7b3f18: ldur            x4, [x0, #-1]
    //     0x7b3f1c: ubfx            x4, x4, #0xc, #0x14
    // 0x7b3f20: cmp             x4, #0xc79
    // 0x7b3f24: b.eq            #0x7b3f3c
    // 0x7b3f28: r8 = WrapParentData
    //     0x7b3f28: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x7b3f2c: ldr             x8, [x8, #0xaf0]
    // 0x7b3f30: r3 = Null
    //     0x7b3f30: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e588] Null
    //     0x7b3f34: ldr             x3, [x3, #0x588]
    // 0x7b3f38: r0 = DefaultTypeTest()
    //     0x7b3f38: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b3f3c: ldur            x3, [fp, #-0x18]
    // 0x7b3f40: LoadField: r2 = r3->field_b
    //     0x7b3f40: ldur            w2, [x3, #0xb]
    // 0x7b3f44: DecompressPointer r2
    //     0x7b3f44: add             x2, x2, HEAP, lsl #32
    // 0x7b3f48: ldur            x0, [fp, #-0x20]
    // 0x7b3f4c: r1 = Null
    //     0x7b3f4c: mov             x1, NULL
    // 0x7b3f50: cmp             w0, NULL
    // 0x7b3f54: b.eq            #0x7b3f80
    // 0x7b3f58: cmp             w2, NULL
    // 0x7b3f5c: b.eq            #0x7b3f80
    // 0x7b3f60: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b3f60: ldur            w4, [x2, #0x17]
    // 0x7b3f64: DecompressPointer r4
    //     0x7b3f64: add             x4, x4, HEAP, lsl #32
    // 0x7b3f68: r8 = X0? bound RenderObject
    //     0x7b3f68: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b3f6c: ldr             x8, [x8, #0x1a8]
    // 0x7b3f70: LoadField: r9 = r4->field_7
    //     0x7b3f70: ldur            x9, [x4, #7]
    // 0x7b3f74: r3 = Null
    //     0x7b3f74: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e598] Null
    //     0x7b3f78: ldr             x3, [x3, #0x598]
    // 0x7b3f7c: blr             x9
    // 0x7b3f80: ldur            x0, [fp, #-0x20]
    // 0x7b3f84: ldur            x1, [fp, #-0x18]
    // 0x7b3f88: StoreField: r1->field_f = r0
    //     0x7b3f88: stur            w0, [x1, #0xf]
    //     0x7b3f8c: ldurb           w16, [x1, #-1]
    //     0x7b3f90: ldurb           w17, [x0, #-1]
    //     0x7b3f94: and             x16, x17, x16, lsr #2
    //     0x7b3f98: tst             x16, HEAP, lsr #32
    //     0x7b3f9c: b.eq            #0x7b3fa4
    //     0x7b3fa0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b3fa4: ldur            x4, [fp, #-0x10]
    // 0x7b3fa8: ldur            x3, [fp, #-8]
    // 0x7b3fac: LoadField: r2 = r3->field_b
    //     0x7b3fac: ldur            w2, [x3, #0xb]
    // 0x7b3fb0: DecompressPointer r2
    //     0x7b3fb0: add             x2, x2, HEAP, lsl #32
    // 0x7b3fb4: r0 = Null
    //     0x7b3fb4: mov             x0, NULL
    // 0x7b3fb8: r1 = Null
    //     0x7b3fb8: mov             x1, NULL
    // 0x7b3fbc: cmp             w0, NULL
    // 0x7b3fc0: b.eq            #0x7b3fec
    // 0x7b3fc4: cmp             w2, NULL
    // 0x7b3fc8: b.eq            #0x7b3fec
    // 0x7b3fcc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b3fcc: ldur            w4, [x2, #0x17]
    // 0x7b3fd0: DecompressPointer r4
    //     0x7b3fd0: add             x4, x4, HEAP, lsl #32
    // 0x7b3fd4: r8 = X0? bound RenderObject
    //     0x7b3fd4: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b3fd8: ldr             x8, [x8, #0x1a8]
    // 0x7b3fdc: LoadField: r9 = r4->field_7
    //     0x7b3fdc: ldur            x9, [x4, #7]
    // 0x7b3fe0: r3 = Null
    //     0x7b3fe0: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e5a8] Null
    //     0x7b3fe4: ldr             x3, [x3, #0x5a8]
    // 0x7b3fe8: blr             x9
    // 0x7b3fec: ldur            x1, [fp, #-8]
    // 0x7b3ff0: StoreField: r1->field_f = rNULL
    //     0x7b3ff0: stur            NULL, [x1, #0xf]
    // 0x7b3ff4: StoreField: r1->field_13 = rNULL
    //     0x7b3ff4: stur            NULL, [x1, #0x13]
    // 0x7b3ff8: ldur            x1, [fp, #-0x10]
    // 0x7b3ffc: LoadField: r2 = r1->field_57
    //     0x7b3ffc: ldur            x2, [x1, #0x57]
    // 0x7b4000: sub             x3, x2, #1
    // 0x7b4004: StoreField: r1->field_57 = r3
    //     0x7b4004: stur            x3, [x1, #0x57]
    // 0x7b4008: r0 = Null
    //     0x7b4008: mov             x0, NULL
    // 0x7b400c: LeaveFrame
    //     0x7b400c: mov             SP, fp
    //     0x7b4010: ldp             fp, lr, [SP], #0x10
    // 0x7b4014: ret
    //     0x7b4014: ret             
    // 0x7b4018: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b4018: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7b401c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b401c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7b4020: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b4020: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ move(/* No info */) {
    // ** addr: 0x7ce7a8, size: 0x160
    // 0x7ce7a8: EnterFrame
    //     0x7ce7a8: stp             fp, lr, [SP, #-0x10]!
    //     0x7ce7ac: mov             fp, SP
    // 0x7ce7b0: AllocStack(0x30)
    //     0x7ce7b0: sub             SP, SP, #0x30
    // 0x7ce7b4: SetupParameters(_RenderWrap&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7ce7b4: mov             x5, x1
    //     0x7ce7b8: mov             x4, x2
    //     0x7ce7bc: stur            x1, [fp, #-8]
    //     0x7ce7c0: stur            x2, [fp, #-0x10]
    //     0x7ce7c4: stur            x3, [fp, #-0x18]
    // 0x7ce7c8: CheckStackOverflow
    //     0x7ce7c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ce7cc: cmp             SP, x16
    //     0x7ce7d0: b.ls            #0x7ce8fc
    // 0x7ce7d4: mov             x0, x4
    // 0x7ce7d8: r2 = Null
    //     0x7ce7d8: mov             x2, NULL
    // 0x7ce7dc: r1 = Null
    //     0x7ce7dc: mov             x1, NULL
    // 0x7ce7e0: r4 = 60
    //     0x7ce7e0: movz            x4, #0x3c
    // 0x7ce7e4: branchIfSmi(r0, 0x7ce7f0)
    //     0x7ce7e4: tbz             w0, #0, #0x7ce7f0
    // 0x7ce7e8: r4 = LoadClassIdInstr(r0)
    //     0x7ce7e8: ldur            x4, [x0, #-1]
    //     0x7ce7ec: ubfx            x4, x4, #0xc, #0x14
    // 0x7ce7f0: sub             x4, x4, #0xbba
    // 0x7ce7f4: cmp             x4, #0x9a
    // 0x7ce7f8: b.ls            #0x7ce80c
    // 0x7ce7fc: r8 = RenderBox
    //     0x7ce7fc: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7ce800: r3 = Null
    //     0x7ce800: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e468] Null
    //     0x7ce804: ldr             x3, [x3, #0x468]
    // 0x7ce808: r0 = RenderBox()
    //     0x7ce808: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7ce80c: ldur            x0, [fp, #-0x18]
    // 0x7ce810: r2 = Null
    //     0x7ce810: mov             x2, NULL
    // 0x7ce814: r1 = Null
    //     0x7ce814: mov             x1, NULL
    // 0x7ce818: r4 = 60
    //     0x7ce818: movz            x4, #0x3c
    // 0x7ce81c: branchIfSmi(r0, 0x7ce828)
    //     0x7ce81c: tbz             w0, #0, #0x7ce828
    // 0x7ce820: r4 = LoadClassIdInstr(r0)
    //     0x7ce820: ldur            x4, [x0, #-1]
    //     0x7ce824: ubfx            x4, x4, #0xc, #0x14
    // 0x7ce828: sub             x4, x4, #0xbba
    // 0x7ce82c: cmp             x4, #0x9a
    // 0x7ce830: b.ls            #0x7ce844
    // 0x7ce834: r8 = RenderBox?
    //     0x7ce834: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x7ce838: r3 = Null
    //     0x7ce838: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e478] Null
    //     0x7ce83c: ldr             x3, [x3, #0x478]
    // 0x7ce840: r0 = RenderBox?()
    //     0x7ce840: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x7ce844: ldur            x3, [fp, #-0x10]
    // 0x7ce848: LoadField: r4 = r3->field_7
    //     0x7ce848: ldur            w4, [x3, #7]
    // 0x7ce84c: DecompressPointer r4
    //     0x7ce84c: add             x4, x4, HEAP, lsl #32
    // 0x7ce850: stur            x4, [fp, #-0x20]
    // 0x7ce854: cmp             w4, NULL
    // 0x7ce858: b.eq            #0x7ce904
    // 0x7ce85c: mov             x0, x4
    // 0x7ce860: r2 = Null
    //     0x7ce860: mov             x2, NULL
    // 0x7ce864: r1 = Null
    //     0x7ce864: mov             x1, NULL
    // 0x7ce868: r4 = LoadClassIdInstr(r0)
    //     0x7ce868: ldur            x4, [x0, #-1]
    //     0x7ce86c: ubfx            x4, x4, #0xc, #0x14
    // 0x7ce870: cmp             x4, #0xc79
    // 0x7ce874: b.eq            #0x7ce88c
    // 0x7ce878: r8 = WrapParentData
    //     0x7ce878: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x7ce87c: ldr             x8, [x8, #0xaf0]
    // 0x7ce880: r3 = Null
    //     0x7ce880: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e488] Null
    //     0x7ce884: ldr             x3, [x3, #0x488]
    // 0x7ce888: r0 = DefaultTypeTest()
    //     0x7ce888: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7ce88c: ldur            x0, [fp, #-0x20]
    // 0x7ce890: LoadField: r1 = r0->field_f
    //     0x7ce890: ldur            w1, [x0, #0xf]
    // 0x7ce894: DecompressPointer r1
    //     0x7ce894: add             x1, x1, HEAP, lsl #32
    // 0x7ce898: r0 = LoadClassIdInstr(r1)
    //     0x7ce898: ldur            x0, [x1, #-1]
    //     0x7ce89c: ubfx            x0, x0, #0xc, #0x14
    // 0x7ce8a0: ldur            x16, [fp, #-0x18]
    // 0x7ce8a4: stp             x16, x1, [SP]
    // 0x7ce8a8: mov             lr, x0
    // 0x7ce8ac: ldr             lr, [x21, lr, lsl #3]
    // 0x7ce8b0: blr             lr
    // 0x7ce8b4: tbnz            w0, #4, #0x7ce8c8
    // 0x7ce8b8: r0 = Null
    //     0x7ce8b8: mov             x0, NULL
    // 0x7ce8bc: LeaveFrame
    //     0x7ce8bc: mov             SP, fp
    //     0x7ce8c0: ldp             fp, lr, [SP], #0x10
    // 0x7ce8c4: ret
    //     0x7ce8c4: ret             
    // 0x7ce8c8: ldur            x1, [fp, #-8]
    // 0x7ce8cc: ldur            x2, [fp, #-0x10]
    // 0x7ce8d0: r0 = _removeFromChildList()
    //     0x7ce8d0: bl              #0x7b3d5c  ; [package:flutter/src/rendering/wrap.dart] _RenderWrap&RenderBox&ContainerRenderObjectMixin::_removeFromChildList
    // 0x7ce8d4: ldur            x1, [fp, #-8]
    // 0x7ce8d8: ldur            x2, [fp, #-0x10]
    // 0x7ce8dc: ldur            x3, [fp, #-0x18]
    // 0x7ce8e0: r0 = _insertIntoChildList()
    //     0x7ce8e0: bl              #0xda6e8c  ; [package:flutter/src/rendering/wrap.dart] _RenderWrap&RenderBox&ContainerRenderObjectMixin::_insertIntoChildList
    // 0x7ce8e4: ldur            x1, [fp, #-8]
    // 0x7ce8e8: r0 = markNeedsLayout()
    //     0x7ce8e8: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0x7ce8ec: r0 = Null
    //     0x7ce8ec: mov             x0, NULL
    // 0x7ce8f0: LeaveFrame
    //     0x7ce8f0: mov             SP, fp
    //     0x7ce8f4: ldp             fp, lr, [SP], #0x10
    // 0x7ce8f8: ret
    //     0x7ce8f8: ret             
    // 0x7ce8fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ce8fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ce900: b               #0x7ce7d4
    // 0x7ce904: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7ce904: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ detach(/* No info */) {
    // ** addr: 0x807d10, size: 0xe8
    // 0x807d10: EnterFrame
    //     0x807d10: stp             fp, lr, [SP, #-0x10]!
    //     0x807d14: mov             fp, SP
    // 0x807d18: AllocStack(0x10)
    //     0x807d18: sub             SP, SP, #0x10
    // 0x807d1c: SetupParameters(_RenderWrap&RenderBox&ContainerRenderObjectMixin this /* r1 => r0, fp-0x8 */)
    //     0x807d1c: mov             x0, x1
    //     0x807d20: stur            x1, [fp, #-8]
    // 0x807d24: CheckStackOverflow
    //     0x807d24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x807d28: cmp             SP, x16
    //     0x807d2c: b.ls            #0x807de4
    // 0x807d30: mov             x1, x0
    // 0x807d34: r0 = detach()
    //     0x807d34: bl              #0x8083b4  ; [package:flutter/src/rendering/object.dart] RenderObject::detach
    // 0x807d38: ldur            x0, [fp, #-8]
    // 0x807d3c: LoadField: r1 = r0->field_5f
    //     0x807d3c: ldur            w1, [x0, #0x5f]
    // 0x807d40: DecompressPointer r1
    //     0x807d40: add             x1, x1, HEAP, lsl #32
    // 0x807d44: mov             x2, x1
    // 0x807d48: stur            x2, [fp, #-8]
    // 0x807d4c: CheckStackOverflow
    //     0x807d4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x807d50: cmp             SP, x16
    //     0x807d54: b.ls            #0x807dec
    // 0x807d58: cmp             w2, NULL
    // 0x807d5c: b.eq            #0x807dd4
    // 0x807d60: r0 = LoadClassIdInstr(r2)
    //     0x807d60: ldur            x0, [x2, #-1]
    //     0x807d64: ubfx            x0, x0, #0xc, #0x14
    // 0x807d68: mov             x1, x2
    // 0x807d6c: r0 = GDT[cid_x0 + 0xeec9]()
    //     0x807d6c: movz            x17, #0xeec9
    //     0x807d70: add             lr, x0, x17
    //     0x807d74: ldr             lr, [x21, lr, lsl #3]
    //     0x807d78: blr             lr
    // 0x807d7c: ldur            x0, [fp, #-8]
    // 0x807d80: LoadField: r3 = r0->field_7
    //     0x807d80: ldur            w3, [x0, #7]
    // 0x807d84: DecompressPointer r3
    //     0x807d84: add             x3, x3, HEAP, lsl #32
    // 0x807d88: stur            x3, [fp, #-0x10]
    // 0x807d8c: cmp             w3, NULL
    // 0x807d90: b.eq            #0x807df4
    // 0x807d94: mov             x0, x3
    // 0x807d98: r2 = Null
    //     0x807d98: mov             x2, NULL
    // 0x807d9c: r1 = Null
    //     0x807d9c: mov             x1, NULL
    // 0x807da0: r4 = LoadClassIdInstr(r0)
    //     0x807da0: ldur            x4, [x0, #-1]
    //     0x807da4: ubfx            x4, x4, #0xc, #0x14
    // 0x807da8: cmp             x4, #0xc79
    // 0x807dac: b.eq            #0x807dc4
    // 0x807db0: r8 = WrapParentData
    //     0x807db0: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x807db4: ldr             x8, [x8, #0xaf0]
    // 0x807db8: r3 = Null
    //     0x807db8: add             x3, PP, #0x44, lsl #12  ; [pp+0x44c30] Null
    //     0x807dbc: ldr             x3, [x3, #0xc30]
    // 0x807dc0: r0 = DefaultTypeTest()
    //     0x807dc0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x807dc4: ldur            x1, [fp, #-0x10]
    // 0x807dc8: LoadField: r2 = r1->field_13
    //     0x807dc8: ldur            w2, [x1, #0x13]
    // 0x807dcc: DecompressPointer r2
    //     0x807dcc: add             x2, x2, HEAP, lsl #32
    // 0x807dd0: b               #0x807d48
    // 0x807dd4: r0 = Null
    //     0x807dd4: mov             x0, NULL
    // 0x807dd8: LeaveFrame
    //     0x807dd8: mov             SP, fp
    //     0x807ddc: ldp             fp, lr, [SP], #0x10
    // 0x807de0: ret
    //     0x807de0: ret             
    // 0x807de4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x807de4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x807de8: b               #0x807d30
    // 0x807dec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x807dec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x807df0: b               #0x807d58
    // 0x807df4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x807df4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ redepthChildren(/* No info */) {
    // ** addr: 0x809150, size: 0xf8
    // 0x809150: EnterFrame
    //     0x809150: stp             fp, lr, [SP, #-0x10]!
    //     0x809154: mov             fp, SP
    // 0x809158: AllocStack(0x18)
    //     0x809158: sub             SP, SP, #0x18
    // 0x80915c: SetupParameters(_RenderWrap&RenderBox&ContainerRenderObjectMixin this /* r1 => r2, fp-0x10 */)
    //     0x80915c: mov             x2, x1
    //     0x809160: stur            x1, [fp, #-0x10]
    // 0x809164: CheckStackOverflow
    //     0x809164: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x809168: cmp             SP, x16
    //     0x80916c: b.ls            #0x809234
    // 0x809170: LoadField: r0 = r2->field_5f
    //     0x809170: ldur            w0, [x2, #0x5f]
    // 0x809174: DecompressPointer r0
    //     0x809174: add             x0, x0, HEAP, lsl #32
    // 0x809178: mov             x3, x0
    // 0x80917c: stur            x3, [fp, #-8]
    // 0x809180: CheckStackOverflow
    //     0x809180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x809184: cmp             SP, x16
    //     0x809188: b.ls            #0x80923c
    // 0x80918c: cmp             w3, NULL
    // 0x809190: b.eq            #0x809224
    // 0x809194: LoadField: r0 = r3->field_b
    //     0x809194: ldur            x0, [x3, #0xb]
    // 0x809198: LoadField: r1 = r2->field_b
    //     0x809198: ldur            x1, [x2, #0xb]
    // 0x80919c: cmp             x0, x1
    // 0x8091a0: b.gt            #0x8091c8
    // 0x8091a4: add             x0, x1, #1
    // 0x8091a8: StoreField: r3->field_b = r0
    //     0x8091a8: stur            x0, [x3, #0xb]
    // 0x8091ac: r0 = LoadClassIdInstr(r3)
    //     0x8091ac: ldur            x0, [x3, #-1]
    //     0x8091b0: ubfx            x0, x0, #0xc, #0x14
    // 0x8091b4: mov             x1, x3
    // 0x8091b8: r0 = GDT[cid_x0 + 0xedec]()
    //     0x8091b8: movz            x17, #0xedec
    //     0x8091bc: add             lr, x0, x17
    //     0x8091c0: ldr             lr, [x21, lr, lsl #3]
    //     0x8091c4: blr             lr
    // 0x8091c8: ldur            x0, [fp, #-8]
    // 0x8091cc: LoadField: r3 = r0->field_7
    //     0x8091cc: ldur            w3, [x0, #7]
    // 0x8091d0: DecompressPointer r3
    //     0x8091d0: add             x3, x3, HEAP, lsl #32
    // 0x8091d4: stur            x3, [fp, #-0x18]
    // 0x8091d8: cmp             w3, NULL
    // 0x8091dc: b.eq            #0x809244
    // 0x8091e0: mov             x0, x3
    // 0x8091e4: r2 = Null
    //     0x8091e4: mov             x2, NULL
    // 0x8091e8: r1 = Null
    //     0x8091e8: mov             x1, NULL
    // 0x8091ec: r4 = LoadClassIdInstr(r0)
    //     0x8091ec: ldur            x4, [x0, #-1]
    //     0x8091f0: ubfx            x4, x4, #0xc, #0x14
    // 0x8091f4: cmp             x4, #0xc79
    // 0x8091f8: b.eq            #0x809210
    // 0x8091fc: r8 = WrapParentData
    //     0x8091fc: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x809200: ldr             x8, [x8, #0xaf0]
    // 0x809204: r3 = Null
    //     0x809204: add             x3, PP, #0x44, lsl #12  ; [pp+0x44c20] Null
    //     0x809208: ldr             x3, [x3, #0xc20]
    // 0x80920c: r0 = DefaultTypeTest()
    //     0x80920c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x809210: ldur            x1, [fp, #-0x18]
    // 0x809214: LoadField: r3 = r1->field_13
    //     0x809214: ldur            w3, [x1, #0x13]
    // 0x809218: DecompressPointer r3
    //     0x809218: add             x3, x3, HEAP, lsl #32
    // 0x80921c: ldur            x2, [fp, #-0x10]
    // 0x809220: b               #0x80917c
    // 0x809224: r0 = Null
    //     0x809224: mov             x0, NULL
    // 0x809228: LeaveFrame
    //     0x809228: mov             SP, fp
    //     0x80922c: ldp             fp, lr, [SP], #0x10
    // 0x809230: ret
    //     0x809230: ret             
    // 0x809234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x809234: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x809238: b               #0x809170
    // 0x80923c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80923c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x809240: b               #0x80918c
    // 0x809244: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x809244: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _insertIntoChildList(/* No info */) {
    // ** addr: 0xda6e8c, size: 0x570
    // 0xda6e8c: EnterFrame
    //     0xda6e8c: stp             fp, lr, [SP, #-0x10]!
    //     0xda6e90: mov             fp, SP
    // 0xda6e94: AllocStack(0x30)
    //     0xda6e94: sub             SP, SP, #0x30
    // 0xda6e98: SetupParameters(_RenderWrap&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xda6e98: mov             x5, x1
    //     0xda6e9c: mov             x4, x2
    //     0xda6ea0: stur            x1, [fp, #-0x10]
    //     0xda6ea4: stur            x2, [fp, #-0x18]
    //     0xda6ea8: stur            x3, [fp, #-0x20]
    // 0xda6eac: LoadField: r6 = r4->field_7
    //     0xda6eac: ldur            w6, [x4, #7]
    // 0xda6eb0: DecompressPointer r6
    //     0xda6eb0: add             x6, x6, HEAP, lsl #32
    // 0xda6eb4: stur            x6, [fp, #-8]
    // 0xda6eb8: cmp             w6, NULL
    // 0xda6ebc: b.eq            #0xda73ec
    // 0xda6ec0: mov             x0, x6
    // 0xda6ec4: r2 = Null
    //     0xda6ec4: mov             x2, NULL
    // 0xda6ec8: r1 = Null
    //     0xda6ec8: mov             x1, NULL
    // 0xda6ecc: r4 = LoadClassIdInstr(r0)
    //     0xda6ecc: ldur            x4, [x0, #-1]
    //     0xda6ed0: ubfx            x4, x4, #0xc, #0x14
    // 0xda6ed4: cmp             x4, #0xc79
    // 0xda6ed8: b.eq            #0xda6ef0
    // 0xda6edc: r8 = WrapParentData
    //     0xda6edc: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0xda6ee0: ldr             x8, [x8, #0xaf0]
    // 0xda6ee4: r3 = Null
    //     0xda6ee4: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e498] Null
    //     0xda6ee8: ldr             x3, [x3, #0x498]
    // 0xda6eec: r0 = DefaultTypeTest()
    //     0xda6eec: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda6ef0: ldur            x3, [fp, #-0x10]
    // 0xda6ef4: LoadField: r0 = r3->field_57
    //     0xda6ef4: ldur            x0, [x3, #0x57]
    // 0xda6ef8: add             x1, x0, #1
    // 0xda6efc: StoreField: r3->field_57 = r1
    //     0xda6efc: stur            x1, [x3, #0x57]
    // 0xda6f00: ldur            x4, [fp, #-0x20]
    // 0xda6f04: cmp             w4, NULL
    // 0xda6f08: b.ne            #0xda7090
    // 0xda6f0c: ldur            x4, [fp, #-8]
    // 0xda6f10: LoadField: r5 = r3->field_5f
    //     0xda6f10: ldur            w5, [x3, #0x5f]
    // 0xda6f14: DecompressPointer r5
    //     0xda6f14: add             x5, x5, HEAP, lsl #32
    // 0xda6f18: stur            x5, [fp, #-0x28]
    // 0xda6f1c: LoadField: r2 = r4->field_b
    //     0xda6f1c: ldur            w2, [x4, #0xb]
    // 0xda6f20: DecompressPointer r2
    //     0xda6f20: add             x2, x2, HEAP, lsl #32
    // 0xda6f24: mov             x0, x5
    // 0xda6f28: r1 = Null
    //     0xda6f28: mov             x1, NULL
    // 0xda6f2c: cmp             w0, NULL
    // 0xda6f30: b.eq            #0xda6f5c
    // 0xda6f34: cmp             w2, NULL
    // 0xda6f38: b.eq            #0xda6f5c
    // 0xda6f3c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda6f3c: ldur            w4, [x2, #0x17]
    // 0xda6f40: DecompressPointer r4
    //     0xda6f40: add             x4, x4, HEAP, lsl #32
    // 0xda6f44: r8 = X0? bound RenderObject
    //     0xda6f44: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda6f48: ldr             x8, [x8, #0x1a8]
    // 0xda6f4c: LoadField: r9 = r4->field_7
    //     0xda6f4c: ldur            x9, [x4, #7]
    // 0xda6f50: r3 = Null
    //     0xda6f50: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e4a8] Null
    //     0xda6f54: ldr             x3, [x3, #0x4a8]
    // 0xda6f58: blr             x9
    // 0xda6f5c: ldur            x0, [fp, #-0x28]
    // 0xda6f60: ldur            x3, [fp, #-8]
    // 0xda6f64: StoreField: r3->field_13 = r0
    //     0xda6f64: stur            w0, [x3, #0x13]
    //     0xda6f68: ldurb           w16, [x3, #-1]
    //     0xda6f6c: ldurb           w17, [x0, #-1]
    //     0xda6f70: and             x16, x17, x16, lsr #2
    //     0xda6f74: tst             x16, HEAP, lsr #32
    //     0xda6f78: b.eq            #0xda6f80
    //     0xda6f7c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda6f80: ldur            x0, [fp, #-0x28]
    // 0xda6f84: cmp             w0, NULL
    // 0xda6f88: b.eq            #0xda7038
    // 0xda6f8c: LoadField: r3 = r0->field_7
    //     0xda6f8c: ldur            w3, [x0, #7]
    // 0xda6f90: DecompressPointer r3
    //     0xda6f90: add             x3, x3, HEAP, lsl #32
    // 0xda6f94: stur            x3, [fp, #-0x30]
    // 0xda6f98: cmp             w3, NULL
    // 0xda6f9c: b.eq            #0xda73f0
    // 0xda6fa0: mov             x0, x3
    // 0xda6fa4: r2 = Null
    //     0xda6fa4: mov             x2, NULL
    // 0xda6fa8: r1 = Null
    //     0xda6fa8: mov             x1, NULL
    // 0xda6fac: r4 = LoadClassIdInstr(r0)
    //     0xda6fac: ldur            x4, [x0, #-1]
    //     0xda6fb0: ubfx            x4, x4, #0xc, #0x14
    // 0xda6fb4: cmp             x4, #0xc79
    // 0xda6fb8: b.eq            #0xda6fd0
    // 0xda6fbc: r8 = WrapParentData
    //     0xda6fbc: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0xda6fc0: ldr             x8, [x8, #0xaf0]
    // 0xda6fc4: r3 = Null
    //     0xda6fc4: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e4b8] Null
    //     0xda6fc8: ldr             x3, [x3, #0x4b8]
    // 0xda6fcc: r0 = DefaultTypeTest()
    //     0xda6fcc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda6fd0: ldur            x3, [fp, #-0x30]
    // 0xda6fd4: LoadField: r2 = r3->field_b
    //     0xda6fd4: ldur            w2, [x3, #0xb]
    // 0xda6fd8: DecompressPointer r2
    //     0xda6fd8: add             x2, x2, HEAP, lsl #32
    // 0xda6fdc: ldur            x0, [fp, #-0x18]
    // 0xda6fe0: r1 = Null
    //     0xda6fe0: mov             x1, NULL
    // 0xda6fe4: cmp             w0, NULL
    // 0xda6fe8: b.eq            #0xda7014
    // 0xda6fec: cmp             w2, NULL
    // 0xda6ff0: b.eq            #0xda7014
    // 0xda6ff4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda6ff4: ldur            w4, [x2, #0x17]
    // 0xda6ff8: DecompressPointer r4
    //     0xda6ff8: add             x4, x4, HEAP, lsl #32
    // 0xda6ffc: r8 = X0? bound RenderObject
    //     0xda6ffc: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda7000: ldr             x8, [x8, #0x1a8]
    // 0xda7004: LoadField: r9 = r4->field_7
    //     0xda7004: ldur            x9, [x4, #7]
    // 0xda7008: r3 = Null
    //     0xda7008: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e4c8] Null
    //     0xda700c: ldr             x3, [x3, #0x4c8]
    // 0xda7010: blr             x9
    // 0xda7014: ldur            x0, [fp, #-0x18]
    // 0xda7018: ldur            x1, [fp, #-0x30]
    // 0xda701c: StoreField: r1->field_f = r0
    //     0xda701c: stur            w0, [x1, #0xf]
    //     0xda7020: ldurb           w16, [x1, #-1]
    //     0xda7024: ldurb           w17, [x0, #-1]
    //     0xda7028: and             x16, x17, x16, lsr #2
    //     0xda702c: tst             x16, HEAP, lsr #32
    //     0xda7030: b.eq            #0xda7038
    //     0xda7034: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda7038: ldur            x5, [fp, #-0x10]
    // 0xda703c: ldur            x0, [fp, #-0x18]
    // 0xda7040: StoreField: r5->field_5f = r0
    //     0xda7040: stur            w0, [x5, #0x5f]
    //     0xda7044: ldurb           w16, [x5, #-1]
    //     0xda7048: ldurb           w17, [x0, #-1]
    //     0xda704c: and             x16, x17, x16, lsr #2
    //     0xda7050: tst             x16, HEAP, lsr #32
    //     0xda7054: b.eq            #0xda705c
    //     0xda7058: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda705c: LoadField: r0 = r5->field_63
    //     0xda705c: ldur            w0, [x5, #0x63]
    // 0xda7060: DecompressPointer r0
    //     0xda7060: add             x0, x0, HEAP, lsl #32
    // 0xda7064: cmp             w0, NULL
    // 0xda7068: b.ne            #0xda73dc
    // 0xda706c: ldur            x0, [fp, #-0x18]
    // 0xda7070: StoreField: r5->field_63 = r0
    //     0xda7070: stur            w0, [x5, #0x63]
    //     0xda7074: ldurb           w16, [x5, #-1]
    //     0xda7078: ldurb           w17, [x0, #-1]
    //     0xda707c: and             x16, x17, x16, lsr #2
    //     0xda7080: tst             x16, HEAP, lsr #32
    //     0xda7084: b.eq            #0xda708c
    //     0xda7088: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda708c: b               #0xda73dc
    // 0xda7090: mov             x5, x3
    // 0xda7094: ldur            x3, [fp, #-8]
    // 0xda7098: LoadField: r6 = r4->field_7
    //     0xda7098: ldur            w6, [x4, #7]
    // 0xda709c: DecompressPointer r6
    //     0xda709c: add             x6, x6, HEAP, lsl #32
    // 0xda70a0: stur            x6, [fp, #-0x28]
    // 0xda70a4: cmp             w6, NULL
    // 0xda70a8: b.eq            #0xda73f4
    // 0xda70ac: mov             x0, x6
    // 0xda70b0: r2 = Null
    //     0xda70b0: mov             x2, NULL
    // 0xda70b4: r1 = Null
    //     0xda70b4: mov             x1, NULL
    // 0xda70b8: r4 = LoadClassIdInstr(r0)
    //     0xda70b8: ldur            x4, [x0, #-1]
    //     0xda70bc: ubfx            x4, x4, #0xc, #0x14
    // 0xda70c0: cmp             x4, #0xc79
    // 0xda70c4: b.eq            #0xda70dc
    // 0xda70c8: r8 = WrapParentData
    //     0xda70c8: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0xda70cc: ldr             x8, [x8, #0xaf0]
    // 0xda70d0: r3 = Null
    //     0xda70d0: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e4d8] Null
    //     0xda70d4: ldr             x3, [x3, #0x4d8]
    // 0xda70d8: r0 = DefaultTypeTest()
    //     0xda70d8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda70dc: ldur            x3, [fp, #-0x28]
    // 0xda70e0: LoadField: r4 = r3->field_13
    //     0xda70e0: ldur            w4, [x3, #0x13]
    // 0xda70e4: DecompressPointer r4
    //     0xda70e4: add             x4, x4, HEAP, lsl #32
    // 0xda70e8: stur            x4, [fp, #-0x30]
    // 0xda70ec: cmp             w4, NULL
    // 0xda70f0: b.ne            #0xda71f0
    // 0xda70f4: ldur            x5, [fp, #-0x10]
    // 0xda70f8: ldur            x4, [fp, #-8]
    // 0xda70fc: LoadField: r2 = r4->field_b
    //     0xda70fc: ldur            w2, [x4, #0xb]
    // 0xda7100: DecompressPointer r2
    //     0xda7100: add             x2, x2, HEAP, lsl #32
    // 0xda7104: ldur            x0, [fp, #-0x20]
    // 0xda7108: r1 = Null
    //     0xda7108: mov             x1, NULL
    // 0xda710c: cmp             w0, NULL
    // 0xda7110: b.eq            #0xda713c
    // 0xda7114: cmp             w2, NULL
    // 0xda7118: b.eq            #0xda713c
    // 0xda711c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda711c: ldur            w4, [x2, #0x17]
    // 0xda7120: DecompressPointer r4
    //     0xda7120: add             x4, x4, HEAP, lsl #32
    // 0xda7124: r8 = X0? bound RenderObject
    //     0xda7124: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda7128: ldr             x8, [x8, #0x1a8]
    // 0xda712c: LoadField: r9 = r4->field_7
    //     0xda712c: ldur            x9, [x4, #7]
    // 0xda7130: r3 = Null
    //     0xda7130: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e4e8] Null
    //     0xda7134: ldr             x3, [x3, #0x4e8]
    // 0xda7138: blr             x9
    // 0xda713c: ldur            x0, [fp, #-0x20]
    // 0xda7140: ldur            x3, [fp, #-8]
    // 0xda7144: StoreField: r3->field_f = r0
    //     0xda7144: stur            w0, [x3, #0xf]
    //     0xda7148: ldurb           w16, [x3, #-1]
    //     0xda714c: ldurb           w17, [x0, #-1]
    //     0xda7150: and             x16, x17, x16, lsr #2
    //     0xda7154: tst             x16, HEAP, lsr #32
    //     0xda7158: b.eq            #0xda7160
    //     0xda715c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda7160: ldur            x3, [fp, #-0x28]
    // 0xda7164: LoadField: r2 = r3->field_b
    //     0xda7164: ldur            w2, [x3, #0xb]
    // 0xda7168: DecompressPointer r2
    //     0xda7168: add             x2, x2, HEAP, lsl #32
    // 0xda716c: ldur            x0, [fp, #-0x18]
    // 0xda7170: r1 = Null
    //     0xda7170: mov             x1, NULL
    // 0xda7174: cmp             w0, NULL
    // 0xda7178: b.eq            #0xda71a4
    // 0xda717c: cmp             w2, NULL
    // 0xda7180: b.eq            #0xda71a4
    // 0xda7184: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7184: ldur            w4, [x2, #0x17]
    // 0xda7188: DecompressPointer r4
    //     0xda7188: add             x4, x4, HEAP, lsl #32
    // 0xda718c: r8 = X0? bound RenderObject
    //     0xda718c: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda7190: ldr             x8, [x8, #0x1a8]
    // 0xda7194: LoadField: r9 = r4->field_7
    //     0xda7194: ldur            x9, [x4, #7]
    // 0xda7198: r3 = Null
    //     0xda7198: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e4f8] Null
    //     0xda719c: ldr             x3, [x3, #0x4f8]
    // 0xda71a0: blr             x9
    // 0xda71a4: ldur            x0, [fp, #-0x18]
    // 0xda71a8: ldur            x5, [fp, #-0x28]
    // 0xda71ac: StoreField: r5->field_13 = r0
    //     0xda71ac: stur            w0, [x5, #0x13]
    //     0xda71b0: ldurb           w16, [x5, #-1]
    //     0xda71b4: ldurb           w17, [x0, #-1]
    //     0xda71b8: and             x16, x17, x16, lsr #2
    //     0xda71bc: tst             x16, HEAP, lsr #32
    //     0xda71c0: b.eq            #0xda71c8
    //     0xda71c4: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda71c8: ldur            x0, [fp, #-0x18]
    // 0xda71cc: ldur            x1, [fp, #-0x10]
    // 0xda71d0: StoreField: r1->field_63 = r0
    //     0xda71d0: stur            w0, [x1, #0x63]
    //     0xda71d4: ldurb           w16, [x1, #-1]
    //     0xda71d8: ldurb           w17, [x0, #-1]
    //     0xda71dc: and             x16, x17, x16, lsr #2
    //     0xda71e0: tst             x16, HEAP, lsr #32
    //     0xda71e4: b.eq            #0xda71ec
    //     0xda71e8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda71ec: b               #0xda73dc
    // 0xda71f0: mov             x5, x3
    // 0xda71f4: ldur            x3, [fp, #-8]
    // 0xda71f8: LoadField: r6 = r3->field_b
    //     0xda71f8: ldur            w6, [x3, #0xb]
    // 0xda71fc: DecompressPointer r6
    //     0xda71fc: add             x6, x6, HEAP, lsl #32
    // 0xda7200: mov             x0, x4
    // 0xda7204: mov             x2, x6
    // 0xda7208: stur            x6, [fp, #-0x10]
    // 0xda720c: r1 = Null
    //     0xda720c: mov             x1, NULL
    // 0xda7210: cmp             w0, NULL
    // 0xda7214: b.eq            #0xda7240
    // 0xda7218: cmp             w2, NULL
    // 0xda721c: b.eq            #0xda7240
    // 0xda7220: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7220: ldur            w4, [x2, #0x17]
    // 0xda7224: DecompressPointer r4
    //     0xda7224: add             x4, x4, HEAP, lsl #32
    // 0xda7228: r8 = X0? bound RenderObject
    //     0xda7228: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda722c: ldr             x8, [x8, #0x1a8]
    // 0xda7230: LoadField: r9 = r4->field_7
    //     0xda7230: ldur            x9, [x4, #7]
    // 0xda7234: r3 = Null
    //     0xda7234: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e508] Null
    //     0xda7238: ldr             x3, [x3, #0x508]
    // 0xda723c: blr             x9
    // 0xda7240: ldur            x0, [fp, #-0x30]
    // 0xda7244: ldur            x3, [fp, #-8]
    // 0xda7248: StoreField: r3->field_13 = r0
    //     0xda7248: stur            w0, [x3, #0x13]
    //     0xda724c: ldurb           w16, [x3, #-1]
    //     0xda7250: ldurb           w17, [x0, #-1]
    //     0xda7254: and             x16, x17, x16, lsr #2
    //     0xda7258: tst             x16, HEAP, lsr #32
    //     0xda725c: b.eq            #0xda7264
    //     0xda7260: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda7264: ldur            x0, [fp, #-0x20]
    // 0xda7268: ldur            x2, [fp, #-0x10]
    // 0xda726c: r1 = Null
    //     0xda726c: mov             x1, NULL
    // 0xda7270: cmp             w0, NULL
    // 0xda7274: b.eq            #0xda72a0
    // 0xda7278: cmp             w2, NULL
    // 0xda727c: b.eq            #0xda72a0
    // 0xda7280: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7280: ldur            w4, [x2, #0x17]
    // 0xda7284: DecompressPointer r4
    //     0xda7284: add             x4, x4, HEAP, lsl #32
    // 0xda7288: r8 = X0? bound RenderObject
    //     0xda7288: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda728c: ldr             x8, [x8, #0x1a8]
    // 0xda7290: LoadField: r9 = r4->field_7
    //     0xda7290: ldur            x9, [x4, #7]
    // 0xda7294: r3 = Null
    //     0xda7294: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e518] Null
    //     0xda7298: ldr             x3, [x3, #0x518]
    // 0xda729c: blr             x9
    // 0xda72a0: ldur            x0, [fp, #-0x20]
    // 0xda72a4: ldur            x1, [fp, #-8]
    // 0xda72a8: StoreField: r1->field_f = r0
    //     0xda72a8: stur            w0, [x1, #0xf]
    //     0xda72ac: ldurb           w16, [x1, #-1]
    //     0xda72b0: ldurb           w17, [x0, #-1]
    //     0xda72b4: and             x16, x17, x16, lsr #2
    //     0xda72b8: tst             x16, HEAP, lsr #32
    //     0xda72bc: b.eq            #0xda72c4
    //     0xda72c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda72c4: ldur            x0, [fp, #-0x30]
    // 0xda72c8: LoadField: r3 = r0->field_7
    //     0xda72c8: ldur            w3, [x0, #7]
    // 0xda72cc: DecompressPointer r3
    //     0xda72cc: add             x3, x3, HEAP, lsl #32
    // 0xda72d0: stur            x3, [fp, #-8]
    // 0xda72d4: cmp             w3, NULL
    // 0xda72d8: b.eq            #0xda73f8
    // 0xda72dc: mov             x0, x3
    // 0xda72e0: r2 = Null
    //     0xda72e0: mov             x2, NULL
    // 0xda72e4: r1 = Null
    //     0xda72e4: mov             x1, NULL
    // 0xda72e8: r4 = LoadClassIdInstr(r0)
    //     0xda72e8: ldur            x4, [x0, #-1]
    //     0xda72ec: ubfx            x4, x4, #0xc, #0x14
    // 0xda72f0: cmp             x4, #0xc79
    // 0xda72f4: b.eq            #0xda730c
    // 0xda72f8: r8 = WrapParentData
    //     0xda72f8: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0xda72fc: ldr             x8, [x8, #0xaf0]
    // 0xda7300: r3 = Null
    //     0xda7300: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e528] Null
    //     0xda7304: ldr             x3, [x3, #0x528]
    // 0xda7308: r0 = DefaultTypeTest()
    //     0xda7308: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda730c: ldur            x3, [fp, #-0x28]
    // 0xda7310: LoadField: r2 = r3->field_b
    //     0xda7310: ldur            w2, [x3, #0xb]
    // 0xda7314: DecompressPointer r2
    //     0xda7314: add             x2, x2, HEAP, lsl #32
    // 0xda7318: ldur            x0, [fp, #-0x18]
    // 0xda731c: r1 = Null
    //     0xda731c: mov             x1, NULL
    // 0xda7320: cmp             w0, NULL
    // 0xda7324: b.eq            #0xda7350
    // 0xda7328: cmp             w2, NULL
    // 0xda732c: b.eq            #0xda7350
    // 0xda7330: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7330: ldur            w4, [x2, #0x17]
    // 0xda7334: DecompressPointer r4
    //     0xda7334: add             x4, x4, HEAP, lsl #32
    // 0xda7338: r8 = X0? bound RenderObject
    //     0xda7338: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda733c: ldr             x8, [x8, #0x1a8]
    // 0xda7340: LoadField: r9 = r4->field_7
    //     0xda7340: ldur            x9, [x4, #7]
    // 0xda7344: r3 = Null
    //     0xda7344: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e538] Null
    //     0xda7348: ldr             x3, [x3, #0x538]
    // 0xda734c: blr             x9
    // 0xda7350: ldur            x0, [fp, #-0x18]
    // 0xda7354: ldur            x1, [fp, #-0x28]
    // 0xda7358: StoreField: r1->field_13 = r0
    //     0xda7358: stur            w0, [x1, #0x13]
    //     0xda735c: ldurb           w16, [x1, #-1]
    //     0xda7360: ldurb           w17, [x0, #-1]
    //     0xda7364: and             x16, x17, x16, lsr #2
    //     0xda7368: tst             x16, HEAP, lsr #32
    //     0xda736c: b.eq            #0xda7374
    //     0xda7370: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda7374: ldur            x3, [fp, #-8]
    // 0xda7378: LoadField: r2 = r3->field_b
    //     0xda7378: ldur            w2, [x3, #0xb]
    // 0xda737c: DecompressPointer r2
    //     0xda737c: add             x2, x2, HEAP, lsl #32
    // 0xda7380: ldur            x0, [fp, #-0x18]
    // 0xda7384: r1 = Null
    //     0xda7384: mov             x1, NULL
    // 0xda7388: cmp             w0, NULL
    // 0xda738c: b.eq            #0xda73b8
    // 0xda7390: cmp             w2, NULL
    // 0xda7394: b.eq            #0xda73b8
    // 0xda7398: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7398: ldur            w4, [x2, #0x17]
    // 0xda739c: DecompressPointer r4
    //     0xda739c: add             x4, x4, HEAP, lsl #32
    // 0xda73a0: r8 = X0? bound RenderObject
    //     0xda73a0: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda73a4: ldr             x8, [x8, #0x1a8]
    // 0xda73a8: LoadField: r9 = r4->field_7
    //     0xda73a8: ldur            x9, [x4, #7]
    // 0xda73ac: r3 = Null
    //     0xda73ac: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e548] Null
    //     0xda73b0: ldr             x3, [x3, #0x548]
    // 0xda73b4: blr             x9
    // 0xda73b8: ldur            x0, [fp, #-0x18]
    // 0xda73bc: ldur            x1, [fp, #-8]
    // 0xda73c0: StoreField: r1->field_f = r0
    //     0xda73c0: stur            w0, [x1, #0xf]
    //     0xda73c4: ldurb           w16, [x1, #-1]
    //     0xda73c8: ldurb           w17, [x0, #-1]
    //     0xda73cc: and             x16, x17, x16, lsr #2
    //     0xda73d0: tst             x16, HEAP, lsr #32
    //     0xda73d4: b.eq            #0xda73dc
    //     0xda73d8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda73dc: r0 = Null
    //     0xda73dc: mov             x0, NULL
    // 0xda73e0: LeaveFrame
    //     0xda73e0: mov             SP, fp
    //     0xda73e4: ldp             fp, lr, [SP], #0x10
    // 0xda73e8: ret
    //     0xda73e8: ret             
    // 0xda73ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda73ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda73f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda73f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda73f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda73f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda73f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda73f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3015, size: 0x68, field offset: 0x68
//   transformed mixin,
abstract class _RenderWrap&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin extends _RenderWrap&RenderBox&ContainerRenderObjectMixin
     with RenderBoxContainerDefaultsMixin<X0 bound RenderBox, X1 bound ContainerBoxParentData> {

  _ defaultComputeDistanceToHighestActualBaseline(/* No info */) {
    // ** addr: 0x74d084, size: 0x2f0
    // 0x74d084: EnterFrame
    //     0x74d084: stp             fp, lr, [SP, #-0x10]!
    //     0x74d088: mov             fp, SP
    // 0x74d08c: AllocStack(0x58)
    //     0x74d08c: sub             SP, SP, #0x58
    // 0x74d090: SetupParameters(dynamic _ /* r2 => r3, fp-0x20 */)
    //     0x74d090: mov             x3, x2
    //     0x74d094: stur            x2, [fp, #-0x20]
    // 0x74d098: CheckStackOverflow
    //     0x74d098: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d09c: cmp             SP, x16
    //     0x74d0a0: b.ls            #0x74d328
    // 0x74d0a4: LoadField: r0 = r1->field_5f
    //     0x74d0a4: ldur            w0, [x1, #0x5f]
    // 0x74d0a8: DecompressPointer r0
    //     0x74d0a8: add             x0, x0, HEAP, lsl #32
    // 0x74d0ac: mov             x4, x0
    // 0x74d0b0: r5 = Null
    //     0x74d0b0: mov             x5, NULL
    // 0x74d0b4: stur            x5, [fp, #-0x10]
    // 0x74d0b8: stur            x4, [fp, #-0x18]
    // 0x74d0bc: CheckStackOverflow
    //     0x74d0bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d0c0: cmp             SP, x16
    //     0x74d0c4: b.ls            #0x74d330
    // 0x74d0c8: cmp             w4, NULL
    // 0x74d0cc: b.eq            #0x74d2f8
    // 0x74d0d0: LoadField: r6 = r4->field_7
    //     0x74d0d0: ldur            w6, [x4, #7]
    // 0x74d0d4: DecompressPointer r6
    //     0x74d0d4: add             x6, x6, HEAP, lsl #32
    // 0x74d0d8: stur            x6, [fp, #-8]
    // 0x74d0dc: cmp             w6, NULL
    // 0x74d0e0: b.eq            #0x74d338
    // 0x74d0e4: mov             x0, x6
    // 0x74d0e8: r2 = Null
    //     0x74d0e8: mov             x2, NULL
    // 0x74d0ec: r1 = Null
    //     0x74d0ec: mov             x1, NULL
    // 0x74d0f0: r4 = LoadClassIdInstr(r0)
    //     0x74d0f0: ldur            x4, [x0, #-1]
    //     0x74d0f4: ubfx            x4, x4, #0xc, #0x14
    // 0x74d0f8: cmp             x4, #0xc79
    // 0x74d0fc: b.eq            #0x74d114
    // 0x74d100: r8 = WrapParentData
    //     0x74d100: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x74d104: ldr             x8, [x8, #0xaf0]
    // 0x74d108: r3 = Null
    //     0x74d108: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e448] Null
    //     0x74d10c: ldr             x3, [x3, #0x448]
    // 0x74d110: r0 = DefaultTypeTest()
    //     0x74d110: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x74d114: r1 = 1
    //     0x74d114: movz            x1, #0x1
    // 0x74d118: r0 = AllocateContext()
    //     0x74d118: bl              #0xec126c  ; AllocateContextStub
    // 0x74d11c: mov             x4, x0
    // 0x74d120: ldur            x3, [fp, #-0x18]
    // 0x74d124: stur            x4, [fp, #-0x30]
    // 0x74d128: StoreField: r4->field_f = r3
    //     0x74d128: stur            w3, [x4, #0xf]
    // 0x74d12c: LoadField: r5 = r3->field_27
    //     0x74d12c: ldur            w5, [x3, #0x27]
    // 0x74d130: DecompressPointer r5
    //     0x74d130: add             x5, x5, HEAP, lsl #32
    // 0x74d134: stur            x5, [fp, #-0x28]
    // 0x74d138: cmp             w5, NULL
    // 0x74d13c: b.eq            #0x74d30c
    // 0x74d140: ldur            x6, [fp, #-8]
    // 0x74d144: mov             x0, x5
    // 0x74d148: r2 = Null
    //     0x74d148: mov             x2, NULL
    // 0x74d14c: r1 = Null
    //     0x74d14c: mov             x1, NULL
    // 0x74d150: r4 = LoadClassIdInstr(r0)
    //     0x74d150: ldur            x4, [x0, #-1]
    //     0x74d154: ubfx            x4, x4, #0xc, #0x14
    // 0x74d158: sub             x4, x4, #0xc83
    // 0x74d15c: cmp             x4, #1
    // 0x74d160: b.ls            #0x74d174
    // 0x74d164: r8 = BoxConstraints
    //     0x74d164: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x74d168: r3 = Null
    //     0x74d168: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e458] Null
    //     0x74d16c: ldr             x3, [x3, #0x458]
    // 0x74d170: r0 = BoxConstraints()
    //     0x74d170: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x74d174: ldur            x2, [fp, #-0x28]
    // 0x74d178: ldur            x3, [fp, #-0x20]
    // 0x74d17c: r0 = AllocateRecord2()
    //     0x74d17c: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x74d180: ldur            x2, [fp, #-0x30]
    // 0x74d184: r1 = Function '<anonymous closure>':.
    //     0x74d184: add             x1, PP, #0x45, lsl #12  ; [pp+0x456d0] AnonymousClosure: (0x74b7cc), in [package:flutter/src/rendering/box.dart] RenderBox::getDistanceToActualBaseline (0x74b4d4)
    //     0x74d188: ldr             x1, [x1, #0x6d0]
    // 0x74d18c: stur            x0, [fp, #-0x28]
    // 0x74d190: r0 = AllocateClosure()
    //     0x74d190: bl              #0xec1630  ; AllocateClosureStub
    // 0x74d194: r16 = <(BoxConstraints, TextBaseline), double?>
    //     0x74d194: add             x16, PP, #0x45, lsl #12  ; [pp+0x456d8] TypeArguments: <(BoxConstraints, TextBaseline), double?>
    //     0x74d198: ldr             x16, [x16, #0x6d8]
    // 0x74d19c: ldur            lr, [fp, #-0x18]
    // 0x74d1a0: stp             lr, x16, [SP, #0x18]
    // 0x74d1a4: r16 = Instance__Baseline
    //     0x74d1a4: add             x16, PP, #0x45, lsl #12  ; [pp+0x456e0] Obj!_Baseline@e117c1
    //     0x74d1a8: ldr             x16, [x16, #0x6e0]
    // 0x74d1ac: ldur            lr, [fp, #-0x28]
    // 0x74d1b0: stp             lr, x16, [SP, #8]
    // 0x74d1b4: str             x0, [SP]
    // 0x74d1b8: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x74d1b8: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x74d1bc: r0 = _computeIntrinsics()
    //     0x74d1bc: bl              #0x72d344  ; [package:flutter/src/rendering/box.dart] RenderBox::_computeIntrinsics
    // 0x74d1c0: mov             x1, x0
    // 0x74d1c4: ldur            x0, [fp, #-8]
    // 0x74d1c8: LoadField: r2 = r0->field_7
    //     0x74d1c8: ldur            w2, [x0, #7]
    // 0x74d1cc: DecompressPointer r2
    //     0x74d1cc: add             x2, x2, HEAP, lsl #32
    // 0x74d1d0: LoadField: d0 = r2->field_f
    //     0x74d1d0: ldur            d0, [x2, #0xf]
    // 0x74d1d4: cmp             w1, NULL
    // 0x74d1d8: b.ne            #0x74d1e4
    // 0x74d1dc: r2 = Null
    //     0x74d1dc: mov             x2, NULL
    // 0x74d1e0: b               #0x74d218
    // 0x74d1e4: LoadField: d1 = r1->field_7
    //     0x74d1e4: ldur            d1, [x1, #7]
    // 0x74d1e8: fadd            d2, d1, d0
    // 0x74d1ec: r1 = inline_Allocate_Double()
    //     0x74d1ec: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x74d1f0: add             x1, x1, #0x10
    //     0x74d1f4: cmp             x2, x1
    //     0x74d1f8: b.ls            #0x74d33c
    //     0x74d1fc: str             x1, [THR, #0x50]  ; THR::top
    //     0x74d200: sub             x1, x1, #0xf
    //     0x74d204: movz            x2, #0xe15c
    //     0x74d208: movk            x2, #0x3, lsl #16
    //     0x74d20c: stur            x2, [x1, #-1]
    // 0x74d210: StoreField: r1->field_7 = d2
    //     0x74d210: stur            d2, [x1, #7]
    // 0x74d214: mov             x2, x1
    // 0x74d218: ldur            x1, [fp, #-0x10]
    // 0x74d21c: cmp             w1, NULL
    // 0x74d220: b.eq            #0x74d280
    // 0x74d224: cmp             w2, NULL
    // 0x74d228: b.eq            #0x74d278
    // 0x74d22c: LoadField: d0 = r1->field_7
    //     0x74d22c: ldur            d0, [x1, #7]
    // 0x74d230: LoadField: d1 = r2->field_7
    //     0x74d230: ldur            d1, [x2, #7]
    // 0x74d234: fcmp            d0, d1
    // 0x74d238: b.lt            #0x74d244
    // 0x74d23c: LoadField: d0 = r2->field_7
    //     0x74d23c: ldur            d0, [x2, #7]
    // 0x74d240: b               #0x74d248
    // 0x74d244: LoadField: d0 = r1->field_7
    //     0x74d244: ldur            d0, [x1, #7]
    // 0x74d248: r1 = inline_Allocate_Double()
    //     0x74d248: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x74d24c: add             x1, x1, #0x10
    //     0x74d250: cmp             x2, x1
    //     0x74d254: b.ls            #0x74d358
    //     0x74d258: str             x1, [THR, #0x50]  ; THR::top
    //     0x74d25c: sub             x1, x1, #0xf
    //     0x74d260: movz            x2, #0xe15c
    //     0x74d264: movk            x2, #0x3, lsl #16
    //     0x74d268: stur            x2, [x1, #-1]
    // 0x74d26c: StoreField: r1->field_7 = d0
    //     0x74d26c: stur            d0, [x1, #7]
    // 0x74d270: mov             x5, x1
    // 0x74d274: b               #0x74d2e8
    // 0x74d278: r3 = true
    //     0x74d278: add             x3, NULL, #0x20  ; true
    // 0x74d27c: b               #0x74d284
    // 0x74d280: r3 = false
    //     0x74d280: add             x3, NULL, #0x30  ; false
    // 0x74d284: cmp             w1, NULL
    // 0x74d288: b.eq            #0x74d2c0
    // 0x74d28c: tbnz            w3, #4, #0x74d29c
    // 0x74d290: r4 = Null
    //     0x74d290: mov             x4, NULL
    // 0x74d294: r3 = Null
    //     0x74d294: mov             x3, NULL
    // 0x74d298: b               #0x74d2a4
    // 0x74d29c: mov             x4, x2
    // 0x74d2a0: mov             x3, x2
    // 0x74d2a4: cmp             w4, NULL
    // 0x74d2a8: b.ne            #0x74d2b4
    // 0x74d2ac: mov             x5, x1
    // 0x74d2b0: b               #0x74d2e8
    // 0x74d2b4: mov             x5, x3
    // 0x74d2b8: r3 = true
    //     0x74d2b8: add             x3, NULL, #0x20  ; true
    // 0x74d2bc: b               #0x74d2c4
    // 0x74d2c0: r5 = Null
    //     0x74d2c0: mov             x5, NULL
    // 0x74d2c4: cmp             w1, NULL
    // 0x74d2c8: b.ne            #0x74d2e4
    // 0x74d2cc: tbnz            w3, #4, #0x74d2d8
    // 0x74d2d0: mov             x1, x5
    // 0x74d2d4: b               #0x74d2dc
    // 0x74d2d8: mov             x1, x2
    // 0x74d2dc: mov             x5, x1
    // 0x74d2e0: b               #0x74d2e8
    // 0x74d2e4: r5 = Null
    //     0x74d2e4: mov             x5, NULL
    // 0x74d2e8: LoadField: r4 = r0->field_13
    //     0x74d2e8: ldur            w4, [x0, #0x13]
    // 0x74d2ec: DecompressPointer r4
    //     0x74d2ec: add             x4, x4, HEAP, lsl #32
    // 0x74d2f0: ldur            x3, [fp, #-0x20]
    // 0x74d2f4: b               #0x74d0b4
    // 0x74d2f8: mov             x1, x5
    // 0x74d2fc: mov             x0, x1
    // 0x74d300: LeaveFrame
    //     0x74d300: mov             SP, fp
    //     0x74d304: ldp             fp, lr, [SP], #0x10
    // 0x74d308: ret
    //     0x74d308: ret             
    // 0x74d30c: r0 = StateError()
    //     0x74d30c: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x74d310: mov             x1, x0
    // 0x74d314: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x74d314: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x74d318: StoreField: r1->field_b = r0
    //     0x74d318: stur            w0, [x1, #0xb]
    // 0x74d31c: mov             x0, x1
    // 0x74d320: r0 = Throw()
    //     0x74d320: bl              #0xec04b8  ; ThrowStub
    // 0x74d324: brk             #0
    // 0x74d328: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d328: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d32c: b               #0x74d0a4
    // 0x74d330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d330: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d334: b               #0x74d0c8
    // 0x74d338: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74d338: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x74d33c: SaveReg d2
    //     0x74d33c: str             q2, [SP, #-0x10]!
    // 0x74d340: SaveReg r0
    //     0x74d340: str             x0, [SP, #-8]!
    // 0x74d344: r0 = AllocateDouble()
    //     0x74d344: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74d348: mov             x1, x0
    // 0x74d34c: RestoreReg r0
    //     0x74d34c: ldr             x0, [SP], #8
    // 0x74d350: RestoreReg d2
    //     0x74d350: ldr             q2, [SP], #0x10
    // 0x74d354: b               #0x74d210
    // 0x74d358: SaveReg d0
    //     0x74d358: str             q0, [SP, #-0x10]!
    // 0x74d35c: SaveReg r0
    //     0x74d35c: str             x0, [SP, #-8]!
    // 0x74d360: r0 = AllocateDouble()
    //     0x74d360: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74d364: mov             x1, x0
    // 0x74d368: RestoreReg r0
    //     0x74d368: ldr             x0, [SP], #8
    // 0x74d36c: RestoreReg d0
    //     0x74d36c: ldr             q0, [SP], #0x10
    // 0x74d370: b               #0x74d26c
  }
  _ defaultPaint(/* No info */) {
    // ** addr: 0x79ee98, size: 0x128
    // 0x79ee98: EnterFrame
    //     0x79ee98: stp             fp, lr, [SP, #-0x10]!
    //     0x79ee9c: mov             fp, SP
    // 0x79eea0: AllocStack(0x38)
    //     0x79eea0: sub             SP, SP, #0x38
    // 0x79eea4: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */)
    //     0x79eea4: mov             x4, x2
    //     0x79eea8: stur            x2, [fp, #-0x18]
    // 0x79eeac: CheckStackOverflow
    //     0x79eeac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79eeb0: cmp             SP, x16
    //     0x79eeb4: b.ls            #0x79efac
    // 0x79eeb8: LoadField: r0 = r1->field_5f
    //     0x79eeb8: ldur            w0, [x1, #0x5f]
    // 0x79eebc: DecompressPointer r0
    //     0x79eebc: add             x0, x0, HEAP, lsl #32
    // 0x79eec0: LoadField: d0 = r3->field_7
    //     0x79eec0: ldur            d0, [x3, #7]
    // 0x79eec4: stur            d0, [fp, #-0x28]
    // 0x79eec8: LoadField: d1 = r3->field_f
    //     0x79eec8: ldur            d1, [x3, #0xf]
    // 0x79eecc: stur            d1, [fp, #-0x20]
    // 0x79eed0: mov             x3, x0
    // 0x79eed4: stur            x3, [fp, #-0x10]
    // 0x79eed8: CheckStackOverflow
    //     0x79eed8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79eedc: cmp             SP, x16
    //     0x79eee0: b.ls            #0x79efb4
    // 0x79eee4: cmp             w3, NULL
    // 0x79eee8: b.eq            #0x79ef9c
    // 0x79eeec: LoadField: r5 = r3->field_7
    //     0x79eeec: ldur            w5, [x3, #7]
    // 0x79eef0: DecompressPointer r5
    //     0x79eef0: add             x5, x5, HEAP, lsl #32
    // 0x79eef4: stur            x5, [fp, #-8]
    // 0x79eef8: cmp             w5, NULL
    // 0x79eefc: b.eq            #0x79efbc
    // 0x79ef00: mov             x0, x5
    // 0x79ef04: r2 = Null
    //     0x79ef04: mov             x2, NULL
    // 0x79ef08: r1 = Null
    //     0x79ef08: mov             x1, NULL
    // 0x79ef0c: r4 = LoadClassIdInstr(r0)
    //     0x79ef0c: ldur            x4, [x0, #-1]
    //     0x79ef10: ubfx            x4, x4, #0xc, #0x14
    // 0x79ef14: cmp             x4, #0xc79
    // 0x79ef18: b.eq            #0x79ef30
    // 0x79ef1c: r8 = WrapParentData
    //     0x79ef1c: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x79ef20: ldr             x8, [x8, #0xaf0]
    // 0x79ef24: r3 = Null
    //     0x79ef24: add             x3, PP, #0x44, lsl #12  ; [pp+0x44af8] Null
    //     0x79ef28: ldr             x3, [x3, #0xaf8]
    // 0x79ef2c: r0 = DefaultTypeTest()
    //     0x79ef2c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x79ef30: ldur            x0, [fp, #-8]
    // 0x79ef34: LoadField: r1 = r0->field_7
    //     0x79ef34: ldur            w1, [x0, #7]
    // 0x79ef38: DecompressPointer r1
    //     0x79ef38: add             x1, x1, HEAP, lsl #32
    // 0x79ef3c: LoadField: d0 = r1->field_7
    //     0x79ef3c: ldur            d0, [x1, #7]
    // 0x79ef40: ldur            d1, [fp, #-0x28]
    // 0x79ef44: fadd            d2, d0, d1
    // 0x79ef48: stur            d2, [fp, #-0x38]
    // 0x79ef4c: LoadField: d0 = r1->field_f
    //     0x79ef4c: ldur            d0, [x1, #0xf]
    // 0x79ef50: ldur            d3, [fp, #-0x20]
    // 0x79ef54: fadd            d4, d0, d3
    // 0x79ef58: stur            d4, [fp, #-0x30]
    // 0x79ef5c: r0 = Offset()
    //     0x79ef5c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x79ef60: ldur            d0, [fp, #-0x38]
    // 0x79ef64: StoreField: r0->field_7 = d0
    //     0x79ef64: stur            d0, [x0, #7]
    // 0x79ef68: ldur            d0, [fp, #-0x30]
    // 0x79ef6c: StoreField: r0->field_f = d0
    //     0x79ef6c: stur            d0, [x0, #0xf]
    // 0x79ef70: ldur            x1, [fp, #-0x18]
    // 0x79ef74: ldur            x2, [fp, #-0x10]
    // 0x79ef78: mov             x3, x0
    // 0x79ef7c: r0 = paintChild()
    //     0x79ef7c: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x79ef80: ldur            x1, [fp, #-8]
    // 0x79ef84: LoadField: r3 = r1->field_13
    //     0x79ef84: ldur            w3, [x1, #0x13]
    // 0x79ef88: DecompressPointer r3
    //     0x79ef88: add             x3, x3, HEAP, lsl #32
    // 0x79ef8c: ldur            x4, [fp, #-0x18]
    // 0x79ef90: ldur            d0, [fp, #-0x28]
    // 0x79ef94: ldur            d1, [fp, #-0x20]
    // 0x79ef98: b               #0x79eed4
    // 0x79ef9c: r0 = Null
    //     0x79ef9c: mov             x0, NULL
    // 0x79efa0: LeaveFrame
    //     0x79efa0: mov             SP, fp
    //     0x79efa4: ldp             fp, lr, [SP], #0x10
    // 0x79efa8: ret
    //     0x79efa8: ret             
    // 0x79efac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x79efac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x79efb0: b               #0x79eeb8
    // 0x79efb4: r0 = StackOverflowSharedWithFPURegs()
    //     0x79efb4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x79efb8: b               #0x79eee4
    // 0x79efbc: r0 = NullCastErrorSharedWithFPURegs()
    //     0x79efbc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ defaultHitTestChildren(/* No info */) {
    // ** addr: 0x7ffc38, size: 0x144
    // 0x7ffc38: EnterFrame
    //     0x7ffc38: stp             fp, lr, [SP, #-0x10]!
    //     0x7ffc3c: mov             fp, SP
    // 0x7ffc40: AllocStack(0x28)
    //     0x7ffc40: sub             SP, SP, #0x28
    // 0x7ffc44: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x7ffc44: mov             x4, x2
    //     0x7ffc48: stur            x2, [fp, #-0x18]
    //     0x7ffc4c: stur            x3, [fp, #-0x20]
    // 0x7ffc50: CheckStackOverflow
    //     0x7ffc50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ffc54: cmp             SP, x16
    //     0x7ffc58: b.ls            #0x7ffd68
    // 0x7ffc5c: LoadField: r0 = r1->field_63
    //     0x7ffc5c: ldur            w0, [x1, #0x63]
    // 0x7ffc60: DecompressPointer r0
    //     0x7ffc60: add             x0, x0, HEAP, lsl #32
    // 0x7ffc64: mov             x5, x0
    // 0x7ffc68: stur            x5, [fp, #-0x10]
    // 0x7ffc6c: CheckStackOverflow
    //     0x7ffc6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ffc70: cmp             SP, x16
    //     0x7ffc74: b.ls            #0x7ffd70
    // 0x7ffc78: cmp             w5, NULL
    // 0x7ffc7c: b.eq            #0x7ffd58
    // 0x7ffc80: LoadField: r6 = r5->field_7
    //     0x7ffc80: ldur            w6, [x5, #7]
    // 0x7ffc84: DecompressPointer r6
    //     0x7ffc84: add             x6, x6, HEAP, lsl #32
    // 0x7ffc88: stur            x6, [fp, #-8]
    // 0x7ffc8c: cmp             w6, NULL
    // 0x7ffc90: b.eq            #0x7ffd78
    // 0x7ffc94: mov             x0, x6
    // 0x7ffc98: r2 = Null
    //     0x7ffc98: mov             x2, NULL
    // 0x7ffc9c: r1 = Null
    //     0x7ffc9c: mov             x1, NULL
    // 0x7ffca0: r4 = LoadClassIdInstr(r0)
    //     0x7ffca0: ldur            x4, [x0, #-1]
    //     0x7ffca4: ubfx            x4, x4, #0xc, #0x14
    // 0x7ffca8: cmp             x4, #0xc79
    // 0x7ffcac: b.eq            #0x7ffcc4
    // 0x7ffcb0: r8 = WrapParentData
    //     0x7ffcb0: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x7ffcb4: ldr             x8, [x8, #0xaf0]
    // 0x7ffcb8: r3 = Null
    //     0x7ffcb8: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b08] Null
    //     0x7ffcbc: ldr             x3, [x3, #0xb08]
    // 0x7ffcc0: r0 = DefaultTypeTest()
    //     0x7ffcc0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7ffcc4: ldur            x0, [fp, #-8]
    // 0x7ffcc8: LoadField: r3 = r0->field_7
    //     0x7ffcc8: ldur            w3, [x0, #7]
    // 0x7ffccc: DecompressPointer r3
    //     0x7ffccc: add             x3, x3, HEAP, lsl #32
    // 0x7ffcd0: ldur            x1, [fp, #-0x20]
    // 0x7ffcd4: mov             x2, x3
    // 0x7ffcd8: stur            x3, [fp, #-0x28]
    // 0x7ffcdc: r0 = -()
    //     0x7ffcdc: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x7ffce0: ldur            x1, [fp, #-0x28]
    // 0x7ffce4: stur            x0, [fp, #-0x28]
    // 0x7ffce8: r0 = unary-()
    //     0x7ffce8: bl              #0x6a58ac  ; [dart:ui] Offset::unary-
    // 0x7ffcec: ldur            x1, [fp, #-0x18]
    // 0x7ffcf0: mov             x2, x0
    // 0x7ffcf4: r0 = pushOffset()
    //     0x7ffcf4: bl              #0x7faa44  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::pushOffset
    // 0x7ffcf8: ldur            x1, [fp, #-0x10]
    // 0x7ffcfc: r0 = LoadClassIdInstr(r1)
    //     0x7ffcfc: ldur            x0, [x1, #-1]
    //     0x7ffd00: ubfx            x0, x0, #0xc, #0x14
    // 0x7ffd04: ldur            x2, [fp, #-0x18]
    // 0x7ffd08: ldur            x3, [fp, #-0x28]
    // 0x7ffd0c: r0 = GDT[cid_x0 + 0xdf93]()
    //     0x7ffd0c: movz            x17, #0xdf93
    //     0x7ffd10: add             lr, x0, x17
    //     0x7ffd14: ldr             lr, [x21, lr, lsl #3]
    //     0x7ffd18: blr             lr
    // 0x7ffd1c: ldur            x1, [fp, #-0x18]
    // 0x7ffd20: stur            x0, [fp, #-0x10]
    // 0x7ffd24: r0 = popTransform()
    //     0x7ffd24: bl              #0x7fa9a8  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::popTransform
    // 0x7ffd28: ldur            x1, [fp, #-0x10]
    // 0x7ffd2c: tbz             w1, #4, #0x7ffd48
    // 0x7ffd30: ldur            x1, [fp, #-8]
    // 0x7ffd34: LoadField: r5 = r1->field_f
    //     0x7ffd34: ldur            w5, [x1, #0xf]
    // 0x7ffd38: DecompressPointer r5
    //     0x7ffd38: add             x5, x5, HEAP, lsl #32
    // 0x7ffd3c: ldur            x4, [fp, #-0x18]
    // 0x7ffd40: ldur            x3, [fp, #-0x20]
    // 0x7ffd44: b               #0x7ffc68
    // 0x7ffd48: r0 = true
    //     0x7ffd48: add             x0, NULL, #0x20  ; true
    // 0x7ffd4c: LeaveFrame
    //     0x7ffd4c: mov             SP, fp
    //     0x7ffd50: ldp             fp, lr, [SP], #0x10
    // 0x7ffd54: ret
    //     0x7ffd54: ret             
    // 0x7ffd58: r0 = false
    //     0x7ffd58: add             x0, NULL, #0x30  ; false
    // 0x7ffd5c: LeaveFrame
    //     0x7ffd5c: mov             SP, fp
    //     0x7ffd60: ldp             fp, lr, [SP], #0x10
    // 0x7ffd64: ret
    //     0x7ffd64: ret             
    // 0x7ffd68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ffd68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ffd6c: b               #0x7ffc5c
    // 0x7ffd70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ffd70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ffd74: b               #0x7ffc78
    // 0x7ffd78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7ffd78: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3016, size: 0x9c, field offset: 0x68
class RenderWrap extends _RenderWrap&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin {

  dynamic computeMinIntrinsicWidth(dynamic) {
    // ** addr: 0x734954, size: 0x24
    // 0x734954: EnterFrame
    //     0x734954: stp             fp, lr, [SP, #-0x10]!
    //     0x734958: mov             fp, SP
    // 0x73495c: ldr             x2, [fp, #0x10]
    // 0x734960: r1 = Function 'computeMinIntrinsicWidth':.
    //     0x734960: add             x1, PP, #0x55, lsl #12  ; [pp+0x552c8] AnonymousClosure: (0x734978), in [package:flutter/src/rendering/wrap.dart] RenderWrap::computeMinIntrinsicWidth (0x7349ec)
    //     0x734964: ldr             x1, [x1, #0x2c8]
    // 0x734968: r0 = AllocateClosure()
    //     0x734968: bl              #0xec1630  ; AllocateClosureStub
    // 0x73496c: LeaveFrame
    //     0x73496c: mov             SP, fp
    //     0x734970: ldp             fp, lr, [SP], #0x10
    // 0x734974: ret
    //     0x734974: ret             
  }
  [closure] double computeMinIntrinsicWidth(dynamic, double) {
    // ** addr: 0x734978, size: 0x74
    // 0x734978: EnterFrame
    //     0x734978: stp             fp, lr, [SP, #-0x10]!
    //     0x73497c: mov             fp, SP
    // 0x734980: ldr             x0, [fp, #0x18]
    // 0x734984: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x734984: ldur            w1, [x0, #0x17]
    // 0x734988: DecompressPointer r1
    //     0x734988: add             x1, x1, HEAP, lsl #32
    // 0x73498c: CheckStackOverflow
    //     0x73498c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734990: cmp             SP, x16
    //     0x734994: b.ls            #0x7349d4
    // 0x734998: ldr             x2, [fp, #0x10]
    // 0x73499c: r0 = computeMinIntrinsicWidth()
    //     0x73499c: bl              #0x7349ec  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::computeMinIntrinsicWidth
    // 0x7349a0: r0 = inline_Allocate_Double()
    //     0x7349a0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7349a4: add             x0, x0, #0x10
    //     0x7349a8: cmp             x1, x0
    //     0x7349ac: b.ls            #0x7349dc
    //     0x7349b0: str             x0, [THR, #0x50]  ; THR::top
    //     0x7349b4: sub             x0, x0, #0xf
    //     0x7349b8: movz            x1, #0xe15c
    //     0x7349bc: movk            x1, #0x3, lsl #16
    //     0x7349c0: stur            x1, [x0, #-1]
    // 0x7349c4: StoreField: r0->field_7 = d0
    //     0x7349c4: stur            d0, [x0, #7]
    // 0x7349c8: LeaveFrame
    //     0x7349c8: mov             SP, fp
    //     0x7349cc: ldp             fp, lr, [SP], #0x10
    // 0x7349d0: ret
    //     0x7349d0: ret             
    // 0x7349d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7349d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7349d8: b               #0x734998
    // 0x7349dc: SaveReg d0
    //     0x7349dc: str             q0, [SP, #-0x10]!
    // 0x7349e0: r0 = AllocateDouble()
    //     0x7349e0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7349e4: RestoreReg d0
    //     0x7349e4: ldr             q0, [SP], #0x10
    // 0x7349e8: b               #0x7349c4
  }
  _ computeMinIntrinsicWidth(/* No info */) {
    // ** addr: 0x7349ec, size: 0x178
    // 0x7349ec: EnterFrame
    //     0x7349ec: stp             fp, lr, [SP, #-0x10]!
    //     0x7349f0: mov             fp, SP
    // 0x7349f4: AllocStack(0x48)
    //     0x7349f4: sub             SP, SP, #0x48
    // 0x7349f8: CheckStackOverflow
    //     0x7349f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7349fc: cmp             SP, x16
    //     0x734a00: b.ls            #0x734b50
    // 0x734a04: LoadField: r0 = r1->field_5f
    //     0x734a04: ldur            w0, [x1, #0x5f]
    // 0x734a08: DecompressPointer r0
    //     0x734a08: add             x0, x0, HEAP, lsl #32
    // 0x734a0c: mov             x1, x0
    // 0x734a10: d0 = 0.000000
    //     0x734a10: eor             v0.16b, v0.16b, v0.16b
    // 0x734a14: stur            x1, [fp, #-8]
    // 0x734a18: stur            d0, [fp, #-0x18]
    // 0x734a1c: CheckStackOverflow
    //     0x734a1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734a20: cmp             SP, x16
    //     0x734a24: b.ls            #0x734b58
    // 0x734a28: cmp             w1, NULL
    // 0x734a2c: b.eq            #0x734b3c
    // 0x734a30: r0 = LoadClassIdInstr(r1)
    //     0x734a30: ldur            x0, [x1, #-1]
    //     0x734a34: ubfx            x0, x0, #0xc, #0x14
    // 0x734a38: str             x1, [SP]
    // 0x734a3c: r0 = GDT[cid_x0 + 0x1220d]()
    //     0x734a3c: movz            x17, #0x220d
    //     0x734a40: movk            x17, #0x1, lsl #16
    //     0x734a44: add             lr, x0, x17
    //     0x734a48: ldr             lr, [x21, lr, lsl #3]
    //     0x734a4c: blr             lr
    // 0x734a50: r16 = <double, double>
    //     0x734a50: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c400] TypeArguments: <double, double>
    //     0x734a54: ldr             x16, [x16, #0x400]
    // 0x734a58: ldur            lr, [fp, #-8]
    // 0x734a5c: stp             lr, x16, [SP, #0x18]
    // 0x734a60: r16 = Instance__IntrinsicDimension
    //     0x734a60: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c410] Obj!_IntrinsicDimension@e35b81
    //     0x734a64: ldr             x16, [x16, #0x410]
    // 0x734a68: r30 = inf
    //     0x734a68: ldr             lr, [PP, #0x49e0]  ; [pp+0x49e0] inf
    // 0x734a6c: stp             lr, x16, [SP, #8]
    // 0x734a70: str             x0, [SP]
    // 0x734a74: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x734a74: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x734a78: r0 = _computeIntrinsics()
    //     0x734a78: bl              #0x72d344  ; [package:flutter/src/rendering/box.dart] RenderBox::_computeIntrinsics
    // 0x734a7c: LoadField: d0 = r0->field_7
    //     0x734a7c: ldur            d0, [x0, #7]
    // 0x734a80: ldur            d1, [fp, #-0x18]
    // 0x734a84: fcmp            d1, d0
    // 0x734a88: b.le            #0x734a98
    // 0x734a8c: mov             v0.16b, v1.16b
    // 0x734a90: d2 = 0.000000
    //     0x734a90: eor             v2.16b, v2.16b, v2.16b
    // 0x734a94: b               #0x734adc
    // 0x734a98: fcmp            d0, d1
    // 0x734a9c: b.le            #0x734aac
    // 0x734aa0: LoadField: d0 = r0->field_7
    //     0x734aa0: ldur            d0, [x0, #7]
    // 0x734aa4: d2 = 0.000000
    //     0x734aa4: eor             v2.16b, v2.16b, v2.16b
    // 0x734aa8: b               #0x734adc
    // 0x734aac: d2 = 0.000000
    //     0x734aac: eor             v2.16b, v2.16b, v2.16b
    // 0x734ab0: fcmp            d1, d2
    // 0x734ab4: b.ne            #0x734ac4
    // 0x734ab8: fadd            d3, d1, d0
    // 0x734abc: mov             v0.16b, v3.16b
    // 0x734ac0: b               #0x734adc
    // 0x734ac4: LoadField: d0 = r0->field_7
    //     0x734ac4: ldur            d0, [x0, #7]
    // 0x734ac8: fcmp            d0, d0
    // 0x734acc: b.vc            #0x734ad8
    // 0x734ad0: LoadField: d0 = r0->field_7
    //     0x734ad0: ldur            d0, [x0, #7]
    // 0x734ad4: b               #0x734adc
    // 0x734ad8: mov             v0.16b, v1.16b
    // 0x734adc: ldur            x0, [fp, #-8]
    // 0x734ae0: stur            d0, [fp, #-0x20]
    // 0x734ae4: LoadField: r3 = r0->field_7
    //     0x734ae4: ldur            w3, [x0, #7]
    // 0x734ae8: DecompressPointer r3
    //     0x734ae8: add             x3, x3, HEAP, lsl #32
    // 0x734aec: stur            x3, [fp, #-0x10]
    // 0x734af0: cmp             w3, NULL
    // 0x734af4: b.eq            #0x734b60
    // 0x734af8: mov             x0, x3
    // 0x734afc: r2 = Null
    //     0x734afc: mov             x2, NULL
    // 0x734b00: r1 = Null
    //     0x734b00: mov             x1, NULL
    // 0x734b04: r4 = LoadClassIdInstr(r0)
    //     0x734b04: ldur            x4, [x0, #-1]
    //     0x734b08: ubfx            x4, x4, #0xc, #0x14
    // 0x734b0c: cmp             x4, #0xc79
    // 0x734b10: b.eq            #0x734b28
    // 0x734b14: r8 = WrapParentData
    //     0x734b14: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x734b18: ldr             x8, [x8, #0xaf0]
    // 0x734b1c: r3 = Null
    //     0x734b1c: add             x3, PP, #0x55, lsl #12  ; [pp+0x552d0] Null
    //     0x734b20: ldr             x3, [x3, #0x2d0]
    // 0x734b24: r0 = DefaultTypeTest()
    //     0x734b24: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x734b28: ldur            x0, [fp, #-0x10]
    // 0x734b2c: LoadField: r1 = r0->field_13
    //     0x734b2c: ldur            w1, [x0, #0x13]
    // 0x734b30: DecompressPointer r1
    //     0x734b30: add             x1, x1, HEAP, lsl #32
    // 0x734b34: ldur            d0, [fp, #-0x20]
    // 0x734b38: b               #0x734a14
    // 0x734b3c: mov             v1.16b, v0.16b
    // 0x734b40: mov             v0.16b, v1.16b
    // 0x734b44: LeaveFrame
    //     0x734b44: mov             SP, fp
    //     0x734b48: ldp             fp, lr, [SP], #0x10
    // 0x734b4c: ret
    //     0x734b4c: ret             
    // 0x734b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x734b50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x734b54: b               #0x734a04
    // 0x734b58: r0 = StackOverflowSharedWithFPURegs()
    //     0x734b58: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x734b5c: b               #0x734a28
    // 0x734b60: r0 = NullCastErrorSharedWithFPURegs()
    //     0x734b60: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ computeDryBaseline(/* No info */) {
    // ** addr: 0x745bf0, size: 0x170
    // 0x745bf0: EnterFrame
    //     0x745bf0: stp             fp, lr, [SP, #-0x10]!
    //     0x745bf4: mov             fp, SP
    // 0x745bf8: AllocStack(0x38)
    //     0x745bf8: sub             SP, SP, #0x38
    // 0x745bfc: SetupParameters(RenderWrap this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x745bfc: mov             x0, x2
    //     0x745c00: stur            x1, [fp, #-8]
    //     0x745c04: stur            x2, [fp, #-0x10]
    //     0x745c08: stur            x3, [fp, #-0x18]
    // 0x745c0c: CheckStackOverflow
    //     0x745c0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x745c10: cmp             SP, x16
    //     0x745c14: b.ls            #0x745d58
    // 0x745c18: r1 = 3
    //     0x745c18: movz            x1, #0x3
    // 0x745c1c: r0 = AllocateContext()
    //     0x745c1c: bl              #0xec126c  ; AllocateContextStub
    // 0x745c20: mov             x3, x0
    // 0x745c24: ldur            x0, [fp, #-0x18]
    // 0x745c28: stur            x3, [fp, #-0x20]
    // 0x745c2c: StoreField: r3->field_f = r0
    //     0x745c2c: stur            w0, [x3, #0xf]
    // 0x745c30: ldur            x0, [fp, #-0x10]
    // 0x745c34: r2 = Null
    //     0x745c34: mov             x2, NULL
    // 0x745c38: r1 = Null
    //     0x745c38: mov             x1, NULL
    // 0x745c3c: r4 = 60
    //     0x745c3c: movz            x4, #0x3c
    // 0x745c40: branchIfSmi(r0, 0x745c4c)
    //     0x745c40: tbz             w0, #0, #0x745c4c
    // 0x745c44: r4 = LoadClassIdInstr(r0)
    //     0x745c44: ldur            x4, [x0, #-1]
    //     0x745c48: ubfx            x4, x4, #0xc, #0x14
    // 0x745c4c: sub             x4, x4, #0xc83
    // 0x745c50: cmp             x4, #1
    // 0x745c54: b.ls            #0x745c68
    // 0x745c58: r8 = BoxConstraints
    //     0x745c58: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x745c5c: r3 = Null
    //     0x745c5c: add             x3, PP, #0x4e, lsl #12  ; [pp+0x4e428] Null
    //     0x745c60: ldr             x3, [x3, #0x428]
    // 0x745c64: r0 = BoxConstraints()
    //     0x745c64: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x745c68: ldur            x1, [fp, #-8]
    // 0x745c6c: LoadField: r0 = r1->field_5f
    //     0x745c6c: ldur            w0, [x1, #0x5f]
    // 0x745c70: DecompressPointer r0
    //     0x745c70: add             x0, x0, HEAP, lsl #32
    // 0x745c74: cmp             w0, NULL
    // 0x745c78: b.ne            #0x745c8c
    // 0x745c7c: r0 = Null
    //     0x745c7c: mov             x0, NULL
    // 0x745c80: LeaveFrame
    //     0x745c80: mov             SP, fp
    //     0x745c84: ldp             fp, lr, [SP], #0x10
    // 0x745c88: ret
    //     0x745c88: ret             
    // 0x745c8c: ldur            x0, [fp, #-0x10]
    // 0x745c90: ldur            x2, [fp, #-0x20]
    // 0x745c94: LoadField: d0 = r0->field_f
    //     0x745c94: ldur            d0, [x0, #0xf]
    // 0x745c98: stur            d0, [fp, #-0x38]
    // 0x745c9c: r0 = BoxConstraints()
    //     0x745c9c: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x745ca0: StoreField: r0->field_7 = rZR
    //     0x745ca0: stur            xzr, [x0, #7]
    // 0x745ca4: ldur            d0, [fp, #-0x38]
    // 0x745ca8: StoreField: r0->field_f = d0
    //     0x745ca8: stur            d0, [x0, #0xf]
    // 0x745cac: ArrayStore: r0[0] = rZR  ; List_8
    //     0x745cac: stur            xzr, [x0, #0x17]
    // 0x745cb0: d0 = inf
    //     0x745cb0: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x745cb4: StoreField: r0->field_1f = d0
    //     0x745cb4: stur            d0, [x0, #0x1f]
    // 0x745cb8: ldur            x4, [fp, #-0x20]
    // 0x745cbc: StoreField: r4->field_13 = r0
    //     0x745cbc: stur            w0, [x4, #0x13]
    // 0x745cc0: ldur            x1, [fp, #-8]
    // 0x745cc4: ldur            x2, [fp, #-0x10]
    // 0x745cc8: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static.
    //     0x745cc8: add             x3, PP, #0x44, lsl #12  ; [pp+0x44d20] Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static. (0x7e54fb140c14)
    //     0x745ccc: ldr             x3, [x3, #0xd20]
    // 0x745cd0: r0 = _computeRuns()
    //     0x745cd0: bl              #0x746720  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::_computeRuns
    // 0x745cd4: mov             x3, x0
    // 0x745cd8: stur            x3, [fp, #-0x28]
    // 0x745cdc: mov             x4, x1
    // 0x745ce0: mov             x1, x3
    // 0x745ce4: ldur            x2, [fp, #-0x10]
    // 0x745ce8: stur            x4, [fp, #-0x18]
    // 0x745cec: r0 = _AxisSize.applyConstraints()
    //     0x745cec: bl              #0x7466e8  ; [package:flutter/src/rendering/wrap.dart] ::_AxisSize.applyConstraints
    // 0x745cf0: mov             x3, x0
    // 0x745cf4: ldur            x0, [fp, #-0x20]
    // 0x745cf8: stur            x3, [fp, #-0x10]
    // 0x745cfc: ArrayStore: r0[0] = rNULL  ; List_4
    //     0x745cfc: stur            NULL, [x0, #0x17]
    // 0x745d00: mov             x2, x0
    // 0x745d04: r1 = Function 'findHighestBaseline':.
    //     0x745d04: add             x1, PP, #0x4e, lsl #12  ; [pp+0x4e438] AnonymousClosure: (0x746c98), in [package:flutter/src/rendering/wrap.dart] RenderWrap::computeDryBaseline (0x745bf0)
    //     0x745d08: ldr             x1, [x1, #0x438]
    // 0x745d0c: r0 = AllocateClosure()
    //     0x745d0c: bl              #0xec1630  ; AllocateClosureStub
    // 0x745d10: ldur            x2, [fp, #-0x20]
    // 0x745d14: r1 = Function 'getChildSize':.
    //     0x745d14: add             x1, PP, #0x4e, lsl #12  ; [pp+0x4e440] AnonymousClosure: (0x746c54), in [package:flutter/src/rendering/wrap.dart] RenderWrap::computeDryBaseline (0x745bf0)
    //     0x745d18: ldr             x1, [x1, #0x440]
    // 0x745d1c: stur            x0, [fp, #-0x30]
    // 0x745d20: r0 = AllocateClosure()
    //     0x745d20: bl              #0xec1630  ; AllocateClosureStub
    // 0x745d24: ldur            x1, [fp, #-8]
    // 0x745d28: ldur            x2, [fp, #-0x18]
    // 0x745d2c: ldur            x3, [fp, #-0x28]
    // 0x745d30: ldur            x5, [fp, #-0x10]
    // 0x745d34: ldur            x6, [fp, #-0x30]
    // 0x745d38: mov             x7, x0
    // 0x745d3c: r0 = _positionChildren()
    //     0x745d3c: bl              #0x745d60  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::_positionChildren
    // 0x745d40: ldur            x1, [fp, #-0x20]
    // 0x745d44: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x745d44: ldur            w0, [x1, #0x17]
    // 0x745d48: DecompressPointer r0
    //     0x745d48: add             x0, x0, HEAP, lsl #32
    // 0x745d4c: LeaveFrame
    //     0x745d4c: mov             SP, fp
    //     0x745d50: ldp             fp, lr, [SP], #0x10
    // 0x745d54: ret
    //     0x745d54: ret             
    // 0x745d58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x745d58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x745d5c: b               #0x745c18
  }
  _ _positionChildren(/* No info */) {
    // ** addr: 0x745d60, size: 0x498
    // 0x745d60: EnterFrame
    //     0x745d60: stp             fp, lr, [SP, #-0x10]!
    //     0x745d64: mov             fp, SP
    // 0x745d68: AllocStack(0xc0)
    //     0x745d68: sub             SP, SP, #0xc0
    // 0x745d6c: SetupParameters(RenderWrap this /* r1 => r6, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r2, fp-0x28 */, dynamic _ /* r7 => r0, fp-0x30 */)
    //     0x745d6c: mov             x4, x2
    //     0x745d70: stur            x2, [fp, #-0x10]
    //     0x745d74: mov             x2, x6
    //     0x745d78: stur            x6, [fp, #-0x28]
    //     0x745d7c: mov             x6, x1
    //     0x745d80: mov             x0, x7
    //     0x745d84: stur            x1, [fp, #-8]
    //     0x745d88: stur            x3, [fp, #-0x18]
    //     0x745d8c: stur            x5, [fp, #-0x20]
    //     0x745d90: stur            x7, [fp, #-0x30]
    // 0x745d94: CheckStackOverflow
    //     0x745d94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x745d98: cmp             SP, x16
    //     0x745d9c: b.ls            #0x7461e0
    // 0x745da0: mov             x1, x6
    // 0x745da4: r0 = size()
    //     0x745da4: bl              #0x7ab968  ; [package:flutter/src/gestures/events.dart] PointerEvent::size
    // 0x745da8: ldur            x1, [fp, #-0x18]
    // 0x745dac: stur            d0, [fp, #-0x68]
    // 0x745db0: r0 = r()
    //     0x745db0: bl              #0xd639e8  ; [dart:ui] Color::r
    // 0x745db4: mov             v1.16b, v0.16b
    // 0x745db8: d0 = 0.000000
    //     0x745db8: eor             v0.16b, v0.16b, v0.16b
    // 0x745dbc: fcmp            d0, d1
    // 0x745dc0: b.le            #0x745dcc
    // 0x745dc4: d1 = 0.000000
    //     0x745dc4: eor             v1.16b, v1.16b, v1.16b
    // 0x745dc8: b               #0x745df4
    // 0x745dcc: fcmp            d1, d0
    // 0x745dd0: b.gt            #0x745df4
    // 0x745dd4: fcmp            d0, d0
    // 0x745dd8: b.ne            #0x745de8
    // 0x745ddc: fadd            d2, d1, d0
    // 0x745de0: mov             v1.16b, v2.16b
    // 0x745de4: b               #0x745df4
    // 0x745de8: fcmp            d1, d1
    // 0x745dec: b.vs            #0x745df4
    // 0x745df0: d1 = 0.000000
    //     0x745df0: eor             v1.16b, v1.16b, v1.16b
    // 0x745df4: ldur            x1, [fp, #-8]
    // 0x745df8: stur            d1, [fp, #-0x70]
    // 0x745dfc: r0 = _areAxesFlipped()
    //     0x745dfc: bl              #0x74660c  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::_areAxesFlipped
    // 0x745e00: mov             x3, x0
    // 0x745e04: stur            x3, [fp, #-0x38]
    // 0x745e08: mov             x2, x1
    // 0x745e0c: stur            x2, [fp, #-0x18]
    // 0x745e10: tbnz            w2, #4, #0x745e30
    // 0x745e14: ldur            x1, [fp, #-8]
    // 0x745e18: r0 = crossAxisAlignment()
    //     0x745e18: bl              #0x746600  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::crossAxisAlignment
    // 0x745e1c: r1 = Instance_WrapCrossAlignment
    //     0x745e1c: add             x1, PP, #0x27, lsl #12  ; [pp+0x27618] Obj!WrapCrossAlignment@e35201
    //     0x745e20: ldr             x1, [x1, #0x618]
    // 0x745e24: r0 = _flipped()
    //     0x745e24: bl              #0x7465c8  ; [package:flutter/src/rendering/wrap.dart] WrapCrossAlignment::_flipped
    // 0x745e28: mov             x2, x0
    // 0x745e2c: b               #0x745e40
    // 0x745e30: ldur            x1, [fp, #-8]
    // 0x745e34: r0 = crossAxisAlignment()
    //     0x745e34: bl              #0x746600  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::crossAxisAlignment
    // 0x745e38: r2 = Instance_WrapCrossAlignment
    //     0x745e38: add             x2, PP, #0x27, lsl #12  ; [pp+0x27618] Obj!WrapCrossAlignment@e35201
    //     0x745e3c: ldr             x2, [x2, #0x618]
    // 0x745e40: ldur            x0, [fp, #-0x10]
    // 0x745e44: ldur            x3, [fp, #-0x38]
    // 0x745e48: ldur            x1, [fp, #-8]
    // 0x745e4c: stur            x2, [fp, #-0x40]
    // 0x745e50: r0 = runAlignment()
    //     0x745e50: bl              #0x7465bc  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::runAlignment
    // 0x745e54: ldur            x1, [fp, #-8]
    // 0x745e58: r0 = runSpacing()
    //     0x745e58: bl              #0x7465b4  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::runSpacing
    // 0x745e5c: ldur            x1, [fp, #-0x10]
    // 0x745e60: stur            d0, [fp, #-0x78]
    // 0x745e64: r0 = LoadClassIdInstr(r1)
    //     0x745e64: ldur            x0, [x1, #-1]
    //     0x745e68: ubfx            x0, x0, #0xc, #0x14
    // 0x745e6c: str             x1, [SP]
    // 0x745e70: r0 = GDT[cid_x0 + 0xc834]()
    //     0x745e70: movz            x17, #0xc834
    //     0x745e74: add             lr, x0, x17
    //     0x745e78: ldr             lr, [x21, lr, lsl #3]
    //     0x745e7c: blr             lr
    // 0x745e80: r2 = LoadInt32Instr(r0)
    //     0x745e80: sbfx            x2, x0, #1, #0x1f
    //     0x745e84: tbz             w0, #0, #0x745e8c
    //     0x745e88: ldur            x2, [x0, #7]
    // 0x745e8c: ldur            d0, [fp, #-0x70]
    // 0x745e90: ldur            d1, [fp, #-0x78]
    // 0x745e94: ldur            x3, [fp, #-0x18]
    // 0x745e98: r1 = Instance_WrapAlignment
    //     0x745e98: add             x1, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0x745e9c: ldr             x1, [x1, #0x610]
    // 0x745ea0: r0 = _distributeSpace()
    //     0x745ea0: bl              #0x7461f8  ; [package:flutter/src/rendering/wrap.dart] WrapAlignment::_distributeSpace
    // 0x745ea4: mov             x3, x0
    // 0x745ea8: stur            x3, [fp, #-0x50]
    // 0x745eac: mov             x4, x1
    // 0x745eb0: ldur            x0, [fp, #-0x38]
    // 0x745eb4: stur            x4, [fp, #-0x48]
    // 0x745eb8: tbnz            w0, #4, #0x745ed4
    // 0x745ebc: ldur            x2, [fp, #-8]
    // 0x745ec0: r1 = Function 'childBefore':.
    //     0x745ec0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44b50] AnonymousClosure: (0x746640), of [package:flutter/src/rendering/wrap.dart] _RenderWrap&RenderBox&ContainerRenderObjectMixin
    //     0x745ec4: ldr             x1, [x1, #0xb50]
    // 0x745ec8: r0 = AllocateClosure()
    //     0x745ec8: bl              #0xec1630  ; AllocateClosureStub
    // 0x745ecc: mov             x2, x0
    // 0x745ed0: b               #0x745ee8
    // 0x745ed4: ldur            x2, [fp, #-8]
    // 0x745ed8: r1 = Function 'childAfter':.
    //     0x745ed8: add             x1, PP, #0x44, lsl #12  ; [pp+0x44b58] AnonymousClosure: (0x734b64), of [package:flutter/src/rendering/wrap.dart] _RenderWrap&RenderBox&ContainerRenderObjectMixin
    //     0x745edc: ldr             x1, [x1, #0xb58]
    // 0x745ee0: r0 = AllocateClosure()
    //     0x745ee0: bl              #0xec1630  ; AllocateClosureStub
    // 0x745ee4: mov             x2, x0
    // 0x745ee8: ldur            x0, [fp, #-0x18]
    // 0x745eec: stur            x2, [fp, #-8]
    // 0x745ef0: tbnz            w0, #4, #0x745f1c
    // 0x745ef4: ldur            x1, [fp, #-0x10]
    // 0x745ef8: r0 = LoadClassIdInstr(r1)
    //     0x745ef8: ldur            x0, [x1, #-1]
    //     0x745efc: ubfx            x0, x0, #0xc, #0x14
    // 0x745f00: r0 = GDT[cid_x0 + 0x131af]()
    //     0x745f00: movz            x17, #0x31af
    //     0x745f04: movk            x17, #0x1, lsl #16
    //     0x745f08: add             lr, x0, x17
    //     0x745f0c: ldr             lr, [x21, lr, lsl #3]
    //     0x745f10: blr             lr
    // 0x745f14: mov             x1, x0
    // 0x745f18: b               #0x745f20
    // 0x745f1c: ldur            x1, [fp, #-0x10]
    // 0x745f20: ldur            x5, [fp, #-0x20]
    // 0x745f24: ldur            x4, [fp, #-0x40]
    // 0x745f28: ldur            x2, [fp, #-0x50]
    // 0x745f2c: ldur            x3, [fp, #-0x48]
    // 0x745f30: r0 = LoadClassIdInstr(r1)
    //     0x745f30: ldur            x0, [x1, #-1]
    //     0x745f34: ubfx            x0, x0, #0xc, #0x14
    // 0x745f38: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x745f38: movz            x17, #0xd35d
    //     0x745f3c: add             lr, x0, x17
    //     0x745f40: ldr             lr, [x21, lr, lsl #3]
    //     0x745f44: blr             lr
    // 0x745f48: mov             x2, x0
    // 0x745f4c: ldur            x0, [fp, #-0x50]
    // 0x745f50: stur            x2, [fp, #-0x10]
    // 0x745f54: LoadField: d0 = r0->field_7
    //     0x745f54: ldur            d0, [x0, #7]
    // 0x745f58: ldur            x0, [fp, #-0x20]
    // 0x745f5c: LoadField: d1 = r0->field_7
    //     0x745f5c: ldur            d1, [x0, #7]
    // 0x745f60: ldur            x0, [fp, #-0x40]
    // 0x745f64: stur            d1, [fp, #-0x80]
    // 0x745f68: LoadField: r3 = r0->field_7
    //     0x745f68: ldur            x3, [x0, #7]
    // 0x745f6c: ldur            x0, [fp, #-0x48]
    // 0x745f70: stur            x3, [fp, #-0x58]
    // 0x745f74: LoadField: d2 = r0->field_7
    //     0x745f74: ldur            d2, [x0, #7]
    // 0x745f78: stur            d2, [fp, #-0x78]
    // 0x745f7c: stur            d0, [fp, #-0x70]
    // 0x745f80: CheckStackOverflow
    //     0x745f80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x745f84: cmp             SP, x16
    //     0x745f88: b.ls            #0x7461e8
    // 0x745f8c: r0 = LoadClassIdInstr(r2)
    //     0x745f8c: ldur            x0, [x2, #-1]
    //     0x745f90: ubfx            x0, x0, #0xc, #0x14
    // 0x745f94: mov             x1, x2
    // 0x745f98: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x745f98: movz            x17, #0x292d
    //     0x745f9c: movk            x17, #0x1, lsl #16
    //     0x745fa0: add             lr, x0, x17
    //     0x745fa4: ldr             lr, [x21, lr, lsl #3]
    //     0x745fa8: blr             lr
    // 0x745fac: tbnz            w0, #4, #0x7461d0
    // 0x745fb0: ldur            x2, [fp, #-0x10]
    // 0x745fb4: ldur            d0, [fp, #-0x80]
    // 0x745fb8: r0 = LoadClassIdInstr(r2)
    //     0x745fb8: ldur            x0, [x2, #-1]
    //     0x745fbc: ubfx            x0, x0, #0xc, #0x14
    // 0x745fc0: mov             x1, x2
    // 0x745fc4: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x745fc4: movz            x17, #0x384d
    //     0x745fc8: movk            x17, #0x1, lsl #16
    //     0x745fcc: add             lr, x0, x17
    //     0x745fd0: ldr             lr, [x21, lr, lsl #3]
    //     0x745fd4: blr             lr
    // 0x745fd8: stur            x0, [fp, #-0x18]
    // 0x745fdc: LoadField: r1 = r0->field_7
    //     0x745fdc: ldur            w1, [x0, #7]
    // 0x745fe0: DecompressPointer r1
    //     0x745fe0: add             x1, x1, HEAP, lsl #32
    // 0x745fe4: LoadField: d2 = r1->field_f
    //     0x745fe4: ldur            d2, [x1, #0xf]
    // 0x745fe8: stur            d2, [fp, #-0x88]
    // 0x745fec: LoadField: r2 = r0->field_b
    //     0x745fec: ldur            x2, [x0, #0xb]
    // 0x745ff0: LoadField: d0 = r1->field_7
    //     0x745ff0: ldur            d0, [x1, #7]
    // 0x745ff4: ldur            d3, [fp, #-0x80]
    // 0x745ff8: fsub            d1, d3, d0
    // 0x745ffc: d4 = 0.000000
    //     0x745ffc: eor             v4.16b, v4.16b, v4.16b
    // 0x746000: fcmp            d4, d1
    // 0x746004: b.le            #0x746010
    // 0x746008: d0 = 0.000000
    //     0x746008: eor             v0.16b, v0.16b, v0.16b
    // 0x74600c: b               #0x746044
    // 0x746010: fcmp            d1, d4
    // 0x746014: b.le            #0x746020
    // 0x746018: mov             v0.16b, v1.16b
    // 0x74601c: b               #0x746044
    // 0x746020: fcmp            d4, d4
    // 0x746024: b.ne            #0x746030
    // 0x746028: fadd            d0, d1, d4
    // 0x74602c: b               #0x746044
    // 0x746030: fcmp            d1, d1
    // 0x746034: b.vc            #0x746040
    // 0x746038: mov             v0.16b, v1.16b
    // 0x74603c: b               #0x746044
    // 0x746040: d0 = 0.000000
    //     0x746040: eor             v0.16b, v0.16b, v0.16b
    // 0x746044: ldur            d1, [fp, #-0x68]
    // 0x746048: ldur            x3, [fp, #-0x38]
    // 0x74604c: r1 = Instance_WrapAlignment
    //     0x74604c: add             x1, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0x746050: ldr             x1, [x1, #0x610]
    // 0x746054: r0 = _distributeSpace()
    //     0x746054: bl              #0x7461f8  ; [package:flutter/src/rendering/wrap.dart] WrapAlignment::_distributeSpace
    // 0x746058: mov             x2, x0
    // 0x74605c: mov             x3, x1
    // 0x746060: ldur            x0, [fp, #-0x18]
    // 0x746064: LoadField: r1 = r0->field_b
    //     0x746064: ldur            x1, [x0, #0xb]
    // 0x746068: LoadField: r4 = r0->field_13
    //     0x746068: ldur            w4, [x0, #0x13]
    // 0x74606c: DecompressPointer r4
    //     0x74606c: add             x4, x4, HEAP, lsl #32
    // 0x746070: LoadField: d0 = r2->field_7
    //     0x746070: ldur            d0, [x2, #7]
    // 0x746074: LoadField: d1 = r3->field_7
    //     0x746074: ldur            d1, [x3, #7]
    // 0x746078: stur            d1, [fp, #-0x98]
    // 0x74607c: mov             v3.16b, v0.16b
    // 0x746080: mov             x3, x1
    // 0x746084: mov             x2, x4
    // 0x746088: ldur            d2, [fp, #-0x70]
    // 0x74608c: ldur            x1, [fp, #-0x58]
    // 0x746090: ldur            d0, [fp, #-0x88]
    // 0x746094: stur            x3, [fp, #-0x60]
    // 0x746098: stur            x2, [fp, #-0x18]
    // 0x74609c: stur            d3, [fp, #-0x90]
    // 0x7460a0: CheckStackOverflow
    //     0x7460a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7460a4: cmp             SP, x16
    //     0x7460a8: b.ls            #0x7461f0
    // 0x7460ac: cmp             w2, NULL
    // 0x7460b0: b.eq            #0x7461ac
    // 0x7460b4: cmp             x3, #0
    // 0x7460b8: b.le            #0x7461ac
    // 0x7460bc: ldur            x16, [fp, #-0x30]
    // 0x7460c0: stp             x2, x16, [SP]
    // 0x7460c4: ldur            x0, [fp, #-0x30]
    // 0x7460c8: ClosureCall
    //     0x7460c8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x7460cc: ldur            x2, [x0, #0x1f]
    //     0x7460d0: blr             x2
    // 0x7460d4: LoadField: d0 = r0->field_7
    //     0x7460d4: ldur            d0, [x0, #7]
    // 0x7460d8: stur            d0, [fp, #-0xa8]
    // 0x7460dc: LoadField: d1 = r0->field_f
    //     0x7460dc: ldur            d1, [x0, #0xf]
    // 0x7460e0: ldur            x0, [fp, #-0x58]
    // 0x7460e4: cmp             x0, #1
    // 0x7460e8: b.gt            #0x746104
    // 0x7460ec: cmp             x0, #0
    // 0x7460f0: b.gt            #0x7460fc
    // 0x7460f4: d6 = 0.000000
    //     0x7460f4: eor             v6.16b, v6.16b, v6.16b
    // 0x7460f8: b               #0x746108
    // 0x7460fc: d6 = 1.000000
    //     0x7460fc: fmov            d6, #1.00000000
    // 0x746100: b               #0x746108
    // 0x746104: d6 = 0.500000
    //     0x746104: fmov            d6, #0.50000000
    // 0x746108: ldur            d4, [fp, #-0x70]
    // 0x74610c: ldur            d5, [fp, #-0x90]
    // 0x746110: ldur            x1, [fp, #-0x60]
    // 0x746114: ldur            d3, [fp, #-0x88]
    // 0x746118: ldur            d2, [fp, #-0x98]
    // 0x74611c: fsub            d7, d3, d1
    // 0x746120: fmul            d1, d6, d7
    // 0x746124: fadd            d6, d4, d1
    // 0x746128: stur            d6, [fp, #-0xa0]
    // 0x74612c: r0 = Offset()
    //     0x74612c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x746130: ldur            d0, [fp, #-0x90]
    // 0x746134: StoreField: r0->field_7 = d0
    //     0x746134: stur            d0, [x0, #7]
    // 0x746138: ldur            d1, [fp, #-0xa0]
    // 0x74613c: StoreField: r0->field_f = d1
    //     0x74613c: stur            d1, [x0, #0xf]
    // 0x746140: ldur            x16, [fp, #-0x28]
    // 0x746144: stp             x0, x16, [SP, #8]
    // 0x746148: ldur            x16, [fp, #-0x18]
    // 0x74614c: str             x16, [SP]
    // 0x746150: ldur            x0, [fp, #-0x28]
    // 0x746154: ClosureCall
    //     0x746154: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x746158: ldur            x2, [x0, #0x1f]
    //     0x74615c: blr             x2
    // 0x746160: ldur            d0, [fp, #-0xa8]
    // 0x746164: ldur            d1, [fp, #-0x98]
    // 0x746168: fadd            d2, d0, d1
    // 0x74616c: ldur            d0, [fp, #-0x90]
    // 0x746170: fadd            d3, d0, d2
    // 0x746174: stur            d3, [fp, #-0xa0]
    // 0x746178: ldur            x16, [fp, #-8]
    // 0x74617c: ldur            lr, [fp, #-0x18]
    // 0x746180: stp             lr, x16, [SP]
    // 0x746184: ldur            x0, [fp, #-8]
    // 0x746188: ClosureCall
    //     0x746188: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x74618c: ldur            x2, [x0, #0x1f]
    //     0x746190: blr             x2
    // 0x746194: ldur            x1, [fp, #-0x60]
    // 0x746198: sub             x3, x1, #1
    // 0x74619c: ldur            d3, [fp, #-0xa0]
    // 0x7461a0: mov             x2, x0
    // 0x7461a4: ldur            d1, [fp, #-0x98]
    // 0x7461a8: b               #0x746088
    // 0x7461ac: ldur            d1, [fp, #-0x70]
    // 0x7461b0: ldur            d0, [fp, #-0x88]
    // 0x7461b4: ldur            d2, [fp, #-0x78]
    // 0x7461b8: fadd            d3, d0, d2
    // 0x7461bc: fadd            d0, d1, d3
    // 0x7461c0: ldur            x2, [fp, #-0x10]
    // 0x7461c4: ldur            x3, [fp, #-0x58]
    // 0x7461c8: ldur            d1, [fp, #-0x80]
    // 0x7461cc: b               #0x745f7c
    // 0x7461d0: r0 = Null
    //     0x7461d0: mov             x0, NULL
    // 0x7461d4: LeaveFrame
    //     0x7461d4: mov             SP, fp
    //     0x7461d8: ldp             fp, lr, [SP], #0x10
    // 0x7461dc: ret
    //     0x7461dc: ret             
    // 0x7461e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7461e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7461e4: b               #0x745da0
    // 0x7461e8: r0 = StackOverflowSharedWithFPURegs()
    //     0x7461e8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7461ec: b               #0x745f8c
    // 0x7461f0: r0 = StackOverflowSharedWithFPURegs()
    //     0x7461f0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7461f4: b               #0x7460ac
  }
  get _ runSpacing(/* No info */) {
    // ** addr: 0x7465b4, size: 0x8
    // 0x7465b4: LoadField: d0 = r1->field_7b
    //     0x7465b4: ldur            d0, [x1, #0x7b]
    // 0x7465b8: ret
    //     0x7465b8: ret             
  }
  get _ runAlignment(/* No info */) {
    // ** addr: 0x7465bc, size: 0xc
    // 0x7465bc: r0 = Instance_WrapAlignment
    //     0x7465bc: add             x0, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0x7465c0: ldr             x0, [x0, #0x610]
    // 0x7465c4: ret
    //     0x7465c4: ret             
  }
  get _ crossAxisAlignment(/* No info */) {
    // ** addr: 0x746600, size: 0xc
    // 0x746600: r0 = Instance_WrapCrossAlignment
    //     0x746600: add             x0, PP, #0x27, lsl #12  ; [pp+0x27618] Obj!WrapCrossAlignment@e35201
    //     0x746604: ldr             x0, [x0, #0x618]
    // 0x746608: ret
    //     0x746608: ret             
  }
  get _ _areAxesFlipped(/* No info */) {
    // ** addr: 0x74660c, size: 0x34
    // 0x74660c: LoadField: r2 = r1->field_87
    //     0x74660c: ldur            w2, [x1, #0x87]
    // 0x746610: DecompressPointer r2
    //     0x746610: add             x2, x2, HEAP, lsl #32
    // 0x746614: cmp             w2, NULL
    // 0x746618: b.ne            #0x746620
    // 0x74661c: r2 = Instance_TextDirection
    //     0x74661c: ldr             x2, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0x746620: LoadField: r3 = r2->field_7
    //     0x746620: ldur            x3, [x2, #7]
    // 0x746624: cmp             x3, #0
    // 0x746628: b.gt            #0x746634
    // 0x74662c: r0 = true
    //     0x74662c: add             x0, NULL, #0x20  ; true
    // 0x746630: b               #0x746638
    // 0x746634: r0 = false
    //     0x746634: add             x0, NULL, #0x30  ; false
    // 0x746638: r1 = false
    //     0x746638: add             x1, NULL, #0x30  ; false
    // 0x74663c: ret
    //     0x74663c: ret             
  }
  _ _computeRuns(/* No info */) {
    // ** addr: 0x746720, size: 0x394
    // 0x746720: EnterFrame
    //     0x746720: stp             fp, lr, [SP, #-0x10]!
    //     0x746724: mov             fp, SP
    // 0x746728: AllocStack(0x98)
    //     0x746728: sub             SP, SP, #0x98
    // 0x74672c: SetupParameters(RenderWrap this /* r1 => r1, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0x74672c: mov             x0, x3
    //     0x746730: stur            x1, [fp, #-8]
    //     0x746734: stur            x3, [fp, #-0x10]
    // 0x746738: CheckStackOverflow
    //     0x746738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74673c: cmp             SP, x16
    //     0x746740: b.ls            #0x746a9c
    // 0x746744: LoadField: d1 = r2->field_f
    //     0x746744: ldur            d1, [x2, #0xf]
    // 0x746748: stur            d1, [fp, #-0x68]
    // 0x74674c: r0 = BoxConstraints()
    //     0x74674c: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x746750: stur            x0, [fp, #-0x18]
    // 0x746754: StoreField: r0->field_7 = rZR
    //     0x746754: stur            xzr, [x0, #7]
    // 0x746758: ldur            d1, [fp, #-0x68]
    // 0x74675c: StoreField: r0->field_f = d1
    //     0x74675c: stur            d1, [x0, #0xf]
    // 0x746760: ArrayStore: r0[0] = rZR  ; List_8
    //     0x746760: stur            xzr, [x0, #0x17]
    // 0x746764: d0 = inf
    //     0x746764: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x746768: StoreField: r0->field_1f = d0
    //     0x746768: stur            d0, [x0, #0x1f]
    // 0x74676c: ldur            x1, [fp, #-8]
    // 0x746770: r0 = _areAxesFlipped()
    //     0x746770: bl              #0x74660c  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::_areAxesFlipped
    // 0x746774: mov             x5, x0
    // 0x746778: ldur            x0, [fp, #-8]
    // 0x74677c: stur            x5, [fp, #-0x20]
    // 0x746780: LoadField: d0 = r0->field_6f
    //     0x746780: ldur            d0, [x0, #0x6f]
    // 0x746784: stur            d0, [fp, #-0x70]
    // 0x746788: r1 = <_RunMetrics>
    //     0x746788: add             x1, PP, #0x44, lsl #12  ; [pp+0x44bd8] TypeArguments: <_RunMetrics>
    //     0x74678c: ldr             x1, [x1, #0xbd8]
    // 0x746790: r2 = 0
    //     0x746790: movz            x2, #0
    // 0x746794: r0 = _GrowableList()
    //     0x746794: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x746798: mov             x2, x0
    // 0x74679c: ldur            x1, [fp, #-8]
    // 0x7467a0: stur            x2, [fp, #-0x40]
    // 0x7467a4: LoadField: r0 = r1->field_5f
    //     0x7467a4: ldur            w0, [x1, #0x5f]
    // 0x7467a8: DecompressPointer r0
    //     0x7467a8: add             x0, x0, HEAP, lsl #32
    // 0x7467ac: mov             x3, x0
    // 0x7467b0: r5 = Null
    //     0x7467b0: mov             x5, NULL
    // 0x7467b4: r4 = Instance_Size
    //     0x7467b4: add             x4, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!Size@e2c001
    //     0x7467b8: ldr             x4, [x4, #0xa20]
    // 0x7467bc: stur            x5, [fp, #-0x28]
    // 0x7467c0: stur            x4, [fp, #-0x30]
    // 0x7467c4: stur            x3, [fp, #-0x38]
    // 0x7467c8: CheckStackOverflow
    //     0x7467c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7467cc: cmp             SP, x16
    //     0x7467d0: b.ls            #0x746aa4
    // 0x7467d4: cmp             w3, NULL
    // 0x7467d8: b.eq            #0x746a1c
    // 0x7467dc: ldur            x16, [fp, #-0x10]
    // 0x7467e0: stp             x3, x16, [SP, #8]
    // 0x7467e4: ldur            x16, [fp, #-0x18]
    // 0x7467e8: str             x16, [SP]
    // 0x7467ec: ldur            x0, [fp, #-0x10]
    // 0x7467f0: ClosureCall
    //     0x7467f0: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x7467f4: ldur            x2, [x0, #0x1f]
    //     0x7467f8: blr             x2
    // 0x7467fc: ldur            x1, [fp, #-0x28]
    // 0x746800: stur            x0, [fp, #-0x48]
    // 0x746804: cmp             w1, NULL
    // 0x746808: b.ne            #0x746838
    // 0x74680c: ldur            x2, [fp, #-0x38]
    // 0x746810: r0 = _RunMetrics()
    //     0x746810: bl              #0x746c48  ; Allocate_RunMetricsStub -> _RunMetrics (size=0x18)
    // 0x746814: mov             x1, x0
    // 0x746818: r0 = 1
    //     0x746818: movz            x0, #0x1
    // 0x74681c: StoreField: r1->field_b = r0
    //     0x74681c: stur            x0, [x1, #0xb]
    // 0x746820: ldur            x4, [fp, #-0x38]
    // 0x746824: StoreField: r1->field_13 = r4
    //     0x746824: stur            w4, [x1, #0x13]
    // 0x746828: ldur            x3, [fp, #-0x48]
    // 0x74682c: StoreField: r1->field_7 = r3
    //     0x74682c: stur            w3, [x1, #7]
    // 0x746830: mov             x0, x1
    // 0x746834: b               #0x74685c
    // 0x746838: ldur            x4, [fp, #-0x38]
    // 0x74683c: mov             x3, x0
    // 0x746840: r0 = 1
    //     0x746840: movz            x0, #0x1
    // 0x746844: ldur            x1, [fp, #-0x28]
    // 0x746848: mov             x2, x4
    // 0x74684c: ldur            x5, [fp, #-0x20]
    // 0x746850: ldur            d0, [fp, #-0x70]
    // 0x746854: ldur            d1, [fp, #-0x68]
    // 0x746858: r0 = tryAddingNewChild()
    //     0x746858: bl              #0x746b0c  ; [package:flutter/src/rendering/wrap.dart] _RunMetrics::tryAddingNewChild
    // 0x74685c: stur            x0, [fp, #-0x48]
    // 0x746860: cmp             w0, NULL
    // 0x746864: b.eq            #0x7469a0
    // 0x746868: ldur            x2, [fp, #-0x40]
    // 0x74686c: LoadField: r1 = r2->field_b
    //     0x74686c: ldur            w1, [x2, #0xb]
    // 0x746870: LoadField: r3 = r2->field_f
    //     0x746870: ldur            w3, [x2, #0xf]
    // 0x746874: DecompressPointer r3
    //     0x746874: add             x3, x3, HEAP, lsl #32
    // 0x746878: LoadField: r4 = r3->field_b
    //     0x746878: ldur            w4, [x3, #0xb]
    // 0x74687c: r3 = LoadInt32Instr(r1)
    //     0x74687c: sbfx            x3, x1, #1, #0x1f
    // 0x746880: stur            x3, [fp, #-0x50]
    // 0x746884: r1 = LoadInt32Instr(r4)
    //     0x746884: sbfx            x1, x4, #1, #0x1f
    // 0x746888: cmp             x3, x1
    // 0x74688c: b.ne            #0x746898
    // 0x746890: mov             x1, x2
    // 0x746894: r0 = _growToNextCapacity()
    //     0x746894: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x746898: ldur            x2, [fp, #-0x40]
    // 0x74689c: ldur            x5, [fp, #-0x28]
    // 0x7468a0: ldur            x3, [fp, #-0x50]
    // 0x7468a4: add             x0, x3, #1
    // 0x7468a8: lsl             x1, x0, #1
    // 0x7468ac: StoreField: r2->field_b = r1
    //     0x7468ac: stur            w1, [x2, #0xb]
    // 0x7468b0: LoadField: r1 = r2->field_f
    //     0x7468b0: ldur            w1, [x2, #0xf]
    // 0x7468b4: DecompressPointer r1
    //     0x7468b4: add             x1, x1, HEAP, lsl #32
    // 0x7468b8: ldur            x0, [fp, #-0x48]
    // 0x7468bc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x7468bc: add             x25, x1, x3, lsl #2
    //     0x7468c0: add             x25, x25, #0xf
    //     0x7468c4: str             w0, [x25]
    //     0x7468c8: tbz             w0, #0, #0x7468e4
    //     0x7468cc: ldurb           w16, [x1, #-1]
    //     0x7468d0: ldurb           w17, [x0, #-1]
    //     0x7468d4: and             x16, x17, x16, lsr #2
    //     0x7468d8: tst             x16, HEAP, lsr #32
    //     0x7468dc: b.eq            #0x7468e4
    //     0x7468e0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7468e4: cmp             w5, NULL
    // 0x7468e8: b.ne            #0x7468f4
    // 0x7468ec: r0 = Null
    //     0x7468ec: mov             x0, NULL
    // 0x7468f0: b               #0x746900
    // 0x7468f4: LoadField: r1 = r5->field_7
    //     0x7468f4: ldur            w1, [x5, #7]
    // 0x7468f8: DecompressPointer r1
    //     0x7468f8: add             x1, x1, HEAP, lsl #32
    // 0x7468fc: r0 = flipped()
    //     0x7468fc: bl              #0x732a78  ; [dart:ui] Size::flipped
    // 0x746900: cmp             w0, NULL
    // 0x746904: b.ne            #0x746914
    // 0x746908: r1 = Instance_Size
    //     0x746908: add             x1, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!Size@e2c001
    //     0x74690c: ldr             x1, [x1, #0xa20]
    // 0x746910: b               #0x746918
    // 0x746914: mov             x1, x0
    // 0x746918: ldur            x0, [fp, #-0x30]
    // 0x74691c: LoadField: d0 = r0->field_7
    //     0x74691c: ldur            d0, [x0, #7]
    // 0x746920: LoadField: d1 = r1->field_7
    //     0x746920: ldur            d1, [x1, #7]
    // 0x746924: fadd            d2, d0, d1
    // 0x746928: stur            d2, [fp, #-0x80]
    // 0x74692c: LoadField: d0 = r0->field_f
    //     0x74692c: ldur            d0, [x0, #0xf]
    // 0x746930: LoadField: d1 = r1->field_f
    //     0x746930: ldur            d1, [x1, #0xf]
    // 0x746934: fcmp            d0, d1
    // 0x746938: b.le            #0x746944
    // 0x74693c: d3 = 0.000000
    //     0x74693c: eor             v3.16b, v3.16b, v3.16b
    // 0x746940: b               #0x74697c
    // 0x746944: fcmp            d1, d0
    // 0x746948: b.le            #0x746958
    // 0x74694c: mov             v0.16b, v1.16b
    // 0x746950: d3 = 0.000000
    //     0x746950: eor             v3.16b, v3.16b, v3.16b
    // 0x746954: b               #0x74697c
    // 0x746958: d3 = 0.000000
    //     0x746958: eor             v3.16b, v3.16b, v3.16b
    // 0x74695c: fcmp            d0, d3
    // 0x746960: b.ne            #0x746970
    // 0x746964: fadd            d4, d0, d1
    // 0x746968: mov             v0.16b, v4.16b
    // 0x74696c: b               #0x74697c
    // 0x746970: fcmp            d1, d1
    // 0x746974: b.vc            #0x74697c
    // 0x746978: mov             v0.16b, v1.16b
    // 0x74697c: stur            d0, [fp, #-0x78]
    // 0x746980: r0 = Size()
    //     0x746980: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x746984: ldur            d0, [fp, #-0x80]
    // 0x746988: StoreField: r0->field_7 = d0
    //     0x746988: stur            d0, [x0, #7]
    // 0x74698c: ldur            d0, [fp, #-0x78]
    // 0x746990: StoreField: r0->field_f = d0
    //     0x746990: stur            d0, [x0, #0xf]
    // 0x746994: ldur            x5, [fp, #-0x48]
    // 0x746998: mov             x4, x0
    // 0x74699c: b               #0x7469ac
    // 0x7469a0: ldur            x5, [fp, #-0x28]
    // 0x7469a4: ldur            x0, [fp, #-0x30]
    // 0x7469a8: mov             x4, x0
    // 0x7469ac: ldur            x0, [fp, #-0x38]
    // 0x7469b0: stur            x5, [fp, #-0x58]
    // 0x7469b4: stur            x4, [fp, #-0x60]
    // 0x7469b8: LoadField: r3 = r0->field_7
    //     0x7469b8: ldur            w3, [x0, #7]
    // 0x7469bc: DecompressPointer r3
    //     0x7469bc: add             x3, x3, HEAP, lsl #32
    // 0x7469c0: stur            x3, [fp, #-0x48]
    // 0x7469c4: cmp             w3, NULL
    // 0x7469c8: b.eq            #0x746aac
    // 0x7469cc: mov             x0, x3
    // 0x7469d0: r2 = Null
    //     0x7469d0: mov             x2, NULL
    // 0x7469d4: r1 = Null
    //     0x7469d4: mov             x1, NULL
    // 0x7469d8: r4 = LoadClassIdInstr(r0)
    //     0x7469d8: ldur            x4, [x0, #-1]
    //     0x7469dc: ubfx            x4, x4, #0xc, #0x14
    // 0x7469e0: cmp             x4, #0xc79
    // 0x7469e4: b.eq            #0x7469fc
    // 0x7469e8: r8 = WrapParentData
    //     0x7469e8: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x7469ec: ldr             x8, [x8, #0xaf0]
    // 0x7469f0: r3 = Null
    //     0x7469f0: add             x3, PP, #0x44, lsl #12  ; [pp+0x44be0] Null
    //     0x7469f4: ldr             x3, [x3, #0xbe0]
    // 0x7469f8: r0 = DefaultTypeTest()
    //     0x7469f8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7469fc: ldur            x0, [fp, #-0x48]
    // 0x746a00: LoadField: r3 = r0->field_13
    //     0x746a00: ldur            w3, [x0, #0x13]
    // 0x746a04: DecompressPointer r3
    //     0x746a04: add             x3, x3, HEAP, lsl #32
    // 0x746a08: ldur            x5, [fp, #-0x58]
    // 0x746a0c: ldur            x4, [fp, #-0x60]
    // 0x746a10: ldur            x1, [fp, #-8]
    // 0x746a14: ldur            x2, [fp, #-0x40]
    // 0x746a18: b               #0x7467bc
    // 0x746a1c: mov             x16, x2
    // 0x746a20: mov             x2, x1
    // 0x746a24: mov             x1, x16
    // 0x746a28: mov             x0, x4
    // 0x746a2c: LoadField: d0 = r2->field_7b
    //     0x746a2c: ldur            d0, [x2, #0x7b]
    // 0x746a30: LoadField: r2 = r1->field_b
    //     0x746a30: ldur            w2, [x1, #0xb]
    // 0x746a34: r3 = LoadInt32Instr(r2)
    //     0x746a34: sbfx            x3, x2, #1, #0x1f
    // 0x746a38: sub             x2, x3, #1
    // 0x746a3c: scvtf           d1, x2
    // 0x746a40: fmul            d2, d0, d1
    // 0x746a44: mov             v0.16b, v2.16b
    // 0x746a48: r0 = _AxisSize.()
    //     0x746a48: bl              #0x746ae0  ; [package:flutter/src/rendering/wrap.dart] ::_AxisSize.
    // 0x746a4c: mov             x2, x0
    // 0x746a50: ldur            x0, [fp, #-0x28]
    // 0x746a54: stur            x2, [fp, #-8]
    // 0x746a58: cmp             w0, NULL
    // 0x746a5c: b.eq            #0x746ab0
    // 0x746a60: LoadField: r1 = r0->field_7
    //     0x746a60: ldur            w1, [x0, #7]
    // 0x746a64: DecompressPointer r1
    //     0x746a64: add             x1, x1, HEAP, lsl #32
    // 0x746a68: r0 = _AxisSize.flipped()
    //     0x746a68: bl              #0x746ab4  ; [package:flutter/src/rendering/wrap.dart] ::_AxisSize.flipped
    // 0x746a6c: ldur            x1, [fp, #-8]
    // 0x746a70: mov             x2, x0
    // 0x746a74: r0 = _AxisSize.+()
    //     0x746a74: bl              #0x7323a8  ; [package:flutter/src/rendering/flex.dart] ::_AxisSize.+
    // 0x746a78: ldur            x1, [fp, #-0x30]
    // 0x746a7c: mov             x2, x0
    // 0x746a80: r0 = _AxisSize.+()
    //     0x746a80: bl              #0x7323a8  ; [package:flutter/src/rendering/flex.dart] ::_AxisSize.+
    // 0x746a84: mov             x1, x0
    // 0x746a88: r0 = _AxisSize.flipped()
    //     0x746a88: bl              #0x746ab4  ; [package:flutter/src/rendering/wrap.dart] ::_AxisSize.flipped
    // 0x746a8c: ldur            x1, [fp, #-0x40]
    // 0x746a90: LeaveFrame
    //     0x746a90: mov             SP, fp
    //     0x746a94: ldp             fp, lr, [SP], #0x10
    // 0x746a98: ret
    //     0x746a98: ret             
    // 0x746a9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x746a9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x746aa0: b               #0x746744
    // 0x746aa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x746aa4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x746aa8: b               #0x7467d4
    // 0x746aac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x746aac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x746ab0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x746ab0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Size getChildSize(dynamic, RenderBox) {
    // ** addr: 0x746c54, size: 0x44
    // 0x746c54: EnterFrame
    //     0x746c54: stp             fp, lr, [SP, #-0x10]!
    //     0x746c58: mov             fp, SP
    // 0x746c5c: ldr             x0, [fp, #0x18]
    // 0x746c60: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x746c60: ldur            w1, [x0, #0x17]
    // 0x746c64: DecompressPointer r1
    //     0x746c64: add             x1, x1, HEAP, lsl #32
    // 0x746c68: CheckStackOverflow
    //     0x746c68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x746c6c: cmp             SP, x16
    //     0x746c70: b.ls            #0x746c90
    // 0x746c74: LoadField: r2 = r1->field_13
    //     0x746c74: ldur            w2, [x1, #0x13]
    // 0x746c78: DecompressPointer r2
    //     0x746c78: add             x2, x2, HEAP, lsl #32
    // 0x746c7c: ldr             x1, [fp, #0x10]
    // 0x746c80: r0 = getDryLayout()
    //     0x746c80: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x746c84: LeaveFrame
    //     0x746c84: mov             SP, fp
    //     0x746c88: ldp             fp, lr, [SP], #0x10
    // 0x746c8c: ret
    //     0x746c8c: ret             
    // 0x746c90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x746c90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x746c94: b               #0x746c74
  }
  [closure] void findHighestBaseline(dynamic, Offset, RenderBox) {
    // ** addr: 0x746c98, size: 0xa0
    // 0x746c98: EnterFrame
    //     0x746c98: stp             fp, lr, [SP, #-0x10]!
    //     0x746c9c: mov             fp, SP
    // 0x746ca0: AllocStack(0x10)
    //     0x746ca0: sub             SP, SP, #0x10
    // 0x746ca4: SetupParameters()
    //     0x746ca4: ldr             x0, [fp, #0x20]
    //     0x746ca8: ldur            w4, [x0, #0x17]
    //     0x746cac: add             x4, x4, HEAP, lsl #32
    //     0x746cb0: stur            x4, [fp, #-0x10]
    // 0x746cb4: CheckStackOverflow
    //     0x746cb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x746cb8: cmp             SP, x16
    //     0x746cbc: b.ls            #0x746d30
    // 0x746cc0: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x746cc0: ldur            w0, [x4, #0x17]
    // 0x746cc4: DecompressPointer r0
    //     0x746cc4: add             x0, x0, HEAP, lsl #32
    // 0x746cc8: stur            x0, [fp, #-8]
    // 0x746ccc: LoadField: r2 = r4->field_13
    //     0x746ccc: ldur            w2, [x4, #0x13]
    // 0x746cd0: DecompressPointer r2
    //     0x746cd0: add             x2, x2, HEAP, lsl #32
    // 0x746cd4: LoadField: r3 = r4->field_f
    //     0x746cd4: ldur            w3, [x4, #0xf]
    // 0x746cd8: DecompressPointer r3
    //     0x746cd8: add             x3, x3, HEAP, lsl #32
    // 0x746cdc: ldr             x1, [fp, #0x10]
    // 0x746ce0: r0 = getDryBaseline()
    //     0x746ce0: bl              #0x730f54  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryBaseline
    // 0x746ce4: mov             x1, x0
    // 0x746ce8: ldr             x0, [fp, #0x18]
    // 0x746cec: LoadField: d0 = r0->field_f
    //     0x746cec: ldur            d0, [x0, #0xf]
    // 0x746cf0: r0 = BaselineOffset.+()
    //     0x746cf0: bl              #0x73d964  ; [package:flutter/src/rendering/box.dart] ::BaselineOffset.+
    // 0x746cf4: ldur            x1, [fp, #-8]
    // 0x746cf8: mov             x2, x0
    // 0x746cfc: r0 = BaselineOffset.minOf()
    //     0x746cfc: bl              #0x746d38  ; [package:flutter/src/rendering/box.dart] ::BaselineOffset.minOf
    // 0x746d00: ldur            x1, [fp, #-0x10]
    // 0x746d04: ArrayStore: r1[0] = r0  ; List_4
    //     0x746d04: stur            w0, [x1, #0x17]
    //     0x746d08: ldurb           w16, [x1, #-1]
    //     0x746d0c: ldurb           w17, [x0, #-1]
    //     0x746d10: and             x16, x17, x16, lsr #2
    //     0x746d14: tst             x16, HEAP, lsr #32
    //     0x746d18: b.eq            #0x746d20
    //     0x746d1c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x746d20: r0 = Null
    //     0x746d20: mov             x0, NULL
    // 0x746d24: LeaveFrame
    //     0x746d24: mov             SP, fp
    //     0x746d28: ldp             fp, lr, [SP], #0x10
    // 0x746d2c: ret
    //     0x746d2c: ret             
    // 0x746d30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x746d30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x746d34: b               #0x746cc0
  }
  dynamic computeMinIntrinsicHeight(dynamic) {
    // ** addr: 0x74a61c, size: 0x24
    // 0x74a61c: EnterFrame
    //     0x74a61c: stp             fp, lr, [SP, #-0x10]!
    //     0x74a620: mov             fp, SP
    // 0x74a624: ldr             x2, [fp, #0x10]
    // 0x74a628: r1 = Function 'computeMinIntrinsicHeight':.
    //     0x74a628: add             x1, PP, #0x55, lsl #12  ; [pp+0x552a8] AnonymousClosure: (0x74a640), in [package:flutter/src/rendering/wrap.dart] RenderWrap::computeMinIntrinsicHeight (0x74a6b4)
    //     0x74a62c: ldr             x1, [x1, #0x2a8]
    // 0x74a630: r0 = AllocateClosure()
    //     0x74a630: bl              #0xec1630  ; AllocateClosureStub
    // 0x74a634: LeaveFrame
    //     0x74a634: mov             SP, fp
    //     0x74a638: ldp             fp, lr, [SP], #0x10
    // 0x74a63c: ret
    //     0x74a63c: ret             
  }
  [closure] double computeMinIntrinsicHeight(dynamic, double) {
    // ** addr: 0x74a640, size: 0x74
    // 0x74a640: EnterFrame
    //     0x74a640: stp             fp, lr, [SP, #-0x10]!
    //     0x74a644: mov             fp, SP
    // 0x74a648: ldr             x0, [fp, #0x18]
    // 0x74a64c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x74a64c: ldur            w1, [x0, #0x17]
    // 0x74a650: DecompressPointer r1
    //     0x74a650: add             x1, x1, HEAP, lsl #32
    // 0x74a654: CheckStackOverflow
    //     0x74a654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74a658: cmp             SP, x16
    //     0x74a65c: b.ls            #0x74a69c
    // 0x74a660: ldr             x2, [fp, #0x10]
    // 0x74a664: r0 = computeMinIntrinsicHeight()
    //     0x74a664: bl              #0x74a6b4  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::computeMinIntrinsicHeight
    // 0x74a668: r0 = inline_Allocate_Double()
    //     0x74a668: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74a66c: add             x0, x0, #0x10
    //     0x74a670: cmp             x1, x0
    //     0x74a674: b.ls            #0x74a6a4
    //     0x74a678: str             x0, [THR, #0x50]  ; THR::top
    //     0x74a67c: sub             x0, x0, #0xf
    //     0x74a680: movz            x1, #0xe15c
    //     0x74a684: movk            x1, #0x3, lsl #16
    //     0x74a688: stur            x1, [x0, #-1]
    // 0x74a68c: StoreField: r0->field_7 = d0
    //     0x74a68c: stur            d0, [x0, #7]
    // 0x74a690: LeaveFrame
    //     0x74a690: mov             SP, fp
    //     0x74a694: ldp             fp, lr, [SP], #0x10
    // 0x74a698: ret
    //     0x74a698: ret             
    // 0x74a69c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74a69c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74a6a0: b               #0x74a660
    // 0x74a6a4: SaveReg d0
    //     0x74a6a4: str             q0, [SP, #-0x10]!
    // 0x74a6a8: r0 = AllocateDouble()
    //     0x74a6a8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74a6ac: RestoreReg d0
    //     0x74a6ac: ldr             q0, [SP], #0x10
    // 0x74a6b0: b               #0x74a68c
  }
  _ computeMinIntrinsicHeight(/* No info */) {
    // ** addr: 0x74a6b4, size: 0x64
    // 0x74a6b4: EnterFrame
    //     0x74a6b4: stp             fp, lr, [SP, #-0x10]!
    //     0x74a6b8: mov             fp, SP
    // 0x74a6bc: AllocStack(0x10)
    //     0x74a6bc: sub             SP, SP, #0x10
    // 0x74a6c0: SetupParameters(RenderWrap this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x74a6c0: stur            x1, [fp, #-8]
    //     0x74a6c4: stur            x2, [fp, #-0x10]
    // 0x74a6c8: CheckStackOverflow
    //     0x74a6c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74a6cc: cmp             SP, x16
    //     0x74a6d0: b.ls            #0x74a710
    // 0x74a6d4: r0 = BoxConstraints()
    //     0x74a6d4: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x74a6d8: StoreField: r0->field_7 = rZR
    //     0x74a6d8: stur            xzr, [x0, #7]
    // 0x74a6dc: ldur            x1, [fp, #-0x10]
    // 0x74a6e0: LoadField: d0 = r1->field_7
    //     0x74a6e0: ldur            d0, [x1, #7]
    // 0x74a6e4: StoreField: r0->field_f = d0
    //     0x74a6e4: stur            d0, [x0, #0xf]
    // 0x74a6e8: ArrayStore: r0[0] = rZR  ; List_8
    //     0x74a6e8: stur            xzr, [x0, #0x17]
    // 0x74a6ec: d0 = inf
    //     0x74a6ec: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x74a6f0: StoreField: r0->field_1f = d0
    //     0x74a6f0: stur            d0, [x0, #0x1f]
    // 0x74a6f4: ldur            x1, [fp, #-8]
    // 0x74a6f8: mov             x2, x0
    // 0x74a6fc: r0 = getDryLayout()
    //     0x74a6fc: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x74a700: LoadField: d0 = r0->field_f
    //     0x74a700: ldur            d0, [x0, #0xf]
    // 0x74a704: LeaveFrame
    //     0x74a704: mov             SP, fp
    //     0x74a708: ldp             fp, lr, [SP], #0x10
    // 0x74a70c: ret
    //     0x74a70c: ret             
    // 0x74a710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74a710: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74a714: b               #0x74a6d4
  }
  _ computeDistanceToActualBaseline(/* No info */) {
    // ** addr: 0x74d058, size: 0x2c
    // 0x74d058: EnterFrame
    //     0x74d058: stp             fp, lr, [SP, #-0x10]!
    //     0x74d05c: mov             fp, SP
    // 0x74d060: CheckStackOverflow
    //     0x74d060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d064: cmp             SP, x16
    //     0x74d068: b.ls            #0x74d07c
    // 0x74d06c: r0 = defaultComputeDistanceToHighestActualBaseline()
    //     0x74d06c: bl              #0x74d084  ; [package:flutter/src/rendering/wrap.dart] _RenderWrap&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultComputeDistanceToHighestActualBaseline
    // 0x74d070: LeaveFrame
    //     0x74d070: mov             SP, fp
    //     0x74d074: ldp             fp, lr, [SP], #0x10
    // 0x74d078: ret
    //     0x74d078: ret             
    // 0x74d07c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d07c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d080: b               #0x74d06c
  }
  dynamic computeMaxIntrinsicWidth(dynamic) {
    // ** addr: 0x750440, size: 0x24
    // 0x750440: EnterFrame
    //     0x750440: stp             fp, lr, [SP, #-0x10]!
    //     0x750444: mov             fp, SP
    // 0x750448: ldr             x2, [fp, #0x10]
    // 0x75044c: r1 = Function 'computeMaxIntrinsicWidth':.
    //     0x75044c: add             x1, PP, #0x55, lsl #12  ; [pp+0x552b0] AnonymousClosure: (0x750464), in [package:flutter/src/rendering/wrap.dart] RenderWrap::computeMaxIntrinsicWidth (0x7504d8)
    //     0x750450: ldr             x1, [x1, #0x2b0]
    // 0x750454: r0 = AllocateClosure()
    //     0x750454: bl              #0xec1630  ; AllocateClosureStub
    // 0x750458: LeaveFrame
    //     0x750458: mov             SP, fp
    //     0x75045c: ldp             fp, lr, [SP], #0x10
    // 0x750460: ret
    //     0x750460: ret             
  }
  [closure] double computeMaxIntrinsicWidth(dynamic, double) {
    // ** addr: 0x750464, size: 0x74
    // 0x750464: EnterFrame
    //     0x750464: stp             fp, lr, [SP, #-0x10]!
    //     0x750468: mov             fp, SP
    // 0x75046c: ldr             x0, [fp, #0x18]
    // 0x750470: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x750470: ldur            w1, [x0, #0x17]
    // 0x750474: DecompressPointer r1
    //     0x750474: add             x1, x1, HEAP, lsl #32
    // 0x750478: CheckStackOverflow
    //     0x750478: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75047c: cmp             SP, x16
    //     0x750480: b.ls            #0x7504c0
    // 0x750484: ldr             x2, [fp, #0x10]
    // 0x750488: r0 = computeMaxIntrinsicWidth()
    //     0x750488: bl              #0x7504d8  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::computeMaxIntrinsicWidth
    // 0x75048c: r0 = inline_Allocate_Double()
    //     0x75048c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x750490: add             x0, x0, #0x10
    //     0x750494: cmp             x1, x0
    //     0x750498: b.ls            #0x7504c8
    //     0x75049c: str             x0, [THR, #0x50]  ; THR::top
    //     0x7504a0: sub             x0, x0, #0xf
    //     0x7504a4: movz            x1, #0xe15c
    //     0x7504a8: movk            x1, #0x3, lsl #16
    //     0x7504ac: stur            x1, [x0, #-1]
    // 0x7504b0: StoreField: r0->field_7 = d0
    //     0x7504b0: stur            d0, [x0, #7]
    // 0x7504b4: LeaveFrame
    //     0x7504b4: mov             SP, fp
    //     0x7504b8: ldp             fp, lr, [SP], #0x10
    // 0x7504bc: ret
    //     0x7504bc: ret             
    // 0x7504c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7504c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7504c4: b               #0x750484
    // 0x7504c8: SaveReg d0
    //     0x7504c8: str             q0, [SP, #-0x10]!
    // 0x7504cc: r0 = AllocateDouble()
    //     0x7504cc: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7504d0: RestoreReg d0
    //     0x7504d0: ldr             q0, [SP], #0x10
    // 0x7504d4: b               #0x7504b0
  }
  _ computeMaxIntrinsicWidth(/* No info */) {
    // ** addr: 0x7504d8, size: 0x124
    // 0x7504d8: EnterFrame
    //     0x7504d8: stp             fp, lr, [SP, #-0x10]!
    //     0x7504dc: mov             fp, SP
    // 0x7504e0: AllocStack(0x48)
    //     0x7504e0: sub             SP, SP, #0x48
    // 0x7504e4: CheckStackOverflow
    //     0x7504e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7504e8: cmp             SP, x16
    //     0x7504ec: b.ls            #0x7505e8
    // 0x7504f0: LoadField: r0 = r1->field_5f
    //     0x7504f0: ldur            w0, [x1, #0x5f]
    // 0x7504f4: DecompressPointer r0
    //     0x7504f4: add             x0, x0, HEAP, lsl #32
    // 0x7504f8: mov             x1, x0
    // 0x7504fc: d0 = 0.000000
    //     0x7504fc: eor             v0.16b, v0.16b, v0.16b
    // 0x750500: stur            x1, [fp, #-8]
    // 0x750504: stur            d0, [fp, #-0x18]
    // 0x750508: CheckStackOverflow
    //     0x750508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75050c: cmp             SP, x16
    //     0x750510: b.ls            #0x7505f0
    // 0x750514: cmp             w1, NULL
    // 0x750518: b.eq            #0x7505d4
    // 0x75051c: r0 = LoadClassIdInstr(r1)
    //     0x75051c: ldur            x0, [x1, #-1]
    //     0x750520: ubfx            x0, x0, #0xc, #0x14
    // 0x750524: str             x1, [SP]
    // 0x750528: r0 = GDT[cid_x0 + 0x11e6b]()
    //     0x750528: movz            x17, #0x1e6b
    //     0x75052c: movk            x17, #0x1, lsl #16
    //     0x750530: add             lr, x0, x17
    //     0x750534: ldr             lr, [x21, lr, lsl #3]
    //     0x750538: blr             lr
    // 0x75053c: r16 = <double, double>
    //     0x75053c: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c400] TypeArguments: <double, double>
    //     0x750540: ldr             x16, [x16, #0x400]
    // 0x750544: ldur            lr, [fp, #-8]
    // 0x750548: stp             lr, x16, [SP, #0x18]
    // 0x75054c: r16 = Instance__IntrinsicDimension
    //     0x75054c: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c4d8] Obj!_IntrinsicDimension@e35ba1
    //     0x750550: ldr             x16, [x16, #0x4d8]
    // 0x750554: r30 = inf
    //     0x750554: ldr             lr, [PP, #0x49e0]  ; [pp+0x49e0] inf
    // 0x750558: stp             lr, x16, [SP, #8]
    // 0x75055c: str             x0, [SP]
    // 0x750560: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x750560: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x750564: r0 = _computeIntrinsics()
    //     0x750564: bl              #0x72d344  ; [package:flutter/src/rendering/box.dart] RenderBox::_computeIntrinsics
    // 0x750568: LoadField: d0 = r0->field_7
    //     0x750568: ldur            d0, [x0, #7]
    // 0x75056c: ldur            d1, [fp, #-0x18]
    // 0x750570: fadd            d2, d1, d0
    // 0x750574: ldur            x0, [fp, #-8]
    // 0x750578: stur            d2, [fp, #-0x20]
    // 0x75057c: LoadField: r3 = r0->field_7
    //     0x75057c: ldur            w3, [x0, #7]
    // 0x750580: DecompressPointer r3
    //     0x750580: add             x3, x3, HEAP, lsl #32
    // 0x750584: stur            x3, [fp, #-0x10]
    // 0x750588: cmp             w3, NULL
    // 0x75058c: b.eq            #0x7505f8
    // 0x750590: mov             x0, x3
    // 0x750594: r2 = Null
    //     0x750594: mov             x2, NULL
    // 0x750598: r1 = Null
    //     0x750598: mov             x1, NULL
    // 0x75059c: r4 = LoadClassIdInstr(r0)
    //     0x75059c: ldur            x4, [x0, #-1]
    //     0x7505a0: ubfx            x4, x4, #0xc, #0x14
    // 0x7505a4: cmp             x4, #0xc79
    // 0x7505a8: b.eq            #0x7505c0
    // 0x7505ac: r8 = WrapParentData
    //     0x7505ac: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x7505b0: ldr             x8, [x8, #0xaf0]
    // 0x7505b4: r3 = Null
    //     0x7505b4: add             x3, PP, #0x55, lsl #12  ; [pp+0x552b8] Null
    //     0x7505b8: ldr             x3, [x3, #0x2b8]
    // 0x7505bc: r0 = DefaultTypeTest()
    //     0x7505bc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7505c0: ldur            x0, [fp, #-0x10]
    // 0x7505c4: LoadField: r1 = r0->field_13
    //     0x7505c4: ldur            w1, [x0, #0x13]
    // 0x7505c8: DecompressPointer r1
    //     0x7505c8: add             x1, x1, HEAP, lsl #32
    // 0x7505cc: ldur            d0, [fp, #-0x20]
    // 0x7505d0: b               #0x750500
    // 0x7505d4: mov             v1.16b, v0.16b
    // 0x7505d8: mov             v0.16b, v1.16b
    // 0x7505dc: LeaveFrame
    //     0x7505dc: mov             SP, fp
    //     0x7505e0: ldp             fp, lr, [SP], #0x10
    // 0x7505e4: ret
    //     0x7505e4: ret             
    // 0x7505e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7505e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7505ec: b               #0x7504f0
    // 0x7505f0: r0 = StackOverflowSharedWithFPURegs()
    //     0x7505f0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7505f4: b               #0x750514
    // 0x7505f8: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7505f8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  dynamic computeMaxIntrinsicHeight(dynamic) {
    // ** addr: 0x7535c0, size: 0x24
    // 0x7535c0: EnterFrame
    //     0x7535c0: stp             fp, lr, [SP, #-0x10]!
    //     0x7535c4: mov             fp, SP
    // 0x7535c8: ldr             x2, [fp, #0x10]
    // 0x7535cc: r1 = Function 'computeMaxIntrinsicHeight':.
    //     0x7535cc: add             x1, PP, #0x55, lsl #12  ; [pp+0x552a0] AnonymousClosure: (0x7535e4), in [package:flutter/src/rendering/wrap.dart] RenderWrap::computeMinIntrinsicHeight (0x74a6b4)
    //     0x7535d0: ldr             x1, [x1, #0x2a0]
    // 0x7535d4: r0 = AllocateClosure()
    //     0x7535d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7535d8: LeaveFrame
    //     0x7535d8: mov             SP, fp
    //     0x7535dc: ldp             fp, lr, [SP], #0x10
    // 0x7535e0: ret
    //     0x7535e0: ret             
  }
  [closure] double computeMaxIntrinsicHeight(dynamic, double) {
    // ** addr: 0x7535e4, size: 0x74
    // 0x7535e4: EnterFrame
    //     0x7535e4: stp             fp, lr, [SP, #-0x10]!
    //     0x7535e8: mov             fp, SP
    // 0x7535ec: ldr             x0, [fp, #0x18]
    // 0x7535f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7535f0: ldur            w1, [x0, #0x17]
    // 0x7535f4: DecompressPointer r1
    //     0x7535f4: add             x1, x1, HEAP, lsl #32
    // 0x7535f8: CheckStackOverflow
    //     0x7535f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7535fc: cmp             SP, x16
    //     0x753600: b.ls            #0x753640
    // 0x753604: ldr             x2, [fp, #0x10]
    // 0x753608: r0 = computeMinIntrinsicHeight()
    //     0x753608: bl              #0x74a6b4  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::computeMinIntrinsicHeight
    // 0x75360c: r0 = inline_Allocate_Double()
    //     0x75360c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x753610: add             x0, x0, #0x10
    //     0x753614: cmp             x1, x0
    //     0x753618: b.ls            #0x753648
    //     0x75361c: str             x0, [THR, #0x50]  ; THR::top
    //     0x753620: sub             x0, x0, #0xf
    //     0x753624: movz            x1, #0xe15c
    //     0x753628: movk            x1, #0x3, lsl #16
    //     0x75362c: stur            x1, [x0, #-1]
    // 0x753630: StoreField: r0->field_7 = d0
    //     0x753630: stur            d0, [x0, #7]
    // 0x753634: LeaveFrame
    //     0x753634: mov             SP, fp
    //     0x753638: ldp             fp, lr, [SP], #0x10
    // 0x75363c: ret
    //     0x75363c: ret             
    // 0x753640: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x753640: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x753644: b               #0x753604
    // 0x753648: SaveReg d0
    //     0x753648: str             q0, [SP, #-0x10]!
    // 0x75364c: r0 = AllocateDouble()
    //     0x75364c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x753650: RestoreReg d0
    //     0x753650: ldr             q0, [SP], #0x10
    // 0x753654: b               #0x753630
  }
  _ computeDryLayout(/* No info */) {
    // ** addr: 0x7569b8, size: 0x2c
    // 0x7569b8: EnterFrame
    //     0x7569b8: stp             fp, lr, [SP, #-0x10]!
    //     0x7569bc: mov             fp, SP
    // 0x7569c0: CheckStackOverflow
    //     0x7569c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7569c4: cmp             SP, x16
    //     0x7569c8: b.ls            #0x7569dc
    // 0x7569cc: r0 = _computeDryLayout()
    //     0x7569cc: bl              #0x7569e4  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::_computeDryLayout
    // 0x7569d0: LeaveFrame
    //     0x7569d0: mov             SP, fp
    //     0x7569d4: ldp             fp, lr, [SP], #0x10
    // 0x7569d8: ret
    //     0x7569d8: ret             
    // 0x7569dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7569dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7569e0: b               #0x7569cc
  }
  _ _computeDryLayout(/* No info */) {
    // ** addr: 0x7569e4, size: 0x668
    // 0x7569e4: EnterFrame
    //     0x7569e4: stp             fp, lr, [SP, #-0x10]!
    //     0x7569e8: mov             fp, SP
    // 0x7569ec: AllocStack(0xa0)
    //     0x7569ec: sub             SP, SP, #0xa0
    // 0x7569f0: SetupParameters(RenderWrap this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x7569f0: mov             x0, x1
    //     0x7569f4: stur            x1, [fp, #-8]
    //     0x7569f8: mov             x1, x2
    //     0x7569fc: stur            x2, [fp, #-0x10]
    // 0x756a00: CheckStackOverflow
    //     0x756a00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x756a04: cmp             SP, x16
    //     0x756a08: b.ls            #0x756f9c
    // 0x756a0c: LoadField: d0 = r1->field_f
    //     0x756a0c: ldur            d0, [x1, #0xf]
    // 0x756a10: stur            d0, [fp, #-0x60]
    // 0x756a14: r0 = BoxConstraints()
    //     0x756a14: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x756a18: stur            x0, [fp, #-0x38]
    // 0x756a1c: StoreField: r0->field_7 = rZR
    //     0x756a1c: stur            xzr, [x0, #7]
    // 0x756a20: ldur            d0, [fp, #-0x60]
    // 0x756a24: StoreField: r0->field_f = d0
    //     0x756a24: stur            d0, [x0, #0xf]
    // 0x756a28: ArrayStore: r0[0] = rZR  ; List_8
    //     0x756a28: stur            xzr, [x0, #0x17]
    // 0x756a2c: d1 = inf
    //     0x756a2c: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x756a30: StoreField: r0->field_1f = d1
    //     0x756a30: stur            d1, [x0, #0x1f]
    // 0x756a34: ldur            x3, [fp, #-8]
    // 0x756a38: LoadField: r1 = r3->field_5f
    //     0x756a38: ldur            w1, [x3, #0x5f]
    // 0x756a3c: DecompressPointer r1
    //     0x756a3c: add             x1, x1, HEAP, lsl #32
    // 0x756a40: mov             x4, x1
    // 0x756a44: r7 = 0.000000
    //     0x756a44: ldr             x7, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x756a48: d2 = 0.000000
    //     0x756a48: eor             v2.16b, v2.16b, v2.16b
    // 0x756a4c: d1 = 0.000000
    //     0x756a4c: eor             v1.16b, v1.16b, v1.16b
    // 0x756a50: r6 = 0.000000
    //     0x756a50: ldr             x6, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x756a54: r5 = 0
    //     0x756a54: movz            x5, #0
    // 0x756a58: stur            x7, [fp, #-0x18]
    // 0x756a5c: stur            x6, [fp, #-0x20]
    // 0x756a60: stur            x5, [fp, #-0x28]
    // 0x756a64: stur            x4, [fp, #-0x30]
    // 0x756a68: stur            d2, [fp, #-0x68]
    // 0x756a6c: stur            d1, [fp, #-0x70]
    // 0x756a70: CheckStackOverflow
    //     0x756a70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x756a74: cmp             SP, x16
    //     0x756a78: b.ls            #0x756fa4
    // 0x756a7c: cmp             w4, NULL
    // 0x756a80: b.eq            #0x756e48
    // 0x756a84: mov             x1, x4
    // 0x756a88: mov             x2, x0
    // 0x756a8c: r0 = getDryLayout()
    //     0x756a8c: bl              #0x73bde4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x756a90: LoadField: d0 = r0->field_7
    //     0x756a90: ldur            d0, [x0, #7]
    // 0x756a94: stur            d0, [fp, #-0x80]
    // 0x756a98: LoadField: d1 = r0->field_f
    //     0x756a98: ldur            d1, [x0, #0xf]
    // 0x756a9c: ldur            x0, [fp, #-0x28]
    // 0x756aa0: stur            d1, [fp, #-0x78]
    // 0x756aa4: cmp             x0, #0
    // 0x756aa8: b.le            #0x756c34
    // 0x756aac: ldur            x1, [fp, #-8]
    // 0x756ab0: ldur            d3, [fp, #-0x70]
    // 0x756ab4: ldur            d2, [fp, #-0x60]
    // 0x756ab8: fadd            d4, d3, d0
    // 0x756abc: LoadField: d5 = r1->field_6f
    //     0x756abc: ldur            d5, [x1, #0x6f]
    // 0x756ac0: fadd            d6, d4, d5
    // 0x756ac4: fcmp            d6, d2
    // 0x756ac8: b.le            #0x756c1c
    // 0x756acc: ldur            x2, [fp, #-0x18]
    // 0x756ad0: r3 = inline_Allocate_Double()
    //     0x756ad0: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x756ad4: add             x3, x3, #0x10
    //     0x756ad8: cmp             x0, x3
    //     0x756adc: b.ls            #0x756fac
    //     0x756ae0: str             x3, [THR, #0x50]  ; THR::top
    //     0x756ae4: sub             x3, x3, #0xf
    //     0x756ae8: movz            x0, #0xe15c
    //     0x756aec: movk            x0, #0x3, lsl #16
    //     0x756af0: stur            x0, [x3, #-1]
    // 0x756af4: StoreField: r3->field_7 = d3
    //     0x756af4: stur            d3, [x3, #7]
    // 0x756af8: stur            x3, [fp, #-0x40]
    // 0x756afc: r0 = 60
    //     0x756afc: movz            x0, #0x3c
    // 0x756b00: branchIfSmi(r2, 0x756b0c)
    //     0x756b00: tbz             w2, #0, #0x756b0c
    // 0x756b04: r0 = LoadClassIdInstr(r2)
    //     0x756b04: ldur            x0, [x2, #-1]
    //     0x756b08: ubfx            x0, x0, #0xc, #0x14
    // 0x756b0c: stp             x3, x2, [SP]
    // 0x756b10: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x756b10: sub             lr, x0, #0xfe3
    //     0x756b14: ldr             lr, [x21, lr, lsl #3]
    //     0x756b18: blr             lr
    // 0x756b1c: tbnz            w0, #4, #0x756b2c
    // 0x756b20: ldur            x0, [fp, #-0x18]
    // 0x756b24: d0 = 0.000000
    //     0x756b24: eor             v0.16b, v0.16b, v0.16b
    // 0x756b28: b               #0x756be8
    // 0x756b2c: ldur            x1, [fp, #-0x18]
    // 0x756b30: r0 = 60
    //     0x756b30: movz            x0, #0x3c
    // 0x756b34: branchIfSmi(r1, 0x756b40)
    //     0x756b34: tbz             w1, #0, #0x756b40
    // 0x756b38: r0 = LoadClassIdInstr(r1)
    //     0x756b38: ldur            x0, [x1, #-1]
    //     0x756b3c: ubfx            x0, x0, #0xc, #0x14
    // 0x756b40: ldur            x16, [fp, #-0x40]
    // 0x756b44: stp             x16, x1, [SP]
    // 0x756b48: r0 = GDT[cid_x0 + -0xfd2]()
    //     0x756b48: sub             lr, x0, #0xfd2
    //     0x756b4c: ldr             lr, [x21, lr, lsl #3]
    //     0x756b50: blr             lr
    // 0x756b54: tbnz            w0, #4, #0x756b64
    // 0x756b58: ldur            x0, [fp, #-0x40]
    // 0x756b5c: d0 = 0.000000
    //     0x756b5c: eor             v0.16b, v0.16b, v0.16b
    // 0x756b60: b               #0x756be8
    // 0x756b64: ldur            x1, [fp, #-0x18]
    // 0x756b68: r0 = 60
    //     0x756b68: movz            x0, #0x3c
    // 0x756b6c: branchIfSmi(r1, 0x756b78)
    //     0x756b6c: tbz             w1, #0, #0x756b78
    // 0x756b70: r0 = LoadClassIdInstr(r1)
    //     0x756b70: ldur            x0, [x1, #-1]
    //     0x756b74: ubfx            x0, x0, #0xc, #0x14
    // 0x756b78: cmp             x0, #0x3e
    // 0x756b7c: b.ne            #0x756bcc
    // 0x756b80: d0 = 0.000000
    //     0x756b80: eor             v0.16b, v0.16b, v0.16b
    // 0x756b84: LoadField: d1 = r1->field_7
    //     0x756b84: ldur            d1, [x1, #7]
    // 0x756b88: fcmp            d1, d0
    // 0x756b8c: b.ne            #0x756bc4
    // 0x756b90: ldur            d3, [fp, #-0x70]
    // 0x756b94: fadd            d2, d1, d3
    // 0x756b98: r0 = inline_Allocate_Double()
    //     0x756b98: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x756b9c: add             x0, x0, #0x10
    //     0x756ba0: cmp             x1, x0
    //     0x756ba4: b.ls            #0x756fd0
    //     0x756ba8: str             x0, [THR, #0x50]  ; THR::top
    //     0x756bac: sub             x0, x0, #0xf
    //     0x756bb0: movz            x1, #0xe15c
    //     0x756bb4: movk            x1, #0x3, lsl #16
    //     0x756bb8: stur            x1, [x0, #-1]
    // 0x756bbc: StoreField: r0->field_7 = d2
    //     0x756bbc: stur            d2, [x0, #7]
    // 0x756bc0: b               #0x756be8
    // 0x756bc4: ldur            d3, [fp, #-0x70]
    // 0x756bc8: b               #0x756bd4
    // 0x756bcc: ldur            d3, [fp, #-0x70]
    // 0x756bd0: d0 = 0.000000
    //     0x756bd0: eor             v0.16b, v0.16b, v0.16b
    // 0x756bd4: fcmp            d3, d3
    // 0x756bd8: b.vc            #0x756be4
    // 0x756bdc: ldur            x0, [fp, #-0x40]
    // 0x756be0: b               #0x756be8
    // 0x756be4: mov             x0, x1
    // 0x756be8: ldur            x2, [fp, #-8]
    // 0x756bec: ldur            d2, [fp, #-0x68]
    // 0x756bf0: ldur            x3, [fp, #-0x20]
    // 0x756bf4: LoadField: d1 = r2->field_7b
    //     0x756bf4: ldur            d1, [x2, #0x7b]
    // 0x756bf8: LoadField: d3 = r3->field_7
    //     0x756bf8: ldur            d3, [x3, #7]
    // 0x756bfc: fadd            d4, d3, d1
    // 0x756c00: fadd            d3, d2, d4
    // 0x756c04: mov             x7, x0
    // 0x756c08: mov             v4.16b, v3.16b
    // 0x756c0c: d3 = 0.000000
    //     0x756c0c: eor             v3.16b, v3.16b, v3.16b
    // 0x756c10: r3 = 0.000000
    //     0x756c10: ldr             x3, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x756c14: r1 = 0
    //     0x756c14: movz            x1, #0
    // 0x756c18: b               #0x756c58
    // 0x756c1c: mov             x2, x1
    // 0x756c20: ldur            x1, [fp, #-0x18]
    // 0x756c24: ldur            d2, [fp, #-0x68]
    // 0x756c28: ldur            x3, [fp, #-0x20]
    // 0x756c2c: d0 = 0.000000
    //     0x756c2c: eor             v0.16b, v0.16b, v0.16b
    // 0x756c30: b               #0x756c4c
    // 0x756c34: ldur            x2, [fp, #-8]
    // 0x756c38: ldur            x1, [fp, #-0x18]
    // 0x756c3c: ldur            d2, [fp, #-0x68]
    // 0x756c40: ldur            d3, [fp, #-0x70]
    // 0x756c44: ldur            x3, [fp, #-0x20]
    // 0x756c48: d0 = 0.000000
    //     0x756c48: eor             v0.16b, v0.16b, v0.16b
    // 0x756c4c: mov             x7, x1
    // 0x756c50: mov             v4.16b, v2.16b
    // 0x756c54: mov             x1, x0
    // 0x756c58: ldur            d1, [fp, #-0x80]
    // 0x756c5c: ldur            d2, [fp, #-0x78]
    // 0x756c60: stur            x7, [fp, #-0x48]
    // 0x756c64: stur            x3, [fp, #-0x50]
    // 0x756c68: stur            x1, [fp, #-0x28]
    // 0x756c6c: stur            d4, [fp, #-0x90]
    // 0x756c70: fadd            d5, d3, d1
    // 0x756c74: stur            d5, [fp, #-0x88]
    // 0x756c78: r4 = inline_Allocate_Double()
    //     0x756c78: ldp             x4, x0, [THR, #0x50]  ; THR::top
    //     0x756c7c: add             x4, x4, #0x10
    //     0x756c80: cmp             x0, x4
    //     0x756c84: b.ls            #0x756fe0
    //     0x756c88: str             x4, [THR, #0x50]  ; THR::top
    //     0x756c8c: sub             x4, x4, #0xf
    //     0x756c90: movz            x0, #0xe15c
    //     0x756c94: movk            x0, #0x3, lsl #16
    //     0x756c98: stur            x0, [x4, #-1]
    // 0x756c9c: StoreField: r4->field_7 = d2
    //     0x756c9c: stur            d2, [x4, #7]
    // 0x756ca0: stur            x4, [fp, #-0x40]
    // 0x756ca4: r0 = 60
    //     0x756ca4: movz            x0, #0x3c
    // 0x756ca8: branchIfSmi(r3, 0x756cb4)
    //     0x756ca8: tbz             w3, #0, #0x756cb4
    // 0x756cac: r0 = LoadClassIdInstr(r3)
    //     0x756cac: ldur            x0, [x3, #-1]
    //     0x756cb0: ubfx            x0, x0, #0xc, #0x14
    // 0x756cb4: stp             x4, x3, [SP]
    // 0x756cb8: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x756cb8: sub             lr, x0, #0xfe3
    //     0x756cbc: ldr             lr, [x21, lr, lsl #3]
    //     0x756cc0: blr             lr
    // 0x756cc4: tbnz            w0, #4, #0x756cd4
    // 0x756cc8: ldur            x6, [fp, #-0x50]
    // 0x756ccc: d0 = 0.000000
    //     0x756ccc: eor             v0.16b, v0.16b, v0.16b
    // 0x756cd0: b               #0x756d94
    // 0x756cd4: ldur            x1, [fp, #-0x50]
    // 0x756cd8: r0 = 60
    //     0x756cd8: movz            x0, #0x3c
    // 0x756cdc: branchIfSmi(r1, 0x756ce8)
    //     0x756cdc: tbz             w1, #0, #0x756ce8
    // 0x756ce0: r0 = LoadClassIdInstr(r1)
    //     0x756ce0: ldur            x0, [x1, #-1]
    //     0x756ce4: ubfx            x0, x0, #0xc, #0x14
    // 0x756ce8: ldur            x16, [fp, #-0x40]
    // 0x756cec: stp             x16, x1, [SP]
    // 0x756cf0: r0 = GDT[cid_x0 + -0xfd2]()
    //     0x756cf0: sub             lr, x0, #0xfd2
    //     0x756cf4: ldr             lr, [x21, lr, lsl #3]
    //     0x756cf8: blr             lr
    // 0x756cfc: tbnz            w0, #4, #0x756d0c
    // 0x756d00: ldur            x6, [fp, #-0x40]
    // 0x756d04: d0 = 0.000000
    //     0x756d04: eor             v0.16b, v0.16b, v0.16b
    // 0x756d08: b               #0x756d94
    // 0x756d0c: ldur            x0, [fp, #-0x50]
    // 0x756d10: r1 = 60
    //     0x756d10: movz            x1, #0x3c
    // 0x756d14: branchIfSmi(r0, 0x756d20)
    //     0x756d14: tbz             w0, #0, #0x756d20
    // 0x756d18: r1 = LoadClassIdInstr(r0)
    //     0x756d18: ldur            x1, [x0, #-1]
    //     0x756d1c: ubfx            x1, x1, #0xc, #0x14
    // 0x756d20: cmp             x1, #0x3e
    // 0x756d24: b.ne            #0x756d78
    // 0x756d28: d0 = 0.000000
    //     0x756d28: eor             v0.16b, v0.16b, v0.16b
    // 0x756d2c: LoadField: d1 = r0->field_7
    //     0x756d2c: ldur            d1, [x0, #7]
    // 0x756d30: fcmp            d1, d0
    // 0x756d34: b.ne            #0x756d70
    // 0x756d38: ldur            d2, [fp, #-0x78]
    // 0x756d3c: fadd            d3, d1, d2
    // 0x756d40: r0 = inline_Allocate_Double()
    //     0x756d40: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x756d44: add             x0, x0, #0x10
    //     0x756d48: cmp             x1, x0
    //     0x756d4c: b.ls            #0x75700c
    //     0x756d50: str             x0, [THR, #0x50]  ; THR::top
    //     0x756d54: sub             x0, x0, #0xf
    //     0x756d58: movz            x1, #0xe15c
    //     0x756d5c: movk            x1, #0x3, lsl #16
    //     0x756d60: stur            x1, [x0, #-1]
    // 0x756d64: StoreField: r0->field_7 = d3
    //     0x756d64: stur            d3, [x0, #7]
    // 0x756d68: mov             x6, x0
    // 0x756d6c: b               #0x756d94
    // 0x756d70: ldur            d2, [fp, #-0x78]
    // 0x756d74: b               #0x756d80
    // 0x756d78: ldur            d2, [fp, #-0x78]
    // 0x756d7c: d0 = 0.000000
    //     0x756d7c: eor             v0.16b, v0.16b, v0.16b
    // 0x756d80: fcmp            d2, d2
    // 0x756d84: b.vc            #0x756d90
    // 0x756d88: ldur            x6, [fp, #-0x40]
    // 0x756d8c: b               #0x756d94
    // 0x756d90: mov             x6, x0
    // 0x756d94: ldur            x0, [fp, #-0x28]
    // 0x756d98: stur            x6, [fp, #-0x50]
    // 0x756d9c: cmp             x0, #0
    // 0x756da0: b.le            #0x756dbc
    // 0x756da4: ldur            x3, [fp, #-8]
    // 0x756da8: ldur            d1, [fp, #-0x88]
    // 0x756dac: LoadField: d2 = r3->field_6f
    //     0x756dac: ldur            d2, [x3, #0x6f]
    // 0x756db0: fadd            d3, d1, d2
    // 0x756db4: mov             v1.16b, v3.16b
    // 0x756db8: b               #0x756dc4
    // 0x756dbc: ldur            x3, [fp, #-8]
    // 0x756dc0: ldur            d1, [fp, #-0x88]
    // 0x756dc4: ldur            x1, [fp, #-0x30]
    // 0x756dc8: stur            d1, [fp, #-0x78]
    // 0x756dcc: add             x5, x0, #1
    // 0x756dd0: stur            x5, [fp, #-0x58]
    // 0x756dd4: LoadField: r4 = r1->field_7
    //     0x756dd4: ldur            w4, [x1, #7]
    // 0x756dd8: DecompressPointer r4
    //     0x756dd8: add             x4, x4, HEAP, lsl #32
    // 0x756ddc: stur            x4, [fp, #-0x40]
    // 0x756de0: cmp             w4, NULL
    // 0x756de4: b.eq            #0x75701c
    // 0x756de8: mov             x0, x4
    // 0x756dec: r2 = Null
    //     0x756dec: mov             x2, NULL
    // 0x756df0: r1 = Null
    //     0x756df0: mov             x1, NULL
    // 0x756df4: r4 = LoadClassIdInstr(r0)
    //     0x756df4: ldur            x4, [x0, #-1]
    //     0x756df8: ubfx            x4, x4, #0xc, #0x14
    // 0x756dfc: cmp             x4, #0xc79
    // 0x756e00: b.eq            #0x756e18
    // 0x756e04: r8 = WrapParentData
    //     0x756e04: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x756e08: ldr             x8, [x8, #0xaf0]
    // 0x756e0c: r3 = Null
    //     0x756e0c: add             x3, PP, #0x44, lsl #12  ; [pp+0x44bf0] Null
    //     0x756e10: ldr             x3, [x3, #0xbf0]
    // 0x756e14: r0 = DefaultTypeTest()
    //     0x756e14: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x756e18: ldur            x0, [fp, #-0x40]
    // 0x756e1c: LoadField: r4 = r0->field_13
    //     0x756e1c: ldur            w4, [x0, #0x13]
    // 0x756e20: DecompressPointer r4
    //     0x756e20: add             x4, x4, HEAP, lsl #32
    // 0x756e24: ldur            x7, [fp, #-0x48]
    // 0x756e28: ldur            d2, [fp, #-0x90]
    // 0x756e2c: ldur            d1, [fp, #-0x78]
    // 0x756e30: ldur            x6, [fp, #-0x50]
    // 0x756e34: ldur            x5, [fp, #-0x58]
    // 0x756e38: ldur            x3, [fp, #-8]
    // 0x756e3c: ldur            x0, [fp, #-0x38]
    // 0x756e40: ldur            d0, [fp, #-0x60]
    // 0x756e44: b               #0x756a58
    // 0x756e48: mov             x1, x7
    // 0x756e4c: mov             v3.16b, v1.16b
    // 0x756e50: mov             x3, x6
    // 0x756e54: LoadField: d0 = r3->field_7
    //     0x756e54: ldur            d0, [x3, #7]
    // 0x756e58: fadd            d1, d2, d0
    // 0x756e5c: stur            d1, [fp, #-0x60]
    // 0x756e60: r2 = inline_Allocate_Double()
    //     0x756e60: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x756e64: add             x2, x2, #0x10
    //     0x756e68: cmp             x0, x2
    //     0x756e6c: b.ls            #0x757020
    //     0x756e70: str             x2, [THR, #0x50]  ; THR::top
    //     0x756e74: sub             x2, x2, #0xf
    //     0x756e78: movz            x0, #0xe15c
    //     0x756e7c: movk            x0, #0x3, lsl #16
    //     0x756e80: stur            x0, [x2, #-1]
    // 0x756e84: StoreField: r2->field_7 = d3
    //     0x756e84: stur            d3, [x2, #7]
    // 0x756e88: stur            x2, [fp, #-8]
    // 0x756e8c: r0 = 60
    //     0x756e8c: movz            x0, #0x3c
    // 0x756e90: branchIfSmi(r1, 0x756e9c)
    //     0x756e90: tbz             w1, #0, #0x756e9c
    // 0x756e94: r0 = LoadClassIdInstr(r1)
    //     0x756e94: ldur            x0, [x1, #-1]
    //     0x756e98: ubfx            x0, x0, #0xc, #0x14
    // 0x756e9c: stp             x2, x1, [SP]
    // 0x756ea0: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x756ea0: sub             lr, x0, #0xfe3
    //     0x756ea4: ldr             lr, [x21, lr, lsl #3]
    //     0x756ea8: blr             lr
    // 0x756eac: tbnz            w0, #4, #0x756eb8
    // 0x756eb0: ldur            x0, [fp, #-0x18]
    // 0x756eb4: b               #0x756f64
    // 0x756eb8: ldur            x1, [fp, #-0x18]
    // 0x756ebc: r0 = 60
    //     0x756ebc: movz            x0, #0x3c
    // 0x756ec0: branchIfSmi(r1, 0x756ecc)
    //     0x756ec0: tbz             w1, #0, #0x756ecc
    // 0x756ec4: r0 = LoadClassIdInstr(r1)
    //     0x756ec4: ldur            x0, [x1, #-1]
    //     0x756ec8: ubfx            x0, x0, #0xc, #0x14
    // 0x756ecc: ldur            x16, [fp, #-8]
    // 0x756ed0: stp             x16, x1, [SP]
    // 0x756ed4: r0 = GDT[cid_x0 + -0xfd2]()
    //     0x756ed4: sub             lr, x0, #0xfd2
    //     0x756ed8: ldr             lr, [x21, lr, lsl #3]
    //     0x756edc: blr             lr
    // 0x756ee0: tbnz            w0, #4, #0x756eec
    // 0x756ee4: ldur            x0, [fp, #-8]
    // 0x756ee8: b               #0x756f64
    // 0x756eec: ldur            x0, [fp, #-0x18]
    // 0x756ef0: r1 = 60
    //     0x756ef0: movz            x1, #0x3c
    // 0x756ef4: branchIfSmi(r0, 0x756f00)
    //     0x756ef4: tbz             w0, #0, #0x756f00
    // 0x756ef8: r1 = LoadClassIdInstr(r0)
    //     0x756ef8: ldur            x1, [x0, #-1]
    //     0x756efc: ubfx            x1, x1, #0xc, #0x14
    // 0x756f00: cmp             x1, #0x3e
    // 0x756f04: b.ne            #0x756f54
    // 0x756f08: d0 = 0.000000
    //     0x756f08: eor             v0.16b, v0.16b, v0.16b
    // 0x756f0c: LoadField: d1 = r0->field_7
    //     0x756f0c: ldur            d1, [x0, #7]
    // 0x756f10: fcmp            d1, d0
    // 0x756f14: b.ne            #0x756f4c
    // 0x756f18: ldur            d0, [fp, #-0x70]
    // 0x756f1c: fadd            d2, d1, d0
    // 0x756f20: r0 = inline_Allocate_Double()
    //     0x756f20: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x756f24: add             x0, x0, #0x10
    //     0x756f28: cmp             x1, x0
    //     0x756f2c: b.ls            #0x75703c
    //     0x756f30: str             x0, [THR, #0x50]  ; THR::top
    //     0x756f34: sub             x0, x0, #0xf
    //     0x756f38: movz            x1, #0xe15c
    //     0x756f3c: movk            x1, #0x3, lsl #16
    //     0x756f40: stur            x1, [x0, #-1]
    // 0x756f44: StoreField: r0->field_7 = d2
    //     0x756f44: stur            d2, [x0, #7]
    // 0x756f48: b               #0x756f64
    // 0x756f4c: ldur            d0, [fp, #-0x70]
    // 0x756f50: b               #0x756f58
    // 0x756f54: ldur            d0, [fp, #-0x70]
    // 0x756f58: fcmp            d0, d0
    // 0x756f5c: b.vc            #0x756f64
    // 0x756f60: ldur            x0, [fp, #-8]
    // 0x756f64: ldur            d0, [fp, #-0x60]
    // 0x756f68: LoadField: d1 = r0->field_7
    //     0x756f68: ldur            d1, [x0, #7]
    // 0x756f6c: stur            d1, [fp, #-0x68]
    // 0x756f70: r0 = Size()
    //     0x756f70: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x756f74: ldur            d0, [fp, #-0x68]
    // 0x756f78: StoreField: r0->field_7 = d0
    //     0x756f78: stur            d0, [x0, #7]
    // 0x756f7c: ldur            d0, [fp, #-0x60]
    // 0x756f80: StoreField: r0->field_f = d0
    //     0x756f80: stur            d0, [x0, #0xf]
    // 0x756f84: ldur            x1, [fp, #-0x10]
    // 0x756f88: mov             x2, x0
    // 0x756f8c: r0 = constrain()
    //     0x756f8c: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x756f90: LeaveFrame
    //     0x756f90: mov             SP, fp
    //     0x756f94: ldp             fp, lr, [SP], #0x10
    // 0x756f98: ret
    //     0x756f98: ret             
    // 0x756f9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x756f9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x756fa0: b               #0x756a0c
    // 0x756fa4: r0 = StackOverflowSharedWithFPURegs()
    //     0x756fa4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x756fa8: b               #0x756a7c
    // 0x756fac: stp             q2, q3, [SP, #-0x20]!
    // 0x756fb0: stp             q0, q1, [SP, #-0x20]!
    // 0x756fb4: stp             x1, x2, [SP, #-0x10]!
    // 0x756fb8: r0 = AllocateDouble()
    //     0x756fb8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x756fbc: mov             x3, x0
    // 0x756fc0: ldp             x1, x2, [SP], #0x10
    // 0x756fc4: ldp             q0, q1, [SP], #0x20
    // 0x756fc8: ldp             q2, q3, [SP], #0x20
    // 0x756fcc: b               #0x756af4
    // 0x756fd0: stp             q0, q2, [SP, #-0x20]!
    // 0x756fd4: r0 = AllocateDouble()
    //     0x756fd4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x756fd8: ldp             q0, q2, [SP], #0x20
    // 0x756fdc: b               #0x756bbc
    // 0x756fe0: stp             q4, q5, [SP, #-0x20]!
    // 0x756fe4: stp             q0, q2, [SP, #-0x20]!
    // 0x756fe8: stp             x3, x7, [SP, #-0x10]!
    // 0x756fec: stp             x1, x2, [SP, #-0x10]!
    // 0x756ff0: r0 = AllocateDouble()
    //     0x756ff0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x756ff4: mov             x4, x0
    // 0x756ff8: ldp             x1, x2, [SP], #0x10
    // 0x756ffc: ldp             x3, x7, [SP], #0x10
    // 0x757000: ldp             q0, q2, [SP], #0x20
    // 0x757004: ldp             q4, q5, [SP], #0x20
    // 0x757008: b               #0x756c9c
    // 0x75700c: stp             q0, q3, [SP, #-0x20]!
    // 0x757010: r0 = AllocateDouble()
    //     0x757010: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757014: ldp             q0, q3, [SP], #0x20
    // 0x757018: b               #0x756d64
    // 0x75701c: r0 = NullCastErrorSharedWithFPURegs()
    //     0x75701c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x757020: stp             q1, q3, [SP, #-0x20]!
    // 0x757024: SaveReg r1
    //     0x757024: str             x1, [SP, #-8]!
    // 0x757028: r0 = AllocateDouble()
    //     0x757028: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75702c: mov             x2, x0
    // 0x757030: RestoreReg r1
    //     0x757030: ldr             x1, [SP], #8
    // 0x757034: ldp             q1, q3, [SP], #0x20
    // 0x757038: b               #0x756e84
    // 0x75703c: SaveReg d2
    //     0x75703c: str             q2, [SP, #-0x10]!
    // 0x757040: r0 = AllocateDouble()
    //     0x757040: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757044: RestoreReg d2
    //     0x757044: ldr             q2, [SP], #0x10
    // 0x757048: b               #0x756f44
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x778f70, size: 0x1ac
    // 0x778f70: EnterFrame
    //     0x778f70: stp             fp, lr, [SP, #-0x10]!
    //     0x778f74: mov             fp, SP
    // 0x778f78: AllocStack(0x20)
    //     0x778f78: sub             SP, SP, #0x20
    // 0x778f7c: SetupParameters(RenderWrap this /* r1 => r3, fp-0x10 */)
    //     0x778f7c: mov             x3, x1
    //     0x778f80: stur            x1, [fp, #-0x10]
    // 0x778f84: CheckStackOverflow
    //     0x778f84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x778f88: cmp             SP, x16
    //     0x778f8c: b.ls            #0x779114
    // 0x778f90: LoadField: r4 = r3->field_27
    //     0x778f90: ldur            w4, [x3, #0x27]
    // 0x778f94: DecompressPointer r4
    //     0x778f94: add             x4, x4, HEAP, lsl #32
    // 0x778f98: stur            x4, [fp, #-8]
    // 0x778f9c: cmp             w4, NULL
    // 0x778fa0: b.eq            #0x7790f8
    // 0x778fa4: mov             x0, x4
    // 0x778fa8: r2 = Null
    //     0x778fa8: mov             x2, NULL
    // 0x778fac: r1 = Null
    //     0x778fac: mov             x1, NULL
    // 0x778fb0: r4 = LoadClassIdInstr(r0)
    //     0x778fb0: ldur            x4, [x0, #-1]
    //     0x778fb4: ubfx            x4, x4, #0xc, #0x14
    // 0x778fb8: sub             x4, x4, #0xc83
    // 0x778fbc: cmp             x4, #1
    // 0x778fc0: b.ls            #0x778fd4
    // 0x778fc4: r8 = BoxConstraints
    //     0x778fc4: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x778fc8: r3 = Null
    //     0x778fc8: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b18] Null
    //     0x778fcc: ldr             x3, [x3, #0xb18]
    // 0x778fd0: r0 = BoxConstraints()
    //     0x778fd0: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x778fd4: ldur            x0, [fp, #-0x10]
    // 0x778fd8: LoadField: r1 = r0->field_5f
    //     0x778fd8: ldur            w1, [x0, #0x5f]
    // 0x778fdc: DecompressPointer r1
    //     0x778fdc: add             x1, x1, HEAP, lsl #32
    // 0x778fe0: cmp             w1, NULL
    // 0x778fe4: b.ne            #0x779028
    // 0x778fe8: ldur            x1, [fp, #-8]
    // 0x778fec: r0 = smallest()
    //     0x778fec: bl              #0x7335c0  ; [package:flutter/src/rendering/box.dart] BoxConstraints::smallest
    // 0x778ff0: ldur            x4, [fp, #-0x10]
    // 0x778ff4: StoreField: r4->field_53 = r0
    //     0x778ff4: stur            w0, [x4, #0x53]
    //     0x778ff8: ldurb           w16, [x4, #-1]
    //     0x778ffc: ldurb           w17, [x0, #-1]
    //     0x779000: and             x16, x17, x16, lsr #2
    //     0x779004: tst             x16, HEAP, lsr #32
    //     0x779008: b.eq            #0x779010
    //     0x77900c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x779010: r0 = false
    //     0x779010: add             x0, NULL, #0x30  ; false
    // 0x779014: StoreField: r4->field_93 = r0
    //     0x779014: stur            w0, [x4, #0x93]
    // 0x779018: r0 = Null
    //     0x779018: mov             x0, NULL
    // 0x77901c: LeaveFrame
    //     0x77901c: mov             SP, fp
    //     0x779020: ldp             fp, lr, [SP], #0x10
    // 0x779024: ret
    //     0x779024: ret             
    // 0x779028: mov             x4, x0
    // 0x77902c: mov             x1, x4
    // 0x779030: ldur            x2, [fp, #-8]
    // 0x779034: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static.
    //     0x779034: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b28] Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static. (0x7e54fb1673f8)
    //     0x779038: ldr             x3, [x3, #0xb28]
    // 0x77903c: r0 = _computeRuns()
    //     0x77903c: bl              #0x746720  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::_computeRuns
    // 0x779040: mov             x3, x0
    // 0x779044: stur            x3, [fp, #-0x20]
    // 0x779048: mov             x4, x1
    // 0x77904c: mov             x1, x3
    // 0x779050: ldur            x2, [fp, #-8]
    // 0x779054: stur            x4, [fp, #-0x18]
    // 0x779058: r0 = _AxisSize.applyConstraints()
    //     0x779058: bl              #0x7466e8  ; [package:flutter/src/rendering/wrap.dart] ::_AxisSize.applyConstraints
    // 0x77905c: mov             x4, x0
    // 0x779060: ldur            x3, [fp, #-0x10]
    // 0x779064: stur            x4, [fp, #-8]
    // 0x779068: StoreField: r3->field_53 = r0
    //     0x779068: stur            w0, [x3, #0x53]
    //     0x77906c: ldurb           w16, [x3, #-1]
    //     0x779070: ldurb           w17, [x0, #-1]
    //     0x779074: and             x16, x17, x16, lsr #2
    //     0x779078: tst             x16, HEAP, lsr #32
    //     0x77907c: b.eq            #0x779084
    //     0x779080: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x779084: mov             x1, x4
    // 0x779088: ldur            x2, [fp, #-0x20]
    // 0x77908c: r0 = _AxisSize.-()
    //     0x77908c: bl              #0x77911c  ; [package:flutter/src/rendering/wrap.dart] ::_AxisSize.-
    // 0x779090: LoadField: d0 = r0->field_7
    //     0x779090: ldur            d0, [x0, #7]
    // 0x779094: d1 = 0.000000
    //     0x779094: eor             v1.16b, v1.16b, v1.16b
    // 0x779098: fcmp            d1, d0
    // 0x77909c: b.le            #0x7790a8
    // 0x7790a0: r2 = true
    //     0x7790a0: add             x2, NULL, #0x20  ; true
    // 0x7790a4: b               #0x7790c0
    // 0x7790a8: LoadField: d0 = r0->field_f
    //     0x7790a8: ldur            d0, [x0, #0xf]
    // 0x7790ac: fcmp            d1, d0
    // 0x7790b0: r16 = true
    //     0x7790b0: add             x16, NULL, #0x20  ; true
    // 0x7790b4: r17 = false
    //     0x7790b4: add             x17, NULL, #0x30  ; false
    // 0x7790b8: csel            x1, x16, x17, gt
    // 0x7790bc: mov             x2, x1
    // 0x7790c0: ldur            x1, [fp, #-0x10]
    // 0x7790c4: StoreField: r1->field_93 = r2
    //     0x7790c4: stur            w2, [x1, #0x93]
    // 0x7790c8: ldur            x2, [fp, #-0x18]
    // 0x7790cc: mov             x3, x0
    // 0x7790d0: ldur            x5, [fp, #-8]
    // 0x7790d4: r6 = Closure: (Offset, RenderBox) => void from Function '_setChildPosition@413302920': static.
    //     0x7790d4: add             x6, PP, #0x44, lsl #12  ; [pp+0x44b30] Closure: (Offset, RenderBox) => void from Function '_setChildPosition@413302920': static. (0x7e54fb179198)
    //     0x7790d8: ldr             x6, [x6, #0xb30]
    // 0x7790dc: r7 = Closure: (RenderBox) => Size from Function '_getChildSize@413302920': static.
    //     0x7790dc: add             x7, PP, #0x44, lsl #12  ; [pp+0x44b38] Closure: (RenderBox) => Size from Function '_getChildSize@413302920': static. (0x7e54fb179168)
    //     0x7790e0: ldr             x7, [x7, #0xb38]
    // 0x7790e4: r0 = _positionChildren()
    //     0x7790e4: bl              #0x745d60  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::_positionChildren
    // 0x7790e8: r0 = Null
    //     0x7790e8: mov             x0, NULL
    // 0x7790ec: LeaveFrame
    //     0x7790ec: mov             SP, fp
    //     0x7790f0: ldp             fp, lr, [SP], #0x10
    // 0x7790f4: ret
    //     0x7790f4: ret             
    // 0x7790f8: r0 = StateError()
    //     0x7790f8: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x7790fc: mov             x1, x0
    // 0x779100: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x779100: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x779104: StoreField: r1->field_b = r0
    //     0x779104: stur            w0, [x1, #0xb]
    // 0x779108: mov             x0, x1
    // 0x77910c: r0 = Throw()
    //     0x77910c: bl              #0xec04b8  ; ThrowStub
    // 0x779110: brk             #0
    // 0x779114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x779114: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x779118: b               #0x778f90
  }
  [closure] static Size _getChildSize(dynamic, RenderBox) {
    // ** addr: 0x779168, size: 0x30
    // 0x779168: EnterFrame
    //     0x779168: stp             fp, lr, [SP, #-0x10]!
    //     0x77916c: mov             fp, SP
    // 0x779170: CheckStackOverflow
    //     0x779170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x779174: cmp             SP, x16
    //     0x779178: b.ls            #0x779190
    // 0x77917c: ldr             x1, [fp, #0x10]
    // 0x779180: r0 = size()
    //     0x779180: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x779184: LeaveFrame
    //     0x779184: mov             SP, fp
    //     0x779188: ldp             fp, lr, [SP], #0x10
    // 0x77918c: ret
    //     0x77918c: ret             
    // 0x779190: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x779190: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x779194: b               #0x77917c
  }
  [closure] static void _setChildPosition(dynamic, Offset, RenderBox) {
    // ** addr: 0x779198, size: 0x34
    // 0x779198: EnterFrame
    //     0x779198: stp             fp, lr, [SP, #-0x10]!
    //     0x77919c: mov             fp, SP
    // 0x7791a0: CheckStackOverflow
    //     0x7791a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7791a4: cmp             SP, x16
    //     0x7791a8: b.ls            #0x7791c4
    // 0x7791ac: ldr             x1, [fp, #0x18]
    // 0x7791b0: ldr             x2, [fp, #0x10]
    // 0x7791b4: r0 = _setChildPosition()
    //     0x7791b4: bl              #0x7791cc  ; [package:flutter/src/rendering/wrap.dart] RenderWrap::_setChildPosition
    // 0x7791b8: LeaveFrame
    //     0x7791b8: mov             SP, fp
    //     0x7791bc: ldp             fp, lr, [SP], #0x10
    // 0x7791c0: ret
    //     0x7791c0: ret             
    // 0x7791c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7791c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7791c8: b               #0x7791ac
  }
  static _ _setChildPosition(/* No info */) {
    // ** addr: 0x7791cc, size: 0x90
    // 0x7791cc: EnterFrame
    //     0x7791cc: stp             fp, lr, [SP, #-0x10]!
    //     0x7791d0: mov             fp, SP
    // 0x7791d4: AllocStack(0x10)
    //     0x7791d4: sub             SP, SP, #0x10
    // 0x7791d8: SetupParameters(dynamic _ /* r1 => r3, fp-0x10 */)
    //     0x7791d8: mov             x3, x1
    //     0x7791dc: stur            x1, [fp, #-0x10]
    // 0x7791e0: LoadField: r4 = r2->field_7
    //     0x7791e0: ldur            w4, [x2, #7]
    // 0x7791e4: DecompressPointer r4
    //     0x7791e4: add             x4, x4, HEAP, lsl #32
    // 0x7791e8: stur            x4, [fp, #-8]
    // 0x7791ec: cmp             w4, NULL
    // 0x7791f0: b.eq            #0x779258
    // 0x7791f4: mov             x0, x4
    // 0x7791f8: r2 = Null
    //     0x7791f8: mov             x2, NULL
    // 0x7791fc: r1 = Null
    //     0x7791fc: mov             x1, NULL
    // 0x779200: r4 = LoadClassIdInstr(r0)
    //     0x779200: ldur            x4, [x0, #-1]
    //     0x779204: ubfx            x4, x4, #0xc, #0x14
    // 0x779208: cmp             x4, #0xc79
    // 0x77920c: b.eq            #0x779224
    // 0x779210: r8 = WrapParentData
    //     0x779210: add             x8, PP, #0x44, lsl #12  ; [pp+0x44af0] Type: WrapParentData
    //     0x779214: ldr             x8, [x8, #0xaf0]
    // 0x779218: r3 = Null
    //     0x779218: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b40] Null
    //     0x77921c: ldr             x3, [x3, #0xb40]
    // 0x779220: r0 = DefaultTypeTest()
    //     0x779220: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x779224: ldur            x0, [fp, #-0x10]
    // 0x779228: ldur            x1, [fp, #-8]
    // 0x77922c: StoreField: r1->field_7 = r0
    //     0x77922c: stur            w0, [x1, #7]
    //     0x779230: ldurb           w16, [x1, #-1]
    //     0x779234: ldurb           w17, [x0, #-1]
    //     0x779238: and             x16, x17, x16, lsr #2
    //     0x77923c: tst             x16, HEAP, lsr #32
    //     0x779240: b.eq            #0x779248
    //     0x779244: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x779248: r0 = Null
    //     0x779248: mov             x0, NULL
    // 0x77924c: LeaveFrame
    //     0x77924c: mov             SP, fp
    //     0x779250: ldp             fp, lr, [SP], #0x10
    // 0x779254: ret
    //     0x779254: ret             
    // 0x779258: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x779258: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ setupParentData(/* No info */) {
    // ** addr: 0x7885f0, size: 0xb0
    // 0x7885f0: EnterFrame
    //     0x7885f0: stp             fp, lr, [SP, #-0x10]!
    //     0x7885f4: mov             fp, SP
    // 0x7885f8: AllocStack(0x8)
    //     0x7885f8: sub             SP, SP, #8
    // 0x7885fc: SetupParameters(RenderWrap this /* r1 => r4 */, dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x7885fc: mov             x0, x2
    //     0x788600: mov             x4, x1
    //     0x788604: mov             x3, x2
    //     0x788608: stur            x2, [fp, #-8]
    // 0x78860c: r2 = Null
    //     0x78860c: mov             x2, NULL
    // 0x788610: r1 = Null
    //     0x788610: mov             x1, NULL
    // 0x788614: r4 = 60
    //     0x788614: movz            x4, #0x3c
    // 0x788618: branchIfSmi(r0, 0x788624)
    //     0x788618: tbz             w0, #0, #0x788624
    // 0x78861c: r4 = LoadClassIdInstr(r0)
    //     0x78861c: ldur            x4, [x0, #-1]
    //     0x788620: ubfx            x4, x4, #0xc, #0x14
    // 0x788624: sub             x4, x4, #0xbba
    // 0x788628: cmp             x4, #0x9a
    // 0x78862c: b.ls            #0x788640
    // 0x788630: r8 = RenderBox
    //     0x788630: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x788634: r3 = Null
    //     0x788634: add             x3, PP, #0x44, lsl #12  ; [pp+0x44c00] Null
    //     0x788638: ldr             x3, [x3, #0xc00]
    // 0x78863c: r0 = RenderBox()
    //     0x78863c: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x788640: ldur            x0, [fp, #-8]
    // 0x788644: LoadField: r1 = r0->field_7
    //     0x788644: ldur            w1, [x0, #7]
    // 0x788648: DecompressPointer r1
    //     0x788648: add             x1, x1, HEAP, lsl #32
    // 0x78864c: r2 = LoadClassIdInstr(r1)
    //     0x78864c: ldur            x2, [x1, #-1]
    //     0x788650: ubfx            x2, x2, #0xc, #0x14
    // 0x788654: cmp             x2, #0xc79
    // 0x788658: b.eq            #0x788690
    // 0x78865c: r1 = <RenderBox>
    //     0x78865c: add             x1, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x788660: ldr             x1, [x1, #0x1d8]
    // 0x788664: r0 = WrapParentData()
    //     0x788664: bl              #0x7886a0  ; AllocateWrapParentDataStub -> WrapParentData (size=0x18)
    // 0x788668: r1 = Instance_Offset
    //     0x788668: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x78866c: StoreField: r0->field_7 = r1
    //     0x78866c: stur            w1, [x0, #7]
    // 0x788670: ldur            x1, [fp, #-8]
    // 0x788674: StoreField: r1->field_7 = r0
    //     0x788674: stur            w0, [x1, #7]
    //     0x788678: ldurb           w16, [x1, #-1]
    //     0x78867c: ldurb           w17, [x0, #-1]
    //     0x788680: and             x16, x17, x16, lsr #2
    //     0x788684: tst             x16, HEAP, lsr #32
    //     0x788688: b.eq            #0x788690
    //     0x78868c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x788690: r0 = Null
    //     0x788690: mov             x0, NULL
    // 0x788694: LeaveFrame
    //     0x788694: mov             SP, fp
    //     0x788698: ldp             fp, lr, [SP], #0x10
    // 0x78869c: ret
    //     0x78869c: ret             
  }
  _ paint(/* No info */) {
    // ** addr: 0x79ee34, size: 0x64
    // 0x79ee34: EnterFrame
    //     0x79ee34: stp             fp, lr, [SP, #-0x10]!
    //     0x79ee38: mov             fp, SP
    // 0x79ee3c: AllocStack(0x18)
    //     0x79ee3c: sub             SP, SP, #0x18
    // 0x79ee40: SetupParameters(RenderWrap this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x79ee40: mov             x4, x1
    //     0x79ee44: mov             x0, x2
    //     0x79ee48: stur            x1, [fp, #-8]
    //     0x79ee4c: stur            x2, [fp, #-0x10]
    //     0x79ee50: stur            x3, [fp, #-0x18]
    // 0x79ee54: CheckStackOverflow
    //     0x79ee54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79ee58: cmp             SP, x16
    //     0x79ee5c: b.ls            #0x79ee90
    // 0x79ee60: LoadField: r1 = r4->field_97
    //     0x79ee60: ldur            w1, [x4, #0x97]
    // 0x79ee64: DecompressPointer r1
    //     0x79ee64: add             x1, x1, HEAP, lsl #32
    // 0x79ee68: r2 = Null
    //     0x79ee68: mov             x2, NULL
    // 0x79ee6c: r0 = layer=()
    //     0x79ee6c: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0x79ee70: ldur            x1, [fp, #-8]
    // 0x79ee74: ldur            x2, [fp, #-0x10]
    // 0x79ee78: ldur            x3, [fp, #-0x18]
    // 0x79ee7c: r0 = defaultPaint()
    //     0x79ee7c: bl              #0x79ee98  ; [package:flutter/src/rendering/wrap.dart] _RenderWrap&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultPaint
    // 0x79ee80: r0 = Null
    //     0x79ee80: mov             x0, NULL
    // 0x79ee84: LeaveFrame
    //     0x79ee84: mov             SP, fp
    //     0x79ee88: ldp             fp, lr, [SP], #0x10
    // 0x79ee8c: ret
    //     0x79ee8c: ret             
    // 0x79ee90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x79ee90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x79ee94: b               #0x79ee60
  }
  _ dispose(/* No info */) {
    // ** addr: 0x7a1518, size: 0x50
    // 0x7a1518: EnterFrame
    //     0x7a1518: stp             fp, lr, [SP, #-0x10]!
    //     0x7a151c: mov             fp, SP
    // 0x7a1520: AllocStack(0x8)
    //     0x7a1520: sub             SP, SP, #8
    // 0x7a1524: SetupParameters(RenderWrap this /* r1 => r0, fp-0x8 */)
    //     0x7a1524: mov             x0, x1
    //     0x7a1528: stur            x1, [fp, #-8]
    // 0x7a152c: CheckStackOverflow
    //     0x7a152c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a1530: cmp             SP, x16
    //     0x7a1534: b.ls            #0x7a1560
    // 0x7a1538: LoadField: r1 = r0->field_97
    //     0x7a1538: ldur            w1, [x0, #0x97]
    // 0x7a153c: DecompressPointer r1
    //     0x7a153c: add             x1, x1, HEAP, lsl #32
    // 0x7a1540: r2 = Null
    //     0x7a1540: mov             x2, NULL
    // 0x7a1544: r0 = layer=()
    //     0x7a1544: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0x7a1548: ldur            x1, [fp, #-8]
    // 0x7a154c: r0 = dispose()
    //     0x7a154c: bl              #0x7a17a8  ; [package:flutter/src/rendering/object.dart] RenderObject::dispose
    // 0x7a1550: r0 = Null
    //     0x7a1550: mov             x0, NULL
    // 0x7a1554: LeaveFrame
    //     0x7a1554: mov             SP, fp
    //     0x7a1558: ldp             fp, lr, [SP], #0x10
    // 0x7a155c: ret
    //     0x7a155c: ret             
    // 0x7a1560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a1560: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a1564: b               #0x7a1538
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x7ffc0c, size: 0x2c
    // 0x7ffc0c: EnterFrame
    //     0x7ffc0c: stp             fp, lr, [SP, #-0x10]!
    //     0x7ffc10: mov             fp, SP
    // 0x7ffc14: CheckStackOverflow
    //     0x7ffc14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ffc18: cmp             SP, x16
    //     0x7ffc1c: b.ls            #0x7ffc30
    // 0x7ffc20: r0 = defaultHitTestChildren()
    //     0x7ffc20: bl              #0x7ffc38  ; [package:flutter/src/rendering/wrap.dart] _RenderWrap&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultHitTestChildren
    // 0x7ffc24: LeaveFrame
    //     0x7ffc24: mov             SP, fp
    //     0x7ffc28: ldp             fp, lr, [SP], #0x10
    // 0x7ffc2c: ret
    //     0x7ffc2c: ret             
    // 0x7ffc30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ffc30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ffc34: b               #0x7ffc20
  }
  _ RenderWrap(/* No info */) {
    // ** addr: 0x856ad8, size: 0x110
    // 0x856ad8: EnterFrame
    //     0x856ad8: stp             fp, lr, [SP, #-0x10]!
    //     0x856adc: mov             fp, SP
    // 0x856ae0: AllocStack(0x20)
    //     0x856ae0: sub             SP, SP, #0x20
    // 0x856ae4: r0 = false
    //     0x856ae4: add             x0, NULL, #0x30  ; false
    // 0x856ae8: mov             x3, x1
    // 0x856aec: stur            x1, [fp, #-8]
    // 0x856af0: stur            x2, [fp, #-0x10]
    // 0x856af4: stur            d0, [fp, #-0x18]
    // 0x856af8: stur            d1, [fp, #-0x20]
    // 0x856afc: CheckStackOverflow
    //     0x856afc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x856b00: cmp             SP, x16
    //     0x856b04: b.ls            #0x856be0
    // 0x856b08: StoreField: r3->field_93 = r0
    //     0x856b08: stur            w0, [x3, #0x93]
    // 0x856b0c: r1 = <ClipRectLayer>
    //     0x856b0c: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a558] TypeArguments: <ClipRectLayer>
    //     0x856b10: ldr             x1, [x1, #0x558]
    // 0x856b14: r0 = LayerHandle()
    //     0x856b14: bl              #0x6d28c4  ; AllocateLayerHandleStub -> LayerHandle<X0 bound Layer> (size=0x10)
    // 0x856b18: ldur            x1, [fp, #-8]
    // 0x856b1c: StoreField: r1->field_97 = r0
    //     0x856b1c: stur            w0, [x1, #0x97]
    //     0x856b20: ldurb           w16, [x1, #-1]
    //     0x856b24: ldurb           w17, [x0, #-1]
    //     0x856b28: and             x16, x17, x16, lsr #2
    //     0x856b2c: tst             x16, HEAP, lsr #32
    //     0x856b30: b.eq            #0x856b38
    //     0x856b34: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856b38: r0 = Instance_Axis
    //     0x856b38: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x856b3c: StoreField: r1->field_67 = r0
    //     0x856b3c: stur            w0, [x1, #0x67]
    // 0x856b40: r0 = Instance_WrapAlignment
    //     0x856b40: add             x0, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0x856b44: ldr             x0, [x0, #0x610]
    // 0x856b48: StoreField: r1->field_6b = r0
    //     0x856b48: stur            w0, [x1, #0x6b]
    // 0x856b4c: ldur            d0, [fp, #-0x20]
    // 0x856b50: StoreField: r1->field_6f = d0
    //     0x856b50: stur            d0, [x1, #0x6f]
    // 0x856b54: StoreField: r1->field_77 = r0
    //     0x856b54: stur            w0, [x1, #0x77]
    // 0x856b58: ldur            d0, [fp, #-0x18]
    // 0x856b5c: StoreField: r1->field_7b = d0
    //     0x856b5c: stur            d0, [x1, #0x7b]
    // 0x856b60: r0 = Instance_WrapCrossAlignment
    //     0x856b60: add             x0, PP, #0x27, lsl #12  ; [pp+0x27618] Obj!WrapCrossAlignment@e35201
    //     0x856b64: ldr             x0, [x0, #0x618]
    // 0x856b68: StoreField: r1->field_83 = r0
    //     0x856b68: stur            w0, [x1, #0x83]
    // 0x856b6c: ldur            x0, [fp, #-0x10]
    // 0x856b70: StoreField: r1->field_87 = r0
    //     0x856b70: stur            w0, [x1, #0x87]
    //     0x856b74: ldurb           w16, [x1, #-1]
    //     0x856b78: ldurb           w17, [x0, #-1]
    //     0x856b7c: and             x16, x17, x16, lsr #2
    //     0x856b80: tst             x16, HEAP, lsr #32
    //     0x856b84: b.eq            #0x856b8c
    //     0x856b88: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856b8c: r0 = Instance_VerticalDirection
    //     0x856b8c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0x856b90: ldr             x0, [x0, #0x748]
    // 0x856b94: StoreField: r1->field_8b = r0
    //     0x856b94: stur            w0, [x1, #0x8b]
    // 0x856b98: r0 = Instance_Clip
    //     0x856b98: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x856b9c: ldr             x0, [x0, #0x750]
    // 0x856ba0: StoreField: r1->field_8f = r0
    //     0x856ba0: stur            w0, [x1, #0x8f]
    // 0x856ba4: StoreField: r1->field_57 = rZR
    //     0x856ba4: stur            xzr, [x1, #0x57]
    // 0x856ba8: r0 = _LayoutCacheStorage()
    //     0x856ba8: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x856bac: ldur            x1, [fp, #-8]
    // 0x856bb0: StoreField: r1->field_4f = r0
    //     0x856bb0: stur            w0, [x1, #0x4f]
    //     0x856bb4: ldurb           w16, [x1, #-1]
    //     0x856bb8: ldurb           w17, [x0, #-1]
    //     0x856bbc: and             x16, x17, x16, lsr #2
    //     0x856bc0: tst             x16, HEAP, lsr #32
    //     0x856bc4: b.eq            #0x856bcc
    //     0x856bc8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x856bcc: r0 = RenderObject()
    //     0x856bcc: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x856bd0: r0 = Null
    //     0x856bd0: mov             x0, NULL
    // 0x856bd4: LeaveFrame
    //     0x856bd4: mov             SP, fp
    //     0x856bd8: ldp             fp, lr, [SP], #0x10
    // 0x856bdc: ret
    //     0x856bdc: ret             
    // 0x856be0: r0 = StackOverflowSharedWithFPURegs()
    //     0x856be0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x856be4: b               #0x856b08
  }
  set _ textDirection=(/* No info */) {
    // ** addr: 0xc69dc4, size: 0x60
    // 0xc69dc4: EnterFrame
    //     0xc69dc4: stp             fp, lr, [SP, #-0x10]!
    //     0xc69dc8: mov             fp, SP
    // 0xc69dcc: mov             x0, x2
    // 0xc69dd0: CheckStackOverflow
    //     0xc69dd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc69dd4: cmp             SP, x16
    //     0xc69dd8: b.ls            #0xc69e1c
    // 0xc69ddc: LoadField: r2 = r1->field_87
    //     0xc69ddc: ldur            w2, [x1, #0x87]
    // 0xc69de0: DecompressPointer r2
    //     0xc69de0: add             x2, x2, HEAP, lsl #32
    // 0xc69de4: cmp             w2, w0
    // 0xc69de8: b.eq            #0xc69e0c
    // 0xc69dec: StoreField: r1->field_87 = r0
    //     0xc69dec: stur            w0, [x1, #0x87]
    //     0xc69df0: ldurb           w16, [x1, #-1]
    //     0xc69df4: ldurb           w17, [x0, #-1]
    //     0xc69df8: and             x16, x17, x16, lsr #2
    //     0xc69dfc: tst             x16, HEAP, lsr #32
    //     0xc69e00: b.eq            #0xc69e08
    //     0xc69e04: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc69e08: r0 = markNeedsLayout()
    //     0xc69e08: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc69e0c: r0 = Null
    //     0xc69e0c: mov             x0, NULL
    // 0xc69e10: LeaveFrame
    //     0xc69e10: mov             SP, fp
    //     0xc69e14: ldp             fp, lr, [SP], #0x10
    // 0xc69e18: ret
    //     0xc69e18: ret             
    // 0xc69e1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc69e1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc69e20: b               #0xc69ddc
  }
  set _ runSpacing=(/* No info */) {
    // ** addr: 0xc69e24, size: 0x50
    // 0xc69e24: EnterFrame
    //     0xc69e24: stp             fp, lr, [SP, #-0x10]!
    //     0xc69e28: mov             fp, SP
    // 0xc69e2c: CheckStackOverflow
    //     0xc69e2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc69e30: cmp             SP, x16
    //     0xc69e34: b.ls            #0xc69e6c
    // 0xc69e38: LoadField: d1 = r1->field_7b
    //     0xc69e38: ldur            d1, [x1, #0x7b]
    // 0xc69e3c: fcmp            d1, d0
    // 0xc69e40: b.ne            #0xc69e54
    // 0xc69e44: r0 = Null
    //     0xc69e44: mov             x0, NULL
    // 0xc69e48: LeaveFrame
    //     0xc69e48: mov             SP, fp
    //     0xc69e4c: ldp             fp, lr, [SP], #0x10
    // 0xc69e50: ret
    //     0xc69e50: ret             
    // 0xc69e54: StoreField: r1->field_7b = d0
    //     0xc69e54: stur            d0, [x1, #0x7b]
    // 0xc69e58: r0 = markNeedsLayout()
    //     0xc69e58: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc69e5c: r0 = Null
    //     0xc69e5c: mov             x0, NULL
    // 0xc69e60: LeaveFrame
    //     0xc69e60: mov             SP, fp
    //     0xc69e64: ldp             fp, lr, [SP], #0x10
    // 0xc69e68: ret
    //     0xc69e68: ret             
    // 0xc69e6c: r0 = StackOverflowSharedWithFPURegs()
    //     0xc69e6c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc69e70: b               #0xc69e38
  }
}

// class id: 3193, size: 0x18, field offset: 0x18
class WrapParentData extends ContainerBoxParentData<dynamic> {
}

// class id: 6995, size: 0x14, field offset: 0x14
enum WrapCrossAlignment extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  get _ _flipped(/* No info */) {
    // ** addr: 0x7465c8, size: 0x38
    // 0x7465c8: LoadField: r2 = r1->field_7
    //     0x7465c8: ldur            x2, [x1, #7]
    // 0x7465cc: cmp             x2, #1
    // 0x7465d0: b.gt            #0x7465f4
    // 0x7465d4: cmp             x2, #0
    // 0x7465d8: b.gt            #0x7465e8
    // 0x7465dc: r0 = Instance_WrapCrossAlignment
    //     0x7465dc: add             x0, PP, #0x44, lsl #12  ; [pp+0x44bc8] Obj!WrapCrossAlignment@e351e1
    //     0x7465e0: ldr             x0, [x0, #0xbc8]
    // 0x7465e4: b               #0x7465fc
    // 0x7465e8: r0 = Instance_WrapCrossAlignment
    //     0x7465e8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27618] Obj!WrapCrossAlignment@e35201
    //     0x7465ec: ldr             x0, [x0, #0x618]
    // 0x7465f0: b               #0x7465fc
    // 0x7465f4: r0 = Instance_WrapCrossAlignment
    //     0x7465f4: add             x0, PP, #0x44, lsl #12  ; [pp+0x44bd0] Obj!WrapCrossAlignment@e351c1
    //     0x7465f8: ldr             x0, [x0, #0xbd0]
    // 0x7465fc: ret
    //     0x7465fc: ret             
  }
  _ _enumToString(/* No info */) {
    // ** addr: 0xc49d8c, size: 0x64
    // 0xc49d8c: EnterFrame
    //     0xc49d8c: stp             fp, lr, [SP, #-0x10]!
    //     0xc49d90: mov             fp, SP
    // 0xc49d94: AllocStack(0x10)
    //     0xc49d94: sub             SP, SP, #0x10
    // 0xc49d98: SetupParameters(WrapCrossAlignment this /* r1 => r0, fp-0x8 */)
    //     0xc49d98: mov             x0, x1
    //     0xc49d9c: stur            x1, [fp, #-8]
    // 0xc49da0: CheckStackOverflow
    //     0xc49da0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc49da4: cmp             SP, x16
    //     0xc49da8: b.ls            #0xc49de8
    // 0xc49dac: r1 = Null
    //     0xc49dac: mov             x1, NULL
    // 0xc49db0: r2 = 4
    //     0xc49db0: movz            x2, #0x4
    // 0xc49db4: r0 = AllocateArray()
    //     0xc49db4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc49db8: r16 = "WrapCrossAlignment."
    //     0xc49db8: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c78] "WrapCrossAlignment."
    //     0xc49dbc: ldr             x16, [x16, #0xc78]
    // 0xc49dc0: StoreField: r0->field_f = r16
    //     0xc49dc0: stur            w16, [x0, #0xf]
    // 0xc49dc4: ldur            x1, [fp, #-8]
    // 0xc49dc8: LoadField: r2 = r1->field_f
    //     0xc49dc8: ldur            w2, [x1, #0xf]
    // 0xc49dcc: DecompressPointer r2
    //     0xc49dcc: add             x2, x2, HEAP, lsl #32
    // 0xc49dd0: StoreField: r0->field_13 = r2
    //     0xc49dd0: stur            w2, [x0, #0x13]
    // 0xc49dd4: str             x0, [SP]
    // 0xc49dd8: r0 = _interpolate()
    //     0xc49dd8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc49ddc: LeaveFrame
    //     0xc49ddc: mov             SP, fp
    //     0xc49de0: ldp             fp, lr, [SP], #0x10
    // 0xc49de4: ret
    //     0xc49de4: ret             
    // 0xc49de8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc49de8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc49dec: b               #0xc49dac
  }
}

// class id: 6996, size: 0x14, field offset: 0x14
enum WrapAlignment extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _distributeSpace(/* No info */) {
    // ** addr: 0x7461f8, size: 0x3bc
    // 0x7461f8: EnterFrame
    //     0x7461f8: stp             fp, lr, [SP, #-0x10]!
    //     0x7461fc: mov             fp, SP
    // 0x746200: CheckStackOverflow
    //     0x746200: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x746204: cmp             SP, x16
    //     0x746208: b.ls            #0x7464d8
    // 0x74620c: r16 = Instance_WrapAlignment
    //     0x74620c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0x746210: ldr             x16, [x16, #0x610]
    // 0x746214: cmp             w1, w16
    // 0x746218: b.ne            #0x746280
    // 0x74621c: tbz             w3, #4, #0x746224
    // 0x746220: d0 = 0.000000
    //     0x746220: eor             v0.16b, v0.16b, v0.16b
    // 0x746224: r3 = inline_Allocate_Double()
    //     0x746224: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x746228: add             x3, x3, #0x10
    //     0x74622c: cmp             x0, x3
    //     0x746230: b.ls            #0x7464e0
    //     0x746234: str             x3, [THR, #0x50]  ; THR::top
    //     0x746238: sub             x3, x3, #0xf
    //     0x74623c: movz            x0, #0xe15c
    //     0x746240: movk            x0, #0x3, lsl #16
    //     0x746244: stur            x0, [x3, #-1]
    // 0x746248: StoreField: r3->field_7 = d1
    //     0x746248: stur            d1, [x3, #7]
    // 0x74624c: r2 = inline_Allocate_Double()
    //     0x74624c: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x746250: add             x2, x2, #0x10
    //     0x746254: cmp             x0, x2
    //     0x746258: b.ls            #0x7464f4
    //     0x74625c: str             x2, [THR, #0x50]  ; THR::top
    //     0x746260: sub             x2, x2, #0xf
    //     0x746264: movz            x0, #0xe15c
    //     0x746268: movk            x0, #0x3, lsl #16
    //     0x74626c: stur            x0, [x2, #-1]
    // 0x746270: StoreField: r2->field_7 = d0
    //     0x746270: stur            d0, [x2, #7]
    // 0x746274: r0 = AllocateRecord2()
    //     0x746274: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x746278: mov             x2, x0
    // 0x74627c: b               #0x7464bc
    // 0x746280: r16 = Instance_WrapAlignment
    //     0x746280: add             x16, PP, #0x44, lsl #12  ; [pp+0x44ba0] Obj!WrapAlignment@e352a1
    //     0x746284: ldr             x16, [x16, #0xba0]
    // 0x746288: cmp             w1, w16
    // 0x74628c: b.ne            #0x7462b8
    // 0x746290: eor             x0, x3, #0x10
    // 0x746294: mov             x3, x0
    // 0x746298: r1 = Instance_WrapAlignment
    //     0x746298: add             x1, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0x74629c: ldr             x1, [x1, #0x610]
    // 0x7462a0: r0 = _distributeSpace()
    //     0x7462a0: bl              #0x7461f8  ; [package:flutter/src/rendering/wrap.dart] WrapAlignment::_distributeSpace
    // 0x7462a4: mov             x2, x0
    // 0x7462a8: mov             x3, x1
    // 0x7462ac: r0 = AllocateRecord2()
    //     0x7462ac: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x7462b0: mov             x2, x0
    // 0x7462b4: b               #0x7464bc
    // 0x7462b8: r16 = Instance_WrapAlignment
    //     0x7462b8: add             x16, PP, #0x44, lsl #12  ; [pp+0x44ba8] Obj!WrapAlignment@e35281
    //     0x7462bc: ldr             x16, [x16, #0xba8]
    // 0x7462c0: cmp             w1, w16
    // 0x7462c4: r16 = true
    //     0x7462c4: add             x16, NULL, #0x20  ; true
    // 0x7462c8: r17 = false
    //     0x7462c8: add             x17, NULL, #0x30  ; false
    // 0x7462cc: csel            x0, x16, x17, eq
    // 0x7462d0: tbnz            w0, #4, #0x7462fc
    // 0x7462d4: cmp             x2, #2
    // 0x7462d8: b.ge            #0x7462fc
    // 0x7462dc: r1 = Instance_WrapAlignment
    //     0x7462dc: add             x1, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0x7462e0: ldr             x1, [x1, #0x610]
    // 0x7462e4: r0 = _distributeSpace()
    //     0x7462e4: bl              #0x7461f8  ; [package:flutter/src/rendering/wrap.dart] WrapAlignment::_distributeSpace
    // 0x7462e8: mov             x2, x0
    // 0x7462ec: mov             x3, x1
    // 0x7462f0: r0 = AllocateRecord2()
    //     0x7462f0: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x7462f4: mov             x2, x0
    // 0x7462f8: b               #0x7464bc
    // 0x7462fc: r16 = Instance_WrapAlignment
    //     0x7462fc: add             x16, PP, #0x44, lsl #12  ; [pp+0x44bb0] Obj!WrapAlignment@e35261
    //     0x746300: ldr             x16, [x16, #0xbb0]
    // 0x746304: cmp             w1, w16
    // 0x746308: b.ne            #0x746370
    // 0x74630c: d2 = 2.000000
    //     0x74630c: fmov            d2, #2.00000000
    // 0x746310: fdiv            d3, d0, d2
    // 0x746314: r3 = inline_Allocate_Double()
    //     0x746314: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x746318: add             x3, x3, #0x10
    //     0x74631c: cmp             x0, x3
    //     0x746320: b.ls            #0x746510
    //     0x746324: str             x3, [THR, #0x50]  ; THR::top
    //     0x746328: sub             x3, x3, #0xf
    //     0x74632c: movz            x0, #0xe15c
    //     0x746330: movk            x0, #0x3, lsl #16
    //     0x746334: stur            x0, [x3, #-1]
    // 0x746338: StoreField: r3->field_7 = d1
    //     0x746338: stur            d1, [x3, #7]
    // 0x74633c: r2 = inline_Allocate_Double()
    //     0x74633c: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x746340: add             x2, x2, #0x10
    //     0x746344: cmp             x0, x2
    //     0x746348: b.ls            #0x746524
    //     0x74634c: str             x2, [THR, #0x50]  ; THR::top
    //     0x746350: sub             x2, x2, #0xf
    //     0x746354: movz            x0, #0xe15c
    //     0x746358: movk            x0, #0x3, lsl #16
    //     0x74635c: stur            x0, [x2, #-1]
    // 0x746360: StoreField: r2->field_7 = d3
    //     0x746360: stur            d3, [x2, #7]
    // 0x746364: r0 = AllocateRecord2()
    //     0x746364: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x746368: mov             x2, x0
    // 0x74636c: b               #0x7464bc
    // 0x746370: d2 = 2.000000
    //     0x746370: fmov            d2, #2.00000000
    // 0x746374: tbnz            w0, #4, #0x7463c0
    // 0x746378: sub             x0, x2, #1
    // 0x74637c: scvtf           d2, x0
    // 0x746380: fdiv            d3, d0, d2
    // 0x746384: fadd            d0, d3, d1
    // 0x746388: r3 = inline_Allocate_Double()
    //     0x746388: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x74638c: add             x3, x3, #0x10
    //     0x746390: cmp             x0, x3
    //     0x746394: b.ls            #0x746540
    //     0x746398: str             x3, [THR, #0x50]  ; THR::top
    //     0x74639c: sub             x3, x3, #0xf
    //     0x7463a0: movz            x0, #0xe15c
    //     0x7463a4: movk            x0, #0x3, lsl #16
    //     0x7463a8: stur            x0, [x3, #-1]
    // 0x7463ac: StoreField: r3->field_7 = d0
    //     0x7463ac: stur            d0, [x3, #7]
    // 0x7463b0: r2 = 0.000000
    //     0x7463b0: ldr             x2, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x7463b4: r0 = AllocateRecord2()
    //     0x7463b4: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x7463b8: mov             x2, x0
    // 0x7463bc: b               #0x7464bc
    // 0x7463c0: r16 = Instance_WrapAlignment
    //     0x7463c0: add             x16, PP, #0x44, lsl #12  ; [pp+0x44bb8] Obj!WrapAlignment@e35241
    //     0x7463c4: ldr             x16, [x16, #0xbb8]
    // 0x7463c8: cmp             w1, w16
    // 0x7463cc: b.ne            #0x74643c
    // 0x7463d0: scvtf           d3, x2
    // 0x7463d4: fdiv            d4, d0, d3
    // 0x7463d8: fdiv            d0, d4, d2
    // 0x7463dc: fadd            d2, d4, d1
    // 0x7463e0: r2 = inline_Allocate_Double()
    //     0x7463e0: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x7463e4: add             x2, x2, #0x10
    //     0x7463e8: cmp             x0, x2
    //     0x7463ec: b.ls            #0x746554
    //     0x7463f0: str             x2, [THR, #0x50]  ; THR::top
    //     0x7463f4: sub             x2, x2, #0xf
    //     0x7463f8: movz            x0, #0xe15c
    //     0x7463fc: movk            x0, #0x3, lsl #16
    //     0x746400: stur            x0, [x2, #-1]
    // 0x746404: StoreField: r2->field_7 = d0
    //     0x746404: stur            d0, [x2, #7]
    // 0x746408: r3 = inline_Allocate_Double()
    //     0x746408: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x74640c: add             x3, x3, #0x10
    //     0x746410: cmp             x0, x3
    //     0x746414: b.ls            #0x746568
    //     0x746418: str             x3, [THR, #0x50]  ; THR::top
    //     0x74641c: sub             x3, x3, #0xf
    //     0x746420: movz            x0, #0xe15c
    //     0x746424: movk            x0, #0x3, lsl #16
    //     0x746428: stur            x0, [x3, #-1]
    // 0x74642c: StoreField: r3->field_7 = d2
    //     0x74642c: stur            d2, [x3, #7]
    // 0x746430: r0 = AllocateRecord2()
    //     0x746430: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x746434: mov             x2, x0
    // 0x746438: b               #0x7464bc
    // 0x74643c: r16 = Instance_WrapAlignment
    //     0x74643c: add             x16, PP, #0x44, lsl #12  ; [pp+0x44bc0] Obj!WrapAlignment@e35221
    //     0x746440: ldr             x16, [x16, #0xbc0]
    // 0x746444: cmp             w1, w16
    // 0x746448: b.ne            #0x7464b8
    // 0x74644c: add             x0, x2, #1
    // 0x746450: scvtf           d2, x0
    // 0x746454: fdiv            d3, d0, d2
    // 0x746458: fadd            d0, d3, d1
    // 0x74645c: r2 = inline_Allocate_Double()
    //     0x74645c: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x746460: add             x2, x2, #0x10
    //     0x746464: cmp             x0, x2
    //     0x746468: b.ls            #0x746584
    //     0x74646c: str             x2, [THR, #0x50]  ; THR::top
    //     0x746470: sub             x2, x2, #0xf
    //     0x746474: movz            x0, #0xe15c
    //     0x746478: movk            x0, #0x3, lsl #16
    //     0x74647c: stur            x0, [x2, #-1]
    // 0x746480: StoreField: r2->field_7 = d3
    //     0x746480: stur            d3, [x2, #7]
    // 0x746484: r3 = inline_Allocate_Double()
    //     0x746484: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x746488: add             x3, x3, #0x10
    //     0x74648c: cmp             x0, x3
    //     0x746490: b.ls            #0x746598
    //     0x746494: str             x3, [THR, #0x50]  ; THR::top
    //     0x746498: sub             x3, x3, #0xf
    //     0x74649c: movz            x0, #0xe15c
    //     0x7464a0: movk            x0, #0x3, lsl #16
    //     0x7464a4: stur            x0, [x3, #-1]
    // 0x7464a8: StoreField: r3->field_7 = d0
    //     0x7464a8: stur            d0, [x3, #7]
    // 0x7464ac: r0 = AllocateRecord2()
    //     0x7464ac: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x7464b0: mov             x2, x0
    // 0x7464b4: b               #0x7464bc
    // 0x7464b8: r2 = Null
    //     0x7464b8: mov             x2, NULL
    // 0x7464bc: LoadField: r0 = r2->field_f
    //     0x7464bc: ldur            w0, [x2, #0xf]
    // 0x7464c0: DecompressPointer r0
    //     0x7464c0: add             x0, x0, HEAP, lsl #32
    // 0x7464c4: LoadField: r1 = r2->field_13
    //     0x7464c4: ldur            w1, [x2, #0x13]
    // 0x7464c8: DecompressPointer r1
    //     0x7464c8: add             x1, x1, HEAP, lsl #32
    // 0x7464cc: LeaveFrame
    //     0x7464cc: mov             SP, fp
    //     0x7464d0: ldp             fp, lr, [SP], #0x10
    // 0x7464d4: ret
    //     0x7464d4: ret             
    // 0x7464d8: r0 = StackOverflowSharedWithFPURegs()
    //     0x7464d8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7464dc: b               #0x74620c
    // 0x7464e0: stp             q0, q1, [SP, #-0x20]!
    // 0x7464e4: r0 = AllocateDouble()
    //     0x7464e4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7464e8: mov             x3, x0
    // 0x7464ec: ldp             q0, q1, [SP], #0x20
    // 0x7464f0: b               #0x746248
    // 0x7464f4: SaveReg d0
    //     0x7464f4: str             q0, [SP, #-0x10]!
    // 0x7464f8: SaveReg r3
    //     0x7464f8: str             x3, [SP, #-8]!
    // 0x7464fc: r0 = AllocateDouble()
    //     0x7464fc: bl              #0xec2254  ; AllocateDoubleStub
    // 0x746500: mov             x2, x0
    // 0x746504: RestoreReg r3
    //     0x746504: ldr             x3, [SP], #8
    // 0x746508: RestoreReg d0
    //     0x746508: ldr             q0, [SP], #0x10
    // 0x74650c: b               #0x746270
    // 0x746510: stp             q1, q3, [SP, #-0x20]!
    // 0x746514: r0 = AllocateDouble()
    //     0x746514: bl              #0xec2254  ; AllocateDoubleStub
    // 0x746518: mov             x3, x0
    // 0x74651c: ldp             q1, q3, [SP], #0x20
    // 0x746520: b               #0x746338
    // 0x746524: SaveReg d3
    //     0x746524: str             q3, [SP, #-0x10]!
    // 0x746528: SaveReg r3
    //     0x746528: str             x3, [SP, #-8]!
    // 0x74652c: r0 = AllocateDouble()
    //     0x74652c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x746530: mov             x2, x0
    // 0x746534: RestoreReg r3
    //     0x746534: ldr             x3, [SP], #8
    // 0x746538: RestoreReg d3
    //     0x746538: ldr             q3, [SP], #0x10
    // 0x74653c: b               #0x746360
    // 0x746540: SaveReg d0
    //     0x746540: str             q0, [SP, #-0x10]!
    // 0x746544: r0 = AllocateDouble()
    //     0x746544: bl              #0xec2254  ; AllocateDoubleStub
    // 0x746548: mov             x3, x0
    // 0x74654c: RestoreReg d0
    //     0x74654c: ldr             q0, [SP], #0x10
    // 0x746550: b               #0x7463ac
    // 0x746554: stp             q0, q2, [SP, #-0x20]!
    // 0x746558: r0 = AllocateDouble()
    //     0x746558: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74655c: mov             x2, x0
    // 0x746560: ldp             q0, q2, [SP], #0x20
    // 0x746564: b               #0x746404
    // 0x746568: SaveReg d2
    //     0x746568: str             q2, [SP, #-0x10]!
    // 0x74656c: SaveReg r2
    //     0x74656c: str             x2, [SP, #-8]!
    // 0x746570: r0 = AllocateDouble()
    //     0x746570: bl              #0xec2254  ; AllocateDoubleStub
    // 0x746574: mov             x3, x0
    // 0x746578: RestoreReg r2
    //     0x746578: ldr             x2, [SP], #8
    // 0x74657c: RestoreReg d2
    //     0x74657c: ldr             q2, [SP], #0x10
    // 0x746580: b               #0x74642c
    // 0x746584: stp             q0, q3, [SP, #-0x20]!
    // 0x746588: r0 = AllocateDouble()
    //     0x746588: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74658c: mov             x2, x0
    // 0x746590: ldp             q0, q3, [SP], #0x20
    // 0x746594: b               #0x746480
    // 0x746598: SaveReg d0
    //     0x746598: str             q0, [SP, #-0x10]!
    // 0x74659c: SaveReg r2
    //     0x74659c: str             x2, [SP, #-8]!
    // 0x7465a0: r0 = AllocateDouble()
    //     0x7465a0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7465a4: mov             x3, x0
    // 0x7465a8: RestoreReg r2
    //     0x7465a8: ldr             x2, [SP], #8
    // 0x7465ac: RestoreReg d0
    //     0x7465ac: ldr             q0, [SP], #0x10
    // 0x7465b0: b               #0x7464a8
  }
  _ _enumToString(/* No info */) {
    // ** addr: 0xc49d28, size: 0x64
    // 0xc49d28: EnterFrame
    //     0xc49d28: stp             fp, lr, [SP, #-0x10]!
    //     0xc49d2c: mov             fp, SP
    // 0xc49d30: AllocStack(0x10)
    //     0xc49d30: sub             SP, SP, #0x10
    // 0xc49d34: SetupParameters(WrapAlignment this /* r1 => r0, fp-0x8 */)
    //     0xc49d34: mov             x0, x1
    //     0xc49d38: stur            x1, [fp, #-8]
    // 0xc49d3c: CheckStackOverflow
    //     0xc49d3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc49d40: cmp             SP, x16
    //     0xc49d44: b.ls            #0xc49d84
    // 0xc49d48: r1 = Null
    //     0xc49d48: mov             x1, NULL
    // 0xc49d4c: r2 = 4
    //     0xc49d4c: movz            x2, #0x4
    // 0xc49d50: r0 = AllocateArray()
    //     0xc49d50: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc49d54: r16 = "WrapAlignment."
    //     0xc49d54: add             x16, PP, #0x39, lsl #12  ; [pp+0x39c80] "WrapAlignment."
    //     0xc49d58: ldr             x16, [x16, #0xc80]
    // 0xc49d5c: StoreField: r0->field_f = r16
    //     0xc49d5c: stur            w16, [x0, #0xf]
    // 0xc49d60: ldur            x1, [fp, #-8]
    // 0xc49d64: LoadField: r2 = r1->field_f
    //     0xc49d64: ldur            w2, [x1, #0xf]
    // 0xc49d68: DecompressPointer r2
    //     0xc49d68: add             x2, x2, HEAP, lsl #32
    // 0xc49d6c: StoreField: r0->field_13 = r2
    //     0xc49d6c: stur            w2, [x0, #0x13]
    // 0xc49d70: str             x0, [SP]
    // 0xc49d74: r0 = _interpolate()
    //     0xc49d74: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc49d78: LeaveFrame
    //     0xc49d78: mov             SP, fp
    //     0xc49d7c: ldp             fp, lr, [SP], #0x10
    // 0xc49d80: ret
    //     0xc49d80: ret             
    // 0xc49d84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc49d84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc49d88: b               #0xc49d48
  }
}
