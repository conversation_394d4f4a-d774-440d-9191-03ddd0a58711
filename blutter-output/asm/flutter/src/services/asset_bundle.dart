// lib: , url: package:flutter/src/services/asset_bundle.dart

// class id: 1049063, size: 0x8
class :: {

  static late final AssetBundle rootBundle; // offset: 0x698

  static _ _errorSummaryWithKey(/* No info */) {
    // ** addr: 0x6982a8, size: 0x84
    // 0x6982a8: EnterFrame
    //     0x6982a8: stp             fp, lr, [SP, #-0x10]!
    //     0x6982ac: mov             fp, SP
    // 0x6982b0: AllocStack(0x10)
    //     0x6982b0: sub             SP, SP, #0x10
    // 0x6982b4: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x6982b4: mov             x0, x1
    //     0x6982b8: stur            x1, [fp, #-8]
    // 0x6982bc: CheckStackOverflow
    //     0x6982bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6982c0: cmp             SP, x16
    //     0x6982c4: b.ls            #0x698324
    // 0x6982c8: r1 = Null
    //     0x6982c8: mov             x1, NULL
    // 0x6982cc: r2 = 6
    //     0x6982cc: movz            x2, #0x6
    // 0x6982d0: r0 = AllocateArray()
    //     0x6982d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6982d4: r16 = "Unable to load asset: \""
    //     0x6982d4: ldr             x16, [PP, #0x3620]  ; [pp+0x3620] "Unable to load asset: \""
    // 0x6982d8: StoreField: r0->field_f = r16
    //     0x6982d8: stur            w16, [x0, #0xf]
    // 0x6982dc: ldur            x1, [fp, #-8]
    // 0x6982e0: StoreField: r0->field_13 = r1
    //     0x6982e0: stur            w1, [x0, #0x13]
    // 0x6982e4: r16 = "\"."
    //     0x6982e4: ldr             x16, [PP, #0x378]  ; [pp+0x378] "\"."
    // 0x6982e8: ArrayStore: r0[0] = r16  ; List_4
    //     0x6982e8: stur            w16, [x0, #0x17]
    // 0x6982ec: str             x0, [SP]
    // 0x6982f0: r0 = _interpolate()
    //     0x6982f0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x6982f4: r1 = <List<Object>>
    //     0x6982f4: ldr             x1, [PP, #0x440]  ; [pp+0x440] TypeArguments: <List<Object>>
    // 0x6982f8: stur            x0, [fp, #-8]
    // 0x6982fc: r0 = ErrorSummary()
    //     0x6982fc: bl              #0x6804cc  ; AllocateErrorSummaryStub -> ErrorSummary (size=0x2c)
    // 0x698300: mov             x1, x0
    // 0x698304: ldur            x2, [fp, #-8]
    // 0x698308: r3 = Instance_DiagnosticLevel
    //     0x698308: ldr             x3, [PP, #0x2748]  ; [pp+0x2748] Obj!DiagnosticLevel@e37041
    // 0x69830c: stur            x0, [fp, #-8]
    // 0x698310: r0 = _ErrorDiagnostic()
    //     0x698310: bl              #0x644784  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::_ErrorDiagnostic
    // 0x698314: ldur            x0, [fp, #-8]
    // 0x698318: LeaveFrame
    //     0x698318: mov             SP, fp
    //     0x69831c: ldp             fp, lr, [SP], #0x10
    // 0x698320: ret
    //     0x698320: ret             
    // 0x698324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x698324: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x698328: b               #0x6982c8
  }
  static AssetBundle rootBundle() {
    // ** addr: 0x69878c, size: 0x2c
    // 0x69878c: EnterFrame
    //     0x69878c: stp             fp, lr, [SP, #-0x10]!
    //     0x698790: mov             fp, SP
    // 0x698794: CheckStackOverflow
    //     0x698794: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x698798: cmp             SP, x16
    //     0x69879c: b.ls            #0x6987b0
    // 0x6987a0: r0 = _initRootBundle()
    //     0x6987a0: bl              #0x6987b8  ; [package:flutter/src/services/asset_bundle.dart] ::_initRootBundle
    // 0x6987a4: LeaveFrame
    //     0x6987a4: mov             SP, fp
    //     0x6987a8: ldp             fp, lr, [SP], #0x10
    // 0x6987ac: ret
    //     0x6987ac: ret             
    // 0x6987b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6987b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6987b4: b               #0x6987a0
  }
  static AssetBundle _initRootBundle() {
    // ** addr: 0x6987b8, size: 0x40
    // 0x6987b8: EnterFrame
    //     0x6987b8: stp             fp, lr, [SP, #-0x10]!
    //     0x6987bc: mov             fp, SP
    // 0x6987c0: AllocStack(0x8)
    //     0x6987c0: sub             SP, SP, #8
    // 0x6987c4: CheckStackOverflow
    //     0x6987c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6987c8: cmp             SP, x16
    //     0x6987cc: b.ls            #0x6987f0
    // 0x6987d0: r0 = PlatformAssetBundle()
    //     0x6987d0: bl              #0x698954  ; AllocatePlatformAssetBundleStub -> PlatformAssetBundle (size=0x14)
    // 0x6987d4: mov             x1, x0
    // 0x6987d8: stur            x0, [fp, #-8]
    // 0x6987dc: r0 = CachingAssetBundle()
    //     0x6987dc: bl              #0x6987f8  ; [package:flutter/src/services/asset_bundle.dart] CachingAssetBundle::CachingAssetBundle
    // 0x6987e0: ldur            x0, [fp, #-8]
    // 0x6987e4: LeaveFrame
    //     0x6987e4: mov             SP, fp
    //     0x6987e8: ldp             fp, lr, [SP], #0x10
    // 0x6987ec: ret
    //     0x6987ec: ret             
    // 0x6987f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6987f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6987f4: b               #0x6987d0
  }
}

// class id: 2826, size: 0x8, field offset: 0x8
abstract class AssetBundle extends Object {

  _ loadString(/* No info */) async {
    // ** addr: 0x72be50, size: 0xe4
    // 0x72be50: EnterFrame
    //     0x72be50: stp             fp, lr, [SP, #-0x10]!
    //     0x72be54: mov             fp, SP
    // 0x72be58: AllocStack(0x38)
    //     0x72be58: sub             SP, SP, #0x38
    // 0x72be5c: SetupParameters(AssetBundle this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x72be5c: stur            NULL, [fp, #-8]
    //     0x72be60: stur            x1, [fp, #-0x10]
    //     0x72be64: stur            x2, [fp, #-0x18]
    // 0x72be68: CheckStackOverflow
    //     0x72be68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72be6c: cmp             SP, x16
    //     0x72be70: b.ls            #0x72bf2c
    // 0x72be74: InitAsync() -> Future<String>
    //     0x72be74: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    //     0x72be78: bl              #0x661298  ; InitAsyncStub
    // 0x72be7c: ldur            x1, [fp, #-0x10]
    // 0x72be80: ldur            x2, [fp, #-0x18]
    // 0x72be84: r0 = load()
    //     0x72be84: bl              #0x698024  ; [package:flutter/src/services/asset_bundle.dart] PlatformAssetBundle::load
    // 0x72be88: mov             x1, x0
    // 0x72be8c: stur            x1, [fp, #-0x10]
    // 0x72be90: r0 = Await()
    //     0x72be90: bl              #0x661044  ; AwaitStub
    // 0x72be94: stur            x0, [fp, #-0x10]
    // 0x72be98: LoadField: r1 = r0->field_13
    //     0x72be98: ldur            w1, [x0, #0x13]
    // 0x72be9c: r2 = LoadInt32Instr(r1)
    //     0x72be9c: sbfx            x2, x1, #1, #0x1f
    // 0x72bea0: r17 = 51200
    //     0x72bea0: movz            x17, #0xc800
    // 0x72bea4: cmp             x2, x17
    // 0x72bea8: b.ge            #0x72bed0
    // 0x72beac: mov             x2, x0
    // 0x72beb0: r1 = Null
    //     0x72beb0: mov             x1, NULL
    // 0x72beb4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x72beb4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x72beb8: r0 = Uint8List.sublistView()
    //     0x72beb8: bl              #0x72bf34  ; [dart:typed_data] Uint8List::Uint8List.sublistView
    // 0x72bebc: mov             x2, x0
    // 0x72bec0: r1 = Instance_Utf8Codec
    //     0x72bec0: ldr             x1, [PP, #0x200]  ; [pp+0x200] Obj!Utf8Codec@e2ccf1
    // 0x72bec4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x72bec4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x72bec8: r0 = decode()
    //     0x72bec8: bl              #0x60b038  ; [dart:convert] Utf8Codec::decode
    // 0x72becc: r0 = ReturnAsyncNotFuture()
    //     0x72becc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x72bed0: ldur            x3, [fp, #-0x18]
    // 0x72bed4: r1 = Null
    //     0x72bed4: mov             x1, NULL
    // 0x72bed8: r2 = 6
    //     0x72bed8: movz            x2, #0x6
    // 0x72bedc: r0 = AllocateArray()
    //     0x72bedc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x72bee0: r16 = "UTF8 decode for \""
    //     0x72bee0: add             x16, PP, #9, lsl #12  ; [pp+0x9430] "UTF8 decode for \""
    //     0x72bee4: ldr             x16, [x16, #0x430]
    // 0x72bee8: StoreField: r0->field_f = r16
    //     0x72bee8: stur            w16, [x0, #0xf]
    // 0x72beec: ldur            x1, [fp, #-0x18]
    // 0x72bef0: StoreField: r0->field_13 = r1
    //     0x72bef0: stur            w1, [x0, #0x13]
    // 0x72bef4: r16 = "\""
    //     0x72bef4: ldr             x16, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0x72bef8: ArrayStore: r0[0] = r16  ; List_4
    //     0x72bef8: stur            w16, [x0, #0x17]
    // 0x72befc: str             x0, [SP]
    // 0x72bf00: r0 = _interpolate()
    //     0x72bf00: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x72bf04: r16 = <ByteData, String>
    //     0x72bf04: add             x16, PP, #9, lsl #12  ; [pp+0x9438] TypeArguments: <ByteData, String>
    //     0x72bf08: ldr             x16, [x16, #0x438]
    // 0x72bf0c: r30 = Closure: (ByteData) => String from Function '_utf8decode@53177032': static.
    //     0x72bf0c: add             lr, PP, #9, lsl #12  ; [pp+0x9440] Closure: (ByteData) => String from Function '_utf8decode@53177032': static. (0x7e54fb12c06c)
    //     0x72bf10: ldr             lr, [lr, #0x440]
    // 0x72bf14: stp             lr, x16, [SP, #0x10]
    // 0x72bf18: ldur            x16, [fp, #-0x10]
    // 0x72bf1c: stp             x0, x16, [SP]
    // 0x72bf20: r4 = const [0x2, 0x3, 0x3, 0x3, null]
    //     0x72bf20: ldr             x4, [PP, #0x3310]  ; [pp+0x3310] List(5) [0x2, 0x3, 0x3, 0x3, Null]
    // 0x72bf24: r0 = compute()
    //     0x72bf24: bl              #0x696ccc  ; [package:flutter/src/foundation/_isolates_io.dart] ::compute
    // 0x72bf28: r0 = ReturnAsync()
    //     0x72bf28: b               #0x6576a4  ; ReturnAsyncStub
    // 0x72bf2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72bf2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72bf30: b               #0x72be74
  }
  [closure] static String _utf8decode(dynamic, ByteData) {
    // ** addr: 0x72c06c, size: 0x30
    // 0x72c06c: EnterFrame
    //     0x72c06c: stp             fp, lr, [SP, #-0x10]!
    //     0x72c070: mov             fp, SP
    // 0x72c074: CheckStackOverflow
    //     0x72c074: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72c078: cmp             SP, x16
    //     0x72c07c: b.ls            #0x72c094
    // 0x72c080: ldr             x1, [fp, #0x10]
    // 0x72c084: r0 = _utf8decode()
    //     0x72c084: bl              #0x72c09c  ; [package:flutter/src/services/asset_bundle.dart] AssetBundle::_utf8decode
    // 0x72c088: LeaveFrame
    //     0x72c088: mov             SP, fp
    //     0x72c08c: ldp             fp, lr, [SP], #0x10
    // 0x72c090: ret
    //     0x72c090: ret             
    // 0x72c094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72c094: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72c098: b               #0x72c080
  }
  static _ _utf8decode(/* No info */) {
    // ** addr: 0x72c09c, size: 0x48
    // 0x72c09c: EnterFrame
    //     0x72c09c: stp             fp, lr, [SP, #-0x10]!
    //     0x72c0a0: mov             fp, SP
    // 0x72c0a4: mov             x2, x1
    // 0x72c0a8: CheckStackOverflow
    //     0x72c0a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72c0ac: cmp             SP, x16
    //     0x72c0b0: b.ls            #0x72c0dc
    // 0x72c0b4: r1 = Null
    //     0x72c0b4: mov             x1, NULL
    // 0x72c0b8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x72c0b8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x72c0bc: r0 = Uint8List.sublistView()
    //     0x72c0bc: bl              #0x72bf34  ; [dart:typed_data] Uint8List::Uint8List.sublistView
    // 0x72c0c0: mov             x2, x0
    // 0x72c0c4: r1 = Instance_Utf8Codec
    //     0x72c0c4: ldr             x1, [PP, #0x200]  ; [pp+0x200] Obj!Utf8Codec@e2ccf1
    // 0x72c0c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x72c0c8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x72c0cc: r0 = decode()
    //     0x72c0cc: bl              #0x60b038  ; [dart:convert] Utf8Codec::decode
    // 0x72c0d0: LeaveFrame
    //     0x72c0d0: mov             SP, fp
    //     0x72c0d4: ldp             fp, lr, [SP], #0x10
    // 0x72c0d8: ret
    //     0x72c0d8: ret             
    // 0x72c0dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72c0dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72c0e0: b               #0x72c0b4
  }
}

// class id: 2827, size: 0x14, field offset: 0x8
abstract class CachingAssetBundle extends AssetBundle {

  _ CachingAssetBundle(/* No info */) {
    // ** addr: 0x6987f8, size: 0xc4
    // 0x6987f8: EnterFrame
    //     0x6987f8: stp             fp, lr, [SP, #-0x10]!
    //     0x6987fc: mov             fp, SP
    // 0x698800: AllocStack(0x18)
    //     0x698800: sub             SP, SP, #0x18
    // 0x698804: SetupParameters(CachingAssetBundle this /* r1 => r1, fp-0x8 */)
    //     0x698804: stur            x1, [fp, #-8]
    // 0x698808: CheckStackOverflow
    //     0x698808: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x69880c: cmp             SP, x16
    //     0x698810: b.ls            #0x6988b4
    // 0x698814: r16 = <String, Future<String>>
    //     0x698814: ldr             x16, [PP, #0x3268]  ; [pp+0x3268] TypeArguments: <String, Future<String>>
    // 0x698818: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x69881c: stp             lr, x16, [SP]
    // 0x698820: r0 = Map._fromLiteral()
    //     0x698820: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x698824: ldur            x1, [fp, #-8]
    // 0x698828: StoreField: r1->field_7 = r0
    //     0x698828: stur            w0, [x1, #7]
    //     0x69882c: ldurb           w16, [x1, #-1]
    //     0x698830: ldurb           w17, [x0, #-1]
    //     0x698834: and             x16, x17, x16, lsr #2
    //     0x698838: tst             x16, HEAP, lsr #32
    //     0x69883c: b.eq            #0x698844
    //     0x698840: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x698844: r16 = <String, Future>
    //     0x698844: ldr             x16, [PP, #0x3270]  ; [pp+0x3270] TypeArguments: <String, Future>
    // 0x698848: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x69884c: stp             lr, x16, [SP]
    // 0x698850: r0 = Map._fromLiteral()
    //     0x698850: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x698854: ldur            x1, [fp, #-8]
    // 0x698858: StoreField: r1->field_b = r0
    //     0x698858: stur            w0, [x1, #0xb]
    //     0x69885c: ldurb           w16, [x1, #-1]
    //     0x698860: ldurb           w17, [x0, #-1]
    //     0x698864: and             x16, x17, x16, lsr #2
    //     0x698868: tst             x16, HEAP, lsr #32
    //     0x69886c: b.eq            #0x698874
    //     0x698870: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x698874: r16 = <String, Future>
    //     0x698874: ldr             x16, [PP, #0x3270]  ; [pp+0x3270] TypeArguments: <String, Future>
    // 0x698878: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x69887c: stp             lr, x16, [SP]
    // 0x698880: r0 = Map._fromLiteral()
    //     0x698880: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x698884: ldur            x1, [fp, #-8]
    // 0x698888: StoreField: r1->field_f = r0
    //     0x698888: stur            w0, [x1, #0xf]
    //     0x69888c: ldurb           w16, [x1, #-1]
    //     0x698890: ldurb           w17, [x0, #-1]
    //     0x698894: and             x16, x17, x16, lsr #2
    //     0x698898: tst             x16, HEAP, lsr #32
    //     0x69889c: b.eq            #0x6988a4
    //     0x6988a0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6988a4: r0 = Null
    //     0x6988a4: mov             x0, NULL
    // 0x6988a8: LeaveFrame
    //     0x6988a8: mov             SP, fp
    //     0x6988ac: ldp             fp, lr, [SP], #0x10
    // 0x6988b0: ret
    //     0x6988b0: ret             
    // 0x6988b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6988b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6988b8: b               #0x698814
  }
  _ clear(/* No info */) {
    // ** addr: 0x6bdfe4, size: 0x64
    // 0x6bdfe4: EnterFrame
    //     0x6bdfe4: stp             fp, lr, [SP, #-0x10]!
    //     0x6bdfe8: mov             fp, SP
    // 0x6bdfec: AllocStack(0x8)
    //     0x6bdfec: sub             SP, SP, #8
    // 0x6bdff0: SetupParameters(CachingAssetBundle this /* r1 => r0, fp-0x8 */)
    //     0x6bdff0: mov             x0, x1
    //     0x6bdff4: stur            x1, [fp, #-8]
    // 0x6bdff8: CheckStackOverflow
    //     0x6bdff8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6bdffc: cmp             SP, x16
    //     0x6be000: b.ls            #0x6be040
    // 0x6be004: LoadField: r1 = r0->field_7
    //     0x6be004: ldur            w1, [x0, #7]
    // 0x6be008: DecompressPointer r1
    //     0x6be008: add             x1, x1, HEAP, lsl #32
    // 0x6be00c: r0 = clear()
    //     0x6be00c: bl              #0x623ad0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x6be010: ldur            x0, [fp, #-8]
    // 0x6be014: LoadField: r1 = r0->field_b
    //     0x6be014: ldur            w1, [x0, #0xb]
    // 0x6be018: DecompressPointer r1
    //     0x6be018: add             x1, x1, HEAP, lsl #32
    // 0x6be01c: r0 = clear()
    //     0x6be01c: bl              #0x623ad0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x6be020: ldur            x0, [fp, #-8]
    // 0x6be024: LoadField: r1 = r0->field_f
    //     0x6be024: ldur            w1, [x0, #0xf]
    // 0x6be028: DecompressPointer r1
    //     0x6be028: add             x1, x1, HEAP, lsl #32
    // 0x6be02c: r0 = clear()
    //     0x6be02c: bl              #0x623ad0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x6be030: r0 = Null
    //     0x6be030: mov             x0, NULL
    // 0x6be034: LeaveFrame
    //     0x6be034: mov             SP, fp
    //     0x6be038: ldp             fp, lr, [SP], #0x10
    // 0x6be03c: ret
    //     0x6be03c: ret             
    // 0x6be040: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6be040: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6be044: b               #0x6be004
  }
  _ loadString(/* No info */) {
    // ** addr: 0x72bd88, size: 0x7c
    // 0x72bd88: EnterFrame
    //     0x72bd88: stp             fp, lr, [SP, #-0x10]!
    //     0x72bd8c: mov             fp, SP
    // 0x72bd90: AllocStack(0x18)
    //     0x72bd90: sub             SP, SP, #0x18
    // 0x72bd94: SetupParameters(CachingAssetBundle this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x72bd94: stur            x1, [fp, #-8]
    //     0x72bd98: stur            x2, [fp, #-0x10]
    // 0x72bd9c: CheckStackOverflow
    //     0x72bd9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72bda0: cmp             SP, x16
    //     0x72bda4: b.ls            #0x72bdfc
    // 0x72bda8: r1 = 2
    //     0x72bda8: movz            x1, #0x2
    // 0x72bdac: r0 = AllocateContext()
    //     0x72bdac: bl              #0xec126c  ; AllocateContextStub
    // 0x72bdb0: mov             x1, x0
    // 0x72bdb4: ldur            x0, [fp, #-8]
    // 0x72bdb8: StoreField: r1->field_f = r0
    //     0x72bdb8: stur            w0, [x1, #0xf]
    // 0x72bdbc: ldur            x3, [fp, #-0x10]
    // 0x72bdc0: StoreField: r1->field_13 = r3
    //     0x72bdc0: stur            w3, [x1, #0x13]
    // 0x72bdc4: LoadField: r4 = r0->field_7
    //     0x72bdc4: ldur            w4, [x0, #7]
    // 0x72bdc8: DecompressPointer r4
    //     0x72bdc8: add             x4, x4, HEAP, lsl #32
    // 0x72bdcc: mov             x2, x1
    // 0x72bdd0: stur            x4, [fp, #-0x18]
    // 0x72bdd4: r1 = Function '<anonymous closure>':.
    //     0x72bdd4: add             x1, PP, #9, lsl #12  ; [pp+0x9428] AnonymousClosure: (0x72be04), in [package:flutter/src/services/asset_bundle.dart] CachingAssetBundle::loadString (0x72bd88)
    //     0x72bdd8: ldr             x1, [x1, #0x428]
    // 0x72bddc: r0 = AllocateClosure()
    //     0x72bddc: bl              #0xec1630  ; AllocateClosureStub
    // 0x72bde0: ldur            x1, [fp, #-0x18]
    // 0x72bde4: ldur            x2, [fp, #-0x10]
    // 0x72bde8: mov             x3, x0
    // 0x72bdec: r0 = putIfAbsent()
    //     0x72bdec: bl              #0x7661b4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::putIfAbsent
    // 0x72bdf0: LeaveFrame
    //     0x72bdf0: mov             SP, fp
    //     0x72bdf4: ldp             fp, lr, [SP], #0x10
    // 0x72bdf8: ret
    //     0x72bdf8: ret             
    // 0x72bdfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72bdfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72be00: b               #0x72bda8
  }
  [closure] Future<String> <anonymous closure>(dynamic) {
    // ** addr: 0x72be04, size: 0x4c
    // 0x72be04: EnterFrame
    //     0x72be04: stp             fp, lr, [SP, #-0x10]!
    //     0x72be08: mov             fp, SP
    // 0x72be0c: ldr             x0, [fp, #0x10]
    // 0x72be10: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x72be10: ldur            w1, [x0, #0x17]
    // 0x72be14: DecompressPointer r1
    //     0x72be14: add             x1, x1, HEAP, lsl #32
    // 0x72be18: CheckStackOverflow
    //     0x72be18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72be1c: cmp             SP, x16
    //     0x72be20: b.ls            #0x72be48
    // 0x72be24: LoadField: r0 = r1->field_f
    //     0x72be24: ldur            w0, [x1, #0xf]
    // 0x72be28: DecompressPointer r0
    //     0x72be28: add             x0, x0, HEAP, lsl #32
    // 0x72be2c: LoadField: r2 = r1->field_13
    //     0x72be2c: ldur            w2, [x1, #0x13]
    // 0x72be30: DecompressPointer r2
    //     0x72be30: add             x2, x2, HEAP, lsl #32
    // 0x72be34: mov             x1, x0
    // 0x72be38: r0 = loadString()
    //     0x72be38: bl              #0x72be50  ; [package:flutter/src/services/asset_bundle.dart] AssetBundle::loadString
    // 0x72be3c: LeaveFrame
    //     0x72be3c: mov             SP, fp
    //     0x72be40: ldp             fp, lr, [SP], #0x10
    // 0x72be44: ret
    //     0x72be44: ret             
    // 0x72be48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72be48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72be4c: b               #0x72be24
  }
  Future<Y0> loadStructuredBinaryData<Y0>(CachingAssetBundle, String, (dynamic, ByteData) => FutureOr<Y0>) {
    // ** addr: 0xd342c0, size: 0x2f0
    // 0xd342c0: EnterFrame
    //     0xd342c0: stp             fp, lr, [SP, #-0x10]!
    //     0xd342c4: mov             fp, SP
    // 0xd342c8: AllocStack(0x48)
    //     0xd342c8: sub             SP, SP, #0x48
    // 0xd342cc: SetupParameters()
    //     0xd342cc: ldur            w0, [x4, #0xf]
    //     0xd342d0: cbnz            w0, #0xd342dc
    //     0xd342d4: mov             x0, NULL
    //     0xd342d8: b               #0xd342ec
    //     0xd342dc: ldur            w0, [x4, #0x17]
    //     0xd342e0: add             x1, fp, w0, sxtw #2
    //     0xd342e4: ldr             x1, [x1, #0x10]
    //     0xd342e8: mov             x0, x1
    //     0xd342ec: ldr             x1, [fp, #0x20]
    //     0xd342f0: stur            x0, [fp, #-8]
    // 0xd342f4: CheckStackOverflow
    //     0xd342f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd342f8: cmp             SP, x16
    //     0xd342fc: b.ls            #0xd345a0
    // 0xd34300: r1 = 4
    //     0xd34300: movz            x1, #0x4
    // 0xd34304: r0 = AllocateContext()
    //     0xd34304: bl              #0xec126c  ; AllocateContextStub
    // 0xd34308: mov             x3, x0
    // 0xd3430c: ldr             x0, [fp, #0x20]
    // 0xd34310: stur            x3, [fp, #-0x18]
    // 0xd34314: StoreField: r3->field_f = r0
    //     0xd34314: stur            w0, [x3, #0xf]
    // 0xd34318: r2 = "AssetManifest.bin"
    //     0xd34318: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1ce40] "AssetManifest.bin"
    //     0xd3431c: ldr             x2, [x2, #0xe40]
    // 0xd34320: StoreField: r3->field_13 = r2
    //     0xd34320: stur            w2, [x3, #0x13]
    // 0xd34324: LoadField: r4 = r0->field_f
    //     0xd34324: ldur            w4, [x0, #0xf]
    // 0xd34328: DecompressPointer r4
    //     0xd34328: add             x4, x4, HEAP, lsl #32
    // 0xd3432c: mov             x1, x4
    // 0xd34330: stur            x4, [fp, #-0x10]
    // 0xd34334: r0 = containsKey()
    //     0xd34334: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xd34338: tbnz            w0, #4, #0xd343b4
    // 0xd3433c: ldur            x0, [fp, #-0x18]
    // 0xd34340: ldur            x3, [fp, #-0x10]
    // 0xd34344: LoadField: r2 = r0->field_13
    //     0xd34344: ldur            w2, [x0, #0x13]
    // 0xd34348: DecompressPointer r2
    //     0xd34348: add             x2, x2, HEAP, lsl #32
    // 0xd3434c: mov             x1, x3
    // 0xd34350: r0 = _getValueOrData()
    //     0xd34350: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xd34354: ldur            x3, [fp, #-0x10]
    // 0xd34358: LoadField: r1 = r3->field_f
    //     0xd34358: ldur            w1, [x3, #0xf]
    // 0xd3435c: DecompressPointer r1
    //     0xd3435c: add             x1, x1, HEAP, lsl #32
    // 0xd34360: cmp             w1, w0
    // 0xd34364: b.ne            #0xd34370
    // 0xd34368: r3 = Null
    //     0xd34368: mov             x3, NULL
    // 0xd3436c: b               #0xd34374
    // 0xd34370: mov             x3, x0
    // 0xd34374: stur            x3, [fp, #-0x20]
    // 0xd34378: cmp             w3, NULL
    // 0xd3437c: b.eq            #0xd345a8
    // 0xd34380: mov             x0, x3
    // 0xd34384: ldur            x1, [fp, #-8]
    // 0xd34388: r2 = Null
    //     0xd34388: mov             x2, NULL
    // 0xd3438c: r8 = Future<Y0>
    //     0xd3438c: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1ce68] Type: Future<Y0>
    //     0xd34390: ldr             x8, [x8, #0xe68]
    // 0xd34394: LoadField: r9 = r8->field_7
    //     0xd34394: ldur            x9, [x8, #7]
    // 0xd34398: r3 = Null
    //     0xd34398: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1ce70] Null
    //     0xd3439c: ldr             x3, [x3, #0xe70]
    // 0xd343a0: blr             x9
    // 0xd343a4: ldur            x0, [fp, #-0x20]
    // 0xd343a8: LeaveFrame
    //     0xd343a8: mov             SP, fp
    //     0xd343ac: ldp             fp, lr, [SP], #0x10
    // 0xd343b0: ret
    //     0xd343b0: ret             
    // 0xd343b4: ldur            x4, [fp, #-8]
    // 0xd343b8: ldur            x0, [fp, #-0x18]
    // 0xd343bc: ldur            x3, [fp, #-0x10]
    // 0xd343c0: ArrayStore: r0[0] = rNULL  ; List_4
    //     0xd343c0: stur            NULL, [x0, #0x17]
    // 0xd343c4: StoreField: r0->field_1b = rNULL
    //     0xd343c4: stur            NULL, [x0, #0x1b]
    // 0xd343c8: LoadField: r2 = r0->field_13
    //     0xd343c8: ldur            w2, [x0, #0x13]
    // 0xd343cc: DecompressPointer r2
    //     0xd343cc: add             x2, x2, HEAP, lsl #32
    // 0xd343d0: ldr             x1, [fp, #0x20]
    // 0xd343d4: r0 = load()
    //     0xd343d4: bl              #0x698024  ; [package:flutter/src/services/asset_bundle.dart] PlatformAssetBundle::load
    // 0xd343d8: ldur            x16, [fp, #-8]
    // 0xd343dc: stp             x0, x16, [SP, #8]
    // 0xd343e0: r16 = Closure: (ByteData) => _AssetManifestBin from Function '<EMAIL>': static.
    //     0xd343e0: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1ce48] Closure: (ByteData) => _AssetManifestBin from Function '<EMAIL>': static. (0x7e54fb734870)
    //     0xd343e4: ldr             x16, [x16, #0xe48]
    // 0xd343e8: str             x16, [SP]
    // 0xd343ec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xd343ec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xd343f0: r0 = then()
    //     0xd343f0: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xd343f4: ldur            x2, [fp, #-0x18]
    // 0xd343f8: r1 = Function '<anonymous closure>':.
    //     0xd343f8: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1ce80] AnonymousClosure: (0xd34638), in [package:flutter/src/services/asset_bundle.dart] CachingAssetBundle::loadStructuredBinaryData (0xd342c0)
    //     0xd343fc: ldr             x1, [x1, #0xe80]
    // 0xd34400: stur            x0, [fp, #-0x20]
    // 0xd34404: r0 = AllocateClosure()
    //     0xd34404: bl              #0xec1630  ; AllocateClosureStub
    // 0xd34408: mov             x3, x0
    // 0xd3440c: ldur            x0, [fp, #-8]
    // 0xd34410: stur            x3, [fp, #-0x28]
    // 0xd34414: StoreField: r3->field_b = r0
    //     0xd34414: stur            w0, [x3, #0xb]
    // 0xd34418: ldur            x2, [fp, #-0x18]
    // 0xd3441c: r1 = Function '<anonymous closure>':.
    //     0xd3441c: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1ce88] AnonymousClosure: (0xd345b0), in [package:flutter/src/services/asset_bundle.dart] CachingAssetBundle::loadStructuredBinaryData (0xd342c0)
    //     0xd34420: ldr             x1, [x1, #0xe88]
    // 0xd34424: r0 = AllocateClosure()
    //     0xd34424: bl              #0xec1630  ; AllocateClosureStub
    // 0xd34428: ldur            x1, [fp, #-8]
    // 0xd3442c: StoreField: r0->field_b = r1
    //     0xd3442c: stur            w1, [x0, #0xb]
    // 0xd34430: r16 = <void?>
    //     0xd34430: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xd34434: ldur            lr, [fp, #-0x20]
    // 0xd34438: stp             lr, x16, [SP, #0x10]
    // 0xd3443c: ldur            x16, [fp, #-0x28]
    // 0xd34440: stp             x0, x16, [SP]
    // 0xd34444: r4 = const [0x1, 0x3, 0x3, 0x2, onError, 0x2, null]
    //     0xd34444: ldr             x4, [PP, #0x1818]  ; [pp+0x1818] List(7) [0x1, 0x3, 0x3, 0x2, "onError", 0x2, Null]
    // 0xd34448: r0 = then()
    //     0xd34448: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xd3444c: ldur            x0, [fp, #-0x18]
    // 0xd34450: LoadField: r1 = r0->field_1b
    //     0xd34450: ldur            w1, [x0, #0x1b]
    // 0xd34454: DecompressPointer r1
    //     0xd34454: add             x1, x1, HEAP, lsl #32
    // 0xd34458: cmp             w1, NULL
    // 0xd3445c: b.eq            #0xd34470
    // 0xd34460: mov             x0, x1
    // 0xd34464: LeaveFrame
    //     0xd34464: mov             SP, fp
    //     0xd34468: ldp             fp, lr, [SP], #0x10
    // 0xd3446c: ret
    //     0xd3446c: ret             
    // 0xd34470: ldur            x2, [fp, #-0x10]
    // 0xd34474: ldur            x1, [fp, #-8]
    // 0xd34478: r0 = _Future()
    //     0xd34478: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0xd3447c: stur            x0, [fp, #-0x20]
    // 0xd34480: StoreField: r0->field_b = rZR
    //     0xd34480: stur            xzr, [x0, #0xb]
    // 0xd34484: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0xd34484: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xd34488: ldr             x0, [x0, #0x7a0]
    //     0xd3448c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xd34490: cmp             w0, w16
    //     0xd34494: b.ne            #0xd344a0
    //     0xd34498: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0xd3449c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xd344a0: mov             x1, x0
    // 0xd344a4: ldur            x0, [fp, #-0x20]
    // 0xd344a8: StoreField: r0->field_13 = r1
    //     0xd344a8: stur            w1, [x0, #0x13]
    // 0xd344ac: ldur            x1, [fp, #-8]
    // 0xd344b0: r0 = _AsyncCompleter()
    //     0xd344b0: bl              #0x661210  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0xd344b4: ldur            x3, [fp, #-0x20]
    // 0xd344b8: StoreField: r0->field_b = r3
    //     0xd344b8: stur            w3, [x0, #0xb]
    // 0xd344bc: ldur            x4, [fp, #-0x18]
    // 0xd344c0: ArrayStore: r4[0] = r0  ; List_4
    //     0xd344c0: stur            w0, [x4, #0x17]
    //     0xd344c4: ldurb           w16, [x4, #-1]
    //     0xd344c8: ldurb           w17, [x0, #-1]
    //     0xd344cc: and             x16, x17, x16, lsr #2
    //     0xd344d0: tst             x16, HEAP, lsr #32
    //     0xd344d4: b.eq            #0xd344dc
    //     0xd344d8: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xd344dc: LoadField: r5 = r4->field_13
    //     0xd344dc: ldur            w5, [x4, #0x13]
    // 0xd344e0: DecompressPointer r5
    //     0xd344e0: add             x5, x5, HEAP, lsl #32
    // 0xd344e4: ldur            x6, [fp, #-0x10]
    // 0xd344e8: stur            x5, [fp, #-0x28]
    // 0xd344ec: LoadField: r7 = r6->field_7
    //     0xd344ec: ldur            w7, [x6, #7]
    // 0xd344f0: DecompressPointer r7
    //     0xd344f0: add             x7, x7, HEAP, lsl #32
    // 0xd344f4: mov             x0, x5
    // 0xd344f8: mov             x2, x7
    // 0xd344fc: stur            x7, [fp, #-8]
    // 0xd34500: r1 = Null
    //     0xd34500: mov             x1, NULL
    // 0xd34504: cmp             w2, NULL
    // 0xd34508: b.eq            #0xd34528
    // 0xd3450c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xd3450c: ldur            w4, [x2, #0x17]
    // 0xd34510: DecompressPointer r4
    //     0xd34510: add             x4, x4, HEAP, lsl #32
    // 0xd34514: r8 = X0
    //     0xd34514: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xd34518: LoadField: r9 = r4->field_7
    //     0xd34518: ldur            x9, [x4, #7]
    // 0xd3451c: r3 = Null
    //     0xd3451c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1ce90] Null
    //     0xd34520: ldr             x3, [x3, #0xe90]
    // 0xd34524: blr             x9
    // 0xd34528: ldur            x0, [fp, #-0x20]
    // 0xd3452c: ldur            x2, [fp, #-8]
    // 0xd34530: r1 = Null
    //     0xd34530: mov             x1, NULL
    // 0xd34534: cmp             w2, NULL
    // 0xd34538: b.eq            #0xd34558
    // 0xd3453c: LoadField: r4 = r2->field_1b
    //     0xd3453c: ldur            w4, [x2, #0x1b]
    // 0xd34540: DecompressPointer r4
    //     0xd34540: add             x4, x4, HEAP, lsl #32
    // 0xd34544: r8 = X1
    //     0xd34544: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xd34548: LoadField: r9 = r4->field_7
    //     0xd34548: ldur            x9, [x4, #7]
    // 0xd3454c: r3 = Null
    //     0xd3454c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1cea0] Null
    //     0xd34550: ldr             x3, [x3, #0xea0]
    // 0xd34554: blr             x9
    // 0xd34558: ldur            x1, [fp, #-0x10]
    // 0xd3455c: ldur            x2, [fp, #-0x28]
    // 0xd34560: r0 = _hashCode()
    //     0xd34560: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xd34564: ldur            x1, [fp, #-0x10]
    // 0xd34568: ldur            x2, [fp, #-0x28]
    // 0xd3456c: ldur            x3, [fp, #-0x20]
    // 0xd34570: mov             x5, x0
    // 0xd34574: r0 = _set()
    //     0xd34574: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xd34578: ldur            x1, [fp, #-0x18]
    // 0xd3457c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xd3457c: ldur            w2, [x1, #0x17]
    // 0xd34580: DecompressPointer r2
    //     0xd34580: add             x2, x2, HEAP, lsl #32
    // 0xd34584: cmp             w2, NULL
    // 0xd34588: b.eq            #0xd345ac
    // 0xd3458c: LoadField: r0 = r2->field_b
    //     0xd3458c: ldur            w0, [x2, #0xb]
    // 0xd34590: DecompressPointer r0
    //     0xd34590: add             x0, x0, HEAP, lsl #32
    // 0xd34594: LeaveFrame
    //     0xd34594: mov             SP, fp
    //     0xd34598: ldp             fp, lr, [SP], #0x10
    // 0xd3459c: ret
    //     0xd3459c: ret             
    // 0xd345a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd345a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd345a4: b               #0xd34300
    // 0xd345a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd345a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd345ac: r0 = NullErrorSharedWithoutFPURegs()
    //     0xd345ac: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, Object, StackTrace) {
    // ** addr: 0xd345b0, size: 0x88
    // 0xd345b0: EnterFrame
    //     0xd345b0: stp             fp, lr, [SP, #-0x10]!
    //     0xd345b4: mov             fp, SP
    // 0xd345b8: AllocStack(0x10)
    //     0xd345b8: sub             SP, SP, #0x10
    // 0xd345bc: SetupParameters()
    //     0xd345bc: ldr             x0, [fp, #0x20]
    //     0xd345c0: ldur            w3, [x0, #0x17]
    //     0xd345c4: add             x3, x3, HEAP, lsl #32
    //     0xd345c8: stur            x3, [fp, #-8]
    // 0xd345cc: CheckStackOverflow
    //     0xd345cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd345d0: cmp             SP, x16
    //     0xd345d4: b.ls            #0xd3462c
    // 0xd345d8: LoadField: r0 = r3->field_f
    //     0xd345d8: ldur            w0, [x3, #0xf]
    // 0xd345dc: DecompressPointer r0
    //     0xd345dc: add             x0, x0, HEAP, lsl #32
    // 0xd345e0: LoadField: r1 = r0->field_f
    //     0xd345e0: ldur            w1, [x0, #0xf]
    // 0xd345e4: DecompressPointer r1
    //     0xd345e4: add             x1, x1, HEAP, lsl #32
    // 0xd345e8: LoadField: r2 = r3->field_13
    //     0xd345e8: ldur            w2, [x3, #0x13]
    // 0xd345ec: DecompressPointer r2
    //     0xd345ec: add             x2, x2, HEAP, lsl #32
    // 0xd345f0: r0 = remove()
    //     0xd345f0: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0xd345f4: ldur            x0, [fp, #-8]
    // 0xd345f8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd345f8: ldur            w1, [x0, #0x17]
    // 0xd345fc: DecompressPointer r1
    //     0xd345fc: add             x1, x1, HEAP, lsl #32
    // 0xd34600: cmp             w1, NULL
    // 0xd34604: b.eq            #0xd34634
    // 0xd34608: ldr             x16, [fp, #0x10]
    // 0xd3460c: str             x16, [SP]
    // 0xd34610: ldr             x2, [fp, #0x18]
    // 0xd34614: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xd34614: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xd34618: r0 = completeError()
    //     0xd34618: bl              #0x6562e0  ; [dart:async] _Completer::completeError
    // 0xd3461c: r0 = Null
    //     0xd3461c: mov             x0, NULL
    // 0xd34620: LeaveFrame
    //     0xd34620: mov             SP, fp
    //     0xd34624: ldp             fp, lr, [SP], #0x10
    // 0xd34628: ret
    //     0xd34628: ret             
    // 0xd3462c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3462c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd34630: b               #0xd345d8
    // 0xd34634: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd34634: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, Y0) {
    // ** addr: 0xd34638, size: 0x150
    // 0xd34638: EnterFrame
    //     0xd34638: stp             fp, lr, [SP, #-0x10]!
    //     0xd3463c: mov             fp, SP
    // 0xd34640: AllocStack(0x30)
    //     0xd34640: sub             SP, SP, #0x30
    // 0xd34644: SetupParameters()
    //     0xd34644: ldr             x0, [fp, #0x18]
    //     0xd34648: ldur            w2, [x0, #0x17]
    //     0xd3464c: add             x2, x2, HEAP, lsl #32
    //     0xd34650: stur            x2, [fp, #-8]
    // 0xd34654: CheckStackOverflow
    //     0xd34654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd34658: cmp             SP, x16
    //     0xd3465c: b.ls            #0xd34780
    // 0xd34660: LoadField: r1 = r0->field_b
    //     0xd34660: ldur            w1, [x0, #0xb]
    // 0xd34664: DecompressPointer r1
    //     0xd34664: add             x1, x1, HEAP, lsl #32
    // 0xd34668: r0 = SynchronousFuture()
    //     0xd34668: bl              #0x942648  ; AllocateSynchronousFutureStub -> SynchronousFuture<X0> (size=0x10)
    // 0xd3466c: mov             x4, x0
    // 0xd34670: ldr             x3, [fp, #0x10]
    // 0xd34674: stur            x4, [fp, #-0x28]
    // 0xd34678: StoreField: r4->field_b = r3
    //     0xd34678: stur            w3, [x4, #0xb]
    // 0xd3467c: mov             x0, x4
    // 0xd34680: ldur            x5, [fp, #-8]
    // 0xd34684: StoreField: r5->field_1b = r0
    //     0xd34684: stur            w0, [x5, #0x1b]
    //     0xd34688: ldurb           w16, [x5, #-1]
    //     0xd3468c: ldurb           w17, [x0, #-1]
    //     0xd34690: and             x16, x17, x16, lsr #2
    //     0xd34694: tst             x16, HEAP, lsr #32
    //     0xd34698: b.eq            #0xd346a0
    //     0xd3469c: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xd346a0: LoadField: r0 = r5->field_f
    //     0xd346a0: ldur            w0, [x5, #0xf]
    // 0xd346a4: DecompressPointer r0
    //     0xd346a4: add             x0, x0, HEAP, lsl #32
    // 0xd346a8: LoadField: r6 = r0->field_f
    //     0xd346a8: ldur            w6, [x0, #0xf]
    // 0xd346ac: DecompressPointer r6
    //     0xd346ac: add             x6, x6, HEAP, lsl #32
    // 0xd346b0: stur            x6, [fp, #-0x20]
    // 0xd346b4: LoadField: r7 = r5->field_13
    //     0xd346b4: ldur            w7, [x5, #0x13]
    // 0xd346b8: DecompressPointer r7
    //     0xd346b8: add             x7, x7, HEAP, lsl #32
    // 0xd346bc: stur            x7, [fp, #-0x18]
    // 0xd346c0: LoadField: r8 = r6->field_7
    //     0xd346c0: ldur            w8, [x6, #7]
    // 0xd346c4: DecompressPointer r8
    //     0xd346c4: add             x8, x8, HEAP, lsl #32
    // 0xd346c8: mov             x0, x7
    // 0xd346cc: mov             x2, x8
    // 0xd346d0: stur            x8, [fp, #-0x10]
    // 0xd346d4: r1 = Null
    //     0xd346d4: mov             x1, NULL
    // 0xd346d8: cmp             w2, NULL
    // 0xd346dc: b.eq            #0xd346fc
    // 0xd346e0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xd346e0: ldur            w4, [x2, #0x17]
    // 0xd346e4: DecompressPointer r4
    //     0xd346e4: add             x4, x4, HEAP, lsl #32
    // 0xd346e8: r8 = X0
    //     0xd346e8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xd346ec: LoadField: r9 = r4->field_7
    //     0xd346ec: ldur            x9, [x4, #7]
    // 0xd346f0: r3 = Null
    //     0xd346f0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1ceb8] Null
    //     0xd346f4: ldr             x3, [x3, #0xeb8]
    // 0xd346f8: blr             x9
    // 0xd346fc: ldur            x0, [fp, #-0x28]
    // 0xd34700: ldur            x2, [fp, #-0x10]
    // 0xd34704: r1 = Null
    //     0xd34704: mov             x1, NULL
    // 0xd34708: cmp             w2, NULL
    // 0xd3470c: b.eq            #0xd3472c
    // 0xd34710: LoadField: r4 = r2->field_1b
    //     0xd34710: ldur            w4, [x2, #0x1b]
    // 0xd34714: DecompressPointer r4
    //     0xd34714: add             x4, x4, HEAP, lsl #32
    // 0xd34718: r8 = X1
    //     0xd34718: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xd3471c: LoadField: r9 = r4->field_7
    //     0xd3471c: ldur            x9, [x4, #7]
    // 0xd34720: r3 = Null
    //     0xd34720: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1cec8] Null
    //     0xd34724: ldr             x3, [x3, #0xec8]
    // 0xd34728: blr             x9
    // 0xd3472c: ldur            x1, [fp, #-0x20]
    // 0xd34730: ldur            x2, [fp, #-0x18]
    // 0xd34734: r0 = _hashCode()
    //     0xd34734: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xd34738: ldur            x1, [fp, #-0x20]
    // 0xd3473c: ldur            x2, [fp, #-0x18]
    // 0xd34740: ldur            x3, [fp, #-0x28]
    // 0xd34744: mov             x5, x0
    // 0xd34748: r0 = _set()
    //     0xd34748: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xd3474c: ldur            x0, [fp, #-8]
    // 0xd34750: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd34750: ldur            w1, [x0, #0x17]
    // 0xd34754: DecompressPointer r1
    //     0xd34754: add             x1, x1, HEAP, lsl #32
    // 0xd34758: cmp             w1, NULL
    // 0xd3475c: b.eq            #0xd34770
    // 0xd34760: ldr             x16, [fp, #0x10]
    // 0xd34764: str             x16, [SP]
    // 0xd34768: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xd34768: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xd3476c: r0 = complete()
    //     0xd3476c: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0xd34770: r0 = Null
    //     0xd34770: mov             x0, NULL
    // 0xd34774: LeaveFrame
    //     0xd34774: mov             SP, fp
    //     0xd34778: ldp             fp, lr, [SP], #0x10
    // 0xd3477c: ret
    //     0xd3477c: ret             
    // 0xd34780: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd34780: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd34784: b               #0xd34660
  }
}

// class id: 2828, size: 0x14, field offset: 0x14
class PlatformAssetBundle extends CachingAssetBundle {

  _ load(/* No info */) {
    // ** addr: 0x698024, size: 0xec
    // 0x698024: EnterFrame
    //     0x698024: stp             fp, lr, [SP, #-0x10]!
    //     0x698028: mov             fp, SP
    // 0x69802c: AllocStack(0x28)
    //     0x69802c: sub             SP, SP, #0x28
    // 0x698030: SetupParameters(PlatformAssetBundle this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0x698030: mov             x0, x1
    //     0x698034: mov             x1, x2
    //     0x698038: stur            x2, [fp, #-8]
    // 0x69803c: CheckStackOverflow
    //     0x69803c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x698040: cmp             SP, x16
    //     0x698044: b.ls            #0x6980fc
    // 0x698048: r1 = 1
    //     0x698048: movz            x1, #0x1
    // 0x69804c: r0 = AllocateContext()
    //     0x69804c: bl              #0xec126c  ; AllocateContextStub
    // 0x698050: ldur            x1, [fp, #-8]
    // 0x698054: stur            x0, [fp, #-0x10]
    // 0x698058: StoreField: r0->field_f = r1
    //     0x698058: stur            w1, [x0, #0xf]
    // 0x69805c: r0 = encodeFull()
    //     0x69805c: bl              #0x698198  ; [dart:core] Uri::encodeFull
    // 0x698060: str             x0, [SP]
    // 0x698064: r1 = Null
    //     0x698064: mov             x1, NULL
    // 0x698068: r4 = const [0, 0x2, 0x1, 0x1, path, 0x1, null]
    //     0x698068: ldr             x4, [PP, #0x35f8]  ; [pp+0x35f8] List(7) [0, 0x2, 0x1, 0x1, "path", 0x1, Null]
    // 0x69806c: r0 = _Uri()
    //     0x69806c: bl              #0x5ff47c  ; [dart:core] _Uri::_Uri
    // 0x698070: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x698070: ldur            w2, [x0, #0x17]
    // 0x698074: DecompressPointer r2
    //     0x698074: add             x2, x2, HEAP, lsl #32
    // 0x698078: r1 = Instance_Utf8Encoder
    //     0x698078: ldr             x1, [PP, #0xe08]  ; [pp+0xe08] Obj!Utf8Encoder@e2cd81
    // 0x69807c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x69807c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x698080: r0 = convert()
    //     0x698080: bl              #0xcf8df0  ; [dart:convert] Utf8Encoder::convert
    // 0x698084: r1 = LoadStaticField(0x69c)
    //     0x698084: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x698088: ldr             x1, [x1, #0xd38]
    // 0x69808c: cmp             w1, NULL
    // 0x698090: b.eq            #0x698104
    // 0x698094: LoadField: r2 = r1->field_97
    //     0x698094: ldur            w2, [x1, #0x97]
    // 0x698098: DecompressPointer r2
    //     0x698098: add             x2, x2, HEAP, lsl #32
    // 0x69809c: r16 = Sentinel
    //     0x69809c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6980a0: cmp             w2, w16
    // 0x6980a4: b.eq            #0x698108
    // 0x6980a8: mov             x2, x0
    // 0x6980ac: r1 = Null
    //     0x6980ac: mov             x1, NULL
    // 0x6980b0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6980b0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6980b4: r0 = ByteData.sublistView()
    //     0x6980b4: bl              #0x698110  ; [dart:typed_data] ByteData::ByteData.sublistView
    // 0x6980b8: mov             x3, x0
    // 0x6980bc: r1 = Instance__DefaultBinaryMessenger
    //     0x6980bc: ldr             x1, [PP, #0x3b0]  ; [pp+0x3b0] Obj!_DefaultBinaryMessenger@e11641
    // 0x6980c0: r2 = "flutter/assets"
    //     0x6980c0: ldr             x2, [PP, #0x3600]  ; [pp+0x3600] "flutter/assets"
    // 0x6980c4: r0 = send()
    //     0x6980c4: bl              #0xdb2678  ; [package:flutter/src/services/binding.dart] _DefaultBinaryMessenger::send
    // 0x6980c8: ldur            x2, [fp, #-0x10]
    // 0x6980cc: r1 = Function '<anonymous closure>':.
    //     0x6980cc: ldr             x1, [PP, #0x3608]  ; [pp+0x3608] AnonymousClosure: (0x6981d4), in [package:flutter/src/services/asset_bundle.dart] PlatformAssetBundle::load (0x698024)
    // 0x6980d0: stur            x0, [fp, #-8]
    // 0x6980d4: r0 = AllocateClosure()
    //     0x6980d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x6980d8: r16 = <ByteData>
    //     0x6980d8: ldr             x16, [PP, #0x3610]  ; [pp+0x3610] TypeArguments: <ByteData>
    // 0x6980dc: ldur            lr, [fp, #-8]
    // 0x6980e0: stp             lr, x16, [SP, #8]
    // 0x6980e4: str             x0, [SP]
    // 0x6980e8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6980e8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6980ec: r0 = then()
    //     0x6980ec: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x6980f0: LeaveFrame
    //     0x6980f0: mov             SP, fp
    //     0x6980f4: ldp             fp, lr, [SP], #0x10
    // 0x6980f8: ret
    //     0x6980f8: ret             
    // 0x6980fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6980fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x698100: b               #0x698048
    // 0x698104: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x698104: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x698108: r9 = _defaultBinaryMessenger
    //     0x698108: ldr             x9, [PP, #0x3c0]  ; [pp+0x3c0] Field <_WidgetsFlutterBinding&BindingBase&GestureBinding&SchedulerBinding&ServicesBinding@263399801._defaultBinaryMessenger@57240726>: late final (offset: 0x98)
    // 0x69810c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x69810c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] ByteData <anonymous closure>(dynamic, ByteData?) {
    // ** addr: 0x6981d4, size: 0xd4
    // 0x6981d4: EnterFrame
    //     0x6981d4: stp             fp, lr, [SP, #-0x10]!
    //     0x6981d8: mov             fp, SP
    // 0x6981dc: AllocStack(0x18)
    //     0x6981dc: sub             SP, SP, #0x18
    // 0x6981e0: SetupParameters()
    //     0x6981e0: ldr             x0, [fp, #0x18]
    //     0x6981e4: ldur            w1, [x0, #0x17]
    //     0x6981e8: add             x1, x1, HEAP, lsl #32
    // 0x6981ec: CheckStackOverflow
    //     0x6981ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6981f0: cmp             SP, x16
    //     0x6981f4: b.ls            #0x6982a0
    // 0x6981f8: ldr             x0, [fp, #0x10]
    // 0x6981fc: cmp             w0, NULL
    // 0x698200: b.eq            #0x698210
    // 0x698204: LeaveFrame
    //     0x698204: mov             SP, fp
    //     0x698208: ldp             fp, lr, [SP], #0x10
    // 0x69820c: ret
    //     0x69820c: ret             
    // 0x698210: LoadField: r0 = r1->field_f
    //     0x698210: ldur            w0, [x1, #0xf]
    // 0x698214: DecompressPointer r0
    //     0x698214: add             x0, x0, HEAP, lsl #32
    // 0x698218: mov             x1, x0
    // 0x69821c: r0 = _errorSummaryWithKey()
    //     0x69821c: bl              #0x6982a8  ; [package:flutter/src/services/asset_bundle.dart] ::_errorSummaryWithKey
    // 0x698220: r1 = <List<Object>>
    //     0x698220: ldr             x1, [PP, #0x440]  ; [pp+0x440] TypeArguments: <List<Object>>
    // 0x698224: stur            x0, [fp, #-8]
    // 0x698228: r0 = ErrorDescription()
    //     0x698228: bl              #0x64483c  ; AllocateErrorDescriptionStub -> ErrorDescription (size=0x2c)
    // 0x69822c: mov             x1, x0
    // 0x698230: r2 = "The asset does not exist or has empty data."
    //     0x698230: ldr             x2, [PP, #0x3618]  ; [pp+0x3618] "The asset does not exist or has empty data."
    // 0x698234: r3 = Instance_DiagnosticLevel
    //     0x698234: ldr             x3, [PP, #0x450]  ; [pp+0x450] Obj!DiagnosticLevel@e37021
    // 0x698238: stur            x0, [fp, #-0x10]
    // 0x69823c: r0 = _ErrorDiagnostic()
    //     0x69823c: bl              #0x644784  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::_ErrorDiagnostic
    // 0x698240: r1 = Null
    //     0x698240: mov             x1, NULL
    // 0x698244: r2 = 4
    //     0x698244: movz            x2, #0x4
    // 0x698248: r0 = AllocateArray()
    //     0x698248: bl              #0xec22fc  ; AllocateArrayStub
    // 0x69824c: mov             x2, x0
    // 0x698250: ldur            x0, [fp, #-8]
    // 0x698254: stur            x2, [fp, #-0x18]
    // 0x698258: StoreField: r2->field_f = r0
    //     0x698258: stur            w0, [x2, #0xf]
    // 0x69825c: ldur            x0, [fp, #-0x10]
    // 0x698260: StoreField: r2->field_13 = r0
    //     0x698260: stur            w0, [x2, #0x13]
    // 0x698264: r1 = <DiagnosticsNode>
    //     0x698264: ldr             x1, [PP, #0x23c0]  ; [pp+0x23c0] TypeArguments: <DiagnosticsNode>
    // 0x698268: r0 = AllocateGrowableArray()
    //     0x698268: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x69826c: mov             x1, x0
    // 0x698270: ldur            x0, [fp, #-0x18]
    // 0x698274: stur            x1, [fp, #-8]
    // 0x698278: StoreField: r1->field_f = r0
    //     0x698278: stur            w0, [x1, #0xf]
    // 0x69827c: r0 = 4
    //     0x69827c: movz            x0, #0x4
    // 0x698280: StoreField: r1->field_b = r0
    //     0x698280: stur            w0, [x1, #0xb]
    // 0x698284: r0 = FlutterError()
    //     0x698284: bl              #0x6804c0  ; AllocateFlutterErrorStub -> FlutterError (size=0x10)
    // 0x698288: mov             x1, x0
    // 0x69828c: ldur            x0, [fp, #-8]
    // 0x698290: StoreField: r1->field_b = r0
    //     0x698290: stur            w0, [x1, #0xb]
    // 0x698294: mov             x0, x1
    // 0x698298: r0 = Throw()
    //     0x698298: bl              #0xec04b8  ; ThrowStub
    // 0x69829c: brk             #0
    // 0x6982a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6982a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6982a4: b               #0x6981f8
  }
  _ loadBuffer(/* No info */) async {
    // ** addr: 0xd09d50, size: 0x1e4
    // 0xd09d50: EnterFrame
    //     0xd09d50: stp             fp, lr, [SP, #-0x10]!
    //     0xd09d54: mov             fp, SP
    // 0xd09d58: AllocStack(0x80)
    //     0xd09d58: sub             SP, SP, #0x80
    // 0xd09d5c: SetupParameters(PlatformAssetBundle this /* r1 => r2, fp-0x58 */, dynamic _ /* r2 => r1, fp-0x60 */)
    //     0xd09d5c: stur            NULL, [fp, #-8]
    //     0xd09d60: stur            x1, [fp, #-0x58]
    //     0xd09d64: mov             x16, x2
    //     0xd09d68: mov             x2, x1
    //     0xd09d6c: mov             x1, x16
    //     0xd09d70: stur            x1, [fp, #-0x60]
    // 0xd09d74: CheckStackOverflow
    //     0xd09d74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd09d78: cmp             SP, x16
    //     0xd09d7c: b.ls            #0xd09f2c
    // 0xd09d80: InitAsync() -> Future<ImmutableBuffer>
    //     0xd09d80: add             x0, PP, #0x22, lsl #12  ; [pp+0x22038] TypeArguments: <ImmutableBuffer>
    //     0xd09d84: ldr             x0, [x0, #0x38]
    //     0xd09d88: bl              #0x661298  ; InitAsyncStub
    // 0xd09d8c: ldur            x1, [fp, #-0x60]
    // 0xd09d90: r0 = fromAsset()
    //     0xd09d90: bl              #0xd09f34  ; [dart:ui] ImmutableBuffer::fromAsset
    // 0xd09d94: mov             x1, x0
    // 0xd09d98: stur            x1, [fp, #-0x58]
    // 0xd09d9c: r0 = Await()
    //     0xd09d9c: bl              #0x661044  ; AwaitStub
    // 0xd09da0: r0 = ReturnAsync()
    //     0xd09da0: b               #0x6576a4  ; ReturnAsyncStub
    // 0xd09da4: sub             SP, fp, #0x80
    // 0xd09da8: mov             x4, x0
    // 0xd09dac: mov             x3, x1
    // 0xd09db0: stur            x0, [fp, #-0x58]
    // 0xd09db4: stur            x1, [fp, #-0x60]
    // 0xd09db8: r2 = Null
    //     0xd09db8: mov             x2, NULL
    // 0xd09dbc: r1 = Null
    //     0xd09dbc: mov             x1, NULL
    // 0xd09dc0: cmp             w0, NULL
    // 0xd09dc4: b.eq            #0xd09e50
    // 0xd09dc8: branchIfSmi(r0, 0xd09e50)
    //     0xd09dc8: tbz             w0, #0, #0xd09e50
    // 0xd09dcc: r3 = LoadClassIdInstr(r0)
    //     0xd09dcc: ldur            x3, [x0, #-1]
    //     0xd09dd0: ubfx            x3, x3, #0xc, #0x14
    // 0xd09dd4: r4 = LoadClassIdInstr(r0)
    //     0xd09dd4: ldur            x4, [x0, #-1]
    //     0xd09dd8: ubfx            x4, x4, #0xc, #0x14
    // 0xd09ddc: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xd09de0: ldr             x3, [x3, #0x18]
    // 0xd09de4: ldr             x3, [x3, x4, lsl #3]
    // 0xd09de8: LoadField: r3 = r3->field_2b
    //     0xd09de8: ldur            w3, [x3, #0x2b]
    // 0xd09dec: DecompressPointer r3
    //     0xd09dec: add             x3, x3, HEAP, lsl #32
    // 0xd09df0: cmp             w3, NULL
    // 0xd09df4: b.eq            #0xd09e50
    // 0xd09df8: LoadField: r3 = r3->field_f
    //     0xd09df8: ldur            w3, [x3, #0xf]
    // 0xd09dfc: lsr             x3, x3, #3
    // 0xd09e00: r17 = 6724
    //     0xd09e00: movz            x17, #0x1a44
    // 0xd09e04: cmp             x3, x17
    // 0xd09e08: b.eq            #0xd09e58
    // 0xd09e0c: r3 = SubtypeTestCache
    //     0xd09e0c: add             x3, PP, #0x22, lsl #12  ; [pp+0x22040] SubtypeTestCache
    //     0xd09e10: ldr             x3, [x3, #0x40]
    // 0xd09e14: r30 = Subtype1TestCacheStub
    //     0xd09e14: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xd09e18: LoadField: r30 = r30->field_7
    //     0xd09e18: ldur            lr, [lr, #7]
    // 0xd09e1c: blr             lr
    // 0xd09e20: cmp             w7, NULL
    // 0xd09e24: b.eq            #0xd09e30
    // 0xd09e28: tbnz            w7, #4, #0xd09e50
    // 0xd09e2c: b               #0xd09e58
    // 0xd09e30: r8 = Exception
    //     0xd09e30: add             x8, PP, #0x22, lsl #12  ; [pp+0x22048] Type: Exception
    //     0xd09e34: ldr             x8, [x8, #0x48]
    // 0xd09e38: r3 = SubtypeTestCache
    //     0xd09e38: add             x3, PP, #0x22, lsl #12  ; [pp+0x22050] SubtypeTestCache
    //     0xd09e3c: ldr             x3, [x3, #0x50]
    // 0xd09e40: r30 = InstanceOfStub
    //     0xd09e40: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xd09e44: LoadField: r30 = r30->field_7
    //     0xd09e44: ldur            lr, [lr, #7]
    // 0xd09e48: blr             lr
    // 0xd09e4c: b               #0xd09e5c
    // 0xd09e50: r0 = false
    //     0xd09e50: add             x0, NULL, #0x30  ; false
    // 0xd09e54: b               #0xd09e5c
    // 0xd09e58: r0 = true
    //     0xd09e58: add             x0, NULL, #0x20  ; true
    // 0xd09e5c: tbnz            w0, #4, #0xd09f1c
    // 0xd09e60: ldur            x0, [fp, #-0x58]
    // 0xd09e64: ldur            x1, [fp, #-0x18]
    // 0xd09e68: r0 = _errorSummaryWithKey()
    //     0xd09e68: bl              #0x6982a8  ; [package:flutter/src/services/asset_bundle.dart] ::_errorSummaryWithKey
    // 0xd09e6c: mov             x1, x0
    // 0xd09e70: ldur            x0, [fp, #-0x58]
    // 0xd09e74: stur            x1, [fp, #-0x68]
    // 0xd09e78: r2 = LoadClassIdInstr(r0)
    //     0xd09e78: ldur            x2, [x0, #-1]
    //     0xd09e7c: ubfx            x2, x2, #0xc, #0x14
    // 0xd09e80: str             x0, [SP]
    // 0xd09e84: mov             x0, x2
    // 0xd09e88: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xd09e88: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xd09e8c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xd09e8c: movz            x17, #0x2b03
    //     0xd09e90: add             lr, x0, x17
    //     0xd09e94: ldr             lr, [x21, lr, lsl #3]
    //     0xd09e98: blr             lr
    // 0xd09e9c: r1 = <List<Object>>
    //     0xd09e9c: ldr             x1, [PP, #0x440]  ; [pp+0x440] TypeArguments: <List<Object>>
    // 0xd09ea0: stur            x0, [fp, #-0x70]
    // 0xd09ea4: r0 = ErrorDescription()
    //     0xd09ea4: bl              #0x64483c  ; AllocateErrorDescriptionStub -> ErrorDescription (size=0x2c)
    // 0xd09ea8: mov             x1, x0
    // 0xd09eac: ldur            x2, [fp, #-0x70]
    // 0xd09eb0: r3 = Instance_DiagnosticLevel
    //     0xd09eb0: ldr             x3, [PP, #0x450]  ; [pp+0x450] Obj!DiagnosticLevel@e37021
    // 0xd09eb4: stur            x0, [fp, #-0x70]
    // 0xd09eb8: r0 = _ErrorDiagnostic()
    //     0xd09eb8: bl              #0x644784  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::_ErrorDiagnostic
    // 0xd09ebc: r1 = Null
    //     0xd09ebc: mov             x1, NULL
    // 0xd09ec0: r2 = 4
    //     0xd09ec0: movz            x2, #0x4
    // 0xd09ec4: r0 = AllocateArray()
    //     0xd09ec4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xd09ec8: mov             x2, x0
    // 0xd09ecc: ldur            x0, [fp, #-0x68]
    // 0xd09ed0: stur            x2, [fp, #-0x78]
    // 0xd09ed4: StoreField: r2->field_f = r0
    //     0xd09ed4: stur            w0, [x2, #0xf]
    // 0xd09ed8: ldur            x0, [fp, #-0x70]
    // 0xd09edc: StoreField: r2->field_13 = r0
    //     0xd09edc: stur            w0, [x2, #0x13]
    // 0xd09ee0: r1 = <DiagnosticsNode>
    //     0xd09ee0: ldr             x1, [PP, #0x23c0]  ; [pp+0x23c0] TypeArguments: <DiagnosticsNode>
    // 0xd09ee4: r0 = AllocateGrowableArray()
    //     0xd09ee4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xd09ee8: mov             x1, x0
    // 0xd09eec: ldur            x0, [fp, #-0x78]
    // 0xd09ef0: stur            x1, [fp, #-0x68]
    // 0xd09ef4: StoreField: r1->field_f = r0
    //     0xd09ef4: stur            w0, [x1, #0xf]
    // 0xd09ef8: r0 = 4
    //     0xd09ef8: movz            x0, #0x4
    // 0xd09efc: StoreField: r1->field_b = r0
    //     0xd09efc: stur            w0, [x1, #0xb]
    // 0xd09f00: r0 = FlutterError()
    //     0xd09f00: bl              #0x6804c0  ; AllocateFlutterErrorStub -> FlutterError (size=0x10)
    // 0xd09f04: mov             x1, x0
    // 0xd09f08: ldur            x0, [fp, #-0x68]
    // 0xd09f0c: StoreField: r1->field_b = r0
    //     0xd09f0c: stur            w0, [x1, #0xb]
    // 0xd09f10: mov             x0, x1
    // 0xd09f14: r0 = Throw()
    //     0xd09f14: bl              #0xec04b8  ; ThrowStub
    // 0xd09f18: brk             #0
    // 0xd09f1c: ldur            x0, [fp, #-0x58]
    // 0xd09f20: ldur            x1, [fp, #-0x60]
    // 0xd09f24: r0 = ReThrow()
    //     0xd09f24: bl              #0xec048c  ; ReThrowStub
    // 0xd09f28: brk             #0
    // 0xd09f2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd09f2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd09f30: b               #0xd09d80
  }
}
