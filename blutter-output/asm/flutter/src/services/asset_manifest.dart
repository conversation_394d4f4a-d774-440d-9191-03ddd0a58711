// lib: , url: package:flutter/src/services/asset_manifest.dart

// class id: 1049064, size: 0x8
class :: {
}

// class id: 2823, size: 0x10, field offset: 0x8
//   const constructor, 
class AssetMetadata extends Object {
}

// class id: 2824, size: 0x10, field offset: 0x8
class _AssetManifestBin extends Object
    implements AssetManifest {

  [closure] static _AssetManifestBin _AssetManifestBin.fromStandardMessageCodecMessage(dynamic, ByteData) {
    // ** addr: 0xd34870, size: 0x34
    // 0xd34870: EnterFrame
    //     0xd34870: stp             fp, lr, [SP, #-0x10]!
    //     0xd34874: mov             fp, SP
    // 0xd34878: CheckStackOverflow
    //     0xd34878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3487c: cmp             SP, x16
    //     0xd34880: b.ls            #0xd3489c
    // 0xd34884: ldr             x2, [fp, #0x10]
    // 0xd34888: r1 = Null
    //     0xd34888: mov             x1, NULL
    // 0xd3488c: r0 = _AssetManifestBin.fromStandardMessageCodecMessage()
    //     0xd3488c: bl              #0xd348a4  ; [package:flutter/src/services/asset_manifest.dart] _AssetManifestBin::_AssetManifestBin.fromStandardMessageCodecMessage
    // 0xd34890: LeaveFrame
    //     0xd34890: mov             SP, fp
    //     0xd34894: ldp             fp, lr, [SP], #0x10
    // 0xd34898: ret
    //     0xd34898: ret             
    // 0xd3489c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3489c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd348a0: b               #0xd34884
  }
  factory _AssetManifestBin _AssetManifestBin.fromStandardMessageCodecMessage(dynamic, ByteData) {
    // ** addr: 0xd348a4, size: 0x80
    // 0xd348a4: EnterFrame
    //     0xd348a4: stp             fp, lr, [SP, #-0x10]!
    //     0xd348a8: mov             fp, SP
    // 0xd348ac: AllocStack(0x20)
    //     0xd348ac: sub             SP, SP, #0x20
    // 0xd348b0: CheckStackOverflow
    //     0xd348b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd348b4: cmp             SP, x16
    //     0xd348b8: b.ls            #0xd3491c
    // 0xd348bc: r1 = Instance_StandardMessageCodec
    //     0xd348bc: ldr             x1, [PP, #0x2e20]  ; [pp+0x2e20] Obj!StandardMessageCodec@e25971
    // 0xd348c0: r0 = decodeMessage()
    //     0xd348c0: bl              #0xcec74c  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::decodeMessage
    // 0xd348c4: mov             x3, x0
    // 0xd348c8: r2 = Null
    //     0xd348c8: mov             x2, NULL
    // 0xd348cc: r1 = Null
    //     0xd348cc: mov             x1, NULL
    // 0xd348d0: stur            x3, [fp, #-8]
    // 0xd348d4: r8 = Map<Object?, Object?>
    //     0xd348d4: ldr             x8, [PP, #0x3008]  ; [pp+0x3008] Type: Map<Object?, Object?>
    // 0xd348d8: r3 = Null
    //     0xd348d8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1ce50] Null
    //     0xd348dc: ldr             x3, [x3, #0xe50]
    // 0xd348e0: r0 = Map<Object?, Object?>()
    //     0xd348e0: bl              #0x6a06a8  ; IsType_Map<Object?, Object?>_Stub
    // 0xd348e4: r16 = <String, List<AssetMetadata>>
    //     0xd348e4: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1ce60] TypeArguments: <String, List<AssetMetadata>>
    //     0xd348e8: ldr             x16, [x16, #0xe60]
    // 0xd348ec: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xd348f0: stp             lr, x16, [SP]
    // 0xd348f4: r0 = Map._fromLiteral()
    //     0xd348f4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xd348f8: stur            x0, [fp, #-0x10]
    // 0xd348fc: r0 = _AssetManifestBin()
    //     0xd348fc: bl              #0xd34924  ; Allocate_AssetManifestBinStub -> _AssetManifestBin (size=0x10)
    // 0xd34900: ldur            x1, [fp, #-0x10]
    // 0xd34904: StoreField: r0->field_b = r1
    //     0xd34904: stur            w1, [x0, #0xb]
    // 0xd34908: ldur            x1, [fp, #-8]
    // 0xd3490c: StoreField: r0->field_7 = r1
    //     0xd3490c: stur            w1, [x0, #7]
    // 0xd34910: LeaveFrame
    //     0xd34910: mov             SP, fp
    //     0xd34914: ldp             fp, lr, [SP], #0x10
    // 0xd34918: ret
    //     0xd34918: ret             
    // 0xd3491c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3491c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd34920: b               #0xd348bc
  }
  _ getAssetVariants(/* No info */) {
    // ** addr: 0xd3527c, size: 0x2cc
    // 0xd3527c: EnterFrame
    //     0xd3527c: stp             fp, lr, [SP, #-0x10]!
    //     0xd35280: mov             fp, SP
    // 0xd35284: AllocStack(0x48)
    //     0xd35284: sub             SP, SP, #0x48
    // 0xd35288: SetupParameters(_AssetManifestBin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xd35288: stur            x1, [fp, #-8]
    //     0xd3528c: stur            x2, [fp, #-0x10]
    // 0xd35290: CheckStackOverflow
    //     0xd35290: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd35294: cmp             SP, x16
    //     0xd35298: b.ls            #0xd3553c
    // 0xd3529c: r1 = 1
    //     0xd3529c: movz            x1, #0x1
    // 0xd352a0: r0 = AllocateContext()
    //     0xd352a0: bl              #0xec126c  ; AllocateContextStub
    // 0xd352a4: ldur            x2, [fp, #-0x10]
    // 0xd352a8: stur            x0, [fp, #-0x20]
    // 0xd352ac: StoreField: r0->field_f = r2
    //     0xd352ac: stur            w2, [x0, #0xf]
    // 0xd352b0: ldur            x3, [fp, #-8]
    // 0xd352b4: LoadField: r4 = r3->field_b
    //     0xd352b4: ldur            w4, [x3, #0xb]
    // 0xd352b8: DecompressPointer r4
    //     0xd352b8: add             x4, x4, HEAP, lsl #32
    // 0xd352bc: mov             x1, x4
    // 0xd352c0: stur            x4, [fp, #-0x18]
    // 0xd352c4: r0 = containsKey()
    //     0xd352c4: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xd352c8: tbz             w0, #4, #0xd354f8
    // 0xd352cc: ldur            x0, [fp, #-8]
    // 0xd352d0: ldur            x3, [fp, #-0x20]
    // 0xd352d4: LoadField: r4 = r0->field_7
    //     0xd352d4: ldur            w4, [x0, #7]
    // 0xd352d8: DecompressPointer r4
    //     0xd352d8: add             x4, x4, HEAP, lsl #32
    // 0xd352dc: stur            x4, [fp, #-0x10]
    // 0xd352e0: LoadField: r2 = r3->field_f
    //     0xd352e0: ldur            w2, [x3, #0xf]
    // 0xd352e4: DecompressPointer r2
    //     0xd352e4: add             x2, x2, HEAP, lsl #32
    // 0xd352e8: r0 = LoadClassIdInstr(r4)
    //     0xd352e8: ldur            x0, [x4, #-1]
    //     0xd352ec: ubfx            x0, x0, #0xc, #0x14
    // 0xd352f0: mov             x1, x4
    // 0xd352f4: r0 = GDT[cid_x0 + -0x114]()
    //     0xd352f4: sub             lr, x0, #0x114
    //     0xd352f8: ldr             lr, [x21, lr, lsl #3]
    //     0xd352fc: blr             lr
    // 0xd35300: cmp             w0, NULL
    // 0xd35304: b.ne            #0xd35318
    // 0xd35308: r0 = Null
    //     0xd35308: mov             x0, NULL
    // 0xd3530c: LeaveFrame
    //     0xd3530c: mov             SP, fp
    //     0xd35310: ldp             fp, lr, [SP], #0x10
    // 0xd35314: ret
    //     0xd35314: ret             
    // 0xd35318: ldur            x3, [fp, #-0x20]
    // 0xd3531c: ldur            x4, [fp, #-0x10]
    // 0xd35320: LoadField: r5 = r3->field_f
    //     0xd35320: ldur            w5, [x3, #0xf]
    // 0xd35324: DecompressPointer r5
    //     0xd35324: add             x5, x5, HEAP, lsl #32
    // 0xd35328: stur            x5, [fp, #-8]
    // 0xd3532c: r0 = LoadClassIdInstr(r4)
    //     0xd3532c: ldur            x0, [x4, #-1]
    //     0xd35330: ubfx            x0, x0, #0xc, #0x14
    // 0xd35334: mov             x1, x4
    // 0xd35338: mov             x2, x5
    // 0xd3533c: r0 = GDT[cid_x0 + -0x114]()
    //     0xd3533c: sub             lr, x0, #0x114
    //     0xd35340: ldr             lr, [x21, lr, lsl #3]
    //     0xd35344: blr             lr
    // 0xd35348: cmp             w0, NULL
    // 0xd3534c: b.ne            #0xd35364
    // 0xd35350: r1 = <Object?>
    //     0xd35350: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xd35354: r2 = 0
    //     0xd35354: movz            x2, #0
    // 0xd35358: r0 = _GrowableList()
    //     0xd35358: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xd3535c: mov             x6, x0
    // 0xd35360: b               #0xd35368
    // 0xd35364: mov             x6, x0
    // 0xd35368: ldur            x3, [fp, #-0x20]
    // 0xd3536c: ldur            x5, [fp, #-0x18]
    // 0xd35370: ldur            x4, [fp, #-0x10]
    // 0xd35374: mov             x0, x6
    // 0xd35378: stur            x6, [fp, #-0x28]
    // 0xd3537c: r2 = Null
    //     0xd3537c: mov             x2, NULL
    // 0xd35380: r1 = Null
    //     0xd35380: mov             x1, NULL
    // 0xd35384: r8 = Iterable<Object?>
    //     0xd35384: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1cd70] Type: Iterable<Object?>
    //     0xd35388: ldr             x8, [x8, #0xd70]
    // 0xd3538c: r3 = Null
    //     0xd3538c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1cd78] Null
    //     0xd35390: ldr             x3, [x3, #0xd78]
    // 0xd35394: r0 = Iterable<Object?>()
    //     0xd35394: bl              #0x723b24  ; IsType_Iterable<Object?>_Stub
    // 0xd35398: ldur            x0, [fp, #-0x28]
    // 0xd3539c: r1 = LoadClassIdInstr(r0)
    //     0xd3539c: ldur            x1, [x0, #-1]
    //     0xd353a0: ubfx            x1, x1, #0xc, #0x14
    // 0xd353a4: r16 = <Map<Object?, Object?>>
    //     0xd353a4: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1cd88] TypeArguments: <Map<Object?, Object?>>
    //     0xd353a8: ldr             x16, [x16, #0xd88]
    // 0xd353ac: stp             x0, x16, [SP]
    // 0xd353b0: mov             x0, x1
    // 0xd353b4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xd353b4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xd353b8: r0 = GDT[cid_x0 + 0xf30c]()
    //     0xd353b8: movz            x17, #0xf30c
    //     0xd353bc: add             lr, x0, x17
    //     0xd353c0: ldr             lr, [x21, lr, lsl #3]
    //     0xd353c4: blr             lr
    // 0xd353c8: ldur            x2, [fp, #-0x20]
    // 0xd353cc: r1 = Function '<anonymous closure>':.
    //     0xd353cc: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1cd90] AnonymousClosure: (0xd35548), in [package:flutter/src/services/asset_manifest.dart] _AssetManifestBin::getAssetVariants (0xd3527c)
    //     0xd353d0: ldr             x1, [x1, #0xd90]
    // 0xd353d4: stur            x0, [fp, #-0x28]
    // 0xd353d8: r0 = AllocateClosure()
    //     0xd353d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xd353dc: mov             x1, x0
    // 0xd353e0: ldur            x0, [fp, #-0x28]
    // 0xd353e4: r2 = LoadClassIdInstr(r0)
    //     0xd353e4: ldur            x2, [x0, #-1]
    //     0xd353e8: ubfx            x2, x2, #0xc, #0x14
    // 0xd353ec: r16 = <AssetMetadata>
    //     0xd353ec: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1cd98] TypeArguments: <AssetMetadata>
    //     0xd353f0: ldr             x16, [x16, #0xd98]
    // 0xd353f4: stp             x0, x16, [SP, #8]
    // 0xd353f8: str             x1, [SP]
    // 0xd353fc: mov             x0, x2
    // 0xd35400: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xd35400: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xd35404: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xd35404: movz            x17, #0xf28c
    //     0xd35408: add             lr, x0, x17
    //     0xd3540c: ldr             lr, [x21, lr, lsl #3]
    //     0xd35410: blr             lr
    // 0xd35414: r1 = LoadClassIdInstr(r0)
    //     0xd35414: ldur            x1, [x0, #-1]
    //     0xd35418: ubfx            x1, x1, #0xc, #0x14
    // 0xd3541c: mov             x16, x0
    // 0xd35420: mov             x0, x1
    // 0xd35424: mov             x1, x16
    // 0xd35428: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xd35428: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xd3542c: r0 = GDT[cid_x0 + 0xd889]()
    //     0xd3542c: movz            x17, #0xd889
    //     0xd35430: add             lr, x0, x17
    //     0xd35434: ldr             lr, [x21, lr, lsl #3]
    //     0xd35438: blr             lr
    // 0xd3543c: mov             x4, x0
    // 0xd35440: ldur            x3, [fp, #-0x18]
    // 0xd35444: stur            x4, [fp, #-0x30]
    // 0xd35448: LoadField: r5 = r3->field_7
    //     0xd35448: ldur            w5, [x3, #7]
    // 0xd3544c: DecompressPointer r5
    //     0xd3544c: add             x5, x5, HEAP, lsl #32
    // 0xd35450: ldur            x0, [fp, #-8]
    // 0xd35454: mov             x2, x5
    // 0xd35458: stur            x5, [fp, #-0x28]
    // 0xd3545c: r1 = Null
    //     0xd3545c: mov             x1, NULL
    // 0xd35460: cmp             w2, NULL
    // 0xd35464: b.eq            #0xd35484
    // 0xd35468: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xd35468: ldur            w4, [x2, #0x17]
    // 0xd3546c: DecompressPointer r4
    //     0xd3546c: add             x4, x4, HEAP, lsl #32
    // 0xd35470: r8 = X0
    //     0xd35470: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xd35474: LoadField: r9 = r4->field_7
    //     0xd35474: ldur            x9, [x4, #7]
    // 0xd35478: r3 = Null
    //     0xd35478: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1cda0] Null
    //     0xd3547c: ldr             x3, [x3, #0xda0]
    // 0xd35480: blr             x9
    // 0xd35484: ldur            x0, [fp, #-0x30]
    // 0xd35488: ldur            x2, [fp, #-0x28]
    // 0xd3548c: r1 = Null
    //     0xd3548c: mov             x1, NULL
    // 0xd35490: cmp             w2, NULL
    // 0xd35494: b.eq            #0xd354b4
    // 0xd35498: LoadField: r4 = r2->field_1b
    //     0xd35498: ldur            w4, [x2, #0x1b]
    // 0xd3549c: DecompressPointer r4
    //     0xd3549c: add             x4, x4, HEAP, lsl #32
    // 0xd354a0: r8 = X1
    //     0xd354a0: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xd354a4: LoadField: r9 = r4->field_7
    //     0xd354a4: ldur            x9, [x4, #7]
    // 0xd354a8: r3 = Null
    //     0xd354a8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1cdb0] Null
    //     0xd354ac: ldr             x3, [x3, #0xdb0]
    // 0xd354b0: blr             x9
    // 0xd354b4: ldur            x1, [fp, #-0x18]
    // 0xd354b8: ldur            x2, [fp, #-8]
    // 0xd354bc: r0 = _hashCode()
    //     0xd354bc: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xd354c0: ldur            x1, [fp, #-0x18]
    // 0xd354c4: ldur            x2, [fp, #-8]
    // 0xd354c8: ldur            x3, [fp, #-0x30]
    // 0xd354cc: mov             x5, x0
    // 0xd354d0: r0 = _set()
    //     0xd354d0: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xd354d4: ldur            x3, [fp, #-0x20]
    // 0xd354d8: LoadField: r2 = r3->field_f
    //     0xd354d8: ldur            w2, [x3, #0xf]
    // 0xd354dc: DecompressPointer r2
    //     0xd354dc: add             x2, x2, HEAP, lsl #32
    // 0xd354e0: ldur            x1, [fp, #-0x10]
    // 0xd354e4: r0 = LoadClassIdInstr(r1)
    //     0xd354e4: ldur            x0, [x1, #-1]
    //     0xd354e8: ubfx            x0, x0, #0xc, #0x14
    // 0xd354ec: r0 = GDT[cid_x0 + 0x725]()
    //     0xd354ec: add             lr, x0, #0x725
    //     0xd354f0: ldr             lr, [x21, lr, lsl #3]
    //     0xd354f4: blr             lr
    // 0xd354f8: ldur            x0, [fp, #-0x20]
    // 0xd354fc: ldur            x3, [fp, #-0x18]
    // 0xd35500: LoadField: r2 = r0->field_f
    //     0xd35500: ldur            w2, [x0, #0xf]
    // 0xd35504: DecompressPointer r2
    //     0xd35504: add             x2, x2, HEAP, lsl #32
    // 0xd35508: mov             x1, x3
    // 0xd3550c: r0 = _getValueOrData()
    //     0xd3550c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xd35510: ldur            x1, [fp, #-0x18]
    // 0xd35514: LoadField: r2 = r1->field_f
    //     0xd35514: ldur            w2, [x1, #0xf]
    // 0xd35518: DecompressPointer r2
    //     0xd35518: add             x2, x2, HEAP, lsl #32
    // 0xd3551c: cmp             w2, w0
    // 0xd35520: b.ne            #0xd35528
    // 0xd35524: r0 = Null
    //     0xd35524: mov             x0, NULL
    // 0xd35528: cmp             w0, NULL
    // 0xd3552c: b.eq            #0xd35544
    // 0xd35530: LeaveFrame
    //     0xd35530: mov             SP, fp
    //     0xd35534: ldp             fp, lr, [SP], #0x10
    // 0xd35538: ret
    //     0xd35538: ret             
    // 0xd3553c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3553c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd35540: b               #0xd3529c
    // 0xd35544: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd35544: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AssetMetadata <anonymous closure>(dynamic, Map<Object?, Object?>) {
    // ** addr: 0xd35548, size: 0x1b4
    // 0xd35548: EnterFrame
    //     0xd35548: stp             fp, lr, [SP, #-0x10]!
    //     0xd3554c: mov             fp, SP
    // 0xd35550: AllocStack(0x30)
    //     0xd35550: sub             SP, SP, #0x30
    // 0xd35554: SetupParameters()
    //     0xd35554: ldr             x0, [fp, #0x18]
    //     0xd35558: ldur            w3, [x0, #0x17]
    //     0xd3555c: add             x3, x3, HEAP, lsl #32
    //     0xd35560: stur            x3, [fp, #-8]
    // 0xd35564: CheckStackOverflow
    //     0xd35564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd35568: cmp             SP, x16
    //     0xd3556c: b.ls            #0xd356ec
    // 0xd35570: ldr             x4, [fp, #0x10]
    // 0xd35574: r0 = LoadClassIdInstr(r4)
    //     0xd35574: ldur            x0, [x4, #-1]
    //     0xd35578: ubfx            x0, x0, #0xc, #0x14
    // 0xd3557c: mov             x1, x4
    // 0xd35580: r2 = "asset"
    //     0xd35580: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1cdc0] "asset"
    //     0xd35584: ldr             x2, [x2, #0xdc0]
    // 0xd35588: r0 = GDT[cid_x0 + -0x114]()
    //     0xd35588: sub             lr, x0, #0x114
    //     0xd3558c: ldr             lr, [x21, lr, lsl #3]
    //     0xd35590: blr             lr
    // 0xd35594: mov             x3, x0
    // 0xd35598: stur            x3, [fp, #-0x10]
    // 0xd3559c: cmp             w3, NULL
    // 0xd355a0: b.eq            #0xd356f4
    // 0xd355a4: mov             x0, x3
    // 0xd355a8: r2 = Null
    //     0xd355a8: mov             x2, NULL
    // 0xd355ac: r1 = Null
    //     0xd355ac: mov             x1, NULL
    // 0xd355b0: r4 = 60
    //     0xd355b0: movz            x4, #0x3c
    // 0xd355b4: branchIfSmi(r0, 0xd355c0)
    //     0xd355b4: tbz             w0, #0, #0xd355c0
    // 0xd355b8: r4 = LoadClassIdInstr(r0)
    //     0xd355b8: ldur            x4, [x0, #-1]
    //     0xd355bc: ubfx            x4, x4, #0xc, #0x14
    // 0xd355c0: sub             x4, x4, #0x5e
    // 0xd355c4: cmp             x4, #1
    // 0xd355c8: b.ls            #0xd355dc
    // 0xd355cc: r8 = String
    //     0xd355cc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xd355d0: r3 = Null
    //     0xd355d0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1cdc8] Null
    //     0xd355d4: ldr             x3, [x3, #0xdc8]
    // 0xd355d8: r0 = String()
    //     0xd355d8: bl              #0xed43b0  ; IsType_String_Stub
    // 0xd355dc: ldr             x3, [fp, #0x10]
    // 0xd355e0: r0 = LoadClassIdInstr(r3)
    //     0xd355e0: ldur            x0, [x3, #-1]
    //     0xd355e4: ubfx            x0, x0, #0xc, #0x14
    // 0xd355e8: mov             x1, x3
    // 0xd355ec: r2 = "dpr"
    //     0xd355ec: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1cdd8] "dpr"
    //     0xd355f0: ldr             x2, [x2, #0xdd8]
    // 0xd355f4: r0 = GDT[cid_x0 + -0x114]()
    //     0xd355f4: sub             lr, x0, #0x114
    //     0xd355f8: ldr             lr, [x21, lr, lsl #3]
    //     0xd355fc: blr             lr
    // 0xd35600: mov             x3, x0
    // 0xd35604: ldr             x1, [fp, #0x10]
    // 0xd35608: stur            x3, [fp, #-0x18]
    // 0xd3560c: r0 = LoadClassIdInstr(r1)
    //     0xd3560c: ldur            x0, [x1, #-1]
    //     0xd35610: ubfx            x0, x0, #0xc, #0x14
    // 0xd35614: r2 = "asset"
    //     0xd35614: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1cdc0] "asset"
    //     0xd35618: ldr             x2, [x2, #0xdc0]
    // 0xd3561c: r0 = GDT[cid_x0 + -0x114]()
    //     0xd3561c: sub             lr, x0, #0x114
    //     0xd35620: ldr             lr, [x21, lr, lsl #3]
    //     0xd35624: blr             lr
    // 0xd35628: mov             x3, x0
    // 0xd3562c: stur            x3, [fp, #-0x20]
    // 0xd35630: cmp             w3, NULL
    // 0xd35634: b.eq            #0xd356f8
    // 0xd35638: mov             x0, x3
    // 0xd3563c: r2 = Null
    //     0xd3563c: mov             x2, NULL
    // 0xd35640: r1 = Null
    //     0xd35640: mov             x1, NULL
    // 0xd35644: r4 = 60
    //     0xd35644: movz            x4, #0x3c
    // 0xd35648: branchIfSmi(r0, 0xd35654)
    //     0xd35648: tbz             w0, #0, #0xd35654
    // 0xd3564c: r4 = LoadClassIdInstr(r0)
    //     0xd3564c: ldur            x4, [x0, #-1]
    //     0xd35650: ubfx            x4, x4, #0xc, #0x14
    // 0xd35654: sub             x4, x4, #0x5e
    // 0xd35658: cmp             x4, #1
    // 0xd3565c: b.ls            #0xd35670
    // 0xd35660: r8 = String
    //     0xd35660: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xd35664: r3 = Null
    //     0xd35664: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1cde0] Null
    //     0xd35668: ldr             x3, [x3, #0xde0]
    // 0xd3566c: r0 = String()
    //     0xd3566c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xd35670: ldur            x0, [fp, #-0x18]
    // 0xd35674: r2 = Null
    //     0xd35674: mov             x2, NULL
    // 0xd35678: r1 = Null
    //     0xd35678: mov             x1, NULL
    // 0xd3567c: r4 = 60
    //     0xd3567c: movz            x4, #0x3c
    // 0xd35680: branchIfSmi(r0, 0xd3568c)
    //     0xd35680: tbz             w0, #0, #0xd3568c
    // 0xd35684: r4 = LoadClassIdInstr(r0)
    //     0xd35684: ldur            x4, [x0, #-1]
    //     0xd35688: ubfx            x4, x4, #0xc, #0x14
    // 0xd3568c: cmp             x4, #0x3e
    // 0xd35690: b.eq            #0xd356a4
    // 0xd35694: r8 = double?
    //     0xd35694: ldr             x8, [PP, #0x12d0]  ; [pp+0x12d0] Type: double?
    // 0xd35698: r3 = Null
    //     0xd35698: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1cdf0] Null
    //     0xd3569c: ldr             x3, [x3, #0xdf0]
    // 0xd356a0: r0 = double?()
    //     0xd356a0: bl              #0xed4434  ; IsType_double?_Stub
    // 0xd356a4: ldur            x0, [fp, #-8]
    // 0xd356a8: LoadField: r1 = r0->field_f
    //     0xd356a8: ldur            w1, [x0, #0xf]
    // 0xd356ac: DecompressPointer r1
    //     0xd356ac: add             x1, x1, HEAP, lsl #32
    // 0xd356b0: r0 = LoadClassIdInstr(r1)
    //     0xd356b0: ldur            x0, [x1, #-1]
    //     0xd356b4: ubfx            x0, x0, #0xc, #0x14
    // 0xd356b8: ldur            x16, [fp, #-0x10]
    // 0xd356bc: stp             x16, x1, [SP]
    // 0xd356c0: mov             lr, x0
    // 0xd356c4: ldr             lr, [x21, lr, lsl #3]
    // 0xd356c8: blr             lr
    // 0xd356cc: r0 = AssetMetadata()
    //     0xd356cc: bl              #0xd351ac  ; AllocateAssetMetadataStub -> AssetMetadata (size=0x10)
    // 0xd356d0: ldur            x1, [fp, #-0x20]
    // 0xd356d4: StoreField: r0->field_b = r1
    //     0xd356d4: stur            w1, [x0, #0xb]
    // 0xd356d8: ldur            x1, [fp, #-0x18]
    // 0xd356dc: StoreField: r0->field_7 = r1
    //     0xd356dc: stur            w1, [x0, #7]
    // 0xd356e0: LeaveFrame
    //     0xd356e0: mov             SP, fp
    //     0xd356e4: ldp             fp, lr, [SP], #0x10
    // 0xd356e8: ret
    //     0xd356e8: ret             
    // 0xd356ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd356ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd356f0: b               #0xd35570
    // 0xd356f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd356f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd356f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd356f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 2825, size: 0x8, field offset: 0x8
abstract class AssetManifest extends Object {

  static _ loadFromAssetBundle(/* No info */) {
    // ** addr: 0xd3426c, size: 0x54
    // 0xd3426c: EnterFrame
    //     0xd3426c: stp             fp, lr, [SP, #-0x10]!
    //     0xd34270: mov             fp, SP
    // 0xd34274: AllocStack(0x20)
    //     0xd34274: sub             SP, SP, #0x20
    // 0xd34278: CheckStackOverflow
    //     0xd34278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3427c: cmp             SP, x16
    //     0xd34280: b.ls            #0xd342b8
    // 0xd34284: r16 = <AssetManifest>
    //     0xd34284: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1ce38] TypeArguments: <AssetManifest>
    //     0xd34288: ldr             x16, [x16, #0xe38]
    // 0xd3428c: stp             x1, x16, [SP, #0x10]
    // 0xd34290: r16 = "AssetManifest.bin"
    //     0xd34290: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1ce40] "AssetManifest.bin"
    //     0xd34294: ldr             x16, [x16, #0xe40]
    // 0xd34298: r30 = Closure: (ByteData) => _AssetManifestBin from Function '<EMAIL>': static.
    //     0xd34298: add             lr, PP, #0x1c, lsl #12  ; [pp+0x1ce48] Closure: (ByteData) => _AssetManifestBin from Function '<EMAIL>': static. (0x7e54fb734870)
    //     0xd3429c: ldr             lr, [lr, #0xe48]
    // 0xd342a0: stp             lr, x16, [SP]
    // 0xd342a4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xd342a4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xd342a8: r0 = loadStructuredBinaryData()
    //     0xd342a8: bl              #0xd342c0  ; [package:flutter/src/services/asset_bundle.dart] CachingAssetBundle::loadStructuredBinaryData
    // 0xd342ac: LeaveFrame
    //     0xd342ac: mov             SP, fp
    //     0xd342b0: ldp             fp, lr, [SP], #0x10
    // 0xd342b4: ret
    //     0xd342b4: ret             
    // 0xd342b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd342b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd342bc: b               #0xd34284
  }
}
