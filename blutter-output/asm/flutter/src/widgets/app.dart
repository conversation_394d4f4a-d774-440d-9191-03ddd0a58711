// lib: , url: package:flutter/src/widgets/app.dart

// class id: 1049107, size: 0x8
class :: {

  static _ basicLocaleListResolution(/* No info */) {
    // ** addr: 0x67459c, size: 0x914
    // 0x67459c: EnterFrame
    //     0x67459c: stp             fp, lr, [SP, #-0x10]!
    //     0x6745a0: mov             fp, SP
    // 0x6745a4: AllocStack(0x80)
    //     0x6745a4: sub             SP, SP, #0x80
    // 0x6745a8: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0x6745a8: mov             x2, x1
    //     0x6745ac: stur            x1, [fp, #-8]
    // 0x6745b0: CheckStackOverflow
    //     0x6745b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6745b4: cmp             SP, x16
    //     0x6745b8: b.ls            #0x674e98
    // 0x6745bc: r0 = LoadClassIdInstr(r2)
    //     0x6745bc: ldur            x0, [x2, #-1]
    //     0x6745c0: ubfx            x0, x0, #0xc, #0x14
    // 0x6745c4: mov             x1, x2
    // 0x6745c8: r0 = GDT[cid_x0 + 0xe879]()
    //     0x6745c8: movz            x17, #0xe879
    //     0x6745cc: add             lr, x0, x17
    //     0x6745d0: ldr             lr, [x21, lr, lsl #3]
    //     0x6745d4: blr             lr
    // 0x6745d8: tbnz            w0, #4, #0x6745f0
    // 0x6745dc: r0 = Instance_Locale
    //     0x6745dc: add             x0, PP, #0x57, lsl #12  ; [pp+0x573b0] Obj!Locale@e26bf1
    //     0x6745e0: ldr             x0, [x0, #0x3b0]
    // 0x6745e4: LeaveFrame
    //     0x6745e4: mov             SP, fp
    //     0x6745e8: ldp             fp, lr, [SP], #0x10
    // 0x6745ec: ret
    //     0x6745ec: ret             
    // 0x6745f0: r1 = <String, Locale>
    //     0x6745f0: add             x1, PP, #0x57, lsl #12  ; [pp+0x573b8] TypeArguments: <String, Locale>
    //     0x6745f4: ldr             x1, [x1, #0x3b8]
    // 0x6745f8: r0 = _HashMap()
    //     0x6745f8: bl              #0x63a3f8  ; Allocate_HashMapStub -> _HashMap<X0, X1> (size=0x20)
    // 0x6745fc: stur            x0, [fp, #-0x10]
    // 0x674600: StoreField: r0->field_b = rZR
    //     0x674600: stur            xzr, [x0, #0xb]
    // 0x674604: ArrayStore: r0[0] = rZR  ; List_8
    //     0x674604: stur            xzr, [x0, #0x17]
    // 0x674608: r1 = <_HashMapEntry?>
    //     0x674608: ldr             x1, [PP, #0x1a10]  ; [pp+0x1a10] TypeArguments: <_HashMapEntry?>
    // 0x67460c: r2 = 16
    //     0x67460c: movz            x2, #0x10
    // 0x674610: r0 = AllocateArray()
    //     0x674610: bl              #0xec22fc  ; AllocateArrayStub
    // 0x674614: mov             x1, x0
    // 0x674618: ldur            x0, [fp, #-0x10]
    // 0x67461c: StoreField: r0->field_13 = r1
    //     0x67461c: stur            w1, [x0, #0x13]
    // 0x674620: r1 = <String, Locale>
    //     0x674620: add             x1, PP, #0x57, lsl #12  ; [pp+0x573b8] TypeArguments: <String, Locale>
    //     0x674624: ldr             x1, [x1, #0x3b8]
    // 0x674628: r0 = _HashMap()
    //     0x674628: bl              #0x63a3f8  ; Allocate_HashMapStub -> _HashMap<X0, X1> (size=0x20)
    // 0x67462c: stur            x0, [fp, #-0x18]
    // 0x674630: StoreField: r0->field_b = rZR
    //     0x674630: stur            xzr, [x0, #0xb]
    // 0x674634: ArrayStore: r0[0] = rZR  ; List_8
    //     0x674634: stur            xzr, [x0, #0x17]
    // 0x674638: r1 = <_HashMapEntry?>
    //     0x674638: ldr             x1, [PP, #0x1a10]  ; [pp+0x1a10] TypeArguments: <_HashMapEntry?>
    // 0x67463c: r2 = 16
    //     0x67463c: movz            x2, #0x10
    // 0x674640: r0 = AllocateArray()
    //     0x674640: bl              #0xec22fc  ; AllocateArrayStub
    // 0x674644: mov             x1, x0
    // 0x674648: ldur            x0, [fp, #-0x18]
    // 0x67464c: StoreField: r0->field_13 = r1
    //     0x67464c: stur            w1, [x0, #0x13]
    // 0x674650: r1 = <String, Locale>
    //     0x674650: add             x1, PP, #0x57, lsl #12  ; [pp+0x573b8] TypeArguments: <String, Locale>
    //     0x674654: ldr             x1, [x1, #0x3b8]
    // 0x674658: r0 = _HashMap()
    //     0x674658: bl              #0x63a3f8  ; Allocate_HashMapStub -> _HashMap<X0, X1> (size=0x20)
    // 0x67465c: stur            x0, [fp, #-0x20]
    // 0x674660: StoreField: r0->field_b = rZR
    //     0x674660: stur            xzr, [x0, #0xb]
    // 0x674664: ArrayStore: r0[0] = rZR  ; List_8
    //     0x674664: stur            xzr, [x0, #0x17]
    // 0x674668: r1 = <_HashMapEntry?>
    //     0x674668: ldr             x1, [PP, #0x1a10]  ; [pp+0x1a10] TypeArguments: <_HashMapEntry?>
    // 0x67466c: r2 = 16
    //     0x67466c: movz            x2, #0x10
    // 0x674670: r0 = AllocateArray()
    //     0x674670: bl              #0xec22fc  ; AllocateArrayStub
    // 0x674674: mov             x1, x0
    // 0x674678: ldur            x0, [fp, #-0x20]
    // 0x67467c: StoreField: r0->field_13 = r1
    //     0x67467c: stur            w1, [x0, #0x13]
    // 0x674680: r1 = <String, Locale>
    //     0x674680: add             x1, PP, #0x57, lsl #12  ; [pp+0x573b8] TypeArguments: <String, Locale>
    //     0x674684: ldr             x1, [x1, #0x3b8]
    // 0x674688: r0 = _HashMap()
    //     0x674688: bl              #0x63a3f8  ; Allocate_HashMapStub -> _HashMap<X0, X1> (size=0x20)
    // 0x67468c: stur            x0, [fp, #-0x28]
    // 0x674690: StoreField: r0->field_b = rZR
    //     0x674690: stur            xzr, [x0, #0xb]
    // 0x674694: ArrayStore: r0[0] = rZR  ; List_8
    //     0x674694: stur            xzr, [x0, #0x17]
    // 0x674698: r1 = <_HashMapEntry?>
    //     0x674698: ldr             x1, [PP, #0x1a10]  ; [pp+0x1a10] TypeArguments: <_HashMapEntry?>
    // 0x67469c: r2 = 16
    //     0x67469c: movz            x2, #0x10
    // 0x6746a0: r0 = AllocateArray()
    //     0x6746a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6746a4: mov             x1, x0
    // 0x6746a8: ldur            x0, [fp, #-0x28]
    // 0x6746ac: StoreField: r0->field_13 = r1
    //     0x6746ac: stur            w1, [x0, #0x13]
    // 0x6746b0: r1 = <String?, Locale>
    //     0x6746b0: add             x1, PP, #0x57, lsl #12  ; [pp+0x573c0] TypeArguments: <String?, Locale>
    //     0x6746b4: ldr             x1, [x1, #0x3c0]
    // 0x6746b8: r0 = _HashMap()
    //     0x6746b8: bl              #0x63a3f8  ; Allocate_HashMapStub -> _HashMap<X0, X1> (size=0x20)
    // 0x6746bc: stur            x0, [fp, #-0x30]
    // 0x6746c0: StoreField: r0->field_b = rZR
    //     0x6746c0: stur            xzr, [x0, #0xb]
    // 0x6746c4: ArrayStore: r0[0] = rZR  ; List_8
    //     0x6746c4: stur            xzr, [x0, #0x17]
    // 0x6746c8: r1 = <_HashMapEntry?>
    //     0x6746c8: ldr             x1, [PP, #0x1a10]  ; [pp+0x1a10] TypeArguments: <_HashMapEntry?>
    // 0x6746cc: r2 = 16
    //     0x6746cc: movz            x2, #0x10
    // 0x6746d0: r0 = AllocateArray()
    //     0x6746d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6746d4: mov             x1, x0
    // 0x6746d8: ldur            x0, [fp, #-0x30]
    // 0x6746dc: StoreField: r0->field_13 = r1
    //     0x6746dc: stur            w1, [x0, #0x13]
    // 0x6746e0: r1 = 0
    //     0x6746e0: movz            x1, #0
    // 0x6746e4: r3 = const [Instance of 'Locale']
    //     0x6746e4: add             x3, PP, #0x23, lsl #12  ; [pp+0x23bb0] List<Locale>(1)
    //     0x6746e8: ldr             x3, [x3, #0xbb0]
    // 0x6746ec: CheckStackOverflow
    //     0x6746ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6746f0: cmp             SP, x16
    //     0x6746f4: b.ls            #0x674ea0
    // 0x6746f8: cmp             x1, #1
    // 0x6746fc: b.ge            #0x6749c4
    // 0x674700: ArrayLoad: r4 = r3[r1]  ; Unknown_4
    //     0x674700: add             x16, x3, x1, lsl #2
    //     0x674704: ldur            w4, [x16, #0xf]
    // 0x674708: DecompressPointer r4
    //     0x674708: add             x4, x4, HEAP, lsl #32
    // 0x67470c: stur            x4, [fp, #-0x48]
    // 0x674710: add             x5, x1, #1
    // 0x674714: stur            x5, [fp, #-0x40]
    // 0x674718: LoadField: r6 = r4->field_7
    //     0x674718: ldur            w6, [x4, #7]
    // 0x67471c: DecompressPointer r6
    //     0x67471c: add             x6, x6, HEAP, lsl #32
    // 0x674720: mov             x2, x6
    // 0x674724: stur            x6, [fp, #-0x38]
    // 0x674728: r1 = _ConstMap len:78
    //     0x674728: ldr             x1, [PP, #0x47d8]  ; [pp+0x47d8] Map<String, String>(78)
    // 0x67472c: r0 = []()
    //     0x67472c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674730: cmp             w0, NULL
    // 0x674734: b.ne            #0x67473c
    // 0x674738: ldur            x0, [fp, #-0x38]
    // 0x67473c: ldur            x3, [fp, #-0x48]
    // 0x674740: stur            x0, [fp, #-0x50]
    // 0x674744: r1 = Null
    //     0x674744: mov             x1, NULL
    // 0x674748: r2 = 10
    //     0x674748: movz            x2, #0xa
    // 0x67474c: r0 = AllocateArray()
    //     0x67474c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x674750: mov             x3, x0
    // 0x674754: ldur            x0, [fp, #-0x50]
    // 0x674758: stur            x3, [fp, #-0x60]
    // 0x67475c: StoreField: r3->field_f = r0
    //     0x67475c: stur            w0, [x3, #0xf]
    // 0x674760: r16 = "_"
    //     0x674760: ldr             x16, [PP, #0x47c8]  ; [pp+0x47c8] "_"
    // 0x674764: StoreField: r3->field_13 = r16
    //     0x674764: stur            w16, [x3, #0x13]
    // 0x674768: ldur            x0, [fp, #-0x48]
    // 0x67476c: LoadField: r4 = r0->field_b
    //     0x67476c: ldur            w4, [x0, #0xb]
    // 0x674770: DecompressPointer r4
    //     0x674770: add             x4, x4, HEAP, lsl #32
    // 0x674774: stur            x4, [fp, #-0x58]
    // 0x674778: ArrayStore: r3[0] = r4  ; List_4
    //     0x674778: stur            w4, [x3, #0x17]
    // 0x67477c: r16 = "_"
    //     0x67477c: ldr             x16, [PP, #0x47c8]  ; [pp+0x47c8] "_"
    // 0x674780: StoreField: r3->field_1b = r16
    //     0x674780: stur            w16, [x3, #0x1b]
    // 0x674784: LoadField: r5 = r0->field_f
    //     0x674784: ldur            w5, [x0, #0xf]
    // 0x674788: DecompressPointer r5
    //     0x674788: add             x5, x5, HEAP, lsl #32
    // 0x67478c: mov             x2, x5
    // 0x674790: stur            x5, [fp, #-0x50]
    // 0x674794: r1 = _ConstMap len:6
    //     0x674794: ldr             x1, [PP, #0x47d0]  ; [pp+0x47d0] Map<String, String>(6)
    // 0x674798: r0 = []()
    //     0x674798: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x67479c: cmp             w0, NULL
    // 0x6747a0: b.ne            #0x6747a8
    // 0x6747a4: ldur            x0, [fp, #-0x50]
    // 0x6747a8: ldur            x1, [fp, #-0x60]
    // 0x6747ac: ArrayStore: r1[4] = r0  ; List_4
    //     0x6747ac: add             x25, x1, #0x1f
    //     0x6747b0: str             w0, [x25]
    //     0x6747b4: tbz             w0, #0, #0x6747d0
    //     0x6747b8: ldurb           w16, [x1, #-1]
    //     0x6747bc: ldurb           w17, [x0, #-1]
    //     0x6747c0: and             x16, x17, x16, lsr #2
    //     0x6747c4: tst             x16, HEAP, lsr #32
    //     0x6747c8: b.eq            #0x6747d0
    //     0x6747cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6747d0: ldur            x16, [fp, #-0x60]
    // 0x6747d4: str             x16, [SP]
    // 0x6747d8: r0 = _interpolate()
    //     0x6747d8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x6747dc: ldur            x1, [fp, #-0x10]
    // 0x6747e0: mov             x2, x0
    // 0x6747e4: stur            x0, [fp, #-0x60]
    // 0x6747e8: r0 = []()
    //     0x6747e8: bl              #0xd36ccc  ; [dart:collection] _HashMap::[]
    // 0x6747ec: cmp             w0, NULL
    // 0x6747f0: b.ne            #0x674804
    // 0x6747f4: ldur            x1, [fp, #-0x10]
    // 0x6747f8: ldur            x2, [fp, #-0x60]
    // 0x6747fc: ldur            x3, [fp, #-0x48]
    // 0x674800: r0 = []=()
    //     0x674800: bl              #0xd36648  ; [dart:collection] _HashMap::[]=
    // 0x674804: ldur            x2, [fp, #-0x38]
    // 0x674808: r1 = _ConstMap len:78
    //     0x674808: ldr             x1, [PP, #0x47d8]  ; [pp+0x47d8] Map<String, String>(78)
    // 0x67480c: r0 = []()
    //     0x67480c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674810: cmp             w0, NULL
    // 0x674814: b.ne            #0x674820
    // 0x674818: ldur            x3, [fp, #-0x38]
    // 0x67481c: b               #0x674824
    // 0x674820: mov             x3, x0
    // 0x674824: ldur            x0, [fp, #-0x58]
    // 0x674828: stur            x3, [fp, #-0x60]
    // 0x67482c: r1 = Null
    //     0x67482c: mov             x1, NULL
    // 0x674830: r2 = 6
    //     0x674830: movz            x2, #0x6
    // 0x674834: r0 = AllocateArray()
    //     0x674834: bl              #0xec22fc  ; AllocateArrayStub
    // 0x674838: mov             x1, x0
    // 0x67483c: ldur            x0, [fp, #-0x60]
    // 0x674840: StoreField: r1->field_f = r0
    //     0x674840: stur            w0, [x1, #0xf]
    // 0x674844: r16 = "_"
    //     0x674844: ldr             x16, [PP, #0x47c8]  ; [pp+0x47c8] "_"
    // 0x674848: StoreField: r1->field_13 = r16
    //     0x674848: stur            w16, [x1, #0x13]
    // 0x67484c: ldur            x0, [fp, #-0x58]
    // 0x674850: ArrayStore: r1[0] = r0  ; List_4
    //     0x674850: stur            w0, [x1, #0x17]
    // 0x674854: str             x1, [SP]
    // 0x674858: r0 = _interpolate()
    //     0x674858: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x67485c: ldur            x1, [fp, #-0x20]
    // 0x674860: mov             x2, x0
    // 0x674864: stur            x0, [fp, #-0x58]
    // 0x674868: r0 = []()
    //     0x674868: bl              #0xd36ccc  ; [dart:collection] _HashMap::[]
    // 0x67486c: cmp             w0, NULL
    // 0x674870: b.ne            #0x674884
    // 0x674874: ldur            x1, [fp, #-0x20]
    // 0x674878: ldur            x2, [fp, #-0x58]
    // 0x67487c: ldur            x3, [fp, #-0x48]
    // 0x674880: r0 = []=()
    //     0x674880: bl              #0xd36648  ; [dart:collection] _HashMap::[]=
    // 0x674884: ldur            x2, [fp, #-0x38]
    // 0x674888: r1 = _ConstMap len:78
    //     0x674888: ldr             x1, [PP, #0x47d8]  ; [pp+0x47d8] Map<String, String>(78)
    // 0x67488c: r0 = []()
    //     0x67488c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674890: cmp             w0, NULL
    // 0x674894: b.ne            #0x67489c
    // 0x674898: ldur            x0, [fp, #-0x38]
    // 0x67489c: stur            x0, [fp, #-0x58]
    // 0x6748a0: r1 = Null
    //     0x6748a0: mov             x1, NULL
    // 0x6748a4: r2 = 6
    //     0x6748a4: movz            x2, #0x6
    // 0x6748a8: r0 = AllocateArray()
    //     0x6748a8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6748ac: mov             x3, x0
    // 0x6748b0: ldur            x0, [fp, #-0x58]
    // 0x6748b4: stur            x3, [fp, #-0x60]
    // 0x6748b8: StoreField: r3->field_f = r0
    //     0x6748b8: stur            w0, [x3, #0xf]
    // 0x6748bc: r16 = "_"
    //     0x6748bc: ldr             x16, [PP, #0x47c8]  ; [pp+0x47c8] "_"
    // 0x6748c0: StoreField: r3->field_13 = r16
    //     0x6748c0: stur            w16, [x3, #0x13]
    // 0x6748c4: ldur            x2, [fp, #-0x50]
    // 0x6748c8: r1 = _ConstMap len:6
    //     0x6748c8: ldr             x1, [PP, #0x47d0]  ; [pp+0x47d0] Map<String, String>(6)
    // 0x6748cc: r0 = []()
    //     0x6748cc: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x6748d0: cmp             w0, NULL
    // 0x6748d4: b.ne            #0x6748dc
    // 0x6748d8: ldur            x0, [fp, #-0x50]
    // 0x6748dc: ldur            x1, [fp, #-0x60]
    // 0x6748e0: ArrayStore: r1[2] = r0  ; List_4
    //     0x6748e0: add             x25, x1, #0x17
    //     0x6748e4: str             w0, [x25]
    //     0x6748e8: tbz             w0, #0, #0x674904
    //     0x6748ec: ldurb           w16, [x1, #-1]
    //     0x6748f0: ldurb           w17, [x0, #-1]
    //     0x6748f4: and             x16, x17, x16, lsr #2
    //     0x6748f8: tst             x16, HEAP, lsr #32
    //     0x6748fc: b.eq            #0x674904
    //     0x674900: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x674904: ldur            x16, [fp, #-0x60]
    // 0x674908: str             x16, [SP]
    // 0x67490c: r0 = _interpolate()
    //     0x67490c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x674910: ldur            x1, [fp, #-0x18]
    // 0x674914: mov             x2, x0
    // 0x674918: stur            x0, [fp, #-0x58]
    // 0x67491c: r0 = []()
    //     0x67491c: bl              #0xd36ccc  ; [dart:collection] _HashMap::[]
    // 0x674920: cmp             w0, NULL
    // 0x674924: b.ne            #0x674938
    // 0x674928: ldur            x1, [fp, #-0x18]
    // 0x67492c: ldur            x2, [fp, #-0x58]
    // 0x674930: ldur            x3, [fp, #-0x48]
    // 0x674934: r0 = []=()
    //     0x674934: bl              #0xd36648  ; [dart:collection] _HashMap::[]=
    // 0x674938: ldur            x2, [fp, #-0x38]
    // 0x67493c: r1 = _ConstMap len:78
    //     0x67493c: ldr             x1, [PP, #0x47d8]  ; [pp+0x47d8] Map<String, String>(78)
    // 0x674940: r0 = []()
    //     0x674940: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674944: cmp             w0, NULL
    // 0x674948: b.ne            #0x674950
    // 0x67494c: ldur            x0, [fp, #-0x38]
    // 0x674950: ldur            x1, [fp, #-0x28]
    // 0x674954: mov             x2, x0
    // 0x674958: stur            x0, [fp, #-0x38]
    // 0x67495c: r0 = []()
    //     0x67495c: bl              #0xd36ccc  ; [dart:collection] _HashMap::[]
    // 0x674960: cmp             w0, NULL
    // 0x674964: b.ne            #0x674978
    // 0x674968: ldur            x1, [fp, #-0x28]
    // 0x67496c: ldur            x2, [fp, #-0x38]
    // 0x674970: ldur            x3, [fp, #-0x48]
    // 0x674974: r0 = []=()
    //     0x674974: bl              #0xd36648  ; [dart:collection] _HashMap::[]=
    // 0x674978: ldur            x2, [fp, #-0x50]
    // 0x67497c: r1 = _ConstMap len:6
    //     0x67497c: ldr             x1, [PP, #0x47d0]  ; [pp+0x47d0] Map<String, String>(6)
    // 0x674980: r0 = []()
    //     0x674980: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674984: cmp             w0, NULL
    // 0x674988: b.ne            #0x674990
    // 0x67498c: ldur            x0, [fp, #-0x50]
    // 0x674990: ldur            x1, [fp, #-0x30]
    // 0x674994: mov             x2, x0
    // 0x674998: stur            x0, [fp, #-0x38]
    // 0x67499c: r0 = []()
    //     0x67499c: bl              #0xd36ccc  ; [dart:collection] _HashMap::[]
    // 0x6749a0: cmp             w0, NULL
    // 0x6749a4: b.ne            #0x6749b8
    // 0x6749a8: ldur            x1, [fp, #-0x30]
    // 0x6749ac: ldur            x2, [fp, #-0x38]
    // 0x6749b0: ldur            x3, [fp, #-0x48]
    // 0x6749b4: r0 = []=()
    //     0x6749b4: bl              #0xd36648  ; [dart:collection] _HashMap::[]=
    // 0x6749b8: ldur            x1, [fp, #-0x40]
    // 0x6749bc: ldur            x0, [fp, #-0x30]
    // 0x6749c0: b               #0x6746e4
    // 0x6749c4: r4 = Null
    //     0x6749c4: mov             x4, NULL
    // 0x6749c8: r3 = Null
    //     0x6749c8: mov             x3, NULL
    // 0x6749cc: r2 = 0
    //     0x6749cc: movz            x2, #0
    // 0x6749d0: ldur            x1, [fp, #-8]
    // 0x6749d4: stur            x4, [fp, #-0x38]
    // 0x6749d8: stur            x3, [fp, #-0x48]
    // 0x6749dc: stur            x2, [fp, #-0x40]
    // 0x6749e0: CheckStackOverflow
    //     0x6749e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6749e4: cmp             SP, x16
    //     0x6749e8: b.ls            #0x674ea8
    // 0x6749ec: r0 = LoadClassIdInstr(r1)
    //     0x6749ec: ldur            x0, [x1, #-1]
    //     0x6749f0: ubfx            x0, x0, #0xc, #0x14
    // 0x6749f4: str             x1, [SP]
    // 0x6749f8: r0 = GDT[cid_x0 + 0xc834]()
    //     0x6749f8: movz            x17, #0xc834
    //     0x6749fc: add             lr, x0, x17
    //     0x674a00: ldr             lr, [x21, lr, lsl #3]
    //     0x674a04: blr             lr
    // 0x674a08: r1 = LoadInt32Instr(r0)
    //     0x674a08: sbfx            x1, x0, #1, #0x1f
    // 0x674a0c: ldur            x2, [fp, #-0x40]
    // 0x674a10: cmp             x2, x1
    // 0x674a14: b.ge            #0x674e64
    // 0x674a18: ldur            x3, [fp, #-8]
    // 0x674a1c: r0 = BoxInt64Instr(r2)
    //     0x674a1c: sbfiz           x0, x2, #1, #0x1f
    //     0x674a20: cmp             x2, x0, asr #1
    //     0x674a24: b.eq            #0x674a30
    //     0x674a28: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x674a2c: stur            x2, [x0, #7]
    // 0x674a30: r1 = LoadClassIdInstr(r3)
    //     0x674a30: ldur            x1, [x3, #-1]
    //     0x674a34: ubfx            x1, x1, #0xc, #0x14
    // 0x674a38: stp             x0, x3, [SP]
    // 0x674a3c: mov             x0, x1
    // 0x674a40: r0 = GDT[cid_x0 + 0x13037]()
    //     0x674a40: movz            x17, #0x3037
    //     0x674a44: movk            x17, #0x1, lsl #16
    //     0x674a48: add             lr, x0, x17
    //     0x674a4c: ldr             lr, [x21, lr, lsl #3]
    //     0x674a50: blr             lr
    // 0x674a54: stur            x0, [fp, #-0x58]
    // 0x674a58: LoadField: r3 = r0->field_7
    //     0x674a58: ldur            w3, [x0, #7]
    // 0x674a5c: DecompressPointer r3
    //     0x674a5c: add             x3, x3, HEAP, lsl #32
    // 0x674a60: mov             x2, x3
    // 0x674a64: stur            x3, [fp, #-0x50]
    // 0x674a68: r1 = _ConstMap len:78
    //     0x674a68: ldr             x1, [PP, #0x47d8]  ; [pp+0x47d8] Map<String, String>(78)
    // 0x674a6c: r0 = []()
    //     0x674a6c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674a70: cmp             w0, NULL
    // 0x674a74: b.ne            #0x674a80
    // 0x674a78: ldur            x3, [fp, #-0x50]
    // 0x674a7c: b               #0x674a84
    // 0x674a80: mov             x3, x0
    // 0x674a84: ldur            x0, [fp, #-0x58]
    // 0x674a88: stur            x3, [fp, #-0x60]
    // 0x674a8c: r1 = Null
    //     0x674a8c: mov             x1, NULL
    // 0x674a90: r2 = 10
    //     0x674a90: movz            x2, #0xa
    // 0x674a94: r0 = AllocateArray()
    //     0x674a94: bl              #0xec22fc  ; AllocateArrayStub
    // 0x674a98: mov             x3, x0
    // 0x674a9c: ldur            x0, [fp, #-0x60]
    // 0x674aa0: stur            x3, [fp, #-0x70]
    // 0x674aa4: StoreField: r3->field_f = r0
    //     0x674aa4: stur            w0, [x3, #0xf]
    // 0x674aa8: r16 = "_"
    //     0x674aa8: ldr             x16, [PP, #0x47c8]  ; [pp+0x47c8] "_"
    // 0x674aac: StoreField: r3->field_13 = r16
    //     0x674aac: stur            w16, [x3, #0x13]
    // 0x674ab0: ldur            x0, [fp, #-0x58]
    // 0x674ab4: LoadField: r4 = r0->field_b
    //     0x674ab4: ldur            w4, [x0, #0xb]
    // 0x674ab8: DecompressPointer r4
    //     0x674ab8: add             x4, x4, HEAP, lsl #32
    // 0x674abc: stur            x4, [fp, #-0x68]
    // 0x674ac0: ArrayStore: r3[0] = r4  ; List_4
    //     0x674ac0: stur            w4, [x3, #0x17]
    // 0x674ac4: r16 = "_"
    //     0x674ac4: ldr             x16, [PP, #0x47c8]  ; [pp+0x47c8] "_"
    // 0x674ac8: StoreField: r3->field_1b = r16
    //     0x674ac8: stur            w16, [x3, #0x1b]
    // 0x674acc: LoadField: r5 = r0->field_f
    //     0x674acc: ldur            w5, [x0, #0xf]
    // 0x674ad0: DecompressPointer r5
    //     0x674ad0: add             x5, x5, HEAP, lsl #32
    // 0x674ad4: mov             x2, x5
    // 0x674ad8: stur            x5, [fp, #-0x60]
    // 0x674adc: r1 = _ConstMap len:6
    //     0x674adc: ldr             x1, [PP, #0x47d0]  ; [pp+0x47d0] Map<String, String>(6)
    // 0x674ae0: r0 = []()
    //     0x674ae0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674ae4: cmp             w0, NULL
    // 0x674ae8: b.ne            #0x674af0
    // 0x674aec: ldur            x0, [fp, #-0x60]
    // 0x674af0: ldur            x1, [fp, #-0x70]
    // 0x674af4: ArrayStore: r1[4] = r0  ; List_4
    //     0x674af4: add             x25, x1, #0x1f
    //     0x674af8: str             w0, [x25]
    //     0x674afc: tbz             w0, #0, #0x674b18
    //     0x674b00: ldurb           w16, [x1, #-1]
    //     0x674b04: ldurb           w17, [x0, #-1]
    //     0x674b08: and             x16, x17, x16, lsr #2
    //     0x674b0c: tst             x16, HEAP, lsr #32
    //     0x674b10: b.eq            #0x674b18
    //     0x674b14: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x674b18: ldur            x16, [fp, #-0x70]
    // 0x674b1c: str             x16, [SP]
    // 0x674b20: r0 = _interpolate()
    //     0x674b20: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x674b24: ldur            x1, [fp, #-0x10]
    // 0x674b28: mov             x2, x0
    // 0x674b2c: r0 = containsKey()
    //     0x674b2c: bl              #0xd1b598  ; [dart:collection] _HashMap::containsKey
    // 0x674b30: tbz             w0, #4, #0x674e54
    // 0x674b34: ldur            x0, [fp, #-0x68]
    // 0x674b38: cmp             w0, NULL
    // 0x674b3c: b.eq            #0x674bb8
    // 0x674b40: ldur            x2, [fp, #-0x50]
    // 0x674b44: r1 = _ConstMap len:78
    //     0x674b44: ldr             x1, [PP, #0x47d8]  ; [pp+0x47d8] Map<String, String>(78)
    // 0x674b48: r0 = []()
    //     0x674b48: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674b4c: cmp             w0, NULL
    // 0x674b50: b.ne            #0x674b5c
    // 0x674b54: ldur            x3, [fp, #-0x50]
    // 0x674b58: b               #0x674b60
    // 0x674b5c: mov             x3, x0
    // 0x674b60: ldur            x0, [fp, #-0x68]
    // 0x674b64: stur            x3, [fp, #-0x70]
    // 0x674b68: r1 = Null
    //     0x674b68: mov             x1, NULL
    // 0x674b6c: r2 = 6
    //     0x674b6c: movz            x2, #0x6
    // 0x674b70: r0 = AllocateArray()
    //     0x674b70: bl              #0xec22fc  ; AllocateArrayStub
    // 0x674b74: mov             x1, x0
    // 0x674b78: ldur            x0, [fp, #-0x70]
    // 0x674b7c: StoreField: r1->field_f = r0
    //     0x674b7c: stur            w0, [x1, #0xf]
    // 0x674b80: r16 = "_"
    //     0x674b80: ldr             x16, [PP, #0x47c8]  ; [pp+0x47c8] "_"
    // 0x674b84: StoreField: r1->field_13 = r16
    //     0x674b84: stur            w16, [x1, #0x13]
    // 0x674b88: ldur            x0, [fp, #-0x68]
    // 0x674b8c: ArrayStore: r1[0] = r0  ; List_4
    //     0x674b8c: stur            w0, [x1, #0x17]
    // 0x674b90: str             x1, [SP]
    // 0x674b94: r0 = _interpolate()
    //     0x674b94: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x674b98: ldur            x1, [fp, #-0x20]
    // 0x674b9c: mov             x2, x0
    // 0x674ba0: r0 = []()
    //     0x674ba0: bl              #0xd36ccc  ; [dart:collection] _HashMap::[]
    // 0x674ba4: cmp             w0, NULL
    // 0x674ba8: b.eq            #0x674bb8
    // 0x674bac: LeaveFrame
    //     0x674bac: mov             SP, fp
    //     0x674bb0: ldp             fp, lr, [SP], #0x10
    // 0x674bb4: ret
    //     0x674bb4: ret             
    // 0x674bb8: ldur            x2, [fp, #-0x60]
    // 0x674bbc: r1 = _ConstMap len:6
    //     0x674bbc: ldr             x1, [PP, #0x47d0]  ; [pp+0x47d0] Map<String, String>(6)
    // 0x674bc0: r0 = []()
    //     0x674bc0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674bc4: cmp             w0, NULL
    // 0x674bc8: b.ne            #0x674bdc
    // 0x674bcc: ldur            x0, [fp, #-0x60]
    // 0x674bd0: cmp             w0, NULL
    // 0x674bd4: b.eq            #0x674c80
    // 0x674bd8: b               #0x674be0
    // 0x674bdc: ldur            x0, [fp, #-0x60]
    // 0x674be0: ldur            x2, [fp, #-0x50]
    // 0x674be4: r1 = _ConstMap len:78
    //     0x674be4: ldr             x1, [PP, #0x47d8]  ; [pp+0x47d8] Map<String, String>(78)
    // 0x674be8: r0 = []()
    //     0x674be8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674bec: cmp             w0, NULL
    // 0x674bf0: b.ne            #0x674bf8
    // 0x674bf4: ldur            x0, [fp, #-0x50]
    // 0x674bf8: stur            x0, [fp, #-0x68]
    // 0x674bfc: r1 = Null
    //     0x674bfc: mov             x1, NULL
    // 0x674c00: r2 = 6
    //     0x674c00: movz            x2, #0x6
    // 0x674c04: r0 = AllocateArray()
    //     0x674c04: bl              #0xec22fc  ; AllocateArrayStub
    // 0x674c08: mov             x3, x0
    // 0x674c0c: ldur            x0, [fp, #-0x68]
    // 0x674c10: stur            x3, [fp, #-0x70]
    // 0x674c14: StoreField: r3->field_f = r0
    //     0x674c14: stur            w0, [x3, #0xf]
    // 0x674c18: r16 = "_"
    //     0x674c18: ldr             x16, [PP, #0x47c8]  ; [pp+0x47c8] "_"
    // 0x674c1c: StoreField: r3->field_13 = r16
    //     0x674c1c: stur            w16, [x3, #0x13]
    // 0x674c20: ldur            x2, [fp, #-0x60]
    // 0x674c24: r1 = _ConstMap len:6
    //     0x674c24: ldr             x1, [PP, #0x47d0]  ; [pp+0x47d0] Map<String, String>(6)
    // 0x674c28: r0 = []()
    //     0x674c28: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674c2c: cmp             w0, NULL
    // 0x674c30: b.ne            #0x674c38
    // 0x674c34: ldur            x0, [fp, #-0x60]
    // 0x674c38: ldur            x1, [fp, #-0x70]
    // 0x674c3c: ArrayStore: r1[2] = r0  ; List_4
    //     0x674c3c: add             x25, x1, #0x17
    //     0x674c40: str             w0, [x25]
    //     0x674c44: tbz             w0, #0, #0x674c60
    //     0x674c48: ldurb           w16, [x1, #-1]
    //     0x674c4c: ldurb           w17, [x0, #-1]
    //     0x674c50: and             x16, x17, x16, lsr #2
    //     0x674c54: tst             x16, HEAP, lsr #32
    //     0x674c58: b.eq            #0x674c60
    //     0x674c5c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x674c60: ldur            x16, [fp, #-0x70]
    // 0x674c64: str             x16, [SP]
    // 0x674c68: r0 = _interpolate()
    //     0x674c68: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x674c6c: ldur            x1, [fp, #-0x18]
    // 0x674c70: mov             x2, x0
    // 0x674c74: r0 = []()
    //     0x674c74: bl              #0xd36ccc  ; [dart:collection] _HashMap::[]
    // 0x674c78: cmp             w0, NULL
    // 0x674c7c: b.ne            #0x674e48
    // 0x674c80: ldur            x0, [fp, #-0x38]
    // 0x674c84: cmp             w0, NULL
    // 0x674c88: b.ne            #0x674e38
    // 0x674c8c: ldur            x2, [fp, #-0x50]
    // 0x674c90: r1 = _ConstMap len:78
    //     0x674c90: ldr             x1, [PP, #0x47d8]  ; [pp+0x47d8] Map<String, String>(78)
    // 0x674c94: r0 = []()
    //     0x674c94: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674c98: cmp             w0, NULL
    // 0x674c9c: b.ne            #0x674ca8
    // 0x674ca0: ldur            x2, [fp, #-0x50]
    // 0x674ca4: b               #0x674cac
    // 0x674ca8: mov             x2, x0
    // 0x674cac: ldur            x1, [fp, #-0x28]
    // 0x674cb0: r0 = []()
    //     0x674cb0: bl              #0xd36ccc  ; [dart:collection] _HashMap::[]
    // 0x674cb4: mov             x1, x0
    // 0x674cb8: stur            x1, [fp, #-0x68]
    // 0x674cbc: cmp             w1, NULL
    // 0x674cc0: b.eq            #0x674da4
    // 0x674cc4: ldur            x2, [fp, #-0x40]
    // 0x674cc8: cbnz            x2, #0x674d9c
    // 0x674ccc: ldur            x3, [fp, #-8]
    // 0x674cd0: r0 = LoadClassIdInstr(r3)
    //     0x674cd0: ldur            x0, [x3, #-1]
    //     0x674cd4: ubfx            x0, x0, #0xc, #0x14
    // 0x674cd8: str             x3, [SP]
    // 0x674cdc: r0 = GDT[cid_x0 + 0xc834]()
    //     0x674cdc: movz            x17, #0xc834
    //     0x674ce0: add             lr, x0, x17
    //     0x674ce4: ldr             lr, [x21, lr, lsl #3]
    //     0x674ce8: blr             lr
    // 0x674cec: r1 = LoadInt32Instr(r0)
    //     0x674cec: sbfx            x1, x0, #1, #0x1f
    // 0x674cf0: cmp             x1, #1
    // 0x674cf4: b.le            #0x674d8c
    // 0x674cf8: ldur            x1, [fp, #-8]
    // 0x674cfc: r0 = LoadClassIdInstr(r1)
    //     0x674cfc: ldur            x0, [x1, #-1]
    //     0x674d00: ubfx            x0, x0, #0xc, #0x14
    // 0x674d04: r16 = 2
    //     0x674d04: movz            x16, #0x2
    // 0x674d08: stp             x16, x1, [SP]
    // 0x674d0c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x674d0c: movz            x17, #0x3037
    //     0x674d10: movk            x17, #0x1, lsl #16
    //     0x674d14: add             lr, x0, x17
    //     0x674d18: ldr             lr, [x21, lr, lsl #3]
    //     0x674d1c: blr             lr
    // 0x674d20: LoadField: r3 = r0->field_7
    //     0x674d20: ldur            w3, [x0, #7]
    // 0x674d24: DecompressPointer r3
    //     0x674d24: add             x3, x3, HEAP, lsl #32
    // 0x674d28: mov             x2, x3
    // 0x674d2c: stur            x3, [fp, #-0x70]
    // 0x674d30: r1 = _ConstMap len:78
    //     0x674d30: ldr             x1, [PP, #0x47d8]  ; [pp+0x47d8] Map<String, String>(78)
    // 0x674d34: r0 = []()
    //     0x674d34: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674d38: cmp             w0, NULL
    // 0x674d3c: b.ne            #0x674d44
    // 0x674d40: ldur            x0, [fp, #-0x70]
    // 0x674d44: ldur            x2, [fp, #-0x50]
    // 0x674d48: stur            x0, [fp, #-0x70]
    // 0x674d4c: r1 = _ConstMap len:78
    //     0x674d4c: ldr             x1, [PP, #0x47d8]  ; [pp+0x47d8] Map<String, String>(78)
    // 0x674d50: r0 = []()
    //     0x674d50: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674d54: cmp             w0, NULL
    // 0x674d58: b.ne            #0x674d64
    // 0x674d5c: ldur            x1, [fp, #-0x50]
    // 0x674d60: b               #0x674d68
    // 0x674d64: mov             x1, x0
    // 0x674d68: ldur            x0, [fp, #-0x70]
    // 0x674d6c: r2 = LoadClassIdInstr(r0)
    //     0x674d6c: ldur            x2, [x0, #-1]
    //     0x674d70: ubfx            x2, x2, #0xc, #0x14
    // 0x674d74: stp             x1, x0, [SP]
    // 0x674d78: mov             x0, x2
    // 0x674d7c: mov             lr, x0
    // 0x674d80: ldr             lr, [x21, lr, lsl #3]
    // 0x674d84: blr             lr
    // 0x674d88: tbz             w0, #4, #0x674d9c
    // 0x674d8c: ldur            x0, [fp, #-0x68]
    // 0x674d90: LeaveFrame
    //     0x674d90: mov             SP, fp
    //     0x674d94: ldp             fp, lr, [SP], #0x10
    // 0x674d98: ret
    //     0x674d98: ret             
    // 0x674d9c: ldur            x4, [fp, #-0x68]
    // 0x674da0: b               #0x674da8
    // 0x674da4: ldur            x4, [fp, #-0x38]
    // 0x674da8: ldur            x0, [fp, #-0x48]
    // 0x674dac: stur            x4, [fp, #-0x50]
    // 0x674db0: cmp             w0, NULL
    // 0x674db4: b.ne            #0x674e24
    // 0x674db8: ldur            x2, [fp, #-0x60]
    // 0x674dbc: r1 = _ConstMap len:6
    //     0x674dbc: ldr             x1, [PP, #0x47d0]  ; [pp+0x47d0] Map<String, String>(6)
    // 0x674dc0: r0 = []()
    //     0x674dc0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674dc4: cmp             w0, NULL
    // 0x674dc8: b.ne            #0x674ddc
    // 0x674dcc: ldur            x0, [fp, #-0x60]
    // 0x674dd0: cmp             w0, NULL
    // 0x674dd4: b.eq            #0x674e24
    // 0x674dd8: b               #0x674de0
    // 0x674ddc: ldur            x0, [fp, #-0x60]
    // 0x674de0: mov             x2, x0
    // 0x674de4: r1 = _ConstMap len:6
    //     0x674de4: ldr             x1, [PP, #0x47d0]  ; [pp+0x47d0] Map<String, String>(6)
    // 0x674de8: r0 = []()
    //     0x674de8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x674dec: cmp             w0, NULL
    // 0x674df0: b.ne            #0x674dfc
    // 0x674df4: ldur            x2, [fp, #-0x60]
    // 0x674df8: b               #0x674e00
    // 0x674dfc: mov             x2, x0
    // 0x674e00: ldur            x1, [fp, #-0x30]
    // 0x674e04: r0 = []()
    //     0x674e04: bl              #0xd36ccc  ; [dart:collection] _HashMap::[]
    // 0x674e08: cmp             w0, NULL
    // 0x674e0c: b.eq            #0x674e18
    // 0x674e10: mov             x1, x0
    // 0x674e14: b               #0x674e1c
    // 0x674e18: ldur            x1, [fp, #-0x48]
    // 0x674e1c: mov             x3, x1
    // 0x674e20: b               #0x674e28
    // 0x674e24: ldur            x3, [fp, #-0x48]
    // 0x674e28: ldur            x1, [fp, #-0x40]
    // 0x674e2c: add             x2, x1, #1
    // 0x674e30: ldur            x4, [fp, #-0x50]
    // 0x674e34: b               #0x6749d0
    // 0x674e38: ldur            x0, [fp, #-0x38]
    // 0x674e3c: LeaveFrame
    //     0x674e3c: mov             SP, fp
    //     0x674e40: ldp             fp, lr, [SP], #0x10
    // 0x674e44: ret
    //     0x674e44: ret             
    // 0x674e48: LeaveFrame
    //     0x674e48: mov             SP, fp
    //     0x674e4c: ldp             fp, lr, [SP], #0x10
    // 0x674e50: ret
    //     0x674e50: ret             
    // 0x674e54: ldur            x0, [fp, #-0x58]
    // 0x674e58: LeaveFrame
    //     0x674e58: mov             SP, fp
    //     0x674e5c: ldp             fp, lr, [SP], #0x10
    // 0x674e60: ret
    //     0x674e60: ret             
    // 0x674e64: ldur            x1, [fp, #-0x38]
    // 0x674e68: cmp             w1, NULL
    // 0x674e6c: b.ne            #0x674e74
    // 0x674e70: ldur            x1, [fp, #-0x48]
    // 0x674e74: cmp             w1, NULL
    // 0x674e78: b.ne            #0x674e88
    // 0x674e7c: r0 = Instance_Locale
    //     0x674e7c: add             x0, PP, #0x57, lsl #12  ; [pp+0x573b0] Obj!Locale@e26bf1
    //     0x674e80: ldr             x0, [x0, #0x3b0]
    // 0x674e84: b               #0x674e8c
    // 0x674e88: mov             x0, x1
    // 0x674e8c: LeaveFrame
    //     0x674e8c: mov             SP, fp
    //     0x674e90: ldp             fp, lr, [SP], #0x10
    // 0x674e94: ret
    //     0x674e94: ret             
    // 0x674e98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x674e98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x674e9c: b               #0x6745bc
    // 0x674ea0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x674ea0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x674ea4: b               #0x6746f8
    // 0x674ea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x674ea8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x674eac: b               #0x6749ec
  }
}

// class id: 4236, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class __WidgetsAppState&State&WidgetsBindingObserver extends State<dynamic>
     with WidgetsBindingObserver {
}

// class id: 4237, size: 0x24, field offset: 0x14
class _WidgetsAppState extends __WidgetsAppState&State&WidgetsBindingObserver {

  [closure] Route<dynamic>? _onGenerateRoute(dynamic, RouteSettings) {
    // ** addr: 0x659cdc, size: 0x3c
    // 0x659cdc: EnterFrame
    //     0x659cdc: stp             fp, lr, [SP, #-0x10]!
    //     0x659ce0: mov             fp, SP
    // 0x659ce4: ldr             x0, [fp, #0x18]
    // 0x659ce8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x659ce8: ldur            w1, [x0, #0x17]
    // 0x659cec: DecompressPointer r1
    //     0x659cec: add             x1, x1, HEAP, lsl #32
    // 0x659cf0: CheckStackOverflow
    //     0x659cf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x659cf4: cmp             SP, x16
    //     0x659cf8: b.ls            #0x659d10
    // 0x659cfc: ldr             x2, [fp, #0x10]
    // 0x659d00: r0 = _onGenerateRoute()
    //     0x659d00: bl              #0x659d3c  ; [package:flutter/src/widgets/app.dart] _WidgetsAppState::_onGenerateRoute
    // 0x659d04: LeaveFrame
    //     0x659d04: mov             SP, fp
    //     0x659d08: ldp             fp, lr, [SP], #0x10
    // 0x659d0c: ret
    //     0x659d0c: ret             
    // 0x659d10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x659d10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x659d14: b               #0x659cfc
  }
  _ _onGenerateRoute(/* No info */) {
    // ** addr: 0x659d3c, size: 0x164
    // 0x659d3c: EnterFrame
    //     0x659d3c: stp             fp, lr, [SP, #-0x10]!
    //     0x659d40: mov             fp, SP
    // 0x659d44: AllocStack(0x38)
    //     0x659d44: sub             SP, SP, #0x38
    // 0x659d48: SetupParameters(_WidgetsAppState this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x659d48: stur            x1, [fp, #-0x10]
    //     0x659d4c: stur            x2, [fp, #-0x18]
    // 0x659d50: CheckStackOverflow
    //     0x659d50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x659d54: cmp             SP, x16
    //     0x659d58: b.ls            #0x659e80
    // 0x659d5c: r0 = LoadClassIdInstr(r2)
    //     0x659d5c: ldur            x0, [x2, #-1]
    //     0x659d60: ubfx            x0, x0, #0xc, #0x14
    // 0x659d64: cmp             x0, #0xa51
    // 0x659d68: b.ne            #0x659d7c
    // 0x659d6c: LoadField: r0 = r2->field_7
    //     0x659d6c: ldur            w0, [x2, #7]
    // 0x659d70: DecompressPointer r0
    //     0x659d70: add             x0, x0, HEAP, lsl #32
    // 0x659d74: mov             x3, x0
    // 0x659d78: b               #0x659d88
    // 0x659d7c: LoadField: r0 = r2->field_6b
    //     0x659d7c: ldur            w0, [x2, #0x6b]
    // 0x659d80: DecompressPointer r0
    //     0x659d80: add             x0, x0, HEAP, lsl #32
    // 0x659d84: mov             x3, x0
    // 0x659d88: stur            x3, [fp, #-8]
    // 0x659d8c: r0 = LoadClassIdInstr(r3)
    //     0x659d8c: ldur            x0, [x3, #-1]
    //     0x659d90: ubfx            x0, x0, #0xc, #0x14
    // 0x659d94: r16 = "/"
    //     0x659d94: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x659d98: stp             x16, x3, [SP]
    // 0x659d9c: mov             lr, x0
    // 0x659da0: ldr             lr, [x21, lr, lsl #3]
    // 0x659da4: blr             lr
    // 0x659da8: tbnz            w0, #4, #0x659dc4
    // 0x659dac: ldur            x0, [fp, #-0x10]
    // 0x659db0: LoadField: r1 = r0->field_b
    //     0x659db0: ldur            w1, [x0, #0xb]
    // 0x659db4: DecompressPointer r1
    //     0x659db4: add             x1, x1, HEAP, lsl #32
    // 0x659db8: cmp             w1, NULL
    // 0x659dbc: b.eq            #0x659e88
    // 0x659dc0: b               #0x659dc8
    // 0x659dc4: ldur            x0, [fp, #-0x10]
    // 0x659dc8: LoadField: r1 = r0->field_b
    //     0x659dc8: ldur            w1, [x0, #0xb]
    // 0x659dcc: DecompressPointer r1
    //     0x659dcc: add             x1, x1, HEAP, lsl #32
    // 0x659dd0: cmp             w1, NULL
    // 0x659dd4: b.eq            #0x659e8c
    // 0x659dd8: ldur            x2, [fp, #-8]
    // 0x659ddc: r1 = _ConstMap len:0
    //     0x659ddc: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1aa60] Map<String, (dynamic this, BuildContext) => Widget>(0)
    //     0x659de0: ldr             x1, [x1, #0xa60]
    // 0x659de4: r0 = []()
    //     0x659de4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x659de8: cmp             w0, NULL
    // 0x659dec: b.eq            #0x659e3c
    // 0x659df0: ldur            x1, [fp, #-0x10]
    // 0x659df4: LoadField: r2 = r1->field_b
    //     0x659df4: ldur            w2, [x1, #0xb]
    // 0x659df8: DecompressPointer r2
    //     0x659df8: add             x2, x2, HEAP, lsl #32
    // 0x659dfc: cmp             w2, NULL
    // 0x659e00: b.eq            #0x659e90
    // 0x659e04: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x659e04: ldur            w1, [x2, #0x17]
    // 0x659e08: DecompressPointer r1
    //     0x659e08: add             x1, x1, HEAP, lsl #32
    // 0x659e0c: cmp             w1, NULL
    // 0x659e10: b.eq            #0x659e94
    // 0x659e14: stp             x1, NULL, [SP, #0x10]
    // 0x659e18: ldur            x16, [fp, #-0x18]
    // 0x659e1c: stp             x0, x16, [SP]
    // 0x659e20: mov             x0, x1
    // 0x659e24: ClosureCall
    //     0x659e24: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    //     0x659e28: ldur            x2, [x0, #0x1f]
    //     0x659e2c: blr             x2
    // 0x659e30: LeaveFrame
    //     0x659e30: mov             SP, fp
    //     0x659e34: ldp             fp, lr, [SP], #0x10
    // 0x659e38: ret
    //     0x659e38: ret             
    // 0x659e3c: ldur            x1, [fp, #-0x10]
    // 0x659e40: LoadField: r0 = r1->field_b
    //     0x659e40: ldur            w0, [x1, #0xb]
    // 0x659e44: DecompressPointer r0
    //     0x659e44: add             x0, x0, HEAP, lsl #32
    // 0x659e48: cmp             w0, NULL
    // 0x659e4c: b.eq            #0x659e98
    // 0x659e50: LoadField: r1 = r0->field_f
    //     0x659e50: ldur            w1, [x0, #0xf]
    // 0x659e54: DecompressPointer r1
    //     0x659e54: add             x1, x1, HEAP, lsl #32
    // 0x659e58: cmp             w1, NULL
    // 0x659e5c: b.eq            #0x659e9c
    // 0x659e60: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x659e60: ldur            w0, [x1, #0x17]
    // 0x659e64: DecompressPointer r0
    //     0x659e64: add             x0, x0, HEAP, lsl #32
    // 0x659e68: mov             x1, x0
    // 0x659e6c: ldur            x2, [fp, #-0x18]
    // 0x659e70: r0 = generator()
    //     0x659e70: bl              #0x659edc  ; [package:get/get_navigation/src/root/get_material_app.dart] GetMaterialApp::generator
    // 0x659e74: LeaveFrame
    //     0x659e74: mov             SP, fp
    //     0x659e78: ldp             fp, lr, [SP], #0x10
    // 0x659e7c: ret
    //     0x659e7c: ret             
    // 0x659e80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x659e80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x659e84: b               #0x659d5c
    // 0x659e88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x659e88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x659e8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x659e8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x659e90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x659e90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x659e94: r0 = NullErrorSharedWithoutFPURegs()
    //     0x659e94: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x659e98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x659e98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x659e9c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x659e9c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ didPopRoute(/* No info */) async {
    // ** addr: 0x673484, size: 0x88
    // 0x673484: EnterFrame
    //     0x673484: stp             fp, lr, [SP, #-0x10]!
    //     0x673488: mov             fp, SP
    // 0x67348c: AllocStack(0x20)
    //     0x67348c: sub             SP, SP, #0x20
    // 0x673490: SetupParameters(_WidgetsAppState this /* r1 => r1, fp-0x10 */)
    //     0x673490: stur            NULL, [fp, #-8]
    //     0x673494: stur            x1, [fp, #-0x10]
    // 0x673498: CheckStackOverflow
    //     0x673498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67349c: cmp             SP, x16
    //     0x6734a0: b.ls            #0x673500
    // 0x6734a4: InitAsync() -> Future<bool>
    //     0x6734a4: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0x6734a8: bl              #0x661298  ; InitAsyncStub
    // 0x6734ac: ldur            x0, [fp, #-0x10]
    // 0x6734b0: LoadField: r1 = r0->field_b
    //     0x6734b0: ldur            w1, [x0, #0xb]
    // 0x6734b4: DecompressPointer r1
    //     0x6734b4: add             x1, x1, HEAP, lsl #32
    // 0x6734b8: cmp             w1, NULL
    // 0x6734bc: b.eq            #0x673508
    // 0x6734c0: LoadField: r1 = r0->field_1b
    //     0x6734c0: ldur            w1, [x0, #0x1b]
    // 0x6734c4: DecompressPointer r1
    //     0x6734c4: add             x1, x1, HEAP, lsl #32
    // 0x6734c8: cmp             w1, NULL
    // 0x6734cc: b.ne            #0x6734d8
    // 0x6734d0: r0 = Null
    //     0x6734d0: mov             x0, NULL
    // 0x6734d4: b               #0x6734dc
    // 0x6734d8: r0 = currentState()
    //     0x6734d8: bl              #0x658c30  ; [package:flutter/src/widgets/framework.dart] GlobalKey::currentState
    // 0x6734dc: cmp             w0, NULL
    // 0x6734e0: b.ne            #0x6734ec
    // 0x6734e4: r0 = false
    //     0x6734e4: add             x0, NULL, #0x30  ; false
    // 0x6734e8: r0 = ReturnAsyncNotFuture()
    //     0x6734e8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x6734ec: r16 = <Object?>
    //     0x6734ec: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x6734f0: stp             x0, x16, [SP]
    // 0x6734f4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x6734f4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x6734f8: r0 = maybePop()
    //     0x6734f8: bl              #0x67350c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::maybePop
    // 0x6734fc: r0 = ReturnAsync()
    //     0x6734fc: b               #0x6576a4  ; ReturnAsyncStub
    // 0x673500: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x673500: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x673504: b               #0x6734a4
    // 0x673508: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x673508: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeLocales(/* No info */) {
    // ** addr: 0x67448c, size: 0xc8
    // 0x67448c: EnterFrame
    //     0x67448c: stp             fp, lr, [SP, #-0x10]!
    //     0x674490: mov             fp, SP
    // 0x674494: AllocStack(0x28)
    //     0x674494: sub             SP, SP, #0x28
    // 0x674498: SetupParameters(_WidgetsAppState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x674498: stur            x1, [fp, #-8]
    //     0x67449c: stur            x2, [fp, #-0x10]
    // 0x6744a0: CheckStackOverflow
    //     0x6744a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6744a4: cmp             SP, x16
    //     0x6744a8: b.ls            #0x674548
    // 0x6744ac: r1 = 2
    //     0x6744ac: movz            x1, #0x2
    // 0x6744b0: r0 = AllocateContext()
    //     0x6744b0: bl              #0xec126c  ; AllocateContextStub
    // 0x6744b4: mov             x3, x0
    // 0x6744b8: ldur            x0, [fp, #-8]
    // 0x6744bc: stur            x3, [fp, #-0x18]
    // 0x6744c0: StoreField: r3->field_f = r0
    //     0x6744c0: stur            w0, [x3, #0xf]
    // 0x6744c4: LoadField: r1 = r0->field_b
    //     0x6744c4: ldur            w1, [x0, #0xb]
    // 0x6744c8: DecompressPointer r1
    //     0x6744c8: add             x1, x1, HEAP, lsl #32
    // 0x6744cc: cmp             w1, NULL
    // 0x6744d0: b.eq            #0x674550
    // 0x6744d4: mov             x1, x0
    // 0x6744d8: ldur            x2, [fp, #-0x10]
    // 0x6744dc: r0 = _resolveLocales()
    //     0x6744dc: bl              #0x674554  ; [package:flutter/src/widgets/app.dart] _WidgetsAppState::_resolveLocales
    // 0x6744e0: mov             x1, x0
    // 0x6744e4: ldur            x2, [fp, #-0x18]
    // 0x6744e8: StoreField: r2->field_13 = r0
    //     0x6744e8: stur            w0, [x2, #0x13]
    //     0x6744ec: ldurb           w16, [x2, #-1]
    //     0x6744f0: ldurb           w17, [x0, #-1]
    //     0x6744f4: and             x16, x17, x16, lsr #2
    //     0x6744f8: tst             x16, HEAP, lsr #32
    //     0x6744fc: b.eq            #0x674504
    //     0x674500: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x674504: ldur            x0, [fp, #-8]
    // 0x674508: LoadField: r3 = r0->field_1f
    //     0x674508: ldur            w3, [x0, #0x1f]
    // 0x67450c: DecompressPointer r3
    //     0x67450c: add             x3, x3, HEAP, lsl #32
    // 0x674510: stp             x3, x1, [SP]
    // 0x674514: r0 = ==()
    //     0x674514: bl              #0xd3987c  ; [dart:ui] Locale::==
    // 0x674518: tbz             w0, #4, #0x674538
    // 0x67451c: ldur            x2, [fp, #-0x18]
    // 0x674520: r1 = Function '<anonymous closure>':.
    //     0x674520: add             x1, PP, #0x57, lsl #12  ; [pp+0x57438] AnonymousClosure: (0x674f50), in [package:flutter/src/widgets/app.dart] _WidgetsAppState::didChangeLocales (0x67448c)
    //     0x674524: ldr             x1, [x1, #0x438]
    // 0x674528: r0 = AllocateClosure()
    //     0x674528: bl              #0xec1630  ; AllocateClosureStub
    // 0x67452c: ldur            x1, [fp, #-8]
    // 0x674530: mov             x2, x0
    // 0x674534: r0 = setState()
    //     0x674534: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x674538: r0 = Null
    //     0x674538: mov             x0, NULL
    // 0x67453c: LeaveFrame
    //     0x67453c: mov             SP, fp
    //     0x674540: ldp             fp, lr, [SP], #0x10
    // 0x674544: ret
    //     0x674544: ret             
    // 0x674548: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x674548: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67454c: b               #0x6744ac
    // 0x674550: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x674550: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _resolveLocales(/* No info */) {
    // ** addr: 0x674554, size: 0x48
    // 0x674554: EnterFrame
    //     0x674554: stp             fp, lr, [SP, #-0x10]!
    //     0x674558: mov             fp, SP
    // 0x67455c: mov             x0, x1
    // 0x674560: mov             x1, x2
    // 0x674564: CheckStackOverflow
    //     0x674564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x674568: cmp             SP, x16
    //     0x67456c: b.ls            #0x674590
    // 0x674570: LoadField: r2 = r0->field_b
    //     0x674570: ldur            w2, [x0, #0xb]
    // 0x674574: DecompressPointer r2
    //     0x674574: add             x2, x2, HEAP, lsl #32
    // 0x674578: cmp             w2, NULL
    // 0x67457c: b.eq            #0x674598
    // 0x674580: r0 = basicLocaleListResolution()
    //     0x674580: bl              #0x67459c  ; [package:flutter/src/widgets/app.dart] ::basicLocaleListResolution
    // 0x674584: LeaveFrame
    //     0x674584: mov             SP, fp
    //     0x674588: ldp             fp, lr, [SP], #0x10
    // 0x67458c: ret
    //     0x67458c: ret             
    // 0x674590: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x674590: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x674594: b               #0x674570
    // 0x674598: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x674598: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x674f50, size: 0x48
    // 0x674f50: ldr             x1, [SP]
    // 0x674f54: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x674f54: ldur            w2, [x1, #0x17]
    // 0x674f58: DecompressPointer r2
    //     0x674f58: add             x2, x2, HEAP, lsl #32
    // 0x674f5c: LoadField: r1 = r2->field_f
    //     0x674f5c: ldur            w1, [x2, #0xf]
    // 0x674f60: DecompressPointer r1
    //     0x674f60: add             x1, x1, HEAP, lsl #32
    // 0x674f64: LoadField: r0 = r2->field_13
    //     0x674f64: ldur            w0, [x2, #0x13]
    // 0x674f68: DecompressPointer r0
    //     0x674f68: add             x0, x0, HEAP, lsl #32
    // 0x674f6c: StoreField: r1->field_1f = r0
    //     0x674f6c: stur            w0, [x1, #0x1f]
    //     0x674f70: ldurb           w16, [x1, #-1]
    //     0x674f74: ldurb           w17, [x0, #-1]
    //     0x674f78: and             x16, x17, x16, lsr #2
    //     0x674f7c: tst             x16, HEAP, lsr #32
    //     0x674f80: b.eq            #0x674f90
    //     0x674f84: str             lr, [SP, #-8]!
    //     0x674f88: bl              #0xec0a28  ; WriteBarrierWrappersStub
    //     0x674f8c: ldr             lr, [SP], #8
    // 0x674f90: r0 = Null
    //     0x674f90: mov             x0, NULL
    // 0x674f94: ret
    //     0x674f94: ret             
  }
  _ didPushRouteInformation(/* No info */) async {
    // ** addr: 0x6761d4, size: 0x1f8
    // 0x6761d4: EnterFrame
    //     0x6761d4: stp             fp, lr, [SP, #-0x10]!
    //     0x6761d8: mov             fp, SP
    // 0x6761dc: AllocStack(0x40)
    //     0x6761dc: sub             SP, SP, #0x40
    // 0x6761e0: SetupParameters(_WidgetsAppState this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x6761e0: stur            NULL, [fp, #-8]
    //     0x6761e4: stur            x1, [fp, #-0x10]
    //     0x6761e8: stur            x2, [fp, #-0x18]
    // 0x6761ec: CheckStackOverflow
    //     0x6761ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6761f0: cmp             SP, x16
    //     0x6761f4: b.ls            #0x6763c0
    // 0x6761f8: InitAsync() -> Future<bool>
    //     0x6761f8: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0x6761fc: bl              #0x661298  ; InitAsyncStub
    // 0x676200: ldur            x0, [fp, #-0x10]
    // 0x676204: LoadField: r1 = r0->field_b
    //     0x676204: ldur            w1, [x0, #0xb]
    // 0x676208: DecompressPointer r1
    //     0x676208: add             x1, x1, HEAP, lsl #32
    // 0x67620c: cmp             w1, NULL
    // 0x676210: b.eq            #0x6763c8
    // 0x676214: LoadField: r1 = r0->field_1b
    //     0x676214: ldur            w1, [x0, #0x1b]
    // 0x676218: DecompressPointer r1
    //     0x676218: add             x1, x1, HEAP, lsl #32
    // 0x67621c: cmp             w1, NULL
    // 0x676220: b.ne            #0x67622c
    // 0x676224: r2 = Null
    //     0x676224: mov             x2, NULL
    // 0x676228: b               #0x676234
    // 0x67622c: r0 = currentState()
    //     0x67622c: bl              #0x658c30  ; [package:flutter/src/widgets/framework.dart] GlobalKey::currentState
    // 0x676230: mov             x2, x0
    // 0x676234: stur            x2, [fp, #-0x20]
    // 0x676238: cmp             w2, NULL
    // 0x67623c: b.ne            #0x676248
    // 0x676240: r0 = false
    //     0x676240: add             x0, NULL, #0x30  ; false
    // 0x676244: r0 = ReturnAsyncNotFuture()
    //     0x676244: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x676248: ldur            x0, [fp, #-0x18]
    // 0x67624c: LoadField: r3 = r0->field_7
    //     0x67624c: ldur            w3, [x0, #7]
    // 0x676250: DecompressPointer r3
    //     0x676250: add             x3, x3, HEAP, lsl #32
    // 0x676254: stur            x3, [fp, #-0x10]
    // 0x676258: r0 = LoadClassIdInstr(r3)
    //     0x676258: ldur            x0, [x3, #-1]
    //     0x67625c: ubfx            x0, x0, #0xc, #0x14
    // 0x676260: mov             x1, x3
    // 0x676264: r0 = GDT[cid_x0 + -0xffa]()
    //     0x676264: sub             lr, x0, #0xffa
    //     0x676268: ldr             lr, [x21, lr, lsl #3]
    //     0x67626c: blr             lr
    // 0x676270: LoadField: r1 = r0->field_7
    //     0x676270: ldur            w1, [x0, #7]
    // 0x676274: cbnz            w1, #0x676280
    // 0x676278: r3 = "/"
    //     0x676278: ldr             x3, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x67627c: b               #0x6762a0
    // 0x676280: ldur            x2, [fp, #-0x10]
    // 0x676284: r0 = LoadClassIdInstr(r2)
    //     0x676284: ldur            x0, [x2, #-1]
    //     0x676288: ubfx            x0, x0, #0xc, #0x14
    // 0x67628c: mov             x1, x2
    // 0x676290: r0 = GDT[cid_x0 + -0xffa]()
    //     0x676290: sub             lr, x0, #0xffa
    //     0x676294: ldr             lr, [x21, lr, lsl #3]
    //     0x676298: blr             lr
    // 0x67629c: mov             x3, x0
    // 0x6762a0: ldur            x2, [fp, #-0x10]
    // 0x6762a4: stur            x3, [fp, #-0x18]
    // 0x6762a8: r0 = LoadClassIdInstr(r2)
    //     0x6762a8: ldur            x0, [x2, #-1]
    //     0x6762ac: ubfx            x0, x0, #0xc, #0x14
    // 0x6762b0: mov             x1, x2
    // 0x6762b4: r0 = GDT[cid_x0 + -0xfd7]()
    //     0x6762b4: sub             lr, x0, #0xfd7
    //     0x6762b8: ldr             lr, [x21, lr, lsl #3]
    //     0x6762bc: blr             lr
    // 0x6762c0: r1 = LoadClassIdInstr(r0)
    //     0x6762c0: ldur            x1, [x0, #-1]
    //     0x6762c4: ubfx            x1, x1, #0xc, #0x14
    // 0x6762c8: mov             x16, x0
    // 0x6762cc: mov             x0, x1
    // 0x6762d0: mov             x1, x16
    // 0x6762d4: r0 = GDT[cid_x0 + 0x667]()
    //     0x6762d4: add             lr, x0, #0x667
    //     0x6762d8: ldr             lr, [x21, lr, lsl #3]
    //     0x6762dc: blr             lr
    // 0x6762e0: tbnz            w0, #4, #0x6762ec
    // 0x6762e4: r3 = Null
    //     0x6762e4: mov             x3, NULL
    // 0x6762e8: b               #0x67630c
    // 0x6762ec: ldur            x2, [fp, #-0x10]
    // 0x6762f0: r0 = LoadClassIdInstr(r2)
    //     0x6762f0: ldur            x0, [x2, #-1]
    //     0x6762f4: ubfx            x0, x0, #0xc, #0x14
    // 0x6762f8: mov             x1, x2
    // 0x6762fc: r0 = GDT[cid_x0 + -0xfd7]()
    //     0x6762fc: sub             lr, x0, #0xfd7
    //     0x676300: ldr             lr, [x21, lr, lsl #3]
    //     0x676304: blr             lr
    // 0x676308: mov             x3, x0
    // 0x67630c: ldur            x2, [fp, #-0x10]
    // 0x676310: stur            x3, [fp, #-0x28]
    // 0x676314: r0 = LoadClassIdInstr(r2)
    //     0x676314: ldur            x0, [x2, #-1]
    //     0x676318: ubfx            x0, x0, #0xc, #0x14
    // 0x67631c: mov             x1, x2
    // 0x676320: r0 = GDT[cid_x0 + -0xfd3]()
    //     0x676320: sub             lr, x0, #0xfd3
    //     0x676324: ldr             lr, [x21, lr, lsl #3]
    //     0x676328: blr             lr
    // 0x67632c: LoadField: r1 = r0->field_7
    //     0x67632c: ldur            w1, [x0, #7]
    // 0x676330: cbnz            w1, #0x67633c
    // 0x676334: r0 = Null
    //     0x676334: mov             x0, NULL
    // 0x676338: b               #0x676354
    // 0x67633c: ldur            x1, [fp, #-0x10]
    // 0x676340: r0 = LoadClassIdInstr(r1)
    //     0x676340: ldur            x0, [x1, #-1]
    //     0x676344: ubfx            x0, x0, #0xc, #0x14
    // 0x676348: r0 = GDT[cid_x0 + -0xfd3]()
    //     0x676348: sub             lr, x0, #0xfd3
    //     0x67634c: ldr             lr, [x21, lr, lsl #3]
    //     0x676350: blr             lr
    // 0x676354: ldur            x16, [fp, #-0x18]
    // 0x676358: ldur            lr, [fp, #-0x28]
    // 0x67635c: stp             lr, x16, [SP, #8]
    // 0x676360: str             x0, [SP]
    // 0x676364: r1 = Null
    //     0x676364: mov             x1, NULL
    // 0x676368: r4 = const [0, 0x4, 0x3, 0x1, fragment, 0x3, path, 0x1, queryParameters, 0x2, null]
    //     0x676368: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d818] List(11) [0, 0x4, 0x3, 0x1, "fragment", 0x3, "path", 0x1, "queryParameters", 0x2, Null]
    //     0x67636c: ldr             x4, [x4, #0x818]
    // 0x676370: r0 = _Uri()
    //     0x676370: bl              #0x5ff47c  ; [dart:core] _Uri::_Uri
    // 0x676374: mov             x1, x0
    // 0x676378: LoadField: r0 = r1->field_23
    //     0x676378: ldur            w0, [x1, #0x23]
    // 0x67637c: DecompressPointer r0
    //     0x67637c: add             x0, x0, HEAP, lsl #32
    // 0x676380: r16 = Sentinel
    //     0x676380: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x676384: cmp             w0, w16
    // 0x676388: b.ne            #0x676398
    // 0x67638c: r2 = _text
    //     0x67638c: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a498] Field <_Uri@0150898._text@0150898>: late final (offset: 0x24)
    //     0x676390: ldr             x2, [x2, #0x498]
    // 0x676394: r0 = InitLateFinalInstanceField()
    //     0x676394: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x676398: mov             x1, x0
    // 0x67639c: r0 = decodeComponent()
    //     0x67639c: bl              #0x6763cc  ; [dart:core] Uri::decodeComponent
    // 0x6763a0: r16 = <Object?>
    //     0x6763a0: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x6763a4: ldur            lr, [fp, #-0x20]
    // 0x6763a8: stp             lr, x16, [SP, #8]
    // 0x6763ac: str             x0, [SP]
    // 0x6763b0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6763b0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6763b4: r0 = pushNamed()
    //     0x6763b4: bl              #0x65978c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pushNamed
    // 0x6763b8: r0 = true
    //     0x6763b8: add             x0, NULL, #0x20  ; true
    // 0x6763bc: r0 = ReturnAsyncNotFuture()
    //     0x6763bc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x6763c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6763c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6763c4: b               #0x6761f8
    // 0x6763c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6763c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeAppLifecycleState(/* No info */) {
    // ** addr: 0x676828, size: 0x30
    // 0x676828: mov             x0, x2
    // 0x67682c: StoreField: r1->field_13 = r0
    //     0x67682c: stur            w0, [x1, #0x13]
    //     0x676830: ldurb           w16, [x1, #-1]
    //     0x676834: ldurb           w17, [x0, #-1]
    //     0x676838: and             x16, x17, x16, lsr #2
    //     0x67683c: tst             x16, HEAP, lsr #32
    //     0x676840: b.eq            #0x676850
    //     0x676844: str             lr, [SP, #-8]!
    //     0x676848: bl              #0xec0a28  ; WriteBarrierWrappersStub
    //     0x67684c: ldr             lr, [SP], #8
    // 0x676850: r0 = Null
    //     0x676850: mov             x0, NULL
    // 0x676854: ret
    //     0x676854: ret             
  }
  _ initState(/* No info */) {
    // ** addr: 0x93ccfc, size: 0x1cc
    // 0x93ccfc: EnterFrame
    //     0x93ccfc: stp             fp, lr, [SP, #-0x10]!
    //     0x93cd00: mov             fp, SP
    // 0x93cd04: AllocStack(0x18)
    //     0x93cd04: sub             SP, SP, #0x18
    // 0x93cd08: SetupParameters(_WidgetsAppState this /* r1 => r0, fp-0x8 */)
    //     0x93cd08: mov             x0, x1
    //     0x93cd0c: stur            x1, [fp, #-8]
    // 0x93cd10: CheckStackOverflow
    //     0x93cd10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93cd14: cmp             SP, x16
    //     0x93cd18: b.ls            #0x93ceb0
    // 0x93cd1c: mov             x1, x0
    // 0x93cd20: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x93cd20: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x93cd24: r0 = _updateRouting()
    //     0x93cd24: bl              #0x93cec8  ; [package:flutter/src/widgets/app.dart] _WidgetsAppState::_updateRouting
    // 0x93cd28: r0 = LoadStaticField(0x7d4)
    //     0x93cd28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93cd2c: ldr             x0, [x0, #0xfa8]
    // 0x93cd30: cmp             w0, NULL
    // 0x93cd34: b.eq            #0x93ceb8
    // 0x93cd38: r0 = InitLateStaticField(0x5d8) // [dart:ui] PlatformDispatcher::_instance
    //     0x93cd38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93cd3c: ldr             x0, [x0, #0xbb0]
    //     0x93cd40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93cd44: cmp             w0, w16
    //     0x93cd48: b.ne            #0x93cd54
    //     0x93cd4c: ldr             x2, [PP, #0x428]  ; [pp+0x428] Field <PlatformDispatcher._instance@********>: static late final (offset: 0x5d8)
    //     0x93cd50: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x93cd54: LoadField: r1 = r0->field_7
    //     0x93cd54: ldur            w1, [x0, #7]
    // 0x93cd58: DecompressPointer r1
    //     0x93cd58: add             x1, x1, HEAP, lsl #32
    // 0x93cd5c: LoadField: r2 = r1->field_1f
    //     0x93cd5c: ldur            w2, [x1, #0x1f]
    // 0x93cd60: DecompressPointer r2
    //     0x93cd60: add             x2, x2, HEAP, lsl #32
    // 0x93cd64: ldur            x0, [fp, #-8]
    // 0x93cd68: LoadField: r1 = r0->field_b
    //     0x93cd68: ldur            w1, [x0, #0xb]
    // 0x93cd6c: DecompressPointer r1
    //     0x93cd6c: add             x1, x1, HEAP, lsl #32
    // 0x93cd70: cmp             w1, NULL
    // 0x93cd74: b.eq            #0x93cebc
    // 0x93cd78: mov             x1, x0
    // 0x93cd7c: r0 = _resolveLocales()
    //     0x93cd7c: bl              #0x674554  ; [package:flutter/src/widgets/app.dart] _WidgetsAppState::_resolveLocales
    // 0x93cd80: ldur            x3, [fp, #-8]
    // 0x93cd84: StoreField: r3->field_1f = r0
    //     0x93cd84: stur            w0, [x3, #0x1f]
    //     0x93cd88: ldurb           w16, [x3, #-1]
    //     0x93cd8c: ldurb           w17, [x0, #-1]
    //     0x93cd90: and             x16, x17, x16, lsr #2
    //     0x93cd94: tst             x16, HEAP, lsr #32
    //     0x93cd98: b.eq            #0x93cda0
    //     0x93cd9c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x93cda0: r0 = LoadStaticField(0x7d4)
    //     0x93cda0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93cda4: ldr             x0, [x0, #0xfa8]
    // 0x93cda8: cmp             w0, NULL
    // 0x93cdac: b.eq            #0x93cec0
    // 0x93cdb0: LoadField: r4 = r0->field_ef
    //     0x93cdb0: ldur            w4, [x0, #0xef]
    // 0x93cdb4: DecompressPointer r4
    //     0x93cdb4: add             x4, x4, HEAP, lsl #32
    // 0x93cdb8: stur            x4, [fp, #-0x10]
    // 0x93cdbc: LoadField: r2 = r4->field_7
    //     0x93cdbc: ldur            w2, [x4, #7]
    // 0x93cdc0: DecompressPointer r2
    //     0x93cdc0: add             x2, x2, HEAP, lsl #32
    // 0x93cdc4: mov             x0, x3
    // 0x93cdc8: r1 = Null
    //     0x93cdc8: mov             x1, NULL
    // 0x93cdcc: cmp             w2, NULL
    // 0x93cdd0: b.eq            #0x93cdf0
    // 0x93cdd4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93cdd4: ldur            w4, [x2, #0x17]
    // 0x93cdd8: DecompressPointer r4
    //     0x93cdd8: add             x4, x4, HEAP, lsl #32
    // 0x93cddc: r8 = X0
    //     0x93cddc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x93cde0: LoadField: r9 = r4->field_7
    //     0x93cde0: ldur            x9, [x4, #7]
    // 0x93cde4: r3 = Null
    //     0x93cde4: add             x3, PP, #0x57, lsl #12  ; [pp+0x57478] Null
    //     0x93cde8: ldr             x3, [x3, #0x478]
    // 0x93cdec: blr             x9
    // 0x93cdf0: ldur            x0, [fp, #-0x10]
    // 0x93cdf4: LoadField: r1 = r0->field_b
    //     0x93cdf4: ldur            w1, [x0, #0xb]
    // 0x93cdf8: LoadField: r2 = r0->field_f
    //     0x93cdf8: ldur            w2, [x0, #0xf]
    // 0x93cdfc: DecompressPointer r2
    //     0x93cdfc: add             x2, x2, HEAP, lsl #32
    // 0x93ce00: LoadField: r3 = r2->field_b
    //     0x93ce00: ldur            w3, [x2, #0xb]
    // 0x93ce04: r2 = LoadInt32Instr(r1)
    //     0x93ce04: sbfx            x2, x1, #1, #0x1f
    // 0x93ce08: stur            x2, [fp, #-0x18]
    // 0x93ce0c: r1 = LoadInt32Instr(r3)
    //     0x93ce0c: sbfx            x1, x3, #1, #0x1f
    // 0x93ce10: cmp             x2, x1
    // 0x93ce14: b.ne            #0x93ce20
    // 0x93ce18: mov             x1, x0
    // 0x93ce1c: r0 = _growToNextCapacity()
    //     0x93ce1c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93ce20: ldur            x4, [fp, #-8]
    // 0x93ce24: ldur            x2, [fp, #-0x10]
    // 0x93ce28: ldur            x3, [fp, #-0x18]
    // 0x93ce2c: add             x5, x3, #1
    // 0x93ce30: lsl             x6, x5, #1
    // 0x93ce34: StoreField: r2->field_b = r6
    //     0x93ce34: stur            w6, [x2, #0xb]
    // 0x93ce38: LoadField: r1 = r2->field_f
    //     0x93ce38: ldur            w1, [x2, #0xf]
    // 0x93ce3c: DecompressPointer r1
    //     0x93ce3c: add             x1, x1, HEAP, lsl #32
    // 0x93ce40: mov             x0, x4
    // 0x93ce44: ArrayStore: r1[r3] = r0  ; List_4
    //     0x93ce44: add             x25, x1, x3, lsl #2
    //     0x93ce48: add             x25, x25, #0xf
    //     0x93ce4c: str             w0, [x25]
    //     0x93ce50: tbz             w0, #0, #0x93ce6c
    //     0x93ce54: ldurb           w16, [x1, #-1]
    //     0x93ce58: ldurb           w17, [x0, #-1]
    //     0x93ce5c: and             x16, x17, x16, lsr #2
    //     0x93ce60: tst             x16, HEAP, lsr #32
    //     0x93ce64: b.eq            #0x93ce6c
    //     0x93ce68: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x93ce6c: r1 = LoadStaticField(0x7d4)
    //     0x93ce6c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x93ce70: ldr             x1, [x1, #0xfa8]
    // 0x93ce74: cmp             w1, NULL
    // 0x93ce78: b.eq            #0x93cec4
    // 0x93ce7c: LoadField: r0 = r1->field_2f
    //     0x93ce7c: ldur            w0, [x1, #0x2f]
    // 0x93ce80: DecompressPointer r0
    //     0x93ce80: add             x0, x0, HEAP, lsl #32
    // 0x93ce84: StoreField: r4->field_13 = r0
    //     0x93ce84: stur            w0, [x4, #0x13]
    //     0x93ce88: ldurb           w16, [x4, #-1]
    //     0x93ce8c: ldurb           w17, [x0, #-1]
    //     0x93ce90: and             x16, x17, x16, lsr #2
    //     0x93ce94: tst             x16, HEAP, lsr #32
    //     0x93ce98: b.eq            #0x93cea0
    //     0x93ce9c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x93cea0: r0 = Null
    //     0x93cea0: mov             x0, NULL
    // 0x93cea4: LeaveFrame
    //     0x93cea4: mov             SP, fp
    //     0x93cea8: ldp             fp, lr, [SP], #0x10
    // 0x93ceac: ret
    //     0x93ceac: ret             
    // 0x93ceb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93ceb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93ceb4: b               #0x93cd1c
    // 0x93ceb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ceb8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93cebc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93cebc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93cec0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93cec0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93cec4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93cec4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateRouting(/* No info */) {
    // ** addr: 0x93cec8, size: 0x11c
    // 0x93cec8: EnterFrame
    //     0x93cec8: stp             fp, lr, [SP, #-0x10]!
    //     0x93cecc: mov             fp, SP
    // 0x93ced0: AllocStack(0x10)
    //     0x93ced0: sub             SP, SP, #0x10
    // 0x93ced4: SetupParameters(_WidgetsAppState this /* r1 => r0, fp-0x10 */, {dynamic oldWidget = Null /* r2, fp-0x8 */})
    //     0x93ced4: mov             x0, x1
    //     0x93ced8: stur            x1, [fp, #-0x10]
    //     0x93cedc: ldur            w1, [x4, #0x13]
    //     0x93cee0: ldur            w2, [x4, #0x1f]
    //     0x93cee4: add             x2, x2, HEAP, lsl #32
    //     0x93cee8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57470] "oldWidget"
    //     0x93ceec: ldr             x16, [x16, #0x470]
    //     0x93cef0: cmp             w2, w16
    //     0x93cef4: b.ne            #0x93cf14
    //     0x93cef8: ldur            w2, [x4, #0x23]
    //     0x93cefc: add             x2, x2, HEAP, lsl #32
    //     0x93cf00: sub             w3, w1, w2
    //     0x93cf04: add             x1, fp, w3, sxtw #2
    //     0x93cf08: ldr             x1, [x1, #8]
    //     0x93cf0c: mov             x2, x1
    //     0x93cf10: b               #0x93cf18
    //     0x93cf14: mov             x2, NULL
    //     0x93cf18: stur            x2, [fp, #-8]
    // 0x93cf1c: CheckStackOverflow
    //     0x93cf1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93cf20: cmp             SP, x16
    //     0x93cf24: b.ls            #0x93cfcc
    // 0x93cf28: LoadField: r1 = r0->field_b
    //     0x93cf28: ldur            w1, [x0, #0xb]
    // 0x93cf2c: DecompressPointer r1
    //     0x93cf2c: add             x1, x1, HEAP, lsl #32
    // 0x93cf30: cmp             w1, NULL
    // 0x93cf34: b.eq            #0x93cfd4
    // 0x93cf38: mov             x1, x0
    // 0x93cf3c: r0 = detach()
    //     0x93cf3c: bl              #0x8083b4  ; [package:flutter/src/rendering/object.dart] RenderObject::detach
    // 0x93cf40: ldur            x1, [fp, #-0x10]
    // 0x93cf44: LoadField: r2 = r1->field_1b
    //     0x93cf44: ldur            w2, [x1, #0x1b]
    // 0x93cf48: DecompressPointer r2
    //     0x93cf48: add             x2, x2, HEAP, lsl #32
    // 0x93cf4c: cmp             w2, NULL
    // 0x93cf50: b.eq            #0x93cf88
    // 0x93cf54: ldur            x2, [fp, #-8]
    // 0x93cf58: LoadField: r3 = r1->field_b
    //     0x93cf58: ldur            w3, [x1, #0xb]
    // 0x93cf5c: DecompressPointer r3
    //     0x93cf5c: add             x3, x3, HEAP, lsl #32
    // 0x93cf60: cmp             w3, NULL
    // 0x93cf64: b.eq            #0x93cfd8
    // 0x93cf68: LoadField: r4 = r3->field_b
    //     0x93cf68: ldur            w4, [x3, #0xb]
    // 0x93cf6c: DecompressPointer r4
    //     0x93cf6c: add             x4, x4, HEAP, lsl #32
    // 0x93cf70: cmp             w2, NULL
    // 0x93cf74: b.eq            #0x93cfdc
    // 0x93cf78: LoadField: r3 = r2->field_b
    //     0x93cf78: ldur            w3, [x2, #0xb]
    // 0x93cf7c: DecompressPointer r3
    //     0x93cf7c: add             x3, x3, HEAP, lsl #32
    // 0x93cf80: cmp             w4, w3
    // 0x93cf84: b.eq            #0x93cfbc
    // 0x93cf88: LoadField: r2 = r1->field_b
    //     0x93cf88: ldur            w2, [x1, #0xb]
    // 0x93cf8c: DecompressPointer r2
    //     0x93cf8c: add             x2, x2, HEAP, lsl #32
    // 0x93cf90: cmp             w2, NULL
    // 0x93cf94: b.eq            #0x93cfe0
    // 0x93cf98: LoadField: r0 = r2->field_b
    //     0x93cf98: ldur            w0, [x2, #0xb]
    // 0x93cf9c: DecompressPointer r0
    //     0x93cf9c: add             x0, x0, HEAP, lsl #32
    // 0x93cfa0: StoreField: r1->field_1b = r0
    //     0x93cfa0: stur            w0, [x1, #0x1b]
    //     0x93cfa4: ldurb           w16, [x1, #-1]
    //     0x93cfa8: ldurb           w17, [x0, #-1]
    //     0x93cfac: and             x16, x17, x16, lsr #2
    //     0x93cfb0: tst             x16, HEAP, lsr #32
    //     0x93cfb4: b.eq            #0x93cfbc
    //     0x93cfb8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x93cfbc: r0 = Null
    //     0x93cfbc: mov             x0, NULL
    // 0x93cfc0: LeaveFrame
    //     0x93cfc0: mov             SP, fp
    //     0x93cfc4: ldp             fp, lr, [SP], #0x10
    // 0x93cfc8: ret
    //     0x93cfc8: ret             
    // 0x93cfcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93cfcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93cfd0: b               #0x93cf28
    // 0x93cfd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93cfd4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93cfd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93cfd8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93cfdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93cfdc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93cfe0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93cfe0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x98df60, size: 0xd0
    // 0x98df60: EnterFrame
    //     0x98df60: stp             fp, lr, [SP, #-0x10]!
    //     0x98df64: mov             fp, SP
    // 0x98df68: AllocStack(0x18)
    //     0x98df68: sub             SP, SP, #0x18
    // 0x98df6c: SetupParameters(_WidgetsAppState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x98df6c: mov             x4, x1
    //     0x98df70: mov             x3, x2
    //     0x98df74: stur            x1, [fp, #-8]
    //     0x98df78: stur            x2, [fp, #-0x10]
    // 0x98df7c: CheckStackOverflow
    //     0x98df7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98df80: cmp             SP, x16
    //     0x98df84: b.ls            #0x98e028
    // 0x98df88: mov             x0, x3
    // 0x98df8c: r2 = Null
    //     0x98df8c: mov             x2, NULL
    // 0x98df90: r1 = Null
    //     0x98df90: mov             x1, NULL
    // 0x98df94: r4 = 60
    //     0x98df94: movz            x4, #0x3c
    // 0x98df98: branchIfSmi(r0, 0x98dfa4)
    //     0x98df98: tbz             w0, #0, #0x98dfa4
    // 0x98df9c: r4 = LoadClassIdInstr(r0)
    //     0x98df9c: ldur            x4, [x0, #-1]
    //     0x98dfa0: ubfx            x4, x4, #0xc, #0x14
    // 0x98dfa4: r17 = 4799
    //     0x98dfa4: movz            x17, #0x12bf
    // 0x98dfa8: cmp             x4, x17
    // 0x98dfac: b.eq            #0x98dfc4
    // 0x98dfb0: r8 = WidgetsApp
    //     0x98dfb0: add             x8, PP, #0x57, lsl #12  ; [pp+0x57440] Type: WidgetsApp
    //     0x98dfb4: ldr             x8, [x8, #0x440]
    // 0x98dfb8: r3 = Null
    //     0x98dfb8: add             x3, PP, #0x57, lsl #12  ; [pp+0x57448] Null
    //     0x98dfbc: ldr             x3, [x3, #0x448]
    // 0x98dfc0: r0 = WidgetsApp()
    //     0x98dfc0: bl              #0x659d18  ; IsType_WidgetsApp_Stub
    // 0x98dfc4: ldur            x3, [fp, #-8]
    // 0x98dfc8: LoadField: r2 = r3->field_7
    //     0x98dfc8: ldur            w2, [x3, #7]
    // 0x98dfcc: DecompressPointer r2
    //     0x98dfcc: add             x2, x2, HEAP, lsl #32
    // 0x98dfd0: ldur            x0, [fp, #-0x10]
    // 0x98dfd4: r1 = Null
    //     0x98dfd4: mov             x1, NULL
    // 0x98dfd8: cmp             w2, NULL
    // 0x98dfdc: b.eq            #0x98e000
    // 0x98dfe0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x98dfe0: ldur            w4, [x2, #0x17]
    // 0x98dfe4: DecompressPointer r4
    //     0x98dfe4: add             x4, x4, HEAP, lsl #32
    // 0x98dfe8: r8 = X0 bound StatefulWidget
    //     0x98dfe8: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x98dfec: ldr             x8, [x8, #0x7f8]
    // 0x98dff0: LoadField: r9 = r4->field_7
    //     0x98dff0: ldur            x9, [x4, #7]
    // 0x98dff4: r3 = Null
    //     0x98dff4: add             x3, PP, #0x57, lsl #12  ; [pp+0x57458] Null
    //     0x98dff8: ldr             x3, [x3, #0x458]
    // 0x98dffc: blr             x9
    // 0x98e000: ldur            x16, [fp, #-0x10]
    // 0x98e004: str             x16, [SP]
    // 0x98e008: ldur            x1, [fp, #-8]
    // 0x98e00c: r4 = const [0, 0x2, 0x1, 0x1, oldWidget, 0x1, null]
    //     0x98e00c: add             x4, PP, #0x57, lsl #12  ; [pp+0x57468] List(7) [0, 0x2, 0x1, 0x1, "oldWidget", 0x1, Null]
    //     0x98e010: ldr             x4, [x4, #0x468]
    // 0x98e014: r0 = _updateRouting()
    //     0x98e014: bl              #0x93cec8  ; [package:flutter/src/widgets/app.dart] _WidgetsAppState::_updateRouting
    // 0x98e018: r0 = Null
    //     0x98e018: mov             x0, NULL
    // 0x98e01c: LeaveFrame
    //     0x98e01c: mov             SP, fp
    //     0x98e020: ldp             fp, lr, [SP], #0x10
    // 0x98e024: ret
    //     0x98e024: ret             
    // 0x98e028: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98e028: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98e02c: b               #0x98df88
  }
  _ build(/* No info */) {
    // ** addr: 0xa103a0, size: 0x4dc
    // 0xa103a0: EnterFrame
    //     0xa103a0: stp             fp, lr, [SP, #-0x10]!
    //     0xa103a4: mov             fp, SP
    // 0xa103a8: AllocStack(0x50)
    //     0xa103a8: sub             SP, SP, #0x50
    // 0xa103ac: SetupParameters(_WidgetsAppState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa103ac: stur            x1, [fp, #-8]
    //     0xa103b0: stur            x2, [fp, #-0x10]
    // 0xa103b4: CheckStackOverflow
    //     0xa103b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa103b8: cmp             SP, x16
    //     0xa103bc: b.ls            #0xa10860
    // 0xa103c0: r1 = 2
    //     0xa103c0: movz            x1, #0x2
    // 0xa103c4: r0 = AllocateContext()
    //     0xa103c4: bl              #0xec126c  ; AllocateContextStub
    // 0xa103c8: mov             x2, x0
    // 0xa103cc: ldur            x0, [fp, #-8]
    // 0xa103d0: stur            x2, [fp, #-0x18]
    // 0xa103d4: StoreField: r2->field_f = r0
    //     0xa103d4: stur            w0, [x2, #0xf]
    // 0xa103d8: LoadField: r1 = r0->field_b
    //     0xa103d8: ldur            w1, [x0, #0xb]
    // 0xa103dc: DecompressPointer r1
    //     0xa103dc: add             x1, x1, HEAP, lsl #32
    // 0xa103e0: cmp             w1, NULL
    // 0xa103e4: b.eq            #0xa10868
    // 0xa103e8: mov             x1, x0
    // 0xa103ec: r0 = _usesNavigator()
    //     0xa103ec: bl              #0xa10d90  ; [package:flutter/src/widgets/app.dart] _WidgetsAppState::_usesNavigator
    // 0xa103f0: tbnz            w0, #4, #0xa10534
    // 0xa103f4: ldur            x0, [fp, #-8]
    // 0xa103f8: ldur            x2, [fp, #-0x18]
    // 0xa103fc: LoadField: r3 = r0->field_1b
    //     0xa103fc: ldur            w3, [x0, #0x1b]
    // 0xa10400: DecompressPointer r3
    //     0xa10400: add             x3, x3, HEAP, lsl #32
    // 0xa10404: mov             x1, x0
    // 0xa10408: stur            x3, [fp, #-0x20]
    // 0xa1040c: r0 = _initialRouteName()
    //     0xa1040c: bl              #0xa10b0c  ; [package:flutter/src/widgets/app.dart] _WidgetsAppState::_initialRouteName
    // 0xa10410: ldur            x2, [fp, #-8]
    // 0xa10414: stur            x0, [fp, #-0x38]
    // 0xa10418: LoadField: r1 = r2->field_b
    //     0xa10418: ldur            w1, [x2, #0xb]
    // 0xa1041c: DecompressPointer r1
    //     0xa1041c: add             x1, x1, HEAP, lsl #32
    // 0xa10420: stur            x1, [fp, #-0x30]
    // 0xa10424: cmp             w1, NULL
    // 0xa10428: b.eq            #0xa1086c
    // 0xa1042c: LoadField: r3 = r1->field_37
    //     0xa1042c: ldur            w3, [x1, #0x37]
    // 0xa10430: DecompressPointer r3
    //     0xa10430: add             x3, x3, HEAP, lsl #32
    // 0xa10434: stur            x3, [fp, #-0x28]
    // 0xa10438: r0 = Navigator()
    //     0xa10438: bl              #0xa10b00  ; AllocateNavigatorStub -> Navigator (size=0x40)
    // 0xa1043c: mov             x3, x0
    // 0xa10440: r0 = const []
    //     0xa10440: add             x0, PP, #0x57, lsl #12  ; [pp+0x57350] List<Page>(0)
    //     0xa10444: ldr             x0, [x0, #0x350]
    // 0xa10448: stur            x3, [fp, #-0x40]
    // 0xa1044c: StoreField: r3->field_b = r0
    //     0xa1044c: stur            w0, [x3, #0xb]
    // 0xa10450: ldur            x0, [fp, #-0x38]
    // 0xa10454: StoreField: r3->field_1b = r0
    //     0xa10454: stur            w0, [x3, #0x1b]
    // 0xa10458: ldur            x2, [fp, #-0x18]
    // 0xa1045c: r1 = Function '<anonymous closure>':.
    //     0xa1045c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57358] AnonymousClosure: (0xa113bc), in [package:flutter/src/widgets/app.dart] _WidgetsAppState::build (0xa103a0)
    //     0xa10460: ldr             x1, [x1, #0x358]
    // 0xa10464: r0 = AllocateClosure()
    //     0xa10464: bl              #0xec1630  ; AllocateClosureStub
    // 0xa10468: mov             x1, x0
    // 0xa1046c: ldur            x0, [fp, #-0x40]
    // 0xa10470: StoreField: r0->field_2f = r1
    //     0xa10470: stur            w1, [x0, #0x2f]
    // 0xa10474: ldur            x2, [fp, #-8]
    // 0xa10478: r1 = Function '_onGenerateRoute@258236006':.
    //     0xa10478: add             x1, PP, #0x57, lsl #12  ; [pp+0x57360] AnonymousClosure: (0x659cdc), in [package:flutter/src/widgets/app.dart] _WidgetsAppState::_onGenerateRoute (0x659d3c)
    //     0xa1047c: ldr             x1, [x1, #0x360]
    // 0xa10480: r0 = AllocateClosure()
    //     0xa10480: bl              #0xec1630  ; AllocateClosureStub
    // 0xa10484: mov             x1, x0
    // 0xa10488: ldur            x0, [fp, #-0x40]
    // 0xa1048c: StoreField: r0->field_1f = r1
    //     0xa1048c: stur            w1, [x0, #0x1f]
    // 0xa10490: r1 = Instance_DefaultTransitionDelegate
    //     0xa10490: add             x1, PP, #0x57, lsl #12  ; [pp+0x57368] Obj!DefaultTransitionDelegate@e0fda1
    //     0xa10494: ldr             x1, [x1, #0x368]
    // 0xa10498: ArrayStore: r0[0] = r1  ; List_4
    //     0xa10498: stur            w1, [x0, #0x17]
    // 0xa1049c: r1 = true
    //     0xa1049c: add             x1, NULL, #0x20  ; true
    // 0xa104a0: StoreField: r0->field_33 = r1
    //     0xa104a0: stur            w1, [x0, #0x33]
    // 0xa104a4: r2 = Instance_Clip
    //     0xa104a4: add             x2, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa104a8: ldr             x2, [x2, #0x750]
    // 0xa104ac: StoreField: r0->field_37 = r2
    //     0xa104ac: stur            w2, [x0, #0x37]
    // 0xa104b0: ldur            x2, [fp, #-0x28]
    // 0xa104b4: StoreField: r0->field_23 = r2
    //     0xa104b4: stur            w2, [x0, #0x23]
    // 0xa104b8: StoreField: r0->field_3b = r1
    //     0xa104b8: stur            w1, [x0, #0x3b]
    // 0xa104bc: r2 = "nav"
    //     0xa104bc: add             x2, PP, #0x4c, lsl #12  ; [pp+0x4cbb8] "nav"
    //     0xa104c0: ldr             x2, [x2, #0xbb8]
    // 0xa104c4: StoreField: r0->field_27 = r2
    //     0xa104c4: stur            w2, [x0, #0x27]
    // 0xa104c8: r2 = Instance_TraversalEdgeBehavior
    //     0xa104c8: add             x2, PP, #0x25, lsl #12  ; [pp+0x25498] Obj!TraversalEdgeBehavior@e34481
    //     0xa104cc: ldr             x2, [x2, #0x498]
    // 0xa104d0: StoreField: r0->field_2b = r2
    //     0xa104d0: stur            w2, [x0, #0x2b]
    // 0xa104d4: ldur            x2, [fp, #-0x20]
    // 0xa104d8: StoreField: r0->field_7 = r2
    //     0xa104d8: stur            w2, [x0, #7]
    // 0xa104dc: r0 = FocusScope()
    //     0xa104dc: bl              #0xa10af4  ; AllocateFocusScopeStub -> FocusScope (size=0x40)
    // 0xa104e0: mov             x1, x0
    // 0xa104e4: ldur            x0, [fp, #-0x40]
    // 0xa104e8: StoreField: r1->field_f = r0
    //     0xa104e8: stur            w0, [x1, #0xf]
    // 0xa104ec: r3 = true
    //     0xa104ec: add             x3, NULL, #0x20  ; true
    // 0xa104f0: ArrayStore: r1[0] = r3  ; List_4
    //     0xa104f0: stur            w3, [x1, #0x17]
    // 0xa104f4: StoreField: r1->field_37 = r3
    //     0xa104f4: stur            w3, [x1, #0x37]
    // 0xa104f8: r0 = "Navigator Scope"
    //     0xa104f8: add             x0, PP, #0x57, lsl #12  ; [pp+0x57370] "Navigator Scope"
    //     0xa104fc: ldr             x0, [x0, #0x370]
    // 0xa10500: StoreField: r1->field_3b = r0
    //     0xa10500: stur            w0, [x1, #0x3b]
    // 0xa10504: mov             x0, x1
    // 0xa10508: ldur            x2, [fp, #-0x18]
    // 0xa1050c: StoreField: r2->field_13 = r0
    //     0xa1050c: stur            w0, [x2, #0x13]
    //     0xa10510: ldurb           w16, [x2, #-1]
    //     0xa10514: ldurb           w17, [x0, #-1]
    //     0xa10518: and             x16, x17, x16, lsr #2
    //     0xa1051c: tst             x16, HEAP, lsr #32
    //     0xa10520: b.eq            #0xa10528
    //     0xa10524: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa10528: ldur            x4, [fp, #-0x30]
    // 0xa1052c: ldur            x0, [fp, #-8]
    // 0xa10530: b               #0xa10554
    // 0xa10534: ldur            x0, [fp, #-8]
    // 0xa10538: ldur            x2, [fp, #-0x18]
    // 0xa1053c: r3 = true
    //     0xa1053c: add             x3, NULL, #0x20  ; true
    // 0xa10540: LoadField: r1 = r0->field_b
    //     0xa10540: ldur            w1, [x0, #0xb]
    // 0xa10544: DecompressPointer r1
    //     0xa10544: add             x1, x1, HEAP, lsl #32
    // 0xa10548: cmp             w1, NULL
    // 0xa1054c: b.eq            #0xa10870
    // 0xa10550: mov             x4, x1
    // 0xa10554: stur            x4, [fp, #-0x20]
    // 0xa10558: r1 = Function '<anonymous closure>':.
    //     0xa10558: add             x1, PP, #0x57, lsl #12  ; [pp+0x57378] AnonymousClosure: (0xa11340), in [package:flutter/src/widgets/app.dart] _WidgetsAppState::build (0xa103a0)
    //     0xa1055c: ldr             x1, [x1, #0x378]
    // 0xa10560: r0 = AllocateClosure()
    //     0xa10560: bl              #0xec1630  ; AllocateClosureStub
    // 0xa10564: stur            x0, [fp, #-0x18]
    // 0xa10568: r0 = Builder()
    //     0xa10568: bl              #0x6a5c84  ; AllocateBuilderStub -> Builder (size=0x10)
    // 0xa1056c: mov             x1, x0
    // 0xa10570: ldur            x0, [fp, #-0x18]
    // 0xa10574: stur            x1, [fp, #-0x28]
    // 0xa10578: StoreField: r1->field_b = r0
    //     0xa10578: stur            w0, [x1, #0xb]
    // 0xa1057c: r0 = DefaultTextStyle()
    //     0xa1057c: bl              #0x9d5004  ; AllocateDefaultTextStyleStub -> DefaultTextStyle (size=0x2c)
    // 0xa10580: mov             x2, x0
    // 0xa10584: r0 = Instance_TextStyle
    //     0xa10584: add             x0, PP, #0x44, lsl #12  ; [pp+0x448c0] Obj!TextStyle@e1b0a1
    //     0xa10588: ldr             x0, [x0, #0x8c0]
    // 0xa1058c: stur            x2, [fp, #-0x18]
    // 0xa10590: StoreField: r2->field_f = r0
    //     0xa10590: stur            w0, [x2, #0xf]
    // 0xa10594: r3 = true
    //     0xa10594: add             x3, NULL, #0x20  ; true
    // 0xa10598: ArrayStore: r2[0] = r3  ; List_4
    //     0xa10598: stur            w3, [x2, #0x17]
    // 0xa1059c: r0 = Instance_TextOverflow
    //     0xa1059c: add             x0, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xa105a0: ldr             x0, [x0, #0xc60]
    // 0xa105a4: StoreField: r2->field_1b = r0
    //     0xa105a4: stur            w0, [x2, #0x1b]
    // 0xa105a8: r0 = Instance_TextWidthBasis
    //     0xa105a8: add             x0, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0xa105ac: ldr             x0, [x0, #0x1d8]
    // 0xa105b0: StoreField: r2->field_23 = r0
    //     0xa105b0: stur            w0, [x2, #0x23]
    // 0xa105b4: ldur            x0, [fp, #-0x28]
    // 0xa105b8: StoreField: r2->field_b = r0
    //     0xa105b8: stur            w0, [x2, #0xb]
    // 0xa105bc: ldur            x0, [fp, #-0x20]
    // 0xa105c0: LoadField: r1 = r0->field_4b
    //     0xa105c0: ldur            w1, [x0, #0x4b]
    // 0xa105c4: DecompressPointer r1
    //     0xa105c4: add             x1, x1, HEAP, lsl #32
    // 0xa105c8: r0 = LoadClassIdInstr(r1)
    //     0xa105c8: ldur            x0, [x1, #-1]
    //     0xa105cc: ubfx            x0, x0, #0xc, #0x14
    // 0xa105d0: d0 = 1.000000
    //     0xa105d0: fmov            d0, #1.00000000
    // 0xa105d4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xa105d4: sub             lr, x0, #1, lsl #12
    //     0xa105d8: ldr             lr, [x21, lr, lsl #3]
    //     0xa105dc: blr             lr
    // 0xa105e0: stur            x0, [fp, #-0x20]
    // 0xa105e4: r0 = Title()
    //     0xa105e4: bl              #0xa10ae8  ; AllocateTitleStub -> Title (size=0x18)
    // 0xa105e8: mov             x3, x0
    // 0xa105ec: r0 = "NU Online"
    //     0xa105ec: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ba0] "NU Online"
    //     0xa105f0: ldr             x0, [x0, #0xba0]
    // 0xa105f4: stur            x3, [fp, #-0x28]
    // 0xa105f8: StoreField: r3->field_b = r0
    //     0xa105f8: stur            w0, [x3, #0xb]
    // 0xa105fc: ldur            x0, [fp, #-0x20]
    // 0xa10600: StoreField: r3->field_f = r0
    //     0xa10600: stur            w0, [x3, #0xf]
    // 0xa10604: ldur            x0, [fp, #-0x18]
    // 0xa10608: StoreField: r3->field_13 = r0
    //     0xa10608: stur            w0, [x3, #0x13]
    // 0xa1060c: ldur            x0, [fp, #-8]
    // 0xa10610: LoadField: r1 = r0->field_b
    //     0xa10610: ldur            w1, [x0, #0xb]
    // 0xa10614: DecompressPointer r1
    //     0xa10614: add             x1, x1, HEAP, lsl #32
    // 0xa10618: cmp             w1, NULL
    // 0xa1061c: b.eq            #0xa10874
    // 0xa10620: LoadField: r4 = r1->field_4f
    //     0xa10620: ldur            w4, [x1, #0x4f]
    // 0xa10624: DecompressPointer r4
    //     0xa10624: add             x4, x4, HEAP, lsl #32
    // 0xa10628: stur            x4, [fp, #-0x18]
    // 0xa1062c: r1 = Null
    //     0xa1062c: mov             x1, NULL
    // 0xa10630: r2 = 2
    //     0xa10630: movz            x2, #0x2
    // 0xa10634: r0 = AllocateArray()
    //     0xa10634: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa10638: mov             x2, x0
    // 0xa1063c: ldur            x0, [fp, #-0x18]
    // 0xa10640: stur            x2, [fp, #-0x20]
    // 0xa10644: StoreField: r2->field_f = r0
    //     0xa10644: stur            w0, [x2, #0xf]
    // 0xa10648: r1 = <Locale>
    //     0xa10648: add             x1, PP, #0xc, lsl #12  ; [pp+0xcb38] TypeArguments: <Locale>
    //     0xa1064c: ldr             x1, [x1, #0xb38]
    // 0xa10650: r0 = AllocateGrowableArray()
    //     0xa10650: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa10654: mov             x1, x0
    // 0xa10658: ldur            x0, [fp, #-0x20]
    // 0xa1065c: StoreField: r1->field_f = r0
    //     0xa1065c: stur            w0, [x1, #0xf]
    // 0xa10660: r0 = 2
    //     0xa10660: movz            x0, #0x2
    // 0xa10664: StoreField: r1->field_b = r0
    //     0xa10664: stur            w0, [x1, #0xb]
    // 0xa10668: mov             x2, x1
    // 0xa1066c: ldur            x1, [fp, #-8]
    // 0xa10670: r0 = _resolveLocales()
    //     0xa10670: bl              #0x674554  ; [package:flutter/src/widgets/app.dart] _WidgetsAppState::_resolveLocales
    // 0xa10674: ldur            x1, [fp, #-8]
    // 0xa10678: stur            x0, [fp, #-0x18]
    // 0xa1067c: LoadField: r2 = r1->field_b
    //     0xa1067c: ldur            w2, [x1, #0xb]
    // 0xa10680: DecompressPointer r2
    //     0xa10680: add             x2, x2, HEAP, lsl #32
    // 0xa10684: cmp             w2, NULL
    // 0xa10688: b.eq            #0xa10878
    // 0xa1068c: r0 = InitLateStaticField(0x7cc) // [package:flutter/src/widgets/app.dart] WidgetsApp::defaultActions
    //     0xa1068c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa10690: ldr             x0, [x0, #0xf98]
    //     0xa10694: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa10698: cmp             w0, w16
    //     0xa1069c: b.ne            #0xa106ac
    //     0xa106a0: add             x2, PP, #0x57, lsl #12  ; [pp+0x57380] Field <WidgetsApp.defaultActions>: static late (offset: 0x7cc)
    //     0xa106a4: ldr             x2, [x2, #0x380]
    //     0xa106a8: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xa106ac: mov             x2, x0
    // 0xa106b0: r1 = <Type, Action<Intent>>
    //     0xa106b0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25448] TypeArguments: <Type, Action<Intent>>
    //     0xa106b4: ldr             x1, [x1, #0x448]
    // 0xa106b8: r0 = LinkedHashMap.of()
    //     0xa106b8: bl              #0x645b10  ; [dart:collection] LinkedHashMap::LinkedHashMap.of
    // 0xa106bc: r1 = <ScrollIntent>
    //     0xa106bc: add             x1, PP, #0x57, lsl #12  ; [pp+0x57108] TypeArguments: <ScrollIntent>
    //     0xa106c0: ldr             x1, [x1, #0x108]
    // 0xa106c4: stur            x0, [fp, #-0x20]
    // 0xa106c8: r0 = ScrollAction()
    //     0xa106c8: bl              #0xa10adc  ; AllocateScrollActionStub -> ScrollAction (size=0x14)
    // 0xa106cc: mov             x1, x0
    // 0xa106d0: stur            x0, [fp, #-0x30]
    // 0xa106d4: r0 = Action()
    //     0xa106d4: bl              #0x940bc8  ; [package:flutter/src/widgets/actions.dart] Action::Action
    // 0xa106d8: ldur            x1, [fp, #-0x30]
    // 0xa106dc: ldur            x2, [fp, #-0x10]
    // 0xa106e0: r0 = _makeOverridableAction()
    //     0xa106e0: bl              #0xbf4f24  ; [package:flutter/src/widgets/actions.dart] ContextAction::_makeOverridableAction
    // 0xa106e4: ldur            x1, [fp, #-0x20]
    // 0xa106e8: mov             x3, x0
    // 0xa106ec: r2 = ScrollIntent
    //     0xa106ec: add             x2, PP, #0x57, lsl #12  ; [pp+0x57100] Type: ScrollIntent
    //     0xa106f0: ldr             x2, [x2, #0x100]
    // 0xa106f4: r0 = []=()
    //     0xa106f4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa106f8: r16 = <FocusScopeNode, _DirectionalPolicyData>
    //     0xa106f8: ldr             x16, [PP, #0x7498]  ; [pp+0x7498] TypeArguments: <FocusScopeNode, _DirectionalPolicyData>
    // 0xa106fc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa10700: stp             lr, x16, [SP]
    // 0xa10704: r0 = Map._fromLiteral()
    //     0xa10704: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa10708: stur            x0, [fp, #-0x10]
    // 0xa1070c: r0 = ReadingOrderTraversalPolicy()
    //     0xa1070c: bl              #0x6b7d7c  ; AllocateReadingOrderTraversalPolicyStub -> ReadingOrderTraversalPolicy (size=0x10)
    // 0xa10710: mov             x2, x0
    // 0xa10714: ldur            x0, [fp, #-0x10]
    // 0xa10718: stur            x2, [fp, #-0x30]
    // 0xa1071c: StoreField: r2->field_b = r0
    //     0xa1071c: stur            w0, [x2, #0xb]
    // 0xa10720: r0 = Closure: (FocusNode, {ScrollPositionAlignmentPolicy? alignmentPolicy, double? alignment, Duration? duration, Curve? curve}) => void from Function 'defaultTraversalRequestFocusCallback': static.
    //     0xa10720: ldr             x0, [PP, #0x74a0]  ; [pp+0x74a0] Closure: (FocusNode, {ScrollPositionAlignmentPolicy? alignmentPolicy, double? alignment, Duration? duration, Curve? curve}) => void from Function 'defaultTraversalRequestFocusCallback': static. (0x7e54fb0b7d88)
    // 0xa10724: StoreField: r2->field_7 = r0
    //     0xa10724: stur            w0, [x2, #7]
    // 0xa10728: ldur            x1, [fp, #-8]
    // 0xa1072c: r0 = _localizationsDelegates()
    //     0xa1072c: bl              #0xa10a0c  ; [package:flutter/src/widgets/app.dart] _WidgetsAppState::_localizationsDelegates
    // 0xa10730: mov             x1, x0
    // 0xa10734: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa10734: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa10738: r0 = toList()
    //     0xa10738: bl              #0xa52dc8  ; [dart:core] _GrowableList::toList
    // 0xa1073c: stur            x0, [fp, #-0x10]
    // 0xa10740: r0 = Localizations()
    //     0xa10740: bl              #0xa10a00  ; AllocateLocalizationsStub -> Localizations (size=0x18)
    // 0xa10744: mov             x1, x0
    // 0xa10748: ldur            x0, [fp, #-0x18]
    // 0xa1074c: stur            x1, [fp, #-0x38]
    // 0xa10750: StoreField: r1->field_b = r0
    //     0xa10750: stur            w0, [x1, #0xb]
    // 0xa10754: ldur            x0, [fp, #-0x10]
    // 0xa10758: StoreField: r1->field_f = r0
    //     0xa10758: stur            w0, [x1, #0xf]
    // 0xa1075c: ldur            x0, [fp, #-0x28]
    // 0xa10760: StoreField: r1->field_13 = r0
    //     0xa10760: stur            w0, [x1, #0x13]
    // 0xa10764: r0 = ShortcutRegistrar()
    //     0xa10764: bl              #0xa109f4  ; AllocateShortcutRegistrarStub -> ShortcutRegistrar (size=0x10)
    // 0xa10768: mov             x1, x0
    // 0xa1076c: ldur            x0, [fp, #-0x38]
    // 0xa10770: stur            x1, [fp, #-0x10]
    // 0xa10774: StoreField: r1->field_b = r0
    //     0xa10774: stur            w0, [x1, #0xb]
    // 0xa10778: r0 = TapRegionSurface()
    //     0xa10778: bl              #0xa109e8  ; AllocateTapRegionSurfaceStub -> TapRegionSurface (size=0x10)
    // 0xa1077c: mov             x1, x0
    // 0xa10780: ldur            x0, [fp, #-0x10]
    // 0xa10784: stur            x1, [fp, #-0x18]
    // 0xa10788: StoreField: r1->field_b = r0
    //     0xa10788: stur            w0, [x1, #0xb]
    // 0xa1078c: r0 = FocusTraversalGroup()
    //     0xa1078c: bl              #0xa109dc  ; AllocateFocusTraversalGroupStub -> FocusTraversalGroup (size=0x1c)
    // 0xa10790: mov             x1, x0
    // 0xa10794: ldur            x2, [fp, #-0x18]
    // 0xa10798: ldur            x3, [fp, #-0x30]
    // 0xa1079c: stur            x0, [fp, #-0x10]
    // 0xa107a0: r0 = FocusTraversalGroup()
    //     0xa107a0: bl              #0xa10918  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalGroup::FocusTraversalGroup
    // 0xa107a4: r0 = Actions()
    //     0xa107a4: bl              #0x9f2aec  ; AllocateActionsStub -> Actions (size=0x18)
    // 0xa107a8: mov             x1, x0
    // 0xa107ac: ldur            x0, [fp, #-0x20]
    // 0xa107b0: stur            x1, [fp, #-0x18]
    // 0xa107b4: StoreField: r1->field_f = r0
    //     0xa107b4: stur            w0, [x1, #0xf]
    // 0xa107b8: ldur            x0, [fp, #-0x10]
    // 0xa107bc: StoreField: r1->field_13 = r0
    //     0xa107bc: stur            w0, [x1, #0x13]
    // 0xa107c0: r0 = DefaultTextEditingShortcuts()
    //     0xa107c0: bl              #0xa1090c  ; AllocateDefaultTextEditingShortcutsStub -> DefaultTextEditingShortcuts (size=0x10)
    // 0xa107c4: mov             x1, x0
    // 0xa107c8: ldur            x0, [fp, #-0x18]
    // 0xa107cc: stur            x1, [fp, #-0x10]
    // 0xa107d0: StoreField: r1->field_b = r0
    //     0xa107d0: stur            w0, [x1, #0xb]
    // 0xa107d4: r0 = Shortcuts()
    //     0xa107d4: bl              #0xa10900  ; AllocateShortcutsStub -> Shortcuts (size=0x1c)
    // 0xa107d8: mov             x3, x0
    // 0xa107dc: ldur            x0, [fp, #-0x10]
    // 0xa107e0: stur            x3, [fp, #-0x18]
    // 0xa107e4: StoreField: r3->field_13 = r0
    //     0xa107e4: stur            w0, [x3, #0x13]
    // 0xa107e8: r0 = true
    //     0xa107e8: add             x0, NULL, #0x20  ; true
    // 0xa107ec: ArrayStore: r3[0] = r0  ; List_4
    //     0xa107ec: stur            w0, [x3, #0x17]
    // 0xa107f0: r0 = _ConstMap len:18
    //     0xa107f0: add             x0, PP, #0x57, lsl #12  ; [pp+0x57388] Map<ShortcutActivator, Intent>(18)
    //     0xa107f4: ldr             x0, [x0, #0x388]
    // 0xa107f8: StoreField: r3->field_f = r0
    //     0xa107f8: stur            w0, [x3, #0xf]
    // 0xa107fc: ldur            x2, [fp, #-8]
    // 0xa10800: r1 = Function '_defaultOnNavigationNotification@258236006':.
    //     0xa10800: add             x1, PP, #0x57, lsl #12  ; [pp+0x57390] AnonymousClosure: (0xa10db4), in [package:flutter/src/widgets/app.dart] _WidgetsAppState::_defaultOnNavigationNotification (0xa10df0)
    //     0xa10804: ldr             x1, [x1, #0x390]
    // 0xa10808: r0 = AllocateClosure()
    //     0xa10808: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1080c: r1 = <NavigationNotification>
    //     0xa1080c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57398] TypeArguments: <NavigationNotification>
    //     0xa10810: ldr             x1, [x1, #0x398]
    // 0xa10814: stur            x0, [fp, #-8]
    // 0xa10818: r0 = NotificationListener()
    //     0xa10818: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xa1081c: mov             x1, x0
    // 0xa10820: ldur            x0, [fp, #-8]
    // 0xa10824: stur            x1, [fp, #-0x10]
    // 0xa10828: StoreField: r1->field_13 = r0
    //     0xa10828: stur            w0, [x1, #0x13]
    // 0xa1082c: ldur            x0, [fp, #-0x18]
    // 0xa10830: StoreField: r1->field_b = r0
    //     0xa10830: stur            w0, [x1, #0xb]
    // 0xa10834: r0 = SharedAppData()
    //     0xa10834: bl              #0xa108d0  ; AllocateSharedAppDataStub -> SharedAppData (size=0x10)
    // 0xa10838: mov             x1, x0
    // 0xa1083c: ldur            x0, [fp, #-0x10]
    // 0xa10840: stur            x1, [fp, #-8]
    // 0xa10844: StoreField: r1->field_b = r0
    //     0xa10844: stur            w0, [x1, #0xb]
    // 0xa10848: r0 = RootRestorationScope()
    //     0xa10848: bl              #0xa108c4  ; AllocateRootRestorationScopeStub -> RootRestorationScope (size=0x14)
    // 0xa1084c: ldur            x1, [fp, #-8]
    // 0xa10850: StoreField: r0->field_b = r1
    //     0xa10850: stur            w1, [x0, #0xb]
    // 0xa10854: LeaveFrame
    //     0xa10854: mov             SP, fp
    //     0xa10858: ldp             fp, lr, [SP], #0x10
    // 0xa1085c: ret
    //     0xa1085c: ret             
    // 0xa10860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa10860: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa10864: b               #0xa103c0
    // 0xa10868: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa10868: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1086c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1086c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa10870: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa10870: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa10874: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa10874: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa10878: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa10878: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _localizationsDelegates(/* No info */) {
    // ** addr: 0xa10a0c, size: 0xd0
    // 0xa10a0c: EnterFrame
    //     0xa10a0c: stp             fp, lr, [SP, #-0x10]!
    //     0xa10a10: mov             fp, SP
    // 0xa10a14: AllocStack(0x18)
    //     0xa10a14: sub             SP, SP, #0x18
    // 0xa10a18: SetupParameters(_WidgetsAppState this /* r1 => r0, fp-0x8 */)
    //     0xa10a18: mov             x0, x1
    //     0xa10a1c: stur            x1, [fp, #-8]
    // 0xa10a20: CheckStackOverflow
    //     0xa10a20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa10a24: cmp             SP, x16
    //     0xa10a28: b.ls            #0xa10ad0
    // 0xa10a2c: r1 = <LocalizationsDelegate>
    //     0xa10a2c: add             x1, PP, #0x44, lsl #12  ; [pp+0x448c8] TypeArguments: <LocalizationsDelegate>
    //     0xa10a30: ldr             x1, [x1, #0x8c8]
    // 0xa10a34: r2 = 0
    //     0xa10a34: movz            x2, #0
    // 0xa10a38: r0 = _GrowableList()
    //     0xa10a38: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa10a3c: mov             x3, x0
    // 0xa10a40: ldur            x0, [fp, #-8]
    // 0xa10a44: stur            x3, [fp, #-0x10]
    // 0xa10a48: LoadField: r1 = r0->field_b
    //     0xa10a48: ldur            w1, [x0, #0xb]
    // 0xa10a4c: DecompressPointer r1
    //     0xa10a4c: add             x1, x1, HEAP, lsl #32
    // 0xa10a50: cmp             w1, NULL
    // 0xa10a54: b.eq            #0xa10ad8
    // 0xa10a58: LoadField: r2 = r1->field_53
    //     0xa10a58: ldur            w2, [x1, #0x53]
    // 0xa10a5c: DecompressPointer r2
    //     0xa10a5c: add             x2, x2, HEAP, lsl #32
    // 0xa10a60: mov             x1, x3
    // 0xa10a64: r0 = addAll()
    //     0xa10a64: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xa10a68: ldur            x0, [fp, #-0x10]
    // 0xa10a6c: LoadField: r1 = r0->field_b
    //     0xa10a6c: ldur            w1, [x0, #0xb]
    // 0xa10a70: LoadField: r2 = r0->field_f
    //     0xa10a70: ldur            w2, [x0, #0xf]
    // 0xa10a74: DecompressPointer r2
    //     0xa10a74: add             x2, x2, HEAP, lsl #32
    // 0xa10a78: LoadField: r3 = r2->field_b
    //     0xa10a78: ldur            w3, [x2, #0xb]
    // 0xa10a7c: r2 = LoadInt32Instr(r1)
    //     0xa10a7c: sbfx            x2, x1, #1, #0x1f
    // 0xa10a80: stur            x2, [fp, #-0x18]
    // 0xa10a84: r1 = LoadInt32Instr(r3)
    //     0xa10a84: sbfx            x1, x3, #1, #0x1f
    // 0xa10a88: cmp             x2, x1
    // 0xa10a8c: b.ne            #0xa10a98
    // 0xa10a90: mov             x1, x0
    // 0xa10a94: r0 = _growToNextCapacity()
    //     0xa10a94: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa10a98: ldur            x0, [fp, #-0x10]
    // 0xa10a9c: ldur            x1, [fp, #-0x18]
    // 0xa10aa0: add             x2, x1, #1
    // 0xa10aa4: lsl             x3, x2, #1
    // 0xa10aa8: StoreField: r0->field_b = r3
    //     0xa10aa8: stur            w3, [x0, #0xb]
    // 0xa10aac: LoadField: r2 = r0->field_f
    //     0xa10aac: ldur            w2, [x0, #0xf]
    // 0xa10ab0: DecompressPointer r2
    //     0xa10ab0: add             x2, x2, HEAP, lsl #32
    // 0xa10ab4: add             x3, x2, x1, lsl #2
    // 0xa10ab8: r16 = Instance__WidgetsLocalizationsDelegate
    //     0xa10ab8: add             x16, PP, #0x57, lsl #12  ; [pp+0x573a8] Obj!_WidgetsLocalizationsDelegate@e14c91
    //     0xa10abc: ldr             x16, [x16, #0x3a8]
    // 0xa10ac0: StoreField: r3->field_f = r16
    //     0xa10ac0: stur            w16, [x3, #0xf]
    // 0xa10ac4: LeaveFrame
    //     0xa10ac4: mov             SP, fp
    //     0xa10ac8: ldp             fp, lr, [SP], #0x10
    // 0xa10acc: ret
    //     0xa10acc: ret             
    // 0xa10ad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa10ad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa10ad4: b               #0xa10a2c
    // 0xa10ad8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa10ad8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _initialRouteName(/* No info */) {
    // ** addr: 0xa10b0c, size: 0xc8
    // 0xa10b0c: EnterFrame
    //     0xa10b0c: stp             fp, lr, [SP, #-0x10]!
    //     0xa10b10: mov             fp, SP
    // 0xa10b14: AllocStack(0x18)
    //     0xa10b14: sub             SP, SP, #0x18
    // 0xa10b18: SetupParameters(_WidgetsAppState this /* r1 => r1, fp-0x8 */)
    //     0xa10b18: stur            x1, [fp, #-8]
    // 0xa10b1c: CheckStackOverflow
    //     0xa10b1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa10b20: cmp             SP, x16
    //     0xa10b24: b.ls            #0xa10bc0
    // 0xa10b28: r0 = LoadStaticField(0x7d4)
    //     0xa10b28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa10b2c: ldr             x0, [x0, #0xfa8]
    // 0xa10b30: cmp             w0, NULL
    // 0xa10b34: b.eq            #0xa10bc8
    // 0xa10b38: r0 = InitLateStaticField(0x5d8) // [dart:ui] PlatformDispatcher::_instance
    //     0xa10b38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa10b3c: ldr             x0, [x0, #0xbb0]
    //     0xa10b40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa10b44: cmp             w0, w16
    //     0xa10b48: b.ne            #0xa10b54
    //     0xa10b4c: ldr             x2, [PP, #0x428]  ; [pp+0x428] Field <PlatformDispatcher._instance@********>: static late final (offset: 0x5d8)
    //     0xa10b50: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa10b54: r0 = __defaultRouteName$Method$FfiNative()
    //     0xa10b54: bl              #0xa10bd4  ; [dart:ui] PlatformDispatcher::__defaultRouteName$Method$FfiNative
    // 0xa10b58: r1 = LoadClassIdInstr(r0)
    //     0xa10b58: ldur            x1, [x0, #-1]
    //     0xa10b5c: ubfx            x1, x1, #0xc, #0x14
    // 0xa10b60: r16 = "/"
    //     0xa10b60: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xa10b64: stp             x16, x0, [SP]
    // 0xa10b68: mov             x0, x1
    // 0xa10b6c: mov             lr, x0
    // 0xa10b70: ldr             lr, [x21, lr, lsl #3]
    // 0xa10b74: blr             lr
    // 0xa10b78: tbz             w0, #4, #0xa10b94
    // 0xa10b7c: r0 = LoadStaticField(0x7d4)
    //     0xa10b7c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa10b80: ldr             x0, [x0, #0xfa8]
    // 0xa10b84: cmp             w0, NULL
    // 0xa10b88: b.eq            #0xa10bcc
    // 0xa10b8c: r0 = __defaultRouteName$Method$FfiNative()
    //     0xa10b8c: bl              #0xa10bd4  ; [dart:ui] PlatformDispatcher::__defaultRouteName$Method$FfiNative
    // 0xa10b90: b               #0xa10bb4
    // 0xa10b94: ldur            x1, [fp, #-8]
    // 0xa10b98: LoadField: r2 = r1->field_b
    //     0xa10b98: ldur            w2, [x1, #0xb]
    // 0xa10b9c: DecompressPointer r2
    //     0xa10b9c: add             x2, x2, HEAP, lsl #32
    // 0xa10ba0: cmp             w2, NULL
    // 0xa10ba4: b.eq            #0xa10bd0
    // 0xa10ba8: LoadField: r1 = r2->field_33
    //     0xa10ba8: ldur            w1, [x2, #0x33]
    // 0xa10bac: DecompressPointer r1
    //     0xa10bac: add             x1, x1, HEAP, lsl #32
    // 0xa10bb0: mov             x0, x1
    // 0xa10bb4: LeaveFrame
    //     0xa10bb4: mov             SP, fp
    //     0xa10bb8: ldp             fp, lr, [SP], #0x10
    // 0xa10bbc: ret
    //     0xa10bbc: ret             
    // 0xa10bc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa10bc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa10bc4: b               #0xa10b28
    // 0xa10bc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa10bc8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa10bcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa10bcc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa10bd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa10bd0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _usesNavigator(/* No info */) {
    // ** addr: 0xa10d90, size: 0x24
    // 0xa10d90: LoadField: r2 = r1->field_b
    //     0xa10d90: ldur            w2, [x1, #0xb]
    // 0xa10d94: DecompressPointer r2
    //     0xa10d94: add             x2, x2, HEAP, lsl #32
    // 0xa10d98: cmp             w2, NULL
    // 0xa10d9c: b.eq            #0xa10da8
    // 0xa10da0: r0 = true
    //     0xa10da0: add             x0, NULL, #0x20  ; true
    // 0xa10da4: ret
    //     0xa10da4: ret             
    // 0xa10da8: EnterFrame
    //     0xa10da8: stp             fp, lr, [SP, #-0x10]!
    //     0xa10dac: mov             fp, SP
    // 0xa10db0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa10db0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool _defaultOnNavigationNotification(dynamic, NavigationNotification) {
    // ** addr: 0xa10db4, size: 0x3c
    // 0xa10db4: EnterFrame
    //     0xa10db4: stp             fp, lr, [SP, #-0x10]!
    //     0xa10db8: mov             fp, SP
    // 0xa10dbc: ldr             x0, [fp, #0x18]
    // 0xa10dc0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa10dc0: ldur            w1, [x0, #0x17]
    // 0xa10dc4: DecompressPointer r1
    //     0xa10dc4: add             x1, x1, HEAP, lsl #32
    // 0xa10dc8: CheckStackOverflow
    //     0xa10dc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa10dcc: cmp             SP, x16
    //     0xa10dd0: b.ls            #0xa10de8
    // 0xa10dd4: ldr             x2, [fp, #0x10]
    // 0xa10dd8: r0 = _defaultOnNavigationNotification()
    //     0xa10dd8: bl              #0xa10df0  ; [package:flutter/src/widgets/app.dart] _WidgetsAppState::_defaultOnNavigationNotification
    // 0xa10ddc: LeaveFrame
    //     0xa10ddc: mov             SP, fp
    //     0xa10de0: ldp             fp, lr, [SP], #0x10
    // 0xa10de4: ret
    //     0xa10de4: ret             
    // 0xa10de8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa10de8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa10dec: b               #0xa10dd4
  }
  _ _defaultOnNavigationNotification(/* No info */) {
    // ** addr: 0xa10df0, size: 0xa4
    // 0xa10df0: EnterFrame
    //     0xa10df0: stp             fp, lr, [SP, #-0x10]!
    //     0xa10df4: mov             fp, SP
    // 0xa10df8: CheckStackOverflow
    //     0xa10df8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa10dfc: cmp             SP, x16
    //     0xa10e00: b.ls            #0xa10e8c
    // 0xa10e04: LoadField: r0 = r1->field_13
    //     0xa10e04: ldur            w0, [x1, #0x13]
    // 0xa10e08: DecompressPointer r0
    //     0xa10e08: add             x0, x0, HEAP, lsl #32
    // 0xa10e0c: cmp             w0, NULL
    // 0xa10e10: b.eq            #0xa10e20
    // 0xa10e14: r16 = Instance_AppLifecycleState
    //     0xa10e14: ldr             x16, [PP, #0x2f60]  ; [pp+0x2f60] Obj!AppLifecycleState@e39981
    // 0xa10e18: cmp             w0, w16
    // 0xa10e1c: b.ne            #0xa10e30
    // 0xa10e20: r0 = true
    //     0xa10e20: add             x0, NULL, #0x20  ; true
    // 0xa10e24: LeaveFrame
    //     0xa10e24: mov             SP, fp
    //     0xa10e28: ldp             fp, lr, [SP], #0x10
    // 0xa10e2c: ret
    //     0xa10e2c: ret             
    // 0xa10e30: r16 = Instance_AppLifecycleState
    //     0xa10e30: ldr             x16, [PP, #0x2fc8]  ; [pp+0x2fc8] Obj!AppLifecycleState@e39961
    // 0xa10e34: cmp             w0, w16
    // 0xa10e38: b.eq            #0xa10e60
    // 0xa10e3c: r16 = Instance_AppLifecycleState
    //     0xa10e3c: ldr             x16, [PP, #0x2350]  ; [pp+0x2350] Obj!AppLifecycleState@e39901
    // 0xa10e40: cmp             w0, w16
    // 0xa10e44: b.eq            #0xa10e60
    // 0xa10e48: r16 = Instance_AppLifecycleState
    //     0xa10e48: ldr             x16, [PP, #0x2fd8]  ; [pp+0x2fd8] Obj!AppLifecycleState@e39941
    // 0xa10e4c: cmp             w0, w16
    // 0xa10e50: b.eq            #0xa10e60
    // 0xa10e54: r16 = Instance_AppLifecycleState
    //     0xa10e54: ldr             x16, [PP, #0x2fe8]  ; [pp+0x2fe8] Obj!AppLifecycleState@e39921
    // 0xa10e58: cmp             w0, w16
    // 0xa10e5c: b.ne            #0xa10e7c
    // 0xa10e60: LoadField: r1 = r2->field_7
    //     0xa10e60: ldur            w1, [x2, #7]
    // 0xa10e64: DecompressPointer r1
    //     0xa10e64: add             x1, x1, HEAP, lsl #32
    // 0xa10e68: r0 = setFrameworkHandlesBack()
    //     0xa10e68: bl              #0xa10e94  ; [package:flutter/src/services/system_navigator.dart] SystemNavigator::setFrameworkHandlesBack
    // 0xa10e6c: r0 = true
    //     0xa10e6c: add             x0, NULL, #0x20  ; true
    // 0xa10e70: LeaveFrame
    //     0xa10e70: mov             SP, fp
    //     0xa10e74: ldp             fp, lr, [SP], #0x10
    // 0xa10e78: ret
    //     0xa10e78: ret             
    // 0xa10e7c: r0 = Null
    //     0xa10e7c: mov             x0, NULL
    // 0xa10e80: LeaveFrame
    //     0xa10e80: mov             SP, fp
    //     0xa10e84: ldp             fp, lr, [SP], #0x10
    // 0xa10e88: ret
    //     0xa10e88: ret             
    // 0xa10e8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa10e8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa10e90: b               #0xa10e04
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xa11340, size: 0x7c
    // 0xa11340: EnterFrame
    //     0xa11340: stp             fp, lr, [SP, #-0x10]!
    //     0xa11344: mov             fp, SP
    // 0xa11348: ldr             x0, [fp, #0x18]
    // 0xa1134c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa1134c: ldur            w1, [x0, #0x17]
    // 0xa11350: DecompressPointer r1
    //     0xa11350: add             x1, x1, HEAP, lsl #32
    // 0xa11354: CheckStackOverflow
    //     0xa11354: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa11358: cmp             SP, x16
    //     0xa1135c: b.ls            #0xa113ac
    // 0xa11360: LoadField: r0 = r1->field_f
    //     0xa11360: ldur            w0, [x1, #0xf]
    // 0xa11364: DecompressPointer r0
    //     0xa11364: add             x0, x0, HEAP, lsl #32
    // 0xa11368: LoadField: r2 = r0->field_b
    //     0xa11368: ldur            w2, [x0, #0xb]
    // 0xa1136c: DecompressPointer r2
    //     0xa1136c: add             x2, x2, HEAP, lsl #32
    // 0xa11370: cmp             w2, NULL
    // 0xa11374: b.eq            #0xa113b4
    // 0xa11378: LoadField: r0 = r2->field_3b
    //     0xa11378: ldur            w0, [x2, #0x3b]
    // 0xa1137c: DecompressPointer r0
    //     0xa1137c: add             x0, x0, HEAP, lsl #32
    // 0xa11380: LoadField: r3 = r1->field_13
    //     0xa11380: ldur            w3, [x1, #0x13]
    // 0xa11384: DecompressPointer r3
    //     0xa11384: add             x3, x3, HEAP, lsl #32
    // 0xa11388: cmp             w0, NULL
    // 0xa1138c: b.eq            #0xa113b8
    // 0xa11390: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa11390: ldur            w1, [x0, #0x17]
    // 0xa11394: DecompressPointer r1
    //     0xa11394: add             x1, x1, HEAP, lsl #32
    // 0xa11398: ldr             x2, [fp, #0x10]
    // 0xa1139c: r0 = _materialBuilder()
    //     0xa1139c: bl              #0x9e4450  ; [package:flutter/src/material/app.dart] _MaterialAppState::_materialBuilder
    // 0xa113a0: LeaveFrame
    //     0xa113a0: mov             SP, fp
    //     0xa113a4: ldp             fp, lr, [SP], #0x10
    // 0xa113a8: ret
    //     0xa113a8: ret             
    // 0xa113ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa113ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa113b0: b               #0xa11360
    // 0xa113b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa113b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa113b8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa113b8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] List<Route<dynamic>> <anonymous closure>(dynamic, NavigatorState, String) {
    // ** addr: 0xa113bc, size: 0x74
    // 0xa113bc: EnterFrame
    //     0xa113bc: stp             fp, lr, [SP, #-0x10]!
    //     0xa113c0: mov             fp, SP
    // 0xa113c4: ldr             x0, [fp, #0x20]
    // 0xa113c8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa113c8: ldur            w1, [x0, #0x17]
    // 0xa113cc: DecompressPointer r1
    //     0xa113cc: add             x1, x1, HEAP, lsl #32
    // 0xa113d0: CheckStackOverflow
    //     0xa113d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa113d4: cmp             SP, x16
    //     0xa113d8: b.ls            #0xa11420
    // 0xa113dc: LoadField: r0 = r1->field_f
    //     0xa113dc: ldur            w0, [x1, #0xf]
    // 0xa113e0: DecompressPointer r0
    //     0xa113e0: add             x0, x0, HEAP, lsl #32
    // 0xa113e4: LoadField: r1 = r0->field_b
    //     0xa113e4: ldur            w1, [x0, #0xb]
    // 0xa113e8: DecompressPointer r1
    //     0xa113e8: add             x1, x1, HEAP, lsl #32
    // 0xa113ec: cmp             w1, NULL
    // 0xa113f0: b.eq            #0xa11428
    // 0xa113f4: LoadField: r0 = r1->field_13
    //     0xa113f4: ldur            w0, [x1, #0x13]
    // 0xa113f8: DecompressPointer r0
    //     0xa113f8: add             x0, x0, HEAP, lsl #32
    // 0xa113fc: cmp             w0, NULL
    // 0xa11400: b.eq            #0xa1142c
    // 0xa11404: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa11404: ldur            w1, [x0, #0x17]
    // 0xa11408: DecompressPointer r1
    //     0xa11408: add             x1, x1, HEAP, lsl #32
    // 0xa1140c: ldr             x2, [fp, #0x10]
    // 0xa11410: r0 = initialRoutesGenerate()
    //     0xa11410: bl              #0xa1146c  ; [package:get/get_navigation/src/root/get_material_app.dart] GetMaterialApp::initialRoutesGenerate
    // 0xa11414: LeaveFrame
    //     0xa11414: mov             SP, fp
    //     0xa11418: ldp             fp, lr, [SP], #0x10
    // 0xa1141c: ret
    //     0xa1141c: ret             
    // 0xa11420: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa11420: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa11424: b               #0xa113dc
    // 0xa11428: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa11428: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1142c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa1142c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
}

// class id: 4799, size: 0x78, field offset: 0xc
class WidgetsApp extends StatefulWidget {

  static late Map<Type, Action<Intent>> defaultActions; // offset: 0x7cc

  _ WidgetsApp(/* No info */) {
    // ** addr: 0x9e40f8, size: 0x1d8
    // 0x9e40f8: EnterFrame
    //     0x9e40f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9e40fc: mov             fp, SP
    // 0x9e4100: r11 = _ConstMap len:0
    //     0x9e4100: add             x11, PP, #0x1a, lsl #12  ; [pp+0x1aa60] Map<String, (dynamic this, BuildContext) => Widget>(0)
    //     0x9e4104: ldr             x11, [x11, #0xa60]
    // 0x9e4108: r10 = "NU Online"
    //     0x9e4108: add             x10, PP, #0x23, lsl #12  ; [pp+0x23ba0] "NU Online"
    //     0x9e410c: ldr             x10, [x10, #0xba0]
    // 0x9e4110: r9 = Instance_TextStyle
    //     0x9e4110: add             x9, PP, #0x44, lsl #12  ; [pp+0x448c0] Obj!TextStyle@e1b0a1
    //     0x9e4114: ldr             x9, [x9, #0x8c0]
    // 0x9e4118: r8 = const [Instance of 'Locale']
    //     0x9e4118: add             x8, PP, #0x23, lsl #12  ; [pp+0x23bb0] List<Locale>(1)
    //     0x9e411c: ldr             x8, [x8, #0xbb0]
    // 0x9e4120: r4 = false
    //     0x9e4120: add             x4, NULL, #0x30  ; false
    // 0x9e4124: ldr             x0, [fp, #0x30]
    // 0x9e4128: mov             x16, x7
    // 0x9e412c: mov             x7, x1
    // 0x9e4130: mov             x1, x16
    // 0x9e4134: mov             x16, x6
    // 0x9e4138: mov             x6, x2
    // 0x9e413c: mov             x2, x16
    // 0x9e4140: mov             x16, x5
    // 0x9e4144: mov             x5, x3
    // 0x9e4148: mov             x3, x16
    // 0x9e414c: StoreField: r7->field_b = r0
    //     0x9e414c: stur            w0, [x7, #0xb]
    //     0x9e4150: ldurb           w16, [x7, #-1]
    //     0x9e4154: ldurb           w17, [x0, #-1]
    //     0x9e4158: and             x16, x17, x16, lsr #2
    //     0x9e415c: tst             x16, HEAP, lsr #32
    //     0x9e4160: b.eq            #0x9e4168
    //     0x9e4164: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0x9e4168: ldr             x0, [fp, #0x18]
    // 0x9e416c: StoreField: r7->field_f = r0
    //     0x9e416c: stur            w0, [x7, #0xf]
    //     0x9e4170: ldurb           w16, [x7, #-1]
    //     0x9e4174: ldurb           w17, [x0, #-1]
    //     0x9e4178: and             x16, x17, x16, lsr #2
    //     0x9e417c: tst             x16, HEAP, lsr #32
    //     0x9e4180: b.eq            #0x9e4188
    //     0x9e4184: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0x9e4188: ldr             x0, [fp, #0x20]
    // 0x9e418c: StoreField: r7->field_13 = r0
    //     0x9e418c: stur            w0, [x7, #0x13]
    //     0x9e4190: ldurb           w16, [x7, #-1]
    //     0x9e4194: ldurb           w17, [x0, #-1]
    //     0x9e4198: and             x16, x17, x16, lsr #2
    //     0x9e419c: tst             x16, HEAP, lsr #32
    //     0x9e41a0: b.eq            #0x9e41a8
    //     0x9e41a4: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0x9e41a8: ldr             x0, [fp, #0x28]
    // 0x9e41ac: StoreField: r7->field_37 = r0
    //     0x9e41ac: stur            w0, [x7, #0x37]
    //     0x9e41b0: ldurb           w16, [x7, #-1]
    //     0x9e41b4: ldurb           w17, [x0, #-1]
    //     0x9e41b8: and             x16, x17, x16, lsr #2
    //     0x9e41bc: tst             x16, HEAP, lsr #32
    //     0x9e41c0: b.eq            #0x9e41c8
    //     0x9e41c4: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0x9e41c8: mov             x0, x3
    // 0x9e41cc: StoreField: r7->field_33 = r0
    //     0x9e41cc: stur            w0, [x7, #0x33]
    //     0x9e41d0: ldurb           w16, [x7, #-1]
    //     0x9e41d4: ldurb           w17, [x0, #-1]
    //     0x9e41d8: and             x16, x17, x16, lsr #2
    //     0x9e41dc: tst             x16, HEAP, lsr #32
    //     0x9e41e0: b.eq            #0x9e41e8
    //     0x9e41e4: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0x9e41e8: ldr             x0, [fp, #0x10]
    // 0x9e41ec: ArrayStore: r7[0] = r0  ; List_4
    //     0x9e41ec: stur            w0, [x7, #0x17]
    //     0x9e41f0: ldurb           w16, [x7, #-1]
    //     0x9e41f4: ldurb           w17, [x0, #-1]
    //     0x9e41f8: and             x16, x17, x16, lsr #2
    //     0x9e41fc: tst             x16, HEAP, lsr #32
    //     0x9e4200: b.eq            #0x9e4208
    //     0x9e4204: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0x9e4208: StoreField: r7->field_27 = r11
    //     0x9e4208: stur            w11, [x7, #0x27]
    // 0x9e420c: mov             x0, x6
    // 0x9e4210: StoreField: r7->field_3b = r0
    //     0x9e4210: stur            w0, [x7, #0x3b]
    //     0x9e4214: ldurb           w16, [x7, #-1]
    //     0x9e4218: ldurb           w17, [x0, #-1]
    //     0x9e421c: and             x16, x17, x16, lsr #2
    //     0x9e4220: tst             x16, HEAP, lsr #32
    //     0x9e4224: b.eq            #0x9e422c
    //     0x9e4228: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0x9e422c: StoreField: r7->field_3f = r10
    //     0x9e422c: stur            w10, [x7, #0x3f]
    // 0x9e4230: StoreField: r7->field_47 = r9
    //     0x9e4230: stur            w9, [x7, #0x47]
    // 0x9e4234: mov             x0, x5
    // 0x9e4238: StoreField: r7->field_4b = r0
    //     0x9e4238: stur            w0, [x7, #0x4b]
    //     0x9e423c: ldurb           w16, [x7, #-1]
    //     0x9e4240: ldurb           w17, [x0, #-1]
    //     0x9e4244: and             x16, x17, x16, lsr #2
    //     0x9e4248: tst             x16, HEAP, lsr #32
    //     0x9e424c: b.eq            #0x9e4254
    //     0x9e4250: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0x9e4254: mov             x0, x1
    // 0x9e4258: StoreField: r7->field_4f = r0
    //     0x9e4258: stur            w0, [x7, #0x4f]
    //     0x9e425c: ldurb           w16, [x7, #-1]
    //     0x9e4260: ldurb           w17, [x0, #-1]
    //     0x9e4264: and             x16, x17, x16, lsr #2
    //     0x9e4268: tst             x16, HEAP, lsr #32
    //     0x9e426c: b.eq            #0x9e4274
    //     0x9e4270: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0x9e4274: ldr             x0, [fp, #0x38]
    // 0x9e4278: StoreField: r7->field_53 = r0
    //     0x9e4278: stur            w0, [x7, #0x53]
    //     0x9e427c: ldurb           w16, [x7, #-1]
    //     0x9e4280: ldurb           w17, [x0, #-1]
    //     0x9e4284: and             x16, x17, x16, lsr #2
    //     0x9e4288: tst             x16, HEAP, lsr #32
    //     0x9e428c: b.eq            #0x9e4294
    //     0x9e4290: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0x9e4294: StoreField: r7->field_5f = r8
    //     0x9e4294: stur            w8, [x7, #0x5f]
    // 0x9e4298: StoreField: r7->field_63 = r4
    //     0x9e4298: stur            w4, [x7, #0x63]
    // 0x9e429c: StoreField: r7->field_67 = r4
    //     0x9e429c: stur            w4, [x7, #0x67]
    // 0x9e42a0: mov             x0, x2
    // 0x9e42a4: StoreField: r7->field_7 = r0
    //     0x9e42a4: stur            w0, [x7, #7]
    //     0x9e42a8: ldurb           w16, [x7, #-1]
    //     0x9e42ac: ldurb           w17, [x0, #-1]
    //     0x9e42b0: and             x16, x17, x16, lsr #2
    //     0x9e42b4: tst             x16, HEAP, lsr #32
    //     0x9e42b8: b.eq            #0x9e42c0
    //     0x9e42bc: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0x9e42c0: r0 = Null
    //     0x9e42c0: mov             x0, NULL
    // 0x9e42c4: LeaveFrame
    //     0x9e42c4: mov             SP, fp
    //     0x9e42c8: ldp             fp, lr, [SP], #0x10
    // 0x9e42cc: ret
    //     0x9e42cc: ret             
  }
  static Map<Type, Action<Intent>> defaultActions() {
    // ** addr: 0xa10f2c, size: 0x36c
    // 0xa10f2c: EnterFrame
    //     0xa10f2c: stp             fp, lr, [SP, #-0x10]!
    //     0xa10f30: mov             fp, SP
    // 0xa10f34: AllocStack(0x20)
    //     0xa10f34: sub             SP, SP, #0x20
    // 0xa10f38: CheckStackOverflow
    //     0xa10f38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa10f3c: cmp             SP, x16
    //     0xa10f40: b.ls            #0xa11290
    // 0xa10f44: r1 = Null
    //     0xa10f44: mov             x1, NULL
    // 0xa10f48: r2 = 36
    //     0xa10f48: movz            x2, #0x24
    // 0xa10f4c: r0 = AllocateArray()
    //     0xa10f4c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa10f50: stur            x0, [fp, #-8]
    // 0xa10f54: r16 = DoNothingIntent
    //     0xa10f54: add             x16, PP, #0x57, lsl #12  ; [pp+0x573d8] Type: DoNothingIntent
    //     0xa10f58: ldr             x16, [x16, #0x3d8]
    // 0xa10f5c: StoreField: r0->field_f = r16
    //     0xa10f5c: stur            w16, [x0, #0xf]
    // 0xa10f60: r1 = <Intent>
    //     0xa10f60: ldr             x1, [PP, #0x4388]  ; [pp+0x4388] TypeArguments: <Intent>
    // 0xa10f64: r0 = DoNothingAction()
    //     0xa10f64: bl              #0xa112e0  ; AllocateDoNothingActionStub -> DoNothingAction (size=0x18)
    // 0xa10f68: mov             x2, x0
    // 0xa10f6c: r0 = true
    //     0xa10f6c: add             x0, NULL, #0x20  ; true
    // 0xa10f70: stur            x2, [fp, #-0x10]
    // 0xa10f74: StoreField: r2->field_13 = r0
    //     0xa10f74: stur            w0, [x2, #0x13]
    // 0xa10f78: mov             x1, x2
    // 0xa10f7c: r0 = Action()
    //     0xa10f7c: bl              #0x940bc8  ; [package:flutter/src/widgets/actions.dart] Action::Action
    // 0xa10f80: ldur            x1, [fp, #-8]
    // 0xa10f84: ldur            x0, [fp, #-0x10]
    // 0xa10f88: ArrayStore: r1[1] = r0  ; List_4
    //     0xa10f88: add             x25, x1, #0x13
    //     0xa10f8c: str             w0, [x25]
    //     0xa10f90: tbz             w0, #0, #0xa10fac
    //     0xa10f94: ldurb           w16, [x1, #-1]
    //     0xa10f98: ldurb           w17, [x0, #-1]
    //     0xa10f9c: and             x16, x17, x16, lsr #2
    //     0xa10fa0: tst             x16, HEAP, lsr #32
    //     0xa10fa4: b.eq            #0xa10fac
    //     0xa10fa8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa10fac: ldur            x0, [fp, #-8]
    // 0xa10fb0: r16 = DoNothingAndStopPropagationIntent
    //     0xa10fb0: add             x16, PP, #0x57, lsl #12  ; [pp+0x573e0] Type: DoNothingAndStopPropagationIntent
    //     0xa10fb4: ldr             x16, [x16, #0x3e0]
    // 0xa10fb8: ArrayStore: r0[0] = r16  ; List_4
    //     0xa10fb8: stur            w16, [x0, #0x17]
    // 0xa10fbc: r1 = <Intent>
    //     0xa10fbc: ldr             x1, [PP, #0x4388]  ; [pp+0x4388] TypeArguments: <Intent>
    // 0xa10fc0: r0 = DoNothingAction()
    //     0xa10fc0: bl              #0xa112e0  ; AllocateDoNothingActionStub -> DoNothingAction (size=0x18)
    // 0xa10fc4: mov             x2, x0
    // 0xa10fc8: r0 = false
    //     0xa10fc8: add             x0, NULL, #0x30  ; false
    // 0xa10fcc: stur            x2, [fp, #-0x10]
    // 0xa10fd0: StoreField: r2->field_13 = r0
    //     0xa10fd0: stur            w0, [x2, #0x13]
    // 0xa10fd4: mov             x1, x2
    // 0xa10fd8: r0 = Action()
    //     0xa10fd8: bl              #0x940bc8  ; [package:flutter/src/widgets/actions.dart] Action::Action
    // 0xa10fdc: ldur            x1, [fp, #-8]
    // 0xa10fe0: ldur            x0, [fp, #-0x10]
    // 0xa10fe4: ArrayStore: r1[3] = r0  ; List_4
    //     0xa10fe4: add             x25, x1, #0x1b
    //     0xa10fe8: str             w0, [x25]
    //     0xa10fec: tbz             w0, #0, #0xa11008
    //     0xa10ff0: ldurb           w16, [x1, #-1]
    //     0xa10ff4: ldurb           w17, [x0, #-1]
    //     0xa10ff8: and             x16, x17, x16, lsr #2
    //     0xa10ffc: tst             x16, HEAP, lsr #32
    //     0xa11000: b.eq            #0xa11008
    //     0xa11004: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa11008: ldur            x0, [fp, #-8]
    // 0xa1100c: r16 = RequestFocusIntent
    //     0xa1100c: add             x16, PP, #0x57, lsl #12  ; [pp+0x573e8] Type: RequestFocusIntent
    //     0xa11010: ldr             x16, [x16, #0x3e8]
    // 0xa11014: StoreField: r0->field_1f = r16
    //     0xa11014: stur            w16, [x0, #0x1f]
    // 0xa11018: r1 = <RequestFocusIntent>
    //     0xa11018: add             x1, PP, #0x57, lsl #12  ; [pp+0x573f0] TypeArguments: <RequestFocusIntent>
    //     0xa1101c: ldr             x1, [x1, #0x3f0]
    // 0xa11020: r0 = RequestFocusAction()
    //     0xa11020: bl              #0xa112d4  ; AllocateRequestFocusActionStub -> RequestFocusAction (size=0x14)
    // 0xa11024: mov             x1, x0
    // 0xa11028: stur            x0, [fp, #-0x10]
    // 0xa1102c: r0 = Action()
    //     0xa1102c: bl              #0x940bc8  ; [package:flutter/src/widgets/actions.dart] Action::Action
    // 0xa11030: ldur            x1, [fp, #-8]
    // 0xa11034: ldur            x0, [fp, #-0x10]
    // 0xa11038: ArrayStore: r1[5] = r0  ; List_4
    //     0xa11038: add             x25, x1, #0x23
    //     0xa1103c: str             w0, [x25]
    //     0xa11040: tbz             w0, #0, #0xa1105c
    //     0xa11044: ldurb           w16, [x1, #-1]
    //     0xa11048: ldurb           w17, [x0, #-1]
    //     0xa1104c: and             x16, x17, x16, lsr #2
    //     0xa11050: tst             x16, HEAP, lsr #32
    //     0xa11054: b.eq            #0xa1105c
    //     0xa11058: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa1105c: ldur            x0, [fp, #-8]
    // 0xa11060: r16 = NextFocusIntent
    //     0xa11060: add             x16, PP, #0x57, lsl #12  ; [pp+0x573f8] Type: NextFocusIntent
    //     0xa11064: ldr             x16, [x16, #0x3f8]
    // 0xa11068: StoreField: r0->field_27 = r16
    //     0xa11068: stur            w16, [x0, #0x27]
    // 0xa1106c: r1 = <NextFocusIntent>
    //     0xa1106c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57400] TypeArguments: <NextFocusIntent>
    //     0xa11070: ldr             x1, [x1, #0x400]
    // 0xa11074: r0 = NextFocusAction()
    //     0xa11074: bl              #0xa112c8  ; AllocateNextFocusActionStub -> NextFocusAction (size=0x14)
    // 0xa11078: mov             x1, x0
    // 0xa1107c: stur            x0, [fp, #-0x10]
    // 0xa11080: r0 = Action()
    //     0xa11080: bl              #0x940bc8  ; [package:flutter/src/widgets/actions.dart] Action::Action
    // 0xa11084: ldur            x1, [fp, #-8]
    // 0xa11088: ldur            x0, [fp, #-0x10]
    // 0xa1108c: ArrayStore: r1[7] = r0  ; List_4
    //     0xa1108c: add             x25, x1, #0x2b
    //     0xa11090: str             w0, [x25]
    //     0xa11094: tbz             w0, #0, #0xa110b0
    //     0xa11098: ldurb           w16, [x1, #-1]
    //     0xa1109c: ldurb           w17, [x0, #-1]
    //     0xa110a0: and             x16, x17, x16, lsr #2
    //     0xa110a4: tst             x16, HEAP, lsr #32
    //     0xa110a8: b.eq            #0xa110b0
    //     0xa110ac: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa110b0: ldur            x0, [fp, #-8]
    // 0xa110b4: r16 = PreviousFocusIntent
    //     0xa110b4: add             x16, PP, #0x57, lsl #12  ; [pp+0x57408] Type: PreviousFocusIntent
    //     0xa110b8: ldr             x16, [x16, #0x408]
    // 0xa110bc: StoreField: r0->field_2f = r16
    //     0xa110bc: stur            w16, [x0, #0x2f]
    // 0xa110c0: r1 = <PreviousFocusIntent>
    //     0xa110c0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57410] TypeArguments: <PreviousFocusIntent>
    //     0xa110c4: ldr             x1, [x1, #0x410]
    // 0xa110c8: r0 = PreviousFocusAction()
    //     0xa110c8: bl              #0xa112bc  ; AllocatePreviousFocusActionStub -> PreviousFocusAction (size=0x14)
    // 0xa110cc: mov             x1, x0
    // 0xa110d0: stur            x0, [fp, #-0x10]
    // 0xa110d4: r0 = Action()
    //     0xa110d4: bl              #0x940bc8  ; [package:flutter/src/widgets/actions.dart] Action::Action
    // 0xa110d8: ldur            x1, [fp, #-8]
    // 0xa110dc: ldur            x0, [fp, #-0x10]
    // 0xa110e0: ArrayStore: r1[9] = r0  ; List_4
    //     0xa110e0: add             x25, x1, #0x33
    //     0xa110e4: str             w0, [x25]
    //     0xa110e8: tbz             w0, #0, #0xa11104
    //     0xa110ec: ldurb           w16, [x1, #-1]
    //     0xa110f0: ldurb           w17, [x0, #-1]
    //     0xa110f4: and             x16, x17, x16, lsr #2
    //     0xa110f8: tst             x16, HEAP, lsr #32
    //     0xa110fc: b.eq            #0xa11104
    //     0xa11100: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa11104: ldur            x0, [fp, #-8]
    // 0xa11108: r16 = DirectionalFocusIntent
    //     0xa11108: add             x16, PP, #0x57, lsl #12  ; [pp+0x57020] Type: DirectionalFocusIntent
    //     0xa1110c: ldr             x16, [x16, #0x20]
    // 0xa11110: StoreField: r0->field_37 = r16
    //     0xa11110: stur            w16, [x0, #0x37]
    // 0xa11114: r1 = <DirectionalFocusIntent>
    //     0xa11114: add             x1, PP, #0x57, lsl #12  ; [pp+0x57028] TypeArguments: <DirectionalFocusIntent>
    //     0xa11118: ldr             x1, [x1, #0x28]
    // 0xa1111c: r0 = DirectionalFocusAction()
    //     0xa1111c: bl              #0xa112b0  ; AllocateDirectionalFocusActionStub -> DirectionalFocusAction (size=0x18)
    // 0xa11120: mov             x2, x0
    // 0xa11124: r0 = false
    //     0xa11124: add             x0, NULL, #0x30  ; false
    // 0xa11128: stur            x2, [fp, #-0x10]
    // 0xa1112c: StoreField: r2->field_13 = r0
    //     0xa1112c: stur            w0, [x2, #0x13]
    // 0xa11130: mov             x1, x2
    // 0xa11134: r0 = Action()
    //     0xa11134: bl              #0x940bc8  ; [package:flutter/src/widgets/actions.dart] Action::Action
    // 0xa11138: ldur            x1, [fp, #-8]
    // 0xa1113c: ldur            x0, [fp, #-0x10]
    // 0xa11140: ArrayStore: r1[11] = r0  ; List_4
    //     0xa11140: add             x25, x1, #0x3b
    //     0xa11144: str             w0, [x25]
    //     0xa11148: tbz             w0, #0, #0xa11164
    //     0xa1114c: ldurb           w16, [x1, #-1]
    //     0xa11150: ldurb           w17, [x0, #-1]
    //     0xa11154: and             x16, x17, x16, lsr #2
    //     0xa11158: tst             x16, HEAP, lsr #32
    //     0xa1115c: b.eq            #0xa11164
    //     0xa11160: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa11164: ldur            x0, [fp, #-8]
    // 0xa11168: r16 = ScrollIntent
    //     0xa11168: add             x16, PP, #0x57, lsl #12  ; [pp+0x57100] Type: ScrollIntent
    //     0xa1116c: ldr             x16, [x16, #0x100]
    // 0xa11170: StoreField: r0->field_3f = r16
    //     0xa11170: stur            w16, [x0, #0x3f]
    // 0xa11174: r1 = <ScrollIntent>
    //     0xa11174: add             x1, PP, #0x57, lsl #12  ; [pp+0x57108] TypeArguments: <ScrollIntent>
    //     0xa11178: ldr             x1, [x1, #0x108]
    // 0xa1117c: r0 = ScrollAction()
    //     0xa1117c: bl              #0xa10adc  ; AllocateScrollActionStub -> ScrollAction (size=0x14)
    // 0xa11180: mov             x1, x0
    // 0xa11184: stur            x0, [fp, #-0x10]
    // 0xa11188: r0 = Action()
    //     0xa11188: bl              #0x940bc8  ; [package:flutter/src/widgets/actions.dart] Action::Action
    // 0xa1118c: ldur            x1, [fp, #-8]
    // 0xa11190: ldur            x0, [fp, #-0x10]
    // 0xa11194: ArrayStore: r1[13] = r0  ; List_4
    //     0xa11194: add             x25, x1, #0x43
    //     0xa11198: str             w0, [x25]
    //     0xa1119c: tbz             w0, #0, #0xa111b8
    //     0xa111a0: ldurb           w16, [x1, #-1]
    //     0xa111a4: ldurb           w17, [x0, #-1]
    //     0xa111a8: and             x16, x17, x16, lsr #2
    //     0xa111ac: tst             x16, HEAP, lsr #32
    //     0xa111b0: b.eq            #0xa111b8
    //     0xa111b4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa111b8: ldur            x0, [fp, #-8]
    // 0xa111bc: r16 = PrioritizedIntents
    //     0xa111bc: add             x16, PP, #0x57, lsl #12  ; [pp+0x57418] Type: PrioritizedIntents
    //     0xa111c0: ldr             x16, [x16, #0x418]
    // 0xa111c4: StoreField: r0->field_47 = r16
    //     0xa111c4: stur            w16, [x0, #0x47]
    // 0xa111c8: r1 = <PrioritizedIntents>
    //     0xa111c8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57420] TypeArguments: <PrioritizedIntents>
    //     0xa111cc: ldr             x1, [x1, #0x420]
    // 0xa111d0: r0 = PrioritizedAction()
    //     0xa111d0: bl              #0xa112a4  ; AllocatePrioritizedActionStub -> PrioritizedAction (size=0x1c)
    // 0xa111d4: mov             x2, x0
    // 0xa111d8: r0 = Sentinel
    //     0xa111d8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa111dc: stur            x2, [fp, #-0x10]
    // 0xa111e0: StoreField: r2->field_13 = r0
    //     0xa111e0: stur            w0, [x2, #0x13]
    // 0xa111e4: ArrayStore: r2[0] = r0  ; List_4
    //     0xa111e4: stur            w0, [x2, #0x17]
    // 0xa111e8: mov             x1, x2
    // 0xa111ec: r0 = Action()
    //     0xa111ec: bl              #0x940bc8  ; [package:flutter/src/widgets/actions.dart] Action::Action
    // 0xa111f0: ldur            x1, [fp, #-8]
    // 0xa111f4: ldur            x0, [fp, #-0x10]
    // 0xa111f8: ArrayStore: r1[15] = r0  ; List_4
    //     0xa111f8: add             x25, x1, #0x4b
    //     0xa111fc: str             w0, [x25]
    //     0xa11200: tbz             w0, #0, #0xa1121c
    //     0xa11204: ldurb           w16, [x1, #-1]
    //     0xa11208: ldurb           w17, [x0, #-1]
    //     0xa1120c: and             x16, x17, x16, lsr #2
    //     0xa11210: tst             x16, HEAP, lsr #32
    //     0xa11214: b.eq            #0xa1121c
    //     0xa11218: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa1121c: ldur            x0, [fp, #-8]
    // 0xa11220: r16 = VoidCallbackIntent
    //     0xa11220: add             x16, PP, #0x57, lsl #12  ; [pp+0x57428] Type: VoidCallbackIntent
    //     0xa11224: ldr             x16, [x16, #0x428]
    // 0xa11228: StoreField: r0->field_4f = r16
    //     0xa11228: stur            w16, [x0, #0x4f]
    // 0xa1122c: r1 = <VoidCallbackIntent>
    //     0xa1122c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57430] TypeArguments: <VoidCallbackIntent>
    //     0xa11230: ldr             x1, [x1, #0x430]
    // 0xa11234: r0 = VoidCallbackAction()
    //     0xa11234: bl              #0xa11298  ; AllocateVoidCallbackActionStub -> VoidCallbackAction (size=0x14)
    // 0xa11238: mov             x1, x0
    // 0xa1123c: stur            x0, [fp, #-0x10]
    // 0xa11240: r0 = Action()
    //     0xa11240: bl              #0x940bc8  ; [package:flutter/src/widgets/actions.dart] Action::Action
    // 0xa11244: ldur            x1, [fp, #-8]
    // 0xa11248: ldur            x0, [fp, #-0x10]
    // 0xa1124c: ArrayStore: r1[17] = r0  ; List_4
    //     0xa1124c: add             x25, x1, #0x53
    //     0xa11250: str             w0, [x25]
    //     0xa11254: tbz             w0, #0, #0xa11270
    //     0xa11258: ldurb           w16, [x1, #-1]
    //     0xa1125c: ldurb           w17, [x0, #-1]
    //     0xa11260: and             x16, x17, x16, lsr #2
    //     0xa11264: tst             x16, HEAP, lsr #32
    //     0xa11268: b.eq            #0xa11270
    //     0xa1126c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa11270: r16 = <Type, Action<Intent>>
    //     0xa11270: add             x16, PP, #0x25, lsl #12  ; [pp+0x25448] TypeArguments: <Type, Action<Intent>>
    //     0xa11274: ldr             x16, [x16, #0x448]
    // 0xa11278: ldur            lr, [fp, #-8]
    // 0xa1127c: stp             lr, x16, [SP]
    // 0xa11280: r0 = Map._fromLiteral()
    //     0xa11280: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa11284: LeaveFrame
    //     0xa11284: mov             SP, fp
    //     0xa11288: ldp             fp, lr, [SP], #0x10
    // 0xa1128c: ret
    //     0xa1128c: ret             
    // 0xa11290: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa11290: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa11294: b               #0xa10f44
  }
  _ createState(/* No info */) {
    // ** addr: 0xa91684, size: 0x24
    // 0xa91684: EnterFrame
    //     0xa91684: stp             fp, lr, [SP, #-0x10]!
    //     0xa91688: mov             fp, SP
    // 0xa9168c: mov             x0, x1
    // 0xa91690: r1 = <WidgetsApp>
    //     0xa91690: add             x1, PP, #0x50, lsl #12  ; [pp+0x50788] TypeArguments: <WidgetsApp>
    //     0xa91694: ldr             x1, [x1, #0x788]
    // 0xa91698: r0 = _WidgetsAppState()
    //     0xa91698: bl              #0xa916a8  ; Allocate_WidgetsAppStateStub -> _WidgetsAppState (size=0x24)
    // 0xa9169c: LeaveFrame
    //     0xa9169c: mov             SP, fp
    //     0xa916a0: ldp             fp, lr, [SP], #0x10
    // 0xa916a4: ret
    //     0xa916a4: ret             
  }
}
