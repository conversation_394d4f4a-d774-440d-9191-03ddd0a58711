// lib: , url: package:flutter/src/widgets/focus_manager.dart

// class id: 1049128, size: 0x8
class :: {

  static _ combineKeyEventResults(/* No info */) {
    // ** addr: 0x693804, size: 0x10c
    // 0x693804: EnterFrame
    //     0x693804: stp             fp, lr, [SP, #-0x10]!
    //     0x693808: mov             fp, SP
    // 0x69380c: AllocStack(0x30)
    //     0x69380c: sub             SP, SP, #0x30
    // 0x693810: LoadField: r3 = r1->field_7
    //     0x693810: ldur            w3, [x1, #7]
    // 0x693814: DecompressPointer r3
    //     0x693814: add             x3, x3, HEAP, lsl #32
    // 0x693818: stur            x3, [fp, #-0x30]
    // 0x69381c: LoadField: r0 = r1->field_b
    //     0x69381c: ldur            w0, [x1, #0xb]
    // 0x693820: r4 = LoadInt32Instr(r0)
    //     0x693820: sbfx            x4, x0, #1, #0x1f
    // 0x693824: stur            x4, [fp, #-0x28]
    // 0x693828: LoadField: r5 = r1->field_f
    //     0x693828: ldur            w5, [x1, #0xf]
    // 0x69382c: DecompressPointer r5
    //     0x69382c: add             x5, x5, HEAP, lsl #32
    // 0x693830: stur            x5, [fp, #-0x20]
    // 0x693834: r6 = false
    //     0x693834: add             x6, NULL, #0x30  ; false
    // 0x693838: r0 = 0
    //     0x693838: movz            x0, #0
    // 0x69383c: stur            x6, [fp, #-0x18]
    // 0x693840: CheckStackOverflow
    //     0x693840: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x693844: cmp             SP, x16
    //     0x693848: b.ls            #0x693908
    // 0x69384c: cmp             x0, x4
    // 0x693850: b.ge            #0x6938e8
    // 0x693854: ArrayLoad: r7 = r5[r0]  ; Unknown_4
    //     0x693854: add             x16, x5, x0, lsl #2
    //     0x693858: ldur            w7, [x16, #0xf]
    // 0x69385c: DecompressPointer r7
    //     0x69385c: add             x7, x7, HEAP, lsl #32
    // 0x693860: stur            x7, [fp, #-0x10]
    // 0x693864: add             x8, x0, #1
    // 0x693868: stur            x8, [fp, #-8]
    // 0x69386c: cmp             w7, NULL
    // 0x693870: b.ne            #0x6938a0
    // 0x693874: mov             x0, x7
    // 0x693878: mov             x2, x3
    // 0x69387c: r1 = Null
    //     0x69387c: mov             x1, NULL
    // 0x693880: cmp             w2, NULL
    // 0x693884: b.eq            #0x6938a0
    // 0x693888: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x693888: ldur            w4, [x2, #0x17]
    // 0x69388c: DecompressPointer r4
    //     0x69388c: add             x4, x4, HEAP, lsl #32
    // 0x693890: r8 = X0
    //     0x693890: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x693894: LoadField: r9 = r4->field_7
    //     0x693894: ldur            x9, [x4, #7]
    // 0x693898: r3 = Null
    //     0x693898: ldr             x3, [PP, #0x22a0]  ; [pp+0x22a0] Null
    // 0x69389c: blr             x9
    // 0x6938a0: ldur            x1, [fp, #-0x10]
    // 0x6938a4: LoadField: r2 = r1->field_7
    //     0x6938a4: ldur            x2, [x1, #7]
    // 0x6938a8: cmp             x2, #1
    // 0x6938ac: b.gt            #0x6938d0
    // 0x6938b0: cmp             x2, #0
    // 0x6938b4: b.le            #0x6938c0
    // 0x6938b8: ldur            x6, [fp, #-0x18]
    // 0x6938bc: b               #0x6938d4
    // 0x6938c0: r0 = Instance_KeyEventResult
    //     0x6938c0: ldr             x0, [PP, #0x2228]  ; [pp+0x2228] Obj!KeyEventResult@e34601
    // 0x6938c4: LeaveFrame
    //     0x6938c4: mov             SP, fp
    //     0x6938c8: ldp             fp, lr, [SP], #0x10
    // 0x6938cc: ret
    //     0x6938cc: ret             
    // 0x6938d0: r6 = true
    //     0x6938d0: add             x6, NULL, #0x20  ; true
    // 0x6938d4: ldur            x0, [fp, #-8]
    // 0x6938d8: ldur            x3, [fp, #-0x30]
    // 0x6938dc: ldur            x5, [fp, #-0x20]
    // 0x6938e0: ldur            x4, [fp, #-0x28]
    // 0x6938e4: b               #0x69383c
    // 0x6938e8: mov             x1, x6
    // 0x6938ec: tbnz            w1, #4, #0x6938f8
    // 0x6938f0: r0 = Instance_KeyEventResult
    //     0x6938f0: ldr             x0, [PP, #0x2230]  ; [pp+0x2230] Obj!KeyEventResult@e345e1
    // 0x6938f4: b               #0x6938fc
    // 0x6938f8: r0 = Instance_KeyEventResult
    //     0x6938f8: ldr             x0, [PP, #0x2238]  ; [pp+0x2238] Obj!KeyEventResult@e345c1
    // 0x6938fc: LeaveFrame
    //     0x6938fc: mov             SP, fp
    //     0x693900: ldp             fp, lr, [SP], #0x10
    // 0x693904: ret
    //     0x693904: ret             
    // 0x693908: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x693908: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x69390c: b               #0x69384c
  }
  get _ primaryFocus(/* No info */) {
    // ** addr: 0x6bc014, size: 0x4c
    // 0x6bc014: EnterFrame
    //     0x6bc014: stp             fp, lr, [SP, #-0x10]!
    //     0x6bc018: mov             fp, SP
    // 0x6bc01c: r1 = LoadStaticField(0x7d4)
    //     0x6bc01c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x6bc020: ldr             x1, [x1, #0xfa8]
    // 0x6bc024: cmp             w1, NULL
    // 0x6bc028: b.eq            #0x6bc058
    // 0x6bc02c: LoadField: r2 = r1->field_eb
    //     0x6bc02c: ldur            w2, [x1, #0xeb]
    // 0x6bc030: DecompressPointer r2
    //     0x6bc030: add             x2, x2, HEAP, lsl #32
    // 0x6bc034: cmp             w2, NULL
    // 0x6bc038: b.eq            #0x6bc05c
    // 0x6bc03c: LoadField: r1 = r2->field_13
    //     0x6bc03c: ldur            w1, [x2, #0x13]
    // 0x6bc040: DecompressPointer r1
    //     0x6bc040: add             x1, x1, HEAP, lsl #32
    // 0x6bc044: LoadField: r0 = r1->field_2b
    //     0x6bc044: ldur            w0, [x1, #0x2b]
    // 0x6bc048: DecompressPointer r0
    //     0x6bc048: add             x0, x0, HEAP, lsl #32
    // 0x6bc04c: LeaveFrame
    //     0x6bc04c: mov             SP, fp
    //     0x6bc050: ldp             fp, lr, [SP], #0x10
    // 0x6bc054: ret
    //     0x6bc054: ret             
    // 0x6bc058: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6bc058: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6bc05c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6bc05c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 2696, size: 0x20, field offset: 0x8
class _HighlightModeManager extends Object {

  _ registerGlobalHandlers(/* No info */) {
    // ** addr: 0x692668, size: 0xc0
    // 0x692668: EnterFrame
    //     0x692668: stp             fp, lr, [SP, #-0x10]!
    //     0x69266c: mov             fp, SP
    // 0x692670: AllocStack(0x10)
    //     0x692670: sub             SP, SP, #0x10
    // 0x692674: SetupParameters(_HighlightModeManager this /* r1 => r0, fp-0x10 */)
    //     0x692674: mov             x0, x1
    //     0x692678: stur            x1, [fp, #-0x10]
    // 0x69267c: CheckStackOverflow
    //     0x69267c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x692680: cmp             SP, x16
    //     0x692684: b.ls            #0x692714
    // 0x692688: r1 = LoadStaticField(0x69c)
    //     0x692688: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x69268c: ldr             x1, [x1, #0xd38]
    // 0x692690: cmp             w1, NULL
    // 0x692694: b.eq            #0x69271c
    // 0x692698: LoadField: r3 = r1->field_93
    //     0x692698: ldur            w3, [x1, #0x93]
    // 0x69269c: DecompressPointer r3
    //     0x69269c: add             x3, x3, HEAP, lsl #32
    // 0x6926a0: r16 = Sentinel
    //     0x6926a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6926a4: cmp             w3, w16
    // 0x6926a8: b.eq            #0x692720
    // 0x6926ac: mov             x2, x0
    // 0x6926b0: stur            x3, [fp, #-8]
    // 0x6926b4: r1 = Function 'handleKeyMessage':.
    //     0x6926b4: ldr             x1, [PP, #0x2168]  ; [pp+0x2168] AnonymousClosure: (0x692df8), in [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::handleKeyMessage (0x692e34)
    // 0x6926b8: r0 = AllocateClosure()
    //     0x6926b8: bl              #0xec1630  ; AllocateClosureStub
    // 0x6926bc: ldur            x1, [fp, #-8]
    // 0x6926c0: StoreField: r1->field_7 = r0
    //     0x6926c0: stur            w0, [x1, #7]
    //     0x6926c4: ldurb           w16, [x1, #-1]
    //     0x6926c8: ldurb           w17, [x0, #-1]
    //     0x6926cc: and             x16, x17, x16, lsr #2
    //     0x6926d0: tst             x16, HEAP, lsr #32
    //     0x6926d4: b.eq            #0x6926dc
    //     0x6926d8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6926dc: r0 = instance()
    //     0x6926dc: bl              #0x6927f0  ; [package:flutter/src/gestures/binding.dart] GestureBinding::instance
    // 0x6926e0: LoadField: r3 = r0->field_13
    //     0x6926e0: ldur            w3, [x0, #0x13]
    // 0x6926e4: DecompressPointer r3
    //     0x6926e4: add             x3, x3, HEAP, lsl #32
    // 0x6926e8: ldur            x2, [fp, #-0x10]
    // 0x6926ec: stur            x3, [fp, #-8]
    // 0x6926f0: r1 = Function 'handlePointerEvent':.
    //     0x6926f0: ldr             x1, [PP, #0x2170]  ; [pp+0x2170] AnonymousClosure: (0x692810), in [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::handlePointerEvent (0x69284c)
    // 0x6926f4: r0 = AllocateClosure()
    //     0x6926f4: bl              #0xec1630  ; AllocateClosureStub
    // 0x6926f8: ldur            x1, [fp, #-8]
    // 0x6926fc: mov             x2, x0
    // 0x692700: r0 = addGlobalRoute()
    //     0x692700: bl              #0x692728  ; [package:flutter/src/gestures/pointer_router.dart] PointerRouter::addGlobalRoute
    // 0x692704: r0 = Null
    //     0x692704: mov             x0, NULL
    // 0x692708: LeaveFrame
    //     0x692708: mov             SP, fp
    //     0x69270c: ldp             fp, lr, [SP], #0x10
    // 0x692710: ret
    //     0x692710: ret             
    // 0x692714: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x692714: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x692718: b               #0x692688
    // 0x69271c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x69271c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x692720: r9 = _keyEventManager
    //     0x692720: ldr             x9, [PP, #0x2178]  ; [pp+0x2178] Field <_WidgetsFlutterBinding&BindingBase&GestureBinding&SchedulerBinding&ServicesBinding@263399801._keyEventManager@57240726>: late final (offset: 0x94)
    // 0x692724: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x692724: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void handlePointerEvent(dynamic, PointerEvent) {
    // ** addr: 0x692810, size: 0x3c
    // 0x692810: EnterFrame
    //     0x692810: stp             fp, lr, [SP, #-0x10]!
    //     0x692814: mov             fp, SP
    // 0x692818: ldr             x0, [fp, #0x18]
    // 0x69281c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x69281c: ldur            w1, [x0, #0x17]
    // 0x692820: DecompressPointer r1
    //     0x692820: add             x1, x1, HEAP, lsl #32
    // 0x692824: CheckStackOverflow
    //     0x692824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x692828: cmp             SP, x16
    //     0x69282c: b.ls            #0x692844
    // 0x692830: ldr             x2, [fp, #0x10]
    // 0x692834: r0 = handlePointerEvent()
    //     0x692834: bl              #0x69284c  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::handlePointerEvent
    // 0x692838: LeaveFrame
    //     0x692838: mov             SP, fp
    //     0x69283c: ldp             fp, lr, [SP], #0x10
    // 0x692840: ret
    //     0x692840: ret             
    // 0x692844: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x692844: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x692848: b               #0x692830
  }
  _ handlePointerEvent(/* No info */) {
    // ** addr: 0x69284c, size: 0xe0
    // 0x69284c: EnterFrame
    //     0x69284c: stp             fp, lr, [SP, #-0x10]!
    //     0x692850: mov             fp, SP
    // 0x692854: AllocStack(0x10)
    //     0x692854: sub             SP, SP, #0x10
    // 0x692858: SetupParameters(_HighlightModeManager this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0x692858: stur            x1, [fp, #-8]
    //     0x69285c: mov             x16, x2
    //     0x692860: mov             x2, x1
    //     0x692864: mov             x1, x16
    // 0x692868: CheckStackOverflow
    //     0x692868: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x69286c: cmp             SP, x16
    //     0x692870: b.ls            #0x692924
    // 0x692874: r0 = LoadClassIdInstr(r1)
    //     0x692874: ldur            x0, [x1, #-1]
    //     0x692878: ubfx            x0, x0, #0xc, #0x14
    // 0x69287c: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x69287c: movz            x17, #0x30b7
    //     0x692880: movk            x17, #0x1, lsl #16
    //     0x692884: add             lr, x0, x17
    //     0x692888: ldr             lr, [x21, lr, lsl #3]
    //     0x69288c: blr             lr
    // 0x692890: LoadField: r1 = r0->field_7
    //     0x692890: ldur            x1, [x0, #7]
    // 0x692894: cmp             x1, #2
    // 0x692898: b.gt            #0x6928b4
    // 0x69289c: cmp             x1, #1
    // 0x6928a0: b.gt            #0x6928c4
    // 0x6928a4: cmp             x1, #0
    // 0x6928a8: b.le            #0x6928c4
    // 0x6928ac: ldur            x0, [fp, #-8]
    // 0x6928b0: b               #0x6928e4
    // 0x6928b4: cmp             x1, #4
    // 0x6928b8: b.gt            #0x6928e0
    // 0x6928bc: cmp             x1, #3
    // 0x6928c0: b.gt            #0x6928d8
    // 0x6928c4: ldur            x0, [fp, #-8]
    // 0x6928c8: r1 = true
    //     0x6928c8: add             x1, NULL, #0x20  ; true
    // 0x6928cc: StoreField: r0->field_7 = r1
    //     0x6928cc: stur            w1, [x0, #7]
    // 0x6928d0: r2 = Instance_FocusHighlightMode
    //     0x6928d0: ldr             x2, [PP, #0x2180]  ; [pp+0x2180] Obj!FocusHighlightMode@e34561
    // 0x6928d4: b               #0x6928f0
    // 0x6928d8: ldur            x0, [fp, #-8]
    // 0x6928dc: b               #0x6928e4
    // 0x6928e0: ldur            x0, [fp, #-8]
    // 0x6928e4: r1 = false
    //     0x6928e4: add             x1, NULL, #0x30  ; false
    // 0x6928e8: StoreField: r0->field_7 = r1
    //     0x6928e8: stur            w1, [x0, #7]
    // 0x6928ec: r2 = Instance_FocusHighlightMode
    //     0x6928ec: ldr             x2, [PP, #0x2188]  ; [pp+0x2188] Obj!FocusHighlightMode@e34541
    // 0x6928f0: mov             x1, x0
    // 0x6928f4: stur            x2, [fp, #-0x10]
    // 0x6928f8: r0 = highlightMode()
    //     0x6928f8: bl              #0x692dbc  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::highlightMode
    // 0x6928fc: mov             x1, x0
    // 0x692900: ldur            x0, [fp, #-0x10]
    // 0x692904: cmp             w0, w1
    // 0x692908: b.eq            #0x692914
    // 0x69290c: ldur            x1, [fp, #-8]
    // 0x692910: r0 = updateMode()
    //     0x692910: bl              #0x69292c  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::updateMode
    // 0x692914: r0 = Null
    //     0x692914: mov             x0, NULL
    // 0x692918: LeaveFrame
    //     0x692918: mov             SP, fp
    //     0x69291c: ldp             fp, lr, [SP], #0x10
    // 0x692920: ret
    //     0x692920: ret             
    // 0x692924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x692924: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x692928: b               #0x692874
  }
  _ updateMode(/* No info */) {
    // ** addr: 0x69292c, size: 0xbc
    // 0x69292c: EnterFrame
    //     0x69292c: stp             fp, lr, [SP, #-0x10]!
    //     0x692930: mov             fp, SP
    // 0x692934: AllocStack(0x10)
    //     0x692934: sub             SP, SP, #0x10
    // 0x692938: SetupParameters(_HighlightModeManager this /* r1 => r1, fp-0x10 */)
    //     0x692938: stur            x1, [fp, #-0x10]
    // 0x69293c: CheckStackOverflow
    //     0x69293c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x692940: cmp             SP, x16
    //     0x692944: b.ls            #0x6929e0
    // 0x692948: LoadField: r0 = r1->field_7
    //     0x692948: ldur            w0, [x1, #7]
    // 0x69294c: DecompressPointer r0
    //     0x69294c: add             x0, x0, HEAP, lsl #32
    // 0x692950: cmp             w0, NULL
    // 0x692954: b.ne            #0x692968
    // 0x692958: r0 = Null
    //     0x692958: mov             x0, NULL
    // 0x69295c: LeaveFrame
    //     0x69295c: mov             SP, fp
    //     0x692960: ldp             fp, lr, [SP], #0x10
    // 0x692964: ret
    //     0x692964: ret             
    // 0x692968: tbnz            w0, #4, #0x692974
    // 0x69296c: r0 = Instance_FocusHighlightMode
    //     0x69296c: ldr             x0, [PP, #0x2180]  ; [pp+0x2180] Obj!FocusHighlightMode@e34561
    // 0x692970: b               #0x692978
    // 0x692974: r0 = Instance_FocusHighlightMode
    //     0x692974: ldr             x0, [PP, #0x2188]  ; [pp+0x2188] Obj!FocusHighlightMode@e34541
    // 0x692978: stur            x0, [fp, #-8]
    // 0x69297c: LoadField: r2 = r1->field_b
    //     0x69297c: ldur            w2, [x1, #0xb]
    // 0x692980: DecompressPointer r2
    //     0x692980: add             x2, x2, HEAP, lsl #32
    // 0x692984: cmp             w2, NULL
    // 0x692988: b.ne            #0x692998
    // 0x69298c: r0 = _defaultModeForPlatform()
    //     0x69298c: bl              #0x692d48  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::_defaultModeForPlatform
    // 0x692990: mov             x3, x0
    // 0x692994: b               #0x69299c
    // 0x692998: mov             x3, x2
    // 0x69299c: ldur            x1, [fp, #-0x10]
    // 0x6929a0: ldur            x2, [fp, #-8]
    // 0x6929a4: mov             x0, x2
    // 0x6929a8: StoreField: r1->field_b = r0
    //     0x6929a8: stur            w0, [x1, #0xb]
    //     0x6929ac: ldurb           w16, [x1, #-1]
    //     0x6929b0: ldurb           w17, [x0, #-1]
    //     0x6929b4: and             x16, x17, x16, lsr #2
    //     0x6929b8: tst             x16, HEAP, lsr #32
    //     0x6929bc: b.eq            #0x6929c4
    //     0x6929c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6929c4: cmp             w2, w3
    // 0x6929c8: b.eq            #0x6929d0
    // 0x6929cc: r0 = notifyListeners()
    //     0x6929cc: bl              #0x6929e8  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::notifyListeners
    // 0x6929d0: r0 = Null
    //     0x6929d0: mov             x0, NULL
    // 0x6929d4: LeaveFrame
    //     0x6929d4: mov             SP, fp
    //     0x6929d8: ldp             fp, lr, [SP], #0x10
    // 0x6929dc: ret
    //     0x6929dc: ret             
    // 0x6929e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6929e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6929e4: b               #0x692948
  }
  _ notifyListeners(/* No info */) {
    // ** addr: 0x6929e8, size: 0x360
    // 0x6929e8: EnterFrame
    //     0x6929e8: stp             fp, lr, [SP, #-0x10]!
    //     0x6929ec: mov             fp, SP
    // 0x6929f0: AllocStack(0xc0)
    //     0x6929f0: sub             SP, SP, #0xc0
    // 0x6929f4: SetupParameters(_HighlightModeManager this /* r1 => r0, fp-0x78 */)
    //     0x6929f4: mov             x0, x1
    //     0x6929f8: stur            x1, [fp, #-0x78]
    // 0x6929fc: CheckStackOverflow
    //     0x6929fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x692a00: cmp             SP, x16
    //     0x692a04: b.ls            #0x692d30
    // 0x692a08: LoadField: r1 = r0->field_1b
    //     0x692a08: ldur            w1, [x0, #0x1b]
    // 0x692a0c: DecompressPointer r1
    //     0x692a0c: add             x1, x1, HEAP, lsl #32
    // 0x692a10: r0 = isEmpty()
    //     0x692a10: bl              #0x7f05e8  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::isEmpty
    // 0x692a14: tbnz            w0, #4, #0x692a28
    // 0x692a18: r0 = Null
    //     0x692a18: mov             x0, NULL
    // 0x692a1c: LeaveFrame
    //     0x692a1c: mov             SP, fp
    //     0x692a20: ldp             fp, lr, [SP], #0x10
    // 0x692a24: ret
    //     0x692a24: ret             
    // 0x692a28: ldur            x0, [fp, #-0x78]
    // 0x692a2c: LoadField: r2 = r0->field_1b
    //     0x692a2c: ldur            w2, [x0, #0x1b]
    // 0x692a30: DecompressPointer r2
    //     0x692a30: add             x2, x2, HEAP, lsl #32
    // 0x692a34: r1 = <(dynamic this, FocusHighlightMode) => void?>
    //     0x692a34: ldr             x1, [PP, #0x2190]  ; [pp+0x2190] TypeArguments: <(dynamic this, FocusHighlightMode) => void?>
    // 0x692a38: r0 = _GrowableList.of()
    //     0x692a38: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x692a3c: stur            x0, [fp, #-0x80]
    // 0x692a40: LoadField: r1 = r0->field_7
    //     0x692a40: ldur            w1, [x0, #7]
    // 0x692a44: DecompressPointer r1
    //     0x692a44: add             x1, x1, HEAP, lsl #32
    // 0x692a48: r0 = ListIterator()
    //     0x692a48: bl              #0x644848  ; AllocateListIteratorStub -> ListIterator<X0> (size=0x24)
    // 0x692a4c: mov             x1, x0
    // 0x692a50: ldur            x0, [fp, #-0x80]
    // 0x692a54: StoreField: r1->field_b = r0
    //     0x692a54: stur            w0, [x1, #0xb]
    // 0x692a58: LoadField: r2 = r0->field_b
    //     0x692a58: ldur            w2, [x0, #0xb]
    // 0x692a5c: r0 = LoadInt32Instr(r2)
    //     0x692a5c: sbfx            x0, x2, #1, #0x1f
    // 0x692a60: StoreField: r1->field_f = r0
    //     0x692a60: stur            x0, [x1, #0xf]
    // 0x692a64: ArrayStore: r1[0] = rZR  ; List_8
    //     0x692a64: stur            xzr, [x1, #0x17]
    // 0x692a68: ldur            x4, [fp, #-0x78]
    // 0x692a6c: r3 = Null
    //     0x692a6c: mov             x3, NULL
    // 0x692a70: r2 = Null
    //     0x692a70: mov             x2, NULL
    // 0x692a74: b               #0x692b54
    // 0x692a78: sub             SP, fp, #0xc0
    // 0x692a7c: mov             x3, x0
    // 0x692a80: stur            x0, [fp, #-0x78]
    // 0x692a84: mov             x0, x1
    // 0x692a88: stur            x1, [fp, #-0x80]
    // 0x692a8c: r1 = Null
    //     0x692a8c: mov             x1, NULL
    // 0x692a90: r2 = 4
    //     0x692a90: movz            x2, #0x4
    // 0x692a94: r0 = AllocateArray()
    //     0x692a94: bl              #0xec22fc  ; AllocateArrayStub
    // 0x692a98: stur            x0, [fp, #-0x88]
    // 0x692a9c: r16 = "while dispatching notifications for "
    //     0x692a9c: ldr             x16, [PP, #0x2198]  ; [pp+0x2198] "while dispatching notifications for "
    // 0x692aa0: StoreField: r0->field_f = r16
    //     0x692aa0: stur            w16, [x0, #0xf]
    // 0x692aa4: ldur            x16, [fp, #-0x70]
    // 0x692aa8: str             x16, [SP]
    // 0x692aac: r0 = runtimeType()
    //     0x692aac: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0x692ab0: ldur            x1, [fp, #-0x88]
    // 0x692ab4: ArrayStore: r1[1] = r0  ; List_4
    //     0x692ab4: add             x25, x1, #0x13
    //     0x692ab8: str             w0, [x25]
    //     0x692abc: tbz             w0, #0, #0x692ad8
    //     0x692ac0: ldurb           w16, [x1, #-1]
    //     0x692ac4: ldurb           w17, [x0, #-1]
    //     0x692ac8: and             x16, x17, x16, lsr #2
    //     0x692acc: tst             x16, HEAP, lsr #32
    //     0x692ad0: b.eq            #0x692ad8
    //     0x692ad4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x692ad8: ldur            x16, [fp, #-0x88]
    // 0x692adc: str             x16, [SP]
    // 0x692ae0: r0 = _interpolate()
    //     0x692ae0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x692ae4: r1 = <List<Object>>
    //     0x692ae4: ldr             x1, [PP, #0x440]  ; [pp+0x440] TypeArguments: <List<Object>>
    // 0x692ae8: stur            x0, [fp, #-0x88]
    // 0x692aec: r0 = ErrorDescription()
    //     0x692aec: bl              #0x64483c  ; AllocateErrorDescriptionStub -> ErrorDescription (size=0x2c)
    // 0x692af0: mov             x1, x0
    // 0x692af4: ldur            x2, [fp, #-0x88]
    // 0x692af8: r3 = Instance_DiagnosticLevel
    //     0x692af8: ldr             x3, [PP, #0x450]  ; [pp+0x450] Obj!DiagnosticLevel@e37021
    // 0x692afc: stur            x0, [fp, #-0x88]
    // 0x692b00: r0 = _ErrorDiagnostic()
    //     0x692b00: bl              #0x644784  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::_ErrorDiagnostic
    // 0x692b04: r0 = FlutterErrorDetails()
    //     0x692b04: bl              #0x644b40  ; AllocateFlutterErrorDetailsStub -> FlutterErrorDetails (size=0x1c)
    // 0x692b08: mov             x1, x0
    // 0x692b0c: ldur            x0, [fp, #-0x78]
    // 0x692b10: StoreField: r1->field_7 = r0
    //     0x692b10: stur            w0, [x1, #7]
    // 0x692b14: ldur            x2, [fp, #-0x80]
    // 0x692b18: StoreField: r1->field_b = r2
    //     0x692b18: stur            w2, [x1, #0xb]
    // 0x692b1c: ldur            x3, [fp, #-0x88]
    // 0x692b20: StoreField: r1->field_f = r3
    //     0x692b20: stur            w3, [x1, #0xf]
    // 0x692b24: r3 = false
    //     0x692b24: add             x3, NULL, #0x30  ; false
    // 0x692b28: ArrayStore: r1[0] = r3  ; List_4
    //     0x692b28: stur            w3, [x1, #0x17]
    // 0x692b2c: r0 = reportError()
    //     0x692b2c: bl              #0x63f6cc  ; [package:flutter/src/foundation/assertions.dart] FlutterError::reportError
    // 0x692b30: ldur            x1, [fp, #-0x70]
    // 0x692b34: ldur            x0, [fp, #-0x38]
    // 0x692b38: mov             x3, x1
    // 0x692b3c: ldur            x2, [fp, #-0x78]
    // 0x692b40: ldur            x1, [fp, #-0x80]
    // 0x692b44: mov             x4, x3
    // 0x692b48: mov             x3, x2
    // 0x692b4c: mov             x2, x1
    // 0x692b50: mov             x1, x0
    // 0x692b54: stur            x4, [fp, #-0x80]
    // 0x692b58: stur            x3, [fp, #-0x88]
    // 0x692b5c: stur            x2, [fp, #-0x90]
    // 0x692b60: stur            x1, [fp, #-0x98]
    // 0x692b64: CheckStackOverflow
    //     0x692b64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x692b68: cmp             SP, x16
    //     0x692b6c: b.ls            #0x692d38
    // 0x692b70: LoadField: r5 = r1->field_b
    //     0x692b70: ldur            w5, [x1, #0xb]
    // 0x692b74: DecompressPointer r5
    //     0x692b74: add             x5, x5, HEAP, lsl #32
    // 0x692b78: stur            x5, [fp, #-0x78]
    // 0x692b7c: r0 = LoadClassIdInstr(r5)
    //     0x692b7c: ldur            x0, [x5, #-1]
    //     0x692b80: ubfx            x0, x0, #0xc, #0x14
    // 0x692b84: str             x5, [SP]
    // 0x692b88: r0 = GDT[cid_x0 + 0xc834]()
    //     0x692b88: movz            x17, #0xc834
    //     0x692b8c: add             lr, x0, x17
    //     0x692b90: ldr             lr, [x21, lr, lsl #3]
    //     0x692b94: blr             lr
    // 0x692b98: ldur            x3, [fp, #-0x98]
    // 0x692b9c: LoadField: r1 = r3->field_f
    //     0x692b9c: ldur            x1, [x3, #0xf]
    // 0x692ba0: r2 = LoadInt32Instr(r0)
    //     0x692ba0: sbfx            x2, x0, #1, #0x1f
    //     0x692ba4: tbz             w0, #0, #0x692bac
    //     0x692ba8: ldur            x2, [x0, #7]
    // 0x692bac: cmp             x1, x2
    // 0x692bb0: b.ne            #0x692d10
    // 0x692bb4: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x692bb4: ldur            x0, [x3, #0x17]
    // 0x692bb8: cmp             x0, x2
    // 0x692bbc: b.lt            #0x692bd4
    // 0x692bc0: StoreField: r3->field_1f = rNULL
    //     0x692bc0: stur            NULL, [x3, #0x1f]
    // 0x692bc4: r0 = Null
    //     0x692bc4: mov             x0, NULL
    // 0x692bc8: LeaveFrame
    //     0x692bc8: mov             SP, fp
    //     0x692bcc: ldp             fp, lr, [SP], #0x10
    // 0x692bd0: ret
    //     0x692bd0: ret             
    // 0x692bd4: ldur            x1, [fp, #-0x78]
    // 0x692bd8: r2 = LoadClassIdInstr(r1)
    //     0x692bd8: ldur            x2, [x1, #-1]
    //     0x692bdc: ubfx            x2, x2, #0xc, #0x14
    // 0x692be0: mov             x16, x0
    // 0x692be4: mov             x0, x2
    // 0x692be8: mov             x2, x16
    // 0x692bec: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x692bec: movz            x17, #0xd28f
    //     0x692bf0: add             lr, x0, x17
    //     0x692bf4: ldr             lr, [x21, lr, lsl #3]
    //     0x692bf8: blr             lr
    // 0x692bfc: mov             x4, x0
    // 0x692c00: ldur            x3, [fp, #-0x98]
    // 0x692c04: stur            x4, [fp, #-0xa0]
    // 0x692c08: StoreField: r3->field_1f = r0
    //     0x692c08: stur            w0, [x3, #0x1f]
    //     0x692c0c: tbz             w0, #0, #0x692c28
    //     0x692c10: ldurb           w16, [x3, #-1]
    //     0x692c14: ldurb           w17, [x0, #-1]
    //     0x692c18: and             x16, x17, x16, lsr #2
    //     0x692c1c: tst             x16, HEAP, lsr #32
    //     0x692c20: b.eq            #0x692c28
    //     0x692c24: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x692c28: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x692c28: ldur            x0, [x3, #0x17]
    // 0x692c2c: add             x1, x0, #1
    // 0x692c30: ArrayStore: r3[0] = r1  ; List_8
    //     0x692c30: stur            x1, [x3, #0x17]
    // 0x692c34: cmp             w4, NULL
    // 0x692c38: b.ne            #0x692c6c
    // 0x692c3c: LoadField: r2 = r3->field_7
    //     0x692c3c: ldur            w2, [x3, #7]
    // 0x692c40: DecompressPointer r2
    //     0x692c40: add             x2, x2, HEAP, lsl #32
    // 0x692c44: mov             x0, x4
    // 0x692c48: r1 = Null
    //     0x692c48: mov             x1, NULL
    // 0x692c4c: cmp             w2, NULL
    // 0x692c50: b.eq            #0x692c6c
    // 0x692c54: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x692c54: ldur            w4, [x2, #0x17]
    // 0x692c58: DecompressPointer r4
    //     0x692c58: add             x4, x4, HEAP, lsl #32
    // 0x692c5c: r8 = X0
    //     0x692c5c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x692c60: LoadField: r9 = r4->field_7
    //     0x692c60: ldur            x9, [x4, #7]
    // 0x692c64: r3 = Null
    //     0x692c64: ldr             x3, [PP, #0x21a0]  ; [pp+0x21a0] Null
    // 0x692c68: blr             x9
    // 0x692c6c: ldur            x3, [fp, #-0x80]
    // 0x692c70: LoadField: r1 = r3->field_1b
    //     0x692c70: ldur            w1, [x3, #0x1b]
    // 0x692c74: DecompressPointer r1
    //     0x692c74: add             x1, x1, HEAP, lsl #32
    // 0x692c78: ldur            x2, [fp, #-0xa0]
    // 0x692c7c: r0 = contains()
    //     0x692c7c: bl              #0x7adb0c  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::contains
    // 0x692c80: tbnz            w0, #4, #0x692cfc
    // 0x692c84: ldur            x3, [fp, #-0x80]
    // 0x692c88: LoadField: r0 = r3->field_b
    //     0x692c88: ldur            w0, [x3, #0xb]
    // 0x692c8c: DecompressPointer r0
    //     0x692c8c: add             x0, x0, HEAP, lsl #32
    // 0x692c90: stur            x0, [fp, #-0xb0]
    // 0x692c94: cmp             w0, NULL
    // 0x692c98: b.ne            #0x692ce0
    // 0x692c9c: r1 = LoadStaticField(0x7d4)
    //     0x692c9c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x692ca0: ldr             x1, [x1, #0xfa8]
    // 0x692ca4: cmp             w1, NULL
    // 0x692ca8: b.eq            #0x692d40
    // 0x692cac: LoadField: r2 = r1->field_cb
    //     0x692cac: ldur            w2, [x1, #0xcb]
    // 0x692cb0: DecompressPointer r2
    //     0x692cb0: add             x2, x2, HEAP, lsl #32
    // 0x692cb4: stur            x2, [fp, #-0xa8]
    // 0x692cb8: cmp             w2, NULL
    // 0x692cbc: b.eq            #0x692d44
    // 0x692cc0: LoadField: r1 = r2->field_2b
    //     0x692cc0: ldur            w1, [x2, #0x2b]
    // 0x692cc4: DecompressPointer r1
    //     0x692cc4: add             x1, x1, HEAP, lsl #32
    // 0x692cc8: r0 = isNotEmpty()
    //     0x692cc8: bl              #0x766fa4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::isNotEmpty
    // 0x692ccc: tbnz            w0, #4, #0x692cd8
    // 0x692cd0: r0 = Instance_FocusHighlightMode
    //     0x692cd0: ldr             x0, [PP, #0x2188]  ; [pp+0x2188] Obj!FocusHighlightMode@e34541
    // 0x692cd4: b               #0x692ce4
    // 0x692cd8: r0 = Instance_FocusHighlightMode
    //     0x692cd8: ldr             x0, [PP, #0x2180]  ; [pp+0x2180] Obj!FocusHighlightMode@e34561
    // 0x692cdc: b               #0x692ce4
    // 0x692ce0: ldur            x0, [fp, #-0xb0]
    // 0x692ce4: ldur            x16, [fp, #-0xa0]
    // 0x692ce8: stp             x0, x16, [SP]
    // 0x692cec: ldur            x0, [fp, #-0xa0]
    // 0x692cf0: ClosureCall
    //     0x692cf0: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x692cf4: ldur            x2, [x0, #0x1f]
    //     0x692cf8: blr             x2
    // 0x692cfc: ldur            x3, [fp, #-0x80]
    // 0x692d00: ldur            x2, [fp, #-0x88]
    // 0x692d04: ldur            x1, [fp, #-0x90]
    // 0x692d08: ldur            x0, [fp, #-0x98]
    // 0x692d0c: b               #0x692b44
    // 0x692d10: ldur            x1, [fp, #-0x78]
    // 0x692d14: r0 = ConcurrentModificationError()
    //     0x692d14: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x692d18: mov             x1, x0
    // 0x692d1c: ldur            x0, [fp, #-0x78]
    // 0x692d20: StoreField: r1->field_b = r0
    //     0x692d20: stur            w0, [x1, #0xb]
    // 0x692d24: mov             x0, x1
    // 0x692d28: r0 = Throw()
    //     0x692d28: bl              #0xec04b8  ; ThrowStub
    // 0x692d2c: brk             #0
    // 0x692d30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x692d30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x692d34: b               #0x692a08
    // 0x692d38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x692d38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x692d3c: b               #0x692b70
    // 0x692d40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x692d40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x692d44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x692d44: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _defaultModeForPlatform(/* No info */) {
    // ** addr: 0x692d48, size: 0x74
    // 0x692d48: EnterFrame
    //     0x692d48: stp             fp, lr, [SP, #-0x10]!
    //     0x692d4c: mov             fp, SP
    // 0x692d50: r1 = LoadStaticField(0x7d4)
    //     0x692d50: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x692d54: ldr             x1, [x1, #0xfa8]
    // 0x692d58: cmp             w1, NULL
    // 0x692d5c: b.eq            #0x692db4
    // 0x692d60: LoadField: r2 = r1->field_cb
    //     0x692d60: ldur            w2, [x1, #0xcb]
    // 0x692d64: DecompressPointer r2
    //     0x692d64: add             x2, x2, HEAP, lsl #32
    // 0x692d68: cmp             w2, NULL
    // 0x692d6c: b.eq            #0x692db8
    // 0x692d70: LoadField: r1 = r2->field_2b
    //     0x692d70: ldur            w1, [x2, #0x2b]
    // 0x692d74: DecompressPointer r1
    //     0x692d74: add             x1, x1, HEAP, lsl #32
    // 0x692d78: LoadField: r2 = r1->field_13
    //     0x692d78: ldur            w2, [x1, #0x13]
    // 0x692d7c: r3 = LoadInt32Instr(r2)
    //     0x692d7c: sbfx            x3, x2, #1, #0x1f
    // 0x692d80: asr             x2, x3, #1
    // 0x692d84: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x692d84: ldur            w3, [x1, #0x17]
    // 0x692d88: r1 = LoadInt32Instr(r3)
    //     0x692d88: sbfx            x1, x3, #1, #0x1f
    // 0x692d8c: sub             x3, x2, x1
    // 0x692d90: cbz             x3, #0x692da4
    // 0x692d94: r0 = Instance_FocusHighlightMode
    //     0x692d94: ldr             x0, [PP, #0x2188]  ; [pp+0x2188] Obj!FocusHighlightMode@e34541
    // 0x692d98: LeaveFrame
    //     0x692d98: mov             SP, fp
    //     0x692d9c: ldp             fp, lr, [SP], #0x10
    // 0x692da0: ret
    //     0x692da0: ret             
    // 0x692da4: r0 = Instance_FocusHighlightMode
    //     0x692da4: ldr             x0, [PP, #0x2180]  ; [pp+0x2180] Obj!FocusHighlightMode@e34561
    // 0x692da8: LeaveFrame
    //     0x692da8: mov             SP, fp
    //     0x692dac: ldp             fp, lr, [SP], #0x10
    // 0x692db0: ret
    //     0x692db0: ret             
    // 0x692db4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x692db4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x692db8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x692db8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ highlightMode(/* No info */) {
    // ** addr: 0x692dbc, size: 0x3c
    // 0x692dbc: EnterFrame
    //     0x692dbc: stp             fp, lr, [SP, #-0x10]!
    //     0x692dc0: mov             fp, SP
    // 0x692dc4: CheckStackOverflow
    //     0x692dc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x692dc8: cmp             SP, x16
    //     0x692dcc: b.ls            #0x692df0
    // 0x692dd0: LoadField: r0 = r1->field_b
    //     0x692dd0: ldur            w0, [x1, #0xb]
    // 0x692dd4: DecompressPointer r0
    //     0x692dd4: add             x0, x0, HEAP, lsl #32
    // 0x692dd8: cmp             w0, NULL
    // 0x692ddc: b.ne            #0x692de4
    // 0x692de0: r0 = _defaultModeForPlatform()
    //     0x692de0: bl              #0x692d48  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::_defaultModeForPlatform
    // 0x692de4: LeaveFrame
    //     0x692de4: mov             SP, fp
    //     0x692de8: ldp             fp, lr, [SP], #0x10
    // 0x692dec: ret
    //     0x692dec: ret             
    // 0x692df0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x692df0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x692df4: b               #0x692dd0
  }
  [closure] bool handleKeyMessage(dynamic, KeyMessage) {
    // ** addr: 0x692df8, size: 0x3c
    // 0x692df8: EnterFrame
    //     0x692df8: stp             fp, lr, [SP, #-0x10]!
    //     0x692dfc: mov             fp, SP
    // 0x692e00: ldr             x0, [fp, #0x18]
    // 0x692e04: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x692e04: ldur            w1, [x0, #0x17]
    // 0x692e08: DecompressPointer r1
    //     0x692e08: add             x1, x1, HEAP, lsl #32
    // 0x692e0c: CheckStackOverflow
    //     0x692e0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x692e10: cmp             SP, x16
    //     0x692e14: b.ls            #0x692e2c
    // 0x692e18: ldr             x2, [fp, #0x10]
    // 0x692e1c: r0 = handleKeyMessage()
    //     0x692e1c: bl              #0x692e34  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::handleKeyMessage
    // 0x692e20: LeaveFrame
    //     0x692e20: mov             SP, fp
    //     0x692e24: ldp             fp, lr, [SP], #0x10
    // 0x692e28: ret
    //     0x692e28: ret             
    // 0x692e2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x692e2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x692e30: b               #0x692e18
  }
  _ handleKeyMessage(/* No info */) {
    // ** addr: 0x692e34, size: 0x9d0
    // 0x692e34: EnterFrame
    //     0x692e34: stp             fp, lr, [SP, #-0x10]!
    //     0x692e38: mov             fp, SP
    // 0x692e3c: AllocStack(0x90)
    //     0x692e3c: sub             SP, SP, #0x90
    // 0x692e40: r0 = false
    //     0x692e40: add             x0, NULL, #0x30  ; false
    // 0x692e44: mov             x3, x1
    // 0x692e48: stur            x1, [fp, #-8]
    // 0x692e4c: stur            x2, [fp, #-0x10]
    // 0x692e50: CheckStackOverflow
    //     0x692e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x692e54: cmp             SP, x16
    //     0x692e58: b.ls            #0x6937b8
    // 0x692e5c: StoreField: r3->field_7 = r0
    //     0x692e5c: stur            w0, [x3, #7]
    // 0x692e60: mov             x1, x3
    // 0x692e64: r0 = updateMode()
    //     0x692e64: bl              #0x69292c  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::updateMode
    // 0x692e68: r0 = instance()
    //     0x692e68: bl              #0x693910  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::instance
    // 0x692e6c: mov             x1, x0
    // 0x692e70: r0 = image()
    //     0x692e70: bl              #0xcd8290  ; [package:image/src/image/pixel_uint4.dart] PixelUint4::image
    // 0x692e74: cmp             w0, NULL
    // 0x692e78: b.ne            #0x692e8c
    // 0x692e7c: r0 = false
    //     0x692e7c: add             x0, NULL, #0x30  ; false
    // 0x692e80: LeaveFrame
    //     0x692e80: mov             SP, fp
    //     0x692e84: ldp             fp, lr, [SP], #0x10
    // 0x692e88: ret
    //     0x692e88: ret             
    // 0x692e8c: ldur            x0, [fp, #-8]
    // 0x692e90: LoadField: r2 = r0->field_13
    //     0x692e90: ldur            w2, [x0, #0x13]
    // 0x692e94: DecompressPointer r2
    //     0x692e94: add             x2, x2, HEAP, lsl #32
    // 0x692e98: mov             x1, x2
    // 0x692e9c: stur            x2, [fp, #-0x18]
    // 0x692ea0: r0 = isNotEmpty()
    //     0x692ea0: bl              #0x874120  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::isNotEmpty
    // 0x692ea4: tbnz            w0, #4, #0x6930d8
    // 0x692ea8: ldur            x0, [fp, #-0x10]
    // 0x692eac: r1 = <KeyEventResult>
    //     0x692eac: ldr             x1, [PP, #0x21d8]  ; [pp+0x21d8] TypeArguments: <KeyEventResult>
    // 0x692eb0: r2 = 0
    //     0x692eb0: movz            x2, #0
    // 0x692eb4: r0 = _GrowableList()
    //     0x692eb4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x692eb8: ldur            x1, [fp, #-0x18]
    // 0x692ebc: stur            x0, [fp, #-0x18]
    // 0x692ec0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x692ec0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x692ec4: r0 = toList()
    //     0x692ec4: bl              #0xa532a8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::toList
    // 0x692ec8: mov             x3, x0
    // 0x692ecc: stur            x3, [fp, #-0x48]
    // 0x692ed0: LoadField: r4 = r3->field_7
    //     0x692ed0: ldur            w4, [x3, #7]
    // 0x692ed4: DecompressPointer r4
    //     0x692ed4: add             x4, x4, HEAP, lsl #32
    // 0x692ed8: stur            x4, [fp, #-0x40]
    // 0x692edc: LoadField: r0 = r3->field_b
    //     0x692edc: ldur            w0, [x3, #0xb]
    // 0x692ee0: r5 = LoadInt32Instr(r0)
    //     0x692ee0: sbfx            x5, x0, #1, #0x1f
    // 0x692ee4: ldur            x6, [fp, #-0x10]
    // 0x692ee8: stur            x5, [fp, #-0x38]
    // 0x692eec: LoadField: r7 = r6->field_7
    //     0x692eec: ldur            w7, [x6, #7]
    // 0x692ef0: DecompressPointer r7
    //     0x692ef0: add             x7, x7, HEAP, lsl #32
    // 0x692ef4: stur            x7, [fp, #-0x30]
    // 0x692ef8: ldur            x8, [fp, #-0x18]
    // 0x692efc: r0 = 0
    //     0x692efc: movz            x0, #0
    // 0x692f00: CheckStackOverflow
    //     0x692f00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x692f04: cmp             SP, x16
    //     0x692f08: b.ls            #0x6937c0
    // 0x692f0c: LoadField: r1 = r3->field_b
    //     0x692f0c: ldur            w1, [x3, #0xb]
    // 0x692f10: r2 = LoadInt32Instr(r1)
    //     0x692f10: sbfx            x2, x1, #1, #0x1f
    // 0x692f14: cmp             x5, x2
    // 0x692f18: b.ne            #0x693718
    // 0x692f1c: cmp             x0, x2
    // 0x692f20: b.ge            #0x6930a0
    // 0x692f24: LoadField: r1 = r3->field_f
    //     0x692f24: ldur            w1, [x3, #0xf]
    // 0x692f28: DecompressPointer r1
    //     0x692f28: add             x1, x1, HEAP, lsl #32
    // 0x692f2c: ArrayLoad: r9 = r1[r0]  ; Unknown_4
    //     0x692f2c: add             x16, x1, x0, lsl #2
    //     0x692f30: ldur            w9, [x16, #0xf]
    // 0x692f34: DecompressPointer r9
    //     0x692f34: add             x9, x9, HEAP, lsl #32
    // 0x692f38: stur            x9, [fp, #-0x28]
    // 0x692f3c: add             x10, x0, #1
    // 0x692f40: stur            x10, [fp, #-0x20]
    // 0x692f44: cmp             w9, NULL
    // 0x692f48: b.ne            #0x692f78
    // 0x692f4c: mov             x0, x9
    // 0x692f50: mov             x2, x4
    // 0x692f54: r1 = Null
    //     0x692f54: mov             x1, NULL
    // 0x692f58: cmp             w2, NULL
    // 0x692f5c: b.eq            #0x692f78
    // 0x692f60: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x692f60: ldur            w4, [x2, #0x17]
    // 0x692f64: DecompressPointer r4
    //     0x692f64: add             x4, x4, HEAP, lsl #32
    // 0x692f68: r8 = X0
    //     0x692f68: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x692f6c: LoadField: r9 = r4->field_7
    //     0x692f6c: ldur            x9, [x4, #7]
    // 0x692f70: r3 = Null
    //     0x692f70: ldr             x3, [PP, #0x21e0]  ; [pp+0x21e0] Null
    // 0x692f74: blr             x9
    // 0x692f78: ldur            x1, [fp, #-0x30]
    // 0x692f7c: LoadField: r0 = r1->field_b
    //     0x692f7c: ldur            w0, [x1, #0xb]
    // 0x692f80: r2 = LoadInt32Instr(r0)
    //     0x692f80: sbfx            x2, x0, #1, #0x1f
    // 0x692f84: stur            x2, [fp, #-0x58]
    // 0x692f88: ldur            x3, [fp, #-0x18]
    // 0x692f8c: r0 = 0
    //     0x692f8c: movz            x0, #0
    // 0x692f90: CheckStackOverflow
    //     0x692f90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x692f94: cmp             SP, x16
    //     0x692f98: b.ls            #0x6937c8
    // 0x692f9c: LoadField: r4 = r1->field_b
    //     0x692f9c: ldur            w4, [x1, #0xb]
    // 0x692fa0: r5 = LoadInt32Instr(r4)
    //     0x692fa0: sbfx            x5, x4, #1, #0x1f
    // 0x692fa4: cmp             x2, x5
    // 0x692fa8: b.ne            #0x6936f8
    // 0x692fac: cmp             x0, x5
    // 0x692fb0: b.ge            #0x69307c
    // 0x692fb4: LoadField: r4 = r1->field_f
    //     0x692fb4: ldur            w4, [x1, #0xf]
    // 0x692fb8: DecompressPointer r4
    //     0x692fb8: add             x4, x4, HEAP, lsl #32
    // 0x692fbc: ArrayLoad: r5 = r4[r0]  ; Unknown_4
    //     0x692fbc: add             x16, x4, x0, lsl #2
    //     0x692fc0: ldur            w5, [x16, #0xf]
    // 0x692fc4: DecompressPointer r5
    //     0x692fc4: add             x5, x5, HEAP, lsl #32
    // 0x692fc8: add             x4, x0, #1
    // 0x692fcc: stur            x4, [fp, #-0x50]
    // 0x692fd0: ldur            x16, [fp, #-0x28]
    // 0x692fd4: stp             x5, x16, [SP]
    // 0x692fd8: ldur            x0, [fp, #-0x28]
    // 0x692fdc: ClosureCall
    //     0x692fdc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x692fe0: ldur            x2, [x0, #0x1f]
    //     0x692fe4: blr             x2
    // 0x692fe8: mov             x2, x0
    // 0x692fec: ldur            x0, [fp, #-0x18]
    // 0x692ff0: stur            x2, [fp, #-0x68]
    // 0x692ff4: LoadField: r1 = r0->field_b
    //     0x692ff4: ldur            w1, [x0, #0xb]
    // 0x692ff8: LoadField: r3 = r0->field_f
    //     0x692ff8: ldur            w3, [x0, #0xf]
    // 0x692ffc: DecompressPointer r3
    //     0x692ffc: add             x3, x3, HEAP, lsl #32
    // 0x693000: LoadField: r4 = r3->field_b
    //     0x693000: ldur            w4, [x3, #0xb]
    // 0x693004: r3 = LoadInt32Instr(r1)
    //     0x693004: sbfx            x3, x1, #1, #0x1f
    // 0x693008: stur            x3, [fp, #-0x60]
    // 0x69300c: r1 = LoadInt32Instr(r4)
    //     0x69300c: sbfx            x1, x4, #1, #0x1f
    // 0x693010: cmp             x3, x1
    // 0x693014: b.ne            #0x693020
    // 0x693018: mov             x1, x0
    // 0x69301c: r0 = _growToNextCapacity()
    //     0x69301c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x693020: ldur            x2, [fp, #-0x18]
    // 0x693024: ldur            x3, [fp, #-0x60]
    // 0x693028: add             x0, x3, #1
    // 0x69302c: lsl             x1, x0, #1
    // 0x693030: StoreField: r2->field_b = r1
    //     0x693030: stur            w1, [x2, #0xb]
    // 0x693034: LoadField: r1 = r2->field_f
    //     0x693034: ldur            w1, [x2, #0xf]
    // 0x693038: DecompressPointer r1
    //     0x693038: add             x1, x1, HEAP, lsl #32
    // 0x69303c: ldur            x0, [fp, #-0x68]
    // 0x693040: ArrayStore: r1[r3] = r0  ; List_4
    //     0x693040: add             x25, x1, x3, lsl #2
    //     0x693044: add             x25, x25, #0xf
    //     0x693048: str             w0, [x25]
    //     0x69304c: tbz             w0, #0, #0x693068
    //     0x693050: ldurb           w16, [x1, #-1]
    //     0x693054: ldurb           w17, [x0, #-1]
    //     0x693058: and             x16, x17, x16, lsr #2
    //     0x69305c: tst             x16, HEAP, lsr #32
    //     0x693060: b.eq            #0x693068
    //     0x693064: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x693068: ldur            x0, [fp, #-0x50]
    // 0x69306c: mov             x3, x2
    // 0x693070: ldur            x1, [fp, #-0x30]
    // 0x693074: ldur            x2, [fp, #-0x58]
    // 0x693078: b               #0x692f90
    // 0x69307c: mov             x2, x3
    // 0x693080: ldur            x0, [fp, #-0x20]
    // 0x693084: ldur            x6, [fp, #-0x10]
    // 0x693088: mov             x8, x2
    // 0x69308c: ldur            x3, [fp, #-0x48]
    // 0x693090: ldur            x7, [fp, #-0x30]
    // 0x693094: ldur            x4, [fp, #-0x40]
    // 0x693098: ldur            x5, [fp, #-0x38]
    // 0x69309c: b               #0x692f00
    // 0x6930a0: mov             x2, x8
    // 0x6930a4: mov             x1, x2
    // 0x6930a8: r0 = combineKeyEventResults()
    //     0x6930a8: bl              #0x693804  ; [package:flutter/src/widgets/focus_manager.dart] ::combineKeyEventResults
    // 0x6930ac: LoadField: r1 = r0->field_7
    //     0x6930ac: ldur            x1, [x0, #7]
    // 0x6930b0: cmp             x1, #1
    // 0x6930b4: b.gt            #0x6930d0
    // 0x6930b8: cmp             x1, #0
    // 0x6930bc: b.gt            #0x6930c8
    // 0x6930c0: r0 = true
    //     0x6930c0: add             x0, NULL, #0x20  ; true
    // 0x6930c4: b               #0x6930dc
    // 0x6930c8: r0 = false
    //     0x6930c8: add             x0, NULL, #0x30  ; false
    // 0x6930cc: b               #0x6930dc
    // 0x6930d0: r0 = false
    //     0x6930d0: add             x0, NULL, #0x30  ; false
    // 0x6930d4: b               #0x6930dc
    // 0x6930d8: r0 = false
    //     0x6930d8: add             x0, NULL, #0x30  ; false
    // 0x6930dc: stur            x0, [fp, #-0x18]
    // 0x6930e0: tbnz            w0, #4, #0x6930f4
    // 0x6930e4: r0 = true
    //     0x6930e4: add             x0, NULL, #0x20  ; true
    // 0x6930e8: LeaveFrame
    //     0x6930e8: mov             SP, fp
    //     0x6930ec: ldp             fp, lr, [SP], #0x10
    // 0x6930f0: ret
    //     0x6930f0: ret             
    // 0x6930f4: ldur            x1, [fp, #-0x10]
    // 0x6930f8: r0 = instance()
    //     0x6930f8: bl              #0x693910  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::instance
    // 0x6930fc: mov             x1, x0
    // 0x693100: r0 = image()
    //     0x693100: bl              #0xcd8290  ; [package:image/src/image/pixel_uint4.dart] PixelUint4::image
    // 0x693104: stur            x0, [fp, #-0x28]
    // 0x693108: cmp             w0, NULL
    // 0x69310c: b.eq            #0x6937d0
    // 0x693110: r1 = Null
    //     0x693110: mov             x1, NULL
    // 0x693114: r2 = 2
    //     0x693114: movz            x2, #0x2
    // 0x693118: r0 = AllocateArray()
    //     0x693118: bl              #0xec22fc  ; AllocateArrayStub
    // 0x69311c: mov             x2, x0
    // 0x693120: ldur            x0, [fp, #-0x28]
    // 0x693124: stur            x2, [fp, #-0x40]
    // 0x693128: StoreField: r2->field_f = r0
    //     0x693128: stur            w0, [x2, #0xf]
    // 0x69312c: r1 = <FocusNode>
    //     0x69312c: ldr             x1, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x693130: r0 = AllocateGrowableArray()
    //     0x693130: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x693134: mov             x1, x0
    // 0x693138: ldur            x0, [fp, #-0x40]
    // 0x69313c: stur            x1, [fp, #-0x28]
    // 0x693140: StoreField: r1->field_f = r0
    //     0x693140: stur            w0, [x1, #0xf]
    // 0x693144: r0 = 2
    //     0x693144: movz            x0, #0x2
    // 0x693148: StoreField: r1->field_b = r0
    //     0x693148: stur            w0, [x1, #0xb]
    // 0x69314c: r0 = instance()
    //     0x69314c: bl              #0x693910  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::instance
    // 0x693150: mov             x1, x0
    // 0x693154: r0 = image()
    //     0x693154: bl              #0xcd8290  ; [package:image/src/image/pixel_uint4.dart] PixelUint4::image
    // 0x693158: cmp             w0, NULL
    // 0x69315c: b.eq            #0x6937d4
    // 0x693160: mov             x1, x0
    // 0x693164: r0 = ancestors()
    //     0x693164: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x693168: ldur            x1, [fp, #-0x28]
    // 0x69316c: mov             x2, x0
    // 0x693170: r0 = addAll()
    //     0x693170: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x693174: ldur            x3, [fp, #-0x28]
    // 0x693178: LoadField: r0 = r3->field_b
    //     0x693178: ldur            w0, [x3, #0xb]
    // 0x69317c: r4 = LoadInt32Instr(r0)
    //     0x69317c: sbfx            x4, x0, #1, #0x1f
    // 0x693180: ldur            x0, [fp, #-0x10]
    // 0x693184: stur            x4, [fp, #-0x38]
    // 0x693188: LoadField: r5 = r0->field_7
    //     0x693188: ldur            w5, [x0, #7]
    // 0x69318c: DecompressPointer r5
    //     0x69318c: add             x5, x5, HEAP, lsl #32
    // 0x693190: stur            x5, [fp, #-0x40]
    // 0x693194: r0 = 0
    //     0x693194: movz            x0, #0
    // 0x693198: CheckStackOverflow
    //     0x693198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x69319c: cmp             SP, x16
    //     0x6931a0: b.ls            #0x6937d8
    // 0x6931a4: LoadField: r1 = r3->field_b
    //     0x6931a4: ldur            w1, [x3, #0xb]
    // 0x6931a8: r2 = LoadInt32Instr(r1)
    //     0x6931a8: sbfx            x2, x1, #1, #0x1f
    // 0x6931ac: cmp             x4, x2
    // 0x6931b0: b.ne            #0x693798
    // 0x6931b4: cmp             x0, x2
    // 0x6931b8: b.ge            #0x6934a8
    // 0x6931bc: LoadField: r1 = r3->field_f
    //     0x6931bc: ldur            w1, [x3, #0xf]
    // 0x6931c0: DecompressPointer r1
    //     0x6931c0: add             x1, x1, HEAP, lsl #32
    // 0x6931c4: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0x6931c4: add             x16, x1, x0, lsl #2
    //     0x6931c8: ldur            w6, [x16, #0xf]
    // 0x6931cc: DecompressPointer r6
    //     0x6931cc: add             x6, x6, HEAP, lsl #32
    // 0x6931d0: stur            x6, [fp, #-0x10]
    // 0x6931d4: add             x7, x0, #1
    // 0x6931d8: stur            x7, [fp, #-0x20]
    // 0x6931dc: cmp             w6, NULL
    // 0x6931e0: b.ne            #0x693218
    // 0x6931e4: mov             x0, x6
    // 0x6931e8: r2 = Null
    //     0x6931e8: mov             x2, NULL
    // 0x6931ec: r1 = Null
    //     0x6931ec: mov             x1, NULL
    // 0x6931f0: r4 = 60
    //     0x6931f0: movz            x4, #0x3c
    // 0x6931f4: branchIfSmi(r0, 0x693200)
    //     0x6931f4: tbz             w0, #0, #0x693200
    // 0x6931f8: r4 = LoadClassIdInstr(r0)
    //     0x6931f8: ldur            x4, [x0, #-1]
    //     0x6931fc: ubfx            x4, x4, #0xc, #0x14
    // 0x693200: sub             x4, x4, #0xb67
    // 0x693204: cmp             x4, #2
    // 0x693208: b.ls            #0x693218
    // 0x69320c: r8 = FocusNode
    //     0x69320c: ldr             x8, [PP, #0x21f8]  ; [pp+0x21f8] Type: FocusNode
    // 0x693210: r3 = Null
    //     0x693210: ldr             x3, [PP, #0x2200]  ; [pp+0x2200] Null
    // 0x693214: r0 = FocusNode()
    //     0x693214: bl              #0x650d18  ; IsType_FocusNode_Stub
    // 0x693218: ldur            x0, [fp, #-0x10]
    // 0x69321c: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x69321c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x693220: ldr             x0, [x0]
    //     0x693224: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x693228: cmp             w0, w16
    //     0x69322c: b.ne            #0x693238
    //     0x693230: ldr             x2, [PP, #0x528]  ; [pp+0x528] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x693234: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x693238: r1 = <KeyEventResult>
    //     0x693238: ldr             x1, [PP, #0x21d8]  ; [pp+0x21d8] TypeArguments: <KeyEventResult>
    // 0x69323c: stur            x0, [fp, #-0x68]
    // 0x693240: r0 = AllocateGrowableArray()
    //     0x693240: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x693244: mov             x1, x0
    // 0x693248: ldur            x0, [fp, #-0x68]
    // 0x69324c: stur            x1, [fp, #-0x70]
    // 0x693250: StoreField: r1->field_f = r0
    //     0x693250: stur            w0, [x1, #0xf]
    // 0x693254: StoreField: r1->field_b = rZR
    //     0x693254: stur            wzr, [x1, #0xb]
    // 0x693258: ldur            x2, [fp, #-0x10]
    // 0x69325c: LoadField: r3 = r2->field_3b
    //     0x69325c: ldur            w3, [x2, #0x3b]
    // 0x693260: DecompressPointer r3
    //     0x693260: add             x3, x3, HEAP, lsl #32
    // 0x693264: cmp             w3, NULL
    // 0x693268: b.eq            #0x693398
    // 0x69326c: ldur            x3, [fp, #-0x40]
    // 0x693270: LoadField: r4 = r3->field_b
    //     0x693270: ldur            w4, [x3, #0xb]
    // 0x693274: r5 = LoadInt32Instr(r4)
    //     0x693274: sbfx            x5, x4, #1, #0x1f
    // 0x693278: stur            x5, [fp, #-0x58]
    // 0x69327c: mov             x6, x0
    // 0x693280: r4 = 0
    //     0x693280: movz            x4, #0
    // 0x693284: r0 = 0
    //     0x693284: movz            x0, #0
    // 0x693288: CheckStackOverflow
    //     0x693288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x69328c: cmp             SP, x16
    //     0x693290: b.ls            #0x6937e0
    // 0x693294: LoadField: r7 = r3->field_b
    //     0x693294: ldur            w7, [x3, #0xb]
    // 0x693298: r8 = LoadInt32Instr(r7)
    //     0x693298: sbfx            x8, x7, #1, #0x1f
    // 0x69329c: cmp             x5, x8
    // 0x6932a0: b.ne            #0x693738
    // 0x6932a4: cmp             x0, x8
    // 0x6932a8: b.ge            #0x69338c
    // 0x6932ac: LoadField: r4 = r3->field_f
    //     0x6932ac: ldur            w4, [x3, #0xf]
    // 0x6932b0: DecompressPointer r4
    //     0x6932b0: add             x4, x4, HEAP, lsl #32
    // 0x6932b4: ArrayLoad: r6 = r4[r0]  ; Unknown_4
    //     0x6932b4: add             x16, x4, x0, lsl #2
    //     0x6932b8: ldur            w6, [x16, #0xf]
    // 0x6932bc: DecompressPointer r6
    //     0x6932bc: add             x6, x6, HEAP, lsl #32
    // 0x6932c0: add             x4, x0, #1
    // 0x6932c4: stur            x4, [fp, #-0x50]
    // 0x6932c8: LoadField: r0 = r2->field_3b
    //     0x6932c8: ldur            w0, [x2, #0x3b]
    // 0x6932cc: DecompressPointer r0
    //     0x6932cc: add             x0, x0, HEAP, lsl #32
    // 0x6932d0: cmp             w0, NULL
    // 0x6932d4: b.eq            #0x6937e8
    // 0x6932d8: stp             x2, x0, [SP, #8]
    // 0x6932dc: str             x6, [SP]
    // 0x6932e0: ClosureCall
    //     0x6932e0: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x6932e4: ldur            x2, [x0, #0x1f]
    //     0x6932e8: blr             x2
    // 0x6932ec: mov             x2, x0
    // 0x6932f0: ldur            x0, [fp, #-0x70]
    // 0x6932f4: stur            x2, [fp, #-0x78]
    // 0x6932f8: LoadField: r1 = r0->field_b
    //     0x6932f8: ldur            w1, [x0, #0xb]
    // 0x6932fc: LoadField: r3 = r0->field_f
    //     0x6932fc: ldur            w3, [x0, #0xf]
    // 0x693300: DecompressPointer r3
    //     0x693300: add             x3, x3, HEAP, lsl #32
    // 0x693304: LoadField: r4 = r3->field_b
    //     0x693304: ldur            w4, [x3, #0xb]
    // 0x693308: r3 = LoadInt32Instr(r1)
    //     0x693308: sbfx            x3, x1, #1, #0x1f
    // 0x69330c: stur            x3, [fp, #-0x60]
    // 0x693310: r1 = LoadInt32Instr(r4)
    //     0x693310: sbfx            x1, x4, #1, #0x1f
    // 0x693314: cmp             x3, x1
    // 0x693318: b.ne            #0x693324
    // 0x69331c: mov             x1, x0
    // 0x693320: r0 = _growToNextCapacity()
    //     0x693320: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x693324: ldur            x2, [fp, #-0x70]
    // 0x693328: ldur            x3, [fp, #-0x60]
    // 0x69332c: add             x4, x3, #1
    // 0x693330: lsl             x0, x4, #1
    // 0x693334: StoreField: r2->field_b = r0
    //     0x693334: stur            w0, [x2, #0xb]
    // 0x693338: LoadField: r5 = r2->field_f
    //     0x693338: ldur            w5, [x2, #0xf]
    // 0x69333c: DecompressPointer r5
    //     0x69333c: add             x5, x5, HEAP, lsl #32
    // 0x693340: mov             x1, x5
    // 0x693344: ldur            x0, [fp, #-0x78]
    // 0x693348: ArrayStore: r1[r3] = r0  ; List_4
    //     0x693348: add             x25, x1, x3, lsl #2
    //     0x69334c: add             x25, x25, #0xf
    //     0x693350: str             w0, [x25]
    //     0x693354: tbz             w0, #0, #0x693370
    //     0x693358: ldurb           w16, [x1, #-1]
    //     0x69335c: ldurb           w17, [x0, #-1]
    //     0x693360: and             x16, x17, x16, lsr #2
    //     0x693364: tst             x16, HEAP, lsr #32
    //     0x693368: b.eq            #0x693370
    //     0x69336c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x693370: mov             x6, x5
    // 0x693374: ldur            x0, [fp, #-0x50]
    // 0x693378: ldur            x3, [fp, #-0x40]
    // 0x69337c: mov             x1, x2
    // 0x693380: ldur            x5, [fp, #-0x58]
    // 0x693384: ldur            x2, [fp, #-0x10]
    // 0x693388: b               #0x693288
    // 0x69338c: mov             x3, x4
    // 0x693390: mov             x4, x6
    // 0x693394: b               #0x6933a0
    // 0x693398: mov             x4, x0
    // 0x69339c: r3 = 0
    //     0x69339c: movz            x3, #0
    // 0x6933a0: stur            x4, [fp, #-0x70]
    // 0x6933a4: stur            x3, [fp, #-0x58]
    // 0x6933a8: r5 = false
    //     0x6933a8: add             x5, NULL, #0x30  ; false
    // 0x6933ac: r0 = 0
    //     0x6933ac: movz            x0, #0
    // 0x6933b0: stur            x5, [fp, #-0x68]
    // 0x6933b4: CheckStackOverflow
    //     0x6933b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6933b8: cmp             SP, x16
    //     0x6933bc: b.ls            #0x6937ec
    // 0x6933c0: cmp             x0, x3
    // 0x6933c4: b.ge            #0x693458
    // 0x6933c8: ArrayLoad: r6 = r4[r0]  ; Unknown_4
    //     0x6933c8: add             x16, x4, x0, lsl #2
    //     0x6933cc: ldur            w6, [x16, #0xf]
    // 0x6933d0: DecompressPointer r6
    //     0x6933d0: add             x6, x6, HEAP, lsl #32
    // 0x6933d4: stur            x6, [fp, #-0x10]
    // 0x6933d8: add             x7, x0, #1
    // 0x6933dc: stur            x7, [fp, #-0x50]
    // 0x6933e0: cmp             w6, NULL
    // 0x6933e4: b.ne            #0x69341c
    // 0x6933e8: mov             x0, x6
    // 0x6933ec: r2 = Null
    //     0x6933ec: mov             x2, NULL
    // 0x6933f0: r1 = Null
    //     0x6933f0: mov             x1, NULL
    // 0x6933f4: r4 = 60
    //     0x6933f4: movz            x4, #0x3c
    // 0x6933f8: branchIfSmi(r0, 0x693404)
    //     0x6933f8: tbz             w0, #0, #0x693404
    // 0x6933fc: r4 = LoadClassIdInstr(r0)
    //     0x6933fc: ldur            x4, [x0, #-1]
    //     0x693400: ubfx            x4, x4, #0xc, #0x14
    // 0x693404: r17 = 6970
    //     0x693404: movz            x17, #0x1b3a
    // 0x693408: cmp             x4, x17
    // 0x69340c: b.eq            #0x69341c
    // 0x693410: r8 = KeyEventResult
    //     0x693410: ldr             x8, [PP, #0x2210]  ; [pp+0x2210] Type: KeyEventResult
    // 0x693414: r3 = Null
    //     0x693414: ldr             x3, [PP, #0x2218]  ; [pp+0x2218] Null
    // 0x693418: r0 = KeyEventResult()
    //     0x693418: bl              #0x693954  ; IsType_KeyEventResult_Stub
    // 0x69341c: ldur            x0, [fp, #-0x10]
    // 0x693420: LoadField: r1 = r0->field_7
    //     0x693420: ldur            x1, [x0, #7]
    // 0x693424: cmp             x1, #1
    // 0x693428: b.gt            #0x693444
    // 0x69342c: cmp             x1, #0
    // 0x693430: b.le            #0x69343c
    // 0x693434: ldur            x5, [fp, #-0x68]
    // 0x693438: b               #0x693448
    // 0x69343c: r0 = Instance_KeyEventResult
    //     0x69343c: ldr             x0, [PP, #0x2228]  ; [pp+0x2228] Obj!KeyEventResult@e34601
    // 0x693440: b               #0x693470
    // 0x693444: r5 = true
    //     0x693444: add             x5, NULL, #0x20  ; true
    // 0x693448: ldur            x0, [fp, #-0x50]
    // 0x69344c: ldur            x4, [fp, #-0x70]
    // 0x693450: ldur            x3, [fp, #-0x58]
    // 0x693454: b               #0x6933b0
    // 0x693458: mov             x0, x5
    // 0x69345c: tbnz            w0, #4, #0x693468
    // 0x693460: r1 = Instance_KeyEventResult
    //     0x693460: ldr             x1, [PP, #0x2230]  ; [pp+0x2230] Obj!KeyEventResult@e345e1
    // 0x693464: b               #0x69346c
    // 0x693468: r1 = Instance_KeyEventResult
    //     0x693468: ldr             x1, [PP, #0x2238]  ; [pp+0x2238] Obj!KeyEventResult@e345c1
    // 0x69346c: mov             x0, x1
    // 0x693470: LoadField: r1 = r0->field_7
    //     0x693470: ldur            x1, [x0, #7]
    // 0x693474: cmp             x1, #1
    // 0x693478: b.gt            #0x6934a0
    // 0x69347c: cmp             x1, #0
    // 0x693480: b.le            #0x693498
    // 0x693484: ldur            x0, [fp, #-0x20]
    // 0x693488: ldur            x5, [fp, #-0x40]
    // 0x69348c: ldur            x3, [fp, #-0x28]
    // 0x693490: ldur            x4, [fp, #-0x38]
    // 0x693494: b               #0x693198
    // 0x693498: r0 = true
    //     0x693498: add             x0, NULL, #0x20  ; true
    // 0x69349c: b               #0x6934ac
    // 0x6934a0: r0 = false
    //     0x6934a0: add             x0, NULL, #0x30  ; false
    // 0x6934a4: b               #0x6934ac
    // 0x6934a8: ldur            x0, [fp, #-0x18]
    // 0x6934ac: stur            x0, [fp, #-0x18]
    // 0x6934b0: tbz             w0, #4, #0x6936e8
    // 0x6934b4: ldur            x1, [fp, #-8]
    // 0x6934b8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x6934b8: ldur            w2, [x1, #0x17]
    // 0x6934bc: DecompressPointer r2
    //     0x6934bc: add             x2, x2, HEAP, lsl #32
    // 0x6934c0: mov             x1, x2
    // 0x6934c4: stur            x2, [fp, #-0x10]
    // 0x6934c8: r0 = isNotEmpty()
    //     0x6934c8: bl              #0x874120  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::isNotEmpty
    // 0x6934cc: tbnz            w0, #4, #0x6936e8
    // 0x6934d0: r1 = <KeyEventResult>
    //     0x6934d0: ldr             x1, [PP, #0x21d8]  ; [pp+0x21d8] TypeArguments: <KeyEventResult>
    // 0x6934d4: r2 = 0
    //     0x6934d4: movz            x2, #0
    // 0x6934d8: r0 = _GrowableList()
    //     0x6934d8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x6934dc: ldur            x1, [fp, #-0x10]
    // 0x6934e0: stur            x0, [fp, #-8]
    // 0x6934e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6934e4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6934e8: r0 = toList()
    //     0x6934e8: bl              #0xa532a8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::toList
    // 0x6934ec: mov             x3, x0
    // 0x6934f0: stur            x3, [fp, #-0x70]
    // 0x6934f4: LoadField: r4 = r3->field_7
    //     0x6934f4: ldur            w4, [x3, #7]
    // 0x6934f8: DecompressPointer r4
    //     0x6934f8: add             x4, x4, HEAP, lsl #32
    // 0x6934fc: stur            x4, [fp, #-0x68]
    // 0x693500: LoadField: r0 = r3->field_b
    //     0x693500: ldur            w0, [x3, #0xb]
    // 0x693504: r5 = LoadInt32Instr(r0)
    //     0x693504: sbfx            x5, x0, #1, #0x1f
    // 0x693508: stur            x5, [fp, #-0x38]
    // 0x69350c: ldur            x6, [fp, #-8]
    // 0x693510: r0 = 0
    //     0x693510: movz            x0, #0
    // 0x693514: ldur            x7, [fp, #-0x40]
    // 0x693518: CheckStackOverflow
    //     0x693518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x69351c: cmp             SP, x16
    //     0x693520: b.ls            #0x6937f4
    // 0x693524: LoadField: r1 = r3->field_b
    //     0x693524: ldur            w1, [x3, #0xb]
    // 0x693528: r2 = LoadInt32Instr(r1)
    //     0x693528: sbfx            x2, x1, #1, #0x1f
    // 0x69352c: cmp             x5, x2
    // 0x693530: b.ne            #0x693778
    // 0x693534: cmp             x0, x2
    // 0x693538: b.ge            #0x6936b0
    // 0x69353c: LoadField: r1 = r3->field_f
    //     0x69353c: ldur            w1, [x3, #0xf]
    // 0x693540: DecompressPointer r1
    //     0x693540: add             x1, x1, HEAP, lsl #32
    // 0x693544: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0x693544: add             x16, x1, x0, lsl #2
    //     0x693548: ldur            w8, [x16, #0xf]
    // 0x69354c: DecompressPointer r8
    //     0x69354c: add             x8, x8, HEAP, lsl #32
    // 0x693550: stur            x8, [fp, #-0x10]
    // 0x693554: add             x9, x0, #1
    // 0x693558: stur            x9, [fp, #-0x20]
    // 0x69355c: cmp             w8, NULL
    // 0x693560: b.ne            #0x693590
    // 0x693564: mov             x0, x8
    // 0x693568: mov             x2, x4
    // 0x69356c: r1 = Null
    //     0x69356c: mov             x1, NULL
    // 0x693570: cmp             w2, NULL
    // 0x693574: b.eq            #0x693590
    // 0x693578: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x693578: ldur            w4, [x2, #0x17]
    // 0x69357c: DecompressPointer r4
    //     0x69357c: add             x4, x4, HEAP, lsl #32
    // 0x693580: r8 = X0
    //     0x693580: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x693584: LoadField: r9 = r4->field_7
    //     0x693584: ldur            x9, [x4, #7]
    // 0x693588: r3 = Null
    //     0x693588: ldr             x3, [PP, #0x2240]  ; [pp+0x2240] Null
    // 0x69358c: blr             x9
    // 0x693590: ldur            x1, [fp, #-0x40]
    // 0x693594: LoadField: r0 = r1->field_b
    //     0x693594: ldur            w0, [x1, #0xb]
    // 0x693598: r2 = LoadInt32Instr(r0)
    //     0x693598: sbfx            x2, x0, #1, #0x1f
    // 0x69359c: stur            x2, [fp, #-0x58]
    // 0x6935a0: ldur            x3, [fp, #-8]
    // 0x6935a4: r0 = 0
    //     0x6935a4: movz            x0, #0
    // 0x6935a8: CheckStackOverflow
    //     0x6935a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6935ac: cmp             SP, x16
    //     0x6935b0: b.ls            #0x6937fc
    // 0x6935b4: LoadField: r4 = r1->field_b
    //     0x6935b4: ldur            w4, [x1, #0xb]
    // 0x6935b8: r5 = LoadInt32Instr(r4)
    //     0x6935b8: sbfx            x5, x4, #1, #0x1f
    // 0x6935bc: cmp             x2, x5
    // 0x6935c0: b.ne            #0x693758
    // 0x6935c4: cmp             x0, x5
    // 0x6935c8: b.ge            #0x693694
    // 0x6935cc: LoadField: r4 = r1->field_f
    //     0x6935cc: ldur            w4, [x1, #0xf]
    // 0x6935d0: DecompressPointer r4
    //     0x6935d0: add             x4, x4, HEAP, lsl #32
    // 0x6935d4: ArrayLoad: r5 = r4[r0]  ; Unknown_4
    //     0x6935d4: add             x16, x4, x0, lsl #2
    //     0x6935d8: ldur            w5, [x16, #0xf]
    // 0x6935dc: DecompressPointer r5
    //     0x6935dc: add             x5, x5, HEAP, lsl #32
    // 0x6935e0: add             x4, x0, #1
    // 0x6935e4: stur            x4, [fp, #-0x50]
    // 0x6935e8: ldur            x16, [fp, #-0x10]
    // 0x6935ec: stp             x5, x16, [SP]
    // 0x6935f0: ldur            x0, [fp, #-0x10]
    // 0x6935f4: ClosureCall
    //     0x6935f4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x6935f8: ldur            x2, [x0, #0x1f]
    //     0x6935fc: blr             x2
    // 0x693600: mov             x2, x0
    // 0x693604: ldur            x0, [fp, #-8]
    // 0x693608: stur            x2, [fp, #-0x78]
    // 0x69360c: LoadField: r1 = r0->field_b
    //     0x69360c: ldur            w1, [x0, #0xb]
    // 0x693610: LoadField: r3 = r0->field_f
    //     0x693610: ldur            w3, [x0, #0xf]
    // 0x693614: DecompressPointer r3
    //     0x693614: add             x3, x3, HEAP, lsl #32
    // 0x693618: LoadField: r4 = r3->field_b
    //     0x693618: ldur            w4, [x3, #0xb]
    // 0x69361c: r3 = LoadInt32Instr(r1)
    //     0x69361c: sbfx            x3, x1, #1, #0x1f
    // 0x693620: stur            x3, [fp, #-0x60]
    // 0x693624: r1 = LoadInt32Instr(r4)
    //     0x693624: sbfx            x1, x4, #1, #0x1f
    // 0x693628: cmp             x3, x1
    // 0x69362c: b.ne            #0x693638
    // 0x693630: mov             x1, x0
    // 0x693634: r0 = _growToNextCapacity()
    //     0x693634: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x693638: ldur            x2, [fp, #-8]
    // 0x69363c: ldur            x3, [fp, #-0x60]
    // 0x693640: add             x0, x3, #1
    // 0x693644: lsl             x1, x0, #1
    // 0x693648: StoreField: r2->field_b = r1
    //     0x693648: stur            w1, [x2, #0xb]
    // 0x69364c: LoadField: r1 = r2->field_f
    //     0x69364c: ldur            w1, [x2, #0xf]
    // 0x693650: DecompressPointer r1
    //     0x693650: add             x1, x1, HEAP, lsl #32
    // 0x693654: ldur            x0, [fp, #-0x78]
    // 0x693658: ArrayStore: r1[r3] = r0  ; List_4
    //     0x693658: add             x25, x1, x3, lsl #2
    //     0x69365c: add             x25, x25, #0xf
    //     0x693660: str             w0, [x25]
    //     0x693664: tbz             w0, #0, #0x693680
    //     0x693668: ldurb           w16, [x1, #-1]
    //     0x69366c: ldurb           w17, [x0, #-1]
    //     0x693670: and             x16, x17, x16, lsr #2
    //     0x693674: tst             x16, HEAP, lsr #32
    //     0x693678: b.eq            #0x693680
    //     0x69367c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x693680: ldur            x0, [fp, #-0x50]
    // 0x693684: mov             x3, x2
    // 0x693688: ldur            x1, [fp, #-0x40]
    // 0x69368c: ldur            x2, [fp, #-0x58]
    // 0x693690: b               #0x6935a8
    // 0x693694: mov             x2, x3
    // 0x693698: ldur            x0, [fp, #-0x20]
    // 0x69369c: mov             x6, x2
    // 0x6936a0: ldur            x3, [fp, #-0x70]
    // 0x6936a4: ldur            x4, [fp, #-0x68]
    // 0x6936a8: ldur            x5, [fp, #-0x38]
    // 0x6936ac: b               #0x693514
    // 0x6936b0: mov             x2, x6
    // 0x6936b4: mov             x1, x2
    // 0x6936b8: r0 = combineKeyEventResults()
    //     0x6936b8: bl              #0x693804  ; [package:flutter/src/widgets/focus_manager.dart] ::combineKeyEventResults
    // 0x6936bc: LoadField: r1 = r0->field_7
    //     0x6936bc: ldur            x1, [x0, #7]
    // 0x6936c0: cmp             x1, #1
    // 0x6936c4: b.gt            #0x6936e0
    // 0x6936c8: cmp             x1, #0
    // 0x6936cc: b.gt            #0x6936d8
    // 0x6936d0: r0 = true
    //     0x6936d0: add             x0, NULL, #0x20  ; true
    // 0x6936d4: b               #0x6936ec
    // 0x6936d8: ldur            x0, [fp, #-0x18]
    // 0x6936dc: b               #0x6936ec
    // 0x6936e0: r0 = false
    //     0x6936e0: add             x0, NULL, #0x30  ; false
    // 0x6936e4: b               #0x6936ec
    // 0x6936e8: ldur            x0, [fp, #-0x18]
    // 0x6936ec: LeaveFrame
    //     0x6936ec: mov             SP, fp
    //     0x6936f0: ldp             fp, lr, [SP], #0x10
    // 0x6936f4: ret
    //     0x6936f4: ret             
    // 0x6936f8: mov             x0, x1
    // 0x6936fc: r0 = ConcurrentModificationError()
    //     0x6936fc: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x693700: mov             x1, x0
    // 0x693704: ldur            x0, [fp, #-0x30]
    // 0x693708: StoreField: r1->field_b = r0
    //     0x693708: stur            w0, [x1, #0xb]
    // 0x69370c: mov             x0, x1
    // 0x693710: r0 = Throw()
    //     0x693710: bl              #0xec04b8  ; ThrowStub
    // 0x693714: brk             #0
    // 0x693718: mov             x0, x3
    // 0x69371c: r0 = ConcurrentModificationError()
    //     0x69371c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x693720: mov             x1, x0
    // 0x693724: ldur            x0, [fp, #-0x48]
    // 0x693728: StoreField: r1->field_b = r0
    //     0x693728: stur            w0, [x1, #0xb]
    // 0x69372c: mov             x0, x1
    // 0x693730: r0 = Throw()
    //     0x693730: bl              #0xec04b8  ; ThrowStub
    // 0x693734: brk             #0
    // 0x693738: mov             x0, x3
    // 0x69373c: r0 = ConcurrentModificationError()
    //     0x69373c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x693740: mov             x1, x0
    // 0x693744: ldur            x0, [fp, #-0x40]
    // 0x693748: StoreField: r1->field_b = r0
    //     0x693748: stur            w0, [x1, #0xb]
    // 0x69374c: mov             x0, x1
    // 0x693750: r0 = Throw()
    //     0x693750: bl              #0xec04b8  ; ThrowStub
    // 0x693754: brk             #0
    // 0x693758: mov             x0, x1
    // 0x69375c: r0 = ConcurrentModificationError()
    //     0x69375c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x693760: mov             x1, x0
    // 0x693764: ldur            x0, [fp, #-0x40]
    // 0x693768: StoreField: r1->field_b = r0
    //     0x693768: stur            w0, [x1, #0xb]
    // 0x69376c: mov             x0, x1
    // 0x693770: r0 = Throw()
    //     0x693770: bl              #0xec04b8  ; ThrowStub
    // 0x693774: brk             #0
    // 0x693778: mov             x0, x3
    // 0x69377c: r0 = ConcurrentModificationError()
    //     0x69377c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x693780: mov             x1, x0
    // 0x693784: ldur            x0, [fp, #-0x70]
    // 0x693788: StoreField: r1->field_b = r0
    //     0x693788: stur            w0, [x1, #0xb]
    // 0x69378c: mov             x0, x1
    // 0x693790: r0 = Throw()
    //     0x693790: bl              #0xec04b8  ; ThrowStub
    // 0x693794: brk             #0
    // 0x693798: mov             x0, x3
    // 0x69379c: r0 = ConcurrentModificationError()
    //     0x69379c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6937a0: mov             x1, x0
    // 0x6937a4: ldur            x0, [fp, #-0x28]
    // 0x6937a8: StoreField: r1->field_b = r0
    //     0x6937a8: stur            w0, [x1, #0xb]
    // 0x6937ac: mov             x0, x1
    // 0x6937b0: r0 = Throw()
    //     0x6937b0: bl              #0xec04b8  ; ThrowStub
    // 0x6937b4: brk             #0
    // 0x6937b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6937b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6937bc: b               #0x692e5c
    // 0x6937c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6937c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6937c4: b               #0x692f0c
    // 0x6937c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6937c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6937cc: b               #0x692f9c
    // 0x6937d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6937d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6937d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6937d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6937d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6937d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6937dc: b               #0x6931a4
    // 0x6937e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6937e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6937e4: b               #0x693294
    // 0x6937e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6937e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6937ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6937ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6937f0: b               #0x6933c0
    // 0x6937f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6937f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6937f8: b               #0x693524
    // 0x6937fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6937fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x693800: b               #0x6935b4
  }
  _ _HighlightModeManager(/* No info */) {
    // ** addr: 0x693ff0, size: 0xe8
    // 0x693ff0: EnterFrame
    //     0x693ff0: stp             fp, lr, [SP, #-0x10]!
    //     0x693ff4: mov             fp, SP
    // 0x693ff8: AllocStack(0x10)
    //     0x693ff8: sub             SP, SP, #0x10
    // 0x693ffc: r0 = Instance_FocusHighlightStrategy
    //     0x693ffc: ldr             x0, [PP, #0x25b8]  ; [pp+0x25b8] Obj!FocusHighlightStrategy@e34521
    // 0x694000: mov             x2, x1
    // 0x694004: stur            x1, [fp, #-8]
    // 0x694008: CheckStackOverflow
    //     0x694008: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x69400c: cmp             SP, x16
    //     0x694010: b.ls            #0x6940d0
    // 0x694014: StoreField: r2->field_f = r0
    //     0x694014: stur            w0, [x2, #0xf]
    // 0x694018: r1 = <(dynamic this, KeyEvent) => KeyEventResult>
    //     0x694018: ldr             x1, [PP, #0x25c0]  ; [pp+0x25c0] TypeArguments: <(dynamic this, KeyEvent) => KeyEventResult>
    // 0x69401c: r0 = HashedObserverList()
    //     0x69401c: bl              #0x6941bc  ; AllocateHashedObserverListStub -> HashedObserverList<X0> (size=0x10)
    // 0x694020: mov             x1, x0
    // 0x694024: stur            x0, [fp, #-0x10]
    // 0x694028: r0 = HashedObserverList()
    //     0x694028: bl              #0x6940d8  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::HashedObserverList
    // 0x69402c: ldur            x0, [fp, #-0x10]
    // 0x694030: ldur            x2, [fp, #-8]
    // 0x694034: StoreField: r2->field_13 = r0
    //     0x694034: stur            w0, [x2, #0x13]
    //     0x694038: ldurb           w16, [x2, #-1]
    //     0x69403c: ldurb           w17, [x0, #-1]
    //     0x694040: and             x16, x17, x16, lsr #2
    //     0x694044: tst             x16, HEAP, lsr #32
    //     0x694048: b.eq            #0x694050
    //     0x69404c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x694050: r1 = <(dynamic this, KeyEvent) => KeyEventResult>
    //     0x694050: ldr             x1, [PP, #0x25c0]  ; [pp+0x25c0] TypeArguments: <(dynamic this, KeyEvent) => KeyEventResult>
    // 0x694054: r0 = HashedObserverList()
    //     0x694054: bl              #0x6941bc  ; AllocateHashedObserverListStub -> HashedObserverList<X0> (size=0x10)
    // 0x694058: mov             x1, x0
    // 0x69405c: stur            x0, [fp, #-0x10]
    // 0x694060: r0 = HashedObserverList()
    //     0x694060: bl              #0x6940d8  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::HashedObserverList
    // 0x694064: ldur            x0, [fp, #-0x10]
    // 0x694068: ldur            x2, [fp, #-8]
    // 0x69406c: ArrayStore: r2[0] = r0  ; List_4
    //     0x69406c: stur            w0, [x2, #0x17]
    //     0x694070: ldurb           w16, [x2, #-1]
    //     0x694074: ldurb           w17, [x0, #-1]
    //     0x694078: and             x16, x17, x16, lsr #2
    //     0x69407c: tst             x16, HEAP, lsr #32
    //     0x694080: b.eq            #0x694088
    //     0x694084: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x694088: r1 = <(dynamic this, FocusHighlightMode) => void?>
    //     0x694088: ldr             x1, [PP, #0x2190]  ; [pp+0x2190] TypeArguments: <(dynamic this, FocusHighlightMode) => void?>
    // 0x69408c: r0 = HashedObserverList()
    //     0x69408c: bl              #0x6941bc  ; AllocateHashedObserverListStub -> HashedObserverList<X0> (size=0x10)
    // 0x694090: mov             x1, x0
    // 0x694094: stur            x0, [fp, #-0x10]
    // 0x694098: r0 = HashedObserverList()
    //     0x694098: bl              #0x6940d8  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::HashedObserverList
    // 0x69409c: ldur            x0, [fp, #-0x10]
    // 0x6940a0: ldur            x1, [fp, #-8]
    // 0x6940a4: StoreField: r1->field_1b = r0
    //     0x6940a4: stur            w0, [x1, #0x1b]
    //     0x6940a8: ldurb           w16, [x1, #-1]
    //     0x6940ac: ldurb           w17, [x0, #-1]
    //     0x6940b0: and             x16, x17, x16, lsr #2
    //     0x6940b4: tst             x16, HEAP, lsr #32
    //     0x6940b8: b.eq            #0x6940c0
    //     0x6940bc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6940c0: r0 = Null
    //     0x6940c0: mov             x0, NULL
    // 0x6940c4: LeaveFrame
    //     0x6940c4: mov             SP, fp
    //     0x6940c8: ldp             fp, lr, [SP], #0x10
    // 0x6940cc: ret
    //     0x6940cc: ret             
    // 0x6940d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6940d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6940d4: b               #0x694014
  }
  _ addListener(/* No info */) {
    // ** addr: 0x9338f4, size: 0x3c
    // 0x9338f4: EnterFrame
    //     0x9338f4: stp             fp, lr, [SP, #-0x10]!
    //     0x9338f8: mov             fp, SP
    // 0x9338fc: CheckStackOverflow
    //     0x9338fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x933900: cmp             SP, x16
    //     0x933904: b.ls            #0x933928
    // 0x933908: LoadField: r0 = r1->field_1b
    //     0x933908: ldur            w0, [x1, #0x1b]
    // 0x93390c: DecompressPointer r0
    //     0x93390c: add             x0, x0, HEAP, lsl #32
    // 0x933910: mov             x1, x0
    // 0x933914: r0 = add()
    //     0x933914: bl              #0x933930  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::add
    // 0x933918: r0 = Null
    //     0x933918: mov             x0, NULL
    // 0x93391c: LeaveFrame
    //     0x93391c: mov             SP, fp
    //     0x933920: ldp             fp, lr, [SP], #0x10
    // 0x933924: ret
    //     0x933924: ret             
    // 0x933928: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x933928: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93392c: b               #0x933908
  }
  _ removeListener(/* No info */) {
    // ** addr: 0xa7d5bc, size: 0x38
    // 0xa7d5bc: EnterFrame
    //     0xa7d5bc: stp             fp, lr, [SP, #-0x10]!
    //     0xa7d5c0: mov             fp, SP
    // 0xa7d5c4: CheckStackOverflow
    //     0xa7d5c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7d5c8: cmp             SP, x16
    //     0xa7d5cc: b.ls            #0xa7d5ec
    // 0xa7d5d0: LoadField: r0 = r1->field_1b
    //     0xa7d5d0: ldur            w0, [x1, #0x1b]
    // 0xa7d5d4: DecompressPointer r0
    //     0xa7d5d4: add             x0, x0, HEAP, lsl #32
    // 0xa7d5d8: mov             x1, x0
    // 0xa7d5dc: r0 = remove()
    //     0xa7d5dc: bl              #0xa7d5f4  ; [package:flutter/src/foundation/observer_list.dart] HashedObserverList::remove
    // 0xa7d5e0: LeaveFrame
    //     0xa7d5e0: mov             SP, fp
    //     0xa7d5e4: ldp             fp, lr, [SP], #0x10
    // 0xa7d5e8: ret
    //     0xa7d5e8: ret             
    // 0xa7d5ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7d5ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7d5f0: b               #0xa7d5d0
  }
}

// class id: 2697, size: 0xc, field offset: 0x8
class FocusAttachment extends Object {

  _ reparent(/* No info */) {
    // ** addr: 0x92b6b0, size: 0xbc
    // 0x92b6b0: EnterFrame
    //     0x92b6b0: stp             fp, lr, [SP, #-0x10]!
    //     0x92b6b4: mov             fp, SP
    // 0x92b6b8: AllocStack(0x8)
    //     0x92b6b8: sub             SP, SP, #8
    // 0x92b6bc: CheckStackOverflow
    //     0x92b6bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92b6c0: cmp             SP, x16
    //     0x92b6c4: b.ls            #0x92b758
    // 0x92b6c8: LoadField: r2 = r1->field_7
    //     0x92b6c8: ldur            w2, [x1, #7]
    // 0x92b6cc: DecompressPointer r2
    //     0x92b6cc: add             x2, x2, HEAP, lsl #32
    // 0x92b6d0: stur            x2, [fp, #-8]
    // 0x92b6d4: LoadField: r0 = r2->field_5b
    //     0x92b6d4: ldur            w0, [x2, #0x5b]
    // 0x92b6d8: DecompressPointer r0
    //     0x92b6d8: add             x0, x0, HEAP, lsl #32
    // 0x92b6dc: cmp             w0, w1
    // 0x92b6e0: b.ne            #0x92b748
    // 0x92b6e4: LoadField: r1 = r2->field_33
    //     0x92b6e4: ldur            w1, [x2, #0x33]
    // 0x92b6e8: DecompressPointer r1
    //     0x92b6e8: add             x1, x1, HEAP, lsl #32
    // 0x92b6ec: cmp             w1, NULL
    // 0x92b6f0: b.eq            #0x92b760
    // 0x92b6f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x92b6f4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x92b6f8: r0 = maybeOf()
    //     0x92b6f8: bl              #0x65184c  ; [package:flutter/src/widgets/focus_scope.dart] Focus::maybeOf
    // 0x92b6fc: cmp             w0, NULL
    // 0x92b700: b.ne            #0x92b73c
    // 0x92b704: ldur            x2, [fp, #-8]
    // 0x92b708: LoadField: r0 = r2->field_33
    //     0x92b708: ldur            w0, [x2, #0x33]
    // 0x92b70c: DecompressPointer r0
    //     0x92b70c: add             x0, x0, HEAP, lsl #32
    // 0x92b710: cmp             w0, NULL
    // 0x92b714: b.eq            #0x92b764
    // 0x92b718: LoadField: r1 = r0->field_1b
    //     0x92b718: ldur            w1, [x0, #0x1b]
    // 0x92b71c: DecompressPointer r1
    //     0x92b71c: add             x1, x1, HEAP, lsl #32
    // 0x92b720: cmp             w1, NULL
    // 0x92b724: b.eq            #0x92b768
    // 0x92b728: LoadField: r0 = r1->field_13
    //     0x92b728: ldur            w0, [x1, #0x13]
    // 0x92b72c: DecompressPointer r0
    //     0x92b72c: add             x0, x0, HEAP, lsl #32
    // 0x92b730: LoadField: r1 = r0->field_27
    //     0x92b730: ldur            w1, [x0, #0x27]
    // 0x92b734: DecompressPointer r1
    //     0x92b734: add             x1, x1, HEAP, lsl #32
    // 0x92b738: b               #0x92b744
    // 0x92b73c: ldur            x2, [fp, #-8]
    // 0x92b740: mov             x1, x0
    // 0x92b744: r0 = _reparent()
    //     0x92b744: bl              #0x6512ac  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_reparent
    // 0x92b748: r0 = Null
    //     0x92b748: mov             x0, NULL
    // 0x92b74c: LeaveFrame
    //     0x92b74c: mov             SP, fp
    //     0x92b750: ldp             fp, lr, [SP], #0x10
    // 0x92b754: ret
    //     0x92b754: ret             
    // 0x92b758: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92b758: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92b75c: b               #0x92b6c8
    // 0x92b760: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92b760: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92b764: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92b764: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92b768: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92b768: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ detach(/* No info */) {
    // ** addr: 0x98f8c0, size: 0xf8
    // 0x98f8c0: EnterFrame
    //     0x98f8c0: stp             fp, lr, [SP, #-0x10]!
    //     0x98f8c4: mov             fp, SP
    // 0x98f8c8: AllocStack(0x18)
    //     0x98f8c8: sub             SP, SP, #0x18
    // 0x98f8cc: CheckStackOverflow
    //     0x98f8cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98f8d0: cmp             SP, x16
    //     0x98f8d4: b.ls            #0x98f9b0
    // 0x98f8d8: LoadField: r0 = r1->field_7
    //     0x98f8d8: ldur            w0, [x1, #7]
    // 0x98f8dc: DecompressPointer r0
    //     0x98f8dc: add             x0, x0, HEAP, lsl #32
    // 0x98f8e0: stur            x0, [fp, #-8]
    // 0x98f8e4: LoadField: r2 = r0->field_5b
    //     0x98f8e4: ldur            w2, [x0, #0x5b]
    // 0x98f8e8: DecompressPointer r2
    //     0x98f8e8: add             x2, x2, HEAP, lsl #32
    // 0x98f8ec: cmp             w2, w1
    // 0x98f8f0: b.ne            #0x98f9a0
    // 0x98f8f4: mov             x1, x0
    // 0x98f8f8: r0 = hasPrimaryFocus()
    //     0x98f8f8: bl              #0x651240  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::hasPrimaryFocus
    // 0x98f8fc: tbz             w0, #4, #0x98f938
    // 0x98f900: ldur            x1, [fp, #-8]
    // 0x98f904: LoadField: r0 = r1->field_3f
    //     0x98f904: ldur            w0, [x1, #0x3f]
    // 0x98f908: DecompressPointer r0
    //     0x98f908: add             x0, x0, HEAP, lsl #32
    // 0x98f90c: cmp             w0, NULL
    // 0x98f910: b.eq            #0x98f954
    // 0x98f914: LoadField: r2 = r0->field_3b
    //     0x98f914: ldur            w2, [x0, #0x3b]
    // 0x98f918: DecompressPointer r2
    //     0x98f918: add             x2, x2, HEAP, lsl #32
    // 0x98f91c: r0 = LoadClassIdInstr(r2)
    //     0x98f91c: ldur            x0, [x2, #-1]
    //     0x98f920: ubfx            x0, x0, #0xc, #0x14
    // 0x98f924: stp             x1, x2, [SP]
    // 0x98f928: mov             lr, x0
    // 0x98f92c: ldr             lr, [x21, lr, lsl #3]
    // 0x98f930: blr             lr
    // 0x98f934: tbnz            w0, #4, #0x98f954
    // 0x98f938: r16 = Instance_UnfocusDisposition
    //     0x98f938: add             x16, PP, #0x23, lsl #12  ; [pp+0x23938] Obj!UnfocusDisposition@e345a1
    //     0x98f93c: ldr             x16, [x16, #0x938]
    // 0x98f940: str             x16, [SP]
    // 0x98f944: ldur            x1, [fp, #-8]
    // 0x98f948: r4 = const [0, 0x2, 0x1, 0x1, disposition, 0x1, null]
    //     0x98f948: add             x4, PP, #0x23, lsl #12  ; [pp+0x23940] List(7) [0, 0x2, 0x1, 0x1, "disposition", 0x1, Null]
    //     0x98f94c: ldr             x4, [x4, #0x940]
    // 0x98f950: r0 = unfocus()
    //     0x98f950: bl              #0x6a834c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::unfocus
    // 0x98f954: ldur            x0, [fp, #-8]
    // 0x98f958: LoadField: r1 = r0->field_3f
    //     0x98f958: ldur            w1, [x0, #0x3f]
    // 0x98f95c: DecompressPointer r1
    //     0x98f95c: add             x1, x1, HEAP, lsl #32
    // 0x98f960: cmp             w1, NULL
    // 0x98f964: b.eq            #0x98f974
    // 0x98f968: mov             x2, x0
    // 0x98f96c: r0 = _markDetached()
    //     0x98f96c: bl              #0x98f9b8  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::_markDetached
    // 0x98f970: ldur            x0, [fp, #-8]
    // 0x98f974: LoadField: r1 = r0->field_4f
    //     0x98f974: ldur            w1, [x0, #0x4f]
    // 0x98f978: DecompressPointer r1
    //     0x98f978: add             x1, x1, HEAP, lsl #32
    // 0x98f97c: cmp             w1, NULL
    // 0x98f980: b.ne            #0x98f98c
    // 0x98f984: mov             x1, x0
    // 0x98f988: b               #0x98f99c
    // 0x98f98c: mov             x2, x0
    // 0x98f990: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x98f990: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x98f994: r0 = _removeChild()
    //     0x98f994: bl              #0x651e74  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_removeChild
    // 0x98f998: ldur            x1, [fp, #-8]
    // 0x98f99c: StoreField: r1->field_5b = rNULL
    //     0x98f99c: stur            NULL, [x1, #0x5b]
    // 0x98f9a0: r0 = Null
    //     0x98f9a0: mov             x0, NULL
    // 0x98f9a4: LeaveFrame
    //     0x98f9a4: mov             SP, fp
    //     0x98f9a8: ldp             fp, lr, [SP], #0x10
    // 0x98f9ac: ret
    //     0x98f9ac: ret             
    // 0x98f9b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98f9b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98f9b4: b               #0x98f8d8
  }
}

// class id: 2698, size: 0x10, field offset: 0x8
//   const constructor, 
class _Autofocus extends Object {

  _ applyIfValid(/* No info */) {
    // ** addr: 0x690460, size: 0xc8
    // 0x690460: EnterFrame
    //     0x690460: stp             fp, lr, [SP, #-0x10]!
    //     0x690464: mov             fp, SP
    // 0x690468: AllocStack(0x18)
    //     0x690468: sub             SP, SP, #0x18
    // 0x69046c: SetupParameters(_Autofocus this /* r1 => r0, fp-0x10 */)
    //     0x69046c: mov             x0, x1
    //     0x690470: stur            x1, [fp, #-0x10]
    // 0x690474: CheckStackOverflow
    //     0x690474: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x690478: cmp             SP, x16
    //     0x69047c: b.ls            #0x690520
    // 0x690480: LoadField: r3 = r0->field_7
    //     0x690480: ldur            w3, [x0, #7]
    // 0x690484: DecompressPointer r3
    //     0x690484: add             x3, x3, HEAP, lsl #32
    // 0x690488: stur            x3, [fp, #-8]
    // 0x69048c: LoadField: r1 = r3->field_4f
    //     0x69048c: ldur            w1, [x3, #0x4f]
    // 0x690490: DecompressPointer r1
    //     0x690490: add             x1, x1, HEAP, lsl #32
    // 0x690494: cmp             w1, NULL
    // 0x690498: b.ne            #0x6904ac
    // 0x69049c: LoadField: r1 = r2->field_27
    //     0x69049c: ldur            w1, [x2, #0x27]
    // 0x6904a0: DecompressPointer r1
    //     0x6904a0: add             x1, x1, HEAP, lsl #32
    // 0x6904a4: cmp             w3, w1
    // 0x6904a8: b.ne            #0x690510
    // 0x6904ac: LoadField: r1 = r3->field_3f
    //     0x6904ac: ldur            w1, [x3, #0x3f]
    // 0x6904b0: DecompressPointer r1
    //     0x6904b0: add             x1, x1, HEAP, lsl #32
    // 0x6904b4: cmp             w1, w2
    // 0x6904b8: b.ne            #0x690510
    // 0x6904bc: mov             x1, x3
    // 0x6904c0: r0 = focusedChild()
    //     0x6904c0: bl              #0x690548  ; [package:flutter/src/widgets/focus_manager.dart] FocusScopeNode::focusedChild
    // 0x6904c4: cmp             w0, NULL
    // 0x6904c8: b.ne            #0x690510
    // 0x6904cc: ldur            x0, [fp, #-0x10]
    // 0x6904d0: LoadField: r2 = r0->field_b
    //     0x6904d0: ldur            w2, [x0, #0xb]
    // 0x6904d4: DecompressPointer r2
    //     0x6904d4: add             x2, x2, HEAP, lsl #32
    // 0x6904d8: mov             x1, x2
    // 0x6904dc: stur            x2, [fp, #-0x18]
    // 0x6904e0: r0 = ancestors()
    //     0x6904e0: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x6904e4: mov             x1, x0
    // 0x6904e8: ldur            x2, [fp, #-8]
    // 0x6904ec: r0 = contains()
    //     0x6904ec: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0x6904f0: tbnz            w0, #4, #0x690510
    // 0x6904f4: ldur            x1, [fp, #-0x18]
    // 0x6904f8: r0 = LoadClassIdInstr(r1)
    //     0x6904f8: ldur            x0, [x1, #-1]
    //     0x6904fc: ubfx            x0, x0, #0xc, #0x14
    // 0x690500: r2 = true
    //     0x690500: add             x2, NULL, #0x20  ; true
    // 0x690504: r0 = GDT[cid_x0 + -0xffc]()
    //     0x690504: sub             lr, x0, #0xffc
    //     0x690508: ldr             lr, [x21, lr, lsl #3]
    //     0x69050c: blr             lr
    // 0x690510: r0 = Null
    //     0x690510: mov             x0, NULL
    // 0x690514: LeaveFrame
    //     0x690514: mov             SP, fp
    //     0x690518: ldp             fp, lr, [SP], #0x10
    // 0x69051c: ret
    //     0x69051c: ret             
    // 0x690520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x690520: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x690524: b               #0x690480
  }
}

// class id: 2732, size: 0xc, field offset: 0x8
class _AppLifecycleListener extends WidgetsBindingObserver {

  _ didChangeAppLifecycleState(/* No info */) {
    // ** addr: 0x68fa3c, size: 0x40
    // 0x68fa3c: EnterFrame
    //     0x68fa3c: stp             fp, lr, [SP, #-0x10]!
    //     0x68fa40: mov             fp, SP
    // 0x68fa44: CheckStackOverflow
    //     0x68fa44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68fa48: cmp             SP, x16
    //     0x68fa4c: b.ls            #0x68fa74
    // 0x68fa50: LoadField: r0 = r1->field_7
    //     0x68fa50: ldur            w0, [x1, #7]
    // 0x68fa54: DecompressPointer r0
    //     0x68fa54: add             x0, x0, HEAP, lsl #32
    // 0x68fa58: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x68fa58: ldur            w1, [x0, #0x17]
    // 0x68fa5c: DecompressPointer r1
    //     0x68fa5c: add             x1, x1, HEAP, lsl #32
    // 0x68fa60: r0 = _appLifecycleChange()
    //     0x68fa60: bl              #0x68fadc  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::_appLifecycleChange
    // 0x68fa64: r0 = Null
    //     0x68fa64: mov             x0, NULL
    // 0x68fa68: LeaveFrame
    //     0x68fa68: mov             SP, fp
    //     0x68fa6c: ldp             fp, lr, [SP], #0x10
    // 0x68fa70: ret
    //     0x68fa70: ret             
    // 0x68fa74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68fa74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68fa78: b               #0x68fa50
  }
}

// class id: 2917, size: 0x24, field offset: 0x8
//   transformed mixin,
abstract class _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier extends _RenderObject&Object&DiagnosticableTreeMixin
     with ChangeNotifier {

  [closure] List<DiagnosticsNode> <anonymous closure>(dynamic) {
    // ** addr: 0x646880, size: 0x120
    // 0x646880: EnterFrame
    //     0x646880: stp             fp, lr, [SP, #-0x10]!
    //     0x646884: mov             fp, SP
    // 0x646888: AllocStack(0x18)
    //     0x646888: sub             SP, SP, #0x18
    // 0x64688c: SetupParameters()
    //     0x64688c: ldr             x0, [fp, #0x10]
    //     0x646890: ldur            w3, [x0, #0x17]
    //     0x646894: add             x3, x3, HEAP, lsl #32
    //     0x646898: stur            x3, [fp, #-8]
    // 0x64689c: CheckStackOverflow
    //     0x64689c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6468a0: cmp             SP, x16
    //     0x6468a4: b.ls            #0x646998
    // 0x6468a8: r1 = Null
    //     0x6468a8: mov             x1, NULL
    // 0x6468ac: r2 = 6
    //     0x6468ac: movz            x2, #0x6
    // 0x6468b0: r0 = AllocateArray()
    //     0x6468b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6468b4: stur            x0, [fp, #-0x10]
    // 0x6468b8: r16 = "The "
    //     0x6468b8: ldr             x16, [PP, #0x23a8]  ; [pp+0x23a8] "The "
    // 0x6468bc: StoreField: r0->field_f = r16
    //     0x6468bc: stur            w16, [x0, #0xf]
    // 0x6468c0: ldur            x1, [fp, #-8]
    // 0x6468c4: LoadField: r2 = r1->field_f
    //     0x6468c4: ldur            w2, [x1, #0xf]
    // 0x6468c8: DecompressPointer r2
    //     0x6468c8: add             x2, x2, HEAP, lsl #32
    // 0x6468cc: str             x2, [SP]
    // 0x6468d0: r0 = runtimeType()
    //     0x6468d0: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0x6468d4: ldur            x1, [fp, #-0x10]
    // 0x6468d8: ArrayStore: r1[1] = r0  ; List_4
    //     0x6468d8: add             x25, x1, #0x13
    //     0x6468dc: str             w0, [x25]
    //     0x6468e0: tbz             w0, #0, #0x6468fc
    //     0x6468e4: ldurb           w16, [x1, #-1]
    //     0x6468e8: ldurb           w17, [x0, #-1]
    //     0x6468ec: and             x16, x17, x16, lsr #2
    //     0x6468f0: tst             x16, HEAP, lsr #32
    //     0x6468f4: b.eq            #0x6468fc
    //     0x6468f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6468fc: ldur            x0, [fp, #-0x10]
    // 0x646900: r16 = " sending notification was"
    //     0x646900: ldr             x16, [PP, #0x23b0]  ; [pp+0x23b0] " sending notification was"
    // 0x646904: ArrayStore: r0[0] = r16  ; List_4
    //     0x646904: stur            w16, [x0, #0x17]
    // 0x646908: str             x0, [SP]
    // 0x64690c: r0 = _interpolate()
    //     0x64690c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x646910: ldur            x0, [fp, #-8]
    // 0x646914: LoadField: r2 = r0->field_f
    //     0x646914: ldur            w2, [x0, #0xf]
    // 0x646918: DecompressPointer r2
    //     0x646918: add             x2, x2, HEAP, lsl #32
    // 0x64691c: stur            x2, [fp, #-0x10]
    // 0x646920: r1 = <ChangeNotifier>
    //     0x646920: ldr             x1, [PP, #0x23b8]  ; [pp+0x23b8] TypeArguments: <ChangeNotifier>
    // 0x646924: r0 = DiagnosticsProperty()
    //     0x646924: bl              #0x641ba0  ; AllocateDiagnosticsPropertyStub -> DiagnosticsProperty<X0> (size=0x2c)
    // 0x646928: mov             x3, x0
    // 0x64692c: r0 = Instance__NoDefaultValue
    //     0x64692c: ldr             x0, [PP, #0x7c8]  ; [pp+0x7c8] Obj!_NoDefaultValue@e14bf1
    // 0x646930: stur            x3, [fp, #-8]
    // 0x646934: StoreField: r3->field_23 = r0
    //     0x646934: stur            w0, [x3, #0x23]
    // 0x646938: r0 = false
    //     0x646938: add             x0, NULL, #0x30  ; false
    // 0x64693c: StoreField: r3->field_13 = r0
    //     0x64693c: stur            w0, [x3, #0x13]
    // 0x646940: r0 = true
    //     0x646940: add             x0, NULL, #0x20  ; true
    // 0x646944: StoreField: r3->field_1b = r0
    //     0x646944: stur            w0, [x3, #0x1b]
    // 0x646948: ldur            x0, [fp, #-0x10]
    // 0x64694c: ArrayStore: r3[0] = r0  ; List_4
    //     0x64694c: stur            w0, [x3, #0x17]
    // 0x646950: r0 = Instance_DiagnosticLevel
    //     0x646950: ldr             x0, [PP, #0x450]  ; [pp+0x450] Obj!DiagnosticLevel@e37021
    // 0x646954: StoreField: r3->field_27 = r0
    //     0x646954: stur            w0, [x3, #0x27]
    // 0x646958: r1 = Null
    //     0x646958: mov             x1, NULL
    // 0x64695c: r2 = 2
    //     0x64695c: movz            x2, #0x2
    // 0x646960: r0 = AllocateArray()
    //     0x646960: bl              #0xec22fc  ; AllocateArrayStub
    // 0x646964: mov             x2, x0
    // 0x646968: ldur            x0, [fp, #-8]
    // 0x64696c: stur            x2, [fp, #-0x10]
    // 0x646970: StoreField: r2->field_f = r0
    //     0x646970: stur            w0, [x2, #0xf]
    // 0x646974: r1 = <DiagnosticsNode>
    //     0x646974: ldr             x1, [PP, #0x23c0]  ; [pp+0x23c0] TypeArguments: <DiagnosticsNode>
    // 0x646978: r0 = AllocateGrowableArray()
    //     0x646978: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x64697c: ldur            x1, [fp, #-0x10]
    // 0x646980: StoreField: r0->field_f = r1
    //     0x646980: stur            w1, [x0, #0xf]
    // 0x646984: r1 = 2
    //     0x646984: movz            x1, #0x2
    // 0x646988: StoreField: r0->field_b = r1
    //     0x646988: stur            w1, [x0, #0xb]
    // 0x64698c: LeaveFrame
    //     0x64698c: mov             SP, fp
    //     0x646990: ldp             fp, lr, [SP], #0x10
    // 0x646994: ret
    //     0x646994: ret             
    // 0x646998: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x646998: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64699c: b               #0x6468a8
  }
  _ notifyListeners(/* No info */) {
    // ** addr: 0x6469a0, size: 0x590
    // 0x6469a0: EnterFrame
    //     0x6469a0: stp             fp, lr, [SP, #-0x10]!
    //     0x6469a4: mov             fp, SP
    // 0x6469a8: AllocStack(0xe8)
    //     0x6469a8: sub             SP, SP, #0xe8
    // 0x6469ac: SetupParameters(_FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier this /* r1 => r1, fp-0x80 */)
    //     0x6469ac: stur            x1, [fp, #-0x80]
    // 0x6469b0: CheckStackOverflow
    //     0x6469b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6469b4: cmp             SP, x16
    //     0x6469b8: b.ls            #0x646ef4
    // 0x6469bc: r1 = 1
    //     0x6469bc: movz            x1, #0x1
    // 0x6469c0: r0 = AllocateContext()
    //     0x6469c0: bl              #0xec126c  ; AllocateContextStub
    // 0x6469c4: mov             x3, x0
    // 0x6469c8: ldur            x2, [fp, #-0x80]
    // 0x6469cc: StoreField: r3->field_f = r2
    //     0x6469cc: stur            w2, [x3, #0xf]
    // 0x6469d0: LoadField: r4 = r2->field_7
    //     0x6469d0: ldur            x4, [x2, #7]
    // 0x6469d4: cbnz            x4, #0x6469e8
    // 0x6469d8: r0 = Null
    //     0x6469d8: mov             x0, NULL
    // 0x6469dc: LeaveFrame
    //     0x6469dc: mov             SP, fp
    //     0x6469e0: ldp             fp, lr, [SP], #0x10
    // 0x6469e4: ret
    //     0x6469e4: ret             
    // 0x6469e8: LoadField: r0 = r2->field_13
    //     0x6469e8: ldur            x0, [x2, #0x13]
    // 0x6469ec: add             x1, x0, #1
    // 0x6469f0: StoreField: r2->field_13 = r1
    //     0x6469f0: stur            x1, [x2, #0x13]
    // 0x6469f4: r0 = BoxInt64Instr(r4)
    //     0x6469f4: sbfiz           x0, x4, #1, #0x1f
    //     0x6469f8: cmp             x4, x0, asr #1
    //     0x6469fc: b.eq            #0x646a08
    //     0x646a00: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x646a04: stur            x4, [x0, #7]
    // 0x646a08: mov             x7, x2
    // 0x646a0c: mov             x6, x3
    // 0x646a10: mov             x3, x0
    // 0x646a14: r5 = Null
    //     0x646a14: mov             x5, NULL
    // 0x646a18: r4 = Null
    //     0x646a18: mov             x4, NULL
    // 0x646a1c: r2 = 0
    //     0x646a1c: movz            x2, #0
    // 0x646a20: b               #0x646b3c
    // 0x646a24: sub             SP, fp, #0xe8
    // 0x646a28: mov             x3, x0
    // 0x646a2c: stur            x0, [fp, #-0x80]
    // 0x646a30: mov             x0, x1
    // 0x646a34: stur            x1, [fp, #-0x88]
    // 0x646a38: r1 = Null
    //     0x646a38: mov             x1, NULL
    // 0x646a3c: r2 = 4
    //     0x646a3c: movz            x2, #0x4
    // 0x646a40: r0 = AllocateArray()
    //     0x646a40: bl              #0xec22fc  ; AllocateArrayStub
    // 0x646a44: stur            x0, [fp, #-0x90]
    // 0x646a48: r16 = "while dispatching notifications for "
    //     0x646a48: ldr             x16, [PP, #0x2198]  ; [pp+0x2198] "while dispatching notifications for "
    // 0x646a4c: StoreField: r0->field_f = r16
    //     0x646a4c: stur            w16, [x0, #0xf]
    // 0x646a50: ldur            x16, [fp, #-0x78]
    // 0x646a54: str             x16, [SP]
    // 0x646a58: r0 = runtimeType()
    //     0x646a58: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0x646a5c: ldur            x1, [fp, #-0x90]
    // 0x646a60: ArrayStore: r1[1] = r0  ; List_4
    //     0x646a60: add             x25, x1, #0x13
    //     0x646a64: str             w0, [x25]
    //     0x646a68: tbz             w0, #0, #0x646a84
    //     0x646a6c: ldurb           w16, [x1, #-1]
    //     0x646a70: ldurb           w17, [x0, #-1]
    //     0x646a74: and             x16, x17, x16, lsr #2
    //     0x646a78: tst             x16, HEAP, lsr #32
    //     0x646a7c: b.eq            #0x646a84
    //     0x646a80: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x646a84: ldur            x16, [fp, #-0x90]
    // 0x646a88: str             x16, [SP]
    // 0x646a8c: r0 = _interpolate()
    //     0x646a8c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x646a90: r1 = <List<Object>>
    //     0x646a90: ldr             x1, [PP, #0x440]  ; [pp+0x440] TypeArguments: <List<Object>>
    // 0x646a94: stur            x0, [fp, #-0x90]
    // 0x646a98: r0 = ErrorDescription()
    //     0x646a98: bl              #0x64483c  ; AllocateErrorDescriptionStub -> ErrorDescription (size=0x2c)
    // 0x646a9c: mov             x1, x0
    // 0x646aa0: ldur            x2, [fp, #-0x90]
    // 0x646aa4: r3 = Instance_DiagnosticLevel
    //     0x646aa4: ldr             x3, [PP, #0x450]  ; [pp+0x450] Obj!DiagnosticLevel@e37021
    // 0x646aa8: stur            x0, [fp, #-0x90]
    // 0x646aac: r0 = _ErrorDiagnostic()
    //     0x646aac: bl              #0x644784  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::_ErrorDiagnostic
    // 0x646ab0: r0 = FlutterErrorDetails()
    //     0x646ab0: bl              #0x644b40  ; AllocateFlutterErrorDetailsStub -> FlutterErrorDetails (size=0x1c)
    // 0x646ab4: mov             x3, x0
    // 0x646ab8: ldur            x0, [fp, #-0x80]
    // 0x646abc: stur            x3, [fp, #-0x98]
    // 0x646ac0: StoreField: r3->field_7 = r0
    //     0x646ac0: stur            w0, [x3, #7]
    // 0x646ac4: ldur            x4, [fp, #-0x88]
    // 0x646ac8: StoreField: r3->field_b = r4
    //     0x646ac8: stur            w4, [x3, #0xb]
    // 0x646acc: ldur            x1, [fp, #-0x90]
    // 0x646ad0: StoreField: r3->field_f = r1
    //     0x646ad0: stur            w1, [x3, #0xf]
    // 0x646ad4: ldur            x2, [fp, #-0x10]
    // 0x646ad8: r1 = Function '<anonymous closure>':.
    //     0x646ad8: ldr             x1, [PP, #0x2368]  ; [pp+0x2368] AnonymousClosure: (0x646880), in [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::notifyListeners (0x6469a0)
    // 0x646adc: r0 = AllocateClosure()
    //     0x646adc: bl              #0xec1630  ; AllocateClosureStub
    // 0x646ae0: ldur            x1, [fp, #-0x98]
    // 0x646ae4: StoreField: r1->field_13 = r0
    //     0x646ae4: stur            w0, [x1, #0x13]
    // 0x646ae8: r0 = false
    //     0x646ae8: add             x0, NULL, #0x30  ; false
    // 0x646aec: ArrayStore: r1[0] = r0  ; List_4
    //     0x646aec: stur            w0, [x1, #0x17]
    // 0x646af0: r0 = reportError()
    //     0x646af0: bl              #0x63f6cc  ; [package:flutter/src/foundation/assertions.dart] FlutterError::reportError
    // 0x646af4: ldur            x3, [fp, #-0x78]
    // 0x646af8: ldur            x2, [fp, #-0x10]
    // 0x646afc: ldur            x1, [fp, #-0x38]
    // 0x646b00: ldur            x0, [fp, #-0x40]
    // 0x646b04: mov             x5, x3
    // 0x646b08: mov             x4, x2
    // 0x646b0c: ldur            x3, [fp, #-0x80]
    // 0x646b10: ldur            x2, [fp, #-0x88]
    // 0x646b14: r6 = LoadInt32Instr(r0)
    //     0x646b14: sbfx            x6, x0, #1, #0x1f
    //     0x646b18: tbz             w0, #0, #0x646b20
    //     0x646b1c: ldur            x6, [x0, #7]
    // 0x646b20: add             x0, x6, #1
    // 0x646b24: mov             x7, x5
    // 0x646b28: mov             x6, x4
    // 0x646b2c: mov             x5, x3
    // 0x646b30: mov             x4, x2
    // 0x646b34: mov             x3, x1
    // 0x646b38: mov             x2, x0
    // 0x646b3c: stur            x7, [fp, #-0x90]
    // 0x646b40: stur            x6, [fp, #-0x98]
    // 0x646b44: stur            x5, [fp, #-0xa0]
    // 0x646b48: stur            x4, [fp, #-0xa8]
    // 0x646b4c: stur            x3, [fp, #-0xb0]
    // 0x646b50: stur            x2, [fp, #-0xb8]
    // 0x646b54: CheckStackOverflow
    //     0x646b54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x646b58: cmp             SP, x16
    //     0x646b5c: b.ls            #0x646efc
    // 0x646b60: r0 = LoadInt32Instr(r3)
    //     0x646b60: sbfx            x0, x3, #1, #0x1f
    //     0x646b64: tbz             w3, #0, #0x646b6c
    //     0x646b68: ldur            x0, [x3, #7]
    // 0x646b6c: cmp             x2, x0
    // 0x646b70: b.ge            #0x646bf8
    // 0x646b74: LoadField: r8 = r7->field_f
    //     0x646b74: ldur            w8, [x7, #0xf]
    // 0x646b78: DecompressPointer r8
    //     0x646b78: add             x8, x8, HEAP, lsl #32
    // 0x646b7c: LoadField: r0 = r8->field_b
    //     0x646b7c: ldur            w0, [x8, #0xb]
    // 0x646b80: r1 = LoadInt32Instr(r0)
    //     0x646b80: sbfx            x1, x0, #1, #0x1f
    // 0x646b84: mov             x0, x1
    // 0x646b88: mov             x1, x2
    // 0x646b8c: cmp             x1, x0
    // 0x646b90: b.hs            #0x646f04
    // 0x646b94: r0 = BoxInt64Instr(r2)
    //     0x646b94: sbfiz           x0, x2, #1, #0x1f
    //     0x646b98: cmp             x2, x0, asr #1
    //     0x646b9c: b.eq            #0x646ba8
    //     0x646ba0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x646ba4: stur            x2, [x0, #7]
    // 0x646ba8: mov             x1, x0
    // 0x646bac: stur            x1, [fp, #-0x88]
    // 0x646bb0: ArrayLoad: r9 = r8[r2]  ; Unknown_4
    //     0x646bb0: add             x16, x8, x2, lsl #2
    //     0x646bb4: ldur            w9, [x16, #0xf]
    // 0x646bb8: DecompressPointer r9
    //     0x646bb8: add             x9, x9, HEAP, lsl #32
    // 0x646bbc: stur            x9, [fp, #-0x80]
    // 0x646bc0: cmp             w9, NULL
    // 0x646bc4: b.eq            #0x646bdc
    // 0x646bc8: str             x9, [SP]
    // 0x646bcc: mov             x0, x9
    // 0x646bd0: ClosureCall
    //     0x646bd0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x646bd4: ldur            x2, [x0, #0x1f]
    //     0x646bd8: blr             x2
    // 0x646bdc: ldur            x5, [fp, #-0x90]
    // 0x646be0: ldur            x4, [fp, #-0x98]
    // 0x646be4: ldur            x3, [fp, #-0xa0]
    // 0x646be8: ldur            x2, [fp, #-0xa8]
    // 0x646bec: ldur            x1, [fp, #-0xb0]
    // 0x646bf0: ldur            x0, [fp, #-0x88]
    // 0x646bf4: b               #0x646b14
    // 0x646bf8: mov             x3, x7
    // 0x646bfc: LoadField: r0 = r3->field_13
    //     0x646bfc: ldur            x0, [x3, #0x13]
    // 0x646c00: sub             x1, x0, #1
    // 0x646c04: StoreField: r3->field_13 = r1
    //     0x646c04: stur            x1, [x3, #0x13]
    // 0x646c08: cbnz            x1, #0x646ee4
    // 0x646c0c: LoadField: r0 = r3->field_1b
    //     0x646c0c: ldur            x0, [x3, #0x1b]
    // 0x646c10: cmp             x0, #0
    // 0x646c14: b.le            #0x646ee4
    // 0x646c18: LoadField: r4 = r3->field_7
    //     0x646c18: ldur            x4, [x3, #7]
    // 0x646c1c: stur            x4, [fp, #-0xc8]
    // 0x646c20: sub             x5, x4, x0
    // 0x646c24: stur            x5, [fp, #-0xc0]
    // 0x646c28: lsl             x0, x5, #1
    // 0x646c2c: LoadField: r6 = r3->field_f
    //     0x646c2c: ldur            w6, [x3, #0xf]
    // 0x646c30: DecompressPointer r6
    //     0x646c30: add             x6, x6, HEAP, lsl #32
    // 0x646c34: stur            x6, [fp, #-0x80]
    // 0x646c38: LoadField: r1 = r6->field_b
    //     0x646c38: ldur            w1, [x6, #0xb]
    // 0x646c3c: r7 = LoadInt32Instr(r1)
    //     0x646c3c: sbfx            x7, x1, #1, #0x1f
    // 0x646c40: stur            x7, [fp, #-0xb8]
    // 0x646c44: cmp             x0, x7
    // 0x646c48: b.gt            #0x646d78
    // 0x646c4c: r0 = BoxInt64Instr(r5)
    //     0x646c4c: sbfiz           x0, x5, #1, #0x1f
    //     0x646c50: cmp             x5, x0, asr #1
    //     0x646c54: b.eq            #0x646c60
    //     0x646c58: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x646c5c: stur            x5, [x0, #7]
    // 0x646c60: mov             x2, x0
    // 0x646c64: r1 = <((dynamic this) => void?)?>
    //     0x646c64: ldr             x1, [PP, #0x1d48]  ; [pp+0x1d48] TypeArguments: <((dynamic this) => void?)?>
    // 0x646c68: r0 = AllocateArray()
    //     0x646c68: bl              #0xec22fc  ; AllocateArrayStub
    // 0x646c6c: mov             x3, x0
    // 0x646c70: stur            x3, [fp, #-0x98]
    // 0x646c74: r7 = 0
    //     0x646c74: movz            x7, #0
    // 0x646c78: r6 = 0
    //     0x646c78: movz            x6, #0
    // 0x646c7c: ldur            x4, [fp, #-0xc8]
    // 0x646c80: ldur            x5, [fp, #-0x80]
    // 0x646c84: stur            x7, [fp, #-0xd8]
    // 0x646c88: stur            x6, [fp, #-0xe0]
    // 0x646c8c: CheckStackOverflow
    //     0x646c8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x646c90: cmp             SP, x16
    //     0x646c94: b.ls            #0x646f08
    // 0x646c98: cmp             x6, x4
    // 0x646c9c: b.ge            #0x646d4c
    // 0x646ca0: ldur            x0, [fp, #-0xb8]
    // 0x646ca4: mov             x1, x6
    // 0x646ca8: cmp             x1, x0
    // 0x646cac: b.hs            #0x646f10
    // 0x646cb0: ArrayLoad: r8 = r5[r6]  ; Unknown_4
    //     0x646cb0: add             x16, x5, x6, lsl #2
    //     0x646cb4: ldur            w8, [x16, #0xf]
    // 0x646cb8: DecompressPointer r8
    //     0x646cb8: add             x8, x8, HEAP, lsl #32
    // 0x646cbc: stur            x8, [fp, #-0x88]
    // 0x646cc0: cmp             w8, NULL
    // 0x646cc4: b.eq            #0x646d34
    // 0x646cc8: add             x9, x7, #1
    // 0x646ccc: mov             x0, x8
    // 0x646cd0: stur            x9, [fp, #-0xd0]
    // 0x646cd4: r2 = Null
    //     0x646cd4: mov             x2, NULL
    // 0x646cd8: r1 = Null
    //     0x646cd8: mov             x1, NULL
    // 0x646cdc: r8 = ((dynamic this) => void?)?
    //     0x646cdc: ldr             x8, [PP, #0x2370]  ; [pp+0x2370] FunctionType: ((dynamic this) => void?)?
    // 0x646ce0: r3 = Null
    //     0x646ce0: ldr             x3, [PP, #0x2378]  ; [pp+0x2378] Null
    // 0x646ce4: r0 = DefaultNullableTypeTest()
    //     0x646ce4: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x646ce8: ldur            x0, [fp, #-0xc0]
    // 0x646cec: ldur            x1, [fp, #-0xd8]
    // 0x646cf0: cmp             x1, x0
    // 0x646cf4: b.hs            #0x646f14
    // 0x646cf8: ldur            x1, [fp, #-0x98]
    // 0x646cfc: ldur            x0, [fp, #-0x88]
    // 0x646d00: ldur            x2, [fp, #-0xd8]
    // 0x646d04: ArrayStore: r1[r2] = r0  ; List_4
    //     0x646d04: add             x25, x1, x2, lsl #2
    //     0x646d08: add             x25, x25, #0xf
    //     0x646d0c: str             w0, [x25]
    //     0x646d10: tbz             w0, #0, #0x646d2c
    //     0x646d14: ldurb           w16, [x1, #-1]
    //     0x646d18: ldurb           w17, [x0, #-1]
    //     0x646d1c: and             x16, x17, x16, lsr #2
    //     0x646d20: tst             x16, HEAP, lsr #32
    //     0x646d24: b.eq            #0x646d2c
    //     0x646d28: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x646d2c: ldur            x7, [fp, #-0xd0]
    // 0x646d30: b               #0x646d3c
    // 0x646d34: mov             x2, x7
    // 0x646d38: mov             x7, x2
    // 0x646d3c: ldur            x0, [fp, #-0xe0]
    // 0x646d40: add             x6, x0, #1
    // 0x646d44: ldur            x3, [fp, #-0x98]
    // 0x646d48: b               #0x646c7c
    // 0x646d4c: ldur            x3, [fp, #-0x90]
    // 0x646d50: ldur            x0, [fp, #-0x98]
    // 0x646d54: StoreField: r3->field_f = r0
    //     0x646d54: stur            w0, [x3, #0xf]
    //     0x646d58: ldurb           w16, [x3, #-1]
    //     0x646d5c: ldurb           w17, [x0, #-1]
    //     0x646d60: and             x16, x17, x16, lsr #2
    //     0x646d64: tst             x16, HEAP, lsr #32
    //     0x646d68: b.eq            #0x646d70
    //     0x646d6c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x646d70: mov             x1, x3
    // 0x646d74: b               #0x646ed8
    // 0x646d78: mov             x4, x6
    // 0x646d7c: LoadField: r5 = r4->field_7
    //     0x646d7c: ldur            w5, [x4, #7]
    // 0x646d80: DecompressPointer r5
    //     0x646d80: add             x5, x5, HEAP, lsl #32
    // 0x646d84: stur            x5, [fp, #-0x98]
    // 0x646d88: r7 = 0
    //     0x646d88: movz            x7, #0
    // 0x646d8c: ldur            x6, [fp, #-0xc0]
    // 0x646d90: stur            x7, [fp, #-0xd0]
    // 0x646d94: CheckStackOverflow
    //     0x646d94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x646d98: cmp             SP, x16
    //     0x646d9c: b.ls            #0x646f18
    // 0x646da0: cmp             x7, x6
    // 0x646da4: b.ge            #0x646ed4
    // 0x646da8: ldur            x0, [fp, #-0xb8]
    // 0x646dac: mov             x1, x7
    // 0x646db0: cmp             x1, x0
    // 0x646db4: b.hs            #0x646f20
    // 0x646db8: ArrayLoad: r0 = r4[r7]  ; Unknown_4
    //     0x646db8: add             x16, x4, x7, lsl #2
    //     0x646dbc: ldur            w0, [x16, #0xf]
    // 0x646dc0: DecompressPointer r0
    //     0x646dc0: add             x0, x0, HEAP, lsl #32
    // 0x646dc4: cmp             w0, NULL
    // 0x646dc8: b.ne            #0x646eb8
    // 0x646dcc: add             x0, x7, #1
    // 0x646dd0: mov             x8, x0
    // 0x646dd4: stur            x8, [fp, #-0xc8]
    // 0x646dd8: CheckStackOverflow
    //     0x646dd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x646ddc: cmp             SP, x16
    //     0x646de0: b.ls            #0x646f24
    // 0x646de4: ldur            x0, [fp, #-0xb8]
    // 0x646de8: mov             x1, x8
    // 0x646dec: cmp             x1, x0
    // 0x646df0: b.hs            #0x646f2c
    // 0x646df4: ArrayLoad: r9 = r4[r8]  ; Unknown_4
    //     0x646df4: add             x16, x4, x8, lsl #2
    //     0x646df8: ldur            w9, [x16, #0xf]
    // 0x646dfc: DecompressPointer r9
    //     0x646dfc: add             x9, x9, HEAP, lsl #32
    // 0x646e00: stur            x9, [fp, #-0x88]
    // 0x646e04: cmp             w9, NULL
    // 0x646e08: b.ne            #0x646e18
    // 0x646e0c: add             x0, x8, #1
    // 0x646e10: mov             x8, x0
    // 0x646e14: b               #0x646dd4
    // 0x646e18: mov             x0, x9
    // 0x646e1c: mov             x2, x5
    // 0x646e20: r1 = Null
    //     0x646e20: mov             x1, NULL
    // 0x646e24: cmp             w2, NULL
    // 0x646e28: b.eq            #0x646e44
    // 0x646e2c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x646e2c: ldur            w4, [x2, #0x17]
    // 0x646e30: DecompressPointer r4
    //     0x646e30: add             x4, x4, HEAP, lsl #32
    // 0x646e34: r8 = X0
    //     0x646e34: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x646e38: LoadField: r9 = r4->field_7
    //     0x646e38: ldur            x9, [x4, #7]
    // 0x646e3c: r3 = Null
    //     0x646e3c: ldr             x3, [PP, #0x2388]  ; [pp+0x2388] Null
    // 0x646e40: blr             x9
    // 0x646e44: ldur            x1, [fp, #-0x80]
    // 0x646e48: ldur            x0, [fp, #-0x88]
    // 0x646e4c: ldur            x3, [fp, #-0xd0]
    // 0x646e50: ArrayStore: r1[r3] = r0  ; List_4
    //     0x646e50: add             x25, x1, x3, lsl #2
    //     0x646e54: add             x25, x25, #0xf
    //     0x646e58: str             w0, [x25]
    //     0x646e5c: tbz             w0, #0, #0x646e78
    //     0x646e60: ldurb           w16, [x1, #-1]
    //     0x646e64: ldurb           w17, [x0, #-1]
    //     0x646e68: and             x16, x17, x16, lsr #2
    //     0x646e6c: tst             x16, HEAP, lsr #32
    //     0x646e70: b.eq            #0x646e78
    //     0x646e74: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x646e78: ldur            x2, [fp, #-0x98]
    // 0x646e7c: r0 = Null
    //     0x646e7c: mov             x0, NULL
    // 0x646e80: r1 = Null
    //     0x646e80: mov             x1, NULL
    // 0x646e84: cmp             w2, NULL
    // 0x646e88: b.eq            #0x646ea4
    // 0x646e8c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x646e8c: ldur            w4, [x2, #0x17]
    // 0x646e90: DecompressPointer r4
    //     0x646e90: add             x4, x4, HEAP, lsl #32
    // 0x646e94: r8 = X0
    //     0x646e94: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x646e98: LoadField: r9 = r4->field_7
    //     0x646e98: ldur            x9, [x4, #7]
    // 0x646e9c: r3 = Null
    //     0x646e9c: ldr             x3, [PP, #0x2398]  ; [pp+0x2398] Null
    // 0x646ea0: blr             x9
    // 0x646ea4: ldur            x1, [fp, #-0x80]
    // 0x646ea8: ldur            x2, [fp, #-0xc8]
    // 0x646eac: ArrayStore: r1[r2] = rNULL  ; Unknown_4
    //     0x646eac: add             x3, x1, x2, lsl #2
    //     0x646eb0: stur            NULL, [x3, #0xf]
    // 0x646eb4: b               #0x646ebc
    // 0x646eb8: mov             x1, x4
    // 0x646ebc: ldur            x2, [fp, #-0xd0]
    // 0x646ec0: add             x7, x2, #1
    // 0x646ec4: ldur            x3, [fp, #-0x90]
    // 0x646ec8: mov             x4, x1
    // 0x646ecc: ldur            x5, [fp, #-0x98]
    // 0x646ed0: b               #0x646d8c
    // 0x646ed4: ldur            x1, [fp, #-0x90]
    // 0x646ed8: ldur            x2, [fp, #-0xc0]
    // 0x646edc: StoreField: r1->field_1b = rZR
    //     0x646edc: stur            xzr, [x1, #0x1b]
    // 0x646ee0: StoreField: r1->field_7 = r2
    //     0x646ee0: stur            x2, [x1, #7]
    // 0x646ee4: r0 = Null
    //     0x646ee4: mov             x0, NULL
    // 0x646ee8: LeaveFrame
    //     0x646ee8: mov             SP, fp
    //     0x646eec: ldp             fp, lr, [SP], #0x10
    // 0x646ef0: ret
    //     0x646ef0: ret             
    // 0x646ef4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x646ef4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x646ef8: b               #0x6469bc
    // 0x646efc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x646efc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x646f00: b               #0x646b60
    // 0x646f04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x646f04: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x646f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x646f08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x646f0c: b               #0x646c98
    // 0x646f10: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x646f10: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x646f14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x646f14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x646f18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x646f18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x646f1c: b               #0x646da0
    // 0x646f20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x646f20: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x646f24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x646f24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x646f28: b               #0x646de4
    // 0x646f2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x646f2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ addListener(/* No info */) {
    // ** addr: 0xa860ec, size: 0x20c
    // 0xa860ec: EnterFrame
    //     0xa860ec: stp             fp, lr, [SP, #-0x10]!
    //     0xa860f0: mov             fp, SP
    // 0xa860f4: AllocStack(0x30)
    //     0xa860f4: sub             SP, SP, #0x30
    // 0xa860f8: SetupParameters(_FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xa860f8: mov             x3, x1
    //     0xa860fc: mov             x0, x2
    //     0xa86100: stur            x1, [fp, #-0x10]
    //     0xa86104: stur            x2, [fp, #-0x18]
    // 0xa86108: LoadField: r4 = r3->field_7
    //     0xa86108: ldur            x4, [x3, #7]
    // 0xa8610c: stur            x4, [fp, #-8]
    // 0xa86110: LoadField: r5 = r3->field_f
    //     0xa86110: ldur            w5, [x3, #0xf]
    // 0xa86114: DecompressPointer r5
    //     0xa86114: add             x5, x5, HEAP, lsl #32
    // 0xa86118: stur            x5, [fp, #-0x30]
    // 0xa8611c: LoadField: r1 = r5->field_b
    //     0xa8611c: ldur            w1, [x5, #0xb]
    // 0xa86120: r6 = LoadInt32Instr(r1)
    //     0xa86120: sbfx            x6, x1, #1, #0x1f
    // 0xa86124: stur            x6, [fp, #-0x28]
    // 0xa86128: cmp             x4, x6
    // 0xa8612c: b.ne            #0xa86244
    // 0xa86130: cbnz            x4, #0xa86174
    // 0xa86134: r1 = <((dynamic this) => void?)?>
    //     0xa86134: ldr             x1, [PP, #0x1d48]  ; [pp+0x1d48] TypeArguments: <((dynamic this) => void?)?>
    // 0xa86138: r2 = 2
    //     0xa86138: movz            x2, #0x2
    // 0xa8613c: r0 = AllocateArray()
    //     0xa8613c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa86140: mov             x1, x0
    // 0xa86144: ldur            x3, [fp, #-0x10]
    // 0xa86148: StoreField: r3->field_f = r0
    //     0xa86148: stur            w0, [x3, #0xf]
    //     0xa8614c: ldurb           w16, [x3, #-1]
    //     0xa86150: ldurb           w17, [x0, #-1]
    //     0xa86154: and             x16, x17, x16, lsr #2
    //     0xa86158: tst             x16, HEAP, lsr #32
    //     0xa8615c: b.eq            #0xa86164
    //     0xa86160: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa86164: mov             x0, x1
    // 0xa86168: mov             x1, x3
    // 0xa8616c: ldur            x4, [fp, #-8]
    // 0xa86170: b               #0xa8623c
    // 0xa86174: lsl             x0, x6, #1
    // 0xa86178: stur            x0, [fp, #-0x20]
    // 0xa8617c: lsl             x2, x0, #1
    // 0xa86180: r1 = <((dynamic this) => void?)?>
    //     0xa86180: ldr             x1, [PP, #0x1d48]  ; [pp+0x1d48] TypeArguments: <((dynamic this) => void?)?>
    // 0xa86184: r0 = AllocateArray()
    //     0xa86184: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa86188: mov             x2, x0
    // 0xa8618c: ldur            x4, [fp, #-8]
    // 0xa86190: ldur            x3, [fp, #-0x30]
    // 0xa86194: r5 = 0
    //     0xa86194: movz            x5, #0
    // 0xa86198: CheckStackOverflow
    //     0xa86198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8619c: cmp             SP, x16
    //     0xa861a0: b.ls            #0xa862e4
    // 0xa861a4: cmp             x5, x4
    // 0xa861a8: b.ge            #0xa86214
    // 0xa861ac: ldur            x0, [fp, #-0x28]
    // 0xa861b0: mov             x1, x5
    // 0xa861b4: cmp             x1, x0
    // 0xa861b8: b.hs            #0xa862ec
    // 0xa861bc: ArrayLoad: r6 = r3[r5]  ; Unknown_4
    //     0xa861bc: add             x16, x3, x5, lsl #2
    //     0xa861c0: ldur            w6, [x16, #0xf]
    // 0xa861c4: DecompressPointer r6
    //     0xa861c4: add             x6, x6, HEAP, lsl #32
    // 0xa861c8: ldur            x0, [fp, #-0x20]
    // 0xa861cc: mov             x1, x5
    // 0xa861d0: cmp             x1, x0
    // 0xa861d4: b.hs            #0xa862f0
    // 0xa861d8: mov             x1, x2
    // 0xa861dc: mov             x0, x6
    // 0xa861e0: ArrayStore: r1[r5] = r0  ; List_4
    //     0xa861e0: add             x25, x1, x5, lsl #2
    //     0xa861e4: add             x25, x25, #0xf
    //     0xa861e8: str             w0, [x25]
    //     0xa861ec: tbz             w0, #0, #0xa86208
    //     0xa861f0: ldurb           w16, [x1, #-1]
    //     0xa861f4: ldurb           w17, [x0, #-1]
    //     0xa861f8: and             x16, x17, x16, lsr #2
    //     0xa861fc: tst             x16, HEAP, lsr #32
    //     0xa86200: b.eq            #0xa86208
    //     0xa86204: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa86208: add             x0, x5, #1
    // 0xa8620c: mov             x5, x0
    // 0xa86210: b               #0xa86198
    // 0xa86214: ldur            x1, [fp, #-0x10]
    // 0xa86218: mov             x0, x2
    // 0xa8621c: StoreField: r1->field_f = r0
    //     0xa8621c: stur            w0, [x1, #0xf]
    //     0xa86220: ldurb           w16, [x1, #-1]
    //     0xa86224: ldurb           w17, [x0, #-1]
    //     0xa86228: and             x16, x17, x16, lsr #2
    //     0xa8622c: tst             x16, HEAP, lsr #32
    //     0xa86230: b.eq            #0xa86238
    //     0xa86234: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa86238: mov             x0, x2
    // 0xa8623c: mov             x3, x0
    // 0xa86240: b               #0xa8624c
    // 0xa86244: mov             x1, x3
    // 0xa86248: mov             x3, x5
    // 0xa8624c: stur            x3, [fp, #-0x30]
    // 0xa86250: add             x0, x4, #1
    // 0xa86254: StoreField: r1->field_7 = r0
    //     0xa86254: stur            x0, [x1, #7]
    // 0xa86258: LoadField: r2 = r3->field_7
    //     0xa86258: ldur            w2, [x3, #7]
    // 0xa8625c: DecompressPointer r2
    //     0xa8625c: add             x2, x2, HEAP, lsl #32
    // 0xa86260: ldur            x0, [fp, #-0x18]
    // 0xa86264: r1 = Null
    //     0xa86264: mov             x1, NULL
    // 0xa86268: cmp             w2, NULL
    // 0xa8626c: b.eq            #0xa86288
    // 0xa86270: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa86270: ldur            w4, [x2, #0x17]
    // 0xa86274: DecompressPointer r4
    //     0xa86274: add             x4, x4, HEAP, lsl #32
    // 0xa86278: r8 = X0
    //     0xa86278: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xa8627c: LoadField: r9 = r4->field_7
    //     0xa8627c: ldur            x9, [x4, #7]
    // 0xa86280: r3 = Null
    //     0xa86280: ldr             x3, [PP, #0x6dd0]  ; [pp+0x6dd0] Null
    // 0xa86284: blr             x9
    // 0xa86288: ldur            x2, [fp, #-0x30]
    // 0xa8628c: LoadField: r3 = r2->field_b
    //     0xa8628c: ldur            w3, [x2, #0xb]
    // 0xa86290: r0 = LoadInt32Instr(r3)
    //     0xa86290: sbfx            x0, x3, #1, #0x1f
    // 0xa86294: ldur            x1, [fp, #-8]
    // 0xa86298: cmp             x1, x0
    // 0xa8629c: b.hs            #0xa862f4
    // 0xa862a0: mov             x1, x2
    // 0xa862a4: ldur            x0, [fp, #-0x18]
    // 0xa862a8: ldur            x2, [fp, #-8]
    // 0xa862ac: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa862ac: add             x25, x1, x2, lsl #2
    //     0xa862b0: add             x25, x25, #0xf
    //     0xa862b4: str             w0, [x25]
    //     0xa862b8: tbz             w0, #0, #0xa862d4
    //     0xa862bc: ldurb           w16, [x1, #-1]
    //     0xa862c0: ldurb           w17, [x0, #-1]
    //     0xa862c4: and             x16, x17, x16, lsr #2
    //     0xa862c8: tst             x16, HEAP, lsr #32
    //     0xa862cc: b.eq            #0xa862d4
    //     0xa862d0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa862d4: r0 = Null
    //     0xa862d4: mov             x0, NULL
    // 0xa862d8: LeaveFrame
    //     0xa862d8: mov             SP, fp
    //     0xa862dc: ldp             fp, lr, [SP], #0x10
    // 0xa862e0: ret
    //     0xa862e0: ret             
    // 0xa862e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa862e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa862e8: b               #0xa861a4
    // 0xa862ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa862ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa862f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa862f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa862f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa862f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ removeListener(/* No info */) {
    // ** addr: 0xa8e344, size: 0x168
    // 0xa8e344: EnterFrame
    //     0xa8e344: stp             fp, lr, [SP, #-0x10]!
    //     0xa8e348: mov             fp, SP
    // 0xa8e34c: AllocStack(0x28)
    //     0xa8e34c: sub             SP, SP, #0x28
    // 0xa8e350: SetupParameters(_FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xa8e350: mov             x3, x1
    //     0xa8e354: stur            x1, [fp, #-0x10]
    //     0xa8e358: stur            x2, [fp, #-0x18]
    // 0xa8e35c: CheckStackOverflow
    //     0xa8e35c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8e360: cmp             SP, x16
    //     0xa8e364: b.ls            #0xa8e494
    // 0xa8e368: r4 = 0
    //     0xa8e368: movz            x4, #0
    // 0xa8e36c: stur            x4, [fp, #-8]
    // 0xa8e370: CheckStackOverflow
    //     0xa8e370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8e374: cmp             SP, x16
    //     0xa8e378: b.ls            #0xa8e49c
    // 0xa8e37c: LoadField: r0 = r3->field_7
    //     0xa8e37c: ldur            x0, [x3, #7]
    // 0xa8e380: cmp             x4, x0
    // 0xa8e384: b.ge            #0xa8e484
    // 0xa8e388: LoadField: r5 = r3->field_f
    //     0xa8e388: ldur            w5, [x3, #0xf]
    // 0xa8e38c: DecompressPointer r5
    //     0xa8e38c: add             x5, x5, HEAP, lsl #32
    // 0xa8e390: LoadField: r0 = r5->field_b
    //     0xa8e390: ldur            w0, [x5, #0xb]
    // 0xa8e394: r1 = LoadInt32Instr(r0)
    //     0xa8e394: sbfx            x1, x0, #1, #0x1f
    // 0xa8e398: mov             x0, x1
    // 0xa8e39c: mov             x1, x4
    // 0xa8e3a0: cmp             x1, x0
    // 0xa8e3a4: b.hs            #0xa8e4a4
    // 0xa8e3a8: ArrayLoad: r0 = r5[r4]  ; Unknown_4
    //     0xa8e3a8: add             x16, x5, x4, lsl #2
    //     0xa8e3ac: ldur            w0, [x16, #0xf]
    // 0xa8e3b0: DecompressPointer r0
    //     0xa8e3b0: add             x0, x0, HEAP, lsl #32
    // 0xa8e3b4: r1 = LoadClassIdInstr(r0)
    //     0xa8e3b4: ldur            x1, [x0, #-1]
    //     0xa8e3b8: ubfx            x1, x1, #0xc, #0x14
    // 0xa8e3bc: stp             x2, x0, [SP]
    // 0xa8e3c0: mov             x0, x1
    // 0xa8e3c4: mov             lr, x0
    // 0xa8e3c8: ldr             lr, [x21, lr, lsl #3]
    // 0xa8e3cc: blr             lr
    // 0xa8e3d0: tbz             w0, #4, #0xa8e3e8
    // 0xa8e3d4: ldur            x3, [fp, #-8]
    // 0xa8e3d8: add             x4, x3, #1
    // 0xa8e3dc: ldur            x3, [fp, #-0x10]
    // 0xa8e3e0: ldur            x2, [fp, #-0x18]
    // 0xa8e3e4: b               #0xa8e36c
    // 0xa8e3e8: ldur            x4, [fp, #-0x10]
    // 0xa8e3ec: ldur            x3, [fp, #-8]
    // 0xa8e3f0: LoadField: r0 = r4->field_13
    //     0xa8e3f0: ldur            x0, [x4, #0x13]
    // 0xa8e3f4: cmp             x0, #0
    // 0xa8e3f8: b.le            #0xa8e474
    // 0xa8e3fc: LoadField: r5 = r4->field_f
    //     0xa8e3fc: ldur            w5, [x4, #0xf]
    // 0xa8e400: DecompressPointer r5
    //     0xa8e400: add             x5, x5, HEAP, lsl #32
    // 0xa8e404: stur            x5, [fp, #-0x18]
    // 0xa8e408: LoadField: r2 = r5->field_7
    //     0xa8e408: ldur            w2, [x5, #7]
    // 0xa8e40c: DecompressPointer r2
    //     0xa8e40c: add             x2, x2, HEAP, lsl #32
    // 0xa8e410: r0 = Null
    //     0xa8e410: mov             x0, NULL
    // 0xa8e414: r1 = Null
    //     0xa8e414: mov             x1, NULL
    // 0xa8e418: cmp             w2, NULL
    // 0xa8e41c: b.eq            #0xa8e438
    // 0xa8e420: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa8e420: ldur            w4, [x2, #0x17]
    // 0xa8e424: DecompressPointer r4
    //     0xa8e424: add             x4, x4, HEAP, lsl #32
    // 0xa8e428: r8 = X0
    //     0xa8e428: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xa8e42c: LoadField: r9 = r4->field_7
    //     0xa8e42c: ldur            x9, [x4, #7]
    // 0xa8e430: r3 = Null
    //     0xa8e430: ldr             x3, [PP, #0x6da0]  ; [pp+0x6da0] Null
    // 0xa8e434: blr             x9
    // 0xa8e438: ldur            x2, [fp, #-0x18]
    // 0xa8e43c: LoadField: r0 = r2->field_b
    //     0xa8e43c: ldur            w0, [x2, #0xb]
    // 0xa8e440: r1 = LoadInt32Instr(r0)
    //     0xa8e440: sbfx            x1, x0, #1, #0x1f
    // 0xa8e444: mov             x0, x1
    // 0xa8e448: ldur            x1, [fp, #-8]
    // 0xa8e44c: cmp             x1, x0
    // 0xa8e450: b.hs            #0xa8e4a8
    // 0xa8e454: ldur            x0, [fp, #-8]
    // 0xa8e458: ArrayStore: r2[r0] = rNULL  ; Unknown_4
    //     0xa8e458: add             x1, x2, x0, lsl #2
    //     0xa8e45c: stur            NULL, [x1, #0xf]
    // 0xa8e460: ldur            x1, [fp, #-0x10]
    // 0xa8e464: LoadField: r0 = r1->field_1b
    //     0xa8e464: ldur            x0, [x1, #0x1b]
    // 0xa8e468: add             x2, x0, #1
    // 0xa8e46c: StoreField: r1->field_1b = r2
    //     0xa8e46c: stur            x2, [x1, #0x1b]
    // 0xa8e470: b               #0xa8e484
    // 0xa8e474: mov             x1, x4
    // 0xa8e478: mov             x0, x3
    // 0xa8e47c: mov             x2, x0
    // 0xa8e480: r0 = _removeAt()
    //     0xa8e480: bl              #0xa8e4ac  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::_removeAt
    // 0xa8e484: r0 = Null
    //     0xa8e484: mov             x0, NULL
    // 0xa8e488: LeaveFrame
    //     0xa8e488: mov             SP, fp
    //     0xa8e48c: ldp             fp, lr, [SP], #0x10
    // 0xa8e490: ret
    //     0xa8e490: ret             
    // 0xa8e494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8e494: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8e498: b               #0xa8e368
    // 0xa8e49c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8e49c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8e4a0: b               #0xa8e37c
    // 0xa8e4a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa8e4a4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa8e4a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa8e4a8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _removeAt(/* No info */) {
    // ** addr: 0xa8e4ac, size: 0x310
    // 0xa8e4ac: EnterFrame
    //     0xa8e4ac: stp             fp, lr, [SP, #-0x10]!
    //     0xa8e4b0: mov             fp, SP
    // 0xa8e4b4: AllocStack(0x38)
    //     0xa8e4b4: sub             SP, SP, #0x38
    // 0xa8e4b8: SetupParameters(_FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier this /* r1 => r4, fp-0x20 */, dynamic _ /* r2 => r3, fp-0x28 */)
    //     0xa8e4b8: mov             x4, x1
    //     0xa8e4bc: mov             x3, x2
    //     0xa8e4c0: stur            x1, [fp, #-0x20]
    //     0xa8e4c4: stur            x2, [fp, #-0x28]
    // 0xa8e4c8: LoadField: r0 = r4->field_7
    //     0xa8e4c8: ldur            x0, [x4, #7]
    // 0xa8e4cc: sub             x5, x0, #1
    // 0xa8e4d0: stur            x5, [fp, #-0x18]
    // 0xa8e4d4: StoreField: r4->field_7 = r5
    //     0xa8e4d4: stur            x5, [x4, #7]
    // 0xa8e4d8: lsl             x0, x5, #1
    // 0xa8e4dc: LoadField: r6 = r4->field_f
    //     0xa8e4dc: ldur            w6, [x4, #0xf]
    // 0xa8e4e0: DecompressPointer r6
    //     0xa8e4e0: add             x6, x6, HEAP, lsl #32
    // 0xa8e4e4: stur            x6, [fp, #-0x10]
    // 0xa8e4e8: LoadField: r1 = r6->field_b
    //     0xa8e4e8: ldur            w1, [x6, #0xb]
    // 0xa8e4ec: r7 = LoadInt32Instr(r1)
    //     0xa8e4ec: sbfx            x7, x1, #1, #0x1f
    // 0xa8e4f0: stur            x7, [fp, #-8]
    // 0xa8e4f4: cmp             x0, x7
    // 0xa8e4f8: b.gt            #0xa8e650
    // 0xa8e4fc: r0 = BoxInt64Instr(r5)
    //     0xa8e4fc: sbfiz           x0, x5, #1, #0x1f
    //     0xa8e500: cmp             x5, x0, asr #1
    //     0xa8e504: b.eq            #0xa8e510
    //     0xa8e508: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa8e50c: stur            x5, [x0, #7]
    // 0xa8e510: mov             x2, x0
    // 0xa8e514: r1 = <((dynamic this) => void?)?>
    //     0xa8e514: ldr             x1, [PP, #0x1d48]  ; [pp+0x1d48] TypeArguments: <((dynamic this) => void?)?>
    // 0xa8e518: r0 = AllocateArray()
    //     0xa8e518: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa8e51c: mov             x2, x0
    // 0xa8e520: ldur            x3, [fp, #-0x28]
    // 0xa8e524: ldur            x4, [fp, #-0x10]
    // 0xa8e528: r5 = 0
    //     0xa8e528: movz            x5, #0
    // 0xa8e52c: CheckStackOverflow
    //     0xa8e52c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8e530: cmp             SP, x16
    //     0xa8e534: b.ls            #0xa8e788
    // 0xa8e538: cmp             x5, x3
    // 0xa8e53c: b.ge            #0xa8e5a8
    // 0xa8e540: ldur            x0, [fp, #-8]
    // 0xa8e544: mov             x1, x5
    // 0xa8e548: cmp             x1, x0
    // 0xa8e54c: b.hs            #0xa8e790
    // 0xa8e550: ArrayLoad: r6 = r4[r5]  ; Unknown_4
    //     0xa8e550: add             x16, x4, x5, lsl #2
    //     0xa8e554: ldur            w6, [x16, #0xf]
    // 0xa8e558: DecompressPointer r6
    //     0xa8e558: add             x6, x6, HEAP, lsl #32
    // 0xa8e55c: ldur            x0, [fp, #-0x18]
    // 0xa8e560: mov             x1, x5
    // 0xa8e564: cmp             x1, x0
    // 0xa8e568: b.hs            #0xa8e794
    // 0xa8e56c: mov             x1, x2
    // 0xa8e570: mov             x0, x6
    // 0xa8e574: ArrayStore: r1[r5] = r0  ; List_4
    //     0xa8e574: add             x25, x1, x5, lsl #2
    //     0xa8e578: add             x25, x25, #0xf
    //     0xa8e57c: str             w0, [x25]
    //     0xa8e580: tbz             w0, #0, #0xa8e59c
    //     0xa8e584: ldurb           w16, [x1, #-1]
    //     0xa8e588: ldurb           w17, [x0, #-1]
    //     0xa8e58c: and             x16, x17, x16, lsr #2
    //     0xa8e590: tst             x16, HEAP, lsr #32
    //     0xa8e594: b.eq            #0xa8e59c
    //     0xa8e598: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa8e59c: add             x0, x5, #1
    // 0xa8e5a0: mov             x5, x0
    // 0xa8e5a4: b               #0xa8e52c
    // 0xa8e5a8: ldur            x5, [fp, #-0x18]
    // 0xa8e5ac: CheckStackOverflow
    //     0xa8e5ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8e5b0: cmp             SP, x16
    //     0xa8e5b4: b.ls            #0xa8e798
    // 0xa8e5b8: cmp             x3, x5
    // 0xa8e5bc: b.ge            #0xa8e628
    // 0xa8e5c0: add             x6, x3, #1
    // 0xa8e5c4: ldur            x0, [fp, #-8]
    // 0xa8e5c8: mov             x1, x6
    // 0xa8e5cc: cmp             x1, x0
    // 0xa8e5d0: b.hs            #0xa8e7a0
    // 0xa8e5d4: ArrayLoad: r7 = r4[r6]  ; Unknown_4
    //     0xa8e5d4: add             x16, x4, x6, lsl #2
    //     0xa8e5d8: ldur            w7, [x16, #0xf]
    // 0xa8e5dc: DecompressPointer r7
    //     0xa8e5dc: add             x7, x7, HEAP, lsl #32
    // 0xa8e5e0: mov             x0, x5
    // 0xa8e5e4: mov             x1, x3
    // 0xa8e5e8: cmp             x1, x0
    // 0xa8e5ec: b.hs            #0xa8e7a4
    // 0xa8e5f0: mov             x1, x2
    // 0xa8e5f4: mov             x0, x7
    // 0xa8e5f8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa8e5f8: add             x25, x1, x3, lsl #2
    //     0xa8e5fc: add             x25, x25, #0xf
    //     0xa8e600: str             w0, [x25]
    //     0xa8e604: tbz             w0, #0, #0xa8e620
    //     0xa8e608: ldurb           w16, [x1, #-1]
    //     0xa8e60c: ldurb           w17, [x0, #-1]
    //     0xa8e610: and             x16, x17, x16, lsr #2
    //     0xa8e614: tst             x16, HEAP, lsr #32
    //     0xa8e618: b.eq            #0xa8e620
    //     0xa8e61c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa8e620: mov             x3, x6
    // 0xa8e624: b               #0xa8e5ac
    // 0xa8e628: ldur            x1, [fp, #-0x20]
    // 0xa8e62c: mov             x0, x2
    // 0xa8e630: StoreField: r1->field_f = r0
    //     0xa8e630: stur            w0, [x1, #0xf]
    //     0xa8e634: ldurb           w16, [x1, #-1]
    //     0xa8e638: ldurb           w17, [x0, #-1]
    //     0xa8e63c: and             x16, x17, x16, lsr #2
    //     0xa8e640: tst             x16, HEAP, lsr #32
    //     0xa8e644: b.eq            #0xa8e64c
    //     0xa8e648: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa8e64c: b               #0xa8e778
    // 0xa8e650: mov             x4, x6
    // 0xa8e654: LoadField: r6 = r4->field_7
    //     0xa8e654: ldur            w6, [x4, #7]
    // 0xa8e658: DecompressPointer r6
    //     0xa8e658: add             x6, x6, HEAP, lsl #32
    // 0xa8e65c: stur            x6, [fp, #-0x38]
    // 0xa8e660: stur            x3, [fp, #-0x30]
    // 0xa8e664: CheckStackOverflow
    //     0xa8e664: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8e668: cmp             SP, x16
    //     0xa8e66c: b.ls            #0xa8e7a8
    // 0xa8e670: cmp             x3, x5
    // 0xa8e674: b.ge            #0xa8e724
    // 0xa8e678: add             x7, x3, #1
    // 0xa8e67c: ldur            x0, [fp, #-8]
    // 0xa8e680: mov             x1, x7
    // 0xa8e684: stur            x7, [fp, #-0x28]
    // 0xa8e688: cmp             x1, x0
    // 0xa8e68c: b.hs            #0xa8e7b0
    // 0xa8e690: ArrayLoad: r8 = r4[r7]  ; Unknown_4
    //     0xa8e690: add             x16, x4, x7, lsl #2
    //     0xa8e694: ldur            w8, [x16, #0xf]
    // 0xa8e698: DecompressPointer r8
    //     0xa8e698: add             x8, x8, HEAP, lsl #32
    // 0xa8e69c: mov             x0, x8
    // 0xa8e6a0: mov             x2, x6
    // 0xa8e6a4: stur            x8, [fp, #-0x20]
    // 0xa8e6a8: r1 = Null
    //     0xa8e6a8: mov             x1, NULL
    // 0xa8e6ac: cmp             w2, NULL
    // 0xa8e6b0: b.eq            #0xa8e6cc
    // 0xa8e6b4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa8e6b4: ldur            w4, [x2, #0x17]
    // 0xa8e6b8: DecompressPointer r4
    //     0xa8e6b8: add             x4, x4, HEAP, lsl #32
    // 0xa8e6bc: r8 = X0
    //     0xa8e6bc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xa8e6c0: LoadField: r9 = r4->field_7
    //     0xa8e6c0: ldur            x9, [x4, #7]
    // 0xa8e6c4: r3 = Null
    //     0xa8e6c4: ldr             x3, [PP, #0x6db0]  ; [pp+0x6db0] Null
    // 0xa8e6c8: blr             x9
    // 0xa8e6cc: ldur            x0, [fp, #-8]
    // 0xa8e6d0: ldur            x1, [fp, #-0x30]
    // 0xa8e6d4: cmp             x1, x0
    // 0xa8e6d8: b.hs            #0xa8e7b4
    // 0xa8e6dc: ldur            x1, [fp, #-0x10]
    // 0xa8e6e0: ldur            x0, [fp, #-0x20]
    // 0xa8e6e4: ldur            x2, [fp, #-0x30]
    // 0xa8e6e8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa8e6e8: add             x25, x1, x2, lsl #2
    //     0xa8e6ec: add             x25, x25, #0xf
    //     0xa8e6f0: str             w0, [x25]
    //     0xa8e6f4: tbz             w0, #0, #0xa8e710
    //     0xa8e6f8: ldurb           w16, [x1, #-1]
    //     0xa8e6fc: ldurb           w17, [x0, #-1]
    //     0xa8e700: and             x16, x17, x16, lsr #2
    //     0xa8e704: tst             x16, HEAP, lsr #32
    //     0xa8e708: b.eq            #0xa8e710
    //     0xa8e70c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa8e710: ldur            x3, [fp, #-0x28]
    // 0xa8e714: ldur            x5, [fp, #-0x18]
    // 0xa8e718: ldur            x4, [fp, #-0x10]
    // 0xa8e71c: ldur            x6, [fp, #-0x38]
    // 0xa8e720: b               #0xa8e660
    // 0xa8e724: mov             x3, x4
    // 0xa8e728: mov             x4, x5
    // 0xa8e72c: ldur            x2, [fp, #-0x38]
    // 0xa8e730: r0 = Null
    //     0xa8e730: mov             x0, NULL
    // 0xa8e734: r1 = Null
    //     0xa8e734: mov             x1, NULL
    // 0xa8e738: cmp             w2, NULL
    // 0xa8e73c: b.eq            #0xa8e758
    // 0xa8e740: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa8e740: ldur            w4, [x2, #0x17]
    // 0xa8e744: DecompressPointer r4
    //     0xa8e744: add             x4, x4, HEAP, lsl #32
    // 0xa8e748: r8 = X0
    //     0xa8e748: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xa8e74c: LoadField: r9 = r4->field_7
    //     0xa8e74c: ldur            x9, [x4, #7]
    // 0xa8e750: r3 = Null
    //     0xa8e750: ldr             x3, [PP, #0x6dc0]  ; [pp+0x6dc0] Null
    // 0xa8e754: blr             x9
    // 0xa8e758: ldur            x0, [fp, #-8]
    // 0xa8e75c: ldur            x1, [fp, #-0x18]
    // 0xa8e760: cmp             x1, x0
    // 0xa8e764: b.hs            #0xa8e7b8
    // 0xa8e768: ldur            x2, [fp, #-0x18]
    // 0xa8e76c: ldur            x1, [fp, #-0x10]
    // 0xa8e770: ArrayStore: r1[r2] = rNULL  ; Unknown_4
    //     0xa8e770: add             x3, x1, x2, lsl #2
    //     0xa8e774: stur            NULL, [x3, #0xf]
    // 0xa8e778: r0 = Null
    //     0xa8e778: mov             x0, NULL
    // 0xa8e77c: LeaveFrame
    //     0xa8e77c: mov             SP, fp
    //     0xa8e780: ldp             fp, lr, [SP], #0x10
    // 0xa8e784: ret
    //     0xa8e784: ret             
    // 0xa8e788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8e788: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8e78c: b               #0xa8e538
    // 0xa8e790: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa8e790: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa8e794: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa8e794: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa8e798: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8e798: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8e79c: b               #0xa8e5b8
    // 0xa8e7a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa8e7a0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa8e7a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa8e7a4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa8e7a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8e7a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8e7ac: b               #0xa8e670
    // 0xa8e7b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa8e7b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa8e7b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa8e7b4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa8e7b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa8e7b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 2918, size: 0x48, field offset: 0x24
class FocusManager extends _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier {

  [closure] void _appLifecycleChange(dynamic, AppLifecycleState) {
    // ** addr: 0x68fa7c, size: 0x3c
    // 0x68fa7c: EnterFrame
    //     0x68fa7c: stp             fp, lr, [SP, #-0x10]!
    //     0x68fa80: mov             fp, SP
    // 0x68fa84: ldr             x0, [fp, #0x18]
    // 0x68fa88: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x68fa88: ldur            w1, [x0, #0x17]
    // 0x68fa8c: DecompressPointer r1
    //     0x68fa8c: add             x1, x1, HEAP, lsl #32
    // 0x68fa90: CheckStackOverflow
    //     0x68fa90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68fa94: cmp             SP, x16
    //     0x68fa98: b.ls            #0x68fab0
    // 0x68fa9c: ldr             x2, [fp, #0x10]
    // 0x68faa0: r0 = _appLifecycleChange()
    //     0x68faa0: bl              #0x68fadc  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::_appLifecycleChange
    // 0x68faa4: LeaveFrame
    //     0x68faa4: mov             SP, fp
    //     0x68faa8: ldp             fp, lr, [SP], #0x10
    // 0x68faac: ret
    //     0x68faac: ret             
    // 0x68fab0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68fab0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68fab4: b               #0x68fa9c
  }
  _ _appLifecycleChange(/* No info */) {
    // ** addr: 0x68fadc, size: 0x124
    // 0x68fadc: EnterFrame
    //     0x68fadc: stp             fp, lr, [SP, #-0x10]!
    //     0x68fae0: mov             fp, SP
    // 0x68fae4: AllocStack(0x20)
    //     0x68fae4: sub             SP, SP, #0x20
    // 0x68fae8: SetupParameters(FocusManager this /* r1 => r1, fp-0x8 */)
    //     0x68fae8: stur            x1, [fp, #-8]
    // 0x68faec: CheckStackOverflow
    //     0x68faec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68faf0: cmp             SP, x16
    //     0x68faf4: b.ls            #0x68fbf8
    // 0x68faf8: r16 = Instance_AppLifecycleState
    //     0x68faf8: ldr             x16, [PP, #0x2350]  ; [pp+0x2350] Obj!AppLifecycleState@e39901
    // 0x68fafc: cmp             w2, w16
    // 0x68fb00: b.ne            #0x68fb68
    // 0x68fb04: LoadField: r0 = r1->field_2b
    //     0x68fb04: ldur            w0, [x1, #0x2b]
    // 0x68fb08: DecompressPointer r0
    //     0x68fb08: add             x0, x0, HEAP, lsl #32
    // 0x68fb0c: LoadField: r2 = r1->field_27
    //     0x68fb0c: ldur            w2, [x1, #0x27]
    // 0x68fb10: DecompressPointer r2
    //     0x68fb10: add             x2, x2, HEAP, lsl #32
    // 0x68fb14: r3 = LoadClassIdInstr(r0)
    //     0x68fb14: ldur            x3, [x0, #-1]
    //     0x68fb18: ubfx            x3, x3, #0xc, #0x14
    // 0x68fb1c: stp             x2, x0, [SP]
    // 0x68fb20: mov             x0, x3
    // 0x68fb24: mov             lr, x0
    // 0x68fb28: ldr             lr, [x21, lr, lsl #3]
    // 0x68fb2c: blr             lr
    // 0x68fb30: tbz             w0, #4, #0x68fb40
    // 0x68fb34: ldur            x0, [fp, #-8]
    // 0x68fb38: StoreField: r0->field_37 = rNULL
    //     0x68fb38: stur            NULL, [x0, #0x37]
    // 0x68fb3c: b               #0x68fbe8
    // 0x68fb40: ldur            x0, [fp, #-8]
    // 0x68fb44: LoadField: r1 = r0->field_37
    //     0x68fb44: ldur            w1, [x0, #0x37]
    // 0x68fb48: DecompressPointer r1
    //     0x68fb48: add             x1, x1, HEAP, lsl #32
    // 0x68fb4c: cmp             w1, NULL
    // 0x68fb50: b.eq            #0x68fbe8
    // 0x68fb54: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x68fb54: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x68fb58: r0 = requestFocus()
    //     0x68fb58: bl              #0x657140  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::requestFocus
    // 0x68fb5c: ldur            x1, [fp, #-8]
    // 0x68fb60: StoreField: r1->field_37 = rNULL
    //     0x68fb60: stur            NULL, [x1, #0x37]
    // 0x68fb64: b               #0x68fbe8
    // 0x68fb68: LoadField: r0 = r1->field_2b
    //     0x68fb68: ldur            w0, [x1, #0x2b]
    // 0x68fb6c: DecompressPointer r0
    //     0x68fb6c: add             x0, x0, HEAP, lsl #32
    // 0x68fb70: LoadField: r2 = r1->field_27
    //     0x68fb70: ldur            w2, [x1, #0x27]
    // 0x68fb74: DecompressPointer r2
    //     0x68fb74: add             x2, x2, HEAP, lsl #32
    // 0x68fb78: stur            x2, [fp, #-0x10]
    // 0x68fb7c: r3 = LoadClassIdInstr(r0)
    //     0x68fb7c: ldur            x3, [x0, #-1]
    //     0x68fb80: ubfx            x3, x3, #0xc, #0x14
    // 0x68fb84: stp             x2, x0, [SP]
    // 0x68fb88: mov             x0, x3
    // 0x68fb8c: mov             lr, x0
    // 0x68fb90: ldr             lr, [x21, lr, lsl #3]
    // 0x68fb94: blr             lr
    // 0x68fb98: tbz             w0, #4, #0x68fbe8
    // 0x68fb9c: ldur            x1, [fp, #-8]
    // 0x68fba0: ldur            x0, [fp, #-0x10]
    // 0x68fba4: StoreField: r1->field_3b = r0
    //     0x68fba4: stur            w0, [x1, #0x3b]
    //     0x68fba8: ldurb           w16, [x1, #-1]
    //     0x68fbac: ldurb           w17, [x0, #-1]
    //     0x68fbb0: and             x16, x17, x16, lsr #2
    //     0x68fbb4: tst             x16, HEAP, lsr #32
    //     0x68fbb8: b.eq            #0x68fbc0
    //     0x68fbbc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x68fbc0: LoadField: r0 = r1->field_2b
    //     0x68fbc0: ldur            w0, [x1, #0x2b]
    // 0x68fbc4: DecompressPointer r0
    //     0x68fbc4: add             x0, x0, HEAP, lsl #32
    // 0x68fbc8: StoreField: r1->field_37 = r0
    //     0x68fbc8: stur            w0, [x1, #0x37]
    //     0x68fbcc: ldurb           w16, [x1, #-1]
    //     0x68fbd0: ldurb           w17, [x0, #-1]
    //     0x68fbd4: and             x16, x17, x16, lsr #2
    //     0x68fbd8: tst             x16, HEAP, lsr #32
    //     0x68fbdc: b.eq            #0x68fbe4
    //     0x68fbe0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x68fbe4: r0 = applyFocusChangesIfNeeded()
    //     0x68fbe4: bl              #0x68fc00  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::applyFocusChangesIfNeeded
    // 0x68fbe8: r0 = Null
    //     0x68fbe8: mov             x0, NULL
    // 0x68fbec: LeaveFrame
    //     0x68fbec: mov             SP, fp
    //     0x68fbf0: ldp             fp, lr, [SP], #0x10
    // 0x68fbf4: ret
    //     0x68fbf4: ret             
    // 0x68fbf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68fbf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68fbfc: b               #0x68faf8
  }
  _ applyFocusChangesIfNeeded(/* No info */) {
    // ** addr: 0x68fc00, size: 0x43c
    // 0x68fc00: EnterFrame
    //     0x68fc00: stp             fp, lr, [SP, #-0x10]!
    //     0x68fc04: mov             fp, SP
    // 0x68fc08: AllocStack(0x58)
    //     0x68fc08: sub             SP, SP, #0x58
    // 0x68fc0c: r0 = false
    //     0x68fc0c: add             x0, NULL, #0x30  ; false
    // 0x68fc10: mov             x3, x1
    // 0x68fc14: stur            x1, [fp, #-0x28]
    // 0x68fc18: CheckStackOverflow
    //     0x68fc18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68fc1c: cmp             SP, x16
    //     0x68fc20: b.ls            #0x690020
    // 0x68fc24: StoreField: r3->field_43 = r0
    //     0x68fc24: stur            w0, [x3, #0x43]
    // 0x68fc28: LoadField: r0 = r3->field_2b
    //     0x68fc28: ldur            w0, [x3, #0x2b]
    // 0x68fc2c: DecompressPointer r0
    //     0x68fc2c: add             x0, x0, HEAP, lsl #32
    // 0x68fc30: stur            x0, [fp, #-0x20]
    // 0x68fc34: LoadField: r4 = r3->field_3f
    //     0x68fc34: ldur            w4, [x3, #0x3f]
    // 0x68fc38: DecompressPointer r4
    //     0x68fc38: add             x4, x4, HEAP, lsl #32
    // 0x68fc3c: stur            x4, [fp, #-0x18]
    // 0x68fc40: LoadField: r1 = r4->field_b
    //     0x68fc40: ldur            w1, [x4, #0xb]
    // 0x68fc44: r5 = LoadInt32Instr(r1)
    //     0x68fc44: sbfx            x5, x1, #1, #0x1f
    // 0x68fc48: stur            x5, [fp, #-0x10]
    // 0x68fc4c: r1 = 0
    //     0x68fc4c: movz            x1, #0
    // 0x68fc50: CheckStackOverflow
    //     0x68fc50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68fc54: cmp             SP, x16
    //     0x68fc58: b.ls            #0x690028
    // 0x68fc5c: LoadField: r2 = r4->field_b
    //     0x68fc5c: ldur            w2, [x4, #0xb]
    // 0x68fc60: r6 = LoadInt32Instr(r2)
    //     0x68fc60: sbfx            x6, x2, #1, #0x1f
    // 0x68fc64: cmp             x5, x6
    // 0x68fc68: b.ne            #0x690000
    // 0x68fc6c: cmp             x1, x6
    // 0x68fc70: b.ge            #0x68fcb4
    // 0x68fc74: LoadField: r2 = r4->field_f
    //     0x68fc74: ldur            w2, [x4, #0xf]
    // 0x68fc78: DecompressPointer r2
    //     0x68fc78: add             x2, x2, HEAP, lsl #32
    // 0x68fc7c: ArrayLoad: r6 = r2[r1]  ; Unknown_4
    //     0x68fc7c: add             x16, x2, x1, lsl #2
    //     0x68fc80: ldur            w6, [x16, #0xf]
    // 0x68fc84: DecompressPointer r6
    //     0x68fc84: add             x6, x6, HEAP, lsl #32
    // 0x68fc88: add             x7, x1, #1
    // 0x68fc8c: mov             x1, x6
    // 0x68fc90: mov             x2, x3
    // 0x68fc94: stur            x7, [fp, #-8]
    // 0x68fc98: r0 = applyIfValid()
    //     0x68fc98: bl              #0x690460  ; [package:flutter/src/widgets/focus_manager.dart] _Autofocus::applyIfValid
    // 0x68fc9c: ldur            x1, [fp, #-8]
    // 0x68fca0: ldur            x3, [fp, #-0x28]
    // 0x68fca4: ldur            x0, [fp, #-0x20]
    // 0x68fca8: ldur            x4, [fp, #-0x18]
    // 0x68fcac: ldur            x5, [fp, #-0x10]
    // 0x68fcb0: b               #0x68fc50
    // 0x68fcb4: mov             x0, x3
    // 0x68fcb8: ldur            x1, [fp, #-0x18]
    // 0x68fcbc: r0 = clear()
    //     0x68fcbc: bl              #0xbd9e80  ; [dart:core] _GrowableList::clear
    // 0x68fcc0: ldur            x1, [fp, #-0x28]
    // 0x68fcc4: LoadField: r2 = r1->field_2b
    //     0x68fcc4: ldur            w2, [x1, #0x2b]
    // 0x68fcc8: DecompressPointer r2
    //     0x68fcc8: add             x2, x2, HEAP, lsl #32
    // 0x68fccc: cmp             w2, NULL
    // 0x68fcd0: b.ne            #0x68fd08
    // 0x68fcd4: LoadField: r0 = r1->field_3b
    //     0x68fcd4: ldur            w0, [x1, #0x3b]
    // 0x68fcd8: DecompressPointer r0
    //     0x68fcd8: add             x0, x0, HEAP, lsl #32
    // 0x68fcdc: cmp             w0, NULL
    // 0x68fce0: b.ne            #0x68fd08
    // 0x68fce4: LoadField: r0 = r1->field_27
    //     0x68fce4: ldur            w0, [x1, #0x27]
    // 0x68fce8: DecompressPointer r0
    //     0x68fce8: add             x0, x0, HEAP, lsl #32
    // 0x68fcec: StoreField: r1->field_3b = r0
    //     0x68fcec: stur            w0, [x1, #0x3b]
    //     0x68fcf0: ldurb           w16, [x1, #-1]
    //     0x68fcf4: ldurb           w17, [x0, #-1]
    //     0x68fcf8: and             x16, x17, x16, lsr #2
    //     0x68fcfc: tst             x16, HEAP, lsr #32
    //     0x68fd00: b.eq            #0x68fd08
    //     0x68fd04: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x68fd08: LoadField: r0 = r1->field_3b
    //     0x68fd08: ldur            w0, [x1, #0x3b]
    // 0x68fd0c: DecompressPointer r0
    //     0x68fd0c: add             x0, x0, HEAP, lsl #32
    // 0x68fd10: cmp             w0, NULL
    // 0x68fd14: b.eq            #0x68fe7c
    // 0x68fd18: r3 = LoadClassIdInstr(r0)
    //     0x68fd18: ldur            x3, [x0, #-1]
    //     0x68fd1c: ubfx            x3, x3, #0xc, #0x14
    // 0x68fd20: stp             x2, x0, [SP]
    // 0x68fd24: mov             x0, x3
    // 0x68fd28: mov             lr, x0
    // 0x68fd2c: ldr             lr, [x21, lr, lsl #3]
    // 0x68fd30: blr             lr
    // 0x68fd34: tbz             w0, #4, #0x68fe78
    // 0x68fd38: ldur            x0, [fp, #-0x20]
    // 0x68fd3c: cmp             w0, NULL
    // 0x68fd40: b.ne            #0x68fd4c
    // 0x68fd44: r0 = Null
    //     0x68fd44: mov             x0, NULL
    // 0x68fd48: b               #0x68fd5c
    // 0x68fd4c: mov             x1, x0
    // 0x68fd50: r0 = ancestors()
    //     0x68fd50: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x68fd54: mov             x1, x0
    // 0x68fd58: r0 = toSet()
    //     0x68fd58: bl              #0x863cac  ; [dart:core] _GrowableList::toSet
    // 0x68fd5c: cmp             w0, NULL
    // 0x68fd60: b.ne            #0x68fdd8
    // 0x68fd64: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x68fd64: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x68fd68: ldr             x0, [x0, #0x778]
    //     0x68fd6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x68fd70: cmp             w0, w16
    //     0x68fd74: b.ne            #0x68fd80
    //     0x68fd78: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x68fd7c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x68fd80: r1 = <FocusNode>
    //     0x68fd80: ldr             x1, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x68fd84: stur            x0, [fp, #-0x30]
    // 0x68fd88: r0 = _Set()
    //     0x68fd88: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x68fd8c: mov             x1, x0
    // 0x68fd90: ldur            x0, [fp, #-0x30]
    // 0x68fd94: stur            x1, [fp, #-0x38]
    // 0x68fd98: StoreField: r1->field_1b = r0
    //     0x68fd98: stur            w0, [x1, #0x1b]
    // 0x68fd9c: StoreField: r1->field_b = rZR
    //     0x68fd9c: stur            wzr, [x1, #0xb]
    // 0x68fda0: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x68fda0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x68fda4: ldr             x0, [x0, #0x780]
    //     0x68fda8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x68fdac: cmp             w0, w16
    //     0x68fdb0: b.ne            #0x68fdbc
    //     0x68fdb4: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x68fdb8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x68fdbc: mov             x1, x0
    // 0x68fdc0: ldur            x0, [fp, #-0x38]
    // 0x68fdc4: StoreField: r0->field_f = r1
    //     0x68fdc4: stur            w1, [x0, #0xf]
    // 0x68fdc8: StoreField: r0->field_13 = rZR
    //     0x68fdc8: stur            wzr, [x0, #0x13]
    // 0x68fdcc: ArrayStore: r0[0] = rZR  ; List_4
    //     0x68fdcc: stur            wzr, [x0, #0x17]
    // 0x68fdd0: mov             x2, x0
    // 0x68fdd4: b               #0x68fddc
    // 0x68fdd8: mov             x2, x0
    // 0x68fddc: ldur            x0, [fp, #-0x28]
    // 0x68fde0: stur            x2, [fp, #-0x30]
    // 0x68fde4: LoadField: r1 = r0->field_3b
    //     0x68fde4: ldur            w1, [x0, #0x3b]
    // 0x68fde8: DecompressPointer r1
    //     0x68fde8: add             x1, x1, HEAP, lsl #32
    // 0x68fdec: cmp             w1, NULL
    // 0x68fdf0: b.eq            #0x690030
    // 0x68fdf4: r0 = ancestors()
    //     0x68fdf4: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x68fdf8: mov             x1, x0
    // 0x68fdfc: r0 = toSet()
    //     0x68fdfc: bl              #0x863cac  ; [dart:core] _GrowableList::toSet
    // 0x68fe00: mov             x3, x0
    // 0x68fe04: ldur            x0, [fp, #-0x28]
    // 0x68fe08: stur            x3, [fp, #-0x40]
    // 0x68fe0c: LoadField: r4 = r0->field_2f
    //     0x68fe0c: ldur            w4, [x0, #0x2f]
    // 0x68fe10: DecompressPointer r4
    //     0x68fe10: add             x4, x4, HEAP, lsl #32
    // 0x68fe14: mov             x1, x3
    // 0x68fe18: ldur            x2, [fp, #-0x30]
    // 0x68fe1c: stur            x4, [fp, #-0x38]
    // 0x68fe20: r0 = difference()
    //     0x68fe20: bl              #0x6900e8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::difference
    // 0x68fe24: ldur            x1, [fp, #-0x38]
    // 0x68fe28: mov             x2, x0
    // 0x68fe2c: r0 = addAll()
    //     0x68fe2c: bl              #0xc1ec5c  ; [dart:_compact_hash] _Set::addAll
    // 0x68fe30: ldur            x1, [fp, #-0x30]
    // 0x68fe34: ldur            x2, [fp, #-0x40]
    // 0x68fe38: r0 = difference()
    //     0x68fe38: bl              #0x6900e8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::difference
    // 0x68fe3c: ldur            x1, [fp, #-0x38]
    // 0x68fe40: mov             x2, x0
    // 0x68fe44: r0 = addAll()
    //     0x68fe44: bl              #0xc1ec5c  ; [dart:_compact_hash] _Set::addAll
    // 0x68fe48: ldur            x1, [fp, #-0x28]
    // 0x68fe4c: LoadField: r0 = r1->field_3b
    //     0x68fe4c: ldur            w0, [x1, #0x3b]
    // 0x68fe50: DecompressPointer r0
    //     0x68fe50: add             x0, x0, HEAP, lsl #32
    // 0x68fe54: StoreField: r1->field_2b = r0
    //     0x68fe54: stur            w0, [x1, #0x2b]
    //     0x68fe58: ldurb           w16, [x1, #-1]
    //     0x68fe5c: ldurb           w17, [x0, #-1]
    //     0x68fe60: and             x16, x17, x16, lsr #2
    //     0x68fe64: tst             x16, HEAP, lsr #32
    //     0x68fe68: b.eq            #0x68fe70
    //     0x68fe6c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x68fe70: StoreField: r1->field_3b = rNULL
    //     0x68fe70: stur            NULL, [x1, #0x3b]
    // 0x68fe74: b               #0x68fe7c
    // 0x68fe78: ldur            x1, [fp, #-0x28]
    // 0x68fe7c: ldur            x2, [fp, #-0x20]
    // 0x68fe80: LoadField: r0 = r1->field_2b
    //     0x68fe80: ldur            w0, [x1, #0x2b]
    // 0x68fe84: DecompressPointer r0
    //     0x68fe84: add             x0, x0, HEAP, lsl #32
    // 0x68fe88: r3 = LoadClassIdInstr(r2)
    //     0x68fe88: ldur            x3, [x2, #-1]
    //     0x68fe8c: ubfx            x3, x3, #0xc, #0x14
    // 0x68fe90: stp             x0, x2, [SP]
    // 0x68fe94: mov             x0, x3
    // 0x68fe98: mov             lr, x0
    // 0x68fe9c: ldr             lr, [x21, lr, lsl #3]
    // 0x68fea0: blr             lr
    // 0x68fea4: tbz             w0, #4, #0x68fee8
    // 0x68fea8: ldur            x0, [fp, #-0x20]
    // 0x68feac: cmp             w0, NULL
    // 0x68feb0: b.eq            #0x68fec8
    // 0x68feb4: ldur            x3, [fp, #-0x28]
    // 0x68feb8: LoadField: r1 = r3->field_2f
    //     0x68feb8: ldur            w1, [x3, #0x2f]
    // 0x68febc: DecompressPointer r1
    //     0x68febc: add             x1, x1, HEAP, lsl #32
    // 0x68fec0: mov             x2, x0
    // 0x68fec4: r0 = add()
    //     0x68fec4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x68fec8: ldur            x0, [fp, #-0x28]
    // 0x68fecc: LoadField: r2 = r0->field_2b
    //     0x68fecc: ldur            w2, [x0, #0x2b]
    // 0x68fed0: DecompressPointer r2
    //     0x68fed0: add             x2, x2, HEAP, lsl #32
    // 0x68fed4: cmp             w2, NULL
    // 0x68fed8: b.eq            #0x68fee8
    // 0x68fedc: LoadField: r1 = r0->field_2f
    //     0x68fedc: ldur            w1, [x0, #0x2f]
    // 0x68fee0: DecompressPointer r1
    //     0x68fee0: add             x1, x1, HEAP, lsl #32
    // 0x68fee4: r0 = add()
    //     0x68fee4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x68fee8: ldur            x0, [fp, #-0x28]
    // 0x68feec: LoadField: r2 = r0->field_2f
    //     0x68feec: ldur            w2, [x0, #0x2f]
    // 0x68fef0: DecompressPointer r2
    //     0x68fef0: add             x2, x2, HEAP, lsl #32
    // 0x68fef4: mov             x1, x2
    // 0x68fef8: stur            x2, [fp, #-0x30]
    // 0x68fefc: r0 = iterator()
    //     0x68fefc: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x68ff00: stur            x0, [fp, #-0x40]
    // 0x68ff04: LoadField: r2 = r0->field_7
    //     0x68ff04: ldur            w2, [x0, #7]
    // 0x68ff08: DecompressPointer r2
    //     0x68ff08: add             x2, x2, HEAP, lsl #32
    // 0x68ff0c: stur            x2, [fp, #-0x38]
    // 0x68ff10: CheckStackOverflow
    //     0x68ff10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68ff14: cmp             SP, x16
    //     0x68ff18: b.ls            #0x690034
    // 0x68ff1c: mov             x1, x0
    // 0x68ff20: r0 = moveNext()
    //     0x68ff20: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x68ff24: tbnz            w0, #4, #0x68ffa8
    // 0x68ff28: ldur            x3, [fp, #-0x40]
    // 0x68ff2c: LoadField: r4 = r3->field_33
    //     0x68ff2c: ldur            w4, [x3, #0x33]
    // 0x68ff30: DecompressPointer r4
    //     0x68ff30: add             x4, x4, HEAP, lsl #32
    // 0x68ff34: stur            x4, [fp, #-0x48]
    // 0x68ff38: cmp             w4, NULL
    // 0x68ff3c: b.ne            #0x68ff6c
    // 0x68ff40: mov             x0, x4
    // 0x68ff44: ldur            x2, [fp, #-0x38]
    // 0x68ff48: r1 = Null
    //     0x68ff48: mov             x1, NULL
    // 0x68ff4c: cmp             w2, NULL
    // 0x68ff50: b.eq            #0x68ff6c
    // 0x68ff54: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x68ff54: ldur            w4, [x2, #0x17]
    // 0x68ff58: DecompressPointer r4
    //     0x68ff58: add             x4, x4, HEAP, lsl #32
    // 0x68ff5c: r8 = X0
    //     0x68ff5c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x68ff60: LoadField: r9 = r4->field_7
    //     0x68ff60: ldur            x9, [x4, #7]
    // 0x68ff64: r3 = Null
    //     0x68ff64: ldr             x3, [PP, #0x2358]  ; [pp+0x2358] Null
    // 0x68ff68: blr             x9
    // 0x68ff6c: ldur            x0, [fp, #-0x48]
    // 0x68ff70: LoadField: r1 = r0->field_4f
    //     0x68ff70: ldur            w1, [x0, #0x4f]
    // 0x68ff74: DecompressPointer r1
    //     0x68ff74: add             x1, x1, HEAP, lsl #32
    // 0x68ff78: cmp             w1, NULL
    // 0x68ff7c: b.eq            #0x68ff9c
    // 0x68ff80: mov             x1, x0
    // 0x68ff84: r0 = hasPrimaryFocus()
    //     0x68ff84: bl              #0x651240  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::hasPrimaryFocus
    // 0x68ff88: tbnz            w0, #4, #0x68ff94
    // 0x68ff8c: ldur            x1, [fp, #-0x48]
    // 0x68ff90: r0 = _setAsFocusedChildForScope()
    //     0x68ff90: bl              #0x650d3c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_setAsFocusedChildForScope
    // 0x68ff94: ldur            x1, [fp, #-0x48]
    // 0x68ff98: r0 = notifyListeners()
    //     0x68ff98: bl              #0x6469a0  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::notifyListeners
    // 0x68ff9c: ldur            x0, [fp, #-0x40]
    // 0x68ffa0: ldur            x2, [fp, #-0x38]
    // 0x68ffa4: b               #0x68ff10
    // 0x68ffa8: ldur            x0, [fp, #-0x28]
    // 0x68ffac: ldur            x2, [fp, #-0x20]
    // 0x68ffb0: ldur            x1, [fp, #-0x30]
    // 0x68ffb4: r0 = clear()
    //     0x68ffb4: bl              #0x67a480  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::clear
    // 0x68ffb8: ldur            x1, [fp, #-0x28]
    // 0x68ffbc: LoadField: r0 = r1->field_2b
    //     0x68ffbc: ldur            w0, [x1, #0x2b]
    // 0x68ffc0: DecompressPointer r0
    //     0x68ffc0: add             x0, x0, HEAP, lsl #32
    // 0x68ffc4: ldur            x2, [fp, #-0x20]
    // 0x68ffc8: r3 = LoadClassIdInstr(r2)
    //     0x68ffc8: ldur            x3, [x2, #-1]
    //     0x68ffcc: ubfx            x3, x3, #0xc, #0x14
    // 0x68ffd0: stp             x0, x2, [SP]
    // 0x68ffd4: mov             x0, x3
    // 0x68ffd8: mov             lr, x0
    // 0x68ffdc: ldr             lr, [x21, lr, lsl #3]
    // 0x68ffe0: blr             lr
    // 0x68ffe4: tbz             w0, #4, #0x68fff0
    // 0x68ffe8: ldur            x1, [fp, #-0x28]
    // 0x68ffec: r0 = notifyListeners()
    //     0x68ffec: bl              #0x6469a0  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::notifyListeners
    // 0x68fff0: r0 = Null
    //     0x68fff0: mov             x0, NULL
    // 0x68fff4: LeaveFrame
    //     0x68fff4: mov             SP, fp
    //     0x68fff8: ldp             fp, lr, [SP], #0x10
    // 0x68fffc: ret
    //     0x68fffc: ret             
    // 0x690000: mov             x0, x4
    // 0x690004: r0 = ConcurrentModificationError()
    //     0x690004: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x690008: mov             x1, x0
    // 0x69000c: ldur            x0, [fp, #-0x18]
    // 0x690010: StoreField: r1->field_b = r0
    //     0x690010: stur            w0, [x1, #0xb]
    // 0x690014: mov             x0, x1
    // 0x690018: r0 = Throw()
    //     0x690018: bl              #0xec04b8  ; ThrowStub
    // 0x69001c: brk             #0
    // 0x690020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x690020: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x690024: b               #0x68fc24
    // 0x690028: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x690028: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x69002c: b               #0x68fc5c
    // 0x690030: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x690030: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x690034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x690034: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x690038: b               #0x68ff1c
  }
  [closure] void applyFocusChangesIfNeeded(dynamic) {
    // ** addr: 0x6900b0, size: 0x38
    // 0x6900b0: EnterFrame
    //     0x6900b0: stp             fp, lr, [SP, #-0x10]!
    //     0x6900b4: mov             fp, SP
    // 0x6900b8: ldr             x0, [fp, #0x10]
    // 0x6900bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6900bc: ldur            w1, [x0, #0x17]
    // 0x6900c0: DecompressPointer r1
    //     0x6900c0: add             x1, x1, HEAP, lsl #32
    // 0x6900c4: CheckStackOverflow
    //     0x6900c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6900c8: cmp             SP, x16
    //     0x6900cc: b.ls            #0x6900e0
    // 0x6900d0: r0 = applyFocusChangesIfNeeded()
    //     0x6900d0: bl              #0x68fc00  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::applyFocusChangesIfNeeded
    // 0x6900d4: LeaveFrame
    //     0x6900d4: mov             SP, fp
    //     0x6900d8: ldp             fp, lr, [SP], #0x10
    // 0x6900dc: ret
    //     0x6900dc: ret             
    // 0x6900e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6900e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6900e4: b               #0x6900d0
  }
  _ registerGlobalHandlers(/* No info */) {
    // ** addr: 0x69262c, size: 0x3c
    // 0x69262c: EnterFrame
    //     0x69262c: stp             fp, lr, [SP, #-0x10]!
    //     0x692630: mov             fp, SP
    // 0x692634: CheckStackOverflow
    //     0x692634: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x692638: cmp             SP, x16
    //     0x69263c: b.ls            #0x692660
    // 0x692640: LoadField: r0 = r1->field_23
    //     0x692640: ldur            w0, [x1, #0x23]
    // 0x692644: DecompressPointer r0
    //     0x692644: add             x0, x0, HEAP, lsl #32
    // 0x692648: mov             x1, x0
    // 0x69264c: r0 = registerGlobalHandlers()
    //     0x69264c: bl              #0x692668  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::registerGlobalHandlers
    // 0x692650: r0 = Null
    //     0x692650: mov             x0, NULL
    // 0x692654: LeaveFrame
    //     0x692654: mov             SP, fp
    //     0x692658: ldp             fp, lr, [SP], #0x10
    // 0x69265c: ret
    //     0x69265c: ret             
    // 0x692660: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x692660: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x692664: b               #0x692640
  }
  get _ instance(/* No info */) {
    // ** addr: 0x693910, size: 0x44
    // 0x693910: EnterFrame
    //     0x693910: stp             fp, lr, [SP, #-0x10]!
    //     0x693914: mov             fp, SP
    // 0x693918: r1 = LoadStaticField(0x7d4)
    //     0x693918: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x69391c: ldr             x1, [x1, #0xfa8]
    // 0x693920: cmp             w1, NULL
    // 0x693924: b.eq            #0x69394c
    // 0x693928: LoadField: r2 = r1->field_eb
    //     0x693928: ldur            w2, [x1, #0xeb]
    // 0x69392c: DecompressPointer r2
    //     0x69392c: add             x2, x2, HEAP, lsl #32
    // 0x693930: cmp             w2, NULL
    // 0x693934: b.eq            #0x693950
    // 0x693938: LoadField: r0 = r2->field_13
    //     0x693938: ldur            w0, [x2, #0x13]
    // 0x69393c: DecompressPointer r0
    //     0x69393c: add             x0, x0, HEAP, lsl #32
    // 0x693940: LeaveFrame
    //     0x693940: mov             SP, fp
    //     0x693944: ldp             fp, lr, [SP], #0x10
    // 0x693948: ret
    //     0x693948: ret             
    // 0x69394c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x69394c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x693950: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x693950: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ FocusManager(/* No info */) {
    // ** addr: 0x693978, size: 0x2ec
    // 0x693978: EnterFrame
    //     0x693978: stp             fp, lr, [SP, #-0x10]!
    //     0x69397c: mov             fp, SP
    // 0x693980: AllocStack(0x30)
    //     0x693980: sub             SP, SP, #0x30
    // 0x693984: r0 = false
    //     0x693984: add             x0, NULL, #0x30  ; false
    // 0x693988: stur            x1, [fp, #-8]
    // 0x69398c: CheckStackOverflow
    //     0x69398c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x693990: cmp             SP, x16
    //     0x693994: b.ls            #0x693c58
    // 0x693998: StoreField: r1->field_43 = r0
    //     0x693998: stur            w0, [x1, #0x43]
    // 0x69399c: r0 = _HighlightModeManager()
    //     0x69399c: bl              #0x6941ec  ; Allocate_HighlightModeManagerStub -> _HighlightModeManager (size=0x20)
    // 0x6939a0: mov             x1, x0
    // 0x6939a4: stur            x0, [fp, #-0x10]
    // 0x6939a8: r0 = _HighlightModeManager()
    //     0x6939a8: bl              #0x693ff0  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::_HighlightModeManager
    // 0x6939ac: ldur            x0, [fp, #-0x10]
    // 0x6939b0: ldur            x1, [fp, #-8]
    // 0x6939b4: StoreField: r1->field_23 = r0
    //     0x6939b4: stur            w0, [x1, #0x23]
    //     0x6939b8: ldurb           w16, [x1, #-1]
    //     0x6939bc: ldurb           w17, [x0, #-1]
    //     0x6939c0: and             x16, x17, x16, lsr #2
    //     0x6939c4: tst             x16, HEAP, lsr #32
    //     0x6939c8: b.eq            #0x6939d0
    //     0x6939cc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6939d0: r0 = FocusScopeNode()
    //     0x6939d0: bl              #0x693fe4  ; AllocateFocusScopeNodeStub -> FocusScopeNode (size=0x70)
    // 0x6939d4: stur            x0, [fp, #-0x10]
    // 0x6939d8: r16 = "Root Focus Scope"
    //     0x6939d8: ldr             x16, [PP, #0x2320]  ; [pp+0x2320] "Root Focus Scope"
    // 0x6939dc: str             x16, [SP]
    // 0x6939e0: mov             x1, x0
    // 0x6939e4: r4 = const [0, 0x2, 0x1, 0x1, debugLabel, 0x1, null]
    //     0x6939e4: ldr             x4, [PP, #0x2328]  ; [pp+0x2328] List(7) [0, 0x2, 0x1, 0x1, "debugLabel", 0x1, Null]
    // 0x6939e8: r0 = FocusScopeNode()
    //     0x6939e8: bl              #0x693c70  ; [package:flutter/src/widgets/focus_manager.dart] FocusScopeNode::FocusScopeNode
    // 0x6939ec: ldur            x0, [fp, #-0x10]
    // 0x6939f0: ldur            x1, [fp, #-8]
    // 0x6939f4: StoreField: r1->field_27 = r0
    //     0x6939f4: stur            w0, [x1, #0x27]
    //     0x6939f8: ldurb           w16, [x1, #-1]
    //     0x6939fc: ldurb           w17, [x0, #-1]
    //     0x693a00: and             x16, x17, x16, lsr #2
    //     0x693a04: tst             x16, HEAP, lsr #32
    //     0x693a08: b.eq            #0x693a10
    //     0x693a0c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x693a10: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x693a10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x693a14: ldr             x0, [x0, #0x778]
    //     0x693a18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x693a1c: cmp             w0, w16
    //     0x693a20: b.ne            #0x693a2c
    //     0x693a24: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x693a28: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x693a2c: r1 = <FocusNode>
    //     0x693a2c: ldr             x1, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x693a30: stur            x0, [fp, #-0x18]
    // 0x693a34: r0 = _Set()
    //     0x693a34: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x693a38: mov             x1, x0
    // 0x693a3c: ldur            x0, [fp, #-0x18]
    // 0x693a40: stur            x1, [fp, #-0x20]
    // 0x693a44: StoreField: r1->field_1b = r0
    //     0x693a44: stur            w0, [x1, #0x1b]
    // 0x693a48: StoreField: r1->field_b = rZR
    //     0x693a48: stur            wzr, [x1, #0xb]
    // 0x693a4c: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x693a4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x693a50: ldr             x0, [x0, #0x780]
    //     0x693a54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x693a58: cmp             w0, w16
    //     0x693a5c: b.ne            #0x693a68
    //     0x693a60: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x693a64: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x693a68: mov             x1, x0
    // 0x693a6c: ldur            x0, [fp, #-0x20]
    // 0x693a70: StoreField: r0->field_f = r1
    //     0x693a70: stur            w1, [x0, #0xf]
    // 0x693a74: StoreField: r0->field_13 = rZR
    //     0x693a74: stur            wzr, [x0, #0x13]
    // 0x693a78: ArrayStore: r0[0] = rZR  ; List_4
    //     0x693a78: stur            wzr, [x0, #0x17]
    // 0x693a7c: ldur            x3, [fp, #-8]
    // 0x693a80: StoreField: r3->field_2f = r0
    //     0x693a80: stur            w0, [x3, #0x2f]
    //     0x693a84: ldurb           w16, [x3, #-1]
    //     0x693a88: ldurb           w17, [x0, #-1]
    //     0x693a8c: and             x16, x17, x16, lsr #2
    //     0x693a90: tst             x16, HEAP, lsr #32
    //     0x693a94: b.eq            #0x693a9c
    //     0x693a98: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x693a9c: r1 = <_Autofocus>
    //     0x693a9c: ldr             x1, [PP, #0x2330]  ; [pp+0x2330] TypeArguments: <_Autofocus>
    // 0x693aa0: r2 = 0
    //     0x693aa0: movz            x2, #0
    // 0x693aa4: r0 = _GrowableList()
    //     0x693aa4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x693aa8: ldur            x1, [fp, #-8]
    // 0x693aac: StoreField: r1->field_3f = r0
    //     0x693aac: stur            w0, [x1, #0x3f]
    //     0x693ab0: ldurb           w16, [x1, #-1]
    //     0x693ab4: ldurb           w17, [x0, #-1]
    //     0x693ab8: and             x16, x17, x16, lsr #2
    //     0x693abc: tst             x16, HEAP, lsr #32
    //     0x693ac0: b.eq            #0x693ac8
    //     0x693ac4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x693ac8: StoreField: r1->field_7 = rZR
    //     0x693ac8: stur            xzr, [x1, #7]
    // 0x693acc: StoreField: r1->field_13 = rZR
    //     0x693acc: stur            xzr, [x1, #0x13]
    // 0x693ad0: StoreField: r1->field_1b = rZR
    //     0x693ad0: stur            xzr, [x1, #0x1b]
    // 0x693ad4: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0x693ad4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x693ad8: ldr             x0, [x0, #0xca8]
    //     0x693adc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x693ae0: cmp             w0, w16
    //     0x693ae4: b.ne            #0x693af0
    //     0x693ae8: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0x693aec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x693af0: ldur            x2, [fp, #-8]
    // 0x693af4: StoreField: r2->field_f = r0
    //     0x693af4: stur            w0, [x2, #0xf]
    //     0x693af8: ldurb           w16, [x2, #-1]
    //     0x693afc: ldurb           w17, [x0, #-1]
    //     0x693b00: and             x16, x17, x16, lsr #2
    //     0x693b04: tst             x16, HEAP, lsr #32
    //     0x693b08: b.eq            #0x693b10
    //     0x693b0c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x693b10: mov             x1, x2
    // 0x693b14: r0 = _simpleInstanceOfFalse()
    //     0x693b14: bl              #0xebd578  ; [dart:core] Object::_simpleInstanceOfFalse
    // 0x693b18: tbnz            w0, #4, #0x693c24
    // 0x693b1c: ldur            x2, [fp, #-8]
    // 0x693b20: r0 = _AppLifecycleListener()
    //     0x693b20: bl              #0x693c64  ; Allocate_AppLifecycleListenerStub -> _AppLifecycleListener (size=0xc)
    // 0x693b24: ldur            x2, [fp, #-8]
    // 0x693b28: r1 = Function '_appLifecycleChange@279042876':.
    //     0x693b28: ldr             x1, [PP, #0x2338]  ; [pp+0x2338] AnonymousClosure: (0x68fa7c), in [package:flutter/src/widgets/focus_manager.dart] FocusManager::_appLifecycleChange (0x68fadc)
    // 0x693b2c: stur            x0, [fp, #-0x18]
    // 0x693b30: r0 = AllocateClosure()
    //     0x693b30: bl              #0xec1630  ; AllocateClosureStub
    // 0x693b34: ldur            x3, [fp, #-0x18]
    // 0x693b38: StoreField: r3->field_7 = r0
    //     0x693b38: stur            w0, [x3, #7]
    // 0x693b3c: mov             x0, x3
    // 0x693b40: ldur            x4, [fp, #-8]
    // 0x693b44: StoreField: r4->field_33 = r0
    //     0x693b44: stur            w0, [x4, #0x33]
    //     0x693b48: ldurb           w16, [x4, #-1]
    //     0x693b4c: ldurb           w17, [x0, #-1]
    //     0x693b50: and             x16, x17, x16, lsr #2
    //     0x693b54: tst             x16, HEAP, lsr #32
    //     0x693b58: b.eq            #0x693b60
    //     0x693b5c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x693b60: r0 = LoadStaticField(0x7d4)
    //     0x693b60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x693b64: ldr             x0, [x0, #0xfa8]
    // 0x693b68: cmp             w0, NULL
    // 0x693b6c: b.eq            #0x693c60
    // 0x693b70: LoadField: r5 = r0->field_ef
    //     0x693b70: ldur            w5, [x0, #0xef]
    // 0x693b74: DecompressPointer r5
    //     0x693b74: add             x5, x5, HEAP, lsl #32
    // 0x693b78: stur            x5, [fp, #-0x20]
    // 0x693b7c: LoadField: r2 = r5->field_7
    //     0x693b7c: ldur            w2, [x5, #7]
    // 0x693b80: DecompressPointer r2
    //     0x693b80: add             x2, x2, HEAP, lsl #32
    // 0x693b84: mov             x0, x3
    // 0x693b88: r1 = Null
    //     0x693b88: mov             x1, NULL
    // 0x693b8c: cmp             w2, NULL
    // 0x693b90: b.eq            #0x693bac
    // 0x693b94: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x693b94: ldur            w4, [x2, #0x17]
    // 0x693b98: DecompressPointer r4
    //     0x693b98: add             x4, x4, HEAP, lsl #32
    // 0x693b9c: r8 = X0
    //     0x693b9c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x693ba0: LoadField: r9 = r4->field_7
    //     0x693ba0: ldur            x9, [x4, #7]
    // 0x693ba4: r3 = Null
    //     0x693ba4: ldr             x3, [PP, #0x2340]  ; [pp+0x2340] Null
    // 0x693ba8: blr             x9
    // 0x693bac: ldur            x0, [fp, #-0x20]
    // 0x693bb0: LoadField: r1 = r0->field_b
    //     0x693bb0: ldur            w1, [x0, #0xb]
    // 0x693bb4: LoadField: r2 = r0->field_f
    //     0x693bb4: ldur            w2, [x0, #0xf]
    // 0x693bb8: DecompressPointer r2
    //     0x693bb8: add             x2, x2, HEAP, lsl #32
    // 0x693bbc: LoadField: r3 = r2->field_b
    //     0x693bbc: ldur            w3, [x2, #0xb]
    // 0x693bc0: r2 = LoadInt32Instr(r1)
    //     0x693bc0: sbfx            x2, x1, #1, #0x1f
    // 0x693bc4: stur            x2, [fp, #-0x28]
    // 0x693bc8: r1 = LoadInt32Instr(r3)
    //     0x693bc8: sbfx            x1, x3, #1, #0x1f
    // 0x693bcc: cmp             x2, x1
    // 0x693bd0: b.ne            #0x693bdc
    // 0x693bd4: mov             x1, x0
    // 0x693bd8: r0 = _growToNextCapacity()
    //     0x693bd8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x693bdc: ldur            x2, [fp, #-0x20]
    // 0x693be0: ldur            x3, [fp, #-0x28]
    // 0x693be4: add             x4, x3, #1
    // 0x693be8: lsl             x5, x4, #1
    // 0x693bec: StoreField: r2->field_b = r5
    //     0x693bec: stur            w5, [x2, #0xb]
    // 0x693bf0: LoadField: r1 = r2->field_f
    //     0x693bf0: ldur            w1, [x2, #0xf]
    // 0x693bf4: DecompressPointer r1
    //     0x693bf4: add             x1, x1, HEAP, lsl #32
    // 0x693bf8: ldur            x0, [fp, #-0x18]
    // 0x693bfc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x693bfc: add             x25, x1, x3, lsl #2
    //     0x693c00: add             x25, x25, #0xf
    //     0x693c04: str             w0, [x25]
    //     0x693c08: tbz             w0, #0, #0x693c24
    //     0x693c0c: ldurb           w16, [x1, #-1]
    //     0x693c10: ldurb           w17, [x0, #-1]
    //     0x693c14: and             x16, x17, x16, lsr #2
    //     0x693c18: tst             x16, HEAP, lsr #32
    //     0x693c1c: b.eq            #0x693c24
    //     0x693c20: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x693c24: ldur            x1, [fp, #-0x10]
    // 0x693c28: ldur            x0, [fp, #-8]
    // 0x693c2c: StoreField: r1->field_3f = r0
    //     0x693c2c: stur            w0, [x1, #0x3f]
    //     0x693c30: ldurb           w16, [x1, #-1]
    //     0x693c34: ldurb           w17, [x0, #-1]
    //     0x693c38: and             x16, x17, x16, lsr #2
    //     0x693c3c: tst             x16, HEAP, lsr #32
    //     0x693c40: b.eq            #0x693c48
    //     0x693c44: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x693c48: r0 = Null
    //     0x693c48: mov             x0, NULL
    // 0x693c4c: LeaveFrame
    //     0x693c4c: mov             SP, fp
    //     0x693c50: ldp             fp, lr, [SP], #0x10
    // 0x693c54: ret
    //     0x693c54: ret             
    // 0x693c58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x693c58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x693c5c: b               #0x693998
    // 0x693c60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x693c60: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ addHighlightModeListener(/* No info */) {
    // ** addr: 0x9338b8, size: 0x3c
    // 0x9338b8: EnterFrame
    //     0x9338b8: stp             fp, lr, [SP, #-0x10]!
    //     0x9338bc: mov             fp, SP
    // 0x9338c0: CheckStackOverflow
    //     0x9338c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9338c4: cmp             SP, x16
    //     0x9338c8: b.ls            #0x9338ec
    // 0x9338cc: LoadField: r0 = r1->field_23
    //     0x9338cc: ldur            w0, [x1, #0x23]
    // 0x9338d0: DecompressPointer r0
    //     0x9338d0: add             x0, x0, HEAP, lsl #32
    // 0x9338d4: mov             x1, x0
    // 0x9338d8: r0 = addListener()
    //     0x9338d8: bl              #0x9338f4  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::addListener
    // 0x9338dc: r0 = Null
    //     0x9338dc: mov             x0, NULL
    // 0x9338e0: LeaveFrame
    //     0x9338e0: mov             SP, fp
    //     0x9338e4: ldp             fp, lr, [SP], #0x10
    // 0x9338e8: ret
    //     0x9338e8: ret             
    // 0x9338ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9338ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9338f0: b               #0x9338cc
  }
  get _ highlightMode(/* No info */) {
    // ** addr: 0x935284, size: 0x4c
    // 0x935284: EnterFrame
    //     0x935284: stp             fp, lr, [SP, #-0x10]!
    //     0x935288: mov             fp, SP
    // 0x93528c: CheckStackOverflow
    //     0x93528c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x935290: cmp             SP, x16
    //     0x935294: b.ls            #0x9352c8
    // 0x935298: LoadField: r0 = r1->field_23
    //     0x935298: ldur            w0, [x1, #0x23]
    // 0x93529c: DecompressPointer r0
    //     0x93529c: add             x0, x0, HEAP, lsl #32
    // 0x9352a0: LoadField: r1 = r0->field_b
    //     0x9352a0: ldur            w1, [x0, #0xb]
    // 0x9352a4: DecompressPointer r1
    //     0x9352a4: add             x1, x1, HEAP, lsl #32
    // 0x9352a8: cmp             w1, NULL
    // 0x9352ac: b.ne            #0x9352b8
    // 0x9352b0: r0 = _defaultModeForPlatform()
    //     0x9352b0: bl              #0x692d48  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::_defaultModeForPlatform
    // 0x9352b4: b               #0x9352bc
    // 0x9352b8: mov             x0, x1
    // 0x9352bc: LeaveFrame
    //     0x9352bc: mov             SP, fp
    //     0x9352c0: ldp             fp, lr, [SP], #0x10
    // 0x9352c4: ret
    //     0x9352c4: ret             
    // 0x9352c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9352c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9352cc: b               #0x935298
  }
  _ _markPropertiesChanged(/* No info */) {
    // ** addr: 0x93a67c, size: 0x58
    // 0x93a67c: EnterFrame
    //     0x93a67c: stp             fp, lr, [SP, #-0x10]!
    //     0x93a680: mov             fp, SP
    // 0x93a684: AllocStack(0x10)
    //     0x93a684: sub             SP, SP, #0x10
    // 0x93a688: SetupParameters(FocusManager this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x93a688: mov             x0, x1
    //     0x93a68c: stur            x1, [fp, #-8]
    //     0x93a690: stur            x2, [fp, #-0x10]
    // 0x93a694: CheckStackOverflow
    //     0x93a694: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93a698: cmp             SP, x16
    //     0x93a69c: b.ls            #0x93a6cc
    // 0x93a6a0: mov             x1, x0
    // 0x93a6a4: r0 = _markNeedsUpdate()
    //     0x93a6a4: bl              #0x93a6d4  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::_markNeedsUpdate
    // 0x93a6a8: ldur            x0, [fp, #-8]
    // 0x93a6ac: LoadField: r1 = r0->field_2f
    //     0x93a6ac: ldur            w1, [x0, #0x2f]
    // 0x93a6b0: DecompressPointer r1
    //     0x93a6b0: add             x1, x1, HEAP, lsl #32
    // 0x93a6b4: ldur            x2, [fp, #-0x10]
    // 0x93a6b8: r0 = add()
    //     0x93a6b8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93a6bc: r0 = Null
    //     0x93a6bc: mov             x0, NULL
    // 0x93a6c0: LeaveFrame
    //     0x93a6c0: mov             SP, fp
    //     0x93a6c4: ldp             fp, lr, [SP], #0x10
    // 0x93a6c8: ret
    //     0x93a6c8: ret             
    // 0x93a6cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93a6cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93a6d0: b               #0x93a6a0
  }
  _ _markNeedsUpdate(/* No info */) {
    // ** addr: 0x93a6d4, size: 0x68
    // 0x93a6d4: EnterFrame
    //     0x93a6d4: stp             fp, lr, [SP, #-0x10]!
    //     0x93a6d8: mov             fp, SP
    // 0x93a6dc: AllocStack(0x8)
    //     0x93a6dc: sub             SP, SP, #8
    // 0x93a6e0: SetupParameters(FocusManager this /* r1 => r2 */)
    //     0x93a6e0: mov             x2, x1
    // 0x93a6e4: CheckStackOverflow
    //     0x93a6e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93a6e8: cmp             SP, x16
    //     0x93a6ec: b.ls            #0x93a734
    // 0x93a6f0: LoadField: r0 = r2->field_43
    //     0x93a6f0: ldur            w0, [x2, #0x43]
    // 0x93a6f4: DecompressPointer r0
    //     0x93a6f4: add             x0, x0, HEAP, lsl #32
    // 0x93a6f8: tbnz            w0, #4, #0x93a70c
    // 0x93a6fc: r0 = Null
    //     0x93a6fc: mov             x0, NULL
    // 0x93a700: LeaveFrame
    //     0x93a700: mov             SP, fp
    //     0x93a704: ldp             fp, lr, [SP], #0x10
    // 0x93a708: ret
    //     0x93a708: ret             
    // 0x93a70c: r0 = true
    //     0x93a70c: add             x0, NULL, #0x20  ; true
    // 0x93a710: StoreField: r2->field_43 = r0
    //     0x93a710: stur            w0, [x2, #0x43]
    // 0x93a714: r1 = Function 'applyFocusChangesIfNeeded':.
    //     0x93a714: ldr             x1, [PP, #0x4e20]  ; [pp+0x4e20] AnonymousClosure: (0x6900b0), in [package:flutter/src/widgets/focus_manager.dart] FocusManager::applyFocusChangesIfNeeded (0x68fc00)
    // 0x93a718: r0 = AllocateClosure()
    //     0x93a718: bl              #0xec1630  ; AllocateClosureStub
    // 0x93a71c: str             x0, [SP]
    // 0x93a720: r0 = scheduleMicrotask()
    //     0x93a720: bl              #0x5f9ad0  ; [dart:async] ::scheduleMicrotask
    // 0x93a724: r0 = Null
    //     0x93a724: mov             x0, NULL
    // 0x93a728: LeaveFrame
    //     0x93a728: mov             SP, fp
    //     0x93a72c: ldp             fp, lr, [SP], #0x10
    // 0x93a730: ret
    //     0x93a730: ret             
    // 0x93a734: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93a734: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93a738: b               #0x93a6f0
  }
  _ _markDetached(/* No info */) {
    // ** addr: 0x98f9b8, size: 0xbc
    // 0x98f9b8: EnterFrame
    //     0x98f9b8: stp             fp, lr, [SP, #-0x10]!
    //     0x98f9bc: mov             fp, SP
    // 0x98f9c0: AllocStack(0x20)
    //     0x98f9c0: sub             SP, SP, #0x20
    // 0x98f9c4: SetupParameters(FocusManager this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x98f9c4: stur            x1, [fp, #-8]
    //     0x98f9c8: stur            x2, [fp, #-0x10]
    // 0x98f9cc: CheckStackOverflow
    //     0x98f9cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98f9d0: cmp             SP, x16
    //     0x98f9d4: b.ls            #0x98fa6c
    // 0x98f9d8: LoadField: r0 = r1->field_2b
    //     0x98f9d8: ldur            w0, [x1, #0x2b]
    // 0x98f9dc: DecompressPointer r0
    //     0x98f9dc: add             x0, x0, HEAP, lsl #32
    // 0x98f9e0: r3 = LoadClassIdInstr(r0)
    //     0x98f9e0: ldur            x3, [x0, #-1]
    //     0x98f9e4: ubfx            x3, x3, #0xc, #0x14
    // 0x98f9e8: stp             x2, x0, [SP]
    // 0x98f9ec: mov             x0, x3
    // 0x98f9f0: mov             lr, x0
    // 0x98f9f4: ldr             lr, [x21, lr, lsl #3]
    // 0x98f9f8: blr             lr
    // 0x98f9fc: tbnz            w0, #4, #0x98fa0c
    // 0x98fa00: ldur            x1, [fp, #-8]
    // 0x98fa04: StoreField: r1->field_2b = rNULL
    //     0x98fa04: stur            NULL, [x1, #0x2b]
    // 0x98fa08: b               #0x98fa10
    // 0x98fa0c: ldur            x1, [fp, #-8]
    // 0x98fa10: LoadField: r0 = r1->field_37
    //     0x98fa10: ldur            w0, [x1, #0x37]
    // 0x98fa14: DecompressPointer r0
    //     0x98fa14: add             x0, x0, HEAP, lsl #32
    // 0x98fa18: r2 = LoadClassIdInstr(r0)
    //     0x98fa18: ldur            x2, [x0, #-1]
    //     0x98fa1c: ubfx            x2, x2, #0xc, #0x14
    // 0x98fa20: ldur            x16, [fp, #-0x10]
    // 0x98fa24: stp             x16, x0, [SP]
    // 0x98fa28: mov             x0, x2
    // 0x98fa2c: mov             lr, x0
    // 0x98fa30: ldr             lr, [x21, lr, lsl #3]
    // 0x98fa34: blr             lr
    // 0x98fa38: tbnz            w0, #4, #0x98fa48
    // 0x98fa3c: ldur            x0, [fp, #-8]
    // 0x98fa40: StoreField: r0->field_37 = rNULL
    //     0x98fa40: stur            NULL, [x0, #0x37]
    // 0x98fa44: b               #0x98fa4c
    // 0x98fa48: ldur            x0, [fp, #-8]
    // 0x98fa4c: LoadField: r1 = r0->field_2f
    //     0x98fa4c: ldur            w1, [x0, #0x2f]
    // 0x98fa50: DecompressPointer r1
    //     0x98fa50: add             x1, x1, HEAP, lsl #32
    // 0x98fa54: ldur            x2, [fp, #-0x10]
    // 0x98fa58: r0 = remove()
    //     0x98fa58: bl              #0xd89c04  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x98fa5c: r0 = Null
    //     0x98fa5c: mov             x0, NULL
    // 0x98fa60: LeaveFrame
    //     0x98fa60: mov             SP, fp
    //     0x98fa64: ldp             fp, lr, [SP], #0x10
    // 0x98fa68: ret
    //     0x98fa68: ret             
    // 0x98fa6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98fa6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98fa70: b               #0x98f9d8
  }
  _ removeHighlightModeListener(/* No info */) {
    // ** addr: 0xa7d584, size: 0x38
    // 0xa7d584: EnterFrame
    //     0xa7d584: stp             fp, lr, [SP, #-0x10]!
    //     0xa7d588: mov             fp, SP
    // 0xa7d58c: CheckStackOverflow
    //     0xa7d58c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7d590: cmp             SP, x16
    //     0xa7d594: b.ls            #0xa7d5b4
    // 0xa7d598: LoadField: r0 = r1->field_23
    //     0xa7d598: ldur            w0, [x1, #0x23]
    // 0xa7d59c: DecompressPointer r0
    //     0xa7d59c: add             x0, x0, HEAP, lsl #32
    // 0xa7d5a0: mov             x1, x0
    // 0xa7d5a4: r0 = removeListener()
    //     0xa7d5a4: bl              #0xa7d5bc  ; [package:flutter/src/widgets/focus_manager.dart] _HighlightModeManager::removeListener
    // 0xa7d5a8: LeaveFrame
    //     0xa7d5a8: mov             SP, fp
    //     0xa7d5ac: ldp             fp, lr, [SP], #0x10
    // 0xa7d5b0: ret
    //     0xa7d5b0: ret             
    // 0xa7d5b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7d5b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7d5b8: b               #0xa7d598
  }
  _ _markNextFocus(/* No info */) {
    // ** addr: 0xdaacbc, size: 0xa0
    // 0xdaacbc: EnterFrame
    //     0xdaacbc: stp             fp, lr, [SP, #-0x10]!
    //     0xdaacc0: mov             fp, SP
    // 0xdaacc4: AllocStack(0x20)
    //     0xdaacc4: sub             SP, SP, #0x20
    // 0xdaacc8: SetupParameters(FocusManager this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xdaacc8: stur            x1, [fp, #-8]
    //     0xdaaccc: mov             x16, x2
    //     0xdaacd0: mov             x2, x1
    //     0xdaacd4: mov             x1, x16
    //     0xdaacd8: stur            x1, [fp, #-0x10]
    // 0xdaacdc: CheckStackOverflow
    //     0xdaacdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdaace0: cmp             SP, x16
    //     0xdaace4: b.ls            #0xdaad54
    // 0xdaace8: LoadField: r0 = r2->field_2b
    //     0xdaace8: ldur            w0, [x2, #0x2b]
    // 0xdaacec: DecompressPointer r0
    //     0xdaacec: add             x0, x0, HEAP, lsl #32
    // 0xdaacf0: r3 = LoadClassIdInstr(r0)
    //     0xdaacf0: ldur            x3, [x0, #-1]
    //     0xdaacf4: ubfx            x3, x3, #0xc, #0x14
    // 0xdaacf8: stp             x1, x0, [SP]
    // 0xdaacfc: mov             x0, x3
    // 0xdaad00: mov             lr, x0
    // 0xdaad04: ldr             lr, [x21, lr, lsl #3]
    // 0xdaad08: blr             lr
    // 0xdaad0c: tbnz            w0, #4, #0xdaad1c
    // 0xdaad10: ldur            x1, [fp, #-8]
    // 0xdaad14: StoreField: r1->field_3b = rNULL
    //     0xdaad14: stur            NULL, [x1, #0x3b]
    // 0xdaad18: b               #0xdaad44
    // 0xdaad1c: ldur            x1, [fp, #-8]
    // 0xdaad20: ldur            x0, [fp, #-0x10]
    // 0xdaad24: StoreField: r1->field_3b = r0
    //     0xdaad24: stur            w0, [x1, #0x3b]
    //     0xdaad28: ldurb           w16, [x1, #-1]
    //     0xdaad2c: ldurb           w17, [x0, #-1]
    //     0xdaad30: and             x16, x17, x16, lsr #2
    //     0xdaad34: tst             x16, HEAP, lsr #32
    //     0xdaad38: b.eq            #0xdaad40
    //     0xdaad3c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xdaad40: r0 = _markNeedsUpdate()
    //     0xdaad40: bl              #0x93a6d4  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::_markNeedsUpdate
    // 0xdaad44: r0 = Null
    //     0xdaad44: mov             x0, NULL
    // 0xdaad48: LeaveFrame
    //     0xdaad48: mov             SP, fp
    //     0xdaad4c: ldp             fp, lr, [SP], #0x10
    // 0xdaad50: ret
    //     0xdaad50: ret             
    // 0xdaad54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdaad54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdaad58: b               #0xdaace8
  }
}

// class id: 2919, size: 0x68, field offset: 0x24
class FocusNode extends _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier {

  _ _setAsFocusedChildForScope(/* No info */) {
    // ** addr: 0x650d3c, size: 0x2e4
    // 0x650d3c: EnterFrame
    //     0x650d3c: stp             fp, lr, [SP, #-0x10]!
    //     0x650d40: mov             fp, SP
    // 0x650d44: AllocStack(0x40)
    //     0x650d44: sub             SP, SP, #0x40
    // 0x650d48: SetupParameters(FocusNode this /* r1 => r0, fp-0x8 */)
    //     0x650d48: mov             x0, x1
    //     0x650d4c: stur            x1, [fp, #-8]
    // 0x650d50: CheckStackOverflow
    //     0x650d50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x650d54: cmp             SP, x16
    //     0x650d58: b.ls            #0x651000
    // 0x650d5c: mov             x1, x0
    // 0x650d60: r0 = ancestors()
    //     0x650d60: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x650d64: r16 = <FocusScopeNode>
    //     0x650d64: ldr             x16, [PP, #0x23c8]  ; [pp+0x23c8] TypeArguments: <FocusScopeNode>
    // 0x650d68: stp             x0, x16, [SP]
    // 0x650d6c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x650d6c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x650d70: r0 = whereType()
    //     0x650d70: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0x650d74: mov             x1, x0
    // 0x650d78: r0 = iterator()
    //     0x650d78: bl              #0x888158  ; [dart:_internal] WhereTypeIterable::iterator
    // 0x650d7c: LoadField: r2 = r0->field_b
    //     0x650d7c: ldur            w2, [x0, #0xb]
    // 0x650d80: DecompressPointer r2
    //     0x650d80: add             x2, x2, HEAP, lsl #32
    // 0x650d84: stur            x2, [fp, #-0x18]
    // 0x650d88: LoadField: r3 = r0->field_7
    //     0x650d88: ldur            w3, [x0, #7]
    // 0x650d8c: DecompressPointer r3
    //     0x650d8c: add             x3, x3, HEAP, lsl #32
    // 0x650d90: stur            x3, [fp, #-0x10]
    // 0x650d94: ldur            x4, [fp, #-8]
    // 0x650d98: stur            x4, [fp, #-8]
    // 0x650d9c: CheckStackOverflow
    //     0x650d9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x650da0: cmp             SP, x16
    //     0x650da4: b.ls            #0x651008
    // 0x650da8: CheckStackOverflow
    //     0x650da8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x650dac: cmp             SP, x16
    //     0x650db0: b.ls            #0x651010
    // 0x650db4: r0 = LoadClassIdInstr(r2)
    //     0x650db4: ldur            x0, [x2, #-1]
    //     0x650db8: ubfx            x0, x0, #0xc, #0x14
    // 0x650dbc: mov             x1, x2
    // 0x650dc0: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x650dc0: movz            x17, #0x292d
    //     0x650dc4: movk            x17, #0x1, lsl #16
    //     0x650dc8: add             lr, x0, x17
    //     0x650dcc: ldr             lr, [x21, lr, lsl #3]
    //     0x650dd0: blr             lr
    // 0x650dd4: tbnz            w0, #4, #0x650ff0
    // 0x650dd8: ldur            x2, [fp, #-0x18]
    // 0x650ddc: r0 = LoadClassIdInstr(r2)
    //     0x650ddc: ldur            x0, [x2, #-1]
    //     0x650de0: ubfx            x0, x0, #0xc, #0x14
    // 0x650de4: mov             x1, x2
    // 0x650de8: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x650de8: movz            x17, #0x384d
    //     0x650dec: movk            x17, #0x1, lsl #16
    //     0x650df0: add             lr, x0, x17
    //     0x650df4: ldr             lr, [x21, lr, lsl #3]
    //     0x650df8: blr             lr
    // 0x650dfc: ldur            x2, [fp, #-0x10]
    // 0x650e00: r1 = Null
    //     0x650e00: mov             x1, NULL
    // 0x650e04: cmp             w2, NULL
    // 0x650e08: b.eq            #0x650e94
    // 0x650e0c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x650e0c: ldur            w3, [x2, #0x17]
    // 0x650e10: DecompressPointer r3
    //     0x650e10: add             x3, x3, HEAP, lsl #32
    // 0x650e14: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0x650e18: cmp             w3, w16
    // 0x650e1c: b.eq            #0x650e94
    // 0x650e20: r16 = Object?
    //     0x650e20: ldr             x16, [PP, #0x1648]  ; [pp+0x1648] Type: Object?
    // 0x650e24: cmp             w3, w16
    // 0x650e28: b.eq            #0x650e94
    // 0x650e2c: r16 = void?
    //     0x650e2c: ldr             x16, [PP, #0x1650]  ; [pp+0x1650] Type: void?
    // 0x650e30: cmp             w3, w16
    // 0x650e34: b.eq            #0x650e94
    // 0x650e38: tbnz            w0, #0, #0x650e54
    // 0x650e3c: r16 = int
    //     0x650e3c: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x650e40: cmp             w3, w16
    // 0x650e44: b.eq            #0x650e94
    // 0x650e48: r16 = num
    //     0x650e48: ldr             x16, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0x650e4c: cmp             w3, w16
    // 0x650e50: b.eq            #0x650e94
    // 0x650e54: r3 = SubtypeTestCache
    //     0x650e54: ldr             x3, [PP, #0x23d0]  ; [pp+0x23d0] SubtypeTestCache
    // 0x650e58: r30 = Subtype6TestCacheStub
    //     0x650e58: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27cc)
    // 0x650e5c: LoadField: r30 = r30->field_7
    //     0x650e5c: ldur            lr, [lr, #7]
    // 0x650e60: blr             lr
    // 0x650e64: cmp             w7, NULL
    // 0x650e68: b.eq            #0x650e74
    // 0x650e6c: tbnz            w7, #4, #0x650e8c
    // 0x650e70: b               #0x650e94
    // 0x650e74: r8 = X0
    //     0x650e74: ldr             x8, [PP, #0x23d8]  ; [pp+0x23d8] TypeParameter: X0
    // 0x650e78: r3 = SubtypeTestCache
    //     0x650e78: ldr             x3, [PP, #0x23e0]  ; [pp+0x23e0] SubtypeTestCache
    // 0x650e7c: r30 = InstanceOfStub
    //     0x650e7c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x650e80: LoadField: r30 = r30->field_7
    //     0x650e80: ldur            lr, [lr, #7]
    // 0x650e84: blr             lr
    // 0x650e88: b               #0x650e98
    // 0x650e8c: r0 = false
    //     0x650e8c: add             x0, NULL, #0x30  ; false
    // 0x650e90: b               #0x650e98
    // 0x650e94: r0 = true
    //     0x650e94: add             x0, NULL, #0x20  ; true
    // 0x650e98: tbz             w0, #4, #0x650eac
    // 0x650e9c: ldur            x4, [fp, #-8]
    // 0x650ea0: ldur            x2, [fp, #-0x18]
    // 0x650ea4: ldur            x3, [fp, #-0x10]
    // 0x650ea8: b               #0x650da8
    // 0x650eac: ldur            x2, [fp, #-0x18]
    // 0x650eb0: r0 = LoadClassIdInstr(r2)
    //     0x650eb0: ldur            x0, [x2, #-1]
    //     0x650eb4: ubfx            x0, x0, #0xc, #0x14
    // 0x650eb8: mov             x1, x2
    // 0x650ebc: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x650ebc: movz            x17, #0x384d
    //     0x650ec0: movk            x17, #0x1, lsl #16
    //     0x650ec4: add             lr, x0, x17
    //     0x650ec8: ldr             lr, [x21, lr, lsl #3]
    //     0x650ecc: blr             lr
    // 0x650ed0: ldur            x2, [fp, #-0x10]
    // 0x650ed4: mov             x3, x0
    // 0x650ed8: r1 = Null
    //     0x650ed8: mov             x1, NULL
    // 0x650edc: stur            x3, [fp, #-0x20]
    // 0x650ee0: cmp             w2, NULL
    // 0x650ee4: b.eq            #0x650f00
    // 0x650ee8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x650ee8: ldur            w4, [x2, #0x17]
    // 0x650eec: DecompressPointer r4
    //     0x650eec: add             x4, x4, HEAP, lsl #32
    // 0x650ef0: r8 = X0
    //     0x650ef0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x650ef4: LoadField: r9 = r4->field_7
    //     0x650ef4: ldur            x9, [x4, #7]
    // 0x650ef8: r3 = Null
    //     0x650ef8: ldr             x3, [PP, #0x23e8]  ; [pp+0x23e8] Null
    // 0x650efc: blr             x9
    // 0x650f00: ldur            x4, [fp, #-0x20]
    // 0x650f04: LoadField: r0 = r4->field_6b
    //     0x650f04: ldur            w0, [x4, #0x6b]
    // 0x650f08: DecompressPointer r0
    //     0x650f08: add             x0, x0, HEAP, lsl #32
    // 0x650f0c: stur            x0, [fp, #-0x28]
    // 0x650f10: LoadField: r1 = r0->field_b
    //     0x650f10: ldur            w1, [x0, #0xb]
    // 0x650f14: r2 = LoadInt32Instr(r1)
    //     0x650f14: sbfx            x2, x1, #1, #0x1f
    // 0x650f18: LoadField: r1 = r0->field_f
    //     0x650f18: ldur            w1, [x0, #0xf]
    // 0x650f1c: DecompressPointer r1
    //     0x650f1c: add             x1, x1, HEAP, lsl #32
    // 0x650f20: ldur            x3, [fp, #-8]
    // 0x650f24: r5 = 0
    //     0x650f24: movz            x5, #0
    // 0x650f28: CheckStackOverflow
    //     0x650f28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x650f2c: cmp             SP, x16
    //     0x650f30: b.ls            #0x651018
    // 0x650f34: cmp             x5, x2
    // 0x650f38: b.ge            #0x650f68
    // 0x650f3c: ArrayLoad: r6 = r1[r5]  ; Unknown_4
    //     0x650f3c: add             x16, x1, x5, lsl #2
    //     0x650f40: ldur            w6, [x16, #0xf]
    // 0x650f44: DecompressPointer r6
    //     0x650f44: add             x6, x6, HEAP, lsl #32
    // 0x650f48: cmp             w6, w3
    // 0x650f4c: b.eq            #0x650f5c
    // 0x650f50: add             x6, x5, #1
    // 0x650f54: mov             x5, x6
    // 0x650f58: b               #0x650f28
    // 0x650f5c: mov             x1, x0
    // 0x650f60: mov             x2, x5
    // 0x650f64: r0 = removeAt()
    //     0x650f64: bl              #0xa8d758  ; [dart:core] _GrowableList::removeAt
    // 0x650f68: ldur            x0, [fp, #-0x28]
    // 0x650f6c: LoadField: r1 = r0->field_b
    //     0x650f6c: ldur            w1, [x0, #0xb]
    // 0x650f70: LoadField: r2 = r0->field_f
    //     0x650f70: ldur            w2, [x0, #0xf]
    // 0x650f74: DecompressPointer r2
    //     0x650f74: add             x2, x2, HEAP, lsl #32
    // 0x650f78: LoadField: r3 = r2->field_b
    //     0x650f78: ldur            w3, [x2, #0xb]
    // 0x650f7c: r2 = LoadInt32Instr(r1)
    //     0x650f7c: sbfx            x2, x1, #1, #0x1f
    // 0x650f80: stur            x2, [fp, #-0x30]
    // 0x650f84: r1 = LoadInt32Instr(r3)
    //     0x650f84: sbfx            x1, x3, #1, #0x1f
    // 0x650f88: cmp             x2, x1
    // 0x650f8c: b.ne            #0x650f98
    // 0x650f90: mov             x1, x0
    // 0x650f94: r0 = _growToNextCapacity()
    //     0x650f94: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x650f98: ldur            x2, [fp, #-0x28]
    // 0x650f9c: ldur            x3, [fp, #-0x30]
    // 0x650fa0: add             x4, x3, #1
    // 0x650fa4: lsl             x5, x4, #1
    // 0x650fa8: StoreField: r2->field_b = r5
    //     0x650fa8: stur            w5, [x2, #0xb]
    // 0x650fac: LoadField: r1 = r2->field_f
    //     0x650fac: ldur            w1, [x2, #0xf]
    // 0x650fb0: DecompressPointer r1
    //     0x650fb0: add             x1, x1, HEAP, lsl #32
    // 0x650fb4: ldur            x0, [fp, #-8]
    // 0x650fb8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x650fb8: add             x25, x1, x3, lsl #2
    //     0x650fbc: add             x25, x25, #0xf
    //     0x650fc0: str             w0, [x25]
    //     0x650fc4: tbz             w0, #0, #0x650fe0
    //     0x650fc8: ldurb           w16, [x1, #-1]
    //     0x650fcc: ldurb           w17, [x0, #-1]
    //     0x650fd0: and             x16, x17, x16, lsr #2
    //     0x650fd4: tst             x16, HEAP, lsr #32
    //     0x650fd8: b.eq            #0x650fe0
    //     0x650fdc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x650fe0: ldur            x4, [fp, #-0x20]
    // 0x650fe4: ldur            x2, [fp, #-0x18]
    // 0x650fe8: ldur            x3, [fp, #-0x10]
    // 0x650fec: b               #0x650d98
    // 0x650ff0: r0 = Null
    //     0x650ff0: mov             x0, NULL
    // 0x650ff4: LeaveFrame
    //     0x650ff4: mov             SP, fp
    //     0x650ff8: ldp             fp, lr, [SP], #0x10
    // 0x650ffc: ret
    //     0x650ffc: ret             
    // 0x651000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651000: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x651004: b               #0x650d5c
    // 0x651008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651008: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65100c: b               #0x650da8
    // 0x651010: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651010: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x651014: b               #0x650db4
    // 0x651018: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651018: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65101c: b               #0x650f34
  }
  get _ ancestors(/* No info */) {
    // ** addr: 0x65105c, size: 0x144
    // 0x65105c: EnterFrame
    //     0x65105c: stp             fp, lr, [SP, #-0x10]!
    //     0x651060: mov             fp, SP
    // 0x651064: AllocStack(0x20)
    //     0x651064: sub             SP, SP, #0x20
    // 0x651068: SetupParameters(FocusNode this /* r1 => r0, fp-0x8 */)
    //     0x651068: mov             x0, x1
    //     0x65106c: stur            x1, [fp, #-8]
    // 0x651070: CheckStackOverflow
    //     0x651070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x651074: cmp             SP, x16
    //     0x651078: b.ls            #0x651190
    // 0x65107c: LoadField: r1 = r0->field_43
    //     0x65107c: ldur            w1, [x0, #0x43]
    // 0x651080: DecompressPointer r1
    //     0x651080: add             x1, x1, HEAP, lsl #32
    // 0x651084: cmp             w1, NULL
    // 0x651088: b.ne            #0x651180
    // 0x65108c: r1 = <FocusNode>
    //     0x65108c: ldr             x1, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x651090: r2 = 0
    //     0x651090: movz            x2, #0
    // 0x651094: r0 = _GrowableList()
    //     0x651094: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x651098: mov             x2, x0
    // 0x65109c: ldur            x0, [fp, #-8]
    // 0x6510a0: stur            x2, [fp, #-0x20]
    // 0x6510a4: LoadField: r1 = r0->field_4f
    //     0x6510a4: ldur            w1, [x0, #0x4f]
    // 0x6510a8: DecompressPointer r1
    //     0x6510a8: add             x1, x1, HEAP, lsl #32
    // 0x6510ac: mov             x3, x1
    // 0x6510b0: stur            x3, [fp, #-0x18]
    // 0x6510b4: CheckStackOverflow
    //     0x6510b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6510b8: cmp             SP, x16
    //     0x6510bc: b.ls            #0x651198
    // 0x6510c0: cmp             w3, NULL
    // 0x6510c4: b.eq            #0x651154
    // 0x6510c8: LoadField: r1 = r2->field_b
    //     0x6510c8: ldur            w1, [x2, #0xb]
    // 0x6510cc: LoadField: r4 = r2->field_f
    //     0x6510cc: ldur            w4, [x2, #0xf]
    // 0x6510d0: DecompressPointer r4
    //     0x6510d0: add             x4, x4, HEAP, lsl #32
    // 0x6510d4: LoadField: r5 = r4->field_b
    //     0x6510d4: ldur            w5, [x4, #0xb]
    // 0x6510d8: r4 = LoadInt32Instr(r1)
    //     0x6510d8: sbfx            x4, x1, #1, #0x1f
    // 0x6510dc: stur            x4, [fp, #-0x10]
    // 0x6510e0: r1 = LoadInt32Instr(r5)
    //     0x6510e0: sbfx            x1, x5, #1, #0x1f
    // 0x6510e4: cmp             x4, x1
    // 0x6510e8: b.ne            #0x6510f4
    // 0x6510ec: mov             x1, x2
    // 0x6510f0: r0 = _growToNextCapacity()
    //     0x6510f0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6510f4: ldur            x2, [fp, #-0x20]
    // 0x6510f8: ldur            x3, [fp, #-0x18]
    // 0x6510fc: ldur            x4, [fp, #-0x10]
    // 0x651100: add             x5, x4, #1
    // 0x651104: lsl             x6, x5, #1
    // 0x651108: StoreField: r2->field_b = r6
    //     0x651108: stur            w6, [x2, #0xb]
    // 0x65110c: LoadField: r1 = r2->field_f
    //     0x65110c: ldur            w1, [x2, #0xf]
    // 0x651110: DecompressPointer r1
    //     0x651110: add             x1, x1, HEAP, lsl #32
    // 0x651114: mov             x0, x3
    // 0x651118: ArrayStore: r1[r4] = r0  ; List_4
    //     0x651118: add             x25, x1, x4, lsl #2
    //     0x65111c: add             x25, x25, #0xf
    //     0x651120: str             w0, [x25]
    //     0x651124: tbz             w0, #0, #0x651140
    //     0x651128: ldurb           w16, [x1, #-1]
    //     0x65112c: ldurb           w17, [x0, #-1]
    //     0x651130: and             x16, x17, x16, lsr #2
    //     0x651134: tst             x16, HEAP, lsr #32
    //     0x651138: b.eq            #0x651140
    //     0x65113c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x651140: LoadField: r0 = r3->field_4f
    //     0x651140: ldur            w0, [x3, #0x4f]
    // 0x651144: DecompressPointer r0
    //     0x651144: add             x0, x0, HEAP, lsl #32
    // 0x651148: mov             x3, x0
    // 0x65114c: ldur            x0, [fp, #-8]
    // 0x651150: b               #0x6510b0
    // 0x651154: mov             x3, x0
    // 0x651158: mov             x0, x2
    // 0x65115c: StoreField: r3->field_43 = r0
    //     0x65115c: stur            w0, [x3, #0x43]
    //     0x651160: ldurb           w16, [x3, #-1]
    //     0x651164: ldurb           w17, [x0, #-1]
    //     0x651168: and             x16, x17, x16, lsr #2
    //     0x65116c: tst             x16, HEAP, lsr #32
    //     0x651170: b.eq            #0x651178
    //     0x651174: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x651178: mov             x0, x2
    // 0x65117c: b               #0x651184
    // 0x651180: mov             x0, x1
    // 0x651184: LeaveFrame
    //     0x651184: mov             SP, fp
    //     0x651188: ldp             fp, lr, [SP], #0x10
    // 0x65118c: ret
    //     0x65118c: ret             
    // 0x651190: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651190: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x651194: b               #0x65107c
    // 0x651198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65119c: b               #0x6510c0
  }
  get _ hasFocus(/* No info */) {
    // ** addr: 0x6511a0, size: 0xa0
    // 0x6511a0: EnterFrame
    //     0x6511a0: stp             fp, lr, [SP, #-0x10]!
    //     0x6511a4: mov             fp, SP
    // 0x6511a8: AllocStack(0x8)
    //     0x6511a8: sub             SP, SP, #8
    // 0x6511ac: SetupParameters(FocusNode this /* r1 => r0, fp-0x8 */)
    //     0x6511ac: mov             x0, x1
    //     0x6511b0: stur            x1, [fp, #-8]
    // 0x6511b4: CheckStackOverflow
    //     0x6511b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6511b8: cmp             SP, x16
    //     0x6511bc: b.ls            #0x651238
    // 0x6511c0: mov             x1, x0
    // 0x6511c4: r0 = hasPrimaryFocus()
    //     0x6511c4: bl              #0x651240  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::hasPrimaryFocus
    // 0x6511c8: tbnz            w0, #4, #0x6511d4
    // 0x6511cc: r0 = true
    //     0x6511cc: add             x0, NULL, #0x20  ; true
    // 0x6511d0: b               #0x65122c
    // 0x6511d4: ldur            x2, [fp, #-8]
    // 0x6511d8: LoadField: r0 = r2->field_3f
    //     0x6511d8: ldur            w0, [x2, #0x3f]
    // 0x6511dc: DecompressPointer r0
    //     0x6511dc: add             x0, x0, HEAP, lsl #32
    // 0x6511e0: cmp             w0, NULL
    // 0x6511e4: b.ne            #0x6511f0
    // 0x6511e8: r1 = Null
    //     0x6511e8: mov             x1, NULL
    // 0x6511ec: b               #0x65121c
    // 0x6511f0: LoadField: r1 = r0->field_2b
    //     0x6511f0: ldur            w1, [x0, #0x2b]
    // 0x6511f4: DecompressPointer r1
    //     0x6511f4: add             x1, x1, HEAP, lsl #32
    // 0x6511f8: cmp             w1, NULL
    // 0x6511fc: b.ne            #0x651208
    // 0x651200: r1 = Null
    //     0x651200: mov             x1, NULL
    // 0x651204: b               #0x65121c
    // 0x651208: r0 = ancestors()
    //     0x651208: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x65120c: mov             x1, x0
    // 0x651210: ldur            x2, [fp, #-8]
    // 0x651214: r0 = contains()
    //     0x651214: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0x651218: mov             x1, x0
    // 0x65121c: cmp             w1, NULL
    // 0x651220: b.ne            #0x651228
    // 0x651224: r1 = false
    //     0x651224: add             x1, NULL, #0x30  ; false
    // 0x651228: mov             x0, x1
    // 0x65122c: LeaveFrame
    //     0x65122c: mov             SP, fp
    //     0x651230: ldp             fp, lr, [SP], #0x10
    // 0x651234: ret
    //     0x651234: ret             
    // 0x651238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651238: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65123c: b               #0x6511c0
  }
  get _ hasPrimaryFocus(/* No info */) {
    // ** addr: 0x651240, size: 0x6c
    // 0x651240: EnterFrame
    //     0x651240: stp             fp, lr, [SP, #-0x10]!
    //     0x651244: mov             fp, SP
    // 0x651248: AllocStack(0x10)
    //     0x651248: sub             SP, SP, #0x10
    // 0x65124c: CheckStackOverflow
    //     0x65124c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x651250: cmp             SP, x16
    //     0x651254: b.ls            #0x6512a4
    // 0x651258: LoadField: r0 = r1->field_3f
    //     0x651258: ldur            w0, [x1, #0x3f]
    // 0x65125c: DecompressPointer r0
    //     0x65125c: add             x0, x0, HEAP, lsl #32
    // 0x651260: cmp             w0, NULL
    // 0x651264: b.ne            #0x651270
    // 0x651268: r0 = Null
    //     0x651268: mov             x0, NULL
    // 0x65126c: b               #0x65127c
    // 0x651270: LoadField: r2 = r0->field_2b
    //     0x651270: ldur            w2, [x0, #0x2b]
    // 0x651274: DecompressPointer r2
    //     0x651274: add             x2, x2, HEAP, lsl #32
    // 0x651278: mov             x0, x2
    // 0x65127c: r2 = LoadClassIdInstr(r0)
    //     0x65127c: ldur            x2, [x0, #-1]
    //     0x651280: ubfx            x2, x2, #0xc, #0x14
    // 0x651284: stp             x1, x0, [SP]
    // 0x651288: mov             x0, x2
    // 0x65128c: mov             lr, x0
    // 0x651290: ldr             lr, [x21, lr, lsl #3]
    // 0x651294: blr             lr
    // 0x651298: LeaveFrame
    //     0x651298: mov             SP, fp
    //     0x65129c: ldp             fp, lr, [SP], #0x10
    // 0x6512a0: ret
    //     0x6512a0: ret             
    // 0x6512a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6512a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6512a8: b               #0x651258
  }
  _ _reparent(/* No info */) {
    // ** addr: 0x6512ac, size: 0x348
    // 0x6512ac: EnterFrame
    //     0x6512ac: stp             fp, lr, [SP, #-0x10]!
    //     0x6512b0: mov             fp, SP
    // 0x6512b4: AllocStack(0x58)
    //     0x6512b4: sub             SP, SP, #0x58
    // 0x6512b8: SetupParameters(FocusNode this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x6512b8: stur            x1, [fp, #-8]
    //     0x6512bc: mov             x16, x2
    //     0x6512c0: mov             x2, x1
    //     0x6512c4: mov             x1, x16
    //     0x6512c8: stur            x1, [fp, #-0x10]
    // 0x6512cc: CheckStackOverflow
    //     0x6512cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6512d0: cmp             SP, x16
    //     0x6512d4: b.ls            #0x6515e0
    // 0x6512d8: LoadField: r0 = r1->field_4f
    //     0x6512d8: ldur            w0, [x1, #0x4f]
    // 0x6512dc: DecompressPointer r0
    //     0x6512dc: add             x0, x0, HEAP, lsl #32
    // 0x6512e0: r3 = LoadClassIdInstr(r0)
    //     0x6512e0: ldur            x3, [x0, #-1]
    //     0x6512e4: ubfx            x3, x3, #0xc, #0x14
    // 0x6512e8: stp             x2, x0, [SP]
    // 0x6512ec: mov             x0, x3
    // 0x6512f0: mov             lr, x0
    // 0x6512f4: ldr             lr, [x21, lr, lsl #3]
    // 0x6512f8: blr             lr
    // 0x6512fc: tbnz            w0, #4, #0x651310
    // 0x651300: r0 = Null
    //     0x651300: mov             x0, NULL
    // 0x651304: LeaveFrame
    //     0x651304: mov             SP, fp
    //     0x651308: ldp             fp, lr, [SP], #0x10
    // 0x65130c: ret
    //     0x65130c: ret             
    // 0x651310: ldur            x0, [fp, #-0x10]
    // 0x651314: mov             x1, x0
    // 0x651318: r0 = enclosingScope()
    //     0x651318: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x65131c: ldur            x1, [fp, #-0x10]
    // 0x651320: stur            x0, [fp, #-0x18]
    // 0x651324: r0 = hasFocus()
    //     0x651324: bl              #0x6511a0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::hasFocus
    // 0x651328: ldur            x2, [fp, #-0x10]
    // 0x65132c: stur            x0, [fp, #-0x28]
    // 0x651330: LoadField: r3 = r2->field_4f
    //     0x651330: ldur            w3, [x2, #0x4f]
    // 0x651334: DecompressPointer r3
    //     0x651334: add             x3, x3, HEAP, lsl #32
    // 0x651338: stur            x3, [fp, #-0x20]
    // 0x65133c: cmp             w3, NULL
    // 0x651340: b.eq            #0x651394
    // 0x651344: ldur            x4, [fp, #-8]
    // 0x651348: r1 = LoadClassIdInstr(r4)
    //     0x651348: ldur            x1, [x4, #-1]
    //     0x65134c: ubfx            x1, x1, #0xc, #0x14
    // 0x651350: sub             x16, x1, #0xb67
    // 0x651354: cmp             x16, #1
    // 0x651358: b.hi            #0x651368
    // 0x65135c: mov             x1, x4
    // 0x651360: r0 = enclosingScope()
    //     0x651360: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x651364: b               #0x65136c
    // 0x651368: ldur            x0, [fp, #-8]
    // 0x65136c: ldur            x3, [fp, #-0x18]
    // 0x651370: cmp             w3, w0
    // 0x651374: r16 = true
    //     0x651374: add             x16, NULL, #0x20  ; true
    // 0x651378: r17 = false
    //     0x651378: add             x17, NULL, #0x30  ; false
    // 0x65137c: csel            x1, x16, x17, ne
    // 0x651380: str             x1, [SP]
    // 0x651384: ldur            x1, [fp, #-0x20]
    // 0x651388: ldur            x2, [fp, #-0x10]
    // 0x65138c: r4 = const [0, 0x3, 0x1, 0x2, removeScopeFocus, 0x2, null]
    //     0x65138c: ldr             x4, [PP, #0x2470]  ; [pp+0x2470] List(7) [0, 0x3, 0x1, 0x2, "removeScopeFocus", 0x2, Null]
    // 0x651390: r0 = _removeChild()
    //     0x651390: bl              #0x651e74  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_removeChild
    // 0x651394: ldur            x0, [fp, #-8]
    // 0x651398: LoadField: r2 = r0->field_53
    //     0x651398: ldur            w2, [x0, #0x53]
    // 0x65139c: DecompressPointer r2
    //     0x65139c: add             x2, x2, HEAP, lsl #32
    // 0x6513a0: stur            x2, [fp, #-0x20]
    // 0x6513a4: LoadField: r1 = r2->field_b
    //     0x6513a4: ldur            w1, [x2, #0xb]
    // 0x6513a8: LoadField: r3 = r2->field_f
    //     0x6513a8: ldur            w3, [x2, #0xf]
    // 0x6513ac: DecompressPointer r3
    //     0x6513ac: add             x3, x3, HEAP, lsl #32
    // 0x6513b0: LoadField: r4 = r3->field_b
    //     0x6513b0: ldur            w4, [x3, #0xb]
    // 0x6513b4: r3 = LoadInt32Instr(r1)
    //     0x6513b4: sbfx            x3, x1, #1, #0x1f
    // 0x6513b8: stur            x3, [fp, #-0x30]
    // 0x6513bc: r1 = LoadInt32Instr(r4)
    //     0x6513bc: sbfx            x1, x4, #1, #0x1f
    // 0x6513c0: cmp             x3, x1
    // 0x6513c4: b.ne            #0x6513d0
    // 0x6513c8: mov             x1, x2
    // 0x6513cc: r0 = _growToNextCapacity()
    //     0x6513cc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6513d0: ldur            x3, [fp, #-8]
    // 0x6513d4: ldur            x4, [fp, #-0x10]
    // 0x6513d8: ldur            x0, [fp, #-0x20]
    // 0x6513dc: ldur            x2, [fp, #-0x30]
    // 0x6513e0: add             x1, x2, #1
    // 0x6513e4: lsl             x5, x1, #1
    // 0x6513e8: StoreField: r0->field_b = r5
    //     0x6513e8: stur            w5, [x0, #0xb]
    // 0x6513ec: LoadField: r1 = r0->field_f
    //     0x6513ec: ldur            w1, [x0, #0xf]
    // 0x6513f0: DecompressPointer r1
    //     0x6513f0: add             x1, x1, HEAP, lsl #32
    // 0x6513f4: mov             x0, x4
    // 0x6513f8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6513f8: add             x25, x1, x2, lsl #2
    //     0x6513fc: add             x25, x25, #0xf
    //     0x651400: str             w0, [x25]
    //     0x651404: tbz             w0, #0, #0x651420
    //     0x651408: ldurb           w16, [x1, #-1]
    //     0x65140c: ldurb           w17, [x0, #-1]
    //     0x651410: and             x16, x17, x16, lsr #2
    //     0x651414: tst             x16, HEAP, lsr #32
    //     0x651418: b.eq            #0x651420
    //     0x65141c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x651420: mov             x0, x3
    // 0x651424: StoreField: r4->field_4f = r0
    //     0x651424: stur            w0, [x4, #0x4f]
    //     0x651428: ldurb           w16, [x4, #-1]
    //     0x65142c: ldurb           w17, [x0, #-1]
    //     0x651430: and             x16, x17, x16, lsr #2
    //     0x651434: tst             x16, HEAP, lsr #32
    //     0x651438: b.eq            #0x651440
    //     0x65143c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x651440: StoreField: r4->field_43 = rNULL
    //     0x651440: stur            NULL, [x4, #0x43]
    // 0x651444: LoadField: r2 = r3->field_3f
    //     0x651444: ldur            w2, [x3, #0x3f]
    // 0x651448: DecompressPointer r2
    //     0x651448: add             x2, x2, HEAP, lsl #32
    // 0x65144c: mov             x1, x4
    // 0x651450: r0 = _updateManager()
    //     0x651450: bl              #0x651b9c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_updateManager
    // 0x651454: ldur            x1, [fp, #-0x10]
    // 0x651458: r0 = ancestors()
    //     0x651458: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x65145c: LoadField: r3 = r0->field_7
    //     0x65145c: ldur            w3, [x0, #7]
    // 0x651460: DecompressPointer r3
    //     0x651460: add             x3, x3, HEAP, lsl #32
    // 0x651464: stur            x3, [fp, #-0x48]
    // 0x651468: LoadField: r1 = r0->field_b
    //     0x651468: ldur            w1, [x0, #0xb]
    // 0x65146c: r4 = LoadInt32Instr(r1)
    //     0x65146c: sbfx            x4, x1, #1, #0x1f
    // 0x651470: stur            x4, [fp, #-0x40]
    // 0x651474: LoadField: r5 = r0->field_f
    //     0x651474: ldur            w5, [x0, #0xf]
    // 0x651478: DecompressPointer r5
    //     0x651478: add             x5, x5, HEAP, lsl #32
    // 0x65147c: stur            x5, [fp, #-0x38]
    // 0x651480: r0 = 0
    //     0x651480: movz            x0, #0
    // 0x651484: CheckStackOverflow
    //     0x651484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x651488: cmp             SP, x16
    //     0x65148c: b.ls            #0x6515e8
    // 0x651490: cmp             x0, x4
    // 0x651494: b.ge            #0x651500
    // 0x651498: ArrayLoad: r6 = r5[r0]  ; Unknown_4
    //     0x651498: add             x16, x5, x0, lsl #2
    //     0x65149c: ldur            w6, [x16, #0xf]
    // 0x6514a0: DecompressPointer r6
    //     0x6514a0: add             x6, x6, HEAP, lsl #32
    // 0x6514a4: stur            x6, [fp, #-0x20]
    // 0x6514a8: add             x7, x0, #1
    // 0x6514ac: stur            x7, [fp, #-0x30]
    // 0x6514b0: cmp             w6, NULL
    // 0x6514b4: b.ne            #0x6514e4
    // 0x6514b8: mov             x0, x6
    // 0x6514bc: mov             x2, x3
    // 0x6514c0: r1 = Null
    //     0x6514c0: mov             x1, NULL
    // 0x6514c4: cmp             w2, NULL
    // 0x6514c8: b.eq            #0x6514e4
    // 0x6514cc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6514cc: ldur            w4, [x2, #0x17]
    // 0x6514d0: DecompressPointer r4
    //     0x6514d0: add             x4, x4, HEAP, lsl #32
    // 0x6514d4: r8 = X0
    //     0x6514d4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6514d8: LoadField: r9 = r4->field_7
    //     0x6514d8: ldur            x9, [x4, #7]
    // 0x6514dc: r3 = Null
    //     0x6514dc: ldr             x3, [PP, #0x2478]  ; [pp+0x2478] Null
    // 0x6514e0: blr             x9
    // 0x6514e4: ldur            x0, [fp, #-0x20]
    // 0x6514e8: StoreField: r0->field_47 = rNULL
    //     0x6514e8: stur            NULL, [x0, #0x47]
    // 0x6514ec: ldur            x0, [fp, #-0x30]
    // 0x6514f0: ldur            x3, [fp, #-0x48]
    // 0x6514f4: ldur            x5, [fp, #-0x38]
    // 0x6514f8: ldur            x4, [fp, #-0x40]
    // 0x6514fc: b               #0x651484
    // 0x651500: ldur            x0, [fp, #-0x28]
    // 0x651504: tbnz            w0, #4, #0x651534
    // 0x651508: ldur            x0, [fp, #-8]
    // 0x65150c: LoadField: r1 = r0->field_3f
    //     0x65150c: ldur            w1, [x0, #0x3f]
    // 0x651510: DecompressPointer r1
    //     0x651510: add             x1, x1, HEAP, lsl #32
    // 0x651514: cmp             w1, NULL
    // 0x651518: b.eq            #0x651534
    // 0x65151c: LoadField: r0 = r1->field_2b
    //     0x65151c: ldur            w0, [x1, #0x2b]
    // 0x651520: DecompressPointer r0
    //     0x651520: add             x0, x0, HEAP, lsl #32
    // 0x651524: cmp             w0, NULL
    // 0x651528: b.eq            #0x651534
    // 0x65152c: mov             x1, x0
    // 0x651530: r0 = _setAsFocusedChildForScope()
    //     0x651530: bl              #0x650d3c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_setAsFocusedChildForScope
    // 0x651534: ldur            x3, [fp, #-0x18]
    // 0x651538: cmp             w3, NULL
    // 0x65153c: b.eq            #0x651598
    // 0x651540: ldur            x0, [fp, #-0x10]
    // 0x651544: LoadField: r1 = r0->field_33
    //     0x651544: ldur            w1, [x0, #0x33]
    // 0x651548: DecompressPointer r1
    //     0x651548: add             x1, x1, HEAP, lsl #32
    // 0x65154c: cmp             w1, NULL
    // 0x651550: b.eq            #0x651598
    // 0x651554: mov             x1, x0
    // 0x651558: r0 = enclosingScope()
    //     0x651558: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x65155c: ldur            x3, [fp, #-0x18]
    // 0x651560: cmp             w0, w3
    // 0x651564: b.eq            #0x651598
    // 0x651568: ldur            x2, [fp, #-0x10]
    // 0x65156c: LoadField: r1 = r2->field_33
    //     0x65156c: ldur            w1, [x2, #0x33]
    // 0x651570: DecompressPointer r1
    //     0x651570: add             x1, x1, HEAP, lsl #32
    // 0x651574: cmp             w1, NULL
    // 0x651578: b.eq            #0x6515f0
    // 0x65157c: r0 = maybeOf()
    //     0x65157c: bl              #0x651710  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalGroup::maybeOf
    // 0x651580: cmp             w0, NULL
    // 0x651584: b.eq            #0x651598
    // 0x651588: mov             x1, x0
    // 0x65158c: ldur            x2, [fp, #-0x10]
    // 0x651590: ldur            x3, [fp, #-0x18]
    // 0x651594: r0 = changedScope()
    //     0x651594: bl              #0x6515f4  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::changedScope
    // 0x651598: ldur            x3, [fp, #-0x10]
    // 0x65159c: LoadField: r0 = r3->field_63
    //     0x65159c: ldur            w0, [x3, #0x63]
    // 0x6515a0: DecompressPointer r0
    //     0x6515a0: add             x0, x0, HEAP, lsl #32
    // 0x6515a4: tbnz            w0, #4, #0x6515d0
    // 0x6515a8: r0 = LoadClassIdInstr(r3)
    //     0x6515a8: ldur            x0, [x3, #-1]
    //     0x6515ac: ubfx            x0, x0, #0xc, #0x14
    // 0x6515b0: mov             x1, x3
    // 0x6515b4: r2 = true
    //     0x6515b4: add             x2, NULL, #0x20  ; true
    // 0x6515b8: r0 = GDT[cid_x0 + -0xffc]()
    //     0x6515b8: sub             lr, x0, #0xffc
    //     0x6515bc: ldr             lr, [x21, lr, lsl #3]
    //     0x6515c0: blr             lr
    // 0x6515c4: ldur            x1, [fp, #-0x10]
    // 0x6515c8: r2 = false
    //     0x6515c8: add             x2, NULL, #0x30  ; false
    // 0x6515cc: StoreField: r1->field_63 = r2
    //     0x6515cc: stur            w2, [x1, #0x63]
    // 0x6515d0: r0 = Null
    //     0x6515d0: mov             x0, NULL
    // 0x6515d4: LeaveFrame
    //     0x6515d4: mov             SP, fp
    //     0x6515d8: ldp             fp, lr, [SP], #0x10
    // 0x6515dc: ret
    //     0x6515dc: ret             
    // 0x6515e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6515e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6515e4: b               #0x6512d8
    // 0x6515e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6515e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6515ec: b               #0x651490
    // 0x6515f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6515f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateManager(/* No info */) {
    // ** addr: 0x651b9c, size: 0x124
    // 0x651b9c: EnterFrame
    //     0x651b9c: stp             fp, lr, [SP, #-0x10]!
    //     0x651ba0: mov             fp, SP
    // 0x651ba4: AllocStack(0x30)
    //     0x651ba4: sub             SP, SP, #0x30
    // 0x651ba8: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x651ba8: stur            x2, [fp, #-8]
    // 0x651bac: CheckStackOverflow
    //     0x651bac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x651bb0: cmp             SP, x16
    //     0x651bb4: b.ls            #0x651cb0
    // 0x651bb8: mov             x0, x2
    // 0x651bbc: StoreField: r1->field_3f = r0
    //     0x651bbc: stur            w0, [x1, #0x3f]
    //     0x651bc0: ldurb           w16, [x1, #-1]
    //     0x651bc4: ldurb           w17, [x0, #-1]
    //     0x651bc8: and             x16, x17, x16, lsr #2
    //     0x651bcc: tst             x16, HEAP, lsr #32
    //     0x651bd0: b.eq            #0x651bd8
    //     0x651bd4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x651bd8: r0 = descendants()
    //     0x651bd8: bl              #0x651cc0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::descendants
    // 0x651bdc: LoadField: r3 = r0->field_7
    //     0x651bdc: ldur            w3, [x0, #7]
    // 0x651be0: DecompressPointer r3
    //     0x651be0: add             x3, x3, HEAP, lsl #32
    // 0x651be4: stur            x3, [fp, #-0x30]
    // 0x651be8: LoadField: r1 = r0->field_b
    //     0x651be8: ldur            w1, [x0, #0xb]
    // 0x651bec: r4 = LoadInt32Instr(r1)
    //     0x651bec: sbfx            x4, x1, #1, #0x1f
    // 0x651bf0: stur            x4, [fp, #-0x28]
    // 0x651bf4: LoadField: r5 = r0->field_f
    //     0x651bf4: ldur            w5, [x0, #0xf]
    // 0x651bf8: DecompressPointer r5
    //     0x651bf8: add             x5, x5, HEAP, lsl #32
    // 0x651bfc: stur            x5, [fp, #-0x20]
    // 0x651c00: r0 = 0
    //     0x651c00: movz            x0, #0
    // 0x651c04: CheckStackOverflow
    //     0x651c04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x651c08: cmp             SP, x16
    //     0x651c0c: b.ls            #0x651cb8
    // 0x651c10: cmp             x0, x4
    // 0x651c14: b.ge            #0x651ca0
    // 0x651c18: ArrayLoad: r6 = r5[r0]  ; Unknown_4
    //     0x651c18: add             x16, x5, x0, lsl #2
    //     0x651c1c: ldur            w6, [x16, #0xf]
    // 0x651c20: DecompressPointer r6
    //     0x651c20: add             x6, x6, HEAP, lsl #32
    // 0x651c24: stur            x6, [fp, #-0x18]
    // 0x651c28: add             x7, x0, #1
    // 0x651c2c: stur            x7, [fp, #-0x10]
    // 0x651c30: cmp             w6, NULL
    // 0x651c34: b.ne            #0x651c64
    // 0x651c38: mov             x0, x6
    // 0x651c3c: mov             x2, x3
    // 0x651c40: r1 = Null
    //     0x651c40: mov             x1, NULL
    // 0x651c44: cmp             w2, NULL
    // 0x651c48: b.eq            #0x651c64
    // 0x651c4c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x651c4c: ldur            w4, [x2, #0x17]
    // 0x651c50: DecompressPointer r4
    //     0x651c50: add             x4, x4, HEAP, lsl #32
    // 0x651c54: r8 = X0
    //     0x651c54: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x651c58: LoadField: r9 = r4->field_7
    //     0x651c58: ldur            x9, [x4, #7]
    // 0x651c5c: r3 = Null
    //     0x651c5c: ldr             x3, [PP, #0x2550]  ; [pp+0x2550] Null
    // 0x651c60: blr             x9
    // 0x651c64: ldur            x1, [fp, #-0x18]
    // 0x651c68: ldur            x0, [fp, #-8]
    // 0x651c6c: StoreField: r1->field_3f = r0
    //     0x651c6c: stur            w0, [x1, #0x3f]
    //     0x651c70: ldurb           w16, [x1, #-1]
    //     0x651c74: ldurb           w17, [x0, #-1]
    //     0x651c78: and             x16, x17, x16, lsr #2
    //     0x651c7c: tst             x16, HEAP, lsr #32
    //     0x651c80: b.eq            #0x651c88
    //     0x651c84: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x651c88: StoreField: r1->field_43 = rNULL
    //     0x651c88: stur            NULL, [x1, #0x43]
    // 0x651c8c: ldur            x0, [fp, #-0x10]
    // 0x651c90: ldur            x3, [fp, #-0x30]
    // 0x651c94: ldur            x5, [fp, #-0x20]
    // 0x651c98: ldur            x4, [fp, #-0x28]
    // 0x651c9c: b               #0x651c04
    // 0x651ca0: r0 = Null
    //     0x651ca0: mov             x0, NULL
    // 0x651ca4: LeaveFrame
    //     0x651ca4: mov             SP, fp
    //     0x651ca8: ldp             fp, lr, [SP], #0x10
    // 0x651cac: ret
    //     0x651cac: ret             
    // 0x651cb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651cb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x651cb4: b               #0x651bb8
    // 0x651cb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651cb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x651cbc: b               #0x651c10
  }
  get _ descendants(/* No info */) {
    // ** addr: 0x651cc0, size: 0x1b4
    // 0x651cc0: EnterFrame
    //     0x651cc0: stp             fp, lr, [SP, #-0x10]!
    //     0x651cc4: mov             fp, SP
    // 0x651cc8: AllocStack(0x38)
    //     0x651cc8: sub             SP, SP, #0x38
    // 0x651ccc: SetupParameters(FocusNode this /* r1 => r0, fp-0x8 */)
    //     0x651ccc: mov             x0, x1
    //     0x651cd0: stur            x1, [fp, #-8]
    // 0x651cd4: CheckStackOverflow
    //     0x651cd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x651cd8: cmp             SP, x16
    //     0x651cdc: b.ls            #0x651e64
    // 0x651ce0: LoadField: r1 = r0->field_47
    //     0x651ce0: ldur            w1, [x0, #0x47]
    // 0x651ce4: DecompressPointer r1
    //     0x651ce4: add             x1, x1, HEAP, lsl #32
    // 0x651ce8: cmp             w1, NULL
    // 0x651cec: b.ne            #0x651e34
    // 0x651cf0: r1 = <FocusNode>
    //     0x651cf0: ldr             x1, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x651cf4: r2 = 0
    //     0x651cf4: movz            x2, #0
    // 0x651cf8: r0 = _GrowableList()
    //     0x651cf8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x651cfc: mov             x2, x0
    // 0x651d00: ldur            x0, [fp, #-8]
    // 0x651d04: stur            x2, [fp, #-0x30]
    // 0x651d08: LoadField: r3 = r0->field_53
    //     0x651d08: ldur            w3, [x0, #0x53]
    // 0x651d0c: DecompressPointer r3
    //     0x651d0c: add             x3, x3, HEAP, lsl #32
    // 0x651d10: stur            x3, [fp, #-0x28]
    // 0x651d14: LoadField: r1 = r3->field_b
    //     0x651d14: ldur            w1, [x3, #0xb]
    // 0x651d18: r4 = LoadInt32Instr(r1)
    //     0x651d18: sbfx            x4, x1, #1, #0x1f
    // 0x651d1c: stur            x4, [fp, #-0x20]
    // 0x651d20: r1 = 0
    //     0x651d20: movz            x1, #0
    // 0x651d24: CheckStackOverflow
    //     0x651d24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x651d28: cmp             SP, x16
    //     0x651d2c: b.ls            #0x651e6c
    // 0x651d30: LoadField: r5 = r3->field_b
    //     0x651d30: ldur            w5, [x3, #0xb]
    // 0x651d34: r6 = LoadInt32Instr(r5)
    //     0x651d34: sbfx            x6, x5, #1, #0x1f
    // 0x651d38: cmp             x4, x6
    // 0x651d3c: b.ne            #0x651e44
    // 0x651d40: cmp             x1, x6
    // 0x651d44: b.ge            #0x651e08
    // 0x651d48: LoadField: r5 = r3->field_f
    //     0x651d48: ldur            w5, [x3, #0xf]
    // 0x651d4c: DecompressPointer r5
    //     0x651d4c: add             x5, x5, HEAP, lsl #32
    // 0x651d50: ArrayLoad: r6 = r5[r1]  ; Unknown_4
    //     0x651d50: add             x16, x5, x1, lsl #2
    //     0x651d54: ldur            w6, [x16, #0xf]
    // 0x651d58: DecompressPointer r6
    //     0x651d58: add             x6, x6, HEAP, lsl #32
    // 0x651d5c: stur            x6, [fp, #-0x18]
    // 0x651d60: add             x5, x1, #1
    // 0x651d64: mov             x1, x6
    // 0x651d68: stur            x5, [fp, #-0x10]
    // 0x651d6c: r0 = descendants()
    //     0x651d6c: bl              #0x651cc0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::descendants
    // 0x651d70: ldur            x1, [fp, #-0x30]
    // 0x651d74: mov             x2, x0
    // 0x651d78: r0 = addAll()
    //     0x651d78: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x651d7c: ldur            x0, [fp, #-0x30]
    // 0x651d80: LoadField: r1 = r0->field_b
    //     0x651d80: ldur            w1, [x0, #0xb]
    // 0x651d84: LoadField: r2 = r0->field_f
    //     0x651d84: ldur            w2, [x0, #0xf]
    // 0x651d88: DecompressPointer r2
    //     0x651d88: add             x2, x2, HEAP, lsl #32
    // 0x651d8c: LoadField: r3 = r2->field_b
    //     0x651d8c: ldur            w3, [x2, #0xb]
    // 0x651d90: r2 = LoadInt32Instr(r1)
    //     0x651d90: sbfx            x2, x1, #1, #0x1f
    // 0x651d94: stur            x2, [fp, #-0x38]
    // 0x651d98: r1 = LoadInt32Instr(r3)
    //     0x651d98: sbfx            x1, x3, #1, #0x1f
    // 0x651d9c: cmp             x2, x1
    // 0x651da0: b.ne            #0x651dac
    // 0x651da4: mov             x1, x0
    // 0x651da8: r0 = _growToNextCapacity()
    //     0x651da8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x651dac: ldur            x2, [fp, #-0x30]
    // 0x651db0: ldur            x3, [fp, #-0x38]
    // 0x651db4: add             x0, x3, #1
    // 0x651db8: lsl             x1, x0, #1
    // 0x651dbc: StoreField: r2->field_b = r1
    //     0x651dbc: stur            w1, [x2, #0xb]
    // 0x651dc0: LoadField: r1 = r2->field_f
    //     0x651dc0: ldur            w1, [x2, #0xf]
    // 0x651dc4: DecompressPointer r1
    //     0x651dc4: add             x1, x1, HEAP, lsl #32
    // 0x651dc8: ldur            x0, [fp, #-0x18]
    // 0x651dcc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x651dcc: add             x25, x1, x3, lsl #2
    //     0x651dd0: add             x25, x25, #0xf
    //     0x651dd4: str             w0, [x25]
    //     0x651dd8: tbz             w0, #0, #0x651df4
    //     0x651ddc: ldurb           w16, [x1, #-1]
    //     0x651de0: ldurb           w17, [x0, #-1]
    //     0x651de4: and             x16, x17, x16, lsr #2
    //     0x651de8: tst             x16, HEAP, lsr #32
    //     0x651dec: b.eq            #0x651df4
    //     0x651df0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x651df4: ldur            x1, [fp, #-0x10]
    // 0x651df8: ldur            x0, [fp, #-8]
    // 0x651dfc: ldur            x3, [fp, #-0x28]
    // 0x651e00: ldur            x4, [fp, #-0x20]
    // 0x651e04: b               #0x651d24
    // 0x651e08: mov             x1, x0
    // 0x651e0c: mov             x0, x2
    // 0x651e10: StoreField: r1->field_47 = r0
    //     0x651e10: stur            w0, [x1, #0x47]
    //     0x651e14: ldurb           w16, [x1, #-1]
    //     0x651e18: ldurb           w17, [x0, #-1]
    //     0x651e1c: and             x16, x17, x16, lsr #2
    //     0x651e20: tst             x16, HEAP, lsr #32
    //     0x651e24: b.eq            #0x651e2c
    //     0x651e28: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x651e2c: mov             x0, x2
    // 0x651e30: b               #0x651e38
    // 0x651e34: mov             x0, x1
    // 0x651e38: LeaveFrame
    //     0x651e38: mov             SP, fp
    //     0x651e3c: ldp             fp, lr, [SP], #0x10
    // 0x651e40: ret
    //     0x651e40: ret             
    // 0x651e44: mov             x0, x3
    // 0x651e48: r0 = ConcurrentModificationError()
    //     0x651e48: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x651e4c: mov             x1, x0
    // 0x651e50: ldur            x0, [fp, #-0x28]
    // 0x651e54: StoreField: r1->field_b = r0
    //     0x651e54: stur            w0, [x1, #0xb]
    // 0x651e58: mov             x0, x1
    // 0x651e5c: r0 = Throw()
    //     0x651e5c: bl              #0xec04b8  ; ThrowStub
    // 0x651e60: brk             #0
    // 0x651e64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651e64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x651e68: b               #0x651ce0
    // 0x651e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651e6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x651e70: b               #0x651d30
  }
  _ _removeChild(/* No info */) {
    // ** addr: 0x651e74, size: 0x1e0
    // 0x651e74: EnterFrame
    //     0x651e74: stp             fp, lr, [SP, #-0x10]!
    //     0x651e78: mov             fp, SP
    // 0x651e7c: AllocStack(0x38)
    //     0x651e7c: sub             SP, SP, #0x38
    // 0x651e80: SetupParameters(FocusNode this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, {dynamic removeScopeFocus = true /* r1 */})
    //     0x651e80: mov             x0, x2
    //     0x651e84: stur            x2, [fp, #-0x10]
    //     0x651e88: mov             x2, x1
    //     0x651e8c: stur            x1, [fp, #-8]
    //     0x651e90: ldur            w1, [x4, #0x13]
    //     0x651e94: ldur            w3, [x4, #0x1f]
    //     0x651e98: add             x3, x3, HEAP, lsl #32
    //     0x651e9c: ldr             x16, [PP, #0x2560]  ; [pp+0x2560] "removeScopeFocus"
    //     0x651ea0: cmp             w3, w16
    //     0x651ea4: b.ne            #0x651ec0
    //     0x651ea8: ldur            w3, [x4, #0x23]
    //     0x651eac: add             x3, x3, HEAP, lsl #32
    //     0x651eb0: sub             w4, w1, w3
    //     0x651eb4: add             x1, fp, w4, sxtw #2
    //     0x651eb8: ldr             x1, [x1, #8]
    //     0x651ebc: b               #0x651ec4
    //     0x651ec0: add             x1, NULL, #0x20  ; true
    // 0x651ec4: CheckStackOverflow
    //     0x651ec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x651ec8: cmp             SP, x16
    //     0x651ecc: b.ls            #0x652044
    // 0x651ed0: tbnz            w1, #4, #0x651f58
    // 0x651ed4: mov             x1, x0
    // 0x651ed8: r0 = enclosingScope()
    //     0x651ed8: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x651edc: stur            x0, [fp, #-0x18]
    // 0x651ee0: r1 = 1
    //     0x651ee0: movz            x1, #0x1
    // 0x651ee4: r0 = AllocateContext()
    //     0x651ee4: bl              #0xec126c  ; AllocateContextStub
    // 0x651ee8: mov             x3, x0
    // 0x651eec: ldur            x0, [fp, #-0x18]
    // 0x651ef0: stur            x3, [fp, #-0x28]
    // 0x651ef4: StoreField: r3->field_f = r0
    //     0x651ef4: stur            w0, [x3, #0xf]
    // 0x651ef8: cmp             w0, NULL
    // 0x651efc: b.eq            #0x651f58
    // 0x651f00: LoadField: r4 = r0->field_6b
    //     0x651f00: ldur            w4, [x0, #0x6b]
    // 0x651f04: DecompressPointer r4
    //     0x651f04: add             x4, x4, HEAP, lsl #32
    // 0x651f08: mov             x1, x4
    // 0x651f0c: ldur            x2, [fp, #-0x10]
    // 0x651f10: stur            x4, [fp, #-0x20]
    // 0x651f14: r0 = remove()
    //     0x651f14: bl              #0x6ec83c  ; [dart:core] _GrowableList::remove
    // 0x651f18: ldur            x1, [fp, #-0x10]
    // 0x651f1c: r0 = descendants()
    //     0x651f1c: bl              #0x651cc0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::descendants
    // 0x651f20: ldur            x2, [fp, #-0x28]
    // 0x651f24: r1 = Function '<anonymous closure>':.
    //     0x651f24: ldr             x1, [PP, #0x2568]  ; [pp+0x2568] AnonymousClosure: (0x652160), in [package:flutter/src/widgets/focus_manager.dart] FocusNode::_removeChild (0x651e74)
    // 0x651f28: stur            x0, [fp, #-0x18]
    // 0x651f2c: r0 = AllocateClosure()
    //     0x651f2c: bl              #0xec1630  ; AllocateClosureStub
    // 0x651f30: ldur            x1, [fp, #-0x18]
    // 0x651f34: mov             x2, x0
    // 0x651f38: r0 = where()
    //     0x651f38: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x651f3c: ldur            x2, [fp, #-0x20]
    // 0x651f40: r1 = Function 'remove':.
    //     0x651f40: ldr             x1, [PP, #0x2570]  ; [pp+0x2570] AnonymousClosure: (0x651020), in [dart:core] _GrowableList::remove (0x6ec83c)
    // 0x651f44: stur            x0, [fp, #-0x18]
    // 0x651f48: r0 = AllocateClosure()
    //     0x651f48: bl              #0xec1630  ; AllocateClosureStub
    // 0x651f4c: ldur            x1, [fp, #-0x18]
    // 0x651f50: mov             x2, x0
    // 0x651f54: r0 = forEach()
    //     0x651f54: bl              #0x7e1920  ; [dart:core] Iterable::forEach
    // 0x651f58: ldur            x2, [fp, #-8]
    // 0x651f5c: ldur            x0, [fp, #-0x10]
    // 0x651f60: StoreField: r0->field_4f = rNULL
    //     0x651f60: stur            NULL, [x0, #0x4f]
    // 0x651f64: mov             x1, x0
    // 0x651f68: r0 = _clearEnclosingScopeCache()
    //     0x651f68: bl              #0x652054  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_clearEnclosingScopeCache
    // 0x651f6c: ldur            x0, [fp, #-8]
    // 0x651f70: LoadField: r1 = r0->field_53
    //     0x651f70: ldur            w1, [x0, #0x53]
    // 0x651f74: DecompressPointer r1
    //     0x651f74: add             x1, x1, HEAP, lsl #32
    // 0x651f78: ldur            x2, [fp, #-0x10]
    // 0x651f7c: r0 = remove()
    //     0x651f7c: bl              #0x6ec83c  ; [dart:core] _GrowableList::remove
    // 0x651f80: ldur            x1, [fp, #-8]
    // 0x651f84: r0 = ancestors()
    //     0x651f84: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x651f88: LoadField: r3 = r0->field_7
    //     0x651f88: ldur            w3, [x0, #7]
    // 0x651f8c: DecompressPointer r3
    //     0x651f8c: add             x3, x3, HEAP, lsl #32
    // 0x651f90: stur            x3, [fp, #-0x20]
    // 0x651f94: LoadField: r1 = r0->field_b
    //     0x651f94: ldur            w1, [x0, #0xb]
    // 0x651f98: r4 = LoadInt32Instr(r1)
    //     0x651f98: sbfx            x4, x1, #1, #0x1f
    // 0x651f9c: stur            x4, [fp, #-0x38]
    // 0x651fa0: LoadField: r5 = r0->field_f
    //     0x651fa0: ldur            w5, [x0, #0xf]
    // 0x651fa4: DecompressPointer r5
    //     0x651fa4: add             x5, x5, HEAP, lsl #32
    // 0x651fa8: stur            x5, [fp, #-0x18]
    // 0x651fac: r0 = 0
    //     0x651fac: movz            x0, #0
    // 0x651fb0: CheckStackOverflow
    //     0x651fb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x651fb4: cmp             SP, x16
    //     0x651fb8: b.ls            #0x65204c
    // 0x651fbc: cmp             x0, x4
    // 0x651fc0: b.ge            #0x65202c
    // 0x651fc4: ArrayLoad: r6 = r5[r0]  ; Unknown_4
    //     0x651fc4: add             x16, x5, x0, lsl #2
    //     0x651fc8: ldur            w6, [x16, #0xf]
    // 0x651fcc: DecompressPointer r6
    //     0x651fcc: add             x6, x6, HEAP, lsl #32
    // 0x651fd0: stur            x6, [fp, #-0x10]
    // 0x651fd4: add             x7, x0, #1
    // 0x651fd8: stur            x7, [fp, #-0x30]
    // 0x651fdc: cmp             w6, NULL
    // 0x651fe0: b.ne            #0x652010
    // 0x651fe4: mov             x0, x6
    // 0x651fe8: mov             x2, x3
    // 0x651fec: r1 = Null
    //     0x651fec: mov             x1, NULL
    // 0x651ff0: cmp             w2, NULL
    // 0x651ff4: b.eq            #0x652010
    // 0x651ff8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x651ff8: ldur            w4, [x2, #0x17]
    // 0x651ffc: DecompressPointer r4
    //     0x651ffc: add             x4, x4, HEAP, lsl #32
    // 0x652000: r8 = X0
    //     0x652000: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x652004: LoadField: r9 = r4->field_7
    //     0x652004: ldur            x9, [x4, #7]
    // 0x652008: r3 = Null
    //     0x652008: ldr             x3, [PP, #0x2578]  ; [pp+0x2578] Null
    // 0x65200c: blr             x9
    // 0x652010: ldur            x1, [fp, #-0x10]
    // 0x652014: StoreField: r1->field_47 = rNULL
    //     0x652014: stur            NULL, [x1, #0x47]
    // 0x652018: ldur            x0, [fp, #-0x30]
    // 0x65201c: ldur            x3, [fp, #-0x20]
    // 0x652020: ldur            x5, [fp, #-0x18]
    // 0x652024: ldur            x4, [fp, #-0x38]
    // 0x652028: b               #0x651fb0
    // 0x65202c: ldur            x1, [fp, #-8]
    // 0x652030: StoreField: r1->field_47 = rNULL
    //     0x652030: stur            NULL, [x1, #0x47]
    // 0x652034: r0 = Null
    //     0x652034: mov             x0, NULL
    // 0x652038: LeaveFrame
    //     0x652038: mov             SP, fp
    //     0x65203c: ldp             fp, lr, [SP], #0x10
    // 0x652040: ret
    //     0x652040: ret             
    // 0x652044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x652044: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x652048: b               #0x651ed0
    // 0x65204c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65204c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x652050: b               #0x651fbc
  }
  _ _clearEnclosingScopeCache(/* No info */) {
    // ** addr: 0x652054, size: 0x10c
    // 0x652054: EnterFrame
    //     0x652054: stp             fp, lr, [SP, #-0x10]!
    //     0x652058: mov             fp, SP
    // 0x65205c: AllocStack(0x20)
    //     0x65205c: sub             SP, SP, #0x20
    // 0x652060: CheckStackOverflow
    //     0x652060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x652064: cmp             SP, x16
    //     0x652068: b.ls            #0x652150
    // 0x65206c: LoadField: r0 = r1->field_5f
    //     0x65206c: ldur            w0, [x1, #0x5f]
    // 0x652070: DecompressPointer r0
    //     0x652070: add             x0, x0, HEAP, lsl #32
    // 0x652074: stur            x0, [fp, #-0x20]
    // 0x652078: cmp             w0, NULL
    // 0x65207c: b.ne            #0x652090
    // 0x652080: r0 = Null
    //     0x652080: mov             x0, NULL
    // 0x652084: LeaveFrame
    //     0x652084: mov             SP, fp
    //     0x652088: ldp             fp, lr, [SP], #0x10
    // 0x65208c: ret
    //     0x65208c: ret             
    // 0x652090: StoreField: r1->field_5f = rNULL
    //     0x652090: stur            NULL, [x1, #0x5f]
    // 0x652094: LoadField: r2 = r1->field_53
    //     0x652094: ldur            w2, [x1, #0x53]
    // 0x652098: DecompressPointer r2
    //     0x652098: add             x2, x2, HEAP, lsl #32
    // 0x65209c: stur            x2, [fp, #-0x18]
    // 0x6520a0: LoadField: r1 = r2->field_b
    //     0x6520a0: ldur            w1, [x2, #0xb]
    // 0x6520a4: r3 = LoadInt32Instr(r1)
    //     0x6520a4: sbfx            x3, x1, #1, #0x1f
    // 0x6520a8: stur            x3, [fp, #-0x10]
    // 0x6520ac: cbz             w1, #0x652120
    // 0x6520b0: r1 = 0
    //     0x6520b0: movz            x1, #0
    // 0x6520b4: CheckStackOverflow
    //     0x6520b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6520b8: cmp             SP, x16
    //     0x6520bc: b.ls            #0x652158
    // 0x6520c0: LoadField: r4 = r2->field_b
    //     0x6520c0: ldur            w4, [x2, #0xb]
    // 0x6520c4: r5 = LoadInt32Instr(r4)
    //     0x6520c4: sbfx            x5, x4, #1, #0x1f
    // 0x6520c8: cmp             x3, x5
    // 0x6520cc: b.ne            #0x652130
    // 0x6520d0: cmp             x1, x5
    // 0x6520d4: b.ge            #0x652120
    // 0x6520d8: LoadField: r4 = r2->field_f
    //     0x6520d8: ldur            w4, [x2, #0xf]
    // 0x6520dc: DecompressPointer r4
    //     0x6520dc: add             x4, x4, HEAP, lsl #32
    // 0x6520e0: ArrayLoad: r5 = r4[r1]  ; Unknown_4
    //     0x6520e0: add             x16, x4, x1, lsl #2
    //     0x6520e4: ldur            w5, [x16, #0xf]
    // 0x6520e8: DecompressPointer r5
    //     0x6520e8: add             x5, x5, HEAP, lsl #32
    // 0x6520ec: add             x4, x1, #1
    // 0x6520f0: stur            x4, [fp, #-8]
    // 0x6520f4: LoadField: r1 = r5->field_5f
    //     0x6520f4: ldur            w1, [x5, #0x5f]
    // 0x6520f8: DecompressPointer r1
    //     0x6520f8: add             x1, x1, HEAP, lsl #32
    // 0x6520fc: cmp             w0, w1
    // 0x652100: b.ne            #0x65210c
    // 0x652104: mov             x1, x5
    // 0x652108: r0 = _clearEnclosingScopeCache()
    //     0x652108: bl              #0x652054  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_clearEnclosingScopeCache
    // 0x65210c: ldur            x1, [fp, #-8]
    // 0x652110: ldur            x0, [fp, #-0x20]
    // 0x652114: ldur            x2, [fp, #-0x18]
    // 0x652118: ldur            x3, [fp, #-0x10]
    // 0x65211c: b               #0x6520b4
    // 0x652120: r0 = Null
    //     0x652120: mov             x0, NULL
    // 0x652124: LeaveFrame
    //     0x652124: mov             SP, fp
    //     0x652128: ldp             fp, lr, [SP], #0x10
    // 0x65212c: ret
    //     0x65212c: ret             
    // 0x652130: mov             x0, x2
    // 0x652134: r0 = ConcurrentModificationError()
    //     0x652134: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x652138: mov             x1, x0
    // 0x65213c: ldur            x0, [fp, #-0x18]
    // 0x652140: StoreField: r1->field_b = r0
    //     0x652140: stur            w0, [x1, #0xb]
    // 0x652144: mov             x0, x1
    // 0x652148: r0 = Throw()
    //     0x652148: bl              #0xec04b8  ; ThrowStub
    // 0x65214c: brk             #0
    // 0x652150: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x652150: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x652154: b               #0x65206c
    // 0x652158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x652158: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65215c: b               #0x6520c0
  }
  [closure] bool <anonymous closure>(dynamic, FocusNode) {
    // ** addr: 0x652160, size: 0x64
    // 0x652160: EnterFrame
    //     0x652160: stp             fp, lr, [SP, #-0x10]!
    //     0x652164: mov             fp, SP
    // 0x652168: AllocStack(0x8)
    //     0x652168: sub             SP, SP, #8
    // 0x65216c: SetupParameters()
    //     0x65216c: ldr             x0, [fp, #0x18]
    //     0x652170: ldur            w2, [x0, #0x17]
    //     0x652174: add             x2, x2, HEAP, lsl #32
    //     0x652178: stur            x2, [fp, #-8]
    // 0x65217c: CheckStackOverflow
    //     0x65217c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x652180: cmp             SP, x16
    //     0x652184: b.ls            #0x6521bc
    // 0x652188: ldr             x1, [fp, #0x10]
    // 0x65218c: r0 = enclosingScope()
    //     0x65218c: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x652190: ldur            x1, [fp, #-8]
    // 0x652194: LoadField: r2 = r1->field_f
    //     0x652194: ldur            w2, [x1, #0xf]
    // 0x652198: DecompressPointer r2
    //     0x652198: add             x2, x2, HEAP, lsl #32
    // 0x65219c: cmp             w0, w2
    // 0x6521a0: r16 = true
    //     0x6521a0: add             x16, NULL, #0x20  ; true
    // 0x6521a4: r17 = false
    //     0x6521a4: add             x17, NULL, #0x30  ; false
    // 0x6521a8: csel            x1, x16, x17, eq
    // 0x6521ac: mov             x0, x1
    // 0x6521b0: LeaveFrame
    //     0x6521b0: mov             SP, fp
    //     0x6521b4: ldp             fp, lr, [SP], #0x10
    // 0x6521b8: ret
    //     0x6521b8: ret             
    // 0x6521bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6521bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6521c0: b               #0x652188
  }
  get _ enclosingScope(/* No info */) {
    // ** addr: 0x6521c4, size: 0xb8
    // 0x6521c4: EnterFrame
    //     0x6521c4: stp             fp, lr, [SP, #-0x10]!
    //     0x6521c8: mov             fp, SP
    // 0x6521cc: AllocStack(0x8)
    //     0x6521cc: sub             SP, SP, #8
    // 0x6521d0: SetupParameters(FocusNode this /* r1 => r0, fp-0x8 */)
    //     0x6521d0: mov             x0, x1
    //     0x6521d4: stur            x1, [fp, #-8]
    // 0x6521d8: CheckStackOverflow
    //     0x6521d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6521dc: cmp             SP, x16
    //     0x6521e0: b.ls            #0x652274
    // 0x6521e4: LoadField: r1 = r0->field_5f
    //     0x6521e4: ldur            w1, [x0, #0x5f]
    // 0x6521e8: DecompressPointer r1
    //     0x6521e8: add             x1, x1, HEAP, lsl #32
    // 0x6521ec: cmp             w1, NULL
    // 0x6521f0: b.ne            #0x652264
    // 0x6521f4: LoadField: r1 = r0->field_4f
    //     0x6521f4: ldur            w1, [x0, #0x4f]
    // 0x6521f8: DecompressPointer r1
    //     0x6521f8: add             x1, x1, HEAP, lsl #32
    // 0x6521fc: cmp             w1, NULL
    // 0x652200: b.ne            #0x652210
    // 0x652204: mov             x2, x0
    // 0x652208: r1 = Null
    //     0x652208: mov             x1, NULL
    // 0x65220c: b               #0x65223c
    // 0x652210: r2 = LoadClassIdInstr(r1)
    //     0x652210: ldur            x2, [x1, #-1]
    //     0x652214: ubfx            x2, x2, #0xc, #0x14
    // 0x652218: sub             x16, x2, #0xb67
    // 0x65221c: cmp             x16, #1
    // 0x652220: b.hi            #0x652230
    // 0x652224: r0 = enclosingScope()
    //     0x652224: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x652228: mov             x2, x0
    // 0x65222c: b               #0x652234
    // 0x652230: mov             x2, x1
    // 0x652234: mov             x1, x2
    // 0x652238: ldur            x2, [fp, #-8]
    // 0x65223c: mov             x0, x1
    // 0x652240: StoreField: r2->field_5f = r0
    //     0x652240: stur            w0, [x2, #0x5f]
    //     0x652244: ldurb           w16, [x2, #-1]
    //     0x652248: ldurb           w17, [x0, #-1]
    //     0x65224c: and             x16, x17, x16, lsr #2
    //     0x652250: tst             x16, HEAP, lsr #32
    //     0x652254: b.eq            #0x65225c
    //     0x652258: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x65225c: mov             x0, x1
    // 0x652260: b               #0x652268
    // 0x652264: mov             x0, x1
    // 0x652268: LeaveFrame
    //     0x652268: mov             SP, fp
    //     0x65226c: ldp             fp, lr, [SP], #0x10
    // 0x652270: ret
    //     0x652270: ret             
    // 0x652274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x652274: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x652278: b               #0x6521e4
  }
  _ requestFocus(/* No info */) {
    // ** addr: 0x657140, size: 0xb8
    // 0x657140: EnterFrame
    //     0x657140: stp             fp, lr, [SP, #-0x10]!
    //     0x657144: mov             fp, SP
    // 0x657148: AllocStack(0x8)
    //     0x657148: sub             SP, SP, #8
    // 0x65714c: SetupParameters([dynamic _ = Null /* r0, fp-0x8 */])
    //     0x65714c: ldur            w0, [x4, #0x13]
    //     0x657150: sub             x2, x0, #2
    //     0x657154: cmp             w2, #2
    //     0x657158: b.lt            #0x657168
    //     0x65715c: add             x0, fp, w2, sxtw #2
    //     0x657160: ldr             x0, [x0, #8]
    //     0x657164: b               #0x65716c
    //     0x657168: mov             x0, NULL
    //     0x65716c: stur            x0, [fp, #-8]
    // 0x657170: CheckStackOverflow
    //     0x657170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x657174: cmp             SP, x16
    //     0x657178: b.ls            #0x6571f0
    // 0x65717c: cmp             w0, NULL
    // 0x657180: b.eq            #0x6571c8
    // 0x657184: LoadField: r2 = r0->field_4f
    //     0x657184: ldur            w2, [x0, #0x4f]
    // 0x657188: DecompressPointer r2
    //     0x657188: add             x2, x2, HEAP, lsl #32
    // 0x65718c: cmp             w2, NULL
    // 0x657190: b.ne            #0x65719c
    // 0x657194: mov             x2, x0
    // 0x657198: r0 = _reparent()
    //     0x657198: bl              #0x6512ac  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_reparent
    // 0x65719c: ldur            x1, [fp, #-8]
    // 0x6571a0: r0 = LoadClassIdInstr(r1)
    //     0x6571a0: ldur            x0, [x1, #-1]
    //     0x6571a4: ubfx            x0, x0, #0xc, #0x14
    // 0x6571a8: r2 = true
    //     0x6571a8: add             x2, NULL, #0x20  ; true
    // 0x6571ac: r0 = GDT[cid_x0 + -0xffc]()
    //     0x6571ac: sub             lr, x0, #0xffc
    //     0x6571b0: ldr             lr, [x21, lr, lsl #3]
    //     0x6571b4: blr             lr
    // 0x6571b8: r0 = Null
    //     0x6571b8: mov             x0, NULL
    // 0x6571bc: LeaveFrame
    //     0x6571bc: mov             SP, fp
    //     0x6571c0: ldp             fp, lr, [SP], #0x10
    // 0x6571c4: ret
    //     0x6571c4: ret             
    // 0x6571c8: r0 = LoadClassIdInstr(r1)
    //     0x6571c8: ldur            x0, [x1, #-1]
    //     0x6571cc: ubfx            x0, x0, #0xc, #0x14
    // 0x6571d0: r2 = true
    //     0x6571d0: add             x2, NULL, #0x20  ; true
    // 0x6571d4: r0 = GDT[cid_x0 + -0xffc]()
    //     0x6571d4: sub             lr, x0, #0xffc
    //     0x6571d8: ldr             lr, [x21, lr, lsl #3]
    //     0x6571dc: blr             lr
    // 0x6571e0: r0 = Null
    //     0x6571e0: mov             x0, NULL
    // 0x6571e4: LeaveFrame
    //     0x6571e4: mov             SP, fp
    //     0x6571e8: ldp             fp, lr, [SP], #0x10
    // 0x6571ec: ret
    //     0x6571ec: ret             
    // 0x6571f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6571f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6571f4: b               #0x65717c
  }
  [closure] void requestFocus(dynamic, [FocusNode?]) {
    // ** addr: 0x6571f8, size: 0x70
    // 0x6571f8: EnterFrame
    //     0x6571f8: stp             fp, lr, [SP, #-0x10]!
    //     0x6571fc: mov             fp, SP
    // 0x657200: AllocStack(0x8)
    //     0x657200: sub             SP, SP, #8
    // 0x657204: SetupParameters(FocusNode this /* r0 */, [dynamic _ = Null /* r1 */])
    //     0x657204: ldur            w0, [x4, #0x13]
    //     0x657208: sub             x1, x0, #2
    //     0x65720c: add             x0, fp, w1, sxtw #2
    //     0x657210: ldr             x0, [x0, #0x10]
    //     0x657214: cmp             w1, #2
    //     0x657218: b.lt            #0x65722c
    //     0x65721c: add             x2, fp, w1, sxtw #2
    //     0x657220: ldr             x2, [x2, #8]
    //     0x657224: mov             x1, x2
    //     0x657228: b               #0x657230
    //     0x65722c: mov             x1, NULL
    //     0x657230: ldur            w2, [x0, #0x17]
    //     0x657234: add             x2, x2, HEAP, lsl #32
    // 0x657238: CheckStackOverflow
    //     0x657238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65723c: cmp             SP, x16
    //     0x657240: b.ls            #0x657260
    // 0x657244: str             x1, [SP]
    // 0x657248: mov             x1, x2
    // 0x65724c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x65724c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x657250: r0 = requestFocus()
    //     0x657250: bl              #0x657140  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::requestFocus
    // 0x657254: LeaveFrame
    //     0x657254: mov             SP, fp
    //     0x657258: ldp             fp, lr, [SP], #0x10
    // 0x65725c: ret
    //     0x65725c: ret             
    // 0x657260: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x657260: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x657264: b               #0x657244
  }
  _ _notify(/* No info */) {
    // ** addr: 0x69003c, size: 0x74
    // 0x69003c: EnterFrame
    //     0x69003c: stp             fp, lr, [SP, #-0x10]!
    //     0x690040: mov             fp, SP
    // 0x690044: AllocStack(0x8)
    //     0x690044: sub             SP, SP, #8
    // 0x690048: SetupParameters(FocusNode this /* r1 => r0, fp-0x8 */)
    //     0x690048: mov             x0, x1
    //     0x69004c: stur            x1, [fp, #-8]
    // 0x690050: CheckStackOverflow
    //     0x690050: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x690054: cmp             SP, x16
    //     0x690058: b.ls            #0x6900a8
    // 0x69005c: LoadField: r1 = r0->field_4f
    //     0x69005c: ldur            w1, [x0, #0x4f]
    // 0x690060: DecompressPointer r1
    //     0x690060: add             x1, x1, HEAP, lsl #32
    // 0x690064: cmp             w1, NULL
    // 0x690068: b.ne            #0x69007c
    // 0x69006c: r0 = Null
    //     0x69006c: mov             x0, NULL
    // 0x690070: LeaveFrame
    //     0x690070: mov             SP, fp
    //     0x690074: ldp             fp, lr, [SP], #0x10
    // 0x690078: ret
    //     0x690078: ret             
    // 0x69007c: mov             x1, x0
    // 0x690080: r0 = hasPrimaryFocus()
    //     0x690080: bl              #0x651240  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::hasPrimaryFocus
    // 0x690084: tbnz            w0, #4, #0x690090
    // 0x690088: ldur            x1, [fp, #-8]
    // 0x69008c: r0 = _setAsFocusedChildForScope()
    //     0x69008c: bl              #0x650d3c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_setAsFocusedChildForScope
    // 0x690090: ldur            x1, [fp, #-8]
    // 0x690094: r0 = notifyListeners()
    //     0x690094: bl              #0x6469a0  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::notifyListeners
    // 0x690098: r0 = Null
    //     0x690098: mov             x0, NULL
    // 0x69009c: LeaveFrame
    //     0x69009c: mov             SP, fp
    //     0x6900a0: ldp             fp, lr, [SP], #0x10
    // 0x6900a4: ret
    //     0x6900a4: ret             
    // 0x6900a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6900a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6900ac: b               #0x69005c
  }
  _ FocusNode(/* No info */) {
    // ** addr: 0x693dec, size: 0x1f8
    // 0x693dec: EnterFrame
    //     0x693dec: stp             fp, lr, [SP, #-0x10]!
    //     0x693df0: mov             fp, SP
    // 0x693df4: AllocStack(0x20)
    //     0x693df4: sub             SP, SP, #0x20
    // 0x693df8: SetupParameters(FocusNode this /* r1 => r0, fp-0x20 */, {dynamic canRequestFocus = true /* r3, fp-0x18 */, dynamic debugLabel, dynamic descendantsAreFocusable = true /* r5, fp-0x10 */, dynamic skipTraversal = false /* r4, fp-0x8 */})
    //     0x693df8: mov             x0, x1
    //     0x693dfc: stur            x1, [fp, #-0x20]
    //     0x693e00: ldur            w1, [x4, #0x13]
    //     0x693e04: ldur            w2, [x4, #0x1f]
    //     0x693e08: add             x2, x2, HEAP, lsl #32
    //     0x693e0c: ldr             x16, [PP, #0x2588]  ; [pp+0x2588] "canRequestFocus"
    //     0x693e10: cmp             w2, w16
    //     0x693e14: b.ne            #0x693e38
    //     0x693e18: ldur            w2, [x4, #0x23]
    //     0x693e1c: add             x2, x2, HEAP, lsl #32
    //     0x693e20: sub             w3, w1, w2
    //     0x693e24: add             x2, fp, w3, sxtw #2
    //     0x693e28: ldr             x2, [x2, #8]
    //     0x693e2c: mov             x3, x2
    //     0x693e30: movz            x2, #0x1
    //     0x693e34: b               #0x693e40
    //     0x693e38: add             x3, NULL, #0x20  ; true
    //     0x693e3c: movz            x2, #0
    //     0x693e40: stur            x3, [fp, #-0x18]
    //     0x693e44: lsl             x5, x2, #1
    //     0x693e48: lsl             w6, w5, #1
    //     0x693e4c: add             w7, w6, #8
    //     0x693e50: add             x16, x4, w7, sxtw #1
    //     0x693e54: ldur            w6, [x16, #0xf]
    //     0x693e58: add             x6, x6, HEAP, lsl #32
    //     0x693e5c: ldr             x16, [PP, #0x2590]  ; [pp+0x2590] "debugLabel"
    //     0x693e60: cmp             w6, w16
    //     0x693e64: b.ne            #0x693e74
    //     0x693e68: add             w2, w5, #2
    //     0x693e6c: sbfx            x5, x2, #1, #0x1f
    //     0x693e70: mov             x2, x5
    //     0x693e74: lsl             x5, x2, #1
    //     0x693e78: lsl             w6, w5, #1
    //     0x693e7c: add             w7, w6, #8
    //     0x693e80: add             x16, x4, w7, sxtw #1
    //     0x693e84: ldur            w8, [x16, #0xf]
    //     0x693e88: add             x8, x8, HEAP, lsl #32
    //     0x693e8c: ldr             x16, [PP, #0x25b0]  ; [pp+0x25b0] "descendantsAreFocusable"
    //     0x693e90: cmp             w8, w16
    //     0x693e94: b.ne            #0x693ec8
    //     0x693e98: add             w2, w6, #0xa
    //     0x693e9c: add             x16, x4, w2, sxtw #1
    //     0x693ea0: ldur            w6, [x16, #0xf]
    //     0x693ea4: add             x6, x6, HEAP, lsl #32
    //     0x693ea8: sub             w2, w1, w6
    //     0x693eac: add             x6, fp, w2, sxtw #2
    //     0x693eb0: ldr             x6, [x6, #8]
    //     0x693eb4: add             w2, w5, #2
    //     0x693eb8: sbfx            x5, x2, #1, #0x1f
    //     0x693ebc: mov             x2, x5
    //     0x693ec0: mov             x5, x6
    //     0x693ec4: b               #0x693ecc
    //     0x693ec8: add             x5, NULL, #0x20  ; true
    //     0x693ecc: stur            x5, [fp, #-0x10]
    //     0x693ed0: lsl             x6, x2, #1
    //     0x693ed4: lsl             w2, w6, #1
    //     0x693ed8: add             w6, w2, #8
    //     0x693edc: add             x16, x4, w6, sxtw #1
    //     0x693ee0: ldur            w7, [x16, #0xf]
    //     0x693ee4: add             x7, x7, HEAP, lsl #32
    //     0x693ee8: ldr             x16, [PP, #0x2598]  ; [pp+0x2598] "skipTraversal"
    //     0x693eec: cmp             w7, w16
    //     0x693ef0: b.ne            #0x693f18
    //     0x693ef4: add             w6, w2, #0xa
    //     0x693ef8: add             x16, x4, w6, sxtw #1
    //     0x693efc: ldur            w2, [x16, #0xf]
    //     0x693f00: add             x2, x2, HEAP, lsl #32
    //     0x693f04: sub             w4, w1, w2
    //     0x693f08: add             x1, fp, w4, sxtw #2
    //     0x693f0c: ldr             x1, [x1, #8]
    //     0x693f10: mov             x4, x1
    //     0x693f14: b               #0x693f1c
    //     0x693f18: add             x4, NULL, #0x30  ; false
    //     0x693f1c: add             x1, NULL, #0x30  ; false
    //     0x693f20: stur            x4, [fp, #-8]
    // 0x693f1c: r1 = false
    // 0x693f24: CheckStackOverflow
    //     0x693f24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x693f28: cmp             SP, x16
    //     0x693f2c: b.ls            #0x693fdc
    // 0x693f30: StoreField: r0->field_4b = r1
    //     0x693f30: stur            w1, [x0, #0x4b]
    // 0x693f34: StoreField: r0->field_63 = r1
    //     0x693f34: stur            w1, [x0, #0x63]
    // 0x693f38: r1 = <FocusNode>
    //     0x693f38: ldr             x1, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x693f3c: r2 = 0
    //     0x693f3c: movz            x2, #0
    // 0x693f40: r0 = _GrowableList()
    //     0x693f40: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x693f44: ldur            x1, [fp, #-0x20]
    // 0x693f48: StoreField: r1->field_53 = r0
    //     0x693f48: stur            w0, [x1, #0x53]
    //     0x693f4c: ldurb           w16, [x1, #-1]
    //     0x693f50: ldurb           w17, [x0, #-1]
    //     0x693f54: and             x16, x17, x16, lsr #2
    //     0x693f58: tst             x16, HEAP, lsr #32
    //     0x693f5c: b.eq            #0x693f64
    //     0x693f60: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x693f64: ldur            x0, [fp, #-8]
    // 0x693f68: StoreField: r1->field_23 = r0
    //     0x693f68: stur            w0, [x1, #0x23]
    // 0x693f6c: ldur            x0, [fp, #-0x18]
    // 0x693f70: StoreField: r1->field_27 = r0
    //     0x693f70: stur            w0, [x1, #0x27]
    // 0x693f74: ldur            x0, [fp, #-0x10]
    // 0x693f78: StoreField: r1->field_2b = r0
    //     0x693f78: stur            w0, [x1, #0x2b]
    // 0x693f7c: r0 = true
    //     0x693f7c: add             x0, NULL, #0x20  ; true
    // 0x693f80: StoreField: r1->field_2f = r0
    //     0x693f80: stur            w0, [x1, #0x2f]
    // 0x693f84: StoreField: r1->field_7 = rZR
    //     0x693f84: stur            xzr, [x1, #7]
    // 0x693f88: StoreField: r1->field_13 = rZR
    //     0x693f88: stur            xzr, [x1, #0x13]
    // 0x693f8c: StoreField: r1->field_1b = rZR
    //     0x693f8c: stur            xzr, [x1, #0x1b]
    // 0x693f90: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0x693f90: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x693f94: ldr             x0, [x0, #0xca8]
    //     0x693f98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x693f9c: cmp             w0, w16
    //     0x693fa0: b.ne            #0x693fac
    //     0x693fa4: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0x693fa8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x693fac: ldur            x1, [fp, #-0x20]
    // 0x693fb0: StoreField: r1->field_f = r0
    //     0x693fb0: stur            w0, [x1, #0xf]
    //     0x693fb4: ldurb           w16, [x1, #-1]
    //     0x693fb8: ldurb           w17, [x0, #-1]
    //     0x693fbc: and             x16, x17, x16, lsr #2
    //     0x693fc0: tst             x16, HEAP, lsr #32
    //     0x693fc4: b.eq            #0x693fcc
    //     0x693fc8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x693fcc: r0 = Null
    //     0x693fcc: mov             x0, NULL
    // 0x693fd0: LeaveFrame
    //     0x693fd0: mov             SP, fp
    //     0x693fd4: ldp             fp, lr, [SP], #0x10
    // 0x693fd8: ret
    //     0x693fd8: ret             
    // 0x693fdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x693fdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x693fe0: b               #0x693f30
  }
  _ unfocus(/* No info */) {
    // ** addr: 0x6a834c, size: 0x47c
    // 0x6a834c: EnterFrame
    //     0x6a834c: stp             fp, lr, [SP, #-0x10]!
    //     0x6a8350: mov             fp, SP
    // 0x6a8354: AllocStack(0x28)
    //     0x6a8354: sub             SP, SP, #0x28
    // 0x6a8358: SetupParameters(FocusNode this /* r1 => r0, fp-0x10 */, {dynamic disposition = Instance_UnfocusDisposition /* r2, fp-0x8 */})
    //     0x6a8358: mov             x0, x1
    //     0x6a835c: stur            x1, [fp, #-0x10]
    //     0x6a8360: ldur            w1, [x4, #0x13]
    //     0x6a8364: ldur            w2, [x4, #0x1f]
    //     0x6a8368: add             x2, x2, HEAP, lsl #32
    //     0x6a836c: ldr             x16, [PP, #0x4e08]  ; [pp+0x4e08] "disposition"
    //     0x6a8370: cmp             w2, w16
    //     0x6a8374: b.ne            #0x6a8394
    //     0x6a8378: ldur            w2, [x4, #0x23]
    //     0x6a837c: add             x2, x2, HEAP, lsl #32
    //     0x6a8380: sub             w3, w1, w2
    //     0x6a8384: add             x1, fp, w3, sxtw #2
    //     0x6a8388: ldr             x1, [x1, #8]
    //     0x6a838c: mov             x2, x1
    //     0x6a8390: b               #0x6a8398
    //     0x6a8394: ldr             x2, [PP, #0x4e10]  ; [pp+0x4e10] Obj!UnfocusDisposition@e34581
    //     0x6a8398: stur            x2, [fp, #-8]
    // 0x6a839c: CheckStackOverflow
    //     0x6a839c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a83a0: cmp             SP, x16
    //     0x6a83a4: b.ls            #0x6a87a0
    // 0x6a83a8: mov             x1, x0
    // 0x6a83ac: r0 = hasFocus()
    //     0x6a83ac: bl              #0x6511a0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::hasFocus
    // 0x6a83b0: tbz             w0, #4, #0x6a83fc
    // 0x6a83b4: ldur            x1, [fp, #-0x10]
    // 0x6a83b8: LoadField: r0 = r1->field_3f
    //     0x6a83b8: ldur            w0, [x1, #0x3f]
    // 0x6a83bc: DecompressPointer r0
    //     0x6a83bc: add             x0, x0, HEAP, lsl #32
    // 0x6a83c0: cmp             w0, NULL
    // 0x6a83c4: b.eq            #0x6a83ec
    // 0x6a83c8: LoadField: r2 = r0->field_3b
    //     0x6a83c8: ldur            w2, [x0, #0x3b]
    // 0x6a83cc: DecompressPointer r2
    //     0x6a83cc: add             x2, x2, HEAP, lsl #32
    // 0x6a83d0: r0 = LoadClassIdInstr(r2)
    //     0x6a83d0: ldur            x0, [x2, #-1]
    //     0x6a83d4: ubfx            x0, x0, #0xc, #0x14
    // 0x6a83d8: stp             x1, x2, [SP]
    // 0x6a83dc: mov             lr, x0
    // 0x6a83e0: ldr             lr, [x21, lr, lsl #3]
    // 0x6a83e4: blr             lr
    // 0x6a83e8: tbz             w0, #4, #0x6a83fc
    // 0x6a83ec: r0 = Null
    //     0x6a83ec: mov             x0, NULL
    // 0x6a83f0: LeaveFrame
    //     0x6a83f0: mov             SP, fp
    //     0x6a83f4: ldp             fp, lr, [SP], #0x10
    // 0x6a83f8: ret
    //     0x6a83f8: ret             
    // 0x6a83fc: ldur            x1, [fp, #-0x10]
    // 0x6a8400: r0 = enclosingScope()
    //     0x6a8400: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x6a8404: stur            x0, [fp, #-0x18]
    // 0x6a8408: cmp             w0, NULL
    // 0x6a840c: b.ne            #0x6a8420
    // 0x6a8410: r0 = Null
    //     0x6a8410: mov             x0, NULL
    // 0x6a8414: LeaveFrame
    //     0x6a8414: mov             SP, fp
    //     0x6a8418: ldp             fp, lr, [SP], #0x10
    // 0x6a841c: ret
    //     0x6a841c: ret             
    // 0x6a8420: ldur            x1, [fp, #-8]
    // 0x6a8424: LoadField: r2 = r1->field_7
    //     0x6a8424: ldur            x2, [x1, #7]
    // 0x6a8428: cmp             x2, #0
    // 0x6a842c: b.gt            #0x6a8560
    // 0x6a8430: mov             x1, x0
    // 0x6a8434: r0 = canRequestFocus()
    //     0x6a8434: bl              #0x6a8938  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::canRequestFocus
    // 0x6a8438: tbnz            w0, #4, #0x6a844c
    // 0x6a843c: ldur            x0, [fp, #-0x18]
    // 0x6a8440: LoadField: r1 = r0->field_6b
    //     0x6a8440: ldur            w1, [x0, #0x6b]
    // 0x6a8444: DecompressPointer r1
    //     0x6a8444: add             x1, x1, HEAP, lsl #32
    // 0x6a8448: r0 = clear()
    //     0x6a8448: bl              #0xbd9e80  ; [dart:core] _GrowableList::clear
    // 0x6a844c: ldur            x0, [fp, #-0x18]
    // 0x6a8450: ldur            x2, [fp, #-0x10]
    // 0x6a8454: stur            x0, [fp, #-8]
    // 0x6a8458: CheckStackOverflow
    //     0x6a8458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a845c: cmp             SP, x16
    //     0x6a8460: b.ls            #0x6a87a8
    // 0x6a8464: cmp             w0, NULL
    // 0x6a8468: b.eq            #0x6a87b0
    // 0x6a846c: LoadField: r1 = r0->field_27
    //     0x6a846c: ldur            w1, [x0, #0x27]
    // 0x6a8470: DecompressPointer r1
    //     0x6a8470: add             x1, x1, HEAP, lsl #32
    // 0x6a8474: tbnz            w1, #4, #0x6a84a0
    // 0x6a8478: mov             x1, x0
    // 0x6a847c: r0 = ancestors()
    //     0x6a847c: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x6a8480: mov             x1, x0
    // 0x6a8484: r2 = Closure: (FocusNode) => bool from Function '_allowDescendantsToBeFocused@279042876': static.
    //     0x6a8484: ldr             x2, [PP, #0x4e18]  ; [pp+0x4e18] Closure: (FocusNode) => bool from Function '_allowDescendantsToBeFocused@279042876': static. (0x7e54fb0a8984)
    // 0x6a8488: r0 = every()
    //     0x6a8488: bl              #0x6a87c8  ; [dart:collection] ListBase::every
    // 0x6a848c: tbnz            w0, #4, #0x6a84a0
    // 0x6a8490: ldur            x1, [fp, #-8]
    // 0x6a8494: r2 = false
    //     0x6a8494: add             x2, NULL, #0x30  ; false
    // 0x6a8498: r0 = _doRequestFocus()
    //     0x6a8498: bl              #0xdaa9f0  ; [package:flutter/src/widgets/focus_manager.dart] FocusScopeNode::_doRequestFocus
    // 0x6a849c: b               #0x6a85d4
    // 0x6a84a0: ldur            x0, [fp, #-8]
    // 0x6a84a4: LoadField: r1 = r0->field_5f
    //     0x6a84a4: ldur            w1, [x0, #0x5f]
    // 0x6a84a8: DecompressPointer r1
    //     0x6a84a8: add             x1, x1, HEAP, lsl #32
    // 0x6a84ac: cmp             w1, NULL
    // 0x6a84b0: b.ne            #0x6a8520
    // 0x6a84b4: LoadField: r1 = r0->field_4f
    //     0x6a84b4: ldur            w1, [x0, #0x4f]
    // 0x6a84b8: DecompressPointer r1
    //     0x6a84b8: add             x1, x1, HEAP, lsl #32
    // 0x6a84bc: cmp             w1, NULL
    // 0x6a84c0: b.ne            #0x6a84d0
    // 0x6a84c4: mov             x1, x0
    // 0x6a84c8: r2 = Null
    //     0x6a84c8: mov             x2, NULL
    // 0x6a84cc: b               #0x6a84f8
    // 0x6a84d0: r2 = LoadClassIdInstr(r1)
    //     0x6a84d0: ldur            x2, [x1, #-1]
    //     0x6a84d4: ubfx            x2, x2, #0xc, #0x14
    // 0x6a84d8: sub             x16, x2, #0xb67
    // 0x6a84dc: cmp             x16, #1
    // 0x6a84e0: b.hi            #0x6a84ec
    // 0x6a84e4: r0 = enclosingScope()
    //     0x6a84e4: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x6a84e8: b               #0x6a84f0
    // 0x6a84ec: mov             x0, x1
    // 0x6a84f0: mov             x2, x0
    // 0x6a84f4: ldur            x1, [fp, #-8]
    // 0x6a84f8: mov             x0, x2
    // 0x6a84fc: StoreField: r1->field_5f = r0
    //     0x6a84fc: stur            w0, [x1, #0x5f]
    //     0x6a8500: ldurb           w16, [x1, #-1]
    //     0x6a8504: ldurb           w17, [x0, #-1]
    //     0x6a8508: and             x16, x17, x16, lsr #2
    //     0x6a850c: tst             x16, HEAP, lsr #32
    //     0x6a8510: b.eq            #0x6a8518
    //     0x6a8514: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6a8518: mov             x0, x2
    // 0x6a851c: b               #0x6a8524
    // 0x6a8520: mov             x0, x1
    // 0x6a8524: cmp             w0, NULL
    // 0x6a8528: b.ne            #0x6a8558
    // 0x6a852c: ldur            x2, [fp, #-0x10]
    // 0x6a8530: LoadField: r0 = r2->field_3f
    //     0x6a8530: ldur            w0, [x2, #0x3f]
    // 0x6a8534: DecompressPointer r0
    //     0x6a8534: add             x0, x0, HEAP, lsl #32
    // 0x6a8538: cmp             w0, NULL
    // 0x6a853c: b.ne            #0x6a8548
    // 0x6a8540: r0 = Null
    //     0x6a8540: mov             x0, NULL
    // 0x6a8544: b               #0x6a8454
    // 0x6a8548: LoadField: r1 = r0->field_27
    //     0x6a8548: ldur            w1, [x0, #0x27]
    // 0x6a854c: DecompressPointer r1
    //     0x6a854c: add             x1, x1, HEAP, lsl #32
    // 0x6a8550: mov             x0, x1
    // 0x6a8554: b               #0x6a8454
    // 0x6a8558: ldur            x2, [fp, #-0x10]
    // 0x6a855c: b               #0x6a8454
    // 0x6a8560: ldur            x2, [fp, #-0x10]
    // 0x6a8564: ldur            x1, [fp, #-0x18]
    // 0x6a8568: r0 = canRequestFocus()
    //     0x6a8568: bl              #0x6a8938  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::canRequestFocus
    // 0x6a856c: tbnz            w0, #4, #0x6a8584
    // 0x6a8570: ldur            x0, [fp, #-0x18]
    // 0x6a8574: LoadField: r1 = r0->field_6b
    //     0x6a8574: ldur            w1, [x0, #0x6b]
    // 0x6a8578: DecompressPointer r1
    //     0x6a8578: add             x1, x1, HEAP, lsl #32
    // 0x6a857c: ldur            x2, [fp, #-0x10]
    // 0x6a8580: r0 = remove()
    //     0x6a8580: bl              #0x6ec83c  ; [dart:core] _GrowableList::remove
    // 0x6a8584: ldur            x2, [fp, #-0x18]
    // 0x6a8588: ldur            x0, [fp, #-0x10]
    // 0x6a858c: stur            x2, [fp, #-8]
    // 0x6a8590: CheckStackOverflow
    //     0x6a8590: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a8594: cmp             SP, x16
    //     0x6a8598: b.ls            #0x6a87b4
    // 0x6a859c: cmp             w2, NULL
    // 0x6a85a0: b.eq            #0x6a87bc
    // 0x6a85a4: LoadField: r1 = r2->field_27
    //     0x6a85a4: ldur            w1, [x2, #0x27]
    // 0x6a85a8: DecompressPointer r1
    //     0x6a85a8: add             x1, x1, HEAP, lsl #32
    // 0x6a85ac: tbnz            w1, #4, #0x6a85e4
    // 0x6a85b0: mov             x1, x2
    // 0x6a85b4: r0 = ancestors()
    //     0x6a85b4: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x6a85b8: mov             x1, x0
    // 0x6a85bc: r2 = Closure: (FocusNode) => bool from Function '_allowDescendantsToBeFocused@279042876': static.
    //     0x6a85bc: ldr             x2, [PP, #0x4e18]  ; [pp+0x4e18] Closure: (FocusNode) => bool from Function '_allowDescendantsToBeFocused@279042876': static. (0x7e54fb0a8984)
    // 0x6a85c0: r0 = every()
    //     0x6a85c0: bl              #0x6a87c8  ; [dart:collection] ListBase::every
    // 0x6a85c4: tbnz            w0, #4, #0x6a85e4
    // 0x6a85c8: ldur            x1, [fp, #-8]
    // 0x6a85cc: r2 = true
    //     0x6a85cc: add             x2, NULL, #0x20  ; true
    // 0x6a85d0: r0 = _doRequestFocus()
    //     0x6a85d0: bl              #0xdaa9f0  ; [package:flutter/src/widgets/focus_manager.dart] FocusScopeNode::_doRequestFocus
    // 0x6a85d4: r0 = Null
    //     0x6a85d4: mov             x0, NULL
    // 0x6a85d8: LeaveFrame
    //     0x6a85d8: mov             SP, fp
    //     0x6a85dc: ldp             fp, lr, [SP], #0x10
    // 0x6a85e0: ret
    //     0x6a85e0: ret             
    // 0x6a85e4: ldur            x0, [fp, #-8]
    // 0x6a85e8: LoadField: r1 = r0->field_5f
    //     0x6a85e8: ldur            w1, [x0, #0x5f]
    // 0x6a85ec: DecompressPointer r1
    //     0x6a85ec: add             x1, x1, HEAP, lsl #32
    // 0x6a85f0: cmp             w1, NULL
    // 0x6a85f4: b.ne            #0x6a8664
    // 0x6a85f8: LoadField: r1 = r0->field_4f
    //     0x6a85f8: ldur            w1, [x0, #0x4f]
    // 0x6a85fc: DecompressPointer r1
    //     0x6a85fc: add             x1, x1, HEAP, lsl #32
    // 0x6a8600: cmp             w1, NULL
    // 0x6a8604: b.ne            #0x6a8614
    // 0x6a8608: mov             x3, x0
    // 0x6a860c: r1 = Null
    //     0x6a860c: mov             x1, NULL
    // 0x6a8610: b               #0x6a863c
    // 0x6a8614: r2 = LoadClassIdInstr(r1)
    //     0x6a8614: ldur            x2, [x1, #-1]
    //     0x6a8618: ubfx            x2, x2, #0xc, #0x14
    // 0x6a861c: sub             x16, x2, #0xb67
    // 0x6a8620: cmp             x16, #1
    // 0x6a8624: b.hi            #0x6a8630
    // 0x6a8628: r0 = enclosingScope()
    //     0x6a8628: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x6a862c: b               #0x6a8634
    // 0x6a8630: mov             x0, x1
    // 0x6a8634: mov             x1, x0
    // 0x6a8638: ldur            x3, [fp, #-8]
    // 0x6a863c: mov             x0, x1
    // 0x6a8640: StoreField: r3->field_5f = r0
    //     0x6a8640: stur            w0, [x3, #0x5f]
    //     0x6a8644: ldurb           w16, [x3, #-1]
    //     0x6a8648: ldurb           w17, [x0, #-1]
    //     0x6a864c: and             x16, x17, x16, lsr #2
    //     0x6a8650: tst             x16, HEAP, lsr #32
    //     0x6a8654: b.eq            #0x6a865c
    //     0x6a8658: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x6a865c: mov             x0, x1
    // 0x6a8660: b               #0x6a866c
    // 0x6a8664: mov             x3, x0
    // 0x6a8668: mov             x0, x1
    // 0x6a866c: cmp             w0, NULL
    // 0x6a8670: b.ne            #0x6a867c
    // 0x6a8674: mov             x0, x3
    // 0x6a8678: b               #0x6a86d8
    // 0x6a867c: LoadField: r1 = r0->field_6b
    //     0x6a867c: ldur            w1, [x0, #0x6b]
    // 0x6a8680: DecompressPointer r1
    //     0x6a8680: add             x1, x1, HEAP, lsl #32
    // 0x6a8684: LoadField: r0 = r1->field_b
    //     0x6a8684: ldur            w0, [x1, #0xb]
    // 0x6a8688: r2 = LoadInt32Instr(r0)
    //     0x6a8688: sbfx            x2, x0, #1, #0x1f
    // 0x6a868c: LoadField: r0 = r1->field_f
    //     0x6a868c: ldur            w0, [x1, #0xf]
    // 0x6a8690: DecompressPointer r0
    //     0x6a8690: add             x0, x0, HEAP, lsl #32
    // 0x6a8694: r4 = 0
    //     0x6a8694: movz            x4, #0
    // 0x6a8698: CheckStackOverflow
    //     0x6a8698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a869c: cmp             SP, x16
    //     0x6a86a0: b.ls            #0x6a87c0
    // 0x6a86a4: cmp             x4, x2
    // 0x6a86a8: b.ge            #0x6a86d4
    // 0x6a86ac: ArrayLoad: r5 = r0[r4]  ; Unknown_4
    //     0x6a86ac: add             x16, x0, x4, lsl #2
    //     0x6a86b0: ldur            w5, [x16, #0xf]
    // 0x6a86b4: DecompressPointer r5
    //     0x6a86b4: add             x5, x5, HEAP, lsl #32
    // 0x6a86b8: cmp             w5, w3
    // 0x6a86bc: b.eq            #0x6a86cc
    // 0x6a86c0: add             x5, x4, #1
    // 0x6a86c4: mov             x4, x5
    // 0x6a86c8: b               #0x6a8698
    // 0x6a86cc: mov             x2, x4
    // 0x6a86d0: r0 = removeAt()
    //     0x6a86d0: bl              #0xa8d758  ; [dart:core] _GrowableList::removeAt
    // 0x6a86d4: ldur            x0, [fp, #-8]
    // 0x6a86d8: LoadField: r1 = r0->field_5f
    //     0x6a86d8: ldur            w1, [x0, #0x5f]
    // 0x6a86dc: DecompressPointer r1
    //     0x6a86dc: add             x1, x1, HEAP, lsl #32
    // 0x6a86e0: cmp             w1, NULL
    // 0x6a86e4: b.ne            #0x6a8758
    // 0x6a86e8: LoadField: r1 = r0->field_4f
    //     0x6a86e8: ldur            w1, [x0, #0x4f]
    // 0x6a86ec: DecompressPointer r1
    //     0x6a86ec: add             x1, x1, HEAP, lsl #32
    // 0x6a86f0: cmp             w1, NULL
    // 0x6a86f4: b.ne            #0x6a8704
    // 0x6a86f8: mov             x2, x0
    // 0x6a86fc: r1 = Null
    //     0x6a86fc: mov             x1, NULL
    // 0x6a8700: b               #0x6a8730
    // 0x6a8704: r2 = LoadClassIdInstr(r1)
    //     0x6a8704: ldur            x2, [x1, #-1]
    //     0x6a8708: ubfx            x2, x2, #0xc, #0x14
    // 0x6a870c: sub             x16, x2, #0xb67
    // 0x6a8710: cmp             x16, #1
    // 0x6a8714: b.hi            #0x6a8724
    // 0x6a8718: r0 = enclosingScope()
    //     0x6a8718: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x6a871c: mov             x2, x0
    // 0x6a8720: b               #0x6a8728
    // 0x6a8724: mov             x2, x1
    // 0x6a8728: mov             x1, x2
    // 0x6a872c: ldur            x2, [fp, #-8]
    // 0x6a8730: mov             x0, x1
    // 0x6a8734: StoreField: r2->field_5f = r0
    //     0x6a8734: stur            w0, [x2, #0x5f]
    //     0x6a8738: ldurb           w16, [x2, #-1]
    //     0x6a873c: ldurb           w17, [x0, #-1]
    //     0x6a8740: and             x16, x17, x16, lsr #2
    //     0x6a8744: tst             x16, HEAP, lsr #32
    //     0x6a8748: b.eq            #0x6a8750
    //     0x6a874c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6a8750: mov             x0, x1
    // 0x6a8754: b               #0x6a875c
    // 0x6a8758: mov             x0, x1
    // 0x6a875c: cmp             w0, NULL
    // 0x6a8760: b.ne            #0x6a8790
    // 0x6a8764: ldur            x1, [fp, #-0x10]
    // 0x6a8768: LoadField: r2 = r1->field_3f
    //     0x6a8768: ldur            w2, [x1, #0x3f]
    // 0x6a876c: DecompressPointer r2
    //     0x6a876c: add             x2, x2, HEAP, lsl #32
    // 0x6a8770: cmp             w2, NULL
    // 0x6a8774: b.ne            #0x6a8780
    // 0x6a8778: r2 = Null
    //     0x6a8778: mov             x2, NULL
    // 0x6a877c: b               #0x6a8798
    // 0x6a8780: LoadField: r3 = r2->field_27
    //     0x6a8780: ldur            w3, [x2, #0x27]
    // 0x6a8784: DecompressPointer r3
    //     0x6a8784: add             x3, x3, HEAP, lsl #32
    // 0x6a8788: mov             x2, x3
    // 0x6a878c: b               #0x6a8798
    // 0x6a8790: ldur            x1, [fp, #-0x10]
    // 0x6a8794: mov             x2, x0
    // 0x6a8798: mov             x0, x1
    // 0x6a879c: b               #0x6a858c
    // 0x6a87a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a87a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a87a4: b               #0x6a83a8
    // 0x6a87a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a87a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a87ac: b               #0x6a8464
    // 0x6a87b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a87b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a87b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a87b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a87b8: b               #0x6a859c
    // 0x6a87bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a87bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a87c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a87c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a87c4: b               #0x6a86a4
  }
  get _ canRequestFocus(/* No info */) {
    // ** addr: 0x6a8938, size: 0x4c
    // 0x6a8938: EnterFrame
    //     0x6a8938: stp             fp, lr, [SP, #-0x10]!
    //     0x6a893c: mov             fp, SP
    // 0x6a8940: CheckStackOverflow
    //     0x6a8940: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a8944: cmp             SP, x16
    //     0x6a8948: b.ls            #0x6a897c
    // 0x6a894c: LoadField: r0 = r1->field_27
    //     0x6a894c: ldur            w0, [x1, #0x27]
    // 0x6a8950: DecompressPointer r0
    //     0x6a8950: add             x0, x0, HEAP, lsl #32
    // 0x6a8954: tbnz            w0, #4, #0x6a896c
    // 0x6a8958: r0 = ancestors()
    //     0x6a8958: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x6a895c: mov             x1, x0
    // 0x6a8960: r2 = Closure: (FocusNode) => bool from Function '_allowDescendantsToBeFocused@279042876': static.
    //     0x6a8960: ldr             x2, [PP, #0x4e18]  ; [pp+0x4e18] Closure: (FocusNode) => bool from Function '_allowDescendantsToBeFocused@279042876': static. (0x7e54fb0a8984)
    // 0x6a8964: r0 = every()
    //     0x6a8964: bl              #0x6a87c8  ; [dart:collection] ListBase::every
    // 0x6a8968: b               #0x6a8970
    // 0x6a896c: r0 = false
    //     0x6a896c: add             x0, NULL, #0x30  ; false
    // 0x6a8970: LeaveFrame
    //     0x6a8970: mov             SP, fp
    //     0x6a8974: ldp             fp, lr, [SP], #0x10
    // 0x6a8978: ret
    //     0x6a8978: ret             
    // 0x6a897c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a897c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a8980: b               #0x6a894c
  }
  [closure] static bool _allowDescendantsToBeFocused(dynamic, FocusNode) {
    // ** addr: 0x6a8984, size: 0x30
    // 0x6a8984: EnterFrame
    //     0x6a8984: stp             fp, lr, [SP, #-0x10]!
    //     0x6a8988: mov             fp, SP
    // 0x6a898c: CheckStackOverflow
    //     0x6a898c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a8990: cmp             SP, x16
    //     0x6a8994: b.ls            #0x6a89ac
    // 0x6a8998: ldr             x1, [fp, #0x10]
    // 0x6a899c: r0 = _allowDescendantsToBeFocused()
    //     0x6a899c: bl              #0x6a89b4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_allowDescendantsToBeFocused
    // 0x6a89a0: LeaveFrame
    //     0x6a89a0: mov             SP, fp
    //     0x6a89a4: ldp             fp, lr, [SP], #0x10
    // 0x6a89a8: ret
    //     0x6a89a8: ret             
    // 0x6a89ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a89ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a89b0: b               #0x6a8998
  }
  static _ _allowDescendantsToBeFocused(/* No info */) {
    // ** addr: 0x6a89b4, size: 0x4c
    // 0x6a89b4: r2 = LoadClassIdInstr(r1)
    //     0x6a89b4: ldur            x2, [x1, #-1]
    //     0x6a89b8: ubfx            x2, x2, #0xc, #0x14
    // 0x6a89bc: sub             x16, x2, #0xb67
    // 0x6a89c0: cmp             x16, #1
    // 0x6a89c4: b.hi            #0x6a89d8
    // 0x6a89c8: LoadField: r2 = r1->field_2b
    //     0x6a89c8: ldur            w2, [x1, #0x2b]
    // 0x6a89cc: DecompressPointer r2
    //     0x6a89cc: add             x2, x2, HEAP, lsl #32
    // 0x6a89d0: mov             x0, x2
    // 0x6a89d4: b               #0x6a89fc
    // 0x6a89d8: LoadField: r2 = r1->field_27
    //     0x6a89d8: ldur            w2, [x1, #0x27]
    // 0x6a89dc: DecompressPointer r2
    //     0x6a89dc: add             x2, x2, HEAP, lsl #32
    // 0x6a89e0: tbnz            w2, #4, #0x6a89f4
    // 0x6a89e4: LoadField: r2 = r1->field_2b
    //     0x6a89e4: ldur            w2, [x1, #0x2b]
    // 0x6a89e8: DecompressPointer r2
    //     0x6a89e8: add             x2, x2, HEAP, lsl #32
    // 0x6a89ec: mov             x1, x2
    // 0x6a89f0: b               #0x6a89f8
    // 0x6a89f4: r1 = false
    //     0x6a89f4: add             x1, NULL, #0x30  ; false
    // 0x6a89f8: mov             x0, x1
    // 0x6a89fc: ret
    //     0x6a89fc: ret             
  }
  _ previousFocus(/* No info */) {
    // ** addr: 0x6b3fb4, size: 0x58
    // 0x6b3fb4: EnterFrame
    //     0x6b3fb4: stp             fp, lr, [SP, #-0x10]!
    //     0x6b3fb8: mov             fp, SP
    // 0x6b3fbc: AllocStack(0x8)
    //     0x6b3fbc: sub             SP, SP, #8
    // 0x6b3fc0: SetupParameters(FocusNode this /* r1 => r2, fp-0x8 */)
    //     0x6b3fc0: mov             x2, x1
    //     0x6b3fc4: stur            x1, [fp, #-8]
    // 0x6b3fc8: CheckStackOverflow
    //     0x6b3fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b3fcc: cmp             SP, x16
    //     0x6b3fd0: b.ls            #0x6b4000
    // 0x6b3fd4: LoadField: r1 = r2->field_33
    //     0x6b3fd4: ldur            w1, [x2, #0x33]
    // 0x6b3fd8: DecompressPointer r1
    //     0x6b3fd8: add             x1, x1, HEAP, lsl #32
    // 0x6b3fdc: cmp             w1, NULL
    // 0x6b3fe0: b.eq            #0x6b4008
    // 0x6b3fe4: r0 = of()
    //     0x6b3fe4: bl              #0x6b8cd8  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalGroup::of
    // 0x6b3fe8: mov             x1, x0
    // 0x6b3fec: ldur            x2, [fp, #-8]
    // 0x6b3ff0: r0 = previous()
    //     0x6b3ff0: bl              #0x6b400c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::previous
    // 0x6b3ff4: LeaveFrame
    //     0x6b3ff4: mov             SP, fp
    //     0x6b3ff8: ldp             fp, lr, [SP], #0x10
    // 0x6b3ffc: ret
    //     0x6b3ffc: ret             
    // 0x6b4000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4000: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4004: b               #0x6b3fd4
    // 0x6b4008: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b4008: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ rect(/* No info */) {
    // ** addr: 0x6b72b0, size: 0x174
    // 0x6b72b0: EnterFrame
    //     0x6b72b0: stp             fp, lr, [SP, #-0x10]!
    //     0x6b72b4: mov             fp, SP
    // 0x6b72b8: AllocStack(0x38)
    //     0x6b72b8: sub             SP, SP, #0x38
    // 0x6b72bc: CheckStackOverflow
    //     0x6b72bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b72c0: cmp             SP, x16
    //     0x6b72c4: b.ls            #0x6b7414
    // 0x6b72c8: LoadField: r0 = r1->field_33
    //     0x6b72c8: ldur            w0, [x1, #0x33]
    // 0x6b72cc: DecompressPointer r0
    //     0x6b72cc: add             x0, x0, HEAP, lsl #32
    // 0x6b72d0: cmp             w0, NULL
    // 0x6b72d4: b.eq            #0x6b741c
    // 0x6b72d8: mov             x1, x0
    // 0x6b72dc: r0 = renderObject()
    //     0x6b72dc: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0x6b72e0: stur            x0, [fp, #-8]
    // 0x6b72e4: cmp             w0, NULL
    // 0x6b72e8: b.eq            #0x6b7420
    // 0x6b72ec: mov             x1, x0
    // 0x6b72f0: r2 = Null
    //     0x6b72f0: mov             x2, NULL
    // 0x6b72f4: r0 = getTransformTo()
    //     0x6b72f4: bl              #0x67fce0  ; [package:flutter/src/rendering/object.dart] RenderObject::getTransformTo
    // 0x6b72f8: mov             x3, x0
    // 0x6b72fc: ldur            x2, [fp, #-8]
    // 0x6b7300: stur            x3, [fp, #-0x10]
    // 0x6b7304: r0 = LoadClassIdInstr(r2)
    //     0x6b7304: ldur            x0, [x2, #-1]
    //     0x6b7308: ubfx            x0, x0, #0xc, #0x14
    // 0x6b730c: mov             x1, x2
    // 0x6b7310: r0 = GDT[cid_x0 + 0x11b12]()
    //     0x6b7310: movz            x17, #0x1b12
    //     0x6b7314: movk            x17, #0x1, lsl #16
    //     0x6b7318: add             lr, x0, x17
    //     0x6b731c: ldr             lr, [x21, lr, lsl #3]
    //     0x6b7320: blr             lr
    // 0x6b7324: LoadField: d0 = r0->field_7
    //     0x6b7324: ldur            d0, [x0, #7]
    // 0x6b7328: stur            d0, [fp, #-0x28]
    // 0x6b732c: LoadField: d1 = r0->field_f
    //     0x6b732c: ldur            d1, [x0, #0xf]
    // 0x6b7330: stur            d1, [fp, #-0x20]
    // 0x6b7334: r0 = Offset()
    //     0x6b7334: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x6b7338: ldur            d0, [fp, #-0x28]
    // 0x6b733c: StoreField: r0->field_7 = d0
    //     0x6b733c: stur            d0, [x0, #7]
    // 0x6b7340: ldur            d0, [fp, #-0x20]
    // 0x6b7344: StoreField: r0->field_f = d0
    //     0x6b7344: stur            d0, [x0, #0xf]
    // 0x6b7348: ldur            x1, [fp, #-0x10]
    // 0x6b734c: mov             x2, x0
    // 0x6b7350: r0 = transformPoint()
    //     0x6b7350: bl              #0x67fb98  ; [package:flutter/src/painting/matrix_utils.dart] MatrixUtils::transformPoint
    // 0x6b7354: ldur            x1, [fp, #-8]
    // 0x6b7358: r2 = Null
    //     0x6b7358: mov             x2, NULL
    // 0x6b735c: stur            x0, [fp, #-0x10]
    // 0x6b7360: r0 = getTransformTo()
    //     0x6b7360: bl              #0x67fce0  ; [package:flutter/src/rendering/object.dart] RenderObject::getTransformTo
    // 0x6b7364: mov             x2, x0
    // 0x6b7368: ldur            x1, [fp, #-8]
    // 0x6b736c: stur            x2, [fp, #-0x18]
    // 0x6b7370: r0 = LoadClassIdInstr(r1)
    //     0x6b7370: ldur            x0, [x1, #-1]
    //     0x6b7374: ubfx            x0, x0, #0xc, #0x14
    // 0x6b7378: r0 = GDT[cid_x0 + 0x11b12]()
    //     0x6b7378: movz            x17, #0x1b12
    //     0x6b737c: movk            x17, #0x1, lsl #16
    //     0x6b7380: add             lr, x0, x17
    //     0x6b7384: ldr             lr, [x21, lr, lsl #3]
    //     0x6b7388: blr             lr
    // 0x6b738c: ArrayLoad: d0 = r0[0]  ; List_8
    //     0x6b738c: ldur            d0, [x0, #0x17]
    // 0x6b7390: stur            d0, [fp, #-0x28]
    // 0x6b7394: LoadField: d1 = r0->field_1f
    //     0x6b7394: ldur            d1, [x0, #0x1f]
    // 0x6b7398: stur            d1, [fp, #-0x20]
    // 0x6b739c: r0 = Offset()
    //     0x6b739c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x6b73a0: ldur            d0, [fp, #-0x28]
    // 0x6b73a4: StoreField: r0->field_7 = d0
    //     0x6b73a4: stur            d0, [x0, #7]
    // 0x6b73a8: ldur            d0, [fp, #-0x20]
    // 0x6b73ac: StoreField: r0->field_f = d0
    //     0x6b73ac: stur            d0, [x0, #0xf]
    // 0x6b73b0: ldur            x1, [fp, #-0x18]
    // 0x6b73b4: mov             x2, x0
    // 0x6b73b8: r0 = transformPoint()
    //     0x6b73b8: bl              #0x67fb98  ; [package:flutter/src/painting/matrix_utils.dart] MatrixUtils::transformPoint
    // 0x6b73bc: mov             x1, x0
    // 0x6b73c0: ldur            x0, [fp, #-0x10]
    // 0x6b73c4: LoadField: d0 = r0->field_7
    //     0x6b73c4: ldur            d0, [x0, #7]
    // 0x6b73c8: stur            d0, [fp, #-0x38]
    // 0x6b73cc: LoadField: d1 = r0->field_f
    //     0x6b73cc: ldur            d1, [x0, #0xf]
    // 0x6b73d0: stur            d1, [fp, #-0x30]
    // 0x6b73d4: LoadField: d2 = r1->field_7
    //     0x6b73d4: ldur            d2, [x1, #7]
    // 0x6b73d8: stur            d2, [fp, #-0x28]
    // 0x6b73dc: LoadField: d3 = r1->field_f
    //     0x6b73dc: ldur            d3, [x1, #0xf]
    // 0x6b73e0: stur            d3, [fp, #-0x20]
    // 0x6b73e4: r0 = Rect()
    //     0x6b73e4: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x6b73e8: ldur            d0, [fp, #-0x38]
    // 0x6b73ec: StoreField: r0->field_7 = d0
    //     0x6b73ec: stur            d0, [x0, #7]
    // 0x6b73f0: ldur            d0, [fp, #-0x30]
    // 0x6b73f4: StoreField: r0->field_f = d0
    //     0x6b73f4: stur            d0, [x0, #0xf]
    // 0x6b73f8: ldur            d0, [fp, #-0x28]
    // 0x6b73fc: ArrayStore: r0[0] = d0  ; List_8
    //     0x6b73fc: stur            d0, [x0, #0x17]
    // 0x6b7400: ldur            d0, [fp, #-0x20]
    // 0x6b7404: StoreField: r0->field_1f = d0
    //     0x6b7404: stur            d0, [x0, #0x1f]
    // 0x6b7408: LeaveFrame
    //     0x6b7408: mov             SP, fp
    //     0x6b740c: ldp             fp, lr, [SP], #0x10
    // 0x6b7410: ret
    //     0x6b7410: ret             
    // 0x6b7414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b7414: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b7418: b               #0x6b72c8
    // 0x6b741c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b741c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b7420: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b7420: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ skipTraversal(/* No info */) {
    // ** addr: 0x6b7ad8, size: 0xf0
    // 0x6b7ad8: EnterFrame
    //     0x6b7ad8: stp             fp, lr, [SP, #-0x10]!
    //     0x6b7adc: mov             fp, SP
    // 0x6b7ae0: AllocStack(0x20)
    //     0x6b7ae0: sub             SP, SP, #0x20
    // 0x6b7ae4: CheckStackOverflow
    //     0x6b7ae4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b7ae8: cmp             SP, x16
    //     0x6b7aec: b.ls            #0x6b7bb8
    // 0x6b7af0: LoadField: r0 = r1->field_23
    //     0x6b7af0: ldur            w0, [x1, #0x23]
    // 0x6b7af4: DecompressPointer r0
    //     0x6b7af4: add             x0, x0, HEAP, lsl #32
    // 0x6b7af8: tbnz            w0, #4, #0x6b7b0c
    // 0x6b7afc: r0 = true
    //     0x6b7afc: add             x0, NULL, #0x20  ; true
    // 0x6b7b00: LeaveFrame
    //     0x6b7b00: mov             SP, fp
    //     0x6b7b04: ldp             fp, lr, [SP], #0x10
    // 0x6b7b08: ret
    //     0x6b7b08: ret             
    // 0x6b7b0c: r0 = ancestors()
    //     0x6b7b0c: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x6b7b10: LoadField: r3 = r0->field_7
    //     0x6b7b10: ldur            w3, [x0, #7]
    // 0x6b7b14: DecompressPointer r3
    //     0x6b7b14: add             x3, x3, HEAP, lsl #32
    // 0x6b7b18: stur            x3, [fp, #-0x20]
    // 0x6b7b1c: LoadField: r1 = r0->field_b
    //     0x6b7b1c: ldur            w1, [x0, #0xb]
    // 0x6b7b20: r4 = LoadInt32Instr(r1)
    //     0x6b7b20: sbfx            x4, x1, #1, #0x1f
    // 0x6b7b24: stur            x4, [fp, #-0x18]
    // 0x6b7b28: LoadField: r5 = r0->field_f
    //     0x6b7b28: ldur            w5, [x0, #0xf]
    // 0x6b7b2c: DecompressPointer r5
    //     0x6b7b2c: add             x5, x5, HEAP, lsl #32
    // 0x6b7b30: stur            x5, [fp, #-0x10]
    // 0x6b7b34: r0 = 0
    //     0x6b7b34: movz            x0, #0
    // 0x6b7b38: CheckStackOverflow
    //     0x6b7b38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b7b3c: cmp             SP, x16
    //     0x6b7b40: b.ls            #0x6b7bc0
    // 0x6b7b44: cmp             x0, x4
    // 0x6b7b48: b.ge            #0x6b7ba8
    // 0x6b7b4c: ArrayLoad: r1 = r5[r0]  ; Unknown_4
    //     0x6b7b4c: add             x16, x5, x0, lsl #2
    //     0x6b7b50: ldur            w1, [x16, #0xf]
    // 0x6b7b54: DecompressPointer r1
    //     0x6b7b54: add             x1, x1, HEAP, lsl #32
    // 0x6b7b58: add             x6, x0, #1
    // 0x6b7b5c: stur            x6, [fp, #-8]
    // 0x6b7b60: cmp             w1, NULL
    // 0x6b7b64: b.ne            #0x6b7b94
    // 0x6b7b68: mov             x0, x1
    // 0x6b7b6c: mov             x2, x3
    // 0x6b7b70: r1 = Null
    //     0x6b7b70: mov             x1, NULL
    // 0x6b7b74: cmp             w2, NULL
    // 0x6b7b78: b.eq            #0x6b7b94
    // 0x6b7b7c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6b7b7c: ldur            w4, [x2, #0x17]
    // 0x6b7b80: DecompressPointer r4
    //     0x6b7b80: add             x4, x4, HEAP, lsl #32
    // 0x6b7b84: r8 = X0
    //     0x6b7b84: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6b7b88: LoadField: r9 = r4->field_7
    //     0x6b7b88: ldur            x9, [x4, #7]
    // 0x6b7b8c: r3 = Null
    //     0x6b7b8c: ldr             x3, [PP, #0x7390]  ; [pp+0x7390] Null
    // 0x6b7b90: blr             x9
    // 0x6b7b94: ldur            x0, [fp, #-8]
    // 0x6b7b98: ldur            x3, [fp, #-0x20]
    // 0x6b7b9c: ldur            x5, [fp, #-0x10]
    // 0x6b7ba0: ldur            x4, [fp, #-0x18]
    // 0x6b7ba4: b               #0x6b7b38
    // 0x6b7ba8: r0 = false
    //     0x6b7ba8: add             x0, NULL, #0x30  ; false
    // 0x6b7bac: LeaveFrame
    //     0x6b7bac: mov             SP, fp
    //     0x6b7bb0: ldp             fp, lr, [SP], #0x10
    // 0x6b7bb4: ret
    //     0x6b7bb4: ret             
    // 0x6b7bb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b7bb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b7bbc: b               #0x6b7af0
    // 0x6b7bc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b7bc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b7bc4: b               #0x6b7b44
  }
  _ nextFocus(/* No info */) {
    // ** addr: 0x6b8d10, size: 0x58
    // 0x6b8d10: EnterFrame
    //     0x6b8d10: stp             fp, lr, [SP, #-0x10]!
    //     0x6b8d14: mov             fp, SP
    // 0x6b8d18: AllocStack(0x8)
    //     0x6b8d18: sub             SP, SP, #8
    // 0x6b8d1c: SetupParameters(FocusNode this /* r1 => r2, fp-0x8 */)
    //     0x6b8d1c: mov             x2, x1
    //     0x6b8d20: stur            x1, [fp, #-8]
    // 0x6b8d24: CheckStackOverflow
    //     0x6b8d24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b8d28: cmp             SP, x16
    //     0x6b8d2c: b.ls            #0x6b8d5c
    // 0x6b8d30: LoadField: r1 = r2->field_33
    //     0x6b8d30: ldur            w1, [x2, #0x33]
    // 0x6b8d34: DecompressPointer r1
    //     0x6b8d34: add             x1, x1, HEAP, lsl #32
    // 0x6b8d38: cmp             w1, NULL
    // 0x6b8d3c: b.eq            #0x6b8d64
    // 0x6b8d40: r0 = of()
    //     0x6b8d40: bl              #0x6b8cd8  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalGroup::of
    // 0x6b8d44: mov             x1, x0
    // 0x6b8d48: ldur            x2, [fp, #-8]
    // 0x6b8d4c: r0 = next()
    //     0x6b8d4c: bl              #0x6b8d68  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::next
    // 0x6b8d50: LeaveFrame
    //     0x6b8d50: mov             SP, fp
    //     0x6b8d54: ldp             fp, lr, [SP], #0x10
    // 0x6b8d58: ret
    //     0x6b8d58: ret             
    // 0x6b8d5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b8d5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b8d60: b               #0x6b8d30
    // 0x6b8d64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b8d64: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  set _ canRequestFocus=(/* No info */) {
    // ** addr: 0x93a5e4, size: 0x98
    // 0x93a5e4: EnterFrame
    //     0x93a5e4: stp             fp, lr, [SP, #-0x10]!
    //     0x93a5e8: mov             fp, SP
    // 0x93a5ec: AllocStack(0x18)
    //     0x93a5ec: sub             SP, SP, #0x18
    // 0x93a5f0: SetupParameters(FocusNode this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x93a5f0: mov             x0, x1
    //     0x93a5f4: stur            x1, [fp, #-8]
    //     0x93a5f8: stur            x2, [fp, #-0x10]
    // 0x93a5fc: CheckStackOverflow
    //     0x93a5fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93a600: cmp             SP, x16
    //     0x93a604: b.ls            #0x93a674
    // 0x93a608: LoadField: r1 = r0->field_27
    //     0x93a608: ldur            w1, [x0, #0x27]
    // 0x93a60c: DecompressPointer r1
    //     0x93a60c: add             x1, x1, HEAP, lsl #32
    // 0x93a610: cmp             w2, w1
    // 0x93a614: b.eq            #0x93a664
    // 0x93a618: StoreField: r0->field_27 = r2
    //     0x93a618: stur            w2, [x0, #0x27]
    // 0x93a61c: mov             x1, x0
    // 0x93a620: r0 = hasFocus()
    //     0x93a620: bl              #0x6511a0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::hasFocus
    // 0x93a624: tbnz            w0, #4, #0x93a64c
    // 0x93a628: ldur            x0, [fp, #-0x10]
    // 0x93a62c: tbz             w0, #4, #0x93a64c
    // 0x93a630: r16 = Instance_UnfocusDisposition
    //     0x93a630: add             x16, PP, #0x23, lsl #12  ; [pp+0x23938] Obj!UnfocusDisposition@e345a1
    //     0x93a634: ldr             x16, [x16, #0x938]
    // 0x93a638: str             x16, [SP]
    // 0x93a63c: ldur            x1, [fp, #-8]
    // 0x93a640: r4 = const [0, 0x2, 0x1, 0x1, disposition, 0x1, null]
    //     0x93a640: add             x4, PP, #0x23, lsl #12  ; [pp+0x23940] List(7) [0, 0x2, 0x1, 0x1, "disposition", 0x1, Null]
    //     0x93a644: ldr             x4, [x4, #0x940]
    // 0x93a648: r0 = unfocus()
    //     0x93a648: bl              #0x6a834c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::unfocus
    // 0x93a64c: ldur            x2, [fp, #-8]
    // 0x93a650: LoadField: r1 = r2->field_3f
    //     0x93a650: ldur            w1, [x2, #0x3f]
    // 0x93a654: DecompressPointer r1
    //     0x93a654: add             x1, x1, HEAP, lsl #32
    // 0x93a658: cmp             w1, NULL
    // 0x93a65c: b.eq            #0x93a664
    // 0x93a660: r0 = _markPropertiesChanged()
    //     0x93a660: bl              #0x93a67c  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::_markPropertiesChanged
    // 0x93a664: r0 = Null
    //     0x93a664: mov             x0, NULL
    // 0x93a668: LeaveFrame
    //     0x93a668: mov             SP, fp
    //     0x93a66c: ldp             fp, lr, [SP], #0x10
    // 0x93a670: ret
    //     0x93a670: ret             
    // 0x93a674: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93a674: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93a678: b               #0x93a608
  }
  _ consumeKeyboardToken(/* No info */) {
    // ** addr: 0x9408a4, size: 0x24
    // 0x9408a4: LoadField: r2 = r1->field_4b
    //     0x9408a4: ldur            w2, [x1, #0x4b]
    // 0x9408a8: DecompressPointer r2
    //     0x9408a8: add             x2, x2, HEAP, lsl #32
    // 0x9408ac: tbz             w2, #4, #0x9408b8
    // 0x9408b0: r0 = false
    //     0x9408b0: add             x0, NULL, #0x30  ; false
    // 0x9408b4: ret
    //     0x9408b4: ret             
    // 0x9408b8: r2 = false
    //     0x9408b8: add             x2, NULL, #0x30  ; false
    // 0x9408bc: StoreField: r1->field_4b = r2
    //     0x9408bc: stur            w2, [x1, #0x4b]
    // 0x9408c0: r0 = true
    //     0x9408c0: add             x0, NULL, #0x20  ; true
    // 0x9408c4: ret
    //     0x9408c4: ret             
  }
  _ attach(/* No info */) {
    // ** addr: 0x9411c4, size: 0xe0
    // 0x9411c4: EnterFrame
    //     0x9411c4: stp             fp, lr, [SP, #-0x10]!
    //     0x9411c8: mov             fp, SP
    // 0x9411cc: AllocStack(0x8)
    //     0x9411cc: sub             SP, SP, #8
    // 0x9411d0: SetupParameters(FocusNode this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0 */, {dynamic onKeyEvent = Null /* r2 */})
    //     0x9411d0: mov             x0, x2
    //     0x9411d4: stur            x1, [fp, #-8]
    //     0x9411d8: ldur            w2, [x4, #0x13]
    //     0x9411dc: ldur            w3, [x4, #0x1f]
    //     0x9411e0: add             x3, x3, HEAP, lsl #32
    //     0x9411e4: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3a488] "onKeyEvent"
    //     0x9411e8: ldr             x16, [x16, #0x488]
    //     0x9411ec: cmp             w3, w16
    //     0x9411f0: b.ne            #0x94120c
    //     0x9411f4: ldur            w3, [x4, #0x23]
    //     0x9411f8: add             x3, x3, HEAP, lsl #32
    //     0x9411fc: sub             w4, w2, w3
    //     0x941200: add             x2, fp, w4, sxtw #2
    //     0x941204: ldr             x2, [x2, #8]
    //     0x941208: b               #0x941210
    //     0x94120c: mov             x2, NULL
    // 0x941210: StoreField: r1->field_33 = r0
    //     0x941210: stur            w0, [x1, #0x33]
    //     0x941214: ldurb           w16, [x1, #-1]
    //     0x941218: ldurb           w17, [x0, #-1]
    //     0x94121c: and             x16, x17, x16, lsr #2
    //     0x941220: tst             x16, HEAP, lsr #32
    //     0x941224: b.eq            #0x94122c
    //     0x941228: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x94122c: StoreField: r1->field_37 = rNULL
    //     0x94122c: stur            NULL, [x1, #0x37]
    // 0x941230: cmp             w2, NULL
    // 0x941234: b.ne            #0x941244
    // 0x941238: LoadField: r0 = r1->field_3b
    //     0x941238: ldur            w0, [x1, #0x3b]
    // 0x94123c: DecompressPointer r0
    //     0x94123c: add             x0, x0, HEAP, lsl #32
    // 0x941240: b               #0x941248
    // 0x941244: mov             x0, x2
    // 0x941248: StoreField: r1->field_3b = r0
    //     0x941248: stur            w0, [x1, #0x3b]
    //     0x94124c: ldurb           w16, [x1, #-1]
    //     0x941250: ldurb           w17, [x0, #-1]
    //     0x941254: and             x16, x17, x16, lsr #2
    //     0x941258: tst             x16, HEAP, lsr #32
    //     0x94125c: b.eq            #0x941264
    //     0x941260: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x941264: r0 = FocusAttachment()
    //     0x941264: bl              #0x9412a4  ; AllocateFocusAttachmentStub -> FocusAttachment (size=0xc)
    // 0x941268: mov             x2, x0
    // 0x94126c: ldur            x1, [fp, #-8]
    // 0x941270: StoreField: r2->field_7 = r1
    //     0x941270: stur            w1, [x2, #7]
    // 0x941274: mov             x0, x2
    // 0x941278: StoreField: r1->field_5b = r0
    //     0x941278: stur            w0, [x1, #0x5b]
    //     0x94127c: ldurb           w16, [x1, #-1]
    //     0x941280: ldurb           w17, [x0, #-1]
    //     0x941284: and             x16, x17, x16, lsr #2
    //     0x941288: tst             x16, HEAP, lsr #32
    //     0x94128c: b.eq            #0x941294
    //     0x941290: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x941294: mov             x0, x2
    // 0x941298: LeaveFrame
    //     0x941298: mov             SP, fp
    //     0x94129c: ldp             fp, lr, [SP], #0x10
    // 0x9412a0: ret
    //     0x9412a0: ret             
  }
  set _ skipTraversal=(/* No info */) {
    // ** addr: 0x9412b0, size: 0x5c
    // 0x9412b0: EnterFrame
    //     0x9412b0: stp             fp, lr, [SP, #-0x10]!
    //     0x9412b4: mov             fp, SP
    // 0x9412b8: mov             x0, x1
    // 0x9412bc: CheckStackOverflow
    //     0x9412bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9412c0: cmp             SP, x16
    //     0x9412c4: b.ls            #0x941304
    // 0x9412c8: LoadField: r1 = r0->field_23
    //     0x9412c8: ldur            w1, [x0, #0x23]
    // 0x9412cc: DecompressPointer r1
    //     0x9412cc: add             x1, x1, HEAP, lsl #32
    // 0x9412d0: cmp             w2, w1
    // 0x9412d4: b.eq            #0x9412f4
    // 0x9412d8: StoreField: r0->field_23 = r2
    //     0x9412d8: stur            w2, [x0, #0x23]
    // 0x9412dc: LoadField: r1 = r0->field_3f
    //     0x9412dc: ldur            w1, [x0, #0x3f]
    // 0x9412e0: DecompressPointer r1
    //     0x9412e0: add             x1, x1, HEAP, lsl #32
    // 0x9412e4: cmp             w1, NULL
    // 0x9412e8: b.eq            #0x9412f4
    // 0x9412ec: mov             x2, x0
    // 0x9412f0: r0 = _markPropertiesChanged()
    //     0x9412f0: bl              #0x93a67c  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::_markPropertiesChanged
    // 0x9412f4: r0 = Null
    //     0x9412f4: mov             x0, NULL
    // 0x9412f8: LeaveFrame
    //     0x9412f8: mov             SP, fp
    //     0x9412fc: ldp             fp, lr, [SP], #0x10
    // 0x941300: ret
    //     0x941300: ret             
    // 0x941304: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941304: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941308: b               #0x9412c8
  }
  set _ descendantsAreFocusable=(/* No info */) {
    // ** addr: 0x94130c, size: 0xa0
    // 0x94130c: EnterFrame
    //     0x94130c: stp             fp, lr, [SP, #-0x10]!
    //     0x941310: mov             fp, SP
    // 0x941314: AllocStack(0x10)
    //     0x941314: sub             SP, SP, #0x10
    // 0x941318: SetupParameters(FocusNode this /* r1 => r0, fp-0x8 */)
    //     0x941318: mov             x0, x1
    //     0x94131c: stur            x1, [fp, #-8]
    // 0x941320: CheckStackOverflow
    //     0x941320: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941324: cmp             SP, x16
    //     0x941328: b.ls            #0x9413a4
    // 0x94132c: LoadField: r1 = r0->field_2b
    //     0x94132c: ldur            w1, [x0, #0x2b]
    // 0x941330: DecompressPointer r1
    //     0x941330: add             x1, x1, HEAP, lsl #32
    // 0x941334: cmp             w2, w1
    // 0x941338: b.ne            #0x94134c
    // 0x94133c: r0 = Null
    //     0x94133c: mov             x0, NULL
    // 0x941340: LeaveFrame
    //     0x941340: mov             SP, fp
    //     0x941344: ldp             fp, lr, [SP], #0x10
    // 0x941348: ret
    //     0x941348: ret             
    // 0x94134c: StoreField: r0->field_2b = r2
    //     0x94134c: stur            w2, [x0, #0x2b]
    // 0x941350: tbz             w2, #4, #0x94137c
    // 0x941354: mov             x1, x0
    // 0x941358: r0 = hasFocus()
    //     0x941358: bl              #0x6511a0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::hasFocus
    // 0x94135c: tbnz            w0, #4, #0x94137c
    // 0x941360: r16 = Instance_UnfocusDisposition
    //     0x941360: add             x16, PP, #0x23, lsl #12  ; [pp+0x23938] Obj!UnfocusDisposition@e345a1
    //     0x941364: ldr             x16, [x16, #0x938]
    // 0x941368: str             x16, [SP]
    // 0x94136c: ldur            x1, [fp, #-8]
    // 0x941370: r4 = const [0, 0x2, 0x1, 0x1, disposition, 0x1, null]
    //     0x941370: add             x4, PP, #0x23, lsl #12  ; [pp+0x23940] List(7) [0, 0x2, 0x1, 0x1, "disposition", 0x1, Null]
    //     0x941374: ldr             x4, [x4, #0x940]
    // 0x941378: r0 = unfocus()
    //     0x941378: bl              #0x6a834c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::unfocus
    // 0x94137c: ldur            x2, [fp, #-8]
    // 0x941380: LoadField: r1 = r2->field_3f
    //     0x941380: ldur            w1, [x2, #0x3f]
    // 0x941384: DecompressPointer r1
    //     0x941384: add             x1, x1, HEAP, lsl #32
    // 0x941388: cmp             w1, NULL
    // 0x94138c: b.eq            #0x941394
    // 0x941390: r0 = _markPropertiesChanged()
    //     0x941390: bl              #0x93a67c  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::_markPropertiesChanged
    // 0x941394: r0 = Null
    //     0x941394: mov             x0, NULL
    // 0x941398: LeaveFrame
    //     0x941398: mov             SP, fp
    //     0x94139c: ldp             fp, lr, [SP], #0x10
    // 0x9413a0: ret
    //     0x9413a0: ret             
    // 0x9413a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9413a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9413a8: b               #0x94132c
  }
  _ focusInDirection(/* No info */) {
    // ** addr: 0xa56d58, size: 0x64
    // 0xa56d58: EnterFrame
    //     0xa56d58: stp             fp, lr, [SP, #-0x10]!
    //     0xa56d5c: mov             fp, SP
    // 0xa56d60: AllocStack(0x10)
    //     0xa56d60: sub             SP, SP, #0x10
    // 0xa56d64: SetupParameters(FocusNode this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xa56d64: mov             x3, x2
    //     0xa56d68: stur            x2, [fp, #-0x10]
    //     0xa56d6c: mov             x2, x1
    //     0xa56d70: stur            x1, [fp, #-8]
    // 0xa56d74: CheckStackOverflow
    //     0xa56d74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa56d78: cmp             SP, x16
    //     0xa56d7c: b.ls            #0xa56db0
    // 0xa56d80: LoadField: r1 = r2->field_33
    //     0xa56d80: ldur            w1, [x2, #0x33]
    // 0xa56d84: DecompressPointer r1
    //     0xa56d84: add             x1, x1, HEAP, lsl #32
    // 0xa56d88: cmp             w1, NULL
    // 0xa56d8c: b.eq            #0xa56db8
    // 0xa56d90: r0 = of()
    //     0xa56d90: bl              #0x6b8cd8  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalGroup::of
    // 0xa56d94: mov             x1, x0
    // 0xa56d98: ldur            x2, [fp, #-8]
    // 0xa56d9c: ldur            x3, [fp, #-0x10]
    // 0xa56da0: r0 = inDirection()
    //     0xa56da0: bl              #0xa56dbc  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::inDirection
    // 0xa56da4: LeaveFrame
    //     0xa56da4: mov             SP, fp
    //     0xa56da8: ldp             fp, lr, [SP], #0x10
    // 0xa56dac: ret
    //     0xa56dac: ret             
    // 0xa56db0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa56db0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa56db4: b               #0xa56d80
    // 0xa56db8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa56db8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ traversalDescendants(/* No info */) {
    // ** addr: 0xa58a38, size: 0x7c
    // 0xa58a38: EnterFrame
    //     0xa58a38: stp             fp, lr, [SP, #-0x10]!
    //     0xa58a3c: mov             fp, SP
    // 0xa58a40: AllocStack(0x8)
    //     0xa58a40: sub             SP, SP, #8
    // 0xa58a44: CheckStackOverflow
    //     0xa58a44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa58a48: cmp             SP, x16
    //     0xa58a4c: b.ls            #0xa58aac
    // 0xa58a50: LoadField: r0 = r1->field_27
    //     0xa58a50: ldur            w0, [x1, #0x27]
    // 0xa58a54: DecompressPointer r0
    //     0xa58a54: add             x0, x0, HEAP, lsl #32
    // 0xa58a58: tbnz            w0, #4, #0xa58a98
    // 0xa58a5c: LoadField: r0 = r1->field_2b
    //     0xa58a5c: ldur            w0, [x1, #0x2b]
    // 0xa58a60: DecompressPointer r0
    //     0xa58a60: add             x0, x0, HEAP, lsl #32
    // 0xa58a64: tbnz            w0, #4, #0xa58a98
    // 0xa58a68: r0 = descendants()
    //     0xa58a68: bl              #0x651cc0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::descendants
    // 0xa58a6c: r1 = Function '<anonymous closure>':.
    //     0xa58a6c: add             x1, PP, #0x59, lsl #12  ; [pp+0x59ea8] AnonymousClosure: (0xa58ab4), in [package:flutter/src/widgets/focus_manager.dart] FocusNode::traversalDescendants (0xa58a38)
    //     0xa58a70: ldr             x1, [x1, #0xea8]
    // 0xa58a74: r2 = Null
    //     0xa58a74: mov             x2, NULL
    // 0xa58a78: stur            x0, [fp, #-8]
    // 0xa58a7c: r0 = AllocateClosure()
    //     0xa58a7c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa58a80: ldur            x1, [fp, #-8]
    // 0xa58a84: mov             x2, x0
    // 0xa58a88: r0 = where()
    //     0xa58a88: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xa58a8c: LeaveFrame
    //     0xa58a8c: mov             SP, fp
    //     0xa58a90: ldp             fp, lr, [SP], #0x10
    // 0xa58a94: ret
    //     0xa58a94: ret             
    // 0xa58a98: r0 = Instance_EmptyIterable
    //     0xa58a98: add             x0, PP, #0x59, lsl #12  ; [pp+0x59df0] Obj!EmptyIterable<FocusNode>@e3a3a1
    //     0xa58a9c: ldr             x0, [x0, #0xdf0]
    // 0xa58aa0: LeaveFrame
    //     0xa58aa0: mov             SP, fp
    //     0xa58aa4: ldp             fp, lr, [SP], #0x10
    // 0xa58aa8: ret
    //     0xa58aa8: ret             
    // 0xa58aac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa58aac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa58ab0: b               #0xa58a50
  }
  [closure] bool <anonymous closure>(dynamic, FocusNode) {
    // ** addr: 0xa58ab4, size: 0x44
    // 0xa58ab4: EnterFrame
    //     0xa58ab4: stp             fp, lr, [SP, #-0x10]!
    //     0xa58ab8: mov             fp, SP
    // 0xa58abc: CheckStackOverflow
    //     0xa58abc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa58ac0: cmp             SP, x16
    //     0xa58ac4: b.ls            #0xa58af0
    // 0xa58ac8: ldr             x1, [fp, #0x10]
    // 0xa58acc: r0 = skipTraversal()
    //     0xa58acc: bl              #0x6b7ad8  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::skipTraversal
    // 0xa58ad0: tbz             w0, #4, #0xa58ae0
    // 0xa58ad4: ldr             x1, [fp, #0x10]
    // 0xa58ad8: r0 = canRequestFocus()
    //     0xa58ad8: bl              #0x6a8938  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::canRequestFocus
    // 0xa58adc: b               #0xa58ae4
    // 0xa58ae0: r0 = false
    //     0xa58ae0: add             x0, NULL, #0x30  ; false
    // 0xa58ae4: LeaveFrame
    //     0xa58ae4: mov             SP, fp
    //     0xa58ae8: ldp             fp, lr, [SP], #0x10
    // 0xa58aec: ret
    //     0xa58aec: ret             
    // 0xa58af0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa58af0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa58af4: b               #0xa58ac8
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa8b4f0, size: 0x54
    // 0xa8b4f0: EnterFrame
    //     0xa8b4f0: stp             fp, lr, [SP, #-0x10]!
    //     0xa8b4f4: mov             fp, SP
    // 0xa8b4f8: AllocStack(0x8)
    //     0xa8b4f8: sub             SP, SP, #8
    // 0xa8b4fc: SetupParameters(FocusNode this /* r1 => r0, fp-0x8 */)
    //     0xa8b4fc: mov             x0, x1
    //     0xa8b500: stur            x1, [fp, #-8]
    // 0xa8b504: CheckStackOverflow
    //     0xa8b504: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8b508: cmp             SP, x16
    //     0xa8b50c: b.ls            #0xa8b53c
    // 0xa8b510: LoadField: r1 = r0->field_5b
    //     0xa8b510: ldur            w1, [x0, #0x5b]
    // 0xa8b514: DecompressPointer r1
    //     0xa8b514: add             x1, x1, HEAP, lsl #32
    // 0xa8b518: cmp             w1, NULL
    // 0xa8b51c: b.eq            #0xa8b524
    // 0xa8b520: r0 = detach()
    //     0xa8b520: bl              #0x98f8c0  ; [package:flutter/src/widgets/focus_manager.dart] FocusAttachment::detach
    // 0xa8b524: ldur            x1, [fp, #-8]
    // 0xa8b528: r0 = dispose()
    //     0xa8b528: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0xa8b52c: r0 = Null
    //     0xa8b52c: mov             x0, NULL
    // 0xa8b530: LeaveFrame
    //     0xa8b530: mov             SP, fp
    //     0xa8b534: ldp             fp, lr, [SP], #0x10
    // 0xa8b538: ret
    //     0xa8b538: ret             
    // 0xa8b53c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8b53c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8b540: b               #0xa8b510
  }
  _ _markNextFocus(/* No info */) {
    // ** addr: 0xdaac34, size: 0x88
    // 0xdaac34: EnterFrame
    //     0xdaac34: stp             fp, lr, [SP, #-0x10]!
    //     0xdaac38: mov             fp, SP
    // 0xdaac3c: AllocStack(0x10)
    //     0xdaac3c: sub             SP, SP, #0x10
    // 0xdaac40: SetupParameters(FocusNode this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xdaac40: mov             x0, x2
    //     0xdaac44: stur            x2, [fp, #-0x10]
    //     0xdaac48: mov             x2, x1
    //     0xdaac4c: stur            x1, [fp, #-8]
    // 0xdaac50: CheckStackOverflow
    //     0xdaac50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdaac54: cmp             SP, x16
    //     0xdaac58: b.ls            #0xdaacb4
    // 0xdaac5c: LoadField: r1 = r2->field_3f
    //     0xdaac5c: ldur            w1, [x2, #0x3f]
    // 0xdaac60: DecompressPointer r1
    //     0xdaac60: add             x1, x1, HEAP, lsl #32
    // 0xdaac64: cmp             w1, NULL
    // 0xdaac68: b.eq            #0xdaac80
    // 0xdaac6c: r0 = _markNextFocus()
    //     0xdaac6c: bl              #0xdaacbc  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::_markNextFocus
    // 0xdaac70: r0 = Null
    //     0xdaac70: mov             x0, NULL
    // 0xdaac74: LeaveFrame
    //     0xdaac74: mov             SP, fp
    //     0xdaac78: ldp             fp, lr, [SP], #0x10
    // 0xdaac7c: ret
    //     0xdaac7c: ret             
    // 0xdaac80: mov             x1, x0
    // 0xdaac84: r0 = _setAsFocusedChildForScope()
    //     0xdaac84: bl              #0x650d3c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_setAsFocusedChildForScope
    // 0xdaac88: ldur            x1, [fp, #-0x10]
    // 0xdaac8c: r0 = _notify()
    //     0xdaac8c: bl              #0x69003c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_notify
    // 0xdaac90: ldur            x1, [fp, #-8]
    // 0xdaac94: ldur            x0, [fp, #-0x10]
    // 0xdaac98: cmp             w0, w1
    // 0xdaac9c: b.eq            #0xdaaca4
    // 0xdaaca0: r0 = _notify()
    //     0xdaaca0: bl              #0x69003c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_notify
    // 0xdaaca4: r0 = Null
    //     0xdaaca4: mov             x0, NULL
    // 0xdaaca8: LeaveFrame
    //     0xdaaca8: mov             SP, fp
    //     0xdaacac: ldp             fp, lr, [SP], #0x10
    // 0xdaacb0: ret
    //     0xdaacb0: ret             
    // 0xdaacb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdaacb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdaacb8: b               #0xdaac5c
  }
  _ _doRequestFocus(/* No info */) {
    // ** addr: 0xdaad5c, size: 0x100
    // 0xdaad5c: EnterFrame
    //     0xdaad5c: stp             fp, lr, [SP, #-0x10]!
    //     0xdaad60: mov             fp, SP
    // 0xdaad64: AllocStack(0x18)
    //     0xdaad64: sub             SP, SP, #0x18
    // 0xdaad68: SetupParameters(FocusNode this /* r1 => r0, fp-0x8 */)
    //     0xdaad68: mov             x0, x1
    //     0xdaad6c: stur            x1, [fp, #-8]
    // 0xdaad70: CheckStackOverflow
    //     0xdaad70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdaad74: cmp             SP, x16
    //     0xdaad78: b.ls            #0xdaae50
    // 0xdaad7c: mov             x1, x0
    // 0xdaad80: r0 = canRequestFocus()
    //     0xdaad80: bl              #0x6a8938  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::canRequestFocus
    // 0xdaad84: tbz             w0, #4, #0xdaad98
    // 0xdaad88: r0 = Null
    //     0xdaad88: mov             x0, NULL
    // 0xdaad8c: LeaveFrame
    //     0xdaad8c: mov             SP, fp
    //     0xdaad90: ldp             fp, lr, [SP], #0x10
    // 0xdaad94: ret
    //     0xdaad94: ret             
    // 0xdaad98: ldur            x0, [fp, #-8]
    // 0xdaad9c: LoadField: r1 = r0->field_4f
    //     0xdaad9c: ldur            w1, [x0, #0x4f]
    // 0xdaada0: DecompressPointer r1
    //     0xdaada0: add             x1, x1, HEAP, lsl #32
    // 0xdaada4: cmp             w1, NULL
    // 0xdaada8: b.ne            #0xdaadc4
    // 0xdaadac: r2 = true
    //     0xdaadac: add             x2, NULL, #0x20  ; true
    // 0xdaadb0: StoreField: r0->field_63 = r2
    //     0xdaadb0: stur            w2, [x0, #0x63]
    // 0xdaadb4: r0 = Null
    //     0xdaadb4: mov             x0, NULL
    // 0xdaadb8: LeaveFrame
    //     0xdaadb8: mov             SP, fp
    //     0xdaadbc: ldp             fp, lr, [SP], #0x10
    // 0xdaadc0: ret
    //     0xdaadc0: ret             
    // 0xdaadc4: r2 = true
    //     0xdaadc4: add             x2, NULL, #0x20  ; true
    // 0xdaadc8: mov             x1, x0
    // 0xdaadcc: r0 = _setAsFocusedChildForScope()
    //     0xdaadcc: bl              #0x650d3c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_setAsFocusedChildForScope
    // 0xdaadd0: ldur            x1, [fp, #-8]
    // 0xdaadd4: r0 = hasPrimaryFocus()
    //     0xdaadd4: bl              #0x651240  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::hasPrimaryFocus
    // 0xdaadd8: tbnz            w0, #4, #0xdaae2c
    // 0xdaaddc: ldur            x2, [fp, #-8]
    // 0xdaade0: LoadField: r0 = r2->field_3f
    //     0xdaade0: ldur            w0, [x2, #0x3f]
    // 0xdaade4: DecompressPointer r0
    //     0xdaade4: add             x0, x0, HEAP, lsl #32
    // 0xdaade8: cmp             w0, NULL
    // 0xdaadec: b.eq            #0xdaae58
    // 0xdaadf0: LoadField: r1 = r0->field_3b
    //     0xdaadf0: ldur            w1, [x0, #0x3b]
    // 0xdaadf4: DecompressPointer r1
    //     0xdaadf4: add             x1, x1, HEAP, lsl #32
    // 0xdaadf8: cmp             w1, NULL
    // 0xdaadfc: b.eq            #0xdaae1c
    // 0xdaae00: r0 = LoadClassIdInstr(r1)
    //     0xdaae00: ldur            x0, [x1, #-1]
    //     0xdaae04: ubfx            x0, x0, #0xc, #0x14
    // 0xdaae08: stp             x2, x1, [SP]
    // 0xdaae0c: mov             lr, x0
    // 0xdaae10: ldr             lr, [x21, lr, lsl #3]
    // 0xdaae14: blr             lr
    // 0xdaae18: tbnz            w0, #4, #0xdaae2c
    // 0xdaae1c: r0 = Null
    //     0xdaae1c: mov             x0, NULL
    // 0xdaae20: LeaveFrame
    //     0xdaae20: mov             SP, fp
    //     0xdaae24: ldp             fp, lr, [SP], #0x10
    // 0xdaae28: ret
    //     0xdaae28: ret             
    // 0xdaae2c: ldur            x2, [fp, #-8]
    // 0xdaae30: r0 = true
    //     0xdaae30: add             x0, NULL, #0x20  ; true
    // 0xdaae34: StoreField: r2->field_4b = r0
    //     0xdaae34: stur            w0, [x2, #0x4b]
    // 0xdaae38: mov             x1, x2
    // 0xdaae3c: r0 = _markNextFocus()
    //     0xdaae3c: bl              #0xdaac34  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_markNextFocus
    // 0xdaae40: r0 = Null
    //     0xdaae40: mov             x0, NULL
    // 0xdaae44: LeaveFrame
    //     0xdaae44: mov             SP, fp
    //     0xdaae48: ldp             fp, lr, [SP], #0x10
    // 0xdaae4c: ret
    //     0xdaae4c: ret             
    // 0xdaae50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdaae50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdaae54: b               #0xdaad7c
    // 0xdaae58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdaae58: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 2921, size: 0x70, field offset: 0x68
class FocusScopeNode extends FocusNode {

  _ setFirstFocus(/* No info */) {
    // ** addr: 0x650c78, size: 0x80
    // 0x650c78: EnterFrame
    //     0x650c78: stp             fp, lr, [SP, #-0x10]!
    //     0x650c7c: mov             fp, SP
    // 0x650c80: AllocStack(0x10)
    //     0x650c80: sub             SP, SP, #0x10
    // 0x650c84: SetupParameters(FocusScopeNode this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x650c84: mov             x3, x1
    //     0x650c88: mov             x0, x2
    //     0x650c8c: stur            x1, [fp, #-8]
    //     0x650c90: stur            x2, [fp, #-0x10]
    // 0x650c94: CheckStackOverflow
    //     0x650c94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x650c98: cmp             SP, x16
    //     0x650c9c: b.ls            #0x650cf0
    // 0x650ca0: LoadField: r1 = r0->field_4f
    //     0x650ca0: ldur            w1, [x0, #0x4f]
    // 0x650ca4: DecompressPointer r1
    //     0x650ca4: add             x1, x1, HEAP, lsl #32
    // 0x650ca8: cmp             w1, NULL
    // 0x650cac: b.ne            #0x650cbc
    // 0x650cb0: mov             x1, x3
    // 0x650cb4: mov             x2, x0
    // 0x650cb8: r0 = _reparent()
    //     0x650cb8: bl              #0x6512ac  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_reparent
    // 0x650cbc: ldur            x1, [fp, #-8]
    // 0x650cc0: r0 = hasFocus()
    //     0x650cc0: bl              #0x6511a0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::hasFocus
    // 0x650cc4: tbnz            w0, #4, #0x650cd8
    // 0x650cc8: ldur            x1, [fp, #-0x10]
    // 0x650ccc: r2 = true
    //     0x650ccc: add             x2, NULL, #0x20  ; true
    // 0x650cd0: r0 = _doRequestFocus()
    //     0x650cd0: bl              #0xdaa9f0  ; [package:flutter/src/widgets/focus_manager.dart] FocusScopeNode::_doRequestFocus
    // 0x650cd4: b               #0x650ce0
    // 0x650cd8: ldur            x1, [fp, #-0x10]
    // 0x650cdc: r0 = _setAsFocusedChildForScope()
    //     0x650cdc: bl              #0x650d3c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_setAsFocusedChildForScope
    // 0x650ce0: r0 = Null
    //     0x650ce0: mov             x0, NULL
    // 0x650ce4: LeaveFrame
    //     0x650ce4: mov             SP, fp
    //     0x650ce8: ldp             fp, lr, [SP], #0x10
    // 0x650cec: ret
    //     0x650cec: ret             
    // 0x650cf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x650cf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x650cf4: b               #0x650ca0
  }
  get _ focusedChild(/* No info */) {
    // ** addr: 0x690548, size: 0x44
    // 0x690548: EnterFrame
    //     0x690548: stp             fp, lr, [SP, #-0x10]!
    //     0x69054c: mov             fp, SP
    // 0x690550: AllocStack(0x10)
    //     0x690550: sub             SP, SP, #0x10
    // 0x690554: CheckStackOverflow
    //     0x690554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x690558: cmp             SP, x16
    //     0x69055c: b.ls            #0x690584
    // 0x690560: LoadField: r0 = r1->field_6b
    //     0x690560: ldur            w0, [x1, #0x6b]
    // 0x690564: DecompressPointer r0
    //     0x690564: add             x0, x0, HEAP, lsl #32
    // 0x690568: r16 = <FocusNode>
    //     0x690568: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x69056c: stp             x0, x16, [SP]
    // 0x690570: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x690570: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x690574: r0 = IterableExtensions.lastOrNull()
    //     0x690574: bl              #0x69058c  ; [dart:collection] ::IterableExtensions.lastOrNull
    // 0x690578: LeaveFrame
    //     0x690578: mov             SP, fp
    //     0x69057c: ldp             fp, lr, [SP], #0x10
    // 0x690580: ret
    //     0x690580: ret             
    // 0x690584: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x690584: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x690588: b               #0x690560
  }
  _ FocusScopeNode(/* No info */) {
    // ** addr: 0x693c70, size: 0x17c
    // 0x693c70: EnterFrame
    //     0x693c70: stp             fp, lr, [SP, #-0x10]!
    //     0x693c74: mov             fp, SP
    // 0x693c78: AllocStack(0x40)
    //     0x693c78: sub             SP, SP, #0x40
    // 0x693c7c: SetupParameters(FocusScopeNode this /* r1 => r0, fp-0x20 */, {dynamic canRequestFocus = true /* r3, fp-0x18 */, dynamic debugLabel = Null /* r5, fp-0x10 */, dynamic skipTraversal = false /* r4, fp-0x8 */})
    //     0x693c7c: mov             x0, x1
    //     0x693c80: stur            x1, [fp, #-0x20]
    //     0x693c84: ldur            w1, [x4, #0x13]
    //     0x693c88: ldur            w2, [x4, #0x1f]
    //     0x693c8c: add             x2, x2, HEAP, lsl #32
    //     0x693c90: ldr             x16, [PP, #0x2588]  ; [pp+0x2588] "canRequestFocus"
    //     0x693c94: cmp             w2, w16
    //     0x693c98: b.ne            #0x693cbc
    //     0x693c9c: ldur            w2, [x4, #0x23]
    //     0x693ca0: add             x2, x2, HEAP, lsl #32
    //     0x693ca4: sub             w3, w1, w2
    //     0x693ca8: add             x2, fp, w3, sxtw #2
    //     0x693cac: ldr             x2, [x2, #8]
    //     0x693cb0: mov             x3, x2
    //     0x693cb4: movz            x2, #0x1
    //     0x693cb8: b               #0x693cc4
    //     0x693cbc: add             x3, NULL, #0x20  ; true
    //     0x693cc0: movz            x2, #0
    //     0x693cc4: stur            x3, [fp, #-0x18]
    //     0x693cc8: lsl             x5, x2, #1
    //     0x693ccc: lsl             w6, w5, #1
    //     0x693cd0: add             w7, w6, #8
    //     0x693cd4: add             x16, x4, w7, sxtw #1
    //     0x693cd8: ldur            w8, [x16, #0xf]
    //     0x693cdc: add             x8, x8, HEAP, lsl #32
    //     0x693ce0: ldr             x16, [PP, #0x2590]  ; [pp+0x2590] "debugLabel"
    //     0x693ce4: cmp             w8, w16
    //     0x693ce8: b.ne            #0x693d1c
    //     0x693cec: add             w2, w6, #0xa
    //     0x693cf0: add             x16, x4, w2, sxtw #1
    //     0x693cf4: ldur            w6, [x16, #0xf]
    //     0x693cf8: add             x6, x6, HEAP, lsl #32
    //     0x693cfc: sub             w2, w1, w6
    //     0x693d00: add             x6, fp, w2, sxtw #2
    //     0x693d04: ldr             x6, [x6, #8]
    //     0x693d08: add             w2, w5, #2
    //     0x693d0c: sbfx            x5, x2, #1, #0x1f
    //     0x693d10: mov             x2, x5
    //     0x693d14: mov             x5, x6
    //     0x693d18: b               #0x693d20
    //     0x693d1c: mov             x5, NULL
    //     0x693d20: stur            x5, [fp, #-0x10]
    //     0x693d24: lsl             x6, x2, #1
    //     0x693d28: lsl             w2, w6, #1
    //     0x693d2c: add             w6, w2, #8
    //     0x693d30: add             x16, x4, w6, sxtw #1
    //     0x693d34: ldur            w7, [x16, #0xf]
    //     0x693d38: add             x7, x7, HEAP, lsl #32
    //     0x693d3c: ldr             x16, [PP, #0x2598]  ; [pp+0x2598] "skipTraversal"
    //     0x693d40: cmp             w7, w16
    //     0x693d44: b.ne            #0x693d6c
    //     0x693d48: add             w6, w2, #0xa
    //     0x693d4c: add             x16, x4, w6, sxtw #1
    //     0x693d50: ldur            w2, [x16, #0xf]
    //     0x693d54: add             x2, x2, HEAP, lsl #32
    //     0x693d58: sub             w4, w1, w2
    //     0x693d5c: add             x1, fp, w4, sxtw #2
    //     0x693d60: ldr             x1, [x1, #8]
    //     0x693d64: mov             x4, x1
    //     0x693d68: b               #0x693d70
    //     0x693d6c: add             x4, NULL, #0x30  ; false
    //     0x693d70: stur            x4, [fp, #-8]
    // 0x693d74: CheckStackOverflow
    //     0x693d74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x693d78: cmp             SP, x16
    //     0x693d7c: b.ls            #0x693de4
    // 0x693d80: r1 = <FocusNode>
    //     0x693d80: ldr             x1, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x693d84: r2 = 0
    //     0x693d84: movz            x2, #0
    // 0x693d88: r0 = _GrowableList()
    //     0x693d88: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x693d8c: ldur            x1, [fp, #-0x20]
    // 0x693d90: StoreField: r1->field_6b = r0
    //     0x693d90: stur            w0, [x1, #0x6b]
    //     0x693d94: ldurb           w16, [x1, #-1]
    //     0x693d98: ldurb           w17, [x0, #-1]
    //     0x693d9c: and             x16, x17, x16, lsr #2
    //     0x693da0: tst             x16, HEAP, lsr #32
    //     0x693da4: b.eq            #0x693dac
    //     0x693da8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x693dac: r0 = Instance_TraversalEdgeBehavior
    //     0x693dac: ldr             x0, [PP, #0x25a0]  ; [pp+0x25a0] Obj!TraversalEdgeBehavior@e34461
    // 0x693db0: StoreField: r1->field_67 = r0
    //     0x693db0: stur            w0, [x1, #0x67]
    // 0x693db4: r16 = true
    //     0x693db4: add             x16, NULL, #0x20  ; true
    // 0x693db8: ldur            lr, [fp, #-0x10]
    // 0x693dbc: stp             lr, x16, [SP, #0x10]
    // 0x693dc0: ldur            x16, [fp, #-8]
    // 0x693dc4: ldur            lr, [fp, #-0x18]
    // 0x693dc8: stp             lr, x16, [SP]
    // 0x693dcc: r4 = const [0, 0x5, 0x4, 0x1, canRequestFocus, 0x4, debugLabel, 0x2, descendantsAreFocusable, 0x1, skipTraversal, 0x3, null]
    //     0x693dcc: ldr             x4, [PP, #0x25a8]  ; [pp+0x25a8] List(13) [0, 0x5, 0x4, 0x1, "canRequestFocus", 0x4, "debugLabel", 0x2, "descendantsAreFocusable", 0x1, "skipTraversal", 0x3, Null]
    // 0x693dd0: r0 = FocusNode()
    //     0x693dd0: bl              #0x693dec  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::FocusNode
    // 0x693dd4: r0 = Null
    //     0x693dd4: mov             x0, NULL
    // 0x693dd8: LeaveFrame
    //     0x693dd8: mov             SP, fp
    //     0x693ddc: ldp             fp, lr, [SP], #0x10
    // 0x693de0: ret
    //     0x693de0: ret             
    // 0x693de4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x693de4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x693de8: b               #0x693d80
  }
  _ autofocus(/* No info */) {
    // ** addr: 0x98f694, size: 0x164
    // 0x98f694: EnterFrame
    //     0x98f694: stp             fp, lr, [SP, #-0x10]!
    //     0x98f698: mov             fp, SP
    // 0x98f69c: AllocStack(0x28)
    //     0x98f69c: sub             SP, SP, #0x28
    // 0x98f6a0: SetupParameters(FocusScopeNode this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x98f6a0: mov             x3, x1
    //     0x98f6a4: mov             x0, x2
    //     0x98f6a8: stur            x1, [fp, #-8]
    //     0x98f6ac: stur            x2, [fp, #-0x10]
    // 0x98f6b0: CheckStackOverflow
    //     0x98f6b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98f6b4: cmp             SP, x16
    //     0x98f6b8: b.ls            #0x98f7f0
    // 0x98f6bc: LoadField: r1 = r0->field_4f
    //     0x98f6bc: ldur            w1, [x0, #0x4f]
    // 0x98f6c0: DecompressPointer r1
    //     0x98f6c0: add             x1, x1, HEAP, lsl #32
    // 0x98f6c4: cmp             w1, NULL
    // 0x98f6c8: b.ne            #0x98f6d8
    // 0x98f6cc: mov             x1, x3
    // 0x98f6d0: mov             x2, x0
    // 0x98f6d4: r0 = _reparent()
    //     0x98f6d4: bl              #0x6512ac  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_reparent
    // 0x98f6d8: ldur            x0, [fp, #-8]
    // 0x98f6dc: LoadField: r1 = r0->field_3f
    //     0x98f6dc: ldur            w1, [x0, #0x3f]
    // 0x98f6e0: DecompressPointer r1
    //     0x98f6e0: add             x1, x1, HEAP, lsl #32
    // 0x98f6e4: cmp             w1, NULL
    // 0x98f6e8: b.eq            #0x98f7cc
    // 0x98f6ec: ldur            x2, [fp, #-0x10]
    // 0x98f6f0: LoadField: r3 = r1->field_3f
    //     0x98f6f0: ldur            w3, [x1, #0x3f]
    // 0x98f6f4: DecompressPointer r3
    //     0x98f6f4: add             x3, x3, HEAP, lsl #32
    // 0x98f6f8: stur            x3, [fp, #-0x18]
    // 0x98f6fc: r0 = _Autofocus()
    //     0x98f6fc: bl              #0x98f7f8  ; Allocate_AutofocusStub -> _Autofocus (size=0x10)
    // 0x98f700: mov             x4, x0
    // 0x98f704: ldur            x3, [fp, #-8]
    // 0x98f708: stur            x4, [fp, #-0x20]
    // 0x98f70c: StoreField: r4->field_7 = r3
    //     0x98f70c: stur            w3, [x4, #7]
    // 0x98f710: ldur            x0, [fp, #-0x10]
    // 0x98f714: StoreField: r4->field_b = r0
    //     0x98f714: stur            w0, [x4, #0xb]
    // 0x98f718: ldur            x5, [fp, #-0x18]
    // 0x98f71c: LoadField: r2 = r5->field_7
    //     0x98f71c: ldur            w2, [x5, #7]
    // 0x98f720: DecompressPointer r2
    //     0x98f720: add             x2, x2, HEAP, lsl #32
    // 0x98f724: mov             x0, x4
    // 0x98f728: r1 = Null
    //     0x98f728: mov             x1, NULL
    // 0x98f72c: cmp             w2, NULL
    // 0x98f730: b.eq            #0x98f750
    // 0x98f734: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x98f734: ldur            w4, [x2, #0x17]
    // 0x98f738: DecompressPointer r4
    //     0x98f738: add             x4, x4, HEAP, lsl #32
    // 0x98f73c: r8 = X0
    //     0x98f73c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x98f740: LoadField: r9 = r4->field_7
    //     0x98f740: ldur            x9, [x4, #7]
    // 0x98f744: r3 = Null
    //     0x98f744: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a470] Null
    //     0x98f748: ldr             x3, [x3, #0x470]
    // 0x98f74c: blr             x9
    // 0x98f750: ldur            x0, [fp, #-0x18]
    // 0x98f754: LoadField: r1 = r0->field_b
    //     0x98f754: ldur            w1, [x0, #0xb]
    // 0x98f758: LoadField: r2 = r0->field_f
    //     0x98f758: ldur            w2, [x0, #0xf]
    // 0x98f75c: DecompressPointer r2
    //     0x98f75c: add             x2, x2, HEAP, lsl #32
    // 0x98f760: LoadField: r3 = r2->field_b
    //     0x98f760: ldur            w3, [x2, #0xb]
    // 0x98f764: r2 = LoadInt32Instr(r1)
    //     0x98f764: sbfx            x2, x1, #1, #0x1f
    // 0x98f768: stur            x2, [fp, #-0x28]
    // 0x98f76c: r1 = LoadInt32Instr(r3)
    //     0x98f76c: sbfx            x1, x3, #1, #0x1f
    // 0x98f770: cmp             x2, x1
    // 0x98f774: b.ne            #0x98f780
    // 0x98f778: mov             x1, x0
    // 0x98f77c: r0 = _growToNextCapacity()
    //     0x98f77c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x98f780: ldur            x0, [fp, #-0x18]
    // 0x98f784: ldur            x2, [fp, #-0x28]
    // 0x98f788: add             x1, x2, #1
    // 0x98f78c: lsl             x3, x1, #1
    // 0x98f790: StoreField: r0->field_b = r3
    //     0x98f790: stur            w3, [x0, #0xb]
    // 0x98f794: LoadField: r1 = r0->field_f
    //     0x98f794: ldur            w1, [x0, #0xf]
    // 0x98f798: DecompressPointer r1
    //     0x98f798: add             x1, x1, HEAP, lsl #32
    // 0x98f79c: ldur            x0, [fp, #-0x20]
    // 0x98f7a0: ArrayStore: r1[r2] = r0  ; List_4
    //     0x98f7a0: add             x25, x1, x2, lsl #2
    //     0x98f7a4: add             x25, x25, #0xf
    //     0x98f7a8: str             w0, [x25]
    //     0x98f7ac: tbz             w0, #0, #0x98f7c8
    //     0x98f7b0: ldurb           w16, [x1, #-1]
    //     0x98f7b4: ldurb           w17, [x0, #-1]
    //     0x98f7b8: and             x16, x17, x16, lsr #2
    //     0x98f7bc: tst             x16, HEAP, lsr #32
    //     0x98f7c0: b.eq            #0x98f7c8
    //     0x98f7c4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x98f7c8: ldur            x0, [fp, #-8]
    // 0x98f7cc: LoadField: r1 = r0->field_3f
    //     0x98f7cc: ldur            w1, [x0, #0x3f]
    // 0x98f7d0: DecompressPointer r1
    //     0x98f7d0: add             x1, x1, HEAP, lsl #32
    // 0x98f7d4: cmp             w1, NULL
    // 0x98f7d8: b.eq            #0x98f7e0
    // 0x98f7dc: r0 = _markNeedsUpdate()
    //     0x98f7dc: bl              #0x93a6d4  ; [package:flutter/src/widgets/focus_manager.dart] FocusManager::_markNeedsUpdate
    // 0x98f7e0: r0 = Null
    //     0x98f7e0: mov             x0, NULL
    // 0x98f7e4: LeaveFrame
    //     0x98f7e4: mov             SP, fp
    //     0x98f7e8: ldp             fp, lr, [SP], #0x10
    // 0x98f7ec: ret
    //     0x98f7ec: ret             
    // 0x98f7f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98f7f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98f7f4: b               #0x98f6bc
  }
  get _ traversalDescendants(/* No info */) {
    // ** addr: 0xa58128, size: 0x5c
    // 0xa58128: EnterFrame
    //     0xa58128: stp             fp, lr, [SP, #-0x10]!
    //     0xa5812c: mov             fp, SP
    // 0xa58130: AllocStack(0x8)
    //     0xa58130: sub             SP, SP, #8
    // 0xa58134: SetupParameters(FocusScopeNode this /* r1 => r0, fp-0x8 */)
    //     0xa58134: mov             x0, x1
    //     0xa58138: stur            x1, [fp, #-8]
    // 0xa5813c: CheckStackOverflow
    //     0xa5813c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa58140: cmp             SP, x16
    //     0xa58144: b.ls            #0xa5817c
    // 0xa58148: mov             x1, x0
    // 0xa5814c: r0 = canRequestFocus()
    //     0xa5814c: bl              #0x6a8938  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::canRequestFocus
    // 0xa58150: tbz             w0, #4, #0xa58168
    // 0xa58154: r0 = Instance_EmptyIterable
    //     0xa58154: add             x0, PP, #0x59, lsl #12  ; [pp+0x59df0] Obj!EmptyIterable<FocusNode>@e3a3a1
    //     0xa58158: ldr             x0, [x0, #0xdf0]
    // 0xa5815c: LeaveFrame
    //     0xa5815c: mov             SP, fp
    //     0xa58160: ldp             fp, lr, [SP], #0x10
    // 0xa58164: ret
    //     0xa58164: ret             
    // 0xa58168: ldur            x1, [fp, #-8]
    // 0xa5816c: r0 = traversalDescendants()
    //     0xa5816c: bl              #0xa58a38  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::traversalDescendants
    // 0xa58170: LeaveFrame
    //     0xa58170: mov             SP, fp
    //     0xa58174: ldp             fp, lr, [SP], #0x10
    // 0xa58178: ret
    //     0xa58178: ret             
    // 0xa5817c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5817c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa58180: b               #0xa58148
  }
  _ _doRequestFocus(/* No info */) {
    // ** addr: 0xdaa9f0, size: 0x244
    // 0xdaa9f0: EnterFrame
    //     0xdaa9f0: stp             fp, lr, [SP, #-0x10]!
    //     0xdaa9f4: mov             fp, SP
    // 0xdaa9f8: AllocStack(0x30)
    //     0xdaa9f8: sub             SP, SP, #0x30
    // 0xdaa9fc: SetupParameters(FocusScopeNode this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xdaa9fc: mov             x3, x1
    //     0xdaaa00: stur            x1, [fp, #-0x10]
    //     0xdaaa04: stur            x2, [fp, #-0x18]
    // 0xdaaa08: CheckStackOverflow
    //     0xdaaa08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdaaa0c: cmp             SP, x16
    //     0xdaaa10: b.ls            #0xdaac18
    // 0xdaaa14: LoadField: r4 = r3->field_6b
    //     0xdaaa14: ldur            w4, [x3, #0x6b]
    // 0xdaaa18: DecompressPointer r4
    //     0xdaaa18: add             x4, x4, HEAP, lsl #32
    // 0xdaaa1c: stur            x4, [fp, #-8]
    // 0xdaaa20: CheckStackOverflow
    //     0xdaaa20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdaaa24: cmp             SP, x16
    //     0xdaaa28: b.ls            #0xdaac20
    // 0xdaaa2c: LoadField: r0 = r4->field_b
    //     0xdaaa2c: ldur            w0, [x4, #0xb]
    // 0xdaaa30: r1 = LoadInt32Instr(r0)
    //     0xdaaa30: sbfx            x1, x0, #1, #0x1f
    // 0xdaaa34: cbz             w0, #0xdaab7c
    // 0xdaaa38: cmp             x1, #0
    // 0xdaaa3c: b.le            #0xdaac0c
    // 0xdaaa40: sub             x5, x1, #1
    // 0xdaaa44: mov             x0, x1
    // 0xdaaa48: mov             x1, x5
    // 0xdaaa4c: cmp             x1, x0
    // 0xdaaa50: b.hs            #0xdaac28
    // 0xdaaa54: LoadField: r0 = r4->field_f
    //     0xdaaa54: ldur            w0, [x4, #0xf]
    // 0xdaaa58: DecompressPointer r0
    //     0xdaaa58: add             x0, x0, HEAP, lsl #32
    // 0xdaaa5c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xdaaa5c: add             x16, x0, x5, lsl #2
    //     0xdaaa60: ldur            w1, [x16, #0xf]
    // 0xdaaa64: DecompressPointer r1
    //     0xdaaa64: add             x1, x1, HEAP, lsl #32
    // 0xdaaa68: LoadField: r0 = r1->field_27
    //     0xdaaa68: ldur            w0, [x1, #0x27]
    // 0xdaaa6c: DecompressPointer r0
    //     0xdaaa6c: add             x0, x0, HEAP, lsl #32
    // 0xdaaa70: tbnz            w0, #4, #0xdaab44
    // 0xdaaa74: r0 = ancestors()
    //     0xdaaa74: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0xdaaa78: mov             x1, x0
    // 0xdaaa7c: r2 = Closure: (FocusNode) => bool from Function '_allowDescendantsToBeFocused@279042876': static.
    //     0xdaaa7c: ldr             x2, [PP, #0x4e18]  ; [pp+0x4e18] Closure: (FocusNode) => bool from Function '_allowDescendantsToBeFocused@279042876': static. (0x7e54fb0a8984)
    // 0xdaaa80: r0 = every()
    //     0xdaaa80: bl              #0x6a87c8  ; [dart:collection] ListBase::every
    // 0xdaaa84: tbnz            w0, #4, #0xdaab44
    // 0xdaaa88: ldur            x2, [fp, #-8]
    // 0xdaaa8c: LoadField: r0 = r2->field_b
    //     0xdaaa8c: ldur            w0, [x2, #0xb]
    // 0xdaaa90: r1 = LoadInt32Instr(r0)
    //     0xdaaa90: sbfx            x1, x0, #1, #0x1f
    // 0xdaaa94: cmp             x1, #0
    // 0xdaaa98: b.le            #0xdaac00
    // 0xdaaa9c: sub             x3, x1, #1
    // 0xdaaaa0: mov             x0, x1
    // 0xdaaaa4: mov             x1, x3
    // 0xdaaaa8: cmp             x1, x0
    // 0xdaaaac: b.hs            #0xdaac2c
    // 0xdaaab0: LoadField: r0 = r2->field_f
    //     0xdaaab0: ldur            w0, [x2, #0xf]
    // 0xdaaab4: DecompressPointer r0
    //     0xdaaab4: add             x0, x0, HEAP, lsl #32
    // 0xdaaab8: ArrayLoad: r4 = r0[r3]  ; Unknown_4
    //     0xdaaab8: add             x16, x0, x3, lsl #2
    //     0xdaaabc: ldur            w4, [x16, #0xf]
    // 0xdaaac0: DecompressPointer r4
    //     0xdaaac0: add             x4, x4, HEAP, lsl #32
    // 0xdaaac4: stur            x4, [fp, #-0x20]
    // 0xdaaac8: LoadField: r0 = r4->field_5f
    //     0xdaaac8: ldur            w0, [x4, #0x5f]
    // 0xdaaacc: DecompressPointer r0
    //     0xdaaacc: add             x0, x0, HEAP, lsl #32
    // 0xdaaad0: cmp             w0, NULL
    // 0xdaaad4: b.ne            #0xdaab7c
    // 0xdaaad8: LoadField: r1 = r4->field_4f
    //     0xdaaad8: ldur            w1, [x4, #0x4f]
    // 0xdaaadc: DecompressPointer r1
    //     0xdaaadc: add             x1, x1, HEAP, lsl #32
    // 0xdaaae0: cmp             w1, NULL
    // 0xdaaae4: b.ne            #0xdaaaf4
    // 0xdaaae8: mov             x1, x4
    // 0xdaaaec: r2 = Null
    //     0xdaaaec: mov             x2, NULL
    // 0xdaaaf0: b               #0xdaab1c
    // 0xdaaaf4: r0 = LoadClassIdInstr(r1)
    //     0xdaaaf4: ldur            x0, [x1, #-1]
    //     0xdaaaf8: ubfx            x0, x0, #0xc, #0x14
    // 0xdaaafc: sub             x16, x0, #0xb67
    // 0xdaab00: cmp             x16, #1
    // 0xdaab04: b.hi            #0xdaab10
    // 0xdaab08: r0 = enclosingScope()
    //     0xdaab08: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0xdaab0c: b               #0xdaab14
    // 0xdaab10: mov             x0, x1
    // 0xdaab14: mov             x2, x0
    // 0xdaab18: ldur            x1, [fp, #-0x20]
    // 0xdaab1c: mov             x0, x2
    // 0xdaab20: StoreField: r1->field_5f = r0
    //     0xdaab20: stur            w0, [x1, #0x5f]
    //     0xdaab24: ldurb           w16, [x1, #-1]
    //     0xdaab28: ldurb           w17, [x0, #-1]
    //     0xdaab2c: and             x16, x17, x16, lsr #2
    //     0xdaab30: tst             x16, HEAP, lsr #32
    //     0xdaab34: b.eq            #0xdaab3c
    //     0xdaab38: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xdaab3c: cmp             w2, NULL
    // 0xdaab40: b.ne            #0xdaab7c
    // 0xdaab44: ldur            x3, [fp, #-8]
    // 0xdaab48: LoadField: r0 = r3->field_b
    //     0xdaab48: ldur            w0, [x3, #0xb]
    // 0xdaab4c: r1 = LoadInt32Instr(r0)
    //     0xdaab4c: sbfx            x1, x0, #1, #0x1f
    // 0xdaab50: sub             x2, x1, #1
    // 0xdaab54: mov             x0, x1
    // 0xdaab58: mov             x1, x2
    // 0xdaab5c: cmp             x1, x0
    // 0xdaab60: b.hs            #0xdaac30
    // 0xdaab64: mov             x1, x3
    // 0xdaab68: r0 = length=()
    //     0xdaab68: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xdaab6c: ldur            x3, [fp, #-0x10]
    // 0xdaab70: ldur            x2, [fp, #-0x18]
    // 0xdaab74: ldur            x4, [fp, #-8]
    // 0xdaab78: b               #0xdaaa20
    // 0xdaab7c: ldur            x0, [fp, #-0x18]
    // 0xdaab80: r16 = <FocusNode>
    //     0xdaab80: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0xdaab84: ldur            lr, [fp, #-8]
    // 0xdaab88: stp             lr, x16, [SP]
    // 0xdaab8c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xdaab8c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xdaab90: r0 = IterableExtensions.lastOrNull()
    //     0xdaab90: bl              #0x69058c  ; [dart:collection] ::IterableExtensions.lastOrNull
    // 0xdaab94: mov             x1, x0
    // 0xdaab98: ldur            x0, [fp, #-0x18]
    // 0xdaab9c: tbnz            w0, #4, #0xdaaba8
    // 0xdaaba0: cmp             w1, NULL
    // 0xdaaba4: b.ne            #0xdaabd8
    // 0xdaaba8: ldur            x1, [fp, #-0x10]
    // 0xdaabac: r0 = canRequestFocus()
    //     0xdaabac: bl              #0x6a8938  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::canRequestFocus
    // 0xdaabb0: tbnz            w0, #4, #0xdaabc8
    // 0xdaabb4: ldur            x1, [fp, #-0x10]
    // 0xdaabb8: r0 = _setAsFocusedChildForScope()
    //     0xdaabb8: bl              #0x650d3c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_setAsFocusedChildForScope
    // 0xdaabbc: ldur            x1, [fp, #-0x10]
    // 0xdaabc0: ldur            x2, [fp, #-0x10]
    // 0xdaabc4: r0 = _markNextFocus()
    //     0xdaabc4: bl              #0xdaac34  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::_markNextFocus
    // 0xdaabc8: r0 = Null
    //     0xdaabc8: mov             x0, NULL
    // 0xdaabcc: LeaveFrame
    //     0xdaabcc: mov             SP, fp
    //     0xdaabd0: ldp             fp, lr, [SP], #0x10
    // 0xdaabd4: ret
    //     0xdaabd4: ret             
    // 0xdaabd8: r0 = LoadClassIdInstr(r1)
    //     0xdaabd8: ldur            x0, [x1, #-1]
    //     0xdaabdc: ubfx            x0, x0, #0xc, #0x14
    // 0xdaabe0: r2 = true
    //     0xdaabe0: add             x2, NULL, #0x20  ; true
    // 0xdaabe4: r0 = GDT[cid_x0 + -0xffc]()
    //     0xdaabe4: sub             lr, x0, #0xffc
    //     0xdaabe8: ldr             lr, [x21, lr, lsl #3]
    //     0xdaabec: blr             lr
    // 0xdaabf0: r0 = Null
    //     0xdaabf0: mov             x0, NULL
    // 0xdaabf4: LeaveFrame
    //     0xdaabf4: mov             SP, fp
    //     0xdaabf8: ldp             fp, lr, [SP], #0x10
    // 0xdaabfc: ret
    //     0xdaabfc: ret             
    // 0xdaac00: r0 = noElement()
    //     0xdaac00: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xdaac04: r0 = Throw()
    //     0xdaac04: bl              #0xec04b8  ; ThrowStub
    // 0xdaac08: brk             #0
    // 0xdaac0c: r0 = noElement()
    //     0xdaac0c: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xdaac10: r0 = Throw()
    //     0xdaac10: bl              #0xec04b8  ; ThrowStub
    // 0xdaac14: brk             #0
    // 0xdaac18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdaac18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdaac1c: b               #0xdaaa14
    // 0xdaac20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdaac20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdaac24: b               #0xdaaa2c
    // 0xdaac28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xdaac28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xdaac2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xdaac2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xdaac30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xdaac30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 6967, size: 0x14, field offset: 0x14
enum FocusHighlightStrategy extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4a814, size: 0x64
    // 0xc4a814: EnterFrame
    //     0xc4a814: stp             fp, lr, [SP, #-0x10]!
    //     0xc4a818: mov             fp, SP
    // 0xc4a81c: AllocStack(0x10)
    //     0xc4a81c: sub             SP, SP, #0x10
    // 0xc4a820: SetupParameters(FocusHighlightStrategy this /* r1 => r0, fp-0x8 */)
    //     0xc4a820: mov             x0, x1
    //     0xc4a824: stur            x1, [fp, #-8]
    // 0xc4a828: CheckStackOverflow
    //     0xc4a828: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4a82c: cmp             SP, x16
    //     0xc4a830: b.ls            #0xc4a870
    // 0xc4a834: r1 = Null
    //     0xc4a834: mov             x1, NULL
    // 0xc4a838: r2 = 4
    //     0xc4a838: movz            x2, #0x4
    // 0xc4a83c: r0 = AllocateArray()
    //     0xc4a83c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4a840: r16 = "FocusHighlightStrategy."
    //     0xc4a840: add             x16, PP, #0x22, lsl #12  ; [pp+0x225e8] "FocusHighlightStrategy."
    //     0xc4a844: ldr             x16, [x16, #0x5e8]
    // 0xc4a848: StoreField: r0->field_f = r16
    //     0xc4a848: stur            w16, [x0, #0xf]
    // 0xc4a84c: ldur            x1, [fp, #-8]
    // 0xc4a850: LoadField: r2 = r1->field_f
    //     0xc4a850: ldur            w2, [x1, #0xf]
    // 0xc4a854: DecompressPointer r2
    //     0xc4a854: add             x2, x2, HEAP, lsl #32
    // 0xc4a858: StoreField: r0->field_13 = r2
    //     0xc4a858: stur            w2, [x0, #0x13]
    // 0xc4a85c: str             x0, [SP]
    // 0xc4a860: r0 = _interpolate()
    //     0xc4a860: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4a864: LeaveFrame
    //     0xc4a864: mov             SP, fp
    //     0xc4a868: ldp             fp, lr, [SP], #0x10
    // 0xc4a86c: ret
    //     0xc4a86c: ret             
    // 0xc4a870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4a870: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4a874: b               #0xc4a834
  }
}

// class id: 6968, size: 0x14, field offset: 0x14
enum FocusHighlightMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4a7b0, size: 0x64
    // 0xc4a7b0: EnterFrame
    //     0xc4a7b0: stp             fp, lr, [SP, #-0x10]!
    //     0xc4a7b4: mov             fp, SP
    // 0xc4a7b8: AllocStack(0x10)
    //     0xc4a7b8: sub             SP, SP, #0x10
    // 0xc4a7bc: SetupParameters(FocusHighlightMode this /* r1 => r0, fp-0x8 */)
    //     0xc4a7bc: mov             x0, x1
    //     0xc4a7c0: stur            x1, [fp, #-8]
    // 0xc4a7c4: CheckStackOverflow
    //     0xc4a7c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4a7c8: cmp             SP, x16
    //     0xc4a7cc: b.ls            #0xc4a80c
    // 0xc4a7d0: r1 = Null
    //     0xc4a7d0: mov             x1, NULL
    // 0xc4a7d4: r2 = 4
    //     0xc4a7d4: movz            x2, #0x4
    // 0xc4a7d8: r0 = AllocateArray()
    //     0xc4a7d8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4a7dc: r16 = "FocusHighlightMode."
    //     0xc4a7dc: add             x16, PP, #0x22, lsl #12  ; [pp+0x225d8] "FocusHighlightMode."
    //     0xc4a7e0: ldr             x16, [x16, #0x5d8]
    // 0xc4a7e4: StoreField: r0->field_f = r16
    //     0xc4a7e4: stur            w16, [x0, #0xf]
    // 0xc4a7e8: ldur            x1, [fp, #-8]
    // 0xc4a7ec: LoadField: r2 = r1->field_f
    //     0xc4a7ec: ldur            w2, [x1, #0xf]
    // 0xc4a7f0: DecompressPointer r2
    //     0xc4a7f0: add             x2, x2, HEAP, lsl #32
    // 0xc4a7f4: StoreField: r0->field_13 = r2
    //     0xc4a7f4: stur            w2, [x0, #0x13]
    // 0xc4a7f8: str             x0, [SP]
    // 0xc4a7fc: r0 = _interpolate()
    //     0xc4a7fc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4a800: LeaveFrame
    //     0xc4a800: mov             SP, fp
    //     0xc4a804: ldp             fp, lr, [SP], #0x10
    // 0xc4a808: ret
    //     0xc4a808: ret             
    // 0xc4a80c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4a80c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4a810: b               #0xc4a7d0
  }
}

// class id: 6969, size: 0x14, field offset: 0x14
enum UnfocusDisposition extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4a74c, size: 0x64
    // 0xc4a74c: EnterFrame
    //     0xc4a74c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4a750: mov             fp, SP
    // 0xc4a754: AllocStack(0x10)
    //     0xc4a754: sub             SP, SP, #0x10
    // 0xc4a758: SetupParameters(UnfocusDisposition this /* r1 => r0, fp-0x8 */)
    //     0xc4a758: mov             x0, x1
    //     0xc4a75c: stur            x1, [fp, #-8]
    // 0xc4a760: CheckStackOverflow
    //     0xc4a760: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4a764: cmp             SP, x16
    //     0xc4a768: b.ls            #0xc4a7a8
    // 0xc4a76c: r1 = Null
    //     0xc4a76c: mov             x1, NULL
    // 0xc4a770: r2 = 4
    //     0xc4a770: movz            x2, #0x4
    // 0xc4a774: r0 = AllocateArray()
    //     0xc4a774: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4a778: r16 = "UnfocusDisposition."
    //     0xc4a778: add             x16, PP, #0x22, lsl #12  ; [pp+0x225d0] "UnfocusDisposition."
    //     0xc4a77c: ldr             x16, [x16, #0x5d0]
    // 0xc4a780: StoreField: r0->field_f = r16
    //     0xc4a780: stur            w16, [x0, #0xf]
    // 0xc4a784: ldur            x1, [fp, #-8]
    // 0xc4a788: LoadField: r2 = r1->field_f
    //     0xc4a788: ldur            w2, [x1, #0xf]
    // 0xc4a78c: DecompressPointer r2
    //     0xc4a78c: add             x2, x2, HEAP, lsl #32
    // 0xc4a790: StoreField: r0->field_13 = r2
    //     0xc4a790: stur            w2, [x0, #0x13]
    // 0xc4a794: str             x0, [SP]
    // 0xc4a798: r0 = _interpolate()
    //     0xc4a798: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4a79c: LeaveFrame
    //     0xc4a79c: mov             SP, fp
    //     0xc4a7a0: ldp             fp, lr, [SP], #0x10
    // 0xc4a7a4: ret
    //     0xc4a7a4: ret             
    // 0xc4a7a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4a7a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4a7ac: b               #0xc4a76c
  }
}

// class id: 6970, size: 0x14, field offset: 0x14
enum KeyEventResult extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4a6e8, size: 0x64
    // 0xc4a6e8: EnterFrame
    //     0xc4a6e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc4a6ec: mov             fp, SP
    // 0xc4a6f0: AllocStack(0x10)
    //     0xc4a6f0: sub             SP, SP, #0x10
    // 0xc4a6f4: SetupParameters(KeyEventResult this /* r1 => r0, fp-0x8 */)
    //     0xc4a6f4: mov             x0, x1
    //     0xc4a6f8: stur            x1, [fp, #-8]
    // 0xc4a6fc: CheckStackOverflow
    //     0xc4a6fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4a700: cmp             SP, x16
    //     0xc4a704: b.ls            #0xc4a744
    // 0xc4a708: r1 = Null
    //     0xc4a708: mov             x1, NULL
    // 0xc4a70c: r2 = 4
    //     0xc4a70c: movz            x2, #0x4
    // 0xc4a710: r0 = AllocateArray()
    //     0xc4a710: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4a714: r16 = "KeyEventResult."
    //     0xc4a714: add             x16, PP, #0x22, lsl #12  ; [pp+0x225e0] "KeyEventResult."
    //     0xc4a718: ldr             x16, [x16, #0x5e0]
    // 0xc4a71c: StoreField: r0->field_f = r16
    //     0xc4a71c: stur            w16, [x0, #0xf]
    // 0xc4a720: ldur            x1, [fp, #-8]
    // 0xc4a724: LoadField: r2 = r1->field_f
    //     0xc4a724: ldur            w2, [x1, #0xf]
    // 0xc4a728: DecompressPointer r2
    //     0xc4a728: add             x2, x2, HEAP, lsl #32
    // 0xc4a72c: StoreField: r0->field_13 = r2
    //     0xc4a72c: stur            w2, [x0, #0x13]
    // 0xc4a730: str             x0, [SP]
    // 0xc4a734: r0 = _interpolate()
    //     0xc4a734: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4a738: LeaveFrame
    //     0xc4a738: mov             SP, fp
    //     0xc4a73c: ldp             fp, lr, [SP], #0x10
    // 0xc4a740: ret
    //     0xc4a740: ret             
    // 0xc4a744: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4a744: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4a748: b               #0xc4a708
  }
}
