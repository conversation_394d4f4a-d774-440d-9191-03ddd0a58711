// lib: , url: package:flutter/src/widgets/focus_traversal.dart

// class id: 1049130, size: 0x8
class :: {

  [closure] static bool <anonymous closure>(dynamic, Element) {
    // ** addr: 0x6b60d8, size: 0xa4
    // 0x6b60d8: EnterFrame
    //     0x6b60d8: stp             fp, lr, [SP, #-0x10]!
    //     0x6b60dc: mov             fp, SP
    // 0x6b60e0: ldr             x2, [fp, #0x18]
    // 0x6b60e4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x6b60e4: ldur            w3, [x2, #0x17]
    // 0x6b60e8: DecompressPointer r3
    //     0x6b60e8: add             x3, x3, HEAP, lsl #32
    // 0x6b60ec: LoadField: r2 = r3->field_f
    //     0x6b60ec: ldur            w2, [x3, #0xf]
    // 0x6b60f0: DecompressPointer r2
    //     0x6b60f0: add             x2, x2, HEAP, lsl #32
    // 0x6b60f4: r4 = LoadInt32Instr(r2)
    //     0x6b60f4: sbfx            x4, x2, #1, #0x1f
    //     0x6b60f8: tbz             w2, #0, #0x6b6100
    //     0x6b60fc: ldur            x4, [x2, #7]
    // 0x6b6100: sub             x2, x4, #1
    // 0x6b6104: r0 = BoxInt64Instr(r2)
    //     0x6b6104: sbfiz           x0, x2, #1, #0x1f
    //     0x6b6108: cmp             x2, x0, asr #1
    //     0x6b610c: b.eq            #0x6b6118
    //     0x6b6110: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6b6114: stur            x2, [x0, #7]
    // 0x6b6118: StoreField: r3->field_f = r0
    //     0x6b6118: stur            w0, [x3, #0xf]
    //     0x6b611c: tbz             w0, #0, #0x6b6138
    //     0x6b6120: ldurb           w16, [x3, #-1]
    //     0x6b6124: ldurb           w17, [x0, #-1]
    //     0x6b6128: and             x16, x17, x16, lsr #2
    //     0x6b612c: tst             x16, HEAP, lsr #32
    //     0x6b6130: b.eq            #0x6b6138
    //     0x6b6134: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x6b6138: cbnz            x2, #0x6b616c
    // 0x6b613c: ldr             x0, [fp, #0x10]
    // 0x6b6140: StoreField: r3->field_13 = r0
    //     0x6b6140: stur            w0, [x3, #0x13]
    //     0x6b6144: ldurb           w16, [x3, #-1]
    //     0x6b6148: ldurb           w17, [x0, #-1]
    //     0x6b614c: and             x16, x17, x16, lsr #2
    //     0x6b6150: tst             x16, HEAP, lsr #32
    //     0x6b6154: b.eq            #0x6b615c
    //     0x6b6158: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x6b615c: r0 = false
    //     0x6b615c: add             x0, NULL, #0x30  ; false
    // 0x6b6160: LeaveFrame
    //     0x6b6160: mov             SP, fp
    //     0x6b6164: ldp             fp, lr, [SP], #0x10
    // 0x6b6168: ret
    //     0x6b6168: ret             
    // 0x6b616c: r0 = true
    //     0x6b616c: add             x0, NULL, #0x20  ; true
    // 0x6b6170: LeaveFrame
    //     0x6b6170: mov             SP, fp
    //     0x6b6174: ldp             fp, lr, [SP], #0x10
    // 0x6b6178: ret
    //     0x6b6178: ret             
  }
}

// class id: 2693, size: 0xc, field offset: 0x8
//   const constructor, 
class _DirectionalPolicyData extends Object {
}

// class id: 2694, size: 0x10, field offset: 0x8
//   const constructor, 
class _DirectionalPolicyDataEntry extends Object {
}

// class id: 2695, size: 0x10, field offset: 0x8
class _FocusTraversalGroupInfo extends Object {
}

// class id: 2920, size: 0x6c, field offset: 0x68
class _FocusTraversalGroupNode extends FocusNode {
}

// class id: 3787, size: 0x10, field offset: 0x8
class _ReadingOrderDirectionalGroupData extends _DiagnosticableTree&Object&Diagnosticable {

  static _ sortWithDirectionality(/* No info */) {
    // ** addr: 0x6b4fe4, size: 0x70
    // 0x6b4fe4: EnterFrame
    //     0x6b4fe4: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4fe8: mov             fp, SP
    // 0x6b4fec: AllocStack(0x28)
    //     0x6b4fec: sub             SP, SP, #0x28
    // 0x6b4ff0: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6b4ff0: stur            x1, [fp, #-8]
    //     0x6b4ff4: stur            x2, [fp, #-0x10]
    // 0x6b4ff8: CheckStackOverflow
    //     0x6b4ff8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4ffc: cmp             SP, x16
    //     0x6b5000: b.ls            #0x6b504c
    // 0x6b5004: r1 = 1
    //     0x6b5004: movz            x1, #0x1
    // 0x6b5008: r0 = AllocateContext()
    //     0x6b5008: bl              #0xec126c  ; AllocateContextStub
    // 0x6b500c: mov             x1, x0
    // 0x6b5010: ldur            x0, [fp, #-0x10]
    // 0x6b5014: StoreField: r1->field_f = r0
    //     0x6b5014: stur            w0, [x1, #0xf]
    // 0x6b5018: mov             x2, x1
    // 0x6b501c: r1 = Function '<anonymous closure>': static.
    //     0x6b501c: ldr             x1, [PP, #0x73c8]  ; [pp+0x73c8] AnonymousClosure: static (0x6b5074), in [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderDirectionalGroupData::sortWithDirectionality (0x6b4fe4)
    // 0x6b5020: r0 = AllocateClosure()
    //     0x6b5020: bl              #0xec1630  ; AllocateClosureStub
    // 0x6b5024: r16 = <_ReadingOrderDirectionalGroupData>
    //     0x6b5024: ldr             x16, [PP, #0x73d0]  ; [pp+0x73d0] TypeArguments: <_ReadingOrderDirectionalGroupData>
    // 0x6b5028: ldur            lr, [fp, #-8]
    // 0x6b502c: stp             lr, x16, [SP, #8]
    // 0x6b5030: str             x0, [SP]
    // 0x6b5034: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6b5034: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6b5038: r0 = mergeSort()
    //     0x6b5038: bl              #0x6b61b4  ; [package:flutter/src/foundation/collections.dart] ::mergeSort
    // 0x6b503c: r0 = Null
    //     0x6b503c: mov             x0, NULL
    // 0x6b5040: LeaveFrame
    //     0x6b5040: mov             SP, fp
    //     0x6b5044: ldp             fp, lr, [SP], #0x10
    // 0x6b5048: ret
    //     0x6b5048: ret             
    // 0x6b504c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b504c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b5050: b               #0x6b5004
  }
  [closure] static int <anonymous closure>(dynamic, _ReadingOrderDirectionalGroupData, _ReadingOrderDirectionalGroupData) {
    // ** addr: 0x6b5074, size: 0x244
    // 0x6b5074: EnterFrame
    //     0x6b5074: stp             fp, lr, [SP, #-0x10]!
    //     0x6b5078: mov             fp, SP
    // 0x6b507c: AllocStack(0x8)
    //     0x6b507c: sub             SP, SP, #8
    // 0x6b5080: SetupParameters()
    //     0x6b5080: ldr             x0, [fp, #0x20]
    //     0x6b5084: ldur            w1, [x0, #0x17]
    //     0x6b5088: add             x1, x1, HEAP, lsl #32
    // 0x6b508c: CheckStackOverflow
    //     0x6b508c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b5090: cmp             SP, x16
    //     0x6b5094: b.ls            #0x6b52b0
    // 0x6b5098: LoadField: r0 = r1->field_f
    //     0x6b5098: ldur            w0, [x1, #0xf]
    // 0x6b509c: DecompressPointer r0
    //     0x6b509c: add             x0, x0, HEAP, lsl #32
    // 0x6b50a0: LoadField: r1 = r0->field_7
    //     0x6b50a0: ldur            x1, [x0, #7]
    // 0x6b50a4: cmp             x1, #0
    // 0x6b50a8: b.gt            #0x6b51a8
    // 0x6b50ac: ldr             x1, [fp, #0x10]
    // 0x6b50b0: r0 = rect()
    //     0x6b50b0: bl              #0x6b52b8  ; [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderDirectionalGroupData::rect
    // 0x6b50b4: ArrayLoad: d0 = r0[0]  ; List_8
    //     0x6b50b4: ldur            d0, [x0, #0x17]
    // 0x6b50b8: ldr             x1, [fp, #0x18]
    // 0x6b50bc: stur            d0, [fp, #-8]
    // 0x6b50c0: r0 = rect()
    //     0x6b50c0: bl              #0x6b52b8  ; [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderDirectionalGroupData::rect
    // 0x6b50c4: ArrayLoad: d0 = r0[0]  ; List_8
    //     0x6b50c4: ldur            d0, [x0, #0x17]
    // 0x6b50c8: ldur            d1, [fp, #-8]
    // 0x6b50cc: fcmp            d0, d1
    // 0x6b50d0: b.le            #0x6b50dc
    // 0x6b50d4: r0 = -1
    //     0x6b50d4: movn            x0, #0
    // 0x6b50d8: b               #0x6b51a0
    // 0x6b50dc: fcmp            d1, d0
    // 0x6b50e0: b.le            #0x6b50ec
    // 0x6b50e4: r0 = 1
    //     0x6b50e4: movz            x0, #0x1
    // 0x6b50e8: b               #0x6b51a0
    // 0x6b50ec: fcmp            d1, d0
    // 0x6b50f0: b.ne            #0x6b517c
    // 0x6b50f4: d2 = 0.000000
    //     0x6b50f4: eor             v2.16b, v2.16b, v2.16b
    // 0x6b50f8: fcmp            d1, d2
    // 0x6b50fc: b.ne            #0x6b5174
    // 0x6b5100: fcmp            d1, #0.0
    // 0x6b5104: b.vs            #0x6b5118
    // 0x6b5108: b.ne            #0x6b5114
    // 0x6b510c: r1 = 0.000000
    //     0x6b510c: fmov            x1, d1
    // 0x6b5110: cmp             x1, #0
    // 0x6b5114: b.lt            #0x6b5120
    // 0x6b5118: r0 = false
    //     0x6b5118: add             x0, NULL, #0x30  ; false
    // 0x6b511c: b               #0x6b5124
    // 0x6b5120: r0 = true
    //     0x6b5120: add             x0, NULL, #0x20  ; true
    // 0x6b5124: fcmp            d0, #0.0
    // 0x6b5128: b.vs            #0x6b513c
    // 0x6b512c: b.ne            #0x6b5138
    // 0x6b5130: r2 = 0.000000
    //     0x6b5130: fmov            x2, d0
    // 0x6b5134: cmp             x2, #0
    // 0x6b5138: b.lt            #0x6b5144
    // 0x6b513c: r1 = false
    //     0x6b513c: add             x1, NULL, #0x30  ; false
    // 0x6b5140: b               #0x6b5148
    // 0x6b5144: r1 = true
    //     0x6b5144: add             x1, NULL, #0x20  ; true
    // 0x6b5148: cmp             w0, w1
    // 0x6b514c: b.ne            #0x6b5158
    // 0x6b5150: r0 = 0
    //     0x6b5150: movz            x0, #0
    // 0x6b5154: b               #0x6b51a0
    // 0x6b5158: tst             x0, #0x10
    // 0x6b515c: cset            x1, ne
    // 0x6b5160: sub             x1, x1, #1
    // 0x6b5164: and             x1, x1, #0xfffffffffffffffc
    // 0x6b5168: add             x1, x1, #2
    // 0x6b516c: r0 = LoadInt32Instr(r1)
    //     0x6b516c: sbfx            x0, x1, #1, #0x1f
    // 0x6b5170: b               #0x6b51a0
    // 0x6b5174: r0 = 0
    //     0x6b5174: movz            x0, #0
    // 0x6b5178: b               #0x6b51a0
    // 0x6b517c: fcmp            d1, d1
    // 0x6b5180: b.vc            #0x6b519c
    // 0x6b5184: fcmp            d0, d0
    // 0x6b5188: b.vc            #0x6b5194
    // 0x6b518c: r0 = 0
    //     0x6b518c: movz            x0, #0
    // 0x6b5190: b               #0x6b51a0
    // 0x6b5194: r0 = 1
    //     0x6b5194: movz            x0, #0x1
    // 0x6b5198: b               #0x6b51a0
    // 0x6b519c: r0 = -1
    //     0x6b519c: movn            x0, #0
    // 0x6b51a0: mov             x1, x0
    // 0x6b51a4: b               #0x6b52a0
    // 0x6b51a8: d2 = 0.000000
    //     0x6b51a8: eor             v2.16b, v2.16b, v2.16b
    // 0x6b51ac: ldr             x1, [fp, #0x18]
    // 0x6b51b0: r0 = rect()
    //     0x6b51b0: bl              #0x6b52b8  ; [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderDirectionalGroupData::rect
    // 0x6b51b4: LoadField: d0 = r0->field_7
    //     0x6b51b4: ldur            d0, [x0, #7]
    // 0x6b51b8: ldr             x1, [fp, #0x10]
    // 0x6b51bc: stur            d0, [fp, #-8]
    // 0x6b51c0: r0 = rect()
    //     0x6b51c0: bl              #0x6b52b8  ; [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderDirectionalGroupData::rect
    // 0x6b51c4: LoadField: d0 = r0->field_7
    //     0x6b51c4: ldur            d0, [x0, #7]
    // 0x6b51c8: ldur            d1, [fp, #-8]
    // 0x6b51cc: fcmp            d0, d1
    // 0x6b51d0: b.le            #0x6b51dc
    // 0x6b51d4: r1 = -1
    //     0x6b51d4: movn            x1, #0
    // 0x6b51d8: b               #0x6b52a0
    // 0x6b51dc: fcmp            d1, d0
    // 0x6b51e0: b.le            #0x6b51ec
    // 0x6b51e4: r1 = 1
    //     0x6b51e4: movz            x1, #0x1
    // 0x6b51e8: b               #0x6b52a0
    // 0x6b51ec: fcmp            d1, d0
    // 0x6b51f0: b.ne            #0x6b527c
    // 0x6b51f4: d2 = 0.000000
    //     0x6b51f4: eor             v2.16b, v2.16b, v2.16b
    // 0x6b51f8: fcmp            d1, d2
    // 0x6b51fc: b.ne            #0x6b5274
    // 0x6b5200: fcmp            d1, #0.0
    // 0x6b5204: b.vs            #0x6b5218
    // 0x6b5208: b.ne            #0x6b5214
    // 0x6b520c: r2 = 0.000000
    //     0x6b520c: fmov            x2, d1
    // 0x6b5210: cmp             x2, #0
    // 0x6b5214: b.lt            #0x6b5220
    // 0x6b5218: r1 = false
    //     0x6b5218: add             x1, NULL, #0x30  ; false
    // 0x6b521c: b               #0x6b5224
    // 0x6b5220: r1 = true
    //     0x6b5220: add             x1, NULL, #0x20  ; true
    // 0x6b5224: fcmp            d0, #0.0
    // 0x6b5228: b.vs            #0x6b523c
    // 0x6b522c: b.ne            #0x6b5238
    // 0x6b5230: r3 = 0.000000
    //     0x6b5230: fmov            x3, d0
    // 0x6b5234: cmp             x3, #0
    // 0x6b5238: b.lt            #0x6b5244
    // 0x6b523c: r2 = false
    //     0x6b523c: add             x2, NULL, #0x30  ; false
    // 0x6b5240: b               #0x6b5248
    // 0x6b5244: r2 = true
    //     0x6b5244: add             x2, NULL, #0x20  ; true
    // 0x6b5248: cmp             w1, w2
    // 0x6b524c: b.ne            #0x6b5258
    // 0x6b5250: r1 = 0
    //     0x6b5250: movz            x1, #0
    // 0x6b5254: b               #0x6b52a0
    // 0x6b5258: tst             x1, #0x10
    // 0x6b525c: cset            x2, ne
    // 0x6b5260: sub             x2, x2, #1
    // 0x6b5264: and             x2, x2, #0xfffffffffffffffc
    // 0x6b5268: add             x2, x2, #2
    // 0x6b526c: r1 = LoadInt32Instr(r2)
    //     0x6b526c: sbfx            x1, x2, #1, #0x1f
    // 0x6b5270: b               #0x6b52a0
    // 0x6b5274: r1 = 0
    //     0x6b5274: movz            x1, #0
    // 0x6b5278: b               #0x6b52a0
    // 0x6b527c: fcmp            d1, d1
    // 0x6b5280: b.vc            #0x6b529c
    // 0x6b5284: fcmp            d0, d0
    // 0x6b5288: b.vc            #0x6b5294
    // 0x6b528c: r1 = 0
    //     0x6b528c: movz            x1, #0
    // 0x6b5290: b               #0x6b52a0
    // 0x6b5294: r1 = 1
    //     0x6b5294: movz            x1, #0x1
    // 0x6b5298: b               #0x6b52a0
    // 0x6b529c: r1 = -1
    //     0x6b529c: movn            x1, #0
    // 0x6b52a0: lsl             x0, x1, #1
    // 0x6b52a4: LeaveFrame
    //     0x6b52a4: mov             SP, fp
    //     0x6b52a8: ldp             fp, lr, [SP], #0x10
    // 0x6b52ac: ret
    //     0x6b52ac: ret             
    // 0x6b52b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b52b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b52b4: b               #0x6b5098
  }
  get _ rect(/* No info */) {
    // ** addr: 0x6b52b8, size: 0x1b4
    // 0x6b52b8: EnterFrame
    //     0x6b52b8: stp             fp, lr, [SP, #-0x10]!
    //     0x6b52bc: mov             fp, SP
    // 0x6b52c0: AllocStack(0x28)
    //     0x6b52c0: sub             SP, SP, #0x28
    // 0x6b52c4: SetupParameters(_ReadingOrderDirectionalGroupData this /* r1 => r0, fp-0x10 */)
    //     0x6b52c4: mov             x0, x1
    //     0x6b52c8: stur            x1, [fp, #-0x10]
    // 0x6b52cc: CheckStackOverflow
    //     0x6b52cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b52d0: cmp             SP, x16
    //     0x6b52d4: b.ls            #0x6b5458
    // 0x6b52d8: LoadField: r1 = r0->field_b
    //     0x6b52d8: ldur            w1, [x0, #0xb]
    // 0x6b52dc: DecompressPointer r1
    //     0x6b52dc: add             x1, x1, HEAP, lsl #32
    // 0x6b52e0: cmp             w1, NULL
    // 0x6b52e4: b.ne            #0x6b5438
    // 0x6b52e8: LoadField: r3 = r0->field_7
    //     0x6b52e8: ldur            w3, [x0, #7]
    // 0x6b52ec: DecompressPointer r3
    //     0x6b52ec: add             x3, x3, HEAP, lsl #32
    // 0x6b52f0: stur            x3, [fp, #-8]
    // 0x6b52f4: r1 = Function '<anonymous closure>':.
    //     0x6b52f4: ldr             x1, [PP, #0x73d8]  ; [pp+0x73d8] Function: [dart:ui] Paint::_objects (0xbfdb8c)
    // 0x6b52f8: r2 = Null
    //     0x6b52f8: mov             x2, NULL
    // 0x6b52fc: r0 = AllocateClosure()
    //     0x6b52fc: bl              #0xec1630  ; AllocateClosureStub
    // 0x6b5300: mov             x1, x0
    // 0x6b5304: ldur            x0, [fp, #-8]
    // 0x6b5308: r2 = LoadClassIdInstr(r0)
    //     0x6b5308: ldur            x2, [x0, #-1]
    //     0x6b530c: ubfx            x2, x2, #0xc, #0x14
    // 0x6b5310: r16 = <Rect>
    //     0x6b5310: ldr             x16, [PP, #0x4598]  ; [pp+0x4598] TypeArguments: <Rect>
    // 0x6b5314: stp             x0, x16, [SP, #8]
    // 0x6b5318: str             x1, [SP]
    // 0x6b531c: mov             x0, x2
    // 0x6b5320: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6b5320: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6b5324: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x6b5324: movz            x17, #0xf28c
    //     0x6b5328: add             lr, x0, x17
    //     0x6b532c: ldr             lr, [x21, lr, lsl #3]
    //     0x6b5330: blr             lr
    // 0x6b5334: r1 = LoadClassIdInstr(r0)
    //     0x6b5334: ldur            x1, [x0, #-1]
    //     0x6b5338: ubfx            x1, x1, #0xc, #0x14
    // 0x6b533c: mov             x16, x0
    // 0x6b5340: mov             x0, x1
    // 0x6b5344: mov             x1, x16
    // 0x6b5348: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x6b5348: movz            x17, #0xd35d
    //     0x6b534c: add             lr, x0, x17
    //     0x6b5350: ldr             lr, [x21, lr, lsl #3]
    //     0x6b5354: blr             lr
    // 0x6b5358: mov             x2, x0
    // 0x6b535c: stur            x2, [fp, #-8]
    // 0x6b5360: ldur            x3, [fp, #-0x10]
    // 0x6b5364: CheckStackOverflow
    //     0x6b5364: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b5368: cmp             SP, x16
    //     0x6b536c: b.ls            #0x6b5460
    // 0x6b5370: r0 = LoadClassIdInstr(r2)
    //     0x6b5370: ldur            x0, [x2, #-1]
    //     0x6b5374: ubfx            x0, x0, #0xc, #0x14
    // 0x6b5378: mov             x1, x2
    // 0x6b537c: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x6b537c: movz            x17, #0x292d
    //     0x6b5380: movk            x17, #0x1, lsl #16
    //     0x6b5384: add             lr, x0, x17
    //     0x6b5388: ldr             lr, [x21, lr, lsl #3]
    //     0x6b538c: blr             lr
    // 0x6b5390: tbnz            w0, #4, #0x6b5430
    // 0x6b5394: ldur            x3, [fp, #-0x10]
    // 0x6b5398: ldur            x2, [fp, #-8]
    // 0x6b539c: r0 = LoadClassIdInstr(r2)
    //     0x6b539c: ldur            x0, [x2, #-1]
    //     0x6b53a0: ubfx            x0, x0, #0xc, #0x14
    // 0x6b53a4: mov             x1, x2
    // 0x6b53a8: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x6b53a8: movz            x17, #0x384d
    //     0x6b53ac: movk            x17, #0x1, lsl #16
    //     0x6b53b0: add             lr, x0, x17
    //     0x6b53b4: ldr             lr, [x21, lr, lsl #3]
    //     0x6b53b8: blr             lr
    // 0x6b53bc: mov             x1, x0
    // 0x6b53c0: ldur            x3, [fp, #-0x10]
    // 0x6b53c4: LoadField: r0 = r3->field_b
    //     0x6b53c4: ldur            w0, [x3, #0xb]
    // 0x6b53c8: DecompressPointer r0
    //     0x6b53c8: add             x0, x0, HEAP, lsl #32
    // 0x6b53cc: cmp             w0, NULL
    // 0x6b53d0: b.ne            #0x6b53f8
    // 0x6b53d4: mov             x0, x1
    // 0x6b53d8: StoreField: r3->field_b = r0
    //     0x6b53d8: stur            w0, [x3, #0xb]
    //     0x6b53dc: ldurb           w16, [x3, #-1]
    //     0x6b53e0: ldurb           w17, [x0, #-1]
    //     0x6b53e4: and             x16, x17, x16, lsr #2
    //     0x6b53e8: tst             x16, HEAP, lsr #32
    //     0x6b53ec: b.eq            #0x6b53f4
    //     0x6b53f0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x6b53f4: mov             x0, x1
    // 0x6b53f8: mov             x2, x1
    // 0x6b53fc: mov             x1, x0
    // 0x6b5400: r0 = expandToInclude()
    //     0x6b5400: bl              #0x686644  ; [dart:ui] Rect::expandToInclude
    // 0x6b5404: ldur            x1, [fp, #-0x10]
    // 0x6b5408: StoreField: r1->field_b = r0
    //     0x6b5408: stur            w0, [x1, #0xb]
    //     0x6b540c: ldurb           w16, [x1, #-1]
    //     0x6b5410: ldurb           w17, [x0, #-1]
    //     0x6b5414: and             x16, x17, x16, lsr #2
    //     0x6b5418: tst             x16, HEAP, lsr #32
    //     0x6b541c: b.eq            #0x6b5424
    //     0x6b5420: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6b5424: mov             x3, x1
    // 0x6b5428: ldur            x2, [fp, #-8]
    // 0x6b542c: b               #0x6b5364
    // 0x6b5430: ldur            x1, [fp, #-0x10]
    // 0x6b5434: b               #0x6b543c
    // 0x6b5438: mov             x1, x0
    // 0x6b543c: LoadField: r0 = r1->field_b
    //     0x6b543c: ldur            w0, [x1, #0xb]
    // 0x6b5440: DecompressPointer r0
    //     0x6b5440: add             x0, x0, HEAP, lsl #32
    // 0x6b5444: cmp             w0, NULL
    // 0x6b5448: b.eq            #0x6b5468
    // 0x6b544c: LeaveFrame
    //     0x6b544c: mov             SP, fp
    //     0x6b5450: ldp             fp, lr, [SP], #0x10
    // 0x6b5454: ret
    //     0x6b5454: ret             
    // 0x6b5458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b5458: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b545c: b               #0x6b52d8
    // 0x6b5460: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b5460: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b5464: b               #0x6b5370
    // 0x6b5468: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b5468: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3788, size: 0x18, field offset: 0x8
class _ReadingOrderSortData extends _DiagnosticableTree&Object&Diagnosticable {

  [closure] static int <anonymous closure>(dynamic, _ReadingOrderSortData, _ReadingOrderSortData) {
    // ** addr: 0x6b594c, size: 0x218
    // 0x6b594c: ldr             x1, [SP, #0x10]
    // 0x6b5950: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x6b5950: ldur            w2, [x1, #0x17]
    // 0x6b5954: DecompressPointer r2
    //     0x6b5954: add             x2, x2, HEAP, lsl #32
    // 0x6b5958: LoadField: r1 = r2->field_f
    //     0x6b5958: ldur            w1, [x2, #0xf]
    // 0x6b595c: DecompressPointer r1
    //     0x6b595c: add             x1, x1, HEAP, lsl #32
    // 0x6b5960: LoadField: r2 = r1->field_7
    //     0x6b5960: ldur            x2, [x1, #7]
    // 0x6b5964: cmp             x2, #0
    // 0x6b5968: b.gt            #0x6b5a68
    // 0x6b596c: ldr             x2, [SP, #8]
    // 0x6b5970: ldr             x1, [SP]
    // 0x6b5974: LoadField: r3 = r1->field_b
    //     0x6b5974: ldur            w3, [x1, #0xb]
    // 0x6b5978: DecompressPointer r3
    //     0x6b5978: add             x3, x3, HEAP, lsl #32
    // 0x6b597c: ArrayLoad: d0 = r3[0]  ; List_8
    //     0x6b597c: ldur            d0, [x3, #0x17]
    // 0x6b5980: LoadField: r3 = r2->field_b
    //     0x6b5980: ldur            w3, [x2, #0xb]
    // 0x6b5984: DecompressPointer r3
    //     0x6b5984: add             x3, x3, HEAP, lsl #32
    // 0x6b5988: ArrayLoad: d1 = r3[0]  ; List_8
    //     0x6b5988: ldur            d1, [x3, #0x17]
    // 0x6b598c: fcmp            d1, d0
    // 0x6b5990: b.le            #0x6b599c
    // 0x6b5994: r3 = -1
    //     0x6b5994: movn            x3, #0
    // 0x6b5998: b               #0x6b5a60
    // 0x6b599c: fcmp            d0, d1
    // 0x6b59a0: b.le            #0x6b59ac
    // 0x6b59a4: r3 = 1
    //     0x6b59a4: movz            x3, #0x1
    // 0x6b59a8: b               #0x6b5a60
    // 0x6b59ac: fcmp            d0, d1
    // 0x6b59b0: b.ne            #0x6b5a3c
    // 0x6b59b4: d2 = 0.000000
    //     0x6b59b4: eor             v2.16b, v2.16b, v2.16b
    // 0x6b59b8: fcmp            d0, d2
    // 0x6b59bc: b.ne            #0x6b5a34
    // 0x6b59c0: fcmp            d0, #0.0
    // 0x6b59c4: b.vs            #0x6b59d8
    // 0x6b59c8: b.ne            #0x6b59d4
    // 0x6b59cc: r4 = 0.000000
    //     0x6b59cc: fmov            x4, d0
    // 0x6b59d0: cmp             x4, #0
    // 0x6b59d4: b.lt            #0x6b59e0
    // 0x6b59d8: r3 = false
    //     0x6b59d8: add             x3, NULL, #0x30  ; false
    // 0x6b59dc: b               #0x6b59e4
    // 0x6b59e0: r3 = true
    //     0x6b59e0: add             x3, NULL, #0x20  ; true
    // 0x6b59e4: fcmp            d1, #0.0
    // 0x6b59e8: b.vs            #0x6b59fc
    // 0x6b59ec: b.ne            #0x6b59f8
    // 0x6b59f0: r5 = 0.000000
    //     0x6b59f0: fmov            x5, d1
    // 0x6b59f4: cmp             x5, #0
    // 0x6b59f8: b.lt            #0x6b5a04
    // 0x6b59fc: r4 = false
    //     0x6b59fc: add             x4, NULL, #0x30  ; false
    // 0x6b5a00: b               #0x6b5a08
    // 0x6b5a04: r4 = true
    //     0x6b5a04: add             x4, NULL, #0x20  ; true
    // 0x6b5a08: cmp             w3, w4
    // 0x6b5a0c: b.ne            #0x6b5a18
    // 0x6b5a10: r3 = 0
    //     0x6b5a10: movz            x3, #0
    // 0x6b5a14: b               #0x6b5a60
    // 0x6b5a18: tst             x3, #0x10
    // 0x6b5a1c: cset            x4, ne
    // 0x6b5a20: sub             x4, x4, #1
    // 0x6b5a24: and             x4, x4, #0xfffffffffffffffc
    // 0x6b5a28: add             x4, x4, #2
    // 0x6b5a2c: r3 = LoadInt32Instr(r4)
    //     0x6b5a2c: sbfx            x3, x4, #1, #0x1f
    // 0x6b5a30: b               #0x6b5a60
    // 0x6b5a34: r3 = 0
    //     0x6b5a34: movz            x3, #0
    // 0x6b5a38: b               #0x6b5a60
    // 0x6b5a3c: fcmp            d0, d0
    // 0x6b5a40: b.vc            #0x6b5a5c
    // 0x6b5a44: fcmp            d1, d1
    // 0x6b5a48: b.vc            #0x6b5a54
    // 0x6b5a4c: r3 = 0
    //     0x6b5a4c: movz            x3, #0
    // 0x6b5a50: b               #0x6b5a60
    // 0x6b5a54: r3 = 1
    //     0x6b5a54: movz            x3, #0x1
    // 0x6b5a58: b               #0x6b5a60
    // 0x6b5a5c: r3 = -1
    //     0x6b5a5c: movn            x3, #0
    // 0x6b5a60: mov             x1, x3
    // 0x6b5a64: b               #0x6b5b5c
    // 0x6b5a68: ldr             x2, [SP, #8]
    // 0x6b5a6c: ldr             x1, [SP]
    // 0x6b5a70: d2 = 0.000000
    //     0x6b5a70: eor             v2.16b, v2.16b, v2.16b
    // 0x6b5a74: LoadField: r3 = r2->field_b
    //     0x6b5a74: ldur            w3, [x2, #0xb]
    // 0x6b5a78: DecompressPointer r3
    //     0x6b5a78: add             x3, x3, HEAP, lsl #32
    // 0x6b5a7c: LoadField: d0 = r3->field_7
    //     0x6b5a7c: ldur            d0, [x3, #7]
    // 0x6b5a80: LoadField: r2 = r1->field_b
    //     0x6b5a80: ldur            w2, [x1, #0xb]
    // 0x6b5a84: DecompressPointer r2
    //     0x6b5a84: add             x2, x2, HEAP, lsl #32
    // 0x6b5a88: LoadField: d1 = r2->field_7
    //     0x6b5a88: ldur            d1, [x2, #7]
    // 0x6b5a8c: fcmp            d1, d0
    // 0x6b5a90: b.le            #0x6b5a9c
    // 0x6b5a94: r1 = -1
    //     0x6b5a94: movn            x1, #0
    // 0x6b5a98: b               #0x6b5b5c
    // 0x6b5a9c: fcmp            d0, d1
    // 0x6b5aa0: b.le            #0x6b5aac
    // 0x6b5aa4: r1 = 1
    //     0x6b5aa4: movz            x1, #0x1
    // 0x6b5aa8: b               #0x6b5b5c
    // 0x6b5aac: fcmp            d0, d1
    // 0x6b5ab0: b.ne            #0x6b5b38
    // 0x6b5ab4: fcmp            d0, d2
    // 0x6b5ab8: b.ne            #0x6b5b30
    // 0x6b5abc: fcmp            d0, #0.0
    // 0x6b5ac0: b.vs            #0x6b5ad4
    // 0x6b5ac4: b.ne            #0x6b5ad0
    // 0x6b5ac8: r2 = 0.000000
    //     0x6b5ac8: fmov            x2, d0
    // 0x6b5acc: cmp             x2, #0
    // 0x6b5ad0: b.lt            #0x6b5adc
    // 0x6b5ad4: r1 = false
    //     0x6b5ad4: add             x1, NULL, #0x30  ; false
    // 0x6b5ad8: b               #0x6b5ae0
    // 0x6b5adc: r1 = true
    //     0x6b5adc: add             x1, NULL, #0x20  ; true
    // 0x6b5ae0: fcmp            d1, #0.0
    // 0x6b5ae4: b.vs            #0x6b5af8
    // 0x6b5ae8: b.ne            #0x6b5af4
    // 0x6b5aec: r3 = 0.000000
    //     0x6b5aec: fmov            x3, d1
    // 0x6b5af0: cmp             x3, #0
    // 0x6b5af4: b.lt            #0x6b5b00
    // 0x6b5af8: r2 = false
    //     0x6b5af8: add             x2, NULL, #0x30  ; false
    // 0x6b5afc: b               #0x6b5b04
    // 0x6b5b00: r2 = true
    //     0x6b5b00: add             x2, NULL, #0x20  ; true
    // 0x6b5b04: cmp             w1, w2
    // 0x6b5b08: b.ne            #0x6b5b14
    // 0x6b5b0c: r1 = 0
    //     0x6b5b0c: movz            x1, #0
    // 0x6b5b10: b               #0x6b5b5c
    // 0x6b5b14: tst             x1, #0x10
    // 0x6b5b18: cset            x2, ne
    // 0x6b5b1c: sub             x2, x2, #1
    // 0x6b5b20: and             x2, x2, #0xfffffffffffffffc
    // 0x6b5b24: add             x2, x2, #2
    // 0x6b5b28: r1 = LoadInt32Instr(r2)
    //     0x6b5b28: sbfx            x1, x2, #1, #0x1f
    // 0x6b5b2c: b               #0x6b5b5c
    // 0x6b5b30: r1 = 0
    //     0x6b5b30: movz            x1, #0
    // 0x6b5b34: b               #0x6b5b5c
    // 0x6b5b38: fcmp            d0, d0
    // 0x6b5b3c: b.vc            #0x6b5b58
    // 0x6b5b40: fcmp            d1, d1
    // 0x6b5b44: b.vc            #0x6b5b50
    // 0x6b5b48: r1 = 0
    //     0x6b5b48: movz            x1, #0
    // 0x6b5b4c: b               #0x6b5b5c
    // 0x6b5b50: r1 = 1
    //     0x6b5b50: movz            x1, #0x1
    // 0x6b5b54: b               #0x6b5b5c
    // 0x6b5b58: r1 = -1
    //     0x6b5b58: movn            x1, #0
    // 0x6b5b5c: lsl             x0, x1, #1
    // 0x6b5b60: ret
    //     0x6b5b60: ret             
  }
  static _ sortWithDirectionality(/* No info */) {
    // ** addr: 0x6b5b64, size: 0x70
    // 0x6b5b64: EnterFrame
    //     0x6b5b64: stp             fp, lr, [SP, #-0x10]!
    //     0x6b5b68: mov             fp, SP
    // 0x6b5b6c: AllocStack(0x28)
    //     0x6b5b6c: sub             SP, SP, #0x28
    // 0x6b5b70: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6b5b70: stur            x1, [fp, #-8]
    //     0x6b5b74: stur            x2, [fp, #-0x10]
    // 0x6b5b78: CheckStackOverflow
    //     0x6b5b78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b5b7c: cmp             SP, x16
    //     0x6b5b80: b.ls            #0x6b5bcc
    // 0x6b5b84: r1 = 1
    //     0x6b5b84: movz            x1, #0x1
    // 0x6b5b88: r0 = AllocateContext()
    //     0x6b5b88: bl              #0xec126c  ; AllocateContextStub
    // 0x6b5b8c: mov             x1, x0
    // 0x6b5b90: ldur            x0, [fp, #-0x10]
    // 0x6b5b94: StoreField: r1->field_f = r0
    //     0x6b5b94: stur            w0, [x1, #0xf]
    // 0x6b5b98: mov             x2, x1
    // 0x6b5b9c: r1 = Function '<anonymous closure>': static.
    //     0x6b5b9c: ldr             x1, [PP, #0x7400]  ; [pp+0x7400] AnonymousClosure: static (0x6b594c), in [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderSortData::sortWithDirectionality (0x6b5b64)
    // 0x6b5ba0: r0 = AllocateClosure()
    //     0x6b5ba0: bl              #0xec1630  ; AllocateClosureStub
    // 0x6b5ba4: r16 = <_ReadingOrderSortData>
    //     0x6b5ba4: ldr             x16, [PP, #0x73a0]  ; [pp+0x73a0] TypeArguments: <_ReadingOrderSortData>
    // 0x6b5ba8: ldur            lr, [fp, #-8]
    // 0x6b5bac: stp             lr, x16, [SP, #8]
    // 0x6b5bb0: str             x0, [SP]
    // 0x6b5bb4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6b5bb4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6b5bb8: r0 = mergeSort()
    //     0x6b5bb8: bl              #0x6b61b4  ; [package:flutter/src/foundation/collections.dart] ::mergeSort
    // 0x6b5bbc: r0 = Null
    //     0x6b5bbc: mov             x0, NULL
    // 0x6b5bc0: LeaveFrame
    //     0x6b5bc0: mov             SP, fp
    //     0x6b5bc4: ldp             fp, lr, [SP], #0x10
    // 0x6b5bc8: ret
    //     0x6b5bc8: ret             
    // 0x6b5bcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b5bcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b5bd0: b               #0x6b5b84
  }
  static _ commonDirectionalityOf(/* No info */) {
    // ** addr: 0x6b5bd4, size: 0x294
    // 0x6b5bd4: EnterFrame
    //     0x6b5bd4: stp             fp, lr, [SP, #-0x10]!
    //     0x6b5bd8: mov             fp, SP
    // 0x6b5bdc: AllocStack(0x50)
    //     0x6b5bdc: sub             SP, SP, #0x50
    // 0x6b5be0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x6b5be0: mov             x0, x1
    //     0x6b5be4: stur            x1, [fp, #-8]
    // 0x6b5be8: CheckStackOverflow
    //     0x6b5be8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b5bec: cmp             SP, x16
    //     0x6b5bf0: b.ls            #0x6b5e54
    // 0x6b5bf4: r1 = Function '<anonymous closure>': static.
    //     0x6b5bf4: ldr             x1, [PP, #0x7408]  ; [pp+0x7408] AnonymousClosure: static (0x6b617c), in [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderSortData::commonDirectionalityOf (0x6b5bd4)
    // 0x6b5bf8: r2 = Null
    //     0x6b5bf8: mov             x2, NULL
    // 0x6b5bfc: r0 = AllocateClosure()
    //     0x6b5bfc: bl              #0xec1630  ; AllocateClosureStub
    // 0x6b5c00: r16 = <Set<Directionality>>
    //     0x6b5c00: ldr             x16, [PP, #0x7410]  ; [pp+0x7410] TypeArguments: <Set<Directionality>>
    // 0x6b5c04: ldur            lr, [fp, #-8]
    // 0x6b5c08: stp             lr, x16, [SP, #8]
    // 0x6b5c0c: str             x0, [SP]
    // 0x6b5c10: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6b5c10: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6b5c14: r0 = map()
    //     0x6b5c14: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x6b5c18: mov             x1, x0
    // 0x6b5c1c: r0 = iterator()
    //     0x6b5c1c: bl              #0x8873a8  ; [dart:_internal] ListIterable::iterator
    // 0x6b5c20: mov             x1, x0
    // 0x6b5c24: stur            x1, [fp, #-0x30]
    // 0x6b5c28: LoadField: r2 = r1->field_b
    //     0x6b5c28: ldur            w2, [x1, #0xb]
    // 0x6b5c2c: DecompressPointer r2
    //     0x6b5c2c: add             x2, x2, HEAP, lsl #32
    // 0x6b5c30: stur            x2, [fp, #-0x28]
    // 0x6b5c34: LoadField: r3 = r1->field_f
    //     0x6b5c34: ldur            x3, [x1, #0xf]
    // 0x6b5c38: stur            x3, [fp, #-0x20]
    // 0x6b5c3c: LoadField: r4 = r1->field_7
    //     0x6b5c3c: ldur            w4, [x1, #7]
    // 0x6b5c40: DecompressPointer r4
    //     0x6b5c40: add             x4, x4, HEAP, lsl #32
    // 0x6b5c44: stur            x4, [fp, #-0x18]
    // 0x6b5c48: r5 = Null
    //     0x6b5c48: mov             x5, NULL
    // 0x6b5c4c: stur            x5, [fp, #-0x10]
    // 0x6b5c50: CheckStackOverflow
    //     0x6b5c50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b5c54: cmp             SP, x16
    //     0x6b5c58: b.ls            #0x6b5e5c
    // 0x6b5c5c: r0 = LoadClassIdInstr(r2)
    //     0x6b5c5c: ldur            x0, [x2, #-1]
    //     0x6b5c60: ubfx            x0, x0, #0xc, #0x14
    // 0x6b5c64: str             x2, [SP]
    // 0x6b5c68: r0 = GDT[cid_x0 + 0xc834]()
    //     0x6b5c68: movz            x17, #0xc834
    //     0x6b5c6c: add             lr, x0, x17
    //     0x6b5c70: ldr             lr, [x21, lr, lsl #3]
    //     0x6b5c74: blr             lr
    // 0x6b5c78: r1 = LoadInt32Instr(r0)
    //     0x6b5c78: sbfx            x1, x0, #1, #0x1f
    //     0x6b5c7c: tbz             w0, #0, #0x6b5c84
    //     0x6b5c80: ldur            x1, [x0, #7]
    // 0x6b5c84: ldur            x3, [fp, #-0x20]
    // 0x6b5c88: cmp             x3, x1
    // 0x6b5c8c: b.ne            #0x6b5e34
    // 0x6b5c90: ldur            x4, [fp, #-0x30]
    // 0x6b5c94: ArrayLoad: r2 = r4[0]  ; List_8
    //     0x6b5c94: ldur            x2, [x4, #0x17]
    // 0x6b5c98: cmp             x2, x1
    // 0x6b5c9c: b.ge            #0x6b5d78
    // 0x6b5ca0: ldur            x5, [fp, #-0x28]
    // 0x6b5ca4: r0 = LoadClassIdInstr(r5)
    //     0x6b5ca4: ldur            x0, [x5, #-1]
    //     0x6b5ca8: ubfx            x0, x0, #0xc, #0x14
    // 0x6b5cac: mov             x1, x5
    // 0x6b5cb0: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x6b5cb0: movz            x17, #0xd28f
    //     0x6b5cb4: add             lr, x0, x17
    //     0x6b5cb8: ldr             lr, [x21, lr, lsl #3]
    //     0x6b5cbc: blr             lr
    // 0x6b5cc0: mov             x4, x0
    // 0x6b5cc4: ldur            x3, [fp, #-0x30]
    // 0x6b5cc8: stur            x4, [fp, #-0x38]
    // 0x6b5ccc: StoreField: r3->field_1f = r0
    //     0x6b5ccc: stur            w0, [x3, #0x1f]
    //     0x6b5cd0: tbz             w0, #0, #0x6b5cec
    //     0x6b5cd4: ldurb           w16, [x3, #-1]
    //     0x6b5cd8: ldurb           w17, [x0, #-1]
    //     0x6b5cdc: and             x16, x17, x16, lsr #2
    //     0x6b5ce0: tst             x16, HEAP, lsr #32
    //     0x6b5ce4: b.eq            #0x6b5cec
    //     0x6b5ce8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x6b5cec: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x6b5cec: ldur            x0, [x3, #0x17]
    // 0x6b5cf0: add             x1, x0, #1
    // 0x6b5cf4: ArrayStore: r3[0] = r1  ; List_8
    //     0x6b5cf4: stur            x1, [x3, #0x17]
    // 0x6b5cf8: cmp             w4, NULL
    // 0x6b5cfc: b.ne            #0x6b5d2c
    // 0x6b5d00: mov             x0, x4
    // 0x6b5d04: ldur            x2, [fp, #-0x18]
    // 0x6b5d08: r1 = Null
    //     0x6b5d08: mov             x1, NULL
    // 0x6b5d0c: cmp             w2, NULL
    // 0x6b5d10: b.eq            #0x6b5d2c
    // 0x6b5d14: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6b5d14: ldur            w4, [x2, #0x17]
    // 0x6b5d18: DecompressPointer r4
    //     0x6b5d18: add             x4, x4, HEAP, lsl #32
    // 0x6b5d1c: r8 = X0
    //     0x6b5d1c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6b5d20: LoadField: r9 = r4->field_7
    //     0x6b5d20: ldur            x9, [x4, #7]
    // 0x6b5d24: r3 = Null
    //     0x6b5d24: ldr             x3, [PP, #0x7418]  ; [pp+0x7418] Null
    // 0x6b5d28: blr             x9
    // 0x6b5d2c: ldur            x2, [fp, #-0x10]
    // 0x6b5d30: cmp             w2, NULL
    // 0x6b5d34: b.ne            #0x6b5d40
    // 0x6b5d38: ldur            x1, [fp, #-0x38]
    // 0x6b5d3c: b               #0x6b5d44
    // 0x6b5d40: mov             x1, x2
    // 0x6b5d44: r0 = LoadClassIdInstr(r1)
    //     0x6b5d44: ldur            x0, [x1, #-1]
    //     0x6b5d48: ubfx            x0, x0, #0xc, #0x14
    // 0x6b5d4c: ldur            x2, [fp, #-0x38]
    // 0x6b5d50: r0 = GDT[cid_x0 + 0x2f68]()
    //     0x6b5d50: movz            x17, #0x2f68
    //     0x6b5d54: add             lr, x0, x17
    //     0x6b5d58: ldr             lr, [x21, lr, lsl #3]
    //     0x6b5d5c: blr             lr
    // 0x6b5d60: mov             x5, x0
    // 0x6b5d64: ldur            x1, [fp, #-0x30]
    // 0x6b5d68: ldur            x4, [fp, #-0x18]
    // 0x6b5d6c: ldur            x2, [fp, #-0x28]
    // 0x6b5d70: ldur            x3, [fp, #-0x20]
    // 0x6b5d74: b               #0x6b5c4c
    // 0x6b5d78: mov             x0, x4
    // 0x6b5d7c: ldur            x2, [fp, #-0x10]
    // 0x6b5d80: StoreField: r0->field_1f = rNULL
    //     0x6b5d80: stur            NULL, [x0, #0x1f]
    // 0x6b5d84: cmp             w2, NULL
    // 0x6b5d88: b.eq            #0x6b5e64
    // 0x6b5d8c: r0 = LoadClassIdInstr(r2)
    //     0x6b5d8c: ldur            x0, [x2, #-1]
    //     0x6b5d90: ubfx            x0, x0, #0xc, #0x14
    // 0x6b5d94: mov             x1, x2
    // 0x6b5d98: r0 = GDT[cid_x0 + 0xe879]()
    //     0x6b5d98: movz            x17, #0xe879
    //     0x6b5d9c: add             lr, x0, x17
    //     0x6b5da0: ldr             lr, [x21, lr, lsl #3]
    //     0x6b5da4: blr             lr
    // 0x6b5da8: tbnz            w0, #4, #0x6b5dcc
    // 0x6b5dac: ldur            x1, [fp, #-8]
    // 0x6b5db0: r0 = first()
    //     0x6b5db0: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6b5db4: LoadField: r1 = r0->field_7
    //     0x6b5db4: ldur            w1, [x0, #7]
    // 0x6b5db8: DecompressPointer r1
    //     0x6b5db8: add             x1, x1, HEAP, lsl #32
    // 0x6b5dbc: mov             x0, x1
    // 0x6b5dc0: LeaveFrame
    //     0x6b5dc0: mov             SP, fp
    //     0x6b5dc4: ldp             fp, lr, [SP], #0x10
    // 0x6b5dc8: ret
    //     0x6b5dc8: ret             
    // 0x6b5dcc: ldur            x0, [fp, #-0x10]
    // 0x6b5dd0: ldur            x1, [fp, #-8]
    // 0x6b5dd4: r0 = first()
    //     0x6b5dd4: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6b5dd8: mov             x1, x0
    // 0x6b5ddc: r0 = directionalAncestors()
    //     0x6b5ddc: bl              #0x6b5e68  ; [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderSortData::directionalAncestors
    // 0x6b5de0: mov             x1, x0
    // 0x6b5de4: ldur            x0, [fp, #-0x10]
    // 0x6b5de8: stur            x1, [fp, #-8]
    // 0x6b5dec: r2 = LoadClassIdInstr(r0)
    //     0x6b5dec: ldur            x2, [x0, #-1]
    //     0x6b5df0: ubfx            x2, x2, #0xc, #0x14
    // 0x6b5df4: str             x0, [SP]
    // 0x6b5df8: mov             x0, x2
    // 0x6b5dfc: r0 = GDT[cid_x0 + 0xf0bf]()
    //     0x6b5dfc: movz            x17, #0xf0bf
    //     0x6b5e00: add             lr, x0, x17
    //     0x6b5e04: ldr             lr, [x21, lr, lsl #3]
    //     0x6b5e08: blr             lr
    // 0x6b5e0c: ldur            x1, [fp, #-8]
    // 0x6b5e10: mov             x2, x0
    // 0x6b5e14: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6b5e14: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6b5e18: r0 = firstWhere()
    //     0x6b5e18: bl              #0x89fe00  ; [dart:collection] ListBase::firstWhere
    // 0x6b5e1c: LoadField: r1 = r0->field_f
    //     0x6b5e1c: ldur            w1, [x0, #0xf]
    // 0x6b5e20: DecompressPointer r1
    //     0x6b5e20: add             x1, x1, HEAP, lsl #32
    // 0x6b5e24: mov             x0, x1
    // 0x6b5e28: LeaveFrame
    //     0x6b5e28: mov             SP, fp
    //     0x6b5e2c: ldp             fp, lr, [SP], #0x10
    // 0x6b5e30: ret
    //     0x6b5e30: ret             
    // 0x6b5e34: ldur            x0, [fp, #-0x28]
    // 0x6b5e38: r0 = ConcurrentModificationError()
    //     0x6b5e38: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6b5e3c: mov             x1, x0
    // 0x6b5e40: ldur            x0, [fp, #-0x28]
    // 0x6b5e44: StoreField: r1->field_b = r0
    //     0x6b5e44: stur            w0, [x1, #0xb]
    // 0x6b5e48: mov             x0, x1
    // 0x6b5e4c: r0 = Throw()
    //     0x6b5e4c: bl              #0xec04b8  ; ThrowStub
    // 0x6b5e50: brk             #0
    // 0x6b5e54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b5e54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b5e58: b               #0x6b5bf4
    // 0x6b5e5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b5e5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b5e60: b               #0x6b5c5c
    // 0x6b5e64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b5e64: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ directionalAncestors(/* No info */) {
    // ** addr: 0x6b5e68, size: 0xbc
    // 0x6b5e68: EnterFrame
    //     0x6b5e68: stp             fp, lr, [SP, #-0x10]!
    //     0x6b5e6c: mov             fp, SP
    // 0x6b5e70: AllocStack(0x18)
    //     0x6b5e70: sub             SP, SP, #0x18
    // 0x6b5e74: SetupParameters(_ReadingOrderSortData this /* r1 => r0, fp-0x8 */)
    //     0x6b5e74: mov             x0, x1
    //     0x6b5e78: stur            x1, [fp, #-8]
    // 0x6b5e7c: CheckStackOverflow
    //     0x6b5e7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b5e80: cmp             SP, x16
    //     0x6b5e84: b.ls            #0x6b5f14
    // 0x6b5e88: r1 = Function 'getDirectionalityAncestors':.
    //     0x6b5e88: ldr             x1, [PP, #0x7448]  ; [pp+0x7448] AnonymousClosure: (0x6b5f24), in [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderSortData::directionalAncestors (0x6b5e68)
    // 0x6b5e8c: r2 = Null
    //     0x6b5e8c: mov             x2, NULL
    // 0x6b5e90: r0 = AllocateClosure()
    //     0x6b5e90: bl              #0xec1630  ; AllocateClosureStub
    // 0x6b5e94: ldur            x1, [fp, #-8]
    // 0x6b5e98: LoadField: r2 = r1->field_13
    //     0x6b5e98: ldur            w2, [x1, #0x13]
    // 0x6b5e9c: DecompressPointer r2
    //     0x6b5e9c: add             x2, x2, HEAP, lsl #32
    // 0x6b5ea0: cmp             w2, NULL
    // 0x6b5ea4: b.ne            #0x6b5efc
    // 0x6b5ea8: LoadField: r2 = r1->field_f
    //     0x6b5ea8: ldur            w2, [x1, #0xf]
    // 0x6b5eac: DecompressPointer r2
    //     0x6b5eac: add             x2, x2, HEAP, lsl #32
    // 0x6b5eb0: LoadField: r3 = r2->field_33
    //     0x6b5eb0: ldur            w3, [x2, #0x33]
    // 0x6b5eb4: DecompressPointer r3
    //     0x6b5eb4: add             x3, x3, HEAP, lsl #32
    // 0x6b5eb8: cmp             w3, NULL
    // 0x6b5ebc: b.eq            #0x6b5f1c
    // 0x6b5ec0: stp             x3, x0, [SP]
    // 0x6b5ec4: ClosureCall
    //     0x6b5ec4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x6b5ec8: ldur            x2, [x0, #0x1f]
    //     0x6b5ecc: blr             x2
    // 0x6b5ed0: mov             x2, x0
    // 0x6b5ed4: ldur            x1, [fp, #-8]
    // 0x6b5ed8: StoreField: r1->field_13 = r0
    //     0x6b5ed8: stur            w0, [x1, #0x13]
    //     0x6b5edc: ldurb           w16, [x1, #-1]
    //     0x6b5ee0: ldurb           w17, [x0, #-1]
    //     0x6b5ee4: and             x16, x17, x16, lsr #2
    //     0x6b5ee8: tst             x16, HEAP, lsr #32
    //     0x6b5eec: b.eq            #0x6b5ef4
    //     0x6b5ef0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6b5ef4: mov             x0, x2
    // 0x6b5ef8: b               #0x6b5f00
    // 0x6b5efc: mov             x0, x2
    // 0x6b5f00: cmp             w0, NULL
    // 0x6b5f04: b.eq            #0x6b5f20
    // 0x6b5f08: LeaveFrame
    //     0x6b5f08: mov             SP, fp
    //     0x6b5f0c: ldp             fp, lr, [SP], #0x10
    // 0x6b5f10: ret
    //     0x6b5f10: ret             
    // 0x6b5f14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b5f14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b5f18: b               #0x6b5e88
    // 0x6b5f1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b5f1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b5f20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b5f20: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] List<Directionality> getDirectionalityAncestors(dynamic, BuildContext) {
    // ** addr: 0x6b5f24, size: 0x1b4
    // 0x6b5f24: EnterFrame
    //     0x6b5f24: stp             fp, lr, [SP, #-0x10]!
    //     0x6b5f28: mov             fp, SP
    // 0x6b5f2c: AllocStack(0x30)
    //     0x6b5f2c: sub             SP, SP, #0x30
    // 0x6b5f30: CheckStackOverflow
    //     0x6b5f30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b5f34: cmp             SP, x16
    //     0x6b5f38: b.ls            #0x6b60c4
    // 0x6b5f3c: r1 = <Directionality>
    //     0x6b5f3c: ldr             x1, [PP, #0x7160]  ; [pp+0x7160] TypeArguments: <Directionality>
    // 0x6b5f40: r2 = 0
    //     0x6b5f40: movz            x2, #0
    // 0x6b5f44: r0 = _GrowableList()
    //     0x6b5f44: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b5f48: stur            x0, [fp, #-8]
    // 0x6b5f4c: r16 = <Directionality>
    //     0x6b5f4c: ldr             x16, [PP, #0x7160]  ; [pp+0x7160] TypeArguments: <Directionality>
    // 0x6b5f50: ldr             lr, [fp, #0x10]
    // 0x6b5f54: stp             lr, x16, [SP]
    // 0x6b5f58: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x6b5f58: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x6b5f5c: r0 = getElementForInheritedWidgetOfExactType()
    //     0x6b5f5c: bl              #0x651a00  ; [package:flutter/src/widgets/framework.dart] Element::getElementForInheritedWidgetOfExactType
    // 0x6b5f60: mov             x4, x0
    // 0x6b5f64: ldur            x3, [fp, #-8]
    // 0x6b5f68: stur            x4, [fp, #-0x18]
    // 0x6b5f6c: CheckStackOverflow
    //     0x6b5f6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b5f70: cmp             SP, x16
    //     0x6b5f74: b.ls            #0x6b60cc
    // 0x6b5f78: cmp             w4, NULL
    // 0x6b5f7c: b.eq            #0x6b60b4
    // 0x6b5f80: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x6b5f80: ldur            w5, [x4, #0x17]
    // 0x6b5f84: DecompressPointer r5
    //     0x6b5f84: add             x5, x5, HEAP, lsl #32
    // 0x6b5f88: stur            x5, [fp, #-0x10]
    // 0x6b5f8c: cmp             w5, NULL
    // 0x6b5f90: b.eq            #0x6b60d4
    // 0x6b5f94: mov             x0, x5
    // 0x6b5f98: r2 = Null
    //     0x6b5f98: mov             x2, NULL
    // 0x6b5f9c: r1 = Null
    //     0x6b5f9c: mov             x1, NULL
    // 0x6b5fa0: r4 = LoadClassIdInstr(r0)
    //     0x6b5fa0: ldur            x4, [x0, #-1]
    //     0x6b5fa4: ubfx            x4, x4, #0xc, #0x14
    // 0x6b5fa8: r17 = 4638
    //     0x6b5fa8: movz            x17, #0x121e
    // 0x6b5fac: cmp             x4, x17
    // 0x6b5fb0: b.eq            #0x6b5fc0
    // 0x6b5fb4: r8 = Directionality
    //     0x6b5fb4: ldr             x8, [PP, #0x7450]  ; [pp+0x7450] Type: Directionality
    // 0x6b5fb8: r3 = Null
    //     0x6b5fb8: ldr             x3, [PP, #0x7458]  ; [pp+0x7458] Null
    // 0x6b5fbc: r0 = Directionality()
    //     0x6b5fbc: bl              #0x6b0b3c  ; IsType_Directionality_Stub
    // 0x6b5fc0: ldur            x0, [fp, #-8]
    // 0x6b5fc4: LoadField: r1 = r0->field_b
    //     0x6b5fc4: ldur            w1, [x0, #0xb]
    // 0x6b5fc8: LoadField: r2 = r0->field_f
    //     0x6b5fc8: ldur            w2, [x0, #0xf]
    // 0x6b5fcc: DecompressPointer r2
    //     0x6b5fcc: add             x2, x2, HEAP, lsl #32
    // 0x6b5fd0: LoadField: r3 = r2->field_b
    //     0x6b5fd0: ldur            w3, [x2, #0xb]
    // 0x6b5fd4: r2 = LoadInt32Instr(r1)
    //     0x6b5fd4: sbfx            x2, x1, #1, #0x1f
    // 0x6b5fd8: stur            x2, [fp, #-0x20]
    // 0x6b5fdc: r1 = LoadInt32Instr(r3)
    //     0x6b5fdc: sbfx            x1, x3, #1, #0x1f
    // 0x6b5fe0: cmp             x2, x1
    // 0x6b5fe4: b.ne            #0x6b5ff0
    // 0x6b5fe8: mov             x1, x0
    // 0x6b5fec: r0 = _growToNextCapacity()
    //     0x6b5fec: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b5ff0: ldur            x2, [fp, #-8]
    // 0x6b5ff4: ldur            x3, [fp, #-0x20]
    // 0x6b5ff8: add             x0, x3, #1
    // 0x6b5ffc: lsl             x1, x0, #1
    // 0x6b6000: StoreField: r2->field_b = r1
    //     0x6b6000: stur            w1, [x2, #0xb]
    // 0x6b6004: LoadField: r1 = r2->field_f
    //     0x6b6004: ldur            w1, [x2, #0xf]
    // 0x6b6008: DecompressPointer r1
    //     0x6b6008: add             x1, x1, HEAP, lsl #32
    // 0x6b600c: ldur            x0, [fp, #-0x10]
    // 0x6b6010: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6b6010: add             x25, x1, x3, lsl #2
    //     0x6b6014: add             x25, x25, #0xf
    //     0x6b6018: str             w0, [x25]
    //     0x6b601c: tbz             w0, #0, #0x6b6038
    //     0x6b6020: ldurb           w16, [x1, #-1]
    //     0x6b6024: ldurb           w17, [x0, #-1]
    //     0x6b6028: and             x16, x17, x16, lsr #2
    //     0x6b602c: tst             x16, HEAP, lsr #32
    //     0x6b6030: b.eq            #0x6b6038
    //     0x6b6034: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6b6038: r1 = 2
    //     0x6b6038: movz            x1, #0x2
    // 0x6b603c: r0 = AllocateContext()
    //     0x6b603c: bl              #0xec126c  ; AllocateContextStub
    // 0x6b6040: mov             x3, x0
    // 0x6b6044: r0 = 2
    //     0x6b6044: movz            x0, #0x2
    // 0x6b6048: stur            x3, [fp, #-0x10]
    // 0x6b604c: StoreField: r3->field_f = r0
    //     0x6b604c: stur            w0, [x3, #0xf]
    // 0x6b6050: mov             x2, x3
    // 0x6b6054: r1 = Function '<anonymous closure>': static.
    //     0x6b6054: ldr             x1, [PP, #0x7468]  ; [pp+0x7468] AnonymousClosure: static (0x6b60d8), of [package:flutter/src/widgets/focus_traversal.dart] 
    // 0x6b6058: r0 = AllocateClosure()
    //     0x6b6058: bl              #0xec1630  ; AllocateClosureStub
    // 0x6b605c: ldur            x1, [fp, #-0x18]
    // 0x6b6060: mov             x2, x0
    // 0x6b6064: r0 = visitAncestorElements()
    //     0x6b6064: bl              #0x679d74  ; [package:flutter/src/widgets/framework.dart] Element::visitAncestorElements
    // 0x6b6068: ldur            x0, [fp, #-0x10]
    // 0x6b606c: LoadField: r1 = r0->field_13
    //     0x6b606c: ldur            w1, [x0, #0x13]
    // 0x6b6070: DecompressPointer r1
    //     0x6b6070: add             x1, x1, HEAP, lsl #32
    // 0x6b6074: cmp             w1, NULL
    // 0x6b6078: b.ne            #0x6b6084
    // 0x6b607c: r4 = Null
    //     0x6b607c: mov             x4, NULL
    // 0x6b6080: b               #0x6b5f64
    // 0x6b6084: LoadField: r0 = r1->field_27
    //     0x6b6084: ldur            w0, [x1, #0x27]
    // 0x6b6088: DecompressPointer r0
    //     0x6b6088: add             x0, x0, HEAP, lsl #32
    // 0x6b608c: cmp             w0, NULL
    // 0x6b6090: b.ne            #0x6b609c
    // 0x6b6094: r1 = Null
    //     0x6b6094: mov             x1, NULL
    // 0x6b6098: b               #0x6b60ac
    // 0x6b609c: mov             x1, x0
    // 0x6b60a0: r2 = Directionality
    //     0x6b60a0: ldr             x2, [PP, #0x7450]  ; [pp+0x7450] Type: Directionality
    // 0x6b60a4: r0 = []()
    //     0x6b60a4: bl              #0x639824  ; [package:flutter/src/foundation/persistent_hash_map.dart] PersistentHashMap::[]
    // 0x6b60a8: mov             x1, x0
    // 0x6b60ac: mov             x4, x1
    // 0x6b60b0: b               #0x6b5f64
    // 0x6b60b4: ldur            x0, [fp, #-8]
    // 0x6b60b8: LeaveFrame
    //     0x6b60b8: mov             SP, fp
    //     0x6b60bc: ldp             fp, lr, [SP], #0x10
    // 0x6b60c0: ret
    //     0x6b60c0: ret             
    // 0x6b60c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b60c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b60c8: b               #0x6b5f3c
    // 0x6b60cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b60cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b60d0: b               #0x6b5f78
    // 0x6b60d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b60d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Set<Directionality> <anonymous closure>(dynamic, _ReadingOrderSortData) {
    // ** addr: 0x6b617c, size: 0x38
    // 0x6b617c: EnterFrame
    //     0x6b617c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b6180: mov             fp, SP
    // 0x6b6184: CheckStackOverflow
    //     0x6b6184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b6188: cmp             SP, x16
    //     0x6b618c: b.ls            #0x6b61ac
    // 0x6b6190: ldr             x1, [fp, #0x10]
    // 0x6b6194: r0 = directionalAncestors()
    //     0x6b6194: bl              #0x6b5e68  ; [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderSortData::directionalAncestors
    // 0x6b6198: mov             x1, x0
    // 0x6b619c: r0 = toSet()
    //     0x6b619c: bl              #0x863cac  ; [dart:core] _GrowableList::toSet
    // 0x6b61a0: LeaveFrame
    //     0x6b61a0: mov             SP, fp
    //     0x6b61a4: ldp             fp, lr, [SP], #0x10
    // 0x6b61a8: ret
    //     0x6b61a8: ret             
    // 0x6b61ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b61ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b61b0: b               #0x6b6190
  }
  static _ _findDirectionality(/* No info */) {
    // ** addr: 0x6b7258, size: 0x58
    // 0x6b7258: EnterFrame
    //     0x6b7258: stp             fp, lr, [SP, #-0x10]!
    //     0x6b725c: mov             fp, SP
    // 0x6b7260: AllocStack(0x10)
    //     0x6b7260: sub             SP, SP, #0x10
    // 0x6b7264: CheckStackOverflow
    //     0x6b7264: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b7268: cmp             SP, x16
    //     0x6b726c: b.ls            #0x6b72a8
    // 0x6b7270: r16 = <Directionality>
    //     0x6b7270: ldr             x16, [PP, #0x7160]  ; [pp+0x7160] TypeArguments: <Directionality>
    // 0x6b7274: stp             x1, x16, [SP]
    // 0x6b7278: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x6b7278: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x6b727c: r0 = getInheritedWidgetOfExactType()
    //     0x6b727c: bl              #0x651934  ; [package:flutter/src/widgets/framework.dart] Element::getInheritedWidgetOfExactType
    // 0x6b7280: cmp             w0, NULL
    // 0x6b7284: b.ne            #0x6b7290
    // 0x6b7288: r0 = Null
    //     0x6b7288: mov             x0, NULL
    // 0x6b728c: b               #0x6b729c
    // 0x6b7290: LoadField: r1 = r0->field_f
    //     0x6b7290: ldur            w1, [x0, #0xf]
    // 0x6b7294: DecompressPointer r1
    //     0x6b7294: add             x1, x1, HEAP, lsl #32
    // 0x6b7298: mov             x0, x1
    // 0x6b729c: LeaveFrame
    //     0x6b729c: mov             SP, fp
    //     0x6b72a0: ldp             fp, lr, [SP], #0x10
    // 0x6b72a4: ret
    //     0x6b72a4: ret             
    // 0x6b72a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b72a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b72ac: b               #0x6b7270
  }
}

// class id: 3789, size: 0xc, field offset: 0x8
//   const constructor, 
abstract class FocusTraversalPolicy extends _DiagnosticableTree&Object&Diagnosticable {

  _ previous(/* No info */) {
    // ** addr: 0x6b400c, size: 0x30
    // 0x6b400c: EnterFrame
    //     0x6b400c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4010: mov             fp, SP
    // 0x6b4014: CheckStackOverflow
    //     0x6b4014: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4018: cmp             SP, x16
    //     0x6b401c: b.ls            #0x6b4034
    // 0x6b4020: r3 = false
    //     0x6b4020: add             x3, NULL, #0x30  ; false
    // 0x6b4024: r0 = _moveFocus()
    //     0x6b4024: bl              #0x6b403c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_moveFocus
    // 0x6b4028: LeaveFrame
    //     0x6b4028: mov             SP, fp
    //     0x6b402c: ldp             fp, lr, [SP], #0x10
    // 0x6b4030: ret
    //     0x6b4030: ret             
    // 0x6b4034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4034: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4038: b               #0x6b4020
  }
  _ _moveFocus(/* No info */) {
    // ** addr: 0x6b403c, size: 0x620
    // 0x6b403c: EnterFrame
    //     0x6b403c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4040: mov             fp, SP
    // 0x6b4044: AllocStack(0x58)
    //     0x6b4044: sub             SP, SP, #0x58
    // 0x6b4048: SetupParameters(FocusTraversalPolicy this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */)
    //     0x6b4048: mov             x0, x2
    //     0x6b404c: stur            x2, [fp, #-0x10]
    //     0x6b4050: mov             x2, x1
    //     0x6b4054: mov             x5, x3
    //     0x6b4058: stur            x1, [fp, #-8]
    //     0x6b405c: stur            x3, [fp, #-0x18]
    // 0x6b4060: CheckStackOverflow
    //     0x6b4060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4064: cmp             SP, x16
    //     0x6b4068: b.ls            #0x6b4638
    // 0x6b406c: r1 = LoadClassIdInstr(r0)
    //     0x6b406c: ldur            x1, [x0, #-1]
    //     0x6b4070: ubfx            x1, x1, #0xc, #0x14
    // 0x6b4074: sub             x16, x1, #0xb67
    // 0x6b4078: cmp             x16, #1
    // 0x6b407c: b.hi            #0x6b408c
    // 0x6b4080: mov             x1, x0
    // 0x6b4084: r0 = enclosingScope()
    //     0x6b4084: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x6b4088: b               #0x6b4090
    // 0x6b408c: ldur            x0, [fp, #-0x10]
    // 0x6b4090: stur            x0, [fp, #-0x20]
    // 0x6b4094: cmp             w0, NULL
    // 0x6b4098: b.eq            #0x6b4640
    // 0x6b409c: ldur            x1, [fp, #-8]
    // 0x6b40a0: mov             x2, x0
    // 0x6b40a4: r0 = invalidateScopeData()
    //     0x6b40a4: bl              #0x6b8c9c  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::invalidateScopeData
    // 0x6b40a8: ldur            x1, [fp, #-0x20]
    // 0x6b40ac: LoadField: r0 = r1->field_6b
    //     0x6b40ac: ldur            w0, [x1, #0x6b]
    // 0x6b40b0: DecompressPointer r0
    //     0x6b40b0: add             x0, x0, HEAP, lsl #32
    // 0x6b40b4: r16 = <FocusNode>
    //     0x6b40b4: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x6b40b8: stp             x0, x16, [SP]
    // 0x6b40bc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x6b40bc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x6b40c0: r0 = IterableExtensions.lastOrNull()
    //     0x6b40c0: bl              #0x69058c  ; [dart:collection] ::IterableExtensions.lastOrNull
    // 0x6b40c4: stur            x0, [fp, #-0x28]
    // 0x6b40c8: cmp             w0, NULL
    // 0x6b40cc: b.ne            #0x6b4128
    // 0x6b40d0: ldur            x5, [fp, #-0x18]
    // 0x6b40d4: tbnz            w5, #4, #0x6b40f0
    // 0x6b40d8: ldur            x1, [fp, #-8]
    // 0x6b40dc: ldur            x2, [fp, #-0x10]
    // 0x6b40e0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6b40e0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6b40e4: r0 = _findInitialFocus()
    //     0x6b40e4: bl              #0x6b8b10  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_findInitialFocus
    // 0x6b40e8: mov             x2, x0
    // 0x6b40ec: b               #0x6b4100
    // 0x6b40f0: ldur            x1, [fp, #-8]
    // 0x6b40f4: ldur            x2, [fp, #-0x10]
    // 0x6b40f8: r0 = findLastFocus()
    //     0x6b40f8: bl              #0x6b8ad4  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::findLastFocus
    // 0x6b40fc: mov             x2, x0
    // 0x6b4100: ldur            x5, [fp, #-0x18]
    // 0x6b4104: tbnz            w5, #4, #0x6b4110
    // 0x6b4108: r3 = Instance_ScrollPositionAlignmentPolicy
    //     0x6b4108: ldr             x3, [PP, #0x7350]  ; [pp+0x7350] Obj!ScrollPositionAlignmentPolicy@e33bc1
    // 0x6b410c: b               #0x6b4114
    // 0x6b4110: r3 = Instance_ScrollPositionAlignmentPolicy
    //     0x6b4110: ldr             x3, [PP, #0x7358]  ; [pp+0x7358] Obj!ScrollPositionAlignmentPolicy@e33ba1
    // 0x6b4114: ldur            x1, [fp, #-8]
    // 0x6b4118: r0 = _requestTabTraversalFocus()
    //     0x6b4118: bl              #0x6b89a0  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_requestTabTraversalFocus
    // 0x6b411c: LeaveFrame
    //     0x6b411c: mov             SP, fp
    //     0x6b4120: ldp             fp, lr, [SP], #0x10
    // 0x6b4124: ret
    //     0x6b4124: ret             
    // 0x6b4128: ldur            x5, [fp, #-0x18]
    // 0x6b412c: ldur            x1, [fp, #-0x20]
    // 0x6b4130: mov             x2, x0
    // 0x6b4134: r0 = _sortAllDescendants()
    //     0x6b4134: bl              #0x6b465c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_sortAllDescendants
    // 0x6b4138: ldur            x5, [fp, #-0x18]
    // 0x6b413c: stur            x0, [fp, #-0x10]
    // 0x6b4140: tbnz            w5, #4, #0x6b42c0
    // 0x6b4144: ldur            x2, [fp, #-0x28]
    // 0x6b4148: mov             x1, x0
    // 0x6b414c: r0 = last()
    //     0x6b414c: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x6b4150: mov             x1, x0
    // 0x6b4154: ldur            x0, [fp, #-0x28]
    // 0x6b4158: cmp             w0, w1
    // 0x6b415c: b.ne            #0x6b42b8
    // 0x6b4160: ldur            x2, [fp, #-0x20]
    // 0x6b4164: LoadField: r1 = r2->field_67
    //     0x6b4164: ldur            w1, [x2, #0x67]
    // 0x6b4168: DecompressPointer r1
    //     0x6b4168: add             x1, x1, HEAP, lsl #32
    // 0x6b416c: LoadField: r3 = r1->field_7
    //     0x6b416c: ldur            x3, [x1, #7]
    // 0x6b4170: cmp             x3, #1
    // 0x6b4174: b.gt            #0x6b41c4
    // 0x6b4178: cmp             x3, #0
    // 0x6b417c: b.gt            #0x6b41a8
    // 0x6b4180: ldur            x1, [fp, #-0x10]
    // 0x6b4184: r0 = first()
    //     0x6b4184: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6b4188: ldur            x1, [fp, #-8]
    // 0x6b418c: mov             x2, x0
    // 0x6b4190: ldur            x5, [fp, #-0x18]
    // 0x6b4194: r3 = Instance_ScrollPositionAlignmentPolicy
    //     0x6b4194: ldr             x3, [PP, #0x7350]  ; [pp+0x7350] Obj!ScrollPositionAlignmentPolicy@e33bc1
    // 0x6b4198: r0 = _requestTabTraversalFocus()
    //     0x6b4198: bl              #0x6b89a0  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_requestTabTraversalFocus
    // 0x6b419c: LeaveFrame
    //     0x6b419c: mov             SP, fp
    //     0x6b41a0: ldp             fp, lr, [SP], #0x10
    // 0x6b41a4: ret
    //     0x6b41a4: ret             
    // 0x6b41a8: mov             x1, x0
    // 0x6b41ac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b41ac: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b41b0: r0 = unfocus()
    //     0x6b41b0: bl              #0x6a834c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::unfocus
    // 0x6b41b4: r0 = false
    //     0x6b41b4: add             x0, NULL, #0x30  ; false
    // 0x6b41b8: LeaveFrame
    //     0x6b41b8: mov             SP, fp
    //     0x6b41bc: ldp             fp, lr, [SP], #0x10
    // 0x6b41c0: ret
    //     0x6b41c0: ret             
    // 0x6b41c4: mov             x1, x2
    // 0x6b41c8: r0 = enclosingScope()
    //     0x6b41c8: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x6b41cc: stur            x0, [fp, #-0x30]
    // 0x6b41d0: cmp             w0, NULL
    // 0x6b41d4: b.eq            #0x6b4290
    // 0x6b41d8: r1 = LoadStaticField(0x7d4)
    //     0x6b41d8: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x6b41dc: ldr             x1, [x1, #0xfa8]
    // 0x6b41e0: cmp             w1, NULL
    // 0x6b41e4: b.eq            #0x6b4644
    // 0x6b41e8: LoadField: r2 = r1->field_eb
    //     0x6b41e8: ldur            w2, [x1, #0xeb]
    // 0x6b41ec: DecompressPointer r2
    //     0x6b41ec: add             x2, x2, HEAP, lsl #32
    // 0x6b41f0: cmp             w2, NULL
    // 0x6b41f4: b.eq            #0x6b4648
    // 0x6b41f8: LoadField: r1 = r2->field_13
    //     0x6b41f8: ldur            w1, [x2, #0x13]
    // 0x6b41fc: DecompressPointer r1
    //     0x6b41fc: add             x1, x1, HEAP, lsl #32
    // 0x6b4200: LoadField: r2 = r1->field_27
    //     0x6b4200: ldur            w2, [x1, #0x27]
    // 0x6b4204: DecompressPointer r2
    //     0x6b4204: add             x2, x2, HEAP, lsl #32
    // 0x6b4208: cmp             w0, w2
    // 0x6b420c: b.eq            #0x6b4290
    // 0x6b4210: ldur            x1, [fp, #-0x28]
    // 0x6b4214: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b4214: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b4218: r0 = unfocus()
    //     0x6b4218: bl              #0x6a834c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::unfocus
    // 0x6b421c: ldur            x1, [fp, #-0x30]
    // 0x6b4220: r0 = nextFocus()
    //     0x6b4220: bl              #0x6b8d10  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::nextFocus
    // 0x6b4224: ldur            x1, [fp, #-0x28]
    // 0x6b4228: r0 = enclosingScope()
    //     0x6b4228: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x6b422c: cmp             w0, NULL
    // 0x6b4230: b.ne            #0x6b423c
    // 0x6b4234: r0 = Null
    //     0x6b4234: mov             x0, NULL
    // 0x6b4238: b               #0x6b4254
    // 0x6b423c: LoadField: r1 = r0->field_6b
    //     0x6b423c: ldur            w1, [x0, #0x6b]
    // 0x6b4240: DecompressPointer r1
    //     0x6b4240: add             x1, x1, HEAP, lsl #32
    // 0x6b4244: r16 = <FocusNode>
    //     0x6b4244: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x6b4248: stp             x1, x16, [SP]
    // 0x6b424c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x6b424c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x6b4250: r0 = IterableExtensions.lastOrNull()
    //     0x6b4250: bl              #0x69058c  ; [dart:collection] ::IterableExtensions.lastOrNull
    // 0x6b4254: r1 = 60
    //     0x6b4254: movz            x1, #0x3c
    // 0x6b4258: branchIfSmi(r0, 0x6b4264)
    //     0x6b4258: tbz             w0, #0, #0x6b4264
    // 0x6b425c: r1 = LoadClassIdInstr(r0)
    //     0x6b425c: ldur            x1, [x0, #-1]
    //     0x6b4260: ubfx            x1, x1, #0xc, #0x14
    // 0x6b4264: ldur            x16, [fp, #-0x28]
    // 0x6b4268: stp             x16, x0, [SP]
    // 0x6b426c: mov             x0, x1
    // 0x6b4270: mov             lr, x0
    // 0x6b4274: ldr             lr, [x21, lr, lsl #3]
    // 0x6b4278: blr             lr
    // 0x6b427c: eor             x1, x0, #0x10
    // 0x6b4280: mov             x0, x1
    // 0x6b4284: LeaveFrame
    //     0x6b4284: mov             SP, fp
    //     0x6b4288: ldp             fp, lr, [SP], #0x10
    // 0x6b428c: ret
    //     0x6b428c: ret             
    // 0x6b4290: ldur            x1, [fp, #-0x10]
    // 0x6b4294: r0 = first()
    //     0x6b4294: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6b4298: ldur            x1, [fp, #-8]
    // 0x6b429c: mov             x2, x0
    // 0x6b42a0: ldur            x5, [fp, #-0x18]
    // 0x6b42a4: r3 = Instance_ScrollPositionAlignmentPolicy
    //     0x6b42a4: ldr             x3, [PP, #0x7350]  ; [pp+0x7350] Obj!ScrollPositionAlignmentPolicy@e33bc1
    // 0x6b42a8: r0 = _requestTabTraversalFocus()
    //     0x6b42a8: bl              #0x6b89a0  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_requestTabTraversalFocus
    // 0x6b42ac: LeaveFrame
    //     0x6b42ac: mov             SP, fp
    //     0x6b42b0: ldp             fp, lr, [SP], #0x10
    // 0x6b42b4: ret
    //     0x6b42b4: ret             
    // 0x6b42b8: ldur            x2, [fp, #-0x20]
    // 0x6b42bc: b               #0x6b42c4
    // 0x6b42c0: ldur            x2, [fp, #-0x20]
    // 0x6b42c4: ldur            x5, [fp, #-0x18]
    // 0x6b42c8: tbz             w5, #4, #0x6b443c
    // 0x6b42cc: ldur            x0, [fp, #-0x28]
    // 0x6b42d0: ldur            x1, [fp, #-0x10]
    // 0x6b42d4: r0 = first()
    //     0x6b42d4: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6b42d8: mov             x1, x0
    // 0x6b42dc: ldur            x0, [fp, #-0x28]
    // 0x6b42e0: cmp             w0, w1
    // 0x6b42e4: b.ne            #0x6b443c
    // 0x6b42e8: ldur            x1, [fp, #-0x20]
    // 0x6b42ec: LoadField: r2 = r1->field_67
    //     0x6b42ec: ldur            w2, [x1, #0x67]
    // 0x6b42f0: DecompressPointer r2
    //     0x6b42f0: add             x2, x2, HEAP, lsl #32
    // 0x6b42f4: LoadField: r3 = r2->field_7
    //     0x6b42f4: ldur            x3, [x2, #7]
    // 0x6b42f8: cmp             x3, #1
    // 0x6b42fc: b.gt            #0x6b434c
    // 0x6b4300: cmp             x3, #0
    // 0x6b4304: b.gt            #0x6b4330
    // 0x6b4308: ldur            x1, [fp, #-0x10]
    // 0x6b430c: r0 = last()
    //     0x6b430c: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x6b4310: ldur            x1, [fp, #-8]
    // 0x6b4314: mov             x2, x0
    // 0x6b4318: ldur            x5, [fp, #-0x18]
    // 0x6b431c: r3 = Instance_ScrollPositionAlignmentPolicy
    //     0x6b431c: ldr             x3, [PP, #0x7358]  ; [pp+0x7358] Obj!ScrollPositionAlignmentPolicy@e33ba1
    // 0x6b4320: r0 = _requestTabTraversalFocus()
    //     0x6b4320: bl              #0x6b89a0  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_requestTabTraversalFocus
    // 0x6b4324: LeaveFrame
    //     0x6b4324: mov             SP, fp
    //     0x6b4328: ldp             fp, lr, [SP], #0x10
    // 0x6b432c: ret
    //     0x6b432c: ret             
    // 0x6b4330: mov             x1, x0
    // 0x6b4334: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b4334: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b4338: r0 = unfocus()
    //     0x6b4338: bl              #0x6a834c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::unfocus
    // 0x6b433c: r0 = false
    //     0x6b433c: add             x0, NULL, #0x30  ; false
    // 0x6b4340: LeaveFrame
    //     0x6b4340: mov             SP, fp
    //     0x6b4344: ldp             fp, lr, [SP], #0x10
    // 0x6b4348: ret
    //     0x6b4348: ret             
    // 0x6b434c: r0 = enclosingScope()
    //     0x6b434c: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x6b4350: stur            x0, [fp, #-0x20]
    // 0x6b4354: cmp             w0, NULL
    // 0x6b4358: b.eq            #0x6b4414
    // 0x6b435c: r1 = LoadStaticField(0x7d4)
    //     0x6b435c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x6b4360: ldr             x1, [x1, #0xfa8]
    // 0x6b4364: cmp             w1, NULL
    // 0x6b4368: b.eq            #0x6b464c
    // 0x6b436c: LoadField: r2 = r1->field_eb
    //     0x6b436c: ldur            w2, [x1, #0xeb]
    // 0x6b4370: DecompressPointer r2
    //     0x6b4370: add             x2, x2, HEAP, lsl #32
    // 0x6b4374: cmp             w2, NULL
    // 0x6b4378: b.eq            #0x6b4650
    // 0x6b437c: LoadField: r1 = r2->field_13
    //     0x6b437c: ldur            w1, [x2, #0x13]
    // 0x6b4380: DecompressPointer r1
    //     0x6b4380: add             x1, x1, HEAP, lsl #32
    // 0x6b4384: LoadField: r2 = r1->field_27
    //     0x6b4384: ldur            w2, [x1, #0x27]
    // 0x6b4388: DecompressPointer r2
    //     0x6b4388: add             x2, x2, HEAP, lsl #32
    // 0x6b438c: cmp             w0, w2
    // 0x6b4390: b.eq            #0x6b4414
    // 0x6b4394: ldur            x1, [fp, #-0x28]
    // 0x6b4398: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b4398: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b439c: r0 = unfocus()
    //     0x6b439c: bl              #0x6a834c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::unfocus
    // 0x6b43a0: ldur            x1, [fp, #-0x20]
    // 0x6b43a4: r0 = previousFocus()
    //     0x6b43a4: bl              #0x6b3fb4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::previousFocus
    // 0x6b43a8: ldur            x1, [fp, #-0x28]
    // 0x6b43ac: r0 = enclosingScope()
    //     0x6b43ac: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x6b43b0: cmp             w0, NULL
    // 0x6b43b4: b.ne            #0x6b43c0
    // 0x6b43b8: r0 = Null
    //     0x6b43b8: mov             x0, NULL
    // 0x6b43bc: b               #0x6b43d8
    // 0x6b43c0: LoadField: r1 = r0->field_6b
    //     0x6b43c0: ldur            w1, [x0, #0x6b]
    // 0x6b43c4: DecompressPointer r1
    //     0x6b43c4: add             x1, x1, HEAP, lsl #32
    // 0x6b43c8: r16 = <FocusNode>
    //     0x6b43c8: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x6b43cc: stp             x1, x16, [SP]
    // 0x6b43d0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x6b43d0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x6b43d4: r0 = IterableExtensions.lastOrNull()
    //     0x6b43d4: bl              #0x69058c  ; [dart:collection] ::IterableExtensions.lastOrNull
    // 0x6b43d8: r1 = 60
    //     0x6b43d8: movz            x1, #0x3c
    // 0x6b43dc: branchIfSmi(r0, 0x6b43e8)
    //     0x6b43dc: tbz             w0, #0, #0x6b43e8
    // 0x6b43e0: r1 = LoadClassIdInstr(r0)
    //     0x6b43e0: ldur            x1, [x0, #-1]
    //     0x6b43e4: ubfx            x1, x1, #0xc, #0x14
    // 0x6b43e8: ldur            x16, [fp, #-0x28]
    // 0x6b43ec: stp             x16, x0, [SP]
    // 0x6b43f0: mov             x0, x1
    // 0x6b43f4: mov             lr, x0
    // 0x6b43f8: ldr             lr, [x21, lr, lsl #3]
    // 0x6b43fc: blr             lr
    // 0x6b4400: eor             x1, x0, #0x10
    // 0x6b4404: mov             x0, x1
    // 0x6b4408: LeaveFrame
    //     0x6b4408: mov             SP, fp
    //     0x6b440c: ldp             fp, lr, [SP], #0x10
    // 0x6b4410: ret
    //     0x6b4410: ret             
    // 0x6b4414: ldur            x1, [fp, #-0x10]
    // 0x6b4418: r0 = last()
    //     0x6b4418: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x6b441c: ldur            x1, [fp, #-8]
    // 0x6b4420: mov             x2, x0
    // 0x6b4424: ldur            x5, [fp, #-0x18]
    // 0x6b4428: r3 = Instance_ScrollPositionAlignmentPolicy
    //     0x6b4428: ldr             x3, [PP, #0x7358]  ; [pp+0x7358] Obj!ScrollPositionAlignmentPolicy@e33ba1
    // 0x6b442c: r0 = _requestTabTraversalFocus()
    //     0x6b442c: bl              #0x6b89a0  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_requestTabTraversalFocus
    // 0x6b4430: LeaveFrame
    //     0x6b4430: mov             SP, fp
    //     0x6b4434: ldp             fp, lr, [SP], #0x10
    // 0x6b4438: ret
    //     0x6b4438: ret             
    // 0x6b443c: ldur            x5, [fp, #-0x18]
    // 0x6b4440: tbnz            w5, #4, #0x6b444c
    // 0x6b4444: ldur            x1, [fp, #-0x10]
    // 0x6b4448: b               #0x6b4468
    // 0x6b444c: ldur            x0, [fp, #-0x10]
    // 0x6b4450: LoadField: r1 = r0->field_7
    //     0x6b4450: ldur            w1, [x0, #7]
    // 0x6b4454: DecompressPointer r1
    //     0x6b4454: add             x1, x1, HEAP, lsl #32
    // 0x6b4458: r0 = ReversedListIterable()
    //     0x6b4458: bl              #0x668634  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0x6b445c: mov             x1, x0
    // 0x6b4460: ldur            x0, [fp, #-0x10]
    // 0x6b4464: StoreField: r1->field_b = r0
    //     0x6b4464: stur            w0, [x1, #0xb]
    // 0x6b4468: r0 = LoadClassIdInstr(r1)
    //     0x6b4468: ldur            x0, [x1, #-1]
    //     0x6b446c: ubfx            x0, x0, #0xc, #0x14
    // 0x6b4470: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x6b4470: movz            x17, #0xd35d
    //     0x6b4474: add             lr, x0, x17
    //     0x6b4478: ldr             lr, [x21, lr, lsl #3]
    //     0x6b447c: blr             lr
    // 0x6b4480: mov             x1, x0
    // 0x6b4484: stur            x1, [fp, #-0x40]
    // 0x6b4488: LoadField: r2 = r1->field_b
    //     0x6b4488: ldur            w2, [x1, #0xb]
    // 0x6b448c: DecompressPointer r2
    //     0x6b448c: add             x2, x2, HEAP, lsl #32
    // 0x6b4490: stur            x2, [fp, #-0x30]
    // 0x6b4494: LoadField: r3 = r1->field_f
    //     0x6b4494: ldur            x3, [x1, #0xf]
    // 0x6b4498: stur            x3, [fp, #-0x38]
    // 0x6b449c: LoadField: r4 = r1->field_7
    //     0x6b449c: ldur            w4, [x1, #7]
    // 0x6b44a0: DecompressPointer r4
    //     0x6b44a0: add             x4, x4, HEAP, lsl #32
    // 0x6b44a4: stur            x4, [fp, #-0x20]
    // 0x6b44a8: r5 = Null
    //     0x6b44a8: mov             x5, NULL
    // 0x6b44ac: stur            x5, [fp, #-0x10]
    // 0x6b44b0: CheckStackOverflow
    //     0x6b44b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b44b4: cmp             SP, x16
    //     0x6b44b8: b.ls            #0x6b4654
    // 0x6b44bc: r0 = LoadClassIdInstr(r2)
    //     0x6b44bc: ldur            x0, [x2, #-1]
    //     0x6b44c0: ubfx            x0, x0, #0xc, #0x14
    // 0x6b44c4: str             x2, [SP]
    // 0x6b44c8: r0 = GDT[cid_x0 + 0xc834]()
    //     0x6b44c8: movz            x17, #0xc834
    //     0x6b44cc: add             lr, x0, x17
    //     0x6b44d0: ldr             lr, [x21, lr, lsl #3]
    //     0x6b44d4: blr             lr
    // 0x6b44d8: r1 = LoadInt32Instr(r0)
    //     0x6b44d8: sbfx            x1, x0, #1, #0x1f
    //     0x6b44dc: tbz             w0, #0, #0x6b44e4
    //     0x6b44e0: ldur            x1, [x0, #7]
    // 0x6b44e4: ldur            x3, [fp, #-0x38]
    // 0x6b44e8: cmp             x3, x1
    // 0x6b44ec: b.ne            #0x6b4618
    // 0x6b44f0: ldur            x4, [fp, #-0x40]
    // 0x6b44f4: ArrayLoad: r2 = r4[0]  ; List_8
    //     0x6b44f4: ldur            x2, [x4, #0x17]
    // 0x6b44f8: cmp             x2, x1
    // 0x6b44fc: b.ge            #0x6b4600
    // 0x6b4500: ldur            x5, [fp, #-0x30]
    // 0x6b4504: r0 = LoadClassIdInstr(r5)
    //     0x6b4504: ldur            x0, [x5, #-1]
    //     0x6b4508: ubfx            x0, x0, #0xc, #0x14
    // 0x6b450c: mov             x1, x5
    // 0x6b4510: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x6b4510: movz            x17, #0xd28f
    //     0x6b4514: add             lr, x0, x17
    //     0x6b4518: ldr             lr, [x21, lr, lsl #3]
    //     0x6b451c: blr             lr
    // 0x6b4520: mov             x4, x0
    // 0x6b4524: ldur            x3, [fp, #-0x40]
    // 0x6b4528: stur            x4, [fp, #-0x48]
    // 0x6b452c: StoreField: r3->field_1f = r0
    //     0x6b452c: stur            w0, [x3, #0x1f]
    //     0x6b4530: tbz             w0, #0, #0x6b454c
    //     0x6b4534: ldurb           w16, [x3, #-1]
    //     0x6b4538: ldurb           w17, [x0, #-1]
    //     0x6b453c: and             x16, x17, x16, lsr #2
    //     0x6b4540: tst             x16, HEAP, lsr #32
    //     0x6b4544: b.eq            #0x6b454c
    //     0x6b4548: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x6b454c: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x6b454c: ldur            x0, [x3, #0x17]
    // 0x6b4550: add             x1, x0, #1
    // 0x6b4554: ArrayStore: r3[0] = r1  ; List_8
    //     0x6b4554: stur            x1, [x3, #0x17]
    // 0x6b4558: cmp             w4, NULL
    // 0x6b455c: b.ne            #0x6b458c
    // 0x6b4560: mov             x0, x4
    // 0x6b4564: ldur            x2, [fp, #-0x20]
    // 0x6b4568: r1 = Null
    //     0x6b4568: mov             x1, NULL
    // 0x6b456c: cmp             w2, NULL
    // 0x6b4570: b.eq            #0x6b458c
    // 0x6b4574: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6b4574: ldur            w4, [x2, #0x17]
    // 0x6b4578: DecompressPointer r4
    //     0x6b4578: add             x4, x4, HEAP, lsl #32
    // 0x6b457c: r8 = X0
    //     0x6b457c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6b4580: LoadField: r9 = r4->field_7
    //     0x6b4580: ldur            x9, [x4, #7]
    // 0x6b4584: r3 = Null
    //     0x6b4584: ldr             x3, [PP, #0x7360]  ; [pp+0x7360] Null
    // 0x6b4588: blr             x9
    // 0x6b458c: ldur            x0, [fp, #-0x10]
    // 0x6b4590: r1 = 60
    //     0x6b4590: movz            x1, #0x3c
    // 0x6b4594: branchIfSmi(r0, 0x6b45a0)
    //     0x6b4594: tbz             w0, #0, #0x6b45a0
    // 0x6b4598: r1 = LoadClassIdInstr(r0)
    //     0x6b4598: ldur            x1, [x0, #-1]
    //     0x6b459c: ubfx            x1, x1, #0xc, #0x14
    // 0x6b45a0: ldur            x16, [fp, #-0x28]
    // 0x6b45a4: stp             x16, x0, [SP]
    // 0x6b45a8: mov             x0, x1
    // 0x6b45ac: mov             lr, x0
    // 0x6b45b0: ldr             lr, [x21, lr, lsl #3]
    // 0x6b45b4: blr             lr
    // 0x6b45b8: tbz             w0, #4, #0x6b45d4
    // 0x6b45bc: ldur            x5, [fp, #-0x48]
    // 0x6b45c0: ldur            x1, [fp, #-0x40]
    // 0x6b45c4: ldur            x4, [fp, #-0x20]
    // 0x6b45c8: ldur            x2, [fp, #-0x30]
    // 0x6b45cc: ldur            x3, [fp, #-0x38]
    // 0x6b45d0: b               #0x6b44ac
    // 0x6b45d4: ldur            x5, [fp, #-0x18]
    // 0x6b45d8: tbnz            w5, #4, #0x6b45e4
    // 0x6b45dc: r3 = Instance_ScrollPositionAlignmentPolicy
    //     0x6b45dc: ldr             x3, [PP, #0x7350]  ; [pp+0x7350] Obj!ScrollPositionAlignmentPolicy@e33bc1
    // 0x6b45e0: b               #0x6b45e8
    // 0x6b45e4: r3 = Instance_ScrollPositionAlignmentPolicy
    //     0x6b45e4: ldr             x3, [PP, #0x7358]  ; [pp+0x7358] Obj!ScrollPositionAlignmentPolicy@e33ba1
    // 0x6b45e8: ldur            x1, [fp, #-8]
    // 0x6b45ec: ldur            x2, [fp, #-0x48]
    // 0x6b45f0: r0 = _requestTabTraversalFocus()
    //     0x6b45f0: bl              #0x6b89a0  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_requestTabTraversalFocus
    // 0x6b45f4: LeaveFrame
    //     0x6b45f4: mov             SP, fp
    //     0x6b45f8: ldp             fp, lr, [SP], #0x10
    // 0x6b45fc: ret
    //     0x6b45fc: ret             
    // 0x6b4600: mov             x0, x4
    // 0x6b4604: StoreField: r0->field_1f = rNULL
    //     0x6b4604: stur            NULL, [x0, #0x1f]
    // 0x6b4608: r0 = false
    //     0x6b4608: add             x0, NULL, #0x30  ; false
    // 0x6b460c: LeaveFrame
    //     0x6b460c: mov             SP, fp
    //     0x6b4610: ldp             fp, lr, [SP], #0x10
    // 0x6b4614: ret
    //     0x6b4614: ret             
    // 0x6b4618: ldur            x0, [fp, #-0x30]
    // 0x6b461c: r0 = ConcurrentModificationError()
    //     0x6b461c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6b4620: mov             x1, x0
    // 0x6b4624: ldur            x0, [fp, #-0x30]
    // 0x6b4628: StoreField: r1->field_b = r0
    //     0x6b4628: stur            w0, [x1, #0xb]
    // 0x6b462c: mov             x0, x1
    // 0x6b4630: r0 = Throw()
    //     0x6b4630: bl              #0xec04b8  ; ThrowStub
    // 0x6b4634: brk             #0
    // 0x6b4638: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4638: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b463c: b               #0x6b406c
    // 0x6b4640: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b4640: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b4644: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b4644: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b4648: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b4648: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b464c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b464c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b4650: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b4650: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b4654: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4654: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4658: b               #0x6b44bc
  }
  static _ _sortAllDescendants(/* No info */) {
    // ** addr: 0x6b465c, size: 0x388
    // 0x6b465c: EnterFrame
    //     0x6b465c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4660: mov             fp, SP
    // 0x6b4664: AllocStack(0x48)
    //     0x6b4664: sub             SP, SP, #0x48
    // 0x6b4668: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6b4668: stur            x1, [fp, #-8]
    //     0x6b466c: stur            x2, [fp, #-0x10]
    // 0x6b4670: CheckStackOverflow
    //     0x6b4670: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4674: cmp             SP, x16
    //     0x6b4678: b.ls            #0x6b49c0
    // 0x6b467c: r1 = 4
    //     0x6b467c: movz            x1, #0x4
    // 0x6b4680: r0 = AllocateContext()
    //     0x6b4680: bl              #0xec126c  ; AllocateContextStub
    // 0x6b4684: mov             x2, x0
    // 0x6b4688: ldur            x0, [fp, #-0x10]
    // 0x6b468c: stur            x2, [fp, #-0x18]
    // 0x6b4690: StoreField: r2->field_f = r0
    //     0x6b4690: stur            w0, [x2, #0xf]
    // 0x6b4694: ldur            x1, [fp, #-8]
    // 0x6b4698: r0 = _getGroupNode()
    //     0x6b4698: bl              #0x6517d8  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalGroup::_getGroupNode
    // 0x6b469c: mov             x4, x0
    // 0x6b46a0: ldur            x0, [fp, #-0x18]
    // 0x6b46a4: stur            x4, [fp, #-0x10]
    // 0x6b46a8: LoadField: r3 = r0->field_f
    //     0x6b46a8: ldur            w3, [x0, #0xf]
    // 0x6b46ac: DecompressPointer r3
    //     0x6b46ac: add             x3, x3, HEAP, lsl #32
    // 0x6b46b0: ldur            x1, [fp, #-8]
    // 0x6b46b4: mov             x2, x4
    // 0x6b46b8: r0 = _findGroups()
    //     0x6b46b8: bl              #0x6b7430  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_findGroups
    // 0x6b46bc: mov             x3, x0
    // 0x6b46c0: ldur            x2, [fp, #-0x18]
    // 0x6b46c4: stur            x3, [fp, #-8]
    // 0x6b46c8: StoreField: r2->field_13 = r0
    //     0x6b46c8: stur            w0, [x2, #0x13]
    //     0x6b46cc: ldurb           w16, [x2, #-1]
    //     0x6b46d0: ldurb           w17, [x0, #-1]
    //     0x6b46d4: and             x16, x17, x16, lsr #2
    //     0x6b46d8: tst             x16, HEAP, lsr #32
    //     0x6b46dc: b.eq            #0x6b46e4
    //     0x6b46e0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6b46e4: LoadField: r1 = r3->field_7
    //     0x6b46e4: ldur            w1, [x3, #7]
    // 0x6b46e8: DecompressPointer r1
    //     0x6b46e8: add             x1, x1, HEAP, lsl #32
    // 0x6b46ec: r0 = _CompactIterable()
    //     0x6b46ec: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x6b46f0: mov             x1, x0
    // 0x6b46f4: ldur            x0, [fp, #-8]
    // 0x6b46f8: StoreField: r1->field_b = r0
    //     0x6b46f8: stur            w0, [x1, #0xb]
    // 0x6b46fc: r2 = -2
    //     0x6b46fc: orr             x2, xzr, #0xfffffffffffffffe
    // 0x6b4700: StoreField: r1->field_f = r2
    //     0x6b4700: stur            x2, [x1, #0xf]
    // 0x6b4704: r2 = 2
    //     0x6b4704: movz            x2, #0x2
    // 0x6b4708: ArrayStore: r1[0] = r2  ; List_8
    //     0x6b4708: stur            x2, [x1, #0x17]
    // 0x6b470c: r0 = iterator()
    //     0x6b470c: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x6b4710: stur            x0, [fp, #-0x28]
    // 0x6b4714: LoadField: r2 = r0->field_7
    //     0x6b4714: ldur            w2, [x0, #7]
    // 0x6b4718: DecompressPointer r2
    //     0x6b4718: add             x2, x2, HEAP, lsl #32
    // 0x6b471c: stur            x2, [fp, #-0x20]
    // 0x6b4720: ldur            x3, [fp, #-8]
    // 0x6b4724: CheckStackOverflow
    //     0x6b4724: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4728: cmp             SP, x16
    //     0x6b472c: b.ls            #0x6b49c8
    // 0x6b4730: mov             x1, x0
    // 0x6b4734: r0 = moveNext()
    //     0x6b4734: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x6b4738: tbnz            w0, #4, #0x6b48a4
    // 0x6b473c: ldur            x3, [fp, #-0x28]
    // 0x6b4740: LoadField: r4 = r3->field_33
    //     0x6b4740: ldur            w4, [x3, #0x33]
    // 0x6b4744: DecompressPointer r4
    //     0x6b4744: add             x4, x4, HEAP, lsl #32
    // 0x6b4748: stur            x4, [fp, #-0x30]
    // 0x6b474c: cmp             w4, NULL
    // 0x6b4750: b.ne            #0x6b4780
    // 0x6b4754: mov             x0, x4
    // 0x6b4758: ldur            x2, [fp, #-0x20]
    // 0x6b475c: r1 = Null
    //     0x6b475c: mov             x1, NULL
    // 0x6b4760: cmp             w2, NULL
    // 0x6b4764: b.eq            #0x6b4780
    // 0x6b4768: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6b4768: ldur            w4, [x2, #0x17]
    // 0x6b476c: DecompressPointer r4
    //     0x6b476c: add             x4, x4, HEAP, lsl #32
    // 0x6b4770: r8 = X0
    //     0x6b4770: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6b4774: LoadField: r9 = r4->field_7
    //     0x6b4774: ldur            x9, [x4, #7]
    // 0x6b4778: r3 = Null
    //     0x6b4778: ldr             x3, [PP, #0x7370]  ; [pp+0x7370] Null
    // 0x6b477c: blr             x9
    // 0x6b4780: ldur            x0, [fp, #-8]
    // 0x6b4784: mov             x1, x0
    // 0x6b4788: ldur            x2, [fp, #-0x30]
    // 0x6b478c: r0 = _getValueOrData()
    //     0x6b478c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x6b4790: mov             x1, x0
    // 0x6b4794: ldur            x0, [fp, #-8]
    // 0x6b4798: LoadField: r2 = r0->field_f
    //     0x6b4798: ldur            w2, [x0, #0xf]
    // 0x6b479c: DecompressPointer r2
    //     0x6b479c: add             x2, x2, HEAP, lsl #32
    // 0x6b47a0: cmp             w2, w1
    // 0x6b47a4: b.ne            #0x6b47ac
    // 0x6b47a8: r1 = Null
    //     0x6b47a8: mov             x1, NULL
    // 0x6b47ac: cmp             w1, NULL
    // 0x6b47b0: b.eq            #0x6b49d0
    // 0x6b47b4: LoadField: r3 = r1->field_7
    //     0x6b47b4: ldur            w3, [x1, #7]
    // 0x6b47b8: DecompressPointer r3
    //     0x6b47b8: add             x3, x3, HEAP, lsl #32
    // 0x6b47bc: mov             x1, x0
    // 0x6b47c0: ldur            x2, [fp, #-0x30]
    // 0x6b47c4: stur            x3, [fp, #-0x38]
    // 0x6b47c8: r0 = _getValueOrData()
    //     0x6b47c8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x6b47cc: mov             x1, x0
    // 0x6b47d0: ldur            x0, [fp, #-8]
    // 0x6b47d4: LoadField: r2 = r0->field_f
    //     0x6b47d4: ldur            w2, [x0, #0xf]
    // 0x6b47d8: DecompressPointer r2
    //     0x6b47d8: add             x2, x2, HEAP, lsl #32
    // 0x6b47dc: cmp             w2, w1
    // 0x6b47e0: b.ne            #0x6b47e8
    // 0x6b47e4: r1 = Null
    //     0x6b47e4: mov             x1, NULL
    // 0x6b47e8: cmp             w1, NULL
    // 0x6b47ec: b.eq            #0x6b49d4
    // 0x6b47f0: LoadField: r2 = r1->field_b
    //     0x6b47f0: ldur            w2, [x1, #0xb]
    // 0x6b47f4: DecompressPointer r2
    //     0x6b47f4: add             x2, x2, HEAP, lsl #32
    // 0x6b47f8: ldur            x1, [fp, #-0x38]
    // 0x6b47fc: r0 = sortDescendants()
    //     0x6b47fc: bl              #0x6b49e4  ; [package:flutter/src/widgets/focus_traversal.dart] ReadingOrderTraversalPolicy::sortDescendants
    // 0x6b4800: mov             x1, x0
    // 0x6b4804: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b4804: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b4808: r0 = toList()
    //     0x6b4808: bl              #0xa52dc8  ; [dart:core] _GrowableList::toList
    // 0x6b480c: ldur            x1, [fp, #-8]
    // 0x6b4810: ldur            x2, [fp, #-0x30]
    // 0x6b4814: stur            x0, [fp, #-0x38]
    // 0x6b4818: r0 = _getValueOrData()
    //     0x6b4818: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x6b481c: mov             x1, x0
    // 0x6b4820: ldur            x0, [fp, #-8]
    // 0x6b4824: LoadField: r2 = r0->field_f
    //     0x6b4824: ldur            w2, [x0, #0xf]
    // 0x6b4828: DecompressPointer r2
    //     0x6b4828: add             x2, x2, HEAP, lsl #32
    // 0x6b482c: cmp             w2, w1
    // 0x6b4830: b.ne            #0x6b4838
    // 0x6b4834: r1 = Null
    //     0x6b4834: mov             x1, NULL
    // 0x6b4838: cmp             w1, NULL
    // 0x6b483c: b.eq            #0x6b49d8
    // 0x6b4840: LoadField: r2 = r1->field_b
    //     0x6b4840: ldur            w2, [x1, #0xb]
    // 0x6b4844: DecompressPointer r2
    //     0x6b4844: add             x2, x2, HEAP, lsl #32
    // 0x6b4848: mov             x1, x2
    // 0x6b484c: r2 = 0
    //     0x6b484c: movz            x2, #0
    // 0x6b4850: r0 = length=()
    //     0x6b4850: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0x6b4854: ldur            x1, [fp, #-8]
    // 0x6b4858: ldur            x2, [fp, #-0x30]
    // 0x6b485c: r0 = _getValueOrData()
    //     0x6b485c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x6b4860: mov             x1, x0
    // 0x6b4864: ldur            x0, [fp, #-8]
    // 0x6b4868: LoadField: r2 = r0->field_f
    //     0x6b4868: ldur            w2, [x0, #0xf]
    // 0x6b486c: DecompressPointer r2
    //     0x6b486c: add             x2, x2, HEAP, lsl #32
    // 0x6b4870: cmp             w2, w1
    // 0x6b4874: b.ne            #0x6b487c
    // 0x6b4878: r1 = Null
    //     0x6b4878: mov             x1, NULL
    // 0x6b487c: cmp             w1, NULL
    // 0x6b4880: b.eq            #0x6b49dc
    // 0x6b4884: LoadField: r2 = r1->field_b
    //     0x6b4884: ldur            w2, [x1, #0xb]
    // 0x6b4888: DecompressPointer r2
    //     0x6b4888: add             x2, x2, HEAP, lsl #32
    // 0x6b488c: mov             x1, x2
    // 0x6b4890: ldur            x2, [fp, #-0x38]
    // 0x6b4894: r0 = addAll()
    //     0x6b4894: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x6b4898: ldur            x0, [fp, #-0x28]
    // 0x6b489c: ldur            x2, [fp, #-0x20]
    // 0x6b48a0: b               #0x6b4720
    // 0x6b48a4: ldur            x3, [fp, #-0x18]
    // 0x6b48a8: ldur            x0, [fp, #-8]
    // 0x6b48ac: r1 = <FocusNode>
    //     0x6b48ac: ldr             x1, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x6b48b0: r2 = 0
    //     0x6b48b0: movz            x2, #0
    // 0x6b48b4: r0 = _GrowableList()
    //     0x6b48b4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b48b8: mov             x4, x0
    // 0x6b48bc: ldur            x3, [fp, #-0x18]
    // 0x6b48c0: stur            x4, [fp, #-0x20]
    // 0x6b48c4: ArrayStore: r3[0] = r0  ; List_4
    //     0x6b48c4: stur            w0, [x3, #0x17]
    //     0x6b48c8: ldurb           w16, [x3, #-1]
    //     0x6b48cc: ldurb           w17, [x0, #-1]
    //     0x6b48d0: and             x16, x17, x16, lsr #2
    //     0x6b48d4: tst             x16, HEAP, lsr #32
    //     0x6b48d8: b.eq            #0x6b48e0
    //     0x6b48dc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x6b48e0: mov             x2, x3
    // 0x6b48e4: r1 = Function 'visitGroups': static.
    //     0x6b48e4: ldr             x1, [PP, #0x7380]  ; [pp+0x7380] AnonymousClosure: static (0x6b87a4), in [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_sortAllDescendants (0x6b465c)
    // 0x6b48e8: r0 = AllocateClosure()
    //     0x6b48e8: bl              #0xec1630  ; AllocateClosureStub
    // 0x6b48ec: mov             x4, x0
    // 0x6b48f0: ldur            x3, [fp, #-0x18]
    // 0x6b48f4: stur            x4, [fp, #-0x28]
    // 0x6b48f8: StoreField: r3->field_1b = r0
    //     0x6b48f8: stur            w0, [x3, #0x1b]
    //     0x6b48fc: ldurb           w16, [x3, #-1]
    //     0x6b4900: ldurb           w17, [x0, #-1]
    //     0x6b4904: and             x16, x17, x16, lsr #2
    //     0x6b4908: tst             x16, HEAP, lsr #32
    //     0x6b490c: b.eq            #0x6b4914
    //     0x6b4910: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x6b4914: ldur            x0, [fp, #-8]
    // 0x6b4918: LoadField: r1 = r0->field_13
    //     0x6b4918: ldur            w1, [x0, #0x13]
    // 0x6b491c: r2 = LoadInt32Instr(r1)
    //     0x6b491c: sbfx            x2, x1, #1, #0x1f
    // 0x6b4920: asr             x1, x2, #1
    // 0x6b4924: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x6b4924: ldur            w2, [x0, #0x17]
    // 0x6b4928: r5 = LoadInt32Instr(r2)
    //     0x6b4928: sbfx            x5, x2, #1, #0x1f
    // 0x6b492c: sub             x2, x1, x5
    // 0x6b4930: cbz             x2, #0x6b4998
    // 0x6b4934: mov             x1, x0
    // 0x6b4938: ldur            x2, [fp, #-0x10]
    // 0x6b493c: r0 = containsKey()
    //     0x6b493c: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x6b4940: tbnz            w0, #4, #0x6b4998
    // 0x6b4944: ldur            x0, [fp, #-8]
    // 0x6b4948: mov             x1, x0
    // 0x6b494c: ldur            x2, [fp, #-0x10]
    // 0x6b4950: r0 = _getValueOrData()
    //     0x6b4950: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x6b4954: mov             x1, x0
    // 0x6b4958: ldur            x0, [fp, #-8]
    // 0x6b495c: LoadField: r2 = r0->field_f
    //     0x6b495c: ldur            w2, [x0, #0xf]
    // 0x6b4960: DecompressPointer r2
    //     0x6b4960: add             x2, x2, HEAP, lsl #32
    // 0x6b4964: cmp             w2, w1
    // 0x6b4968: b.ne            #0x6b4974
    // 0x6b496c: r0 = Null
    //     0x6b496c: mov             x0, NULL
    // 0x6b4970: b               #0x6b4978
    // 0x6b4974: mov             x0, x1
    // 0x6b4978: cmp             w0, NULL
    // 0x6b497c: b.eq            #0x6b49e0
    // 0x6b4980: ldur            x16, [fp, #-0x28]
    // 0x6b4984: stp             x0, x16, [SP]
    // 0x6b4988: ldur            x0, [fp, #-0x28]
    // 0x6b498c: ClosureCall
    //     0x6b498c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x6b4990: ldur            x2, [x0, #0x1f]
    //     0x6b4994: blr             x2
    // 0x6b4998: ldur            x2, [fp, #-0x18]
    // 0x6b499c: r1 = Function '<anonymous closure>': static.
    //     0x6b499c: ldr             x1, [PP, #0x7388]  ; [pp+0x7388] AnonymousClosure: static (0x6b86f0), in [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_sortAllDescendants (0x6b465c)
    // 0x6b49a0: r0 = AllocateClosure()
    //     0x6b49a0: bl              #0xec1630  ; AllocateClosureStub
    // 0x6b49a4: ldur            x1, [fp, #-0x20]
    // 0x6b49a8: mov             x2, x0
    // 0x6b49ac: r0 = removeWhere()
    //     0x6b49ac: bl              #0x6eb068  ; [dart:collection] ListBase::removeWhere
    // 0x6b49b0: ldur            x0, [fp, #-0x20]
    // 0x6b49b4: LeaveFrame
    //     0x6b49b4: mov             SP, fp
    //     0x6b49b8: ldp             fp, lr, [SP], #0x10
    // 0x6b49bc: ret
    //     0x6b49bc: ret             
    // 0x6b49c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b49c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b49c4: b               #0x6b467c
    // 0x6b49c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b49c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b49cc: b               #0x6b4730
    // 0x6b49d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b49d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b49d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b49d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b49d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b49d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b49dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b49dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b49e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b49e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _findGroups(/* No info */) {
    // ** addr: 0x6b7430, size: 0x6a8
    // 0x6b7430: EnterFrame
    //     0x6b7430: stp             fp, lr, [SP, #-0x10]!
    //     0x6b7434: mov             fp, SP
    // 0x6b7438: AllocStack(0x88)
    //     0x6b7438: sub             SP, SP, #0x88
    // 0x6b743c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0x6b743c: stur            x1, [fp, #-8]
    //     0x6b7440: stur            x3, [fp, #-0x10]
    // 0x6b7444: CheckStackOverflow
    //     0x6b7444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b7448: cmp             SP, x16
    //     0x6b744c: b.ls            #0x6b7aa0
    // 0x6b7450: cmp             w2, NULL
    // 0x6b7454: b.ne            #0x6b7460
    // 0x6b7458: r0 = Null
    //     0x6b7458: mov             x0, NULL
    // 0x6b745c: b               #0x6b7468
    // 0x6b7460: LoadField: r0 = r2->field_67
    //     0x6b7460: ldur            w0, [x2, #0x67]
    // 0x6b7464: DecompressPointer r0
    //     0x6b7464: add             x0, x0, HEAP, lsl #32
    // 0x6b7468: cmp             w0, NULL
    // 0x6b746c: b.ne            #0x6b74a0
    // 0x6b7470: r16 = <FocusScopeNode, _DirectionalPolicyData>
    //     0x6b7470: ldr             x16, [PP, #0x7498]  ; [pp+0x7498] TypeArguments: <FocusScopeNode, _DirectionalPolicyData>
    // 0x6b7474: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x6b7478: stp             lr, x16, [SP]
    // 0x6b747c: r0 = Map._fromLiteral()
    //     0x6b747c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x6b7480: stur            x0, [fp, #-0x18]
    // 0x6b7484: r0 = ReadingOrderTraversalPolicy()
    //     0x6b7484: bl              #0x6b7d7c  ; AllocateReadingOrderTraversalPolicyStub -> ReadingOrderTraversalPolicy (size=0x10)
    // 0x6b7488: mov             x1, x0
    // 0x6b748c: ldur            x0, [fp, #-0x18]
    // 0x6b7490: StoreField: r1->field_b = r0
    //     0x6b7490: stur            w0, [x1, #0xb]
    // 0x6b7494: r0 = Closure: (FocusNode, {ScrollPositionAlignmentPolicy? alignmentPolicy, double? alignment, Duration? duration, Curve? curve}) => void from Function 'defaultTraversalRequestFocusCallback': static.
    //     0x6b7494: ldr             x0, [PP, #0x74a0]  ; [pp+0x74a0] Closure: (FocusNode, {ScrollPositionAlignmentPolicy? alignmentPolicy, double? alignment, Duration? duration, Curve? curve}) => void from Function 'defaultTraversalRequestFocusCallback': static. (0x7e54fb0b7d88)
    // 0x6b7498: StoreField: r1->field_7 = r0
    //     0x6b7498: stur            w0, [x1, #7]
    // 0x6b749c: mov             x0, x1
    // 0x6b74a0: stur            x0, [fp, #-0x18]
    // 0x6b74a4: r16 = <FocusNode?, _FocusTraversalGroupInfo>
    //     0x6b74a4: ldr             x16, [PP, #0x74a8]  ; [pp+0x74a8] TypeArguments: <FocusNode?, _FocusTraversalGroupInfo>
    // 0x6b74a8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x6b74ac: stp             lr, x16, [SP]
    // 0x6b74b0: r0 = Map._fromLiteral()
    //     0x6b74b0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x6b74b4: ldur            x1, [fp, #-8]
    // 0x6b74b8: stur            x0, [fp, #-8]
    // 0x6b74bc: r0 = _getDescendantsWithoutExpandingScope()
    //     0x6b74bc: bl              #0x6b7bf4  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_getDescendantsWithoutExpandingScope
    // 0x6b74c0: mov             x3, x0
    // 0x6b74c4: stur            x3, [fp, #-0x40]
    // 0x6b74c8: LoadField: r4 = r3->field_7
    //     0x6b74c8: ldur            w4, [x3, #7]
    // 0x6b74cc: DecompressPointer r4
    //     0x6b74cc: add             x4, x4, HEAP, lsl #32
    // 0x6b74d0: stur            x4, [fp, #-0x38]
    // 0x6b74d4: LoadField: r0 = r3->field_b
    //     0x6b74d4: ldur            w0, [x3, #0xb]
    // 0x6b74d8: r5 = LoadInt32Instr(r0)
    //     0x6b74d8: sbfx            x5, x0, #1, #0x1f
    // 0x6b74dc: stur            x5, [fp, #-0x30]
    // 0x6b74e0: r0 = 0
    //     0x6b74e0: movz            x0, #0
    // 0x6b74e4: ldur            x7, [fp, #-0x10]
    // 0x6b74e8: ldur            x6, [fp, #-8]
    // 0x6b74ec: CheckStackOverflow
    //     0x6b74ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b74f0: cmp             SP, x16
    //     0x6b74f4: b.ls            #0x6b7aa8
    // 0x6b74f8: LoadField: r1 = r3->field_b
    //     0x6b74f8: ldur            w1, [x3, #0xb]
    // 0x6b74fc: r2 = LoadInt32Instr(r1)
    //     0x6b74fc: sbfx            x2, x1, #1, #0x1f
    // 0x6b7500: cmp             x5, x2
    // 0x6b7504: b.ne            #0x6b7a80
    // 0x6b7508: cmp             x0, x2
    // 0x6b750c: b.ge            #0x6b7a70
    // 0x6b7510: LoadField: r1 = r3->field_f
    //     0x6b7510: ldur            w1, [x3, #0xf]
    // 0x6b7514: DecompressPointer r1
    //     0x6b7514: add             x1, x1, HEAP, lsl #32
    // 0x6b7518: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0x6b7518: add             x16, x1, x0, lsl #2
    //     0x6b751c: ldur            w8, [x16, #0xf]
    // 0x6b7520: DecompressPointer r8
    //     0x6b7520: add             x8, x8, HEAP, lsl #32
    // 0x6b7524: stur            x8, [fp, #-0x28]
    // 0x6b7528: add             x9, x0, #1
    // 0x6b752c: stur            x9, [fp, #-0x20]
    // 0x6b7530: cmp             w8, NULL
    // 0x6b7534: b.ne            #0x6b7564
    // 0x6b7538: mov             x0, x8
    // 0x6b753c: mov             x2, x4
    // 0x6b7540: r1 = Null
    //     0x6b7540: mov             x1, NULL
    // 0x6b7544: cmp             w2, NULL
    // 0x6b7548: b.eq            #0x6b7564
    // 0x6b754c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6b754c: ldur            w4, [x2, #0x17]
    // 0x6b7550: DecompressPointer r4
    //     0x6b7550: add             x4, x4, HEAP, lsl #32
    // 0x6b7554: r8 = X0
    //     0x6b7554: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6b7558: LoadField: r9 = r4->field_7
    //     0x6b7558: ldur            x9, [x4, #7]
    // 0x6b755c: r3 = Null
    //     0x6b755c: ldr             x3, [PP, #0x74b0]  ; [pp+0x74b0] Null
    // 0x6b7560: blr             x9
    // 0x6b7564: ldur            x0, [fp, #-0x28]
    // 0x6b7568: CheckStackOverflow
    //     0x6b7568: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b756c: cmp             SP, x16
    //     0x6b7570: b.ls            #0x6b7ab0
    // 0x6b7574: LoadField: r1 = r0->field_4f
    //     0x6b7574: ldur            w1, [x0, #0x4f]
    // 0x6b7578: DecompressPointer r1
    //     0x6b7578: add             x1, x1, HEAP, lsl #32
    // 0x6b757c: cmp             w1, NULL
    // 0x6b7580: b.eq            #0x6b75c4
    // 0x6b7584: LoadField: r2 = r0->field_33
    //     0x6b7584: ldur            w2, [x0, #0x33]
    // 0x6b7588: DecompressPointer r2
    //     0x6b7588: add             x2, x2, HEAP, lsl #32
    // 0x6b758c: cmp             w2, NULL
    // 0x6b7590: b.eq            #0x6b75bc
    // 0x6b7594: r2 = 60
    //     0x6b7594: movz            x2, #0x3c
    // 0x6b7598: branchIfSmi(r0, 0x6b75a4)
    //     0x6b7598: tbz             w0, #0, #0x6b75a4
    // 0x6b759c: r2 = LoadClassIdInstr(r0)
    //     0x6b759c: ldur            x2, [x0, #-1]
    //     0x6b75a0: ubfx            x2, x2, #0xc, #0x14
    // 0x6b75a4: cmp             x2, #0xb68
    // 0x6b75a8: b.eq            #0x6b75b4
    // 0x6b75ac: mov             x0, x1
    // 0x6b75b0: b               #0x6b7568
    // 0x6b75b4: mov             x3, x0
    // 0x6b75b8: b               #0x6b75c8
    // 0x6b75bc: r3 = Null
    //     0x6b75bc: mov             x3, NULL
    // 0x6b75c0: b               #0x6b75c8
    // 0x6b75c4: r3 = Null
    //     0x6b75c4: mov             x3, NULL
    // 0x6b75c8: ldur            x0, [fp, #-0x28]
    // 0x6b75cc: stur            x3, [fp, #-0x50]
    // 0x6b75d0: cmp             w0, w3
    // 0x6b75d4: b.ne            #0x6b77fc
    // 0x6b75d8: cmp             w3, NULL
    // 0x6b75dc: b.eq            #0x6b7ab8
    // 0x6b75e0: LoadField: r0 = r3->field_4f
    //     0x6b75e0: ldur            w0, [x3, #0x4f]
    // 0x6b75e4: DecompressPointer r0
    //     0x6b75e4: add             x0, x0, HEAP, lsl #32
    // 0x6b75e8: cmp             w0, NULL
    // 0x6b75ec: b.eq            #0x6b7abc
    // 0x6b75f0: CheckStackOverflow
    //     0x6b75f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b75f4: cmp             SP, x16
    //     0x6b75f8: b.ls            #0x6b7ac0
    // 0x6b75fc: LoadField: r1 = r0->field_4f
    //     0x6b75fc: ldur            w1, [x0, #0x4f]
    // 0x6b7600: DecompressPointer r1
    //     0x6b7600: add             x1, x1, HEAP, lsl #32
    // 0x6b7604: cmp             w1, NULL
    // 0x6b7608: b.eq            #0x6b7644
    // 0x6b760c: LoadField: r2 = r0->field_33
    //     0x6b760c: ldur            w2, [x0, #0x33]
    // 0x6b7610: DecompressPointer r2
    //     0x6b7610: add             x2, x2, HEAP, lsl #32
    // 0x6b7614: cmp             w2, NULL
    // 0x6b7618: b.eq            #0x6b763c
    // 0x6b761c: r2 = LoadClassIdInstr(r0)
    //     0x6b761c: ldur            x2, [x0, #-1]
    //     0x6b7620: ubfx            x2, x2, #0xc, #0x14
    // 0x6b7624: cmp             x2, #0xb68
    // 0x6b7628: b.eq            #0x6b7634
    // 0x6b762c: mov             x0, x1
    // 0x6b7630: b               #0x6b75f0
    // 0x6b7634: mov             x5, x0
    // 0x6b7638: b               #0x6b7648
    // 0x6b763c: r5 = Null
    //     0x6b763c: mov             x5, NULL
    // 0x6b7640: b               #0x6b7648
    // 0x6b7644: r5 = Null
    //     0x6b7644: mov             x5, NULL
    // 0x6b7648: ldur            x4, [fp, #-8]
    // 0x6b764c: stur            x5, [fp, #-0x48]
    // 0x6b7650: r0 = LoadClassIdInstr(r4)
    //     0x6b7650: ldur            x0, [x4, #-1]
    //     0x6b7654: ubfx            x0, x0, #0xc, #0x14
    // 0x6b7658: mov             x1, x4
    // 0x6b765c: mov             x2, x5
    // 0x6b7660: r0 = GDT[cid_x0 + -0x114]()
    //     0x6b7660: sub             lr, x0, #0x114
    //     0x6b7664: ldr             lr, [x21, lr, lsl #3]
    //     0x6b7668: blr             lr
    // 0x6b766c: cmp             w0, NULL
    // 0x6b7670: b.ne            #0x6b771c
    // 0x6b7674: ldur            x2, [fp, #-0x48]
    // 0x6b7678: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x6b7678: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6b767c: ldr             x0, [x0]
    //     0x6b7680: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6b7684: cmp             w0, w16
    //     0x6b7688: b.ne            #0x6b7694
    //     0x6b768c: ldr             x2, [PP, #0x528]  ; [pp+0x528] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x6b7690: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6b7694: r1 = <FocusNode>
    //     0x6b7694: ldr             x1, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x6b7698: stur            x0, [fp, #-0x58]
    // 0x6b769c: r0 = AllocateGrowableArray()
    //     0x6b769c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x6b76a0: mov             x1, x0
    // 0x6b76a4: ldur            x0, [fp, #-0x58]
    // 0x6b76a8: stur            x1, [fp, #-0x60]
    // 0x6b76ac: StoreField: r1->field_f = r0
    //     0x6b76ac: stur            w0, [x1, #0xf]
    // 0x6b76b0: StoreField: r1->field_b = rZR
    //     0x6b76b0: stur            wzr, [x1, #0xb]
    // 0x6b76b4: ldur            x2, [fp, #-0x48]
    // 0x6b76b8: cmp             w2, NULL
    // 0x6b76bc: b.ne            #0x6b76c8
    // 0x6b76c0: r0 = Null
    //     0x6b76c0: mov             x0, NULL
    // 0x6b76c4: b               #0x6b76d0
    // 0x6b76c8: LoadField: r0 = r2->field_67
    //     0x6b76c8: ldur            w0, [x2, #0x67]
    // 0x6b76cc: DecompressPointer r0
    //     0x6b76cc: add             x0, x0, HEAP, lsl #32
    // 0x6b76d0: cmp             w0, NULL
    // 0x6b76d4: b.ne            #0x6b76dc
    // 0x6b76d8: ldur            x0, [fp, #-0x18]
    // 0x6b76dc: stur            x0, [fp, #-0x58]
    // 0x6b76e0: r0 = _FocusTraversalGroupInfo()
    //     0x6b76e0: bl              #0x6b7bc8  ; Allocate_FocusTraversalGroupInfoStub -> _FocusTraversalGroupInfo (size=0x10)
    // 0x6b76e4: mov             x3, x0
    // 0x6b76e8: ldur            x0, [fp, #-0x58]
    // 0x6b76ec: stur            x3, [fp, #-0x68]
    // 0x6b76f0: StoreField: r3->field_7 = r0
    //     0x6b76f0: stur            w0, [x3, #7]
    // 0x6b76f4: ldur            x0, [fp, #-0x60]
    // 0x6b76f8: StoreField: r3->field_b = r0
    //     0x6b76f8: stur            w0, [x3, #0xb]
    // 0x6b76fc: ldur            x1, [fp, #-8]
    // 0x6b7700: ldur            x2, [fp, #-0x48]
    // 0x6b7704: r0 = _hashCode()
    //     0x6b7704: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x6b7708: ldur            x1, [fp, #-8]
    // 0x6b770c: ldur            x2, [fp, #-0x48]
    // 0x6b7710: ldur            x3, [fp, #-0x68]
    // 0x6b7714: mov             x5, x0
    // 0x6b7718: r0 = _set()
    //     0x6b7718: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x6b771c: ldur            x3, [fp, #-8]
    // 0x6b7720: r0 = LoadClassIdInstr(r3)
    //     0x6b7720: ldur            x0, [x3, #-1]
    //     0x6b7724: ubfx            x0, x0, #0xc, #0x14
    // 0x6b7728: mov             x1, x3
    // 0x6b772c: ldur            x2, [fp, #-0x48]
    // 0x6b7730: r0 = GDT[cid_x0 + -0x114]()
    //     0x6b7730: sub             lr, x0, #0x114
    //     0x6b7734: ldr             lr, [x21, lr, lsl #3]
    //     0x6b7738: blr             lr
    // 0x6b773c: cmp             w0, NULL
    // 0x6b7740: b.eq            #0x6b7ac8
    // 0x6b7744: LoadField: r3 = r0->field_b
    //     0x6b7744: ldur            w3, [x0, #0xb]
    // 0x6b7748: DecompressPointer r3
    //     0x6b7748: add             x3, x3, HEAP, lsl #32
    // 0x6b774c: stur            x3, [fp, #-0x48]
    // 0x6b7750: LoadField: r2 = r3->field_7
    //     0x6b7750: ldur            w2, [x3, #7]
    // 0x6b7754: DecompressPointer r2
    //     0x6b7754: add             x2, x2, HEAP, lsl #32
    // 0x6b7758: ldur            x0, [fp, #-0x50]
    // 0x6b775c: r1 = Null
    //     0x6b775c: mov             x1, NULL
    // 0x6b7760: cmp             w2, NULL
    // 0x6b7764: b.eq            #0x6b7780
    // 0x6b7768: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6b7768: ldur            w4, [x2, #0x17]
    // 0x6b776c: DecompressPointer r4
    //     0x6b776c: add             x4, x4, HEAP, lsl #32
    // 0x6b7770: r8 = X0
    //     0x6b7770: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6b7774: LoadField: r9 = r4->field_7
    //     0x6b7774: ldur            x9, [x4, #7]
    // 0x6b7778: r3 = Null
    //     0x6b7778: ldr             x3, [PP, #0x74c0]  ; [pp+0x74c0] Null
    // 0x6b777c: blr             x9
    // 0x6b7780: ldur            x0, [fp, #-0x48]
    // 0x6b7784: LoadField: r1 = r0->field_b
    //     0x6b7784: ldur            w1, [x0, #0xb]
    // 0x6b7788: LoadField: r2 = r0->field_f
    //     0x6b7788: ldur            w2, [x0, #0xf]
    // 0x6b778c: DecompressPointer r2
    //     0x6b778c: add             x2, x2, HEAP, lsl #32
    // 0x6b7790: LoadField: r3 = r2->field_b
    //     0x6b7790: ldur            w3, [x2, #0xb]
    // 0x6b7794: r2 = LoadInt32Instr(r1)
    //     0x6b7794: sbfx            x2, x1, #1, #0x1f
    // 0x6b7798: stur            x2, [fp, #-0x70]
    // 0x6b779c: r1 = LoadInt32Instr(r3)
    //     0x6b779c: sbfx            x1, x3, #1, #0x1f
    // 0x6b77a0: cmp             x2, x1
    // 0x6b77a4: b.ne            #0x6b77b0
    // 0x6b77a8: mov             x1, x0
    // 0x6b77ac: r0 = _growToNextCapacity()
    //     0x6b77ac: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b77b0: ldur            x0, [fp, #-0x48]
    // 0x6b77b4: ldur            x2, [fp, #-0x70]
    // 0x6b77b8: add             x1, x2, #1
    // 0x6b77bc: lsl             x3, x1, #1
    // 0x6b77c0: StoreField: r0->field_b = r3
    //     0x6b77c0: stur            w3, [x0, #0xb]
    // 0x6b77c4: LoadField: r1 = r0->field_f
    //     0x6b77c4: ldur            w1, [x0, #0xf]
    // 0x6b77c8: DecompressPointer r1
    //     0x6b77c8: add             x1, x1, HEAP, lsl #32
    // 0x6b77cc: ldur            x0, [fp, #-0x50]
    // 0x6b77d0: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6b77d0: add             x25, x1, x2, lsl #2
    //     0x6b77d4: add             x25, x25, #0xf
    //     0x6b77d8: str             w0, [x25]
    //     0x6b77dc: tbz             w0, #0, #0x6b77f8
    //     0x6b77e0: ldurb           w16, [x1, #-1]
    //     0x6b77e4: ldurb           w17, [x0, #-1]
    //     0x6b77e8: and             x16, x17, x16, lsr #2
    //     0x6b77ec: tst             x16, HEAP, lsr #32
    //     0x6b77f0: b.eq            #0x6b77f8
    //     0x6b77f4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6b77f8: b               #0x6b7a5c
    // 0x6b77fc: ldur            x2, [fp, #-0x10]
    // 0x6b7800: cmp             w0, w2
    // 0x6b7804: b.eq            #0x6b78dc
    // 0x6b7808: LoadField: r1 = r0->field_27
    //     0x6b7808: ldur            w1, [x0, #0x27]
    // 0x6b780c: DecompressPointer r1
    //     0x6b780c: add             x1, x1, HEAP, lsl #32
    // 0x6b7810: tbnz            w1, #4, #0x6b7a5c
    // 0x6b7814: mov             x1, x0
    // 0x6b7818: r0 = ancestors()
    //     0x6b7818: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x6b781c: mov             x1, x0
    // 0x6b7820: r2 = Closure: (FocusNode) => bool from Function '_allowDescendantsToBeFocused@279042876': static.
    //     0x6b7820: ldr             x2, [PP, #0x4e18]  ; [pp+0x4e18] Closure: (FocusNode) => bool from Function '_allowDescendantsToBeFocused@279042876': static. (0x7e54fb0a8984)
    // 0x6b7824: r0 = every()
    //     0x6b7824: bl              #0x6a87c8  ; [dart:collection] ListBase::every
    // 0x6b7828: tbnz            w0, #4, #0x6b7a5c
    // 0x6b782c: ldur            x0, [fp, #-0x28]
    // 0x6b7830: LoadField: r1 = r0->field_23
    //     0x6b7830: ldur            w1, [x0, #0x23]
    // 0x6b7834: DecompressPointer r1
    //     0x6b7834: add             x1, x1, HEAP, lsl #32
    // 0x6b7838: tbz             w1, #4, #0x6b7a5c
    // 0x6b783c: mov             x1, x0
    // 0x6b7840: r0 = ancestors()
    //     0x6b7840: bl              #0x65105c  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::ancestors
    // 0x6b7844: LoadField: r3 = r0->field_7
    //     0x6b7844: ldur            w3, [x0, #7]
    // 0x6b7848: DecompressPointer r3
    //     0x6b7848: add             x3, x3, HEAP, lsl #32
    // 0x6b784c: stur            x3, [fp, #-0x58]
    // 0x6b7850: LoadField: r1 = r0->field_b
    //     0x6b7850: ldur            w1, [x0, #0xb]
    // 0x6b7854: r4 = LoadInt32Instr(r1)
    //     0x6b7854: sbfx            x4, x1, #1, #0x1f
    // 0x6b7858: stur            x4, [fp, #-0x78]
    // 0x6b785c: LoadField: r5 = r0->field_f
    //     0x6b785c: ldur            w5, [x0, #0xf]
    // 0x6b7860: DecompressPointer r5
    //     0x6b7860: add             x5, x5, HEAP, lsl #32
    // 0x6b7864: stur            x5, [fp, #-0x48]
    // 0x6b7868: r0 = 0
    //     0x6b7868: movz            x0, #0
    // 0x6b786c: CheckStackOverflow
    //     0x6b786c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b7870: cmp             SP, x16
    //     0x6b7874: b.ls            #0x6b7acc
    // 0x6b7878: cmp             x0, x4
    // 0x6b787c: b.ge            #0x6b78dc
    // 0x6b7880: ArrayLoad: r1 = r5[r0]  ; Unknown_4
    //     0x6b7880: add             x16, x5, x0, lsl #2
    //     0x6b7884: ldur            w1, [x16, #0xf]
    // 0x6b7888: DecompressPointer r1
    //     0x6b7888: add             x1, x1, HEAP, lsl #32
    // 0x6b788c: add             x6, x0, #1
    // 0x6b7890: stur            x6, [fp, #-0x70]
    // 0x6b7894: cmp             w1, NULL
    // 0x6b7898: b.ne            #0x6b78c8
    // 0x6b789c: mov             x0, x1
    // 0x6b78a0: mov             x2, x3
    // 0x6b78a4: r1 = Null
    //     0x6b78a4: mov             x1, NULL
    // 0x6b78a8: cmp             w2, NULL
    // 0x6b78ac: b.eq            #0x6b78c8
    // 0x6b78b0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6b78b0: ldur            w4, [x2, #0x17]
    // 0x6b78b4: DecompressPointer r4
    //     0x6b78b4: add             x4, x4, HEAP, lsl #32
    // 0x6b78b8: r8 = X0
    //     0x6b78b8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6b78bc: LoadField: r9 = r4->field_7
    //     0x6b78bc: ldur            x9, [x4, #7]
    // 0x6b78c0: r3 = Null
    //     0x6b78c0: ldr             x3, [PP, #0x74d0]  ; [pp+0x74d0] Null
    // 0x6b78c4: blr             x9
    // 0x6b78c8: ldur            x0, [fp, #-0x70]
    // 0x6b78cc: ldur            x3, [fp, #-0x58]
    // 0x6b78d0: ldur            x5, [fp, #-0x48]
    // 0x6b78d4: ldur            x4, [fp, #-0x78]
    // 0x6b78d8: b               #0x6b786c
    // 0x6b78dc: ldur            x3, [fp, #-8]
    // 0x6b78e0: r0 = LoadClassIdInstr(r3)
    //     0x6b78e0: ldur            x0, [x3, #-1]
    //     0x6b78e4: ubfx            x0, x0, #0xc, #0x14
    // 0x6b78e8: mov             x1, x3
    // 0x6b78ec: ldur            x2, [fp, #-0x50]
    // 0x6b78f0: r0 = GDT[cid_x0 + -0x114]()
    //     0x6b78f0: sub             lr, x0, #0x114
    //     0x6b78f4: ldr             lr, [x21, lr, lsl #3]
    //     0x6b78f8: blr             lr
    // 0x6b78fc: cmp             w0, NULL
    // 0x6b7900: b.ne            #0x6b79b4
    // 0x6b7904: ldur            x2, [fp, #-0x50]
    // 0x6b7908: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x6b7908: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6b790c: ldr             x0, [x0]
    //     0x6b7910: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6b7914: cmp             w0, w16
    //     0x6b7918: b.ne            #0x6b7924
    //     0x6b791c: ldr             x2, [PP, #0x528]  ; [pp+0x528] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x6b7920: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6b7924: r1 = <FocusNode>
    //     0x6b7924: ldr             x1, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x6b7928: stur            x0, [fp, #-0x48]
    // 0x6b792c: r0 = AllocateGrowableArray()
    //     0x6b792c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x6b7930: mov             x1, x0
    // 0x6b7934: ldur            x0, [fp, #-0x48]
    // 0x6b7938: stur            x1, [fp, #-0x58]
    // 0x6b793c: StoreField: r1->field_f = r0
    //     0x6b793c: stur            w0, [x1, #0xf]
    // 0x6b7940: StoreField: r1->field_b = rZR
    //     0x6b7940: stur            wzr, [x1, #0xb]
    // 0x6b7944: ldur            x2, [fp, #-0x50]
    // 0x6b7948: cmp             w2, NULL
    // 0x6b794c: b.ne            #0x6b7958
    // 0x6b7950: r3 = Null
    //     0x6b7950: mov             x3, NULL
    // 0x6b7954: b               #0x6b7960
    // 0x6b7958: LoadField: r3 = r2->field_67
    //     0x6b7958: ldur            w3, [x2, #0x67]
    // 0x6b795c: DecompressPointer r3
    //     0x6b795c: add             x3, x3, HEAP, lsl #32
    // 0x6b7960: cmp             w3, NULL
    // 0x6b7964: b.ne            #0x6b7970
    // 0x6b7968: ldur            x0, [fp, #-0x18]
    // 0x6b796c: b               #0x6b7974
    // 0x6b7970: mov             x0, x3
    // 0x6b7974: stur            x0, [fp, #-0x48]
    // 0x6b7978: r0 = _FocusTraversalGroupInfo()
    //     0x6b7978: bl              #0x6b7bc8  ; Allocate_FocusTraversalGroupInfoStub -> _FocusTraversalGroupInfo (size=0x10)
    // 0x6b797c: mov             x3, x0
    // 0x6b7980: ldur            x0, [fp, #-0x48]
    // 0x6b7984: stur            x3, [fp, #-0x60]
    // 0x6b7988: StoreField: r3->field_7 = r0
    //     0x6b7988: stur            w0, [x3, #7]
    // 0x6b798c: ldur            x0, [fp, #-0x58]
    // 0x6b7990: StoreField: r3->field_b = r0
    //     0x6b7990: stur            w0, [x3, #0xb]
    // 0x6b7994: ldur            x1, [fp, #-8]
    // 0x6b7998: ldur            x2, [fp, #-0x50]
    // 0x6b799c: r0 = _hashCode()
    //     0x6b799c: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x6b79a0: ldur            x1, [fp, #-8]
    // 0x6b79a4: ldur            x2, [fp, #-0x50]
    // 0x6b79a8: ldur            x3, [fp, #-0x60]
    // 0x6b79ac: mov             x5, x0
    // 0x6b79b0: r0 = _set()
    //     0x6b79b0: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x6b79b4: ldur            x3, [fp, #-8]
    // 0x6b79b8: r0 = LoadClassIdInstr(r3)
    //     0x6b79b8: ldur            x0, [x3, #-1]
    //     0x6b79bc: ubfx            x0, x0, #0xc, #0x14
    // 0x6b79c0: mov             x1, x3
    // 0x6b79c4: ldur            x2, [fp, #-0x50]
    // 0x6b79c8: r0 = GDT[cid_x0 + -0x114]()
    //     0x6b79c8: sub             lr, x0, #0x114
    //     0x6b79cc: ldr             lr, [x21, lr, lsl #3]
    //     0x6b79d0: blr             lr
    // 0x6b79d4: cmp             w0, NULL
    // 0x6b79d8: b.eq            #0x6b7ad4
    // 0x6b79dc: LoadField: r2 = r0->field_b
    //     0x6b79dc: ldur            w2, [x0, #0xb]
    // 0x6b79e0: DecompressPointer r2
    //     0x6b79e0: add             x2, x2, HEAP, lsl #32
    // 0x6b79e4: stur            x2, [fp, #-0x48]
    // 0x6b79e8: LoadField: r0 = r2->field_b
    //     0x6b79e8: ldur            w0, [x2, #0xb]
    // 0x6b79ec: LoadField: r1 = r2->field_f
    //     0x6b79ec: ldur            w1, [x2, #0xf]
    // 0x6b79f0: DecompressPointer r1
    //     0x6b79f0: add             x1, x1, HEAP, lsl #32
    // 0x6b79f4: LoadField: r3 = r1->field_b
    //     0x6b79f4: ldur            w3, [x1, #0xb]
    // 0x6b79f8: r4 = LoadInt32Instr(r0)
    //     0x6b79f8: sbfx            x4, x0, #1, #0x1f
    // 0x6b79fc: stur            x4, [fp, #-0x70]
    // 0x6b7a00: r0 = LoadInt32Instr(r3)
    //     0x6b7a00: sbfx            x0, x3, #1, #0x1f
    // 0x6b7a04: cmp             x4, x0
    // 0x6b7a08: b.ne            #0x6b7a14
    // 0x6b7a0c: mov             x1, x2
    // 0x6b7a10: r0 = _growToNextCapacity()
    //     0x6b7a10: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b7a14: ldur            x0, [fp, #-0x48]
    // 0x6b7a18: ldur            x2, [fp, #-0x70]
    // 0x6b7a1c: add             x1, x2, #1
    // 0x6b7a20: lsl             x3, x1, #1
    // 0x6b7a24: StoreField: r0->field_b = r3
    //     0x6b7a24: stur            w3, [x0, #0xb]
    // 0x6b7a28: LoadField: r1 = r0->field_f
    //     0x6b7a28: ldur            w1, [x0, #0xf]
    // 0x6b7a2c: DecompressPointer r1
    //     0x6b7a2c: add             x1, x1, HEAP, lsl #32
    // 0x6b7a30: ldur            x0, [fp, #-0x28]
    // 0x6b7a34: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6b7a34: add             x25, x1, x2, lsl #2
    //     0x6b7a38: add             x25, x25, #0xf
    //     0x6b7a3c: str             w0, [x25]
    //     0x6b7a40: tbz             w0, #0, #0x6b7a5c
    //     0x6b7a44: ldurb           w16, [x1, #-1]
    //     0x6b7a48: ldurb           w17, [x0, #-1]
    //     0x6b7a4c: and             x16, x17, x16, lsr #2
    //     0x6b7a50: tst             x16, HEAP, lsr #32
    //     0x6b7a54: b.eq            #0x6b7a5c
    //     0x6b7a58: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6b7a5c: ldur            x0, [fp, #-0x20]
    // 0x6b7a60: ldur            x3, [fp, #-0x40]
    // 0x6b7a64: ldur            x4, [fp, #-0x38]
    // 0x6b7a68: ldur            x5, [fp, #-0x30]
    // 0x6b7a6c: b               #0x6b74e4
    // 0x6b7a70: ldur            x0, [fp, #-8]
    // 0x6b7a74: LeaveFrame
    //     0x6b7a74: mov             SP, fp
    //     0x6b7a78: ldp             fp, lr, [SP], #0x10
    // 0x6b7a7c: ret
    //     0x6b7a7c: ret             
    // 0x6b7a80: mov             x0, x3
    // 0x6b7a84: r0 = ConcurrentModificationError()
    //     0x6b7a84: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6b7a88: mov             x1, x0
    // 0x6b7a8c: ldur            x0, [fp, #-0x40]
    // 0x6b7a90: StoreField: r1->field_b = r0
    //     0x6b7a90: stur            w0, [x1, #0xb]
    // 0x6b7a94: mov             x0, x1
    // 0x6b7a98: r0 = Throw()
    //     0x6b7a98: bl              #0xec04b8  ; ThrowStub
    // 0x6b7a9c: brk             #0
    // 0x6b7aa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b7aa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b7aa4: b               #0x6b7450
    // 0x6b7aa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b7aa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b7aac: b               #0x6b74f8
    // 0x6b7ab0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b7ab0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b7ab4: b               #0x6b7574
    // 0x6b7ab8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b7ab8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b7abc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b7abc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b7ac0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b7ac0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b7ac4: b               #0x6b75fc
    // 0x6b7ac8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b7ac8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b7acc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b7acc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b7ad0: b               #0x6b7878
    // 0x6b7ad4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b7ad4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _getDescendantsWithoutExpandingScope(/* No info */) {
    // ** addr: 0x6b7bf4, size: 0x188
    // 0x6b7bf4: EnterFrame
    //     0x6b7bf4: stp             fp, lr, [SP, #-0x10]!
    //     0x6b7bf8: mov             fp, SP
    // 0x6b7bfc: AllocStack(0x30)
    //     0x6b7bfc: sub             SP, SP, #0x30
    // 0x6b7c00: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x6b7c00: mov             x0, x1
    //     0x6b7c04: stur            x1, [fp, #-8]
    // 0x6b7c08: CheckStackOverflow
    //     0x6b7c08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b7c0c: cmp             SP, x16
    //     0x6b7c10: b.ls            #0x6b7d6c
    // 0x6b7c14: r1 = <FocusNode>
    //     0x6b7c14: ldr             x1, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x6b7c18: r2 = 0
    //     0x6b7c18: movz            x2, #0
    // 0x6b7c1c: r0 = _GrowableList()
    //     0x6b7c1c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b7c20: mov             x2, x0
    // 0x6b7c24: ldur            x0, [fp, #-8]
    // 0x6b7c28: stur            x2, [fp, #-0x30]
    // 0x6b7c2c: LoadField: r3 = r0->field_53
    //     0x6b7c2c: ldur            w3, [x0, #0x53]
    // 0x6b7c30: DecompressPointer r3
    //     0x6b7c30: add             x3, x3, HEAP, lsl #32
    // 0x6b7c34: stur            x3, [fp, #-0x28]
    // 0x6b7c38: LoadField: r0 = r3->field_b
    //     0x6b7c38: ldur            w0, [x3, #0xb]
    // 0x6b7c3c: r4 = LoadInt32Instr(r0)
    //     0x6b7c3c: sbfx            x4, x0, #1, #0x1f
    // 0x6b7c40: stur            x4, [fp, #-0x20]
    // 0x6b7c44: r0 = 0
    //     0x6b7c44: movz            x0, #0
    // 0x6b7c48: CheckStackOverflow
    //     0x6b7c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b7c4c: cmp             SP, x16
    //     0x6b7c50: b.ls            #0x6b7d74
    // 0x6b7c54: LoadField: r1 = r3->field_b
    //     0x6b7c54: ldur            w1, [x3, #0xb]
    // 0x6b7c58: r5 = LoadInt32Instr(r1)
    //     0x6b7c58: sbfx            x5, x1, #1, #0x1f
    // 0x6b7c5c: cmp             x4, x5
    // 0x6b7c60: b.ne            #0x6b7d4c
    // 0x6b7c64: cmp             x0, x5
    // 0x6b7c68: b.ge            #0x6b7d3c
    // 0x6b7c6c: LoadField: r1 = r3->field_f
    //     0x6b7c6c: ldur            w1, [x3, #0xf]
    // 0x6b7c70: DecompressPointer r1
    //     0x6b7c70: add             x1, x1, HEAP, lsl #32
    // 0x6b7c74: ArrayLoad: r5 = r1[r0]  ; Unknown_4
    //     0x6b7c74: add             x16, x1, x0, lsl #2
    //     0x6b7c78: ldur            w5, [x16, #0xf]
    // 0x6b7c7c: DecompressPointer r5
    //     0x6b7c7c: add             x5, x5, HEAP, lsl #32
    // 0x6b7c80: stur            x5, [fp, #-8]
    // 0x6b7c84: add             x6, x0, #1
    // 0x6b7c88: stur            x6, [fp, #-0x18]
    // 0x6b7c8c: LoadField: r0 = r2->field_b
    //     0x6b7c8c: ldur            w0, [x2, #0xb]
    // 0x6b7c90: LoadField: r1 = r2->field_f
    //     0x6b7c90: ldur            w1, [x2, #0xf]
    // 0x6b7c94: DecompressPointer r1
    //     0x6b7c94: add             x1, x1, HEAP, lsl #32
    // 0x6b7c98: LoadField: r7 = r1->field_b
    //     0x6b7c98: ldur            w7, [x1, #0xb]
    // 0x6b7c9c: r8 = LoadInt32Instr(r0)
    //     0x6b7c9c: sbfx            x8, x0, #1, #0x1f
    // 0x6b7ca0: stur            x8, [fp, #-0x10]
    // 0x6b7ca4: r0 = LoadInt32Instr(r7)
    //     0x6b7ca4: sbfx            x0, x7, #1, #0x1f
    // 0x6b7ca8: cmp             x8, x0
    // 0x6b7cac: b.ne            #0x6b7cb8
    // 0x6b7cb0: mov             x1, x2
    // 0x6b7cb4: r0 = _growToNextCapacity()
    //     0x6b7cb4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b7cb8: ldur            x2, [fp, #-0x30]
    // 0x6b7cbc: ldur            x4, [fp, #-0x10]
    // 0x6b7cc0: ldur            x3, [fp, #-8]
    // 0x6b7cc4: add             x0, x4, #1
    // 0x6b7cc8: lsl             x1, x0, #1
    // 0x6b7ccc: StoreField: r2->field_b = r1
    //     0x6b7ccc: stur            w1, [x2, #0xb]
    // 0x6b7cd0: LoadField: r1 = r2->field_f
    //     0x6b7cd0: ldur            w1, [x2, #0xf]
    // 0x6b7cd4: DecompressPointer r1
    //     0x6b7cd4: add             x1, x1, HEAP, lsl #32
    // 0x6b7cd8: mov             x0, x3
    // 0x6b7cdc: ArrayStore: r1[r4] = r0  ; List_4
    //     0x6b7cdc: add             x25, x1, x4, lsl #2
    //     0x6b7ce0: add             x25, x25, #0xf
    //     0x6b7ce4: str             w0, [x25]
    //     0x6b7ce8: tbz             w0, #0, #0x6b7d04
    //     0x6b7cec: ldurb           w16, [x1, #-1]
    //     0x6b7cf0: ldurb           w17, [x0, #-1]
    //     0x6b7cf4: and             x16, x17, x16, lsr #2
    //     0x6b7cf8: tst             x16, HEAP, lsr #32
    //     0x6b7cfc: b.eq            #0x6b7d04
    //     0x6b7d00: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6b7d04: r0 = LoadClassIdInstr(r3)
    //     0x6b7d04: ldur            x0, [x3, #-1]
    //     0x6b7d08: ubfx            x0, x0, #0xc, #0x14
    // 0x6b7d0c: cmp             x0, #0xb69
    // 0x6b7d10: b.eq            #0x6b7d28
    // 0x6b7d14: mov             x1, x3
    // 0x6b7d18: r0 = _getDescendantsWithoutExpandingScope()
    //     0x6b7d18: bl              #0x6b7bf4  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_getDescendantsWithoutExpandingScope
    // 0x6b7d1c: ldur            x1, [fp, #-0x30]
    // 0x6b7d20: mov             x2, x0
    // 0x6b7d24: r0 = addAll()
    //     0x6b7d24: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x6b7d28: ldur            x0, [fp, #-0x18]
    // 0x6b7d2c: ldur            x2, [fp, #-0x30]
    // 0x6b7d30: ldur            x3, [fp, #-0x28]
    // 0x6b7d34: ldur            x4, [fp, #-0x20]
    // 0x6b7d38: b               #0x6b7c48
    // 0x6b7d3c: ldur            x0, [fp, #-0x30]
    // 0x6b7d40: LeaveFrame
    //     0x6b7d40: mov             SP, fp
    //     0x6b7d44: ldp             fp, lr, [SP], #0x10
    // 0x6b7d48: ret
    //     0x6b7d48: ret             
    // 0x6b7d4c: mov             x0, x3
    // 0x6b7d50: r0 = ConcurrentModificationError()
    //     0x6b7d50: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6b7d54: mov             x1, x0
    // 0x6b7d58: ldur            x0, [fp, #-0x28]
    // 0x6b7d5c: StoreField: r1->field_b = r0
    //     0x6b7d5c: stur            w0, [x1, #0xb]
    // 0x6b7d60: mov             x0, x1
    // 0x6b7d64: r0 = Throw()
    //     0x6b7d64: bl              #0xec04b8  ; ThrowStub
    // 0x6b7d68: brk             #0
    // 0x6b7d6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b7d6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b7d70: b               #0x6b7c14
    // 0x6b7d74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b7d74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b7d78: b               #0x6b7c54
  }
  [closure] static void defaultTraversalRequestFocusCallback(dynamic, FocusNode, {ScrollPositionAlignmentPolicy? alignmentPolicy, double? alignment, Duration? duration, Curve? curve}) {
    // ** addr: 0x6b7d88, size: 0x184
    // 0x6b7d88: EnterFrame
    //     0x6b7d88: stp             fp, lr, [SP, #-0x10]!
    //     0x6b7d8c: mov             fp, SP
    // 0x6b7d90: AllocStack(0x20)
    //     0x6b7d90: sub             SP, SP, #0x20
    // 0x6b7d94: SetupParameters(dynamic _ /* r2 */, {dynamic alignment = Null /* r3 */, dynamic alignmentPolicy = Null /* r5 */, dynamic curve = Null /* r6 */, dynamic duration = Null /* r0 */})
    //     0x6b7d94: ldur            w0, [x4, #0x13]
    //     0x6b7d98: sub             x1, x0, #4
    //     0x6b7d9c: add             x2, fp, w1, sxtw #2
    //     0x6b7da0: ldr             x2, [x2, #0x10]
    //     0x6b7da4: ldur            w1, [x4, #0x1f]
    //     0x6b7da8: add             x1, x1, HEAP, lsl #32
    //     0x6b7dac: ldr             x16, [PP, #0x74e0]  ; [pp+0x74e0] "alignment"
    //     0x6b7db0: cmp             w1, w16
    //     0x6b7db4: b.ne            #0x6b7dd8
    //     0x6b7db8: ldur            w1, [x4, #0x23]
    //     0x6b7dbc: add             x1, x1, HEAP, lsl #32
    //     0x6b7dc0: sub             w3, w0, w1
    //     0x6b7dc4: add             x1, fp, w3, sxtw #2
    //     0x6b7dc8: ldr             x1, [x1, #8]
    //     0x6b7dcc: mov             x3, x1
    //     0x6b7dd0: movz            x1, #0x1
    //     0x6b7dd4: b               #0x6b7de0
    //     0x6b7dd8: mov             x3, NULL
    //     0x6b7ddc: movz            x1, #0
    //     0x6b7de0: lsl             x5, x1, #1
    //     0x6b7de4: lsl             w6, w5, #1
    //     0x6b7de8: add             w7, w6, #8
    //     0x6b7dec: add             x16, x4, w7, sxtw #1
    //     0x6b7df0: ldur            w8, [x16, #0xf]
    //     0x6b7df4: add             x8, x8, HEAP, lsl #32
    //     0x6b7df8: ldr             x16, [PP, #0x74e8]  ; [pp+0x74e8] "alignmentPolicy"
    //     0x6b7dfc: cmp             w8, w16
    //     0x6b7e00: b.ne            #0x6b7e34
    //     0x6b7e04: add             w1, w6, #0xa
    //     0x6b7e08: add             x16, x4, w1, sxtw #1
    //     0x6b7e0c: ldur            w6, [x16, #0xf]
    //     0x6b7e10: add             x6, x6, HEAP, lsl #32
    //     0x6b7e14: sub             w1, w0, w6
    //     0x6b7e18: add             x6, fp, w1, sxtw #2
    //     0x6b7e1c: ldr             x6, [x6, #8]
    //     0x6b7e20: add             w1, w5, #2
    //     0x6b7e24: sbfx            x5, x1, #1, #0x1f
    //     0x6b7e28: mov             x1, x5
    //     0x6b7e2c: mov             x5, x6
    //     0x6b7e30: b               #0x6b7e38
    //     0x6b7e34: mov             x5, NULL
    //     0x6b7e38: lsl             x6, x1, #1
    //     0x6b7e3c: lsl             w7, w6, #1
    //     0x6b7e40: add             w8, w7, #8
    //     0x6b7e44: add             x16, x4, w8, sxtw #1
    //     0x6b7e48: ldur            w9, [x16, #0xf]
    //     0x6b7e4c: add             x9, x9, HEAP, lsl #32
    //     0x6b7e50: ldr             x16, [PP, #0x4e58]  ; [pp+0x4e58] "curve"
    //     0x6b7e54: cmp             w9, w16
    //     0x6b7e58: b.ne            #0x6b7e8c
    //     0x6b7e5c: add             w1, w7, #0xa
    //     0x6b7e60: add             x16, x4, w1, sxtw #1
    //     0x6b7e64: ldur            w7, [x16, #0xf]
    //     0x6b7e68: add             x7, x7, HEAP, lsl #32
    //     0x6b7e6c: sub             w1, w0, w7
    //     0x6b7e70: add             x7, fp, w1, sxtw #2
    //     0x6b7e74: ldr             x7, [x7, #8]
    //     0x6b7e78: add             w1, w6, #2
    //     0x6b7e7c: sbfx            x6, x1, #1, #0x1f
    //     0x6b7e80: mov             x1, x6
    //     0x6b7e84: mov             x6, x7
    //     0x6b7e88: b               #0x6b7e90
    //     0x6b7e8c: mov             x6, NULL
    //     0x6b7e90: lsl             x7, x1, #1
    //     0x6b7e94: lsl             w1, w7, #1
    //     0x6b7e98: add             w7, w1, #8
    //     0x6b7e9c: add             x16, x4, w7, sxtw #1
    //     0x6b7ea0: ldur            w8, [x16, #0xf]
    //     0x6b7ea4: add             x8, x8, HEAP, lsl #32
    //     0x6b7ea8: ldr             x16, [PP, #0x4e68]  ; [pp+0x4e68] "duration"
    //     0x6b7eac: cmp             w8, w16
    //     0x6b7eb0: b.ne            #0x6b7ed4
    //     0x6b7eb4: add             w7, w1, #0xa
    //     0x6b7eb8: add             x16, x4, w7, sxtw #1
    //     0x6b7ebc: ldur            w1, [x16, #0xf]
    //     0x6b7ec0: add             x1, x1, HEAP, lsl #32
    //     0x6b7ec4: sub             w4, w0, w1
    //     0x6b7ec8: add             x0, fp, w4, sxtw #2
    //     0x6b7ecc: ldr             x0, [x0, #8]
    //     0x6b7ed0: b               #0x6b7ed8
    //     0x6b7ed4: mov             x0, NULL
    // 0x6b7ed8: CheckStackOverflow
    //     0x6b7ed8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b7edc: cmp             SP, x16
    //     0x6b7ee0: b.ls            #0x6b7f04
    // 0x6b7ee4: stp             x3, x5, [SP, #0x10]
    // 0x6b7ee8: stp             x6, x0, [SP]
    // 0x6b7eec: mov             x1, x2
    // 0x6b7ef0: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, alignmentPolicy, 0x1, curve, 0x4, duration, 0x3, null]
    //     0x6b7ef0: ldr             x4, [PP, #0x74f0]  ; [pp+0x74f0] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "alignmentPolicy", 0x1, "curve", 0x4, "duration", 0x3, Null]
    // 0x6b7ef4: r0 = defaultTraversalRequestFocusCallback()
    //     0x6b7ef4: bl              #0x6b7f0c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::defaultTraversalRequestFocusCallback
    // 0x6b7ef8: LeaveFrame
    //     0x6b7ef8: mov             SP, fp
    //     0x6b7efc: ldp             fp, lr, [SP], #0x10
    // 0x6b7f00: ret
    //     0x6b7f00: ret             
    // 0x6b7f04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b7f04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b7f08: b               #0x6b7ee4
  }
  static _ defaultTraversalRequestFocusCallback(/* No info */) {
    // ** addr: 0x6b7f0c, size: 0x25c
    // 0x6b7f0c: EnterFrame
    //     0x6b7f0c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b7f10: mov             fp, SP
    // 0x6b7f14: AllocStack(0x48)
    //     0x6b7f14: sub             SP, SP, #0x48
    // 0x6b7f18: SetupParameters(dynamic _ /* r1 => r0, fp-0x28 */, {dynamic alignment = Null /* r3, fp-0x20 */, dynamic alignmentPolicy = Null /* r5, fp-0x18 */, dynamic curve = Null /* r6, fp-0x10 */, dynamic duration = Null /* r2, fp-0x8 */})
    //     0x6b7f18: mov             x0, x1
    //     0x6b7f1c: stur            x1, [fp, #-0x28]
    //     0x6b7f20: ldur            w1, [x4, #0x13]
    //     0x6b7f24: ldur            w2, [x4, #0x1f]
    //     0x6b7f28: add             x2, x2, HEAP, lsl #32
    //     0x6b7f2c: ldr             x16, [PP, #0x74e0]  ; [pp+0x74e0] "alignment"
    //     0x6b7f30: cmp             w2, w16
    //     0x6b7f34: b.ne            #0x6b7f58
    //     0x6b7f38: ldur            w2, [x4, #0x23]
    //     0x6b7f3c: add             x2, x2, HEAP, lsl #32
    //     0x6b7f40: sub             w3, w1, w2
    //     0x6b7f44: add             x2, fp, w3, sxtw #2
    //     0x6b7f48: ldr             x2, [x2, #8]
    //     0x6b7f4c: mov             x3, x2
    //     0x6b7f50: movz            x2, #0x1
    //     0x6b7f54: b               #0x6b7f60
    //     0x6b7f58: mov             x3, NULL
    //     0x6b7f5c: movz            x2, #0
    //     0x6b7f60: stur            x3, [fp, #-0x20]
    //     0x6b7f64: lsl             x5, x2, #1
    //     0x6b7f68: lsl             w6, w5, #1
    //     0x6b7f6c: add             w7, w6, #8
    //     0x6b7f70: add             x16, x4, w7, sxtw #1
    //     0x6b7f74: ldur            w8, [x16, #0xf]
    //     0x6b7f78: add             x8, x8, HEAP, lsl #32
    //     0x6b7f7c: ldr             x16, [PP, #0x74e8]  ; [pp+0x74e8] "alignmentPolicy"
    //     0x6b7f80: cmp             w8, w16
    //     0x6b7f84: b.ne            #0x6b7fb8
    //     0x6b7f88: add             w2, w6, #0xa
    //     0x6b7f8c: add             x16, x4, w2, sxtw #1
    //     0x6b7f90: ldur            w6, [x16, #0xf]
    //     0x6b7f94: add             x6, x6, HEAP, lsl #32
    //     0x6b7f98: sub             w2, w1, w6
    //     0x6b7f9c: add             x6, fp, w2, sxtw #2
    //     0x6b7fa0: ldr             x6, [x6, #8]
    //     0x6b7fa4: add             w2, w5, #2
    //     0x6b7fa8: sbfx            x5, x2, #1, #0x1f
    //     0x6b7fac: mov             x2, x5
    //     0x6b7fb0: mov             x5, x6
    //     0x6b7fb4: b               #0x6b7fbc
    //     0x6b7fb8: mov             x5, NULL
    //     0x6b7fbc: stur            x5, [fp, #-0x18]
    //     0x6b7fc0: lsl             x6, x2, #1
    //     0x6b7fc4: lsl             w7, w6, #1
    //     0x6b7fc8: add             w8, w7, #8
    //     0x6b7fcc: add             x16, x4, w8, sxtw #1
    //     0x6b7fd0: ldur            w9, [x16, #0xf]
    //     0x6b7fd4: add             x9, x9, HEAP, lsl #32
    //     0x6b7fd8: ldr             x16, [PP, #0x4e58]  ; [pp+0x4e58] "curve"
    //     0x6b7fdc: cmp             w9, w16
    //     0x6b7fe0: b.ne            #0x6b8014
    //     0x6b7fe4: add             w2, w7, #0xa
    //     0x6b7fe8: add             x16, x4, w2, sxtw #1
    //     0x6b7fec: ldur            w7, [x16, #0xf]
    //     0x6b7ff0: add             x7, x7, HEAP, lsl #32
    //     0x6b7ff4: sub             w2, w1, w7
    //     0x6b7ff8: add             x7, fp, w2, sxtw #2
    //     0x6b7ffc: ldr             x7, [x7, #8]
    //     0x6b8000: add             w2, w6, #2
    //     0x6b8004: sbfx            x6, x2, #1, #0x1f
    //     0x6b8008: mov             x2, x6
    //     0x6b800c: mov             x6, x7
    //     0x6b8010: b               #0x6b8018
    //     0x6b8014: mov             x6, NULL
    //     0x6b8018: stur            x6, [fp, #-0x10]
    //     0x6b801c: lsl             x7, x2, #1
    //     0x6b8020: lsl             w2, w7, #1
    //     0x6b8024: add             w7, w2, #8
    //     0x6b8028: add             x16, x4, w7, sxtw #1
    //     0x6b802c: ldur            w8, [x16, #0xf]
    //     0x6b8030: add             x8, x8, HEAP, lsl #32
    //     0x6b8034: ldr             x16, [PP, #0x4e68]  ; [pp+0x4e68] "duration"
    //     0x6b8038: cmp             w8, w16
    //     0x6b803c: b.ne            #0x6b8064
    //     0x6b8040: add             w7, w2, #0xa
    //     0x6b8044: add             x16, x4, w7, sxtw #1
    //     0x6b8048: ldur            w2, [x16, #0xf]
    //     0x6b804c: add             x2, x2, HEAP, lsl #32
    //     0x6b8050: sub             w4, w1, w2
    //     0x6b8054: add             x1, fp, w4, sxtw #2
    //     0x6b8058: ldr             x1, [x1, #8]
    //     0x6b805c: mov             x2, x1
    //     0x6b8060: b               #0x6b8068
    //     0x6b8064: mov             x2, NULL
    //     0x6b8068: stur            x2, [fp, #-8]
    // 0x6b806c: CheckStackOverflow
    //     0x6b806c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b8070: cmp             SP, x16
    //     0x6b8074: b.ls            #0x6b8138
    // 0x6b8078: mov             x1, x0
    // 0x6b807c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b807c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b8080: r0 = requestFocus()
    //     0x6b8080: bl              #0x657140  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::requestFocus
    // 0x6b8084: ldur            x0, [fp, #-0x28]
    // 0x6b8088: LoadField: r1 = r0->field_33
    //     0x6b8088: ldur            w1, [x0, #0x33]
    // 0x6b808c: DecompressPointer r1
    //     0x6b808c: add             x1, x1, HEAP, lsl #32
    // 0x6b8090: cmp             w1, NULL
    // 0x6b8094: b.eq            #0x6b8140
    // 0x6b8098: ldur            x0, [fp, #-0x20]
    // 0x6b809c: cmp             w0, NULL
    // 0x6b80a0: b.ne            #0x6b80ac
    // 0x6b80a4: d0 = 1.000000
    //     0x6b80a4: fmov            d0, #1.00000000
    // 0x6b80a8: b               #0x6b80b0
    // 0x6b80ac: LoadField: d0 = r0->field_7
    //     0x6b80ac: ldur            d0, [x0, #7]
    // 0x6b80b0: ldur            x0, [fp, #-0x18]
    // 0x6b80b4: cmp             w0, NULL
    // 0x6b80b8: b.ne            #0x6b80c4
    // 0x6b80bc: r2 = Instance_ScrollPositionAlignmentPolicy
    //     0x6b80bc: ldr             x2, [PP, #0x74f8]  ; [pp+0x74f8] Obj!ScrollPositionAlignmentPolicy@e33b81
    // 0x6b80c0: b               #0x6b80c8
    // 0x6b80c4: mov             x2, x0
    // 0x6b80c8: ldur            x0, [fp, #-8]
    // 0x6b80cc: cmp             w0, NULL
    // 0x6b80d0: b.ne            #0x6b80dc
    // 0x6b80d4: r3 = Instance_Duration
    //     0x6b80d4: ldr             x3, [PP, #0x1d38]  ; [pp+0x1d38] Obj!Duration@e3a081
    // 0x6b80d8: b               #0x6b80e0
    // 0x6b80dc: mov             x3, x0
    // 0x6b80e0: ldur            x0, [fp, #-0x10]
    // 0x6b80e4: cmp             w0, NULL
    // 0x6b80e8: b.ne            #0x6b80f0
    // 0x6b80ec: r0 = Instance_Cubic
    //     0x6b80ec: ldr             x0, [PP, #0x5088]  ; [pp+0x5088] Obj!Cubic@e14d71
    // 0x6b80f0: r4 = inline_Allocate_Double()
    //     0x6b80f0: ldp             x4, x5, [THR, #0x50]  ; THR::top
    //     0x6b80f4: add             x4, x4, #0x10
    //     0x6b80f8: cmp             x5, x4
    //     0x6b80fc: b.ls            #0x6b8144
    //     0x6b8100: str             x4, [THR, #0x50]  ; THR::top
    //     0x6b8104: sub             x4, x4, #0xf
    //     0x6b8108: movz            x5, #0xe15c
    //     0x6b810c: movk            x5, #0x3, lsl #16
    //     0x6b8110: stur            x5, [x4, #-1]
    // 0x6b8114: StoreField: r4->field_7 = d0
    //     0x6b8114: stur            d0, [x4, #7]
    // 0x6b8118: stp             x2, x4, [SP, #0x10]
    // 0x6b811c: stp             x0, x3, [SP]
    // 0x6b8120: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x1, alignmentPolicy, 0x2, curve, 0x4, duration, 0x3, null]
    //     0x6b8120: ldr             x4, [PP, #0x7500]  ; [pp+0x7500] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x1, "alignmentPolicy", 0x2, "curve", 0x4, "duration", 0x3, Null]
    // 0x6b8124: r0 = ensureVisible()
    //     0x6b8124: bl              #0x6b8168  ; [package:flutter/src/widgets/scrollable.dart] Scrollable::ensureVisible
    // 0x6b8128: r0 = Null
    //     0x6b8128: mov             x0, NULL
    // 0x6b812c: LeaveFrame
    //     0x6b812c: mov             SP, fp
    //     0x6b8130: ldp             fp, lr, [SP], #0x10
    // 0x6b8134: ret
    //     0x6b8134: ret             
    // 0x6b8138: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b8138: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b813c: b               #0x6b8078
    // 0x6b8140: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b8140: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b8144: SaveReg d0
    //     0x6b8144: str             q0, [SP, #-0x10]!
    // 0x6b8148: stp             x2, x3, [SP, #-0x10]!
    // 0x6b814c: stp             x0, x1, [SP, #-0x10]!
    // 0x6b8150: r0 = AllocateDouble()
    //     0x6b8150: bl              #0xec2254  ; AllocateDoubleStub
    // 0x6b8154: mov             x4, x0
    // 0x6b8158: ldp             x0, x1, [SP], #0x10
    // 0x6b815c: ldp             x2, x3, [SP], #0x10
    // 0x6b8160: RestoreReg d0
    //     0x6b8160: ldr             q0, [SP], #0x10
    // 0x6b8164: b               #0x6b8114
  }
  [closure] static bool <anonymous closure>(dynamic, FocusNode) {
    // ** addr: 0x6b86f0, size: 0x5c
    // 0x6b86f0: EnterFrame
    //     0x6b86f0: stp             fp, lr, [SP, #-0x10]!
    //     0x6b86f4: mov             fp, SP
    // 0x6b86f8: ldr             x0, [fp, #0x18]
    // 0x6b86fc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b86fc: ldur            w1, [x0, #0x17]
    // 0x6b8700: DecompressPointer r1
    //     0x6b8700: add             x1, x1, HEAP, lsl #32
    // 0x6b8704: CheckStackOverflow
    //     0x6b8704: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b8708: cmp             SP, x16
    //     0x6b870c: b.ls            #0x6b8744
    // 0x6b8710: LoadField: r0 = r1->field_f
    //     0x6b8710: ldur            w0, [x1, #0xf]
    // 0x6b8714: DecompressPointer r0
    //     0x6b8714: add             x0, x0, HEAP, lsl #32
    // 0x6b8718: ldr             x1, [fp, #0x10]
    // 0x6b871c: cmp             w1, w0
    // 0x6b8720: b.eq            #0x6b8734
    // 0x6b8724: r0 = _canRequestTraversalFocus()
    //     0x6b8724: bl              #0x6b874c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_canRequestTraversalFocus
    // 0x6b8728: eor             x1, x0, #0x10
    // 0x6b872c: mov             x0, x1
    // 0x6b8730: b               #0x6b8738
    // 0x6b8734: r0 = false
    //     0x6b8734: add             x0, NULL, #0x30  ; false
    // 0x6b8738: LeaveFrame
    //     0x6b8738: mov             SP, fp
    //     0x6b873c: ldp             fp, lr, [SP], #0x10
    // 0x6b8740: ret
    //     0x6b8740: ret             
    // 0x6b8744: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b8744: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b8748: b               #0x6b8710
  }
  static _ _canRequestTraversalFocus(/* No info */) {
    // ** addr: 0x6b874c, size: 0x58
    // 0x6b874c: EnterFrame
    //     0x6b874c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b8750: mov             fp, SP
    // 0x6b8754: AllocStack(0x8)
    //     0x6b8754: sub             SP, SP, #8
    // 0x6b8758: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x6b8758: mov             x0, x1
    //     0x6b875c: stur            x1, [fp, #-8]
    // 0x6b8760: CheckStackOverflow
    //     0x6b8760: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b8764: cmp             SP, x16
    //     0x6b8768: b.ls            #0x6b879c
    // 0x6b876c: mov             x1, x0
    // 0x6b8770: r0 = canRequestFocus()
    //     0x6b8770: bl              #0x6a8938  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::canRequestFocus
    // 0x6b8774: tbnz            w0, #4, #0x6b878c
    // 0x6b8778: ldur            x1, [fp, #-8]
    // 0x6b877c: r0 = skipTraversal()
    //     0x6b877c: bl              #0x6b7ad8  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::skipTraversal
    // 0x6b8780: eor             x1, x0, #0x10
    // 0x6b8784: mov             x0, x1
    // 0x6b8788: b               #0x6b8790
    // 0x6b878c: r0 = false
    //     0x6b878c: add             x0, NULL, #0x30  ; false
    // 0x6b8790: LeaveFrame
    //     0x6b8790: mov             SP, fp
    //     0x6b8794: ldp             fp, lr, [SP], #0x10
    // 0x6b8798: ret
    //     0x6b8798: ret             
    // 0x6b879c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b879c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b87a0: b               #0x6b876c
  }
  [closure] static void visitGroups(dynamic, _FocusTraversalGroupInfo) {
    // ** addr: 0x6b87a4, size: 0x1fc
    // 0x6b87a4: EnterFrame
    //     0x6b87a4: stp             fp, lr, [SP, #-0x10]!
    //     0x6b87a8: mov             fp, SP
    // 0x6b87ac: AllocStack(0x58)
    //     0x6b87ac: sub             SP, SP, #0x58
    // 0x6b87b0: SetupParameters()
    //     0x6b87b0: ldr             x0, [fp, #0x18]
    //     0x6b87b4: ldur            w1, [x0, #0x17]
    //     0x6b87b8: add             x1, x1, HEAP, lsl #32
    // 0x6b87bc: CheckStackOverflow
    //     0x6b87bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b87c0: cmp             SP, x16
    //     0x6b87c4: b.ls            #0x6b898c
    // 0x6b87c8: ldr             x0, [fp, #0x10]
    // 0x6b87cc: LoadField: r3 = r0->field_b
    //     0x6b87cc: ldur            w3, [x0, #0xb]
    // 0x6b87d0: DecompressPointer r3
    //     0x6b87d0: add             x3, x3, HEAP, lsl #32
    // 0x6b87d4: stur            x3, [fp, #-0x40]
    // 0x6b87d8: LoadField: r0 = r3->field_b
    //     0x6b87d8: ldur            w0, [x3, #0xb]
    // 0x6b87dc: r4 = LoadInt32Instr(r0)
    //     0x6b87dc: sbfx            x4, x0, #1, #0x1f
    // 0x6b87e0: stur            x4, [fp, #-0x38]
    // 0x6b87e4: LoadField: r0 = r1->field_13
    //     0x6b87e4: ldur            w0, [x1, #0x13]
    // 0x6b87e8: DecompressPointer r0
    //     0x6b87e8: add             x0, x0, HEAP, lsl #32
    // 0x6b87ec: stur            x0, [fp, #-0x30]
    // 0x6b87f0: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x6b87f0: ldur            w5, [x1, #0x17]
    // 0x6b87f4: DecompressPointer r5
    //     0x6b87f4: add             x5, x5, HEAP, lsl #32
    // 0x6b87f8: stur            x5, [fp, #-0x28]
    // 0x6b87fc: LoadField: r6 = r1->field_1b
    //     0x6b87fc: ldur            w6, [x1, #0x1b]
    // 0x6b8800: DecompressPointer r6
    //     0x6b8800: add             x6, x6, HEAP, lsl #32
    // 0x6b8804: stur            x6, [fp, #-0x20]
    // 0x6b8808: r1 = 0
    //     0x6b8808: movz            x1, #0
    // 0x6b880c: CheckStackOverflow
    //     0x6b880c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b8810: cmp             SP, x16
    //     0x6b8814: b.ls            #0x6b8994
    // 0x6b8818: LoadField: r2 = r3->field_b
    //     0x6b8818: ldur            w2, [x3, #0xb]
    // 0x6b881c: r7 = LoadInt32Instr(r2)
    //     0x6b881c: sbfx            x7, x2, #1, #0x1f
    // 0x6b8820: cmp             x4, x7
    // 0x6b8824: b.ne            #0x6b896c
    // 0x6b8828: cmp             x1, x7
    // 0x6b882c: b.ge            #0x6b895c
    // 0x6b8830: LoadField: r2 = r3->field_f
    //     0x6b8830: ldur            w2, [x3, #0xf]
    // 0x6b8834: DecompressPointer r2
    //     0x6b8834: add             x2, x2, HEAP, lsl #32
    // 0x6b8838: ArrayLoad: r7 = r2[r1]  ; Unknown_4
    //     0x6b8838: add             x16, x2, x1, lsl #2
    //     0x6b883c: ldur            w7, [x16, #0xf]
    // 0x6b8840: DecompressPointer r7
    //     0x6b8840: add             x7, x7, HEAP, lsl #32
    // 0x6b8844: stur            x7, [fp, #-0x18]
    // 0x6b8848: add             x8, x1, #1
    // 0x6b884c: stur            x8, [fp, #-0x10]
    // 0x6b8850: LoadField: r9 = r0->field_f
    //     0x6b8850: ldur            w9, [x0, #0xf]
    // 0x6b8854: DecompressPointer r9
    //     0x6b8854: add             x9, x9, HEAP, lsl #32
    // 0x6b8858: mov             x1, x0
    // 0x6b885c: mov             x2, x7
    // 0x6b8860: stur            x9, [fp, #-8]
    // 0x6b8864: r0 = _getValueOrData()
    //     0x6b8864: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x6b8868: mov             x1, x0
    // 0x6b886c: ldur            x0, [fp, #-8]
    // 0x6b8870: cmp             w0, w1
    // 0x6b8874: b.eq            #0x6b88c8
    // 0x6b8878: ldur            x0, [fp, #-0x30]
    // 0x6b887c: mov             x1, x0
    // 0x6b8880: ldur            x2, [fp, #-0x18]
    // 0x6b8884: r0 = _getValueOrData()
    //     0x6b8884: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x6b8888: ldur            x1, [fp, #-0x30]
    // 0x6b888c: LoadField: r2 = r1->field_f
    //     0x6b888c: ldur            w2, [x1, #0xf]
    // 0x6b8890: DecompressPointer r2
    //     0x6b8890: add             x2, x2, HEAP, lsl #32
    // 0x6b8894: cmp             w2, w0
    // 0x6b8898: b.ne            #0x6b88a0
    // 0x6b889c: r0 = Null
    //     0x6b889c: mov             x0, NULL
    // 0x6b88a0: cmp             w0, NULL
    // 0x6b88a4: b.eq            #0x6b899c
    // 0x6b88a8: ldur            x16, [fp, #-0x20]
    // 0x6b88ac: stp             x0, x16, [SP]
    // 0x6b88b0: ldur            x0, [fp, #-0x20]
    // 0x6b88b4: ClosureCall
    //     0x6b88b4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x6b88b8: ldur            x2, [x0, #0x1f]
    //     0x6b88bc: blr             x2
    // 0x6b88c0: ldur            x2, [fp, #-0x28]
    // 0x6b88c4: b               #0x6b8940
    // 0x6b88c8: ldur            x0, [fp, #-0x28]
    // 0x6b88cc: LoadField: r1 = r0->field_b
    //     0x6b88cc: ldur            w1, [x0, #0xb]
    // 0x6b88d0: LoadField: r2 = r0->field_f
    //     0x6b88d0: ldur            w2, [x0, #0xf]
    // 0x6b88d4: DecompressPointer r2
    //     0x6b88d4: add             x2, x2, HEAP, lsl #32
    // 0x6b88d8: LoadField: r3 = r2->field_b
    //     0x6b88d8: ldur            w3, [x2, #0xb]
    // 0x6b88dc: r2 = LoadInt32Instr(r1)
    //     0x6b88dc: sbfx            x2, x1, #1, #0x1f
    // 0x6b88e0: stur            x2, [fp, #-0x48]
    // 0x6b88e4: r1 = LoadInt32Instr(r3)
    //     0x6b88e4: sbfx            x1, x3, #1, #0x1f
    // 0x6b88e8: cmp             x2, x1
    // 0x6b88ec: b.ne            #0x6b88f8
    // 0x6b88f0: mov             x1, x0
    // 0x6b88f4: r0 = _growToNextCapacity()
    //     0x6b88f4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b88f8: ldur            x2, [fp, #-0x28]
    // 0x6b88fc: ldur            x3, [fp, #-0x48]
    // 0x6b8900: add             x0, x3, #1
    // 0x6b8904: lsl             x1, x0, #1
    // 0x6b8908: StoreField: r2->field_b = r1
    //     0x6b8908: stur            w1, [x2, #0xb]
    // 0x6b890c: LoadField: r1 = r2->field_f
    //     0x6b890c: ldur            w1, [x2, #0xf]
    // 0x6b8910: DecompressPointer r1
    //     0x6b8910: add             x1, x1, HEAP, lsl #32
    // 0x6b8914: ldur            x0, [fp, #-0x18]
    // 0x6b8918: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6b8918: add             x25, x1, x3, lsl #2
    //     0x6b891c: add             x25, x25, #0xf
    //     0x6b8920: str             w0, [x25]
    //     0x6b8924: tbz             w0, #0, #0x6b8940
    //     0x6b8928: ldurb           w16, [x1, #-1]
    //     0x6b892c: ldurb           w17, [x0, #-1]
    //     0x6b8930: and             x16, x17, x16, lsr #2
    //     0x6b8934: tst             x16, HEAP, lsr #32
    //     0x6b8938: b.eq            #0x6b8940
    //     0x6b893c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6b8940: ldur            x1, [fp, #-0x10]
    // 0x6b8944: ldur            x3, [fp, #-0x40]
    // 0x6b8948: ldur            x0, [fp, #-0x30]
    // 0x6b894c: mov             x5, x2
    // 0x6b8950: ldur            x6, [fp, #-0x20]
    // 0x6b8954: ldur            x4, [fp, #-0x38]
    // 0x6b8958: b               #0x6b880c
    // 0x6b895c: r0 = Null
    //     0x6b895c: mov             x0, NULL
    // 0x6b8960: LeaveFrame
    //     0x6b8960: mov             SP, fp
    //     0x6b8964: ldp             fp, lr, [SP], #0x10
    // 0x6b8968: ret
    //     0x6b8968: ret             
    // 0x6b896c: mov             x0, x3
    // 0x6b8970: r0 = ConcurrentModificationError()
    //     0x6b8970: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6b8974: mov             x1, x0
    // 0x6b8978: ldur            x0, [fp, #-0x40]
    // 0x6b897c: StoreField: r1->field_b = r0
    //     0x6b897c: stur            w0, [x1, #0xb]
    // 0x6b8980: mov             x0, x1
    // 0x6b8984: r0 = Throw()
    //     0x6b8984: bl              #0xec04b8  ; ThrowStub
    // 0x6b8988: brk             #0
    // 0x6b898c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b898c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b8990: b               #0x6b87c8
    // 0x6b8994: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b8994: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b8998: b               #0x6b8818
    // 0x6b899c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b899c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _requestTabTraversalFocus(/* No info */) {
    // ** addr: 0x6b89a0, size: 0x134
    // 0x6b89a0: EnterFrame
    //     0x6b89a0: stp             fp, lr, [SP, #-0x10]!
    //     0x6b89a4: mov             fp, SP
    // 0x6b89a8: AllocStack(0x48)
    //     0x6b89a8: sub             SP, SP, #0x48
    // 0x6b89ac: SetupParameters(FocusTraversalPolicy this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */)
    //     0x6b89ac: stur            x1, [fp, #-0x10]
    //     0x6b89b0: stur            x2, [fp, #-0x18]
    //     0x6b89b4: stur            x3, [fp, #-0x20]
    //     0x6b89b8: stur            x5, [fp, #-0x28]
    // 0x6b89bc: CheckStackOverflow
    //     0x6b89bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b89c0: cmp             SP, x16
    //     0x6b89c4: b.ls            #0x6b8ac8
    // 0x6b89c8: r0 = LoadClassIdInstr(r2)
    //     0x6b89c8: ldur            x0, [x2, #-1]
    //     0x6b89cc: ubfx            x0, x0, #0xc, #0x14
    // 0x6b89d0: cmp             x0, #0xb69
    // 0x6b89d4: b.ne            #0x6b8a90
    // 0x6b89d8: LoadField: r0 = r2->field_6b
    //     0x6b89d8: ldur            w0, [x2, #0x6b]
    // 0x6b89dc: DecompressPointer r0
    //     0x6b89dc: add             x0, x0, HEAP, lsl #32
    // 0x6b89e0: stur            x0, [fp, #-8]
    // 0x6b89e4: r16 = <FocusNode>
    //     0x6b89e4: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x6b89e8: stp             x0, x16, [SP]
    // 0x6b89ec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x6b89ec: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x6b89f0: r0 = IterableExtensions.lastOrNull()
    //     0x6b89f0: bl              #0x69058c  ; [dart:collection] ::IterableExtensions.lastOrNull
    // 0x6b89f4: cmp             w0, NULL
    // 0x6b89f8: b.eq            #0x6b8a38
    // 0x6b89fc: r16 = <FocusNode>
    //     0x6b89fc: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x6b8a00: ldur            lr, [fp, #-8]
    // 0x6b8a04: stp             lr, x16, [SP]
    // 0x6b8a08: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x6b8a08: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x6b8a0c: r0 = IterableExtensions.lastOrNull()
    //     0x6b8a0c: bl              #0x69058c  ; [dart:collection] ::IterableExtensions.lastOrNull
    // 0x6b8a10: cmp             w0, NULL
    // 0x6b8a14: b.eq            #0x6b8ad0
    // 0x6b8a18: ldur            x1, [fp, #-0x10]
    // 0x6b8a1c: mov             x2, x0
    // 0x6b8a20: ldur            x3, [fp, #-0x20]
    // 0x6b8a24: ldur            x5, [fp, #-0x28]
    // 0x6b8a28: r0 = _requestTabTraversalFocus()
    //     0x6b8a28: bl              #0x6b89a0  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_requestTabTraversalFocus
    // 0x6b8a2c: LeaveFrame
    //     0x6b8a2c: mov             SP, fp
    //     0x6b8a30: ldp             fp, lr, [SP], #0x10
    // 0x6b8a34: ret
    //     0x6b8a34: ret             
    // 0x6b8a38: ldur            x1, [fp, #-0x18]
    // 0x6b8a3c: ldur            x2, [fp, #-0x18]
    // 0x6b8a40: r0 = _sortAllDescendants()
    //     0x6b8a40: bl              #0x6b465c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_sortAllDescendants
    // 0x6b8a44: LoadField: r1 = r0->field_b
    //     0x6b8a44: ldur            w1, [x0, #0xb]
    // 0x6b8a48: cbz             w1, #0x6b8a90
    // 0x6b8a4c: ldur            x5, [fp, #-0x28]
    // 0x6b8a50: tbnz            w5, #4, #0x6b8a64
    // 0x6b8a54: mov             x1, x0
    // 0x6b8a58: r0 = first()
    //     0x6b8a58: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6b8a5c: mov             x2, x0
    // 0x6b8a60: b               #0x6b8a70
    // 0x6b8a64: mov             x1, x0
    // 0x6b8a68: r0 = last()
    //     0x6b8a68: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x6b8a6c: mov             x2, x0
    // 0x6b8a70: ldur            x1, [fp, #-0x10]
    // 0x6b8a74: ldur            x3, [fp, #-0x20]
    // 0x6b8a78: ldur            x5, [fp, #-0x28]
    // 0x6b8a7c: r0 = _requestTabTraversalFocus()
    //     0x6b8a7c: bl              #0x6b89a0  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_requestTabTraversalFocus
    // 0x6b8a80: r0 = true
    //     0x6b8a80: add             x0, NULL, #0x20  ; true
    // 0x6b8a84: LeaveFrame
    //     0x6b8a84: mov             SP, fp
    //     0x6b8a88: ldp             fp, lr, [SP], #0x10
    // 0x6b8a8c: ret
    //     0x6b8a8c: ret             
    // 0x6b8a90: ldur            x1, [fp, #-0x18]
    // 0x6b8a94: r0 = hasPrimaryFocus()
    //     0x6b8a94: bl              #0x651240  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::hasPrimaryFocus
    // 0x6b8a98: stur            x0, [fp, #-8]
    // 0x6b8a9c: ldur            x16, [fp, #-0x20]
    // 0x6b8aa0: stp             NULL, x16, [SP, #0x10]
    // 0x6b8aa4: stp             NULL, NULL, [SP]
    // 0x6b8aa8: ldur            x1, [fp, #-0x18]
    // 0x6b8aac: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, alignmentPolicy, 0x1, curve, 0x4, duration, 0x3, null]
    //     0x6b8aac: ldr             x4, [PP, #0x74f0]  ; [pp+0x74f0] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "alignmentPolicy", 0x1, "curve", 0x4, "duration", 0x3, Null]
    // 0x6b8ab0: r0 = defaultTraversalRequestFocusCallback()
    //     0x6b8ab0: bl              #0x6b7f0c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::defaultTraversalRequestFocusCallback
    // 0x6b8ab4: ldur            x1, [fp, #-8]
    // 0x6b8ab8: eor             x0, x1, #0x10
    // 0x6b8abc: LeaveFrame
    //     0x6b8abc: mov             SP, fp
    //     0x6b8ac0: ldp             fp, lr, [SP], #0x10
    // 0x6b8ac4: ret
    //     0x6b8ac4: ret             
    // 0x6b8ac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b8ac8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b8acc: b               #0x6b89c8
    // 0x6b8ad0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b8ad0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ findLastFocus(/* No info */) {
    // ** addr: 0x6b8ad4, size: 0x3c
    // 0x6b8ad4: EnterFrame
    //     0x6b8ad4: stp             fp, lr, [SP, #-0x10]!
    //     0x6b8ad8: mov             fp, SP
    // 0x6b8adc: AllocStack(0x8)
    //     0x6b8adc: sub             SP, SP, #8
    // 0x6b8ae0: CheckStackOverflow
    //     0x6b8ae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b8ae4: cmp             SP, x16
    //     0x6b8ae8: b.ls            #0x6b8b08
    // 0x6b8aec: r16 = true
    //     0x6b8aec: add             x16, NULL, #0x20  ; true
    // 0x6b8af0: str             x16, [SP]
    // 0x6b8af4: r4 = const [0, 0x3, 0x1, 0x2, fromEnd, 0x2, null]
    //     0x6b8af4: ldr             x4, [PP, #0x7708]  ; [pp+0x7708] List(7) [0, 0x3, 0x1, 0x2, "fromEnd", 0x2, Null]
    // 0x6b8af8: r0 = _findInitialFocus()
    //     0x6b8af8: bl              #0x6b8b10  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_findInitialFocus
    // 0x6b8afc: LeaveFrame
    //     0x6b8afc: mov             SP, fp
    //     0x6b8b00: ldp             fp, lr, [SP], #0x10
    // 0x6b8b04: ret
    //     0x6b8b04: ret             
    // 0x6b8b08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b8b08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b8b0c: b               #0x6b8aec
  }
  _ _findInitialFocus(/* No info */) {
    // ** addr: 0x6b8b10, size: 0x15c
    // 0x6b8b10: EnterFrame
    //     0x6b8b10: stp             fp, lr, [SP, #-0x10]!
    //     0x6b8b14: mov             fp, SP
    // 0x6b8b18: AllocStack(0x30)
    //     0x6b8b18: sub             SP, SP, #0x30
    // 0x6b8b1c: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */, {dynamic fromEnd = false /* r2, fp-0x8 */})
    //     0x6b8b1c: mov             x0, x2
    //     0x6b8b20: stur            x2, [fp, #-0x10]
    //     0x6b8b24: ldur            w1, [x4, #0x13]
    //     0x6b8b28: ldur            w2, [x4, #0x1f]
    //     0x6b8b2c: add             x2, x2, HEAP, lsl #32
    //     0x6b8b30: ldr             x16, [PP, #0x7710]  ; [pp+0x7710] "fromEnd"
    //     0x6b8b34: cmp             w2, w16
    //     0x6b8b38: b.ne            #0x6b8b58
    //     0x6b8b3c: ldur            w2, [x4, #0x23]
    //     0x6b8b40: add             x2, x2, HEAP, lsl #32
    //     0x6b8b44: sub             w3, w1, w2
    //     0x6b8b48: add             x1, fp, w3, sxtw #2
    //     0x6b8b4c: ldr             x1, [x1, #8]
    //     0x6b8b50: mov             x2, x1
    //     0x6b8b54: b               #0x6b8b5c
    //     0x6b8b58: add             x2, NULL, #0x30  ; false
    //     0x6b8b5c: stur            x2, [fp, #-8]
    // 0x6b8b60: CheckStackOverflow
    //     0x6b8b60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b8b64: cmp             SP, x16
    //     0x6b8b68: b.ls            #0x6b8c60
    // 0x6b8b6c: r1 = LoadClassIdInstr(r0)
    //     0x6b8b6c: ldur            x1, [x0, #-1]
    //     0x6b8b70: ubfx            x1, x1, #0xc, #0x14
    // 0x6b8b74: sub             x16, x1, #0xb67
    // 0x6b8b78: cmp             x16, #1
    // 0x6b8b7c: b.hi            #0x6b8b90
    // 0x6b8b80: mov             x1, x0
    // 0x6b8b84: r0 = enclosingScope()
    //     0x6b8b84: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x6b8b88: mov             x1, x0
    // 0x6b8b8c: b               #0x6b8b94
    // 0x6b8b90: ldur            x1, [fp, #-0x10]
    // 0x6b8b94: stur            x1, [fp, #-0x18]
    // 0x6b8b98: cmp             w1, NULL
    // 0x6b8b9c: b.eq            #0x6b8c68
    // 0x6b8ba0: LoadField: r0 = r1->field_6b
    //     0x6b8ba0: ldur            w0, [x1, #0x6b]
    // 0x6b8ba4: DecompressPointer r0
    //     0x6b8ba4: add             x0, x0, HEAP, lsl #32
    // 0x6b8ba8: r16 = <FocusNode>
    //     0x6b8ba8: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x6b8bac: stp             x0, x16, [SP]
    // 0x6b8bb0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x6b8bb0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x6b8bb4: r0 = IterableExtensions.lastOrNull()
    //     0x6b8bb4: bl              #0x69058c  ; [dart:collection] ::IterableExtensions.lastOrNull
    // 0x6b8bb8: stur            x0, [fp, #-0x20]
    // 0x6b8bbc: cmp             w0, NULL
    // 0x6b8bc0: b.ne            #0x6b8c3c
    // 0x6b8bc4: ldur            x1, [fp, #-0x18]
    // 0x6b8bc8: r0 = descendants()
    //     0x6b8bc8: bl              #0x651cc0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::descendants
    // 0x6b8bcc: LoadField: r1 = r0->field_b
    //     0x6b8bcc: ldur            w1, [x0, #0xb]
    // 0x6b8bd0: cbz             w1, #0x6b8c3c
    // 0x6b8bd4: ldur            x1, [fp, #-0x18]
    // 0x6b8bd8: ldur            x2, [fp, #-0x10]
    // 0x6b8bdc: r0 = _sortAllDescendants()
    //     0x6b8bdc: bl              #0x6b465c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_sortAllDescendants
    // 0x6b8be0: r1 = Function '<anonymous closure>':.
    //     0x6b8be0: ldr             x1, [PP, #0x7718]  ; [pp+0x7718] AnonymousClosure: (0x6b8c6c), in [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_findInitialFocus (0x6b8b10)
    // 0x6b8be4: r2 = Null
    //     0x6b8be4: mov             x2, NULL
    // 0x6b8be8: stur            x0, [fp, #-0x18]
    // 0x6b8bec: r0 = AllocateClosure()
    //     0x6b8bec: bl              #0xec1630  ; AllocateClosureStub
    // 0x6b8bf0: ldur            x1, [fp, #-0x18]
    // 0x6b8bf4: mov             x2, x0
    // 0x6b8bf8: r0 = where()
    //     0x6b8bf8: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x6b8bfc: mov             x1, x0
    // 0x6b8c00: stur            x0, [fp, #-0x18]
    // 0x6b8c04: r0 = isEmpty()
    //     0x6b8c04: bl              #0x7f062c  ; [dart:core] Iterable::isEmpty
    // 0x6b8c08: tbnz            w0, #4, #0x6b8c14
    // 0x6b8c0c: r1 = Null
    //     0x6b8c0c: mov             x1, NULL
    // 0x6b8c10: b               #0x6b8c40
    // 0x6b8c14: ldur            x0, [fp, #-8]
    // 0x6b8c18: tbnz            w0, #4, #0x6b8c2c
    // 0x6b8c1c: ldur            x1, [fp, #-0x18]
    // 0x6b8c20: r0 = last()
    //     0x6b8c20: bl              #0x7a963c  ; [dart:core] Iterable::last
    // 0x6b8c24: mov             x1, x0
    // 0x6b8c28: b               #0x6b8c40
    // 0x6b8c2c: ldur            x1, [fp, #-0x18]
    // 0x6b8c30: r0 = first()
    //     0x6b8c30: bl              #0x8939e0  ; [dart:core] Iterable::first
    // 0x6b8c34: mov             x1, x0
    // 0x6b8c38: b               #0x6b8c40
    // 0x6b8c3c: ldur            x1, [fp, #-0x20]
    // 0x6b8c40: cmp             w1, NULL
    // 0x6b8c44: b.ne            #0x6b8c50
    // 0x6b8c48: ldur            x0, [fp, #-0x10]
    // 0x6b8c4c: b               #0x6b8c54
    // 0x6b8c50: mov             x0, x1
    // 0x6b8c54: LeaveFrame
    //     0x6b8c54: mov             SP, fp
    //     0x6b8c58: ldp             fp, lr, [SP], #0x10
    // 0x6b8c5c: ret
    //     0x6b8c5c: ret             
    // 0x6b8c60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b8c60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b8c64: b               #0x6b8b6c
    // 0x6b8c68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b8c68: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, FocusNode) {
    // ** addr: 0x6b8c6c, size: 0x30
    // 0x6b8c6c: EnterFrame
    //     0x6b8c6c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b8c70: mov             fp, SP
    // 0x6b8c74: CheckStackOverflow
    //     0x6b8c74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b8c78: cmp             SP, x16
    //     0x6b8c7c: b.ls            #0x6b8c94
    // 0x6b8c80: ldr             x1, [fp, #0x10]
    // 0x6b8c84: r0 = _canRequestTraversalFocus()
    //     0x6b8c84: bl              #0x6b874c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_canRequestTraversalFocus
    // 0x6b8c88: LeaveFrame
    //     0x6b8c88: mov             SP, fp
    //     0x6b8c8c: ldp             fp, lr, [SP], #0x10
    // 0x6b8c90: ret
    //     0x6b8c90: ret             
    // 0x6b8c94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b8c94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b8c98: b               #0x6b8c80
  }
  _ next(/* No info */) {
    // ** addr: 0x6b8d68, size: 0x30
    // 0x6b8d68: EnterFrame
    //     0x6b8d68: stp             fp, lr, [SP, #-0x10]!
    //     0x6b8d6c: mov             fp, SP
    // 0x6b8d70: CheckStackOverflow
    //     0x6b8d70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b8d74: cmp             SP, x16
    //     0x6b8d78: b.ls            #0x6b8d90
    // 0x6b8d7c: r3 = true
    //     0x6b8d7c: add             x3, NULL, #0x20  ; true
    // 0x6b8d80: r0 = _moveFocus()
    //     0x6b8d80: bl              #0x6b403c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::_moveFocus
    // 0x6b8d84: LeaveFrame
    //     0x6b8d84: mov             SP, fp
    //     0x6b8d88: ldp             fp, lr, [SP], #0x10
    // 0x6b8d8c: ret
    //     0x6b8d8c: ret             
    // 0x6b8d90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b8d90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b8d94: b               #0x6b8d7c
  }
}

// class id: 3790, size: 0x10, field offset: 0xc
//   transformed mixin,
abstract class _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin extends FocusTraversalPolicy
     with DirectionalFocusTraversalPolicyMixin {

  _ changedScope(/* No info */) {
    // ** addr: 0x6515f4, size: 0xc8
    // 0x6515f4: EnterFrame
    //     0x6515f4: stp             fp, lr, [SP, #-0x10]!
    //     0x6515f8: mov             fp, SP
    // 0x6515fc: AllocStack(0x20)
    //     0x6515fc: sub             SP, SP, #0x20
    // 0x651600: SetupParameters(_WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0x651600: mov             x0, x2
    //     0x651604: stur            x2, [fp, #-0x10]
    //     0x651608: mov             x2, x3
    //     0x65160c: stur            x1, [fp, #-8]
    //     0x651610: stur            x3, [fp, #-0x18]
    // 0x651614: CheckStackOverflow
    //     0x651614: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x651618: cmp             SP, x16
    //     0x65161c: b.ls            #0x6516b4
    // 0x651620: r1 = 1
    //     0x651620: movz            x1, #0x1
    // 0x651624: r0 = AllocateContext()
    //     0x651624: bl              #0xec126c  ; AllocateContextStub
    // 0x651628: mov             x3, x0
    // 0x65162c: ldur            x0, [fp, #-0x10]
    // 0x651630: stur            x3, [fp, #-0x20]
    // 0x651634: StoreField: r3->field_f = r0
    //     0x651634: stur            w0, [x3, #0xf]
    // 0x651638: ldur            x0, [fp, #-8]
    // 0x65163c: LoadField: r4 = r0->field_b
    //     0x65163c: ldur            w4, [x0, #0xb]
    // 0x651640: DecompressPointer r4
    //     0x651640: add             x4, x4, HEAP, lsl #32
    // 0x651644: mov             x1, x4
    // 0x651648: ldur            x2, [fp, #-0x18]
    // 0x65164c: stur            x4, [fp, #-0x10]
    // 0x651650: r0 = _getValueOrData()
    //     0x651650: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x651654: mov             x1, x0
    // 0x651658: ldur            x0, [fp, #-0x10]
    // 0x65165c: LoadField: r2 = r0->field_f
    //     0x65165c: ldur            w2, [x0, #0xf]
    // 0x651660: DecompressPointer r2
    //     0x651660: add             x2, x2, HEAP, lsl #32
    // 0x651664: cmp             w2, w1
    // 0x651668: b.ne            #0x651674
    // 0x65166c: r0 = Null
    //     0x65166c: mov             x0, NULL
    // 0x651670: b               #0x651678
    // 0x651674: mov             x0, x1
    // 0x651678: cmp             w0, NULL
    // 0x65167c: b.eq            #0x6516a4
    // 0x651680: LoadField: r3 = r0->field_7
    //     0x651680: ldur            w3, [x0, #7]
    // 0x651684: DecompressPointer r3
    //     0x651684: add             x3, x3, HEAP, lsl #32
    // 0x651688: ldur            x2, [fp, #-0x20]
    // 0x65168c: stur            x3, [fp, #-8]
    // 0x651690: r1 = Function '<anonymous closure>':.
    //     0x651690: ldr             x1, [PP, #0x2488]  ; [pp+0x2488] AnonymousClosure: (0x6516bc), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::changedScope (0x6515f4)
    // 0x651694: r0 = AllocateClosure()
    //     0x651694: bl              #0xec1630  ; AllocateClosureStub
    // 0x651698: ldur            x1, [fp, #-8]
    // 0x65169c: mov             x2, x0
    // 0x6516a0: r0 = removeWhere()
    //     0x6516a0: bl              #0x6eb068  ; [dart:collection] ListBase::removeWhere
    // 0x6516a4: r0 = Null
    //     0x6516a4: mov             x0, NULL
    // 0x6516a8: LeaveFrame
    //     0x6516a8: mov             SP, fp
    //     0x6516ac: ldp             fp, lr, [SP], #0x10
    // 0x6516b0: ret
    //     0x6516b0: ret             
    // 0x6516b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6516b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6516b8: b               #0x651620
  }
  [closure] bool <anonymous closure>(dynamic, _DirectionalPolicyDataEntry) {
    // ** addr: 0x6516bc, size: 0x34
    // 0x6516bc: ldr             x1, [SP, #8]
    // 0x6516c0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x6516c0: ldur            w2, [x1, #0x17]
    // 0x6516c4: DecompressPointer r2
    //     0x6516c4: add             x2, x2, HEAP, lsl #32
    // 0x6516c8: ldr             x1, [SP]
    // 0x6516cc: LoadField: r3 = r1->field_b
    //     0x6516cc: ldur            w3, [x1, #0xb]
    // 0x6516d0: DecompressPointer r3
    //     0x6516d0: add             x3, x3, HEAP, lsl #32
    // 0x6516d4: LoadField: r1 = r2->field_f
    //     0x6516d4: ldur            w1, [x2, #0xf]
    // 0x6516d8: DecompressPointer r1
    //     0x6516d8: add             x1, x1, HEAP, lsl #32
    // 0x6516dc: cmp             w3, w1
    // 0x6516e0: r16 = true
    //     0x6516e0: add             x16, NULL, #0x20  ; true
    // 0x6516e4: r17 = false
    //     0x6516e4: add             x17, NULL, #0x30  ; false
    // 0x6516e8: csel            x0, x16, x17, eq
    // 0x6516ec: ret
    //     0x6516ec: ret             
  }
  _ invalidateScopeData(/* No info */) {
    // ** addr: 0x6b8c9c, size: 0x3c
    // 0x6b8c9c: EnterFrame
    //     0x6b8c9c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b8ca0: mov             fp, SP
    // 0x6b8ca4: CheckStackOverflow
    //     0x6b8ca4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b8ca8: cmp             SP, x16
    //     0x6b8cac: b.ls            #0x6b8cd0
    // 0x6b8cb0: LoadField: r0 = r1->field_b
    //     0x6b8cb0: ldur            w0, [x1, #0xb]
    // 0x6b8cb4: DecompressPointer r0
    //     0x6b8cb4: add             x0, x0, HEAP, lsl #32
    // 0x6b8cb8: mov             x1, x0
    // 0x6b8cbc: r0 = remove()
    //     0x6b8cbc: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x6b8cc0: r0 = Null
    //     0x6b8cc0: mov             x0, NULL
    // 0x6b8cc4: LeaveFrame
    //     0x6b8cc4: mov             SP, fp
    //     0x6b8cc8: ldp             fp, lr, [SP], #0x10
    // 0x6b8ccc: ret
    //     0x6b8ccc: ret             
    // 0x6b8cd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b8cd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b8cd4: b               #0x6b8cb0
  }
  _ inDirection(/* No info */) {
    // ** addr: 0xa56dbc, size: 0x734
    // 0xa56dbc: EnterFrame
    //     0xa56dbc: stp             fp, lr, [SP, #-0x10]!
    //     0xa56dc0: mov             fp, SP
    // 0xa56dc4: AllocStack(0x78)
    //     0xa56dc4: sub             SP, SP, #0x78
    // 0xa56dc8: SetupParameters(_WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa56dc8: mov             x0, x2
    //     0xa56dcc: stur            x2, [fp, #-0x10]
    //     0xa56dd0: mov             x2, x1
    //     0xa56dd4: stur            x1, [fp, #-8]
    //     0xa56dd8: stur            x3, [fp, #-0x18]
    // 0xa56ddc: CheckStackOverflow
    //     0xa56ddc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa56de0: cmp             SP, x16
    //     0xa56de4: b.ls            #0xa574d8
    // 0xa56de8: r1 = LoadClassIdInstr(r0)
    //     0xa56de8: ldur            x1, [x0, #-1]
    //     0xa56dec: ubfx            x1, x1, #0xc, #0x14
    // 0xa56df0: sub             x16, x1, #0xb67
    // 0xa56df4: cmp             x16, #1
    // 0xa56df8: b.hi            #0xa56e0c
    // 0xa56dfc: mov             x1, x0
    // 0xa56e00: r0 = enclosingScope()
    //     0xa56e00: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0xa56e04: mov             x3, x0
    // 0xa56e08: b               #0xa56e10
    // 0xa56e0c: ldur            x3, [fp, #-0x10]
    // 0xa56e10: stur            x3, [fp, #-0x20]
    // 0xa56e14: cmp             w3, NULL
    // 0xa56e18: b.eq            #0xa574e0
    // 0xa56e1c: LoadField: r0 = r3->field_6b
    //     0xa56e1c: ldur            w0, [x3, #0x6b]
    // 0xa56e20: DecompressPointer r0
    //     0xa56e20: add             x0, x0, HEAP, lsl #32
    // 0xa56e24: r16 = <FocusNode>
    //     0xa56e24: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0xa56e28: stp             x0, x16, [SP]
    // 0xa56e2c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa56e2c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa56e30: r0 = IterableExtensions.lastOrNull()
    //     0xa56e30: bl              #0x69058c  ; [dart:collection] ::IterableExtensions.lastOrNull
    // 0xa56e34: stur            x0, [fp, #-0x28]
    // 0xa56e38: cmp             w0, NULL
    // 0xa56e3c: b.ne            #0xa56ec4
    // 0xa56e40: ldur            x1, [fp, #-8]
    // 0xa56e44: ldur            x2, [fp, #-0x10]
    // 0xa56e48: ldur            x3, [fp, #-0x18]
    // 0xa56e4c: r0 = findFirstFocusInDirection()
    //     0xa56e4c: bl              #0xa58f54  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::findFirstFocusInDirection
    // 0xa56e50: cmp             w0, NULL
    // 0xa56e54: b.ne            #0xa56e60
    // 0xa56e58: ldur            x1, [fp, #-0x10]
    // 0xa56e5c: b               #0xa56e64
    // 0xa56e60: mov             x1, x0
    // 0xa56e64: ldur            x4, [fp, #-0x18]
    // 0xa56e68: LoadField: r0 = r4->field_7
    //     0xa56e68: ldur            x0, [x4, #7]
    // 0xa56e6c: cmp             x0, #1
    // 0xa56e70: b.gt            #0xa56e80
    // 0xa56e74: cmp             x0, #0
    // 0xa56e78: b.gt            #0xa56e88
    // 0xa56e7c: b               #0xa56ea0
    // 0xa56e80: cmp             x0, #2
    // 0xa56e84: b.gt            #0xa56ea0
    // 0xa56e88: r16 = Instance_ScrollPositionAlignmentPolicy
    //     0xa56e88: ldr             x16, [PP, #0x7350]  ; [pp+0x7350] Obj!ScrollPositionAlignmentPolicy@e33bc1
    // 0xa56e8c: stp             NULL, x16, [SP, #0x10]
    // 0xa56e90: stp             NULL, NULL, [SP]
    // 0xa56e94: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, alignmentPolicy, 0x1, curve, 0x4, duration, 0x3, null]
    //     0xa56e94: ldr             x4, [PP, #0x74f0]  ; [pp+0x74f0] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "alignmentPolicy", 0x1, "curve", 0x4, "duration", 0x3, Null]
    // 0xa56e98: r0 = defaultTraversalRequestFocusCallback()
    //     0xa56e98: bl              #0x6b7f0c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::defaultTraversalRequestFocusCallback
    // 0xa56e9c: b               #0xa56eb4
    // 0xa56ea0: r16 = Instance_ScrollPositionAlignmentPolicy
    //     0xa56ea0: ldr             x16, [PP, #0x7358]  ; [pp+0x7358] Obj!ScrollPositionAlignmentPolicy@e33ba1
    // 0xa56ea4: stp             NULL, x16, [SP, #0x10]
    // 0xa56ea8: stp             NULL, NULL, [SP]
    // 0xa56eac: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, alignmentPolicy, 0x1, curve, 0x4, duration, 0x3, null]
    //     0xa56eac: ldr             x4, [PP, #0x74f0]  ; [pp+0x74f0] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "alignmentPolicy", 0x1, "curve", 0x4, "duration", 0x3, Null]
    // 0xa56eb0: r0 = defaultTraversalRequestFocusCallback()
    //     0xa56eb0: bl              #0x6b7f0c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::defaultTraversalRequestFocusCallback
    // 0xa56eb4: r0 = true
    //     0xa56eb4: add             x0, NULL, #0x20  ; true
    // 0xa56eb8: LeaveFrame
    //     0xa56eb8: mov             SP, fp
    //     0xa56ebc: ldp             fp, lr, [SP], #0x10
    // 0xa56ec0: ret
    //     0xa56ec0: ret             
    // 0xa56ec4: ldur            x4, [fp, #-0x18]
    // 0xa56ec8: ldur            x1, [fp, #-8]
    // 0xa56ecc: mov             x2, x4
    // 0xa56ed0: ldur            x3, [fp, #-0x20]
    // 0xa56ed4: r0 = _popPolicyDataIfNeeded()
    //     0xa56ed4: bl              #0xa58af8  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::_popPolicyDataIfNeeded
    // 0xa56ed8: tbnz            w0, #4, #0xa56eec
    // 0xa56edc: r0 = true
    //     0xa56edc: add             x0, NULL, #0x20  ; true
    // 0xa56ee0: LeaveFrame
    //     0xa56ee0: mov             SP, fp
    //     0xa56ee4: ldp             fp, lr, [SP], #0x10
    // 0xa56ee8: ret
    //     0xa56ee8: ret             
    // 0xa56eec: ldur            x2, [fp, #-0x18]
    // 0xa56ef0: ldur            x0, [fp, #-0x28]
    // 0xa56ef4: LoadField: r1 = r0->field_33
    //     0xa56ef4: ldur            w1, [x0, #0x33]
    // 0xa56ef8: DecompressPointer r1
    //     0xa56ef8: add             x1, x1, HEAP, lsl #32
    // 0xa56efc: cmp             w1, NULL
    // 0xa56f00: b.eq            #0xa574e4
    // 0xa56f04: r0 = maybeOf()
    //     0xa56f04: bl              #0x6b85f0  ; [package:flutter/src/widgets/scrollable.dart] Scrollable::maybeOf
    // 0xa56f08: stur            x0, [fp, #-0x10]
    // 0xa56f0c: r1 = 3
    //     0xa56f0c: movz            x1, #0x3
    // 0xa56f10: r0 = AllocateContext()
    //     0xa56f10: bl              #0xec126c  ; AllocateContextStub
    // 0xa56f14: mov             x2, x0
    // 0xa56f18: ldur            x0, [fp, #-0x10]
    // 0xa56f1c: stur            x2, [fp, #-0x38]
    // 0xa56f20: StoreField: r2->field_f = r0
    //     0xa56f20: stur            w0, [x2, #0xf]
    // 0xa56f24: ldur            x3, [fp, #-0x18]
    // 0xa56f28: LoadField: r4 = r3->field_7
    //     0xa56f28: ldur            x4, [x3, #7]
    // 0xa56f2c: stur            x4, [fp, #-0x30]
    // 0xa56f30: cmp             x4, #1
    // 0xa56f34: b.gt            #0xa56f54
    // 0xa56f38: cmp             x4, #0
    // 0xa56f3c: b.le            #0xa56f5c
    // 0xa56f40: mov             x3, x2
    // 0xa56f44: mov             x2, x0
    // 0xa56f48: d0 = -inf
    //     0xa56f48: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0xa56f4c: d1 = inf
    //     0xa56f4c: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xa56f50: b               #0xa571e4
    // 0xa56f54: cmp             x4, #2
    // 0xa56f58: b.gt            #0xa571d4
    // 0xa56f5c: ldur            x1, [fp, #-0x28]
    // 0xa56f60: r0 = rect()
    //     0xa56f60: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa56f64: ldur            x1, [fp, #-0x20]
    // 0xa56f68: stur            x0, [fp, #-0x40]
    // 0xa56f6c: r0 = canRequestFocus()
    //     0xa56f6c: bl              #0x6a8938  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::canRequestFocus
    // 0xa56f70: tbz             w0, #4, #0xa56f80
    // 0xa56f74: r5 = Instance_EmptyIterable
    //     0xa56f74: add             x5, PP, #0x59, lsl #12  ; [pp+0x59df0] Obj!EmptyIterable<FocusNode>@e3a3a1
    //     0xa56f78: ldr             x5, [x5, #0xdf0]
    // 0xa56f7c: b               #0xa56f8c
    // 0xa56f80: ldur            x1, [fp, #-0x20]
    // 0xa56f84: r0 = traversalDescendants()
    //     0xa56f84: bl              #0xa58a38  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::traversalDescendants
    // 0xa56f88: mov             x5, x0
    // 0xa56f8c: ldur            x1, [fp, #-8]
    // 0xa56f90: ldur            x2, [fp, #-0x18]
    // 0xa56f94: ldur            x3, [fp, #-0x40]
    // 0xa56f98: r0 = _sortAndFilterVertically()
    //     0xa56f98: bl              #0xa58678  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::_sortAndFilterVertically
    // 0xa56f9c: stur            x0, [fp, #-0x40]
    // 0xa56fa0: LoadField: r1 = r0->field_b
    //     0xa56fa0: ldur            w1, [x0, #0xb]
    // 0xa56fa4: cbnz            w1, #0xa56fb0
    // 0xa56fa8: r0 = Null
    //     0xa56fa8: mov             x0, NULL
    // 0xa56fac: b               #0xa57440
    // 0xa56fb0: ldur            x2, [fp, #-0x10]
    // 0xa56fb4: cmp             w2, NULL
    // 0xa56fb8: b.eq            #0xa57044
    // 0xa56fbc: LoadField: r1 = r2->field_2b
    //     0xa56fbc: ldur            w1, [x2, #0x2b]
    // 0xa56fc0: DecompressPointer r1
    //     0xa56fc0: add             x1, x1, HEAP, lsl #32
    // 0xa56fc4: cmp             w1, NULL
    // 0xa56fc8: b.eq            #0xa574e8
    // 0xa56fcc: r0 = atEdge()
    //     0xa56fcc: bl              #0xa585f8  ; [package:flutter/src/widgets/scroll_position.dart] _ScrollPosition&ViewportOffset&ScrollMetrics::atEdge
    // 0xa56fd0: tbz             w0, #4, #0xa57044
    // 0xa56fd4: ldur            x2, [fp, #-0x38]
    // 0xa56fd8: r1 = Function '<anonymous closure>':.
    //     0xa56fd8: add             x1, PP, #0x59, lsl #12  ; [pp+0x59df8] AnonymousClosure: (0xa59528), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::inDirection (0xa56dbc)
    //     0xa56fdc: ldr             x1, [x1, #0xdf8]
    // 0xa56fe0: r0 = AllocateClosure()
    //     0xa56fe0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa56fe4: ldur            x1, [fp, #-0x40]
    // 0xa56fe8: mov             x2, x0
    // 0xa56fec: r0 = where()
    //     0xa56fec: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xa56ff0: mov             x1, x0
    // 0xa56ff4: stur            x0, [fp, #-0x48]
    // 0xa56ff8: r0 = iterator()
    //     0xa56ff8: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0xa56ffc: r1 = LoadClassIdInstr(r0)
    //     0xa56ffc: ldur            x1, [x0, #-1]
    //     0xa57000: ubfx            x1, x1, #0xc, #0x14
    // 0xa57004: mov             x16, x0
    // 0xa57008: mov             x0, x1
    // 0xa5700c: mov             x1, x16
    // 0xa57010: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xa57010: movz            x17, #0x292d
    //     0xa57014: movk            x17, #0x1, lsl #16
    //     0xa57018: add             lr, x0, x17
    //     0xa5701c: ldr             lr, [x21, lr, lsl #3]
    //     0xa57020: blr             lr
    // 0xa57024: eor             x1, x0, #0x10
    // 0xa57028: eor             x0, x1, #0x10
    // 0xa5702c: tbnz            w0, #4, #0xa57038
    // 0xa57030: ldur            x0, [fp, #-0x48]
    // 0xa57034: b               #0xa5703c
    // 0xa57038: ldur            x0, [fp, #-0x40]
    // 0xa5703c: mov             x1, x0
    // 0xa57040: b               #0xa57048
    // 0xa57044: ldur            x1, [fp, #-0x40]
    // 0xa57048: ldur            x2, [fp, #-0x18]
    // 0xa5704c: r16 = Instance_TraversalDirection
    //     0xa5704c: add             x16, PP, #0x59, lsl #12  ; [pp+0x59e00] Obj!TraversalDirection@e344a1
    //     0xa57050: ldr             x16, [x16, #0xe00]
    // 0xa57054: cmp             w2, w16
    // 0xa57058: b.ne            #0xa5709c
    // 0xa5705c: r0 = LoadClassIdInstr(r1)
    //     0xa5705c: ldur            x0, [x1, #-1]
    //     0xa57060: ubfx            x0, x0, #0xc, #0x14
    // 0xa57064: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa57064: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa57068: r0 = GDT[cid_x0 + 0xd889]()
    //     0xa57068: movz            x17, #0xd889
    //     0xa5706c: add             lr, x0, x17
    //     0xa57070: ldr             lr, [x21, lr, lsl #3]
    //     0xa57074: blr             lr
    // 0xa57078: stur            x0, [fp, #-0x40]
    // 0xa5707c: LoadField: r1 = r0->field_7
    //     0xa5707c: ldur            w1, [x0, #7]
    // 0xa57080: DecompressPointer r1
    //     0xa57080: add             x1, x1, HEAP, lsl #32
    // 0xa57084: r0 = ReversedListIterable()
    //     0xa57084: bl              #0x668634  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0xa57088: mov             x1, x0
    // 0xa5708c: ldur            x0, [fp, #-0x40]
    // 0xa57090: StoreField: r1->field_b = r0
    //     0xa57090: stur            w0, [x1, #0xb]
    // 0xa57094: mov             x0, x1
    // 0xa57098: b               #0xa570a0
    // 0xa5709c: mov             x0, x1
    // 0xa570a0: ldur            x2, [fp, #-0x38]
    // 0xa570a4: ldur            x1, [fp, #-0x28]
    // 0xa570a8: stur            x0, [fp, #-0x40]
    // 0xa570ac: r0 = rect()
    //     0xa570ac: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa570b0: LoadField: d0 = r0->field_7
    //     0xa570b0: ldur            d0, [x0, #7]
    // 0xa570b4: ldur            x1, [fp, #-0x28]
    // 0xa570b8: stur            d0, [fp, #-0x50]
    // 0xa570bc: r0 = rect()
    //     0xa570bc: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa570c0: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xa570c0: ldur            d0, [x0, #0x17]
    // 0xa570c4: stur            d0, [fp, #-0x58]
    // 0xa570c8: r0 = Rect()
    //     0xa570c8: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0xa570cc: ldur            d0, [fp, #-0x50]
    // 0xa570d0: StoreField: r0->field_7 = d0
    //     0xa570d0: stur            d0, [x0, #7]
    // 0xa570d4: d0 = -inf
    //     0xa570d4: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0xa570d8: StoreField: r0->field_f = d0
    //     0xa570d8: stur            d0, [x0, #0xf]
    // 0xa570dc: ldur            d0, [fp, #-0x58]
    // 0xa570e0: ArrayStore: r0[0] = d0  ; List_8
    //     0xa570e0: stur            d0, [x0, #0x17]
    // 0xa570e4: d1 = inf
    //     0xa570e4: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xa570e8: StoreField: r0->field_1f = d1
    //     0xa570e8: stur            d1, [x0, #0x1f]
    // 0xa570ec: ldur            x3, [fp, #-0x38]
    // 0xa570f0: ArrayStore: r3[0] = r0  ; List_4
    //     0xa570f0: stur            w0, [x3, #0x17]
    //     0xa570f4: ldurb           w16, [x3, #-1]
    //     0xa570f8: ldurb           w17, [x0, #-1]
    //     0xa570fc: and             x16, x17, x16, lsr #2
    //     0xa57100: tst             x16, HEAP, lsr #32
    //     0xa57104: b.eq            #0xa5710c
    //     0xa57108: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa5710c: mov             x2, x3
    // 0xa57110: r1 = Function '<anonymous closure>':.
    //     0xa57110: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e08] AnonymousClosure: (0xa595a0), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::inDirection (0xa56dbc)
    //     0xa57114: ldr             x1, [x1, #0xe08]
    // 0xa57118: r0 = AllocateClosure()
    //     0xa57118: bl              #0xec1630  ; AllocateClosureStub
    // 0xa5711c: ldur            x3, [fp, #-0x40]
    // 0xa57120: r1 = LoadClassIdInstr(r3)
    //     0xa57120: ldur            x1, [x3, #-1]
    //     0xa57124: ubfx            x1, x1, #0xc, #0x14
    // 0xa57128: mov             x2, x0
    // 0xa5712c: mov             x0, x1
    // 0xa57130: mov             x1, x3
    // 0xa57134: r0 = GDT[cid_x0 + 0xea28]()
    //     0xa57134: movz            x17, #0xea28
    //     0xa57138: add             lr, x0, x17
    //     0xa5713c: ldr             lr, [x21, lr, lsl #3]
    //     0xa57140: blr             lr
    // 0xa57144: mov             x1, x0
    // 0xa57148: stur            x0, [fp, #-0x48]
    // 0xa5714c: r0 = iterator()
    //     0xa5714c: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0xa57150: r1 = LoadClassIdInstr(r0)
    //     0xa57150: ldur            x1, [x0, #-1]
    //     0xa57154: ubfx            x1, x1, #0xc, #0x14
    // 0xa57158: mov             x16, x0
    // 0xa5715c: mov             x0, x1
    // 0xa57160: mov             x1, x16
    // 0xa57164: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xa57164: movz            x17, #0x292d
    //     0xa57168: movk            x17, #0x1, lsl #16
    //     0xa5716c: add             lr, x0, x17
    //     0xa57170: ldr             lr, [x21, lr, lsl #3]
    //     0xa57174: blr             lr
    // 0xa57178: eor             x1, x0, #0x10
    // 0xa5717c: eor             x0, x1, #0x10
    // 0xa57180: tbnz            w0, #4, #0xa571ac
    // 0xa57184: ldur            x1, [fp, #-0x28]
    // 0xa57188: r0 = rect()
    //     0xa57188: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa5718c: mov             x1, x0
    // 0xa57190: r0 = center()
    //     0xa57190: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa57194: mov             x1, x0
    // 0xa57198: ldur            x2, [fp, #-0x48]
    // 0xa5719c: r0 = _sortByDistancePreferVertical()
    //     0xa5719c: bl              #0xa584b4  ; [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_sortByDistancePreferVertical
    // 0xa571a0: mov             x1, x0
    // 0xa571a4: r0 = first()
    //     0xa571a4: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xa571a8: b               #0xa57440
    // 0xa571ac: ldur            x1, [fp, #-0x28]
    // 0xa571b0: r0 = rect()
    //     0xa571b0: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa571b4: mov             x1, x0
    // 0xa571b8: r0 = center()
    //     0xa571b8: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa571bc: mov             x1, x0
    // 0xa571c0: ldur            x2, [fp, #-0x40]
    // 0xa571c4: r0 = _sortClosestEdgesByDistancePreferHorizontal()
    //     0xa571c4: bl              #0xa58184  ; [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_sortClosestEdgesByDistancePreferHorizontal
    // 0xa571c8: mov             x1, x0
    // 0xa571cc: r0 = first()
    //     0xa571cc: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xa571d0: b               #0xa57440
    // 0xa571d4: mov             x3, x2
    // 0xa571d8: mov             x2, x0
    // 0xa571dc: d0 = -inf
    //     0xa571dc: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0xa571e0: d1 = inf
    //     0xa571e0: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xa571e4: ldur            x1, [fp, #-0x28]
    // 0xa571e8: r0 = rect()
    //     0xa571e8: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa571ec: ldur            x1, [fp, #-0x20]
    // 0xa571f0: stur            x0, [fp, #-0x40]
    // 0xa571f4: r0 = traversalDescendants()
    //     0xa571f4: bl              #0xa58128  ; [package:flutter/src/widgets/focus_manager.dart] FocusScopeNode::traversalDescendants
    // 0xa571f8: ldur            x1, [fp, #-8]
    // 0xa571fc: ldur            x2, [fp, #-0x18]
    // 0xa57200: ldur            x3, [fp, #-0x40]
    // 0xa57204: mov             x5, x0
    // 0xa57208: r0 = _sortAndFilterHorizontally()
    //     0xa57208: bl              #0xa57d68  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::_sortAndFilterHorizontally
    // 0xa5720c: stur            x0, [fp, #-0x40]
    // 0xa57210: LoadField: r1 = r0->field_b
    //     0xa57210: ldur            w1, [x0, #0xb]
    // 0xa57214: cbnz            w1, #0xa57220
    // 0xa57218: r0 = Null
    //     0xa57218: mov             x0, NULL
    // 0xa5721c: b               #0xa57440
    // 0xa57220: ldur            x1, [fp, #-0x10]
    // 0xa57224: cmp             w1, NULL
    // 0xa57228: b.eq            #0xa572b8
    // 0xa5722c: LoadField: r2 = r1->field_2b
    //     0xa5722c: ldur            w2, [x1, #0x2b]
    // 0xa57230: DecompressPointer r2
    //     0xa57230: add             x2, x2, HEAP, lsl #32
    // 0xa57234: cmp             w2, NULL
    // 0xa57238: b.eq            #0xa574ec
    // 0xa5723c: mov             x1, x2
    // 0xa57240: r0 = atEdge()
    //     0xa57240: bl              #0xa585f8  ; [package:flutter/src/widgets/scroll_position.dart] _ScrollPosition&ViewportOffset&ScrollMetrics::atEdge
    // 0xa57244: tbz             w0, #4, #0xa572b8
    // 0xa57248: ldur            x2, [fp, #-0x38]
    // 0xa5724c: r1 = Function '<anonymous closure>':.
    //     0xa5724c: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e10] AnonymousClosure: (0xa59528), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::inDirection (0xa56dbc)
    //     0xa57250: ldr             x1, [x1, #0xe10]
    // 0xa57254: r0 = AllocateClosure()
    //     0xa57254: bl              #0xec1630  ; AllocateClosureStub
    // 0xa57258: ldur            x1, [fp, #-0x40]
    // 0xa5725c: mov             x2, x0
    // 0xa57260: r0 = where()
    //     0xa57260: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xa57264: mov             x1, x0
    // 0xa57268: stur            x0, [fp, #-0x10]
    // 0xa5726c: r0 = iterator()
    //     0xa5726c: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0xa57270: r1 = LoadClassIdInstr(r0)
    //     0xa57270: ldur            x1, [x0, #-1]
    //     0xa57274: ubfx            x1, x1, #0xc, #0x14
    // 0xa57278: mov             x16, x0
    // 0xa5727c: mov             x0, x1
    // 0xa57280: mov             x1, x16
    // 0xa57284: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xa57284: movz            x17, #0x292d
    //     0xa57288: movk            x17, #0x1, lsl #16
    //     0xa5728c: add             lr, x0, x17
    //     0xa57290: ldr             lr, [x21, lr, lsl #3]
    //     0xa57294: blr             lr
    // 0xa57298: eor             x1, x0, #0x10
    // 0xa5729c: eor             x0, x1, #0x10
    // 0xa572a0: tbnz            w0, #4, #0xa572ac
    // 0xa572a4: ldur            x0, [fp, #-0x10]
    // 0xa572a8: b               #0xa572b0
    // 0xa572ac: ldur            x0, [fp, #-0x40]
    // 0xa572b0: mov             x1, x0
    // 0xa572b4: b               #0xa572bc
    // 0xa572b8: ldur            x1, [fp, #-0x40]
    // 0xa572bc: ldur            x2, [fp, #-0x18]
    // 0xa572c0: r16 = Instance_TraversalDirection
    //     0xa572c0: add             x16, PP, #0x59, lsl #12  ; [pp+0x59e18] Obj!TraversalDirection@e34501
    //     0xa572c4: ldr             x16, [x16, #0xe18]
    // 0xa572c8: cmp             w2, w16
    // 0xa572cc: b.ne            #0xa57310
    // 0xa572d0: r0 = LoadClassIdInstr(r1)
    //     0xa572d0: ldur            x0, [x1, #-1]
    //     0xa572d4: ubfx            x0, x0, #0xc, #0x14
    // 0xa572d8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa572d8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa572dc: r0 = GDT[cid_x0 + 0xd889]()
    //     0xa572dc: movz            x17, #0xd889
    //     0xa572e0: add             lr, x0, x17
    //     0xa572e4: ldr             lr, [x21, lr, lsl #3]
    //     0xa572e8: blr             lr
    // 0xa572ec: stur            x0, [fp, #-0x10]
    // 0xa572f0: LoadField: r1 = r0->field_7
    //     0xa572f0: ldur            w1, [x0, #7]
    // 0xa572f4: DecompressPointer r1
    //     0xa572f4: add             x1, x1, HEAP, lsl #32
    // 0xa572f8: r0 = ReversedListIterable()
    //     0xa572f8: bl              #0x668634  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0xa572fc: mov             x1, x0
    // 0xa57300: ldur            x0, [fp, #-0x10]
    // 0xa57304: StoreField: r1->field_b = r0
    //     0xa57304: stur            w0, [x1, #0xb]
    // 0xa57308: mov             x0, x1
    // 0xa5730c: b               #0xa57314
    // 0xa57310: mov             x0, x1
    // 0xa57314: ldur            x2, [fp, #-0x38]
    // 0xa57318: ldur            x1, [fp, #-0x28]
    // 0xa5731c: stur            x0, [fp, #-0x10]
    // 0xa57320: r0 = rect()
    //     0xa57320: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa57324: LoadField: d0 = r0->field_f
    //     0xa57324: ldur            d0, [x0, #0xf]
    // 0xa57328: ldur            x1, [fp, #-0x28]
    // 0xa5732c: stur            d0, [fp, #-0x50]
    // 0xa57330: r0 = rect()
    //     0xa57330: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa57334: LoadField: d0 = r0->field_1f
    //     0xa57334: ldur            d0, [x0, #0x1f]
    // 0xa57338: stur            d0, [fp, #-0x58]
    // 0xa5733c: r0 = Rect()
    //     0xa5733c: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0xa57340: d0 = -inf
    //     0xa57340: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0xa57344: StoreField: r0->field_7 = d0
    //     0xa57344: stur            d0, [x0, #7]
    // 0xa57348: ldur            d0, [fp, #-0x50]
    // 0xa5734c: StoreField: r0->field_f = d0
    //     0xa5734c: stur            d0, [x0, #0xf]
    // 0xa57350: d0 = inf
    //     0xa57350: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xa57354: ArrayStore: r0[0] = d0  ; List_8
    //     0xa57354: stur            d0, [x0, #0x17]
    // 0xa57358: ldur            d0, [fp, #-0x58]
    // 0xa5735c: StoreField: r0->field_1f = d0
    //     0xa5735c: stur            d0, [x0, #0x1f]
    // 0xa57360: ldur            x2, [fp, #-0x38]
    // 0xa57364: StoreField: r2->field_13 = r0
    //     0xa57364: stur            w0, [x2, #0x13]
    //     0xa57368: ldurb           w16, [x2, #-1]
    //     0xa5736c: ldurb           w17, [x0, #-1]
    //     0xa57370: and             x16, x17, x16, lsr #2
    //     0xa57374: tst             x16, HEAP, lsr #32
    //     0xa57378: b.eq            #0xa57380
    //     0xa5737c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa57380: r1 = Function '<anonymous closure>':.
    //     0xa57380: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e20] AnonymousClosure: (0xa5949c), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::inDirection (0xa56dbc)
    //     0xa57384: ldr             x1, [x1, #0xe20]
    // 0xa57388: r0 = AllocateClosure()
    //     0xa57388: bl              #0xec1630  ; AllocateClosureStub
    // 0xa5738c: ldur            x3, [fp, #-0x10]
    // 0xa57390: r1 = LoadClassIdInstr(r3)
    //     0xa57390: ldur            x1, [x3, #-1]
    //     0xa57394: ubfx            x1, x1, #0xc, #0x14
    // 0xa57398: mov             x2, x0
    // 0xa5739c: mov             x0, x1
    // 0xa573a0: mov             x1, x3
    // 0xa573a4: r0 = GDT[cid_x0 + 0xea28]()
    //     0xa573a4: movz            x17, #0xea28
    //     0xa573a8: add             lr, x0, x17
    //     0xa573ac: ldr             lr, [x21, lr, lsl #3]
    //     0xa573b0: blr             lr
    // 0xa573b4: mov             x1, x0
    // 0xa573b8: stur            x0, [fp, #-0x38]
    // 0xa573bc: r0 = iterator()
    //     0xa573bc: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0xa573c0: r1 = LoadClassIdInstr(r0)
    //     0xa573c0: ldur            x1, [x0, #-1]
    //     0xa573c4: ubfx            x1, x1, #0xc, #0x14
    // 0xa573c8: mov             x16, x0
    // 0xa573cc: mov             x0, x1
    // 0xa573d0: mov             x1, x16
    // 0xa573d4: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xa573d4: movz            x17, #0x292d
    //     0xa573d8: movk            x17, #0x1, lsl #16
    //     0xa573dc: add             lr, x0, x17
    //     0xa573e0: ldr             lr, [x21, lr, lsl #3]
    //     0xa573e4: blr             lr
    // 0xa573e8: eor             x1, x0, #0x10
    // 0xa573ec: eor             x0, x1, #0x10
    // 0xa573f0: tbnz            w0, #4, #0xa5741c
    // 0xa573f4: ldur            x1, [fp, #-0x28]
    // 0xa573f8: r0 = rect()
    //     0xa573f8: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa573fc: mov             x1, x0
    // 0xa57400: r0 = center()
    //     0xa57400: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa57404: mov             x1, x0
    // 0xa57408: ldur            x2, [fp, #-0x38]
    // 0xa5740c: r0 = _sortByDistancePreferHorizontal()
    //     0xa5740c: bl              #0xa57b18  ; [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_sortByDistancePreferHorizontal
    // 0xa57410: mov             x1, x0
    // 0xa57414: r0 = first()
    //     0xa57414: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xa57418: b               #0xa57440
    // 0xa5741c: ldur            x1, [fp, #-0x28]
    // 0xa57420: r0 = rect()
    //     0xa57420: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa57424: mov             x1, x0
    // 0xa57428: r0 = center()
    //     0xa57428: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa5742c: mov             x1, x0
    // 0xa57430: ldur            x2, [fp, #-0x10]
    // 0xa57434: r0 = _sortClosestEdgesByDistancePreferVertical()
    //     0xa57434: bl              #0xa576dc  ; [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_sortClosestEdgesByDistancePreferVertical
    // 0xa57438: mov             x1, x0
    // 0xa5743c: r0 = first()
    //     0xa5743c: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xa57440: stur            x0, [fp, #-0x10]
    // 0xa57444: cmp             w0, NULL
    // 0xa57448: b.eq            #0xa574c8
    // 0xa5744c: ldur            x4, [fp, #-0x30]
    // 0xa57450: ldur            x1, [fp, #-8]
    // 0xa57454: ldur            x2, [fp, #-0x18]
    // 0xa57458: ldur            x3, [fp, #-0x20]
    // 0xa5745c: ldur            x5, [fp, #-0x28]
    // 0xa57460: r0 = _pushPolicyData()
    //     0xa57460: bl              #0xa574f0  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::_pushPolicyData
    // 0xa57464: ldur            x0, [fp, #-0x30]
    // 0xa57468: cmp             x0, #1
    // 0xa5746c: b.gt            #0xa5747c
    // 0xa57470: cmp             x0, #0
    // 0xa57474: b.gt            #0xa57484
    // 0xa57478: b               #0xa574a0
    // 0xa5747c: cmp             x0, #2
    // 0xa57480: b.gt            #0xa574a0
    // 0xa57484: r16 = Instance_ScrollPositionAlignmentPolicy
    //     0xa57484: ldr             x16, [PP, #0x7350]  ; [pp+0x7350] Obj!ScrollPositionAlignmentPolicy@e33bc1
    // 0xa57488: stp             NULL, x16, [SP, #0x10]
    // 0xa5748c: stp             NULL, NULL, [SP]
    // 0xa57490: ldur            x1, [fp, #-0x10]
    // 0xa57494: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, alignmentPolicy, 0x1, curve, 0x4, duration, 0x3, null]
    //     0xa57494: ldr             x4, [PP, #0x74f0]  ; [pp+0x74f0] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "alignmentPolicy", 0x1, "curve", 0x4, "duration", 0x3, Null]
    // 0xa57498: r0 = defaultTraversalRequestFocusCallback()
    //     0xa57498: bl              #0x6b7f0c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::defaultTraversalRequestFocusCallback
    // 0xa5749c: b               #0xa574b8
    // 0xa574a0: r16 = Instance_ScrollPositionAlignmentPolicy
    //     0xa574a0: ldr             x16, [PP, #0x7358]  ; [pp+0x7358] Obj!ScrollPositionAlignmentPolicy@e33ba1
    // 0xa574a4: stp             NULL, x16, [SP, #0x10]
    // 0xa574a8: stp             NULL, NULL, [SP]
    // 0xa574ac: ldur            x1, [fp, #-0x10]
    // 0xa574b0: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, alignmentPolicy, 0x1, curve, 0x4, duration, 0x3, null]
    //     0xa574b0: ldr             x4, [PP, #0x74f0]  ; [pp+0x74f0] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "alignmentPolicy", 0x1, "curve", 0x4, "duration", 0x3, Null]
    // 0xa574b4: r0 = defaultTraversalRequestFocusCallback()
    //     0xa574b4: bl              #0x6b7f0c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::defaultTraversalRequestFocusCallback
    // 0xa574b8: r0 = true
    //     0xa574b8: add             x0, NULL, #0x20  ; true
    // 0xa574bc: LeaveFrame
    //     0xa574bc: mov             SP, fp
    //     0xa574c0: ldp             fp, lr, [SP], #0x10
    // 0xa574c4: ret
    //     0xa574c4: ret             
    // 0xa574c8: r0 = false
    //     0xa574c8: add             x0, NULL, #0x30  ; false
    // 0xa574cc: LeaveFrame
    //     0xa574cc: mov             SP, fp
    //     0xa574d0: ldp             fp, lr, [SP], #0x10
    // 0xa574d4: ret
    //     0xa574d4: ret             
    // 0xa574d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa574d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa574dc: b               #0xa56de8
    // 0xa574e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa574e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa574e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa574e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa574e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa574e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa574ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa574ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _pushPolicyData(/* No info */) {
    // ** addr: 0xa574f0, size: 0x1d4
    // 0xa574f0: EnterFrame
    //     0xa574f0: stp             fp, lr, [SP, #-0x10]!
    //     0xa574f4: mov             fp, SP
    // 0xa574f8: AllocStack(0x38)
    //     0xa574f8: sub             SP, SP, #0x38
    // 0xa574fc: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xa574fc: mov             x0, x3
    //     0xa57500: stur            x3, [fp, #-0x18]
    //     0xa57504: mov             x3, x2
    //     0xa57508: stur            x2, [fp, #-0x10]
    //     0xa5750c: stur            x5, [fp, #-0x20]
    // 0xa57510: CheckStackOverflow
    //     0xa57510: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa57514: cmp             SP, x16
    //     0xa57518: b.ls            #0xa576bc
    // 0xa5751c: LoadField: r4 = r1->field_b
    //     0xa5751c: ldur            w4, [x1, #0xb]
    // 0xa57520: DecompressPointer r4
    //     0xa57520: add             x4, x4, HEAP, lsl #32
    // 0xa57524: mov             x1, x4
    // 0xa57528: mov             x2, x0
    // 0xa5752c: stur            x4, [fp, #-8]
    // 0xa57530: r0 = _getValueOrData()
    //     0xa57530: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa57534: ldur            x1, [fp, #-8]
    // 0xa57538: LoadField: r2 = r1->field_f
    //     0xa57538: ldur            w2, [x1, #0xf]
    // 0xa5753c: DecompressPointer r2
    //     0xa5753c: add             x2, x2, HEAP, lsl #32
    // 0xa57540: cmp             w2, w0
    // 0xa57544: b.ne            #0xa57550
    // 0xa57548: r3 = Null
    //     0xa57548: mov             x3, NULL
    // 0xa5754c: b               #0xa57554
    // 0xa57550: mov             x3, x0
    // 0xa57554: ldur            x2, [fp, #-0x10]
    // 0xa57558: ldur            x0, [fp, #-0x20]
    // 0xa5755c: stur            x3, [fp, #-0x28]
    // 0xa57560: r0 = _DirectionalPolicyDataEntry()
    //     0xa57560: bl              #0xa576d0  ; Allocate_DirectionalPolicyDataEntryStub -> _DirectionalPolicyDataEntry (size=0x10)
    // 0xa57564: mov             x3, x0
    // 0xa57568: ldur            x0, [fp, #-0x10]
    // 0xa5756c: stur            x3, [fp, #-0x30]
    // 0xa57570: StoreField: r3->field_7 = r0
    //     0xa57570: stur            w0, [x3, #7]
    // 0xa57574: ldur            x0, [fp, #-0x20]
    // 0xa57578: StoreField: r3->field_b = r0
    //     0xa57578: stur            w0, [x3, #0xb]
    // 0xa5757c: ldur            x0, [fp, #-0x28]
    // 0xa57580: cmp             w0, NULL
    // 0xa57584: b.eq            #0xa57644
    // 0xa57588: LoadField: r4 = r0->field_7
    //     0xa57588: ldur            w4, [x0, #7]
    // 0xa5758c: DecompressPointer r4
    //     0xa5758c: add             x4, x4, HEAP, lsl #32
    // 0xa57590: stur            x4, [fp, #-0x10]
    // 0xa57594: LoadField: r2 = r4->field_7
    //     0xa57594: ldur            w2, [x4, #7]
    // 0xa57598: DecompressPointer r2
    //     0xa57598: add             x2, x2, HEAP, lsl #32
    // 0xa5759c: mov             x0, x3
    // 0xa575a0: r1 = Null
    //     0xa575a0: mov             x1, NULL
    // 0xa575a4: cmp             w2, NULL
    // 0xa575a8: b.eq            #0xa575c8
    // 0xa575ac: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa575ac: ldur            w4, [x2, #0x17]
    // 0xa575b0: DecompressPointer r4
    //     0xa575b0: add             x4, x4, HEAP, lsl #32
    // 0xa575b4: r8 = X0
    //     0xa575b4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xa575b8: LoadField: r9 = r4->field_7
    //     0xa575b8: ldur            x9, [x4, #7]
    // 0xa575bc: r3 = Null
    //     0xa575bc: add             x3, PP, #0x59, lsl #12  ; [pp+0x59e28] Null
    //     0xa575c0: ldr             x3, [x3, #0xe28]
    // 0xa575c4: blr             x9
    // 0xa575c8: ldur            x0, [fp, #-0x10]
    // 0xa575cc: LoadField: r1 = r0->field_b
    //     0xa575cc: ldur            w1, [x0, #0xb]
    // 0xa575d0: LoadField: r2 = r0->field_f
    //     0xa575d0: ldur            w2, [x0, #0xf]
    // 0xa575d4: DecompressPointer r2
    //     0xa575d4: add             x2, x2, HEAP, lsl #32
    // 0xa575d8: LoadField: r3 = r2->field_b
    //     0xa575d8: ldur            w3, [x2, #0xb]
    // 0xa575dc: r2 = LoadInt32Instr(r1)
    //     0xa575dc: sbfx            x2, x1, #1, #0x1f
    // 0xa575e0: stur            x2, [fp, #-0x38]
    // 0xa575e4: r1 = LoadInt32Instr(r3)
    //     0xa575e4: sbfx            x1, x3, #1, #0x1f
    // 0xa575e8: cmp             x2, x1
    // 0xa575ec: b.ne            #0xa575f8
    // 0xa575f0: mov             x1, x0
    // 0xa575f4: r0 = _growToNextCapacity()
    //     0xa575f4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa575f8: ldur            x0, [fp, #-0x10]
    // 0xa575fc: ldur            x2, [fp, #-0x38]
    // 0xa57600: add             x1, x2, #1
    // 0xa57604: lsl             x3, x1, #1
    // 0xa57608: StoreField: r0->field_b = r3
    //     0xa57608: stur            w3, [x0, #0xb]
    // 0xa5760c: LoadField: r1 = r0->field_f
    //     0xa5760c: ldur            w1, [x0, #0xf]
    // 0xa57610: DecompressPointer r1
    //     0xa57610: add             x1, x1, HEAP, lsl #32
    // 0xa57614: ldur            x0, [fp, #-0x30]
    // 0xa57618: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa57618: add             x25, x1, x2, lsl #2
    //     0xa5761c: add             x25, x25, #0xf
    //     0xa57620: str             w0, [x25]
    //     0xa57624: tbz             w0, #0, #0xa57640
    //     0xa57628: ldurb           w16, [x1, #-1]
    //     0xa5762c: ldurb           w17, [x0, #-1]
    //     0xa57630: and             x16, x17, x16, lsr #2
    //     0xa57634: tst             x16, HEAP, lsr #32
    //     0xa57638: b.eq            #0xa57640
    //     0xa5763c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa57640: b               #0xa576ac
    // 0xa57644: mov             x0, x3
    // 0xa57648: r3 = 2
    //     0xa57648: movz            x3, #0x2
    // 0xa5764c: mov             x2, x3
    // 0xa57650: r1 = Null
    //     0xa57650: mov             x1, NULL
    // 0xa57654: r0 = AllocateArray()
    //     0xa57654: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa57658: mov             x2, x0
    // 0xa5765c: ldur            x0, [fp, #-0x30]
    // 0xa57660: stur            x2, [fp, #-0x10]
    // 0xa57664: StoreField: r2->field_f = r0
    //     0xa57664: stur            w0, [x2, #0xf]
    // 0xa57668: r1 = <_DirectionalPolicyDataEntry>
    //     0xa57668: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e38] TypeArguments: <_DirectionalPolicyDataEntry>
    //     0xa5766c: ldr             x1, [x1, #0xe38]
    // 0xa57670: r0 = AllocateGrowableArray()
    //     0xa57670: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa57674: mov             x1, x0
    // 0xa57678: ldur            x0, [fp, #-0x10]
    // 0xa5767c: stur            x1, [fp, #-0x20]
    // 0xa57680: StoreField: r1->field_f = r0
    //     0xa57680: stur            w0, [x1, #0xf]
    // 0xa57684: r0 = 2
    //     0xa57684: movz            x0, #0x2
    // 0xa57688: StoreField: r1->field_b = r0
    //     0xa57688: stur            w0, [x1, #0xb]
    // 0xa5768c: r0 = _DirectionalPolicyData()
    //     0xa5768c: bl              #0xa576c4  ; Allocate_DirectionalPolicyDataStub -> _DirectionalPolicyData (size=0xc)
    // 0xa57690: mov             x1, x0
    // 0xa57694: ldur            x0, [fp, #-0x20]
    // 0xa57698: StoreField: r1->field_7 = r0
    //     0xa57698: stur            w0, [x1, #7]
    // 0xa5769c: mov             x3, x1
    // 0xa576a0: ldur            x1, [fp, #-8]
    // 0xa576a4: ldur            x2, [fp, #-0x18]
    // 0xa576a8: r0 = []=()
    //     0xa576a8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa576ac: r0 = Null
    //     0xa576ac: mov             x0, NULL
    // 0xa576b0: LeaveFrame
    //     0xa576b0: mov             SP, fp
    //     0xa576b4: ldp             fp, lr, [SP], #0x10
    // 0xa576b8: ret
    //     0xa576b8: ret             
    // 0xa576bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa576bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa576c0: b               #0xa5751c
  }
  _ _sortAndFilterHorizontally(/* No info */) {
    // ** addr: 0xa57d68, size: 0x190
    // 0xa57d68: EnterFrame
    //     0xa57d68: stp             fp, lr, [SP, #-0x10]!
    //     0xa57d6c: mov             fp, SP
    // 0xa57d70: AllocStack(0x30)
    //     0xa57d70: sub             SP, SP, #0x30
    // 0xa57d74: SetupParameters(_WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r1, fp-0x18 */)
    //     0xa57d74: mov             x0, x1
    //     0xa57d78: mov             x1, x5
    //     0xa57d7c: stur            x2, [fp, #-8]
    //     0xa57d80: stur            x3, [fp, #-0x10]
    //     0xa57d84: stur            x5, [fp, #-0x18]
    // 0xa57d88: CheckStackOverflow
    //     0xa57d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa57d8c: cmp             SP, x16
    //     0xa57d90: b.ls            #0xa57ef0
    // 0xa57d94: r1 = 1
    //     0xa57d94: movz            x1, #0x1
    // 0xa57d98: r0 = AllocateContext()
    //     0xa57d98: bl              #0xec126c  ; AllocateContextStub
    // 0xa57d9c: mov             x1, x0
    // 0xa57da0: ldur            x0, [fp, #-0x10]
    // 0xa57da4: StoreField: r1->field_f = r0
    //     0xa57da4: stur            w0, [x1, #0xf]
    // 0xa57da8: ldur            x0, [fp, #-8]
    // 0xa57dac: r16 = Instance_TraversalDirection
    //     0xa57dac: add             x16, PP, #0x59, lsl #12  ; [pp+0x59e18] Obj!TraversalDirection@e34501
    //     0xa57db0: ldr             x16, [x16, #0xe18]
    // 0xa57db4: cmp             w0, w16
    // 0xa57db8: b.ne            #0xa57dd4
    // 0xa57dbc: mov             x2, x1
    // 0xa57dc0: r1 = Function '<anonymous closure>':.
    //     0xa57dc0: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e50] AnonymousClosure: (0xa58084), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::_sortAndFilterHorizontally (0xa57d68)
    //     0xa57dc4: ldr             x1, [x1, #0xe50]
    // 0xa57dc8: r0 = AllocateClosure()
    //     0xa57dc8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa57dcc: mov             x2, x0
    // 0xa57dd0: b               #0xa57e20
    // 0xa57dd4: r16 = Instance_TraversalDirection
    //     0xa57dd4: add             x16, PP, #0x59, lsl #12  ; [pp+0x59e58] Obj!TraversalDirection@e344e1
    //     0xa57dd8: ldr             x16, [x16, #0xe58]
    // 0xa57ddc: cmp             w0, w16
    // 0xa57de0: b.ne            #0xa57dfc
    // 0xa57de4: mov             x2, x1
    // 0xa57de8: r1 = Function '<anonymous closure>':.
    //     0xa57de8: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e60] AnonymousClosure: (0xa57fe0), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::_sortAndFilterHorizontally (0xa57d68)
    //     0xa57dec: ldr             x1, [x1, #0xe60]
    // 0xa57df0: r0 = AllocateClosure()
    //     0xa57df0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa57df4: mov             x2, x0
    // 0xa57df8: b               #0xa57e20
    // 0xa57dfc: r16 = Instance_TraversalDirection
    //     0xa57dfc: add             x16, PP, #0x59, lsl #12  ; [pp+0x59e00] Obj!TraversalDirection@e344a1
    //     0xa57e00: ldr             x16, [x16, #0xe00]
    // 0xa57e04: cmp             w0, w16
    // 0xa57e08: b.eq            #0xa57ea0
    // 0xa57e0c: r16 = Instance_TraversalDirection
    //     0xa57e0c: add             x16, PP, #0x59, lsl #12  ; [pp+0x59e68] Obj!TraversalDirection@e344c1
    //     0xa57e10: ldr             x16, [x16, #0xe68]
    // 0xa57e14: cmp             w0, w16
    // 0xa57e18: b.eq            #0xa57ea0
    // 0xa57e1c: r2 = Null
    //     0xa57e1c: mov             x2, NULL
    // 0xa57e20: ldur            x1, [fp, #-0x18]
    // 0xa57e24: r0 = LoadClassIdInstr(r1)
    //     0xa57e24: ldur            x0, [x1, #-1]
    //     0xa57e28: ubfx            x0, x0, #0xc, #0x14
    // 0xa57e2c: r0 = GDT[cid_x0 + 0xea28]()
    //     0xa57e2c: movz            x17, #0xea28
    //     0xa57e30: add             lr, x0, x17
    //     0xa57e34: ldr             lr, [x21, lr, lsl #3]
    //     0xa57e38: blr             lr
    // 0xa57e3c: r1 = LoadClassIdInstr(r0)
    //     0xa57e3c: ldur            x1, [x0, #-1]
    //     0xa57e40: ubfx            x1, x1, #0xc, #0x14
    // 0xa57e44: mov             x16, x0
    // 0xa57e48: mov             x0, x1
    // 0xa57e4c: mov             x1, x16
    // 0xa57e50: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa57e50: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa57e54: r0 = GDT[cid_x0 + 0xd889]()
    //     0xa57e54: movz            x17, #0xd889
    //     0xa57e58: add             lr, x0, x17
    //     0xa57e5c: ldr             lr, [x21, lr, lsl #3]
    //     0xa57e60: blr             lr
    // 0xa57e64: r1 = Function '<anonymous closure>':.
    //     0xa57e64: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e70] AnonymousClosure: (0xa57ef8), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::_sortAndFilterHorizontally (0xa57d68)
    //     0xa57e68: ldr             x1, [x1, #0xe70]
    // 0xa57e6c: r2 = Null
    //     0xa57e6c: mov             x2, NULL
    // 0xa57e70: stur            x0, [fp, #-0x10]
    // 0xa57e74: r0 = AllocateClosure()
    //     0xa57e74: bl              #0xec1630  ; AllocateClosureStub
    // 0xa57e78: r16 = <FocusNode>
    //     0xa57e78: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0xa57e7c: ldur            lr, [fp, #-0x10]
    // 0xa57e80: stp             lr, x16, [SP, #8]
    // 0xa57e84: str             x0, [SP]
    // 0xa57e88: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa57e88: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa57e8c: r0 = mergeSort()
    //     0xa57e8c: bl              #0x6b61b4  ; [package:flutter/src/foundation/collections.dart] ::mergeSort
    // 0xa57e90: ldur            x0, [fp, #-0x10]
    // 0xa57e94: LeaveFrame
    //     0xa57e94: mov             SP, fp
    //     0xa57e98: ldp             fp, lr, [SP], #0x10
    // 0xa57e9c: ret
    //     0xa57e9c: ret             
    // 0xa57ea0: r1 = Null
    //     0xa57ea0: mov             x1, NULL
    // 0xa57ea4: r2 = 4
    //     0xa57ea4: movz            x2, #0x4
    // 0xa57ea8: r0 = AllocateArray()
    //     0xa57ea8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa57eac: r16 = "Invalid direction "
    //     0xa57eac: add             x16, PP, #0x59, lsl #12  ; [pp+0x59e78] "Invalid direction "
    //     0xa57eb0: ldr             x16, [x16, #0xe78]
    // 0xa57eb4: StoreField: r0->field_f = r16
    //     0xa57eb4: stur            w16, [x0, #0xf]
    // 0xa57eb8: ldur            x1, [fp, #-8]
    // 0xa57ebc: StoreField: r0->field_13 = r1
    //     0xa57ebc: stur            w1, [x0, #0x13]
    // 0xa57ec0: str             x0, [SP]
    // 0xa57ec4: r0 = _interpolate()
    //     0xa57ec4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa57ec8: stur            x0, [fp, #-8]
    // 0xa57ecc: r0 = ArgumentError()
    //     0xa57ecc: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xa57ed0: mov             x1, x0
    // 0xa57ed4: ldur            x0, [fp, #-8]
    // 0xa57ed8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa57ed8: stur            w0, [x1, #0x17]
    // 0xa57edc: r0 = false
    //     0xa57edc: add             x0, NULL, #0x30  ; false
    // 0xa57ee0: StoreField: r1->field_b = r0
    //     0xa57ee0: stur            w0, [x1, #0xb]
    // 0xa57ee4: mov             x0, x1
    // 0xa57ee8: r0 = Throw()
    //     0xa57ee8: bl              #0xec04b8  ; ThrowStub
    // 0xa57eec: brk             #0
    // 0xa57ef0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa57ef0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa57ef4: b               #0xa57d94
  }
  [closure] int <anonymous closure>(dynamic, FocusNode, FocusNode) {
    // ** addr: 0xa57ef8, size: 0xe8
    // 0xa57ef8: EnterFrame
    //     0xa57ef8: stp             fp, lr, [SP, #-0x10]!
    //     0xa57efc: mov             fp, SP
    // 0xa57f00: AllocStack(0x8)
    //     0xa57f00: sub             SP, SP, #8
    // 0xa57f04: CheckStackOverflow
    //     0xa57f04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa57f08: cmp             SP, x16
    //     0xa57f0c: b.ls            #0xa57fa8
    // 0xa57f10: ldr             x1, [fp, #0x18]
    // 0xa57f14: r0 = rect()
    //     0xa57f14: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa57f18: mov             x1, x0
    // 0xa57f1c: r0 = center()
    //     0xa57f1c: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa57f20: LoadField: d0 = r0->field_7
    //     0xa57f20: ldur            d0, [x0, #7]
    // 0xa57f24: ldr             x1, [fp, #0x10]
    // 0xa57f28: stur            d0, [fp, #-8]
    // 0xa57f2c: r0 = rect()
    //     0xa57f2c: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa57f30: mov             x1, x0
    // 0xa57f34: r0 = center()
    //     0xa57f34: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa57f38: LoadField: d0 = r0->field_7
    //     0xa57f38: ldur            d0, [x0, #7]
    // 0xa57f3c: ldur            d1, [fp, #-8]
    // 0xa57f40: r1 = inline_Allocate_Double()
    //     0xa57f40: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa57f44: add             x1, x1, #0x10
    //     0xa57f48: cmp             x0, x1
    //     0xa57f4c: b.ls            #0xa57fb0
    //     0xa57f50: str             x1, [THR, #0x50]  ; THR::top
    //     0xa57f54: sub             x1, x1, #0xf
    //     0xa57f58: movz            x0, #0xe15c
    //     0xa57f5c: movk            x0, #0x3, lsl #16
    //     0xa57f60: stur            x0, [x1, #-1]
    // 0xa57f64: StoreField: r1->field_7 = d1
    //     0xa57f64: stur            d1, [x1, #7]
    // 0xa57f68: r2 = inline_Allocate_Double()
    //     0xa57f68: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xa57f6c: add             x2, x2, #0x10
    //     0xa57f70: cmp             x0, x2
    //     0xa57f74: b.ls            #0xa57fc4
    //     0xa57f78: str             x2, [THR, #0x50]  ; THR::top
    //     0xa57f7c: sub             x2, x2, #0xf
    //     0xa57f80: movz            x0, #0xe15c
    //     0xa57f84: movk            x0, #0x3, lsl #16
    //     0xa57f88: stur            x0, [x2, #-1]
    // 0xa57f8c: StoreField: r2->field_7 = d0
    //     0xa57f8c: stur            d0, [x2, #7]
    // 0xa57f90: r0 = compareTo()
    //     0xa57f90: bl              #0x6da84c  ; [dart:core] _Double::compareTo
    // 0xa57f94: lsl             x1, x0, #1
    // 0xa57f98: mov             x0, x1
    // 0xa57f9c: LeaveFrame
    //     0xa57f9c: mov             SP, fp
    //     0xa57fa0: ldp             fp, lr, [SP], #0x10
    // 0xa57fa4: ret
    //     0xa57fa4: ret             
    // 0xa57fa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa57fa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa57fac: b               #0xa57f10
    // 0xa57fb0: stp             q0, q1, [SP, #-0x20]!
    // 0xa57fb4: r0 = AllocateDouble()
    //     0xa57fb4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa57fb8: mov             x1, x0
    // 0xa57fbc: ldp             q0, q1, [SP], #0x20
    // 0xa57fc0: b               #0xa57f64
    // 0xa57fc4: SaveReg d0
    //     0xa57fc4: str             q0, [SP, #-0x10]!
    // 0xa57fc8: SaveReg r1
    //     0xa57fc8: str             x1, [SP, #-8]!
    // 0xa57fcc: r0 = AllocateDouble()
    //     0xa57fcc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa57fd0: mov             x2, x0
    // 0xa57fd4: RestoreReg r1
    //     0xa57fd4: ldr             x1, [SP], #8
    // 0xa57fd8: RestoreReg d0
    //     0xa57fd8: ldr             q0, [SP], #0x10
    // 0xa57fdc: b               #0xa57f8c
  }
  [closure] bool <anonymous closure>(dynamic, FocusNode) {
    // ** addr: 0xa57fe0, size: 0xa4
    // 0xa57fe0: EnterFrame
    //     0xa57fe0: stp             fp, lr, [SP, #-0x10]!
    //     0xa57fe4: mov             fp, SP
    // 0xa57fe8: AllocStack(0x18)
    //     0xa57fe8: sub             SP, SP, #0x18
    // 0xa57fec: SetupParameters()
    //     0xa57fec: ldr             x0, [fp, #0x18]
    //     0xa57ff0: ldur            w2, [x0, #0x17]
    //     0xa57ff4: add             x2, x2, HEAP, lsl #32
    //     0xa57ff8: stur            x2, [fp, #-8]
    // 0xa57ffc: CheckStackOverflow
    //     0xa57ffc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa58000: cmp             SP, x16
    //     0xa58004: b.ls            #0xa5807c
    // 0xa58008: ldr             x1, [fp, #0x10]
    // 0xa5800c: r0 = rect()
    //     0xa5800c: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa58010: mov             x1, x0
    // 0xa58014: ldur            x0, [fp, #-8]
    // 0xa58018: LoadField: r2 = r0->field_f
    //     0xa58018: ldur            w2, [x0, #0xf]
    // 0xa5801c: DecompressPointer r2
    //     0xa5801c: add             x2, x2, HEAP, lsl #32
    // 0xa58020: stp             x2, x1, [SP]
    // 0xa58024: r0 = ==()
    //     0xa58024: bl              #0xd386f4  ; [dart:ui] Rect::==
    // 0xa58028: tbz             w0, #4, #0xa5806c
    // 0xa5802c: ldur            x0, [fp, #-8]
    // 0xa58030: ldr             x1, [fp, #0x10]
    // 0xa58034: r0 = rect()
    //     0xa58034: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa58038: mov             x1, x0
    // 0xa5803c: r0 = center()
    //     0xa5803c: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa58040: LoadField: d0 = r0->field_7
    //     0xa58040: ldur            d0, [x0, #7]
    // 0xa58044: ldur            x1, [fp, #-8]
    // 0xa58048: LoadField: r2 = r1->field_f
    //     0xa58048: ldur            w2, [x1, #0xf]
    // 0xa5804c: DecompressPointer r2
    //     0xa5804c: add             x2, x2, HEAP, lsl #32
    // 0xa58050: ArrayLoad: d1 = r2[0]  ; List_8
    //     0xa58050: ldur            d1, [x2, #0x17]
    // 0xa58054: fcmp            d0, d1
    // 0xa58058: r16 = true
    //     0xa58058: add             x16, NULL, #0x20  ; true
    // 0xa5805c: r17 = false
    //     0xa5805c: add             x17, NULL, #0x30  ; false
    // 0xa58060: csel            x1, x16, x17, ge
    // 0xa58064: mov             x0, x1
    // 0xa58068: b               #0xa58070
    // 0xa5806c: r0 = false
    //     0xa5806c: add             x0, NULL, #0x30  ; false
    // 0xa58070: LeaveFrame
    //     0xa58070: mov             SP, fp
    //     0xa58074: ldp             fp, lr, [SP], #0x10
    // 0xa58078: ret
    //     0xa58078: ret             
    // 0xa5807c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5807c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa58080: b               #0xa58008
  }
  [closure] bool <anonymous closure>(dynamic, FocusNode) {
    // ** addr: 0xa58084, size: 0xa4
    // 0xa58084: EnterFrame
    //     0xa58084: stp             fp, lr, [SP, #-0x10]!
    //     0xa58088: mov             fp, SP
    // 0xa5808c: AllocStack(0x18)
    //     0xa5808c: sub             SP, SP, #0x18
    // 0xa58090: SetupParameters()
    //     0xa58090: ldr             x0, [fp, #0x18]
    //     0xa58094: ldur            w2, [x0, #0x17]
    //     0xa58098: add             x2, x2, HEAP, lsl #32
    //     0xa5809c: stur            x2, [fp, #-8]
    // 0xa580a0: CheckStackOverflow
    //     0xa580a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa580a4: cmp             SP, x16
    //     0xa580a8: b.ls            #0xa58120
    // 0xa580ac: ldr             x1, [fp, #0x10]
    // 0xa580b0: r0 = rect()
    //     0xa580b0: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa580b4: mov             x1, x0
    // 0xa580b8: ldur            x0, [fp, #-8]
    // 0xa580bc: LoadField: r2 = r0->field_f
    //     0xa580bc: ldur            w2, [x0, #0xf]
    // 0xa580c0: DecompressPointer r2
    //     0xa580c0: add             x2, x2, HEAP, lsl #32
    // 0xa580c4: stp             x2, x1, [SP]
    // 0xa580c8: r0 = ==()
    //     0xa580c8: bl              #0xd386f4  ; [dart:ui] Rect::==
    // 0xa580cc: tbz             w0, #4, #0xa58110
    // 0xa580d0: ldur            x0, [fp, #-8]
    // 0xa580d4: ldr             x1, [fp, #0x10]
    // 0xa580d8: r0 = rect()
    //     0xa580d8: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa580dc: mov             x1, x0
    // 0xa580e0: r0 = center()
    //     0xa580e0: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa580e4: LoadField: d0 = r0->field_7
    //     0xa580e4: ldur            d0, [x0, #7]
    // 0xa580e8: ldur            x1, [fp, #-8]
    // 0xa580ec: LoadField: r2 = r1->field_f
    //     0xa580ec: ldur            w2, [x1, #0xf]
    // 0xa580f0: DecompressPointer r2
    //     0xa580f0: add             x2, x2, HEAP, lsl #32
    // 0xa580f4: LoadField: d1 = r2->field_7
    //     0xa580f4: ldur            d1, [x2, #7]
    // 0xa580f8: fcmp            d1, d0
    // 0xa580fc: r16 = true
    //     0xa580fc: add             x16, NULL, #0x20  ; true
    // 0xa58100: r17 = false
    //     0xa58100: add             x17, NULL, #0x30  ; false
    // 0xa58104: csel            x1, x16, x17, ge
    // 0xa58108: mov             x0, x1
    // 0xa5810c: b               #0xa58114
    // 0xa58110: r0 = false
    //     0xa58110: add             x0, NULL, #0x30  ; false
    // 0xa58114: LeaveFrame
    //     0xa58114: mov             SP, fp
    //     0xa58118: ldp             fp, lr, [SP], #0x10
    // 0xa5811c: ret
    //     0xa5811c: ret             
    // 0xa58120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa58120: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa58124: b               #0xa580ac
  }
  _ _sortAndFilterVertically(/* No info */) {
    // ** addr: 0xa58678, size: 0x190
    // 0xa58678: EnterFrame
    //     0xa58678: stp             fp, lr, [SP, #-0x10]!
    //     0xa5867c: mov             fp, SP
    // 0xa58680: AllocStack(0x30)
    //     0xa58680: sub             SP, SP, #0x30
    // 0xa58684: SetupParameters(_WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r1, fp-0x18 */)
    //     0xa58684: mov             x0, x1
    //     0xa58688: mov             x1, x5
    //     0xa5868c: stur            x2, [fp, #-8]
    //     0xa58690: stur            x3, [fp, #-0x10]
    //     0xa58694: stur            x5, [fp, #-0x18]
    // 0xa58698: CheckStackOverflow
    //     0xa58698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5869c: cmp             SP, x16
    //     0xa586a0: b.ls            #0xa58800
    // 0xa586a4: r1 = 1
    //     0xa586a4: movz            x1, #0x1
    // 0xa586a8: r0 = AllocateContext()
    //     0xa586a8: bl              #0xec126c  ; AllocateContextStub
    // 0xa586ac: mov             x1, x0
    // 0xa586b0: ldur            x0, [fp, #-0x10]
    // 0xa586b4: StoreField: r1->field_f = r0
    //     0xa586b4: stur            w0, [x1, #0xf]
    // 0xa586b8: ldur            x0, [fp, #-8]
    // 0xa586bc: r16 = Instance_TraversalDirection
    //     0xa586bc: add             x16, PP, #0x59, lsl #12  ; [pp+0x59e00] Obj!TraversalDirection@e344a1
    //     0xa586c0: ldr             x16, [x16, #0xe00]
    // 0xa586c4: cmp             w0, w16
    // 0xa586c8: b.ne            #0xa586e4
    // 0xa586cc: mov             x2, x1
    // 0xa586d0: r1 = Function '<anonymous closure>':.
    //     0xa586d0: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e90] AnonymousClosure: (0xa58994), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::_sortAndFilterVertically (0xa58678)
    //     0xa586d4: ldr             x1, [x1, #0xe90]
    // 0xa586d8: r0 = AllocateClosure()
    //     0xa586d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa586dc: mov             x2, x0
    // 0xa586e0: b               #0xa58730
    // 0xa586e4: r16 = Instance_TraversalDirection
    //     0xa586e4: add             x16, PP, #0x59, lsl #12  ; [pp+0x59e68] Obj!TraversalDirection@e344c1
    //     0xa586e8: ldr             x16, [x16, #0xe68]
    // 0xa586ec: cmp             w0, w16
    // 0xa586f0: b.ne            #0xa5870c
    // 0xa586f4: mov             x2, x1
    // 0xa586f8: r1 = Function '<anonymous closure>':.
    //     0xa586f8: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e98] AnonymousClosure: (0xa588f0), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::_sortAndFilterVertically (0xa58678)
    //     0xa586fc: ldr             x1, [x1, #0xe98]
    // 0xa58700: r0 = AllocateClosure()
    //     0xa58700: bl              #0xec1630  ; AllocateClosureStub
    // 0xa58704: mov             x2, x0
    // 0xa58708: b               #0xa58730
    // 0xa5870c: r16 = Instance_TraversalDirection
    //     0xa5870c: add             x16, PP, #0x59, lsl #12  ; [pp+0x59e18] Obj!TraversalDirection@e34501
    //     0xa58710: ldr             x16, [x16, #0xe18]
    // 0xa58714: cmp             w0, w16
    // 0xa58718: b.eq            #0xa587b0
    // 0xa5871c: r16 = Instance_TraversalDirection
    //     0xa5871c: add             x16, PP, #0x59, lsl #12  ; [pp+0x59e58] Obj!TraversalDirection@e344e1
    //     0xa58720: ldr             x16, [x16, #0xe58]
    // 0xa58724: cmp             w0, w16
    // 0xa58728: b.eq            #0xa587b0
    // 0xa5872c: r2 = Null
    //     0xa5872c: mov             x2, NULL
    // 0xa58730: ldur            x1, [fp, #-0x18]
    // 0xa58734: r0 = LoadClassIdInstr(r1)
    //     0xa58734: ldur            x0, [x1, #-1]
    //     0xa58738: ubfx            x0, x0, #0xc, #0x14
    // 0xa5873c: r0 = GDT[cid_x0 + 0xea28]()
    //     0xa5873c: movz            x17, #0xea28
    //     0xa58740: add             lr, x0, x17
    //     0xa58744: ldr             lr, [x21, lr, lsl #3]
    //     0xa58748: blr             lr
    // 0xa5874c: r1 = LoadClassIdInstr(r0)
    //     0xa5874c: ldur            x1, [x0, #-1]
    //     0xa58750: ubfx            x1, x1, #0xc, #0x14
    // 0xa58754: mov             x16, x0
    // 0xa58758: mov             x0, x1
    // 0xa5875c: mov             x1, x16
    // 0xa58760: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa58760: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa58764: r0 = GDT[cid_x0 + 0xd889]()
    //     0xa58764: movz            x17, #0xd889
    //     0xa58768: add             lr, x0, x17
    //     0xa5876c: ldr             lr, [x21, lr, lsl #3]
    //     0xa58770: blr             lr
    // 0xa58774: r1 = Function '<anonymous closure>':.
    //     0xa58774: add             x1, PP, #0x59, lsl #12  ; [pp+0x59ea0] AnonymousClosure: (0xa58808), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::_sortAndFilterVertically (0xa58678)
    //     0xa58778: ldr             x1, [x1, #0xea0]
    // 0xa5877c: r2 = Null
    //     0xa5877c: mov             x2, NULL
    // 0xa58780: stur            x0, [fp, #-0x10]
    // 0xa58784: r0 = AllocateClosure()
    //     0xa58784: bl              #0xec1630  ; AllocateClosureStub
    // 0xa58788: r16 = <FocusNode>
    //     0xa58788: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0xa5878c: ldur            lr, [fp, #-0x10]
    // 0xa58790: stp             lr, x16, [SP, #8]
    // 0xa58794: str             x0, [SP]
    // 0xa58798: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa58798: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa5879c: r0 = mergeSort()
    //     0xa5879c: bl              #0x6b61b4  ; [package:flutter/src/foundation/collections.dart] ::mergeSort
    // 0xa587a0: ldur            x0, [fp, #-0x10]
    // 0xa587a4: LeaveFrame
    //     0xa587a4: mov             SP, fp
    //     0xa587a8: ldp             fp, lr, [SP], #0x10
    // 0xa587ac: ret
    //     0xa587ac: ret             
    // 0xa587b0: r1 = Null
    //     0xa587b0: mov             x1, NULL
    // 0xa587b4: r2 = 4
    //     0xa587b4: movz            x2, #0x4
    // 0xa587b8: r0 = AllocateArray()
    //     0xa587b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa587bc: r16 = "Invalid direction "
    //     0xa587bc: add             x16, PP, #0x59, lsl #12  ; [pp+0x59e78] "Invalid direction "
    //     0xa587c0: ldr             x16, [x16, #0xe78]
    // 0xa587c4: StoreField: r0->field_f = r16
    //     0xa587c4: stur            w16, [x0, #0xf]
    // 0xa587c8: ldur            x1, [fp, #-8]
    // 0xa587cc: StoreField: r0->field_13 = r1
    //     0xa587cc: stur            w1, [x0, #0x13]
    // 0xa587d0: str             x0, [SP]
    // 0xa587d4: r0 = _interpolate()
    //     0xa587d4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa587d8: stur            x0, [fp, #-8]
    // 0xa587dc: r0 = ArgumentError()
    //     0xa587dc: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xa587e0: mov             x1, x0
    // 0xa587e4: ldur            x0, [fp, #-8]
    // 0xa587e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa587e8: stur            w0, [x1, #0x17]
    // 0xa587ec: r0 = false
    //     0xa587ec: add             x0, NULL, #0x30  ; false
    // 0xa587f0: StoreField: r1->field_b = r0
    //     0xa587f0: stur            w0, [x1, #0xb]
    // 0xa587f4: mov             x0, x1
    // 0xa587f8: r0 = Throw()
    //     0xa587f8: bl              #0xec04b8  ; ThrowStub
    // 0xa587fc: brk             #0
    // 0xa58800: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa58800: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa58804: b               #0xa586a4
  }
  [closure] int <anonymous closure>(dynamic, FocusNode, FocusNode) {
    // ** addr: 0xa58808, size: 0xe8
    // 0xa58808: EnterFrame
    //     0xa58808: stp             fp, lr, [SP, #-0x10]!
    //     0xa5880c: mov             fp, SP
    // 0xa58810: AllocStack(0x8)
    //     0xa58810: sub             SP, SP, #8
    // 0xa58814: CheckStackOverflow
    //     0xa58814: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa58818: cmp             SP, x16
    //     0xa5881c: b.ls            #0xa588b8
    // 0xa58820: ldr             x1, [fp, #0x18]
    // 0xa58824: r0 = rect()
    //     0xa58824: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa58828: mov             x1, x0
    // 0xa5882c: r0 = center()
    //     0xa5882c: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa58830: LoadField: d0 = r0->field_f
    //     0xa58830: ldur            d0, [x0, #0xf]
    // 0xa58834: ldr             x1, [fp, #0x10]
    // 0xa58838: stur            d0, [fp, #-8]
    // 0xa5883c: r0 = rect()
    //     0xa5883c: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa58840: mov             x1, x0
    // 0xa58844: r0 = center()
    //     0xa58844: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa58848: LoadField: d0 = r0->field_f
    //     0xa58848: ldur            d0, [x0, #0xf]
    // 0xa5884c: ldur            d1, [fp, #-8]
    // 0xa58850: r1 = inline_Allocate_Double()
    //     0xa58850: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa58854: add             x1, x1, #0x10
    //     0xa58858: cmp             x0, x1
    //     0xa5885c: b.ls            #0xa588c0
    //     0xa58860: str             x1, [THR, #0x50]  ; THR::top
    //     0xa58864: sub             x1, x1, #0xf
    //     0xa58868: movz            x0, #0xe15c
    //     0xa5886c: movk            x0, #0x3, lsl #16
    //     0xa58870: stur            x0, [x1, #-1]
    // 0xa58874: StoreField: r1->field_7 = d1
    //     0xa58874: stur            d1, [x1, #7]
    // 0xa58878: r2 = inline_Allocate_Double()
    //     0xa58878: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xa5887c: add             x2, x2, #0x10
    //     0xa58880: cmp             x0, x2
    //     0xa58884: b.ls            #0xa588d4
    //     0xa58888: str             x2, [THR, #0x50]  ; THR::top
    //     0xa5888c: sub             x2, x2, #0xf
    //     0xa58890: movz            x0, #0xe15c
    //     0xa58894: movk            x0, #0x3, lsl #16
    //     0xa58898: stur            x0, [x2, #-1]
    // 0xa5889c: StoreField: r2->field_7 = d0
    //     0xa5889c: stur            d0, [x2, #7]
    // 0xa588a0: r0 = compareTo()
    //     0xa588a0: bl              #0x6da84c  ; [dart:core] _Double::compareTo
    // 0xa588a4: lsl             x1, x0, #1
    // 0xa588a8: mov             x0, x1
    // 0xa588ac: LeaveFrame
    //     0xa588ac: mov             SP, fp
    //     0xa588b0: ldp             fp, lr, [SP], #0x10
    // 0xa588b4: ret
    //     0xa588b4: ret             
    // 0xa588b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa588b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa588bc: b               #0xa58820
    // 0xa588c0: stp             q0, q1, [SP, #-0x20]!
    // 0xa588c4: r0 = AllocateDouble()
    //     0xa588c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa588c8: mov             x1, x0
    // 0xa588cc: ldp             q0, q1, [SP], #0x20
    // 0xa588d0: b               #0xa58874
    // 0xa588d4: SaveReg d0
    //     0xa588d4: str             q0, [SP, #-0x10]!
    // 0xa588d8: SaveReg r1
    //     0xa588d8: str             x1, [SP, #-8]!
    // 0xa588dc: r0 = AllocateDouble()
    //     0xa588dc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa588e0: mov             x2, x0
    // 0xa588e4: RestoreReg r1
    //     0xa588e4: ldr             x1, [SP], #8
    // 0xa588e8: RestoreReg d0
    //     0xa588e8: ldr             q0, [SP], #0x10
    // 0xa588ec: b               #0xa5889c
  }
  [closure] bool <anonymous closure>(dynamic, FocusNode) {
    // ** addr: 0xa588f0, size: 0xa4
    // 0xa588f0: EnterFrame
    //     0xa588f0: stp             fp, lr, [SP, #-0x10]!
    //     0xa588f4: mov             fp, SP
    // 0xa588f8: AllocStack(0x18)
    //     0xa588f8: sub             SP, SP, #0x18
    // 0xa588fc: SetupParameters()
    //     0xa588fc: ldr             x0, [fp, #0x18]
    //     0xa58900: ldur            w2, [x0, #0x17]
    //     0xa58904: add             x2, x2, HEAP, lsl #32
    //     0xa58908: stur            x2, [fp, #-8]
    // 0xa5890c: CheckStackOverflow
    //     0xa5890c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa58910: cmp             SP, x16
    //     0xa58914: b.ls            #0xa5898c
    // 0xa58918: ldr             x1, [fp, #0x10]
    // 0xa5891c: r0 = rect()
    //     0xa5891c: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa58920: mov             x1, x0
    // 0xa58924: ldur            x0, [fp, #-8]
    // 0xa58928: LoadField: r2 = r0->field_f
    //     0xa58928: ldur            w2, [x0, #0xf]
    // 0xa5892c: DecompressPointer r2
    //     0xa5892c: add             x2, x2, HEAP, lsl #32
    // 0xa58930: stp             x2, x1, [SP]
    // 0xa58934: r0 = ==()
    //     0xa58934: bl              #0xd386f4  ; [dart:ui] Rect::==
    // 0xa58938: tbz             w0, #4, #0xa5897c
    // 0xa5893c: ldur            x0, [fp, #-8]
    // 0xa58940: ldr             x1, [fp, #0x10]
    // 0xa58944: r0 = rect()
    //     0xa58944: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa58948: mov             x1, x0
    // 0xa5894c: r0 = center()
    //     0xa5894c: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa58950: LoadField: d0 = r0->field_f
    //     0xa58950: ldur            d0, [x0, #0xf]
    // 0xa58954: ldur            x1, [fp, #-8]
    // 0xa58958: LoadField: r2 = r1->field_f
    //     0xa58958: ldur            w2, [x1, #0xf]
    // 0xa5895c: DecompressPointer r2
    //     0xa5895c: add             x2, x2, HEAP, lsl #32
    // 0xa58960: LoadField: d1 = r2->field_1f
    //     0xa58960: ldur            d1, [x2, #0x1f]
    // 0xa58964: fcmp            d0, d1
    // 0xa58968: r16 = true
    //     0xa58968: add             x16, NULL, #0x20  ; true
    // 0xa5896c: r17 = false
    //     0xa5896c: add             x17, NULL, #0x30  ; false
    // 0xa58970: csel            x1, x16, x17, ge
    // 0xa58974: mov             x0, x1
    // 0xa58978: b               #0xa58980
    // 0xa5897c: r0 = false
    //     0xa5897c: add             x0, NULL, #0x30  ; false
    // 0xa58980: LeaveFrame
    //     0xa58980: mov             SP, fp
    //     0xa58984: ldp             fp, lr, [SP], #0x10
    // 0xa58988: ret
    //     0xa58988: ret             
    // 0xa5898c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5898c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa58990: b               #0xa58918
  }
  [closure] bool <anonymous closure>(dynamic, FocusNode) {
    // ** addr: 0xa58994, size: 0xa4
    // 0xa58994: EnterFrame
    //     0xa58994: stp             fp, lr, [SP, #-0x10]!
    //     0xa58998: mov             fp, SP
    // 0xa5899c: AllocStack(0x18)
    //     0xa5899c: sub             SP, SP, #0x18
    // 0xa589a0: SetupParameters()
    //     0xa589a0: ldr             x0, [fp, #0x18]
    //     0xa589a4: ldur            w2, [x0, #0x17]
    //     0xa589a8: add             x2, x2, HEAP, lsl #32
    //     0xa589ac: stur            x2, [fp, #-8]
    // 0xa589b0: CheckStackOverflow
    //     0xa589b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa589b4: cmp             SP, x16
    //     0xa589b8: b.ls            #0xa58a30
    // 0xa589bc: ldr             x1, [fp, #0x10]
    // 0xa589c0: r0 = rect()
    //     0xa589c0: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa589c4: mov             x1, x0
    // 0xa589c8: ldur            x0, [fp, #-8]
    // 0xa589cc: LoadField: r2 = r0->field_f
    //     0xa589cc: ldur            w2, [x0, #0xf]
    // 0xa589d0: DecompressPointer r2
    //     0xa589d0: add             x2, x2, HEAP, lsl #32
    // 0xa589d4: stp             x2, x1, [SP]
    // 0xa589d8: r0 = ==()
    //     0xa589d8: bl              #0xd386f4  ; [dart:ui] Rect::==
    // 0xa589dc: tbz             w0, #4, #0xa58a20
    // 0xa589e0: ldur            x0, [fp, #-8]
    // 0xa589e4: ldr             x1, [fp, #0x10]
    // 0xa589e8: r0 = rect()
    //     0xa589e8: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa589ec: mov             x1, x0
    // 0xa589f0: r0 = center()
    //     0xa589f0: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa589f4: LoadField: d0 = r0->field_f
    //     0xa589f4: ldur            d0, [x0, #0xf]
    // 0xa589f8: ldur            x1, [fp, #-8]
    // 0xa589fc: LoadField: r2 = r1->field_f
    //     0xa589fc: ldur            w2, [x1, #0xf]
    // 0xa58a00: DecompressPointer r2
    //     0xa58a00: add             x2, x2, HEAP, lsl #32
    // 0xa58a04: LoadField: d1 = r2->field_f
    //     0xa58a04: ldur            d1, [x2, #0xf]
    // 0xa58a08: fcmp            d1, d0
    // 0xa58a0c: r16 = true
    //     0xa58a0c: add             x16, NULL, #0x20  ; true
    // 0xa58a10: r17 = false
    //     0xa58a10: add             x17, NULL, #0x30  ; false
    // 0xa58a14: csel            x1, x16, x17, ge
    // 0xa58a18: mov             x0, x1
    // 0xa58a1c: b               #0xa58a24
    // 0xa58a20: r0 = false
    //     0xa58a20: add             x0, NULL, #0x30  ; false
    // 0xa58a24: LeaveFrame
    //     0xa58a24: mov             SP, fp
    //     0xa58a28: ldp             fp, lr, [SP], #0x10
    // 0xa58a2c: ret
    //     0xa58a2c: ret             
    // 0xa58a30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa58a30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa58a34: b               #0xa589bc
  }
  _ _popPolicyDataIfNeeded(/* No info */) {
    // ** addr: 0xa58af8, size: 0x2b8
    // 0xa58af8: EnterFrame
    //     0xa58af8: stp             fp, lr, [SP, #-0x10]!
    //     0xa58afc: mov             fp, SP
    // 0xa58b00: AllocStack(0x40)
    //     0xa58b00: sub             SP, SP, #0x40
    // 0xa58b04: SetupParameters(_WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0xa58b04: mov             x0, x2
    //     0xa58b08: stur            x2, [fp, #-0x10]
    //     0xa58b0c: mov             x2, x3
    //     0xa58b10: stur            x1, [fp, #-8]
    //     0xa58b14: stur            x3, [fp, #-0x18]
    // 0xa58b18: CheckStackOverflow
    //     0xa58b18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa58b1c: cmp             SP, x16
    //     0xa58b20: b.ls            #0xa58da8
    // 0xa58b24: r1 = 3
    //     0xa58b24: movz            x1, #0x3
    // 0xa58b28: r0 = AllocateContext()
    //     0xa58b28: bl              #0xec126c  ; AllocateContextStub
    // 0xa58b2c: mov             x3, x0
    // 0xa58b30: ldur            x0, [fp, #-8]
    // 0xa58b34: stur            x3, [fp, #-0x28]
    // 0xa58b38: StoreField: r3->field_f = r0
    //     0xa58b38: stur            w0, [x3, #0xf]
    // 0xa58b3c: ldur            x2, [fp, #-0x18]
    // 0xa58b40: StoreField: r3->field_13 = r2
    //     0xa58b40: stur            w2, [x3, #0x13]
    // 0xa58b44: LoadField: r4 = r0->field_b
    //     0xa58b44: ldur            w4, [x0, #0xb]
    // 0xa58b48: DecompressPointer r4
    //     0xa58b48: add             x4, x4, HEAP, lsl #32
    // 0xa58b4c: mov             x1, x4
    // 0xa58b50: stur            x4, [fp, #-0x20]
    // 0xa58b54: r0 = _getValueOrData()
    //     0xa58b54: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa58b58: mov             x1, x0
    // 0xa58b5c: ldur            x0, [fp, #-0x20]
    // 0xa58b60: LoadField: r2 = r0->field_f
    //     0xa58b60: ldur            w2, [x0, #0xf]
    // 0xa58b64: DecompressPointer r2
    //     0xa58b64: add             x2, x2, HEAP, lsl #32
    // 0xa58b68: cmp             w2, w1
    // 0xa58b6c: b.ne            #0xa58b78
    // 0xa58b70: r3 = Null
    //     0xa58b70: mov             x3, NULL
    // 0xa58b74: b               #0xa58b7c
    // 0xa58b78: mov             x3, x1
    // 0xa58b7c: ldur            x2, [fp, #-0x28]
    // 0xa58b80: mov             x0, x3
    // 0xa58b84: stur            x3, [fp, #-0x20]
    // 0xa58b88: ArrayStore: r2[0] = r0  ; List_4
    //     0xa58b88: stur            w0, [x2, #0x17]
    //     0xa58b8c: ldurb           w16, [x2, #-1]
    //     0xa58b90: ldurb           w17, [x0, #-1]
    //     0xa58b94: and             x16, x17, x16, lsr #2
    //     0xa58b98: tst             x16, HEAP, lsr #32
    //     0xa58b9c: b.eq            #0xa58ba4
    //     0xa58ba0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa58ba4: cmp             w3, NULL
    // 0xa58ba8: b.eq            #0xa58d68
    // 0xa58bac: LoadField: r0 = r3->field_7
    //     0xa58bac: ldur            w0, [x3, #7]
    // 0xa58bb0: DecompressPointer r0
    //     0xa58bb0: add             x0, x0, HEAP, lsl #32
    // 0xa58bb4: stur            x0, [fp, #-0x18]
    // 0xa58bb8: LoadField: r1 = r0->field_b
    //     0xa58bb8: ldur            w1, [x0, #0xb]
    // 0xa58bbc: cbz             w1, #0xa58d68
    // 0xa58bc0: ldur            x4, [fp, #-0x10]
    // 0xa58bc4: mov             x1, x0
    // 0xa58bc8: r0 = first()
    //     0xa58bc8: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xa58bcc: LoadField: r1 = r0->field_7
    //     0xa58bcc: ldur            w1, [x0, #7]
    // 0xa58bd0: DecompressPointer r1
    //     0xa58bd0: add             x1, x1, HEAP, lsl #32
    // 0xa58bd4: ldur            x0, [fp, #-0x10]
    // 0xa58bd8: cmp             w1, w0
    // 0xa58bdc: b.eq            #0xa58d68
    // 0xa58be0: ldur            x1, [fp, #-0x18]
    // 0xa58be4: r0 = last()
    //     0xa58be4: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xa58be8: LoadField: r1 = r0->field_b
    //     0xa58be8: ldur            w1, [x0, #0xb]
    // 0xa58bec: DecompressPointer r1
    //     0xa58bec: add             x1, x1, HEAP, lsl #32
    // 0xa58bf0: LoadField: r0 = r1->field_4f
    //     0xa58bf0: ldur            w0, [x1, #0x4f]
    // 0xa58bf4: DecompressPointer r0
    //     0xa58bf4: add             x0, x0, HEAP, lsl #32
    // 0xa58bf8: cmp             w0, NULL
    // 0xa58bfc: b.ne            #0xa58c24
    // 0xa58c00: ldur            x0, [fp, #-0x28]
    // 0xa58c04: LoadField: r2 = r0->field_13
    //     0xa58c04: ldur            w2, [x0, #0x13]
    // 0xa58c08: DecompressPointer r2
    //     0xa58c08: add             x2, x2, HEAP, lsl #32
    // 0xa58c0c: ldur            x1, [fp, #-8]
    // 0xa58c10: r0 = invalidateScopeData()
    //     0xa58c10: bl              #0x6b8c9c  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::invalidateScopeData
    // 0xa58c14: r0 = false
    //     0xa58c14: add             x0, NULL, #0x30  ; false
    // 0xa58c18: LeaveFrame
    //     0xa58c18: mov             SP, fp
    //     0xa58c1c: ldp             fp, lr, [SP], #0x10
    // 0xa58c20: ret
    //     0xa58c20: ret             
    // 0xa58c24: ldur            x3, [fp, #-0x10]
    // 0xa58c28: ldur            x0, [fp, #-0x28]
    // 0xa58c2c: mov             x2, x0
    // 0xa58c30: r1 = Function 'popOrInvalidate':.
    //     0xa58c30: add             x1, PP, #0x59, lsl #12  ; [pp+0x59eb0] AnonymousClosure: (0xa58db0), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::_popPolicyDataIfNeeded (0xa58af8)
    //     0xa58c34: ldr             x1, [x1, #0xeb0]
    // 0xa58c38: r0 = AllocateClosure()
    //     0xa58c38: bl              #0xec1630  ; AllocateClosureStub
    // 0xa58c3c: mov             x2, x0
    // 0xa58c40: ldur            x0, [fp, #-0x10]
    // 0xa58c44: stur            x2, [fp, #-0x30]
    // 0xa58c48: LoadField: r1 = r0->field_7
    //     0xa58c48: ldur            x1, [x0, #7]
    // 0xa58c4c: cmp             x1, #1
    // 0xa58c50: b.gt            #0xa58c60
    // 0xa58c54: cmp             x1, #0
    // 0xa58c58: b.gt            #0xa58ce8
    // 0xa58c5c: b               #0xa58c68
    // 0xa58c60: cmp             x1, #2
    // 0xa58c64: b.gt            #0xa58ce8
    // 0xa58c68: ldur            x1, [fp, #-0x18]
    // 0xa58c6c: r0 = first()
    //     0xa58c6c: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xa58c70: LoadField: r1 = r0->field_7
    //     0xa58c70: ldur            w1, [x0, #7]
    // 0xa58c74: DecompressPointer r1
    //     0xa58c74: add             x1, x1, HEAP, lsl #32
    // 0xa58c78: LoadField: r0 = r1->field_7
    //     0xa58c78: ldur            x0, [x1, #7]
    // 0xa58c7c: cmp             x0, #1
    // 0xa58c80: b.gt            #0xa58c90
    // 0xa58c84: cmp             x0, #0
    // 0xa58c88: b.gt            #0xa58cd0
    // 0xa58c8c: b               #0xa58c98
    // 0xa58c90: cmp             x0, #2
    // 0xa58c94: b.gt            #0xa58cd0
    // 0xa58c98: ldur            x16, [fp, #-0x30]
    // 0xa58c9c: ldur            lr, [fp, #-0x10]
    // 0xa58ca0: stp             lr, x16, [SP]
    // 0xa58ca4: ldur            x0, [fp, #-0x30]
    // 0xa58ca8: ClosureCall
    //     0xa58ca8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa58cac: ldur            x2, [x0, #0x1f]
    //     0xa58cb0: blr             x2
    // 0xa58cb4: r16 = true
    //     0xa58cb4: add             x16, NULL, #0x20  ; true
    // 0xa58cb8: cmp             w0, w16
    // 0xa58cbc: b.ne            #0xa58d68
    // 0xa58cc0: r0 = true
    //     0xa58cc0: add             x0, NULL, #0x20  ; true
    // 0xa58cc4: LeaveFrame
    //     0xa58cc4: mov             SP, fp
    //     0xa58cc8: ldp             fp, lr, [SP], #0x10
    // 0xa58ccc: ret
    //     0xa58ccc: ret             
    // 0xa58cd0: ldur            x0, [fp, #-0x28]
    // 0xa58cd4: LoadField: r2 = r0->field_13
    //     0xa58cd4: ldur            w2, [x0, #0x13]
    // 0xa58cd8: DecompressPointer r2
    //     0xa58cd8: add             x2, x2, HEAP, lsl #32
    // 0xa58cdc: ldur            x1, [fp, #-8]
    // 0xa58ce0: r0 = invalidateScopeData()
    //     0xa58ce0: bl              #0x6b8c9c  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::invalidateScopeData
    // 0xa58ce4: b               #0xa58d68
    // 0xa58ce8: ldur            x1, [fp, #-0x18]
    // 0xa58cec: r0 = first()
    //     0xa58cec: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xa58cf0: LoadField: r1 = r0->field_7
    //     0xa58cf0: ldur            w1, [x0, #7]
    // 0xa58cf4: DecompressPointer r1
    //     0xa58cf4: add             x1, x1, HEAP, lsl #32
    // 0xa58cf8: LoadField: r0 = r1->field_7
    //     0xa58cf8: ldur            x0, [x1, #7]
    // 0xa58cfc: cmp             x0, #1
    // 0xa58d00: b.gt            #0xa58d10
    // 0xa58d04: cmp             x0, #0
    // 0xa58d08: b.gt            #0xa58d30
    // 0xa58d0c: b               #0xa58d18
    // 0xa58d10: cmp             x0, #2
    // 0xa58d14: b.gt            #0xa58d30
    // 0xa58d18: ldur            x0, [fp, #-0x28]
    // 0xa58d1c: LoadField: r2 = r0->field_13
    //     0xa58d1c: ldur            w2, [x0, #0x13]
    // 0xa58d20: DecompressPointer r2
    //     0xa58d20: add             x2, x2, HEAP, lsl #32
    // 0xa58d24: ldur            x1, [fp, #-8]
    // 0xa58d28: r0 = invalidateScopeData()
    //     0xa58d28: bl              #0x6b8c9c  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::invalidateScopeData
    // 0xa58d2c: b               #0xa58d68
    // 0xa58d30: ldur            x16, [fp, #-0x30]
    // 0xa58d34: ldur            lr, [fp, #-0x10]
    // 0xa58d38: stp             lr, x16, [SP]
    // 0xa58d3c: ldur            x0, [fp, #-0x30]
    // 0xa58d40: ClosureCall
    //     0xa58d40: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa58d44: ldur            x2, [x0, #0x1f]
    //     0xa58d48: blr             x2
    // 0xa58d4c: r16 = true
    //     0xa58d4c: add             x16, NULL, #0x20  ; true
    // 0xa58d50: cmp             w0, w16
    // 0xa58d54: b.ne            #0xa58d68
    // 0xa58d58: r0 = true
    //     0xa58d58: add             x0, NULL, #0x20  ; true
    // 0xa58d5c: LeaveFrame
    //     0xa58d5c: mov             SP, fp
    //     0xa58d60: ldp             fp, lr, [SP], #0x10
    // 0xa58d64: ret
    //     0xa58d64: ret             
    // 0xa58d68: ldur            x0, [fp, #-0x20]
    // 0xa58d6c: cmp             w0, NULL
    // 0xa58d70: b.eq            #0xa58d98
    // 0xa58d74: LoadField: r1 = r0->field_7
    //     0xa58d74: ldur            w1, [x0, #7]
    // 0xa58d78: DecompressPointer r1
    //     0xa58d78: add             x1, x1, HEAP, lsl #32
    // 0xa58d7c: LoadField: r0 = r1->field_b
    //     0xa58d7c: ldur            w0, [x1, #0xb]
    // 0xa58d80: cbnz            w0, #0xa58d98
    // 0xa58d84: ldur            x0, [fp, #-0x28]
    // 0xa58d88: LoadField: r2 = r0->field_13
    //     0xa58d88: ldur            w2, [x0, #0x13]
    // 0xa58d8c: DecompressPointer r2
    //     0xa58d8c: add             x2, x2, HEAP, lsl #32
    // 0xa58d90: ldur            x1, [fp, #-8]
    // 0xa58d94: r0 = invalidateScopeData()
    //     0xa58d94: bl              #0x6b8c9c  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::invalidateScopeData
    // 0xa58d98: r0 = false
    //     0xa58d98: add             x0, NULL, #0x30  ; false
    // 0xa58d9c: LeaveFrame
    //     0xa58d9c: mov             SP, fp
    //     0xa58da0: ldp             fp, lr, [SP], #0x10
    // 0xa58da4: ret
    //     0xa58da4: ret             
    // 0xa58da8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa58da8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa58dac: b               #0xa58b24
  }
  [closure] bool popOrInvalidate(dynamic, TraversalDirection) {
    // ** addr: 0xa58db0, size: 0x1a4
    // 0xa58db0: EnterFrame
    //     0xa58db0: stp             fp, lr, [SP, #-0x10]!
    //     0xa58db4: mov             fp, SP
    // 0xa58db8: AllocStack(0x38)
    //     0xa58db8: sub             SP, SP, #0x38
    // 0xa58dbc: SetupParameters()
    //     0xa58dbc: ldr             x0, [fp, #0x18]
    //     0xa58dc0: ldur            w3, [x0, #0x17]
    //     0xa58dc4: add             x3, x3, HEAP, lsl #32
    //     0xa58dc8: stur            x3, [fp, #-0x10]
    // 0xa58dcc: CheckStackOverflow
    //     0xa58dcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa58dd0: cmp             SP, x16
    //     0xa58dd4: b.ls            #0xa58f30
    // 0xa58dd8: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa58dd8: ldur            w0, [x3, #0x17]
    // 0xa58ddc: DecompressPointer r0
    //     0xa58ddc: add             x0, x0, HEAP, lsl #32
    // 0xa58de0: cmp             w0, NULL
    // 0xa58de4: b.eq            #0xa58f38
    // 0xa58de8: LoadField: r2 = r0->field_7
    //     0xa58de8: ldur            w2, [x0, #7]
    // 0xa58dec: DecompressPointer r2
    //     0xa58dec: add             x2, x2, HEAP, lsl #32
    // 0xa58df0: LoadField: r0 = r2->field_b
    //     0xa58df0: ldur            w0, [x2, #0xb]
    // 0xa58df4: r1 = LoadInt32Instr(r0)
    //     0xa58df4: sbfx            x1, x0, #1, #0x1f
    // 0xa58df8: sub             x4, x1, #1
    // 0xa58dfc: mov             x0, x1
    // 0xa58e00: mov             x1, x4
    // 0xa58e04: cmp             x1, x0
    // 0xa58e08: b.hs            #0xa58f3c
    // 0xa58e0c: LoadField: r0 = r2->field_f
    //     0xa58e0c: ldur            w0, [x2, #0xf]
    // 0xa58e10: DecompressPointer r0
    //     0xa58e10: add             x0, x0, HEAP, lsl #32
    // 0xa58e14: ArrayLoad: r5 = r0[r4]  ; Unknown_4
    //     0xa58e14: add             x16, x0, x4, lsl #2
    //     0xa58e18: ldur            w5, [x16, #0xf]
    // 0xa58e1c: DecompressPointer r5
    //     0xa58e1c: add             x5, x5, HEAP, lsl #32
    // 0xa58e20: mov             x1, x2
    // 0xa58e24: mov             x2, x4
    // 0xa58e28: stur            x5, [fp, #-8]
    // 0xa58e2c: r0 = length=()
    //     0xa58e2c: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xa58e30: ldur            x0, [fp, #-8]
    // 0xa58e34: LoadField: r2 = r0->field_b
    //     0xa58e34: ldur            w2, [x0, #0xb]
    // 0xa58e38: DecompressPointer r2
    //     0xa58e38: add             x2, x2, HEAP, lsl #32
    // 0xa58e3c: stur            x2, [fp, #-0x18]
    // 0xa58e40: LoadField: r1 = r2->field_33
    //     0xa58e40: ldur            w1, [x2, #0x33]
    // 0xa58e44: DecompressPointer r1
    //     0xa58e44: add             x1, x1, HEAP, lsl #32
    // 0xa58e48: cmp             w1, NULL
    // 0xa58e4c: b.eq            #0xa58f40
    // 0xa58e50: r0 = maybeOf()
    //     0xa58e50: bl              #0x6b85f0  ; [package:flutter/src/widgets/scrollable.dart] Scrollable::maybeOf
    // 0xa58e54: stur            x0, [fp, #-8]
    // 0xa58e58: r1 = LoadStaticField(0x7d4)
    //     0xa58e58: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xa58e5c: ldr             x1, [x1, #0xfa8]
    // 0xa58e60: cmp             w1, NULL
    // 0xa58e64: b.eq            #0xa58f44
    // 0xa58e68: LoadField: r2 = r1->field_eb
    //     0xa58e68: ldur            w2, [x1, #0xeb]
    // 0xa58e6c: DecompressPointer r2
    //     0xa58e6c: add             x2, x2, HEAP, lsl #32
    // 0xa58e70: cmp             w2, NULL
    // 0xa58e74: b.eq            #0xa58f48
    // 0xa58e78: LoadField: r1 = r2->field_13
    //     0xa58e78: ldur            w1, [x2, #0x13]
    // 0xa58e7c: DecompressPointer r1
    //     0xa58e7c: add             x1, x1, HEAP, lsl #32
    // 0xa58e80: LoadField: r2 = r1->field_2b
    //     0xa58e80: ldur            w2, [x1, #0x2b]
    // 0xa58e84: DecompressPointer r2
    //     0xa58e84: add             x2, x2, HEAP, lsl #32
    // 0xa58e88: cmp             w2, NULL
    // 0xa58e8c: b.eq            #0xa58f4c
    // 0xa58e90: LoadField: r1 = r2->field_33
    //     0xa58e90: ldur            w1, [x2, #0x33]
    // 0xa58e94: DecompressPointer r1
    //     0xa58e94: add             x1, x1, HEAP, lsl #32
    // 0xa58e98: cmp             w1, NULL
    // 0xa58e9c: b.eq            #0xa58f50
    // 0xa58ea0: r0 = maybeOf()
    //     0xa58ea0: bl              #0x6b85f0  ; [package:flutter/src/widgets/scrollable.dart] Scrollable::maybeOf
    // 0xa58ea4: mov             x1, x0
    // 0xa58ea8: ldur            x0, [fp, #-8]
    // 0xa58eac: cmp             w0, w1
    // 0xa58eb0: b.eq            #0xa58edc
    // 0xa58eb4: ldur            x0, [fp, #-0x10]
    // 0xa58eb8: LoadField: r1 = r0->field_f
    //     0xa58eb8: ldur            w1, [x0, #0xf]
    // 0xa58ebc: DecompressPointer r1
    //     0xa58ebc: add             x1, x1, HEAP, lsl #32
    // 0xa58ec0: LoadField: r2 = r0->field_13
    //     0xa58ec0: ldur            w2, [x0, #0x13]
    // 0xa58ec4: DecompressPointer r2
    //     0xa58ec4: add             x2, x2, HEAP, lsl #32
    // 0xa58ec8: r0 = invalidateScopeData()
    //     0xa58ec8: bl              #0x6b8c9c  ; [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::invalidateScopeData
    // 0xa58ecc: r0 = false
    //     0xa58ecc: add             x0, NULL, #0x30  ; false
    // 0xa58ed0: LeaveFrame
    //     0xa58ed0: mov             SP, fp
    //     0xa58ed4: ldp             fp, lr, [SP], #0x10
    // 0xa58ed8: ret
    //     0xa58ed8: ret             
    // 0xa58edc: ldr             x0, [fp, #0x10]
    // 0xa58ee0: LoadField: r1 = r0->field_7
    //     0xa58ee0: ldur            x1, [x0, #7]
    // 0xa58ee4: cmp             x1, #1
    // 0xa58ee8: b.gt            #0xa58ef8
    // 0xa58eec: cmp             x1, #0
    // 0xa58ef0: b.gt            #0xa58f00
    // 0xa58ef4: b               #0xa58f08
    // 0xa58ef8: cmp             x1, #2
    // 0xa58efc: b.gt            #0xa58f08
    // 0xa58f00: r0 = Instance_ScrollPositionAlignmentPolicy
    //     0xa58f00: ldr             x0, [PP, #0x7350]  ; [pp+0x7350] Obj!ScrollPositionAlignmentPolicy@e33bc1
    // 0xa58f04: b               #0xa58f0c
    // 0xa58f08: r0 = Instance_ScrollPositionAlignmentPolicy
    //     0xa58f08: ldr             x0, [PP, #0x7358]  ; [pp+0x7358] Obj!ScrollPositionAlignmentPolicy@e33ba1
    // 0xa58f0c: stp             NULL, x0, [SP, #0x10]
    // 0xa58f10: stp             NULL, NULL, [SP]
    // 0xa58f14: ldur            x1, [fp, #-0x18]
    // 0xa58f18: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, alignmentPolicy, 0x1, curve, 0x4, duration, 0x3, null]
    //     0xa58f18: ldr             x4, [PP, #0x74f0]  ; [pp+0x74f0] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "alignmentPolicy", 0x1, "curve", 0x4, "duration", 0x3, Null]
    // 0xa58f1c: r0 = defaultTraversalRequestFocusCallback()
    //     0xa58f1c: bl              #0x6b7f0c  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalPolicy::defaultTraversalRequestFocusCallback
    // 0xa58f20: r0 = true
    //     0xa58f20: add             x0, NULL, #0x20  ; true
    // 0xa58f24: LeaveFrame
    //     0xa58f24: mov             SP, fp
    //     0xa58f28: ldp             fp, lr, [SP], #0x10
    // 0xa58f2c: ret
    //     0xa58f2c: ret             
    // 0xa58f30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa58f30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa58f34: b               #0xa58dd8
    // 0xa58f38: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa58f38: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xa58f3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa58f3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa58f40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa58f40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa58f44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa58f44: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa58f48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa58f48: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa58f4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa58f4c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa58f50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa58f50: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ findFirstFocusInDirection(/* No info */) {
    // ** addr: 0xa58f54, size: 0x170
    // 0xa58f54: EnterFrame
    //     0xa58f54: stp             fp, lr, [SP, #-0x10]!
    //     0xa58f58: mov             fp, SP
    // 0xa58f5c: AllocStack(0x30)
    //     0xa58f5c: sub             SP, SP, #0x30
    // 0xa58f60: SetupParameters(_WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin this /* r1 => r0 */, dynamic _ /* r2 => r1 */, dynamic _ /* r3 => r3, fp-0x8 */)
    //     0xa58f60: mov             x0, x1
    //     0xa58f64: mov             x1, x2
    //     0xa58f68: stur            x3, [fp, #-8]
    // 0xa58f6c: CheckStackOverflow
    //     0xa58f6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa58f70: cmp             SP, x16
    //     0xa58f74: b.ls            #0xa590b8
    // 0xa58f78: r0 = LoadClassIdInstr(r1)
    //     0xa58f78: ldur            x0, [x1, #-1]
    //     0xa58f7c: ubfx            x0, x0, #0xc, #0x14
    // 0xa58f80: sub             x16, x0, #0xb67
    // 0xa58f84: cmp             x16, #1
    // 0xa58f88: b.hi            #0xa58f94
    // 0xa58f8c: r0 = enclosingScope()
    //     0xa58f8c: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0xa58f90: b               #0xa58f98
    // 0xa58f94: mov             x0, x1
    // 0xa58f98: stur            x0, [fp, #-0x10]
    // 0xa58f9c: cmp             w0, NULL
    // 0xa58fa0: b.eq            #0xa590c0
    // 0xa58fa4: mov             x1, x0
    // 0xa58fa8: r0 = canRequestFocus()
    //     0xa58fa8: bl              #0x6a8938  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::canRequestFocus
    // 0xa58fac: tbz             w0, #4, #0xa58fbc
    // 0xa58fb0: r1 = Instance_EmptyIterable
    //     0xa58fb0: add             x1, PP, #0x59, lsl #12  ; [pp+0x59df0] Obj!EmptyIterable<FocusNode>@e3a3a1
    //     0xa58fb4: ldr             x1, [x1, #0xdf0]
    // 0xa58fb8: b               #0xa58fc8
    // 0xa58fbc: ldur            x1, [fp, #-0x10]
    // 0xa58fc0: r0 = traversalDescendants()
    //     0xa58fc0: bl              #0xa58a38  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::traversalDescendants
    // 0xa58fc4: mov             x1, x0
    // 0xa58fc8: ldur            x2, [fp, #-8]
    // 0xa58fcc: r0 = LoadClassIdInstr(r1)
    //     0xa58fcc: ldur            x0, [x1, #-1]
    //     0xa58fd0: ubfx            x0, x0, #0xc, #0x14
    // 0xa58fd4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa58fd4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa58fd8: r0 = GDT[cid_x0 + 0xd889]()
    //     0xa58fd8: movz            x17, #0xd889
    //     0xa58fdc: add             lr, x0, x17
    //     0xa58fe0: ldr             lr, [x21, lr, lsl #3]
    //     0xa58fe4: blr             lr
    // 0xa58fe8: stur            x0, [fp, #-0x10]
    // 0xa58fec: r1 = 2
    //     0xa58fec: movz            x1, #0x2
    // 0xa58ff0: r0 = AllocateContext()
    //     0xa58ff0: bl              #0xec126c  ; AllocateContextStub
    // 0xa58ff4: mov             x1, x0
    // 0xa58ff8: ldur            x0, [fp, #-8]
    // 0xa58ffc: stur            x1, [fp, #-0x18]
    // 0xa59000: LoadField: r2 = r0->field_7
    //     0xa59000: ldur            x2, [x0, #7]
    // 0xa59004: cmp             x2, #1
    // 0xa59008: b.gt            #0xa59034
    // 0xa5900c: cmp             x2, #0
    // 0xa59010: b.gt            #0xa59024
    // 0xa59014: r2 = true
    //     0xa59014: add             x2, NULL, #0x20  ; true
    // 0xa59018: r3 = false
    //     0xa59018: add             x3, NULL, #0x30  ; false
    // 0xa5901c: r0 = AllocateRecord2()
    //     0xa5901c: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0xa59020: b               #0xa59058
    // 0xa59024: r2 = false
    //     0xa59024: add             x2, NULL, #0x30  ; false
    // 0xa59028: r3 = true
    //     0xa59028: add             x3, NULL, #0x20  ; true
    // 0xa5902c: r0 = AllocateRecord2()
    //     0xa5902c: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0xa59030: b               #0xa59058
    // 0xa59034: cmp             x2, #2
    // 0xa59038: b.gt            #0xa5904c
    // 0xa5903c: r2 = true
    //     0xa5903c: add             x2, NULL, #0x20  ; true
    // 0xa59040: r3 = true
    //     0xa59040: add             x3, NULL, #0x20  ; true
    // 0xa59044: r0 = AllocateRecord2()
    //     0xa59044: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0xa59048: b               #0xa59058
    // 0xa5904c: r2 = false
    //     0xa5904c: add             x2, NULL, #0x30  ; false
    // 0xa59050: r3 = false
    //     0xa59050: add             x3, NULL, #0x30  ; false
    // 0xa59054: r0 = AllocateRecord2()
    //     0xa59054: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0xa59058: ldur            x2, [fp, #-0x18]
    // 0xa5905c: LoadField: r1 = r0->field_f
    //     0xa5905c: ldur            w1, [x0, #0xf]
    // 0xa59060: DecompressPointer r1
    //     0xa59060: add             x1, x1, HEAP, lsl #32
    // 0xa59064: StoreField: r2->field_f = r1
    //     0xa59064: stur            w1, [x2, #0xf]
    // 0xa59068: LoadField: r1 = r0->field_13
    //     0xa59068: ldur            w1, [x0, #0x13]
    // 0xa5906c: DecompressPointer r1
    //     0xa5906c: add             x1, x1, HEAP, lsl #32
    // 0xa59070: StoreField: r2->field_13 = r1
    //     0xa59070: stur            w1, [x2, #0x13]
    // 0xa59074: r1 = Function '<anonymous closure>':.
    //     0xa59074: add             x1, PP, #0x59, lsl #12  ; [pp+0x59ec0] AnonymousClosure: (0xa5916c), in [package:flutter/src/widgets/focus_traversal.dart] _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin::findFirstFocusInDirection (0xa58f54)
    //     0xa59078: ldr             x1, [x1, #0xec0]
    // 0xa5907c: r0 = AllocateClosure()
    //     0xa5907c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa59080: r16 = <FocusNode>
    //     0xa59080: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0xa59084: ldur            lr, [fp, #-0x10]
    // 0xa59088: stp             lr, x16, [SP, #8]
    // 0xa5908c: str             x0, [SP]
    // 0xa59090: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa59090: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa59094: r0 = mergeSort()
    //     0xa59094: bl              #0x6b61b4  ; [package:flutter/src/foundation/collections.dart] ::mergeSort
    // 0xa59098: r16 = <FocusNode>
    //     0xa59098: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0xa5909c: ldur            lr, [fp, #-0x10]
    // 0xa590a0: stp             lr, x16, [SP]
    // 0xa590a4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa590a4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa590a8: r0 = IterableExtensions.firstOrNull()
    //     0xa590a8: bl              #0xa590c4  ; [dart:collection] ::IterableExtensions.firstOrNull
    // 0xa590ac: LeaveFrame
    //     0xa590ac: mov             SP, fp
    //     0xa590b0: ldp             fp, lr, [SP], #0x10
    // 0xa590b4: ret
    //     0xa590b4: ret             
    // 0xa590b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa590b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa590bc: b               #0xa58f78
    // 0xa590c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa590c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] int <anonymous closure>(dynamic, FocusNode, FocusNode) {
    // ** addr: 0xa5916c, size: 0x330
    // 0xa5916c: EnterFrame
    //     0xa5916c: stp             fp, lr, [SP, #-0x10]!
    //     0xa59170: mov             fp, SP
    // 0xa59174: AllocStack(0x8)
    //     0xa59174: sub             SP, SP, #8
    // 0xa59178: SetupParameters()
    //     0xa59178: ldr             x0, [fp, #0x20]
    //     0xa5917c: ldur            w1, [x0, #0x17]
    //     0xa59180: add             x1, x1, HEAP, lsl #32
    // 0xa59184: CheckStackOverflow
    //     0xa59184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa59188: cmp             SP, x16
    //     0xa5918c: b.ls            #0xa593d4
    // 0xa59190: LoadField: r0 = r1->field_f
    //     0xa59190: ldur            w0, [x1, #0xf]
    // 0xa59194: DecompressPointer r0
    //     0xa59194: add             x0, x0, HEAP, lsl #32
    // 0xa59198: tbnz            w0, #4, #0xa592b8
    // 0xa5919c: LoadField: r0 = r1->field_13
    //     0xa5919c: ldur            w0, [x1, #0x13]
    // 0xa591a0: DecompressPointer r0
    //     0xa591a0: add             x0, x0, HEAP, lsl #32
    // 0xa591a4: tbnz            w0, #4, #0xa59230
    // 0xa591a8: ldr             x1, [fp, #0x18]
    // 0xa591ac: r0 = rect()
    //     0xa591ac: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa591b0: LoadField: d0 = r0->field_f
    //     0xa591b0: ldur            d0, [x0, #0xf]
    // 0xa591b4: ldr             x1, [fp, #0x10]
    // 0xa591b8: stur            d0, [fp, #-8]
    // 0xa591bc: r0 = rect()
    //     0xa591bc: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa591c0: LoadField: d0 = r0->field_f
    //     0xa591c0: ldur            d0, [x0, #0xf]
    // 0xa591c4: ldur            d1, [fp, #-8]
    // 0xa591c8: r1 = inline_Allocate_Double()
    //     0xa591c8: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa591cc: add             x1, x1, #0x10
    //     0xa591d0: cmp             x0, x1
    //     0xa591d4: b.ls            #0xa593dc
    //     0xa591d8: str             x1, [THR, #0x50]  ; THR::top
    //     0xa591dc: sub             x1, x1, #0xf
    //     0xa591e0: movz            x0, #0xe15c
    //     0xa591e4: movk            x0, #0x3, lsl #16
    //     0xa591e8: stur            x0, [x1, #-1]
    // 0xa591ec: StoreField: r1->field_7 = d1
    //     0xa591ec: stur            d1, [x1, #7]
    // 0xa591f0: r2 = inline_Allocate_Double()
    //     0xa591f0: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xa591f4: add             x2, x2, #0x10
    //     0xa591f8: cmp             x0, x2
    //     0xa591fc: b.ls            #0xa593f0
    //     0xa59200: str             x2, [THR, #0x50]  ; THR::top
    //     0xa59204: sub             x2, x2, #0xf
    //     0xa59208: movz            x0, #0xe15c
    //     0xa5920c: movk            x0, #0x3, lsl #16
    //     0xa59210: stur            x0, [x2, #-1]
    // 0xa59214: StoreField: r2->field_7 = d0
    //     0xa59214: stur            d0, [x2, #7]
    // 0xa59218: r0 = compareTo()
    //     0xa59218: bl              #0x6da84c  ; [dart:core] _Double::compareTo
    // 0xa5921c: lsl             x1, x0, #1
    // 0xa59220: mov             x0, x1
    // 0xa59224: LeaveFrame
    //     0xa59224: mov             SP, fp
    //     0xa59228: ldp             fp, lr, [SP], #0x10
    // 0xa5922c: ret
    //     0xa5922c: ret             
    // 0xa59230: ldr             x1, [fp, #0x10]
    // 0xa59234: r0 = rect()
    //     0xa59234: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa59238: LoadField: d0 = r0->field_1f
    //     0xa59238: ldur            d0, [x0, #0x1f]
    // 0xa5923c: ldr             x1, [fp, #0x18]
    // 0xa59240: stur            d0, [fp, #-8]
    // 0xa59244: r0 = rect()
    //     0xa59244: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa59248: LoadField: d0 = r0->field_1f
    //     0xa59248: ldur            d0, [x0, #0x1f]
    // 0xa5924c: ldur            d1, [fp, #-8]
    // 0xa59250: r1 = inline_Allocate_Double()
    //     0xa59250: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa59254: add             x1, x1, #0x10
    //     0xa59258: cmp             x0, x1
    //     0xa5925c: b.ls            #0xa5940c
    //     0xa59260: str             x1, [THR, #0x50]  ; THR::top
    //     0xa59264: sub             x1, x1, #0xf
    //     0xa59268: movz            x0, #0xe15c
    //     0xa5926c: movk            x0, #0x3, lsl #16
    //     0xa59270: stur            x0, [x1, #-1]
    // 0xa59274: StoreField: r1->field_7 = d1
    //     0xa59274: stur            d1, [x1, #7]
    // 0xa59278: r2 = inline_Allocate_Double()
    //     0xa59278: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xa5927c: add             x2, x2, #0x10
    //     0xa59280: cmp             x0, x2
    //     0xa59284: b.ls            #0xa59420
    //     0xa59288: str             x2, [THR, #0x50]  ; THR::top
    //     0xa5928c: sub             x2, x2, #0xf
    //     0xa59290: movz            x0, #0xe15c
    //     0xa59294: movk            x0, #0x3, lsl #16
    //     0xa59298: stur            x0, [x2, #-1]
    // 0xa5929c: StoreField: r2->field_7 = d0
    //     0xa5929c: stur            d0, [x2, #7]
    // 0xa592a0: r0 = compareTo()
    //     0xa592a0: bl              #0x6da84c  ; [dart:core] _Double::compareTo
    // 0xa592a4: lsl             x1, x0, #1
    // 0xa592a8: mov             x0, x1
    // 0xa592ac: LeaveFrame
    //     0xa592ac: mov             SP, fp
    //     0xa592b0: ldp             fp, lr, [SP], #0x10
    // 0xa592b4: ret
    //     0xa592b4: ret             
    // 0xa592b8: LoadField: r0 = r1->field_13
    //     0xa592b8: ldur            w0, [x1, #0x13]
    // 0xa592bc: DecompressPointer r0
    //     0xa592bc: add             x0, x0, HEAP, lsl #32
    // 0xa592c0: tbnz            w0, #4, #0xa5934c
    // 0xa592c4: ldr             x1, [fp, #0x18]
    // 0xa592c8: r0 = rect()
    //     0xa592c8: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa592cc: LoadField: d0 = r0->field_7
    //     0xa592cc: ldur            d0, [x0, #7]
    // 0xa592d0: ldr             x1, [fp, #0x10]
    // 0xa592d4: stur            d0, [fp, #-8]
    // 0xa592d8: r0 = rect()
    //     0xa592d8: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa592dc: LoadField: d0 = r0->field_7
    //     0xa592dc: ldur            d0, [x0, #7]
    // 0xa592e0: ldur            d1, [fp, #-8]
    // 0xa592e4: r1 = inline_Allocate_Double()
    //     0xa592e4: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa592e8: add             x1, x1, #0x10
    //     0xa592ec: cmp             x0, x1
    //     0xa592f0: b.ls            #0xa5943c
    //     0xa592f4: str             x1, [THR, #0x50]  ; THR::top
    //     0xa592f8: sub             x1, x1, #0xf
    //     0xa592fc: movz            x0, #0xe15c
    //     0xa59300: movk            x0, #0x3, lsl #16
    //     0xa59304: stur            x0, [x1, #-1]
    // 0xa59308: StoreField: r1->field_7 = d1
    //     0xa59308: stur            d1, [x1, #7]
    // 0xa5930c: r2 = inline_Allocate_Double()
    //     0xa5930c: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xa59310: add             x2, x2, #0x10
    //     0xa59314: cmp             x0, x2
    //     0xa59318: b.ls            #0xa59450
    //     0xa5931c: str             x2, [THR, #0x50]  ; THR::top
    //     0xa59320: sub             x2, x2, #0xf
    //     0xa59324: movz            x0, #0xe15c
    //     0xa59328: movk            x0, #0x3, lsl #16
    //     0xa5932c: stur            x0, [x2, #-1]
    // 0xa59330: StoreField: r2->field_7 = d0
    //     0xa59330: stur            d0, [x2, #7]
    // 0xa59334: r0 = compareTo()
    //     0xa59334: bl              #0x6da84c  ; [dart:core] _Double::compareTo
    // 0xa59338: lsl             x1, x0, #1
    // 0xa5933c: mov             x0, x1
    // 0xa59340: LeaveFrame
    //     0xa59340: mov             SP, fp
    //     0xa59344: ldp             fp, lr, [SP], #0x10
    // 0xa59348: ret
    //     0xa59348: ret             
    // 0xa5934c: ldr             x1, [fp, #0x10]
    // 0xa59350: r0 = rect()
    //     0xa59350: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa59354: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xa59354: ldur            d0, [x0, #0x17]
    // 0xa59358: ldr             x1, [fp, #0x18]
    // 0xa5935c: stur            d0, [fp, #-8]
    // 0xa59360: r0 = rect()
    //     0xa59360: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa59364: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xa59364: ldur            d0, [x0, #0x17]
    // 0xa59368: ldur            d1, [fp, #-8]
    // 0xa5936c: r1 = inline_Allocate_Double()
    //     0xa5936c: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa59370: add             x1, x1, #0x10
    //     0xa59374: cmp             x0, x1
    //     0xa59378: b.ls            #0xa5946c
    //     0xa5937c: str             x1, [THR, #0x50]  ; THR::top
    //     0xa59380: sub             x1, x1, #0xf
    //     0xa59384: movz            x0, #0xe15c
    //     0xa59388: movk            x0, #0x3, lsl #16
    //     0xa5938c: stur            x0, [x1, #-1]
    // 0xa59390: StoreField: r1->field_7 = d1
    //     0xa59390: stur            d1, [x1, #7]
    // 0xa59394: r2 = inline_Allocate_Double()
    //     0xa59394: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xa59398: add             x2, x2, #0x10
    //     0xa5939c: cmp             x0, x2
    //     0xa593a0: b.ls            #0xa59480
    //     0xa593a4: str             x2, [THR, #0x50]  ; THR::top
    //     0xa593a8: sub             x2, x2, #0xf
    //     0xa593ac: movz            x0, #0xe15c
    //     0xa593b0: movk            x0, #0x3, lsl #16
    //     0xa593b4: stur            x0, [x2, #-1]
    // 0xa593b8: StoreField: r2->field_7 = d0
    //     0xa593b8: stur            d0, [x2, #7]
    // 0xa593bc: r0 = compareTo()
    //     0xa593bc: bl              #0x6da84c  ; [dart:core] _Double::compareTo
    // 0xa593c0: lsl             x1, x0, #1
    // 0xa593c4: mov             x0, x1
    // 0xa593c8: LeaveFrame
    //     0xa593c8: mov             SP, fp
    //     0xa593cc: ldp             fp, lr, [SP], #0x10
    // 0xa593d0: ret
    //     0xa593d0: ret             
    // 0xa593d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa593d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa593d8: b               #0xa59190
    // 0xa593dc: stp             q0, q1, [SP, #-0x20]!
    // 0xa593e0: r0 = AllocateDouble()
    //     0xa593e0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa593e4: mov             x1, x0
    // 0xa593e8: ldp             q0, q1, [SP], #0x20
    // 0xa593ec: b               #0xa591ec
    // 0xa593f0: SaveReg d0
    //     0xa593f0: str             q0, [SP, #-0x10]!
    // 0xa593f4: SaveReg r1
    //     0xa593f4: str             x1, [SP, #-8]!
    // 0xa593f8: r0 = AllocateDouble()
    //     0xa593f8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa593fc: mov             x2, x0
    // 0xa59400: RestoreReg r1
    //     0xa59400: ldr             x1, [SP], #8
    // 0xa59404: RestoreReg d0
    //     0xa59404: ldr             q0, [SP], #0x10
    // 0xa59408: b               #0xa59214
    // 0xa5940c: stp             q0, q1, [SP, #-0x20]!
    // 0xa59410: r0 = AllocateDouble()
    //     0xa59410: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa59414: mov             x1, x0
    // 0xa59418: ldp             q0, q1, [SP], #0x20
    // 0xa5941c: b               #0xa59274
    // 0xa59420: SaveReg d0
    //     0xa59420: str             q0, [SP, #-0x10]!
    // 0xa59424: SaveReg r1
    //     0xa59424: str             x1, [SP, #-8]!
    // 0xa59428: r0 = AllocateDouble()
    //     0xa59428: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa5942c: mov             x2, x0
    // 0xa59430: RestoreReg r1
    //     0xa59430: ldr             x1, [SP], #8
    // 0xa59434: RestoreReg d0
    //     0xa59434: ldr             q0, [SP], #0x10
    // 0xa59438: b               #0xa5929c
    // 0xa5943c: stp             q0, q1, [SP, #-0x20]!
    // 0xa59440: r0 = AllocateDouble()
    //     0xa59440: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa59444: mov             x1, x0
    // 0xa59448: ldp             q0, q1, [SP], #0x20
    // 0xa5944c: b               #0xa59308
    // 0xa59450: SaveReg d0
    //     0xa59450: str             q0, [SP, #-0x10]!
    // 0xa59454: SaveReg r1
    //     0xa59454: str             x1, [SP, #-8]!
    // 0xa59458: r0 = AllocateDouble()
    //     0xa59458: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa5945c: mov             x2, x0
    // 0xa59460: RestoreReg r1
    //     0xa59460: ldr             x1, [SP], #8
    // 0xa59464: RestoreReg d0
    //     0xa59464: ldr             q0, [SP], #0x10
    // 0xa59468: b               #0xa59330
    // 0xa5946c: stp             q0, q1, [SP, #-0x20]!
    // 0xa59470: r0 = AllocateDouble()
    //     0xa59470: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa59474: mov             x1, x0
    // 0xa59478: ldp             q0, q1, [SP], #0x20
    // 0xa5947c: b               #0xa59390
    // 0xa59480: SaveReg d0
    //     0xa59480: str             q0, [SP, #-0x10]!
    // 0xa59484: SaveReg r1
    //     0xa59484: str             x1, [SP, #-8]!
    // 0xa59488: r0 = AllocateDouble()
    //     0xa59488: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa5948c: mov             x2, x0
    // 0xa59490: RestoreReg r1
    //     0xa59490: ldr             x1, [SP], #8
    // 0xa59494: RestoreReg d0
    //     0xa59494: ldr             q0, [SP], #0x10
    // 0xa59498: b               #0xa593b8
  }
  [closure] bool <anonymous closure>(dynamic, FocusNode) {
    // ** addr: 0xa5949c, size: 0x8c
    // 0xa5949c: EnterFrame
    //     0xa5949c: stp             fp, lr, [SP, #-0x10]!
    //     0xa594a0: mov             fp, SP
    // 0xa594a4: AllocStack(0x8)
    //     0xa594a4: sub             SP, SP, #8
    // 0xa594a8: SetupParameters()
    //     0xa594a8: ldr             x0, [fp, #0x18]
    //     0xa594ac: ldur            w2, [x0, #0x17]
    //     0xa594b0: add             x2, x2, HEAP, lsl #32
    //     0xa594b4: stur            x2, [fp, #-8]
    // 0xa594b8: CheckStackOverflow
    //     0xa594b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa594bc: cmp             SP, x16
    //     0xa594c0: b.ls            #0xa59520
    // 0xa594c4: ldr             x1, [fp, #0x10]
    // 0xa594c8: r0 = rect()
    //     0xa594c8: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa594cc: mov             x1, x0
    // 0xa594d0: ldur            x0, [fp, #-8]
    // 0xa594d4: LoadField: r2 = r0->field_13
    //     0xa594d4: ldur            w2, [x0, #0x13]
    // 0xa594d8: DecompressPointer r2
    //     0xa594d8: add             x2, x2, HEAP, lsl #32
    // 0xa594dc: r0 = intersect()
    //     0xa594dc: bl              #0x6b6fa8  ; [dart:ui] Rect::intersect
    // 0xa594e0: LoadField: d0 = r0->field_7
    //     0xa594e0: ldur            d0, [x0, #7]
    // 0xa594e4: ArrayLoad: d1 = r0[0]  ; List_8
    //     0xa594e4: ldur            d1, [x0, #0x17]
    // 0xa594e8: fcmp            d0, d1
    // 0xa594ec: b.lt            #0xa594f8
    // 0xa594f0: r1 = true
    //     0xa594f0: add             x1, NULL, #0x20  ; true
    // 0xa594f4: b               #0xa59510
    // 0xa594f8: LoadField: d0 = r0->field_f
    //     0xa594f8: ldur            d0, [x0, #0xf]
    // 0xa594fc: LoadField: d1 = r0->field_1f
    //     0xa594fc: ldur            d1, [x0, #0x1f]
    // 0xa59500: fcmp            d0, d1
    // 0xa59504: r16 = true
    //     0xa59504: add             x16, NULL, #0x20  ; true
    // 0xa59508: r17 = false
    //     0xa59508: add             x17, NULL, #0x30  ; false
    // 0xa5950c: csel            x1, x16, x17, ge
    // 0xa59510: eor             x0, x1, #0x10
    // 0xa59514: LeaveFrame
    //     0xa59514: mov             SP, fp
    //     0xa59518: ldp             fp, lr, [SP], #0x10
    // 0xa5951c: ret
    //     0xa5951c: ret             
    // 0xa59520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa59520: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa59524: b               #0xa594c4
  }
  [closure] bool <anonymous closure>(dynamic, FocusNode) {
    // ** addr: 0xa59528, size: 0x78
    // 0xa59528: EnterFrame
    //     0xa59528: stp             fp, lr, [SP, #-0x10]!
    //     0xa5952c: mov             fp, SP
    // 0xa59530: AllocStack(0x8)
    //     0xa59530: sub             SP, SP, #8
    // 0xa59534: SetupParameters()
    //     0xa59534: ldr             x0, [fp, #0x18]
    //     0xa59538: ldur            w2, [x0, #0x17]
    //     0xa5953c: add             x2, x2, HEAP, lsl #32
    //     0xa59540: stur            x2, [fp, #-8]
    // 0xa59544: CheckStackOverflow
    //     0xa59544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa59548: cmp             SP, x16
    //     0xa5954c: b.ls            #0xa59594
    // 0xa59550: ldr             x0, [fp, #0x10]
    // 0xa59554: LoadField: r1 = r0->field_33
    //     0xa59554: ldur            w1, [x0, #0x33]
    // 0xa59558: DecompressPointer r1
    //     0xa59558: add             x1, x1, HEAP, lsl #32
    // 0xa5955c: cmp             w1, NULL
    // 0xa59560: b.eq            #0xa5959c
    // 0xa59564: r0 = maybeOf()
    //     0xa59564: bl              #0x6b85f0  ; [package:flutter/src/widgets/scrollable.dart] Scrollable::maybeOf
    // 0xa59568: ldur            x1, [fp, #-8]
    // 0xa5956c: LoadField: r2 = r1->field_f
    //     0xa5956c: ldur            w2, [x1, #0xf]
    // 0xa59570: DecompressPointer r2
    //     0xa59570: add             x2, x2, HEAP, lsl #32
    // 0xa59574: cmp             w0, w2
    // 0xa59578: r16 = true
    //     0xa59578: add             x16, NULL, #0x20  ; true
    // 0xa5957c: r17 = false
    //     0xa5957c: add             x17, NULL, #0x30  ; false
    // 0xa59580: csel            x1, x16, x17, eq
    // 0xa59584: mov             x0, x1
    // 0xa59588: LeaveFrame
    //     0xa59588: mov             SP, fp
    //     0xa5958c: ldp             fp, lr, [SP], #0x10
    // 0xa59590: ret
    //     0xa59590: ret             
    // 0xa59594: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa59594: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa59598: b               #0xa59550
    // 0xa5959c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa5959c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, FocusNode) {
    // ** addr: 0xa595a0, size: 0x8c
    // 0xa595a0: EnterFrame
    //     0xa595a0: stp             fp, lr, [SP, #-0x10]!
    //     0xa595a4: mov             fp, SP
    // 0xa595a8: AllocStack(0x8)
    //     0xa595a8: sub             SP, SP, #8
    // 0xa595ac: SetupParameters()
    //     0xa595ac: ldr             x0, [fp, #0x18]
    //     0xa595b0: ldur            w2, [x0, #0x17]
    //     0xa595b4: add             x2, x2, HEAP, lsl #32
    //     0xa595b8: stur            x2, [fp, #-8]
    // 0xa595bc: CheckStackOverflow
    //     0xa595bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa595c0: cmp             SP, x16
    //     0xa595c4: b.ls            #0xa59624
    // 0xa595c8: ldr             x1, [fp, #0x10]
    // 0xa595cc: r0 = rect()
    //     0xa595cc: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa595d0: mov             x1, x0
    // 0xa595d4: ldur            x0, [fp, #-8]
    // 0xa595d8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xa595d8: ldur            w2, [x0, #0x17]
    // 0xa595dc: DecompressPointer r2
    //     0xa595dc: add             x2, x2, HEAP, lsl #32
    // 0xa595e0: r0 = intersect()
    //     0xa595e0: bl              #0x6b6fa8  ; [dart:ui] Rect::intersect
    // 0xa595e4: LoadField: d0 = r0->field_7
    //     0xa595e4: ldur            d0, [x0, #7]
    // 0xa595e8: ArrayLoad: d1 = r0[0]  ; List_8
    //     0xa595e8: ldur            d1, [x0, #0x17]
    // 0xa595ec: fcmp            d0, d1
    // 0xa595f0: b.lt            #0xa595fc
    // 0xa595f4: r1 = true
    //     0xa595f4: add             x1, NULL, #0x20  ; true
    // 0xa595f8: b               #0xa59614
    // 0xa595fc: LoadField: d0 = r0->field_f
    //     0xa595fc: ldur            d0, [x0, #0xf]
    // 0xa59600: LoadField: d1 = r0->field_1f
    //     0xa59600: ldur            d1, [x0, #0x1f]
    // 0xa59604: fcmp            d0, d1
    // 0xa59608: r16 = true
    //     0xa59608: add             x16, NULL, #0x20  ; true
    // 0xa5960c: r17 = false
    //     0xa5960c: add             x17, NULL, #0x30  ; false
    // 0xa59610: csel            x1, x16, x17, ge
    // 0xa59614: eor             x0, x1, #0x10
    // 0xa59618: LeaveFrame
    //     0xa59618: mov             SP, fp
    //     0xa5961c: ldp             fp, lr, [SP], #0x10
    // 0xa59620: ret
    //     0xa59620: ret             
    // 0xa59624: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa59624: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa59628: b               #0xa595c8
  }
}

// class id: 3791, size: 0x10, field offset: 0x10
class ReadingOrderTraversalPolicy extends _WidgetOrderTraversalPolicy&FocusTraversalPolicy&DirectionalFocusTraversalPolicyMixin {

  _ sortDescendants(/* No info */) {
    // ** addr: 0x6b49e4, size: 0x414
    // 0x6b49e4: EnterFrame
    //     0x6b49e4: stp             fp, lr, [SP, #-0x10]!
    //     0x6b49e8: mov             fp, SP
    // 0x6b49ec: AllocStack(0x48)
    //     0x6b49ec: sub             SP, SP, #0x48
    // 0x6b49f0: SetupParameters(ReadingOrderTraversalPolicy this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x6b49f0: mov             x3, x1
    //     0x6b49f4: mov             x0, x2
    //     0x6b49f8: stur            x1, [fp, #-8]
    //     0x6b49fc: stur            x2, [fp, #-0x10]
    // 0x6b4a00: CheckStackOverflow
    //     0x6b4a00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4a04: cmp             SP, x16
    //     0x6b4a08: b.ls            #0x6b4dd4
    // 0x6b4a0c: LoadField: r1 = r0->field_b
    //     0x6b4a0c: ldur            w1, [x0, #0xb]
    // 0x6b4a10: r2 = LoadInt32Instr(r1)
    //     0x6b4a10: sbfx            x2, x1, #1, #0x1f
    // 0x6b4a14: cmp             x2, #1
    // 0x6b4a18: b.gt            #0x6b4a28
    // 0x6b4a1c: LeaveFrame
    //     0x6b4a1c: mov             SP, fp
    //     0x6b4a20: ldp             fp, lr, [SP], #0x10
    // 0x6b4a24: ret
    //     0x6b4a24: ret             
    // 0x6b4a28: r1 = <_ReadingOrderSortData>
    //     0x6b4a28: ldr             x1, [PP, #0x73a0]  ; [pp+0x73a0] TypeArguments: <_ReadingOrderSortData>
    // 0x6b4a2c: r2 = 0
    //     0x6b4a2c: movz            x2, #0
    // 0x6b4a30: r0 = _GrowableList()
    //     0x6b4a30: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b4a34: mov             x4, x0
    // 0x6b4a38: ldur            x3, [fp, #-0x10]
    // 0x6b4a3c: stur            x4, [fp, #-0x38]
    // 0x6b4a40: LoadField: r5 = r3->field_7
    //     0x6b4a40: ldur            w5, [x3, #7]
    // 0x6b4a44: DecompressPointer r5
    //     0x6b4a44: add             x5, x5, HEAP, lsl #32
    // 0x6b4a48: stur            x5, [fp, #-0x30]
    // 0x6b4a4c: LoadField: r0 = r3->field_b
    //     0x6b4a4c: ldur            w0, [x3, #0xb]
    // 0x6b4a50: r6 = LoadInt32Instr(r0)
    //     0x6b4a50: sbfx            x6, x0, #1, #0x1f
    // 0x6b4a54: stur            x6, [fp, #-0x28]
    // 0x6b4a58: r0 = 0
    //     0x6b4a58: movz            x0, #0
    // 0x6b4a5c: CheckStackOverflow
    //     0x6b4a5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4a60: cmp             SP, x16
    //     0x6b4a64: b.ls            #0x6b4ddc
    // 0x6b4a68: LoadField: r1 = r3->field_b
    //     0x6b4a68: ldur            w1, [x3, #0xb]
    // 0x6b4a6c: r2 = LoadInt32Instr(r1)
    //     0x6b4a6c: sbfx            x2, x1, #1, #0x1f
    // 0x6b4a70: cmp             x6, x2
    // 0x6b4a74: b.ne            #0x6b4db4
    // 0x6b4a78: cmp             x0, x2
    // 0x6b4a7c: b.ge            #0x6b4be0
    // 0x6b4a80: LoadField: r1 = r3->field_f
    //     0x6b4a80: ldur            w1, [x3, #0xf]
    // 0x6b4a84: DecompressPointer r1
    //     0x6b4a84: add             x1, x1, HEAP, lsl #32
    // 0x6b4a88: ArrayLoad: r7 = r1[r0]  ; Unknown_4
    //     0x6b4a88: add             x16, x1, x0, lsl #2
    //     0x6b4a8c: ldur            w7, [x16, #0xf]
    // 0x6b4a90: DecompressPointer r7
    //     0x6b4a90: add             x7, x7, HEAP, lsl #32
    // 0x6b4a94: stur            x7, [fp, #-0x20]
    // 0x6b4a98: add             x8, x0, #1
    // 0x6b4a9c: stur            x8, [fp, #-0x18]
    // 0x6b4aa0: cmp             w7, NULL
    // 0x6b4aa4: b.ne            #0x6b4ad4
    // 0x6b4aa8: mov             x0, x7
    // 0x6b4aac: mov             x2, x5
    // 0x6b4ab0: r1 = Null
    //     0x6b4ab0: mov             x1, NULL
    // 0x6b4ab4: cmp             w2, NULL
    // 0x6b4ab8: b.eq            #0x6b4ad4
    // 0x6b4abc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6b4abc: ldur            w4, [x2, #0x17]
    // 0x6b4ac0: DecompressPointer r4
    //     0x6b4ac0: add             x4, x4, HEAP, lsl #32
    // 0x6b4ac4: r8 = X0
    //     0x6b4ac4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6b4ac8: LoadField: r9 = r4->field_7
    //     0x6b4ac8: ldur            x9, [x4, #7]
    // 0x6b4acc: r3 = Null
    //     0x6b4acc: ldr             x3, [PP, #0x73a8]  ; [pp+0x73a8] Null
    // 0x6b4ad0: blr             x9
    // 0x6b4ad4: ldur            x1, [fp, #-0x38]
    // 0x6b4ad8: ldur            x0, [fp, #-0x20]
    // 0x6b4adc: r0 = _ReadingOrderSortData()
    //     0x6b4adc: bl              #0x6b7424  ; Allocate_ReadingOrderSortDataStub -> _ReadingOrderSortData (size=0x18)
    // 0x6b4ae0: mov             x2, x0
    // 0x6b4ae4: ldur            x0, [fp, #-0x20]
    // 0x6b4ae8: stur            x2, [fp, #-0x40]
    // 0x6b4aec: StoreField: r2->field_f = r0
    //     0x6b4aec: stur            w0, [x2, #0xf]
    // 0x6b4af0: mov             x1, x0
    // 0x6b4af4: r0 = rect()
    //     0x6b4af4: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0x6b4af8: ldur            x2, [fp, #-0x40]
    // 0x6b4afc: StoreField: r2->field_b = r0
    //     0x6b4afc: stur            w0, [x2, #0xb]
    //     0x6b4b00: ldurb           w16, [x2, #-1]
    //     0x6b4b04: ldurb           w17, [x0, #-1]
    //     0x6b4b08: and             x16, x17, x16, lsr #2
    //     0x6b4b0c: tst             x16, HEAP, lsr #32
    //     0x6b4b10: b.eq            #0x6b4b18
    //     0x6b4b14: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6b4b18: ldur            x0, [fp, #-0x20]
    // 0x6b4b1c: LoadField: r1 = r0->field_33
    //     0x6b4b1c: ldur            w1, [x0, #0x33]
    // 0x6b4b20: DecompressPointer r1
    //     0x6b4b20: add             x1, x1, HEAP, lsl #32
    // 0x6b4b24: cmp             w1, NULL
    // 0x6b4b28: b.eq            #0x6b4de4
    // 0x6b4b2c: r0 = _findDirectionality()
    //     0x6b4b2c: bl              #0x6b7258  ; [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderSortData::_findDirectionality
    // 0x6b4b30: ldur            x2, [fp, #-0x40]
    // 0x6b4b34: StoreField: r2->field_7 = r0
    //     0x6b4b34: stur            w0, [x2, #7]
    //     0x6b4b38: ldurb           w16, [x2, #-1]
    //     0x6b4b3c: ldurb           w17, [x0, #-1]
    //     0x6b4b40: and             x16, x17, x16, lsr #2
    //     0x6b4b44: tst             x16, HEAP, lsr #32
    //     0x6b4b48: b.eq            #0x6b4b50
    //     0x6b4b4c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6b4b50: ldur            x0, [fp, #-0x38]
    // 0x6b4b54: LoadField: r1 = r0->field_b
    //     0x6b4b54: ldur            w1, [x0, #0xb]
    // 0x6b4b58: LoadField: r3 = r0->field_f
    //     0x6b4b58: ldur            w3, [x0, #0xf]
    // 0x6b4b5c: DecompressPointer r3
    //     0x6b4b5c: add             x3, x3, HEAP, lsl #32
    // 0x6b4b60: LoadField: r4 = r3->field_b
    //     0x6b4b60: ldur            w4, [x3, #0xb]
    // 0x6b4b64: r3 = LoadInt32Instr(r1)
    //     0x6b4b64: sbfx            x3, x1, #1, #0x1f
    // 0x6b4b68: stur            x3, [fp, #-0x48]
    // 0x6b4b6c: r1 = LoadInt32Instr(r4)
    //     0x6b4b6c: sbfx            x1, x4, #1, #0x1f
    // 0x6b4b70: cmp             x3, x1
    // 0x6b4b74: b.ne            #0x6b4b80
    // 0x6b4b78: mov             x1, x0
    // 0x6b4b7c: r0 = _growToNextCapacity()
    //     0x6b4b7c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b4b80: ldur            x3, [fp, #-0x38]
    // 0x6b4b84: ldur            x2, [fp, #-0x48]
    // 0x6b4b88: add             x0, x2, #1
    // 0x6b4b8c: lsl             x1, x0, #1
    // 0x6b4b90: StoreField: r3->field_b = r1
    //     0x6b4b90: stur            w1, [x3, #0xb]
    // 0x6b4b94: LoadField: r1 = r3->field_f
    //     0x6b4b94: ldur            w1, [x3, #0xf]
    // 0x6b4b98: DecompressPointer r1
    //     0x6b4b98: add             x1, x1, HEAP, lsl #32
    // 0x6b4b9c: ldur            x0, [fp, #-0x40]
    // 0x6b4ba0: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6b4ba0: add             x25, x1, x2, lsl #2
    //     0x6b4ba4: add             x25, x25, #0xf
    //     0x6b4ba8: str             w0, [x25]
    //     0x6b4bac: tbz             w0, #0, #0x6b4bc8
    //     0x6b4bb0: ldurb           w16, [x1, #-1]
    //     0x6b4bb4: ldurb           w17, [x0, #-1]
    //     0x6b4bb8: and             x16, x17, x16, lsr #2
    //     0x6b4bbc: tst             x16, HEAP, lsr #32
    //     0x6b4bc0: b.eq            #0x6b4bc8
    //     0x6b4bc4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6b4bc8: ldur            x0, [fp, #-0x18]
    // 0x6b4bcc: mov             x4, x3
    // 0x6b4bd0: ldur            x3, [fp, #-0x10]
    // 0x6b4bd4: ldur            x5, [fp, #-0x30]
    // 0x6b4bd8: ldur            x6, [fp, #-0x28]
    // 0x6b4bdc: b               #0x6b4a5c
    // 0x6b4be0: mov             x3, x4
    // 0x6b4be4: r1 = <FocusNode>
    //     0x6b4be4: ldr             x1, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0x6b4be8: r2 = 0
    //     0x6b4be8: movz            x2, #0
    // 0x6b4bec: r0 = _GrowableList()
    //     0x6b4bec: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b4bf0: ldur            x1, [fp, #-8]
    // 0x6b4bf4: ldur            x2, [fp, #-0x38]
    // 0x6b4bf8: stur            x0, [fp, #-0x20]
    // 0x6b4bfc: r0 = _pickNext()
    //     0x6b4bfc: bl              #0x6b4e18  ; [package:flutter/src/widgets/focus_traversal.dart] ReadingOrderTraversalPolicy::_pickNext
    // 0x6b4c00: stur            x0, [fp, #-0x40]
    // 0x6b4c04: LoadField: r2 = r0->field_f
    //     0x6b4c04: ldur            w2, [x0, #0xf]
    // 0x6b4c08: DecompressPointer r2
    //     0x6b4c08: add             x2, x2, HEAP, lsl #32
    // 0x6b4c0c: ldur            x3, [fp, #-0x20]
    // 0x6b4c10: stur            x2, [fp, #-0x30]
    // 0x6b4c14: LoadField: r1 = r3->field_b
    //     0x6b4c14: ldur            w1, [x3, #0xb]
    // 0x6b4c18: LoadField: r4 = r3->field_f
    //     0x6b4c18: ldur            w4, [x3, #0xf]
    // 0x6b4c1c: DecompressPointer r4
    //     0x6b4c1c: add             x4, x4, HEAP, lsl #32
    // 0x6b4c20: LoadField: r5 = r4->field_b
    //     0x6b4c20: ldur            w5, [x4, #0xb]
    // 0x6b4c24: r4 = LoadInt32Instr(r1)
    //     0x6b4c24: sbfx            x4, x1, #1, #0x1f
    // 0x6b4c28: stur            x4, [fp, #-0x18]
    // 0x6b4c2c: r1 = LoadInt32Instr(r5)
    //     0x6b4c2c: sbfx            x1, x5, #1, #0x1f
    // 0x6b4c30: cmp             x4, x1
    // 0x6b4c34: b.ne            #0x6b4c40
    // 0x6b4c38: mov             x1, x3
    // 0x6b4c3c: r0 = _growToNextCapacity()
    //     0x6b4c3c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b4c40: ldur            x3, [fp, #-0x20]
    // 0x6b4c44: ldur            x2, [fp, #-0x18]
    // 0x6b4c48: add             x0, x2, #1
    // 0x6b4c4c: lsl             x1, x0, #1
    // 0x6b4c50: StoreField: r3->field_b = r1
    //     0x6b4c50: stur            w1, [x3, #0xb]
    // 0x6b4c54: LoadField: r1 = r3->field_f
    //     0x6b4c54: ldur            w1, [x3, #0xf]
    // 0x6b4c58: DecompressPointer r1
    //     0x6b4c58: add             x1, x1, HEAP, lsl #32
    // 0x6b4c5c: ldur            x0, [fp, #-0x30]
    // 0x6b4c60: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6b4c60: add             x25, x1, x2, lsl #2
    //     0x6b4c64: add             x25, x25, #0xf
    //     0x6b4c68: str             w0, [x25]
    //     0x6b4c6c: tbz             w0, #0, #0x6b4c88
    //     0x6b4c70: ldurb           w16, [x1, #-1]
    //     0x6b4c74: ldurb           w17, [x0, #-1]
    //     0x6b4c78: and             x16, x17, x16, lsr #2
    //     0x6b4c7c: tst             x16, HEAP, lsr #32
    //     0x6b4c80: b.eq            #0x6b4c88
    //     0x6b4c84: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6b4c88: ldur            x1, [fp, #-0x38]
    // 0x6b4c8c: ldur            x2, [fp, #-0x40]
    // 0x6b4c90: r0 = remove()
    //     0x6b4c90: bl              #0x6ec83c  ; [dart:core] _GrowableList::remove
    // 0x6b4c94: ldur            x3, [fp, #-0x38]
    // 0x6b4c98: ldur            x0, [fp, #-0x20]
    // 0x6b4c9c: CheckStackOverflow
    //     0x6b4c9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4ca0: cmp             SP, x16
    //     0x6b4ca4: b.ls            #0x6b4de8
    // 0x6b4ca8: LoadField: r1 = r3->field_b
    //     0x6b4ca8: ldur            w1, [x3, #0xb]
    // 0x6b4cac: cbz             w1, #0x6b4da4
    // 0x6b4cb0: ldur            x1, [fp, #-8]
    // 0x6b4cb4: mov             x2, x3
    // 0x6b4cb8: r0 = _pickNext()
    //     0x6b4cb8: bl              #0x6b4e18  ; [package:flutter/src/widgets/focus_traversal.dart] ReadingOrderTraversalPolicy::_pickNext
    // 0x6b4cbc: stur            x0, [fp, #-0x40]
    // 0x6b4cc0: LoadField: r2 = r0->field_f
    //     0x6b4cc0: ldur            w2, [x0, #0xf]
    // 0x6b4cc4: DecompressPointer r2
    //     0x6b4cc4: add             x2, x2, HEAP, lsl #32
    // 0x6b4cc8: ldur            x3, [fp, #-0x20]
    // 0x6b4ccc: stur            x2, [fp, #-0x30]
    // 0x6b4cd0: LoadField: r1 = r3->field_b
    //     0x6b4cd0: ldur            w1, [x3, #0xb]
    // 0x6b4cd4: LoadField: r4 = r3->field_f
    //     0x6b4cd4: ldur            w4, [x3, #0xf]
    // 0x6b4cd8: DecompressPointer r4
    //     0x6b4cd8: add             x4, x4, HEAP, lsl #32
    // 0x6b4cdc: LoadField: r5 = r4->field_b
    //     0x6b4cdc: ldur            w5, [x4, #0xb]
    // 0x6b4ce0: r4 = LoadInt32Instr(r1)
    //     0x6b4ce0: sbfx            x4, x1, #1, #0x1f
    // 0x6b4ce4: stur            x4, [fp, #-0x18]
    // 0x6b4ce8: r1 = LoadInt32Instr(r5)
    //     0x6b4ce8: sbfx            x1, x5, #1, #0x1f
    // 0x6b4cec: cmp             x4, x1
    // 0x6b4cf0: b.ne            #0x6b4cfc
    // 0x6b4cf4: mov             x1, x3
    // 0x6b4cf8: r0 = _growToNextCapacity()
    //     0x6b4cf8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b4cfc: ldur            x4, [fp, #-0x38]
    // 0x6b4d00: ldur            x3, [fp, #-0x20]
    // 0x6b4d04: ldur            x2, [fp, #-0x18]
    // 0x6b4d08: add             x0, x2, #1
    // 0x6b4d0c: lsl             x1, x0, #1
    // 0x6b4d10: StoreField: r3->field_b = r1
    //     0x6b4d10: stur            w1, [x3, #0xb]
    // 0x6b4d14: LoadField: r1 = r3->field_f
    //     0x6b4d14: ldur            w1, [x3, #0xf]
    // 0x6b4d18: DecompressPointer r1
    //     0x6b4d18: add             x1, x1, HEAP, lsl #32
    // 0x6b4d1c: ldur            x0, [fp, #-0x30]
    // 0x6b4d20: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6b4d20: add             x25, x1, x2, lsl #2
    //     0x6b4d24: add             x25, x25, #0xf
    //     0x6b4d28: str             w0, [x25]
    //     0x6b4d2c: tbz             w0, #0, #0x6b4d48
    //     0x6b4d30: ldurb           w16, [x1, #-1]
    //     0x6b4d34: ldurb           w17, [x0, #-1]
    //     0x6b4d38: and             x16, x17, x16, lsr #2
    //     0x6b4d3c: tst             x16, HEAP, lsr #32
    //     0x6b4d40: b.eq            #0x6b4d48
    //     0x6b4d44: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6b4d48: LoadField: r0 = r4->field_b
    //     0x6b4d48: ldur            w0, [x4, #0xb]
    // 0x6b4d4c: r1 = LoadInt32Instr(r0)
    //     0x6b4d4c: sbfx            x1, x0, #1, #0x1f
    // 0x6b4d50: LoadField: r0 = r4->field_f
    //     0x6b4d50: ldur            w0, [x4, #0xf]
    // 0x6b4d54: DecompressPointer r0
    //     0x6b4d54: add             x0, x0, HEAP, lsl #32
    // 0x6b4d58: ldur            x2, [fp, #-0x40]
    // 0x6b4d5c: r5 = 0
    //     0x6b4d5c: movz            x5, #0
    // 0x6b4d60: CheckStackOverflow
    //     0x6b4d60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4d64: cmp             SP, x16
    //     0x6b4d68: b.ls            #0x6b4df0
    // 0x6b4d6c: cmp             x5, x1
    // 0x6b4d70: b.ge            #0x6b4c94
    // 0x6b4d74: ArrayLoad: r6 = r0[r5]  ; Unknown_4
    //     0x6b4d74: add             x16, x0, x5, lsl #2
    //     0x6b4d78: ldur            w6, [x16, #0xf]
    // 0x6b4d7c: DecompressPointer r6
    //     0x6b4d7c: add             x6, x6, HEAP, lsl #32
    // 0x6b4d80: cmp             w6, w2
    // 0x6b4d84: b.eq            #0x6b4d94
    // 0x6b4d88: add             x6, x5, #1
    // 0x6b4d8c: mov             x5, x6
    // 0x6b4d90: b               #0x6b4d60
    // 0x6b4d94: mov             x1, x4
    // 0x6b4d98: mov             x2, x5
    // 0x6b4d9c: r0 = removeAt()
    //     0x6b4d9c: bl              #0xa8d758  ; [dart:core] _GrowableList::removeAt
    // 0x6b4da0: b               #0x6b4c94
    // 0x6b4da4: ldur            x0, [fp, #-0x20]
    // 0x6b4da8: LeaveFrame
    //     0x6b4da8: mov             SP, fp
    //     0x6b4dac: ldp             fp, lr, [SP], #0x10
    // 0x6b4db0: ret
    //     0x6b4db0: ret             
    // 0x6b4db4: mov             x0, x3
    // 0x6b4db8: r0 = ConcurrentModificationError()
    //     0x6b4db8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6b4dbc: mov             x1, x0
    // 0x6b4dc0: ldur            x0, [fp, #-0x10]
    // 0x6b4dc4: StoreField: r1->field_b = r0
    //     0x6b4dc4: stur            w0, [x1, #0xb]
    // 0x6b4dc8: mov             x0, x1
    // 0x6b4dcc: r0 = Throw()
    //     0x6b4dcc: bl              #0xec04b8  ; ThrowStub
    // 0x6b4dd0: brk             #0
    // 0x6b4dd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4dd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4dd8: b               #0x6b4a0c
    // 0x6b4ddc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4ddc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4de0: b               #0x6b4a68
    // 0x6b4de4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b4de4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b4de8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4de8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4dec: b               #0x6b4ca8
    // 0x6b4df0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4df0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4df4: b               #0x6b4d6c
  }
  _ _pickNext(/* No info */) {
    // ** addr: 0x6b4e18, size: 0x1cc
    // 0x6b4e18: EnterFrame
    //     0x6b4e18: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4e1c: mov             fp, SP
    // 0x6b4e20: AllocStack(0x48)
    //     0x6b4e20: sub             SP, SP, #0x48
    // 0x6b4e24: SetupParameters(ReadingOrderTraversalPolicy this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x6b4e24: mov             x3, x1
    //     0x6b4e28: mov             x0, x2
    //     0x6b4e2c: stur            x1, [fp, #-8]
    //     0x6b4e30: stur            x2, [fp, #-0x10]
    // 0x6b4e34: CheckStackOverflow
    //     0x6b4e34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4e38: cmp             SP, x16
    //     0x6b4e3c: b.ls            #0x6b4fd8
    // 0x6b4e40: r1 = Function '<anonymous closure>':.
    //     0x6b4e40: ldr             x1, [PP, #0x73b8]  ; [pp+0x73b8] AnonymousClosure: (0x6b715c), in [package:flutter/src/widgets/focus_traversal.dart] ReadingOrderTraversalPolicy::_pickNext (0x6b4e18)
    // 0x6b4e44: r2 = Null
    //     0x6b4e44: mov             x2, NULL
    // 0x6b4e48: r0 = AllocateClosure()
    //     0x6b4e48: bl              #0xec1630  ; AllocateClosureStub
    // 0x6b4e4c: r16 = <_ReadingOrderSortData>
    //     0x6b4e4c: ldr             x16, [PP, #0x73a0]  ; [pp+0x73a0] TypeArguments: <_ReadingOrderSortData>
    // 0x6b4e50: ldur            lr, [fp, #-0x10]
    // 0x6b4e54: stp             lr, x16, [SP, #8]
    // 0x6b4e58: str             x0, [SP]
    // 0x6b4e5c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6b4e5c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6b4e60: r0 = mergeSort()
    //     0x6b4e60: bl              #0x6b61b4  ; [package:flutter/src/foundation/collections.dart] ::mergeSort
    // 0x6b4e64: ldur            x1, [fp, #-0x10]
    // 0x6b4e68: r0 = first()
    //     0x6b4e68: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6b4e6c: stur            x0, [fp, #-0x18]
    // 0x6b4e70: LoadField: r1 = r0->field_b
    //     0x6b4e70: ldur            w1, [x0, #0xb]
    // 0x6b4e74: DecompressPointer r1
    //     0x6b4e74: add             x1, x1, HEAP, lsl #32
    // 0x6b4e78: LoadField: d0 = r1->field_f
    //     0x6b4e78: ldur            d0, [x1, #0xf]
    // 0x6b4e7c: stur            d0, [fp, #-0x30]
    // 0x6b4e80: LoadField: d1 = r1->field_1f
    //     0x6b4e80: ldur            d1, [x1, #0x1f]
    // 0x6b4e84: stur            d1, [fp, #-0x28]
    // 0x6b4e88: r0 = Rect()
    //     0x6b4e88: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x6b4e8c: d0 = -inf
    //     0x6b4e8c: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0x6b4e90: stur            x0, [fp, #-0x20]
    // 0x6b4e94: StoreField: r0->field_7 = d0
    //     0x6b4e94: stur            d0, [x0, #7]
    // 0x6b4e98: ldur            d0, [fp, #-0x30]
    // 0x6b4e9c: StoreField: r0->field_f = d0
    //     0x6b4e9c: stur            d0, [x0, #0xf]
    // 0x6b4ea0: d0 = inf
    //     0x6b4ea0: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x6b4ea4: ArrayStore: r0[0] = d0  ; List_8
    //     0x6b4ea4: stur            d0, [x0, #0x17]
    // 0x6b4ea8: ldur            d0, [fp, #-0x28]
    // 0x6b4eac: StoreField: r0->field_1f = d0
    //     0x6b4eac: stur            d0, [x0, #0x1f]
    // 0x6b4eb0: r1 = 1
    //     0x6b4eb0: movz            x1, #0x1
    // 0x6b4eb4: r0 = AllocateContext()
    //     0x6b4eb4: bl              #0xec126c  ; AllocateContextStub
    // 0x6b4eb8: mov             x1, x0
    // 0x6b4ebc: ldur            x0, [fp, #-0x20]
    // 0x6b4ec0: StoreField: r1->field_f = r0
    //     0x6b4ec0: stur            w0, [x1, #0xf]
    // 0x6b4ec4: mov             x2, x1
    // 0x6b4ec8: r1 = Function '<anonymous closure>':.
    //     0x6b4ec8: ldr             x1, [PP, #0x73c0]  ; [pp+0x73c0] AnonymousClosure: (0x6b6f20), of [package:flutter/src/widgets/focus_traversal.dart] ReadingOrderTraversalPolicy
    // 0x6b4ecc: r0 = AllocateClosure()
    //     0x6b4ecc: bl              #0xec1630  ; AllocateClosureStub
    // 0x6b4ed0: ldur            x1, [fp, #-0x10]
    // 0x6b4ed4: mov             x2, x0
    // 0x6b4ed8: r0 = where()
    //     0x6b4ed8: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x6b4edc: r1 = LoadClassIdInstr(r0)
    //     0x6b4edc: ldur            x1, [x0, #-1]
    //     0x6b4ee0: ubfx            x1, x1, #0xc, #0x14
    // 0x6b4ee4: mov             x16, x0
    // 0x6b4ee8: mov             x0, x1
    // 0x6b4eec: mov             x1, x16
    // 0x6b4ef0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b4ef0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b4ef4: r0 = GDT[cid_x0 + 0xd889]()
    //     0x6b4ef4: movz            x17, #0xd889
    //     0x6b4ef8: add             lr, x0, x17
    //     0x6b4efc: ldr             lr, [x21, lr, lsl #3]
    //     0x6b4f00: blr             lr
    // 0x6b4f04: stur            x0, [fp, #-0x10]
    // 0x6b4f08: LoadField: r1 = r0->field_b
    //     0x6b4f08: ldur            w1, [x0, #0xb]
    // 0x6b4f0c: r2 = LoadInt32Instr(r1)
    //     0x6b4f0c: sbfx            x2, x1, #1, #0x1f
    // 0x6b4f10: cmp             x2, #1
    // 0x6b4f14: b.gt            #0x6b4f28
    // 0x6b4f18: ldur            x0, [fp, #-0x18]
    // 0x6b4f1c: LeaveFrame
    //     0x6b4f1c: mov             SP, fp
    //     0x6b4f20: ldp             fp, lr, [SP], #0x10
    // 0x6b4f24: ret
    //     0x6b4f24: ret             
    // 0x6b4f28: mov             x1, x0
    // 0x6b4f2c: r0 = commonDirectionalityOf()
    //     0x6b4f2c: bl              #0x6b5bd4  ; [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderSortData::commonDirectionalityOf
    // 0x6b4f30: stur            x0, [fp, #-0x18]
    // 0x6b4f34: cmp             w0, NULL
    // 0x6b4f38: b.eq            #0x6b4fe0
    // 0x6b4f3c: ldur            x1, [fp, #-0x10]
    // 0x6b4f40: mov             x2, x0
    // 0x6b4f44: r0 = sortWithDirectionality()
    //     0x6b4f44: bl              #0x6b5b64  ; [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderSortData::sortWithDirectionality
    // 0x6b4f48: ldur            x1, [fp, #-8]
    // 0x6b4f4c: ldur            x2, [fp, #-0x10]
    // 0x6b4f50: r0 = _collectDirectionalityGroups()
    //     0x6b4f50: bl              #0x6b546c  ; [package:flutter/src/widgets/focus_traversal.dart] ReadingOrderTraversalPolicy::_collectDirectionalityGroups
    // 0x6b4f54: stur            x0, [fp, #-8]
    // 0x6b4f58: LoadField: r1 = r0->field_b
    //     0x6b4f58: ldur            w1, [x0, #0xb]
    // 0x6b4f5c: cmp             w1, #2
    // 0x6b4f60: b.ne            #0x6b4f98
    // 0x6b4f64: mov             x1, x0
    // 0x6b4f68: r0 = first()
    //     0x6b4f68: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6b4f6c: LoadField: r1 = r0->field_7
    //     0x6b4f6c: ldur            w1, [x0, #7]
    // 0x6b4f70: DecompressPointer r1
    //     0x6b4f70: add             x1, x1, HEAP, lsl #32
    // 0x6b4f74: r0 = LoadClassIdInstr(r1)
    //     0x6b4f74: ldur            x0, [x1, #-1]
    //     0x6b4f78: ubfx            x0, x0, #0xc, #0x14
    // 0x6b4f7c: r0 = GDT[cid_x0 + 0xd20f]()
    //     0x6b4f7c: movz            x17, #0xd20f
    //     0x6b4f80: add             lr, x0, x17
    //     0x6b4f84: ldr             lr, [x21, lr, lsl #3]
    //     0x6b4f88: blr             lr
    // 0x6b4f8c: LeaveFrame
    //     0x6b4f8c: mov             SP, fp
    //     0x6b4f90: ldp             fp, lr, [SP], #0x10
    // 0x6b4f94: ret
    //     0x6b4f94: ret             
    // 0x6b4f98: mov             x1, x0
    // 0x6b4f9c: ldur            x2, [fp, #-0x18]
    // 0x6b4fa0: r0 = sortWithDirectionality()
    //     0x6b4fa0: bl              #0x6b4fe4  ; [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderDirectionalGroupData::sortWithDirectionality
    // 0x6b4fa4: ldur            x1, [fp, #-8]
    // 0x6b4fa8: r0 = first()
    //     0x6b4fa8: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6b4fac: LoadField: r1 = r0->field_7
    //     0x6b4fac: ldur            w1, [x0, #7]
    // 0x6b4fb0: DecompressPointer r1
    //     0x6b4fb0: add             x1, x1, HEAP, lsl #32
    // 0x6b4fb4: r0 = LoadClassIdInstr(r1)
    //     0x6b4fb4: ldur            x0, [x1, #-1]
    //     0x6b4fb8: ubfx            x0, x0, #0xc, #0x14
    // 0x6b4fbc: r0 = GDT[cid_x0 + 0xd20f]()
    //     0x6b4fbc: movz            x17, #0xd20f
    //     0x6b4fc0: add             lr, x0, x17
    //     0x6b4fc4: ldr             lr, [x21, lr, lsl #3]
    //     0x6b4fc8: blr             lr
    // 0x6b4fcc: LeaveFrame
    //     0x6b4fcc: mov             SP, fp
    //     0x6b4fd0: ldp             fp, lr, [SP], #0x10
    // 0x6b4fd4: ret
    //     0x6b4fd4: ret             
    // 0x6b4fd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4fd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4fdc: b               #0x6b4e40
    // 0x6b4fe0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b4fe0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _collectDirectionalityGroups(/* No info */) {
    // ** addr: 0x6b546c, size: 0x4d4
    // 0x6b546c: EnterFrame
    //     0x6b546c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b5470: mov             fp, SP
    // 0x6b5474: AllocStack(0x68)
    //     0x6b5474: sub             SP, SP, #0x68
    // 0x6b5478: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x6b5478: mov             x0, x2
    //     0x6b547c: stur            x2, [fp, #-8]
    // 0x6b5480: CheckStackOverflow
    //     0x6b5480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b5484: cmp             SP, x16
    //     0x6b5488: b.ls            #0x6b5924
    // 0x6b548c: mov             x1, x0
    // 0x6b5490: r0 = first()
    //     0x6b5490: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6b5494: LoadField: r3 = r0->field_7
    //     0x6b5494: ldur            w3, [x0, #7]
    // 0x6b5498: DecompressPointer r3
    //     0x6b5498: add             x3, x3, HEAP, lsl #32
    // 0x6b549c: stur            x3, [fp, #-0x10]
    // 0x6b54a0: r1 = <_ReadingOrderSortData>
    //     0x6b54a0: ldr             x1, [PP, #0x73a0]  ; [pp+0x73a0] TypeArguments: <_ReadingOrderSortData>
    // 0x6b54a4: r2 = 0
    //     0x6b54a4: movz            x2, #0
    // 0x6b54a8: r0 = _GrowableList()
    //     0x6b54a8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b54ac: r1 = <_ReadingOrderDirectionalGroupData>
    //     0x6b54ac: ldr             x1, [PP, #0x73d0]  ; [pp+0x73d0] TypeArguments: <_ReadingOrderDirectionalGroupData>
    // 0x6b54b0: r2 = 0
    //     0x6b54b0: movz            x2, #0
    // 0x6b54b4: stur            x0, [fp, #-0x18]
    // 0x6b54b8: r0 = _GrowableList()
    //     0x6b54b8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b54bc: mov             x4, x0
    // 0x6b54c0: ldur            x3, [fp, #-8]
    // 0x6b54c4: stur            x4, [fp, #-0x40]
    // 0x6b54c8: LoadField: r5 = r3->field_7
    //     0x6b54c8: ldur            w5, [x3, #7]
    // 0x6b54cc: DecompressPointer r5
    //     0x6b54cc: add             x5, x5, HEAP, lsl #32
    // 0x6b54d0: stur            x5, [fp, #-0x38]
    // 0x6b54d4: LoadField: r0 = r3->field_b
    //     0x6b54d4: ldur            w0, [x3, #0xb]
    // 0x6b54d8: r6 = LoadInt32Instr(r0)
    //     0x6b54d8: sbfx            x6, x0, #1, #0x1f
    // 0x6b54dc: stur            x6, [fp, #-0x30]
    // 0x6b54e0: ldur            x8, [fp, #-0x10]
    // 0x6b54e4: ldur            x7, [fp, #-0x18]
    // 0x6b54e8: r0 = 0
    //     0x6b54e8: movz            x0, #0
    // 0x6b54ec: stur            x8, [fp, #-0x18]
    // 0x6b54f0: stur            x7, [fp, #-0x28]
    // 0x6b54f4: CheckStackOverflow
    //     0x6b54f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b54f8: cmp             SP, x16
    //     0x6b54fc: b.ls            #0x6b592c
    // 0x6b5500: LoadField: r1 = r3->field_b
    //     0x6b5500: ldur            w1, [x3, #0xb]
    // 0x6b5504: r2 = LoadInt32Instr(r1)
    //     0x6b5504: sbfx            x2, x1, #1, #0x1f
    // 0x6b5508: cmp             x6, x2
    // 0x6b550c: b.ne            #0x6b5904
    // 0x6b5510: cmp             x0, x2
    // 0x6b5514: b.ge            #0x6b5734
    // 0x6b5518: LoadField: r1 = r3->field_f
    //     0x6b5518: ldur            w1, [x3, #0xf]
    // 0x6b551c: DecompressPointer r1
    //     0x6b551c: add             x1, x1, HEAP, lsl #32
    // 0x6b5520: ArrayLoad: r9 = r1[r0]  ; Unknown_4
    //     0x6b5520: add             x16, x1, x0, lsl #2
    //     0x6b5524: ldur            w9, [x16, #0xf]
    // 0x6b5528: DecompressPointer r9
    //     0x6b5528: add             x9, x9, HEAP, lsl #32
    // 0x6b552c: stur            x9, [fp, #-0x10]
    // 0x6b5530: add             x10, x0, #1
    // 0x6b5534: stur            x10, [fp, #-0x20]
    // 0x6b5538: cmp             w9, NULL
    // 0x6b553c: b.ne            #0x6b556c
    // 0x6b5540: mov             x0, x9
    // 0x6b5544: mov             x2, x5
    // 0x6b5548: r1 = Null
    //     0x6b5548: mov             x1, NULL
    // 0x6b554c: cmp             w2, NULL
    // 0x6b5550: b.eq            #0x6b556c
    // 0x6b5554: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6b5554: ldur            w4, [x2, #0x17]
    // 0x6b5558: DecompressPointer r4
    //     0x6b5558: add             x4, x4, HEAP, lsl #32
    // 0x6b555c: r8 = X0
    //     0x6b555c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6b5560: LoadField: r9 = r4->field_7
    //     0x6b5560: ldur            x9, [x4, #7]
    // 0x6b5564: r3 = Null
    //     0x6b5564: ldr             x3, [PP, #0x73e0]  ; [pp+0x73e0] Null
    // 0x6b5568: blr             x9
    // 0x6b556c: ldur            x3, [fp, #-0x18]
    // 0x6b5570: ldur            x4, [fp, #-0x10]
    // 0x6b5574: LoadField: r0 = r4->field_7
    //     0x6b5574: ldur            w0, [x4, #7]
    // 0x6b5578: DecompressPointer r0
    //     0x6b5578: add             x0, x0, HEAP, lsl #32
    // 0x6b557c: stur            x0, [fp, #-0x50]
    // 0x6b5580: cmp             w0, w3
    // 0x6b5584: b.ne            #0x6b5644
    // 0x6b5588: ldur            x5, [fp, #-0x28]
    // 0x6b558c: LoadField: r2 = r5->field_7
    //     0x6b558c: ldur            w2, [x5, #7]
    // 0x6b5590: DecompressPointer r2
    //     0x6b5590: add             x2, x2, HEAP, lsl #32
    // 0x6b5594: mov             x0, x4
    // 0x6b5598: r1 = Null
    //     0x6b5598: mov             x1, NULL
    // 0x6b559c: cmp             w2, NULL
    // 0x6b55a0: b.eq            #0x6b55bc
    // 0x6b55a4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6b55a4: ldur            w4, [x2, #0x17]
    // 0x6b55a8: DecompressPointer r4
    //     0x6b55a8: add             x4, x4, HEAP, lsl #32
    // 0x6b55ac: r8 = X0
    //     0x6b55ac: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6b55b0: LoadField: r9 = r4->field_7
    //     0x6b55b0: ldur            x9, [x4, #7]
    // 0x6b55b4: r3 = Null
    //     0x6b55b4: ldr             x3, [PP, #0x73f0]  ; [pp+0x73f0] Null
    // 0x6b55b8: blr             x9
    // 0x6b55bc: ldur            x0, [fp, #-0x28]
    // 0x6b55c0: LoadField: r1 = r0->field_b
    //     0x6b55c0: ldur            w1, [x0, #0xb]
    // 0x6b55c4: LoadField: r2 = r0->field_f
    //     0x6b55c4: ldur            w2, [x0, #0xf]
    // 0x6b55c8: DecompressPointer r2
    //     0x6b55c8: add             x2, x2, HEAP, lsl #32
    // 0x6b55cc: LoadField: r3 = r2->field_b
    //     0x6b55cc: ldur            w3, [x2, #0xb]
    // 0x6b55d0: r2 = LoadInt32Instr(r1)
    //     0x6b55d0: sbfx            x2, x1, #1, #0x1f
    // 0x6b55d4: stur            x2, [fp, #-0x48]
    // 0x6b55d8: r1 = LoadInt32Instr(r3)
    //     0x6b55d8: sbfx            x1, x3, #1, #0x1f
    // 0x6b55dc: cmp             x2, x1
    // 0x6b55e0: b.ne            #0x6b55ec
    // 0x6b55e4: mov             x1, x0
    // 0x6b55e8: r0 = _growToNextCapacity()
    //     0x6b55e8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b55ec: ldur            x2, [fp, #-0x28]
    // 0x6b55f0: ldur            x3, [fp, #-0x48]
    // 0x6b55f4: add             x0, x3, #1
    // 0x6b55f8: lsl             x1, x0, #1
    // 0x6b55fc: StoreField: r2->field_b = r1
    //     0x6b55fc: stur            w1, [x2, #0xb]
    // 0x6b5600: LoadField: r1 = r2->field_f
    //     0x6b5600: ldur            w1, [x2, #0xf]
    // 0x6b5604: DecompressPointer r1
    //     0x6b5604: add             x1, x1, HEAP, lsl #32
    // 0x6b5608: ldur            x0, [fp, #-0x10]
    // 0x6b560c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6b560c: add             x25, x1, x3, lsl #2
    //     0x6b5610: add             x25, x25, #0xf
    //     0x6b5614: str             w0, [x25]
    //     0x6b5618: tbz             w0, #0, #0x6b5634
    //     0x6b561c: ldurb           w16, [x1, #-1]
    //     0x6b5620: ldurb           w17, [x0, #-1]
    //     0x6b5624: and             x16, x17, x16, lsr #2
    //     0x6b5628: tst             x16, HEAP, lsr #32
    //     0x6b562c: b.eq            #0x6b5634
    //     0x6b5630: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6b5634: ldur            x8, [fp, #-0x18]
    // 0x6b5638: mov             x7, x2
    // 0x6b563c: r2 = 2
    //     0x6b563c: movz            x2, #0x2
    // 0x6b5640: b               #0x6b571c
    // 0x6b5644: ldur            x1, [fp, #-0x40]
    // 0x6b5648: ldur            x2, [fp, #-0x28]
    // 0x6b564c: r0 = _ReadingOrderDirectionalGroupData()
    //     0x6b564c: bl              #0x6b5940  ; Allocate_ReadingOrderDirectionalGroupDataStub -> _ReadingOrderDirectionalGroupData (size=0x10)
    // 0x6b5650: ldur            x1, [fp, #-0x28]
    // 0x6b5654: stur            x0, [fp, #-0x18]
    // 0x6b5658: StoreField: r0->field_7 = r1
    //     0x6b5658: stur            w1, [x0, #7]
    // 0x6b565c: ldur            x2, [fp, #-0x40]
    // 0x6b5660: LoadField: r1 = r2->field_b
    //     0x6b5660: ldur            w1, [x2, #0xb]
    // 0x6b5664: LoadField: r3 = r2->field_f
    //     0x6b5664: ldur            w3, [x2, #0xf]
    // 0x6b5668: DecompressPointer r3
    //     0x6b5668: add             x3, x3, HEAP, lsl #32
    // 0x6b566c: LoadField: r4 = r3->field_b
    //     0x6b566c: ldur            w4, [x3, #0xb]
    // 0x6b5670: r3 = LoadInt32Instr(r1)
    //     0x6b5670: sbfx            x3, x1, #1, #0x1f
    // 0x6b5674: stur            x3, [fp, #-0x48]
    // 0x6b5678: r1 = LoadInt32Instr(r4)
    //     0x6b5678: sbfx            x1, x4, #1, #0x1f
    // 0x6b567c: cmp             x3, x1
    // 0x6b5680: b.ne            #0x6b568c
    // 0x6b5684: mov             x1, x2
    // 0x6b5688: r0 = _growToNextCapacity()
    //     0x6b5688: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b568c: ldur            x3, [fp, #-0x40]
    // 0x6b5690: ldur            x2, [fp, #-0x48]
    // 0x6b5694: ldur            x4, [fp, #-0x10]
    // 0x6b5698: r5 = 2
    //     0x6b5698: movz            x5, #0x2
    // 0x6b569c: add             x0, x2, #1
    // 0x6b56a0: lsl             x1, x0, #1
    // 0x6b56a4: StoreField: r3->field_b = r1
    //     0x6b56a4: stur            w1, [x3, #0xb]
    // 0x6b56a8: LoadField: r1 = r3->field_f
    //     0x6b56a8: ldur            w1, [x3, #0xf]
    // 0x6b56ac: DecompressPointer r1
    //     0x6b56ac: add             x1, x1, HEAP, lsl #32
    // 0x6b56b0: ldur            x0, [fp, #-0x18]
    // 0x6b56b4: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6b56b4: add             x25, x1, x2, lsl #2
    //     0x6b56b8: add             x25, x25, #0xf
    //     0x6b56bc: str             w0, [x25]
    //     0x6b56c0: tbz             w0, #0, #0x6b56dc
    //     0x6b56c4: ldurb           w16, [x1, #-1]
    //     0x6b56c8: ldurb           w17, [x0, #-1]
    //     0x6b56cc: and             x16, x17, x16, lsr #2
    //     0x6b56d0: tst             x16, HEAP, lsr #32
    //     0x6b56d4: b.eq            #0x6b56dc
    //     0x6b56d8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6b56dc: mov             x2, x5
    // 0x6b56e0: r1 = Null
    //     0x6b56e0: mov             x1, NULL
    // 0x6b56e4: r0 = AllocateArray()
    //     0x6b56e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6b56e8: mov             x2, x0
    // 0x6b56ec: ldur            x0, [fp, #-0x10]
    // 0x6b56f0: stur            x2, [fp, #-0x18]
    // 0x6b56f4: StoreField: r2->field_f = r0
    //     0x6b56f4: stur            w0, [x2, #0xf]
    // 0x6b56f8: r1 = <_ReadingOrderSortData>
    //     0x6b56f8: ldr             x1, [PP, #0x73a0]  ; [pp+0x73a0] TypeArguments: <_ReadingOrderSortData>
    // 0x6b56fc: r0 = AllocateGrowableArray()
    //     0x6b56fc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x6b5700: mov             x1, x0
    // 0x6b5704: ldur            x0, [fp, #-0x18]
    // 0x6b5708: StoreField: r1->field_f = r0
    //     0x6b5708: stur            w0, [x1, #0xf]
    // 0x6b570c: r2 = 2
    //     0x6b570c: movz            x2, #0x2
    // 0x6b5710: StoreField: r1->field_b = r2
    //     0x6b5710: stur            w2, [x1, #0xb]
    // 0x6b5714: ldur            x8, [fp, #-0x50]
    // 0x6b5718: mov             x7, x1
    // 0x6b571c: ldur            x0, [fp, #-0x20]
    // 0x6b5720: ldur            x3, [fp, #-8]
    // 0x6b5724: ldur            x4, [fp, #-0x40]
    // 0x6b5728: ldur            x5, [fp, #-0x38]
    // 0x6b572c: ldur            x6, [fp, #-0x30]
    // 0x6b5730: b               #0x6b54ec
    // 0x6b5734: mov             x1, x7
    // 0x6b5738: LoadField: r0 = r1->field_b
    //     0x6b5738: ldur            w0, [x1, #0xb]
    // 0x6b573c: cbz             w0, #0x6b57d4
    // 0x6b5740: ldur            x0, [fp, #-0x40]
    // 0x6b5744: r0 = _ReadingOrderDirectionalGroupData()
    //     0x6b5744: bl              #0x6b5940  ; Allocate_ReadingOrderDirectionalGroupDataStub -> _ReadingOrderDirectionalGroupData (size=0x10)
    // 0x6b5748: mov             x2, x0
    // 0x6b574c: ldur            x0, [fp, #-0x28]
    // 0x6b5750: stur            x2, [fp, #-0x10]
    // 0x6b5754: StoreField: r2->field_7 = r0
    //     0x6b5754: stur            w0, [x2, #7]
    // 0x6b5758: ldur            x0, [fp, #-0x40]
    // 0x6b575c: LoadField: r1 = r0->field_b
    //     0x6b575c: ldur            w1, [x0, #0xb]
    // 0x6b5760: LoadField: r3 = r0->field_f
    //     0x6b5760: ldur            w3, [x0, #0xf]
    // 0x6b5764: DecompressPointer r3
    //     0x6b5764: add             x3, x3, HEAP, lsl #32
    // 0x6b5768: LoadField: r4 = r3->field_b
    //     0x6b5768: ldur            w4, [x3, #0xb]
    // 0x6b576c: r3 = LoadInt32Instr(r1)
    //     0x6b576c: sbfx            x3, x1, #1, #0x1f
    // 0x6b5770: stur            x3, [fp, #-0x20]
    // 0x6b5774: r1 = LoadInt32Instr(r4)
    //     0x6b5774: sbfx            x1, x4, #1, #0x1f
    // 0x6b5778: cmp             x3, x1
    // 0x6b577c: b.ne            #0x6b5788
    // 0x6b5780: mov             x1, x0
    // 0x6b5784: r0 = _growToNextCapacity()
    //     0x6b5784: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b5788: ldur            x2, [fp, #-0x40]
    // 0x6b578c: ldur            x3, [fp, #-0x20]
    // 0x6b5790: add             x0, x3, #1
    // 0x6b5794: lsl             x1, x0, #1
    // 0x6b5798: StoreField: r2->field_b = r1
    //     0x6b5798: stur            w1, [x2, #0xb]
    // 0x6b579c: LoadField: r1 = r2->field_f
    //     0x6b579c: ldur            w1, [x2, #0xf]
    // 0x6b57a0: DecompressPointer r1
    //     0x6b57a0: add             x1, x1, HEAP, lsl #32
    // 0x6b57a4: ldur            x0, [fp, #-0x10]
    // 0x6b57a8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6b57a8: add             x25, x1, x3, lsl #2
    //     0x6b57ac: add             x25, x25, #0xf
    //     0x6b57b0: str             w0, [x25]
    //     0x6b57b4: tbz             w0, #0, #0x6b57d0
    //     0x6b57b8: ldurb           w16, [x1, #-1]
    //     0x6b57bc: ldurb           w17, [x0, #-1]
    //     0x6b57c0: and             x16, x17, x16, lsr #2
    //     0x6b57c4: tst             x16, HEAP, lsr #32
    //     0x6b57c8: b.eq            #0x6b57d0
    //     0x6b57cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6b57d0: b               #0x6b57d8
    // 0x6b57d4: ldur            x2, [fp, #-0x40]
    // 0x6b57d8: LoadField: r0 = r2->field_b
    //     0x6b57d8: ldur            w0, [x2, #0xb]
    // 0x6b57dc: r1 = LoadInt32Instr(r0)
    //     0x6b57dc: sbfx            x1, x0, #1, #0x1f
    // 0x6b57e0: stur            x1, [fp, #-0x30]
    // 0x6b57e4: r0 = 0
    //     0x6b57e4: movz            x0, #0
    // 0x6b57e8: CheckStackOverflow
    //     0x6b57e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b57ec: cmp             SP, x16
    //     0x6b57f0: b.ls            #0x6b5934
    // 0x6b57f4: LoadField: r3 = r2->field_b
    //     0x6b57f4: ldur            w3, [x2, #0xb]
    // 0x6b57f8: r4 = LoadInt32Instr(r3)
    //     0x6b57f8: sbfx            x4, x3, #1, #0x1f
    // 0x6b57fc: cmp             x1, x4
    // 0x6b5800: b.ne            #0x6b58e4
    // 0x6b5804: cmp             x0, x4
    // 0x6b5808: b.ge            #0x6b58d4
    // 0x6b580c: LoadField: r3 = r2->field_f
    //     0x6b580c: ldur            w3, [x2, #0xf]
    // 0x6b5810: DecompressPointer r3
    //     0x6b5810: add             x3, x3, HEAP, lsl #32
    // 0x6b5814: ArrayLoad: r4 = r3[r0]  ; Unknown_4
    //     0x6b5814: add             x16, x3, x0, lsl #2
    //     0x6b5818: ldur            w4, [x16, #0xf]
    // 0x6b581c: DecompressPointer r4
    //     0x6b581c: add             x4, x4, HEAP, lsl #32
    // 0x6b5820: add             x3, x0, #1
    // 0x6b5824: stur            x3, [fp, #-0x20]
    // 0x6b5828: LoadField: r5 = r4->field_7
    //     0x6b5828: ldur            w5, [x4, #7]
    // 0x6b582c: DecompressPointer r5
    //     0x6b582c: add             x5, x5, HEAP, lsl #32
    // 0x6b5830: stur            x5, [fp, #-0x10]
    // 0x6b5834: r0 = LoadClassIdInstr(r5)
    //     0x6b5834: ldur            x0, [x5, #-1]
    //     0x6b5838: ubfx            x0, x0, #0xc, #0x14
    // 0x6b583c: str             x5, [SP]
    // 0x6b5840: r0 = GDT[cid_x0 + 0xc834]()
    //     0x6b5840: movz            x17, #0xc834
    //     0x6b5844: add             lr, x0, x17
    //     0x6b5848: ldr             lr, [x21, lr, lsl #3]
    //     0x6b584c: blr             lr
    // 0x6b5850: cmp             w0, #2
    // 0x6b5854: b.eq            #0x6b58c4
    // 0x6b5858: ldur            x2, [fp, #-0x10]
    // 0x6b585c: r0 = LoadClassIdInstr(r2)
    //     0x6b585c: ldur            x0, [x2, #-1]
    //     0x6b5860: ubfx            x0, x0, #0xc, #0x14
    // 0x6b5864: mov             x1, x2
    // 0x6b5868: r0 = GDT[cid_x0 + 0xd20f]()
    //     0x6b5868: movz            x17, #0xd20f
    //     0x6b586c: add             lr, x0, x17
    //     0x6b5870: ldr             lr, [x21, lr, lsl #3]
    //     0x6b5874: blr             lr
    // 0x6b5878: LoadField: r1 = r0->field_7
    //     0x6b5878: ldur            w1, [x0, #7]
    // 0x6b587c: DecompressPointer r1
    //     0x6b587c: add             x1, x1, HEAP, lsl #32
    // 0x6b5880: stur            x1, [fp, #-0x18]
    // 0x6b5884: cmp             w1, NULL
    // 0x6b5888: b.eq            #0x6b593c
    // 0x6b588c: r1 = 1
    //     0x6b588c: movz            x1, #0x1
    // 0x6b5890: r0 = AllocateContext()
    //     0x6b5890: bl              #0xec126c  ; AllocateContextStub
    // 0x6b5894: mov             x1, x0
    // 0x6b5898: ldur            x0, [fp, #-0x18]
    // 0x6b589c: StoreField: r1->field_f = r0
    //     0x6b589c: stur            w0, [x1, #0xf]
    // 0x6b58a0: mov             x2, x1
    // 0x6b58a4: r1 = Function '<anonymous closure>': static.
    //     0x6b58a4: ldr             x1, [PP, #0x7400]  ; [pp+0x7400] AnonymousClosure: static (0x6b594c), in [package:flutter/src/widgets/focus_traversal.dart] _ReadingOrderSortData::sortWithDirectionality (0x6b5b64)
    // 0x6b58a8: r0 = AllocateClosure()
    //     0x6b58a8: bl              #0xec1630  ; AllocateClosureStub
    // 0x6b58ac: r16 = <_ReadingOrderSortData>
    //     0x6b58ac: ldr             x16, [PP, #0x73a0]  ; [pp+0x73a0] TypeArguments: <_ReadingOrderSortData>
    // 0x6b58b0: ldur            lr, [fp, #-0x10]
    // 0x6b58b4: stp             lr, x16, [SP, #8]
    // 0x6b58b8: str             x0, [SP]
    // 0x6b58bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6b58bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6b58c0: r0 = mergeSort()
    //     0x6b58c0: bl              #0x6b61b4  ; [package:flutter/src/foundation/collections.dart] ::mergeSort
    // 0x6b58c4: ldur            x0, [fp, #-0x20]
    // 0x6b58c8: ldur            x2, [fp, #-0x40]
    // 0x6b58cc: ldur            x1, [fp, #-0x30]
    // 0x6b58d0: b               #0x6b57e8
    // 0x6b58d4: ldur            x0, [fp, #-0x40]
    // 0x6b58d8: LeaveFrame
    //     0x6b58d8: mov             SP, fp
    //     0x6b58dc: ldp             fp, lr, [SP], #0x10
    // 0x6b58e0: ret
    //     0x6b58e0: ret             
    // 0x6b58e4: mov             x0, x2
    // 0x6b58e8: r0 = ConcurrentModificationError()
    //     0x6b58e8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6b58ec: mov             x1, x0
    // 0x6b58f0: ldur            x0, [fp, #-0x40]
    // 0x6b58f4: StoreField: r1->field_b = r0
    //     0x6b58f4: stur            w0, [x1, #0xb]
    // 0x6b58f8: mov             x0, x1
    // 0x6b58fc: r0 = Throw()
    //     0x6b58fc: bl              #0xec04b8  ; ThrowStub
    // 0x6b5900: brk             #0
    // 0x6b5904: mov             x0, x3
    // 0x6b5908: r0 = ConcurrentModificationError()
    //     0x6b5908: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6b590c: mov             x1, x0
    // 0x6b5910: ldur            x0, [fp, #-8]
    // 0x6b5914: StoreField: r1->field_b = r0
    //     0x6b5914: stur            w0, [x1, #0xb]
    // 0x6b5918: mov             x0, x1
    // 0x6b591c: r0 = Throw()
    //     0x6b591c: bl              #0xec04b8  ; ThrowStub
    // 0x6b5920: brk             #0
    // 0x6b5924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b5924: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b5928: b               #0x6b548c
    // 0x6b592c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b592c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b5930: b               #0x6b5500
    // 0x6b5934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b5934: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b5938: b               #0x6b57f4
    // 0x6b593c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b593c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, _ReadingOrderSortData) {
    // ** addr: 0x6b6f20, size: 0x88
    // 0x6b6f20: EnterFrame
    //     0x6b6f20: stp             fp, lr, [SP, #-0x10]!
    //     0x6b6f24: mov             fp, SP
    // 0x6b6f28: ldr             x0, [fp, #0x18]
    // 0x6b6f2c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b6f2c: ldur            w1, [x0, #0x17]
    // 0x6b6f30: DecompressPointer r1
    //     0x6b6f30: add             x1, x1, HEAP, lsl #32
    // 0x6b6f34: CheckStackOverflow
    //     0x6b6f34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b6f38: cmp             SP, x16
    //     0x6b6f3c: b.ls            #0x6b6fa0
    // 0x6b6f40: ldr             x0, [fp, #0x10]
    // 0x6b6f44: LoadField: r2 = r0->field_b
    //     0x6b6f44: ldur            w2, [x0, #0xb]
    // 0x6b6f48: DecompressPointer r2
    //     0x6b6f48: add             x2, x2, HEAP, lsl #32
    // 0x6b6f4c: LoadField: r0 = r1->field_f
    //     0x6b6f4c: ldur            w0, [x1, #0xf]
    // 0x6b6f50: DecompressPointer r0
    //     0x6b6f50: add             x0, x0, HEAP, lsl #32
    // 0x6b6f54: mov             x1, x2
    // 0x6b6f58: mov             x2, x0
    // 0x6b6f5c: r0 = intersect()
    //     0x6b6f5c: bl              #0x6b6fa8  ; [dart:ui] Rect::intersect
    // 0x6b6f60: LoadField: d0 = r0->field_7
    //     0x6b6f60: ldur            d0, [x0, #7]
    // 0x6b6f64: ArrayLoad: d1 = r0[0]  ; List_8
    //     0x6b6f64: ldur            d1, [x0, #0x17]
    // 0x6b6f68: fcmp            d0, d1
    // 0x6b6f6c: b.lt            #0x6b6f78
    // 0x6b6f70: r1 = true
    //     0x6b6f70: add             x1, NULL, #0x20  ; true
    // 0x6b6f74: b               #0x6b6f90
    // 0x6b6f78: LoadField: d0 = r0->field_f
    //     0x6b6f78: ldur            d0, [x0, #0xf]
    // 0x6b6f7c: LoadField: d1 = r0->field_1f
    //     0x6b6f7c: ldur            d1, [x0, #0x1f]
    // 0x6b6f80: fcmp            d0, d1
    // 0x6b6f84: r16 = true
    //     0x6b6f84: add             x16, NULL, #0x20  ; true
    // 0x6b6f88: r17 = false
    //     0x6b6f88: add             x17, NULL, #0x30  ; false
    // 0x6b6f8c: csel            x1, x16, x17, ge
    // 0x6b6f90: eor             x0, x1, #0x10
    // 0x6b6f94: LeaveFrame
    //     0x6b6f94: mov             SP, fp
    //     0x6b6f98: ldp             fp, lr, [SP], #0x10
    // 0x6b6f9c: ret
    //     0x6b6f9c: ret             
    // 0x6b6fa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b6fa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b6fa4: b               #0x6b6f40
  }
  [closure] int <anonymous closure>(dynamic, _ReadingOrderSortData, _ReadingOrderSortData) {
    // ** addr: 0x6b715c, size: 0xfc
    // 0x6b715c: ldr             x1, [SP, #8]
    // 0x6b7160: LoadField: r2 = r1->field_b
    //     0x6b7160: ldur            w2, [x1, #0xb]
    // 0x6b7164: DecompressPointer r2
    //     0x6b7164: add             x2, x2, HEAP, lsl #32
    // 0x6b7168: LoadField: d0 = r2->field_f
    //     0x6b7168: ldur            d0, [x2, #0xf]
    // 0x6b716c: ldr             x1, [SP]
    // 0x6b7170: LoadField: r2 = r1->field_b
    //     0x6b7170: ldur            w2, [x1, #0xb]
    // 0x6b7174: DecompressPointer r2
    //     0x6b7174: add             x2, x2, HEAP, lsl #32
    // 0x6b7178: LoadField: d1 = r2->field_f
    //     0x6b7178: ldur            d1, [x2, #0xf]
    // 0x6b717c: fcmp            d1, d0
    // 0x6b7180: b.le            #0x6b718c
    // 0x6b7184: r1 = -1
    //     0x6b7184: movn            x1, #0
    // 0x6b7188: b               #0x6b7250
    // 0x6b718c: fcmp            d0, d1
    // 0x6b7190: b.le            #0x6b719c
    // 0x6b7194: r1 = 1
    //     0x6b7194: movz            x1, #0x1
    // 0x6b7198: b               #0x6b7250
    // 0x6b719c: fcmp            d0, d1
    // 0x6b71a0: b.ne            #0x6b722c
    // 0x6b71a4: d2 = 0.000000
    //     0x6b71a4: eor             v2.16b, v2.16b, v2.16b
    // 0x6b71a8: fcmp            d0, d2
    // 0x6b71ac: b.ne            #0x6b7224
    // 0x6b71b0: fcmp            d0, #0.0
    // 0x6b71b4: b.vs            #0x6b71c8
    // 0x6b71b8: b.ne            #0x6b71c4
    // 0x6b71bc: r2 = 0.000000
    //     0x6b71bc: fmov            x2, d0
    // 0x6b71c0: cmp             x2, #0
    // 0x6b71c4: b.lt            #0x6b71d0
    // 0x6b71c8: r1 = false
    //     0x6b71c8: add             x1, NULL, #0x30  ; false
    // 0x6b71cc: b               #0x6b71d4
    // 0x6b71d0: r1 = true
    //     0x6b71d0: add             x1, NULL, #0x20  ; true
    // 0x6b71d4: fcmp            d1, #0.0
    // 0x6b71d8: b.vs            #0x6b71ec
    // 0x6b71dc: b.ne            #0x6b71e8
    // 0x6b71e0: r3 = 0.000000
    //     0x6b71e0: fmov            x3, d1
    // 0x6b71e4: cmp             x3, #0
    // 0x6b71e8: b.lt            #0x6b71f4
    // 0x6b71ec: r2 = false
    //     0x6b71ec: add             x2, NULL, #0x30  ; false
    // 0x6b71f0: b               #0x6b71f8
    // 0x6b71f4: r2 = true
    //     0x6b71f4: add             x2, NULL, #0x20  ; true
    // 0x6b71f8: cmp             w1, w2
    // 0x6b71fc: b.ne            #0x6b7208
    // 0x6b7200: r1 = 0
    //     0x6b7200: movz            x1, #0
    // 0x6b7204: b               #0x6b7250
    // 0x6b7208: tst             x1, #0x10
    // 0x6b720c: cset            x2, ne
    // 0x6b7210: sub             x2, x2, #1
    // 0x6b7214: and             x2, x2, #0xfffffffffffffffc
    // 0x6b7218: add             x2, x2, #2
    // 0x6b721c: r1 = LoadInt32Instr(r2)
    //     0x6b721c: sbfx            x1, x2, #1, #0x1f
    // 0x6b7220: b               #0x6b7250
    // 0x6b7224: r1 = 0
    //     0x6b7224: movz            x1, #0
    // 0x6b7228: b               #0x6b7250
    // 0x6b722c: fcmp            d0, d0
    // 0x6b7230: b.vc            #0x6b724c
    // 0x6b7234: fcmp            d1, d1
    // 0x6b7238: b.vc            #0x6b7244
    // 0x6b723c: r1 = 0
    //     0x6b723c: movz            x1, #0
    // 0x6b7240: b               #0x6b7250
    // 0x6b7244: r1 = 1
    //     0x6b7244: movz            x1, #0x1
    // 0x6b7248: b               #0x6b7250
    // 0x6b724c: r1 = -1
    //     0x6b724c: movn            x1, #0
    // 0x6b7250: lsl             x0, x1, #1
    // 0x6b7254: ret
    //     0x6b7254: ret             
  }
}

// class id: 3792, size: 0xc, field offset: 0xc
abstract class DirectionalFocusTraversalPolicyMixin extends FocusTraversalPolicy {

  static _ _sortClosestEdgesByDistancePreferVertical(/* No info */) {
    // ** addr: 0xa576dc, size: 0xa4
    // 0xa576dc: EnterFrame
    //     0xa576dc: stp             fp, lr, [SP, #-0x10]!
    //     0xa576e0: mov             fp, SP
    // 0xa576e4: AllocStack(0x30)
    //     0xa576e4: sub             SP, SP, #0x30
    // 0xa576e8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa576e8: mov             x0, x1
    //     0xa576ec: stur            x1, [fp, #-8]
    //     0xa576f0: mov             x1, x2
    //     0xa576f4: stur            x2, [fp, #-0x10]
    // 0xa576f8: CheckStackOverflow
    //     0xa576f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa576fc: cmp             SP, x16
    //     0xa57700: b.ls            #0xa57778
    // 0xa57704: r1 = 1
    //     0xa57704: movz            x1, #0x1
    // 0xa57708: r0 = AllocateContext()
    //     0xa57708: bl              #0xec126c  ; AllocateContextStub
    // 0xa5770c: mov             x2, x0
    // 0xa57710: ldur            x0, [fp, #-8]
    // 0xa57714: stur            x2, [fp, #-0x18]
    // 0xa57718: StoreField: r2->field_f = r0
    //     0xa57718: stur            w0, [x2, #0xf]
    // 0xa5771c: ldur            x1, [fp, #-0x10]
    // 0xa57720: r0 = LoadClassIdInstr(r1)
    //     0xa57720: ldur            x0, [x1, #-1]
    //     0xa57724: ubfx            x0, x0, #0xc, #0x14
    // 0xa57728: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa57728: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa5772c: r0 = GDT[cid_x0 + 0xd889]()
    //     0xa5772c: movz            x17, #0xd889
    //     0xa57730: add             lr, x0, x17
    //     0xa57734: ldr             lr, [x21, lr, lsl #3]
    //     0xa57738: blr             lr
    // 0xa5773c: ldur            x2, [fp, #-0x18]
    // 0xa57740: r1 = Function '<anonymous closure>': static.
    //     0xa57740: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e40] AnonymousClosure: static (0xa57780), in [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_sortClosestEdgesByDistancePreferVertical (0xa576dc)
    //     0xa57744: ldr             x1, [x1, #0xe40]
    // 0xa57748: stur            x0, [fp, #-8]
    // 0xa5774c: r0 = AllocateClosure()
    //     0xa5774c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa57750: r16 = <FocusNode>
    //     0xa57750: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0xa57754: ldur            lr, [fp, #-8]
    // 0xa57758: stp             lr, x16, [SP, #8]
    // 0xa5775c: str             x0, [SP]
    // 0xa57760: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa57760: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa57764: r0 = mergeSort()
    //     0xa57764: bl              #0x6b61b4  ; [package:flutter/src/foundation/collections.dart] ::mergeSort
    // 0xa57768: ldur            x0, [fp, #-8]
    // 0xa5776c: LeaveFrame
    //     0xa5776c: mov             SP, fp
    //     0xa57770: ldp             fp, lr, [SP], #0x10
    // 0xa57774: ret
    //     0xa57774: ret             
    // 0xa57778: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa57778: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5777c: b               #0xa57704
  }
  [closure] static int <anonymous closure>(dynamic, FocusNode, FocusNode) {
    // ** addr: 0xa57780, size: 0xd0
    // 0xa57780: EnterFrame
    //     0xa57780: stp             fp, lr, [SP, #-0x10]!
    //     0xa57784: mov             fp, SP
    // 0xa57788: AllocStack(0x18)
    //     0xa57788: sub             SP, SP, #0x18
    // 0xa5778c: SetupParameters()
    //     0xa5778c: ldr             x0, [fp, #0x20]
    //     0xa57790: ldur            w2, [x0, #0x17]
    //     0xa57794: add             x2, x2, HEAP, lsl #32
    //     0xa57798: stur            x2, [fp, #-0x10]
    // 0xa5779c: CheckStackOverflow
    //     0xa5779c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa577a0: cmp             SP, x16
    //     0xa577a4: b.ls            #0xa57848
    // 0xa577a8: LoadField: r0 = r2->field_f
    //     0xa577a8: ldur            w0, [x2, #0xf]
    // 0xa577ac: DecompressPointer r0
    //     0xa577ac: add             x0, x0, HEAP, lsl #32
    // 0xa577b0: ldr             x1, [fp, #0x18]
    // 0xa577b4: stur            x0, [fp, #-8]
    // 0xa577b8: r0 = rect()
    //     0xa577b8: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa577bc: ldr             x1, [fp, #0x10]
    // 0xa577c0: stur            x0, [fp, #-0x18]
    // 0xa577c4: r0 = rect()
    //     0xa577c4: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa577c8: ldur            x1, [fp, #-8]
    // 0xa577cc: ldur            x2, [fp, #-0x18]
    // 0xa577d0: mov             x3, x0
    // 0xa577d4: r0 = _verticalCompareClosestEdge()
    //     0xa577d4: bl              #0xa5795c  ; [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_verticalCompareClosestEdge
    // 0xa577d8: cbnz            x0, #0xa57834
    // 0xa577dc: ldur            x0, [fp, #-0x10]
    // 0xa577e0: LoadField: r2 = r0->field_f
    //     0xa577e0: ldur            w2, [x0, #0xf]
    // 0xa577e4: DecompressPointer r2
    //     0xa577e4: add             x2, x2, HEAP, lsl #32
    // 0xa577e8: ldr             x1, [fp, #0x18]
    // 0xa577ec: stur            x2, [fp, #-8]
    // 0xa577f0: r0 = rect()
    //     0xa577f0: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa577f4: mov             x1, x0
    // 0xa577f8: r0 = center()
    //     0xa577f8: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa577fc: ldr             x1, [fp, #0x10]
    // 0xa57800: stur            x0, [fp, #-0x10]
    // 0xa57804: r0 = rect()
    //     0xa57804: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa57808: mov             x1, x0
    // 0xa5780c: r0 = center()
    //     0xa5780c: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa57810: ldur            x1, [fp, #-8]
    // 0xa57814: ldur            x2, [fp, #-0x10]
    // 0xa57818: mov             x3, x0
    // 0xa5781c: r0 = _horizontalCompare()
    //     0xa5781c: bl              #0xa57850  ; [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_horizontalCompare
    // 0xa57820: lsl             x1, x0, #1
    // 0xa57824: mov             x0, x1
    // 0xa57828: LeaveFrame
    //     0xa57828: mov             SP, fp
    //     0xa5782c: ldp             fp, lr, [SP], #0x10
    // 0xa57830: ret
    //     0xa57830: ret             
    // 0xa57834: lsl             x1, x0, #1
    // 0xa57838: mov             x0, x1
    // 0xa5783c: LeaveFrame
    //     0xa5783c: mov             SP, fp
    //     0xa57840: ldp             fp, lr, [SP], #0x10
    // 0xa57844: ret
    //     0xa57844: ret             
    // 0xa57848: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa57848: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5784c: b               #0xa577a8
  }
  static _ _horizontalCompare(/* No info */) {
    // ** addr: 0xa57850, size: 0x10c
    // 0xa57850: EnterFrame
    //     0xa57850: stp             fp, lr, [SP, #-0x10]!
    //     0xa57854: mov             fp, SP
    // 0xa57858: d0 = 0.000000
    //     0xa57858: eor             v0.16b, v0.16b, v0.16b
    // 0xa5785c: CheckStackOverflow
    //     0xa5785c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa57860: cmp             SP, x16
    //     0xa57864: b.ls            #0xa57924
    // 0xa57868: LoadField: d1 = r2->field_7
    //     0xa57868: ldur            d1, [x2, #7]
    // 0xa5786c: LoadField: d2 = r1->field_7
    //     0xa5786c: ldur            d2, [x1, #7]
    // 0xa57870: fsub            d3, d1, d2
    // 0xa57874: fcmp            d3, d0
    // 0xa57878: b.ne            #0xa57884
    // 0xa5787c: d1 = 0.000000
    //     0xa5787c: eor             v1.16b, v1.16b, v1.16b
    // 0xa57880: b               #0xa57898
    // 0xa57884: fcmp            d0, d3
    // 0xa57888: b.le            #0xa57894
    // 0xa5788c: fneg            d1, d3
    // 0xa57890: b               #0xa57898
    // 0xa57894: mov             v1.16b, v3.16b
    // 0xa57898: LoadField: d3 = r3->field_7
    //     0xa57898: ldur            d3, [x3, #7]
    // 0xa5789c: fsub            d4, d3, d2
    // 0xa578a0: fcmp            d4, d0
    // 0xa578a4: b.ne            #0xa578b0
    // 0xa578a8: d0 = 0.000000
    //     0xa578a8: eor             v0.16b, v0.16b, v0.16b
    // 0xa578ac: b               #0xa578c4
    // 0xa578b0: fcmp            d0, d4
    // 0xa578b4: b.le            #0xa578c0
    // 0xa578b8: fneg            d0, d4
    // 0xa578bc: b               #0xa578c4
    // 0xa578c0: mov             v0.16b, v4.16b
    // 0xa578c4: r1 = inline_Allocate_Double()
    //     0xa578c4: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa578c8: add             x1, x1, #0x10
    //     0xa578cc: cmp             x0, x1
    //     0xa578d0: b.ls            #0xa5792c
    //     0xa578d4: str             x1, [THR, #0x50]  ; THR::top
    //     0xa578d8: sub             x1, x1, #0xf
    //     0xa578dc: movz            x0, #0xe15c
    //     0xa578e0: movk            x0, #0x3, lsl #16
    //     0xa578e4: stur            x0, [x1, #-1]
    // 0xa578e8: StoreField: r1->field_7 = d1
    //     0xa578e8: stur            d1, [x1, #7]
    // 0xa578ec: r2 = inline_Allocate_Double()
    //     0xa578ec: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xa578f0: add             x2, x2, #0x10
    //     0xa578f4: cmp             x0, x2
    //     0xa578f8: b.ls            #0xa57940
    //     0xa578fc: str             x2, [THR, #0x50]  ; THR::top
    //     0xa57900: sub             x2, x2, #0xf
    //     0xa57904: movz            x0, #0xe15c
    //     0xa57908: movk            x0, #0x3, lsl #16
    //     0xa5790c: stur            x0, [x2, #-1]
    // 0xa57910: StoreField: r2->field_7 = d0
    //     0xa57910: stur            d0, [x2, #7]
    // 0xa57914: r0 = compareTo()
    //     0xa57914: bl              #0x6da84c  ; [dart:core] _Double::compareTo
    // 0xa57918: LeaveFrame
    //     0xa57918: mov             SP, fp
    //     0xa5791c: ldp             fp, lr, [SP], #0x10
    // 0xa57920: ret
    //     0xa57920: ret             
    // 0xa57924: r0 = StackOverflowSharedWithFPURegs()
    //     0xa57924: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa57928: b               #0xa57868
    // 0xa5792c: stp             q0, q1, [SP, #-0x20]!
    // 0xa57930: r0 = AllocateDouble()
    //     0xa57930: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa57934: mov             x1, x0
    // 0xa57938: ldp             q0, q1, [SP], #0x20
    // 0xa5793c: b               #0xa578e8
    // 0xa57940: SaveReg d0
    //     0xa57940: str             q0, [SP, #-0x10]!
    // 0xa57944: SaveReg r1
    //     0xa57944: str             x1, [SP, #-8]!
    // 0xa57948: r0 = AllocateDouble()
    //     0xa57948: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa5794c: mov             x2, x0
    // 0xa57950: RestoreReg r1
    //     0xa57950: ldr             x1, [SP], #8
    // 0xa57954: RestoreReg d0
    //     0xa57954: ldr             q0, [SP], #0x10
    // 0xa57958: b               #0xa57910
  }
  static _ _verticalCompareClosestEdge(/* No info */) {
    // ** addr: 0xa5795c, size: 0x1bc
    // 0xa5795c: EnterFrame
    //     0xa5795c: stp             fp, lr, [SP, #-0x10]!
    //     0xa57960: mov             fp, SP
    // 0xa57964: d0 = 0.000000
    //     0xa57964: eor             v0.16b, v0.16b, v0.16b
    // 0xa57968: CheckStackOverflow
    //     0xa57968: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5796c: cmp             SP, x16
    //     0xa57970: b.ls            #0xa57ae0
    // 0xa57974: LoadField: d1 = r2->field_f
    //     0xa57974: ldur            d1, [x2, #0xf]
    // 0xa57978: LoadField: d2 = r1->field_f
    //     0xa57978: ldur            d2, [x1, #0xf]
    // 0xa5797c: fsub            d3, d1, d2
    // 0xa57980: fcmp            d3, d0
    // 0xa57984: b.ne            #0xa57990
    // 0xa57988: d3 = 0.000000
    //     0xa57988: eor             v3.16b, v3.16b, v3.16b
    // 0xa5798c: b               #0xa579a0
    // 0xa57990: fcmp            d0, d3
    // 0xa57994: b.le            #0xa579a0
    // 0xa57998: fneg            d4, d3
    // 0xa5799c: mov             v3.16b, v4.16b
    // 0xa579a0: LoadField: d4 = r2->field_1f
    //     0xa579a0: ldur            d4, [x2, #0x1f]
    // 0xa579a4: fsub            d5, d4, d2
    // 0xa579a8: fcmp            d5, d0
    // 0xa579ac: b.ne            #0xa579b8
    // 0xa579b0: d5 = 0.000000
    //     0xa579b0: eor             v5.16b, v5.16b, v5.16b
    // 0xa579b4: b               #0xa579c8
    // 0xa579b8: fcmp            d0, d5
    // 0xa579bc: b.le            #0xa579c8
    // 0xa579c0: fneg            d6, d5
    // 0xa579c4: mov             v5.16b, v6.16b
    // 0xa579c8: fcmp            d5, d3
    // 0xa579cc: b.gt            #0xa579d4
    // 0xa579d0: mov             v1.16b, v4.16b
    // 0xa579d4: LoadField: d3 = r3->field_f
    //     0xa579d4: ldur            d3, [x3, #0xf]
    // 0xa579d8: fsub            d4, d3, d2
    // 0xa579dc: fcmp            d4, d0
    // 0xa579e0: b.ne            #0xa579ec
    // 0xa579e4: d4 = 0.000000
    //     0xa579e4: eor             v4.16b, v4.16b, v4.16b
    // 0xa579e8: b               #0xa579fc
    // 0xa579ec: fcmp            d0, d4
    // 0xa579f0: b.le            #0xa579fc
    // 0xa579f4: fneg            d5, d4
    // 0xa579f8: mov             v4.16b, v5.16b
    // 0xa579fc: LoadField: d5 = r3->field_1f
    //     0xa579fc: ldur            d5, [x3, #0x1f]
    // 0xa57a00: fsub            d6, d5, d2
    // 0xa57a04: fcmp            d6, d0
    // 0xa57a08: b.ne            #0xa57a14
    // 0xa57a0c: d6 = 0.000000
    //     0xa57a0c: eor             v6.16b, v6.16b, v6.16b
    // 0xa57a10: b               #0xa57a24
    // 0xa57a14: fcmp            d0, d6
    // 0xa57a18: b.le            #0xa57a24
    // 0xa57a1c: fneg            d7, d6
    // 0xa57a20: mov             v6.16b, v7.16b
    // 0xa57a24: fcmp            d6, d4
    // 0xa57a28: b.gt            #0xa57a30
    // 0xa57a2c: mov             v3.16b, v5.16b
    // 0xa57a30: fsub            d4, d1, d2
    // 0xa57a34: fcmp            d4, d0
    // 0xa57a38: b.ne            #0xa57a44
    // 0xa57a3c: d1 = 0.000000
    //     0xa57a3c: eor             v1.16b, v1.16b, v1.16b
    // 0xa57a40: b               #0xa57a58
    // 0xa57a44: fcmp            d0, d4
    // 0xa57a48: b.le            #0xa57a54
    // 0xa57a4c: fneg            d1, d4
    // 0xa57a50: b               #0xa57a58
    // 0xa57a54: mov             v1.16b, v4.16b
    // 0xa57a58: fsub            d4, d3, d2
    // 0xa57a5c: fcmp            d4, d0
    // 0xa57a60: b.ne            #0xa57a6c
    // 0xa57a64: d0 = 0.000000
    //     0xa57a64: eor             v0.16b, v0.16b, v0.16b
    // 0xa57a68: b               #0xa57a80
    // 0xa57a6c: fcmp            d0, d4
    // 0xa57a70: b.le            #0xa57a7c
    // 0xa57a74: fneg            d0, d4
    // 0xa57a78: b               #0xa57a80
    // 0xa57a7c: mov             v0.16b, v4.16b
    // 0xa57a80: r1 = inline_Allocate_Double()
    //     0xa57a80: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa57a84: add             x1, x1, #0x10
    //     0xa57a88: cmp             x0, x1
    //     0xa57a8c: b.ls            #0xa57ae8
    //     0xa57a90: str             x1, [THR, #0x50]  ; THR::top
    //     0xa57a94: sub             x1, x1, #0xf
    //     0xa57a98: movz            x0, #0xe15c
    //     0xa57a9c: movk            x0, #0x3, lsl #16
    //     0xa57aa0: stur            x0, [x1, #-1]
    // 0xa57aa4: StoreField: r1->field_7 = d1
    //     0xa57aa4: stur            d1, [x1, #7]
    // 0xa57aa8: r2 = inline_Allocate_Double()
    //     0xa57aa8: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xa57aac: add             x2, x2, #0x10
    //     0xa57ab0: cmp             x0, x2
    //     0xa57ab4: b.ls            #0xa57afc
    //     0xa57ab8: str             x2, [THR, #0x50]  ; THR::top
    //     0xa57abc: sub             x2, x2, #0xf
    //     0xa57ac0: movz            x0, #0xe15c
    //     0xa57ac4: movk            x0, #0x3, lsl #16
    //     0xa57ac8: stur            x0, [x2, #-1]
    // 0xa57acc: StoreField: r2->field_7 = d0
    //     0xa57acc: stur            d0, [x2, #7]
    // 0xa57ad0: r0 = compareTo()
    //     0xa57ad0: bl              #0x6da84c  ; [dart:core] _Double::compareTo
    // 0xa57ad4: LeaveFrame
    //     0xa57ad4: mov             SP, fp
    //     0xa57ad8: ldp             fp, lr, [SP], #0x10
    // 0xa57adc: ret
    //     0xa57adc: ret             
    // 0xa57ae0: r0 = StackOverflowSharedWithFPURegs()
    //     0xa57ae0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa57ae4: b               #0xa57974
    // 0xa57ae8: stp             q0, q1, [SP, #-0x20]!
    // 0xa57aec: r0 = AllocateDouble()
    //     0xa57aec: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa57af0: mov             x1, x0
    // 0xa57af4: ldp             q0, q1, [SP], #0x20
    // 0xa57af8: b               #0xa57aa4
    // 0xa57afc: SaveReg d0
    //     0xa57afc: str             q0, [SP, #-0x10]!
    // 0xa57b00: SaveReg r1
    //     0xa57b00: str             x1, [SP, #-8]!
    // 0xa57b04: r0 = AllocateDouble()
    //     0xa57b04: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa57b08: mov             x2, x0
    // 0xa57b0c: RestoreReg r1
    //     0xa57b0c: ldr             x1, [SP], #8
    // 0xa57b10: RestoreReg d0
    //     0xa57b10: ldr             q0, [SP], #0x10
    // 0xa57b14: b               #0xa57acc
  }
  static _ _sortByDistancePreferHorizontal(/* No info */) {
    // ** addr: 0xa57b18, size: 0x8c
    // 0xa57b18: EnterFrame
    //     0xa57b18: stp             fp, lr, [SP, #-0x10]!
    //     0xa57b1c: mov             fp, SP
    // 0xa57b20: AllocStack(0x30)
    //     0xa57b20: sub             SP, SP, #0x30
    // 0xa57b24: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa57b24: stur            x1, [fp, #-8]
    //     0xa57b28: stur            x2, [fp, #-0x10]
    // 0xa57b2c: CheckStackOverflow
    //     0xa57b2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa57b30: cmp             SP, x16
    //     0xa57b34: b.ls            #0xa57b9c
    // 0xa57b38: r1 = 1
    //     0xa57b38: movz            x1, #0x1
    // 0xa57b3c: r0 = AllocateContext()
    //     0xa57b3c: bl              #0xec126c  ; AllocateContextStub
    // 0xa57b40: mov             x3, x0
    // 0xa57b44: ldur            x0, [fp, #-8]
    // 0xa57b48: stur            x3, [fp, #-0x18]
    // 0xa57b4c: StoreField: r3->field_f = r0
    //     0xa57b4c: stur            w0, [x3, #0xf]
    // 0xa57b50: ldur            x2, [fp, #-0x10]
    // 0xa57b54: LoadField: r1 = r2->field_7
    //     0xa57b54: ldur            w1, [x2, #7]
    // 0xa57b58: DecompressPointer r1
    //     0xa57b58: add             x1, x1, HEAP, lsl #32
    // 0xa57b5c: r0 = _GrowableList.of()
    //     0xa57b5c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xa57b60: ldur            x2, [fp, #-0x18]
    // 0xa57b64: r1 = Function '<anonymous closure>': static.
    //     0xa57b64: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e48] AnonymousClosure: static (0xa57ba4), in [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_sortByDistancePreferHorizontal (0xa57b18)
    //     0xa57b68: ldr             x1, [x1, #0xe48]
    // 0xa57b6c: stur            x0, [fp, #-8]
    // 0xa57b70: r0 = AllocateClosure()
    //     0xa57b70: bl              #0xec1630  ; AllocateClosureStub
    // 0xa57b74: r16 = <FocusNode>
    //     0xa57b74: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0xa57b78: ldur            lr, [fp, #-8]
    // 0xa57b7c: stp             lr, x16, [SP, #8]
    // 0xa57b80: str             x0, [SP]
    // 0xa57b84: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa57b84: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa57b88: r0 = mergeSort()
    //     0xa57b88: bl              #0x6b61b4  ; [package:flutter/src/foundation/collections.dart] ::mergeSort
    // 0xa57b8c: ldur            x0, [fp, #-8]
    // 0xa57b90: LeaveFrame
    //     0xa57b90: mov             SP, fp
    //     0xa57b94: ldp             fp, lr, [SP], #0x10
    // 0xa57b98: ret
    //     0xa57b98: ret             
    // 0xa57b9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa57b9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa57ba0: b               #0xa57b38
  }
  [closure] static int <anonymous closure>(dynamic, FocusNode, FocusNode) {
    // ** addr: 0xa57ba4, size: 0xb8
    // 0xa57ba4: EnterFrame
    //     0xa57ba4: stp             fp, lr, [SP, #-0x10]!
    //     0xa57ba8: mov             fp, SP
    // 0xa57bac: AllocStack(0x18)
    //     0xa57bac: sub             SP, SP, #0x18
    // 0xa57bb0: SetupParameters()
    //     0xa57bb0: ldr             x0, [fp, #0x20]
    //     0xa57bb4: ldur            w2, [x0, #0x17]
    //     0xa57bb8: add             x2, x2, HEAP, lsl #32
    //     0xa57bbc: stur            x2, [fp, #-8]
    // 0xa57bc0: CheckStackOverflow
    //     0xa57bc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa57bc4: cmp             SP, x16
    //     0xa57bc8: b.ls            #0xa57c54
    // 0xa57bcc: ldr             x1, [fp, #0x18]
    // 0xa57bd0: r0 = rect()
    //     0xa57bd0: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa57bd4: mov             x1, x0
    // 0xa57bd8: r0 = center()
    //     0xa57bd8: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa57bdc: ldr             x1, [fp, #0x10]
    // 0xa57be0: stur            x0, [fp, #-0x10]
    // 0xa57be4: r0 = rect()
    //     0xa57be4: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa57be8: mov             x1, x0
    // 0xa57bec: r0 = center()
    //     0xa57bec: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa57bf0: mov             x4, x0
    // 0xa57bf4: ldur            x0, [fp, #-8]
    // 0xa57bf8: stur            x4, [fp, #-0x18]
    // 0xa57bfc: LoadField: r1 = r0->field_f
    //     0xa57bfc: ldur            w1, [x0, #0xf]
    // 0xa57c00: DecompressPointer r1
    //     0xa57c00: add             x1, x1, HEAP, lsl #32
    // 0xa57c04: ldur            x2, [fp, #-0x10]
    // 0xa57c08: mov             x3, x4
    // 0xa57c0c: r0 = _horizontalCompare()
    //     0xa57c0c: bl              #0xa57850  ; [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_horizontalCompare
    // 0xa57c10: cbnz            x0, #0xa57c40
    // 0xa57c14: ldur            x0, [fp, #-8]
    // 0xa57c18: LoadField: r1 = r0->field_f
    //     0xa57c18: ldur            w1, [x0, #0xf]
    // 0xa57c1c: DecompressPointer r1
    //     0xa57c1c: add             x1, x1, HEAP, lsl #32
    // 0xa57c20: ldur            x2, [fp, #-0x10]
    // 0xa57c24: ldur            x3, [fp, #-0x18]
    // 0xa57c28: r0 = _verticalCompare()
    //     0xa57c28: bl              #0xa57c5c  ; [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_verticalCompare
    // 0xa57c2c: lsl             x1, x0, #1
    // 0xa57c30: mov             x0, x1
    // 0xa57c34: LeaveFrame
    //     0xa57c34: mov             SP, fp
    //     0xa57c38: ldp             fp, lr, [SP], #0x10
    // 0xa57c3c: ret
    //     0xa57c3c: ret             
    // 0xa57c40: lsl             x1, x0, #1
    // 0xa57c44: mov             x0, x1
    // 0xa57c48: LeaveFrame
    //     0xa57c48: mov             SP, fp
    //     0xa57c4c: ldp             fp, lr, [SP], #0x10
    // 0xa57c50: ret
    //     0xa57c50: ret             
    // 0xa57c54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa57c54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa57c58: b               #0xa57bcc
  }
  static _ _verticalCompare(/* No info */) {
    // ** addr: 0xa57c5c, size: 0x10c
    // 0xa57c5c: EnterFrame
    //     0xa57c5c: stp             fp, lr, [SP, #-0x10]!
    //     0xa57c60: mov             fp, SP
    // 0xa57c64: d0 = 0.000000
    //     0xa57c64: eor             v0.16b, v0.16b, v0.16b
    // 0xa57c68: CheckStackOverflow
    //     0xa57c68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa57c6c: cmp             SP, x16
    //     0xa57c70: b.ls            #0xa57d30
    // 0xa57c74: LoadField: d1 = r2->field_f
    //     0xa57c74: ldur            d1, [x2, #0xf]
    // 0xa57c78: LoadField: d2 = r1->field_f
    //     0xa57c78: ldur            d2, [x1, #0xf]
    // 0xa57c7c: fsub            d3, d1, d2
    // 0xa57c80: fcmp            d3, d0
    // 0xa57c84: b.ne            #0xa57c90
    // 0xa57c88: d1 = 0.000000
    //     0xa57c88: eor             v1.16b, v1.16b, v1.16b
    // 0xa57c8c: b               #0xa57ca4
    // 0xa57c90: fcmp            d0, d3
    // 0xa57c94: b.le            #0xa57ca0
    // 0xa57c98: fneg            d1, d3
    // 0xa57c9c: b               #0xa57ca4
    // 0xa57ca0: mov             v1.16b, v3.16b
    // 0xa57ca4: LoadField: d3 = r3->field_f
    //     0xa57ca4: ldur            d3, [x3, #0xf]
    // 0xa57ca8: fsub            d4, d3, d2
    // 0xa57cac: fcmp            d4, d0
    // 0xa57cb0: b.ne            #0xa57cbc
    // 0xa57cb4: d0 = 0.000000
    //     0xa57cb4: eor             v0.16b, v0.16b, v0.16b
    // 0xa57cb8: b               #0xa57cd0
    // 0xa57cbc: fcmp            d0, d4
    // 0xa57cc0: b.le            #0xa57ccc
    // 0xa57cc4: fneg            d0, d4
    // 0xa57cc8: b               #0xa57cd0
    // 0xa57ccc: mov             v0.16b, v4.16b
    // 0xa57cd0: r1 = inline_Allocate_Double()
    //     0xa57cd0: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa57cd4: add             x1, x1, #0x10
    //     0xa57cd8: cmp             x0, x1
    //     0xa57cdc: b.ls            #0xa57d38
    //     0xa57ce0: str             x1, [THR, #0x50]  ; THR::top
    //     0xa57ce4: sub             x1, x1, #0xf
    //     0xa57ce8: movz            x0, #0xe15c
    //     0xa57cec: movk            x0, #0x3, lsl #16
    //     0xa57cf0: stur            x0, [x1, #-1]
    // 0xa57cf4: StoreField: r1->field_7 = d1
    //     0xa57cf4: stur            d1, [x1, #7]
    // 0xa57cf8: r2 = inline_Allocate_Double()
    //     0xa57cf8: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xa57cfc: add             x2, x2, #0x10
    //     0xa57d00: cmp             x0, x2
    //     0xa57d04: b.ls            #0xa57d4c
    //     0xa57d08: str             x2, [THR, #0x50]  ; THR::top
    //     0xa57d0c: sub             x2, x2, #0xf
    //     0xa57d10: movz            x0, #0xe15c
    //     0xa57d14: movk            x0, #0x3, lsl #16
    //     0xa57d18: stur            x0, [x2, #-1]
    // 0xa57d1c: StoreField: r2->field_7 = d0
    //     0xa57d1c: stur            d0, [x2, #7]
    // 0xa57d20: r0 = compareTo()
    //     0xa57d20: bl              #0x6da84c  ; [dart:core] _Double::compareTo
    // 0xa57d24: LeaveFrame
    //     0xa57d24: mov             SP, fp
    //     0xa57d28: ldp             fp, lr, [SP], #0x10
    // 0xa57d2c: ret
    //     0xa57d2c: ret             
    // 0xa57d30: r0 = StackOverflowSharedWithFPURegs()
    //     0xa57d30: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa57d34: b               #0xa57c74
    // 0xa57d38: stp             q0, q1, [SP, #-0x20]!
    // 0xa57d3c: r0 = AllocateDouble()
    //     0xa57d3c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa57d40: mov             x1, x0
    // 0xa57d44: ldp             q0, q1, [SP], #0x20
    // 0xa57d48: b               #0xa57cf4
    // 0xa57d4c: SaveReg d0
    //     0xa57d4c: str             q0, [SP, #-0x10]!
    // 0xa57d50: SaveReg r1
    //     0xa57d50: str             x1, [SP, #-8]!
    // 0xa57d54: r0 = AllocateDouble()
    //     0xa57d54: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa57d58: mov             x2, x0
    // 0xa57d5c: RestoreReg r1
    //     0xa57d5c: ldr             x1, [SP], #8
    // 0xa57d60: RestoreReg d0
    //     0xa57d60: ldr             q0, [SP], #0x10
    // 0xa57d64: b               #0xa57d1c
  }
  static _ _sortClosestEdgesByDistancePreferHorizontal(/* No info */) {
    // ** addr: 0xa58184, size: 0xa4
    // 0xa58184: EnterFrame
    //     0xa58184: stp             fp, lr, [SP, #-0x10]!
    //     0xa58188: mov             fp, SP
    // 0xa5818c: AllocStack(0x30)
    //     0xa5818c: sub             SP, SP, #0x30
    // 0xa58190: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa58190: mov             x0, x1
    //     0xa58194: stur            x1, [fp, #-8]
    //     0xa58198: mov             x1, x2
    //     0xa5819c: stur            x2, [fp, #-0x10]
    // 0xa581a0: CheckStackOverflow
    //     0xa581a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa581a4: cmp             SP, x16
    //     0xa581a8: b.ls            #0xa58220
    // 0xa581ac: r1 = 1
    //     0xa581ac: movz            x1, #0x1
    // 0xa581b0: r0 = AllocateContext()
    //     0xa581b0: bl              #0xec126c  ; AllocateContextStub
    // 0xa581b4: mov             x2, x0
    // 0xa581b8: ldur            x0, [fp, #-8]
    // 0xa581bc: stur            x2, [fp, #-0x18]
    // 0xa581c0: StoreField: r2->field_f = r0
    //     0xa581c0: stur            w0, [x2, #0xf]
    // 0xa581c4: ldur            x1, [fp, #-0x10]
    // 0xa581c8: r0 = LoadClassIdInstr(r1)
    //     0xa581c8: ldur            x0, [x1, #-1]
    //     0xa581cc: ubfx            x0, x0, #0xc, #0x14
    // 0xa581d0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa581d0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa581d4: r0 = GDT[cid_x0 + 0xd889]()
    //     0xa581d4: movz            x17, #0xd889
    //     0xa581d8: add             lr, x0, x17
    //     0xa581dc: ldr             lr, [x21, lr, lsl #3]
    //     0xa581e0: blr             lr
    // 0xa581e4: ldur            x2, [fp, #-0x18]
    // 0xa581e8: r1 = Function '<anonymous closure>': static.
    //     0xa581e8: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e80] AnonymousClosure: static (0xa58228), in [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_sortClosestEdgesByDistancePreferHorizontal (0xa58184)
    //     0xa581ec: ldr             x1, [x1, #0xe80]
    // 0xa581f0: stur            x0, [fp, #-8]
    // 0xa581f4: r0 = AllocateClosure()
    //     0xa581f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa581f8: r16 = <FocusNode>
    //     0xa581f8: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0xa581fc: ldur            lr, [fp, #-8]
    // 0xa58200: stp             lr, x16, [SP, #8]
    // 0xa58204: str             x0, [SP]
    // 0xa58208: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa58208: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa5820c: r0 = mergeSort()
    //     0xa5820c: bl              #0x6b61b4  ; [package:flutter/src/foundation/collections.dart] ::mergeSort
    // 0xa58210: ldur            x0, [fp, #-8]
    // 0xa58214: LeaveFrame
    //     0xa58214: mov             SP, fp
    //     0xa58218: ldp             fp, lr, [SP], #0x10
    // 0xa5821c: ret
    //     0xa5821c: ret             
    // 0xa58220: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa58220: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa58224: b               #0xa581ac
  }
  [closure] static int <anonymous closure>(dynamic, FocusNode, FocusNode) {
    // ** addr: 0xa58228, size: 0xd0
    // 0xa58228: EnterFrame
    //     0xa58228: stp             fp, lr, [SP, #-0x10]!
    //     0xa5822c: mov             fp, SP
    // 0xa58230: AllocStack(0x18)
    //     0xa58230: sub             SP, SP, #0x18
    // 0xa58234: SetupParameters()
    //     0xa58234: ldr             x0, [fp, #0x20]
    //     0xa58238: ldur            w2, [x0, #0x17]
    //     0xa5823c: add             x2, x2, HEAP, lsl #32
    //     0xa58240: stur            x2, [fp, #-0x10]
    // 0xa58244: CheckStackOverflow
    //     0xa58244: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa58248: cmp             SP, x16
    //     0xa5824c: b.ls            #0xa582f0
    // 0xa58250: LoadField: r0 = r2->field_f
    //     0xa58250: ldur            w0, [x2, #0xf]
    // 0xa58254: DecompressPointer r0
    //     0xa58254: add             x0, x0, HEAP, lsl #32
    // 0xa58258: ldr             x1, [fp, #0x18]
    // 0xa5825c: stur            x0, [fp, #-8]
    // 0xa58260: r0 = rect()
    //     0xa58260: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa58264: ldr             x1, [fp, #0x10]
    // 0xa58268: stur            x0, [fp, #-0x18]
    // 0xa5826c: r0 = rect()
    //     0xa5826c: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa58270: ldur            x1, [fp, #-8]
    // 0xa58274: ldur            x2, [fp, #-0x18]
    // 0xa58278: mov             x3, x0
    // 0xa5827c: r0 = _horizontalCompareClosestEdge()
    //     0xa5827c: bl              #0xa582f8  ; [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_horizontalCompareClosestEdge
    // 0xa58280: cbnz            x0, #0xa582dc
    // 0xa58284: ldur            x0, [fp, #-0x10]
    // 0xa58288: LoadField: r2 = r0->field_f
    //     0xa58288: ldur            w2, [x0, #0xf]
    // 0xa5828c: DecompressPointer r2
    //     0xa5828c: add             x2, x2, HEAP, lsl #32
    // 0xa58290: ldr             x1, [fp, #0x18]
    // 0xa58294: stur            x2, [fp, #-8]
    // 0xa58298: r0 = rect()
    //     0xa58298: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa5829c: mov             x1, x0
    // 0xa582a0: r0 = center()
    //     0xa582a0: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa582a4: ldr             x1, [fp, #0x10]
    // 0xa582a8: stur            x0, [fp, #-0x10]
    // 0xa582ac: r0 = rect()
    //     0xa582ac: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa582b0: mov             x1, x0
    // 0xa582b4: r0 = center()
    //     0xa582b4: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa582b8: ldur            x1, [fp, #-8]
    // 0xa582bc: ldur            x2, [fp, #-0x10]
    // 0xa582c0: mov             x3, x0
    // 0xa582c4: r0 = _verticalCompare()
    //     0xa582c4: bl              #0xa57c5c  ; [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_verticalCompare
    // 0xa582c8: lsl             x1, x0, #1
    // 0xa582cc: mov             x0, x1
    // 0xa582d0: LeaveFrame
    //     0xa582d0: mov             SP, fp
    //     0xa582d4: ldp             fp, lr, [SP], #0x10
    // 0xa582d8: ret
    //     0xa582d8: ret             
    // 0xa582dc: lsl             x1, x0, #1
    // 0xa582e0: mov             x0, x1
    // 0xa582e4: LeaveFrame
    //     0xa582e4: mov             SP, fp
    //     0xa582e8: ldp             fp, lr, [SP], #0x10
    // 0xa582ec: ret
    //     0xa582ec: ret             
    // 0xa582f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa582f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa582f4: b               #0xa58250
  }
  static _ _horizontalCompareClosestEdge(/* No info */) {
    // ** addr: 0xa582f8, size: 0x1bc
    // 0xa582f8: EnterFrame
    //     0xa582f8: stp             fp, lr, [SP, #-0x10]!
    //     0xa582fc: mov             fp, SP
    // 0xa58300: d0 = 0.000000
    //     0xa58300: eor             v0.16b, v0.16b, v0.16b
    // 0xa58304: CheckStackOverflow
    //     0xa58304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa58308: cmp             SP, x16
    //     0xa5830c: b.ls            #0xa5847c
    // 0xa58310: LoadField: d1 = r2->field_7
    //     0xa58310: ldur            d1, [x2, #7]
    // 0xa58314: LoadField: d2 = r1->field_7
    //     0xa58314: ldur            d2, [x1, #7]
    // 0xa58318: fsub            d3, d1, d2
    // 0xa5831c: fcmp            d3, d0
    // 0xa58320: b.ne            #0xa5832c
    // 0xa58324: d3 = 0.000000
    //     0xa58324: eor             v3.16b, v3.16b, v3.16b
    // 0xa58328: b               #0xa5833c
    // 0xa5832c: fcmp            d0, d3
    // 0xa58330: b.le            #0xa5833c
    // 0xa58334: fneg            d4, d3
    // 0xa58338: mov             v3.16b, v4.16b
    // 0xa5833c: ArrayLoad: d4 = r2[0]  ; List_8
    //     0xa5833c: ldur            d4, [x2, #0x17]
    // 0xa58340: fsub            d5, d4, d2
    // 0xa58344: fcmp            d5, d0
    // 0xa58348: b.ne            #0xa58354
    // 0xa5834c: d5 = 0.000000
    //     0xa5834c: eor             v5.16b, v5.16b, v5.16b
    // 0xa58350: b               #0xa58364
    // 0xa58354: fcmp            d0, d5
    // 0xa58358: b.le            #0xa58364
    // 0xa5835c: fneg            d6, d5
    // 0xa58360: mov             v5.16b, v6.16b
    // 0xa58364: fcmp            d5, d3
    // 0xa58368: b.gt            #0xa58370
    // 0xa5836c: mov             v1.16b, v4.16b
    // 0xa58370: LoadField: d3 = r3->field_7
    //     0xa58370: ldur            d3, [x3, #7]
    // 0xa58374: fsub            d4, d3, d2
    // 0xa58378: fcmp            d4, d0
    // 0xa5837c: b.ne            #0xa58388
    // 0xa58380: d4 = 0.000000
    //     0xa58380: eor             v4.16b, v4.16b, v4.16b
    // 0xa58384: b               #0xa58398
    // 0xa58388: fcmp            d0, d4
    // 0xa5838c: b.le            #0xa58398
    // 0xa58390: fneg            d5, d4
    // 0xa58394: mov             v4.16b, v5.16b
    // 0xa58398: ArrayLoad: d5 = r3[0]  ; List_8
    //     0xa58398: ldur            d5, [x3, #0x17]
    // 0xa5839c: fsub            d6, d5, d2
    // 0xa583a0: fcmp            d6, d0
    // 0xa583a4: b.ne            #0xa583b0
    // 0xa583a8: d6 = 0.000000
    //     0xa583a8: eor             v6.16b, v6.16b, v6.16b
    // 0xa583ac: b               #0xa583c0
    // 0xa583b0: fcmp            d0, d6
    // 0xa583b4: b.le            #0xa583c0
    // 0xa583b8: fneg            d7, d6
    // 0xa583bc: mov             v6.16b, v7.16b
    // 0xa583c0: fcmp            d6, d4
    // 0xa583c4: b.gt            #0xa583cc
    // 0xa583c8: mov             v3.16b, v5.16b
    // 0xa583cc: fsub            d4, d1, d2
    // 0xa583d0: fcmp            d4, d0
    // 0xa583d4: b.ne            #0xa583e0
    // 0xa583d8: d1 = 0.000000
    //     0xa583d8: eor             v1.16b, v1.16b, v1.16b
    // 0xa583dc: b               #0xa583f4
    // 0xa583e0: fcmp            d0, d4
    // 0xa583e4: b.le            #0xa583f0
    // 0xa583e8: fneg            d1, d4
    // 0xa583ec: b               #0xa583f4
    // 0xa583f0: mov             v1.16b, v4.16b
    // 0xa583f4: fsub            d4, d3, d2
    // 0xa583f8: fcmp            d4, d0
    // 0xa583fc: b.ne            #0xa58408
    // 0xa58400: d0 = 0.000000
    //     0xa58400: eor             v0.16b, v0.16b, v0.16b
    // 0xa58404: b               #0xa5841c
    // 0xa58408: fcmp            d0, d4
    // 0xa5840c: b.le            #0xa58418
    // 0xa58410: fneg            d0, d4
    // 0xa58414: b               #0xa5841c
    // 0xa58418: mov             v0.16b, v4.16b
    // 0xa5841c: r1 = inline_Allocate_Double()
    //     0xa5841c: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa58420: add             x1, x1, #0x10
    //     0xa58424: cmp             x0, x1
    //     0xa58428: b.ls            #0xa58484
    //     0xa5842c: str             x1, [THR, #0x50]  ; THR::top
    //     0xa58430: sub             x1, x1, #0xf
    //     0xa58434: movz            x0, #0xe15c
    //     0xa58438: movk            x0, #0x3, lsl #16
    //     0xa5843c: stur            x0, [x1, #-1]
    // 0xa58440: StoreField: r1->field_7 = d1
    //     0xa58440: stur            d1, [x1, #7]
    // 0xa58444: r2 = inline_Allocate_Double()
    //     0xa58444: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xa58448: add             x2, x2, #0x10
    //     0xa5844c: cmp             x0, x2
    //     0xa58450: b.ls            #0xa58498
    //     0xa58454: str             x2, [THR, #0x50]  ; THR::top
    //     0xa58458: sub             x2, x2, #0xf
    //     0xa5845c: movz            x0, #0xe15c
    //     0xa58460: movk            x0, #0x3, lsl #16
    //     0xa58464: stur            x0, [x2, #-1]
    // 0xa58468: StoreField: r2->field_7 = d0
    //     0xa58468: stur            d0, [x2, #7]
    // 0xa5846c: r0 = compareTo()
    //     0xa5846c: bl              #0x6da84c  ; [dart:core] _Double::compareTo
    // 0xa58470: LeaveFrame
    //     0xa58470: mov             SP, fp
    //     0xa58474: ldp             fp, lr, [SP], #0x10
    // 0xa58478: ret
    //     0xa58478: ret             
    // 0xa5847c: r0 = StackOverflowSharedWithFPURegs()
    //     0xa5847c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa58480: b               #0xa58310
    // 0xa58484: stp             q0, q1, [SP, #-0x20]!
    // 0xa58488: r0 = AllocateDouble()
    //     0xa58488: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa5848c: mov             x1, x0
    // 0xa58490: ldp             q0, q1, [SP], #0x20
    // 0xa58494: b               #0xa58440
    // 0xa58498: SaveReg d0
    //     0xa58498: str             q0, [SP, #-0x10]!
    // 0xa5849c: SaveReg r1
    //     0xa5849c: str             x1, [SP, #-8]!
    // 0xa584a0: r0 = AllocateDouble()
    //     0xa584a0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa584a4: mov             x2, x0
    // 0xa584a8: RestoreReg r1
    //     0xa584a8: ldr             x1, [SP], #8
    // 0xa584ac: RestoreReg d0
    //     0xa584ac: ldr             q0, [SP], #0x10
    // 0xa584b0: b               #0xa58468
  }
  static _ _sortByDistancePreferVertical(/* No info */) {
    // ** addr: 0xa584b4, size: 0x8c
    // 0xa584b4: EnterFrame
    //     0xa584b4: stp             fp, lr, [SP, #-0x10]!
    //     0xa584b8: mov             fp, SP
    // 0xa584bc: AllocStack(0x30)
    //     0xa584bc: sub             SP, SP, #0x30
    // 0xa584c0: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa584c0: stur            x1, [fp, #-8]
    //     0xa584c4: stur            x2, [fp, #-0x10]
    // 0xa584c8: CheckStackOverflow
    //     0xa584c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa584cc: cmp             SP, x16
    //     0xa584d0: b.ls            #0xa58538
    // 0xa584d4: r1 = 1
    //     0xa584d4: movz            x1, #0x1
    // 0xa584d8: r0 = AllocateContext()
    //     0xa584d8: bl              #0xec126c  ; AllocateContextStub
    // 0xa584dc: mov             x3, x0
    // 0xa584e0: ldur            x0, [fp, #-8]
    // 0xa584e4: stur            x3, [fp, #-0x18]
    // 0xa584e8: StoreField: r3->field_f = r0
    //     0xa584e8: stur            w0, [x3, #0xf]
    // 0xa584ec: ldur            x2, [fp, #-0x10]
    // 0xa584f0: LoadField: r1 = r2->field_7
    //     0xa584f0: ldur            w1, [x2, #7]
    // 0xa584f4: DecompressPointer r1
    //     0xa584f4: add             x1, x1, HEAP, lsl #32
    // 0xa584f8: r0 = _GrowableList.of()
    //     0xa584f8: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xa584fc: ldur            x2, [fp, #-0x18]
    // 0xa58500: r1 = Function '<anonymous closure>': static.
    //     0xa58500: add             x1, PP, #0x59, lsl #12  ; [pp+0x59e88] AnonymousClosure: static (0xa58540), in [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_sortByDistancePreferVertical (0xa584b4)
    //     0xa58504: ldr             x1, [x1, #0xe88]
    // 0xa58508: stur            x0, [fp, #-8]
    // 0xa5850c: r0 = AllocateClosure()
    //     0xa5850c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa58510: r16 = <FocusNode>
    //     0xa58510: ldr             x16, [PP, #0x21f0]  ; [pp+0x21f0] TypeArguments: <FocusNode>
    // 0xa58514: ldur            lr, [fp, #-8]
    // 0xa58518: stp             lr, x16, [SP, #8]
    // 0xa5851c: str             x0, [SP]
    // 0xa58520: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa58520: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa58524: r0 = mergeSort()
    //     0xa58524: bl              #0x6b61b4  ; [package:flutter/src/foundation/collections.dart] ::mergeSort
    // 0xa58528: ldur            x0, [fp, #-8]
    // 0xa5852c: LeaveFrame
    //     0xa5852c: mov             SP, fp
    //     0xa58530: ldp             fp, lr, [SP], #0x10
    // 0xa58534: ret
    //     0xa58534: ret             
    // 0xa58538: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa58538: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5853c: b               #0xa584d4
  }
  [closure] static int <anonymous closure>(dynamic, FocusNode, FocusNode) {
    // ** addr: 0xa58540, size: 0xb8
    // 0xa58540: EnterFrame
    //     0xa58540: stp             fp, lr, [SP, #-0x10]!
    //     0xa58544: mov             fp, SP
    // 0xa58548: AllocStack(0x18)
    //     0xa58548: sub             SP, SP, #0x18
    // 0xa5854c: SetupParameters()
    //     0xa5854c: ldr             x0, [fp, #0x20]
    //     0xa58550: ldur            w2, [x0, #0x17]
    //     0xa58554: add             x2, x2, HEAP, lsl #32
    //     0xa58558: stur            x2, [fp, #-8]
    // 0xa5855c: CheckStackOverflow
    //     0xa5855c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa58560: cmp             SP, x16
    //     0xa58564: b.ls            #0xa585f0
    // 0xa58568: ldr             x1, [fp, #0x18]
    // 0xa5856c: r0 = rect()
    //     0xa5856c: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa58570: mov             x1, x0
    // 0xa58574: r0 = center()
    //     0xa58574: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa58578: ldr             x1, [fp, #0x10]
    // 0xa5857c: stur            x0, [fp, #-0x10]
    // 0xa58580: r0 = rect()
    //     0xa58580: bl              #0x6b72b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::rect
    // 0xa58584: mov             x1, x0
    // 0xa58588: r0 = center()
    //     0xa58588: bl              #0x67f01c  ; [dart:ui] Rect::center
    // 0xa5858c: mov             x4, x0
    // 0xa58590: ldur            x0, [fp, #-8]
    // 0xa58594: stur            x4, [fp, #-0x18]
    // 0xa58598: LoadField: r1 = r0->field_f
    //     0xa58598: ldur            w1, [x0, #0xf]
    // 0xa5859c: DecompressPointer r1
    //     0xa5859c: add             x1, x1, HEAP, lsl #32
    // 0xa585a0: ldur            x2, [fp, #-0x10]
    // 0xa585a4: mov             x3, x4
    // 0xa585a8: r0 = _verticalCompare()
    //     0xa585a8: bl              #0xa57c5c  ; [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_verticalCompare
    // 0xa585ac: cbnz            x0, #0xa585dc
    // 0xa585b0: ldur            x0, [fp, #-8]
    // 0xa585b4: LoadField: r1 = r0->field_f
    //     0xa585b4: ldur            w1, [x0, #0xf]
    // 0xa585b8: DecompressPointer r1
    //     0xa585b8: add             x1, x1, HEAP, lsl #32
    // 0xa585bc: ldur            x2, [fp, #-0x10]
    // 0xa585c0: ldur            x3, [fp, #-0x18]
    // 0xa585c4: r0 = _horizontalCompare()
    //     0xa585c4: bl              #0xa57850  ; [package:flutter/src/widgets/focus_traversal.dart] DirectionalFocusTraversalPolicyMixin::_horizontalCompare
    // 0xa585c8: lsl             x1, x0, #1
    // 0xa585cc: mov             x0, x1
    // 0xa585d0: LeaveFrame
    //     0xa585d0: mov             SP, fp
    //     0xa585d4: ldp             fp, lr, [SP], #0x10
    // 0xa585d8: ret
    //     0xa585d8: ret             
    // 0xa585dc: lsl             x1, x0, #1
    // 0xa585e0: mov             x0, x1
    // 0xa585e4: LeaveFrame
    //     0xa585e4: mov             SP, fp
    //     0xa585e8: ldp             fp, lr, [SP], #0x10
    // 0xa585ec: ret
    //     0xa585ec: ret             
    // 0xa585f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa585f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa585f4: b               #0xa58568
  }
}

// class id: 3823, size: 0x10, field offset: 0x8
//   const constructor, 
class DirectionalFocusIntent extends Intent {

  TraversalDirection field_8;
  bool field_c;
}

// class id: 3824, size: 0x8, field offset: 0x8
//   const constructor, 
class PreviousFocusIntent extends Intent {
}

// class id: 3825, size: 0x8, field offset: 0x8
//   const constructor, 
class NextFocusIntent extends Intent {
}

// class id: 3826, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class RequestFocusIntent extends Intent {
}

// class id: 3893, size: 0x18, field offset: 0x14
class DirectionalFocusAction extends Action<dynamic> {

  _ invoke(/* No info */) {
    // ** addr: 0xa56ccc, size: 0x8c
    // 0xa56ccc: EnterFrame
    //     0xa56ccc: stp             fp, lr, [SP, #-0x10]!
    //     0xa56cd0: mov             fp, SP
    // 0xa56cd4: CheckStackOverflow
    //     0xa56cd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa56cd8: cmp             SP, x16
    //     0xa56cdc: b.ls            #0xa56d44
    // 0xa56ce0: LoadField: r0 = r1->field_13
    //     0xa56ce0: ldur            w0, [x1, #0x13]
    // 0xa56ce4: DecompressPointer r0
    //     0xa56ce4: add             x0, x0, HEAP, lsl #32
    // 0xa56ce8: tbz             w0, #4, #0xa56d34
    // 0xa56cec: r0 = LoadStaticField(0x7d4)
    //     0xa56cec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa56cf0: ldr             x0, [x0, #0xfa8]
    // 0xa56cf4: cmp             w0, NULL
    // 0xa56cf8: b.eq            #0xa56d4c
    // 0xa56cfc: LoadField: r1 = r0->field_eb
    //     0xa56cfc: ldur            w1, [x0, #0xeb]
    // 0xa56d00: DecompressPointer r1
    //     0xa56d00: add             x1, x1, HEAP, lsl #32
    // 0xa56d04: cmp             w1, NULL
    // 0xa56d08: b.eq            #0xa56d50
    // 0xa56d0c: LoadField: r0 = r1->field_13
    //     0xa56d0c: ldur            w0, [x1, #0x13]
    // 0xa56d10: DecompressPointer r0
    //     0xa56d10: add             x0, x0, HEAP, lsl #32
    // 0xa56d14: LoadField: r1 = r0->field_2b
    //     0xa56d14: ldur            w1, [x0, #0x2b]
    // 0xa56d18: DecompressPointer r1
    //     0xa56d18: add             x1, x1, HEAP, lsl #32
    // 0xa56d1c: cmp             w1, NULL
    // 0xa56d20: b.eq            #0xa56d54
    // 0xa56d24: LoadField: r0 = r2->field_7
    //     0xa56d24: ldur            w0, [x2, #7]
    // 0xa56d28: DecompressPointer r0
    //     0xa56d28: add             x0, x0, HEAP, lsl #32
    // 0xa56d2c: mov             x2, x0
    // 0xa56d30: r0 = focusInDirection()
    //     0xa56d30: bl              #0xa56d58  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::focusInDirection
    // 0xa56d34: r0 = Null
    //     0xa56d34: mov             x0, NULL
    // 0xa56d38: LeaveFrame
    //     0xa56d38: mov             SP, fp
    //     0xa56d3c: ldp             fp, lr, [SP], #0x10
    // 0xa56d40: ret
    //     0xa56d40: ret             
    // 0xa56d44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa56d44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa56d48: b               #0xa56ce0
    // 0xa56d4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa56d4c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa56d50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa56d50: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa56d54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa56d54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3894, size: 0x14, field offset: 0x14
class PreviousFocusAction extends Action<dynamic> {

  _ invoke(/* No info */) {
    // ** addr: 0xa56c5c, size: 0x70
    // 0xa56c5c: EnterFrame
    //     0xa56c5c: stp             fp, lr, [SP, #-0x10]!
    //     0xa56c60: mov             fp, SP
    // 0xa56c64: CheckStackOverflow
    //     0xa56c64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa56c68: cmp             SP, x16
    //     0xa56c6c: b.ls            #0xa56cb8
    // 0xa56c70: r0 = LoadStaticField(0x7d4)
    //     0xa56c70: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa56c74: ldr             x0, [x0, #0xfa8]
    // 0xa56c78: cmp             w0, NULL
    // 0xa56c7c: b.eq            #0xa56cc0
    // 0xa56c80: LoadField: r1 = r0->field_eb
    //     0xa56c80: ldur            w1, [x0, #0xeb]
    // 0xa56c84: DecompressPointer r1
    //     0xa56c84: add             x1, x1, HEAP, lsl #32
    // 0xa56c88: cmp             w1, NULL
    // 0xa56c8c: b.eq            #0xa56cc4
    // 0xa56c90: LoadField: r0 = r1->field_13
    //     0xa56c90: ldur            w0, [x1, #0x13]
    // 0xa56c94: DecompressPointer r0
    //     0xa56c94: add             x0, x0, HEAP, lsl #32
    // 0xa56c98: LoadField: r1 = r0->field_2b
    //     0xa56c98: ldur            w1, [x0, #0x2b]
    // 0xa56c9c: DecompressPointer r1
    //     0xa56c9c: add             x1, x1, HEAP, lsl #32
    // 0xa56ca0: cmp             w1, NULL
    // 0xa56ca4: b.eq            #0xa56cc8
    // 0xa56ca8: r0 = previousFocus()
    //     0xa56ca8: bl              #0x6b3fb4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::previousFocus
    // 0xa56cac: LeaveFrame
    //     0xa56cac: mov             SP, fp
    //     0xa56cb0: ldp             fp, lr, [SP], #0x10
    // 0xa56cb4: ret
    //     0xa56cb4: ret             
    // 0xa56cb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa56cb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa56cbc: b               #0xa56c70
    // 0xa56cc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa56cc0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa56cc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa56cc4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa56cc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa56cc8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ toKeyEventResult(/* No info */) {
    // ** addr: 0xbda2c8, size: 0xa4
    // 0xbda2c8: EnterFrame
    //     0xbda2c8: stp             fp, lr, [SP, #-0x10]!
    //     0xbda2cc: mov             fp, SP
    // 0xbda2d0: AllocStack(0x8)
    //     0xbda2d0: sub             SP, SP, #8
    // 0xbda2d4: SetupParameters(PreviousFocusAction this /* r1 => r5 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r3, fp-0x8 */)
    //     0xbda2d4: mov             x0, x2
    //     0xbda2d8: mov             x5, x1
    //     0xbda2dc: mov             x4, x2
    //     0xbda2e0: stur            x3, [fp, #-8]
    // 0xbda2e4: r2 = Null
    //     0xbda2e4: mov             x2, NULL
    // 0xbda2e8: r1 = Null
    //     0xbda2e8: mov             x1, NULL
    // 0xbda2ec: r4 = 60
    //     0xbda2ec: movz            x4, #0x3c
    // 0xbda2f0: branchIfSmi(r0, 0xbda2fc)
    //     0xbda2f0: tbz             w0, #0, #0xbda2fc
    // 0xbda2f4: r4 = LoadClassIdInstr(r0)
    //     0xbda2f4: ldur            x4, [x0, #-1]
    //     0xbda2f8: ubfx            x4, x4, #0xc, #0x14
    // 0xbda2fc: cmp             x4, #0xef0
    // 0xbda300: b.eq            #0xbda318
    // 0xbda304: r8 = PreviousFocusIntent
    //     0xbda304: add             x8, PP, #0x57, lsl #12  ; [pp+0x57408] Type: PreviousFocusIntent
    //     0xbda308: ldr             x8, [x8, #0x408]
    // 0xbda30c: r3 = Null
    //     0xbda30c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5dce8] Null
    //     0xbda310: ldr             x3, [x3, #0xce8]
    // 0xbda314: r0 = PreviousFocusIntent()
    //     0xbda314: bl              #0x6bc094  ; IsType_PreviousFocusIntent_Stub
    // 0xbda318: ldur            x0, [fp, #-8]
    // 0xbda31c: r2 = Null
    //     0xbda31c: mov             x2, NULL
    // 0xbda320: r1 = Null
    //     0xbda320: mov             x1, NULL
    // 0xbda324: r4 = 60
    //     0xbda324: movz            x4, #0x3c
    // 0xbda328: branchIfSmi(r0, 0xbda334)
    //     0xbda328: tbz             w0, #0, #0xbda334
    // 0xbda32c: r4 = LoadClassIdInstr(r0)
    //     0xbda32c: ldur            x4, [x0, #-1]
    //     0xbda330: ubfx            x4, x4, #0xc, #0x14
    // 0xbda334: cmp             x4, #0x3f
    // 0xbda338: b.eq            #0xbda34c
    // 0xbda33c: r8 = bool
    //     0xbda33c: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xbda340: r3 = Null
    //     0xbda340: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5dcf8] Null
    //     0xbda344: ldr             x3, [x3, #0xcf8]
    // 0xbda348: r0 = bool()
    //     0xbda348: bl              #0xed4390  ; IsType_bool_Stub
    // 0xbda34c: ldur            x1, [fp, #-8]
    // 0xbda350: tbnz            w1, #4, #0xbda35c
    // 0xbda354: r0 = Instance_KeyEventResult
    //     0xbda354: ldr             x0, [PP, #0x2228]  ; [pp+0x2228] Obj!KeyEventResult@e34601
    // 0xbda358: b               #0xbda360
    // 0xbda35c: r0 = Instance_KeyEventResult
    //     0xbda35c: ldr             x0, [PP, #0x2230]  ; [pp+0x2230] Obj!KeyEventResult@e345e1
    // 0xbda360: LeaveFrame
    //     0xbda360: mov             SP, fp
    //     0xbda364: ldp             fp, lr, [SP], #0x10
    // 0xbda368: ret
    //     0xbda368: ret             
  }
}

// class id: 3895, size: 0x14, field offset: 0x14
class NextFocusAction extends Action<dynamic> {

  _ invoke(/* No info */) {
    // ** addr: 0xa56bec, size: 0x70
    // 0xa56bec: EnterFrame
    //     0xa56bec: stp             fp, lr, [SP, #-0x10]!
    //     0xa56bf0: mov             fp, SP
    // 0xa56bf4: CheckStackOverflow
    //     0xa56bf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa56bf8: cmp             SP, x16
    //     0xa56bfc: b.ls            #0xa56c48
    // 0xa56c00: r0 = LoadStaticField(0x7d4)
    //     0xa56c00: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa56c04: ldr             x0, [x0, #0xfa8]
    // 0xa56c08: cmp             w0, NULL
    // 0xa56c0c: b.eq            #0xa56c50
    // 0xa56c10: LoadField: r1 = r0->field_eb
    //     0xa56c10: ldur            w1, [x0, #0xeb]
    // 0xa56c14: DecompressPointer r1
    //     0xa56c14: add             x1, x1, HEAP, lsl #32
    // 0xa56c18: cmp             w1, NULL
    // 0xa56c1c: b.eq            #0xa56c54
    // 0xa56c20: LoadField: r0 = r1->field_13
    //     0xa56c20: ldur            w0, [x1, #0x13]
    // 0xa56c24: DecompressPointer r0
    //     0xa56c24: add             x0, x0, HEAP, lsl #32
    // 0xa56c28: LoadField: r1 = r0->field_2b
    //     0xa56c28: ldur            w1, [x0, #0x2b]
    // 0xa56c2c: DecompressPointer r1
    //     0xa56c2c: add             x1, x1, HEAP, lsl #32
    // 0xa56c30: cmp             w1, NULL
    // 0xa56c34: b.eq            #0xa56c58
    // 0xa56c38: r0 = nextFocus()
    //     0xa56c38: bl              #0x6b8d10  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::nextFocus
    // 0xa56c3c: LeaveFrame
    //     0xa56c3c: mov             SP, fp
    //     0xa56c40: ldp             fp, lr, [SP], #0x10
    // 0xa56c44: ret
    //     0xa56c44: ret             
    // 0xa56c48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa56c48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa56c4c: b               #0xa56c00
    // 0xa56c50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa56c50: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa56c54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa56c54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa56c58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa56c58: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ toKeyEventResult(/* No info */) {
    // ** addr: 0xbda224, size: 0xa4
    // 0xbda224: EnterFrame
    //     0xbda224: stp             fp, lr, [SP, #-0x10]!
    //     0xbda228: mov             fp, SP
    // 0xbda22c: AllocStack(0x8)
    //     0xbda22c: sub             SP, SP, #8
    // 0xbda230: SetupParameters(NextFocusAction this /* r1 => r5 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r3, fp-0x8 */)
    //     0xbda230: mov             x0, x2
    //     0xbda234: mov             x5, x1
    //     0xbda238: mov             x4, x2
    //     0xbda23c: stur            x3, [fp, #-8]
    // 0xbda240: r2 = Null
    //     0xbda240: mov             x2, NULL
    // 0xbda244: r1 = Null
    //     0xbda244: mov             x1, NULL
    // 0xbda248: r4 = 60
    //     0xbda248: movz            x4, #0x3c
    // 0xbda24c: branchIfSmi(r0, 0xbda258)
    //     0xbda24c: tbz             w0, #0, #0xbda258
    // 0xbda250: r4 = LoadClassIdInstr(r0)
    //     0xbda250: ldur            x4, [x0, #-1]
    //     0xbda254: ubfx            x4, x4, #0xc, #0x14
    // 0xbda258: cmp             x4, #0xef1
    // 0xbda25c: b.eq            #0xbda274
    // 0xbda260: r8 = NextFocusIntent
    //     0xbda260: add             x8, PP, #0x57, lsl #12  ; [pp+0x573f8] Type: NextFocusIntent
    //     0xbda264: ldr             x8, [x8, #0x3f8]
    // 0xbda268: r3 = Null
    //     0xbda268: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5dcc8] Null
    //     0xbda26c: ldr             x3, [x3, #0xcc8]
    // 0xbda270: r0 = NextFocusIntent()
    //     0xbda270: bl              #0x6bc0b4  ; IsType_NextFocusIntent_Stub
    // 0xbda274: ldur            x0, [fp, #-8]
    // 0xbda278: r2 = Null
    //     0xbda278: mov             x2, NULL
    // 0xbda27c: r1 = Null
    //     0xbda27c: mov             x1, NULL
    // 0xbda280: r4 = 60
    //     0xbda280: movz            x4, #0x3c
    // 0xbda284: branchIfSmi(r0, 0xbda290)
    //     0xbda284: tbz             w0, #0, #0xbda290
    // 0xbda288: r4 = LoadClassIdInstr(r0)
    //     0xbda288: ldur            x4, [x0, #-1]
    //     0xbda28c: ubfx            x4, x4, #0xc, #0x14
    // 0xbda290: cmp             x4, #0x3f
    // 0xbda294: b.eq            #0xbda2a8
    // 0xbda298: r8 = bool
    //     0xbda298: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xbda29c: r3 = Null
    //     0xbda29c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5dcd8] Null
    //     0xbda2a0: ldr             x3, [x3, #0xcd8]
    // 0xbda2a4: r0 = bool()
    //     0xbda2a4: bl              #0xed4390  ; IsType_bool_Stub
    // 0xbda2a8: ldur            x1, [fp, #-8]
    // 0xbda2ac: tbnz            w1, #4, #0xbda2b8
    // 0xbda2b0: r0 = Instance_KeyEventResult
    //     0xbda2b0: ldr             x0, [PP, #0x2228]  ; [pp+0x2228] Obj!KeyEventResult@e34601
    // 0xbda2b4: b               #0xbda2bc
    // 0xbda2b8: r0 = Instance_KeyEventResult
    //     0xbda2b8: ldr             x0, [PP, #0x2230]  ; [pp+0x2230] Obj!KeyEventResult@e345e1
    // 0xbda2bc: LeaveFrame
    //     0xbda2bc: mov             SP, fp
    //     0xbda2c0: ldp             fp, lr, [SP], #0x10
    // 0xbda2c4: ret
    //     0xbda2c4: ret             
  }
}

// class id: 3896, size: 0x14, field offset: 0x14
class RequestFocusAction extends Action<dynamic> {
}

// class id: 4215, size: 0x18, field offset: 0x14
class _FocusTraversalGroupState extends State<dynamic> {

  late final _FocusTraversalGroupNode focusNode; // offset: 0x14

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x98fa74, size: 0x150
    // 0x98fa74: EnterFrame
    //     0x98fa74: stp             fp, lr, [SP, #-0x10]!
    //     0x98fa78: mov             fp, SP
    // 0x98fa7c: AllocStack(0x10)
    //     0x98fa7c: sub             SP, SP, #0x10
    // 0x98fa80: SetupParameters(_FocusTraversalGroupState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x98fa80: mov             x4, x1
    //     0x98fa84: mov             x3, x2
    //     0x98fa88: stur            x1, [fp, #-8]
    //     0x98fa8c: stur            x2, [fp, #-0x10]
    // 0x98fa90: CheckStackOverflow
    //     0x98fa90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98fa94: cmp             SP, x16
    //     0x98fa98: b.ls            #0x98fbb4
    // 0x98fa9c: mov             x0, x3
    // 0x98faa0: r2 = Null
    //     0x98faa0: mov             x2, NULL
    // 0x98faa4: r1 = Null
    //     0x98faa4: mov             x1, NULL
    // 0x98faa8: r4 = 60
    //     0x98faa8: movz            x4, #0x3c
    // 0x98faac: branchIfSmi(r0, 0x98fab8)
    //     0x98faac: tbz             w0, #0, #0x98fab8
    // 0x98fab0: r4 = LoadClassIdInstr(r0)
    //     0x98fab0: ldur            x4, [x0, #-1]
    //     0x98fab4: ubfx            x4, x4, #0xc, #0x14
    // 0x98fab8: r17 = 4784
    //     0x98fab8: movz            x17, #0x12b0
    // 0x98fabc: cmp             x4, x17
    // 0x98fac0: b.eq            #0x98fad8
    // 0x98fac4: r8 = FocusTraversalGroup
    //     0x98fac4: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a3c0] Type: FocusTraversalGroup
    //     0x98fac8: ldr             x8, [x8, #0x3c0]
    // 0x98facc: r3 = Null
    //     0x98facc: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a3c8] Null
    //     0x98fad0: ldr             x3, [x3, #0x3c8]
    // 0x98fad4: r0 = FocusTraversalGroup()
    //     0x98fad4: bl              #0x65176c  ; IsType_FocusTraversalGroup_Stub
    // 0x98fad8: ldur            x3, [fp, #-8]
    // 0x98fadc: LoadField: r2 = r3->field_7
    //     0x98fadc: ldur            w2, [x3, #7]
    // 0x98fae0: DecompressPointer r2
    //     0x98fae0: add             x2, x2, HEAP, lsl #32
    // 0x98fae4: ldur            x0, [fp, #-0x10]
    // 0x98fae8: r1 = Null
    //     0x98fae8: mov             x1, NULL
    // 0x98faec: cmp             w2, NULL
    // 0x98faf0: b.eq            #0x98fb14
    // 0x98faf4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x98faf4: ldur            w4, [x2, #0x17]
    // 0x98faf8: DecompressPointer r4
    //     0x98faf8: add             x4, x4, HEAP, lsl #32
    // 0x98fafc: r8 = X0 bound StatefulWidget
    //     0x98fafc: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x98fb00: ldr             x8, [x8, #0x7f8]
    // 0x98fb04: LoadField: r9 = r4->field_7
    //     0x98fb04: ldur            x9, [x4, #7]
    // 0x98fb08: r3 = Null
    //     0x98fb08: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a3d8] Null
    //     0x98fb0c: ldr             x3, [x3, #0x3d8]
    // 0x98fb10: blr             x9
    // 0x98fb14: ldur            x0, [fp, #-0x10]
    // 0x98fb18: LoadField: r1 = r0->field_b
    //     0x98fb18: ldur            w1, [x0, #0xb]
    // 0x98fb1c: DecompressPointer r1
    //     0x98fb1c: add             x1, x1, HEAP, lsl #32
    // 0x98fb20: ldur            x0, [fp, #-8]
    // 0x98fb24: LoadField: r2 = r0->field_b
    //     0x98fb24: ldur            w2, [x0, #0xb]
    // 0x98fb28: DecompressPointer r2
    //     0x98fb28: add             x2, x2, HEAP, lsl #32
    // 0x98fb2c: cmp             w2, NULL
    // 0x98fb30: b.eq            #0x98fbbc
    // 0x98fb34: LoadField: r3 = r2->field_b
    //     0x98fb34: ldur            w3, [x2, #0xb]
    // 0x98fb38: DecompressPointer r3
    //     0x98fb38: add             x3, x3, HEAP, lsl #32
    // 0x98fb3c: cmp             w1, w3
    // 0x98fb40: b.eq            #0x98fba4
    // 0x98fb44: mov             x1, x0
    // 0x98fb48: LoadField: r0 = r1->field_13
    //     0x98fb48: ldur            w0, [x1, #0x13]
    // 0x98fb4c: DecompressPointer r0
    //     0x98fb4c: add             x0, x0, HEAP, lsl #32
    // 0x98fb50: r16 = Sentinel
    //     0x98fb50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x98fb54: cmp             w0, w16
    // 0x98fb58: b.ne            #0x98fb68
    // 0x98fb5c: r2 = focusNode
    //     0x98fb5c: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a3b0] Field <<EMAIL>>: late final (offset: 0x14)
    //     0x98fb60: ldr             x2, [x2, #0x3b0]
    // 0x98fb64: r0 = InitLateFinalInstanceField()
    //     0x98fb64: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x98fb68: mov             x2, x0
    // 0x98fb6c: ldur            x1, [fp, #-8]
    // 0x98fb70: LoadField: r3 = r1->field_b
    //     0x98fb70: ldur            w3, [x1, #0xb]
    // 0x98fb74: DecompressPointer r3
    //     0x98fb74: add             x3, x3, HEAP, lsl #32
    // 0x98fb78: cmp             w3, NULL
    // 0x98fb7c: b.eq            #0x98fbc0
    // 0x98fb80: LoadField: r0 = r3->field_b
    //     0x98fb80: ldur            w0, [x3, #0xb]
    // 0x98fb84: DecompressPointer r0
    //     0x98fb84: add             x0, x0, HEAP, lsl #32
    // 0x98fb88: StoreField: r2->field_67 = r0
    //     0x98fb88: stur            w0, [x2, #0x67]
    //     0x98fb8c: ldurb           w16, [x2, #-1]
    //     0x98fb90: ldurb           w17, [x0, #-1]
    //     0x98fb94: and             x16, x17, x16, lsr #2
    //     0x98fb98: tst             x16, HEAP, lsr #32
    //     0x98fb9c: b.eq            #0x98fba4
    //     0x98fba0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x98fba4: r0 = Null
    //     0x98fba4: mov             x0, NULL
    // 0x98fba8: LeaveFrame
    //     0x98fba8: mov             SP, fp
    //     0x98fbac: ldp             fp, lr, [SP], #0x10
    // 0x98fbb0: ret
    //     0x98fbb0: ret             
    // 0x98fbb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98fbb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98fbb8: b               #0x98fa9c
    // 0x98fbbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98fbbc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98fbc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98fbc0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _FocusTraversalGroupNode focusNode(_FocusTraversalGroupState) {
    // ** addr: 0x98fbc4, size: 0x80
    // 0x98fbc4: EnterFrame
    //     0x98fbc4: stp             fp, lr, [SP, #-0x10]!
    //     0x98fbc8: mov             fp, SP
    // 0x98fbcc: AllocStack(0x18)
    //     0x98fbcc: sub             SP, SP, #0x18
    // 0x98fbd0: CheckStackOverflow
    //     0x98fbd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98fbd4: cmp             SP, x16
    //     0x98fbd8: b.ls            #0x98fc38
    // 0x98fbdc: ldr             x0, [fp, #0x10]
    // 0x98fbe0: LoadField: r1 = r0->field_b
    //     0x98fbe0: ldur            w1, [x0, #0xb]
    // 0x98fbe4: DecompressPointer r1
    //     0x98fbe4: add             x1, x1, HEAP, lsl #32
    // 0x98fbe8: cmp             w1, NULL
    // 0x98fbec: b.eq            #0x98fc40
    // 0x98fbf0: LoadField: r0 = r1->field_b
    //     0x98fbf0: ldur            w0, [x1, #0xb]
    // 0x98fbf4: DecompressPointer r0
    //     0x98fbf4: add             x0, x0, HEAP, lsl #32
    // 0x98fbf8: stur            x0, [fp, #-8]
    // 0x98fbfc: r0 = _FocusTraversalGroupNode()
    //     0x98fbfc: bl              #0x98fc44  ; Allocate_FocusTraversalGroupNodeStub -> _FocusTraversalGroupNode (size=0x6c)
    // 0x98fc00: mov             x2, x0
    // 0x98fc04: ldur            x0, [fp, #-8]
    // 0x98fc08: stur            x2, [fp, #-0x10]
    // 0x98fc0c: StoreField: r2->field_67 = r0
    //     0x98fc0c: stur            w0, [x2, #0x67]
    // 0x98fc10: r16 = "FocusTraversalGroup"
    //     0x98fc10: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3a3b8] "FocusTraversalGroup"
    //     0x98fc14: ldr             x16, [x16, #0x3b8]
    // 0x98fc18: str             x16, [SP]
    // 0x98fc1c: mov             x1, x2
    // 0x98fc20: r4 = const [0, 0x2, 0x1, 0x1, debugLabel, 0x1, null]
    //     0x98fc20: ldr             x4, [PP, #0x2328]  ; [pp+0x2328] List(7) [0, 0x2, 0x1, 0x1, "debugLabel", 0x1, Null]
    // 0x98fc24: r0 = FocusNode()
    //     0x98fc24: bl              #0x693dec  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::FocusNode
    // 0x98fc28: ldur            x0, [fp, #-0x10]
    // 0x98fc2c: LeaveFrame
    //     0x98fc2c: mov             SP, fp
    //     0x98fc30: ldp             fp, lr, [SP], #0x10
    // 0x98fc34: ret
    //     0x98fc34: ret             
    // 0x98fc38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98fc38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98fc3c: b               #0x98fbdc
    // 0x98fc40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98fc40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa16580, size: 0xb8
    // 0xa16580: EnterFrame
    //     0xa16580: stp             fp, lr, [SP, #-0x10]!
    //     0xa16584: mov             fp, SP
    // 0xa16588: AllocStack(0x10)
    //     0xa16588: sub             SP, SP, #0x10
    // 0xa1658c: SetupParameters(_FocusTraversalGroupState this /* r1 => r0, fp-0x8 */)
    //     0xa1658c: mov             x0, x1
    //     0xa16590: stur            x1, [fp, #-8]
    // 0xa16594: CheckStackOverflow
    //     0xa16594: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa16598: cmp             SP, x16
    //     0xa1659c: b.ls            #0xa1662c
    // 0xa165a0: mov             x1, x0
    // 0xa165a4: LoadField: r0 = r1->field_13
    //     0xa165a4: ldur            w0, [x1, #0x13]
    // 0xa165a8: DecompressPointer r0
    //     0xa165a8: add             x0, x0, HEAP, lsl #32
    // 0xa165ac: r16 = Sentinel
    //     0xa165ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa165b0: cmp             w0, w16
    // 0xa165b4: b.ne            #0xa165c4
    // 0xa165b8: r2 = focusNode
    //     0xa165b8: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a3b0] Field <<EMAIL>>: late final (offset: 0x14)
    //     0xa165bc: ldr             x2, [x2, #0x3b0]
    // 0xa165c0: r0 = InitLateFinalInstanceField()
    //     0xa165c0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa165c4: mov             x1, x0
    // 0xa165c8: ldur            x0, [fp, #-8]
    // 0xa165cc: stur            x1, [fp, #-0x10]
    // 0xa165d0: LoadField: r2 = r0->field_b
    //     0xa165d0: ldur            w2, [x0, #0xb]
    // 0xa165d4: DecompressPointer r2
    //     0xa165d4: add             x2, x2, HEAP, lsl #32
    // 0xa165d8: cmp             w2, NULL
    // 0xa165dc: b.eq            #0xa16634
    // 0xa165e0: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xa165e0: ldur            w0, [x2, #0x17]
    // 0xa165e4: DecompressPointer r0
    //     0xa165e4: add             x0, x0, HEAP, lsl #32
    // 0xa165e8: stur            x0, [fp, #-8]
    // 0xa165ec: r0 = Focus()
    //     0xa165ec: bl              #0x9e3f78  ; AllocateFocusStub -> Focus (size=0x40)
    // 0xa165f0: ldur            x1, [fp, #-8]
    // 0xa165f4: StoreField: r0->field_f = r1
    //     0xa165f4: stur            w1, [x0, #0xf]
    // 0xa165f8: ldur            x1, [fp, #-0x10]
    // 0xa165fc: StoreField: r0->field_13 = r1
    //     0xa165fc: stur            w1, [x0, #0x13]
    // 0xa16600: r1 = false
    //     0xa16600: add             x1, NULL, #0x30  ; false
    // 0xa16604: ArrayStore: r0[0] = r1  ; List_4
    //     0xa16604: stur            w1, [x0, #0x17]
    // 0xa16608: StoreField: r0->field_37 = r1
    //     0xa16608: stur            w1, [x0, #0x37]
    // 0xa1660c: StoreField: r0->field_27 = r1
    //     0xa1660c: stur            w1, [x0, #0x27]
    // 0xa16610: r1 = true
    //     0xa16610: add             x1, NULL, #0x20  ; true
    // 0xa16614: StoreField: r0->field_2b = r1
    //     0xa16614: stur            w1, [x0, #0x2b]
    // 0xa16618: StoreField: r0->field_2f = r1
    //     0xa16618: stur            w1, [x0, #0x2f]
    // 0xa1661c: StoreField: r0->field_33 = r1
    //     0xa1661c: stur            w1, [x0, #0x33]
    // 0xa16620: LeaveFrame
    //     0xa16620: mov             SP, fp
    //     0xa16624: ldp             fp, lr, [SP], #0x10
    // 0xa16628: ret
    //     0xa16628: ret             
    // 0xa1662c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1662c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa16630: b               #0xa165a0
    // 0xa16634: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa16634: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7fdc4, size: 0x54
    // 0xa7fdc4: EnterFrame
    //     0xa7fdc4: stp             fp, lr, [SP, #-0x10]!
    //     0xa7fdc8: mov             fp, SP
    // 0xa7fdcc: CheckStackOverflow
    //     0xa7fdcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7fdd0: cmp             SP, x16
    //     0xa7fdd4: b.ls            #0xa7fe10
    // 0xa7fdd8: LoadField: r0 = r1->field_13
    //     0xa7fdd8: ldur            w0, [x1, #0x13]
    // 0xa7fddc: DecompressPointer r0
    //     0xa7fddc: add             x0, x0, HEAP, lsl #32
    // 0xa7fde0: r16 = Sentinel
    //     0xa7fde0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7fde4: cmp             w0, w16
    // 0xa7fde8: b.ne            #0xa7fdf8
    // 0xa7fdec: r2 = focusNode
    //     0xa7fdec: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a3b0] Field <<EMAIL>>: late final (offset: 0x14)
    //     0xa7fdf0: ldr             x2, [x2, #0x3b0]
    // 0xa7fdf4: r0 = InitLateFinalInstanceField()
    //     0xa7fdf4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa7fdf8: mov             x1, x0
    // 0xa7fdfc: r0 = dispose()
    //     0xa7fdfc: bl              #0xa8b4f0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::dispose
    // 0xa7fe00: r0 = Null
    //     0xa7fe00: mov             x0, NULL
    // 0xa7fe04: LeaveFrame
    //     0xa7fe04: mov             SP, fp
    //     0xa7fe08: ldp             fp, lr, [SP], #0x10
    // 0xa7fe0c: ret
    //     0xa7fe0c: ret             
    // 0xa7fe10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7fe10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7fe14: b               #0xa7fdd8
  }
}

// class id: 4784, size: 0x1c, field offset: 0xc
class FocusTraversalGroup extends StatefulWidget {

  static _ maybeOf(/* No info */) {
    // ** addr: 0x651710, size: 0x5c
    // 0x651710: EnterFrame
    //     0x651710: stp             fp, lr, [SP, #-0x10]!
    //     0x651714: mov             fp, SP
    // 0x651718: AllocStack(0x8)
    //     0x651718: sub             SP, SP, #8
    // 0x65171c: CheckStackOverflow
    //     0x65171c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x651720: cmp             SP, x16
    //     0x651724: b.ls            #0x651764
    // 0x651728: r16 = false
    //     0x651728: add             x16, NULL, #0x30  ; false
    // 0x65172c: str             x16, [SP]
    // 0x651730: r4 = const [0, 0x2, 0x1, 0x1, createDependency, 0x1, null]
    //     0x651730: ldr             x4, [PP, #0x24a0]  ; [pp+0x24a0] List(7) [0, 0x2, 0x1, 0x1, "createDependency", 0x1, Null]
    // 0x651734: r0 = maybeOf()
    //     0x651734: bl              #0x65184c  ; [package:flutter/src/widgets/focus_scope.dart] Focus::maybeOf
    // 0x651738: cmp             w0, NULL
    // 0x65173c: b.ne            #0x651750
    // 0x651740: r0 = Null
    //     0x651740: mov             x0, NULL
    // 0x651744: LeaveFrame
    //     0x651744: mov             SP, fp
    //     0x651748: ldp             fp, lr, [SP], #0x10
    // 0x65174c: ret
    //     0x65174c: ret             
    // 0x651750: mov             x1, x0
    // 0x651754: r0 = maybeOfNode()
    //     0x651754: bl              #0x651790  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalGroup::maybeOfNode
    // 0x651758: LeaveFrame
    //     0x651758: mov             SP, fp
    //     0x65175c: ldp             fp, lr, [SP], #0x10
    // 0x651760: ret
    //     0x651760: ret             
    // 0x651764: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651764: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x651768: b               #0x651728
  }
  static _ maybeOfNode(/* No info */) {
    // ** addr: 0x651790, size: 0x48
    // 0x651790: EnterFrame
    //     0x651790: stp             fp, lr, [SP, #-0x10]!
    //     0x651794: mov             fp, SP
    // 0x651798: CheckStackOverflow
    //     0x651798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65179c: cmp             SP, x16
    //     0x6517a0: b.ls            #0x6517d0
    // 0x6517a4: r0 = _getGroupNode()
    //     0x6517a4: bl              #0x6517d8  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalGroup::_getGroupNode
    // 0x6517a8: cmp             w0, NULL
    // 0x6517ac: b.ne            #0x6517b8
    // 0x6517b0: r0 = Null
    //     0x6517b0: mov             x0, NULL
    // 0x6517b4: b               #0x6517c4
    // 0x6517b8: LoadField: r1 = r0->field_67
    //     0x6517b8: ldur            w1, [x0, #0x67]
    // 0x6517bc: DecompressPointer r1
    //     0x6517bc: add             x1, x1, HEAP, lsl #32
    // 0x6517c0: mov             x0, x1
    // 0x6517c4: LeaveFrame
    //     0x6517c4: mov             SP, fp
    //     0x6517c8: ldp             fp, lr, [SP], #0x10
    // 0x6517cc: ret
    //     0x6517cc: ret             
    // 0x6517d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6517d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6517d4: b               #0x6517a4
  }
  static _ _getGroupNode(/* No info */) {
    // ** addr: 0x6517d8, size: 0x74
    // 0x6517d8: mov             x0, x1
    // 0x6517dc: CheckStackOverflow
    //     0x6517dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6517e0: cmp             SP, x16
    //     0x6517e4: b.ls            #0x651834
    // 0x6517e8: LoadField: r1 = r0->field_4f
    //     0x6517e8: ldur            w1, [x0, #0x4f]
    // 0x6517ec: DecompressPointer r1
    //     0x6517ec: add             x1, x1, HEAP, lsl #32
    // 0x6517f0: cmp             w1, NULL
    // 0x6517f4: b.eq            #0x65182c
    // 0x6517f8: LoadField: r2 = r0->field_33
    //     0x6517f8: ldur            w2, [x0, #0x33]
    // 0x6517fc: DecompressPointer r2
    //     0x6517fc: add             x2, x2, HEAP, lsl #32
    // 0x651800: cmp             w2, NULL
    // 0x651804: b.eq            #0x651824
    // 0x651808: r2 = LoadClassIdInstr(r0)
    //     0x651808: ldur            x2, [x0, #-1]
    //     0x65180c: ubfx            x2, x2, #0xc, #0x14
    // 0x651810: cmp             x2, #0xb68
    // 0x651814: b.eq            #0x651820
    // 0x651818: mov             x0, x1
    // 0x65181c: b               #0x6517dc
    // 0x651820: ret
    //     0x651820: ret             
    // 0x651824: r0 = Null
    //     0x651824: mov             x0, NULL
    // 0x651828: ret
    //     0x651828: ret             
    // 0x65182c: r0 = Null
    //     0x65182c: mov             x0, NULL
    // 0x651830: ret
    //     0x651830: ret             
    // 0x651834: EnterFrame
    //     0x651834: stp             fp, lr, [SP, #-0x10]!
    //     0x651838: mov             fp, SP
    // 0x65183c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65183c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x651840: LeaveFrame
    //     0x651840: mov             SP, fp
    //     0x651844: ldp             fp, lr, [SP], #0x10
    // 0x651848: b               #0x6517e8
  }
  static _ of(/* No info */) {
    // ** addr: 0x6b8cd8, size: 0x38
    // 0x6b8cd8: EnterFrame
    //     0x6b8cd8: stp             fp, lr, [SP, #-0x10]!
    //     0x6b8cdc: mov             fp, SP
    // 0x6b8ce0: CheckStackOverflow
    //     0x6b8ce0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b8ce4: cmp             SP, x16
    //     0x6b8ce8: b.ls            #0x6b8d04
    // 0x6b8cec: r0 = maybeOf()
    //     0x6b8cec: bl              #0x651710  ; [package:flutter/src/widgets/focus_traversal.dart] FocusTraversalGroup::maybeOf
    // 0x6b8cf0: cmp             w0, NULL
    // 0x6b8cf4: b.eq            #0x6b8d0c
    // 0x6b8cf8: LeaveFrame
    //     0x6b8cf8: mov             SP, fp
    //     0x6b8cfc: ldp             fp, lr, [SP], #0x10
    // 0x6b8d00: ret
    //     0x6b8d00: ret             
    // 0x6b8d04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b8d04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b8d08: b               #0x6b8cec
    // 0x6b8d0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b8d0c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ FocusTraversalGroup(/* No info */) {
    // ** addr: 0xa10918, size: 0xc4
    // 0xa10918: EnterFrame
    //     0xa10918: stp             fp, lr, [SP, #-0x10]!
    //     0xa1091c: mov             fp, SP
    // 0xa10920: AllocStack(0x20)
    //     0xa10920: sub             SP, SP, #0x20
    // 0xa10924: r0 = true
    //     0xa10924: add             x0, NULL, #0x20  ; true
    // 0xa10928: stur            x1, [fp, #-8]
    // 0xa1092c: mov             x16, x2
    // 0xa10930: mov             x2, x1
    // 0xa10934: mov             x1, x16
    // 0xa10938: CheckStackOverflow
    //     0xa10938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1093c: cmp             SP, x16
    //     0xa10940: b.ls            #0xa109d4
    // 0xa10944: StoreField: r2->field_f = r0
    //     0xa10944: stur            w0, [x2, #0xf]
    // 0xa10948: StoreField: r2->field_13 = r0
    //     0xa10948: stur            w0, [x2, #0x13]
    // 0xa1094c: mov             x0, x1
    // 0xa10950: ArrayStore: r2[0] = r0  ; List_4
    //     0xa10950: stur            w0, [x2, #0x17]
    //     0xa10954: ldurb           w16, [x2, #-1]
    //     0xa10958: ldurb           w17, [x0, #-1]
    //     0xa1095c: and             x16, x17, x16, lsr #2
    //     0xa10960: tst             x16, HEAP, lsr #32
    //     0xa10964: b.eq            #0xa1096c
    //     0xa10968: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa1096c: cmp             w3, NULL
    // 0xa10970: b.ne            #0xa109a0
    // 0xa10974: r16 = <FocusScopeNode, _DirectionalPolicyData>
    //     0xa10974: ldr             x16, [PP, #0x7498]  ; [pp+0x7498] TypeArguments: <FocusScopeNode, _DirectionalPolicyData>
    // 0xa10978: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa1097c: stp             lr, x16, [SP]
    // 0xa10980: r0 = Map._fromLiteral()
    //     0xa10980: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa10984: stur            x0, [fp, #-0x10]
    // 0xa10988: r0 = ReadingOrderTraversalPolicy()
    //     0xa10988: bl              #0x6b7d7c  ; AllocateReadingOrderTraversalPolicyStub -> ReadingOrderTraversalPolicy (size=0x10)
    // 0xa1098c: ldur            x1, [fp, #-0x10]
    // 0xa10990: StoreField: r0->field_b = r1
    //     0xa10990: stur            w1, [x0, #0xb]
    // 0xa10994: r1 = Closure: (FocusNode, {ScrollPositionAlignmentPolicy? alignmentPolicy, double? alignment, Duration? duration, Curve? curve}) => void from Function 'defaultTraversalRequestFocusCallback': static.
    //     0xa10994: ldr             x1, [PP, #0x74a0]  ; [pp+0x74a0] Closure: (FocusNode, {ScrollPositionAlignmentPolicy? alignmentPolicy, double? alignment, Duration? duration, Curve? curve}) => void from Function 'defaultTraversalRequestFocusCallback': static. (0x7e54fb0b7d88)
    // 0xa10998: StoreField: r0->field_7 = r1
    //     0xa10998: stur            w1, [x0, #7]
    // 0xa1099c: b               #0xa109a4
    // 0xa109a0: mov             x0, x3
    // 0xa109a4: ldur            x1, [fp, #-8]
    // 0xa109a8: StoreField: r1->field_b = r0
    //     0xa109a8: stur            w0, [x1, #0xb]
    //     0xa109ac: ldurb           w16, [x1, #-1]
    //     0xa109b0: ldurb           w17, [x0, #-1]
    //     0xa109b4: and             x16, x17, x16, lsr #2
    //     0xa109b8: tst             x16, HEAP, lsr #32
    //     0xa109bc: b.eq            #0xa109c4
    //     0xa109c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa109c4: r0 = Null
    //     0xa109c4: mov             x0, NULL
    // 0xa109c8: LeaveFrame
    //     0xa109c8: mov             SP, fp
    //     0xa109cc: ldp             fp, lr, [SP], #0x10
    // 0xa109d0: ret
    //     0xa109d0: ret             
    // 0xa109d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa109d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa109d8: b               #0xa10944
  }
  _ createState(/* No info */) {
    // ** addr: 0xa91f20, size: 0x2c
    // 0xa91f20: EnterFrame
    //     0xa91f20: stp             fp, lr, [SP, #-0x10]!
    //     0xa91f24: mov             fp, SP
    // 0xa91f28: mov             x0, x1
    // 0xa91f2c: r1 = <FocusTraversalGroup>
    //     0xa91f2c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30f70] TypeArguments: <FocusTraversalGroup>
    //     0xa91f30: ldr             x1, [x1, #0xf70]
    // 0xa91f34: r0 = _FocusTraversalGroupState()
    //     0xa91f34: bl              #0xa91f4c  ; Allocate_FocusTraversalGroupStateStub -> _FocusTraversalGroupState (size=0x18)
    // 0xa91f38: r1 = Sentinel
    //     0xa91f38: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa91f3c: StoreField: r0->field_13 = r1
    //     0xa91f3c: stur            w1, [x0, #0x13]
    // 0xa91f40: LeaveFrame
    //     0xa91f40: mov             SP, fp
    //     0xa91f44: ldp             fp, lr, [SP], #0x10
    // 0xa91f48: ret
    //     0xa91f48: ret             
  }
}

// class id: 6965, size: 0x14, field offset: 0x14
enum TraversalEdgeBehavior extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4a8dc, size: 0x64
    // 0xc4a8dc: EnterFrame
    //     0xc4a8dc: stp             fp, lr, [SP, #-0x10]!
    //     0xc4a8e0: mov             fp, SP
    // 0xc4a8e4: AllocStack(0x10)
    //     0xc4a8e4: sub             SP, SP, #0x10
    // 0xc4a8e8: SetupParameters(TraversalEdgeBehavior this /* r1 => r0, fp-0x8 */)
    //     0xc4a8e8: mov             x0, x1
    //     0xc4a8ec: stur            x1, [fp, #-8]
    // 0xc4a8f0: CheckStackOverflow
    //     0xc4a8f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4a8f4: cmp             SP, x16
    //     0xc4a8f8: b.ls            #0xc4a938
    // 0xc4a8fc: r1 = Null
    //     0xc4a8fc: mov             x1, NULL
    // 0xc4a900: r2 = 4
    //     0xc4a900: movz            x2, #0x4
    // 0xc4a904: r0 = AllocateArray()
    //     0xc4a904: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4a908: r16 = "TraversalEdgeBehavior."
    //     0xc4a908: add             x16, PP, #0x22, lsl #12  ; [pp+0x225c8] "TraversalEdgeBehavior."
    //     0xc4a90c: ldr             x16, [x16, #0x5c8]
    // 0xc4a910: StoreField: r0->field_f = r16
    //     0xc4a910: stur            w16, [x0, #0xf]
    // 0xc4a914: ldur            x1, [fp, #-8]
    // 0xc4a918: LoadField: r2 = r1->field_f
    //     0xc4a918: ldur            w2, [x1, #0xf]
    // 0xc4a91c: DecompressPointer r2
    //     0xc4a91c: add             x2, x2, HEAP, lsl #32
    // 0xc4a920: StoreField: r0->field_13 = r2
    //     0xc4a920: stur            w2, [x0, #0x13]
    // 0xc4a924: str             x0, [SP]
    // 0xc4a928: r0 = _interpolate()
    //     0xc4a928: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4a92c: LeaveFrame
    //     0xc4a92c: mov             SP, fp
    //     0xc4a930: ldp             fp, lr, [SP], #0x10
    // 0xc4a934: ret
    //     0xc4a934: ret             
    // 0xc4a938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4a938: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4a93c: b               #0xc4a8fc
  }
}

// class id: 6966, size: 0x14, field offset: 0x14
enum TraversalDirection extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4a878, size: 0x64
    // 0xc4a878: EnterFrame
    //     0xc4a878: stp             fp, lr, [SP, #-0x10]!
    //     0xc4a87c: mov             fp, SP
    // 0xc4a880: AllocStack(0x10)
    //     0xc4a880: sub             SP, SP, #0x10
    // 0xc4a884: SetupParameters(TraversalDirection this /* r1 => r0, fp-0x8 */)
    //     0xc4a884: mov             x0, x1
    //     0xc4a888: stur            x1, [fp, #-8]
    // 0xc4a88c: CheckStackOverflow
    //     0xc4a88c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4a890: cmp             SP, x16
    //     0xc4a894: b.ls            #0xc4a8d4
    // 0xc4a898: r1 = Null
    //     0xc4a898: mov             x1, NULL
    // 0xc4a89c: r2 = 4
    //     0xc4a89c: movz            x2, #0x4
    // 0xc4a8a0: r0 = AllocateArray()
    //     0xc4a8a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4a8a4: r16 = "TraversalDirection."
    //     0xc4a8a4: add             x16, PP, #0x59, lsl #12  ; [pp+0x59de8] "TraversalDirection."
    //     0xc4a8a8: ldr             x16, [x16, #0xde8]
    // 0xc4a8ac: StoreField: r0->field_f = r16
    //     0xc4a8ac: stur            w16, [x0, #0xf]
    // 0xc4a8b0: ldur            x1, [fp, #-8]
    // 0xc4a8b4: LoadField: r2 = r1->field_f
    //     0xc4a8b4: ldur            w2, [x1, #0xf]
    // 0xc4a8b8: DecompressPointer r2
    //     0xc4a8b8: add             x2, x2, HEAP, lsl #32
    // 0xc4a8bc: StoreField: r0->field_13 = r2
    //     0xc4a8bc: stur            w2, [x0, #0x13]
    // 0xc4a8c0: str             x0, [SP]
    // 0xc4a8c4: r0 = _interpolate()
    //     0xc4a8c4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4a8c8: LeaveFrame
    //     0xc4a8c8: mov             SP, fp
    //     0xc4a8cc: ldp             fp, lr, [SP], #0x10
    // 0xc4a8d0: ret
    //     0xc4a8d0: ret             
    // 0xc4a8d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4a8d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4a8d8: b               #0xc4a898
  }
}
