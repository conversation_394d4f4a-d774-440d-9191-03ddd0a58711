// lib: , url: package:flutter/src/widgets/gesture_detector.dart

// class id: 1049133, size: 0x8
class :: {
}

// class id: 2682, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class SemanticsGestureDelegate extends Object {
}

// class id: 2683, size: 0xc, field offset: 0x8
class _DefaultSemanticsGestureDelegate extends SemanticsGestureDelegate {

  _ assignSemantics(/* No info */) {
    // ** addr: 0x85d91c, size: 0xc0
    // 0x85d91c: EnterFrame
    //     0x85d91c: stp             fp, lr, [SP, #-0x10]!
    //     0x85d920: mov             fp, SP
    // 0x85d924: AllocStack(0x18)
    //     0x85d924: sub             SP, SP, #0x18
    // 0x85d928: SetupParameters(_DefaultSemanticsGestureDelegate this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x85d928: mov             x3, x1
    //     0x85d92c: mov             x0, x2
    //     0x85d930: stur            x1, [fp, #-0x10]
    //     0x85d934: stur            x2, [fp, #-0x18]
    // 0x85d938: CheckStackOverflow
    //     0x85d938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85d93c: cmp             SP, x16
    //     0x85d940: b.ls            #0x85d9d0
    // 0x85d944: LoadField: r1 = r3->field_7
    //     0x85d944: ldur            w1, [x3, #7]
    // 0x85d948: DecompressPointer r1
    //     0x85d948: add             x1, x1, HEAP, lsl #32
    // 0x85d94c: LoadField: r4 = r1->field_13
    //     0x85d94c: ldur            w4, [x1, #0x13]
    // 0x85d950: DecompressPointer r4
    //     0x85d950: add             x4, x4, HEAP, lsl #32
    // 0x85d954: stur            x4, [fp, #-8]
    // 0x85d958: cmp             w4, NULL
    // 0x85d95c: b.eq            #0x85d9d8
    // 0x85d960: mov             x1, x3
    // 0x85d964: mov             x2, x4
    // 0x85d968: r0 = _getTapHandler()
    //     0x85d968: bl              #0x85f0e8  ; [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::_getTapHandler
    // 0x85d96c: ldur            x1, [fp, #-0x18]
    // 0x85d970: mov             x2, x0
    // 0x85d974: r0 = onTap=()
    //     0x85d974: bl              #0x85f00c  ; [package:flutter/src/rendering/proxy_box.dart] RenderSemanticsGestureHandler::onTap=
    // 0x85d978: ldur            x1, [fp, #-0x10]
    // 0x85d97c: ldur            x2, [fp, #-8]
    // 0x85d980: r0 = _getLongPressHandler()
    //     0x85d980: bl              #0x85e3f8  ; [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::_getLongPressHandler
    // 0x85d984: ldur            x1, [fp, #-0x18]
    // 0x85d988: mov             x2, x0
    // 0x85d98c: r0 = onLongPress=()
    //     0x85d98c: bl              #0x85e31c  ; [package:flutter/src/rendering/proxy_box.dart] RenderSemanticsGestureHandler::onLongPress=
    // 0x85d990: ldur            x1, [fp, #-0x10]
    // 0x85d994: ldur            x2, [fp, #-8]
    // 0x85d998: r0 = _getHorizontalDragUpdateHandler()
    //     0x85d998: bl              #0x85e118  ; [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::_getHorizontalDragUpdateHandler
    // 0x85d99c: ldur            x1, [fp, #-0x18]
    // 0x85d9a0: mov             x2, x0
    // 0x85d9a4: r0 = onHorizontalDragUpdate=()
    //     0x85d9a4: bl              #0x85e03c  ; [package:flutter/src/rendering/proxy_box.dart] RenderSemanticsGestureHandler::onHorizontalDragUpdate=
    // 0x85d9a8: ldur            x1, [fp, #-0x10]
    // 0x85d9ac: ldur            x2, [fp, #-8]
    // 0x85d9b0: r0 = _getVerticalDragUpdateHandler()
    //     0x85d9b0: bl              #0x85dab8  ; [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::_getVerticalDragUpdateHandler
    // 0x85d9b4: ldur            x1, [fp, #-0x18]
    // 0x85d9b8: mov             x2, x0
    // 0x85d9bc: r0 = onVerticalDragUpdate=()
    //     0x85d9bc: bl              #0x85d9dc  ; [package:flutter/src/rendering/proxy_box.dart] RenderSemanticsGestureHandler::onVerticalDragUpdate=
    // 0x85d9c0: r0 = Null
    //     0x85d9c0: mov             x0, NULL
    // 0x85d9c4: LeaveFrame
    //     0x85d9c4: mov             SP, fp
    //     0x85d9c8: ldp             fp, lr, [SP], #0x10
    // 0x85d9cc: ret
    //     0x85d9cc: ret             
    // 0x85d9d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85d9d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85d9d4: b               #0x85d944
    // 0x85d9d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x85d9d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _getVerticalDragUpdateHandler(/* No info */) {
    // ** addr: 0x85dab8, size: 0x204
    // 0x85dab8: EnterFrame
    //     0x85dab8: stp             fp, lr, [SP, #-0x10]!
    //     0x85dabc: mov             fp, SP
    // 0x85dac0: AllocStack(0x18)
    //     0x85dac0: sub             SP, SP, #0x18
    // 0x85dac4: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x85dac4: mov             x3, x2
    //     0x85dac8: stur            x2, [fp, #-8]
    // 0x85dacc: CheckStackOverflow
    //     0x85dacc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85dad0: cmp             SP, x16
    //     0x85dad4: b.ls            #0x85dcb4
    // 0x85dad8: r0 = LoadClassIdInstr(r3)
    //     0x85dad8: ldur            x0, [x3, #-1]
    //     0x85dadc: ubfx            x0, x0, #0xc, #0x14
    // 0x85dae0: mov             x1, x3
    // 0x85dae4: r2 = VerticalDragGestureRecognizer
    //     0x85dae4: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a160] Type: VerticalDragGestureRecognizer
    //     0x85dae8: ldr             x2, [x2, #0x160]
    // 0x85daec: r0 = GDT[cid_x0 + -0x114]()
    //     0x85daec: sub             lr, x0, #0x114
    //     0x85daf0: ldr             lr, [x21, lr, lsl #3]
    //     0x85daf4: blr             lr
    // 0x85daf8: mov             x3, x0
    // 0x85dafc: r2 = Null
    //     0x85dafc: mov             x2, NULL
    // 0x85db00: r1 = Null
    //     0x85db00: mov             x1, NULL
    // 0x85db04: stur            x3, [fp, #-0x10]
    // 0x85db08: r4 = 60
    //     0x85db08: movz            x4, #0x3c
    // 0x85db0c: branchIfSmi(r0, 0x85db18)
    //     0x85db0c: tbz             w0, #0, #0x85db18
    // 0x85db10: r4 = LoadClassIdInstr(r0)
    //     0x85db10: ldur            x4, [x0, #-1]
    //     0x85db14: ubfx            x4, x4, #0xc, #0x14
    // 0x85db18: sub             x4, x4, #0xdde
    // 0x85db1c: cmp             x4, #1
    // 0x85db20: b.ls            #0x85db38
    // 0x85db24: r8 = VerticalDragGestureRecognizer?
    //     0x85db24: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a1a8] Type: VerticalDragGestureRecognizer?
    //     0x85db28: ldr             x8, [x8, #0x1a8]
    // 0x85db2c: r3 = Null
    //     0x85db2c: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a1b0] Null
    //     0x85db30: ldr             x3, [x3, #0x1b0]
    // 0x85db34: r0 = DefaultNullableTypeTest()
    //     0x85db34: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x85db38: r1 = 4
    //     0x85db38: movz            x1, #0x4
    // 0x85db3c: r0 = AllocateContext()
    //     0x85db3c: bl              #0xec126c  ; AllocateContextStub
    // 0x85db40: mov             x4, x0
    // 0x85db44: ldur            x3, [fp, #-0x10]
    // 0x85db48: stur            x4, [fp, #-0x18]
    // 0x85db4c: StoreField: r4->field_f = r3
    //     0x85db4c: stur            w3, [x4, #0xf]
    // 0x85db50: ldur            x1, [fp, #-8]
    // 0x85db54: r0 = LoadClassIdInstr(r1)
    //     0x85db54: ldur            x0, [x1, #-1]
    //     0x85db58: ubfx            x0, x0, #0xc, #0x14
    // 0x85db5c: r2 = PanGestureRecognizer
    //     0x85db5c: add             x2, PP, #0x25, lsl #12  ; [pp+0x25348] Type: PanGestureRecognizer
    //     0x85db60: ldr             x2, [x2, #0x348]
    // 0x85db64: r0 = GDT[cid_x0 + -0x114]()
    //     0x85db64: sub             lr, x0, #0x114
    //     0x85db68: ldr             lr, [x21, lr, lsl #3]
    //     0x85db6c: blr             lr
    // 0x85db70: mov             x3, x0
    // 0x85db74: r2 = Null
    //     0x85db74: mov             x2, NULL
    // 0x85db78: r1 = Null
    //     0x85db78: mov             x1, NULL
    // 0x85db7c: stur            x3, [fp, #-8]
    // 0x85db80: r4 = 60
    //     0x85db80: movz            x4, #0x3c
    // 0x85db84: branchIfSmi(r0, 0x85db90)
    //     0x85db84: tbz             w0, #0, #0x85db90
    // 0x85db88: r4 = LoadClassIdInstr(r0)
    //     0x85db88: ldur            x4, [x0, #-1]
    //     0x85db8c: ubfx            x4, x4, #0xc, #0x14
    // 0x85db90: sub             x4, x4, #0xde0
    // 0x85db94: cmp             x4, #1
    // 0x85db98: b.ls            #0x85dbb0
    // 0x85db9c: r8 = PanGestureRecognizer?
    //     0x85db9c: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a1c0] Type: PanGestureRecognizer?
    //     0x85dba0: ldr             x8, [x8, #0x1c0]
    // 0x85dba4: r3 = Null
    //     0x85dba4: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a1c8] Null
    //     0x85dba8: ldr             x3, [x3, #0x1c8]
    // 0x85dbac: r0 = DefaultNullableTypeTest()
    //     0x85dbac: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x85dbb0: ldur            x0, [fp, #-8]
    // 0x85dbb4: ldur            x3, [fp, #-0x18]
    // 0x85dbb8: StoreField: r3->field_13 = r0
    //     0x85dbb8: stur            w0, [x3, #0x13]
    //     0x85dbbc: ldurb           w16, [x3, #-1]
    //     0x85dbc0: ldurb           w17, [x0, #-1]
    //     0x85dbc4: and             x16, x17, x16, lsr #2
    //     0x85dbc8: tst             x16, HEAP, lsr #32
    //     0x85dbcc: b.eq            #0x85dbd4
    //     0x85dbd0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x85dbd4: ldur            x0, [fp, #-0x10]
    // 0x85dbd8: cmp             w0, NULL
    // 0x85dbdc: b.ne            #0x85dbe8
    // 0x85dbe0: r4 = Null
    //     0x85dbe0: mov             x4, NULL
    // 0x85dbe4: b               #0x85dc00
    // 0x85dbe8: mov             x2, x3
    // 0x85dbec: r1 = Function '<anonymous closure>':.
    //     0x85dbec: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a1d8] AnonymousClosure: (0x85dea0), in [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::_getVerticalDragUpdateHandler (0x85dab8)
    //     0x85dbf0: ldr             x1, [x1, #0x1d8]
    // 0x85dbf4: r0 = AllocateClosure()
    //     0x85dbf4: bl              #0xec1630  ; AllocateClosureStub
    // 0x85dbf8: mov             x4, x0
    // 0x85dbfc: ldur            x3, [fp, #-0x18]
    // 0x85dc00: ldur            x1, [fp, #-8]
    // 0x85dc04: mov             x0, x4
    // 0x85dc08: stur            x4, [fp, #-0x10]
    // 0x85dc0c: ArrayStore: r3[0] = r0  ; List_4
    //     0x85dc0c: stur            w0, [x3, #0x17]
    //     0x85dc10: ldurb           w16, [x3, #-1]
    //     0x85dc14: ldurb           w17, [x0, #-1]
    //     0x85dc18: and             x16, x17, x16, lsr #2
    //     0x85dc1c: tst             x16, HEAP, lsr #32
    //     0x85dc20: b.eq            #0x85dc28
    //     0x85dc24: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x85dc28: cmp             w1, NULL
    // 0x85dc2c: b.ne            #0x85dc40
    // 0x85dc30: mov             x2, x3
    // 0x85dc34: mov             x1, x4
    // 0x85dc38: r3 = Null
    //     0x85dc38: mov             x3, NULL
    // 0x85dc3c: b               #0x85dc5c
    // 0x85dc40: mov             x2, x3
    // 0x85dc44: r1 = Function '<anonymous closure>':.
    //     0x85dc44: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a1e0] AnonymousClosure: (0x85dd54), in [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::_getVerticalDragUpdateHandler (0x85dab8)
    //     0x85dc48: ldr             x1, [x1, #0x1e0]
    // 0x85dc4c: r0 = AllocateClosure()
    //     0x85dc4c: bl              #0xec1630  ; AllocateClosureStub
    // 0x85dc50: mov             x3, x0
    // 0x85dc54: ldur            x2, [fp, #-0x18]
    // 0x85dc58: ldur            x1, [fp, #-0x10]
    // 0x85dc5c: mov             x0, x3
    // 0x85dc60: StoreField: r2->field_1b = r0
    //     0x85dc60: stur            w0, [x2, #0x1b]
    //     0x85dc64: ldurb           w16, [x2, #-1]
    //     0x85dc68: ldurb           w17, [x0, #-1]
    //     0x85dc6c: and             x16, x17, x16, lsr #2
    //     0x85dc70: tst             x16, HEAP, lsr #32
    //     0x85dc74: b.eq            #0x85dc7c
    //     0x85dc78: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x85dc7c: cmp             w1, NULL
    // 0x85dc80: b.ne            #0x85dc9c
    // 0x85dc84: cmp             w3, NULL
    // 0x85dc88: b.ne            #0x85dc9c
    // 0x85dc8c: r0 = Null
    //     0x85dc8c: mov             x0, NULL
    // 0x85dc90: LeaveFrame
    //     0x85dc90: mov             SP, fp
    //     0x85dc94: ldp             fp, lr, [SP], #0x10
    // 0x85dc98: ret
    //     0x85dc98: ret             
    // 0x85dc9c: r1 = Function '<anonymous closure>':.
    //     0x85dc9c: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a1e8] AnonymousClosure: (0x85dcbc), in [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::_getVerticalDragUpdateHandler (0x85dab8)
    //     0x85dca0: ldr             x1, [x1, #0x1e8]
    // 0x85dca4: r0 = AllocateClosure()
    //     0x85dca4: bl              #0xec1630  ; AllocateClosureStub
    // 0x85dca8: LeaveFrame
    //     0x85dca8: mov             SP, fp
    //     0x85dcac: ldp             fp, lr, [SP], #0x10
    // 0x85dcb0: ret
    //     0x85dcb0: ret             
    // 0x85dcb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85dcb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85dcb8: b               #0x85dad8
  }
  [closure] void <anonymous closure>(dynamic, DragUpdateDetails) {
    // ** addr: 0x85dcbc, size: 0x98
    // 0x85dcbc: EnterFrame
    //     0x85dcbc: stp             fp, lr, [SP, #-0x10]!
    //     0x85dcc0: mov             fp, SP
    // 0x85dcc4: AllocStack(0x18)
    //     0x85dcc4: sub             SP, SP, #0x18
    // 0x85dcc8: SetupParameters()
    //     0x85dcc8: ldr             x0, [fp, #0x18]
    //     0x85dccc: ldur            w1, [x0, #0x17]
    //     0x85dcd0: add             x1, x1, HEAP, lsl #32
    //     0x85dcd4: stur            x1, [fp, #-8]
    // 0x85dcd8: CheckStackOverflow
    //     0x85dcd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85dcdc: cmp             SP, x16
    //     0x85dce0: b.ls            #0x85dd4c
    // 0x85dce4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x85dce4: ldur            w0, [x1, #0x17]
    // 0x85dce8: DecompressPointer r0
    //     0x85dce8: add             x0, x0, HEAP, lsl #32
    // 0x85dcec: cmp             w0, NULL
    // 0x85dcf0: b.ne            #0x85dcfc
    // 0x85dcf4: mov             x0, x1
    // 0x85dcf8: b               #0x85dd14
    // 0x85dcfc: ldr             x16, [fp, #0x10]
    // 0x85dd00: stp             x16, x0, [SP]
    // 0x85dd04: ClosureCall
    //     0x85dd04: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85dd08: ldur            x2, [x0, #0x1f]
    //     0x85dd0c: blr             x2
    // 0x85dd10: ldur            x0, [fp, #-8]
    // 0x85dd14: LoadField: r1 = r0->field_1b
    //     0x85dd14: ldur            w1, [x0, #0x1b]
    // 0x85dd18: DecompressPointer r1
    //     0x85dd18: add             x1, x1, HEAP, lsl #32
    // 0x85dd1c: cmp             w1, NULL
    // 0x85dd20: b.eq            #0x85dd3c
    // 0x85dd24: ldr             x16, [fp, #0x10]
    // 0x85dd28: stp             x16, x1, [SP]
    // 0x85dd2c: mov             x0, x1
    // 0x85dd30: ClosureCall
    //     0x85dd30: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85dd34: ldur            x2, [x0, #0x1f]
    //     0x85dd38: blr             x2
    // 0x85dd3c: r0 = Null
    //     0x85dd3c: mov             x0, NULL
    // 0x85dd40: LeaveFrame
    //     0x85dd40: mov             SP, fp
    //     0x85dd44: ldp             fp, lr, [SP], #0x10
    // 0x85dd48: ret
    //     0x85dd48: ret             
    // 0x85dd4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85dd4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85dd50: b               #0x85dce4
  }
  [closure] void <anonymous closure>(dynamic, DragUpdateDetails) {
    // ** addr: 0x85dd54, size: 0x14c
    // 0x85dd54: EnterFrame
    //     0x85dd54: stp             fp, lr, [SP, #-0x10]!
    //     0x85dd58: mov             fp, SP
    // 0x85dd5c: AllocStack(0x20)
    //     0x85dd5c: sub             SP, SP, #0x20
    // 0x85dd60: SetupParameters()
    //     0x85dd60: ldr             x0, [fp, #0x18]
    //     0x85dd64: ldur            w1, [x0, #0x17]
    //     0x85dd68: add             x1, x1, HEAP, lsl #32
    // 0x85dd6c: CheckStackOverflow
    //     0x85dd6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85dd70: cmp             SP, x16
    //     0x85dd74: b.ls            #0x85de94
    // 0x85dd78: LoadField: r0 = r1->field_13
    //     0x85dd78: ldur            w0, [x1, #0x13]
    // 0x85dd7c: DecompressPointer r0
    //     0x85dd7c: add             x0, x0, HEAP, lsl #32
    // 0x85dd80: stur            x0, [fp, #-0x10]
    // 0x85dd84: cmp             w0, NULL
    // 0x85dd88: b.eq            #0x85de9c
    // 0x85dd8c: LoadField: r1 = r0->field_2b
    //     0x85dd8c: ldur            w1, [x0, #0x2b]
    // 0x85dd90: DecompressPointer r1
    //     0x85dd90: add             x1, x1, HEAP, lsl #32
    // 0x85dd94: stur            x1, [fp, #-8]
    // 0x85dd98: cmp             w1, NULL
    // 0x85dd9c: b.eq            #0x85ddc8
    // 0x85dda0: r0 = DragDownDetails()
    //     0x85dda0: bl              #0x7cd40c  ; AllocateDragDownDetailsStub -> DragDownDetails (size=0xc)
    // 0x85dda4: r1 = Instance_Offset
    //     0x85dda4: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x85dda8: StoreField: r0->field_7 = r1
    //     0x85dda8: stur            w1, [x0, #7]
    // 0x85ddac: ldur            x16, [fp, #-8]
    // 0x85ddb0: stp             x0, x16, [SP]
    // 0x85ddb4: ldur            x0, [fp, #-8]
    // 0x85ddb8: ClosureCall
    //     0x85ddb8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85ddbc: ldur            x2, [x0, #0x1f]
    //     0x85ddc0: blr             x2
    // 0x85ddc4: ldur            x0, [fp, #-0x10]
    // 0x85ddc8: LoadField: r1 = r0->field_2f
    //     0x85ddc8: ldur            w1, [x0, #0x2f]
    // 0x85ddcc: DecompressPointer r1
    //     0x85ddcc: add             x1, x1, HEAP, lsl #32
    // 0x85ddd0: stur            x1, [fp, #-8]
    // 0x85ddd4: cmp             w1, NULL
    // 0x85ddd8: b.ne            #0x85dde4
    // 0x85dddc: mov             x1, x0
    // 0x85dde0: b               #0x85de0c
    // 0x85dde4: r0 = DragStartDetails()
    //     0x85dde4: bl              #0x75a0a8  ; AllocateDragStartDetailsStub -> DragStartDetails (size=0x14)
    // 0x85dde8: r1 = Instance_Offset
    //     0x85dde8: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x85ddec: StoreField: r0->field_b = r1
    //     0x85ddec: stur            w1, [x0, #0xb]
    // 0x85ddf0: ldur            x16, [fp, #-8]
    // 0x85ddf4: stp             x0, x16, [SP]
    // 0x85ddf8: ldur            x0, [fp, #-8]
    // 0x85ddfc: ClosureCall
    //     0x85ddfc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85de00: ldur            x2, [x0, #0x1f]
    //     0x85de04: blr             x2
    // 0x85de08: ldur            x1, [fp, #-0x10]
    // 0x85de0c: LoadField: r0 = r1->field_33
    //     0x85de0c: ldur            w0, [x1, #0x33]
    // 0x85de10: DecompressPointer r0
    //     0x85de10: add             x0, x0, HEAP, lsl #32
    // 0x85de14: cmp             w0, NULL
    // 0x85de18: b.ne            #0x85de24
    // 0x85de1c: mov             x0, x1
    // 0x85de20: b               #0x85de3c
    // 0x85de24: ldr             x16, [fp, #0x10]
    // 0x85de28: stp             x16, x0, [SP]
    // 0x85de2c: ClosureCall
    //     0x85de2c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85de30: ldur            x2, [x0, #0x1f]
    //     0x85de34: blr             x2
    // 0x85de38: ldur            x0, [fp, #-0x10]
    // 0x85de3c: LoadField: r1 = r0->field_37
    //     0x85de3c: ldur            w1, [x0, #0x37]
    // 0x85de40: DecompressPointer r1
    //     0x85de40: add             x1, x1, HEAP, lsl #32
    // 0x85de44: stur            x1, [fp, #-8]
    // 0x85de48: cmp             w1, NULL
    // 0x85de4c: b.eq            #0x85de84
    // 0x85de50: r0 = DragEndDetails()
    //     0x85de50: bl              #0x7d6e54  ; AllocateDragEndDetailsStub -> DragEndDetails (size=0x14)
    // 0x85de54: mov             x1, x0
    // 0x85de58: r0 = Instance_Velocity
    //     0x85de58: add             x0, PP, #0x39, lsl #12  ; [pp+0x39c58] Obj!Velocity@e14a31
    //     0x85de5c: ldr             x0, [x0, #0xc58]
    // 0x85de60: StoreField: r1->field_7 = r0
    //     0x85de60: stur            w0, [x1, #7]
    // 0x85de64: r0 = Instance_Offset
    //     0x85de64: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x85de68: StoreField: r1->field_f = r0
    //     0x85de68: stur            w0, [x1, #0xf]
    // 0x85de6c: ldur            x16, [fp, #-8]
    // 0x85de70: stp             x1, x16, [SP]
    // 0x85de74: ldur            x0, [fp, #-8]
    // 0x85de78: ClosureCall
    //     0x85de78: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85de7c: ldur            x2, [x0, #0x1f]
    //     0x85de80: blr             x2
    // 0x85de84: r0 = Null
    //     0x85de84: mov             x0, NULL
    // 0x85de88: LeaveFrame
    //     0x85de88: mov             SP, fp
    //     0x85de8c: ldp             fp, lr, [SP], #0x10
    // 0x85de90: ret
    //     0x85de90: ret             
    // 0x85de94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85de94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85de98: b               #0x85dd78
    // 0x85de9c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x85de9c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, DragUpdateDetails) {
    // ** addr: 0x85dea0, size: 0x154
    // 0x85dea0: EnterFrame
    //     0x85dea0: stp             fp, lr, [SP, #-0x10]!
    //     0x85dea4: mov             fp, SP
    // 0x85dea8: AllocStack(0x20)
    //     0x85dea8: sub             SP, SP, #0x20
    // 0x85deac: SetupParameters()
    //     0x85deac: ldr             x0, [fp, #0x18]
    //     0x85deb0: ldur            w1, [x0, #0x17]
    //     0x85deb4: add             x1, x1, HEAP, lsl #32
    // 0x85deb8: CheckStackOverflow
    //     0x85deb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85debc: cmp             SP, x16
    //     0x85dec0: b.ls            #0x85dfe8
    // 0x85dec4: LoadField: r0 = r1->field_f
    //     0x85dec4: ldur            w0, [x1, #0xf]
    // 0x85dec8: DecompressPointer r0
    //     0x85dec8: add             x0, x0, HEAP, lsl #32
    // 0x85decc: stur            x0, [fp, #-0x10]
    // 0x85ded0: cmp             w0, NULL
    // 0x85ded4: b.eq            #0x85dff0
    // 0x85ded8: LoadField: r1 = r0->field_2b
    //     0x85ded8: ldur            w1, [x0, #0x2b]
    // 0x85dedc: DecompressPointer r1
    //     0x85dedc: add             x1, x1, HEAP, lsl #32
    // 0x85dee0: stur            x1, [fp, #-8]
    // 0x85dee4: cmp             w1, NULL
    // 0x85dee8: b.eq            #0x85df14
    // 0x85deec: r0 = DragDownDetails()
    //     0x85deec: bl              #0x7cd40c  ; AllocateDragDownDetailsStub -> DragDownDetails (size=0xc)
    // 0x85def0: r1 = Instance_Offset
    //     0x85def0: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x85def4: StoreField: r0->field_7 = r1
    //     0x85def4: stur            w1, [x0, #7]
    // 0x85def8: ldur            x16, [fp, #-8]
    // 0x85defc: stp             x0, x16, [SP]
    // 0x85df00: ldur            x0, [fp, #-8]
    // 0x85df04: ClosureCall
    //     0x85df04: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85df08: ldur            x2, [x0, #0x1f]
    //     0x85df0c: blr             x2
    // 0x85df10: ldur            x0, [fp, #-0x10]
    // 0x85df14: LoadField: r1 = r0->field_2f
    //     0x85df14: ldur            w1, [x0, #0x2f]
    // 0x85df18: DecompressPointer r1
    //     0x85df18: add             x1, x1, HEAP, lsl #32
    // 0x85df1c: stur            x1, [fp, #-8]
    // 0x85df20: cmp             w1, NULL
    // 0x85df24: b.ne            #0x85df30
    // 0x85df28: mov             x1, x0
    // 0x85df2c: b               #0x85df58
    // 0x85df30: r0 = DragStartDetails()
    //     0x85df30: bl              #0x75a0a8  ; AllocateDragStartDetailsStub -> DragStartDetails (size=0x14)
    // 0x85df34: r1 = Instance_Offset
    //     0x85df34: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x85df38: StoreField: r0->field_b = r1
    //     0x85df38: stur            w1, [x0, #0xb]
    // 0x85df3c: ldur            x16, [fp, #-8]
    // 0x85df40: stp             x0, x16, [SP]
    // 0x85df44: ldur            x0, [fp, #-8]
    // 0x85df48: ClosureCall
    //     0x85df48: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85df4c: ldur            x2, [x0, #0x1f]
    //     0x85df50: blr             x2
    // 0x85df54: ldur            x1, [fp, #-0x10]
    // 0x85df58: LoadField: r0 = r1->field_33
    //     0x85df58: ldur            w0, [x1, #0x33]
    // 0x85df5c: DecompressPointer r0
    //     0x85df5c: add             x0, x0, HEAP, lsl #32
    // 0x85df60: cmp             w0, NULL
    // 0x85df64: b.ne            #0x85df70
    // 0x85df68: mov             x0, x1
    // 0x85df6c: b               #0x85df88
    // 0x85df70: ldr             x16, [fp, #0x10]
    // 0x85df74: stp             x16, x0, [SP]
    // 0x85df78: ClosureCall
    //     0x85df78: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85df7c: ldur            x2, [x0, #0x1f]
    //     0x85df80: blr             x2
    // 0x85df84: ldur            x0, [fp, #-0x10]
    // 0x85df88: LoadField: r1 = r0->field_37
    //     0x85df88: ldur            w1, [x0, #0x37]
    // 0x85df8c: DecompressPointer r1
    //     0x85df8c: add             x1, x1, HEAP, lsl #32
    // 0x85df90: stur            x1, [fp, #-8]
    // 0x85df94: cmp             w1, NULL
    // 0x85df98: b.eq            #0x85dfd8
    // 0x85df9c: r0 = DragEndDetails()
    //     0x85df9c: bl              #0x7d6e54  ; AllocateDragEndDetailsStub -> DragEndDetails (size=0x14)
    // 0x85dfa0: mov             x1, x0
    // 0x85dfa4: r0 = Instance_Velocity
    //     0x85dfa4: add             x0, PP, #0x39, lsl #12  ; [pp+0x39c58] Obj!Velocity@e14a31
    //     0x85dfa8: ldr             x0, [x0, #0xc58]
    // 0x85dfac: StoreField: r1->field_7 = r0
    //     0x85dfac: stur            w0, [x1, #7]
    // 0x85dfb0: r0 = 0.000000
    //     0x85dfb0: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x85dfb4: StoreField: r1->field_b = r0
    //     0x85dfb4: stur            w0, [x1, #0xb]
    // 0x85dfb8: r0 = Instance_Offset
    //     0x85dfb8: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x85dfbc: StoreField: r1->field_f = r0
    //     0x85dfbc: stur            w0, [x1, #0xf]
    // 0x85dfc0: ldur            x16, [fp, #-8]
    // 0x85dfc4: stp             x1, x16, [SP]
    // 0x85dfc8: ldur            x0, [fp, #-8]
    // 0x85dfcc: ClosureCall
    //     0x85dfcc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85dfd0: ldur            x2, [x0, #0x1f]
    //     0x85dfd4: blr             x2
    // 0x85dfd8: r0 = Null
    //     0x85dfd8: mov             x0, NULL
    // 0x85dfdc: LeaveFrame
    //     0x85dfdc: mov             SP, fp
    //     0x85dfe0: ldp             fp, lr, [SP], #0x10
    // 0x85dfe4: ret
    //     0x85dfe4: ret             
    // 0x85dfe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85dfe8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85dfec: b               #0x85dec4
    // 0x85dff0: r0 = NullErrorSharedWithoutFPURegs()
    //     0x85dff0: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _getHorizontalDragUpdateHandler(/* No info */) {
    // ** addr: 0x85e118, size: 0x204
    // 0x85e118: EnterFrame
    //     0x85e118: stp             fp, lr, [SP, #-0x10]!
    //     0x85e11c: mov             fp, SP
    // 0x85e120: AllocStack(0x18)
    //     0x85e120: sub             SP, SP, #0x18
    // 0x85e124: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x85e124: mov             x3, x2
    //     0x85e128: stur            x2, [fp, #-8]
    // 0x85e12c: CheckStackOverflow
    //     0x85e12c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85e130: cmp             SP, x16
    //     0x85e134: b.ls            #0x85e314
    // 0x85e138: r0 = LoadClassIdInstr(r3)
    //     0x85e138: ldur            x0, [x3, #-1]
    //     0x85e13c: ubfx            x0, x0, #0xc, #0x14
    // 0x85e140: mov             x1, x3
    // 0x85e144: r2 = HorizontalDragGestureRecognizer
    //     0x85e144: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a180] Type: HorizontalDragGestureRecognizer
    //     0x85e148: ldr             x2, [x2, #0x180]
    // 0x85e14c: r0 = GDT[cid_x0 + -0x114]()
    //     0x85e14c: sub             lr, x0, #0x114
    //     0x85e150: ldr             lr, [x21, lr, lsl #3]
    //     0x85e154: blr             lr
    // 0x85e158: mov             x3, x0
    // 0x85e15c: r2 = Null
    //     0x85e15c: mov             x2, NULL
    // 0x85e160: r1 = Null
    //     0x85e160: mov             x1, NULL
    // 0x85e164: stur            x3, [fp, #-0x10]
    // 0x85e168: r4 = 60
    //     0x85e168: movz            x4, #0x3c
    // 0x85e16c: branchIfSmi(r0, 0x85e178)
    //     0x85e16c: tbz             w0, #0, #0x85e178
    // 0x85e170: r4 = LoadClassIdInstr(r0)
    //     0x85e170: ldur            x4, [x0, #-1]
    //     0x85e174: ubfx            x4, x4, #0xc, #0x14
    // 0x85e178: sub             x4, x4, #0xddc
    // 0x85e17c: cmp             x4, #1
    // 0x85e180: b.ls            #0x85e198
    // 0x85e184: r8 = HorizontalDragGestureRecognizer?
    //     0x85e184: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a1f8] Type: HorizontalDragGestureRecognizer?
    //     0x85e188: ldr             x8, [x8, #0x1f8]
    // 0x85e18c: r3 = Null
    //     0x85e18c: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a200] Null
    //     0x85e190: ldr             x3, [x3, #0x200]
    // 0x85e194: r0 = DefaultNullableTypeTest()
    //     0x85e194: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x85e198: r1 = 4
    //     0x85e198: movz            x1, #0x4
    // 0x85e19c: r0 = AllocateContext()
    //     0x85e19c: bl              #0xec126c  ; AllocateContextStub
    // 0x85e1a0: mov             x4, x0
    // 0x85e1a4: ldur            x3, [fp, #-0x10]
    // 0x85e1a8: stur            x4, [fp, #-0x18]
    // 0x85e1ac: StoreField: r4->field_f = r3
    //     0x85e1ac: stur            w3, [x4, #0xf]
    // 0x85e1b0: ldur            x1, [fp, #-8]
    // 0x85e1b4: r0 = LoadClassIdInstr(r1)
    //     0x85e1b4: ldur            x0, [x1, #-1]
    //     0x85e1b8: ubfx            x0, x0, #0xc, #0x14
    // 0x85e1bc: r2 = PanGestureRecognizer
    //     0x85e1bc: add             x2, PP, #0x25, lsl #12  ; [pp+0x25348] Type: PanGestureRecognizer
    //     0x85e1c0: ldr             x2, [x2, #0x348]
    // 0x85e1c4: r0 = GDT[cid_x0 + -0x114]()
    //     0x85e1c4: sub             lr, x0, #0x114
    //     0x85e1c8: ldr             lr, [x21, lr, lsl #3]
    //     0x85e1cc: blr             lr
    // 0x85e1d0: mov             x3, x0
    // 0x85e1d4: r2 = Null
    //     0x85e1d4: mov             x2, NULL
    // 0x85e1d8: r1 = Null
    //     0x85e1d8: mov             x1, NULL
    // 0x85e1dc: stur            x3, [fp, #-8]
    // 0x85e1e0: r4 = 60
    //     0x85e1e0: movz            x4, #0x3c
    // 0x85e1e4: branchIfSmi(r0, 0x85e1f0)
    //     0x85e1e4: tbz             w0, #0, #0x85e1f0
    // 0x85e1e8: r4 = LoadClassIdInstr(r0)
    //     0x85e1e8: ldur            x4, [x0, #-1]
    //     0x85e1ec: ubfx            x4, x4, #0xc, #0x14
    // 0x85e1f0: sub             x4, x4, #0xde0
    // 0x85e1f4: cmp             x4, #1
    // 0x85e1f8: b.ls            #0x85e210
    // 0x85e1fc: r8 = PanGestureRecognizer?
    //     0x85e1fc: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a1c0] Type: PanGestureRecognizer?
    //     0x85e200: ldr             x8, [x8, #0x1c0]
    // 0x85e204: r3 = Null
    //     0x85e204: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a210] Null
    //     0x85e208: ldr             x3, [x3, #0x210]
    // 0x85e20c: r0 = DefaultNullableTypeTest()
    //     0x85e20c: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x85e210: ldur            x0, [fp, #-8]
    // 0x85e214: ldur            x3, [fp, #-0x18]
    // 0x85e218: StoreField: r3->field_13 = r0
    //     0x85e218: stur            w0, [x3, #0x13]
    //     0x85e21c: ldurb           w16, [x3, #-1]
    //     0x85e220: ldurb           w17, [x0, #-1]
    //     0x85e224: and             x16, x17, x16, lsr #2
    //     0x85e228: tst             x16, HEAP, lsr #32
    //     0x85e22c: b.eq            #0x85e234
    //     0x85e230: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x85e234: ldur            x0, [fp, #-0x10]
    // 0x85e238: cmp             w0, NULL
    // 0x85e23c: b.ne            #0x85e248
    // 0x85e240: r4 = Null
    //     0x85e240: mov             x4, NULL
    // 0x85e244: b               #0x85e260
    // 0x85e248: mov             x2, x3
    // 0x85e24c: r1 = Function '<anonymous closure>':.
    //     0x85e24c: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a220] AnonymousClosure: (0x85dea0), in [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::_getVerticalDragUpdateHandler (0x85dab8)
    //     0x85e250: ldr             x1, [x1, #0x220]
    // 0x85e254: r0 = AllocateClosure()
    //     0x85e254: bl              #0xec1630  ; AllocateClosureStub
    // 0x85e258: mov             x4, x0
    // 0x85e25c: ldur            x3, [fp, #-0x18]
    // 0x85e260: ldur            x1, [fp, #-8]
    // 0x85e264: mov             x0, x4
    // 0x85e268: stur            x4, [fp, #-0x10]
    // 0x85e26c: ArrayStore: r3[0] = r0  ; List_4
    //     0x85e26c: stur            w0, [x3, #0x17]
    //     0x85e270: ldurb           w16, [x3, #-1]
    //     0x85e274: ldurb           w17, [x0, #-1]
    //     0x85e278: and             x16, x17, x16, lsr #2
    //     0x85e27c: tst             x16, HEAP, lsr #32
    //     0x85e280: b.eq            #0x85e288
    //     0x85e284: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x85e288: cmp             w1, NULL
    // 0x85e28c: b.ne            #0x85e2a0
    // 0x85e290: mov             x2, x3
    // 0x85e294: mov             x1, x4
    // 0x85e298: r3 = Null
    //     0x85e298: mov             x3, NULL
    // 0x85e29c: b               #0x85e2bc
    // 0x85e2a0: mov             x2, x3
    // 0x85e2a4: r1 = Function '<anonymous closure>':.
    //     0x85e2a4: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a228] AnonymousClosure: (0x85dd54), in [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::_getVerticalDragUpdateHandler (0x85dab8)
    //     0x85e2a8: ldr             x1, [x1, #0x228]
    // 0x85e2ac: r0 = AllocateClosure()
    //     0x85e2ac: bl              #0xec1630  ; AllocateClosureStub
    // 0x85e2b0: mov             x3, x0
    // 0x85e2b4: ldur            x2, [fp, #-0x18]
    // 0x85e2b8: ldur            x1, [fp, #-0x10]
    // 0x85e2bc: mov             x0, x3
    // 0x85e2c0: StoreField: r2->field_1b = r0
    //     0x85e2c0: stur            w0, [x2, #0x1b]
    //     0x85e2c4: ldurb           w16, [x2, #-1]
    //     0x85e2c8: ldurb           w17, [x0, #-1]
    //     0x85e2cc: and             x16, x17, x16, lsr #2
    //     0x85e2d0: tst             x16, HEAP, lsr #32
    //     0x85e2d4: b.eq            #0x85e2dc
    //     0x85e2d8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x85e2dc: cmp             w1, NULL
    // 0x85e2e0: b.ne            #0x85e2fc
    // 0x85e2e4: cmp             w3, NULL
    // 0x85e2e8: b.ne            #0x85e2fc
    // 0x85e2ec: r0 = Null
    //     0x85e2ec: mov             x0, NULL
    // 0x85e2f0: LeaveFrame
    //     0x85e2f0: mov             SP, fp
    //     0x85e2f4: ldp             fp, lr, [SP], #0x10
    // 0x85e2f8: ret
    //     0x85e2f8: ret             
    // 0x85e2fc: r1 = Function '<anonymous closure>':.
    //     0x85e2fc: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a230] AnonymousClosure: (0x85dcbc), in [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::_getVerticalDragUpdateHandler (0x85dab8)
    //     0x85e300: ldr             x1, [x1, #0x230]
    // 0x85e304: r0 = AllocateClosure()
    //     0x85e304: bl              #0xec1630  ; AllocateClosureStub
    // 0x85e308: LeaveFrame
    //     0x85e308: mov             SP, fp
    //     0x85e30c: ldp             fp, lr, [SP], #0x10
    // 0x85e310: ret
    //     0x85e310: ret             
    // 0x85e314: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85e314: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85e318: b               #0x85e138
  }
  _ _getLongPressHandler(/* No info */) {
    // ** addr: 0x85e3f8, size: 0xc8
    // 0x85e3f8: EnterFrame
    //     0x85e3f8: stp             fp, lr, [SP, #-0x10]!
    //     0x85e3fc: mov             fp, SP
    // 0x85e400: AllocStack(0x8)
    //     0x85e400: sub             SP, SP, #8
    // 0x85e404: SetupParameters(_DefaultSemanticsGestureDelegate this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x85e404: mov             x0, x1
    //     0x85e408: mov             x1, x2
    // 0x85e40c: CheckStackOverflow
    //     0x85e40c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85e410: cmp             SP, x16
    //     0x85e414: b.ls            #0x85e4b8
    // 0x85e418: r0 = LoadClassIdInstr(r1)
    //     0x85e418: ldur            x0, [x1, #-1]
    //     0x85e41c: ubfx            x0, x0, #0xc, #0x14
    // 0x85e420: r2 = LongPressGestureRecognizer
    //     0x85e420: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a140] Type: LongPressGestureRecognizer
    //     0x85e424: ldr             x2, [x2, #0x140]
    // 0x85e428: r0 = GDT[cid_x0 + -0x114]()
    //     0x85e428: sub             lr, x0, #0x114
    //     0x85e42c: ldr             lr, [x21, lr, lsl #3]
    //     0x85e430: blr             lr
    // 0x85e434: mov             x3, x0
    // 0x85e438: r2 = Null
    //     0x85e438: mov             x2, NULL
    // 0x85e43c: r1 = Null
    //     0x85e43c: mov             x1, NULL
    // 0x85e440: stur            x3, [fp, #-8]
    // 0x85e444: r4 = 60
    //     0x85e444: movz            x4, #0x3c
    // 0x85e448: branchIfSmi(r0, 0x85e454)
    //     0x85e448: tbz             w0, #0, #0x85e454
    // 0x85e44c: r4 = LoadClassIdInstr(r0)
    //     0x85e44c: ldur            x4, [x0, #-1]
    //     0x85e450: ubfx            x4, x4, #0xc, #0x14
    // 0x85e454: cmp             x4, #0xdd9
    // 0x85e458: b.eq            #0x85e470
    // 0x85e45c: r8 = LongPressGestureRecognizer?
    //     0x85e45c: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a238] Type: LongPressGestureRecognizer?
    //     0x85e460: ldr             x8, [x8, #0x238]
    // 0x85e464: r3 = Null
    //     0x85e464: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a240] Null
    //     0x85e468: ldr             x3, [x3, #0x240]
    // 0x85e46c: r0 = DefaultNullableTypeTest()
    //     0x85e46c: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x85e470: r1 = 1
    //     0x85e470: movz            x1, #0x1
    // 0x85e474: r0 = AllocateContext()
    //     0x85e474: bl              #0xec126c  ; AllocateContextStub
    // 0x85e478: mov             x1, x0
    // 0x85e47c: ldur            x0, [fp, #-8]
    // 0x85e480: StoreField: r1->field_f = r0
    //     0x85e480: stur            w0, [x1, #0xf]
    // 0x85e484: cmp             w0, NULL
    // 0x85e488: b.ne            #0x85e49c
    // 0x85e48c: r0 = Null
    //     0x85e48c: mov             x0, NULL
    // 0x85e490: LeaveFrame
    //     0x85e490: mov             SP, fp
    //     0x85e494: ldp             fp, lr, [SP], #0x10
    // 0x85e498: ret
    //     0x85e498: ret             
    // 0x85e49c: mov             x2, x1
    // 0x85e4a0: r1 = Function '<anonymous closure>':.
    //     0x85e4a0: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a250] AnonymousClosure: (0x85e4c0), in [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::_getLongPressHandler (0x85e3f8)
    //     0x85e4a4: ldr             x1, [x1, #0x250]
    // 0x85e4a8: r0 = AllocateClosure()
    //     0x85e4a8: bl              #0xec1630  ; AllocateClosureStub
    // 0x85e4ac: LeaveFrame
    //     0x85e4ac: mov             SP, fp
    //     0x85e4b0: ldp             fp, lr, [SP], #0x10
    // 0x85e4b4: ret
    //     0x85e4b4: ret             
    // 0x85e4b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85e4b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85e4bc: b               #0x85e418
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x85e4c0, size: 0x100
    // 0x85e4c0: EnterFrame
    //     0x85e4c0: stp             fp, lr, [SP, #-0x10]!
    //     0x85e4c4: mov             fp, SP
    // 0x85e4c8: AllocStack(0x18)
    //     0x85e4c8: sub             SP, SP, #0x18
    // 0x85e4cc: SetupParameters()
    //     0x85e4cc: ldr             x0, [fp, #0x10]
    //     0x85e4d0: ldur            w1, [x0, #0x17]
    //     0x85e4d4: add             x1, x1, HEAP, lsl #32
    // 0x85e4d8: CheckStackOverflow
    //     0x85e4d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85e4dc: cmp             SP, x16
    //     0x85e4e0: b.ls            #0x85e5b4
    // 0x85e4e4: LoadField: r2 = r1->field_f
    //     0x85e4e4: ldur            w2, [x1, #0xf]
    // 0x85e4e8: DecompressPointer r2
    //     0x85e4e8: add             x2, x2, HEAP, lsl #32
    // 0x85e4ec: stur            x2, [fp, #-8]
    // 0x85e4f0: cmp             w2, NULL
    // 0x85e4f4: b.eq            #0x85e5bc
    // 0x85e4f8: LoadField: r0 = r2->field_5f
    //     0x85e4f8: ldur            w0, [x2, #0x5f]
    // 0x85e4fc: DecompressPointer r0
    //     0x85e4fc: add             x0, x0, HEAP, lsl #32
    // 0x85e500: cmp             w0, NULL
    // 0x85e504: b.ne            #0x85e510
    // 0x85e508: mov             x1, x2
    // 0x85e50c: b               #0x85e52c
    // 0x85e510: r16 = Instance_LongPressStartDetails
    //     0x85e510: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3a258] Obj!LongPressStartDetails@e14a71
    //     0x85e514: ldr             x16, [x16, #0x258]
    // 0x85e518: stp             x16, x0, [SP]
    // 0x85e51c: ClosureCall
    //     0x85e51c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85e520: ldur            x2, [x0, #0x1f]
    //     0x85e524: blr             x2
    // 0x85e528: ldur            x1, [fp, #-8]
    // 0x85e52c: LoadField: r0 = r1->field_5b
    //     0x85e52c: ldur            w0, [x1, #0x5b]
    // 0x85e530: DecompressPointer r0
    //     0x85e530: add             x0, x0, HEAP, lsl #32
    // 0x85e534: cmp             w0, NULL
    // 0x85e538: b.eq            #0x85e550
    // 0x85e53c: str             x0, [SP]
    // 0x85e540: ClosureCall
    //     0x85e540: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x85e544: ldur            x2, [x0, #0x1f]
    //     0x85e548: blr             x2
    // 0x85e54c: ldur            x1, [fp, #-8]
    // 0x85e550: LoadField: r0 = r1->field_6b
    //     0x85e550: ldur            w0, [x1, #0x6b]
    // 0x85e554: DecompressPointer r0
    //     0x85e554: add             x0, x0, HEAP, lsl #32
    // 0x85e558: cmp             w0, NULL
    // 0x85e55c: b.ne            #0x85e568
    // 0x85e560: mov             x0, x1
    // 0x85e564: b               #0x85e584
    // 0x85e568: r16 = Instance_LongPressEndDetails
    //     0x85e568: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3a260] Obj!LongPressEndDetails@e14a51
    //     0x85e56c: ldr             x16, [x16, #0x260]
    // 0x85e570: stp             x16, x0, [SP]
    // 0x85e574: ClosureCall
    //     0x85e574: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85e578: ldur            x2, [x0, #0x1f]
    //     0x85e57c: blr             x2
    // 0x85e580: ldur            x0, [fp, #-8]
    // 0x85e584: LoadField: r1 = r0->field_67
    //     0x85e584: ldur            w1, [x0, #0x67]
    // 0x85e588: DecompressPointer r1
    //     0x85e588: add             x1, x1, HEAP, lsl #32
    // 0x85e58c: cmp             w1, NULL
    // 0x85e590: b.eq            #0x85e5a4
    // 0x85e594: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x85e594: ldur            w0, [x1, #0x17]
    // 0x85e598: DecompressPointer r0
    //     0x85e598: add             x0, x0, HEAP, lsl #32
    // 0x85e59c: mov             x1, x0
    // 0x85e5a0: r0 = _handlePressUp()
    //     0x85e5a0: bl              #0x85e61c  ; [package:flutter/src/material/tooltip.dart] TooltipState::_handlePressUp
    // 0x85e5a4: r0 = Null
    //     0x85e5a4: mov             x0, NULL
    // 0x85e5a8: LeaveFrame
    //     0x85e5a8: mov             SP, fp
    //     0x85e5ac: ldp             fp, lr, [SP], #0x10
    // 0x85e5b0: ret
    //     0x85e5b0: ret             
    // 0x85e5b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85e5b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85e5b8: b               #0x85e4e4
    // 0x85e5bc: r0 = NullErrorSharedWithoutFPURegs()
    //     0x85e5bc: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _getTapHandler(/* No info */) {
    // ** addr: 0x85f0e8, size: 0xcc
    // 0x85f0e8: EnterFrame
    //     0x85f0e8: stp             fp, lr, [SP, #-0x10]!
    //     0x85f0ec: mov             fp, SP
    // 0x85f0f0: AllocStack(0x8)
    //     0x85f0f0: sub             SP, SP, #8
    // 0x85f0f4: SetupParameters(_DefaultSemanticsGestureDelegate this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x85f0f4: mov             x0, x1
    //     0x85f0f8: mov             x1, x2
    // 0x85f0fc: CheckStackOverflow
    //     0x85f0fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85f100: cmp             SP, x16
    //     0x85f104: b.ls            #0x85f1ac
    // 0x85f108: r0 = LoadClassIdInstr(r1)
    //     0x85f108: ldur            x0, [x1, #-1]
    //     0x85f10c: ubfx            x0, x0, #0xc, #0x14
    // 0x85f110: r2 = TapGestureRecognizer
    //     0x85f110: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a120] Type: TapGestureRecognizer
    //     0x85f114: ldr             x2, [x2, #0x120]
    // 0x85f118: r0 = GDT[cid_x0 + -0x114]()
    //     0x85f118: sub             lr, x0, #0x114
    //     0x85f11c: ldr             lr, [x21, lr, lsl #3]
    //     0x85f120: blr             lr
    // 0x85f124: mov             x3, x0
    // 0x85f128: r2 = Null
    //     0x85f128: mov             x2, NULL
    // 0x85f12c: r1 = Null
    //     0x85f12c: mov             x1, NULL
    // 0x85f130: stur            x3, [fp, #-8]
    // 0x85f134: r4 = 60
    //     0x85f134: movz            x4, #0x3c
    // 0x85f138: branchIfSmi(r0, 0x85f144)
    //     0x85f138: tbz             w0, #0, #0x85f144
    // 0x85f13c: r4 = LoadClassIdInstr(r0)
    //     0x85f13c: ldur            x4, [x0, #-1]
    //     0x85f140: ubfx            x4, x4, #0xc, #0x14
    // 0x85f144: sub             x4, x4, #0xdd7
    // 0x85f148: cmp             x4, #1
    // 0x85f14c: b.ls            #0x85f164
    // 0x85f150: r8 = TapGestureRecognizer?
    //     0x85f150: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a2c8] Type: TapGestureRecognizer?
    //     0x85f154: ldr             x8, [x8, #0x2c8]
    // 0x85f158: r3 = Null
    //     0x85f158: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a2d0] Null
    //     0x85f15c: ldr             x3, [x3, #0x2d0]
    // 0x85f160: r0 = DefaultNullableTypeTest()
    //     0x85f160: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x85f164: r1 = 1
    //     0x85f164: movz            x1, #0x1
    // 0x85f168: r0 = AllocateContext()
    //     0x85f168: bl              #0xec126c  ; AllocateContextStub
    // 0x85f16c: mov             x1, x0
    // 0x85f170: ldur            x0, [fp, #-8]
    // 0x85f174: StoreField: r1->field_f = r0
    //     0x85f174: stur            w0, [x1, #0xf]
    // 0x85f178: cmp             w0, NULL
    // 0x85f17c: b.ne            #0x85f190
    // 0x85f180: r0 = Null
    //     0x85f180: mov             x0, NULL
    // 0x85f184: LeaveFrame
    //     0x85f184: mov             SP, fp
    //     0x85f188: ldp             fp, lr, [SP], #0x10
    // 0x85f18c: ret
    //     0x85f18c: ret             
    // 0x85f190: mov             x2, x1
    // 0x85f194: r1 = Function '<anonymous closure>':.
    //     0x85f194: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a2e0] AnonymousClosure: (0x85f1b4), in [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::_getTapHandler (0x85f0e8)
    //     0x85f198: ldr             x1, [x1, #0x2e0]
    // 0x85f19c: r0 = AllocateClosure()
    //     0x85f19c: bl              #0xec1630  ; AllocateClosureStub
    // 0x85f1a0: LeaveFrame
    //     0x85f1a0: mov             SP, fp
    //     0x85f1a4: ldp             fp, lr, [SP], #0x10
    // 0x85f1a8: ret
    //     0x85f1a8: ret             
    // 0x85f1ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85f1ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85f1b0: b               #0x85f108
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x85f1b4, size: 0xf8
    // 0x85f1b4: EnterFrame
    //     0x85f1b4: stp             fp, lr, [SP, #-0x10]!
    //     0x85f1b8: mov             fp, SP
    // 0x85f1bc: AllocStack(0x20)
    //     0x85f1bc: sub             SP, SP, #0x20
    // 0x85f1c0: SetupParameters()
    //     0x85f1c0: ldr             x0, [fp, #0x10]
    //     0x85f1c4: ldur            w1, [x0, #0x17]
    //     0x85f1c8: add             x1, x1, HEAP, lsl #32
    // 0x85f1cc: CheckStackOverflow
    //     0x85f1cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85f1d0: cmp             SP, x16
    //     0x85f1d4: b.ls            #0x85f2a0
    // 0x85f1d8: LoadField: r0 = r1->field_f
    //     0x85f1d8: ldur            w0, [x1, #0xf]
    // 0x85f1dc: DecompressPointer r0
    //     0x85f1dc: add             x0, x0, HEAP, lsl #32
    // 0x85f1e0: stur            x0, [fp, #-0x10]
    // 0x85f1e4: cmp             w0, NULL
    // 0x85f1e8: b.eq            #0x85f2a8
    // 0x85f1ec: LoadField: r1 = r0->field_57
    //     0x85f1ec: ldur            w1, [x0, #0x57]
    // 0x85f1f0: DecompressPointer r1
    //     0x85f1f0: add             x1, x1, HEAP, lsl #32
    // 0x85f1f4: stur            x1, [fp, #-8]
    // 0x85f1f8: cmp             w1, NULL
    // 0x85f1fc: b.eq            #0x85f22c
    // 0x85f200: r0 = TapDownDetails()
    //     0x85f200: bl              #0x85f2b8  ; AllocateTapDownDetailsStub -> TapDownDetails (size=0x10)
    // 0x85f204: r1 = Instance_Offset
    //     0x85f204: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x85f208: StoreField: r0->field_7 = r1
    //     0x85f208: stur            w1, [x0, #7]
    // 0x85f20c: StoreField: r0->field_b = r1
    //     0x85f20c: stur            w1, [x0, #0xb]
    // 0x85f210: ldur            x16, [fp, #-8]
    // 0x85f214: stp             x0, x16, [SP]
    // 0x85f218: ldur            x0, [fp, #-8]
    // 0x85f21c: ClosureCall
    //     0x85f21c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85f220: ldur            x2, [x0, #0x1f]
    //     0x85f224: blr             x2
    // 0x85f228: ldur            x0, [fp, #-0x10]
    // 0x85f22c: LoadField: r1 = r0->field_5b
    //     0x85f22c: ldur            w1, [x0, #0x5b]
    // 0x85f230: DecompressPointer r1
    //     0x85f230: add             x1, x1, HEAP, lsl #32
    // 0x85f234: stur            x1, [fp, #-8]
    // 0x85f238: cmp             w1, NULL
    // 0x85f23c: b.eq            #0x85f26c
    // 0x85f240: r0 = TapUpDetails()
    //     0x85f240: bl              #0x85f2ac  ; AllocateTapUpDetailsStub -> TapUpDetails (size=0xc)
    // 0x85f244: mov             x1, x0
    // 0x85f248: r0 = Instance_Offset
    //     0x85f248: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x85f24c: StoreField: r1->field_7 = r0
    //     0x85f24c: stur            w0, [x1, #7]
    // 0x85f250: ldur            x16, [fp, #-8]
    // 0x85f254: stp             x1, x16, [SP]
    // 0x85f258: ldur            x0, [fp, #-8]
    // 0x85f25c: ClosureCall
    //     0x85f25c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x85f260: ldur            x2, [x0, #0x1f]
    //     0x85f264: blr             x2
    // 0x85f268: ldur            x0, [fp, #-0x10]
    // 0x85f26c: LoadField: r1 = r0->field_5f
    //     0x85f26c: ldur            w1, [x0, #0x5f]
    // 0x85f270: DecompressPointer r1
    //     0x85f270: add             x1, x1, HEAP, lsl #32
    // 0x85f274: cmp             w1, NULL
    // 0x85f278: b.eq            #0x85f290
    // 0x85f27c: str             x1, [SP]
    // 0x85f280: mov             x0, x1
    // 0x85f284: ClosureCall
    //     0x85f284: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x85f288: ldur            x2, [x0, #0x1f]
    //     0x85f28c: blr             x2
    // 0x85f290: r0 = Null
    //     0x85f290: mov             x0, NULL
    // 0x85f294: LeaveFrame
    //     0x85f294: mov             SP, fp
    //     0x85f298: ldp             fp, lr, [SP], #0x10
    // 0x85f29c: ret
    //     0x85f29c: ret             
    // 0x85f2a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85f2a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85f2a4: b               #0x85f1d8
    // 0x85f2a8: r0 = NullErrorSharedWithoutFPURegs()
    //     0x85f2a8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
}

// class id: 2684, size: 0xc, field offset: 0x8
//   const constructor, 
abstract class GestureRecognizerFactory<X0 bound GestureRecognizer> extends Object {
}

// class id: 2686, size: 0x14, field offset: 0xc
//   const constructor, 
class GestureRecognizerFactoryWithHandlers<X0 bound GestureRecognizer> extends GestureRecognizerFactory<X0 bound GestureRecognizer> {

  _ initializer(/* No info */) {
    // ** addr: 0xdb8b74, size: 0x98
    // 0xdb8b74: EnterFrame
    //     0xdb8b74: stp             fp, lr, [SP, #-0x10]!
    //     0xdb8b78: mov             fp, SP
    // 0xdb8b7c: AllocStack(0x20)
    //     0xdb8b7c: sub             SP, SP, #0x20
    // 0xdb8b80: SetupParameters(GestureRecognizerFactoryWithHandlers<X0 bound GestureRecognizer> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xdb8b80: mov             x4, x1
    //     0xdb8b84: mov             x3, x2
    //     0xdb8b88: stur            x1, [fp, #-8]
    //     0xdb8b8c: stur            x2, [fp, #-0x10]
    // 0xdb8b90: CheckStackOverflow
    //     0xdb8b90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb8b94: cmp             SP, x16
    //     0xdb8b98: b.ls            #0xdb8c04
    // 0xdb8b9c: LoadField: r2 = r4->field_7
    //     0xdb8b9c: ldur            w2, [x4, #7]
    // 0xdb8ba0: DecompressPointer r2
    //     0xdb8ba0: add             x2, x2, HEAP, lsl #32
    // 0xdb8ba4: mov             x0, x3
    // 0xdb8ba8: r1 = Null
    //     0xdb8ba8: mov             x1, NULL
    // 0xdb8bac: cmp             w2, NULL
    // 0xdb8bb0: b.eq            #0xdb8bd4
    // 0xdb8bb4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xdb8bb4: ldur            w4, [x2, #0x17]
    // 0xdb8bb8: DecompressPointer r4
    //     0xdb8bb8: add             x4, x4, HEAP, lsl #32
    // 0xdb8bbc: r8 = X0 bound GestureRecognizer
    //     0xdb8bbc: add             x8, PP, #0x46, lsl #12  ; [pp+0x46230] TypeParameter: X0 bound GestureRecognizer
    //     0xdb8bc0: ldr             x8, [x8, #0x230]
    // 0xdb8bc4: LoadField: r9 = r4->field_7
    //     0xdb8bc4: ldur            x9, [x4, #7]
    // 0xdb8bc8: r3 = Null
    //     0xdb8bc8: add             x3, PP, #0x46, lsl #12  ; [pp+0x46238] Null
    //     0xdb8bcc: ldr             x3, [x3, #0x238]
    // 0xdb8bd0: blr             x9
    // 0xdb8bd4: ldur            x0, [fp, #-8]
    // 0xdb8bd8: LoadField: r1 = r0->field_f
    //     0xdb8bd8: ldur            w1, [x0, #0xf]
    // 0xdb8bdc: DecompressPointer r1
    //     0xdb8bdc: add             x1, x1, HEAP, lsl #32
    // 0xdb8be0: ldur            x16, [fp, #-0x10]
    // 0xdb8be4: stp             x16, x1, [SP]
    // 0xdb8be8: mov             x0, x1
    // 0xdb8bec: ClosureCall
    //     0xdb8bec: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xdb8bf0: ldur            x2, [x0, #0x1f]
    //     0xdb8bf4: blr             x2
    // 0xdb8bf8: LeaveFrame
    //     0xdb8bf8: mov             SP, fp
    //     0xdb8bfc: ldp             fp, lr, [SP], #0x10
    // 0xdb8c00: ret
    //     0xdb8c00: ret             
    // 0xdb8c04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb8c04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb8c08: b               #0xdb8b9c
  }
  _ constructor(/* No info */) {
    // ** addr: 0xdb8c9c, size: 0x44
    // 0xdb8c9c: EnterFrame
    //     0xdb8c9c: stp             fp, lr, [SP, #-0x10]!
    //     0xdb8ca0: mov             fp, SP
    // 0xdb8ca4: AllocStack(0x8)
    //     0xdb8ca4: sub             SP, SP, #8
    // 0xdb8ca8: CheckStackOverflow
    //     0xdb8ca8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb8cac: cmp             SP, x16
    //     0xdb8cb0: b.ls            #0xdb8cd8
    // 0xdb8cb4: LoadField: r0 = r1->field_b
    //     0xdb8cb4: ldur            w0, [x1, #0xb]
    // 0xdb8cb8: DecompressPointer r0
    //     0xdb8cb8: add             x0, x0, HEAP, lsl #32
    // 0xdb8cbc: str             x0, [SP]
    // 0xdb8cc0: ClosureCall
    //     0xdb8cc0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xdb8cc4: ldur            x2, [x0, #0x1f]
    //     0xdb8cc8: blr             x2
    // 0xdb8ccc: LeaveFrame
    //     0xdb8ccc: mov             SP, fp
    //     0xdb8cd0: ldp             fp, lr, [SP], #0x10
    // 0xdb8cd4: ret
    //     0xdb8cd4: ret             
    // 0xdb8cd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb8cd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb8cdc: b               #0xdb8cb4
  }
}

// class id: 4213, size: 0x1c, field offset: 0x14
class RawGestureDetectorState extends State<dynamic> {

  _ replaceSemanticsActions(/* No info */) {
    // ** addr: 0x7a7200, size: 0xc4
    // 0x7a7200: EnterFrame
    //     0x7a7200: stp             fp, lr, [SP, #-0x10]!
    //     0x7a7204: mov             fp, SP
    // 0x7a7208: AllocStack(0x10)
    //     0x7a7208: sub             SP, SP, #0x10
    // 0x7a720c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x7a720c: stur            x2, [fp, #-8]
    // 0x7a7210: CheckStackOverflow
    //     0x7a7210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a7214: cmp             SP, x16
    //     0x7a7218: b.ls            #0x7a72b0
    // 0x7a721c: LoadField: r0 = r1->field_b
    //     0x7a721c: ldur            w0, [x1, #0xb]
    // 0x7a7220: DecompressPointer r0
    //     0x7a7220: add             x0, x0, HEAP, lsl #32
    // 0x7a7224: cmp             w0, NULL
    // 0x7a7228: b.eq            #0x7a72b8
    // 0x7a722c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x7a722c: ldur            w3, [x0, #0x17]
    // 0x7a7230: DecompressPointer r3
    //     0x7a7230: add             x3, x3, HEAP, lsl #32
    // 0x7a7234: tbnz            w3, #4, #0x7a7248
    // 0x7a7238: r0 = Null
    //     0x7a7238: mov             x0, NULL
    // 0x7a723c: LeaveFrame
    //     0x7a723c: mov             SP, fp
    //     0x7a7240: ldp             fp, lr, [SP], #0x10
    // 0x7a7244: ret
    //     0x7a7244: ret             
    // 0x7a7248: LoadField: r0 = r1->field_f
    //     0x7a7248: ldur            w0, [x1, #0xf]
    // 0x7a724c: DecompressPointer r0
    //     0x7a724c: add             x0, x0, HEAP, lsl #32
    // 0x7a7250: cmp             w0, NULL
    // 0x7a7254: b.eq            #0x7a72bc
    // 0x7a7258: mov             x1, x0
    // 0x7a725c: r0 = renderObject()
    //     0x7a725c: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0x7a7260: mov             x3, x0
    // 0x7a7264: r2 = Null
    //     0x7a7264: mov             x2, NULL
    // 0x7a7268: r1 = Null
    //     0x7a7268: mov             x1, NULL
    // 0x7a726c: stur            x3, [fp, #-0x10]
    // 0x7a7270: r4 = LoadClassIdInstr(r0)
    //     0x7a7270: ldur            x4, [x0, #-1]
    //     0x7a7274: ubfx            x4, x4, #0xc, #0x14
    // 0x7a7278: cmp             x4, #0xc48
    // 0x7a727c: b.eq            #0x7a728c
    // 0x7a7280: r8 = RenderSemanticsGestureHandler?
    //     0x7a7280: ldr             x8, [PP, #0x76d8]  ; [pp+0x76d8] Type: RenderSemanticsGestureHandler?
    // 0x7a7284: r3 = Null
    //     0x7a7284: ldr             x3, [PP, #0x76e0]  ; [pp+0x76e0] Null
    // 0x7a7288: r0 = DefaultNullableTypeTest()
    //     0x7a7288: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7a728c: ldur            x1, [fp, #-0x10]
    // 0x7a7290: cmp             w1, NULL
    // 0x7a7294: b.eq            #0x7a72c0
    // 0x7a7298: ldur            x2, [fp, #-8]
    // 0x7a729c: r0 = validActions=()
    //     0x7a729c: bl              #0x7a72e8  ; [package:flutter/src/rendering/proxy_box.dart] RenderSemanticsGestureHandler::validActions=
    // 0x7a72a0: r0 = Null
    //     0x7a72a0: mov             x0, NULL
    // 0x7a72a4: LeaveFrame
    //     0x7a72a4: mov             SP, fp
    //     0x7a72a8: ldp             fp, lr, [SP], #0x10
    // 0x7a72ac: ret
    //     0x7a72ac: ret             
    // 0x7a72b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a72b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a72b4: b               #0x7a721c
    // 0x7a72b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7a72b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7a72bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7a72bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7a72c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7a72c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _handlePointerPanZoomStart(dynamic, PointerPanZoomStartEvent) {
    // ** addr: 0x80a9c4, size: 0x3c
    // 0x80a9c4: EnterFrame
    //     0x80a9c4: stp             fp, lr, [SP, #-0x10]!
    //     0x80a9c8: mov             fp, SP
    // 0x80a9cc: ldr             x0, [fp, #0x18]
    // 0x80a9d0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x80a9d0: ldur            w1, [x0, #0x17]
    // 0x80a9d4: DecompressPointer r1
    //     0x80a9d4: add             x1, x1, HEAP, lsl #32
    // 0x80a9d8: CheckStackOverflow
    //     0x80a9d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80a9dc: cmp             SP, x16
    //     0x80a9e0: b.ls            #0x80a9f8
    // 0x80a9e4: ldr             x2, [fp, #0x10]
    // 0x80a9e8: r0 = _handlePointerPanZoomStart()
    //     0x80a9e8: bl              #0x80aa3c  ; [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_handlePointerPanZoomStart
    // 0x80a9ec: LeaveFrame
    //     0x80a9ec: mov             SP, fp
    //     0x80a9f0: ldp             fp, lr, [SP], #0x10
    // 0x80a9f4: ret
    //     0x80a9f4: ret             
    // 0x80a9f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80a9f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80a9fc: b               #0x80a9e4
  }
  _ _handlePointerPanZoomStart(/* No info */) {
    // ** addr: 0x80aa3c, size: 0x1cc
    // 0x80aa3c: EnterFrame
    //     0x80aa3c: stp             fp, lr, [SP, #-0x10]!
    //     0x80aa40: mov             fp, SP
    // 0x80aa44: AllocStack(0x28)
    //     0x80aa44: sub             SP, SP, #0x28
    // 0x80aa48: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x80aa48: stur            x2, [fp, #-8]
    // 0x80aa4c: CheckStackOverflow
    //     0x80aa4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80aa50: cmp             SP, x16
    //     0x80aa54: b.ls            #0x80abf4
    // 0x80aa58: LoadField: r0 = r1->field_13
    //     0x80aa58: ldur            w0, [x1, #0x13]
    // 0x80aa5c: DecompressPointer r0
    //     0x80aa5c: add             x0, x0, HEAP, lsl #32
    // 0x80aa60: cmp             w0, NULL
    // 0x80aa64: b.eq            #0x80abfc
    // 0x80aa68: r1 = LoadClassIdInstr(r0)
    //     0x80aa68: ldur            x1, [x0, #-1]
    //     0x80aa6c: ubfx            x1, x1, #0xc, #0x14
    // 0x80aa70: mov             x16, x0
    // 0x80aa74: mov             x0, x1
    // 0x80aa78: mov             x1, x16
    // 0x80aa7c: r0 = GDT[cid_x0 + 0x73d]()
    //     0x80aa7c: add             lr, x0, #0x73d
    //     0x80aa80: ldr             lr, [x21, lr, lsl #3]
    //     0x80aa84: blr             lr
    // 0x80aa88: r1 = LoadClassIdInstr(r0)
    //     0x80aa88: ldur            x1, [x0, #-1]
    //     0x80aa8c: ubfx            x1, x1, #0xc, #0x14
    // 0x80aa90: mov             x16, x0
    // 0x80aa94: mov             x0, x1
    // 0x80aa98: mov             x1, x16
    // 0x80aa9c: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x80aa9c: movz            x17, #0xd35d
    //     0x80aaa0: add             lr, x0, x17
    //     0x80aaa4: ldr             lr, [x21, lr, lsl #3]
    //     0x80aaa8: blr             lr
    // 0x80aaac: mov             x2, x0
    // 0x80aab0: stur            x2, [fp, #-0x10]
    // 0x80aab4: ldur            x3, [fp, #-8]
    // 0x80aab8: CheckStackOverflow
    //     0x80aab8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80aabc: cmp             SP, x16
    //     0x80aac0: b.ls            #0x80ac00
    // 0x80aac4: r0 = LoadClassIdInstr(r2)
    //     0x80aac4: ldur            x0, [x2, #-1]
    //     0x80aac8: ubfx            x0, x0, #0xc, #0x14
    // 0x80aacc: mov             x1, x2
    // 0x80aad0: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x80aad0: movz            x17, #0x292d
    //     0x80aad4: movk            x17, #0x1, lsl #16
    //     0x80aad8: add             lr, x0, x17
    //     0x80aadc: ldr             lr, [x21, lr, lsl #3]
    //     0x80aae0: blr             lr
    // 0x80aae4: tbnz            w0, #4, #0x80abe4
    // 0x80aae8: ldur            x3, [fp, #-8]
    // 0x80aaec: ldur            x2, [fp, #-0x10]
    // 0x80aaf0: r0 = LoadClassIdInstr(r2)
    //     0x80aaf0: ldur            x0, [x2, #-1]
    //     0x80aaf4: ubfx            x0, x0, #0xc, #0x14
    // 0x80aaf8: mov             x1, x2
    // 0x80aafc: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x80aafc: movz            x17, #0x384d
    //     0x80ab00: movk            x17, #0x1, lsl #16
    //     0x80ab04: add             lr, x0, x17
    //     0x80ab08: ldr             lr, [x21, lr, lsl #3]
    //     0x80ab0c: blr             lr
    // 0x80ab10: mov             x2, x0
    // 0x80ab14: stur            x2, [fp, #-0x20]
    // 0x80ab18: LoadField: r3 = r2->field_13
    //     0x80ab18: ldur            w3, [x2, #0x13]
    // 0x80ab1c: DecompressPointer r3
    //     0x80ab1c: add             x3, x3, HEAP, lsl #32
    // 0x80ab20: ldur            x4, [fp, #-8]
    // 0x80ab24: stur            x3, [fp, #-0x18]
    // 0x80ab28: r0 = LoadClassIdInstr(r4)
    //     0x80ab28: ldur            x0, [x4, #-1]
    //     0x80ab2c: ubfx            x0, x0, #0xc, #0x14
    // 0x80ab30: mov             x1, x4
    // 0x80ab34: r0 = GDT[cid_x0 + -0x1000]()
    //     0x80ab34: sub             lr, x0, #1, lsl #12
    //     0x80ab38: ldr             lr, [x21, lr, lsl #3]
    //     0x80ab3c: blr             lr
    // 0x80ab40: mov             x3, x0
    // 0x80ab44: ldur            x2, [fp, #-8]
    // 0x80ab48: stur            x3, [fp, #-0x28]
    // 0x80ab4c: r0 = LoadClassIdInstr(r2)
    //     0x80ab4c: ldur            x0, [x2, #-1]
    //     0x80ab50: ubfx            x0, x0, #0xc, #0x14
    // 0x80ab54: mov             x1, x2
    // 0x80ab58: r0 = GDT[cid_x0 + 0x130b7]()
    //     0x80ab58: movz            x17, #0x30b7
    //     0x80ab5c: movk            x17, #0x1, lsl #16
    //     0x80ab60: add             lr, x0, x17
    //     0x80ab64: ldr             lr, [x21, lr, lsl #3]
    //     0x80ab68: blr             lr
    // 0x80ab6c: mov             x3, x0
    // 0x80ab70: ldur            x2, [fp, #-0x28]
    // 0x80ab74: r0 = BoxInt64Instr(r2)
    //     0x80ab74: sbfiz           x0, x2, #1, #0x1f
    //     0x80ab78: cmp             x2, x0, asr #1
    //     0x80ab7c: b.eq            #0x80ab88
    //     0x80ab80: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x80ab84: stur            x2, [x0, #7]
    // 0x80ab88: ldur            x1, [fp, #-0x18]
    // 0x80ab8c: mov             x2, x0
    // 0x80ab90: r0 = []=()
    //     0x80ab90: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x80ab94: ldur            x3, [fp, #-0x20]
    // 0x80ab98: r0 = LoadClassIdInstr(r3)
    //     0x80ab98: ldur            x0, [x3, #-1]
    //     0x80ab9c: ubfx            x0, x0, #0xc, #0x14
    // 0x80aba0: mov             x1, x3
    // 0x80aba4: ldur            x2, [fp, #-8]
    // 0x80aba8: r0 = GDT[cid_x0 + 0xf276]()
    //     0x80aba8: movz            x17, #0xf276
    //     0x80abac: add             lr, x0, x17
    //     0x80abb0: ldr             lr, [x21, lr, lsl #3]
    //     0x80abb4: blr             lr
    // 0x80abb8: tbnz            w0, #4, #0x80abdc
    // 0x80abbc: ldur            x1, [fp, #-0x20]
    // 0x80abc0: r0 = LoadClassIdInstr(r1)
    //     0x80abc0: ldur            x0, [x1, #-1]
    //     0x80abc4: ubfx            x0, x0, #0xc, #0x14
    // 0x80abc8: ldur            x2, [fp, #-8]
    // 0x80abcc: r0 = GDT[cid_x0 + 0xfddc]()
    //     0x80abcc: movz            x17, #0xfddc
    //     0x80abd0: add             lr, x0, x17
    //     0x80abd4: ldr             lr, [x21, lr, lsl #3]
    //     0x80abd8: blr             lr
    // 0x80abdc: ldur            x2, [fp, #-0x10]
    // 0x80abe0: b               #0x80aab4
    // 0x80abe4: r0 = Null
    //     0x80abe4: mov             x0, NULL
    // 0x80abe8: LeaveFrame
    //     0x80abe8: mov             SP, fp
    //     0x80abec: ldp             fp, lr, [SP], #0x10
    // 0x80abf0: ret
    //     0x80abf0: ret             
    // 0x80abf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80abf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80abf8: b               #0x80aa58
    // 0x80abfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80abfc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x80ac00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80ac00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80ac04: b               #0x80aac4
  }
  [closure] void _updateSemanticsForRenderObject(dynamic, RenderSemanticsGestureHandler) {
    // ** addr: 0x85d898, size: 0x3c
    // 0x85d898: EnterFrame
    //     0x85d898: stp             fp, lr, [SP, #-0x10]!
    //     0x85d89c: mov             fp, SP
    // 0x85d8a0: ldr             x0, [fp, #0x18]
    // 0x85d8a4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x85d8a4: ldur            w1, [x0, #0x17]
    // 0x85d8a8: DecompressPointer r1
    //     0x85d8a8: add             x1, x1, HEAP, lsl #32
    // 0x85d8ac: CheckStackOverflow
    //     0x85d8ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85d8b0: cmp             SP, x16
    //     0x85d8b4: b.ls            #0x85d8cc
    // 0x85d8b8: ldr             x2, [fp, #0x10]
    // 0x85d8bc: r0 = _updateSemanticsForRenderObject()
    //     0x85d8bc: bl              #0x85d8d4  ; [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_updateSemanticsForRenderObject
    // 0x85d8c0: LeaveFrame
    //     0x85d8c0: mov             SP, fp
    //     0x85d8c4: ldp             fp, lr, [SP], #0x10
    // 0x85d8c8: ret
    //     0x85d8c8: ret             
    // 0x85d8cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85d8cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85d8d0: b               #0x85d8b8
  }
  _ _updateSemanticsForRenderObject(/* No info */) {
    // ** addr: 0x85d8d4, size: 0x48
    // 0x85d8d4: EnterFrame
    //     0x85d8d4: stp             fp, lr, [SP, #-0x10]!
    //     0x85d8d8: mov             fp, SP
    // 0x85d8dc: CheckStackOverflow
    //     0x85d8dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85d8e0: cmp             SP, x16
    //     0x85d8e4: b.ls            #0x85d910
    // 0x85d8e8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x85d8e8: ldur            w0, [x1, #0x17]
    // 0x85d8ec: DecompressPointer r0
    //     0x85d8ec: add             x0, x0, HEAP, lsl #32
    // 0x85d8f0: cmp             w0, NULL
    // 0x85d8f4: b.eq            #0x85d918
    // 0x85d8f8: mov             x1, x0
    // 0x85d8fc: r0 = assignSemantics()
    //     0x85d8fc: bl              #0x85d91c  ; [package:flutter/src/widgets/gesture_detector.dart] _DefaultSemanticsGestureDelegate::assignSemantics
    // 0x85d900: r0 = Null
    //     0x85d900: mov             x0, NULL
    // 0x85d904: LeaveFrame
    //     0x85d904: mov             SP, fp
    //     0x85d908: ldp             fp, lr, [SP], #0x10
    // 0x85d90c: ret
    //     0x85d90c: ret             
    // 0x85d910: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85d910: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85d914: b               #0x85d8e8
    // 0x85d918: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x85d918: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x941830, size: 0x84
    // 0x941830: EnterFrame
    //     0x941830: stp             fp, lr, [SP, #-0x10]!
    //     0x941834: mov             fp, SP
    // 0x941838: AllocStack(0x10)
    //     0x941838: sub             SP, SP, #0x10
    // 0x94183c: SetupParameters(RawGestureDetectorState this /* r1 => r1, fp-0x10 */)
    //     0x94183c: stur            x1, [fp, #-0x10]
    // 0x941840: CheckStackOverflow
    //     0x941840: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941844: cmp             SP, x16
    //     0x941848: b.ls            #0x9418a8
    // 0x94184c: LoadField: r0 = r1->field_b
    //     0x94184c: ldur            w0, [x1, #0xb]
    // 0x941850: DecompressPointer r0
    //     0x941850: add             x0, x0, HEAP, lsl #32
    // 0x941854: stur            x0, [fp, #-8]
    // 0x941858: cmp             w0, NULL
    // 0x94185c: b.eq            #0x9418b0
    // 0x941860: r0 = _DefaultSemanticsGestureDelegate()
    //     0x941860: bl              #0x941ca0  ; Allocate_DefaultSemanticsGestureDelegateStub -> _DefaultSemanticsGestureDelegate (size=0xc)
    // 0x941864: ldur            x1, [fp, #-0x10]
    // 0x941868: StoreField: r0->field_7 = r1
    //     0x941868: stur            w1, [x0, #7]
    // 0x94186c: ArrayStore: r1[0] = r0  ; List_4
    //     0x94186c: stur            w0, [x1, #0x17]
    //     0x941870: ldurb           w16, [x1, #-1]
    //     0x941874: ldurb           w17, [x0, #-1]
    //     0x941878: and             x16, x17, x16, lsr #2
    //     0x94187c: tst             x16, HEAP, lsr #32
    //     0x941880: b.eq            #0x941888
    //     0x941884: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x941888: ldur            x0, [fp, #-8]
    // 0x94188c: LoadField: r2 = r0->field_f
    //     0x94188c: ldur            w2, [x0, #0xf]
    // 0x941890: DecompressPointer r2
    //     0x941890: add             x2, x2, HEAP, lsl #32
    // 0x941894: r0 = _syncAll()
    //     0x941894: bl              #0x9418b4  ; [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_syncAll
    // 0x941898: r0 = Null
    //     0x941898: mov             x0, NULL
    // 0x94189c: LeaveFrame
    //     0x94189c: mov             SP, fp
    //     0x9418a0: ldp             fp, lr, [SP], #0x10
    // 0x9418a4: ret
    //     0x9418a4: ret             
    // 0x9418a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9418a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9418ac: b               #0x94184c
    // 0x9418b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9418b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _syncAll(/* No info */) {
    // ** addr: 0x9418b4, size: 0x3ec
    // 0x9418b4: EnterFrame
    //     0x9418b4: stp             fp, lr, [SP, #-0x10]!
    //     0x9418b8: mov             fp, SP
    // 0x9418bc: AllocStack(0x40)
    //     0x9418bc: sub             SP, SP, #0x40
    // 0x9418c0: SetupParameters(RawGestureDetectorState this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x9418c0: mov             x0, x1
    //     0x9418c4: stur            x1, [fp, #-0x10]
    //     0x9418c8: mov             x1, x2
    //     0x9418cc: stur            x2, [fp, #-0x18]
    // 0x9418d0: CheckStackOverflow
    //     0x9418d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9418d4: cmp             SP, x16
    //     0x9418d8: b.ls            #0x941c68
    // 0x9418dc: LoadField: r2 = r0->field_13
    //     0x9418dc: ldur            w2, [x0, #0x13]
    // 0x9418e0: DecompressPointer r2
    //     0x9418e0: add             x2, x2, HEAP, lsl #32
    // 0x9418e4: stur            x2, [fp, #-8]
    // 0x9418e8: cmp             w2, NULL
    // 0x9418ec: b.eq            #0x941c70
    // 0x9418f0: r16 = <Type, GestureRecognizer>
    //     0x9418f0: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3a318] TypeArguments: <Type, GestureRecognizer>
    //     0x9418f4: ldr             x16, [x16, #0x318]
    // 0x9418f8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x9418fc: stp             lr, x16, [SP]
    // 0x941900: r0 = Map._fromLiteral()
    //     0x941900: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x941904: ldur            x2, [fp, #-0x10]
    // 0x941908: StoreField: r2->field_13 = r0
    //     0x941908: stur            w0, [x2, #0x13]
    //     0x94190c: ldurb           w16, [x2, #-1]
    //     0x941910: ldurb           w17, [x0, #-1]
    //     0x941914: and             x16, x17, x16, lsr #2
    //     0x941918: tst             x16, HEAP, lsr #32
    //     0x94191c: b.eq            #0x941924
    //     0x941920: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x941924: ldur            x3, [fp, #-0x18]
    // 0x941928: r0 = LoadClassIdInstr(r3)
    //     0x941928: ldur            x0, [x3, #-1]
    //     0x94192c: ubfx            x0, x0, #0xc, #0x14
    // 0x941930: mov             x1, x3
    // 0x941934: r0 = GDT[cid_x0 + 0x656]()
    //     0x941934: add             lr, x0, #0x656
    //     0x941938: ldr             lr, [x21, lr, lsl #3]
    //     0x94193c: blr             lr
    // 0x941940: r1 = LoadClassIdInstr(r0)
    //     0x941940: ldur            x1, [x0, #-1]
    //     0x941944: ubfx            x1, x1, #0xc, #0x14
    // 0x941948: mov             x16, x0
    // 0x94194c: mov             x0, x1
    // 0x941950: mov             x1, x16
    // 0x941954: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x941954: movz            x17, #0xd35d
    //     0x941958: add             lr, x0, x17
    //     0x94195c: ldr             lr, [x21, lr, lsl #3]
    //     0x941960: blr             lr
    // 0x941964: mov             x2, x0
    // 0x941968: stur            x2, [fp, #-0x20]
    // 0x94196c: ldur            x3, [fp, #-0x10]
    // 0x941970: ldur            x4, [fp, #-0x18]
    // 0x941974: ldur            x5, [fp, #-8]
    // 0x941978: CheckStackOverflow
    //     0x941978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94197c: cmp             SP, x16
    //     0x941980: b.ls            #0x941c74
    // 0x941984: r0 = LoadClassIdInstr(r2)
    //     0x941984: ldur            x0, [x2, #-1]
    //     0x941988: ubfx            x0, x0, #0xc, #0x14
    // 0x94198c: mov             x1, x2
    // 0x941990: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x941990: movz            x17, #0x292d
    //     0x941994: movk            x17, #0x1, lsl #16
    //     0x941998: add             lr, x0, x17
    //     0x94199c: ldr             lr, [x21, lr, lsl #3]
    //     0x9419a0: blr             lr
    // 0x9419a4: tbnz            w0, #4, #0x941b24
    // 0x9419a8: ldur            x3, [fp, #-0x10]
    // 0x9419ac: ldur            x4, [fp, #-8]
    // 0x9419b0: ldur            x2, [fp, #-0x20]
    // 0x9419b4: r0 = LoadClassIdInstr(r2)
    //     0x9419b4: ldur            x0, [x2, #-1]
    //     0x9419b8: ubfx            x0, x0, #0xc, #0x14
    // 0x9419bc: mov             x1, x2
    // 0x9419c0: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x9419c0: movz            x17, #0x384d
    //     0x9419c4: movk            x17, #0x1, lsl #16
    //     0x9419c8: add             lr, x0, x17
    //     0x9419cc: ldr             lr, [x21, lr, lsl #3]
    //     0x9419d0: blr             lr
    // 0x9419d4: mov             x4, x0
    // 0x9419d8: ldur            x3, [fp, #-0x10]
    // 0x9419dc: stur            x4, [fp, #-0x30]
    // 0x9419e0: LoadField: r5 = r3->field_13
    //     0x9419e0: ldur            w5, [x3, #0x13]
    // 0x9419e4: DecompressPointer r5
    //     0x9419e4: add             x5, x5, HEAP, lsl #32
    // 0x9419e8: stur            x5, [fp, #-0x28]
    // 0x9419ec: cmp             w5, NULL
    // 0x9419f0: b.eq            #0x941c7c
    // 0x9419f4: ldur            x6, [fp, #-8]
    // 0x9419f8: r0 = LoadClassIdInstr(r6)
    //     0x9419f8: ldur            x0, [x6, #-1]
    //     0x9419fc: ubfx            x0, x0, #0xc, #0x14
    // 0x941a00: mov             x1, x6
    // 0x941a04: mov             x2, x4
    // 0x941a08: r0 = GDT[cid_x0 + -0x114]()
    //     0x941a08: sub             lr, x0, #0x114
    //     0x941a0c: ldr             lr, [x21, lr, lsl #3]
    //     0x941a10: blr             lr
    // 0x941a14: cmp             w0, NULL
    // 0x941a18: b.ne            #0x941a6c
    // 0x941a1c: ldur            x3, [fp, #-0x18]
    // 0x941a20: r0 = LoadClassIdInstr(r3)
    //     0x941a20: ldur            x0, [x3, #-1]
    //     0x941a24: ubfx            x0, x0, #0xc, #0x14
    // 0x941a28: mov             x1, x3
    // 0x941a2c: ldur            x2, [fp, #-0x30]
    // 0x941a30: r0 = GDT[cid_x0 + -0x114]()
    //     0x941a30: sub             lr, x0, #0x114
    //     0x941a34: ldr             lr, [x21, lr, lsl #3]
    //     0x941a38: blr             lr
    // 0x941a3c: cmp             w0, NULL
    // 0x941a40: b.eq            #0x941c80
    // 0x941a44: r1 = LoadClassIdInstr(r0)
    //     0x941a44: ldur            x1, [x0, #-1]
    //     0x941a48: ubfx            x1, x1, #0xc, #0x14
    // 0x941a4c: mov             x16, x0
    // 0x941a50: mov             x0, x1
    // 0x941a54: mov             x1, x16
    // 0x941a58: r0 = GDT[cid_x0 + -0xfee]()
    //     0x941a58: sub             lr, x0, #0xfee
    //     0x941a5c: ldr             lr, [x21, lr, lsl #3]
    //     0x941a60: blr             lr
    // 0x941a64: mov             x3, x0
    // 0x941a68: b               #0x941a70
    // 0x941a6c: mov             x3, x0
    // 0x941a70: ldur            x5, [fp, #-0x10]
    // 0x941a74: ldur            x4, [fp, #-0x18]
    // 0x941a78: ldur            x1, [fp, #-0x28]
    // 0x941a7c: r0 = LoadClassIdInstr(r1)
    //     0x941a7c: ldur            x0, [x1, #-1]
    //     0x941a80: ubfx            x0, x0, #0xc, #0x14
    // 0x941a84: ldur            x2, [fp, #-0x30]
    // 0x941a88: r0 = GDT[cid_x0 + -0x10d]()
    //     0x941a88: sub             lr, x0, #0x10d
    //     0x941a8c: ldr             lr, [x21, lr, lsl #3]
    //     0x941a90: blr             lr
    // 0x941a94: ldur            x3, [fp, #-0x18]
    // 0x941a98: r0 = LoadClassIdInstr(r3)
    //     0x941a98: ldur            x0, [x3, #-1]
    //     0x941a9c: ubfx            x0, x0, #0xc, #0x14
    // 0x941aa0: mov             x1, x3
    // 0x941aa4: ldur            x2, [fp, #-0x30]
    // 0x941aa8: r0 = GDT[cid_x0 + -0x114]()
    //     0x941aa8: sub             lr, x0, #0x114
    //     0x941aac: ldr             lr, [x21, lr, lsl #3]
    //     0x941ab0: blr             lr
    // 0x941ab4: mov             x3, x0
    // 0x941ab8: stur            x3, [fp, #-0x28]
    // 0x941abc: cmp             w3, NULL
    // 0x941ac0: b.eq            #0x941c84
    // 0x941ac4: ldur            x4, [fp, #-0x10]
    // 0x941ac8: LoadField: r1 = r4->field_13
    //     0x941ac8: ldur            w1, [x4, #0x13]
    // 0x941acc: DecompressPointer r1
    //     0x941acc: add             x1, x1, HEAP, lsl #32
    // 0x941ad0: cmp             w1, NULL
    // 0x941ad4: b.eq            #0x941c88
    // 0x941ad8: r0 = LoadClassIdInstr(r1)
    //     0x941ad8: ldur            x0, [x1, #-1]
    //     0x941adc: ubfx            x0, x0, #0xc, #0x14
    // 0x941ae0: ldur            x2, [fp, #-0x30]
    // 0x941ae4: r0 = GDT[cid_x0 + -0x114]()
    //     0x941ae4: sub             lr, x0, #0x114
    //     0x941ae8: ldr             lr, [x21, lr, lsl #3]
    //     0x941aec: blr             lr
    // 0x941af0: cmp             w0, NULL
    // 0x941af4: b.eq            #0x941c8c
    // 0x941af8: ldur            x1, [fp, #-0x28]
    // 0x941afc: r2 = LoadClassIdInstr(r1)
    //     0x941afc: ldur            x2, [x1, #-1]
    //     0x941b00: ubfx            x2, x2, #0xc, #0x14
    // 0x941b04: mov             x16, x0
    // 0x941b08: mov             x0, x2
    // 0x941b0c: mov             x2, x16
    // 0x941b10: r0 = GDT[cid_x0 + -0xfeb]()
    //     0x941b10: sub             lr, x0, #0xfeb
    //     0x941b14: ldr             lr, [x21, lr, lsl #3]
    //     0x941b18: blr             lr
    // 0x941b1c: ldur            x2, [fp, #-0x20]
    // 0x941b20: b               #0x94196c
    // 0x941b24: ldur            x2, [fp, #-8]
    // 0x941b28: r0 = LoadClassIdInstr(r2)
    //     0x941b28: ldur            x0, [x2, #-1]
    //     0x941b2c: ubfx            x0, x0, #0xc, #0x14
    // 0x941b30: mov             x1, x2
    // 0x941b34: r0 = GDT[cid_x0 + 0x656]()
    //     0x941b34: add             lr, x0, #0x656
    //     0x941b38: ldr             lr, [x21, lr, lsl #3]
    //     0x941b3c: blr             lr
    // 0x941b40: r1 = LoadClassIdInstr(r0)
    //     0x941b40: ldur            x1, [x0, #-1]
    //     0x941b44: ubfx            x1, x1, #0xc, #0x14
    // 0x941b48: mov             x16, x0
    // 0x941b4c: mov             x0, x1
    // 0x941b50: mov             x1, x16
    // 0x941b54: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x941b54: movz            x17, #0xd35d
    //     0x941b58: add             lr, x0, x17
    //     0x941b5c: ldr             lr, [x21, lr, lsl #3]
    //     0x941b60: blr             lr
    // 0x941b64: mov             x2, x0
    // 0x941b68: stur            x2, [fp, #-0x18]
    // 0x941b6c: ldur            x4, [fp, #-0x10]
    // 0x941b70: ldur            x3, [fp, #-8]
    // 0x941b74: CheckStackOverflow
    //     0x941b74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941b78: cmp             SP, x16
    //     0x941b7c: b.ls            #0x941c90
    // 0x941b80: r0 = LoadClassIdInstr(r2)
    //     0x941b80: ldur            x0, [x2, #-1]
    //     0x941b84: ubfx            x0, x0, #0xc, #0x14
    // 0x941b88: mov             x1, x2
    // 0x941b8c: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x941b8c: movz            x17, #0x292d
    //     0x941b90: movk            x17, #0x1, lsl #16
    //     0x941b94: add             lr, x0, x17
    //     0x941b98: ldr             lr, [x21, lr, lsl #3]
    //     0x941b9c: blr             lr
    // 0x941ba0: tbnz            w0, #4, #0x941c58
    // 0x941ba4: ldur            x3, [fp, #-0x10]
    // 0x941ba8: ldur            x2, [fp, #-0x18]
    // 0x941bac: r0 = LoadClassIdInstr(r2)
    //     0x941bac: ldur            x0, [x2, #-1]
    //     0x941bb0: ubfx            x0, x0, #0xc, #0x14
    // 0x941bb4: mov             x1, x2
    // 0x941bb8: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x941bb8: movz            x17, #0x384d
    //     0x941bbc: movk            x17, #0x1, lsl #16
    //     0x941bc0: add             lr, x0, x17
    //     0x941bc4: ldr             lr, [x21, lr, lsl #3]
    //     0x941bc8: blr             lr
    // 0x941bcc: mov             x4, x0
    // 0x941bd0: ldur            x3, [fp, #-0x10]
    // 0x941bd4: stur            x4, [fp, #-0x20]
    // 0x941bd8: LoadField: r1 = r3->field_13
    //     0x941bd8: ldur            w1, [x3, #0x13]
    // 0x941bdc: DecompressPointer r1
    //     0x941bdc: add             x1, x1, HEAP, lsl #32
    // 0x941be0: cmp             w1, NULL
    // 0x941be4: b.eq            #0x941c98
    // 0x941be8: r0 = LoadClassIdInstr(r1)
    //     0x941be8: ldur            x0, [x1, #-1]
    //     0x941bec: ubfx            x0, x0, #0xc, #0x14
    // 0x941bf0: mov             x2, x4
    // 0x941bf4: r0 = GDT[cid_x0 + 0x55f]()
    //     0x941bf4: add             lr, x0, #0x55f
    //     0x941bf8: ldr             lr, [x21, lr, lsl #3]
    //     0x941bfc: blr             lr
    // 0x941c00: tbz             w0, #4, #0x941c50
    // 0x941c04: ldur            x3, [fp, #-8]
    // 0x941c08: r0 = LoadClassIdInstr(r3)
    //     0x941c08: ldur            x0, [x3, #-1]
    //     0x941c0c: ubfx            x0, x0, #0xc, #0x14
    // 0x941c10: mov             x1, x3
    // 0x941c14: ldur            x2, [fp, #-0x20]
    // 0x941c18: r0 = GDT[cid_x0 + -0x114]()
    //     0x941c18: sub             lr, x0, #0x114
    //     0x941c1c: ldr             lr, [x21, lr, lsl #3]
    //     0x941c20: blr             lr
    // 0x941c24: cmp             w0, NULL
    // 0x941c28: b.eq            #0x941c9c
    // 0x941c2c: r1 = LoadClassIdInstr(r0)
    //     0x941c2c: ldur            x1, [x0, #-1]
    //     0x941c30: ubfx            x1, x1, #0xc, #0x14
    // 0x941c34: mov             x16, x0
    // 0x941c38: mov             x0, x1
    // 0x941c3c: mov             x1, x16
    // 0x941c40: r0 = GDT[cid_x0 + 0xf510]()
    //     0x941c40: movz            x17, #0xf510
    //     0x941c44: add             lr, x0, x17
    //     0x941c48: ldr             lr, [x21, lr, lsl #3]
    //     0x941c4c: blr             lr
    // 0x941c50: ldur            x2, [fp, #-0x18]
    // 0x941c54: b               #0x941b6c
    // 0x941c58: r0 = Null
    //     0x941c58: mov             x0, NULL
    // 0x941c5c: LeaveFrame
    //     0x941c5c: mov             SP, fp
    //     0x941c60: ldp             fp, lr, [SP], #0x10
    // 0x941c64: ret
    //     0x941c64: ret             
    // 0x941c68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941c68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941c6c: b               #0x9418dc
    // 0x941c70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941c70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941c74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941c74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941c78: b               #0x941984
    // 0x941c7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941c7c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941c80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941c80: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941c84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941c84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941c88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941c88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941c8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941c8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941c90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941c90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941c94: b               #0x941b80
    // 0x941c98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941c98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941c9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941c9c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x98fc50, size: 0xdc
    // 0x98fc50: EnterFrame
    //     0x98fc50: stp             fp, lr, [SP, #-0x10]!
    //     0x98fc54: mov             fp, SP
    // 0x98fc58: AllocStack(0x10)
    //     0x98fc58: sub             SP, SP, #0x10
    // 0x98fc5c: SetupParameters(RawGestureDetectorState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x98fc5c: mov             x4, x1
    //     0x98fc60: mov             x3, x2
    //     0x98fc64: stur            x1, [fp, #-8]
    //     0x98fc68: stur            x2, [fp, #-0x10]
    // 0x98fc6c: CheckStackOverflow
    //     0x98fc6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98fc70: cmp             SP, x16
    //     0x98fc74: b.ls            #0x98fd20
    // 0x98fc78: mov             x0, x3
    // 0x98fc7c: r2 = Null
    //     0x98fc7c: mov             x2, NULL
    // 0x98fc80: r1 = Null
    //     0x98fc80: mov             x1, NULL
    // 0x98fc84: r4 = 60
    //     0x98fc84: movz            x4, #0x3c
    // 0x98fc88: branchIfSmi(r0, 0x98fc94)
    //     0x98fc88: tbz             w0, #0, #0x98fc94
    // 0x98fc8c: r4 = LoadClassIdInstr(r0)
    //     0x98fc8c: ldur            x4, [x0, #-1]
    //     0x98fc90: ubfx            x4, x4, #0xc, #0x14
    // 0x98fc94: r17 = 4782
    //     0x98fc94: movz            x17, #0x12ae
    // 0x98fc98: cmp             x4, x17
    // 0x98fc9c: b.eq            #0x98fcb4
    // 0x98fca0: r8 = RawGestureDetector
    //     0x98fca0: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a2f0] Type: RawGestureDetector
    //     0x98fca4: ldr             x8, [x8, #0x2f0]
    // 0x98fca8: r3 = Null
    //     0x98fca8: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a2f8] Null
    //     0x98fcac: ldr             x3, [x3, #0x2f8]
    // 0x98fcb0: r0 = RawGestureDetector()
    //     0x98fcb0: bl              #0x7a72c4  ; IsType_RawGestureDetector_Stub
    // 0x98fcb4: ldur            x3, [fp, #-8]
    // 0x98fcb8: LoadField: r2 = r3->field_7
    //     0x98fcb8: ldur            w2, [x3, #7]
    // 0x98fcbc: DecompressPointer r2
    //     0x98fcbc: add             x2, x2, HEAP, lsl #32
    // 0x98fcc0: ldur            x0, [fp, #-0x10]
    // 0x98fcc4: r1 = Null
    //     0x98fcc4: mov             x1, NULL
    // 0x98fcc8: cmp             w2, NULL
    // 0x98fccc: b.eq            #0x98fcf0
    // 0x98fcd0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x98fcd0: ldur            w4, [x2, #0x17]
    // 0x98fcd4: DecompressPointer r4
    //     0x98fcd4: add             x4, x4, HEAP, lsl #32
    // 0x98fcd8: r8 = X0 bound StatefulWidget
    //     0x98fcd8: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x98fcdc: ldr             x8, [x8, #0x7f8]
    // 0x98fce0: LoadField: r9 = r4->field_7
    //     0x98fce0: ldur            x9, [x4, #7]
    // 0x98fce4: r3 = Null
    //     0x98fce4: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a308] Null
    //     0x98fce8: ldr             x3, [x3, #0x308]
    // 0x98fcec: blr             x9
    // 0x98fcf0: ldur            x1, [fp, #-8]
    // 0x98fcf4: LoadField: r0 = r1->field_b
    //     0x98fcf4: ldur            w0, [x1, #0xb]
    // 0x98fcf8: DecompressPointer r0
    //     0x98fcf8: add             x0, x0, HEAP, lsl #32
    // 0x98fcfc: cmp             w0, NULL
    // 0x98fd00: b.eq            #0x98fd28
    // 0x98fd04: LoadField: r2 = r0->field_f
    //     0x98fd04: ldur            w2, [x0, #0xf]
    // 0x98fd08: DecompressPointer r2
    //     0x98fd08: add             x2, x2, HEAP, lsl #32
    // 0x98fd0c: r0 = _syncAll()
    //     0x98fd0c: bl              #0x9418b4  ; [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_syncAll
    // 0x98fd10: r0 = Null
    //     0x98fd10: mov             x0, NULL
    // 0x98fd14: LeaveFrame
    //     0x98fd14: mov             SP, fp
    //     0x98fd18: ldp             fp, lr, [SP], #0x10
    // 0x98fd1c: ret
    //     0x98fd1c: ret             
    // 0x98fd20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98fd20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98fd24: b               #0x98fc78
    // 0x98fd28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98fd28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ replaceGestureRecognizers(/* No info */) {
    // ** addr: 0x99a4f0, size: 0xcc
    // 0x99a4f0: EnterFrame
    //     0x99a4f0: stp             fp, lr, [SP, #-0x10]!
    //     0x99a4f4: mov             fp, SP
    // 0x99a4f8: AllocStack(0x10)
    //     0x99a4f8: sub             SP, SP, #0x10
    // 0x99a4fc: SetupParameters(RawGestureDetectorState this /* r1 => r0, fp-0x8 */)
    //     0x99a4fc: mov             x0, x1
    //     0x99a500: stur            x1, [fp, #-8]
    // 0x99a504: CheckStackOverflow
    //     0x99a504: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99a508: cmp             SP, x16
    //     0x99a50c: b.ls            #0x99a5a8
    // 0x99a510: mov             x1, x0
    // 0x99a514: r0 = _syncAll()
    //     0x99a514: bl              #0x9418b4  ; [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_syncAll
    // 0x99a518: ldur            x0, [fp, #-8]
    // 0x99a51c: LoadField: r1 = r0->field_b
    //     0x99a51c: ldur            w1, [x0, #0xb]
    // 0x99a520: DecompressPointer r1
    //     0x99a520: add             x1, x1, HEAP, lsl #32
    // 0x99a524: cmp             w1, NULL
    // 0x99a528: b.eq            #0x99a5b0
    // 0x99a52c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x99a52c: ldur            w2, [x1, #0x17]
    // 0x99a530: DecompressPointer r2
    //     0x99a530: add             x2, x2, HEAP, lsl #32
    // 0x99a534: tbz             w2, #4, #0x99a598
    // 0x99a538: LoadField: r1 = r0->field_f
    //     0x99a538: ldur            w1, [x0, #0xf]
    // 0x99a53c: DecompressPointer r1
    //     0x99a53c: add             x1, x1, HEAP, lsl #32
    // 0x99a540: cmp             w1, NULL
    // 0x99a544: b.eq            #0x99a5b4
    // 0x99a548: r0 = renderObject()
    //     0x99a548: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0x99a54c: mov             x3, x0
    // 0x99a550: stur            x3, [fp, #-0x10]
    // 0x99a554: cmp             w3, NULL
    // 0x99a558: b.eq            #0x99a5b8
    // 0x99a55c: mov             x0, x3
    // 0x99a560: r2 = Null
    //     0x99a560: mov             x2, NULL
    // 0x99a564: r1 = Null
    //     0x99a564: mov             x1, NULL
    // 0x99a568: r4 = LoadClassIdInstr(r0)
    //     0x99a568: ldur            x4, [x0, #-1]
    //     0x99a56c: ubfx            x4, x4, #0xc, #0x14
    // 0x99a570: cmp             x4, #0xc48
    // 0x99a574: b.eq            #0x99a58c
    // 0x99a578: r8 = RenderSemanticsGestureHandler
    //     0x99a578: add             x8, PP, #0x46, lsl #12  ; [pp+0x46060] Type: RenderSemanticsGestureHandler
    //     0x99a57c: ldr             x8, [x8, #0x60]
    // 0x99a580: r3 = Null
    //     0x99a580: add             x3, PP, #0x46, lsl #12  ; [pp+0x46068] Null
    //     0x99a584: ldr             x3, [x3, #0x68]
    // 0x99a588: r0 = DefaultTypeTest()
    //     0x99a588: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x99a58c: ldur            x1, [fp, #-8]
    // 0x99a590: ldur            x2, [fp, #-0x10]
    // 0x99a594: r0 = _updateSemanticsForRenderObject()
    //     0x99a594: bl              #0x85d8d4  ; [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_updateSemanticsForRenderObject
    // 0x99a598: r0 = Null
    //     0x99a598: mov             x0, NULL
    // 0x99a59c: LeaveFrame
    //     0x99a59c: mov             SP, fp
    //     0x99a5a0: ldp             fp, lr, [SP], #0x10
    // 0x99a5a4: ret
    //     0x99a5a4: ret             
    // 0x99a5a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99a5a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99a5ac: b               #0x99a510
    // 0x99a5b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99a5b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99a5b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99a5b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99a5b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99a5b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa16a38, size: 0x15c
    // 0xa16a38: EnterFrame
    //     0xa16a38: stp             fp, lr, [SP, #-0x10]!
    //     0xa16a3c: mov             fp, SP
    // 0xa16a40: AllocStack(0x30)
    //     0xa16a40: sub             SP, SP, #0x30
    // 0xa16a44: SetupParameters(RawGestureDetectorState this /* r1 => r0, fp-0x28 */)
    //     0xa16a44: mov             x0, x1
    //     0xa16a48: stur            x1, [fp, #-0x28]
    // 0xa16a4c: CheckStackOverflow
    //     0xa16a4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa16a50: cmp             SP, x16
    //     0xa16a54: b.ls            #0xa16b88
    // 0xa16a58: LoadField: r1 = r0->field_b
    //     0xa16a58: ldur            w1, [x0, #0xb]
    // 0xa16a5c: DecompressPointer r1
    //     0xa16a5c: add             x1, x1, HEAP, lsl #32
    // 0xa16a60: stur            x1, [fp, #-0x20]
    // 0xa16a64: cmp             w1, NULL
    // 0xa16a68: b.eq            #0xa16b90
    // 0xa16a6c: LoadField: r2 = r1->field_13
    //     0xa16a6c: ldur            w2, [x1, #0x13]
    // 0xa16a70: DecompressPointer r2
    //     0xa16a70: add             x2, x2, HEAP, lsl #32
    // 0xa16a74: stur            x2, [fp, #-0x18]
    // 0xa16a78: cmp             w2, NULL
    // 0xa16a7c: b.ne            #0xa16aa4
    // 0xa16a80: LoadField: r3 = r1->field_b
    //     0xa16a80: ldur            w3, [x1, #0xb]
    // 0xa16a84: DecompressPointer r3
    //     0xa16a84: add             x3, x3, HEAP, lsl #32
    // 0xa16a88: cmp             w3, NULL
    // 0xa16a8c: b.ne            #0xa16a9c
    // 0xa16a90: r3 = Instance_HitTestBehavior
    //     0xa16a90: add             x3, PP, #0x25, lsl #12  ; [pp+0x251d0] Obj!HitTestBehavior@e358e1
    //     0xa16a94: ldr             x3, [x3, #0x1d0]
    // 0xa16a98: b               #0xa16aa8
    // 0xa16a9c: r3 = Instance_HitTestBehavior
    //     0xa16a9c: ldr             x3, [PP, #0x6c40]  ; [pp+0x6c40] Obj!HitTestBehavior@e358a1
    // 0xa16aa0: b               #0xa16aa8
    // 0xa16aa4: mov             x3, x2
    // 0xa16aa8: stur            x3, [fp, #-0x10]
    // 0xa16aac: LoadField: r4 = r1->field_b
    //     0xa16aac: ldur            w4, [x1, #0xb]
    // 0xa16ab0: DecompressPointer r4
    //     0xa16ab0: add             x4, x4, HEAP, lsl #32
    // 0xa16ab4: stur            x4, [fp, #-8]
    // 0xa16ab8: r0 = Listener()
    //     0xa16ab8: bl              #0x9daab0  ; AllocateListenerStub -> Listener (size=0x38)
    // 0xa16abc: ldur            x2, [fp, #-0x28]
    // 0xa16ac0: r1 = Function '_handlePointerDown@284132872':.
    //     0xa16ac0: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a190] AnonymousClosure: (0xa16be0), in [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_handlePointerDown (0xa16c1c)
    //     0xa16ac4: ldr             x1, [x1, #0x190]
    // 0xa16ac8: stur            x0, [fp, #-0x30]
    // 0xa16acc: r0 = AllocateClosure()
    //     0xa16acc: bl              #0xec1630  ; AllocateClosureStub
    // 0xa16ad0: mov             x1, x0
    // 0xa16ad4: ldur            x0, [fp, #-0x30]
    // 0xa16ad8: StoreField: r0->field_f = r1
    //     0xa16ad8: stur            w1, [x0, #0xf]
    // 0xa16adc: ldur            x2, [fp, #-0x28]
    // 0xa16ae0: r1 = Function '_handlePointerPanZoomStart@284132872':.
    //     0xa16ae0: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a198] AnonymousClosure: (0x80a9c4), in [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_handlePointerPanZoomStart (0x80aa3c)
    //     0xa16ae4: ldr             x1, [x1, #0x198]
    // 0xa16ae8: r0 = AllocateClosure()
    //     0xa16ae8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa16aec: mov             x1, x0
    // 0xa16af0: ldur            x0, [fp, #-0x30]
    // 0xa16af4: StoreField: r0->field_23 = r1
    //     0xa16af4: stur            w1, [x0, #0x23]
    // 0xa16af8: ldur            x1, [fp, #-0x10]
    // 0xa16afc: StoreField: r0->field_33 = r1
    //     0xa16afc: stur            w1, [x0, #0x33]
    // 0xa16b00: ldur            x1, [fp, #-8]
    // 0xa16b04: StoreField: r0->field_b = r1
    //     0xa16b04: stur            w1, [x0, #0xb]
    // 0xa16b08: ldur            x1, [fp, #-0x20]
    // 0xa16b0c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa16b0c: ldur            w2, [x1, #0x17]
    // 0xa16b10: DecompressPointer r2
    //     0xa16b10: add             x2, x2, HEAP, lsl #32
    // 0xa16b14: tbz             w2, #4, #0xa16b74
    // 0xa16b18: ldur            x1, [fp, #-0x18]
    // 0xa16b1c: cmp             w1, NULL
    // 0xa16b20: b.ne            #0xa16b30
    // 0xa16b24: ldur            x1, [fp, #-0x28]
    // 0xa16b28: r0 = _defaultBehavior()
    //     0xa16b28: bl              #0xa16ba0  ; [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_defaultBehavior
    // 0xa16b2c: mov             x1, x0
    // 0xa16b30: ldur            x0, [fp, #-0x30]
    // 0xa16b34: stur            x1, [fp, #-8]
    // 0xa16b38: r0 = _GestureSemantics()
    //     0xa16b38: bl              #0xa16b94  ; Allocate_GestureSemanticsStub -> _GestureSemantics (size=0x18)
    // 0xa16b3c: mov             x3, x0
    // 0xa16b40: ldur            x0, [fp, #-8]
    // 0xa16b44: stur            x3, [fp, #-0x10]
    // 0xa16b48: StoreField: r3->field_f = r0
    //     0xa16b48: stur            w0, [x3, #0xf]
    // 0xa16b4c: ldur            x2, [fp, #-0x28]
    // 0xa16b50: r1 = Function '_updateSemanticsForRenderObject@284132872':.
    //     0xa16b50: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a1a0] AnonymousClosure: (0x85d898), in [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_updateSemanticsForRenderObject (0x85d8d4)
    //     0xa16b54: ldr             x1, [x1, #0x1a0]
    // 0xa16b58: r0 = AllocateClosure()
    //     0xa16b58: bl              #0xec1630  ; AllocateClosureStub
    // 0xa16b5c: ldur            x1, [fp, #-0x10]
    // 0xa16b60: StoreField: r1->field_13 = r0
    //     0xa16b60: stur            w0, [x1, #0x13]
    // 0xa16b64: ldur            x2, [fp, #-0x30]
    // 0xa16b68: StoreField: r1->field_b = r2
    //     0xa16b68: stur            w2, [x1, #0xb]
    // 0xa16b6c: mov             x0, x1
    // 0xa16b70: b               #0xa16b7c
    // 0xa16b74: mov             x2, x0
    // 0xa16b78: mov             x0, x2
    // 0xa16b7c: LeaveFrame
    //     0xa16b7c: mov             SP, fp
    //     0xa16b80: ldp             fp, lr, [SP], #0x10
    // 0xa16b84: ret
    //     0xa16b84: ret             
    // 0xa16b88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa16b88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa16b8c: b               #0xa16a58
    // 0xa16b90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa16b90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _defaultBehavior(/* No info */) {
    // ** addr: 0xa16ba0, size: 0x40
    // 0xa16ba0: LoadField: r2 = r1->field_b
    //     0xa16ba0: ldur            w2, [x1, #0xb]
    // 0xa16ba4: DecompressPointer r2
    //     0xa16ba4: add             x2, x2, HEAP, lsl #32
    // 0xa16ba8: cmp             w2, NULL
    // 0xa16bac: b.eq            #0xa16bd4
    // 0xa16bb0: LoadField: r1 = r2->field_b
    //     0xa16bb0: ldur            w1, [x2, #0xb]
    // 0xa16bb4: DecompressPointer r1
    //     0xa16bb4: add             x1, x1, HEAP, lsl #32
    // 0xa16bb8: cmp             w1, NULL
    // 0xa16bbc: b.ne            #0xa16bcc
    // 0xa16bc0: r0 = Instance_HitTestBehavior
    //     0xa16bc0: add             x0, PP, #0x25, lsl #12  ; [pp+0x251d0] Obj!HitTestBehavior@e358e1
    //     0xa16bc4: ldr             x0, [x0, #0x1d0]
    // 0xa16bc8: b               #0xa16bd0
    // 0xa16bcc: r0 = Instance_HitTestBehavior
    //     0xa16bcc: ldr             x0, [PP, #0x6c40]  ; [pp+0x6c40] Obj!HitTestBehavior@e358a1
    // 0xa16bd0: ret
    //     0xa16bd0: ret             
    // 0xa16bd4: EnterFrame
    //     0xa16bd4: stp             fp, lr, [SP, #-0x10]!
    //     0xa16bd8: mov             fp, SP
    // 0xa16bdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa16bdc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _handlePointerDown(dynamic, PointerDownEvent) {
    // ** addr: 0xa16be0, size: 0x3c
    // 0xa16be0: EnterFrame
    //     0xa16be0: stp             fp, lr, [SP, #-0x10]!
    //     0xa16be4: mov             fp, SP
    // 0xa16be8: ldr             x0, [fp, #0x18]
    // 0xa16bec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa16bec: ldur            w1, [x0, #0x17]
    // 0xa16bf0: DecompressPointer r1
    //     0xa16bf0: add             x1, x1, HEAP, lsl #32
    // 0xa16bf4: CheckStackOverflow
    //     0xa16bf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa16bf8: cmp             SP, x16
    //     0xa16bfc: b.ls            #0xa16c14
    // 0xa16c00: ldr             x2, [fp, #0x10]
    // 0xa16c04: r0 = _handlePointerDown()
    //     0xa16c04: bl              #0xa16c1c  ; [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_handlePointerDown
    // 0xa16c08: LeaveFrame
    //     0xa16c08: mov             SP, fp
    //     0xa16c0c: ldp             fp, lr, [SP], #0x10
    // 0xa16c10: ret
    //     0xa16c10: ret             
    // 0xa16c14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa16c14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa16c18: b               #0xa16c00
  }
  _ _handlePointerDown(/* No info */) {
    // ** addr: 0xa16c1c, size: 0x1f0
    // 0xa16c1c: EnterFrame
    //     0xa16c1c: stp             fp, lr, [SP, #-0x10]!
    //     0xa16c20: mov             fp, SP
    // 0xa16c24: AllocStack(0x28)
    //     0xa16c24: sub             SP, SP, #0x28
    // 0xa16c28: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xa16c28: stur            x2, [fp, #-8]
    // 0xa16c2c: CheckStackOverflow
    //     0xa16c2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa16c30: cmp             SP, x16
    //     0xa16c34: b.ls            #0xa16df8
    // 0xa16c38: LoadField: r0 = r1->field_13
    //     0xa16c38: ldur            w0, [x1, #0x13]
    // 0xa16c3c: DecompressPointer r0
    //     0xa16c3c: add             x0, x0, HEAP, lsl #32
    // 0xa16c40: cmp             w0, NULL
    // 0xa16c44: b.eq            #0xa16e00
    // 0xa16c48: r1 = LoadClassIdInstr(r0)
    //     0xa16c48: ldur            x1, [x0, #-1]
    //     0xa16c4c: ubfx            x1, x1, #0xc, #0x14
    // 0xa16c50: mov             x16, x0
    // 0xa16c54: mov             x0, x1
    // 0xa16c58: mov             x1, x16
    // 0xa16c5c: r0 = GDT[cid_x0 + 0x73d]()
    //     0xa16c5c: add             lr, x0, #0x73d
    //     0xa16c60: ldr             lr, [x21, lr, lsl #3]
    //     0xa16c64: blr             lr
    // 0xa16c68: r1 = LoadClassIdInstr(r0)
    //     0xa16c68: ldur            x1, [x0, #-1]
    //     0xa16c6c: ubfx            x1, x1, #0xc, #0x14
    // 0xa16c70: mov             x16, x0
    // 0xa16c74: mov             x0, x1
    // 0xa16c78: mov             x1, x16
    // 0xa16c7c: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xa16c7c: movz            x17, #0xd35d
    //     0xa16c80: add             lr, x0, x17
    //     0xa16c84: ldr             lr, [x21, lr, lsl #3]
    //     0xa16c88: blr             lr
    // 0xa16c8c: mov             x2, x0
    // 0xa16c90: stur            x2, [fp, #-0x10]
    // 0xa16c94: ldur            x3, [fp, #-8]
    // 0xa16c98: CheckStackOverflow
    //     0xa16c98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa16c9c: cmp             SP, x16
    //     0xa16ca0: b.ls            #0xa16e04
    // 0xa16ca4: r0 = LoadClassIdInstr(r2)
    //     0xa16ca4: ldur            x0, [x2, #-1]
    //     0xa16ca8: ubfx            x0, x0, #0xc, #0x14
    // 0xa16cac: mov             x1, x2
    // 0xa16cb0: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xa16cb0: movz            x17, #0x292d
    //     0xa16cb4: movk            x17, #0x1, lsl #16
    //     0xa16cb8: add             lr, x0, x17
    //     0xa16cbc: ldr             lr, [x21, lr, lsl #3]
    //     0xa16cc0: blr             lr
    // 0xa16cc4: tbnz            w0, #4, #0xa16de8
    // 0xa16cc8: ldur            x3, [fp, #-8]
    // 0xa16ccc: ldur            x2, [fp, #-0x10]
    // 0xa16cd0: r0 = LoadClassIdInstr(r2)
    //     0xa16cd0: ldur            x0, [x2, #-1]
    //     0xa16cd4: ubfx            x0, x0, #0xc, #0x14
    // 0xa16cd8: mov             x1, x2
    // 0xa16cdc: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xa16cdc: movz            x17, #0x384d
    //     0xa16ce0: movk            x17, #0x1, lsl #16
    //     0xa16ce4: add             lr, x0, x17
    //     0xa16ce8: ldr             lr, [x21, lr, lsl #3]
    //     0xa16cec: blr             lr
    // 0xa16cf0: mov             x2, x0
    // 0xa16cf4: stur            x2, [fp, #-0x20]
    // 0xa16cf8: LoadField: r3 = r2->field_13
    //     0xa16cf8: ldur            w3, [x2, #0x13]
    // 0xa16cfc: DecompressPointer r3
    //     0xa16cfc: add             x3, x3, HEAP, lsl #32
    // 0xa16d00: ldur            x4, [fp, #-8]
    // 0xa16d04: stur            x3, [fp, #-0x18]
    // 0xa16d08: r0 = LoadClassIdInstr(r4)
    //     0xa16d08: ldur            x0, [x4, #-1]
    //     0xa16d0c: ubfx            x0, x0, #0xc, #0x14
    // 0xa16d10: mov             x1, x4
    // 0xa16d14: r0 = GDT[cid_x0 + -0x1000]()
    //     0xa16d14: sub             lr, x0, #1, lsl #12
    //     0xa16d18: ldr             lr, [x21, lr, lsl #3]
    //     0xa16d1c: blr             lr
    // 0xa16d20: mov             x3, x0
    // 0xa16d24: ldur            x2, [fp, #-8]
    // 0xa16d28: stur            x3, [fp, #-0x28]
    // 0xa16d2c: r0 = LoadClassIdInstr(r2)
    //     0xa16d2c: ldur            x0, [x2, #-1]
    //     0xa16d30: ubfx            x0, x0, #0xc, #0x14
    // 0xa16d34: mov             x1, x2
    // 0xa16d38: r0 = GDT[cid_x0 + 0x130b7]()
    //     0xa16d38: movz            x17, #0x30b7
    //     0xa16d3c: movk            x17, #0x1, lsl #16
    //     0xa16d40: add             lr, x0, x17
    //     0xa16d44: ldr             lr, [x21, lr, lsl #3]
    //     0xa16d48: blr             lr
    // 0xa16d4c: mov             x3, x0
    // 0xa16d50: ldur            x2, [fp, #-0x28]
    // 0xa16d54: r0 = BoxInt64Instr(r2)
    //     0xa16d54: sbfiz           x0, x2, #1, #0x1f
    //     0xa16d58: cmp             x2, x0, asr #1
    //     0xa16d5c: b.eq            #0xa16d68
    //     0xa16d60: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa16d64: stur            x2, [x0, #7]
    // 0xa16d68: ldur            x1, [fp, #-0x18]
    // 0xa16d6c: mov             x2, x0
    // 0xa16d70: r0 = []=()
    //     0xa16d70: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa16d74: ldur            x3, [fp, #-0x20]
    // 0xa16d78: r0 = LoadClassIdInstr(r3)
    //     0xa16d78: ldur            x0, [x3, #-1]
    //     0xa16d7c: ubfx            x0, x0, #0xc, #0x14
    // 0xa16d80: mov             x1, x3
    // 0xa16d84: ldur            x2, [fp, #-8]
    // 0xa16d88: r0 = GDT[cid_x0 + 0xe954]()
    //     0xa16d88: movz            x17, #0xe954
    //     0xa16d8c: add             lr, x0, x17
    //     0xa16d90: ldr             lr, [x21, lr, lsl #3]
    //     0xa16d94: blr             lr
    // 0xa16d98: tbnz            w0, #4, #0xa16dc0
    // 0xa16d9c: ldur            x1, [fp, #-0x20]
    // 0xa16da0: r0 = LoadClassIdInstr(r1)
    //     0xa16da0: ldur            x0, [x1, #-1]
    //     0xa16da4: ubfx            x0, x0, #0xc, #0x14
    // 0xa16da8: ldur            x2, [fp, #-8]
    // 0xa16dac: r0 = GDT[cid_x0 + 0xf0b7]()
    //     0xa16dac: movz            x17, #0xf0b7
    //     0xa16db0: add             lr, x0, x17
    //     0xa16db4: ldr             lr, [x21, lr, lsl #3]
    //     0xa16db8: blr             lr
    // 0xa16dbc: b               #0xa16de0
    // 0xa16dc0: ldur            x1, [fp, #-0x20]
    // 0xa16dc4: r0 = LoadClassIdInstr(r1)
    //     0xa16dc4: ldur            x0, [x1, #-1]
    //     0xa16dc8: ubfx            x0, x0, #0xc, #0x14
    // 0xa16dcc: ldur            x2, [fp, #-8]
    // 0xa16dd0: r0 = GDT[cid_x0 + 0xfe8f]()
    //     0xa16dd0: movz            x17, #0xfe8f
    //     0xa16dd4: add             lr, x0, x17
    //     0xa16dd8: ldr             lr, [x21, lr, lsl #3]
    //     0xa16ddc: blr             lr
    // 0xa16de0: ldur            x2, [fp, #-0x10]
    // 0xa16de4: b               #0xa16c94
    // 0xa16de8: r0 = Null
    //     0xa16de8: mov             x0, NULL
    // 0xa16dec: LeaveFrame
    //     0xa16dec: mov             SP, fp
    //     0xa16df0: ldp             fp, lr, [SP], #0x10
    // 0xa16df4: ret
    //     0xa16df4: ret             
    // 0xa16df8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa16df8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa16dfc: b               #0xa16c38
    // 0xa16e00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa16e00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa16e04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa16e04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa16e08: b               #0xa16ca4
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7fe18, size: 0x11c
    // 0xa7fe18: EnterFrame
    //     0xa7fe18: stp             fp, lr, [SP, #-0x10]!
    //     0xa7fe1c: mov             fp, SP
    // 0xa7fe20: AllocStack(0x10)
    //     0xa7fe20: sub             SP, SP, #0x10
    // 0xa7fe24: SetupParameters(RawGestureDetectorState this /* r1 => r2, fp-0x8 */)
    //     0xa7fe24: mov             x2, x1
    //     0xa7fe28: stur            x1, [fp, #-8]
    // 0xa7fe2c: CheckStackOverflow
    //     0xa7fe2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7fe30: cmp             SP, x16
    //     0xa7fe34: b.ls            #0xa7ff20
    // 0xa7fe38: LoadField: r1 = r2->field_13
    //     0xa7fe38: ldur            w1, [x2, #0x13]
    // 0xa7fe3c: DecompressPointer r1
    //     0xa7fe3c: add             x1, x1, HEAP, lsl #32
    // 0xa7fe40: cmp             w1, NULL
    // 0xa7fe44: b.eq            #0xa7ff28
    // 0xa7fe48: r0 = LoadClassIdInstr(r1)
    //     0xa7fe48: ldur            x0, [x1, #-1]
    //     0xa7fe4c: ubfx            x0, x0, #0xc, #0x14
    // 0xa7fe50: r0 = GDT[cid_x0 + 0x73d]()
    //     0xa7fe50: add             lr, x0, #0x73d
    //     0xa7fe54: ldr             lr, [x21, lr, lsl #3]
    //     0xa7fe58: blr             lr
    // 0xa7fe5c: r1 = LoadClassIdInstr(r0)
    //     0xa7fe5c: ldur            x1, [x0, #-1]
    //     0xa7fe60: ubfx            x1, x1, #0xc, #0x14
    // 0xa7fe64: mov             x16, x0
    // 0xa7fe68: mov             x0, x1
    // 0xa7fe6c: mov             x1, x16
    // 0xa7fe70: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xa7fe70: movz            x17, #0xd35d
    //     0xa7fe74: add             lr, x0, x17
    //     0xa7fe78: ldr             lr, [x21, lr, lsl #3]
    //     0xa7fe7c: blr             lr
    // 0xa7fe80: mov             x2, x0
    // 0xa7fe84: stur            x2, [fp, #-0x10]
    // 0xa7fe88: CheckStackOverflow
    //     0xa7fe88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7fe8c: cmp             SP, x16
    //     0xa7fe90: b.ls            #0xa7ff2c
    // 0xa7fe94: r0 = LoadClassIdInstr(r2)
    //     0xa7fe94: ldur            x0, [x2, #-1]
    //     0xa7fe98: ubfx            x0, x0, #0xc, #0x14
    // 0xa7fe9c: mov             x1, x2
    // 0xa7fea0: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xa7fea0: movz            x17, #0x292d
    //     0xa7fea4: movk            x17, #0x1, lsl #16
    //     0xa7fea8: add             lr, x0, x17
    //     0xa7feac: ldr             lr, [x21, lr, lsl #3]
    //     0xa7feb0: blr             lr
    // 0xa7feb4: tbnz            w0, #4, #0xa7ff08
    // 0xa7feb8: ldur            x2, [fp, #-0x10]
    // 0xa7febc: r0 = LoadClassIdInstr(r2)
    //     0xa7febc: ldur            x0, [x2, #-1]
    //     0xa7fec0: ubfx            x0, x0, #0xc, #0x14
    // 0xa7fec4: mov             x1, x2
    // 0xa7fec8: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xa7fec8: movz            x17, #0x384d
    //     0xa7fecc: movk            x17, #0x1, lsl #16
    //     0xa7fed0: add             lr, x0, x17
    //     0xa7fed4: ldr             lr, [x21, lr, lsl #3]
    //     0xa7fed8: blr             lr
    // 0xa7fedc: r1 = LoadClassIdInstr(r0)
    //     0xa7fedc: ldur            x1, [x0, #-1]
    //     0xa7fee0: ubfx            x1, x1, #0xc, #0x14
    // 0xa7fee4: mov             x16, x0
    // 0xa7fee8: mov             x0, x1
    // 0xa7feec: mov             x1, x16
    // 0xa7fef0: r0 = GDT[cid_x0 + 0xf510]()
    //     0xa7fef0: movz            x17, #0xf510
    //     0xa7fef4: add             lr, x0, x17
    //     0xa7fef8: ldr             lr, [x21, lr, lsl #3]
    //     0xa7fefc: blr             lr
    // 0xa7ff00: ldur            x2, [fp, #-0x10]
    // 0xa7ff04: b               #0xa7fe88
    // 0xa7ff08: ldur            x1, [fp, #-8]
    // 0xa7ff0c: StoreField: r1->field_13 = rNULL
    //     0xa7ff0c: stur            NULL, [x1, #0x13]
    // 0xa7ff10: r0 = Null
    //     0xa7ff10: mov             x0, NULL
    // 0xa7ff14: LeaveFrame
    //     0xa7ff14: mov             SP, fp
    //     0xa7ff18: ldp             fp, lr, [SP], #0x10
    // 0xa7ff1c: ret
    //     0xa7ff1c: ret             
    // 0xa7ff20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7ff20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7ff24: b               #0xa7fe38
    // 0xa7ff28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7ff28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa7ff2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7ff2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7ff30: b               #0xa7fe94
  }
}

// class id: 4504, size: 0x18, field offset: 0x10
//   const constructor, 
class _GestureSemantics extends SingleChildRenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x85d810, size: 0x88
    // 0x85d810: EnterFrame
    //     0x85d810: stp             fp, lr, [SP, #-0x10]!
    //     0x85d814: mov             fp, SP
    // 0x85d818: AllocStack(0x10)
    //     0x85d818: sub             SP, SP, #0x10
    // 0x85d81c: SetupParameters(_GestureSemantics this /* r1 => r1, fp-0x8 */)
    //     0x85d81c: stur            x1, [fp, #-8]
    // 0x85d820: CheckStackOverflow
    //     0x85d820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85d824: cmp             SP, x16
    //     0x85d828: b.ls            #0x85d890
    // 0x85d82c: r0 = RenderSemanticsGestureHandler()
    //     0x85d82c: bl              #0x85f314  ; AllocateRenderSemanticsGestureHandlerStub -> RenderSemanticsGestureHandler (size=0x7c)
    // 0x85d830: mov             x1, x0
    // 0x85d834: stur            x0, [fp, #-0x10]
    // 0x85d838: r0 = RenderSemanticsGestureHandler()
    //     0x85d838: bl              #0x85f2c4  ; [package:flutter/src/rendering/proxy_box.dart] RenderSemanticsGestureHandler::RenderSemanticsGestureHandler
    // 0x85d83c: ldur            x1, [fp, #-8]
    // 0x85d840: LoadField: r0 = r1->field_f
    //     0x85d840: ldur            w0, [x1, #0xf]
    // 0x85d844: DecompressPointer r0
    //     0x85d844: add             x0, x0, HEAP, lsl #32
    // 0x85d848: ldur            x3, [fp, #-0x10]
    // 0x85d84c: StoreField: r3->field_5b = r0
    //     0x85d84c: stur            w0, [x3, #0x5b]
    //     0x85d850: ldurb           w16, [x3, #-1]
    //     0x85d854: ldurb           w17, [x0, #-1]
    //     0x85d858: and             x16, x17, x16, lsr #2
    //     0x85d85c: tst             x16, HEAP, lsr #32
    //     0x85d860: b.eq            #0x85d868
    //     0x85d864: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x85d868: LoadField: r0 = r1->field_13
    //     0x85d868: ldur            w0, [x1, #0x13]
    // 0x85d86c: DecompressPointer r0
    //     0x85d86c: add             x0, x0, HEAP, lsl #32
    // 0x85d870: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x85d870: ldur            w1, [x0, #0x17]
    // 0x85d874: DecompressPointer r1
    //     0x85d874: add             x1, x1, HEAP, lsl #32
    // 0x85d878: mov             x2, x3
    // 0x85d87c: r0 = _updateSemanticsForRenderObject()
    //     0x85d87c: bl              #0x85d8d4  ; [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_updateSemanticsForRenderObject
    // 0x85d880: ldur            x0, [fp, #-0x10]
    // 0x85d884: LeaveFrame
    //     0x85d884: mov             SP, fp
    //     0x85d888: ldp             fp, lr, [SP], #0x10
    // 0x85d88c: ret
    //     0x85d88c: ret             
    // 0x85d890: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85d890: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85d894: b               #0x85d82c
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xc71478, size: 0xb4
    // 0xc71478: EnterFrame
    //     0xc71478: stp             fp, lr, [SP, #-0x10]!
    //     0xc7147c: mov             fp, SP
    // 0xc71480: AllocStack(0x10)
    //     0xc71480: sub             SP, SP, #0x10
    // 0xc71484: SetupParameters(_GestureSemantics this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xc71484: mov             x4, x1
    //     0xc71488: stur            x1, [fp, #-8]
    //     0xc7148c: stur            x3, [fp, #-0x10]
    // 0xc71490: CheckStackOverflow
    //     0xc71490: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc71494: cmp             SP, x16
    //     0xc71498: b.ls            #0xc71524
    // 0xc7149c: mov             x0, x3
    // 0xc714a0: r2 = Null
    //     0xc714a0: mov             x2, NULL
    // 0xc714a4: r1 = Null
    //     0xc714a4: mov             x1, NULL
    // 0xc714a8: r4 = 60
    //     0xc714a8: movz            x4, #0x3c
    // 0xc714ac: branchIfSmi(r0, 0xc714b8)
    //     0xc714ac: tbz             w0, #0, #0xc714b8
    // 0xc714b0: r4 = LoadClassIdInstr(r0)
    //     0xc714b0: ldur            x4, [x0, #-1]
    //     0xc714b4: ubfx            x4, x4, #0xc, #0x14
    // 0xc714b8: cmp             x4, #0xc48
    // 0xc714bc: b.eq            #0xc714d4
    // 0xc714c0: r8 = RenderSemanticsGestureHandler
    //     0xc714c0: add             x8, PP, #0x46, lsl #12  ; [pp+0x46060] Type: RenderSemanticsGestureHandler
    //     0xc714c4: ldr             x8, [x8, #0x60]
    // 0xc714c8: r3 = Null
    //     0xc714c8: add             x3, PP, #0x46, lsl #12  ; [pp+0x46220] Null
    //     0xc714cc: ldr             x3, [x3, #0x220]
    // 0xc714d0: r0 = DefaultTypeTest()
    //     0xc714d0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc714d4: ldur            x1, [fp, #-8]
    // 0xc714d8: LoadField: r0 = r1->field_f
    //     0xc714d8: ldur            w0, [x1, #0xf]
    // 0xc714dc: DecompressPointer r0
    //     0xc714dc: add             x0, x0, HEAP, lsl #32
    // 0xc714e0: ldur            x2, [fp, #-0x10]
    // 0xc714e4: StoreField: r2->field_5b = r0
    //     0xc714e4: stur            w0, [x2, #0x5b]
    //     0xc714e8: ldurb           w16, [x2, #-1]
    //     0xc714ec: ldurb           w17, [x0, #-1]
    //     0xc714f0: and             x16, x17, x16, lsr #2
    //     0xc714f4: tst             x16, HEAP, lsr #32
    //     0xc714f8: b.eq            #0xc71500
    //     0xc714fc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc71500: LoadField: r0 = r1->field_13
    //     0xc71500: ldur            w0, [x1, #0x13]
    // 0xc71504: DecompressPointer r0
    //     0xc71504: add             x0, x0, HEAP, lsl #32
    // 0xc71508: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc71508: ldur            w1, [x0, #0x17]
    // 0xc7150c: DecompressPointer r1
    //     0xc7150c: add             x1, x1, HEAP, lsl #32
    // 0xc71510: r0 = _updateSemanticsForRenderObject()
    //     0xc71510: bl              #0x85d8d4  ; [package:flutter/src/widgets/gesture_detector.dart] RawGestureDetectorState::_updateSemanticsForRenderObject
    // 0xc71514: r0 = Null
    //     0xc71514: mov             x0, NULL
    // 0xc71518: LeaveFrame
    //     0xc71518: mov             SP, fp
    //     0xc7151c: ldp             fp, lr, [SP], #0x10
    // 0xc71520: ret
    //     0xc71520: ret             
    // 0xc71524: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc71524: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc71528: b               #0xc7149c
  }
}

// class id: 4782, size: 0x20, field offset: 0xc
//   const constructor, 
class RawGestureDetector extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa9207c, size: 0x30
    // 0xa9207c: EnterFrame
    //     0xa9207c: stp             fp, lr, [SP, #-0x10]!
    //     0xa92080: mov             fp, SP
    // 0xa92084: mov             x0, x1
    // 0xa92088: r1 = <RawGestureDetector>
    //     0xa92088: add             x1, PP, #0x30, lsl #12  ; [pp+0x30f60] TypeArguments: <RawGestureDetector>
    //     0xa9208c: ldr             x1, [x1, #0xf60]
    // 0xa92090: r0 = RawGestureDetectorState()
    //     0xa92090: bl              #0xa920ac  ; AllocateRawGestureDetectorStateStub -> RawGestureDetectorState (size=0x1c)
    // 0xa92094: r1 = _ConstMap len:0
    //     0xa92094: add             x1, PP, #0x30, lsl #12  ; [pp+0x30f68] Map<Type, GestureRecognizer>(0)
    //     0xa92098: ldr             x1, [x1, #0xf68]
    // 0xa9209c: StoreField: r0->field_13 = r1
    //     0xa9209c: stur            w1, [x0, #0x13]
    // 0xa920a0: LeaveFrame
    //     0xa920a0: mov             SP, fp
    //     0xa920a4: ldp             fp, lr, [SP], #0x10
    // 0xa920a8: ret
    //     0xa920a8: ret             
  }
}

// class id: 5351, size: 0x10c, field offset: 0xc
class GestureDetector extends StatelessWidget {

  _ GestureDetector(/* No info */) {
    // ** addr: 0x7e5134, size: 0x740
    // 0x7e5134: EnterFrame
    //     0x7e5134: stp             fp, lr, [SP, #-0x10]!
    //     0x7e5138: mov             fp, SP
    // 0x7e513c: AllocStack(0x8)
    //     0x7e513c: sub             SP, SP, #8
    // 0x7e5140: SetupParameters({dynamic behavior = Null /* r5 */, dynamic child = Null /* r0 */, dynamic excludeFromSemantics = false /* r6 */, dynamic key = Null /* fp-0x8 */, dynamic onHorizontalDragEnd = Null /* r8 */, dynamic onHorizontalDragStart = Null /* r9 */, dynamic onHorizontalDragUpdate = Null /* r10 */, dynamic onLongPress = Null /* r11 */, dynamic onTap = Null /* r12 */, dynamic onTapCancel = Null /* r13 */, dynamic onTapDown = Null /* r14 */, dynamic onTapUp = Null /* r19 */, dynamic onVerticalDragEnd = Null /* r20 */, dynamic onVerticalDragStart = Null /* r7 */, dynamic onVerticalDragUpdate = Null /* r23 */})
    //     0x7e5140: ldur            w2, [x4, #0x13]
    //     0x7e5144: ldur            w3, [x4, #0x1f]
    //     0x7e5148: add             x3, x3, HEAP, lsl #32
    //     0x7e514c: add             x16, PP, #0x23, lsl #12  ; [pp+0x238b8] "behavior"
    //     0x7e5150: ldr             x16, [x16, #0x8b8]
    //     0x7e5154: cmp             w3, w16
    //     0x7e5158: b.ne            #0x7e517c
    //     0x7e515c: ldur            w3, [x4, #0x23]
    //     0x7e5160: add             x3, x3, HEAP, lsl #32
    //     0x7e5164: sub             w5, w2, w3
    //     0x7e5168: add             x3, fp, w5, sxtw #2
    //     0x7e516c: ldr             x3, [x3, #8]
    //     0x7e5170: mov             x5, x3
    //     0x7e5174: movz            x3, #0x1
    //     0x7e5178: b               #0x7e5184
    //     0x7e517c: mov             x5, NULL
    //     0x7e5180: movz            x3, #0
    //     0x7e5184: lsl             x6, x3, #1
    //     0x7e5188: lsl             w7, w6, #1
    //     0x7e518c: add             w8, w7, #8
    //     0x7e5190: add             x16, x4, w8, sxtw #1
    //     0x7e5194: ldur            w9, [x16, #0xf]
    //     0x7e5198: add             x9, x9, HEAP, lsl #32
    //     0x7e519c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22230] "child"
    //     0x7e51a0: ldr             x16, [x16, #0x230]
    //     0x7e51a4: cmp             w9, w16
    //     0x7e51a8: b.ne            #0x7e51dc
    //     0x7e51ac: add             w8, w7, #0xa
    //     0x7e51b0: add             x16, x4, w8, sxtw #1
    //     0x7e51b4: ldur            w7, [x16, #0xf]
    //     0x7e51b8: add             x7, x7, HEAP, lsl #32
    //     0x7e51bc: sub             w8, w2, w7
    //     0x7e51c0: add             x7, fp, w8, sxtw #2
    //     0x7e51c4: ldr             x7, [x7, #8]
    //     0x7e51c8: add             w8, w6, #2
    //     0x7e51cc: sbfx            x6, x8, #1, #0x1f
    //     0x7e51d0: mov             x0, x7
    //     0x7e51d4: mov             x3, x6
    //     0x7e51d8: b               #0x7e51e0
    //     0x7e51dc: mov             x0, NULL
    //     0x7e51e0: lsl             x6, x3, #1
    //     0x7e51e4: lsl             w7, w6, #1
    //     0x7e51e8: add             w8, w7, #8
    //     0x7e51ec: add             x16, x4, w8, sxtw #1
    //     0x7e51f0: ldur            w9, [x16, #0xf]
    //     0x7e51f4: add             x9, x9, HEAP, lsl #32
    //     0x7e51f8: add             x16, PP, #0x25, lsl #12  ; [pp+0x25838] "excludeFromSemantics"
    //     0x7e51fc: ldr             x16, [x16, #0x838]
    //     0x7e5200: cmp             w9, w16
    //     0x7e5204: b.ne            #0x7e5238
    //     0x7e5208: add             w8, w7, #0xa
    //     0x7e520c: add             x16, x4, w8, sxtw #1
    //     0x7e5210: ldur            w7, [x16, #0xf]
    //     0x7e5214: add             x7, x7, HEAP, lsl #32
    //     0x7e5218: sub             w8, w2, w7
    //     0x7e521c: add             x7, fp, w8, sxtw #2
    //     0x7e5220: ldr             x7, [x7, #8]
    //     0x7e5224: add             w8, w6, #2
    //     0x7e5228: sbfx            x6, x8, #1, #0x1f
    //     0x7e522c: mov             x3, x6
    //     0x7e5230: mov             x6, x7
    //     0x7e5234: b               #0x7e523c
    //     0x7e5238: add             x6, NULL, #0x30  ; false
    //     0x7e523c: lsl             x7, x3, #1
    //     0x7e5240: lsl             w8, w7, #1
    //     0x7e5244: add             w9, w8, #8
    //     0x7e5248: add             x16, x4, w9, sxtw #1
    //     0x7e524c: ldur            w10, [x16, #0xf]
    //     0x7e5250: add             x10, x10, HEAP, lsl #32
    //     0x7e5254: ldr             x16, [PP, #0xab8]  ; [pp+0xab8] "key"
    //     0x7e5258: cmp             w10, w16
    //     0x7e525c: b.ne            #0x7e5290
    //     0x7e5260: add             w9, w8, #0xa
    //     0x7e5264: add             x16, x4, w9, sxtw #1
    //     0x7e5268: ldur            w8, [x16, #0xf]
    //     0x7e526c: add             x8, x8, HEAP, lsl #32
    //     0x7e5270: sub             w9, w2, w8
    //     0x7e5274: add             x8, fp, w9, sxtw #2
    //     0x7e5278: ldr             x8, [x8, #8]
    //     0x7e527c: add             w9, w7, #2
    //     0x7e5280: sbfx            x7, x9, #1, #0x1f
    //     0x7e5284: mov             x3, x7
    //     0x7e5288: mov             x7, x8
    //     0x7e528c: b               #0x7e5294
    //     0x7e5290: mov             x7, NULL
    //     0x7e5294: stur            x7, [fp, #-8]
    //     0x7e5298: lsl             x8, x3, #1
    //     0x7e529c: lsl             w9, w8, #1
    //     0x7e52a0: add             w10, w9, #8
    //     0x7e52a4: add             x16, x4, w10, sxtw #1
    //     0x7e52a8: ldur            w11, [x16, #0xf]
    //     0x7e52ac: add             x11, x11, HEAP, lsl #32
    //     0x7e52b0: add             x16, PP, #0x25, lsl #12  ; [pp+0x25840] "onHorizontalDragEnd"
    //     0x7e52b4: ldr             x16, [x16, #0x840]
    //     0x7e52b8: cmp             w11, w16
    //     0x7e52bc: b.ne            #0x7e52f0
    //     0x7e52c0: add             w10, w9, #0xa
    //     0x7e52c4: add             x16, x4, w10, sxtw #1
    //     0x7e52c8: ldur            w9, [x16, #0xf]
    //     0x7e52cc: add             x9, x9, HEAP, lsl #32
    //     0x7e52d0: sub             w10, w2, w9
    //     0x7e52d4: add             x9, fp, w10, sxtw #2
    //     0x7e52d8: ldr             x9, [x9, #8]
    //     0x7e52dc: add             w10, w8, #2
    //     0x7e52e0: sbfx            x8, x10, #1, #0x1f
    //     0x7e52e4: mov             x3, x8
    //     0x7e52e8: mov             x8, x9
    //     0x7e52ec: b               #0x7e52f4
    //     0x7e52f0: mov             x8, NULL
    //     0x7e52f4: lsl             x9, x3, #1
    //     0x7e52f8: lsl             w10, w9, #1
    //     0x7e52fc: add             w11, w10, #8
    //     0x7e5300: add             x16, x4, w11, sxtw #1
    //     0x7e5304: ldur            w12, [x16, #0xf]
    //     0x7e5308: add             x12, x12, HEAP, lsl #32
    //     0x7e530c: add             x16, PP, #0x25, lsl #12  ; [pp+0x25848] "onHorizontalDragStart"
    //     0x7e5310: ldr             x16, [x16, #0x848]
    //     0x7e5314: cmp             w12, w16
    //     0x7e5318: b.ne            #0x7e534c
    //     0x7e531c: add             w11, w10, #0xa
    //     0x7e5320: add             x16, x4, w11, sxtw #1
    //     0x7e5324: ldur            w10, [x16, #0xf]
    //     0x7e5328: add             x10, x10, HEAP, lsl #32
    //     0x7e532c: sub             w11, w2, w10
    //     0x7e5330: add             x10, fp, w11, sxtw #2
    //     0x7e5334: ldr             x10, [x10, #8]
    //     0x7e5338: add             w11, w9, #2
    //     0x7e533c: sbfx            x9, x11, #1, #0x1f
    //     0x7e5340: mov             x3, x9
    //     0x7e5344: mov             x9, x10
    //     0x7e5348: b               #0x7e5350
    //     0x7e534c: mov             x9, NULL
    //     0x7e5350: lsl             x10, x3, #1
    //     0x7e5354: lsl             w11, w10, #1
    //     0x7e5358: add             w12, w11, #8
    //     0x7e535c: add             x16, x4, w12, sxtw #1
    //     0x7e5360: ldur            w13, [x16, #0xf]
    //     0x7e5364: add             x13, x13, HEAP, lsl #32
    //     0x7e5368: add             x16, PP, #0x25, lsl #12  ; [pp+0x25850] "onHorizontalDragUpdate"
    //     0x7e536c: ldr             x16, [x16, #0x850]
    //     0x7e5370: cmp             w13, w16
    //     0x7e5374: b.ne            #0x7e53a8
    //     0x7e5378: add             w12, w11, #0xa
    //     0x7e537c: add             x16, x4, w12, sxtw #1
    //     0x7e5380: ldur            w11, [x16, #0xf]
    //     0x7e5384: add             x11, x11, HEAP, lsl #32
    //     0x7e5388: sub             w12, w2, w11
    //     0x7e538c: add             x11, fp, w12, sxtw #2
    //     0x7e5390: ldr             x11, [x11, #8]
    //     0x7e5394: add             w12, w10, #2
    //     0x7e5398: sbfx            x10, x12, #1, #0x1f
    //     0x7e539c: mov             x3, x10
    //     0x7e53a0: mov             x10, x11
    //     0x7e53a4: b               #0x7e53ac
    //     0x7e53a8: mov             x10, NULL
    //     0x7e53ac: lsl             x11, x3, #1
    //     0x7e53b0: lsl             w12, w11, #1
    //     0x7e53b4: add             w13, w12, #8
    //     0x7e53b8: add             x16, x4, w13, sxtw #1
    //     0x7e53bc: ldur            w14, [x16, #0xf]
    //     0x7e53c0: add             x14, x14, HEAP, lsl #32
    //     0x7e53c4: add             x16, PP, #0x25, lsl #12  ; [pp+0x25858] "onLongPress"
    //     0x7e53c8: ldr             x16, [x16, #0x858]
    //     0x7e53cc: cmp             w14, w16
    //     0x7e53d0: b.ne            #0x7e5404
    //     0x7e53d4: add             w13, w12, #0xa
    //     0x7e53d8: add             x16, x4, w13, sxtw #1
    //     0x7e53dc: ldur            w12, [x16, #0xf]
    //     0x7e53e0: add             x12, x12, HEAP, lsl #32
    //     0x7e53e4: sub             w13, w2, w12
    //     0x7e53e8: add             x12, fp, w13, sxtw #2
    //     0x7e53ec: ldr             x12, [x12, #8]
    //     0x7e53f0: add             w13, w11, #2
    //     0x7e53f4: sbfx            x11, x13, #1, #0x1f
    //     0x7e53f8: mov             x3, x11
    //     0x7e53fc: mov             x11, x12
    //     0x7e5400: b               #0x7e5408
    //     0x7e5404: mov             x11, NULL
    //     0x7e5408: lsl             x12, x3, #1
    //     0x7e540c: lsl             w13, w12, #1
    //     0x7e5410: add             w14, w13, #8
    //     0x7e5414: add             x16, x4, w14, sxtw #1
    //     0x7e5418: ldur            w19, [x16, #0xf]
    //     0x7e541c: add             x19, x19, HEAP, lsl #32
    //     0x7e5420: add             x16, PP, #0x22, lsl #12  ; [pp+0x222d8] "onTap"
    //     0x7e5424: ldr             x16, [x16, #0x2d8]
    //     0x7e5428: cmp             w19, w16
    //     0x7e542c: b.ne            #0x7e5460
    //     0x7e5430: add             w14, w13, #0xa
    //     0x7e5434: add             x16, x4, w14, sxtw #1
    //     0x7e5438: ldur            w13, [x16, #0xf]
    //     0x7e543c: add             x13, x13, HEAP, lsl #32
    //     0x7e5440: sub             w14, w2, w13
    //     0x7e5444: add             x13, fp, w14, sxtw #2
    //     0x7e5448: ldr             x13, [x13, #8]
    //     0x7e544c: add             w14, w12, #2
    //     0x7e5450: sbfx            x12, x14, #1, #0x1f
    //     0x7e5454: mov             x3, x12
    //     0x7e5458: mov             x12, x13
    //     0x7e545c: b               #0x7e5464
    //     0x7e5460: mov             x12, NULL
    //     0x7e5464: lsl             x13, x3, #1
    //     0x7e5468: lsl             w14, w13, #1
    //     0x7e546c: add             w19, w14, #8
    //     0x7e5470: add             x16, x4, w19, sxtw #1
    //     0x7e5474: ldur            w20, [x16, #0xf]
    //     0x7e5478: add             x20, x20, HEAP, lsl #32
    //     0x7e547c: add             x16, PP, #0x25, lsl #12  ; [pp+0x25860] "onTapCancel"
    //     0x7e5480: ldr             x16, [x16, #0x860]
    //     0x7e5484: cmp             w20, w16
    //     0x7e5488: b.ne            #0x7e54bc
    //     0x7e548c: add             w19, w14, #0xa
    //     0x7e5490: add             x16, x4, w19, sxtw #1
    //     0x7e5494: ldur            w14, [x16, #0xf]
    //     0x7e5498: add             x14, x14, HEAP, lsl #32
    //     0x7e549c: sub             w19, w2, w14
    //     0x7e54a0: add             x14, fp, w19, sxtw #2
    //     0x7e54a4: ldr             x14, [x14, #8]
    //     0x7e54a8: add             w19, w13, #2
    //     0x7e54ac: sbfx            x13, x19, #1, #0x1f
    //     0x7e54b0: mov             x3, x13
    //     0x7e54b4: mov             x13, x14
    //     0x7e54b8: b               #0x7e54c0
    //     0x7e54bc: mov             x13, NULL
    //     0x7e54c0: lsl             x14, x3, #1
    //     0x7e54c4: lsl             w19, w14, #1
    //     0x7e54c8: add             w20, w19, #8
    //     0x7e54cc: add             x16, x4, w20, sxtw #1
    //     0x7e54d0: ldur            w23, [x16, #0xf]
    //     0x7e54d4: add             x23, x23, HEAP, lsl #32
    //     0x7e54d8: add             x16, PP, #0x25, lsl #12  ; [pp+0x25868] "onTapDown"
    //     0x7e54dc: ldr             x16, [x16, #0x868]
    //     0x7e54e0: cmp             w23, w16
    //     0x7e54e4: b.ne            #0x7e5518
    //     0x7e54e8: add             w20, w19, #0xa
    //     0x7e54ec: add             x16, x4, w20, sxtw #1
    //     0x7e54f0: ldur            w19, [x16, #0xf]
    //     0x7e54f4: add             x19, x19, HEAP, lsl #32
    //     0x7e54f8: sub             w20, w2, w19
    //     0x7e54fc: add             x19, fp, w20, sxtw #2
    //     0x7e5500: ldr             x19, [x19, #8]
    //     0x7e5504: add             w20, w14, #2
    //     0x7e5508: sbfx            x14, x20, #1, #0x1f
    //     0x7e550c: mov             x3, x14
    //     0x7e5510: mov             x14, x19
    //     0x7e5514: b               #0x7e551c
    //     0x7e5518: mov             x14, NULL
    //     0x7e551c: lsl             x19, x3, #1
    //     0x7e5520: lsl             w20, w19, #1
    //     0x7e5524: add             w23, w20, #8
    //     0x7e5528: add             x16, x4, w23, sxtw #1
    //     0x7e552c: ldur            w24, [x16, #0xf]
    //     0x7e5530: add             x24, x24, HEAP, lsl #32
    //     0x7e5534: add             x16, PP, #0x25, lsl #12  ; [pp+0x25870] "onTapUp"
    //     0x7e5538: ldr             x16, [x16, #0x870]
    //     0x7e553c: cmp             w24, w16
    //     0x7e5540: b.ne            #0x7e5574
    //     0x7e5544: add             w23, w20, #0xa
    //     0x7e5548: add             x16, x4, w23, sxtw #1
    //     0x7e554c: ldur            w20, [x16, #0xf]
    //     0x7e5550: add             x20, x20, HEAP, lsl #32
    //     0x7e5554: sub             w23, w2, w20
    //     0x7e5558: add             x20, fp, w23, sxtw #2
    //     0x7e555c: ldr             x20, [x20, #8]
    //     0x7e5560: add             w23, w19, #2
    //     0x7e5564: sbfx            x19, x23, #1, #0x1f
    //     0x7e5568: mov             x3, x19
    //     0x7e556c: mov             x19, x20
    //     0x7e5570: b               #0x7e5578
    //     0x7e5574: mov             x19, NULL
    //     0x7e5578: lsl             x20, x3, #1
    //     0x7e557c: lsl             w23, w20, #1
    //     0x7e5580: add             w24, w23, #8
    //     0x7e5584: add             x16, x4, w24, sxtw #1
    //     0x7e5588: ldur            w25, [x16, #0xf]
    //     0x7e558c: add             x25, x25, HEAP, lsl #32
    //     0x7e5590: add             x16, PP, #0x25, lsl #12  ; [pp+0x25878] "onVerticalDragEnd"
    //     0x7e5594: ldr             x16, [x16, #0x878]
    //     0x7e5598: cmp             w25, w16
    //     0x7e559c: b.ne            #0x7e55d0
    //     0x7e55a0: add             w24, w23, #0xa
    //     0x7e55a4: add             x16, x4, w24, sxtw #1
    //     0x7e55a8: ldur            w23, [x16, #0xf]
    //     0x7e55ac: add             x23, x23, HEAP, lsl #32
    //     0x7e55b0: sub             w24, w2, w23
    //     0x7e55b4: add             x23, fp, w24, sxtw #2
    //     0x7e55b8: ldr             x23, [x23, #8]
    //     0x7e55bc: add             w24, w20, #2
    //     0x7e55c0: sbfx            x20, x24, #1, #0x1f
    //     0x7e55c4: mov             x3, x20
    //     0x7e55c8: mov             x20, x23
    //     0x7e55cc: b               #0x7e55d4
    //     0x7e55d0: mov             x20, NULL
    //     0x7e55d4: lsl             x23, x3, #1
    //     0x7e55d8: lsl             w24, w23, #1
    //     0x7e55dc: add             w25, w24, #8
    //     0x7e55e0: add             x16, x4, w25, sxtw #1
    //     0x7e55e4: ldur            w7, [x16, #0xf]
    //     0x7e55e8: add             x7, x7, HEAP, lsl #32
    //     0x7e55ec: add             x16, PP, #0x25, lsl #12  ; [pp+0x25880] "onVerticalDragStart"
    //     0x7e55f0: ldr             x16, [x16, #0x880]
    //     0x7e55f4: cmp             w7, w16
    //     0x7e55f8: b.ne            #0x7e562c
    //     0x7e55fc: add             w7, w24, #0xa
    //     0x7e5600: add             x16, x4, w7, sxtw #1
    //     0x7e5604: ldur            w24, [x16, #0xf]
    //     0x7e5608: add             x24, x24, HEAP, lsl #32
    //     0x7e560c: sub             w7, w2, w24
    //     0x7e5610: add             x24, fp, w7, sxtw #2
    //     0x7e5614: ldr             x24, [x24, #8]
    //     0x7e5618: add             w7, w23, #2
    //     0x7e561c: sbfx            x23, x7, #1, #0x1f
    //     0x7e5620: mov             x7, x24
    //     0x7e5624: mov             x3, x23
    //     0x7e5628: b               #0x7e5630
    //     0x7e562c: mov             x7, NULL
    //     0x7e5630: lsl             x23, x3, #1
    //     0x7e5634: lsl             w3, w23, #1
    //     0x7e5638: add             w23, w3, #8
    //     0x7e563c: add             x16, x4, w23, sxtw #1
    //     0x7e5640: ldur            w24, [x16, #0xf]
    //     0x7e5644: add             x24, x24, HEAP, lsl #32
    //     0x7e5648: add             x16, PP, #0x25, lsl #12  ; [pp+0x25888] "onVerticalDragUpdate"
    //     0x7e564c: ldr             x16, [x16, #0x888]
    //     0x7e5650: cmp             w24, w16
    //     0x7e5654: b.ne            #0x7e567c
    //     0x7e5658: add             w23, w3, #0xa
    //     0x7e565c: add             x16, x4, w23, sxtw #1
    //     0x7e5660: ldur            w3, [x16, #0xf]
    //     0x7e5664: add             x3, x3, HEAP, lsl #32
    //     0x7e5668: sub             w4, w2, w3
    //     0x7e566c: add             x2, fp, w4, sxtw #2
    //     0x7e5670: ldr             x2, [x2, #8]
    //     0x7e5674: mov             x23, x2
    //     0x7e5678: b               #0x7e5680
    //     0x7e567c: mov             x23, NULL
    // 0x7e5680: r4 = false
    //     0x7e5680: add             x4, NULL, #0x30  ; false
    // 0x7e5684: r3 = Instance_DragStartBehavior
    //     0x7e5684: ldr             x3, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0x7e5688: r2 = Instance_Offset
    //     0x7e5688: add             x2, PP, #0x25, lsl #12  ; [pp+0x25890] Obj!Offset@e2c521
    //     0x7e568c: ldr             x2, [x2, #0x890]
    // 0x7e5690: StoreField: r1->field_b = r0
    //     0x7e5690: stur            w0, [x1, #0xb]
    //     0x7e5694: ldurb           w16, [x1, #-1]
    //     0x7e5698: ldurb           w17, [x0, #-1]
    //     0x7e569c: and             x16, x17, x16, lsr #2
    //     0x7e56a0: tst             x16, HEAP, lsr #32
    //     0x7e56a4: b.eq            #0x7e56ac
    //     0x7e56a8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e56ac: mov             x0, x14
    // 0x7e56b0: StoreField: r1->field_f = r0
    //     0x7e56b0: stur            w0, [x1, #0xf]
    //     0x7e56b4: ldurb           w16, [x1, #-1]
    //     0x7e56b8: ldurb           w17, [x0, #-1]
    //     0x7e56bc: and             x16, x17, x16, lsr #2
    //     0x7e56c0: tst             x16, HEAP, lsr #32
    //     0x7e56c4: b.eq            #0x7e56cc
    //     0x7e56c8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e56cc: mov             x0, x19
    // 0x7e56d0: StoreField: r1->field_13 = r0
    //     0x7e56d0: stur            w0, [x1, #0x13]
    //     0x7e56d4: ldurb           w16, [x1, #-1]
    //     0x7e56d8: ldurb           w17, [x0, #-1]
    //     0x7e56dc: and             x16, x17, x16, lsr #2
    //     0x7e56e0: tst             x16, HEAP, lsr #32
    //     0x7e56e4: b.eq            #0x7e56ec
    //     0x7e56e8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e56ec: mov             x0, x12
    // 0x7e56f0: ArrayStore: r1[0] = r0  ; List_4
    //     0x7e56f0: stur            w0, [x1, #0x17]
    //     0x7e56f4: ldurb           w16, [x1, #-1]
    //     0x7e56f8: ldurb           w17, [x0, #-1]
    //     0x7e56fc: and             x16, x17, x16, lsr #2
    //     0x7e5700: tst             x16, HEAP, lsr #32
    //     0x7e5704: b.eq            #0x7e570c
    //     0x7e5708: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e570c: mov             x0, x13
    // 0x7e5710: StoreField: r1->field_1b = r0
    //     0x7e5710: stur            w0, [x1, #0x1b]
    //     0x7e5714: ldurb           w16, [x1, #-1]
    //     0x7e5718: ldurb           w17, [x0, #-1]
    //     0x7e571c: and             x16, x17, x16, lsr #2
    //     0x7e5720: tst             x16, HEAP, lsr #32
    //     0x7e5724: b.eq            #0x7e572c
    //     0x7e5728: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e572c: mov             x0, x11
    // 0x7e5730: StoreField: r1->field_4f = r0
    //     0x7e5730: stur            w0, [x1, #0x4f]
    //     0x7e5734: ldurb           w16, [x1, #-1]
    //     0x7e5738: ldurb           w17, [x0, #-1]
    //     0x7e573c: and             x16, x17, x16, lsr #2
    //     0x7e5740: tst             x16, HEAP, lsr #32
    //     0x7e5744: b.eq            #0x7e574c
    //     0x7e5748: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e574c: mov             x0, x7
    // 0x7e5750: StoreField: r1->field_9f = r0
    //     0x7e5750: stur            w0, [x1, #0x9f]
    //     0x7e5754: ldurb           w16, [x1, #-1]
    //     0x7e5758: ldurb           w17, [x0, #-1]
    //     0x7e575c: and             x16, x17, x16, lsr #2
    //     0x7e5760: tst             x16, HEAP, lsr #32
    //     0x7e5764: b.eq            #0x7e576c
    //     0x7e5768: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e576c: mov             x0, x23
    // 0x7e5770: StoreField: r1->field_a3 = r0
    //     0x7e5770: stur            w0, [x1, #0xa3]
    //     0x7e5774: ldurb           w16, [x1, #-1]
    //     0x7e5778: ldurb           w17, [x0, #-1]
    //     0x7e577c: and             x16, x17, x16, lsr #2
    //     0x7e5780: tst             x16, HEAP, lsr #32
    //     0x7e5784: b.eq            #0x7e578c
    //     0x7e5788: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e578c: mov             x0, x20
    // 0x7e5790: StoreField: r1->field_a7 = r0
    //     0x7e5790: stur            w0, [x1, #0xa7]
    //     0x7e5794: ldurb           w16, [x1, #-1]
    //     0x7e5798: ldurb           w17, [x0, #-1]
    //     0x7e579c: and             x16, x17, x16, lsr #2
    //     0x7e57a0: tst             x16, HEAP, lsr #32
    //     0x7e57a4: b.eq            #0x7e57ac
    //     0x7e57a8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e57ac: mov             x0, x9
    // 0x7e57b0: StoreField: r1->field_b3 = r0
    //     0x7e57b0: stur            w0, [x1, #0xb3]
    //     0x7e57b4: ldurb           w16, [x1, #-1]
    //     0x7e57b8: ldurb           w17, [x0, #-1]
    //     0x7e57bc: and             x16, x17, x16, lsr #2
    //     0x7e57c0: tst             x16, HEAP, lsr #32
    //     0x7e57c4: b.eq            #0x7e57cc
    //     0x7e57c8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e57cc: mov             x0, x10
    // 0x7e57d0: StoreField: r1->field_b7 = r0
    //     0x7e57d0: stur            w0, [x1, #0xb7]
    //     0x7e57d4: ldurb           w16, [x1, #-1]
    //     0x7e57d8: ldurb           w17, [x0, #-1]
    //     0x7e57dc: and             x16, x17, x16, lsr #2
    //     0x7e57e0: tst             x16, HEAP, lsr #32
    //     0x7e57e4: b.eq            #0x7e57ec
    //     0x7e57e8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e57ec: mov             x0, x8
    // 0x7e57f0: StoreField: r1->field_bb = r0
    //     0x7e57f0: stur            w0, [x1, #0xbb]
    //     0x7e57f4: ldurb           w16, [x1, #-1]
    //     0x7e57f8: ldurb           w17, [x0, #-1]
    //     0x7e57fc: and             x16, x17, x16, lsr #2
    //     0x7e5800: tst             x16, HEAP, lsr #32
    //     0x7e5804: b.eq            #0x7e580c
    //     0x7e5808: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e580c: mov             x0, x5
    // 0x7e5810: StoreField: r1->field_f3 = r0
    //     0x7e5810: stur            w0, [x1, #0xf3]
    //     0x7e5814: ldurb           w16, [x1, #-1]
    //     0x7e5818: ldurb           w17, [x0, #-1]
    //     0x7e581c: and             x16, x17, x16, lsr #2
    //     0x7e5820: tst             x16, HEAP, lsr #32
    //     0x7e5824: b.eq            #0x7e582c
    //     0x7e5828: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e582c: StoreField: r1->field_f7 = r6
    //     0x7e582c: stur            w6, [x1, #0xf7]
    // 0x7e5830: StoreField: r1->field_fb = r3
    //     0x7e5830: stur            w3, [x1, #0xfb]
    // 0x7e5834: r17 = 259
    //     0x7e5834: movz            x17, #0x103
    // 0x7e5838: str             w4, [x1, x17]
    // 0x7e583c: r17 = 263
    //     0x7e583c: movz            x17, #0x107
    // 0x7e5840: str             w2, [x1, x17]
    // 0x7e5844: ldur            x0, [fp, #-8]
    // 0x7e5848: StoreField: r1->field_7 = r0
    //     0x7e5848: stur            w0, [x1, #7]
    //     0x7e584c: ldurb           w16, [x1, #-1]
    //     0x7e5850: ldurb           w17, [x0, #-1]
    //     0x7e5854: and             x16, x17, x16, lsr #2
    //     0x7e5858: tst             x16, HEAP, lsr #32
    //     0x7e585c: b.eq            #0x7e5864
    //     0x7e5860: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e5864: r0 = Null
    //     0x7e5864: mov             x0, NULL
    // 0x7e5868: LeaveFrame
    //     0x7e5868: mov             SP, fp
    //     0x7e586c: ldp             fp, lr, [SP], #0x10
    // 0x7e5870: ret
    //     0x7e5870: ret             
  }
  [closure] TapGestureRecognizer <anonymous closure>(dynamic) {
    // ** addr: 0x94b254, size: 0x64
    // 0x94b254: EnterFrame
    //     0x94b254: stp             fp, lr, [SP, #-0x10]!
    //     0x94b258: mov             fp, SP
    // 0x94b25c: AllocStack(0x8)
    //     0x94b25c: sub             SP, SP, #8
    // 0x94b260: CheckStackOverflow
    //     0x94b260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94b264: cmp             SP, x16
    //     0x94b268: b.ls            #0x94b2b0
    // 0x94b26c: r0 = TapGestureRecognizer()
    //     0x94b26c: bl              #0x7632dc  ; AllocateTapGestureRecognizerStub -> TapGestureRecognizer (size=0x84)
    // 0x94b270: mov             x4, x0
    // 0x94b274: r0 = false
    //     0x94b274: add             x0, NULL, #0x30  ; false
    // 0x94b278: stur            x4, [fp, #-8]
    // 0x94b27c: StoreField: r4->field_47 = r0
    //     0x94b27c: stur            w0, [x4, #0x47]
    // 0x94b280: StoreField: r4->field_4b = r0
    //     0x94b280: stur            w0, [x4, #0x4b]
    // 0x94b284: mov             x1, x4
    // 0x94b288: r2 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static.
    //     0x94b288: add             x2, PP, #0x25, lsl #12  ; [pp+0x253d8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static. (0x7e54fb8bbd8c)
    //     0x94b28c: ldr             x2, [x2, #0x3d8]
    // 0x94b290: r3 = Instance_Duration
    //     0x94b290: ldr             x3, [PP, #0x6e28]  ; [pp+0x6e28] Obj!Duration@e3a0a1
    // 0x94b294: r5 = Null
    //     0x94b294: mov             x5, NULL
    // 0x94b298: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x94b298: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x94b29c: r0 = PrimaryPointerGestureRecognizer()
    //     0x94b29c: bl              #0x762f7c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::PrimaryPointerGestureRecognizer
    // 0x94b2a0: ldur            x0, [fp, #-8]
    // 0x94b2a4: LeaveFrame
    //     0x94b2a4: mov             SP, fp
    //     0x94b2a8: ldp             fp, lr, [SP], #0x10
    // 0x94b2ac: ret
    //     0x94b2ac: ret             
    // 0x94b2b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94b2b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94b2b4: b               #0x94b26c
  }
  [closure] VerticalDragGestureRecognizer <anonymous closure>(dynamic) {
    // ** addr: 0xa9a790, size: 0x44
    // 0xa9a790: EnterFrame
    //     0xa9a790: stp             fp, lr, [SP, #-0x10]!
    //     0xa9a794: mov             fp, SP
    // 0xa9a798: AllocStack(0x8)
    //     0xa9a798: sub             SP, SP, #8
    // 0xa9a79c: CheckStackOverflow
    //     0xa9a79c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9a7a0: cmp             SP, x16
    //     0xa9a7a4: b.ls            #0xa9a7cc
    // 0xa9a7a8: r0 = VerticalDragGestureRecognizer()
    //     0xa9a7a8: bl              #0x99b3d4  ; AllocateVerticalDragGestureRecognizerStub -> VerticalDragGestureRecognizer (size=0x90)
    // 0xa9a7ac: mov             x1, x0
    // 0xa9a7b0: r2 = Null
    //     0xa9a7b0: mov             x2, NULL
    // 0xa9a7b4: stur            x0, [fp, #-8]
    // 0xa9a7b8: r0 = DragGestureRecognizer()
    //     0xa9a7b8: bl              #0x85347c  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::DragGestureRecognizer
    // 0xa9a7bc: ldur            x0, [fp, #-8]
    // 0xa9a7c0: LeaveFrame
    //     0xa9a7c0: mov             SP, fp
    //     0xa9a7c4: ldp             fp, lr, [SP], #0x10
    // 0xa9a7c8: ret
    //     0xa9a7c8: ret             
    // 0xa9a7cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9a7cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9a7d0: b               #0xa9a7a8
  }
  _ build(/* No info */) {
    // ** addr: 0xaa27cc, size: 0x318
    // 0xaa27cc: EnterFrame
    //     0xaa27cc: stp             fp, lr, [SP, #-0x10]!
    //     0xaa27d0: mov             fp, SP
    // 0xaa27d4: AllocStack(0x38)
    //     0xaa27d4: sub             SP, SP, #0x38
    // 0xaa27d8: SetupParameters(GestureDetector this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xaa27d8: stur            x1, [fp, #-8]
    //     0xaa27dc: stur            x2, [fp, #-0x10]
    // 0xaa27e0: CheckStackOverflow
    //     0xaa27e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa27e4: cmp             SP, x16
    //     0xaa27e8: b.ls            #0xaa2adc
    // 0xaa27ec: r1 = 4
    //     0xaa27ec: movz            x1, #0x4
    // 0xaa27f0: r0 = AllocateContext()
    //     0xaa27f0: bl              #0xec126c  ; AllocateContextStub
    // 0xaa27f4: mov             x1, x0
    // 0xaa27f8: ldur            x0, [fp, #-8]
    // 0xaa27fc: stur            x1, [fp, #-0x18]
    // 0xaa2800: StoreField: r1->field_f = r0
    //     0xaa2800: stur            w0, [x1, #0xf]
    // 0xaa2804: ldur            x2, [fp, #-0x10]
    // 0xaa2808: StoreField: r1->field_13 = r2
    //     0xaa2808: stur            w2, [x1, #0x13]
    // 0xaa280c: r16 = <Type, GestureRecognizerFactory<GestureRecognizer>>
    //     0xaa280c: add             x16, PP, #0x25, lsl #12  ; [pp+0x25368] TypeArguments: <Type, GestureRecognizerFactory<GestureRecognizer>>
    //     0xaa2810: ldr             x16, [x16, #0x368]
    // 0xaa2814: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xaa2818: stp             lr, x16, [SP]
    // 0xaa281c: r0 = Map._fromLiteral()
    //     0xaa281c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xaa2820: ldur            x2, [fp, #-0x18]
    // 0xaa2824: stur            x0, [fp, #-0x10]
    // 0xaa2828: LoadField: r1 = r2->field_13
    //     0xaa2828: ldur            w1, [x2, #0x13]
    // 0xaa282c: DecompressPointer r1
    //     0xaa282c: add             x1, x1, HEAP, lsl #32
    // 0xaa2830: r0 = maybeGestureSettingsOf()
    //     0xaa2830: bl              #0x9a88ec  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::maybeGestureSettingsOf
    // 0xaa2834: ldur            x2, [fp, #-0x18]
    // 0xaa2838: ArrayStore: r2[0] = r0  ; List_4
    //     0xaa2838: stur            w0, [x2, #0x17]
    //     0xaa283c: ldurb           w16, [x2, #-1]
    //     0xaa2840: ldurb           w17, [x0, #-1]
    //     0xaa2844: and             x16, x17, x16, lsr #2
    //     0xaa2848: tst             x16, HEAP, lsr #32
    //     0xaa284c: b.eq            #0xaa2854
    //     0xaa2850: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xaa2854: LoadField: r1 = r2->field_13
    //     0xaa2854: ldur            w1, [x2, #0x13]
    // 0xaa2858: DecompressPointer r1
    //     0xaa2858: add             x1, x1, HEAP, lsl #32
    // 0xaa285c: r0 = of()
    //     0xaa285c: bl              #0x999ac0  ; [package:flutter/src/widgets/scroll_configuration.dart] ScrollConfiguration::of
    // 0xaa2860: ldur            x2, [fp, #-0x18]
    // 0xaa2864: StoreField: r2->field_1b = r0
    //     0xaa2864: stur            w0, [x2, #0x1b]
    //     0xaa2868: ldurb           w16, [x2, #-1]
    //     0xaa286c: ldurb           w17, [x0, #-1]
    //     0xaa2870: and             x16, x17, x16, lsr #2
    //     0xaa2874: tst             x16, HEAP, lsr #32
    //     0xaa2878: b.eq            #0xaa2880
    //     0xaa287c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xaa2880: ldur            x0, [fp, #-8]
    // 0xaa2884: LoadField: r1 = r0->field_f
    //     0xaa2884: ldur            w1, [x0, #0xf]
    // 0xaa2888: DecompressPointer r1
    //     0xaa2888: add             x1, x1, HEAP, lsl #32
    // 0xaa288c: cmp             w1, NULL
    // 0xaa2890: b.ne            #0xaa28c4
    // 0xaa2894: LoadField: r1 = r0->field_13
    //     0xaa2894: ldur            w1, [x0, #0x13]
    // 0xaa2898: DecompressPointer r1
    //     0xaa2898: add             x1, x1, HEAP, lsl #32
    // 0xaa289c: cmp             w1, NULL
    // 0xaa28a0: b.ne            #0xaa28c4
    // 0xaa28a4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa28a4: ldur            w1, [x0, #0x17]
    // 0xaa28a8: DecompressPointer r1
    //     0xaa28a8: add             x1, x1, HEAP, lsl #32
    // 0xaa28ac: cmp             w1, NULL
    // 0xaa28b0: b.ne            #0xaa28c4
    // 0xaa28b4: LoadField: r1 = r0->field_1b
    //     0xaa28b4: ldur            w1, [x0, #0x1b]
    // 0xaa28b8: DecompressPointer r1
    //     0xaa28b8: add             x1, x1, HEAP, lsl #32
    // 0xaa28bc: cmp             w1, NULL
    // 0xaa28c0: b.eq            #0xaa2914
    // 0xaa28c4: r1 = <TapGestureRecognizer>
    //     0xaa28c4: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a108] TypeArguments: <TapGestureRecognizer>
    //     0xaa28c8: ldr             x1, [x1, #0x108]
    // 0xaa28cc: r0 = GestureRecognizerFactoryWithHandlers()
    //     0xaa28cc: bl              #0x947d60  ; AllocateGestureRecognizerFactoryWithHandlersStub -> GestureRecognizerFactoryWithHandlers<X0 bound GestureRecognizer> (size=0x14)
    // 0xaa28d0: ldur            x2, [fp, #-0x18]
    // 0xaa28d4: r1 = Function '<anonymous closure>':.
    //     0xaa28d4: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a110] AnonymousClosure: (0x94b254), in [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::build (0xaa27cc)
    //     0xaa28d8: ldr             x1, [x1, #0x110]
    // 0xaa28dc: stur            x0, [fp, #-0x20]
    // 0xaa28e0: r0 = AllocateClosure()
    //     0xaa28e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa28e4: ldur            x3, [fp, #-0x20]
    // 0xaa28e8: StoreField: r3->field_b = r0
    //     0xaa28e8: stur            w0, [x3, #0xb]
    // 0xaa28ec: ldur            x2, [fp, #-0x18]
    // 0xaa28f0: r1 = Function '<anonymous closure>':.
    //     0xaa28f0: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a118] AnonymousClosure: (0xaa2ecc), in [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::build (0xaa27cc)
    //     0xaa28f4: ldr             x1, [x1, #0x118]
    // 0xaa28f8: r0 = AllocateClosure()
    //     0xaa28f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa28fc: ldur            x3, [fp, #-0x20]
    // 0xaa2900: StoreField: r3->field_f = r0
    //     0xaa2900: stur            w0, [x3, #0xf]
    // 0xaa2904: ldur            x1, [fp, #-0x10]
    // 0xaa2908: r2 = TapGestureRecognizer
    //     0xaa2908: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a120] Type: TapGestureRecognizer
    //     0xaa290c: ldr             x2, [x2, #0x120]
    // 0xaa2910: r0 = []=()
    //     0xaa2910: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xaa2914: ldur            x0, [fp, #-8]
    // 0xaa2918: LoadField: r1 = r0->field_4f
    //     0xaa2918: ldur            w1, [x0, #0x4f]
    // 0xaa291c: DecompressPointer r1
    //     0xaa291c: add             x1, x1, HEAP, lsl #32
    // 0xaa2920: cmp             w1, NULL
    // 0xaa2924: b.eq            #0xaa2978
    // 0xaa2928: r1 = <LongPressGestureRecognizer>
    //     0xaa2928: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a128] TypeArguments: <LongPressGestureRecognizer>
    //     0xaa292c: ldr             x1, [x1, #0x128]
    // 0xaa2930: r0 = GestureRecognizerFactoryWithHandlers()
    //     0xaa2930: bl              #0x947d60  ; AllocateGestureRecognizerFactoryWithHandlersStub -> GestureRecognizerFactoryWithHandlers<X0 bound GestureRecognizer> (size=0x14)
    // 0xaa2934: ldur            x2, [fp, #-0x18]
    // 0xaa2938: r1 = Function '<anonymous closure>':.
    //     0xaa2938: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a130] AnonymousClosure: (0xaa2e64), in [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::build (0xaa27cc)
    //     0xaa293c: ldr             x1, [x1, #0x130]
    // 0xaa2940: stur            x0, [fp, #-0x20]
    // 0xaa2944: r0 = AllocateClosure()
    //     0xaa2944: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa2948: ldur            x3, [fp, #-0x20]
    // 0xaa294c: StoreField: r3->field_b = r0
    //     0xaa294c: stur            w0, [x3, #0xb]
    // 0xaa2950: ldur            x2, [fp, #-0x18]
    // 0xaa2954: r1 = Function '<anonymous closure>':.
    //     0xaa2954: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a138] AnonymousClosure: (0xaa2d98), in [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::build (0xaa27cc)
    //     0xaa2958: ldr             x1, [x1, #0x138]
    // 0xaa295c: r0 = AllocateClosure()
    //     0xaa295c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa2960: ldur            x3, [fp, #-0x20]
    // 0xaa2964: StoreField: r3->field_f = r0
    //     0xaa2964: stur            w0, [x3, #0xf]
    // 0xaa2968: ldur            x1, [fp, #-0x10]
    // 0xaa296c: r2 = LongPressGestureRecognizer
    //     0xaa296c: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a140] Type: LongPressGestureRecognizer
    //     0xaa2970: ldr             x2, [x2, #0x140]
    // 0xaa2974: r0 = []=()
    //     0xaa2974: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xaa2978: ldur            x0, [fp, #-8]
    // 0xaa297c: LoadField: r1 = r0->field_9f
    //     0xaa297c: ldur            w1, [x0, #0x9f]
    // 0xaa2980: DecompressPointer r1
    //     0xaa2980: add             x1, x1, HEAP, lsl #32
    // 0xaa2984: cmp             w1, NULL
    // 0xaa2988: b.ne            #0xaa29ac
    // 0xaa298c: LoadField: r1 = r0->field_a3
    //     0xaa298c: ldur            w1, [x0, #0xa3]
    // 0xaa2990: DecompressPointer r1
    //     0xaa2990: add             x1, x1, HEAP, lsl #32
    // 0xaa2994: cmp             w1, NULL
    // 0xaa2998: b.ne            #0xaa29ac
    // 0xaa299c: LoadField: r1 = r0->field_a7
    //     0xaa299c: ldur            w1, [x0, #0xa7]
    // 0xaa29a0: DecompressPointer r1
    //     0xaa29a0: add             x1, x1, HEAP, lsl #32
    // 0xaa29a4: cmp             w1, NULL
    // 0xaa29a8: b.eq            #0xaa29fc
    // 0xaa29ac: r1 = <VerticalDragGestureRecognizer>
    //     0xaa29ac: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a148] TypeArguments: <VerticalDragGestureRecognizer>
    //     0xaa29b0: ldr             x1, [x1, #0x148]
    // 0xaa29b4: r0 = GestureRecognizerFactoryWithHandlers()
    //     0xaa29b4: bl              #0x947d60  ; AllocateGestureRecognizerFactoryWithHandlersStub -> GestureRecognizerFactoryWithHandlers<X0 bound GestureRecognizer> (size=0x14)
    // 0xaa29b8: ldur            x2, [fp, #-0x18]
    // 0xaa29bc: r1 = Function '<anonymous closure>':.
    //     0xaa29bc: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a150] AnonymousClosure: (0xa9a790), in [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::build (0xaa27cc)
    //     0xaa29c0: ldr             x1, [x1, #0x150]
    // 0xaa29c4: stur            x0, [fp, #-0x20]
    // 0xaa29c8: r0 = AllocateClosure()
    //     0xaa29c8: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa29cc: ldur            x3, [fp, #-0x20]
    // 0xaa29d0: StoreField: r3->field_b = r0
    //     0xaa29d0: stur            w0, [x3, #0xb]
    // 0xaa29d4: ldur            x2, [fp, #-0x18]
    // 0xaa29d8: r1 = Function '<anonymous closure>':.
    //     0xaa29d8: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a158] AnonymousClosure: (0xaa2c60), in [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::build (0xaa27cc)
    //     0xaa29dc: ldr             x1, [x1, #0x158]
    // 0xaa29e0: r0 = AllocateClosure()
    //     0xaa29e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa29e4: ldur            x3, [fp, #-0x20]
    // 0xaa29e8: StoreField: r3->field_f = r0
    //     0xaa29e8: stur            w0, [x3, #0xf]
    // 0xaa29ec: ldur            x1, [fp, #-0x10]
    // 0xaa29f0: r2 = VerticalDragGestureRecognizer
    //     0xaa29f0: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a160] Type: VerticalDragGestureRecognizer
    //     0xaa29f4: ldr             x2, [x2, #0x160]
    // 0xaa29f8: r0 = []=()
    //     0xaa29f8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xaa29fc: ldur            x0, [fp, #-8]
    // 0xaa2a00: LoadField: r1 = r0->field_b3
    //     0xaa2a00: ldur            w1, [x0, #0xb3]
    // 0xaa2a04: DecompressPointer r1
    //     0xaa2a04: add             x1, x1, HEAP, lsl #32
    // 0xaa2a08: cmp             w1, NULL
    // 0xaa2a0c: b.ne            #0xaa2a30
    // 0xaa2a10: LoadField: r1 = r0->field_b7
    //     0xaa2a10: ldur            w1, [x0, #0xb7]
    // 0xaa2a14: DecompressPointer r1
    //     0xaa2a14: add             x1, x1, HEAP, lsl #32
    // 0xaa2a18: cmp             w1, NULL
    // 0xaa2a1c: b.ne            #0xaa2a30
    // 0xaa2a20: LoadField: r1 = r0->field_bb
    //     0xaa2a20: ldur            w1, [x0, #0xbb]
    // 0xaa2a24: DecompressPointer r1
    //     0xaa2a24: add             x1, x1, HEAP, lsl #32
    // 0xaa2a28: cmp             w1, NULL
    // 0xaa2a2c: b.eq            #0xaa2a80
    // 0xaa2a30: r1 = <HorizontalDragGestureRecognizer>
    //     0xaa2a30: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a168] TypeArguments: <HorizontalDragGestureRecognizer>
    //     0xaa2a34: ldr             x1, [x1, #0x168]
    // 0xaa2a38: r0 = GestureRecognizerFactoryWithHandlers()
    //     0xaa2a38: bl              #0x947d60  ; AllocateGestureRecognizerFactoryWithHandlersStub -> GestureRecognizerFactoryWithHandlers<X0 bound GestureRecognizer> (size=0x14)
    // 0xaa2a3c: ldur            x2, [fp, #-0x18]
    // 0xaa2a40: r1 = Function '<anonymous closure>':.
    //     0xaa2a40: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a170] AnonymousClosure: (0xaa2c1c), in [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::build (0xaa27cc)
    //     0xaa2a44: ldr             x1, [x1, #0x170]
    // 0xaa2a48: stur            x0, [fp, #-0x20]
    // 0xaa2a4c: r0 = AllocateClosure()
    //     0xaa2a4c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa2a50: ldur            x3, [fp, #-0x20]
    // 0xaa2a54: StoreField: r3->field_b = r0
    //     0xaa2a54: stur            w0, [x3, #0xb]
    // 0xaa2a58: ldur            x2, [fp, #-0x18]
    // 0xaa2a5c: r1 = Function '<anonymous closure>':.
    //     0xaa2a5c: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a178] AnonymousClosure: (0xaa2ae4), in [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::build (0xaa27cc)
    //     0xaa2a60: ldr             x1, [x1, #0x178]
    // 0xaa2a64: r0 = AllocateClosure()
    //     0xaa2a64: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa2a68: ldur            x3, [fp, #-0x20]
    // 0xaa2a6c: StoreField: r3->field_f = r0
    //     0xaa2a6c: stur            w0, [x3, #0xf]
    // 0xaa2a70: ldur            x1, [fp, #-0x10]
    // 0xaa2a74: r2 = HorizontalDragGestureRecognizer
    //     0xaa2a74: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a180] Type: HorizontalDragGestureRecognizer
    //     0xaa2a78: ldr             x2, [x2, #0x180]
    // 0xaa2a7c: r0 = []=()
    //     0xaa2a7c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xaa2a80: ldur            x0, [fp, #-8]
    // 0xaa2a84: ldur            x1, [fp, #-0x10]
    // 0xaa2a88: LoadField: r2 = r0->field_f3
    //     0xaa2a88: ldur            w2, [x0, #0xf3]
    // 0xaa2a8c: DecompressPointer r2
    //     0xaa2a8c: add             x2, x2, HEAP, lsl #32
    // 0xaa2a90: stur            x2, [fp, #-0x28]
    // 0xaa2a94: LoadField: r3 = r0->field_f7
    //     0xaa2a94: ldur            w3, [x0, #0xf7]
    // 0xaa2a98: DecompressPointer r3
    //     0xaa2a98: add             x3, x3, HEAP, lsl #32
    // 0xaa2a9c: stur            x3, [fp, #-0x20]
    // 0xaa2aa0: LoadField: r4 = r0->field_b
    //     0xaa2aa0: ldur            w4, [x0, #0xb]
    // 0xaa2aa4: DecompressPointer r4
    //     0xaa2aa4: add             x4, x4, HEAP, lsl #32
    // 0xaa2aa8: stur            x4, [fp, #-0x18]
    // 0xaa2aac: r0 = RawGestureDetector()
    //     0xaa2aac: bl              #0x9d2c8c  ; AllocateRawGestureDetectorStub -> RawGestureDetector (size=0x20)
    // 0xaa2ab0: ldur            x1, [fp, #-0x18]
    // 0xaa2ab4: StoreField: r0->field_b = r1
    //     0xaa2ab4: stur            w1, [x0, #0xb]
    // 0xaa2ab8: ldur            x1, [fp, #-0x10]
    // 0xaa2abc: StoreField: r0->field_f = r1
    //     0xaa2abc: stur            w1, [x0, #0xf]
    // 0xaa2ac0: ldur            x1, [fp, #-0x28]
    // 0xaa2ac4: StoreField: r0->field_13 = r1
    //     0xaa2ac4: stur            w1, [x0, #0x13]
    // 0xaa2ac8: ldur            x1, [fp, #-0x20]
    // 0xaa2acc: ArrayStore: r0[0] = r1  ; List_4
    //     0xaa2acc: stur            w1, [x0, #0x17]
    // 0xaa2ad0: LeaveFrame
    //     0xaa2ad0: mov             SP, fp
    //     0xaa2ad4: ldp             fp, lr, [SP], #0x10
    // 0xaa2ad8: ret
    //     0xaa2ad8: ret             
    // 0xaa2adc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa2adc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa2ae0: b               #0xaa27ec
  }
  [closure] void <anonymous closure>(dynamic, HorizontalDragGestureRecognizer) {
    // ** addr: 0xaa2ae4, size: 0x138
    // 0xaa2ae4: EnterFrame
    //     0xaa2ae4: stp             fp, lr, [SP, #-0x10]!
    //     0xaa2ae8: mov             fp, SP
    // 0xaa2aec: AllocStack(0x8)
    //     0xaa2aec: sub             SP, SP, #8
    // 0xaa2af0: SetupParameters()
    //     0xaa2af0: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    //     0xaa2af4: ldr             x0, [fp, #0x18]
    //     0xaa2af8: ldur            w3, [x0, #0x17]
    //     0xaa2afc: add             x3, x3, HEAP, lsl #32
    //     0xaa2b00: stur            x3, [fp, #-8]
    // 0xaa2af0: r1 = Instance_DragStartBehavior
    // 0xaa2b04: CheckStackOverflow
    //     0xaa2b04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa2b08: cmp             SP, x16
    //     0xaa2b0c: b.ls            #0xaa2c14
    // 0xaa2b10: ldr             x4, [fp, #0x10]
    // 0xaa2b14: StoreField: r4->field_2b = rNULL
    //     0xaa2b14: stur            NULL, [x4, #0x2b]
    // 0xaa2b18: LoadField: r2 = r3->field_f
    //     0xaa2b18: ldur            w2, [x3, #0xf]
    // 0xaa2b1c: DecompressPointer r2
    //     0xaa2b1c: add             x2, x2, HEAP, lsl #32
    // 0xaa2b20: LoadField: r0 = r2->field_b3
    //     0xaa2b20: ldur            w0, [x2, #0xb3]
    // 0xaa2b24: DecompressPointer r0
    //     0xaa2b24: add             x0, x0, HEAP, lsl #32
    // 0xaa2b28: StoreField: r4->field_2f = r0
    //     0xaa2b28: stur            w0, [x4, #0x2f]
    //     0xaa2b2c: ldurb           w16, [x4, #-1]
    //     0xaa2b30: ldurb           w17, [x0, #-1]
    //     0xaa2b34: and             x16, x17, x16, lsr #2
    //     0xaa2b38: tst             x16, HEAP, lsr #32
    //     0xaa2b3c: b.eq            #0xaa2b44
    //     0xaa2b40: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa2b44: LoadField: r0 = r2->field_b7
    //     0xaa2b44: ldur            w0, [x2, #0xb7]
    // 0xaa2b48: DecompressPointer r0
    //     0xaa2b48: add             x0, x0, HEAP, lsl #32
    // 0xaa2b4c: StoreField: r4->field_33 = r0
    //     0xaa2b4c: stur            w0, [x4, #0x33]
    //     0xaa2b50: ldurb           w16, [x4, #-1]
    //     0xaa2b54: ldurb           w17, [x0, #-1]
    //     0xaa2b58: and             x16, x17, x16, lsr #2
    //     0xaa2b5c: tst             x16, HEAP, lsr #32
    //     0xaa2b60: b.eq            #0xaa2b68
    //     0xaa2b64: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa2b68: LoadField: r0 = r2->field_bb
    //     0xaa2b68: ldur            w0, [x2, #0xbb]
    // 0xaa2b6c: DecompressPointer r0
    //     0xaa2b6c: add             x0, x0, HEAP, lsl #32
    // 0xaa2b70: StoreField: r4->field_37 = r0
    //     0xaa2b70: stur            w0, [x4, #0x37]
    //     0xaa2b74: ldurb           w16, [x4, #-1]
    //     0xaa2b78: ldurb           w17, [x0, #-1]
    //     0xaa2b7c: and             x16, x17, x16, lsr #2
    //     0xaa2b80: tst             x16, HEAP, lsr #32
    //     0xaa2b84: b.eq            #0xaa2b8c
    //     0xaa2b88: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa2b8c: StoreField: r4->field_3b = rNULL
    //     0xaa2b8c: stur            NULL, [x4, #0x3b]
    // 0xaa2b90: StoreField: r4->field_23 = r1
    //     0xaa2b90: stur            w1, [x4, #0x23]
    // 0xaa2b94: LoadField: r1 = r3->field_1b
    //     0xaa2b94: ldur            w1, [x3, #0x1b]
    // 0xaa2b98: DecompressPointer r1
    //     0xaa2b98: add             x1, x1, HEAP, lsl #32
    // 0xaa2b9c: LoadField: r2 = r3->field_13
    //     0xaa2b9c: ldur            w2, [x3, #0x13]
    // 0xaa2ba0: DecompressPointer r2
    //     0xaa2ba0: add             x2, x2, HEAP, lsl #32
    // 0xaa2ba4: r0 = LoadClassIdInstr(r1)
    //     0xaa2ba4: ldur            x0, [x1, #-1]
    //     0xaa2ba8: ubfx            x0, x0, #0xc, #0x14
    // 0xaa2bac: r0 = GDT[cid_x0 + -0xff8]()
    //     0xaa2bac: sub             lr, x0, #0xff8
    //     0xaa2bb0: ldr             lr, [x21, lr, lsl #3]
    //     0xaa2bb4: blr             lr
    // 0xaa2bb8: ldr             x1, [fp, #0x10]
    // 0xaa2bbc: StoreField: r1->field_27 = r0
    //     0xaa2bbc: stur            w0, [x1, #0x27]
    //     0xaa2bc0: ldurb           w16, [x1, #-1]
    //     0xaa2bc4: ldurb           w17, [x0, #-1]
    //     0xaa2bc8: and             x16, x17, x16, lsr #2
    //     0xaa2bcc: tst             x16, HEAP, lsr #32
    //     0xaa2bd0: b.eq            #0xaa2bd8
    //     0xaa2bd4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa2bd8: ldur            x2, [fp, #-8]
    // 0xaa2bdc: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xaa2bdc: ldur            w0, [x2, #0x17]
    // 0xaa2be0: DecompressPointer r0
    //     0xaa2be0: add             x0, x0, HEAP, lsl #32
    // 0xaa2be4: StoreField: r1->field_7 = r0
    //     0xaa2be4: stur            w0, [x1, #7]
    //     0xaa2be8: ldurb           w16, [x1, #-1]
    //     0xaa2bec: ldurb           w17, [x0, #-1]
    //     0xaa2bf0: and             x16, x17, x16, lsr #2
    //     0xaa2bf4: tst             x16, HEAP, lsr #32
    //     0xaa2bf8: b.eq            #0xaa2c00
    //     0xaa2bfc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa2c00: StoreField: r1->field_b = rNULL
    //     0xaa2c00: stur            NULL, [x1, #0xb]
    // 0xaa2c04: r0 = Null
    //     0xaa2c04: mov             x0, NULL
    // 0xaa2c08: LeaveFrame
    //     0xaa2c08: mov             SP, fp
    //     0xaa2c0c: ldp             fp, lr, [SP], #0x10
    // 0xaa2c10: ret
    //     0xaa2c10: ret             
    // 0xaa2c14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa2c14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa2c18: b               #0xaa2b10
  }
  [closure] HorizontalDragGestureRecognizer <anonymous closure>(dynamic) {
    // ** addr: 0xaa2c1c, size: 0x44
    // 0xaa2c1c: EnterFrame
    //     0xaa2c1c: stp             fp, lr, [SP, #-0x10]!
    //     0xaa2c20: mov             fp, SP
    // 0xaa2c24: AllocStack(0x8)
    //     0xaa2c24: sub             SP, SP, #8
    // 0xaa2c28: CheckStackOverflow
    //     0xaa2c28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa2c2c: cmp             SP, x16
    //     0xaa2c30: b.ls            #0xaa2c58
    // 0xaa2c34: r0 = HorizontalDragGestureRecognizer()
    //     0xaa2c34: bl              #0x853680  ; AllocateHorizontalDragGestureRecognizerStub -> HorizontalDragGestureRecognizer (size=0x90)
    // 0xaa2c38: mov             x1, x0
    // 0xaa2c3c: r2 = Null
    //     0xaa2c3c: mov             x2, NULL
    // 0xaa2c40: stur            x0, [fp, #-8]
    // 0xaa2c44: r0 = DragGestureRecognizer()
    //     0xaa2c44: bl              #0x85347c  ; [package:flutter/src/gestures/monodrag.dart] DragGestureRecognizer::DragGestureRecognizer
    // 0xaa2c48: ldur            x0, [fp, #-8]
    // 0xaa2c4c: LeaveFrame
    //     0xaa2c4c: mov             SP, fp
    //     0xaa2c50: ldp             fp, lr, [SP], #0x10
    // 0xaa2c54: ret
    //     0xaa2c54: ret             
    // 0xaa2c58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa2c58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa2c5c: b               #0xaa2c34
  }
  [closure] void <anonymous closure>(dynamic, VerticalDragGestureRecognizer) {
    // ** addr: 0xaa2c60, size: 0x138
    // 0xaa2c60: EnterFrame
    //     0xaa2c60: stp             fp, lr, [SP, #-0x10]!
    //     0xaa2c64: mov             fp, SP
    // 0xaa2c68: AllocStack(0x8)
    //     0xaa2c68: sub             SP, SP, #8
    // 0xaa2c6c: SetupParameters()
    //     0xaa2c6c: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    //     0xaa2c70: ldr             x0, [fp, #0x18]
    //     0xaa2c74: ldur            w3, [x0, #0x17]
    //     0xaa2c78: add             x3, x3, HEAP, lsl #32
    //     0xaa2c7c: stur            x3, [fp, #-8]
    // 0xaa2c6c: r1 = Instance_DragStartBehavior
    // 0xaa2c80: CheckStackOverflow
    //     0xaa2c80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa2c84: cmp             SP, x16
    //     0xaa2c88: b.ls            #0xaa2d90
    // 0xaa2c8c: ldr             x4, [fp, #0x10]
    // 0xaa2c90: StoreField: r4->field_2b = rNULL
    //     0xaa2c90: stur            NULL, [x4, #0x2b]
    // 0xaa2c94: LoadField: r2 = r3->field_f
    //     0xaa2c94: ldur            w2, [x3, #0xf]
    // 0xaa2c98: DecompressPointer r2
    //     0xaa2c98: add             x2, x2, HEAP, lsl #32
    // 0xaa2c9c: LoadField: r0 = r2->field_9f
    //     0xaa2c9c: ldur            w0, [x2, #0x9f]
    // 0xaa2ca0: DecompressPointer r0
    //     0xaa2ca0: add             x0, x0, HEAP, lsl #32
    // 0xaa2ca4: StoreField: r4->field_2f = r0
    //     0xaa2ca4: stur            w0, [x4, #0x2f]
    //     0xaa2ca8: ldurb           w16, [x4, #-1]
    //     0xaa2cac: ldurb           w17, [x0, #-1]
    //     0xaa2cb0: and             x16, x17, x16, lsr #2
    //     0xaa2cb4: tst             x16, HEAP, lsr #32
    //     0xaa2cb8: b.eq            #0xaa2cc0
    //     0xaa2cbc: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa2cc0: LoadField: r0 = r2->field_a3
    //     0xaa2cc0: ldur            w0, [x2, #0xa3]
    // 0xaa2cc4: DecompressPointer r0
    //     0xaa2cc4: add             x0, x0, HEAP, lsl #32
    // 0xaa2cc8: StoreField: r4->field_33 = r0
    //     0xaa2cc8: stur            w0, [x4, #0x33]
    //     0xaa2ccc: ldurb           w16, [x4, #-1]
    //     0xaa2cd0: ldurb           w17, [x0, #-1]
    //     0xaa2cd4: and             x16, x17, x16, lsr #2
    //     0xaa2cd8: tst             x16, HEAP, lsr #32
    //     0xaa2cdc: b.eq            #0xaa2ce4
    //     0xaa2ce0: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa2ce4: LoadField: r0 = r2->field_a7
    //     0xaa2ce4: ldur            w0, [x2, #0xa7]
    // 0xaa2ce8: DecompressPointer r0
    //     0xaa2ce8: add             x0, x0, HEAP, lsl #32
    // 0xaa2cec: StoreField: r4->field_37 = r0
    //     0xaa2cec: stur            w0, [x4, #0x37]
    //     0xaa2cf0: ldurb           w16, [x4, #-1]
    //     0xaa2cf4: ldurb           w17, [x0, #-1]
    //     0xaa2cf8: and             x16, x17, x16, lsr #2
    //     0xaa2cfc: tst             x16, HEAP, lsr #32
    //     0xaa2d00: b.eq            #0xaa2d08
    //     0xaa2d04: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa2d08: StoreField: r4->field_3b = rNULL
    //     0xaa2d08: stur            NULL, [x4, #0x3b]
    // 0xaa2d0c: StoreField: r4->field_23 = r1
    //     0xaa2d0c: stur            w1, [x4, #0x23]
    // 0xaa2d10: LoadField: r1 = r3->field_1b
    //     0xaa2d10: ldur            w1, [x3, #0x1b]
    // 0xaa2d14: DecompressPointer r1
    //     0xaa2d14: add             x1, x1, HEAP, lsl #32
    // 0xaa2d18: LoadField: r2 = r3->field_13
    //     0xaa2d18: ldur            w2, [x3, #0x13]
    // 0xaa2d1c: DecompressPointer r2
    //     0xaa2d1c: add             x2, x2, HEAP, lsl #32
    // 0xaa2d20: r0 = LoadClassIdInstr(r1)
    //     0xaa2d20: ldur            x0, [x1, #-1]
    //     0xaa2d24: ubfx            x0, x0, #0xc, #0x14
    // 0xaa2d28: r0 = GDT[cid_x0 + -0xff8]()
    //     0xaa2d28: sub             lr, x0, #0xff8
    //     0xaa2d2c: ldr             lr, [x21, lr, lsl #3]
    //     0xaa2d30: blr             lr
    // 0xaa2d34: ldr             x1, [fp, #0x10]
    // 0xaa2d38: StoreField: r1->field_27 = r0
    //     0xaa2d38: stur            w0, [x1, #0x27]
    //     0xaa2d3c: ldurb           w16, [x1, #-1]
    //     0xaa2d40: ldurb           w17, [x0, #-1]
    //     0xaa2d44: and             x16, x17, x16, lsr #2
    //     0xaa2d48: tst             x16, HEAP, lsr #32
    //     0xaa2d4c: b.eq            #0xaa2d54
    //     0xaa2d50: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa2d54: ldur            x2, [fp, #-8]
    // 0xaa2d58: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xaa2d58: ldur            w0, [x2, #0x17]
    // 0xaa2d5c: DecompressPointer r0
    //     0xaa2d5c: add             x0, x0, HEAP, lsl #32
    // 0xaa2d60: StoreField: r1->field_7 = r0
    //     0xaa2d60: stur            w0, [x1, #7]
    //     0xaa2d64: ldurb           w16, [x1, #-1]
    //     0xaa2d68: ldurb           w17, [x0, #-1]
    //     0xaa2d6c: and             x16, x17, x16, lsr #2
    //     0xaa2d70: tst             x16, HEAP, lsr #32
    //     0xaa2d74: b.eq            #0xaa2d7c
    //     0xaa2d78: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa2d7c: StoreField: r1->field_b = rNULL
    //     0xaa2d7c: stur            NULL, [x1, #0xb]
    // 0xaa2d80: r0 = Null
    //     0xaa2d80: mov             x0, NULL
    // 0xaa2d84: LeaveFrame
    //     0xaa2d84: mov             SP, fp
    //     0xaa2d88: ldp             fp, lr, [SP], #0x10
    // 0xaa2d8c: ret
    //     0xaa2d8c: ret             
    // 0xaa2d90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa2d90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa2d94: b               #0xaa2c8c
  }
  [closure] void <anonymous closure>(dynamic, LongPressGestureRecognizer) {
    // ** addr: 0xaa2d98, size: 0xcc
    // 0xaa2d98: EnterFrame
    //     0xaa2d98: stp             fp, lr, [SP, #-0x10]!
    //     0xaa2d9c: mov             fp, SP
    // 0xaa2da0: ldr             x1, [fp, #0x18]
    // 0xaa2da4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xaa2da4: ldur            w2, [x1, #0x17]
    // 0xaa2da8: DecompressPointer r2
    //     0xaa2da8: add             x2, x2, HEAP, lsl #32
    // 0xaa2dac: ldr             x1, [fp, #0x10]
    // 0xaa2db0: StoreField: r1->field_53 = rNULL
    //     0xaa2db0: stur            NULL, [x1, #0x53]
    // 0xaa2db4: StoreField: r1->field_57 = rNULL
    //     0xaa2db4: stur            NULL, [x1, #0x57]
    // 0xaa2db8: LoadField: r3 = r2->field_f
    //     0xaa2db8: ldur            w3, [x2, #0xf]
    // 0xaa2dbc: DecompressPointer r3
    //     0xaa2dbc: add             x3, x3, HEAP, lsl #32
    // 0xaa2dc0: LoadField: r0 = r3->field_4f
    //     0xaa2dc0: ldur            w0, [x3, #0x4f]
    // 0xaa2dc4: DecompressPointer r0
    //     0xaa2dc4: add             x0, x0, HEAP, lsl #32
    // 0xaa2dc8: StoreField: r1->field_5b = r0
    //     0xaa2dc8: stur            w0, [x1, #0x5b]
    //     0xaa2dcc: ldurb           w16, [x1, #-1]
    //     0xaa2dd0: ldurb           w17, [x0, #-1]
    //     0xaa2dd4: and             x16, x17, x16, lsr #2
    //     0xaa2dd8: tst             x16, HEAP, lsr #32
    //     0xaa2ddc: b.eq            #0xaa2de4
    //     0xaa2de0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa2de4: StoreField: r1->field_5f = rNULL
    //     0xaa2de4: stur            NULL, [x1, #0x5f]
    // 0xaa2de8: StoreField: r1->field_63 = rNULL
    //     0xaa2de8: stur            NULL, [x1, #0x63]
    // 0xaa2dec: StoreField: r1->field_67 = rNULL
    //     0xaa2dec: stur            NULL, [x1, #0x67]
    // 0xaa2df0: StoreField: r1->field_6b = rNULL
    //     0xaa2df0: stur            NULL, [x1, #0x6b]
    // 0xaa2df4: StoreField: r1->field_6f = rNULL
    //     0xaa2df4: stur            NULL, [x1, #0x6f]
    // 0xaa2df8: StoreField: r1->field_73 = rNULL
    //     0xaa2df8: stur            NULL, [x1, #0x73]
    // 0xaa2dfc: StoreField: r1->field_77 = rNULL
    //     0xaa2dfc: stur            NULL, [x1, #0x77]
    // 0xaa2e00: StoreField: r1->field_7b = rNULL
    //     0xaa2e00: stur            NULL, [x1, #0x7b]
    // 0xaa2e04: StoreField: r1->field_7f = rNULL
    //     0xaa2e04: stur            NULL, [x1, #0x7f]
    // 0xaa2e08: StoreField: r1->field_83 = rNULL
    //     0xaa2e08: stur            NULL, [x1, #0x83]
    // 0xaa2e0c: StoreField: r1->field_87 = rNULL
    //     0xaa2e0c: stur            NULL, [x1, #0x87]
    // 0xaa2e10: StoreField: r1->field_8b = rNULL
    //     0xaa2e10: stur            NULL, [x1, #0x8b]
    // 0xaa2e14: StoreField: r1->field_8f = rNULL
    //     0xaa2e14: stur            NULL, [x1, #0x8f]
    // 0xaa2e18: StoreField: r1->field_93 = rNULL
    //     0xaa2e18: stur            NULL, [x1, #0x93]
    // 0xaa2e1c: StoreField: r1->field_97 = rNULL
    //     0xaa2e1c: stur            NULL, [x1, #0x97]
    // 0xaa2e20: StoreField: r1->field_9b = rNULL
    //     0xaa2e20: stur            NULL, [x1, #0x9b]
    // 0xaa2e24: StoreField: r1->field_9f = rNULL
    //     0xaa2e24: stur            NULL, [x1, #0x9f]
    // 0xaa2e28: StoreField: r1->field_a3 = rNULL
    //     0xaa2e28: stur            NULL, [x1, #0xa3]
    // 0xaa2e2c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xaa2e2c: ldur            w0, [x2, #0x17]
    // 0xaa2e30: DecompressPointer r0
    //     0xaa2e30: add             x0, x0, HEAP, lsl #32
    // 0xaa2e34: StoreField: r1->field_7 = r0
    //     0xaa2e34: stur            w0, [x1, #7]
    //     0xaa2e38: ldurb           w16, [x1, #-1]
    //     0xaa2e3c: ldurb           w17, [x0, #-1]
    //     0xaa2e40: and             x16, x17, x16, lsr #2
    //     0xaa2e44: tst             x16, HEAP, lsr #32
    //     0xaa2e48: b.eq            #0xaa2e50
    //     0xaa2e4c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa2e50: StoreField: r1->field_b = rNULL
    //     0xaa2e50: stur            NULL, [x1, #0xb]
    // 0xaa2e54: r0 = Null
    //     0xaa2e54: mov             x0, NULL
    // 0xaa2e58: LeaveFrame
    //     0xaa2e58: mov             SP, fp
    //     0xaa2e5c: ldp             fp, lr, [SP], #0x10
    // 0xaa2e60: ret
    //     0xaa2e60: ret             
  }
  [closure] LongPressGestureRecognizer <anonymous closure>(dynamic) {
    // ** addr: 0xaa2e64, size: 0x68
    // 0xaa2e64: EnterFrame
    //     0xaa2e64: stp             fp, lr, [SP, #-0x10]!
    //     0xaa2e68: mov             fp, SP
    // 0xaa2e6c: AllocStack(0x10)
    //     0xaa2e6c: sub             SP, SP, #0x10
    // 0xaa2e70: CheckStackOverflow
    //     0xaa2e70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa2e74: cmp             SP, x16
    //     0xaa2e78: b.ls            #0xaa2ec4
    // 0xaa2e7c: r0 = LongPressGestureRecognizer()
    //     0xaa2e7c: bl              #0x762f70  ; AllocateLongPressGestureRecognizerStub -> LongPressGestureRecognizer (size=0xac)
    // 0xaa2e80: mov             x4, x0
    // 0xaa2e84: r0 = false
    //     0xaa2e84: add             x0, NULL, #0x30  ; false
    // 0xaa2e88: stur            x4, [fp, #-8]
    // 0xaa2e8c: StoreField: r4->field_47 = r0
    //     0xaa2e8c: stur            w0, [x4, #0x47]
    // 0xaa2e90: str             NULL, [SP]
    // 0xaa2e94: mov             x1, x4
    // 0xaa2e98: r2 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@426232524': static.
    //     0xaa2e98: add             x2, PP, #0x33, lsl #12  ; [pp+0x330a8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@426232524': static. (0x7e54fb163834)
    //     0xaa2e9c: ldr             x2, [x2, #0xa8]
    // 0xaa2ea0: r3 = Instance_Duration
    //     0xaa2ea0: ldr             x3, [PP, #0x4fa0]  ; [pp+0x4fa0] Obj!Duration@e3a0b1
    // 0xaa2ea4: r5 = Null
    //     0xaa2ea4: mov             x5, NULL
    // 0xaa2ea8: r4 = const [0, 0x5, 0x1, 0x4, postAcceptSlopTolerance, 0x4, null]
    //     0xaa2ea8: add             x4, PP, #0x33, lsl #12  ; [pp+0x330b0] List(7) [0, 0x5, 0x1, 0x4, "postAcceptSlopTolerance", 0x4, Null]
    //     0xaa2eac: ldr             x4, [x4, #0xb0]
    // 0xaa2eb0: r0 = PrimaryPointerGestureRecognizer()
    //     0xaa2eb0: bl              #0x762f7c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::PrimaryPointerGestureRecognizer
    // 0xaa2eb4: ldur            x0, [fp, #-8]
    // 0xaa2eb8: LeaveFrame
    //     0xaa2eb8: mov             SP, fp
    //     0xaa2ebc: ldp             fp, lr, [SP], #0x10
    // 0xaa2ec0: ret
    //     0xaa2ec0: ret             
    // 0xaa2ec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa2ec4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa2ec8: b               #0xaa2e7c
  }
  [closure] void <anonymous closure>(dynamic, TapGestureRecognizer) {
    // ** addr: 0xaa2ecc, size: 0x104
    // 0xaa2ecc: EnterFrame
    //     0xaa2ecc: stp             fp, lr, [SP, #-0x10]!
    //     0xaa2ed0: mov             fp, SP
    // 0xaa2ed4: ldr             x1, [fp, #0x18]
    // 0xaa2ed8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xaa2ed8: ldur            w2, [x1, #0x17]
    // 0xaa2edc: DecompressPointer r2
    //     0xaa2edc: add             x2, x2, HEAP, lsl #32
    // 0xaa2ee0: LoadField: r1 = r2->field_f
    //     0xaa2ee0: ldur            w1, [x2, #0xf]
    // 0xaa2ee4: DecompressPointer r1
    //     0xaa2ee4: add             x1, x1, HEAP, lsl #32
    // 0xaa2ee8: LoadField: r0 = r1->field_f
    //     0xaa2ee8: ldur            w0, [x1, #0xf]
    // 0xaa2eec: DecompressPointer r0
    //     0xaa2eec: add             x0, x0, HEAP, lsl #32
    // 0xaa2ef0: ldr             x3, [fp, #0x10]
    // 0xaa2ef4: StoreField: r3->field_57 = r0
    //     0xaa2ef4: stur            w0, [x3, #0x57]
    //     0xaa2ef8: ldurb           w16, [x3, #-1]
    //     0xaa2efc: ldurb           w17, [x0, #-1]
    //     0xaa2f00: and             x16, x17, x16, lsr #2
    //     0xaa2f04: tst             x16, HEAP, lsr #32
    //     0xaa2f08: b.eq            #0xaa2f10
    //     0xaa2f0c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaa2f10: LoadField: r0 = r1->field_13
    //     0xaa2f10: ldur            w0, [x1, #0x13]
    // 0xaa2f14: DecompressPointer r0
    //     0xaa2f14: add             x0, x0, HEAP, lsl #32
    // 0xaa2f18: StoreField: r3->field_5b = r0
    //     0xaa2f18: stur            w0, [x3, #0x5b]
    //     0xaa2f1c: ldurb           w16, [x3, #-1]
    //     0xaa2f20: ldurb           w17, [x0, #-1]
    //     0xaa2f24: and             x16, x17, x16, lsr #2
    //     0xaa2f28: tst             x16, HEAP, lsr #32
    //     0xaa2f2c: b.eq            #0xaa2f34
    //     0xaa2f30: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaa2f34: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xaa2f34: ldur            w0, [x1, #0x17]
    // 0xaa2f38: DecompressPointer r0
    //     0xaa2f38: add             x0, x0, HEAP, lsl #32
    // 0xaa2f3c: StoreField: r3->field_5f = r0
    //     0xaa2f3c: stur            w0, [x3, #0x5f]
    //     0xaa2f40: ldurb           w16, [x3, #-1]
    //     0xaa2f44: ldurb           w17, [x0, #-1]
    //     0xaa2f48: and             x16, x17, x16, lsr #2
    //     0xaa2f4c: tst             x16, HEAP, lsr #32
    //     0xaa2f50: b.eq            #0xaa2f58
    //     0xaa2f54: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaa2f58: LoadField: r0 = r1->field_1b
    //     0xaa2f58: ldur            w0, [x1, #0x1b]
    // 0xaa2f5c: DecompressPointer r0
    //     0xaa2f5c: add             x0, x0, HEAP, lsl #32
    // 0xaa2f60: StoreField: r3->field_63 = r0
    //     0xaa2f60: stur            w0, [x3, #0x63]
    //     0xaa2f64: ldurb           w16, [x3, #-1]
    //     0xaa2f68: ldurb           w17, [x0, #-1]
    //     0xaa2f6c: and             x16, x17, x16, lsr #2
    //     0xaa2f70: tst             x16, HEAP, lsr #32
    //     0xaa2f74: b.eq            #0xaa2f7c
    //     0xaa2f78: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaa2f7c: StoreField: r3->field_67 = rNULL
    //     0xaa2f7c: stur            NULL, [x3, #0x67]
    // 0xaa2f80: StoreField: r3->field_6b = rNULL
    //     0xaa2f80: stur            NULL, [x3, #0x6b]
    // 0xaa2f84: StoreField: r3->field_6f = rNULL
    //     0xaa2f84: stur            NULL, [x3, #0x6f]
    // 0xaa2f88: StoreField: r3->field_73 = rNULL
    //     0xaa2f88: stur            NULL, [x3, #0x73]
    // 0xaa2f8c: StoreField: r3->field_77 = rNULL
    //     0xaa2f8c: stur            NULL, [x3, #0x77]
    // 0xaa2f90: StoreField: r3->field_7b = rNULL
    //     0xaa2f90: stur            NULL, [x3, #0x7b]
    // 0xaa2f94: StoreField: r3->field_7f = rNULL
    //     0xaa2f94: stur            NULL, [x3, #0x7f]
    // 0xaa2f98: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xaa2f98: ldur            w0, [x2, #0x17]
    // 0xaa2f9c: DecompressPointer r0
    //     0xaa2f9c: add             x0, x0, HEAP, lsl #32
    // 0xaa2fa0: StoreField: r3->field_7 = r0
    //     0xaa2fa0: stur            w0, [x3, #7]
    //     0xaa2fa4: ldurb           w16, [x3, #-1]
    //     0xaa2fa8: ldurb           w17, [x0, #-1]
    //     0xaa2fac: and             x16, x17, x16, lsr #2
    //     0xaa2fb0: tst             x16, HEAP, lsr #32
    //     0xaa2fb4: b.eq            #0xaa2fbc
    //     0xaa2fb8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaa2fbc: StoreField: r3->field_b = rNULL
    //     0xaa2fbc: stur            NULL, [x3, #0xb]
    // 0xaa2fc0: r0 = Null
    //     0xaa2fc0: mov             x0, NULL
    // 0xaa2fc4: LeaveFrame
    //     0xaa2fc4: mov             SP, fp
    //     0xaa2fc8: ldp             fp, lr, [SP], #0x10
    // 0xaa2fcc: ret
    //     0xaa2fcc: ret             
  }
}
