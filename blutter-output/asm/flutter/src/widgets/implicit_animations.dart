// lib: , url: package:flutter/src/widgets/implicit_animations.dart

// class id: 1049140, size: 0x8
class :: {
}

// class id: 3739, size: 0x14, field offset: 0x14
class TextStyleTween extends Tween<dynamic> {

  _ lerp(/* No info */) {
    // ** addr: 0x886a38, size: 0x90
    // 0x886a38: EnterFrame
    //     0x886a38: stp             fp, lr, [SP, #-0x10]!
    //     0x886a3c: mov             fp, SP
    // 0x886a40: CheckStackOverflow
    //     0x886a40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x886a44: cmp             SP, x16
    //     0x886a48: b.ls            #0x886aa0
    // 0x886a4c: LoadField: r0 = r1->field_b
    //     0x886a4c: ldur            w0, [x1, #0xb]
    // 0x886a50: DecompressPointer r0
    //     0x886a50: add             x0, x0, HEAP, lsl #32
    // 0x886a54: LoadField: r2 = r1->field_f
    //     0x886a54: ldur            w2, [x1, #0xf]
    // 0x886a58: DecompressPointer r2
    //     0x886a58: add             x2, x2, HEAP, lsl #32
    // 0x886a5c: r3 = inline_Allocate_Double()
    //     0x886a5c: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0x886a60: add             x3, x3, #0x10
    //     0x886a64: cmp             x1, x3
    //     0x886a68: b.ls            #0x886aa8
    //     0x886a6c: str             x3, [THR, #0x50]  ; THR::top
    //     0x886a70: sub             x3, x3, #0xf
    //     0x886a74: movz            x1, #0xe15c
    //     0x886a78: movk            x1, #0x3, lsl #16
    //     0x886a7c: stur            x1, [x3, #-1]
    // 0x886a80: StoreField: r3->field_7 = d0
    //     0x886a80: stur            d0, [x3, #7]
    // 0x886a84: mov             x1, x0
    // 0x886a88: r0 = lerp()
    //     0x886a88: bl              #0x877d8c  ; [package:flutter/src/painting/text_style.dart] TextStyle::lerp
    // 0x886a8c: cmp             w0, NULL
    // 0x886a90: b.eq            #0x886ac4
    // 0x886a94: LeaveFrame
    //     0x886a94: mov             SP, fp
    //     0x886a98: ldp             fp, lr, [SP], #0x10
    // 0x886a9c: ret
    //     0x886a9c: ret             
    // 0x886aa0: r0 = StackOverflowSharedWithFPURegs()
    //     0x886aa0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x886aa4: b               #0x886a4c
    // 0x886aa8: SaveReg d0
    //     0x886aa8: str             q0, [SP, #-0x10]!
    // 0x886aac: stp             x0, x2, [SP, #-0x10]!
    // 0x886ab0: r0 = AllocateDouble()
    //     0x886ab0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x886ab4: mov             x3, x0
    // 0x886ab8: ldp             x0, x2, [SP], #0x10
    // 0x886abc: RestoreReg d0
    //     0x886abc: ldr             q0, [SP], #0x10
    // 0x886ac0: b               #0x886a80
    // 0x886ac4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x886ac4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3740, size: 0x14, field offset: 0x14
class Matrix4Tween extends Tween<dynamic> {

  _ lerp(/* No info */) {
    // ** addr: 0x8847ec, size: 0x1ac
    // 0x8847ec: EnterFrame
    //     0x8847ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8847f0: mov             fp, SP
    // 0x8847f4: AllocStack(0x48)
    //     0x8847f4: sub             SP, SP, #0x48
    // 0x8847f8: SetupParameters(Matrix4Tween this /* r1 => r1, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x40 */)
    //     0x8847f8: stur            x1, [fp, #-8]
    //     0x8847fc: stur            d0, [fp, #-0x40]
    // 0x884800: CheckStackOverflow
    //     0x884800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x884804: cmp             SP, x16
    //     0x884808: b.ls            #0x884988
    // 0x88480c: r0 = Vector3()
    //     0x88480c: bl              #0x68e1e4  ; AllocateVector3Stub -> Vector3 (size=0xc)
    // 0x884810: r4 = 6
    //     0x884810: movz            x4, #0x6
    // 0x884814: stur            x0, [fp, #-0x10]
    // 0x884818: r0 = AllocateFloat64Array()
    //     0x884818: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0x88481c: ldur            x2, [fp, #-0x10]
    // 0x884820: StoreField: r2->field_7 = r0
    //     0x884820: stur            w0, [x2, #7]
    // 0x884824: r0 = Vector3()
    //     0x884824: bl              #0x68e1e4  ; AllocateVector3Stub -> Vector3 (size=0xc)
    // 0x884828: r4 = 6
    //     0x884828: movz            x4, #0x6
    // 0x88482c: stur            x0, [fp, #-0x18]
    // 0x884830: r0 = AllocateFloat64Array()
    //     0x884830: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0x884834: ldur            x2, [fp, #-0x18]
    // 0x884838: StoreField: r2->field_7 = r0
    //     0x884838: stur            w0, [x2, #7]
    // 0x88483c: r1 = Null
    //     0x88483c: mov             x1, NULL
    // 0x884840: r0 = Quaternion.identity()
    //     0x884840: bl              #0x8869fc  ; [package:vector_math/vector_math_64.dart] Quaternion::Quaternion.identity
    // 0x884844: r1 = Null
    //     0x884844: mov             x1, NULL
    // 0x884848: stur            x0, [fp, #-0x20]
    // 0x88484c: r0 = Quaternion.identity()
    //     0x88484c: bl              #0x8869fc  ; [package:vector_math/vector_math_64.dart] Quaternion::Quaternion.identity
    // 0x884850: stur            x0, [fp, #-0x28]
    // 0x884854: r0 = Vector3()
    //     0x884854: bl              #0x68e1e4  ; AllocateVector3Stub -> Vector3 (size=0xc)
    // 0x884858: r4 = 6
    //     0x884858: movz            x4, #0x6
    // 0x88485c: stur            x0, [fp, #-0x30]
    // 0x884860: r0 = AllocateFloat64Array()
    //     0x884860: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0x884864: ldur            x5, [fp, #-0x30]
    // 0x884868: StoreField: r5->field_7 = r0
    //     0x884868: stur            w0, [x5, #7]
    // 0x88486c: r0 = Vector3()
    //     0x88486c: bl              #0x68e1e4  ; AllocateVector3Stub -> Vector3 (size=0xc)
    // 0x884870: r4 = 6
    //     0x884870: movz            x4, #0x6
    // 0x884874: stur            x0, [fp, #-0x38]
    // 0x884878: r0 = AllocateFloat64Array()
    //     0x884878: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0x88487c: mov             x1, x0
    // 0x884880: ldur            x0, [fp, #-0x38]
    // 0x884884: StoreField: r0->field_7 = r1
    //     0x884884: stur            w1, [x0, #7]
    // 0x884888: ldur            x4, [fp, #-8]
    // 0x88488c: LoadField: r1 = r4->field_b
    //     0x88488c: ldur            w1, [x4, #0xb]
    // 0x884890: DecompressPointer r1
    //     0x884890: add             x1, x1, HEAP, lsl #32
    // 0x884894: cmp             w1, NULL
    // 0x884898: b.eq            #0x884990
    // 0x88489c: ldur            x2, [fp, #-0x10]
    // 0x8848a0: ldur            x3, [fp, #-0x20]
    // 0x8848a4: ldur            x5, [fp, #-0x30]
    // 0x8848a8: r0 = decompose()
    //     0x8848a8: bl              #0x885580  ; [package:vector_math/vector_math_64.dart] Matrix4::decompose
    // 0x8848ac: ldur            x0, [fp, #-8]
    // 0x8848b0: LoadField: r1 = r0->field_f
    //     0x8848b0: ldur            w1, [x0, #0xf]
    // 0x8848b4: DecompressPointer r1
    //     0x8848b4: add             x1, x1, HEAP, lsl #32
    // 0x8848b8: cmp             w1, NULL
    // 0x8848bc: b.eq            #0x884994
    // 0x8848c0: ldur            x2, [fp, #-0x18]
    // 0x8848c4: ldur            x3, [fp, #-0x28]
    // 0x8848c8: ldur            x5, [fp, #-0x38]
    // 0x8848cc: r0 = decompose()
    //     0x8848cc: bl              #0x885580  ; [package:vector_math/vector_math_64.dart] Matrix4::decompose
    // 0x8848d0: ldur            d1, [fp, #-0x40]
    // 0x8848d4: d0 = 1.000000
    //     0x8848d4: fmov            d0, #1.00000000
    // 0x8848d8: fsub            d2, d0, d1
    // 0x8848dc: ldur            x1, [fp, #-0x10]
    // 0x8848e0: mov             v0.16b, v2.16b
    // 0x8848e4: stur            d2, [fp, #-0x48]
    // 0x8848e8: r0 = scaled()
    //     0x8848e8: bl              #0x68dcdc  ; [package:vector_math/vector_math_64.dart] Vector3::scaled
    // 0x8848ec: ldur            x1, [fp, #-0x18]
    // 0x8848f0: ldur            d0, [fp, #-0x40]
    // 0x8848f4: stur            x0, [fp, #-8]
    // 0x8848f8: r0 = scaled()
    //     0x8848f8: bl              #0x68dcdc  ; [package:vector_math/vector_math_64.dart] Vector3::scaled
    // 0x8848fc: ldur            x1, [fp, #-8]
    // 0x884900: mov             x2, x0
    // 0x884904: r0 = +()
    //     0x884904: bl              #0x68daac  ; [package:vector_math/vector_math_64.dart] Vector3::+
    // 0x884908: ldur            x1, [fp, #-0x20]
    // 0x88490c: ldur            d0, [fp, #-0x48]
    // 0x884910: stur            x0, [fp, #-8]
    // 0x884914: r0 = scaled()
    //     0x884914: bl              #0x8854f0  ; [package:vector_math/vector_math_64.dart] Quaternion::scaled
    // 0x884918: ldur            x1, [fp, #-0x28]
    // 0x88491c: ldur            d0, [fp, #-0x40]
    // 0x884920: stur            x0, [fp, #-0x10]
    // 0x884924: r0 = scaled()
    //     0x884924: bl              #0x8854f0  ; [package:vector_math/vector_math_64.dart] Quaternion::scaled
    // 0x884928: ldur            x1, [fp, #-0x10]
    // 0x88492c: mov             x2, x0
    // 0x884930: r0 = +()
    //     0x884930: bl              #0x8853b0  ; [package:vector_math/vector_math_64.dart] Quaternion::+
    // 0x884934: mov             x1, x0
    // 0x884938: r0 = normalized()
    //     0x884938: bl              #0x884d34  ; [package:vector_math/vector_math_64.dart] Quaternion::normalized
    // 0x88493c: ldur            x1, [fp, #-0x30]
    // 0x884940: ldur            d0, [fp, #-0x48]
    // 0x884944: stur            x0, [fp, #-0x10]
    // 0x884948: r0 = scaled()
    //     0x884948: bl              #0x68dcdc  ; [package:vector_math/vector_math_64.dart] Vector3::scaled
    // 0x88494c: ldur            x1, [fp, #-0x38]
    // 0x884950: ldur            d0, [fp, #-0x40]
    // 0x884954: stur            x0, [fp, #-0x18]
    // 0x884958: r0 = scaled()
    //     0x884958: bl              #0x68dcdc  ; [package:vector_math/vector_math_64.dart] Vector3::scaled
    // 0x88495c: ldur            x1, [fp, #-0x18]
    // 0x884960: mov             x2, x0
    // 0x884964: r0 = +()
    //     0x884964: bl              #0x68daac  ; [package:vector_math/vector_math_64.dart] Vector3::+
    // 0x884968: ldur            x2, [fp, #-8]
    // 0x88496c: ldur            x3, [fp, #-0x10]
    // 0x884970: mov             x5, x0
    // 0x884974: r1 = Null
    //     0x884974: mov             x1, NULL
    // 0x884978: r0 = Matrix4.compose()
    //     0x884978: bl              #0x884998  ; [package:vector_math/vector_math_64.dart] Matrix4::Matrix4.compose
    // 0x88497c: LeaveFrame
    //     0x88497c: mov             SP, fp
    //     0x884980: ldp             fp, lr, [SP], #0x10
    // 0x884984: ret
    //     0x884984: ret             
    // 0x884988: r0 = StackOverflowSharedWithFPURegs()
    //     0x884988: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x88498c: b               #0x88480c
    // 0x884990: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x884990: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x884994: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x884994: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3741, size: 0x14, field offset: 0x14
class BorderRadiusTween extends Tween<dynamic> {

  _ lerp(/* No info */) {
    // ** addr: 0x88455c, size: 0x40
    // 0x88455c: EnterFrame
    //     0x88455c: stp             fp, lr, [SP, #-0x10]!
    //     0x884560: mov             fp, SP
    // 0x884564: CheckStackOverflow
    //     0x884564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x884568: cmp             SP, x16
    //     0x88456c: b.ls            #0x884594
    // 0x884570: LoadField: r0 = r1->field_b
    //     0x884570: ldur            w0, [x1, #0xb]
    // 0x884574: DecompressPointer r0
    //     0x884574: add             x0, x0, HEAP, lsl #32
    // 0x884578: LoadField: r2 = r1->field_f
    //     0x884578: ldur            w2, [x1, #0xf]
    // 0x88457c: DecompressPointer r2
    //     0x88457c: add             x2, x2, HEAP, lsl #32
    // 0x884580: mov             x1, x0
    // 0x884584: r0 = lerp()
    //     0x884584: bl              #0x8845c8  ; [package:flutter/src/painting/border_radius.dart] BorderRadius::lerp
    // 0x884588: LeaveFrame
    //     0x884588: mov             SP, fp
    //     0x88458c: ldp             fp, lr, [SP], #0x10
    // 0x884590: ret
    //     0x884590: ret             
    // 0x884594: r0 = StackOverflowSharedWithFPURegs()
    //     0x884594: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x884598: b               #0x884570
  }
}

// class id: 3742, size: 0x14, field offset: 0x14
class EdgeInsetsGeometryTween extends Tween<dynamic> {

  _ lerp(/* No info */) {
    // ** addr: 0x8844cc, size: 0x90
    // 0x8844cc: EnterFrame
    //     0x8844cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8844d0: mov             fp, SP
    // 0x8844d4: CheckStackOverflow
    //     0x8844d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8844d8: cmp             SP, x16
    //     0x8844dc: b.ls            #0x884534
    // 0x8844e0: LoadField: r0 = r1->field_b
    //     0x8844e0: ldur            w0, [x1, #0xb]
    // 0x8844e4: DecompressPointer r0
    //     0x8844e4: add             x0, x0, HEAP, lsl #32
    // 0x8844e8: LoadField: r2 = r1->field_f
    //     0x8844e8: ldur            w2, [x1, #0xf]
    // 0x8844ec: DecompressPointer r2
    //     0x8844ec: add             x2, x2, HEAP, lsl #32
    // 0x8844f0: r3 = inline_Allocate_Double()
    //     0x8844f0: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0x8844f4: add             x3, x3, #0x10
    //     0x8844f8: cmp             x1, x3
    //     0x8844fc: b.ls            #0x88453c
    //     0x884500: str             x3, [THR, #0x50]  ; THR::top
    //     0x884504: sub             x3, x3, #0xf
    //     0x884508: movz            x1, #0xe15c
    //     0x88450c: movk            x1, #0x3, lsl #16
    //     0x884510: stur            x1, [x3, #-1]
    // 0x884514: StoreField: r3->field_7 = d0
    //     0x884514: stur            d0, [x3, #7]
    // 0x884518: mov             x1, x0
    // 0x88451c: r0 = lerp()
    //     0x88451c: bl              #0x876de0  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::lerp
    // 0x884520: cmp             w0, NULL
    // 0x884524: b.eq            #0x884558
    // 0x884528: LeaveFrame
    //     0x884528: mov             SP, fp
    //     0x88452c: ldp             fp, lr, [SP], #0x10
    // 0x884530: ret
    //     0x884530: ret             
    // 0x884534: r0 = StackOverflowSharedWithFPURegs()
    //     0x884534: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x884538: b               #0x8844e0
    // 0x88453c: SaveReg d0
    //     0x88453c: str             q0, [SP, #-0x10]!
    // 0x884540: stp             x0, x2, [SP, #-0x10]!
    // 0x884544: r0 = AllocateDouble()
    //     0x884544: bl              #0xec2254  ; AllocateDoubleStub
    // 0x884548: mov             x3, x0
    // 0x88454c: ldp             x0, x2, [SP], #0x10
    // 0x884550: RestoreReg d0
    //     0x884550: ldr             q0, [SP], #0x10
    // 0x884554: b               #0x884514
    // 0x884558: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x884558: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3743, size: 0x14, field offset: 0x14
class EdgeInsetsTween extends Tween<dynamic> {

  _ lerp(/* No info */) {
    // ** addr: 0x884480, size: 0x4c
    // 0x884480: EnterFrame
    //     0x884480: stp             fp, lr, [SP, #-0x10]!
    //     0x884484: mov             fp, SP
    // 0x884488: CheckStackOverflow
    //     0x884488: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88448c: cmp             SP, x16
    //     0x884490: b.ls            #0x8844c0
    // 0x884494: LoadField: r0 = r1->field_b
    //     0x884494: ldur            w0, [x1, #0xb]
    // 0x884498: DecompressPointer r0
    //     0x884498: add             x0, x0, HEAP, lsl #32
    // 0x88449c: LoadField: r2 = r1->field_f
    //     0x88449c: ldur            w2, [x1, #0xf]
    // 0x8844a0: DecompressPointer r2
    //     0x8844a0: add             x2, x2, HEAP, lsl #32
    // 0x8844a4: mov             x1, x0
    // 0x8844a8: r0 = lerp()
    //     0x8844a8: bl              #0x6a9fdc  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsets::lerp
    // 0x8844ac: cmp             w0, NULL
    // 0x8844b0: b.eq            #0x8844c8
    // 0x8844b4: LeaveFrame
    //     0x8844b4: mov             SP, fp
    //     0x8844b8: ldp             fp, lr, [SP], #0x10
    // 0x8844bc: ret
    //     0x8844bc: ret             
    // 0x8844c0: r0 = StackOverflowSharedWithFPURegs()
    //     0x8844c0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x8844c4: b               #0x884494
    // 0x8844c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8844c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3744, size: 0x14, field offset: 0x14
class DecorationTween extends Tween<dynamic> {

  _ lerp(/* No info */) {
    // ** addr: 0x884434, size: 0x4c
    // 0x884434: EnterFrame
    //     0x884434: stp             fp, lr, [SP, #-0x10]!
    //     0x884438: mov             fp, SP
    // 0x88443c: CheckStackOverflow
    //     0x88443c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x884440: cmp             SP, x16
    //     0x884444: b.ls            #0x884474
    // 0x884448: LoadField: r0 = r1->field_b
    //     0x884448: ldur            w0, [x1, #0xb]
    // 0x88444c: DecompressPointer r0
    //     0x88444c: add             x0, x0, HEAP, lsl #32
    // 0x884450: LoadField: r2 = r1->field_f
    //     0x884450: ldur            w2, [x1, #0xf]
    // 0x884454: DecompressPointer r2
    //     0x884454: add             x2, x2, HEAP, lsl #32
    // 0x884458: mov             x1, x0
    // 0x88445c: r0 = lerp()
    //     0x88445c: bl              #0x87be38  ; [package:flutter/src/painting/decoration.dart] Decoration::lerp
    // 0x884460: cmp             w0, NULL
    // 0x884464: b.eq            #0x88447c
    // 0x884468: LeaveFrame
    //     0x884468: mov             SP, fp
    //     0x88446c: ldp             fp, lr, [SP], #0x10
    // 0x884470: ret
    //     0x884470: ret             
    // 0x884474: r0 = StackOverflowSharedWithFPURegs()
    //     0x884474: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x884478: b               #0x884448
    // 0x88447c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x88447c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3745, size: 0x14, field offset: 0x14
class BoxConstraintsTween extends Tween<dynamic> {

  _ lerp(/* No info */) {
    // ** addr: 0x8843e8, size: 0x4c
    // 0x8843e8: EnterFrame
    //     0x8843e8: stp             fp, lr, [SP, #-0x10]!
    //     0x8843ec: mov             fp, SP
    // 0x8843f0: CheckStackOverflow
    //     0x8843f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8843f4: cmp             SP, x16
    //     0x8843f8: b.ls            #0x884428
    // 0x8843fc: LoadField: r0 = r1->field_b
    //     0x8843fc: ldur            w0, [x1, #0xb]
    // 0x884400: DecompressPointer r0
    //     0x884400: add             x0, x0, HEAP, lsl #32
    // 0x884404: LoadField: r2 = r1->field_f
    //     0x884404: ldur            w2, [x1, #0xf]
    // 0x884408: DecompressPointer r2
    //     0x884408: add             x2, x2, HEAP, lsl #32
    // 0x88440c: mov             x1, x0
    // 0x884410: r0 = lerp()
    //     0x884410: bl              #0x87ff34  ; [package:flutter/src/rendering/box.dart] BoxConstraints::lerp
    // 0x884414: cmp             w0, NULL
    // 0x884418: b.eq            #0x884430
    // 0x88441c: LeaveFrame
    //     0x88441c: mov             SP, fp
    //     0x884420: ldp             fp, lr, [SP], #0x10
    // 0x884424: ret
    //     0x884424: ret             
    // 0x884428: r0 = StackOverflowSharedWithFPURegs()
    //     0x884428: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x88442c: b               #0x8843fc
    // 0x884430: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x884430: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4286, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class _ImplicitlyAnimatedWidgetState&State&SingleTickerProviderStateMixin<X0 bound ImplicitlyAnimatedWidget> extends State<X0 bound ImplicitlyAnimatedWidget>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6f49c8, size: 0x98
    // 0x6f49c8: EnterFrame
    //     0x6f49c8: stp             fp, lr, [SP, #-0x10]!
    //     0x6f49cc: mov             fp, SP
    // 0x6f49d0: AllocStack(0x10)
    //     0x6f49d0: sub             SP, SP, #0x10
    // 0x6f49d4: SetupParameters(_ImplicitlyAnimatedWidgetState&State&SingleTickerProviderStateMixin<X0 bound ImplicitlyAnimatedWidget> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6f49d4: stur            x1, [fp, #-8]
    //     0x6f49d8: stur            x2, [fp, #-0x10]
    // 0x6f49dc: CheckStackOverflow
    //     0x6f49dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f49e0: cmp             SP, x16
    //     0x6f49e4: b.ls            #0x6f4a54
    // 0x6f49e8: r0 = Ticker()
    //     0x6f49e8: bl              #0x6efe64  ; AllocateTickerStub -> Ticker (size=0x1c)
    // 0x6f49ec: mov             x1, x0
    // 0x6f49f0: r0 = false
    //     0x6f49f0: add             x0, NULL, #0x30  ; false
    // 0x6f49f4: StoreField: r1->field_b = r0
    //     0x6f49f4: stur            w0, [x1, #0xb]
    // 0x6f49f8: ldur            x0, [fp, #-0x10]
    // 0x6f49fc: StoreField: r1->field_13 = r0
    //     0x6f49fc: stur            w0, [x1, #0x13]
    // 0x6f4a00: mov             x0, x1
    // 0x6f4a04: ldur            x2, [fp, #-8]
    // 0x6f4a08: StoreField: r2->field_13 = r0
    //     0x6f4a08: stur            w0, [x2, #0x13]
    //     0x6f4a0c: ldurb           w16, [x2, #-1]
    //     0x6f4a10: ldurb           w17, [x0, #-1]
    //     0x6f4a14: and             x16, x17, x16, lsr #2
    //     0x6f4a18: tst             x16, HEAP, lsr #32
    //     0x6f4a1c: b.eq            #0x6f4a24
    //     0x6f4a20: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6f4a24: mov             x1, x2
    // 0x6f4a28: r0 = _updateTickerModeNotifier()
    //     0x6f4a28: bl              #0x6f4a60  ; [package:flutter/src/widgets/implicit_animations.dart] _ImplicitlyAnimatedWidgetState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6f4a2c: ldur            x1, [fp, #-8]
    // 0x6f4a30: r0 = _updateTicker()
    //     0x6f4a30: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6f4a34: ldur            x1, [fp, #-8]
    // 0x6f4a38: LoadField: r0 = r1->field_13
    //     0x6f4a38: ldur            w0, [x1, #0x13]
    // 0x6f4a3c: DecompressPointer r0
    //     0x6f4a3c: add             x0, x0, HEAP, lsl #32
    // 0x6f4a40: cmp             w0, NULL
    // 0x6f4a44: b.eq            #0x6f4a5c
    // 0x6f4a48: LeaveFrame
    //     0x6f4a48: mov             SP, fp
    //     0x6f4a4c: ldp             fp, lr, [SP], #0x10
    // 0x6f4a50: ret
    //     0x6f4a50: ret             
    // 0x6f4a54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f4a54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f4a58: b               #0x6f49e8
    // 0x6f4a5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f4a5c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6f4a60, size: 0x124
    // 0x6f4a60: EnterFrame
    //     0x6f4a60: stp             fp, lr, [SP, #-0x10]!
    //     0x6f4a64: mov             fp, SP
    // 0x6f4a68: AllocStack(0x18)
    //     0x6f4a68: sub             SP, SP, #0x18
    // 0x6f4a6c: SetupParameters(_ImplicitlyAnimatedWidgetState&State&SingleTickerProviderStateMixin<X0 bound ImplicitlyAnimatedWidget> this /* r1 => r2, fp-0x8 */)
    //     0x6f4a6c: mov             x2, x1
    //     0x6f4a70: stur            x1, [fp, #-8]
    // 0x6f4a74: CheckStackOverflow
    //     0x6f4a74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f4a78: cmp             SP, x16
    //     0x6f4a7c: b.ls            #0x6f4b78
    // 0x6f4a80: LoadField: r1 = r2->field_f
    //     0x6f4a80: ldur            w1, [x2, #0xf]
    // 0x6f4a84: DecompressPointer r1
    //     0x6f4a84: add             x1, x1, HEAP, lsl #32
    // 0x6f4a88: cmp             w1, NULL
    // 0x6f4a8c: b.eq            #0x6f4b80
    // 0x6f4a90: r0 = getNotifier()
    //     0x6f4a90: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6f4a94: mov             x3, x0
    // 0x6f4a98: ldur            x0, [fp, #-8]
    // 0x6f4a9c: stur            x3, [fp, #-0x18]
    // 0x6f4aa0: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6f4aa0: ldur            w4, [x0, #0x17]
    // 0x6f4aa4: DecompressPointer r4
    //     0x6f4aa4: add             x4, x4, HEAP, lsl #32
    // 0x6f4aa8: stur            x4, [fp, #-0x10]
    // 0x6f4aac: cmp             w3, w4
    // 0x6f4ab0: b.ne            #0x6f4ac4
    // 0x6f4ab4: r0 = Null
    //     0x6f4ab4: mov             x0, NULL
    // 0x6f4ab8: LeaveFrame
    //     0x6f4ab8: mov             SP, fp
    //     0x6f4abc: ldp             fp, lr, [SP], #0x10
    // 0x6f4ac0: ret
    //     0x6f4ac0: ret             
    // 0x6f4ac4: cmp             w4, NULL
    // 0x6f4ac8: b.eq            #0x6f4b0c
    // 0x6f4acc: mov             x2, x0
    // 0x6f4ad0: r1 = Function '_updateTicker@364311458':.
    //     0x6f4ad0: add             x1, PP, #0x46, lsl #12  ; [pp+0x460d8] AnonymousClosure: (0x6f4b84), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6f4ad4: ldr             x1, [x1, #0xd8]
    // 0x6f4ad8: r0 = AllocateClosure()
    //     0x6f4ad8: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f4adc: ldur            x1, [fp, #-0x10]
    // 0x6f4ae0: r2 = LoadClassIdInstr(r1)
    //     0x6f4ae0: ldur            x2, [x1, #-1]
    //     0x6f4ae4: ubfx            x2, x2, #0xc, #0x14
    // 0x6f4ae8: mov             x16, x0
    // 0x6f4aec: mov             x0, x2
    // 0x6f4af0: mov             x2, x16
    // 0x6f4af4: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6f4af4: movz            x17, #0xbf5c
    //     0x6f4af8: add             lr, x0, x17
    //     0x6f4afc: ldr             lr, [x21, lr, lsl #3]
    //     0x6f4b00: blr             lr
    // 0x6f4b04: ldur            x0, [fp, #-8]
    // 0x6f4b08: ldur            x3, [fp, #-0x18]
    // 0x6f4b0c: mov             x2, x0
    // 0x6f4b10: r1 = Function '_updateTicker@364311458':.
    //     0x6f4b10: add             x1, PP, #0x46, lsl #12  ; [pp+0x460d8] AnonymousClosure: (0x6f4b84), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6f4b14: ldr             x1, [x1, #0xd8]
    // 0x6f4b18: r0 = AllocateClosure()
    //     0x6f4b18: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f4b1c: ldur            x3, [fp, #-0x18]
    // 0x6f4b20: r1 = LoadClassIdInstr(r3)
    //     0x6f4b20: ldur            x1, [x3, #-1]
    //     0x6f4b24: ubfx            x1, x1, #0xc, #0x14
    // 0x6f4b28: mov             x2, x0
    // 0x6f4b2c: mov             x0, x1
    // 0x6f4b30: mov             x1, x3
    // 0x6f4b34: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6f4b34: movz            x17, #0xc407
    //     0x6f4b38: add             lr, x0, x17
    //     0x6f4b3c: ldr             lr, [x21, lr, lsl #3]
    //     0x6f4b40: blr             lr
    // 0x6f4b44: ldur            x0, [fp, #-0x18]
    // 0x6f4b48: ldur            x1, [fp, #-8]
    // 0x6f4b4c: ArrayStore: r1[0] = r0  ; List_4
    //     0x6f4b4c: stur            w0, [x1, #0x17]
    //     0x6f4b50: ldurb           w16, [x1, #-1]
    //     0x6f4b54: ldurb           w17, [x0, #-1]
    //     0x6f4b58: and             x16, x17, x16, lsr #2
    //     0x6f4b5c: tst             x16, HEAP, lsr #32
    //     0x6f4b60: b.eq            #0x6f4b68
    //     0x6f4b64: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6f4b68: r0 = Null
    //     0x6f4b68: mov             x0, NULL
    // 0x6f4b6c: LeaveFrame
    //     0x6f4b6c: mov             SP, fp
    //     0x6f4b70: ldp             fp, lr, [SP], #0x10
    // 0x6f4b74: ret
    //     0x6f4b74: ret             
    // 0x6f4b78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f4b78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f4b7c: b               #0x6f4a80
    // 0x6f4b80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f4b80: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTicker(dynamic) {
    // ** addr: 0x6f4b84, size: 0x38
    // 0x6f4b84: EnterFrame
    //     0x6f4b84: stp             fp, lr, [SP, #-0x10]!
    //     0x6f4b88: mov             fp, SP
    // 0x6f4b8c: ldr             x0, [fp, #0x10]
    // 0x6f4b90: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6f4b90: ldur            w1, [x0, #0x17]
    // 0x6f4b94: DecompressPointer r1
    //     0x6f4b94: add             x1, x1, HEAP, lsl #32
    // 0x6f4b98: CheckStackOverflow
    //     0x6f4b98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f4b9c: cmp             SP, x16
    //     0x6f4ba0: b.ls            #0x6f4bb4
    // 0x6f4ba4: r0 = _updateTicker()
    //     0x6f4ba4: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6f4ba8: LeaveFrame
    //     0x6f4ba8: mov             SP, fp
    //     0x6f4bac: ldp             fp, lr, [SP], #0x10
    // 0x6f4bb0: ret
    //     0x6f4bb0: ret             
    // 0x6f4bb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f4bb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f4bb8: b               #0x6f4ba4
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7dcac, size: 0x94
    // 0xa7dcac: EnterFrame
    //     0xa7dcac: stp             fp, lr, [SP, #-0x10]!
    //     0xa7dcb0: mov             fp, SP
    // 0xa7dcb4: AllocStack(0x10)
    //     0xa7dcb4: sub             SP, SP, #0x10
    // 0xa7dcb8: SetupParameters(_ImplicitlyAnimatedWidgetState&State&SingleTickerProviderStateMixin<X0 bound ImplicitlyAnimatedWidget> this /* r1 => r0, fp-0x10 */)
    //     0xa7dcb8: mov             x0, x1
    //     0xa7dcbc: stur            x1, [fp, #-0x10]
    // 0xa7dcc0: CheckStackOverflow
    //     0xa7dcc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7dcc4: cmp             SP, x16
    //     0xa7dcc8: b.ls            #0xa7dd38
    // 0xa7dccc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa7dccc: ldur            w3, [x0, #0x17]
    // 0xa7dcd0: DecompressPointer r3
    //     0xa7dcd0: add             x3, x3, HEAP, lsl #32
    // 0xa7dcd4: stur            x3, [fp, #-8]
    // 0xa7dcd8: cmp             w3, NULL
    // 0xa7dcdc: b.ne            #0xa7dce8
    // 0xa7dce0: mov             x1, x0
    // 0xa7dce4: b               #0xa7dd24
    // 0xa7dce8: mov             x2, x0
    // 0xa7dcec: r1 = Function '_updateTicker@364311458':.
    //     0xa7dcec: add             x1, PP, #0x46, lsl #12  ; [pp+0x460d8] AnonymousClosure: (0x6f4b84), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0xa7dcf0: ldr             x1, [x1, #0xd8]
    // 0xa7dcf4: r0 = AllocateClosure()
    //     0xa7dcf4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa7dcf8: ldur            x1, [fp, #-8]
    // 0xa7dcfc: r2 = LoadClassIdInstr(r1)
    //     0xa7dcfc: ldur            x2, [x1, #-1]
    //     0xa7dd00: ubfx            x2, x2, #0xc, #0x14
    // 0xa7dd04: mov             x16, x0
    // 0xa7dd08: mov             x0, x2
    // 0xa7dd0c: mov             x2, x16
    // 0xa7dd10: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa7dd10: movz            x17, #0xbf5c
    //     0xa7dd14: add             lr, x0, x17
    //     0xa7dd18: ldr             lr, [x21, lr, lsl #3]
    //     0xa7dd1c: blr             lr
    // 0xa7dd20: ldur            x1, [fp, #-0x10]
    // 0xa7dd24: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa7dd24: stur            NULL, [x1, #0x17]
    // 0xa7dd28: r0 = Null
    //     0xa7dd28: mov             x0, NULL
    // 0xa7dd2c: LeaveFrame
    //     0xa7dd2c: mov             SP, fp
    //     0xa7dd30: ldp             fp, lr, [SP], #0x10
    // 0xa7dd34: ret
    //     0xa7dd34: ret             
    // 0xa7dd38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7dd38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7dd3c: b               #0xa7dccc
  }
  _ activate(/* No info */) {
    // ** addr: 0xa84fec, size: 0x48
    // 0xa84fec: EnterFrame
    //     0xa84fec: stp             fp, lr, [SP, #-0x10]!
    //     0xa84ff0: mov             fp, SP
    // 0xa84ff4: AllocStack(0x8)
    //     0xa84ff4: sub             SP, SP, #8
    // 0xa84ff8: SetupParameters(_ImplicitlyAnimatedWidgetState&State&SingleTickerProviderStateMixin<X0 bound ImplicitlyAnimatedWidget> this /* r1 => r0, fp-0x8 */)
    //     0xa84ff8: mov             x0, x1
    //     0xa84ffc: stur            x1, [fp, #-8]
    // 0xa85000: CheckStackOverflow
    //     0xa85000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85004: cmp             SP, x16
    //     0xa85008: b.ls            #0xa8502c
    // 0xa8500c: mov             x1, x0
    // 0xa85010: r0 = _updateTickerModeNotifier()
    //     0xa85010: bl              #0x6f4a60  ; [package:flutter/src/widgets/implicit_animations.dart] _ImplicitlyAnimatedWidgetState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa85014: ldur            x1, [fp, #-8]
    // 0xa85018: r0 = _updateTicker()
    //     0xa85018: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0xa8501c: r0 = Null
    //     0xa8501c: mov             x0, NULL
    // 0xa85020: LeaveFrame
    //     0xa85020: mov             SP, fp
    //     0xa85024: ldp             fp, lr, [SP], #0x10
    // 0xa85028: ret
    //     0xa85028: ret             
    // 0xa8502c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8502c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85030: b               #0xa8500c
  }
}

// class id: 4287, size: 0x24, field offset: 0x1c
abstract class ImplicitlyAnimatedWidgetState<X0 bound ImplicitlyAnimatedWidget> extends _ImplicitlyAnimatedWidgetState&State&SingleTickerProviderStateMixin<X0 bound ImplicitlyAnimatedWidget> {

  late CurvedAnimation _animation; // offset: 0x20
  late final AnimationController _controller; // offset: 0x1c

  AnimationController _controller(ImplicitlyAnimatedWidgetState<X0>) {
    // ** addr: 0x93663c, size: 0x7c
    // 0x93663c: EnterFrame
    //     0x93663c: stp             fp, lr, [SP, #-0x10]!
    //     0x936640: mov             fp, SP
    // 0x936644: AllocStack(0x18)
    //     0x936644: sub             SP, SP, #0x18
    // 0x936648: CheckStackOverflow
    //     0x936648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93664c: cmp             SP, x16
    //     0x936650: b.ls            #0x9366ac
    // 0x936654: ldr             x2, [fp, #0x10]
    // 0x936658: LoadField: r0 = r2->field_b
    //     0x936658: ldur            w0, [x2, #0xb]
    // 0x93665c: DecompressPointer r0
    //     0x93665c: add             x0, x0, HEAP, lsl #32
    // 0x936660: cmp             w0, NULL
    // 0x936664: b.eq            #0x9366b4
    // 0x936668: LoadField: r3 = r0->field_f
    //     0x936668: ldur            w3, [x0, #0xf]
    // 0x93666c: DecompressPointer r3
    //     0x93666c: add             x3, x3, HEAP, lsl #32
    // 0x936670: stur            x3, [fp, #-8]
    // 0x936674: r1 = <double>
    //     0x936674: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x936678: r0 = AnimationController()
    //     0x936678: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x93667c: stur            x0, [fp, #-0x10]
    // 0x936680: ldur            x16, [fp, #-8]
    // 0x936684: str             x16, [SP]
    // 0x936688: mov             x1, x0
    // 0x93668c: ldr             x2, [fp, #0x10]
    // 0x936690: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x936690: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x936694: ldr             x4, [x4, #0x408]
    // 0x936698: r0 = AnimationController()
    //     0x936698: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x93669c: ldur            x0, [fp, #-0x10]
    // 0x9366a0: LeaveFrame
    //     0x9366a0: mov             SP, fp
    //     0x9366a4: ldp             fp, lr, [SP], #0x10
    // 0x9366a8: ret
    //     0x9366a8: ret             
    // 0x9366ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9366ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9366b0: b               #0x936654
    // 0x9366b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9366b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x9366b8, size: 0xb4
    // 0x9366b8: EnterFrame
    //     0x9366b8: stp             fp, lr, [SP, #-0x10]!
    //     0x9366bc: mov             fp, SP
    // 0x9366c0: AllocStack(0x10)
    //     0x9366c0: sub             SP, SP, #0x10
    // 0x9366c4: SetupParameters(ImplicitlyAnimatedWidgetState<X0 bound ImplicitlyAnimatedWidget> this /* r1 => r1, fp-0x8 */)
    //     0x9366c4: stur            x1, [fp, #-8]
    // 0x9366c8: CheckStackOverflow
    //     0x9366c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9366cc: cmp             SP, x16
    //     0x9366d0: b.ls            #0x936764
    // 0x9366d4: r1 = 1
    //     0x9366d4: movz            x1, #0x1
    // 0x9366d8: r0 = AllocateContext()
    //     0x9366d8: bl              #0xec126c  ; AllocateContextStub
    // 0x9366dc: mov             x2, x0
    // 0x9366e0: ldur            x0, [fp, #-8]
    // 0x9366e4: stur            x2, [fp, #-0x10]
    // 0x9366e8: StoreField: r2->field_f = r0
    //     0x9366e8: stur            w0, [x2, #0xf]
    // 0x9366ec: mov             x1, x0
    // 0x9366f0: LoadField: r0 = r1->field_1b
    //     0x9366f0: ldur            w0, [x1, #0x1b]
    // 0x9366f4: DecompressPointer r0
    //     0x9366f4: add             x0, x0, HEAP, lsl #32
    // 0x9366f8: r16 = Sentinel
    //     0x9366f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9366fc: cmp             w0, w16
    // 0x936700: b.ne            #0x936710
    // 0x936704: r2 = _controller
    //     0x936704: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f8] Field <ImplicitlyAnimatedWidgetState._controller@291443363>: late final (offset: 0x1c)
    //     0x936708: ldr             x2, [x2, #0xf8]
    // 0x93670c: r0 = InitLateFinalInstanceField()
    //     0x93670c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x936710: ldur            x2, [fp, #-0x10]
    // 0x936714: r1 = Function '<anonymous closure>':.
    //     0x936714: add             x1, PP, #0x46, lsl #12  ; [pp+0x46138] AnonymousClosure: (0x93695c), in [package:flutter/src/widgets/implicit_animations.dart] ImplicitlyAnimatedWidgetState::initState (0x9366b8)
    //     0x936718: ldr             x1, [x1, #0x138]
    // 0x93671c: stur            x0, [fp, #-0x10]
    // 0x936720: r0 = AllocateClosure()
    //     0x936720: bl              #0xec1630  ; AllocateClosureStub
    // 0x936724: ldur            x1, [fp, #-0x10]
    // 0x936728: mov             x2, x0
    // 0x93672c: r0 = addStatusListener()
    //     0x93672c: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0x936730: ldur            x1, [fp, #-8]
    // 0x936734: r0 = _constructTweens()
    //     0x936734: bl              #0x93676c  ; [package:flutter/src/widgets/implicit_animations.dart] ImplicitlyAnimatedWidgetState::_constructTweens
    // 0x936738: ldur            x1, [fp, #-8]
    // 0x93673c: r0 = LoadClassIdInstr(r1)
    //     0x93673c: ldur            x0, [x1, #-1]
    //     0x936740: ubfx            x0, x0, #0xc, #0x14
    // 0x936744: r0 = GDT[cid_x0 + 0x2247]()
    //     0x936744: movz            x17, #0x2247
    //     0x936748: add             lr, x0, x17
    //     0x93674c: ldr             lr, [x21, lr, lsl #3]
    //     0x936750: blr             lr
    // 0x936754: r0 = Null
    //     0x936754: mov             x0, NULL
    // 0x936758: LeaveFrame
    //     0x936758: mov             SP, fp
    //     0x93675c: ldp             fp, lr, [SP], #0x10
    // 0x936760: ret
    //     0x936760: ret             
    // 0x936764: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x936764: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x936768: b               #0x9366d4
  }
  _ _constructTweens(/* No info */) {
    // ** addr: 0x93676c, size: 0x94
    // 0x93676c: EnterFrame
    //     0x93676c: stp             fp, lr, [SP, #-0x10]!
    //     0x936770: mov             fp, SP
    // 0x936774: AllocStack(0x10)
    //     0x936774: sub             SP, SP, #0x10
    // 0x936778: SetupParameters(ImplicitlyAnimatedWidgetState<X0 bound ImplicitlyAnimatedWidget> this /* r1 => r1, fp-0x8 */)
    //     0x936778: stur            x1, [fp, #-8]
    // 0x93677c: CheckStackOverflow
    //     0x93677c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x936780: cmp             SP, x16
    //     0x936784: b.ls            #0x9367f8
    // 0x936788: r1 = 2
    //     0x936788: movz            x1, #0x2
    // 0x93678c: r0 = AllocateContext()
    //     0x93678c: bl              #0xec126c  ; AllocateContextStub
    // 0x936790: mov             x3, x0
    // 0x936794: ldur            x0, [fp, #-8]
    // 0x936798: stur            x3, [fp, #-0x10]
    // 0x93679c: StoreField: r3->field_f = r0
    //     0x93679c: stur            w0, [x3, #0xf]
    // 0x9367a0: r1 = false
    //     0x9367a0: add             x1, NULL, #0x30  ; false
    // 0x9367a4: StoreField: r3->field_13 = r1
    //     0x9367a4: stur            w1, [x3, #0x13]
    // 0x9367a8: mov             x2, x3
    // 0x9367ac: r1 = Function '<anonymous closure>':.
    //     0x9367ac: add             x1, PP, #0x46, lsl #12  ; [pp+0x46130] AnonymousClosure: (0x936800), in [package:flutter/src/widgets/implicit_animations.dart] ImplicitlyAnimatedWidgetState::_constructTweens (0x93676c)
    //     0x9367b0: ldr             x1, [x1, #0x130]
    // 0x9367b4: r0 = AllocateClosure()
    //     0x9367b4: bl              #0xec1630  ; AllocateClosureStub
    // 0x9367b8: ldur            x1, [fp, #-8]
    // 0x9367bc: r2 = LoadClassIdInstr(r1)
    //     0x9367bc: ldur            x2, [x1, #-1]
    //     0x9367c0: ubfx            x2, x2, #0xc, #0x14
    // 0x9367c4: mov             x16, x0
    // 0x9367c8: mov             x0, x2
    // 0x9367cc: mov             x2, x16
    // 0x9367d0: r0 = GDT[cid_x0 + 0x15f9]()
    //     0x9367d0: movz            x17, #0x15f9
    //     0x9367d4: add             lr, x0, x17
    //     0x9367d8: ldr             lr, [x21, lr, lsl #3]
    //     0x9367dc: blr             lr
    // 0x9367e0: ldur            x1, [fp, #-0x10]
    // 0x9367e4: LoadField: r0 = r1->field_13
    //     0x9367e4: ldur            w0, [x1, #0x13]
    // 0x9367e8: DecompressPointer r0
    //     0x9367e8: add             x0, x0, HEAP, lsl #32
    // 0x9367ec: LeaveFrame
    //     0x9367ec: mov             SP, fp
    //     0x9367f0: ldp             fp, lr, [SP], #0x10
    // 0x9367f4: ret
    //     0x9367f4: ret             
    // 0x9367f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9367f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9367fc: b               #0x936788
  }
  [closure] Tween<dynamic>? <anonymous closure>(dynamic, Tween<dynamic>?, dynamic, (dynamic, dynamic) => Tween<dynamic>) {
    // ** addr: 0x936800, size: 0xec
    // 0x936800: EnterFrame
    //     0x936800: stp             fp, lr, [SP, #-0x10]!
    //     0x936804: mov             fp, SP
    // 0x936808: AllocStack(0x20)
    //     0x936808: sub             SP, SP, #0x20
    // 0x93680c: SetupParameters()
    //     0x93680c: ldr             x0, [fp, #0x28]
    //     0x936810: ldur            w1, [x0, #0x17]
    //     0x936814: add             x1, x1, HEAP, lsl #32
    //     0x936818: stur            x1, [fp, #-8]
    // 0x93681c: CheckStackOverflow
    //     0x93681c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x936820: cmp             SP, x16
    //     0x936824: b.ls            #0x9368e4
    // 0x936828: ldr             x3, [fp, #0x18]
    // 0x93682c: cmp             w3, NULL
    // 0x936830: b.eq            #0x9368d4
    // 0x936834: ldr             x0, [fp, #0x20]
    // 0x936838: cmp             w0, NULL
    // 0x93683c: b.ne            #0x936860
    // 0x936840: ldr             x16, [fp, #0x10]
    // 0x936844: stp             x3, x16, [SP]
    // 0x936848: ldr             x0, [fp, #0x10]
    // 0x93684c: ClosureCall
    //     0x93684c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x936850: ldur            x2, [x0, #0x1f]
    //     0x936854: blr             x2
    // 0x936858: mov             x4, x0
    // 0x93685c: b               #0x936864
    // 0x936860: mov             x4, x0
    // 0x936864: ldur            x0, [fp, #-8]
    // 0x936868: stur            x4, [fp, #-0x10]
    // 0x93686c: LoadField: r1 = r0->field_f
    //     0x93686c: ldur            w1, [x0, #0xf]
    // 0x936870: DecompressPointer r1
    //     0x936870: add             x1, x1, HEAP, lsl #32
    // 0x936874: mov             x2, x4
    // 0x936878: ldr             x3, [fp, #0x18]
    // 0x93687c: r0 = _shouldAnimateTween()
    //     0x93687c: bl              #0x9368ec  ; [package:flutter/src/widgets/implicit_animations.dart] ImplicitlyAnimatedWidgetState::_shouldAnimateTween
    // 0x936880: tbnz            w0, #4, #0x936894
    // 0x936884: ldur            x0, [fp, #-8]
    // 0x936888: r1 = true
    //     0x936888: add             x1, NULL, #0x20  ; true
    // 0x93688c: StoreField: r0->field_13 = r1
    //     0x93688c: stur            w1, [x0, #0x13]
    // 0x936890: b               #0x9368cc
    // 0x936894: ldur            x3, [fp, #-0x10]
    // 0x936898: LoadField: r0 = r3->field_f
    //     0x936898: ldur            w0, [x3, #0xf]
    // 0x93689c: DecompressPointer r0
    //     0x93689c: add             x0, x0, HEAP, lsl #32
    // 0x9368a0: cmp             w0, NULL
    // 0x9368a4: b.ne            #0x9368cc
    // 0x9368a8: LoadField: r2 = r3->field_b
    //     0x9368a8: ldur            w2, [x3, #0xb]
    // 0x9368ac: DecompressPointer r2
    //     0x9368ac: add             x2, x2, HEAP, lsl #32
    // 0x9368b0: r0 = LoadClassIdInstr(r3)
    //     0x9368b0: ldur            x0, [x3, #-1]
    //     0x9368b4: ubfx            x0, x0, #0xc, #0x14
    // 0x9368b8: mov             x1, x3
    // 0x9368bc: r0 = GDT[cid_x0 + 0x1933]()
    //     0x9368bc: movz            x17, #0x1933
    //     0x9368c0: add             lr, x0, x17
    //     0x9368c4: ldr             lr, [x21, lr, lsl #3]
    //     0x9368c8: blr             lr
    // 0x9368cc: ldur            x0, [fp, #-0x10]
    // 0x9368d0: b               #0x9368d8
    // 0x9368d4: r0 = Null
    //     0x9368d4: mov             x0, NULL
    // 0x9368d8: LeaveFrame
    //     0x9368d8: mov             SP, fp
    //     0x9368dc: ldp             fp, lr, [SP], #0x10
    // 0x9368e0: ret
    //     0x9368e0: ret             
    // 0x9368e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9368e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9368e8: b               #0x936828
  }
  _ _shouldAnimateTween(/* No info */) {
    // ** addr: 0x9368ec, size: 0x70
    // 0x9368ec: EnterFrame
    //     0x9368ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9368f0: mov             fp, SP
    // 0x9368f4: AllocStack(0x10)
    //     0x9368f4: sub             SP, SP, #0x10
    // 0x9368f8: CheckStackOverflow
    //     0x9368f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9368fc: cmp             SP, x16
    //     0x936900: b.ls            #0x936954
    // 0x936904: LoadField: r0 = r2->field_f
    //     0x936904: ldur            w0, [x2, #0xf]
    // 0x936908: DecompressPointer r0
    //     0x936908: add             x0, x0, HEAP, lsl #32
    // 0x93690c: cmp             w0, NULL
    // 0x936910: b.ne            #0x93691c
    // 0x936914: LoadField: r0 = r2->field_b
    //     0x936914: ldur            w0, [x2, #0xb]
    // 0x936918: DecompressPointer r0
    //     0x936918: add             x0, x0, HEAP, lsl #32
    // 0x93691c: r1 = 60
    //     0x93691c: movz            x1, #0x3c
    // 0x936920: branchIfSmi(r3, 0x93692c)
    //     0x936920: tbz             w3, #0, #0x93692c
    // 0x936924: r1 = LoadClassIdInstr(r3)
    //     0x936924: ldur            x1, [x3, #-1]
    //     0x936928: ubfx            x1, x1, #0xc, #0x14
    // 0x93692c: stp             x0, x3, [SP]
    // 0x936930: mov             x0, x1
    // 0x936934: mov             lr, x0
    // 0x936938: ldr             lr, [x21, lr, lsl #3]
    // 0x93693c: blr             lr
    // 0x936940: eor             x1, x0, #0x10
    // 0x936944: mov             x0, x1
    // 0x936948: LeaveFrame
    //     0x936948: mov             SP, fp
    //     0x93694c: ldp             fp, lr, [SP], #0x10
    // 0x936950: ret
    //     0x936950: ret             
    // 0x936954: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x936954: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x936958: b               #0x936904
  }
  [closure] void <anonymous closure>(dynamic, AnimationStatus) {
    // ** addr: 0x93695c, size: 0x48
    // 0x93695c: ldr             x1, [SP, #8]
    // 0x936960: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x936960: ldur            w2, [x1, #0x17]
    // 0x936964: DecompressPointer r2
    //     0x936964: add             x2, x2, HEAP, lsl #32
    // 0x936968: ldr             x1, [SP]
    // 0x93696c: r16 = Instance_AnimationStatus
    //     0x93696c: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0x936970: cmp             w1, w16
    // 0x936974: b.ne            #0x936990
    // 0x936978: LoadField: r1 = r2->field_f
    //     0x936978: ldur            w1, [x2, #0xf]
    // 0x93697c: DecompressPointer r1
    //     0x93697c: add             x1, x1, HEAP, lsl #32
    // 0x936980: LoadField: r2 = r1->field_b
    //     0x936980: ldur            w2, [x1, #0xb]
    // 0x936984: DecompressPointer r2
    //     0x936984: add             x2, x2, HEAP, lsl #32
    // 0x936988: cmp             w2, NULL
    // 0x93698c: b.eq            #0x936998
    // 0x936990: r0 = Null
    //     0x936990: mov             x0, NULL
    // 0x936994: ret
    //     0x936994: ret             
    // 0x936998: EnterFrame
    //     0x936998: stp             fp, lr, [SP, #-0x10]!
    //     0x93699c: mov             fp, SP
    // 0x9369a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9369a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x987844, size: 0x24c
    // 0x987844: EnterFrame
    //     0x987844: stp             fp, lr, [SP, #-0x10]!
    //     0x987848: mov             fp, SP
    // 0x98784c: AllocStack(0x20)
    //     0x98784c: sub             SP, SP, #0x20
    // 0x987850: SetupParameters(ImplicitlyAnimatedWidgetState<X0 bound ImplicitlyAnimatedWidget> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x987850: mov             x0, x2
    //     0x987854: stur            x1, [fp, #-8]
    //     0x987858: stur            x2, [fp, #-0x10]
    // 0x98785c: CheckStackOverflow
    //     0x98785c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x987860: cmp             SP, x16
    //     0x987864: b.ls            #0x987a80
    // 0x987868: r1 = 1
    //     0x987868: movz            x1, #0x1
    // 0x98786c: r0 = AllocateContext()
    //     0x98786c: bl              #0xec126c  ; AllocateContextStub
    // 0x987870: mov             x4, x0
    // 0x987874: ldur            x3, [fp, #-8]
    // 0x987878: stur            x4, [fp, #-0x20]
    // 0x98787c: StoreField: r4->field_f = r3
    //     0x98787c: stur            w3, [x4, #0xf]
    // 0x987880: LoadField: r5 = r3->field_7
    //     0x987880: ldur            w5, [x3, #7]
    // 0x987884: DecompressPointer r5
    //     0x987884: add             x5, x5, HEAP, lsl #32
    // 0x987888: ldur            x0, [fp, #-0x10]
    // 0x98788c: mov             x2, x5
    // 0x987890: stur            x5, [fp, #-0x18]
    // 0x987894: r1 = Null
    //     0x987894: mov             x1, NULL
    // 0x987898: cmp             w2, NULL
    // 0x98789c: b.eq            #0x9878c0
    // 0x9878a0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9878a0: ldur            w4, [x2, #0x17]
    // 0x9878a4: DecompressPointer r4
    //     0x9878a4: add             x4, x4, HEAP, lsl #32
    // 0x9878a8: r8 = X0 bound ImplicitlyAnimatedWidget
    //     0x9878a8: add             x8, PP, #0x46, lsl #12  ; [pp+0x46100] TypeParameter: X0 bound ImplicitlyAnimatedWidget
    //     0x9878ac: ldr             x8, [x8, #0x100]
    // 0x9878b0: LoadField: r9 = r4->field_7
    //     0x9878b0: ldur            x9, [x4, #7]
    // 0x9878b4: r3 = Null
    //     0x9878b4: add             x3, PP, #0x46, lsl #12  ; [pp+0x46108] Null
    //     0x9878b8: ldr             x3, [x3, #0x108]
    // 0x9878bc: blr             x9
    // 0x9878c0: ldur            x0, [fp, #-0x10]
    // 0x9878c4: ldur            x2, [fp, #-0x18]
    // 0x9878c8: r1 = Null
    //     0x9878c8: mov             x1, NULL
    // 0x9878cc: cmp             w2, NULL
    // 0x9878d0: b.eq            #0x9878f4
    // 0x9878d4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9878d4: ldur            w4, [x2, #0x17]
    // 0x9878d8: DecompressPointer r4
    //     0x9878d8: add             x4, x4, HEAP, lsl #32
    // 0x9878dc: r8 = X0 bound StatefulWidget
    //     0x9878dc: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9878e0: ldr             x8, [x8, #0x7f8]
    // 0x9878e4: LoadField: r9 = r4->field_7
    //     0x9878e4: ldur            x9, [x4, #7]
    // 0x9878e8: r3 = Null
    //     0x9878e8: add             x3, PP, #0x46, lsl #12  ; [pp+0x46118] Null
    //     0x9878ec: ldr             x3, [x3, #0x118]
    // 0x9878f0: blr             x9
    // 0x9878f4: ldur            x0, [fp, #-8]
    // 0x9878f8: LoadField: r1 = r0->field_b
    //     0x9878f8: ldur            w1, [x0, #0xb]
    // 0x9878fc: DecompressPointer r1
    //     0x9878fc: add             x1, x1, HEAP, lsl #32
    // 0x987900: cmp             w1, NULL
    // 0x987904: b.eq            #0x987a88
    // 0x987908: LoadField: r2 = r1->field_b
    //     0x987908: ldur            w2, [x1, #0xb]
    // 0x98790c: DecompressPointer r2
    //     0x98790c: add             x2, x2, HEAP, lsl #32
    // 0x987910: ldur            x1, [fp, #-0x10]
    // 0x987914: LoadField: r3 = r1->field_b
    //     0x987914: ldur            w3, [x1, #0xb]
    // 0x987918: DecompressPointer r3
    //     0x987918: add             x3, x3, HEAP, lsl #32
    // 0x98791c: cmp             w2, w3
    // 0x987920: b.eq            #0x98797c
    // 0x987924: mov             x1, x0
    // 0x987928: LoadField: r0 = r1->field_1f
    //     0x987928: ldur            w0, [x1, #0x1f]
    // 0x98792c: DecompressPointer r0
    //     0x98792c: add             x0, x0, HEAP, lsl #32
    // 0x987930: r16 = Sentinel
    //     0x987930: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x987934: cmp             w0, w16
    // 0x987938: b.ne            #0x987948
    // 0x98793c: r2 = _animation
    //     0x98793c: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f0] Field <ImplicitlyAnimatedWidgetState._animation@291443363>: late (offset: 0x20)
    //     0x987940: ldr             x2, [x2, #0xf0]
    // 0x987944: r0 = InitLateInstanceField()
    //     0x987944: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0x987948: mov             x1, x0
    // 0x98794c: r0 = dispose()
    //     0x98794c: bl              #0x7a0b6c  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::dispose
    // 0x987950: ldur            x1, [fp, #-8]
    // 0x987954: r0 = _createCurve()
    //     0x987954: bl              #0x987a90  ; [package:flutter/src/widgets/implicit_animations.dart] ImplicitlyAnimatedWidgetState::_createCurve
    // 0x987958: ldur            x2, [fp, #-8]
    // 0x98795c: StoreField: r2->field_1f = r0
    //     0x98795c: stur            w0, [x2, #0x1f]
    //     0x987960: ldurb           w16, [x2, #-1]
    //     0x987964: ldurb           w17, [x0, #-1]
    //     0x987968: and             x16, x17, x16, lsr #2
    //     0x98796c: tst             x16, HEAP, lsr #32
    //     0x987970: b.eq            #0x987978
    //     0x987974: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x987978: b               #0x987980
    // 0x98797c: mov             x2, x0
    // 0x987980: mov             x1, x2
    // 0x987984: LoadField: r0 = r1->field_1b
    //     0x987984: ldur            w0, [x1, #0x1b]
    // 0x987988: DecompressPointer r0
    //     0x987988: add             x0, x0, HEAP, lsl #32
    // 0x98798c: r16 = Sentinel
    //     0x98798c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x987990: cmp             w0, w16
    // 0x987994: b.ne            #0x9879a4
    // 0x987998: r2 = _controller
    //     0x987998: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f8] Field <ImplicitlyAnimatedWidgetState._controller@291443363>: late final (offset: 0x1c)
    //     0x98799c: ldr             x2, [x2, #0xf8]
    // 0x9879a0: r0 = InitLateFinalInstanceField()
    //     0x9879a0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9879a4: mov             x1, x0
    // 0x9879a8: ldur            x2, [fp, #-8]
    // 0x9879ac: LoadField: r0 = r2->field_b
    //     0x9879ac: ldur            w0, [x2, #0xb]
    // 0x9879b0: DecompressPointer r0
    //     0x9879b0: add             x0, x0, HEAP, lsl #32
    // 0x9879b4: cmp             w0, NULL
    // 0x9879b8: b.eq            #0x987a8c
    // 0x9879bc: LoadField: r3 = r0->field_f
    //     0x9879bc: ldur            w3, [x0, #0xf]
    // 0x9879c0: DecompressPointer r3
    //     0x9879c0: add             x3, x3, HEAP, lsl #32
    // 0x9879c4: mov             x0, x3
    // 0x9879c8: StoreField: r1->field_27 = r0
    //     0x9879c8: stur            w0, [x1, #0x27]
    //     0x9879cc: ldurb           w16, [x1, #-1]
    //     0x9879d0: ldurb           w17, [x0, #-1]
    //     0x9879d4: and             x16, x17, x16, lsr #2
    //     0x9879d8: tst             x16, HEAP, lsr #32
    //     0x9879dc: b.eq            #0x9879e4
    //     0x9879e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9879e4: mov             x1, x2
    // 0x9879e8: r0 = _constructTweens()
    //     0x9879e8: bl              #0x93676c  ; [package:flutter/src/widgets/implicit_animations.dart] ImplicitlyAnimatedWidgetState::_constructTweens
    // 0x9879ec: tbnz            w0, #4, #0x987a70
    // 0x9879f0: ldur            x0, [fp, #-8]
    // 0x9879f4: ldur            x2, [fp, #-0x20]
    // 0x9879f8: r1 = Function '<anonymous closure>':.
    //     0x9879f8: add             x1, PP, #0x46, lsl #12  ; [pp+0x46128] AnonymousClosure: (0x987b38), in [package:flutter/src/widgets/implicit_animations.dart] ImplicitlyAnimatedWidgetState::didUpdateWidget (0x987844)
    //     0x9879fc: ldr             x1, [x1, #0x128]
    // 0x987a00: r0 = AllocateClosure()
    //     0x987a00: bl              #0xec1630  ; AllocateClosureStub
    // 0x987a04: ldur            x3, [fp, #-8]
    // 0x987a08: r1 = LoadClassIdInstr(r3)
    //     0x987a08: ldur            x1, [x3, #-1]
    //     0x987a0c: ubfx            x1, x1, #0xc, #0x14
    // 0x987a10: mov             x2, x0
    // 0x987a14: mov             x0, x1
    // 0x987a18: mov             x1, x3
    // 0x987a1c: r0 = GDT[cid_x0 + 0x15f9]()
    //     0x987a1c: movz            x17, #0x15f9
    //     0x987a20: add             lr, x0, x17
    //     0x987a24: ldr             lr, [x21, lr, lsl #3]
    //     0x987a28: blr             lr
    // 0x987a2c: ldur            x0, [fp, #-8]
    // 0x987a30: LoadField: r2 = r0->field_1b
    //     0x987a30: ldur            w2, [x0, #0x1b]
    // 0x987a34: DecompressPointer r2
    //     0x987a34: add             x2, x2, HEAP, lsl #32
    // 0x987a38: mov             x1, x2
    // 0x987a3c: stur            x2, [fp, #-0x10]
    // 0x987a40: d0 = 0.000000
    //     0x987a40: eor             v0.16b, v0.16b, v0.16b
    // 0x987a44: r0 = value=()
    //     0x987a44: bl              #0x6567c4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::value=
    // 0x987a48: ldur            x1, [fp, #-0x10]
    // 0x987a4c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x987a4c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x987a50: r0 = forward()
    //     0x987a50: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x987a54: ldur            x1, [fp, #-8]
    // 0x987a58: r0 = LoadClassIdInstr(r1)
    //     0x987a58: ldur            x0, [x1, #-1]
    //     0x987a5c: ubfx            x0, x0, #0xc, #0x14
    // 0x987a60: r0 = GDT[cid_x0 + 0x2247]()
    //     0x987a60: movz            x17, #0x2247
    //     0x987a64: add             lr, x0, x17
    //     0x987a68: ldr             lr, [x21, lr, lsl #3]
    //     0x987a6c: blr             lr
    // 0x987a70: r0 = Null
    //     0x987a70: mov             x0, NULL
    // 0x987a74: LeaveFrame
    //     0x987a74: mov             SP, fp
    //     0x987a78: ldp             fp, lr, [SP], #0x10
    // 0x987a7c: ret
    //     0x987a7c: ret             
    // 0x987a80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x987a80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x987a84: b               #0x987868
    // 0x987a88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x987a88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x987a8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x987a8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  CurvedAnimation _createCurve(ImplicitlyAnimatedWidgetState<X0>) {
    // ** addr: 0x987a90, size: 0xa8
    // 0x987a90: EnterFrame
    //     0x987a90: stp             fp, lr, [SP, #-0x10]!
    //     0x987a94: mov             fp, SP
    // 0x987a98: AllocStack(0x10)
    //     0x987a98: sub             SP, SP, #0x10
    // 0x987a9c: SetupParameters(ImplicitlyAnimatedWidgetState<X0 bound ImplicitlyAnimatedWidget> this /* r1 => r0, fp-0x8 */)
    //     0x987a9c: mov             x0, x1
    //     0x987aa0: stur            x1, [fp, #-8]
    // 0x987aa4: CheckStackOverflow
    //     0x987aa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x987aa8: cmp             SP, x16
    //     0x987aac: b.ls            #0x987b2c
    // 0x987ab0: mov             x1, x0
    // 0x987ab4: LoadField: r0 = r1->field_1b
    //     0x987ab4: ldur            w0, [x1, #0x1b]
    // 0x987ab8: DecompressPointer r0
    //     0x987ab8: add             x0, x0, HEAP, lsl #32
    // 0x987abc: r16 = Sentinel
    //     0x987abc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x987ac0: cmp             w0, w16
    // 0x987ac4: b.ne            #0x987ad4
    // 0x987ac8: r2 = _controller
    //     0x987ac8: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f8] Field <ImplicitlyAnimatedWidgetState._controller@291443363>: late final (offset: 0x1c)
    //     0x987acc: ldr             x2, [x2, #0xf8]
    // 0x987ad0: r0 = InitLateFinalInstanceField()
    //     0x987ad0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x987ad4: mov             x2, x0
    // 0x987ad8: ldur            x0, [fp, #-8]
    // 0x987adc: stur            x2, [fp, #-0x10]
    // 0x987ae0: LoadField: r1 = r0->field_b
    //     0x987ae0: ldur            w1, [x0, #0xb]
    // 0x987ae4: DecompressPointer r1
    //     0x987ae4: add             x1, x1, HEAP, lsl #32
    // 0x987ae8: cmp             w1, NULL
    // 0x987aec: b.eq            #0x987b34
    // 0x987af0: LoadField: r0 = r1->field_b
    //     0x987af0: ldur            w0, [x1, #0xb]
    // 0x987af4: DecompressPointer r0
    //     0x987af4: add             x0, x0, HEAP, lsl #32
    // 0x987af8: stur            x0, [fp, #-8]
    // 0x987afc: r1 = <double>
    //     0x987afc: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x987b00: r0 = CurvedAnimation()
    //     0x987b00: bl              #0x7e34d0  ; AllocateCurvedAnimationStub -> CurvedAnimation (size=0x1c)
    // 0x987b04: mov             x1, x0
    // 0x987b08: ldur            x2, [fp, #-8]
    // 0x987b0c: ldur            x3, [fp, #-0x10]
    // 0x987b10: stur            x0, [fp, #-8]
    // 0x987b14: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x987b14: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x987b18: r0 = CurvedAnimation()
    //     0x987b18: bl              #0x7e338c  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::CurvedAnimation
    // 0x987b1c: ldur            x0, [fp, #-8]
    // 0x987b20: LeaveFrame
    //     0x987b20: mov             SP, fp
    //     0x987b24: ldp             fp, lr, [SP], #0x10
    // 0x987b28: ret
    //     0x987b28: ret             
    // 0x987b2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x987b2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x987b30: b               #0x987ab0
    // 0x987b34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x987b34: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Tween<dynamic>? <anonymous closure>(dynamic, Tween<dynamic>?, dynamic, (dynamic, dynamic) => Tween<dynamic>) {
    // ** addr: 0x987b38, size: 0x50
    // 0x987b38: EnterFrame
    //     0x987b38: stp             fp, lr, [SP, #-0x10]!
    //     0x987b3c: mov             fp, SP
    // 0x987b40: ldr             x0, [fp, #0x28]
    // 0x987b44: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x987b44: ldur            w1, [x0, #0x17]
    // 0x987b48: DecompressPointer r1
    //     0x987b48: add             x1, x1, HEAP, lsl #32
    // 0x987b4c: CheckStackOverflow
    //     0x987b4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x987b50: cmp             SP, x16
    //     0x987b54: b.ls            #0x987b80
    // 0x987b58: LoadField: r0 = r1->field_f
    //     0x987b58: ldur            w0, [x1, #0xf]
    // 0x987b5c: DecompressPointer r0
    //     0x987b5c: add             x0, x0, HEAP, lsl #32
    // 0x987b60: mov             x1, x0
    // 0x987b64: ldr             x2, [fp, #0x20]
    // 0x987b68: ldr             x3, [fp, #0x18]
    // 0x987b6c: r0 = _updateTween()
    //     0x987b6c: bl              #0x987b88  ; [package:flutter/src/widgets/implicit_animations.dart] ImplicitlyAnimatedWidgetState::_updateTween
    // 0x987b70: ldr             x0, [fp, #0x20]
    // 0x987b74: LeaveFrame
    //     0x987b74: mov             SP, fp
    //     0x987b78: ldp             fp, lr, [SP], #0x10
    // 0x987b7c: ret
    //     0x987b7c: ret             
    // 0x987b80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x987b80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x987b84: b               #0x987b58
  }
  _ _updateTween(/* No info */) {
    // ** addr: 0x987b88, size: 0xcc
    // 0x987b88: EnterFrame
    //     0x987b88: stp             fp, lr, [SP, #-0x10]!
    //     0x987b8c: mov             fp, SP
    // 0x987b90: AllocStack(0x10)
    //     0x987b90: sub             SP, SP, #0x10
    // 0x987b94: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r2, fp-0x10 */)
    //     0x987b94: mov             x0, x2
    //     0x987b98: stur            x2, [fp, #-8]
    //     0x987b9c: mov             x2, x3
    //     0x987ba0: stur            x3, [fp, #-0x10]
    // 0x987ba4: CheckStackOverflow
    //     0x987ba4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x987ba8: cmp             SP, x16
    //     0x987bac: b.ls            #0x987c4c
    // 0x987bb0: cmp             w0, NULL
    // 0x987bb4: b.ne            #0x987bc8
    // 0x987bb8: r0 = Null
    //     0x987bb8: mov             x0, NULL
    // 0x987bbc: LeaveFrame
    //     0x987bbc: mov             SP, fp
    //     0x987bc0: ldp             fp, lr, [SP], #0x10
    // 0x987bc4: ret
    //     0x987bc4: ret             
    // 0x987bc8: LoadField: r0 = r1->field_1f
    //     0x987bc8: ldur            w0, [x1, #0x1f]
    // 0x987bcc: DecompressPointer r0
    //     0x987bcc: add             x0, x0, HEAP, lsl #32
    // 0x987bd0: r16 = Sentinel
    //     0x987bd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x987bd4: cmp             w0, w16
    // 0x987bd8: b.ne            #0x987be8
    // 0x987bdc: r2 = _animation
    //     0x987bdc: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f0] Field <ImplicitlyAnimatedWidgetState._animation@291443363>: late (offset: 0x20)
    //     0x987be0: ldr             x2, [x2, #0xf0]
    // 0x987be4: r0 = InitLateInstanceField()
    //     0x987be4: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0x987be8: ldur            x1, [fp, #-8]
    // 0x987bec: mov             x2, x0
    // 0x987bf0: r0 = evaluate()
    //     0x987bf0: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x987bf4: ldur            x3, [fp, #-8]
    // 0x987bf8: r1 = LoadClassIdInstr(r3)
    //     0x987bf8: ldur            x1, [x3, #-1]
    //     0x987bfc: ubfx            x1, x1, #0xc, #0x14
    // 0x987c00: mov             x2, x0
    // 0x987c04: mov             x0, x1
    // 0x987c08: mov             x1, x3
    // 0x987c0c: r0 = GDT[cid_x0 + 0xe6cd]()
    //     0x987c0c: movz            x17, #0xe6cd
    //     0x987c10: add             lr, x0, x17
    //     0x987c14: ldr             lr, [x21, lr, lsl #3]
    //     0x987c18: blr             lr
    // 0x987c1c: ldur            x1, [fp, #-8]
    // 0x987c20: r0 = LoadClassIdInstr(r1)
    //     0x987c20: ldur            x0, [x1, #-1]
    //     0x987c24: ubfx            x0, x0, #0xc, #0x14
    // 0x987c28: ldur            x2, [fp, #-0x10]
    // 0x987c2c: r0 = GDT[cid_x0 + 0x1933]()
    //     0x987c2c: movz            x17, #0x1933
    //     0x987c30: add             lr, x0, x17
    //     0x987c34: ldr             lr, [x21, lr, lsl #3]
    //     0x987c38: blr             lr
    // 0x987c3c: r0 = Null
    //     0x987c3c: mov             x0, NULL
    // 0x987c40: LeaveFrame
    //     0x987c40: mov             SP, fp
    //     0x987c44: ldp             fp, lr, [SP], #0x10
    // 0x987c48: ret
    //     0x987c48: ret             
    // 0x987c4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x987c4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x987c50: b               #0x987bb0
  }
  CurvedAnimation _animation(ImplicitlyAnimatedWidgetState<X0>) {
    // ** addr: 0x987c54, size: 0x30
    // 0x987c54: EnterFrame
    //     0x987c54: stp             fp, lr, [SP, #-0x10]!
    //     0x987c58: mov             fp, SP
    // 0x987c5c: CheckStackOverflow
    //     0x987c5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x987c60: cmp             SP, x16
    //     0x987c64: b.ls            #0x987c7c
    // 0x987c68: ldr             x1, [fp, #0x10]
    // 0x987c6c: r0 = _createCurve()
    //     0x987c6c: bl              #0x987a90  ; [package:flutter/src/widgets/implicit_animations.dart] ImplicitlyAnimatedWidgetState::_createCurve
    // 0x987c70: LeaveFrame
    //     0x987c70: mov             SP, fp
    //     0x987c74: ldp             fp, lr, [SP], #0x10
    // 0x987c78: ret
    //     0x987c78: ret             
    // 0x987c7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x987c7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x987c80: b               #0x987c68
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa7dc14, size: 0x98
    // 0xa7dc14: EnterFrame
    //     0xa7dc14: stp             fp, lr, [SP, #-0x10]!
    //     0xa7dc18: mov             fp, SP
    // 0xa7dc1c: AllocStack(0x8)
    //     0xa7dc1c: sub             SP, SP, #8
    // 0xa7dc20: SetupParameters(ImplicitlyAnimatedWidgetState<X0 bound ImplicitlyAnimatedWidget> this /* r1 => r0, fp-0x8 */)
    //     0xa7dc20: mov             x0, x1
    //     0xa7dc24: stur            x1, [fp, #-8]
    // 0xa7dc28: CheckStackOverflow
    //     0xa7dc28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7dc2c: cmp             SP, x16
    //     0xa7dc30: b.ls            #0xa7dca4
    // 0xa7dc34: mov             x1, x0
    // 0xa7dc38: LoadField: r0 = r1->field_1f
    //     0xa7dc38: ldur            w0, [x1, #0x1f]
    // 0xa7dc3c: DecompressPointer r0
    //     0xa7dc3c: add             x0, x0, HEAP, lsl #32
    // 0xa7dc40: r16 = Sentinel
    //     0xa7dc40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7dc44: cmp             w0, w16
    // 0xa7dc48: b.ne            #0xa7dc58
    // 0xa7dc4c: r2 = _animation
    //     0xa7dc4c: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f0] Field <ImplicitlyAnimatedWidgetState._animation@291443363>: late (offset: 0x20)
    //     0xa7dc50: ldr             x2, [x2, #0xf0]
    // 0xa7dc54: r0 = InitLateInstanceField()
    //     0xa7dc54: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0xa7dc58: mov             x1, x0
    // 0xa7dc5c: r0 = dispose()
    //     0xa7dc5c: bl              #0x7a0b6c  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::dispose
    // 0xa7dc60: ldur            x1, [fp, #-8]
    // 0xa7dc64: LoadField: r0 = r1->field_1b
    //     0xa7dc64: ldur            w0, [x1, #0x1b]
    // 0xa7dc68: DecompressPointer r0
    //     0xa7dc68: add             x0, x0, HEAP, lsl #32
    // 0xa7dc6c: r16 = Sentinel
    //     0xa7dc6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa7dc70: cmp             w0, w16
    // 0xa7dc74: b.ne            #0xa7dc84
    // 0xa7dc78: r2 = _controller
    //     0xa7dc78: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f8] Field <ImplicitlyAnimatedWidgetState._controller@291443363>: late final (offset: 0x1c)
    //     0xa7dc7c: ldr             x2, [x2, #0xf8]
    // 0xa7dc80: r0 = InitLateFinalInstanceField()
    //     0xa7dc80: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa7dc84: mov             x1, x0
    // 0xa7dc88: r0 = dispose()
    //     0xa7dc88: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa7dc8c: ldur            x1, [fp, #-8]
    // 0xa7dc90: r0 = dispose()
    //     0xa7dc90: bl              #0xa7dcac  ; [package:flutter/src/widgets/implicit_animations.dart] _ImplicitlyAnimatedWidgetState&State&SingleTickerProviderStateMixin::dispose
    // 0xa7dc94: r0 = Null
    //     0xa7dc94: mov             x0, NULL
    // 0xa7dc98: LeaveFrame
    //     0xa7dc98: mov             SP, fp
    //     0xa7dc9c: ldp             fp, lr, [SP], #0x10
    // 0xa7dca0: ret
    //     0xa7dca0: ret             
    // 0xa7dca4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7dca4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7dca8: b               #0xa7dc34
  }
}

// class id: 4288, size: 0x2c, field offset: 0x24
class _AnimatedOpacityState extends ImplicitlyAnimatedWidgetState<dynamic> {

  late Animation<double> _opacityAnimation; // offset: 0x28

  _ build(/* No info */) {
    // ** addr: 0x9fbe54, size: 0x78
    // 0x9fbe54: EnterFrame
    //     0x9fbe54: stp             fp, lr, [SP, #-0x10]!
    //     0x9fbe58: mov             fp, SP
    // 0x9fbe5c: AllocStack(0x10)
    //     0x9fbe5c: sub             SP, SP, #0x10
    // 0x9fbe60: LoadField: r0 = r1->field_27
    //     0x9fbe60: ldur            w0, [x1, #0x27]
    // 0x9fbe64: DecompressPointer r0
    //     0x9fbe64: add             x0, x0, HEAP, lsl #32
    // 0x9fbe68: r16 = Sentinel
    //     0x9fbe68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9fbe6c: cmp             w0, w16
    // 0x9fbe70: b.eq            #0x9fbebc
    // 0x9fbe74: stur            x0, [fp, #-0x10]
    // 0x9fbe78: LoadField: r2 = r1->field_b
    //     0x9fbe78: ldur            w2, [x1, #0xb]
    // 0x9fbe7c: DecompressPointer r2
    //     0x9fbe7c: add             x2, x2, HEAP, lsl #32
    // 0x9fbe80: cmp             w2, NULL
    // 0x9fbe84: b.eq            #0x9fbec8
    // 0x9fbe88: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x9fbe88: ldur            w1, [x2, #0x17]
    // 0x9fbe8c: DecompressPointer r1
    //     0x9fbe8c: add             x1, x1, HEAP, lsl #32
    // 0x9fbe90: stur            x1, [fp, #-8]
    // 0x9fbe94: r0 = FadeTransition()
    //     0x9fbe94: bl              #0x91a518  ; AllocateFadeTransitionStub -> FadeTransition (size=0x18)
    // 0x9fbe98: ldur            x1, [fp, #-0x10]
    // 0x9fbe9c: StoreField: r0->field_f = r1
    //     0x9fbe9c: stur            w1, [x0, #0xf]
    // 0x9fbea0: r1 = false
    //     0x9fbea0: add             x1, NULL, #0x30  ; false
    // 0x9fbea4: StoreField: r0->field_13 = r1
    //     0x9fbea4: stur            w1, [x0, #0x13]
    // 0x9fbea8: ldur            x1, [fp, #-8]
    // 0x9fbeac: StoreField: r0->field_b = r1
    //     0x9fbeac: stur            w1, [x0, #0xb]
    // 0x9fbeb0: LeaveFrame
    //     0x9fbeb0: mov             SP, fp
    //     0x9fbeb4: ldp             fp, lr, [SP], #0x10
    // 0x9fbeb8: ret
    //     0x9fbeb8: ret             
    // 0x9fbebc: r9 = _opacityAnimation
    //     0x9fbebc: add             x9, PP, #0x5c, lsl #12  ; [pp+0x5cca8] Field <_AnimatedOpacityState@291443363._opacityAnimation@291443363>: late (offset: 0x28)
    //     0x9fbec0: ldr             x9, [x9, #0xca8]
    // 0x9fbec4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9fbec4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9fbec8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbec8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateTweens(/* No info */) {
    // ** addr: 0xc299e8, size: 0xa8
    // 0xc299e8: EnterFrame
    //     0xc299e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc299ec: mov             fp, SP
    // 0xc299f0: AllocStack(0x8)
    //     0xc299f0: sub             SP, SP, #8
    // 0xc299f4: SetupParameters(_AnimatedOpacityState this /* r1 => r0, fp-0x8 */)
    //     0xc299f4: mov             x0, x1
    //     0xc299f8: stur            x1, [fp, #-8]
    // 0xc299fc: CheckStackOverflow
    //     0xc299fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc29a00: cmp             SP, x16
    //     0xc29a04: b.ls            #0xc29a84
    // 0xc29a08: mov             x1, x0
    // 0xc29a0c: LoadField: r0 = r1->field_1f
    //     0xc29a0c: ldur            w0, [x1, #0x1f]
    // 0xc29a10: DecompressPointer r0
    //     0xc29a10: add             x0, x0, HEAP, lsl #32
    // 0xc29a14: r16 = Sentinel
    //     0xc29a14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc29a18: cmp             w0, w16
    // 0xc29a1c: b.ne            #0xc29a2c
    // 0xc29a20: r2 = _animation
    //     0xc29a20: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f0] Field <ImplicitlyAnimatedWidgetState._animation@291443363>: late (offset: 0x20)
    //     0xc29a24: ldr             x2, [x2, #0xf0]
    // 0xc29a28: r0 = InitLateInstanceField()
    //     0xc29a28: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0xc29a2c: mov             x1, x0
    // 0xc29a30: ldur            x0, [fp, #-8]
    // 0xc29a34: LoadField: r2 = r0->field_23
    //     0xc29a34: ldur            w2, [x0, #0x23]
    // 0xc29a38: DecompressPointer r2
    //     0xc29a38: add             x2, x2, HEAP, lsl #32
    // 0xc29a3c: cmp             w2, NULL
    // 0xc29a40: b.eq            #0xc29a8c
    // 0xc29a44: mov             x16, x1
    // 0xc29a48: mov             x1, x2
    // 0xc29a4c: mov             x2, x16
    // 0xc29a50: r0 = animate()
    //     0xc29a50: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0xc29a54: ldur            x1, [fp, #-8]
    // 0xc29a58: StoreField: r1->field_27 = r0
    //     0xc29a58: stur            w0, [x1, #0x27]
    //     0xc29a5c: ldurb           w16, [x1, #-1]
    //     0xc29a60: ldurb           w17, [x0, #-1]
    //     0xc29a64: and             x16, x17, x16, lsr #2
    //     0xc29a68: tst             x16, HEAP, lsr #32
    //     0xc29a6c: b.eq            #0xc29a74
    //     0xc29a70: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc29a74: r0 = Null
    //     0xc29a74: mov             x0, NULL
    // 0xc29a78: LeaveFrame
    //     0xc29a78: mov             SP, fp
    //     0xc29a7c: ldp             fp, lr, [SP], #0x10
    // 0xc29a80: ret
    //     0xc29a80: ret             
    // 0xc29a84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc29a84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc29a88: b               #0xc29a08
    // 0xc29a8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc29a8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ forEachTween(/* No info */) {
    // ** addr: 0xcd66e4, size: 0x108
    // 0xcd66e4: EnterFrame
    //     0xcd66e4: stp             fp, lr, [SP, #-0x10]!
    //     0xcd66e8: mov             fp, SP
    // 0xcd66ec: AllocStack(0x40)
    //     0xcd66ec: sub             SP, SP, #0x40
    // 0xcd66f0: SetupParameters(_AnimatedOpacityState this /* r1 => r3, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */)
    //     0xcd66f0: mov             x3, x1
    //     0xcd66f4: mov             x0, x2
    //     0xcd66f8: stur            x1, [fp, #-0x18]
    //     0xcd66fc: stur            x2, [fp, #-0x20]
    // 0xcd6700: CheckStackOverflow
    //     0xcd6700: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcd6704: cmp             SP, x16
    //     0xcd6708: b.ls            #0xcd67bc
    // 0xcd670c: LoadField: r4 = r3->field_23
    //     0xcd670c: ldur            w4, [x3, #0x23]
    // 0xcd6710: DecompressPointer r4
    //     0xcd6710: add             x4, x4, HEAP, lsl #32
    // 0xcd6714: stur            x4, [fp, #-0x10]
    // 0xcd6718: LoadField: r1 = r3->field_b
    //     0xcd6718: ldur            w1, [x3, #0xb]
    // 0xcd671c: DecompressPointer r1
    //     0xcd671c: add             x1, x1, HEAP, lsl #32
    // 0xcd6720: cmp             w1, NULL
    // 0xcd6724: b.eq            #0xcd67c4
    // 0xcd6728: LoadField: d0 = r1->field_1b
    //     0xcd6728: ldur            d0, [x1, #0x1b]
    // 0xcd672c: r5 = inline_Allocate_Double()
    //     0xcd672c: ldp             x5, x1, [THR, #0x50]  ; THR::top
    //     0xcd6730: add             x5, x5, #0x10
    //     0xcd6734: cmp             x1, x5
    //     0xcd6738: b.ls            #0xcd67c8
    //     0xcd673c: str             x5, [THR, #0x50]  ; THR::top
    //     0xcd6740: sub             x5, x5, #0xf
    //     0xcd6744: movz            x1, #0xe15c
    //     0xcd6748: movk            x1, #0x3, lsl #16
    //     0xcd674c: stur            x1, [x5, #-1]
    // 0xcd6750: StoreField: r5->field_7 = d0
    //     0xcd6750: stur            d0, [x5, #7]
    // 0xcd6754: stur            x5, [fp, #-8]
    // 0xcd6758: r1 = Function '<anonymous closure>':.
    //     0xcd6758: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5ccb0] AnonymousClosure: (0xcd67ec), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedOpacityState::forEachTween (0xcd66e4)
    //     0xcd675c: ldr             x1, [x1, #0xcb0]
    // 0xcd6760: r2 = Null
    //     0xcd6760: mov             x2, NULL
    // 0xcd6764: r0 = AllocateClosure()
    //     0xcd6764: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd6768: ldur            x16, [fp, #-0x20]
    // 0xcd676c: ldur            lr, [fp, #-0x10]
    // 0xcd6770: stp             lr, x16, [SP, #0x10]
    // 0xcd6774: ldur            x16, [fp, #-8]
    // 0xcd6778: stp             x0, x16, [SP]
    // 0xcd677c: ldur            x0, [fp, #-0x20]
    // 0xcd6780: ClosureCall
    //     0xcd6780: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd6784: ldur            x2, [x0, #0x1f]
    //     0xcd6788: blr             x2
    // 0xcd678c: ldur            x1, [fp, #-0x18]
    // 0xcd6790: StoreField: r1->field_23 = r0
    //     0xcd6790: stur            w0, [x1, #0x23]
    //     0xcd6794: ldurb           w16, [x1, #-1]
    //     0xcd6798: ldurb           w17, [x0, #-1]
    //     0xcd679c: and             x16, x17, x16, lsr #2
    //     0xcd67a0: tst             x16, HEAP, lsr #32
    //     0xcd67a4: b.eq            #0xcd67ac
    //     0xcd67a8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcd67ac: r0 = Null
    //     0xcd67ac: mov             x0, NULL
    // 0xcd67b0: LeaveFrame
    //     0xcd67b0: mov             SP, fp
    //     0xcd67b4: ldp             fp, lr, [SP], #0x10
    // 0xcd67b8: ret
    //     0xcd67b8: ret             
    // 0xcd67bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcd67bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcd67c0: b               #0xcd670c
    // 0xcd67c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd67c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd67c8: SaveReg d0
    //     0xcd67c8: str             q0, [SP, #-0x10]!
    // 0xcd67cc: stp             x3, x4, [SP, #-0x10]!
    // 0xcd67d0: SaveReg r0
    //     0xcd67d0: str             x0, [SP, #-8]!
    // 0xcd67d4: r0 = AllocateDouble()
    //     0xcd67d4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xcd67d8: mov             x5, x0
    // 0xcd67dc: RestoreReg r0
    //     0xcd67dc: ldr             x0, [SP], #8
    // 0xcd67e0: ldp             x3, x4, [SP], #0x10
    // 0xcd67e4: RestoreReg d0
    //     0xcd67e4: ldr             q0, [SP], #0x10
    // 0xcd67e8: b               #0xcd6750
  }
  [closure] Tween<double> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd67ec, size: 0x58
    // 0xcd67ec: EnterFrame
    //     0xcd67ec: stp             fp, lr, [SP, #-0x10]!
    //     0xcd67f0: mov             fp, SP
    // 0xcd67f4: ldr             x0, [fp, #0x10]
    // 0xcd67f8: r2 = Null
    //     0xcd67f8: mov             x2, NULL
    // 0xcd67fc: r1 = Null
    //     0xcd67fc: mov             x1, NULL
    // 0xcd6800: r4 = 60
    //     0xcd6800: movz            x4, #0x3c
    // 0xcd6804: branchIfSmi(r0, 0xcd6810)
    //     0xcd6804: tbz             w0, #0, #0xcd6810
    // 0xcd6808: r4 = LoadClassIdInstr(r0)
    //     0xcd6808: ldur            x4, [x0, #-1]
    //     0xcd680c: ubfx            x4, x4, #0xc, #0x14
    // 0xcd6810: cmp             x4, #0x3e
    // 0xcd6814: b.eq            #0xcd6828
    // 0xcd6818: r8 = double
    //     0xcd6818: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xcd681c: r3 = Null
    //     0xcd681c: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5ccb8] Null
    //     0xcd6820: ldr             x3, [x3, #0xcb8]
    // 0xcd6824: r0 = double()
    //     0xcd6824: bl              #0xed4460  ; IsType_double_Stub
    // 0xcd6828: r1 = <double>
    //     0xcd6828: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xcd682c: r0 = Tween()
    //     0xcd682c: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xcd6830: ldr             x1, [fp, #0x10]
    // 0xcd6834: StoreField: r0->field_b = r1
    //     0xcd6834: stur            w1, [x0, #0xb]
    // 0xcd6838: LeaveFrame
    //     0xcd6838: mov             SP, fp
    //     0xcd683c: ldp             fp, lr, [SP], #0x10
    // 0xcd6840: ret
    //     0xcd6840: ret             
  }
}

// class id: 4289, size: 0x2c, field offset: 0x24
class _AnimatedRotationState extends ImplicitlyAnimatedWidgetState<dynamic> {

  late Animation<double> _turnsAnimation; // offset: 0x28

  _ build(/* No info */) {
    // ** addr: 0x9fbda8, size: 0x88
    // 0x9fbda8: EnterFrame
    //     0x9fbda8: stp             fp, lr, [SP, #-0x10]!
    //     0x9fbdac: mov             fp, SP
    // 0x9fbdb0: AllocStack(0x10)
    //     0x9fbdb0: sub             SP, SP, #0x10
    // 0x9fbdb4: LoadField: r0 = r1->field_27
    //     0x9fbdb4: ldur            w0, [x1, #0x27]
    // 0x9fbdb8: DecompressPointer r0
    //     0x9fbdb8: add             x0, x0, HEAP, lsl #32
    // 0x9fbdbc: r16 = Sentinel
    //     0x9fbdbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9fbdc0: cmp             w0, w16
    // 0x9fbdc4: b.eq            #0x9fbe20
    // 0x9fbdc8: stur            x0, [fp, #-0x10]
    // 0x9fbdcc: LoadField: r2 = r1->field_b
    //     0x9fbdcc: ldur            w2, [x1, #0xb]
    // 0x9fbdd0: DecompressPointer r2
    //     0x9fbdd0: add             x2, x2, HEAP, lsl #32
    // 0x9fbdd4: cmp             w2, NULL
    // 0x9fbdd8: b.eq            #0x9fbe2c
    // 0x9fbddc: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x9fbddc: ldur            w1, [x2, #0x17]
    // 0x9fbde0: DecompressPointer r1
    //     0x9fbde0: add             x1, x1, HEAP, lsl #32
    // 0x9fbde4: stur            x1, [fp, #-8]
    // 0x9fbde8: r0 = RotationTransition()
    //     0x9fbde8: bl              #0x9f0c10  ; AllocateRotationTransitionStub -> RotationTransition (size=0x20)
    // 0x9fbdec: r1 = Closure: (double) => Matrix4 from Function '_handleTurnsMatrix@367170175': static.
    //     0x9fbdec: add             x1, PP, #0x41, lsl #12  ; [pp+0x41580] Closure: (double) => Matrix4 from Function '_handleTurnsMatrix@367170175': static. (0x7e54fb3f0c1c)
    //     0x9fbdf0: ldr             x1, [x1, #0x580]
    // 0x9fbdf4: StoreField: r0->field_f = r1
    //     0x9fbdf4: stur            w1, [x0, #0xf]
    // 0x9fbdf8: r1 = Instance_Alignment
    //     0x9fbdf8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x9fbdfc: ldr             x1, [x1, #0x898]
    // 0x9fbe00: StoreField: r0->field_13 = r1
    //     0x9fbe00: stur            w1, [x0, #0x13]
    // 0x9fbe04: ldur            x1, [fp, #-8]
    // 0x9fbe08: StoreField: r0->field_1b = r1
    //     0x9fbe08: stur            w1, [x0, #0x1b]
    // 0x9fbe0c: ldur            x1, [fp, #-0x10]
    // 0x9fbe10: StoreField: r0->field_b = r1
    //     0x9fbe10: stur            w1, [x0, #0xb]
    // 0x9fbe14: LeaveFrame
    //     0x9fbe14: mov             SP, fp
    //     0x9fbe18: ldp             fp, lr, [SP], #0x10
    // 0x9fbe1c: ret
    //     0x9fbe1c: ret             
    // 0x9fbe20: r9 = _turnsAnimation
    //     0x9fbe20: add             x9, PP, #0x46, lsl #12  ; [pp+0x460e8] Field <_AnimatedRotationState@291443363._turnsAnimation@291443363>: late (offset: 0x28)
    //     0x9fbe24: ldr             x9, [x9, #0xe8]
    // 0x9fbe28: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9fbe28: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9fbe2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbe2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ forEachTween(/* No info */) {
    // ** addr: 0xcd6584, size: 0x108
    // 0xcd6584: EnterFrame
    //     0xcd6584: stp             fp, lr, [SP, #-0x10]!
    //     0xcd6588: mov             fp, SP
    // 0xcd658c: AllocStack(0x40)
    //     0xcd658c: sub             SP, SP, #0x40
    // 0xcd6590: SetupParameters(_AnimatedRotationState this /* r1 => r3, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */)
    //     0xcd6590: mov             x3, x1
    //     0xcd6594: mov             x0, x2
    //     0xcd6598: stur            x1, [fp, #-0x18]
    //     0xcd659c: stur            x2, [fp, #-0x20]
    // 0xcd65a0: CheckStackOverflow
    //     0xcd65a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcd65a4: cmp             SP, x16
    //     0xcd65a8: b.ls            #0xcd665c
    // 0xcd65ac: LoadField: r4 = r3->field_23
    //     0xcd65ac: ldur            w4, [x3, #0x23]
    // 0xcd65b0: DecompressPointer r4
    //     0xcd65b0: add             x4, x4, HEAP, lsl #32
    // 0xcd65b4: stur            x4, [fp, #-0x10]
    // 0xcd65b8: LoadField: r1 = r3->field_b
    //     0xcd65b8: ldur            w1, [x3, #0xb]
    // 0xcd65bc: DecompressPointer r1
    //     0xcd65bc: add             x1, x1, HEAP, lsl #32
    // 0xcd65c0: cmp             w1, NULL
    // 0xcd65c4: b.eq            #0xcd6664
    // 0xcd65c8: LoadField: d0 = r1->field_1b
    //     0xcd65c8: ldur            d0, [x1, #0x1b]
    // 0xcd65cc: r5 = inline_Allocate_Double()
    //     0xcd65cc: ldp             x5, x1, [THR, #0x50]  ; THR::top
    //     0xcd65d0: add             x5, x5, #0x10
    //     0xcd65d4: cmp             x1, x5
    //     0xcd65d8: b.ls            #0xcd6668
    //     0xcd65dc: str             x5, [THR, #0x50]  ; THR::top
    //     0xcd65e0: sub             x5, x5, #0xf
    //     0xcd65e4: movz            x1, #0xe15c
    //     0xcd65e8: movk            x1, #0x3, lsl #16
    //     0xcd65ec: stur            x1, [x5, #-1]
    // 0xcd65f0: StoreField: r5->field_7 = d0
    //     0xcd65f0: stur            d0, [x5, #7]
    // 0xcd65f4: stur            x5, [fp, #-8]
    // 0xcd65f8: r1 = Function '<anonymous closure>':.
    //     0xcd65f8: add             x1, PP, #0x50, lsl #12  ; [pp+0x502c0] AnonymousClosure: (0xcd668c), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedRotationState::forEachTween (0xcd6584)
    //     0xcd65fc: ldr             x1, [x1, #0x2c0]
    // 0xcd6600: r2 = Null
    //     0xcd6600: mov             x2, NULL
    // 0xcd6604: r0 = AllocateClosure()
    //     0xcd6604: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd6608: ldur            x16, [fp, #-0x20]
    // 0xcd660c: ldur            lr, [fp, #-0x10]
    // 0xcd6610: stp             lr, x16, [SP, #0x10]
    // 0xcd6614: ldur            x16, [fp, #-8]
    // 0xcd6618: stp             x0, x16, [SP]
    // 0xcd661c: ldur            x0, [fp, #-0x20]
    // 0xcd6620: ClosureCall
    //     0xcd6620: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd6624: ldur            x2, [x0, #0x1f]
    //     0xcd6628: blr             x2
    // 0xcd662c: ldur            x1, [fp, #-0x18]
    // 0xcd6630: StoreField: r1->field_23 = r0
    //     0xcd6630: stur            w0, [x1, #0x23]
    //     0xcd6634: ldurb           w16, [x1, #-1]
    //     0xcd6638: ldurb           w17, [x0, #-1]
    //     0xcd663c: and             x16, x17, x16, lsr #2
    //     0xcd6640: tst             x16, HEAP, lsr #32
    //     0xcd6644: b.eq            #0xcd664c
    //     0xcd6648: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcd664c: r0 = Null
    //     0xcd664c: mov             x0, NULL
    // 0xcd6650: LeaveFrame
    //     0xcd6650: mov             SP, fp
    //     0xcd6654: ldp             fp, lr, [SP], #0x10
    // 0xcd6658: ret
    //     0xcd6658: ret             
    // 0xcd665c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcd665c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcd6660: b               #0xcd65ac
    // 0xcd6664: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd6664: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd6668: SaveReg d0
    //     0xcd6668: str             q0, [SP, #-0x10]!
    // 0xcd666c: stp             x3, x4, [SP, #-0x10]!
    // 0xcd6670: SaveReg r0
    //     0xcd6670: str             x0, [SP, #-8]!
    // 0xcd6674: r0 = AllocateDouble()
    //     0xcd6674: bl              #0xec2254  ; AllocateDoubleStub
    // 0xcd6678: mov             x5, x0
    // 0xcd667c: RestoreReg r0
    //     0xcd667c: ldr             x0, [SP], #8
    // 0xcd6680: ldp             x3, x4, [SP], #0x10
    // 0xcd6684: RestoreReg d0
    //     0xcd6684: ldr             q0, [SP], #0x10
    // 0xcd6688: b               #0xcd65f0
  }
  [closure] Tween<double> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd668c, size: 0x58
    // 0xcd668c: EnterFrame
    //     0xcd668c: stp             fp, lr, [SP, #-0x10]!
    //     0xcd6690: mov             fp, SP
    // 0xcd6694: ldr             x0, [fp, #0x10]
    // 0xcd6698: r2 = Null
    //     0xcd6698: mov             x2, NULL
    // 0xcd669c: r1 = Null
    //     0xcd669c: mov             x1, NULL
    // 0xcd66a0: r4 = 60
    //     0xcd66a0: movz            x4, #0x3c
    // 0xcd66a4: branchIfSmi(r0, 0xcd66b0)
    //     0xcd66a4: tbz             w0, #0, #0xcd66b0
    // 0xcd66a8: r4 = LoadClassIdInstr(r0)
    //     0xcd66a8: ldur            x4, [x0, #-1]
    //     0xcd66ac: ubfx            x4, x4, #0xc, #0x14
    // 0xcd66b0: cmp             x4, #0x3e
    // 0xcd66b4: b.eq            #0xcd66c8
    // 0xcd66b8: r8 = double
    //     0xcd66b8: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xcd66bc: r3 = Null
    //     0xcd66bc: add             x3, PP, #0x50, lsl #12  ; [pp+0x502c8] Null
    //     0xcd66c0: ldr             x3, [x3, #0x2c8]
    // 0xcd66c4: r0 = double()
    //     0xcd66c4: bl              #0xed4460  ; IsType_double_Stub
    // 0xcd66c8: r1 = <double>
    //     0xcd66c8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xcd66cc: r0 = Tween()
    //     0xcd66cc: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xcd66d0: ldr             x1, [fp, #0x10]
    // 0xcd66d4: StoreField: r0->field_b = r1
    //     0xcd66d4: stur            w1, [x0, #0xb]
    // 0xcd66d8: LeaveFrame
    //     0xcd66d8: mov             SP, fp
    //     0xcd66dc: ldp             fp, lr, [SP], #0x10
    // 0xcd66e0: ret
    //     0xcd66e0: ret             
  }
}

// class id: 4290, size: 0x24, field offset: 0x24
abstract class AnimatedWidgetBaseState<X0 bound ImplicitlyAnimatedWidget> extends ImplicitlyAnimatedWidgetState<X0 bound ImplicitlyAnimatedWidget> {

  _ initState(/* No info */) {
    // ** addr: 0x93652c, size: 0x84
    // 0x93652c: EnterFrame
    //     0x93652c: stp             fp, lr, [SP, #-0x10]!
    //     0x936530: mov             fp, SP
    // 0x936534: AllocStack(0x8)
    //     0x936534: sub             SP, SP, #8
    // 0x936538: SetupParameters(AnimatedWidgetBaseState<X0 bound ImplicitlyAnimatedWidget> this /* r1 => r0, fp-0x8 */)
    //     0x936538: mov             x0, x1
    //     0x93653c: stur            x1, [fp, #-8]
    // 0x936540: CheckStackOverflow
    //     0x936540: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x936544: cmp             SP, x16
    //     0x936548: b.ls            #0x9365a8
    // 0x93654c: mov             x1, x0
    // 0x936550: r0 = initState()
    //     0x936550: bl              #0x9366b8  ; [package:flutter/src/widgets/implicit_animations.dart] ImplicitlyAnimatedWidgetState::initState
    // 0x936554: ldur            x1, [fp, #-8]
    // 0x936558: LoadField: r0 = r1->field_1b
    //     0x936558: ldur            w0, [x1, #0x1b]
    // 0x93655c: DecompressPointer r0
    //     0x93655c: add             x0, x0, HEAP, lsl #32
    // 0x936560: r16 = Sentinel
    //     0x936560: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x936564: cmp             w0, w16
    // 0x936568: b.ne            #0x936578
    // 0x93656c: r2 = _controller
    //     0x93656c: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f8] Field <ImplicitlyAnimatedWidgetState._controller@291443363>: late final (offset: 0x1c)
    //     0x936570: ldr             x2, [x2, #0xf8]
    // 0x936574: r0 = InitLateFinalInstanceField()
    //     0x936574: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x936578: ldur            x2, [fp, #-8]
    // 0x93657c: r1 = Function '_handleAnimationChanged@291443363':.
    //     0x93657c: add             x1, PP, #0x50, lsl #12  ; [pp+0x502d8] AnonymousClosure: (0x9365b0), in [package:flutter/src/widgets/implicit_animations.dart] AnimatedWidgetBaseState::_handleAnimationChanged (0x9365e8)
    //     0x936580: ldr             x1, [x1, #0x2d8]
    // 0x936584: stur            x0, [fp, #-8]
    // 0x936588: r0 = AllocateClosure()
    //     0x936588: bl              #0xec1630  ; AllocateClosureStub
    // 0x93658c: ldur            x1, [fp, #-8]
    // 0x936590: mov             x2, x0
    // 0x936594: r0 = addActionListener()
    //     0x936594: bl              #0xc680d4  ; [package:flutter/src/widgets/actions.dart] Action::addActionListener
    // 0x936598: r0 = Null
    //     0x936598: mov             x0, NULL
    // 0x93659c: LeaveFrame
    //     0x93659c: mov             SP, fp
    //     0x9365a0: ldp             fp, lr, [SP], #0x10
    // 0x9365a4: ret
    //     0x9365a4: ret             
    // 0x9365a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9365a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9365ac: b               #0x93654c
  }
  [closure] void _handleAnimationChanged(dynamic) {
    // ** addr: 0x9365b0, size: 0x38
    // 0x9365b0: EnterFrame
    //     0x9365b0: stp             fp, lr, [SP, #-0x10]!
    //     0x9365b4: mov             fp, SP
    // 0x9365b8: ldr             x0, [fp, #0x10]
    // 0x9365bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9365bc: ldur            w1, [x0, #0x17]
    // 0x9365c0: DecompressPointer r1
    //     0x9365c0: add             x1, x1, HEAP, lsl #32
    // 0x9365c4: CheckStackOverflow
    //     0x9365c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9365c8: cmp             SP, x16
    //     0x9365cc: b.ls            #0x9365e0
    // 0x9365d0: r0 = _handleAnimationChanged()
    //     0x9365d0: bl              #0x9365e8  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedWidgetBaseState::_handleAnimationChanged
    // 0x9365d4: LeaveFrame
    //     0x9365d4: mov             SP, fp
    //     0x9365d8: ldp             fp, lr, [SP], #0x10
    // 0x9365dc: ret
    //     0x9365dc: ret             
    // 0x9365e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9365e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9365e4: b               #0x9365d0
  }
  _ _handleAnimationChanged(/* No info */) {
    // ** addr: 0x9365e8, size: 0x54
    // 0x9365e8: EnterFrame
    //     0x9365e8: stp             fp, lr, [SP, #-0x10]!
    //     0x9365ec: mov             fp, SP
    // 0x9365f0: AllocStack(0x8)
    //     0x9365f0: sub             SP, SP, #8
    // 0x9365f4: SetupParameters(AnimatedWidgetBaseState<X0 bound ImplicitlyAnimatedWidget> this /* r1 => r0, fp-0x8 */)
    //     0x9365f4: mov             x0, x1
    //     0x9365f8: stur            x1, [fp, #-8]
    // 0x9365fc: CheckStackOverflow
    //     0x9365fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x936600: cmp             SP, x16
    //     0x936604: b.ls            #0x936634
    // 0x936608: r1 = Function '<anonymous closure>':.
    //     0x936608: add             x1, PP, #0x50, lsl #12  ; [pp+0x502e0] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x93660c: ldr             x1, [x1, #0x2e0]
    // 0x936610: r2 = Null
    //     0x936610: mov             x2, NULL
    // 0x936614: r0 = AllocateClosure()
    //     0x936614: bl              #0xec1630  ; AllocateClosureStub
    // 0x936618: ldur            x1, [fp, #-8]
    // 0x93661c: mov             x2, x0
    // 0x936620: r0 = setState()
    //     0x936620: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x936624: r0 = Null
    //     0x936624: mov             x0, NULL
    // 0x936628: LeaveFrame
    //     0x936628: mov             SP, fp
    //     0x93662c: ldp             fp, lr, [SP], #0x10
    // 0x936630: ret
    //     0x936630: ret             
    // 0x936634: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x936634: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x936638: b               #0x936608
  }
}

// class id: 4291, size: 0x34, field offset: 0x24
class _AnimatedPhysicalModelState extends AnimatedWidgetBaseState<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x9fbc10, size: 0x18c
    // 0x9fbc10: EnterFrame
    //     0x9fbc10: stp             fp, lr, [SP, #-0x10]!
    //     0x9fbc14: mov             fp, SP
    // 0x9fbc18: AllocStack(0x30)
    //     0x9fbc18: sub             SP, SP, #0x30
    // 0x9fbc1c: SetupParameters(_AnimatedPhysicalModelState this /* r1 => r0, fp-0x18 */)
    //     0x9fbc1c: mov             x0, x1
    //     0x9fbc20: stur            x1, [fp, #-0x18]
    // 0x9fbc24: CheckStackOverflow
    //     0x9fbc24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9fbc28: cmp             SP, x16
    //     0x9fbc2c: b.ls            #0x9fbd78
    // 0x9fbc30: LoadField: r1 = r0->field_b
    //     0x9fbc30: ldur            w1, [x0, #0xb]
    // 0x9fbc34: DecompressPointer r1
    //     0x9fbc34: add             x1, x1, HEAP, lsl #32
    // 0x9fbc38: cmp             w1, NULL
    // 0x9fbc3c: b.eq            #0x9fbd80
    // 0x9fbc40: LoadField: r2 = r1->field_1f
    //     0x9fbc40: ldur            w2, [x1, #0x1f]
    // 0x9fbc44: DecompressPointer r2
    //     0x9fbc44: add             x2, x2, HEAP, lsl #32
    // 0x9fbc48: stur            x2, [fp, #-0x10]
    // 0x9fbc4c: LoadField: r3 = r0->field_23
    //     0x9fbc4c: ldur            w3, [x0, #0x23]
    // 0x9fbc50: DecompressPointer r3
    //     0x9fbc50: add             x3, x3, HEAP, lsl #32
    // 0x9fbc54: stur            x3, [fp, #-8]
    // 0x9fbc58: cmp             w3, NULL
    // 0x9fbc5c: b.eq            #0x9fbd84
    // 0x9fbc60: mov             x1, x0
    // 0x9fbc64: LoadField: r0 = r1->field_1f
    //     0x9fbc64: ldur            w0, [x1, #0x1f]
    // 0x9fbc68: DecompressPointer r0
    //     0x9fbc68: add             x0, x0, HEAP, lsl #32
    // 0x9fbc6c: r16 = Sentinel
    //     0x9fbc6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9fbc70: cmp             w0, w16
    // 0x9fbc74: b.ne            #0x9fbc84
    // 0x9fbc78: r2 = _animation
    //     0x9fbc78: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f0] Field <ImplicitlyAnimatedWidgetState._animation@291443363>: late (offset: 0x20)
    //     0x9fbc7c: ldr             x2, [x2, #0xf0]
    // 0x9fbc80: r0 = InitLateInstanceField()
    //     0x9fbc80: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0x9fbc84: ldur            x1, [fp, #-8]
    // 0x9fbc88: mov             x2, x0
    // 0x9fbc8c: r0 = evaluate()
    //     0x9fbc8c: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9fbc90: mov             x3, x0
    // 0x9fbc94: ldur            x0, [fp, #-0x18]
    // 0x9fbc98: stur            x3, [fp, #-8]
    // 0x9fbc9c: LoadField: r1 = r0->field_27
    //     0x9fbc9c: ldur            w1, [x0, #0x27]
    // 0x9fbca0: DecompressPointer r1
    //     0x9fbca0: add             x1, x1, HEAP, lsl #32
    // 0x9fbca4: cmp             w1, NULL
    // 0x9fbca8: b.eq            #0x9fbd88
    // 0x9fbcac: LoadField: r2 = r0->field_1f
    //     0x9fbcac: ldur            w2, [x0, #0x1f]
    // 0x9fbcb0: DecompressPointer r2
    //     0x9fbcb0: add             x2, x2, HEAP, lsl #32
    // 0x9fbcb4: r0 = evaluate()
    //     0x9fbcb4: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9fbcb8: mov             x3, x0
    // 0x9fbcbc: ldur            x0, [fp, #-0x18]
    // 0x9fbcc0: stur            x3, [fp, #-0x28]
    // 0x9fbcc4: LoadField: r1 = r0->field_b
    //     0x9fbcc4: ldur            w1, [x0, #0xb]
    // 0x9fbcc8: DecompressPointer r1
    //     0x9fbcc8: add             x1, x1, HEAP, lsl #32
    // 0x9fbccc: cmp             w1, NULL
    // 0x9fbcd0: b.eq            #0x9fbd8c
    // 0x9fbcd4: LoadField: r4 = r1->field_2f
    //     0x9fbcd4: ldur            w4, [x1, #0x2f]
    // 0x9fbcd8: DecompressPointer r4
    //     0x9fbcd8: add             x4, x4, HEAP, lsl #32
    // 0x9fbcdc: stur            x4, [fp, #-0x20]
    // 0x9fbce0: LoadField: r1 = r0->field_2f
    //     0x9fbce0: ldur            w1, [x0, #0x2f]
    // 0x9fbce4: DecompressPointer r1
    //     0x9fbce4: add             x1, x1, HEAP, lsl #32
    // 0x9fbce8: cmp             w1, NULL
    // 0x9fbcec: b.eq            #0x9fbd90
    // 0x9fbcf0: LoadField: r2 = r0->field_1f
    //     0x9fbcf0: ldur            w2, [x0, #0x1f]
    // 0x9fbcf4: DecompressPointer r2
    //     0x9fbcf4: add             x2, x2, HEAP, lsl #32
    // 0x9fbcf8: r0 = evaluate()
    //     0x9fbcf8: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9fbcfc: stur            x0, [fp, #-0x30]
    // 0x9fbd00: cmp             w0, NULL
    // 0x9fbd04: b.eq            #0x9fbd94
    // 0x9fbd08: ldur            x1, [fp, #-0x18]
    // 0x9fbd0c: LoadField: r2 = r1->field_b
    //     0x9fbd0c: ldur            w2, [x1, #0xb]
    // 0x9fbd10: DecompressPointer r2
    //     0x9fbd10: add             x2, x2, HEAP, lsl #32
    // 0x9fbd14: cmp             w2, NULL
    // 0x9fbd18: b.eq            #0x9fbd98
    // 0x9fbd1c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x9fbd1c: ldur            w1, [x2, #0x17]
    // 0x9fbd20: DecompressPointer r1
    //     0x9fbd20: add             x1, x1, HEAP, lsl #32
    // 0x9fbd24: stur            x1, [fp, #-0x18]
    // 0x9fbd28: r0 = PhysicalModel()
    //     0x9fbd28: bl              #0x9fbd9c  ; AllocatePhysicalModelStub -> PhysicalModel (size=0x2c)
    // 0x9fbd2c: r1 = Instance_BoxShape
    //     0x9fbd2c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0x9fbd30: ldr             x1, [x1, #0xca8]
    // 0x9fbd34: StoreField: r0->field_f = r1
    //     0x9fbd34: stur            w1, [x0, #0xf]
    // 0x9fbd38: ldur            x1, [fp, #-0x10]
    // 0x9fbd3c: StoreField: r0->field_13 = r1
    //     0x9fbd3c: stur            w1, [x0, #0x13]
    // 0x9fbd40: ldur            x1, [fp, #-8]
    // 0x9fbd44: ArrayStore: r0[0] = r1  ; List_4
    //     0x9fbd44: stur            w1, [x0, #0x17]
    // 0x9fbd48: ldur            x1, [fp, #-0x28]
    // 0x9fbd4c: LoadField: d0 = r1->field_7
    //     0x9fbd4c: ldur            d0, [x1, #7]
    // 0x9fbd50: StoreField: r0->field_1b = d0
    //     0x9fbd50: stur            d0, [x0, #0x1b]
    // 0x9fbd54: ldur            x1, [fp, #-0x20]
    // 0x9fbd58: StoreField: r0->field_23 = r1
    //     0x9fbd58: stur            w1, [x0, #0x23]
    // 0x9fbd5c: ldur            x1, [fp, #-0x30]
    // 0x9fbd60: StoreField: r0->field_27 = r1
    //     0x9fbd60: stur            w1, [x0, #0x27]
    // 0x9fbd64: ldur            x1, [fp, #-0x18]
    // 0x9fbd68: StoreField: r0->field_b = r1
    //     0x9fbd68: stur            w1, [x0, #0xb]
    // 0x9fbd6c: LeaveFrame
    //     0x9fbd6c: mov             SP, fp
    //     0x9fbd70: ldp             fp, lr, [SP], #0x10
    // 0x9fbd74: ret
    //     0x9fbd74: ret             
    // 0x9fbd78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9fbd78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9fbd7c: b               #0x9fbc30
    // 0x9fbd80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbd80: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fbd84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbd84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fbd88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbd88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fbd8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbd8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fbd90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbd90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fbd94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbd94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fbd98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbd98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ forEachTween(/* No info */) {
    // ** addr: 0xcd6168, size: 0x278
    // 0xcd6168: EnterFrame
    //     0xcd6168: stp             fp, lr, [SP, #-0x10]!
    //     0xcd616c: mov             fp, SP
    // 0xcd6170: AllocStack(0x40)
    //     0xcd6170: sub             SP, SP, #0x40
    // 0xcd6174: SetupParameters(_AnimatedPhysicalModelState this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xcd6174: mov             x3, x1
    //     0xcd6178: mov             x0, x2
    //     0xcd617c: stur            x1, [fp, #-0x10]
    //     0xcd6180: stur            x2, [fp, #-0x18]
    // 0xcd6184: CheckStackOverflow
    //     0xcd6184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcd6188: cmp             SP, x16
    //     0xcd618c: b.ls            #0xcd63ac
    // 0xcd6190: LoadField: r4 = r3->field_23
    //     0xcd6190: ldur            w4, [x3, #0x23]
    // 0xcd6194: DecompressPointer r4
    //     0xcd6194: add             x4, x4, HEAP, lsl #32
    // 0xcd6198: stur            x4, [fp, #-8]
    // 0xcd619c: LoadField: r1 = r3->field_b
    //     0xcd619c: ldur            w1, [x3, #0xb]
    // 0xcd61a0: DecompressPointer r1
    //     0xcd61a0: add             x1, x1, HEAP, lsl #32
    // 0xcd61a4: cmp             w1, NULL
    // 0xcd61a8: b.eq            #0xcd63b4
    // 0xcd61ac: r1 = Function '<anonymous closure>':.
    //     0xcd61ac: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f00] AnonymousClosure: (0xcd6518), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedPhysicalModelState::forEachTween (0xcd6168)
    //     0xcd61b0: ldr             x1, [x1, #0xf00]
    // 0xcd61b4: r2 = Null
    //     0xcd61b4: mov             x2, NULL
    // 0xcd61b8: r0 = AllocateClosure()
    //     0xcd61b8: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd61bc: ldur            x16, [fp, #-0x18]
    // 0xcd61c0: ldur            lr, [fp, #-8]
    // 0xcd61c4: stp             lr, x16, [SP, #0x10]
    // 0xcd61c8: r16 = Instance_BorderRadius
    //     0xcd61c8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b68] Obj!BorderRadius@e13a71
    //     0xcd61cc: ldr             x16, [x16, #0xb68]
    // 0xcd61d0: stp             x0, x16, [SP]
    // 0xcd61d4: ldur            x0, [fp, #-0x18]
    // 0xcd61d8: ClosureCall
    //     0xcd61d8: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd61dc: ldur            x2, [x0, #0x1f]
    //     0xcd61e0: blr             x2
    // 0xcd61e4: ldur            x3, [fp, #-0x10]
    // 0xcd61e8: StoreField: r3->field_23 = r0
    //     0xcd61e8: stur            w0, [x3, #0x23]
    //     0xcd61ec: ldurb           w16, [x3, #-1]
    //     0xcd61f0: ldurb           w17, [x0, #-1]
    //     0xcd61f4: and             x16, x17, x16, lsr #2
    //     0xcd61f8: tst             x16, HEAP, lsr #32
    //     0xcd61fc: b.eq            #0xcd6204
    //     0xcd6200: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xcd6204: LoadField: r0 = r3->field_27
    //     0xcd6204: ldur            w0, [x3, #0x27]
    // 0xcd6208: DecompressPointer r0
    //     0xcd6208: add             x0, x0, HEAP, lsl #32
    // 0xcd620c: stur            x0, [fp, #-0x20]
    // 0xcd6210: LoadField: r1 = r3->field_b
    //     0xcd6210: ldur            w1, [x3, #0xb]
    // 0xcd6214: DecompressPointer r1
    //     0xcd6214: add             x1, x1, HEAP, lsl #32
    // 0xcd6218: cmp             w1, NULL
    // 0xcd621c: b.eq            #0xcd63b8
    // 0xcd6220: LoadField: d0 = r1->field_27
    //     0xcd6220: ldur            d0, [x1, #0x27]
    // 0xcd6224: r4 = inline_Allocate_Double()
    //     0xcd6224: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0xcd6228: add             x4, x4, #0x10
    //     0xcd622c: cmp             x1, x4
    //     0xcd6230: b.ls            #0xcd63bc
    //     0xcd6234: str             x4, [THR, #0x50]  ; THR::top
    //     0xcd6238: sub             x4, x4, #0xf
    //     0xcd623c: movz            x1, #0xe15c
    //     0xcd6240: movk            x1, #0x3, lsl #16
    //     0xcd6244: stur            x1, [x4, #-1]
    // 0xcd6248: StoreField: r4->field_7 = d0
    //     0xcd6248: stur            d0, [x4, #7]
    // 0xcd624c: stur            x4, [fp, #-8]
    // 0xcd6250: r1 = Function '<anonymous closure>':.
    //     0xcd6250: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f08] AnonymousClosure: (0xcd64c0), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedPhysicalModelState::forEachTween (0xcd6168)
    //     0xcd6254: ldr             x1, [x1, #0xf08]
    // 0xcd6258: r2 = Null
    //     0xcd6258: mov             x2, NULL
    // 0xcd625c: r0 = AllocateClosure()
    //     0xcd625c: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd6260: ldur            x16, [fp, #-0x18]
    // 0xcd6264: ldur            lr, [fp, #-0x20]
    // 0xcd6268: stp             lr, x16, [SP, #0x10]
    // 0xcd626c: ldur            x16, [fp, #-8]
    // 0xcd6270: stp             x0, x16, [SP]
    // 0xcd6274: ldur            x0, [fp, #-0x18]
    // 0xcd6278: ClosureCall
    //     0xcd6278: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd627c: ldur            x2, [x0, #0x1f]
    //     0xcd6280: blr             x2
    // 0xcd6284: ldur            x3, [fp, #-0x10]
    // 0xcd6288: StoreField: r3->field_27 = r0
    //     0xcd6288: stur            w0, [x3, #0x27]
    //     0xcd628c: ldurb           w16, [x3, #-1]
    //     0xcd6290: ldurb           w17, [x0, #-1]
    //     0xcd6294: and             x16, x17, x16, lsr #2
    //     0xcd6298: tst             x16, HEAP, lsr #32
    //     0xcd629c: b.eq            #0xcd62a4
    //     0xcd62a0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xcd62a4: LoadField: r0 = r3->field_2b
    //     0xcd62a4: ldur            w0, [x3, #0x2b]
    // 0xcd62a8: DecompressPointer r0
    //     0xcd62a8: add             x0, x0, HEAP, lsl #32
    // 0xcd62ac: stur            x0, [fp, #-0x20]
    // 0xcd62b0: LoadField: r1 = r3->field_b
    //     0xcd62b0: ldur            w1, [x3, #0xb]
    // 0xcd62b4: DecompressPointer r1
    //     0xcd62b4: add             x1, x1, HEAP, lsl #32
    // 0xcd62b8: cmp             w1, NULL
    // 0xcd62bc: b.eq            #0xcd63d8
    // 0xcd62c0: LoadField: r4 = r1->field_2f
    //     0xcd62c0: ldur            w4, [x1, #0x2f]
    // 0xcd62c4: DecompressPointer r4
    //     0xcd62c4: add             x4, x4, HEAP, lsl #32
    // 0xcd62c8: stur            x4, [fp, #-8]
    // 0xcd62cc: r1 = Function '<anonymous closure>':.
    //     0xcd62cc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f10] AnonymousClosure: (0xcd6450), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedPhysicalModelState::forEachTween (0xcd6168)
    //     0xcd62d0: ldr             x1, [x1, #0xf10]
    // 0xcd62d4: r2 = Null
    //     0xcd62d4: mov             x2, NULL
    // 0xcd62d8: r0 = AllocateClosure()
    //     0xcd62d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd62dc: ldur            x16, [fp, #-0x18]
    // 0xcd62e0: ldur            lr, [fp, #-0x20]
    // 0xcd62e4: stp             lr, x16, [SP, #0x10]
    // 0xcd62e8: ldur            x16, [fp, #-8]
    // 0xcd62ec: stp             x0, x16, [SP]
    // 0xcd62f0: ldur            x0, [fp, #-0x18]
    // 0xcd62f4: ClosureCall
    //     0xcd62f4: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd62f8: ldur            x2, [x0, #0x1f]
    //     0xcd62fc: blr             x2
    // 0xcd6300: ldur            x3, [fp, #-0x10]
    // 0xcd6304: StoreField: r3->field_2b = r0
    //     0xcd6304: stur            w0, [x3, #0x2b]
    //     0xcd6308: ldurb           w16, [x3, #-1]
    //     0xcd630c: ldurb           w17, [x0, #-1]
    //     0xcd6310: and             x16, x17, x16, lsr #2
    //     0xcd6314: tst             x16, HEAP, lsr #32
    //     0xcd6318: b.eq            #0xcd6320
    //     0xcd631c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xcd6320: LoadField: r0 = r3->field_2f
    //     0xcd6320: ldur            w0, [x3, #0x2f]
    // 0xcd6324: DecompressPointer r0
    //     0xcd6324: add             x0, x0, HEAP, lsl #32
    // 0xcd6328: stur            x0, [fp, #-0x20]
    // 0xcd632c: LoadField: r1 = r3->field_b
    //     0xcd632c: ldur            w1, [x3, #0xb]
    // 0xcd6330: DecompressPointer r1
    //     0xcd6330: add             x1, x1, HEAP, lsl #32
    // 0xcd6334: cmp             w1, NULL
    // 0xcd6338: b.eq            #0xcd63dc
    // 0xcd633c: LoadField: r4 = r1->field_37
    //     0xcd633c: ldur            w4, [x1, #0x37]
    // 0xcd6340: DecompressPointer r4
    //     0xcd6340: add             x4, x4, HEAP, lsl #32
    // 0xcd6344: stur            x4, [fp, #-8]
    // 0xcd6348: r1 = Function '<anonymous closure>':.
    //     0xcd6348: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f18] AnonymousClosure: (0xcd63e0), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedPhysicalModelState::forEachTween (0xcd6168)
    //     0xcd634c: ldr             x1, [x1, #0xf18]
    // 0xcd6350: r2 = Null
    //     0xcd6350: mov             x2, NULL
    // 0xcd6354: r0 = AllocateClosure()
    //     0xcd6354: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd6358: ldur            x16, [fp, #-0x18]
    // 0xcd635c: ldur            lr, [fp, #-0x20]
    // 0xcd6360: stp             lr, x16, [SP, #0x10]
    // 0xcd6364: ldur            x16, [fp, #-8]
    // 0xcd6368: stp             x0, x16, [SP]
    // 0xcd636c: ldur            x0, [fp, #-0x18]
    // 0xcd6370: ClosureCall
    //     0xcd6370: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd6374: ldur            x2, [x0, #0x1f]
    //     0xcd6378: blr             x2
    // 0xcd637c: ldur            x1, [fp, #-0x10]
    // 0xcd6380: StoreField: r1->field_2f = r0
    //     0xcd6380: stur            w0, [x1, #0x2f]
    //     0xcd6384: ldurb           w16, [x1, #-1]
    //     0xcd6388: ldurb           w17, [x0, #-1]
    //     0xcd638c: and             x16, x17, x16, lsr #2
    //     0xcd6390: tst             x16, HEAP, lsr #32
    //     0xcd6394: b.eq            #0xcd639c
    //     0xcd6398: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcd639c: r0 = Null
    //     0xcd639c: mov             x0, NULL
    // 0xcd63a0: LeaveFrame
    //     0xcd63a0: mov             SP, fp
    //     0xcd63a4: ldp             fp, lr, [SP], #0x10
    // 0xcd63a8: ret
    //     0xcd63a8: ret             
    // 0xcd63ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcd63ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcd63b0: b               #0xcd6190
    // 0xcd63b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd63b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd63b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd63b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd63bc: SaveReg d0
    //     0xcd63bc: str             q0, [SP, #-0x10]!
    // 0xcd63c0: stp             x0, x3, [SP, #-0x10]!
    // 0xcd63c4: r0 = AllocateDouble()
    //     0xcd63c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xcd63c8: mov             x4, x0
    // 0xcd63cc: ldp             x0, x3, [SP], #0x10
    // 0xcd63d0: RestoreReg d0
    //     0xcd63d0: ldr             q0, [SP], #0x10
    // 0xcd63d4: b               #0xcd6248
    // 0xcd63d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd63d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd63dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd63dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] ColorTween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd63e0, size: 0x70
    // 0xcd63e0: EnterFrame
    //     0xcd63e0: stp             fp, lr, [SP, #-0x10]!
    //     0xcd63e4: mov             fp, SP
    // 0xcd63e8: ldr             x0, [fp, #0x10]
    // 0xcd63ec: r2 = Null
    //     0xcd63ec: mov             x2, NULL
    // 0xcd63f0: r1 = Null
    //     0xcd63f0: mov             x1, NULL
    // 0xcd63f4: r4 = 60
    //     0xcd63f4: movz            x4, #0x3c
    // 0xcd63f8: branchIfSmi(r0, 0xcd6404)
    //     0xcd63f8: tbz             w0, #0, #0xcd6404
    // 0xcd63fc: r4 = LoadClassIdInstr(r0)
    //     0xcd63fc: ldur            x4, [x0, #-1]
    //     0xcd6400: ubfx            x4, x4, #0xc, #0x14
    // 0xcd6404: cmp             x4, #0xfeb
    // 0xcd6408: b.eq            #0xcd6430
    // 0xcd640c: r17 = -6078
    //     0xcd640c: movn            x17, #0x17bd
    // 0xcd6410: add             x4, x4, x17
    // 0xcd6414: cmp             x4, #5
    // 0xcd6418: b.ls            #0xcd6430
    // 0xcd641c: r8 = Color
    //     0xcd641c: add             x8, PP, #0x54, lsl #12  ; [pp+0x54bf0] Type: Color
    //     0xcd6420: ldr             x8, [x8, #0xbf0]
    // 0xcd6424: r3 = Null
    //     0xcd6424: add             x3, PP, #0x56, lsl #12  ; [pp+0x56f20] Null
    //     0xcd6428: ldr             x3, [x3, #0xf20]
    // 0xcd642c: r0 = Color()
    //     0xcd642c: bl              #0x624c5c  ; IsType_Color_Stub
    // 0xcd6430: r1 = <Color?>
    //     0xcd6430: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xcd6434: ldr             x1, [x1, #0x98]
    // 0xcd6438: r0 = ColorTween()
    //     0xcd6438: bl              #0x7e3594  ; AllocateColorTweenStub -> ColorTween (size=0x14)
    // 0xcd643c: ldr             x1, [fp, #0x10]
    // 0xcd6440: StoreField: r0->field_b = r1
    //     0xcd6440: stur            w1, [x0, #0xb]
    // 0xcd6444: LeaveFrame
    //     0xcd6444: mov             SP, fp
    //     0xcd6448: ldp             fp, lr, [SP], #0x10
    // 0xcd644c: ret
    //     0xcd644c: ret             
  }
  [closure] ColorTween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd6450, size: 0x70
    // 0xcd6450: EnterFrame
    //     0xcd6450: stp             fp, lr, [SP, #-0x10]!
    //     0xcd6454: mov             fp, SP
    // 0xcd6458: ldr             x0, [fp, #0x10]
    // 0xcd645c: r2 = Null
    //     0xcd645c: mov             x2, NULL
    // 0xcd6460: r1 = Null
    //     0xcd6460: mov             x1, NULL
    // 0xcd6464: r4 = 60
    //     0xcd6464: movz            x4, #0x3c
    // 0xcd6468: branchIfSmi(r0, 0xcd6474)
    //     0xcd6468: tbz             w0, #0, #0xcd6474
    // 0xcd646c: r4 = LoadClassIdInstr(r0)
    //     0xcd646c: ldur            x4, [x0, #-1]
    //     0xcd6470: ubfx            x4, x4, #0xc, #0x14
    // 0xcd6474: cmp             x4, #0xfeb
    // 0xcd6478: b.eq            #0xcd64a0
    // 0xcd647c: r17 = -6078
    //     0xcd647c: movn            x17, #0x17bd
    // 0xcd6480: add             x4, x4, x17
    // 0xcd6484: cmp             x4, #5
    // 0xcd6488: b.ls            #0xcd64a0
    // 0xcd648c: r8 = Color
    //     0xcd648c: add             x8, PP, #0x54, lsl #12  ; [pp+0x54bf0] Type: Color
    //     0xcd6490: ldr             x8, [x8, #0xbf0]
    // 0xcd6494: r3 = Null
    //     0xcd6494: add             x3, PP, #0x56, lsl #12  ; [pp+0x56f30] Null
    //     0xcd6498: ldr             x3, [x3, #0xf30]
    // 0xcd649c: r0 = Color()
    //     0xcd649c: bl              #0x624c5c  ; IsType_Color_Stub
    // 0xcd64a0: r1 = <Color?>
    //     0xcd64a0: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xcd64a4: ldr             x1, [x1, #0x98]
    // 0xcd64a8: r0 = ColorTween()
    //     0xcd64a8: bl              #0x7e3594  ; AllocateColorTweenStub -> ColorTween (size=0x14)
    // 0xcd64ac: ldr             x1, [fp, #0x10]
    // 0xcd64b0: StoreField: r0->field_b = r1
    //     0xcd64b0: stur            w1, [x0, #0xb]
    // 0xcd64b4: LeaveFrame
    //     0xcd64b4: mov             SP, fp
    //     0xcd64b8: ldp             fp, lr, [SP], #0x10
    // 0xcd64bc: ret
    //     0xcd64bc: ret             
  }
  [closure] Tween<double> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd64c0, size: 0x58
    // 0xcd64c0: EnterFrame
    //     0xcd64c0: stp             fp, lr, [SP, #-0x10]!
    //     0xcd64c4: mov             fp, SP
    // 0xcd64c8: ldr             x0, [fp, #0x10]
    // 0xcd64cc: r2 = Null
    //     0xcd64cc: mov             x2, NULL
    // 0xcd64d0: r1 = Null
    //     0xcd64d0: mov             x1, NULL
    // 0xcd64d4: r4 = 60
    //     0xcd64d4: movz            x4, #0x3c
    // 0xcd64d8: branchIfSmi(r0, 0xcd64e4)
    //     0xcd64d8: tbz             w0, #0, #0xcd64e4
    // 0xcd64dc: r4 = LoadClassIdInstr(r0)
    //     0xcd64dc: ldur            x4, [x0, #-1]
    //     0xcd64e0: ubfx            x4, x4, #0xc, #0x14
    // 0xcd64e4: cmp             x4, #0x3e
    // 0xcd64e8: b.eq            #0xcd64fc
    // 0xcd64ec: r8 = double
    //     0xcd64ec: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xcd64f0: r3 = Null
    //     0xcd64f0: add             x3, PP, #0x56, lsl #12  ; [pp+0x56f40] Null
    //     0xcd64f4: ldr             x3, [x3, #0xf40]
    // 0xcd64f8: r0 = double()
    //     0xcd64f8: bl              #0xed4460  ; IsType_double_Stub
    // 0xcd64fc: r1 = <double>
    //     0xcd64fc: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xcd6500: r0 = Tween()
    //     0xcd6500: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xcd6504: ldr             x1, [fp, #0x10]
    // 0xcd6508: StoreField: r0->field_b = r1
    //     0xcd6508: stur            w1, [x0, #0xb]
    // 0xcd650c: LeaveFrame
    //     0xcd650c: mov             SP, fp
    //     0xcd6510: ldp             fp, lr, [SP], #0x10
    // 0xcd6514: ret
    //     0xcd6514: ret             
  }
  [closure] BorderRadiusTween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd6518, size: 0x60
    // 0xcd6518: EnterFrame
    //     0xcd6518: stp             fp, lr, [SP, #-0x10]!
    //     0xcd651c: mov             fp, SP
    // 0xcd6520: ldr             x0, [fp, #0x10]
    // 0xcd6524: r2 = Null
    //     0xcd6524: mov             x2, NULL
    // 0xcd6528: r1 = Null
    //     0xcd6528: mov             x1, NULL
    // 0xcd652c: r4 = 60
    //     0xcd652c: movz            x4, #0x3c
    // 0xcd6530: branchIfSmi(r0, 0xcd653c)
    //     0xcd6530: tbz             w0, #0, #0xcd653c
    // 0xcd6534: r4 = LoadClassIdInstr(r0)
    //     0xcd6534: ldur            x4, [x0, #-1]
    //     0xcd6538: ubfx            x4, x4, #0xc, #0x14
    // 0xcd653c: cmp             x4, #0xcc3
    // 0xcd6540: b.eq            #0xcd6558
    // 0xcd6544: r8 = BorderRadius
    //     0xcd6544: add             x8, PP, #0x39, lsl #12  ; [pp+0x39668] Type: BorderRadius
    //     0xcd6548: ldr             x8, [x8, #0x668]
    // 0xcd654c: r3 = Null
    //     0xcd654c: add             x3, PP, #0x56, lsl #12  ; [pp+0x56f50] Null
    //     0xcd6550: ldr             x3, [x3, #0xf50]
    // 0xcd6554: r0 = BorderRadius()
    //     0xcd6554: bl              #0x63d138  ; IsType_BorderRadius_Stub
    // 0xcd6558: r1 = <BorderRadius?>
    //     0xcd6558: add             x1, PP, #0x56, lsl #12  ; [pp+0x56f60] TypeArguments: <BorderRadius?>
    //     0xcd655c: ldr             x1, [x1, #0xf60]
    // 0xcd6560: r0 = BorderRadiusTween()
    //     0xcd6560: bl              #0xcd6578  ; AllocateBorderRadiusTweenStub -> BorderRadiusTween (size=0x14)
    // 0xcd6564: ldr             x1, [fp, #0x10]
    // 0xcd6568: StoreField: r0->field_b = r1
    //     0xcd6568: stur            w1, [x0, #0xb]
    // 0xcd656c: LeaveFrame
    //     0xcd656c: mov             SP, fp
    //     0xcd6570: ldp             fp, lr, [SP], #0x10
    // 0xcd6574: ret
    //     0xcd6574: ret             
  }
}

// class id: 4292, size: 0x28, field offset: 0x24
class _AnimatedDefaultTextStyleState extends AnimatedWidgetBaseState<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x9fbb34, size: 0xdc
    // 0x9fbb34: EnterFrame
    //     0x9fbb34: stp             fp, lr, [SP, #-0x10]!
    //     0x9fbb38: mov             fp, SP
    // 0x9fbb3c: AllocStack(0x18)
    //     0x9fbb3c: sub             SP, SP, #0x18
    // 0x9fbb40: SetupParameters(_AnimatedDefaultTextStyleState this /* r1 => r0, fp-0x10 */)
    //     0x9fbb40: mov             x0, x1
    //     0x9fbb44: stur            x1, [fp, #-0x10]
    // 0x9fbb48: CheckStackOverflow
    //     0x9fbb48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9fbb4c: cmp             SP, x16
    //     0x9fbb50: b.ls            #0x9fbc00
    // 0x9fbb54: LoadField: r2 = r0->field_23
    //     0x9fbb54: ldur            w2, [x0, #0x23]
    // 0x9fbb58: DecompressPointer r2
    //     0x9fbb58: add             x2, x2, HEAP, lsl #32
    // 0x9fbb5c: stur            x2, [fp, #-8]
    // 0x9fbb60: cmp             w2, NULL
    // 0x9fbb64: b.eq            #0x9fbc08
    // 0x9fbb68: mov             x1, x0
    // 0x9fbb6c: LoadField: r0 = r1->field_1f
    //     0x9fbb6c: ldur            w0, [x1, #0x1f]
    // 0x9fbb70: DecompressPointer r0
    //     0x9fbb70: add             x0, x0, HEAP, lsl #32
    // 0x9fbb74: r16 = Sentinel
    //     0x9fbb74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9fbb78: cmp             w0, w16
    // 0x9fbb7c: b.ne            #0x9fbb8c
    // 0x9fbb80: r2 = _animation
    //     0x9fbb80: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f0] Field <ImplicitlyAnimatedWidgetState._animation@291443363>: late (offset: 0x20)
    //     0x9fbb84: ldr             x2, [x2, #0xf0]
    // 0x9fbb88: r0 = InitLateInstanceField()
    //     0x9fbb88: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0x9fbb8c: ldur            x1, [fp, #-8]
    // 0x9fbb90: mov             x2, x0
    // 0x9fbb94: r0 = evaluate()
    //     0x9fbb94: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9fbb98: mov             x1, x0
    // 0x9fbb9c: ldur            x0, [fp, #-0x10]
    // 0x9fbba0: stur            x1, [fp, #-0x18]
    // 0x9fbba4: LoadField: r2 = r0->field_b
    //     0x9fbba4: ldur            w2, [x0, #0xb]
    // 0x9fbba8: DecompressPointer r2
    //     0x9fbba8: add             x2, x2, HEAP, lsl #32
    // 0x9fbbac: cmp             w2, NULL
    // 0x9fbbb0: b.eq            #0x9fbc0c
    // 0x9fbbb4: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x9fbbb4: ldur            w0, [x2, #0x17]
    // 0x9fbbb8: DecompressPointer r0
    //     0x9fbbb8: add             x0, x0, HEAP, lsl #32
    // 0x9fbbbc: stur            x0, [fp, #-8]
    // 0x9fbbc0: r0 = DefaultTextStyle()
    //     0x9fbbc0: bl              #0x9d5004  ; AllocateDefaultTextStyleStub -> DefaultTextStyle (size=0x2c)
    // 0x9fbbc4: ldur            x1, [fp, #-0x18]
    // 0x9fbbc8: StoreField: r0->field_f = r1
    //     0x9fbbc8: stur            w1, [x0, #0xf]
    // 0x9fbbcc: r1 = true
    //     0x9fbbcc: add             x1, NULL, #0x20  ; true
    // 0x9fbbd0: ArrayStore: r0[0] = r1  ; List_4
    //     0x9fbbd0: stur            w1, [x0, #0x17]
    // 0x9fbbd4: r1 = Instance_TextOverflow
    //     0x9fbbd4: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0x9fbbd8: ldr             x1, [x1, #0xc60]
    // 0x9fbbdc: StoreField: r0->field_1b = r1
    //     0x9fbbdc: stur            w1, [x0, #0x1b]
    // 0x9fbbe0: r1 = Instance_TextWidthBasis
    //     0x9fbbe0: add             x1, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0x9fbbe4: ldr             x1, [x1, #0x1d8]
    // 0x9fbbe8: StoreField: r0->field_23 = r1
    //     0x9fbbe8: stur            w1, [x0, #0x23]
    // 0x9fbbec: ldur            x1, [fp, #-8]
    // 0x9fbbf0: StoreField: r0->field_b = r1
    //     0x9fbbf0: stur            w1, [x0, #0xb]
    // 0x9fbbf4: LeaveFrame
    //     0x9fbbf4: mov             SP, fp
    //     0x9fbbf8: ldp             fp, lr, [SP], #0x10
    // 0x9fbbfc: ret
    //     0x9fbbfc: ret             
    // 0x9fbc00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9fbc00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9fbc04: b               #0x9fbb54
    // 0x9fbc08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbc08: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fbc0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbc0c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ forEachTween(/* No info */) {
    // ** addr: 0xcd6038, size: 0xc0
    // 0xcd6038: EnterFrame
    //     0xcd6038: stp             fp, lr, [SP, #-0x10]!
    //     0xcd603c: mov             fp, SP
    // 0xcd6040: AllocStack(0x40)
    //     0xcd6040: sub             SP, SP, #0x40
    // 0xcd6044: SetupParameters(_AnimatedDefaultTextStyleState this /* r1 => r3, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */)
    //     0xcd6044: mov             x3, x1
    //     0xcd6048: mov             x0, x2
    //     0xcd604c: stur            x1, [fp, #-0x18]
    //     0xcd6050: stur            x2, [fp, #-0x20]
    // 0xcd6054: CheckStackOverflow
    //     0xcd6054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcd6058: cmp             SP, x16
    //     0xcd605c: b.ls            #0xcd60ec
    // 0xcd6060: LoadField: r4 = r3->field_23
    //     0xcd6060: ldur            w4, [x3, #0x23]
    // 0xcd6064: DecompressPointer r4
    //     0xcd6064: add             x4, x4, HEAP, lsl #32
    // 0xcd6068: stur            x4, [fp, #-0x10]
    // 0xcd606c: LoadField: r1 = r3->field_b
    //     0xcd606c: ldur            w1, [x3, #0xb]
    // 0xcd6070: DecompressPointer r1
    //     0xcd6070: add             x1, x1, HEAP, lsl #32
    // 0xcd6074: cmp             w1, NULL
    // 0xcd6078: b.eq            #0xcd60f4
    // 0xcd607c: LoadField: r5 = r1->field_1b
    //     0xcd607c: ldur            w5, [x1, #0x1b]
    // 0xcd6080: DecompressPointer r5
    //     0xcd6080: add             x5, x5, HEAP, lsl #32
    // 0xcd6084: stur            x5, [fp, #-8]
    // 0xcd6088: r1 = Function '<anonymous closure>':.
    //     0xcd6088: add             x1, PP, #0x50, lsl #12  ; [pp+0x502f8] AnonymousClosure: (0xcd60f8), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedDefaultTextStyleState::forEachTween (0xcd6038)
    //     0xcd608c: ldr             x1, [x1, #0x2f8]
    // 0xcd6090: r2 = Null
    //     0xcd6090: mov             x2, NULL
    // 0xcd6094: r0 = AllocateClosure()
    //     0xcd6094: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd6098: ldur            x16, [fp, #-0x20]
    // 0xcd609c: ldur            lr, [fp, #-0x10]
    // 0xcd60a0: stp             lr, x16, [SP, #0x10]
    // 0xcd60a4: ldur            x16, [fp, #-8]
    // 0xcd60a8: stp             x0, x16, [SP]
    // 0xcd60ac: ldur            x0, [fp, #-0x20]
    // 0xcd60b0: ClosureCall
    //     0xcd60b0: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd60b4: ldur            x2, [x0, #0x1f]
    //     0xcd60b8: blr             x2
    // 0xcd60bc: ldur            x1, [fp, #-0x18]
    // 0xcd60c0: StoreField: r1->field_23 = r0
    //     0xcd60c0: stur            w0, [x1, #0x23]
    //     0xcd60c4: ldurb           w16, [x1, #-1]
    //     0xcd60c8: ldurb           w17, [x0, #-1]
    //     0xcd60cc: and             x16, x17, x16, lsr #2
    //     0xcd60d0: tst             x16, HEAP, lsr #32
    //     0xcd60d4: b.eq            #0xcd60dc
    //     0xcd60d8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcd60dc: r0 = Null
    //     0xcd60dc: mov             x0, NULL
    // 0xcd60e0: LeaveFrame
    //     0xcd60e0: mov             SP, fp
    //     0xcd60e4: ldp             fp, lr, [SP], #0x10
    // 0xcd60e8: ret
    //     0xcd60e8: ret             
    // 0xcd60ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcd60ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcd60f0: b               #0xcd6060
    // 0xcd60f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd60f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] TextStyleTween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd60f8, size: 0x64
    // 0xcd60f8: EnterFrame
    //     0xcd60f8: stp             fp, lr, [SP, #-0x10]!
    //     0xcd60fc: mov             fp, SP
    // 0xcd6100: ldr             x0, [fp, #0x10]
    // 0xcd6104: r2 = Null
    //     0xcd6104: mov             x2, NULL
    // 0xcd6108: r1 = Null
    //     0xcd6108: mov             x1, NULL
    // 0xcd610c: r4 = 60
    //     0xcd610c: movz            x4, #0x3c
    // 0xcd6110: branchIfSmi(r0, 0xcd611c)
    //     0xcd6110: tbz             w0, #0, #0xcd611c
    // 0xcd6114: r4 = LoadClassIdInstr(r0)
    //     0xcd6114: ldur            x4, [x0, #-1]
    //     0xcd6118: ubfx            x4, x4, #0xc, #0x14
    // 0xcd611c: sub             x4, x4, #0xf17
    // 0xcd6120: cmp             x4, #2
    // 0xcd6124: b.ls            #0xcd613c
    // 0xcd6128: r8 = TextStyle
    //     0xcd6128: add             x8, PP, #0x50, lsl #12  ; [pp+0x50300] Type: TextStyle
    //     0xcd612c: ldr             x8, [x8, #0x300]
    // 0xcd6130: r3 = Null
    //     0xcd6130: add             x3, PP, #0x50, lsl #12  ; [pp+0x50308] Null
    //     0xcd6134: ldr             x3, [x3, #0x308]
    // 0xcd6138: r0 = TextStyle()
    //     0xcd6138: bl              #0x624d00  ; IsType_TextStyle_Stub
    // 0xcd613c: r1 = <TextStyle>
    //     0xcd613c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23dc0] TypeArguments: <TextStyle>
    //     0xcd6140: ldr             x1, [x1, #0xdc0]
    // 0xcd6144: r0 = TextStyleTween()
    //     0xcd6144: bl              #0xcd615c  ; AllocateTextStyleTweenStub -> TextStyleTween (size=0x14)
    // 0xcd6148: ldr             x1, [fp, #0x10]
    // 0xcd614c: StoreField: r0->field_b = r1
    //     0xcd614c: stur            w1, [x0, #0xb]
    // 0xcd6150: LeaveFrame
    //     0xcd6150: mov             SP, fp
    //     0xcd6154: ldp             fp, lr, [SP], #0x10
    // 0xcd6158: ret
    //     0xcd6158: ret             
  }
}

// class id: 4293, size: 0x3c, field offset: 0x24
class _AnimatedPositionedState extends AnimatedWidgetBaseState<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x9fba08, size: 0x12c
    // 0x9fba08: EnterFrame
    //     0x9fba08: stp             fp, lr, [SP, #-0x10]!
    //     0x9fba0c: mov             fp, SP
    // 0x9fba10: AllocStack(0x20)
    //     0x9fba10: sub             SP, SP, #0x20
    // 0x9fba14: SetupParameters(_AnimatedPositionedState this /* r1 => r0, fp-0x10 */)
    //     0x9fba14: mov             x0, x1
    //     0x9fba18: stur            x1, [fp, #-0x10]
    // 0x9fba1c: CheckStackOverflow
    //     0x9fba1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9fba20: cmp             SP, x16
    //     0x9fba24: b.ls            #0x9fbb28
    // 0x9fba28: LoadField: r2 = r0->field_23
    //     0x9fba28: ldur            w2, [x0, #0x23]
    // 0x9fba2c: DecompressPointer r2
    //     0x9fba2c: add             x2, x2, HEAP, lsl #32
    // 0x9fba30: stur            x2, [fp, #-8]
    // 0x9fba34: cmp             w2, NULL
    // 0x9fba38: b.ne            #0x9fba44
    // 0x9fba3c: r2 = Null
    //     0x9fba3c: mov             x2, NULL
    // 0x9fba40: b               #0x9fba7c
    // 0x9fba44: mov             x1, x0
    // 0x9fba48: LoadField: r0 = r1->field_1f
    //     0x9fba48: ldur            w0, [x1, #0x1f]
    // 0x9fba4c: DecompressPointer r0
    //     0x9fba4c: add             x0, x0, HEAP, lsl #32
    // 0x9fba50: r16 = Sentinel
    //     0x9fba50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9fba54: cmp             w0, w16
    // 0x9fba58: b.ne            #0x9fba68
    // 0x9fba5c: r2 = _animation
    //     0x9fba5c: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f0] Field <ImplicitlyAnimatedWidgetState._animation@291443363>: late (offset: 0x20)
    //     0x9fba60: ldr             x2, [x2, #0xf0]
    // 0x9fba64: r0 = InitLateInstanceField()
    //     0x9fba64: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0x9fba68: ldur            x1, [fp, #-8]
    // 0x9fba6c: mov             x2, x0
    // 0x9fba70: r0 = evaluate()
    //     0x9fba70: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9fba74: mov             x2, x0
    // 0x9fba78: ldur            x0, [fp, #-0x10]
    // 0x9fba7c: stur            x2, [fp, #-0x18]
    // 0x9fba80: LoadField: r3 = r0->field_27
    //     0x9fba80: ldur            w3, [x0, #0x27]
    // 0x9fba84: DecompressPointer r3
    //     0x9fba84: add             x3, x3, HEAP, lsl #32
    // 0x9fba88: stur            x3, [fp, #-8]
    // 0x9fba8c: cmp             w3, NULL
    // 0x9fba90: b.ne            #0x9fba9c
    // 0x9fba94: r3 = Null
    //     0x9fba94: mov             x3, NULL
    // 0x9fba98: b               #0x9fbad8
    // 0x9fba9c: mov             x1, x0
    // 0x9fbaa0: LoadField: r0 = r1->field_1f
    //     0x9fbaa0: ldur            w0, [x1, #0x1f]
    // 0x9fbaa4: DecompressPointer r0
    //     0x9fbaa4: add             x0, x0, HEAP, lsl #32
    // 0x9fbaa8: r16 = Sentinel
    //     0x9fbaa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9fbaac: cmp             w0, w16
    // 0x9fbab0: b.ne            #0x9fbac0
    // 0x9fbab4: r2 = _animation
    //     0x9fbab4: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f0] Field <ImplicitlyAnimatedWidgetState._animation@291443363>: late (offset: 0x20)
    //     0x9fbab8: ldr             x2, [x2, #0xf0]
    // 0x9fbabc: r0 = InitLateInstanceField()
    //     0x9fbabc: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0x9fbac0: ldur            x1, [fp, #-8]
    // 0x9fbac4: mov             x2, x0
    // 0x9fbac8: r0 = evaluate()
    //     0x9fbac8: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9fbacc: mov             x3, x0
    // 0x9fbad0: ldur            x0, [fp, #-0x10]
    // 0x9fbad4: ldur            x2, [fp, #-0x18]
    // 0x9fbad8: stur            x3, [fp, #-0x20]
    // 0x9fbadc: LoadField: r1 = r0->field_b
    //     0x9fbadc: ldur            w1, [x0, #0xb]
    // 0x9fbae0: DecompressPointer r1
    //     0x9fbae0: add             x1, x1, HEAP, lsl #32
    // 0x9fbae4: cmp             w1, NULL
    // 0x9fbae8: b.eq            #0x9fbb30
    // 0x9fbaec: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9fbaec: ldur            w0, [x1, #0x17]
    // 0x9fbaf0: DecompressPointer r0
    //     0x9fbaf0: add             x0, x0, HEAP, lsl #32
    // 0x9fbaf4: stur            x0, [fp, #-8]
    // 0x9fbaf8: r1 = <StackParentData>
    //     0x9fbaf8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25780] TypeArguments: <StackParentData>
    //     0x9fbafc: ldr             x1, [x1, #0x780]
    // 0x9fbb00: r0 = Positioned()
    //     0x9fbb00: bl              #0x9f19f8  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x9fbb04: ldur            x1, [fp, #-0x18]
    // 0x9fbb08: StoreField: r0->field_13 = r1
    //     0x9fbb08: stur            w1, [x0, #0x13]
    // 0x9fbb0c: ldur            x1, [fp, #-0x20]
    // 0x9fbb10: ArrayStore: r0[0] = r1  ; List_4
    //     0x9fbb10: stur            w1, [x0, #0x17]
    // 0x9fbb14: ldur            x1, [fp, #-8]
    // 0x9fbb18: StoreField: r0->field_b = r1
    //     0x9fbb18: stur            w1, [x0, #0xb]
    // 0x9fbb1c: LeaveFrame
    //     0x9fbb1c: mov             SP, fp
    //     0x9fbb20: ldp             fp, lr, [SP], #0x10
    // 0x9fbb24: ret
    //     0x9fbb24: ret             
    // 0x9fbb28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9fbb28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9fbb2c: b               #0x9fba28
    // 0x9fbb30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fbb30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ forEachTween(/* No info */) {
    // ** addr: 0xcd5b40, size: 0x2e8
    // 0xcd5b40: EnterFrame
    //     0xcd5b40: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5b44: mov             fp, SP
    // 0xcd5b48: AllocStack(0x40)
    //     0xcd5b48: sub             SP, SP, #0x40
    // 0xcd5b4c: SetupParameters(_AnimatedPositionedState this /* r1 => r3, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */)
    //     0xcd5b4c: mov             x3, x1
    //     0xcd5b50: mov             x0, x2
    //     0xcd5b54: stur            x1, [fp, #-0x18]
    //     0xcd5b58: stur            x2, [fp, #-0x20]
    // 0xcd5b5c: CheckStackOverflow
    //     0xcd5b5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcd5b60: cmp             SP, x16
    //     0xcd5b64: b.ls            #0xcd5dc8
    // 0xcd5b68: LoadField: r4 = r3->field_23
    //     0xcd5b68: ldur            w4, [x3, #0x23]
    // 0xcd5b6c: DecompressPointer r4
    //     0xcd5b6c: add             x4, x4, HEAP, lsl #32
    // 0xcd5b70: stur            x4, [fp, #-0x10]
    // 0xcd5b74: LoadField: r1 = r3->field_b
    //     0xcd5b74: ldur            w1, [x3, #0xb]
    // 0xcd5b78: DecompressPointer r1
    //     0xcd5b78: add             x1, x1, HEAP, lsl #32
    // 0xcd5b7c: cmp             w1, NULL
    // 0xcd5b80: b.eq            #0xcd5dd0
    // 0xcd5b84: LoadField: d0 = r1->field_1b
    //     0xcd5b84: ldur            d0, [x1, #0x1b]
    // 0xcd5b88: r5 = inline_Allocate_Double()
    //     0xcd5b88: ldp             x5, x1, [THR, #0x50]  ; THR::top
    //     0xcd5b8c: add             x5, x5, #0x10
    //     0xcd5b90: cmp             x1, x5
    //     0xcd5b94: b.ls            #0xcd5dd4
    //     0xcd5b98: str             x5, [THR, #0x50]  ; THR::top
    //     0xcd5b9c: sub             x5, x5, #0xf
    //     0xcd5ba0: movz            x1, #0xe15c
    //     0xcd5ba4: movk            x1, #0x3, lsl #16
    //     0xcd5ba8: stur            x1, [x5, #-1]
    // 0xcd5bac: StoreField: r5->field_7 = d0
    //     0xcd5bac: stur            d0, [x5, #7]
    // 0xcd5bb0: stur            x5, [fp, #-8]
    // 0xcd5bb4: r1 = Function '<anonymous closure>':.
    //     0xcd5bb4: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5ccc8] AnonymousClosure: (0xcd5fe0), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedPositionedState::forEachTween (0xcd5b40)
    //     0xcd5bb8: ldr             x1, [x1, #0xcc8]
    // 0xcd5bbc: r2 = Null
    //     0xcd5bbc: mov             x2, NULL
    // 0xcd5bc0: r0 = AllocateClosure()
    //     0xcd5bc0: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd5bc4: ldur            x16, [fp, #-0x20]
    // 0xcd5bc8: ldur            lr, [fp, #-0x10]
    // 0xcd5bcc: stp             lr, x16, [SP, #0x10]
    // 0xcd5bd0: ldur            x16, [fp, #-8]
    // 0xcd5bd4: stp             x0, x16, [SP]
    // 0xcd5bd8: ldur            x0, [fp, #-0x20]
    // 0xcd5bdc: ClosureCall
    //     0xcd5bdc: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd5be0: ldur            x2, [x0, #0x1f]
    //     0xcd5be4: blr             x2
    // 0xcd5be8: ldur            x3, [fp, #-0x18]
    // 0xcd5bec: StoreField: r3->field_23 = r0
    //     0xcd5bec: stur            w0, [x3, #0x23]
    //     0xcd5bf0: ldurb           w16, [x3, #-1]
    //     0xcd5bf4: ldurb           w17, [x0, #-1]
    //     0xcd5bf8: and             x16, x17, x16, lsr #2
    //     0xcd5bfc: tst             x16, HEAP, lsr #32
    //     0xcd5c00: b.eq            #0xcd5c08
    //     0xcd5c04: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xcd5c08: LoadField: r0 = r3->field_27
    //     0xcd5c08: ldur            w0, [x3, #0x27]
    // 0xcd5c0c: DecompressPointer r0
    //     0xcd5c0c: add             x0, x0, HEAP, lsl #32
    // 0xcd5c10: stur            x0, [fp, #-0x10]
    // 0xcd5c14: LoadField: r1 = r3->field_b
    //     0xcd5c14: ldur            w1, [x3, #0xb]
    // 0xcd5c18: DecompressPointer r1
    //     0xcd5c18: add             x1, x1, HEAP, lsl #32
    // 0xcd5c1c: cmp             w1, NULL
    // 0xcd5c20: b.eq            #0xcd5df8
    // 0xcd5c24: LoadField: d0 = r1->field_23
    //     0xcd5c24: ldur            d0, [x1, #0x23]
    // 0xcd5c28: r4 = inline_Allocate_Double()
    //     0xcd5c28: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0xcd5c2c: add             x4, x4, #0x10
    //     0xcd5c30: cmp             x1, x4
    //     0xcd5c34: b.ls            #0xcd5dfc
    //     0xcd5c38: str             x4, [THR, #0x50]  ; THR::top
    //     0xcd5c3c: sub             x4, x4, #0xf
    //     0xcd5c40: movz            x1, #0xe15c
    //     0xcd5c44: movk            x1, #0x3, lsl #16
    //     0xcd5c48: stur            x1, [x4, #-1]
    // 0xcd5c4c: StoreField: r4->field_7 = d0
    //     0xcd5c4c: stur            d0, [x4, #7]
    // 0xcd5c50: stur            x4, [fp, #-8]
    // 0xcd5c54: r1 = Function '<anonymous closure>':.
    //     0xcd5c54: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5ccd0] AnonymousClosure: (0xcd5f88), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedPositionedState::forEachTween (0xcd5b40)
    //     0xcd5c58: ldr             x1, [x1, #0xcd0]
    // 0xcd5c5c: r2 = Null
    //     0xcd5c5c: mov             x2, NULL
    // 0xcd5c60: r0 = AllocateClosure()
    //     0xcd5c60: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd5c64: ldur            x16, [fp, #-0x20]
    // 0xcd5c68: ldur            lr, [fp, #-0x10]
    // 0xcd5c6c: stp             lr, x16, [SP, #0x10]
    // 0xcd5c70: ldur            x16, [fp, #-8]
    // 0xcd5c74: stp             x0, x16, [SP]
    // 0xcd5c78: ldur            x0, [fp, #-0x20]
    // 0xcd5c7c: ClosureCall
    //     0xcd5c7c: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd5c80: ldur            x2, [x0, #0x1f]
    //     0xcd5c84: blr             x2
    // 0xcd5c88: ldur            x3, [fp, #-0x18]
    // 0xcd5c8c: StoreField: r3->field_27 = r0
    //     0xcd5c8c: stur            w0, [x3, #0x27]
    //     0xcd5c90: ldurb           w16, [x3, #-1]
    //     0xcd5c94: ldurb           w17, [x0, #-1]
    //     0xcd5c98: and             x16, x17, x16, lsr #2
    //     0xcd5c9c: tst             x16, HEAP, lsr #32
    //     0xcd5ca0: b.eq            #0xcd5ca8
    //     0xcd5ca4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xcd5ca8: LoadField: r0 = r3->field_b
    //     0xcd5ca8: ldur            w0, [x3, #0xb]
    // 0xcd5cac: DecompressPointer r0
    //     0xcd5cac: add             x0, x0, HEAP, lsl #32
    // 0xcd5cb0: cmp             w0, NULL
    // 0xcd5cb4: b.eq            #0xcd5e18
    // 0xcd5cb8: r1 = Function '<anonymous closure>':.
    //     0xcd5cb8: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5ccd8] AnonymousClosure: (0xcd5f30), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedPositionedState::forEachTween (0xcd5b40)
    //     0xcd5cbc: ldr             x1, [x1, #0xcd8]
    // 0xcd5cc0: r2 = Null
    //     0xcd5cc0: mov             x2, NULL
    // 0xcd5cc4: r0 = AllocateClosure()
    //     0xcd5cc4: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd5cc8: ldur            x16, [fp, #-0x20]
    // 0xcd5ccc: stp             NULL, x16, [SP, #0x10]
    // 0xcd5cd0: stp             x0, NULL, [SP]
    // 0xcd5cd4: ldur            x0, [fp, #-0x20]
    // 0xcd5cd8: ClosureCall
    //     0xcd5cd8: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd5cdc: ldur            x2, [x0, #0x1f]
    //     0xcd5ce0: blr             x2
    // 0xcd5ce4: ldur            x0, [fp, #-0x18]
    // 0xcd5ce8: StoreField: r0->field_2b = rNULL
    //     0xcd5ce8: stur            NULL, [x0, #0x2b]
    // 0xcd5cec: LoadField: r1 = r0->field_b
    //     0xcd5cec: ldur            w1, [x0, #0xb]
    // 0xcd5cf0: DecompressPointer r1
    //     0xcd5cf0: add             x1, x1, HEAP, lsl #32
    // 0xcd5cf4: cmp             w1, NULL
    // 0xcd5cf8: b.eq            #0xcd5e1c
    // 0xcd5cfc: r1 = Function '<anonymous closure>':.
    //     0xcd5cfc: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5cce0] AnonymousClosure: (0xcd5ed8), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedPositionedState::forEachTween (0xcd5b40)
    //     0xcd5d00: ldr             x1, [x1, #0xce0]
    // 0xcd5d04: r2 = Null
    //     0xcd5d04: mov             x2, NULL
    // 0xcd5d08: r0 = AllocateClosure()
    //     0xcd5d08: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd5d0c: ldur            x16, [fp, #-0x20]
    // 0xcd5d10: stp             NULL, x16, [SP, #0x10]
    // 0xcd5d14: stp             x0, NULL, [SP]
    // 0xcd5d18: ldur            x0, [fp, #-0x20]
    // 0xcd5d1c: ClosureCall
    //     0xcd5d1c: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd5d20: ldur            x2, [x0, #0x1f]
    //     0xcd5d24: blr             x2
    // 0xcd5d28: ldur            x0, [fp, #-0x18]
    // 0xcd5d2c: StoreField: r0->field_2f = rNULL
    //     0xcd5d2c: stur            NULL, [x0, #0x2f]
    // 0xcd5d30: LoadField: r1 = r0->field_b
    //     0xcd5d30: ldur            w1, [x0, #0xb]
    // 0xcd5d34: DecompressPointer r1
    //     0xcd5d34: add             x1, x1, HEAP, lsl #32
    // 0xcd5d38: cmp             w1, NULL
    // 0xcd5d3c: b.eq            #0xcd5e20
    // 0xcd5d40: r1 = Function '<anonymous closure>':.
    //     0xcd5d40: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5cce8] AnonymousClosure: (0xcd5e80), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedPositionedState::forEachTween (0xcd5b40)
    //     0xcd5d44: ldr             x1, [x1, #0xce8]
    // 0xcd5d48: r2 = Null
    //     0xcd5d48: mov             x2, NULL
    // 0xcd5d4c: r0 = AllocateClosure()
    //     0xcd5d4c: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd5d50: ldur            x16, [fp, #-0x20]
    // 0xcd5d54: stp             NULL, x16, [SP, #0x10]
    // 0xcd5d58: stp             x0, NULL, [SP]
    // 0xcd5d5c: ldur            x0, [fp, #-0x20]
    // 0xcd5d60: ClosureCall
    //     0xcd5d60: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd5d64: ldur            x2, [x0, #0x1f]
    //     0xcd5d68: blr             x2
    // 0xcd5d6c: ldur            x0, [fp, #-0x18]
    // 0xcd5d70: StoreField: r0->field_33 = rNULL
    //     0xcd5d70: stur            NULL, [x0, #0x33]
    // 0xcd5d74: LoadField: r1 = r0->field_b
    //     0xcd5d74: ldur            w1, [x0, #0xb]
    // 0xcd5d78: DecompressPointer r1
    //     0xcd5d78: add             x1, x1, HEAP, lsl #32
    // 0xcd5d7c: cmp             w1, NULL
    // 0xcd5d80: b.eq            #0xcd5e24
    // 0xcd5d84: r1 = Function '<anonymous closure>':.
    //     0xcd5d84: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5ccf0] AnonymousClosure: (0xcd5e28), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedPositionedState::forEachTween (0xcd5b40)
    //     0xcd5d88: ldr             x1, [x1, #0xcf0]
    // 0xcd5d8c: r2 = Null
    //     0xcd5d8c: mov             x2, NULL
    // 0xcd5d90: r0 = AllocateClosure()
    //     0xcd5d90: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd5d94: ldur            x16, [fp, #-0x20]
    // 0xcd5d98: stp             NULL, x16, [SP, #0x10]
    // 0xcd5d9c: stp             x0, NULL, [SP]
    // 0xcd5da0: ldur            x0, [fp, #-0x20]
    // 0xcd5da4: ClosureCall
    //     0xcd5da4: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd5da8: ldur            x2, [x0, #0x1f]
    //     0xcd5dac: blr             x2
    // 0xcd5db0: ldur            x1, [fp, #-0x18]
    // 0xcd5db4: StoreField: r1->field_37 = rNULL
    //     0xcd5db4: stur            NULL, [x1, #0x37]
    // 0xcd5db8: r0 = Null
    //     0xcd5db8: mov             x0, NULL
    // 0xcd5dbc: LeaveFrame
    //     0xcd5dbc: mov             SP, fp
    //     0xcd5dc0: ldp             fp, lr, [SP], #0x10
    // 0xcd5dc4: ret
    //     0xcd5dc4: ret             
    // 0xcd5dc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcd5dc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcd5dcc: b               #0xcd5b68
    // 0xcd5dd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd5dd0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd5dd4: SaveReg d0
    //     0xcd5dd4: str             q0, [SP, #-0x10]!
    // 0xcd5dd8: stp             x3, x4, [SP, #-0x10]!
    // 0xcd5ddc: SaveReg r0
    //     0xcd5ddc: str             x0, [SP, #-8]!
    // 0xcd5de0: r0 = AllocateDouble()
    //     0xcd5de0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xcd5de4: mov             x5, x0
    // 0xcd5de8: RestoreReg r0
    //     0xcd5de8: ldr             x0, [SP], #8
    // 0xcd5dec: ldp             x3, x4, [SP], #0x10
    // 0xcd5df0: RestoreReg d0
    //     0xcd5df0: ldr             q0, [SP], #0x10
    // 0xcd5df4: b               #0xcd5bac
    // 0xcd5df8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd5df8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd5dfc: SaveReg d0
    //     0xcd5dfc: str             q0, [SP, #-0x10]!
    // 0xcd5e00: stp             x0, x3, [SP, #-0x10]!
    // 0xcd5e04: r0 = AllocateDouble()
    //     0xcd5e04: bl              #0xec2254  ; AllocateDoubleStub
    // 0xcd5e08: mov             x4, x0
    // 0xcd5e0c: ldp             x0, x3, [SP], #0x10
    // 0xcd5e10: RestoreReg d0
    //     0xcd5e10: ldr             q0, [SP], #0x10
    // 0xcd5e14: b               #0xcd5c4c
    // 0xcd5e18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd5e18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd5e1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd5e1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd5e20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd5e20: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd5e24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd5e24: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Tween<double> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd5e28, size: 0x58
    // 0xcd5e28: EnterFrame
    //     0xcd5e28: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5e2c: mov             fp, SP
    // 0xcd5e30: ldr             x0, [fp, #0x10]
    // 0xcd5e34: r2 = Null
    //     0xcd5e34: mov             x2, NULL
    // 0xcd5e38: r1 = Null
    //     0xcd5e38: mov             x1, NULL
    // 0xcd5e3c: r4 = 60
    //     0xcd5e3c: movz            x4, #0x3c
    // 0xcd5e40: branchIfSmi(r0, 0xcd5e4c)
    //     0xcd5e40: tbz             w0, #0, #0xcd5e4c
    // 0xcd5e44: r4 = LoadClassIdInstr(r0)
    //     0xcd5e44: ldur            x4, [x0, #-1]
    //     0xcd5e48: ubfx            x4, x4, #0xc, #0x14
    // 0xcd5e4c: cmp             x4, #0x3e
    // 0xcd5e50: b.eq            #0xcd5e64
    // 0xcd5e54: r8 = double
    //     0xcd5e54: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xcd5e58: r3 = Null
    //     0xcd5e58: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5ccf8] Null
    //     0xcd5e5c: ldr             x3, [x3, #0xcf8]
    // 0xcd5e60: r0 = double()
    //     0xcd5e60: bl              #0xed4460  ; IsType_double_Stub
    // 0xcd5e64: r1 = <double>
    //     0xcd5e64: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xcd5e68: r0 = Tween()
    //     0xcd5e68: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xcd5e6c: ldr             x1, [fp, #0x10]
    // 0xcd5e70: StoreField: r0->field_b = r1
    //     0xcd5e70: stur            w1, [x0, #0xb]
    // 0xcd5e74: LeaveFrame
    //     0xcd5e74: mov             SP, fp
    //     0xcd5e78: ldp             fp, lr, [SP], #0x10
    // 0xcd5e7c: ret
    //     0xcd5e7c: ret             
  }
  [closure] Tween<double> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd5e80, size: 0x58
    // 0xcd5e80: EnterFrame
    //     0xcd5e80: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5e84: mov             fp, SP
    // 0xcd5e88: ldr             x0, [fp, #0x10]
    // 0xcd5e8c: r2 = Null
    //     0xcd5e8c: mov             x2, NULL
    // 0xcd5e90: r1 = Null
    //     0xcd5e90: mov             x1, NULL
    // 0xcd5e94: r4 = 60
    //     0xcd5e94: movz            x4, #0x3c
    // 0xcd5e98: branchIfSmi(r0, 0xcd5ea4)
    //     0xcd5e98: tbz             w0, #0, #0xcd5ea4
    // 0xcd5e9c: r4 = LoadClassIdInstr(r0)
    //     0xcd5e9c: ldur            x4, [x0, #-1]
    //     0xcd5ea0: ubfx            x4, x4, #0xc, #0x14
    // 0xcd5ea4: cmp             x4, #0x3e
    // 0xcd5ea8: b.eq            #0xcd5ebc
    // 0xcd5eac: r8 = double
    //     0xcd5eac: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xcd5eb0: r3 = Null
    //     0xcd5eb0: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cd08] Null
    //     0xcd5eb4: ldr             x3, [x3, #0xd08]
    // 0xcd5eb8: r0 = double()
    //     0xcd5eb8: bl              #0xed4460  ; IsType_double_Stub
    // 0xcd5ebc: r1 = <double>
    //     0xcd5ebc: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xcd5ec0: r0 = Tween()
    //     0xcd5ec0: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xcd5ec4: ldr             x1, [fp, #0x10]
    // 0xcd5ec8: StoreField: r0->field_b = r1
    //     0xcd5ec8: stur            w1, [x0, #0xb]
    // 0xcd5ecc: LeaveFrame
    //     0xcd5ecc: mov             SP, fp
    //     0xcd5ed0: ldp             fp, lr, [SP], #0x10
    // 0xcd5ed4: ret
    //     0xcd5ed4: ret             
  }
  [closure] Tween<double> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd5ed8, size: 0x58
    // 0xcd5ed8: EnterFrame
    //     0xcd5ed8: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5edc: mov             fp, SP
    // 0xcd5ee0: ldr             x0, [fp, #0x10]
    // 0xcd5ee4: r2 = Null
    //     0xcd5ee4: mov             x2, NULL
    // 0xcd5ee8: r1 = Null
    //     0xcd5ee8: mov             x1, NULL
    // 0xcd5eec: r4 = 60
    //     0xcd5eec: movz            x4, #0x3c
    // 0xcd5ef0: branchIfSmi(r0, 0xcd5efc)
    //     0xcd5ef0: tbz             w0, #0, #0xcd5efc
    // 0xcd5ef4: r4 = LoadClassIdInstr(r0)
    //     0xcd5ef4: ldur            x4, [x0, #-1]
    //     0xcd5ef8: ubfx            x4, x4, #0xc, #0x14
    // 0xcd5efc: cmp             x4, #0x3e
    // 0xcd5f00: b.eq            #0xcd5f14
    // 0xcd5f04: r8 = double
    //     0xcd5f04: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xcd5f08: r3 = Null
    //     0xcd5f08: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cd18] Null
    //     0xcd5f0c: ldr             x3, [x3, #0xd18]
    // 0xcd5f10: r0 = double()
    //     0xcd5f10: bl              #0xed4460  ; IsType_double_Stub
    // 0xcd5f14: r1 = <double>
    //     0xcd5f14: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xcd5f18: r0 = Tween()
    //     0xcd5f18: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xcd5f1c: ldr             x1, [fp, #0x10]
    // 0xcd5f20: StoreField: r0->field_b = r1
    //     0xcd5f20: stur            w1, [x0, #0xb]
    // 0xcd5f24: LeaveFrame
    //     0xcd5f24: mov             SP, fp
    //     0xcd5f28: ldp             fp, lr, [SP], #0x10
    // 0xcd5f2c: ret
    //     0xcd5f2c: ret             
  }
  [closure] Tween<double> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd5f30, size: 0x58
    // 0xcd5f30: EnterFrame
    //     0xcd5f30: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5f34: mov             fp, SP
    // 0xcd5f38: ldr             x0, [fp, #0x10]
    // 0xcd5f3c: r2 = Null
    //     0xcd5f3c: mov             x2, NULL
    // 0xcd5f40: r1 = Null
    //     0xcd5f40: mov             x1, NULL
    // 0xcd5f44: r4 = 60
    //     0xcd5f44: movz            x4, #0x3c
    // 0xcd5f48: branchIfSmi(r0, 0xcd5f54)
    //     0xcd5f48: tbz             w0, #0, #0xcd5f54
    // 0xcd5f4c: r4 = LoadClassIdInstr(r0)
    //     0xcd5f4c: ldur            x4, [x0, #-1]
    //     0xcd5f50: ubfx            x4, x4, #0xc, #0x14
    // 0xcd5f54: cmp             x4, #0x3e
    // 0xcd5f58: b.eq            #0xcd5f6c
    // 0xcd5f5c: r8 = double
    //     0xcd5f5c: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xcd5f60: r3 = Null
    //     0xcd5f60: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cd28] Null
    //     0xcd5f64: ldr             x3, [x3, #0xd28]
    // 0xcd5f68: r0 = double()
    //     0xcd5f68: bl              #0xed4460  ; IsType_double_Stub
    // 0xcd5f6c: r1 = <double>
    //     0xcd5f6c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xcd5f70: r0 = Tween()
    //     0xcd5f70: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xcd5f74: ldr             x1, [fp, #0x10]
    // 0xcd5f78: StoreField: r0->field_b = r1
    //     0xcd5f78: stur            w1, [x0, #0xb]
    // 0xcd5f7c: LeaveFrame
    //     0xcd5f7c: mov             SP, fp
    //     0xcd5f80: ldp             fp, lr, [SP], #0x10
    // 0xcd5f84: ret
    //     0xcd5f84: ret             
  }
  [closure] Tween<double> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd5f88, size: 0x58
    // 0xcd5f88: EnterFrame
    //     0xcd5f88: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5f8c: mov             fp, SP
    // 0xcd5f90: ldr             x0, [fp, #0x10]
    // 0xcd5f94: r2 = Null
    //     0xcd5f94: mov             x2, NULL
    // 0xcd5f98: r1 = Null
    //     0xcd5f98: mov             x1, NULL
    // 0xcd5f9c: r4 = 60
    //     0xcd5f9c: movz            x4, #0x3c
    // 0xcd5fa0: branchIfSmi(r0, 0xcd5fac)
    //     0xcd5fa0: tbz             w0, #0, #0xcd5fac
    // 0xcd5fa4: r4 = LoadClassIdInstr(r0)
    //     0xcd5fa4: ldur            x4, [x0, #-1]
    //     0xcd5fa8: ubfx            x4, x4, #0xc, #0x14
    // 0xcd5fac: cmp             x4, #0x3e
    // 0xcd5fb0: b.eq            #0xcd5fc4
    // 0xcd5fb4: r8 = double
    //     0xcd5fb4: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xcd5fb8: r3 = Null
    //     0xcd5fb8: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cd38] Null
    //     0xcd5fbc: ldr             x3, [x3, #0xd38]
    // 0xcd5fc0: r0 = double()
    //     0xcd5fc0: bl              #0xed4460  ; IsType_double_Stub
    // 0xcd5fc4: r1 = <double>
    //     0xcd5fc4: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xcd5fc8: r0 = Tween()
    //     0xcd5fc8: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xcd5fcc: ldr             x1, [fp, #0x10]
    // 0xcd5fd0: StoreField: r0->field_b = r1
    //     0xcd5fd0: stur            w1, [x0, #0xb]
    // 0xcd5fd4: LeaveFrame
    //     0xcd5fd4: mov             SP, fp
    //     0xcd5fd8: ldp             fp, lr, [SP], #0x10
    // 0xcd5fdc: ret
    //     0xcd5fdc: ret             
  }
  [closure] Tween<double> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd5fe0, size: 0x58
    // 0xcd5fe0: EnterFrame
    //     0xcd5fe0: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5fe4: mov             fp, SP
    // 0xcd5fe8: ldr             x0, [fp, #0x10]
    // 0xcd5fec: r2 = Null
    //     0xcd5fec: mov             x2, NULL
    // 0xcd5ff0: r1 = Null
    //     0xcd5ff0: mov             x1, NULL
    // 0xcd5ff4: r4 = 60
    //     0xcd5ff4: movz            x4, #0x3c
    // 0xcd5ff8: branchIfSmi(r0, 0xcd6004)
    //     0xcd5ff8: tbz             w0, #0, #0xcd6004
    // 0xcd5ffc: r4 = LoadClassIdInstr(r0)
    //     0xcd5ffc: ldur            x4, [x0, #-1]
    //     0xcd6000: ubfx            x4, x4, #0xc, #0x14
    // 0xcd6004: cmp             x4, #0x3e
    // 0xcd6008: b.eq            #0xcd601c
    // 0xcd600c: r8 = double
    //     0xcd600c: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xcd6010: r3 = Null
    //     0xcd6010: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cd48] Null
    //     0xcd6014: ldr             x3, [x3, #0xd48]
    // 0xcd6018: r0 = double()
    //     0xcd6018: bl              #0xed4460  ; IsType_double_Stub
    // 0xcd601c: r1 = <double>
    //     0xcd601c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xcd6020: r0 = Tween()
    //     0xcd6020: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xcd6024: ldr             x1, [fp, #0x10]
    // 0xcd6028: StoreField: r0->field_b = r1
    //     0xcd6028: stur            w1, [x0, #0xb]
    // 0xcd602c: LeaveFrame
    //     0xcd602c: mov             SP, fp
    //     0xcd6030: ldp             fp, lr, [SP], #0x10
    // 0xcd6034: ret
    //     0xcd6034: ret             
  }
}

// class id: 4294, size: 0x28, field offset: 0x24
class _AnimatedPaddingState extends AnimatedWidgetBaseState<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x9fb908, size: 0xdc
    // 0x9fb908: EnterFrame
    //     0x9fb908: stp             fp, lr, [SP, #-0x10]!
    //     0x9fb90c: mov             fp, SP
    // 0x9fb910: AllocStack(0x18)
    //     0x9fb910: sub             SP, SP, #0x18
    // 0x9fb914: SetupParameters(_AnimatedPaddingState this /* r1 => r0, fp-0x10 */)
    //     0x9fb914: mov             x0, x1
    //     0x9fb918: stur            x1, [fp, #-0x10]
    // 0x9fb91c: CheckStackOverflow
    //     0x9fb91c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9fb920: cmp             SP, x16
    //     0x9fb924: b.ls            #0x9fb9d4
    // 0x9fb928: LoadField: r2 = r0->field_23
    //     0x9fb928: ldur            w2, [x0, #0x23]
    // 0x9fb92c: DecompressPointer r2
    //     0x9fb92c: add             x2, x2, HEAP, lsl #32
    // 0x9fb930: stur            x2, [fp, #-8]
    // 0x9fb934: cmp             w2, NULL
    // 0x9fb938: b.eq            #0x9fb9dc
    // 0x9fb93c: mov             x1, x0
    // 0x9fb940: LoadField: r0 = r1->field_1f
    //     0x9fb940: ldur            w0, [x1, #0x1f]
    // 0x9fb944: DecompressPointer r0
    //     0x9fb944: add             x0, x0, HEAP, lsl #32
    // 0x9fb948: r16 = Sentinel
    //     0x9fb948: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9fb94c: cmp             w0, w16
    // 0x9fb950: b.ne            #0x9fb960
    // 0x9fb954: r2 = _animation
    //     0x9fb954: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f0] Field <ImplicitlyAnimatedWidgetState._animation@291443363>: late (offset: 0x20)
    //     0x9fb958: ldr             x2, [x2, #0xf0]
    // 0x9fb95c: r0 = InitLateInstanceField()
    //     0x9fb95c: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0x9fb960: ldur            x1, [fp, #-8]
    // 0x9fb964: mov             x2, x0
    // 0x9fb968: r0 = evaluate()
    //     0x9fb968: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9fb96c: r1 = LoadClassIdInstr(r0)
    //     0x9fb96c: ldur            x1, [x0, #-1]
    //     0x9fb970: ubfx            x1, x1, #0xc, #0x14
    // 0x9fb974: mov             x16, x0
    // 0x9fb978: mov             x0, x1
    // 0x9fb97c: mov             x1, x16
    // 0x9fb980: r0 = GDT[cid_x0 + -0xf9f]()
    //     0x9fb980: sub             lr, x0, #0xf9f
    //     0x9fb984: ldr             lr, [x21, lr, lsl #3]
    //     0x9fb988: blr             lr
    // 0x9fb98c: mov             x1, x0
    // 0x9fb990: ldur            x0, [fp, #-0x10]
    // 0x9fb994: stur            x1, [fp, #-0x18]
    // 0x9fb998: LoadField: r2 = r0->field_b
    //     0x9fb998: ldur            w2, [x0, #0xb]
    // 0x9fb99c: DecompressPointer r2
    //     0x9fb99c: add             x2, x2, HEAP, lsl #32
    // 0x9fb9a0: cmp             w2, NULL
    // 0x9fb9a4: b.eq            #0x9fb9e0
    // 0x9fb9a8: LoadField: r0 = r2->field_1b
    //     0x9fb9a8: ldur            w0, [x2, #0x1b]
    // 0x9fb9ac: DecompressPointer r0
    //     0x9fb9ac: add             x0, x0, HEAP, lsl #32
    // 0x9fb9b0: stur            x0, [fp, #-8]
    // 0x9fb9b4: r0 = Padding()
    //     0x9fb9b4: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9fb9b8: ldur            x1, [fp, #-0x18]
    // 0x9fb9bc: StoreField: r0->field_f = r1
    //     0x9fb9bc: stur            w1, [x0, #0xf]
    // 0x9fb9c0: ldur            x1, [fp, #-8]
    // 0x9fb9c4: StoreField: r0->field_b = r1
    //     0x9fb9c4: stur            w1, [x0, #0xb]
    // 0x9fb9c8: LeaveFrame
    //     0x9fb9c8: mov             SP, fp
    //     0x9fb9cc: ldp             fp, lr, [SP], #0x10
    // 0x9fb9d0: ret
    //     0x9fb9d0: ret             
    // 0x9fb9d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9fb9d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9fb9d8: b               #0x9fb928
    // 0x9fb9dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fb9dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fb9e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fb9e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ forEachTween(/* No info */) {
    // ** addr: 0xcd5a1c, size: 0xc0
    // 0xcd5a1c: EnterFrame
    //     0xcd5a1c: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5a20: mov             fp, SP
    // 0xcd5a24: AllocStack(0x40)
    //     0xcd5a24: sub             SP, SP, #0x40
    // 0xcd5a28: SetupParameters(_AnimatedPaddingState this /* r1 => r3, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */)
    //     0xcd5a28: mov             x3, x1
    //     0xcd5a2c: mov             x0, x2
    //     0xcd5a30: stur            x1, [fp, #-0x18]
    //     0xcd5a34: stur            x2, [fp, #-0x20]
    // 0xcd5a38: CheckStackOverflow
    //     0xcd5a38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcd5a3c: cmp             SP, x16
    //     0xcd5a40: b.ls            #0xcd5ad0
    // 0xcd5a44: LoadField: r4 = r3->field_23
    //     0xcd5a44: ldur            w4, [x3, #0x23]
    // 0xcd5a48: DecompressPointer r4
    //     0xcd5a48: add             x4, x4, HEAP, lsl #32
    // 0xcd5a4c: stur            x4, [fp, #-0x10]
    // 0xcd5a50: LoadField: r1 = r3->field_b
    //     0xcd5a50: ldur            w1, [x3, #0xb]
    // 0xcd5a54: DecompressPointer r1
    //     0xcd5a54: add             x1, x1, HEAP, lsl #32
    // 0xcd5a58: cmp             w1, NULL
    // 0xcd5a5c: b.eq            #0xcd5ad8
    // 0xcd5a60: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xcd5a60: ldur            w5, [x1, #0x17]
    // 0xcd5a64: DecompressPointer r5
    //     0xcd5a64: add             x5, x5, HEAP, lsl #32
    // 0xcd5a68: stur            x5, [fp, #-8]
    // 0xcd5a6c: r1 = Function '<anonymous closure>':.
    //     0xcd5a6c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56ee8] AnonymousClosure: (0xcd5adc), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedPaddingState::forEachTween (0xcd5a1c)
    //     0xcd5a70: ldr             x1, [x1, #0xee8]
    // 0xcd5a74: r2 = Null
    //     0xcd5a74: mov             x2, NULL
    // 0xcd5a78: r0 = AllocateClosure()
    //     0xcd5a78: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd5a7c: ldur            x16, [fp, #-0x20]
    // 0xcd5a80: ldur            lr, [fp, #-0x10]
    // 0xcd5a84: stp             lr, x16, [SP, #0x10]
    // 0xcd5a88: ldur            x16, [fp, #-8]
    // 0xcd5a8c: stp             x0, x16, [SP]
    // 0xcd5a90: ldur            x0, [fp, #-0x20]
    // 0xcd5a94: ClosureCall
    //     0xcd5a94: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd5a98: ldur            x2, [x0, #0x1f]
    //     0xcd5a9c: blr             x2
    // 0xcd5aa0: ldur            x1, [fp, #-0x18]
    // 0xcd5aa4: StoreField: r1->field_23 = r0
    //     0xcd5aa4: stur            w0, [x1, #0x23]
    //     0xcd5aa8: ldurb           w16, [x1, #-1]
    //     0xcd5aac: ldurb           w17, [x0, #-1]
    //     0xcd5ab0: and             x16, x17, x16, lsr #2
    //     0xcd5ab4: tst             x16, HEAP, lsr #32
    //     0xcd5ab8: b.eq            #0xcd5ac0
    //     0xcd5abc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcd5ac0: r0 = Null
    //     0xcd5ac0: mov             x0, NULL
    // 0xcd5ac4: LeaveFrame
    //     0xcd5ac4: mov             SP, fp
    //     0xcd5ac8: ldp             fp, lr, [SP], #0x10
    // 0xcd5acc: ret
    //     0xcd5acc: ret             
    // 0xcd5ad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcd5ad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcd5ad4: b               #0xcd5a44
    // 0xcd5ad8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd5ad8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] EdgeInsetsGeometryTween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd5adc, size: 0x64
    // 0xcd5adc: EnterFrame
    //     0xcd5adc: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5ae0: mov             fp, SP
    // 0xcd5ae4: ldr             x0, [fp, #0x10]
    // 0xcd5ae8: r2 = Null
    //     0xcd5ae8: mov             x2, NULL
    // 0xcd5aec: r1 = Null
    //     0xcd5aec: mov             x1, NULL
    // 0xcd5af0: r4 = 60
    //     0xcd5af0: movz            x4, #0x3c
    // 0xcd5af4: branchIfSmi(r0, 0xcd5b00)
    //     0xcd5af4: tbz             w0, #0, #0xcd5b00
    // 0xcd5af8: r4 = LoadClassIdInstr(r0)
    //     0xcd5af8: ldur            x4, [x0, #-1]
    //     0xcd5afc: ubfx            x4, x4, #0xc, #0x14
    // 0xcd5b00: sub             x4, x4, #0xcb5
    // 0xcd5b04: cmp             x4, #2
    // 0xcd5b08: b.ls            #0xcd5b20
    // 0xcd5b0c: r8 = EdgeInsetsGeometry
    //     0xcd5b0c: add             x8, PP, #0x56, lsl #12  ; [pp+0x56e70] Type: EdgeInsetsGeometry
    //     0xcd5b10: ldr             x8, [x8, #0xe70]
    // 0xcd5b14: r3 = Null
    //     0xcd5b14: add             x3, PP, #0x56, lsl #12  ; [pp+0x56ef0] Null
    //     0xcd5b18: ldr             x3, [x3, #0xef0]
    // 0xcd5b1c: r0 = EdgeInsetsGeometry()
    //     0xcd5b1c: bl              #0x65df0c  ; IsType_EdgeInsetsGeometry_Stub
    // 0xcd5b20: r1 = <EdgeInsetsGeometry>
    //     0xcd5b20: add             x1, PP, #0x23, lsl #12  ; [pp+0x23dc8] TypeArguments: <EdgeInsetsGeometry>
    //     0xcd5b24: ldr             x1, [x1, #0xdc8]
    // 0xcd5b28: r0 = EdgeInsetsGeometryTween()
    //     0xcd5b28: bl              #0xcd5814  ; AllocateEdgeInsetsGeometryTweenStub -> EdgeInsetsGeometryTween (size=0x14)
    // 0xcd5b2c: ldr             x1, [fp, #0x10]
    // 0xcd5b30: StoreField: r0->field_b = r1
    //     0xcd5b30: stur            w1, [x0, #0xb]
    // 0xcd5b34: LeaveFrame
    //     0xcd5b34: mov             SP, fp
    //     0xcd5b38: ldp             fp, lr, [SP], #0x10
    // 0xcd5b3c: ret
    //     0xcd5b3c: ret             
  }
}

// class id: 4295, size: 0x44, field offset: 0x24
class _AnimatedContainerState extends AnimatedWidgetBaseState<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x9fb738, size: 0x1ac
    // 0x9fb738: EnterFrame
    //     0x9fb738: stp             fp, lr, [SP, #-0x10]!
    //     0x9fb73c: mov             fp, SP
    // 0x9fb740: AllocStack(0x70)
    //     0x9fb740: sub             SP, SP, #0x70
    // 0x9fb744: SetupParameters(_AnimatedContainerState this /* r1 => r0, fp-0x8 */)
    //     0x9fb744: mov             x0, x1
    //     0x9fb748: stur            x1, [fp, #-8]
    // 0x9fb74c: CheckStackOverflow
    //     0x9fb74c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9fb750: cmp             SP, x16
    //     0x9fb754: b.ls            #0x9fb8d8
    // 0x9fb758: mov             x1, x0
    // 0x9fb75c: LoadField: r0 = r1->field_1f
    //     0x9fb75c: ldur            w0, [x1, #0x1f]
    // 0x9fb760: DecompressPointer r0
    //     0x9fb760: add             x0, x0, HEAP, lsl #32
    // 0x9fb764: r16 = Sentinel
    //     0x9fb764: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9fb768: cmp             w0, w16
    // 0x9fb76c: b.ne            #0x9fb77c
    // 0x9fb770: r2 = _animation
    //     0x9fb770: add             x2, PP, #0x46, lsl #12  ; [pp+0x460f0] Field <ImplicitlyAnimatedWidgetState._animation@291443363>: late (offset: 0x20)
    //     0x9fb774: ldr             x2, [x2, #0xf0]
    // 0x9fb778: r0 = InitLateInstanceField()
    //     0x9fb778: bl              #0xec02d4  ; InitLateInstanceFieldStub
    // 0x9fb77c: mov             x3, x0
    // 0x9fb780: ldur            x0, [fp, #-8]
    // 0x9fb784: stur            x3, [fp, #-0x10]
    // 0x9fb788: LoadField: r1 = r0->field_23
    //     0x9fb788: ldur            w1, [x0, #0x23]
    // 0x9fb78c: DecompressPointer r1
    //     0x9fb78c: add             x1, x1, HEAP, lsl #32
    // 0x9fb790: cmp             w1, NULL
    // 0x9fb794: b.ne            #0x9fb7a0
    // 0x9fb798: r3 = Null
    //     0x9fb798: mov             x3, NULL
    // 0x9fb79c: b               #0x9fb7b0
    // 0x9fb7a0: mov             x2, x3
    // 0x9fb7a4: r0 = evaluate()
    //     0x9fb7a4: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9fb7a8: mov             x3, x0
    // 0x9fb7ac: ldur            x0, [fp, #-8]
    // 0x9fb7b0: stur            x3, [fp, #-0x18]
    // 0x9fb7b4: LoadField: r1 = r0->field_27
    //     0x9fb7b4: ldur            w1, [x0, #0x27]
    // 0x9fb7b8: DecompressPointer r1
    //     0x9fb7b8: add             x1, x1, HEAP, lsl #32
    // 0x9fb7bc: cmp             w1, NULL
    // 0x9fb7c0: b.ne            #0x9fb7cc
    // 0x9fb7c4: r3 = Null
    //     0x9fb7c4: mov             x3, NULL
    // 0x9fb7c8: b               #0x9fb7dc
    // 0x9fb7cc: ldur            x2, [fp, #-0x10]
    // 0x9fb7d0: r0 = evaluate()
    //     0x9fb7d0: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9fb7d4: mov             x3, x0
    // 0x9fb7d8: ldur            x0, [fp, #-8]
    // 0x9fb7dc: stur            x3, [fp, #-0x20]
    // 0x9fb7e0: LoadField: r1 = r0->field_2b
    //     0x9fb7e0: ldur            w1, [x0, #0x2b]
    // 0x9fb7e4: DecompressPointer r1
    //     0x9fb7e4: add             x1, x1, HEAP, lsl #32
    // 0x9fb7e8: cmp             w1, NULL
    // 0x9fb7ec: b.ne            #0x9fb7f8
    // 0x9fb7f0: r3 = Null
    //     0x9fb7f0: mov             x3, NULL
    // 0x9fb7f4: b               #0x9fb808
    // 0x9fb7f8: ldur            x2, [fp, #-0x10]
    // 0x9fb7fc: r0 = evaluate()
    //     0x9fb7fc: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9fb800: mov             x3, x0
    // 0x9fb804: ldur            x0, [fp, #-8]
    // 0x9fb808: stur            x3, [fp, #-0x28]
    // 0x9fb80c: LoadField: r1 = r0->field_33
    //     0x9fb80c: ldur            w1, [x0, #0x33]
    // 0x9fb810: DecompressPointer r1
    //     0x9fb810: add             x1, x1, HEAP, lsl #32
    // 0x9fb814: cmp             w1, NULL
    // 0x9fb818: b.ne            #0x9fb824
    // 0x9fb81c: r3 = Null
    //     0x9fb81c: mov             x3, NULL
    // 0x9fb820: b               #0x9fb834
    // 0x9fb824: ldur            x2, [fp, #-0x10]
    // 0x9fb828: r0 = evaluate()
    //     0x9fb828: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9fb82c: mov             x3, x0
    // 0x9fb830: ldur            x0, [fp, #-8]
    // 0x9fb834: stur            x3, [fp, #-0x30]
    // 0x9fb838: LoadField: r1 = r0->field_37
    //     0x9fb838: ldur            w1, [x0, #0x37]
    // 0x9fb83c: DecompressPointer r1
    //     0x9fb83c: add             x1, x1, HEAP, lsl #32
    // 0x9fb840: cmp             w1, NULL
    // 0x9fb844: b.ne            #0x9fb850
    // 0x9fb848: r1 = Null
    //     0x9fb848: mov             x1, NULL
    // 0x9fb84c: b               #0x9fb860
    // 0x9fb850: ldur            x2, [fp, #-0x10]
    // 0x9fb854: r0 = evaluate()
    //     0x9fb854: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0x9fb858: mov             x1, x0
    // 0x9fb85c: ldur            x0, [fp, #-8]
    // 0x9fb860: stur            x1, [fp, #-0x10]
    // 0x9fb864: LoadField: r2 = r0->field_b
    //     0x9fb864: ldur            w2, [x0, #0xb]
    // 0x9fb868: DecompressPointer r2
    //     0x9fb868: add             x2, x2, HEAP, lsl #32
    // 0x9fb86c: cmp             w2, NULL
    // 0x9fb870: b.eq            #0x9fb8e0
    // 0x9fb874: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x9fb874: ldur            w0, [x2, #0x17]
    // 0x9fb878: DecompressPointer r0
    //     0x9fb878: add             x0, x0, HEAP, lsl #32
    // 0x9fb87c: stur            x0, [fp, #-8]
    // 0x9fb880: r0 = Container()
    //     0x9fb880: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9fb884: stur            x0, [fp, #-0x38]
    // 0x9fb888: ldur            x16, [fp, #-0x18]
    // 0x9fb88c: ldur            lr, [fp, #-0x20]
    // 0x9fb890: stp             lr, x16, [SP, #0x28]
    // 0x9fb894: ldur            x16, [fp, #-0x28]
    // 0x9fb898: ldur            lr, [fp, #-0x30]
    // 0x9fb89c: stp             lr, x16, [SP, #0x18]
    // 0x9fb8a0: ldur            x16, [fp, #-0x10]
    // 0x9fb8a4: r30 = Instance_Clip
    //     0x9fb8a4: add             lr, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0x9fb8a8: ldr             lr, [lr, #0x750]
    // 0x9fb8ac: stp             lr, x16, [SP, #8]
    // 0x9fb8b0: ldur            x16, [fp, #-8]
    // 0x9fb8b4: str             x16, [SP]
    // 0x9fb8b8: mov             x1, x0
    // 0x9fb8bc: r4 = const [0, 0x8, 0x7, 0x1, alignment, 0x1, child, 0x7, clipBehavior, 0x6, constraints, 0x4, decoration, 0x3, margin, 0x5, padding, 0x2, null]
    //     0x9fb8bc: add             x4, PP, #0x56, lsl #12  ; [pp+0x56df8] List(19) [0, 0x8, 0x7, 0x1, "alignment", 0x1, "child", 0x7, "clipBehavior", 0x6, "constraints", 0x4, "decoration", 0x3, "margin", 0x5, "padding", 0x2, Null]
    //     0x9fb8c0: ldr             x4, [x4, #0xdf8]
    // 0x9fb8c4: r0 = Container()
    //     0x9fb8c4: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9fb8c8: ldur            x0, [fp, #-0x38]
    // 0x9fb8cc: LeaveFrame
    //     0x9fb8cc: mov             SP, fp
    //     0x9fb8d0: ldp             fp, lr, [SP], #0x10
    // 0x9fb8d4: ret
    //     0x9fb8d4: ret             
    // 0x9fb8d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9fb8d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9fb8dc: b               #0x9fb758
    // 0x9fb8e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fb8e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ forEachTween(/* No info */) {
    // ** addr: 0xcd5340, size: 0x398
    // 0xcd5340: EnterFrame
    //     0xcd5340: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5344: mov             fp, SP
    // 0xcd5348: AllocStack(0x40)
    //     0xcd5348: sub             SP, SP, #0x40
    // 0xcd534c: SetupParameters(_AnimatedContainerState this /* r1 => r3, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */)
    //     0xcd534c: mov             x3, x1
    //     0xcd5350: mov             x0, x2
    //     0xcd5354: stur            x1, [fp, #-0x18]
    //     0xcd5358: stur            x2, [fp, #-0x20]
    // 0xcd535c: CheckStackOverflow
    //     0xcd535c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcd5360: cmp             SP, x16
    //     0xcd5364: b.ls            #0xcd56b0
    // 0xcd5368: LoadField: r4 = r3->field_23
    //     0xcd5368: ldur            w4, [x3, #0x23]
    // 0xcd536c: DecompressPointer r4
    //     0xcd536c: add             x4, x4, HEAP, lsl #32
    // 0xcd5370: stur            x4, [fp, #-0x10]
    // 0xcd5374: LoadField: r1 = r3->field_b
    //     0xcd5374: ldur            w1, [x3, #0xb]
    // 0xcd5378: DecompressPointer r1
    //     0xcd5378: add             x1, x1, HEAP, lsl #32
    // 0xcd537c: cmp             w1, NULL
    // 0xcd5380: b.eq            #0xcd56b8
    // 0xcd5384: LoadField: r5 = r1->field_1b
    //     0xcd5384: ldur            w5, [x1, #0x1b]
    // 0xcd5388: DecompressPointer r5
    //     0xcd5388: add             x5, x5, HEAP, lsl #32
    // 0xcd538c: stur            x5, [fp, #-8]
    // 0xcd5390: r1 = Function '<anonymous closure>':.
    //     0xcd5390: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e00] AnonymousClosure: (0xcd59b8), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedContainerState::forEachTween (0xcd5340)
    //     0xcd5394: ldr             x1, [x1, #0xe00]
    // 0xcd5398: r2 = Null
    //     0xcd5398: mov             x2, NULL
    // 0xcd539c: r0 = AllocateClosure()
    //     0xcd539c: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd53a0: ldur            x16, [fp, #-0x20]
    // 0xcd53a4: ldur            lr, [fp, #-0x10]
    // 0xcd53a8: stp             lr, x16, [SP, #0x10]
    // 0xcd53ac: ldur            x16, [fp, #-8]
    // 0xcd53b0: stp             x0, x16, [SP]
    // 0xcd53b4: ldur            x0, [fp, #-0x20]
    // 0xcd53b8: ClosureCall
    //     0xcd53b8: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd53bc: ldur            x2, [x0, #0x1f]
    //     0xcd53c0: blr             x2
    // 0xcd53c4: ldur            x3, [fp, #-0x18]
    // 0xcd53c8: StoreField: r3->field_23 = r0
    //     0xcd53c8: stur            w0, [x3, #0x23]
    //     0xcd53cc: ldurb           w16, [x3, #-1]
    //     0xcd53d0: ldurb           w17, [x0, #-1]
    //     0xcd53d4: and             x16, x17, x16, lsr #2
    //     0xcd53d8: tst             x16, HEAP, lsr #32
    //     0xcd53dc: b.eq            #0xcd53e4
    //     0xcd53e0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xcd53e4: LoadField: r0 = r3->field_27
    //     0xcd53e4: ldur            w0, [x3, #0x27]
    // 0xcd53e8: DecompressPointer r0
    //     0xcd53e8: add             x0, x0, HEAP, lsl #32
    // 0xcd53ec: stur            x0, [fp, #-0x10]
    // 0xcd53f0: LoadField: r1 = r3->field_b
    //     0xcd53f0: ldur            w1, [x3, #0xb]
    // 0xcd53f4: DecompressPointer r1
    //     0xcd53f4: add             x1, x1, HEAP, lsl #32
    // 0xcd53f8: cmp             w1, NULL
    // 0xcd53fc: b.eq            #0xcd56bc
    // 0xcd5400: LoadField: r4 = r1->field_1f
    //     0xcd5400: ldur            w4, [x1, #0x1f]
    // 0xcd5404: DecompressPointer r4
    //     0xcd5404: add             x4, x4, HEAP, lsl #32
    // 0xcd5408: stur            x4, [fp, #-8]
    // 0xcd540c: r1 = Function '<anonymous closure>':.
    //     0xcd540c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e08] AnonymousClosure: (0xcd5954), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedContainerState::forEachTween (0xcd5340)
    //     0xcd5410: ldr             x1, [x1, #0xe08]
    // 0xcd5414: r2 = Null
    //     0xcd5414: mov             x2, NULL
    // 0xcd5418: r0 = AllocateClosure()
    //     0xcd5418: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd541c: ldur            x16, [fp, #-0x20]
    // 0xcd5420: ldur            lr, [fp, #-0x10]
    // 0xcd5424: stp             lr, x16, [SP, #0x10]
    // 0xcd5428: ldur            x16, [fp, #-8]
    // 0xcd542c: stp             x0, x16, [SP]
    // 0xcd5430: ldur            x0, [fp, #-0x20]
    // 0xcd5434: ClosureCall
    //     0xcd5434: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd5438: ldur            x2, [x0, #0x1f]
    //     0xcd543c: blr             x2
    // 0xcd5440: ldur            x3, [fp, #-0x18]
    // 0xcd5444: StoreField: r3->field_27 = r0
    //     0xcd5444: stur            w0, [x3, #0x27]
    //     0xcd5448: ldurb           w16, [x3, #-1]
    //     0xcd544c: ldurb           w17, [x0, #-1]
    //     0xcd5450: and             x16, x17, x16, lsr #2
    //     0xcd5454: tst             x16, HEAP, lsr #32
    //     0xcd5458: b.eq            #0xcd5460
    //     0xcd545c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xcd5460: LoadField: r0 = r3->field_2b
    //     0xcd5460: ldur            w0, [x3, #0x2b]
    // 0xcd5464: DecompressPointer r0
    //     0xcd5464: add             x0, x0, HEAP, lsl #32
    // 0xcd5468: stur            x0, [fp, #-0x10]
    // 0xcd546c: LoadField: r1 = r3->field_b
    //     0xcd546c: ldur            w1, [x3, #0xb]
    // 0xcd5470: DecompressPointer r1
    //     0xcd5470: add             x1, x1, HEAP, lsl #32
    // 0xcd5474: cmp             w1, NULL
    // 0xcd5478: b.eq            #0xcd56c0
    // 0xcd547c: LoadField: r4 = r1->field_23
    //     0xcd547c: ldur            w4, [x1, #0x23]
    // 0xcd5480: DecompressPointer r4
    //     0xcd5480: add             x4, x4, HEAP, lsl #32
    // 0xcd5484: stur            x4, [fp, #-8]
    // 0xcd5488: r1 = Function '<anonymous closure>':.
    //     0xcd5488: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e10] AnonymousClosure: (0xcd58f0), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedContainerState::forEachTween (0xcd5340)
    //     0xcd548c: ldr             x1, [x1, #0xe10]
    // 0xcd5490: r2 = Null
    //     0xcd5490: mov             x2, NULL
    // 0xcd5494: r0 = AllocateClosure()
    //     0xcd5494: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd5498: ldur            x16, [fp, #-0x20]
    // 0xcd549c: ldur            lr, [fp, #-0x10]
    // 0xcd54a0: stp             lr, x16, [SP, #0x10]
    // 0xcd54a4: ldur            x16, [fp, #-8]
    // 0xcd54a8: stp             x0, x16, [SP]
    // 0xcd54ac: ldur            x0, [fp, #-0x20]
    // 0xcd54b0: ClosureCall
    //     0xcd54b0: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd54b4: ldur            x2, [x0, #0x1f]
    //     0xcd54b8: blr             x2
    // 0xcd54bc: ldur            x3, [fp, #-0x18]
    // 0xcd54c0: StoreField: r3->field_2b = r0
    //     0xcd54c0: stur            w0, [x3, #0x2b]
    //     0xcd54c4: ldurb           w16, [x3, #-1]
    //     0xcd54c8: ldurb           w17, [x0, #-1]
    //     0xcd54cc: and             x16, x17, x16, lsr #2
    //     0xcd54d0: tst             x16, HEAP, lsr #32
    //     0xcd54d4: b.eq            #0xcd54dc
    //     0xcd54d8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xcd54dc: LoadField: r0 = r3->field_b
    //     0xcd54dc: ldur            w0, [x3, #0xb]
    // 0xcd54e0: DecompressPointer r0
    //     0xcd54e0: add             x0, x0, HEAP, lsl #32
    // 0xcd54e4: cmp             w0, NULL
    // 0xcd54e8: b.eq            #0xcd56c4
    // 0xcd54ec: r1 = Function '<anonymous closure>':.
    //     0xcd54ec: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e18] AnonymousClosure: (0xcd588c), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedContainerState::forEachTween (0xcd5340)
    //     0xcd54f0: ldr             x1, [x1, #0xe18]
    // 0xcd54f4: r2 = Null
    //     0xcd54f4: mov             x2, NULL
    // 0xcd54f8: r0 = AllocateClosure()
    //     0xcd54f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd54fc: ldur            x16, [fp, #-0x20]
    // 0xcd5500: stp             NULL, x16, [SP, #0x10]
    // 0xcd5504: stp             x0, NULL, [SP]
    // 0xcd5508: ldur            x0, [fp, #-0x20]
    // 0xcd550c: ClosureCall
    //     0xcd550c: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd5510: ldur            x2, [x0, #0x1f]
    //     0xcd5514: blr             x2
    // 0xcd5518: ldur            x0, [fp, #-0x18]
    // 0xcd551c: StoreField: r0->field_2f = rNULL
    //     0xcd551c: stur            NULL, [x0, #0x2f]
    // 0xcd5520: LoadField: r3 = r0->field_33
    //     0xcd5520: ldur            w3, [x0, #0x33]
    // 0xcd5524: DecompressPointer r3
    //     0xcd5524: add             x3, x3, HEAP, lsl #32
    // 0xcd5528: stur            x3, [fp, #-0x10]
    // 0xcd552c: LoadField: r1 = r0->field_b
    //     0xcd552c: ldur            w1, [x0, #0xb]
    // 0xcd5530: DecompressPointer r1
    //     0xcd5530: add             x1, x1, HEAP, lsl #32
    // 0xcd5534: cmp             w1, NULL
    // 0xcd5538: b.eq            #0xcd56c8
    // 0xcd553c: LoadField: r4 = r1->field_2b
    //     0xcd553c: ldur            w4, [x1, #0x2b]
    // 0xcd5540: DecompressPointer r4
    //     0xcd5540: add             x4, x4, HEAP, lsl #32
    // 0xcd5544: stur            x4, [fp, #-8]
    // 0xcd5548: r1 = Function '<anonymous closure>':.
    //     0xcd5548: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e20] AnonymousClosure: (0xcd5820), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedContainerState::forEachTween (0xcd5340)
    //     0xcd554c: ldr             x1, [x1, #0xe20]
    // 0xcd5550: r2 = Null
    //     0xcd5550: mov             x2, NULL
    // 0xcd5554: r0 = AllocateClosure()
    //     0xcd5554: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd5558: ldur            x16, [fp, #-0x20]
    // 0xcd555c: ldur            lr, [fp, #-0x10]
    // 0xcd5560: stp             lr, x16, [SP, #0x10]
    // 0xcd5564: ldur            x16, [fp, #-8]
    // 0xcd5568: stp             x0, x16, [SP]
    // 0xcd556c: ldur            x0, [fp, #-0x20]
    // 0xcd5570: ClosureCall
    //     0xcd5570: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd5574: ldur            x2, [x0, #0x1f]
    //     0xcd5578: blr             x2
    // 0xcd557c: ldur            x3, [fp, #-0x18]
    // 0xcd5580: StoreField: r3->field_33 = r0
    //     0xcd5580: stur            w0, [x3, #0x33]
    //     0xcd5584: ldurb           w16, [x3, #-1]
    //     0xcd5588: ldurb           w17, [x0, #-1]
    //     0xcd558c: and             x16, x17, x16, lsr #2
    //     0xcd5590: tst             x16, HEAP, lsr #32
    //     0xcd5594: b.eq            #0xcd559c
    //     0xcd5598: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xcd559c: LoadField: r0 = r3->field_37
    //     0xcd559c: ldur            w0, [x3, #0x37]
    // 0xcd55a0: DecompressPointer r0
    //     0xcd55a0: add             x0, x0, HEAP, lsl #32
    // 0xcd55a4: stur            x0, [fp, #-0x10]
    // 0xcd55a8: LoadField: r1 = r3->field_b
    //     0xcd55a8: ldur            w1, [x3, #0xb]
    // 0xcd55ac: DecompressPointer r1
    //     0xcd55ac: add             x1, x1, HEAP, lsl #32
    // 0xcd55b0: cmp             w1, NULL
    // 0xcd55b4: b.eq            #0xcd56cc
    // 0xcd55b8: LoadField: r4 = r1->field_2f
    //     0xcd55b8: ldur            w4, [x1, #0x2f]
    // 0xcd55bc: DecompressPointer r4
    //     0xcd55bc: add             x4, x4, HEAP, lsl #32
    // 0xcd55c0: stur            x4, [fp, #-8]
    // 0xcd55c4: r1 = Function '<anonymous closure>':.
    //     0xcd55c4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e28] AnonymousClosure: (0xcd57b0), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedContainerState::forEachTween (0xcd5340)
    //     0xcd55c8: ldr             x1, [x1, #0xe28]
    // 0xcd55cc: r2 = Null
    //     0xcd55cc: mov             x2, NULL
    // 0xcd55d0: r0 = AllocateClosure()
    //     0xcd55d0: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd55d4: ldur            x16, [fp, #-0x20]
    // 0xcd55d8: ldur            lr, [fp, #-0x10]
    // 0xcd55dc: stp             lr, x16, [SP, #0x10]
    // 0xcd55e0: ldur            x16, [fp, #-8]
    // 0xcd55e4: stp             x0, x16, [SP]
    // 0xcd55e8: ldur            x0, [fp, #-0x20]
    // 0xcd55ec: ClosureCall
    //     0xcd55ec: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd55f0: ldur            x2, [x0, #0x1f]
    //     0xcd55f4: blr             x2
    // 0xcd55f8: ldur            x3, [fp, #-0x18]
    // 0xcd55fc: StoreField: r3->field_37 = r0
    //     0xcd55fc: stur            w0, [x3, #0x37]
    //     0xcd5600: ldurb           w16, [x3, #-1]
    //     0xcd5604: ldurb           w17, [x0, #-1]
    //     0xcd5608: and             x16, x17, x16, lsr #2
    //     0xcd560c: tst             x16, HEAP, lsr #32
    //     0xcd5610: b.eq            #0xcd5618
    //     0xcd5614: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xcd5618: LoadField: r0 = r3->field_b
    //     0xcd5618: ldur            w0, [x3, #0xb]
    // 0xcd561c: DecompressPointer r0
    //     0xcd561c: add             x0, x0, HEAP, lsl #32
    // 0xcd5620: cmp             w0, NULL
    // 0xcd5624: b.eq            #0xcd56d0
    // 0xcd5628: r1 = Function '<anonymous closure>':.
    //     0xcd5628: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e30] AnonymousClosure: (0xcd5748), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedContainerState::forEachTween (0xcd5340)
    //     0xcd562c: ldr             x1, [x1, #0xe30]
    // 0xcd5630: r2 = Null
    //     0xcd5630: mov             x2, NULL
    // 0xcd5634: r0 = AllocateClosure()
    //     0xcd5634: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd5638: ldur            x16, [fp, #-0x20]
    // 0xcd563c: stp             NULL, x16, [SP, #0x10]
    // 0xcd5640: stp             x0, NULL, [SP]
    // 0xcd5644: ldur            x0, [fp, #-0x20]
    // 0xcd5648: ClosureCall
    //     0xcd5648: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd564c: ldur            x2, [x0, #0x1f]
    //     0xcd5650: blr             x2
    // 0xcd5654: ldur            x0, [fp, #-0x18]
    // 0xcd5658: StoreField: r0->field_3b = rNULL
    //     0xcd5658: stur            NULL, [x0, #0x3b]
    // 0xcd565c: LoadField: r1 = r0->field_b
    //     0xcd565c: ldur            w1, [x0, #0xb]
    // 0xcd5660: DecompressPointer r1
    //     0xcd5660: add             x1, x1, HEAP, lsl #32
    // 0xcd5664: cmp             w1, NULL
    // 0xcd5668: b.eq            #0xcd56d4
    // 0xcd566c: r1 = Function '<anonymous closure>':.
    //     0xcd566c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e38] AnonymousClosure: (0xcd56d8), in [package:flutter/src/widgets/implicit_animations.dart] _AnimatedContainerState::forEachTween (0xcd5340)
    //     0xcd5670: ldr             x1, [x1, #0xe38]
    // 0xcd5674: r2 = Null
    //     0xcd5674: mov             x2, NULL
    // 0xcd5678: r0 = AllocateClosure()
    //     0xcd5678: bl              #0xec1630  ; AllocateClosureStub
    // 0xcd567c: ldur            x16, [fp, #-0x20]
    // 0xcd5680: stp             NULL, x16, [SP, #0x10]
    // 0xcd5684: stp             x0, NULL, [SP]
    // 0xcd5688: ldur            x0, [fp, #-0x20]
    // 0xcd568c: ClosureCall
    //     0xcd568c: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcd5690: ldur            x2, [x0, #0x1f]
    //     0xcd5694: blr             x2
    // 0xcd5698: ldur            x1, [fp, #-0x18]
    // 0xcd569c: StoreField: r1->field_3f = rNULL
    //     0xcd569c: stur            NULL, [x1, #0x3f]
    // 0xcd56a0: r0 = Null
    //     0xcd56a0: mov             x0, NULL
    // 0xcd56a4: LeaveFrame
    //     0xcd56a4: mov             SP, fp
    //     0xcd56a8: ldp             fp, lr, [SP], #0x10
    // 0xcd56ac: ret
    //     0xcd56ac: ret             
    // 0xcd56b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcd56b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcd56b4: b               #0xcd5368
    // 0xcd56b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd56b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd56bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd56bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd56c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd56c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd56c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd56c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd56c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd56c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd56cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd56cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd56d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd56d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcd56d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcd56d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AlignmentGeometryTween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd56d8, size: 0x64
    // 0xcd56d8: EnterFrame
    //     0xcd56d8: stp             fp, lr, [SP, #-0x10]!
    //     0xcd56dc: mov             fp, SP
    // 0xcd56e0: ldr             x0, [fp, #0x10]
    // 0xcd56e4: r2 = Null
    //     0xcd56e4: mov             x2, NULL
    // 0xcd56e8: r1 = Null
    //     0xcd56e8: mov             x1, NULL
    // 0xcd56ec: r4 = 60
    //     0xcd56ec: movz            x4, #0x3c
    // 0xcd56f0: branchIfSmi(r0, 0xcd56fc)
    //     0xcd56f0: tbz             w0, #0, #0xcd56fc
    // 0xcd56f4: r4 = LoadClassIdInstr(r0)
    //     0xcd56f4: ldur            x4, [x0, #-1]
    //     0xcd56f8: ubfx            x4, x4, #0xc, #0x14
    // 0xcd56fc: sub             x4, x4, #0xcc8
    // 0xcd5700: cmp             x4, #2
    // 0xcd5704: b.ls            #0xcd571c
    // 0xcd5708: r8 = AlignmentGeometry
    //     0xcd5708: add             x8, PP, #0x56, lsl #12  ; [pp+0x56e40] Type: AlignmentGeometry
    //     0xcd570c: ldr             x8, [x8, #0xe40]
    // 0xcd5710: r3 = Null
    //     0xcd5710: add             x3, PP, #0x56, lsl #12  ; [pp+0x56e48] Null
    //     0xcd5714: ldr             x3, [x3, #0xe48]
    // 0xcd5718: r0 = DefaultTypeTest()
    //     0xcd5718: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xcd571c: r1 = <AlignmentGeometry?>
    //     0xcd571c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e58] TypeArguments: <AlignmentGeometry?>
    //     0xcd5720: ldr             x1, [x1, #0xe58]
    // 0xcd5724: r0 = AlignmentGeometryTween()
    //     0xcd5724: bl              #0xcd573c  ; AllocateAlignmentGeometryTweenStub -> AlignmentGeometryTween (size=0x14)
    // 0xcd5728: ldr             x1, [fp, #0x10]
    // 0xcd572c: StoreField: r0->field_b = r1
    //     0xcd572c: stur            w1, [x0, #0xb]
    // 0xcd5730: LeaveFrame
    //     0xcd5730: mov             SP, fp
    //     0xcd5734: ldp             fp, lr, [SP], #0x10
    // 0xcd5738: ret
    //     0xcd5738: ret             
  }
  [closure] Matrix4Tween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd5748, size: 0x5c
    // 0xcd5748: EnterFrame
    //     0xcd5748: stp             fp, lr, [SP, #-0x10]!
    //     0xcd574c: mov             fp, SP
    // 0xcd5750: ldr             x0, [fp, #0x10]
    // 0xcd5754: r2 = Null
    //     0xcd5754: mov             x2, NULL
    // 0xcd5758: r1 = Null
    //     0xcd5758: mov             x1, NULL
    // 0xcd575c: r4 = 60
    //     0xcd575c: movz            x4, #0x3c
    // 0xcd5760: branchIfSmi(r0, 0xcd576c)
    //     0xcd5760: tbz             w0, #0, #0xcd576c
    // 0xcd5764: r4 = LoadClassIdInstr(r0)
    //     0xcd5764: ldur            x4, [x0, #-1]
    //     0xcd5768: ubfx            x4, x4, #0xc, #0x14
    // 0xcd576c: cmp             x4, #0xca1
    // 0xcd5770: b.eq            #0xcd5788
    // 0xcd5774: r8 = Matrix4
    //     0xcd5774: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a710] Type: Matrix4
    //     0xcd5778: ldr             x8, [x8, #0x710]
    // 0xcd577c: r3 = Null
    //     0xcd577c: add             x3, PP, #0x56, lsl #12  ; [pp+0x56e60] Null
    //     0xcd5780: ldr             x3, [x3, #0xe60]
    // 0xcd5784: r0 = Matrix4()
    //     0xcd5784: bl              #0x645ac4  ; IsType_Matrix4_Stub
    // 0xcd5788: r1 = <Matrix4>
    //     0xcd5788: ldr             x1, [PP, #0x2db8]  ; [pp+0x2db8] TypeArguments: <Matrix4>
    // 0xcd578c: r0 = Matrix4Tween()
    //     0xcd578c: bl              #0xcd57a4  ; AllocateMatrix4TweenStub -> Matrix4Tween (size=0x14)
    // 0xcd5790: ldr             x1, [fp, #0x10]
    // 0xcd5794: StoreField: r0->field_b = r1
    //     0xcd5794: stur            w1, [x0, #0xb]
    // 0xcd5798: LeaveFrame
    //     0xcd5798: mov             SP, fp
    //     0xcd579c: ldp             fp, lr, [SP], #0x10
    // 0xcd57a0: ret
    //     0xcd57a0: ret             
  }
  [closure] EdgeInsetsGeometryTween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd57b0, size: 0x64
    // 0xcd57b0: EnterFrame
    //     0xcd57b0: stp             fp, lr, [SP, #-0x10]!
    //     0xcd57b4: mov             fp, SP
    // 0xcd57b8: ldr             x0, [fp, #0x10]
    // 0xcd57bc: r2 = Null
    //     0xcd57bc: mov             x2, NULL
    // 0xcd57c0: r1 = Null
    //     0xcd57c0: mov             x1, NULL
    // 0xcd57c4: r4 = 60
    //     0xcd57c4: movz            x4, #0x3c
    // 0xcd57c8: branchIfSmi(r0, 0xcd57d4)
    //     0xcd57c8: tbz             w0, #0, #0xcd57d4
    // 0xcd57cc: r4 = LoadClassIdInstr(r0)
    //     0xcd57cc: ldur            x4, [x0, #-1]
    //     0xcd57d0: ubfx            x4, x4, #0xc, #0x14
    // 0xcd57d4: sub             x4, x4, #0xcb5
    // 0xcd57d8: cmp             x4, #2
    // 0xcd57dc: b.ls            #0xcd57f4
    // 0xcd57e0: r8 = EdgeInsetsGeometry
    //     0xcd57e0: add             x8, PP, #0x56, lsl #12  ; [pp+0x56e70] Type: EdgeInsetsGeometry
    //     0xcd57e4: ldr             x8, [x8, #0xe70]
    // 0xcd57e8: r3 = Null
    //     0xcd57e8: add             x3, PP, #0x56, lsl #12  ; [pp+0x56e78] Null
    //     0xcd57ec: ldr             x3, [x3, #0xe78]
    // 0xcd57f0: r0 = EdgeInsetsGeometry()
    //     0xcd57f0: bl              #0x65df0c  ; IsType_EdgeInsetsGeometry_Stub
    // 0xcd57f4: r1 = <EdgeInsetsGeometry>
    //     0xcd57f4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23dc8] TypeArguments: <EdgeInsetsGeometry>
    //     0xcd57f8: ldr             x1, [x1, #0xdc8]
    // 0xcd57fc: r0 = EdgeInsetsGeometryTween()
    //     0xcd57fc: bl              #0xcd5814  ; AllocateEdgeInsetsGeometryTweenStub -> EdgeInsetsGeometryTween (size=0x14)
    // 0xcd5800: ldr             x1, [fp, #0x10]
    // 0xcd5804: StoreField: r0->field_b = r1
    //     0xcd5804: stur            w1, [x0, #0xb]
    // 0xcd5808: LeaveFrame
    //     0xcd5808: mov             SP, fp
    //     0xcd580c: ldp             fp, lr, [SP], #0x10
    // 0xcd5810: ret
    //     0xcd5810: ret             
  }
  [closure] BoxConstraintsTween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd5820, size: 0x60
    // 0xcd5820: EnterFrame
    //     0xcd5820: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5824: mov             fp, SP
    // 0xcd5828: ldr             x0, [fp, #0x10]
    // 0xcd582c: r2 = Null
    //     0xcd582c: mov             x2, NULL
    // 0xcd5830: r1 = Null
    //     0xcd5830: mov             x1, NULL
    // 0xcd5834: r4 = 60
    //     0xcd5834: movz            x4, #0x3c
    // 0xcd5838: branchIfSmi(r0, 0xcd5844)
    //     0xcd5838: tbz             w0, #0, #0xcd5844
    // 0xcd583c: r4 = LoadClassIdInstr(r0)
    //     0xcd583c: ldur            x4, [x0, #-1]
    //     0xcd5840: ubfx            x4, x4, #0xc, #0x14
    // 0xcd5844: sub             x4, x4, #0xc83
    // 0xcd5848: cmp             x4, #1
    // 0xcd584c: b.ls            #0xcd5860
    // 0xcd5850: r8 = BoxConstraints
    //     0xcd5850: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0xcd5854: r3 = Null
    //     0xcd5854: add             x3, PP, #0x56, lsl #12  ; [pp+0x56e88] Null
    //     0xcd5858: ldr             x3, [x3, #0xe88]
    // 0xcd585c: r0 = BoxConstraints()
    //     0xcd585c: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0xcd5860: r1 = <BoxConstraints>
    //     0xcd5860: add             x1, PP, #0x37, lsl #12  ; [pp+0x37fa8] TypeArguments: <BoxConstraints>
    //     0xcd5864: ldr             x1, [x1, #0xfa8]
    // 0xcd5868: r0 = BoxConstraintsTween()
    //     0xcd5868: bl              #0xcd5880  ; AllocateBoxConstraintsTweenStub -> BoxConstraintsTween (size=0x14)
    // 0xcd586c: ldr             x1, [fp, #0x10]
    // 0xcd5870: StoreField: r0->field_b = r1
    //     0xcd5870: stur            w1, [x0, #0xb]
    // 0xcd5874: LeaveFrame
    //     0xcd5874: mov             SP, fp
    //     0xcd5878: ldp             fp, lr, [SP], #0x10
    // 0xcd587c: ret
    //     0xcd587c: ret             
  }
  [closure] DecorationTween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd588c, size: 0x64
    // 0xcd588c: EnterFrame
    //     0xcd588c: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5890: mov             fp, SP
    // 0xcd5894: ldr             x0, [fp, #0x10]
    // 0xcd5898: r2 = Null
    //     0xcd5898: mov             x2, NULL
    // 0xcd589c: r1 = Null
    //     0xcd589c: mov             x1, NULL
    // 0xcd58a0: r4 = 60
    //     0xcd58a0: movz            x4, #0x3c
    // 0xcd58a4: branchIfSmi(r0, 0xcd58b0)
    //     0xcd58a4: tbz             w0, #0, #0xcd58b0
    // 0xcd58a8: r4 = LoadClassIdInstr(r0)
    //     0xcd58a8: ldur            x4, [x0, #-1]
    //     0xcd58ac: ubfx            x4, x4, #0xc, #0x14
    // 0xcd58b0: sub             x4, x4, #0xfe4
    // 0xcd58b4: cmp             x4, #3
    // 0xcd58b8: b.ls            #0xcd58d0
    // 0xcd58bc: r8 = Decoration
    //     0xcd58bc: add             x8, PP, #0x56, lsl #12  ; [pp+0x56e98] Type: Decoration
    //     0xcd58c0: ldr             x8, [x8, #0xe98]
    // 0xcd58c4: r3 = Null
    //     0xcd58c4: add             x3, PP, #0x56, lsl #12  ; [pp+0x56ea0] Null
    //     0xcd58c8: ldr             x3, [x3, #0xea0]
    // 0xcd58cc: r0 = Decoration()
    //     0xcd58cc: bl              #0x73b664  ; IsType_Decoration_Stub
    // 0xcd58d0: r1 = <Decoration>
    //     0xcd58d0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56eb0] TypeArguments: <Decoration>
    //     0xcd58d4: ldr             x1, [x1, #0xeb0]
    // 0xcd58d8: r0 = DecorationTween()
    //     0xcd58d8: bl              #0x92e960  ; AllocateDecorationTweenStub -> DecorationTween (size=0x14)
    // 0xcd58dc: ldr             x1, [fp, #0x10]
    // 0xcd58e0: StoreField: r0->field_b = r1
    //     0xcd58e0: stur            w1, [x0, #0xb]
    // 0xcd58e4: LeaveFrame
    //     0xcd58e4: mov             SP, fp
    //     0xcd58e8: ldp             fp, lr, [SP], #0x10
    // 0xcd58ec: ret
    //     0xcd58ec: ret             
  }
  [closure] DecorationTween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd58f0, size: 0x64
    // 0xcd58f0: EnterFrame
    //     0xcd58f0: stp             fp, lr, [SP, #-0x10]!
    //     0xcd58f4: mov             fp, SP
    // 0xcd58f8: ldr             x0, [fp, #0x10]
    // 0xcd58fc: r2 = Null
    //     0xcd58fc: mov             x2, NULL
    // 0xcd5900: r1 = Null
    //     0xcd5900: mov             x1, NULL
    // 0xcd5904: r4 = 60
    //     0xcd5904: movz            x4, #0x3c
    // 0xcd5908: branchIfSmi(r0, 0xcd5914)
    //     0xcd5908: tbz             w0, #0, #0xcd5914
    // 0xcd590c: r4 = LoadClassIdInstr(r0)
    //     0xcd590c: ldur            x4, [x0, #-1]
    //     0xcd5910: ubfx            x4, x4, #0xc, #0x14
    // 0xcd5914: sub             x4, x4, #0xfe4
    // 0xcd5918: cmp             x4, #3
    // 0xcd591c: b.ls            #0xcd5934
    // 0xcd5920: r8 = Decoration
    //     0xcd5920: add             x8, PP, #0x56, lsl #12  ; [pp+0x56e98] Type: Decoration
    //     0xcd5924: ldr             x8, [x8, #0xe98]
    // 0xcd5928: r3 = Null
    //     0xcd5928: add             x3, PP, #0x56, lsl #12  ; [pp+0x56eb8] Null
    //     0xcd592c: ldr             x3, [x3, #0xeb8]
    // 0xcd5930: r0 = Decoration()
    //     0xcd5930: bl              #0x73b664  ; IsType_Decoration_Stub
    // 0xcd5934: r1 = <Decoration>
    //     0xcd5934: add             x1, PP, #0x56, lsl #12  ; [pp+0x56eb0] TypeArguments: <Decoration>
    //     0xcd5938: ldr             x1, [x1, #0xeb0]
    // 0xcd593c: r0 = DecorationTween()
    //     0xcd593c: bl              #0x92e960  ; AllocateDecorationTweenStub -> DecorationTween (size=0x14)
    // 0xcd5940: ldr             x1, [fp, #0x10]
    // 0xcd5944: StoreField: r0->field_b = r1
    //     0xcd5944: stur            w1, [x0, #0xb]
    // 0xcd5948: LeaveFrame
    //     0xcd5948: mov             SP, fp
    //     0xcd594c: ldp             fp, lr, [SP], #0x10
    // 0xcd5950: ret
    //     0xcd5950: ret             
  }
  [closure] EdgeInsetsGeometryTween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd5954, size: 0x64
    // 0xcd5954: EnterFrame
    //     0xcd5954: stp             fp, lr, [SP, #-0x10]!
    //     0xcd5958: mov             fp, SP
    // 0xcd595c: ldr             x0, [fp, #0x10]
    // 0xcd5960: r2 = Null
    //     0xcd5960: mov             x2, NULL
    // 0xcd5964: r1 = Null
    //     0xcd5964: mov             x1, NULL
    // 0xcd5968: r4 = 60
    //     0xcd5968: movz            x4, #0x3c
    // 0xcd596c: branchIfSmi(r0, 0xcd5978)
    //     0xcd596c: tbz             w0, #0, #0xcd5978
    // 0xcd5970: r4 = LoadClassIdInstr(r0)
    //     0xcd5970: ldur            x4, [x0, #-1]
    //     0xcd5974: ubfx            x4, x4, #0xc, #0x14
    // 0xcd5978: sub             x4, x4, #0xcb5
    // 0xcd597c: cmp             x4, #2
    // 0xcd5980: b.ls            #0xcd5998
    // 0xcd5984: r8 = EdgeInsetsGeometry
    //     0xcd5984: add             x8, PP, #0x56, lsl #12  ; [pp+0x56e70] Type: EdgeInsetsGeometry
    //     0xcd5988: ldr             x8, [x8, #0xe70]
    // 0xcd598c: r3 = Null
    //     0xcd598c: add             x3, PP, #0x56, lsl #12  ; [pp+0x56ec8] Null
    //     0xcd5990: ldr             x3, [x3, #0xec8]
    // 0xcd5994: r0 = EdgeInsetsGeometry()
    //     0xcd5994: bl              #0x65df0c  ; IsType_EdgeInsetsGeometry_Stub
    // 0xcd5998: r1 = <EdgeInsetsGeometry>
    //     0xcd5998: add             x1, PP, #0x23, lsl #12  ; [pp+0x23dc8] TypeArguments: <EdgeInsetsGeometry>
    //     0xcd599c: ldr             x1, [x1, #0xdc8]
    // 0xcd59a0: r0 = EdgeInsetsGeometryTween()
    //     0xcd59a0: bl              #0xcd5814  ; AllocateEdgeInsetsGeometryTweenStub -> EdgeInsetsGeometryTween (size=0x14)
    // 0xcd59a4: ldr             x1, [fp, #0x10]
    // 0xcd59a8: StoreField: r0->field_b = r1
    //     0xcd59a8: stur            w1, [x0, #0xb]
    // 0xcd59ac: LeaveFrame
    //     0xcd59ac: mov             SP, fp
    //     0xcd59b0: ldp             fp, lr, [SP], #0x10
    // 0xcd59b4: ret
    //     0xcd59b4: ret             
  }
  [closure] AlignmentGeometryTween <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xcd59b8, size: 0x64
    // 0xcd59b8: EnterFrame
    //     0xcd59b8: stp             fp, lr, [SP, #-0x10]!
    //     0xcd59bc: mov             fp, SP
    // 0xcd59c0: ldr             x0, [fp, #0x10]
    // 0xcd59c4: r2 = Null
    //     0xcd59c4: mov             x2, NULL
    // 0xcd59c8: r1 = Null
    //     0xcd59c8: mov             x1, NULL
    // 0xcd59cc: r4 = 60
    //     0xcd59cc: movz            x4, #0x3c
    // 0xcd59d0: branchIfSmi(r0, 0xcd59dc)
    //     0xcd59d0: tbz             w0, #0, #0xcd59dc
    // 0xcd59d4: r4 = LoadClassIdInstr(r0)
    //     0xcd59d4: ldur            x4, [x0, #-1]
    //     0xcd59d8: ubfx            x4, x4, #0xc, #0x14
    // 0xcd59dc: sub             x4, x4, #0xcc8
    // 0xcd59e0: cmp             x4, #2
    // 0xcd59e4: b.ls            #0xcd59fc
    // 0xcd59e8: r8 = AlignmentGeometry
    //     0xcd59e8: add             x8, PP, #0x56, lsl #12  ; [pp+0x56e40] Type: AlignmentGeometry
    //     0xcd59ec: ldr             x8, [x8, #0xe40]
    // 0xcd59f0: r3 = Null
    //     0xcd59f0: add             x3, PP, #0x56, lsl #12  ; [pp+0x56ed8] Null
    //     0xcd59f4: ldr             x3, [x3, #0xed8]
    // 0xcd59f8: r0 = DefaultTypeTest()
    //     0xcd59f8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xcd59fc: r1 = <AlignmentGeometry?>
    //     0xcd59fc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e58] TypeArguments: <AlignmentGeometry?>
    //     0xcd5a00: ldr             x1, [x1, #0xe58]
    // 0xcd5a04: r0 = AlignmentGeometryTween()
    //     0xcd5a04: bl              #0xcd573c  ; AllocateAlignmentGeometryTweenStub -> AlignmentGeometryTween (size=0x14)
    // 0xcd5a08: ldr             x1, [fp, #0x10]
    // 0xcd5a0c: StoreField: r0->field_b = r1
    //     0xcd5a0c: stur            w1, [x0, #0xb]
    // 0xcd5a10: LeaveFrame
    //     0xcd5a10: mov             SP, fp
    //     0xcd5a14: ldp             fp, lr, [SP], #0x10
    // 0xcd5a18: ret
    //     0xcd5a18: ret             
  }
}

// class id: 4830, size: 0x18, field offset: 0xc
//   const constructor, 
abstract class ImplicitlyAnimatedWidget extends StatefulWidget {
}

// class id: 4831, size: 0x40, field offset: 0x18
//   const constructor, 
class AnimatedPhysicalModel extends ImplicitlyAnimatedWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa90400, size: 0x30
    // 0xa90400: EnterFrame
    //     0xa90400: stp             fp, lr, [SP, #-0x10]!
    //     0xa90404: mov             fp, SP
    // 0xa90408: mov             x0, x1
    // 0xa9040c: r1 = <AnimatedPhysicalModel>
    //     0xa9040c: add             x1, PP, #0x50, lsl #12  ; [pp+0x502e8] TypeArguments: <AnimatedPhysicalModel>
    //     0xa90410: ldr             x1, [x1, #0x2e8]
    // 0xa90414: r0 = _AnimatedPhysicalModelState()
    //     0xa90414: bl              #0xa90430  ; Allocate_AnimatedPhysicalModelStateStub -> _AnimatedPhysicalModelState (size=0x34)
    // 0xa90418: r1 = Sentinel
    //     0xa90418: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9041c: StoreField: r0->field_1b = r1
    //     0xa9041c: stur            w1, [x0, #0x1b]
    // 0xa90420: StoreField: r0->field_1f = r1
    //     0xa90420: stur            w1, [x0, #0x1f]
    // 0xa90424: LeaveFrame
    //     0xa90424: mov             SP, fp
    //     0xa90428: ldp             fp, lr, [SP], #0x10
    // 0xa9042c: ret
    //     0xa9042c: ret             
  }
}

// class id: 4832, size: 0x38, field offset: 0x18
//   const constructor, 
class AnimatedDefaultTextStyle extends ImplicitlyAnimatedWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa903c4, size: 0x30
    // 0xa903c4: EnterFrame
    //     0xa903c4: stp             fp, lr, [SP, #-0x10]!
    //     0xa903c8: mov             fp, SP
    // 0xa903cc: mov             x0, x1
    // 0xa903d0: r1 = <AnimatedDefaultTextStyle>
    //     0xa903d0: add             x1, PP, #0x46, lsl #12  ; [pp+0x460e0] TypeArguments: <AnimatedDefaultTextStyle>
    //     0xa903d4: ldr             x1, [x1, #0xe0]
    // 0xa903d8: r0 = _AnimatedDefaultTextStyleState()
    //     0xa903d8: bl              #0xa903f4  ; Allocate_AnimatedDefaultTextStyleStateStub -> _AnimatedDefaultTextStyleState (size=0x28)
    // 0xa903dc: r1 = Sentinel
    //     0xa903dc: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa903e0: StoreField: r0->field_1b = r1
    //     0xa903e0: stur            w1, [x0, #0x1b]
    // 0xa903e4: StoreField: r0->field_1f = r1
    //     0xa903e4: stur            w1, [x0, #0x1f]
    // 0xa903e8: LeaveFrame
    //     0xa903e8: mov             SP, fp
    //     0xa903ec: ldp             fp, lr, [SP], #0x10
    // 0xa903f0: ret
    //     0xa903f0: ret             
  }
}

// class id: 4833, size: 0x28, field offset: 0x18
//   const constructor, 
class AnimatedOpacity extends ImplicitlyAnimatedWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa90384, size: 0x34
    // 0xa90384: EnterFrame
    //     0xa90384: stp             fp, lr, [SP, #-0x10]!
    //     0xa90388: mov             fp, SP
    // 0xa9038c: mov             x0, x1
    // 0xa90390: r1 = <AnimatedOpacity>
    //     0xa90390: add             x1, PP, #0x59, lsl #12  ; [pp+0x59dc0] TypeArguments: <AnimatedOpacity>
    //     0xa90394: ldr             x1, [x1, #0xdc0]
    // 0xa90398: r0 = _AnimatedOpacityState()
    //     0xa90398: bl              #0xa903b8  ; Allocate_AnimatedOpacityStateStub -> _AnimatedOpacityState (size=0x2c)
    // 0xa9039c: r1 = Sentinel
    //     0xa9039c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa903a0: StoreField: r0->field_27 = r1
    //     0xa903a0: stur            w1, [x0, #0x27]
    // 0xa903a4: StoreField: r0->field_1b = r1
    //     0xa903a4: stur            w1, [x0, #0x1b]
    // 0xa903a8: StoreField: r0->field_1f = r1
    //     0xa903a8: stur            w1, [x0, #0x1f]
    // 0xa903ac: LeaveFrame
    //     0xa903ac: mov             SP, fp
    //     0xa903b0: ldp             fp, lr, [SP], #0x10
    // 0xa903b4: ret
    //     0xa903b4: ret             
  }
}

// class id: 4834, size: 0x2c, field offset: 0x18
//   const constructor, 
class AnimatedRotation extends ImplicitlyAnimatedWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa90344, size: 0x34
    // 0xa90344: EnterFrame
    //     0xa90344: stp             fp, lr, [SP, #-0x10]!
    //     0xa90348: mov             fp, SP
    // 0xa9034c: mov             x0, x1
    // 0xa90350: r1 = <AnimatedRotation>
    //     0xa90350: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a0b8] TypeArguments: <AnimatedRotation>
    //     0xa90354: ldr             x1, [x1, #0xb8]
    // 0xa90358: r0 = _AnimatedRotationState()
    //     0xa90358: bl              #0xa90378  ; Allocate_AnimatedRotationStateStub -> _AnimatedRotationState (size=0x2c)
    // 0xa9035c: r1 = Sentinel
    //     0xa9035c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa90360: StoreField: r0->field_27 = r1
    //     0xa90360: stur            w1, [x0, #0x27]
    // 0xa90364: StoreField: r0->field_1b = r1
    //     0xa90364: stur            w1, [x0, #0x1b]
    // 0xa90368: StoreField: r0->field_1f = r1
    //     0xa90368: stur            w1, [x0, #0x1f]
    // 0xa9036c: LeaveFrame
    //     0xa9036c: mov             SP, fp
    //     0xa90370: ldp             fp, lr, [SP], #0x10
    // 0xa90374: ret
    //     0xa90374: ret             
  }
}

// class id: 4835, size: 0x3c, field offset: 0x18
//   const constructor, 
class AnimatedPositioned extends ImplicitlyAnimatedWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa90308, size: 0x30
    // 0xa90308: EnterFrame
    //     0xa90308: stp             fp, lr, [SP, #-0x10]!
    //     0xa9030c: mov             fp, SP
    // 0xa90310: mov             x0, x1
    // 0xa90314: r1 = <AnimatedPositioned>
    //     0xa90314: add             x1, PP, #0x59, lsl #12  ; [pp+0x59dc8] TypeArguments: <AnimatedPositioned>
    //     0xa90318: ldr             x1, [x1, #0xdc8]
    // 0xa9031c: r0 = _AnimatedPositionedState()
    //     0xa9031c: bl              #0xa90338  ; Allocate_AnimatedPositionedStateStub -> _AnimatedPositionedState (size=0x3c)
    // 0xa90320: r1 = Sentinel
    //     0xa90320: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa90324: StoreField: r0->field_1b = r1
    //     0xa90324: stur            w1, [x0, #0x1b]
    // 0xa90328: StoreField: r0->field_1f = r1
    //     0xa90328: stur            w1, [x0, #0x1f]
    // 0xa9032c: LeaveFrame
    //     0xa9032c: mov             SP, fp
    //     0xa90330: ldp             fp, lr, [SP], #0x10
    // 0xa90334: ret
    //     0xa90334: ret             
  }
}

// class id: 4836, size: 0x20, field offset: 0x18
class AnimatedPadding extends ImplicitlyAnimatedWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa902cc, size: 0x30
    // 0xa902cc: EnterFrame
    //     0xa902cc: stp             fp, lr, [SP, #-0x10]!
    //     0xa902d0: mov             fp, SP
    // 0xa902d4: mov             x0, x1
    // 0xa902d8: r1 = <AnimatedPadding>
    //     0xa902d8: add             x1, PP, #0x50, lsl #12  ; [pp+0x502b8] TypeArguments: <AnimatedPadding>
    //     0xa902dc: ldr             x1, [x1, #0x2b8]
    // 0xa902e0: r0 = _AnimatedPaddingState()
    //     0xa902e0: bl              #0xa902fc  ; Allocate_AnimatedPaddingStateStub -> _AnimatedPaddingState (size=0x28)
    // 0xa902e4: r1 = Sentinel
    //     0xa902e4: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa902e8: StoreField: r0->field_1b = r1
    //     0xa902e8: stur            w1, [x0, #0x1b]
    // 0xa902ec: StoreField: r0->field_1f = r1
    //     0xa902ec: stur            w1, [x0, #0x1f]
    // 0xa902f0: LeaveFrame
    //     0xa902f0: mov             SP, fp
    //     0xa902f4: ldp             fp, lr, [SP], #0x10
    // 0xa902f8: ret
    //     0xa902f8: ret             
  }
}

// class id: 4837, size: 0x40, field offset: 0x18
class AnimatedContainer extends ImplicitlyAnimatedWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa90290, size: 0x30
    // 0xa90290: EnterFrame
    //     0xa90290: stp             fp, lr, [SP, #-0x10]!
    //     0xa90294: mov             fp, SP
    // 0xa90298: mov             x0, x1
    // 0xa9029c: r1 = <AnimatedContainer>
    //     0xa9029c: add             x1, PP, #0x50, lsl #12  ; [pp+0x502f0] TypeArguments: <AnimatedContainer>
    //     0xa902a0: ldr             x1, [x1, #0x2f0]
    // 0xa902a4: r0 = _AnimatedContainerState()
    //     0xa902a4: bl              #0xa902c0  ; Allocate_AnimatedContainerStateStub -> _AnimatedContainerState (size=0x44)
    // 0xa902a8: r1 = Sentinel
    //     0xa902a8: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa902ac: StoreField: r0->field_1b = r1
    //     0xa902ac: stur            w1, [x0, #0x1b]
    // 0xa902b0: StoreField: r0->field_1f = r1
    //     0xa902b0: stur            w1, [x0, #0x1f]
    // 0xa902b4: LeaveFrame
    //     0xa902b4: mov             SP, fp
    //     0xa902b8: ldp             fp, lr, [SP], #0x10
    // 0xa902bc: ret
    //     0xa902bc: ret             
  }
  _ AnimatedContainer(/* No info */) {
    // ** addr: 0xa9b524, size: 0x268
    // 0xa9b524: EnterFrame
    //     0xa9b524: stp             fp, lr, [SP, #-0x10]!
    //     0xa9b528: mov             fp, SP
    // 0xa9b52c: mov             x16, x5
    // 0xa9b530: mov             x5, x1
    // 0xa9b534: mov             x1, x16
    // 0xa9b538: mov             x16, x3
    // 0xa9b53c: mov             x3, x2
    // 0xa9b540: mov             x2, x16
    // 0xa9b544: LoadField: r6 = r4->field_13
    //     0xa9b544: ldur            w6, [x4, #0x13]
    // 0xa9b548: LoadField: r7 = r4->field_1f
    //     0xa9b548: ldur            w7, [x4, #0x1f]
    // 0xa9b54c: DecompressPointer r7
    //     0xa9b54c: add             x7, x7, HEAP, lsl #32
    // 0xa9b550: r16 = "alignment"
    //     0xa9b550: ldr             x16, [PP, #0x74e0]  ; [pp+0x74e0] "alignment"
    // 0xa9b554: cmp             w7, w16
    // 0xa9b558: b.ne            #0xa9b57c
    // 0xa9b55c: LoadField: r7 = r4->field_23
    //     0xa9b55c: ldur            w7, [x4, #0x23]
    // 0xa9b560: DecompressPointer r7
    //     0xa9b560: add             x7, x7, HEAP, lsl #32
    // 0xa9b564: sub             w8, w6, w7
    // 0xa9b568: add             x7, fp, w8, sxtw #2
    // 0xa9b56c: ldr             x7, [x7, #8]
    // 0xa9b570: mov             x0, x7
    // 0xa9b574: r7 = 1
    //     0xa9b574: movz            x7, #0x1
    // 0xa9b578: b               #0xa9b584
    // 0xa9b57c: r0 = Null
    //     0xa9b57c: mov             x0, NULL
    // 0xa9b580: r7 = 0
    //     0xa9b580: movz            x7, #0
    // 0xa9b584: lsl             x8, x7, #1
    // 0xa9b588: lsl             w9, w8, #1
    // 0xa9b58c: add             w10, w9, #8
    // 0xa9b590: ArrayLoad: r11 = r4[r10]  ; Unknown_4
    //     0xa9b590: add             x16, x4, w10, sxtw #1
    //     0xa9b594: ldur            w11, [x16, #0xf]
    // 0xa9b598: DecompressPointer r11
    //     0xa9b598: add             x11, x11, HEAP, lsl #32
    // 0xa9b59c: r16 = "constraints"
    //     0xa9b59c: add             x16, PP, #0x28, lsl #12  ; [pp+0x283a0] "constraints"
    //     0xa9b5a0: ldr             x16, [x16, #0x3a0]
    // 0xa9b5a4: cmp             w11, w16
    // 0xa9b5a8: b.ne            #0xa9b5dc
    // 0xa9b5ac: add             w10, w9, #0xa
    // 0xa9b5b0: ArrayLoad: r9 = r4[r10]  ; Unknown_4
    //     0xa9b5b0: add             x16, x4, w10, sxtw #1
    //     0xa9b5b4: ldur            w9, [x16, #0xf]
    // 0xa9b5b8: DecompressPointer r9
    //     0xa9b5b8: add             x9, x9, HEAP, lsl #32
    // 0xa9b5bc: sub             w10, w6, w9
    // 0xa9b5c0: add             x9, fp, w10, sxtw #2
    // 0xa9b5c4: ldr             x9, [x9, #8]
    // 0xa9b5c8: add             w10, w8, #2
    // 0xa9b5cc: r8 = LoadInt32Instr(r10)
    //     0xa9b5cc: sbfx            x8, x10, #1, #0x1f
    // 0xa9b5d0: mov             x7, x8
    // 0xa9b5d4: mov             x8, x9
    // 0xa9b5d8: b               #0xa9b5e0
    // 0xa9b5dc: r8 = Null
    //     0xa9b5dc: mov             x8, NULL
    // 0xa9b5e0: lsl             x9, x7, #1
    // 0xa9b5e4: lsl             w10, w9, #1
    // 0xa9b5e8: add             w11, w10, #8
    // 0xa9b5ec: ArrayLoad: r12 = r4[r11]  ; Unknown_4
    //     0xa9b5ec: add             x16, x4, w11, sxtw #1
    //     0xa9b5f0: ldur            w12, [x16, #0xf]
    // 0xa9b5f4: DecompressPointer r12
    //     0xa9b5f4: add             x12, x12, HEAP, lsl #32
    // 0xa9b5f8: r16 = "margin"
    //     0xa9b5f8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24b08] "margin"
    //     0xa9b5fc: ldr             x16, [x16, #0xb08]
    // 0xa9b600: cmp             w12, w16
    // 0xa9b604: b.ne            #0xa9b638
    // 0xa9b608: add             w11, w10, #0xa
    // 0xa9b60c: ArrayLoad: r10 = r4[r11]  ; Unknown_4
    //     0xa9b60c: add             x16, x4, w11, sxtw #1
    //     0xa9b610: ldur            w10, [x16, #0xf]
    // 0xa9b614: DecompressPointer r10
    //     0xa9b614: add             x10, x10, HEAP, lsl #32
    // 0xa9b618: sub             w11, w6, w10
    // 0xa9b61c: add             x10, fp, w11, sxtw #2
    // 0xa9b620: ldr             x10, [x10, #8]
    // 0xa9b624: add             w11, w9, #2
    // 0xa9b628: r9 = LoadInt32Instr(r11)
    //     0xa9b628: sbfx            x9, x11, #1, #0x1f
    // 0xa9b62c: mov             x7, x9
    // 0xa9b630: mov             x9, x10
    // 0xa9b634: b               #0xa9b63c
    // 0xa9b638: r9 = Null
    //     0xa9b638: mov             x9, NULL
    // 0xa9b63c: lsl             x10, x7, #1
    // 0xa9b640: lsl             w7, w10, #1
    // 0xa9b644: add             w10, w7, #8
    // 0xa9b648: ArrayLoad: r11 = r4[r10]  ; Unknown_4
    //     0xa9b648: add             x16, x4, w10, sxtw #1
    //     0xa9b64c: ldur            w11, [x16, #0xf]
    // 0xa9b650: DecompressPointer r11
    //     0xa9b650: add             x11, x11, HEAP, lsl #32
    // 0xa9b654: r16 = "padding"
    //     0xa9b654: add             x16, PP, #0x23, lsl #12  ; [pp+0x23d88] "padding"
    //     0xa9b658: ldr             x16, [x16, #0xd88]
    // 0xa9b65c: cmp             w11, w16
    // 0xa9b660: b.ne            #0xa9b688
    // 0xa9b664: add             w10, w7, #0xa
    // 0xa9b668: ArrayLoad: r7 = r4[r10]  ; Unknown_4
    //     0xa9b668: add             x16, x4, w10, sxtw #1
    //     0xa9b66c: ldur            w7, [x16, #0xf]
    // 0xa9b670: DecompressPointer r7
    //     0xa9b670: add             x7, x7, HEAP, lsl #32
    // 0xa9b674: sub             w4, w6, w7
    // 0xa9b678: add             x6, fp, w4, sxtw #2
    // 0xa9b67c: ldr             x6, [x6, #8]
    // 0xa9b680: mov             x7, x6
    // 0xa9b684: b               #0xa9b68c
    // 0xa9b688: r7 = Null
    //     0xa9b688: mov             x7, NULL
    // 0xa9b68c: r6 = Instance_Clip
    //     0xa9b68c: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa9b690: ldr             x6, [x6, #0x750]
    // 0xa9b694: r4 = Instance__Linear
    //     0xa9b694: ldr             x4, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xa9b698: StoreField: r5->field_1b = r0
    //     0xa9b698: stur            w0, [x5, #0x1b]
    //     0xa9b69c: ldurb           w16, [x5, #-1]
    //     0xa9b6a0: ldurb           w17, [x0, #-1]
    //     0xa9b6a4: and             x16, x17, x16, lsr #2
    //     0xa9b6a8: tst             x16, HEAP, lsr #32
    //     0xa9b6ac: b.eq            #0xa9b6b4
    //     0xa9b6b0: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xa9b6b4: mov             x0, x7
    // 0xa9b6b8: StoreField: r5->field_1f = r0
    //     0xa9b6b8: stur            w0, [x5, #0x1f]
    //     0xa9b6bc: ldurb           w16, [x5, #-1]
    //     0xa9b6c0: ldurb           w17, [x0, #-1]
    //     0xa9b6c4: and             x16, x17, x16, lsr #2
    //     0xa9b6c8: tst             x16, HEAP, lsr #32
    //     0xa9b6cc: b.eq            #0xa9b6d4
    //     0xa9b6d0: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xa9b6d4: mov             x0, x9
    // 0xa9b6d8: StoreField: r5->field_2f = r0
    //     0xa9b6d8: stur            w0, [x5, #0x2f]
    //     0xa9b6dc: ldurb           w16, [x5, #-1]
    //     0xa9b6e0: ldurb           w17, [x0, #-1]
    //     0xa9b6e4: and             x16, x17, x16, lsr #2
    //     0xa9b6e8: tst             x16, HEAP, lsr #32
    //     0xa9b6ec: b.eq            #0xa9b6f4
    //     0xa9b6f0: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xa9b6f4: mov             x0, x3
    // 0xa9b6f8: ArrayStore: r5[0] = r0  ; List_4
    //     0xa9b6f8: stur            w0, [x5, #0x17]
    //     0xa9b6fc: ldurb           w16, [x5, #-1]
    //     0xa9b700: ldurb           w17, [x0, #-1]
    //     0xa9b704: and             x16, x17, x16, lsr #2
    //     0xa9b708: tst             x16, HEAP, lsr #32
    //     0xa9b70c: b.eq            #0xa9b714
    //     0xa9b710: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xa9b714: StoreField: r5->field_3b = r6
    //     0xa9b714: stur            w6, [x5, #0x3b]
    // 0xa9b718: mov             x0, x2
    // 0xa9b71c: StoreField: r5->field_23 = r0
    //     0xa9b71c: stur            w0, [x5, #0x23]
    //     0xa9b720: ldurb           w16, [x5, #-1]
    //     0xa9b724: ldurb           w17, [x0, #-1]
    //     0xa9b728: and             x16, x17, x16, lsr #2
    //     0xa9b72c: tst             x16, HEAP, lsr #32
    //     0xa9b730: b.eq            #0xa9b738
    //     0xa9b734: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xa9b738: mov             x0, x8
    // 0xa9b73c: StoreField: r5->field_2b = r0
    //     0xa9b73c: stur            w0, [x5, #0x2b]
    //     0xa9b740: ldurb           w16, [x5, #-1]
    //     0xa9b744: ldurb           w17, [x0, #-1]
    //     0xa9b748: and             x16, x17, x16, lsr #2
    //     0xa9b74c: tst             x16, HEAP, lsr #32
    //     0xa9b750: b.eq            #0xa9b758
    //     0xa9b754: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xa9b758: StoreField: r5->field_b = r4
    //     0xa9b758: stur            w4, [x5, #0xb]
    // 0xa9b75c: mov             x0, x1
    // 0xa9b760: StoreField: r5->field_f = r0
    //     0xa9b760: stur            w0, [x5, #0xf]
    //     0xa9b764: ldurb           w16, [x5, #-1]
    //     0xa9b768: ldurb           w17, [x0, #-1]
    //     0xa9b76c: and             x16, x17, x16, lsr #2
    //     0xa9b770: tst             x16, HEAP, lsr #32
    //     0xa9b774: b.eq            #0xa9b77c
    //     0xa9b778: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xa9b77c: r0 = Null
    //     0xa9b77c: mov             x0, NULL
    // 0xa9b780: LeaveFrame
    //     0xa9b780: mov             SP, fp
    //     0xa9b784: ldp             fp, lr, [SP], #0x10
    // 0xa9b788: ret
    //     0xa9b788: ret             
  }
}
