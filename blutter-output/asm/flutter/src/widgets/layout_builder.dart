// lib: , url: package:flutter/src/widgets/layout_builder.dart

// class id: 1049144, size: 0x8
class :: {

  static _ _reportException(/* No info */) {
    // ** addr: 0x870bf4, size: 0x7c
    // 0x870bf4: EnterFrame
    //     0x870bf4: stp             fp, lr, [SP, #-0x10]!
    //     0x870bf8: mov             fp, SP
    // 0x870bfc: AllocStack(0x28)
    //     0x870bfc: sub             SP, SP, #0x28
    // 0x870c00: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x870c00: stur            x1, [fp, #-8]
    //     0x870c04: stur            x2, [fp, #-0x10]
    //     0x870c08: stur            x3, [fp, #-0x18]
    //     0x870c0c: stur            x5, [fp, #-0x20]
    // 0x870c10: CheckStackOverflow
    //     0x870c10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x870c14: cmp             SP, x16
    //     0x870c18: b.ls            #0x870c68
    // 0x870c1c: r0 = FlutterErrorDetails()
    //     0x870c1c: bl              #0x644b40  ; AllocateFlutterErrorDetailsStub -> FlutterErrorDetails (size=0x1c)
    // 0x870c20: mov             x2, x0
    // 0x870c24: ldur            x0, [fp, #-0x10]
    // 0x870c28: stur            x2, [fp, #-0x28]
    // 0x870c2c: StoreField: r2->field_7 = r0
    //     0x870c2c: stur            w0, [x2, #7]
    // 0x870c30: ldur            x0, [fp, #-0x18]
    // 0x870c34: StoreField: r2->field_b = r0
    //     0x870c34: stur            w0, [x2, #0xb]
    // 0x870c38: ldur            x0, [fp, #-8]
    // 0x870c3c: StoreField: r2->field_f = r0
    //     0x870c3c: stur            w0, [x2, #0xf]
    // 0x870c40: ldur            x0, [fp, #-0x20]
    // 0x870c44: StoreField: r2->field_13 = r0
    //     0x870c44: stur            w0, [x2, #0x13]
    // 0x870c48: r0 = false
    //     0x870c48: add             x0, NULL, #0x30  ; false
    // 0x870c4c: ArrayStore: r2[0] = r0  ; List_4
    //     0x870c4c: stur            w0, [x2, #0x17]
    // 0x870c50: mov             x1, x2
    // 0x870c54: r0 = reportError()
    //     0x870c54: bl              #0x63f6cc  ; [package:flutter/src/foundation/assertions.dart] FlutterError::reportError
    // 0x870c58: ldur            x0, [fp, #-0x28]
    // 0x870c5c: LeaveFrame
    //     0x870c5c: mov             SP, fp
    //     0x870c60: ldp             fp, lr, [SP], #0x10
    // 0x870c64: ret
    //     0x870c64: ret             
    // 0x870c68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x870c68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x870c6c: b               #0x870c1c
  }
}

// class id: 2999, size: 0x54, field offset: 0x54
abstract class RenderConstrainedLayoutBuilder<C1X0 bound Constraints, C1X1 bound RenderObject> extends RenderObjectWithChildMixin<C1X0 bound Constraints> {
}

// class id: 3075, size: 0x60, field offset: 0x5c
//   transformed mixin,
abstract class __RenderLayoutBuilder&RenderBox&RenderObjectWithChildMixin&RenderConstrainedLayoutBuilder extends _RenderShiftedBox&RenderBox&RenderObjectWithChildMixin
     with RenderConstrainedLayoutBuilder<C1X0 bound Constraints, C1X1 bound RenderObject> {

  _ rebuildIfNecessary(/* No info */) {
    // ** addr: 0x76ad58, size: 0x5c
    // 0x76ad58: EnterFrame
    //     0x76ad58: stp             fp, lr, [SP, #-0x10]!
    //     0x76ad5c: mov             fp, SP
    // 0x76ad60: AllocStack(0x18)
    //     0x76ad60: sub             SP, SP, #0x18
    // 0x76ad64: CheckStackOverflow
    //     0x76ad64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76ad68: cmp             SP, x16
    //     0x76ad6c: b.ls            #0x76ada8
    // 0x76ad70: LoadField: r0 = r1->field_5b
    //     0x76ad70: ldur            w0, [x1, #0x5b]
    // 0x76ad74: DecompressPointer r0
    //     0x76ad74: add             x0, x0, HEAP, lsl #32
    // 0x76ad78: cmp             w0, NULL
    // 0x76ad7c: b.eq            #0x76adb0
    // 0x76ad80: r16 = <BoxConstraints>
    //     0x76ad80: add             x16, PP, #0x37, lsl #12  ; [pp+0x37fa8] TypeArguments: <BoxConstraints>
    //     0x76ad84: ldr             x16, [x16, #0xfa8]
    // 0x76ad88: stp             x1, x16, [SP, #8]
    // 0x76ad8c: str             x0, [SP]
    // 0x76ad90: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x76ad90: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x76ad94: r0 = invokeLayoutCallback()
    //     0x76ad94: bl              #0x7688b0  ; [package:flutter/src/rendering/object.dart] RenderObject::invokeLayoutCallback
    // 0x76ad98: r0 = Null
    //     0x76ad98: mov             x0, NULL
    // 0x76ad9c: LeaveFrame
    //     0x76ad9c: mov             SP, fp
    //     0x76ada0: ldp             fp, lr, [SP], #0x10
    // 0x76ada4: ret
    //     0x76ada4: ret             
    // 0x76ada8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76ada8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76adac: b               #0x76ad70
    // 0x76adb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76adb0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ updateCallback(/* No info */) {
    // ** addr: 0x870474, size: 0xa4
    // 0x870474: EnterFrame
    //     0x870474: stp             fp, lr, [SP, #-0x10]!
    //     0x870478: mov             fp, SP
    // 0x87047c: AllocStack(0x20)
    //     0x87047c: sub             SP, SP, #0x20
    // 0x870480: SetupParameters(__RenderLayoutBuilder&RenderBox&RenderObjectWithChildMixin&RenderConstrainedLayoutBuilder this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x870480: stur            x1, [fp, #-8]
    //     0x870484: mov             x16, x2
    //     0x870488: mov             x2, x1
    //     0x87048c: mov             x1, x16
    //     0x870490: stur            x1, [fp, #-0x10]
    // 0x870494: CheckStackOverflow
    //     0x870494: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x870498: cmp             SP, x16
    //     0x87049c: b.ls            #0x870510
    // 0x8704a0: LoadField: r0 = r2->field_5b
    //     0x8704a0: ldur            w0, [x2, #0x5b]
    // 0x8704a4: DecompressPointer r0
    //     0x8704a4: add             x0, x0, HEAP, lsl #32
    // 0x8704a8: r3 = LoadClassIdInstr(r1)
    //     0x8704a8: ldur            x3, [x1, #-1]
    //     0x8704ac: ubfx            x3, x3, #0xc, #0x14
    // 0x8704b0: stp             x0, x1, [SP]
    // 0x8704b4: mov             x0, x3
    // 0x8704b8: mov             lr, x0
    // 0x8704bc: ldr             lr, [x21, lr, lsl #3]
    // 0x8704c0: blr             lr
    // 0x8704c4: tbnz            w0, #4, #0x8704d8
    // 0x8704c8: r0 = Null
    //     0x8704c8: mov             x0, NULL
    // 0x8704cc: LeaveFrame
    //     0x8704cc: mov             SP, fp
    //     0x8704d0: ldp             fp, lr, [SP], #0x10
    // 0x8704d4: ret
    //     0x8704d4: ret             
    // 0x8704d8: ldur            x1, [fp, #-8]
    // 0x8704dc: ldur            x0, [fp, #-0x10]
    // 0x8704e0: StoreField: r1->field_5b = r0
    //     0x8704e0: stur            w0, [x1, #0x5b]
    //     0x8704e4: ldurb           w16, [x1, #-1]
    //     0x8704e8: ldurb           w17, [x0, #-1]
    //     0x8704ec: and             x16, x17, x16, lsr #2
    //     0x8704f0: tst             x16, HEAP, lsr #32
    //     0x8704f4: b.eq            #0x8704fc
    //     0x8704f8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8704fc: r0 = markNeedsLayout()
    //     0x8704fc: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0x870500: r0 = Null
    //     0x870500: mov             x0, NULL
    // 0x870504: LeaveFrame
    //     0x870504: mov             SP, fp
    //     0x870508: ldp             fp, lr, [SP], #0x10
    // 0x87050c: ret
    //     0x87050c: ret             
    // 0x870510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x870510: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x870514: b               #0x8704a0
  }
}

// class id: 3076, size: 0x60, field offset: 0x60
class _RenderLayoutBuilder extends __RenderLayoutBuilder&RenderBox&RenderObjectWithChildMixin&RenderConstrainedLayoutBuilder {

  dynamic computeMinIntrinsicWidth(dynamic) {
    // ** addr: 0x72e538, size: 0x24
    // 0x72e538: EnterFrame
    //     0x72e538: stp             fp, lr, [SP, #-0x10]!
    //     0x72e53c: mov             fp, SP
    // 0x72e540: ldr             x2, [fp, #0x10]
    // 0x72e544: r1 = Function 'computeMinIntrinsicWidth':.
    //     0x72e544: add             x1, PP, #0x56, lsl #12  ; [pp+0x56d48] AnonymousClosure: static (0x7ab958), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::scrim (0x635b70)
    //     0x72e548: ldr             x1, [x1, #0xd48]
    // 0x72e54c: r0 = AllocateClosure()
    //     0x72e54c: bl              #0xec1630  ; AllocateClosureStub
    // 0x72e550: LeaveFrame
    //     0x72e550: mov             SP, fp
    //     0x72e554: ldp             fp, lr, [SP], #0x10
    // 0x72e558: ret
    //     0x72e558: ret             
  }
  _ computeDryBaseline(/* No info */) {
    // ** addr: 0x73dd44, size: 0x58
    // 0x73dd44: EnterFrame
    //     0x73dd44: stp             fp, lr, [SP, #-0x10]!
    //     0x73dd48: mov             fp, SP
    // 0x73dd4c: mov             x0, x2
    // 0x73dd50: mov             x5, x1
    // 0x73dd54: mov             x4, x2
    // 0x73dd58: r2 = Null
    //     0x73dd58: mov             x2, NULL
    // 0x73dd5c: r1 = Null
    //     0x73dd5c: mov             x1, NULL
    // 0x73dd60: r4 = 60
    //     0x73dd60: movz            x4, #0x3c
    // 0x73dd64: branchIfSmi(r0, 0x73dd70)
    //     0x73dd64: tbz             w0, #0, #0x73dd70
    // 0x73dd68: r4 = LoadClassIdInstr(r0)
    //     0x73dd68: ldur            x4, [x0, #-1]
    //     0x73dd6c: ubfx            x4, x4, #0xc, #0x14
    // 0x73dd70: sub             x4, x4, #0xc83
    // 0x73dd74: cmp             x4, #1
    // 0x73dd78: b.ls            #0x73dd8c
    // 0x73dd7c: r8 = BoxConstraints
    //     0x73dd7c: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x73dd80: r3 = Null
    //     0x73dd80: add             x3, PP, #0x50, lsl #12  ; [pp+0x500f0] Null
    //     0x73dd84: ldr             x3, [x3, #0xf0]
    // 0x73dd88: r0 = BoxConstraints()
    //     0x73dd88: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x73dd8c: r0 = Null
    //     0x73dd8c: mov             x0, NULL
    // 0x73dd90: LeaveFrame
    //     0x73dd90: mov             SP, fp
    //     0x73dd94: ldp             fp, lr, [SP], #0x10
    // 0x73dd98: ret
    //     0x73dd98: ret             
  }
  dynamic computeMinIntrinsicHeight(dynamic) {
    // ** addr: 0x7485e4, size: 0x24
    // 0x7485e4: EnterFrame
    //     0x7485e4: stp             fp, lr, [SP, #-0x10]!
    //     0x7485e8: mov             fp, SP
    // 0x7485ec: ldr             x2, [fp, #0x10]
    // 0x7485f0: r1 = Function 'computeMinIntrinsicHeight':.
    //     0x7485f0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56d38] AnonymousClosure: static (0x7ab958), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::scrim (0x635b70)
    //     0x7485f4: ldr             x1, [x1, #0xd38]
    // 0x7485f8: r0 = AllocateClosure()
    //     0x7485f8: bl              #0xec1630  ; AllocateClosureStub
    // 0x7485fc: LeaveFrame
    //     0x7485fc: mov             SP, fp
    //     0x748600: ldp             fp, lr, [SP], #0x10
    // 0x748604: ret
    //     0x748604: ret             
  }
  _ computeDistanceToActualBaseline(/* No info */) {
    // ** addr: 0x74b94c, size: 0x60
    // 0x74b94c: EnterFrame
    //     0x74b94c: stp             fp, lr, [SP, #-0x10]!
    //     0x74b950: mov             fp, SP
    // 0x74b954: CheckStackOverflow
    //     0x74b954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74b958: cmp             SP, x16
    //     0x74b95c: b.ls            #0x74b9a4
    // 0x74b960: LoadField: r0 = r1->field_57
    //     0x74b960: ldur            w0, [x1, #0x57]
    // 0x74b964: DecompressPointer r0
    //     0x74b964: add             x0, x0, HEAP, lsl #32
    // 0x74b968: cmp             w0, NULL
    // 0x74b96c: b.ne            #0x74b978
    // 0x74b970: r1 = Null
    //     0x74b970: mov             x1, NULL
    // 0x74b974: b               #0x74b984
    // 0x74b978: mov             x1, x0
    // 0x74b97c: r0 = getDistanceToActualBaseline()
    //     0x74b97c: bl              #0x74b4d4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDistanceToActualBaseline
    // 0x74b980: mov             x1, x0
    // 0x74b984: cmp             w1, NULL
    // 0x74b988: b.ne            #0x74b994
    // 0x74b98c: r0 = Null
    //     0x74b98c: mov             x0, NULL
    // 0x74b990: b               #0x74b998
    // 0x74b994: mov             x0, x1
    // 0x74b998: LeaveFrame
    //     0x74b998: mov             SP, fp
    //     0x74b99c: ldp             fp, lr, [SP], #0x10
    // 0x74b9a0: ret
    //     0x74b9a0: ret             
    // 0x74b9a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74b9a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74b9a8: b               #0x74b960
  }
  dynamic computeMaxIntrinsicWidth(dynamic) {
    // ** addr: 0x74eae8, size: 0x24
    // 0x74eae8: EnterFrame
    //     0x74eae8: stp             fp, lr, [SP, #-0x10]!
    //     0x74eaec: mov             fp, SP
    // 0x74eaf0: ldr             x2, [fp, #0x10]
    // 0x74eaf4: r1 = Function 'computeMaxIntrinsicWidth':.
    //     0x74eaf4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56d40] AnonymousClosure: static (0x7ab958), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::scrim (0x635b70)
    //     0x74eaf8: ldr             x1, [x1, #0xd40]
    // 0x74eafc: r0 = AllocateClosure()
    //     0x74eafc: bl              #0xec1630  ; AllocateClosureStub
    // 0x74eb00: LeaveFrame
    //     0x74eb00: mov             SP, fp
    //     0x74eb04: ldp             fp, lr, [SP], #0x10
    // 0x74eb08: ret
    //     0x74eb08: ret             
  }
  dynamic computeMaxIntrinsicHeight(dynamic) {
    // ** addr: 0x7526c8, size: 0x24
    // 0x7526c8: EnterFrame
    //     0x7526c8: stp             fp, lr, [SP, #-0x10]!
    //     0x7526cc: mov             fp, SP
    // 0x7526d0: ldr             x2, [fp, #0x10]
    // 0x7526d4: r1 = Function 'computeMaxIntrinsicHeight':.
    //     0x7526d4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56d30] AnonymousClosure: static (0x7ab958), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::scrim (0x635b70)
    //     0x7526d8: ldr             x1, [x1, #0xd30]
    // 0x7526dc: r0 = AllocateClosure()
    //     0x7526dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x7526e0: LeaveFrame
    //     0x7526e0: mov             SP, fp
    //     0x7526e4: ldp             fp, lr, [SP], #0x10
    // 0x7526e8: ret
    //     0x7526e8: ret             
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x76abd4, size: 0x184
    // 0x76abd4: EnterFrame
    //     0x76abd4: stp             fp, lr, [SP, #-0x10]!
    //     0x76abd8: mov             fp, SP
    // 0x76abdc: AllocStack(0x28)
    //     0x76abdc: sub             SP, SP, #0x28
    // 0x76abe0: SetupParameters(_RenderLayoutBuilder this /* r1 => r3, fp-0x10 */)
    //     0x76abe0: mov             x3, x1
    //     0x76abe4: stur            x1, [fp, #-0x10]
    // 0x76abe8: CheckStackOverflow
    //     0x76abe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76abec: cmp             SP, x16
    //     0x76abf0: b.ls            #0x76ad4c
    // 0x76abf4: LoadField: r4 = r3->field_27
    //     0x76abf4: ldur            w4, [x3, #0x27]
    // 0x76abf8: DecompressPointer r4
    //     0x76abf8: add             x4, x4, HEAP, lsl #32
    // 0x76abfc: stur            x4, [fp, #-8]
    // 0x76ac00: cmp             w4, NULL
    // 0x76ac04: b.eq            #0x76ad30
    // 0x76ac08: mov             x0, x4
    // 0x76ac0c: r2 = Null
    //     0x76ac0c: mov             x2, NULL
    // 0x76ac10: r1 = Null
    //     0x76ac10: mov             x1, NULL
    // 0x76ac14: r4 = LoadClassIdInstr(r0)
    //     0x76ac14: ldur            x4, [x0, #-1]
    //     0x76ac18: ubfx            x4, x4, #0xc, #0x14
    // 0x76ac1c: sub             x4, x4, #0xc83
    // 0x76ac20: cmp             x4, #1
    // 0x76ac24: b.ls            #0x76ac38
    // 0x76ac28: r8 = BoxConstraints
    //     0x76ac28: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x76ac2c: r3 = Null
    //     0x76ac2c: add             x3, PP, #0x50, lsl #12  ; [pp+0x500e0] Null
    //     0x76ac30: ldr             x3, [x3, #0xe0]
    // 0x76ac34: r0 = BoxConstraints()
    //     0x76ac34: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x76ac38: ldur            x1, [fp, #-0x10]
    // 0x76ac3c: r0 = rebuildIfNecessary()
    //     0x76ac3c: bl              #0x76ad58  ; [package:flutter/src/widgets/layout_builder.dart] __RenderLayoutBuilder&RenderBox&RenderObjectWithChildMixin&RenderConstrainedLayoutBuilder::rebuildIfNecessary
    // 0x76ac40: ldur            x3, [fp, #-0x10]
    // 0x76ac44: LoadField: r1 = r3->field_57
    //     0x76ac44: ldur            w1, [x3, #0x57]
    // 0x76ac48: DecompressPointer r1
    //     0x76ac48: add             x1, x1, HEAP, lsl #32
    // 0x76ac4c: cmp             w1, NULL
    // 0x76ac50: b.eq            #0x76acc8
    // 0x76ac54: r0 = LoadClassIdInstr(r1)
    //     0x76ac54: ldur            x0, [x1, #-1]
    //     0x76ac58: ubfx            x0, x0, #0xc, #0x14
    // 0x76ac5c: r16 = true
    //     0x76ac5c: add             x16, NULL, #0x20  ; true
    // 0x76ac60: str             x16, [SP]
    // 0x76ac64: ldur            x2, [fp, #-8]
    // 0x76ac68: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x76ac68: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x76ac6c: ldr             x4, [x4, #0x5c0]
    // 0x76ac70: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x76ac70: movz            x17, #0xed1d
    //     0x76ac74: add             lr, x0, x17
    //     0x76ac78: ldr             lr, [x21, lr, lsl #3]
    //     0x76ac7c: blr             lr
    // 0x76ac80: ldur            x0, [fp, #-0x10]
    // 0x76ac84: LoadField: r1 = r0->field_57
    //     0x76ac84: ldur            w1, [x0, #0x57]
    // 0x76ac88: DecompressPointer r1
    //     0x76ac88: add             x1, x1, HEAP, lsl #32
    // 0x76ac8c: cmp             w1, NULL
    // 0x76ac90: b.eq            #0x76ad54
    // 0x76ac94: r0 = size()
    //     0x76ac94: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x76ac98: ldur            x1, [fp, #-8]
    // 0x76ac9c: mov             x2, x0
    // 0x76aca0: r0 = constrain()
    //     0x76aca0: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x76aca4: ldur            x2, [fp, #-0x10]
    // 0x76aca8: StoreField: r2->field_53 = r0
    //     0x76aca8: stur            w0, [x2, #0x53]
    //     0x76acac: ldurb           w16, [x2, #-1]
    //     0x76acb0: ldurb           w17, [x0, #-1]
    //     0x76acb4: and             x16, x17, x16, lsr #2
    //     0x76acb8: tst             x16, HEAP, lsr #32
    //     0x76acbc: b.eq            #0x76acc4
    //     0x76acc0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x76acc4: b               #0x76ad20
    // 0x76acc8: mov             x2, x3
    // 0x76accc: ldur            x1, [fp, #-8]
    // 0x76acd0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x76acd0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x76acd4: r0 = constrainWidth()
    //     0x76acd4: bl              #0x6cea58  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainWidth
    // 0x76acd8: ldur            x1, [fp, #-8]
    // 0x76acdc: stur            d0, [fp, #-0x18]
    // 0x76ace0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x76ace0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x76ace4: r0 = constrainHeight()
    //     0x76ace4: bl              #0x6ce9e4  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainHeight
    // 0x76ace8: stur            d0, [fp, #-0x20]
    // 0x76acec: r0 = Size()
    //     0x76acec: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x76acf0: ldur            d0, [fp, #-0x18]
    // 0x76acf4: StoreField: r0->field_7 = d0
    //     0x76acf4: stur            d0, [x0, #7]
    // 0x76acf8: ldur            d0, [fp, #-0x20]
    // 0x76acfc: StoreField: r0->field_f = d0
    //     0x76acfc: stur            d0, [x0, #0xf]
    // 0x76ad00: ldur            x1, [fp, #-0x10]
    // 0x76ad04: StoreField: r1->field_53 = r0
    //     0x76ad04: stur            w0, [x1, #0x53]
    //     0x76ad08: ldurb           w16, [x1, #-1]
    //     0x76ad0c: ldurb           w17, [x0, #-1]
    //     0x76ad10: and             x16, x17, x16, lsr #2
    //     0x76ad14: tst             x16, HEAP, lsr #32
    //     0x76ad18: b.eq            #0x76ad20
    //     0x76ad1c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x76ad20: r0 = Null
    //     0x76ad20: mov             x0, NULL
    // 0x76ad24: LeaveFrame
    //     0x76ad24: mov             SP, fp
    //     0x76ad28: ldp             fp, lr, [SP], #0x10
    // 0x76ad2c: ret
    //     0x76ad2c: ret             
    // 0x76ad30: r0 = StateError()
    //     0x76ad30: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x76ad34: mov             x1, x0
    // 0x76ad38: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x76ad38: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x76ad3c: StoreField: r1->field_b = r0
    //     0x76ad3c: stur            w0, [x1, #0xb]
    // 0x76ad40: mov             x0, x1
    // 0x76ad44: r0 = Throw()
    //     0x76ad44: bl              #0xec04b8  ; ThrowStub
    // 0x76ad48: brk             #0
    // 0x76ad4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76ad4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76ad50: b               #0x76abf4
    // 0x76ad54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76ad54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ paint(/* No info */) {
    // ** addr: 0x795a38, size: 0x48
    // 0x795a38: EnterFrame
    //     0x795a38: stp             fp, lr, [SP, #-0x10]!
    //     0x795a3c: mov             fp, SP
    // 0x795a40: mov             x0, x1
    // 0x795a44: mov             x1, x2
    // 0x795a48: CheckStackOverflow
    //     0x795a48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x795a4c: cmp             SP, x16
    //     0x795a50: b.ls            #0x795a78
    // 0x795a54: LoadField: r2 = r0->field_57
    //     0x795a54: ldur            w2, [x0, #0x57]
    // 0x795a58: DecompressPointer r2
    //     0x795a58: add             x2, x2, HEAP, lsl #32
    // 0x795a5c: cmp             w2, NULL
    // 0x795a60: b.eq            #0x795a68
    // 0x795a64: r0 = paintChild()
    //     0x795a64: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x795a68: r0 = Null
    //     0x795a68: mov             x0, NULL
    // 0x795a6c: LeaveFrame
    //     0x795a6c: mov             SP, fp
    //     0x795a70: ldp             fp, lr, [SP], #0x10
    // 0x795a74: ret
    //     0x795a74: ret             
    // 0x795a78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x795a78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x795a7c: b               #0x795a54
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x7fce38, size: 0x7c
    // 0x7fce38: EnterFrame
    //     0x7fce38: stp             fp, lr, [SP, #-0x10]!
    //     0x7fce3c: mov             fp, SP
    // 0x7fce40: CheckStackOverflow
    //     0x7fce40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fce44: cmp             SP, x16
    //     0x7fce48: b.ls            #0x7fceac
    // 0x7fce4c: LoadField: r0 = r1->field_57
    //     0x7fce4c: ldur            w0, [x1, #0x57]
    // 0x7fce50: DecompressPointer r0
    //     0x7fce50: add             x0, x0, HEAP, lsl #32
    // 0x7fce54: cmp             w0, NULL
    // 0x7fce58: b.ne            #0x7fce64
    // 0x7fce5c: r1 = Null
    //     0x7fce5c: mov             x1, NULL
    // 0x7fce60: b               #0x7fce8c
    // 0x7fce64: r1 = LoadClassIdInstr(r0)
    //     0x7fce64: ldur            x1, [x0, #-1]
    //     0x7fce68: ubfx            x1, x1, #0xc, #0x14
    // 0x7fce6c: mov             x16, x0
    // 0x7fce70: mov             x0, x1
    // 0x7fce74: mov             x1, x16
    // 0x7fce78: r0 = GDT[cid_x0 + 0xdf93]()
    //     0x7fce78: movz            x17, #0xdf93
    //     0x7fce7c: add             lr, x0, x17
    //     0x7fce80: ldr             lr, [x21, lr, lsl #3]
    //     0x7fce84: blr             lr
    // 0x7fce88: mov             x1, x0
    // 0x7fce8c: cmp             w1, NULL
    // 0x7fce90: b.ne            #0x7fce9c
    // 0x7fce94: r0 = false
    //     0x7fce94: add             x0, NULL, #0x30  ; false
    // 0x7fce98: b               #0x7fcea0
    // 0x7fce9c: mov             x0, x1
    // 0x7fcea0: LeaveFrame
    //     0x7fcea0: mov             SP, fp
    //     0x7fcea4: ldp             fp, lr, [SP], #0x10
    // 0x7fcea8: ret
    //     0x7fcea8: ret             
    // 0x7fceac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fceac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fceb0: b               #0x7fce4c
  }
}

// class id: 4413, size: 0x5c, field offset: 0x44
class _LayoutBuilderElement<X0 bound Constraints> extends RenderObjectElement {

  late final BuildScope _buildScope; // offset: 0x4c

  _ insertRenderObjectChild(/* No info */) {
    // ** addr: 0x7af7c0, size: 0x80
    // 0x7af7c0: EnterFrame
    //     0x7af7c0: stp             fp, lr, [SP, #-0x10]!
    //     0x7af7c4: mov             fp, SP
    // 0x7af7c8: AllocStack(0x10)
    //     0x7af7c8: sub             SP, SP, #0x10
    // 0x7af7cc: SetupParameters(dynamic _ /* r2 => r4, fp-0x10 */)
    //     0x7af7cc: mov             x4, x2
    //     0x7af7d0: stur            x2, [fp, #-0x10]
    // 0x7af7d4: CheckStackOverflow
    //     0x7af7d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7af7d8: cmp             SP, x16
    //     0x7af7dc: b.ls            #0x7af834
    // 0x7af7e0: LoadField: r3 = r1->field_3b
    //     0x7af7e0: ldur            w3, [x1, #0x3b]
    // 0x7af7e4: DecompressPointer r3
    //     0x7af7e4: add             x3, x3, HEAP, lsl #32
    // 0x7af7e8: stur            x3, [fp, #-8]
    // 0x7af7ec: cmp             w3, NULL
    // 0x7af7f0: b.eq            #0x7af83c
    // 0x7af7f4: LoadField: r2 = r1->field_43
    //     0x7af7f4: ldur            w2, [x1, #0x43]
    // 0x7af7f8: DecompressPointer r2
    //     0x7af7f8: add             x2, x2, HEAP, lsl #32
    // 0x7af7fc: mov             x0, x3
    // 0x7af800: r1 = Null
    //     0x7af800: mov             x1, NULL
    // 0x7af804: r8 = RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    //     0x7af804: ldr             x8, [PP, #0x4550]  ; [pp+0x4550] Type: RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    // 0x7af808: LoadField: r9 = r8->field_7
    //     0x7af808: ldur            x9, [x8, #7]
    // 0x7af80c: r3 = Null
    //     0x7af80c: add             x3, PP, #0x50, lsl #12  ; [pp+0x50110] Null
    //     0x7af810: ldr             x3, [x3, #0x110]
    // 0x7af814: blr             x9
    // 0x7af818: ldur            x1, [fp, #-8]
    // 0x7af81c: ldur            x2, [fp, #-0x10]
    // 0x7af820: r0 = child=()
    //     0x7af820: bl              #0x895c88  ; [package:flutter/src/rendering/shifted_box.dart] _RenderShiftedBox&RenderBox&RenderObjectWithChildMixin::child=
    // 0x7af824: r0 = Null
    //     0x7af824: mov             x0, NULL
    // 0x7af828: LeaveFrame
    //     0x7af828: mov             SP, fp
    //     0x7af82c: ldp             fp, lr, [SP], #0x10
    // 0x7af830: ret
    //     0x7af830: ret             
    // 0x7af834: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7af834: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7af838: b               #0x7af7e0
    // 0x7af83c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7af83c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ removeRenderObjectChild(/* No info */) {
    // ** addr: 0x803628, size: 0x78
    // 0x803628: EnterFrame
    //     0x803628: stp             fp, lr, [SP, #-0x10]!
    //     0x80362c: mov             fp, SP
    // 0x803630: AllocStack(0x8)
    //     0x803630: sub             SP, SP, #8
    // 0x803634: CheckStackOverflow
    //     0x803634: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x803638: cmp             SP, x16
    //     0x80363c: b.ls            #0x803694
    // 0x803640: LoadField: r3 = r1->field_3b
    //     0x803640: ldur            w3, [x1, #0x3b]
    // 0x803644: DecompressPointer r3
    //     0x803644: add             x3, x3, HEAP, lsl #32
    // 0x803648: stur            x3, [fp, #-8]
    // 0x80364c: cmp             w3, NULL
    // 0x803650: b.eq            #0x80369c
    // 0x803654: LoadField: r2 = r1->field_43
    //     0x803654: ldur            w2, [x1, #0x43]
    // 0x803658: DecompressPointer r2
    //     0x803658: add             x2, x2, HEAP, lsl #32
    // 0x80365c: mov             x0, x3
    // 0x803660: r1 = Null
    //     0x803660: mov             x1, NULL
    // 0x803664: r8 = RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    //     0x803664: ldr             x8, [PP, #0x4550]  ; [pp+0x4550] Type: RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    // 0x803668: LoadField: r9 = r8->field_7
    //     0x803668: ldur            x9, [x8, #7]
    // 0x80366c: r3 = Null
    //     0x80366c: add             x3, PP, #0x50, lsl #12  ; [pp+0x50100] Null
    //     0x803670: ldr             x3, [x3, #0x100]
    // 0x803674: blr             x9
    // 0x803678: ldur            x1, [fp, #-8]
    // 0x80367c: r2 = Null
    //     0x80367c: mov             x2, NULL
    // 0x803680: r0 = child=()
    //     0x803680: bl              #0x895c88  ; [package:flutter/src/rendering/shifted_box.dart] _RenderShiftedBox&RenderBox&RenderObjectWithChildMixin::child=
    // 0x803684: r0 = Null
    //     0x803684: mov             x0, NULL
    // 0x803688: LeaveFrame
    //     0x803688: mov             SP, fp
    //     0x80368c: ldp             fp, lr, [SP], #0x10
    // 0x803690: ret
    //     0x803690: ret             
    // 0x803694: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x803694: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x803698: b               #0x803640
    // 0x80369c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80369c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ update(/* No info */) {
    // ** addr: 0x8702e8, size: 0x18c
    // 0x8702e8: EnterFrame
    //     0x8702e8: stp             fp, lr, [SP, #-0x10]!
    //     0x8702ec: mov             fp, SP
    // 0x8702f0: AllocStack(0x28)
    //     0x8702f0: sub             SP, SP, #0x28
    // 0x8702f4: SetupParameters(_LayoutBuilderElement<X0 bound Constraints> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x8702f4: mov             x4, x1
    //     0x8702f8: mov             x3, x2
    //     0x8702fc: stur            x1, [fp, #-0x10]
    //     0x870300: stur            x2, [fp, #-0x18]
    // 0x870304: CheckStackOverflow
    //     0x870304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x870308: cmp             SP, x16
    //     0x87030c: b.ls            #0x870460
    // 0x870310: LoadField: r5 = r4->field_43
    //     0x870310: ldur            w5, [x4, #0x43]
    // 0x870314: DecompressPointer r5
    //     0x870314: add             x5, x5, HEAP, lsl #32
    // 0x870318: mov             x0, x3
    // 0x87031c: mov             x2, x5
    // 0x870320: stur            x5, [fp, #-8]
    // 0x870324: r1 = Null
    //     0x870324: mov             x1, NULL
    // 0x870328: r8 = ConstrainedLayoutBuilder<X0 bound Constraints>
    //     0x870328: add             x8, PP, #0x50, lsl #12  ; [pp+0x50170] Type: ConstrainedLayoutBuilder<X0 bound Constraints>
    //     0x87032c: ldr             x8, [x8, #0x170]
    // 0x870330: LoadField: r9 = r8->field_7
    //     0x870330: ldur            x9, [x8, #7]
    // 0x870334: r3 = Null
    //     0x870334: add             x3, PP, #0x50, lsl #12  ; [pp+0x50178] Null
    //     0x870338: ldr             x3, [x3, #0x178]
    // 0x87033c: blr             x9
    // 0x870340: ldur            x3, [fp, #-0x10]
    // 0x870344: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x870344: ldur            w4, [x3, #0x17]
    // 0x870348: DecompressPointer r4
    //     0x870348: add             x4, x4, HEAP, lsl #32
    // 0x87034c: stur            x4, [fp, #-0x20]
    // 0x870350: cmp             w4, NULL
    // 0x870354: b.eq            #0x870468
    // 0x870358: mov             x0, x4
    // 0x87035c: ldur            x2, [fp, #-8]
    // 0x870360: r1 = Null
    //     0x870360: mov             x1, NULL
    // 0x870364: r8 = ConstrainedLayoutBuilder<X0 bound Constraints>
    //     0x870364: add             x8, PP, #0x50, lsl #12  ; [pp+0x50170] Type: ConstrainedLayoutBuilder<X0 bound Constraints>
    //     0x870368: ldr             x8, [x8, #0x170]
    // 0x87036c: LoadField: r9 = r8->field_7
    //     0x87036c: ldur            x9, [x8, #7]
    // 0x870370: r3 = Null
    //     0x870370: add             x3, PP, #0x50, lsl #12  ; [pp+0x50188] Null
    //     0x870374: ldr             x3, [x3, #0x188]
    // 0x870378: blr             x9
    // 0x87037c: ldur            x1, [fp, #-0x10]
    // 0x870380: ldur            x2, [fp, #-0x18]
    // 0x870384: r0 = update()
    //     0x870384: bl              #0x86fd38  ; [package:flutter/src/widgets/framework.dart] RenderObjectElement::update
    // 0x870388: ldur            x3, [fp, #-0x10]
    // 0x87038c: LoadField: r4 = r3->field_3b
    //     0x87038c: ldur            w4, [x3, #0x3b]
    // 0x870390: DecompressPointer r4
    //     0x870390: add             x4, x4, HEAP, lsl #32
    // 0x870394: stur            x4, [fp, #-0x28]
    // 0x870398: cmp             w4, NULL
    // 0x87039c: b.eq            #0x87046c
    // 0x8703a0: mov             x0, x4
    // 0x8703a4: ldur            x2, [fp, #-8]
    // 0x8703a8: r1 = Null
    //     0x8703a8: mov             x1, NULL
    // 0x8703ac: r8 = RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    //     0x8703ac: ldr             x8, [PP, #0x4550]  ; [pp+0x4550] Type: RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    // 0x8703b0: LoadField: r9 = r8->field_7
    //     0x8703b0: ldur            x9, [x8, #7]
    // 0x8703b4: r3 = Null
    //     0x8703b4: add             x3, PP, #0x50, lsl #12  ; [pp+0x50198] Null
    //     0x8703b8: ldr             x3, [x3, #0x198]
    // 0x8703bc: blr             x9
    // 0x8703c0: ldur            x2, [fp, #-0x10]
    // 0x8703c4: r1 = Function '_rebuildWithConstraints@295188862':.
    //     0x8703c4: add             x1, PP, #0x50, lsl #12  ; [pp+0x501a8] AnonymousClosure: (0x870518), in [package:flutter/src/widgets/layout_builder.dart] _LayoutBuilderElement::_rebuildWithConstraints (0x870554)
    //     0x8703c8: ldr             x1, [x1, #0x1a8]
    // 0x8703cc: r0 = AllocateClosure()
    //     0x8703cc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8703d0: ldur            x1, [fp, #-0x28]
    // 0x8703d4: mov             x2, x0
    // 0x8703d8: r0 = updateCallback()
    //     0x8703d8: bl              #0x870474  ; [package:flutter/src/widgets/layout_builder.dart] __RenderLayoutBuilder&RenderBox&RenderObjectWithChildMixin&RenderConstrainedLayoutBuilder::updateCallback
    // 0x8703dc: ldur            x0, [fp, #-0x18]
    // 0x8703e0: LoadField: r2 = r0->field_b
    //     0x8703e0: ldur            w2, [x0, #0xb]
    // 0x8703e4: DecompressPointer r2
    //     0x8703e4: add             x2, x2, HEAP, lsl #32
    // 0x8703e8: ldur            x0, [fp, #-0x20]
    // 0x8703ec: r1 = Null
    //     0x8703ec: mov             x1, NULL
    // 0x8703f0: r8 = ConstrainedLayoutBuilder<X0 bound Constraints>
    //     0x8703f0: add             x8, PP, #0x50, lsl #12  ; [pp+0x501b0] Type: ConstrainedLayoutBuilder<X0 bound Constraints>
    //     0x8703f4: ldr             x8, [x8, #0x1b0]
    // 0x8703f8: LoadField: r9 = r8->field_7
    //     0x8703f8: ldur            x9, [x8, #7]
    // 0x8703fc: r3 = Null
    //     0x8703fc: add             x3, PP, #0x50, lsl #12  ; [pp+0x501b8] Null
    //     0x870400: ldr             x3, [x3, #0x1b8]
    // 0x870404: blr             x9
    // 0x870408: ldur            x0, [fp, #-0x10]
    // 0x87040c: r1 = true
    //     0x87040c: add             x1, NULL, #0x20  ; true
    // 0x870410: StoreField: r0->field_57 = r1
    //     0x870410: stur            w1, [x0, #0x57]
    // 0x870414: LoadField: r3 = r0->field_3b
    //     0x870414: ldur            w3, [x0, #0x3b]
    // 0x870418: DecompressPointer r3
    //     0x870418: add             x3, x3, HEAP, lsl #32
    // 0x87041c: stur            x3, [fp, #-0x18]
    // 0x870420: cmp             w3, NULL
    // 0x870424: b.eq            #0x870470
    // 0x870428: mov             x0, x3
    // 0x87042c: ldur            x2, [fp, #-8]
    // 0x870430: r1 = Null
    //     0x870430: mov             x1, NULL
    // 0x870434: r8 = RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    //     0x870434: ldr             x8, [PP, #0x4550]  ; [pp+0x4550] Type: RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    // 0x870438: LoadField: r9 = r8->field_7
    //     0x870438: ldur            x9, [x8, #7]
    // 0x87043c: r3 = Null
    //     0x87043c: add             x3, PP, #0x50, lsl #12  ; [pp+0x501c8] Null
    //     0x870440: ldr             x3, [x3, #0x1c8]
    // 0x870444: blr             x9
    // 0x870448: ldur            x1, [fp, #-0x18]
    // 0x87044c: r0 = markNeedsLayout()
    //     0x87044c: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0x870450: r0 = Null
    //     0x870450: mov             x0, NULL
    // 0x870454: LeaveFrame
    //     0x870454: mov             SP, fp
    //     0x870458: ldp             fp, lr, [SP], #0x10
    // 0x87045c: ret
    //     0x87045c: ret             
    // 0x870460: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x870460: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x870464: b               #0x870310
    // 0x870468: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x870468: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x87046c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x87046c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x870470: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x870470: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _rebuildWithConstraints(dynamic, Object?) {
    // ** addr: 0x870518, size: 0x3c
    // 0x870518: EnterFrame
    //     0x870518: stp             fp, lr, [SP, #-0x10]!
    //     0x87051c: mov             fp, SP
    // 0x870520: ldr             x0, [fp, #0x18]
    // 0x870524: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x870524: ldur            w1, [x0, #0x17]
    // 0x870528: DecompressPointer r1
    //     0x870528: add             x1, x1, HEAP, lsl #32
    // 0x87052c: CheckStackOverflow
    //     0x87052c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x870530: cmp             SP, x16
    //     0x870534: b.ls            #0x87054c
    // 0x870538: ldr             x2, [fp, #0x10]
    // 0x87053c: r0 = _rebuildWithConstraints()
    //     0x87053c: bl              #0x870554  ; [package:flutter/src/widgets/layout_builder.dart] _LayoutBuilderElement::_rebuildWithConstraints
    // 0x870540: LeaveFrame
    //     0x870540: mov             SP, fp
    //     0x870544: ldp             fp, lr, [SP], #0x10
    // 0x870548: ret
    //     0x870548: ret             
    // 0x87054c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x87054c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x870550: b               #0x870538
  }
  _ _rebuildWithConstraints(/* No info */) {
    // ** addr: 0x870554, size: 0x11c
    // 0x870554: EnterFrame
    //     0x870554: stp             fp, lr, [SP, #-0x10]!
    //     0x870558: mov             fp, SP
    // 0x87055c: AllocStack(0x28)
    //     0x87055c: sub             SP, SP, #0x28
    // 0x870560: SetupParameters(_LayoutBuilderElement<X0 bound Constraints> this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x870560: mov             x0, x2
    //     0x870564: stur            x2, [fp, #-0x10]
    //     0x870568: mov             x2, x1
    //     0x87056c: stur            x1, [fp, #-8]
    // 0x870570: CheckStackOverflow
    //     0x870570: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x870574: cmp             SP, x16
    //     0x870578: b.ls            #0x870664
    // 0x87057c: r1 = 2
    //     0x87057c: movz            x1, #0x2
    // 0x870580: r0 = AllocateContext()
    //     0x870580: bl              #0xec126c  ; AllocateContextStub
    // 0x870584: mov             x4, x0
    // 0x870588: ldur            x3, [fp, #-8]
    // 0x87058c: stur            x4, [fp, #-0x18]
    // 0x870590: StoreField: r4->field_f = r3
    //     0x870590: stur            w3, [x4, #0xf]
    // 0x870594: ldur            x5, [fp, #-0x10]
    // 0x870598: StoreField: r4->field_13 = r5
    //     0x870598: stur            w5, [x4, #0x13]
    // 0x87059c: LoadField: r2 = r3->field_43
    //     0x87059c: ldur            w2, [x3, #0x43]
    // 0x8705a0: DecompressPointer r2
    //     0x8705a0: add             x2, x2, HEAP, lsl #32
    // 0x8705a4: mov             x0, x5
    // 0x8705a8: r1 = Null
    //     0x8705a8: mov             x1, NULL
    // 0x8705ac: cmp             w2, NULL
    // 0x8705b0: b.eq            #0x8705d4
    // 0x8705b4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8705b4: ldur            w4, [x2, #0x17]
    // 0x8705b8: DecompressPointer r4
    //     0x8705b8: add             x4, x4, HEAP, lsl #32
    // 0x8705bc: r8 = X0 bound Constraints
    //     0x8705bc: add             x8, PP, #0x50, lsl #12  ; [pp+0x501d8] TypeParameter: X0 bound Constraints
    //     0x8705c0: ldr             x8, [x8, #0x1d8]
    // 0x8705c4: LoadField: r9 = r4->field_7
    //     0x8705c4: ldur            x9, [x4, #7]
    // 0x8705c8: r3 = Null
    //     0x8705c8: add             x3, PP, #0x50, lsl #12  ; [pp+0x501e0] Null
    //     0x8705cc: ldr             x3, [x3, #0x1e0]
    // 0x8705d0: blr             x9
    // 0x8705d4: ldur            x2, [fp, #-0x18]
    // 0x8705d8: r1 = Function 'updateChildCallback':.
    //     0x8705d8: add             x1, PP, #0x50, lsl #12  ; [pp+0x501f0] AnonymousClosure: (0x870670), in [package:flutter/src/widgets/layout_builder.dart] _LayoutBuilderElement::_rebuildWithConstraints (0x870554)
    //     0x8705dc: ldr             x1, [x1, #0x1f0]
    // 0x8705e0: r0 = AllocateClosure()
    //     0x8705e0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8705e4: mov             x1, x0
    // 0x8705e8: ldur            x2, [fp, #-8]
    // 0x8705ec: stur            x1, [fp, #-0x18]
    // 0x8705f0: LoadField: r0 = r2->field_57
    //     0x8705f0: ldur            w0, [x2, #0x57]
    // 0x8705f4: DecompressPointer r0
    //     0x8705f4: add             x0, x0, HEAP, lsl #32
    // 0x8705f8: tbz             w0, #4, #0x870628
    // 0x8705fc: ldur            x0, [fp, #-0x10]
    // 0x870600: LoadField: r3 = r2->field_53
    //     0x870600: ldur            w3, [x2, #0x53]
    // 0x870604: DecompressPointer r3
    //     0x870604: add             x3, x3, HEAP, lsl #32
    // 0x870608: r4 = LoadClassIdInstr(r0)
    //     0x870608: ldur            x4, [x0, #-1]
    //     0x87060c: ubfx            x4, x4, #0xc, #0x14
    // 0x870610: stp             x3, x0, [SP]
    // 0x870614: mov             x0, x4
    // 0x870618: mov             lr, x0
    // 0x87061c: ldr             lr, [x21, lr, lsl #3]
    // 0x870620: blr             lr
    // 0x870624: tbz             w0, #4, #0x870630
    // 0x870628: ldur            x0, [fp, #-0x18]
    // 0x87062c: b               #0x870634
    // 0x870630: r0 = Null
    //     0x870630: mov             x0, NULL
    // 0x870634: ldur            x2, [fp, #-8]
    // 0x870638: LoadField: r1 = r2->field_1b
    //     0x870638: ldur            w1, [x2, #0x1b]
    // 0x87063c: DecompressPointer r1
    //     0x87063c: add             x1, x1, HEAP, lsl #32
    // 0x870640: cmp             w1, NULL
    // 0x870644: b.eq            #0x87066c
    // 0x870648: str             x0, [SP]
    // 0x87064c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x87064c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x870650: r0 = buildScope()
    //     0x870650: bl              #0x6d12a8  ; [package:flutter/src/widgets/framework.dart] BuildOwner::buildScope
    // 0x870654: r0 = Null
    //     0x870654: mov             x0, NULL
    // 0x870658: LeaveFrame
    //     0x870658: mov             SP, fp
    //     0x87065c: ldp             fp, lr, [SP], #0x10
    // 0x870660: ret
    //     0x870660: ret             
    // 0x870664: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x870664: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x870668: b               #0x87057c
    // 0x87066c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x87066c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void updateChildCallback(dynamic) {
    // ** addr: 0x870670, size: 0x584
    // 0x870670: EnterFrame
    //     0x870670: stp             fp, lr, [SP, #-0x10]!
    //     0x870674: mov             fp, SP
    // 0x870678: AllocStack(0xa0)
    //     0x870678: sub             SP, SP, #0xa0
    // 0x87067c: SetupParameters()
    //     0x87067c: ldr             x0, [fp, #0x10]
    //     0x870680: ldur            w3, [x0, #0x17]
    //     0x870684: add             x3, x3, HEAP, lsl #32
    //     0x870688: stur            x3, [fp, #-0x78]
    // 0x87068c: CheckStackOverflow
    //     0x87068c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x870690: cmp             SP, x16
    //     0x870694: b.ls            #0x870bdc
    // 0x870698: LoadField: r4 = r3->field_f
    //     0x870698: ldur            w4, [x3, #0xf]
    // 0x87069c: DecompressPointer r4
    //     0x87069c: add             x4, x4, HEAP, lsl #32
    // 0x8706a0: stur            x4, [fp, #-0x70]
    // 0x8706a4: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x8706a4: ldur            w5, [x4, #0x17]
    // 0x8706a8: DecompressPointer r5
    //     0x8706a8: add             x5, x5, HEAP, lsl #32
    // 0x8706ac: stur            x5, [fp, #-0x68]
    // 0x8706b0: cmp             w5, NULL
    // 0x8706b4: b.eq            #0x870be4
    // 0x8706b8: LoadField: r6 = r4->field_43
    //     0x8706b8: ldur            w6, [x4, #0x43]
    // 0x8706bc: DecompressPointer r6
    //     0x8706bc: add             x6, x6, HEAP, lsl #32
    // 0x8706c0: mov             x0, x5
    // 0x8706c4: mov             x2, x6
    // 0x8706c8: stur            x6, [fp, #-0x60]
    // 0x8706cc: r1 = Null
    //     0x8706cc: mov             x1, NULL
    // 0x8706d0: r8 = ConstrainedLayoutBuilder<X0 bound Constraints>
    //     0x8706d0: add             x8, PP, #0x50, lsl #12  ; [pp+0x50170] Type: ConstrainedLayoutBuilder<X0 bound Constraints>
    //     0x8706d4: ldr             x8, [x8, #0x170]
    // 0x8706d8: LoadField: r9 = r8->field_7
    //     0x8706d8: ldur            x9, [x8, #7]
    // 0x8706dc: r3 = Null
    //     0x8706dc: add             x3, PP, #0x50, lsl #12  ; [pp+0x501f8] Null
    //     0x8706e0: ldr             x3, [x3, #0x1f8]
    // 0x8706e4: blr             x9
    // 0x8706e8: ldur            x3, [fp, #-0x78]
    // 0x8706ec: LoadField: r4 = r3->field_13
    //     0x8706ec: ldur            w4, [x3, #0x13]
    // 0x8706f0: DecompressPointer r4
    //     0x8706f0: add             x4, x4, HEAP, lsl #32
    // 0x8706f4: ldur            x0, [fp, #-0x68]
    // 0x8706f8: stur            x4, [fp, #-0x88]
    // 0x8706fc: LoadField: r5 = r0->field_f
    //     0x8706fc: ldur            w5, [x0, #0xf]
    // 0x870700: DecompressPointer r5
    //     0x870700: add             x5, x5, HEAP, lsl #32
    // 0x870704: mov             x0, x5
    // 0x870708: ldur            x2, [fp, #-0x60]
    // 0x87070c: stur            x5, [fp, #-0x80]
    // 0x870710: r1 = Null
    //     0x870710: mov             x1, NULL
    // 0x870714: r8 = (dynamic this, BuildContext, X0 bound Constraints) => Widget
    //     0x870714: add             x8, PP, #0x50, lsl #12  ; [pp+0x50208] FunctionType: (dynamic this, BuildContext, X0 bound Constraints) => Widget
    //     0x870718: ldr             x8, [x8, #0x208]
    // 0x87071c: LoadField: r9 = r8->field_7
    //     0x87071c: ldur            x9, [x8, #7]
    // 0x870720: r3 = Null
    //     0x870720: add             x3, PP, #0x50, lsl #12  ; [pp+0x50210] Null
    //     0x870724: ldr             x3, [x3, #0x210]
    // 0x870728: blr             x9
    // 0x87072c: ldur            x16, [fp, #-0x80]
    // 0x870730: ldur            lr, [fp, #-0x70]
    // 0x870734: stp             lr, x16, [SP, #8]
    // 0x870738: ldur            x16, [fp, #-0x88]
    // 0x87073c: str             x16, [SP]
    // 0x870740: ldur            x0, [fp, #-0x80]
    // 0x870744: ClosureCall
    //     0x870744: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x870748: ldur            x2, [x0, #0x1f]
    //     0x87074c: blr             x2
    // 0x870750: mov             x1, x0
    // 0x870754: ldur            x0, [fp, #-0x78]
    // 0x870758: LoadField: r2 = r0->field_f
    //     0x870758: ldur            w2, [x0, #0xf]
    // 0x87075c: DecompressPointer r2
    //     0x87075c: add             x2, x2, HEAP, lsl #32
    // 0x870760: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x870760: ldur            w3, [x2, #0x17]
    // 0x870764: DecompressPointer r3
    //     0x870764: add             x3, x3, HEAP, lsl #32
    // 0x870768: cmp             w3, NULL
    // 0x87076c: b.eq            #0x870be8
    // 0x870770: mov             x5, x0
    // 0x870774: mov             x0, x1
    // 0x870778: r4 = Null
    //     0x870778: mov             x4, NULL
    // 0x87077c: r3 = Null
    //     0x87077c: mov             x3, NULL
    // 0x870780: b               #0x870850
    // 0x870784: sub             SP, fp, #0xa0
    // 0x870788: mov             x2, x0
    // 0x87078c: mov             x3, x1
    // 0x870790: stur            x0, [fp, #-0x60]
    // 0x870794: stur            x1, [fp, #-0x68]
    // 0x870798: r0 = InitLateStaticField(0x818) // [package:flutter/src/widgets/framework.dart] ErrorWidget::builder
    //     0x870798: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x87079c: ldr             x0, [x0, #0x1030]
    //     0x8707a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8707a4: cmp             w0, w16
    //     0x8707a8: b.ne            #0x8707b8
    //     0x8707ac: add             x2, PP, #0x22, lsl #12  ; [pp+0x22558] Field <ErrorWidget.builder>: static late (offset: 0x818)
    //     0x8707b0: ldr             x2, [x2, #0x558]
    //     0x8707b4: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8707b8: r1 = Null
    //     0x8707b8: mov             x1, NULL
    // 0x8707bc: r2 = 4
    //     0x8707bc: movz            x2, #0x4
    // 0x8707c0: r0 = AllocateArray()
    //     0x8707c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8707c4: r16 = "building "
    //     0x8707c4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22560] "building "
    //     0x8707c8: ldr             x16, [x16, #0x560]
    // 0x8707cc: StoreField: r0->field_f = r16
    //     0x8707cc: stur            w16, [x0, #0xf]
    // 0x8707d0: ldur            x1, [fp, #-0x10]
    // 0x8707d4: LoadField: r2 = r1->field_f
    //     0x8707d4: ldur            w2, [x1, #0xf]
    // 0x8707d8: DecompressPointer r2
    //     0x8707d8: add             x2, x2, HEAP, lsl #32
    // 0x8707dc: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x8707dc: ldur            w3, [x2, #0x17]
    // 0x8707e0: DecompressPointer r3
    //     0x8707e0: add             x3, x3, HEAP, lsl #32
    // 0x8707e4: cmp             w3, NULL
    // 0x8707e8: b.eq            #0x870bec
    // 0x8707ec: StoreField: r0->field_13 = r3
    //     0x8707ec: stur            w3, [x0, #0x13]
    // 0x8707f0: str             x0, [SP]
    // 0x8707f4: r0 = _interpolate()
    //     0x8707f4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8707f8: r1 = <List<Object>>
    //     0x8707f8: ldr             x1, [PP, #0x440]  ; [pp+0x440] TypeArguments: <List<Object>>
    // 0x8707fc: stur            x0, [fp, #-0x70]
    // 0x870800: r0 = ErrorDescription()
    //     0x870800: bl              #0x64483c  ; AllocateErrorDescriptionStub -> ErrorDescription (size=0x2c)
    // 0x870804: mov             x1, x0
    // 0x870808: ldur            x2, [fp, #-0x70]
    // 0x87080c: r3 = Instance_DiagnosticLevel
    //     0x87080c: ldr             x3, [PP, #0x450]  ; [pp+0x450] Obj!DiagnosticLevel@e37021
    // 0x870810: stur            x0, [fp, #-0x70]
    // 0x870814: r0 = _ErrorDiagnostic()
    //     0x870814: bl              #0x644784  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::_ErrorDiagnostic
    // 0x870818: r1 = Function '<anonymous closure>':.
    //     0x870818: add             x1, PP, #0x50, lsl #12  ; [pp+0x50220] AnonymousClosure: (0x870c70), in [package:flutter/src/widgets/framework.dart] ComponentElement::performRebuild (0x89df80)
    //     0x87081c: ldr             x1, [x1, #0x220]
    // 0x870820: r2 = Null
    //     0x870820: mov             x2, NULL
    // 0x870824: r0 = AllocateClosure()
    //     0x870824: bl              #0xec1630  ; AllocateClosureStub
    // 0x870828: ldur            x1, [fp, #-0x70]
    // 0x87082c: ldur            x2, [fp, #-0x60]
    // 0x870830: ldur            x3, [fp, #-0x68]
    // 0x870834: mov             x5, x0
    // 0x870838: r0 = _reportException()
    //     0x870838: bl              #0x870bf4  ; [package:flutter/src/widgets/layout_builder.dart] ::_reportException
    // 0x87083c: mov             x1, x0
    // 0x870840: r0 = _defaultErrorWidgetBuilder()
    //     0x870840: bl              #0x870294  ; [package:flutter/src/widgets/framework.dart] ErrorWidget::_defaultErrorWidgetBuilder
    // 0x870844: ldur            x5, [fp, #-0x10]
    // 0x870848: ldur            x4, [fp, #-0x60]
    // 0x87084c: ldur            x3, [fp, #-0x68]
    // 0x870850: stur            x5, [fp, #-0x70]
    // 0x870854: stur            x4, [fp, #-0x78]
    // 0x870858: stur            x3, [fp, #-0x80]
    // 0x87085c: stur            x0, [fp, #-0x88]
    // 0x870860: LoadField: r6 = r5->field_f
    //     0x870860: ldur            w6, [x5, #0xf]
    // 0x870864: DecompressPointer r6
    //     0x870864: add             x6, x6, HEAP, lsl #32
    // 0x870868: stur            x6, [fp, #-0x68]
    // 0x87086c: LoadField: r7 = r6->field_47
    //     0x87086c: ldur            w7, [x6, #0x47]
    // 0x870870: DecompressPointer r7
    //     0x870870: add             x7, x7, HEAP, lsl #32
    // 0x870874: stur            x7, [fp, #-0x60]
    // 0x870878: cmp             w0, NULL
    // 0x87087c: b.ne            #0x87089c
    // 0x870880: cmp             w7, NULL
    // 0x870884: b.eq            #0x870894
    // 0x870888: mov             x1, x6
    // 0x87088c: mov             x2, x7
    // 0x870890: r0 = deactivateChild()
    //     0x870890: bl              #0x86b388  ; [package:flutter/src/widgets/framework.dart] Element::deactivateChild
    // 0x870894: r0 = Null
    //     0x870894: mov             x0, NULL
    // 0x870898: b               #0x8709e8
    // 0x87089c: mov             x2, x7
    // 0x8708a0: cmp             w2, NULL
    // 0x8708a4: b.eq            #0x8709d8
    // 0x8708a8: ldur            x3, [fp, #-0x88]
    // 0x8708ac: r0 = LoadClassIdInstr(r2)
    //     0x8708ac: ldur            x0, [x2, #-1]
    //     0x8708b0: ubfx            x0, x0, #0xc, #0x14
    // 0x8708b4: mov             x1, x2
    // 0x8708b8: r0 = GDT[cid_x0 + 0xee4]()
    //     0x8708b8: add             lr, x0, #0xee4
    //     0x8708bc: ldr             lr, [x21, lr, lsl #3]
    //     0x8708c0: blr             lr
    // 0x8708c4: ldur            x2, [fp, #-0x88]
    // 0x8708c8: cmp             w0, w2
    // 0x8708cc: b.ne            #0x87091c
    // 0x8708d0: ldur            x1, [fp, #-0x60]
    // 0x8708d4: LoadField: r0 = r1->field_f
    //     0x8708d4: ldur            w0, [x1, #0xf]
    // 0x8708d8: DecompressPointer r0
    //     0x8708d8: add             x0, x0, HEAP, lsl #32
    // 0x8708dc: r3 = 60
    //     0x8708dc: movz            x3, #0x3c
    // 0x8708e0: branchIfSmi(r0, 0x8708ec)
    //     0x8708e0: tbz             w0, #0, #0x8708ec
    // 0x8708e4: r3 = LoadClassIdInstr(r0)
    //     0x8708e4: ldur            x3, [x0, #-1]
    //     0x8708e8: ubfx            x3, x3, #0xc, #0x14
    // 0x8708ec: stp             NULL, x0, [SP]
    // 0x8708f0: mov             x0, x3
    // 0x8708f4: mov             lr, x0
    // 0x8708f8: ldr             lr, [x21, lr, lsl #3]
    // 0x8708fc: blr             lr
    // 0x870900: tbz             w0, #4, #0x870914
    // 0x870904: ldur            x1, [fp, #-0x68]
    // 0x870908: ldur            x2, [fp, #-0x60]
    // 0x87090c: r3 = Null
    //     0x87090c: mov             x3, NULL
    // 0x870910: r0 = updateSlotForChild()
    //     0x870910: bl              #0x86ece8  ; [package:flutter/src/widgets/framework.dart] Element::updateSlotForChild
    // 0x870914: ldur            x0, [fp, #-0x60]
    // 0x870918: b               #0x8709e8
    // 0x87091c: ldur            x2, [fp, #-0x60]
    // 0x870920: r0 = LoadClassIdInstr(r2)
    //     0x870920: ldur            x0, [x2, #-1]
    //     0x870924: ubfx            x0, x0, #0xc, #0x14
    // 0x870928: mov             x1, x2
    // 0x87092c: r0 = GDT[cid_x0 + 0xee4]()
    //     0x87092c: add             lr, x0, #0xee4
    //     0x870930: ldr             lr, [x21, lr, lsl #3]
    //     0x870934: blr             lr
    // 0x870938: mov             x1, x0
    // 0x87093c: ldur            x2, [fp, #-0x88]
    // 0x870940: r0 = canUpdate()
    //     0x870940: bl              #0x86bb0c  ; [package:flutter/src/widgets/framework.dart] Widget::canUpdate
    // 0x870944: tbnz            w0, #4, #0x8709b8
    // 0x870948: ldur            x2, [fp, #-0x60]
    // 0x87094c: LoadField: r0 = r2->field_f
    //     0x87094c: ldur            w0, [x2, #0xf]
    // 0x870950: DecompressPointer r0
    //     0x870950: add             x0, x0, HEAP, lsl #32
    // 0x870954: r1 = 60
    //     0x870954: movz            x1, #0x3c
    // 0x870958: branchIfSmi(r0, 0x870964)
    //     0x870958: tbz             w0, #0, #0x870964
    // 0x87095c: r1 = LoadClassIdInstr(r0)
    //     0x87095c: ldur            x1, [x0, #-1]
    //     0x870960: ubfx            x1, x1, #0xc, #0x14
    // 0x870964: stp             NULL, x0, [SP]
    // 0x870968: mov             x0, x1
    // 0x87096c: mov             lr, x0
    // 0x870970: ldr             lr, [x21, lr, lsl #3]
    // 0x870974: blr             lr
    // 0x870978: tbz             w0, #4, #0x87098c
    // 0x87097c: ldur            x1, [fp, #-0x68]
    // 0x870980: ldur            x2, [fp, #-0x60]
    // 0x870984: r3 = Null
    //     0x870984: mov             x3, NULL
    // 0x870988: r0 = updateSlotForChild()
    //     0x870988: bl              #0x86ece8  ; [package:flutter/src/widgets/framework.dart] Element::updateSlotForChild
    // 0x87098c: ldur            x3, [fp, #-0x60]
    // 0x870990: r0 = LoadClassIdInstr(r3)
    //     0x870990: ldur            x0, [x3, #-1]
    //     0x870994: ubfx            x0, x0, #0xc, #0x14
    // 0x870998: mov             x1, x3
    // 0x87099c: ldur            x2, [fp, #-0x88]
    // 0x8709a0: r0 = GDT[cid_x0 + 0xe072]()
    //     0x8709a0: movz            x17, #0xe072
    //     0x8709a4: add             lr, x0, x17
    //     0x8709a8: ldr             lr, [x21, lr, lsl #3]
    //     0x8709ac: blr             lr
    // 0x8709b0: ldur            x0, [fp, #-0x60]
    // 0x8709b4: b               #0x8709e8
    // 0x8709b8: ldur            x1, [fp, #-0x68]
    // 0x8709bc: ldur            x2, [fp, #-0x60]
    // 0x8709c0: r0 = deactivateChild()
    //     0x8709c0: bl              #0x86b388  ; [package:flutter/src/widgets/framework.dart] Element::deactivateChild
    // 0x8709c4: ldur            x1, [fp, #-0x68]
    // 0x8709c8: ldur            x2, [fp, #-0x88]
    // 0x8709cc: r3 = Null
    //     0x8709cc: mov             x3, NULL
    // 0x8709d0: r0 = inflateWidget()
    //     0x8709d0: bl              #0x86b1c8  ; [package:flutter/src/widgets/framework.dart] Element::inflateWidget
    // 0x8709d4: b               #0x8709e8
    // 0x8709d8: ldur            x1, [fp, #-0x68]
    // 0x8709dc: ldur            x2, [fp, #-0x88]
    // 0x8709e0: r3 = Null
    //     0x8709e0: mov             x3, NULL
    // 0x8709e4: r0 = inflateWidget()
    //     0x8709e4: bl              #0x86b1c8  ; [package:flutter/src/widgets/framework.dart] Element::inflateWidget
    // 0x8709e8: ldur            x1, [fp, #-0x68]
    // 0x8709ec: StoreField: r1->field_47 = r0
    //     0x8709ec: stur            w0, [x1, #0x47]
    //     0x8709f0: ldurb           w16, [x1, #-1]
    //     0x8709f4: ldurb           w17, [x0, #-1]
    //     0x8709f8: and             x16, x17, x16, lsr #2
    //     0x8709fc: tst             x16, HEAP, lsr #32
    //     0x870a00: b.eq            #0x870a08
    //     0x870a04: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x870a08: ldur            x0, [fp, #-0x70]
    // 0x870a0c: b               #0x870b44
    // 0x870a10: sub             SP, fp, #0xa0
    // 0x870a14: mov             x2, x0
    // 0x870a18: mov             x3, x1
    // 0x870a1c: stur            x0, [fp, #-0x60]
    // 0x870a20: stur            x1, [fp, #-0x68]
    // 0x870a24: r0 = InitLateStaticField(0x818) // [package:flutter/src/widgets/framework.dart] ErrorWidget::builder
    //     0x870a24: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x870a28: ldr             x0, [x0, #0x1030]
    //     0x870a2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x870a30: cmp             w0, w16
    //     0x870a34: b.ne            #0x870a44
    //     0x870a38: add             x2, PP, #0x22, lsl #12  ; [pp+0x22558] Field <ErrorWidget.builder>: static late (offset: 0x818)
    //     0x870a3c: ldr             x2, [x2, #0x558]
    //     0x870a40: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x870a44: r1 = <List<Object>>
    //     0x870a44: ldr             x1, [PP, #0x440]  ; [pp+0x440] TypeArguments: <List<Object>>
    // 0x870a48: r0 = ErrorDescription()
    //     0x870a48: bl              #0x64483c  ; AllocateErrorDescriptionStub -> ErrorDescription (size=0x2c)
    // 0x870a4c: r1 = Null
    //     0x870a4c: mov             x1, NULL
    // 0x870a50: r2 = 4
    //     0x870a50: movz            x2, #0x4
    // 0x870a54: stur            x0, [fp, #-0x70]
    // 0x870a58: r0 = AllocateArray()
    //     0x870a58: bl              #0xec22fc  ; AllocateArrayStub
    // 0x870a5c: r16 = "building "
    //     0x870a5c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22560] "building "
    //     0x870a60: ldr             x16, [x16, #0x560]
    // 0x870a64: StoreField: r0->field_f = r16
    //     0x870a64: stur            w16, [x0, #0xf]
    // 0x870a68: ldur            x1, [fp, #-0x28]
    // 0x870a6c: LoadField: r2 = r1->field_f
    //     0x870a6c: ldur            w2, [x1, #0xf]
    // 0x870a70: DecompressPointer r2
    //     0x870a70: add             x2, x2, HEAP, lsl #32
    // 0x870a74: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x870a74: ldur            w3, [x2, #0x17]
    // 0x870a78: DecompressPointer r3
    //     0x870a78: add             x3, x3, HEAP, lsl #32
    // 0x870a7c: cmp             w3, NULL
    // 0x870a80: b.eq            #0x870bf0
    // 0x870a84: StoreField: r0->field_13 = r3
    //     0x870a84: stur            w3, [x0, #0x13]
    // 0x870a88: str             x0, [SP]
    // 0x870a8c: r0 = _interpolate()
    //     0x870a8c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x870a90: ldur            x1, [fp, #-0x70]
    // 0x870a94: mov             x2, x0
    // 0x870a98: r3 = Instance_DiagnosticLevel
    //     0x870a98: ldr             x3, [PP, #0x450]  ; [pp+0x450] Obj!DiagnosticLevel@e37021
    // 0x870a9c: stur            x0, [fp, #-0x78]
    // 0x870aa0: r0 = _ErrorDiagnostic()
    //     0x870aa0: bl              #0x644784  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::_ErrorDiagnostic
    // 0x870aa4: r1 = Function '<anonymous closure>':.
    //     0x870aa4: add             x1, PP, #0x50, lsl #12  ; [pp+0x50228] AnonymousClosure: (0x870c70), in [package:flutter/src/widgets/framework.dart] ComponentElement::performRebuild (0x89df80)
    //     0x870aa8: ldr             x1, [x1, #0x228]
    // 0x870aac: r2 = Null
    //     0x870aac: mov             x2, NULL
    // 0x870ab0: r0 = AllocateClosure()
    //     0x870ab0: bl              #0xec1630  ; AllocateClosureStub
    // 0x870ab4: ldur            x1, [fp, #-0x70]
    // 0x870ab8: ldur            x2, [fp, #-0x60]
    // 0x870abc: ldur            x3, [fp, #-0x68]
    // 0x870ac0: mov             x5, x0
    // 0x870ac4: r0 = _reportException()
    //     0x870ac4: bl              #0x870bf4  ; [package:flutter/src/widgets/layout_builder.dart] ::_reportException
    // 0x870ac8: mov             x2, x0
    // 0x870acc: r0 = Closure: (FlutterErrorDetails) => Widget from Function '_defaultErrorWidgetBuilder@283042623': static.
    //     0x870acc: add             x0, PP, #0x22, lsl #12  ; [pp+0x22570] Closure: (FlutterErrorDetails) => Widget from Function '_defaultErrorWidgetBuilder@283042623': static. (0x7e54fb270264)
    //     0x870ad0: ldr             x0, [x0, #0x570]
    // 0x870ad4: stur            x2, [fp, #-0x78]
    // 0x870ad8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x870ad8: ldur            w3, [x0, #0x17]
    // 0x870adc: DecompressPointer r3
    //     0x870adc: add             x3, x3, HEAP, lsl #32
    // 0x870ae0: mov             x1, x2
    // 0x870ae4: stur            x3, [fp, #-0x70]
    // 0x870ae8: r0 = _defaultErrorWidgetBuilder()
    //     0x870ae8: bl              #0x870294  ; [package:flutter/src/widgets/framework.dart] ErrorWidget::_defaultErrorWidgetBuilder
    // 0x870aec: mov             x4, x0
    // 0x870af0: ldur            x0, [fp, #-0x28]
    // 0x870af4: stur            x4, [fp, #-0x80]
    // 0x870af8: LoadField: r5 = r0->field_f
    //     0x870af8: ldur            w5, [x0, #0xf]
    // 0x870afc: DecompressPointer r5
    //     0x870afc: add             x5, x5, HEAP, lsl #32
    // 0x870b00: stur            x5, [fp, #-0x78]
    // 0x870b04: LoadField: r6 = r5->field_f
    //     0x870b04: ldur            w6, [x5, #0xf]
    // 0x870b08: DecompressPointer r6
    //     0x870b08: add             x6, x6, HEAP, lsl #32
    // 0x870b0c: mov             x1, x5
    // 0x870b10: mov             x2, x4
    // 0x870b14: mov             x3, x6
    // 0x870b18: stur            x6, [fp, #-0x70]
    // 0x870b1c: r0 = inflateWidget()
    //     0x870b1c: bl              #0x86b1c8  ; [package:flutter/src/widgets/framework.dart] Element::inflateWidget
    // 0x870b20: ldur            x1, [fp, #-0x78]
    // 0x870b24: StoreField: r1->field_47 = r0
    //     0x870b24: stur            w0, [x1, #0x47]
    //     0x870b28: ldurb           w16, [x1, #-1]
    //     0x870b2c: ldurb           w17, [x0, #-1]
    //     0x870b30: and             x16, x17, x16, lsr #2
    //     0x870b34: tst             x16, HEAP, lsr #32
    //     0x870b38: b.eq            #0x870b40
    //     0x870b3c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x870b40: ldur            x0, [fp, #-0x28]
    // 0x870b44: r2 = false
    //     0x870b44: add             x2, NULL, #0x30  ; false
    // 0x870b48: LoadField: r1 = r0->field_f
    //     0x870b48: ldur            w1, [x0, #0xf]
    // 0x870b4c: DecompressPointer r1
    //     0x870b4c: add             x1, x1, HEAP, lsl #32
    // 0x870b50: StoreField: r1->field_57 = r2
    //     0x870b50: stur            w2, [x1, #0x57]
    // 0x870b54: LoadField: r2 = r0->field_13
    //     0x870b54: ldur            w2, [x0, #0x13]
    // 0x870b58: DecompressPointer r2
    //     0x870b58: add             x2, x2, HEAP, lsl #32
    // 0x870b5c: mov             x0, x2
    // 0x870b60: StoreField: r1->field_53 = r0
    //     0x870b60: stur            w0, [x1, #0x53]
    //     0x870b64: ldurb           w16, [x1, #-1]
    //     0x870b68: ldurb           w17, [x0, #-1]
    //     0x870b6c: and             x16, x17, x16, lsr #2
    //     0x870b70: tst             x16, HEAP, lsr #32
    //     0x870b74: b.eq            #0x870b7c
    //     0x870b78: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x870b7c: r0 = Null
    //     0x870b7c: mov             x0, NULL
    // 0x870b80: LeaveFrame
    //     0x870b80: mov             SP, fp
    //     0x870b84: ldp             fp, lr, [SP], #0x10
    // 0x870b88: ret
    //     0x870b88: ret             
    // 0x870b8c: r2 = false
    //     0x870b8c: add             x2, NULL, #0x30  ; false
    // 0x870b90: sub             SP, fp, #0xa0
    // 0x870b94: mov             x3, x0
    // 0x870b98: ldur            x0, [fp, #-0x10]
    // 0x870b9c: LoadField: r4 = r0->field_f
    //     0x870b9c: ldur            w4, [x0, #0xf]
    // 0x870ba0: DecompressPointer r4
    //     0x870ba0: add             x4, x4, HEAP, lsl #32
    // 0x870ba4: StoreField: r4->field_57 = r2
    //     0x870ba4: stur            w2, [x4, #0x57]
    // 0x870ba8: LoadField: r2 = r0->field_13
    //     0x870ba8: ldur            w2, [x0, #0x13]
    // 0x870bac: DecompressPointer r2
    //     0x870bac: add             x2, x2, HEAP, lsl #32
    // 0x870bb0: mov             x0, x2
    // 0x870bb4: StoreField: r4->field_53 = r0
    //     0x870bb4: stur            w0, [x4, #0x53]
    //     0x870bb8: ldurb           w16, [x4, #-1]
    //     0x870bbc: ldurb           w17, [x0, #-1]
    //     0x870bc0: and             x16, x17, x16, lsr #2
    //     0x870bc4: tst             x16, HEAP, lsr #32
    //     0x870bc8: b.eq            #0x870bd0
    //     0x870bcc: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x870bd0: mov             x0, x3
    // 0x870bd4: r0 = ReThrow()
    //     0x870bd4: bl              #0xec048c  ; ReThrowStub
    // 0x870bd8: brk             #0
    // 0x870bdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x870bdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x870be0: b               #0x870698
    // 0x870be4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x870be4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x870be8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x870be8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x870bec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x870bec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x870bf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x870bf0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ mount(/* No info */) {
    // ** addr: 0x890d20, size: 0x9c
    // 0x890d20: EnterFrame
    //     0x890d20: stp             fp, lr, [SP, #-0x10]!
    //     0x890d24: mov             fp, SP
    // 0x890d28: AllocStack(0x10)
    //     0x890d28: sub             SP, SP, #0x10
    // 0x890d2c: SetupParameters(_LayoutBuilderElement<X0 bound Constraints> this /* r1 => r0, fp-0x8 */)
    //     0x890d2c: mov             x0, x1
    //     0x890d30: stur            x1, [fp, #-8]
    // 0x890d34: CheckStackOverflow
    //     0x890d34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x890d38: cmp             SP, x16
    //     0x890d3c: b.ls            #0x890db0
    // 0x890d40: mov             x1, x0
    // 0x890d44: r0 = mount()
    //     0x890d44: bl              #0x8911b0  ; [package:flutter/src/widgets/framework.dart] RenderObjectElement::mount
    // 0x890d48: ldur            x3, [fp, #-8]
    // 0x890d4c: LoadField: r4 = r3->field_3b
    //     0x890d4c: ldur            w4, [x3, #0x3b]
    // 0x890d50: DecompressPointer r4
    //     0x890d50: add             x4, x4, HEAP, lsl #32
    // 0x890d54: stur            x4, [fp, #-0x10]
    // 0x890d58: cmp             w4, NULL
    // 0x890d5c: b.eq            #0x890db8
    // 0x890d60: LoadField: r2 = r3->field_43
    //     0x890d60: ldur            w2, [x3, #0x43]
    // 0x890d64: DecompressPointer r2
    //     0x890d64: add             x2, x2, HEAP, lsl #32
    // 0x890d68: mov             x0, x4
    // 0x890d6c: r1 = Null
    //     0x890d6c: mov             x1, NULL
    // 0x890d70: r8 = RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    //     0x890d70: ldr             x8, [PP, #0x4550]  ; [pp+0x4550] Type: RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    // 0x890d74: LoadField: r9 = r8->field_7
    //     0x890d74: ldur            x9, [x8, #7]
    // 0x890d78: r3 = Null
    //     0x890d78: add             x3, PP, #0x50, lsl #12  ; [pp+0x50230] Null
    //     0x890d7c: ldr             x3, [x3, #0x230]
    // 0x890d80: blr             x9
    // 0x890d84: ldur            x2, [fp, #-8]
    // 0x890d88: r1 = Function '_rebuildWithConstraints@295188862':.
    //     0x890d88: add             x1, PP, #0x50, lsl #12  ; [pp+0x501a8] AnonymousClosure: (0x870518), in [package:flutter/src/widgets/layout_builder.dart] _LayoutBuilderElement::_rebuildWithConstraints (0x870554)
    //     0x890d8c: ldr             x1, [x1, #0x1a8]
    // 0x890d90: r0 = AllocateClosure()
    //     0x890d90: bl              #0xec1630  ; AllocateClosureStub
    // 0x890d94: ldur            x1, [fp, #-0x10]
    // 0x890d98: mov             x2, x0
    // 0x890d9c: r0 = updateCallback()
    //     0x890d9c: bl              #0x870474  ; [package:flutter/src/widgets/layout_builder.dart] __RenderLayoutBuilder&RenderBox&RenderObjectWithChildMixin&RenderConstrainedLayoutBuilder::updateCallback
    // 0x890da0: r0 = Null
    //     0x890da0: mov             x0, NULL
    // 0x890da4: LeaveFrame
    //     0x890da4: mov             SP, fp
    //     0x890da8: ldp             fp, lr, [SP], #0x10
    // 0x890dac: ret
    //     0x890dac: ret             
    // 0x890db0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x890db0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x890db4: b               #0x890d40
    // 0x890db8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x890db8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  BuildScope buildScope(_LayoutBuilderElement<X0>) {
    // ** addr: 0x893a7c, size: 0x48
    // 0x893a7c: EnterFrame
    //     0x893a7c: stp             fp, lr, [SP, #-0x10]!
    //     0x893a80: mov             fp, SP
    // 0x893a84: CheckStackOverflow
    //     0x893a84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x893a88: cmp             SP, x16
    //     0x893a8c: b.ls            #0x893abc
    // 0x893a90: LoadField: r0 = r1->field_4b
    //     0x893a90: ldur            w0, [x1, #0x4b]
    // 0x893a94: DecompressPointer r0
    //     0x893a94: add             x0, x0, HEAP, lsl #32
    // 0x893a98: r16 = Sentinel
    //     0x893a98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x893a9c: cmp             w0, w16
    // 0x893aa0: b.ne            #0x893ab0
    // 0x893aa4: r2 = _buildScope
    //     0x893aa4: add             x2, PP, #0x50, lsl #12  ; [pp+0x50240] Field <_LayoutBuilderElement@295188862._buildScope@295188862>: late final (offset: 0x4c)
    //     0x893aa8: ldr             x2, [x2, #0x240]
    // 0x893aac: r0 = InitLateFinalInstanceField()
    //     0x893aac: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x893ab0: LeaveFrame
    //     0x893ab0: mov             SP, fp
    //     0x893ab4: ldp             fp, lr, [SP], #0x10
    // 0x893ab8: ret
    //     0x893ab8: ret             
    // 0x893abc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x893abc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x893ac0: b               #0x893a90
  }
  BuildScope _buildScope(_LayoutBuilderElement<X0>) {
    // ** addr: 0x893ac4, size: 0xc0
    // 0x893ac4: EnterFrame
    //     0x893ac4: stp             fp, lr, [SP, #-0x10]!
    //     0x893ac8: mov             fp, SP
    // 0x893acc: AllocStack(0x18)
    //     0x893acc: sub             SP, SP, #0x18
    // 0x893ad0: CheckStackOverflow
    //     0x893ad0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x893ad4: cmp             SP, x16
    //     0x893ad8: b.ls            #0x893b7c
    // 0x893adc: ldr             x0, [fp, #0x10]
    // 0x893ae0: r1 = 60
    //     0x893ae0: movz            x1, #0x3c
    // 0x893ae4: branchIfSmi(r0, 0x893af0)
    //     0x893ae4: tbz             w0, #0, #0x893af0
    // 0x893ae8: r1 = LoadClassIdInstr(r0)
    //     0x893ae8: ldur            x1, [x0, #-1]
    //     0x893aec: ubfx            x1, x1, #0xc, #0x14
    // 0x893af0: str             x0, [SP]
    // 0x893af4: mov             x0, x1
    // 0x893af8: r0 = GDT[cid_x0 + -0xcbe]()
    //     0x893af8: sub             lr, x0, #0xcbe
    //     0x893afc: ldr             lr, [x21, lr, lsl #3]
    //     0x893b00: blr             lr
    // 0x893b04: stur            x0, [fp, #-8]
    // 0x893b08: r0 = BuildScope()
    //     0x893b08: bl              #0x893b84  ; AllocateBuildScopeStub -> BuildScope (size=0x1c)
    // 0x893b0c: mov             x3, x0
    // 0x893b10: r0 = false
    //     0x893b10: add             x0, NULL, #0x30  ; false
    // 0x893b14: stur            x3, [fp, #-0x10]
    // 0x893b18: StoreField: r3->field_7 = r0
    //     0x893b18: stur            w0, [x3, #7]
    // 0x893b1c: StoreField: r3->field_b = r0
    //     0x893b1c: stur            w0, [x3, #0xb]
    // 0x893b20: r1 = <Element>
    //     0x893b20: ldr             x1, [PP, #0x2158]  ; [pp+0x2158] TypeArguments: <Element>
    // 0x893b24: r2 = 0
    //     0x893b24: movz            x2, #0
    // 0x893b28: r0 = _GrowableList()
    //     0x893b28: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x893b2c: ldur            x1, [fp, #-0x10]
    // 0x893b30: ArrayStore: r1[0] = r0  ; List_4
    //     0x893b30: stur            w0, [x1, #0x17]
    //     0x893b34: ldurb           w16, [x1, #-1]
    //     0x893b38: ldurb           w17, [x0, #-1]
    //     0x893b3c: and             x16, x17, x16, lsr #2
    //     0x893b40: tst             x16, HEAP, lsr #32
    //     0x893b44: b.eq            #0x893b4c
    //     0x893b48: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x893b4c: ldur            x0, [fp, #-8]
    // 0x893b50: StoreField: r1->field_f = r0
    //     0x893b50: stur            w0, [x1, #0xf]
    //     0x893b54: ldurb           w16, [x1, #-1]
    //     0x893b58: ldurb           w17, [x0, #-1]
    //     0x893b5c: and             x16, x17, x16, lsr #2
    //     0x893b60: tst             x16, HEAP, lsr #32
    //     0x893b64: b.eq            #0x893b6c
    //     0x893b68: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x893b6c: mov             x0, x1
    // 0x893b70: LeaveFrame
    //     0x893b70: mov             SP, fp
    //     0x893b74: ldp             fp, lr, [SP], #0x10
    // 0x893b78: ret
    //     0x893b78: ret             
    // 0x893b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x893b7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x893b80: b               #0x893adc
  }
  _ markNeedsBuild(/* No info */) {
    // ** addr: 0x898a98, size: 0x88
    // 0x898a98: EnterFrame
    //     0x898a98: stp             fp, lr, [SP, #-0x10]!
    //     0x898a9c: mov             fp, SP
    // 0x898aa0: AllocStack(0x10)
    //     0x898aa0: sub             SP, SP, #0x10
    // 0x898aa4: SetupParameters(_LayoutBuilderElement<X0 bound Constraints> this /* r1 => r3, fp-0x10 */)
    //     0x898aa4: mov             x3, x1
    //     0x898aa8: stur            x1, [fp, #-0x10]
    // 0x898aac: CheckStackOverflow
    //     0x898aac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x898ab0: cmp             SP, x16
    //     0x898ab4: b.ls            #0x898b14
    // 0x898ab8: LoadField: r4 = r3->field_3b
    //     0x898ab8: ldur            w4, [x3, #0x3b]
    // 0x898abc: DecompressPointer r4
    //     0x898abc: add             x4, x4, HEAP, lsl #32
    // 0x898ac0: stur            x4, [fp, #-8]
    // 0x898ac4: cmp             w4, NULL
    // 0x898ac8: b.eq            #0x898b1c
    // 0x898acc: LoadField: r2 = r3->field_43
    //     0x898acc: ldur            w2, [x3, #0x43]
    // 0x898ad0: DecompressPointer r2
    //     0x898ad0: add             x2, x2, HEAP, lsl #32
    // 0x898ad4: mov             x0, x4
    // 0x898ad8: r1 = Null
    //     0x898ad8: mov             x1, NULL
    // 0x898adc: r8 = RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    //     0x898adc: ldr             x8, [PP, #0x4550]  ; [pp+0x4550] Type: RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    // 0x898ae0: LoadField: r9 = r8->field_7
    //     0x898ae0: ldur            x9, [x8, #7]
    // 0x898ae4: r3 = Null
    //     0x898ae4: add             x3, PP, #0x50, lsl #12  ; [pp+0x50160] Null
    //     0x898ae8: ldr             x3, [x3, #0x160]
    // 0x898aec: blr             x9
    // 0x898af0: ldur            x1, [fp, #-8]
    // 0x898af4: r0 = markNeedsLayout()
    //     0x898af4: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0x898af8: ldur            x2, [fp, #-0x10]
    // 0x898afc: r1 = true
    //     0x898afc: add             x1, NULL, #0x20  ; true
    // 0x898b00: StoreField: r2->field_57 = r1
    //     0x898b00: stur            w1, [x2, #0x57]
    // 0x898b04: r0 = Null
    //     0x898b04: mov             x0, NULL
    // 0x898b08: LeaveFrame
    //     0x898b08: mov             SP, fp
    //     0x898b0c: ldp             fp, lr, [SP], #0x10
    // 0x898b10: ret
    //     0x898b10: ret             
    // 0x898b14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x898b14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x898b18: b               #0x898ab8
    // 0x898b1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x898b1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _scheduleRebuild(dynamic) {
    // ** addr: 0x898e14, size: 0x38
    // 0x898e14: EnterFrame
    //     0x898e14: stp             fp, lr, [SP, #-0x10]!
    //     0x898e18: mov             fp, SP
    // 0x898e1c: ldr             x0, [fp, #0x10]
    // 0x898e20: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x898e20: ldur            w1, [x0, #0x17]
    // 0x898e24: DecompressPointer r1
    //     0x898e24: add             x1, x1, HEAP, lsl #32
    // 0x898e28: CheckStackOverflow
    //     0x898e28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x898e2c: cmp             SP, x16
    //     0x898e30: b.ls            #0x898e44
    // 0x898e34: r0 = _scheduleRebuild()
    //     0x898e34: bl              #0x898e4c  ; [package:flutter/src/widgets/layout_builder.dart] _LayoutBuilderElement::_scheduleRebuild
    // 0x898e38: LeaveFrame
    //     0x898e38: mov             SP, fp
    //     0x898e3c: ldp             fp, lr, [SP], #0x10
    // 0x898e40: ret
    //     0x898e40: ret             
    // 0x898e44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x898e44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x898e48: b               #0x898e34
  }
  _ _scheduleRebuild(/* No info */) {
    // ** addr: 0x898e4c, size: 0xe8
    // 0x898e4c: EnterFrame
    //     0x898e4c: stp             fp, lr, [SP, #-0x10]!
    //     0x898e50: mov             fp, SP
    // 0x898e54: AllocStack(0x8)
    //     0x898e54: sub             SP, SP, #8
    // 0x898e58: SetupParameters(_LayoutBuilderElement<X0 bound Constraints> this /* r1 => r2 */)
    //     0x898e58: mov             x2, x1
    // 0x898e5c: CheckStackOverflow
    //     0x898e5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x898e60: cmp             SP, x16
    //     0x898e64: b.ls            #0x898f28
    // 0x898e68: LoadField: r0 = r2->field_4f
    //     0x898e68: ldur            w0, [x2, #0x4f]
    // 0x898e6c: DecompressPointer r0
    //     0x898e6c: add             x0, x0, HEAP, lsl #32
    // 0x898e70: tbnz            w0, #4, #0x898e84
    // 0x898e74: r0 = Null
    //     0x898e74: mov             x0, NULL
    // 0x898e78: LeaveFrame
    //     0x898e78: mov             SP, fp
    //     0x898e7c: ldp             fp, lr, [SP], #0x10
    // 0x898e80: ret
    //     0x898e80: ret             
    // 0x898e84: r0 = LoadStaticField(0x958)
    //     0x898e84: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x898e88: ldr             x0, [x0, #0x12b0]
    // 0x898e8c: stur            x0, [fp, #-8]
    // 0x898e90: cmp             w0, NULL
    // 0x898e94: b.eq            #0x898f30
    // 0x898e98: LoadField: r1 = r0->field_5f
    //     0x898e98: ldur            w1, [x0, #0x5f]
    // 0x898e9c: DecompressPointer r1
    //     0x898e9c: add             x1, x1, HEAP, lsl #32
    // 0x898ea0: r16 = Instance_SchedulerPhase
    //     0x898ea0: ldr             x16, [PP, #0x1d88]  ; [pp+0x1d88] Obj!SchedulerPhase@e351a1
    // 0x898ea4: cmp             w1, w16
    // 0x898ea8: b.eq            #0x898eb8
    // 0x898eac: r16 = Instance_SchedulerPhase
    //     0x898eac: ldr             x16, [PP, #0x1fe0]  ; [pp+0x1fe0] Obj!SchedulerPhase@e35121
    // 0x898eb0: cmp             w1, w16
    // 0x898eb4: b.ne            #0x898ee4
    // 0x898eb8: r1 = true
    //     0x898eb8: add             x1, NULL, #0x20  ; true
    // 0x898ebc: StoreField: r2->field_4f = r1
    //     0x898ebc: stur            w1, [x2, #0x4f]
    // 0x898ec0: r1 = Function '_frameCallback@295188862':.
    //     0x898ec0: ldr             x1, [PP, #0x4548]  ; [pp+0x4548] AnonymousClosure: (0x898f34), in [package:flutter/src/widgets/layout_builder.dart] _LayoutBuilderElement::_frameCallback (0x898f70)
    // 0x898ec4: r0 = AllocateClosure()
    //     0x898ec4: bl              #0xec1630  ; AllocateClosureStub
    // 0x898ec8: ldur            x1, [fp, #-8]
    // 0x898ecc: mov             x2, x0
    // 0x898ed0: r0 = scheduleFrameCallback()
    //     0x898ed0: bl              #0x6559c8  ; [package:flutter/src/widgets/binding.dart] _WidgetsFlutterBinding&BindingBase&GestureBinding&SchedulerBinding::scheduleFrameCallback
    // 0x898ed4: r0 = Null
    //     0x898ed4: mov             x0, NULL
    // 0x898ed8: LeaveFrame
    //     0x898ed8: mov             SP, fp
    //     0x898edc: ldp             fp, lr, [SP], #0x10
    // 0x898ee0: ret
    //     0x898ee0: ret             
    // 0x898ee4: r16 = Instance_SchedulerPhase
    //     0x898ee4: ldr             x16, [PP, #0x2000]  ; [pp+0x2000] Obj!SchedulerPhase@e35181
    // 0x898ee8: cmp             w1, w16
    // 0x898eec: b.eq            #0x898f08
    // 0x898ef0: r16 = Instance_SchedulerPhase
    //     0x898ef0: ldr             x16, [PP, #0x2010]  ; [pp+0x2010] Obj!SchedulerPhase@e35161
    // 0x898ef4: cmp             w1, w16
    // 0x898ef8: b.eq            #0x898f08
    // 0x898efc: r16 = Instance_SchedulerPhase
    //     0x898efc: ldr             x16, [PP, #0x1fc8]  ; [pp+0x1fc8] Obj!SchedulerPhase@e35141
    // 0x898f00: cmp             w1, w16
    // 0x898f04: b.eq            #0x898f08
    // 0x898f08: mov             x1, x2
    // 0x898f0c: r0 = renderObject()
    //     0x898f0c: bl              #0xd15444  ; [package:flutter/src/widgets/layout_builder.dart] _LayoutBuilderElement::renderObject
    // 0x898f10: mov             x1, x0
    // 0x898f14: r0 = markNeedsLayout()
    //     0x898f14: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0x898f18: r0 = Null
    //     0x898f18: mov             x0, NULL
    // 0x898f1c: LeaveFrame
    //     0x898f1c: mov             SP, fp
    //     0x898f20: ldp             fp, lr, [SP], #0x10
    // 0x898f24: ret
    //     0x898f24: ret             
    // 0x898f28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x898f28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x898f2c: b               #0x898e68
    // 0x898f30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x898f30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _frameCallback(dynamic, Duration) {
    // ** addr: 0x898f34, size: 0x3c
    // 0x898f34: EnterFrame
    //     0x898f34: stp             fp, lr, [SP, #-0x10]!
    //     0x898f38: mov             fp, SP
    // 0x898f3c: ldr             x0, [fp, #0x18]
    // 0x898f40: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x898f40: ldur            w1, [x0, #0x17]
    // 0x898f44: DecompressPointer r1
    //     0x898f44: add             x1, x1, HEAP, lsl #32
    // 0x898f48: CheckStackOverflow
    //     0x898f48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x898f4c: cmp             SP, x16
    //     0x898f50: b.ls            #0x898f68
    // 0x898f54: ldr             x2, [fp, #0x10]
    // 0x898f58: r0 = _frameCallback()
    //     0x898f58: bl              #0x898f70  ; [package:flutter/src/widgets/layout_builder.dart] _LayoutBuilderElement::_frameCallback
    // 0x898f5c: LeaveFrame
    //     0x898f5c: mov             SP, fp
    //     0x898f60: ldp             fp, lr, [SP], #0x10
    // 0x898f64: ret
    //     0x898f64: ret             
    // 0x898f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x898f68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x898f6c: b               #0x898f54
  }
  _ _frameCallback(/* No info */) {
    // ** addr: 0x898f70, size: 0x88
    // 0x898f70: EnterFrame
    //     0x898f70: stp             fp, lr, [SP, #-0x10]!
    //     0x898f74: mov             fp, SP
    // 0x898f78: AllocStack(0x8)
    //     0x898f78: sub             SP, SP, #8
    // 0x898f7c: r0 = false
    //     0x898f7c: add             x0, NULL, #0x30  ; false
    // 0x898f80: CheckStackOverflow
    //     0x898f80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x898f84: cmp             SP, x16
    //     0x898f88: b.ls            #0x898fec
    // 0x898f8c: StoreField: r1->field_4f = r0
    //     0x898f8c: stur            w0, [x1, #0x4f]
    // 0x898f90: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x898f90: ldur            w0, [x1, #0x17]
    // 0x898f94: DecompressPointer r0
    //     0x898f94: add             x0, x0, HEAP, lsl #32
    // 0x898f98: cmp             w0, NULL
    // 0x898f9c: b.eq            #0x898fdc
    // 0x898fa0: LoadField: r3 = r1->field_3b
    //     0x898fa0: ldur            w3, [x1, #0x3b]
    // 0x898fa4: DecompressPointer r3
    //     0x898fa4: add             x3, x3, HEAP, lsl #32
    // 0x898fa8: stur            x3, [fp, #-8]
    // 0x898fac: cmp             w3, NULL
    // 0x898fb0: b.eq            #0x898ff4
    // 0x898fb4: LoadField: r2 = r1->field_43
    //     0x898fb4: ldur            w2, [x1, #0x43]
    // 0x898fb8: DecompressPointer r2
    //     0x898fb8: add             x2, x2, HEAP, lsl #32
    // 0x898fbc: mov             x0, x3
    // 0x898fc0: r1 = Null
    //     0x898fc0: mov             x1, NULL
    // 0x898fc4: r8 = RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    //     0x898fc4: ldr             x8, [PP, #0x4550]  ; [pp+0x4550] Type: RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    // 0x898fc8: LoadField: r9 = r8->field_7
    //     0x898fc8: ldur            x9, [x8, #7]
    // 0x898fcc: r3 = Null
    //     0x898fcc: ldr             x3, [PP, #0x4558]  ; [pp+0x4558] Null
    // 0x898fd0: blr             x9
    // 0x898fd4: ldur            x1, [fp, #-8]
    // 0x898fd8: r0 = markNeedsLayout()
    //     0x898fd8: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0x898fdc: r0 = Null
    //     0x898fdc: mov             x0, NULL
    // 0x898fe0: LeaveFrame
    //     0x898fe0: mov             SP, fp
    //     0x898fe4: ldp             fp, lr, [SP], #0x10
    // 0x898fe8: ret
    //     0x898fe8: ret             
    // 0x898fec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x898fec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x898ff0: b               #0x898f8c
    // 0x898ff4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x898ff4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ performRebuild(/* No info */) {
    // ** addr: 0x89c2e0, size: 0x148
    // 0x89c2e0: EnterFrame
    //     0x89c2e0: stp             fp, lr, [SP, #-0x10]!
    //     0x89c2e4: mov             fp, SP
    // 0x89c2e8: AllocStack(0x20)
    //     0x89c2e8: sub             SP, SP, #0x20
    // 0x89c2ec: SetupParameters(_LayoutBuilderElement<X0 bound Constraints> this /* r1 => r3, fp-0x18 */)
    //     0x89c2ec: mov             x3, x1
    //     0x89c2f0: stur            x1, [fp, #-0x18]
    // 0x89c2f4: CheckStackOverflow
    //     0x89c2f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89c2f8: cmp             SP, x16
    //     0x89c2fc: b.ls            #0x89c414
    // 0x89c300: LoadField: r4 = r3->field_3b
    //     0x89c300: ldur            w4, [x3, #0x3b]
    // 0x89c304: DecompressPointer r4
    //     0x89c304: add             x4, x4, HEAP, lsl #32
    // 0x89c308: stur            x4, [fp, #-0x10]
    // 0x89c30c: cmp             w4, NULL
    // 0x89c310: b.eq            #0x89c41c
    // 0x89c314: LoadField: r5 = r3->field_43
    //     0x89c314: ldur            w5, [x3, #0x43]
    // 0x89c318: DecompressPointer r5
    //     0x89c318: add             x5, x5, HEAP, lsl #32
    // 0x89c31c: mov             x0, x4
    // 0x89c320: mov             x2, x5
    // 0x89c324: stur            x5, [fp, #-8]
    // 0x89c328: r1 = Null
    //     0x89c328: mov             x1, NULL
    // 0x89c32c: r8 = RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    //     0x89c32c: ldr             x8, [PP, #0x4550]  ; [pp+0x4550] Type: RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    // 0x89c330: LoadField: r9 = r8->field_7
    //     0x89c330: ldur            x9, [x8, #7]
    // 0x89c334: r3 = Null
    //     0x89c334: add             x3, PP, #0x50, lsl #12  ; [pp+0x50130] Null
    //     0x89c338: ldr             x3, [x3, #0x130]
    // 0x89c33c: blr             x9
    // 0x89c340: ldur            x1, [fp, #-0x10]
    // 0x89c344: r0 = markNeedsLayout()
    //     0x89c344: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0x89c348: ldur            x3, [fp, #-0x18]
    // 0x89c34c: r0 = true
    //     0x89c34c: add             x0, NULL, #0x20  ; true
    // 0x89c350: StoreField: r3->field_57 = r0
    //     0x89c350: stur            w0, [x3, #0x57]
    // 0x89c354: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x89c354: ldur            w4, [x3, #0x17]
    // 0x89c358: DecompressPointer r4
    //     0x89c358: add             x4, x4, HEAP, lsl #32
    // 0x89c35c: stur            x4, [fp, #-0x10]
    // 0x89c360: cmp             w4, NULL
    // 0x89c364: b.eq            #0x89c420
    // 0x89c368: mov             x0, x4
    // 0x89c36c: r2 = Null
    //     0x89c36c: mov             x2, NULL
    // 0x89c370: r1 = Null
    //     0x89c370: mov             x1, NULL
    // 0x89c374: r4 = LoadClassIdInstr(r0)
    //     0x89c374: ldur            x4, [x0, #-1]
    //     0x89c378: ubfx            x4, x4, #0xc, #0x14
    // 0x89c37c: r17 = -4457
    //     0x89c37c: movn            x17, #0x1168
    // 0x89c380: add             x4, x4, x17
    // 0x89c384: cmp             x4, #0x8c
    // 0x89c388: b.ls            #0x89c3a0
    // 0x89c38c: r8 = RenderObjectWidget
    //     0x89c38c: add             x8, PP, #0x22, lsl #12  ; [pp+0x224c8] Type: RenderObjectWidget
    //     0x89c390: ldr             x8, [x8, #0x4c8]
    // 0x89c394: r3 = Null
    //     0x89c394: add             x3, PP, #0x50, lsl #12  ; [pp+0x50140] Null
    //     0x89c398: ldr             x3, [x3, #0x140]
    // 0x89c39c: r0 = DefaultTypeTest()
    //     0x89c39c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x89c3a0: ldur            x3, [fp, #-0x18]
    // 0x89c3a4: LoadField: r4 = r3->field_3b
    //     0x89c3a4: ldur            w4, [x3, #0x3b]
    // 0x89c3a8: DecompressPointer r4
    //     0x89c3a8: add             x4, x4, HEAP, lsl #32
    // 0x89c3ac: stur            x4, [fp, #-0x20]
    // 0x89c3b0: cmp             w4, NULL
    // 0x89c3b4: b.eq            #0x89c424
    // 0x89c3b8: mov             x0, x4
    // 0x89c3bc: ldur            x2, [fp, #-8]
    // 0x89c3c0: r1 = Null
    //     0x89c3c0: mov             x1, NULL
    // 0x89c3c4: r8 = RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    //     0x89c3c4: ldr             x8, [PP, #0x4550]  ; [pp+0x4550] Type: RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    // 0x89c3c8: LoadField: r9 = r8->field_7
    //     0x89c3c8: ldur            x9, [x8, #7]
    // 0x89c3cc: r3 = Null
    //     0x89c3cc: add             x3, PP, #0x50, lsl #12  ; [pp+0x50150] Null
    //     0x89c3d0: ldr             x3, [x3, #0x150]
    // 0x89c3d4: blr             x9
    // 0x89c3d8: ldur            x1, [fp, #-0x10]
    // 0x89c3dc: r0 = LoadClassIdInstr(r1)
    //     0x89c3dc: ldur            x0, [x1, #-1]
    //     0x89c3e0: ubfx            x0, x0, #0xc, #0x14
    // 0x89c3e4: ldur            x2, [fp, #-0x18]
    // 0x89c3e8: ldur            x3, [fp, #-0x20]
    // 0x89c3ec: r0 = GDT[cid_x0 + 0x16fa]()
    //     0x89c3ec: movz            x17, #0x16fa
    //     0x89c3f0: add             lr, x0, x17
    //     0x89c3f4: ldr             lr, [x21, lr, lsl #3]
    //     0x89c3f8: blr             lr
    // 0x89c3fc: ldur            x1, [fp, #-0x18]
    // 0x89c400: r0 = performRebuild()
    //     0x89c400: bl              #0x89e3f4  ; [package:flutter/src/widgets/framework.dart] Element::performRebuild
    // 0x89c404: r0 = Null
    //     0x89c404: mov             x0, NULL
    // 0x89c408: LeaveFrame
    //     0x89c408: mov             SP, fp
    //     0x89c40c: ldp             fp, lr, [SP], #0x10
    // 0x89c410: ret
    //     0x89c410: ret             
    // 0x89c414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89c414: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89c418: b               #0x89c300
    // 0x89c41c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x89c41c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x89c420: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x89c420: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x89c424: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x89c424: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _LayoutBuilderElement(/* No info */) {
    // ** addr: 0xbbe1a4, size: 0x64
    // 0xbbe1a4: r6 = Sentinel
    //     0xbbe1a4: ldr             x6, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbe1a8: r5 = false
    //     0xbbe1a8: add             x5, NULL, #0x30  ; false
    // 0xbbe1ac: r4 = true
    //     0xbbe1ac: add             x4, NULL, #0x20  ; true
    // 0xbbe1b0: r3 = Instance__ElementLifecycle
    //     0xbbe1b0: add             x3, PP, #0xd, lsl #12  ; [pp+0xda00] Obj!_ElementLifecycle@e343c1
    //     0xbbe1b4: ldr             x3, [x3, #0xa00]
    // 0xbbe1b8: mov             x0, x2
    // 0xbbe1bc: StoreField: r1->field_4b = r6
    //     0xbbe1bc: stur            w6, [x1, #0x4b]
    // 0xbbe1c0: StoreField: r1->field_4f = r5
    //     0xbbe1c0: stur            w5, [x1, #0x4f]
    // 0xbbe1c4: StoreField: r1->field_57 = r4
    //     0xbbe1c4: stur            w4, [x1, #0x57]
    // 0xbbe1c8: StoreField: r1->field_13 = r6
    //     0xbbe1c8: stur            w6, [x1, #0x13]
    // 0xbbe1cc: StoreField: r1->field_23 = r3
    //     0xbbe1cc: stur            w3, [x1, #0x23]
    // 0xbbe1d0: StoreField: r1->field_2f = r5
    //     0xbbe1d0: stur            w5, [x1, #0x2f]
    // 0xbbe1d4: StoreField: r1->field_33 = r4
    //     0xbbe1d4: stur            w4, [x1, #0x33]
    // 0xbbe1d8: StoreField: r1->field_37 = r5
    //     0xbbe1d8: stur            w5, [x1, #0x37]
    // 0xbbe1dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbe1dc: stur            w0, [x1, #0x17]
    //     0xbbe1e0: ldurb           w16, [x1, #-1]
    //     0xbbe1e4: ldurb           w17, [x0, #-1]
    //     0xbbe1e8: and             x16, x17, x16, lsr #2
    //     0xbbe1ec: tst             x16, HEAP, lsr #32
    //     0xbbe1f0: b.eq            #0xbbe200
    //     0xbbe1f4: str             lr, [SP, #-8]!
    //     0xbbe1f8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    //     0xbbe1fc: ldr             lr, [SP], #8
    // 0xbbe200: r0 = Null
    //     0xbbe200: mov             x0, NULL
    // 0xbbe204: ret
    //     0xbbe204: ret             
  }
  _ unmount(/* No info */) {
    // ** addr: 0xccce04, size: 0x88
    // 0xccce04: EnterFrame
    //     0xccce04: stp             fp, lr, [SP, #-0x10]!
    //     0xccce08: mov             fp, SP
    // 0xccce0c: AllocStack(0x10)
    //     0xccce0c: sub             SP, SP, #0x10
    // 0xccce10: SetupParameters(_LayoutBuilderElement<X0 bound Constraints> this /* r1 => r3, fp-0x10 */)
    //     0xccce10: mov             x3, x1
    //     0xccce14: stur            x1, [fp, #-0x10]
    // 0xccce18: CheckStackOverflow
    //     0xccce18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xccce1c: cmp             SP, x16
    //     0xccce20: b.ls            #0xccce80
    // 0xccce24: LoadField: r4 = r3->field_3b
    //     0xccce24: ldur            w4, [x3, #0x3b]
    // 0xccce28: DecompressPointer r4
    //     0xccce28: add             x4, x4, HEAP, lsl #32
    // 0xccce2c: stur            x4, [fp, #-8]
    // 0xccce30: cmp             w4, NULL
    // 0xccce34: b.eq            #0xccce88
    // 0xccce38: LoadField: r2 = r3->field_43
    //     0xccce38: ldur            w2, [x3, #0x43]
    // 0xccce3c: DecompressPointer r2
    //     0xccce3c: add             x2, x2, HEAP, lsl #32
    // 0xccce40: mov             x0, x4
    // 0xccce44: r1 = Null
    //     0xccce44: mov             x1, NULL
    // 0xccce48: r8 = RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    //     0xccce48: ldr             x8, [PP, #0x4550]  ; [pp+0x4550] Type: RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    // 0xccce4c: LoadField: r9 = r8->field_7
    //     0xccce4c: ldur            x9, [x8, #7]
    // 0xccce50: r3 = Null
    //     0xccce50: add             x3, PP, #0x50, lsl #12  ; [pp+0x50120] Null
    //     0xccce54: ldr             x3, [x3, #0x120]
    // 0xccce58: blr             x9
    // 0xccce5c: ldur            x1, [fp, #-8]
    // 0xccce60: r2 = Null
    //     0xccce60: mov             x2, NULL
    // 0xccce64: r0 = updateCallback()
    //     0xccce64: bl              #0x870474  ; [package:flutter/src/widgets/layout_builder.dart] __RenderLayoutBuilder&RenderBox&RenderObjectWithChildMixin&RenderConstrainedLayoutBuilder::updateCallback
    // 0xccce68: ldur            x1, [fp, #-0x10]
    // 0xccce6c: r0 = unmount()
    //     0xccce6c: bl              #0xcccf34  ; [package:flutter/src/widgets/framework.dart] RenderObjectElement::unmount
    // 0xccce70: r0 = Null
    //     0xccce70: mov             x0, NULL
    // 0xccce74: LeaveFrame
    //     0xccce74: mov             SP, fp
    //     0xccce78: ldp             fp, lr, [SP], #0x10
    // 0xccce7c: ret
    //     0xccce7c: ret             
    // 0xccce80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xccce80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xccce84: b               #0xccce24
    // 0xccce88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xccce88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ renderObject(/* No info */) {
    // ** addr: 0xd15444, size: 0x54
    // 0xd15444: EnterFrame
    //     0xd15444: stp             fp, lr, [SP, #-0x10]!
    //     0xd15448: mov             fp, SP
    // 0xd1544c: AllocStack(0x8)
    //     0xd1544c: sub             SP, SP, #8
    // 0xd15450: LoadField: r3 = r1->field_3b
    //     0xd15450: ldur            w3, [x1, #0x3b]
    // 0xd15454: DecompressPointer r3
    //     0xd15454: add             x3, x3, HEAP, lsl #32
    // 0xd15458: stur            x3, [fp, #-8]
    // 0xd1545c: cmp             w3, NULL
    // 0xd15460: b.eq            #0xd15494
    // 0xd15464: LoadField: r2 = r1->field_43
    //     0xd15464: ldur            w2, [x1, #0x43]
    // 0xd15468: DecompressPointer r2
    //     0xd15468: add             x2, x2, HEAP, lsl #32
    // 0xd1546c: mov             x0, x3
    // 0xd15470: r1 = Null
    //     0xd15470: mov             x1, NULL
    // 0xd15474: r8 = RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    //     0xd15474: ldr             x8, [PP, #0x4550]  ; [pp+0x4550] Type: RenderConstrainedLayoutBuilder<X0 bound Constraints, RenderObject>
    // 0xd15478: LoadField: r9 = r8->field_7
    //     0xd15478: ldur            x9, [x8, #7]
    // 0xd1547c: r3 = Null
    //     0xd1547c: ldr             x3, [PP, #0x4568]  ; [pp+0x4568] Null
    // 0xd15480: blr             x9
    // 0xd15484: ldur            x0, [fp, #-8]
    // 0xd15488: LeaveFrame
    //     0xd15488: mov             SP, fp
    //     0xd1548c: ldp             fp, lr, [SP], #0x10
    // 0xd15490: ret
    //     0xd15490: ret             
    // 0xd15494: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd15494: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  dynamic _scheduleRebuild(dynamic) {
    // ** addr: 0xd7ae78, size: 0x24
    // 0xd7ae78: EnterFrame
    //     0xd7ae78: stp             fp, lr, [SP, #-0x10]!
    //     0xd7ae7c: mov             fp, SP
    // 0xd7ae80: ldr             x2, [fp, #0x10]
    // 0xd7ae84: r1 = Function '_scheduleRebuild@295188862':.
    //     0xd7ae84: add             x1, PP, #0x56, lsl #12  ; [pp+0x56d50] AnonymousClosure: (0x898e14), in [package:flutter/src/widgets/layout_builder.dart] _LayoutBuilderElement::_scheduleRebuild (0x898e4c)
    //     0xd7ae88: ldr             x1, [x1, #0xd50]
    // 0xd7ae8c: r0 = AllocateClosure()
    //     0xd7ae8c: bl              #0xec1630  ; AllocateClosureStub
    // 0xd7ae90: LeaveFrame
    //     0xd7ae90: mov             SP, fp
    //     0xd7ae94: ldp             fp, lr, [SP], #0x10
    // 0xd7ae98: ret
    //     0xd7ae98: ret             
  }
}

// class id: 4473, size: 0x14, field offset: 0xc
//   const constructor, 
abstract class ConstrainedLayoutBuilder<X0 bound Constraints> extends RenderObjectWidget {

  _ createElement(/* No info */) {
    // ** addr: 0xbbe150, size: 0x54
    // 0xbbe150: EnterFrame
    //     0xbbe150: stp             fp, lr, [SP, #-0x10]!
    //     0xbbe154: mov             fp, SP
    // 0xbbe158: AllocStack(0x8)
    //     0xbbe158: sub             SP, SP, #8
    // 0xbbe15c: SetupParameters(ConstrainedLayoutBuilder<X0 bound Constraints> this /* r1 => r2, fp-0x8 */)
    //     0xbbe15c: mov             x2, x1
    //     0xbbe160: stur            x1, [fp, #-8]
    // 0xbbe164: CheckStackOverflow
    //     0xbbe164: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbe168: cmp             SP, x16
    //     0xbbe16c: b.ls            #0xbbe19c
    // 0xbbe170: LoadField: r1 = r2->field_b
    //     0xbbe170: ldur            w1, [x2, #0xb]
    // 0xbbe174: DecompressPointer r1
    //     0xbbe174: add             x1, x1, HEAP, lsl #32
    // 0xbbe178: r0 = _LayoutBuilderElement()
    //     0xbbe178: bl              #0xbbe208  ; Allocate_LayoutBuilderElementStub -> _LayoutBuilderElement<X0 bound Constraints> (size=0x5c)
    // 0xbbe17c: mov             x1, x0
    // 0xbbe180: ldur            x2, [fp, #-8]
    // 0xbbe184: stur            x0, [fp, #-8]
    // 0xbbe188: r0 = _LayoutBuilderElement()
    //     0xbbe188: bl              #0xbbe1a4  ; [package:flutter/src/widgets/layout_builder.dart] _LayoutBuilderElement::_LayoutBuilderElement
    // 0xbbe18c: ldur            x0, [fp, #-8]
    // 0xbbe190: LeaveFrame
    //     0xbbe190: mov             SP, fp
    //     0xbbe194: ldp             fp, lr, [SP], #0x10
    // 0xbbe198: ret
    //     0xbbe198: ret             
    // 0xbbe19c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbe19c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbe1a0: b               #0xbbe170
  }
}

// class id: 4474, size: 0x14, field offset: 0x14
//   const constructor, 
class LayoutBuilder extends ConstrainedLayoutBuilder<dynamic> {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x860e4c, size: 0x50
    // 0x860e4c: EnterFrame
    //     0x860e4c: stp             fp, lr, [SP, #-0x10]!
    //     0x860e50: mov             fp, SP
    // 0x860e54: AllocStack(0x8)
    //     0x860e54: sub             SP, SP, #8
    // 0x860e58: CheckStackOverflow
    //     0x860e58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x860e5c: cmp             SP, x16
    //     0x860e60: b.ls            #0x860e94
    // 0x860e64: r0 = _RenderLayoutBuilder()
    //     0x860e64: bl              #0x860f78  ; Allocate_RenderLayoutBuilderStub -> _RenderLayoutBuilder (size=0x60)
    // 0x860e68: stur            x0, [fp, #-8]
    // 0x860e6c: r0 = _LayoutCacheStorage()
    //     0x860e6c: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x860e70: mov             x1, x0
    // 0x860e74: ldur            x0, [fp, #-8]
    // 0x860e78: StoreField: r0->field_4f = r1
    //     0x860e78: stur            w1, [x0, #0x4f]
    // 0x860e7c: mov             x1, x0
    // 0x860e80: r0 = RenderObject()
    //     0x860e80: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x860e84: ldur            x0, [fp, #-8]
    // 0x860e88: LeaveFrame
    //     0x860e88: mov             SP, fp
    //     0x860e8c: ldp             fp, lr, [SP], #0x10
    // 0x860e90: ret
    //     0x860e90: ret             
    // 0x860e94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x860e94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x860e98: b               #0x860e64
  }
}
