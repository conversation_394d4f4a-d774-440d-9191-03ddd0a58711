// lib: , url: package:flutter/src/widgets/modal_barrier.dart

// class id: 1049149, size: 0x8
class :: {
}

// class id: 2685, size: 0x10, field offset: 0xc
//   const constructor, 
class _AnyTapGestureRecognizerFactory extends GestureRecognizerFactory<dynamic> {

  _ initializer(/* No info */) {
    // ** addr: 0xdb8c0c, size: 0x90
    // 0xdb8c0c: EnterFrame
    //     0xdb8c0c: stp             fp, lr, [SP, #-0x10]!
    //     0xdb8c10: mov             fp, SP
    // 0xdb8c14: AllocStack(0x10)
    //     0xdb8c14: sub             SP, SP, #0x10
    // 0xdb8c18: SetupParameters(_AnyTapGestureRecognizerFactory this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xdb8c18: mov             x0, x2
    //     0xdb8c1c: mov             x4, x1
    //     0xdb8c20: mov             x3, x2
    //     0xdb8c24: stur            x1, [fp, #-8]
    //     0xdb8c28: stur            x2, [fp, #-0x10]
    // 0xdb8c2c: r2 = Null
    //     0xdb8c2c: mov             x2, NULL
    // 0xdb8c30: r1 = Null
    //     0xdb8c30: mov             x1, NULL
    // 0xdb8c34: r4 = 60
    //     0xdb8c34: movz            x4, #0x3c
    // 0xdb8c38: branchIfSmi(r0, 0xdb8c44)
    //     0xdb8c38: tbz             w0, #0, #0xdb8c44
    // 0xdb8c3c: r4 = LoadClassIdInstr(r0)
    //     0xdb8c3c: ldur            x4, [x0, #-1]
    //     0xdb8c40: ubfx            x4, x4, #0xc, #0x14
    // 0xdb8c44: cmp             x4, #0xdd6
    // 0xdb8c48: b.eq            #0xdb8c60
    // 0xdb8c4c: r8 = _AnyTapGestureRecognizer
    //     0xdb8c4c: add             x8, PP, #0x30, lsl #12  ; [pp+0x30f40] Type: _AnyTapGestureRecognizer
    //     0xdb8c50: ldr             x8, [x8, #0xf40]
    // 0xdb8c54: r3 = Null
    //     0xdb8c54: add             x3, PP, #0x46, lsl #12  ; [pp+0x460c0] Null
    //     0xdb8c58: ldr             x3, [x3, #0xc0]
    // 0xdb8c5c: r0 = DefaultTypeTest()
    //     0xdb8c5c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xdb8c60: ldur            x1, [fp, #-8]
    // 0xdb8c64: LoadField: r0 = r1->field_b
    //     0xdb8c64: ldur            w0, [x1, #0xb]
    // 0xdb8c68: DecompressPointer r0
    //     0xdb8c68: add             x0, x0, HEAP, lsl #32
    // 0xdb8c6c: ldur            x1, [fp, #-0x10]
    // 0xdb8c70: StoreField: r1->field_57 = r0
    //     0xdb8c70: stur            w0, [x1, #0x57]
    //     0xdb8c74: ldurb           w16, [x1, #-1]
    //     0xdb8c78: ldurb           w17, [x0, #-1]
    //     0xdb8c7c: and             x16, x17, x16, lsr #2
    //     0xdb8c80: tst             x16, HEAP, lsr #32
    //     0xdb8c84: b.eq            #0xdb8c8c
    //     0xdb8c88: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xdb8c8c: r0 = Null
    //     0xdb8c8c: mov             x0, NULL
    // 0xdb8c90: LeaveFrame
    //     0xdb8c90: mov             SP, fp
    //     0xdb8c94: ldp             fp, lr, [SP], #0x10
    // 0xdb8c98: ret
    //     0xdb8c98: ret             
  }
  _ constructor(/* No info */) {
    // ** addr: 0xdb8ce0, size: 0x64
    // 0xdb8ce0: EnterFrame
    //     0xdb8ce0: stp             fp, lr, [SP, #-0x10]!
    //     0xdb8ce4: mov             fp, SP
    // 0xdb8ce8: AllocStack(0x8)
    //     0xdb8ce8: sub             SP, SP, #8
    // 0xdb8cec: CheckStackOverflow
    //     0xdb8cec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb8cf0: cmp             SP, x16
    //     0xdb8cf4: b.ls            #0xdb8d3c
    // 0xdb8cf8: r0 = _AnyTapGestureRecognizer()
    //     0xdb8cf8: bl              #0xdb8d44  ; Allocate_AnyTapGestureRecognizerStub -> _AnyTapGestureRecognizer (size=0x5c)
    // 0xdb8cfc: mov             x4, x0
    // 0xdb8d00: r0 = false
    //     0xdb8d00: add             x0, NULL, #0x30  ; false
    // 0xdb8d04: stur            x4, [fp, #-8]
    // 0xdb8d08: StoreField: r4->field_47 = r0
    //     0xdb8d08: stur            w0, [x4, #0x47]
    // 0xdb8d0c: StoreField: r4->field_4b = r0
    //     0xdb8d0c: stur            w0, [x4, #0x4b]
    // 0xdb8d10: mov             x1, x4
    // 0xdb8d14: r2 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static.
    //     0xdb8d14: add             x2, PP, #0x25, lsl #12  ; [pp+0x253d8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static. (0x7e54fb8bbd8c)
    //     0xdb8d18: ldr             x2, [x2, #0x3d8]
    // 0xdb8d1c: r3 = Instance_Duration
    //     0xdb8d1c: ldr             x3, [PP, #0x6e28]  ; [pp+0x6e28] Obj!Duration@e3a0a1
    // 0xdb8d20: r5 = Null
    //     0xdb8d20: mov             x5, NULL
    // 0xdb8d24: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xdb8d24: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xdb8d28: r0 = PrimaryPointerGestureRecognizer()
    //     0xdb8d28: bl              #0x762f7c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::PrimaryPointerGestureRecognizer
    // 0xdb8d2c: ldur            x0, [fp, #-8]
    // 0xdb8d30: LeaveFrame
    //     0xdb8d30: mov             SP, fp
    //     0xdb8d34: ldp             fp, lr, [SP], #0x10
    // 0xdb8d38: ret
    //     0xdb8d38: ret             
    // 0xdb8d3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb8d3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb8d40: b               #0xdb8cf8
  }
}

// class id: 3542, size: 0x5c, field offset: 0x58
class _AnyTapGestureRecognizer extends BaseTapGestureRecognizer {

  _ isPointerAllowed(/* No info */) {
    // ** addr: 0x852c0c, size: 0x4c
    // 0x852c0c: EnterFrame
    //     0x852c0c: stp             fp, lr, [SP, #-0x10]!
    //     0x852c10: mov             fp, SP
    // 0x852c14: CheckStackOverflow
    //     0x852c14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x852c18: cmp             SP, x16
    //     0x852c1c: b.ls            #0x852c50
    // 0x852c20: LoadField: r0 = r1->field_57
    //     0x852c20: ldur            w0, [x1, #0x57]
    // 0x852c24: DecompressPointer r0
    //     0x852c24: add             x0, x0, HEAP, lsl #32
    // 0x852c28: cmp             w0, NULL
    // 0x852c2c: b.ne            #0x852c40
    // 0x852c30: r0 = false
    //     0x852c30: add             x0, NULL, #0x30  ; false
    // 0x852c34: LeaveFrame
    //     0x852c34: mov             SP, fp
    //     0x852c38: ldp             fp, lr, [SP], #0x10
    // 0x852c3c: ret
    //     0x852c3c: ret             
    // 0x852c40: r0 = isPointerAllowed()
    //     0x852c40: bl              #0x852e08  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::isPointerAllowed
    // 0x852c44: LeaveFrame
    //     0x852c44: mov             SP, fp
    //     0x852c48: ldp             fp, lr, [SP], #0x10
    // 0x852c4c: ret
    //     0x852c4c: ret             
    // 0x852c50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x852c50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x852c54: b               #0x852c20
  }
  _ handleTapUp(/* No info */) {
    // ** addr: 0xd82a00, size: 0x54
    // 0xd82a00: EnterFrame
    //     0xd82a00: stp             fp, lr, [SP, #-0x10]!
    //     0xd82a04: mov             fp, SP
    // 0xd82a08: AllocStack(0x18)
    //     0xd82a08: sub             SP, SP, #0x18
    // 0xd82a0c: CheckStackOverflow
    //     0xd82a0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd82a10: cmp             SP, x16
    //     0xd82a14: b.ls            #0xd82a4c
    // 0xd82a18: LoadField: r0 = r1->field_57
    //     0xd82a18: ldur            w0, [x1, #0x57]
    // 0xd82a1c: DecompressPointer r0
    //     0xd82a1c: add             x0, x0, HEAP, lsl #32
    // 0xd82a20: cmp             w0, NULL
    // 0xd82a24: b.eq            #0xd82a3c
    // 0xd82a28: r16 = <void?>
    //     0xd82a28: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xd82a2c: stp             x1, x16, [SP, #8]
    // 0xd82a30: str             x0, [SP]
    // 0xd82a34: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xd82a34: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xd82a38: r0 = invokeCallback()
    //     0xd82a38: bl              #0x759bd0  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::invokeCallback
    // 0xd82a3c: r0 = Null
    //     0xd82a3c: mov             x0, NULL
    // 0xd82a40: LeaveFrame
    //     0xd82a40: mov             SP, fp
    //     0xd82a44: ldp             fp, lr, [SP], #0x10
    // 0xd82a48: ret
    //     0xd82a48: ret             
    // 0xd82a4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd82a4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd82a50: b               #0xd82a18
  }
}

// class id: 4855, size: 0x28, field offset: 0x10
//   const constructor, 
class AnimatedModalBarrier extends AnimatedWidget {

  _ build(/* No info */) {
    // ** addr: 0xc21380, size: 0xc8
    // 0xc21380: EnterFrame
    //     0xc21380: stp             fp, lr, [SP, #-0x10]!
    //     0xc21384: mov             fp, SP
    // 0xc21388: AllocStack(0x20)
    //     0xc21388: sub             SP, SP, #0x20
    // 0xc2138c: SetupParameters(AnimatedModalBarrier this /* r1 => r3, fp-0x10 */)
    //     0xc2138c: mov             x3, x1
    //     0xc21390: stur            x1, [fp, #-0x10]
    // 0xc21394: CheckStackOverflow
    //     0xc21394: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc21398: cmp             SP, x16
    //     0xc2139c: b.ls            #0xc21440
    // 0xc213a0: LoadField: r4 = r3->field_b
    //     0xc213a0: ldur            w4, [x3, #0xb]
    // 0xc213a4: DecompressPointer r4
    //     0xc213a4: add             x4, x4, HEAP, lsl #32
    // 0xc213a8: mov             x0, x4
    // 0xc213ac: stur            x4, [fp, #-8]
    // 0xc213b0: r2 = Null
    //     0xc213b0: mov             x2, NULL
    // 0xc213b4: r1 = Null
    //     0xc213b4: mov             x1, NULL
    // 0xc213b8: r8 = Animation<Color?>
    //     0xc213b8: add             x8, PP, #0x30, lsl #12  ; [pp+0x30f28] Type: Animation<Color?>
    //     0xc213bc: ldr             x8, [x8, #0xf28]
    // 0xc213c0: r3 = Null
    //     0xc213c0: add             x3, PP, #0x30, lsl #12  ; [pp+0x30f30] Null
    //     0xc213c4: ldr             x3, [x3, #0xf30]
    // 0xc213c8: r0 = Animation<Color?>()
    //     0xc213c8: bl              #0x7e5b98  ; IsType_Animation<Color?>_Stub
    // 0xc213cc: ldur            x1, [fp, #-8]
    // 0xc213d0: r0 = LoadClassIdInstr(r1)
    //     0xc213d0: ldur            x0, [x1, #-1]
    //     0xc213d4: ubfx            x0, x0, #0xc, #0x14
    // 0xc213d8: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xc213d8: movz            x17, #0x276f
    //     0xc213dc: movk            x17, #0x1, lsl #16
    //     0xc213e0: add             lr, x0, x17
    //     0xc213e4: ldr             lr, [x21, lr, lsl #3]
    //     0xc213e8: blr             lr
    // 0xc213ec: mov             x1, x0
    // 0xc213f0: ldur            x0, [fp, #-0x10]
    // 0xc213f4: stur            x1, [fp, #-0x20]
    // 0xc213f8: LoadField: r2 = r0->field_f
    //     0xc213f8: ldur            w2, [x0, #0xf]
    // 0xc213fc: DecompressPointer r2
    //     0xc213fc: add             x2, x2, HEAP, lsl #32
    // 0xc21400: stur            x2, [fp, #-0x18]
    // 0xc21404: LoadField: r3 = r0->field_13
    //     0xc21404: ldur            w3, [x0, #0x13]
    // 0xc21408: DecompressPointer r3
    //     0xc21408: add             x3, x3, HEAP, lsl #32
    // 0xc2140c: stur            x3, [fp, #-8]
    // 0xc21410: r0 = ModalBarrier()
    //     0xc21410: bl              #0xc21448  ; AllocateModalBarrierStub -> ModalBarrier (size=0x28)
    // 0xc21414: ldur            x1, [fp, #-0x20]
    // 0xc21418: StoreField: r0->field_b = r1
    //     0xc21418: stur            w1, [x0, #0xb]
    // 0xc2141c: ldur            x1, [fp, #-0x18]
    // 0xc21420: StoreField: r0->field_f = r1
    //     0xc21420: stur            w1, [x0, #0xf]
    // 0xc21424: ldur            x1, [fp, #-8]
    // 0xc21428: StoreField: r0->field_1b = r1
    //     0xc21428: stur            w1, [x0, #0x1b]
    // 0xc2142c: r1 = true
    //     0xc2142c: add             x1, NULL, #0x20  ; true
    // 0xc21430: ArrayStore: r0[0] = r1  ; List_4
    //     0xc21430: stur            w1, [x0, #0x17]
    // 0xc21434: LeaveFrame
    //     0xc21434: mov             SP, fp
    //     0xc21438: ldp             fp, lr, [SP], #0x10
    // 0xc2143c: ret
    //     0xc2143c: ret             
    // 0xc21440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc21440: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc21444: b               #0xc213a0
  }
}

// class id: 5346, size: 0x14, field offset: 0xc
//   const constructor, 
class _ModalBarrierGestureDetector extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xaa44d0, size: 0xd0
    // 0xaa44d0: EnterFrame
    //     0xaa44d0: stp             fp, lr, [SP, #-0x10]!
    //     0xaa44d4: mov             fp, SP
    // 0xaa44d8: AllocStack(0x28)
    //     0xaa44d8: sub             SP, SP, #0x28
    // 0xaa44dc: SetupParameters(_ModalBarrierGestureDetector this /* r1 => r0, fp-0x8 */)
    //     0xaa44dc: mov             x0, x1
    //     0xaa44e0: stur            x1, [fp, #-8]
    // 0xaa44e4: CheckStackOverflow
    //     0xaa44e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa44e8: cmp             SP, x16
    //     0xaa44ec: b.ls            #0xaa4598
    // 0xaa44f0: r1 = Null
    //     0xaa44f0: mov             x1, NULL
    // 0xaa44f4: r2 = 4
    //     0xaa44f4: movz            x2, #0x4
    // 0xaa44f8: r0 = AllocateArray()
    //     0xaa44f8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaa44fc: stur            x0, [fp, #-0x18]
    // 0xaa4500: r16 = _AnyTapGestureRecognizer
    //     0xaa4500: add             x16, PP, #0x30, lsl #12  ; [pp+0x30f40] Type: _AnyTapGestureRecognizer
    //     0xaa4504: ldr             x16, [x16, #0xf40]
    // 0xaa4508: StoreField: r0->field_f = r16
    //     0xaa4508: stur            w16, [x0, #0xf]
    // 0xaa450c: ldur            x2, [fp, #-8]
    // 0xaa4510: LoadField: r3 = r2->field_f
    //     0xaa4510: ldur            w3, [x2, #0xf]
    // 0xaa4514: DecompressPointer r3
    //     0xaa4514: add             x3, x3, HEAP, lsl #32
    // 0xaa4518: stur            x3, [fp, #-0x10]
    // 0xaa451c: r1 = <_AnyTapGestureRecognizer>
    //     0xaa451c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30f48] TypeArguments: <_AnyTapGestureRecognizer>
    //     0xaa4520: ldr             x1, [x1, #0xf48]
    // 0xaa4524: r0 = _AnyTapGestureRecognizerFactory()
    //     0xaa4524: bl              #0xaa45a0  ; Allocate_AnyTapGestureRecognizerFactoryStub -> _AnyTapGestureRecognizerFactory (size=0x10)
    // 0xaa4528: mov             x1, x0
    // 0xaa452c: ldur            x0, [fp, #-0x10]
    // 0xaa4530: StoreField: r1->field_b = r0
    //     0xaa4530: stur            w0, [x1, #0xb]
    // 0xaa4534: ldur            x0, [fp, #-0x18]
    // 0xaa4538: StoreField: r0->field_13 = r1
    //     0xaa4538: stur            w1, [x0, #0x13]
    // 0xaa453c: r16 = <Type, GestureRecognizerFactory<GestureRecognizer>>
    //     0xaa453c: add             x16, PP, #0x25, lsl #12  ; [pp+0x25368] TypeArguments: <Type, GestureRecognizerFactory<GestureRecognizer>>
    //     0xaa4540: ldr             x16, [x16, #0x368]
    // 0xaa4544: stp             x0, x16, [SP]
    // 0xaa4548: r0 = Map._fromLiteral()
    //     0xaa4548: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xaa454c: mov             x1, x0
    // 0xaa4550: ldur            x0, [fp, #-8]
    // 0xaa4554: stur            x1, [fp, #-0x18]
    // 0xaa4558: LoadField: r2 = r0->field_b
    //     0xaa4558: ldur            w2, [x0, #0xb]
    // 0xaa455c: DecompressPointer r2
    //     0xaa455c: add             x2, x2, HEAP, lsl #32
    // 0xaa4560: stur            x2, [fp, #-0x10]
    // 0xaa4564: r0 = RawGestureDetector()
    //     0xaa4564: bl              #0x9d2c8c  ; AllocateRawGestureDetectorStub -> RawGestureDetector (size=0x20)
    // 0xaa4568: ldur            x1, [fp, #-0x10]
    // 0xaa456c: StoreField: r0->field_b = r1
    //     0xaa456c: stur            w1, [x0, #0xb]
    // 0xaa4570: ldur            x1, [fp, #-0x18]
    // 0xaa4574: StoreField: r0->field_f = r1
    //     0xaa4574: stur            w1, [x0, #0xf]
    // 0xaa4578: r1 = Instance_HitTestBehavior
    //     0xaa4578: add             x1, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xaa457c: ldr             x1, [x1, #0x1c8]
    // 0xaa4580: StoreField: r0->field_13 = r1
    //     0xaa4580: stur            w1, [x0, #0x13]
    // 0xaa4584: r1 = false
    //     0xaa4584: add             x1, NULL, #0x30  ; false
    // 0xaa4588: ArrayStore: r0[0] = r1  ; List_4
    //     0xaa4588: stur            w1, [x0, #0x17]
    // 0xaa458c: LeaveFrame
    //     0xaa458c: mov             SP, fp
    //     0xaa4590: ldp             fp, lr, [SP], #0x10
    // 0xaa4594: ret
    //     0xaa4594: ret             
    // 0xaa4598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa4598: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa459c: b               #0xaa44f0
  }
}

// class id: 5347, size: 0x28, field offset: 0xc
//   const constructor, 
class ModalBarrier extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xaa41f8, size: 0x248
    // 0xaa41f8: EnterFrame
    //     0xaa41f8: stp             fp, lr, [SP, #-0x10]!
    //     0xaa41fc: mov             fp, SP
    // 0xaa4200: AllocStack(0x70)
    //     0xaa4200: sub             SP, SP, #0x70
    // 0xaa4204: SetupParameters(ModalBarrier this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xaa4204: mov             x0, x1
    //     0xaa4208: stur            x1, [fp, #-8]
    //     0xaa420c: mov             x1, x2
    //     0xaa4210: stur            x2, [fp, #-0x10]
    // 0xaa4214: CheckStackOverflow
    //     0xaa4214: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa4218: cmp             SP, x16
    //     0xaa421c: b.ls            #0xaa4438
    // 0xaa4220: r1 = 2
    //     0xaa4220: movz            x1, #0x2
    // 0xaa4224: r0 = AllocateContext()
    //     0xaa4224: bl              #0xec126c  ; AllocateContextStub
    // 0xaa4228: mov             x1, x0
    // 0xaa422c: ldur            x0, [fp, #-8]
    // 0xaa4230: StoreField: r1->field_f = r0
    //     0xaa4230: stur            w0, [x1, #0xf]
    // 0xaa4234: ldur            x3, [fp, #-0x10]
    // 0xaa4238: StoreField: r1->field_13 = r3
    //     0xaa4238: stur            w3, [x1, #0x13]
    // 0xaa423c: LoadField: r2 = r0->field_f
    //     0xaa423c: ldur            w2, [x0, #0xf]
    // 0xaa4240: DecompressPointer r2
    //     0xaa4240: add             x2, x2, HEAP, lsl #32
    // 0xaa4244: tbnz            w2, #4, #0xaa4250
    // 0xaa4248: r4 = true
    //     0xaa4248: add             x4, NULL, #0x20  ; true
    // 0xaa424c: b               #0xaa4254
    // 0xaa4250: r4 = false
    //     0xaa4250: add             x4, NULL, #0x30  ; false
    // 0xaa4254: mov             x2, x1
    // 0xaa4258: stur            x4, [fp, #-0x18]
    // 0xaa425c: r1 = Function 'handleDismiss':.
    //     0xaa425c: add             x1, PP, #0x25, lsl #12  ; [pp+0x254d0] AnonymousClosure: (0xaa4458), in [package:flutter/src/widgets/modal_barrier.dart] ModalBarrier::build (0xaa41f8)
    //     0xaa4260: ldr             x1, [x1, #0x4d0]
    // 0xaa4264: r0 = AllocateClosure()
    //     0xaa4264: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa4268: mov             x2, x0
    // 0xaa426c: ldur            x0, [fp, #-0x18]
    // 0xaa4270: stur            x2, [fp, #-0x38]
    // 0xaa4274: tbnz            w0, #4, #0xaa4294
    // 0xaa4278: ldur            x3, [fp, #-8]
    // 0xaa427c: LoadField: r1 = r3->field_1b
    //     0xaa427c: ldur            w1, [x3, #0x1b]
    // 0xaa4280: DecompressPointer r1
    //     0xaa4280: add             x1, x1, HEAP, lsl #32
    // 0xaa4284: cmp             w1, NULL
    // 0xaa4288: b.eq            #0xaa4298
    // 0xaa428c: mov             x4, x2
    // 0xaa4290: b               #0xaa429c
    // 0xaa4294: ldur            x3, [fp, #-8]
    // 0xaa4298: r4 = Null
    //     0xaa4298: mov             x4, NULL
    // 0xaa429c: stur            x4, [fp, #-0x30]
    // 0xaa42a0: tbnz            w0, #4, #0xaa42bc
    // 0xaa42a4: LoadField: r1 = r3->field_1b
    //     0xaa42a4: ldur            w1, [x3, #0x1b]
    // 0xaa42a8: DecompressPointer r1
    //     0xaa42a8: add             x1, x1, HEAP, lsl #32
    // 0xaa42ac: cmp             w1, NULL
    // 0xaa42b0: b.eq            #0xaa42bc
    // 0xaa42b4: mov             x5, x2
    // 0xaa42b8: b               #0xaa42c0
    // 0xaa42bc: r5 = Null
    //     0xaa42bc: mov             x5, NULL
    // 0xaa42c0: stur            x5, [fp, #-0x28]
    // 0xaa42c4: tbnz            w0, #4, #0xaa42d8
    // 0xaa42c8: LoadField: r1 = r3->field_1b
    //     0xaa42c8: ldur            w1, [x3, #0x1b]
    // 0xaa42cc: DecompressPointer r1
    //     0xaa42cc: add             x1, x1, HEAP, lsl #32
    // 0xaa42d0: mov             x6, x1
    // 0xaa42d4: b               #0xaa42dc
    // 0xaa42d8: r6 = Null
    //     0xaa42d8: mov             x6, NULL
    // 0xaa42dc: stur            x6, [fp, #-0x20]
    // 0xaa42e0: tbnz            w0, #4, #0xaa4304
    // 0xaa42e4: LoadField: r1 = r3->field_1b
    //     0xaa42e4: ldur            w1, [x3, #0x1b]
    // 0xaa42e8: DecompressPointer r1
    //     0xaa42e8: add             x1, x1, HEAP, lsl #32
    // 0xaa42ec: cmp             w1, NULL
    // 0xaa42f0: b.eq            #0xaa4304
    // 0xaa42f4: ldur            x1, [fp, #-0x10]
    // 0xaa42f8: r0 = of()
    //     0xaa42f8: bl              #0x6b0ae8  ; [package:flutter/src/widgets/basic.dart] Directionality::of
    // 0xaa42fc: mov             x1, x0
    // 0xaa4300: b               #0xaa4308
    // 0xaa4304: r1 = Null
    //     0xaa4304: mov             x1, NULL
    // 0xaa4308: ldur            x0, [fp, #-8]
    // 0xaa430c: stur            x1, [fp, #-0x40]
    // 0xaa4310: LoadField: r2 = r0->field_b
    //     0xaa4310: ldur            w2, [x0, #0xb]
    // 0xaa4314: DecompressPointer r2
    //     0xaa4314: add             x2, x2, HEAP, lsl #32
    // 0xaa4318: stur            x2, [fp, #-0x10]
    // 0xaa431c: cmp             w2, NULL
    // 0xaa4320: b.ne            #0xaa432c
    // 0xaa4324: r1 = Null
    //     0xaa4324: mov             x1, NULL
    // 0xaa4328: b               #0xaa433c
    // 0xaa432c: r0 = ColoredBox()
    //     0xaa432c: bl              #0x9e2ff4  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xaa4330: mov             x1, x0
    // 0xaa4334: ldur            x0, [fp, #-0x10]
    // 0xaa4338: StoreField: r1->field_f = r0
    //     0xaa4338: stur            w0, [x1, #0xf]
    // 0xaa433c: ldur            x0, [fp, #-0x18]
    // 0xaa4340: stur            x1, [fp, #-8]
    // 0xaa4344: r0 = ConstrainedBox()
    //     0xaa4344: bl              #0x9d4fe0  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xaa4348: mov             x1, x0
    // 0xaa434c: r0 = Instance_BoxConstraints
    //     0xaa434c: add             x0, PP, #0x25, lsl #12  ; [pp+0x254d8] Obj!BoxConstraints@e11811
    //     0xaa4350: ldr             x0, [x0, #0x4d8]
    // 0xaa4354: stur            x1, [fp, #-0x10]
    // 0xaa4358: StoreField: r1->field_f = r0
    //     0xaa4358: stur            w0, [x1, #0xf]
    // 0xaa435c: ldur            x0, [fp, #-8]
    // 0xaa4360: StoreField: r1->field_b = r0
    //     0xaa4360: stur            w0, [x1, #0xb]
    // 0xaa4364: r0 = MouseRegion()
    //     0xaa4364: bl              #0x9d4fc8  ; AllocateMouseRegionStub -> MouseRegion (size=0x28)
    // 0xaa4368: mov             x1, x0
    // 0xaa436c: r0 = Instance_SystemMouseCursor
    //     0xaa436c: ldr             x0, [PP, #0x26a8]  ; [pp+0x26a8] Obj!SystemMouseCursor@e1cef1
    // 0xaa4370: stur            x1, [fp, #-8]
    // 0xaa4374: StoreField: r1->field_1b = r0
    //     0xaa4374: stur            w0, [x1, #0x1b]
    // 0xaa4378: r0 = true
    //     0xaa4378: add             x0, NULL, #0x20  ; true
    // 0xaa437c: StoreField: r1->field_1f = r0
    //     0xaa437c: stur            w0, [x1, #0x1f]
    // 0xaa4380: ldur            x2, [fp, #-0x10]
    // 0xaa4384: StoreField: r1->field_b = r2
    //     0xaa4384: stur            w2, [x1, #0xb]
    // 0xaa4388: r0 = Semantics()
    //     0xaa4388: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0xaa438c: stur            x0, [fp, #-0x10]
    // 0xaa4390: ldur            x16, [fp, #-0x30]
    // 0xaa4394: stp             x16, NULL, [SP, #0x20]
    // 0xaa4398: ldur            x16, [fp, #-0x28]
    // 0xaa439c: ldur            lr, [fp, #-0x20]
    // 0xaa43a0: stp             lr, x16, [SP, #0x10]
    // 0xaa43a4: ldur            x16, [fp, #-0x40]
    // 0xaa43a8: ldur            lr, [fp, #-8]
    // 0xaa43ac: stp             lr, x16, [SP]
    // 0xaa43b0: mov             x1, x0
    // 0xaa43b4: r4 = const [0, 0x7, 0x6, 0x1, child, 0x6, label, 0x4, onDismiss, 0x3, onTap, 0x2, onTapHint, 0x1, textDirection, 0x5, null]
    //     0xaa43b4: add             x4, PP, #0x25, lsl #12  ; [pp+0x254e0] List(17) [0, 0x7, 0x6, 0x1, "child", 0x6, "label", 0x4, "onDismiss", 0x3, "onTap", 0x2, "onTapHint", 0x1, "textDirection", 0x5, Null]
    //     0xaa43b8: ldr             x4, [x4, #0x4e0]
    // 0xaa43bc: r0 = Semantics()
    //     0xaa43bc: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0xaa43c0: ldur            x0, [fp, #-0x18]
    // 0xaa43c4: tbz             w0, #4, #0xaa43d0
    // 0xaa43c8: r2 = true
    //     0xaa43c8: add             x2, NULL, #0x20  ; true
    // 0xaa43cc: b               #0xaa43d4
    // 0xaa43d0: r2 = false
    //     0xaa43d0: add             x2, NULL, #0x30  ; false
    // 0xaa43d4: ldur            x1, [fp, #-0x38]
    // 0xaa43d8: ldur            x0, [fp, #-0x10]
    // 0xaa43dc: stur            x2, [fp, #-8]
    // 0xaa43e0: r0 = _ModalBarrierGestureDetector()
    //     0xaa43e0: bl              #0xaa444c  ; Allocate_ModalBarrierGestureDetectorStub -> _ModalBarrierGestureDetector (size=0x14)
    // 0xaa43e4: mov             x1, x0
    // 0xaa43e8: ldur            x0, [fp, #-0x10]
    // 0xaa43ec: stur            x1, [fp, #-0x18]
    // 0xaa43f0: StoreField: r1->field_b = r0
    //     0xaa43f0: stur            w0, [x1, #0xb]
    // 0xaa43f4: ldur            x0, [fp, #-0x38]
    // 0xaa43f8: StoreField: r1->field_f = r0
    //     0xaa43f8: stur            w0, [x1, #0xf]
    // 0xaa43fc: r0 = ExcludeSemantics()
    //     0xaa43fc: bl              #0x6ac8e0  ; AllocateExcludeSemanticsStub -> ExcludeSemantics (size=0x14)
    // 0xaa4400: mov             x1, x0
    // 0xaa4404: ldur            x0, [fp, #-8]
    // 0xaa4408: stur            x1, [fp, #-0x10]
    // 0xaa440c: StoreField: r1->field_f = r0
    //     0xaa440c: stur            w0, [x1, #0xf]
    // 0xaa4410: ldur            x0, [fp, #-0x18]
    // 0xaa4414: StoreField: r1->field_b = r0
    //     0xaa4414: stur            w0, [x1, #0xb]
    // 0xaa4418: r0 = BlockSemantics()
    //     0xaa4418: bl              #0xaa4440  ; AllocateBlockSemanticsStub -> BlockSemantics (size=0x14)
    // 0xaa441c: r1 = true
    //     0xaa441c: add             x1, NULL, #0x20  ; true
    // 0xaa4420: StoreField: r0->field_f = r1
    //     0xaa4420: stur            w1, [x0, #0xf]
    // 0xaa4424: ldur            x1, [fp, #-0x10]
    // 0xaa4428: StoreField: r0->field_b = r1
    //     0xaa4428: stur            w1, [x0, #0xb]
    // 0xaa442c: LeaveFrame
    //     0xaa442c: mov             SP, fp
    //     0xaa4430: ldp             fp, lr, [SP], #0x10
    // 0xaa4434: ret
    //     0xaa4434: ret             
    // 0xaa4438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa4438: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa443c: b               #0xaa4220
  }
  [closure] void handleDismiss(dynamic) {
    // ** addr: 0xaa4458, size: 0x78
    // 0xaa4458: EnterFrame
    //     0xaa4458: stp             fp, lr, [SP, #-0x10]!
    //     0xaa445c: mov             fp, SP
    // 0xaa4460: AllocStack(0x10)
    //     0xaa4460: sub             SP, SP, #0x10
    // 0xaa4464: SetupParameters()
    //     0xaa4464: ldr             x0, [fp, #0x10]
    //     0xaa4468: ldur            w1, [x0, #0x17]
    //     0xaa446c: add             x1, x1, HEAP, lsl #32
    // 0xaa4470: CheckStackOverflow
    //     0xaa4470: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa4474: cmp             SP, x16
    //     0xaa4478: b.ls            #0xaa44c8
    // 0xaa447c: LoadField: r0 = r1->field_f
    //     0xaa447c: ldur            w0, [x1, #0xf]
    // 0xaa4480: DecompressPointer r0
    //     0xaa4480: add             x0, x0, HEAP, lsl #32
    // 0xaa4484: LoadField: r2 = r0->field_f
    //     0xaa4484: ldur            w2, [x0, #0xf]
    // 0xaa4488: DecompressPointer r2
    //     0xaa4488: add             x2, x2, HEAP, lsl #32
    // 0xaa448c: tbnz            w2, #4, #0xaa44ac
    // 0xaa4490: LoadField: r0 = r1->field_13
    //     0xaa4490: ldur            w0, [x1, #0x13]
    // 0xaa4494: DecompressPointer r0
    //     0xaa4494: add             x0, x0, HEAP, lsl #32
    // 0xaa4498: r16 = <Object?>
    //     0xaa4498: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xaa449c: stp             x0, x16, [SP]
    // 0xaa44a0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaa44a0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaa44a4: r0 = maybePop()
    //     0xaa44a4: bl              #0xa97df8  ; [package:flutter/src/widgets/navigator.dart] Navigator::maybePop
    // 0xaa44a8: b               #0xaa44b8
    // 0xaa44ac: r1 = Instance_SystemSoundType
    //     0xaa44ac: add             x1, PP, #0x25, lsl #12  ; [pp+0x254e8] Obj!SystemSoundType@e34d41
    //     0xaa44b0: ldr             x1, [x1, #0x4e8]
    // 0xaa44b4: r0 = play()
    //     0xaa44b4: bl              #0x9f34c8  ; [package:flutter/src/services/system_sound.dart] SystemSound::play
    // 0xaa44b8: r0 = Null
    //     0xaa44b8: mov             x0, NULL
    // 0xaa44bc: LeaveFrame
    //     0xaa44bc: mov             SP, fp
    //     0xaa44c0: ldp             fp, lr, [SP], #0x10
    // 0xaa44c4: ret
    //     0xaa44c4: ret             
    // 0xaa44c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa44c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa44cc: b               #0xaa447c
  }
}
