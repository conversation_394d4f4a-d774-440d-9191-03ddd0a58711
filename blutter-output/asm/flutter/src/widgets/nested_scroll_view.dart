// lib: , url: package:flutter/src/widgets/nested_scroll_view.dart

// class id: 1049152, size: 0x8
class :: {
}

// class id: 2580, size: 0x3c, field offset: 0x24
class _NestedScrollMetrics extends FixedScrollMetrics {
}

// class id: 2595, size: 0x20, field offset: 0x18
class _NestedOuterBallisticScrollActivity extends BallisticScrollActivity {

  _ resetActivity(/* No info */) {
    // ** addr: 0xbfa4d0, size: 0xb4
    // 0xbfa4d0: EnterFrame
    //     0xbfa4d0: stp             fp, lr, [SP, #-0x10]!
    //     0xbfa4d4: mov             fp, SP
    // 0xbfa4d8: AllocStack(0x18)
    //     0xbfa4d8: sub             SP, SP, #0x18
    // 0xbfa4dc: SetupParameters(_NestedOuterBallisticScrollActivity this /* r1 => r3, fp-0x10 */)
    //     0xbfa4dc: mov             x3, x1
    //     0xbfa4e0: stur            x1, [fp, #-0x10]
    // 0xbfa4e4: CheckStackOverflow
    //     0xbfa4e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfa4e8: cmp             SP, x16
    //     0xbfa4ec: b.ls            #0xbfa574
    // 0xbfa4f0: LoadField: r4 = r3->field_7
    //     0xbfa4f0: ldur            w4, [x3, #7]
    // 0xbfa4f4: DecompressPointer r4
    //     0xbfa4f4: add             x4, x4, HEAP, lsl #32
    // 0xbfa4f8: mov             x0, x4
    // 0xbfa4fc: stur            x4, [fp, #-8]
    // 0xbfa500: r2 = Null
    //     0xbfa500: mov             x2, NULL
    // 0xbfa504: r1 = Null
    //     0xbfa504: mov             x1, NULL
    // 0xbfa508: r4 = LoadClassIdInstr(r0)
    //     0xbfa508: ldur            x4, [x0, #-1]
    //     0xbfa50c: ubfx            x4, x4, #0xc, #0x14
    // 0xbfa510: cmp             x4, #0xe46
    // 0xbfa514: b.eq            #0xbfa528
    // 0xbfa518: r8 = _NestedScrollPosition
    //     0xbfa518: ldr             x8, [PP, #0x75e0]  ; [pp+0x75e0] Type: _NestedScrollPosition
    // 0xbfa51c: r3 = Null
    //     0xbfa51c: add             x3, PP, #0x56, lsl #12  ; [pp+0x56cd0] Null
    //     0xbfa520: ldr             x3, [x3, #0xcd0]
    // 0xbfa524: r0 = DefaultTypeTest()
    //     0xbfa524: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xbfa528: ldur            x0, [fp, #-0x10]
    // 0xbfa52c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbfa52c: ldur            w2, [x0, #0x17]
    // 0xbfa530: DecompressPointer r2
    //     0xbfa530: add             x2, x2, HEAP, lsl #32
    // 0xbfa534: stur            x2, [fp, #-0x18]
    // 0xbfa538: LoadField: r1 = r0->field_f
    //     0xbfa538: ldur            w1, [x0, #0xf]
    // 0xbfa53c: DecompressPointer r1
    //     0xbfa53c: add             x1, x1, HEAP, lsl #32
    // 0xbfa540: r16 = Sentinel
    //     0xbfa540: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbfa544: cmp             w1, w16
    // 0xbfa548: b.eq            #0xbfa57c
    // 0xbfa54c: r0 = velocity()
    //     0xbfa54c: bl              #0xbf6f5c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::velocity
    // 0xbfa550: ldur            x1, [fp, #-0x18]
    // 0xbfa554: r0 = createOuterBallisticScrollActivity()
    //     0xbfa554: bl              #0xbfa584  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::createOuterBallisticScrollActivity
    // 0xbfa558: ldur            x1, [fp, #-8]
    // 0xbfa55c: mov             x2, x0
    // 0xbfa560: r0 = beginActivity()
    //     0xbfa560: bl              #0x67956c  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::beginActivity
    // 0xbfa564: r0 = Null
    //     0xbfa564: mov             x0, NULL
    // 0xbfa568: LeaveFrame
    //     0xbfa568: mov             SP, fp
    //     0xbfa56c: ldp             fp, lr, [SP], #0x10
    // 0xbfa570: ret
    //     0xbfa570: ret             
    // 0xbfa574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfa574: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfa578: b               #0xbfa4f0
    // 0xbfa57c: r9 = _controller
    //     0xbfa57c: ldr             x9, [PP, #0x7638]  ; [pp+0x7638] Field <BallisticScrollActivity._controller@324498029>: late (offset: 0x10)
    // 0xbfa580: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbfa580: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ applyNewDimensions(/* No info */) {
    // ** addr: 0xc2ac20, size: 0xb4
    // 0xc2ac20: EnterFrame
    //     0xc2ac20: stp             fp, lr, [SP, #-0x10]!
    //     0xc2ac24: mov             fp, SP
    // 0xc2ac28: AllocStack(0x18)
    //     0xc2ac28: sub             SP, SP, #0x18
    // 0xc2ac2c: SetupParameters(_NestedOuterBallisticScrollActivity this /* r1 => r3, fp-0x10 */)
    //     0xc2ac2c: mov             x3, x1
    //     0xc2ac30: stur            x1, [fp, #-0x10]
    // 0xc2ac34: CheckStackOverflow
    //     0xc2ac34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2ac38: cmp             SP, x16
    //     0xc2ac3c: b.ls            #0xc2acc4
    // 0xc2ac40: LoadField: r4 = r3->field_7
    //     0xc2ac40: ldur            w4, [x3, #7]
    // 0xc2ac44: DecompressPointer r4
    //     0xc2ac44: add             x4, x4, HEAP, lsl #32
    // 0xc2ac48: mov             x0, x4
    // 0xc2ac4c: stur            x4, [fp, #-8]
    // 0xc2ac50: r2 = Null
    //     0xc2ac50: mov             x2, NULL
    // 0xc2ac54: r1 = Null
    //     0xc2ac54: mov             x1, NULL
    // 0xc2ac58: r4 = LoadClassIdInstr(r0)
    //     0xc2ac58: ldur            x4, [x0, #-1]
    //     0xc2ac5c: ubfx            x4, x4, #0xc, #0x14
    // 0xc2ac60: cmp             x4, #0xe46
    // 0xc2ac64: b.eq            #0xc2ac78
    // 0xc2ac68: r8 = _NestedScrollPosition
    //     0xc2ac68: ldr             x8, [PP, #0x75e0]  ; [pp+0x75e0] Type: _NestedScrollPosition
    // 0xc2ac6c: r3 = Null
    //     0xc2ac6c: add             x3, PP, #0x56, lsl #12  ; [pp+0x56cc0] Null
    //     0xc2ac70: ldr             x3, [x3, #0xcc0]
    // 0xc2ac74: r0 = DefaultTypeTest()
    //     0xc2ac74: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc2ac78: ldur            x0, [fp, #-0x10]
    // 0xc2ac7c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xc2ac7c: ldur            w2, [x0, #0x17]
    // 0xc2ac80: DecompressPointer r2
    //     0xc2ac80: add             x2, x2, HEAP, lsl #32
    // 0xc2ac84: stur            x2, [fp, #-0x18]
    // 0xc2ac88: LoadField: r1 = r0->field_f
    //     0xc2ac88: ldur            w1, [x0, #0xf]
    // 0xc2ac8c: DecompressPointer r1
    //     0xc2ac8c: add             x1, x1, HEAP, lsl #32
    // 0xc2ac90: r16 = Sentinel
    //     0xc2ac90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc2ac94: cmp             w1, w16
    // 0xc2ac98: b.eq            #0xc2accc
    // 0xc2ac9c: r0 = velocity()
    //     0xc2ac9c: bl              #0xbf6f5c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::velocity
    // 0xc2aca0: ldur            x1, [fp, #-0x18]
    // 0xc2aca4: r0 = createOuterBallisticScrollActivity()
    //     0xc2aca4: bl              #0xbfa584  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::createOuterBallisticScrollActivity
    // 0xc2aca8: ldur            x1, [fp, #-8]
    // 0xc2acac: mov             x2, x0
    // 0xc2acb0: r0 = beginActivity()
    //     0xc2acb0: bl              #0x67956c  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::beginActivity
    // 0xc2acb4: r0 = Null
    //     0xc2acb4: mov             x0, NULL
    // 0xc2acb8: LeaveFrame
    //     0xc2acb8: mov             SP, fp
    //     0xc2acbc: ldp             fp, lr, [SP], #0x10
    // 0xc2acc0: ret
    //     0xc2acc0: ret             
    // 0xc2acc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2acc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2acc8: b               #0xc2ac40
    // 0xc2accc: r9 = _controller
    //     0xc2accc: ldr             x9, [PP, #0x7638]  ; [pp+0x7638] Field <BallisticScrollActivity._controller@324498029>: late (offset: 0x10)
    // 0xc2acd0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc2acd0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ applyMoveTo(/* No info */) {
    // ** addr: 0xdba3e8, size: 0x190
    // 0xdba3e8: EnterFrame
    //     0xdba3e8: stp             fp, lr, [SP, #-0x10]!
    //     0xdba3ec: mov             fp, SP
    // 0xdba3f0: AllocStack(0x18)
    //     0xdba3f0: sub             SP, SP, #0x18
    // 0xdba3f4: SetupParameters(_NestedOuterBallisticScrollActivity this /* r1 => r0, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x18 */)
    //     0xdba3f4: mov             x0, x1
    //     0xdba3f8: stur            x1, [fp, #-8]
    //     0xdba3fc: stur            d0, [fp, #-0x18]
    // 0xdba400: CheckStackOverflow
    //     0xdba400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdba404: cmp             SP, x16
    //     0xdba408: b.ls            #0xdba568
    // 0xdba40c: LoadField: r1 = r0->field_f
    //     0xdba40c: ldur            w1, [x0, #0xf]
    // 0xdba410: DecompressPointer r1
    //     0xdba410: add             x1, x1, HEAP, lsl #32
    // 0xdba414: r16 = Sentinel
    //     0xdba414: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xdba418: cmp             w1, w16
    // 0xdba41c: b.eq            #0xdba570
    // 0xdba420: r0 = velocity()
    //     0xdba420: bl              #0xbf6f5c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::velocity
    // 0xdba424: mov             v1.16b, v0.16b
    // 0xdba428: d0 = 0.000000
    //     0xdba428: eor             v0.16b, v0.16b, v0.16b
    // 0xdba42c: fcmp            d1, d0
    // 0xdba430: b.le            #0xdba48c
    // 0xdba434: ldur            x0, [fp, #-8]
    // 0xdba438: ldur            d1, [fp, #-0x18]
    // 0xdba43c: LoadField: r1 = r0->field_1b
    //     0xdba43c: ldur            w1, [x0, #0x1b]
    // 0xdba440: DecompressPointer r1
    //     0xdba440: add             x1, x1, HEAP, lsl #32
    // 0xdba444: LoadField: d0 = r1->field_23
    //     0xdba444: ldur            d0, [x1, #0x23]
    // 0xdba448: fcmp            d0, d1
    // 0xdba44c: b.le            #0xdba460
    // 0xdba450: r0 = true
    //     0xdba450: add             x0, NULL, #0x20  ; true
    // 0xdba454: LeaveFrame
    //     0xdba454: mov             SP, fp
    //     0xdba458: ldp             fp, lr, [SP], #0x10
    // 0xdba45c: ret
    //     0xdba45c: ret             
    // 0xdba460: LoadField: d0 = r1->field_2b
    //     0xdba460: ldur            d0, [x1, #0x2b]
    // 0xdba464: fcmp            d1, d0
    // 0xdba468: b.le            #0xdba474
    // 0xdba46c: r2 = true
    //     0xdba46c: add             x2, NULL, #0x20  ; true
    // 0xdba470: b               #0xdba47c
    // 0xdba474: mov             v0.16b, v1.16b
    // 0xdba478: r2 = false
    //     0xdba478: add             x2, NULL, #0x30  ; false
    // 0xdba47c: mov             x16, x0
    // 0xdba480: mov             x0, x1
    // 0xdba484: mov             x1, x16
    // 0xdba488: b               #0xdba540
    // 0xdba48c: ldur            x0, [fp, #-8]
    // 0xdba490: ldur            d1, [fp, #-0x18]
    // 0xdba494: mov             x1, x0
    // 0xdba498: r0 = velocity()
    //     0xdba498: bl              #0xdaa554  ; [package:flutter/src/widgets/scroll_activity.dart] BallisticScrollActivity::velocity
    // 0xdba49c: mov             v1.16b, v0.16b
    // 0xdba4a0: d0 = 0.000000
    //     0xdba4a0: eor             v0.16b, v0.16b, v0.16b
    // 0xdba4a4: fcmp            d0, d1
    // 0xdba4a8: b.le            #0xdba4f8
    // 0xdba4ac: ldur            x1, [fp, #-8]
    // 0xdba4b0: ldur            d0, [fp, #-0x18]
    // 0xdba4b4: LoadField: r0 = r1->field_1b
    //     0xdba4b4: ldur            w0, [x1, #0x1b]
    // 0xdba4b8: DecompressPointer r0
    //     0xdba4b8: add             x0, x0, HEAP, lsl #32
    // 0xdba4bc: LoadField: d1 = r0->field_2b
    //     0xdba4bc: ldur            d1, [x0, #0x2b]
    // 0xdba4c0: fcmp            d0, d1
    // 0xdba4c4: b.le            #0xdba4d8
    // 0xdba4c8: r0 = true
    //     0xdba4c8: add             x0, NULL, #0x20  ; true
    // 0xdba4cc: LeaveFrame
    //     0xdba4cc: mov             SP, fp
    //     0xdba4d0: ldp             fp, lr, [SP], #0x10
    // 0xdba4d4: ret
    //     0xdba4d4: ret             
    // 0xdba4d8: LoadField: d1 = r0->field_23
    //     0xdba4d8: ldur            d1, [x0, #0x23]
    // 0xdba4dc: fcmp            d1, d0
    // 0xdba4e0: b.le            #0xdba4f0
    // 0xdba4e4: mov             v0.16b, v1.16b
    // 0xdba4e8: r2 = true
    //     0xdba4e8: add             x2, NULL, #0x20  ; true
    // 0xdba4ec: b               #0xdba540
    // 0xdba4f0: r2 = false
    //     0xdba4f0: add             x2, NULL, #0x30  ; false
    // 0xdba4f4: b               #0xdba540
    // 0xdba4f8: ldur            x1, [fp, #-8]
    // 0xdba4fc: ldur            d0, [fp, #-0x18]
    // 0xdba500: LoadField: r0 = r1->field_1b
    //     0xdba500: ldur            w0, [x1, #0x1b]
    // 0xdba504: DecompressPointer r0
    //     0xdba504: add             x0, x0, HEAP, lsl #32
    // 0xdba508: LoadField: d1 = r0->field_23
    //     0xdba508: ldur            d1, [x0, #0x23]
    // 0xdba50c: LoadField: d2 = r0->field_2b
    //     0xdba50c: ldur            d2, [x0, #0x2b]
    // 0xdba510: fcmp            d1, d0
    // 0xdba514: b.le            #0xdba520
    // 0xdba518: mov             v0.16b, v1.16b
    // 0xdba51c: b               #0xdba53c
    // 0xdba520: fcmp            d0, d2
    // 0xdba524: b.le            #0xdba530
    // 0xdba528: mov             v0.16b, v2.16b
    // 0xdba52c: b               #0xdba53c
    // 0xdba530: fcmp            d0, d0
    // 0xdba534: b.vc            #0xdba53c
    // 0xdba538: mov             v0.16b, v2.16b
    // 0xdba53c: r2 = true
    //     0xdba53c: add             x2, NULL, #0x20  ; true
    // 0xdba540: stur            x2, [fp, #-0x10]
    // 0xdba544: LoadField: d1 = r0->field_33
    //     0xdba544: ldur            d1, [x0, #0x33]
    // 0xdba548: fadd            d2, d0, d1
    // 0xdba54c: mov             v0.16b, v2.16b
    // 0xdba550: r0 = applyMoveTo()
    //     0xdba550: bl              #0xdba578  ; [package:flutter/src/widgets/scroll_activity.dart] BallisticScrollActivity::applyMoveTo
    // 0xdba554: ldur            x1, [fp, #-0x10]
    // 0xdba558: eor             x0, x1, #0x10
    // 0xdba55c: LeaveFrame
    //     0xdba55c: mov             SP, fp
    //     0xdba560: ldp             fp, lr, [SP], #0x10
    // 0xdba564: ret
    //     0xdba564: ret             
    // 0xdba568: r0 = StackOverflowSharedWithFPURegs()
    //     0xdba568: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xdba56c: b               #0xdba40c
    // 0xdba570: r9 = _controller
    //     0xdba570: ldr             x9, [PP, #0x7638]  ; [pp+0x7638] Field <BallisticScrollActivity._controller@324498029>: late (offset: 0x10)
    // 0xdba574: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xdba574: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
  }
}

// class id: 2596, size: 0x1c, field offset: 0x18
class _NestedInnerBallisticScrollActivity extends BallisticScrollActivity {

  _ resetActivity(/* No info */) {
    // ** addr: 0xbf9404, size: 0xb8
    // 0xbf9404: EnterFrame
    //     0xbf9404: stp             fp, lr, [SP, #-0x10]!
    //     0xbf9408: mov             fp, SP
    // 0xbf940c: AllocStack(0x18)
    //     0xbf940c: sub             SP, SP, #0x18
    // 0xbf9410: SetupParameters(_NestedInnerBallisticScrollActivity this /* r1 => r3, fp-0x10 */)
    //     0xbf9410: mov             x3, x1
    //     0xbf9414: stur            x1, [fp, #-0x10]
    // 0xbf9418: CheckStackOverflow
    //     0xbf9418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf941c: cmp             SP, x16
    //     0xbf9420: b.ls            #0xbf94ac
    // 0xbf9424: LoadField: r4 = r3->field_7
    //     0xbf9424: ldur            w4, [x3, #7]
    // 0xbf9428: DecompressPointer r4
    //     0xbf9428: add             x4, x4, HEAP, lsl #32
    // 0xbf942c: mov             x0, x4
    // 0xbf9430: stur            x4, [fp, #-8]
    // 0xbf9434: r2 = Null
    //     0xbf9434: mov             x2, NULL
    // 0xbf9438: r1 = Null
    //     0xbf9438: mov             x1, NULL
    // 0xbf943c: r4 = LoadClassIdInstr(r0)
    //     0xbf943c: ldur            x4, [x0, #-1]
    //     0xbf9440: ubfx            x4, x4, #0xc, #0x14
    // 0xbf9444: cmp             x4, #0xe46
    // 0xbf9448: b.eq            #0xbf945c
    // 0xbf944c: r8 = _NestedScrollPosition
    //     0xbf944c: ldr             x8, [PP, #0x75e0]  ; [pp+0x75e0] Type: _NestedScrollPosition
    // 0xbf9450: r3 = Null
    //     0xbf9450: add             x3, PP, #0x56, lsl #12  ; [pp+0x56cf0] Null
    //     0xbf9454: ldr             x3, [x3, #0xcf0]
    // 0xbf9458: r0 = DefaultTypeTest()
    //     0xbf9458: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xbf945c: ldur            x0, [fp, #-0x10]
    // 0xbf9460: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbf9460: ldur            w2, [x0, #0x17]
    // 0xbf9464: DecompressPointer r2
    //     0xbf9464: add             x2, x2, HEAP, lsl #32
    // 0xbf9468: stur            x2, [fp, #-0x18]
    // 0xbf946c: LoadField: r1 = r0->field_f
    //     0xbf946c: ldur            w1, [x0, #0xf]
    // 0xbf9470: DecompressPointer r1
    //     0xbf9470: add             x1, x1, HEAP, lsl #32
    // 0xbf9474: r16 = Sentinel
    //     0xbf9474: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbf9478: cmp             w1, w16
    // 0xbf947c: b.eq            #0xbf94b4
    // 0xbf9480: r0 = velocity()
    //     0xbf9480: bl              #0xbf6f5c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::velocity
    // 0xbf9484: ldur            x1, [fp, #-0x18]
    // 0xbf9488: ldur            x2, [fp, #-8]
    // 0xbf948c: r0 = createInnerBallisticScrollActivity()
    //     0xbf948c: bl              #0xbf94bc  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::createInnerBallisticScrollActivity
    // 0xbf9490: ldur            x1, [fp, #-8]
    // 0xbf9494: mov             x2, x0
    // 0xbf9498: r0 = beginActivity()
    //     0xbf9498: bl              #0x67956c  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::beginActivity
    // 0xbf949c: r0 = Null
    //     0xbf949c: mov             x0, NULL
    // 0xbf94a0: LeaveFrame
    //     0xbf94a0: mov             SP, fp
    //     0xbf94a4: ldp             fp, lr, [SP], #0x10
    // 0xbf94a8: ret
    //     0xbf94a8: ret             
    // 0xbf94ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf94ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf94b0: b               #0xbf9424
    // 0xbf94b4: r9 = _controller
    //     0xbf94b4: ldr             x9, [PP, #0x7638]  ; [pp+0x7638] Field <BallisticScrollActivity._controller@324498029>: late (offset: 0x10)
    // 0xbf94b8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbf94b8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ applyNewDimensions(/* No info */) {
    // ** addr: 0xc2ab68, size: 0xb8
    // 0xc2ab68: EnterFrame
    //     0xc2ab68: stp             fp, lr, [SP, #-0x10]!
    //     0xc2ab6c: mov             fp, SP
    // 0xc2ab70: AllocStack(0x18)
    //     0xc2ab70: sub             SP, SP, #0x18
    // 0xc2ab74: SetupParameters(_NestedInnerBallisticScrollActivity this /* r1 => r3, fp-0x10 */)
    //     0xc2ab74: mov             x3, x1
    //     0xc2ab78: stur            x1, [fp, #-0x10]
    // 0xc2ab7c: CheckStackOverflow
    //     0xc2ab7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2ab80: cmp             SP, x16
    //     0xc2ab84: b.ls            #0xc2ac10
    // 0xc2ab88: LoadField: r4 = r3->field_7
    //     0xc2ab88: ldur            w4, [x3, #7]
    // 0xc2ab8c: DecompressPointer r4
    //     0xc2ab8c: add             x4, x4, HEAP, lsl #32
    // 0xc2ab90: mov             x0, x4
    // 0xc2ab94: stur            x4, [fp, #-8]
    // 0xc2ab98: r2 = Null
    //     0xc2ab98: mov             x2, NULL
    // 0xc2ab9c: r1 = Null
    //     0xc2ab9c: mov             x1, NULL
    // 0xc2aba0: r4 = LoadClassIdInstr(r0)
    //     0xc2aba0: ldur            x4, [x0, #-1]
    //     0xc2aba4: ubfx            x4, x4, #0xc, #0x14
    // 0xc2aba8: cmp             x4, #0xe46
    // 0xc2abac: b.eq            #0xc2abc0
    // 0xc2abb0: r8 = _NestedScrollPosition
    //     0xc2abb0: ldr             x8, [PP, #0x75e0]  ; [pp+0x75e0] Type: _NestedScrollPosition
    // 0xc2abb4: r3 = Null
    //     0xc2abb4: add             x3, PP, #0x56, lsl #12  ; [pp+0x56ce0] Null
    //     0xc2abb8: ldr             x3, [x3, #0xce0]
    // 0xc2abbc: r0 = DefaultTypeTest()
    //     0xc2abbc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc2abc0: ldur            x0, [fp, #-0x10]
    // 0xc2abc4: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xc2abc4: ldur            w2, [x0, #0x17]
    // 0xc2abc8: DecompressPointer r2
    //     0xc2abc8: add             x2, x2, HEAP, lsl #32
    // 0xc2abcc: stur            x2, [fp, #-0x18]
    // 0xc2abd0: LoadField: r1 = r0->field_f
    //     0xc2abd0: ldur            w1, [x0, #0xf]
    // 0xc2abd4: DecompressPointer r1
    //     0xc2abd4: add             x1, x1, HEAP, lsl #32
    // 0xc2abd8: r16 = Sentinel
    //     0xc2abd8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc2abdc: cmp             w1, w16
    // 0xc2abe0: b.eq            #0xc2ac18
    // 0xc2abe4: r0 = velocity()
    //     0xc2abe4: bl              #0xbf6f5c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::velocity
    // 0xc2abe8: ldur            x1, [fp, #-0x18]
    // 0xc2abec: ldur            x2, [fp, #-8]
    // 0xc2abf0: r0 = createInnerBallisticScrollActivity()
    //     0xc2abf0: bl              #0xbf94bc  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::createInnerBallisticScrollActivity
    // 0xc2abf4: ldur            x1, [fp, #-8]
    // 0xc2abf8: mov             x2, x0
    // 0xc2abfc: r0 = beginActivity()
    //     0xc2abfc: bl              #0x67956c  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::beginActivity
    // 0xc2ac00: r0 = Null
    //     0xc2ac00: mov             x0, NULL
    // 0xc2ac04: LeaveFrame
    //     0xc2ac04: mov             SP, fp
    //     0xc2ac08: ldp             fp, lr, [SP], #0x10
    // 0xc2ac0c: ret
    //     0xc2ac0c: ret             
    // 0xc2ac10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2ac10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2ac14: b               #0xc2ab88
    // 0xc2ac18: r9 = _controller
    //     0xc2ac18: ldr             x9, [PP, #0x7638]  ; [pp+0x7638] Field <BallisticScrollActivity._controller@324498029>: late (offset: 0x10)
    // 0xc2ac1c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc2ac1c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ applyMoveTo(/* No info */) {
    // ** addr: 0xdba354, size: 0x94
    // 0xdba354: EnterFrame
    //     0xdba354: stp             fp, lr, [SP, #-0x10]!
    //     0xdba358: mov             fp, SP
    // 0xdba35c: AllocStack(0x20)
    //     0xdba35c: sub             SP, SP, #0x20
    // 0xdba360: SetupParameters(_NestedInnerBallisticScrollActivity this /* r1 => r3, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x20 */)
    //     0xdba360: mov             x3, x1
    //     0xdba364: stur            x1, [fp, #-0x18]
    //     0xdba368: stur            d0, [fp, #-0x20]
    // 0xdba36c: CheckStackOverflow
    //     0xdba36c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdba370: cmp             SP, x16
    //     0xdba374: b.ls            #0xdba3e0
    // 0xdba378: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xdba378: ldur            w4, [x3, #0x17]
    // 0xdba37c: DecompressPointer r4
    //     0xdba37c: add             x4, x4, HEAP, lsl #32
    // 0xdba380: stur            x4, [fp, #-0x10]
    // 0xdba384: LoadField: r5 = r3->field_7
    //     0xdba384: ldur            w5, [x3, #7]
    // 0xdba388: DecompressPointer r5
    //     0xdba388: add             x5, x5, HEAP, lsl #32
    // 0xdba38c: mov             x0, x5
    // 0xdba390: stur            x5, [fp, #-8]
    // 0xdba394: r2 = Null
    //     0xdba394: mov             x2, NULL
    // 0xdba398: r1 = Null
    //     0xdba398: mov             x1, NULL
    // 0xdba39c: r4 = LoadClassIdInstr(r0)
    //     0xdba39c: ldur            x4, [x0, #-1]
    //     0xdba3a0: ubfx            x4, x4, #0xc, #0x14
    // 0xdba3a4: cmp             x4, #0xe46
    // 0xdba3a8: b.eq            #0xdba3bc
    // 0xdba3ac: r8 = _NestedScrollPosition
    //     0xdba3ac: ldr             x8, [PP, #0x75e0]  ; [pp+0x75e0] Type: _NestedScrollPosition
    // 0xdba3b0: r3 = Null
    //     0xdba3b0: add             x3, PP, #0x1d, lsl #12  ; [pp+0x1d778] Null
    //     0xdba3b4: ldr             x3, [x3, #0x778]
    // 0xdba3b8: r0 = DefaultTypeTest()
    //     0xdba3b8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xdba3bc: ldur            x1, [fp, #-0x10]
    // 0xdba3c0: ldur            d0, [fp, #-0x20]
    // 0xdba3c4: ldur            x2, [fp, #-8]
    // 0xdba3c8: r0 = nestOffset()
    //     0xdba3c8: bl              #0x67b10c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::nestOffset
    // 0xdba3cc: ldur            x1, [fp, #-0x18]
    // 0xdba3d0: r0 = applyMoveTo()
    //     0xdba3d0: bl              #0xdba578  ; [package:flutter/src/widgets/scroll_activity.dart] BallisticScrollActivity::applyMoveTo
    // 0xdba3d4: LeaveFrame
    //     0xdba3d4: mov             SP, fp
    //     0xdba3d8: ldp             fp, lr, [SP], #0x10
    // 0xdba3dc: ret
    //     0xdba3dc: ret             
    // 0xdba3e0: r0 = StackOverflowSharedWithFPURegs()
    //     0xdba3e0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xdba3e4: b               #0xdba378
  }
}

// class id: 2628, size: 0x28, field offset: 0x8
class _NestedScrollCoordinator extends Object
    implements ScrollActivityDelegate, ScrollHoldController {

  late _NestedScrollController _innerController; // offset: 0x1c
  late _NestedScrollController _outerController; // offset: 0x18

  _ animateTo(/* No info */) async {
    // ** addr: 0x678c70, size: 0x1cc
    // 0x678c70: EnterFrame
    //     0x678c70: stp             fp, lr, [SP, #-0x10]!
    //     0x678c74: mov             fp, SP
    // 0x678c78: AllocStack(0x48)
    //     0x678c78: sub             SP, SP, #0x48
    // 0x678c7c: SetupParameters(_NestedScrollCoordinator this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* d0 => d0, fp-0x38 */)
    //     0x678c7c: stur            NULL, [fp, #-8]
    //     0x678c80: stur            x1, [fp, #-0x10]
    //     0x678c84: stur            x2, [fp, #-0x18]
    //     0x678c88: stur            x3, [fp, #-0x20]
    //     0x678c8c: stur            d0, [fp, #-0x38]
    // 0x678c90: CheckStackOverflow
    //     0x678c90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x678c94: cmp             SP, x16
    //     0x678c98: b.ls            #0x678e14
    // 0x678c9c: r1 = 5
    //     0x678c9c: movz            x1, #0x5
    // 0x678ca0: r0 = AllocateContext()
    //     0x678ca0: bl              #0xec126c  ; AllocateContextStub
    // 0x678ca4: mov             x2, x0
    // 0x678ca8: ldur            x1, [fp, #-0x10]
    // 0x678cac: stur            x2, [fp, #-0x28]
    // 0x678cb0: StoreField: r2->field_f = r1
    //     0x678cb0: stur            w1, [x2, #0xf]
    // 0x678cb4: ldur            d0, [fp, #-0x38]
    // 0x678cb8: r0 = inline_Allocate_Double()
    //     0x678cb8: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x678cbc: add             x0, x0, #0x10
    //     0x678cc0: cmp             x3, x0
    //     0x678cc4: b.ls            #0x678e1c
    //     0x678cc8: str             x0, [THR, #0x50]  ; THR::top
    //     0x678ccc: sub             x0, x0, #0xf
    //     0x678cd0: movz            x3, #0xe15c
    //     0x678cd4: movk            x3, #0x3, lsl #16
    //     0x678cd8: stur            x3, [x0, #-1]
    // 0x678cdc: StoreField: r0->field_7 = d0
    //     0x678cdc: stur            d0, [x0, #7]
    // 0x678ce0: StoreField: r2->field_13 = r0
    //     0x678ce0: stur            w0, [x2, #0x13]
    // 0x678ce4: ldur            x0, [fp, #-0x18]
    // 0x678ce8: ArrayStore: r2[0] = r0  ; List_4
    //     0x678ce8: stur            w0, [x2, #0x17]
    // 0x678cec: ldur            x0, [fp, #-0x20]
    // 0x678cf0: StoreField: r2->field_1b = r0
    //     0x678cf0: stur            w0, [x2, #0x1b]
    // 0x678cf4: InitAsync() -> Future<void?>
    //     0x678cf4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x678cf8: bl              #0x661298  ; InitAsyncStub
    // 0x678cfc: ldur            x1, [fp, #-0x10]
    // 0x678d00: r0 = _outerPosition()
    //     0x678d00: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x678d04: stur            x0, [fp, #-0x20]
    // 0x678d08: cmp             w0, NULL
    // 0x678d0c: b.eq            #0x678e34
    // 0x678d10: ldur            x2, [fp, #-0x28]
    // 0x678d14: LoadField: r3 = r2->field_13
    //     0x678d14: ldur            w3, [x2, #0x13]
    // 0x678d18: DecompressPointer r3
    //     0x678d18: add             x3, x3, HEAP, lsl #32
    // 0x678d1c: ldur            x1, [fp, #-0x10]
    // 0x678d20: stur            x3, [fp, #-0x18]
    // 0x678d24: r0 = _outerPosition()
    //     0x678d24: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x678d28: cmp             w0, NULL
    // 0x678d2c: b.eq            #0x678e38
    // 0x678d30: ldur            x1, [fp, #-0x18]
    // 0x678d34: LoadField: d0 = r1->field_7
    //     0x678d34: ldur            d0, [x1, #7]
    // 0x678d38: ldur            x1, [fp, #-0x10]
    // 0x678d3c: mov             x2, x0
    // 0x678d40: r0 = nestOffset()
    //     0x678d40: bl              #0x67b10c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::nestOffset
    // 0x678d44: ldur            x0, [fp, #-0x28]
    // 0x678d48: LoadField: r2 = r0->field_1b
    //     0x678d48: ldur            w2, [x0, #0x1b]
    // 0x678d4c: DecompressPointer r2
    //     0x678d4c: add             x2, x2, HEAP, lsl #32
    // 0x678d50: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x678d50: ldur            w3, [x0, #0x17]
    // 0x678d54: DecompressPointer r3
    //     0x678d54: add             x3, x3, HEAP, lsl #32
    // 0x678d58: ldur            x1, [fp, #-0x20]
    // 0x678d5c: r0 = createDrivenScrollActivity()
    //     0x678d5c: bl              #0x67a828  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::createDrivenScrollActivity
    // 0x678d60: mov             x1, x0
    // 0x678d64: stur            x0, [fp, #-0x18]
    // 0x678d68: r0 = done()
    //     0x678d68: bl              #0x67a7f8  ; [package:flutter/src/widgets/scroll_activity.dart] DrivenScrollActivity::done
    // 0x678d6c: r1 = Null
    //     0x678d6c: mov             x1, NULL
    // 0x678d70: r2 = 2
    //     0x678d70: movz            x2, #0x2
    // 0x678d74: stur            x0, [fp, #-0x20]
    // 0x678d78: r0 = AllocateArray()
    //     0x678d78: bl              #0xec22fc  ; AllocateArrayStub
    // 0x678d7c: mov             x2, x0
    // 0x678d80: ldur            x0, [fp, #-0x20]
    // 0x678d84: stur            x2, [fp, #-0x30]
    // 0x678d88: StoreField: r2->field_f = r0
    //     0x678d88: stur            w0, [x2, #0xf]
    // 0x678d8c: r1 = <Future<void?>>
    //     0x678d8c: ldr             x1, [PP, #0x33f8]  ; [pp+0x33f8] TypeArguments: <Future<void?>>
    // 0x678d90: r0 = AllocateGrowableArray()
    //     0x678d90: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x678d94: mov             x3, x0
    // 0x678d98: ldur            x0, [fp, #-0x30]
    // 0x678d9c: stur            x3, [fp, #-0x20]
    // 0x678da0: StoreField: r3->field_f = r0
    //     0x678da0: stur            w0, [x3, #0xf]
    // 0x678da4: r0 = 2
    //     0x678da4: movz            x0, #0x2
    // 0x678da8: StoreField: r3->field_b = r0
    //     0x678da8: stur            w0, [x3, #0xb]
    // 0x678dac: mov             x0, x3
    // 0x678db0: ldur            x4, [fp, #-0x28]
    // 0x678db4: StoreField: r4->field_1f = r0
    //     0x678db4: stur            w0, [x4, #0x1f]
    //     0x678db8: ldurb           w16, [x4, #-1]
    //     0x678dbc: ldurb           w17, [x0, #-1]
    //     0x678dc0: and             x16, x17, x16, lsr #2
    //     0x678dc4: tst             x16, HEAP, lsr #32
    //     0x678dc8: b.eq            #0x678dd0
    //     0x678dcc: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x678dd0: mov             x2, x4
    // 0x678dd4: r1 = Function '<anonymous closure>':.
    //     0x678dd4: ldr             x1, [PP, #0x6ea8]  ; [pp+0x6ea8] AnonymousClosure: (0x67b4b4), in [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::animateTo (0x678c70)
    // 0x678dd8: r0 = AllocateClosure()
    //     0x678dd8: bl              #0xec1630  ; AllocateClosureStub
    // 0x678ddc: ldur            x1, [fp, #-0x10]
    // 0x678de0: ldur            x2, [fp, #-0x18]
    // 0x678de4: mov             x3, x0
    // 0x678de8: r0 = beginActivity()
    //     0x678de8: bl              #0x678e3c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::beginActivity
    // 0x678dec: r16 = <void?>
    //     0x678dec: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x678df0: ldur            lr, [fp, #-0x20]
    // 0x678df4: stp             lr, x16, [SP]
    // 0x678df8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x678df8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x678dfc: r0 = wait()
    //     0x678dfc: bl              #0x678258  ; [dart:async] Future::wait
    // 0x678e00: mov             x1, x0
    // 0x678e04: stur            x1, [fp, #-0x10]
    // 0x678e08: r0 = Await()
    //     0x678e08: bl              #0x661044  ; AwaitStub
    // 0x678e0c: r0 = Null
    //     0x678e0c: mov             x0, NULL
    // 0x678e10: r0 = ReturnAsyncNotFuture()
    //     0x678e10: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x678e14: r0 = StackOverflowSharedWithFPURegs()
    //     0x678e14: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x678e18: b               #0x678c9c
    // 0x678e1c: SaveReg d0
    //     0x678e1c: str             q0, [SP, #-0x10]!
    // 0x678e20: stp             x1, x2, [SP, #-0x10]!
    // 0x678e24: r0 = AllocateDouble()
    //     0x678e24: bl              #0xec2254  ; AllocateDoubleStub
    // 0x678e28: ldp             x1, x2, [SP], #0x10
    // 0x678e2c: RestoreReg d0
    //     0x678e2c: ldr             q0, [SP], #0x10
    // 0x678e30: b               #0x678cdc
    // 0x678e34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x678e34: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x678e38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x678e38: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ beginActivity(/* No info */) {
    // ** addr: 0x678e3c, size: 0x258
    // 0x678e3c: EnterFrame
    //     0x678e3c: stp             fp, lr, [SP, #-0x10]!
    //     0x678e40: mov             fp, SP
    // 0x678e44: AllocStack(0x60)
    //     0x678e44: sub             SP, SP, #0x60
    // 0x678e48: SetupParameters(_NestedScrollCoordinator this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x678e48: mov             x0, x3
    //     0x678e4c: stur            x3, [fp, #-0x18]
    //     0x678e50: mov             x3, x1
    //     0x678e54: stur            x1, [fp, #-8]
    //     0x678e58: stur            x2, [fp, #-0x10]
    // 0x678e5c: CheckStackOverflow
    //     0x678e5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x678e60: cmp             SP, x16
    //     0x678e64: b.ls            #0x679080
    // 0x678e68: mov             x1, x3
    // 0x678e6c: r0 = _outerPosition()
    //     0x678e6c: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x678e70: cmp             w0, NULL
    // 0x678e74: b.eq            #0x679088
    // 0x678e78: mov             x1, x0
    // 0x678e7c: ldur            x2, [fp, #-0x10]
    // 0x678e80: r0 = beginActivity()
    //     0x678e80: bl              #0x67956c  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::beginActivity
    // 0x678e84: ldur            x1, [fp, #-0x10]
    // 0x678e88: r0 = LoadClassIdInstr(r1)
    //     0x678e88: ldur            x0, [x1, #-1]
    //     0x678e8c: ubfx            x0, x0, #0xc, #0x14
    // 0x678e90: r0 = GDT[cid_x0 + -0xff6]()
    //     0x678e90: sub             lr, x0, #0xff6
    //     0x678e94: ldr             lr, [x21, lr, lsl #3]
    //     0x678e98: blr             lr
    // 0x678e9c: ldur            x1, [fp, #-8]
    // 0x678ea0: stur            x0, [fp, #-0x10]
    // 0x678ea4: r0 = _innerPositions()
    //     0x678ea4: bl              #0x67950c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_innerPositions
    // 0x678ea8: stur            x0, [fp, #-0x28]
    // 0x678eac: LoadField: r2 = r0->field_7
    //     0x678eac: ldur            w2, [x0, #7]
    // 0x678eb0: DecompressPointer r2
    //     0x678eb0: add             x2, x2, HEAP, lsl #32
    // 0x678eb4: stur            x2, [fp, #-0x20]
    // 0x678eb8: str             x0, [SP]
    // 0x678ebc: r0 = length()
    //     0x678ebc: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x678ec0: r1 = LoadInt32Instr(r0)
    //     0x678ec0: sbfx            x1, x0, #1, #0x1f
    //     0x678ec4: tbz             w0, #0, #0x678ecc
    //     0x678ec8: ldur            x1, [x0, #7]
    // 0x678ecc: ldur            x2, [fp, #-0x28]
    // 0x678ed0: stur            x1, [fp, #-0x40]
    // 0x678ed4: LoadField: r3 = r2->field_b
    //     0x678ed4: ldur            w3, [x2, #0xb]
    // 0x678ed8: DecompressPointer r3
    //     0x678ed8: add             x3, x3, HEAP, lsl #32
    // 0x678edc: stur            x3, [fp, #-0x38]
    // 0x678ee0: ldur            x5, [fp, #-0x10]
    // 0x678ee4: r4 = 0
    //     0x678ee4: movz            x4, #0
    // 0x678ee8: stur            x5, [fp, #-0x10]
    // 0x678eec: stur            x4, [fp, #-0x30]
    // 0x678ef0: CheckStackOverflow
    //     0x678ef0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x678ef4: cmp             SP, x16
    //     0x678ef8: b.ls            #0x67908c
    // 0x678efc: r0 = LoadClassIdInstr(r3)
    //     0x678efc: ldur            x0, [x3, #-1]
    //     0x678f00: ubfx            x0, x0, #0xc, #0x14
    // 0x678f04: str             x3, [SP]
    // 0x678f08: r0 = GDT[cid_x0 + 0xc834]()
    //     0x678f08: movz            x17, #0xc834
    //     0x678f0c: add             lr, x0, x17
    //     0x678f10: ldr             lr, [x21, lr, lsl #3]
    //     0x678f14: blr             lr
    // 0x678f18: r1 = LoadInt32Instr(r0)
    //     0x678f18: sbfx            x1, x0, #1, #0x1f
    //     0x678f1c: tbz             w0, #0, #0x678f24
    //     0x678f20: ldur            x1, [x0, #7]
    // 0x678f24: ldur            x2, [fp, #-0x40]
    // 0x678f28: cmp             x2, x1
    // 0x678f2c: b.ne            #0x679060
    // 0x678f30: ldur            x3, [fp, #-0x30]
    // 0x678f34: cmp             x3, x1
    // 0x678f38: b.ge            #0x679014
    // 0x678f3c: r0 = BoxInt64Instr(r3)
    //     0x678f3c: sbfiz           x0, x3, #1, #0x1f
    //     0x678f40: cmp             x3, x0, asr #1
    //     0x678f44: b.eq            #0x678f50
    //     0x678f48: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x678f4c: stur            x3, [x0, #7]
    // 0x678f50: ldur            x16, [fp, #-0x28]
    // 0x678f54: stp             x0, x16, [SP]
    // 0x678f58: r0 = []()
    //     0x678f58: bl              #0x66e408  ; [dart:_internal] _CastListBase::[]
    // 0x678f5c: mov             x3, x0
    // 0x678f60: ldur            x0, [fp, #-0x30]
    // 0x678f64: stur            x3, [fp, #-0x50]
    // 0x678f68: add             x4, x0, #1
    // 0x678f6c: stur            x4, [fp, #-0x48]
    // 0x678f70: cmp             w3, NULL
    // 0x678f74: b.ne            #0x678fa4
    // 0x678f78: mov             x0, x3
    // 0x678f7c: ldur            x2, [fp, #-0x20]
    // 0x678f80: r1 = Null
    //     0x678f80: mov             x1, NULL
    // 0x678f84: cmp             w2, NULL
    // 0x678f88: b.eq            #0x678fa4
    // 0x678f8c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x678f8c: ldur            w4, [x2, #0x17]
    // 0x678f90: DecompressPointer r4
    //     0x678f90: add             x4, x4, HEAP, lsl #32
    // 0x678f94: r8 = X0
    //     0x678f94: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x678f98: LoadField: r9 = r4->field_7
    //     0x678f98: ldur            x9, [x4, #7]
    // 0x678f9c: r3 = Null
    //     0x678f9c: ldr             x3, [PP, #0x6ec8]  ; [pp+0x6ec8] Null
    // 0x678fa0: blr             x9
    // 0x678fa4: ldur            x1, [fp, #-0x10]
    // 0x678fa8: ldur            x16, [fp, #-0x18]
    // 0x678fac: ldur            lr, [fp, #-0x50]
    // 0x678fb0: stp             lr, x16, [SP]
    // 0x678fb4: ldur            x0, [fp, #-0x18]
    // 0x678fb8: ClosureCall
    //     0x678fb8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x678fbc: ldur            x2, [x0, #0x1f]
    //     0x678fc0: blr             x2
    // 0x678fc4: ldur            x1, [fp, #-0x50]
    // 0x678fc8: mov             x2, x0
    // 0x678fcc: stur            x0, [fp, #-0x50]
    // 0x678fd0: r0 = beginActivity()
    //     0x678fd0: bl              #0x67956c  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::beginActivity
    // 0x678fd4: ldur            x0, [fp, #-0x10]
    // 0x678fd8: tbnz            w0, #4, #0x678ffc
    // 0x678fdc: ldur            x1, [fp, #-0x50]
    // 0x678fe0: r0 = LoadClassIdInstr(r1)
    //     0x678fe0: ldur            x0, [x1, #-1]
    //     0x678fe4: ubfx            x0, x0, #0xc, #0x14
    // 0x678fe8: r0 = GDT[cid_x0 + -0xff6]()
    //     0x678fe8: sub             lr, x0, #0xff6
    //     0x678fec: ldr             lr, [x21, lr, lsl #3]
    //     0x678ff0: blr             lr
    // 0x678ff4: mov             x5, x0
    // 0x678ff8: b               #0x679000
    // 0x678ffc: r5 = false
    //     0x678ffc: add             x5, NULL, #0x30  ; false
    // 0x679000: ldur            x4, [fp, #-0x48]
    // 0x679004: ldur            x2, [fp, #-0x28]
    // 0x679008: ldur            x3, [fp, #-0x38]
    // 0x67900c: ldur            x1, [fp, #-0x40]
    // 0x679010: b               #0x678ee8
    // 0x679014: ldur            x2, [fp, #-8]
    // 0x679018: ldur            x0, [fp, #-0x10]
    // 0x67901c: LoadField: r1 = r2->field_23
    //     0x67901c: ldur            w1, [x2, #0x23]
    // 0x679020: DecompressPointer r1
    //     0x679020: add             x1, x1, HEAP, lsl #32
    // 0x679024: cmp             w1, NULL
    // 0x679028: b.ne            #0x679034
    // 0x67902c: mov             x1, x2
    // 0x679030: b               #0x679040
    // 0x679034: r0 = dispose()
    //     0x679034: bl              #0x6794b4  ; [package:flutter/src/widgets/scroll_activity.dart] ScrollDragController::dispose
    // 0x679038: ldur            x1, [fp, #-8]
    // 0x67903c: ldur            x0, [fp, #-0x10]
    // 0x679040: StoreField: r1->field_23 = rNULL
    //     0x679040: stur            NULL, [x1, #0x23]
    // 0x679044: tbz             w0, #4, #0x679050
    // 0x679048: r2 = Instance_ScrollDirection
    //     0x679048: ldr             x2, [PP, #0x6ed8]  ; [pp+0x6ed8] Obj!ScrollDirection@e352e1
    // 0x67904c: r0 = updateUserScrollDirection()
    //     0x67904c: bl              #0x679094  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::updateUserScrollDirection
    // 0x679050: r0 = Null
    //     0x679050: mov             x0, NULL
    // 0x679054: LeaveFrame
    //     0x679054: mov             SP, fp
    //     0x679058: ldp             fp, lr, [SP], #0x10
    // 0x67905c: ret
    //     0x67905c: ret             
    // 0x679060: ldur            x0, [fp, #-0x28]
    // 0x679064: r0 = ConcurrentModificationError()
    //     0x679064: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x679068: mov             x1, x0
    // 0x67906c: ldur            x0, [fp, #-0x28]
    // 0x679070: StoreField: r1->field_b = r0
    //     0x679070: stur            w0, [x1, #0xb]
    // 0x679074: mov             x0, x1
    // 0x679078: r0 = Throw()
    //     0x679078: bl              #0xec04b8  ; ThrowStub
    // 0x67907c: brk             #0
    // 0x679080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x679080: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x679084: b               #0x678e68
    // 0x679088: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x679088: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67908c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67908c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x679090: b               #0x678efc
  }
  _ updateUserScrollDirection(/* No info */) {
    // ** addr: 0x679094, size: 0x2b8
    // 0x679094: EnterFrame
    //     0x679094: stp             fp, lr, [SP, #-0x10]!
    //     0x679098: mov             fp, SP
    // 0x67909c: AllocStack(0x60)
    //     0x67909c: sub             SP, SP, #0x60
    // 0x6790a0: SetupParameters(_NestedScrollCoordinator this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0 */)
    //     0x6790a0: mov             x0, x2
    //     0x6790a4: mov             x2, x1
    //     0x6790a8: stur            x1, [fp, #-8]
    // 0x6790ac: CheckStackOverflow
    //     0x6790ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6790b0: cmp             SP, x16
    //     0x6790b4: b.ls            #0x67932c
    // 0x6790b8: LoadField: r1 = r2->field_1f
    //     0x6790b8: ldur            w1, [x2, #0x1f]
    // 0x6790bc: DecompressPointer r1
    //     0x6790bc: add             x1, x1, HEAP, lsl #32
    // 0x6790c0: cmp             w1, w0
    // 0x6790c4: b.ne            #0x6790d8
    // 0x6790c8: r0 = Null
    //     0x6790c8: mov             x0, NULL
    // 0x6790cc: LeaveFrame
    //     0x6790cc: mov             SP, fp
    //     0x6790d0: ldp             fp, lr, [SP], #0x10
    // 0x6790d4: ret
    //     0x6790d4: ret             
    // 0x6790d8: StoreField: r2->field_1f = r0
    //     0x6790d8: stur            w0, [x2, #0x1f]
    //     0x6790dc: ldurb           w16, [x2, #-1]
    //     0x6790e0: ldurb           w17, [x0, #-1]
    //     0x6790e4: and             x16, x17, x16, lsr #2
    //     0x6790e8: tst             x16, HEAP, lsr #32
    //     0x6790ec: b.eq            #0x6790f4
    //     0x6790f0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6790f4: mov             x1, x2
    // 0x6790f8: r0 = _outerPosition()
    //     0x6790f8: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x6790fc: cmp             w0, NULL
    // 0x679100: b.eq            #0x679334
    // 0x679104: mov             x1, x0
    // 0x679108: r0 = didUpdateScrollDirection()
    //     0x679108: bl              #0x6793f8  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didUpdateScrollDirection
    // 0x67910c: ldur            x0, [fp, #-8]
    // 0x679110: LoadField: r1 = r0->field_1b
    //     0x679110: ldur            w1, [x0, #0x1b]
    // 0x679114: DecompressPointer r1
    //     0x679114: add             x1, x1, HEAP, lsl #32
    // 0x679118: r16 = Sentinel
    //     0x679118: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x67911c: cmp             w1, w16
    // 0x679120: b.eq            #0x679338
    // 0x679124: r0 = nestedPositions()
    //     0x679124: bl              #0x6793b4  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollController::nestedPositions
    // 0x679128: mov             x1, x0
    // 0x67912c: stur            x1, [fp, #-0x18]
    // 0x679130: LoadField: r2 = r1->field_7
    //     0x679130: ldur            w2, [x1, #7]
    // 0x679134: DecompressPointer r2
    //     0x679134: add             x2, x2, HEAP, lsl #32
    // 0x679138: stur            x2, [fp, #-0x10]
    // 0x67913c: LoadField: r3 = r1->field_b
    //     0x67913c: ldur            w3, [x1, #0xb]
    // 0x679140: DecompressPointer r3
    //     0x679140: add             x3, x3, HEAP, lsl #32
    // 0x679144: stur            x3, [fp, #-8]
    // 0x679148: r0 = LoadClassIdInstr(r3)
    //     0x679148: ldur            x0, [x3, #-1]
    //     0x67914c: ubfx            x0, x0, #0xc, #0x14
    // 0x679150: str             x3, [SP]
    // 0x679154: r0 = GDT[cid_x0 + 0xc834]()
    //     0x679154: movz            x17, #0xc834
    //     0x679158: add             lr, x0, x17
    //     0x67915c: ldr             lr, [x21, lr, lsl #3]
    //     0x679160: blr             lr
    // 0x679164: r1 = LoadInt32Instr(r0)
    //     0x679164: sbfx            x1, x0, #1, #0x1f
    //     0x679168: tbz             w0, #0, #0x679170
    //     0x67916c: ldur            x1, [x0, #7]
    // 0x679170: stur            x1, [fp, #-0x28]
    // 0x679174: r3 = 0
    //     0x679174: movz            x3, #0
    // 0x679178: ldur            x2, [fp, #-8]
    // 0x67917c: stur            x3, [fp, #-0x20]
    // 0x679180: CheckStackOverflow
    //     0x679180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x679184: cmp             SP, x16
    //     0x679188: b.ls            #0x679340
    // 0x67918c: r0 = LoadClassIdInstr(r2)
    //     0x67918c: ldur            x0, [x2, #-1]
    //     0x679190: ubfx            x0, x0, #0xc, #0x14
    // 0x679194: str             x2, [SP]
    // 0x679198: r0 = GDT[cid_x0 + 0xc834]()
    //     0x679198: movz            x17, #0xc834
    //     0x67919c: add             lr, x0, x17
    //     0x6791a0: ldr             lr, [x21, lr, lsl #3]
    //     0x6791a4: blr             lr
    // 0x6791a8: r1 = LoadInt32Instr(r0)
    //     0x6791a8: sbfx            x1, x0, #1, #0x1f
    //     0x6791ac: tbz             w0, #0, #0x6791b4
    //     0x6791b0: ldur            x1, [x0, #7]
    // 0x6791b4: ldur            x2, [fp, #-0x28]
    // 0x6791b8: cmp             x2, x1
    // 0x6791bc: b.ne            #0x67930c
    // 0x6791c0: ldur            x3, [fp, #-0x20]
    // 0x6791c4: cmp             x3, x1
    // 0x6791c8: b.ge            #0x6792fc
    // 0x6791cc: ldur            x4, [fp, #-8]
    // 0x6791d0: r0 = BoxInt64Instr(r3)
    //     0x6791d0: sbfiz           x0, x3, #1, #0x1f
    //     0x6791d4: cmp             x3, x0, asr #1
    //     0x6791d8: b.eq            #0x6791e4
    //     0x6791dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6791e0: stur            x3, [x0, #7]
    // 0x6791e4: r1 = LoadClassIdInstr(r4)
    //     0x6791e4: ldur            x1, [x4, #-1]
    //     0x6791e8: ubfx            x1, x1, #0xc, #0x14
    // 0x6791ec: stp             x0, x4, [SP]
    // 0x6791f0: mov             x0, x1
    // 0x6791f4: r0 = GDT[cid_x0 + 0x13037]()
    //     0x6791f4: movz            x17, #0x3037
    //     0x6791f8: movk            x17, #0x1, lsl #16
    //     0x6791fc: add             lr, x0, x17
    //     0x679200: ldr             lr, [x21, lr, lsl #3]
    //     0x679204: blr             lr
    // 0x679208: ldur            x2, [fp, #-0x10]
    // 0x67920c: mov             x3, x0
    // 0x679210: r1 = Null
    //     0x679210: mov             x1, NULL
    // 0x679214: stur            x3, [fp, #-0x30]
    // 0x679218: cmp             w2, NULL
    // 0x67921c: b.eq            #0x679238
    // 0x679220: LoadField: r4 = r2->field_1f
    //     0x679220: ldur            w4, [x2, #0x1f]
    // 0x679224: DecompressPointer r4
    //     0x679224: add             x4, x4, HEAP, lsl #32
    // 0x679228: r8 = C1X1
    //     0x679228: ldr             x8, [PP, #0x6ee0]  ; [pp+0x6ee0] TypeParameter: C1X1
    // 0x67922c: LoadField: r9 = r4->field_7
    //     0x67922c: ldur            x9, [x4, #7]
    // 0x679230: r3 = Null
    //     0x679230: ldr             x3, [PP, #0x6ee8]  ; [pp+0x6ee8] Null
    // 0x679234: blr             x9
    // 0x679238: ldur            x0, [fp, #-0x20]
    // 0x67923c: add             x3, x0, #1
    // 0x679240: ldur            x4, [fp, #-0x30]
    // 0x679244: stur            x3, [fp, #-0x38]
    // 0x679248: cmp             w4, NULL
    // 0x67924c: b.ne            #0x67927c
    // 0x679250: mov             x0, x4
    // 0x679254: ldur            x2, [fp, #-0x10]
    // 0x679258: r1 = Null
    //     0x679258: mov             x1, NULL
    // 0x67925c: cmp             w2, NULL
    // 0x679260: b.eq            #0x67927c
    // 0x679264: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x679264: ldur            w4, [x2, #0x17]
    // 0x679268: DecompressPointer r4
    //     0x679268: add             x4, x4, HEAP, lsl #32
    // 0x67926c: r8 = X0
    //     0x67926c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x679270: LoadField: r9 = r4->field_7
    //     0x679270: ldur            x9, [x4, #7]
    // 0x679274: r3 = Null
    //     0x679274: ldr             x3, [PP, #0x6ef8]  ; [pp+0x6ef8] Null
    // 0x679278: blr             x9
    // 0x67927c: ldur            x0, [fp, #-0x30]
    // 0x679280: mov             x1, x0
    // 0x679284: r0 = copyWith()
    //     0x679284: bl              #0xd891b8  ; [package:flutter/src/widgets/scroll_position.dart] _ScrollPosition&ViewportOffset&ScrollMetrics::copyWith
    // 0x679288: mov             x2, x0
    // 0x67928c: ldur            x0, [fp, #-0x30]
    // 0x679290: stur            x2, [fp, #-0x48]
    // 0x679294: LoadField: r3 = r0->field_27
    //     0x679294: ldur            w3, [x0, #0x27]
    // 0x679298: DecompressPointer r3
    //     0x679298: add             x3, x3, HEAP, lsl #32
    // 0x67929c: mov             x1, x3
    // 0x6792a0: stur            x3, [fp, #-0x40]
    // 0x6792a4: r0 = notificationContext()
    //     0x6792a4: bl              #0x67937c  ; [package:flutter/src/widgets/scrollable.dart] ScrollableState::notificationContext
    // 0x6792a8: stur            x0, [fp, #-0x30]
    // 0x6792ac: cmp             w0, NULL
    // 0x6792b0: b.eq            #0x679348
    // 0x6792b4: r0 = UserScrollNotification()
    //     0x6792b4: bl              #0x679370  ; AllocateUserScrollNotificationStub -> UserScrollNotification (size=0x18)
    // 0x6792b8: mov             x2, x0
    // 0x6792bc: ldur            x0, [fp, #-0x48]
    // 0x6792c0: stur            x2, [fp, #-0x50]
    // 0x6792c4: StoreField: r2->field_f = r0
    //     0x6792c4: stur            w0, [x2, #0xf]
    // 0x6792c8: ldur            x0, [fp, #-0x30]
    // 0x6792cc: StoreField: r2->field_13 = r0
    //     0x6792cc: stur            w0, [x2, #0x13]
    // 0x6792d0: StoreField: r2->field_7 = rZR
    //     0x6792d0: stur            xzr, [x2, #7]
    // 0x6792d4: ldur            x0, [fp, #-0x40]
    // 0x6792d8: LoadField: r1 = r0->field_4b
    //     0x6792d8: ldur            w1, [x0, #0x4b]
    // 0x6792dc: DecompressPointer r1
    //     0x6792dc: add             x1, x1, HEAP, lsl #32
    // 0x6792e0: r0 = _currentElement()
    //     0x6792e0: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x6792e4: ldur            x1, [fp, #-0x50]
    // 0x6792e8: mov             x2, x0
    // 0x6792ec: r0 = dispatch()
    //     0x6792ec: bl              #0x650038  ; [package:flutter/src/widgets/notification_listener.dart] Notification::dispatch
    // 0x6792f0: ldur            x3, [fp, #-0x38]
    // 0x6792f4: ldur            x1, [fp, #-0x28]
    // 0x6792f8: b               #0x679178
    // 0x6792fc: r0 = Null
    //     0x6792fc: mov             x0, NULL
    // 0x679300: LeaveFrame
    //     0x679300: mov             SP, fp
    //     0x679304: ldp             fp, lr, [SP], #0x10
    // 0x679308: ret
    //     0x679308: ret             
    // 0x67930c: ldur            x0, [fp, #-0x18]
    // 0x679310: r0 = ConcurrentModificationError()
    //     0x679310: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x679314: mov             x1, x0
    // 0x679318: ldur            x0, [fp, #-0x18]
    // 0x67931c: StoreField: r1->field_b = r0
    //     0x67931c: stur            w0, [x1, #0xb]
    // 0x679320: mov             x0, x1
    // 0x679324: r0 = Throw()
    //     0x679324: bl              #0xec04b8  ; ThrowStub
    // 0x679328: brk             #0
    // 0x67932c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67932c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x679330: b               #0x6790b8
    // 0x679334: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x679334: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x679338: r9 = _innerController
    //     0x679338: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0x67933c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x67933c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x679340: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x679340: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x679344: b               #0x67918c
    // 0x679348: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x679348: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _innerPositions(/* No info */) {
    // ** addr: 0x67950c, size: 0x60
    // 0x67950c: EnterFrame
    //     0x67950c: stp             fp, lr, [SP, #-0x10]!
    //     0x679510: mov             fp, SP
    // 0x679514: AllocStack(0x10)
    //     0x679514: sub             SP, SP, #0x10
    // 0x679518: CheckStackOverflow
    //     0x679518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67951c: cmp             SP, x16
    //     0x679520: b.ls            #0x67955c
    // 0x679524: LoadField: r0 = r1->field_1b
    //     0x679524: ldur            w0, [x1, #0x1b]
    // 0x679528: DecompressPointer r0
    //     0x679528: add             x0, x0, HEAP, lsl #32
    // 0x67952c: r16 = Sentinel
    //     0x67952c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x679530: cmp             w0, w16
    // 0x679534: b.eq            #0x679564
    // 0x679538: LoadField: r1 = r0->field_3b
    //     0x679538: ldur            w1, [x0, #0x3b]
    // 0x67953c: DecompressPointer r1
    //     0x67953c: add             x1, x1, HEAP, lsl #32
    // 0x679540: r16 = <_NestedScrollPosition>
    //     0x679540: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0x679544: stp             x1, x16, [SP]
    // 0x679548: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x679548: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x67954c: r0 = cast()
    //     0x67954c: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0x679550: LeaveFrame
    //     0x679550: mov             SP, fp
    //     0x679554: ldp             fp, lr, [SP], #0x10
    // 0x679558: ret
    //     0x679558: ret             
    // 0x67955c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67955c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x679560: b               #0x679524
    // 0x679564: r9 = _innerController
    //     0x679564: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0x679568: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x679568: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ nestOffset(/* No info */) {
    // ** addr: 0x67b10c, size: 0x244
    // 0x67b10c: EnterFrame
    //     0x67b10c: stp             fp, lr, [SP, #-0x10]!
    //     0x67b110: mov             fp, SP
    // 0x67b114: AllocStack(0x20)
    //     0x67b114: sub             SP, SP, #0x20
    // 0x67b118: SetupParameters(_NestedScrollCoordinator this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x20 */)
    //     0x67b118: mov             x0, x1
    //     0x67b11c: stur            x1, [fp, #-8]
    //     0x67b120: stur            x2, [fp, #-0x10]
    //     0x67b124: stur            d0, [fp, #-0x20]
    // 0x67b128: CheckStackOverflow
    //     0x67b128: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67b12c: cmp             SP, x16
    //     0x67b130: b.ls            #0x67b30c
    // 0x67b134: mov             x1, x0
    // 0x67b138: r0 = _outerPosition()
    //     0x67b138: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x67b13c: mov             x1, x0
    // 0x67b140: ldur            x0, [fp, #-0x10]
    // 0x67b144: cmp             w0, w1
    // 0x67b148: b.ne            #0x67b1cc
    // 0x67b14c: ldur            d0, [fp, #-0x20]
    // 0x67b150: ldur            x1, [fp, #-8]
    // 0x67b154: r0 = _outerPosition()
    //     0x67b154: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x67b158: cmp             w0, NULL
    // 0x67b15c: b.eq            #0x67b314
    // 0x67b160: LoadField: r2 = r0->field_2f
    //     0x67b160: ldur            w2, [x0, #0x2f]
    // 0x67b164: DecompressPointer r2
    //     0x67b164: add             x2, x2, HEAP, lsl #32
    // 0x67b168: stur            x2, [fp, #-0x18]
    // 0x67b16c: cmp             w2, NULL
    // 0x67b170: b.eq            #0x67b318
    // 0x67b174: ldur            x1, [fp, #-8]
    // 0x67b178: r0 = _outerPosition()
    //     0x67b178: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x67b17c: cmp             w0, NULL
    // 0x67b180: b.eq            #0x67b31c
    // 0x67b184: LoadField: r1 = r0->field_33
    //     0x67b184: ldur            w1, [x0, #0x33]
    // 0x67b188: DecompressPointer r1
    //     0x67b188: add             x1, x1, HEAP, lsl #32
    // 0x67b18c: cmp             w1, NULL
    // 0x67b190: b.eq            #0x67b320
    // 0x67b194: ldur            x0, [fp, #-0x18]
    // 0x67b198: LoadField: d0 = r0->field_7
    //     0x67b198: ldur            d0, [x0, #7]
    // 0x67b19c: ldur            d1, [fp, #-0x20]
    // 0x67b1a0: fcmp            d0, d1
    // 0x67b1a4: b.gt            #0x67b1c0
    // 0x67b1a8: LoadField: d0 = r1->field_7
    //     0x67b1a8: ldur            d0, [x1, #7]
    // 0x67b1ac: fcmp            d1, d0
    // 0x67b1b0: b.gt            #0x67b1c0
    // 0x67b1b4: fcmp            d1, d1
    // 0x67b1b8: b.vs            #0x67b1c0
    // 0x67b1bc: mov             v0.16b, v1.16b
    // 0x67b1c0: LeaveFrame
    //     0x67b1c0: mov             SP, fp
    //     0x67b1c4: ldp             fp, lr, [SP], #0x10
    // 0x67b1c8: ret
    //     0x67b1c8: ret             
    // 0x67b1cc: ldur            d1, [fp, #-0x20]
    // 0x67b1d0: ldur            x1, [fp, #-8]
    // 0x67b1d4: r0 = _outerPosition()
    //     0x67b1d4: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x67b1d8: cmp             w0, NULL
    // 0x67b1dc: b.eq            #0x67b324
    // 0x67b1e0: LoadField: r1 = r0->field_2f
    //     0x67b1e0: ldur            w1, [x0, #0x2f]
    // 0x67b1e4: DecompressPointer r1
    //     0x67b1e4: add             x1, x1, HEAP, lsl #32
    // 0x67b1e8: cmp             w1, NULL
    // 0x67b1ec: b.eq            #0x67b328
    // 0x67b1f0: LoadField: d0 = r1->field_7
    //     0x67b1f0: ldur            d0, [x1, #7]
    // 0x67b1f4: ldur            d1, [fp, #-0x20]
    // 0x67b1f8: fcmp            d0, d1
    // 0x67b1fc: b.le            #0x67b25c
    // 0x67b200: ldur            x0, [fp, #-0x10]
    // 0x67b204: ldur            x1, [fp, #-8]
    // 0x67b208: r0 = _outerPosition()
    //     0x67b208: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x67b20c: cmp             w0, NULL
    // 0x67b210: b.eq            #0x67b32c
    // 0x67b214: LoadField: r1 = r0->field_2f
    //     0x67b214: ldur            w1, [x0, #0x2f]
    // 0x67b218: DecompressPointer r1
    //     0x67b218: add             x1, x1, HEAP, lsl #32
    // 0x67b21c: cmp             w1, NULL
    // 0x67b220: b.eq            #0x67b330
    // 0x67b224: LoadField: d0 = r1->field_7
    //     0x67b224: ldur            d0, [x1, #7]
    // 0x67b228: ldur            d1, [fp, #-0x20]
    // 0x67b22c: fsub            d2, d1, d0
    // 0x67b230: ldur            x0, [fp, #-0x10]
    // 0x67b234: LoadField: r1 = r0->field_2f
    //     0x67b234: ldur            w1, [x0, #0x2f]
    // 0x67b238: DecompressPointer r1
    //     0x67b238: add             x1, x1, HEAP, lsl #32
    // 0x67b23c: cmp             w1, NULL
    // 0x67b240: b.eq            #0x67b334
    // 0x67b244: LoadField: d0 = r1->field_7
    //     0x67b244: ldur            d0, [x1, #7]
    // 0x67b248: fadd            d1, d2, d0
    // 0x67b24c: mov             v0.16b, v1.16b
    // 0x67b250: LeaveFrame
    //     0x67b250: mov             SP, fp
    //     0x67b254: ldp             fp, lr, [SP], #0x10
    // 0x67b258: ret
    //     0x67b258: ret             
    // 0x67b25c: ldur            x0, [fp, #-0x10]
    // 0x67b260: ldur            x1, [fp, #-8]
    // 0x67b264: r0 = _outerPosition()
    //     0x67b264: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x67b268: cmp             w0, NULL
    // 0x67b26c: b.eq            #0x67b338
    // 0x67b270: LoadField: r1 = r0->field_33
    //     0x67b270: ldur            w1, [x0, #0x33]
    // 0x67b274: DecompressPointer r1
    //     0x67b274: add             x1, x1, HEAP, lsl #32
    // 0x67b278: cmp             w1, NULL
    // 0x67b27c: b.eq            #0x67b33c
    // 0x67b280: LoadField: d0 = r1->field_7
    //     0x67b280: ldur            d0, [x1, #7]
    // 0x67b284: ldur            d1, [fp, #-0x20]
    // 0x67b288: fcmp            d1, d0
    // 0x67b28c: b.le            #0x67b2e8
    // 0x67b290: ldur            x0, [fp, #-0x10]
    // 0x67b294: ldur            x1, [fp, #-8]
    // 0x67b298: r0 = _outerPosition()
    //     0x67b298: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x67b29c: cmp             w0, NULL
    // 0x67b2a0: b.eq            #0x67b340
    // 0x67b2a4: LoadField: r1 = r0->field_33
    //     0x67b2a4: ldur            w1, [x0, #0x33]
    // 0x67b2a8: DecompressPointer r1
    //     0x67b2a8: add             x1, x1, HEAP, lsl #32
    // 0x67b2ac: cmp             w1, NULL
    // 0x67b2b0: b.eq            #0x67b344
    // 0x67b2b4: LoadField: d1 = r1->field_7
    //     0x67b2b4: ldur            d1, [x1, #7]
    // 0x67b2b8: ldur            d2, [fp, #-0x20]
    // 0x67b2bc: fsub            d3, d2, d1
    // 0x67b2c0: ldur            x0, [fp, #-0x10]
    // 0x67b2c4: LoadField: r1 = r0->field_2f
    //     0x67b2c4: ldur            w1, [x0, #0x2f]
    // 0x67b2c8: DecompressPointer r1
    //     0x67b2c8: add             x1, x1, HEAP, lsl #32
    // 0x67b2cc: cmp             w1, NULL
    // 0x67b2d0: b.eq            #0x67b348
    // 0x67b2d4: LoadField: d1 = r1->field_7
    //     0x67b2d4: ldur            d1, [x1, #7]
    // 0x67b2d8: fadd            d0, d3, d1
    // 0x67b2dc: LeaveFrame
    //     0x67b2dc: mov             SP, fp
    //     0x67b2e0: ldp             fp, lr, [SP], #0x10
    // 0x67b2e4: ret
    //     0x67b2e4: ret             
    // 0x67b2e8: ldur            x0, [fp, #-0x10]
    // 0x67b2ec: LoadField: r1 = r0->field_2f
    //     0x67b2ec: ldur            w1, [x0, #0x2f]
    // 0x67b2f0: DecompressPointer r1
    //     0x67b2f0: add             x1, x1, HEAP, lsl #32
    // 0x67b2f4: cmp             w1, NULL
    // 0x67b2f8: b.eq            #0x67b34c
    // 0x67b2fc: LoadField: d0 = r1->field_7
    //     0x67b2fc: ldur            d0, [x1, #7]
    // 0x67b300: LeaveFrame
    //     0x67b300: mov             SP, fp
    //     0x67b304: ldp             fp, lr, [SP], #0x10
    // 0x67b308: ret
    //     0x67b308: ret             
    // 0x67b30c: r0 = StackOverflowSharedWithFPURegs()
    //     0x67b30c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x67b310: b               #0x67b134
    // 0x67b314: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b314: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b318: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b318: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b31c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b31c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b320: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b320: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b324: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b324: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b328: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b328: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b32c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b32c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b330: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b330: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b334: r0 = NullCastErrorSharedWithFPURegs()
    //     0x67b334: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x67b338: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b338: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b33c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b33c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b340: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b340: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b344: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b344: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b348: r0 = NullCastErrorSharedWithFPURegs()
    //     0x67b348: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x67b34c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b34c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _outerPosition(/* No info */) {
    // ** addr: 0x67b350, size: 0x80
    // 0x67b350: EnterFrame
    //     0x67b350: stp             fp, lr, [SP, #-0x10]!
    //     0x67b354: mov             fp, SP
    // 0x67b358: AllocStack(0x10)
    //     0x67b358: sub             SP, SP, #0x10
    // 0x67b35c: CheckStackOverflow
    //     0x67b35c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67b360: cmp             SP, x16
    //     0x67b364: b.ls            #0x67b3c0
    // 0x67b368: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x67b368: ldur            w0, [x1, #0x17]
    // 0x67b36c: DecompressPointer r0
    //     0x67b36c: add             x0, x0, HEAP, lsl #32
    // 0x67b370: r16 = Sentinel
    //     0x67b370: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x67b374: cmp             w0, w16
    // 0x67b378: b.eq            #0x67b3c8
    // 0x67b37c: LoadField: r1 = r0->field_3b
    //     0x67b37c: ldur            w1, [x0, #0x3b]
    // 0x67b380: DecompressPointer r1
    //     0x67b380: add             x1, x1, HEAP, lsl #32
    // 0x67b384: LoadField: r0 = r1->field_b
    //     0x67b384: ldur            w0, [x1, #0xb]
    // 0x67b388: cbnz            w0, #0x67b39c
    // 0x67b38c: r0 = Null
    //     0x67b38c: mov             x0, NULL
    // 0x67b390: LeaveFrame
    //     0x67b390: mov             SP, fp
    //     0x67b394: ldp             fp, lr, [SP], #0x10
    // 0x67b398: ret
    //     0x67b398: ret             
    // 0x67b39c: r16 = <_NestedScrollPosition>
    //     0x67b39c: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0x67b3a0: stp             x1, x16, [SP]
    // 0x67b3a4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x67b3a4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x67b3a8: r0 = cast()
    //     0x67b3a8: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0x67b3ac: mov             x1, x0
    // 0x67b3b0: r0 = single()
    //     0x67b3b0: bl              #0x67b3d0  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::single
    // 0x67b3b4: LeaveFrame
    //     0x67b3b4: mov             SP, fp
    //     0x67b3b8: ldp             fp, lr, [SP], #0x10
    // 0x67b3bc: ret
    //     0x67b3bc: ret             
    // 0x67b3c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67b3c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67b3c4: b               #0x67b368
    // 0x67b3c8: r9 = _outerController
    //     0x67b3c8: ldr             x9, [PP, #0x7018]  ; [pp+0x7018] Field <_NestedScrollCoordinator@303016527._outerController@303016527>: late (offset: 0x18)
    // 0x67b3cc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x67b3cc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] DrivenScrollActivity <anonymous closure>(dynamic, _NestedScrollPosition) {
    // ** addr: 0x67b4b4, size: 0x160
    // 0x67b4b4: EnterFrame
    //     0x67b4b4: stp             fp, lr, [SP, #-0x10]!
    //     0x67b4b8: mov             fp, SP
    // 0x67b4bc: AllocStack(0x20)
    //     0x67b4bc: sub             SP, SP, #0x20
    // 0x67b4c0: SetupParameters()
    //     0x67b4c0: ldr             x0, [fp, #0x18]
    //     0x67b4c4: ldur            w3, [x0, #0x17]
    //     0x67b4c8: add             x3, x3, HEAP, lsl #32
    //     0x67b4cc: stur            x3, [fp, #-8]
    // 0x67b4d0: CheckStackOverflow
    //     0x67b4d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67b4d4: cmp             SP, x16
    //     0x67b4d8: b.ls            #0x67b604
    // 0x67b4dc: LoadField: r1 = r3->field_f
    //     0x67b4dc: ldur            w1, [x3, #0xf]
    // 0x67b4e0: DecompressPointer r1
    //     0x67b4e0: add             x1, x1, HEAP, lsl #32
    // 0x67b4e4: LoadField: r0 = r3->field_13
    //     0x67b4e4: ldur            w0, [x3, #0x13]
    // 0x67b4e8: DecompressPointer r0
    //     0x67b4e8: add             x0, x0, HEAP, lsl #32
    // 0x67b4ec: LoadField: d0 = r0->field_7
    //     0x67b4ec: ldur            d0, [x0, #7]
    // 0x67b4f0: ldr             x2, [fp, #0x10]
    // 0x67b4f4: r0 = nestOffset()
    //     0x67b4f4: bl              #0x67b10c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::nestOffset
    // 0x67b4f8: ldur            x0, [fp, #-8]
    // 0x67b4fc: LoadField: r2 = r0->field_1b
    //     0x67b4fc: ldur            w2, [x0, #0x1b]
    // 0x67b500: DecompressPointer r2
    //     0x67b500: add             x2, x2, HEAP, lsl #32
    // 0x67b504: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x67b504: ldur            w3, [x0, #0x17]
    // 0x67b508: DecompressPointer r3
    //     0x67b508: add             x3, x3, HEAP, lsl #32
    // 0x67b50c: ldr             x1, [fp, #0x10]
    // 0x67b510: r0 = createDrivenScrollActivity()
    //     0x67b510: bl              #0x67a828  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::createDrivenScrollActivity
    // 0x67b514: mov             x3, x0
    // 0x67b518: ldur            x0, [fp, #-8]
    // 0x67b51c: stur            x3, [fp, #-0x18]
    // 0x67b520: LoadField: r4 = r0->field_1f
    //     0x67b520: ldur            w4, [x0, #0x1f]
    // 0x67b524: DecompressPointer r4
    //     0x67b524: add             x4, x4, HEAP, lsl #32
    // 0x67b528: stur            x4, [fp, #-0x10]
    // 0x67b52c: LoadField: r0 = r3->field_f
    //     0x67b52c: ldur            w0, [x3, #0xf]
    // 0x67b530: DecompressPointer r0
    //     0x67b530: add             x0, x0, HEAP, lsl #32
    // 0x67b534: r16 = Sentinel
    //     0x67b534: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x67b538: cmp             w0, w16
    // 0x67b53c: b.eq            #0x67b60c
    // 0x67b540: LoadField: r5 = r0->field_b
    //     0x67b540: ldur            w5, [x0, #0xb]
    // 0x67b544: DecompressPointer r5
    //     0x67b544: add             x5, x5, HEAP, lsl #32
    // 0x67b548: stur            x5, [fp, #-8]
    // 0x67b54c: LoadField: r2 = r4->field_7
    //     0x67b54c: ldur            w2, [x4, #7]
    // 0x67b550: DecompressPointer r2
    //     0x67b550: add             x2, x2, HEAP, lsl #32
    // 0x67b554: mov             x0, x5
    // 0x67b558: r1 = Null
    //     0x67b558: mov             x1, NULL
    // 0x67b55c: cmp             w2, NULL
    // 0x67b560: b.eq            #0x67b57c
    // 0x67b564: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x67b564: ldur            w4, [x2, #0x17]
    // 0x67b568: DecompressPointer r4
    //     0x67b568: add             x4, x4, HEAP, lsl #32
    // 0x67b56c: r8 = X0
    //     0x67b56c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x67b570: LoadField: r9 = r4->field_7
    //     0x67b570: ldur            x9, [x4, #7]
    // 0x67b574: r3 = Null
    //     0x67b574: ldr             x3, [PP, #0x6eb0]  ; [pp+0x6eb0] Null
    // 0x67b578: blr             x9
    // 0x67b57c: ldur            x0, [fp, #-0x10]
    // 0x67b580: LoadField: r1 = r0->field_b
    //     0x67b580: ldur            w1, [x0, #0xb]
    // 0x67b584: LoadField: r2 = r0->field_f
    //     0x67b584: ldur            w2, [x0, #0xf]
    // 0x67b588: DecompressPointer r2
    //     0x67b588: add             x2, x2, HEAP, lsl #32
    // 0x67b58c: LoadField: r3 = r2->field_b
    //     0x67b58c: ldur            w3, [x2, #0xb]
    // 0x67b590: r2 = LoadInt32Instr(r1)
    //     0x67b590: sbfx            x2, x1, #1, #0x1f
    // 0x67b594: stur            x2, [fp, #-0x20]
    // 0x67b598: r1 = LoadInt32Instr(r3)
    //     0x67b598: sbfx            x1, x3, #1, #0x1f
    // 0x67b59c: cmp             x2, x1
    // 0x67b5a0: b.ne            #0x67b5ac
    // 0x67b5a4: mov             x1, x0
    // 0x67b5a8: r0 = _growToNextCapacity()
    //     0x67b5a8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x67b5ac: ldur            x2, [fp, #-0x10]
    // 0x67b5b0: ldur            x3, [fp, #-0x20]
    // 0x67b5b4: add             x4, x3, #1
    // 0x67b5b8: lsl             x5, x4, #1
    // 0x67b5bc: StoreField: r2->field_b = r5
    //     0x67b5bc: stur            w5, [x2, #0xb]
    // 0x67b5c0: LoadField: r1 = r2->field_f
    //     0x67b5c0: ldur            w1, [x2, #0xf]
    // 0x67b5c4: DecompressPointer r1
    //     0x67b5c4: add             x1, x1, HEAP, lsl #32
    // 0x67b5c8: ldur            x0, [fp, #-8]
    // 0x67b5cc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x67b5cc: add             x25, x1, x3, lsl #2
    //     0x67b5d0: add             x25, x25, #0xf
    //     0x67b5d4: str             w0, [x25]
    //     0x67b5d8: tbz             w0, #0, #0x67b5f4
    //     0x67b5dc: ldurb           w16, [x1, #-1]
    //     0x67b5e0: ldurb           w17, [x0, #-1]
    //     0x67b5e4: and             x16, x17, x16, lsr #2
    //     0x67b5e8: tst             x16, HEAP, lsr #32
    //     0x67b5ec: b.eq            #0x67b5f4
    //     0x67b5f0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x67b5f4: ldur            x0, [fp, #-0x18]
    // 0x67b5f8: LeaveFrame
    //     0x67b5f8: mov             SP, fp
    //     0x67b5fc: ldp             fp, lr, [SP], #0x10
    // 0x67b600: ret
    //     0x67b600: ret             
    // 0x67b604: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67b604: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67b608: b               #0x67b4dc
    // 0x67b60c: r9 = _completer
    //     0x67b60c: ldr             x9, [PP, #0x6ec0]  ; [pp+0x6ec0] Field <DrivenScrollActivity._completer@324498029>: late final (offset: 0x10)
    // 0x67b610: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x67b610: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ unnestOffset(/* No info */) {
    // ** addr: 0x67b614, size: 0x190
    // 0x67b614: EnterFrame
    //     0x67b614: stp             fp, lr, [SP, #-0x10]!
    //     0x67b618: mov             fp, SP
    // 0x67b61c: AllocStack(0x28)
    //     0x67b61c: sub             SP, SP, #0x28
    // 0x67b620: SetupParameters(_NestedScrollCoordinator this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x20 */)
    //     0x67b620: mov             x0, x1
    //     0x67b624: stur            x1, [fp, #-8]
    //     0x67b628: stur            x2, [fp, #-0x10]
    //     0x67b62c: stur            d0, [fp, #-0x20]
    // 0x67b630: CheckStackOverflow
    //     0x67b630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67b634: cmp             SP, x16
    //     0x67b638: b.ls            #0x67b778
    // 0x67b63c: mov             x1, x0
    // 0x67b640: r0 = _outerPosition()
    //     0x67b640: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x67b644: mov             x1, x0
    // 0x67b648: ldur            x0, [fp, #-0x10]
    // 0x67b64c: cmp             w0, w1
    // 0x67b650: b.ne            #0x67b6d4
    // 0x67b654: ldur            d0, [fp, #-0x20]
    // 0x67b658: ldur            x1, [fp, #-8]
    // 0x67b65c: r0 = _outerPosition()
    //     0x67b65c: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x67b660: cmp             w0, NULL
    // 0x67b664: b.eq            #0x67b780
    // 0x67b668: LoadField: r2 = r0->field_2f
    //     0x67b668: ldur            w2, [x0, #0x2f]
    // 0x67b66c: DecompressPointer r2
    //     0x67b66c: add             x2, x2, HEAP, lsl #32
    // 0x67b670: stur            x2, [fp, #-0x18]
    // 0x67b674: cmp             w2, NULL
    // 0x67b678: b.eq            #0x67b784
    // 0x67b67c: ldur            x1, [fp, #-8]
    // 0x67b680: r0 = _outerPosition()
    //     0x67b680: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x67b684: cmp             w0, NULL
    // 0x67b688: b.eq            #0x67b788
    // 0x67b68c: LoadField: r1 = r0->field_33
    //     0x67b68c: ldur            w1, [x0, #0x33]
    // 0x67b690: DecompressPointer r1
    //     0x67b690: add             x1, x1, HEAP, lsl #32
    // 0x67b694: cmp             w1, NULL
    // 0x67b698: b.eq            #0x67b78c
    // 0x67b69c: ldur            x0, [fp, #-0x18]
    // 0x67b6a0: LoadField: d0 = r0->field_7
    //     0x67b6a0: ldur            d0, [x0, #7]
    // 0x67b6a4: ldur            d1, [fp, #-0x20]
    // 0x67b6a8: fcmp            d0, d1
    // 0x67b6ac: b.gt            #0x67b6c8
    // 0x67b6b0: LoadField: d0 = r1->field_7
    //     0x67b6b0: ldur            d0, [x1, #7]
    // 0x67b6b4: fcmp            d1, d0
    // 0x67b6b8: b.gt            #0x67b6c8
    // 0x67b6bc: fcmp            d1, d1
    // 0x67b6c0: b.vs            #0x67b6c8
    // 0x67b6c4: mov             v0.16b, v1.16b
    // 0x67b6c8: LeaveFrame
    //     0x67b6c8: mov             SP, fp
    //     0x67b6cc: ldp             fp, lr, [SP], #0x10
    // 0x67b6d0: ret
    //     0x67b6d0: ret             
    // 0x67b6d4: ldur            d1, [fp, #-0x20]
    // 0x67b6d8: LoadField: r1 = r0->field_2f
    //     0x67b6d8: ldur            w1, [x0, #0x2f]
    // 0x67b6dc: DecompressPointer r1
    //     0x67b6dc: add             x1, x1, HEAP, lsl #32
    // 0x67b6e0: cmp             w1, NULL
    // 0x67b6e4: b.eq            #0x67b790
    // 0x67b6e8: LoadField: d0 = r1->field_7
    //     0x67b6e8: ldur            d0, [x1, #7]
    // 0x67b6ec: fcmp            d0, d1
    // 0x67b6f0: b.le            #0x67b738
    // 0x67b6f4: fsub            d2, d1, d0
    // 0x67b6f8: ldur            x1, [fp, #-8]
    // 0x67b6fc: stur            d2, [fp, #-0x28]
    // 0x67b700: r0 = _outerPosition()
    //     0x67b700: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x67b704: cmp             w0, NULL
    // 0x67b708: b.eq            #0x67b794
    // 0x67b70c: LoadField: r1 = r0->field_2f
    //     0x67b70c: ldur            w1, [x0, #0x2f]
    // 0x67b710: DecompressPointer r1
    //     0x67b710: add             x1, x1, HEAP, lsl #32
    // 0x67b714: cmp             w1, NULL
    // 0x67b718: b.eq            #0x67b798
    // 0x67b71c: LoadField: d0 = r1->field_7
    //     0x67b71c: ldur            d0, [x1, #7]
    // 0x67b720: ldur            d1, [fp, #-0x28]
    // 0x67b724: fadd            d2, d1, d0
    // 0x67b728: mov             v0.16b, v2.16b
    // 0x67b72c: LeaveFrame
    //     0x67b72c: mov             SP, fp
    //     0x67b730: ldp             fp, lr, [SP], #0x10
    // 0x67b734: ret
    //     0x67b734: ret             
    // 0x67b738: fsub            d2, d1, d0
    // 0x67b73c: ldur            x1, [fp, #-8]
    // 0x67b740: stur            d2, [fp, #-0x28]
    // 0x67b744: r0 = _outerPosition()
    //     0x67b744: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x67b748: cmp             w0, NULL
    // 0x67b74c: b.eq            #0x67b79c
    // 0x67b750: LoadField: r1 = r0->field_33
    //     0x67b750: ldur            w1, [x0, #0x33]
    // 0x67b754: DecompressPointer r1
    //     0x67b754: add             x1, x1, HEAP, lsl #32
    // 0x67b758: cmp             w1, NULL
    // 0x67b75c: b.eq            #0x67b7a0
    // 0x67b760: LoadField: d1 = r1->field_7
    //     0x67b760: ldur            d1, [x1, #7]
    // 0x67b764: ldur            d2, [fp, #-0x28]
    // 0x67b768: fadd            d0, d2, d1
    // 0x67b76c: LeaveFrame
    //     0x67b76c: mov             SP, fp
    //     0x67b770: ldp             fp, lr, [SP], #0x10
    // 0x67b774: ret
    //     0x67b774: ret             
    // 0x67b778: r0 = StackOverflowSharedWithFPURegs()
    //     0x67b778: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x67b77c: b               #0x67b63c
    // 0x67b780: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b780: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b784: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b784: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b788: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b788: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b78c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b78c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b790: r0 = NullCastErrorSharedWithFPURegs()
    //     0x67b790: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x67b794: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b794: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b798: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b798: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b79c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b79c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x67b7a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x67b7a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ jumpTo(/* No info */) {
    // ** addr: 0x7a69c4, size: 0x2b4
    // 0x7a69c4: EnterFrame
    //     0x7a69c4: stp             fp, lr, [SP, #-0x10]!
    //     0x7a69c8: mov             fp, SP
    // 0x7a69cc: AllocStack(0x60)
    //     0x7a69cc: sub             SP, SP, #0x60
    // 0x7a69d0: SetupParameters(_NestedScrollCoordinator this /* r1 => r0, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x48 */)
    //     0x7a69d0: mov             x0, x1
    //     0x7a69d4: stur            x1, [fp, #-8]
    //     0x7a69d8: stur            d0, [fp, #-0x48]
    // 0x7a69dc: CheckStackOverflow
    //     0x7a69dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a69e0: cmp             SP, x16
    //     0x7a69e4: b.ls            #0x7a6c50
    // 0x7a69e8: mov             x1, x0
    // 0x7a69ec: r0 = goIdle()
    //     0x7a69ec: bl              #0xdb2ef4  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::goIdle
    // 0x7a69f0: ldur            x1, [fp, #-8]
    // 0x7a69f4: r0 = _outerPosition()
    //     0x7a69f4: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x7a69f8: stur            x0, [fp, #-0x10]
    // 0x7a69fc: cmp             w0, NULL
    // 0x7a6a00: b.eq            #0x7a6c58
    // 0x7a6a04: ldur            x1, [fp, #-8]
    // 0x7a6a08: r0 = _outerPosition()
    //     0x7a6a08: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x7a6a0c: cmp             w0, NULL
    // 0x7a6a10: b.eq            #0x7a6c5c
    // 0x7a6a14: ldur            x1, [fp, #-8]
    // 0x7a6a18: ldur            d0, [fp, #-0x48]
    // 0x7a6a1c: mov             x2, x0
    // 0x7a6a20: r0 = nestOffset()
    //     0x7a6a20: bl              #0x67b10c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::nestOffset
    // 0x7a6a24: ldur            x1, [fp, #-0x10]
    // 0x7a6a28: r0 = localJumpTo()
    //     0x7a6a28: bl              #0x7a7568  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::localJumpTo
    // 0x7a6a2c: ldur            x1, [fp, #-8]
    // 0x7a6a30: LoadField: r0 = r1->field_1b
    //     0x7a6a30: ldur            w0, [x1, #0x1b]
    // 0x7a6a34: DecompressPointer r0
    //     0x7a6a34: add             x0, x0, HEAP, lsl #32
    // 0x7a6a38: r16 = Sentinel
    //     0x7a6a38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7a6a3c: cmp             w0, w16
    // 0x7a6a40: b.eq            #0x7a6c60
    // 0x7a6a44: LoadField: r2 = r0->field_3b
    //     0x7a6a44: ldur            w2, [x0, #0x3b]
    // 0x7a6a48: DecompressPointer r2
    //     0x7a6a48: add             x2, x2, HEAP, lsl #32
    // 0x7a6a4c: r16 = <_NestedScrollPosition>
    //     0x7a6a4c: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0x7a6a50: stp             x2, x16, [SP]
    // 0x7a6a54: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x7a6a54: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x7a6a58: r0 = cast()
    //     0x7a6a58: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0x7a6a5c: stur            x0, [fp, #-0x18]
    // 0x7a6a60: LoadField: r2 = r0->field_7
    //     0x7a6a60: ldur            w2, [x0, #7]
    // 0x7a6a64: DecompressPointer r2
    //     0x7a6a64: add             x2, x2, HEAP, lsl #32
    // 0x7a6a68: stur            x2, [fp, #-0x10]
    // 0x7a6a6c: str             x0, [SP]
    // 0x7a6a70: r0 = length()
    //     0x7a6a70: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x7a6a74: r1 = LoadInt32Instr(r0)
    //     0x7a6a74: sbfx            x1, x0, #1, #0x1f
    //     0x7a6a78: tbz             w0, #0, #0x7a6a80
    //     0x7a6a7c: ldur            x1, [x0, #7]
    // 0x7a6a80: ldur            x0, [fp, #-0x18]
    // 0x7a6a84: stur            x1, [fp, #-0x30]
    // 0x7a6a88: LoadField: r2 = r0->field_b
    //     0x7a6a88: ldur            w2, [x0, #0xb]
    // 0x7a6a8c: DecompressPointer r2
    //     0x7a6a8c: add             x2, x2, HEAP, lsl #32
    // 0x7a6a90: stur            x2, [fp, #-0x28]
    // 0x7a6a94: r3 = 0
    //     0x7a6a94: movz            x3, #0
    // 0x7a6a98: stur            x3, [fp, #-0x20]
    // 0x7a6a9c: CheckStackOverflow
    //     0x7a6a9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a6aa0: cmp             SP, x16
    //     0x7a6aa4: b.ls            #0x7a6c68
    // 0x7a6aa8: str             x0, [SP]
    // 0x7a6aac: r0 = length()
    //     0x7a6aac: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x7a6ab0: r1 = LoadInt32Instr(r0)
    //     0x7a6ab0: sbfx            x1, x0, #1, #0x1f
    //     0x7a6ab4: tbz             w0, #0, #0x7a6abc
    //     0x7a6ab8: ldur            x1, [x0, #7]
    // 0x7a6abc: ldur            x2, [fp, #-0x30]
    // 0x7a6ac0: cmp             x2, x1
    // 0x7a6ac4: b.ne            #0x7a6c30
    // 0x7a6ac8: ldur            x3, [fp, #-0x20]
    // 0x7a6acc: cmp             x3, x1
    // 0x7a6ad0: b.ge            #0x7a6c14
    // 0x7a6ad4: ldur            x4, [fp, #-0x28]
    // 0x7a6ad8: r0 = BoxInt64Instr(r3)
    //     0x7a6ad8: sbfiz           x0, x3, #1, #0x1f
    //     0x7a6adc: cmp             x3, x0, asr #1
    //     0x7a6ae0: b.eq            #0x7a6aec
    //     0x7a6ae4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7a6ae8: stur            x3, [x0, #7]
    // 0x7a6aec: r1 = LoadClassIdInstr(r4)
    //     0x7a6aec: ldur            x1, [x4, #-1]
    //     0x7a6af0: ubfx            x1, x1, #0xc, #0x14
    // 0x7a6af4: stp             x0, x4, [SP]
    // 0x7a6af8: mov             x0, x1
    // 0x7a6afc: r0 = GDT[cid_x0 + 0x13037]()
    //     0x7a6afc: movz            x17, #0x3037
    //     0x7a6b00: movk            x17, #0x1, lsl #16
    //     0x7a6b04: add             lr, x0, x17
    //     0x7a6b08: ldr             lr, [x21, lr, lsl #3]
    //     0x7a6b0c: blr             lr
    // 0x7a6b10: ldur            x2, [fp, #-0x10]
    // 0x7a6b14: mov             x3, x0
    // 0x7a6b18: r1 = Null
    //     0x7a6b18: mov             x1, NULL
    // 0x7a6b1c: stur            x3, [fp, #-0x38]
    // 0x7a6b20: cmp             w2, NULL
    // 0x7a6b24: b.eq            #0x7a6b40
    // 0x7a6b28: LoadField: r4 = r2->field_1f
    //     0x7a6b28: ldur            w4, [x2, #0x1f]
    // 0x7a6b2c: DecompressPointer r4
    //     0x7a6b2c: add             x4, x4, HEAP, lsl #32
    // 0x7a6b30: r8 = C1X1
    //     0x7a6b30: ldr             x8, [PP, #0x6ee0]  ; [pp+0x6ee0] TypeParameter: C1X1
    // 0x7a6b34: LoadField: r9 = r4->field_7
    //     0x7a6b34: ldur            x9, [x4, #7]
    // 0x7a6b38: r3 = Null
    //     0x7a6b38: ldr             x3, [PP, #0x7598]  ; [pp+0x7598] Null
    // 0x7a6b3c: blr             x9
    // 0x7a6b40: ldur            x0, [fp, #-0x20]
    // 0x7a6b44: add             x3, x0, #1
    // 0x7a6b48: ldur            x4, [fp, #-0x38]
    // 0x7a6b4c: stur            x3, [fp, #-0x40]
    // 0x7a6b50: cmp             w4, NULL
    // 0x7a6b54: b.ne            #0x7a6b84
    // 0x7a6b58: mov             x0, x4
    // 0x7a6b5c: ldur            x2, [fp, #-0x10]
    // 0x7a6b60: r1 = Null
    //     0x7a6b60: mov             x1, NULL
    // 0x7a6b64: cmp             w2, NULL
    // 0x7a6b68: b.eq            #0x7a6b84
    // 0x7a6b6c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7a6b6c: ldur            w4, [x2, #0x17]
    // 0x7a6b70: DecompressPointer r4
    //     0x7a6b70: add             x4, x4, HEAP, lsl #32
    // 0x7a6b74: r8 = X0
    //     0x7a6b74: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7a6b78: LoadField: r9 = r4->field_7
    //     0x7a6b78: ldur            x9, [x4, #7]
    // 0x7a6b7c: r3 = Null
    //     0x7a6b7c: ldr             x3, [PP, #0x75a8]  ; [pp+0x75a8] Null
    // 0x7a6b80: blr             x9
    // 0x7a6b84: ldur            x0, [fp, #-0x38]
    // 0x7a6b88: ldur            x1, [fp, #-8]
    // 0x7a6b8c: ldur            d0, [fp, #-0x48]
    // 0x7a6b90: mov             x2, x0
    // 0x7a6b94: r0 = nestOffset()
    //     0x7a6b94: bl              #0x67b10c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::nestOffset
    // 0x7a6b98: ldur            x0, [fp, #-0x38]
    // 0x7a6b9c: LoadField: r1 = r0->field_3f
    //     0x7a6b9c: ldur            w1, [x0, #0x3f]
    // 0x7a6ba0: DecompressPointer r1
    //     0x7a6ba0: add             x1, x1, HEAP, lsl #32
    // 0x7a6ba4: cmp             w1, NULL
    // 0x7a6ba8: b.eq            #0x7a6c70
    // 0x7a6bac: LoadField: d1 = r1->field_7
    //     0x7a6bac: ldur            d1, [x1, #7]
    // 0x7a6bb0: stur            d1, [fp, #-0x50]
    // 0x7a6bb4: fcmp            d1, d0
    // 0x7a6bb8: b.eq            #0x7a6c00
    // 0x7a6bbc: mov             x1, x0
    // 0x7a6bc0: r0 = forcePixels()
    //     0x7a6bc0: bl              #0x7a6d40  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::forcePixels
    // 0x7a6bc4: ldur            x1, [fp, #-0x38]
    // 0x7a6bc8: r0 = didStartScroll()
    //     0x7a6bc8: bl              #0x679798  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didStartScroll
    // 0x7a6bcc: ldur            x0, [fp, #-0x38]
    // 0x7a6bd0: LoadField: r1 = r0->field_3f
    //     0x7a6bd0: ldur            w1, [x0, #0x3f]
    // 0x7a6bd4: DecompressPointer r1
    //     0x7a6bd4: add             x1, x1, HEAP, lsl #32
    // 0x7a6bd8: cmp             w1, NULL
    // 0x7a6bdc: b.eq            #0x7a6c74
    // 0x7a6be0: LoadField: d0 = r1->field_7
    //     0x7a6be0: ldur            d0, [x1, #7]
    // 0x7a6be4: ldur            d1, [fp, #-0x50]
    // 0x7a6be8: fsub            d2, d0, d1
    // 0x7a6bec: mov             x1, x0
    // 0x7a6bf0: mov             v0.16b, v2.16b
    // 0x7a6bf4: r0 = didUpdateScrollPositionBy()
    //     0x7a6bf4: bl              #0x7a6c78  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didUpdateScrollPositionBy
    // 0x7a6bf8: ldur            x1, [fp, #-0x38]
    // 0x7a6bfc: r0 = didEndScroll()
    //     0x7a6bfc: bl              #0x679988  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didEndScroll
    // 0x7a6c00: ldur            x3, [fp, #-0x40]
    // 0x7a6c04: ldur            x0, [fp, #-0x18]
    // 0x7a6c08: ldur            x2, [fp, #-0x28]
    // 0x7a6c0c: ldur            x1, [fp, #-0x30]
    // 0x7a6c10: b               #0x7a6a98
    // 0x7a6c14: ldur            x1, [fp, #-8]
    // 0x7a6c18: d0 = 0.000000
    //     0x7a6c18: eor             v0.16b, v0.16b, v0.16b
    // 0x7a6c1c: r0 = goBallistic()
    //     0x7a6c1c: bl              #0xdb9198  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::goBallistic
    // 0x7a6c20: r0 = Null
    //     0x7a6c20: mov             x0, NULL
    // 0x7a6c24: LeaveFrame
    //     0x7a6c24: mov             SP, fp
    //     0x7a6c28: ldp             fp, lr, [SP], #0x10
    // 0x7a6c2c: ret
    //     0x7a6c2c: ret             
    // 0x7a6c30: ldur            x0, [fp, #-0x18]
    // 0x7a6c34: r0 = ConcurrentModificationError()
    //     0x7a6c34: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x7a6c38: mov             x1, x0
    // 0x7a6c3c: ldur            x0, [fp, #-0x18]
    // 0x7a6c40: StoreField: r1->field_b = r0
    //     0x7a6c40: stur            w0, [x1, #0xb]
    // 0x7a6c44: mov             x0, x1
    // 0x7a6c48: r0 = Throw()
    //     0x7a6c48: bl              #0xec04b8  ; ThrowStub
    // 0x7a6c4c: brk             #0
    // 0x7a6c50: r0 = StackOverflowSharedWithFPURegs()
    //     0x7a6c50: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7a6c54: b               #0x7a69e8
    // 0x7a6c58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7a6c58: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7a6c5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7a6c5c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7a6c60: r9 = _innerController
    //     0x7a6c60: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0x7a6c64: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7a6c64: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7a6c68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a6c68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a6c6c: b               #0x7a6aa8
    // 0x7a6c70: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7a6c70: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x7a6c74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7a6c74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _NestedScrollCoordinator(/* No info */) {
    // ** addr: 0x9435e0, size: 0x13c
    // 0x9435e0: EnterFrame
    //     0x9435e0: stp             fp, lr, [SP, #-0x10]!
    //     0x9435e4: mov             fp, SP
    // 0x9435e8: AllocStack(0x18)
    //     0x9435e8: sub             SP, SP, #0x18
    // 0x9435ec: r5 = Sentinel
    //     0x9435ec: ldr             x5, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9435f0: r0 = Instance_ScrollDirection
    //     0x9435f0: ldr             x0, [PP, #0x6ed8]  ; [pp+0x6ed8] Obj!ScrollDirection@e352e1
    // 0x9435f4: r4 = false
    //     0x9435f4: add             x4, NULL, #0x30  ; false
    // 0x9435f8: stur            x1, [fp, #-8]
    // 0x9435fc: mov             x16, x3
    // 0x943600: mov             x3, x1
    // 0x943604: mov             x1, x16
    // 0x943608: CheckStackOverflow
    //     0x943608: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94360c: cmp             SP, x16
    //     0x943610: b.ls            #0x943714
    // 0x943614: ArrayStore: r3[0] = r5  ; List_4
    //     0x943614: stur            w5, [x3, #0x17]
    // 0x943618: StoreField: r3->field_1b = r5
    //     0x943618: stur            w5, [x3, #0x1b]
    // 0x94361c: StoreField: r3->field_1f = r0
    //     0x94361c: stur            w0, [x3, #0x1f]
    // 0x943620: mov             x0, x2
    // 0x943624: StoreField: r3->field_7 = r0
    //     0x943624: stur            w0, [x3, #7]
    //     0x943628: ldurb           w16, [x3, #-1]
    //     0x94362c: ldurb           w17, [x0, #-1]
    //     0x943630: and             x16, x17, x16, lsr #2
    //     0x943634: tst             x16, HEAP, lsr #32
    //     0x943638: b.eq            #0x943640
    //     0x94363c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x943640: mov             x0, x1
    // 0x943644: StoreField: r3->field_f = r0
    //     0x943644: stur            w0, [x3, #0xf]
    //     0x943648: ldurb           w16, [x3, #-1]
    //     0x94364c: ldurb           w17, [x0, #-1]
    //     0x943650: and             x16, x17, x16, lsr #2
    //     0x943654: tst             x16, HEAP, lsr #32
    //     0x943658: b.eq            #0x943660
    //     0x94365c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x943660: StoreField: r3->field_13 = r4
    //     0x943660: stur            w4, [x3, #0x13]
    // 0x943664: r0 = _NestedScrollController()
    //     0x943664: bl              #0x94371c  ; Allocate_NestedScrollControllerStub -> _NestedScrollController (size=0x44)
    // 0x943668: mov             x2, x0
    // 0x94366c: ldur            x0, [fp, #-8]
    // 0x943670: stur            x2, [fp, #-0x10]
    // 0x943674: StoreField: r2->field_3f = r0
    //     0x943674: stur            w0, [x2, #0x3f]
    // 0x943678: r16 = "outer"
    //     0x943678: add             x16, PP, #0x46, lsl #12  ; [pp+0x460b0] "outer"
    //     0x94367c: ldr             x16, [x16, #0xb0]
    // 0x943680: str             x16, [SP]
    // 0x943684: mov             x1, x2
    // 0x943688: r4 = const [0, 0x2, 0x1, 0x1, debugLabel, 0x1, null]
    //     0x943688: ldr             x4, [PP, #0x2328]  ; [pp+0x2328] List(7) [0, 0x2, 0x1, 0x1, "debugLabel", 0x1, Null]
    // 0x94368c: r0 = ScrollController()
    //     0x94368c: bl              #0x685160  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x943690: ldur            x0, [fp, #-0x10]
    // 0x943694: ldur            x1, [fp, #-8]
    // 0x943698: ArrayStore: r1[0] = r0  ; List_4
    //     0x943698: stur            w0, [x1, #0x17]
    //     0x94369c: ldurb           w16, [x1, #-1]
    //     0x9436a0: ldurb           w17, [x0, #-1]
    //     0x9436a4: and             x16, x17, x16, lsr #2
    //     0x9436a8: tst             x16, HEAP, lsr #32
    //     0x9436ac: b.eq            #0x9436b4
    //     0x9436b0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9436b4: r0 = _NestedScrollController()
    //     0x9436b4: bl              #0x94371c  ; Allocate_NestedScrollControllerStub -> _NestedScrollController (size=0x44)
    // 0x9436b8: mov             x2, x0
    // 0x9436bc: ldur            x0, [fp, #-8]
    // 0x9436c0: stur            x2, [fp, #-0x10]
    // 0x9436c4: StoreField: r2->field_3f = r0
    //     0x9436c4: stur            w0, [x2, #0x3f]
    // 0x9436c8: r16 = "inner"
    //     0x9436c8: add             x16, PP, #0x46, lsl #12  ; [pp+0x460b8] "inner"
    //     0x9436cc: ldr             x16, [x16, #0xb8]
    // 0x9436d0: str             x16, [SP]
    // 0x9436d4: mov             x1, x2
    // 0x9436d8: r4 = const [0, 0x2, 0x1, 0x1, debugLabel, 0x1, null]
    //     0x9436d8: ldr             x4, [PP, #0x2328]  ; [pp+0x2328] List(7) [0, 0x2, 0x1, 0x1, "debugLabel", 0x1, Null]
    // 0x9436dc: r0 = ScrollController()
    //     0x9436dc: bl              #0x685160  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x9436e0: ldur            x0, [fp, #-0x10]
    // 0x9436e4: ldur            x1, [fp, #-8]
    // 0x9436e8: StoreField: r1->field_1b = r0
    //     0x9436e8: stur            w0, [x1, #0x1b]
    //     0x9436ec: ldurb           w16, [x1, #-1]
    //     0x9436f0: ldurb           w17, [x0, #-1]
    //     0x9436f4: and             x16, x17, x16, lsr #2
    //     0x9436f8: tst             x16, HEAP, lsr #32
    //     0x9436fc: b.eq            #0x943704
    //     0x943700: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x943704: r0 = Null
    //     0x943704: mov             x0, NULL
    // 0x943708: LeaveFrame
    //     0x943708: mov             SP, fp
    //     0x94370c: ldp             fp, lr, [SP], #0x10
    // 0x943710: ret
    //     0x943710: ret             
    // 0x943714: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x943714: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x943718: b               #0x943614
  }
  get _ hasScrolledBody(/* No info */) {
    // ** addr: 0x943810, size: 0x224
    // 0x943810: EnterFrame
    //     0x943810: stp             fp, lr, [SP, #-0x10]!
    //     0x943814: mov             fp, SP
    // 0x943818: AllocStack(0x48)
    //     0x943818: sub             SP, SP, #0x48
    // 0x94381c: CheckStackOverflow
    //     0x94381c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x943820: cmp             SP, x16
    //     0x943824: b.ls            #0x943a1c
    // 0x943828: LoadField: r0 = r1->field_1b
    //     0x943828: ldur            w0, [x1, #0x1b]
    // 0x94382c: DecompressPointer r0
    //     0x94382c: add             x0, x0, HEAP, lsl #32
    // 0x943830: r16 = Sentinel
    //     0x943830: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x943834: cmp             w0, w16
    // 0x943838: b.eq            #0x943a24
    // 0x94383c: LoadField: r1 = r0->field_3b
    //     0x94383c: ldur            w1, [x0, #0x3b]
    // 0x943840: DecompressPointer r1
    //     0x943840: add             x1, x1, HEAP, lsl #32
    // 0x943844: r16 = <_NestedScrollPosition>
    //     0x943844: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0x943848: stp             x1, x16, [SP]
    // 0x94384c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x94384c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x943850: r0 = cast()
    //     0x943850: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0x943854: stur            x0, [fp, #-0x10]
    // 0x943858: LoadField: r2 = r0->field_7
    //     0x943858: ldur            w2, [x0, #7]
    // 0x94385c: DecompressPointer r2
    //     0x94385c: add             x2, x2, HEAP, lsl #32
    // 0x943860: stur            x2, [fp, #-8]
    // 0x943864: str             x0, [SP]
    // 0x943868: r0 = length()
    //     0x943868: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x94386c: r1 = LoadInt32Instr(r0)
    //     0x94386c: sbfx            x1, x0, #1, #0x1f
    //     0x943870: tbz             w0, #0, #0x943878
    //     0x943874: ldur            x1, [x0, #7]
    // 0x943878: ldur            x0, [fp, #-0x10]
    // 0x94387c: stur            x1, [fp, #-0x28]
    // 0x943880: LoadField: r2 = r0->field_b
    //     0x943880: ldur            w2, [x0, #0xb]
    // 0x943884: DecompressPointer r2
    //     0x943884: add             x2, x2, HEAP, lsl #32
    // 0x943888: stur            x2, [fp, #-0x20]
    // 0x94388c: r3 = 0
    //     0x94388c: movz            x3, #0
    // 0x943890: stur            x3, [fp, #-0x18]
    // 0x943894: CheckStackOverflow
    //     0x943894: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x943898: cmp             SP, x16
    //     0x94389c: b.ls            #0x943a2c
    // 0x9438a0: str             x0, [SP]
    // 0x9438a4: r0 = length()
    //     0x9438a4: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x9438a8: r1 = LoadInt32Instr(r0)
    //     0x9438a8: sbfx            x1, x0, #1, #0x1f
    //     0x9438ac: tbz             w0, #0, #0x9438b4
    //     0x9438b0: ldur            x1, [x0, #7]
    // 0x9438b4: ldur            x2, [fp, #-0x28]
    // 0x9438b8: cmp             x2, x1
    // 0x9438bc: b.ne            #0x9439fc
    // 0x9438c0: ldur            x3, [fp, #-0x18]
    // 0x9438c4: cmp             x3, x1
    // 0x9438c8: b.ge            #0x9439ec
    // 0x9438cc: ldur            x4, [fp, #-0x20]
    // 0x9438d0: r0 = BoxInt64Instr(r3)
    //     0x9438d0: sbfiz           x0, x3, #1, #0x1f
    //     0x9438d4: cmp             x3, x0, asr #1
    //     0x9438d8: b.eq            #0x9438e4
    //     0x9438dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9438e0: stur            x3, [x0, #7]
    // 0x9438e4: r1 = LoadClassIdInstr(r4)
    //     0x9438e4: ldur            x1, [x4, #-1]
    //     0x9438e8: ubfx            x1, x1, #0xc, #0x14
    // 0x9438ec: stp             x0, x4, [SP]
    // 0x9438f0: mov             x0, x1
    // 0x9438f4: r0 = GDT[cid_x0 + 0x13037]()
    //     0x9438f4: movz            x17, #0x3037
    //     0x9438f8: movk            x17, #0x1, lsl #16
    //     0x9438fc: add             lr, x0, x17
    //     0x943900: ldr             lr, [x21, lr, lsl #3]
    //     0x943904: blr             lr
    // 0x943908: ldur            x2, [fp, #-8]
    // 0x94390c: mov             x3, x0
    // 0x943910: r1 = Null
    //     0x943910: mov             x1, NULL
    // 0x943914: stur            x3, [fp, #-0x30]
    // 0x943918: cmp             w2, NULL
    // 0x94391c: b.eq            #0x94393c
    // 0x943920: LoadField: r4 = r2->field_1f
    //     0x943920: ldur            w4, [x2, #0x1f]
    // 0x943924: DecompressPointer r4
    //     0x943924: add             x4, x4, HEAP, lsl #32
    // 0x943928: r8 = C1X1
    //     0x943928: ldr             x8, [PP, #0x6ee0]  ; [pp+0x6ee0] TypeParameter: C1X1
    // 0x94392c: LoadField: r9 = r4->field_7
    //     0x94392c: ldur            x9, [x4, #7]
    // 0x943930: r3 = Null
    //     0x943930: add             x3, PP, #0x45, lsl #12  ; [pp+0x45f60] Null
    //     0x943934: ldr             x3, [x3, #0xf60]
    // 0x943938: blr             x9
    // 0x94393c: ldur            x0, [fp, #-0x18]
    // 0x943940: add             x3, x0, #1
    // 0x943944: ldur            x4, [fp, #-0x30]
    // 0x943948: stur            x3, [fp, #-0x38]
    // 0x94394c: cmp             w4, NULL
    // 0x943950: b.ne            #0x943984
    // 0x943954: mov             x0, x4
    // 0x943958: ldur            x2, [fp, #-8]
    // 0x94395c: r1 = Null
    //     0x94395c: mov             x1, NULL
    // 0x943960: cmp             w2, NULL
    // 0x943964: b.eq            #0x943984
    // 0x943968: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x943968: ldur            w4, [x2, #0x17]
    // 0x94396c: DecompressPointer r4
    //     0x94396c: add             x4, x4, HEAP, lsl #32
    // 0x943970: r8 = X0
    //     0x943970: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x943974: LoadField: r9 = r4->field_7
    //     0x943974: ldur            x9, [x4, #7]
    // 0x943978: r3 = Null
    //     0x943978: add             x3, PP, #0x45, lsl #12  ; [pp+0x45f70] Null
    //     0x94397c: ldr             x3, [x3, #0xf70]
    // 0x943980: blr             x9
    // 0x943984: ldur            x0, [fp, #-0x30]
    // 0x943988: LoadField: r1 = r0->field_2f
    //     0x943988: ldur            w1, [x0, #0x2f]
    // 0x94398c: DecompressPointer r1
    //     0x94398c: add             x1, x1, HEAP, lsl #32
    // 0x943990: cmp             w1, NULL
    // 0x943994: b.eq            #0x9439d8
    // 0x943998: LoadField: r2 = r0->field_33
    //     0x943998: ldur            w2, [x0, #0x33]
    // 0x94399c: DecompressPointer r2
    //     0x94399c: add             x2, x2, HEAP, lsl #32
    // 0x9439a0: cmp             w2, NULL
    // 0x9439a4: b.eq            #0x9439d8
    // 0x9439a8: LoadField: r2 = r0->field_3f
    //     0x9439a8: ldur            w2, [x0, #0x3f]
    // 0x9439ac: DecompressPointer r2
    //     0x9439ac: add             x2, x2, HEAP, lsl #32
    // 0x9439b0: cmp             w2, NULL
    // 0x9439b4: b.eq            #0x9439d8
    // 0x9439b8: LoadField: d0 = r2->field_7
    //     0x9439b8: ldur            d0, [x2, #7]
    // 0x9439bc: LoadField: d1 = r1->field_7
    //     0x9439bc: ldur            d1, [x1, #7]
    // 0x9439c0: fcmp            d0, d1
    // 0x9439c4: b.le            #0x9439d8
    // 0x9439c8: r0 = true
    //     0x9439c8: add             x0, NULL, #0x20  ; true
    // 0x9439cc: LeaveFrame
    //     0x9439cc: mov             SP, fp
    //     0x9439d0: ldp             fp, lr, [SP], #0x10
    // 0x9439d4: ret
    //     0x9439d4: ret             
    // 0x9439d8: ldur            x3, [fp, #-0x38]
    // 0x9439dc: ldur            x0, [fp, #-0x10]
    // 0x9439e0: ldur            x2, [fp, #-0x20]
    // 0x9439e4: ldur            x1, [fp, #-0x28]
    // 0x9439e8: b               #0x943890
    // 0x9439ec: r0 = false
    //     0x9439ec: add             x0, NULL, #0x30  ; false
    // 0x9439f0: LeaveFrame
    //     0x9439f0: mov             SP, fp
    //     0x9439f4: ldp             fp, lr, [SP], #0x10
    // 0x9439f8: ret
    //     0x9439f8: ret             
    // 0x9439fc: ldur            x0, [fp, #-0x10]
    // 0x943a00: r0 = ConcurrentModificationError()
    //     0x943a00: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x943a04: mov             x1, x0
    // 0x943a08: ldur            x0, [fp, #-0x10]
    // 0x943a0c: StoreField: r1->field_b = r0
    //     0x943a0c: stur            w0, [x1, #0xb]
    // 0x943a10: mov             x0, x1
    // 0x943a14: r0 = Throw()
    //     0x943a14: bl              #0xec04b8  ; ThrowStub
    // 0x943a18: brk             #0
    // 0x943a1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x943a1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x943a20: b               #0x943828
    // 0x943a24: r9 = _innerController
    //     0x943a24: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0x943a28: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x943a28: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x943a2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x943a2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x943a30: b               #0x9438a0
  }
  get _ canScrollBody(/* No info */) {
    // ** addr: 0x999644, size: 0x7c
    // 0x999644: EnterFrame
    //     0x999644: stp             fp, lr, [SP, #-0x10]!
    //     0x999648: mov             fp, SP
    // 0x99964c: CheckStackOverflow
    //     0x99964c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x999650: cmp             SP, x16
    //     0x999654: b.ls            #0x9996b8
    // 0x999658: r0 = _outerPosition()
    //     0x999658: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x99965c: cmp             w0, NULL
    // 0x999660: b.ne            #0x999674
    // 0x999664: r0 = true
    //     0x999664: add             x0, NULL, #0x20  ; true
    // 0x999668: LeaveFrame
    //     0x999668: mov             SP, fp
    //     0x99966c: ldp             fp, lr, [SP], #0x10
    // 0x999670: ret
    //     0x999670: ret             
    // 0x999674: LoadField: r1 = r0->field_47
    //     0x999674: ldur            w1, [x0, #0x47]
    // 0x999678: DecompressPointer r1
    //     0x999678: add             x1, x1, HEAP, lsl #32
    // 0x99967c: tbnz            w1, #4, #0x9996a8
    // 0x999680: mov             x1, x0
    // 0x999684: r0 = extentAfter()
    //     0x999684: bl              #0x9996c0  ; [package:flutter/src/widgets/scroll_position.dart] _ScrollPosition&ViewportOffset&ScrollMetrics::extentAfter
    // 0x999688: mov             v1.16b, v0.16b
    // 0x99968c: d0 = 0.000000
    //     0x99968c: eor             v0.16b, v0.16b, v0.16b
    // 0x999690: fcmp            d1, d0
    // 0x999694: r16 = true
    //     0x999694: add             x16, NULL, #0x20  ; true
    // 0x999698: r17 = false
    //     0x999698: add             x17, NULL, #0x30  ; false
    // 0x99969c: csel            x1, x16, x17, eq
    // 0x9996a0: mov             x0, x1
    // 0x9996a4: b               #0x9996ac
    // 0x9996a8: r0 = false
    //     0x9996a8: add             x0, NULL, #0x30  ; false
    // 0x9996ac: LeaveFrame
    //     0x9996ac: mov             SP, fp
    //     0x9996b0: ldp             fp, lr, [SP], #0x10
    // 0x9996b4: ret
    //     0x9996b4: ret             
    // 0x9996b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9996b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9996bc: b               #0x999658
  }
  _ updateCanDrag(/* No info */) {
    // ** addr: 0x999eac, size: 0x288
    // 0x999eac: EnterFrame
    //     0x999eac: stp             fp, lr, [SP, #-0x10]!
    //     0x999eb0: mov             fp, SP
    // 0x999eb4: AllocStack(0x58)
    //     0x999eb4: sub             SP, SP, #0x58
    // 0x999eb8: SetupParameters(_NestedScrollCoordinator this /* r1 => r0, fp-0x8 */)
    //     0x999eb8: mov             x0, x1
    //     0x999ebc: stur            x1, [fp, #-8]
    // 0x999ec0: CheckStackOverflow
    //     0x999ec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x999ec4: cmp             SP, x16
    //     0x999ec8: b.ls            #0x99a114
    // 0x999ecc: mov             x1, x0
    // 0x999ed0: r0 = _outerPosition()
    //     0x999ed0: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x999ed4: cmp             w0, NULL
    // 0x999ed8: b.eq            #0x99a11c
    // 0x999edc: LoadField: r1 = r0->field_47
    //     0x999edc: ldur            w1, [x0, #0x47]
    // 0x999ee0: DecompressPointer r1
    //     0x999ee0: add             x1, x1, HEAP, lsl #32
    // 0x999ee4: tbz             w1, #4, #0x999ef8
    // 0x999ee8: r0 = Null
    //     0x999ee8: mov             x0, NULL
    // 0x999eec: LeaveFrame
    //     0x999eec: mov             SP, fp
    //     0x999ef0: ldp             fp, lr, [SP], #0x10
    // 0x999ef4: ret
    //     0x999ef4: ret             
    // 0x999ef8: ldur            x1, [fp, #-8]
    // 0x999efc: LoadField: r0 = r1->field_1b
    //     0x999efc: ldur            w0, [x1, #0x1b]
    // 0x999f00: DecompressPointer r0
    //     0x999f00: add             x0, x0, HEAP, lsl #32
    // 0x999f04: r16 = Sentinel
    //     0x999f04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x999f08: cmp             w0, w16
    // 0x999f0c: b.eq            #0x99a120
    // 0x999f10: LoadField: r2 = r0->field_3b
    //     0x999f10: ldur            w2, [x0, #0x3b]
    // 0x999f14: DecompressPointer r2
    //     0x999f14: add             x2, x2, HEAP, lsl #32
    // 0x999f18: r16 = <_NestedScrollPosition>
    //     0x999f18: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0x999f1c: stp             x2, x16, [SP]
    // 0x999f20: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x999f20: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x999f24: r0 = cast()
    //     0x999f24: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0x999f28: stur            x0, [fp, #-0x18]
    // 0x999f2c: LoadField: r2 = r0->field_7
    //     0x999f2c: ldur            w2, [x0, #7]
    // 0x999f30: DecompressPointer r2
    //     0x999f30: add             x2, x2, HEAP, lsl #32
    // 0x999f34: stur            x2, [fp, #-0x10]
    // 0x999f38: str             x0, [SP]
    // 0x999f3c: r0 = length()
    //     0x999f3c: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x999f40: r1 = LoadInt32Instr(r0)
    //     0x999f40: sbfx            x1, x0, #1, #0x1f
    //     0x999f44: tbz             w0, #0, #0x999f4c
    //     0x999f48: ldur            x1, [x0, #7]
    // 0x999f4c: ldur            x0, [fp, #-0x18]
    // 0x999f50: stur            x1, [fp, #-0x38]
    // 0x999f54: LoadField: r2 = r0->field_b
    //     0x999f54: ldur            w2, [x0, #0xb]
    // 0x999f58: DecompressPointer r2
    //     0x999f58: add             x2, x2, HEAP, lsl #32
    // 0x999f5c: stur            x2, [fp, #-0x30]
    // 0x999f60: r4 = false
    //     0x999f60: add             x4, NULL, #0x30  ; false
    // 0x999f64: r3 = 0
    //     0x999f64: movz            x3, #0
    // 0x999f68: stur            x4, [fp, #-0x20]
    // 0x999f6c: stur            x3, [fp, #-0x28]
    // 0x999f70: CheckStackOverflow
    //     0x999f70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x999f74: cmp             SP, x16
    //     0x999f78: b.ls            #0x99a128
    // 0x999f7c: str             x0, [SP]
    // 0x999f80: r0 = length()
    //     0x999f80: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x999f84: r1 = LoadInt32Instr(r0)
    //     0x999f84: sbfx            x1, x0, #1, #0x1f
    //     0x999f88: tbz             w0, #0, #0x999f90
    //     0x999f8c: ldur            x1, [x0, #7]
    // 0x999f90: ldur            x2, [fp, #-0x38]
    // 0x999f94: cmp             x2, x1
    // 0x999f98: b.ne            #0x99a0f4
    // 0x999f9c: ldur            x3, [fp, #-0x28]
    // 0x999fa0: cmp             x3, x1
    // 0x999fa4: b.ge            #0x99a0c4
    // 0x999fa8: ldur            x4, [fp, #-0x30]
    // 0x999fac: r0 = BoxInt64Instr(r3)
    //     0x999fac: sbfiz           x0, x3, #1, #0x1f
    //     0x999fb0: cmp             x3, x0, asr #1
    //     0x999fb4: b.eq            #0x999fc0
    //     0x999fb8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x999fbc: stur            x3, [x0, #7]
    // 0x999fc0: r1 = LoadClassIdInstr(r4)
    //     0x999fc0: ldur            x1, [x4, #-1]
    //     0x999fc4: ubfx            x1, x1, #0xc, #0x14
    // 0x999fc8: stp             x0, x4, [SP]
    // 0x999fcc: mov             x0, x1
    // 0x999fd0: r0 = GDT[cid_x0 + 0x13037]()
    //     0x999fd0: movz            x17, #0x3037
    //     0x999fd4: movk            x17, #0x1, lsl #16
    //     0x999fd8: add             lr, x0, x17
    //     0x999fdc: ldr             lr, [x21, lr, lsl #3]
    //     0x999fe0: blr             lr
    // 0x999fe4: ldur            x2, [fp, #-0x10]
    // 0x999fe8: mov             x3, x0
    // 0x999fec: r1 = Null
    //     0x999fec: mov             x1, NULL
    // 0x999ff0: stur            x3, [fp, #-0x40]
    // 0x999ff4: cmp             w2, NULL
    // 0x999ff8: b.eq            #0x99a018
    // 0x999ffc: LoadField: r4 = r2->field_1f
    //     0x999ffc: ldur            w4, [x2, #0x1f]
    // 0x99a000: DecompressPointer r4
    //     0x99a000: add             x4, x4, HEAP, lsl #32
    // 0x99a004: r8 = C1X1
    //     0x99a004: ldr             x8, [PP, #0x6ee0]  ; [pp+0x6ee0] TypeParameter: C1X1
    // 0x99a008: LoadField: r9 = r4->field_7
    //     0x99a008: ldur            x9, [x4, #7]
    // 0x99a00c: r3 = Null
    //     0x99a00c: add             x3, PP, #0x45, lsl #12  ; [pp+0x45fd0] Null
    //     0x99a010: ldr             x3, [x3, #0xfd0]
    // 0x99a014: blr             x9
    // 0x99a018: ldur            x0, [fp, #-0x28]
    // 0x99a01c: add             x3, x0, #1
    // 0x99a020: ldur            x4, [fp, #-0x40]
    // 0x99a024: stur            x3, [fp, #-0x48]
    // 0x99a028: cmp             w4, NULL
    // 0x99a02c: b.ne            #0x99a060
    // 0x99a030: mov             x0, x4
    // 0x99a034: ldur            x2, [fp, #-0x10]
    // 0x99a038: r1 = Null
    //     0x99a038: mov             x1, NULL
    // 0x99a03c: cmp             w2, NULL
    // 0x99a040: b.eq            #0x99a060
    // 0x99a044: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x99a044: ldur            w4, [x2, #0x17]
    // 0x99a048: DecompressPointer r4
    //     0x99a048: add             x4, x4, HEAP, lsl #32
    // 0x99a04c: r8 = X0
    //     0x99a04c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x99a050: LoadField: r9 = r4->field_7
    //     0x99a050: ldur            x9, [x4, #7]
    // 0x99a054: r3 = Null
    //     0x99a054: add             x3, PP, #0x45, lsl #12  ; [pp+0x45fe0] Null
    //     0x99a058: ldr             x3, [x3, #0xfe0]
    // 0x99a05c: blr             x9
    // 0x99a060: ldur            x2, [fp, #-0x40]
    // 0x99a064: LoadField: r0 = r2->field_47
    //     0x99a064: ldur            w0, [x2, #0x47]
    // 0x99a068: DecompressPointer r0
    //     0x99a068: add             x0, x0, HEAP, lsl #32
    // 0x99a06c: tbnz            w0, #4, #0x99a0b4
    // 0x99a070: ldur            x0, [fp, #-0x20]
    // 0x99a074: tbnz            w0, #4, #0x99a080
    // 0x99a078: r4 = true
    //     0x99a078: add             x4, NULL, #0x20  ; true
    // 0x99a07c: b               #0x99a0a0
    // 0x99a080: LoadField: r1 = r2->field_23
    //     0x99a080: ldur            w1, [x2, #0x23]
    // 0x99a084: DecompressPointer r1
    //     0x99a084: add             x1, x1, HEAP, lsl #32
    // 0x99a088: r0 = LoadClassIdInstr(r1)
    //     0x99a088: ldur            x0, [x1, #-1]
    //     0x99a08c: ubfx            x0, x0, #0xc, #0x14
    // 0x99a090: r0 = GDT[cid_x0 + -0xffd]()
    //     0x99a090: sub             lr, x0, #0xffd
    //     0x99a094: ldr             lr, [x21, lr, lsl #3]
    //     0x99a098: blr             lr
    // 0x99a09c: mov             x4, x0
    // 0x99a0a0: ldur            x3, [fp, #-0x48]
    // 0x99a0a4: ldur            x0, [fp, #-0x18]
    // 0x99a0a8: ldur            x2, [fp, #-0x30]
    // 0x99a0ac: ldur            x1, [fp, #-0x38]
    // 0x99a0b0: b               #0x999f68
    // 0x99a0b4: r0 = Null
    //     0x99a0b4: mov             x0, NULL
    // 0x99a0b8: LeaveFrame
    //     0x99a0b8: mov             SP, fp
    //     0x99a0bc: ldp             fp, lr, [SP], #0x10
    // 0x99a0c0: ret
    //     0x99a0c0: ret             
    // 0x99a0c4: ldur            x0, [fp, #-0x20]
    // 0x99a0c8: ldur            x1, [fp, #-8]
    // 0x99a0cc: r0 = _outerPosition()
    //     0x99a0cc: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x99a0d0: cmp             w0, NULL
    // 0x99a0d4: b.eq            #0x99a130
    // 0x99a0d8: mov             x1, x0
    // 0x99a0dc: ldur            x2, [fp, #-0x20]
    // 0x99a0e0: r0 = updateCanDrag()
    //     0x99a0e0: bl              #0x99a134  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::updateCanDrag
    // 0x99a0e4: r0 = Null
    //     0x99a0e4: mov             x0, NULL
    // 0x99a0e8: LeaveFrame
    //     0x99a0e8: mov             SP, fp
    //     0x99a0ec: ldp             fp, lr, [SP], #0x10
    // 0x99a0f0: ret
    //     0x99a0f0: ret             
    // 0x99a0f4: ldur            x0, [fp, #-0x18]
    // 0x99a0f8: r0 = ConcurrentModificationError()
    //     0x99a0f8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x99a0fc: mov             x1, x0
    // 0x99a100: ldur            x0, [fp, #-0x18]
    // 0x99a104: StoreField: r1->field_b = r0
    //     0x99a104: stur            w0, [x1, #0xb]
    // 0x99a108: mov             x0, x1
    // 0x99a10c: r0 = Throw()
    //     0x99a10c: bl              #0xec04b8  ; ThrowStub
    // 0x99a110: brk             #0
    // 0x99a114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99a114: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99a118: b               #0x999ecc
    // 0x99a11c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99a11c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99a120: r9 = _innerController
    //     0x99a120: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0x99a124: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x99a124: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x99a128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99a128: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99a12c: b               #0x999f7c
    // 0x99a130: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99a130: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ drag(/* No info */) {
    // ** addr: 0x99ae04, size: 0x118
    // 0x99ae04: EnterFrame
    //     0x99ae04: stp             fp, lr, [SP, #-0x10]!
    //     0x99ae08: mov             fp, SP
    // 0x99ae0c: AllocStack(0x28)
    //     0x99ae0c: sub             SP, SP, #0x28
    // 0x99ae10: SetupParameters(_NestedScrollCoordinator this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x99ae10: stur            x1, [fp, #-8]
    //     0x99ae14: stur            x2, [fp, #-0x10]
    //     0x99ae18: stur            x3, [fp, #-0x18]
    // 0x99ae1c: CheckStackOverflow
    //     0x99ae1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99ae20: cmp             SP, x16
    //     0x99ae24: b.ls            #0x99af10
    // 0x99ae28: r0 = ScrollDragController()
    //     0x99ae28: bl              #0x99af28  ; AllocateScrollDragControllerStub -> ScrollDragController (size=0x2c)
    // 0x99ae2c: mov             x1, x0
    // 0x99ae30: ldur            x0, [fp, #-0x18]
    // 0x99ae34: stur            x1, [fp, #-0x20]
    // 0x99ae38: StoreField: r1->field_b = r0
    //     0x99ae38: stur            w0, [x1, #0xb]
    // 0x99ae3c: ldur            x0, [fp, #-8]
    // 0x99ae40: StoreField: r1->field_7 = r0
    //     0x99ae40: stur            w0, [x1, #7]
    // 0x99ae44: ldur            x2, [fp, #-0x10]
    // 0x99ae48: StoreField: r1->field_27 = r2
    //     0x99ae48: stur            w2, [x1, #0x27]
    // 0x99ae4c: r3 = false
    //     0x99ae4c: add             x3, NULL, #0x30  ; false
    // 0x99ae50: StoreField: r1->field_1b = r3
    //     0x99ae50: stur            w3, [x1, #0x1b]
    // 0x99ae54: LoadField: r4 = r2->field_7
    //     0x99ae54: ldur            w4, [x2, #7]
    // 0x99ae58: DecompressPointer r4
    //     0x99ae58: add             x4, x4, HEAP, lsl #32
    // 0x99ae5c: ArrayStore: r1[0] = r4  ; List_4
    //     0x99ae5c: stur            w4, [x1, #0x17]
    // 0x99ae60: LoadField: r4 = r2->field_f
    //     0x99ae60: ldur            w4, [x2, #0xf]
    // 0x99ae64: DecompressPointer r4
    //     0x99ae64: add             x4, x4, HEAP, lsl #32
    // 0x99ae68: StoreField: r1->field_23 = r4
    //     0x99ae68: stur            w4, [x1, #0x23]
    // 0x99ae6c: r1 = 1
    //     0x99ae6c: movz            x1, #0x1
    // 0x99ae70: r0 = AllocateContext()
    //     0x99ae70: bl              #0xec126c  ; AllocateContextStub
    // 0x99ae74: mov             x2, x0
    // 0x99ae78: ldur            x0, [fp, #-0x20]
    // 0x99ae7c: stur            x2, [fp, #-0x10]
    // 0x99ae80: StoreField: r2->field_f = r0
    //     0x99ae80: stur            w0, [x2, #0xf]
    // 0x99ae84: ldur            x1, [fp, #-8]
    // 0x99ae88: r0 = _outerPosition()
    //     0x99ae88: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x99ae8c: stur            x0, [fp, #-0x18]
    // 0x99ae90: cmp             w0, NULL
    // 0x99ae94: b.eq            #0x99af18
    // 0x99ae98: r0 = DragScrollActivity()
    //     0x99ae98: bl              #0x99af1c  ; AllocateDragScrollActivityStub -> DragScrollActivity (size=0x14)
    // 0x99ae9c: mov             x3, x0
    // 0x99aea0: ldur            x0, [fp, #-0x20]
    // 0x99aea4: stur            x3, [fp, #-0x28]
    // 0x99aea8: StoreField: r3->field_f = r0
    //     0x99aea8: stur            w0, [x3, #0xf]
    // 0x99aeac: r1 = false
    //     0x99aeac: add             x1, NULL, #0x30  ; false
    // 0x99aeb0: StoreField: r3->field_b = r1
    //     0x99aeb0: stur            w1, [x3, #0xb]
    // 0x99aeb4: ldur            x1, [fp, #-0x18]
    // 0x99aeb8: StoreField: r3->field_7 = r1
    //     0x99aeb8: stur            w1, [x3, #7]
    // 0x99aebc: ldur            x2, [fp, #-0x10]
    // 0x99aec0: r1 = Function '<anonymous closure>':.
    //     0x99aec0: add             x1, PP, #0x46, lsl #12  ; [pp+0x46048] AnonymousClosure: (0x99af34), in [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::drag (0x99ae04)
    //     0x99aec4: ldr             x1, [x1, #0x48]
    // 0x99aec8: r0 = AllocateClosure()
    //     0x99aec8: bl              #0xec1630  ; AllocateClosureStub
    // 0x99aecc: ldur            x1, [fp, #-8]
    // 0x99aed0: ldur            x2, [fp, #-0x28]
    // 0x99aed4: mov             x3, x0
    // 0x99aed8: r0 = beginActivity()
    //     0x99aed8: bl              #0x678e3c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::beginActivity
    // 0x99aedc: ldur            x0, [fp, #-0x20]
    // 0x99aee0: ldur            x1, [fp, #-8]
    // 0x99aee4: StoreField: r1->field_23 = r0
    //     0x99aee4: stur            w0, [x1, #0x23]
    //     0x99aee8: ldurb           w16, [x1, #-1]
    //     0x99aeec: ldurb           w17, [x0, #-1]
    //     0x99aef0: and             x16, x17, x16, lsr #2
    //     0x99aef4: tst             x16, HEAP, lsr #32
    //     0x99aef8: b.eq            #0x99af00
    //     0x99aefc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x99af00: ldur            x0, [fp, #-0x20]
    // 0x99af04: LeaveFrame
    //     0x99af04: mov             SP, fp
    //     0x99af08: ldp             fp, lr, [SP], #0x10
    // 0x99af0c: ret
    //     0x99af0c: ret             
    // 0x99af10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99af10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99af14: b               #0x99ae28
    // 0x99af18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99af18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] DragScrollActivity <anonymous closure>(dynamic, _NestedScrollPosition) {
    // ** addr: 0x99af34, size: 0x4c
    // 0x99af34: EnterFrame
    //     0x99af34: stp             fp, lr, [SP, #-0x10]!
    //     0x99af38: mov             fp, SP
    // 0x99af3c: AllocStack(0x8)
    //     0x99af3c: sub             SP, SP, #8
    // 0x99af40: SetupParameters()
    //     0x99af40: ldr             x0, [fp, #0x18]
    //     0x99af44: ldur            w1, [x0, #0x17]
    //     0x99af48: add             x1, x1, HEAP, lsl #32
    // 0x99af4c: LoadField: r0 = r1->field_f
    //     0x99af4c: ldur            w0, [x1, #0xf]
    // 0x99af50: DecompressPointer r0
    //     0x99af50: add             x0, x0, HEAP, lsl #32
    // 0x99af54: stur            x0, [fp, #-8]
    // 0x99af58: r0 = DragScrollActivity()
    //     0x99af58: bl              #0x99af1c  ; AllocateDragScrollActivityStub -> DragScrollActivity (size=0x14)
    // 0x99af5c: ldur            x1, [fp, #-8]
    // 0x99af60: StoreField: r0->field_f = r1
    //     0x99af60: stur            w1, [x0, #0xf]
    // 0x99af64: r1 = false
    //     0x99af64: add             x1, NULL, #0x30  ; false
    // 0x99af68: StoreField: r0->field_b = r1
    //     0x99af68: stur            w1, [x0, #0xb]
    // 0x99af6c: ldr             x1, [fp, #0x10]
    // 0x99af70: StoreField: r0->field_7 = r1
    //     0x99af70: stur            w1, [x0, #7]
    // 0x99af74: LeaveFrame
    //     0x99af74: mov             SP, fp
    //     0x99af78: ldp             fp, lr, [SP], #0x10
    // 0x99af7c: ret
    //     0x99af7c: ret             
  }
  _ hold(/* No info */) {
    // ** addr: 0x99b120, size: 0x98
    // 0x99b120: EnterFrame
    //     0x99b120: stp             fp, lr, [SP, #-0x10]!
    //     0x99b124: mov             fp, SP
    // 0x99b128: AllocStack(0x20)
    //     0x99b128: sub             SP, SP, #0x20
    // 0x99b12c: SetupParameters(_NestedScrollCoordinator this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x99b12c: mov             x0, x1
    //     0x99b130: stur            x1, [fp, #-8]
    //     0x99b134: stur            x2, [fp, #-0x10]
    // 0x99b138: CheckStackOverflow
    //     0x99b138: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99b13c: cmp             SP, x16
    //     0x99b140: b.ls            #0x99b1ac
    // 0x99b144: mov             x1, x0
    // 0x99b148: r0 = _outerPosition()
    //     0x99b148: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x99b14c: stur            x0, [fp, #-0x18]
    // 0x99b150: cmp             w0, NULL
    // 0x99b154: b.eq            #0x99b1b4
    // 0x99b158: r0 = HoldScrollActivity()
    //     0x99b158: bl              #0x99b2f4  ; AllocateHoldScrollActivityStub -> HoldScrollActivity (size=0x14)
    // 0x99b15c: mov             x3, x0
    // 0x99b160: ldur            x0, [fp, #-0x10]
    // 0x99b164: stur            x3, [fp, #-0x20]
    // 0x99b168: StoreField: r3->field_f = r0
    //     0x99b168: stur            w0, [x3, #0xf]
    // 0x99b16c: r0 = false
    //     0x99b16c: add             x0, NULL, #0x30  ; false
    // 0x99b170: StoreField: r3->field_b = r0
    //     0x99b170: stur            w0, [x3, #0xb]
    // 0x99b174: ldur            x0, [fp, #-0x18]
    // 0x99b178: StoreField: r3->field_7 = r0
    //     0x99b178: stur            w0, [x3, #7]
    // 0x99b17c: r1 = Function '<anonymous closure>':.
    //     0x99b17c: add             x1, PP, #0x46, lsl #12  ; [pp+0x46058] AnonymousClosure: (0x99b1b8), in [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::hold (0x99b120)
    //     0x99b180: ldr             x1, [x1, #0x58]
    // 0x99b184: r2 = Null
    //     0x99b184: mov             x2, NULL
    // 0x99b188: r0 = AllocateClosure()
    //     0x99b188: bl              #0xec1630  ; AllocateClosureStub
    // 0x99b18c: ldur            x1, [fp, #-8]
    // 0x99b190: ldur            x2, [fp, #-0x20]
    // 0x99b194: mov             x3, x0
    // 0x99b198: r0 = beginActivity()
    //     0x99b198: bl              #0x678e3c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::beginActivity
    // 0x99b19c: ldur            x0, [fp, #-8]
    // 0x99b1a0: LeaveFrame
    //     0x99b1a0: mov             SP, fp
    //     0x99b1a4: ldp             fp, lr, [SP], #0x10
    // 0x99b1a8: ret
    //     0x99b1a8: ret             
    // 0x99b1ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99b1ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99b1b0: b               #0x99b144
    // 0x99b1b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99b1b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] HoldScrollActivity <anonymous closure>(dynamic, _NestedScrollPosition) {
    // ** addr: 0x99b1b8, size: 0x28
    // 0x99b1b8: EnterFrame
    //     0x99b1b8: stp             fp, lr, [SP, #-0x10]!
    //     0x99b1bc: mov             fp, SP
    // 0x99b1c0: r0 = HoldScrollActivity()
    //     0x99b1c0: bl              #0x99b2f4  ; AllocateHoldScrollActivityStub -> HoldScrollActivity (size=0x14)
    // 0x99b1c4: r1 = false
    //     0x99b1c4: add             x1, NULL, #0x30  ; false
    // 0x99b1c8: StoreField: r0->field_b = r1
    //     0x99b1c8: stur            w1, [x0, #0xb]
    // 0x99b1cc: ldr             x1, [fp, #0x10]
    // 0x99b1d0: StoreField: r0->field_7 = r1
    //     0x99b1d0: stur            w1, [x0, #7]
    // 0x99b1d4: LeaveFrame
    //     0x99b1d4: mov             SP, fp
    //     0x99b1d8: ldp             fp, lr, [SP], #0x10
    // 0x99b1dc: ret
    //     0x99b1dc: ret             
  }
  _ updateParent(/* No info */) {
    // ** addr: 0x99b494, size: 0x7c
    // 0x99b494: EnterFrame
    //     0x99b494: stp             fp, lr, [SP, #-0x10]!
    //     0x99b498: mov             fp, SP
    // 0x99b49c: AllocStack(0x10)
    //     0x99b49c: sub             SP, SP, #0x10
    // 0x99b4a0: SetupParameters(_NestedScrollCoordinator this /* r1 => r0, fp-0x8 */)
    //     0x99b4a0: mov             x0, x1
    //     0x99b4a4: stur            x1, [fp, #-8]
    // 0x99b4a8: CheckStackOverflow
    //     0x99b4a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99b4ac: cmp             SP, x16
    //     0x99b4b0: b.ls            #0x99b504
    // 0x99b4b4: mov             x1, x0
    // 0x99b4b8: r0 = _outerPosition()
    //     0x99b4b8: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x99b4bc: stur            x0, [fp, #-0x10]
    // 0x99b4c0: cmp             w0, NULL
    // 0x99b4c4: b.eq            #0x99b4f4
    // 0x99b4c8: ldur            x1, [fp, #-8]
    // 0x99b4cc: LoadField: r2 = r1->field_7
    //     0x99b4cc: ldur            w2, [x1, #7]
    // 0x99b4d0: DecompressPointer r2
    //     0x99b4d0: add             x2, x2, HEAP, lsl #32
    // 0x99b4d4: LoadField: r1 = r2->field_f
    //     0x99b4d4: ldur            w1, [x2, #0xf]
    // 0x99b4d8: DecompressPointer r1
    //     0x99b4d8: add             x1, x1, HEAP, lsl #32
    // 0x99b4dc: cmp             w1, NULL
    // 0x99b4e0: b.eq            #0x99b50c
    // 0x99b4e4: r0 = maybeOf()
    //     0x99b4e4: bl              #0x92fc74  ; [package:flutter/src/widgets/primary_scroll_controller.dart] PrimaryScrollController::maybeOf
    // 0x99b4e8: ldur            x1, [fp, #-0x10]
    // 0x99b4ec: mov             x2, x0
    // 0x99b4f0: r0 = setParent()
    //     0x99b4f0: bl              #0x99b758  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::setParent
    // 0x99b4f4: r0 = Null
    //     0x99b4f4: mov             x0, NULL
    // 0x99b4f8: LeaveFrame
    //     0x99b4f8: mov             SP, fp
    //     0x99b4fc: ldp             fp, lr, [SP], #0x10
    // 0x99b500: ret
    //     0x99b500: ret             
    // 0x99b504: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99b504: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99b508: b               #0x99b4b4
    // 0x99b50c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99b50c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ updateShadow(/* No info */) {
    // ** addr: 0x99b718, size: 0x40
    // 0x99b718: EnterFrame
    //     0x99b718: stp             fp, lr, [SP, #-0x10]!
    //     0x99b71c: mov             fp, SP
    // 0x99b720: CheckStackOverflow
    //     0x99b720: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99b724: cmp             SP, x16
    //     0x99b728: b.ls            #0x99b750
    // 0x99b72c: LoadField: r0 = r1->field_f
    //     0x99b72c: ldur            w0, [x1, #0xf]
    // 0x99b730: DecompressPointer r0
    //     0x99b730: add             x0, x0, HEAP, lsl #32
    // 0x99b734: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x99b734: ldur            w1, [x0, #0x17]
    // 0x99b738: DecompressPointer r1
    //     0x99b738: add             x1, x1, HEAP, lsl #32
    // 0x99b73c: r0 = _handleHasScrolledBodyChanged()
    //     0x99b73c: bl              #0x94376c  ; [package:flutter/src/widgets/nested_scroll_view.dart] NestedScrollViewState::_handleHasScrolledBodyChanged
    // 0x99b740: r0 = Null
    //     0x99b740: mov             x0, NULL
    // 0x99b744: LeaveFrame
    //     0x99b744: mov             SP, fp
    //     0x99b748: ldp             fp, lr, [SP], #0x10
    // 0x99b74c: ret
    //     0x99b74c: ret             
    // 0x99b750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99b750: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99b754: b               #0x99b72c
  }
  _ setParent(/* No info */) {
    // ** addr: 0x9a7a2c, size: 0x34
    // 0x9a7a2c: EnterFrame
    //     0x9a7a2c: stp             fp, lr, [SP, #-0x10]!
    //     0x9a7a30: mov             fp, SP
    // 0x9a7a34: CheckStackOverflow
    //     0x9a7a34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a7a38: cmp             SP, x16
    //     0x9a7a3c: b.ls            #0x9a7a58
    // 0x9a7a40: StoreField: r1->field_b = rNULL
    //     0x9a7a40: stur            NULL, [x1, #0xb]
    // 0x9a7a44: r0 = updateParent()
    //     0x9a7a44: bl              #0x99b494  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::updateParent
    // 0x9a7a48: r0 = Null
    //     0x9a7a48: mov             x0, NULL
    // 0x9a7a4c: LeaveFrame
    //     0x9a7a4c: mov             SP, fp
    //     0x9a7a50: ldp             fp, lr, [SP], #0x10
    // 0x9a7a54: ret
    //     0x9a7a54: ret             
    // 0x9a7a58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a7a58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a7a5c: b               #0x9a7a40
  }
  _ pointerScroll(/* No info */) {
    // ** addr: 0x9de170, size: 0xe70
    // 0x9de170: EnterFrame
    //     0x9de170: stp             fp, lr, [SP, #-0x10]!
    //     0x9de174: mov             fp, SP
    // 0x9de178: AllocStack(0x80)
    //     0x9de178: sub             SP, SP, #0x80
    // 0x9de17c: d1 = 0.000000
    //     0x9de17c: eor             v1.16b, v1.16b, v1.16b
    // 0x9de180: mov             x0, x1
    // 0x9de184: stur            x1, [fp, #-8]
    // 0x9de188: stur            d0, [fp, #-0x68]
    // 0x9de18c: CheckStackOverflow
    //     0x9de18c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9de190: cmp             SP, x16
    //     0x9de194: b.ls            #0x9def0c
    // 0x9de198: fcmp            d0, d1
    // 0x9de19c: b.ne            #0x9de1bc
    // 0x9de1a0: mov             x1, x0
    // 0x9de1a4: mov             v0.16b, v1.16b
    // 0x9de1a8: r0 = goBallistic()
    //     0x9de1a8: bl              #0xdb9198  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::goBallistic
    // 0x9de1ac: r0 = Null
    //     0x9de1ac: mov             x0, NULL
    // 0x9de1b0: LeaveFrame
    //     0x9de1b0: mov             SP, fp
    //     0x9de1b4: ldp             fp, lr, [SP], #0x10
    // 0x9de1b8: ret
    //     0x9de1b8: ret             
    // 0x9de1bc: mov             x1, x0
    // 0x9de1c0: r0 = goIdle()
    //     0x9de1c0: bl              #0xdb2ef4  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::goIdle
    // 0x9de1c4: ldur            d1, [fp, #-0x68]
    // 0x9de1c8: d0 = 0.000000
    //     0x9de1c8: eor             v0.16b, v0.16b, v0.16b
    // 0x9de1cc: fcmp            d0, d1
    // 0x9de1d0: b.le            #0x9de1e0
    // 0x9de1d4: r2 = Instance_ScrollDirection
    //     0x9de1d4: add             x2, PP, #0x4f, lsl #12  ; [pp+0x4f810] Obj!ScrollDirection@e35301
    //     0x9de1d8: ldr             x2, [x2, #0x810]
    // 0x9de1dc: b               #0x9de1e8
    // 0x9de1e0: r2 = Instance_ScrollDirection
    //     0x9de1e0: add             x2, PP, #0x4f, lsl #12  ; [pp+0x4f818] Obj!ScrollDirection@e35321
    //     0x9de1e4: ldr             x2, [x2, #0x818]
    // 0x9de1e8: ldur            x0, [fp, #-8]
    // 0x9de1ec: mov             x1, x0
    // 0x9de1f0: r0 = updateUserScrollDirection()
    //     0x9de1f0: bl              #0x679094  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::updateUserScrollDirection
    // 0x9de1f4: ldur            x1, [fp, #-8]
    // 0x9de1f8: r0 = _outerPosition()
    //     0x9de1f8: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x9de1fc: cmp             w0, NULL
    // 0x9de200: b.eq            #0x9def14
    // 0x9de204: LoadField: r1 = r0->field_63
    //     0x9de204: ldur            w1, [x0, #0x63]
    // 0x9de208: DecompressPointer r1
    //     0x9de208: add             x1, x1, HEAP, lsl #32
    // 0x9de20c: r2 = true
    //     0x9de20c: add             x2, NULL, #0x20  ; true
    // 0x9de210: r0 = value=()
    //     0x9de210: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x9de214: ldur            x1, [fp, #-8]
    // 0x9de218: r0 = _outerPosition()
    //     0x9de218: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x9de21c: cmp             w0, NULL
    // 0x9de220: b.eq            #0x9def18
    // 0x9de224: mov             x1, x0
    // 0x9de228: r0 = didStartScroll()
    //     0x9de228: bl              #0x679798  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didStartScroll
    // 0x9de22c: ldur            x1, [fp, #-8]
    // 0x9de230: LoadField: r0 = r1->field_1b
    //     0x9de230: ldur            w0, [x1, #0x1b]
    // 0x9de234: DecompressPointer r0
    //     0x9de234: add             x0, x0, HEAP, lsl #32
    // 0x9de238: r16 = Sentinel
    //     0x9de238: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9de23c: cmp             w0, w16
    // 0x9de240: b.eq            #0x9def1c
    // 0x9de244: LoadField: r2 = r0->field_3b
    //     0x9de244: ldur            w2, [x0, #0x3b]
    // 0x9de248: DecompressPointer r2
    //     0x9de248: add             x2, x2, HEAP, lsl #32
    // 0x9de24c: r16 = <_NestedScrollPosition>
    //     0x9de24c: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0x9de250: stp             x2, x16, [SP]
    // 0x9de254: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9de254: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9de258: r0 = cast()
    //     0x9de258: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0x9de25c: stur            x0, [fp, #-0x18]
    // 0x9de260: LoadField: r2 = r0->field_7
    //     0x9de260: ldur            w2, [x0, #7]
    // 0x9de264: DecompressPointer r2
    //     0x9de264: add             x2, x2, HEAP, lsl #32
    // 0x9de268: stur            x2, [fp, #-0x10]
    // 0x9de26c: str             x0, [SP]
    // 0x9de270: r0 = length()
    //     0x9de270: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x9de274: r1 = LoadInt32Instr(r0)
    //     0x9de274: sbfx            x1, x0, #1, #0x1f
    //     0x9de278: tbz             w0, #0, #0x9de280
    //     0x9de27c: ldur            x1, [x0, #7]
    // 0x9de280: ldur            x0, [fp, #-0x18]
    // 0x9de284: stur            x1, [fp, #-0x30]
    // 0x9de288: LoadField: r2 = r0->field_b
    //     0x9de288: ldur            w2, [x0, #0xb]
    // 0x9de28c: DecompressPointer r2
    //     0x9de28c: add             x2, x2, HEAP, lsl #32
    // 0x9de290: stur            x2, [fp, #-0x28]
    // 0x9de294: r3 = 0
    //     0x9de294: movz            x3, #0
    // 0x9de298: stur            x3, [fp, #-0x20]
    // 0x9de29c: CheckStackOverflow
    //     0x9de29c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9de2a0: cmp             SP, x16
    //     0x9de2a4: b.ls            #0x9def24
    // 0x9de2a8: str             x0, [SP]
    // 0x9de2ac: r0 = length()
    //     0x9de2ac: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x9de2b0: r1 = LoadInt32Instr(r0)
    //     0x9de2b0: sbfx            x1, x0, #1, #0x1f
    //     0x9de2b4: tbz             w0, #0, #0x9de2bc
    //     0x9de2b8: ldur            x1, [x0, #7]
    // 0x9de2bc: ldur            x2, [fp, #-0x30]
    // 0x9de2c0: cmp             x2, x1
    // 0x9de2c4: b.ne            #0x9deeec
    // 0x9de2c8: ldur            x3, [fp, #-0x20]
    // 0x9de2cc: cmp             x3, x1
    // 0x9de2d0: b.ge            #0x9de460
    // 0x9de2d4: ldur            x4, [fp, #-0x28]
    // 0x9de2d8: r0 = BoxInt64Instr(r3)
    //     0x9de2d8: sbfiz           x0, x3, #1, #0x1f
    //     0x9de2dc: cmp             x3, x0, asr #1
    //     0x9de2e0: b.eq            #0x9de2ec
    //     0x9de2e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9de2e8: stur            x3, [x0, #7]
    // 0x9de2ec: r1 = LoadClassIdInstr(r4)
    //     0x9de2ec: ldur            x1, [x4, #-1]
    //     0x9de2f0: ubfx            x1, x1, #0xc, #0x14
    // 0x9de2f4: stp             x0, x4, [SP]
    // 0x9de2f8: mov             x0, x1
    // 0x9de2fc: r0 = GDT[cid_x0 + 0x13037]()
    //     0x9de2fc: movz            x17, #0x3037
    //     0x9de300: movk            x17, #0x1, lsl #16
    //     0x9de304: add             lr, x0, x17
    //     0x9de308: ldr             lr, [x21, lr, lsl #3]
    //     0x9de30c: blr             lr
    // 0x9de310: ldur            x2, [fp, #-0x10]
    // 0x9de314: mov             x3, x0
    // 0x9de318: r1 = Null
    //     0x9de318: mov             x1, NULL
    // 0x9de31c: stur            x3, [fp, #-0x38]
    // 0x9de320: cmp             w2, NULL
    // 0x9de324: b.eq            #0x9de344
    // 0x9de328: LoadField: r4 = r2->field_1f
    //     0x9de328: ldur            w4, [x2, #0x1f]
    // 0x9de32c: DecompressPointer r4
    //     0x9de32c: add             x4, x4, HEAP, lsl #32
    // 0x9de330: r8 = C1X1
    //     0x9de330: ldr             x8, [PP, #0x6ee0]  ; [pp+0x6ee0] TypeParameter: C1X1
    // 0x9de334: LoadField: r9 = r4->field_7
    //     0x9de334: ldur            x9, [x4, #7]
    // 0x9de338: r3 = Null
    //     0x9de338: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f820] Null
    //     0x9de33c: ldr             x3, [x3, #0x820]
    // 0x9de340: blr             x9
    // 0x9de344: ldur            x0, [fp, #-0x20]
    // 0x9de348: add             x3, x0, #1
    // 0x9de34c: ldur            x4, [fp, #-0x38]
    // 0x9de350: stur            x3, [fp, #-0x40]
    // 0x9de354: cmp             w4, NULL
    // 0x9de358: b.ne            #0x9de38c
    // 0x9de35c: mov             x0, x4
    // 0x9de360: ldur            x2, [fp, #-0x10]
    // 0x9de364: r1 = Null
    //     0x9de364: mov             x1, NULL
    // 0x9de368: cmp             w2, NULL
    // 0x9de36c: b.eq            #0x9de38c
    // 0x9de370: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9de370: ldur            w4, [x2, #0x17]
    // 0x9de374: DecompressPointer r4
    //     0x9de374: add             x4, x4, HEAP, lsl #32
    // 0x9de378: r8 = X0
    //     0x9de378: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x9de37c: LoadField: r9 = r4->field_7
    //     0x9de37c: ldur            x9, [x4, #7]
    // 0x9de380: r3 = Null
    //     0x9de380: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f830] Null
    //     0x9de384: ldr             x3, [x3, #0x830]
    // 0x9de388: blr             x9
    // 0x9de38c: ldur            x1, [fp, #-0x38]
    // 0x9de390: LoadField: r2 = r1->field_63
    //     0x9de390: ldur            w2, [x1, #0x63]
    // 0x9de394: DecompressPointer r2
    //     0x9de394: add             x2, x2, HEAP, lsl #32
    // 0x9de398: stur            x2, [fp, #-0x48]
    // 0x9de39c: LoadField: r0 = r2->field_27
    //     0x9de39c: ldur            w0, [x2, #0x27]
    // 0x9de3a0: DecompressPointer r0
    //     0x9de3a0: add             x0, x0, HEAP, lsl #32
    // 0x9de3a4: r3 = 60
    //     0x9de3a4: movz            x3, #0x3c
    // 0x9de3a8: branchIfSmi(r0, 0x9de3b4)
    //     0x9de3a8: tbz             w0, #0, #0x9de3b4
    // 0x9de3ac: r3 = LoadClassIdInstr(r0)
    //     0x9de3ac: ldur            x3, [x0, #-1]
    //     0x9de3b0: ubfx            x3, x3, #0xc, #0x14
    // 0x9de3b4: r16 = true
    //     0x9de3b4: add             x16, NULL, #0x20  ; true
    // 0x9de3b8: stp             x16, x0, [SP]
    // 0x9de3bc: mov             x0, x3
    // 0x9de3c0: mov             lr, x0
    // 0x9de3c4: ldr             lr, [x21, lr, lsl #3]
    // 0x9de3c8: blr             lr
    // 0x9de3cc: tbz             w0, #4, #0x9de3e0
    // 0x9de3d0: ldur            x1, [fp, #-0x48]
    // 0x9de3d4: r0 = true
    //     0x9de3d4: add             x0, NULL, #0x20  ; true
    // 0x9de3d8: StoreField: r1->field_27 = r0
    //     0x9de3d8: stur            w0, [x1, #0x27]
    // 0x9de3dc: r0 = notifyListeners()
    //     0x9de3dc: bl              #0x6462b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners
    // 0x9de3e0: ldur            x0, [fp, #-0x38]
    // 0x9de3e4: LoadField: r2 = r0->field_67
    //     0x9de3e4: ldur            w2, [x0, #0x67]
    // 0x9de3e8: DecompressPointer r2
    //     0x9de3e8: add             x2, x2, HEAP, lsl #32
    // 0x9de3ec: stur            x2, [fp, #-0x48]
    // 0x9de3f0: cmp             w2, NULL
    // 0x9de3f4: b.eq            #0x9def2c
    // 0x9de3f8: mov             x1, x0
    // 0x9de3fc: r0 = copyWith()
    //     0x9de3fc: bl              #0xd891b8  ; [package:flutter/src/widgets/scroll_position.dart] _ScrollPosition&ViewportOffset&ScrollMetrics::copyWith
    // 0x9de400: mov             x2, x0
    // 0x9de404: ldur            x0, [fp, #-0x38]
    // 0x9de408: stur            x2, [fp, #-0x50]
    // 0x9de40c: LoadField: r1 = r0->field_27
    //     0x9de40c: ldur            w1, [x0, #0x27]
    // 0x9de410: DecompressPointer r1
    //     0x9de410: add             x1, x1, HEAP, lsl #32
    // 0x9de414: LoadField: r0 = r1->field_4b
    //     0x9de414: ldur            w0, [x1, #0x4b]
    // 0x9de418: DecompressPointer r0
    //     0x9de418: add             x0, x0, HEAP, lsl #32
    // 0x9de41c: mov             x1, x0
    // 0x9de420: r0 = _currentElement()
    //     0x9de420: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x9de424: ldur            x1, [fp, #-0x48]
    // 0x9de428: r2 = LoadClassIdInstr(r1)
    //     0x9de428: ldur            x2, [x1, #-1]
    //     0x9de42c: ubfx            x2, x2, #0xc, #0x14
    // 0x9de430: mov             x3, x0
    // 0x9de434: mov             x0, x2
    // 0x9de438: ldur            x2, [fp, #-0x50]
    // 0x9de43c: r0 = GDT[cid_x0 + 0x3ea0]()
    //     0x9de43c: movz            x17, #0x3ea0
    //     0x9de440: add             lr, x0, x17
    //     0x9de444: ldr             lr, [x21, lr, lsl #3]
    //     0x9de448: blr             lr
    // 0x9de44c: ldur            x3, [fp, #-0x40]
    // 0x9de450: ldur            x0, [fp, #-0x18]
    // 0x9de454: ldur            x2, [fp, #-0x28]
    // 0x9de458: ldur            x1, [fp, #-0x30]
    // 0x9de45c: b               #0x9de298
    // 0x9de460: ldur            x1, [fp, #-8]
    // 0x9de464: LoadField: r0 = r1->field_1b
    //     0x9de464: ldur            w0, [x1, #0x1b]
    // 0x9de468: DecompressPointer r0
    //     0x9de468: add             x0, x0, HEAP, lsl #32
    // 0x9de46c: r16 = Sentinel
    //     0x9de46c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9de470: cmp             w0, w16
    // 0x9de474: b.eq            #0x9def30
    // 0x9de478: LoadField: r2 = r0->field_3b
    //     0x9de478: ldur            w2, [x0, #0x3b]
    // 0x9de47c: DecompressPointer r2
    //     0x9de47c: add             x2, x2, HEAP, lsl #32
    // 0x9de480: r16 = <_NestedScrollPosition>
    //     0x9de480: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0x9de484: stp             x2, x16, [SP]
    // 0x9de488: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9de488: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9de48c: r0 = cast()
    //     0x9de48c: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0x9de490: str             x0, [SP]
    // 0x9de494: r0 = length()
    //     0x9de494: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x9de498: cbnz            w0, #0x9de4bc
    // 0x9de49c: ldur            x1, [fp, #-8]
    // 0x9de4a0: r0 = _outerPosition()
    //     0x9de4a0: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x9de4a4: cmp             w0, NULL
    // 0x9de4a8: b.eq            #0x9def38
    // 0x9de4ac: mov             x1, x0
    // 0x9de4b0: ldur            d0, [fp, #-0x68]
    // 0x9de4b4: r0 = applyClampedPointerSignalUpdate()
    //     0x9de4b4: bl              #0x9defe0  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyClampedPointerSignalUpdate
    // 0x9de4b8: b               #0x9decb8
    // 0x9de4bc: ldur            d1, [fp, #-0x68]
    // 0x9de4c0: d0 = 0.000000
    //     0x9de4c0: eor             v0.16b, v0.16b, v0.16b
    // 0x9de4c4: fcmp            d1, d0
    // 0x9de4c8: b.le            #0x9de990
    // 0x9de4cc: ldur            x1, [fp, #-8]
    // 0x9de4d0: LoadField: r0 = r1->field_1b
    //     0x9de4d0: ldur            w0, [x1, #0x1b]
    // 0x9de4d4: DecompressPointer r0
    //     0x9de4d4: add             x0, x0, HEAP, lsl #32
    // 0x9de4d8: LoadField: r2 = r0->field_3b
    //     0x9de4d8: ldur            w2, [x0, #0x3b]
    // 0x9de4dc: DecompressPointer r2
    //     0x9de4dc: add             x2, x2, HEAP, lsl #32
    // 0x9de4e0: r16 = <_NestedScrollPosition>
    //     0x9de4e0: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0x9de4e4: stp             x2, x16, [SP]
    // 0x9de4e8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9de4e8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9de4ec: r0 = cast()
    //     0x9de4ec: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0x9de4f0: stur            x0, [fp, #-0x28]
    // 0x9de4f4: LoadField: r2 = r0->field_7
    //     0x9de4f4: ldur            w2, [x0, #7]
    // 0x9de4f8: DecompressPointer r2
    //     0x9de4f8: add             x2, x2, HEAP, lsl #32
    // 0x9de4fc: stur            x2, [fp, #-0x10]
    // 0x9de500: str             x0, [SP]
    // 0x9de504: r0 = length()
    //     0x9de504: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x9de508: r1 = LoadInt32Instr(r0)
    //     0x9de508: sbfx            x1, x0, #1, #0x1f
    //     0x9de50c: tbz             w0, #0, #0x9de514
    //     0x9de510: ldur            x1, [x0, #7]
    // 0x9de514: ldur            d0, [fp, #-0x68]
    // 0x9de518: stur            x1, [fp, #-0x30]
    // 0x9de51c: r0 = inline_Allocate_Double()
    //     0x9de51c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x9de520: add             x0, x0, #0x10
    //     0x9de524: cmp             x2, x0
    //     0x9de528: b.ls            #0x9def3c
    //     0x9de52c: str             x0, [THR, #0x50]  ; THR::top
    //     0x9de530: sub             x0, x0, #0xf
    //     0x9de534: movz            x2, #0xe15c
    //     0x9de538: movk            x2, #0x3, lsl #16
    //     0x9de53c: stur            x2, [x0, #-1]
    // 0x9de540: StoreField: r0->field_7 = d0
    //     0x9de540: stur            d0, [x0, #7]
    // 0x9de544: ldur            x2, [fp, #-0x28]
    // 0x9de548: LoadField: r3 = r2->field_b
    //     0x9de548: ldur            w3, [x2, #0xb]
    // 0x9de54c: DecompressPointer r3
    //     0x9de54c: add             x3, x3, HEAP, lsl #32
    // 0x9de550: stur            x3, [fp, #-0x48]
    // 0x9de554: mov             x4, x0
    // 0x9de558: r0 = 0
    //     0x9de558: movz            x0, #0
    // 0x9de55c: stur            x4, [fp, #-0x38]
    // 0x9de560: stur            x0, [fp, #-0x20]
    // 0x9de564: CheckStackOverflow
    //     0x9de564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9de568: cmp             SP, x16
    //     0x9de56c: b.ls            #0x9def54
    // 0x9de570: str             x2, [SP]
    // 0x9de574: r0 = length()
    //     0x9de574: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x9de578: r1 = LoadInt32Instr(r0)
    //     0x9de578: sbfx            x1, x0, #1, #0x1f
    //     0x9de57c: tbz             w0, #0, #0x9de584
    //     0x9de580: ldur            x1, [x0, #7]
    // 0x9de584: ldur            x2, [fp, #-0x30]
    // 0x9de588: cmp             x2, x1
    // 0x9de58c: b.ne            #0x9dee8c
    // 0x9de590: ldur            x3, [fp, #-0x20]
    // 0x9de594: cmp             x3, x1
    // 0x9de598: b.ge            #0x9de7c8
    // 0x9de59c: ldur            x4, [fp, #-0x48]
    // 0x9de5a0: r0 = BoxInt64Instr(r3)
    //     0x9de5a0: sbfiz           x0, x3, #1, #0x1f
    //     0x9de5a4: cmp             x3, x0, asr #1
    //     0x9de5a8: b.eq            #0x9de5b4
    //     0x9de5ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9de5b0: stur            x3, [x0, #7]
    // 0x9de5b4: r1 = LoadClassIdInstr(r4)
    //     0x9de5b4: ldur            x1, [x4, #-1]
    //     0x9de5b8: ubfx            x1, x1, #0xc, #0x14
    // 0x9de5bc: stp             x0, x4, [SP]
    // 0x9de5c0: mov             x0, x1
    // 0x9de5c4: r0 = GDT[cid_x0 + 0x13037]()
    //     0x9de5c4: movz            x17, #0x3037
    //     0x9de5c8: movk            x17, #0x1, lsl #16
    //     0x9de5cc: add             lr, x0, x17
    //     0x9de5d0: ldr             lr, [x21, lr, lsl #3]
    //     0x9de5d4: blr             lr
    // 0x9de5d8: ldur            x2, [fp, #-0x10]
    // 0x9de5dc: mov             x3, x0
    // 0x9de5e0: r1 = Null
    //     0x9de5e0: mov             x1, NULL
    // 0x9de5e4: stur            x3, [fp, #-0x50]
    // 0x9de5e8: cmp             w2, NULL
    // 0x9de5ec: b.eq            #0x9de60c
    // 0x9de5f0: LoadField: r4 = r2->field_1f
    //     0x9de5f0: ldur            w4, [x2, #0x1f]
    // 0x9de5f4: DecompressPointer r4
    //     0x9de5f4: add             x4, x4, HEAP, lsl #32
    // 0x9de5f8: r8 = C1X1
    //     0x9de5f8: ldr             x8, [PP, #0x6ee0]  ; [pp+0x6ee0] TypeParameter: C1X1
    // 0x9de5fc: LoadField: r9 = r4->field_7
    //     0x9de5fc: ldur            x9, [x4, #7]
    // 0x9de600: r3 = Null
    //     0x9de600: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f840] Null
    //     0x9de604: ldr             x3, [x3, #0x840]
    // 0x9de608: blr             x9
    // 0x9de60c: ldur            x0, [fp, #-0x20]
    // 0x9de610: add             x3, x0, #1
    // 0x9de614: ldur            x4, [fp, #-0x50]
    // 0x9de618: stur            x3, [fp, #-0x40]
    // 0x9de61c: cmp             w4, NULL
    // 0x9de620: b.ne            #0x9de654
    // 0x9de624: mov             x0, x4
    // 0x9de628: ldur            x2, [fp, #-0x10]
    // 0x9de62c: r1 = Null
    //     0x9de62c: mov             x1, NULL
    // 0x9de630: cmp             w2, NULL
    // 0x9de634: b.eq            #0x9de654
    // 0x9de638: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9de638: ldur            w4, [x2, #0x17]
    // 0x9de63c: DecompressPointer r4
    //     0x9de63c: add             x4, x4, HEAP, lsl #32
    // 0x9de640: r8 = X0
    //     0x9de640: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x9de644: LoadField: r9 = r4->field_7
    //     0x9de644: ldur            x9, [x4, #7]
    // 0x9de648: r3 = Null
    //     0x9de648: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f850] Null
    //     0x9de64c: ldr             x3, [x3, #0x850]
    // 0x9de650: blr             x9
    // 0x9de654: ldur            x1, [fp, #-0x50]
    // 0x9de658: d1 = 0.000000
    //     0x9de658: eor             v1.16b, v1.16b, v1.16b
    // 0x9de65c: LoadField: r0 = r1->field_3f
    //     0x9de65c: ldur            w0, [x1, #0x3f]
    // 0x9de660: DecompressPointer r0
    //     0x9de660: add             x0, x0, HEAP, lsl #32
    // 0x9de664: cmp             w0, NULL
    // 0x9de668: b.eq            #0x9def5c
    // 0x9de66c: LoadField: d0 = r0->field_7
    //     0x9de66c: ldur            d0, [x0, #7]
    // 0x9de670: fcmp            d1, d0
    // 0x9de674: b.le            #0x9de7a4
    // 0x9de678: ldur            x0, [fp, #-0x38]
    // 0x9de67c: ldur            d0, [fp, #-0x68]
    // 0x9de680: r0 = applyClampedPointerSignalUpdate()
    //     0x9de680: bl              #0x9defe0  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyClampedPointerSignalUpdate
    // 0x9de684: stur            d0, [fp, #-0x70]
    // 0x9de688: r1 = inline_Allocate_Double()
    //     0x9de688: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0x9de68c: add             x1, x1, #0x10
    //     0x9de690: cmp             x0, x1
    //     0x9de694: b.ls            #0x9def60
    //     0x9de698: str             x1, [THR, #0x50]  ; THR::top
    //     0x9de69c: sub             x1, x1, #0xf
    //     0x9de6a0: movz            x0, #0xe15c
    //     0x9de6a4: movk            x0, #0x3, lsl #16
    //     0x9de6a8: stur            x0, [x1, #-1]
    // 0x9de6ac: StoreField: r1->field_7 = d0
    //     0x9de6ac: stur            d0, [x1, #7]
    // 0x9de6b0: ldur            x2, [fp, #-0x38]
    // 0x9de6b4: stur            x1, [fp, #-0x50]
    // 0x9de6b8: r0 = 60
    //     0x9de6b8: movz            x0, #0x3c
    // 0x9de6bc: branchIfSmi(r2, 0x9de6c8)
    //     0x9de6bc: tbz             w2, #0, #0x9de6c8
    // 0x9de6c0: r0 = LoadClassIdInstr(r2)
    //     0x9de6c0: ldur            x0, [x2, #-1]
    //     0x9de6c4: ubfx            x0, x0, #0xc, #0x14
    // 0x9de6c8: stp             x1, x2, [SP]
    // 0x9de6cc: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x9de6cc: sub             lr, x0, #0xfe3
    //     0x9de6d0: ldr             lr, [x21, lr, lsl #3]
    //     0x9de6d4: blr             lr
    // 0x9de6d8: tbnz            w0, #4, #0x9de6e8
    // 0x9de6dc: ldur            x1, [fp, #-0x38]
    // 0x9de6e0: d0 = 0.000000
    //     0x9de6e0: eor             v0.16b, v0.16b, v0.16b
    // 0x9de6e4: b               #0x9de79c
    // 0x9de6e8: ldur            x1, [fp, #-0x38]
    // 0x9de6ec: r0 = 60
    //     0x9de6ec: movz            x0, #0x3c
    // 0x9de6f0: branchIfSmi(r1, 0x9de6fc)
    //     0x9de6f0: tbz             w1, #0, #0x9de6fc
    // 0x9de6f4: r0 = LoadClassIdInstr(r1)
    //     0x9de6f4: ldur            x0, [x1, #-1]
    //     0x9de6f8: ubfx            x0, x0, #0xc, #0x14
    // 0x9de6fc: ldur            x16, [fp, #-0x50]
    // 0x9de700: stp             x16, x1, [SP]
    // 0x9de704: r0 = GDT[cid_x0 + -0xfd2]()
    //     0x9de704: sub             lr, x0, #0xfd2
    //     0x9de708: ldr             lr, [x21, lr, lsl #3]
    //     0x9de70c: blr             lr
    // 0x9de710: tbnz            w0, #4, #0x9de720
    // 0x9de714: ldur            x1, [fp, #-0x50]
    // 0x9de718: d0 = 0.000000
    //     0x9de718: eor             v0.16b, v0.16b, v0.16b
    // 0x9de71c: b               #0x9de79c
    // 0x9de720: ldur            x1, [fp, #-0x38]
    // 0x9de724: r0 = 60
    //     0x9de724: movz            x0, #0x3c
    // 0x9de728: branchIfSmi(r1, 0x9de734)
    //     0x9de728: tbz             w1, #0, #0x9de734
    // 0x9de72c: r0 = LoadClassIdInstr(r1)
    //     0x9de72c: ldur            x0, [x1, #-1]
    //     0x9de730: ubfx            x0, x0, #0xc, #0x14
    // 0x9de734: cmp             x0, #0x3e
    // 0x9de738: b.ne            #0x9de788
    // 0x9de73c: d0 = 0.000000
    //     0x9de73c: eor             v0.16b, v0.16b, v0.16b
    // 0x9de740: LoadField: d1 = r1->field_7
    //     0x9de740: ldur            d1, [x1, #7]
    // 0x9de744: fcmp            d1, d0
    // 0x9de748: b.ne            #0x9de780
    // 0x9de74c: ldur            d2, [fp, #-0x70]
    // 0x9de750: fadd            d3, d1, d2
    // 0x9de754: r1 = inline_Allocate_Double()
    //     0x9de754: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0x9de758: add             x1, x1, #0x10
    //     0x9de75c: cmp             x0, x1
    //     0x9de760: b.ls            #0x9def74
    //     0x9de764: str             x1, [THR, #0x50]  ; THR::top
    //     0x9de768: sub             x1, x1, #0xf
    //     0x9de76c: movz            x0, #0xe15c
    //     0x9de770: movk            x0, #0x3, lsl #16
    //     0x9de774: stur            x0, [x1, #-1]
    // 0x9de778: StoreField: r1->field_7 = d3
    //     0x9de778: stur            d3, [x1, #7]
    // 0x9de77c: b               #0x9de79c
    // 0x9de780: ldur            d2, [fp, #-0x70]
    // 0x9de784: b               #0x9de790
    // 0x9de788: ldur            d2, [fp, #-0x70]
    // 0x9de78c: d0 = 0.000000
    //     0x9de78c: eor             v0.16b, v0.16b, v0.16b
    // 0x9de790: fcmp            d2, d2
    // 0x9de794: b.vc            #0x9de79c
    // 0x9de798: ldur            x1, [fp, #-0x50]
    // 0x9de79c: mov             x4, x1
    // 0x9de7a0: b               #0x9de7b0
    // 0x9de7a4: ldur            x1, [fp, #-0x38]
    // 0x9de7a8: mov             v0.16b, v1.16b
    // 0x9de7ac: mov             x4, x1
    // 0x9de7b0: ldur            x0, [fp, #-0x40]
    // 0x9de7b4: ldur            d0, [fp, #-0x68]
    // 0x9de7b8: ldur            x2, [fp, #-0x28]
    // 0x9de7bc: ldur            x3, [fp, #-0x48]
    // 0x9de7c0: ldur            x1, [fp, #-0x30]
    // 0x9de7c4: b               #0x9de55c
    // 0x9de7c8: ldur            x1, [fp, #-0x38]
    // 0x9de7cc: d0 = 0.000000
    //     0x9de7cc: eor             v0.16b, v0.16b, v0.16b
    // 0x9de7d0: LoadField: d1 = r1->field_7
    //     0x9de7d0: ldur            d1, [x1, #7]
    // 0x9de7d4: stur            d1, [fp, #-0x70]
    // 0x9de7d8: fcmp            d1, d0
    // 0x9de7dc: b.eq            #0x9decb8
    // 0x9de7e0: ldur            x1, [fp, #-8]
    // 0x9de7e4: r0 = _outerPosition()
    //     0x9de7e4: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x9de7e8: cmp             w0, NULL
    // 0x9de7ec: b.eq            #0x9def88
    // 0x9de7f0: mov             x1, x0
    // 0x9de7f4: ldur            d0, [fp, #-0x70]
    // 0x9de7f8: r0 = applyClampedPointerSignalUpdate()
    //     0x9de7f8: bl              #0x9defe0  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyClampedPointerSignalUpdate
    // 0x9de7fc: mov             v1.16b, v0.16b
    // 0x9de800: d0 = 0.000000
    //     0x9de800: eor             v0.16b, v0.16b, v0.16b
    // 0x9de804: stur            d1, [fp, #-0x70]
    // 0x9de808: fcmp            d1, d0
    // 0x9de80c: b.eq            #0x9decb8
    // 0x9de810: ldur            x1, [fp, #-8]
    // 0x9de814: LoadField: r0 = r1->field_1b
    //     0x9de814: ldur            w0, [x1, #0x1b]
    // 0x9de818: DecompressPointer r0
    //     0x9de818: add             x0, x0, HEAP, lsl #32
    // 0x9de81c: r16 = Sentinel
    //     0x9de81c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9de820: cmp             w0, w16
    // 0x9de824: b.eq            #0x9def8c
    // 0x9de828: LoadField: r2 = r0->field_3b
    //     0x9de828: ldur            w2, [x0, #0x3b]
    // 0x9de82c: DecompressPointer r2
    //     0x9de82c: add             x2, x2, HEAP, lsl #32
    // 0x9de830: r16 = <_NestedScrollPosition>
    //     0x9de830: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0x9de834: stp             x2, x16, [SP]
    // 0x9de838: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9de838: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9de83c: r0 = cast()
    //     0x9de83c: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0x9de840: stur            x0, [fp, #-0x38]
    // 0x9de844: LoadField: r2 = r0->field_7
    //     0x9de844: ldur            w2, [x0, #7]
    // 0x9de848: DecompressPointer r2
    //     0x9de848: add             x2, x2, HEAP, lsl #32
    // 0x9de84c: stur            x2, [fp, #-0x10]
    // 0x9de850: str             x0, [SP]
    // 0x9de854: r0 = length()
    //     0x9de854: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x9de858: r1 = LoadInt32Instr(r0)
    //     0x9de858: sbfx            x1, x0, #1, #0x1f
    //     0x9de85c: tbz             w0, #0, #0x9de864
    //     0x9de860: ldur            x1, [x0, #7]
    // 0x9de864: ldur            x0, [fp, #-0x38]
    // 0x9de868: stur            x1, [fp, #-0x30]
    // 0x9de86c: LoadField: r2 = r0->field_b
    //     0x9de86c: ldur            w2, [x0, #0xb]
    // 0x9de870: DecompressPointer r2
    //     0x9de870: add             x2, x2, HEAP, lsl #32
    // 0x9de874: stur            x2, [fp, #-0x48]
    // 0x9de878: r3 = 0
    //     0x9de878: movz            x3, #0
    // 0x9de87c: stur            x3, [fp, #-0x20]
    // 0x9de880: CheckStackOverflow
    //     0x9de880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9de884: cmp             SP, x16
    //     0x9de888: b.ls            #0x9def94
    // 0x9de88c: str             x0, [SP]
    // 0x9de890: r0 = length()
    //     0x9de890: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x9de894: r1 = LoadInt32Instr(r0)
    //     0x9de894: sbfx            x1, x0, #1, #0x1f
    //     0x9de898: tbz             w0, #0, #0x9de8a0
    //     0x9de89c: ldur            x1, [x0, #7]
    // 0x9de8a0: ldur            x2, [fp, #-0x30]
    // 0x9de8a4: cmp             x2, x1
    // 0x9de8a8: b.ne            #0x9dee6c
    // 0x9de8ac: ldur            x3, [fp, #-0x20]
    // 0x9de8b0: cmp             x3, x1
    // 0x9de8b4: b.ge            #0x9decb8
    // 0x9de8b8: ldur            x4, [fp, #-0x48]
    // 0x9de8bc: r0 = BoxInt64Instr(r3)
    //     0x9de8bc: sbfiz           x0, x3, #1, #0x1f
    //     0x9de8c0: cmp             x3, x0, asr #1
    //     0x9de8c4: b.eq            #0x9de8d0
    //     0x9de8c8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9de8cc: stur            x3, [x0, #7]
    // 0x9de8d0: r1 = LoadClassIdInstr(r4)
    //     0x9de8d0: ldur            x1, [x4, #-1]
    //     0x9de8d4: ubfx            x1, x1, #0xc, #0x14
    // 0x9de8d8: stp             x0, x4, [SP]
    // 0x9de8dc: mov             x0, x1
    // 0x9de8e0: r0 = GDT[cid_x0 + 0x13037]()
    //     0x9de8e0: movz            x17, #0x3037
    //     0x9de8e4: movk            x17, #0x1, lsl #16
    //     0x9de8e8: add             lr, x0, x17
    //     0x9de8ec: ldr             lr, [x21, lr, lsl #3]
    //     0x9de8f0: blr             lr
    // 0x9de8f4: ldur            x2, [fp, #-0x10]
    // 0x9de8f8: mov             x3, x0
    // 0x9de8fc: r1 = Null
    //     0x9de8fc: mov             x1, NULL
    // 0x9de900: stur            x3, [fp, #-0x50]
    // 0x9de904: cmp             w2, NULL
    // 0x9de908: b.eq            #0x9de928
    // 0x9de90c: LoadField: r4 = r2->field_1f
    //     0x9de90c: ldur            w4, [x2, #0x1f]
    // 0x9de910: DecompressPointer r4
    //     0x9de910: add             x4, x4, HEAP, lsl #32
    // 0x9de914: r8 = C1X1
    //     0x9de914: ldr             x8, [PP, #0x6ee0]  ; [pp+0x6ee0] TypeParameter: C1X1
    // 0x9de918: LoadField: r9 = r4->field_7
    //     0x9de918: ldur            x9, [x4, #7]
    // 0x9de91c: r3 = Null
    //     0x9de91c: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f860] Null
    //     0x9de920: ldr             x3, [x3, #0x860]
    // 0x9de924: blr             x9
    // 0x9de928: ldur            x0, [fp, #-0x20]
    // 0x9de92c: add             x3, x0, #1
    // 0x9de930: ldur            x4, [fp, #-0x50]
    // 0x9de934: stur            x3, [fp, #-0x40]
    // 0x9de938: cmp             w4, NULL
    // 0x9de93c: b.ne            #0x9de970
    // 0x9de940: mov             x0, x4
    // 0x9de944: ldur            x2, [fp, #-0x10]
    // 0x9de948: r1 = Null
    //     0x9de948: mov             x1, NULL
    // 0x9de94c: cmp             w2, NULL
    // 0x9de950: b.eq            #0x9de970
    // 0x9de954: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9de954: ldur            w4, [x2, #0x17]
    // 0x9de958: DecompressPointer r4
    //     0x9de958: add             x4, x4, HEAP, lsl #32
    // 0x9de95c: r8 = X0
    //     0x9de95c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x9de960: LoadField: r9 = r4->field_7
    //     0x9de960: ldur            x9, [x4, #7]
    // 0x9de964: r3 = Null
    //     0x9de964: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f870] Null
    //     0x9de968: ldr             x3, [x3, #0x870]
    // 0x9de96c: blr             x9
    // 0x9de970: ldur            x1, [fp, #-0x50]
    // 0x9de974: ldur            d0, [fp, #-0x70]
    // 0x9de978: r0 = applyClampedPointerSignalUpdate()
    //     0x9de978: bl              #0x9defe0  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyClampedPointerSignalUpdate
    // 0x9de97c: ldur            x3, [fp, #-0x40]
    // 0x9de980: ldur            x0, [fp, #-0x38]
    // 0x9de984: ldur            x2, [fp, #-0x48]
    // 0x9de988: ldur            x1, [fp, #-0x30]
    // 0x9de98c: b               #0x9de87c
    // 0x9de990: fcmp            d1, d0
    // 0x9de994: b.eq            #0x9decb8
    // 0x9de998: ldur            x1, [fp, #-8]
    // 0x9de99c: LoadField: r0 = r1->field_1b
    //     0x9de99c: ldur            w0, [x1, #0x1b]
    // 0x9de9a0: DecompressPointer r0
    //     0x9de9a0: add             x0, x0, HEAP, lsl #32
    // 0x9de9a4: LoadField: r2 = r0->field_3b
    //     0x9de9a4: ldur            w2, [x0, #0x3b]
    // 0x9de9a8: DecompressPointer r2
    //     0x9de9a8: add             x2, x2, HEAP, lsl #32
    // 0x9de9ac: r16 = <_NestedScrollPosition>
    //     0x9de9ac: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0x9de9b0: stp             x2, x16, [SP]
    // 0x9de9b4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9de9b4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9de9b8: r0 = cast()
    //     0x9de9b8: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0x9de9bc: stur            x0, [fp, #-0x48]
    // 0x9de9c0: LoadField: r2 = r0->field_7
    //     0x9de9c0: ldur            w2, [x0, #7]
    // 0x9de9c4: DecompressPointer r2
    //     0x9de9c4: add             x2, x2, HEAP, lsl #32
    // 0x9de9c8: stur            x2, [fp, #-0x10]
    // 0x9de9cc: str             x0, [SP]
    // 0x9de9d0: r0 = length()
    //     0x9de9d0: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x9de9d4: r1 = LoadInt32Instr(r0)
    //     0x9de9d4: sbfx            x1, x0, #1, #0x1f
    //     0x9de9d8: tbz             w0, #0, #0x9de9e0
    //     0x9de9dc: ldur            x1, [x0, #7]
    // 0x9de9e0: ldur            x0, [fp, #-0x48]
    // 0x9de9e4: stur            x1, [fp, #-0x30]
    // 0x9de9e8: LoadField: r2 = r0->field_b
    //     0x9de9e8: ldur            w2, [x0, #0xb]
    // 0x9de9ec: DecompressPointer r2
    //     0x9de9ec: add             x2, x2, HEAP, lsl #32
    // 0x9de9f0: stur            x2, [fp, #-0x58]
    // 0x9de9f4: r4 = 0.000000
    //     0x9de9f4: ldr             x4, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x9de9f8: r3 = 0
    //     0x9de9f8: movz            x3, #0
    // 0x9de9fc: stur            x4, [fp, #-0x50]
    // 0x9dea00: stur            x3, [fp, #-0x20]
    // 0x9dea04: CheckStackOverflow
    //     0x9dea04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9dea08: cmp             SP, x16
    //     0x9dea0c: b.ls            #0x9def9c
    // 0x9dea10: str             x0, [SP]
    // 0x9dea14: r0 = length()
    //     0x9dea14: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x9dea18: r1 = LoadInt32Instr(r0)
    //     0x9dea18: sbfx            x1, x0, #1, #0x1f
    //     0x9dea1c: tbz             w0, #0, #0x9dea24
    //     0x9dea20: ldur            x1, [x0, #7]
    // 0x9dea24: ldur            x2, [fp, #-0x30]
    // 0x9dea28: cmp             x2, x1
    // 0x9dea2c: b.ne            #0x9deeac
    // 0x9dea30: ldur            x3, [fp, #-0x20]
    // 0x9dea34: cmp             x3, x1
    // 0x9dea38: b.ge            #0x9dec84
    // 0x9dea3c: ldur            x4, [fp, #-0x58]
    // 0x9dea40: r0 = BoxInt64Instr(r3)
    //     0x9dea40: sbfiz           x0, x3, #1, #0x1f
    //     0x9dea44: cmp             x3, x0, asr #1
    //     0x9dea48: b.eq            #0x9dea54
    //     0x9dea4c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9dea50: stur            x3, [x0, #7]
    // 0x9dea54: r1 = LoadClassIdInstr(r4)
    //     0x9dea54: ldur            x1, [x4, #-1]
    //     0x9dea58: ubfx            x1, x1, #0xc, #0x14
    // 0x9dea5c: stp             x0, x4, [SP]
    // 0x9dea60: mov             x0, x1
    // 0x9dea64: r0 = GDT[cid_x0 + 0x13037]()
    //     0x9dea64: movz            x17, #0x3037
    //     0x9dea68: movk            x17, #0x1, lsl #16
    //     0x9dea6c: add             lr, x0, x17
    //     0x9dea70: ldr             lr, [x21, lr, lsl #3]
    //     0x9dea74: blr             lr
    // 0x9dea78: ldur            x2, [fp, #-0x10]
    // 0x9dea7c: mov             x3, x0
    // 0x9dea80: r1 = Null
    //     0x9dea80: mov             x1, NULL
    // 0x9dea84: stur            x3, [fp, #-0x60]
    // 0x9dea88: cmp             w2, NULL
    // 0x9dea8c: b.eq            #0x9deaac
    // 0x9dea90: LoadField: r4 = r2->field_1f
    //     0x9dea90: ldur            w4, [x2, #0x1f]
    // 0x9dea94: DecompressPointer r4
    //     0x9dea94: add             x4, x4, HEAP, lsl #32
    // 0x9dea98: r8 = C1X1
    //     0x9dea98: ldr             x8, [PP, #0x6ee0]  ; [pp+0x6ee0] TypeParameter: C1X1
    // 0x9dea9c: LoadField: r9 = r4->field_7
    //     0x9dea9c: ldur            x9, [x4, #7]
    // 0x9deaa0: r3 = Null
    //     0x9deaa0: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f880] Null
    //     0x9deaa4: ldr             x3, [x3, #0x880]
    // 0x9deaa8: blr             x9
    // 0x9deaac: ldur            x0, [fp, #-0x20]
    // 0x9deab0: add             x3, x0, #1
    // 0x9deab4: ldur            x4, [fp, #-0x60]
    // 0x9deab8: stur            x3, [fp, #-0x40]
    // 0x9deabc: cmp             w4, NULL
    // 0x9deac0: b.ne            #0x9deaf4
    // 0x9deac4: mov             x0, x4
    // 0x9deac8: ldur            x2, [fp, #-0x10]
    // 0x9deacc: r1 = Null
    //     0x9deacc: mov             x1, NULL
    // 0x9dead0: cmp             w2, NULL
    // 0x9dead4: b.eq            #0x9deaf4
    // 0x9dead8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9dead8: ldur            w4, [x2, #0x17]
    // 0x9deadc: DecompressPointer r4
    //     0x9deadc: add             x4, x4, HEAP, lsl #32
    // 0x9deae0: r8 = X0
    //     0x9deae0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x9deae4: LoadField: r9 = r4->field_7
    //     0x9deae4: ldur            x9, [x4, #7]
    // 0x9deae8: r3 = Null
    //     0x9deae8: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f890] Null
    //     0x9deaec: ldr             x3, [x3, #0x890]
    // 0x9deaf0: blr             x9
    // 0x9deaf4: ldur            x0, [fp, #-0x50]
    // 0x9deaf8: ldur            x1, [fp, #-0x60]
    // 0x9deafc: ldur            d0, [fp, #-0x68]
    // 0x9deb00: r0 = applyClampedPointerSignalUpdate()
    //     0x9deb00: bl              #0x9defe0  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyClampedPointerSignalUpdate
    // 0x9deb04: stur            d0, [fp, #-0x70]
    // 0x9deb08: r1 = inline_Allocate_Double()
    //     0x9deb08: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0x9deb0c: add             x1, x1, #0x10
    //     0x9deb10: cmp             x0, x1
    //     0x9deb14: b.ls            #0x9defa4
    //     0x9deb18: str             x1, [THR, #0x50]  ; THR::top
    //     0x9deb1c: sub             x1, x1, #0xf
    //     0x9deb20: movz            x0, #0xe15c
    //     0x9deb24: movk            x0, #0x3, lsl #16
    //     0x9deb28: stur            x0, [x1, #-1]
    // 0x9deb2c: StoreField: r1->field_7 = d0
    //     0x9deb2c: stur            d0, [x1, #7]
    // 0x9deb30: ldur            x2, [fp, #-0x50]
    // 0x9deb34: stur            x1, [fp, #-0x60]
    // 0x9deb38: r0 = 60
    //     0x9deb38: movz            x0, #0x3c
    // 0x9deb3c: branchIfSmi(r2, 0x9deb48)
    //     0x9deb3c: tbz             w2, #0, #0x9deb48
    // 0x9deb40: r0 = LoadClassIdInstr(r2)
    //     0x9deb40: ldur            x0, [x2, #-1]
    //     0x9deb44: ubfx            x0, x0, #0xc, #0x14
    // 0x9deb48: stp             x1, x2, [SP]
    // 0x9deb4c: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x9deb4c: sub             lr, x0, #0xfe3
    //     0x9deb50: ldr             lr, [x21, lr, lsl #3]
    //     0x9deb54: blr             lr
    // 0x9deb58: tbnz            w0, #4, #0x9deb64
    // 0x9deb5c: ldur            x4, [fp, #-0x60]
    // 0x9deb60: b               #0x9dec70
    // 0x9deb64: ldur            x1, [fp, #-0x50]
    // 0x9deb68: r0 = 60
    //     0x9deb68: movz            x0, #0x3c
    // 0x9deb6c: branchIfSmi(r1, 0x9deb78)
    //     0x9deb6c: tbz             w1, #0, #0x9deb78
    // 0x9deb70: r0 = LoadClassIdInstr(r1)
    //     0x9deb70: ldur            x0, [x1, #-1]
    //     0x9deb74: ubfx            x0, x0, #0xc, #0x14
    // 0x9deb78: ldur            x16, [fp, #-0x60]
    // 0x9deb7c: stp             x16, x1, [SP]
    // 0x9deb80: r0 = GDT[cid_x0 + -0xfd2]()
    //     0x9deb80: sub             lr, x0, #0xfd2
    //     0x9deb84: ldr             lr, [x21, lr, lsl #3]
    //     0x9deb88: blr             lr
    // 0x9deb8c: tbnz            w0, #4, #0x9deb98
    // 0x9deb90: ldur            x4, [fp, #-0x50]
    // 0x9deb94: b               #0x9dec70
    // 0x9deb98: ldur            x1, [fp, #-0x50]
    // 0x9deb9c: r0 = 60
    //     0x9deb9c: movz            x0, #0x3c
    // 0x9deba0: branchIfSmi(r1, 0x9debac)
    //     0x9deba0: tbz             w1, #0, #0x9debac
    // 0x9deba4: r0 = LoadClassIdInstr(r1)
    //     0x9deba4: ldur            x0, [x1, #-1]
    //     0x9deba8: ubfx            x0, x0, #0xc, #0x14
    // 0x9debac: cmp             x0, #0x3e
    // 0x9debb0: b.ne            #0x9dec0c
    // 0x9debb4: d0 = 0.000000
    //     0x9debb4: eor             v0.16b, v0.16b, v0.16b
    // 0x9debb8: LoadField: d1 = r1->field_7
    //     0x9debb8: ldur            d1, [x1, #7]
    // 0x9debbc: fcmp            d1, d0
    // 0x9debc0: b.ne            #0x9dec04
    // 0x9debc4: ldur            d2, [fp, #-0x70]
    // 0x9debc8: fadd            d3, d1, d2
    // 0x9debcc: fmul            d4, d3, d1
    // 0x9debd0: fmul            d1, d4, d2
    // 0x9debd4: r0 = inline_Allocate_Double()
    //     0x9debd4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x9debd8: add             x0, x0, #0x10
    //     0x9debdc: cmp             x1, x0
    //     0x9debe0: b.ls            #0x9defb8
    //     0x9debe4: str             x0, [THR, #0x50]  ; THR::top
    //     0x9debe8: sub             x0, x0, #0xf
    //     0x9debec: movz            x1, #0xe15c
    //     0x9debf0: movk            x1, #0x3, lsl #16
    //     0x9debf4: stur            x1, [x0, #-1]
    // 0x9debf8: StoreField: r0->field_7 = d1
    //     0x9debf8: stur            d1, [x0, #7]
    // 0x9debfc: mov             x4, x0
    // 0x9dec00: b               #0x9dec70
    // 0x9dec04: ldur            d2, [fp, #-0x70]
    // 0x9dec08: b               #0x9dec14
    // 0x9dec0c: ldur            d2, [fp, #-0x70]
    // 0x9dec10: d0 = 0.000000
    //     0x9dec10: eor             v0.16b, v0.16b, v0.16b
    // 0x9dec14: r0 = 60
    //     0x9dec14: movz            x0, #0x3c
    // 0x9dec18: branchIfSmi(r1, 0x9dec24)
    //     0x9dec18: tbz             w1, #0, #0x9dec24
    // 0x9dec1c: r0 = LoadClassIdInstr(r1)
    //     0x9dec1c: ldur            x0, [x1, #-1]
    //     0x9dec20: ubfx            x0, x0, #0xc, #0x14
    // 0x9dec24: stp             xzr, x1, [SP]
    // 0x9dec28: mov             lr, x0
    // 0x9dec2c: ldr             lr, [x21, lr, lsl #3]
    // 0x9dec30: blr             lr
    // 0x9dec34: tbnz            w0, #4, #0x9dec58
    // 0x9dec38: ldur            d0, [fp, #-0x70]
    // 0x9dec3c: fcmp            d0, #0.0
    // 0x9dec40: b.vs            #0x9dec5c
    // 0x9dec44: b.ne            #0x9dec50
    // 0x9dec48: r0 = 0.000000
    //     0x9dec48: fmov            x0, d0
    // 0x9dec4c: cmp             x0, #0
    // 0x9dec50: b.ge            #0x9dec5c
    // 0x9dec54: b               #0x9dec64
    // 0x9dec58: ldur            d0, [fp, #-0x70]
    // 0x9dec5c: fcmp            d0, d0
    // 0x9dec60: b.vc            #0x9dec6c
    // 0x9dec64: ldur            x4, [fp, #-0x60]
    // 0x9dec68: b               #0x9dec70
    // 0x9dec6c: ldur            x4, [fp, #-0x50]
    // 0x9dec70: ldur            x3, [fp, #-0x40]
    // 0x9dec74: ldur            x0, [fp, #-0x48]
    // 0x9dec78: ldur            x2, [fp, #-0x58]
    // 0x9dec7c: ldur            x1, [fp, #-0x30]
    // 0x9dec80: b               #0x9de9fc
    // 0x9dec84: ldur            x0, [fp, #-0x50]
    // 0x9dec88: d0 = 0.000000
    //     0x9dec88: eor             v0.16b, v0.16b, v0.16b
    // 0x9dec8c: LoadField: d1 = r0->field_7
    //     0x9dec8c: ldur            d1, [x0, #7]
    // 0x9dec90: stur            d1, [fp, #-0x68]
    // 0x9dec94: fcmp            d1, d0
    // 0x9dec98: b.eq            #0x9decb8
    // 0x9dec9c: ldur            x1, [fp, #-8]
    // 0x9deca0: r0 = _outerPosition()
    //     0x9deca0: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x9deca4: cmp             w0, NULL
    // 0x9deca8: b.eq            #0x9defc8
    // 0x9decac: mov             x1, x0
    // 0x9decb0: ldur            d0, [fp, #-0x68]
    // 0x9decb4: r0 = applyClampedPointerSignalUpdate()
    //     0x9decb4: bl              #0x9defe0  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyClampedPointerSignalUpdate
    // 0x9decb8: ldur            x0, [fp, #-8]
    // 0x9decbc: mov             x1, x0
    // 0x9decc0: r0 = _outerPosition()
    //     0x9decc0: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0x9decc4: cmp             w0, NULL
    // 0x9decc8: b.eq            #0x9defcc
    // 0x9deccc: mov             x1, x0
    // 0x9decd0: r0 = didEndScroll()
    //     0x9decd0: bl              #0x679988  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didEndScroll
    // 0x9decd4: ldur            x1, [fp, #-8]
    // 0x9decd8: LoadField: r0 = r1->field_1b
    //     0x9decd8: ldur            w0, [x1, #0x1b]
    // 0x9decdc: DecompressPointer r0
    //     0x9decdc: add             x0, x0, HEAP, lsl #32
    // 0x9dece0: r16 = Sentinel
    //     0x9dece0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9dece4: cmp             w0, w16
    // 0x9dece8: b.eq            #0x9defd0
    // 0x9decec: LoadField: r2 = r0->field_3b
    //     0x9decec: ldur            w2, [x0, #0x3b]
    // 0x9decf0: DecompressPointer r2
    //     0x9decf0: add             x2, x2, HEAP, lsl #32
    // 0x9decf4: r16 = <_NestedScrollPosition>
    //     0x9decf4: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0x9decf8: stp             x2, x16, [SP]
    // 0x9decfc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9decfc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9ded00: r0 = cast()
    //     0x9ded00: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0x9ded04: stur            x0, [fp, #-0x50]
    // 0x9ded08: LoadField: r2 = r0->field_7
    //     0x9ded08: ldur            w2, [x0, #7]
    // 0x9ded0c: DecompressPointer r2
    //     0x9ded0c: add             x2, x2, HEAP, lsl #32
    // 0x9ded10: stur            x2, [fp, #-0x10]
    // 0x9ded14: str             x0, [SP]
    // 0x9ded18: r0 = length()
    //     0x9ded18: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x9ded1c: r1 = LoadInt32Instr(r0)
    //     0x9ded1c: sbfx            x1, x0, #1, #0x1f
    //     0x9ded20: tbz             w0, #0, #0x9ded28
    //     0x9ded24: ldur            x1, [x0, #7]
    // 0x9ded28: ldur            x0, [fp, #-0x50]
    // 0x9ded2c: stur            x1, [fp, #-0x30]
    // 0x9ded30: LoadField: r2 = r0->field_b
    //     0x9ded30: ldur            w2, [x0, #0xb]
    // 0x9ded34: DecompressPointer r2
    //     0x9ded34: add             x2, x2, HEAP, lsl #32
    // 0x9ded38: stur            x2, [fp, #-0x58]
    // 0x9ded3c: r3 = 0
    //     0x9ded3c: movz            x3, #0
    // 0x9ded40: stur            x3, [fp, #-0x20]
    // 0x9ded44: CheckStackOverflow
    //     0x9ded44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ded48: cmp             SP, x16
    //     0x9ded4c: b.ls            #0x9defd8
    // 0x9ded50: str             x0, [SP]
    // 0x9ded54: r0 = length()
    //     0x9ded54: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0x9ded58: r1 = LoadInt32Instr(r0)
    //     0x9ded58: sbfx            x1, x0, #1, #0x1f
    //     0x9ded5c: tbz             w0, #0, #0x9ded64
    //     0x9ded60: ldur            x1, [x0, #7]
    // 0x9ded64: ldur            x2, [fp, #-0x30]
    // 0x9ded68: cmp             x2, x1
    // 0x9ded6c: b.ne            #0x9deecc
    // 0x9ded70: ldur            x3, [fp, #-0x20]
    // 0x9ded74: cmp             x3, x1
    // 0x9ded78: b.ge            #0x9dee50
    // 0x9ded7c: ldur            x4, [fp, #-0x58]
    // 0x9ded80: r0 = BoxInt64Instr(r3)
    //     0x9ded80: sbfiz           x0, x3, #1, #0x1f
    //     0x9ded84: cmp             x3, x0, asr #1
    //     0x9ded88: b.eq            #0x9ded94
    //     0x9ded8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9ded90: stur            x3, [x0, #7]
    // 0x9ded94: r1 = LoadClassIdInstr(r4)
    //     0x9ded94: ldur            x1, [x4, #-1]
    //     0x9ded98: ubfx            x1, x1, #0xc, #0x14
    // 0x9ded9c: stp             x0, x4, [SP]
    // 0x9deda0: mov             x0, x1
    // 0x9deda4: r0 = GDT[cid_x0 + 0x13037]()
    //     0x9deda4: movz            x17, #0x3037
    //     0x9deda8: movk            x17, #0x1, lsl #16
    //     0x9dedac: add             lr, x0, x17
    //     0x9dedb0: ldr             lr, [x21, lr, lsl #3]
    //     0x9dedb4: blr             lr
    // 0x9dedb8: ldur            x2, [fp, #-0x10]
    // 0x9dedbc: mov             x3, x0
    // 0x9dedc0: r1 = Null
    //     0x9dedc0: mov             x1, NULL
    // 0x9dedc4: stur            x3, [fp, #-0x60]
    // 0x9dedc8: cmp             w2, NULL
    // 0x9dedcc: b.eq            #0x9dedec
    // 0x9dedd0: LoadField: r4 = r2->field_1f
    //     0x9dedd0: ldur            w4, [x2, #0x1f]
    // 0x9dedd4: DecompressPointer r4
    //     0x9dedd4: add             x4, x4, HEAP, lsl #32
    // 0x9dedd8: r8 = C1X1
    //     0x9dedd8: ldr             x8, [PP, #0x6ee0]  ; [pp+0x6ee0] TypeParameter: C1X1
    // 0x9deddc: LoadField: r9 = r4->field_7
    //     0x9deddc: ldur            x9, [x4, #7]
    // 0x9dede0: r3 = Null
    //     0x9dede0: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f8a0] Null
    //     0x9dede4: ldr             x3, [x3, #0x8a0]
    // 0x9dede8: blr             x9
    // 0x9dedec: ldur            x0, [fp, #-0x20]
    // 0x9dedf0: add             x3, x0, #1
    // 0x9dedf4: ldur            x4, [fp, #-0x60]
    // 0x9dedf8: stur            x3, [fp, #-0x40]
    // 0x9dedfc: cmp             w4, NULL
    // 0x9dee00: b.ne            #0x9dee34
    // 0x9dee04: mov             x0, x4
    // 0x9dee08: ldur            x2, [fp, #-0x10]
    // 0x9dee0c: r1 = Null
    //     0x9dee0c: mov             x1, NULL
    // 0x9dee10: cmp             w2, NULL
    // 0x9dee14: b.eq            #0x9dee34
    // 0x9dee18: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9dee18: ldur            w4, [x2, #0x17]
    // 0x9dee1c: DecompressPointer r4
    //     0x9dee1c: add             x4, x4, HEAP, lsl #32
    // 0x9dee20: r8 = X0
    //     0x9dee20: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x9dee24: LoadField: r9 = r4->field_7
    //     0x9dee24: ldur            x9, [x4, #7]
    // 0x9dee28: r3 = Null
    //     0x9dee28: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4f8b0] Null
    //     0x9dee2c: ldr             x3, [x3, #0x8b0]
    // 0x9dee30: blr             x9
    // 0x9dee34: ldur            x1, [fp, #-0x60]
    // 0x9dee38: r0 = didEndScroll()
    //     0x9dee38: bl              #0x679988  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didEndScroll
    // 0x9dee3c: ldur            x3, [fp, #-0x40]
    // 0x9dee40: ldur            x0, [fp, #-0x50]
    // 0x9dee44: ldur            x2, [fp, #-0x58]
    // 0x9dee48: ldur            x1, [fp, #-0x30]
    // 0x9dee4c: b               #0x9ded40
    // 0x9dee50: ldur            x1, [fp, #-8]
    // 0x9dee54: d0 = 0.000000
    //     0x9dee54: eor             v0.16b, v0.16b, v0.16b
    // 0x9dee58: r0 = goBallistic()
    //     0x9dee58: bl              #0xdb9198  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::goBallistic
    // 0x9dee5c: r0 = Null
    //     0x9dee5c: mov             x0, NULL
    // 0x9dee60: LeaveFrame
    //     0x9dee60: mov             SP, fp
    //     0x9dee64: ldp             fp, lr, [SP], #0x10
    // 0x9dee68: ret
    //     0x9dee68: ret             
    // 0x9dee6c: ldur            x0, [fp, #-0x38]
    // 0x9dee70: r0 = ConcurrentModificationError()
    //     0x9dee70: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x9dee74: mov             x1, x0
    // 0x9dee78: ldur            x0, [fp, #-0x38]
    // 0x9dee7c: StoreField: r1->field_b = r0
    //     0x9dee7c: stur            w0, [x1, #0xb]
    // 0x9dee80: mov             x0, x1
    // 0x9dee84: r0 = Throw()
    //     0x9dee84: bl              #0xec04b8  ; ThrowStub
    // 0x9dee88: brk             #0
    // 0x9dee8c: ldur            x0, [fp, #-0x28]
    // 0x9dee90: r0 = ConcurrentModificationError()
    //     0x9dee90: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x9dee94: mov             x1, x0
    // 0x9dee98: ldur            x0, [fp, #-0x28]
    // 0x9dee9c: StoreField: r1->field_b = r0
    //     0x9dee9c: stur            w0, [x1, #0xb]
    // 0x9deea0: mov             x0, x1
    // 0x9deea4: r0 = Throw()
    //     0x9deea4: bl              #0xec04b8  ; ThrowStub
    // 0x9deea8: brk             #0
    // 0x9deeac: ldur            x0, [fp, #-0x48]
    // 0x9deeb0: r0 = ConcurrentModificationError()
    //     0x9deeb0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x9deeb4: mov             x1, x0
    // 0x9deeb8: ldur            x0, [fp, #-0x48]
    // 0x9deebc: StoreField: r1->field_b = r0
    //     0x9deebc: stur            w0, [x1, #0xb]
    // 0x9deec0: mov             x0, x1
    // 0x9deec4: r0 = Throw()
    //     0x9deec4: bl              #0xec04b8  ; ThrowStub
    // 0x9deec8: brk             #0
    // 0x9deecc: ldur            x0, [fp, #-0x50]
    // 0x9deed0: r0 = ConcurrentModificationError()
    //     0x9deed0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x9deed4: mov             x1, x0
    // 0x9deed8: ldur            x0, [fp, #-0x50]
    // 0x9deedc: StoreField: r1->field_b = r0
    //     0x9deedc: stur            w0, [x1, #0xb]
    // 0x9deee0: mov             x0, x1
    // 0x9deee4: r0 = Throw()
    //     0x9deee4: bl              #0xec04b8  ; ThrowStub
    // 0x9deee8: brk             #0
    // 0x9deeec: ldur            x0, [fp, #-0x18]
    // 0x9deef0: r0 = ConcurrentModificationError()
    //     0x9deef0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x9deef4: mov             x1, x0
    // 0x9deef8: ldur            x0, [fp, #-0x18]
    // 0x9deefc: StoreField: r1->field_b = r0
    //     0x9deefc: stur            w0, [x1, #0xb]
    // 0x9def00: mov             x0, x1
    // 0x9def04: r0 = Throw()
    //     0x9def04: bl              #0xec04b8  ; ThrowStub
    // 0x9def08: brk             #0
    // 0x9def0c: r0 = StackOverflowSharedWithFPURegs()
    //     0x9def0c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x9def10: b               #0x9de198
    // 0x9def14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9def14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9def18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9def18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9def1c: r9 = _innerController
    //     0x9def1c: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0x9def20: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9def20: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9def24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9def24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9def28: b               #0x9de2a8
    // 0x9def2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9def2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9def30: r9 = _innerController
    //     0x9def30: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0x9def34: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9def34: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9def38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9def38: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9def3c: SaveReg d0
    //     0x9def3c: str             q0, [SP, #-0x10]!
    // 0x9def40: SaveReg r1
    //     0x9def40: str             x1, [SP, #-8]!
    // 0x9def44: r0 = AllocateDouble()
    //     0x9def44: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9def48: RestoreReg r1
    //     0x9def48: ldr             x1, [SP], #8
    // 0x9def4c: RestoreReg d0
    //     0x9def4c: ldr             q0, [SP], #0x10
    // 0x9def50: b               #0x9de540
    // 0x9def54: r0 = StackOverflowSharedWithFPURegs()
    //     0x9def54: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x9def58: b               #0x9de570
    // 0x9def5c: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9def5c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9def60: SaveReg d0
    //     0x9def60: str             q0, [SP, #-0x10]!
    // 0x9def64: r0 = AllocateDouble()
    //     0x9def64: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9def68: mov             x1, x0
    // 0x9def6c: RestoreReg d0
    //     0x9def6c: ldr             q0, [SP], #0x10
    // 0x9def70: b               #0x9de6ac
    // 0x9def74: stp             q0, q3, [SP, #-0x20]!
    // 0x9def78: r0 = AllocateDouble()
    //     0x9def78: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9def7c: mov             x1, x0
    // 0x9def80: ldp             q0, q3, [SP], #0x20
    // 0x9def84: b               #0x9de778
    // 0x9def88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9def88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9def8c: r9 = _innerController
    //     0x9def8c: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0x9def90: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x9def90: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x9def94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9def94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9def98: b               #0x9de88c
    // 0x9def9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9def9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9defa0: b               #0x9dea10
    // 0x9defa4: SaveReg d0
    //     0x9defa4: str             q0, [SP, #-0x10]!
    // 0x9defa8: r0 = AllocateDouble()
    //     0x9defa8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9defac: mov             x1, x0
    // 0x9defb0: RestoreReg d0
    //     0x9defb0: ldr             q0, [SP], #0x10
    // 0x9defb4: b               #0x9deb2c
    // 0x9defb8: stp             q0, q1, [SP, #-0x20]!
    // 0x9defbc: r0 = AllocateDouble()
    //     0x9defbc: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9defc0: ldp             q0, q1, [SP], #0x20
    // 0x9defc4: b               #0x9debf8
    // 0x9defc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9defc8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9defcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9defcc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9defd0: r9 = _innerController
    //     0x9defd0: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0x9defd4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9defd4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9defd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9defd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9defdc: b               #0x9ded50
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa802d4, size: 0x98
    // 0xa802d4: EnterFrame
    //     0xa802d4: stp             fp, lr, [SP, #-0x10]!
    //     0xa802d8: mov             fp, SP
    // 0xa802dc: AllocStack(0x8)
    //     0xa802dc: sub             SP, SP, #8
    // 0xa802e0: SetupParameters(_NestedScrollCoordinator this /* r1 => r0, fp-0x8 */)
    //     0xa802e0: mov             x0, x1
    //     0xa802e4: stur            x1, [fp, #-8]
    // 0xa802e8: CheckStackOverflow
    //     0xa802e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa802ec: cmp             SP, x16
    //     0xa802f0: b.ls            #0xa80354
    // 0xa802f4: LoadField: r1 = r0->field_23
    //     0xa802f4: ldur            w1, [x0, #0x23]
    // 0xa802f8: DecompressPointer r1
    //     0xa802f8: add             x1, x1, HEAP, lsl #32
    // 0xa802fc: cmp             w1, NULL
    // 0xa80300: b.eq            #0xa8030c
    // 0xa80304: r0 = dispose()
    //     0xa80304: bl              #0x6794b4  ; [package:flutter/src/widgets/scroll_activity.dart] ScrollDragController::dispose
    // 0xa80308: ldur            x0, [fp, #-8]
    // 0xa8030c: StoreField: r0->field_23 = rNULL
    //     0xa8030c: stur            NULL, [x0, #0x23]
    // 0xa80310: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa80310: ldur            w1, [x0, #0x17]
    // 0xa80314: DecompressPointer r1
    //     0xa80314: add             x1, x1, HEAP, lsl #32
    // 0xa80318: r16 = Sentinel
    //     0xa80318: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa8031c: cmp             w1, w16
    // 0xa80320: b.eq            #0xa8035c
    // 0xa80324: r0 = dispose()
    //     0xa80324: bl              #0xa876d8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xa80328: ldur            x0, [fp, #-8]
    // 0xa8032c: LoadField: r1 = r0->field_1b
    //     0xa8032c: ldur            w1, [x0, #0x1b]
    // 0xa80330: DecompressPointer r1
    //     0xa80330: add             x1, x1, HEAP, lsl #32
    // 0xa80334: r16 = Sentinel
    //     0xa80334: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa80338: cmp             w1, w16
    // 0xa8033c: b.eq            #0xa80364
    // 0xa80340: r0 = dispose()
    //     0xa80340: bl              #0xa876d8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xa80344: r0 = Null
    //     0xa80344: mov             x0, NULL
    // 0xa80348: LeaveFrame
    //     0xa80348: mov             SP, fp
    //     0xa8034c: ldp             fp, lr, [SP], #0x10
    // 0xa80350: ret
    //     0xa80350: ret             
    // 0xa80354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa80354: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa80358: b               #0xa802f4
    // 0xa8035c: r9 = _outerController
    //     0xa8035c: ldr             x9, [PP, #0x7018]  ; [pp+0x7018] Field <_NestedScrollCoordinator@303016527._outerController@303016527>: late (offset: 0x18)
    // 0xa80360: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa80360: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa80364: r9 = _innerController
    //     0xa80364: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0xa80368: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa80368: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ createInnerBallisticScrollActivity(/* No info */) {
    // ** addr: 0xbf94bc, size: 0x90
    // 0xbf94bc: EnterFrame
    //     0xbf94bc: stp             fp, lr, [SP, #-0x10]!
    //     0xbf94c0: mov             fp, SP
    // 0xbf94c4: AllocStack(0x18)
    //     0xbf94c4: sub             SP, SP, #0x18
    // 0xbf94c8: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* d0 => d1, fp-0x18 */)
    //     0xbf94c8: mov             x0, x2
    //     0xbf94cc: mov             v1.16b, v0.16b
    //     0xbf94d0: stur            x2, [fp, #-0x10]
    //     0xbf94d4: stur            d0, [fp, #-0x18]
    // 0xbf94d8: CheckStackOverflow
    //     0xbf94d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf94dc: cmp             SP, x16
    //     0xbf94e0: b.ls            #0xbf9544
    // 0xbf94e4: LoadField: r3 = r0->field_23
    //     0xbf94e4: ldur            w3, [x0, #0x23]
    // 0xbf94e8: DecompressPointer r3
    //     0xbf94e8: add             x3, x3, HEAP, lsl #32
    // 0xbf94ec: mov             x2, x0
    // 0xbf94f0: mov             v0.16b, v1.16b
    // 0xbf94f4: stur            x3, [fp, #-8]
    // 0xbf94f8: r0 = _getMetrics()
    //     0xbf94f8: bl              #0xbf9cb8  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_getMetrics
    // 0xbf94fc: ldur            x1, [fp, #-8]
    // 0xbf9500: r2 = LoadClassIdInstr(r1)
    //     0xbf9500: ldur            x2, [x1, #-1]
    //     0xbf9504: ubfx            x2, x2, #0xc, #0x14
    // 0xbf9508: mov             x16, x0
    // 0xbf950c: mov             x0, x2
    // 0xbf9510: mov             x2, x16
    // 0xbf9514: ldur            d0, [fp, #-0x18]
    // 0xbf9518: r0 = GDT[cid_x0 + -0xff1]()
    //     0xbf9518: sub             lr, x0, #0xff1
    //     0xbf951c: ldr             lr, [x21, lr, lsl #3]
    //     0xbf9520: blr             lr
    // 0xbf9524: ldur            x1, [fp, #-0x10]
    // 0xbf9528: mov             x2, x0
    // 0xbf952c: r3 = Instance__NestedBallisticScrollActivityMode
    //     0xbf952c: ldr             x3, [PP, #0x75c0]  ; [pp+0x75c0] Obj!_NestedBallisticScrollActivityMode@e33da1
    // 0xbf9530: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xbf9530: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xbf9534: r0 = createBallisticScrollActivity()
    //     0xbf9534: bl              #0xbf954c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::createBallisticScrollActivity
    // 0xbf9538: LeaveFrame
    //     0xbf9538: mov             SP, fp
    //     0xbf953c: ldp             fp, lr, [SP], #0x10
    // 0xbf9540: ret
    //     0xbf9540: ret             
    // 0xbf9544: r0 = StackOverflowSharedWithFPURegs()
    //     0xbf9544: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xbf9548: b               #0xbf94e4
  }
  _ _getMetrics(/* No info */) {
    // ** addr: 0xbf9cb8, size: 0x80c
    // 0xbf9cb8: EnterFrame
    //     0xbf9cb8: stp             fp, lr, [SP, #-0x10]!
    //     0xbf9cbc: mov             fp, SP
    // 0xbf9cc0: AllocStack(0x50)
    //     0xbf9cc0: sub             SP, SP, #0x50
    // 0xbf9cc4: SetupParameters(_NestedScrollCoordinator this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x30 */)
    //     0xbf9cc4: mov             x0, x1
    //     0xbf9cc8: stur            x1, [fp, #-8]
    //     0xbf9ccc: stur            x2, [fp, #-0x10]
    //     0xbf9cd0: stur            d0, [fp, #-0x30]
    // 0xbf9cd4: CheckStackOverflow
    //     0xbf9cd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf9cd8: cmp             SP, x16
    //     0xbf9cdc: b.ls            #0xbfa398
    // 0xbf9ce0: LoadField: r1 = r2->field_3f
    //     0xbf9ce0: ldur            w1, [x2, #0x3f]
    // 0xbf9ce4: DecompressPointer r1
    //     0xbf9ce4: add             x1, x1, HEAP, lsl #32
    // 0xbf9ce8: cmp             w1, NULL
    // 0xbf9cec: b.eq            #0xbfa3a0
    // 0xbf9cf0: LoadField: r3 = r2->field_2f
    //     0xbf9cf0: ldur            w3, [x2, #0x2f]
    // 0xbf9cf4: DecompressPointer r3
    //     0xbf9cf4: add             x3, x3, HEAP, lsl #32
    // 0xbf9cf8: cmp             w3, NULL
    // 0xbf9cfc: b.eq            #0xbfa3a4
    // 0xbf9d00: LoadField: d1 = r1->field_7
    //     0xbf9d00: ldur            d1, [x1, #7]
    // 0xbf9d04: LoadField: d2 = r3->field_7
    //     0xbf9d04: ldur            d2, [x3, #7]
    // 0xbf9d08: fcmp            d1, d2
    // 0xbf9d0c: b.ne            #0xbf9e28
    // 0xbf9d10: mov             x1, x0
    // 0xbf9d14: r0 = _outerPosition()
    //     0xbf9d14: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbf9d18: cmp             w0, NULL
    // 0xbf9d1c: b.eq            #0xbfa3a8
    // 0xbf9d20: LoadField: r2 = r0->field_3f
    //     0xbf9d20: ldur            w2, [x0, #0x3f]
    // 0xbf9d24: DecompressPointer r2
    //     0xbf9d24: add             x2, x2, HEAP, lsl #32
    // 0xbf9d28: stur            x2, [fp, #-0x18]
    // 0xbf9d2c: cmp             w2, NULL
    // 0xbf9d30: b.eq            #0xbfa3ac
    // 0xbf9d34: ldur            x1, [fp, #-8]
    // 0xbf9d38: r0 = _outerPosition()
    //     0xbf9d38: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbf9d3c: cmp             w0, NULL
    // 0xbf9d40: b.eq            #0xbfa3b0
    // 0xbf9d44: LoadField: r2 = r0->field_2f
    //     0xbf9d44: ldur            w2, [x0, #0x2f]
    // 0xbf9d48: DecompressPointer r2
    //     0xbf9d48: add             x2, x2, HEAP, lsl #32
    // 0xbf9d4c: stur            x2, [fp, #-0x20]
    // 0xbf9d50: cmp             w2, NULL
    // 0xbf9d54: b.eq            #0xbfa3b4
    // 0xbf9d58: ldur            x1, [fp, #-8]
    // 0xbf9d5c: r0 = _outerPosition()
    //     0xbf9d5c: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbf9d60: cmp             w0, NULL
    // 0xbf9d64: b.eq            #0xbfa3b8
    // 0xbf9d68: LoadField: r1 = r0->field_33
    //     0xbf9d68: ldur            w1, [x0, #0x33]
    // 0xbf9d6c: DecompressPointer r1
    //     0xbf9d6c: add             x1, x1, HEAP, lsl #32
    // 0xbf9d70: cmp             w1, NULL
    // 0xbf9d74: b.eq            #0xbfa3bc
    // 0xbf9d78: ldur            x0, [fp, #-0x18]
    // 0xbf9d7c: LoadField: d0 = r0->field_7
    //     0xbf9d7c: ldur            d0, [x0, #7]
    // 0xbf9d80: ldur            x2, [fp, #-0x20]
    // 0xbf9d84: LoadField: d1 = r2->field_7
    //     0xbf9d84: ldur            d1, [x2, #7]
    // 0xbf9d88: fcmp            d1, d0
    // 0xbf9d8c: b.le            #0xbf9d98
    // 0xbf9d90: mov             v0.16b, v1.16b
    // 0xbf9d94: b               #0xbf9dbc
    // 0xbf9d98: LoadField: d1 = r1->field_7
    //     0xbf9d98: ldur            d1, [x1, #7]
    // 0xbf9d9c: fcmp            d0, d1
    // 0xbf9da0: b.le            #0xbf9dac
    // 0xbf9da4: mov             v0.16b, v1.16b
    // 0xbf9da8: b               #0xbf9dbc
    // 0xbf9dac: LoadField: d2 = r0->field_7
    //     0xbf9dac: ldur            d2, [x0, #7]
    // 0xbf9db0: fcmp            d2, d2
    // 0xbf9db4: b.vc            #0xbf9dbc
    // 0xbf9db8: mov             v0.16b, v1.16b
    // 0xbf9dbc: ldur            x1, [fp, #-8]
    // 0xbf9dc0: stur            d0, [fp, #-0x28]
    // 0xbf9dc4: r0 = _outerPosition()
    //     0xbf9dc4: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbf9dc8: cmp             w0, NULL
    // 0xbf9dcc: b.eq            #0xbfa3c0
    // 0xbf9dd0: LoadField: r2 = r0->field_2f
    //     0xbf9dd0: ldur            w2, [x0, #0x2f]
    // 0xbf9dd4: DecompressPointer r2
    //     0xbf9dd4: add             x2, x2, HEAP, lsl #32
    // 0xbf9dd8: stur            x2, [fp, #-0x18]
    // 0xbf9ddc: cmp             w2, NULL
    // 0xbf9de0: b.eq            #0xbfa3c4
    // 0xbf9de4: ldur            x1, [fp, #-8]
    // 0xbf9de8: r0 = _outerPosition()
    //     0xbf9de8: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbf9dec: cmp             w0, NULL
    // 0xbf9df0: b.eq            #0xbfa3c8
    // 0xbf9df4: LoadField: r1 = r0->field_33
    //     0xbf9df4: ldur            w1, [x0, #0x33]
    // 0xbf9df8: DecompressPointer r1
    //     0xbf9df8: add             x1, x1, HEAP, lsl #32
    // 0xbf9dfc: cmp             w1, NULL
    // 0xbf9e00: b.eq            #0xbfa3cc
    // 0xbf9e04: ldur            x0, [fp, #-0x18]
    // 0xbf9e08: LoadField: d0 = r0->field_7
    //     0xbf9e08: ldur            d0, [x0, #7]
    // 0xbf9e0c: LoadField: d1 = r1->field_7
    //     0xbf9e0c: ldur            d1, [x1, #7]
    // 0xbf9e10: ldur            d4, [fp, #-0x28]
    // 0xbf9e14: mov             v3.16b, v0.16b
    // 0xbf9e18: mov             v2.16b, v1.16b
    // 0xbf9e1c: d1 = 0.000000
    //     0xbf9e1c: eor             v1.16b, v1.16b, v1.16b
    // 0xbf9e20: d0 = 0.000000
    //     0xbf9e20: eor             v0.16b, v0.16b, v0.16b
    // 0xbf9e24: b               #0xbfa1c4
    // 0xbf9e28: fcmp            d2, d1
    // 0xbf9e2c: b.le            #0xbf9e68
    // 0xbf9e30: fsub            d3, d1, d2
    // 0xbf9e34: ldur            x1, [fp, #-8]
    // 0xbf9e38: stur            d3, [fp, #-0x28]
    // 0xbf9e3c: r0 = _outerPosition()
    //     0xbf9e3c: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbf9e40: cmp             w0, NULL
    // 0xbf9e44: b.eq            #0xbfa3d0
    // 0xbf9e48: LoadField: r1 = r0->field_2f
    //     0xbf9e48: ldur            w1, [x0, #0x2f]
    // 0xbf9e4c: DecompressPointer r1
    //     0xbf9e4c: add             x1, x1, HEAP, lsl #32
    // 0xbf9e50: cmp             w1, NULL
    // 0xbf9e54: b.eq            #0xbfa3d4
    // 0xbf9e58: LoadField: d0 = r1->field_7
    //     0xbf9e58: ldur            d0, [x1, #7]
    // 0xbf9e5c: ldur            d1, [fp, #-0x28]
    // 0xbf9e60: fadd            d2, d1, d0
    // 0xbf9e64: b               #0xbf9e9c
    // 0xbf9e68: fsub            d0, d1, d2
    // 0xbf9e6c: ldur            x1, [fp, #-8]
    // 0xbf9e70: stur            d0, [fp, #-0x28]
    // 0xbf9e74: r0 = _outerPosition()
    //     0xbf9e74: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbf9e78: cmp             w0, NULL
    // 0xbf9e7c: b.eq            #0xbfa3d8
    // 0xbf9e80: LoadField: r1 = r0->field_33
    //     0xbf9e80: ldur            w1, [x0, #0x33]
    // 0xbf9e84: DecompressPointer r1
    //     0xbf9e84: add             x1, x1, HEAP, lsl #32
    // 0xbf9e88: cmp             w1, NULL
    // 0xbf9e8c: b.eq            #0xbfa3dc
    // 0xbf9e90: LoadField: d0 = r1->field_7
    //     0xbf9e90: ldur            d0, [x1, #7]
    // 0xbf9e94: ldur            d1, [fp, #-0x28]
    // 0xbf9e98: fadd            d2, d1, d0
    // 0xbf9e9c: ldur            d0, [fp, #-0x30]
    // 0xbf9ea0: d1 = 0.000000
    //     0xbf9ea0: eor             v1.16b, v1.16b, v1.16b
    // 0xbf9ea4: stur            d2, [fp, #-0x28]
    // 0xbf9ea8: fcmp            d0, d1
    // 0xbf9eac: b.le            #0xbf9f84
    // 0xbf9eb0: ldur            x0, [fp, #-0x10]
    // 0xbf9eb4: LoadField: r1 = r0->field_3f
    //     0xbf9eb4: ldur            w1, [x0, #0x3f]
    // 0xbf9eb8: DecompressPointer r1
    //     0xbf9eb8: add             x1, x1, HEAP, lsl #32
    // 0xbf9ebc: cmp             w1, NULL
    // 0xbf9ec0: b.eq            #0xbfa3e0
    // 0xbf9ec4: LoadField: r2 = r0->field_2f
    //     0xbf9ec4: ldur            w2, [x0, #0x2f]
    // 0xbf9ec8: DecompressPointer r2
    //     0xbf9ec8: add             x2, x2, HEAP, lsl #32
    // 0xbf9ecc: cmp             w2, NULL
    // 0xbf9ed0: b.eq            #0xbfa3e4
    // 0xbf9ed4: LoadField: d3 = r1->field_7
    //     0xbf9ed4: ldur            d3, [x1, #7]
    // 0xbf9ed8: LoadField: d4 = r2->field_7
    //     0xbf9ed8: ldur            d4, [x2, #7]
    // 0xbf9edc: fcmp            d3, d4
    // 0xbf9ee0: b.le            #0xbf9f84
    // 0xbf9ee4: ldur            x1, [fp, #-8]
    // 0xbf9ee8: r0 = _outerPosition()
    //     0xbf9ee8: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbf9eec: cmp             w0, NULL
    // 0xbf9ef0: b.eq            #0xbfa3e8
    // 0xbf9ef4: LoadField: r2 = r0->field_33
    //     0xbf9ef4: ldur            w2, [x0, #0x33]
    // 0xbf9ef8: DecompressPointer r2
    //     0xbf9ef8: add             x2, x2, HEAP, lsl #32
    // 0xbf9efc: stur            x2, [fp, #-0x18]
    // 0xbf9f00: cmp             w2, NULL
    // 0xbf9f04: b.eq            #0xbfa3ec
    // 0xbf9f08: ldur            x1, [fp, #-8]
    // 0xbf9f0c: r0 = _outerPosition()
    //     0xbf9f0c: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbf9f10: cmp             w0, NULL
    // 0xbf9f14: b.eq            #0xbfa3f0
    // 0xbf9f18: LoadField: r1 = r0->field_3f
    //     0xbf9f18: ldur            w1, [x0, #0x3f]
    // 0xbf9f1c: DecompressPointer r1
    //     0xbf9f1c: add             x1, x1, HEAP, lsl #32
    // 0xbf9f20: cmp             w1, NULL
    // 0xbf9f24: b.eq            #0xbfa3f4
    // 0xbf9f28: ldur            x0, [fp, #-0x18]
    // 0xbf9f2c: LoadField: d0 = r0->field_7
    //     0xbf9f2c: ldur            d0, [x0, #7]
    // 0xbf9f30: LoadField: d1 = r1->field_7
    //     0xbf9f30: ldur            d1, [x1, #7]
    // 0xbf9f34: fsub            d2, d0, d1
    // 0xbf9f38: ldur            d0, [fp, #-0x28]
    // 0xbf9f3c: stur            d2, [fp, #-0x40]
    // 0xbf9f40: fadd            d1, d0, d2
    // 0xbf9f44: ldur            x1, [fp, #-8]
    // 0xbf9f48: stur            d1, [fp, #-0x38]
    // 0xbf9f4c: r0 = _outerPosition()
    //     0xbf9f4c: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbf9f50: cmp             w0, NULL
    // 0xbf9f54: b.eq            #0xbfa3f8
    // 0xbf9f58: LoadField: r1 = r0->field_3f
    //     0xbf9f58: ldur            w1, [x0, #0x3f]
    // 0xbf9f5c: DecompressPointer r1
    //     0xbf9f5c: add             x1, x1, HEAP, lsl #32
    // 0xbf9f60: cmp             w1, NULL
    // 0xbf9f64: b.eq            #0xbfa3fc
    // 0xbf9f68: LoadField: d0 = r1->field_7
    //     0xbf9f68: ldur            d0, [x1, #7]
    // 0xbf9f6c: ldur            d2, [fp, #-0x28]
    // 0xbf9f70: fsub            d1, d0, d2
    // 0xbf9f74: mov             v3.16b, v2.16b
    // 0xbf9f78: ldur            d2, [fp, #-0x38]
    // 0xbf9f7c: ldur            d0, [fp, #-0x40]
    // 0xbf9f80: b               #0xbfa1c0
    // 0xbf9f84: fcmp            d1, d0
    // 0xbf9f88: b.le            #0xbfa05c
    // 0xbf9f8c: ldur            x0, [fp, #-0x10]
    // 0xbf9f90: LoadField: r1 = r0->field_3f
    //     0xbf9f90: ldur            w1, [x0, #0x3f]
    // 0xbf9f94: DecompressPointer r1
    //     0xbf9f94: add             x1, x1, HEAP, lsl #32
    // 0xbf9f98: cmp             w1, NULL
    // 0xbf9f9c: b.eq            #0xbfa400
    // 0xbf9fa0: LoadField: r2 = r0->field_2f
    //     0xbf9fa0: ldur            w2, [x0, #0x2f]
    // 0xbf9fa4: DecompressPointer r2
    //     0xbf9fa4: add             x2, x2, HEAP, lsl #32
    // 0xbf9fa8: cmp             w2, NULL
    // 0xbf9fac: b.eq            #0xbfa404
    // 0xbf9fb0: LoadField: d3 = r1->field_7
    //     0xbf9fb0: ldur            d3, [x1, #7]
    // 0xbf9fb4: LoadField: d4 = r2->field_7
    //     0xbf9fb4: ldur            d4, [x2, #7]
    // 0xbf9fb8: fcmp            d4, d3
    // 0xbf9fbc: b.le            #0xbfa05c
    // 0xbf9fc0: ldur            x1, [fp, #-8]
    // 0xbf9fc4: r0 = _outerPosition()
    //     0xbf9fc4: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbf9fc8: cmp             w0, NULL
    // 0xbf9fcc: b.eq            #0xbfa408
    // 0xbf9fd0: LoadField: r2 = r0->field_3f
    //     0xbf9fd0: ldur            w2, [x0, #0x3f]
    // 0xbf9fd4: DecompressPointer r2
    //     0xbf9fd4: add             x2, x2, HEAP, lsl #32
    // 0xbf9fd8: stur            x2, [fp, #-0x18]
    // 0xbf9fdc: cmp             w2, NULL
    // 0xbf9fe0: b.eq            #0xbfa40c
    // 0xbf9fe4: ldur            x1, [fp, #-8]
    // 0xbf9fe8: r0 = _outerPosition()
    //     0xbf9fe8: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbf9fec: cmp             w0, NULL
    // 0xbf9ff0: b.eq            #0xbfa410
    // 0xbf9ff4: LoadField: r1 = r0->field_2f
    //     0xbf9ff4: ldur            w1, [x0, #0x2f]
    // 0xbf9ff8: DecompressPointer r1
    //     0xbf9ff8: add             x1, x1, HEAP, lsl #32
    // 0xbf9ffc: cmp             w1, NULL
    // 0xbfa000: b.eq            #0xbfa414
    // 0xbfa004: ldur            x0, [fp, #-0x18]
    // 0xbfa008: LoadField: d0 = r0->field_7
    //     0xbfa008: ldur            d0, [x0, #7]
    // 0xbfa00c: LoadField: d1 = r1->field_7
    //     0xbfa00c: ldur            d1, [x1, #7]
    // 0xbfa010: fsub            d2, d0, d1
    // 0xbfa014: ldur            d0, [fp, #-0x28]
    // 0xbfa018: stur            d2, [fp, #-0x40]
    // 0xbfa01c: fsub            d1, d0, d2
    // 0xbfa020: ldur            x1, [fp, #-8]
    // 0xbfa024: stur            d1, [fp, #-0x38]
    // 0xbfa028: r0 = _outerPosition()
    //     0xbfa028: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa02c: cmp             w0, NULL
    // 0xbfa030: b.eq            #0xbfa418
    // 0xbfa034: LoadField: r1 = r0->field_3f
    //     0xbfa034: ldur            w1, [x0, #0x3f]
    // 0xbfa038: DecompressPointer r1
    //     0xbfa038: add             x1, x1, HEAP, lsl #32
    // 0xbfa03c: cmp             w1, NULL
    // 0xbfa040: b.eq            #0xbfa41c
    // 0xbfa044: LoadField: d0 = r1->field_7
    //     0xbfa044: ldur            d0, [x1, #7]
    // 0xbfa048: ldur            d2, [fp, #-0x28]
    // 0xbfa04c: fsub            d1, d0, d2
    // 0xbfa050: ldur            d3, [fp, #-0x38]
    // 0xbfa054: ldur            d0, [fp, #-0x40]
    // 0xbfa058: b               #0xbfa1c0
    // 0xbfa05c: fcmp            d0, d1
    // 0xbfa060: b.le            #0xbfa0c0
    // 0xbfa064: ldur            x1, [fp, #-8]
    // 0xbfa068: r0 = _outerPosition()
    //     0xbfa068: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa06c: cmp             w0, NULL
    // 0xbfa070: b.eq            #0xbfa420
    // 0xbfa074: LoadField: r2 = r0->field_2f
    //     0xbfa074: ldur            w2, [x0, #0x2f]
    // 0xbfa078: DecompressPointer r2
    //     0xbfa078: add             x2, x2, HEAP, lsl #32
    // 0xbfa07c: stur            x2, [fp, #-0x18]
    // 0xbfa080: cmp             w2, NULL
    // 0xbfa084: b.eq            #0xbfa424
    // 0xbfa088: ldur            x1, [fp, #-8]
    // 0xbfa08c: r0 = _outerPosition()
    //     0xbfa08c: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa090: cmp             w0, NULL
    // 0xbfa094: b.eq            #0xbfa428
    // 0xbfa098: LoadField: r1 = r0->field_3f
    //     0xbfa098: ldur            w1, [x0, #0x3f]
    // 0xbfa09c: DecompressPointer r1
    //     0xbfa09c: add             x1, x1, HEAP, lsl #32
    // 0xbfa0a0: cmp             w1, NULL
    // 0xbfa0a4: b.eq            #0xbfa42c
    // 0xbfa0a8: ldur            x0, [fp, #-0x18]
    // 0xbfa0ac: LoadField: d0 = r0->field_7
    //     0xbfa0ac: ldur            d0, [x0, #7]
    // 0xbfa0b0: LoadField: d1 = r1->field_7
    //     0xbfa0b0: ldur            d1, [x1, #7]
    // 0xbfa0b4: fsub            d2, d0, d1
    // 0xbfa0b8: mov             v0.16b, v2.16b
    // 0xbfa0bc: b               #0xbfa158
    // 0xbfa0c0: fcmp            d1, d0
    // 0xbfa0c4: b.le            #0xbfa154
    // 0xbfa0c8: ldur            x1, [fp, #-8]
    // 0xbfa0cc: r0 = _outerPosition()
    //     0xbfa0cc: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa0d0: cmp             w0, NULL
    // 0xbfa0d4: b.eq            #0xbfa430
    // 0xbfa0d8: LoadField: r2 = r0->field_3f
    //     0xbfa0d8: ldur            w2, [x0, #0x3f]
    // 0xbfa0dc: DecompressPointer r2
    //     0xbfa0dc: add             x2, x2, HEAP, lsl #32
    // 0xbfa0e0: stur            x2, [fp, #-0x18]
    // 0xbfa0e4: cmp             w2, NULL
    // 0xbfa0e8: b.eq            #0xbfa434
    // 0xbfa0ec: ldur            x1, [fp, #-8]
    // 0xbfa0f0: r0 = _outerPosition()
    //     0xbfa0f0: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa0f4: cmp             w0, NULL
    // 0xbfa0f8: b.eq            #0xbfa438
    // 0xbfa0fc: LoadField: r2 = r0->field_33
    //     0xbfa0fc: ldur            w2, [x0, #0x33]
    // 0xbfa100: DecompressPointer r2
    //     0xbfa100: add             x2, x2, HEAP, lsl #32
    // 0xbfa104: stur            x2, [fp, #-0x20]
    // 0xbfa108: cmp             w2, NULL
    // 0xbfa10c: b.eq            #0xbfa43c
    // 0xbfa110: ldur            x1, [fp, #-8]
    // 0xbfa114: r0 = _outerPosition()
    //     0xbfa114: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa118: cmp             w0, NULL
    // 0xbfa11c: b.eq            #0xbfa440
    // 0xbfa120: LoadField: r1 = r0->field_2f
    //     0xbfa120: ldur            w1, [x0, #0x2f]
    // 0xbfa124: DecompressPointer r1
    //     0xbfa124: add             x1, x1, HEAP, lsl #32
    // 0xbfa128: cmp             w1, NULL
    // 0xbfa12c: b.eq            #0xbfa444
    // 0xbfa130: ldur            x0, [fp, #-0x20]
    // 0xbfa134: LoadField: d0 = r0->field_7
    //     0xbfa134: ldur            d0, [x0, #7]
    // 0xbfa138: LoadField: d1 = r1->field_7
    //     0xbfa138: ldur            d1, [x1, #7]
    // 0xbfa13c: fsub            d2, d0, d1
    // 0xbfa140: ldur            x0, [fp, #-0x18]
    // 0xbfa144: LoadField: d0 = r0->field_7
    //     0xbfa144: ldur            d0, [x0, #7]
    // 0xbfa148: fsub            d1, d0, d2
    // 0xbfa14c: mov             v0.16b, v1.16b
    // 0xbfa150: b               #0xbfa158
    // 0xbfa154: d0 = 0.000000
    //     0xbfa154: eor             v0.16b, v0.16b, v0.16b
    // 0xbfa158: ldur            x1, [fp, #-8]
    // 0xbfa15c: stur            d0, [fp, #-0x30]
    // 0xbfa160: r0 = _outerPosition()
    //     0xbfa160: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa164: cmp             w0, NULL
    // 0xbfa168: b.eq            #0xbfa448
    // 0xbfa16c: LoadField: r2 = r0->field_2f
    //     0xbfa16c: ldur            w2, [x0, #0x2f]
    // 0xbfa170: DecompressPointer r2
    //     0xbfa170: add             x2, x2, HEAP, lsl #32
    // 0xbfa174: stur            x2, [fp, #-0x18]
    // 0xbfa178: cmp             w2, NULL
    // 0xbfa17c: b.eq            #0xbfa44c
    // 0xbfa180: ldur            x1, [fp, #-8]
    // 0xbfa184: r0 = _outerPosition()
    //     0xbfa184: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa188: cmp             w0, NULL
    // 0xbfa18c: b.eq            #0xbfa450
    // 0xbfa190: LoadField: r1 = r0->field_33
    //     0xbfa190: ldur            w1, [x0, #0x33]
    // 0xbfa194: DecompressPointer r1
    //     0xbfa194: add             x1, x1, HEAP, lsl #32
    // 0xbfa198: cmp             w1, NULL
    // 0xbfa19c: b.eq            #0xbfa454
    // 0xbfa1a0: LoadField: d0 = r1->field_7
    //     0xbfa1a0: ldur            d0, [x1, #7]
    // 0xbfa1a4: ldur            d1, [fp, #-0x30]
    // 0xbfa1a8: fadd            d2, d0, d1
    // 0xbfa1ac: ldur            x0, [fp, #-0x18]
    // 0xbfa1b0: LoadField: d0 = r0->field_7
    //     0xbfa1b0: ldur            d0, [x0, #7]
    // 0xbfa1b4: mov             v3.16b, v0.16b
    // 0xbfa1b8: mov             v0.16b, v1.16b
    // 0xbfa1bc: d1 = 0.000000
    //     0xbfa1bc: eor             v1.16b, v1.16b, v1.16b
    // 0xbfa1c0: ldur            d4, [fp, #-0x28]
    // 0xbfa1c4: ldur            x0, [fp, #-0x10]
    // 0xbfa1c8: ldur            x1, [fp, #-8]
    // 0xbfa1cc: stur            d4, [fp, #-0x28]
    // 0xbfa1d0: stur            d3, [fp, #-0x30]
    // 0xbfa1d4: stur            d2, [fp, #-0x38]
    // 0xbfa1d8: stur            d1, [fp, #-0x40]
    // 0xbfa1dc: stur            d0, [fp, #-0x48]
    // 0xbfa1e0: r0 = _outerPosition()
    //     0xbfa1e0: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa1e4: cmp             w0, NULL
    // 0xbfa1e8: b.eq            #0xbfa458
    // 0xbfa1ec: LoadField: r2 = r0->field_2f
    //     0xbfa1ec: ldur            w2, [x0, #0x2f]
    // 0xbfa1f0: DecompressPointer r2
    //     0xbfa1f0: add             x2, x2, HEAP, lsl #32
    // 0xbfa1f4: stur            x2, [fp, #-0x18]
    // 0xbfa1f8: cmp             w2, NULL
    // 0xbfa1fc: b.eq            #0xbfa45c
    // 0xbfa200: ldur            x1, [fp, #-8]
    // 0xbfa204: r0 = _outerPosition()
    //     0xbfa204: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa208: cmp             w0, NULL
    // 0xbfa20c: b.eq            #0xbfa460
    // 0xbfa210: LoadField: r1 = r0->field_33
    //     0xbfa210: ldur            w1, [x0, #0x33]
    // 0xbfa214: DecompressPointer r1
    //     0xbfa214: add             x1, x1, HEAP, lsl #32
    // 0xbfa218: cmp             w1, NULL
    // 0xbfa21c: b.eq            #0xbfa464
    // 0xbfa220: ldur            x0, [fp, #-0x10]
    // 0xbfa224: LoadField: r2 = r0->field_33
    //     0xbfa224: ldur            w2, [x0, #0x33]
    // 0xbfa228: DecompressPointer r2
    //     0xbfa228: add             x2, x2, HEAP, lsl #32
    // 0xbfa22c: cmp             w2, NULL
    // 0xbfa230: b.eq            #0xbfa468
    // 0xbfa234: LoadField: d0 = r1->field_7
    //     0xbfa234: ldur            d0, [x1, #7]
    // 0xbfa238: LoadField: d1 = r2->field_7
    //     0xbfa238: ldur            d1, [x2, #7]
    // 0xbfa23c: fadd            d2, d0, d1
    // 0xbfa240: LoadField: r1 = r0->field_2f
    //     0xbfa240: ldur            w1, [x0, #0x2f]
    // 0xbfa244: DecompressPointer r1
    //     0xbfa244: add             x1, x1, HEAP, lsl #32
    // 0xbfa248: cmp             w1, NULL
    // 0xbfa24c: b.eq            #0xbfa46c
    // 0xbfa250: LoadField: d0 = r1->field_7
    //     0xbfa250: ldur            d0, [x1, #7]
    // 0xbfa254: fsub            d1, d2, d0
    // 0xbfa258: ldur            d0, [fp, #-0x48]
    // 0xbfa25c: fadd            d2, d1, d0
    // 0xbfa260: ldur            x1, [fp, #-8]
    // 0xbfa264: stur            d2, [fp, #-0x50]
    // 0xbfa268: r0 = _outerPosition()
    //     0xbfa268: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa26c: cmp             w0, NULL
    // 0xbfa270: b.eq            #0xbfa470
    // 0xbfa274: LoadField: r2 = r0->field_43
    //     0xbfa274: ldur            w2, [x0, #0x43]
    // 0xbfa278: DecompressPointer r2
    //     0xbfa278: add             x2, x2, HEAP, lsl #32
    // 0xbfa27c: stur            x2, [fp, #-0x10]
    // 0xbfa280: cmp             w2, NULL
    // 0xbfa284: b.eq            #0xbfa474
    // 0xbfa288: ldur            x1, [fp, #-8]
    // 0xbfa28c: r0 = _outerPosition()
    //     0xbfa28c: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa290: cmp             w0, NULL
    // 0xbfa294: b.eq            #0xbfa478
    // 0xbfa298: LoadField: r1 = r0->field_27
    //     0xbfa298: ldur            w1, [x0, #0x27]
    // 0xbfa29c: DecompressPointer r1
    //     0xbfa29c: add             x1, x1, HEAP, lsl #32
    // 0xbfa2a0: LoadField: r0 = r1->field_b
    //     0xbfa2a0: ldur            w0, [x1, #0xb]
    // 0xbfa2a4: DecompressPointer r0
    //     0xbfa2a4: add             x0, x0, HEAP, lsl #32
    // 0xbfa2a8: cmp             w0, NULL
    // 0xbfa2ac: b.eq            #0xbfa47c
    // 0xbfa2b0: LoadField: r2 = r0->field_b
    //     0xbfa2b0: ldur            w2, [x0, #0xb]
    // 0xbfa2b4: DecompressPointer r2
    //     0xbfa2b4: add             x2, x2, HEAP, lsl #32
    // 0xbfa2b8: ldur            x1, [fp, #-8]
    // 0xbfa2bc: stur            x2, [fp, #-0x20]
    // 0xbfa2c0: r0 = _outerPosition()
    //     0xbfa2c0: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa2c4: cmp             w0, NULL
    // 0xbfa2c8: b.eq            #0xbfa480
    // 0xbfa2cc: LoadField: r1 = r0->field_27
    //     0xbfa2cc: ldur            w1, [x0, #0x27]
    // 0xbfa2d0: DecompressPointer r1
    //     0xbfa2d0: add             x1, x1, HEAP, lsl #32
    // 0xbfa2d4: LoadField: r0 = r1->field_33
    //     0xbfa2d4: ldur            w0, [x1, #0x33]
    // 0xbfa2d8: DecompressPointer r0
    //     0xbfa2d8: add             x0, x0, HEAP, lsl #32
    // 0xbfa2dc: r16 = Sentinel
    //     0xbfa2dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbfa2e0: cmp             w0, w16
    // 0xbfa2e4: b.eq            #0xbfa484
    // 0xbfa2e8: stur            x0, [fp, #-8]
    // 0xbfa2ec: r0 = _NestedScrollMetrics()
    //     0xbfa2ec: bl              #0xbfa4c4  ; Allocate_NestedScrollMetricsStub -> _NestedScrollMetrics (size=0x3c)
    // 0xbfa2f0: ldur            d0, [fp, #-0x30]
    // 0xbfa2f4: StoreField: r0->field_23 = d0
    //     0xbfa2f4: stur            d0, [x0, #0x23]
    // 0xbfa2f8: ldur            d0, [fp, #-0x38]
    // 0xbfa2fc: StoreField: r0->field_2b = d0
    //     0xbfa2fc: stur            d0, [x0, #0x2b]
    // 0xbfa300: ldur            d0, [fp, #-0x40]
    // 0xbfa304: StoreField: r0->field_33 = d0
    //     0xbfa304: stur            d0, [x0, #0x33]
    // 0xbfa308: ldur            x1, [fp, #-0x20]
    // 0xbfa30c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbfa30c: stur            w1, [x0, #0x17]
    // 0xbfa310: ldur            x1, [fp, #-8]
    // 0xbfa314: LoadField: d0 = r1->field_7
    //     0xbfa314: ldur            d0, [x1, #7]
    // 0xbfa318: StoreField: r0->field_1b = d0
    //     0xbfa318: stur            d0, [x0, #0x1b]
    // 0xbfa31c: ldur            x1, [fp, #-0x18]
    // 0xbfa320: StoreField: r0->field_7 = r1
    //     0xbfa320: stur            w1, [x0, #7]
    // 0xbfa324: ldur            d0, [fp, #-0x50]
    // 0xbfa328: r1 = inline_Allocate_Double()
    //     0xbfa328: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xbfa32c: add             x1, x1, #0x10
    //     0xbfa330: cmp             x2, x1
    //     0xbfa334: b.ls            #0xbfa48c
    //     0xbfa338: str             x1, [THR, #0x50]  ; THR::top
    //     0xbfa33c: sub             x1, x1, #0xf
    //     0xbfa340: movz            x2, #0xe15c
    //     0xbfa344: movk            x2, #0x3, lsl #16
    //     0xbfa348: stur            x2, [x1, #-1]
    // 0xbfa34c: StoreField: r1->field_7 = d0
    //     0xbfa34c: stur            d0, [x1, #7]
    // 0xbfa350: StoreField: r0->field_b = r1
    //     0xbfa350: stur            w1, [x0, #0xb]
    // 0xbfa354: ldur            d0, [fp, #-0x28]
    // 0xbfa358: r1 = inline_Allocate_Double()
    //     0xbfa358: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xbfa35c: add             x1, x1, #0x10
    //     0xbfa360: cmp             x2, x1
    //     0xbfa364: b.ls            #0xbfa4a8
    //     0xbfa368: str             x1, [THR, #0x50]  ; THR::top
    //     0xbfa36c: sub             x1, x1, #0xf
    //     0xbfa370: movz            x2, #0xe15c
    //     0xbfa374: movk            x2, #0x3, lsl #16
    //     0xbfa378: stur            x2, [x1, #-1]
    // 0xbfa37c: StoreField: r1->field_7 = d0
    //     0xbfa37c: stur            d0, [x1, #7]
    // 0xbfa380: StoreField: r0->field_f = r1
    //     0xbfa380: stur            w1, [x0, #0xf]
    // 0xbfa384: ldur            x1, [fp, #-0x10]
    // 0xbfa388: StoreField: r0->field_13 = r1
    //     0xbfa388: stur            w1, [x0, #0x13]
    // 0xbfa38c: LeaveFrame
    //     0xbfa38c: mov             SP, fp
    //     0xbfa390: ldp             fp, lr, [SP], #0x10
    // 0xbfa394: ret
    //     0xbfa394: ret             
    // 0xbfa398: r0 = StackOverflowSharedWithFPURegs()
    //     0xbfa398: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xbfa39c: b               #0xbf9ce0
    // 0xbfa3a0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbfa3a0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbfa3a4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbfa3a4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbfa3a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3e0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbfa3e0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbfa3e4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbfa3e4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbfa3e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa3fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa3fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa400: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbfa400: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbfa404: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbfa404: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbfa408: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa408: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa40c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa40c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa410: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa410: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa414: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa414: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa418: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa418: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa41c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa41c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa420: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa420: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa424: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa424: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa428: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa428: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa42c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa42c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa430: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa430: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa434: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa434: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa438: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa438: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa43c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa43c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa440: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa440: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa444: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa444: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa448: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa448: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa44c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa44c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa450: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa450: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa454: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa454: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa458: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa458: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa45c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa45c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa460: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa460: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa464: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa464: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa468: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa468: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa46c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbfa46c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbfa470: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa470: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa474: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa474: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa478: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa478: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa47c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa47c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa480: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa480: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa484: r9 = _devicePixelRatio
    //     0xbfa484: ldr             x9, [PP, #0x6f10]  ; [pp+0x6f10] Field <ScrollableState._devicePixelRatio@338019050>: late (offset: 0x34)
    // 0xbfa488: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbfa488: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbfa48c: SaveReg d0
    //     0xbfa48c: str             q0, [SP, #-0x10]!
    // 0xbfa490: SaveReg r0
    //     0xbfa490: str             x0, [SP, #-8]!
    // 0xbfa494: r0 = AllocateDouble()
    //     0xbfa494: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbfa498: mov             x1, x0
    // 0xbfa49c: RestoreReg r0
    //     0xbfa49c: ldr             x0, [SP], #8
    // 0xbfa4a0: RestoreReg d0
    //     0xbfa4a0: ldr             q0, [SP], #0x10
    // 0xbfa4a4: b               #0xbfa34c
    // 0xbfa4a8: SaveReg d0
    //     0xbfa4a8: str             q0, [SP, #-0x10]!
    // 0xbfa4ac: SaveReg r0
    //     0xbfa4ac: str             x0, [SP, #-8]!
    // 0xbfa4b0: r0 = AllocateDouble()
    //     0xbfa4b0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbfa4b4: mov             x1, x0
    // 0xbfa4b8: RestoreReg r0
    //     0xbfa4b8: ldr             x0, [SP], #8
    // 0xbfa4bc: RestoreReg d0
    //     0xbfa4bc: ldr             q0, [SP], #0x10
    // 0xbfa4c0: b               #0xbfa37c
  }
  _ createOuterBallisticScrollActivity(/* No info */) {
    // ** addr: 0xbfa584, size: 0x3cc
    // 0xbfa584: EnterFrame
    //     0xbfa584: stp             fp, lr, [SP, #-0x10]!
    //     0xbfa588: mov             fp, SP
    // 0xbfa58c: AllocStack(0x60)
    //     0xbfa58c: sub             SP, SP, #0x60
    // 0xbfa590: d1 = 0.000000
    //     0xbfa590: eor             v1.16b, v1.16b, v1.16b
    // 0xbfa594: stur            x1, [fp, #-8]
    // 0xbfa598: stur            d0, [fp, #-0x50]
    // 0xbfa59c: CheckStackOverflow
    //     0xbfa59c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfa5a0: cmp             SP, x16
    //     0xbfa5a4: b.ls            #0xbfa914
    // 0xbfa5a8: fcmp            d0, d1
    // 0xbfa5ac: b.eq            #0xbfa7dc
    // 0xbfa5b0: LoadField: r0 = r1->field_1b
    //     0xbfa5b0: ldur            w0, [x1, #0x1b]
    // 0xbfa5b4: DecompressPointer r0
    //     0xbfa5b4: add             x0, x0, HEAP, lsl #32
    // 0xbfa5b8: r16 = Sentinel
    //     0xbfa5b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbfa5bc: cmp             w0, w16
    // 0xbfa5c0: b.eq            #0xbfa91c
    // 0xbfa5c4: LoadField: r2 = r0->field_3b
    //     0xbfa5c4: ldur            w2, [x0, #0x3b]
    // 0xbfa5c8: DecompressPointer r2
    //     0xbfa5c8: add             x2, x2, HEAP, lsl #32
    // 0xbfa5cc: r16 = <_NestedScrollPosition>
    //     0xbfa5cc: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0xbfa5d0: stp             x2, x16, [SP]
    // 0xbfa5d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbfa5d4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbfa5d8: r0 = cast()
    //     0xbfa5d8: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0xbfa5dc: stur            x0, [fp, #-0x18]
    // 0xbfa5e0: LoadField: r2 = r0->field_7
    //     0xbfa5e0: ldur            w2, [x0, #7]
    // 0xbfa5e4: DecompressPointer r2
    //     0xbfa5e4: add             x2, x2, HEAP, lsl #32
    // 0xbfa5e8: stur            x2, [fp, #-0x10]
    // 0xbfa5ec: str             x0, [SP]
    // 0xbfa5f0: r0 = length()
    //     0xbfa5f0: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0xbfa5f4: r1 = LoadInt32Instr(r0)
    //     0xbfa5f4: sbfx            x1, x0, #1, #0x1f
    //     0xbfa5f8: tbz             w0, #0, #0xbfa600
    //     0xbfa5fc: ldur            x1, [x0, #7]
    // 0xbfa600: ldur            x0, [fp, #-0x18]
    // 0xbfa604: stur            x1, [fp, #-0x38]
    // 0xbfa608: LoadField: r2 = r0->field_b
    //     0xbfa608: ldur            w2, [x0, #0xb]
    // 0xbfa60c: DecompressPointer r2
    //     0xbfa60c: add             x2, x2, HEAP, lsl #32
    // 0xbfa610: stur            x2, [fp, #-0x30]
    // 0xbfa614: ldur            d0, [fp, #-0x50]
    // 0xbfa618: r4 = Null
    //     0xbfa618: mov             x4, NULL
    // 0xbfa61c: r3 = 0
    //     0xbfa61c: movz            x3, #0
    // 0xbfa620: stur            x4, [fp, #-0x20]
    // 0xbfa624: stur            x3, [fp, #-0x28]
    // 0xbfa628: CheckStackOverflow
    //     0xbfa628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfa62c: cmp             SP, x16
    //     0xbfa630: b.ls            #0xbfa924
    // 0xbfa634: str             x0, [SP]
    // 0xbfa638: r0 = length()
    //     0xbfa638: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0xbfa63c: r1 = LoadInt32Instr(r0)
    //     0xbfa63c: sbfx            x1, x0, #1, #0x1f
    //     0xbfa640: tbz             w0, #0, #0xbfa648
    //     0xbfa644: ldur            x1, [x0, #7]
    // 0xbfa648: ldur            x2, [fp, #-0x38]
    // 0xbfa64c: cmp             x2, x1
    // 0xbfa650: b.ne            #0xbfa8f4
    // 0xbfa654: ldur            x3, [fp, #-0x28]
    // 0xbfa658: cmp             x3, x1
    // 0xbfa65c: b.ge            #0xbfa7cc
    // 0xbfa660: ldur            x4, [fp, #-0x30]
    // 0xbfa664: r0 = BoxInt64Instr(r3)
    //     0xbfa664: sbfiz           x0, x3, #1, #0x1f
    //     0xbfa668: cmp             x3, x0, asr #1
    //     0xbfa66c: b.eq            #0xbfa678
    //     0xbfa670: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbfa674: stur            x3, [x0, #7]
    // 0xbfa678: r1 = LoadClassIdInstr(r4)
    //     0xbfa678: ldur            x1, [x4, #-1]
    //     0xbfa67c: ubfx            x1, x1, #0xc, #0x14
    // 0xbfa680: stp             x0, x4, [SP]
    // 0xbfa684: mov             x0, x1
    // 0xbfa688: r0 = GDT[cid_x0 + 0x13037]()
    //     0xbfa688: movz            x17, #0x3037
    //     0xbfa68c: movk            x17, #0x1, lsl #16
    //     0xbfa690: add             lr, x0, x17
    //     0xbfa694: ldr             lr, [x21, lr, lsl #3]
    //     0xbfa698: blr             lr
    // 0xbfa69c: ldur            x2, [fp, #-0x10]
    // 0xbfa6a0: mov             x3, x0
    // 0xbfa6a4: r1 = Null
    //     0xbfa6a4: mov             x1, NULL
    // 0xbfa6a8: stur            x3, [fp, #-0x40]
    // 0xbfa6ac: cmp             w2, NULL
    // 0xbfa6b0: b.eq            #0xbfa6cc
    // 0xbfa6b4: LoadField: r4 = r2->field_1f
    //     0xbfa6b4: ldur            w4, [x2, #0x1f]
    // 0xbfa6b8: DecompressPointer r4
    //     0xbfa6b8: add             x4, x4, HEAP, lsl #32
    // 0xbfa6bc: r8 = C1X1
    //     0xbfa6bc: ldr             x8, [PP, #0x6ee0]  ; [pp+0x6ee0] TypeParameter: C1X1
    // 0xbfa6c0: LoadField: r9 = r4->field_7
    //     0xbfa6c0: ldur            x9, [x4, #7]
    // 0xbfa6c4: r3 = Null
    //     0xbfa6c4: ldr             x3, [PP, #0x7660]  ; [pp+0x7660] Null
    // 0xbfa6c8: blr             x9
    // 0xbfa6cc: ldur            x0, [fp, #-0x28]
    // 0xbfa6d0: add             x3, x0, #1
    // 0xbfa6d4: ldur            x4, [fp, #-0x40]
    // 0xbfa6d8: stur            x3, [fp, #-0x48]
    // 0xbfa6dc: cmp             w4, NULL
    // 0xbfa6e0: b.ne            #0xbfa710
    // 0xbfa6e4: mov             x0, x4
    // 0xbfa6e8: ldur            x2, [fp, #-0x10]
    // 0xbfa6ec: r1 = Null
    //     0xbfa6ec: mov             x1, NULL
    // 0xbfa6f0: cmp             w2, NULL
    // 0xbfa6f4: b.eq            #0xbfa710
    // 0xbfa6f8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbfa6f8: ldur            w4, [x2, #0x17]
    // 0xbfa6fc: DecompressPointer r4
    //     0xbfa6fc: add             x4, x4, HEAP, lsl #32
    // 0xbfa700: r8 = X0
    //     0xbfa700: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xbfa704: LoadField: r9 = r4->field_7
    //     0xbfa704: ldur            x9, [x4, #7]
    // 0xbfa708: r3 = Null
    //     0xbfa708: ldr             x3, [PP, #0x7670]  ; [pp+0x7670] Null
    // 0xbfa70c: blr             x9
    // 0xbfa710: ldur            x0, [fp, #-0x20]
    // 0xbfa714: cmp             w0, NULL
    // 0xbfa718: b.eq            #0xbfa7a4
    // 0xbfa71c: ldur            d1, [fp, #-0x50]
    // 0xbfa720: d0 = 0.000000
    //     0xbfa720: eor             v0.16b, v0.16b, v0.16b
    // 0xbfa724: fcmp            d1, d0
    // 0xbfa728: b.le            #0xbfa768
    // 0xbfa72c: ldur            x1, [fp, #-0x40]
    // 0xbfa730: LoadField: r2 = r0->field_3f
    //     0xbfa730: ldur            w2, [x0, #0x3f]
    // 0xbfa734: DecompressPointer r2
    //     0xbfa734: add             x2, x2, HEAP, lsl #32
    // 0xbfa738: cmp             w2, NULL
    // 0xbfa73c: b.eq            #0xbfa92c
    // 0xbfa740: LoadField: r3 = r1->field_3f
    //     0xbfa740: ldur            w3, [x1, #0x3f]
    // 0xbfa744: DecompressPointer r3
    //     0xbfa744: add             x3, x3, HEAP, lsl #32
    // 0xbfa748: cmp             w3, NULL
    // 0xbfa74c: b.eq            #0xbfa930
    // 0xbfa750: LoadField: d2 = r2->field_7
    //     0xbfa750: ldur            d2, [x2, #7]
    // 0xbfa754: LoadField: d3 = r3->field_7
    //     0xbfa754: ldur            d3, [x3, #7]
    // 0xbfa758: fcmp            d3, d2
    // 0xbfa75c: b.le            #0xbfa7b0
    // 0xbfa760: mov             x4, x0
    // 0xbfa764: b               #0xbfa7b4
    // 0xbfa768: ldur            x1, [fp, #-0x40]
    // 0xbfa76c: LoadField: r2 = r0->field_3f
    //     0xbfa76c: ldur            w2, [x0, #0x3f]
    // 0xbfa770: DecompressPointer r2
    //     0xbfa770: add             x2, x2, HEAP, lsl #32
    // 0xbfa774: cmp             w2, NULL
    // 0xbfa778: b.eq            #0xbfa934
    // 0xbfa77c: LoadField: r3 = r1->field_3f
    //     0xbfa77c: ldur            w3, [x1, #0x3f]
    // 0xbfa780: DecompressPointer r3
    //     0xbfa780: add             x3, x3, HEAP, lsl #32
    // 0xbfa784: cmp             w3, NULL
    // 0xbfa788: b.eq            #0xbfa938
    // 0xbfa78c: LoadField: d2 = r2->field_7
    //     0xbfa78c: ldur            d2, [x2, #7]
    // 0xbfa790: LoadField: d3 = r3->field_7
    //     0xbfa790: ldur            d3, [x3, #7]
    // 0xbfa794: fcmp            d2, d3
    // 0xbfa798: b.le            #0xbfa7b0
    // 0xbfa79c: mov             x4, x0
    // 0xbfa7a0: b               #0xbfa7b4
    // 0xbfa7a4: ldur            d1, [fp, #-0x50]
    // 0xbfa7a8: ldur            x1, [fp, #-0x40]
    // 0xbfa7ac: d0 = 0.000000
    //     0xbfa7ac: eor             v0.16b, v0.16b, v0.16b
    // 0xbfa7b0: mov             x4, x1
    // 0xbfa7b4: ldur            x3, [fp, #-0x48]
    // 0xbfa7b8: mov             v0.16b, v1.16b
    // 0xbfa7bc: ldur            x0, [fp, #-0x18]
    // 0xbfa7c0: ldur            x2, [fp, #-0x30]
    // 0xbfa7c4: ldur            x1, [fp, #-0x38]
    // 0xbfa7c8: b               #0xbfa620
    // 0xbfa7cc: ldur            d1, [fp, #-0x50]
    // 0xbfa7d0: ldur            x0, [fp, #-0x20]
    // 0xbfa7d4: mov             x2, x0
    // 0xbfa7d8: b               #0xbfa7e4
    // 0xbfa7dc: mov             v1.16b, v0.16b
    // 0xbfa7e0: r2 = Null
    //     0xbfa7e0: mov             x2, NULL
    // 0xbfa7e4: cmp             w2, NULL
    // 0xbfa7e8: b.ne            #0xbfa874
    // 0xbfa7ec: ldur            x1, [fp, #-8]
    // 0xbfa7f0: r0 = _outerPosition()
    //     0xbfa7f0: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa7f4: stur            x0, [fp, #-0x10]
    // 0xbfa7f8: cmp             w0, NULL
    // 0xbfa7fc: b.eq            #0xbfa93c
    // 0xbfa800: ldur            x1, [fp, #-8]
    // 0xbfa804: r0 = _outerPosition()
    //     0xbfa804: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa808: cmp             w0, NULL
    // 0xbfa80c: b.eq            #0xbfa940
    // 0xbfa810: LoadField: r2 = r0->field_23
    //     0xbfa810: ldur            w2, [x0, #0x23]
    // 0xbfa814: DecompressPointer r2
    //     0xbfa814: add             x2, x2, HEAP, lsl #32
    // 0xbfa818: ldur            x1, [fp, #-8]
    // 0xbfa81c: stur            x2, [fp, #-0x20]
    // 0xbfa820: r0 = _outerPosition()
    //     0xbfa820: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa824: cmp             w0, NULL
    // 0xbfa828: b.eq            #0xbfa944
    // 0xbfa82c: ldur            x1, [fp, #-0x20]
    // 0xbfa830: r2 = LoadClassIdInstr(r1)
    //     0xbfa830: ldur            x2, [x1, #-1]
    //     0xbfa834: ubfx            x2, x2, #0xc, #0x14
    // 0xbfa838: mov             x16, x0
    // 0xbfa83c: mov             x0, x2
    // 0xbfa840: mov             x2, x16
    // 0xbfa844: ldur            d0, [fp, #-0x50]
    // 0xbfa848: r0 = GDT[cid_x0 + -0xff1]()
    //     0xbfa848: sub             lr, x0, #0xff1
    //     0xbfa84c: ldr             lr, [x21, lr, lsl #3]
    //     0xbfa850: blr             lr
    // 0xbfa854: ldur            x1, [fp, #-0x10]
    // 0xbfa858: mov             x2, x0
    // 0xbfa85c: r3 = Instance__NestedBallisticScrollActivityMode
    //     0xbfa85c: ldr             x3, [PP, #0x7680]  ; [pp+0x7680] Obj!_NestedBallisticScrollActivityMode@e33de1
    // 0xbfa860: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xbfa860: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xbfa864: r0 = createBallisticScrollActivity()
    //     0xbfa864: bl              #0xbf954c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::createBallisticScrollActivity
    // 0xbfa868: LeaveFrame
    //     0xbfa868: mov             SP, fp
    //     0xbfa86c: ldp             fp, lr, [SP], #0x10
    // 0xbfa870: ret
    //     0xbfa870: ret             
    // 0xbfa874: ldur            x1, [fp, #-8]
    // 0xbfa878: ldur            d0, [fp, #-0x50]
    // 0xbfa87c: r0 = _getMetrics()
    //     0xbfa87c: bl              #0xbf9cb8  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_getMetrics
    // 0xbfa880: ldur            x1, [fp, #-8]
    // 0xbfa884: stur            x0, [fp, #-0x10]
    // 0xbfa888: r0 = _outerPosition()
    //     0xbfa888: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa88c: stur            x0, [fp, #-0x20]
    // 0xbfa890: cmp             w0, NULL
    // 0xbfa894: b.eq            #0xbfa948
    // 0xbfa898: ldur            x1, [fp, #-8]
    // 0xbfa89c: r0 = _outerPosition()
    //     0xbfa89c: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xbfa8a0: cmp             w0, NULL
    // 0xbfa8a4: b.eq            #0xbfa94c
    // 0xbfa8a8: LoadField: r1 = r0->field_23
    //     0xbfa8a8: ldur            w1, [x0, #0x23]
    // 0xbfa8ac: DecompressPointer r1
    //     0xbfa8ac: add             x1, x1, HEAP, lsl #32
    // 0xbfa8b0: r0 = LoadClassIdInstr(r1)
    //     0xbfa8b0: ldur            x0, [x1, #-1]
    //     0xbfa8b4: ubfx            x0, x0, #0xc, #0x14
    // 0xbfa8b8: ldur            x2, [fp, #-0x10]
    // 0xbfa8bc: ldur            d0, [fp, #-0x50]
    // 0xbfa8c0: r0 = GDT[cid_x0 + -0xff1]()
    //     0xbfa8c0: sub             lr, x0, #0xff1
    //     0xbfa8c4: ldr             lr, [x21, lr, lsl #3]
    //     0xbfa8c8: blr             lr
    // 0xbfa8cc: ldur            x16, [fp, #-0x10]
    // 0xbfa8d0: str             x16, [SP]
    // 0xbfa8d4: ldur            x1, [fp, #-0x20]
    // 0xbfa8d8: mov             x2, x0
    // 0xbfa8dc: r3 = Instance__NestedBallisticScrollActivityMode
    //     0xbfa8dc: ldr             x3, [PP, #0x7688]  ; [pp+0x7688] Obj!_NestedBallisticScrollActivityMode@e33dc1
    // 0xbfa8e0: r4 = const [0, 0x4, 0x1, 0x3, metrics, 0x3, null]
    //     0xbfa8e0: ldr             x4, [PP, #0x7690]  ; [pp+0x7690] List(7) [0, 0x4, 0x1, 0x3, "metrics", 0x3, Null]
    // 0xbfa8e4: r0 = createBallisticScrollActivity()
    //     0xbfa8e4: bl              #0xbf954c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::createBallisticScrollActivity
    // 0xbfa8e8: LeaveFrame
    //     0xbfa8e8: mov             SP, fp
    //     0xbfa8ec: ldp             fp, lr, [SP], #0x10
    // 0xbfa8f0: ret
    //     0xbfa8f0: ret             
    // 0xbfa8f4: ldur            x0, [fp, #-0x18]
    // 0xbfa8f8: r0 = ConcurrentModificationError()
    //     0xbfa8f8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xbfa8fc: mov             x1, x0
    // 0xbfa900: ldur            x0, [fp, #-0x18]
    // 0xbfa904: StoreField: r1->field_b = r0
    //     0xbfa904: stur            w0, [x1, #0xb]
    // 0xbfa908: mov             x0, x1
    // 0xbfa90c: r0 = Throw()
    //     0xbfa90c: bl              #0xec04b8  ; ThrowStub
    // 0xbfa910: brk             #0
    // 0xbfa914: r0 = StackOverflowSharedWithFPURegs()
    //     0xbfa914: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xbfa918: b               #0xbfa5a8
    // 0xbfa91c: r9 = _innerController
    //     0xbfa91c: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0xbfa920: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xbfa920: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xbfa924: r0 = StackOverflowSharedWithFPURegs()
    //     0xbfa924: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xbfa928: b               #0xbfa634
    // 0xbfa92c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbfa92c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbfa930: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbfa930: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbfa934: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbfa934: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbfa938: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbfa938: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbfa93c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa93c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa940: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa940: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa944: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa944: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa948: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa948: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa94c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa94c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ applyUserOffset(/* No info */) {
    // ** addr: 0xd2617c, size: 0xabc
    // 0xd2617c: EnterFrame
    //     0xd2617c: stp             fp, lr, [SP, #-0x10]!
    //     0xd26180: mov             fp, SP
    // 0xd26184: AllocStack(0x80)
    //     0xd26184: sub             SP, SP, #0x80
    // 0xd26188: d1 = 0.000000
    //     0xd26188: eor             v1.16b, v1.16b, v1.16b
    // 0xd2618c: mov             x0, x1
    // 0xd26190: stur            x1, [fp, #-8]
    // 0xd26194: stur            d0, [fp, #-0x68]
    // 0xd26198: CheckStackOverflow
    //     0xd26198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2619c: cmp             SP, x16
    //     0xd261a0: b.ls            #0xd26b74
    // 0xd261a4: fcmp            d0, d1
    // 0xd261a8: b.le            #0xd261b8
    // 0xd261ac: r2 = Instance_ScrollDirection
    //     0xd261ac: add             x2, PP, #0x4f, lsl #12  ; [pp+0x4f810] Obj!ScrollDirection@e35301
    //     0xd261b0: ldr             x2, [x2, #0x810]
    // 0xd261b4: b               #0xd261c0
    // 0xd261b8: r2 = Instance_ScrollDirection
    //     0xd261b8: add             x2, PP, #0x4f, lsl #12  ; [pp+0x4f818] Obj!ScrollDirection@e35321
    //     0xd261bc: ldr             x2, [x2, #0x818]
    // 0xd261c0: mov             x1, x0
    // 0xd261c4: r0 = updateUserScrollDirection()
    //     0xd261c4: bl              #0x679094  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::updateUserScrollDirection
    // 0xd261c8: ldur            x1, [fp, #-8]
    // 0xd261cc: LoadField: r0 = r1->field_1b
    //     0xd261cc: ldur            w0, [x1, #0x1b]
    // 0xd261d0: DecompressPointer r0
    //     0xd261d0: add             x0, x0, HEAP, lsl #32
    // 0xd261d4: r16 = Sentinel
    //     0xd261d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd261d8: cmp             w0, w16
    // 0xd261dc: b.eq            #0xd26b7c
    // 0xd261e0: LoadField: r2 = r0->field_3b
    //     0xd261e0: ldur            w2, [x0, #0x3b]
    // 0xd261e4: DecompressPointer r2
    //     0xd261e4: add             x2, x2, HEAP, lsl #32
    // 0xd261e8: r16 = <_NestedScrollPosition>
    //     0xd261e8: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0xd261ec: stp             x2, x16, [SP]
    // 0xd261f0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xd261f0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xd261f4: r0 = cast()
    //     0xd261f4: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0xd261f8: str             x0, [SP]
    // 0xd261fc: r0 = length()
    //     0xd261fc: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0xd26200: cbnz            w0, #0xd26224
    // 0xd26204: ldur            x1, [fp, #-8]
    // 0xd26208: r0 = _outerPosition()
    //     0xd26208: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xd2620c: cmp             w0, NULL
    // 0xd26210: b.eq            #0xd26b84
    // 0xd26214: mov             x1, x0
    // 0xd26218: ldur            d0, [fp, #-0x68]
    // 0xd2621c: r0 = applyFullDragUpdate()
    //     0xd2621c: bl              #0xd26e98  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyFullDragUpdate
    // 0xd26220: b               #0xd26b04
    // 0xd26224: ldur            d1, [fp, #-0x68]
    // 0xd26228: d0 = 0.000000
    //     0xd26228: eor             v0.16b, v0.16b, v0.16b
    // 0xd2622c: fcmp            d0, d1
    // 0xd26230: b.le            #0xd2672c
    // 0xd26234: ldur            x1, [fp, #-8]
    // 0xd26238: LoadField: r0 = r1->field_1b
    //     0xd26238: ldur            w0, [x1, #0x1b]
    // 0xd2623c: DecompressPointer r0
    //     0xd2623c: add             x0, x0, HEAP, lsl #32
    // 0xd26240: LoadField: r2 = r0->field_3b
    //     0xd26240: ldur            w2, [x0, #0x3b]
    // 0xd26244: DecompressPointer r2
    //     0xd26244: add             x2, x2, HEAP, lsl #32
    // 0xd26248: r16 = <_NestedScrollPosition>
    //     0xd26248: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0xd2624c: stp             x2, x16, [SP]
    // 0xd26250: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xd26250: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xd26254: r0 = cast()
    //     0xd26254: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0xd26258: stur            x0, [fp, #-0x18]
    // 0xd2625c: LoadField: r2 = r0->field_7
    //     0xd2625c: ldur            w2, [x0, #7]
    // 0xd26260: DecompressPointer r2
    //     0xd26260: add             x2, x2, HEAP, lsl #32
    // 0xd26264: stur            x2, [fp, #-0x10]
    // 0xd26268: str             x0, [SP]
    // 0xd2626c: r0 = length()
    //     0xd2626c: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0xd26270: r1 = LoadInt32Instr(r0)
    //     0xd26270: sbfx            x1, x0, #1, #0x1f
    //     0xd26274: tbz             w0, #0, #0xd2627c
    //     0xd26278: ldur            x1, [x0, #7]
    // 0xd2627c: ldur            d0, [fp, #-0x68]
    // 0xd26280: stur            x1, [fp, #-0x38]
    // 0xd26284: r0 = inline_Allocate_Double()
    //     0xd26284: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xd26288: add             x0, x0, #0x10
    //     0xd2628c: cmp             x2, x0
    //     0xd26290: b.ls            #0xd26b88
    //     0xd26294: str             x0, [THR, #0x50]  ; THR::top
    //     0xd26298: sub             x0, x0, #0xf
    //     0xd2629c: movz            x2, #0xe15c
    //     0xd262a0: movk            x2, #0x3, lsl #16
    //     0xd262a4: stur            x2, [x0, #-1]
    // 0xd262a8: StoreField: r0->field_7 = d0
    //     0xd262a8: stur            d0, [x0, #7]
    // 0xd262ac: ldur            x2, [fp, #-0x18]
    // 0xd262b0: LoadField: r3 = r2->field_b
    //     0xd262b0: ldur            w3, [x2, #0xb]
    // 0xd262b4: DecompressPointer r3
    //     0xd262b4: add             x3, x3, HEAP, lsl #32
    // 0xd262b8: stur            x3, [fp, #-0x30]
    // 0xd262bc: mov             x4, x0
    // 0xd262c0: r0 = 0
    //     0xd262c0: movz            x0, #0
    // 0xd262c4: stur            x4, [fp, #-0x20]
    // 0xd262c8: stur            x0, [fp, #-0x28]
    // 0xd262cc: CheckStackOverflow
    //     0xd262cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd262d0: cmp             SP, x16
    //     0xd262d4: b.ls            #0xd26ba0
    // 0xd262d8: str             x2, [SP]
    // 0xd262dc: r0 = length()
    //     0xd262dc: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0xd262e0: r1 = LoadInt32Instr(r0)
    //     0xd262e0: sbfx            x1, x0, #1, #0x1f
    //     0xd262e4: tbz             w0, #0, #0xd262ec
    //     0xd262e8: ldur            x1, [x0, #7]
    // 0xd262ec: ldur            x2, [fp, #-0x38]
    // 0xd262f0: cmp             x2, x1
    // 0xd262f4: b.ne            #0xd26b34
    // 0xd262f8: ldur            x3, [fp, #-0x28]
    // 0xd262fc: cmp             x3, x1
    // 0xd26300: b.ge            #0xd26530
    // 0xd26304: ldur            x4, [fp, #-0x30]
    // 0xd26308: r0 = BoxInt64Instr(r3)
    //     0xd26308: sbfiz           x0, x3, #1, #0x1f
    //     0xd2630c: cmp             x3, x0, asr #1
    //     0xd26310: b.eq            #0xd2631c
    //     0xd26314: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd26318: stur            x3, [x0, #7]
    // 0xd2631c: r1 = LoadClassIdInstr(r4)
    //     0xd2631c: ldur            x1, [x4, #-1]
    //     0xd26320: ubfx            x1, x1, #0xc, #0x14
    // 0xd26324: stp             x0, x4, [SP]
    // 0xd26328: mov             x0, x1
    // 0xd2632c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd2632c: movz            x17, #0x3037
    //     0xd26330: movk            x17, #0x1, lsl #16
    //     0xd26334: add             lr, x0, x17
    //     0xd26338: ldr             lr, [x21, lr, lsl #3]
    //     0xd2633c: blr             lr
    // 0xd26340: ldur            x2, [fp, #-0x10]
    // 0xd26344: mov             x3, x0
    // 0xd26348: r1 = Null
    //     0xd26348: mov             x1, NULL
    // 0xd2634c: stur            x3, [fp, #-0x40]
    // 0xd26350: cmp             w2, NULL
    // 0xd26354: b.eq            #0xd26374
    // 0xd26358: LoadField: r4 = r2->field_1f
    //     0xd26358: ldur            w4, [x2, #0x1f]
    // 0xd2635c: DecompressPointer r4
    //     0xd2635c: add             x4, x4, HEAP, lsl #32
    // 0xd26360: r8 = C1X1
    //     0xd26360: ldr             x8, [PP, #0x6ee0]  ; [pp+0x6ee0] TypeParameter: C1X1
    // 0xd26364: LoadField: r9 = r4->field_7
    //     0xd26364: ldur            x9, [x4, #7]
    // 0xd26368: r3 = Null
    //     0xd26368: add             x3, PP, #0x50, lsl #12  ; [pp+0x50048] Null
    //     0xd2636c: ldr             x3, [x3, #0x48]
    // 0xd26370: blr             x9
    // 0xd26374: ldur            x0, [fp, #-0x28]
    // 0xd26378: add             x3, x0, #1
    // 0xd2637c: ldur            x4, [fp, #-0x40]
    // 0xd26380: stur            x3, [fp, #-0x48]
    // 0xd26384: cmp             w4, NULL
    // 0xd26388: b.ne            #0xd263bc
    // 0xd2638c: mov             x0, x4
    // 0xd26390: ldur            x2, [fp, #-0x10]
    // 0xd26394: r1 = Null
    //     0xd26394: mov             x1, NULL
    // 0xd26398: cmp             w2, NULL
    // 0xd2639c: b.eq            #0xd263bc
    // 0xd263a0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xd263a0: ldur            w4, [x2, #0x17]
    // 0xd263a4: DecompressPointer r4
    //     0xd263a4: add             x4, x4, HEAP, lsl #32
    // 0xd263a8: r8 = X0
    //     0xd263a8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xd263ac: LoadField: r9 = r4->field_7
    //     0xd263ac: ldur            x9, [x4, #7]
    // 0xd263b0: r3 = Null
    //     0xd263b0: add             x3, PP, #0x50, lsl #12  ; [pp+0x50058] Null
    //     0xd263b4: ldr             x3, [x3, #0x58]
    // 0xd263b8: blr             x9
    // 0xd263bc: ldur            x1, [fp, #-0x40]
    // 0xd263c0: d1 = 0.000000
    //     0xd263c0: eor             v1.16b, v1.16b, v1.16b
    // 0xd263c4: LoadField: r0 = r1->field_3f
    //     0xd263c4: ldur            w0, [x1, #0x3f]
    // 0xd263c8: DecompressPointer r0
    //     0xd263c8: add             x0, x0, HEAP, lsl #32
    // 0xd263cc: cmp             w0, NULL
    // 0xd263d0: b.eq            #0xd26ba8
    // 0xd263d4: LoadField: d0 = r0->field_7
    //     0xd263d4: ldur            d0, [x0, #7]
    // 0xd263d8: fcmp            d1, d0
    // 0xd263dc: b.le            #0xd2650c
    // 0xd263e0: ldur            x0, [fp, #-0x20]
    // 0xd263e4: ldur            d0, [fp, #-0x68]
    // 0xd263e8: r0 = applyClampedDragUpdate()
    //     0xd263e8: bl              #0xd26c38  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyClampedDragUpdate
    // 0xd263ec: stur            d0, [fp, #-0x70]
    // 0xd263f0: r1 = inline_Allocate_Double()
    //     0xd263f0: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xd263f4: add             x1, x1, #0x10
    //     0xd263f8: cmp             x0, x1
    //     0xd263fc: b.ls            #0xd26bac
    //     0xd26400: str             x1, [THR, #0x50]  ; THR::top
    //     0xd26404: sub             x1, x1, #0xf
    //     0xd26408: movz            x0, #0xe15c
    //     0xd2640c: movk            x0, #0x3, lsl #16
    //     0xd26410: stur            x0, [x1, #-1]
    // 0xd26414: StoreField: r1->field_7 = d0
    //     0xd26414: stur            d0, [x1, #7]
    // 0xd26418: ldur            x2, [fp, #-0x20]
    // 0xd2641c: stur            x1, [fp, #-0x40]
    // 0xd26420: r0 = 60
    //     0xd26420: movz            x0, #0x3c
    // 0xd26424: branchIfSmi(r2, 0xd26430)
    //     0xd26424: tbz             w2, #0, #0xd26430
    // 0xd26428: r0 = LoadClassIdInstr(r2)
    //     0xd26428: ldur            x0, [x2, #-1]
    //     0xd2642c: ubfx            x0, x0, #0xc, #0x14
    // 0xd26430: stp             x1, x2, [SP]
    // 0xd26434: r0 = GDT[cid_x0 + -0xfe3]()
    //     0xd26434: sub             lr, x0, #0xfe3
    //     0xd26438: ldr             lr, [x21, lr, lsl #3]
    //     0xd2643c: blr             lr
    // 0xd26440: tbnz            w0, #4, #0xd26450
    // 0xd26444: ldur            x1, [fp, #-0x20]
    // 0xd26448: d0 = 0.000000
    //     0xd26448: eor             v0.16b, v0.16b, v0.16b
    // 0xd2644c: b               #0xd26504
    // 0xd26450: ldur            x1, [fp, #-0x20]
    // 0xd26454: r0 = 60
    //     0xd26454: movz            x0, #0x3c
    // 0xd26458: branchIfSmi(r1, 0xd26464)
    //     0xd26458: tbz             w1, #0, #0xd26464
    // 0xd2645c: r0 = LoadClassIdInstr(r1)
    //     0xd2645c: ldur            x0, [x1, #-1]
    //     0xd26460: ubfx            x0, x0, #0xc, #0x14
    // 0xd26464: ldur            x16, [fp, #-0x40]
    // 0xd26468: stp             x16, x1, [SP]
    // 0xd2646c: r0 = GDT[cid_x0 + -0xfd2]()
    //     0xd2646c: sub             lr, x0, #0xfd2
    //     0xd26470: ldr             lr, [x21, lr, lsl #3]
    //     0xd26474: blr             lr
    // 0xd26478: tbnz            w0, #4, #0xd26488
    // 0xd2647c: ldur            x1, [fp, #-0x40]
    // 0xd26480: d0 = 0.000000
    //     0xd26480: eor             v0.16b, v0.16b, v0.16b
    // 0xd26484: b               #0xd26504
    // 0xd26488: ldur            x1, [fp, #-0x20]
    // 0xd2648c: r0 = 60
    //     0xd2648c: movz            x0, #0x3c
    // 0xd26490: branchIfSmi(r1, 0xd2649c)
    //     0xd26490: tbz             w1, #0, #0xd2649c
    // 0xd26494: r0 = LoadClassIdInstr(r1)
    //     0xd26494: ldur            x0, [x1, #-1]
    //     0xd26498: ubfx            x0, x0, #0xc, #0x14
    // 0xd2649c: cmp             x0, #0x3e
    // 0xd264a0: b.ne            #0xd264f0
    // 0xd264a4: d0 = 0.000000
    //     0xd264a4: eor             v0.16b, v0.16b, v0.16b
    // 0xd264a8: LoadField: d1 = r1->field_7
    //     0xd264a8: ldur            d1, [x1, #7]
    // 0xd264ac: fcmp            d1, d0
    // 0xd264b0: b.ne            #0xd264e8
    // 0xd264b4: ldur            d2, [fp, #-0x70]
    // 0xd264b8: fadd            d3, d1, d2
    // 0xd264bc: r1 = inline_Allocate_Double()
    //     0xd264bc: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xd264c0: add             x1, x1, #0x10
    //     0xd264c4: cmp             x0, x1
    //     0xd264c8: b.ls            #0xd26bc0
    //     0xd264cc: str             x1, [THR, #0x50]  ; THR::top
    //     0xd264d0: sub             x1, x1, #0xf
    //     0xd264d4: movz            x0, #0xe15c
    //     0xd264d8: movk            x0, #0x3, lsl #16
    //     0xd264dc: stur            x0, [x1, #-1]
    // 0xd264e0: StoreField: r1->field_7 = d3
    //     0xd264e0: stur            d3, [x1, #7]
    // 0xd264e4: b               #0xd26504
    // 0xd264e8: ldur            d2, [fp, #-0x70]
    // 0xd264ec: b               #0xd264f8
    // 0xd264f0: ldur            d2, [fp, #-0x70]
    // 0xd264f4: d0 = 0.000000
    //     0xd264f4: eor             v0.16b, v0.16b, v0.16b
    // 0xd264f8: fcmp            d2, d2
    // 0xd264fc: b.vc            #0xd26504
    // 0xd26500: ldur            x1, [fp, #-0x40]
    // 0xd26504: mov             x4, x1
    // 0xd26508: b               #0xd26518
    // 0xd2650c: ldur            x1, [fp, #-0x20]
    // 0xd26510: mov             v0.16b, v1.16b
    // 0xd26514: mov             x4, x1
    // 0xd26518: ldur            x0, [fp, #-0x48]
    // 0xd2651c: ldur            d0, [fp, #-0x68]
    // 0xd26520: ldur            x2, [fp, #-0x18]
    // 0xd26524: ldur            x3, [fp, #-0x30]
    // 0xd26528: ldur            x1, [fp, #-0x38]
    // 0xd2652c: b               #0xd262c4
    // 0xd26530: ldur            x1, [fp, #-0x20]
    // 0xd26534: d0 = 0.000000
    //     0xd26534: eor             v0.16b, v0.16b, v0.16b
    // 0xd26538: LoadField: d1 = r1->field_7
    //     0xd26538: ldur            d1, [x1, #7]
    // 0xd2653c: stur            d1, [fp, #-0x70]
    // 0xd26540: fcmp            d1, d0
    // 0xd26544: b.ne            #0xd26558
    // 0xd26548: d2 = 0.000000
    //     0xd26548: ldr             d2, [PP, #0x6fc8]  ; [pp+0x6fc8] IMM: double(1e-10) from 0x3ddb7cdfd9d7bdbb
    // 0xd2654c: fcmp            d0, d2
    // 0xd26550: b.le            #0xd26b04
    // 0xd26554: b               #0xd2657c
    // 0xd26558: d2 = 0.000000
    //     0xd26558: ldr             d2, [PP, #0x6fc8]  ; [pp+0x6fc8] IMM: double(1e-10) from 0x3ddb7cdfd9d7bdbb
    // 0xd2655c: fcmp            d0, d1
    // 0xd26560: b.le            #0xd26574
    // 0xd26564: fneg            d3, d1
    // 0xd26568: fcmp            d3, d2
    // 0xd2656c: b.le            #0xd26b04
    // 0xd26570: b               #0xd2657c
    // 0xd26574: fcmp            d1, d2
    // 0xd26578: b.le            #0xd26b04
    // 0xd2657c: ldur            x1, [fp, #-8]
    // 0xd26580: r0 = _outerPosition()
    //     0xd26580: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xd26584: cmp             w0, NULL
    // 0xd26588: b.eq            #0xd26bd4
    // 0xd2658c: mov             x1, x0
    // 0xd26590: ldur            d0, [fp, #-0x70]
    // 0xd26594: r0 = applyClampedDragUpdate()
    //     0xd26594: bl              #0xd26c38  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyClampedDragUpdate
    // 0xd26598: mov             v1.16b, v0.16b
    // 0xd2659c: d0 = 0.000000
    //     0xd2659c: eor             v0.16b, v0.16b, v0.16b
    // 0xd265a0: stur            d1, [fp, #-0x70]
    // 0xd265a4: fcmp            d1, d0
    // 0xd265a8: b.eq            #0xd26b04
    // 0xd265ac: ldur            x0, [fp, #-8]
    // 0xd265b0: LoadField: r1 = r0->field_1b
    //     0xd265b0: ldur            w1, [x0, #0x1b]
    // 0xd265b4: DecompressPointer r1
    //     0xd265b4: add             x1, x1, HEAP, lsl #32
    // 0xd265b8: r16 = Sentinel
    //     0xd265b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd265bc: cmp             w1, w16
    // 0xd265c0: b.eq            #0xd26bd8
    // 0xd265c4: LoadField: r0 = r1->field_3b
    //     0xd265c4: ldur            w0, [x1, #0x3b]
    // 0xd265c8: DecompressPointer r0
    //     0xd265c8: add             x0, x0, HEAP, lsl #32
    // 0xd265cc: r16 = <_NestedScrollPosition>
    //     0xd265cc: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0xd265d0: stp             x0, x16, [SP]
    // 0xd265d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xd265d4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xd265d8: r0 = cast()
    //     0xd265d8: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0xd265dc: stur            x0, [fp, #-0x20]
    // 0xd265e0: LoadField: r2 = r0->field_7
    //     0xd265e0: ldur            w2, [x0, #7]
    // 0xd265e4: DecompressPointer r2
    //     0xd265e4: add             x2, x2, HEAP, lsl #32
    // 0xd265e8: stur            x2, [fp, #-0x10]
    // 0xd265ec: str             x0, [SP]
    // 0xd265f0: r0 = length()
    //     0xd265f0: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0xd265f4: r1 = LoadInt32Instr(r0)
    //     0xd265f4: sbfx            x1, x0, #1, #0x1f
    //     0xd265f8: tbz             w0, #0, #0xd26600
    //     0xd265fc: ldur            x1, [x0, #7]
    // 0xd26600: ldur            x0, [fp, #-0x20]
    // 0xd26604: stur            x1, [fp, #-0x38]
    // 0xd26608: LoadField: r2 = r0->field_b
    //     0xd26608: ldur            w2, [x0, #0xb]
    // 0xd2660c: DecompressPointer r2
    //     0xd2660c: add             x2, x2, HEAP, lsl #32
    // 0xd26610: stur            x2, [fp, #-0x30]
    // 0xd26614: r3 = 0
    //     0xd26614: movz            x3, #0
    // 0xd26618: stur            x3, [fp, #-0x28]
    // 0xd2661c: CheckStackOverflow
    //     0xd2661c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd26620: cmp             SP, x16
    //     0xd26624: b.ls            #0xd26be0
    // 0xd26628: str             x0, [SP]
    // 0xd2662c: r0 = length()
    //     0xd2662c: bl              #0x9134a0  ; [dart:_internal] _CastIterableBase::length
    // 0xd26630: r1 = LoadInt32Instr(r0)
    //     0xd26630: sbfx            x1, x0, #1, #0x1f
    //     0xd26634: tbz             w0, #0, #0xd2663c
    //     0xd26638: ldur            x1, [x0, #7]
    // 0xd2663c: ldur            x2, [fp, #-0x38]
    // 0xd26640: cmp             x2, x1
    // 0xd26644: b.ne            #0xd26b14
    // 0xd26648: ldur            x3, [fp, #-0x28]
    // 0xd2664c: cmp             x3, x1
    // 0xd26650: b.ge            #0xd26b04
    // 0xd26654: ldur            x4, [fp, #-0x30]
    // 0xd26658: r0 = BoxInt64Instr(r3)
    //     0xd26658: sbfiz           x0, x3, #1, #0x1f
    //     0xd2665c: cmp             x3, x0, asr #1
    //     0xd26660: b.eq            #0xd2666c
    //     0xd26664: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd26668: stur            x3, [x0, #7]
    // 0xd2666c: r1 = LoadClassIdInstr(r4)
    //     0xd2666c: ldur            x1, [x4, #-1]
    //     0xd26670: ubfx            x1, x1, #0xc, #0x14
    // 0xd26674: stp             x0, x4, [SP]
    // 0xd26678: mov             x0, x1
    // 0xd2667c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd2667c: movz            x17, #0x3037
    //     0xd26680: movk            x17, #0x1, lsl #16
    //     0xd26684: add             lr, x0, x17
    //     0xd26688: ldr             lr, [x21, lr, lsl #3]
    //     0xd2668c: blr             lr
    // 0xd26690: ldur            x2, [fp, #-0x10]
    // 0xd26694: mov             x3, x0
    // 0xd26698: r1 = Null
    //     0xd26698: mov             x1, NULL
    // 0xd2669c: stur            x3, [fp, #-0x40]
    // 0xd266a0: cmp             w2, NULL
    // 0xd266a4: b.eq            #0xd266c4
    // 0xd266a8: LoadField: r4 = r2->field_1f
    //     0xd266a8: ldur            w4, [x2, #0x1f]
    // 0xd266ac: DecompressPointer r4
    //     0xd266ac: add             x4, x4, HEAP, lsl #32
    // 0xd266b0: r8 = C1X1
    //     0xd266b0: ldr             x8, [PP, #0x6ee0]  ; [pp+0x6ee0] TypeParameter: C1X1
    // 0xd266b4: LoadField: r9 = r4->field_7
    //     0xd266b4: ldur            x9, [x4, #7]
    // 0xd266b8: r3 = Null
    //     0xd266b8: add             x3, PP, #0x50, lsl #12  ; [pp+0x50068] Null
    //     0xd266bc: ldr             x3, [x3, #0x68]
    // 0xd266c0: blr             x9
    // 0xd266c4: ldur            x0, [fp, #-0x28]
    // 0xd266c8: add             x3, x0, #1
    // 0xd266cc: ldur            x4, [fp, #-0x40]
    // 0xd266d0: stur            x3, [fp, #-0x48]
    // 0xd266d4: cmp             w4, NULL
    // 0xd266d8: b.ne            #0xd2670c
    // 0xd266dc: mov             x0, x4
    // 0xd266e0: ldur            x2, [fp, #-0x10]
    // 0xd266e4: r1 = Null
    //     0xd266e4: mov             x1, NULL
    // 0xd266e8: cmp             w2, NULL
    // 0xd266ec: b.eq            #0xd2670c
    // 0xd266f0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xd266f0: ldur            w4, [x2, #0x17]
    // 0xd266f4: DecompressPointer r4
    //     0xd266f4: add             x4, x4, HEAP, lsl #32
    // 0xd266f8: r8 = X0
    //     0xd266f8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xd266fc: LoadField: r9 = r4->field_7
    //     0xd266fc: ldur            x9, [x4, #7]
    // 0xd26700: r3 = Null
    //     0xd26700: add             x3, PP, #0x50, lsl #12  ; [pp+0x50078] Null
    //     0xd26704: ldr             x3, [x3, #0x78]
    // 0xd26708: blr             x9
    // 0xd2670c: ldur            x1, [fp, #-0x40]
    // 0xd26710: ldur            d0, [fp, #-0x70]
    // 0xd26714: r0 = applyFullDragUpdate()
    //     0xd26714: bl              #0xd26e98  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyFullDragUpdate
    // 0xd26718: ldur            x3, [fp, #-0x48]
    // 0xd2671c: ldur            x0, [fp, #-0x20]
    // 0xd26720: ldur            x2, [fp, #-0x30]
    // 0xd26724: ldur            x1, [fp, #-0x38]
    // 0xd26728: b               #0xd26618
    // 0xd2672c: ldur            x0, [fp, #-8]
    // 0xd26730: fcmp            d1, d0
    // 0xd26734: b.eq            #0xd26b04
    // 0xd26738: r1 = <double>
    //     0xd26738: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xd2673c: r2 = 0
    //     0xd2673c: movz            x2, #0
    // 0xd26740: r0 = _GrowableList()
    //     0xd26740: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xd26744: ldur            x1, [fp, #-8]
    // 0xd26748: stur            x0, [fp, #-0x10]
    // 0xd2674c: LoadField: r2 = r1->field_1b
    //     0xd2674c: ldur            w2, [x1, #0x1b]
    // 0xd26750: DecompressPointer r2
    //     0xd26750: add             x2, x2, HEAP, lsl #32
    // 0xd26754: LoadField: r3 = r2->field_3b
    //     0xd26754: ldur            w3, [x2, #0x3b]
    // 0xd26758: DecompressPointer r3
    //     0xd26758: add             x3, x3, HEAP, lsl #32
    // 0xd2675c: r16 = <_NestedScrollPosition>
    //     0xd2675c: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0xd26760: stp             x3, x16, [SP]
    // 0xd26764: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xd26764: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xd26768: r0 = cast()
    //     0xd26768: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0xd2676c: mov             x1, x0
    // 0xd26770: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xd26770: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xd26774: r0 = toList()
    //     0xd26774: bl              #0x8637dc  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::toList
    // 0xd26778: mov             x3, x0
    // 0xd2677c: stur            x3, [fp, #-0x58]
    // 0xd26780: LoadField: r4 = r3->field_7
    //     0xd26780: ldur            w4, [x3, #7]
    // 0xd26784: DecompressPointer r4
    //     0xd26784: add             x4, x4, HEAP, lsl #32
    // 0xd26788: stur            x4, [fp, #-0x50]
    // 0xd2678c: LoadField: r0 = r3->field_b
    //     0xd2678c: ldur            w0, [x3, #0xb]
    // 0xd26790: r5 = LoadInt32Instr(r0)
    //     0xd26790: sbfx            x5, x0, #1, #0x1f
    // 0xd26794: stur            x5, [fp, #-0x38]
    // 0xd26798: ldur            x6, [fp, #-0x10]
    // 0xd2679c: r7 = 0.000000
    //     0xd2679c: ldr             x7, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xd267a0: r0 = 0
    //     0xd267a0: movz            x0, #0
    // 0xd267a4: stur            x7, [fp, #-0x40]
    // 0xd267a8: CheckStackOverflow
    //     0xd267a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd267ac: cmp             SP, x16
    //     0xd267b0: b.ls            #0xd26be8
    // 0xd267b4: LoadField: r1 = r3->field_b
    //     0xd267b4: ldur            w1, [x3, #0xb]
    // 0xd267b8: r2 = LoadInt32Instr(r1)
    //     0xd267b8: sbfx            x2, x1, #1, #0x1f
    // 0xd267bc: cmp             x5, x2
    // 0xd267c0: b.ne            #0xd26b54
    // 0xd267c4: cmp             x0, x2
    // 0xd267c8: b.ge            #0xd269f0
    // 0xd267cc: LoadField: r1 = r3->field_f
    //     0xd267cc: ldur            w1, [x3, #0xf]
    // 0xd267d0: DecompressPointer r1
    //     0xd267d0: add             x1, x1, HEAP, lsl #32
    // 0xd267d4: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0xd267d4: add             x16, x1, x0, lsl #2
    //     0xd267d8: ldur            w8, [x16, #0xf]
    // 0xd267dc: DecompressPointer r8
    //     0xd267dc: add             x8, x8, HEAP, lsl #32
    // 0xd267e0: stur            x8, [fp, #-0x30]
    // 0xd267e4: add             x9, x0, #1
    // 0xd267e8: stur            x9, [fp, #-0x28]
    // 0xd267ec: cmp             w8, NULL
    // 0xd267f0: b.ne            #0xd26824
    // 0xd267f4: mov             x0, x8
    // 0xd267f8: mov             x2, x4
    // 0xd267fc: r1 = Null
    //     0xd267fc: mov             x1, NULL
    // 0xd26800: cmp             w2, NULL
    // 0xd26804: b.eq            #0xd26824
    // 0xd26808: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xd26808: ldur            w4, [x2, #0x17]
    // 0xd2680c: DecompressPointer r4
    //     0xd2680c: add             x4, x4, HEAP, lsl #32
    // 0xd26810: r8 = X0
    //     0xd26810: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xd26814: LoadField: r9 = r4->field_7
    //     0xd26814: ldur            x9, [x4, #7]
    // 0xd26818: r3 = Null
    //     0xd26818: add             x3, PP, #0x50, lsl #12  ; [pp+0x50088] Null
    //     0xd2681c: ldr             x3, [x3, #0x88]
    // 0xd26820: blr             x9
    // 0xd26824: ldur            x0, [fp, #-0x40]
    // 0xd26828: ldur            x1, [fp, #-0x30]
    // 0xd2682c: ldur            d0, [fp, #-0x68]
    // 0xd26830: r0 = applyClampedDragUpdate()
    //     0xd26830: bl              #0xd26c38  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyClampedDragUpdate
    // 0xd26834: stur            d0, [fp, #-0x70]
    // 0xd26838: r1 = inline_Allocate_Double()
    //     0xd26838: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xd2683c: add             x1, x1, #0x10
    //     0xd26840: cmp             x0, x1
    //     0xd26844: b.ls            #0xd26bf0
    //     0xd26848: str             x1, [THR, #0x50]  ; THR::top
    //     0xd2684c: sub             x1, x1, #0xf
    //     0xd26850: movz            x0, #0xe15c
    //     0xd26854: movk            x0, #0x3, lsl #16
    //     0xd26858: stur            x0, [x1, #-1]
    // 0xd2685c: StoreField: r1->field_7 = d0
    //     0xd2685c: stur            d0, [x1, #7]
    // 0xd26860: ldur            x2, [fp, #-0x40]
    // 0xd26864: stur            x1, [fp, #-0x30]
    // 0xd26868: r0 = 60
    //     0xd26868: movz            x0, #0x3c
    // 0xd2686c: branchIfSmi(r2, 0xd26878)
    //     0xd2686c: tbz             w2, #0, #0xd26878
    // 0xd26870: r0 = LoadClassIdInstr(r2)
    //     0xd26870: ldur            x0, [x2, #-1]
    //     0xd26874: ubfx            x0, x0, #0xc, #0x14
    // 0xd26878: stp             x1, x2, [SP]
    // 0xd2687c: r0 = GDT[cid_x0 + -0xfe3]()
    //     0xd2687c: sub             lr, x0, #0xfe3
    //     0xd26880: ldr             lr, [x21, lr, lsl #3]
    //     0xd26884: blr             lr
    // 0xd26888: tbnz            w0, #4, #0xd26898
    // 0xd2688c: ldur            x7, [fp, #-0x40]
    // 0xd26890: d0 = 0.000000
    //     0xd26890: eor             v0.16b, v0.16b, v0.16b
    // 0xd26894: b               #0xd26958
    // 0xd26898: ldur            x1, [fp, #-0x40]
    // 0xd2689c: r0 = 60
    //     0xd2689c: movz            x0, #0x3c
    // 0xd268a0: branchIfSmi(r1, 0xd268ac)
    //     0xd268a0: tbz             w1, #0, #0xd268ac
    // 0xd268a4: r0 = LoadClassIdInstr(r1)
    //     0xd268a4: ldur            x0, [x1, #-1]
    //     0xd268a8: ubfx            x0, x0, #0xc, #0x14
    // 0xd268ac: ldur            x16, [fp, #-0x30]
    // 0xd268b0: stp             x16, x1, [SP]
    // 0xd268b4: r0 = GDT[cid_x0 + -0xfd2]()
    //     0xd268b4: sub             lr, x0, #0xfd2
    //     0xd268b8: ldr             lr, [x21, lr, lsl #3]
    //     0xd268bc: blr             lr
    // 0xd268c0: tbnz            w0, #4, #0xd268d0
    // 0xd268c4: ldur            x7, [fp, #-0x30]
    // 0xd268c8: d0 = 0.000000
    //     0xd268c8: eor             v0.16b, v0.16b, v0.16b
    // 0xd268cc: b               #0xd26958
    // 0xd268d0: ldur            x1, [fp, #-0x40]
    // 0xd268d4: r0 = 60
    //     0xd268d4: movz            x0, #0x3c
    // 0xd268d8: branchIfSmi(r1, 0xd268e4)
    //     0xd268d8: tbz             w1, #0, #0xd268e4
    // 0xd268dc: r0 = LoadClassIdInstr(r1)
    //     0xd268dc: ldur            x0, [x1, #-1]
    //     0xd268e0: ubfx            x0, x0, #0xc, #0x14
    // 0xd268e4: cmp             x0, #0x3e
    // 0xd268e8: b.ne            #0xd2693c
    // 0xd268ec: d0 = 0.000000
    //     0xd268ec: eor             v0.16b, v0.16b, v0.16b
    // 0xd268f0: LoadField: d1 = r1->field_7
    //     0xd268f0: ldur            d1, [x1, #7]
    // 0xd268f4: fcmp            d1, d0
    // 0xd268f8: b.ne            #0xd26934
    // 0xd268fc: ldur            d2, [fp, #-0x70]
    // 0xd26900: fadd            d3, d1, d2
    // 0xd26904: r1 = inline_Allocate_Double()
    //     0xd26904: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xd26908: add             x1, x1, #0x10
    //     0xd2690c: cmp             x0, x1
    //     0xd26910: b.ls            #0xd26c04
    //     0xd26914: str             x1, [THR, #0x50]  ; THR::top
    //     0xd26918: sub             x1, x1, #0xf
    //     0xd2691c: movz            x0, #0xe15c
    //     0xd26920: movk            x0, #0x3, lsl #16
    //     0xd26924: stur            x0, [x1, #-1]
    // 0xd26928: StoreField: r1->field_7 = d3
    //     0xd26928: stur            d3, [x1, #7]
    // 0xd2692c: mov             x7, x1
    // 0xd26930: b               #0xd26958
    // 0xd26934: ldur            d2, [fp, #-0x70]
    // 0xd26938: b               #0xd26944
    // 0xd2693c: ldur            d2, [fp, #-0x70]
    // 0xd26940: d0 = 0.000000
    //     0xd26940: eor             v0.16b, v0.16b, v0.16b
    // 0xd26944: fcmp            d2, d2
    // 0xd26948: b.vc            #0xd26954
    // 0xd2694c: ldur            x7, [fp, #-0x30]
    // 0xd26950: b               #0xd26958
    // 0xd26954: mov             x7, x1
    // 0xd26958: ldur            x0, [fp, #-0x10]
    // 0xd2695c: stur            x7, [fp, #-0x60]
    // 0xd26960: LoadField: r1 = r0->field_b
    //     0xd26960: ldur            w1, [x0, #0xb]
    // 0xd26964: LoadField: r2 = r0->field_f
    //     0xd26964: ldur            w2, [x0, #0xf]
    // 0xd26968: DecompressPointer r2
    //     0xd26968: add             x2, x2, HEAP, lsl #32
    // 0xd2696c: LoadField: r3 = r2->field_b
    //     0xd2696c: ldur            w3, [x2, #0xb]
    // 0xd26970: r2 = LoadInt32Instr(r1)
    //     0xd26970: sbfx            x2, x1, #1, #0x1f
    // 0xd26974: stur            x2, [fp, #-0x48]
    // 0xd26978: r1 = LoadInt32Instr(r3)
    //     0xd26978: sbfx            x1, x3, #1, #0x1f
    // 0xd2697c: cmp             x2, x1
    // 0xd26980: b.ne            #0xd2698c
    // 0xd26984: mov             x1, x0
    // 0xd26988: r0 = _growToNextCapacity()
    //     0xd26988: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xd2698c: ldur            x2, [fp, #-0x10]
    // 0xd26990: ldur            x3, [fp, #-0x48]
    // 0xd26994: add             x0, x3, #1
    // 0xd26998: lsl             x1, x0, #1
    // 0xd2699c: StoreField: r2->field_b = r1
    //     0xd2699c: stur            w1, [x2, #0xb]
    // 0xd269a0: LoadField: r1 = r2->field_f
    //     0xd269a0: ldur            w1, [x2, #0xf]
    // 0xd269a4: DecompressPointer r1
    //     0xd269a4: add             x1, x1, HEAP, lsl #32
    // 0xd269a8: ldur            x0, [fp, #-0x30]
    // 0xd269ac: ArrayStore: r1[r3] = r0  ; List_4
    //     0xd269ac: add             x25, x1, x3, lsl #2
    //     0xd269b0: add             x25, x25, #0xf
    //     0xd269b4: str             w0, [x25]
    //     0xd269b8: tbz             w0, #0, #0xd269d4
    //     0xd269bc: ldurb           w16, [x1, #-1]
    //     0xd269c0: ldurb           w17, [x0, #-1]
    //     0xd269c4: and             x16, x17, x16, lsr #2
    //     0xd269c8: tst             x16, HEAP, lsr #32
    //     0xd269cc: b.eq            #0xd269d4
    //     0xd269d0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xd269d4: ldur            x7, [fp, #-0x60]
    // 0xd269d8: ldur            x0, [fp, #-0x28]
    // 0xd269dc: mov             x6, x2
    // 0xd269e0: ldur            x3, [fp, #-0x58]
    // 0xd269e4: ldur            x4, [fp, #-0x50]
    // 0xd269e8: ldur            x5, [fp, #-0x38]
    // 0xd269ec: b               #0xd267a4
    // 0xd269f0: mov             x2, x6
    // 0xd269f4: mov             x1, x7
    // 0xd269f8: d0 = 0.000000
    //     0xd269f8: eor             v0.16b, v0.16b, v0.16b
    // 0xd269fc: LoadField: d1 = r1->field_7
    //     0xd269fc: ldur            d1, [x1, #7]
    // 0xd26a00: stur            d1, [fp, #-0x68]
    // 0xd26a04: fcmp            d1, d0
    // 0xd26a08: b.eq            #0xd26a60
    // 0xd26a0c: ldur            x1, [fp, #-8]
    // 0xd26a10: r0 = _outerPosition()
    //     0xd26a10: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xd26a14: cmp             w0, NULL
    // 0xd26a18: b.eq            #0xd26c18
    // 0xd26a1c: mov             x1, x0
    // 0xd26a20: ldur            d0, [fp, #-0x68]
    // 0xd26a24: r0 = applyClampedDragUpdate()
    //     0xd26a24: bl              #0xd26c38  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyClampedDragUpdate
    // 0xd26a28: mov             v1.16b, v0.16b
    // 0xd26a2c: ldur            d0, [fp, #-0x68]
    // 0xd26a30: fsub            d2, d0, d1
    // 0xd26a34: r0 = inline_Allocate_Double()
    //     0xd26a34: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xd26a38: add             x0, x0, #0x10
    //     0xd26a3c: cmp             x1, x0
    //     0xd26a40: b.ls            #0xd26c1c
    //     0xd26a44: str             x0, [THR, #0x50]  ; THR::top
    //     0xd26a48: sub             x0, x0, #0xf
    //     0xd26a4c: movz            x1, #0xe15c
    //     0xd26a50: movk            x1, #0x3, lsl #16
    //     0xd26a54: stur            x1, [x0, #-1]
    // 0xd26a58: StoreField: r0->field_7 = d2
    //     0xd26a58: stur            d2, [x0, #7]
    // 0xd26a5c: b               #0xd26a64
    // 0xd26a60: mov             x0, x1
    // 0xd26a64: LoadField: d1 = r0->field_7
    //     0xd26a64: ldur            d1, [x0, #7]
    // 0xd26a68: stur            d1, [fp, #-0x68]
    // 0xd26a6c: r4 = 0
    //     0xd26a6c: movz            x4, #0
    // 0xd26a70: ldur            x2, [fp, #-0x10]
    // 0xd26a74: ldur            x3, [fp, #-0x58]
    // 0xd26a78: d2 = 0.000000
    //     0xd26a78: eor             v2.16b, v2.16b, v2.16b
    // 0xd26a7c: stur            x4, [fp, #-0x28]
    // 0xd26a80: CheckStackOverflow
    //     0xd26a80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd26a84: cmp             SP, x16
    //     0xd26a88: b.ls            #0xd26c2c
    // 0xd26a8c: LoadField: r0 = r3->field_b
    //     0xd26a8c: ldur            w0, [x3, #0xb]
    // 0xd26a90: r1 = LoadInt32Instr(r0)
    //     0xd26a90: sbfx            x1, x0, #1, #0x1f
    // 0xd26a94: cmp             x4, x1
    // 0xd26a98: b.ge            #0xd26b04
    // 0xd26a9c: LoadField: r0 = r2->field_b
    //     0xd26a9c: ldur            w0, [x2, #0xb]
    // 0xd26aa0: r1 = LoadInt32Instr(r0)
    //     0xd26aa0: sbfx            x1, x0, #1, #0x1f
    // 0xd26aa4: mov             x0, x1
    // 0xd26aa8: mov             x1, x4
    // 0xd26aac: cmp             x1, x0
    // 0xd26ab0: b.hs            #0xd26c34
    // 0xd26ab4: LoadField: r0 = r2->field_f
    //     0xd26ab4: ldur            w0, [x2, #0xf]
    // 0xd26ab8: DecompressPointer r0
    //     0xd26ab8: add             x0, x0, HEAP, lsl #32
    // 0xd26abc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xd26abc: add             x16, x0, x4, lsl #2
    //     0xd26ac0: ldur            w1, [x16, #0xf]
    // 0xd26ac4: DecompressPointer r1
    //     0xd26ac4: add             x1, x1, HEAP, lsl #32
    // 0xd26ac8: LoadField: d0 = r1->field_7
    //     0xd26ac8: ldur            d0, [x1, #7]
    // 0xd26acc: fsub            d3, d0, d1
    // 0xd26ad0: fcmp            d3, d2
    // 0xd26ad4: b.le            #0xd26af4
    // 0xd26ad8: LoadField: r0 = r3->field_f
    //     0xd26ad8: ldur            w0, [x3, #0xf]
    // 0xd26adc: DecompressPointer r0
    //     0xd26adc: add             x0, x0, HEAP, lsl #32
    // 0xd26ae0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xd26ae0: add             x16, x0, x4, lsl #2
    //     0xd26ae4: ldur            w1, [x16, #0xf]
    // 0xd26ae8: DecompressPointer r1
    //     0xd26ae8: add             x1, x1, HEAP, lsl #32
    // 0xd26aec: mov             v0.16b, v3.16b
    // 0xd26af0: r0 = applyFullDragUpdate()
    //     0xd26af0: bl              #0xd26e98  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::applyFullDragUpdate
    // 0xd26af4: ldur            x0, [fp, #-0x28]
    // 0xd26af8: add             x4, x0, #1
    // 0xd26afc: ldur            d1, [fp, #-0x68]
    // 0xd26b00: b               #0xd26a70
    // 0xd26b04: r0 = Null
    //     0xd26b04: mov             x0, NULL
    // 0xd26b08: LeaveFrame
    //     0xd26b08: mov             SP, fp
    //     0xd26b0c: ldp             fp, lr, [SP], #0x10
    // 0xd26b10: ret
    //     0xd26b10: ret             
    // 0xd26b14: ldur            x0, [fp, #-0x20]
    // 0xd26b18: r0 = ConcurrentModificationError()
    //     0xd26b18: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xd26b1c: mov             x1, x0
    // 0xd26b20: ldur            x0, [fp, #-0x20]
    // 0xd26b24: StoreField: r1->field_b = r0
    //     0xd26b24: stur            w0, [x1, #0xb]
    // 0xd26b28: mov             x0, x1
    // 0xd26b2c: r0 = Throw()
    //     0xd26b2c: bl              #0xec04b8  ; ThrowStub
    // 0xd26b30: brk             #0
    // 0xd26b34: ldur            x0, [fp, #-0x18]
    // 0xd26b38: r0 = ConcurrentModificationError()
    //     0xd26b38: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xd26b3c: mov             x1, x0
    // 0xd26b40: ldur            x0, [fp, #-0x18]
    // 0xd26b44: StoreField: r1->field_b = r0
    //     0xd26b44: stur            w0, [x1, #0xb]
    // 0xd26b48: mov             x0, x1
    // 0xd26b4c: r0 = Throw()
    //     0xd26b4c: bl              #0xec04b8  ; ThrowStub
    // 0xd26b50: brk             #0
    // 0xd26b54: mov             x0, x3
    // 0xd26b58: r0 = ConcurrentModificationError()
    //     0xd26b58: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xd26b5c: mov             x1, x0
    // 0xd26b60: ldur            x0, [fp, #-0x58]
    // 0xd26b64: StoreField: r1->field_b = r0
    //     0xd26b64: stur            w0, [x1, #0xb]
    // 0xd26b68: mov             x0, x1
    // 0xd26b6c: r0 = Throw()
    //     0xd26b6c: bl              #0xec04b8  ; ThrowStub
    // 0xd26b70: brk             #0
    // 0xd26b74: r0 = StackOverflowSharedWithFPURegs()
    //     0xd26b74: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd26b78: b               #0xd261a4
    // 0xd26b7c: r9 = _innerController
    //     0xd26b7c: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0xd26b80: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd26b80: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd26b84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd26b84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd26b88: SaveReg d0
    //     0xd26b88: str             q0, [SP, #-0x10]!
    // 0xd26b8c: SaveReg r1
    //     0xd26b8c: str             x1, [SP, #-8]!
    // 0xd26b90: r0 = AllocateDouble()
    //     0xd26b90: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd26b94: RestoreReg r1
    //     0xd26b94: ldr             x1, [SP], #8
    // 0xd26b98: RestoreReg d0
    //     0xd26b98: ldr             q0, [SP], #0x10
    // 0xd26b9c: b               #0xd262a8
    // 0xd26ba0: r0 = StackOverflowSharedWithFPURegs()
    //     0xd26ba0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd26ba4: b               #0xd262d8
    // 0xd26ba8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xd26ba8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xd26bac: SaveReg d0
    //     0xd26bac: str             q0, [SP, #-0x10]!
    // 0xd26bb0: r0 = AllocateDouble()
    //     0xd26bb0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd26bb4: mov             x1, x0
    // 0xd26bb8: RestoreReg d0
    //     0xd26bb8: ldr             q0, [SP], #0x10
    // 0xd26bbc: b               #0xd26414
    // 0xd26bc0: stp             q0, q3, [SP, #-0x20]!
    // 0xd26bc4: r0 = AllocateDouble()
    //     0xd26bc4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd26bc8: mov             x1, x0
    // 0xd26bcc: ldp             q0, q3, [SP], #0x20
    // 0xd26bd0: b               #0xd264e0
    // 0xd26bd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd26bd4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd26bd8: r9 = _innerController
    //     0xd26bd8: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0xd26bdc: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xd26bdc: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xd26be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd26be0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd26be4: b               #0xd26628
    // 0xd26be8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd26be8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd26bec: b               #0xd267b4
    // 0xd26bf0: SaveReg d0
    //     0xd26bf0: str             q0, [SP, #-0x10]!
    // 0xd26bf4: r0 = AllocateDouble()
    //     0xd26bf4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd26bf8: mov             x1, x0
    // 0xd26bfc: RestoreReg d0
    //     0xd26bfc: ldr             q0, [SP], #0x10
    // 0xd26c00: b               #0xd2685c
    // 0xd26c04: stp             q0, q3, [SP, #-0x20]!
    // 0xd26c08: r0 = AllocateDouble()
    //     0xd26c08: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd26c0c: mov             x1, x0
    // 0xd26c10: ldp             q0, q3, [SP], #0x20
    // 0xd26c14: b               #0xd26928
    // 0xd26c18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd26c18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd26c1c: SaveReg d2
    //     0xd26c1c: str             q2, [SP, #-0x10]!
    // 0xd26c20: r0 = AllocateDouble()
    //     0xd26c20: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd26c24: RestoreReg d2
    //     0xd26c24: ldr             q2, [SP], #0x10
    // 0xd26c28: b               #0xd26a58
    // 0xd26c2c: r0 = StackOverflowSharedWithFPURegs()
    //     0xd26c2c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd26c30: b               #0xd26a8c
    // 0xd26c34: r0 = RangeErrorSharedWithFPURegs()
    //     0xd26c34: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
  _ goIdle(/* No info */) {
    // ** addr: 0xdb2ef4, size: 0x64
    // 0xdb2ef4: EnterFrame
    //     0xdb2ef4: stp             fp, lr, [SP, #-0x10]!
    //     0xdb2ef8: mov             fp, SP
    // 0xdb2efc: AllocStack(0x8)
    //     0xdb2efc: sub             SP, SP, #8
    // 0xdb2f00: SetupParameters(_NestedScrollCoordinator this /* r1 => r0, fp-0x8 */)
    //     0xdb2f00: mov             x0, x1
    //     0xdb2f04: stur            x1, [fp, #-8]
    // 0xdb2f08: CheckStackOverflow
    //     0xdb2f08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb2f0c: cmp             SP, x16
    //     0xdb2f10: b.ls            #0xdb2f4c
    // 0xdb2f14: mov             x1, x0
    // 0xdb2f18: r0 = _outerPosition()
    //     0xdb2f18: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xdb2f1c: cmp             w0, NULL
    // 0xdb2f20: b.eq            #0xdb2f54
    // 0xdb2f24: mov             x1, x0
    // 0xdb2f28: r0 = _createIdleScrollActivity()
    //     0xdb2f28: bl              #0xdb2f58  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_createIdleScrollActivity
    // 0xdb2f2c: ldur            x1, [fp, #-8]
    // 0xdb2f30: mov             x2, x0
    // 0xdb2f34: r3 = Closure: (_NestedScrollPosition) => IdleScrollActivity from Function '_createIdleScrollActivity@303016527': static.
    //     0xdb2f34: ldr             x3, [PP, #0x76f0]  ; [pp+0x76f0] Closure: (_NestedScrollPosition) => IdleScrollActivity from Function '_createIdleScrollActivity@303016527': static. (0x7e54fb7b2f88)
    // 0xdb2f38: r0 = beginActivity()
    //     0xdb2f38: bl              #0x678e3c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::beginActivity
    // 0xdb2f3c: r0 = Null
    //     0xdb2f3c: mov             x0, NULL
    // 0xdb2f40: LeaveFrame
    //     0xdb2f40: mov             SP, fp
    //     0xdb2f44: ldp             fp, lr, [SP], #0x10
    // 0xdb2f48: ret
    //     0xdb2f48: ret             
    // 0xdb2f4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb2f4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb2f50: b               #0xdb2f14
    // 0xdb2f54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb2f54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _createIdleScrollActivity(/* No info */) {
    // ** addr: 0xdb2f58, size: 0x30
    // 0xdb2f58: EnterFrame
    //     0xdb2f58: stp             fp, lr, [SP, #-0x10]!
    //     0xdb2f5c: mov             fp, SP
    // 0xdb2f60: AllocStack(0x8)
    //     0xdb2f60: sub             SP, SP, #8
    // 0xdb2f64: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xdb2f64: stur            x1, [fp, #-8]
    // 0xdb2f68: r0 = IdleScrollActivity()
    //     0xdb2f68: bl              #0xbf9cac  ; AllocateIdleScrollActivityStub -> IdleScrollActivity (size=0x10)
    // 0xdb2f6c: r1 = false
    //     0xdb2f6c: add             x1, NULL, #0x30  ; false
    // 0xdb2f70: StoreField: r0->field_b = r1
    //     0xdb2f70: stur            w1, [x0, #0xb]
    // 0xdb2f74: ldur            x1, [fp, #-8]
    // 0xdb2f78: StoreField: r0->field_7 = r1
    //     0xdb2f78: stur            w1, [x0, #7]
    // 0xdb2f7c: LeaveFrame
    //     0xdb2f7c: mov             SP, fp
    //     0xdb2f80: ldp             fp, lr, [SP], #0x10
    // 0xdb2f84: ret
    //     0xdb2f84: ret             
  }
  [closure] static IdleScrollActivity _createIdleScrollActivity(dynamic, _NestedScrollPosition) {
    // ** addr: 0xdb2f88, size: 0x30
    // 0xdb2f88: EnterFrame
    //     0xdb2f88: stp             fp, lr, [SP, #-0x10]!
    //     0xdb2f8c: mov             fp, SP
    // 0xdb2f90: CheckStackOverflow
    //     0xdb2f90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb2f94: cmp             SP, x16
    //     0xdb2f98: b.ls            #0xdb2fb0
    // 0xdb2f9c: ldr             x1, [fp, #0x10]
    // 0xdb2fa0: r0 = _createIdleScrollActivity()
    //     0xdb2fa0: bl              #0xdb2f58  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_createIdleScrollActivity
    // 0xdb2fa4: LeaveFrame
    //     0xdb2fa4: mov             SP, fp
    //     0xdb2fa8: ldp             fp, lr, [SP], #0x10
    // 0xdb2fac: ret
    //     0xdb2fac: ret             
    // 0xdb2fb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb2fb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb2fb4: b               #0xdb2f9c
  }
  get _ axisDirection(/* No info */) {
    // ** addr: 0xdb8ec8, size: 0x5c
    // 0xdb8ec8: EnterFrame
    //     0xdb8ec8: stp             fp, lr, [SP, #-0x10]!
    //     0xdb8ecc: mov             fp, SP
    // 0xdb8ed0: CheckStackOverflow
    //     0xdb8ed0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb8ed4: cmp             SP, x16
    //     0xdb8ed8: b.ls            #0xdb8f14
    // 0xdb8edc: r0 = _outerPosition()
    //     0xdb8edc: bl              #0x67b350  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_outerPosition
    // 0xdb8ee0: cmp             w0, NULL
    // 0xdb8ee4: b.eq            #0xdb8f1c
    // 0xdb8ee8: LoadField: r1 = r0->field_27
    //     0xdb8ee8: ldur            w1, [x0, #0x27]
    // 0xdb8eec: DecompressPointer r1
    //     0xdb8eec: add             x1, x1, HEAP, lsl #32
    // 0xdb8ef0: LoadField: r2 = r1->field_b
    //     0xdb8ef0: ldur            w2, [x1, #0xb]
    // 0xdb8ef4: DecompressPointer r2
    //     0xdb8ef4: add             x2, x2, HEAP, lsl #32
    // 0xdb8ef8: cmp             w2, NULL
    // 0xdb8efc: b.eq            #0xdb8f20
    // 0xdb8f00: LoadField: r0 = r2->field_b
    //     0xdb8f00: ldur            w0, [x2, #0xb]
    // 0xdb8f04: DecompressPointer r0
    //     0xdb8f04: add             x0, x0, HEAP, lsl #32
    // 0xdb8f08: LeaveFrame
    //     0xdb8f08: mov             SP, fp
    //     0xdb8f0c: ldp             fp, lr, [SP], #0x10
    // 0xdb8f10: ret
    //     0xdb8f10: ret             
    // 0xdb8f14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb8f14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb8f18: b               #0xdb8edc
    // 0xdb8f1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb8f1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdb8f20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb8f20: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ goBallistic(/* No info */) {
    // ** addr: 0xdb9198, size: 0xc4
    // 0xdb9198: EnterFrame
    //     0xdb9198: stp             fp, lr, [SP, #-0x10]!
    //     0xdb919c: mov             fp, SP
    // 0xdb91a0: AllocStack(0x18)
    //     0xdb91a0: sub             SP, SP, #0x18
    // 0xdb91a4: SetupParameters(_NestedScrollCoordinator this /* r1 => r1, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x18 */)
    //     0xdb91a4: stur            x1, [fp, #-8]
    //     0xdb91a8: stur            d0, [fp, #-0x18]
    // 0xdb91ac: CheckStackOverflow
    //     0xdb91ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb91b0: cmp             SP, x16
    //     0xdb91b4: b.ls            #0xdb9238
    // 0xdb91b8: r1 = 2
    //     0xdb91b8: movz            x1, #0x2
    // 0xdb91bc: r0 = AllocateContext()
    //     0xdb91bc: bl              #0xec126c  ; AllocateContextStub
    // 0xdb91c0: mov             x2, x0
    // 0xdb91c4: ldur            x0, [fp, #-8]
    // 0xdb91c8: stur            x2, [fp, #-0x10]
    // 0xdb91cc: StoreField: r2->field_f = r0
    //     0xdb91cc: stur            w0, [x2, #0xf]
    // 0xdb91d0: ldur            d0, [fp, #-0x18]
    // 0xdb91d4: r1 = inline_Allocate_Double()
    //     0xdb91d4: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xdb91d8: add             x1, x1, #0x10
    //     0xdb91dc: cmp             x3, x1
    //     0xdb91e0: b.ls            #0xdb9240
    //     0xdb91e4: str             x1, [THR, #0x50]  ; THR::top
    //     0xdb91e8: sub             x1, x1, #0xf
    //     0xdb91ec: movz            x3, #0xe15c
    //     0xdb91f0: movk            x3, #0x3, lsl #16
    //     0xdb91f4: stur            x3, [x1, #-1]
    // 0xdb91f8: StoreField: r1->field_7 = d0
    //     0xdb91f8: stur            d0, [x1, #7]
    // 0xdb91fc: StoreField: r2->field_13 = r1
    //     0xdb91fc: stur            w1, [x2, #0x13]
    // 0xdb9200: mov             x1, x0
    // 0xdb9204: r0 = createOuterBallisticScrollActivity()
    //     0xdb9204: bl              #0xbfa584  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::createOuterBallisticScrollActivity
    // 0xdb9208: ldur            x2, [fp, #-0x10]
    // 0xdb920c: r1 = Function '<anonymous closure>':.
    //     0xdb920c: ldr             x1, [PP, #0x75b8]  ; [pp+0x75b8] AnonymousClosure: (0xdb925c), in [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::goBallistic (0xdb9198)
    // 0xdb9210: stur            x0, [fp, #-0x10]
    // 0xdb9214: r0 = AllocateClosure()
    //     0xdb9214: bl              #0xec1630  ; AllocateClosureStub
    // 0xdb9218: ldur            x1, [fp, #-8]
    // 0xdb921c: ldur            x2, [fp, #-0x10]
    // 0xdb9220: mov             x3, x0
    // 0xdb9224: r0 = beginActivity()
    //     0xdb9224: bl              #0x678e3c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::beginActivity
    // 0xdb9228: r0 = Null
    //     0xdb9228: mov             x0, NULL
    // 0xdb922c: LeaveFrame
    //     0xdb922c: mov             SP, fp
    //     0xdb9230: ldp             fp, lr, [SP], #0x10
    // 0xdb9234: ret
    //     0xdb9234: ret             
    // 0xdb9238: r0 = StackOverflowSharedWithFPURegs()
    //     0xdb9238: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xdb923c: b               #0xdb91b8
    // 0xdb9240: SaveReg d0
    //     0xdb9240: str             q0, [SP, #-0x10]!
    // 0xdb9244: stp             x0, x2, [SP, #-0x10]!
    // 0xdb9248: r0 = AllocateDouble()
    //     0xdb9248: bl              #0xec2254  ; AllocateDoubleStub
    // 0xdb924c: mov             x1, x0
    // 0xdb9250: ldp             x0, x2, [SP], #0x10
    // 0xdb9254: RestoreReg d0
    //     0xdb9254: ldr             q0, [SP], #0x10
    // 0xdb9258: b               #0xdb91f8
  }
  [closure] ScrollActivity <anonymous closure>(dynamic, _NestedScrollPosition) {
    // ** addr: 0xdb925c, size: 0x54
    // 0xdb925c: EnterFrame
    //     0xdb925c: stp             fp, lr, [SP, #-0x10]!
    //     0xdb9260: mov             fp, SP
    // 0xdb9264: ldr             x0, [fp, #0x18]
    // 0xdb9268: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xdb9268: ldur            w1, [x0, #0x17]
    // 0xdb926c: DecompressPointer r1
    //     0xdb926c: add             x1, x1, HEAP, lsl #32
    // 0xdb9270: CheckStackOverflow
    //     0xdb9270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb9274: cmp             SP, x16
    //     0xdb9278: b.ls            #0xdb92a8
    // 0xdb927c: LoadField: r0 = r1->field_f
    //     0xdb927c: ldur            w0, [x1, #0xf]
    // 0xdb9280: DecompressPointer r0
    //     0xdb9280: add             x0, x0, HEAP, lsl #32
    // 0xdb9284: LoadField: r2 = r1->field_13
    //     0xdb9284: ldur            w2, [x1, #0x13]
    // 0xdb9288: DecompressPointer r2
    //     0xdb9288: add             x2, x2, HEAP, lsl #32
    // 0xdb928c: LoadField: d0 = r2->field_7
    //     0xdb928c: ldur            d0, [x2, #7]
    // 0xdb9290: mov             x1, x0
    // 0xdb9294: ldr             x2, [fp, #0x10]
    // 0xdb9298: r0 = createInnerBallisticScrollActivity()
    //     0xdb9298: bl              #0xbf94bc  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::createInnerBallisticScrollActivity
    // 0xdb929c: LeaveFrame
    //     0xdb929c: mov             SP, fp
    //     0xdb92a0: ldp             fp, lr, [SP], #0x10
    // 0xdb92a4: ret
    //     0xdb92a4: ret             
    // 0xdb92a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb92a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb92ac: b               #0xdb927c
  }
  _ cancel(/* No info */) {
    // ** addr: 0xdba320, size: 0x34
    // 0xdba320: EnterFrame
    //     0xdba320: stp             fp, lr, [SP, #-0x10]!
    //     0xdba324: mov             fp, SP
    // 0xdba328: CheckStackOverflow
    //     0xdba328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdba32c: cmp             SP, x16
    //     0xdba330: b.ls            #0xdba34c
    // 0xdba334: d0 = 0.000000
    //     0xdba334: eor             v0.16b, v0.16b, v0.16b
    // 0xdba338: r0 = goBallistic()
    //     0xdba338: bl              #0xdb9198  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::goBallistic
    // 0xdba33c: r0 = Null
    //     0xdba33c: mov             x0, NULL
    // 0xdba340: LeaveFrame
    //     0xdba340: mov             SP, fp
    //     0xdba344: ldp             fp, lr, [SP], #0x10
    // 0xdba348: ret
    //     0xdba348: ret             
    // 0xdba34c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdba34c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdba350: b               #0xdba334
  }
}

// class id: 2984, size: 0x58, field offset: 0x54
//   transformed mixin,
abstract class _RenderSliverOverlapAbsorber&RenderSliver&RenderObjectWithChildMixin extends RenderSliver
     with RenderObjectWithChildMixin<X0 bound RenderObject> {

  set _ child=(/* No info */) {
    // ** addr: 0x898ff8, size: 0xcc
    // 0x898ff8: EnterFrame
    //     0x898ff8: stp             fp, lr, [SP, #-0x10]!
    //     0x898ffc: mov             fp, SP
    // 0x899000: AllocStack(0x10)
    //     0x899000: sub             SP, SP, #0x10
    // 0x899004: SetupParameters(_RenderSliverOverlapAbsorber&RenderSliver&RenderObjectWithChildMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x899004: mov             x4, x1
    //     0x899008: mov             x3, x2
    //     0x89900c: stur            x1, [fp, #-8]
    //     0x899010: stur            x2, [fp, #-0x10]
    // 0x899014: CheckStackOverflow
    //     0x899014: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x899018: cmp             SP, x16
    //     0x89901c: b.ls            #0x8990bc
    // 0x899020: mov             x0, x3
    // 0x899024: r2 = Null
    //     0x899024: mov             x2, NULL
    // 0x899028: r1 = Null
    //     0x899028: mov             x1, NULL
    // 0x89902c: r4 = 60
    //     0x89902c: movz            x4, #0x3c
    // 0x899030: branchIfSmi(r0, 0x89903c)
    //     0x899030: tbz             w0, #0, #0x89903c
    // 0x899034: r4 = LoadClassIdInstr(r0)
    //     0x899034: ldur            x4, [x0, #-1]
    //     0x899038: ubfx            x4, x4, #0xc, #0x14
    // 0x89903c: sub             x4, x4, #0xb86
    // 0x899040: cmp             x4, #0x28
    // 0x899044: b.ls            #0x89905c
    // 0x899048: r8 = RenderSliver?
    //     0x899048: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5f0] Type: RenderSliver?
    //     0x89904c: ldr             x8, [x8, #0x5f0]
    // 0x899050: r3 = Null
    //     0x899050: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a5f8] Null
    //     0x899054: ldr             x3, [x3, #0x5f8]
    // 0x899058: r0 = DefaultNullableTypeTest()
    //     0x899058: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x89905c: ldur            x0, [fp, #-8]
    // 0x899060: LoadField: r2 = r0->field_53
    //     0x899060: ldur            w2, [x0, #0x53]
    // 0x899064: DecompressPointer r2
    //     0x899064: add             x2, x2, HEAP, lsl #32
    // 0x899068: cmp             w2, NULL
    // 0x89906c: b.eq            #0x899078
    // 0x899070: mov             x1, x0
    // 0x899074: r0 = dropChild()
    //     0x899074: bl              #0x8019dc  ; [package:flutter/src/rendering/object.dart] RenderObject::dropChild
    // 0x899078: ldur            x1, [fp, #-8]
    // 0x89907c: ldur            x2, [fp, #-0x10]
    // 0x899080: mov             x0, x2
    // 0x899084: StoreField: r1->field_53 = r0
    //     0x899084: stur            w0, [x1, #0x53]
    //     0x899088: ldurb           w16, [x1, #-1]
    //     0x89908c: ldurb           w17, [x0, #-1]
    //     0x899090: and             x16, x17, x16, lsr #2
    //     0x899094: tst             x16, HEAP, lsr #32
    //     0x899098: b.eq            #0x8990a0
    //     0x89909c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8990a0: cmp             w2, NULL
    // 0x8990a4: b.eq            #0x8990ac
    // 0x8990a8: r0 = adoptChild()
    //     0x8990a8: bl              #0x8057b8  ; [package:flutter/src/rendering/object.dart] RenderObject::adoptChild
    // 0x8990ac: r0 = Null
    //     0x8990ac: mov             x0, NULL
    // 0x8990b0: LeaveFrame
    //     0x8990b0: mov             SP, fp
    //     0x8990b4: ldp             fp, lr, [SP], #0x10
    // 0x8990b8: ret
    //     0x8990b8: ret             
    // 0x8990bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8990bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8990c0: b               #0x899020
  }
}

// class id: 3024, size: 0xac, field offset: 0xa8
class RenderNestedScrollViewViewport extends RenderViewport {

  [closure] void markNeedsLayout(dynamic) {
    // ** addr: 0x7644dc, size: 0x38
    // 0x7644dc: EnterFrame
    //     0x7644dc: stp             fp, lr, [SP, #-0x10]!
    //     0x7644e0: mov             fp, SP
    // 0x7644e4: ldr             x0, [fp, #0x10]
    // 0x7644e8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7644e8: ldur            w1, [x0, #0x17]
    // 0x7644ec: DecompressPointer r1
    //     0x7644ec: add             x1, x1, HEAP, lsl #32
    // 0x7644f0: CheckStackOverflow
    //     0x7644f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7644f4: cmp             SP, x16
    //     0x7644f8: b.ls            #0x76450c
    // 0x7644fc: r0 = markNeedsLayout()
    //     0x7644fc: bl              #0x803e98  ; [package:flutter/src/widgets/nested_scroll_view.dart] RenderNestedScrollViewViewport::markNeedsLayout
    // 0x764500: LeaveFrame
    //     0x764500: mov             SP, fp
    //     0x764504: ldp             fp, lr, [SP], #0x10
    // 0x764508: ret
    //     0x764508: ret             
    // 0x76450c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76450c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x764510: b               #0x7644fc
  }
  _ markNeedsLayout(/* No info */) {
    // ** addr: 0x803e98, size: 0x4c
    // 0x803e98: EnterFrame
    //     0x803e98: stp             fp, lr, [SP, #-0x10]!
    //     0x803e9c: mov             fp, SP
    // 0x803ea0: AllocStack(0x8)
    //     0x803ea0: sub             SP, SP, #8
    // 0x803ea4: SetupParameters(RenderNestedScrollViewViewport this /* r1 => r0, fp-0x8 */)
    //     0x803ea4: mov             x0, x1
    //     0x803ea8: stur            x1, [fp, #-8]
    // 0x803eac: CheckStackOverflow
    //     0x803eac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x803eb0: cmp             SP, x16
    //     0x803eb4: b.ls            #0x803edc
    // 0x803eb8: LoadField: r1 = r0->field_a7
    //     0x803eb8: ldur            w1, [x0, #0xa7]
    // 0x803ebc: DecompressPointer r1
    //     0x803ebc: add             x1, x1, HEAP, lsl #32
    // 0x803ec0: r0 = notifyListeners()
    //     0x803ec0: bl              #0x6462b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners
    // 0x803ec4: ldur            x1, [fp, #-8]
    // 0x803ec8: r0 = markNeedsLayout()
    //     0x803ec8: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0x803ecc: r0 = Null
    //     0x803ecc: mov             x0, NULL
    // 0x803ed0: LeaveFrame
    //     0x803ed0: mov             SP, fp
    //     0x803ed4: ldp             fp, lr, [SP], #0x10
    // 0x803ed8: ret
    //     0x803ed8: ret             
    // 0x803edc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x803edc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x803ee0: b               #0x803eb8
  }
  set _ handle=(/* No info */) {
    // ** addr: 0xc6b6ac, size: 0x7c
    // 0xc6b6ac: EnterFrame
    //     0xc6b6ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc6b6b0: mov             fp, SP
    // 0xc6b6b4: mov             x16, x2
    // 0xc6b6b8: mov             x2, x1
    // 0xc6b6bc: mov             x1, x16
    // 0xc6b6c0: CheckStackOverflow
    //     0xc6b6c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6b6c4: cmp             SP, x16
    //     0xc6b6c8: b.ls            #0xc6b720
    // 0xc6b6cc: LoadField: r0 = r2->field_a7
    //     0xc6b6cc: ldur            w0, [x2, #0xa7]
    // 0xc6b6d0: DecompressPointer r0
    //     0xc6b6d0: add             x0, x0, HEAP, lsl #32
    // 0xc6b6d4: cmp             w0, w1
    // 0xc6b6d8: b.ne            #0xc6b6ec
    // 0xc6b6dc: r0 = Null
    //     0xc6b6dc: mov             x0, NULL
    // 0xc6b6e0: LeaveFrame
    //     0xc6b6e0: mov             SP, fp
    //     0xc6b6e4: ldp             fp, lr, [SP], #0x10
    // 0xc6b6e8: ret
    //     0xc6b6e8: ret             
    // 0xc6b6ec: mov             x0, x1
    // 0xc6b6f0: StoreField: r2->field_a7 = r0
    //     0xc6b6f0: stur            w0, [x2, #0xa7]
    //     0xc6b6f4: ldurb           w16, [x2, #-1]
    //     0xc6b6f8: ldurb           w17, [x0, #-1]
    //     0xc6b6fc: and             x16, x17, x16, lsr #2
    //     0xc6b700: tst             x16, HEAP, lsr #32
    //     0xc6b704: b.eq            #0xc6b70c
    //     0xc6b708: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc6b70c: r0 = notifyListeners()
    //     0xc6b70c: bl              #0x6462b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners
    // 0xc6b710: r0 = Null
    //     0xc6b710: mov             x0, NULL
    // 0xc6b714: LeaveFrame
    //     0xc6b714: mov             SP, fp
    //     0xc6b718: ldp             fp, lr, [SP], #0x10
    // 0xc6b71c: ret
    //     0xc6b71c: ret             
    // 0xc6b720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6b720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6b724: b               #0xc6b6cc
  }
}

// class id: 3623, size: 0x24, field offset: 0x24
class SliverOverlapAbsorberHandle extends ChangeNotifier {
}

// class id: 3649, size: 0x44, field offset: 0x40
class _NestedScrollController extends ScrollController {

  get _ nestedPositions(/* No info */) {
    // ** addr: 0x6793b4, size: 0x44
    // 0x6793b4: EnterFrame
    //     0x6793b4: stp             fp, lr, [SP, #-0x10]!
    //     0x6793b8: mov             fp, SP
    // 0x6793bc: AllocStack(0x10)
    //     0x6793bc: sub             SP, SP, #0x10
    // 0x6793c0: CheckStackOverflow
    //     0x6793c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6793c4: cmp             SP, x16
    //     0x6793c8: b.ls            #0x6793f0
    // 0x6793cc: LoadField: r0 = r1->field_3b
    //     0x6793cc: ldur            w0, [x1, #0x3b]
    // 0x6793d0: DecompressPointer r0
    //     0x6793d0: add             x0, x0, HEAP, lsl #32
    // 0x6793d4: r16 = <_NestedScrollPosition>
    //     0x6793d4: ldr             x16, [PP, #0x6f18]  ; [pp+0x6f18] TypeArguments: <_NestedScrollPosition>
    // 0x6793d8: stp             x0, x16, [SP]
    // 0x6793dc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x6793dc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x6793e0: r0 = cast()
    //     0x6793e0: bl              #0x864dd4  ; [dart:collection] ListBase::cast
    // 0x6793e4: LeaveFrame
    //     0x6793e4: mov             SP, fp
    //     0x6793e8: ldp             fp, lr, [SP], #0x10
    // 0x6793ec: ret
    //     0x6793ec: ret             
    // 0x6793f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6793f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6793f4: b               #0x6793cc
  }
  _ _scheduleUpdateShadow(/* No info */) {
    // ** addr: 0x99b59c, size: 0x130
    // 0x99b59c: EnterFrame
    //     0x99b59c: stp             fp, lr, [SP, #-0x10]!
    //     0x99b5a0: mov             fp, SP
    // 0x99b5a4: AllocStack(0x18)
    //     0x99b5a4: sub             SP, SP, #0x18
    // 0x99b5a8: SetupParameters(_NestedScrollController this /* r1 => r1, fp-0x8 */)
    //     0x99b5a8: stur            x1, [fp, #-8]
    // 0x99b5ac: CheckStackOverflow
    //     0x99b5ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99b5b0: cmp             SP, x16
    //     0x99b5b4: b.ls            #0x99b6c0
    // 0x99b5b8: r1 = 1
    //     0x99b5b8: movz            x1, #0x1
    // 0x99b5bc: r0 = AllocateContext()
    //     0x99b5bc: bl              #0xec126c  ; AllocateContextStub
    // 0x99b5c0: mov             x1, x0
    // 0x99b5c4: ldur            x0, [fp, #-8]
    // 0x99b5c8: StoreField: r1->field_f = r0
    //     0x99b5c8: stur            w0, [x1, #0xf]
    // 0x99b5cc: r0 = LoadStaticField(0x958)
    //     0x99b5cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x99b5d0: ldr             x0, [x0, #0x12b0]
    // 0x99b5d4: cmp             w0, NULL
    // 0x99b5d8: b.eq            #0x99b6c8
    // 0x99b5dc: LoadField: r3 = r0->field_53
    //     0x99b5dc: ldur            w3, [x0, #0x53]
    // 0x99b5e0: DecompressPointer r3
    //     0x99b5e0: add             x3, x3, HEAP, lsl #32
    // 0x99b5e4: stur            x3, [fp, #-0x10]
    // 0x99b5e8: LoadField: r0 = r3->field_7
    //     0x99b5e8: ldur            w0, [x3, #7]
    // 0x99b5ec: DecompressPointer r0
    //     0x99b5ec: add             x0, x0, HEAP, lsl #32
    // 0x99b5f0: mov             x2, x1
    // 0x99b5f4: stur            x0, [fp, #-8]
    // 0x99b5f8: r1 = Function '<anonymous closure>':.
    //     0x99b5f8: add             x1, PP, #0x46, lsl #12  ; [pp+0x46088] AnonymousClosure: (0x99b6cc), in [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollController::_scheduleUpdateShadow (0x99b59c)
    //     0x99b5fc: ldr             x1, [x1, #0x88]
    // 0x99b600: r0 = AllocateClosure()
    //     0x99b600: bl              #0xec1630  ; AllocateClosureStub
    // 0x99b604: ldur            x2, [fp, #-8]
    // 0x99b608: mov             x3, x0
    // 0x99b60c: r1 = Null
    //     0x99b60c: mov             x1, NULL
    // 0x99b610: stur            x3, [fp, #-8]
    // 0x99b614: cmp             w2, NULL
    // 0x99b618: b.eq            #0x99b638
    // 0x99b61c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x99b61c: ldur            w4, [x2, #0x17]
    // 0x99b620: DecompressPointer r4
    //     0x99b620: add             x4, x4, HEAP, lsl #32
    // 0x99b624: r8 = X0
    //     0x99b624: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x99b628: LoadField: r9 = r4->field_7
    //     0x99b628: ldur            x9, [x4, #7]
    // 0x99b62c: r3 = Null
    //     0x99b62c: add             x3, PP, #0x46, lsl #12  ; [pp+0x46090] Null
    //     0x99b630: ldr             x3, [x3, #0x90]
    // 0x99b634: blr             x9
    // 0x99b638: ldur            x0, [fp, #-0x10]
    // 0x99b63c: LoadField: r1 = r0->field_b
    //     0x99b63c: ldur            w1, [x0, #0xb]
    // 0x99b640: LoadField: r2 = r0->field_f
    //     0x99b640: ldur            w2, [x0, #0xf]
    // 0x99b644: DecompressPointer r2
    //     0x99b644: add             x2, x2, HEAP, lsl #32
    // 0x99b648: LoadField: r3 = r2->field_b
    //     0x99b648: ldur            w3, [x2, #0xb]
    // 0x99b64c: r2 = LoadInt32Instr(r1)
    //     0x99b64c: sbfx            x2, x1, #1, #0x1f
    // 0x99b650: stur            x2, [fp, #-0x18]
    // 0x99b654: r1 = LoadInt32Instr(r3)
    //     0x99b654: sbfx            x1, x3, #1, #0x1f
    // 0x99b658: cmp             x2, x1
    // 0x99b65c: b.ne            #0x99b668
    // 0x99b660: mov             x1, x0
    // 0x99b664: r0 = _growToNextCapacity()
    //     0x99b664: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x99b668: ldur            x2, [fp, #-0x10]
    // 0x99b66c: ldur            x3, [fp, #-0x18]
    // 0x99b670: add             x4, x3, #1
    // 0x99b674: lsl             x5, x4, #1
    // 0x99b678: StoreField: r2->field_b = r5
    //     0x99b678: stur            w5, [x2, #0xb]
    // 0x99b67c: LoadField: r1 = r2->field_f
    //     0x99b67c: ldur            w1, [x2, #0xf]
    // 0x99b680: DecompressPointer r1
    //     0x99b680: add             x1, x1, HEAP, lsl #32
    // 0x99b684: ldur            x0, [fp, #-8]
    // 0x99b688: ArrayStore: r1[r3] = r0  ; List_4
    //     0x99b688: add             x25, x1, x3, lsl #2
    //     0x99b68c: add             x25, x25, #0xf
    //     0x99b690: str             w0, [x25]
    //     0x99b694: tbz             w0, #0, #0x99b6b0
    //     0x99b698: ldurb           w16, [x1, #-1]
    //     0x99b69c: ldurb           w17, [x0, #-1]
    //     0x99b6a0: and             x16, x17, x16, lsr #2
    //     0x99b6a4: tst             x16, HEAP, lsr #32
    //     0x99b6a8: b.eq            #0x99b6b0
    //     0x99b6ac: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x99b6b0: r0 = Null
    //     0x99b6b0: mov             x0, NULL
    // 0x99b6b4: LeaveFrame
    //     0x99b6b4: mov             SP, fp
    //     0x99b6b8: ldp             fp, lr, [SP], #0x10
    // 0x99b6bc: ret
    //     0x99b6bc: ret             
    // 0x99b6c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99b6c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99b6c4: b               #0x99b5b8
    // 0x99b6c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99b6c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x99b6cc, size: 0x4c
    // 0x99b6cc: EnterFrame
    //     0x99b6cc: stp             fp, lr, [SP, #-0x10]!
    //     0x99b6d0: mov             fp, SP
    // 0x99b6d4: ldr             x0, [fp, #0x18]
    // 0x99b6d8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x99b6d8: ldur            w1, [x0, #0x17]
    // 0x99b6dc: DecompressPointer r1
    //     0x99b6dc: add             x1, x1, HEAP, lsl #32
    // 0x99b6e0: CheckStackOverflow
    //     0x99b6e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99b6e4: cmp             SP, x16
    //     0x99b6e8: b.ls            #0x99b710
    // 0x99b6ec: LoadField: r0 = r1->field_f
    //     0x99b6ec: ldur            w0, [x1, #0xf]
    // 0x99b6f0: DecompressPointer r0
    //     0x99b6f0: add             x0, x0, HEAP, lsl #32
    // 0x99b6f4: LoadField: r1 = r0->field_3f
    //     0x99b6f4: ldur            w1, [x0, #0x3f]
    // 0x99b6f8: DecompressPointer r1
    //     0x99b6f8: add             x1, x1, HEAP, lsl #32
    // 0x99b6fc: r0 = updateShadow()
    //     0x99b6fc: bl              #0x99b718  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::updateShadow
    // 0x99b700: r0 = Null
    //     0x99b700: mov             x0, NULL
    // 0x99b704: LeaveFrame
    //     0x99b704: mov             SP, fp
    //     0x99b708: ldp             fp, lr, [SP], #0x10
    // 0x99b70c: ret
    //     0x99b70c: ret             
    // 0x99b710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99b710: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99b714: b               #0x99b6ec
  }
  [closure] void _scheduleUpdateShadow(dynamic) {
    // ** addr: 0x99beac, size: 0x38
    // 0x99beac: EnterFrame
    //     0x99beac: stp             fp, lr, [SP, #-0x10]!
    //     0x99beb0: mov             fp, SP
    // 0x99beb4: ldr             x0, [fp, #0x10]
    // 0x99beb8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x99beb8: ldur            w1, [x0, #0x17]
    // 0x99bebc: DecompressPointer r1
    //     0x99bebc: add             x1, x1, HEAP, lsl #32
    // 0x99bec0: CheckStackOverflow
    //     0x99bec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99bec4: cmp             SP, x16
    //     0x99bec8: b.ls            #0x99bedc
    // 0x99becc: r0 = _scheduleUpdateShadow()
    //     0x99becc: bl              #0x99b59c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollController::_scheduleUpdateShadow
    // 0x99bed0: LeaveFrame
    //     0x99bed0: mov             SP, fp
    //     0x99bed4: ldp             fp, lr, [SP], #0x10
    // 0x99bed8: ret
    //     0x99bed8: ret             
    // 0x99bedc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99bedc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99bee0: b               #0x99becc
  }
}

// class id: 3654, size: 0x74, field offset: 0x6c
class _NestedScrollPosition extends ScrollPosition
    implements ScrollActivityDelegate {

  _ createDrivenScrollActivity(/* No info */) {
    // ** addr: 0x67a828, size: 0xa8
    // 0x67a828: EnterFrame
    //     0x67a828: stp             fp, lr, [SP, #-0x10]!
    //     0x67a82c: mov             fp, SP
    // 0x67a830: AllocStack(0x38)
    //     0x67a830: sub             SP, SP, #0x38
    // 0x67a834: SetupParameters(_NestedScrollPosition this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r5, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* d0 => d1, fp-0x30 */)
    //     0x67a834: mov             x0, x1
    //     0x67a838: mov             v1.16b, v0.16b
    //     0x67a83c: mov             x5, x2
    //     0x67a840: stur            x1, [fp, #-0x10]
    //     0x67a844: stur            x2, [fp, #-0x18]
    //     0x67a848: stur            x3, [fp, #-0x20]
    //     0x67a84c: stur            d0, [fp, #-0x30]
    // 0x67a850: CheckStackOverflow
    //     0x67a850: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67a854: cmp             SP, x16
    //     0x67a858: b.ls            #0x67a8c4
    // 0x67a85c: LoadField: r2 = r0->field_3f
    //     0x67a85c: ldur            w2, [x0, #0x3f]
    // 0x67a860: DecompressPointer r2
    //     0x67a860: add             x2, x2, HEAP, lsl #32
    // 0x67a864: stur            x2, [fp, #-8]
    // 0x67a868: cmp             w2, NULL
    // 0x67a86c: b.eq            #0x67a8cc
    // 0x67a870: mov             x1, x0
    // 0x67a874: r0 = element()
    //     0x67a874: bl              #0xdc719c  ; [package:flutter_html/src/tree/replaced_element.dart] RubyElement::element
    // 0x67a878: mov             x1, x0
    // 0x67a87c: ldur            x0, [fp, #-8]
    // 0x67a880: stur            x1, [fp, #-0x28]
    // 0x67a884: LoadField: d0 = r0->field_7
    //     0x67a884: ldur            d0, [x0, #7]
    // 0x67a888: stur            d0, [fp, #-0x38]
    // 0x67a88c: r0 = DrivenScrollActivity()
    //     0x67a88c: bl              #0x67b100  ; AllocateDrivenScrollActivityStub -> DrivenScrollActivity (size=0x18)
    // 0x67a890: mov             x1, x0
    // 0x67a894: ldur            x2, [fp, #-0x10]
    // 0x67a898: ldur            x3, [fp, #-0x20]
    // 0x67a89c: ldur            x5, [fp, #-0x18]
    // 0x67a8a0: ldur            d0, [fp, #-0x38]
    // 0x67a8a4: ldur            d1, [fp, #-0x30]
    // 0x67a8a8: ldur            x6, [fp, #-0x28]
    // 0x67a8ac: stur            x0, [fp, #-8]
    // 0x67a8b0: r0 = DrivenScrollActivity()
    //     0x67a8b0: bl              #0x67a8d0  ; [package:flutter/src/widgets/scroll_activity.dart] DrivenScrollActivity::DrivenScrollActivity
    // 0x67a8b4: ldur            x0, [fp, #-8]
    // 0x67a8b8: LeaveFrame
    //     0x67a8b8: mov             SP, fp
    //     0x67a8bc: ldp             fp, lr, [SP], #0x10
    // 0x67a8c0: ret
    //     0x67a8c0: ret             
    // 0x67a8c4: r0 = StackOverflowSharedWithFPURegs()
    //     0x67a8c4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x67a8c8: b               #0x67a85c
    // 0x67a8cc: r0 = NullCastErrorSharedWithFPURegs()
    //     0x67a8cc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ localJumpTo(/* No info */) {
    // ** addr: 0x7a7568, size: 0xa4
    // 0x7a7568: EnterFrame
    //     0x7a7568: stp             fp, lr, [SP, #-0x10]!
    //     0x7a756c: mov             fp, SP
    // 0x7a7570: AllocStack(0x10)
    //     0x7a7570: sub             SP, SP, #0x10
    // 0x7a7574: SetupParameters(_NestedScrollPosition this /* r1 => r0, fp-0x8 */)
    //     0x7a7574: mov             x0, x1
    //     0x7a7578: stur            x1, [fp, #-8]
    // 0x7a757c: CheckStackOverflow
    //     0x7a757c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a7580: cmp             SP, x16
    //     0x7a7584: b.ls            #0x7a75fc
    // 0x7a7588: LoadField: r1 = r0->field_3f
    //     0x7a7588: ldur            w1, [x0, #0x3f]
    // 0x7a758c: DecompressPointer r1
    //     0x7a758c: add             x1, x1, HEAP, lsl #32
    // 0x7a7590: cmp             w1, NULL
    // 0x7a7594: b.eq            #0x7a7604
    // 0x7a7598: LoadField: d1 = r1->field_7
    //     0x7a7598: ldur            d1, [x1, #7]
    // 0x7a759c: stur            d1, [fp, #-0x10]
    // 0x7a75a0: fcmp            d1, d0
    // 0x7a75a4: b.eq            #0x7a75ec
    // 0x7a75a8: mov             x1, x0
    // 0x7a75ac: r0 = forcePixels()
    //     0x7a75ac: bl              #0x7a6d40  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::forcePixels
    // 0x7a75b0: ldur            x1, [fp, #-8]
    // 0x7a75b4: r0 = didStartScroll()
    //     0x7a75b4: bl              #0x679798  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didStartScroll
    // 0x7a75b8: ldur            x0, [fp, #-8]
    // 0x7a75bc: LoadField: r1 = r0->field_3f
    //     0x7a75bc: ldur            w1, [x0, #0x3f]
    // 0x7a75c0: DecompressPointer r1
    //     0x7a75c0: add             x1, x1, HEAP, lsl #32
    // 0x7a75c4: cmp             w1, NULL
    // 0x7a75c8: b.eq            #0x7a7608
    // 0x7a75cc: LoadField: d0 = r1->field_7
    //     0x7a75cc: ldur            d0, [x1, #7]
    // 0x7a75d0: ldur            d1, [fp, #-0x10]
    // 0x7a75d4: fsub            d2, d0, d1
    // 0x7a75d8: mov             x1, x0
    // 0x7a75dc: mov             v0.16b, v2.16b
    // 0x7a75e0: r0 = didUpdateScrollPositionBy()
    //     0x7a75e0: bl              #0x7a6c78  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didUpdateScrollPositionBy
    // 0x7a75e4: ldur            x1, [fp, #-8]
    // 0x7a75e8: r0 = didEndScroll()
    //     0x7a75e8: bl              #0x679988  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didEndScroll
    // 0x7a75ec: r0 = Null
    //     0x7a75ec: mov             x0, NULL
    // 0x7a75f0: LeaveFrame
    //     0x7a75f0: mov             SP, fp
    //     0x7a75f4: ldp             fp, lr, [SP], #0x10
    // 0x7a75f8: ret
    //     0x7a75f8: ret             
    // 0x7a75fc: r0 = StackOverflowSharedWithFPURegs()
    //     0x7a75fc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7a7600: b               #0x7a7588
    // 0x7a7604: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7a7604: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x7a7608: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7a7608: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _NestedScrollPosition(/* No info */) {
    // ** addr: 0x9992d4, size: 0xa8
    // 0x9992d4: EnterFrame
    //     0x9992d4: stp             fp, lr, [SP, #-0x10]!
    //     0x9992d8: mov             fp, SP
    // 0x9992dc: AllocStack(0x8)
    //     0x9992dc: sub             SP, SP, #8
    // 0x9992e0: SetupParameters(_NestedScrollPosition this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r0 */, dynamic _ /* r5 => r3 */, dynamic _ /* r6 => r5 */)
    //     0x9992e0: mov             x4, x1
    //     0x9992e4: mov             x0, x3
    //     0x9992e8: mov             x3, x5
    //     0x9992ec: mov             x5, x6
    //     0x9992f0: stur            x1, [fp, #-8]
    // 0x9992f4: CheckStackOverflow
    //     0x9992f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9992f8: cmp             SP, x16
    //     0x9992fc: b.ls            #0x999374
    // 0x999300: StoreField: r4->field_6b = r0
    //     0x999300: stur            w0, [x4, #0x6b]
    //     0x999304: ldurb           w16, [x4, #-1]
    //     0x999308: ldurb           w17, [x0, #-1]
    //     0x99930c: and             x16, x17, x16, lsr #2
    //     0x999310: tst             x16, HEAP, lsr #32
    //     0x999314: b.eq            #0x99931c
    //     0x999318: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x99931c: mov             x1, x4
    // 0x999320: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x999320: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x999324: r0 = ScrollPosition()
    //     0x999324: bl              #0x99937c  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::ScrollPosition
    // 0x999328: ldur            x0, [fp, #-8]
    // 0x99932c: LoadField: r1 = r0->field_3f
    //     0x99932c: ldur            w1, [x0, #0x3f]
    // 0x999330: DecompressPointer r1
    //     0x999330: add             x1, x1, HEAP, lsl #32
    // 0x999334: cmp             w1, NULL
    // 0x999338: b.ne            #0x999344
    // 0x99933c: r1 = 0.000000
    //     0x99933c: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x999340: StoreField: r0->field_3f = r1
    //     0x999340: stur            w1, [x0, #0x3f]
    // 0x999344: LoadField: r1 = r0->field_67
    //     0x999344: ldur            w1, [x0, #0x67]
    // 0x999348: DecompressPointer r1
    //     0x999348: add             x1, x1, HEAP, lsl #32
    // 0x99934c: cmp             w1, NULL
    // 0x999350: b.ne            #0x99935c
    // 0x999354: mov             x1, x0
    // 0x999358: r0 = goIdle()
    //     0x999358: bl              #0xd839b8  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::goIdle
    // 0x99935c: ldur            x1, [fp, #-8]
    // 0x999360: r0 = saveScrollOffset()
    //     0x999360: bl              #0xc4553c  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::saveScrollOffset
    // 0x999364: r0 = Null
    //     0x999364: mov             x0, NULL
    // 0x999368: LeaveFrame
    //     0x999368: mov             SP, fp
    //     0x99936c: ldp             fp, lr, [SP], #0x10
    // 0x999370: ret
    //     0x999370: ret             
    // 0x999374: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x999374: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x999378: b               #0x999300
  }
  _ updateCanDrag(/* No info */) {
    // ** addr: 0x99a134, size: 0x7c
    // 0x99a134: EnterFrame
    //     0x99a134: stp             fp, lr, [SP, #-0x10]!
    //     0x99a138: mov             fp, SP
    // 0x99a13c: AllocStack(0x10)
    //     0x99a13c: sub             SP, SP, #0x10
    // 0x99a140: SetupParameters(_NestedScrollPosition this /* r1 => r2 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x99a140: mov             x3, x2
    //     0x99a144: stur            x2, [fp, #-0x10]
    //     0x99a148: mov             x2, x1
    // 0x99a14c: CheckStackOverflow
    //     0x99a14c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99a150: cmp             SP, x16
    //     0x99a154: b.ls            #0x99a1a8
    // 0x99a158: LoadField: r4 = r2->field_27
    //     0x99a158: ldur            w4, [x2, #0x27]
    // 0x99a15c: DecompressPointer r4
    //     0x99a15c: add             x4, x4, HEAP, lsl #32
    // 0x99a160: stur            x4, [fp, #-8]
    // 0x99a164: LoadField: r1 = r2->field_23
    //     0x99a164: ldur            w1, [x2, #0x23]
    // 0x99a168: DecompressPointer r1
    //     0x99a168: add             x1, x1, HEAP, lsl #32
    // 0x99a16c: r0 = LoadClassIdInstr(r1)
    //     0x99a16c: ldur            x0, [x1, #-1]
    //     0x99a170: ubfx            x0, x0, #0xc, #0x14
    // 0x99a174: r0 = GDT[cid_x0 + -0xffd]()
    //     0x99a174: sub             lr, x0, #0xffd
    //     0x99a178: ldr             lr, [x21, lr, lsl #3]
    //     0x99a17c: blr             lr
    // 0x99a180: tbnz            w0, #4, #0x99a18c
    // 0x99a184: r2 = true
    //     0x99a184: add             x2, NULL, #0x20  ; true
    // 0x99a188: b               #0x99a190
    // 0x99a18c: ldur            x2, [fp, #-0x10]
    // 0x99a190: ldur            x1, [fp, #-8]
    // 0x99a194: r0 = setCanDrag()
    //     0x99a194: bl              #0x99a1b0  ; [package:flutter/src/widgets/scrollable.dart] ScrollableState::setCanDrag
    // 0x99a198: r0 = Null
    //     0x99a198: mov             x0, NULL
    // 0x99a19c: LeaveFrame
    //     0x99a19c: mov             SP, fp
    //     0x99a1a0: ldp             fp, lr, [SP], #0x10
    // 0x99a1a4: ret
    //     0x99a1a4: ret             
    // 0x99a1a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99a1a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99a1ac: b               #0x99a158
  }
  _ setParent(/* No info */) {
    // ** addr: 0x99b758, size: 0x2bc
    // 0x99b758: EnterFrame
    //     0x99b758: stp             fp, lr, [SP, #-0x10]!
    //     0x99b75c: mov             fp, SP
    // 0x99b760: AllocStack(0x20)
    //     0x99b760: sub             SP, SP, #0x20
    // 0x99b764: SetupParameters(_NestedScrollPosition this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x99b764: mov             x3, x1
    //     0x99b768: mov             x0, x2
    //     0x99b76c: stur            x1, [fp, #-0x10]
    //     0x99b770: stur            x2, [fp, #-0x18]
    // 0x99b774: CheckStackOverflow
    //     0x99b774: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99b778: cmp             SP, x16
    //     0x99b77c: b.ls            #0x99ba0c
    // 0x99b780: LoadField: r4 = r3->field_6f
    //     0x99b780: ldur            w4, [x3, #0x6f]
    // 0x99b784: DecompressPointer r4
    //     0x99b784: add             x4, x4, HEAP, lsl #32
    // 0x99b788: stur            x4, [fp, #-8]
    // 0x99b78c: cmp             w4, NULL
    // 0x99b790: b.ne            #0x99b7a0
    // 0x99b794: mov             x4, x3
    // 0x99b798: mov             x3, x0
    // 0x99b79c: b               #0x99b83c
    // 0x99b7a0: r1 = LoadClassIdInstr(r4)
    //     0x99b7a0: ldur            x1, [x4, #-1]
    //     0x99b7a4: ubfx            x1, x1, #0xc, #0x14
    // 0x99b7a8: sub             x16, x1, #0xe3f
    // 0x99b7ac: cmp             x16, #1
    // 0x99b7b0: b.hi            #0x99b7bc
    // 0x99b7b4: mov             x0, x4
    // 0x99b7b8: b               #0x99b808
    // 0x99b7bc: cmp             x1, #0xe41
    // 0x99b7c0: b.ne            #0x99b804
    // 0x99b7c4: mov             x1, x3
    // 0x99b7c8: r2 = Null
    //     0x99b7c8: mov             x2, NULL
    // 0x99b7cc: r0 = setParent()
    //     0x99b7cc: bl              #0x99b758  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::setParent
    // 0x99b7d0: ldur            x2, [fp, #-8]
    // 0x99b7d4: r1 = Function '_scheduleUpdateShadow@303016527':.
    //     0x99b7d4: add             x1, PP, #0x45, lsl #12  ; [pp+0x45fa8] AnonymousClosure: (0x99beac), in [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollController::_scheduleUpdateShadow (0x99b59c)
    //     0x99b7d8: ldr             x1, [x1, #0xfa8]
    // 0x99b7dc: r0 = AllocateClosure()
    //     0x99b7dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x99b7e0: ldur            x1, [fp, #-0x10]
    // 0x99b7e4: mov             x2, x0
    // 0x99b7e8: r0 = removeListener()
    //     0x99b7e8: bl              #0xa8a5dc  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x99b7ec: ldur            x1, [fp, #-8]
    // 0x99b7f0: ldur            x2, [fp, #-0x10]
    // 0x99b7f4: r0 = detach()
    //     0x99b7f4: bl              #0xd8944c  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::detach
    // 0x99b7f8: ldur            x1, [fp, #-8]
    // 0x99b7fc: r0 = _scheduleUpdateShadow()
    //     0x99b7fc: bl              #0x99b59c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollController::_scheduleUpdateShadow
    // 0x99b800: b               #0x99b834
    // 0x99b804: ldur            x0, [fp, #-8]
    // 0x99b808: mov             x2, x0
    // 0x99b80c: r1 = Function 'notifyListeners':.
    //     0x99b80c: ldr             x1, [PP, #0x2608]  ; [pp+0x2608] AnonymousClosure: (0x646848), in [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners (0x6462b8)
    // 0x99b810: r0 = AllocateClosure()
    //     0x99b810: bl              #0xec1630  ; AllocateClosureStub
    // 0x99b814: ldur            x1, [fp, #-0x10]
    // 0x99b818: mov             x2, x0
    // 0x99b81c: r0 = removeListener()
    //     0x99b81c: bl              #0xa8a5dc  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x99b820: ldur            x0, [fp, #-8]
    // 0x99b824: LoadField: r1 = r0->field_3b
    //     0x99b824: ldur            w1, [x0, #0x3b]
    // 0x99b828: DecompressPointer r1
    //     0x99b828: add             x1, x1, HEAP, lsl #32
    // 0x99b82c: ldur            x2, [fp, #-0x10]
    // 0x99b830: r0 = remove()
    //     0x99b830: bl              #0x6ec83c  ; [dart:core] _GrowableList::remove
    // 0x99b834: ldur            x4, [fp, #-0x10]
    // 0x99b838: ldur            x3, [fp, #-0x18]
    // 0x99b83c: mov             x0, x3
    // 0x99b840: StoreField: r4->field_6f = r0
    //     0x99b840: stur            w0, [x4, #0x6f]
    //     0x99b844: ldurb           w16, [x4, #-1]
    //     0x99b848: ldurb           w17, [x0, #-1]
    //     0x99b84c: and             x16, x17, x16, lsr #2
    //     0x99b850: tst             x16, HEAP, lsr #32
    //     0x99b854: b.eq            #0x99b85c
    //     0x99b858: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x99b85c: cmp             w3, NULL
    // 0x99b860: b.eq            #0x99b9fc
    // 0x99b864: r0 = LoadClassIdInstr(r3)
    //     0x99b864: ldur            x0, [x3, #-1]
    //     0x99b868: ubfx            x0, x0, #0xc, #0x14
    // 0x99b86c: cmp             x0, #0xe3f
    // 0x99b870: b.eq            #0x99b92c
    // 0x99b874: cmp             x0, #0xe40
    // 0x99b878: b.ne            #0x99b8cc
    // 0x99b87c: mov             x1, x3
    // 0x99b880: mov             x2, x4
    // 0x99b884: r0 = attach()
    //     0x99b884: bl              #0xd85040  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::attach
    // 0x99b888: ldur            x0, [fp, #-0x10]
    // 0x99b88c: r2 = Null
    //     0x99b88c: mov             x2, NULL
    // 0x99b890: r1 = Null
    //     0x99b890: mov             x1, NULL
    // 0x99b894: r4 = LoadClassIdInstr(r0)
    //     0x99b894: ldur            x4, [x0, #-1]
    //     0x99b898: ubfx            x4, x4, #0xc, #0x14
    // 0x99b89c: cmp             x4, #0xe48
    // 0x99b8a0: b.eq            #0x99b8b8
    // 0x99b8a4: r8 = _PagePosition
    //     0x99b8a4: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c148] Type: _PagePosition
    //     0x99b8a8: ldr             x8, [x8, #0x148]
    // 0x99b8ac: r3 = Null
    //     0x99b8ac: add             x3, PP, #0x45, lsl #12  ; [pp+0x45fb0] Null
    //     0x99b8b0: ldr             x3, [x3, #0xfb0]
    // 0x99b8b4: r0 = DefaultTypeTest()
    //     0x99b8b4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x99b8b8: ldur            x3, [fp, #-0x18]
    // 0x99b8bc: LoadField: d0 = r3->field_4b
    //     0x99b8bc: ldur            d0, [x3, #0x4b]
    // 0x99b8c0: ldur            x1, [fp, #-0x10]
    // 0x99b8c4: r0 = viewportFraction=()
    //     0x99b8c4: bl              #0x99b510  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::viewportFraction=
    // 0x99b8c8: b               #0x99b9fc
    // 0x99b8cc: cmp             x0, #0xe41
    // 0x99b8d0: b.ne            #0x99b928
    // 0x99b8d4: mov             x1, x3
    // 0x99b8d8: ldur            x2, [fp, #-0x10]
    // 0x99b8dc: r0 = attach()
    //     0x99b8dc: bl              #0xd85040  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::attach
    // 0x99b8e0: ldur            x2, [fp, #-0x18]
    // 0x99b8e4: LoadField: r0 = r2->field_3f
    //     0x99b8e4: ldur            w0, [x2, #0x3f]
    // 0x99b8e8: DecompressPointer r0
    //     0x99b8e8: add             x0, x0, HEAP, lsl #32
    // 0x99b8ec: mov             x1, x0
    // 0x99b8f0: stur            x0, [fp, #-8]
    // 0x99b8f4: r0 = updateParent()
    //     0x99b8f4: bl              #0x99b494  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::updateParent
    // 0x99b8f8: ldur            x1, [fp, #-8]
    // 0x99b8fc: r0 = updateCanDrag()
    //     0x99b8fc: bl              #0x999eac  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::updateCanDrag
    // 0x99b900: ldur            x2, [fp, #-0x18]
    // 0x99b904: r1 = Function '_scheduleUpdateShadow@303016527':.
    //     0x99b904: add             x1, PP, #0x45, lsl #12  ; [pp+0x45fa8] AnonymousClosure: (0x99beac), in [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollController::_scheduleUpdateShadow (0x99b59c)
    //     0x99b908: ldr             x1, [x1, #0xfa8]
    // 0x99b90c: r0 = AllocateClosure()
    //     0x99b90c: bl              #0xec1630  ; AllocateClosureStub
    // 0x99b910: ldur            x1, [fp, #-0x10]
    // 0x99b914: mov             x2, x0
    // 0x99b918: r0 = addListener()
    //     0x99b918: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x99b91c: ldur            x1, [fp, #-0x18]
    // 0x99b920: r0 = _scheduleUpdateShadow()
    //     0x99b920: bl              #0x99b59c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollController::_scheduleUpdateShadow
    // 0x99b924: b               #0x99b9fc
    // 0x99b928: ldur            x3, [fp, #-0x18]
    // 0x99b92c: LoadField: r4 = r3->field_3b
    //     0x99b92c: ldur            w4, [x3, #0x3b]
    // 0x99b930: DecompressPointer r4
    //     0x99b930: add             x4, x4, HEAP, lsl #32
    // 0x99b934: stur            x4, [fp, #-8]
    // 0x99b938: LoadField: r2 = r4->field_7
    //     0x99b938: ldur            w2, [x4, #7]
    // 0x99b93c: DecompressPointer r2
    //     0x99b93c: add             x2, x2, HEAP, lsl #32
    // 0x99b940: ldur            x0, [fp, #-0x10]
    // 0x99b944: r1 = Null
    //     0x99b944: mov             x1, NULL
    // 0x99b948: cmp             w2, NULL
    // 0x99b94c: b.eq            #0x99b96c
    // 0x99b950: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x99b950: ldur            w4, [x2, #0x17]
    // 0x99b954: DecompressPointer r4
    //     0x99b954: add             x4, x4, HEAP, lsl #32
    // 0x99b958: r8 = X0
    //     0x99b958: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x99b95c: LoadField: r9 = r4->field_7
    //     0x99b95c: ldur            x9, [x4, #7]
    // 0x99b960: r3 = Null
    //     0x99b960: add             x3, PP, #0x45, lsl #12  ; [pp+0x45fc0] Null
    //     0x99b964: ldr             x3, [x3, #0xfc0]
    // 0x99b968: blr             x9
    // 0x99b96c: ldur            x0, [fp, #-8]
    // 0x99b970: LoadField: r1 = r0->field_b
    //     0x99b970: ldur            w1, [x0, #0xb]
    // 0x99b974: LoadField: r2 = r0->field_f
    //     0x99b974: ldur            w2, [x0, #0xf]
    // 0x99b978: DecompressPointer r2
    //     0x99b978: add             x2, x2, HEAP, lsl #32
    // 0x99b97c: LoadField: r3 = r2->field_b
    //     0x99b97c: ldur            w3, [x2, #0xb]
    // 0x99b980: r2 = LoadInt32Instr(r1)
    //     0x99b980: sbfx            x2, x1, #1, #0x1f
    // 0x99b984: stur            x2, [fp, #-0x20]
    // 0x99b988: r1 = LoadInt32Instr(r3)
    //     0x99b988: sbfx            x1, x3, #1, #0x1f
    // 0x99b98c: cmp             x2, x1
    // 0x99b990: b.ne            #0x99b99c
    // 0x99b994: mov             x1, x0
    // 0x99b998: r0 = _growToNextCapacity()
    //     0x99b998: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x99b99c: ldur            x0, [fp, #-8]
    // 0x99b9a0: ldur            x2, [fp, #-0x20]
    // 0x99b9a4: add             x1, x2, #1
    // 0x99b9a8: lsl             x3, x1, #1
    // 0x99b9ac: StoreField: r0->field_b = r3
    //     0x99b9ac: stur            w3, [x0, #0xb]
    // 0x99b9b0: LoadField: r1 = r0->field_f
    //     0x99b9b0: ldur            w1, [x0, #0xf]
    // 0x99b9b4: DecompressPointer r1
    //     0x99b9b4: add             x1, x1, HEAP, lsl #32
    // 0x99b9b8: ldur            x0, [fp, #-0x10]
    // 0x99b9bc: ArrayStore: r1[r2] = r0  ; List_4
    //     0x99b9bc: add             x25, x1, x2, lsl #2
    //     0x99b9c0: add             x25, x25, #0xf
    //     0x99b9c4: str             w0, [x25]
    //     0x99b9c8: tbz             w0, #0, #0x99b9e4
    //     0x99b9cc: ldurb           w16, [x1, #-1]
    //     0x99b9d0: ldurb           w17, [x0, #-1]
    //     0x99b9d4: and             x16, x17, x16, lsr #2
    //     0x99b9d8: tst             x16, HEAP, lsr #32
    //     0x99b9dc: b.eq            #0x99b9e4
    //     0x99b9e0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x99b9e4: ldur            x2, [fp, #-0x18]
    // 0x99b9e8: r1 = Function 'notifyListeners':.
    //     0x99b9e8: ldr             x1, [PP, #0x2608]  ; [pp+0x2608] AnonymousClosure: (0x646848), in [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::notifyListeners (0x6462b8)
    // 0x99b9ec: r0 = AllocateClosure()
    //     0x99b9ec: bl              #0xec1630  ; AllocateClosureStub
    // 0x99b9f0: ldur            x1, [fp, #-0x10]
    // 0x99b9f4: mov             x2, x0
    // 0x99b9f8: r0 = addListener()
    //     0x99b9f8: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x99b9fc: r0 = Null
    //     0x99b9fc: mov             x0, NULL
    // 0x99ba00: LeaveFrame
    //     0x99ba00: mov             SP, fp
    //     0x99ba04: ldp             fp, lr, [SP], #0x10
    // 0x99ba08: ret
    //     0x99ba08: ret             
    // 0x99ba0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99ba0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99ba10: b               #0x99b780
  }
  _ applyClampedPointerSignalUpdate(/* No info */) {
    // ** addr: 0x9defe0, size: 0x208
    // 0x9defe0: EnterFrame
    //     0x9defe0: stp             fp, lr, [SP, #-0x10]!
    //     0x9defe4: mov             fp, SP
    // 0x9defe8: AllocStack(0x18)
    //     0x9defe8: sub             SP, SP, #0x18
    // 0x9defec: d1 = 0.000000
    //     0x9defec: eor             v1.16b, v1.16b, v1.16b
    // 0x9deff0: mov             x0, x1
    // 0x9deff4: mov             v2.16b, v0.16b
    // 0x9deff8: stur            x1, [fp, #-8]
    // 0x9deffc: stur            d0, [fp, #-0x18]
    // 0x9df000: CheckStackOverflow
    //     0x9df000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9df004: cmp             SP, x16
    //     0x9df008: b.ls            #0x9df1cc
    // 0x9df00c: fcmp            d2, d1
    // 0x9df010: b.le            #0x9df01c
    // 0x9df014: d0 = -inf
    //     0x9df014: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0x9df018: b               #0x9df0b8
    // 0x9df01c: LoadField: r1 = r0->field_2f
    //     0x9df01c: ldur            w1, [x0, #0x2f]
    // 0x9df020: DecompressPointer r1
    //     0x9df020: add             x1, x1, HEAP, lsl #32
    // 0x9df024: cmp             w1, NULL
    // 0x9df028: b.eq            #0x9df1d4
    // 0x9df02c: LoadField: r2 = r0->field_3f
    //     0x9df02c: ldur            w2, [x0, #0x3f]
    // 0x9df030: DecompressPointer r2
    //     0x9df030: add             x2, x2, HEAP, lsl #32
    // 0x9df034: cmp             w2, NULL
    // 0x9df038: b.eq            #0x9df1d8
    // 0x9df03c: LoadField: d0 = r1->field_7
    //     0x9df03c: ldur            d0, [x1, #7]
    // 0x9df040: LoadField: d3 = r2->field_7
    //     0x9df040: ldur            d3, [x2, #7]
    // 0x9df044: fcmp            d0, d3
    // 0x9df048: b.le            #0x9df054
    // 0x9df04c: LoadField: d0 = r2->field_7
    //     0x9df04c: ldur            d0, [x2, #7]
    // 0x9df050: b               #0x9df0b8
    // 0x9df054: fcmp            d3, d0
    // 0x9df058: b.le            #0x9df064
    // 0x9df05c: LoadField: d0 = r1->field_7
    //     0x9df05c: ldur            d0, [x1, #7]
    // 0x9df060: b               #0x9df0b8
    // 0x9df064: fcmp            d0, d1
    // 0x9df068: b.ne            #0x9df07c
    // 0x9df06c: fadd            d4, d0, d3
    // 0x9df070: fmul            d5, d4, d0
    // 0x9df074: fmul            d0, d5, d3
    // 0x9df078: b               #0x9df0b8
    // 0x9df07c: fcmp            d0, d1
    // 0x9df080: b.ne            #0x9df0a0
    // 0x9df084: LoadField: d0 = r2->field_7
    //     0x9df084: ldur            d0, [x2, #7]
    // 0x9df088: fcmp            d0, #0.0
    // 0x9df08c: b.vs            #0x9df0a0
    // 0x9df090: b.ne            #0x9df09c
    // 0x9df094: r3 = 0.000000
    //     0x9df094: fmov            x3, d0
    // 0x9df098: cmp             x3, #0
    // 0x9df09c: b.lt            #0x9df0ac
    // 0x9df0a0: LoadField: d0 = r2->field_7
    //     0x9df0a0: ldur            d0, [x2, #7]
    // 0x9df0a4: fcmp            d0, d0
    // 0x9df0a8: b.vc            #0x9df0b4
    // 0x9df0ac: LoadField: d0 = r2->field_7
    //     0x9df0ac: ldur            d0, [x2, #7]
    // 0x9df0b0: b               #0x9df0b8
    // 0x9df0b4: LoadField: d0 = r1->field_7
    //     0x9df0b4: ldur            d0, [x1, #7]
    // 0x9df0b8: fcmp            d1, d2
    // 0x9df0bc: b.le            #0x9df0c8
    // 0x9df0c0: d3 = inf
    //     0x9df0c0: ldr             d3, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x9df0c4: b               #0x9df13c
    // 0x9df0c8: LoadField: r1 = r0->field_33
    //     0x9df0c8: ldur            w1, [x0, #0x33]
    // 0x9df0cc: DecompressPointer r1
    //     0x9df0cc: add             x1, x1, HEAP, lsl #32
    // 0x9df0d0: cmp             w1, NULL
    // 0x9df0d4: b.eq            #0x9df1dc
    // 0x9df0d8: LoadField: r2 = r0->field_3f
    //     0x9df0d8: ldur            w2, [x0, #0x3f]
    // 0x9df0dc: DecompressPointer r2
    //     0x9df0dc: add             x2, x2, HEAP, lsl #32
    // 0x9df0e0: cmp             w2, NULL
    // 0x9df0e4: b.eq            #0x9df1e0
    // 0x9df0e8: LoadField: d3 = r1->field_7
    //     0x9df0e8: ldur            d3, [x1, #7]
    // 0x9df0ec: LoadField: d4 = r2->field_7
    //     0x9df0ec: ldur            d4, [x2, #7]
    // 0x9df0f0: fcmp            d3, d4
    // 0x9df0f4: b.le            #0x9df100
    // 0x9df0f8: LoadField: d3 = r1->field_7
    //     0x9df0f8: ldur            d3, [x1, #7]
    // 0x9df0fc: b               #0x9df13c
    // 0x9df100: fcmp            d4, d3
    // 0x9df104: b.le            #0x9df110
    // 0x9df108: LoadField: d3 = r2->field_7
    //     0x9df108: ldur            d3, [x2, #7]
    // 0x9df10c: b               #0x9df13c
    // 0x9df110: fcmp            d3, d1
    // 0x9df114: b.ne            #0x9df124
    // 0x9df118: fadd            d5, d3, d4
    // 0x9df11c: mov             v3.16b, v5.16b
    // 0x9df120: b               #0x9df13c
    // 0x9df124: LoadField: d3 = r2->field_7
    //     0x9df124: ldur            d3, [x2, #7]
    // 0x9df128: fcmp            d3, d3
    // 0x9df12c: b.vc            #0x9df138
    // 0x9df130: LoadField: d3 = r2->field_7
    //     0x9df130: ldur            d3, [x2, #7]
    // 0x9df134: b               #0x9df13c
    // 0x9df138: LoadField: d3 = r1->field_7
    //     0x9df138: ldur            d3, [x1, #7]
    // 0x9df13c: LoadField: r1 = r0->field_3f
    //     0x9df13c: ldur            w1, [x0, #0x3f]
    // 0x9df140: DecompressPointer r1
    //     0x9df140: add             x1, x1, HEAP, lsl #32
    // 0x9df144: cmp             w1, NULL
    // 0x9df148: b.eq            #0x9df1e4
    // 0x9df14c: LoadField: d4 = r1->field_7
    //     0x9df14c: ldur            d4, [x1, #7]
    // 0x9df150: fadd            d5, d4, d2
    // 0x9df154: fcmp            d0, d5
    // 0x9df158: b.gt            #0x9df180
    // 0x9df15c: fcmp            d5, d3
    // 0x9df160: b.le            #0x9df16c
    // 0x9df164: mov             v0.16b, v3.16b
    // 0x9df168: b               #0x9df180
    // 0x9df16c: fcmp            d5, d5
    // 0x9df170: b.vc            #0x9df17c
    // 0x9df174: mov             v0.16b, v3.16b
    // 0x9df178: b               #0x9df180
    // 0x9df17c: mov             v0.16b, v5.16b
    // 0x9df180: fsub            d3, d0, d4
    // 0x9df184: stur            d3, [fp, #-0x10]
    // 0x9df188: fcmp            d3, d1
    // 0x9df18c: b.ne            #0x9df1a0
    // 0x9df190: mov             v0.16b, v2.16b
    // 0x9df194: LeaveFrame
    //     0x9df194: mov             SP, fp
    //     0x9df198: ldp             fp, lr, [SP], #0x10
    // 0x9df19c: ret
    //     0x9df19c: ret             
    // 0x9df1a0: mov             x1, x0
    // 0x9df1a4: r0 = forcePixels()
    //     0x9df1a4: bl              #0x7a6d40  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::forcePixels
    // 0x9df1a8: ldur            x1, [fp, #-8]
    // 0x9df1ac: ldur            d0, [fp, #-0x10]
    // 0x9df1b0: r0 = didUpdateScrollPositionBy()
    //     0x9df1b0: bl              #0x7a6c78  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didUpdateScrollPositionBy
    // 0x9df1b4: ldur            d1, [fp, #-0x18]
    // 0x9df1b8: ldur            d2, [fp, #-0x10]
    // 0x9df1bc: fsub            d0, d1, d2
    // 0x9df1c0: LeaveFrame
    //     0x9df1c0: mov             SP, fp
    //     0x9df1c4: ldp             fp, lr, [SP], #0x10
    // 0x9df1c8: ret
    //     0x9df1c8: ret             
    // 0x9df1cc: r0 = StackOverflowSharedWithFPURegs()
    //     0x9df1cc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x9df1d0: b               #0x9df00c
    // 0x9df1d4: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9df1d4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9df1d8: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9df1d8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9df1dc: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9df1dc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9df1e0: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9df1e0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9df1e4: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9df1e4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ createBallisticScrollActivity(/* No info */) {
    // ** addr: 0xbf954c, size: 0x208
    // 0xbf954c: EnterFrame
    //     0xbf954c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf9550: mov             fp, SP
    // 0xbf9554: AllocStack(0x38)
    //     0xbf9554: sub             SP, SP, #0x38
    // 0xbf9558: SetupParameters(_NestedScrollPosition this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x28 */, {dynamic metrics = Null /* r2, fp-0x20 */})
    //     0xbf9558: mov             x0, x2
    //     0xbf955c: stur            x1, [fp, #-8]
    //     0xbf9560: stur            x2, [fp, #-0x28]
    //     0xbf9564: ldur            w2, [x4, #0x13]
    //     0xbf9568: ldur            w5, [x4, #0x1f]
    //     0xbf956c: add             x5, x5, HEAP, lsl #32
    //     0xbf9570: ldr             x16, [PP, #0x75c8]  ; [pp+0x75c8] "metrics"
    //     0xbf9574: cmp             w5, w16
    //     0xbf9578: b.ne            #0xbf9594
    //     0xbf957c: ldur            w5, [x4, #0x23]
    //     0xbf9580: add             x5, x5, HEAP, lsl #32
    //     0xbf9584: sub             w4, w2, w5
    //     0xbf9588: add             x2, fp, w4, sxtw #2
    //     0xbf958c: ldr             x2, [x2, #8]
    //     0xbf9590: b               #0xbf9598
    //     0xbf9594: mov             x2, NULL
    //     0xbf9598: stur            x2, [fp, #-0x20]
    // 0xbf959c: CheckStackOverflow
    //     0xbf959c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf95a0: cmp             SP, x16
    //     0xbf95a4: b.ls            #0xbf9748
    // 0xbf95a8: cmp             w0, NULL
    // 0xbf95ac: b.ne            #0xbf95d0
    // 0xbf95b0: r0 = IdleScrollActivity()
    //     0xbf95b0: bl              #0xbf9cac  ; AllocateIdleScrollActivityStub -> IdleScrollActivity (size=0x10)
    // 0xbf95b4: r1 = false
    //     0xbf95b4: add             x1, NULL, #0x30  ; false
    // 0xbf95b8: StoreField: r0->field_b = r1
    //     0xbf95b8: stur            w1, [x0, #0xb]
    // 0xbf95bc: ldur            x4, [fp, #-8]
    // 0xbf95c0: StoreField: r0->field_7 = r4
    //     0xbf95c0: stur            w4, [x0, #7]
    // 0xbf95c4: LeaveFrame
    //     0xbf95c4: mov             SP, fp
    //     0xbf95c8: ldp             fp, lr, [SP], #0x10
    // 0xbf95cc: ret
    //     0xbf95cc: ret             
    // 0xbf95d0: mov             x4, x1
    // 0xbf95d4: r1 = false
    //     0xbf95d4: add             x1, NULL, #0x30  ; false
    // 0xbf95d8: LoadField: r5 = r3->field_7
    //     0xbf95d8: ldur            x5, [x3, #7]
    // 0xbf95dc: cmp             x5, #1
    // 0xbf95e0: b.gt            #0xbf96fc
    // 0xbf95e4: cmp             x5, #0
    // 0xbf95e8: b.gt            #0xbf9698
    // 0xbf95ec: cmp             w2, NULL
    // 0xbf95f0: b.eq            #0xbf9750
    // 0xbf95f4: LoadField: d0 = r2->field_23
    //     0xbf95f4: ldur            d0, [x2, #0x23]
    // 0xbf95f8: LoadField: d1 = r2->field_2b
    //     0xbf95f8: ldur            d1, [x2, #0x2b]
    // 0xbf95fc: fcmp            d0, d1
    // 0xbf9600: b.ne            #0xbf962c
    // 0xbf9604: r0 = IdleScrollActivity()
    //     0xbf9604: bl              #0xbf9cac  ; AllocateIdleScrollActivityStub -> IdleScrollActivity (size=0x10)
    // 0xbf9608: mov             x1, x0
    // 0xbf960c: r0 = false
    //     0xbf960c: add             x0, NULL, #0x30  ; false
    // 0xbf9610: StoreField: r1->field_b = r0
    //     0xbf9610: stur            w0, [x1, #0xb]
    // 0xbf9614: ldur            x3, [fp, #-8]
    // 0xbf9618: StoreField: r1->field_7 = r3
    //     0xbf9618: stur            w3, [x1, #7]
    // 0xbf961c: mov             x0, x1
    // 0xbf9620: LeaveFrame
    //     0xbf9620: mov             SP, fp
    //     0xbf9624: ldp             fp, lr, [SP], #0x10
    // 0xbf9628: ret
    //     0xbf9628: ret             
    // 0xbf962c: mov             x3, x4
    // 0xbf9630: LoadField: r4 = r3->field_6b
    //     0xbf9630: ldur            w4, [x3, #0x6b]
    // 0xbf9634: DecompressPointer r4
    //     0xbf9634: add             x4, x4, HEAP, lsl #32
    // 0xbf9638: stur            x4, [fp, #-0x18]
    // 0xbf963c: LoadField: r5 = r3->field_27
    //     0xbf963c: ldur            w5, [x3, #0x27]
    // 0xbf9640: DecompressPointer r5
    //     0xbf9640: add             x5, x5, HEAP, lsl #32
    // 0xbf9644: mov             x1, x3
    // 0xbf9648: stur            x5, [fp, #-0x10]
    // 0xbf964c: r0 = shouldIgnorePointer()
    //     0xbf964c: bl              #0xbf9c20  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::shouldIgnorePointer
    // 0xbf9650: stur            x0, [fp, #-0x30]
    // 0xbf9654: r0 = _NestedOuterBallisticScrollActivity()
    //     0xbf9654: bl              #0xbf9c14  ; Allocate_NestedOuterBallisticScrollActivityStub -> _NestedOuterBallisticScrollActivity (size=0x20)
    // 0xbf9658: mov             x4, x0
    // 0xbf965c: ldur            x0, [fp, #-0x18]
    // 0xbf9660: stur            x4, [fp, #-0x38]
    // 0xbf9664: ArrayStore: r4[0] = r0  ; List_4
    //     0xbf9664: stur            w0, [x4, #0x17]
    // 0xbf9668: ldur            x0, [fp, #-0x20]
    // 0xbf966c: StoreField: r4->field_1b = r0
    //     0xbf966c: stur            w0, [x4, #0x1b]
    // 0xbf9670: mov             x1, x4
    // 0xbf9674: ldur            x2, [fp, #-8]
    // 0xbf9678: ldur            x3, [fp, #-0x28]
    // 0xbf967c: ldur            x5, [fp, #-0x10]
    // 0xbf9680: ldur            x6, [fp, #-0x30]
    // 0xbf9684: r0 = BallisticScrollActivity()
    //     0xbf9684: bl              #0xbf976c  ; [package:flutter/src/widgets/scroll_activity.dart] BallisticScrollActivity::BallisticScrollActivity
    // 0xbf9688: ldur            x0, [fp, #-0x38]
    // 0xbf968c: LeaveFrame
    //     0xbf968c: mov             SP, fp
    //     0xbf9690: ldp             fp, lr, [SP], #0x10
    // 0xbf9694: ret
    //     0xbf9694: ret             
    // 0xbf9698: mov             x0, x4
    // 0xbf969c: LoadField: r2 = r0->field_6b
    //     0xbf969c: ldur            w2, [x0, #0x6b]
    // 0xbf96a0: DecompressPointer r2
    //     0xbf96a0: add             x2, x2, HEAP, lsl #32
    // 0xbf96a4: stur            x2, [fp, #-0x18]
    // 0xbf96a8: LoadField: r5 = r0->field_27
    //     0xbf96a8: ldur            w5, [x0, #0x27]
    // 0xbf96ac: DecompressPointer r5
    //     0xbf96ac: add             x5, x5, HEAP, lsl #32
    // 0xbf96b0: mov             x1, x0
    // 0xbf96b4: stur            x5, [fp, #-0x10]
    // 0xbf96b8: r0 = shouldIgnorePointer()
    //     0xbf96b8: bl              #0xbf9c20  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::shouldIgnorePointer
    // 0xbf96bc: stur            x0, [fp, #-0x20]
    // 0xbf96c0: r0 = _NestedInnerBallisticScrollActivity()
    //     0xbf96c0: bl              #0xbf9760  ; Allocate_NestedInnerBallisticScrollActivityStub -> _NestedInnerBallisticScrollActivity (size=0x1c)
    // 0xbf96c4: mov             x4, x0
    // 0xbf96c8: ldur            x0, [fp, #-0x18]
    // 0xbf96cc: stur            x4, [fp, #-0x30]
    // 0xbf96d0: ArrayStore: r4[0] = r0  ; List_4
    //     0xbf96d0: stur            w0, [x4, #0x17]
    // 0xbf96d4: mov             x1, x4
    // 0xbf96d8: ldur            x2, [fp, #-8]
    // 0xbf96dc: ldur            x3, [fp, #-0x28]
    // 0xbf96e0: ldur            x5, [fp, #-0x10]
    // 0xbf96e4: ldur            x6, [fp, #-0x20]
    // 0xbf96e8: r0 = BallisticScrollActivity()
    //     0xbf96e8: bl              #0xbf976c  ; [package:flutter/src/widgets/scroll_activity.dart] BallisticScrollActivity::BallisticScrollActivity
    // 0xbf96ec: ldur            x0, [fp, #-0x30]
    // 0xbf96f0: LeaveFrame
    //     0xbf96f0: mov             SP, fp
    //     0xbf96f4: ldp             fp, lr, [SP], #0x10
    // 0xbf96f8: ret
    //     0xbf96f8: ret             
    // 0xbf96fc: mov             x0, x4
    // 0xbf9700: LoadField: r5 = r0->field_27
    //     0xbf9700: ldur            w5, [x0, #0x27]
    // 0xbf9704: DecompressPointer r5
    //     0xbf9704: add             x5, x5, HEAP, lsl #32
    // 0xbf9708: mov             x1, x0
    // 0xbf970c: stur            x5, [fp, #-0x10]
    // 0xbf9710: r0 = shouldIgnorePointer()
    //     0xbf9710: bl              #0xbf9c20  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::shouldIgnorePointer
    // 0xbf9714: stur            x0, [fp, #-0x18]
    // 0xbf9718: r0 = BallisticScrollActivity()
    //     0xbf9718: bl              #0xbf9754  ; AllocateBallisticScrollActivityStub -> BallisticScrollActivity (size=0x18)
    // 0xbf971c: mov             x1, x0
    // 0xbf9720: ldur            x2, [fp, #-8]
    // 0xbf9724: ldur            x3, [fp, #-0x28]
    // 0xbf9728: ldur            x5, [fp, #-0x10]
    // 0xbf972c: ldur            x6, [fp, #-0x18]
    // 0xbf9730: stur            x0, [fp, #-8]
    // 0xbf9734: r0 = BallisticScrollActivity()
    //     0xbf9734: bl              #0xbf976c  ; [package:flutter/src/widgets/scroll_activity.dart] BallisticScrollActivity::BallisticScrollActivity
    // 0xbf9738: ldur            x0, [fp, #-8]
    // 0xbf973c: LeaveFrame
    //     0xbf973c: mov             SP, fp
    //     0xbf9740: ldp             fp, lr, [SP], #0x10
    // 0xbf9744: ret
    //     0xbf9744: ret             
    // 0xbf9748: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf9748: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf974c: b               #0xbf95a8
    // 0xbf9750: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf9750: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ restoreScrollOffset(/* No info */) {
    // ** addr: 0xc32d90, size: 0x50
    // 0xc32d90: EnterFrame
    //     0xc32d90: stp             fp, lr, [SP, #-0x10]!
    //     0xc32d94: mov             fp, SP
    // 0xc32d98: AllocStack(0x8)
    //     0xc32d98: sub             SP, SP, #8
    // 0xc32d9c: SetupParameters(_NestedScrollPosition this /* r1 => r0, fp-0x8 */)
    //     0xc32d9c: mov             x0, x1
    //     0xc32da0: stur            x1, [fp, #-8]
    // 0xc32da4: CheckStackOverflow
    //     0xc32da4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc32da8: cmp             SP, x16
    //     0xc32dac: b.ls            #0xc32dd8
    // 0xc32db0: LoadField: r1 = r0->field_6b
    //     0xc32db0: ldur            w1, [x0, #0x6b]
    // 0xc32db4: DecompressPointer r1
    //     0xc32db4: add             x1, x1, HEAP, lsl #32
    // 0xc32db8: r0 = canScrollBody()
    //     0xc32db8: bl              #0x999644  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::canScrollBody
    // 0xc32dbc: tbnz            w0, #4, #0xc32dc8
    // 0xc32dc0: ldur            x1, [fp, #-8]
    // 0xc32dc4: r0 = restoreScrollOffset()
    //     0xc32dc4: bl              #0xc32c8c  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::restoreScrollOffset
    // 0xc32dc8: r0 = Null
    //     0xc32dc8: mov             x0, NULL
    // 0xc32dcc: LeaveFrame
    //     0xc32dcc: mov             SP, fp
    //     0xc32dd0: ldp             fp, lr, [SP], #0x10
    // 0xc32dd4: ret
    //     0xc32dd4: ret             
    // 0xc32dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc32dd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc32ddc: b               #0xc32db0
  }
  _ absorb(/* No info */) {
    // ** addr: 0xcfe398, size: 0x74
    // 0xcfe398: EnterFrame
    //     0xcfe398: stp             fp, lr, [SP, #-0x10]!
    //     0xcfe39c: mov             fp, SP
    // 0xcfe3a0: AllocStack(0x8)
    //     0xcfe3a0: sub             SP, SP, #8
    // 0xcfe3a4: SetupParameters(_NestedScrollPosition this /* r1 => r0, fp-0x8 */)
    //     0xcfe3a4: mov             x0, x1
    //     0xcfe3a8: stur            x1, [fp, #-8]
    // 0xcfe3ac: CheckStackOverflow
    //     0xcfe3ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcfe3b0: cmp             SP, x16
    //     0xcfe3b4: b.ls            #0xcfe400
    // 0xcfe3b8: mov             x1, x0
    // 0xcfe3bc: r0 = absorb()
    //     0xcfe3bc: bl              #0x999744  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::absorb
    // 0xcfe3c0: ldur            x0, [fp, #-8]
    // 0xcfe3c4: LoadField: r1 = r0->field_67
    //     0xcfe3c4: ldur            w1, [x0, #0x67]
    // 0xcfe3c8: DecompressPointer r1
    //     0xcfe3c8: add             x1, x1, HEAP, lsl #32
    // 0xcfe3cc: cmp             w1, NULL
    // 0xcfe3d0: b.eq            #0xcfe408
    // 0xcfe3d4: StoreField: r1->field_7 = r0
    //     0xcfe3d4: stur            w0, [x1, #7]
    //     0xcfe3d8: ldurb           w16, [x1, #-1]
    //     0xcfe3dc: ldurb           w17, [x0, #-1]
    //     0xcfe3e0: and             x16, x17, x16, lsr #2
    //     0xcfe3e4: tst             x16, HEAP, lsr #32
    //     0xcfe3e8: b.eq            #0xcfe3f0
    //     0xcfe3ec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcfe3f0: r0 = Null
    //     0xcfe3f0: mov             x0, NULL
    // 0xcfe3f4: LeaveFrame
    //     0xcfe3f4: mov             SP, fp
    //     0xcfe3f8: ldp             fp, lr, [SP], #0x10
    // 0xcfe3fc: ret
    //     0xcfe3fc: ret             
    // 0xcfe400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcfe400: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcfe404: b               #0xcfe3b8
    // 0xcfe408: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcfe408: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ applyClampedDragUpdate(/* No info */) {
    // ** addr: 0xd26c38, size: 0x260
    // 0xd26c38: EnterFrame
    //     0xd26c38: stp             fp, lr, [SP, #-0x10]!
    //     0xd26c3c: mov             fp, SP
    // 0xd26c40: AllocStack(0x20)
    //     0xd26c40: sub             SP, SP, #0x20
    // 0xd26c44: d1 = 0.000000
    //     0xd26c44: eor             v1.16b, v1.16b, v1.16b
    // 0xd26c48: mov             x3, x1
    // 0xd26c4c: mov             v2.16b, v0.16b
    // 0xd26c50: stur            x1, [fp, #-8]
    // 0xd26c54: stur            d0, [fp, #-0x20]
    // 0xd26c58: CheckStackOverflow
    //     0xd26c58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd26c5c: cmp             SP, x16
    //     0xd26c60: b.ls            #0xd26e7c
    // 0xd26c64: fcmp            d1, d2
    // 0xd26c68: b.le            #0xd26c74
    // 0xd26c6c: d0 = -inf
    //     0xd26c6c: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0xd26c70: b               #0xd26d10
    // 0xd26c74: LoadField: r0 = r3->field_2f
    //     0xd26c74: ldur            w0, [x3, #0x2f]
    // 0xd26c78: DecompressPointer r0
    //     0xd26c78: add             x0, x0, HEAP, lsl #32
    // 0xd26c7c: cmp             w0, NULL
    // 0xd26c80: b.eq            #0xd26e84
    // 0xd26c84: LoadField: r1 = r3->field_3f
    //     0xd26c84: ldur            w1, [x3, #0x3f]
    // 0xd26c88: DecompressPointer r1
    //     0xd26c88: add             x1, x1, HEAP, lsl #32
    // 0xd26c8c: cmp             w1, NULL
    // 0xd26c90: b.eq            #0xd26e88
    // 0xd26c94: LoadField: d0 = r0->field_7
    //     0xd26c94: ldur            d0, [x0, #7]
    // 0xd26c98: LoadField: d3 = r1->field_7
    //     0xd26c98: ldur            d3, [x1, #7]
    // 0xd26c9c: fcmp            d0, d3
    // 0xd26ca0: b.le            #0xd26cac
    // 0xd26ca4: LoadField: d0 = r1->field_7
    //     0xd26ca4: ldur            d0, [x1, #7]
    // 0xd26ca8: b               #0xd26d10
    // 0xd26cac: fcmp            d3, d0
    // 0xd26cb0: b.le            #0xd26cbc
    // 0xd26cb4: LoadField: d0 = r0->field_7
    //     0xd26cb4: ldur            d0, [x0, #7]
    // 0xd26cb8: b               #0xd26d10
    // 0xd26cbc: fcmp            d0, d1
    // 0xd26cc0: b.ne            #0xd26cd4
    // 0xd26cc4: fadd            d4, d0, d3
    // 0xd26cc8: fmul            d5, d4, d0
    // 0xd26ccc: fmul            d0, d5, d3
    // 0xd26cd0: b               #0xd26d10
    // 0xd26cd4: fcmp            d0, d1
    // 0xd26cd8: b.ne            #0xd26cf8
    // 0xd26cdc: LoadField: d0 = r1->field_7
    //     0xd26cdc: ldur            d0, [x1, #7]
    // 0xd26ce0: fcmp            d0, #0.0
    // 0xd26ce4: b.vs            #0xd26cf8
    // 0xd26ce8: b.ne            #0xd26cf4
    // 0xd26cec: r2 = 0.000000
    //     0xd26cec: fmov            x2, d0
    // 0xd26cf0: cmp             x2, #0
    // 0xd26cf4: b.lt            #0xd26d04
    // 0xd26cf8: LoadField: d0 = r1->field_7
    //     0xd26cf8: ldur            d0, [x1, #7]
    // 0xd26cfc: fcmp            d0, d0
    // 0xd26d00: b.vc            #0xd26d0c
    // 0xd26d04: LoadField: d0 = r1->field_7
    //     0xd26d04: ldur            d0, [x1, #7]
    // 0xd26d08: b               #0xd26d10
    // 0xd26d0c: LoadField: d0 = r0->field_7
    //     0xd26d0c: ldur            d0, [x0, #7]
    // 0xd26d10: fcmp            d2, d1
    // 0xd26d14: b.le            #0xd26d20
    // 0xd26d18: d3 = inf
    //     0xd26d18: ldr             d3, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xd26d1c: b               #0xd26da4
    // 0xd26d20: LoadField: r0 = r3->field_3f
    //     0xd26d20: ldur            w0, [x3, #0x3f]
    // 0xd26d24: DecompressPointer r0
    //     0xd26d24: add             x0, x0, HEAP, lsl #32
    // 0xd26d28: cmp             w0, NULL
    // 0xd26d2c: b.eq            #0xd26e8c
    // 0xd26d30: LoadField: d3 = r0->field_7
    //     0xd26d30: ldur            d3, [x0, #7]
    // 0xd26d34: fcmp            d1, d3
    // 0xd26d38: b.le            #0xd26d44
    // 0xd26d3c: d3 = 0.000000
    //     0xd26d3c: eor             v3.16b, v3.16b, v3.16b
    // 0xd26d40: b               #0xd26da4
    // 0xd26d44: LoadField: r1 = r3->field_33
    //     0xd26d44: ldur            w1, [x3, #0x33]
    // 0xd26d48: DecompressPointer r1
    //     0xd26d48: add             x1, x1, HEAP, lsl #32
    // 0xd26d4c: cmp             w1, NULL
    // 0xd26d50: b.eq            #0xd26e90
    // 0xd26d54: LoadField: d4 = r1->field_7
    //     0xd26d54: ldur            d4, [x1, #7]
    // 0xd26d58: fcmp            d4, d3
    // 0xd26d5c: b.le            #0xd26d68
    // 0xd26d60: LoadField: d3 = r1->field_7
    //     0xd26d60: ldur            d3, [x1, #7]
    // 0xd26d64: b               #0xd26da4
    // 0xd26d68: fcmp            d3, d4
    // 0xd26d6c: b.le            #0xd26d78
    // 0xd26d70: LoadField: d3 = r0->field_7
    //     0xd26d70: ldur            d3, [x0, #7]
    // 0xd26d74: b               #0xd26da4
    // 0xd26d78: fcmp            d4, d1
    // 0xd26d7c: b.ne            #0xd26d8c
    // 0xd26d80: fadd            d5, d4, d3
    // 0xd26d84: mov             v3.16b, v5.16b
    // 0xd26d88: b               #0xd26da4
    // 0xd26d8c: LoadField: d3 = r0->field_7
    //     0xd26d8c: ldur            d3, [x0, #7]
    // 0xd26d90: fcmp            d3, d3
    // 0xd26d94: b.vc            #0xd26da0
    // 0xd26d98: LoadField: d3 = r0->field_7
    //     0xd26d98: ldur            d3, [x0, #7]
    // 0xd26d9c: b               #0xd26da4
    // 0xd26da0: LoadField: d3 = r1->field_7
    //     0xd26da0: ldur            d3, [x1, #7]
    // 0xd26da4: LoadField: r0 = r3->field_3f
    //     0xd26da4: ldur            w0, [x3, #0x3f]
    // 0xd26da8: DecompressPointer r0
    //     0xd26da8: add             x0, x0, HEAP, lsl #32
    // 0xd26dac: cmp             w0, NULL
    // 0xd26db0: b.eq            #0xd26e94
    // 0xd26db4: LoadField: d4 = r0->field_7
    //     0xd26db4: ldur            d4, [x0, #7]
    // 0xd26db8: stur            d4, [fp, #-0x18]
    // 0xd26dbc: fsub            d5, d4, d2
    // 0xd26dc0: fcmp            d0, d5
    // 0xd26dc4: b.le            #0xd26dd0
    // 0xd26dc8: mov             v3.16b, v0.16b
    // 0xd26dcc: b               #0xd26de4
    // 0xd26dd0: fcmp            d5, d3
    // 0xd26dd4: b.gt            #0xd26de4
    // 0xd26dd8: fcmp            d5, d5
    // 0xd26ddc: b.vs            #0xd26de4
    // 0xd26de0: mov             v3.16b, v5.16b
    // 0xd26de4: stur            d3, [fp, #-0x10]
    // 0xd26de8: fsub            d0, d3, d4
    // 0xd26dec: fcmp            d0, d1
    // 0xd26df0: b.ne            #0xd26e04
    // 0xd26df4: mov             v0.16b, v2.16b
    // 0xd26df8: LeaveFrame
    //     0xd26df8: mov             SP, fp
    //     0xd26dfc: ldp             fp, lr, [SP], #0x10
    // 0xd26e00: ret
    //     0xd26e00: ret             
    // 0xd26e04: LoadField: r1 = r3->field_23
    //     0xd26e04: ldur            w1, [x3, #0x23]
    // 0xd26e08: DecompressPointer r1
    //     0xd26e08: add             x1, x1, HEAP, lsl #32
    // 0xd26e0c: r0 = LoadClassIdInstr(r1)
    //     0xd26e0c: ldur            x0, [x1, #-1]
    //     0xd26e10: ubfx            x0, x0, #0xc, #0x14
    // 0xd26e14: mov             x2, x3
    // 0xd26e18: mov             v0.16b, v3.16b
    // 0xd26e1c: r0 = GDT[cid_x0 + -0xf9a]()
    //     0xd26e1c: sub             lr, x0, #0xf9a
    //     0xd26e20: ldr             lr, [x21, lr, lsl #3]
    //     0xd26e24: blr             lr
    // 0xd26e28: mov             v1.16b, v0.16b
    // 0xd26e2c: ldur            d0, [fp, #-0x10]
    // 0xd26e30: fsub            d2, d0, d1
    // 0xd26e34: ldur            d0, [fp, #-0x18]
    // 0xd26e38: fsub            d1, d2, d0
    // 0xd26e3c: stur            d1, [fp, #-0x10]
    // 0xd26e40: d0 = 0.000000
    //     0xd26e40: eor             v0.16b, v0.16b, v0.16b
    // 0xd26e44: fcmp            d1, d0
    // 0xd26e48: b.eq            #0xd26e64
    // 0xd26e4c: ldur            x1, [fp, #-8]
    // 0xd26e50: mov             v0.16b, v2.16b
    // 0xd26e54: r0 = forcePixels()
    //     0xd26e54: bl              #0x7a6d40  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::forcePixels
    // 0xd26e58: ldur            x1, [fp, #-8]
    // 0xd26e5c: ldur            d0, [fp, #-0x10]
    // 0xd26e60: r0 = didUpdateScrollPositionBy()
    //     0xd26e60: bl              #0x7a6c78  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didUpdateScrollPositionBy
    // 0xd26e64: ldur            d2, [fp, #-0x20]
    // 0xd26e68: ldur            d1, [fp, #-0x10]
    // 0xd26e6c: fadd            d0, d2, d1
    // 0xd26e70: LeaveFrame
    //     0xd26e70: mov             SP, fp
    //     0xd26e74: ldp             fp, lr, [SP], #0x10
    // 0xd26e78: ret
    //     0xd26e78: ret             
    // 0xd26e7c: r0 = StackOverflowSharedWithFPURegs()
    //     0xd26e7c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd26e80: b               #0xd26c64
    // 0xd26e84: r0 = NullCastErrorSharedWithFPURegs()
    //     0xd26e84: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xd26e88: r0 = NullCastErrorSharedWithFPURegs()
    //     0xd26e88: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xd26e8c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xd26e8c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xd26e90: r0 = NullCastErrorSharedWithFPURegs()
    //     0xd26e90: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xd26e94: r0 = NullCastErrorSharedWithFPURegs()
    //     0xd26e94: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ applyFullDragUpdate(/* No info */) {
    // ** addr: 0xd26e98, size: 0x16c
    // 0xd26e98: EnterFrame
    //     0xd26e98: stp             fp, lr, [SP, #-0x10]!
    //     0xd26e9c: mov             fp, SP
    // 0xd26ea0: AllocStack(0x38)
    //     0xd26ea0: sub             SP, SP, #0x38
    // 0xd26ea4: SetupParameters(_NestedScrollPosition this /* r1 => r3, fp-0x18 */)
    //     0xd26ea4: mov             x3, x1
    //     0xd26ea8: stur            x1, [fp, #-0x18]
    // 0xd26eac: CheckStackOverflow
    //     0xd26eac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd26eb0: cmp             SP, x16
    //     0xd26eb4: b.ls            #0xd26ff8
    // 0xd26eb8: LoadField: r4 = r3->field_3f
    //     0xd26eb8: ldur            w4, [x3, #0x3f]
    // 0xd26ebc: DecompressPointer r4
    //     0xd26ebc: add             x4, x4, HEAP, lsl #32
    // 0xd26ec0: stur            x4, [fp, #-0x10]
    // 0xd26ec4: cmp             w4, NULL
    // 0xd26ec8: b.eq            #0xd27000
    // 0xd26ecc: LoadField: r5 = r3->field_23
    //     0xd26ecc: ldur            w5, [x3, #0x23]
    // 0xd26ed0: DecompressPointer r5
    //     0xd26ed0: add             x5, x5, HEAP, lsl #32
    // 0xd26ed4: stur            x5, [fp, #-8]
    // 0xd26ed8: r0 = LoadClassIdInstr(r5)
    //     0xd26ed8: ldur            x0, [x5, #-1]
    //     0xd26edc: ubfx            x0, x0, #0xc, #0x14
    // 0xd26ee0: mov             x1, x5
    // 0xd26ee4: mov             x2, x3
    // 0xd26ee8: r0 = GDT[cid_x0 + -0xe87]()
    //     0xd26ee8: sub             lr, x0, #0xe87
    //     0xd26eec: ldr             lr, [x21, lr, lsl #3]
    //     0xd26ef0: blr             lr
    // 0xd26ef4: ldur            x0, [fp, #-0x10]
    // 0xd26ef8: LoadField: d1 = r0->field_7
    //     0xd26ef8: ldur            d1, [x0, #7]
    // 0xd26efc: stur            d1, [fp, #-0x28]
    // 0xd26f00: fsub            d2, d1, d0
    // 0xd26f04: stur            d2, [fp, #-0x20]
    // 0xd26f08: fsub            d0, d1, d2
    // 0xd26f0c: d3 = 0.000000
    //     0xd26f0c: eor             v3.16b, v3.16b, v3.16b
    // 0xd26f10: fcmp            d0, d3
    // 0xd26f14: b.ne            #0xd26f28
    // 0xd26f18: d4 = 0.000000
    //     0xd26f18: ldr             d4, [PP, #0x6fc8]  ; [pp+0x6fc8] IMM: double(1e-10) from 0x3ddb7cdfd9d7bdbb
    // 0xd26f1c: fcmp            d4, d3
    // 0xd26f20: b.le            #0xd26f5c
    // 0xd26f24: b               #0xd26f4c
    // 0xd26f28: d4 = 0.000000
    //     0xd26f28: ldr             d4, [PP, #0x6fc8]  ; [pp+0x6fc8] IMM: double(1e-10) from 0x3ddb7cdfd9d7bdbb
    // 0xd26f2c: fcmp            d3, d0
    // 0xd26f30: b.le            #0xd26f44
    // 0xd26f34: fneg            d5, d0
    // 0xd26f38: fcmp            d4, d5
    // 0xd26f3c: b.le            #0xd26f5c
    // 0xd26f40: b               #0xd26f4c
    // 0xd26f44: fcmp            d4, d0
    // 0xd26f48: b.le            #0xd26f5c
    // 0xd26f4c: mov             v0.16b, v3.16b
    // 0xd26f50: LeaveFrame
    //     0xd26f50: mov             SP, fp
    //     0xd26f54: ldp             fp, lr, [SP], #0x10
    // 0xd26f58: ret
    //     0xd26f58: ret             
    // 0xd26f5c: ldur            x1, [fp, #-8]
    // 0xd26f60: r0 = LoadClassIdInstr(r1)
    //     0xd26f60: ldur            x0, [x1, #-1]
    //     0xd26f64: ubfx            x0, x0, #0xc, #0x14
    // 0xd26f68: ldur            x2, [fp, #-0x18]
    // 0xd26f6c: mov             v0.16b, v2.16b
    // 0xd26f70: r0 = GDT[cid_x0 + -0xf9a]()
    //     0xd26f70: sub             lr, x0, #0xf9a
    //     0xd26f74: ldr             lr, [x21, lr, lsl #3]
    //     0xd26f78: blr             lr
    // 0xd26f7c: mov             v1.16b, v0.16b
    // 0xd26f80: ldur            d0, [fp, #-0x20]
    // 0xd26f84: stur            d1, [fp, #-0x38]
    // 0xd26f88: fsub            d2, d0, d1
    // 0xd26f8c: ldur            d3, [fp, #-0x28]
    // 0xd26f90: stur            d2, [fp, #-0x30]
    // 0xd26f94: fcmp            d2, d3
    // 0xd26f98: b.eq            #0xd26fc0
    // 0xd26f9c: ldur            x1, [fp, #-0x18]
    // 0xd26fa0: mov             v0.16b, v2.16b
    // 0xd26fa4: r0 = forcePixels()
    //     0xd26fa4: bl              #0x7a6d40  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::forcePixels
    // 0xd26fa8: ldur            d0, [fp, #-0x30]
    // 0xd26fac: ldur            d1, [fp, #-0x28]
    // 0xd26fb0: fsub            d2, d0, d1
    // 0xd26fb4: ldur            x1, [fp, #-0x18]
    // 0xd26fb8: mov             v0.16b, v2.16b
    // 0xd26fbc: r0 = didUpdateScrollPositionBy()
    //     0xd26fbc: bl              #0x7a6c78  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didUpdateScrollPositionBy
    // 0xd26fc0: ldur            d1, [fp, #-0x38]
    // 0xd26fc4: d0 = 0.000000
    //     0xd26fc4: eor             v0.16b, v0.16b, v0.16b
    // 0xd26fc8: fcmp            d1, d0
    // 0xd26fcc: b.eq            #0xd26fec
    // 0xd26fd0: ldur            x1, [fp, #-0x18]
    // 0xd26fd4: mov             v0.16b, v1.16b
    // 0xd26fd8: r0 = didOverscrollBy()
    //     0xd26fd8: bl              #0xd27004  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::didOverscrollBy
    // 0xd26fdc: ldur            d0, [fp, #-0x38]
    // 0xd26fe0: LeaveFrame
    //     0xd26fe0: mov             SP, fp
    //     0xd26fe4: ldp             fp, lr, [SP], #0x10
    // 0xd26fe8: ret
    //     0xd26fe8: ret             
    // 0xd26fec: LeaveFrame
    //     0xd26fec: mov             SP, fp
    //     0xd26ff0: ldp             fp, lr, [SP], #0x10
    // 0xd26ff4: ret
    //     0xd26ff4: ret             
    // 0xd26ff8: r0 = StackOverflowSharedWithFPURegs()
    //     0xd26ff8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd26ffc: b               #0xd26eb8
    // 0xd27000: r0 = NullCastErrorSharedWithFPURegs()
    //     0xd27000: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ goIdle(/* No info */) {
    // ** addr: 0xd839b8, size: 0x6c
    // 0xd839b8: EnterFrame
    //     0xd839b8: stp             fp, lr, [SP, #-0x10]!
    //     0xd839bc: mov             fp, SP
    // 0xd839c0: AllocStack(0x8)
    //     0xd839c0: sub             SP, SP, #8
    // 0xd839c4: SetupParameters(_NestedScrollPosition this /* r1 => r1, fp-0x8 */)
    //     0xd839c4: stur            x1, [fp, #-8]
    // 0xd839c8: CheckStackOverflow
    //     0xd839c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd839cc: cmp             SP, x16
    //     0xd839d0: b.ls            #0xd83a1c
    // 0xd839d4: r0 = IdleScrollActivity()
    //     0xd839d4: bl              #0xbf9cac  ; AllocateIdleScrollActivityStub -> IdleScrollActivity (size=0x10)
    // 0xd839d8: mov             x1, x0
    // 0xd839dc: r0 = false
    //     0xd839dc: add             x0, NULL, #0x30  ; false
    // 0xd839e0: StoreField: r1->field_b = r0
    //     0xd839e0: stur            w0, [x1, #0xb]
    // 0xd839e4: ldur            x0, [fp, #-8]
    // 0xd839e8: StoreField: r1->field_7 = r0
    //     0xd839e8: stur            w0, [x1, #7]
    // 0xd839ec: mov             x2, x1
    // 0xd839f0: mov             x1, x0
    // 0xd839f4: r0 = beginActivity()
    //     0xd839f4: bl              #0x67956c  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::beginActivity
    // 0xd839f8: ldur            x0, [fp, #-8]
    // 0xd839fc: LoadField: r1 = r0->field_6b
    //     0xd839fc: ldur            w1, [x0, #0x6b]
    // 0xd83a00: DecompressPointer r1
    //     0xd83a00: add             x1, x1, HEAP, lsl #32
    // 0xd83a04: r2 = Instance_ScrollDirection
    //     0xd83a04: ldr             x2, [PP, #0x6ed8]  ; [pp+0x6ed8] Obj!ScrollDirection@e352e1
    // 0xd83a08: r0 = updateUserScrollDirection()
    //     0xd83a08: bl              #0x679094  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::updateUserScrollDirection
    // 0xd83a0c: r0 = Null
    //     0xd83a0c: mov             x0, NULL
    // 0xd83a10: LeaveFrame
    //     0xd83a10: mov             SP, fp
    //     0xd83a14: ldp             fp, lr, [SP], #0x10
    // 0xd83a18: ret
    //     0xd83a18: ret             
    // 0xd83a1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd83a1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd83a20: b               #0xd839d4
  }
  _ drag(/* No info */) {
    // ** addr: 0xd847e8, size: 0x38
    // 0xd847e8: EnterFrame
    //     0xd847e8: stp             fp, lr, [SP, #-0x10]!
    //     0xd847ec: mov             fp, SP
    // 0xd847f0: CheckStackOverflow
    //     0xd847f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd847f4: cmp             SP, x16
    //     0xd847f8: b.ls            #0xd84818
    // 0xd847fc: LoadField: r0 = r1->field_6b
    //     0xd847fc: ldur            w0, [x1, #0x6b]
    // 0xd84800: DecompressPointer r0
    //     0xd84800: add             x0, x0, HEAP, lsl #32
    // 0xd84804: mov             x1, x0
    // 0xd84808: r0 = drag()
    //     0xd84808: bl              #0x99ae04  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::drag
    // 0xd8480c: LeaveFrame
    //     0xd8480c: mov             SP, fp
    //     0xd84810: ldp             fp, lr, [SP], #0x10
    // 0xd84814: ret
    //     0xd84814: ret             
    // 0xd84818: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd84818: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8481c: b               #0xd847fc
  }
  _ pointerScroll(/* No info */) {
    // ** addr: 0xd85004, size: 0x3c
    // 0xd85004: EnterFrame
    //     0xd85004: stp             fp, lr, [SP, #-0x10]!
    //     0xd85008: mov             fp, SP
    // 0xd8500c: CheckStackOverflow
    //     0xd8500c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd85010: cmp             SP, x16
    //     0xd85014: b.ls            #0xd85038
    // 0xd85018: LoadField: r0 = r1->field_6b
    //     0xd85018: ldur            w0, [x1, #0x6b]
    // 0xd8501c: DecompressPointer r0
    //     0xd8501c: add             x0, x0, HEAP, lsl #32
    // 0xd85020: mov             x1, x0
    // 0xd85024: r0 = pointerScroll()
    //     0xd85024: bl              #0x9de170  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::pointerScroll
    // 0xd85028: r0 = Null
    //     0xd85028: mov             x0, NULL
    // 0xd8502c: LeaveFrame
    //     0xd8502c: mov             SP, fp
    //     0xd85030: ldp             fp, lr, [SP], #0x10
    // 0xd85034: ret
    //     0xd85034: ret             
    // 0xd85038: r0 = StackOverflowSharedWithFPURegs()
    //     0xd85038: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd8503c: b               #0xd85018
  }
  get _ axisDirection(/* No info */) {
    // ** addr: 0xd87e48, size: 0x30
    // 0xd87e48: LoadField: r2 = r1->field_27
    //     0xd87e48: ldur            w2, [x1, #0x27]
    // 0xd87e4c: DecompressPointer r2
    //     0xd87e4c: add             x2, x2, HEAP, lsl #32
    // 0xd87e50: LoadField: r1 = r2->field_b
    //     0xd87e50: ldur            w1, [x2, #0xb]
    // 0xd87e54: DecompressPointer r1
    //     0xd87e54: add             x1, x1, HEAP, lsl #32
    // 0xd87e58: cmp             w1, NULL
    // 0xd87e5c: b.eq            #0xd87e6c
    // 0xd87e60: LoadField: r0 = r1->field_b
    //     0xd87e60: ldur            w0, [x1, #0xb]
    // 0xd87e64: DecompressPointer r0
    //     0xd87e64: add             x0, x0, HEAP, lsl #32
    // 0xd87e68: ret
    //     0xd87e68: ret             
    // 0xd87e6c: EnterFrame
    //     0xd87e6c: stp             fp, lr, [SP, #-0x10]!
    //     0xd87e70: mov             fp, SP
    // 0xd87e74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd87e74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ animateTo(/* No info */) {
    // ** addr: 0xd880f0, size: 0x60
    // 0xd880f0: EnterFrame
    //     0xd880f0: stp             fp, lr, [SP, #-0x10]!
    //     0xd880f4: mov             fp, SP
    // 0xd880f8: AllocStack(0x18)
    //     0xd880f8: sub             SP, SP, #0x18
    // 0xd880fc: SetupParameters(_NestedScrollPosition this /* r1 => r2 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xd880fc: mov             x0, x2
    //     0xd88100: stur            x2, [fp, #-0x10]
    //     0xd88104: mov             x2, x1
    //     0xd88108: stur            x3, [fp, #-0x18]
    // 0xd8810c: CheckStackOverflow
    //     0xd8810c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd88110: cmp             SP, x16
    //     0xd88114: b.ls            #0xd88148
    // 0xd88118: LoadField: r4 = r2->field_6b
    //     0xd88118: ldur            w4, [x2, #0x6b]
    // 0xd8811c: DecompressPointer r4
    //     0xd8811c: add             x4, x4, HEAP, lsl #32
    // 0xd88120: mov             x1, x4
    // 0xd88124: stur            x4, [fp, #-8]
    // 0xd88128: r0 = unnestOffset()
    //     0xd88128: bl              #0x67b614  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::unnestOffset
    // 0xd8812c: ldur            x1, [fp, #-8]
    // 0xd88130: ldur            x2, [fp, #-0x10]
    // 0xd88134: ldur            x3, [fp, #-0x18]
    // 0xd88138: r0 = animateTo()
    //     0xd88138: bl              #0x678c70  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::animateTo
    // 0xd8813c: LeaveFrame
    //     0xd8813c: mov             SP, fp
    //     0xd88140: ldp             fp, lr, [SP], #0x10
    // 0xd88144: ret
    //     0xd88144: ret             
    // 0xd88148: r0 = StackOverflowSharedWithFPURegs()
    //     0xd88148: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd8814c: b               #0xd88118
  }
  _ goBallistic(/* No info */) {
    // ** addr: 0xd88208, size: 0xac
    // 0xd88208: EnterFrame
    //     0xd88208: stp             fp, lr, [SP, #-0x10]!
    //     0xd8820c: mov             fp, SP
    // 0xd88210: AllocStack(0x10)
    //     0xd88210: sub             SP, SP, #0x10
    // 0xd88214: d1 = 0.000000
    //     0xd88214: eor             v1.16b, v1.16b, v1.16b
    // 0xd88218: mov             x0, x1
    // 0xd8821c: stur            x1, [fp, #-8]
    // 0xd88220: stur            d0, [fp, #-0x10]
    // 0xd88224: CheckStackOverflow
    //     0xd88224: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd88228: cmp             SP, x16
    //     0xd8822c: b.ls            #0xd882ac
    // 0xd88230: fcmp            d0, d1
    // 0xd88234: b.eq            #0xd88240
    // 0xd88238: mov             x3, x0
    // 0xd8823c: b               #0xd88250
    // 0xd88240: mov             x1, x0
    // 0xd88244: r0 = outOfRange()
    //     0xd88244: bl              #0xc3f37c  ; [package:flutter/src/widgets/scroll_position.dart] _ScrollPosition&ViewportOffset&ScrollMetrics::outOfRange
    // 0xd88248: tbnz            w0, #4, #0xd8827c
    // 0xd8824c: ldur            x3, [fp, #-8]
    // 0xd88250: LoadField: r1 = r3->field_23
    //     0xd88250: ldur            w1, [x3, #0x23]
    // 0xd88254: DecompressPointer r1
    //     0xd88254: add             x1, x1, HEAP, lsl #32
    // 0xd88258: r0 = LoadClassIdInstr(r1)
    //     0xd88258: ldur            x0, [x1, #-1]
    //     0xd8825c: ubfx            x0, x0, #0xc, #0x14
    // 0xd88260: mov             x2, x3
    // 0xd88264: ldur            d0, [fp, #-0x10]
    // 0xd88268: r0 = GDT[cid_x0 + -0xff1]()
    //     0xd88268: sub             lr, x0, #0xff1
    //     0xd8826c: ldr             lr, [x21, lr, lsl #3]
    //     0xd88270: blr             lr
    // 0xd88274: mov             x2, x0
    // 0xd88278: b               #0xd88280
    // 0xd8827c: r2 = Null
    //     0xd8827c: mov             x2, NULL
    // 0xd88280: ldur            x1, [fp, #-8]
    // 0xd88284: r3 = Instance__NestedBallisticScrollActivityMode
    //     0xd88284: ldr             x3, [PP, #0x7680]  ; [pp+0x7680] Obj!_NestedBallisticScrollActivityMode@e33de1
    // 0xd88288: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xd88288: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xd8828c: r0 = createBallisticScrollActivity()
    //     0xd8828c: bl              #0xbf954c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollPosition::createBallisticScrollActivity
    // 0xd88290: ldur            x1, [fp, #-8]
    // 0xd88294: mov             x2, x0
    // 0xd88298: r0 = beginActivity()
    //     0xd88298: bl              #0x67956c  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::beginActivity
    // 0xd8829c: r0 = Null
    //     0xd8829c: mov             x0, NULL
    // 0xd882a0: LeaveFrame
    //     0xd882a0: mov             SP, fp
    //     0xd882a4: ldp             fp, lr, [SP], #0x10
    // 0xd882a8: ret
    //     0xd882a8: ret             
    // 0xd882ac: r0 = StackOverflowSharedWithFPURegs()
    //     0xd882ac: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd882b0: b               #0xd88230
  }
  _ jumpTo(/* No info */) {
    // ** addr: 0xd893fc, size: 0x50
    // 0xd893fc: EnterFrame
    //     0xd893fc: stp             fp, lr, [SP, #-0x10]!
    //     0xd89400: mov             fp, SP
    // 0xd89404: AllocStack(0x8)
    //     0xd89404: sub             SP, SP, #8
    // 0xd89408: SetupParameters(_NestedScrollPosition this /* r1 => r2 */)
    //     0xd89408: mov             x2, x1
    // 0xd8940c: CheckStackOverflow
    //     0xd8940c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd89410: cmp             SP, x16
    //     0xd89414: b.ls            #0xd89444
    // 0xd89418: LoadField: r0 = r2->field_6b
    //     0xd89418: ldur            w0, [x2, #0x6b]
    // 0xd8941c: DecompressPointer r0
    //     0xd8941c: add             x0, x0, HEAP, lsl #32
    // 0xd89420: mov             x1, x0
    // 0xd89424: stur            x0, [fp, #-8]
    // 0xd89428: r0 = unnestOffset()
    //     0xd89428: bl              #0x67b614  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::unnestOffset
    // 0xd8942c: ldur            x1, [fp, #-8]
    // 0xd89430: r0 = jumpTo()
    //     0xd89430: bl              #0x7a69c4  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::jumpTo
    // 0xd89434: r0 = Null
    //     0xd89434: mov             x0, NULL
    // 0xd89438: LeaveFrame
    //     0xd89438: mov             SP, fp
    //     0xd8943c: ldp             fp, lr, [SP], #0x10
    // 0xd89440: ret
    //     0xd89440: ret             
    // 0xd89444: r0 = StackOverflowSharedWithFPURegs()
    //     0xd89444: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd89448: b               #0xd89418
  }
}

// class id: 4203, size: 0x20, field offset: 0x14
class NestedScrollViewState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x943520, size: 0x9c
    // 0x943520: EnterFrame
    //     0x943520: stp             fp, lr, [SP, #-0x10]!
    //     0x943524: mov             fp, SP
    // 0x943528: AllocStack(0x10)
    //     0x943528: sub             SP, SP, #0x10
    // 0x94352c: SetupParameters(NestedScrollViewState this /* r1 => r0, fp-0x8 */)
    //     0x94352c: mov             x0, x1
    //     0x943530: stur            x1, [fp, #-8]
    // 0x943534: CheckStackOverflow
    //     0x943534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x943538: cmp             SP, x16
    //     0x94353c: b.ls            #0x9435b0
    // 0x943540: LoadField: r1 = r0->field_b
    //     0x943540: ldur            w1, [x0, #0xb]
    // 0x943544: DecompressPointer r1
    //     0x943544: add             x1, x1, HEAP, lsl #32
    // 0x943548: cmp             w1, NULL
    // 0x94354c: b.eq            #0x9435b8
    // 0x943550: mov             x2, x0
    // 0x943554: r1 = Function '_handleHasScrolledBodyChanged@303016527':.
    //     0x943554: add             x1, PP, #0x46, lsl #12  ; [pp+0x460a8] AnonymousClosure: (0x943734), in [package:flutter/src/widgets/nested_scroll_view.dart] NestedScrollViewState::_handleHasScrolledBodyChanged (0x94376c)
    //     0x943558: ldr             x1, [x1, #0xa8]
    // 0x94355c: r0 = AllocateClosure()
    //     0x94355c: bl              #0xec1630  ; AllocateClosureStub
    // 0x943560: stur            x0, [fp, #-0x10]
    // 0x943564: r0 = _NestedScrollCoordinator()
    //     0x943564: bl              #0x943728  ; Allocate_NestedScrollCoordinatorStub -> _NestedScrollCoordinator (size=0x28)
    // 0x943568: mov             x1, x0
    // 0x94356c: ldur            x2, [fp, #-8]
    // 0x943570: ldur            x3, [fp, #-0x10]
    // 0x943574: stur            x0, [fp, #-0x10]
    // 0x943578: r0 = _NestedScrollCoordinator()
    //     0x943578: bl              #0x9435e0  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::_NestedScrollCoordinator
    // 0x94357c: ldur            x0, [fp, #-0x10]
    // 0x943580: ldur            x1, [fp, #-8]
    // 0x943584: ArrayStore: r1[0] = r0  ; List_4
    //     0x943584: stur            w0, [x1, #0x17]
    //     0x943588: ldurb           w16, [x1, #-1]
    //     0x94358c: ldurb           w17, [x0, #-1]
    //     0x943590: and             x16, x17, x16, lsr #2
    //     0x943594: tst             x16, HEAP, lsr #32
    //     0x943598: b.eq            #0x9435a0
    //     0x94359c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9435a0: r0 = Null
    //     0x9435a0: mov             x0, NULL
    // 0x9435a4: LeaveFrame
    //     0x9435a4: mov             SP, fp
    //     0x9435a8: ldp             fp, lr, [SP], #0x10
    // 0x9435ac: ret
    //     0x9435ac: ret             
    // 0x9435b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9435b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9435b4: b               #0x943540
    // 0x9435b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9435b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _handleHasScrolledBodyChanged(dynamic) {
    // ** addr: 0x943734, size: 0x38
    // 0x943734: EnterFrame
    //     0x943734: stp             fp, lr, [SP, #-0x10]!
    //     0x943738: mov             fp, SP
    // 0x94373c: ldr             x0, [fp, #0x10]
    // 0x943740: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x943740: ldur            w1, [x0, #0x17]
    // 0x943744: DecompressPointer r1
    //     0x943744: add             x1, x1, HEAP, lsl #32
    // 0x943748: CheckStackOverflow
    //     0x943748: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94374c: cmp             SP, x16
    //     0x943750: b.ls            #0x943764
    // 0x943754: r0 = _handleHasScrolledBodyChanged()
    //     0x943754: bl              #0x94376c  ; [package:flutter/src/widgets/nested_scroll_view.dart] NestedScrollViewState::_handleHasScrolledBodyChanged
    // 0x943758: LeaveFrame
    //     0x943758: mov             SP, fp
    //     0x94375c: ldp             fp, lr, [SP], #0x10
    // 0x943760: ret
    //     0x943760: ret             
    // 0x943764: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x943764: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x943768: b               #0x943754
  }
  _ _handleHasScrolledBodyChanged(/* No info */) {
    // ** addr: 0x94376c, size: 0xa4
    // 0x94376c: EnterFrame
    //     0x94376c: stp             fp, lr, [SP, #-0x10]!
    //     0x943770: mov             fp, SP
    // 0x943774: AllocStack(0x8)
    //     0x943774: sub             SP, SP, #8
    // 0x943778: SetupParameters(NestedScrollViewState this /* r1 => r0, fp-0x8 */)
    //     0x943778: mov             x0, x1
    //     0x94377c: stur            x1, [fp, #-8]
    // 0x943780: CheckStackOverflow
    //     0x943780: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x943784: cmp             SP, x16
    //     0x943788: b.ls            #0x943804
    // 0x94378c: LoadField: r1 = r0->field_f
    //     0x94378c: ldur            w1, [x0, #0xf]
    // 0x943790: DecompressPointer r1
    //     0x943790: add             x1, x1, HEAP, lsl #32
    // 0x943794: cmp             w1, NULL
    // 0x943798: b.ne            #0x9437ac
    // 0x94379c: r0 = Null
    //     0x94379c: mov             x0, NULL
    // 0x9437a0: LeaveFrame
    //     0x9437a0: mov             SP, fp
    //     0x9437a4: ldp             fp, lr, [SP], #0x10
    // 0x9437a8: ret
    //     0x9437a8: ret             
    // 0x9437ac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9437ac: ldur            w1, [x0, #0x17]
    // 0x9437b0: DecompressPointer r1
    //     0x9437b0: add             x1, x1, HEAP, lsl #32
    // 0x9437b4: cmp             w1, NULL
    // 0x9437b8: b.eq            #0x94380c
    // 0x9437bc: r0 = hasScrolledBody()
    //     0x9437bc: bl              #0x943810  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::hasScrolledBody
    // 0x9437c0: mov             x1, x0
    // 0x9437c4: ldur            x0, [fp, #-8]
    // 0x9437c8: LoadField: r2 = r0->field_1b
    //     0x9437c8: ldur            w2, [x0, #0x1b]
    // 0x9437cc: DecompressPointer r2
    //     0x9437cc: add             x2, x2, HEAP, lsl #32
    // 0x9437d0: cmp             w2, w1
    // 0x9437d4: b.eq            #0x9437f4
    // 0x9437d8: r1 = Function '<anonymous closure>':.
    //     0x9437d8: add             x1, PP, #0x46, lsl #12  ; [pp+0x460a0] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x9437dc: ldr             x1, [x1, #0xa0]
    // 0x9437e0: r2 = Null
    //     0x9437e0: mov             x2, NULL
    // 0x9437e4: r0 = AllocateClosure()
    //     0x9437e4: bl              #0xec1630  ; AllocateClosureStub
    // 0x9437e8: ldur            x1, [fp, #-8]
    // 0x9437ec: mov             x2, x0
    // 0x9437f0: r0 = setState()
    //     0x9437f0: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9437f4: r0 = Null
    //     0x9437f4: mov             x0, NULL
    // 0x9437f8: LeaveFrame
    //     0x9437f8: mov             SP, fp
    //     0x9437fc: ldp             fp, lr, [SP], #0x10
    // 0x943800: ret
    //     0x943800: ret             
    // 0x943804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x943804: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x943808: b               #0x94378c
    // 0x94380c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94380c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x994c8c, size: 0xbc
    // 0x994c8c: EnterFrame
    //     0x994c8c: stp             fp, lr, [SP, #-0x10]!
    //     0x994c90: mov             fp, SP
    // 0x994c94: AllocStack(0x10)
    //     0x994c94: sub             SP, SP, #0x10
    // 0x994c98: SetupParameters(NestedScrollViewState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x994c98: mov             x0, x2
    //     0x994c9c: mov             x4, x1
    //     0x994ca0: mov             x3, x2
    //     0x994ca4: stur            x1, [fp, #-8]
    //     0x994ca8: stur            x2, [fp, #-0x10]
    // 0x994cac: r2 = Null
    //     0x994cac: mov             x2, NULL
    // 0x994cb0: r1 = Null
    //     0x994cb0: mov             x1, NULL
    // 0x994cb4: r4 = 60
    //     0x994cb4: movz            x4, #0x3c
    // 0x994cb8: branchIfSmi(r0, 0x994cc4)
    //     0x994cb8: tbz             w0, #0, #0x994cc4
    // 0x994cbc: r4 = LoadClassIdInstr(r0)
    //     0x994cbc: ldur            x4, [x0, #-1]
    //     0x994cc0: ubfx            x4, x4, #0xc, #0x14
    // 0x994cc4: r17 = 4776
    //     0x994cc4: movz            x17, #0x12a8
    // 0x994cc8: cmp             x4, x17
    // 0x994ccc: b.eq            #0x994ce4
    // 0x994cd0: r8 = NestedScrollView
    //     0x994cd0: add             x8, PP, #0x45, lsl #12  ; [pp+0x45f80] Type: NestedScrollView
    //     0x994cd4: ldr             x8, [x8, #0xf80]
    // 0x994cd8: r3 = Null
    //     0x994cd8: add             x3, PP, #0x45, lsl #12  ; [pp+0x45f88] Null
    //     0x994cdc: ldr             x3, [x3, #0xf88]
    // 0x994ce0: r0 = NestedScrollView()
    //     0x994ce0: bl              #0x9435bc  ; IsType_NestedScrollView_Stub
    // 0x994ce4: ldur            x3, [fp, #-8]
    // 0x994ce8: LoadField: r2 = r3->field_7
    //     0x994ce8: ldur            w2, [x3, #7]
    // 0x994cec: DecompressPointer r2
    //     0x994cec: add             x2, x2, HEAP, lsl #32
    // 0x994cf0: ldur            x0, [fp, #-0x10]
    // 0x994cf4: r1 = Null
    //     0x994cf4: mov             x1, NULL
    // 0x994cf8: cmp             w2, NULL
    // 0x994cfc: b.eq            #0x994d20
    // 0x994d00: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x994d00: ldur            w4, [x2, #0x17]
    // 0x994d04: DecompressPointer r4
    //     0x994d04: add             x4, x4, HEAP, lsl #32
    // 0x994d08: r8 = X0 bound StatefulWidget
    //     0x994d08: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x994d0c: ldr             x8, [x8, #0x7f8]
    // 0x994d10: LoadField: r9 = r4->field_7
    //     0x994d10: ldur            x9, [x4, #7]
    // 0x994d14: r3 = Null
    //     0x994d14: add             x3, PP, #0x45, lsl #12  ; [pp+0x45f98] Null
    //     0x994d18: ldr             x3, [x3, #0xf98]
    // 0x994d1c: blr             x9
    // 0x994d20: ldur            x1, [fp, #-8]
    // 0x994d24: LoadField: r2 = r1->field_b
    //     0x994d24: ldur            w2, [x1, #0xb]
    // 0x994d28: DecompressPointer r2
    //     0x994d28: add             x2, x2, HEAP, lsl #32
    // 0x994d2c: cmp             w2, NULL
    // 0x994d30: b.eq            #0x994d44
    // 0x994d34: r0 = Null
    //     0x994d34: mov             x0, NULL
    // 0x994d38: LeaveFrame
    //     0x994d38: mov             SP, fp
    //     0x994d3c: ldp             fp, lr, [SP], #0x10
    // 0x994d40: ret
    //     0x994d40: ret             
    // 0x994d44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x994d44: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9a79d0, size: 0x5c
    // 0x9a79d0: EnterFrame
    //     0x9a79d0: stp             fp, lr, [SP, #-0x10]!
    //     0x9a79d4: mov             fp, SP
    // 0x9a79d8: CheckStackOverflow
    //     0x9a79d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a79dc: cmp             SP, x16
    //     0x9a79e0: b.ls            #0x9a7a1c
    // 0x9a79e4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9a79e4: ldur            w0, [x1, #0x17]
    // 0x9a79e8: DecompressPointer r0
    //     0x9a79e8: add             x0, x0, HEAP, lsl #32
    // 0x9a79ec: cmp             w0, NULL
    // 0x9a79f0: b.eq            #0x9a7a24
    // 0x9a79f4: LoadField: r2 = r1->field_b
    //     0x9a79f4: ldur            w2, [x1, #0xb]
    // 0x9a79f8: DecompressPointer r2
    //     0x9a79f8: add             x2, x2, HEAP, lsl #32
    // 0x9a79fc: cmp             w2, NULL
    // 0x9a7a00: b.eq            #0x9a7a28
    // 0x9a7a04: mov             x1, x0
    // 0x9a7a08: r0 = setParent()
    //     0x9a7a08: bl              #0x9a7a2c  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::setParent
    // 0x9a7a0c: r0 = Null
    //     0x9a7a0c: mov             x0, NULL
    // 0x9a7a10: LeaveFrame
    //     0x9a7a10: mov             SP, fp
    //     0x9a7a14: ldp             fp, lr, [SP], #0x10
    // 0x9a7a18: ret
    //     0x9a7a18: ret             
    // 0x9a7a1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a7a1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a7a20: b               #0x9a79e4
    // 0x9a7a24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a7a24: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a7a28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a7a28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa1781c, size: 0x80
    // 0xa1781c: EnterFrame
    //     0xa1781c: stp             fp, lr, [SP, #-0x10]!
    //     0xa17820: mov             fp, SP
    // 0xa17824: AllocStack(0x18)
    //     0xa17824: sub             SP, SP, #0x18
    // 0xa17828: SetupParameters(NestedScrollViewState this /* r1 => r1, fp-0x8 */)
    //     0xa17828: stur            x1, [fp, #-8]
    // 0xa1782c: r1 = 1
    //     0xa1782c: movz            x1, #0x1
    // 0xa17830: r0 = AllocateContext()
    //     0xa17830: bl              #0xec126c  ; AllocateContextStub
    // 0xa17834: mov             x1, x0
    // 0xa17838: ldur            x0, [fp, #-8]
    // 0xa1783c: StoreField: r1->field_f = r0
    //     0xa1783c: stur            w0, [x1, #0xf]
    // 0xa17840: LoadField: r2 = r0->field_b
    //     0xa17840: ldur            w2, [x0, #0xb]
    // 0xa17844: DecompressPointer r2
    //     0xa17844: add             x2, x2, HEAP, lsl #32
    // 0xa17848: cmp             w2, NULL
    // 0xa1784c: b.eq            #0xa17898
    // 0xa17850: mov             x2, x1
    // 0xa17854: r1 = Function '<anonymous closure>':.
    //     0xa17854: add             x1, PP, #0x45, lsl #12  ; [pp+0x45f58] AnonymousClosure: (0xa178a8), in [package:flutter/src/widgets/nested_scroll_view.dart] NestedScrollViewState::build (0xa1781c)
    //     0xa17858: ldr             x1, [x1, #0xf58]
    // 0xa1785c: r0 = AllocateClosure()
    //     0xa1785c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa17860: stur            x0, [fp, #-0x10]
    // 0xa17864: r0 = Builder()
    //     0xa17864: bl              #0x6a5c84  ; AllocateBuilderStub -> Builder (size=0x10)
    // 0xa17868: mov             x1, x0
    // 0xa1786c: ldur            x0, [fp, #-0x10]
    // 0xa17870: stur            x1, [fp, #-0x18]
    // 0xa17874: StoreField: r1->field_b = r0
    //     0xa17874: stur            w0, [x1, #0xb]
    // 0xa17878: r0 = _InheritedNestedScrollView()
    //     0xa17878: bl              #0xa1789c  ; Allocate_InheritedNestedScrollViewStub -> _InheritedNestedScrollView (size=0x14)
    // 0xa1787c: ldur            x1, [fp, #-8]
    // 0xa17880: StoreField: r0->field_f = r1
    //     0xa17880: stur            w1, [x0, #0xf]
    // 0xa17884: ldur            x1, [fp, #-0x18]
    // 0xa17888: StoreField: r0->field_b = r1
    //     0xa17888: stur            w1, [x0, #0xb]
    // 0xa1788c: LeaveFrame
    //     0xa1788c: mov             SP, fp
    //     0xa17890: ldp             fp, lr, [SP], #0x10
    // 0xa17894: ret
    //     0xa17894: ret             
    // 0xa17898: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa17898: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] _NestedScrollViewCustomScrollView <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xa178a8, size: 0x20c
    // 0xa178a8: EnterFrame
    //     0xa178a8: stp             fp, lr, [SP, #-0x10]!
    //     0xa178ac: mov             fp, SP
    // 0xa178b0: AllocStack(0x28)
    //     0xa178b0: sub             SP, SP, #0x28
    // 0xa178b4: SetupParameters()
    //     0xa178b4: ldr             x0, [fp, #0x18]
    //     0xa178b8: ldur            w2, [x0, #0x17]
    //     0xa178bc: add             x2, x2, HEAP, lsl #32
    //     0xa178c0: stur            x2, [fp, #-0x10]
    // 0xa178c4: CheckStackOverflow
    //     0xa178c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa178c8: cmp             SP, x16
    //     0xa178cc: b.ls            #0xa17a84
    // 0xa178d0: LoadField: r0 = r2->field_f
    //     0xa178d0: ldur            w0, [x2, #0xf]
    // 0xa178d4: DecompressPointer r0
    //     0xa178d4: add             x0, x0, HEAP, lsl #32
    // 0xa178d8: stur            x0, [fp, #-8]
    // 0xa178dc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa178dc: ldur            w1, [x0, #0x17]
    // 0xa178e0: DecompressPointer r1
    //     0xa178e0: add             x1, x1, HEAP, lsl #32
    // 0xa178e4: cmp             w1, NULL
    // 0xa178e8: b.eq            #0xa17a8c
    // 0xa178ec: r0 = hasScrolledBody()
    //     0xa178ec: bl              #0x943810  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::hasScrolledBody
    // 0xa178f0: mov             x1, x0
    // 0xa178f4: ldur            x0, [fp, #-8]
    // 0xa178f8: StoreField: r0->field_1b = r1
    //     0xa178f8: stur            w1, [x0, #0x1b]
    // 0xa178fc: ldur            x0, [fp, #-0x10]
    // 0xa17900: LoadField: r1 = r0->field_f
    //     0xa17900: ldur            w1, [x0, #0xf]
    // 0xa17904: DecompressPointer r1
    //     0xa17904: add             x1, x1, HEAP, lsl #32
    // 0xa17908: LoadField: r2 = r1->field_b
    //     0xa17908: ldur            w2, [x1, #0xb]
    // 0xa1790c: DecompressPointer r2
    //     0xa1790c: add             x2, x2, HEAP, lsl #32
    // 0xa17910: cmp             w2, NULL
    // 0xa17914: b.eq            #0xa17a90
    // 0xa17918: ldr             x1, [fp, #0x10]
    // 0xa1791c: r0 = of()
    //     0xa1791c: bl              #0x999ac0  ; [package:flutter/src/widgets/scroll_configuration.dart] ScrollConfiguration::of
    // 0xa17920: r1 = LoadClassIdInstr(r0)
    //     0xa17920: ldur            x1, [x0, #-1]
    //     0xa17924: ubfx            x1, x1, #0xc, #0x14
    // 0xa17928: r16 = false
    //     0xa17928: add             x16, NULL, #0x30  ; false
    // 0xa1792c: str             x16, [SP]
    // 0xa17930: mov             x16, x0
    // 0xa17934: mov             x0, x1
    // 0xa17938: mov             x1, x16
    // 0xa1793c: r4 = const [0, 0x2, 0x1, 0x1, scrollbars, 0x1, null]
    //     0xa1793c: add             x4, PP, #0x45, lsl #12  ; [pp+0x45e60] List(7) [0, 0x2, 0x1, 0x1, "scrollbars", 0x1, Null]
    //     0xa17940: ldr             x4, [x4, #0xe60]
    // 0xa17944: r0 = GDT[cid_x0 + -0xffc]()
    //     0xa17944: sub             lr, x0, #0xffc
    //     0xa17948: ldr             lr, [x21, lr, lsl #3]
    //     0xa1794c: blr             lr
    // 0xa17950: mov             x4, x0
    // 0xa17954: ldur            x0, [fp, #-0x10]
    // 0xa17958: stur            x4, [fp, #-0x18]
    // 0xa1795c: LoadField: r1 = r0->field_f
    //     0xa1795c: ldur            w1, [x0, #0xf]
    // 0xa17960: DecompressPointer r1
    //     0xa17960: add             x1, x1, HEAP, lsl #32
    // 0xa17964: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa17964: ldur            w2, [x1, #0x17]
    // 0xa17968: DecompressPointer r2
    //     0xa17968: add             x2, x2, HEAP, lsl #32
    // 0xa1796c: cmp             w2, NULL
    // 0xa17970: b.eq            #0xa17a94
    // 0xa17974: ArrayLoad: r6 = r2[0]  ; List_4
    //     0xa17974: ldur            w6, [x2, #0x17]
    // 0xa17978: DecompressPointer r6
    //     0xa17978: add             x6, x6, HEAP, lsl #32
    // 0xa1797c: r16 = Sentinel
    //     0xa1797c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa17980: cmp             w6, w16
    // 0xa17984: b.eq            #0xa17a98
    // 0xa17988: stur            x6, [fp, #-8]
    // 0xa1798c: LoadField: r3 = r1->field_b
    //     0xa1798c: ldur            w3, [x1, #0xb]
    // 0xa17990: DecompressPointer r3
    //     0xa17990: add             x3, x3, HEAP, lsl #32
    // 0xa17994: cmp             w3, NULL
    // 0xa17998: b.eq            #0xa17aa0
    // 0xa1799c: LoadField: r5 = r2->field_1b
    //     0xa1799c: ldur            w5, [x2, #0x1b]
    // 0xa179a0: DecompressPointer r5
    //     0xa179a0: add             x5, x5, HEAP, lsl #32
    // 0xa179a4: r16 = Sentinel
    //     0xa179a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa179a8: cmp             w5, w16
    // 0xa179ac: b.eq            #0xa17aa4
    // 0xa179b0: LoadField: r2 = r1->field_1b
    //     0xa179b0: ldur            w2, [x1, #0x1b]
    // 0xa179b4: DecompressPointer r2
    //     0xa179b4: add             x2, x2, HEAP, lsl #32
    // 0xa179b8: cmp             w2, NULL
    // 0xa179bc: b.eq            #0xa17aac
    // 0xa179c0: mov             x1, x3
    // 0xa179c4: mov             x3, x5
    // 0xa179c8: mov             x5, x2
    // 0xa179cc: ldr             x2, [fp, #0x10]
    // 0xa179d0: r0 = _buildSlivers()
    //     0xa179d0: bl              #0xa17ac0  ; [package:flutter/src/widgets/nested_scroll_view.dart] NestedScrollView::_buildSlivers
    // 0xa179d4: mov             x1, x0
    // 0xa179d8: ldur            x0, [fp, #-0x10]
    // 0xa179dc: stur            x1, [fp, #-0x20]
    // 0xa179e0: LoadField: r2 = r0->field_f
    //     0xa179e0: ldur            w2, [x0, #0xf]
    // 0xa179e4: DecompressPointer r2
    //     0xa179e4: add             x2, x2, HEAP, lsl #32
    // 0xa179e8: LoadField: r0 = r2->field_13
    //     0xa179e8: ldur            w0, [x2, #0x13]
    // 0xa179ec: DecompressPointer r0
    //     0xa179ec: add             x0, x0, HEAP, lsl #32
    // 0xa179f0: stur            x0, [fp, #-0x10]
    // 0xa179f4: LoadField: r3 = r2->field_b
    //     0xa179f4: ldur            w3, [x2, #0xb]
    // 0xa179f8: DecompressPointer r3
    //     0xa179f8: add             x3, x3, HEAP, lsl #32
    // 0xa179fc: cmp             w3, NULL
    // 0xa17a00: b.eq            #0xa17ab0
    // 0xa17a04: r0 = _NestedScrollViewCustomScrollView()
    //     0xa17a04: bl              #0xa17ab4  ; Allocate_NestedScrollViewCustomScrollViewStub -> _NestedScrollViewCustomScrollView (size=0x58)
    // 0xa17a08: ldur            x1, [fp, #-0x10]
    // 0xa17a0c: StoreField: r0->field_53 = r1
    //     0xa17a0c: stur            w1, [x0, #0x53]
    // 0xa17a10: ldur            x1, [fp, #-0x20]
    // 0xa17a14: StoreField: r0->field_4f = r1
    //     0xa17a14: stur            w1, [x0, #0x4f]
    // 0xa17a18: r1 = Instance_Axis
    //     0xa17a18: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa17a1c: StoreField: r0->field_b = r1
    //     0xa17a1c: stur            w1, [x0, #0xb]
    // 0xa17a20: r1 = false
    //     0xa17a20: add             x1, NULL, #0x30  ; false
    // 0xa17a24: StoreField: r0->field_f = r1
    //     0xa17a24: stur            w1, [x0, #0xf]
    // 0xa17a28: ldur            x2, [fp, #-8]
    // 0xa17a2c: StoreField: r0->field_13 = r2
    //     0xa17a2c: stur            w2, [x0, #0x13]
    // 0xa17a30: ldur            x2, [fp, #-0x18]
    // 0xa17a34: StoreField: r0->field_1f = r2
    //     0xa17a34: stur            w2, [x0, #0x1f]
    // 0xa17a38: StoreField: r0->field_23 = r1
    //     0xa17a38: stur            w1, [x0, #0x23]
    // 0xa17a3c: StoreField: r0->field_2b = rZR
    //     0xa17a3c: stur            xzr, [x0, #0x2b]
    // 0xa17a40: r1 = Instance_DragStartBehavior
    //     0xa17a40: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xa17a44: StoreField: r0->field_3b = r1
    //     0xa17a44: stur            w1, [x0, #0x3b]
    // 0xa17a48: r1 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xa17a48: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f00] Obj!ScrollViewKeyboardDismissBehavior@e33b61
    //     0xa17a4c: ldr             x1, [x1, #0xf00]
    // 0xa17a50: StoreField: r0->field_3f = r1
    //     0xa17a50: stur            w1, [x0, #0x3f]
    // 0xa17a54: r1 = Instance_Clip
    //     0xa17a54: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa17a58: ldr             x1, [x1, #0x7c0]
    // 0xa17a5c: StoreField: r0->field_47 = r1
    //     0xa17a5c: stur            w1, [x0, #0x47]
    // 0xa17a60: r1 = Instance_HitTestBehavior
    //     0xa17a60: add             x1, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xa17a64: ldr             x1, [x1, #0x1c8]
    // 0xa17a68: StoreField: r0->field_4b = r1
    //     0xa17a68: stur            w1, [x0, #0x4b]
    // 0xa17a6c: r1 = Instance_ClampingScrollPhysics
    //     0xa17a6c: add             x1, PP, #0x28, lsl #12  ; [pp+0x28410] Obj!ClampingScrollPhysics@e0fd61
    //     0xa17a70: ldr             x1, [x1, #0x410]
    // 0xa17a74: StoreField: r0->field_1b = r1
    //     0xa17a74: stur            w1, [x0, #0x1b]
    // 0xa17a78: LeaveFrame
    //     0xa17a78: mov             SP, fp
    //     0xa17a7c: ldp             fp, lr, [SP], #0x10
    // 0xa17a80: ret
    //     0xa17a80: ret             
    // 0xa17a84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa17a84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa17a88: b               #0xa178d0
    // 0xa17a8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa17a8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa17a90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa17a90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa17a94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa17a94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa17a98: r9 = _outerController
    //     0xa17a98: ldr             x9, [PP, #0x7018]  ; [pp+0x7018] Field <_NestedScrollCoordinator@303016527._outerController@303016527>: late (offset: 0x18)
    // 0xa17a9c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa17a9c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa17aa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa17aa0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa17aa4: r9 = _innerController
    //     0xa17aa4: ldr             x9, [PP, #0x6f08]  ; [pp+0x6f08] Field <_NestedScrollCoordinator@303016527._innerController@303016527>: late (offset: 0x1c)
    // 0xa17aa8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa17aa8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa17aac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa17aac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa17ab0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa17ab0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa80270, size: 0x64
    // 0xa80270: EnterFrame
    //     0xa80270: stp             fp, lr, [SP, #-0x10]!
    //     0xa80274: mov             fp, SP
    // 0xa80278: AllocStack(0x8)
    //     0xa80278: sub             SP, SP, #8
    // 0xa8027c: SetupParameters(NestedScrollViewState this /* r1 => r0, fp-0x8 */)
    //     0xa8027c: mov             x0, x1
    //     0xa80280: stur            x1, [fp, #-8]
    // 0xa80284: CheckStackOverflow
    //     0xa80284: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa80288: cmp             SP, x16
    //     0xa8028c: b.ls            #0xa802c8
    // 0xa80290: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa80290: ldur            w1, [x0, #0x17]
    // 0xa80294: DecompressPointer r1
    //     0xa80294: add             x1, x1, HEAP, lsl #32
    // 0xa80298: cmp             w1, NULL
    // 0xa8029c: b.eq            #0xa802d0
    // 0xa802a0: r0 = dispose()
    //     0xa802a0: bl              #0xa802d4  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::dispose
    // 0xa802a4: ldur            x0, [fp, #-8]
    // 0xa802a8: ArrayStore: r0[0] = rNULL  ; List_4
    //     0xa802a8: stur            NULL, [x0, #0x17]
    // 0xa802ac: LoadField: r1 = r0->field_13
    //     0xa802ac: ldur            w1, [x0, #0x13]
    // 0xa802b0: DecompressPointer r1
    //     0xa802b0: add             x1, x1, HEAP, lsl #32
    // 0xa802b4: r0 = dispose()
    //     0xa802b4: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0xa802b8: r0 = Null
    //     0xa802b8: mov             x0, NULL
    // 0xa802bc: LeaveFrame
    //     0xa802bc: mov             SP, fp
    //     0xa802c0: ldp             fp, lr, [SP], #0x10
    // 0xa802c4: ret
    //     0xa802c4: ret             
    // 0xa802c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa802c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa802cc: b               #0xa80290
    // 0xa802d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa802d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4573, size: 0x38, field offset: 0x34
class NestedScrollViewViewport extends Viewport {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x858cf4, size: 0xa8
    // 0x858cf4: EnterFrame
    //     0x858cf4: stp             fp, lr, [SP, #-0x10]!
    //     0x858cf8: mov             fp, SP
    // 0x858cfc: AllocStack(0x28)
    //     0x858cfc: sub             SP, SP, #0x28
    // 0x858d00: SetupParameters(NestedScrollViewViewport this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1 */)
    //     0x858d00: mov             x0, x1
    //     0x858d04: stur            x1, [fp, #-0x10]
    //     0x858d08: mov             x1, x2
    // 0x858d0c: CheckStackOverflow
    //     0x858d0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x858d10: cmp             SP, x16
    //     0x858d14: b.ls            #0x858d94
    // 0x858d18: LoadField: r3 = r0->field_f
    //     0x858d18: ldur            w3, [x0, #0xf]
    // 0x858d1c: DecompressPointer r3
    //     0x858d1c: add             x3, x3, HEAP, lsl #32
    // 0x858d20: mov             x2, x3
    // 0x858d24: stur            x3, [fp, #-8]
    // 0x858d28: r0 = getDefaultCrossAxisDirection()
    //     0x858d28: bl              #0x8590cc  ; [package:flutter/src/widgets/viewport.dart] Viewport::getDefaultCrossAxisDirection
    // 0x858d2c: mov             x2, x0
    // 0x858d30: ldur            x0, [fp, #-0x10]
    // 0x858d34: stur            x2, [fp, #-0x28]
    // 0x858d38: LoadField: r5 = r0->field_1f
    //     0x858d38: ldur            w5, [x0, #0x1f]
    // 0x858d3c: DecompressPointer r5
    //     0x858d3c: add             x5, x5, HEAP, lsl #32
    // 0x858d40: stur            x5, [fp, #-0x20]
    // 0x858d44: LoadField: r3 = r0->field_33
    //     0x858d44: ldur            w3, [x0, #0x33]
    // 0x858d48: DecompressPointer r3
    //     0x858d48: add             x3, x3, HEAP, lsl #32
    // 0x858d4c: stur            x3, [fp, #-0x18]
    // 0x858d50: r1 = <SliverPhysicalContainerParentData>
    //     0x858d50: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4f220] TypeArguments: <SliverPhysicalContainerParentData>
    //     0x858d54: ldr             x1, [x1, #0x220]
    // 0x858d58: r0 = RenderNestedScrollViewViewport()
    //     0x858d58: bl              #0x8590c0  ; AllocateRenderNestedScrollViewViewportStub -> RenderNestedScrollViewViewport (size=0xac)
    // 0x858d5c: mov             x4, x0
    // 0x858d60: ldur            x0, [fp, #-0x18]
    // 0x858d64: stur            x4, [fp, #-0x10]
    // 0x858d68: StoreField: r4->field_a7 = r0
    //     0x858d68: stur            w0, [x4, #0xa7]
    // 0x858d6c: mov             x1, x4
    // 0x858d70: ldur            x2, [fp, #-8]
    // 0x858d74: ldur            x3, [fp, #-0x28]
    // 0x858d78: ldur            x5, [fp, #-0x20]
    // 0x858d7c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x858d7c: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x858d80: r0 = RenderViewport()
    //     0x858d80: bl              #0x858d9c  ; [package:flutter/src/rendering/viewport.dart] RenderViewport::RenderViewport
    // 0x858d84: ldur            x0, [fp, #-0x10]
    // 0x858d88: LeaveFrame
    //     0x858d88: mov             SP, fp
    //     0x858d8c: ldp             fp, lr, [SP], #0x10
    // 0x858d90: ret
    //     0x858d90: ret             
    // 0x858d94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x858d94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x858d98: b               #0x858d18
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xc6b5b4, size: 0xf8
    // 0xc6b5b4: EnterFrame
    //     0xc6b5b4: stp             fp, lr, [SP, #-0x10]!
    //     0xc6b5b8: mov             fp, SP
    // 0xc6b5bc: AllocStack(0x20)
    //     0xc6b5bc: sub             SP, SP, #0x20
    // 0xc6b5c0: SetupParameters(NestedScrollViewViewport this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xc6b5c0: mov             x5, x1
    //     0xc6b5c4: mov             x4, x2
    //     0xc6b5c8: stur            x1, [fp, #-8]
    //     0xc6b5cc: stur            x2, [fp, #-0x10]
    //     0xc6b5d0: stur            x3, [fp, #-0x18]
    // 0xc6b5d4: CheckStackOverflow
    //     0xc6b5d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6b5d8: cmp             SP, x16
    //     0xc6b5dc: b.ls            #0xc6b6a4
    // 0xc6b5e0: mov             x0, x3
    // 0xc6b5e4: r2 = Null
    //     0xc6b5e4: mov             x2, NULL
    // 0xc6b5e8: r1 = Null
    //     0xc6b5e8: mov             x1, NULL
    // 0xc6b5ec: r4 = 60
    //     0xc6b5ec: movz            x4, #0x3c
    // 0xc6b5f0: branchIfSmi(r0, 0xc6b5fc)
    //     0xc6b5f0: tbz             w0, #0, #0xc6b5fc
    // 0xc6b5f4: r4 = LoadClassIdInstr(r0)
    //     0xc6b5f4: ldur            x4, [x0, #-1]
    //     0xc6b5f8: ubfx            x4, x4, #0xc, #0x14
    // 0xc6b5fc: cmp             x4, #0xbd0
    // 0xc6b600: b.eq            #0xc6b618
    // 0xc6b604: r8 = RenderNestedScrollViewViewport
    //     0xc6b604: add             x8, PP, #0x56, lsl #12  ; [pp+0x56d00] Type: RenderNestedScrollViewViewport
    //     0xc6b608: ldr             x8, [x8, #0xd00]
    // 0xc6b60c: r3 = Null
    //     0xc6b60c: add             x3, PP, #0x56, lsl #12  ; [pp+0x56d08] Null
    //     0xc6b610: ldr             x3, [x3, #0xd08]
    // 0xc6b614: r0 = DefaultTypeTest()
    //     0xc6b614: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc6b618: ldur            x0, [fp, #-8]
    // 0xc6b61c: LoadField: r3 = r0->field_f
    //     0xc6b61c: ldur            w3, [x0, #0xf]
    // 0xc6b620: DecompressPointer r3
    //     0xc6b620: add             x3, x3, HEAP, lsl #32
    // 0xc6b624: ldur            x1, [fp, #-0x18]
    // 0xc6b628: mov             x2, x3
    // 0xc6b62c: stur            x3, [fp, #-0x20]
    // 0xc6b630: r0 = axisDirection=()
    //     0xc6b630: bl              #0xc6b9a0  ; [package:flutter/src/rendering/viewport.dart] RenderViewportBase::axisDirection=
    // 0xc6b634: ldur            x1, [fp, #-0x10]
    // 0xc6b638: ldur            x2, [fp, #-0x20]
    // 0xc6b63c: r0 = getDefaultCrossAxisDirection()
    //     0xc6b63c: bl              #0x8590cc  ; [package:flutter/src/widgets/viewport.dart] Viewport::getDefaultCrossAxisDirection
    // 0xc6b640: ldur            x1, [fp, #-0x18]
    // 0xc6b644: mov             x2, x0
    // 0xc6b648: r0 = crossAxisDirection=()
    //     0xc6b648: bl              #0xc6b8c4  ; [package:flutter/src/rendering/viewport.dart] RenderViewportBase::crossAxisDirection=
    // 0xc6b64c: ldur            x0, [fp, #-0x18]
    // 0xc6b650: LoadField: d0 = r0->field_8f
    //     0xc6b650: ldur            d0, [x0, #0x8f]
    // 0xc6b654: d1 = 0.000000
    //     0xc6b654: eor             v1.16b, v1.16b, v1.16b
    // 0xc6b658: fcmp            d1, d0
    // 0xc6b65c: b.eq            #0xc6b66c
    // 0xc6b660: StoreField: r0->field_8f = rZR
    //     0xc6b660: stur            xzr, [x0, #0x8f]
    // 0xc6b664: mov             x1, x0
    // 0xc6b668: r0 = markNeedsLayout()
    //     0xc6b668: bl              #0x803e98  ; [package:flutter/src/widgets/nested_scroll_view.dart] RenderNestedScrollViewViewport::markNeedsLayout
    // 0xc6b66c: ldur            x0, [fp, #-8]
    // 0xc6b670: LoadField: r2 = r0->field_1f
    //     0xc6b670: ldur            w2, [x0, #0x1f]
    // 0xc6b674: DecompressPointer r2
    //     0xc6b674: add             x2, x2, HEAP, lsl #32
    // 0xc6b678: ldur            x1, [fp, #-0x18]
    // 0xc6b67c: r0 = offset=()
    //     0xc6b67c: bl              #0xc6b728  ; [package:flutter/src/rendering/viewport.dart] RenderViewportBase::offset=
    // 0xc6b680: ldur            x0, [fp, #-8]
    // 0xc6b684: LoadField: r2 = r0->field_33
    //     0xc6b684: ldur            w2, [x0, #0x33]
    // 0xc6b688: DecompressPointer r2
    //     0xc6b688: add             x2, x2, HEAP, lsl #32
    // 0xc6b68c: ldur            x1, [fp, #-0x18]
    // 0xc6b690: r0 = handle=()
    //     0xc6b690: bl              #0xc6b6ac  ; [package:flutter/src/widgets/nested_scroll_view.dart] RenderNestedScrollViewViewport::handle=
    // 0xc6b694: r0 = Null
    //     0xc6b694: mov             x0, NULL
    // 0xc6b698: LeaveFrame
    //     0xc6b698: mov             SP, fp
    //     0xc6b69c: ldp             fp, lr, [SP], #0x10
    // 0xc6b6a0: ret
    //     0xc6b6a0: ret             
    // 0xc6b6a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6b6a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6b6a8: b               #0xc6b5e0
  }
}

// class id: 4627, size: 0x14, field offset: 0x10
//   const constructor, 
class _InheritedNestedScrollView extends InheritedWidget {

  _ updateShouldNotify(/* No info */) {
    // ** addr: 0xa89290, size: 0x8c
    // 0xa89290: EnterFrame
    //     0xa89290: stp             fp, lr, [SP, #-0x10]!
    //     0xa89294: mov             fp, SP
    // 0xa89298: AllocStack(0x10)
    //     0xa89298: sub             SP, SP, #0x10
    // 0xa8929c: SetupParameters(_InheritedNestedScrollView this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa8929c: mov             x0, x2
    //     0xa892a0: mov             x4, x1
    //     0xa892a4: mov             x3, x2
    //     0xa892a8: stur            x1, [fp, #-8]
    //     0xa892ac: stur            x2, [fp, #-0x10]
    // 0xa892b0: r2 = Null
    //     0xa892b0: mov             x2, NULL
    // 0xa892b4: r1 = Null
    //     0xa892b4: mov             x1, NULL
    // 0xa892b8: r4 = 60
    //     0xa892b8: movz            x4, #0x3c
    // 0xa892bc: branchIfSmi(r0, 0xa892c8)
    //     0xa892bc: tbz             w0, #0, #0xa892c8
    // 0xa892c0: r4 = LoadClassIdInstr(r0)
    //     0xa892c0: ldur            x4, [x0, #-1]
    //     0xa892c4: ubfx            x4, x4, #0xc, #0x14
    // 0xa892c8: r17 = 4627
    //     0xa892c8: movz            x17, #0x1213
    // 0xa892cc: cmp             x4, x17
    // 0xa892d0: b.eq            #0xa892e8
    // 0xa892d4: r8 = _InheritedNestedScrollView
    //     0xa892d4: add             x8, PP, #0x50, lsl #12  ; [pp+0x50030] Type: _InheritedNestedScrollView
    //     0xa892d8: ldr             x8, [x8, #0x30]
    // 0xa892dc: r3 = Null
    //     0xa892dc: add             x3, PP, #0x50, lsl #12  ; [pp+0x50038] Null
    //     0xa892e0: ldr             x3, [x3, #0x38]
    // 0xa892e4: r0 = DefaultTypeTest()
    //     0xa892e4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xa892e8: ldur            x1, [fp, #-8]
    // 0xa892ec: LoadField: r2 = r1->field_f
    //     0xa892ec: ldur            w2, [x1, #0xf]
    // 0xa892f0: DecompressPointer r2
    //     0xa892f0: add             x2, x2, HEAP, lsl #32
    // 0xa892f4: ldur            x1, [fp, #-0x10]
    // 0xa892f8: LoadField: r3 = r1->field_f
    //     0xa892f8: ldur            w3, [x1, #0xf]
    // 0xa892fc: DecompressPointer r3
    //     0xa892fc: add             x3, x3, HEAP, lsl #32
    // 0xa89300: cmp             w2, w3
    // 0xa89304: r16 = true
    //     0xa89304: add             x16, NULL, #0x20  ; true
    // 0xa89308: r17 = false
    //     0xa89308: add             x17, NULL, #0x30  ; false
    // 0xa8930c: csel            x0, x16, x17, ne
    // 0xa89310: LeaveFrame
    //     0xa89310: mov             SP, fp
    //     0xa89314: ldp             fp, lr, [SP], #0x10
    // 0xa89318: ret
    //     0xa89318: ret             
  }
}

// class id: 4776, size: 0x3c, field offset: 0xc
//   const constructor, 
class NestedScrollView extends StatefulWidget {

  _ _buildSlivers(/* No info */) {
    // ** addr: 0xa17ac0, size: 0x150
    // 0xa17ac0: EnterFrame
    //     0xa17ac0: stp             fp, lr, [SP, #-0x10]!
    //     0xa17ac4: mov             fp, SP
    // 0xa17ac8: AllocStack(0x48)
    //     0xa17ac8: sub             SP, SP, #0x48
    // 0xa17acc: SetupParameters(NestedScrollView this /* r1 => r1, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xa17acc: stur            x1, [fp, #-8]
    //     0xa17ad0: stur            x3, [fp, #-0x10]
    // 0xa17ad4: CheckStackOverflow
    //     0xa17ad4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa17ad8: cmp             SP, x16
    //     0xa17adc: b.ls            #0xa17c08
    // 0xa17ae0: LoadField: r0 = r1->field_1b
    //     0xa17ae0: ldur            w0, [x1, #0x1b]
    // 0xa17ae4: DecompressPointer r0
    //     0xa17ae4: add             x0, x0, HEAP, lsl #32
    // 0xa17ae8: stp             x2, x0, [SP, #8]
    // 0xa17aec: str             x5, [SP]
    // 0xa17af0: ClosureCall
    //     0xa17af0: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xa17af4: ldur            x2, [x0, #0x1f]
    //     0xa17af8: blr             x2
    // 0xa17afc: mov             x2, x0
    // 0xa17b00: r1 = <Widget>
    //     0xa17b00: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa17b04: r0 = _GrowableList.of()
    //     0xa17b04: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xa17b08: r1 = const [Instance of 'TargetPlatform', Instance of 'TargetPlatform', Instance of 'TargetPlatform', Instance of 'TargetPlatform', Instance of 'TargetPlatform', Instance of 'TargetPlatform']
    //     0xa17b08: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d1d0] List<TargetPlatform>(6)
    //     0xa17b0c: ldr             x1, [x1, #0x1d0]
    // 0xa17b10: stur            x0, [fp, #-0x18]
    // 0xa17b14: r0 = toSet()
    //     0xa17b14: bl              #0x863ce4  ; [dart:collection] ListBase::toSet
    // 0xa17b18: mov             x1, x0
    // 0xa17b1c: ldur            x0, [fp, #-8]
    // 0xa17b20: stur            x1, [fp, #-0x28]
    // 0xa17b24: LoadField: r2 = r0->field_1f
    //     0xa17b24: ldur            w2, [x0, #0x1f]
    // 0xa17b28: DecompressPointer r2
    //     0xa17b28: add             x2, x2, HEAP, lsl #32
    // 0xa17b2c: stur            x2, [fp, #-0x20]
    // 0xa17b30: r0 = PrimaryScrollController()
    //     0xa17b30: bl              #0xa17c1c  ; AllocatePrimaryScrollControllerStub -> PrimaryScrollController (size=0x1c)
    // 0xa17b34: mov             x1, x0
    // 0xa17b38: ldur            x0, [fp, #-0x10]
    // 0xa17b3c: stur            x1, [fp, #-8]
    // 0xa17b40: StoreField: r1->field_f = r0
    //     0xa17b40: stur            w0, [x1, #0xf]
    // 0xa17b44: ldur            x0, [fp, #-0x28]
    // 0xa17b48: ArrayStore: r1[0] = r0  ; List_4
    //     0xa17b48: stur            w0, [x1, #0x17]
    // 0xa17b4c: r0 = Instance_Axis
    //     0xa17b4c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa17b50: StoreField: r1->field_13 = r0
    //     0xa17b50: stur            w0, [x1, #0x13]
    // 0xa17b54: ldur            x0, [fp, #-0x20]
    // 0xa17b58: StoreField: r1->field_b = r0
    //     0xa17b58: stur            w0, [x1, #0xb]
    // 0xa17b5c: r0 = SliverFillRemaining()
    //     0xa17b5c: bl              #0xa17c10  ; AllocateSliverFillRemainingStub -> SliverFillRemaining (size=0x18)
    // 0xa17b60: mov             x2, x0
    // 0xa17b64: ldur            x0, [fp, #-8]
    // 0xa17b68: stur            x2, [fp, #-0x10]
    // 0xa17b6c: StoreField: r2->field_b = r0
    //     0xa17b6c: stur            w0, [x2, #0xb]
    // 0xa17b70: r0 = true
    //     0xa17b70: add             x0, NULL, #0x20  ; true
    // 0xa17b74: StoreField: r2->field_f = r0
    //     0xa17b74: stur            w0, [x2, #0xf]
    // 0xa17b78: r0 = false
    //     0xa17b78: add             x0, NULL, #0x30  ; false
    // 0xa17b7c: StoreField: r2->field_13 = r0
    //     0xa17b7c: stur            w0, [x2, #0x13]
    // 0xa17b80: ldur            x0, [fp, #-0x18]
    // 0xa17b84: LoadField: r1 = r0->field_b
    //     0xa17b84: ldur            w1, [x0, #0xb]
    // 0xa17b88: LoadField: r3 = r0->field_f
    //     0xa17b88: ldur            w3, [x0, #0xf]
    // 0xa17b8c: DecompressPointer r3
    //     0xa17b8c: add             x3, x3, HEAP, lsl #32
    // 0xa17b90: LoadField: r4 = r3->field_b
    //     0xa17b90: ldur            w4, [x3, #0xb]
    // 0xa17b94: r3 = LoadInt32Instr(r1)
    //     0xa17b94: sbfx            x3, x1, #1, #0x1f
    // 0xa17b98: stur            x3, [fp, #-0x30]
    // 0xa17b9c: r1 = LoadInt32Instr(r4)
    //     0xa17b9c: sbfx            x1, x4, #1, #0x1f
    // 0xa17ba0: cmp             x3, x1
    // 0xa17ba4: b.ne            #0xa17bb0
    // 0xa17ba8: mov             x1, x0
    // 0xa17bac: r0 = _growToNextCapacity()
    //     0xa17bac: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa17bb0: ldur            x2, [fp, #-0x18]
    // 0xa17bb4: ldur            x3, [fp, #-0x30]
    // 0xa17bb8: add             x4, x3, #1
    // 0xa17bbc: lsl             x5, x4, #1
    // 0xa17bc0: StoreField: r2->field_b = r5
    //     0xa17bc0: stur            w5, [x2, #0xb]
    // 0xa17bc4: LoadField: r1 = r2->field_f
    //     0xa17bc4: ldur            w1, [x2, #0xf]
    // 0xa17bc8: DecompressPointer r1
    //     0xa17bc8: add             x1, x1, HEAP, lsl #32
    // 0xa17bcc: ldur            x0, [fp, #-0x10]
    // 0xa17bd0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa17bd0: add             x25, x1, x3, lsl #2
    //     0xa17bd4: add             x25, x25, #0xf
    //     0xa17bd8: str             w0, [x25]
    //     0xa17bdc: tbz             w0, #0, #0xa17bf8
    //     0xa17be0: ldurb           w16, [x1, #-1]
    //     0xa17be4: ldurb           w17, [x0, #-1]
    //     0xa17be8: and             x16, x17, x16, lsr #2
    //     0xa17bec: tst             x16, HEAP, lsr #32
    //     0xa17bf0: b.eq            #0xa17bf8
    //     0xa17bf4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa17bf8: mov             x0, x2
    // 0xa17bfc: LeaveFrame
    //     0xa17bfc: mov             SP, fp
    //     0xa17c00: ldp             fp, lr, [SP], #0x10
    // 0xa17c04: ret
    //     0xa17c04: ret             
    // 0xa17c08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa17c08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa17c0c: b               #0xa17ae0
  }
  _ createState(/* No info */) {
    // ** addr: 0xa925d0, size: 0x7c
    // 0xa925d0: EnterFrame
    //     0xa925d0: stp             fp, lr, [SP, #-0x10]!
    //     0xa925d4: mov             fp, SP
    // 0xa925d8: AllocStack(0x8)
    //     0xa925d8: sub             SP, SP, #8
    // 0xa925dc: CheckStackOverflow
    //     0xa925dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa925e0: cmp             SP, x16
    //     0xa925e4: b.ls            #0xa92644
    // 0xa925e8: r0 = SliverOverlapAbsorberHandle()
    //     0xa925e8: bl              #0xa92658  ; AllocateSliverOverlapAbsorberHandleStub -> SliverOverlapAbsorberHandle (size=0x24)
    // 0xa925ec: stur            x0, [fp, #-8]
    // 0xa925f0: StoreField: r0->field_7 = rZR
    //     0xa925f0: stur            xzr, [x0, #7]
    // 0xa925f4: StoreField: r0->field_13 = rZR
    //     0xa925f4: stur            xzr, [x0, #0x13]
    // 0xa925f8: StoreField: r0->field_1b = rZR
    //     0xa925f8: stur            xzr, [x0, #0x1b]
    // 0xa925fc: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0xa925fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa92600: ldr             x0, [x0, #0xca8]
    //     0xa92604: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa92608: cmp             w0, w16
    //     0xa9260c: b.ne            #0xa92618
    //     0xa92610: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0xa92614: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa92618: mov             x1, x0
    // 0xa9261c: ldur            x0, [fp, #-8]
    // 0xa92620: StoreField: r0->field_f = r1
    //     0xa92620: stur            w1, [x0, #0xf]
    // 0xa92624: r1 = <NestedScrollView>
    //     0xa92624: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a038] TypeArguments: <NestedScrollView>
    //     0xa92628: ldr             x1, [x1, #0x38]
    // 0xa9262c: r0 = NestedScrollViewState()
    //     0xa9262c: bl              #0xa9264c  ; AllocateNestedScrollViewStateStub -> NestedScrollViewState (size=0x20)
    // 0xa92630: ldur            x1, [fp, #-8]
    // 0xa92634: StoreField: r0->field_13 = r1
    //     0xa92634: stur            w1, [x0, #0x13]
    // 0xa92638: LeaveFrame
    //     0xa92638: mov             SP, fp
    //     0xa9263c: ldp             fp, lr, [SP], #0x10
    // 0xa92640: ret
    //     0xa92640: ret             
    // 0xa92644: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa92644: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa92648: b               #0xa925e8
  }
}

// class id: 5344, size: 0x58, field offset: 0x54
//   const constructor, 
class _NestedScrollViewCustomScrollView extends CustomScrollView {

  _ buildViewport(/* No info */) {
    // ** addr: 0xcf2cd4, size: 0x70
    // 0xcf2cd4: EnterFrame
    //     0xcf2cd4: stp             fp, lr, [SP, #-0x10]!
    //     0xcf2cd8: mov             fp, SP
    // 0xcf2cdc: AllocStack(0x20)
    //     0xcf2cdc: sub             SP, SP, #0x20
    // 0xcf2ce0: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xcf2ce0: stur            x2, [fp, #-0x10]
    //     0xcf2ce4: stur            x3, [fp, #-0x18]
    //     0xcf2ce8: stur            x5, [fp, #-0x20]
    // 0xcf2cec: LoadField: r0 = r1->field_53
    //     0xcf2cec: ldur            w0, [x1, #0x53]
    // 0xcf2cf0: DecompressPointer r0
    //     0xcf2cf0: add             x0, x0, HEAP, lsl #32
    // 0xcf2cf4: stur            x0, [fp, #-8]
    // 0xcf2cf8: r0 = NestedScrollViewViewport()
    //     0xcf2cf8: bl              #0xcf2d44  ; AllocateNestedScrollViewViewportStub -> NestedScrollViewViewport (size=0x38)
    // 0xcf2cfc: ldur            x1, [fp, #-8]
    // 0xcf2d00: StoreField: r0->field_33 = r1
    //     0xcf2d00: stur            w1, [x0, #0x33]
    // 0xcf2d04: ldur            x1, [fp, #-0x18]
    // 0xcf2d08: StoreField: r0->field_f = r1
    //     0xcf2d08: stur            w1, [x0, #0xf]
    // 0xcf2d0c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xcf2d0c: stur            xzr, [x0, #0x17]
    // 0xcf2d10: ldur            x1, [fp, #-0x10]
    // 0xcf2d14: StoreField: r0->field_1f = r1
    //     0xcf2d14: stur            w1, [x0, #0x1f]
    // 0xcf2d18: r1 = Instance_CacheExtentStyle
    //     0xcf2d18: add             x1, PP, #0x45, lsl #12  ; [pp+0x45df8] Obj!CacheExtentStyle@e35341
    //     0xcf2d1c: ldr             x1, [x1, #0xdf8]
    // 0xcf2d20: StoreField: r0->field_2b = r1
    //     0xcf2d20: stur            w1, [x0, #0x2b]
    // 0xcf2d24: r1 = Instance_Clip
    //     0xcf2d24: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xcf2d28: ldr             x1, [x1, #0x7c0]
    // 0xcf2d2c: StoreField: r0->field_2f = r1
    //     0xcf2d2c: stur            w1, [x0, #0x2f]
    // 0xcf2d30: ldur            x1, [fp, #-0x20]
    // 0xcf2d34: StoreField: r0->field_b = r1
    //     0xcf2d34: stur            w1, [x0, #0xb]
    // 0xcf2d38: LeaveFrame
    //     0xcf2d38: mov             SP, fp
    //     0xcf2d3c: ldp             fp, lr, [SP], #0x10
    // 0xcf2d40: ret
    //     0xcf2d40: ret             
  }
}

// class id: 6953, size: 0x14, field offset: 0x14
enum _NestedBallisticScrollActivityMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4ad28, size: 0x64
    // 0xc4ad28: EnterFrame
    //     0xc4ad28: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ad2c: mov             fp, SP
    // 0xc4ad30: AllocStack(0x10)
    //     0xc4ad30: sub             SP, SP, #0x10
    // 0xc4ad34: SetupParameters(_NestedBallisticScrollActivityMode this /* r1 => r0, fp-0x8 */)
    //     0xc4ad34: mov             x0, x1
    //     0xc4ad38: stur            x1, [fp, #-8]
    // 0xc4ad3c: CheckStackOverflow
    //     0xc4ad3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ad40: cmp             SP, x16
    //     0xc4ad44: b.ls            #0xc4ad84
    // 0xc4ad48: r1 = Null
    //     0xc4ad48: mov             x1, NULL
    // 0xc4ad4c: r2 = 4
    //     0xc4ad4c: movz            x2, #0x4
    // 0xc4ad50: r0 = AllocateArray()
    //     0xc4ad50: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ad54: r16 = "_NestedBallisticScrollActivityMode."
    //     0xc4ad54: add             x16, PP, #0x22, lsl #12  ; [pp+0x22380] "_NestedBallisticScrollActivityMode."
    //     0xc4ad58: ldr             x16, [x16, #0x380]
    // 0xc4ad5c: StoreField: r0->field_f = r16
    //     0xc4ad5c: stur            w16, [x0, #0xf]
    // 0xc4ad60: ldur            x1, [fp, #-8]
    // 0xc4ad64: LoadField: r2 = r1->field_f
    //     0xc4ad64: ldur            w2, [x1, #0xf]
    // 0xc4ad68: DecompressPointer r2
    //     0xc4ad68: add             x2, x2, HEAP, lsl #32
    // 0xc4ad6c: StoreField: r0->field_13 = r2
    //     0xc4ad6c: stur            w2, [x0, #0x13]
    // 0xc4ad70: str             x0, [SP]
    // 0xc4ad74: r0 = _interpolate()
    //     0xc4ad74: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4ad78: LeaveFrame
    //     0xc4ad78: mov             SP, fp
    //     0xc4ad7c: ldp             fp, lr, [SP], #0x10
    // 0xc4ad80: ret
    //     0xc4ad80: ret             
    // 0xc4ad84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ad84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4ad88: b               #0xc4ad48
  }
}
