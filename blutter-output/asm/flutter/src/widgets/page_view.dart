// lib: , url: package:flutter/src/widgets/page_view.dart

// class id: 1049159, size: 0x8
class :: {
}

// class id: 2579, size: 0x2c, field offset: 0x24
class PageMetrics extends FixedScrollMetrics {

  get _ page(/* No info */) {
    // ** addr: 0xa1ba9c, size: 0x168
    // 0xa1ba9c: EnterFrame
    //     0xa1ba9c: stp             fp, lr, [SP, #-0x10]!
    //     0xa1baa0: mov             fp, SP
    // 0xa1baa4: LoadField: r2 = r1->field_f
    //     0xa1baa4: ldur            w2, [x1, #0xf]
    // 0xa1baa8: DecompressPointer r2
    //     0xa1baa8: add             x2, x2, HEAP, lsl #32
    // 0xa1baac: cmp             w2, NULL
    // 0xa1bab0: b.eq            #0xa1bbe4
    // 0xa1bab4: LoadField: r3 = r1->field_7
    //     0xa1bab4: ldur            w3, [x1, #7]
    // 0xa1bab8: DecompressPointer r3
    //     0xa1bab8: add             x3, x3, HEAP, lsl #32
    // 0xa1babc: cmp             w3, NULL
    // 0xa1bac0: b.eq            #0xa1bbe8
    // 0xa1bac4: LoadField: r4 = r1->field_b
    //     0xa1bac4: ldur            w4, [x1, #0xb]
    // 0xa1bac8: DecompressPointer r4
    //     0xa1bac8: add             x4, x4, HEAP, lsl #32
    // 0xa1bacc: cmp             w4, NULL
    // 0xa1bad0: b.eq            #0xa1bbec
    // 0xa1bad4: LoadField: d0 = r2->field_7
    //     0xa1bad4: ldur            d0, [x2, #7]
    // 0xa1bad8: LoadField: d1 = r3->field_7
    //     0xa1bad8: ldur            d1, [x3, #7]
    // 0xa1badc: fcmp            d1, d0
    // 0xa1bae0: b.gt            #0xa1bb00
    // 0xa1bae4: LoadField: d1 = r4->field_7
    //     0xa1bae4: ldur            d1, [x4, #7]
    // 0xa1bae8: fcmp            d0, d1
    // 0xa1baec: b.gt            #0xa1bb00
    // 0xa1baf0: LoadField: d2 = r2->field_7
    //     0xa1baf0: ldur            d2, [x2, #7]
    // 0xa1baf4: fcmp            d2, d2
    // 0xa1baf8: b.vs            #0xa1bb00
    // 0xa1bafc: mov             v1.16b, v0.16b
    // 0xa1bb00: d0 = 0.000000
    //     0xa1bb00: eor             v0.16b, v0.16b, v0.16b
    // 0xa1bb04: fcmp            d0, d1
    // 0xa1bb08: b.le            #0xa1bb14
    // 0xa1bb0c: d2 = 0.000000
    //     0xa1bb0c: eor             v2.16b, v2.16b, v2.16b
    // 0xa1bb10: b               #0xa1bb48
    // 0xa1bb14: fcmp            d1, d0
    // 0xa1bb18: b.le            #0xa1bb24
    // 0xa1bb1c: mov             v2.16b, v1.16b
    // 0xa1bb20: b               #0xa1bb48
    // 0xa1bb24: fcmp            d0, d0
    // 0xa1bb28: b.ne            #0xa1bb34
    // 0xa1bb2c: fadd            d2, d1, d0
    // 0xa1bb30: b               #0xa1bb48
    // 0xa1bb34: fcmp            d1, d1
    // 0xa1bb38: b.vc            #0xa1bb44
    // 0xa1bb3c: mov             v2.16b, v1.16b
    // 0xa1bb40: b               #0xa1bb48
    // 0xa1bb44: d2 = 0.000000
    //     0xa1bb44: eor             v2.16b, v2.16b, v2.16b
    // 0xa1bb48: d1 = 1.000000
    //     0xa1bb48: fmov            d1, #1.00000000
    // 0xa1bb4c: LoadField: r2 = r1->field_13
    //     0xa1bb4c: ldur            w2, [x1, #0x13]
    // 0xa1bb50: DecompressPointer r2
    //     0xa1bb50: add             x2, x2, HEAP, lsl #32
    // 0xa1bb54: cmp             w2, NULL
    // 0xa1bb58: b.eq            #0xa1bbf0
    // 0xa1bb5c: LoadField: d3 = r1->field_23
    //     0xa1bb5c: ldur            d3, [x1, #0x23]
    // 0xa1bb60: LoadField: d4 = r2->field_7
    //     0xa1bb60: ldur            d4, [x2, #7]
    // 0xa1bb64: fmul            d5, d4, d3
    // 0xa1bb68: fcmp            d1, d5
    // 0xa1bb6c: b.le            #0xa1bb78
    // 0xa1bb70: d0 = 1.000000
    //     0xa1bb70: fmov            d0, #1.00000000
    // 0xa1bb74: b               #0xa1bbac
    // 0xa1bb78: fcmp            d5, d1
    // 0xa1bb7c: b.le            #0xa1bb88
    // 0xa1bb80: mov             v0.16b, v5.16b
    // 0xa1bb84: b               #0xa1bbac
    // 0xa1bb88: fcmp            d1, d0
    // 0xa1bb8c: b.ne            #0xa1bb98
    // 0xa1bb90: fadd            d0, d5, d1
    // 0xa1bb94: b               #0xa1bbac
    // 0xa1bb98: fcmp            d5, d5
    // 0xa1bb9c: b.vc            #0xa1bba8
    // 0xa1bba0: mov             v0.16b, v5.16b
    // 0xa1bba4: b               #0xa1bbac
    // 0xa1bba8: d0 = 1.000000
    //     0xa1bba8: fmov            d0, #1.00000000
    // 0xa1bbac: fdiv            d1, d2, d0
    // 0xa1bbb0: r0 = inline_Allocate_Double()
    //     0xa1bbb0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa1bbb4: add             x0, x0, #0x10
    //     0xa1bbb8: cmp             x1, x0
    //     0xa1bbbc: b.ls            #0xa1bbf4
    //     0xa1bbc0: str             x0, [THR, #0x50]  ; THR::top
    //     0xa1bbc4: sub             x0, x0, #0xf
    //     0xa1bbc8: movz            x1, #0xe15c
    //     0xa1bbcc: movk            x1, #0x3, lsl #16
    //     0xa1bbd0: stur            x1, [x0, #-1]
    // 0xa1bbd4: StoreField: r0->field_7 = d1
    //     0xa1bbd4: stur            d1, [x0, #7]
    // 0xa1bbd8: LeaveFrame
    //     0xa1bbd8: mov             SP, fp
    //     0xa1bbdc: ldp             fp, lr, [SP], #0x10
    // 0xa1bbe0: ret
    //     0xa1bbe0: ret             
    // 0xa1bbe4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1bbe4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1bbe8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1bbe8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1bbec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1bbec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1bbf0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa1bbf0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa1bbf4: SaveReg d1
    //     0xa1bbf4: str             q1, [SP, #-0x10]!
    // 0xa1bbf8: r0 = AllocateDouble()
    //     0xa1bbf8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa1bbfc: RestoreReg d1
    //     0xa1bbfc: ldr             q1, [SP], #0x10
    // 0xa1bc00: b               #0xa1bbd4
  }
}

// class id: 2620, size: 0xc, field offset: 0xc
//   const constructor, 
class PageScrollPhysics extends ScrollPhysics {

  _ applyTo(/* No info */) {
    // ** addr: 0xda3054, size: 0x40
    // 0xda3054: EnterFrame
    //     0xda3054: stp             fp, lr, [SP, #-0x10]!
    //     0xda3058: mov             fp, SP
    // 0xda305c: AllocStack(0x8)
    //     0xda305c: sub             SP, SP, #8
    // 0xda3060: CheckStackOverflow
    //     0xda3060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xda3064: cmp             SP, x16
    //     0xda3068: b.ls            #0xda308c
    // 0xda306c: r0 = buildParent()
    //     0xda306c: bl              #0xda2fcc  ; [package:flutter/src/widgets/scroll_physics.dart] ScrollPhysics::buildParent
    // 0xda3070: stur            x0, [fp, #-8]
    // 0xda3074: r0 = PageScrollPhysics()
    //     0xda3074: bl              #0xda3094  ; AllocatePageScrollPhysicsStub -> PageScrollPhysics (size=0xc)
    // 0xda3078: ldur            x1, [fp, #-8]
    // 0xda307c: StoreField: r0->field_7 = r1
    //     0xda307c: stur            w1, [x0, #7]
    // 0xda3080: LeaveFrame
    //     0xda3080: mov             SP, fp
    //     0xda3084: ldp             fp, lr, [SP], #0x10
    // 0xda3088: ret
    //     0xda3088: ret             
    // 0xda308c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xda308c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xda3090: b               #0xda306c
  }
  _ createBallisticSimulation(/* No info */) {
    // ** addr: 0xdb9754, size: 0x1d0
    // 0xdb9754: EnterFrame
    //     0xdb9754: stp             fp, lr, [SP, #-0x10]!
    //     0xdb9758: mov             fp, SP
    // 0xdb975c: AllocStack(0x38)
    //     0xdb975c: sub             SP, SP, #0x38
    // 0xdb9760: d1 = 0.000000
    //     0xdb9760: eor             v1.16b, v1.16b, v1.16b
    // 0xdb9764: mov             x3, x1
    // 0xdb9768: stur            x1, [fp, #-8]
    // 0xdb976c: stur            x2, [fp, #-0x10]
    // 0xdb9770: stur            d0, [fp, #-0x20]
    // 0xdb9774: CheckStackOverflow
    //     0xdb9774: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb9778: cmp             SP, x16
    //     0xdb977c: b.ls            #0xdb991c
    // 0xdb9780: fcmp            d1, d0
    // 0xdb9784: b.lt            #0xdb97d0
    // 0xdb9788: r0 = LoadClassIdInstr(r2)
    //     0xdb9788: ldur            x0, [x2, #-1]
    //     0xdb978c: ubfx            x0, x0, #0xc, #0x14
    // 0xdb9790: mov             x1, x2
    // 0xdb9794: r0 = GDT[cid_x0 + -0xfed]()
    //     0xdb9794: sub             lr, x0, #0xfed
    //     0xdb9798: ldr             lr, [x21, lr, lsl #3]
    //     0xdb979c: blr             lr
    // 0xdb97a0: ldur            x2, [fp, #-0x10]
    // 0xdb97a4: stur            d0, [fp, #-0x28]
    // 0xdb97a8: r0 = LoadClassIdInstr(r2)
    //     0xdb97a8: ldur            x0, [x2, #-1]
    //     0xdb97ac: ubfx            x0, x0, #0xc, #0x14
    // 0xdb97b0: mov             x1, x2
    // 0xdb97b4: r0 = GDT[cid_x0 + -0xf92]()
    //     0xdb97b4: sub             lr, x0, #0xf92
    //     0xdb97b8: ldr             lr, [x21, lr, lsl #3]
    //     0xdb97bc: blr             lr
    // 0xdb97c0: mov             v1.16b, v0.16b
    // 0xdb97c4: ldur            d0, [fp, #-0x28]
    // 0xdb97c8: fcmp            d1, d0
    // 0xdb97cc: b.ge            #0xdb982c
    // 0xdb97d0: ldur            d1, [fp, #-0x20]
    // 0xdb97d4: d0 = 0.000000
    //     0xdb97d4: eor             v0.16b, v0.16b, v0.16b
    // 0xdb97d8: fcmp            d1, d0
    // 0xdb97dc: b.lt            #0xdb9848
    // 0xdb97e0: ldur            x2, [fp, #-0x10]
    // 0xdb97e4: r0 = LoadClassIdInstr(r2)
    //     0xdb97e4: ldur            x0, [x2, #-1]
    //     0xdb97e8: ubfx            x0, x0, #0xc, #0x14
    // 0xdb97ec: mov             x1, x2
    // 0xdb97f0: r0 = GDT[cid_x0 + -0xfed]()
    //     0xdb97f0: sub             lr, x0, #0xfed
    //     0xdb97f4: ldr             lr, [x21, lr, lsl #3]
    //     0xdb97f8: blr             lr
    // 0xdb97fc: ldur            x2, [fp, #-0x10]
    // 0xdb9800: stur            d0, [fp, #-0x28]
    // 0xdb9804: r0 = LoadClassIdInstr(r2)
    //     0xdb9804: ldur            x0, [x2, #-1]
    //     0xdb9808: ubfx            x0, x0, #0xc, #0x14
    // 0xdb980c: mov             x1, x2
    // 0xdb9810: r0 = GDT[cid_x0 + -0xf96]()
    //     0xdb9810: sub             lr, x0, #0xf96
    //     0xdb9814: ldr             lr, [x21, lr, lsl #3]
    //     0xdb9818: blr             lr
    // 0xdb981c: mov             v1.16b, v0.16b
    // 0xdb9820: ldur            d0, [fp, #-0x28]
    // 0xdb9824: fcmp            d0, d1
    // 0xdb9828: b.lt            #0xdb9848
    // 0xdb982c: ldur            x1, [fp, #-8]
    // 0xdb9830: ldur            x2, [fp, #-0x10]
    // 0xdb9834: ldur            d0, [fp, #-0x20]
    // 0xdb9838: r0 = createBallisticSimulation()
    //     0xdb9838: bl              #0xdba2c0  ; [package:flutter/src/widgets/scroll_physics.dart] ScrollPhysics::createBallisticSimulation
    // 0xdb983c: LeaveFrame
    //     0xdb983c: mov             SP, fp
    //     0xdb9840: ldp             fp, lr, [SP], #0x10
    // 0xdb9844: ret
    //     0xdb9844: ret             
    // 0xdb9848: ldur            x0, [fp, #-0x10]
    // 0xdb984c: ldur            x1, [fp, #-8]
    // 0xdb9850: mov             x2, x0
    // 0xdb9854: r0 = toleranceFor()
    //     0xdb9854: bl              #0xd88000  ; [package:flutter/src/widgets/scroll_physics.dart] ScrollPhysics::toleranceFor
    // 0xdb9858: ldur            x1, [fp, #-8]
    // 0xdb985c: ldur            x2, [fp, #-0x10]
    // 0xdb9860: mov             x3, x0
    // 0xdb9864: ldur            d0, [fp, #-0x20]
    // 0xdb9868: stur            x0, [fp, #-0x18]
    // 0xdb986c: r0 = _getTargetPixels()
    //     0xdb986c: bl              #0xdb9930  ; [package:flutter/src/widgets/page_view.dart] PageScrollPhysics::_getTargetPixels
    // 0xdb9870: ldur            x2, [fp, #-0x10]
    // 0xdb9874: stur            d0, [fp, #-0x28]
    // 0xdb9878: r0 = LoadClassIdInstr(r2)
    //     0xdb9878: ldur            x0, [x2, #-1]
    //     0xdb987c: ubfx            x0, x0, #0xc, #0x14
    // 0xdb9880: mov             x1, x2
    // 0xdb9884: r0 = GDT[cid_x0 + -0xfed]()
    //     0xdb9884: sub             lr, x0, #0xfed
    //     0xdb9888: ldr             lr, [x21, lr, lsl #3]
    //     0xdb988c: blr             lr
    // 0xdb9890: ldur            d1, [fp, #-0x28]
    // 0xdb9894: fcmp            d1, d0
    // 0xdb9898: b.eq            #0xdb990c
    // 0xdb989c: ldur            x0, [fp, #-0x10]
    // 0xdb98a0: ldur            x1, [fp, #-8]
    // 0xdb98a4: r0 = spring()
    //     0xdb98a4: bl              #0xc401dc  ; [package:flutter/src/widgets/scroll_physics.dart] ScrollPhysics::spring
    // 0xdb98a8: mov             x2, x0
    // 0xdb98ac: ldur            x1, [fp, #-0x10]
    // 0xdb98b0: stur            x2, [fp, #-8]
    // 0xdb98b4: r0 = LoadClassIdInstr(r1)
    //     0xdb98b4: ldur            x0, [x1, #-1]
    //     0xdb98b8: ubfx            x0, x0, #0xc, #0x14
    // 0xdb98bc: r0 = GDT[cid_x0 + -0xfed]()
    //     0xdb98bc: sub             lr, x0, #0xfed
    //     0xdb98c0: ldr             lr, [x21, lr, lsl #3]
    //     0xdb98c4: blr             lr
    // 0xdb98c8: stur            d0, [fp, #-0x30]
    // 0xdb98cc: r0 = ScrollSpringSimulation()
    //     0xdb98cc: bl              #0xdb9924  ; AllocateScrollSpringSimulationStub -> ScrollSpringSimulation (size=0x18)
    // 0xdb98d0: stur            x0, [fp, #-0x10]
    // 0xdb98d4: ldur            x16, [fp, #-0x18]
    // 0xdb98d8: str             x16, [SP]
    // 0xdb98dc: mov             x1, x0
    // 0xdb98e0: ldur            x2, [fp, #-8]
    // 0xdb98e4: ldur            d0, [fp, #-0x30]
    // 0xdb98e8: ldur            d1, [fp, #-0x28]
    // 0xdb98ec: ldur            d2, [fp, #-0x20]
    // 0xdb98f0: r4 = const [0, 0x6, 0x1, 0x5, tolerance, 0x5, null]
    //     0xdb98f0: add             x4, PP, #0x39, lsl #12  ; [pp+0x39ef8] List(7) [0, 0x6, 0x1, 0x5, "tolerance", 0x5, Null]
    //     0xdb98f4: ldr             x4, [x4, #0xef8]
    // 0xdb98f8: r0 = SpringSimulation()
    //     0xdb98f8: bl              #0x930ce0  ; [package:flutter/src/physics/spring_simulation.dart] SpringSimulation::SpringSimulation
    // 0xdb98fc: ldur            x0, [fp, #-0x10]
    // 0xdb9900: LeaveFrame
    //     0xdb9900: mov             SP, fp
    //     0xdb9904: ldp             fp, lr, [SP], #0x10
    // 0xdb9908: ret
    //     0xdb9908: ret             
    // 0xdb990c: r0 = Null
    //     0xdb990c: mov             x0, NULL
    // 0xdb9910: LeaveFrame
    //     0xdb9910: mov             SP, fp
    //     0xdb9914: ldp             fp, lr, [SP], #0x10
    // 0xdb9918: ret
    //     0xdb9918: ret             
    // 0xdb991c: r0 = StackOverflowSharedWithFPURegs()
    //     0xdb991c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xdb9920: b               #0xdb9780
  }
  _ _getTargetPixels(/* No info */) {
    // ** addr: 0xdb9930, size: 0xcc
    // 0xdb9930: EnterFrame
    //     0xdb9930: stp             fp, lr, [SP, #-0x10]!
    //     0xdb9934: mov             fp, SP
    // 0xdb9938: AllocStack(0x20)
    //     0xdb9938: sub             SP, SP, #0x20
    // 0xdb993c: SetupParameters(PageScrollPhysics this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x20 */)
    //     0xdb993c: mov             x4, x1
    //     0xdb9940: mov             x0, x2
    //     0xdb9944: stur            x1, [fp, #-8]
    //     0xdb9948: stur            x2, [fp, #-0x10]
    //     0xdb994c: stur            x3, [fp, #-0x18]
    //     0xdb9950: stur            d0, [fp, #-0x20]
    // 0xdb9954: CheckStackOverflow
    //     0xdb9954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb9958: cmp             SP, x16
    //     0xdb995c: b.ls            #0xdb99f4
    // 0xdb9960: mov             x1, x4
    // 0xdb9964: mov             x2, x0
    // 0xdb9968: r0 = _getPage()
    //     0xdb9968: bl              #0xdb9a78  ; [package:flutter/src/widgets/page_view.dart] PageScrollPhysics::_getPage
    // 0xdb996c: ldur            x0, [fp, #-0x18]
    // 0xdb9970: ArrayLoad: d1 = r0[0]  ; List_8
    //     0xdb9970: ldur            d1, [x0, #0x17]
    // 0xdb9974: fneg            d2, d1
    // 0xdb9978: ldur            d3, [fp, #-0x20]
    // 0xdb997c: fcmp            d2, d3
    // 0xdb9980: b.le            #0xdb9994
    // 0xdb9984: d2 = 0.500000
    //     0xdb9984: fmov            d2, #0.50000000
    // 0xdb9988: fsub            d1, d0, d2
    // 0xdb998c: mov             v0.16b, v1.16b
    // 0xdb9990: b               #0xdb99a8
    // 0xdb9994: d2 = 0.500000
    //     0xdb9994: fmov            d2, #0.50000000
    // 0xdb9998: fcmp            d3, d1
    // 0xdb999c: b.le            #0xdb99a8
    // 0xdb99a0: fadd            d1, d0, d2
    // 0xdb99a4: mov             v0.16b, v1.16b
    // 0xdb99a8: stp             fp, lr, [SP, #-0x10]!
    // 0xdb99ac: mov             fp, SP
    // 0xdb99b0: CallRuntime_LibcRound(double) -> double
    //     0xdb99b0: and             SP, SP, #0xfffffffffffffff0
    //     0xdb99b4: mov             sp, SP
    //     0xdb99b8: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xdb99bc: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xdb99c0: blr             x16
    //     0xdb99c4: movz            x16, #0x8
    //     0xdb99c8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xdb99cc: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xdb99d0: sub             sp, x16, #1, lsl #12
    //     0xdb99d4: mov             SP, fp
    //     0xdb99d8: ldp             fp, lr, [SP], #0x10
    // 0xdb99dc: ldur            x1, [fp, #-8]
    // 0xdb99e0: ldur            x2, [fp, #-0x10]
    // 0xdb99e4: r0 = _getPixels()
    //     0xdb99e4: bl              #0xdb99fc  ; [package:flutter/src/widgets/page_view.dart] PageScrollPhysics::_getPixels
    // 0xdb99e8: LeaveFrame
    //     0xdb99e8: mov             SP, fp
    //     0xdb99ec: ldp             fp, lr, [SP], #0x10
    // 0xdb99f0: ret
    //     0xdb99f0: ret             
    // 0xdb99f4: r0 = StackOverflowSharedWithFPURegs()
    //     0xdb99f4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xdb99f8: b               #0xdb9960
  }
  _ _getPixels(/* No info */) {
    // ** addr: 0xdb99fc, size: 0x7c
    // 0xdb99fc: EnterFrame
    //     0xdb99fc: stp             fp, lr, [SP, #-0x10]!
    //     0xdb9a00: mov             fp, SP
    // 0xdb9a04: AllocStack(0x8)
    //     0xdb9a04: sub             SP, SP, #8
    // 0xdb9a08: SetupParameters(PageScrollPhysics this /* r1 => r0 */, dynamic _ /* r2 => r1 */, dynamic _ /* d0 => d0, fp-0x8 */)
    //     0xdb9a08: mov             x0, x1
    //     0xdb9a0c: mov             x1, x2
    //     0xdb9a10: stur            d0, [fp, #-8]
    // 0xdb9a14: CheckStackOverflow
    //     0xdb9a14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb9a18: cmp             SP, x16
    //     0xdb9a1c: b.ls            #0xdb9a70
    // 0xdb9a20: r0 = LoadClassIdInstr(r1)
    //     0xdb9a20: ldur            x0, [x1, #-1]
    //     0xdb9a24: ubfx            x0, x0, #0xc, #0x14
    // 0xdb9a28: cmp             x0, #0xe48
    // 0xdb9a2c: b.ne            #0xdb9a40
    // 0xdb9a30: r0 = getPixelsFromPage()
    //     0xdb9a30: bl              #0x8e9c14  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::getPixelsFromPage
    // 0xdb9a34: LeaveFrame
    //     0xdb9a34: mov             SP, fp
    //     0xdb9a38: ldp             fp, lr, [SP], #0x10
    // 0xdb9a3c: ret
    //     0xdb9a3c: ret             
    // 0xdb9a40: r0 = LoadClassIdInstr(r1)
    //     0xdb9a40: ldur            x0, [x1, #-1]
    //     0xdb9a44: ubfx            x0, x0, #0xc, #0x14
    // 0xdb9a48: r0 = GDT[cid_x0 + 0x2bb4]()
    //     0xdb9a48: movz            x17, #0x2bb4
    //     0xdb9a4c: add             lr, x0, x17
    //     0xdb9a50: ldr             lr, [x21, lr, lsl #3]
    //     0xdb9a54: blr             lr
    // 0xdb9a58: ldur            d1, [fp, #-8]
    // 0xdb9a5c: fmul            d2, d1, d0
    // 0xdb9a60: mov             v0.16b, v2.16b
    // 0xdb9a64: LeaveFrame
    //     0xdb9a64: mov             SP, fp
    //     0xdb9a68: ldp             fp, lr, [SP], #0x10
    // 0xdb9a6c: ret
    //     0xdb9a6c: ret             
    // 0xdb9a70: r0 = StackOverflowSharedWithFPURegs()
    //     0xdb9a70: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xdb9a74: b               #0xdb9a20
  }
  _ _getPage(/* No info */) {
    // ** addr: 0xdb9a78, size: 0xa8
    // 0xdb9a78: EnterFrame
    //     0xdb9a78: stp             fp, lr, [SP, #-0x10]!
    //     0xdb9a7c: mov             fp, SP
    // 0xdb9a80: AllocStack(0x10)
    //     0xdb9a80: sub             SP, SP, #0x10
    // 0xdb9a84: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xdb9a84: stur            x2, [fp, #-8]
    // 0xdb9a88: CheckStackOverflow
    //     0xdb9a88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb9a8c: cmp             SP, x16
    //     0xdb9a90: b.ls            #0xdb9b14
    // 0xdb9a94: r0 = LoadClassIdInstr(r2)
    //     0xdb9a94: ldur            x0, [x2, #-1]
    //     0xdb9a98: ubfx            x0, x0, #0xc, #0x14
    // 0xdb9a9c: cmp             x0, #0xe48
    // 0xdb9aa0: b.ne            #0xdb9ac4
    // 0xdb9aa4: mov             x1, x2
    // 0xdb9aa8: r0 = page()
    //     0xdb9aa8: bl              #0x92c44c  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::page
    // 0xdb9aac: cmp             w0, NULL
    // 0xdb9ab0: b.eq            #0xdb9b1c
    // 0xdb9ab4: LoadField: d0 = r0->field_7
    //     0xdb9ab4: ldur            d0, [x0, #7]
    // 0xdb9ab8: LeaveFrame
    //     0xdb9ab8: mov             SP, fp
    //     0xdb9abc: ldp             fp, lr, [SP], #0x10
    // 0xdb9ac0: ret
    //     0xdb9ac0: ret             
    // 0xdb9ac4: r0 = LoadClassIdInstr(r2)
    //     0xdb9ac4: ldur            x0, [x2, #-1]
    //     0xdb9ac8: ubfx            x0, x0, #0xc, #0x14
    // 0xdb9acc: mov             x1, x2
    // 0xdb9ad0: r0 = GDT[cid_x0 + -0xfed]()
    //     0xdb9ad0: sub             lr, x0, #0xfed
    //     0xdb9ad4: ldr             lr, [x21, lr, lsl #3]
    //     0xdb9ad8: blr             lr
    // 0xdb9adc: ldur            x1, [fp, #-8]
    // 0xdb9ae0: stur            d0, [fp, #-0x10]
    // 0xdb9ae4: r0 = LoadClassIdInstr(r1)
    //     0xdb9ae4: ldur            x0, [x1, #-1]
    //     0xdb9ae8: ubfx            x0, x0, #0xc, #0x14
    // 0xdb9aec: r0 = GDT[cid_x0 + 0x2bb4]()
    //     0xdb9aec: movz            x17, #0x2bb4
    //     0xdb9af0: add             lr, x0, x17
    //     0xdb9af4: ldr             lr, [x21, lr, lsl #3]
    //     0xdb9af8: blr             lr
    // 0xdb9afc: ldur            d1, [fp, #-0x10]
    // 0xdb9b00: fdiv            d2, d1, d0
    // 0xdb9b04: mov             v0.16b, v2.16b
    // 0xdb9b08: LeaveFrame
    //     0xdb9b08: mov             SP, fp
    //     0xdb9b0c: ldp             fp, lr, [SP], #0x10
    // 0xdb9b10: ret
    //     0xdb9b10: ret             
    // 0xdb9b14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb9b14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb9b18: b               #0xdb9a94
    // 0xdb9b1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb9b1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 2621, size: 0x10, field offset: 0xc
//   const constructor, 
class _ForceImplicitScrollPhysics extends ScrollPhysics {

  _ applyTo(/* No info */) {
    // ** addr: 0xda2f84, size: 0x48
    // 0xda2f84: EnterFrame
    //     0xda2f84: stp             fp, lr, [SP, #-0x10]!
    //     0xda2f88: mov             fp, SP
    // 0xda2f8c: AllocStack(0x8)
    //     0xda2f8c: sub             SP, SP, #8
    // 0xda2f90: CheckStackOverflow
    //     0xda2f90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xda2f94: cmp             SP, x16
    //     0xda2f98: b.ls            #0xda2fc4
    // 0xda2f9c: r0 = buildParent()
    //     0xda2f9c: bl              #0xda2fcc  ; [package:flutter/src/widgets/scroll_physics.dart] ScrollPhysics::buildParent
    // 0xda2fa0: stur            x0, [fp, #-8]
    // 0xda2fa4: r0 = _ForceImplicitScrollPhysics()
    //     0xda2fa4: bl              #0xa1b7e0  ; Allocate_ForceImplicitScrollPhysicsStub -> _ForceImplicitScrollPhysics (size=0x10)
    // 0xda2fa8: r1 = false
    //     0xda2fa8: add             x1, NULL, #0x30  ; false
    // 0xda2fac: StoreField: r0->field_b = r1
    //     0xda2fac: stur            w1, [x0, #0xb]
    // 0xda2fb0: ldur            x1, [fp, #-8]
    // 0xda2fb4: StoreField: r0->field_7 = r1
    //     0xda2fb4: stur            w1, [x0, #7]
    // 0xda2fb8: LeaveFrame
    //     0xda2fb8: mov             SP, fp
    //     0xda2fbc: ldp             fp, lr, [SP], #0x10
    // 0xda2fc0: ret
    //     0xda2fc0: ret             
    // 0xda2fc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xda2fc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xda2fc8: b               #0xda2f9c
  }
}

// class id: 3648, size: 0x54, field offset: 0x40
class PageController extends ScrollController {

  _ jumpToPage(/* No info */) {
    // ** addr: 0x8e9ab4, size: 0x160
    // 0x8e9ab4: EnterFrame
    //     0x8e9ab4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e9ab8: mov             fp, SP
    // 0x8e9abc: AllocStack(0x20)
    //     0x8e9abc: sub             SP, SP, #0x20
    // 0x8e9ac0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8e9ac0: stur            x2, [fp, #-8]
    // 0x8e9ac4: CheckStackOverflow
    //     0x8e9ac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e9ac8: cmp             SP, x16
    //     0x8e9acc: b.ls            #0x8e9c0c
    // 0x8e9ad0: LoadField: r0 = r1->field_3b
    //     0x8e9ad0: ldur            w0, [x1, #0x3b]
    // 0x8e9ad4: DecompressPointer r0
    //     0x8e9ad4: add             x0, x0, HEAP, lsl #32
    // 0x8e9ad8: mov             x1, x0
    // 0x8e9adc: r0 = single()
    //     0x8e9adc: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x8e9ae0: mov             x3, x0
    // 0x8e9ae4: r2 = Null
    //     0x8e9ae4: mov             x2, NULL
    // 0x8e9ae8: r1 = Null
    //     0x8e9ae8: mov             x1, NULL
    // 0x8e9aec: stur            x3, [fp, #-0x10]
    // 0x8e9af0: r4 = 60
    //     0x8e9af0: movz            x4, #0x3c
    // 0x8e9af4: branchIfSmi(r0, 0x8e9b00)
    //     0x8e9af4: tbz             w0, #0, #0x8e9b00
    // 0x8e9af8: r4 = LoadClassIdInstr(r0)
    //     0x8e9af8: ldur            x4, [x0, #-1]
    //     0x8e9afc: ubfx            x4, x4, #0xc, #0x14
    // 0x8e9b00: cmp             x4, #0xe48
    // 0x8e9b04: b.eq            #0x8e9b1c
    // 0x8e9b08: r8 = _PagePosition
    //     0x8e9b08: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c148] Type: _PagePosition
    //     0x8e9b0c: ldr             x8, [x8, #0x148]
    // 0x8e9b10: r3 = Null
    //     0x8e9b10: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c150] Null
    //     0x8e9b14: ldr             x3, [x3, #0x150]
    // 0x8e9b18: r0 = DefaultTypeTest()
    //     0x8e9b18: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8e9b1c: ldur            x2, [fp, #-0x10]
    // 0x8e9b20: LoadField: r0 = r2->field_83
    //     0x8e9b20: ldur            w0, [x2, #0x83]
    // 0x8e9b24: DecompressPointer r0
    //     0x8e9b24: add             x0, x0, HEAP, lsl #32
    // 0x8e9b28: cmp             w0, NULL
    // 0x8e9b2c: b.eq            #0x8e9b80
    // 0x8e9b30: ldur            x3, [fp, #-8]
    // 0x8e9b34: r0 = BoxInt64Instr(r3)
    //     0x8e9b34: sbfiz           x0, x3, #1, #0x1f
    //     0x8e9b38: cmp             x3, x0, asr #1
    //     0x8e9b3c: b.eq            #0x8e9b48
    //     0x8e9b40: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e9b44: stur            x3, [x0, #7]
    // 0x8e9b48: stp             x0, NULL, [SP]
    // 0x8e9b4c: r0 = _Double.fromInteger()
    //     0x8e9b4c: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x8e9b50: ldur            x2, [fp, #-0x10]
    // 0x8e9b54: StoreField: r2->field_83 = r0
    //     0x8e9b54: stur            w0, [x2, #0x83]
    //     0x8e9b58: ldurb           w16, [x2, #-1]
    //     0x8e9b5c: ldurb           w17, [x0, #-1]
    //     0x8e9b60: and             x16, x17, x16, lsr #2
    //     0x8e9b64: tst             x16, HEAP, lsr #32
    //     0x8e9b68: b.eq            #0x8e9b70
    //     0x8e9b6c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e9b70: r0 = Null
    //     0x8e9b70: mov             x0, NULL
    // 0x8e9b74: LeaveFrame
    //     0x8e9b74: mov             SP, fp
    //     0x8e9b78: ldp             fp, lr, [SP], #0x10
    // 0x8e9b7c: ret
    //     0x8e9b7c: ret             
    // 0x8e9b80: ldur            x3, [fp, #-8]
    // 0x8e9b84: LoadField: r0 = r2->field_43
    //     0x8e9b84: ldur            w0, [x2, #0x43]
    // 0x8e9b88: DecompressPointer r0
    //     0x8e9b88: add             x0, x0, HEAP, lsl #32
    // 0x8e9b8c: cmp             w0, NULL
    // 0x8e9b90: b.ne            #0x8e9bcc
    // 0x8e9b94: r0 = BoxInt64Instr(r3)
    //     0x8e9b94: sbfiz           x0, x3, #1, #0x1f
    //     0x8e9b98: cmp             x3, x0, asr #1
    //     0x8e9b9c: b.eq            #0x8e9ba8
    //     0x8e9ba0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e9ba4: stur            x3, [x0, #7]
    // 0x8e9ba8: stp             x0, NULL, [SP]
    // 0x8e9bac: r0 = _Double.fromInteger()
    //     0x8e9bac: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x8e9bb0: LoadField: d0 = r0->field_7
    //     0x8e9bb0: ldur            d0, [x0, #7]
    // 0x8e9bb4: ldur            x2, [fp, #-0x10]
    // 0x8e9bb8: StoreField: r2->field_7b = d0
    //     0x8e9bb8: stur            d0, [x2, #0x7b]
    // 0x8e9bbc: r0 = Null
    //     0x8e9bbc: mov             x0, NULL
    // 0x8e9bc0: LeaveFrame
    //     0x8e9bc0: mov             SP, fp
    //     0x8e9bc4: ldp             fp, lr, [SP], #0x10
    // 0x8e9bc8: ret
    //     0x8e9bc8: ret             
    // 0x8e9bcc: r0 = BoxInt64Instr(r3)
    //     0x8e9bcc: sbfiz           x0, x3, #1, #0x1f
    //     0x8e9bd0: cmp             x3, x0, asr #1
    //     0x8e9bd4: b.eq            #0x8e9be0
    //     0x8e9bd8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e9bdc: stur            x3, [x0, #7]
    // 0x8e9be0: stp             x0, NULL, [SP]
    // 0x8e9be4: r0 = _Double.fromInteger()
    //     0x8e9be4: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x8e9be8: LoadField: d0 = r0->field_7
    //     0x8e9be8: ldur            d0, [x0, #7]
    // 0x8e9bec: ldur            x1, [fp, #-0x10]
    // 0x8e9bf0: r0 = getPixelsFromPage()
    //     0x8e9bf0: bl              #0x8e9c14  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::getPixelsFromPage
    // 0x8e9bf4: ldur            x1, [fp, #-0x10]
    // 0x8e9bf8: r0 = jumpTo()
    //     0x8e9bf8: bl              #0xd89338  ; [package:flutter/src/widgets/scroll_position_with_single_context.dart] ScrollPositionWithSingleContext::jumpTo
    // 0x8e9bfc: r0 = Null
    //     0x8e9bfc: mov             x0, NULL
    // 0x8e9c00: LeaveFrame
    //     0x8e9c00: mov             SP, fp
    //     0x8e9c04: ldp             fp, lr, [SP], #0x10
    // 0x8e9c08: ret
    //     0x8e9c08: ret             
    // 0x8e9c0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e9c0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e9c10: b               #0x8e9ad0
  }
  _ animateToPage(/* No info */) {
    // ** addr: 0x8f11bc, size: 0x200
    // 0x8f11bc: EnterFrame
    //     0x8f11bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8f11c0: mov             fp, SP
    // 0x8f11c4: AllocStack(0x38)
    //     0x8f11c4: sub             SP, SP, #0x38
    // 0x8f11c8: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r2, fp-0x10 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0x8f11c8: mov             x0, x2
    //     0x8f11cc: stur            x2, [fp, #-8]
    //     0x8f11d0: mov             x2, x3
    //     0x8f11d4: stur            x3, [fp, #-0x10]
    //     0x8f11d8: mov             x3, x5
    //     0x8f11dc: stur            x5, [fp, #-0x18]
    // 0x8f11e0: CheckStackOverflow
    //     0x8f11e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f11e4: cmp             SP, x16
    //     0x8f11e8: b.ls            #0x8f13b4
    // 0x8f11ec: LoadField: r4 = r1->field_3b
    //     0x8f11ec: ldur            w4, [x1, #0x3b]
    // 0x8f11f0: DecompressPointer r4
    //     0x8f11f0: add             x4, x4, HEAP, lsl #32
    // 0x8f11f4: mov             x1, x4
    // 0x8f11f8: r0 = single()
    //     0x8f11f8: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x8f11fc: mov             x3, x0
    // 0x8f1200: r2 = Null
    //     0x8f1200: mov             x2, NULL
    // 0x8f1204: r1 = Null
    //     0x8f1204: mov             x1, NULL
    // 0x8f1208: stur            x3, [fp, #-0x20]
    // 0x8f120c: r4 = 60
    //     0x8f120c: movz            x4, #0x3c
    // 0x8f1210: branchIfSmi(r0, 0x8f121c)
    //     0x8f1210: tbz             w0, #0, #0x8f121c
    // 0x8f1214: r4 = LoadClassIdInstr(r0)
    //     0x8f1214: ldur            x4, [x0, #-1]
    //     0x8f1218: ubfx            x4, x4, #0xc, #0x14
    // 0x8f121c: cmp             x4, #0xe48
    // 0x8f1220: b.eq            #0x8f1238
    // 0x8f1224: r8 = _PagePosition
    //     0x8f1224: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c148] Type: _PagePosition
    //     0x8f1228: ldr             x8, [x8, #0x148]
    // 0x8f122c: r3 = Null
    //     0x8f122c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Null
    //     0x8f1230: ldr             x3, [x3, #0xb10]
    // 0x8f1234: r0 = DefaultTypeTest()
    //     0x8f1234: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8f1238: ldur            x2, [fp, #-0x20]
    // 0x8f123c: LoadField: r0 = r2->field_83
    //     0x8f123c: ldur            w0, [x2, #0x83]
    // 0x8f1240: DecompressPointer r0
    //     0x8f1240: add             x0, x0, HEAP, lsl #32
    // 0x8f1244: cmp             w0, NULL
    // 0x8f1248: b.eq            #0x8f12e0
    // 0x8f124c: ldur            x3, [fp, #-8]
    // 0x8f1250: r0 = BoxInt64Instr(r3)
    //     0x8f1250: sbfiz           x0, x3, #1, #0x1f
    //     0x8f1254: cmp             x3, x0, asr #1
    //     0x8f1258: b.eq            #0x8f1264
    //     0x8f125c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f1260: stur            x3, [x0, #7]
    // 0x8f1264: stp             x0, NULL, [SP]
    // 0x8f1268: r0 = _Double.fromInteger()
    //     0x8f1268: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x8f126c: ldur            x2, [fp, #-0x20]
    // 0x8f1270: StoreField: r2->field_83 = r0
    //     0x8f1270: stur            w0, [x2, #0x83]
    //     0x8f1274: ldurb           w16, [x2, #-1]
    //     0x8f1278: ldurb           w17, [x0, #-1]
    //     0x8f127c: and             x16, x17, x16, lsr #2
    //     0x8f1280: tst             x16, HEAP, lsr #32
    //     0x8f1284: b.eq            #0x8f128c
    //     0x8f1288: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8f128c: r1 = <void?>
    //     0x8f128c: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8f1290: r0 = _Future()
    //     0x8f1290: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x8f1294: stur            x0, [fp, #-0x28]
    // 0x8f1298: StoreField: r0->field_b = rZR
    //     0x8f1298: stur            xzr, [x0, #0xb]
    // 0x8f129c: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x8f129c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f12a0: ldr             x0, [x0, #0x7a0]
    //     0x8f12a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f12a8: cmp             w0, w16
    //     0x8f12ac: b.ne            #0x8f12b8
    //     0x8f12b0: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x8f12b4: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8f12b8: mov             x1, x0
    // 0x8f12bc: ldur            x0, [fp, #-0x28]
    // 0x8f12c0: StoreField: r0->field_13 = r1
    //     0x8f12c0: stur            w1, [x0, #0x13]
    // 0x8f12c4: mov             x1, x0
    // 0x8f12c8: r2 = Null
    //     0x8f12c8: mov             x2, NULL
    // 0x8f12cc: r0 = _asyncComplete()
    //     0x8f12cc: bl              #0x5f9868  ; [dart:async] _Future::_asyncComplete
    // 0x8f12d0: ldur            x0, [fp, #-0x28]
    // 0x8f12d4: LeaveFrame
    //     0x8f12d4: mov             SP, fp
    //     0x8f12d8: ldp             fp, lr, [SP], #0x10
    // 0x8f12dc: ret
    //     0x8f12dc: ret             
    // 0x8f12e0: ldur            x3, [fp, #-8]
    // 0x8f12e4: LoadField: r0 = r2->field_43
    //     0x8f12e4: ldur            w0, [x2, #0x43]
    // 0x8f12e8: DecompressPointer r0
    //     0x8f12e8: add             x0, x0, HEAP, lsl #32
    // 0x8f12ec: cmp             w0, NULL
    // 0x8f12f0: b.ne            #0x8f1370
    // 0x8f12f4: r0 = BoxInt64Instr(r3)
    //     0x8f12f4: sbfiz           x0, x3, #1, #0x1f
    //     0x8f12f8: cmp             x3, x0, asr #1
    //     0x8f12fc: b.eq            #0x8f1308
    //     0x8f1300: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f1304: stur            x3, [x0, #7]
    // 0x8f1308: stp             x0, NULL, [SP]
    // 0x8f130c: r0 = _Double.fromInteger()
    //     0x8f130c: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x8f1310: LoadField: d0 = r0->field_7
    //     0x8f1310: ldur            d0, [x0, #7]
    // 0x8f1314: ldur            x2, [fp, #-0x20]
    // 0x8f1318: StoreField: r2->field_7b = d0
    //     0x8f1318: stur            d0, [x2, #0x7b]
    // 0x8f131c: r1 = <void?>
    //     0x8f131c: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8f1320: r0 = _Future()
    //     0x8f1320: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x8f1324: stur            x0, [fp, #-0x28]
    // 0x8f1328: StoreField: r0->field_b = rZR
    //     0x8f1328: stur            xzr, [x0, #0xb]
    // 0x8f132c: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x8f132c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f1330: ldr             x0, [x0, #0x7a0]
    //     0x8f1334: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f1338: cmp             w0, w16
    //     0x8f133c: b.ne            #0x8f1348
    //     0x8f1340: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x8f1344: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8f1348: mov             x1, x0
    // 0x8f134c: ldur            x0, [fp, #-0x28]
    // 0x8f1350: StoreField: r0->field_13 = r1
    //     0x8f1350: stur            w1, [x0, #0x13]
    // 0x8f1354: mov             x1, x0
    // 0x8f1358: r2 = Null
    //     0x8f1358: mov             x2, NULL
    // 0x8f135c: r0 = _asyncComplete()
    //     0x8f135c: bl              #0x5f9868  ; [dart:async] _Future::_asyncComplete
    // 0x8f1360: ldur            x0, [fp, #-0x28]
    // 0x8f1364: LeaveFrame
    //     0x8f1364: mov             SP, fp
    //     0x8f1368: ldp             fp, lr, [SP], #0x10
    // 0x8f136c: ret
    //     0x8f136c: ret             
    // 0x8f1370: r0 = BoxInt64Instr(r3)
    //     0x8f1370: sbfiz           x0, x3, #1, #0x1f
    //     0x8f1374: cmp             x3, x0, asr #1
    //     0x8f1378: b.eq            #0x8f1384
    //     0x8f137c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f1380: stur            x3, [x0, #7]
    // 0x8f1384: stp             x0, NULL, [SP]
    // 0x8f1388: r0 = _Double.fromInteger()
    //     0x8f1388: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x8f138c: LoadField: d0 = r0->field_7
    //     0x8f138c: ldur            d0, [x0, #7]
    // 0x8f1390: ldur            x1, [fp, #-0x20]
    // 0x8f1394: r0 = getPixelsFromPage()
    //     0x8f1394: bl              #0x8e9c14  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::getPixelsFromPage
    // 0x8f1398: ldur            x1, [fp, #-0x20]
    // 0x8f139c: ldur            x2, [fp, #-0x10]
    // 0x8f13a0: ldur            x3, [fp, #-0x18]
    // 0x8f13a4: r0 = animateTo()
    //     0x8f13a4: bl              #0xd87e78  ; [package:flutter/src/widgets/scroll_position_with_single_context.dart] ScrollPositionWithSingleContext::animateTo
    // 0x8f13a8: LeaveFrame
    //     0x8f13a8: mov             SP, fp
    //     0x8f13ac: ldp             fp, lr, [SP], #0x10
    // 0x8f13b0: ret
    //     0x8f13b0: ret             
    // 0x8f13b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f13b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f13b8: b               #0x8f11ec
  }
  get _ page(/* No info */) {
    // ** addr: 0x92c3cc, size: 0x80
    // 0x92c3cc: EnterFrame
    //     0x92c3cc: stp             fp, lr, [SP, #-0x10]!
    //     0x92c3d0: mov             fp, SP
    // 0x92c3d4: AllocStack(0x8)
    //     0x92c3d4: sub             SP, SP, #8
    // 0x92c3d8: CheckStackOverflow
    //     0x92c3d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92c3dc: cmp             SP, x16
    //     0x92c3e0: b.ls            #0x92c444
    // 0x92c3e4: LoadField: r0 = r1->field_3b
    //     0x92c3e4: ldur            w0, [x1, #0x3b]
    // 0x92c3e8: DecompressPointer r0
    //     0x92c3e8: add             x0, x0, HEAP, lsl #32
    // 0x92c3ec: mov             x1, x0
    // 0x92c3f0: r0 = single()
    //     0x92c3f0: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x92c3f4: mov             x3, x0
    // 0x92c3f8: r2 = Null
    //     0x92c3f8: mov             x2, NULL
    // 0x92c3fc: r1 = Null
    //     0x92c3fc: mov             x1, NULL
    // 0x92c400: stur            x3, [fp, #-8]
    // 0x92c404: r4 = 60
    //     0x92c404: movz            x4, #0x3c
    // 0x92c408: branchIfSmi(r0, 0x92c414)
    //     0x92c408: tbz             w0, #0, #0x92c414
    // 0x92c40c: r4 = LoadClassIdInstr(r0)
    //     0x92c40c: ldur            x4, [x0, #-1]
    //     0x92c410: ubfx            x4, x4, #0xc, #0x14
    // 0x92c414: cmp             x4, #0xe48
    // 0x92c418: b.eq            #0x92c430
    // 0x92c41c: r8 = _PagePosition
    //     0x92c41c: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c148] Type: _PagePosition
    //     0x92c420: ldr             x8, [x8, #0x148]
    // 0x92c424: r3 = Null
    //     0x92c424: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fb30] Null
    //     0x92c428: ldr             x3, [x3, #0xb30]
    // 0x92c42c: r0 = DefaultTypeTest()
    //     0x92c42c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x92c430: ldur            x1, [fp, #-8]
    // 0x92c434: r0 = page()
    //     0x92c434: bl              #0x92c44c  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::page
    // 0x92c438: LeaveFrame
    //     0x92c438: mov             SP, fp
    //     0x92c43c: ldp             fp, lr, [SP], #0x10
    // 0x92c440: ret
    //     0x92c440: ret             
    // 0x92c444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92c444: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92c448: b               #0x92c3e4
  }
  _ nextPage(/* No info */) {
    // ** addr: 0xad66a8, size: 0xe0
    // 0xad66a8: EnterFrame
    //     0xad66a8: stp             fp, lr, [SP, #-0x10]!
    //     0xad66ac: mov             fp, SP
    // 0xad66b0: AllocStack(0x8)
    //     0xad66b0: sub             SP, SP, #8
    // 0xad66b4: SetupParameters(PageController this /* r1 => r0, fp-0x8 */)
    //     0xad66b4: mov             x0, x1
    //     0xad66b8: stur            x1, [fp, #-8]
    // 0xad66bc: CheckStackOverflow
    //     0xad66bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad66c0: cmp             SP, x16
    //     0xad66c4: b.ls            #0xad6760
    // 0xad66c8: mov             x1, x0
    // 0xad66cc: r0 = page()
    //     0xad66cc: bl              #0x92c3cc  ; [package:flutter/src/widgets/page_view.dart] PageController::page
    // 0xad66d0: cmp             w0, NULL
    // 0xad66d4: b.eq            #0xad6768
    // 0xad66d8: LoadField: d0 = r0->field_7
    //     0xad66d8: ldur            d0, [x0, #7]
    // 0xad66dc: stp             fp, lr, [SP, #-0x10]!
    // 0xad66e0: mov             fp, SP
    // 0xad66e4: CallRuntime_LibcRound(double) -> double
    //     0xad66e4: and             SP, SP, #0xfffffffffffffff0
    //     0xad66e8: mov             sp, SP
    //     0xad66ec: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xad66f0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xad66f4: blr             x16
    //     0xad66f8: movz            x16, #0x8
    //     0xad66fc: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xad6700: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xad6704: sub             sp, x16, #1, lsl #12
    //     0xad6708: mov             SP, fp
    //     0xad670c: ldp             fp, lr, [SP], #0x10
    // 0xad6710: fcmp            d0, d0
    // 0xad6714: b.vs            #0xad676c
    // 0xad6718: fcvtzs          x0, d0
    // 0xad671c: asr             x16, x0, #0x1e
    // 0xad6720: cmp             x16, x0, asr #63
    // 0xad6724: b.ne            #0xad676c
    // 0xad6728: lsl             x0, x0, #1
    // 0xad672c: r1 = LoadInt32Instr(r0)
    //     0xad672c: sbfx            x1, x0, #1, #0x1f
    //     0xad6730: tbz             w0, #0, #0xad6738
    //     0xad6734: ldur            x1, [x0, #7]
    // 0xad6738: add             x2, x1, #1
    // 0xad673c: ldur            x1, [fp, #-8]
    // 0xad6740: r3 = Instance_Cubic
    //     0xad6740: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!Cubic@e14e61
    //     0xad6744: ldr             x3, [x3, #0xb28]
    // 0xad6748: r5 = Instance_Duration
    //     0xad6748: add             x5, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0xad674c: ldr             x5, [x5, #0x9c0]
    // 0xad6750: r0 = animateToPage()
    //     0xad6750: bl              #0x8f11bc  ; [package:flutter/src/widgets/page_view.dart] PageController::animateToPage
    // 0xad6754: LeaveFrame
    //     0xad6754: mov             SP, fp
    //     0xad6758: ldp             fp, lr, [SP], #0x10
    // 0xad675c: ret
    //     0xad675c: ret             
    // 0xad6760: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad6760: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad6764: b               #0xad66c8
    // 0xad6768: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad6768: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad676c: SaveReg d0
    //     0xad676c: str             q0, [SP, #-0x10]!
    // 0xad6770: r0 = 74
    //     0xad6770: movz            x0, #0x4a
    // 0xad6774: r30 = DoubleToIntegerStub
    //     0xad6774: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xad6778: LoadField: r30 = r30->field_7
    //     0xad6778: ldur            lr, [lr, #7]
    // 0xad677c: blr             lr
    // 0xad6780: RestoreReg d0
    //     0xad6780: ldr             q0, [SP], #0x10
    // 0xad6784: b               #0xad672c
  }
  _ previousPage(/* No info */) {
    // ** addr: 0xad6814, size: 0x128
    // 0xad6814: EnterFrame
    //     0xad6814: stp             fp, lr, [SP, #-0x10]!
    //     0xad6818: mov             fp, SP
    // 0xad681c: AllocStack(0x10)
    //     0xad681c: sub             SP, SP, #0x10
    // 0xad6820: SetupParameters(PageController this /* r1 => r0, fp-0x8 */)
    //     0xad6820: mov             x0, x1
    //     0xad6824: stur            x1, [fp, #-8]
    // 0xad6828: CheckStackOverflow
    //     0xad6828: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad682c: cmp             SP, x16
    //     0xad6830: b.ls            #0xad6914
    // 0xad6834: LoadField: r1 = r0->field_3b
    //     0xad6834: ldur            w1, [x0, #0x3b]
    // 0xad6838: DecompressPointer r1
    //     0xad6838: add             x1, x1, HEAP, lsl #32
    // 0xad683c: r0 = single()
    //     0xad683c: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0xad6840: mov             x3, x0
    // 0xad6844: r2 = Null
    //     0xad6844: mov             x2, NULL
    // 0xad6848: r1 = Null
    //     0xad6848: mov             x1, NULL
    // 0xad684c: stur            x3, [fp, #-0x10]
    // 0xad6850: r4 = 60
    //     0xad6850: movz            x4, #0x3c
    // 0xad6854: branchIfSmi(r0, 0xad6860)
    //     0xad6854: tbz             w0, #0, #0xad6860
    // 0xad6858: r4 = LoadClassIdInstr(r0)
    //     0xad6858: ldur            x4, [x0, #-1]
    //     0xad685c: ubfx            x4, x4, #0xc, #0x14
    // 0xad6860: cmp             x4, #0xe48
    // 0xad6864: b.eq            #0xad687c
    // 0xad6868: r8 = _PagePosition
    //     0xad6868: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c148] Type: _PagePosition
    //     0xad686c: ldr             x8, [x8, #0x148]
    // 0xad6870: r3 = Null
    //     0xad6870: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fb40] Null
    //     0xad6874: ldr             x3, [x3, #0xb40]
    // 0xad6878: r0 = DefaultTypeTest()
    //     0xad6878: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xad687c: ldur            x1, [fp, #-0x10]
    // 0xad6880: r0 = page()
    //     0xad6880: bl              #0x92c44c  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::page
    // 0xad6884: cmp             w0, NULL
    // 0xad6888: b.eq            #0xad691c
    // 0xad688c: LoadField: d0 = r0->field_7
    //     0xad688c: ldur            d0, [x0, #7]
    // 0xad6890: stp             fp, lr, [SP, #-0x10]!
    // 0xad6894: mov             fp, SP
    // 0xad6898: CallRuntime_LibcRound(double) -> double
    //     0xad6898: and             SP, SP, #0xfffffffffffffff0
    //     0xad689c: mov             sp, SP
    //     0xad68a0: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xad68a4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xad68a8: blr             x16
    //     0xad68ac: movz            x16, #0x8
    //     0xad68b0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xad68b4: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xad68b8: sub             sp, x16, #1, lsl #12
    //     0xad68bc: mov             SP, fp
    //     0xad68c0: ldp             fp, lr, [SP], #0x10
    // 0xad68c4: fcmp            d0, d0
    // 0xad68c8: b.vs            #0xad6920
    // 0xad68cc: fcvtzs          x0, d0
    // 0xad68d0: asr             x16, x0, #0x1e
    // 0xad68d4: cmp             x16, x0, asr #63
    // 0xad68d8: b.ne            #0xad6920
    // 0xad68dc: lsl             x0, x0, #1
    // 0xad68e0: r1 = LoadInt32Instr(r0)
    //     0xad68e0: sbfx            x1, x0, #1, #0x1f
    //     0xad68e4: tbz             w0, #0, #0xad68ec
    //     0xad68e8: ldur            x1, [x0, #7]
    // 0xad68ec: sub             x2, x1, #1
    // 0xad68f0: ldur            x1, [fp, #-8]
    // 0xad68f4: r3 = Instance_Cubic
    //     0xad68f4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!Cubic@e14e61
    //     0xad68f8: ldr             x3, [x3, #0xb28]
    // 0xad68fc: r5 = Instance_Duration
    //     0xad68fc: add             x5, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0xad6900: ldr             x5, [x5, #0x9c0]
    // 0xad6904: r0 = animateToPage()
    //     0xad6904: bl              #0x8f11bc  ; [package:flutter/src/widgets/page_view.dart] PageController::animateToPage
    // 0xad6908: LeaveFrame
    //     0xad6908: mov             SP, fp
    //     0xad690c: ldp             fp, lr, [SP], #0x10
    // 0xad6910: ret
    //     0xad6910: ret             
    // 0xad6914: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad6914: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad6918: b               #0xad6834
    // 0xad691c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad691c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad6920: SaveReg d0
    //     0xad6920: str             q0, [SP, #-0x10]!
    // 0xad6924: r0 = 74
    //     0xad6924: movz            x0, #0x4a
    // 0xad6928: r30 = DoubleToIntegerStub
    //     0xad6928: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xad692c: LoadField: r30 = r30->field_7
    //     0xad692c: ldur            lr, [lr, #7]
    // 0xad6930: blr             lr
    // 0xad6934: RestoreReg d0
    //     0xad6934: ldr             q0, [SP], #0x10
    // 0xad6938: b               #0xad68e0
  }
}

// class id: 3656, size: 0x90, field offset: 0x7c
class _PagePosition extends ScrollPositionWithSingleContext
    implements PageMetrics {

  _ getPageFromPixels(/* No info */) {
    // ** addr: 0x67a638, size: 0x138
    // 0x67a638: EnterFrame
    //     0x67a638: stp             fp, lr, [SP, #-0x10]!
    //     0x67a63c: mov             fp, SP
    // 0x67a640: AllocStack(0x18)
    //     0x67a640: sub             SP, SP, #0x18
    // 0x67a644: SetupParameters(_PagePosition this /* r1 => r0, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x10 */, dynamic _ /* d1 => d1, fp-0x18 */)
    //     0x67a644: mov             x0, x1
    //     0x67a648: stur            x1, [fp, #-8]
    //     0x67a64c: stur            d0, [fp, #-0x10]
    //     0x67a650: stur            d1, [fp, #-0x18]
    // 0x67a654: CheckStackOverflow
    //     0x67a654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67a658: cmp             SP, x16
    //     0x67a65c: b.ls            #0x67a768
    // 0x67a660: mov             x1, x0
    // 0x67a664: r0 = _initialPageOffset()
    //     0x67a664: bl              #0x67a770  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::_initialPageOffset
    // 0x67a668: mov             v1.16b, v0.16b
    // 0x67a66c: ldur            d0, [fp, #-0x10]
    // 0x67a670: fsub            d2, d0, d1
    // 0x67a674: d1 = 0.000000
    //     0x67a674: eor             v1.16b, v1.16b, v1.16b
    // 0x67a678: fcmp            d1, d2
    // 0x67a67c: b.le            #0x67a688
    // 0x67a680: d2 = 0.000000
    //     0x67a680: eor             v2.16b, v2.16b, v2.16b
    // 0x67a684: b               #0x67a6b0
    // 0x67a688: fcmp            d2, d1
    // 0x67a68c: b.gt            #0x67a6b0
    // 0x67a690: fcmp            d1, d1
    // 0x67a694: b.ne            #0x67a6a4
    // 0x67a698: fadd            d0, d2, d1
    // 0x67a69c: mov             v2.16b, v0.16b
    // 0x67a6a0: b               #0x67a6b0
    // 0x67a6a4: fcmp            d2, d2
    // 0x67a6a8: b.vs            #0x67a6b0
    // 0x67a6ac: d2 = 0.000000
    //     0x67a6ac: eor             v2.16b, v2.16b, v2.16b
    // 0x67a6b0: ldur            x0, [fp, #-8]
    // 0x67a6b4: ldur            d0, [fp, #-0x18]
    // 0x67a6b8: LoadField: d3 = r0->field_87
    //     0x67a6b8: ldur            d3, [x0, #0x87]
    // 0x67a6bc: fmul            d4, d0, d3
    // 0x67a6c0: fdiv            d3, d2, d4
    // 0x67a6c4: mov             v0.16b, v3.16b
    // 0x67a6c8: stur            d3, [fp, #-0x10]
    // 0x67a6cc: stp             fp, lr, [SP, #-0x10]!
    // 0x67a6d0: mov             fp, SP
    // 0x67a6d4: CallRuntime_LibcRound(double) -> double
    //     0x67a6d4: and             SP, SP, #0xfffffffffffffff0
    //     0x67a6d8: mov             sp, SP
    //     0x67a6dc: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0x67a6e0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x67a6e4: blr             x16
    //     0x67a6e8: movz            x16, #0x8
    //     0x67a6ec: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x67a6f0: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x67a6f4: sub             sp, x16, #1, lsl #12
    //     0x67a6f8: mov             SP, fp
    //     0x67a6fc: ldp             fp, lr, [SP], #0x10
    // 0x67a700: mov             v1.16b, v0.16b
    // 0x67a704: ldur            d0, [fp, #-0x10]
    // 0x67a708: fsub            d2, d0, d1
    // 0x67a70c: d3 = 0.000000
    //     0x67a70c: eor             v3.16b, v3.16b, v3.16b
    // 0x67a710: fcmp            d2, d3
    // 0x67a714: b.ne            #0x67a728
    // 0x67a718: d4 = 0.000000
    //     0x67a718: ldr             d4, [PP, #0x6fc8]  ; [pp+0x6fc8] IMM: double(1e-10) from 0x3ddb7cdfd9d7bdbb
    // 0x67a71c: fcmp            d4, d3
    // 0x67a720: b.le            #0x67a75c
    // 0x67a724: b               #0x67a74c
    // 0x67a728: d4 = 0.000000
    //     0x67a728: ldr             d4, [PP, #0x6fc8]  ; [pp+0x6fc8] IMM: double(1e-10) from 0x3ddb7cdfd9d7bdbb
    // 0x67a72c: fcmp            d3, d2
    // 0x67a730: b.le            #0x67a744
    // 0x67a734: fneg            d3, d2
    // 0x67a738: fcmp            d4, d3
    // 0x67a73c: b.le            #0x67a75c
    // 0x67a740: b               #0x67a74c
    // 0x67a744: fcmp            d4, d2
    // 0x67a748: b.le            #0x67a75c
    // 0x67a74c: mov             v0.16b, v1.16b
    // 0x67a750: LeaveFrame
    //     0x67a750: mov             SP, fp
    //     0x67a754: ldp             fp, lr, [SP], #0x10
    // 0x67a758: ret
    //     0x67a758: ret             
    // 0x67a75c: LeaveFrame
    //     0x67a75c: mov             SP, fp
    //     0x67a760: ldp             fp, lr, [SP], #0x10
    // 0x67a764: ret
    //     0x67a764: ret             
    // 0x67a768: r0 = StackOverflowSharedWithFPURegs()
    //     0x67a768: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x67a76c: b               #0x67a660
  }
  get _ _initialPageOffset(/* No info */) {
    // ** addr: 0x67a770, size: 0x88
    // 0x67a770: d3 = 1.000000
    //     0x67a770: fmov            d3, #1.00000000
    // 0x67a774: d2 = 2.000000
    //     0x67a774: fmov            d2, #2.00000000
    // 0x67a778: d1 = 0.000000
    //     0x67a778: eor             v1.16b, v1.16b, v1.16b
    // 0x67a77c: LoadField: r0 = r1->field_43
    //     0x67a77c: ldur            w0, [x1, #0x43]
    // 0x67a780: DecompressPointer r0
    //     0x67a780: add             x0, x0, HEAP, lsl #32
    // 0x67a784: cmp             w0, NULL
    // 0x67a788: b.eq            #0x67a7ec
    // 0x67a78c: LoadField: d4 = r1->field_87
    //     0x67a78c: ldur            d4, [x1, #0x87]
    // 0x67a790: fsub            d5, d4, d3
    // 0x67a794: LoadField: d3 = r0->field_7
    //     0x67a794: ldur            d3, [x0, #7]
    // 0x67a798: fmul            d4, d3, d5
    // 0x67a79c: fdiv            d3, d4, d2
    // 0x67a7a0: fcmp            d1, d3
    // 0x67a7a4: b.le            #0x67a7b0
    // 0x67a7a8: d0 = 0.000000
    //     0x67a7a8: eor             v0.16b, v0.16b, v0.16b
    // 0x67a7ac: b               #0x67a7e8
    // 0x67a7b0: fcmp            d3, d1
    // 0x67a7b4: b.le            #0x67a7c0
    // 0x67a7b8: mov             v0.16b, v3.16b
    // 0x67a7bc: b               #0x67a7e8
    // 0x67a7c0: fcmp            d1, d1
    // 0x67a7c4: b.ne            #0x67a7d4
    // 0x67a7c8: fadd            d2, d3, d1
    // 0x67a7cc: mov             v0.16b, v2.16b
    // 0x67a7d0: b               #0x67a7e8
    // 0x67a7d4: fcmp            d3, d3
    // 0x67a7d8: b.vc            #0x67a7e4
    // 0x67a7dc: mov             v0.16b, v3.16b
    // 0x67a7e0: b               #0x67a7e8
    // 0x67a7e4: d0 = 0.000000
    //     0x67a7e4: eor             v0.16b, v0.16b, v0.16b
    // 0x67a7e8: ret
    //     0x67a7e8: ret             
    // 0x67a7ec: EnterFrame
    //     0x67a7ec: stp             fp, lr, [SP, #-0x10]!
    //     0x67a7f0: mov             fp, SP
    // 0x67a7f4: r0 = NullCastErrorSharedWithFPURegs()
    //     0x67a7f4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ getPixelsFromPage(/* No info */) {
    // ** addr: 0x8e9c14, size: 0x64
    // 0x8e9c14: EnterFrame
    //     0x8e9c14: stp             fp, lr, [SP, #-0x10]!
    //     0x8e9c18: mov             fp, SP
    // 0x8e9c1c: AllocStack(0x8)
    //     0x8e9c1c: sub             SP, SP, #8
    // 0x8e9c20: CheckStackOverflow
    //     0x8e9c20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e9c24: cmp             SP, x16
    //     0x8e9c28: b.ls            #0x8e9c6c
    // 0x8e9c2c: LoadField: r0 = r1->field_43
    //     0x8e9c2c: ldur            w0, [x1, #0x43]
    // 0x8e9c30: DecompressPointer r0
    //     0x8e9c30: add             x0, x0, HEAP, lsl #32
    // 0x8e9c34: cmp             w0, NULL
    // 0x8e9c38: b.eq            #0x8e9c74
    // 0x8e9c3c: LoadField: d1 = r0->field_7
    //     0x8e9c3c: ldur            d1, [x0, #7]
    // 0x8e9c40: fmul            d2, d0, d1
    // 0x8e9c44: LoadField: d0 = r1->field_87
    //     0x8e9c44: ldur            d0, [x1, #0x87]
    // 0x8e9c48: fmul            d1, d2, d0
    // 0x8e9c4c: stur            d1, [fp, #-8]
    // 0x8e9c50: r0 = _initialPageOffset()
    //     0x8e9c50: bl              #0x67a770  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::_initialPageOffset
    // 0x8e9c54: ldur            d1, [fp, #-8]
    // 0x8e9c58: fadd            d2, d1, d0
    // 0x8e9c5c: mov             v0.16b, v2.16b
    // 0x8e9c60: LeaveFrame
    //     0x8e9c60: mov             SP, fp
    //     0x8e9c64: ldp             fp, lr, [SP], #0x10
    // 0x8e9c68: ret
    //     0x8e9c68: ret             
    // 0x8e9c6c: r0 = StackOverflowSharedWithFPURegs()
    //     0x8e9c6c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x8e9c70: b               #0x8e9c2c
    // 0x8e9c74: r0 = NullCastErrorSharedWithFPURegs()
    //     0x8e9c74: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  get _ page(/* No info */) {
    // ** addr: 0x92c44c, size: 0x14c
    // 0x92c44c: EnterFrame
    //     0x92c44c: stp             fp, lr, [SP, #-0x10]!
    //     0x92c450: mov             fp, SP
    // 0x92c454: CheckStackOverflow
    //     0x92c454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92c458: cmp             SP, x16
    //     0x92c45c: b.ls            #0x92c570
    // 0x92c460: LoadField: r0 = r1->field_3f
    //     0x92c460: ldur            w0, [x1, #0x3f]
    // 0x92c464: DecompressPointer r0
    //     0x92c464: add             x0, x0, HEAP, lsl #32
    // 0x92c468: cmp             w0, NULL
    // 0x92c46c: b.ne            #0x92c480
    // 0x92c470: r0 = Null
    //     0x92c470: mov             x0, NULL
    // 0x92c474: LeaveFrame
    //     0x92c474: mov             SP, fp
    //     0x92c478: ldp             fp, lr, [SP], #0x10
    // 0x92c47c: ret
    //     0x92c47c: ret             
    // 0x92c480: LoadField: r2 = r1->field_2f
    //     0x92c480: ldur            w2, [x1, #0x2f]
    // 0x92c484: DecompressPointer r2
    //     0x92c484: add             x2, x2, HEAP, lsl #32
    // 0x92c488: cmp             w2, NULL
    // 0x92c48c: b.eq            #0x92c4a0
    // 0x92c490: LoadField: r3 = r1->field_33
    //     0x92c490: ldur            w3, [x1, #0x33]
    // 0x92c494: DecompressPointer r3
    //     0x92c494: add             x3, x3, HEAP, lsl #32
    // 0x92c498: cmp             w3, NULL
    // 0x92c49c: b.ne            #0x92c4ac
    // 0x92c4a0: LoadField: r3 = r1->field_47
    //     0x92c4a0: ldur            w3, [x1, #0x47]
    // 0x92c4a4: DecompressPointer r3
    //     0x92c4a4: add             x3, x3, HEAP, lsl #32
    // 0x92c4a8: tbnz            w3, #4, #0x92c560
    // 0x92c4ac: LoadField: r3 = r1->field_83
    //     0x92c4ac: ldur            w3, [x1, #0x83]
    // 0x92c4b0: DecompressPointer r3
    //     0x92c4b0: add             x3, x3, HEAP, lsl #32
    // 0x92c4b4: cmp             w3, NULL
    // 0x92c4b8: b.ne            #0x92c52c
    // 0x92c4bc: cmp             w2, NULL
    // 0x92c4c0: b.eq            #0x92c578
    // 0x92c4c4: LoadField: r3 = r1->field_33
    //     0x92c4c4: ldur            w3, [x1, #0x33]
    // 0x92c4c8: DecompressPointer r3
    //     0x92c4c8: add             x3, x3, HEAP, lsl #32
    // 0x92c4cc: cmp             w3, NULL
    // 0x92c4d0: b.eq            #0x92c57c
    // 0x92c4d4: LoadField: d0 = r0->field_7
    //     0x92c4d4: ldur            d0, [x0, #7]
    // 0x92c4d8: LoadField: d1 = r2->field_7
    //     0x92c4d8: ldur            d1, [x2, #7]
    // 0x92c4dc: fcmp            d1, d0
    // 0x92c4e0: b.le            #0x92c4ec
    // 0x92c4e4: mov             v0.16b, v1.16b
    // 0x92c4e8: b               #0x92c510
    // 0x92c4ec: LoadField: d1 = r3->field_7
    //     0x92c4ec: ldur            d1, [x3, #7]
    // 0x92c4f0: fcmp            d0, d1
    // 0x92c4f4: b.le            #0x92c500
    // 0x92c4f8: mov             v0.16b, v1.16b
    // 0x92c4fc: b               #0x92c510
    // 0x92c500: LoadField: d2 = r0->field_7
    //     0x92c500: ldur            d2, [x0, #7]
    // 0x92c504: fcmp            d2, d2
    // 0x92c508: b.vc            #0x92c510
    // 0x92c50c: mov             v0.16b, v1.16b
    // 0x92c510: LoadField: r0 = r1->field_43
    //     0x92c510: ldur            w0, [x1, #0x43]
    // 0x92c514: DecompressPointer r0
    //     0x92c514: add             x0, x0, HEAP, lsl #32
    // 0x92c518: cmp             w0, NULL
    // 0x92c51c: b.eq            #0x92c580
    // 0x92c520: LoadField: d1 = r0->field_7
    //     0x92c520: ldur            d1, [x0, #7]
    // 0x92c524: r0 = getPageFromPixels()
    //     0x92c524: bl              #0x67a638  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::getPageFromPixels
    // 0x92c528: b               #0x92c530
    // 0x92c52c: LoadField: d0 = r3->field_7
    //     0x92c52c: ldur            d0, [x3, #7]
    // 0x92c530: r1 = inline_Allocate_Double()
    //     0x92c530: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x92c534: add             x1, x1, #0x10
    //     0x92c538: cmp             x2, x1
    //     0x92c53c: b.ls            #0x92c584
    //     0x92c540: str             x1, [THR, #0x50]  ; THR::top
    //     0x92c544: sub             x1, x1, #0xf
    //     0x92c548: movz            x2, #0xe15c
    //     0x92c54c: movk            x2, #0x3, lsl #16
    //     0x92c550: stur            x2, [x1, #-1]
    // 0x92c554: StoreField: r1->field_7 = d0
    //     0x92c554: stur            d0, [x1, #7]
    // 0x92c558: mov             x0, x1
    // 0x92c55c: b               #0x92c564
    // 0x92c560: r0 = Null
    //     0x92c560: mov             x0, NULL
    // 0x92c564: LeaveFrame
    //     0x92c564: mov             SP, fp
    //     0x92c568: ldp             fp, lr, [SP], #0x10
    // 0x92c56c: ret
    //     0x92c56c: ret             
    // 0x92c570: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92c570: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92c574: b               #0x92c460
    // 0x92c578: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92c578: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92c57c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92c57c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92c580: r0 = NullCastErrorSharedWithFPURegs()
    //     0x92c580: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x92c584: SaveReg d0
    //     0x92c584: str             q0, [SP, #-0x10]!
    // 0x92c588: r0 = AllocateDouble()
    //     0x92c588: bl              #0xec2254  ; AllocateDoubleStub
    // 0x92c58c: mov             x1, x0
    // 0x92c590: RestoreReg d0
    //     0x92c590: ldr             q0, [SP], #0x10
    // 0x92c594: b               #0x92c554
  }
  _ _PagePosition(/* No info */) {
    // ** addr: 0x999924, size: 0x94
    // 0x999924: EnterFrame
    //     0x999924: stp             fp, lr, [SP, #-0x10]!
    //     0x999928: mov             fp, SP
    // 0x99992c: AllocStack(0x30)
    //     0x99992c: sub             SP, SP, #0x30
    // 0x999930: SetupParameters(_PagePosition this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */, dynamic _ /* r6 => r6, fp-0x20 */)
    //     0x999930: mov             x4, x1
    //     0x999934: stur            x1, [fp, #-8]
    //     0x999938: stur            x2, [fp, #-0x10]
    //     0x99993c: stur            x5, [fp, #-0x18]
    //     0x999940: stur            x6, [fp, #-0x20]
    // 0x999944: CheckStackOverflow
    //     0x999944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x999948: cmp             SP, x16
    //     0x99994c: b.ls            #0x9999b0
    // 0x999950: StoreField: r4->field_87 = d0
    //     0x999950: stur            d0, [x4, #0x87]
    // 0x999954: r0 = BoxInt64Instr(r3)
    //     0x999954: sbfiz           x0, x3, #1, #0x1f
    //     0x999958: cmp             x3, x0, asr #1
    //     0x99995c: b.eq            #0x999968
    //     0x999960: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x999964: stur            x3, [x0, #7]
    // 0x999968: stp             x0, NULL, [SP]
    // 0x99996c: r0 = _Double.fromInteger()
    //     0x99996c: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x999970: LoadField: d0 = r0->field_7
    //     0x999970: ldur            d0, [x0, #7]
    // 0x999974: ldur            x1, [fp, #-8]
    // 0x999978: StoreField: r1->field_7b = d0
    //     0x999978: stur            d0, [x1, #0x7b]
    // 0x99997c: r16 = true
    //     0x99997c: add             x16, NULL, #0x20  ; true
    // 0x999980: str             x16, [SP]
    // 0x999984: ldur            x2, [fp, #-0x10]
    // 0x999988: ldur            x5, [fp, #-0x18]
    // 0x99998c: ldur            x6, [fp, #-0x20]
    // 0x999990: r3 = Null
    //     0x999990: mov             x3, NULL
    // 0x999994: r4 = const [0, 0x6, 0x1, 0x5, keepScrollOffset, 0x5, null]
    //     0x999994: add             x4, PP, #0x4f, lsl #12  ; [pp+0x4fad8] List(7) [0, 0x6, 0x1, 0x5, "keepScrollOffset", 0x5, Null]
    //     0x999998: ldr             x4, [x4, #0xad8]
    // 0x99999c: r0 = ScrollPositionWithSingleContext()
    //     0x99999c: bl              #0x9999c4  ; [package:flutter/src/widgets/scroll_position_with_single_context.dart] ScrollPositionWithSingleContext::ScrollPositionWithSingleContext
    // 0x9999a0: r0 = Null
    //     0x9999a0: mov             x0, NULL
    // 0x9999a4: LeaveFrame
    //     0x9999a4: mov             SP, fp
    //     0x9999a8: ldp             fp, lr, [SP], #0x10
    // 0x9999ac: ret
    //     0x9999ac: ret             
    // 0x9999b0: r0 = StackOverflowSharedWithFPURegs()
    //     0x9999b0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x9999b4: b               #0x999950
  }
  set _ viewportFraction=(/* No info */) {
    // ** addr: 0x99b510, size: 0x8c
    // 0x99b510: EnterFrame
    //     0x99b510: stp             fp, lr, [SP, #-0x10]!
    //     0x99b514: mov             fp, SP
    // 0x99b518: AllocStack(0x10)
    //     0x99b518: sub             SP, SP, #0x10
    // 0x99b51c: SetupParameters(_PagePosition this /* r1 => r0, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x10 */)
    //     0x99b51c: mov             x0, x1
    //     0x99b520: stur            x1, [fp, #-8]
    //     0x99b524: stur            d0, [fp, #-0x10]
    // 0x99b528: CheckStackOverflow
    //     0x99b528: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99b52c: cmp             SP, x16
    //     0x99b530: b.ls            #0x99b594
    // 0x99b534: LoadField: d1 = r0->field_87
    //     0x99b534: ldur            d1, [x0, #0x87]
    // 0x99b538: fcmp            d1, d0
    // 0x99b53c: b.ne            #0x99b550
    // 0x99b540: r0 = Null
    //     0x99b540: mov             x0, NULL
    // 0x99b544: LeaveFrame
    //     0x99b544: mov             SP, fp
    //     0x99b548: ldp             fp, lr, [SP], #0x10
    // 0x99b54c: ret
    //     0x99b54c: ret             
    // 0x99b550: mov             x1, x0
    // 0x99b554: r0 = page()
    //     0x99b554: bl              #0x92c44c  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::page
    // 0x99b558: mov             x1, x0
    // 0x99b55c: ldur            x0, [fp, #-8]
    // 0x99b560: ldur            d0, [fp, #-0x10]
    // 0x99b564: StoreField: r0->field_87 = d0
    //     0x99b564: stur            d0, [x0, #0x87]
    // 0x99b568: cmp             w1, NULL
    // 0x99b56c: b.eq            #0x99b584
    // 0x99b570: LoadField: d0 = r1->field_7
    //     0x99b570: ldur            d0, [x1, #7]
    // 0x99b574: mov             x1, x0
    // 0x99b578: r0 = getPixelsFromPage()
    //     0x99b578: bl              #0x8e9c14  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::getPixelsFromPage
    // 0x99b57c: ldur            x1, [fp, #-8]
    // 0x99b580: r0 = forcePixels()
    //     0x99b580: bl              #0x7a6d40  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::forcePixels
    // 0x99b584: r0 = Null
    //     0x99b584: mov             x0, NULL
    // 0x99b588: LeaveFrame
    //     0x99b588: mov             SP, fp
    //     0x99b58c: ldp             fp, lr, [SP], #0x10
    // 0x99b590: ret
    //     0x99b590: ret             
    // 0x99b594: r0 = StackOverflowSharedWithFPURegs()
    //     0x99b594: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x99b598: b               #0x99b534
  }
  _ restoreScrollOffset(/* No info */) {
    // ** addr: 0xc32b9c, size: 0xf0
    // 0xc32b9c: EnterFrame
    //     0xc32b9c: stp             fp, lr, [SP, #-0x10]!
    //     0xc32ba0: mov             fp, SP
    // 0xc32ba4: AllocStack(0x10)
    //     0xc32ba4: sub             SP, SP, #0x10
    // 0xc32ba8: SetupParameters(_PagePosition this /* r1 => r0, fp-0x10 */)
    //     0xc32ba8: mov             x0, x1
    //     0xc32bac: stur            x1, [fp, #-0x10]
    // 0xc32bb0: CheckStackOverflow
    //     0xc32bb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc32bb4: cmp             SP, x16
    //     0xc32bb8: b.ls            #0xc32c7c
    // 0xc32bbc: LoadField: r1 = r0->field_3f
    //     0xc32bbc: ldur            w1, [x0, #0x3f]
    // 0xc32bc0: DecompressPointer r1
    //     0xc32bc0: add             x1, x1, HEAP, lsl #32
    // 0xc32bc4: cmp             w1, NULL
    // 0xc32bc8: b.ne            #0xc32c6c
    // 0xc32bcc: LoadField: r2 = r0->field_27
    //     0xc32bcc: ldur            w2, [x0, #0x27]
    // 0xc32bd0: DecompressPointer r2
    //     0xc32bd0: add             x2, x2, HEAP, lsl #32
    // 0xc32bd4: stur            x2, [fp, #-8]
    // 0xc32bd8: LoadField: r1 = r2->field_f
    //     0xc32bd8: ldur            w1, [x2, #0xf]
    // 0xc32bdc: DecompressPointer r1
    //     0xc32bdc: add             x1, x1, HEAP, lsl #32
    // 0xc32be0: cmp             w1, NULL
    // 0xc32be4: b.eq            #0xc32c84
    // 0xc32be8: r0 = maybeOf()
    //     0xc32be8: bl              #0x679fbc  ; [package:flutter/src/widgets/page_storage.dart] PageStorage::maybeOf
    // 0xc32bec: cmp             w0, NULL
    // 0xc32bf0: b.ne            #0xc32bfc
    // 0xc32bf4: r3 = Null
    //     0xc32bf4: mov             x3, NULL
    // 0xc32bf8: b               #0xc32c1c
    // 0xc32bfc: ldur            x1, [fp, #-8]
    // 0xc32c00: LoadField: r2 = r1->field_f
    //     0xc32c00: ldur            w2, [x1, #0xf]
    // 0xc32c04: DecompressPointer r2
    //     0xc32c04: add             x2, x2, HEAP, lsl #32
    // 0xc32c08: cmp             w2, NULL
    // 0xc32c0c: b.eq            #0xc32c88
    // 0xc32c10: mov             x1, x0
    // 0xc32c14: r0 = readState()
    //     0xc32c14: bl              #0x933608  ; [package:flutter/src/widgets/page_storage.dart] PageStorageBucket::readState
    // 0xc32c18: mov             x3, x0
    // 0xc32c1c: mov             x0, x3
    // 0xc32c20: stur            x3, [fp, #-8]
    // 0xc32c24: r2 = Null
    //     0xc32c24: mov             x2, NULL
    // 0xc32c28: r1 = Null
    //     0xc32c28: mov             x1, NULL
    // 0xc32c2c: r4 = 60
    //     0xc32c2c: movz            x4, #0x3c
    // 0xc32c30: branchIfSmi(r0, 0xc32c3c)
    //     0xc32c30: tbz             w0, #0, #0xc32c3c
    // 0xc32c34: r4 = LoadClassIdInstr(r0)
    //     0xc32c34: ldur            x4, [x0, #-1]
    //     0xc32c38: ubfx            x4, x4, #0xc, #0x14
    // 0xc32c3c: cmp             x4, #0x3e
    // 0xc32c40: b.eq            #0xc32c54
    // 0xc32c44: r8 = double?
    //     0xc32c44: ldr             x8, [PP, #0x12d0]  ; [pp+0x12d0] Type: double?
    // 0xc32c48: r3 = Null
    //     0xc32c48: add             x3, PP, #0x56, lsl #12  ; [pp+0x56938] Null
    //     0xc32c4c: ldr             x3, [x3, #0x938]
    // 0xc32c50: r0 = double?()
    //     0xc32c50: bl              #0xed4434  ; IsType_double?_Stub
    // 0xc32c54: ldur            x1, [fp, #-8]
    // 0xc32c58: cmp             w1, NULL
    // 0xc32c5c: b.eq            #0xc32c6c
    // 0xc32c60: ldur            x2, [fp, #-0x10]
    // 0xc32c64: LoadField: d0 = r1->field_7
    //     0xc32c64: ldur            d0, [x1, #7]
    // 0xc32c68: StoreField: r2->field_7b = d0
    //     0xc32c68: stur            d0, [x2, #0x7b]
    // 0xc32c6c: r0 = Null
    //     0xc32c6c: mov             x0, NULL
    // 0xc32c70: LeaveFrame
    //     0xc32c70: mov             SP, fp
    //     0xc32c74: ldp             fp, lr, [SP], #0x10
    // 0xc32c78: ret
    //     0xc32c78: ret             
    // 0xc32c7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc32c7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc32c80: b               #0xc32bbc
    // 0xc32c84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc32c84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc32c88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc32c88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ ensureVisible(/* No info */) {
    // ** addr: 0xc440c4, size: 0x30
    // 0xc440c4: EnterFrame
    //     0xc440c4: stp             fp, lr, [SP, #-0x10]!
    //     0xc440c8: mov             fp, SP
    // 0xc440cc: CheckStackOverflow
    //     0xc440cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc440d0: cmp             SP, x16
    //     0xc440d4: b.ls            #0xc440ec
    // 0xc440d8: r4 = const [0, 0x6, 0, 0x6, null]
    //     0xc440d8: ldr             x4, [PP, #0x7508]  ; [pp+0x7508] List(5) [0, 0x6, 0, 0x6, Null]
    // 0xc440dc: r0 = ensureVisible()
    //     0xc440dc: bl              #0xc440f4  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::ensureVisible
    // 0xc440e0: LeaveFrame
    //     0xc440e0: mov             SP, fp
    //     0xc440e4: ldp             fp, lr, [SP], #0x10
    // 0xc440e8: ret
    //     0xc440e8: ret             
    // 0xc440ec: r0 = StackOverflowSharedWithFPURegs()
    //     0xc440ec: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc440f0: b               #0xc440d8
  }
  _ saveScrollOffset(/* No info */) {
    // ** addr: 0xc45420, size: 0x11c
    // 0xc45420: EnterFrame
    //     0xc45420: stp             fp, lr, [SP, #-0x10]!
    //     0xc45424: mov             fp, SP
    // 0xc45428: AllocStack(0x20)
    //     0xc45428: sub             SP, SP, #0x20
    // 0xc4542c: SetupParameters(_PagePosition this /* r1 => r0, fp-0x10 */)
    //     0xc4542c: mov             x0, x1
    //     0xc45430: stur            x1, [fp, #-0x10]
    // 0xc45434: CheckStackOverflow
    //     0xc45434: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc45438: cmp             SP, x16
    //     0xc4543c: b.ls            #0xc45510
    // 0xc45440: LoadField: r2 = r0->field_27
    //     0xc45440: ldur            w2, [x0, #0x27]
    // 0xc45444: DecompressPointer r2
    //     0xc45444: add             x2, x2, HEAP, lsl #32
    // 0xc45448: stur            x2, [fp, #-8]
    // 0xc4544c: LoadField: r1 = r2->field_f
    //     0xc4544c: ldur            w1, [x2, #0xf]
    // 0xc45450: DecompressPointer r1
    //     0xc45450: add             x1, x1, HEAP, lsl #32
    // 0xc45454: cmp             w1, NULL
    // 0xc45458: b.eq            #0xc45518
    // 0xc4545c: r0 = maybeOf()
    //     0xc4545c: bl              #0x679fbc  ; [package:flutter/src/widgets/page_storage.dart] PageStorage::maybeOf
    // 0xc45460: stur            x0, [fp, #-0x20]
    // 0xc45464: cmp             w0, NULL
    // 0xc45468: b.eq            #0xc45500
    // 0xc4546c: ldur            x1, [fp, #-0x10]
    // 0xc45470: ldur            x2, [fp, #-8]
    // 0xc45474: LoadField: r3 = r2->field_f
    //     0xc45474: ldur            w3, [x2, #0xf]
    // 0xc45478: DecompressPointer r3
    //     0xc45478: add             x3, x3, HEAP, lsl #32
    // 0xc4547c: stur            x3, [fp, #-0x18]
    // 0xc45480: cmp             w3, NULL
    // 0xc45484: b.eq            #0xc4551c
    // 0xc45488: LoadField: r2 = r1->field_83
    //     0xc45488: ldur            w2, [x1, #0x83]
    // 0xc4548c: DecompressPointer r2
    //     0xc4548c: add             x2, x2, HEAP, lsl #32
    // 0xc45490: cmp             w2, NULL
    // 0xc45494: b.ne            #0xc454c8
    // 0xc45498: LoadField: r2 = r1->field_3f
    //     0xc45498: ldur            w2, [x1, #0x3f]
    // 0xc4549c: DecompressPointer r2
    //     0xc4549c: add             x2, x2, HEAP, lsl #32
    // 0xc454a0: cmp             w2, NULL
    // 0xc454a4: b.eq            #0xc45520
    // 0xc454a8: LoadField: r4 = r1->field_43
    //     0xc454a8: ldur            w4, [x1, #0x43]
    // 0xc454ac: DecompressPointer r4
    //     0xc454ac: add             x4, x4, HEAP, lsl #32
    // 0xc454b0: cmp             w4, NULL
    // 0xc454b4: b.eq            #0xc45524
    // 0xc454b8: LoadField: d0 = r2->field_7
    //     0xc454b8: ldur            d0, [x2, #7]
    // 0xc454bc: LoadField: d1 = r4->field_7
    //     0xc454bc: ldur            d1, [x4, #7]
    // 0xc454c0: r0 = getPageFromPixels()
    //     0xc454c0: bl              #0x67a638  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::getPageFromPixels
    // 0xc454c4: b               #0xc454cc
    // 0xc454c8: LoadField: d0 = r2->field_7
    //     0xc454c8: ldur            d0, [x2, #7]
    // 0xc454cc: r3 = inline_Allocate_Double()
    //     0xc454cc: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xc454d0: add             x3, x3, #0x10
    //     0xc454d4: cmp             x0, x3
    //     0xc454d8: b.ls            #0xc45528
    //     0xc454dc: str             x3, [THR, #0x50]  ; THR::top
    //     0xc454e0: sub             x3, x3, #0xf
    //     0xc454e4: movz            x0, #0xe15c
    //     0xc454e8: movk            x0, #0x3, lsl #16
    //     0xc454ec: stur            x0, [x3, #-1]
    // 0xc454f0: StoreField: r3->field_7 = d0
    //     0xc454f0: stur            d0, [x3, #7]
    // 0xc454f4: ldur            x1, [fp, #-0x20]
    // 0xc454f8: ldur            x2, [fp, #-0x18]
    // 0xc454fc: r0 = writeState()
    //     0xc454fc: bl              #0x679b9c  ; [package:flutter/src/widgets/page_storage.dart] PageStorageBucket::writeState
    // 0xc45500: r0 = Null
    //     0xc45500: mov             x0, NULL
    // 0xc45504: LeaveFrame
    //     0xc45504: mov             SP, fp
    //     0xc45508: ldp             fp, lr, [SP], #0x10
    // 0xc4550c: ret
    //     0xc4550c: ret             
    // 0xc45510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc45510: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc45514: b               #0xc45440
    // 0xc45518: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc45518: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc4551c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc4551c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc45520: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc45520: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc45524: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc45524: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc45528: SaveReg d0
    //     0xc45528: str             q0, [SP, #-0x10]!
    // 0xc4552c: r0 = AllocateDouble()
    //     0xc4552c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc45530: mov             x3, x0
    // 0xc45534: RestoreReg d0
    //     0xc45534: ldr             q0, [SP], #0x10
    // 0xc45538: b               #0xc454f0
  }
  _ absorb(/* No info */) {
    // ** addr: 0xcfe1cc, size: 0xa0
    // 0xcfe1cc: EnterFrame
    //     0xcfe1cc: stp             fp, lr, [SP, #-0x10]!
    //     0xcfe1d0: mov             fp, SP
    // 0xcfe1d4: AllocStack(0x10)
    //     0xcfe1d4: sub             SP, SP, #0x10
    // 0xcfe1d8: SetupParameters(_PagePosition this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xcfe1d8: mov             x3, x1
    //     0xcfe1dc: mov             x0, x2
    //     0xcfe1e0: stur            x1, [fp, #-8]
    //     0xcfe1e4: stur            x2, [fp, #-0x10]
    // 0xcfe1e8: CheckStackOverflow
    //     0xcfe1e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcfe1ec: cmp             SP, x16
    //     0xcfe1f0: b.ls            #0xcfe264
    // 0xcfe1f4: mov             x1, x3
    // 0xcfe1f8: mov             x2, x0
    // 0xcfe1fc: r0 = absorb()
    //     0xcfe1fc: bl              #0xcfe26c  ; [package:flutter/src/widgets/scroll_position_with_single_context.dart] ScrollPositionWithSingleContext::absorb
    // 0xcfe200: ldur            x1, [fp, #-0x10]
    // 0xcfe204: r2 = LoadClassIdInstr(r1)
    //     0xcfe204: ldur            x2, [x1, #-1]
    //     0xcfe208: ubfx            x2, x2, #0xc, #0x14
    // 0xcfe20c: cmp             x2, #0xe48
    // 0xcfe210: b.eq            #0xcfe224
    // 0xcfe214: r0 = Null
    //     0xcfe214: mov             x0, NULL
    // 0xcfe218: LeaveFrame
    //     0xcfe218: mov             SP, fp
    //     0xcfe21c: ldp             fp, lr, [SP], #0x10
    // 0xcfe220: ret
    //     0xcfe220: ret             
    // 0xcfe224: LoadField: r0 = r1->field_83
    //     0xcfe224: ldur            w0, [x1, #0x83]
    // 0xcfe228: DecompressPointer r0
    //     0xcfe228: add             x0, x0, HEAP, lsl #32
    // 0xcfe22c: cmp             w0, NULL
    // 0xcfe230: b.eq            #0xcfe254
    // 0xcfe234: ldur            x1, [fp, #-8]
    // 0xcfe238: StoreField: r1->field_83 = r0
    //     0xcfe238: stur            w0, [x1, #0x83]
    //     0xcfe23c: ldurb           w16, [x1, #-1]
    //     0xcfe240: ldurb           w17, [x0, #-1]
    //     0xcfe244: and             x16, x17, x16, lsr #2
    //     0xcfe248: tst             x16, HEAP, lsr #32
    //     0xcfe24c: b.eq            #0xcfe254
    //     0xcfe250: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcfe254: r0 = Null
    //     0xcfe254: mov             x0, NULL
    // 0xcfe258: LeaveFrame
    //     0xcfe258: mov             SP, fp
    //     0xcfe25c: ldp             fp, lr, [SP], #0x10
    // 0xcfe260: ret
    //     0xcfe260: ret             
    // 0xcfe264: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcfe264: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcfe268: b               #0xcfe1f4
  }
  _ applyViewportDimension(/* No info */) {
    // ** addr: 0xd882d8, size: 0x27c
    // 0xd882d8: EnterFrame
    //     0xd882d8: stp             fp, lr, [SP, #-0x10]!
    //     0xd882dc: mov             fp, SP
    // 0xd882e0: AllocStack(0x38)
    //     0xd882e0: sub             SP, SP, #0x38
    // 0xd882e4: SetupParameters(_PagePosition this /* r1 => r1, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x20 */)
    //     0xd882e4: stur            x1, [fp, #-0x10]
    //     0xd882e8: stur            d0, [fp, #-0x20]
    // 0xd882ec: CheckStackOverflow
    //     0xd882ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd882f0: cmp             SP, x16
    //     0xd882f4: b.ls            #0xd88500
    // 0xd882f8: LoadField: r0 = r1->field_43
    //     0xd882f8: ldur            w0, [x1, #0x43]
    // 0xd882fc: DecompressPointer r0
    //     0xd882fc: add             x0, x0, HEAP, lsl #32
    // 0xd88300: cmp             w0, NULL
    // 0xd88304: b.ne            #0xd8830c
    // 0xd88308: r0 = Null
    //     0xd88308: mov             x0, NULL
    // 0xd8830c: stur            x0, [fp, #-8]
    // 0xd88310: r2 = inline_Allocate_Double()
    //     0xd88310: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xd88314: add             x2, x2, #0x10
    //     0xd88318: cmp             x3, x2
    //     0xd8831c: b.ls            #0xd88508
    //     0xd88320: str             x2, [THR, #0x50]  ; THR::top
    //     0xd88324: sub             x2, x2, #0xf
    //     0xd88328: movz            x3, #0xe15c
    //     0xd8832c: movk            x3, #0x3, lsl #16
    //     0xd88330: stur            x3, [x2, #-1]
    // 0xd88334: StoreField: r2->field_7 = d0
    //     0xd88334: stur            d0, [x2, #7]
    // 0xd88338: stp             x0, x2, [SP]
    // 0xd8833c: r0 = ==()
    //     0xd8833c: bl              #0xd81600  ; [dart:core] _Double::==
    // 0xd88340: tbnz            w0, #4, #0xd88354
    // 0xd88344: r0 = true
    //     0xd88344: add             x0, NULL, #0x20  ; true
    // 0xd88348: LeaveFrame
    //     0xd88348: mov             SP, fp
    //     0xd8834c: ldp             fp, lr, [SP], #0x10
    // 0xd88350: ret
    //     0xd88350: ret             
    // 0xd88354: ldur            x0, [fp, #-0x10]
    // 0xd88358: mov             x1, x0
    // 0xd8835c: ldur            d0, [fp, #-0x20]
    // 0xd88360: r0 = applyViewportDimension()
    //     0xd88360: bl              #0xd88554  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::applyViewportDimension
    // 0xd88364: ldur            x1, [fp, #-0x10]
    // 0xd88368: LoadField: r0 = r1->field_3f
    //     0xd88368: ldur            w0, [x1, #0x3f]
    // 0xd8836c: DecompressPointer r0
    //     0xd8836c: add             x0, x0, HEAP, lsl #32
    // 0xd88370: cmp             w0, NULL
    // 0xd88374: b.eq            #0xd88380
    // 0xd88378: mov             x2, x0
    // 0xd8837c: b               #0xd88384
    // 0xd88380: r2 = Null
    //     0xd88380: mov             x2, NULL
    // 0xd88384: stur            x2, [fp, #-0x18]
    // 0xd88388: cmp             w2, NULL
    // 0xd8838c: b.ne            #0xd8839c
    // 0xd88390: LoadField: d0 = r1->field_7b
    //     0xd88390: ldur            d0, [x1, #0x7b]
    // 0xd88394: mov             v2.16b, v0.16b
    // 0xd88398: b               #0xd88404
    // 0xd8839c: ldur            x3, [fp, #-8]
    // 0xd883a0: r0 = LoadClassIdInstr(r3)
    //     0xd883a0: ldur            x0, [x3, #-1]
    //     0xd883a4: ubfx            x0, x0, #0xc, #0x14
    // 0xd883a8: r16 = 0.000000
    //     0xd883a8: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xd883ac: stp             x16, x3, [SP]
    // 0xd883b0: mov             lr, x0
    // 0xd883b4: ldr             lr, [x21, lr, lsl #3]
    // 0xd883b8: blr             lr
    // 0xd883bc: tbnz            w0, #4, #0xd883dc
    // 0xd883c0: ldur            x0, [fp, #-0x10]
    // 0xd883c4: LoadField: r1 = r0->field_83
    //     0xd883c4: ldur            w1, [x0, #0x83]
    // 0xd883c8: DecompressPointer r1
    //     0xd883c8: add             x1, x1, HEAP, lsl #32
    // 0xd883cc: cmp             w1, NULL
    // 0xd883d0: b.eq            #0xd88524
    // 0xd883d4: LoadField: d0 = r1->field_7
    //     0xd883d4: ldur            d0, [x1, #7]
    // 0xd883d8: b               #0xd88400
    // 0xd883dc: ldur            x0, [fp, #-0x10]
    // 0xd883e0: ldur            x1, [fp, #-8]
    // 0xd883e4: ldur            x2, [fp, #-0x18]
    // 0xd883e8: cmp             w1, NULL
    // 0xd883ec: b.eq            #0xd88528
    // 0xd883f0: LoadField: d0 = r2->field_7
    //     0xd883f0: ldur            d0, [x2, #7]
    // 0xd883f4: LoadField: d1 = r1->field_7
    //     0xd883f4: ldur            d1, [x1, #7]
    // 0xd883f8: mov             x1, x0
    // 0xd883fc: r0 = getPageFromPixels()
    //     0xd883fc: bl              #0x67a638  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::getPageFromPixels
    // 0xd88400: mov             v2.16b, v0.16b
    // 0xd88404: ldur            d1, [fp, #-0x20]
    // 0xd88408: ldur            x1, [fp, #-0x10]
    // 0xd8840c: mov             v0.16b, v2.16b
    // 0xd88410: stur            d2, [fp, #-0x28]
    // 0xd88414: r0 = getPixelsFromPage()
    //     0xd88414: bl              #0x8e9c14  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::getPixelsFromPage
    // 0xd88418: mov             v2.16b, v0.16b
    // 0xd8841c: ldur            d0, [fp, #-0x20]
    // 0xd88420: d1 = 0.000000
    //     0xd88420: eor             v1.16b, v1.16b, v1.16b
    // 0xd88424: fcmp            d0, d1
    // 0xd88428: b.ne            #0xd8845c
    // 0xd8842c: ldur            d0, [fp, #-0x28]
    // 0xd88430: r0 = inline_Allocate_Double()
    //     0xd88430: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xd88434: add             x0, x0, #0x10
    //     0xd88438: cmp             x1, x0
    //     0xd8843c: b.ls            #0xd8852c
    //     0xd88440: str             x0, [THR, #0x50]  ; THR::top
    //     0xd88444: sub             x0, x0, #0xf
    //     0xd88448: movz            x1, #0xe15c
    //     0xd8844c: movk            x1, #0x3, lsl #16
    //     0xd88450: stur            x1, [x0, #-1]
    // 0xd88454: StoreField: r0->field_7 = d0
    //     0xd88454: stur            d0, [x0, #7]
    // 0xd88458: b               #0xd88460
    // 0xd8845c: r0 = Null
    //     0xd8845c: mov             x0, NULL
    // 0xd88460: ldur            x1, [fp, #-0x10]
    // 0xd88464: StoreField: r1->field_83 = r0
    //     0xd88464: stur            w0, [x1, #0x83]
    //     0xd88468: ldurb           w16, [x1, #-1]
    //     0xd8846c: ldurb           w17, [x0, #-1]
    //     0xd88470: and             x16, x17, x16, lsr #2
    //     0xd88474: tst             x16, HEAP, lsr #32
    //     0xd88478: b.eq            #0xd88480
    //     0xd8847c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xd88480: r0 = inline_Allocate_Double()
    //     0xd88480: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xd88484: add             x0, x0, #0x10
    //     0xd88488: cmp             x2, x0
    //     0xd8848c: b.ls            #0xd8853c
    //     0xd88490: str             x0, [THR, #0x50]  ; THR::top
    //     0xd88494: sub             x0, x0, #0xf
    //     0xd88498: movz            x2, #0xe15c
    //     0xd8849c: movk            x2, #0x3, lsl #16
    //     0xd884a0: stur            x2, [x0, #-1]
    // 0xd884a4: StoreField: r0->field_7 = d2
    //     0xd884a4: stur            d2, [x0, #7]
    // 0xd884a8: stur            x0, [fp, #-8]
    // 0xd884ac: ldur            x16, [fp, #-0x18]
    // 0xd884b0: stp             x16, x0, [SP]
    // 0xd884b4: r0 = ==()
    //     0xd884b4: bl              #0xd81600  ; [dart:core] _Double::==
    // 0xd884b8: tbz             w0, #4, #0xd884f0
    // 0xd884bc: ldur            x1, [fp, #-0x10]
    // 0xd884c0: ldur            x0, [fp, #-8]
    // 0xd884c4: StoreField: r1->field_3f = r0
    //     0xd884c4: stur            w0, [x1, #0x3f]
    //     0xd884c8: ldurb           w16, [x1, #-1]
    //     0xd884cc: ldurb           w17, [x0, #-1]
    //     0xd884d0: and             x16, x17, x16, lsr #2
    //     0xd884d4: tst             x16, HEAP, lsr #32
    //     0xd884d8: b.eq            #0xd884e0
    //     0xd884dc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xd884e0: r0 = false
    //     0xd884e0: add             x0, NULL, #0x30  ; false
    // 0xd884e4: LeaveFrame
    //     0xd884e4: mov             SP, fp
    //     0xd884e8: ldp             fp, lr, [SP], #0x10
    // 0xd884ec: ret
    //     0xd884ec: ret             
    // 0xd884f0: r0 = true
    //     0xd884f0: add             x0, NULL, #0x20  ; true
    // 0xd884f4: LeaveFrame
    //     0xd884f4: mov             SP, fp
    //     0xd884f8: ldp             fp, lr, [SP], #0x10
    // 0xd884fc: ret
    //     0xd884fc: ret             
    // 0xd88500: r0 = StackOverflowSharedWithFPURegs()
    //     0xd88500: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd88504: b               #0xd882f8
    // 0xd88508: SaveReg d0
    //     0xd88508: str             q0, [SP, #-0x10]!
    // 0xd8850c: stp             x0, x1, [SP, #-0x10]!
    // 0xd88510: r0 = AllocateDouble()
    //     0xd88510: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd88514: mov             x2, x0
    // 0xd88518: ldp             x0, x1, [SP], #0x10
    // 0xd8851c: RestoreReg d0
    //     0xd8851c: ldr             q0, [SP], #0x10
    // 0xd88520: b               #0xd88334
    // 0xd88524: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd88524: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd88528: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd88528: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd8852c: stp             q0, q2, [SP, #-0x20]!
    // 0xd88530: r0 = AllocateDouble()
    //     0xd88530: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd88534: ldp             q0, q2, [SP], #0x20
    // 0xd88538: b               #0xd88454
    // 0xd8853c: SaveReg d2
    //     0xd8853c: str             q2, [SP, #-0x10]!
    // 0xd88540: SaveReg r1
    //     0xd88540: str             x1, [SP, #-8]!
    // 0xd88544: r0 = AllocateDouble()
    //     0xd88544: bl              #0xec2254  ; AllocateDoubleStub
    // 0xd88548: RestoreReg r1
    //     0xd88548: ldr             x1, [SP], #8
    // 0xd8854c: RestoreReg d2
    //     0xd8854c: ldr             q2, [SP], #0x10
    // 0xd88550: b               #0xd884a4
  }
  _ applyContentDimensions(/* No info */) {
    // ** addr: 0xd887c4, size: 0xbc
    // 0xd887c4: EnterFrame
    //     0xd887c4: stp             fp, lr, [SP, #-0x10]!
    //     0xd887c8: mov             fp, SP
    // 0xd887cc: AllocStack(0x20)
    //     0xd887cc: sub             SP, SP, #0x20
    // 0xd887d0: SetupParameters(_PagePosition this /* r1 => r0, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x10 */, dynamic _ /* d1 => d1, fp-0x18 */)
    //     0xd887d0: mov             x0, x1
    //     0xd887d4: stur            x1, [fp, #-8]
    //     0xd887d8: stur            d0, [fp, #-0x10]
    //     0xd887dc: stur            d1, [fp, #-0x18]
    // 0xd887e0: CheckStackOverflow
    //     0xd887e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd887e4: cmp             SP, x16
    //     0xd887e8: b.ls            #0xd88878
    // 0xd887ec: mov             x1, x0
    // 0xd887f0: r0 = _initialPageOffset()
    //     0xd887f0: bl              #0x67a770  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::_initialPageOffset
    // 0xd887f4: mov             v1.16b, v0.16b
    // 0xd887f8: ldur            d0, [fp, #-0x10]
    // 0xd887fc: fadd            d2, d0, d1
    // 0xd88800: ldur            x1, [fp, #-8]
    // 0xd88804: stur            d2, [fp, #-0x20]
    // 0xd88808: r0 = _initialPageOffset()
    //     0xd88808: bl              #0x67a770  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::_initialPageOffset
    // 0xd8880c: mov             v1.16b, v0.16b
    // 0xd88810: ldur            d0, [fp, #-0x18]
    // 0xd88814: fsub            d2, d0, d1
    // 0xd88818: ldur            d0, [fp, #-0x20]
    // 0xd8881c: fcmp            d0, d2
    // 0xd88820: b.le            #0xd8882c
    // 0xd88824: mov             v1.16b, v0.16b
    // 0xd88828: b               #0xd88864
    // 0xd8882c: fcmp            d2, d0
    // 0xd88830: b.le            #0xd8883c
    // 0xd88834: mov             v1.16b, v2.16b
    // 0xd88838: b               #0xd88864
    // 0xd8883c: d1 = 0.000000
    //     0xd8883c: eor             v1.16b, v1.16b, v1.16b
    // 0xd88840: fcmp            d0, d1
    // 0xd88844: b.ne            #0xd88850
    // 0xd88848: fadd            d1, d0, d2
    // 0xd8884c: b               #0xd88864
    // 0xd88850: fcmp            d2, d2
    // 0xd88854: b.vc            #0xd88860
    // 0xd88858: mov             v1.16b, v2.16b
    // 0xd8885c: b               #0xd88864
    // 0xd88860: mov             v1.16b, v0.16b
    // 0xd88864: ldur            x1, [fp, #-8]
    // 0xd88868: r0 = applyContentDimensions()
    //     0xd88868: bl              #0xd88880  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::applyContentDimensions
    // 0xd8886c: LeaveFrame
    //     0xd8886c: mov             SP, fp
    //     0xd88870: ldp             fp, lr, [SP], #0x10
    // 0xd88874: ret
    //     0xd88874: ret             
    // 0xd88878: r0 = StackOverflowSharedWithFPURegs()
    //     0xd88878: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xd8887c: b               #0xd887ec
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xd89084, size: 0x128
    // 0xd89084: EnterFrame
    //     0xd89084: stp             fp, lr, [SP, #-0x10]!
    //     0xd89088: mov             fp, SP
    // 0xd8908c: AllocStack(0x38)
    //     0xd8908c: sub             SP, SP, #0x38
    // 0xd89090: LoadField: r0 = r1->field_2f
    //     0xd89090: ldur            w0, [x1, #0x2f]
    // 0xd89094: DecompressPointer r0
    //     0xd89094: add             x0, x0, HEAP, lsl #32
    // 0xd89098: cmp             w0, NULL
    // 0xd8909c: b.eq            #0xd890b8
    // 0xd890a0: LoadField: r2 = r1->field_33
    //     0xd890a0: ldur            w2, [x1, #0x33]
    // 0xd890a4: DecompressPointer r2
    //     0xd890a4: add             x2, x2, HEAP, lsl #32
    // 0xd890a8: cmp             w2, NULL
    // 0xd890ac: b.eq            #0xd890b8
    // 0xd890b0: mov             x2, x0
    // 0xd890b4: b               #0xd890bc
    // 0xd890b8: r2 = Null
    //     0xd890b8: mov             x2, NULL
    // 0xd890bc: stur            x2, [fp, #-0x30]
    // 0xd890c0: cmp             w0, NULL
    // 0xd890c4: b.eq            #0xd890d8
    // 0xd890c8: LoadField: r0 = r1->field_33
    //     0xd890c8: ldur            w0, [x1, #0x33]
    // 0xd890cc: DecompressPointer r0
    //     0xd890cc: add             x0, x0, HEAP, lsl #32
    // 0xd890d0: cmp             w0, NULL
    // 0xd890d4: b.ne            #0xd890dc
    // 0xd890d8: r0 = Null
    //     0xd890d8: mov             x0, NULL
    // 0xd890dc: stur            x0, [fp, #-0x28]
    // 0xd890e0: LoadField: r3 = r1->field_3f
    //     0xd890e0: ldur            w3, [x1, #0x3f]
    // 0xd890e4: DecompressPointer r3
    //     0xd890e4: add             x3, x3, HEAP, lsl #32
    // 0xd890e8: cmp             w3, NULL
    // 0xd890ec: b.ne            #0xd890f4
    // 0xd890f0: r3 = Null
    //     0xd890f0: mov             x3, NULL
    // 0xd890f4: stur            x3, [fp, #-0x20]
    // 0xd890f8: LoadField: r4 = r1->field_43
    //     0xd890f8: ldur            w4, [x1, #0x43]
    // 0xd890fc: DecompressPointer r4
    //     0xd890fc: add             x4, x4, HEAP, lsl #32
    // 0xd89100: cmp             w4, NULL
    // 0xd89104: b.ne            #0xd8910c
    // 0xd89108: r4 = Null
    //     0xd89108: mov             x4, NULL
    // 0xd8910c: stur            x4, [fp, #-0x18]
    // 0xd89110: LoadField: r5 = r1->field_27
    //     0xd89110: ldur            w5, [x1, #0x27]
    // 0xd89114: DecompressPointer r5
    //     0xd89114: add             x5, x5, HEAP, lsl #32
    // 0xd89118: LoadField: r6 = r5->field_b
    //     0xd89118: ldur            w6, [x5, #0xb]
    // 0xd8911c: DecompressPointer r6
    //     0xd8911c: add             x6, x6, HEAP, lsl #32
    // 0xd89120: cmp             w6, NULL
    // 0xd89124: b.eq            #0xd891a0
    // 0xd89128: LoadField: r7 = r6->field_b
    //     0xd89128: ldur            w7, [x6, #0xb]
    // 0xd8912c: DecompressPointer r7
    //     0xd8912c: add             x7, x7, HEAP, lsl #32
    // 0xd89130: stur            x7, [fp, #-0x10]
    // 0xd89134: LoadField: d0 = r1->field_87
    //     0xd89134: ldur            d0, [x1, #0x87]
    // 0xd89138: stur            d0, [fp, #-0x38]
    // 0xd8913c: LoadField: r1 = r5->field_33
    //     0xd8913c: ldur            w1, [x5, #0x33]
    // 0xd89140: DecompressPointer r1
    //     0xd89140: add             x1, x1, HEAP, lsl #32
    // 0xd89144: r16 = Sentinel
    //     0xd89144: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd89148: cmp             w1, w16
    // 0xd8914c: b.eq            #0xd891a4
    // 0xd89150: stur            x1, [fp, #-8]
    // 0xd89154: r0 = PageMetrics()
    //     0xd89154: bl              #0xd891ac  ; AllocatePageMetricsStub -> PageMetrics (size=0x2c)
    // 0xd89158: ldur            d0, [fp, #-0x38]
    // 0xd8915c: StoreField: r0->field_23 = d0
    //     0xd8915c: stur            d0, [x0, #0x23]
    // 0xd89160: ldur            x1, [fp, #-0x10]
    // 0xd89164: ArrayStore: r0[0] = r1  ; List_4
    //     0xd89164: stur            w1, [x0, #0x17]
    // 0xd89168: ldur            x1, [fp, #-8]
    // 0xd8916c: LoadField: d0 = r1->field_7
    //     0xd8916c: ldur            d0, [x1, #7]
    // 0xd89170: StoreField: r0->field_1b = d0
    //     0xd89170: stur            d0, [x0, #0x1b]
    // 0xd89174: ldur            x1, [fp, #-0x30]
    // 0xd89178: StoreField: r0->field_7 = r1
    //     0xd89178: stur            w1, [x0, #7]
    // 0xd8917c: ldur            x1, [fp, #-0x28]
    // 0xd89180: StoreField: r0->field_b = r1
    //     0xd89180: stur            w1, [x0, #0xb]
    // 0xd89184: ldur            x1, [fp, #-0x20]
    // 0xd89188: StoreField: r0->field_f = r1
    //     0xd89188: stur            w1, [x0, #0xf]
    // 0xd8918c: ldur            x1, [fp, #-0x18]
    // 0xd89190: StoreField: r0->field_13 = r1
    //     0xd89190: stur            w1, [x0, #0x13]
    // 0xd89194: LeaveFrame
    //     0xd89194: mov             SP, fp
    //     0xd89198: ldp             fp, lr, [SP], #0x10
    // 0xd8919c: ret
    //     0xd8919c: ret             
    // 0xd891a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd891a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd891a4: r9 = _devicePixelRatio
    //     0xd891a4: ldr             x9, [PP, #0x6f10]  ; [pp+0x6f10] Field <ScrollableState._devicePixelRatio@338019050>: late (offset: 0x34)
    // 0xd891a8: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xd891a8: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
  }
}

// class id: 4194, size: 0x20, field offset: 0x14
class _PageViewState extends State<dynamic> {

  late PageController _controller; // offset: 0x1c

  _ initState(/* No info */) {
    // ** addr: 0x94491c, size: 0x6c
    // 0x94491c: EnterFrame
    //     0x94491c: stp             fp, lr, [SP, #-0x10]!
    //     0x944920: mov             fp, SP
    // 0x944924: AllocStack(0x8)
    //     0x944924: sub             SP, SP, #8
    // 0x944928: SetupParameters(_PageViewState this /* r1 => r0, fp-0x8 */)
    //     0x944928: mov             x0, x1
    //     0x94492c: stur            x1, [fp, #-8]
    // 0x944930: CheckStackOverflow
    //     0x944930: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944934: cmp             SP, x16
    //     0x944938: b.ls            #0x944974
    // 0x94493c: mov             x1, x0
    // 0x944940: r0 = _initController()
    //     0x944940: bl              #0x9449ac  ; [package:flutter/src/widgets/page_view.dart] _PageViewState::_initController
    // 0x944944: ldur            x1, [fp, #-8]
    // 0x944948: LoadField: r2 = r1->field_1b
    //     0x944948: ldur            w2, [x1, #0x1b]
    // 0x94494c: DecompressPointer r2
    //     0x94494c: add             x2, x2, HEAP, lsl #32
    // 0x944950: r16 = Sentinel
    //     0x944950: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x944954: cmp             w2, w16
    // 0x944958: b.eq            #0x94497c
    // 0x94495c: LoadField: r3 = r2->field_3f
    //     0x94495c: ldur            x3, [x2, #0x3f]
    // 0x944960: StoreField: r1->field_13 = r3
    //     0x944960: stur            x3, [x1, #0x13]
    // 0x944964: r0 = Null
    //     0x944964: mov             x0, NULL
    // 0x944968: LeaveFrame
    //     0x944968: mov             SP, fp
    //     0x94496c: ldp             fp, lr, [SP], #0x10
    // 0x944970: ret
    //     0x944970: ret             
    // 0x944974: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944974: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944978: b               #0x94493c
    // 0x94497c: r9 = _controller
    //     0x94497c: add             x9, PP, #0x45, lsl #12  ; [pp+0x45e78] Field <_PageViewState@310030489._controller@310030489>: late (offset: 0x1c)
    //     0x944980: ldr             x9, [x9, #0xe78]
    // 0x944984: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x944984: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _initController(/* No info */) {
    // ** addr: 0x9449ac, size: 0xac
    // 0x9449ac: EnterFrame
    //     0x9449ac: stp             fp, lr, [SP, #-0x10]!
    //     0x9449b0: mov             fp, SP
    // 0x9449b4: AllocStack(0x10)
    //     0x9449b4: sub             SP, SP, #0x10
    // 0x9449b8: SetupParameters(_PageViewState this /* r1 => r1, fp-0x8 */)
    //     0x9449b8: stur            x1, [fp, #-8]
    // 0x9449bc: CheckStackOverflow
    //     0x9449bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9449c0: cmp             SP, x16
    //     0x9449c4: b.ls            #0x944a4c
    // 0x9449c8: LoadField: r0 = r1->field_b
    //     0x9449c8: ldur            w0, [x1, #0xb]
    // 0x9449cc: DecompressPointer r0
    //     0x9449cc: add             x0, x0, HEAP, lsl #32
    // 0x9449d0: cmp             w0, NULL
    // 0x9449d4: b.eq            #0x944a54
    // 0x9449d8: LoadField: r2 = r0->field_1b
    //     0x9449d8: ldur            w2, [x0, #0x1b]
    // 0x9449dc: DecompressPointer r2
    //     0x9449dc: add             x2, x2, HEAP, lsl #32
    // 0x9449e0: cmp             w2, NULL
    // 0x9449e4: b.ne            #0x944a18
    // 0x9449e8: r0 = PageController()
    //     0x9449e8: bl              #0x80f964  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x9449ec: stur            x0, [fp, #-0x10]
    // 0x9449f0: StoreField: r0->field_3f = rZR
    //     0x9449f0: stur            xzr, [x0, #0x3f]
    // 0x9449f4: r1 = true
    //     0x9449f4: add             x1, NULL, #0x20  ; true
    // 0x9449f8: StoreField: r0->field_47 = r1
    //     0x9449f8: stur            w1, [x0, #0x47]
    // 0x9449fc: d0 = 1.000000
    //     0x9449fc: fmov            d0, #1.00000000
    // 0x944a00: StoreField: r0->field_4b = d0
    //     0x944a00: stur            d0, [x0, #0x4b]
    // 0x944a04: mov             x1, x0
    // 0x944a08: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x944a08: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x944a0c: r0 = ScrollController()
    //     0x944a0c: bl              #0x685160  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x944a10: ldur            x0, [fp, #-0x10]
    // 0x944a14: b               #0x944a1c
    // 0x944a18: mov             x0, x2
    // 0x944a1c: ldur            x1, [fp, #-8]
    // 0x944a20: StoreField: r1->field_1b = r0
    //     0x944a20: stur            w0, [x1, #0x1b]
    //     0x944a24: ldurb           w16, [x1, #-1]
    //     0x944a28: ldurb           w17, [x0, #-1]
    //     0x944a2c: and             x16, x17, x16, lsr #2
    //     0x944a30: tst             x16, HEAP, lsr #32
    //     0x944a34: b.eq            #0x944a3c
    //     0x944a38: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x944a3c: r0 = Null
    //     0x944a3c: mov             x0, NULL
    // 0x944a40: LeaveFrame
    //     0x944a40: mov             SP, fp
    //     0x944a44: ldp             fp, lr, [SP], #0x10
    // 0x944a48: ret
    //     0x944a48: ret             
    // 0x944a4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944a4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944a50: b               #0x9449c8
    // 0x944a54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x944a54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x995300, size: 0x120
    // 0x995300: EnterFrame
    //     0x995300: stp             fp, lr, [SP, #-0x10]!
    //     0x995304: mov             fp, SP
    // 0x995308: AllocStack(0x10)
    //     0x995308: sub             SP, SP, #0x10
    // 0x99530c: SetupParameters(_PageViewState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x99530c: mov             x4, x1
    //     0x995310: mov             x3, x2
    //     0x995314: stur            x1, [fp, #-8]
    //     0x995318: stur            x2, [fp, #-0x10]
    // 0x99531c: CheckStackOverflow
    //     0x99531c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x995320: cmp             SP, x16
    //     0x995324: b.ls            #0x995408
    // 0x995328: mov             x0, x3
    // 0x99532c: r2 = Null
    //     0x99532c: mov             x2, NULL
    // 0x995330: r1 = Null
    //     0x995330: mov             x1, NULL
    // 0x995334: r4 = 60
    //     0x995334: movz            x4, #0x3c
    // 0x995338: branchIfSmi(r0, 0x995344)
    //     0x995338: tbz             w0, #0, #0x995344
    // 0x99533c: r4 = LoadClassIdInstr(r0)
    //     0x99533c: ldur            x4, [x0, #-1]
    //     0x995340: ubfx            x4, x4, #0xc, #0x14
    // 0x995344: r17 = 4770
    //     0x995344: movz            x17, #0x12a2
    // 0x995348: cmp             x4, x17
    // 0x99534c: b.eq            #0x995364
    // 0x995350: r8 = PageView
    //     0x995350: add             x8, PP, #0x45, lsl #12  ; [pp+0x45ea8] Type: PageView
    //     0x995354: ldr             x8, [x8, #0xea8]
    // 0x995358: r3 = Null
    //     0x995358: add             x3, PP, #0x45, lsl #12  ; [pp+0x45eb0] Null
    //     0x99535c: ldr             x3, [x3, #0xeb0]
    // 0x995360: r0 = PageView()
    //     0x995360: bl              #0x944988  ; IsType_PageView_Stub
    // 0x995364: ldur            x0, [fp, #-0x10]
    // 0x995368: LoadField: r1 = r0->field_1b
    //     0x995368: ldur            w1, [x0, #0x1b]
    // 0x99536c: DecompressPointer r1
    //     0x99536c: add             x1, x1, HEAP, lsl #32
    // 0x995370: ldur            x2, [fp, #-8]
    // 0x995374: LoadField: r3 = r2->field_b
    //     0x995374: ldur            w3, [x2, #0xb]
    // 0x995378: DecompressPointer r3
    //     0x995378: add             x3, x3, HEAP, lsl #32
    // 0x99537c: cmp             w3, NULL
    // 0x995380: b.eq            #0x995410
    // 0x995384: LoadField: r4 = r3->field_1b
    //     0x995384: ldur            w4, [x3, #0x1b]
    // 0x995388: DecompressPointer r4
    //     0x995388: add             x4, x4, HEAP, lsl #32
    // 0x99538c: cmp             w1, w4
    // 0x995390: b.eq            #0x9953bc
    // 0x995394: cmp             w1, NULL
    // 0x995398: b.ne            #0x9953b4
    // 0x99539c: LoadField: r1 = r2->field_1b
    //     0x99539c: ldur            w1, [x2, #0x1b]
    // 0x9953a0: DecompressPointer r1
    //     0x9953a0: add             x1, x1, HEAP, lsl #32
    // 0x9953a4: r16 = Sentinel
    //     0x9953a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9953a8: cmp             w1, w16
    // 0x9953ac: b.eq            #0x995414
    // 0x9953b0: r0 = dispose()
    //     0x9953b0: bl              #0xa876d8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0x9953b4: ldur            x1, [fp, #-8]
    // 0x9953b8: r0 = _initController()
    //     0x9953b8: bl              #0x9449ac  ; [package:flutter/src/widgets/page_view.dart] _PageViewState::_initController
    // 0x9953bc: ldur            x0, [fp, #-8]
    // 0x9953c0: LoadField: r2 = r0->field_7
    //     0x9953c0: ldur            w2, [x0, #7]
    // 0x9953c4: DecompressPointer r2
    //     0x9953c4: add             x2, x2, HEAP, lsl #32
    // 0x9953c8: ldur            x0, [fp, #-0x10]
    // 0x9953cc: r1 = Null
    //     0x9953cc: mov             x1, NULL
    // 0x9953d0: cmp             w2, NULL
    // 0x9953d4: b.eq            #0x9953f8
    // 0x9953d8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9953d8: ldur            w4, [x2, #0x17]
    // 0x9953dc: DecompressPointer r4
    //     0x9953dc: add             x4, x4, HEAP, lsl #32
    // 0x9953e0: r8 = X0 bound StatefulWidget
    //     0x9953e0: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9953e4: ldr             x8, [x8, #0x7f8]
    // 0x9953e8: LoadField: r9 = r4->field_7
    //     0x9953e8: ldur            x9, [x4, #7]
    // 0x9953ec: r3 = Null
    //     0x9953ec: add             x3, PP, #0x45, lsl #12  ; [pp+0x45ec0] Null
    //     0x9953f0: ldr             x3, [x3, #0xec0]
    // 0x9953f4: blr             x9
    // 0x9953f8: r0 = Null
    //     0x9953f8: mov             x0, NULL
    // 0x9953fc: LeaveFrame
    //     0x9953fc: mov             SP, fp
    //     0x995400: ldp             fp, lr, [SP], #0x10
    // 0x995404: ret
    //     0x995404: ret             
    // 0x995408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x995408: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99540c: b               #0x995328
    // 0x995410: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x995410: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x995414: r9 = _controller
    //     0x995414: add             x9, PP, #0x45, lsl #12  ; [pp+0x45e78] Field <_PageViewState@310030489._controller@310030489>: late (offset: 0x1c)
    //     0x995418: ldr             x9, [x9, #0xe78]
    // 0x99541c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x99541c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa1b590, size: 0x250
    // 0xa1b590: EnterFrame
    //     0xa1b590: stp             fp, lr, [SP, #-0x10]!
    //     0xa1b594: mov             fp, SP
    // 0xa1b598: AllocStack(0x38)
    //     0xa1b598: sub             SP, SP, #0x38
    // 0xa1b59c: SetupParameters(_PageViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa1b59c: stur            x1, [fp, #-8]
    //     0xa1b5a0: stur            x2, [fp, #-0x10]
    // 0xa1b5a4: CheckStackOverflow
    //     0xa1b5a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1b5a8: cmp             SP, x16
    //     0xa1b5ac: b.ls            #0xa1b7c4
    // 0xa1b5b0: r1 = 2
    //     0xa1b5b0: movz            x1, #0x2
    // 0xa1b5b4: r0 = AllocateContext()
    //     0xa1b5b4: bl              #0xec126c  ; AllocateContextStub
    // 0xa1b5b8: mov             x3, x0
    // 0xa1b5bc: ldur            x0, [fp, #-8]
    // 0xa1b5c0: stur            x3, [fp, #-0x18]
    // 0xa1b5c4: StoreField: r3->field_f = r0
    //     0xa1b5c4: stur            w0, [x3, #0xf]
    // 0xa1b5c8: mov             x1, x0
    // 0xa1b5cc: ldur            x2, [fp, #-0x10]
    // 0xa1b5d0: r0 = _getDirection()
    //     0xa1b5d0: bl              #0xa1b7ec  ; [package:flutter/src/widgets/page_view.dart] _PageViewState::_getDirection
    // 0xa1b5d4: mov             x1, x0
    // 0xa1b5d8: ldur            x2, [fp, #-0x18]
    // 0xa1b5dc: stur            x1, [fp, #-0x28]
    // 0xa1b5e0: StoreField: r2->field_13 = r0
    //     0xa1b5e0: stur            w0, [x2, #0x13]
    //     0xa1b5e4: ldurb           w16, [x2, #-1]
    //     0xa1b5e8: ldurb           w17, [x0, #-1]
    //     0xa1b5ec: and             x16, x17, x16, lsr #2
    //     0xa1b5f0: tst             x16, HEAP, lsr #32
    //     0xa1b5f4: b.eq            #0xa1b5fc
    //     0xa1b5f8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa1b5fc: ldur            x0, [fp, #-8]
    // 0xa1b600: LoadField: r3 = r0->field_b
    //     0xa1b600: ldur            w3, [x0, #0xb]
    // 0xa1b604: DecompressPointer r3
    //     0xa1b604: add             x3, x3, HEAP, lsl #32
    // 0xa1b608: stur            x3, [fp, #-0x20]
    // 0xa1b60c: cmp             w3, NULL
    // 0xa1b610: b.eq            #0xa1b7cc
    // 0xa1b614: r0 = _ForceImplicitScrollPhysics()
    //     0xa1b614: bl              #0xa1b7e0  ; Allocate_ForceImplicitScrollPhysicsStub -> _ForceImplicitScrollPhysics (size=0x10)
    // 0xa1b618: mov             x3, x0
    // 0xa1b61c: r0 = false
    //     0xa1b61c: add             x0, NULL, #0x30  ; false
    // 0xa1b620: stur            x3, [fp, #-0x30]
    // 0xa1b624: StoreField: r3->field_b = r0
    //     0xa1b624: stur            w0, [x3, #0xb]
    // 0xa1b628: ldur            x1, [fp, #-0x20]
    // 0xa1b62c: LoadField: r2 = r1->field_1f
    //     0xa1b62c: ldur            w2, [x1, #0x1f]
    // 0xa1b630: DecompressPointer r2
    //     0xa1b630: add             x2, x2, HEAP, lsl #32
    // 0xa1b634: cmp             w2, NULL
    // 0xa1b638: b.ne            #0xa1b664
    // 0xa1b63c: LoadField: r2 = r1->field_3b
    //     0xa1b63c: ldur            w2, [x1, #0x3b]
    // 0xa1b640: DecompressPointer r2
    //     0xa1b640: add             x2, x2, HEAP, lsl #32
    // 0xa1b644: cmp             w2, NULL
    // 0xa1b648: b.ne            #0xa1b654
    // 0xa1b64c: r0 = Null
    //     0xa1b64c: mov             x0, NULL
    // 0xa1b650: b               #0xa1b660
    // 0xa1b654: mov             x1, x2
    // 0xa1b658: ldur            x2, [fp, #-0x10]
    // 0xa1b65c: r0 = getScrollPhysics()
    //     0xa1b65c: bl              #0xd44718  ; [package:flutter/src/widgets/scroll_configuration.dart] _WrappedScrollBehavior::getScrollPhysics
    // 0xa1b660: mov             x2, x0
    // 0xa1b664: ldur            x0, [fp, #-8]
    // 0xa1b668: r1 = Instance_PageScrollPhysics
    //     0xa1b668: add             x1, PP, #0x43, lsl #12  ; [pp+0x43c00] Obj!PageScrollPhysics@e0fd91
    //     0xa1b66c: ldr             x1, [x1, #0xc00]
    // 0xa1b670: r0 = applyTo()
    //     0xa1b670: bl              #0xda3054  ; [package:flutter/src/widgets/page_view.dart] PageScrollPhysics::applyTo
    // 0xa1b674: ldur            x1, [fp, #-0x30]
    // 0xa1b678: mov             x2, x0
    // 0xa1b67c: r0 = applyTo()
    //     0xa1b67c: bl              #0xda2f84  ; [package:flutter/src/widgets/page_view.dart] _ForceImplicitScrollPhysics::applyTo
    // 0xa1b680: mov             x2, x0
    // 0xa1b684: ldur            x0, [fp, #-8]
    // 0xa1b688: stur            x2, [fp, #-0x30]
    // 0xa1b68c: LoadField: r1 = r0->field_b
    //     0xa1b68c: ldur            w1, [x0, #0xb]
    // 0xa1b690: DecompressPointer r1
    //     0xa1b690: add             x1, x1, HEAP, lsl #32
    // 0xa1b694: cmp             w1, NULL
    // 0xa1b698: b.eq            #0xa1b7d0
    // 0xa1b69c: LoadField: r3 = r0->field_1b
    //     0xa1b69c: ldur            w3, [x0, #0x1b]
    // 0xa1b6a0: DecompressPointer r3
    //     0xa1b6a0: add             x3, x3, HEAP, lsl #32
    // 0xa1b6a4: r16 = Sentinel
    //     0xa1b6a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa1b6a8: cmp             w3, w16
    // 0xa1b6ac: b.eq            #0xa1b7d4
    // 0xa1b6b0: stur            x3, [fp, #-0x20]
    // 0xa1b6b4: LoadField: r0 = r1->field_3b
    //     0xa1b6b4: ldur            w0, [x1, #0x3b]
    // 0xa1b6b8: DecompressPointer r0
    //     0xa1b6b8: add             x0, x0, HEAP, lsl #32
    // 0xa1b6bc: cmp             w0, NULL
    // 0xa1b6c0: b.ne            #0xa1b704
    // 0xa1b6c4: ldur            x1, [fp, #-0x10]
    // 0xa1b6c8: r0 = of()
    //     0xa1b6c8: bl              #0x999ac0  ; [package:flutter/src/widgets/scroll_configuration.dart] ScrollConfiguration::of
    // 0xa1b6cc: r1 = LoadClassIdInstr(r0)
    //     0xa1b6cc: ldur            x1, [x0, #-1]
    //     0xa1b6d0: ubfx            x1, x1, #0xc, #0x14
    // 0xa1b6d4: r16 = false
    //     0xa1b6d4: add             x16, NULL, #0x30  ; false
    // 0xa1b6d8: str             x16, [SP]
    // 0xa1b6dc: mov             x16, x0
    // 0xa1b6e0: mov             x0, x1
    // 0xa1b6e4: mov             x1, x16
    // 0xa1b6e8: r4 = const [0, 0x2, 0x1, 0x1, scrollbars, 0x1, null]
    //     0xa1b6e8: add             x4, PP, #0x45, lsl #12  ; [pp+0x45e60] List(7) [0, 0x2, 0x1, 0x1, "scrollbars", 0x1, Null]
    //     0xa1b6ec: ldr             x4, [x4, #0xe60]
    // 0xa1b6f0: r0 = GDT[cid_x0 + -0xffc]()
    //     0xa1b6f0: sub             lr, x0, #0xffc
    //     0xa1b6f4: ldr             lr, [x21, lr, lsl #3]
    //     0xa1b6f8: blr             lr
    // 0xa1b6fc: mov             x3, x0
    // 0xa1b700: b               #0xa1b708
    // 0xa1b704: mov             x3, x0
    // 0xa1b708: ldur            x2, [fp, #-0x28]
    // 0xa1b70c: ldur            x0, [fp, #-0x30]
    // 0xa1b710: ldur            x1, [fp, #-0x20]
    // 0xa1b714: stur            x3, [fp, #-8]
    // 0xa1b718: r0 = Scrollable()
    //     0xa1b718: bl              #0xa12c1c  ; AllocateScrollableStub -> Scrollable (size=0x3c)
    // 0xa1b71c: mov             x3, x0
    // 0xa1b720: ldur            x0, [fp, #-0x28]
    // 0xa1b724: stur            x3, [fp, #-0x10]
    // 0xa1b728: StoreField: r3->field_b = r0
    //     0xa1b728: stur            w0, [x3, #0xb]
    // 0xa1b72c: ldur            x0, [fp, #-0x20]
    // 0xa1b730: StoreField: r3->field_f = r0
    //     0xa1b730: stur            w0, [x3, #0xf]
    // 0xa1b734: ldur            x0, [fp, #-0x30]
    // 0xa1b738: StoreField: r3->field_13 = r0
    //     0xa1b738: stur            w0, [x3, #0x13]
    // 0xa1b73c: ldur            x2, [fp, #-0x18]
    // 0xa1b740: r1 = Function '<anonymous closure>':.
    //     0xa1b740: add             x1, PP, #0x45, lsl #12  ; [pp+0x45e68] AnonymousClosure: (0xa1bc04), in [package:flutter/src/widgets/page_view.dart] _PageViewState::build (0xa1b590)
    //     0xa1b744: ldr             x1, [x1, #0xe68]
    // 0xa1b748: r0 = AllocateClosure()
    //     0xa1b748: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1b74c: mov             x1, x0
    // 0xa1b750: ldur            x0, [fp, #-0x10]
    // 0xa1b754: ArrayStore: r0[0] = r1  ; List_4
    //     0xa1b754: stur            w1, [x0, #0x17]
    // 0xa1b758: r1 = false
    //     0xa1b758: add             x1, NULL, #0x30  ; false
    // 0xa1b75c: StoreField: r0->field_1f = r1
    //     0xa1b75c: stur            w1, [x0, #0x1f]
    // 0xa1b760: r1 = Instance_DragStartBehavior
    //     0xa1b760: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xa1b764: StoreField: r0->field_2b = r1
    //     0xa1b764: stur            w1, [x0, #0x2b]
    // 0xa1b768: ldur            x1, [fp, #-8]
    // 0xa1b76c: StoreField: r0->field_33 = r1
    //     0xa1b76c: stur            w1, [x0, #0x33]
    // 0xa1b770: r1 = Instance_Clip
    //     0xa1b770: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa1b774: ldr             x1, [x1, #0x7c0]
    // 0xa1b778: StoreField: r0->field_37 = r1
    //     0xa1b778: stur            w1, [x0, #0x37]
    // 0xa1b77c: r1 = Instance_HitTestBehavior
    //     0xa1b77c: add             x1, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xa1b780: ldr             x1, [x1, #0x1c8]
    // 0xa1b784: StoreField: r0->field_23 = r1
    //     0xa1b784: stur            w1, [x0, #0x23]
    // 0xa1b788: ldur            x2, [fp, #-0x18]
    // 0xa1b78c: r1 = Function '<anonymous closure>':.
    //     0xa1b78c: add             x1, PP, #0x45, lsl #12  ; [pp+0x45e70] AnonymousClosure: (0xa1b8f4), in [package:flutter/src/widgets/page_view.dart] _PageViewState::build (0xa1b590)
    //     0xa1b790: ldr             x1, [x1, #0xe70]
    // 0xa1b794: r0 = AllocateClosure()
    //     0xa1b794: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1b798: r1 = <ScrollNotification>
    //     0xa1b798: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xa1b79c: ldr             x1, [x1, #0x110]
    // 0xa1b7a0: stur            x0, [fp, #-8]
    // 0xa1b7a4: r0 = NotificationListener()
    //     0xa1b7a4: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xa1b7a8: ldur            x1, [fp, #-8]
    // 0xa1b7ac: StoreField: r0->field_13 = r1
    //     0xa1b7ac: stur            w1, [x0, #0x13]
    // 0xa1b7b0: ldur            x1, [fp, #-0x10]
    // 0xa1b7b4: StoreField: r0->field_b = r1
    //     0xa1b7b4: stur            w1, [x0, #0xb]
    // 0xa1b7b8: LeaveFrame
    //     0xa1b7b8: mov             SP, fp
    //     0xa1b7bc: ldp             fp, lr, [SP], #0x10
    // 0xa1b7c0: ret
    //     0xa1b7c0: ret             
    // 0xa1b7c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1b7c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1b7c8: b               #0xa1b5b0
    // 0xa1b7cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1b7cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1b7d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1b7d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1b7d4: r9 = _controller
    //     0xa1b7d4: add             x9, PP, #0x45, lsl #12  ; [pp+0x45e78] Field <_PageViewState@310030489._controller@310030489>: late (offset: 0x1c)
    //     0xa1b7d8: ldr             x9, [x9, #0xe78]
    // 0xa1b7dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa1b7dc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _getDirection(/* No info */) {
    // ** addr: 0xa1b7ec, size: 0x108
    // 0xa1b7ec: EnterFrame
    //     0xa1b7ec: stp             fp, lr, [SP, #-0x10]!
    //     0xa1b7f0: mov             fp, SP
    // 0xa1b7f4: AllocStack(0x8)
    //     0xa1b7f4: sub             SP, SP, #8
    // 0xa1b7f8: SetupParameters(_PageViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0xa1b7f8: mov             x0, x1
    //     0xa1b7fc: stur            x1, [fp, #-8]
    //     0xa1b800: mov             x1, x2
    // 0xa1b804: CheckStackOverflow
    //     0xa1b804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1b808: cmp             SP, x16
    //     0xa1b80c: b.ls            #0xa1b8e4
    // 0xa1b810: LoadField: r2 = r0->field_b
    //     0xa1b810: ldur            w2, [x0, #0xb]
    // 0xa1b814: DecompressPointer r2
    //     0xa1b814: add             x2, x2, HEAP, lsl #32
    // 0xa1b818: cmp             w2, NULL
    // 0xa1b81c: b.eq            #0xa1b8ec
    // 0xa1b820: LoadField: r3 = r2->field_13
    //     0xa1b820: ldur            w3, [x2, #0x13]
    // 0xa1b824: DecompressPointer r3
    //     0xa1b824: add             x3, x3, HEAP, lsl #32
    // 0xa1b828: LoadField: r4 = r3->field_7
    //     0xa1b828: ldur            x4, [x3, #7]
    // 0xa1b82c: cmp             x4, #0
    // 0xa1b830: b.gt            #0xa1b8c0
    // 0xa1b834: r0 = of()
    //     0xa1b834: bl              #0x6b0ae8  ; [package:flutter/src/widgets/basic.dart] Directionality::of
    // 0xa1b838: LoadField: r1 = r0->field_7
    //     0xa1b838: ldur            x1, [x0, #7]
    // 0xa1b83c: cmp             x1, #0
    // 0xa1b840: b.gt            #0xa1b84c
    // 0xa1b844: r3 = Instance_AxisDirection
    //     0xa1b844: ldr             x3, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0xa1b848: b               #0xa1b850
    // 0xa1b84c: r3 = Instance_AxisDirection
    //     0xa1b84c: ldr             x3, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0xa1b850: ldur            x1, [fp, #-8]
    // 0xa1b854: LoadField: r4 = r1->field_b
    //     0xa1b854: ldur            w4, [x1, #0xb]
    // 0xa1b858: DecompressPointer r4
    //     0xa1b858: add             x4, x4, HEAP, lsl #32
    // 0xa1b85c: cmp             w4, NULL
    // 0xa1b860: b.eq            #0xa1b8f0
    // 0xa1b864: ArrayLoad: r1 = r4[0]  ; List_4
    //     0xa1b864: ldur            w1, [x4, #0x17]
    // 0xa1b868: DecompressPointer r1
    //     0xa1b868: add             x1, x1, HEAP, lsl #32
    // 0xa1b86c: tbnz            w1, #4, #0xa1b8b0
    // 0xa1b870: LoadField: r1 = r3->field_7
    //     0xa1b870: ldur            x1, [x3, #7]
    // 0xa1b874: cmp             x1, #1
    // 0xa1b878: b.gt            #0xa1b894
    // 0xa1b87c: cmp             x1, #0
    // 0xa1b880: b.gt            #0xa1b88c
    // 0xa1b884: r1 = Instance_AxisDirection
    //     0xa1b884: ldr             x1, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xa1b888: b               #0xa1b8a8
    // 0xa1b88c: r1 = Instance_AxisDirection
    //     0xa1b88c: ldr             x1, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0xa1b890: b               #0xa1b8a8
    // 0xa1b894: cmp             x1, #2
    // 0xa1b898: b.gt            #0xa1b8a4
    // 0xa1b89c: r1 = Instance_AxisDirection
    //     0xa1b89c: ldr             x1, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xa1b8a0: b               #0xa1b8a8
    // 0xa1b8a4: r1 = Instance_AxisDirection
    //     0xa1b8a4: ldr             x1, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0xa1b8a8: mov             x0, x1
    // 0xa1b8ac: b               #0xa1b8b4
    // 0xa1b8b0: mov             x0, x3
    // 0xa1b8b4: LeaveFrame
    //     0xa1b8b4: mov             SP, fp
    //     0xa1b8b8: ldp             fp, lr, [SP], #0x10
    // 0xa1b8bc: ret
    //     0xa1b8bc: ret             
    // 0xa1b8c0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa1b8c0: ldur            w1, [x2, #0x17]
    // 0xa1b8c4: DecompressPointer r1
    //     0xa1b8c4: add             x1, x1, HEAP, lsl #32
    // 0xa1b8c8: tbnz            w1, #4, #0xa1b8d4
    // 0xa1b8cc: r0 = Instance_AxisDirection
    //     0xa1b8cc: ldr             x0, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xa1b8d0: b               #0xa1b8d8
    // 0xa1b8d4: r0 = Instance_AxisDirection
    //     0xa1b8d4: ldr             x0, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xa1b8d8: LeaveFrame
    //     0xa1b8d8: mov             SP, fp
    //     0xa1b8dc: ldp             fp, lr, [SP], #0x10
    // 0xa1b8e0: ret
    //     0xa1b8e0: ret             
    // 0xa1b8e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1b8e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1b8e8: b               #0xa1b810
    // 0xa1b8ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1b8ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1b8f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1b8f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, ScrollNotification) {
    // ** addr: 0xa1b8f4, size: 0x1a8
    // 0xa1b8f4: EnterFrame
    //     0xa1b8f4: stp             fp, lr, [SP, #-0x10]!
    //     0xa1b8f8: mov             fp, SP
    // 0xa1b8fc: AllocStack(0x20)
    //     0xa1b8fc: sub             SP, SP, #0x20
    // 0xa1b900: SetupParameters()
    //     0xa1b900: ldr             x0, [fp, #0x18]
    //     0xa1b904: ldur            w3, [x0, #0x17]
    //     0xa1b908: add             x3, x3, HEAP, lsl #32
    //     0xa1b90c: stur            x3, [fp, #-0x10]
    // 0xa1b910: CheckStackOverflow
    //     0xa1b910: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1b914: cmp             SP, x16
    //     0xa1b918: b.ls            #0xa1ba6c
    // 0xa1b91c: ldr             x0, [fp, #0x10]
    // 0xa1b920: LoadField: r1 = r0->field_7
    //     0xa1b920: ldur            x1, [x0, #7]
    // 0xa1b924: cbnz            x1, #0xa1ba5c
    // 0xa1b928: LoadField: r1 = r3->field_f
    //     0xa1b928: ldur            w1, [x3, #0xf]
    // 0xa1b92c: DecompressPointer r1
    //     0xa1b92c: add             x1, x1, HEAP, lsl #32
    // 0xa1b930: LoadField: r2 = r1->field_b
    //     0xa1b930: ldur            w2, [x1, #0xb]
    // 0xa1b934: DecompressPointer r2
    //     0xa1b934: add             x2, x2, HEAP, lsl #32
    // 0xa1b938: cmp             w2, NULL
    // 0xa1b93c: b.eq            #0xa1ba74
    // 0xa1b940: LoadField: r1 = r2->field_27
    //     0xa1b940: ldur            w1, [x2, #0x27]
    // 0xa1b944: DecompressPointer r1
    //     0xa1b944: add             x1, x1, HEAP, lsl #32
    // 0xa1b948: cmp             w1, NULL
    // 0xa1b94c: b.eq            #0xa1ba5c
    // 0xa1b950: r1 = LoadClassIdInstr(r0)
    //     0xa1b950: ldur            x1, [x0, #-1]
    //     0xa1b954: ubfx            x1, x1, #0xc, #0x14
    // 0xa1b958: cmp             x1, #0xaa1
    // 0xa1b95c: b.ne            #0xa1ba5c
    // 0xa1b960: LoadField: r4 = r0->field_f
    //     0xa1b960: ldur            w4, [x0, #0xf]
    // 0xa1b964: DecompressPointer r4
    //     0xa1b964: add             x4, x4, HEAP, lsl #32
    // 0xa1b968: mov             x0, x4
    // 0xa1b96c: stur            x4, [fp, #-8]
    // 0xa1b970: r2 = Null
    //     0xa1b970: mov             x2, NULL
    // 0xa1b974: r1 = Null
    //     0xa1b974: mov             x1, NULL
    // 0xa1b978: r4 = LoadClassIdInstr(r0)
    //     0xa1b978: ldur            x4, [x0, #-1]
    //     0xa1b97c: ubfx            x4, x4, #0xc, #0x14
    // 0xa1b980: cmp             x4, #0xa13
    // 0xa1b984: b.eq            #0xa1b9a4
    // 0xa1b988: cmp             x4, #0xe48
    // 0xa1b98c: b.eq            #0xa1b9a4
    // 0xa1b990: r8 = PageMetrics
    //     0xa1b990: add             x8, PP, #0x45, lsl #12  ; [pp+0x45e80] Type: PageMetrics
    //     0xa1b994: ldr             x8, [x8, #0xe80]
    // 0xa1b998: r3 = Null
    //     0xa1b998: add             x3, PP, #0x45, lsl #12  ; [pp+0x45e88] Null
    //     0xa1b99c: ldr             x3, [x3, #0xe88]
    // 0xa1b9a0: r0 = DefaultTypeTest()
    //     0xa1b9a0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xa1b9a4: ldur            x1, [fp, #-8]
    // 0xa1b9a8: r0 = page()
    //     0xa1b9a8: bl              #0xa1ba9c  ; [package:flutter/src/widgets/page_view.dart] PageMetrics::page
    // 0xa1b9ac: LoadField: d0 = r0->field_7
    //     0xa1b9ac: ldur            d0, [x0, #7]
    // 0xa1b9b0: stp             fp, lr, [SP, #-0x10]!
    // 0xa1b9b4: mov             fp, SP
    // 0xa1b9b8: CallRuntime_LibcRound(double) -> double
    //     0xa1b9b8: and             SP, SP, #0xfffffffffffffff0
    //     0xa1b9bc: mov             sp, SP
    //     0xa1b9c0: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xa1b9c4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xa1b9c8: blr             x16
    //     0xa1b9cc: movz            x16, #0x8
    //     0xa1b9d0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xa1b9d4: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xa1b9d8: sub             sp, x16, #1, lsl #12
    //     0xa1b9dc: mov             SP, fp
    //     0xa1b9e0: ldp             fp, lr, [SP], #0x10
    // 0xa1b9e4: fcmp            d0, d0
    // 0xa1b9e8: b.vs            #0xa1ba78
    // 0xa1b9ec: fcvtzs          x0, d0
    // 0xa1b9f0: asr             x16, x0, #0x1e
    // 0xa1b9f4: cmp             x16, x0, asr #63
    // 0xa1b9f8: b.ne            #0xa1ba78
    // 0xa1b9fc: lsl             x0, x0, #1
    // 0xa1ba00: ldur            x1, [fp, #-0x10]
    // 0xa1ba04: LoadField: r2 = r1->field_f
    //     0xa1ba04: ldur            w2, [x1, #0xf]
    // 0xa1ba08: DecompressPointer r2
    //     0xa1ba08: add             x2, x2, HEAP, lsl #32
    // 0xa1ba0c: LoadField: r1 = r2->field_13
    //     0xa1ba0c: ldur            x1, [x2, #0x13]
    // 0xa1ba10: r3 = LoadInt32Instr(r0)
    //     0xa1ba10: sbfx            x3, x0, #1, #0x1f
    //     0xa1ba14: tbz             w0, #0, #0xa1ba1c
    //     0xa1ba18: ldur            x3, [x0, #7]
    // 0xa1ba1c: cmp             x3, x1
    // 0xa1ba20: b.eq            #0xa1ba5c
    // 0xa1ba24: StoreField: r2->field_13 = r3
    //     0xa1ba24: stur            x3, [x2, #0x13]
    // 0xa1ba28: LoadField: r1 = r2->field_b
    //     0xa1ba28: ldur            w1, [x2, #0xb]
    // 0xa1ba2c: DecompressPointer r1
    //     0xa1ba2c: add             x1, x1, HEAP, lsl #32
    // 0xa1ba30: cmp             w1, NULL
    // 0xa1ba34: b.eq            #0xa1ba94
    // 0xa1ba38: LoadField: r2 = r1->field_27
    //     0xa1ba38: ldur            w2, [x1, #0x27]
    // 0xa1ba3c: DecompressPointer r2
    //     0xa1ba3c: add             x2, x2, HEAP, lsl #32
    // 0xa1ba40: cmp             w2, NULL
    // 0xa1ba44: b.eq            #0xa1ba98
    // 0xa1ba48: stp             x0, x2, [SP]
    // 0xa1ba4c: mov             x0, x2
    // 0xa1ba50: ClosureCall
    //     0xa1ba50: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa1ba54: ldur            x2, [x0, #0x1f]
    //     0xa1ba58: blr             x2
    // 0xa1ba5c: r0 = false
    //     0xa1ba5c: add             x0, NULL, #0x30  ; false
    // 0xa1ba60: LeaveFrame
    //     0xa1ba60: mov             SP, fp
    //     0xa1ba64: ldp             fp, lr, [SP], #0x10
    // 0xa1ba68: ret
    //     0xa1ba68: ret             
    // 0xa1ba6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1ba6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1ba70: b               #0xa1b91c
    // 0xa1ba74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1ba74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1ba78: SaveReg d0
    //     0xa1ba78: str             q0, [SP, #-0x10]!
    // 0xa1ba7c: r0 = 74
    //     0xa1ba7c: movz            x0, #0x4a
    // 0xa1ba80: r30 = DoubleToIntegerStub
    //     0xa1ba80: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xa1ba84: LoadField: r30 = r30->field_7
    //     0xa1ba84: ldur            lr, [lr, #7]
    // 0xa1ba88: blr             lr
    // 0xa1ba8c: RestoreReg d0
    //     0xa1ba8c: ldr             q0, [SP], #0x10
    // 0xa1ba90: b               #0xa1ba00
    // 0xa1ba94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1ba94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1ba98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1ba98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Viewport <anonymous closure>(dynamic, BuildContext, ViewportOffset) {
    // ** addr: 0xa1bc04, size: 0x128
    // 0xa1bc04: EnterFrame
    //     0xa1bc04: stp             fp, lr, [SP, #-0x10]!
    //     0xa1bc08: mov             fp, SP
    // 0xa1bc0c: AllocStack(0x20)
    //     0xa1bc0c: sub             SP, SP, #0x20
    // 0xa1bc10: SetupParameters()
    //     0xa1bc10: ldr             x0, [fp, #0x20]
    //     0xa1bc14: ldur            w1, [x0, #0x17]
    //     0xa1bc18: add             x1, x1, HEAP, lsl #32
    //     0xa1bc1c: stur            x1, [fp, #-0x10]
    // 0xa1bc20: LoadField: r0 = r1->field_f
    //     0xa1bc20: ldur            w0, [x1, #0xf]
    // 0xa1bc24: DecompressPointer r0
    //     0xa1bc24: add             x0, x0, HEAP, lsl #32
    // 0xa1bc28: LoadField: r2 = r0->field_b
    //     0xa1bc28: ldur            w2, [x0, #0xb]
    // 0xa1bc2c: DecompressPointer r2
    //     0xa1bc2c: add             x2, x2, HEAP, lsl #32
    // 0xa1bc30: cmp             w2, NULL
    // 0xa1bc34: b.eq            #0xa1bd1c
    // 0xa1bc38: LoadField: r3 = r0->field_1b
    //     0xa1bc38: ldur            w3, [x0, #0x1b]
    // 0xa1bc3c: DecompressPointer r3
    //     0xa1bc3c: add             x3, x3, HEAP, lsl #32
    // 0xa1bc40: r16 = Sentinel
    //     0xa1bc40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa1bc44: cmp             w3, w16
    // 0xa1bc48: b.eq            #0xa1bd20
    // 0xa1bc4c: LoadField: d0 = r3->field_4b
    //     0xa1bc4c: ldur            d0, [x3, #0x4b]
    // 0xa1bc50: stur            d0, [fp, #-0x20]
    // 0xa1bc54: LoadField: r0 = r2->field_2b
    //     0xa1bc54: ldur            w0, [x2, #0x2b]
    // 0xa1bc58: DecompressPointer r0
    //     0xa1bc58: add             x0, x0, HEAP, lsl #32
    // 0xa1bc5c: stur            x0, [fp, #-8]
    // 0xa1bc60: r0 = SliverFillViewport()
    //     0xa1bc60: bl              #0xa1bd38  ; AllocateSliverFillViewportStub -> SliverFillViewport (size=0x1c)
    // 0xa1bc64: mov             x3, x0
    // 0xa1bc68: ldur            x0, [fp, #-8]
    // 0xa1bc6c: stur            x3, [fp, #-0x18]
    // 0xa1bc70: ArrayStore: r3[0] = r0  ; List_4
    //     0xa1bc70: stur            w0, [x3, #0x17]
    // 0xa1bc74: ldur            d0, [fp, #-0x20]
    // 0xa1bc78: StoreField: r3->field_b = d0
    //     0xa1bc78: stur            d0, [x3, #0xb]
    // 0xa1bc7c: r0 = true
    //     0xa1bc7c: add             x0, NULL, #0x20  ; true
    // 0xa1bc80: StoreField: r3->field_13 = r0
    //     0xa1bc80: stur            w0, [x3, #0x13]
    // 0xa1bc84: r1 = Null
    //     0xa1bc84: mov             x1, NULL
    // 0xa1bc88: r2 = 2
    //     0xa1bc88: movz            x2, #0x2
    // 0xa1bc8c: r0 = AllocateArray()
    //     0xa1bc8c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa1bc90: mov             x2, x0
    // 0xa1bc94: ldur            x0, [fp, #-0x18]
    // 0xa1bc98: stur            x2, [fp, #-8]
    // 0xa1bc9c: StoreField: r2->field_f = r0
    //     0xa1bc9c: stur            w0, [x2, #0xf]
    // 0xa1bca0: r1 = <Widget>
    //     0xa1bca0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa1bca4: r0 = AllocateGrowableArray()
    //     0xa1bca4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa1bca8: mov             x1, x0
    // 0xa1bcac: ldur            x0, [fp, #-8]
    // 0xa1bcb0: stur            x1, [fp, #-0x18]
    // 0xa1bcb4: StoreField: r1->field_f = r0
    //     0xa1bcb4: stur            w0, [x1, #0xf]
    // 0xa1bcb8: r0 = 2
    //     0xa1bcb8: movz            x0, #0x2
    // 0xa1bcbc: StoreField: r1->field_b = r0
    //     0xa1bcbc: stur            w0, [x1, #0xb]
    // 0xa1bcc0: ldur            x0, [fp, #-0x10]
    // 0xa1bcc4: LoadField: r2 = r0->field_13
    //     0xa1bcc4: ldur            w2, [x0, #0x13]
    // 0xa1bcc8: DecompressPointer r2
    //     0xa1bcc8: add             x2, x2, HEAP, lsl #32
    // 0xa1bccc: stur            x2, [fp, #-8]
    // 0xa1bcd0: r0 = Viewport()
    //     0xa1bcd0: bl              #0xa1bd2c  ; AllocateViewportStub -> Viewport (size=0x34)
    // 0xa1bcd4: ldur            x1, [fp, #-8]
    // 0xa1bcd8: StoreField: r0->field_f = r1
    //     0xa1bcd8: stur            w1, [x0, #0xf]
    // 0xa1bcdc: ArrayStore: r0[0] = rZR  ; List_8
    //     0xa1bcdc: stur            xzr, [x0, #0x17]
    // 0xa1bce0: ldr             x1, [fp, #0x10]
    // 0xa1bce4: StoreField: r0->field_1f = r1
    //     0xa1bce4: stur            w1, [x0, #0x1f]
    // 0xa1bce8: r1 = 0.000000
    //     0xa1bce8: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xa1bcec: StoreField: r0->field_27 = r1
    //     0xa1bcec: stur            w1, [x0, #0x27]
    // 0xa1bcf0: r1 = Instance_CacheExtentStyle
    //     0xa1bcf0: add             x1, PP, #0x45, lsl #12  ; [pp+0x45e98] Obj!CacheExtentStyle@e35361
    //     0xa1bcf4: ldr             x1, [x1, #0xe98]
    // 0xa1bcf8: StoreField: r0->field_2b = r1
    //     0xa1bcf8: stur            w1, [x0, #0x2b]
    // 0xa1bcfc: r1 = Instance_Clip
    //     0xa1bcfc: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa1bd00: ldr             x1, [x1, #0x7c0]
    // 0xa1bd04: StoreField: r0->field_2f = r1
    //     0xa1bd04: stur            w1, [x0, #0x2f]
    // 0xa1bd08: ldur            x1, [fp, #-0x18]
    // 0xa1bd0c: StoreField: r0->field_b = r1
    //     0xa1bd0c: stur            w1, [x0, #0xb]
    // 0xa1bd10: LeaveFrame
    //     0xa1bd10: mov             SP, fp
    //     0xa1bd14: ldp             fp, lr, [SP], #0x10
    // 0xa1bd18: ret
    //     0xa1bd18: ret             
    // 0xa1bd1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1bd1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1bd20: r9 = _controller
    //     0xa1bd20: add             x9, PP, #0x45, lsl #12  ; [pp+0x45e78] Field <_PageViewState@310030489._controller@310030489>: late (offset: 0x1c)
    //     0xa1bd24: ldr             x9, [x9, #0xe78]
    // 0xa1bd28: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa1bd28: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa80740, size: 0x78
    // 0xa80740: EnterFrame
    //     0xa80740: stp             fp, lr, [SP, #-0x10]!
    //     0xa80744: mov             fp, SP
    // 0xa80748: CheckStackOverflow
    //     0xa80748: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8074c: cmp             SP, x16
    //     0xa80750: b.ls            #0xa807a0
    // 0xa80754: LoadField: r0 = r1->field_b
    //     0xa80754: ldur            w0, [x1, #0xb]
    // 0xa80758: DecompressPointer r0
    //     0xa80758: add             x0, x0, HEAP, lsl #32
    // 0xa8075c: cmp             w0, NULL
    // 0xa80760: b.eq            #0xa807a8
    // 0xa80764: LoadField: r2 = r0->field_1b
    //     0xa80764: ldur            w2, [x0, #0x1b]
    // 0xa80768: DecompressPointer r2
    //     0xa80768: add             x2, x2, HEAP, lsl #32
    // 0xa8076c: cmp             w2, NULL
    // 0xa80770: b.ne            #0xa80790
    // 0xa80774: LoadField: r0 = r1->field_1b
    //     0xa80774: ldur            w0, [x1, #0x1b]
    // 0xa80778: DecompressPointer r0
    //     0xa80778: add             x0, x0, HEAP, lsl #32
    // 0xa8077c: r16 = Sentinel
    //     0xa8077c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa80780: cmp             w0, w16
    // 0xa80784: b.eq            #0xa807ac
    // 0xa80788: mov             x1, x0
    // 0xa8078c: r0 = dispose()
    //     0xa8078c: bl              #0xa876d8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xa80790: r0 = Null
    //     0xa80790: mov             x0, NULL
    // 0xa80794: LeaveFrame
    //     0xa80794: mov             SP, fp
    //     0xa80798: ldp             fp, lr, [SP], #0x10
    // 0xa8079c: ret
    //     0xa8079c: ret             
    // 0xa807a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa807a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa807a4: b               #0xa80754
    // 0xa807a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa807a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa807ac: r9 = _controller
    //     0xa807ac: add             x9, PP, #0x45, lsl #12  ; [pp+0x45e78] Field <_PageViewState@310030489._controller@310030489>: late (offset: 0x1c)
    //     0xa807b0: ldr             x9, [x9, #0xe78]
    // 0xa807b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa807b4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4770, size: 0x44, field offset: 0xc
//   const constructor, 
class PageView extends StatefulWidget {

  _ PageView.builder(/* No info */) {
    // ** addr: 0x9d3014, size: 0x30c
    // 0x9d3014: EnterFrame
    //     0x9d3014: stp             fp, lr, [SP, #-0x10]!
    //     0x9d3018: mov             fp, SP
    // 0x9d301c: AllocStack(0x20)
    //     0x9d301c: sub             SP, SP, #0x20
    // 0x9d3020: SetupParameters(PageView this /* r1 => r2, fp-0x18 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r1 */, {dynamic itemCount = Null /* r7, fp-0x10 */, dynamic key = Null /* r8, fp-0x8 */, dynamic physics = Null /* r9 */, dynamic reverse = false /* r10 */, dynamic scrollBehavior = Null /* r14 */})
    //     0x9d3020: mov             x0, x2
    //     0x9d3024: mov             x2, x1
    //     0x9d3028: stur            x1, [fp, #-0x18]
    //     0x9d302c: mov             x1, x5
    //     0x9d3030: stur            x3, [fp, #-0x20]
    //     0x9d3034: ldur            w5, [x4, #0x13]
    //     0x9d3038: ldur            w6, [x4, #0x1f]
    //     0x9d303c: add             x6, x6, HEAP, lsl #32
    //     0x9d3040: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c098] "itemCount"
    //     0x9d3044: ldr             x16, [x16, #0x98]
    //     0x9d3048: cmp             w6, w16
    //     0x9d304c: b.ne            #0x9d3070
    //     0x9d3050: ldur            w6, [x4, #0x23]
    //     0x9d3054: add             x6, x6, HEAP, lsl #32
    //     0x9d3058: sub             w7, w5, w6
    //     0x9d305c: add             x6, fp, w7, sxtw #2
    //     0x9d3060: ldr             x6, [x6, #8]
    //     0x9d3064: mov             x7, x6
    //     0x9d3068: movz            x6, #0x1
    //     0x9d306c: b               #0x9d3078
    //     0x9d3070: mov             x7, NULL
    //     0x9d3074: movz            x6, #0
    //     0x9d3078: stur            x7, [fp, #-0x10]
    //     0x9d307c: lsl             x8, x6, #1
    //     0x9d3080: lsl             w9, w8, #1
    //     0x9d3084: add             w10, w9, #8
    //     0x9d3088: add             x16, x4, w10, sxtw #1
    //     0x9d308c: ldur            w11, [x16, #0xf]
    //     0x9d3090: add             x11, x11, HEAP, lsl #32
    //     0x9d3094: ldr             x16, [PP, #0xab8]  ; [pp+0xab8] "key"
    //     0x9d3098: cmp             w11, w16
    //     0x9d309c: b.ne            #0x9d30d0
    //     0x9d30a0: add             w6, w9, #0xa
    //     0x9d30a4: add             x16, x4, w6, sxtw #1
    //     0x9d30a8: ldur            w9, [x16, #0xf]
    //     0x9d30ac: add             x9, x9, HEAP, lsl #32
    //     0x9d30b0: sub             w6, w5, w9
    //     0x9d30b4: add             x9, fp, w6, sxtw #2
    //     0x9d30b8: ldr             x9, [x9, #8]
    //     0x9d30bc: add             w6, w8, #2
    //     0x9d30c0: sbfx            x8, x6, #1, #0x1f
    //     0x9d30c4: mov             x6, x8
    //     0x9d30c8: mov             x8, x9
    //     0x9d30cc: b               #0x9d30d4
    //     0x9d30d0: mov             x8, NULL
    //     0x9d30d4: stur            x8, [fp, #-8]
    //     0x9d30d8: lsl             x9, x6, #1
    //     0x9d30dc: lsl             w10, w9, #1
    //     0x9d30e0: add             w11, w10, #8
    //     0x9d30e4: add             x16, x4, w11, sxtw #1
    //     0x9d30e8: ldur            w12, [x16, #0xf]
    //     0x9d30ec: add             x12, x12, HEAP, lsl #32
    //     0x9d30f0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26ee8] "physics"
    //     0x9d30f4: ldr             x16, [x16, #0xee8]
    //     0x9d30f8: cmp             w12, w16
    //     0x9d30fc: b.ne            #0x9d3130
    //     0x9d3100: add             w6, w10, #0xa
    //     0x9d3104: add             x16, x4, w6, sxtw #1
    //     0x9d3108: ldur            w10, [x16, #0xf]
    //     0x9d310c: add             x10, x10, HEAP, lsl #32
    //     0x9d3110: sub             w6, w5, w10
    //     0x9d3114: add             x10, fp, w6, sxtw #2
    //     0x9d3118: ldr             x10, [x10, #8]
    //     0x9d311c: add             w6, w9, #2
    //     0x9d3120: sbfx            x9, x6, #1, #0x1f
    //     0x9d3124: mov             x6, x9
    //     0x9d3128: mov             x9, x10
    //     0x9d312c: b               #0x9d3134
    //     0x9d3130: mov             x9, NULL
    //     0x9d3134: lsl             x10, x6, #1
    //     0x9d3138: lsl             w11, w10, #1
    //     0x9d313c: add             w12, w11, #8
    //     0x9d3140: add             x16, x4, w12, sxtw #1
    //     0x9d3144: ldur            w13, [x16, #0xf]
    //     0x9d3148: add             x13, x13, HEAP, lsl #32
    //     0x9d314c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c0a0] "reverse"
    //     0x9d3150: ldr             x16, [x16, #0xa0]
    //     0x9d3154: cmp             w13, w16
    //     0x9d3158: b.ne            #0x9d318c
    //     0x9d315c: add             w6, w11, #0xa
    //     0x9d3160: add             x16, x4, w6, sxtw #1
    //     0x9d3164: ldur            w11, [x16, #0xf]
    //     0x9d3168: add             x11, x11, HEAP, lsl #32
    //     0x9d316c: sub             w6, w5, w11
    //     0x9d3170: add             x11, fp, w6, sxtw #2
    //     0x9d3174: ldr             x11, [x11, #8]
    //     0x9d3178: add             w6, w10, #2
    //     0x9d317c: sbfx            x10, x6, #1, #0x1f
    //     0x9d3180: mov             x6, x10
    //     0x9d3184: mov             x10, x11
    //     0x9d3188: b               #0x9d3190
    //     0x9d318c: add             x10, NULL, #0x30  ; false
    //     0x9d3190: lsl             x11, x6, #1
    //     0x9d3194: lsl             w6, w11, #1
    //     0x9d3198: add             w11, w6, #8
    //     0x9d319c: add             x16, x4, w11, sxtw #1
    //     0x9d31a0: ldur            w12, [x16, #0xf]
    //     0x9d31a4: add             x12, x12, HEAP, lsl #32
    //     0x9d31a8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c0a8] "scrollBehavior"
    //     0x9d31ac: ldr             x16, [x16, #0xa8]
    //     0x9d31b0: cmp             w12, w16
    //     0x9d31b4: b.ne            #0x9d31dc
    //     0x9d31b8: add             w11, w6, #0xa
    //     0x9d31bc: add             x16, x4, w11, sxtw #1
    //     0x9d31c0: ldur            w6, [x16, #0xf]
    //     0x9d31c4: add             x6, x6, HEAP, lsl #32
    //     0x9d31c8: sub             w4, w5, w6
    //     0x9d31cc: add             x5, fp, w4, sxtw #2
    //     0x9d31d0: ldr             x5, [x5, #8]
    //     0x9d31d4: mov             x14, x5
    //     0x9d31d8: b               #0x9d31e0
    //     0x9d31dc: mov             x14, NULL
    // 0x9d31e0: r13 = false
    //     0x9d31e0: add             x13, NULL, #0x30  ; false
    // 0x9d31e4: r12 = Instance_Axis
    //     0x9d31e4: ldr             x12, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x9d31e8: r11 = true
    //     0x9d31e8: add             x11, NULL, #0x20  ; true
    // 0x9d31ec: r6 = Instance_DragStartBehavior
    //     0x9d31ec: ldr             x6, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0x9d31f0: r5 = Instance_Clip
    //     0x9d31f0: add             x5, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0x9d31f4: ldr             x5, [x5, #0x7c0]
    // 0x9d31f8: r4 = Instance_HitTestBehavior
    //     0x9d31f8: add             x4, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0x9d31fc: ldr             x4, [x4, #0x1c8]
    // 0x9d3200: StoreField: r2->field_13 = r12
    //     0x9d3200: stur            w12, [x2, #0x13]
    // 0x9d3204: ArrayStore: r2[0] = r10  ; List_4
    //     0x9d3204: stur            w10, [x2, #0x17]
    // 0x9d3208: StoreField: r2->field_1b = r0
    //     0x9d3208: stur            w0, [x2, #0x1b]
    //     0x9d320c: ldurb           w16, [x2, #-1]
    //     0x9d3210: ldurb           w17, [x0, #-1]
    //     0x9d3214: and             x16, x17, x16, lsr #2
    //     0x9d3218: tst             x16, HEAP, lsr #32
    //     0x9d321c: b.eq            #0x9d3224
    //     0x9d3220: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9d3224: mov             x0, x9
    // 0x9d3228: StoreField: r2->field_1f = r0
    //     0x9d3228: stur            w0, [x2, #0x1f]
    //     0x9d322c: ldurb           w16, [x2, #-1]
    //     0x9d3230: ldurb           w17, [x0, #-1]
    //     0x9d3234: and             x16, x17, x16, lsr #2
    //     0x9d3238: tst             x16, HEAP, lsr #32
    //     0x9d323c: b.eq            #0x9d3244
    //     0x9d3240: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9d3244: StoreField: r2->field_23 = r11
    //     0x9d3244: stur            w11, [x2, #0x23]
    // 0x9d3248: mov             x0, x1
    // 0x9d324c: StoreField: r2->field_27 = r0
    //     0x9d324c: stur            w0, [x2, #0x27]
    //     0x9d3250: ldurb           w16, [x2, #-1]
    //     0x9d3254: ldurb           w17, [x0, #-1]
    //     0x9d3258: and             x16, x17, x16, lsr #2
    //     0x9d325c: tst             x16, HEAP, lsr #32
    //     0x9d3260: b.eq            #0x9d3268
    //     0x9d3264: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9d3268: StoreField: r2->field_2f = r6
    //     0x9d3268: stur            w6, [x2, #0x2f]
    // 0x9d326c: StoreField: r2->field_b = r13
    //     0x9d326c: stur            w13, [x2, #0xb]
    // 0x9d3270: StoreField: r2->field_33 = r5
    //     0x9d3270: stur            w5, [x2, #0x33]
    // 0x9d3274: StoreField: r2->field_37 = r4
    //     0x9d3274: stur            w4, [x2, #0x37]
    // 0x9d3278: mov             x0, x14
    // 0x9d327c: StoreField: r2->field_3b = r0
    //     0x9d327c: stur            w0, [x2, #0x3b]
    //     0x9d3280: ldurb           w16, [x2, #-1]
    //     0x9d3284: ldurb           w17, [x0, #-1]
    //     0x9d3288: and             x16, x17, x16, lsr #2
    //     0x9d328c: tst             x16, HEAP, lsr #32
    //     0x9d3290: b.eq            #0x9d3298
    //     0x9d3294: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9d3298: StoreField: r2->field_3f = r11
    //     0x9d3298: stur            w11, [x2, #0x3f]
    // 0x9d329c: r0 = SliverChildBuilderDelegate()
    //     0x9d329c: bl              #0x9d3320  ; AllocateSliverChildBuilderDelegateStub -> SliverChildBuilderDelegate (size=0x2c)
    // 0x9d32a0: ldur            x1, [fp, #-0x20]
    // 0x9d32a4: StoreField: r0->field_7 = r1
    //     0x9d32a4: stur            w1, [x0, #7]
    // 0x9d32a8: ldur            x1, [fp, #-0x10]
    // 0x9d32ac: StoreField: r0->field_b = r1
    //     0x9d32ac: stur            w1, [x0, #0xb]
    // 0x9d32b0: r1 = true
    //     0x9d32b0: add             x1, NULL, #0x20  ; true
    // 0x9d32b4: StoreField: r0->field_f = r1
    //     0x9d32b4: stur            w1, [x0, #0xf]
    // 0x9d32b8: StoreField: r0->field_13 = r1
    //     0x9d32b8: stur            w1, [x0, #0x13]
    // 0x9d32bc: ArrayStore: r0[0] = r1  ; List_4
    //     0x9d32bc: stur            w1, [x0, #0x17]
    // 0x9d32c0: r1 = Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static.
    //     0x9d32c0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26ef8] Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static. (0x7e54fb8bd554)
    //     0x9d32c4: ldr             x1, [x1, #0xef8]
    // 0x9d32c8: StoreField: r0->field_23 = r1
    //     0x9d32c8: stur            w1, [x0, #0x23]
    // 0x9d32cc: StoreField: r0->field_1b = rZR
    //     0x9d32cc: stur            xzr, [x0, #0x1b]
    // 0x9d32d0: ldur            x1, [fp, #-0x18]
    // 0x9d32d4: StoreField: r1->field_2b = r0
    //     0x9d32d4: stur            w0, [x1, #0x2b]
    //     0x9d32d8: ldurb           w16, [x1, #-1]
    //     0x9d32dc: ldurb           w17, [x0, #-1]
    //     0x9d32e0: and             x16, x17, x16, lsr #2
    //     0x9d32e4: tst             x16, HEAP, lsr #32
    //     0x9d32e8: b.eq            #0x9d32f0
    //     0x9d32ec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9d32f0: ldur            x0, [fp, #-8]
    // 0x9d32f4: StoreField: r1->field_7 = r0
    //     0x9d32f4: stur            w0, [x1, #7]
    //     0x9d32f8: ldurb           w16, [x1, #-1]
    //     0x9d32fc: ldurb           w17, [x0, #-1]
    //     0x9d3300: and             x16, x17, x16, lsr #2
    //     0x9d3304: tst             x16, HEAP, lsr #32
    //     0x9d3308: b.eq            #0x9d3310
    //     0x9d330c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9d3310: r0 = Null
    //     0x9d3310: mov             x0, NULL
    // 0x9d3314: LeaveFrame
    //     0x9d3314: mov             SP, fp
    //     0x9d3318: ldp             fp, lr, [SP], #0x10
    // 0x9d331c: ret
    //     0x9d331c: ret             
  }
  _ PageView(/* No info */) {
    // ** addr: 0xa07c84, size: 0x108
    // 0xa07c84: EnterFrame
    //     0xa07c84: stp             fp, lr, [SP, #-0x10]!
    //     0xa07c88: mov             fp, SP
    // 0xa07c8c: AllocStack(0x10)
    //     0xa07c8c: sub             SP, SP, #0x10
    // 0xa07c90: r0 = Instance_Axis
    //     0xa07c90: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xa07c94: r9 = false
    //     0xa07c94: add             x9, NULL, #0x30  ; false
    // 0xa07c98: r8 = true
    //     0xa07c98: add             x8, NULL, #0x20  ; true
    // 0xa07c9c: r7 = Instance_DragStartBehavior
    //     0xa07c9c: ldr             x7, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xa07ca0: r6 = Instance_Clip
    //     0xa07ca0: add             x6, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa07ca4: ldr             x6, [x6, #0x7c0]
    // 0xa07ca8: r4 = Instance_HitTestBehavior
    //     0xa07ca8: add             x4, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xa07cac: ldr             x4, [x4, #0x1c8]
    // 0xa07cb0: stur            x1, [fp, #-8]
    // 0xa07cb4: mov             x16, x5
    // 0xa07cb8: mov             x5, x1
    // 0xa07cbc: mov             x1, x16
    // 0xa07cc0: stur            x2, [fp, #-0x10]
    // 0xa07cc4: mov             x16, x3
    // 0xa07cc8: mov             x3, x2
    // 0xa07ccc: mov             x2, x16
    // 0xa07cd0: CheckStackOverflow
    //     0xa07cd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa07cd4: cmp             SP, x16
    //     0xa07cd8: b.ls            #0xa07d84
    // 0xa07cdc: StoreField: r5->field_13 = r0
    //     0xa07cdc: stur            w0, [x5, #0x13]
    // 0xa07ce0: ArrayStore: r5[0] = r9  ; List_4
    //     0xa07ce0: stur            w9, [x5, #0x17]
    // 0xa07ce4: mov             x0, x2
    // 0xa07ce8: StoreField: r5->field_1b = r0
    //     0xa07ce8: stur            w0, [x5, #0x1b]
    //     0xa07cec: ldurb           w16, [x5, #-1]
    //     0xa07cf0: ldurb           w17, [x0, #-1]
    //     0xa07cf4: and             x16, x17, x16, lsr #2
    //     0xa07cf8: tst             x16, HEAP, lsr #32
    //     0xa07cfc: b.eq            #0xa07d04
    //     0xa07d00: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xa07d04: mov             x0, x1
    // 0xa07d08: StoreField: r5->field_1f = r0
    //     0xa07d08: stur            w0, [x5, #0x1f]
    //     0xa07d0c: ldurb           w16, [x5, #-1]
    //     0xa07d10: ldurb           w17, [x0, #-1]
    //     0xa07d14: and             x16, x17, x16, lsr #2
    //     0xa07d18: tst             x16, HEAP, lsr #32
    //     0xa07d1c: b.eq            #0xa07d24
    //     0xa07d20: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xa07d24: StoreField: r5->field_23 = r8
    //     0xa07d24: stur            w8, [x5, #0x23]
    // 0xa07d28: StoreField: r5->field_2f = r7
    //     0xa07d28: stur            w7, [x5, #0x2f]
    // 0xa07d2c: StoreField: r5->field_b = r9
    //     0xa07d2c: stur            w9, [x5, #0xb]
    // 0xa07d30: StoreField: r5->field_33 = r6
    //     0xa07d30: stur            w6, [x5, #0x33]
    // 0xa07d34: StoreField: r5->field_37 = r4
    //     0xa07d34: stur            w4, [x5, #0x37]
    // 0xa07d38: StoreField: r5->field_3f = r8
    //     0xa07d38: stur            w8, [x5, #0x3f]
    // 0xa07d3c: r0 = SliverChildListDelegate()
    //     0xa07d3c: bl              #0xa07e78  ; AllocateSliverChildListDelegateStub -> SliverChildListDelegate (size=0x28)
    // 0xa07d40: mov             x1, x0
    // 0xa07d44: ldur            x2, [fp, #-0x10]
    // 0xa07d48: stur            x0, [fp, #-0x10]
    // 0xa07d4c: r0 = SliverChildListDelegate()
    //     0xa07d4c: bl              #0xa07d8c  ; [package:flutter/src/widgets/scroll_delegate.dart] SliverChildListDelegate::SliverChildListDelegate
    // 0xa07d50: ldur            x0, [fp, #-0x10]
    // 0xa07d54: ldur            x1, [fp, #-8]
    // 0xa07d58: StoreField: r1->field_2b = r0
    //     0xa07d58: stur            w0, [x1, #0x2b]
    //     0xa07d5c: ldurb           w16, [x1, #-1]
    //     0xa07d60: ldurb           w17, [x0, #-1]
    //     0xa07d64: and             x16, x17, x16, lsr #2
    //     0xa07d68: tst             x16, HEAP, lsr #32
    //     0xa07d6c: b.eq            #0xa07d74
    //     0xa07d70: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa07d74: r0 = Null
    //     0xa07d74: mov             x0, NULL
    // 0xa07d78: LeaveFrame
    //     0xa07d78: mov             SP, fp
    //     0xa07d7c: ldp             fp, lr, [SP], #0x10
    // 0xa07d80: ret
    //     0xa07d80: ret             
    // 0xa07d84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa07d84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa07d88: b               #0xa07cdc
  }
  _ createState(/* No info */) {
    // ** addr: 0xa92864, size: 0x30
    // 0xa92864: EnterFrame
    //     0xa92864: stp             fp, lr, [SP, #-0x10]!
    //     0xa92868: mov             fp, SP
    // 0xa9286c: mov             x0, x1
    // 0xa92870: r1 = <PageView>
    //     0xa92870: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3a028] TypeArguments: <PageView>
    //     0xa92874: ldr             x1, [x1, #0x28]
    // 0xa92878: r0 = _PageViewState()
    //     0xa92878: bl              #0xa92894  ; Allocate_PageViewStateStub -> _PageViewState (size=0x20)
    // 0xa9287c: StoreField: r0->field_13 = rZR
    //     0xa9287c: stur            xzr, [x0, #0x13]
    // 0xa92880: r1 = Sentinel
    //     0xa92880: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa92884: StoreField: r0->field_1b = r1
    //     0xa92884: stur            w1, [x0, #0x1b]
    // 0xa92888: LeaveFrame
    //     0xa92888: mov             SP, fp
    //     0xa9288c: ldp             fp, lr, [SP], #0x10
    // 0xa92890: ret
    //     0xa92890: ret             
  }
}
