// lib: , url: package:flutter/src/widgets/reorderable_list.dart

// class id: 1049167, size: 0x8
class :: {

  static _ _extentOffset(/* No info */) {
    // ** addr: 0x945a1c, size: 0x4c
    // 0x945a1c: EnterFrame
    //     0x945a1c: stp             fp, lr, [SP, #-0x10]!
    //     0x945a20: mov             fp, SP
    // 0x945a24: AllocStack(0x8)
    //     0x945a24: sub             SP, SP, #8
    // 0x945a28: SetupParameters(dynamic _ /* d0 => d0, fp-0x8 */)
    //     0x945a28: stur            d0, [fp, #-8]
    // 0x945a2c: LoadField: r0 = r1->field_7
    //     0x945a2c: ldur            x0, [x1, #7]
    // 0x945a30: cmp             x0, #0
    // 0x945a34: b.gt            #0x945a4c
    // 0x945a38: r0 = Offset()
    //     0x945a38: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x945a3c: ldur            d0, [fp, #-8]
    // 0x945a40: StoreField: r0->field_7 = d0
    //     0x945a40: stur            d0, [x0, #7]
    // 0x945a44: StoreField: r0->field_f = rZR
    //     0x945a44: stur            xzr, [x0, #0xf]
    // 0x945a48: b               #0x945a5c
    // 0x945a4c: r0 = Offset()
    //     0x945a4c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x945a50: StoreField: r0->field_7 = rZR
    //     0x945a50: stur            xzr, [x0, #7]
    // 0x945a54: ldur            d0, [fp, #-8]
    // 0x945a58: StoreField: r0->field_f = d0
    //     0x945a58: stur            d0, [x0, #0xf]
    // 0x945a5c: LeaveFrame
    //     0x945a5c: mov             SP, fp
    //     0x945a60: ldp             fp, lr, [SP], #0x10
    // 0x945a64: ret
    //     0x945a64: ret             
  }
  static _ _sizeExtent(/* No info */) {
    // ** addr: 0x997cf0, size: 0x24
    // 0x997cf0: LoadField: r0 = r2->field_7
    //     0x997cf0: ldur            x0, [x2, #7]
    // 0x997cf4: cmp             x0, #0
    // 0x997cf8: b.gt            #0x997d08
    // 0x997cfc: LoadField: d1 = r1->field_7
    //     0x997cfc: ldur            d1, [x1, #7]
    // 0x997d00: mov             v0.16b, v1.16b
    // 0x997d04: b               #0x997d10
    // 0x997d08: LoadField: d1 = r1->field_f
    //     0x997d08: ldur            d1, [x1, #0xf]
    // 0x997d0c: mov             v0.16b, v1.16b
    // 0x997d10: ret
    //     0x997d10: ret             
  }
  static _ _extentSize(/* No info */) {
    // ** addr: 0xa1cea0, size: 0x4c
    // 0xa1cea0: EnterFrame
    //     0xa1cea0: stp             fp, lr, [SP, #-0x10]!
    //     0xa1cea4: mov             fp, SP
    // 0xa1cea8: AllocStack(0x8)
    //     0xa1cea8: sub             SP, SP, #8
    // 0xa1ceac: SetupParameters(dynamic _ /* d0 => d0, fp-0x8 */)
    //     0xa1ceac: stur            d0, [fp, #-8]
    // 0xa1ceb0: LoadField: r0 = r1->field_7
    //     0xa1ceb0: ldur            x0, [x1, #7]
    // 0xa1ceb4: cmp             x0, #0
    // 0xa1ceb8: b.gt            #0xa1ced0
    // 0xa1cebc: r0 = Size()
    //     0xa1cebc: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0xa1cec0: ldur            d0, [fp, #-8]
    // 0xa1cec4: StoreField: r0->field_7 = d0
    //     0xa1cec4: stur            d0, [x0, #7]
    // 0xa1cec8: StoreField: r0->field_f = rZR
    //     0xa1cec8: stur            xzr, [x0, #0xf]
    // 0xa1cecc: b               #0xa1cee0
    // 0xa1ced0: r0 = Size()
    //     0xa1ced0: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0xa1ced4: StoreField: r0->field_7 = rZR
    //     0xa1ced4: stur            xzr, [x0, #7]
    // 0xa1ced8: ldur            d0, [fp, #-8]
    // 0xa1cedc: StoreField: r0->field_f = d0
    //     0xa1cedc: stur            d0, [x0, #0xf]
    // 0xa1cee0: LeaveFrame
    //     0xa1cee0: mov             SP, fp
    //     0xa1cee4: ldp             fp, lr, [SP], #0x10
    // 0xa1cee8: ret
    //     0xa1cee8: ret             
  }
  static _ _overlayOrigin(/* No info */) {
    // ** addr: 0xaa6018, size: 0xc4
    // 0xaa6018: EnterFrame
    //     0xaa6018: stp             fp, lr, [SP, #-0x10]!
    //     0xaa601c: mov             fp, SP
    // 0xaa6020: AllocStack(0x8)
    //     0xaa6020: sub             SP, SP, #8
    // 0xaa6024: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xaa6024: mov             x2, x1
    //     0xaa6028: stur            x1, [fp, #-8]
    // 0xaa602c: CheckStackOverflow
    //     0xaa602c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6030: cmp             SP, x16
    //     0xaa6034: b.ls            #0xaa60cc
    // 0xaa6038: r0 = LoadClassIdInstr(r2)
    //     0xaa6038: ldur            x0, [x2, #-1]
    //     0xaa603c: ubfx            x0, x0, #0xc, #0x14
    // 0xaa6040: mov             x1, x2
    // 0xaa6044: r0 = GDT[cid_x0 + 0xee4]()
    //     0xaa6044: add             lr, x0, #0xee4
    //     0xaa6048: ldr             lr, [x21, lr, lsl #3]
    //     0xaa604c: blr             lr
    // 0xaa6050: ldur            x1, [fp, #-8]
    // 0xaa6054: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaa6054: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaa6058: r0 = of()
    //     0xaa6058: bl              #0x6a5014  ; [package:flutter/src/widgets/overlay.dart] Overlay::of
    // 0xaa605c: LoadField: r1 = r0->field_f
    //     0xaa605c: ldur            w1, [x0, #0xf]
    // 0xaa6060: DecompressPointer r1
    //     0xaa6060: add             x1, x1, HEAP, lsl #32
    // 0xaa6064: cmp             w1, NULL
    // 0xaa6068: b.eq            #0xaa60d4
    // 0xaa606c: r0 = renderObject()
    //     0xaa606c: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xaa6070: mov             x3, x0
    // 0xaa6074: stur            x3, [fp, #-8]
    // 0xaa6078: cmp             w3, NULL
    // 0xaa607c: b.eq            #0xaa60d8
    // 0xaa6080: mov             x0, x3
    // 0xaa6084: r2 = Null
    //     0xaa6084: mov             x2, NULL
    // 0xaa6088: r1 = Null
    //     0xaa6088: mov             x1, NULL
    // 0xaa608c: r4 = LoadClassIdInstr(r0)
    //     0xaa608c: ldur            x4, [x0, #-1]
    //     0xaa6090: ubfx            x4, x4, #0xc, #0x14
    // 0xaa6094: sub             x4, x4, #0xbba
    // 0xaa6098: cmp             x4, #0x9a
    // 0xaa609c: b.ls            #0xaa60b0
    // 0xaa60a0: r8 = RenderBox
    //     0xaa60a0: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0xaa60a4: r3 = Null
    //     0xaa60a4: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fd58] Null
    //     0xaa60a8: ldr             x3, [x3, #0xd58]
    // 0xaa60ac: r0 = RenderBox()
    //     0xaa60ac: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0xaa60b0: ldur            x1, [fp, #-8]
    // 0xaa60b4: r2 = Instance_Offset
    //     0xaa60b4: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xaa60b8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaa60b8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaa60bc: r0 = localToGlobal()
    //     0xaa60bc: bl              #0x67fb20  ; [package:flutter/src/rendering/box.dart] RenderBox::localToGlobal
    // 0xaa60c0: LeaveFrame
    //     0xaa60c0: mov             SP, fp
    //     0xaa60c4: ldp             fp, lr, [SP], #0x10
    // 0xaa60c8: ret
    //     0xaa60c8: ret             
    // 0xaa60cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa60cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa60d0: b               #0xaa6038
    // 0xaa60d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa60d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa60d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa60d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _restrictAxis(/* No info */) {
    // ** addr: 0xd8d794, size: 0x58
    // 0xd8d794: EnterFrame
    //     0xd8d794: stp             fp, lr, [SP, #-0x10]!
    //     0xd8d798: mov             fp, SP
    // 0xd8d79c: AllocStack(0x8)
    //     0xd8d79c: sub             SP, SP, #8
    // 0xd8d7a0: LoadField: r0 = r2->field_7
    //     0xd8d7a0: ldur            x0, [x2, #7]
    // 0xd8d7a4: cmp             x0, #0
    // 0xd8d7a8: b.gt            #0xd8d7c8
    // 0xd8d7ac: LoadField: d0 = r1->field_7
    //     0xd8d7ac: ldur            d0, [x1, #7]
    // 0xd8d7b0: stur            d0, [fp, #-8]
    // 0xd8d7b4: r0 = Offset()
    //     0xd8d7b4: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xd8d7b8: ldur            d0, [fp, #-8]
    // 0xd8d7bc: StoreField: r0->field_7 = d0
    //     0xd8d7bc: stur            d0, [x0, #7]
    // 0xd8d7c0: StoreField: r0->field_f = rZR
    //     0xd8d7c0: stur            xzr, [x0, #0xf]
    // 0xd8d7c4: b               #0xd8d7e0
    // 0xd8d7c8: LoadField: d0 = r1->field_f
    //     0xd8d7c8: ldur            d0, [x1, #0xf]
    // 0xd8d7cc: stur            d0, [fp, #-8]
    // 0xd8d7d0: r0 = Offset()
    //     0xd8d7d0: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xd8d7d4: StoreField: r0->field_7 = rZR
    //     0xd8d7d4: stur            xzr, [x0, #7]
    // 0xd8d7d8: ldur            d0, [fp, #-8]
    // 0xd8d7dc: StoreField: r0->field_f = d0
    //     0xd8d7dc: stur            d0, [x0, #0xf]
    // 0xd8d7e0: LeaveFrame
    //     0xd8d7e0: mov             SP, fp
    //     0xd8d7e4: ldp             fp, lr, [SP], #0x10
    // 0xd8d7e8: ret
    //     0xd8d7e8: ret             
  }
}

// class id: 3512, size: 0x4c, field offset: 0x8
class _DragInfo extends Drag {

  late double itemExtent; // offset: 0x40
  late CapturedThemes capturedThemes; // offset: 0x44
  late SliverReorderableListState listState; // offset: 0x24
  late int index; // offset: 0x28
  late Size itemSize; // offset: 0x38
  late BoxConstraints itemLayoutConstraints; // offset: 0x3c
  late Offset dragPosition; // offset: 0x30
  late Offset dragOffset; // offset: 0x34
  late Widget child; // offset: 0x2c

  _ dispose(/* No info */) {
    // ** addr: 0x9962b8, size: 0x44
    // 0x9962b8: EnterFrame
    //     0x9962b8: stp             fp, lr, [SP, #-0x10]!
    //     0x9962bc: mov             fp, SP
    // 0x9962c0: CheckStackOverflow
    //     0x9962c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9962c4: cmp             SP, x16
    //     0x9962c8: b.ls            #0x9962f4
    // 0x9962cc: LoadField: r0 = r1->field_47
    //     0x9962cc: ldur            w0, [x1, #0x47]
    // 0x9962d0: DecompressPointer r0
    //     0x9962d0: add             x0, x0, HEAP, lsl #32
    // 0x9962d4: cmp             w0, NULL
    // 0x9962d8: b.eq            #0x9962e4
    // 0x9962dc: mov             x1, x0
    // 0x9962e0: r0 = dispose()
    //     0x9962e0: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0x9962e4: r0 = Null
    //     0x9962e4: mov             x0, NULL
    // 0x9962e8: LeaveFrame
    //     0x9962e8: mov             SP, fp
    //     0x9962ec: ldp             fp, lr, [SP], #0x10
    // 0x9962f0: ret
    //     0x9962f0: ret             
    // 0x9962f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9962f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9962f8: b               #0x9962cc
  }
  _ startDrag(/* No info */) {
    // ** addr: 0xaa57c8, size: 0xd0
    // 0xaa57c8: EnterFrame
    //     0xaa57c8: stp             fp, lr, [SP, #-0x10]!
    //     0xaa57cc: mov             fp, SP
    // 0xaa57d0: AllocStack(0x28)
    //     0xaa57d0: sub             SP, SP, #0x28
    // 0xaa57d4: SetupParameters(_DragInfo this /* r1 => r1, fp-0x8 */)
    //     0xaa57d4: stur            x1, [fp, #-8]
    // 0xaa57d8: CheckStackOverflow
    //     0xaa57d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa57dc: cmp             SP, x16
    //     0xaa57e0: b.ls            #0xaa5890
    // 0xaa57e4: r1 = 1
    //     0xaa57e4: movz            x1, #0x1
    // 0xaa57e8: r0 = AllocateContext()
    //     0xaa57e8: bl              #0xec126c  ; AllocateContextStub
    // 0xaa57ec: mov             x2, x0
    // 0xaa57f0: ldur            x0, [fp, #-8]
    // 0xaa57f4: stur            x2, [fp, #-0x18]
    // 0xaa57f8: StoreField: r2->field_f = r0
    //     0xaa57f8: stur            w0, [x2, #0xf]
    // 0xaa57fc: LoadField: r3 = r0->field_1f
    //     0xaa57fc: ldur            w3, [x0, #0x1f]
    // 0xaa5800: DecompressPointer r3
    //     0xaa5800: add             x3, x3, HEAP, lsl #32
    // 0xaa5804: stur            x3, [fp, #-0x10]
    // 0xaa5808: r1 = <double>
    //     0xaa5808: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xaa580c: r0 = AnimationController()
    //     0xaa580c: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0xaa5810: stur            x0, [fp, #-0x20]
    // 0xaa5814: r16 = Instance_Duration
    //     0xaa5814: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d90] Obj!Duration@e3a0e1
    //     0xaa5818: ldr             x16, [x16, #0xd90]
    // 0xaa581c: str             x16, [SP]
    // 0xaa5820: mov             x1, x0
    // 0xaa5824: ldur            x2, [fp, #-0x10]
    // 0xaa5828: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0xaa5828: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0xaa582c: ldr             x4, [x4, #0x408]
    // 0xaa5830: r0 = AnimationController()
    //     0xaa5830: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0xaa5834: ldur            x2, [fp, #-0x18]
    // 0xaa5838: r1 = Function '<anonymous closure>':.
    //     0xaa5838: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fe08] AnonymousClosure: (0xaa5898), in [package:flutter/src/widgets/reorderable_list.dart] _DragInfo::startDrag (0xaa57c8)
    //     0xaa583c: ldr             x1, [x1, #0xe08]
    // 0xaa5840: r0 = AllocateClosure()
    //     0xaa5840: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa5844: ldur            x1, [fp, #-0x20]
    // 0xaa5848: mov             x2, x0
    // 0xaa584c: r0 = addStatusListener()
    //     0xaa584c: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0xaa5850: ldur            x1, [fp, #-0x20]
    // 0xaa5854: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaa5854: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaa5858: r0 = forward()
    //     0xaa5858: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0xaa585c: ldur            x0, [fp, #-0x20]
    // 0xaa5860: ldur            x1, [fp, #-8]
    // 0xaa5864: StoreField: r1->field_47 = r0
    //     0xaa5864: stur            w0, [x1, #0x47]
    //     0xaa5868: ldurb           w16, [x1, #-1]
    //     0xaa586c: ldurb           w17, [x0, #-1]
    //     0xaa5870: and             x16, x17, x16, lsr #2
    //     0xaa5874: tst             x16, HEAP, lsr #32
    //     0xaa5878: b.eq            #0xaa5880
    //     0xaa587c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa5880: r0 = Null
    //     0xaa5880: mov             x0, NULL
    // 0xaa5884: LeaveFrame
    //     0xaa5884: mov             SP, fp
    //     0xaa5888: ldp             fp, lr, [SP], #0x10
    // 0xaa588c: ret
    //     0xaa588c: ret             
    // 0xaa5890: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa5890: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa5894: b               #0xaa57e4
  }
  [closure] void <anonymous closure>(dynamic, AnimationStatus) {
    // ** addr: 0xaa5898, size: 0x58
    // 0xaa5898: EnterFrame
    //     0xaa5898: stp             fp, lr, [SP, #-0x10]!
    //     0xaa589c: mov             fp, SP
    // 0xaa58a0: ldr             x0, [fp, #0x18]
    // 0xaa58a4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa58a4: ldur            w1, [x0, #0x17]
    // 0xaa58a8: DecompressPointer r1
    //     0xaa58a8: add             x1, x1, HEAP, lsl #32
    // 0xaa58ac: CheckStackOverflow
    //     0xaa58ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa58b0: cmp             SP, x16
    //     0xaa58b4: b.ls            #0xaa58e8
    // 0xaa58b8: ldr             x0, [fp, #0x10]
    // 0xaa58bc: r16 = Instance_AnimationStatus
    //     0xaa58bc: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0xaa58c0: cmp             w0, w16
    // 0xaa58c4: b.ne            #0xaa58d8
    // 0xaa58c8: LoadField: r0 = r1->field_f
    //     0xaa58c8: ldur            w0, [x1, #0xf]
    // 0xaa58cc: DecompressPointer r0
    //     0xaa58cc: add             x0, x0, HEAP, lsl #32
    // 0xaa58d0: mov             x1, x0
    // 0xaa58d4: r0 = _dropCompleted()
    //     0xaa58d4: bl              #0xaa58f0  ; [package:flutter/src/widgets/reorderable_list.dart] _DragInfo::_dropCompleted
    // 0xaa58d8: r0 = Null
    //     0xaa58d8: mov             x0, NULL
    // 0xaa58dc: LeaveFrame
    //     0xaa58dc: mov             SP, fp
    //     0xaa58e0: ldp             fp, lr, [SP], #0x10
    // 0xaa58e4: ret
    //     0xaa58e4: ret             
    // 0xaa58e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa58e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa58ec: b               #0xaa58b8
  }
  _ _dropCompleted(/* No info */) {
    // ** addr: 0xaa58f0, size: 0x78
    // 0xaa58f0: EnterFrame
    //     0xaa58f0: stp             fp, lr, [SP, #-0x10]!
    //     0xaa58f4: mov             fp, SP
    // 0xaa58f8: AllocStack(0x8)
    //     0xaa58f8: sub             SP, SP, #8
    // 0xaa58fc: SetupParameters(_DragInfo this /* r1 => r0, fp-0x8 */)
    //     0xaa58fc: mov             x0, x1
    //     0xaa5900: stur            x1, [fp, #-8]
    // 0xaa5904: CheckStackOverflow
    //     0xaa5904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa5908: cmp             SP, x16
    //     0xaa590c: b.ls            #0xaa595c
    // 0xaa5910: LoadField: r1 = r0->field_47
    //     0xaa5910: ldur            w1, [x0, #0x47]
    // 0xaa5914: DecompressPointer r1
    //     0xaa5914: add             x1, x1, HEAP, lsl #32
    // 0xaa5918: cmp             w1, NULL
    // 0xaa591c: b.eq            #0xaa5928
    // 0xaa5920: r0 = dispose()
    //     0xaa5920: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xaa5924: ldur            x0, [fp, #-8]
    // 0xaa5928: StoreField: r0->field_47 = rNULL
    //     0xaa5928: stur            NULL, [x0, #0x47]
    // 0xaa592c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa592c: ldur            w1, [x0, #0x17]
    // 0xaa5930: DecompressPointer r1
    //     0xaa5930: add             x1, x1, HEAP, lsl #32
    // 0xaa5934: cmp             w1, NULL
    // 0xaa5938: b.eq            #0xaa5964
    // 0xaa593c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xaa593c: ldur            w0, [x1, #0x17]
    // 0xaa5940: DecompressPointer r0
    //     0xaa5940: add             x0, x0, HEAP, lsl #32
    // 0xaa5944: mov             x1, x0
    // 0xaa5948: r0 = _dropCompleted()
    //     0xaa5948: bl              #0x995ed0  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dropCompleted
    // 0xaa594c: r0 = Null
    //     0xaa594c: mov             x0, NULL
    // 0xaa5950: LeaveFrame
    //     0xaa5950: mov             SP, fp
    //     0xaa5954: ldp             fp, lr, [SP], #0x10
    // 0xaa5958: ret
    //     0xaa5958: ret             
    // 0xaa595c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa595c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa5960: b               #0xaa5910
    // 0xaa5964: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaa5964: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _DragInfo(/* No info */) {
    // ** addr: 0xaa5968, size: 0x45c
    // 0xaa5968: EnterFrame
    //     0xaa5968: stp             fp, lr, [SP, #-0x10]!
    //     0xaa596c: mov             fp, SP
    // 0xaa5970: AllocStack(0x20)
    //     0xaa5970: sub             SP, SP, #0x20
    // 0xaa5974: r0 = Sentinel
    //     0xaa5974: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa5978: mov             x4, x3
    // 0xaa597c: stur            x3, [fp, #-0x18]
    // 0xaa5980: mov             x3, x5
    // 0xaa5984: mov             x5, x2
    // 0xaa5988: stur            x2, [fp, #-0x10]
    // 0xaa598c: mov             x2, x6
    // 0xaa5990: mov             x6, x1
    // 0xaa5994: stur            x1, [fp, #-8]
    // 0xaa5998: mov             x1, x7
    // 0xaa599c: CheckStackOverflow
    //     0xaa599c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa59a0: cmp             SP, x16
    //     0xaa59a4: b.ls            #0xaa5d88
    // 0xaa59a8: StoreField: r6->field_23 = r0
    //     0xaa59a8: stur            w0, [x6, #0x23]
    // 0xaa59ac: StoreField: r6->field_27 = r0
    //     0xaa59ac: stur            w0, [x6, #0x27]
    // 0xaa59b0: StoreField: r6->field_2b = r0
    //     0xaa59b0: stur            w0, [x6, #0x2b]
    // 0xaa59b4: StoreField: r6->field_2f = r0
    //     0xaa59b4: stur            w0, [x6, #0x2f]
    // 0xaa59b8: StoreField: r6->field_33 = r0
    //     0xaa59b8: stur            w0, [x6, #0x33]
    // 0xaa59bc: StoreField: r6->field_37 = r0
    //     0xaa59bc: stur            w0, [x6, #0x37]
    // 0xaa59c0: StoreField: r6->field_3b = r0
    //     0xaa59c0: stur            w0, [x6, #0x3b]
    // 0xaa59c4: StoreField: r6->field_3f = r0
    //     0xaa59c4: stur            w0, [x6, #0x3f]
    // 0xaa59c8: StoreField: r6->field_43 = r0
    //     0xaa59c8: stur            w0, [x6, #0x43]
    // 0xaa59cc: ldr             x0, [fp, #0x18]
    // 0xaa59d0: StoreField: r6->field_7 = r0
    //     0xaa59d0: stur            w0, [x6, #7]
    //     0xaa59d4: ldurb           w16, [x6, #-1]
    //     0xaa59d8: ldurb           w17, [x0, #-1]
    //     0xaa59dc: and             x16, x17, x16, lsr #2
    //     0xaa59e0: tst             x16, HEAP, lsr #32
    //     0xaa59e4: b.eq            #0xaa59ec
    //     0xaa59e8: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xaa59ec: ldr             x0, [fp, #0x28]
    // 0xaa59f0: StoreField: r6->field_b = r0
    //     0xaa59f0: stur            w0, [x6, #0xb]
    //     0xaa59f4: ldurb           w16, [x6, #-1]
    //     0xaa59f8: ldurb           w17, [x0, #-1]
    //     0xaa59fc: and             x16, x17, x16, lsr #2
    //     0xaa5a00: tst             x16, HEAP, lsr #32
    //     0xaa5a04: b.eq            #0xaa5a0c
    //     0xaa5a08: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xaa5a0c: mov             x0, x1
    // 0xaa5a10: StoreField: r6->field_f = r0
    //     0xaa5a10: stur            w0, [x6, #0xf]
    //     0xaa5a14: ldurb           w16, [x6, #-1]
    //     0xaa5a18: ldurb           w17, [x0, #-1]
    //     0xaa5a1c: and             x16, x17, x16, lsr #2
    //     0xaa5a20: tst             x16, HEAP, lsr #32
    //     0xaa5a24: b.eq            #0xaa5a2c
    //     0xaa5a28: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xaa5a2c: mov             x0, x3
    // 0xaa5a30: StoreField: r6->field_13 = r0
    //     0xaa5a30: stur            w0, [x6, #0x13]
    //     0xaa5a34: ldurb           w16, [x6, #-1]
    //     0xaa5a38: ldurb           w17, [x0, #-1]
    //     0xaa5a3c: and             x16, x17, x16, lsr #2
    //     0xaa5a40: tst             x16, HEAP, lsr #32
    //     0xaa5a44: b.eq            #0xaa5a4c
    //     0xaa5a48: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xaa5a4c: mov             x0, x2
    // 0xaa5a50: ArrayStore: r6[0] = r0  ; List_4
    //     0xaa5a50: stur            w0, [x6, #0x17]
    //     0xaa5a54: ldurb           w16, [x6, #-1]
    //     0xaa5a58: ldurb           w17, [x0, #-1]
    //     0xaa5a5c: and             x16, x17, x16, lsr #2
    //     0xaa5a60: tst             x16, HEAP, lsr #32
    //     0xaa5a64: b.eq            #0xaa5a6c
    //     0xaa5a68: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xaa5a6c: ldr             x0, [fp, #0x20]
    // 0xaa5a70: StoreField: r6->field_1b = r0
    //     0xaa5a70: stur            w0, [x6, #0x1b]
    //     0xaa5a74: ldurb           w16, [x6, #-1]
    //     0xaa5a78: ldurb           w17, [x0, #-1]
    //     0xaa5a7c: and             x16, x17, x16, lsr #2
    //     0xaa5a80: tst             x16, HEAP, lsr #32
    //     0xaa5a84: b.eq            #0xaa5a8c
    //     0xaa5a88: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xaa5a8c: ldr             x0, [fp, #0x10]
    // 0xaa5a90: StoreField: r6->field_1f = r0
    //     0xaa5a90: stur            w0, [x6, #0x1f]
    //     0xaa5a94: ldurb           w16, [x6, #-1]
    //     0xaa5a98: ldurb           w17, [x0, #-1]
    //     0xaa5a9c: and             x16, x17, x16, lsr #2
    //     0xaa5aa0: tst             x16, HEAP, lsr #32
    //     0xaa5aa4: b.eq            #0xaa5aac
    //     0xaa5aa8: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xaa5aac: LoadField: r1 = r4->field_f
    //     0xaa5aac: ldur            w1, [x4, #0xf]
    // 0xaa5ab0: DecompressPointer r1
    //     0xaa5ab0: add             x1, x1, HEAP, lsl #32
    // 0xaa5ab4: cmp             w1, NULL
    // 0xaa5ab8: b.eq            #0xaa5d90
    // 0xaa5abc: r0 = renderObject()
    //     0xaa5abc: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xaa5ac0: mov             x3, x0
    // 0xaa5ac4: stur            x3, [fp, #-0x20]
    // 0xaa5ac8: cmp             w3, NULL
    // 0xaa5acc: b.eq            #0xaa5d94
    // 0xaa5ad0: mov             x0, x3
    // 0xaa5ad4: r2 = Null
    //     0xaa5ad4: mov             x2, NULL
    // 0xaa5ad8: r1 = Null
    //     0xaa5ad8: mov             x1, NULL
    // 0xaa5adc: r4 = LoadClassIdInstr(r0)
    //     0xaa5adc: ldur            x4, [x0, #-1]
    //     0xaa5ae0: ubfx            x4, x4, #0xc, #0x14
    // 0xaa5ae4: sub             x4, x4, #0xbba
    // 0xaa5ae8: cmp             x4, #0x9a
    // 0xaa5aec: b.ls            #0xaa5b00
    // 0xaa5af0: r8 = RenderBox
    //     0xaa5af0: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0xaa5af4: r3 = Null
    //     0xaa5af4: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fe10] Null
    //     0xaa5af8: ldr             x3, [x3, #0xe10]
    // 0xaa5afc: r0 = RenderBox()
    //     0xaa5afc: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0xaa5b00: ldur            x3, [fp, #-0x18]
    // 0xaa5b04: LoadField: r0 = r3->field_13
    //     0xaa5b04: ldur            w0, [x3, #0x13]
    // 0xaa5b08: DecompressPointer r0
    //     0xaa5b08: add             x0, x0, HEAP, lsl #32
    // 0xaa5b0c: r16 = Sentinel
    //     0xaa5b0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa5b10: cmp             w0, w16
    // 0xaa5b14: b.eq            #0xaa5d98
    // 0xaa5b18: ldur            x4, [fp, #-8]
    // 0xaa5b1c: StoreField: r4->field_23 = r0
    //     0xaa5b1c: stur            w0, [x4, #0x23]
    //     0xaa5b20: ldurb           w16, [x4, #-1]
    //     0xaa5b24: ldurb           w17, [x0, #-1]
    //     0xaa5b28: and             x16, x17, x16, lsr #2
    //     0xaa5b2c: tst             x16, HEAP, lsr #32
    //     0xaa5b30: b.eq            #0xaa5b38
    //     0xaa5b34: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa5b38: LoadField: r2 = r3->field_b
    //     0xaa5b38: ldur            w2, [x3, #0xb]
    // 0xaa5b3c: DecompressPointer r2
    //     0xaa5b3c: add             x2, x2, HEAP, lsl #32
    // 0xaa5b40: cmp             w2, NULL
    // 0xaa5b44: b.eq            #0xaa5da4
    // 0xaa5b48: LoadField: r5 = r2->field_b
    //     0xaa5b48: ldur            x5, [x2, #0xb]
    // 0xaa5b4c: r0 = BoxInt64Instr(r5)
    //     0xaa5b4c: sbfiz           x0, x5, #1, #0x1f
    //     0xaa5b50: cmp             x5, x0, asr #1
    //     0xaa5b54: b.eq            #0xaa5b60
    //     0xaa5b58: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaa5b5c: stur            x5, [x0, #7]
    // 0xaa5b60: StoreField: r4->field_27 = r0
    //     0xaa5b60: stur            w0, [x4, #0x27]
    //     0xaa5b64: tbz             w0, #0, #0xaa5b80
    //     0xaa5b68: ldurb           w16, [x4, #-1]
    //     0xaa5b6c: ldurb           w17, [x0, #-1]
    //     0xaa5b70: and             x16, x17, x16, lsr #2
    //     0xaa5b74: tst             x16, HEAP, lsr #32
    //     0xaa5b78: b.eq            #0xaa5b80
    //     0xaa5b7c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa5b80: LoadField: r0 = r2->field_13
    //     0xaa5b80: ldur            w0, [x2, #0x13]
    // 0xaa5b84: DecompressPointer r0
    //     0xaa5b84: add             x0, x0, HEAP, lsl #32
    // 0xaa5b88: StoreField: r4->field_2b = r0
    //     0xaa5b88: stur            w0, [x4, #0x2b]
    //     0xaa5b8c: ldurb           w16, [x4, #-1]
    //     0xaa5b90: ldurb           w17, [x0, #-1]
    //     0xaa5b94: and             x16, x17, x16, lsr #2
    //     0xaa5b98: tst             x16, HEAP, lsr #32
    //     0xaa5b9c: b.eq            #0xaa5ba4
    //     0xaa5ba0: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa5ba4: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xaa5ba4: ldur            w0, [x2, #0x17]
    // 0xaa5ba8: DecompressPointer r0
    //     0xaa5ba8: add             x0, x0, HEAP, lsl #32
    // 0xaa5bac: StoreField: r4->field_43 = r0
    //     0xaa5bac: stur            w0, [x4, #0x43]
    //     0xaa5bb0: ldurb           w16, [x4, #-1]
    //     0xaa5bb4: ldurb           w17, [x0, #-1]
    //     0xaa5bb8: and             x16, x17, x16, lsr #2
    //     0xaa5bbc: tst             x16, HEAP, lsr #32
    //     0xaa5bc0: b.eq            #0xaa5bc8
    //     0xaa5bc4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa5bc8: ldur            x0, [fp, #-0x10]
    // 0xaa5bcc: StoreField: r4->field_2f = r0
    //     0xaa5bcc: stur            w0, [x4, #0x2f]
    //     0xaa5bd0: ldurb           w16, [x4, #-1]
    //     0xaa5bd4: ldurb           w17, [x0, #-1]
    //     0xaa5bd8: and             x16, x17, x16, lsr #2
    //     0xaa5bdc: tst             x16, HEAP, lsr #32
    //     0xaa5be0: b.eq            #0xaa5be8
    //     0xaa5be4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa5be8: ldur            x1, [fp, #-0x20]
    // 0xaa5bec: ldur            x2, [fp, #-0x10]
    // 0xaa5bf0: r0 = globalToLocal()
    //     0xaa5bf0: bl              #0x6a9a0c  ; [package:flutter/src/rendering/box.dart] RenderBox::globalToLocal
    // 0xaa5bf4: ldur            x2, [fp, #-8]
    // 0xaa5bf8: StoreField: r2->field_33 = r0
    //     0xaa5bf8: stur            w0, [x2, #0x33]
    //     0xaa5bfc: ldurb           w16, [x2, #-1]
    //     0xaa5c00: ldurb           w17, [x0, #-1]
    //     0xaa5c04: and             x16, x17, x16, lsr #2
    //     0xaa5c08: tst             x16, HEAP, lsr #32
    //     0xaa5c0c: b.eq            #0xaa5c14
    //     0xaa5c10: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xaa5c14: ldur            x0, [fp, #-0x18]
    // 0xaa5c18: LoadField: r1 = r0->field_f
    //     0xaa5c18: ldur            w1, [x0, #0xf]
    // 0xaa5c1c: DecompressPointer r1
    //     0xaa5c1c: add             x1, x1, HEAP, lsl #32
    // 0xaa5c20: cmp             w1, NULL
    // 0xaa5c24: b.eq            #0xaa5da8
    // 0xaa5c28: r0 = renderObject()
    //     0xaa5c28: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xaa5c2c: r1 = LoadClassIdInstr(r0)
    //     0xaa5c2c: ldur            x1, [x0, #-1]
    //     0xaa5c30: ubfx            x1, x1, #0xc, #0x14
    // 0xaa5c34: sub             x16, x1, #0xbba
    // 0xaa5c38: cmp             x16, #0x9a
    // 0xaa5c3c: b.hi            #0xaa5c50
    // 0xaa5c40: mov             x1, x0
    // 0xaa5c44: r0 = size()
    //     0xaa5c44: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xaa5c48: mov             x1, x0
    // 0xaa5c4c: b               #0xaa5c54
    // 0xaa5c50: r1 = Null
    //     0xaa5c50: mov             x1, NULL
    // 0xaa5c54: ldur            x3, [fp, #-8]
    // 0xaa5c58: ldur            x4, [fp, #-0x20]
    // 0xaa5c5c: cmp             w1, NULL
    // 0xaa5c60: b.eq            #0xaa5dac
    // 0xaa5c64: mov             x0, x1
    // 0xaa5c68: StoreField: r3->field_37 = r0
    //     0xaa5c68: stur            w0, [x3, #0x37]
    //     0xaa5c6c: ldurb           w16, [x3, #-1]
    //     0xaa5c70: ldurb           w17, [x0, #-1]
    //     0xaa5c74: and             x16, x17, x16, lsr #2
    //     0xaa5c78: tst             x16, HEAP, lsr #32
    //     0xaa5c7c: b.eq            #0xaa5c84
    //     0xaa5c80: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaa5c84: ldr             x2, [fp, #0x18]
    // 0xaa5c88: r0 = _sizeExtent()
    //     0xaa5c88: bl              #0x997cf0  ; [package:flutter/src/widgets/reorderable_list.dart] ::_sizeExtent
    // 0xaa5c8c: r0 = inline_Allocate_Double()
    //     0xaa5c8c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaa5c90: add             x0, x0, #0x10
    //     0xaa5c94: cmp             x1, x0
    //     0xaa5c98: b.ls            #0xaa5db0
    //     0xaa5c9c: str             x0, [THR, #0x50]  ; THR::top
    //     0xaa5ca0: sub             x0, x0, #0xf
    //     0xaa5ca4: movz            x1, #0xe15c
    //     0xaa5ca8: movk            x1, #0x3, lsl #16
    //     0xaa5cac: stur            x1, [x0, #-1]
    // 0xaa5cb0: StoreField: r0->field_7 = d0
    //     0xaa5cb0: stur            d0, [x0, #7]
    // 0xaa5cb4: ldur            x3, [fp, #-8]
    // 0xaa5cb8: StoreField: r3->field_3f = r0
    //     0xaa5cb8: stur            w0, [x3, #0x3f]
    //     0xaa5cbc: ldurb           w16, [x3, #-1]
    //     0xaa5cc0: ldurb           w17, [x0, #-1]
    //     0xaa5cc4: and             x16, x17, x16, lsr #2
    //     0xaa5cc8: tst             x16, HEAP, lsr #32
    //     0xaa5ccc: b.eq            #0xaa5cd4
    //     0xaa5cd0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaa5cd4: ldur            x0, [fp, #-0x20]
    // 0xaa5cd8: LoadField: r4 = r0->field_27
    //     0xaa5cd8: ldur            w4, [x0, #0x27]
    // 0xaa5cdc: DecompressPointer r4
    //     0xaa5cdc: add             x4, x4, HEAP, lsl #32
    // 0xaa5ce0: stur            x4, [fp, #-0x10]
    // 0xaa5ce4: cmp             w4, NULL
    // 0xaa5ce8: b.eq            #0xaa5d6c
    // 0xaa5cec: ldur            x5, [fp, #-0x18]
    // 0xaa5cf0: mov             x0, x4
    // 0xaa5cf4: r2 = Null
    //     0xaa5cf4: mov             x2, NULL
    // 0xaa5cf8: r1 = Null
    //     0xaa5cf8: mov             x1, NULL
    // 0xaa5cfc: r4 = LoadClassIdInstr(r0)
    //     0xaa5cfc: ldur            x4, [x0, #-1]
    //     0xaa5d00: ubfx            x4, x4, #0xc, #0x14
    // 0xaa5d04: sub             x4, x4, #0xc83
    // 0xaa5d08: cmp             x4, #1
    // 0xaa5d0c: b.ls            #0xaa5d20
    // 0xaa5d10: r8 = BoxConstraints
    //     0xaa5d10: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0xaa5d14: r3 = Null
    //     0xaa5d14: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fe20] Null
    //     0xaa5d18: ldr             x3, [x3, #0xe20]
    // 0xaa5d1c: r0 = BoxConstraints()
    //     0xaa5d1c: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0xaa5d20: ldur            x0, [fp, #-0x10]
    // 0xaa5d24: ldur            x1, [fp, #-8]
    // 0xaa5d28: StoreField: r1->field_3b = r0
    //     0xaa5d28: stur            w0, [x1, #0x3b]
    //     0xaa5d2c: ldurb           w16, [x1, #-1]
    //     0xaa5d30: ldurb           w17, [x0, #-1]
    //     0xaa5d34: and             x16, x17, x16, lsr #2
    //     0xaa5d38: tst             x16, HEAP, lsr #32
    //     0xaa5d3c: b.eq            #0xaa5d44
    //     0xaa5d40: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa5d44: ldur            x0, [fp, #-0x18]
    // 0xaa5d48: LoadField: r1 = r0->field_f
    //     0xaa5d48: ldur            w1, [x0, #0xf]
    // 0xaa5d4c: DecompressPointer r1
    //     0xaa5d4c: add             x1, x1, HEAP, lsl #32
    // 0xaa5d50: cmp             w1, NULL
    // 0xaa5d54: b.eq            #0xaa5dc0
    // 0xaa5d58: r0 = of()
    //     0xaa5d58: bl              #0x9a7dc8  ; [package:flutter/src/widgets/scrollable.dart] Scrollable::of
    // 0xaa5d5c: r0 = Null
    //     0xaa5d5c: mov             x0, NULL
    // 0xaa5d60: LeaveFrame
    //     0xaa5d60: mov             SP, fp
    //     0xaa5d64: ldp             fp, lr, [SP], #0x10
    // 0xaa5d68: ret
    //     0xaa5d68: ret             
    // 0xaa5d6c: r0 = StateError()
    //     0xaa5d6c: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xaa5d70: mov             x1, x0
    // 0xaa5d74: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0xaa5d74: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0xaa5d78: StoreField: r1->field_b = r0
    //     0xaa5d78: stur            w0, [x1, #0xb]
    // 0xaa5d7c: mov             x0, x1
    // 0xaa5d80: r0 = Throw()
    //     0xaa5d80: bl              #0xec04b8  ; ThrowStub
    // 0xaa5d84: brk             #0
    // 0xaa5d88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa5d88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa5d8c: b               #0xaa59a8
    // 0xaa5d90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5d90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa5d94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5d94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa5d98: r9 = _listState
    //     0xaa5d98: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fe00] Field <_ReorderableItemState@318218688._listState@318218688>: late (offset: 0x14)
    //     0xaa5d9c: ldr             x9, [x9, #0xe00]
    // 0xaa5da0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa5da0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa5da4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5da4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa5da8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5da8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa5dac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5dac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa5db0: SaveReg d0
    //     0xaa5db0: str             q0, [SP, #-0x10]!
    // 0xaa5db4: r0 = AllocateDouble()
    //     0xaa5db4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xaa5db8: RestoreReg d0
    //     0xaa5db8: ldr             q0, [SP], #0x10
    // 0xaa5dbc: b               #0xaa5cb0
    // 0xaa5dc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5dc0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget createProxy(dynamic, BuildContext) {
    // ** addr: 0xaa5dd0, size: 0x3c
    // 0xaa5dd0: EnterFrame
    //     0xaa5dd0: stp             fp, lr, [SP, #-0x10]!
    //     0xaa5dd4: mov             fp, SP
    // 0xaa5dd8: ldr             x0, [fp, #0x18]
    // 0xaa5ddc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa5ddc: ldur            w1, [x0, #0x17]
    // 0xaa5de0: DecompressPointer r1
    //     0xaa5de0: add             x1, x1, HEAP, lsl #32
    // 0xaa5de4: CheckStackOverflow
    //     0xaa5de4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa5de8: cmp             SP, x16
    //     0xaa5dec: b.ls            #0xaa5e04
    // 0xaa5df0: ldr             x2, [fp, #0x10]
    // 0xaa5df4: r0 = createProxy()
    //     0xaa5df4: bl              #0xaa5e0c  ; [package:flutter/src/widgets/reorderable_list.dart] _DragInfo::createProxy
    // 0xaa5df8: LeaveFrame
    //     0xaa5df8: mov             SP, fp
    //     0xaa5dfc: ldp             fp, lr, [SP], #0x10
    // 0xaa5e00: ret
    //     0xaa5e00: ret             
    // 0xaa5e04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa5e04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa5e08: b               #0xaa5df0
  }
  _ createProxy(/* No info */) {
    // ** addr: 0xaa5e0c, size: 0x200
    // 0xaa5e0c: EnterFrame
    //     0xaa5e0c: stp             fp, lr, [SP, #-0x10]!
    //     0xaa5e10: mov             fp, SP
    // 0xaa5e14: AllocStack(0x50)
    //     0xaa5e14: sub             SP, SP, #0x50
    // 0xaa5e18: SetupParameters(_DragInfo this /* r1 => r3, fp-0x38 */, dynamic _ /* r2 => r0, fp-0x40 */)
    //     0xaa5e18: mov             x3, x1
    //     0xaa5e1c: mov             x0, x2
    //     0xaa5e20: stur            x1, [fp, #-0x38]
    //     0xaa5e24: stur            x2, [fp, #-0x40]
    // 0xaa5e28: CheckStackOverflow
    //     0xaa5e28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa5e2c: cmp             SP, x16
    //     0xaa5e30: b.ls            #0xaa5fa0
    // 0xaa5e34: LoadField: r4 = r3->field_43
    //     0xaa5e34: ldur            w4, [x3, #0x43]
    // 0xaa5e38: DecompressPointer r4
    //     0xaa5e38: add             x4, x4, HEAP, lsl #32
    // 0xaa5e3c: r16 = Sentinel
    //     0xaa5e3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa5e40: cmp             w4, w16
    // 0xaa5e44: b.eq            #0xaa5fa8
    // 0xaa5e48: stur            x4, [fp, #-0x30]
    // 0xaa5e4c: LoadField: r5 = r3->field_23
    //     0xaa5e4c: ldur            w5, [x3, #0x23]
    // 0xaa5e50: DecompressPointer r5
    //     0xaa5e50: add             x5, x5, HEAP, lsl #32
    // 0xaa5e54: r16 = Sentinel
    //     0xaa5e54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa5e58: cmp             w5, w16
    // 0xaa5e5c: b.eq            #0xaa5fb4
    // 0xaa5e60: stur            x5, [fp, #-0x28]
    // 0xaa5e64: LoadField: r6 = r3->field_27
    //     0xaa5e64: ldur            w6, [x3, #0x27]
    // 0xaa5e68: DecompressPointer r6
    //     0xaa5e68: add             x6, x6, HEAP, lsl #32
    // 0xaa5e6c: r16 = Sentinel
    //     0xaa5e6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa5e70: cmp             w6, w16
    // 0xaa5e74: b.eq            #0xaa5fc0
    // 0xaa5e78: stur            x6, [fp, #-0x20]
    // 0xaa5e7c: LoadField: r7 = r3->field_37
    //     0xaa5e7c: ldur            w7, [x3, #0x37]
    // 0xaa5e80: DecompressPointer r7
    //     0xaa5e80: add             x7, x7, HEAP, lsl #32
    // 0xaa5e84: r16 = Sentinel
    //     0xaa5e84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa5e88: cmp             w7, w16
    // 0xaa5e8c: b.eq            #0xaa5fcc
    // 0xaa5e90: stur            x7, [fp, #-0x18]
    // 0xaa5e94: LoadField: r8 = r3->field_3b
    //     0xaa5e94: ldur            w8, [x3, #0x3b]
    // 0xaa5e98: DecompressPointer r8
    //     0xaa5e98: add             x8, x8, HEAP, lsl #32
    // 0xaa5e9c: r16 = Sentinel
    //     0xaa5e9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa5ea0: cmp             w8, w16
    // 0xaa5ea4: b.eq            #0xaa5fd8
    // 0xaa5ea8: stur            x8, [fp, #-0x10]
    // 0xaa5eac: LoadField: r10 = r3->field_47
    //     0xaa5eac: ldur            w10, [x3, #0x47]
    // 0xaa5eb0: DecompressPointer r10
    //     0xaa5eb0: add             x10, x10, HEAP, lsl #32
    // 0xaa5eb4: stur            x10, [fp, #-8]
    // 0xaa5eb8: cmp             w10, NULL
    // 0xaa5ebc: b.eq            #0xaa5fe4
    // 0xaa5ec0: LoadField: r1 = r3->field_2f
    //     0xaa5ec0: ldur            w1, [x3, #0x2f]
    // 0xaa5ec4: DecompressPointer r1
    //     0xaa5ec4: add             x1, x1, HEAP, lsl #32
    // 0xaa5ec8: r16 = Sentinel
    //     0xaa5ec8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa5ecc: cmp             w1, w16
    // 0xaa5ed0: b.eq            #0xaa5fe8
    // 0xaa5ed4: LoadField: r2 = r3->field_33
    //     0xaa5ed4: ldur            w2, [x3, #0x33]
    // 0xaa5ed8: DecompressPointer r2
    //     0xaa5ed8: add             x2, x2, HEAP, lsl #32
    // 0xaa5edc: r16 = Sentinel
    //     0xaa5edc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa5ee0: cmp             w2, w16
    // 0xaa5ee4: b.eq            #0xaa5ff4
    // 0xaa5ee8: r0 = -()
    //     0xaa5ee8: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xaa5eec: ldur            x1, [fp, #-0x40]
    // 0xaa5ef0: stur            x0, [fp, #-0x40]
    // 0xaa5ef4: r0 = _overlayOrigin()
    //     0xaa5ef4: bl              #0xaa6018  ; [package:flutter/src/widgets/reorderable_list.dart] ::_overlayOrigin
    // 0xaa5ef8: ldur            x1, [fp, #-0x40]
    // 0xaa5efc: mov             x2, x0
    // 0xaa5f00: r0 = -()
    //     0xaa5f00: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xaa5f04: mov             x1, x0
    // 0xaa5f08: ldur            x0, [fp, #-0x38]
    // 0xaa5f0c: stur            x1, [fp, #-0x50]
    // 0xaa5f10: LoadField: r2 = r0->field_1b
    //     0xaa5f10: ldur            w2, [x0, #0x1b]
    // 0xaa5f14: DecompressPointer r2
    //     0xaa5f14: add             x2, x2, HEAP, lsl #32
    // 0xaa5f18: stur            x2, [fp, #-0x48]
    // 0xaa5f1c: LoadField: r3 = r0->field_2b
    //     0xaa5f1c: ldur            w3, [x0, #0x2b]
    // 0xaa5f20: DecompressPointer r3
    //     0xaa5f20: add             x3, x3, HEAP, lsl #32
    // 0xaa5f24: r16 = Sentinel
    //     0xaa5f24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa5f28: cmp             w3, w16
    // 0xaa5f2c: b.eq            #0xaa6000
    // 0xaa5f30: stur            x3, [fp, #-0x40]
    // 0xaa5f34: r0 = _DragItemProxy()
    //     0xaa5f34: bl              #0xaa600c  ; Allocate_DragItemProxyStub -> _DragItemProxy (size=0x30)
    // 0xaa5f38: mov             x1, x0
    // 0xaa5f3c: ldur            x0, [fp, #-0x28]
    // 0xaa5f40: StoreField: r1->field_b = r0
    //     0xaa5f40: stur            w0, [x1, #0xb]
    // 0xaa5f44: ldur            x0, [fp, #-0x20]
    // 0xaa5f48: r2 = LoadInt32Instr(r0)
    //     0xaa5f48: sbfx            x2, x0, #1, #0x1f
    //     0xaa5f4c: tbz             w0, #0, #0xaa5f54
    //     0xaa5f50: ldur            x2, [x0, #7]
    // 0xaa5f54: StoreField: r1->field_f = r2
    //     0xaa5f54: stur            x2, [x1, #0xf]
    // 0xaa5f58: ldur            x0, [fp, #-0x40]
    // 0xaa5f5c: ArrayStore: r1[0] = r0  ; List_4
    //     0xaa5f5c: stur            w0, [x1, #0x17]
    // 0xaa5f60: ldur            x0, [fp, #-0x50]
    // 0xaa5f64: StoreField: r1->field_1b = r0
    //     0xaa5f64: stur            w0, [x1, #0x1b]
    // 0xaa5f68: ldur            x0, [fp, #-0x18]
    // 0xaa5f6c: StoreField: r1->field_1f = r0
    //     0xaa5f6c: stur            w0, [x1, #0x1f]
    // 0xaa5f70: ldur            x0, [fp, #-0x10]
    // 0xaa5f74: StoreField: r1->field_23 = r0
    //     0xaa5f74: stur            w0, [x1, #0x23]
    // 0xaa5f78: ldur            x0, [fp, #-8]
    // 0xaa5f7c: StoreField: r1->field_27 = r0
    //     0xaa5f7c: stur            w0, [x1, #0x27]
    // 0xaa5f80: ldur            x0, [fp, #-0x48]
    // 0xaa5f84: StoreField: r1->field_2b = r0
    //     0xaa5f84: stur            w0, [x1, #0x2b]
    // 0xaa5f88: mov             x2, x1
    // 0xaa5f8c: ldur            x1, [fp, #-0x30]
    // 0xaa5f90: r0 = wrap()
    //     0xaa5f90: bl              #0x6a4dec  ; [package:flutter/src/widgets/inherited_theme.dart] CapturedThemes::wrap
    // 0xaa5f94: LeaveFrame
    //     0xaa5f94: mov             SP, fp
    //     0xaa5f98: ldp             fp, lr, [SP], #0x10
    // 0xaa5f9c: ret
    //     0xaa5f9c: ret             
    // 0xaa5fa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa5fa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa5fa4: b               #0xaa5e34
    // 0xaa5fa8: r9 = capturedThemes
    //     0xaa5fa8: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd18] Field <<EMAIL>>: late (offset: 0x44)
    //     0xaa5fac: ldr             x9, [x9, #0xd18]
    // 0xaa5fb0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa5fb0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa5fb4: r9 = listState
    //     0xaa5fb4: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd20] Field <<EMAIL>>: late (offset: 0x24)
    //     0xaa5fb8: ldr             x9, [x9, #0xd20]
    // 0xaa5fbc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa5fbc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa5fc0: r9 = index
    //     0xaa5fc0: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd28] Field <<EMAIL>>: late (offset: 0x28)
    //     0xaa5fc4: ldr             x9, [x9, #0xd28]
    // 0xaa5fc8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa5fc8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa5fcc: r9 = itemSize
    //     0xaa5fcc: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd30] Field <<EMAIL>>: late (offset: 0x38)
    //     0xaa5fd0: ldr             x9, [x9, #0xd30]
    // 0xaa5fd4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa5fd4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa5fd8: r9 = itemLayoutConstraints
    //     0xaa5fd8: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd38] Field <<EMAIL>>: late (offset: 0x3c)
    //     0xaa5fdc: ldr             x9, [x9, #0xd38]
    // 0xaa5fe0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa5fe0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa5fe4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5fe4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa5fe8: r9 = dragPosition
    //     0xaa5fe8: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd40] Field <<EMAIL>>: late (offset: 0x30)
    //     0xaa5fec: ldr             x9, [x9, #0xd40]
    // 0xaa5ff0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa5ff0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa5ff4: r9 = dragOffset
    //     0xaa5ff4: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd48] Field <<EMAIL>>: late (offset: 0x34)
    //     0xaa5ff8: ldr             x9, [x9, #0xd48]
    // 0xaa5ffc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa5ffc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa6000: r9 = child
    //     0xaa6000: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd50] Field <<EMAIL>>: late (offset: 0x2c)
    //     0xaa6004: ldr             x9, [x9, #0xd50]
    // 0xaa6008: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa6008: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ cancel(/* No info */) {
    // ** addr: 0xd23304, size: 0x74
    // 0xd23304: EnterFrame
    //     0xd23304: stp             fp, lr, [SP, #-0x10]!
    //     0xd23308: mov             fp, SP
    // 0xd2330c: AllocStack(0x8)
    //     0xd2330c: sub             SP, SP, #8
    // 0xd23310: SetupParameters(_DragInfo this /* r1 => r2, fp-0x8 */)
    //     0xd23310: mov             x2, x1
    //     0xd23314: stur            x1, [fp, #-8]
    // 0xd23318: CheckStackOverflow
    //     0xd23318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2331c: cmp             SP, x16
    //     0xd23320: b.ls            #0xd2336c
    // 0xd23324: LoadField: r1 = r2->field_47
    //     0xd23324: ldur            w1, [x2, #0x47]
    // 0xd23328: DecompressPointer r1
    //     0xd23328: add             x1, x1, HEAP, lsl #32
    // 0xd2332c: cmp             w1, NULL
    // 0xd23330: b.eq            #0xd2333c
    // 0xd23334: r0 = dispose()
    //     0xd23334: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xd23338: ldur            x2, [fp, #-8]
    // 0xd2333c: StoreField: r2->field_47 = rNULL
    //     0xd2333c: stur            NULL, [x2, #0x47]
    // 0xd23340: LoadField: r0 = r2->field_13
    //     0xd23340: ldur            w0, [x2, #0x13]
    // 0xd23344: DecompressPointer r0
    //     0xd23344: add             x0, x0, HEAP, lsl #32
    // 0xd23348: cmp             w0, NULL
    // 0xd2334c: b.eq            #0xd23374
    // 0xd23350: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd23350: ldur            w1, [x0, #0x17]
    // 0xd23354: DecompressPointer r1
    //     0xd23354: add             x1, x1, HEAP, lsl #32
    // 0xd23358: r0 = _dragCancel()
    //     0xd23358: bl              #0xaa6984  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragCancel
    // 0xd2335c: r0 = Null
    //     0xd2335c: mov             x0, NULL
    // 0xd23360: LeaveFrame
    //     0xd23360: mov             SP, fp
    //     0xd23364: ldp             fp, lr, [SP], #0x10
    // 0xd23368: ret
    //     0xd23368: ret             
    // 0xd2336c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd2336c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd23370: b               #0xd23324
    // 0xd23374: r0 = NullErrorSharedWithoutFPURegs()
    //     0xd23374: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ end(/* No info */) {
    // ** addr: 0xd2dff8, size: 0x78
    // 0xd2dff8: EnterFrame
    //     0xd2dff8: stp             fp, lr, [SP, #-0x10]!
    //     0xd2dffc: mov             fp, SP
    // 0xd2e000: AllocStack(0x8)
    //     0xd2e000: sub             SP, SP, #8
    // 0xd2e004: SetupParameters(_DragInfo this /* r1 => r0, fp-0x8 */)
    //     0xd2e004: mov             x0, x1
    //     0xd2e008: stur            x1, [fp, #-8]
    // 0xd2e00c: CheckStackOverflow
    //     0xd2e00c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2e010: cmp             SP, x16
    //     0xd2e014: b.ls            #0xd2e060
    // 0xd2e018: LoadField: r1 = r0->field_47
    //     0xd2e018: ldur            w1, [x0, #0x47]
    // 0xd2e01c: DecompressPointer r1
    //     0xd2e01c: add             x1, x1, HEAP, lsl #32
    // 0xd2e020: cmp             w1, NULL
    // 0xd2e024: b.eq            #0xd2e068
    // 0xd2e028: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xd2e028: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xd2e02c: r0 = reverse()
    //     0xd2e02c: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0xd2e030: ldur            x2, [fp, #-8]
    // 0xd2e034: LoadField: r0 = r2->field_f
    //     0xd2e034: ldur            w0, [x2, #0xf]
    // 0xd2e038: DecompressPointer r0
    //     0xd2e038: add             x0, x0, HEAP, lsl #32
    // 0xd2e03c: cmp             w0, NULL
    // 0xd2e040: b.eq            #0xd2e06c
    // 0xd2e044: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd2e044: ldur            w1, [x0, #0x17]
    // 0xd2e048: DecompressPointer r1
    //     0xd2e048: add             x1, x1, HEAP, lsl #32
    // 0xd2e04c: r0 = _dragEnd()
    //     0xd2e04c: bl              #0xaa6118  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragEnd
    // 0xd2e050: r0 = Null
    //     0xd2e050: mov             x0, NULL
    // 0xd2e054: LeaveFrame
    //     0xd2e054: mov             SP, fp
    //     0xd2e058: ldp             fp, lr, [SP], #0x10
    // 0xd2e05c: ret
    //     0xd2e05c: ret             
    // 0xd2e060: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd2e060: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd2e064: b               #0xd2e018
    // 0xd2e068: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd2e068: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd2e06c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xd2e06c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ update(/* No info */) {
    // ** addr: 0xd8d6b4, size: 0xe0
    // 0xd8d6b4: EnterFrame
    //     0xd8d6b4: stp             fp, lr, [SP, #-0x10]!
    //     0xd8d6b8: mov             fp, SP
    // 0xd8d6bc: AllocStack(0x10)
    //     0xd8d6bc: sub             SP, SP, #0x10
    // 0xd8d6c0: SetupParameters(_DragInfo this /* r1 => r0, fp-0x10 */)
    //     0xd8d6c0: mov             x0, x1
    //     0xd8d6c4: stur            x1, [fp, #-0x10]
    // 0xd8d6c8: CheckStackOverflow
    //     0xd8d6c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8d6cc: cmp             SP, x16
    //     0xd8d6d0: b.ls            #0xd8d77c
    // 0xd8d6d4: LoadField: r3 = r2->field_b
    //     0xd8d6d4: ldur            w3, [x2, #0xb]
    // 0xd8d6d8: DecompressPointer r3
    //     0xd8d6d8: add             x3, x3, HEAP, lsl #32
    // 0xd8d6dc: stur            x3, [fp, #-8]
    // 0xd8d6e0: LoadField: r2 = r0->field_7
    //     0xd8d6e0: ldur            w2, [x0, #7]
    // 0xd8d6e4: DecompressPointer r2
    //     0xd8d6e4: add             x2, x2, HEAP, lsl #32
    // 0xd8d6e8: mov             x1, x3
    // 0xd8d6ec: r0 = _restrictAxis()
    //     0xd8d6ec: bl              #0xd8d794  ; [package:flutter/src/widgets/reorderable_list.dart] ::_restrictAxis
    // 0xd8d6f0: mov             x1, x0
    // 0xd8d6f4: ldur            x0, [fp, #-0x10]
    // 0xd8d6f8: LoadField: r2 = r0->field_2f
    //     0xd8d6f8: ldur            w2, [x0, #0x2f]
    // 0xd8d6fc: DecompressPointer r2
    //     0xd8d6fc: add             x2, x2, HEAP, lsl #32
    // 0xd8d700: r16 = Sentinel
    //     0xd8d700: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd8d704: cmp             w2, w16
    // 0xd8d708: b.eq            #0xd8d784
    // 0xd8d70c: mov             x16, x1
    // 0xd8d710: mov             x1, x2
    // 0xd8d714: mov             x2, x16
    // 0xd8d718: r0 = +()
    //     0xd8d718: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0xd8d71c: mov             x1, x0
    // 0xd8d720: ldur            x2, [fp, #-0x10]
    // 0xd8d724: StoreField: r2->field_2f = r0
    //     0xd8d724: stur            w0, [x2, #0x2f]
    //     0xd8d728: ldurb           w16, [x2, #-1]
    //     0xd8d72c: ldurb           w17, [x0, #-1]
    //     0xd8d730: and             x16, x17, x16, lsr #2
    //     0xd8d734: tst             x16, HEAP, lsr #32
    //     0xd8d738: b.eq            #0xd8d740
    //     0xd8d73c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xd8d740: LoadField: r0 = r2->field_b
    //     0xd8d740: ldur            w0, [x2, #0xb]
    // 0xd8d744: DecompressPointer r0
    //     0xd8d744: add             x0, x0, HEAP, lsl #32
    // 0xd8d748: cmp             w0, NULL
    // 0xd8d74c: b.eq            #0xd8d790
    // 0xd8d750: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xd8d750: ldur            w3, [x0, #0x17]
    // 0xd8d754: DecompressPointer r3
    //     0xd8d754: add             x3, x3, HEAP, lsl #32
    // 0xd8d758: mov             x16, x1
    // 0xd8d75c: mov             x1, x3
    // 0xd8d760: mov             x3, x16
    // 0xd8d764: ldur            x5, [fp, #-8]
    // 0xd8d768: r0 = _dragUpdate()
    //     0xd8d768: bl              #0xaa6a2c  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragUpdate
    // 0xd8d76c: r0 = Null
    //     0xd8d76c: mov             x0, NULL
    // 0xd8d770: LeaveFrame
    //     0xd8d770: mov             SP, fp
    //     0xd8d774: ldp             fp, lr, [SP], #0x10
    // 0xd8d778: ret
    //     0xd8d778: ret             
    // 0xd8d77c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8d77c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8d780: b               #0xd8d6d4
    // 0xd8d784: r9 = dragPosition
    //     0xd8d784: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd40] Field <<EMAIL>>: late (offset: 0x30)
    //     0xd8d788: ldr             x9, [x9, #0xd40]
    // 0xd8d78c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd8d78c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd8d790: r0 = NullErrorSharedWithoutFPURegs()
    //     0xd8d790: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
}

// class id: 3576, size: 0x20, field offset: 0x10
//   const constructor, 
class _ReorderableItemGlobalKey extends GlobalObjectKey<dynamic> {

  _ ==(/* No info */) {
    // ** addr: 0xd5bf44, size: 0x11c
    // 0xd5bf44: EnterFrame
    //     0xd5bf44: stp             fp, lr, [SP, #-0x10]!
    //     0xd5bf48: mov             fp, SP
    // 0xd5bf4c: AllocStack(0x10)
    //     0xd5bf4c: sub             SP, SP, #0x10
    // 0xd5bf50: CheckStackOverflow
    //     0xd5bf50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd5bf54: cmp             SP, x16
    //     0xd5bf58: b.ls            #0xd5c058
    // 0xd5bf5c: ldr             x0, [fp, #0x10]
    // 0xd5bf60: cmp             w0, NULL
    // 0xd5bf64: b.ne            #0xd5bf78
    // 0xd5bf68: r0 = false
    //     0xd5bf68: add             x0, NULL, #0x30  ; false
    // 0xd5bf6c: LeaveFrame
    //     0xd5bf6c: mov             SP, fp
    //     0xd5bf70: ldp             fp, lr, [SP], #0x10
    // 0xd5bf74: ret
    //     0xd5bf74: ret             
    // 0xd5bf78: str             x0, [SP]
    // 0xd5bf7c: r0 = runtimeType()
    //     0xd5bf7c: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd5bf80: r1 = LoadClassIdInstr(r0)
    //     0xd5bf80: ldur            x1, [x0, #-1]
    //     0xd5bf84: ubfx            x1, x1, #0xc, #0x14
    // 0xd5bf88: r16 = _ReorderableItemGlobalKey
    //     0xd5bf88: add             x16, PP, #0x59, lsl #12  ; [pp+0x59bc0] Type: _ReorderableItemGlobalKey
    //     0xd5bf8c: ldr             x16, [x16, #0xbc0]
    // 0xd5bf90: stp             x16, x0, [SP]
    // 0xd5bf94: mov             x0, x1
    // 0xd5bf98: mov             lr, x0
    // 0xd5bf9c: ldr             lr, [x21, lr, lsl #3]
    // 0xd5bfa0: blr             lr
    // 0xd5bfa4: tbz             w0, #4, #0xd5bfb8
    // 0xd5bfa8: r0 = false
    //     0xd5bfa8: add             x0, NULL, #0x30  ; false
    // 0xd5bfac: LeaveFrame
    //     0xd5bfac: mov             SP, fp
    //     0xd5bfb0: ldp             fp, lr, [SP], #0x10
    // 0xd5bfb4: ret
    //     0xd5bfb4: ret             
    // 0xd5bfb8: ldr             x1, [fp, #0x10]
    // 0xd5bfbc: r0 = 60
    //     0xd5bfbc: movz            x0, #0x3c
    // 0xd5bfc0: branchIfSmi(r1, 0xd5bfcc)
    //     0xd5bfc0: tbz             w1, #0, #0xd5bfcc
    // 0xd5bfc4: r0 = LoadClassIdInstr(r1)
    //     0xd5bfc4: ldur            x0, [x1, #-1]
    //     0xd5bfc8: ubfx            x0, x0, #0xc, #0x14
    // 0xd5bfcc: cmp             x0, #0xdf8
    // 0xd5bfd0: b.ne            #0xd5c048
    // 0xd5bfd4: ldr             x2, [fp, #0x18]
    // 0xd5bfd8: LoadField: r0 = r1->field_f
    //     0xd5bfd8: ldur            w0, [x1, #0xf]
    // 0xd5bfdc: DecompressPointer r0
    //     0xd5bfdc: add             x0, x0, HEAP, lsl #32
    // 0xd5bfe0: LoadField: r3 = r2->field_f
    //     0xd5bfe0: ldur            w3, [x2, #0xf]
    // 0xd5bfe4: DecompressPointer r3
    //     0xd5bfe4: add             x3, x3, HEAP, lsl #32
    // 0xd5bfe8: r4 = LoadClassIdInstr(r0)
    //     0xd5bfe8: ldur            x4, [x0, #-1]
    //     0xd5bfec: ubfx            x4, x4, #0xc, #0x14
    // 0xd5bff0: stp             x3, x0, [SP]
    // 0xd5bff4: mov             x0, x4
    // 0xd5bff8: mov             lr, x0
    // 0xd5bffc: ldr             lr, [x21, lr, lsl #3]
    // 0xd5c000: blr             lr
    // 0xd5c004: tbnz            w0, #4, #0xd5c048
    // 0xd5c008: ldr             x2, [fp, #0x18]
    // 0xd5c00c: ldr             x1, [fp, #0x10]
    // 0xd5c010: LoadField: r3 = r1->field_13
    //     0xd5c010: ldur            x3, [x1, #0x13]
    // 0xd5c014: LoadField: r4 = r2->field_13
    //     0xd5c014: ldur            x4, [x2, #0x13]
    // 0xd5c018: cmp             x3, x4
    // 0xd5c01c: b.ne            #0xd5c048
    // 0xd5c020: LoadField: r3 = r1->field_1b
    //     0xd5c020: ldur            w3, [x1, #0x1b]
    // 0xd5c024: DecompressPointer r3
    //     0xd5c024: add             x3, x3, HEAP, lsl #32
    // 0xd5c028: LoadField: r1 = r2->field_1b
    //     0xd5c028: ldur            w1, [x2, #0x1b]
    // 0xd5c02c: DecompressPointer r1
    //     0xd5c02c: add             x1, x1, HEAP, lsl #32
    // 0xd5c030: cmp             w3, w1
    // 0xd5c034: r16 = true
    //     0xd5c034: add             x16, NULL, #0x20  ; true
    // 0xd5c038: r17 = false
    //     0xd5c038: add             x17, NULL, #0x30  ; false
    // 0xd5c03c: csel            x2, x16, x17, eq
    // 0xd5c040: mov             x0, x2
    // 0xd5c044: b               #0xd5c04c
    // 0xd5c048: r0 = false
    //     0xd5c048: add             x0, NULL, #0x30  ; false
    // 0xd5c04c: LeaveFrame
    //     0xd5c04c: mov             SP, fp
    //     0xd5c050: ldp             fp, lr, [SP], #0x10
    // 0xd5c054: ret
    //     0xd5c054: ret             
    // 0xd5c058: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd5c058: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd5c05c: b               #0xd5bf5c
  }
}

// class id: 4186, size: 0x28, field offset: 0x14
class _ReorderableItemState extends State<dynamic> {

  late SliverReorderableListState _listState; // offset: 0x14

  _ deactivate(/* No info */) {
    // ** addr: 0x92b8f4, size: 0x6c
    // 0x92b8f4: EnterFrame
    //     0x92b8f4: stp             fp, lr, [SP, #-0x10]!
    //     0x92b8f8: mov             fp, SP
    // 0x92b8fc: mov             x3, x1
    // 0x92b900: CheckStackOverflow
    //     0x92b900: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92b904: cmp             SP, x16
    //     0x92b908: b.ls            #0x92b948
    // 0x92b90c: LoadField: r1 = r3->field_13
    //     0x92b90c: ldur            w1, [x3, #0x13]
    // 0x92b910: DecompressPointer r1
    //     0x92b910: add             x1, x1, HEAP, lsl #32
    // 0x92b914: r16 = Sentinel
    //     0x92b914: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x92b918: cmp             w1, w16
    // 0x92b91c: b.eq            #0x92b950
    // 0x92b920: LoadField: r0 = r3->field_b
    //     0x92b920: ldur            w0, [x3, #0xb]
    // 0x92b924: DecompressPointer r0
    //     0x92b924: add             x0, x0, HEAP, lsl #32
    // 0x92b928: cmp             w0, NULL
    // 0x92b92c: b.eq            #0x92b95c
    // 0x92b930: LoadField: r2 = r0->field_b
    //     0x92b930: ldur            x2, [x0, #0xb]
    // 0x92b934: r0 = _unregisterItem()
    //     0x92b934: bl              #0x92b9a8  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_unregisterItem
    // 0x92b938: r0 = Null
    //     0x92b938: mov             x0, NULL
    // 0x92b93c: LeaveFrame
    //     0x92b93c: mov             SP, fp
    //     0x92b940: ldp             fp, lr, [SP], #0x10
    // 0x92b944: ret
    //     0x92b944: ret             
    // 0x92b948: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92b948: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92b94c: b               #0x92b90c
    // 0x92b950: r9 = _listState
    //     0x92b950: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fe00] Field <_ReorderableItemState@318218688._listState@318218688>: late (offset: 0x14)
    //     0x92b954: ldr             x9, [x9, #0xe00]
    // 0x92b958: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x92b958: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x92b95c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92b95c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x945044, size: 0x78
    // 0x945044: EnterFrame
    //     0x945044: stp             fp, lr, [SP, #-0x10]!
    //     0x945048: mov             fp, SP
    // 0x94504c: AllocStack(0x8)
    //     0x94504c: sub             SP, SP, #8
    // 0x945050: SetupParameters(_ReorderableItemState this /* r1 => r2, fp-0x8 */)
    //     0x945050: mov             x2, x1
    //     0x945054: stur            x1, [fp, #-8]
    // 0x945058: CheckStackOverflow
    //     0x945058: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94505c: cmp             SP, x16
    //     0x945060: b.ls            #0x9450b0
    // 0x945064: LoadField: r1 = r2->field_f
    //     0x945064: ldur            w1, [x2, #0xf]
    // 0x945068: DecompressPointer r1
    //     0x945068: add             x1, x1, HEAP, lsl #32
    // 0x94506c: cmp             w1, NULL
    // 0x945070: b.eq            #0x9450b8
    // 0x945074: r0 = of()
    //     0x945074: bl              #0x945b10  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableList::of
    // 0x945078: mov             x1, x0
    // 0x94507c: ldur            x2, [fp, #-8]
    // 0x945080: StoreField: r2->field_13 = r0
    //     0x945080: stur            w0, [x2, #0x13]
    //     0x945084: ldurb           w16, [x2, #-1]
    //     0x945088: ldurb           w17, [x0, #-1]
    //     0x94508c: and             x16, x17, x16, lsr #2
    //     0x945090: tst             x16, HEAP, lsr #32
    //     0x945094: b.eq            #0x94509c
    //     0x945098: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x94509c: r0 = _registerItem()
    //     0x94509c: bl              #0x9450bc  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_registerItem
    // 0x9450a0: r0 = Null
    //     0x9450a0: mov             x0, NULL
    // 0x9450a4: LeaveFrame
    //     0x9450a4: mov             SP, fp
    //     0x9450a8: ldp             fp, lr, [SP], #0x10
    // 0x9450ac: ret
    //     0x9450ac: ret             
    // 0x9450b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9450b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9450b4: b               #0x945064
    // 0x9450b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9450b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ rebuild(/* No info */) {
    // ** addr: 0x945384, size: 0x64
    // 0x945384: EnterFrame
    //     0x945384: stp             fp, lr, [SP, #-0x10]!
    //     0x945388: mov             fp, SP
    // 0x94538c: AllocStack(0x8)
    //     0x94538c: sub             SP, SP, #8
    // 0x945390: SetupParameters(_ReorderableItemState this /* r1 => r0, fp-0x8 */)
    //     0x945390: mov             x0, x1
    //     0x945394: stur            x1, [fp, #-8]
    // 0x945398: CheckStackOverflow
    //     0x945398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94539c: cmp             SP, x16
    //     0x9453a0: b.ls            #0x9453e0
    // 0x9453a4: LoadField: r1 = r0->field_f
    //     0x9453a4: ldur            w1, [x0, #0xf]
    // 0x9453a8: DecompressPointer r1
    //     0x9453a8: add             x1, x1, HEAP, lsl #32
    // 0x9453ac: cmp             w1, NULL
    // 0x9453b0: b.eq            #0x9453d0
    // 0x9453b4: r1 = Function '<anonymous closure>':.
    //     0x9453b4: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fe30] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x9453b8: ldr             x1, [x1, #0xe30]
    // 0x9453bc: r2 = Null
    //     0x9453bc: mov             x2, NULL
    // 0x9453c0: r0 = AllocateClosure()
    //     0x9453c0: bl              #0xec1630  ; AllocateClosureStub
    // 0x9453c4: ldur            x1, [fp, #-8]
    // 0x9453c8: mov             x2, x0
    // 0x9453cc: r0 = setState()
    //     0x9453cc: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9453d0: r0 = Null
    //     0x9453d0: mov             x0, NULL
    // 0x9453d4: LeaveFrame
    //     0x9453d4: mov             SP, fp
    //     0x9453d8: ldp             fp, lr, [SP], #0x10
    // 0x9453dc: ret
    //     0x9453dc: ret             
    // 0x9453e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9453e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9453e4: b               #0x9453a4
  }
  [closure] void rebuild(dynamic) {
    // ** addr: 0x9453e8, size: 0x38
    // 0x9453e8: EnterFrame
    //     0x9453e8: stp             fp, lr, [SP, #-0x10]!
    //     0x9453ec: mov             fp, SP
    // 0x9453f0: ldr             x0, [fp, #0x10]
    // 0x9453f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9453f4: ldur            w1, [x0, #0x17]
    // 0x9453f8: DecompressPointer r1
    //     0x9453f8: add             x1, x1, HEAP, lsl #32
    // 0x9453fc: CheckStackOverflow
    //     0x9453fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x945400: cmp             SP, x16
    //     0x945404: b.ls            #0x945418
    // 0x945408: r0 = rebuild()
    //     0x945408: bl              #0x945384  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::rebuild
    // 0x94540c: LeaveFrame
    //     0x94540c: mov             SP, fp
    //     0x945410: ldp             fp, lr, [SP], #0x10
    // 0x945414: ret
    //     0x945414: ret             
    // 0x945418: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945418: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94541c: b               #0x945408
  }
  set _ dragging=(/* No info */) {
    // ** addr: 0x945420, size: 0x7c
    // 0x945420: EnterFrame
    //     0x945420: stp             fp, lr, [SP, #-0x10]!
    //     0x945424: mov             fp, SP
    // 0x945428: AllocStack(0x8)
    //     0x945428: sub             SP, SP, #8
    // 0x94542c: SetupParameters(_ReorderableItemState this /* r1 => r1, fp-0x8 */)
    //     0x94542c: stur            x1, [fp, #-8]
    // 0x945430: CheckStackOverflow
    //     0x945430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x945434: cmp             SP, x16
    //     0x945438: b.ls            #0x945494
    // 0x94543c: r1 = 2
    //     0x94543c: movz            x1, #0x2
    // 0x945440: r0 = AllocateContext()
    //     0x945440: bl              #0xec126c  ; AllocateContextStub
    // 0x945444: mov             x1, x0
    // 0x945448: ldur            x0, [fp, #-8]
    // 0x94544c: StoreField: r1->field_f = r0
    //     0x94544c: stur            w0, [x1, #0xf]
    // 0x945450: r2 = true
    //     0x945450: add             x2, NULL, #0x20  ; true
    // 0x945454: StoreField: r1->field_13 = r2
    //     0x945454: stur            w2, [x1, #0x13]
    // 0x945458: LoadField: r2 = r0->field_f
    //     0x945458: ldur            w2, [x0, #0xf]
    // 0x94545c: DecompressPointer r2
    //     0x94545c: add             x2, x2, HEAP, lsl #32
    // 0x945460: cmp             w2, NULL
    // 0x945464: b.eq            #0x945484
    // 0x945468: mov             x2, x1
    // 0x94546c: r1 = Function '<anonymous closure>':.
    //     0x94546c: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fe38] AnonymousClosure: (0x94549c), in [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::dragging= (0x945420)
    //     0x945470: ldr             x1, [x1, #0xe38]
    // 0x945474: r0 = AllocateClosure()
    //     0x945474: bl              #0xec1630  ; AllocateClosureStub
    // 0x945478: ldur            x1, [fp, #-8]
    // 0x94547c: mov             x2, x0
    // 0x945480: r0 = setState()
    //     0x945480: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x945484: r0 = Null
    //     0x945484: mov             x0, NULL
    // 0x945488: LeaveFrame
    //     0x945488: mov             SP, fp
    //     0x94548c: ldp             fp, lr, [SP], #0x10
    // 0x945490: ret
    //     0x945490: ret             
    // 0x945494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945494: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x945498: b               #0x94543c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x94549c, size: 0x28
    // 0x94549c: ldr             x1, [SP]
    // 0x9454a0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9454a0: ldur            w2, [x1, #0x17]
    // 0x9454a4: DecompressPointer r2
    //     0x9454a4: add             x2, x2, HEAP, lsl #32
    // 0x9454a8: LoadField: r1 = r2->field_f
    //     0x9454a8: ldur            w1, [x2, #0xf]
    // 0x9454ac: DecompressPointer r1
    //     0x9454ac: add             x1, x1, HEAP, lsl #32
    // 0x9454b0: LoadField: r3 = r2->field_13
    //     0x9454b0: ldur            w3, [x2, #0x13]
    // 0x9454b4: DecompressPointer r3
    //     0x9454b4: add             x3, x3, HEAP, lsl #32
    // 0x9454b8: StoreField: r1->field_23 = r3
    //     0x9454b8: stur            w3, [x1, #0x23]
    // 0x9454bc: r0 = Null
    //     0x9454bc: mov             x0, NULL
    // 0x9454c0: ret
    //     0x9454c0: ret             
  }
  _ updateForGap(/* No info */) {
    // ** addr: 0x9454c4, size: 0x424
    // 0x9454c4: EnterFrame
    //     0x9454c4: stp             fp, lr, [SP, #-0x10]!
    //     0x9454c8: mov             fp, SP
    // 0x9454cc: AllocStack(0x48)
    //     0x9454cc: sub             SP, SP, #0x48
    // 0x9454d0: SetupParameters(_ReorderableItemState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */, dynamic _ /* d0 => d0, fp-0x38 */)
    //     0x9454d0: mov             x0, x1
    //     0x9454d4: stur            x1, [fp, #-8]
    //     0x9454d8: stur            x2, [fp, #-0x10]
    //     0x9454dc: stur            x3, [fp, #-0x18]
    //     0x9454e0: stur            x5, [fp, #-0x20]
    //     0x9454e4: stur            x6, [fp, #-0x28]
    //     0x9454e8: stur            d0, [fp, #-0x38]
    // 0x9454ec: CheckStackOverflow
    //     0x9454ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9454f0: cmp             SP, x16
    //     0x9454f4: b.ls            #0x945890
    // 0x9454f8: r1 = 1
    //     0x9454f8: movz            x1, #0x1
    // 0x9454fc: r0 = AllocateContext()
    //     0x9454fc: bl              #0xec126c  ; AllocateContextStub
    // 0x945500: ldur            x2, [fp, #-8]
    // 0x945504: stur            x0, [fp, #-0x30]
    // 0x945508: StoreField: r0->field_f = r2
    //     0x945508: stur            w2, [x0, #0xf]
    // 0x94550c: ldur            x3, [fp, #-0x10]
    // 0x945510: ldur            x1, [fp, #-0x18]
    // 0x945514: cmp             x1, x3
    // 0x945518: b.ge            #0x945600
    // 0x94551c: LoadField: r4 = r2->field_b
    //     0x94551c: ldur            w4, [x2, #0xb]
    // 0x945520: DecompressPointer r4
    //     0x945520: add             x4, x4, HEAP, lsl #32
    // 0x945524: cmp             w4, NULL
    // 0x945528: b.eq            #0x945898
    // 0x94552c: LoadField: r5 = r4->field_b
    //     0x94552c: ldur            x5, [x4, #0xb]
    // 0x945530: cmp             x5, x3
    // 0x945534: b.ge            #0x9455f4
    // 0x945538: cmp             x5, x1
    // 0x94553c: b.lt            #0x9455e8
    // 0x945540: ldur            x4, [fp, #-0x28]
    // 0x945544: tbnz            w4, #4, #0x945558
    // 0x945548: ldur            d0, [fp, #-0x38]
    // 0x94554c: fneg            d1, d0
    // 0x945550: mov             v0.16b, v1.16b
    // 0x945554: b               #0x94555c
    // 0x945558: ldur            d0, [fp, #-0x38]
    // 0x94555c: LoadField: r1 = r2->field_13
    //     0x94555c: ldur            w1, [x2, #0x13]
    // 0x945560: DecompressPointer r1
    //     0x945560: add             x1, x1, HEAP, lsl #32
    // 0x945564: r16 = Sentinel
    //     0x945564: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x945568: cmp             w1, w16
    // 0x94556c: b.eq            #0x94589c
    // 0x945570: LoadField: r3 = r1->field_3f
    //     0x945570: ldur            w3, [x1, #0x3f]
    // 0x945574: DecompressPointer r3
    //     0x945574: add             x3, x3, HEAP, lsl #32
    // 0x945578: r16 = Sentinel
    //     0x945578: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x94557c: cmp             w3, w16
    // 0x945580: b.eq            #0x9458a8
    // 0x945584: LoadField: r1 = r3->field_b
    //     0x945584: ldur            w1, [x3, #0xb]
    // 0x945588: DecompressPointer r1
    //     0x945588: add             x1, x1, HEAP, lsl #32
    // 0x94558c: cmp             w1, NULL
    // 0x945590: b.eq            #0x9458b4
    // 0x945594: LoadField: r3 = r1->field_b
    //     0x945594: ldur            w3, [x1, #0xb]
    // 0x945598: DecompressPointer r3
    //     0x945598: add             x3, x3, HEAP, lsl #32
    // 0x94559c: r16 = Instance_AxisDirection
    //     0x94559c: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0x9455a0: cmp             w3, w16
    // 0x9455a4: b.eq            #0x9455b4
    // 0x9455a8: r16 = Instance_AxisDirection
    //     0x9455a8: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0x9455ac: cmp             w3, w16
    // 0x9455b0: b.ne            #0x9455bc
    // 0x9455b4: r1 = Instance_Axis
    //     0x9455b4: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x9455b8: b               #0x9455e0
    // 0x9455bc: r16 = Instance_AxisDirection
    //     0x9455bc: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0x9455c0: cmp             w3, w16
    // 0x9455c4: b.eq            #0x9455d4
    // 0x9455c8: r16 = Instance_AxisDirection
    //     0x9455c8: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0x9455cc: cmp             w3, w16
    // 0x9455d0: b.ne            #0x9455dc
    // 0x9455d4: r1 = Instance_Axis
    //     0x9455d4: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x9455d8: b               #0x9455e0
    // 0x9455dc: r1 = Null
    //     0x9455dc: mov             x1, NULL
    // 0x9455e0: r0 = _extentOffset()
    //     0x9455e0: bl              #0x945a1c  ; [package:flutter/src/widgets/reorderable_list.dart] ::_extentOffset
    // 0x9455e4: b               #0x9456d4
    // 0x9455e8: ldur            d0, [fp, #-0x38]
    // 0x9455ec: ldur            x4, [fp, #-0x28]
    // 0x9455f0: b               #0x945608
    // 0x9455f4: ldur            d0, [fp, #-0x38]
    // 0x9455f8: ldur            x4, [fp, #-0x28]
    // 0x9455fc: b               #0x945608
    // 0x945600: ldur            d0, [fp, #-0x38]
    // 0x945604: ldur            x4, [fp, #-0x28]
    // 0x945608: cmp             x1, x3
    // 0x94560c: b.le            #0x9456d0
    // 0x945610: ldur            x2, [fp, #-8]
    // 0x945614: LoadField: r0 = r2->field_b
    //     0x945614: ldur            w0, [x2, #0xb]
    // 0x945618: DecompressPointer r0
    //     0x945618: add             x0, x0, HEAP, lsl #32
    // 0x94561c: cmp             w0, NULL
    // 0x945620: b.eq            #0x9458b8
    // 0x945624: LoadField: r5 = r0->field_b
    //     0x945624: ldur            x5, [x0, #0xb]
    // 0x945628: cmp             x5, x3
    // 0x94562c: b.le            #0x9456d0
    // 0x945630: cmp             x5, x1
    // 0x945634: b.ge            #0x9456d0
    // 0x945638: tbz             w4, #4, #0x945644
    // 0x94563c: fneg            d1, d0
    // 0x945640: mov             v0.16b, v1.16b
    // 0x945644: LoadField: r0 = r2->field_13
    //     0x945644: ldur            w0, [x2, #0x13]
    // 0x945648: DecompressPointer r0
    //     0x945648: add             x0, x0, HEAP, lsl #32
    // 0x94564c: r16 = Sentinel
    //     0x94564c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x945650: cmp             w0, w16
    // 0x945654: b.eq            #0x9458bc
    // 0x945658: LoadField: r1 = r0->field_3f
    //     0x945658: ldur            w1, [x0, #0x3f]
    // 0x94565c: DecompressPointer r1
    //     0x94565c: add             x1, x1, HEAP, lsl #32
    // 0x945660: r16 = Sentinel
    //     0x945660: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x945664: cmp             w1, w16
    // 0x945668: b.eq            #0x9458c8
    // 0x94566c: LoadField: r0 = r1->field_b
    //     0x94566c: ldur            w0, [x1, #0xb]
    // 0x945670: DecompressPointer r0
    //     0x945670: add             x0, x0, HEAP, lsl #32
    // 0x945674: cmp             w0, NULL
    // 0x945678: b.eq            #0x9458d4
    // 0x94567c: LoadField: r1 = r0->field_b
    //     0x94567c: ldur            w1, [x0, #0xb]
    // 0x945680: DecompressPointer r1
    //     0x945680: add             x1, x1, HEAP, lsl #32
    // 0x945684: r16 = Instance_AxisDirection
    //     0x945684: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0x945688: cmp             w1, w16
    // 0x94568c: b.eq            #0x94569c
    // 0x945690: r16 = Instance_AxisDirection
    //     0x945690: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0x945694: cmp             w1, w16
    // 0x945698: b.ne            #0x9456a4
    // 0x94569c: r1 = Instance_Axis
    //     0x94569c: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x9456a0: b               #0x9456c8
    // 0x9456a4: r16 = Instance_AxisDirection
    //     0x9456a4: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0x9456a8: cmp             w1, w16
    // 0x9456ac: b.eq            #0x9456bc
    // 0x9456b0: r16 = Instance_AxisDirection
    //     0x9456b0: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0x9456b4: cmp             w1, w16
    // 0x9456b8: b.ne            #0x9456c4
    // 0x9456bc: r1 = Instance_Axis
    //     0x9456bc: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x9456c0: b               #0x9456c8
    // 0x9456c4: r1 = Null
    //     0x9456c4: mov             x1, NULL
    // 0x9456c8: r0 = _extentOffset()
    //     0x9456c8: bl              #0x945a1c  ; [package:flutter/src/widgets/reorderable_list.dart] ::_extentOffset
    // 0x9456cc: b               #0x9456d4
    // 0x9456d0: r0 = Instance_Offset
    //     0x9456d0: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x9456d4: ldur            x2, [fp, #-8]
    // 0x9456d8: stur            x0, [fp, #-0x28]
    // 0x9456dc: LoadField: r1 = r2->field_1b
    //     0x9456dc: ldur            w1, [x2, #0x1b]
    // 0x9456e0: DecompressPointer r1
    //     0x9456e0: add             x1, x1, HEAP, lsl #32
    // 0x9456e4: stp             x1, x0, [SP]
    // 0x9456e8: r0 = ==()
    //     0x9456e8: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0x9456ec: tbz             w0, #4, #0x945880
    // 0x9456f0: ldur            x2, [fp, #-8]
    // 0x9456f4: ldur            x1, [fp, #-0x20]
    // 0x9456f8: ldur            x0, [fp, #-0x28]
    // 0x9456fc: StoreField: r2->field_1b = r0
    //     0x9456fc: stur            w0, [x2, #0x1b]
    //     0x945700: ldurb           w16, [x2, #-1]
    //     0x945704: ldurb           w17, [x0, #-1]
    //     0x945708: and             x16, x17, x16, lsr #2
    //     0x94570c: tst             x16, HEAP, lsr #32
    //     0x945710: b.eq            #0x945718
    //     0x945714: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x945718: tbnz            w1, #4, #0x945830
    // 0x94571c: LoadField: r0 = r2->field_1f
    //     0x94571c: ldur            w0, [x2, #0x1f]
    // 0x945720: DecompressPointer r0
    //     0x945720: add             x0, x0, HEAP, lsl #32
    // 0x945724: cmp             w0, NULL
    // 0x945728: b.ne            #0x9457dc
    // 0x94572c: LoadField: r0 = r2->field_13
    //     0x94572c: ldur            w0, [x2, #0x13]
    // 0x945730: DecompressPointer r0
    //     0x945730: add             x0, x0, HEAP, lsl #32
    // 0x945734: r16 = Sentinel
    //     0x945734: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x945738: cmp             w0, w16
    // 0x94573c: b.eq            #0x9458d8
    // 0x945740: stur            x0, [fp, #-0x20]
    // 0x945744: r1 = <double>
    //     0x945744: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x945748: r0 = AnimationController()
    //     0x945748: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x94574c: stur            x0, [fp, #-0x28]
    // 0x945750: r16 = Instance_Duration
    //     0x945750: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d90] Obj!Duration@e3a0e1
    //     0x945754: ldr             x16, [x16, #0xd90]
    // 0x945758: str             x16, [SP]
    // 0x94575c: mov             x1, x0
    // 0x945760: ldur            x2, [fp, #-0x20]
    // 0x945764: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x945764: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x945768: ldr             x4, [x4, #0x408]
    // 0x94576c: r0 = AnimationController()
    //     0x94576c: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x945770: ldur            x2, [fp, #-8]
    // 0x945774: r1 = Function 'rebuild':.
    //     0x945774: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fdf0] AnonymousClosure: (0x9453e8), in [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::rebuild (0x945384)
    //     0x945778: ldr             x1, [x1, #0xdf0]
    // 0x94577c: r0 = AllocateClosure()
    //     0x94577c: bl              #0xec1630  ; AllocateClosureStub
    // 0x945780: ldur            x1, [fp, #-0x28]
    // 0x945784: mov             x2, x0
    // 0x945788: r0 = addActionListener()
    //     0x945788: bl              #0xc680d4  ; [package:flutter/src/widgets/actions.dart] Action::addActionListener
    // 0x94578c: ldur            x2, [fp, #-0x30]
    // 0x945790: r1 = Function '<anonymous closure>':.
    //     0x945790: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fdf8] AnonymousClosure: (0x945a68), in [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::updateForGap (0x9454c4)
    //     0x945794: ldr             x1, [x1, #0xdf8]
    // 0x945798: r0 = AllocateClosure()
    //     0x945798: bl              #0xec1630  ; AllocateClosureStub
    // 0x94579c: ldur            x1, [fp, #-0x28]
    // 0x9457a0: mov             x2, x0
    // 0x9457a4: r0 = addStatusListener()
    //     0x9457a4: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0x9457a8: ldur            x1, [fp, #-0x28]
    // 0x9457ac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9457ac: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9457b0: r0 = forward()
    //     0x9457b0: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x9457b4: ldur            x0, [fp, #-0x28]
    // 0x9457b8: ldur            x2, [fp, #-8]
    // 0x9457bc: StoreField: r2->field_1f = r0
    //     0x9457bc: stur            w0, [x2, #0x1f]
    //     0x9457c0: ldurb           w16, [x2, #-1]
    //     0x9457c4: ldurb           w17, [x0, #-1]
    //     0x9457c8: and             x16, x17, x16, lsr #2
    //     0x9457cc: tst             x16, HEAP, lsr #32
    //     0x9457d0: b.eq            #0x9457d8
    //     0x9457d4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9457d8: b               #0x945828
    // 0x9457dc: mov             x1, x2
    // 0x9457e0: r0 = offset()
    //     0x9457e0: bl              #0x94597c  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::offset
    // 0x9457e4: ldur            x2, [fp, #-8]
    // 0x9457e8: ArrayStore: r2[0] = r0  ; List_4
    //     0x9457e8: stur            w0, [x2, #0x17]
    //     0x9457ec: ldurb           w16, [x2, #-1]
    //     0x9457f0: ldurb           w17, [x0, #-1]
    //     0x9457f4: and             x16, x17, x16, lsr #2
    //     0x9457f8: tst             x16, HEAP, lsr #32
    //     0x9457fc: b.eq            #0x945804
    //     0x945800: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x945804: LoadField: r1 = r2->field_1f
    //     0x945804: ldur            w1, [x2, #0x1f]
    // 0x945808: DecompressPointer r1
    //     0x945808: add             x1, x1, HEAP, lsl #32
    // 0x94580c: cmp             w1, NULL
    // 0x945810: b.eq            #0x9458e4
    // 0x945814: r16 = 0.000000
    //     0x945814: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x945818: str             x16, [SP]
    // 0x94581c: r4 = const [0, 0x2, 0x1, 0x1, from, 0x1, null]
    //     0x94581c: add             x4, PP, #0x3a, lsl #12  ; [pp+0x3a278] List(7) [0, 0x2, 0x1, 0x1, "from", 0x1, Null]
    //     0x945820: ldr             x4, [x4, #0x278]
    // 0x945824: r0 = forward()
    //     0x945824: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x945828: ldur            x1, [fp, #-8]
    // 0x94582c: b               #0x94587c
    // 0x945830: mov             x0, x2
    // 0x945834: LoadField: r1 = r0->field_1f
    //     0x945834: ldur            w1, [x0, #0x1f]
    // 0x945838: DecompressPointer r1
    //     0x945838: add             x1, x1, HEAP, lsl #32
    // 0x94583c: cmp             w1, NULL
    // 0x945840: b.eq            #0x945854
    // 0x945844: r0 = dispose()
    //     0x945844: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0x945848: ldur            x1, [fp, #-8]
    // 0x94584c: StoreField: r1->field_1f = rNULL
    //     0x94584c: stur            NULL, [x1, #0x1f]
    // 0x945850: b               #0x945858
    // 0x945854: mov             x1, x0
    // 0x945858: LoadField: r0 = r1->field_1b
    //     0x945858: ldur            w0, [x1, #0x1b]
    // 0x94585c: DecompressPointer r0
    //     0x94585c: add             x0, x0, HEAP, lsl #32
    // 0x945860: ArrayStore: r1[0] = r0  ; List_4
    //     0x945860: stur            w0, [x1, #0x17]
    //     0x945864: ldurb           w16, [x1, #-1]
    //     0x945868: ldurb           w17, [x0, #-1]
    //     0x94586c: and             x16, x17, x16, lsr #2
    //     0x945870: tst             x16, HEAP, lsr #32
    //     0x945874: b.eq            #0x94587c
    //     0x945878: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x94587c: r0 = rebuild()
    //     0x94587c: bl              #0x945384  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::rebuild
    // 0x945880: r0 = Null
    //     0x945880: mov             x0, NULL
    // 0x945884: LeaveFrame
    //     0x945884: mov             SP, fp
    //     0x945888: ldp             fp, lr, [SP], #0x10
    // 0x94588c: ret
    //     0x94588c: ret             
    // 0x945890: r0 = StackOverflowSharedWithFPURegs()
    //     0x945890: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x945894: b               #0x9454f8
    // 0x945898: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945898: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94589c: r9 = _listState
    //     0x94589c: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fe00] Field <_ReorderableItemState@318218688._listState@318218688>: late (offset: 0x14)
    //     0x9458a0: ldr             x9, [x9, #0xe00]
    // 0x9458a4: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x9458a4: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x9458a8: r9 = _scrollable
    //     0x9458a8: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0x9458ac: ldr             x9, [x9, #0xd10]
    // 0x9458b0: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x9458b0: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x9458b4: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9458b4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9458b8: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9458b8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9458bc: r9 = _listState
    //     0x9458bc: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fe00] Field <_ReorderableItemState@318218688._listState@318218688>: late (offset: 0x14)
    //     0x9458c0: ldr             x9, [x9, #0xe00]
    // 0x9458c4: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x9458c4: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x9458c8: r9 = _scrollable
    //     0x9458c8: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0x9458cc: ldr             x9, [x9, #0xd10]
    // 0x9458d0: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x9458d0: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x9458d4: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9458d4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9458d8: r9 = _listState
    //     0x9458d8: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fe00] Field <_ReorderableItemState@318218688._listState@318218688>: late (offset: 0x14)
    //     0x9458dc: ldr             x9, [x9, #0xe00]
    // 0x9458e0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9458e0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9458e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9458e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ offset(/* No info */) {
    // ** addr: 0x94597c, size: 0xa0
    // 0x94597c: EnterFrame
    //     0x94597c: stp             fp, lr, [SP, #-0x10]!
    //     0x945980: mov             fp, SP
    // 0x945984: AllocStack(0x8)
    //     0x945984: sub             SP, SP, #8
    // 0x945988: SetupParameters(_ReorderableItemState this /* r1 => r0, fp-0x8 */)
    //     0x945988: mov             x0, x1
    //     0x94598c: stur            x1, [fp, #-8]
    // 0x945990: CheckStackOverflow
    //     0x945990: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x945994: cmp             SP, x16
    //     0x945998: b.ls            #0x945a0c
    // 0x94599c: LoadField: r1 = r0->field_1f
    //     0x94599c: ldur            w1, [x0, #0x1f]
    // 0x9459a0: DecompressPointer r1
    //     0x9459a0: add             x1, x1, HEAP, lsl #32
    // 0x9459a4: cmp             w1, NULL
    // 0x9459a8: b.eq            #0x9459f4
    // 0x9459ac: LoadField: r2 = r1->field_37
    //     0x9459ac: ldur            w2, [x1, #0x37]
    // 0x9459b0: DecompressPointer r2
    //     0x9459b0: add             x2, x2, HEAP, lsl #32
    // 0x9459b4: r16 = Sentinel
    //     0x9459b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9459b8: cmp             w2, w16
    // 0x9459bc: b.eq            #0x945a14
    // 0x9459c0: LoadField: d0 = r2->field_7
    //     0x9459c0: ldur            d0, [x2, #7]
    // 0x9459c4: r1 = Instance_Cubic
    //     0x9459c4: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb00] Obj!Cubic@e14e01
    //     0x9459c8: ldr             x1, [x1, #0xb00]
    // 0x9459cc: r0 = transform()
    //     0x9459cc: bl              #0xcd6c40  ; [package:flutter/src/animation/curves.dart] Curve::transform
    // 0x9459d0: ldur            x0, [fp, #-8]
    // 0x9459d4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9459d4: ldur            w1, [x0, #0x17]
    // 0x9459d8: DecompressPointer r1
    //     0x9459d8: add             x1, x1, HEAP, lsl #32
    // 0x9459dc: LoadField: r2 = r0->field_1b
    //     0x9459dc: ldur            w2, [x0, #0x1b]
    // 0x9459e0: DecompressPointer r2
    //     0x9459e0: add             x2, x2, HEAP, lsl #32
    // 0x9459e4: r0 = lerp()
    //     0x9459e4: bl              #0x7f5018  ; [dart:ui] Offset::lerp
    // 0x9459e8: LeaveFrame
    //     0x9459e8: mov             SP, fp
    //     0x9459ec: ldp             fp, lr, [SP], #0x10
    // 0x9459f0: ret
    //     0x9459f0: ret             
    // 0x9459f4: LoadField: r1 = r0->field_1b
    //     0x9459f4: ldur            w1, [x0, #0x1b]
    // 0x9459f8: DecompressPointer r1
    //     0x9459f8: add             x1, x1, HEAP, lsl #32
    // 0x9459fc: mov             x0, x1
    // 0x945a00: LeaveFrame
    //     0x945a00: mov             SP, fp
    //     0x945a04: ldp             fp, lr, [SP], #0x10
    // 0x945a08: ret
    //     0x945a08: ret             
    // 0x945a0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945a0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x945a10: b               #0x94599c
    // 0x945a14: r9 = _value
    //     0x945a14: ldr             x9, [PP, #0x4ea8]  ; [pp+0x4ea8] Field <AnimationController._value@441066280>: late (offset: 0x38)
    // 0x945a18: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x945a18: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, AnimationStatus) {
    // ** addr: 0x945a68, size: 0xa8
    // 0x945a68: EnterFrame
    //     0x945a68: stp             fp, lr, [SP, #-0x10]!
    //     0x945a6c: mov             fp, SP
    // 0x945a70: AllocStack(0x8)
    //     0x945a70: sub             SP, SP, #8
    // 0x945a74: SetupParameters()
    //     0x945a74: ldr             x0, [fp, #0x18]
    //     0x945a78: ldur            w2, [x0, #0x17]
    //     0x945a7c: add             x2, x2, HEAP, lsl #32
    //     0x945a80: stur            x2, [fp, #-8]
    // 0x945a84: CheckStackOverflow
    //     0x945a84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x945a88: cmp             SP, x16
    //     0x945a8c: b.ls            #0x945b04
    // 0x945a90: ldr             x0, [fp, #0x10]
    // 0x945a94: r16 = Instance_AnimationStatus
    //     0x945a94: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0x945a98: cmp             w0, w16
    // 0x945a9c: b.ne            #0x945af4
    // 0x945aa0: LoadField: r1 = r2->field_f
    //     0x945aa0: ldur            w1, [x2, #0xf]
    // 0x945aa4: DecompressPointer r1
    //     0x945aa4: add             x1, x1, HEAP, lsl #32
    // 0x945aa8: LoadField: r0 = r1->field_1b
    //     0x945aa8: ldur            w0, [x1, #0x1b]
    // 0x945aac: DecompressPointer r0
    //     0x945aac: add             x0, x0, HEAP, lsl #32
    // 0x945ab0: ArrayStore: r1[0] = r0  ; List_4
    //     0x945ab0: stur            w0, [x1, #0x17]
    //     0x945ab4: ldurb           w16, [x1, #-1]
    //     0x945ab8: ldurb           w17, [x0, #-1]
    //     0x945abc: and             x16, x17, x16, lsr #2
    //     0x945ac0: tst             x16, HEAP, lsr #32
    //     0x945ac4: b.eq            #0x945acc
    //     0x945ac8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x945acc: LoadField: r0 = r1->field_1f
    //     0x945acc: ldur            w0, [x1, #0x1f]
    // 0x945ad0: DecompressPointer r0
    //     0x945ad0: add             x0, x0, HEAP, lsl #32
    // 0x945ad4: cmp             w0, NULL
    // 0x945ad8: b.eq            #0x945b0c
    // 0x945adc: mov             x1, x0
    // 0x945ae0: r0 = dispose()
    //     0x945ae0: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0x945ae4: ldur            x1, [fp, #-8]
    // 0x945ae8: LoadField: r2 = r1->field_f
    //     0x945ae8: ldur            w2, [x1, #0xf]
    // 0x945aec: DecompressPointer r2
    //     0x945aec: add             x2, x2, HEAP, lsl #32
    // 0x945af0: StoreField: r2->field_1f = rNULL
    //     0x945af0: stur            NULL, [x2, #0x1f]
    // 0x945af4: r0 = Null
    //     0x945af4: mov             x0, NULL
    // 0x945af8: LeaveFrame
    //     0x945af8: mov             SP, fp
    //     0x945afc: ldp             fp, lr, [SP], #0x10
    // 0x945b00: ret
    //     0x945b00: ret             
    // 0x945b04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945b04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x945b08: b               #0x945a90
    // 0x945b0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945b0c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ targetGeometry(/* No info */) {
    // ** addr: 0x997c24, size: 0xcc
    // 0x997c24: EnterFrame
    //     0x997c24: stp             fp, lr, [SP, #-0x10]!
    //     0x997c28: mov             fp, SP
    // 0x997c2c: AllocStack(0x10)
    //     0x997c2c: sub             SP, SP, #0x10
    // 0x997c30: SetupParameters(_ReorderableItemState this /* r1 => r0, fp-0x8 */)
    //     0x997c30: mov             x0, x1
    //     0x997c34: stur            x1, [fp, #-8]
    // 0x997c38: CheckStackOverflow
    //     0x997c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x997c3c: cmp             SP, x16
    //     0x997c40: b.ls            #0x997ce0
    // 0x997c44: LoadField: r1 = r0->field_f
    //     0x997c44: ldur            w1, [x0, #0xf]
    // 0x997c48: DecompressPointer r1
    //     0x997c48: add             x1, x1, HEAP, lsl #32
    // 0x997c4c: cmp             w1, NULL
    // 0x997c50: b.eq            #0x997ce8
    // 0x997c54: r0 = renderObject()
    //     0x997c54: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0x997c58: mov             x3, x0
    // 0x997c5c: stur            x3, [fp, #-0x10]
    // 0x997c60: cmp             w3, NULL
    // 0x997c64: b.eq            #0x997cec
    // 0x997c68: mov             x0, x3
    // 0x997c6c: r2 = Null
    //     0x997c6c: mov             x2, NULL
    // 0x997c70: r1 = Null
    //     0x997c70: mov             x1, NULL
    // 0x997c74: r4 = LoadClassIdInstr(r0)
    //     0x997c74: ldur            x4, [x0, #-1]
    //     0x997c78: ubfx            x4, x4, #0xc, #0x14
    // 0x997c7c: sub             x4, x4, #0xbba
    // 0x997c80: cmp             x4, #0x9a
    // 0x997c84: b.ls            #0x997c98
    // 0x997c88: r8 = RenderBox
    //     0x997c88: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x997c8c: r3 = Null
    //     0x997c8c: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fd88] Null
    //     0x997c90: ldr             x3, [x3, #0xd88]
    // 0x997c94: r0 = RenderBox()
    //     0x997c94: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x997c98: ldur            x1, [fp, #-0x10]
    // 0x997c9c: r2 = Instance_Offset
    //     0x997c9c: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x997ca0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x997ca0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x997ca4: r0 = localToGlobal()
    //     0x997ca4: bl              #0x67fb20  ; [package:flutter/src/rendering/box.dart] RenderBox::localToGlobal
    // 0x997ca8: mov             x1, x0
    // 0x997cac: ldur            x0, [fp, #-8]
    // 0x997cb0: LoadField: r2 = r0->field_1b
    //     0x997cb0: ldur            w2, [x0, #0x1b]
    // 0x997cb4: DecompressPointer r2
    //     0x997cb4: add             x2, x2, HEAP, lsl #32
    // 0x997cb8: r0 = +()
    //     0x997cb8: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x997cbc: ldur            x1, [fp, #-0x10]
    // 0x997cc0: stur            x0, [fp, #-8]
    // 0x997cc4: r0 = size()
    //     0x997cc4: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x997cc8: ldur            x1, [fp, #-8]
    // 0x997ccc: mov             x2, x0
    // 0x997cd0: r0 = &()
    //     0x997cd0: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x997cd4: LeaveFrame
    //     0x997cd4: mov             SP, fp
    //     0x997cd8: ldp             fp, lr, [SP], #0x10
    // 0x997cdc: ret
    //     0x997cdc: ret             
    // 0x997ce0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x997ce0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x997ce4: b               #0x997c44
    // 0x997ce8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997ce8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997cec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997cec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x997d14, size: 0x11c
    // 0x997d14: EnterFrame
    //     0x997d14: stp             fp, lr, [SP, #-0x10]!
    //     0x997d18: mov             fp, SP
    // 0x997d1c: AllocStack(0x10)
    //     0x997d1c: sub             SP, SP, #0x10
    // 0x997d20: SetupParameters(_ReorderableItemState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x997d20: mov             x4, x1
    //     0x997d24: mov             x3, x2
    //     0x997d28: stur            x1, [fp, #-8]
    //     0x997d2c: stur            x2, [fp, #-0x10]
    // 0x997d30: CheckStackOverflow
    //     0x997d30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x997d34: cmp             SP, x16
    //     0x997d38: b.ls            #0x997e18
    // 0x997d3c: mov             x0, x3
    // 0x997d40: r2 = Null
    //     0x997d40: mov             x2, NULL
    // 0x997d44: r1 = Null
    //     0x997d44: mov             x1, NULL
    // 0x997d48: r4 = 60
    //     0x997d48: movz            x4, #0x3c
    // 0x997d4c: branchIfSmi(r0, 0x997d58)
    //     0x997d4c: tbz             w0, #0, #0x997d58
    // 0x997d50: r4 = LoadClassIdInstr(r0)
    //     0x997d50: ldur            x4, [x0, #-1]
    //     0x997d54: ubfx            x4, x4, #0xc, #0x14
    // 0x997d58: r17 = 4763
    //     0x997d58: movz            x17, #0x129b
    // 0x997d5c: cmp             x4, x17
    // 0x997d60: b.eq            #0x997d78
    // 0x997d64: r8 = _ReorderableItem
    //     0x997d64: add             x8, PP, #0x5c, lsl #12  ; [pp+0x5c698] Type: _ReorderableItem
    //     0x997d68: ldr             x8, [x8, #0x698]
    // 0x997d6c: r3 = Null
    //     0x997d6c: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5c6a0] Null
    //     0x997d70: ldr             x3, [x3, #0x6a0]
    // 0x997d74: r0 = _ReorderableItem()
    //     0x997d74: bl              #0x92b984  ; IsType__ReorderableItem_Stub
    // 0x997d78: ldur            x3, [fp, #-8]
    // 0x997d7c: LoadField: r2 = r3->field_7
    //     0x997d7c: ldur            w2, [x3, #7]
    // 0x997d80: DecompressPointer r2
    //     0x997d80: add             x2, x2, HEAP, lsl #32
    // 0x997d84: ldur            x0, [fp, #-0x10]
    // 0x997d88: r1 = Null
    //     0x997d88: mov             x1, NULL
    // 0x997d8c: cmp             w2, NULL
    // 0x997d90: b.eq            #0x997db4
    // 0x997d94: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x997d94: ldur            w4, [x2, #0x17]
    // 0x997d98: DecompressPointer r4
    //     0x997d98: add             x4, x4, HEAP, lsl #32
    // 0x997d9c: r8 = X0 bound StatefulWidget
    //     0x997d9c: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x997da0: ldr             x8, [x8, #0x7f8]
    // 0x997da4: LoadField: r9 = r4->field_7
    //     0x997da4: ldur            x9, [x4, #7]
    // 0x997da8: r3 = Null
    //     0x997da8: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5c6b0] Null
    //     0x997dac: ldr             x3, [x3, #0x6b0]
    // 0x997db0: blr             x9
    // 0x997db4: ldur            x0, [fp, #-0x10]
    // 0x997db8: LoadField: r2 = r0->field_b
    //     0x997db8: ldur            x2, [x0, #0xb]
    // 0x997dbc: ldur            x0, [fp, #-8]
    // 0x997dc0: LoadField: r1 = r0->field_b
    //     0x997dc0: ldur            w1, [x0, #0xb]
    // 0x997dc4: DecompressPointer r1
    //     0x997dc4: add             x1, x1, HEAP, lsl #32
    // 0x997dc8: cmp             w1, NULL
    // 0x997dcc: b.eq            #0x997e20
    // 0x997dd0: LoadField: r3 = r1->field_b
    //     0x997dd0: ldur            x3, [x1, #0xb]
    // 0x997dd4: cmp             x2, x3
    // 0x997dd8: b.eq            #0x997e08
    // 0x997ddc: LoadField: r1 = r0->field_13
    //     0x997ddc: ldur            w1, [x0, #0x13]
    // 0x997de0: DecompressPointer r1
    //     0x997de0: add             x1, x1, HEAP, lsl #32
    // 0x997de4: r16 = Sentinel
    //     0x997de4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x997de8: cmp             w1, w16
    // 0x997dec: b.eq            #0x997e24
    // 0x997df0: mov             x3, x0
    // 0x997df4: r0 = _unregisterItem()
    //     0x997df4: bl              #0x92b9a8  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_unregisterItem
    // 0x997df8: ldur            x2, [fp, #-8]
    // 0x997dfc: LoadField: r1 = r2->field_13
    //     0x997dfc: ldur            w1, [x2, #0x13]
    // 0x997e00: DecompressPointer r1
    //     0x997e00: add             x1, x1, HEAP, lsl #32
    // 0x997e04: r0 = _registerItem()
    //     0x997e04: bl              #0x9450bc  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_registerItem
    // 0x997e08: r0 = Null
    //     0x997e08: mov             x0, NULL
    // 0x997e0c: LeaveFrame
    //     0x997e0c: mov             SP, fp
    //     0x997e10: ldp             fp, lr, [SP], #0x10
    // 0x997e14: ret
    //     0x997e14: ret             
    // 0x997e18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x997e18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x997e1c: b               #0x997d3c
    // 0x997e20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997e20: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997e24: r9 = _listState
    //     0x997e24: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fe00] Field <_ReorderableItemState@318218688._listState@318218688>: late (offset: 0x14)
    //     0x997e28: ldr             x9, [x9, #0xe00]
    // 0x997e2c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x997e2c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa1cc2c, size: 0x1e0
    // 0xa1cc2c: EnterFrame
    //     0xa1cc2c: stp             fp, lr, [SP, #-0x10]!
    //     0xa1cc30: mov             fp, SP
    // 0xa1cc34: AllocStack(0x18)
    //     0xa1cc34: sub             SP, SP, #0x18
    // 0xa1cc38: SetupParameters(_ReorderableItemState this /* r1 => r0, fp-0x18 */)
    //     0xa1cc38: mov             x0, x1
    //     0xa1cc3c: stur            x1, [fp, #-0x18]
    // 0xa1cc40: CheckStackOverflow
    //     0xa1cc40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1cc44: cmp             SP, x16
    //     0xa1cc48: b.ls            #0xa1cda4
    // 0xa1cc4c: LoadField: r1 = r0->field_23
    //     0xa1cc4c: ldur            w1, [x0, #0x23]
    // 0xa1cc50: DecompressPointer r1
    //     0xa1cc50: add             x1, x1, HEAP, lsl #32
    // 0xa1cc54: tbnz            w1, #4, #0xa1cd30
    // 0xa1cc58: LoadField: r1 = r0->field_13
    //     0xa1cc58: ldur            w1, [x0, #0x13]
    // 0xa1cc5c: DecompressPointer r1
    //     0xa1cc5c: add             x1, x1, HEAP, lsl #32
    // 0xa1cc60: r16 = Sentinel
    //     0xa1cc60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa1cc64: cmp             w1, w16
    // 0xa1cc68: b.eq            #0xa1cdac
    // 0xa1cc6c: LoadField: r0 = r1->field_27
    //     0xa1cc6c: ldur            w0, [x1, #0x27]
    // 0xa1cc70: DecompressPointer r0
    //     0xa1cc70: add             x0, x0, HEAP, lsl #32
    // 0xa1cc74: cmp             w0, NULL
    // 0xa1cc78: b.eq            #0xa1cdb8
    // 0xa1cc7c: LoadField: r2 = r0->field_3f
    //     0xa1cc7c: ldur            w2, [x0, #0x3f]
    // 0xa1cc80: DecompressPointer r2
    //     0xa1cc80: add             x2, x2, HEAP, lsl #32
    // 0xa1cc84: r16 = Sentinel
    //     0xa1cc84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa1cc88: cmp             w2, w16
    // 0xa1cc8c: b.eq            #0xa1cdbc
    // 0xa1cc90: stur            x2, [fp, #-8]
    // 0xa1cc94: r0 = _scrollDirection()
    //     0xa1cc94: bl              #0x9458e8  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_scrollDirection
    // 0xa1cc98: mov             x1, x0
    // 0xa1cc9c: ldur            x0, [fp, #-8]
    // 0xa1cca0: LoadField: d0 = r0->field_7
    //     0xa1cca0: ldur            d0, [x0, #7]
    // 0xa1cca4: r0 = _extentSize()
    //     0xa1cca4: bl              #0xa1cea0  ; [package:flutter/src/widgets/reorderable_list.dart] ::_extentSize
    // 0xa1cca8: stur            x0, [fp, #-0x10]
    // 0xa1ccac: LoadField: d0 = r0->field_7
    //     0xa1ccac: ldur            d0, [x0, #7]
    // 0xa1ccb0: r1 = inline_Allocate_Double()
    //     0xa1ccb0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xa1ccb4: add             x1, x1, #0x10
    //     0xa1ccb8: cmp             x2, x1
    //     0xa1ccbc: b.ls            #0xa1cdc8
    //     0xa1ccc0: str             x1, [THR, #0x50]  ; THR::top
    //     0xa1ccc4: sub             x1, x1, #0xf
    //     0xa1ccc8: movz            x2, #0xe15c
    //     0xa1cccc: movk            x2, #0x3, lsl #16
    //     0xa1ccd0: stur            x2, [x1, #-1]
    // 0xa1ccd4: StoreField: r1->field_7 = d0
    //     0xa1ccd4: stur            d0, [x1, #7]
    // 0xa1ccd8: stur            x1, [fp, #-8]
    // 0xa1ccdc: r0 = SizedBox()
    //     0xa1ccdc: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa1cce0: mov             x1, x0
    // 0xa1cce4: ldur            x0, [fp, #-8]
    // 0xa1cce8: StoreField: r1->field_f = r0
    //     0xa1cce8: stur            w0, [x1, #0xf]
    // 0xa1ccec: ldur            x0, [fp, #-0x10]
    // 0xa1ccf0: LoadField: d0 = r0->field_f
    //     0xa1ccf0: ldur            d0, [x0, #0xf]
    // 0xa1ccf4: r0 = inline_Allocate_Double()
    //     0xa1ccf4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa1ccf8: add             x0, x0, #0x10
    //     0xa1ccfc: cmp             x2, x0
    //     0xa1cd00: b.ls            #0xa1cde4
    //     0xa1cd04: str             x0, [THR, #0x50]  ; THR::top
    //     0xa1cd08: sub             x0, x0, #0xf
    //     0xa1cd0c: movz            x2, #0xe15c
    //     0xa1cd10: movk            x2, #0x3, lsl #16
    //     0xa1cd14: stur            x2, [x0, #-1]
    // 0xa1cd18: StoreField: r0->field_7 = d0
    //     0xa1cd18: stur            d0, [x0, #7]
    // 0xa1cd1c: StoreField: r1->field_13 = r0
    //     0xa1cd1c: stur            w0, [x1, #0x13]
    // 0xa1cd20: mov             x0, x1
    // 0xa1cd24: LeaveFrame
    //     0xa1cd24: mov             SP, fp
    //     0xa1cd28: ldp             fp, lr, [SP], #0x10
    // 0xa1cd2c: ret
    //     0xa1cd2c: ret             
    // 0xa1cd30: LoadField: r1 = r0->field_13
    //     0xa1cd30: ldur            w1, [x0, #0x13]
    // 0xa1cd34: DecompressPointer r1
    //     0xa1cd34: add             x1, x1, HEAP, lsl #32
    // 0xa1cd38: r16 = Sentinel
    //     0xa1cd38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa1cd3c: cmp             w1, w16
    // 0xa1cd40: b.eq            #0xa1cdfc
    // 0xa1cd44: mov             x2, x0
    // 0xa1cd48: r0 = _registerItem()
    //     0xa1cd48: bl              #0x9450bc  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_registerItem
    // 0xa1cd4c: ldur            x1, [fp, #-0x18]
    // 0xa1cd50: r0 = offset()
    //     0xa1cd50: bl              #0x94597c  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::offset
    // 0xa1cd54: mov             x1, x0
    // 0xa1cd58: ldur            x0, [fp, #-0x18]
    // 0xa1cd5c: stur            x1, [fp, #-0x10]
    // 0xa1cd60: LoadField: r2 = r0->field_b
    //     0xa1cd60: ldur            w2, [x0, #0xb]
    // 0xa1cd64: DecompressPointer r2
    //     0xa1cd64: add             x2, x2, HEAP, lsl #32
    // 0xa1cd68: cmp             w2, NULL
    // 0xa1cd6c: b.eq            #0xa1ce08
    // 0xa1cd70: LoadField: r0 = r2->field_13
    //     0xa1cd70: ldur            w0, [x2, #0x13]
    // 0xa1cd74: DecompressPointer r0
    //     0xa1cd74: add             x0, x0, HEAP, lsl #32
    // 0xa1cd78: stur            x0, [fp, #-8]
    // 0xa1cd7c: r0 = Transform()
    //     0xa1cd7c: bl              #0x9d3c68  ; AllocateTransformStub -> Transform (size=0x24)
    // 0xa1cd80: mov             x1, x0
    // 0xa1cd84: ldur            x2, [fp, #-8]
    // 0xa1cd88: ldur            x3, [fp, #-0x10]
    // 0xa1cd8c: stur            x0, [fp, #-8]
    // 0xa1cd90: r0 = Transform.translate()
    //     0xa1cd90: bl              #0xa1ce0c  ; [package:flutter/src/widgets/basic.dart] Transform::Transform.translate
    // 0xa1cd94: ldur            x0, [fp, #-8]
    // 0xa1cd98: LeaveFrame
    //     0xa1cd98: mov             SP, fp
    //     0xa1cd9c: ldp             fp, lr, [SP], #0x10
    // 0xa1cda0: ret
    //     0xa1cda0: ret             
    // 0xa1cda4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1cda4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1cda8: b               #0xa1cc4c
    // 0xa1cdac: r9 = _listState
    //     0xa1cdac: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fe00] Field <_ReorderableItemState@318218688._listState@318218688>: late (offset: 0x14)
    //     0xa1cdb0: ldr             x9, [x9, #0xe00]
    // 0xa1cdb4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa1cdb4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa1cdb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1cdb8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1cdbc: r9 = itemExtent
    //     0xa1cdbc: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd08] Field <<EMAIL>>: late (offset: 0x40)
    //     0xa1cdc0: ldr             x9, [x9, #0xd08]
    // 0xa1cdc4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa1cdc4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa1cdc8: SaveReg d0
    //     0xa1cdc8: str             q0, [SP, #-0x10]!
    // 0xa1cdcc: SaveReg r0
    //     0xa1cdcc: str             x0, [SP, #-8]!
    // 0xa1cdd0: r0 = AllocateDouble()
    //     0xa1cdd0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa1cdd4: mov             x1, x0
    // 0xa1cdd8: RestoreReg r0
    //     0xa1cdd8: ldr             x0, [SP], #8
    // 0xa1cddc: RestoreReg d0
    //     0xa1cddc: ldr             q0, [SP], #0x10
    // 0xa1cde0: b               #0xa1ccd4
    // 0xa1cde4: SaveReg d0
    //     0xa1cde4: str             q0, [SP, #-0x10]!
    // 0xa1cde8: SaveReg r1
    //     0xa1cde8: str             x1, [SP, #-8]!
    // 0xa1cdec: r0 = AllocateDouble()
    //     0xa1cdec: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa1cdf0: RestoreReg r1
    //     0xa1cdf0: ldr             x1, [SP], #8
    // 0xa1cdf4: RestoreReg d0
    //     0xa1cdf4: ldr             q0, [SP], #0x10
    // 0xa1cdf8: b               #0xa1cd18
    // 0xa1cdfc: r9 = _listState
    //     0xa1cdfc: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fe00] Field <_ReorderableItemState@318218688._listState@318218688>: late (offset: 0x14)
    //     0xa1ce00: ldr             x9, [x9, #0xe00]
    // 0xa1ce04: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa1ce04: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa1ce08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1ce08: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa80a08, size: 0x8c
    // 0xa80a08: EnterFrame
    //     0xa80a08: stp             fp, lr, [SP, #-0x10]!
    //     0xa80a0c: mov             fp, SP
    // 0xa80a10: AllocStack(0x8)
    //     0xa80a10: sub             SP, SP, #8
    // 0xa80a14: SetupParameters(_ReorderableItemState this /* r1 => r3, fp-0x8 */)
    //     0xa80a14: mov             x3, x1
    //     0xa80a18: stur            x1, [fp, #-8]
    // 0xa80a1c: CheckStackOverflow
    //     0xa80a1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa80a20: cmp             SP, x16
    //     0xa80a24: b.ls            #0xa80a7c
    // 0xa80a28: LoadField: r1 = r3->field_1f
    //     0xa80a28: ldur            w1, [x3, #0x1f]
    // 0xa80a2c: DecompressPointer r1
    //     0xa80a2c: add             x1, x1, HEAP, lsl #32
    // 0xa80a30: cmp             w1, NULL
    // 0xa80a34: b.eq            #0xa80a40
    // 0xa80a38: r0 = dispose()
    //     0xa80a38: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa80a3c: ldur            x3, [fp, #-8]
    // 0xa80a40: LoadField: r1 = r3->field_13
    //     0xa80a40: ldur            w1, [x3, #0x13]
    // 0xa80a44: DecompressPointer r1
    //     0xa80a44: add             x1, x1, HEAP, lsl #32
    // 0xa80a48: r16 = Sentinel
    //     0xa80a48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa80a4c: cmp             w1, w16
    // 0xa80a50: b.eq            #0xa80a84
    // 0xa80a54: LoadField: r0 = r3->field_b
    //     0xa80a54: ldur            w0, [x3, #0xb]
    // 0xa80a58: DecompressPointer r0
    //     0xa80a58: add             x0, x0, HEAP, lsl #32
    // 0xa80a5c: cmp             w0, NULL
    // 0xa80a60: b.eq            #0xa80a90
    // 0xa80a64: LoadField: r2 = r0->field_b
    //     0xa80a64: ldur            x2, [x0, #0xb]
    // 0xa80a68: r0 = _unregisterItem()
    //     0xa80a68: bl              #0x92b9a8  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_unregisterItem
    // 0xa80a6c: r0 = Null
    //     0xa80a6c: mov             x0, NULL
    // 0xa80a70: LeaveFrame
    //     0xa80a70: mov             SP, fp
    //     0xa80a74: ldp             fp, lr, [SP], #0x10
    // 0xa80a78: ret
    //     0xa80a78: ret             
    // 0xa80a7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa80a7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa80a80: b               #0xa80a28
    // 0xa80a84: r9 = _listState
    //     0xa80a84: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fe00] Field <_ReorderableItemState@318218688._listState@318218688>: late (offset: 0x14)
    //     0xa80a88: ldr             x9, [x9, #0xe00]
    // 0xa80a8c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa80a8c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa80a90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa80a90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4187, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class _SliverReorderableListState&State&TickerProviderStateMixin extends State<dynamic>
     with TickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6f892c, size: 0x184
    // 0x6f892c: EnterFrame
    //     0x6f892c: stp             fp, lr, [SP, #-0x10]!
    //     0x6f8930: mov             fp, SP
    // 0x6f8934: AllocStack(0x20)
    //     0x6f8934: sub             SP, SP, #0x20
    // 0x6f8938: SetupParameters(_SliverReorderableListState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6f8938: mov             x0, x1
    //     0x6f893c: stur            x1, [fp, #-8]
    //     0x6f8940: stur            x2, [fp, #-0x10]
    // 0x6f8944: CheckStackOverflow
    //     0x6f8944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f8948: cmp             SP, x16
    //     0x6f894c: b.ls            #0x6f8aa0
    // 0x6f8950: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6f8950: ldur            w1, [x0, #0x17]
    // 0x6f8954: DecompressPointer r1
    //     0x6f8954: add             x1, x1, HEAP, lsl #32
    // 0x6f8958: cmp             w1, NULL
    // 0x6f895c: b.ne            #0x6f8968
    // 0x6f8960: mov             x1, x0
    // 0x6f8964: r0 = _updateTickerModeNotifier()
    //     0x6f8964: bl              #0x6f8ad4  ; [package:flutter/src/widgets/reorderable_list.dart] _SliverReorderableListState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6f8968: ldur            x0, [fp, #-8]
    // 0x6f896c: LoadField: r1 = r0->field_13
    //     0x6f896c: ldur            w1, [x0, #0x13]
    // 0x6f8970: DecompressPointer r1
    //     0x6f8970: add             x1, x1, HEAP, lsl #32
    // 0x6f8974: cmp             w1, NULL
    // 0x6f8978: b.ne            #0x6f8a10
    // 0x6f897c: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x6f897c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6f8980: ldr             x0, [x0, #0x778]
    //     0x6f8984: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6f8988: cmp             w0, w16
    //     0x6f898c: b.ne            #0x6f8998
    //     0x6f8990: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x6f8994: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6f8998: r1 = <_WidgetTicker>
    //     0x6f8998: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8c0] TypeArguments: <_WidgetTicker>
    //     0x6f899c: ldr             x1, [x1, #0x8c0]
    // 0x6f89a0: stur            x0, [fp, #-0x18]
    // 0x6f89a4: r0 = _Set()
    //     0x6f89a4: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x6f89a8: mov             x1, x0
    // 0x6f89ac: ldur            x0, [fp, #-0x18]
    // 0x6f89b0: stur            x1, [fp, #-0x20]
    // 0x6f89b4: StoreField: r1->field_1b = r0
    //     0x6f89b4: stur            w0, [x1, #0x1b]
    // 0x6f89b8: StoreField: r1->field_b = rZR
    //     0x6f89b8: stur            wzr, [x1, #0xb]
    // 0x6f89bc: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x6f89bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6f89c0: ldr             x0, [x0, #0x780]
    //     0x6f89c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6f89c8: cmp             w0, w16
    //     0x6f89cc: b.ne            #0x6f89d8
    //     0x6f89d0: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x6f89d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6f89d8: mov             x1, x0
    // 0x6f89dc: ldur            x0, [fp, #-0x20]
    // 0x6f89e0: StoreField: r0->field_f = r1
    //     0x6f89e0: stur            w1, [x0, #0xf]
    // 0x6f89e4: StoreField: r0->field_13 = rZR
    //     0x6f89e4: stur            wzr, [x0, #0x13]
    // 0x6f89e8: ArrayStore: r0[0] = rZR  ; List_4
    //     0x6f89e8: stur            wzr, [x0, #0x17]
    // 0x6f89ec: ldur            x1, [fp, #-8]
    // 0x6f89f0: StoreField: r1->field_13 = r0
    //     0x6f89f0: stur            w0, [x1, #0x13]
    //     0x6f89f4: ldurb           w16, [x1, #-1]
    //     0x6f89f8: ldurb           w17, [x0, #-1]
    //     0x6f89fc: and             x16, x17, x16, lsr #2
    //     0x6f8a00: tst             x16, HEAP, lsr #32
    //     0x6f8a04: b.eq            #0x6f8a0c
    //     0x6f8a08: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6f8a0c: b               #0x6f8a14
    // 0x6f8a10: mov             x1, x0
    // 0x6f8a14: ldur            x0, [fp, #-0x10]
    // 0x6f8a18: r0 = _WidgetTicker()
    //     0x6f8a18: bl              #0x6f03ec  ; Allocate_WidgetTickerStub -> _WidgetTicker (size=0x20)
    // 0x6f8a1c: mov             x3, x0
    // 0x6f8a20: ldur            x2, [fp, #-8]
    // 0x6f8a24: stur            x3, [fp, #-0x18]
    // 0x6f8a28: StoreField: r3->field_1b = r2
    //     0x6f8a28: stur            w2, [x3, #0x1b]
    // 0x6f8a2c: r0 = false
    //     0x6f8a2c: add             x0, NULL, #0x30  ; false
    // 0x6f8a30: StoreField: r3->field_b = r0
    //     0x6f8a30: stur            w0, [x3, #0xb]
    // 0x6f8a34: ldur            x0, [fp, #-0x10]
    // 0x6f8a38: StoreField: r3->field_13 = r0
    //     0x6f8a38: stur            w0, [x3, #0x13]
    // 0x6f8a3c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6f8a3c: ldur            w1, [x2, #0x17]
    // 0x6f8a40: DecompressPointer r1
    //     0x6f8a40: add             x1, x1, HEAP, lsl #32
    // 0x6f8a44: cmp             w1, NULL
    // 0x6f8a48: b.eq            #0x6f8aa8
    // 0x6f8a4c: r0 = LoadClassIdInstr(r1)
    //     0x6f8a4c: ldur            x0, [x1, #-1]
    //     0x6f8a50: ubfx            x0, x0, #0xc, #0x14
    // 0x6f8a54: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6f8a54: movz            x17, #0x276f
    //     0x6f8a58: movk            x17, #0x1, lsl #16
    //     0x6f8a5c: add             lr, x0, x17
    //     0x6f8a60: ldr             lr, [x21, lr, lsl #3]
    //     0x6f8a64: blr             lr
    // 0x6f8a68: eor             x2, x0, #0x10
    // 0x6f8a6c: ldur            x1, [fp, #-0x18]
    // 0x6f8a70: r0 = muted=()
    //     0x6f8a70: bl              #0x6efbf4  ; [package:flutter/src/scheduler/ticker.dart] Ticker::muted=
    // 0x6f8a74: ldur            x0, [fp, #-8]
    // 0x6f8a78: LoadField: r1 = r0->field_13
    //     0x6f8a78: ldur            w1, [x0, #0x13]
    // 0x6f8a7c: DecompressPointer r1
    //     0x6f8a7c: add             x1, x1, HEAP, lsl #32
    // 0x6f8a80: cmp             w1, NULL
    // 0x6f8a84: b.eq            #0x6f8aac
    // 0x6f8a88: ldur            x2, [fp, #-0x18]
    // 0x6f8a8c: r0 = add()
    //     0x6f8a8c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x6f8a90: ldur            x0, [fp, #-0x18]
    // 0x6f8a94: LeaveFrame
    //     0x6f8a94: mov             SP, fp
    //     0x6f8a98: ldp             fp, lr, [SP], #0x10
    // 0x6f8a9c: ret
    //     0x6f8a9c: ret             
    // 0x6f8aa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f8aa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f8aa4: b               #0x6f8950
    // 0x6f8aa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f8aa8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6f8aac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f8aac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6f8ad4, size: 0x124
    // 0x6f8ad4: EnterFrame
    //     0x6f8ad4: stp             fp, lr, [SP, #-0x10]!
    //     0x6f8ad8: mov             fp, SP
    // 0x6f8adc: AllocStack(0x18)
    //     0x6f8adc: sub             SP, SP, #0x18
    // 0x6f8ae0: SetupParameters(_SliverReorderableListState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6f8ae0: mov             x2, x1
    //     0x6f8ae4: stur            x1, [fp, #-8]
    // 0x6f8ae8: CheckStackOverflow
    //     0x6f8ae8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f8aec: cmp             SP, x16
    //     0x6f8af0: b.ls            #0x6f8bec
    // 0x6f8af4: LoadField: r1 = r2->field_f
    //     0x6f8af4: ldur            w1, [x2, #0xf]
    // 0x6f8af8: DecompressPointer r1
    //     0x6f8af8: add             x1, x1, HEAP, lsl #32
    // 0x6f8afc: cmp             w1, NULL
    // 0x6f8b00: b.eq            #0x6f8bf4
    // 0x6f8b04: r0 = getNotifier()
    //     0x6f8b04: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6f8b08: mov             x3, x0
    // 0x6f8b0c: ldur            x0, [fp, #-8]
    // 0x6f8b10: stur            x3, [fp, #-0x18]
    // 0x6f8b14: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6f8b14: ldur            w4, [x0, #0x17]
    // 0x6f8b18: DecompressPointer r4
    //     0x6f8b18: add             x4, x4, HEAP, lsl #32
    // 0x6f8b1c: stur            x4, [fp, #-0x10]
    // 0x6f8b20: cmp             w3, w4
    // 0x6f8b24: b.ne            #0x6f8b38
    // 0x6f8b28: r0 = Null
    //     0x6f8b28: mov             x0, NULL
    // 0x6f8b2c: LeaveFrame
    //     0x6f8b2c: mov             SP, fp
    //     0x6f8b30: ldp             fp, lr, [SP], #0x10
    // 0x6f8b34: ret
    //     0x6f8b34: ret             
    // 0x6f8b38: cmp             w4, NULL
    // 0x6f8b3c: b.eq            #0x6f8b80
    // 0x6f8b40: mov             x2, x0
    // 0x6f8b44: r1 = Function '_updateTickers@364311458':.
    //     0x6f8b44: add             x1, PP, #0x56, lsl #12  ; [pp+0x567d0] AnonymousClosure: (0x6f8bf8), in [package:flutter/src/widgets/reorderable_list.dart] _SliverReorderableListState&State&TickerProviderStateMixin::_updateTickers (0x6f8c30)
    //     0x6f8b48: ldr             x1, [x1, #0x7d0]
    // 0x6f8b4c: r0 = AllocateClosure()
    //     0x6f8b4c: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f8b50: ldur            x1, [fp, #-0x10]
    // 0x6f8b54: r2 = LoadClassIdInstr(r1)
    //     0x6f8b54: ldur            x2, [x1, #-1]
    //     0x6f8b58: ubfx            x2, x2, #0xc, #0x14
    // 0x6f8b5c: mov             x16, x0
    // 0x6f8b60: mov             x0, x2
    // 0x6f8b64: mov             x2, x16
    // 0x6f8b68: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6f8b68: movz            x17, #0xbf5c
    //     0x6f8b6c: add             lr, x0, x17
    //     0x6f8b70: ldr             lr, [x21, lr, lsl #3]
    //     0x6f8b74: blr             lr
    // 0x6f8b78: ldur            x0, [fp, #-8]
    // 0x6f8b7c: ldur            x3, [fp, #-0x18]
    // 0x6f8b80: mov             x2, x0
    // 0x6f8b84: r1 = Function '_updateTickers@364311458':.
    //     0x6f8b84: add             x1, PP, #0x56, lsl #12  ; [pp+0x567d0] AnonymousClosure: (0x6f8bf8), in [package:flutter/src/widgets/reorderable_list.dart] _SliverReorderableListState&State&TickerProviderStateMixin::_updateTickers (0x6f8c30)
    //     0x6f8b88: ldr             x1, [x1, #0x7d0]
    // 0x6f8b8c: r0 = AllocateClosure()
    //     0x6f8b8c: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f8b90: ldur            x3, [fp, #-0x18]
    // 0x6f8b94: r1 = LoadClassIdInstr(r3)
    //     0x6f8b94: ldur            x1, [x3, #-1]
    //     0x6f8b98: ubfx            x1, x1, #0xc, #0x14
    // 0x6f8b9c: mov             x2, x0
    // 0x6f8ba0: mov             x0, x1
    // 0x6f8ba4: mov             x1, x3
    // 0x6f8ba8: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6f8ba8: movz            x17, #0xc407
    //     0x6f8bac: add             lr, x0, x17
    //     0x6f8bb0: ldr             lr, [x21, lr, lsl #3]
    //     0x6f8bb4: blr             lr
    // 0x6f8bb8: ldur            x0, [fp, #-0x18]
    // 0x6f8bbc: ldur            x1, [fp, #-8]
    // 0x6f8bc0: ArrayStore: r1[0] = r0  ; List_4
    //     0x6f8bc0: stur            w0, [x1, #0x17]
    //     0x6f8bc4: ldurb           w16, [x1, #-1]
    //     0x6f8bc8: ldurb           w17, [x0, #-1]
    //     0x6f8bcc: and             x16, x17, x16, lsr #2
    //     0x6f8bd0: tst             x16, HEAP, lsr #32
    //     0x6f8bd4: b.eq            #0x6f8bdc
    //     0x6f8bd8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6f8bdc: r0 = Null
    //     0x6f8bdc: mov             x0, NULL
    // 0x6f8be0: LeaveFrame
    //     0x6f8be0: mov             SP, fp
    //     0x6f8be4: ldp             fp, lr, [SP], #0x10
    // 0x6f8be8: ret
    //     0x6f8be8: ret             
    // 0x6f8bec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f8bec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f8bf0: b               #0x6f8af4
    // 0x6f8bf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f8bf4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTickers(dynamic) {
    // ** addr: 0x6f8bf8, size: 0x38
    // 0x6f8bf8: EnterFrame
    //     0x6f8bf8: stp             fp, lr, [SP, #-0x10]!
    //     0x6f8bfc: mov             fp, SP
    // 0x6f8c00: ldr             x0, [fp, #0x10]
    // 0x6f8c04: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6f8c04: ldur            w1, [x0, #0x17]
    // 0x6f8c08: DecompressPointer r1
    //     0x6f8c08: add             x1, x1, HEAP, lsl #32
    // 0x6f8c0c: CheckStackOverflow
    //     0x6f8c0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f8c10: cmp             SP, x16
    //     0x6f8c14: b.ls            #0x6f8c28
    // 0x6f8c18: r0 = _updateTickers()
    //     0x6f8c18: bl              #0x6f8c30  ; [package:flutter/src/widgets/reorderable_list.dart] _SliverReorderableListState&State&TickerProviderStateMixin::_updateTickers
    // 0x6f8c1c: LeaveFrame
    //     0x6f8c1c: mov             SP, fp
    //     0x6f8c20: ldp             fp, lr, [SP], #0x10
    // 0x6f8c24: ret
    //     0x6f8c24: ret             
    // 0x6f8c28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f8c28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f8c2c: b               #0x6f8c18
  }
  _ _updateTickers(/* No info */) {
    // ** addr: 0x6f8c30, size: 0x164
    // 0x6f8c30: EnterFrame
    //     0x6f8c30: stp             fp, lr, [SP, #-0x10]!
    //     0x6f8c34: mov             fp, SP
    // 0x6f8c38: AllocStack(0x20)
    //     0x6f8c38: sub             SP, SP, #0x20
    // 0x6f8c3c: SetupParameters(_SliverReorderableListState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6f8c3c: mov             x2, x1
    //     0x6f8c40: stur            x1, [fp, #-8]
    // 0x6f8c44: CheckStackOverflow
    //     0x6f8c44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f8c48: cmp             SP, x16
    //     0x6f8c4c: b.ls            #0x6f8d7c
    // 0x6f8c50: LoadField: r0 = r2->field_13
    //     0x6f8c50: ldur            w0, [x2, #0x13]
    // 0x6f8c54: DecompressPointer r0
    //     0x6f8c54: add             x0, x0, HEAP, lsl #32
    // 0x6f8c58: cmp             w0, NULL
    // 0x6f8c5c: b.eq            #0x6f8d6c
    // 0x6f8c60: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6f8c60: ldur            w1, [x2, #0x17]
    // 0x6f8c64: DecompressPointer r1
    //     0x6f8c64: add             x1, x1, HEAP, lsl #32
    // 0x6f8c68: cmp             w1, NULL
    // 0x6f8c6c: b.eq            #0x6f8d84
    // 0x6f8c70: r0 = LoadClassIdInstr(r1)
    //     0x6f8c70: ldur            x0, [x1, #-1]
    //     0x6f8c74: ubfx            x0, x0, #0xc, #0x14
    // 0x6f8c78: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6f8c78: movz            x17, #0x276f
    //     0x6f8c7c: movk            x17, #0x1, lsl #16
    //     0x6f8c80: add             lr, x0, x17
    //     0x6f8c84: ldr             lr, [x21, lr, lsl #3]
    //     0x6f8c88: blr             lr
    // 0x6f8c8c: eor             x2, x0, #0x10
    // 0x6f8c90: ldur            x0, [fp, #-8]
    // 0x6f8c94: stur            x2, [fp, #-0x10]
    // 0x6f8c98: LoadField: r1 = r0->field_13
    //     0x6f8c98: ldur            w1, [x0, #0x13]
    // 0x6f8c9c: DecompressPointer r1
    //     0x6f8c9c: add             x1, x1, HEAP, lsl #32
    // 0x6f8ca0: cmp             w1, NULL
    // 0x6f8ca4: b.eq            #0x6f8d88
    // 0x6f8ca8: r0 = iterator()
    //     0x6f8ca8: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x6f8cac: stur            x0, [fp, #-0x18]
    // 0x6f8cb0: LoadField: r2 = r0->field_7
    //     0x6f8cb0: ldur            w2, [x0, #7]
    // 0x6f8cb4: DecompressPointer r2
    //     0x6f8cb4: add             x2, x2, HEAP, lsl #32
    // 0x6f8cb8: stur            x2, [fp, #-8]
    // 0x6f8cbc: ldur            x3, [fp, #-0x10]
    // 0x6f8cc0: CheckStackOverflow
    //     0x6f8cc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f8cc4: cmp             SP, x16
    //     0x6f8cc8: b.ls            #0x6f8d8c
    // 0x6f8ccc: mov             x1, x0
    // 0x6f8cd0: r0 = moveNext()
    //     0x6f8cd0: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x6f8cd4: tbnz            w0, #4, #0x6f8d6c
    // 0x6f8cd8: ldur            x3, [fp, #-0x18]
    // 0x6f8cdc: LoadField: r4 = r3->field_33
    //     0x6f8cdc: ldur            w4, [x3, #0x33]
    // 0x6f8ce0: DecompressPointer r4
    //     0x6f8ce0: add             x4, x4, HEAP, lsl #32
    // 0x6f8ce4: stur            x4, [fp, #-0x20]
    // 0x6f8ce8: cmp             w4, NULL
    // 0x6f8cec: b.ne            #0x6f8d20
    // 0x6f8cf0: mov             x0, x4
    // 0x6f8cf4: ldur            x2, [fp, #-8]
    // 0x6f8cf8: r1 = Null
    //     0x6f8cf8: mov             x1, NULL
    // 0x6f8cfc: cmp             w2, NULL
    // 0x6f8d00: b.eq            #0x6f8d20
    // 0x6f8d04: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6f8d04: ldur            w4, [x2, #0x17]
    // 0x6f8d08: DecompressPointer r4
    //     0x6f8d08: add             x4, x4, HEAP, lsl #32
    // 0x6f8d0c: r8 = X0
    //     0x6f8d0c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6f8d10: LoadField: r9 = r4->field_7
    //     0x6f8d10: ldur            x9, [x4, #7]
    // 0x6f8d14: r3 = Null
    //     0x6f8d14: add             x3, PP, #0x56, lsl #12  ; [pp+0x567c0] Null
    //     0x6f8d18: ldr             x3, [x3, #0x7c0]
    // 0x6f8d1c: blr             x9
    // 0x6f8d20: ldur            x2, [fp, #-0x10]
    // 0x6f8d24: ldur            x0, [fp, #-0x20]
    // 0x6f8d28: LoadField: r1 = r0->field_b
    //     0x6f8d28: ldur            w1, [x0, #0xb]
    // 0x6f8d2c: DecompressPointer r1
    //     0x6f8d2c: add             x1, x1, HEAP, lsl #32
    // 0x6f8d30: cmp             w2, w1
    // 0x6f8d34: b.eq            #0x6f8d60
    // 0x6f8d38: StoreField: r0->field_b = r2
    //     0x6f8d38: stur            w2, [x0, #0xb]
    // 0x6f8d3c: tbnz            w2, #4, #0x6f8d4c
    // 0x6f8d40: mov             x1, x0
    // 0x6f8d44: r0 = unscheduleTick()
    //     0x6f8d44: bl              #0x656684  ; [package:flutter/src/scheduler/ticker.dart] Ticker::unscheduleTick
    // 0x6f8d48: b               #0x6f8d60
    // 0x6f8d4c: mov             x1, x0
    // 0x6f8d50: r0 = shouldScheduleTick()
    //     0x6f8d50: bl              #0x655b90  ; [package:flutter/src/scheduler/ticker.dart] Ticker::shouldScheduleTick
    // 0x6f8d54: tbnz            w0, #4, #0x6f8d60
    // 0x6f8d58: ldur            x1, [fp, #-0x20]
    // 0x6f8d5c: r0 = scheduleTick()
    //     0x6f8d5c: bl              #0x655924  ; [package:flutter/src/scheduler/ticker.dart] Ticker::scheduleTick
    // 0x6f8d60: ldur            x0, [fp, #-0x18]
    // 0x6f8d64: ldur            x2, [fp, #-8]
    // 0x6f8d68: b               #0x6f8cbc
    // 0x6f8d6c: r0 = Null
    //     0x6f8d6c: mov             x0, NULL
    // 0x6f8d70: LeaveFrame
    //     0x6f8d70: mov             SP, fp
    //     0x6f8d74: ldp             fp, lr, [SP], #0x10
    // 0x6f8d78: ret
    //     0x6f8d78: ret             
    // 0x6f8d7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f8d7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f8d80: b               #0x6f8c50
    // 0x6f8d84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f8d84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6f8d88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f8d88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6f8d8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f8d8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f8d90: b               #0x6f8ccc
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa80974, size: 0x94
    // 0xa80974: EnterFrame
    //     0xa80974: stp             fp, lr, [SP, #-0x10]!
    //     0xa80978: mov             fp, SP
    // 0xa8097c: AllocStack(0x10)
    //     0xa8097c: sub             SP, SP, #0x10
    // 0xa80980: SetupParameters(_SliverReorderableListState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa80980: mov             x0, x1
    //     0xa80984: stur            x1, [fp, #-0x10]
    // 0xa80988: CheckStackOverflow
    //     0xa80988: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8098c: cmp             SP, x16
    //     0xa80990: b.ls            #0xa80a00
    // 0xa80994: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa80994: ldur            w3, [x0, #0x17]
    // 0xa80998: DecompressPointer r3
    //     0xa80998: add             x3, x3, HEAP, lsl #32
    // 0xa8099c: stur            x3, [fp, #-8]
    // 0xa809a0: cmp             w3, NULL
    // 0xa809a4: b.ne            #0xa809b0
    // 0xa809a8: mov             x1, x0
    // 0xa809ac: b               #0xa809ec
    // 0xa809b0: mov             x2, x0
    // 0xa809b4: r1 = Function '_updateTickers@364311458':.
    //     0xa809b4: add             x1, PP, #0x56, lsl #12  ; [pp+0x567d0] AnonymousClosure: (0x6f8bf8), in [package:flutter/src/widgets/reorderable_list.dart] _SliverReorderableListState&State&TickerProviderStateMixin::_updateTickers (0x6f8c30)
    //     0xa809b8: ldr             x1, [x1, #0x7d0]
    // 0xa809bc: r0 = AllocateClosure()
    //     0xa809bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xa809c0: ldur            x1, [fp, #-8]
    // 0xa809c4: r2 = LoadClassIdInstr(r1)
    //     0xa809c4: ldur            x2, [x1, #-1]
    //     0xa809c8: ubfx            x2, x2, #0xc, #0x14
    // 0xa809cc: mov             x16, x0
    // 0xa809d0: mov             x0, x2
    // 0xa809d4: mov             x2, x16
    // 0xa809d8: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa809d8: movz            x17, #0xbf5c
    //     0xa809dc: add             lr, x0, x17
    //     0xa809e0: ldr             lr, [x21, lr, lsl #3]
    //     0xa809e4: blr             lr
    // 0xa809e8: ldur            x1, [fp, #-0x10]
    // 0xa809ec: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa809ec: stur            NULL, [x1, #0x17]
    // 0xa809f0: r0 = Null
    //     0xa809f0: mov             x0, NULL
    // 0xa809f4: LeaveFrame
    //     0xa809f4: mov             SP, fp
    //     0xa809f8: ldp             fp, lr, [SP], #0x10
    // 0xa809fc: ret
    //     0xa809fc: ret             
    // 0xa80a00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa80a00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa80a04: b               #0xa80994
  }
  _ activate(/* No info */) {
    // ** addr: 0xa85888, size: 0x48
    // 0xa85888: EnterFrame
    //     0xa85888: stp             fp, lr, [SP, #-0x10]!
    //     0xa8588c: mov             fp, SP
    // 0xa85890: AllocStack(0x8)
    //     0xa85890: sub             SP, SP, #8
    // 0xa85894: SetupParameters(_SliverReorderableListState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa85894: mov             x0, x1
    //     0xa85898: stur            x1, [fp, #-8]
    // 0xa8589c: CheckStackOverflow
    //     0xa8589c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa858a0: cmp             SP, x16
    //     0xa858a4: b.ls            #0xa858c8
    // 0xa858a8: mov             x1, x0
    // 0xa858ac: r0 = _updateTickerModeNotifier()
    //     0xa858ac: bl              #0x6f8ad4  ; [package:flutter/src/widgets/reorderable_list.dart] _SliverReorderableListState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa858b0: ldur            x1, [fp, #-8]
    // 0xa858b4: r0 = _updateTickers()
    //     0xa858b4: bl              #0x6f8c30  ; [package:flutter/src/widgets/reorderable_list.dart] _SliverReorderableListState&State&TickerProviderStateMixin::_updateTickers
    // 0xa858b8: r0 = Null
    //     0xa858b8: mov             x0, NULL
    // 0xa858bc: LeaveFrame
    //     0xa858bc: mov             SP, fp
    //     0xa858c0: ldp             fp, lr, [SP], #0x10
    // 0xa858c4: ret
    //     0xa858c4: ret             
    // 0xa858c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa858c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa858cc: b               #0xa858a8
  }
}

// class id: 4188, size: 0x44, field offset: 0x1c
class SliverReorderableListState extends _SliverReorderableListState&State&TickerProviderStateMixin {

  late ScrollableState _scrollable; // offset: 0x40

  _ _unregisterItem(/* No info */) {
    // ** addr: 0x92b9a8, size: 0x98
    // 0x92b9a8: EnterFrame
    //     0x92b9a8: stp             fp, lr, [SP, #-0x10]!
    //     0x92b9ac: mov             fp, SP
    // 0x92b9b0: AllocStack(0x18)
    //     0x92b9b0: sub             SP, SP, #0x18
    // 0x92b9b4: SetupParameters(dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x92b9b4: stur            x3, [fp, #-0x18]
    // 0x92b9b8: CheckStackOverflow
    //     0x92b9b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92b9bc: cmp             SP, x16
    //     0x92b9c0: b.ls            #0x92ba38
    // 0x92b9c4: LoadField: r4 = r1->field_1b
    //     0x92b9c4: ldur            w4, [x1, #0x1b]
    // 0x92b9c8: DecompressPointer r4
    //     0x92b9c8: add             x4, x4, HEAP, lsl #32
    // 0x92b9cc: stur            x4, [fp, #-0x10]
    // 0x92b9d0: r0 = BoxInt64Instr(r2)
    //     0x92b9d0: sbfiz           x0, x2, #1, #0x1f
    //     0x92b9d4: cmp             x2, x0, asr #1
    //     0x92b9d8: b.eq            #0x92b9e4
    //     0x92b9dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x92b9e0: stur            x2, [x0, #7]
    // 0x92b9e4: mov             x1, x4
    // 0x92b9e8: mov             x2, x0
    // 0x92b9ec: stur            x0, [fp, #-8]
    // 0x92b9f0: r0 = _getValueOrData()
    //     0x92b9f0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x92b9f4: ldur            x1, [fp, #-0x10]
    // 0x92b9f8: LoadField: r2 = r1->field_f
    //     0x92b9f8: ldur            w2, [x1, #0xf]
    // 0x92b9fc: DecompressPointer r2
    //     0x92b9fc: add             x2, x2, HEAP, lsl #32
    // 0x92ba00: cmp             w2, w0
    // 0x92ba04: b.ne            #0x92ba10
    // 0x92ba08: r2 = Null
    //     0x92ba08: mov             x2, NULL
    // 0x92ba0c: b               #0x92ba14
    // 0x92ba10: mov             x2, x0
    // 0x92ba14: ldur            x0, [fp, #-0x18]
    // 0x92ba18: cmp             w2, w0
    // 0x92ba1c: b.ne            #0x92ba28
    // 0x92ba20: ldur            x2, [fp, #-8]
    // 0x92ba24: r0 = remove()
    //     0x92ba24: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x92ba28: r0 = Null
    //     0x92ba28: mov             x0, NULL
    // 0x92ba2c: LeaveFrame
    //     0x92ba2c: mov             SP, fp
    //     0x92ba30: ldp             fp, lr, [SP], #0x10
    // 0x92ba34: ret
    //     0x92ba34: ret             
    // 0x92ba38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92ba38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92ba3c: b               #0x92b9c4
  }
  _ _registerItem(/* No info */) {
    // ** addr: 0x9450bc, size: 0x2c8
    // 0x9450bc: EnterFrame
    //     0x9450bc: stp             fp, lr, [SP, #-0x10]!
    //     0x9450c0: mov             fp, SP
    // 0x9450c4: AllocStack(0x18)
    //     0x9450c4: sub             SP, SP, #0x18
    // 0x9450c8: SetupParameters(SliverReorderableListState this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x9450c8: mov             x4, x1
    //     0x9450cc: mov             x3, x2
    //     0x9450d0: stur            x1, [fp, #-0x10]
    //     0x9450d4: stur            x2, [fp, #-0x18]
    // 0x9450d8: CheckStackOverflow
    //     0x9450d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9450dc: cmp             SP, x16
    //     0x9450e0: b.ls            #0x945338
    // 0x9450e4: LoadField: r0 = r4->field_27
    //     0x9450e4: ldur            w0, [x4, #0x27]
    // 0x9450e8: DecompressPointer r0
    //     0x9450e8: add             x0, x0, HEAP, lsl #32
    // 0x9450ec: cmp             w0, NULL
    // 0x9450f0: b.eq            #0x94522c
    // 0x9450f4: LoadField: r5 = r4->field_1b
    //     0x9450f4: ldur            w5, [x4, #0x1b]
    // 0x9450f8: DecompressPointer r5
    //     0x9450f8: add             x5, x5, HEAP, lsl #32
    // 0x9450fc: stur            x5, [fp, #-8]
    // 0x945100: LoadField: r0 = r3->field_b
    //     0x945100: ldur            w0, [x3, #0xb]
    // 0x945104: DecompressPointer r0
    //     0x945104: add             x0, x0, HEAP, lsl #32
    // 0x945108: cmp             w0, NULL
    // 0x94510c: b.eq            #0x945340
    // 0x945110: LoadField: r2 = r0->field_b
    //     0x945110: ldur            x2, [x0, #0xb]
    // 0x945114: r0 = BoxInt64Instr(r2)
    //     0x945114: sbfiz           x0, x2, #1, #0x1f
    //     0x945118: cmp             x2, x0, asr #1
    //     0x94511c: b.eq            #0x945128
    //     0x945120: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x945124: stur            x2, [x0, #7]
    // 0x945128: mov             x1, x5
    // 0x94512c: mov             x2, x0
    // 0x945130: r0 = _getValueOrData()
    //     0x945130: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x945134: mov             x1, x0
    // 0x945138: ldur            x0, [fp, #-8]
    // 0x94513c: LoadField: r2 = r0->field_f
    //     0x94513c: ldur            w2, [x0, #0xf]
    // 0x945140: DecompressPointer r2
    //     0x945140: add             x2, x2, HEAP, lsl #32
    // 0x945144: cmp             w2, w1
    // 0x945148: b.ne            #0x945150
    // 0x94514c: r1 = Null
    //     0x94514c: mov             x1, NULL
    // 0x945150: ldur            x0, [fp, #-0x18]
    // 0x945154: cmp             w1, w0
    // 0x945158: b.eq            #0x94522c
    // 0x94515c: ldur            x4, [fp, #-0x10]
    // 0x945160: LoadField: r1 = r4->field_27
    //     0x945160: ldur            w1, [x4, #0x27]
    // 0x945164: DecompressPointer r1
    //     0x945164: add             x1, x1, HEAP, lsl #32
    // 0x945168: cmp             w1, NULL
    // 0x94516c: b.eq            #0x945344
    // 0x945170: LoadField: r2 = r1->field_27
    //     0x945170: ldur            w2, [x1, #0x27]
    // 0x945174: DecompressPointer r2
    //     0x945174: add             x2, x2, HEAP, lsl #32
    // 0x945178: r16 = Sentinel
    //     0x945178: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x94517c: cmp             w2, w16
    // 0x945180: b.eq            #0x945348
    // 0x945184: LoadField: r3 = r1->field_3f
    //     0x945184: ldur            w3, [x1, #0x3f]
    // 0x945188: DecompressPointer r3
    //     0x945188: add             x3, x3, HEAP, lsl #32
    // 0x94518c: r16 = Sentinel
    //     0x94518c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x945190: cmp             w3, w16
    // 0x945194: b.eq            #0x945354
    // 0x945198: LoadField: r1 = r4->field_3f
    //     0x945198: ldur            w1, [x4, #0x3f]
    // 0x94519c: DecompressPointer r1
    //     0x94519c: add             x1, x1, HEAP, lsl #32
    // 0x9451a0: r16 = Sentinel
    //     0x9451a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9451a4: cmp             w1, w16
    // 0x9451a8: b.eq            #0x945360
    // 0x9451ac: LoadField: r5 = r1->field_b
    //     0x9451ac: ldur            w5, [x1, #0xb]
    // 0x9451b0: DecompressPointer r5
    //     0x9451b0: add             x5, x5, HEAP, lsl #32
    // 0x9451b4: cmp             w5, NULL
    // 0x9451b8: b.eq            #0x94536c
    // 0x9451bc: LoadField: r1 = r5->field_b
    //     0x9451bc: ldur            w1, [x5, #0xb]
    // 0x9451c0: DecompressPointer r1
    //     0x9451c0: add             x1, x1, HEAP, lsl #32
    // 0x9451c4: r16 = Instance_AxisDirection
    //     0x9451c4: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0x9451c8: cmp             w1, w16
    // 0x9451cc: b.eq            #0x9451dc
    // 0x9451d0: r16 = Instance_AxisDirection
    //     0x9451d0: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0x9451d4: cmp             w1, w16
    // 0x9451d8: b.ne            #0x9451e4
    // 0x9451dc: r6 = true
    //     0x9451dc: add             x6, NULL, #0x20  ; true
    // 0x9451e0: b               #0x945208
    // 0x9451e4: r16 = Instance_AxisDirection
    //     0x9451e4: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0x9451e8: cmp             w1, w16
    // 0x9451ec: b.eq            #0x9451fc
    // 0x9451f0: r16 = Instance_AxisDirection
    //     0x9451f0: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0x9451f4: cmp             w1, w16
    // 0x9451f8: b.ne            #0x945204
    // 0x9451fc: r6 = false
    //     0x9451fc: add             x6, NULL, #0x30  ; false
    // 0x945200: b               #0x945208
    // 0x945204: r6 = Null
    //     0x945204: mov             x6, NULL
    // 0x945208: r1 = LoadInt32Instr(r2)
    //     0x945208: sbfx            x1, x2, #1, #0x1f
    //     0x94520c: tbz             w2, #0, #0x945214
    //     0x945210: ldur            x1, [x2, #7]
    // 0x945214: LoadField: d0 = r3->field_7
    //     0x945214: ldur            d0, [x3, #7]
    // 0x945218: mov             x2, x1
    // 0x94521c: mov             x3, x1
    // 0x945220: mov             x1, x0
    // 0x945224: r5 = false
    //     0x945224: add             x5, NULL, #0x30  ; false
    // 0x945228: r0 = updateForGap()
    //     0x945228: bl              #0x9454c4  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::updateForGap
    // 0x94522c: ldur            x5, [fp, #-0x10]
    // 0x945230: ldur            x4, [fp, #-0x18]
    // 0x945234: LoadField: r2 = r5->field_1b
    //     0x945234: ldur            w2, [x5, #0x1b]
    // 0x945238: DecompressPointer r2
    //     0x945238: add             x2, x2, HEAP, lsl #32
    // 0x94523c: LoadField: r0 = r4->field_b
    //     0x94523c: ldur            w0, [x4, #0xb]
    // 0x945240: DecompressPointer r0
    //     0x945240: add             x0, x0, HEAP, lsl #32
    // 0x945244: cmp             w0, NULL
    // 0x945248: b.eq            #0x945370
    // 0x94524c: LoadField: r3 = r0->field_b
    //     0x94524c: ldur            x3, [x0, #0xb]
    // 0x945250: r0 = BoxInt64Instr(r3)
    //     0x945250: sbfiz           x0, x3, #1, #0x1f
    //     0x945254: cmp             x3, x0, asr #1
    //     0x945258: b.eq            #0x945264
    //     0x94525c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x945260: stur            x3, [x0, #7]
    // 0x945264: mov             x1, x2
    // 0x945268: mov             x2, x0
    // 0x94526c: mov             x3, x4
    // 0x945270: r0 = []=()
    //     0x945270: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x945274: ldur            x3, [fp, #-0x18]
    // 0x945278: LoadField: r0 = r3->field_b
    //     0x945278: ldur            w0, [x3, #0xb]
    // 0x94527c: DecompressPointer r0
    //     0x94527c: add             x0, x0, HEAP, lsl #32
    // 0x945280: cmp             w0, NULL
    // 0x945284: b.eq            #0x945374
    // 0x945288: LoadField: r2 = r0->field_b
    //     0x945288: ldur            x2, [x0, #0xb]
    // 0x94528c: ldur            x0, [fp, #-0x10]
    // 0x945290: LoadField: r1 = r0->field_27
    //     0x945290: ldur            w1, [x0, #0x27]
    // 0x945294: DecompressPointer r1
    //     0x945294: add             x1, x1, HEAP, lsl #32
    // 0x945298: cmp             w1, NULL
    // 0x94529c: b.ne            #0x9452a8
    // 0x9452a0: r4 = Null
    //     0x9452a0: mov             x4, NULL
    // 0x9452a4: b               #0x9452c0
    // 0x9452a8: LoadField: r0 = r1->field_27
    //     0x9452a8: ldur            w0, [x1, #0x27]
    // 0x9452ac: DecompressPointer r0
    //     0x9452ac: add             x0, x0, HEAP, lsl #32
    // 0x9452b0: r16 = Sentinel
    //     0x9452b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9452b4: cmp             w0, w16
    // 0x9452b8: b.eq            #0x945378
    // 0x9452bc: mov             x4, x0
    // 0x9452c0: r0 = BoxInt64Instr(r2)
    //     0x9452c0: sbfiz           x0, x2, #1, #0x1f
    //     0x9452c4: cmp             x2, x0, asr #1
    //     0x9452c8: b.eq            #0x9452d4
    //     0x9452cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9452d0: stur            x2, [x0, #7]
    // 0x9452d4: cmp             w0, w4
    // 0x9452d8: b.eq            #0x945314
    // 0x9452dc: and             w16, w0, w4
    // 0x9452e0: branchIfSmi(r16, 0x945328)
    //     0x9452e0: tbz             w16, #0, #0x945328
    // 0x9452e4: r16 = LoadClassIdInstr(r0)
    //     0x9452e4: ldur            x16, [x0, #-1]
    //     0x9452e8: ubfx            x16, x16, #0xc, #0x14
    // 0x9452ec: cmp             x16, #0x3d
    // 0x9452f0: b.ne            #0x945328
    // 0x9452f4: r16 = LoadClassIdInstr(r4)
    //     0x9452f4: ldur            x16, [x4, #-1]
    //     0x9452f8: ubfx            x16, x16, #0xc, #0x14
    // 0x9452fc: cmp             x16, #0x3d
    // 0x945300: b.ne            #0x945328
    // 0x945304: LoadField: r16 = r0->field_7
    //     0x945304: ldur            x16, [x0, #7]
    // 0x945308: LoadField: r17 = r4->field_7
    //     0x945308: ldur            x17, [x4, #7]
    // 0x94530c: cmp             x16, x17
    // 0x945310: b.ne            #0x945328
    // 0x945314: mov             x1, x3
    // 0x945318: r2 = true
    //     0x945318: add             x2, NULL, #0x20  ; true
    // 0x94531c: r0 = dragging=()
    //     0x94531c: bl              #0x945420  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::dragging=
    // 0x945320: ldur            x1, [fp, #-0x18]
    // 0x945324: r0 = rebuild()
    //     0x945324: bl              #0x945384  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::rebuild
    // 0x945328: r0 = Null
    //     0x945328: mov             x0, NULL
    // 0x94532c: LeaveFrame
    //     0x94532c: mov             SP, fp
    //     0x945330: ldp             fp, lr, [SP], #0x10
    // 0x945334: ret
    //     0x945334: ret             
    // 0x945338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945338: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94533c: b               #0x9450e4
    // 0x945340: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945340: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x945344: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945344: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x945348: r9 = index
    //     0x945348: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd28] Field <<EMAIL>>: late (offset: 0x28)
    //     0x94534c: ldr             x9, [x9, #0xd28]
    // 0x945350: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x945350: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x945354: r9 = itemExtent
    //     0x945354: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd08] Field <<EMAIL>>: late (offset: 0x40)
    //     0x945358: ldr             x9, [x9, #0xd08]
    // 0x94535c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x94535c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x945360: r9 = _scrollable
    //     0x945360: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0x945364: ldr             x9, [x9, #0xd10]
    // 0x945368: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x945368: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x94536c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94536c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x945370: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945370: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x945374: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945374: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x945378: r9 = index
    //     0x945378: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd28] Field <<EMAIL>>: late (offset: 0x28)
    //     0x94537c: ldr             x9, [x9, #0xd28]
    // 0x945380: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x945380: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ _scrollDirection(/* No info */) {
    // ** addr: 0x9458e8, size: 0x94
    // 0x9458e8: EnterFrame
    //     0x9458e8: stp             fp, lr, [SP, #-0x10]!
    //     0x9458ec: mov             fp, SP
    // 0x9458f0: LoadField: r2 = r1->field_3f
    //     0x9458f0: ldur            w2, [x1, #0x3f]
    // 0x9458f4: DecompressPointer r2
    //     0x9458f4: add             x2, x2, HEAP, lsl #32
    // 0x9458f8: r16 = Sentinel
    //     0x9458f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9458fc: cmp             w2, w16
    // 0x945900: b.eq            #0x94596c
    // 0x945904: LoadField: r1 = r2->field_b
    //     0x945904: ldur            w1, [x2, #0xb]
    // 0x945908: DecompressPointer r1
    //     0x945908: add             x1, x1, HEAP, lsl #32
    // 0x94590c: cmp             w1, NULL
    // 0x945910: b.eq            #0x945978
    // 0x945914: LoadField: r2 = r1->field_b
    //     0x945914: ldur            w2, [x1, #0xb]
    // 0x945918: DecompressPointer r2
    //     0x945918: add             x2, x2, HEAP, lsl #32
    // 0x94591c: r16 = Instance_AxisDirection
    //     0x94591c: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0x945920: cmp             w2, w16
    // 0x945924: b.eq            #0x945934
    // 0x945928: r16 = Instance_AxisDirection
    //     0x945928: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0x94592c: cmp             w2, w16
    // 0x945930: b.ne            #0x94593c
    // 0x945934: r0 = Instance_Axis
    //     0x945934: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x945938: b               #0x945960
    // 0x94593c: r16 = Instance_AxisDirection
    //     0x94593c: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0x945940: cmp             w2, w16
    // 0x945944: b.eq            #0x945954
    // 0x945948: r16 = Instance_AxisDirection
    //     0x945948: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0x94594c: cmp             w2, w16
    // 0x945950: b.ne            #0x94595c
    // 0x945954: r0 = Instance_Axis
    //     0x945954: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x945958: b               #0x945960
    // 0x94595c: r0 = Null
    //     0x94595c: mov             x0, NULL
    // 0x945960: LeaveFrame
    //     0x945960: mov             SP, fp
    //     0x945964: ldp             fp, lr, [SP], #0x10
    // 0x945968: ret
    //     0x945968: ret             
    // 0x94596c: r9 = _scrollable
    //     0x94596c: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0x945970: ldr             x9, [x9, #0xd10]
    // 0x945974: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x945974: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x945978: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945978: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x995c4c, size: 0x1c8
    // 0x995c4c: EnterFrame
    //     0x995c4c: stp             fp, lr, [SP, #-0x10]!
    //     0x995c50: mov             fp, SP
    // 0x995c54: AllocStack(0x18)
    //     0x995c54: sub             SP, SP, #0x18
    // 0x995c58: SetupParameters(SliverReorderableListState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x995c58: mov             x4, x1
    //     0x995c5c: mov             x3, x2
    //     0x995c60: stur            x1, [fp, #-8]
    //     0x995c64: stur            x2, [fp, #-0x10]
    // 0x995c68: CheckStackOverflow
    //     0x995c68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x995c6c: cmp             SP, x16
    //     0x995c70: b.ls            #0x995df4
    // 0x995c74: mov             x0, x3
    // 0x995c78: r2 = Null
    //     0x995c78: mov             x2, NULL
    // 0x995c7c: r1 = Null
    //     0x995c7c: mov             x1, NULL
    // 0x995c80: r4 = 60
    //     0x995c80: movz            x4, #0x3c
    // 0x995c84: branchIfSmi(r0, 0x995c90)
    //     0x995c84: tbz             w0, #0, #0x995c90
    // 0x995c88: r4 = LoadClassIdInstr(r0)
    //     0x995c88: ldur            x4, [x0, #-1]
    //     0x995c8c: ubfx            x4, x4, #0xc, #0x14
    // 0x995c90: r17 = 4764
    //     0x995c90: movz            x17, #0x129c
    // 0x995c94: cmp             x4, x17
    // 0x995c98: b.eq            #0x995cb0
    // 0x995c9c: r8 = SliverReorderableList
    //     0x995c9c: add             x8, PP, #0x56, lsl #12  ; [pp+0x56840] Type: SliverReorderableList
    //     0x995ca0: ldr             x8, [x8, #0x840]
    // 0x995ca4: r3 = Null
    //     0x995ca4: add             x3, PP, #0x56, lsl #12  ; [pp+0x56848] Null
    //     0x995ca8: ldr             x3, [x3, #0x848]
    // 0x995cac: r0 = SliverReorderableList()
    //     0x995cac: bl              #0x6f8ab0  ; IsType_SliverReorderableList_Stub
    // 0x995cb0: ldur            x3, [fp, #-8]
    // 0x995cb4: LoadField: r2 = r3->field_7
    //     0x995cb4: ldur            w2, [x3, #7]
    // 0x995cb8: DecompressPointer r2
    //     0x995cb8: add             x2, x2, HEAP, lsl #32
    // 0x995cbc: ldur            x0, [fp, #-0x10]
    // 0x995cc0: r1 = Null
    //     0x995cc0: mov             x1, NULL
    // 0x995cc4: cmp             w2, NULL
    // 0x995cc8: b.eq            #0x995cec
    // 0x995ccc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x995ccc: ldur            w4, [x2, #0x17]
    // 0x995cd0: DecompressPointer r4
    //     0x995cd0: add             x4, x4, HEAP, lsl #32
    // 0x995cd4: r8 = X0 bound StatefulWidget
    //     0x995cd4: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x995cd8: ldr             x8, [x8, #0x7f8]
    // 0x995cdc: LoadField: r9 = r4->field_7
    //     0x995cdc: ldur            x9, [x4, #7]
    // 0x995ce0: r3 = Null
    //     0x995ce0: add             x3, PP, #0x56, lsl #12  ; [pp+0x56858] Null
    //     0x995ce4: ldr             x3, [x3, #0x858]
    // 0x995ce8: blr             x9
    // 0x995cec: ldur            x0, [fp, #-8]
    // 0x995cf0: LoadField: r1 = r0->field_b
    //     0x995cf0: ldur            w1, [x0, #0xb]
    // 0x995cf4: DecompressPointer r1
    //     0x995cf4: add             x1, x1, HEAP, lsl #32
    // 0x995cf8: cmp             w1, NULL
    // 0x995cfc: b.eq            #0x995dfc
    // 0x995d00: LoadField: r2 = r1->field_13
    //     0x995d00: ldur            x2, [x1, #0x13]
    // 0x995d04: ldur            x1, [fp, #-0x10]
    // 0x995d08: LoadField: r3 = r1->field_13
    //     0x995d08: ldur            x3, [x1, #0x13]
    // 0x995d0c: cmp             x2, x3
    // 0x995d10: b.eq            #0x995d1c
    // 0x995d14: mov             x1, x0
    // 0x995d18: r0 = cancelReorder()
    //     0x995d18: bl              #0x995e24  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::cancelReorder
    // 0x995d1c: ldur            x2, [fp, #-8]
    // 0x995d20: d0 = 50.000000
    //     0x995d20: ldr             d0, [PP, #0x5ac0]  ; [pp+0x5ac0] IMM: double(50) from 0x4049000000000000
    // 0x995d24: LoadField: r0 = r2->field_b
    //     0x995d24: ldur            w0, [x2, #0xb]
    // 0x995d28: DecompressPointer r0
    //     0x995d28: add             x0, x0, HEAP, lsl #32
    // 0x995d2c: cmp             w0, NULL
    // 0x995d30: b.eq            #0x995e00
    // 0x995d34: fcmp            d0, d0
    // 0x995d38: b.eq            #0x995de4
    // 0x995d3c: LoadField: r1 = r2->field_3b
    //     0x995d3c: ldur            w1, [x2, #0x3b]
    // 0x995d40: DecompressPointer r1
    //     0x995d40: add             x1, x1, HEAP, lsl #32
    // 0x995d44: cmp             w1, NULL
    // 0x995d48: b.eq            #0x995d54
    // 0x995d4c: r0 = disallowIndicator()
    //     0x995d4c: bl              #0x995e14  ; [package:flutter/src/widgets/overscroll_indicator.dart] OverscrollIndicatorNotification::disallowIndicator
    // 0x995d50: ldur            x2, [fp, #-8]
    // 0x995d54: LoadField: r0 = r2->field_3f
    //     0x995d54: ldur            w0, [x2, #0x3f]
    // 0x995d58: DecompressPointer r0
    //     0x995d58: add             x0, x0, HEAP, lsl #32
    // 0x995d5c: r16 = Sentinel
    //     0x995d5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x995d60: cmp             w0, w16
    // 0x995d64: b.eq            #0x995e04
    // 0x995d68: stur            x0, [fp, #-0x10]
    // 0x995d6c: LoadField: r1 = r2->field_b
    //     0x995d6c: ldur            w1, [x2, #0xb]
    // 0x995d70: DecompressPointer r1
    //     0x995d70: add             x1, x1, HEAP, lsl #32
    // 0x995d74: cmp             w1, NULL
    // 0x995d78: b.eq            #0x995e10
    // 0x995d7c: r0 = EdgeDraggingAutoScroller()
    //     0x995d7c: bl              #0x94619c  ; AllocateEdgeDraggingAutoScrollerStub -> EdgeDraggingAutoScroller (size=0x20)
    // 0x995d80: mov             x3, x0
    // 0x995d84: r0 = Sentinel
    //     0x995d84: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x995d88: stur            x3, [fp, #-0x18]
    // 0x995d8c: ArrayStore: r3[0] = r0  ; List_4
    //     0x995d8c: stur            w0, [x3, #0x17]
    // 0x995d90: r0 = false
    //     0x995d90: add             x0, NULL, #0x30  ; false
    // 0x995d94: StoreField: r3->field_1b = r0
    //     0x995d94: stur            w0, [x3, #0x1b]
    // 0x995d98: ldur            x0, [fp, #-0x10]
    // 0x995d9c: StoreField: r3->field_7 = r0
    //     0x995d9c: stur            w0, [x3, #7]
    // 0x995da0: ldur            x2, [fp, #-8]
    // 0x995da4: r1 = Function '_handleScrollableAutoScrolled@318218688':.
    //     0x995da4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56868] AnonymousClosure: (0x9962fc), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_handleScrollableAutoScrolled (0x996334)
    //     0x995da8: ldr             x1, [x1, #0x868]
    // 0x995dac: r0 = AllocateClosure()
    //     0x995dac: bl              #0xec1630  ; AllocateClosureStub
    // 0x995db0: mov             x1, x0
    // 0x995db4: ldur            x0, [fp, #-0x18]
    // 0x995db8: StoreField: r0->field_b = r1
    //     0x995db8: stur            w1, [x0, #0xb]
    // 0x995dbc: d0 = 50.000000
    //     0x995dbc: ldr             d0, [PP, #0x5ac0]  ; [pp+0x5ac0] IMM: double(50) from 0x4049000000000000
    // 0x995dc0: StoreField: r0->field_f = d0
    //     0x995dc0: stur            d0, [x0, #0xf]
    // 0x995dc4: ldur            x1, [fp, #-8]
    // 0x995dc8: StoreField: r1->field_3b = r0
    //     0x995dc8: stur            w0, [x1, #0x3b]
    //     0x995dcc: ldurb           w16, [x1, #-1]
    //     0x995dd0: ldurb           w17, [x0, #-1]
    //     0x995dd4: and             x16, x17, x16, lsr #2
    //     0x995dd8: tst             x16, HEAP, lsr #32
    //     0x995ddc: b.eq            #0x995de4
    //     0x995de0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x995de4: r0 = Null
    //     0x995de4: mov             x0, NULL
    // 0x995de8: LeaveFrame
    //     0x995de8: mov             SP, fp
    //     0x995dec: ldp             fp, lr, [SP], #0x10
    // 0x995df0: ret
    //     0x995df0: ret             
    // 0x995df4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x995df4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x995df8: b               #0x995c74
    // 0x995dfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x995dfc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x995e00: r0 = NullCastErrorSharedWithFPURegs()
    //     0x995e00: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x995e04: r9 = _scrollable
    //     0x995e04: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0x995e08: ldr             x9, [x9, #0xd10]
    // 0x995e0c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x995e0c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x995e10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x995e10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ cancelReorder(/* No info */) {
    // ** addr: 0x995e24, size: 0x64
    // 0x995e24: EnterFrame
    //     0x995e24: stp             fp, lr, [SP, #-0x10]!
    //     0x995e28: mov             fp, SP
    // 0x995e2c: AllocStack(0x8)
    //     0x995e2c: sub             SP, SP, #8
    // 0x995e30: SetupParameters(SliverReorderableListState this /* r1 => r1, fp-0x8 */)
    //     0x995e30: stur            x1, [fp, #-8]
    // 0x995e34: CheckStackOverflow
    //     0x995e34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x995e38: cmp             SP, x16
    //     0x995e3c: b.ls            #0x995e80
    // 0x995e40: r1 = 1
    //     0x995e40: movz            x1, #0x1
    // 0x995e44: r0 = AllocateContext()
    //     0x995e44: bl              #0xec126c  ; AllocateContextStub
    // 0x995e48: mov             x1, x0
    // 0x995e4c: ldur            x0, [fp, #-8]
    // 0x995e50: StoreField: r1->field_f = r0
    //     0x995e50: stur            w0, [x1, #0xf]
    // 0x995e54: mov             x2, x1
    // 0x995e58: r1 = Function '<anonymous closure>':.
    //     0x995e58: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fe48] AnonymousClosure: (0x995e88), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dropCompleted (0x995ed0)
    //     0x995e5c: ldr             x1, [x1, #0xe48]
    // 0x995e60: r0 = AllocateClosure()
    //     0x995e60: bl              #0xec1630  ; AllocateClosureStub
    // 0x995e64: ldur            x1, [fp, #-8]
    // 0x995e68: mov             x2, x0
    // 0x995e6c: r0 = setState()
    //     0x995e6c: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x995e70: r0 = Null
    //     0x995e70: mov             x0, NULL
    // 0x995e74: LeaveFrame
    //     0x995e74: mov             SP, fp
    //     0x995e78: ldp             fp, lr, [SP], #0x10
    // 0x995e7c: ret
    //     0x995e7c: ret             
    // 0x995e80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x995e80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x995e84: b               #0x995e40
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x995e88, size: 0x48
    // 0x995e88: EnterFrame
    //     0x995e88: stp             fp, lr, [SP, #-0x10]!
    //     0x995e8c: mov             fp, SP
    // 0x995e90: ldr             x0, [fp, #0x10]
    // 0x995e94: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x995e94: ldur            w1, [x0, #0x17]
    // 0x995e98: DecompressPointer r1
    //     0x995e98: add             x1, x1, HEAP, lsl #32
    // 0x995e9c: CheckStackOverflow
    //     0x995e9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x995ea0: cmp             SP, x16
    //     0x995ea4: b.ls            #0x995ec8
    // 0x995ea8: LoadField: r0 = r1->field_f
    //     0x995ea8: ldur            w0, [x1, #0xf]
    // 0x995eac: DecompressPointer r0
    //     0x995eac: add             x0, x0, HEAP, lsl #32
    // 0x995eb0: mov             x1, x0
    // 0x995eb4: r0 = _dragReset()
    //     0x995eb4: bl              #0x995fec  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragReset
    // 0x995eb8: r0 = Null
    //     0x995eb8: mov             x0, NULL
    // 0x995ebc: LeaveFrame
    //     0x995ebc: mov             SP, fp
    //     0x995ec0: ldp             fp, lr, [SP], #0x10
    // 0x995ec4: ret
    //     0x995ec4: ret             
    // 0x995ec8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x995ec8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x995ecc: b               #0x995ea8
  }
  _ _dropCompleted(/* No info */) {
    // ** addr: 0x995ed0, size: 0xe4
    // 0x995ed0: EnterFrame
    //     0x995ed0: stp             fp, lr, [SP, #-0x10]!
    //     0x995ed4: mov             fp, SP
    // 0x995ed8: AllocStack(0x28)
    //     0x995ed8: sub             SP, SP, #0x28
    // 0x995edc: SetupParameters(SliverReorderableListState this /* r1 => r1, fp-0x8 */)
    //     0x995edc: stur            x1, [fp, #-8]
    // 0x995ee0: CheckStackOverflow
    //     0x995ee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x995ee4: cmp             SP, x16
    //     0x995ee8: b.ls            #0x995fa0
    // 0x995eec: r1 = 1
    //     0x995eec: movz            x1, #0x1
    // 0x995ef0: r0 = AllocateContext()
    //     0x995ef0: bl              #0xec126c  ; AllocateContextStub
    // 0x995ef4: mov             x2, x0
    // 0x995ef8: ldur            x1, [fp, #-8]
    // 0x995efc: stur            x2, [fp, #-0x10]
    // 0x995f00: StoreField: r2->field_f = r1
    //     0x995f00: stur            w1, [x2, #0xf]
    // 0x995f04: LoadField: r0 = r1->field_23
    //     0x995f04: ldur            w0, [x1, #0x23]
    // 0x995f08: DecompressPointer r0
    //     0x995f08: add             x0, x0, HEAP, lsl #32
    // 0x995f0c: cmp             w0, NULL
    // 0x995f10: b.eq            #0x995fa8
    // 0x995f14: LoadField: r3 = r1->field_2b
    //     0x995f14: ldur            w3, [x1, #0x2b]
    // 0x995f18: DecompressPointer r3
    //     0x995f18: add             x3, x3, HEAP, lsl #32
    // 0x995f1c: cmp             w3, NULL
    // 0x995f20: b.eq            #0x995fac
    // 0x995f24: r4 = LoadInt32Instr(r0)
    //     0x995f24: sbfx            x4, x0, #1, #0x1f
    //     0x995f28: tbz             w0, #0, #0x995f30
    //     0x995f2c: ldur            x4, [x0, #7]
    // 0x995f30: r5 = LoadInt32Instr(r3)
    //     0x995f30: sbfx            x5, x3, #1, #0x1f
    //     0x995f34: tbz             w3, #0, #0x995f3c
    //     0x995f38: ldur            x5, [x3, #7]
    // 0x995f3c: cmp             x4, x5
    // 0x995f40: b.eq            #0x995f74
    // 0x995f44: LoadField: r4 = r1->field_b
    //     0x995f44: ldur            w4, [x1, #0xb]
    // 0x995f48: DecompressPointer r4
    //     0x995f48: add             x4, x4, HEAP, lsl #32
    // 0x995f4c: cmp             w4, NULL
    // 0x995f50: b.eq            #0x995fb0
    // 0x995f54: LoadField: r5 = r4->field_1b
    //     0x995f54: ldur            w5, [x4, #0x1b]
    // 0x995f58: DecompressPointer r5
    //     0x995f58: add             x5, x5, HEAP, lsl #32
    // 0x995f5c: stp             x0, x5, [SP, #8]
    // 0x995f60: str             x3, [SP]
    // 0x995f64: mov             x0, x5
    // 0x995f68: ClosureCall
    //     0x995f68: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x995f6c: ldur            x2, [x0, #0x1f]
    //     0x995f70: blr             x2
    // 0x995f74: ldur            x2, [fp, #-0x10]
    // 0x995f78: r1 = Function '<anonymous closure>':.
    //     0x995f78: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fd68] AnonymousClosure: (0x995e88), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dropCompleted (0x995ed0)
    //     0x995f7c: ldr             x1, [x1, #0xd68]
    // 0x995f80: r0 = AllocateClosure()
    //     0x995f80: bl              #0xec1630  ; AllocateClosureStub
    // 0x995f84: ldur            x1, [fp, #-8]
    // 0x995f88: mov             x2, x0
    // 0x995f8c: r0 = setState()
    //     0x995f8c: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x995f90: r0 = Null
    //     0x995f90: mov             x0, NULL
    // 0x995f94: LeaveFrame
    //     0x995f94: mov             SP, fp
    //     0x995f98: ldp             fp, lr, [SP], #0x10
    // 0x995f9c: ret
    //     0x995f9c: ret             
    // 0x995fa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x995fa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x995fa4: b               #0x995eec
    // 0x995fa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x995fa8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x995fac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x995fac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x995fb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x995fb0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _dropCompleted(dynamic) {
    // ** addr: 0x995fb4, size: 0x38
    // 0x995fb4: EnterFrame
    //     0x995fb4: stp             fp, lr, [SP, #-0x10]!
    //     0x995fb8: mov             fp, SP
    // 0x995fbc: ldr             x0, [fp, #0x10]
    // 0x995fc0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x995fc0: ldur            w1, [x0, #0x17]
    // 0x995fc4: DecompressPointer r1
    //     0x995fc4: add             x1, x1, HEAP, lsl #32
    // 0x995fc8: CheckStackOverflow
    //     0x995fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x995fcc: cmp             SP, x16
    //     0x995fd0: b.ls            #0x995fe4
    // 0x995fd4: r0 = _dropCompleted()
    //     0x995fd4: bl              #0x995ed0  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dropCompleted
    // 0x995fd8: LeaveFrame
    //     0x995fd8: mov             SP, fp
    //     0x995fdc: ldp             fp, lr, [SP], #0x10
    // 0x995fe0: ret
    //     0x995fe0: ret             
    // 0x995fe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x995fe4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x995fe8: b               #0x995fd4
  }
  _ _dragReset(/* No info */) {
    // ** addr: 0x995fec, size: 0x184
    // 0x995fec: EnterFrame
    //     0x995fec: stp             fp, lr, [SP, #-0x10]!
    //     0x995ff0: mov             fp, SP
    // 0x995ff4: AllocStack(0x10)
    //     0x995ff4: sub             SP, SP, #0x10
    // 0x995ff8: SetupParameters(SliverReorderableListState this /* r1 => r0, fp-0x10 */)
    //     0x995ff8: mov             x0, x1
    //     0x995ffc: stur            x1, [fp, #-0x10]
    // 0x996000: CheckStackOverflow
    //     0x996000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x996004: cmp             SP, x16
    //     0x996008: b.ls            #0x996160
    // 0x99600c: LoadField: r1 = r0->field_27
    //     0x99600c: ldur            w1, [x0, #0x27]
    // 0x996010: DecompressPointer r1
    //     0x996010: add             x1, x1, HEAP, lsl #32
    // 0x996014: cmp             w1, NULL
    // 0x996018: b.eq            #0x996150
    // 0x99601c: LoadField: r2 = r0->field_23
    //     0x99601c: ldur            w2, [x0, #0x23]
    // 0x996020: DecompressPointer r2
    //     0x996020: add             x2, x2, HEAP, lsl #32
    // 0x996024: cmp             w2, NULL
    // 0x996028: b.eq            #0x9960b4
    // 0x99602c: LoadField: r3 = r0->field_1b
    //     0x99602c: ldur            w3, [x0, #0x1b]
    // 0x996030: DecompressPointer r3
    //     0x996030: add             x3, x3, HEAP, lsl #32
    // 0x996034: mov             x1, x3
    // 0x996038: stur            x3, [fp, #-8]
    // 0x99603c: r0 = containsKey()
    //     0x99603c: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x996040: tbnz            w0, #4, #0x9960b0
    // 0x996044: ldur            x0, [fp, #-0x10]
    // 0x996048: ldur            x3, [fp, #-8]
    // 0x99604c: LoadField: r2 = r0->field_23
    //     0x99604c: ldur            w2, [x0, #0x23]
    // 0x996050: DecompressPointer r2
    //     0x996050: add             x2, x2, HEAP, lsl #32
    // 0x996054: cmp             w2, NULL
    // 0x996058: b.eq            #0x996168
    // 0x99605c: mov             x1, x3
    // 0x996060: r0 = _getValueOrData()
    //     0x996060: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x996064: mov             x1, x0
    // 0x996068: ldur            x0, [fp, #-8]
    // 0x99606c: LoadField: r2 = r0->field_f
    //     0x99606c: ldur            w2, [x0, #0xf]
    // 0x996070: DecompressPointer r2
    //     0x996070: add             x2, x2, HEAP, lsl #32
    // 0x996074: cmp             w2, w1
    // 0x996078: b.ne            #0x996084
    // 0x99607c: r2 = Null
    //     0x99607c: mov             x2, NULL
    // 0x996080: b               #0x996088
    // 0x996084: mov             x2, x1
    // 0x996088: ldur            x0, [fp, #-0x10]
    // 0x99608c: r1 = false
    //     0x99608c: add             x1, NULL, #0x30  ; false
    // 0x996090: cmp             w2, NULL
    // 0x996094: b.eq            #0x99616c
    // 0x996098: StoreField: r2->field_23 = r1
    //     0x996098: stur            w1, [x2, #0x23]
    // 0x99609c: mov             x1, x2
    // 0x9960a0: r0 = rebuild()
    //     0x9960a0: bl              #0x945384  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::rebuild
    // 0x9960a4: ldur            x0, [fp, #-0x10]
    // 0x9960a8: StoreField: r0->field_23 = rNULL
    //     0x9960a8: stur            NULL, [x0, #0x23]
    // 0x9960ac: b               #0x9960b4
    // 0x9960b0: ldur            x0, [fp, #-0x10]
    // 0x9960b4: LoadField: r1 = r0->field_27
    //     0x9960b4: ldur            w1, [x0, #0x27]
    // 0x9960b8: DecompressPointer r1
    //     0x9960b8: add             x1, x1, HEAP, lsl #32
    // 0x9960bc: cmp             w1, NULL
    // 0x9960c0: b.eq            #0x9960cc
    // 0x9960c4: r0 = dispose()
    //     0x9960c4: bl              #0x9962b8  ; [package:flutter/src/widgets/reorderable_list.dart] _DragInfo::dispose
    // 0x9960c8: ldur            x0, [fp, #-0x10]
    // 0x9960cc: StoreField: r0->field_27 = rNULL
    //     0x9960cc: stur            NULL, [x0, #0x27]
    // 0x9960d0: LoadField: r1 = r0->field_3b
    //     0x9960d0: ldur            w1, [x0, #0x3b]
    // 0x9960d4: DecompressPointer r1
    //     0x9960d4: add             x1, x1, HEAP, lsl #32
    // 0x9960d8: cmp             w1, NULL
    // 0x9960dc: b.eq            #0x9960e8
    // 0x9960e0: r0 = disallowIndicator()
    //     0x9960e0: bl              #0x995e14  ; [package:flutter/src/widgets/overscroll_indicator.dart] OverscrollIndicatorNotification::disallowIndicator
    // 0x9960e4: ldur            x0, [fp, #-0x10]
    // 0x9960e8: mov             x1, x0
    // 0x9960ec: r0 = _resetItemGap()
    //     0x9960ec: bl              #0x996170  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_resetItemGap
    // 0x9960f0: ldur            x0, [fp, #-0x10]
    // 0x9960f4: LoadField: r1 = r0->field_33
    //     0x9960f4: ldur            w1, [x0, #0x33]
    // 0x9960f8: DecompressPointer r1
    //     0x9960f8: add             x1, x1, HEAP, lsl #32
    // 0x9960fc: cmp             w1, NULL
    // 0x996100: b.eq            #0x99610c
    // 0x996104: r0 = dispose()
    //     0x996104: bl              #0x7f95b4  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::dispose
    // 0x996108: ldur            x0, [fp, #-0x10]
    // 0x99610c: StoreField: r0->field_33 = rNULL
    //     0x99610c: stur            NULL, [x0, #0x33]
    // 0x996110: LoadField: r1 = r0->field_1f
    //     0x996110: ldur            w1, [x0, #0x1f]
    // 0x996114: DecompressPointer r1
    //     0x996114: add             x1, x1, HEAP, lsl #32
    // 0x996118: cmp             w1, NULL
    // 0x99611c: b.eq            #0x996128
    // 0x996120: r0 = remove()
    //     0x996120: bl              #0x64e5d0  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::remove
    // 0x996124: ldur            x0, [fp, #-0x10]
    // 0x996128: LoadField: r1 = r0->field_1f
    //     0x996128: ldur            w1, [x0, #0x1f]
    // 0x99612c: DecompressPointer r1
    //     0x99612c: add             x1, x1, HEAP, lsl #32
    // 0x996130: cmp             w1, NULL
    // 0x996134: b.ne            #0x996140
    // 0x996138: mov             x1, x0
    // 0x99613c: b               #0x996148
    // 0x996140: r0 = dispose()
    //     0x996140: bl              #0x6a4cd8  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::dispose
    // 0x996144: ldur            x1, [fp, #-0x10]
    // 0x996148: StoreField: r1->field_1f = rNULL
    //     0x996148: stur            NULL, [x1, #0x1f]
    // 0x99614c: StoreField: r1->field_2f = rNULL
    //     0x99614c: stur            NULL, [x1, #0x2f]
    // 0x996150: r0 = Null
    //     0x996150: mov             x0, NULL
    // 0x996154: LeaveFrame
    //     0x996154: mov             SP, fp
    //     0x996158: ldp             fp, lr, [SP], #0x10
    // 0x99615c: ret
    //     0x99615c: ret             
    // 0x996160: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x996160: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x996164: b               #0x99600c
    // 0x996168: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x996168: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99616c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99616c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _resetItemGap(/* No info */) {
    // ** addr: 0x996170, size: 0x148
    // 0x996170: EnterFrame
    //     0x996170: stp             fp, lr, [SP, #-0x10]!
    //     0x996174: mov             fp, SP
    // 0x996178: AllocStack(0x18)
    //     0x996178: sub             SP, SP, #0x18
    // 0x99617c: CheckStackOverflow
    //     0x99617c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x996180: cmp             SP, x16
    //     0x996184: b.ls            #0x9962a8
    // 0x996188: LoadField: r0 = r1->field_1b
    //     0x996188: ldur            w0, [x1, #0x1b]
    // 0x99618c: DecompressPointer r0
    //     0x99618c: add             x0, x0, HEAP, lsl #32
    // 0x996190: stur            x0, [fp, #-8]
    // 0x996194: LoadField: r2 = r0->field_7
    //     0x996194: ldur            w2, [x0, #7]
    // 0x996198: DecompressPointer r2
    //     0x996198: add             x2, x2, HEAP, lsl #32
    // 0x99619c: r1 = Null
    //     0x99619c: mov             x1, NULL
    // 0x9961a0: r3 = <X1>
    //     0x9961a0: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x9961a4: r0 = Null
    //     0x9961a4: mov             x0, NULL
    // 0x9961a8: cmp             x2, x0
    // 0x9961ac: b.eq            #0x9961bc
    // 0x9961b0: r30 = InstantiateTypeArgumentsStub
    //     0x9961b0: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x9961b4: LoadField: r30 = r30->field_7
    //     0x9961b4: ldur            lr, [lr, #7]
    // 0x9961b8: blr             lr
    // 0x9961bc: mov             x1, x0
    // 0x9961c0: r0 = _CompactIterable()
    //     0x9961c0: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x9961c4: mov             x1, x0
    // 0x9961c8: ldur            x0, [fp, #-8]
    // 0x9961cc: StoreField: r1->field_b = r0
    //     0x9961cc: stur            w0, [x1, #0xb]
    // 0x9961d0: r0 = -1
    //     0x9961d0: movn            x0, #0
    // 0x9961d4: StoreField: r1->field_f = r0
    //     0x9961d4: stur            x0, [x1, #0xf]
    // 0x9961d8: r0 = 2
    //     0x9961d8: movz            x0, #0x2
    // 0x9961dc: ArrayStore: r1[0] = r0  ; List_8
    //     0x9961dc: stur            x0, [x1, #0x17]
    // 0x9961e0: r0 = iterator()
    //     0x9961e0: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x9961e4: stur            x0, [fp, #-0x10]
    // 0x9961e8: LoadField: r2 = r0->field_7
    //     0x9961e8: ldur            w2, [x0, #7]
    // 0x9961ec: DecompressPointer r2
    //     0x9961ec: add             x2, x2, HEAP, lsl #32
    // 0x9961f0: stur            x2, [fp, #-8]
    // 0x9961f4: CheckStackOverflow
    //     0x9961f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9961f8: cmp             SP, x16
    //     0x9961fc: b.ls            #0x9962b0
    // 0x996200: mov             x1, x0
    // 0x996204: r0 = moveNext()
    //     0x996204: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x996208: tbnz            w0, #4, #0x996298
    // 0x99620c: ldur            x3, [fp, #-0x10]
    // 0x996210: LoadField: r4 = r3->field_33
    //     0x996210: ldur            w4, [x3, #0x33]
    // 0x996214: DecompressPointer r4
    //     0x996214: add             x4, x4, HEAP, lsl #32
    // 0x996218: stur            x4, [fp, #-0x18]
    // 0x99621c: cmp             w4, NULL
    // 0x996220: b.ne            #0x996254
    // 0x996224: mov             x0, x4
    // 0x996228: ldur            x2, [fp, #-8]
    // 0x99622c: r1 = Null
    //     0x99622c: mov             x1, NULL
    // 0x996230: cmp             w2, NULL
    // 0x996234: b.eq            #0x996254
    // 0x996238: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x996238: ldur            w4, [x2, #0x17]
    // 0x99623c: DecompressPointer r4
    //     0x99623c: add             x4, x4, HEAP, lsl #32
    // 0x996240: r8 = X0
    //     0x996240: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x996244: LoadField: r9 = r4->field_7
    //     0x996244: ldur            x9, [x4, #7]
    // 0x996248: r3 = Null
    //     0x996248: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fd70] Null
    //     0x99624c: ldr             x3, [x3, #0xd70]
    // 0x996250: blr             x9
    // 0x996254: ldur            x0, [fp, #-0x18]
    // 0x996258: LoadField: r1 = r0->field_1f
    //     0x996258: ldur            w1, [x0, #0x1f]
    // 0x99625c: DecompressPointer r1
    //     0x99625c: add             x1, x1, HEAP, lsl #32
    // 0x996260: cmp             w1, NULL
    // 0x996264: b.eq            #0x996278
    // 0x996268: r0 = dispose()
    //     0x996268: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0x99626c: ldur            x1, [fp, #-0x18]
    // 0x996270: StoreField: r1->field_1f = rNULL
    //     0x996270: stur            NULL, [x1, #0x1f]
    // 0x996274: b               #0x99627c
    // 0x996278: mov             x1, x0
    // 0x99627c: r0 = Instance_Offset
    //     0x99627c: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x996280: ArrayStore: r1[0] = r0  ; List_4
    //     0x996280: stur            w0, [x1, #0x17]
    // 0x996284: StoreField: r1->field_1b = r0
    //     0x996284: stur            w0, [x1, #0x1b]
    // 0x996288: r0 = rebuild()
    //     0x996288: bl              #0x945384  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::rebuild
    // 0x99628c: ldur            x0, [fp, #-0x10]
    // 0x996290: ldur            x2, [fp, #-8]
    // 0x996294: b               #0x9961f4
    // 0x996298: r0 = Null
    //     0x996298: mov             x0, NULL
    // 0x99629c: LeaveFrame
    //     0x99629c: mov             SP, fp
    //     0x9962a0: ldp             fp, lr, [SP], #0x10
    // 0x9962a4: ret
    //     0x9962a4: ret             
    // 0x9962a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9962a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9962ac: b               #0x996188
    // 0x9962b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9962b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9962b4: b               #0x996200
  }
  [closure] void _handleScrollableAutoScrolled(dynamic) {
    // ** addr: 0x9962fc, size: 0x38
    // 0x9962fc: EnterFrame
    //     0x9962fc: stp             fp, lr, [SP, #-0x10]!
    //     0x996300: mov             fp, SP
    // 0x996304: ldr             x0, [fp, #0x10]
    // 0x996308: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x996308: ldur            w1, [x0, #0x17]
    // 0x99630c: DecompressPointer r1
    //     0x99630c: add             x1, x1, HEAP, lsl #32
    // 0x996310: CheckStackOverflow
    //     0x996310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x996314: cmp             SP, x16
    //     0x996318: b.ls            #0x99632c
    // 0x99631c: r0 = _handleScrollableAutoScrolled()
    //     0x99631c: bl              #0x996334  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_handleScrollableAutoScrolled
    // 0x996320: LeaveFrame
    //     0x996320: mov             SP, fp
    //     0x996324: ldp             fp, lr, [SP], #0x10
    // 0x996328: ret
    //     0x996328: ret             
    // 0x99632c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99632c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x996330: b               #0x99631c
  }
  _ _handleScrollableAutoScrolled(/* No info */) {
    // ** addr: 0x996334, size: 0x88
    // 0x996334: EnterFrame
    //     0x996334: stp             fp, lr, [SP, #-0x10]!
    //     0x996338: mov             fp, SP
    // 0x99633c: AllocStack(0x10)
    //     0x99633c: sub             SP, SP, #0x10
    // 0x996340: SetupParameters(SliverReorderableListState this /* r1 => r0, fp-0x8 */)
    //     0x996340: mov             x0, x1
    //     0x996344: stur            x1, [fp, #-8]
    // 0x996348: CheckStackOverflow
    //     0x996348: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99634c: cmp             SP, x16
    //     0x996350: b.ls            #0x9963b4
    // 0x996354: LoadField: r1 = r0->field_27
    //     0x996354: ldur            w1, [x0, #0x27]
    // 0x996358: DecompressPointer r1
    //     0x996358: add             x1, x1, HEAP, lsl #32
    // 0x99635c: cmp             w1, NULL
    // 0x996360: b.ne            #0x996374
    // 0x996364: r0 = Null
    //     0x996364: mov             x0, NULL
    // 0x996368: LeaveFrame
    //     0x996368: mov             SP, fp
    //     0x99636c: ldp             fp, lr, [SP], #0x10
    // 0x996370: ret
    //     0x996370: ret             
    // 0x996374: mov             x1, x0
    // 0x996378: r0 = _dragUpdateItems()
    //     0x996378: bl              #0x997330  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragUpdateItems
    // 0x99637c: ldur            x1, [fp, #-8]
    // 0x996380: LoadField: r0 = r1->field_3b
    //     0x996380: ldur            w0, [x1, #0x3b]
    // 0x996384: DecompressPointer r0
    //     0x996384: add             x0, x0, HEAP, lsl #32
    // 0x996388: stur            x0, [fp, #-0x10]
    // 0x99638c: cmp             w0, NULL
    // 0x996390: b.eq            #0x9963a4
    // 0x996394: r0 = _dragTargetRect()
    //     0x996394: bl              #0x997218  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragTargetRect
    // 0x996398: ldur            x1, [fp, #-0x10]
    // 0x99639c: mov             x2, x0
    // 0x9963a0: r0 = startAutoScrollIfNecessary()
    //     0x9963a0: bl              #0x9963bc  ; [package:flutter/src/widgets/scrollable_helpers.dart] EdgeDraggingAutoScroller::startAutoScrollIfNecessary
    // 0x9963a4: r0 = Null
    //     0x9963a4: mov             x0, NULL
    // 0x9963a8: LeaveFrame
    //     0x9963a8: mov             SP, fp
    //     0x9963ac: ldp             fp, lr, [SP], #0x10
    // 0x9963b0: ret
    //     0x9963b0: ret             
    // 0x9963b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9963b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9963b8: b               #0x996354
  }
  get _ _dragTargetRect(/* No info */) {
    // ** addr: 0x997218, size: 0x118
    // 0x997218: EnterFrame
    //     0x997218: stp             fp, lr, [SP, #-0x10]!
    //     0x99721c: mov             fp, SP
    // 0x997220: AllocStack(0x28)
    //     0x997220: sub             SP, SP, #0x28
    // 0x997224: SetupParameters(SliverReorderableListState this /* r1 => r0, fp-0x8 */)
    //     0x997224: mov             x0, x1
    //     0x997228: stur            x1, [fp, #-8]
    // 0x99722c: CheckStackOverflow
    //     0x99722c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x997230: cmp             SP, x16
    //     0x997234: b.ls            #0x9972fc
    // 0x997238: LoadField: r1 = r0->field_27
    //     0x997238: ldur            w1, [x0, #0x27]
    // 0x99723c: DecompressPointer r1
    //     0x99723c: add             x1, x1, HEAP, lsl #32
    // 0x997240: cmp             w1, NULL
    // 0x997244: b.eq            #0x997304
    // 0x997248: LoadField: r2 = r1->field_2f
    //     0x997248: ldur            w2, [x1, #0x2f]
    // 0x99724c: DecompressPointer r2
    //     0x99724c: add             x2, x2, HEAP, lsl #32
    // 0x997250: r16 = Sentinel
    //     0x997250: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x997254: cmp             w2, w16
    // 0x997258: b.eq            #0x997308
    // 0x99725c: LoadField: r3 = r1->field_33
    //     0x99725c: ldur            w3, [x1, #0x33]
    // 0x997260: DecompressPointer r3
    //     0x997260: add             x3, x3, HEAP, lsl #32
    // 0x997264: r16 = Sentinel
    //     0x997264: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x997268: cmp             w3, w16
    // 0x99726c: b.eq            #0x997314
    // 0x997270: mov             x1, x2
    // 0x997274: mov             x2, x3
    // 0x997278: r0 = -()
    //     0x997278: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x99727c: LoadField: d0 = r0->field_7
    //     0x99727c: ldur            d0, [x0, #7]
    // 0x997280: stur            d0, [fp, #-0x28]
    // 0x997284: LoadField: d1 = r0->field_f
    //     0x997284: ldur            d1, [x0, #0xf]
    // 0x997288: ldur            x0, [fp, #-8]
    // 0x99728c: stur            d1, [fp, #-0x20]
    // 0x997290: LoadField: r1 = r0->field_27
    //     0x997290: ldur            w1, [x0, #0x27]
    // 0x997294: DecompressPointer r1
    //     0x997294: add             x1, x1, HEAP, lsl #32
    // 0x997298: cmp             w1, NULL
    // 0x99729c: b.eq            #0x997320
    // 0x9972a0: LoadField: r0 = r1->field_37
    //     0x9972a0: ldur            w0, [x1, #0x37]
    // 0x9972a4: DecompressPointer r0
    //     0x9972a4: add             x0, x0, HEAP, lsl #32
    // 0x9972a8: r16 = Sentinel
    //     0x9972a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9972ac: cmp             w0, w16
    // 0x9972b0: b.eq            #0x997324
    // 0x9972b4: LoadField: d2 = r0->field_7
    //     0x9972b4: ldur            d2, [x0, #7]
    // 0x9972b8: LoadField: d3 = r0->field_f
    //     0x9972b8: ldur            d3, [x0, #0xf]
    // 0x9972bc: fadd            d4, d0, d2
    // 0x9972c0: stur            d4, [fp, #-0x18]
    // 0x9972c4: fadd            d2, d1, d3
    // 0x9972c8: stur            d2, [fp, #-0x10]
    // 0x9972cc: r0 = Rect()
    //     0x9972cc: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x9972d0: ldur            d0, [fp, #-0x28]
    // 0x9972d4: StoreField: r0->field_7 = d0
    //     0x9972d4: stur            d0, [x0, #7]
    // 0x9972d8: ldur            d0, [fp, #-0x20]
    // 0x9972dc: StoreField: r0->field_f = d0
    //     0x9972dc: stur            d0, [x0, #0xf]
    // 0x9972e0: ldur            d0, [fp, #-0x18]
    // 0x9972e4: ArrayStore: r0[0] = d0  ; List_8
    //     0x9972e4: stur            d0, [x0, #0x17]
    // 0x9972e8: ldur            d0, [fp, #-0x10]
    // 0x9972ec: StoreField: r0->field_1f = d0
    //     0x9972ec: stur            d0, [x0, #0x1f]
    // 0x9972f0: LeaveFrame
    //     0x9972f0: mov             SP, fp
    //     0x9972f4: ldp             fp, lr, [SP], #0x10
    // 0x9972f8: ret
    //     0x9972f8: ret             
    // 0x9972fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9972fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x997300: b               #0x997238
    // 0x997304: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997304: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997308: r9 = dragPosition
    //     0x997308: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd40] Field <<EMAIL>>: late (offset: 0x30)
    //     0x99730c: ldr             x9, [x9, #0xd40]
    // 0x997310: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x997310: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x997314: r9 = dragOffset
    //     0x997314: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd48] Field <<EMAIL>>: late (offset: 0x34)
    //     0x997318: ldr             x9, [x9, #0xd48]
    // 0x99731c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x99731c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x997320: r0 = NullCastErrorSharedWithFPURegs()
    //     0x997320: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x997324: r9 = itemSize
    //     0x997324: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd30] Field <<EMAIL>>: late (offset: 0x38)
    //     0x997328: ldr             x9, [x9, #0xd30]
    // 0x99732c: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x99732c: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
  }
  _ _dragUpdateItems(/* No info */) {
    // ** addr: 0x997330, size: 0x8f4
    // 0x997330: EnterFrame
    //     0x997330: stp             fp, lr, [SP, #-0x10]!
    //     0x997334: mov             fp, SP
    // 0x997338: AllocStack(0x58)
    //     0x997338: sub             SP, SP, #0x58
    // 0x99733c: SetupParameters(SliverReorderableListState this /* r1 => r0, fp-0x10 */)
    //     0x99733c: mov             x0, x1
    //     0x997340: stur            x1, [fp, #-0x10]
    // 0x997344: CheckStackOverflow
    //     0x997344: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x997348: cmp             SP, x16
    //     0x99734c: b.ls            #0x997b8c
    // 0x997350: LoadField: r1 = r0->field_27
    //     0x997350: ldur            w1, [x0, #0x27]
    // 0x997354: DecompressPointer r1
    //     0x997354: add             x1, x1, HEAP, lsl #32
    // 0x997358: cmp             w1, NULL
    // 0x99735c: b.eq            #0x997b94
    // 0x997360: LoadField: r3 = r1->field_3f
    //     0x997360: ldur            w3, [x1, #0x3f]
    // 0x997364: DecompressPointer r3
    //     0x997364: add             x3, x3, HEAP, lsl #32
    // 0x997368: r16 = Sentinel
    //     0x997368: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x99736c: cmp             w3, w16
    // 0x997370: b.eq            #0x997b98
    // 0x997374: stur            x3, [fp, #-8]
    // 0x997378: LoadField: r2 = r1->field_2f
    //     0x997378: ldur            w2, [x1, #0x2f]
    // 0x99737c: DecompressPointer r2
    //     0x99737c: add             x2, x2, HEAP, lsl #32
    // 0x997380: r16 = Sentinel
    //     0x997380: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x997384: cmp             w2, w16
    // 0x997388: b.eq            #0x997ba4
    // 0x99738c: LoadField: r4 = r1->field_33
    //     0x99738c: ldur            w4, [x1, #0x33]
    // 0x997390: DecompressPointer r4
    //     0x997390: add             x4, x4, HEAP, lsl #32
    // 0x997394: r16 = Sentinel
    //     0x997394: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x997398: cmp             w4, w16
    // 0x99739c: b.eq            #0x997bb0
    // 0x9973a0: mov             x1, x2
    // 0x9973a4: mov             x2, x4
    // 0x9973a8: r0 = -()
    //     0x9973a8: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x9973ac: mov             x1, x0
    // 0x9973b0: ldur            x0, [fp, #-0x10]
    // 0x9973b4: LoadField: r2 = r0->field_3f
    //     0x9973b4: ldur            w2, [x0, #0x3f]
    // 0x9973b8: DecompressPointer r2
    //     0x9973b8: add             x2, x2, HEAP, lsl #32
    // 0x9973bc: r16 = Sentinel
    //     0x9973bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9973c0: cmp             w2, w16
    // 0x9973c4: b.eq            #0x997bbc
    // 0x9973c8: LoadField: r3 = r2->field_b
    //     0x9973c8: ldur            w3, [x2, #0xb]
    // 0x9973cc: DecompressPointer r3
    //     0x9973cc: add             x3, x3, HEAP, lsl #32
    // 0x9973d0: cmp             w3, NULL
    // 0x9973d4: b.eq            #0x997bc8
    // 0x9973d8: LoadField: r2 = r3->field_b
    //     0x9973d8: ldur            w2, [x3, #0xb]
    // 0x9973dc: DecompressPointer r2
    //     0x9973dc: add             x2, x2, HEAP, lsl #32
    // 0x9973e0: r16 = Instance_AxisDirection
    //     0x9973e0: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0x9973e4: cmp             w2, w16
    // 0x9973e8: b.eq            #0x9973f8
    // 0x9973ec: r16 = Instance_AxisDirection
    //     0x9973ec: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0x9973f0: cmp             w2, w16
    // 0x9973f4: b.ne            #0x997400
    // 0x9973f8: r2 = Instance_Axis
    //     0x9973f8: ldr             x2, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x9973fc: b               #0x997424
    // 0x997400: r16 = Instance_AxisDirection
    //     0x997400: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0x997404: cmp             w2, w16
    // 0x997408: b.eq            #0x997418
    // 0x99740c: r16 = Instance_AxisDirection
    //     0x99740c: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0x997410: cmp             w2, w16
    // 0x997414: b.ne            #0x997420
    // 0x997418: r2 = Instance_Axis
    //     0x997418: ldr             x2, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x99741c: b               #0x997424
    // 0x997420: r2 = Null
    //     0x997420: mov             x2, NULL
    // 0x997424: ldur            x3, [fp, #-8]
    // 0x997428: r0 = _sizeExtent()
    //     0x997428: bl              #0x997cf0  ; [package:flutter/src/widgets/reorderable_list.dart] ::_sizeExtent
    // 0x99742c: ldur            x0, [fp, #-8]
    // 0x997430: stur            d0, [fp, #-0x58]
    // 0x997434: LoadField: d1 = r0->field_7
    //     0x997434: ldur            d1, [x0, #7]
    // 0x997438: stur            d1, [fp, #-0x50]
    // 0x99743c: fadd            d2, d0, d1
    // 0x997440: ldur            x0, [fp, #-0x10]
    // 0x997444: stur            d2, [fp, #-0x48]
    // 0x997448: LoadField: r4 = r0->field_2b
    //     0x997448: ldur            w4, [x0, #0x2b]
    // 0x99744c: DecompressPointer r4
    //     0x99744c: add             x4, x4, HEAP, lsl #32
    // 0x997450: stur            x4, [fp, #-0x20]
    // 0x997454: cmp             w4, NULL
    // 0x997458: b.eq            #0x997bcc
    // 0x99745c: LoadField: r5 = r0->field_1b
    //     0x99745c: ldur            w5, [x0, #0x1b]
    // 0x997460: DecompressPointer r5
    //     0x997460: add             x5, x5, HEAP, lsl #32
    // 0x997464: stur            x5, [fp, #-0x18]
    // 0x997468: LoadField: r6 = r5->field_7
    //     0x997468: ldur            w6, [x5, #7]
    // 0x99746c: DecompressPointer r6
    //     0x99746c: add             x6, x6, HEAP, lsl #32
    // 0x997470: mov             x2, x6
    // 0x997474: stur            x6, [fp, #-8]
    // 0x997478: r1 = Null
    //     0x997478: mov             x1, NULL
    // 0x99747c: r3 = <X1>
    //     0x99747c: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x997480: r0 = Null
    //     0x997480: mov             x0, NULL
    // 0x997484: cmp             x2, x0
    // 0x997488: b.eq            #0x997498
    // 0x99748c: r30 = InstantiateTypeArgumentsStub
    //     0x99748c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x997490: LoadField: r30 = r30->field_7
    //     0x997490: ldur            lr, [lr, #7]
    // 0x997494: blr             lr
    // 0x997498: mov             x1, x0
    // 0x99749c: r0 = _CompactIterable()
    //     0x99749c: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x9974a0: mov             x1, x0
    // 0x9974a4: ldur            x0, [fp, #-0x18]
    // 0x9974a8: StoreField: r1->field_b = r0
    //     0x9974a8: stur            w0, [x1, #0xb]
    // 0x9974ac: r2 = -1
    //     0x9974ac: movn            x2, #0
    // 0x9974b0: StoreField: r1->field_f = r2
    //     0x9974b0: stur            x2, [x1, #0xf]
    // 0x9974b4: r3 = 2
    //     0x9974b4: movz            x3, #0x2
    // 0x9974b8: ArrayStore: r1[0] = r3  ; List_8
    //     0x9974b8: stur            x3, [x1, #0x17]
    // 0x9974bc: r0 = iterator()
    //     0x9974bc: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x9974c0: mov             x2, x0
    // 0x9974c4: ldur            x0, [fp, #-0x20]
    // 0x9974c8: stur            x2, [fp, #-0x30]
    // 0x9974cc: r1 = LoadInt32Instr(r0)
    //     0x9974cc: sbfx            x1, x0, #1, #0x1f
    //     0x9974d0: tbz             w0, #0, #0x9974d8
    //     0x9974d4: ldur            x1, [x0, #7]
    // 0x9974d8: LoadField: r0 = r2->field_7
    //     0x9974d8: ldur            w0, [x2, #7]
    // 0x9974dc: DecompressPointer r0
    //     0x9974dc: add             x0, x0, HEAP, lsl #32
    // 0x9974e0: stur            x0, [fp, #-0x20]
    // 0x9974e4: mov             x4, x1
    // 0x9974e8: ldur            x3, [fp, #-0x10]
    // 0x9974ec: ldur            d0, [fp, #-0x58]
    // 0x9974f0: ldur            d1, [fp, #-0x48]
    // 0x9974f4: stur            x4, [fp, #-0x28]
    // 0x9974f8: CheckStackOverflow
    //     0x9974f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9974fc: cmp             SP, x16
    //     0x997500: b.ls            #0x997bd0
    // 0x997504: mov             x1, x2
    // 0x997508: r0 = moveNext()
    //     0x997508: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x99750c: tbnz            w0, #4, #0x99794c
    // 0x997510: ldur            x3, [fp, #-0x30]
    // 0x997514: LoadField: r4 = r3->field_33
    //     0x997514: ldur            w4, [x3, #0x33]
    // 0x997518: DecompressPointer r4
    //     0x997518: add             x4, x4, HEAP, lsl #32
    // 0x99751c: stur            x4, [fp, #-0x38]
    // 0x997520: cmp             w4, NULL
    // 0x997524: b.ne            #0x997558
    // 0x997528: mov             x0, x4
    // 0x99752c: ldur            x2, [fp, #-0x20]
    // 0x997530: r1 = Null
    //     0x997530: mov             x1, NULL
    // 0x997534: cmp             w2, NULL
    // 0x997538: b.eq            #0x997558
    // 0x99753c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x99753c: ldur            w4, [x2, #0x17]
    // 0x997540: DecompressPointer r4
    //     0x997540: add             x4, x4, HEAP, lsl #32
    // 0x997544: r8 = X0
    //     0x997544: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x997548: LoadField: r9 = r4->field_7
    //     0x997548: ldur            x9, [x4, #7]
    // 0x99754c: r3 = Null
    //     0x99754c: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fdc0] Null
    //     0x997550: ldr             x3, [x3, #0xdc0]
    // 0x997554: blr             x9
    // 0x997558: ldur            x0, [fp, #-0x10]
    // 0x99755c: LoadField: r1 = r0->field_3f
    //     0x99755c: ldur            w1, [x0, #0x3f]
    // 0x997560: DecompressPointer r1
    //     0x997560: add             x1, x1, HEAP, lsl #32
    // 0x997564: LoadField: r2 = r1->field_b
    //     0x997564: ldur            w2, [x1, #0xb]
    // 0x997568: DecompressPointer r2
    //     0x997568: add             x2, x2, HEAP, lsl #32
    // 0x99756c: cmp             w2, NULL
    // 0x997570: b.eq            #0x997bd8
    // 0x997574: LoadField: r1 = r2->field_b
    //     0x997574: ldur            w1, [x2, #0xb]
    // 0x997578: DecompressPointer r1
    //     0x997578: add             x1, x1, HEAP, lsl #32
    // 0x99757c: r16 = Instance_AxisDirection
    //     0x99757c: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0x997580: cmp             w1, w16
    // 0x997584: b.eq            #0x997594
    // 0x997588: r16 = Instance_AxisDirection
    //     0x997588: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0x99758c: cmp             w1, w16
    // 0x997590: b.ne            #0x9975d4
    // 0x997594: ldur            x2, [fp, #-0x38]
    // 0x997598: LoadField: r1 = r2->field_b
    //     0x997598: ldur            w1, [x2, #0xb]
    // 0x99759c: DecompressPointer r1
    //     0x99759c: add             x1, x1, HEAP, lsl #32
    // 0x9975a0: cmp             w1, NULL
    // 0x9975a4: b.eq            #0x997bdc
    // 0x9975a8: LoadField: r3 = r1->field_b
    //     0x9975a8: ldur            x3, [x1, #0xb]
    // 0x9975ac: LoadField: r1 = r0->field_23
    //     0x9975ac: ldur            w1, [x0, #0x23]
    // 0x9975b0: DecompressPointer r1
    //     0x9975b0: add             x1, x1, HEAP, lsl #32
    // 0x9975b4: cmp             w1, NULL
    // 0x9975b8: b.eq            #0x997be0
    // 0x9975bc: r4 = LoadInt32Instr(r1)
    //     0x9975bc: sbfx            x4, x1, #1, #0x1f
    //     0x9975c0: tbz             w1, #0, #0x9975c8
    //     0x9975c4: ldur            x4, [x1, #7]
    // 0x9975c8: cmp             x3, x4
    // 0x9975cc: b.ne            #0x9975f0
    // 0x9975d0: b               #0x997600
    // 0x9975d4: ldur            x2, [fp, #-0x38]
    // 0x9975d8: r16 = Instance_AxisDirection
    //     0x9975d8: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0x9975dc: cmp             w1, w16
    // 0x9975e0: b.eq            #0x9975f0
    // 0x9975e4: r16 = Instance_AxisDirection
    //     0x9975e4: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0x9975e8: cmp             w1, w16
    // 0x9975ec: b.eq            #0x9975f0
    // 0x9975f0: LoadField: r1 = r2->field_f
    //     0x9975f0: ldur            w1, [x2, #0xf]
    // 0x9975f4: DecompressPointer r1
    //     0x9975f4: add             x1, x1, HEAP, lsl #32
    // 0x9975f8: cmp             w1, NULL
    // 0x9975fc: b.ne            #0x997618
    // 0x997600: ldur            x4, [fp, #-0x28]
    // 0x997604: mov             x5, x0
    // 0x997608: ldur            d5, [fp, #-0x58]
    // 0x99760c: ldur            d4, [fp, #-0x48]
    // 0x997610: d1 = 2.000000
    //     0x997610: fmov            d1, #2.00000000
    // 0x997614: b               #0x997934
    // 0x997618: r0 = renderObject()
    //     0x997618: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0x99761c: mov             x3, x0
    // 0x997620: stur            x3, [fp, #-0x40]
    // 0x997624: cmp             w3, NULL
    // 0x997628: b.eq            #0x997be4
    // 0x99762c: mov             x0, x3
    // 0x997630: r2 = Null
    //     0x997630: mov             x2, NULL
    // 0x997634: r1 = Null
    //     0x997634: mov             x1, NULL
    // 0x997638: r4 = LoadClassIdInstr(r0)
    //     0x997638: ldur            x4, [x0, #-1]
    //     0x99763c: ubfx            x4, x4, #0xc, #0x14
    // 0x997640: sub             x4, x4, #0xbba
    // 0x997644: cmp             x4, #0x9a
    // 0x997648: b.ls            #0x99765c
    // 0x99764c: r8 = RenderBox
    //     0x99764c: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x997650: r3 = Null
    //     0x997650: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fdd0] Null
    //     0x997654: ldr             x3, [x3, #0xdd0]
    // 0x997658: r0 = RenderBox()
    //     0x997658: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x99765c: ldur            x1, [fp, #-0x40]
    // 0x997660: r2 = Instance_Offset
    //     0x997660: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x997664: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x997664: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x997668: r0 = localToGlobal()
    //     0x997668: bl              #0x67fb20  ; [package:flutter/src/rendering/box.dart] RenderBox::localToGlobal
    // 0x99766c: mov             x1, x0
    // 0x997670: ldur            x0, [fp, #-0x38]
    // 0x997674: LoadField: r2 = r0->field_1b
    //     0x997674: ldur            w2, [x0, #0x1b]
    // 0x997678: DecompressPointer r2
    //     0x997678: add             x2, x2, HEAP, lsl #32
    // 0x99767c: r0 = +()
    //     0x99767c: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x997680: ldur            x1, [fp, #-0x40]
    // 0x997684: stur            x0, [fp, #-0x40]
    // 0x997688: r0 = size()
    //     0x997688: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x99768c: ldur            x1, [fp, #-0x40]
    // 0x997690: mov             x2, x0
    // 0x997694: r0 = &()
    //     0x997694: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x997698: ldur            x5, [fp, #-0x10]
    // 0x99769c: LoadField: r1 = r5->field_3f
    //     0x99769c: ldur            w1, [x5, #0x3f]
    // 0x9976a0: DecompressPointer r1
    //     0x9976a0: add             x1, x1, HEAP, lsl #32
    // 0x9976a4: LoadField: r2 = r1->field_b
    //     0x9976a4: ldur            w2, [x1, #0xb]
    // 0x9976a8: DecompressPointer r2
    //     0x9976a8: add             x2, x2, HEAP, lsl #32
    // 0x9976ac: cmp             w2, NULL
    // 0x9976b0: b.eq            #0x997be8
    // 0x9976b4: LoadField: r1 = r2->field_b
    //     0x9976b4: ldur            w1, [x2, #0xb]
    // 0x9976b8: DecompressPointer r1
    //     0x9976b8: add             x1, x1, HEAP, lsl #32
    // 0x9976bc: r16 = Instance_AxisDirection
    //     0x9976bc: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0x9976c0: cmp             w1, w16
    // 0x9976c4: b.eq            #0x9976d4
    // 0x9976c8: r16 = Instance_AxisDirection
    //     0x9976c8: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0x9976cc: cmp             w1, w16
    // 0x9976d0: b.ne            #0x9976dc
    // 0x9976d4: LoadField: d0 = r0->field_f
    //     0x9976d4: ldur            d0, [x0, #0xf]
    // 0x9976d8: b               #0x9976f8
    // 0x9976dc: r16 = Instance_AxisDirection
    //     0x9976dc: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0x9976e0: cmp             w1, w16
    // 0x9976e4: b.eq            #0x9976f4
    // 0x9976e8: r16 = Instance_AxisDirection
    //     0x9976e8: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0x9976ec: cmp             w1, w16
    // 0x9976f0: b.eq            #0x9976f4
    // 0x9976f4: LoadField: d0 = r0->field_7
    //     0x9976f4: ldur            d0, [x0, #7]
    // 0x9976f8: r16 = Instance_AxisDirection
    //     0x9976f8: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0x9976fc: cmp             w1, w16
    // 0x997700: b.eq            #0x997710
    // 0x997704: r16 = Instance_AxisDirection
    //     0x997704: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0x997708: cmp             w1, w16
    // 0x99770c: b.ne            #0x997724
    // 0x997710: LoadField: d1 = r0->field_1f
    //     0x997710: ldur            d1, [x0, #0x1f]
    // 0x997714: LoadField: d2 = r0->field_f
    //     0x997714: ldur            d2, [x0, #0xf]
    // 0x997718: fsub            d3, d1, d2
    // 0x99771c: mov             v2.16b, v3.16b
    // 0x997720: b               #0x99774c
    // 0x997724: r16 = Instance_AxisDirection
    //     0x997724: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0x997728: cmp             w1, w16
    // 0x99772c: b.eq            #0x99773c
    // 0x997730: r16 = Instance_AxisDirection
    //     0x997730: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0x997734: cmp             w1, w16
    // 0x997738: b.eq            #0x99773c
    // 0x99773c: ArrayLoad: d1 = r0[0]  ; List_8
    //     0x99773c: ldur            d1, [x0, #0x17]
    // 0x997740: LoadField: d2 = r0->field_7
    //     0x997740: ldur            d2, [x0, #7]
    // 0x997744: fsub            d3, d1, d2
    // 0x997748: mov             v2.16b, v3.16b
    // 0x99774c: d1 = 2.000000
    //     0x99774c: fmov            d1, #2.00000000
    // 0x997750: fadd            d3, d0, d2
    // 0x997754: fdiv            d4, d2, d1
    // 0x997758: fadd            d2, d0, d4
    // 0x99775c: r16 = Instance_AxisDirection
    //     0x99775c: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0x997760: cmp             w1, w16
    // 0x997764: b.eq            #0x997774
    // 0x997768: r16 = Instance_AxisDirection
    //     0x997768: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0x99776c: cmp             w1, w16
    // 0x997770: b.ne            #0x99784c
    // 0x997774: ldur            d4, [fp, #-0x48]
    // 0x997778: fcmp            d3, d4
    // 0x99777c: b.lt            #0x9977b0
    // 0x997780: fcmp            d4, d2
    // 0x997784: b.ge            #0x997790
    // 0x997788: ldur            x0, [fp, #-0x38]
    // 0x99778c: b               #0x9977b4
    // 0x997790: ldur            x0, [fp, #-0x38]
    // 0x997794: LoadField: r1 = r0->field_b
    //     0x997794: ldur            w1, [x0, #0xb]
    // 0x997798: DecompressPointer r1
    //     0x997798: add             x1, x1, HEAP, lsl #32
    // 0x99779c: cmp             w1, NULL
    // 0x9977a0: b.eq            #0x997bec
    // 0x9977a4: LoadField: r0 = r1->field_b
    //     0x9977a4: ldur            x0, [x1, #0xb]
    // 0x9977a8: mov             x4, x0
    // 0x9977ac: b               #0x997958
    // 0x9977b0: ldur            x0, [fp, #-0x38]
    // 0x9977b4: ldur            d5, [fp, #-0x58]
    // 0x9977b8: fcmp            d2, d5
    // 0x9977bc: b.lt            #0x9977e8
    // 0x9977c0: fcmp            d5, d0
    // 0x9977c4: b.lt            #0x9977e8
    // 0x9977c8: LoadField: r1 = r0->field_b
    //     0x9977c8: ldur            w1, [x0, #0xb]
    // 0x9977cc: DecompressPointer r1
    //     0x9977cc: add             x1, x1, HEAP, lsl #32
    // 0x9977d0: cmp             w1, NULL
    // 0x9977d4: b.eq            #0x997bf0
    // 0x9977d8: LoadField: r0 = r1->field_b
    //     0x9977d8: ldur            x0, [x1, #0xb]
    // 0x9977dc: add             x1, x0, #1
    // 0x9977e0: mov             x4, x1
    // 0x9977e4: b               #0x997958
    // 0x9977e8: fcmp            d0, d4
    // 0x9977ec: b.le            #0x99781c
    // 0x9977f0: ldur            x2, [fp, #-0x28]
    // 0x9977f4: LoadField: r1 = r0->field_b
    //     0x9977f4: ldur            w1, [x0, #0xb]
    // 0x9977f8: DecompressPointer r1
    //     0x9977f8: add             x1, x1, HEAP, lsl #32
    // 0x9977fc: cmp             w1, NULL
    // 0x997800: b.eq            #0x997bf4
    // 0x997804: LoadField: r3 = r1->field_b
    //     0x997804: ldur            x3, [x1, #0xb]
    // 0x997808: add             x1, x3, #1
    // 0x99780c: cmp             x2, x1
    // 0x997810: b.ge            #0x997820
    // 0x997814: mov             x0, x1
    // 0x997818: b               #0x997930
    // 0x99781c: ldur            x2, [fp, #-0x28]
    // 0x997820: fcmp            d5, d3
    // 0x997824: b.le            #0x997844
    // 0x997828: LoadField: r1 = r0->field_b
    //     0x997828: ldur            w1, [x0, #0xb]
    // 0x99782c: DecompressPointer r1
    //     0x99782c: add             x1, x1, HEAP, lsl #32
    // 0x997830: cmp             w1, NULL
    // 0x997834: b.eq            #0x997bf8
    // 0x997838: LoadField: r0 = r1->field_b
    //     0x997838: ldur            x0, [x1, #0xb]
    // 0x99783c: cmp             x2, x0
    // 0x997840: b.gt            #0x997930
    // 0x997844: mov             x0, x2
    // 0x997848: b               #0x997930
    // 0x99784c: ldur            d5, [fp, #-0x58]
    // 0x997850: ldur            d4, [fp, #-0x48]
    // 0x997854: ldur            x2, [fp, #-0x28]
    // 0x997858: ldur            x0, [fp, #-0x38]
    // 0x99785c: r16 = Instance_AxisDirection
    //     0x99785c: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0x997860: cmp             w1, w16
    // 0x997864: b.eq            #0x997874
    // 0x997868: r16 = Instance_AxisDirection
    //     0x997868: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0x99786c: cmp             w1, w16
    // 0x997870: b.eq            #0x997874
    // 0x997874: LoadField: r1 = r0->field_b
    //     0x997874: ldur            w1, [x0, #0xb]
    // 0x997878: DecompressPointer r1
    //     0x997878: add             x1, x1, HEAP, lsl #32
    // 0x99787c: cmp             w1, NULL
    // 0x997880: b.eq            #0x997bfc
    // 0x997884: LoadField: r0 = r1->field_b
    //     0x997884: ldur            x0, [x1, #0xb]
    // 0x997888: LoadField: r1 = r5->field_23
    //     0x997888: ldur            w1, [x5, #0x23]
    // 0x99788c: DecompressPointer r1
    //     0x99788c: add             x1, x1, HEAP, lsl #32
    // 0x997890: cmp             w1, NULL
    // 0x997894: b.eq            #0x997c00
    // 0x997898: r3 = LoadInt32Instr(r1)
    //     0x997898: sbfx            x3, x1, #1, #0x1f
    //     0x99789c: tbz             w1, #0, #0x9978a4
    //     0x9978a0: ldur            x3, [x1, #7]
    // 0x9978a4: cmp             x0, x3
    // 0x9978a8: b.ne            #0x9978cc
    // 0x9978ac: fcmp            d4, d2
    // 0x9978b0: b.lt            #0x9978c4
    // 0x9978b4: fcmp            d3, d4
    // 0x9978b8: b.lt            #0x9978c4
    // 0x9978bc: mov             x0, x3
    // 0x9978c0: b               #0x997930
    // 0x9978c4: mov             x0, x2
    // 0x9978c8: b               #0x997930
    // 0x9978cc: fcmp            d5, d0
    // 0x9978d0: b.lt            #0x9978e4
    // 0x9978d4: fcmp            d2, d5
    // 0x9978d8: b.lt            #0x9978e4
    // 0x9978dc: mov             x4, x0
    // 0x9978e0: b               #0x997958
    // 0x9978e4: fcmp            d4, d2
    // 0x9978e8: b.lt            #0x997900
    // 0x9978ec: fcmp            d3, d4
    // 0x9978f0: b.lt            #0x997900
    // 0x9978f4: add             x1, x0, #1
    // 0x9978f8: mov             x4, x1
    // 0x9978fc: b               #0x997958
    // 0x997900: fcmp            d5, d3
    // 0x997904: b.le            #0x99791c
    // 0x997908: add             x1, x0, #1
    // 0x99790c: cmp             x2, x1
    // 0x997910: b.ge            #0x99791c
    // 0x997914: mov             x0, x1
    // 0x997918: b               #0x997930
    // 0x99791c: fcmp            d0, d4
    // 0x997920: b.le            #0x99792c
    // 0x997924: cmp             x2, x0
    // 0x997928: b.gt            #0x997930
    // 0x99792c: mov             x0, x2
    // 0x997930: mov             x4, x0
    // 0x997934: mov             x3, x5
    // 0x997938: mov             v0.16b, v5.16b
    // 0x99793c: mov             v1.16b, v4.16b
    // 0x997940: ldur            x2, [fp, #-0x30]
    // 0x997944: ldur            x0, [fp, #-0x20]
    // 0x997948: b               #0x9974f4
    // 0x99794c: ldur            x5, [fp, #-0x10]
    // 0x997950: ldur            x2, [fp, #-0x28]
    // 0x997954: mov             x4, x2
    // 0x997958: stur            x4, [fp, #-0x28]
    // 0x99795c: LoadField: r2 = r5->field_2b
    //     0x99795c: ldur            w2, [x5, #0x2b]
    // 0x997960: DecompressPointer r2
    //     0x997960: add             x2, x2, HEAP, lsl #32
    // 0x997964: r0 = BoxInt64Instr(r4)
    //     0x997964: sbfiz           x0, x4, #1, #0x1f
    //     0x997968: cmp             x4, x0, asr #1
    //     0x99796c: b.eq            #0x997978
    //     0x997970: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x997974: stur            x4, [x0, #7]
    // 0x997978: cmp             w0, w2
    // 0x99797c: b.eq            #0x997b7c
    // 0x997980: and             w16, w0, w2
    // 0x997984: branchIfSmi(r16, 0x9979b8)
    //     0x997984: tbz             w16, #0, #0x9979b8
    // 0x997988: r16 = LoadClassIdInstr(r0)
    //     0x997988: ldur            x16, [x0, #-1]
    //     0x99798c: ubfx            x16, x16, #0xc, #0x14
    // 0x997990: cmp             x16, #0x3d
    // 0x997994: b.ne            #0x9979b8
    // 0x997998: r16 = LoadClassIdInstr(r2)
    //     0x997998: ldur            x16, [x2, #-1]
    //     0x99799c: ubfx            x16, x16, #0xc, #0x14
    // 0x9979a0: cmp             x16, #0x3d
    // 0x9979a4: b.ne            #0x9979b8
    // 0x9979a8: LoadField: r16 = r0->field_7
    //     0x9979a8: ldur            x16, [x0, #7]
    // 0x9979ac: LoadField: r17 = r2->field_7
    //     0x9979ac: ldur            x17, [x2, #7]
    // 0x9979b0: cmp             x16, x17
    // 0x9979b4: b.eq            #0x997b7c
    // 0x9979b8: ldur            x6, [fp, #-0x18]
    // 0x9979bc: StoreField: r5->field_2b = r0
    //     0x9979bc: stur            w0, [x5, #0x2b]
    //     0x9979c0: tbz             w0, #0, #0x9979dc
    //     0x9979c4: ldurb           w16, [x5, #-1]
    //     0x9979c8: ldurb           w17, [x0, #-1]
    //     0x9979cc: and             x16, x17, x16, lsr #2
    //     0x9979d0: tst             x16, HEAP, lsr #32
    //     0x9979d4: b.eq            #0x9979dc
    //     0x9979d8: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x9979dc: ldur            x2, [fp, #-8]
    // 0x9979e0: r1 = Null
    //     0x9979e0: mov             x1, NULL
    // 0x9979e4: r3 = <X1>
    //     0x9979e4: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x9979e8: r0 = Null
    //     0x9979e8: mov             x0, NULL
    // 0x9979ec: cmp             x2, x0
    // 0x9979f0: b.eq            #0x997a00
    // 0x9979f4: r30 = InstantiateTypeArgumentsStub
    //     0x9979f4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x9979f8: LoadField: r30 = r30->field_7
    //     0x9979f8: ldur            lr, [lr, #7]
    // 0x9979fc: blr             lr
    // 0x997a00: mov             x1, x0
    // 0x997a04: r0 = _CompactIterable()
    //     0x997a04: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x997a08: mov             x1, x0
    // 0x997a0c: ldur            x0, [fp, #-0x18]
    // 0x997a10: StoreField: r1->field_b = r0
    //     0x997a10: stur            w0, [x1, #0xb]
    // 0x997a14: r0 = -1
    //     0x997a14: movn            x0, #0
    // 0x997a18: StoreField: r1->field_f = r0
    //     0x997a18: stur            x0, [x1, #0xf]
    // 0x997a1c: r0 = 2
    //     0x997a1c: movz            x0, #0x2
    // 0x997a20: ArrayStore: r1[0] = r0  ; List_8
    //     0x997a20: stur            x0, [x1, #0x17]
    // 0x997a24: r0 = iterator()
    //     0x997a24: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x997a28: stur            x0, [fp, #-0x18]
    // 0x997a2c: LoadField: r2 = r0->field_7
    //     0x997a2c: ldur            w2, [x0, #7]
    // 0x997a30: DecompressPointer r2
    //     0x997a30: add             x2, x2, HEAP, lsl #32
    // 0x997a34: stur            x2, [fp, #-8]
    // 0x997a38: ldur            x3, [fp, #-0x10]
    // 0x997a3c: CheckStackOverflow
    //     0x997a3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x997a40: cmp             SP, x16
    //     0x997a44: b.ls            #0x997c04
    // 0x997a48: mov             x1, x0
    // 0x997a4c: r0 = moveNext()
    //     0x997a4c: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x997a50: tbnz            w0, #4, #0x997b7c
    // 0x997a54: ldur            x3, [fp, #-0x18]
    // 0x997a58: LoadField: r4 = r3->field_33
    //     0x997a58: ldur            w4, [x3, #0x33]
    // 0x997a5c: DecompressPointer r4
    //     0x997a5c: add             x4, x4, HEAP, lsl #32
    // 0x997a60: stur            x4, [fp, #-0x20]
    // 0x997a64: cmp             w4, NULL
    // 0x997a68: b.ne            #0x997a9c
    // 0x997a6c: mov             x0, x4
    // 0x997a70: ldur            x2, [fp, #-8]
    // 0x997a74: r1 = Null
    //     0x997a74: mov             x1, NULL
    // 0x997a78: cmp             w2, NULL
    // 0x997a7c: b.eq            #0x997a9c
    // 0x997a80: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x997a80: ldur            w4, [x2, #0x17]
    // 0x997a84: DecompressPointer r4
    //     0x997a84: add             x4, x4, HEAP, lsl #32
    // 0x997a88: r8 = X0
    //     0x997a88: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x997a8c: LoadField: r9 = r4->field_7
    //     0x997a8c: ldur            x9, [x4, #7]
    // 0x997a90: r3 = Null
    //     0x997a90: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fde0] Null
    //     0x997a94: ldr             x3, [x3, #0xde0]
    // 0x997a98: blr             x9
    // 0x997a9c: ldur            x0, [fp, #-0x10]
    // 0x997aa0: ldur            x1, [fp, #-0x20]
    // 0x997aa4: LoadField: r2 = r1->field_b
    //     0x997aa4: ldur            w2, [x1, #0xb]
    // 0x997aa8: DecompressPointer r2
    //     0x997aa8: add             x2, x2, HEAP, lsl #32
    // 0x997aac: cmp             w2, NULL
    // 0x997ab0: b.eq            #0x997c0c
    // 0x997ab4: LoadField: r3 = r2->field_b
    //     0x997ab4: ldur            x3, [x2, #0xb]
    // 0x997ab8: LoadField: r2 = r0->field_23
    //     0x997ab8: ldur            w2, [x0, #0x23]
    // 0x997abc: DecompressPointer r2
    //     0x997abc: add             x2, x2, HEAP, lsl #32
    // 0x997ac0: cmp             w2, NULL
    // 0x997ac4: b.eq            #0x997c10
    // 0x997ac8: r4 = LoadInt32Instr(r2)
    //     0x997ac8: sbfx            x4, x2, #1, #0x1f
    //     0x997acc: tbz             w2, #0, #0x997ad4
    //     0x997ad0: ldur            x4, [x2, #7]
    // 0x997ad4: cmp             x3, x4
    // 0x997ad8: b.eq            #0x997b70
    // 0x997adc: LoadField: r2 = r1->field_f
    //     0x997adc: ldur            w2, [x1, #0xf]
    // 0x997ae0: DecompressPointer r2
    //     0x997ae0: add             x2, x2, HEAP, lsl #32
    // 0x997ae4: cmp             w2, NULL
    // 0x997ae8: b.eq            #0x997b70
    // 0x997aec: LoadField: r2 = r0->field_3f
    //     0x997aec: ldur            w2, [x0, #0x3f]
    // 0x997af0: DecompressPointer r2
    //     0x997af0: add             x2, x2, HEAP, lsl #32
    // 0x997af4: r16 = Sentinel
    //     0x997af4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x997af8: cmp             w2, w16
    // 0x997afc: b.eq            #0x997c14
    // 0x997b00: LoadField: r3 = r2->field_b
    //     0x997b00: ldur            w3, [x2, #0xb]
    // 0x997b04: DecompressPointer r3
    //     0x997b04: add             x3, x3, HEAP, lsl #32
    // 0x997b08: cmp             w3, NULL
    // 0x997b0c: b.eq            #0x997c20
    // 0x997b10: LoadField: r2 = r3->field_b
    //     0x997b10: ldur            w2, [x3, #0xb]
    // 0x997b14: DecompressPointer r2
    //     0x997b14: add             x2, x2, HEAP, lsl #32
    // 0x997b18: r16 = Instance_AxisDirection
    //     0x997b18: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0x997b1c: cmp             w2, w16
    // 0x997b20: b.eq            #0x997b30
    // 0x997b24: r16 = Instance_AxisDirection
    //     0x997b24: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0x997b28: cmp             w2, w16
    // 0x997b2c: b.ne            #0x997b38
    // 0x997b30: r6 = true
    //     0x997b30: add             x6, NULL, #0x20  ; true
    // 0x997b34: b               #0x997b5c
    // 0x997b38: r16 = Instance_AxisDirection
    //     0x997b38: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0x997b3c: cmp             w2, w16
    // 0x997b40: b.eq            #0x997b50
    // 0x997b44: r16 = Instance_AxisDirection
    //     0x997b44: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0x997b48: cmp             w2, w16
    // 0x997b4c: b.ne            #0x997b58
    // 0x997b50: r6 = false
    //     0x997b50: add             x6, NULL, #0x30  ; false
    // 0x997b54: b               #0x997b5c
    // 0x997b58: r6 = Null
    //     0x997b58: mov             x6, NULL
    // 0x997b5c: mov             x2, x4
    // 0x997b60: ldur            x3, [fp, #-0x28]
    // 0x997b64: ldur            d0, [fp, #-0x50]
    // 0x997b68: r5 = true
    //     0x997b68: add             x5, NULL, #0x20  ; true
    // 0x997b6c: r0 = updateForGap()
    //     0x997b6c: bl              #0x9454c4  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::updateForGap
    // 0x997b70: ldur            x0, [fp, #-0x18]
    // 0x997b74: ldur            x2, [fp, #-8]
    // 0x997b78: b               #0x997a38
    // 0x997b7c: r0 = Null
    //     0x997b7c: mov             x0, NULL
    // 0x997b80: LeaveFrame
    //     0x997b80: mov             SP, fp
    //     0x997b84: ldp             fp, lr, [SP], #0x10
    // 0x997b88: ret
    //     0x997b88: ret             
    // 0x997b8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x997b8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x997b90: b               #0x997350
    // 0x997b94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997b94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997b98: r9 = itemExtent
    //     0x997b98: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd08] Field <<EMAIL>>: late (offset: 0x40)
    //     0x997b9c: ldr             x9, [x9, #0xd08]
    // 0x997ba0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x997ba0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x997ba4: r9 = dragPosition
    //     0x997ba4: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd40] Field <<EMAIL>>: late (offset: 0x30)
    //     0x997ba8: ldr             x9, [x9, #0xd40]
    // 0x997bac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x997bac: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x997bb0: r9 = dragOffset
    //     0x997bb0: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd48] Field <<EMAIL>>: late (offset: 0x34)
    //     0x997bb4: ldr             x9, [x9, #0xd48]
    // 0x997bb8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x997bb8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x997bbc: r9 = _scrollable
    //     0x997bbc: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0x997bc0: ldr             x9, [x9, #0xd10]
    // 0x997bc4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x997bc4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x997bc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997bc8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997bcc: r0 = NullCastErrorSharedWithFPURegs()
    //     0x997bcc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x997bd0: r0 = StackOverflowSharedWithFPURegs()
    //     0x997bd0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x997bd4: b               #0x997504
    // 0x997bd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997bd8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997bdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997bdc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997be0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997be0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997be4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997be4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997be8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997be8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997bec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997bec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997bf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997bf0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997bf4: r0 = NullCastErrorSharedWithFPURegs()
    //     0x997bf4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x997bf8: r0 = NullCastErrorSharedWithFPURegs()
    //     0x997bf8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x997bfc: r0 = NullCastErrorSharedWithFPURegs()
    //     0x997bfc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x997c00: r0 = NullCastErrorSharedWithFPURegs()
    //     0x997c00: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x997c04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x997c04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x997c08: b               #0x997a48
    // 0x997c0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997c0c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997c10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997c10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x997c14: r9 = _scrollable
    //     0x997c14: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0x997c18: ldr             x9, [x9, #0xd10]
    // 0x997c1c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x997c1c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x997c20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x997c20: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9a7c90, size: 0x138
    // 0x9a7c90: EnterFrame
    //     0x9a7c90: stp             fp, lr, [SP, #-0x10]!
    //     0x9a7c94: mov             fp, SP
    // 0x9a7c98: AllocStack(0x18)
    //     0x9a7c98: sub             SP, SP, #0x18
    // 0x9a7c9c: SetupParameters(SliverReorderableListState this /* r1 => r2, fp-0x8 */)
    //     0x9a7c9c: mov             x2, x1
    //     0x9a7ca0: stur            x1, [fp, #-8]
    // 0x9a7ca4: CheckStackOverflow
    //     0x9a7ca4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a7ca8: cmp             SP, x16
    //     0x9a7cac: b.ls            #0x9a7db8
    // 0x9a7cb0: LoadField: r1 = r2->field_f
    //     0x9a7cb0: ldur            w1, [x2, #0xf]
    // 0x9a7cb4: DecompressPointer r1
    //     0x9a7cb4: add             x1, x1, HEAP, lsl #32
    // 0x9a7cb8: cmp             w1, NULL
    // 0x9a7cbc: b.eq            #0x9a7dc0
    // 0x9a7cc0: r0 = of()
    //     0x9a7cc0: bl              #0x9a7dc8  ; [package:flutter/src/widgets/scrollable.dart] Scrollable::of
    // 0x9a7cc4: mov             x1, x0
    // 0x9a7cc8: ldur            x2, [fp, #-8]
    // 0x9a7ccc: StoreField: r2->field_3f = r0
    //     0x9a7ccc: stur            w0, [x2, #0x3f]
    //     0x9a7cd0: ldurb           w16, [x2, #-1]
    //     0x9a7cd4: ldurb           w17, [x0, #-1]
    //     0x9a7cd8: and             x16, x17, x16, lsr #2
    //     0x9a7cdc: tst             x16, HEAP, lsr #32
    //     0x9a7ce0: b.eq            #0x9a7ce8
    //     0x9a7ce4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9a7ce8: LoadField: r0 = r2->field_3b
    //     0x9a7ce8: ldur            w0, [x2, #0x3b]
    // 0x9a7cec: DecompressPointer r0
    //     0x9a7cec: add             x0, x0, HEAP, lsl #32
    // 0x9a7cf0: cmp             w0, NULL
    // 0x9a7cf4: b.ne            #0x9a7d00
    // 0x9a7cf8: r3 = Null
    //     0x9a7cf8: mov             x3, NULL
    // 0x9a7cfc: b               #0x9a7d08
    // 0x9a7d00: LoadField: r3 = r0->field_7
    //     0x9a7d00: ldur            w3, [x0, #7]
    // 0x9a7d04: DecompressPointer r3
    //     0x9a7d04: add             x3, x3, HEAP, lsl #32
    // 0x9a7d08: cmp             w3, w1
    // 0x9a7d0c: b.eq            #0x9a7da8
    // 0x9a7d10: cmp             w0, NULL
    // 0x9a7d14: b.eq            #0x9a7d24
    // 0x9a7d18: mov             x1, x0
    // 0x9a7d1c: r0 = disallowIndicator()
    //     0x9a7d1c: bl              #0x995e14  ; [package:flutter/src/widgets/overscroll_indicator.dart] OverscrollIndicatorNotification::disallowIndicator
    // 0x9a7d20: ldur            x2, [fp, #-8]
    // 0x9a7d24: LoadField: r0 = r2->field_3f
    //     0x9a7d24: ldur            w0, [x2, #0x3f]
    // 0x9a7d28: DecompressPointer r0
    //     0x9a7d28: add             x0, x0, HEAP, lsl #32
    // 0x9a7d2c: stur            x0, [fp, #-0x10]
    // 0x9a7d30: LoadField: r1 = r2->field_b
    //     0x9a7d30: ldur            w1, [x2, #0xb]
    // 0x9a7d34: DecompressPointer r1
    //     0x9a7d34: add             x1, x1, HEAP, lsl #32
    // 0x9a7d38: cmp             w1, NULL
    // 0x9a7d3c: b.eq            #0x9a7dc4
    // 0x9a7d40: r0 = EdgeDraggingAutoScroller()
    //     0x9a7d40: bl              #0x94619c  ; AllocateEdgeDraggingAutoScrollerStub -> EdgeDraggingAutoScroller (size=0x20)
    // 0x9a7d44: mov             x3, x0
    // 0x9a7d48: r0 = Sentinel
    //     0x9a7d48: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a7d4c: stur            x3, [fp, #-0x18]
    // 0x9a7d50: ArrayStore: r3[0] = r0  ; List_4
    //     0x9a7d50: stur            w0, [x3, #0x17]
    // 0x9a7d54: r0 = false
    //     0x9a7d54: add             x0, NULL, #0x30  ; false
    // 0x9a7d58: StoreField: r3->field_1b = r0
    //     0x9a7d58: stur            w0, [x3, #0x1b]
    // 0x9a7d5c: ldur            x0, [fp, #-0x10]
    // 0x9a7d60: StoreField: r3->field_7 = r0
    //     0x9a7d60: stur            w0, [x3, #7]
    // 0x9a7d64: ldur            x2, [fp, #-8]
    // 0x9a7d68: r1 = Function '_handleScrollableAutoScrolled@318218688':.
    //     0x9a7d68: add             x1, PP, #0x56, lsl #12  ; [pp+0x56868] AnonymousClosure: (0x9962fc), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_handleScrollableAutoScrolled (0x996334)
    //     0x9a7d6c: ldr             x1, [x1, #0x868]
    // 0x9a7d70: r0 = AllocateClosure()
    //     0x9a7d70: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a7d74: mov             x1, x0
    // 0x9a7d78: ldur            x0, [fp, #-0x18]
    // 0x9a7d7c: StoreField: r0->field_b = r1
    //     0x9a7d7c: stur            w1, [x0, #0xb]
    // 0x9a7d80: d0 = 50.000000
    //     0x9a7d80: ldr             d0, [PP, #0x5ac0]  ; [pp+0x5ac0] IMM: double(50) from 0x4049000000000000
    // 0x9a7d84: StoreField: r0->field_f = d0
    //     0x9a7d84: stur            d0, [x0, #0xf]
    // 0x9a7d88: ldur            x1, [fp, #-8]
    // 0x9a7d8c: StoreField: r1->field_3b = r0
    //     0x9a7d8c: stur            w0, [x1, #0x3b]
    //     0x9a7d90: ldurb           w16, [x1, #-1]
    //     0x9a7d94: ldurb           w17, [x0, #-1]
    //     0x9a7d98: and             x16, x17, x16, lsr #2
    //     0x9a7d9c: tst             x16, HEAP, lsr #32
    //     0x9a7da0: b.eq            #0x9a7da8
    //     0x9a7da4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9a7da8: r0 = Null
    //     0x9a7da8: mov             x0, NULL
    // 0x9a7dac: LeaveFrame
    //     0x9a7dac: mov             SP, fp
    //     0x9a7db0: ldp             fp, lr, [SP], #0x10
    // 0x9a7db4: ret
    //     0x9a7db4: ret             
    // 0x9a7db8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a7db8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a7dbc: b               #0x9a7cb0
    // 0x9a7dc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a7dc0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a7dc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a7dc4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa1c258, size: 0xa8
    // 0xa1c258: EnterFrame
    //     0xa1c258: stp             fp, lr, [SP, #-0x10]!
    //     0xa1c25c: mov             fp, SP
    // 0xa1c260: AllocStack(0x18)
    //     0xa1c260: sub             SP, SP, #0x18
    // 0xa1c264: SetupParameters(SliverReorderableListState this /* r1 => r0 */)
    //     0xa1c264: mov             x0, x1
    // 0xa1c268: LoadField: r1 = r0->field_b
    //     0xa1c268: ldur            w1, [x0, #0xb]
    // 0xa1c26c: DecompressPointer r1
    //     0xa1c26c: add             x1, x1, HEAP, lsl #32
    // 0xa1c270: cmp             w1, NULL
    // 0xa1c274: b.eq            #0xa1c2fc
    // 0xa1c278: LoadField: r3 = r1->field_13
    //     0xa1c278: ldur            x3, [x1, #0x13]
    // 0xa1c27c: mov             x2, x0
    // 0xa1c280: stur            x3, [fp, #-8]
    // 0xa1c284: r1 = Function '_itemBuilder@318218688':.
    //     0xa1c284: add             x1, PP, #0x56, lsl #12  ; [pp+0x567e8] AnonymousClosure: (0xa1c30c), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_itemBuilder (0xa1c34c)
    //     0xa1c288: ldr             x1, [x1, #0x7e8]
    // 0xa1c28c: r0 = AllocateClosure()
    //     0xa1c28c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1c290: stur            x0, [fp, #-0x10]
    // 0xa1c294: r0 = SliverChildBuilderDelegate()
    //     0xa1c294: bl              #0x9d3320  ; AllocateSliverChildBuilderDelegateStub -> SliverChildBuilderDelegate (size=0x2c)
    // 0xa1c298: mov             x2, x0
    // 0xa1c29c: ldur            x0, [fp, #-0x10]
    // 0xa1c2a0: stur            x2, [fp, #-0x18]
    // 0xa1c2a4: StoreField: r2->field_7 = r0
    //     0xa1c2a4: stur            w0, [x2, #7]
    // 0xa1c2a8: ldur            x3, [fp, #-8]
    // 0xa1c2ac: r0 = BoxInt64Instr(r3)
    //     0xa1c2ac: sbfiz           x0, x3, #1, #0x1f
    //     0xa1c2b0: cmp             x3, x0, asr #1
    //     0xa1c2b4: b.eq            #0xa1c2c0
    //     0xa1c2b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa1c2bc: stur            x3, [x0, #7]
    // 0xa1c2c0: StoreField: r2->field_b = r0
    //     0xa1c2c0: stur            w0, [x2, #0xb]
    // 0xa1c2c4: r0 = true
    //     0xa1c2c4: add             x0, NULL, #0x20  ; true
    // 0xa1c2c8: StoreField: r2->field_f = r0
    //     0xa1c2c8: stur            w0, [x2, #0xf]
    // 0xa1c2cc: StoreField: r2->field_13 = r0
    //     0xa1c2cc: stur            w0, [x2, #0x13]
    // 0xa1c2d0: ArrayStore: r2[0] = r0  ; List_4
    //     0xa1c2d0: stur            w0, [x2, #0x17]
    // 0xa1c2d4: r0 = Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static.
    //     0xa1c2d4: add             x0, PP, #0x26, lsl #12  ; [pp+0x26ef8] Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static. (0x7e54fb8bd554)
    //     0xa1c2d8: ldr             x0, [x0, #0xef8]
    // 0xa1c2dc: StoreField: r2->field_23 = r0
    //     0xa1c2dc: stur            w0, [x2, #0x23]
    // 0xa1c2e0: StoreField: r2->field_1b = rZR
    //     0xa1c2e0: stur            xzr, [x2, #0x1b]
    // 0xa1c2e4: r0 = SliverList()
    //     0xa1c2e4: bl              #0xa1c300  ; AllocateSliverListStub -> SliverList (size=0x10)
    // 0xa1c2e8: ldur            x1, [fp, #-0x18]
    // 0xa1c2ec: StoreField: r0->field_b = r1
    //     0xa1c2ec: stur            w1, [x0, #0xb]
    // 0xa1c2f0: LeaveFrame
    //     0xa1c2f0: mov             SP, fp
    //     0xa1c2f4: ldp             fp, lr, [SP], #0x10
    // 0xa1c2f8: ret
    //     0xa1c2f8: ret             
    // 0xa1c2fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c2fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget _itemBuilder(dynamic, BuildContext, int) {
    // ** addr: 0xa1c30c, size: 0x40
    // 0xa1c30c: EnterFrame
    //     0xa1c30c: stp             fp, lr, [SP, #-0x10]!
    //     0xa1c310: mov             fp, SP
    // 0xa1c314: ldr             x0, [fp, #0x20]
    // 0xa1c318: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa1c318: ldur            w1, [x0, #0x17]
    // 0xa1c31c: DecompressPointer r1
    //     0xa1c31c: add             x1, x1, HEAP, lsl #32
    // 0xa1c320: CheckStackOverflow
    //     0xa1c320: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1c324: cmp             SP, x16
    //     0xa1c328: b.ls            #0xa1c344
    // 0xa1c32c: ldr             x2, [fp, #0x18]
    // 0xa1c330: ldr             x3, [fp, #0x10]
    // 0xa1c334: r0 = _itemBuilder()
    //     0xa1c334: bl              #0xa1c34c  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_itemBuilder
    // 0xa1c338: LeaveFrame
    //     0xa1c338: mov             SP, fp
    //     0xa1c33c: ldp             fp, lr, [SP], #0x10
    // 0xa1c340: ret
    //     0xa1c340: ret             
    // 0xa1c344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1c344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1c348: b               #0xa1c32c
  }
  _ _itemBuilder(/* No info */) {
    // ** addr: 0xa1c34c, size: 0x264
    // 0xa1c34c: EnterFrame
    //     0xa1c34c: stp             fp, lr, [SP, #-0x10]!
    //     0xa1c350: mov             fp, SP
    // 0xa1c354: AllocStack(0x40)
    //     0xa1c354: sub             SP, SP, #0x40
    // 0xa1c358: SetupParameters(SliverReorderableListState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0xa1c358: mov             x4, x2
    //     0xa1c35c: stur            x2, [fp, #-0x18]
    //     0xa1c360: mov             x2, x1
    //     0xa1c364: mov             x0, x3
    //     0xa1c368: stur            x1, [fp, #-8]
    //     0xa1c36c: stur            x3, [fp, #-0x20]
    // 0xa1c370: CheckStackOverflow
    //     0xa1c370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1c374: cmp             SP, x16
    //     0xa1c378: b.ls            #0xa1c574
    // 0xa1c37c: LoadField: r1 = r2->field_27
    //     0xa1c37c: ldur            w1, [x2, #0x27]
    // 0xa1c380: DecompressPointer r1
    //     0xa1c380: add             x1, x1, HEAP, lsl #32
    // 0xa1c384: cmp             w1, NULL
    // 0xa1c388: b.eq            #0xa1c460
    // 0xa1c38c: LoadField: r1 = r2->field_b
    //     0xa1c38c: ldur            w1, [x2, #0xb]
    // 0xa1c390: DecompressPointer r1
    //     0xa1c390: add             x1, x1, HEAP, lsl #32
    // 0xa1c394: cmp             w1, NULL
    // 0xa1c398: b.eq            #0xa1c57c
    // 0xa1c39c: LoadField: r3 = r1->field_13
    //     0xa1c39c: ldur            x3, [x1, #0x13]
    // 0xa1c3a0: r1 = LoadInt32Instr(r0)
    //     0xa1c3a0: sbfx            x1, x0, #1, #0x1f
    //     0xa1c3a4: tbz             w0, #0, #0xa1c3ac
    //     0xa1c3a8: ldur            x1, [x0, #7]
    // 0xa1c3ac: cmp             x1, x3
    // 0xa1c3b0: b.lt            #0xa1c458
    // 0xa1c3b4: mov             x1, x2
    // 0xa1c3b8: r0 = _scrollDirection()
    //     0xa1c3b8: bl              #0x9458e8  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_scrollDirection
    // 0xa1c3bc: LoadField: r1 = r0->field_7
    //     0xa1c3bc: ldur            x1, [x0, #7]
    // 0xa1c3c0: cmp             x1, #0
    // 0xa1c3c4: b.gt            #0xa1c40c
    // 0xa1c3c8: ldur            x5, [fp, #-8]
    // 0xa1c3cc: LoadField: r0 = r5->field_27
    //     0xa1c3cc: ldur            w0, [x5, #0x27]
    // 0xa1c3d0: DecompressPointer r0
    //     0xa1c3d0: add             x0, x0, HEAP, lsl #32
    // 0xa1c3d4: cmp             w0, NULL
    // 0xa1c3d8: b.eq            #0xa1c580
    // 0xa1c3dc: LoadField: r1 = r0->field_3f
    //     0xa1c3dc: ldur            w1, [x0, #0x3f]
    // 0xa1c3e0: DecompressPointer r1
    //     0xa1c3e0: add             x1, x1, HEAP, lsl #32
    // 0xa1c3e4: r16 = Sentinel
    //     0xa1c3e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa1c3e8: cmp             w1, w16
    // 0xa1c3ec: b.eq            #0xa1c584
    // 0xa1c3f0: stur            x1, [fp, #-0x10]
    // 0xa1c3f4: r0 = SizedBox()
    //     0xa1c3f4: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa1c3f8: mov             x1, x0
    // 0xa1c3fc: ldur            x0, [fp, #-0x10]
    // 0xa1c400: StoreField: r1->field_f = r0
    //     0xa1c400: stur            w0, [x1, #0xf]
    // 0xa1c404: mov             x0, x1
    // 0xa1c408: b               #0xa1c44c
    // 0xa1c40c: ldur            x5, [fp, #-8]
    // 0xa1c410: LoadField: r0 = r5->field_27
    //     0xa1c410: ldur            w0, [x5, #0x27]
    // 0xa1c414: DecompressPointer r0
    //     0xa1c414: add             x0, x0, HEAP, lsl #32
    // 0xa1c418: cmp             w0, NULL
    // 0xa1c41c: b.eq            #0xa1c590
    // 0xa1c420: LoadField: r1 = r0->field_3f
    //     0xa1c420: ldur            w1, [x0, #0x3f]
    // 0xa1c424: DecompressPointer r1
    //     0xa1c424: add             x1, x1, HEAP, lsl #32
    // 0xa1c428: r16 = Sentinel
    //     0xa1c428: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa1c42c: cmp             w1, w16
    // 0xa1c430: b.eq            #0xa1c594
    // 0xa1c434: stur            x1, [fp, #-0x10]
    // 0xa1c438: r0 = SizedBox()
    //     0xa1c438: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa1c43c: mov             x1, x0
    // 0xa1c440: ldur            x0, [fp, #-0x10]
    // 0xa1c444: StoreField: r1->field_13 = r0
    //     0xa1c444: stur            w0, [x1, #0x13]
    // 0xa1c448: mov             x0, x1
    // 0xa1c44c: LeaveFrame
    //     0xa1c44c: mov             SP, fp
    //     0xa1c450: ldp             fp, lr, [SP], #0x10
    // 0xa1c454: ret
    //     0xa1c454: ret             
    // 0xa1c458: mov             x5, x2
    // 0xa1c45c: b               #0xa1c464
    // 0xa1c460: mov             x5, x2
    // 0xa1c464: LoadField: r1 = r5->field_b
    //     0xa1c464: ldur            w1, [x5, #0xb]
    // 0xa1c468: DecompressPointer r1
    //     0xa1c468: add             x1, x1, HEAP, lsl #32
    // 0xa1c46c: cmp             w1, NULL
    // 0xa1c470: b.eq            #0xa1c5a0
    // 0xa1c474: LoadField: r2 = r1->field_b
    //     0xa1c474: ldur            w2, [x1, #0xb]
    // 0xa1c478: DecompressPointer r2
    //     0xa1c478: add             x2, x2, HEAP, lsl #32
    // 0xa1c47c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa1c47c: ldur            w1, [x2, #0x17]
    // 0xa1c480: DecompressPointer r1
    //     0xa1c480: add             x1, x1, HEAP, lsl #32
    // 0xa1c484: mov             x2, x4
    // 0xa1c488: mov             x3, x0
    // 0xa1c48c: r0 = _itemBuilder()
    //     0xa1c48c: bl              #0xa014a0  ; [package:flutter/src/material/reorderable_list.dart] _ReorderableListViewState::_itemBuilder
    // 0xa1c490: mov             x2, x0
    // 0xa1c494: ldur            x0, [fp, #-8]
    // 0xa1c498: stur            x2, [fp, #-0x10]
    // 0xa1c49c: LoadField: r1 = r0->field_b
    //     0xa1c49c: ldur            w1, [x0, #0xb]
    // 0xa1c4a0: DecompressPointer r1
    //     0xa1c4a0: add             x1, x1, HEAP, lsl #32
    // 0xa1c4a4: cmp             w1, NULL
    // 0xa1c4a8: b.eq            #0xa1c5a4
    // 0xa1c4ac: ldur            x1, [fp, #-0x18]
    // 0xa1c4b0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa1c4b0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa1c4b4: r0 = of()
    //     0xa1c4b4: bl              #0x6a5014  ; [package:flutter/src/widgets/overlay.dart] Overlay::of
    // 0xa1c4b8: ldur            x2, [fp, #-0x10]
    // 0xa1c4bc: stur            x0, [fp, #-0x30]
    // 0xa1c4c0: LoadField: r3 = r2->field_7
    //     0xa1c4c0: ldur            w3, [x2, #7]
    // 0xa1c4c4: DecompressPointer r3
    //     0xa1c4c4: add             x3, x3, HEAP, lsl #32
    // 0xa1c4c8: stur            x3, [fp, #-0x28]
    // 0xa1c4cc: cmp             w3, NULL
    // 0xa1c4d0: b.eq            #0xa1c5a8
    // 0xa1c4d4: r1 = <State<StatefulWidget>>
    //     0xa1c4d4: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0xa1c4d8: r0 = _ReorderableItemGlobalKey()
    //     0xa1c4d8: bl              #0xa1cc20  ; Allocate_ReorderableItemGlobalKeyStub -> _ReorderableItemGlobalKey (size=0x20)
    // 0xa1c4dc: mov             x3, x0
    // 0xa1c4e0: ldur            x0, [fp, #-0x28]
    // 0xa1c4e4: stur            x3, [fp, #-0x40]
    // 0xa1c4e8: StoreField: r3->field_f = r0
    //     0xa1c4e8: stur            w0, [x3, #0xf]
    // 0xa1c4ec: ldur            x1, [fp, #-0x20]
    // 0xa1c4f0: r4 = LoadInt32Instr(r1)
    //     0xa1c4f0: sbfx            x4, x1, #1, #0x1f
    //     0xa1c4f4: tbz             w1, #0, #0xa1c4fc
    //     0xa1c4f8: ldur            x4, [x1, #7]
    // 0xa1c4fc: stur            x4, [fp, #-0x38]
    // 0xa1c500: StoreField: r3->field_13 = r4
    //     0xa1c500: stur            x4, [x3, #0x13]
    // 0xa1c504: ldur            x5, [fp, #-8]
    // 0xa1c508: StoreField: r3->field_1b = r5
    //     0xa1c508: stur            w5, [x3, #0x1b]
    // 0xa1c50c: StoreField: r3->field_b = r0
    //     0xa1c50c: stur            w0, [x3, #0xb]
    // 0xa1c510: ldur            x0, [fp, #-0x30]
    // 0xa1c514: LoadField: r2 = r0->field_f
    //     0xa1c514: ldur            w2, [x0, #0xf]
    // 0xa1c518: DecompressPointer r2
    //     0xa1c518: add             x2, x2, HEAP, lsl #32
    // 0xa1c51c: cmp             w2, NULL
    // 0xa1c520: b.eq            #0xa1c5ac
    // 0xa1c524: ldur            x1, [fp, #-0x18]
    // 0xa1c528: r0 = capture()
    //     0xa1c528: bl              #0x6a4738  ; [package:flutter/src/widgets/inherited_theme.dart] InheritedTheme::capture
    // 0xa1c52c: ldur            x1, [fp, #-8]
    // 0xa1c530: ldur            x2, [fp, #-0x10]
    // 0xa1c534: ldur            x3, [fp, #-0x38]
    // 0xa1c538: stur            x0, [fp, #-8]
    // 0xa1c53c: r0 = _wrapWithSemantics()
    //     0xa1c53c: bl              #0xa1c5bc  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_wrapWithSemantics
    // 0xa1c540: stur            x0, [fp, #-0x10]
    // 0xa1c544: r0 = _ReorderableItem()
    //     0xa1c544: bl              #0xa1c5b0  ; Allocate_ReorderableItemStub -> _ReorderableItem (size=0x1c)
    // 0xa1c548: ldur            x1, [fp, #-0x38]
    // 0xa1c54c: StoreField: r0->field_b = r1
    //     0xa1c54c: stur            x1, [x0, #0xb]
    // 0xa1c550: ldur            x1, [fp, #-0x10]
    // 0xa1c554: StoreField: r0->field_13 = r1
    //     0xa1c554: stur            w1, [x0, #0x13]
    // 0xa1c558: ldur            x1, [fp, #-8]
    // 0xa1c55c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa1c55c: stur            w1, [x0, #0x17]
    // 0xa1c560: ldur            x1, [fp, #-0x40]
    // 0xa1c564: StoreField: r0->field_7 = r1
    //     0xa1c564: stur            w1, [x0, #7]
    // 0xa1c568: LeaveFrame
    //     0xa1c568: mov             SP, fp
    //     0xa1c56c: ldp             fp, lr, [SP], #0x10
    // 0xa1c570: ret
    //     0xa1c570: ret             
    // 0xa1c574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1c574: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1c578: b               #0xa1c37c
    // 0xa1c57c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c57c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1c580: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c580: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1c584: r9 = itemExtent
    //     0xa1c584: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd08] Field <<EMAIL>>: late (offset: 0x40)
    //     0xa1c588: ldr             x9, [x9, #0xd08]
    // 0xa1c58c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa1c58c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa1c590: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c590: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1c594: r9 = itemExtent
    //     0xa1c594: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd08] Field <<EMAIL>>: late (offset: 0x40)
    //     0xa1c598: ldr             x9, [x9, #0xd08]
    // 0xa1c59c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa1c59c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa1c5a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c5a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1c5a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c5a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1c5a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c5a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1c5ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c5ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _wrapWithSemantics(/* No info */) {
    // ** addr: 0xa1c5bc, size: 0x2e4
    // 0xa1c5bc: EnterFrame
    //     0xa1c5bc: stp             fp, lr, [SP, #-0x10]!
    //     0xa1c5c0: mov             fp, SP
    // 0xa1c5c4: AllocStack(0x58)
    //     0xa1c5c4: sub             SP, SP, #0x58
    // 0xa1c5c8: SetupParameters(SliverReorderableListState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa1c5c8: stur            x1, [fp, #-8]
    //     0xa1c5cc: stur            x2, [fp, #-0x10]
    //     0xa1c5d0: stur            x3, [fp, #-0x18]
    // 0xa1c5d4: CheckStackOverflow
    //     0xa1c5d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1c5d8: cmp             SP, x16
    //     0xa1c5dc: b.ls            #0xa1c888
    // 0xa1c5e0: r1 = 3
    //     0xa1c5e0: movz            x1, #0x3
    // 0xa1c5e4: r0 = AllocateContext()
    //     0xa1c5e4: bl              #0xec126c  ; AllocateContextStub
    // 0xa1c5e8: mov             x4, x0
    // 0xa1c5ec: ldur            x3, [fp, #-8]
    // 0xa1c5f0: stur            x4, [fp, #-0x20]
    // 0xa1c5f4: StoreField: r4->field_f = r3
    //     0xa1c5f4: stur            w3, [x4, #0xf]
    // 0xa1c5f8: ldur            x2, [fp, #-0x18]
    // 0xa1c5fc: r0 = BoxInt64Instr(r2)
    //     0xa1c5fc: sbfiz           x0, x2, #1, #0x1f
    //     0xa1c600: cmp             x2, x0, asr #1
    //     0xa1c604: b.eq            #0xa1c610
    //     0xa1c608: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa1c60c: stur            x2, [x0, #7]
    // 0xa1c610: StoreField: r4->field_13 = r0
    //     0xa1c610: stur            w0, [x4, #0x13]
    // 0xa1c614: mov             x2, x4
    // 0xa1c618: r1 = Function 'reorder':.
    //     0xa1c618: add             x1, PP, #0x56, lsl #12  ; [pp+0x567f0] AnonymousClosure: (0xa1cb80), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_wrapWithSemantics (0xa1c5bc)
    //     0xa1c61c: ldr             x1, [x1, #0x7f0]
    // 0xa1c620: r0 = AllocateClosure()
    //     0xa1c620: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1c624: ldur            x2, [fp, #-0x20]
    // 0xa1c628: ArrayStore: r2[0] = r0  ; List_4
    //     0xa1c628: stur            w0, [x2, #0x17]
    // 0xa1c62c: r16 = <CustomSemanticsAction, (dynamic this) => void?>
    //     0xa1c62c: ldr             x16, [PP, #0x2a80]  ; [pp+0x2a80] TypeArguments: <CustomSemanticsAction, (dynamic this) => void?>
    // 0xa1c630: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa1c634: stp             lr, x16, [SP]
    // 0xa1c638: r0 = Map._fromLiteral()
    //     0xa1c638: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa1c63c: mov             x2, x0
    // 0xa1c640: ldur            x0, [fp, #-8]
    // 0xa1c644: stur            x2, [fp, #-0x28]
    // 0xa1c648: LoadField: r1 = r0->field_f
    //     0xa1c648: ldur            w1, [x0, #0xf]
    // 0xa1c64c: DecompressPointer r1
    //     0xa1c64c: add             x1, x1, HEAP, lsl #32
    // 0xa1c650: cmp             w1, NULL
    // 0xa1c654: b.eq            #0xa1c890
    // 0xa1c658: r0 = of()
    //     0xa1c658: bl              #0x9f11a8  ; [package:flutter/src/widgets/localizations.dart] WidgetsLocalizations::of
    // 0xa1c65c: ldur            x1, [fp, #-8]
    // 0xa1c660: r0 = _scrollDirection()
    //     0xa1c660: bl              #0x9458e8  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_scrollDirection
    // 0xa1c664: r16 = Instance_Axis
    //     0xa1c664: ldr             x16, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xa1c668: cmp             w0, w16
    // 0xa1c66c: r16 = true
    //     0xa1c66c: add             x16, NULL, #0x20  ; true
    // 0xa1c670: r17 = false
    //     0xa1c670: add             x17, NULL, #0x30  ; false
    // 0xa1c674: csel            x1, x16, x17, eq
    // 0xa1c678: ldur            x2, [fp, #-0x20]
    // 0xa1c67c: stur            x1, [fp, #-0x30]
    // 0xa1c680: LoadField: r0 = r2->field_13
    //     0xa1c680: ldur            w0, [x2, #0x13]
    // 0xa1c684: DecompressPointer r0
    //     0xa1c684: add             x0, x0, HEAP, lsl #32
    // 0xa1c688: r3 = LoadInt32Instr(r0)
    //     0xa1c688: sbfx            x3, x0, #1, #0x1f
    //     0xa1c68c: tbz             w0, #0, #0xa1c694
    //     0xa1c690: ldur            x3, [x0, #7]
    // 0xa1c694: cmp             x3, #0
    // 0xa1c698: b.le            #0xa1c758
    // 0xa1c69c: r0 = CustomSemanticsAction()
    //     0xa1c69c: bl              #0x6cb468  ; AllocateCustomSemanticsActionStub -> CustomSemanticsAction (size=0x14)
    // 0xa1c6a0: mov             x3, x0
    // 0xa1c6a4: r0 = "Move to the start"
    //     0xa1c6a4: add             x0, PP, #0x47, lsl #12  ; [pp+0x479b0] "Move to the start"
    //     0xa1c6a8: ldr             x0, [x0, #0x9b0]
    // 0xa1c6ac: stur            x3, [fp, #-0x38]
    // 0xa1c6b0: StoreField: r3->field_7 = r0
    //     0xa1c6b0: stur            w0, [x3, #7]
    // 0xa1c6b4: ldur            x2, [fp, #-0x20]
    // 0xa1c6b8: r1 = Function 'moveToStart':.
    //     0xa1c6b8: add             x1, PP, #0x56, lsl #12  ; [pp+0x567f8] AnonymousClosure: (0xa1caf0), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_wrapWithSemantics (0xa1c5bc)
    //     0xa1c6bc: ldr             x1, [x1, #0x7f8]
    // 0xa1c6c0: r0 = AllocateClosure()
    //     0xa1c6c0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1c6c4: ldur            x1, [fp, #-0x28]
    // 0xa1c6c8: ldur            x2, [fp, #-0x38]
    // 0xa1c6cc: mov             x3, x0
    // 0xa1c6d0: r0 = []=()
    //     0xa1c6d0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa1c6d4: ldur            x0, [fp, #-0x30]
    // 0xa1c6d8: tbnz            w0, #4, #0xa1c718
    // 0xa1c6dc: ldur            x2, [fp, #-8]
    // 0xa1c6e0: LoadField: r1 = r2->field_f
    //     0xa1c6e0: ldur            w1, [x2, #0xf]
    // 0xa1c6e4: DecompressPointer r1
    //     0xa1c6e4: add             x1, x1, HEAP, lsl #32
    // 0xa1c6e8: cmp             w1, NULL
    // 0xa1c6ec: b.eq            #0xa1c894
    // 0xa1c6f0: r0 = of()
    //     0xa1c6f0: bl              #0x6b0ae8  ; [package:flutter/src/widgets/basic.dart] Directionality::of
    // 0xa1c6f4: r16 = Instance_TextDirection
    //     0xa1c6f4: ldr             x16, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0xa1c6f8: cmp             w0, w16
    // 0xa1c6fc: b.ne            #0xa1c70c
    // 0xa1c700: r0 = "Move left"
    //     0xa1c700: add             x0, PP, #0x56, lsl #12  ; [pp+0x56800] "Move left"
    //     0xa1c704: ldr             x0, [x0, #0x800]
    // 0xa1c708: b               #0xa1c720
    // 0xa1c70c: r0 = "Move right"
    //     0xa1c70c: add             x0, PP, #0x56, lsl #12  ; [pp+0x56808] "Move right"
    //     0xa1c710: ldr             x0, [x0, #0x808]
    // 0xa1c714: b               #0xa1c720
    // 0xa1c718: r0 = "Move up"
    //     0xa1c718: add             x0, PP, #0x47, lsl #12  ; [pp+0x479c0] "Move up"
    //     0xa1c71c: ldr             x0, [x0, #0x9c0]
    // 0xa1c720: stur            x0, [fp, #-0x38]
    // 0xa1c724: r0 = CustomSemanticsAction()
    //     0xa1c724: bl              #0x6cb468  ; AllocateCustomSemanticsActionStub -> CustomSemanticsAction (size=0x14)
    // 0xa1c728: mov             x3, x0
    // 0xa1c72c: ldur            x0, [fp, #-0x38]
    // 0xa1c730: stur            x3, [fp, #-0x40]
    // 0xa1c734: StoreField: r3->field_7 = r0
    //     0xa1c734: stur            w0, [x3, #7]
    // 0xa1c738: ldur            x2, [fp, #-0x20]
    // 0xa1c73c: r1 = Function 'moveBefore':.
    //     0xa1c73c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56810] AnonymousClosure: (0xa1ca34), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_wrapWithSemantics (0xa1c5bc)
    //     0xa1c740: ldr             x1, [x1, #0x810]
    // 0xa1c744: r0 = AllocateClosure()
    //     0xa1c744: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1c748: ldur            x1, [fp, #-0x28]
    // 0xa1c74c: ldur            x2, [fp, #-0x40]
    // 0xa1c750: mov             x3, x0
    // 0xa1c754: r0 = []=()
    //     0xa1c754: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa1c758: ldur            x0, [fp, #-8]
    // 0xa1c75c: ldur            x2, [fp, #-0x20]
    // 0xa1c760: LoadField: r1 = r2->field_13
    //     0xa1c760: ldur            w1, [x2, #0x13]
    // 0xa1c764: DecompressPointer r1
    //     0xa1c764: add             x1, x1, HEAP, lsl #32
    // 0xa1c768: LoadField: r3 = r0->field_b
    //     0xa1c768: ldur            w3, [x0, #0xb]
    // 0xa1c76c: DecompressPointer r3
    //     0xa1c76c: add             x3, x3, HEAP, lsl #32
    // 0xa1c770: cmp             w3, NULL
    // 0xa1c774: b.eq            #0xa1c898
    // 0xa1c778: LoadField: r4 = r3->field_13
    //     0xa1c778: ldur            x4, [x3, #0x13]
    // 0xa1c77c: sub             x3, x4, #1
    // 0xa1c780: r4 = LoadInt32Instr(r1)
    //     0xa1c780: sbfx            x4, x1, #1, #0x1f
    //     0xa1c784: tbz             w1, #0, #0xa1c78c
    //     0xa1c788: ldur            x4, [x1, #7]
    // 0xa1c78c: cmp             x4, x3
    // 0xa1c790: b.ge            #0xa1c84c
    // 0xa1c794: ldur            x1, [fp, #-0x30]
    // 0xa1c798: tbnz            w1, #4, #0xa1c7d4
    // 0xa1c79c: LoadField: r1 = r0->field_f
    //     0xa1c79c: ldur            w1, [x0, #0xf]
    // 0xa1c7a0: DecompressPointer r1
    //     0xa1c7a0: add             x1, x1, HEAP, lsl #32
    // 0xa1c7a4: cmp             w1, NULL
    // 0xa1c7a8: b.eq            #0xa1c89c
    // 0xa1c7ac: r0 = of()
    //     0xa1c7ac: bl              #0x6b0ae8  ; [package:flutter/src/widgets/basic.dart] Directionality::of
    // 0xa1c7b0: r16 = Instance_TextDirection
    //     0xa1c7b0: ldr             x16, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0xa1c7b4: cmp             w0, w16
    // 0xa1c7b8: b.ne            #0xa1c7c8
    // 0xa1c7bc: r0 = "Move right"
    //     0xa1c7bc: add             x0, PP, #0x56, lsl #12  ; [pp+0x56808] "Move right"
    //     0xa1c7c0: ldr             x0, [x0, #0x808]
    // 0xa1c7c4: b               #0xa1c7dc
    // 0xa1c7c8: r0 = "Move left"
    //     0xa1c7c8: add             x0, PP, #0x56, lsl #12  ; [pp+0x56800] "Move left"
    //     0xa1c7cc: ldr             x0, [x0, #0x800]
    // 0xa1c7d0: b               #0xa1c7dc
    // 0xa1c7d4: r0 = "Move down"
    //     0xa1c7d4: add             x0, PP, #0x47, lsl #12  ; [pp+0x479d0] "Move down"
    //     0xa1c7d8: ldr             x0, [x0, #0x9d0]
    // 0xa1c7dc: stur            x0, [fp, #-8]
    // 0xa1c7e0: r0 = CustomSemanticsAction()
    //     0xa1c7e0: bl              #0x6cb468  ; AllocateCustomSemanticsActionStub -> CustomSemanticsAction (size=0x14)
    // 0xa1c7e4: mov             x3, x0
    // 0xa1c7e8: ldur            x0, [fp, #-8]
    // 0xa1c7ec: stur            x3, [fp, #-0x30]
    // 0xa1c7f0: StoreField: r3->field_7 = r0
    //     0xa1c7f0: stur            w0, [x3, #7]
    // 0xa1c7f4: ldur            x2, [fp, #-0x20]
    // 0xa1c7f8: r1 = Function 'moveAfter':.
    //     0xa1c7f8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56818] AnonymousClosure: (0xa1c978), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_wrapWithSemantics (0xa1c5bc)
    //     0xa1c7fc: ldr             x1, [x1, #0x818]
    // 0xa1c800: r0 = AllocateClosure()
    //     0xa1c800: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1c804: ldur            x1, [fp, #-0x28]
    // 0xa1c808: ldur            x2, [fp, #-0x30]
    // 0xa1c80c: mov             x3, x0
    // 0xa1c810: r0 = []=()
    //     0xa1c810: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa1c814: r0 = CustomSemanticsAction()
    //     0xa1c814: bl              #0x6cb468  ; AllocateCustomSemanticsActionStub -> CustomSemanticsAction (size=0x14)
    // 0xa1c818: mov             x3, x0
    // 0xa1c81c: r0 = "Move to the end"
    //     0xa1c81c: add             x0, PP, #0x47, lsl #12  ; [pp+0x479e0] "Move to the end"
    //     0xa1c820: ldr             x0, [x0, #0x9e0]
    // 0xa1c824: stur            x3, [fp, #-8]
    // 0xa1c828: StoreField: r3->field_7 = r0
    //     0xa1c828: stur            w0, [x3, #7]
    // 0xa1c82c: ldur            x2, [fp, #-0x20]
    // 0xa1c830: r1 = Function 'moveToEnd':.
    //     0xa1c830: add             x1, PP, #0x56, lsl #12  ; [pp+0x56820] AnonymousClosure: (0xa1c8a0), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_wrapWithSemantics (0xa1c5bc)
    //     0xa1c834: ldr             x1, [x1, #0x820]
    // 0xa1c838: r0 = AllocateClosure()
    //     0xa1c838: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1c83c: ldur            x1, [fp, #-0x28]
    // 0xa1c840: ldur            x2, [fp, #-8]
    // 0xa1c844: mov             x3, x0
    // 0xa1c848: r0 = []=()
    //     0xa1c848: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa1c84c: r0 = Semantics()
    //     0xa1c84c: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0xa1c850: stur            x0, [fp, #-8]
    // 0xa1c854: r16 = true
    //     0xa1c854: add             x16, NULL, #0x20  ; true
    // 0xa1c858: ldur            lr, [fp, #-0x28]
    // 0xa1c85c: stp             lr, x16, [SP, #8]
    // 0xa1c860: ldur            x16, [fp, #-0x10]
    // 0xa1c864: str             x16, [SP]
    // 0xa1c868: mov             x1, x0
    // 0xa1c86c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, container, 0x1, customSemanticsActions, 0x2, null]
    //     0xa1c86c: add             x4, PP, #0x56, lsl #12  ; [pp+0x56828] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "container", 0x1, "customSemanticsActions", 0x2, Null]
    //     0xa1c870: ldr             x4, [x4, #0x828]
    // 0xa1c874: r0 = Semantics()
    //     0xa1c874: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0xa1c878: ldur            x0, [fp, #-8]
    // 0xa1c87c: LeaveFrame
    //     0xa1c87c: mov             SP, fp
    //     0xa1c880: ldp             fp, lr, [SP], #0x10
    // 0xa1c884: ret
    //     0xa1c884: ret             
    // 0xa1c888: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1c888: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1c88c: b               #0xa1c5e0
    // 0xa1c890: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c890: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1c894: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c894: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1c898: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c898: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1c89c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c89c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void moveToEnd(dynamic) {
    // ** addr: 0xa1c8a0, size: 0xd8
    // 0xa1c8a0: EnterFrame
    //     0xa1c8a0: stp             fp, lr, [SP, #-0x10]!
    //     0xa1c8a4: mov             fp, SP
    // 0xa1c8a8: AllocStack(0x18)
    //     0xa1c8a8: sub             SP, SP, #0x18
    // 0xa1c8ac: SetupParameters()
    //     0xa1c8ac: ldr             x0, [fp, #0x10]
    //     0xa1c8b0: ldur            w1, [x0, #0x17]
    //     0xa1c8b4: add             x1, x1, HEAP, lsl #32
    // 0xa1c8b8: CheckStackOverflow
    //     0xa1c8b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1c8bc: cmp             SP, x16
    //     0xa1c8c0: b.ls            #0xa1c968
    // 0xa1c8c4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa1c8c4: ldur            w0, [x1, #0x17]
    // 0xa1c8c8: DecompressPointer r0
    //     0xa1c8c8: add             x0, x0, HEAP, lsl #32
    // 0xa1c8cc: LoadField: r2 = r1->field_13
    //     0xa1c8cc: ldur            w2, [x1, #0x13]
    // 0xa1c8d0: DecompressPointer r2
    //     0xa1c8d0: add             x2, x2, HEAP, lsl #32
    // 0xa1c8d4: LoadField: r3 = r1->field_f
    //     0xa1c8d4: ldur            w3, [x1, #0xf]
    // 0xa1c8d8: DecompressPointer r3
    //     0xa1c8d8: add             x3, x3, HEAP, lsl #32
    // 0xa1c8dc: LoadField: r1 = r3->field_b
    //     0xa1c8dc: ldur            w1, [x3, #0xb]
    // 0xa1c8e0: DecompressPointer r1
    //     0xa1c8e0: add             x1, x1, HEAP, lsl #32
    // 0xa1c8e4: cmp             w1, NULL
    // 0xa1c8e8: b.eq            #0xa1c970
    // 0xa1c8ec: LoadField: r3 = r1->field_13
    //     0xa1c8ec: ldur            x3, [x1, #0x13]
    // 0xa1c8f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa1c8f0: ldur            w1, [x0, #0x17]
    // 0xa1c8f4: DecompressPointer r1
    //     0xa1c8f4: add             x1, x1, HEAP, lsl #32
    // 0xa1c8f8: r0 = LoadInt32Instr(r2)
    //     0xa1c8f8: sbfx            x0, x2, #1, #0x1f
    //     0xa1c8fc: tbz             w2, #0, #0xa1c904
    //     0xa1c900: ldur            x0, [x2, #7]
    // 0xa1c904: cmp             x0, x3
    // 0xa1c908: b.eq            #0xa1c958
    // 0xa1c90c: LoadField: r0 = r1->field_f
    //     0xa1c90c: ldur            w0, [x1, #0xf]
    // 0xa1c910: DecompressPointer r0
    //     0xa1c910: add             x0, x0, HEAP, lsl #32
    // 0xa1c914: LoadField: r1 = r0->field_b
    //     0xa1c914: ldur            w1, [x0, #0xb]
    // 0xa1c918: DecompressPointer r1
    //     0xa1c918: add             x1, x1, HEAP, lsl #32
    // 0xa1c91c: cmp             w1, NULL
    // 0xa1c920: b.eq            #0xa1c974
    // 0xa1c924: LoadField: r4 = r1->field_1b
    //     0xa1c924: ldur            w4, [x1, #0x1b]
    // 0xa1c928: DecompressPointer r4
    //     0xa1c928: add             x4, x4, HEAP, lsl #32
    // 0xa1c92c: r0 = BoxInt64Instr(r3)
    //     0xa1c92c: sbfiz           x0, x3, #1, #0x1f
    //     0xa1c930: cmp             x3, x0, asr #1
    //     0xa1c934: b.eq            #0xa1c940
    //     0xa1c938: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa1c93c: stur            x3, [x0, #7]
    // 0xa1c940: stp             x2, x4, [SP, #8]
    // 0xa1c944: str             x0, [SP]
    // 0xa1c948: mov             x0, x4
    // 0xa1c94c: ClosureCall
    //     0xa1c94c: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xa1c950: ldur            x2, [x0, #0x1f]
    //     0xa1c954: blr             x2
    // 0xa1c958: r0 = Null
    //     0xa1c958: mov             x0, NULL
    // 0xa1c95c: LeaveFrame
    //     0xa1c95c: mov             SP, fp
    //     0xa1c960: ldp             fp, lr, [SP], #0x10
    // 0xa1c964: ret
    //     0xa1c964: ret             
    // 0xa1c968: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1c968: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1c96c: b               #0xa1c8c4
    // 0xa1c970: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c970: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1c974: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c974: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void moveAfter(dynamic) {
    // ** addr: 0xa1c978, size: 0xbc
    // 0xa1c978: EnterFrame
    //     0xa1c978: stp             fp, lr, [SP, #-0x10]!
    //     0xa1c97c: mov             fp, SP
    // 0xa1c980: AllocStack(0x18)
    //     0xa1c980: sub             SP, SP, #0x18
    // 0xa1c984: SetupParameters()
    //     0xa1c984: ldr             x0, [fp, #0x10]
    //     0xa1c988: ldur            w1, [x0, #0x17]
    //     0xa1c98c: add             x1, x1, HEAP, lsl #32
    // 0xa1c990: CheckStackOverflow
    //     0xa1c990: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1c994: cmp             SP, x16
    //     0xa1c998: b.ls            #0xa1ca28
    // 0xa1c99c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa1c99c: ldur            w0, [x1, #0x17]
    // 0xa1c9a0: DecompressPointer r0
    //     0xa1c9a0: add             x0, x0, HEAP, lsl #32
    // 0xa1c9a4: LoadField: r2 = r1->field_13
    //     0xa1c9a4: ldur            w2, [x1, #0x13]
    // 0xa1c9a8: DecompressPointer r2
    //     0xa1c9a8: add             x2, x2, HEAP, lsl #32
    // 0xa1c9ac: r1 = LoadInt32Instr(r2)
    //     0xa1c9ac: sbfx            x1, x2, #1, #0x1f
    //     0xa1c9b0: tbz             w2, #0, #0xa1c9b8
    //     0xa1c9b4: ldur            x1, [x2, #7]
    // 0xa1c9b8: add             x3, x1, #2
    // 0xa1c9bc: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa1c9bc: ldur            w4, [x0, #0x17]
    // 0xa1c9c0: DecompressPointer r4
    //     0xa1c9c0: add             x4, x4, HEAP, lsl #32
    // 0xa1c9c4: cmp             x1, x3
    // 0xa1c9c8: b.eq            #0xa1ca18
    // 0xa1c9cc: LoadField: r0 = r4->field_f
    //     0xa1c9cc: ldur            w0, [x4, #0xf]
    // 0xa1c9d0: DecompressPointer r0
    //     0xa1c9d0: add             x0, x0, HEAP, lsl #32
    // 0xa1c9d4: LoadField: r1 = r0->field_b
    //     0xa1c9d4: ldur            w1, [x0, #0xb]
    // 0xa1c9d8: DecompressPointer r1
    //     0xa1c9d8: add             x1, x1, HEAP, lsl #32
    // 0xa1c9dc: cmp             w1, NULL
    // 0xa1c9e0: b.eq            #0xa1ca30
    // 0xa1c9e4: LoadField: r4 = r1->field_1b
    //     0xa1c9e4: ldur            w4, [x1, #0x1b]
    // 0xa1c9e8: DecompressPointer r4
    //     0xa1c9e8: add             x4, x4, HEAP, lsl #32
    // 0xa1c9ec: r0 = BoxInt64Instr(r3)
    //     0xa1c9ec: sbfiz           x0, x3, #1, #0x1f
    //     0xa1c9f0: cmp             x3, x0, asr #1
    //     0xa1c9f4: b.eq            #0xa1ca00
    //     0xa1c9f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa1c9fc: stur            x3, [x0, #7]
    // 0xa1ca00: stp             x2, x4, [SP, #8]
    // 0xa1ca04: str             x0, [SP]
    // 0xa1ca08: mov             x0, x4
    // 0xa1ca0c: ClosureCall
    //     0xa1ca0c: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xa1ca10: ldur            x2, [x0, #0x1f]
    //     0xa1ca14: blr             x2
    // 0xa1ca18: r0 = Null
    //     0xa1ca18: mov             x0, NULL
    // 0xa1ca1c: LeaveFrame
    //     0xa1ca1c: mov             SP, fp
    //     0xa1ca20: ldp             fp, lr, [SP], #0x10
    // 0xa1ca24: ret
    //     0xa1ca24: ret             
    // 0xa1ca28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1ca28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1ca2c: b               #0xa1c99c
    // 0xa1ca30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1ca30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void moveBefore(dynamic) {
    // ** addr: 0xa1ca34, size: 0xbc
    // 0xa1ca34: EnterFrame
    //     0xa1ca34: stp             fp, lr, [SP, #-0x10]!
    //     0xa1ca38: mov             fp, SP
    // 0xa1ca3c: AllocStack(0x18)
    //     0xa1ca3c: sub             SP, SP, #0x18
    // 0xa1ca40: SetupParameters()
    //     0xa1ca40: ldr             x0, [fp, #0x10]
    //     0xa1ca44: ldur            w1, [x0, #0x17]
    //     0xa1ca48: add             x1, x1, HEAP, lsl #32
    // 0xa1ca4c: CheckStackOverflow
    //     0xa1ca4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1ca50: cmp             SP, x16
    //     0xa1ca54: b.ls            #0xa1cae4
    // 0xa1ca58: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa1ca58: ldur            w0, [x1, #0x17]
    // 0xa1ca5c: DecompressPointer r0
    //     0xa1ca5c: add             x0, x0, HEAP, lsl #32
    // 0xa1ca60: LoadField: r2 = r1->field_13
    //     0xa1ca60: ldur            w2, [x1, #0x13]
    // 0xa1ca64: DecompressPointer r2
    //     0xa1ca64: add             x2, x2, HEAP, lsl #32
    // 0xa1ca68: r1 = LoadInt32Instr(r2)
    //     0xa1ca68: sbfx            x1, x2, #1, #0x1f
    //     0xa1ca6c: tbz             w2, #0, #0xa1ca74
    //     0xa1ca70: ldur            x1, [x2, #7]
    // 0xa1ca74: sub             x3, x1, #1
    // 0xa1ca78: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa1ca78: ldur            w4, [x0, #0x17]
    // 0xa1ca7c: DecompressPointer r4
    //     0xa1ca7c: add             x4, x4, HEAP, lsl #32
    // 0xa1ca80: cmp             x1, x3
    // 0xa1ca84: b.eq            #0xa1cad4
    // 0xa1ca88: LoadField: r0 = r4->field_f
    //     0xa1ca88: ldur            w0, [x4, #0xf]
    // 0xa1ca8c: DecompressPointer r0
    //     0xa1ca8c: add             x0, x0, HEAP, lsl #32
    // 0xa1ca90: LoadField: r1 = r0->field_b
    //     0xa1ca90: ldur            w1, [x0, #0xb]
    // 0xa1ca94: DecompressPointer r1
    //     0xa1ca94: add             x1, x1, HEAP, lsl #32
    // 0xa1ca98: cmp             w1, NULL
    // 0xa1ca9c: b.eq            #0xa1caec
    // 0xa1caa0: LoadField: r4 = r1->field_1b
    //     0xa1caa0: ldur            w4, [x1, #0x1b]
    // 0xa1caa4: DecompressPointer r4
    //     0xa1caa4: add             x4, x4, HEAP, lsl #32
    // 0xa1caa8: r0 = BoxInt64Instr(r3)
    //     0xa1caa8: sbfiz           x0, x3, #1, #0x1f
    //     0xa1caac: cmp             x3, x0, asr #1
    //     0xa1cab0: b.eq            #0xa1cabc
    //     0xa1cab4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa1cab8: stur            x3, [x0, #7]
    // 0xa1cabc: stp             x2, x4, [SP, #8]
    // 0xa1cac0: str             x0, [SP]
    // 0xa1cac4: mov             x0, x4
    // 0xa1cac8: ClosureCall
    //     0xa1cac8: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xa1cacc: ldur            x2, [x0, #0x1f]
    //     0xa1cad0: blr             x2
    // 0xa1cad4: r0 = Null
    //     0xa1cad4: mov             x0, NULL
    // 0xa1cad8: LeaveFrame
    //     0xa1cad8: mov             SP, fp
    //     0xa1cadc: ldp             fp, lr, [SP], #0x10
    // 0xa1cae0: ret
    //     0xa1cae0: ret             
    // 0xa1cae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1cae4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1cae8: b               #0xa1ca58
    // 0xa1caec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1caec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void moveToStart(dynamic) {
    // ** addr: 0xa1caf0, size: 0x90
    // 0xa1caf0: EnterFrame
    //     0xa1caf0: stp             fp, lr, [SP, #-0x10]!
    //     0xa1caf4: mov             fp, SP
    // 0xa1caf8: AllocStack(0x18)
    //     0xa1caf8: sub             SP, SP, #0x18
    // 0xa1cafc: SetupParameters()
    //     0xa1cafc: ldr             x0, [fp, #0x10]
    //     0xa1cb00: ldur            w1, [x0, #0x17]
    //     0xa1cb04: add             x1, x1, HEAP, lsl #32
    // 0xa1cb08: CheckStackOverflow
    //     0xa1cb08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1cb0c: cmp             SP, x16
    //     0xa1cb10: b.ls            #0xa1cb74
    // 0xa1cb14: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa1cb14: ldur            w0, [x1, #0x17]
    // 0xa1cb18: DecompressPointer r0
    //     0xa1cb18: add             x0, x0, HEAP, lsl #32
    // 0xa1cb1c: LoadField: r2 = r1->field_13
    //     0xa1cb1c: ldur            w2, [x1, #0x13]
    // 0xa1cb20: DecompressPointer r2
    //     0xa1cb20: add             x2, x2, HEAP, lsl #32
    // 0xa1cb24: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa1cb24: ldur            w1, [x0, #0x17]
    // 0xa1cb28: DecompressPointer r1
    //     0xa1cb28: add             x1, x1, HEAP, lsl #32
    // 0xa1cb2c: cbz             w2, #0xa1cb64
    // 0xa1cb30: LoadField: r0 = r1->field_f
    //     0xa1cb30: ldur            w0, [x1, #0xf]
    // 0xa1cb34: DecompressPointer r0
    //     0xa1cb34: add             x0, x0, HEAP, lsl #32
    // 0xa1cb38: LoadField: r1 = r0->field_b
    //     0xa1cb38: ldur            w1, [x0, #0xb]
    // 0xa1cb3c: DecompressPointer r1
    //     0xa1cb3c: add             x1, x1, HEAP, lsl #32
    // 0xa1cb40: cmp             w1, NULL
    // 0xa1cb44: b.eq            #0xa1cb7c
    // 0xa1cb48: LoadField: r0 = r1->field_1b
    //     0xa1cb48: ldur            w0, [x1, #0x1b]
    // 0xa1cb4c: DecompressPointer r0
    //     0xa1cb4c: add             x0, x0, HEAP, lsl #32
    // 0xa1cb50: stp             x2, x0, [SP, #8]
    // 0xa1cb54: str             xzr, [SP]
    // 0xa1cb58: ClosureCall
    //     0xa1cb58: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xa1cb5c: ldur            x2, [x0, #0x1f]
    //     0xa1cb60: blr             x2
    // 0xa1cb64: r0 = Null
    //     0xa1cb64: mov             x0, NULL
    // 0xa1cb68: LeaveFrame
    //     0xa1cb68: mov             SP, fp
    //     0xa1cb6c: ldp             fp, lr, [SP], #0x10
    // 0xa1cb70: ret
    //     0xa1cb70: ret             
    // 0xa1cb74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1cb74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1cb78: b               #0xa1cb14
    // 0xa1cb7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1cb7c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void reorder(dynamic, int, int) {
    // ** addr: 0xa1cb80, size: 0xa0
    // 0xa1cb80: EnterFrame
    //     0xa1cb80: stp             fp, lr, [SP, #-0x10]!
    //     0xa1cb84: mov             fp, SP
    // 0xa1cb88: AllocStack(0x18)
    //     0xa1cb88: sub             SP, SP, #0x18
    // 0xa1cb8c: SetupParameters()
    //     0xa1cb8c: ldr             x0, [fp, #0x20]
    //     0xa1cb90: ldur            w1, [x0, #0x17]
    //     0xa1cb94: add             x1, x1, HEAP, lsl #32
    // 0xa1cb98: CheckStackOverflow
    //     0xa1cb98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1cb9c: cmp             SP, x16
    //     0xa1cba0: b.ls            #0xa1cc14
    // 0xa1cba4: ldr             x0, [fp, #0x18]
    // 0xa1cba8: r2 = LoadInt32Instr(r0)
    //     0xa1cba8: sbfx            x2, x0, #1, #0x1f
    //     0xa1cbac: tbz             w0, #0, #0xa1cbb4
    //     0xa1cbb0: ldur            x2, [x0, #7]
    // 0xa1cbb4: ldr             x3, [fp, #0x10]
    // 0xa1cbb8: r4 = LoadInt32Instr(r3)
    //     0xa1cbb8: sbfx            x4, x3, #1, #0x1f
    //     0xa1cbbc: tbz             w3, #0, #0xa1cbc4
    //     0xa1cbc0: ldur            x4, [x3, #7]
    // 0xa1cbc4: cmp             x2, x4
    // 0xa1cbc8: b.eq            #0xa1cc04
    // 0xa1cbcc: LoadField: r2 = r1->field_f
    //     0xa1cbcc: ldur            w2, [x1, #0xf]
    // 0xa1cbd0: DecompressPointer r2
    //     0xa1cbd0: add             x2, x2, HEAP, lsl #32
    // 0xa1cbd4: LoadField: r1 = r2->field_b
    //     0xa1cbd4: ldur            w1, [x2, #0xb]
    // 0xa1cbd8: DecompressPointer r1
    //     0xa1cbd8: add             x1, x1, HEAP, lsl #32
    // 0xa1cbdc: cmp             w1, NULL
    // 0xa1cbe0: b.eq            #0xa1cc1c
    // 0xa1cbe4: LoadField: r2 = r1->field_1b
    //     0xa1cbe4: ldur            w2, [x1, #0x1b]
    // 0xa1cbe8: DecompressPointer r2
    //     0xa1cbe8: add             x2, x2, HEAP, lsl #32
    // 0xa1cbec: stp             x0, x2, [SP, #8]
    // 0xa1cbf0: str             x3, [SP]
    // 0xa1cbf4: mov             x0, x2
    // 0xa1cbf8: ClosureCall
    //     0xa1cbf8: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xa1cbfc: ldur            x2, [x0, #0x1f]
    //     0xa1cc00: blr             x2
    // 0xa1cc04: r0 = Null
    //     0xa1cc04: mov             x0, NULL
    // 0xa1cc08: LeaveFrame
    //     0xa1cc08: mov             SP, fp
    //     0xa1cc0c: ldp             fp, lr, [SP], #0x10
    // 0xa1cc10: ret
    //     0xa1cc10: ret             
    // 0xa1cc14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1cc14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1cc18: b               #0xa1cba4
    // 0xa1cc1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1cc1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa80914, size: 0x60
    // 0xa80914: EnterFrame
    //     0xa80914: stp             fp, lr, [SP, #-0x10]!
    //     0xa80918: mov             fp, SP
    // 0xa8091c: AllocStack(0x8)
    //     0xa8091c: sub             SP, SP, #8
    // 0xa80920: SetupParameters(SliverReorderableListState this /* r1 => r0, fp-0x8 */)
    //     0xa80920: mov             x0, x1
    //     0xa80924: stur            x1, [fp, #-8]
    // 0xa80928: CheckStackOverflow
    //     0xa80928: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8092c: cmp             SP, x16
    //     0xa80930: b.ls            #0xa8096c
    // 0xa80934: mov             x1, x0
    // 0xa80938: r0 = _dragReset()
    //     0xa80938: bl              #0x995fec  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragReset
    // 0xa8093c: ldur            x0, [fp, #-8]
    // 0xa80940: LoadField: r1 = r0->field_33
    //     0xa80940: ldur            w1, [x0, #0x33]
    // 0xa80944: DecompressPointer r1
    //     0xa80944: add             x1, x1, HEAP, lsl #32
    // 0xa80948: cmp             w1, NULL
    // 0xa8094c: b.eq            #0xa80954
    // 0xa80950: r0 = dispose()
    //     0xa80950: bl              #0x7f95b4  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::dispose
    // 0xa80954: ldur            x1, [fp, #-8]
    // 0xa80958: r0 = dispose()
    //     0xa80958: bl              #0xa80974  ; [package:flutter/src/widgets/reorderable_list.dart] _SliverReorderableListState&State&TickerProviderStateMixin::dispose
    // 0xa8095c: r0 = Null
    //     0xa8095c: mov             x0, NULL
    // 0xa80960: LeaveFrame
    //     0xa80960: mov             SP, fp
    //     0xa80964: ldp             fp, lr, [SP], #0x10
    // 0xa80968: ret
    //     0xa80968: ret             
    // 0xa8096c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8096c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa80970: b               #0xa80934
  }
  _ startItemDragReorder(/* No info */) {
    // ** addr: 0xaa4f9c, size: 0x98
    // 0xaa4f9c: EnterFrame
    //     0xaa4f9c: stp             fp, lr, [SP, #-0x10]!
    //     0xaa4fa0: mov             fp, SP
    // 0xaa4fa4: AllocStack(0x20)
    //     0xaa4fa4: sub             SP, SP, #0x20
    // 0xaa4fa8: SetupParameters(SliverReorderableListState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xaa4fa8: stur            x1, [fp, #-8]
    //     0xaa4fac: stur            x2, [fp, #-0x10]
    //     0xaa4fb0: stur            x3, [fp, #-0x18]
    //     0xaa4fb4: stur            x5, [fp, #-0x20]
    // 0xaa4fb8: CheckStackOverflow
    //     0xaa4fb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa4fbc: cmp             SP, x16
    //     0xaa4fc0: b.ls            #0xaa502c
    // 0xaa4fc4: r1 = 4
    //     0xaa4fc4: movz            x1, #0x4
    // 0xaa4fc8: r0 = AllocateContext()
    //     0xaa4fc8: bl              #0xec126c  ; AllocateContextStub
    // 0xaa4fcc: mov             x2, x0
    // 0xaa4fd0: ldur            x3, [fp, #-8]
    // 0xaa4fd4: StoreField: r2->field_f = r3
    //     0xaa4fd4: stur            w3, [x2, #0xf]
    // 0xaa4fd8: ldur            x0, [fp, #-0x10]
    // 0xaa4fdc: StoreField: r2->field_13 = r0
    //     0xaa4fdc: stur            w0, [x2, #0x13]
    // 0xaa4fe0: ldur            x4, [fp, #-0x18]
    // 0xaa4fe4: r0 = BoxInt64Instr(r4)
    //     0xaa4fe4: sbfiz           x0, x4, #1, #0x1f
    //     0xaa4fe8: cmp             x4, x0, asr #1
    //     0xaa4fec: b.eq            #0xaa4ff8
    //     0xaa4ff0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaa4ff4: stur            x4, [x0, #7]
    // 0xaa4ff8: ArrayStore: r2[0] = r0  ; List_4
    //     0xaa4ff8: stur            w0, [x2, #0x17]
    // 0xaa4ffc: ldur            x0, [fp, #-0x20]
    // 0xaa5000: StoreField: r2->field_1b = r0
    //     0xaa5000: stur            w0, [x2, #0x1b]
    // 0xaa5004: r1 = Function '<anonymous closure>':.
    //     0xaa5004: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fcb8] AnonymousClosure: (0xaa5034), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::startItemDragReorder (0xaa4f9c)
    //     0xaa5008: ldr             x1, [x1, #0xcb8]
    // 0xaa500c: r0 = AllocateClosure()
    //     0xaa500c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa5010: ldur            x1, [fp, #-8]
    // 0xaa5014: mov             x2, x0
    // 0xaa5018: r0 = setState()
    //     0xaa5018: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaa501c: r0 = Null
    //     0xaa501c: mov             x0, NULL
    // 0xaa5020: LeaveFrame
    //     0xaa5020: mov             SP, fp
    //     0xaa5024: ldp             fp, lr, [SP], #0x10
    // 0xaa5028: ret
    //     0xaa5028: ret             
    // 0xaa502c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa502c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa5030: b               #0xaa4fc4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaa5034, size: 0x2ac
    // 0xaa5034: EnterFrame
    //     0xaa5034: stp             fp, lr, [SP, #-0x10]!
    //     0xaa5038: mov             fp, SP
    // 0xaa503c: AllocStack(0x18)
    //     0xaa503c: sub             SP, SP, #0x18
    // 0xaa5040: SetupParameters()
    //     0xaa5040: ldr             x0, [fp, #0x10]
    //     0xaa5044: ldur            w2, [x0, #0x17]
    //     0xaa5048: add             x2, x2, HEAP, lsl #32
    //     0xaa504c: stur            x2, [fp, #-8]
    // 0xaa5050: CheckStackOverflow
    //     0xaa5050: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa5054: cmp             SP, x16
    //     0xaa5058: b.ls            #0xaa52d4
    // 0xaa505c: LoadField: r1 = r2->field_f
    //     0xaa505c: ldur            w1, [x2, #0xf]
    // 0xaa5060: DecompressPointer r1
    //     0xaa5060: add             x1, x1, HEAP, lsl #32
    // 0xaa5064: LoadField: r0 = r1->field_27
    //     0xaa5064: ldur            w0, [x1, #0x27]
    // 0xaa5068: DecompressPointer r0
    //     0xaa5068: add             x0, x0, HEAP, lsl #32
    // 0xaa506c: cmp             w0, NULL
    // 0xaa5070: b.eq            #0xaa5080
    // 0xaa5074: r0 = cancelReorder()
    //     0xaa5074: bl              #0x995e24  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::cancelReorder
    // 0xaa5078: ldur            x0, [fp, #-8]
    // 0xaa507c: b               #0xaa5164
    // 0xaa5080: LoadField: r0 = r1->field_33
    //     0xaa5080: ldur            w0, [x1, #0x33]
    // 0xaa5084: DecompressPointer r0
    //     0xaa5084: add             x0, x0, HEAP, lsl #32
    // 0xaa5088: cmp             w0, NULL
    // 0xaa508c: b.eq            #0xaa5160
    // 0xaa5090: ldur            x2, [fp, #-8]
    // 0xaa5094: LoadField: r3 = r1->field_37
    //     0xaa5094: ldur            w3, [x1, #0x37]
    // 0xaa5098: DecompressPointer r3
    //     0xaa5098: add             x3, x3, HEAP, lsl #32
    // 0xaa509c: stur            x3, [fp, #-0x10]
    // 0xaa50a0: LoadField: r1 = r2->field_13
    //     0xaa50a0: ldur            w1, [x2, #0x13]
    // 0xaa50a4: DecompressPointer r1
    //     0xaa50a4: add             x1, x1, HEAP, lsl #32
    // 0xaa50a8: r0 = LoadClassIdInstr(r1)
    //     0xaa50a8: ldur            x0, [x1, #-1]
    //     0xaa50ac: ubfx            x0, x0, #0xc, #0x14
    // 0xaa50b0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xaa50b0: sub             lr, x0, #1, lsl #12
    //     0xaa50b4: ldr             lr, [x21, lr, lsl #3]
    //     0xaa50b8: blr             lr
    // 0xaa50bc: mov             x2, x0
    // 0xaa50c0: r0 = BoxInt64Instr(r2)
    //     0xaa50c0: sbfiz           x0, x2, #1, #0x1f
    //     0xaa50c4: cmp             x2, x0, asr #1
    //     0xaa50c8: b.eq            #0xaa50d4
    //     0xaa50cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaa50d0: stur            x2, [x0, #7]
    // 0xaa50d4: mov             x1, x0
    // 0xaa50d8: ldur            x0, [fp, #-0x10]
    // 0xaa50dc: cmp             w0, w1
    // 0xaa50e0: b.eq            #0xaa5158
    // 0xaa50e4: and             w16, w0, w1
    // 0xaa50e8: branchIfSmi(r16, 0xaa511c)
    //     0xaa50e8: tbz             w16, #0, #0xaa511c
    // 0xaa50ec: r16 = LoadClassIdInstr(r0)
    //     0xaa50ec: ldur            x16, [x0, #-1]
    //     0xaa50f0: ubfx            x16, x16, #0xc, #0x14
    // 0xaa50f4: cmp             x16, #0x3d
    // 0xaa50f8: b.ne            #0xaa511c
    // 0xaa50fc: r16 = LoadClassIdInstr(r1)
    //     0xaa50fc: ldur            x16, [x1, #-1]
    //     0xaa5100: ubfx            x16, x16, #0xc, #0x14
    // 0xaa5104: cmp             x16, #0x3d
    // 0xaa5108: b.ne            #0xaa511c
    // 0xaa510c: LoadField: r16 = r0->field_7
    //     0xaa510c: ldur            x16, [x0, #7]
    // 0xaa5110: LoadField: r17 = r1->field_7
    //     0xaa5110: ldur            x17, [x1, #7]
    // 0xaa5114: cmp             x16, x17
    // 0xaa5118: b.eq            #0xaa5158
    // 0xaa511c: ldur            x0, [fp, #-8]
    // 0xaa5120: LoadField: r1 = r0->field_f
    //     0xaa5120: ldur            w1, [x0, #0xf]
    // 0xaa5124: DecompressPointer r1
    //     0xaa5124: add             x1, x1, HEAP, lsl #32
    // 0xaa5128: LoadField: r2 = r1->field_33
    //     0xaa5128: ldur            w2, [x1, #0x33]
    // 0xaa512c: DecompressPointer r2
    //     0xaa512c: add             x2, x2, HEAP, lsl #32
    // 0xaa5130: cmp             w2, NULL
    // 0xaa5134: b.eq            #0xaa52dc
    // 0xaa5138: mov             x1, x2
    // 0xaa513c: r0 = dispose()
    //     0xaa513c: bl              #0x7f95b4  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::dispose
    // 0xaa5140: ldur            x0, [fp, #-8]
    // 0xaa5144: LoadField: r1 = r0->field_f
    //     0xaa5144: ldur            w1, [x0, #0xf]
    // 0xaa5148: DecompressPointer r1
    //     0xaa5148: add             x1, x1, HEAP, lsl #32
    // 0xaa514c: StoreField: r1->field_33 = rNULL
    //     0xaa514c: stur            NULL, [x1, #0x33]
    // 0xaa5150: StoreField: r1->field_37 = rNULL
    //     0xaa5150: stur            NULL, [x1, #0x37]
    // 0xaa5154: b               #0xaa5164
    // 0xaa5158: ldur            x0, [fp, #-8]
    // 0xaa515c: b               #0xaa5164
    // 0xaa5160: ldur            x0, [fp, #-8]
    // 0xaa5164: LoadField: r1 = r0->field_f
    //     0xaa5164: ldur            w1, [x0, #0xf]
    // 0xaa5168: DecompressPointer r1
    //     0xaa5168: add             x1, x1, HEAP, lsl #32
    // 0xaa516c: LoadField: r2 = r1->field_1b
    //     0xaa516c: ldur            w2, [x1, #0x1b]
    // 0xaa5170: DecompressPointer r2
    //     0xaa5170: add             x2, x2, HEAP, lsl #32
    // 0xaa5174: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa5174: ldur            w1, [x0, #0x17]
    // 0xaa5178: DecompressPointer r1
    //     0xaa5178: add             x1, x1, HEAP, lsl #32
    // 0xaa517c: mov             x16, x1
    // 0xaa5180: mov             x1, x2
    // 0xaa5184: mov             x2, x16
    // 0xaa5188: r0 = containsKey()
    //     0xaa5188: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xaa518c: tbnz            w0, #4, #0xaa52b4
    // 0xaa5190: ldur            x3, [fp, #-8]
    // 0xaa5194: LoadField: r4 = r3->field_f
    //     0xaa5194: ldur            w4, [x3, #0xf]
    // 0xaa5198: DecompressPointer r4
    //     0xaa5198: add             x4, x4, HEAP, lsl #32
    // 0xaa519c: stur            x4, [fp, #-0x18]
    // 0xaa51a0: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xaa51a0: ldur            w0, [x3, #0x17]
    // 0xaa51a4: DecompressPointer r0
    //     0xaa51a4: add             x0, x0, HEAP, lsl #32
    // 0xaa51a8: StoreField: r4->field_23 = r0
    //     0xaa51a8: stur            w0, [x4, #0x23]
    //     0xaa51ac: tbz             w0, #0, #0xaa51c8
    //     0xaa51b0: ldurb           w16, [x4, #-1]
    //     0xaa51b4: ldurb           w17, [x0, #-1]
    //     0xaa51b8: and             x16, x17, x16, lsr #2
    //     0xaa51bc: tst             x16, HEAP, lsr #32
    //     0xaa51c0: b.eq            #0xaa51c8
    //     0xaa51c4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa51c8: LoadField: r0 = r3->field_1b
    //     0xaa51c8: ldur            w0, [x3, #0x1b]
    // 0xaa51cc: DecompressPointer r0
    //     0xaa51cc: add             x0, x0, HEAP, lsl #32
    // 0xaa51d0: mov             x2, x4
    // 0xaa51d4: stur            x0, [fp, #-0x10]
    // 0xaa51d8: r1 = Function '_dragStart@318218688':.
    //     0xaa51d8: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fcc0] AnonymousClosure: (0xaa52e0), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragStart (0xaa531c)
    //     0xaa51dc: ldr             x1, [x1, #0xcc0]
    // 0xaa51e0: r0 = AllocateClosure()
    //     0xaa51e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa51e4: ldur            x3, [fp, #-0x10]
    // 0xaa51e8: ArrayStore: r3[0] = r0  ; List_4
    //     0xaa51e8: stur            w0, [x3, #0x17]
    //     0xaa51ec: ldurb           w16, [x3, #-1]
    //     0xaa51f0: ldurb           w17, [x0, #-1]
    //     0xaa51f4: and             x16, x17, x16, lsr #2
    //     0xaa51f8: tst             x16, HEAP, lsr #32
    //     0xaa51fc: b.eq            #0xaa5204
    //     0xaa5200: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaa5204: ldur            x0, [fp, #-8]
    // 0xaa5208: LoadField: r2 = r0->field_13
    //     0xaa5208: ldur            w2, [x0, #0x13]
    // 0xaa520c: DecompressPointer r2
    //     0xaa520c: add             x2, x2, HEAP, lsl #32
    // 0xaa5210: mov             x1, x3
    // 0xaa5214: r0 = addPointer()
    //     0xaa5214: bl              #0x802d08  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::addPointer
    // 0xaa5218: ldur            x0, [fp, #-0x10]
    // 0xaa521c: ldur            x1, [fp, #-0x18]
    // 0xaa5220: StoreField: r1->field_33 = r0
    //     0xaa5220: stur            w0, [x1, #0x33]
    //     0xaa5224: ldurb           w16, [x1, #-1]
    //     0xaa5228: ldurb           w17, [x0, #-1]
    //     0xaa522c: and             x16, x17, x16, lsr #2
    //     0xaa5230: tst             x16, HEAP, lsr #32
    //     0xaa5234: b.eq            #0xaa523c
    //     0xaa5238: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa523c: ldur            x0, [fp, #-8]
    // 0xaa5240: LoadField: r2 = r0->field_f
    //     0xaa5240: ldur            w2, [x0, #0xf]
    // 0xaa5244: DecompressPointer r2
    //     0xaa5244: add             x2, x2, HEAP, lsl #32
    // 0xaa5248: stur            x2, [fp, #-0x10]
    // 0xaa524c: LoadField: r1 = r0->field_13
    //     0xaa524c: ldur            w1, [x0, #0x13]
    // 0xaa5250: DecompressPointer r1
    //     0xaa5250: add             x1, x1, HEAP, lsl #32
    // 0xaa5254: r0 = LoadClassIdInstr(r1)
    //     0xaa5254: ldur            x0, [x1, #-1]
    //     0xaa5258: ubfx            x0, x0, #0xc, #0x14
    // 0xaa525c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xaa525c: sub             lr, x0, #1, lsl #12
    //     0xaa5260: ldr             lr, [x21, lr, lsl #3]
    //     0xaa5264: blr             lr
    // 0xaa5268: mov             x2, x0
    // 0xaa526c: r0 = BoxInt64Instr(r2)
    //     0xaa526c: sbfiz           x0, x2, #1, #0x1f
    //     0xaa5270: cmp             x2, x0, asr #1
    //     0xaa5274: b.eq            #0xaa5280
    //     0xaa5278: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaa527c: stur            x2, [x0, #7]
    // 0xaa5280: ldur            x1, [fp, #-0x10]
    // 0xaa5284: StoreField: r1->field_37 = r0
    //     0xaa5284: stur            w0, [x1, #0x37]
    //     0xaa5288: tbz             w0, #0, #0xaa52a4
    //     0xaa528c: ldurb           w16, [x1, #-1]
    //     0xaa5290: ldurb           w17, [x0, #-1]
    //     0xaa5294: and             x16, x17, x16, lsr #2
    //     0xaa5298: tst             x16, HEAP, lsr #32
    //     0xaa529c: b.eq            #0xaa52a4
    //     0xaa52a0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa52a4: r0 = Null
    //     0xaa52a4: mov             x0, NULL
    // 0xaa52a8: LeaveFrame
    //     0xaa52a8: mov             SP, fp
    //     0xaa52ac: ldp             fp, lr, [SP], #0x10
    // 0xaa52b0: ret
    //     0xaa52b0: ret             
    // 0xaa52b4: r0 = _Exception()
    //     0xaa52b4: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0xaa52b8: mov             x1, x0
    // 0xaa52bc: r0 = "Attempting to start a drag on a non-visible item"
    //     0xaa52bc: add             x0, PP, #0x4f, lsl #12  ; [pp+0x4fcc8] "Attempting to start a drag on a non-visible item"
    //     0xaa52c0: ldr             x0, [x0, #0xcc8]
    // 0xaa52c4: StoreField: r1->field_7 = r0
    //     0xaa52c4: stur            w0, [x1, #7]
    // 0xaa52c8: mov             x0, x1
    // 0xaa52cc: r0 = Throw()
    //     0xaa52cc: bl              #0xec04b8  ; ThrowStub
    // 0xaa52d0: brk             #0
    // 0xaa52d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa52d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa52d8: b               #0xaa505c
    // 0xaa52dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa52dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Drag? _dragStart(dynamic, Offset) {
    // ** addr: 0xaa52e0, size: 0x3c
    // 0xaa52e0: EnterFrame
    //     0xaa52e0: stp             fp, lr, [SP, #-0x10]!
    //     0xaa52e4: mov             fp, SP
    // 0xaa52e8: ldr             x0, [fp, #0x18]
    // 0xaa52ec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa52ec: ldur            w1, [x0, #0x17]
    // 0xaa52f0: DecompressPointer r1
    //     0xaa52f0: add             x1, x1, HEAP, lsl #32
    // 0xaa52f4: CheckStackOverflow
    //     0xaa52f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa52f8: cmp             SP, x16
    //     0xaa52fc: b.ls            #0xaa5314
    // 0xaa5300: ldr             x2, [fp, #0x10]
    // 0xaa5304: r0 = _dragStart()
    //     0xaa5304: bl              #0xaa531c  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragStart
    // 0xaa5308: LeaveFrame
    //     0xaa5308: mov             SP, fp
    //     0xaa530c: ldp             fp, lr, [SP], #0x10
    // 0xaa5310: ret
    //     0xaa5310: ret             
    // 0xaa5314: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa5314: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa5318: b               #0xaa5300
  }
  _ _dragStart(/* No info */) {
    // ** addr: 0xaa531c, size: 0x4ac
    // 0xaa531c: EnterFrame
    //     0xaa531c: stp             fp, lr, [SP, #-0x10]!
    //     0xaa5320: mov             fp, SP
    // 0xaa5324: AllocStack(0x78)
    //     0xaa5324: sub             SP, SP, #0x78
    // 0xaa5328: SetupParameters(SliverReorderableListState this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xaa5328: mov             x3, x1
    //     0xaa532c: mov             x0, x2
    //     0xaa5330: stur            x1, [fp, #-0x10]
    //     0xaa5334: stur            x2, [fp, #-0x18]
    // 0xaa5338: CheckStackOverflow
    //     0xaa5338: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa533c: cmp             SP, x16
    //     0xaa5340: b.ls            #0xaa5774
    // 0xaa5344: LoadField: r4 = r3->field_1b
    //     0xaa5344: ldur            w4, [x3, #0x1b]
    // 0xaa5348: DecompressPointer r4
    //     0xaa5348: add             x4, x4, HEAP, lsl #32
    // 0xaa534c: stur            x4, [fp, #-8]
    // 0xaa5350: LoadField: r2 = r3->field_23
    //     0xaa5350: ldur            w2, [x3, #0x23]
    // 0xaa5354: DecompressPointer r2
    //     0xaa5354: add             x2, x2, HEAP, lsl #32
    // 0xaa5358: cmp             w2, NULL
    // 0xaa535c: b.eq            #0xaa577c
    // 0xaa5360: mov             x1, x4
    // 0xaa5364: r0 = _getValueOrData()
    //     0xaa5364: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xaa5368: mov             x1, x0
    // 0xaa536c: ldur            x0, [fp, #-8]
    // 0xaa5370: LoadField: r2 = r0->field_f
    //     0xaa5370: ldur            w2, [x0, #0xf]
    // 0xaa5374: DecompressPointer r2
    //     0xaa5374: add             x2, x2, HEAP, lsl #32
    // 0xaa5378: cmp             w2, w1
    // 0xaa537c: b.ne            #0xaa5388
    // 0xaa5380: r4 = Null
    //     0xaa5380: mov             x4, NULL
    // 0xaa5384: b               #0xaa538c
    // 0xaa5388: mov             x4, x1
    // 0xaa538c: ldur            x3, [fp, #-0x10]
    // 0xaa5390: stur            x4, [fp, #-0x20]
    // 0xaa5394: cmp             w4, NULL
    // 0xaa5398: b.eq            #0xaa5780
    // 0xaa539c: mov             x1, x4
    // 0xaa53a0: r2 = true
    //     0xaa53a0: add             x2, NULL, #0x20  ; true
    // 0xaa53a4: r0 = dragging=()
    //     0xaa53a4: bl              #0x945420  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::dragging=
    // 0xaa53a8: ldur            x0, [fp, #-0x10]
    // 0xaa53ac: LoadField: r1 = r0->field_b
    //     0xaa53ac: ldur            w1, [x0, #0xb]
    // 0xaa53b0: DecompressPointer r1
    //     0xaa53b0: add             x1, x1, HEAP, lsl #32
    // 0xaa53b4: cmp             w1, NULL
    // 0xaa53b8: b.eq            #0xaa5784
    // 0xaa53bc: ldur            x1, [fp, #-0x20]
    // 0xaa53c0: r0 = rebuild()
    //     0xaa53c0: bl              #0x945384  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::rebuild
    // 0xaa53c4: ldur            x3, [fp, #-0x20]
    // 0xaa53c8: LoadField: r0 = r3->field_b
    //     0xaa53c8: ldur            w0, [x3, #0xb]
    // 0xaa53cc: DecompressPointer r0
    //     0xaa53cc: add             x0, x0, HEAP, lsl #32
    // 0xaa53d0: cmp             w0, NULL
    // 0xaa53d4: b.eq            #0xaa5788
    // 0xaa53d8: LoadField: r2 = r0->field_b
    //     0xaa53d8: ldur            x2, [x0, #0xb]
    // 0xaa53dc: r0 = BoxInt64Instr(r2)
    //     0xaa53dc: sbfiz           x0, x2, #1, #0x1f
    //     0xaa53e0: cmp             x2, x0, asr #1
    //     0xaa53e4: b.eq            #0xaa53f0
    //     0xaa53e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaa53ec: stur            x2, [x0, #7]
    // 0xaa53f0: ldur            x2, [fp, #-0x10]
    // 0xaa53f4: StoreField: r2->field_2b = r0
    //     0xaa53f4: stur            w0, [x2, #0x2b]
    //     0xaa53f8: tbz             w0, #0, #0xaa5414
    //     0xaa53fc: ldurb           w16, [x2, #-1]
    //     0xaa5400: ldurb           w17, [x0, #-1]
    //     0xaa5404: and             x16, x17, x16, lsr #2
    //     0xaa5408: tst             x16, HEAP, lsr #32
    //     0xaa540c: b.eq            #0xaa5414
    //     0xaa5410: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xaa5414: mov             x1, x2
    // 0xaa5418: r0 = _scrollDirection()
    //     0xaa5418: bl              #0x9458e8  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_scrollDirection
    // 0xaa541c: mov             x3, x0
    // 0xaa5420: ldur            x0, [fp, #-0x10]
    // 0xaa5424: stur            x3, [fp, #-0x30]
    // 0xaa5428: LoadField: r1 = r0->field_b
    //     0xaa5428: ldur            w1, [x0, #0xb]
    // 0xaa542c: DecompressPointer r1
    //     0xaa542c: add             x1, x1, HEAP, lsl #32
    // 0xaa5430: cmp             w1, NULL
    // 0xaa5434: b.eq            #0xaa578c
    // 0xaa5438: LoadField: r4 = r1->field_27
    //     0xaa5438: ldur            w4, [x1, #0x27]
    // 0xaa543c: DecompressPointer r4
    //     0xaa543c: add             x4, x4, HEAP, lsl #32
    // 0xaa5440: mov             x2, x0
    // 0xaa5444: stur            x4, [fp, #-0x28]
    // 0xaa5448: r1 = Function '_dragUpdate@318218688':.
    //     0xaa5448: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fcd0] AnonymousClosure: (0xaa69e8), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragUpdate (0xaa6a2c)
    //     0xaa544c: ldr             x1, [x1, #0xcd0]
    // 0xaa5450: r0 = AllocateClosure()
    //     0xaa5450: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa5454: ldur            x2, [fp, #-0x10]
    // 0xaa5458: r1 = Function '_dragCancel@318218688':.
    //     0xaa5458: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fcd8] AnonymousClosure: (0xaa6948), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragCancel (0xaa6984)
    //     0xaa545c: ldr             x1, [x1, #0xcd8]
    // 0xaa5460: stur            x0, [fp, #-0x38]
    // 0xaa5464: r0 = AllocateClosure()
    //     0xaa5464: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa5468: ldur            x2, [fp, #-0x10]
    // 0xaa546c: r1 = Function '_dragEnd@318218688':.
    //     0xaa546c: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fce0] AnonymousClosure: (0xaa60dc), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragEnd (0xaa6118)
    //     0xaa5470: ldr             x1, [x1, #0xce0]
    // 0xaa5474: stur            x0, [fp, #-0x40]
    // 0xaa5478: r0 = AllocateClosure()
    //     0xaa5478: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa547c: ldur            x2, [fp, #-0x10]
    // 0xaa5480: r1 = Function '_dropCompleted@318218688':.
    //     0xaa5480: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fce8] AnonymousClosure: (0x995fb4), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dropCompleted (0x995ed0)
    //     0xaa5484: ldr             x1, [x1, #0xce8]
    // 0xaa5488: stur            x0, [fp, #-0x48]
    // 0xaa548c: r0 = AllocateClosure()
    //     0xaa548c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa5490: stur            x0, [fp, #-0x50]
    // 0xaa5494: r0 = _DragInfo()
    //     0xaa5494: bl              #0xaa5dc4  ; Allocate_DragInfoStub -> _DragInfo (size=0x4c)
    // 0xaa5498: stur            x0, [fp, #-0x58]
    // 0xaa549c: ldur            x16, [fp, #-0x38]
    // 0xaa54a0: ldur            lr, [fp, #-0x28]
    // 0xaa54a4: stp             lr, x16, [SP, #0x10]
    // 0xaa54a8: ldur            x16, [fp, #-0x30]
    // 0xaa54ac: ldur            lr, [fp, #-0x10]
    // 0xaa54b0: stp             lr, x16, [SP]
    // 0xaa54b4: mov             x1, x0
    // 0xaa54b8: ldur            x2, [fp, #-0x18]
    // 0xaa54bc: ldur            x3, [fp, #-0x20]
    // 0xaa54c0: ldur            x5, [fp, #-0x40]
    // 0xaa54c4: ldur            x6, [fp, #-0x50]
    // 0xaa54c8: ldur            x7, [fp, #-0x48]
    // 0xaa54cc: r0 = _DragInfo()
    //     0xaa54cc: bl              #0xaa5968  ; [package:flutter/src/widgets/reorderable_list.dart] _DragInfo::_DragInfo
    // 0xaa54d0: ldur            x0, [fp, #-0x58]
    // 0xaa54d4: ldur            x2, [fp, #-0x10]
    // 0xaa54d8: StoreField: r2->field_27 = r0
    //     0xaa54d8: stur            w0, [x2, #0x27]
    //     0xaa54dc: ldurb           w16, [x2, #-1]
    //     0xaa54e0: ldurb           w17, [x0, #-1]
    //     0xaa54e4: and             x16, x17, x16, lsr #2
    //     0xaa54e8: tst             x16, HEAP, lsr #32
    //     0xaa54ec: b.eq            #0xaa54f4
    //     0xaa54f0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xaa54f4: ldur            x1, [fp, #-0x58]
    // 0xaa54f8: r0 = startDrag()
    //     0xaa54f8: bl              #0xaa57c8  ; [package:flutter/src/widgets/reorderable_list.dart] _DragInfo::startDrag
    // 0xaa54fc: ldur            x0, [fp, #-0x10]
    // 0xaa5500: LoadField: r1 = r0->field_f
    //     0xaa5500: ldur            w1, [x0, #0xf]
    // 0xaa5504: DecompressPointer r1
    //     0xaa5504: add             x1, x1, HEAP, lsl #32
    // 0xaa5508: cmp             w1, NULL
    // 0xaa550c: b.eq            #0xaa5790
    // 0xaa5510: LoadField: r2 = r0->field_b
    //     0xaa5510: ldur            w2, [x0, #0xb]
    // 0xaa5514: DecompressPointer r2
    //     0xaa5514: add             x2, x2, HEAP, lsl #32
    // 0xaa5518: cmp             w2, NULL
    // 0xaa551c: b.eq            #0xaa5794
    // 0xaa5520: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaa5520: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaa5524: r0 = of()
    //     0xaa5524: bl              #0x6a5014  ; [package:flutter/src/widgets/overlay.dart] Overlay::of
    // 0xaa5528: mov             x3, x0
    // 0xaa552c: ldur            x0, [fp, #-0x10]
    // 0xaa5530: stur            x3, [fp, #-0x18]
    // 0xaa5534: LoadField: r2 = r0->field_27
    //     0xaa5534: ldur            w2, [x0, #0x27]
    // 0xaa5538: DecompressPointer r2
    //     0xaa5538: add             x2, x2, HEAP, lsl #32
    // 0xaa553c: cmp             w2, NULL
    // 0xaa5540: b.eq            #0xaa5798
    // 0xaa5544: r1 = Function 'createProxy':.
    //     0xaa5544: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fcf0] AnonymousClosure: (0xaa5dd0), in [package:flutter/src/widgets/reorderable_list.dart] _DragInfo::createProxy (0xaa5e0c)
    //     0xaa5548: ldr             x1, [x1, #0xcf0]
    // 0xaa554c: r0 = AllocateClosure()
    //     0xaa554c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa5550: stur            x0, [fp, #-0x28]
    // 0xaa5554: r0 = OverlayEntry()
    //     0xaa5554: bl              #0x6a5798  ; AllocateOverlayEntryStub -> OverlayEntry (size=0x28)
    // 0xaa5558: mov             x1, x0
    // 0xaa555c: ldur            x2, [fp, #-0x28]
    // 0xaa5560: stur            x0, [fp, #-0x28]
    // 0xaa5564: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaa5564: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaa5568: r0 = OverlayEntry()
    //     0xaa5568: bl              #0x6a55c8  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::OverlayEntry
    // 0xaa556c: ldur            x0, [fp, #-0x28]
    // 0xaa5570: ldur            x3, [fp, #-0x10]
    // 0xaa5574: StoreField: r3->field_1f = r0
    //     0xaa5574: stur            w0, [x3, #0x1f]
    //     0xaa5578: ldurb           w16, [x3, #-1]
    //     0xaa557c: ldurb           w17, [x0, #-1]
    //     0xaa5580: and             x16, x17, x16, lsr #2
    //     0xaa5584: tst             x16, HEAP, lsr #32
    //     0xaa5588: b.eq            #0xaa5590
    //     0xaa558c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaa5590: ldur            x1, [fp, #-0x18]
    // 0xaa5594: ldur            x2, [fp, #-0x28]
    // 0xaa5598: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaa5598: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaa559c: r0 = insert()
    //     0xaa559c: bl              #0x6a4e34  ; [package:flutter/src/widgets/overlay.dart] OverlayState::insert
    // 0xaa55a0: ldur            x0, [fp, #-8]
    // 0xaa55a4: LoadField: r2 = r0->field_7
    //     0xaa55a4: ldur            w2, [x0, #7]
    // 0xaa55a8: DecompressPointer r2
    //     0xaa55a8: add             x2, x2, HEAP, lsl #32
    // 0xaa55ac: r1 = Null
    //     0xaa55ac: mov             x1, NULL
    // 0xaa55b0: r3 = <X1>
    //     0xaa55b0: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0xaa55b4: r0 = Null
    //     0xaa55b4: mov             x0, NULL
    // 0xaa55b8: cmp             x2, x0
    // 0xaa55bc: b.eq            #0xaa55cc
    // 0xaa55c0: r30 = InstantiateTypeArgumentsStub
    //     0xaa55c0: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xaa55c4: LoadField: r30 = r30->field_7
    //     0xaa55c4: ldur            lr, [lr, #7]
    // 0xaa55c8: blr             lr
    // 0xaa55cc: mov             x1, x0
    // 0xaa55d0: r0 = _CompactIterable()
    //     0xaa55d0: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xaa55d4: mov             x1, x0
    // 0xaa55d8: ldur            x0, [fp, #-8]
    // 0xaa55dc: StoreField: r1->field_b = r0
    //     0xaa55dc: stur            w0, [x1, #0xb]
    // 0xaa55e0: r0 = -1
    //     0xaa55e0: movn            x0, #0
    // 0xaa55e4: StoreField: r1->field_f = r0
    //     0xaa55e4: stur            x0, [x1, #0xf]
    // 0xaa55e8: r0 = 2
    //     0xaa55e8: movz            x0, #0x2
    // 0xaa55ec: ArrayStore: r1[0] = r0  ; List_8
    //     0xaa55ec: stur            x0, [x1, #0x17]
    // 0xaa55f0: r0 = iterator()
    //     0xaa55f0: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0xaa55f4: stur            x0, [fp, #-0x18]
    // 0xaa55f8: LoadField: r2 = r0->field_7
    //     0xaa55f8: ldur            w2, [x0, #7]
    // 0xaa55fc: DecompressPointer r2
    //     0xaa55fc: add             x2, x2, HEAP, lsl #32
    // 0xaa5600: stur            x2, [fp, #-8]
    // 0xaa5604: ldur            x3, [fp, #-0x10]
    // 0xaa5608: ldur            x4, [fp, #-0x20]
    // 0xaa560c: CheckStackOverflow
    //     0xaa560c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa5610: cmp             SP, x16
    //     0xaa5614: b.ls            #0xaa579c
    // 0xaa5618: mov             x1, x0
    // 0xaa561c: r0 = moveNext()
    //     0xaa561c: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0xaa5620: tbnz            w0, #4, #0xaa575c
    // 0xaa5624: ldur            x3, [fp, #-0x18]
    // 0xaa5628: LoadField: r4 = r3->field_33
    //     0xaa5628: ldur            w4, [x3, #0x33]
    // 0xaa562c: DecompressPointer r4
    //     0xaa562c: add             x4, x4, HEAP, lsl #32
    // 0xaa5630: stur            x4, [fp, #-0x28]
    // 0xaa5634: cmp             w4, NULL
    // 0xaa5638: b.ne            #0xaa566c
    // 0xaa563c: mov             x0, x4
    // 0xaa5640: ldur            x2, [fp, #-8]
    // 0xaa5644: r1 = Null
    //     0xaa5644: mov             x1, NULL
    // 0xaa5648: cmp             w2, NULL
    // 0xaa564c: b.eq            #0xaa566c
    // 0xaa5650: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xaa5650: ldur            w4, [x2, #0x17]
    // 0xaa5654: DecompressPointer r4
    //     0xaa5654: add             x4, x4, HEAP, lsl #32
    // 0xaa5658: r8 = X0
    //     0xaa5658: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xaa565c: LoadField: r9 = r4->field_7
    //     0xaa565c: ldur            x9, [x4, #7]
    // 0xaa5660: r3 = Null
    //     0xaa5660: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fcf8] Null
    //     0xaa5664: ldr             x3, [x3, #0xcf8]
    // 0xaa5668: blr             x9
    // 0xaa566c: ldur            x1, [fp, #-0x28]
    // 0xaa5670: ldur            x0, [fp, #-0x20]
    // 0xaa5674: cmp             w1, w0
    // 0xaa5678: b.eq            #0xaa5750
    // 0xaa567c: LoadField: r2 = r1->field_f
    //     0xaa567c: ldur            w2, [x1, #0xf]
    // 0xaa5680: DecompressPointer r2
    //     0xaa5680: add             x2, x2, HEAP, lsl #32
    // 0xaa5684: cmp             w2, NULL
    // 0xaa5688: b.eq            #0xaa5750
    // 0xaa568c: ldur            x4, [fp, #-0x10]
    // 0xaa5690: LoadField: r2 = r4->field_2b
    //     0xaa5690: ldur            w2, [x4, #0x2b]
    // 0xaa5694: DecompressPointer r2
    //     0xaa5694: add             x2, x2, HEAP, lsl #32
    // 0xaa5698: cmp             w2, NULL
    // 0xaa569c: b.eq            #0xaa57a4
    // 0xaa56a0: LoadField: r3 = r4->field_27
    //     0xaa56a0: ldur            w3, [x4, #0x27]
    // 0xaa56a4: DecompressPointer r3
    //     0xaa56a4: add             x3, x3, HEAP, lsl #32
    // 0xaa56a8: cmp             w3, NULL
    // 0xaa56ac: b.eq            #0xaa57a8
    // 0xaa56b0: LoadField: r5 = r3->field_3f
    //     0xaa56b0: ldur            w5, [x3, #0x3f]
    // 0xaa56b4: DecompressPointer r5
    //     0xaa56b4: add             x5, x5, HEAP, lsl #32
    // 0xaa56b8: r16 = Sentinel
    //     0xaa56b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa56bc: cmp             w5, w16
    // 0xaa56c0: b.eq            #0xaa57ac
    // 0xaa56c4: LoadField: r3 = r4->field_3f
    //     0xaa56c4: ldur            w3, [x4, #0x3f]
    // 0xaa56c8: DecompressPointer r3
    //     0xaa56c8: add             x3, x3, HEAP, lsl #32
    // 0xaa56cc: r16 = Sentinel
    //     0xaa56cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa56d0: cmp             w3, w16
    // 0xaa56d4: b.eq            #0xaa57b8
    // 0xaa56d8: LoadField: r6 = r3->field_b
    //     0xaa56d8: ldur            w6, [x3, #0xb]
    // 0xaa56dc: DecompressPointer r6
    //     0xaa56dc: add             x6, x6, HEAP, lsl #32
    // 0xaa56e0: cmp             w6, NULL
    // 0xaa56e4: b.eq            #0xaa57c4
    // 0xaa56e8: LoadField: r3 = r6->field_b
    //     0xaa56e8: ldur            w3, [x6, #0xb]
    // 0xaa56ec: DecompressPointer r3
    //     0xaa56ec: add             x3, x3, HEAP, lsl #32
    // 0xaa56f0: r16 = Instance_AxisDirection
    //     0xaa56f0: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xaa56f4: cmp             w3, w16
    // 0xaa56f8: b.eq            #0xaa5708
    // 0xaa56fc: r16 = Instance_AxisDirection
    //     0xaa56fc: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0xaa5700: cmp             w3, w16
    // 0xaa5704: b.ne            #0xaa5710
    // 0xaa5708: r6 = true
    //     0xaa5708: add             x6, NULL, #0x20  ; true
    // 0xaa570c: b               #0xaa5734
    // 0xaa5710: r16 = Instance_AxisDirection
    //     0xaa5710: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xaa5714: cmp             w3, w16
    // 0xaa5718: b.eq            #0xaa5728
    // 0xaa571c: r16 = Instance_AxisDirection
    //     0xaa571c: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0xaa5720: cmp             w3, w16
    // 0xaa5724: b.ne            #0xaa5730
    // 0xaa5728: r6 = false
    //     0xaa5728: add             x6, NULL, #0x30  ; false
    // 0xaa572c: b               #0xaa5734
    // 0xaa5730: r6 = Null
    //     0xaa5730: mov             x6, NULL
    // 0xaa5734: r3 = LoadInt32Instr(r2)
    //     0xaa5734: sbfx            x3, x2, #1, #0x1f
    //     0xaa5738: tbz             w2, #0, #0xaa5740
    //     0xaa573c: ldur            x3, [x2, #7]
    // 0xaa5740: LoadField: d0 = r5->field_7
    //     0xaa5740: ldur            d0, [x5, #7]
    // 0xaa5744: mov             x2, x3
    // 0xaa5748: r5 = false
    //     0xaa5748: add             x5, NULL, #0x30  ; false
    // 0xaa574c: r0 = updateForGap()
    //     0xaa574c: bl              #0x9454c4  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::updateForGap
    // 0xaa5750: ldur            x0, [fp, #-0x18]
    // 0xaa5754: ldur            x2, [fp, #-8]
    // 0xaa5758: b               #0xaa5604
    // 0xaa575c: ldur            x1, [fp, #-0x10]
    // 0xaa5760: LoadField: r0 = r1->field_27
    //     0xaa5760: ldur            w0, [x1, #0x27]
    // 0xaa5764: DecompressPointer r0
    //     0xaa5764: add             x0, x0, HEAP, lsl #32
    // 0xaa5768: LeaveFrame
    //     0xaa5768: mov             SP, fp
    //     0xaa576c: ldp             fp, lr, [SP], #0x10
    // 0xaa5770: ret
    //     0xaa5770: ret             
    // 0xaa5774: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa5774: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa5778: b               #0xaa5344
    // 0xaa577c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa577c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa5780: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5780: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa5784: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5784: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa5788: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5788: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa578c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa578c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa5790: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5790: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa5794: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5794: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa5798: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa5798: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa579c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa579c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa57a0: b               #0xaa5618
    // 0xaa57a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa57a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa57a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa57a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa57ac: r9 = itemExtent
    //     0xaa57ac: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd08] Field <<EMAIL>>: late (offset: 0x40)
    //     0xaa57b0: ldr             x9, [x9, #0xd08]
    // 0xaa57b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa57b4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa57b8: r9 = _scrollable
    //     0xaa57b8: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0xaa57bc: ldr             x9, [x9, #0xd10]
    // 0xaa57c0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa57c0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa57c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa57c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _dragEnd(dynamic, _DragInfo) {
    // ** addr: 0xaa60dc, size: 0x3c
    // 0xaa60dc: EnterFrame
    //     0xaa60dc: stp             fp, lr, [SP, #-0x10]!
    //     0xaa60e0: mov             fp, SP
    // 0xaa60e4: ldr             x0, [fp, #0x18]
    // 0xaa60e8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa60e8: ldur            w1, [x0, #0x17]
    // 0xaa60ec: DecompressPointer r1
    //     0xaa60ec: add             x1, x1, HEAP, lsl #32
    // 0xaa60f0: CheckStackOverflow
    //     0xaa60f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa60f4: cmp             SP, x16
    //     0xaa60f8: b.ls            #0xaa6110
    // 0xaa60fc: ldr             x2, [fp, #0x10]
    // 0xaa6100: r0 = _dragEnd()
    //     0xaa6100: bl              #0xaa6118  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragEnd
    // 0xaa6104: LeaveFrame
    //     0xaa6104: mov             SP, fp
    //     0xaa6108: ldp             fp, lr, [SP], #0x10
    // 0xaa610c: ret
    //     0xaa610c: ret             
    // 0xaa6110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa6110: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6114: b               #0xaa60fc
  }
  _ _dragEnd(/* No info */) {
    // ** addr: 0xaa6118, size: 0x88
    // 0xaa6118: EnterFrame
    //     0xaa6118: stp             fp, lr, [SP, #-0x10]!
    //     0xaa611c: mov             fp, SP
    // 0xaa6120: AllocStack(0x10)
    //     0xaa6120: sub             SP, SP, #0x10
    // 0xaa6124: SetupParameters(SliverReorderableListState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xaa6124: stur            x1, [fp, #-8]
    //     0xaa6128: stur            x2, [fp, #-0x10]
    // 0xaa612c: CheckStackOverflow
    //     0xaa612c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6130: cmp             SP, x16
    //     0xaa6134: b.ls            #0xaa6194
    // 0xaa6138: r1 = 2
    //     0xaa6138: movz            x1, #0x2
    // 0xaa613c: r0 = AllocateContext()
    //     0xaa613c: bl              #0xec126c  ; AllocateContextStub
    // 0xaa6140: mov             x1, x0
    // 0xaa6144: ldur            x0, [fp, #-8]
    // 0xaa6148: StoreField: r1->field_f = r0
    //     0xaa6148: stur            w0, [x1, #0xf]
    // 0xaa614c: ldur            x2, [fp, #-0x10]
    // 0xaa6150: StoreField: r1->field_13 = r2
    //     0xaa6150: stur            w2, [x1, #0x13]
    // 0xaa6154: mov             x2, x1
    // 0xaa6158: r1 = Function '<anonymous closure>':.
    //     0xaa6158: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fd80] AnonymousClosure: (0xaa61a0), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragEnd (0xaa6118)
    //     0xaa615c: ldr             x1, [x1, #0xd80]
    // 0xaa6160: r0 = AllocateClosure()
    //     0xaa6160: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa6164: ldur            x1, [fp, #-8]
    // 0xaa6168: mov             x2, x0
    // 0xaa616c: r0 = setState()
    //     0xaa616c: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaa6170: ldur            x1, [fp, #-8]
    // 0xaa6174: LoadField: r2 = r1->field_b
    //     0xaa6174: ldur            w2, [x1, #0xb]
    // 0xaa6178: DecompressPointer r2
    //     0xaa6178: add             x2, x2, HEAP, lsl #32
    // 0xaa617c: cmp             w2, NULL
    // 0xaa6180: b.eq            #0xaa619c
    // 0xaa6184: r0 = Null
    //     0xaa6184: mov             x0, NULL
    // 0xaa6188: LeaveFrame
    //     0xaa6188: mov             SP, fp
    //     0xaa618c: ldp             fp, lr, [SP], #0x10
    // 0xaa6190: ret
    //     0xaa6190: ret             
    // 0xaa6194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa6194: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6198: b               #0xaa6138
    // 0xaa619c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa619c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaa61a0, size: 0x5dc
    // 0xaa61a0: EnterFrame
    //     0xaa61a0: stp             fp, lr, [SP, #-0x10]!
    //     0xaa61a4: mov             fp, SP
    // 0xaa61a8: AllocStack(0x20)
    //     0xaa61a8: sub             SP, SP, #0x20
    // 0xaa61ac: SetupParameters()
    //     0xaa61ac: ldr             x0, [fp, #0x10]
    //     0xaa61b0: ldur            w3, [x0, #0x17]
    //     0xaa61b4: add             x3, x3, HEAP, lsl #32
    //     0xaa61b8: stur            x3, [fp, #-0x10]
    // 0xaa61bc: CheckStackOverflow
    //     0xaa61bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa61c0: cmp             SP, x16
    //     0xaa61c4: b.ls            #0xaa66f0
    // 0xaa61c8: LoadField: r0 = r3->field_f
    //     0xaa61c8: ldur            w0, [x3, #0xf]
    // 0xaa61cc: DecompressPointer r0
    //     0xaa61cc: add             x0, x0, HEAP, lsl #32
    // 0xaa61d0: stur            x0, [fp, #-8]
    // 0xaa61d4: LoadField: r1 = r0->field_2b
    //     0xaa61d4: ldur            w1, [x0, #0x2b]
    // 0xaa61d8: DecompressPointer r1
    //     0xaa61d8: add             x1, x1, HEAP, lsl #32
    // 0xaa61dc: LoadField: r2 = r3->field_13
    //     0xaa61dc: ldur            w2, [x3, #0x13]
    // 0xaa61e0: DecompressPointer r2
    //     0xaa61e0: add             x2, x2, HEAP, lsl #32
    // 0xaa61e4: LoadField: r4 = r2->field_27
    //     0xaa61e4: ldur            w4, [x2, #0x27]
    // 0xaa61e8: DecompressPointer r4
    //     0xaa61e8: add             x4, x4, HEAP, lsl #32
    // 0xaa61ec: r16 = Sentinel
    //     0xaa61ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa61f0: cmp             w4, w16
    // 0xaa61f4: b.eq            #0xaa66f8
    // 0xaa61f8: cmp             w1, w4
    // 0xaa61fc: b.eq            #0xaa6238
    // 0xaa6200: and             w16, w1, w4
    // 0xaa6204: branchIfSmi(r16, 0xaa6278)
    //     0xaa6204: tbz             w16, #0, #0xaa6278
    // 0xaa6208: r16 = LoadClassIdInstr(r1)
    //     0xaa6208: ldur            x16, [x1, #-1]
    //     0xaa620c: ubfx            x16, x16, #0xc, #0x14
    // 0xaa6210: cmp             x16, #0x3d
    // 0xaa6214: b.ne            #0xaa6278
    // 0xaa6218: r16 = LoadClassIdInstr(r4)
    //     0xaa6218: ldur            x16, [x4, #-1]
    //     0xaa621c: ubfx            x16, x16, #0xc, #0x14
    // 0xaa6220: cmp             x16, #0x3d
    // 0xaa6224: b.ne            #0xaa6278
    // 0xaa6228: LoadField: r16 = r1->field_7
    //     0xaa6228: ldur            x16, [x1, #7]
    // 0xaa622c: LoadField: r17 = r4->field_7
    //     0xaa622c: ldur            x17, [x4, #7]
    // 0xaa6230: cmp             x16, x17
    // 0xaa6234: b.ne            #0xaa6278
    // 0xaa6238: cmp             w1, NULL
    // 0xaa623c: b.eq            #0xaa6704
    // 0xaa6240: r2 = LoadInt32Instr(r1)
    //     0xaa6240: sbfx            x2, x1, #1, #0x1f
    //     0xaa6244: tbz             w1, #0, #0xaa624c
    //     0xaa6248: ldur            x2, [x1, #7]
    // 0xaa624c: mov             x1, x0
    // 0xaa6250: r0 = _itemOffsetAt()
    //     0xaa6250: bl              #0xaa68a0  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_itemOffsetAt
    // 0xaa6254: ldur            x4, [fp, #-8]
    // 0xaa6258: StoreField: r4->field_2f = r0
    //     0xaa6258: stur            w0, [x4, #0x2f]
    //     0xaa625c: ldurb           w16, [x4, #-1]
    //     0xaa6260: ldurb           w17, [x0, #-1]
    //     0xaa6264: and             x16, x17, x16, lsr #2
    //     0xaa6268: tst             x16, HEAP, lsr #32
    //     0xaa626c: b.eq            #0xaa6274
    //     0xaa6270: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa6274: b               #0xaa66e0
    // 0xaa6278: mov             x4, x0
    // 0xaa627c: LoadField: r0 = r4->field_3f
    //     0xaa627c: ldur            w0, [x4, #0x3f]
    // 0xaa6280: DecompressPointer r0
    //     0xaa6280: add             x0, x0, HEAP, lsl #32
    // 0xaa6284: r16 = Sentinel
    //     0xaa6284: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa6288: cmp             w0, w16
    // 0xaa628c: b.eq            #0xaa6708
    // 0xaa6290: LoadField: r2 = r0->field_b
    //     0xaa6290: ldur            w2, [x0, #0xb]
    // 0xaa6294: DecompressPointer r2
    //     0xaa6294: add             x2, x2, HEAP, lsl #32
    // 0xaa6298: cmp             w2, NULL
    // 0xaa629c: b.eq            #0xaa6714
    // 0xaa62a0: LoadField: r0 = r2->field_b
    //     0xaa62a0: ldur            w0, [x2, #0xb]
    // 0xaa62a4: DecompressPointer r0
    //     0xaa62a4: add             x0, x0, HEAP, lsl #32
    // 0xaa62a8: r16 = Instance_AxisDirection
    //     0xaa62a8: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xaa62ac: cmp             w0, w16
    // 0xaa62b0: b.eq            #0xaa62c0
    // 0xaa62b4: r16 = Instance_AxisDirection
    //     0xaa62b4: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0xaa62b8: cmp             w0, w16
    // 0xaa62bc: b.ne            #0xaa64e0
    // 0xaa62c0: cmp             w1, NULL
    // 0xaa62c4: b.eq            #0xaa6718
    // 0xaa62c8: LoadField: r0 = r4->field_1b
    //     0xaa62c8: ldur            w0, [x4, #0x1b]
    // 0xaa62cc: DecompressPointer r0
    //     0xaa62cc: add             x0, x0, HEAP, lsl #32
    // 0xaa62d0: LoadField: r2 = r0->field_13
    //     0xaa62d0: ldur            w2, [x0, #0x13]
    // 0xaa62d4: r5 = LoadInt32Instr(r2)
    //     0xaa62d4: sbfx            x5, x2, #1, #0x1f
    // 0xaa62d8: asr             x2, x5, #1
    // 0xaa62dc: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xaa62dc: ldur            w5, [x0, #0x17]
    // 0xaa62e0: r0 = LoadInt32Instr(r5)
    //     0xaa62e0: sbfx            x0, x5, #1, #0x1f
    // 0xaa62e4: sub             x5, x2, x0
    // 0xaa62e8: r2 = LoadInt32Instr(r1)
    //     0xaa62e8: sbfx            x2, x1, #1, #0x1f
    //     0xaa62ec: tbz             w1, #0, #0xaa62f4
    //     0xaa62f0: ldur            x2, [x1, #7]
    // 0xaa62f4: cmp             x2, x5
    // 0xaa62f8: b.lt            #0xaa63e4
    // 0xaa62fc: sub             x2, x5, #1
    // 0xaa6300: mov             x1, x4
    // 0xaa6304: r0 = _itemOffsetAt()
    //     0xaa6304: bl              #0xaa68a0  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_itemOffsetAt
    // 0xaa6308: mov             x2, x0
    // 0xaa630c: ldur            x0, [fp, #-0x10]
    // 0xaa6310: stur            x2, [fp, #-0x18]
    // 0xaa6314: LoadField: r1 = r0->field_13
    //     0xaa6314: ldur            w1, [x0, #0x13]
    // 0xaa6318: DecompressPointer r1
    //     0xaa6318: add             x1, x1, HEAP, lsl #32
    // 0xaa631c: LoadField: r3 = r1->field_3f
    //     0xaa631c: ldur            w3, [x1, #0x3f]
    // 0xaa6320: DecompressPointer r3
    //     0xaa6320: add             x3, x3, HEAP, lsl #32
    // 0xaa6324: r16 = Sentinel
    //     0xaa6324: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa6328: cmp             w3, w16
    // 0xaa632c: b.eq            #0xaa671c
    // 0xaa6330: LoadField: r1 = r0->field_f
    //     0xaa6330: ldur            w1, [x0, #0xf]
    // 0xaa6334: DecompressPointer r1
    //     0xaa6334: add             x1, x1, HEAP, lsl #32
    // 0xaa6338: LoadField: r0 = r1->field_3f
    //     0xaa6338: ldur            w0, [x1, #0x3f]
    // 0xaa633c: DecompressPointer r0
    //     0xaa633c: add             x0, x0, HEAP, lsl #32
    // 0xaa6340: r16 = Sentinel
    //     0xaa6340: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa6344: cmp             w0, w16
    // 0xaa6348: b.eq            #0xaa6728
    // 0xaa634c: LoadField: r1 = r0->field_b
    //     0xaa634c: ldur            w1, [x0, #0xb]
    // 0xaa6350: DecompressPointer r1
    //     0xaa6350: add             x1, x1, HEAP, lsl #32
    // 0xaa6354: cmp             w1, NULL
    // 0xaa6358: b.eq            #0xaa6734
    // 0xaa635c: LoadField: r0 = r1->field_b
    //     0xaa635c: ldur            w0, [x1, #0xb]
    // 0xaa6360: DecompressPointer r0
    //     0xaa6360: add             x0, x0, HEAP, lsl #32
    // 0xaa6364: r16 = Instance_AxisDirection
    //     0xaa6364: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xaa6368: cmp             w0, w16
    // 0xaa636c: b.eq            #0xaa637c
    // 0xaa6370: r16 = Instance_AxisDirection
    //     0xaa6370: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xaa6374: cmp             w0, w16
    // 0xaa6378: b.ne            #0xaa6384
    // 0xaa637c: r1 = Instance_Axis
    //     0xaa637c: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaa6380: b               #0xaa63a8
    // 0xaa6384: r16 = Instance_AxisDirection
    //     0xaa6384: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0xaa6388: cmp             w0, w16
    // 0xaa638c: b.eq            #0xaa639c
    // 0xaa6390: r16 = Instance_AxisDirection
    //     0xaa6390: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0xaa6394: cmp             w0, w16
    // 0xaa6398: b.ne            #0xaa63a4
    // 0xaa639c: r1 = Instance_Axis
    //     0xaa639c: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaa63a0: b               #0xaa63a8
    // 0xaa63a4: r1 = Null
    //     0xaa63a4: mov             x1, NULL
    // 0xaa63a8: ldur            x0, [fp, #-8]
    // 0xaa63ac: LoadField: d0 = r3->field_7
    //     0xaa63ac: ldur            d0, [x3, #7]
    // 0xaa63b0: r0 = _extentOffset()
    //     0xaa63b0: bl              #0x945a1c  ; [package:flutter/src/widgets/reorderable_list.dart] ::_extentOffset
    // 0xaa63b4: ldur            x1, [fp, #-0x18]
    // 0xaa63b8: mov             x2, x0
    // 0xaa63bc: r0 = -()
    //     0xaa63bc: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xaa63c0: ldur            x3, [fp, #-8]
    // 0xaa63c4: StoreField: r3->field_2f = r0
    //     0xaa63c4: stur            w0, [x3, #0x2f]
    //     0xaa63c8: ldurb           w16, [x3, #-1]
    //     0xaa63cc: ldurb           w17, [x0, #-1]
    //     0xaa63d0: and             x16, x17, x16, lsr #2
    //     0xaa63d4: tst             x16, HEAP, lsr #32
    //     0xaa63d8: b.eq            #0xaa63e0
    //     0xaa63dc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaa63e0: b               #0xaa66e0
    // 0xaa63e4: mov             x0, x3
    // 0xaa63e8: mov             x3, x4
    // 0xaa63ec: mov             x1, x3
    // 0xaa63f0: r0 = _itemOffsetAt()
    //     0xaa63f0: bl              #0xaa68a0  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_itemOffsetAt
    // 0xaa63f4: mov             x3, x0
    // 0xaa63f8: ldur            x0, [fp, #-0x10]
    // 0xaa63fc: stur            x3, [fp, #-0x18]
    // 0xaa6400: LoadField: r1 = r0->field_f
    //     0xaa6400: ldur            w1, [x0, #0xf]
    // 0xaa6404: DecompressPointer r1
    //     0xaa6404: add             x1, x1, HEAP, lsl #32
    // 0xaa6408: LoadField: r2 = r1->field_2b
    //     0xaa6408: ldur            w2, [x1, #0x2b]
    // 0xaa640c: DecompressPointer r2
    //     0xaa640c: add             x2, x2, HEAP, lsl #32
    // 0xaa6410: cmp             w2, NULL
    // 0xaa6414: b.eq            #0xaa6738
    // 0xaa6418: r4 = LoadInt32Instr(r2)
    //     0xaa6418: sbfx            x4, x2, #1, #0x1f
    //     0xaa641c: tbz             w2, #0, #0xaa6424
    //     0xaa6420: ldur            x4, [x2, #7]
    // 0xaa6424: mov             x2, x4
    // 0xaa6428: r0 = _itemExtentAt()
    //     0xaa6428: bl              #0xaa677c  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_itemExtentAt
    // 0xaa642c: ldur            x3, [fp, #-0x10]
    // 0xaa6430: LoadField: r0 = r3->field_f
    //     0xaa6430: ldur            w0, [x3, #0xf]
    // 0xaa6434: DecompressPointer r0
    //     0xaa6434: add             x0, x0, HEAP, lsl #32
    // 0xaa6438: LoadField: r1 = r0->field_3f
    //     0xaa6438: ldur            w1, [x0, #0x3f]
    // 0xaa643c: DecompressPointer r1
    //     0xaa643c: add             x1, x1, HEAP, lsl #32
    // 0xaa6440: r16 = Sentinel
    //     0xaa6440: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa6444: cmp             w1, w16
    // 0xaa6448: b.eq            #0xaa673c
    // 0xaa644c: LoadField: r0 = r1->field_b
    //     0xaa644c: ldur            w0, [x1, #0xb]
    // 0xaa6450: DecompressPointer r0
    //     0xaa6450: add             x0, x0, HEAP, lsl #32
    // 0xaa6454: cmp             w0, NULL
    // 0xaa6458: b.eq            #0xaa6748
    // 0xaa645c: LoadField: r1 = r0->field_b
    //     0xaa645c: ldur            w1, [x0, #0xb]
    // 0xaa6460: DecompressPointer r1
    //     0xaa6460: add             x1, x1, HEAP, lsl #32
    // 0xaa6464: r16 = Instance_AxisDirection
    //     0xaa6464: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xaa6468: cmp             w1, w16
    // 0xaa646c: b.eq            #0xaa647c
    // 0xaa6470: r16 = Instance_AxisDirection
    //     0xaa6470: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xaa6474: cmp             w1, w16
    // 0xaa6478: b.ne            #0xaa6484
    // 0xaa647c: r1 = Instance_Axis
    //     0xaa647c: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaa6480: b               #0xaa64a8
    // 0xaa6484: r16 = Instance_AxisDirection
    //     0xaa6484: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0xaa6488: cmp             w1, w16
    // 0xaa648c: b.eq            #0xaa649c
    // 0xaa6490: r16 = Instance_AxisDirection
    //     0xaa6490: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0xaa6494: cmp             w1, w16
    // 0xaa6498: b.ne            #0xaa64a4
    // 0xaa649c: r1 = Instance_Axis
    //     0xaa649c: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaa64a0: b               #0xaa64a8
    // 0xaa64a4: r1 = Null
    //     0xaa64a4: mov             x1, NULL
    // 0xaa64a8: ldur            x0, [fp, #-8]
    // 0xaa64ac: r0 = _extentOffset()
    //     0xaa64ac: bl              #0x945a1c  ; [package:flutter/src/widgets/reorderable_list.dart] ::_extentOffset
    // 0xaa64b0: ldur            x1, [fp, #-0x18]
    // 0xaa64b4: mov             x2, x0
    // 0xaa64b8: r0 = +()
    //     0xaa64b8: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0xaa64bc: ldur            x4, [fp, #-8]
    // 0xaa64c0: StoreField: r4->field_2f = r0
    //     0xaa64c0: stur            w0, [x4, #0x2f]
    //     0xaa64c4: ldurb           w16, [x4, #-1]
    //     0xaa64c8: ldurb           w17, [x0, #-1]
    //     0xaa64cc: and             x16, x17, x16, lsr #2
    //     0xaa64d0: tst             x16, HEAP, lsr #32
    //     0xaa64d4: b.eq            #0xaa64dc
    //     0xaa64d8: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaa64dc: b               #0xaa66e0
    // 0xaa64e0: r16 = Instance_AxisDirection
    //     0xaa64e0: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xaa64e4: cmp             w0, w16
    // 0xaa64e8: b.eq            #0xaa64f8
    // 0xaa64ec: r16 = Instance_AxisDirection
    //     0xaa64ec: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0xaa64f0: cmp             w0, w16
    // 0xaa64f4: b.eq            #0xaa64f8
    // 0xaa64f8: cmp             w1, NULL
    // 0xaa64fc: b.eq            #0xaa674c
    // 0xaa6500: cbnz            w1, #0xaa65ec
    // 0xaa6504: mov             x1, x4
    // 0xaa6508: r2 = 0
    //     0xaa6508: movz            x2, #0
    // 0xaa650c: r0 = _itemOffsetAt()
    //     0xaa650c: bl              #0xaa68a0  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_itemOffsetAt
    // 0xaa6510: mov             x2, x0
    // 0xaa6514: ldur            x0, [fp, #-0x10]
    // 0xaa6518: stur            x2, [fp, #-0x18]
    // 0xaa651c: LoadField: r1 = r0->field_13
    //     0xaa651c: ldur            w1, [x0, #0x13]
    // 0xaa6520: DecompressPointer r1
    //     0xaa6520: add             x1, x1, HEAP, lsl #32
    // 0xaa6524: LoadField: r3 = r1->field_3f
    //     0xaa6524: ldur            w3, [x1, #0x3f]
    // 0xaa6528: DecompressPointer r3
    //     0xaa6528: add             x3, x3, HEAP, lsl #32
    // 0xaa652c: r16 = Sentinel
    //     0xaa652c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa6530: cmp             w3, w16
    // 0xaa6534: b.eq            #0xaa6750
    // 0xaa6538: LoadField: r1 = r0->field_f
    //     0xaa6538: ldur            w1, [x0, #0xf]
    // 0xaa653c: DecompressPointer r1
    //     0xaa653c: add             x1, x1, HEAP, lsl #32
    // 0xaa6540: LoadField: r0 = r1->field_3f
    //     0xaa6540: ldur            w0, [x1, #0x3f]
    // 0xaa6544: DecompressPointer r0
    //     0xaa6544: add             x0, x0, HEAP, lsl #32
    // 0xaa6548: r16 = Sentinel
    //     0xaa6548: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa654c: cmp             w0, w16
    // 0xaa6550: b.eq            #0xaa675c
    // 0xaa6554: LoadField: r1 = r0->field_b
    //     0xaa6554: ldur            w1, [x0, #0xb]
    // 0xaa6558: DecompressPointer r1
    //     0xaa6558: add             x1, x1, HEAP, lsl #32
    // 0xaa655c: cmp             w1, NULL
    // 0xaa6560: b.eq            #0xaa6768
    // 0xaa6564: LoadField: r0 = r1->field_b
    //     0xaa6564: ldur            w0, [x1, #0xb]
    // 0xaa6568: DecompressPointer r0
    //     0xaa6568: add             x0, x0, HEAP, lsl #32
    // 0xaa656c: r16 = Instance_AxisDirection
    //     0xaa656c: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xaa6570: cmp             w0, w16
    // 0xaa6574: b.eq            #0xaa6584
    // 0xaa6578: r16 = Instance_AxisDirection
    //     0xaa6578: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xaa657c: cmp             w0, w16
    // 0xaa6580: b.ne            #0xaa658c
    // 0xaa6584: r1 = Instance_Axis
    //     0xaa6584: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaa6588: b               #0xaa65b0
    // 0xaa658c: r16 = Instance_AxisDirection
    //     0xaa658c: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0xaa6590: cmp             w0, w16
    // 0xaa6594: b.eq            #0xaa65a4
    // 0xaa6598: r16 = Instance_AxisDirection
    //     0xaa6598: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0xaa659c: cmp             w0, w16
    // 0xaa65a0: b.ne            #0xaa65ac
    // 0xaa65a4: r1 = Instance_Axis
    //     0xaa65a4: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaa65a8: b               #0xaa65b0
    // 0xaa65ac: r1 = Null
    //     0xaa65ac: mov             x1, NULL
    // 0xaa65b0: ldur            x0, [fp, #-8]
    // 0xaa65b4: LoadField: d0 = r3->field_7
    //     0xaa65b4: ldur            d0, [x3, #7]
    // 0xaa65b8: r0 = _extentOffset()
    //     0xaa65b8: bl              #0x945a1c  ; [package:flutter/src/widgets/reorderable_list.dart] ::_extentOffset
    // 0xaa65bc: ldur            x1, [fp, #-0x18]
    // 0xaa65c0: mov             x2, x0
    // 0xaa65c4: r0 = -()
    //     0xaa65c4: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xaa65c8: ldur            x3, [fp, #-8]
    // 0xaa65cc: StoreField: r3->field_2f = r0
    //     0xaa65cc: stur            w0, [x3, #0x2f]
    //     0xaa65d0: ldurb           w16, [x3, #-1]
    //     0xaa65d4: ldurb           w17, [x0, #-1]
    //     0xaa65d8: and             x16, x17, x16, lsr #2
    //     0xaa65dc: tst             x16, HEAP, lsr #32
    //     0xaa65e0: b.eq            #0xaa65e8
    //     0xaa65e4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaa65e8: b               #0xaa66e0
    // 0xaa65ec: mov             x0, x3
    // 0xaa65f0: mov             x3, x4
    // 0xaa65f4: r2 = LoadInt32Instr(r1)
    //     0xaa65f4: sbfx            x2, x1, #1, #0x1f
    //     0xaa65f8: tbz             w1, #0, #0xaa6600
    //     0xaa65fc: ldur            x2, [x1, #7]
    // 0xaa6600: sub             x4, x2, #1
    // 0xaa6604: mov             x1, x3
    // 0xaa6608: mov             x2, x4
    // 0xaa660c: stur            x4, [fp, #-0x20]
    // 0xaa6610: r0 = _itemOffsetAt()
    //     0xaa6610: bl              #0xaa68a0  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_itemOffsetAt
    // 0xaa6614: mov             x3, x0
    // 0xaa6618: ldur            x0, [fp, #-0x10]
    // 0xaa661c: stur            x3, [fp, #-0x18]
    // 0xaa6620: LoadField: r1 = r0->field_f
    //     0xaa6620: ldur            w1, [x0, #0xf]
    // 0xaa6624: DecompressPointer r1
    //     0xaa6624: add             x1, x1, HEAP, lsl #32
    // 0xaa6628: ldur            x2, [fp, #-0x20]
    // 0xaa662c: r0 = _itemExtentAt()
    //     0xaa662c: bl              #0xaa677c  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_itemExtentAt
    // 0xaa6630: ldur            x0, [fp, #-0x10]
    // 0xaa6634: LoadField: r1 = r0->field_f
    //     0xaa6634: ldur            w1, [x0, #0xf]
    // 0xaa6638: DecompressPointer r1
    //     0xaa6638: add             x1, x1, HEAP, lsl #32
    // 0xaa663c: LoadField: r0 = r1->field_3f
    //     0xaa663c: ldur            w0, [x1, #0x3f]
    // 0xaa6640: DecompressPointer r0
    //     0xaa6640: add             x0, x0, HEAP, lsl #32
    // 0xaa6644: r16 = Sentinel
    //     0xaa6644: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa6648: cmp             w0, w16
    // 0xaa664c: b.eq            #0xaa676c
    // 0xaa6650: LoadField: r1 = r0->field_b
    //     0xaa6650: ldur            w1, [x0, #0xb]
    // 0xaa6654: DecompressPointer r1
    //     0xaa6654: add             x1, x1, HEAP, lsl #32
    // 0xaa6658: cmp             w1, NULL
    // 0xaa665c: b.eq            #0xaa6778
    // 0xaa6660: LoadField: r0 = r1->field_b
    //     0xaa6660: ldur            w0, [x1, #0xb]
    // 0xaa6664: DecompressPointer r0
    //     0xaa6664: add             x0, x0, HEAP, lsl #32
    // 0xaa6668: r16 = Instance_AxisDirection
    //     0xaa6668: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xaa666c: cmp             w0, w16
    // 0xaa6670: b.eq            #0xaa6680
    // 0xaa6674: r16 = Instance_AxisDirection
    //     0xaa6674: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xaa6678: cmp             w0, w16
    // 0xaa667c: b.ne            #0xaa6688
    // 0xaa6680: r1 = Instance_Axis
    //     0xaa6680: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaa6684: b               #0xaa66ac
    // 0xaa6688: r16 = Instance_AxisDirection
    //     0xaa6688: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0xaa668c: cmp             w0, w16
    // 0xaa6690: b.eq            #0xaa66a0
    // 0xaa6694: r16 = Instance_AxisDirection
    //     0xaa6694: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0xaa6698: cmp             w0, w16
    // 0xaa669c: b.ne            #0xaa66a8
    // 0xaa66a0: r1 = Instance_Axis
    //     0xaa66a0: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaa66a4: b               #0xaa66ac
    // 0xaa66a8: r1 = Null
    //     0xaa66a8: mov             x1, NULL
    // 0xaa66ac: ldur            x0, [fp, #-8]
    // 0xaa66b0: r0 = _extentOffset()
    //     0xaa66b0: bl              #0x945a1c  ; [package:flutter/src/widgets/reorderable_list.dart] ::_extentOffset
    // 0xaa66b4: ldur            x1, [fp, #-0x18]
    // 0xaa66b8: mov             x2, x0
    // 0xaa66bc: r0 = +()
    //     0xaa66bc: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0xaa66c0: ldur            x1, [fp, #-8]
    // 0xaa66c4: StoreField: r1->field_2f = r0
    //     0xaa66c4: stur            w0, [x1, #0x2f]
    //     0xaa66c8: ldurb           w16, [x1, #-1]
    //     0xaa66cc: ldurb           w17, [x0, #-1]
    //     0xaa66d0: and             x16, x17, x16, lsr #2
    //     0xaa66d4: tst             x16, HEAP, lsr #32
    //     0xaa66d8: b.eq            #0xaa66e0
    //     0xaa66dc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa66e0: r0 = Null
    //     0xaa66e0: mov             x0, NULL
    // 0xaa66e4: LeaveFrame
    //     0xaa66e4: mov             SP, fp
    //     0xaa66e8: ldp             fp, lr, [SP], #0x10
    // 0xaa66ec: ret
    //     0xaa66ec: ret             
    // 0xaa66f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa66f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa66f4: b               #0xaa61c8
    // 0xaa66f8: r9 = index
    //     0xaa66f8: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd28] Field <<EMAIL>>: late (offset: 0x28)
    //     0xaa66fc: ldr             x9, [x9, #0xd28]
    // 0xaa6700: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa6700: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa6704: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa6704: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa6708: r9 = _scrollable
    //     0xaa6708: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0xaa670c: ldr             x9, [x9, #0xd10]
    // 0xaa6710: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa6710: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa6714: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa6714: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa6718: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa6718: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa671c: r9 = itemExtent
    //     0xaa671c: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd08] Field <<EMAIL>>: late (offset: 0x40)
    //     0xaa6720: ldr             x9, [x9, #0xd08]
    // 0xaa6724: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa6724: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa6728: r9 = _scrollable
    //     0xaa6728: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0xaa672c: ldr             x9, [x9, #0xd10]
    // 0xaa6730: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa6730: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa6734: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa6734: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa6738: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa6738: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa673c: r9 = _scrollable
    //     0xaa673c: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0xaa6740: ldr             x9, [x9, #0xd10]
    // 0xaa6744: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xaa6744: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xaa6748: r0 = NullCastErrorSharedWithFPURegs()
    //     0xaa6748: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xaa674c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa674c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa6750: r9 = itemExtent
    //     0xaa6750: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd08] Field <<EMAIL>>: late (offset: 0x40)
    //     0xaa6754: ldr             x9, [x9, #0xd08]
    // 0xaa6758: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa6758: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa675c: r9 = _scrollable
    //     0xaa675c: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0xaa6760: ldr             x9, [x9, #0xd10]
    // 0xaa6764: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa6764: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa6768: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa6768: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa676c: r9 = _scrollable
    //     0xaa676c: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0xaa6770: ldr             x9, [x9, #0xd10]
    // 0xaa6774: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xaa6774: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xaa6778: r0 = NullCastErrorSharedWithFPURegs()
    //     0xaa6778: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ _itemExtentAt(/* No info */) {
    // ** addr: 0xaa677c, size: 0x124
    // 0xaa677c: EnterFrame
    //     0xaa677c: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6780: mov             fp, SP
    // 0xaa6784: AllocStack(0x10)
    //     0xaa6784: sub             SP, SP, #0x10
    // 0xaa6788: SetupParameters(SliverReorderableListState this /* r1 => r3, fp-0x10 */)
    //     0xaa6788: mov             x3, x1
    //     0xaa678c: stur            x1, [fp, #-0x10]
    // 0xaa6790: CheckStackOverflow
    //     0xaa6790: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6794: cmp             SP, x16
    //     0xaa6798: b.ls            #0xaa6884
    // 0xaa679c: LoadField: r4 = r3->field_1b
    //     0xaa679c: ldur            w4, [x3, #0x1b]
    // 0xaa67a0: DecompressPointer r4
    //     0xaa67a0: add             x4, x4, HEAP, lsl #32
    // 0xaa67a4: stur            x4, [fp, #-8]
    // 0xaa67a8: r0 = BoxInt64Instr(r2)
    //     0xaa67a8: sbfiz           x0, x2, #1, #0x1f
    //     0xaa67ac: cmp             x2, x0, asr #1
    //     0xaa67b0: b.eq            #0xaa67bc
    //     0xaa67b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaa67b8: stur            x2, [x0, #7]
    // 0xaa67bc: mov             x1, x4
    // 0xaa67c0: mov             x2, x0
    // 0xaa67c4: r0 = _getValueOrData()
    //     0xaa67c4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xaa67c8: mov             x1, x0
    // 0xaa67cc: ldur            x0, [fp, #-8]
    // 0xaa67d0: LoadField: r2 = r0->field_f
    //     0xaa67d0: ldur            w2, [x0, #0xf]
    // 0xaa67d4: DecompressPointer r2
    //     0xaa67d4: add             x2, x2, HEAP, lsl #32
    // 0xaa67d8: cmp             w2, w1
    // 0xaa67dc: b.ne            #0xaa67e4
    // 0xaa67e0: r1 = Null
    //     0xaa67e0: mov             x1, NULL
    // 0xaa67e4: ldur            x0, [fp, #-0x10]
    // 0xaa67e8: cmp             w1, NULL
    // 0xaa67ec: b.eq            #0xaa688c
    // 0xaa67f0: r0 = targetGeometry()
    //     0xaa67f0: bl              #0x997c24  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::targetGeometry
    // 0xaa67f4: mov             x1, x0
    // 0xaa67f8: r0 = size()
    //     0xaa67f8: bl              #0x684980  ; [dart:ui] Rect::size
    // 0xaa67fc: mov             x1, x0
    // 0xaa6800: ldur            x0, [fp, #-0x10]
    // 0xaa6804: LoadField: r2 = r0->field_3f
    //     0xaa6804: ldur            w2, [x0, #0x3f]
    // 0xaa6808: DecompressPointer r2
    //     0xaa6808: add             x2, x2, HEAP, lsl #32
    // 0xaa680c: r16 = Sentinel
    //     0xaa680c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa6810: cmp             w2, w16
    // 0xaa6814: b.eq            #0xaa6890
    // 0xaa6818: LoadField: r0 = r2->field_b
    //     0xaa6818: ldur            w0, [x2, #0xb]
    // 0xaa681c: DecompressPointer r0
    //     0xaa681c: add             x0, x0, HEAP, lsl #32
    // 0xaa6820: cmp             w0, NULL
    // 0xaa6824: b.eq            #0xaa689c
    // 0xaa6828: LoadField: r2 = r0->field_b
    //     0xaa6828: ldur            w2, [x0, #0xb]
    // 0xaa682c: DecompressPointer r2
    //     0xaa682c: add             x2, x2, HEAP, lsl #32
    // 0xaa6830: r16 = Instance_AxisDirection
    //     0xaa6830: ldr             x16, [PP, #0x7568]  ; [pp+0x7568] Obj!AxisDirection@e35ea1
    // 0xaa6834: cmp             w2, w16
    // 0xaa6838: b.eq            #0xaa6848
    // 0xaa683c: r16 = Instance_AxisDirection
    //     0xaa683c: ldr             x16, [PP, #0x7570]  ; [pp+0x7570] Obj!AxisDirection@e35e81
    // 0xaa6840: cmp             w2, w16
    // 0xaa6844: b.ne            #0xaa6850
    // 0xaa6848: r2 = Instance_Axis
    //     0xaa6848: ldr             x2, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xaa684c: b               #0xaa6874
    // 0xaa6850: r16 = Instance_AxisDirection
    //     0xaa6850: ldr             x16, [PP, #0x7580]  ; [pp+0x7580] Obj!AxisDirection@e35ee1
    // 0xaa6854: cmp             w2, w16
    // 0xaa6858: b.eq            #0xaa6868
    // 0xaa685c: r16 = Instance_AxisDirection
    //     0xaa685c: ldr             x16, [PP, #0x7588]  ; [pp+0x7588] Obj!AxisDirection@e35ec1
    // 0xaa6860: cmp             w2, w16
    // 0xaa6864: b.ne            #0xaa6870
    // 0xaa6868: r2 = Instance_Axis
    //     0xaa6868: ldr             x2, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaa686c: b               #0xaa6874
    // 0xaa6870: r2 = Null
    //     0xaa6870: mov             x2, NULL
    // 0xaa6874: r0 = _sizeExtent()
    //     0xaa6874: bl              #0x997cf0  ; [package:flutter/src/widgets/reorderable_list.dart] ::_sizeExtent
    // 0xaa6878: LeaveFrame
    //     0xaa6878: mov             SP, fp
    //     0xaa687c: ldp             fp, lr, [SP], #0x10
    // 0xaa6880: ret
    //     0xaa6880: ret             
    // 0xaa6884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa6884: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6888: b               #0xaa679c
    // 0xaa688c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa688c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaa6890: r9 = _scrollable
    //     0xaa6890: add             x9, PP, #0x4f, lsl #12  ; [pp+0x4fd10] Field <SliverReorderableListState._scrollable@318218688>: late (offset: 0x40)
    //     0xaa6894: ldr             x9, [x9, #0xd10]
    // 0xaa6898: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa6898: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa689c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa689c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _itemOffsetAt(/* No info */) {
    // ** addr: 0xaa68a0, size: 0xa8
    // 0xaa68a0: EnterFrame
    //     0xaa68a0: stp             fp, lr, [SP, #-0x10]!
    //     0xaa68a4: mov             fp, SP
    // 0xaa68a8: AllocStack(0x18)
    //     0xaa68a8: sub             SP, SP, #0x18
    // 0xaa68ac: CheckStackOverflow
    //     0xaa68ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa68b0: cmp             SP, x16
    //     0xaa68b4: b.ls            #0xaa693c
    // 0xaa68b8: LoadField: r3 = r1->field_1b
    //     0xaa68b8: ldur            w3, [x1, #0x1b]
    // 0xaa68bc: DecompressPointer r3
    //     0xaa68bc: add             x3, x3, HEAP, lsl #32
    // 0xaa68c0: stur            x3, [fp, #-8]
    // 0xaa68c4: r0 = BoxInt64Instr(r2)
    //     0xaa68c4: sbfiz           x0, x2, #1, #0x1f
    //     0xaa68c8: cmp             x2, x0, asr #1
    //     0xaa68cc: b.eq            #0xaa68d8
    //     0xaa68d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaa68d4: stur            x2, [x0, #7]
    // 0xaa68d8: mov             x1, x3
    // 0xaa68dc: mov             x2, x0
    // 0xaa68e0: r0 = _getValueOrData()
    //     0xaa68e0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xaa68e4: mov             x1, x0
    // 0xaa68e8: ldur            x0, [fp, #-8]
    // 0xaa68ec: LoadField: r2 = r0->field_f
    //     0xaa68ec: ldur            w2, [x0, #0xf]
    // 0xaa68f0: DecompressPointer r2
    //     0xaa68f0: add             x2, x2, HEAP, lsl #32
    // 0xaa68f4: cmp             w2, w1
    // 0xaa68f8: b.ne            #0xaa6900
    // 0xaa68fc: r1 = Null
    //     0xaa68fc: mov             x1, NULL
    // 0xaa6900: cmp             w1, NULL
    // 0xaa6904: b.eq            #0xaa6944
    // 0xaa6908: r0 = targetGeometry()
    //     0xaa6908: bl              #0x997c24  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::targetGeometry
    // 0xaa690c: LoadField: d0 = r0->field_7
    //     0xaa690c: ldur            d0, [x0, #7]
    // 0xaa6910: stur            d0, [fp, #-0x18]
    // 0xaa6914: LoadField: d1 = r0->field_f
    //     0xaa6914: ldur            d1, [x0, #0xf]
    // 0xaa6918: stur            d1, [fp, #-0x10]
    // 0xaa691c: r0 = Offset()
    //     0xaa691c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xaa6920: ldur            d0, [fp, #-0x18]
    // 0xaa6924: StoreField: r0->field_7 = d0
    //     0xaa6924: stur            d0, [x0, #7]
    // 0xaa6928: ldur            d0, [fp, #-0x10]
    // 0xaa692c: StoreField: r0->field_f = d0
    //     0xaa692c: stur            d0, [x0, #0xf]
    // 0xaa6930: LeaveFrame
    //     0xaa6930: mov             SP, fp
    //     0xaa6934: ldp             fp, lr, [SP], #0x10
    // 0xaa6938: ret
    //     0xaa6938: ret             
    // 0xaa693c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa693c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6940: b               #0xaa68b8
    // 0xaa6944: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa6944: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _dragCancel(dynamic, _DragInfo) {
    // ** addr: 0xaa6948, size: 0x3c
    // 0xaa6948: EnterFrame
    //     0xaa6948: stp             fp, lr, [SP, #-0x10]!
    //     0xaa694c: mov             fp, SP
    // 0xaa6950: ldr             x0, [fp, #0x18]
    // 0xaa6954: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa6954: ldur            w1, [x0, #0x17]
    // 0xaa6958: DecompressPointer r1
    //     0xaa6958: add             x1, x1, HEAP, lsl #32
    // 0xaa695c: CheckStackOverflow
    //     0xaa695c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6960: cmp             SP, x16
    //     0xaa6964: b.ls            #0xaa697c
    // 0xaa6968: ldr             x2, [fp, #0x10]
    // 0xaa696c: r0 = _dragCancel()
    //     0xaa696c: bl              #0xaa6984  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragCancel
    // 0xaa6970: LeaveFrame
    //     0xaa6970: mov             SP, fp
    //     0xaa6974: ldp             fp, lr, [SP], #0x10
    // 0xaa6978: ret
    //     0xaa6978: ret             
    // 0xaa697c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa697c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6980: b               #0xaa6968
  }
  _ _dragCancel(/* No info */) {
    // ** addr: 0xaa6984, size: 0x64
    // 0xaa6984: EnterFrame
    //     0xaa6984: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6988: mov             fp, SP
    // 0xaa698c: AllocStack(0x8)
    //     0xaa698c: sub             SP, SP, #8
    // 0xaa6990: SetupParameters(SliverReorderableListState this /* r1 => r1, fp-0x8 */)
    //     0xaa6990: stur            x1, [fp, #-8]
    // 0xaa6994: CheckStackOverflow
    //     0xaa6994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6998: cmp             SP, x16
    //     0xaa699c: b.ls            #0xaa69e0
    // 0xaa69a0: r1 = 1
    //     0xaa69a0: movz            x1, #0x1
    // 0xaa69a4: r0 = AllocateContext()
    //     0xaa69a4: bl              #0xec126c  ; AllocateContextStub
    // 0xaa69a8: mov             x1, x0
    // 0xaa69ac: ldur            x0, [fp, #-8]
    // 0xaa69b0: StoreField: r1->field_f = r0
    //     0xaa69b0: stur            w0, [x1, #0xf]
    // 0xaa69b4: mov             x2, x1
    // 0xaa69b8: r1 = Function '<anonymous closure>':.
    //     0xaa69b8: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fd98] AnonymousClosure: (0x995e88), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dropCompleted (0x995ed0)
    //     0xaa69bc: ldr             x1, [x1, #0xd98]
    // 0xaa69c0: r0 = AllocateClosure()
    //     0xaa69c0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa69c4: ldur            x1, [fp, #-8]
    // 0xaa69c8: mov             x2, x0
    // 0xaa69cc: r0 = setState()
    //     0xaa69cc: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaa69d0: r0 = Null
    //     0xaa69d0: mov             x0, NULL
    // 0xaa69d4: LeaveFrame
    //     0xaa69d4: mov             SP, fp
    //     0xaa69d8: ldp             fp, lr, [SP], #0x10
    // 0xaa69dc: ret
    //     0xaa69dc: ret             
    // 0xaa69e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa69e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa69e4: b               #0xaa69a0
  }
  [closure] void _dragUpdate(dynamic, _DragInfo, Offset, Offset) {
    // ** addr: 0xaa69e8, size: 0x44
    // 0xaa69e8: EnterFrame
    //     0xaa69e8: stp             fp, lr, [SP, #-0x10]!
    //     0xaa69ec: mov             fp, SP
    // 0xaa69f0: ldr             x0, [fp, #0x28]
    // 0xaa69f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa69f4: ldur            w1, [x0, #0x17]
    // 0xaa69f8: DecompressPointer r1
    //     0xaa69f8: add             x1, x1, HEAP, lsl #32
    // 0xaa69fc: CheckStackOverflow
    //     0xaa69fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6a00: cmp             SP, x16
    //     0xaa6a04: b.ls            #0xaa6a24
    // 0xaa6a08: ldr             x2, [fp, #0x20]
    // 0xaa6a0c: ldr             x3, [fp, #0x18]
    // 0xaa6a10: ldr             x5, [fp, #0x10]
    // 0xaa6a14: r0 = _dragUpdate()
    //     0xaa6a14: bl              #0xaa6a2c  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragUpdate
    // 0xaa6a18: LeaveFrame
    //     0xaa6a18: mov             SP, fp
    //     0xaa6a1c: ldp             fp, lr, [SP], #0x10
    // 0xaa6a20: ret
    //     0xaa6a20: ret             
    // 0xaa6a24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa6a24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6a28: b               #0xaa6a08
  }
  _ _dragUpdate(/* No info */) {
    // ** addr: 0xaa6a2c, size: 0x64
    // 0xaa6a2c: EnterFrame
    //     0xaa6a2c: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6a30: mov             fp, SP
    // 0xaa6a34: AllocStack(0x8)
    //     0xaa6a34: sub             SP, SP, #8
    // 0xaa6a38: SetupParameters(SliverReorderableListState this /* r1 => r1, fp-0x8 */)
    //     0xaa6a38: stur            x1, [fp, #-8]
    // 0xaa6a3c: CheckStackOverflow
    //     0xaa6a3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6a40: cmp             SP, x16
    //     0xaa6a44: b.ls            #0xaa6a88
    // 0xaa6a48: r1 = 1
    //     0xaa6a48: movz            x1, #0x1
    // 0xaa6a4c: r0 = AllocateContext()
    //     0xaa6a4c: bl              #0xec126c  ; AllocateContextStub
    // 0xaa6a50: mov             x1, x0
    // 0xaa6a54: ldur            x0, [fp, #-8]
    // 0xaa6a58: StoreField: r1->field_f = r0
    //     0xaa6a58: stur            w0, [x1, #0xf]
    // 0xaa6a5c: mov             x2, x1
    // 0xaa6a60: r1 = Function '<anonymous closure>':.
    //     0xaa6a60: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fda0] AnonymousClosure: (0xaa6a90), in [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragUpdate (0xaa6a2c)
    //     0xaa6a64: ldr             x1, [x1, #0xda0]
    // 0xaa6a68: r0 = AllocateClosure()
    //     0xaa6a68: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa6a6c: ldur            x1, [fp, #-8]
    // 0xaa6a70: mov             x2, x0
    // 0xaa6a74: r0 = setState()
    //     0xaa6a74: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaa6a78: r0 = Null
    //     0xaa6a78: mov             x0, NULL
    // 0xaa6a7c: LeaveFrame
    //     0xaa6a7c: mov             SP, fp
    //     0xaa6a80: ldp             fp, lr, [SP], #0x10
    // 0xaa6a84: ret
    //     0xaa6a84: ret             
    // 0xaa6a88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa6a88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6a8c: b               #0xaa6a48
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaa6a90, size: 0xa4
    // 0xaa6a90: EnterFrame
    //     0xaa6a90: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6a94: mov             fp, SP
    // 0xaa6a98: AllocStack(0x8)
    //     0xaa6a98: sub             SP, SP, #8
    // 0xaa6a9c: SetupParameters()
    //     0xaa6a9c: ldr             x0, [fp, #0x10]
    //     0xaa6aa0: ldur            w2, [x0, #0x17]
    //     0xaa6aa4: add             x2, x2, HEAP, lsl #32
    //     0xaa6aa8: stur            x2, [fp, #-8]
    // 0xaa6aac: CheckStackOverflow
    //     0xaa6aac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6ab0: cmp             SP, x16
    //     0xaa6ab4: b.ls            #0xaa6b2c
    // 0xaa6ab8: LoadField: r0 = r2->field_f
    //     0xaa6ab8: ldur            w0, [x2, #0xf]
    // 0xaa6abc: DecompressPointer r0
    //     0xaa6abc: add             x0, x0, HEAP, lsl #32
    // 0xaa6ac0: LoadField: r1 = r0->field_1f
    //     0xaa6ac0: ldur            w1, [x0, #0x1f]
    // 0xaa6ac4: DecompressPointer r1
    //     0xaa6ac4: add             x1, x1, HEAP, lsl #32
    // 0xaa6ac8: cmp             w1, NULL
    // 0xaa6acc: b.ne            #0xaa6ad8
    // 0xaa6ad0: mov             x0, x2
    // 0xaa6ad4: b               #0xaa6ae0
    // 0xaa6ad8: r0 = markNeedsBuild()
    //     0xaa6ad8: bl              #0x650960  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::markNeedsBuild
    // 0xaa6adc: ldur            x0, [fp, #-8]
    // 0xaa6ae0: LoadField: r1 = r0->field_f
    //     0xaa6ae0: ldur            w1, [x0, #0xf]
    // 0xaa6ae4: DecompressPointer r1
    //     0xaa6ae4: add             x1, x1, HEAP, lsl #32
    // 0xaa6ae8: r0 = _dragUpdateItems()
    //     0xaa6ae8: bl              #0x997330  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragUpdateItems
    // 0xaa6aec: ldur            x0, [fp, #-8]
    // 0xaa6af0: LoadField: r1 = r0->field_f
    //     0xaa6af0: ldur            w1, [x0, #0xf]
    // 0xaa6af4: DecompressPointer r1
    //     0xaa6af4: add             x1, x1, HEAP, lsl #32
    // 0xaa6af8: LoadField: r0 = r1->field_3b
    //     0xaa6af8: ldur            w0, [x1, #0x3b]
    // 0xaa6afc: DecompressPointer r0
    //     0xaa6afc: add             x0, x0, HEAP, lsl #32
    // 0xaa6b00: stur            x0, [fp, #-8]
    // 0xaa6b04: cmp             w0, NULL
    // 0xaa6b08: b.eq            #0xaa6b1c
    // 0xaa6b0c: r0 = _dragTargetRect()
    //     0xaa6b0c: bl              #0x997218  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_dragTargetRect
    // 0xaa6b10: ldur            x1, [fp, #-8]
    // 0xaa6b14: mov             x2, x0
    // 0xaa6b18: r0 = startAutoScrollIfNecessary()
    //     0xaa6b18: bl              #0x9963bc  ; [package:flutter/src/widgets/scrollable_helpers.dart] EdgeDraggingAutoScroller::startAutoScrollIfNecessary
    // 0xaa6b1c: r0 = Null
    //     0xaa6b1c: mov             x0, NULL
    // 0xaa6b20: LeaveFrame
    //     0xaa6b20: mov             SP, fp
    //     0xaa6b24: ldp             fp, lr, [SP], #0x10
    // 0xaa6b28: ret
    //     0xaa6b28: ret             
    // 0xaa6b2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa6b2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6b30: b               #0xaa6ab8
  }
}

// class id: 4763, size: 0x1c, field offset: 0xc
//   const constructor, 
class _ReorderableItem extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa929e4, size: 0x40
    // 0xa929e4: EnterFrame
    //     0xa929e4: stp             fp, lr, [SP, #-0x10]!
    //     0xa929e8: mov             fp, SP
    // 0xa929ec: mov             x0, x1
    // 0xa929f0: r1 = <_ReorderableItem>
    //     0xa929f0: add             x1, PP, #0x59, lsl #12  ; [pp+0x59bb8] TypeArguments: <_ReorderableItem>
    //     0xa929f4: ldr             x1, [x1, #0xbb8]
    // 0xa929f8: r0 = _ReorderableItemState()
    //     0xa929f8: bl              #0xa92a24  ; Allocate_ReorderableItemStateStub -> _ReorderableItemState (size=0x28)
    // 0xa929fc: r1 = Sentinel
    //     0xa929fc: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa92a00: StoreField: r0->field_13 = r1
    //     0xa92a00: stur            w1, [x0, #0x13]
    // 0xa92a04: r1 = Instance_Offset
    //     0xa92a04: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xa92a08: ArrayStore: r0[0] = r1  ; List_4
    //     0xa92a08: stur            w1, [x0, #0x17]
    // 0xa92a0c: StoreField: r0->field_1b = r1
    //     0xa92a0c: stur            w1, [x0, #0x1b]
    // 0xa92a10: r1 = false
    //     0xa92a10: add             x1, NULL, #0x30  ; false
    // 0xa92a14: StoreField: r0->field_23 = r1
    //     0xa92a14: stur            w1, [x0, #0x23]
    // 0xa92a18: LeaveFrame
    //     0xa92a18: mov             SP, fp
    //     0xa92a1c: ldp             fp, lr, [SP], #0x10
    // 0xa92a20: ret
    //     0xa92a20: ret             
  }
}

// class id: 4764, size: 0x40, field offset: 0xc
//   const constructor, 
class SliverReorderableList extends StatefulWidget {

  static _ of(/* No info */) {
    // ** addr: 0x945b10, size: 0x4c
    // 0x945b10: EnterFrame
    //     0x945b10: stp             fp, lr, [SP, #-0x10]!
    //     0x945b14: mov             fp, SP
    // 0x945b18: AllocStack(0x10)
    //     0x945b18: sub             SP, SP, #0x10
    // 0x945b1c: CheckStackOverflow
    //     0x945b1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x945b20: cmp             SP, x16
    //     0x945b24: b.ls            #0x945b50
    // 0x945b28: r16 = <SliverReorderableListState>
    //     0x945b28: add             x16, PP, #0x4f, lsl #12  ; [pp+0x4fe60] TypeArguments: <SliverReorderableListState>
    //     0x945b2c: ldr             x16, [x16, #0xe60]
    // 0x945b30: stp             x1, x16, [SP]
    // 0x945b34: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x945b34: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x945b38: r0 = findAncestorStateOfType()
    //     0x945b38: bl              #0x6a4a9c  ; [package:flutter/src/widgets/framework.dart] Element::findAncestorStateOfType
    // 0x945b3c: cmp             w0, NULL
    // 0x945b40: b.eq            #0x945b58
    // 0x945b44: LeaveFrame
    //     0x945b44: mov             SP, fp
    //     0x945b48: ldp             fp, lr, [SP], #0x10
    // 0x945b4c: ret
    //     0x945b4c: ret             
    // 0x945b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945b50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x945b54: b               #0x945b28
    // 0x945b58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945b58: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ createState(/* No info */) {
    // ** addr: 0xa92958, size: 0x80
    // 0xa92958: EnterFrame
    //     0xa92958: stp             fp, lr, [SP, #-0x10]!
    //     0xa9295c: mov             fp, SP
    // 0xa92960: AllocStack(0x18)
    //     0xa92960: sub             SP, SP, #0x18
    // 0xa92964: CheckStackOverflow
    //     0xa92964: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa92968: cmp             SP, x16
    //     0xa9296c: b.ls            #0xa929d0
    // 0xa92970: r1 = <SliverReorderableList>
    //     0xa92970: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fca0] TypeArguments: <SliverReorderableList>
    //     0xa92974: ldr             x1, [x1, #0xca0]
    // 0xa92978: r0 = SliverReorderableListState()
    //     0xa92978: bl              #0xa929d8  ; AllocateSliverReorderableListStateStub -> SliverReorderableListState (size=0x44)
    // 0xa9297c: mov             x1, x0
    // 0xa92980: r0 = Sentinel
    //     0xa92980: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa92984: stur            x1, [fp, #-8]
    // 0xa92988: StoreField: r1->field_3f = r0
    //     0xa92988: stur            w0, [x1, #0x3f]
    // 0xa9298c: r16 = <int, _ReorderableItemState>
    //     0xa9298c: add             x16, PP, #0x4f, lsl #12  ; [pp+0x4fca8] TypeArguments: <int, _ReorderableItemState>
    //     0xa92990: ldr             x16, [x16, #0xca8]
    // 0xa92994: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa92998: stp             lr, x16, [SP]
    // 0xa9299c: r0 = Map._fromLiteral()
    //     0xa9299c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa929a0: ldur            x1, [fp, #-8]
    // 0xa929a4: StoreField: r1->field_1b = r0
    //     0xa929a4: stur            w0, [x1, #0x1b]
    //     0xa929a8: ldurb           w16, [x1, #-1]
    //     0xa929ac: ldurb           w17, [x0, #-1]
    //     0xa929b0: and             x16, x17, x16, lsr #2
    //     0xa929b4: tst             x16, HEAP, lsr #32
    //     0xa929b8: b.eq            #0xa929c0
    //     0xa929bc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa929c0: mov             x0, x1
    // 0xa929c4: LeaveFrame
    //     0xa929c4: mov             SP, fp
    //     0xa929c8: ldp             fp, lr, [SP], #0x10
    // 0xa929cc: ret
    //     0xa929cc: ret             
    // 0xa929d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa929d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa929d4: b               #0xa92970
  }
  static _ maybeOf(/* No info */) {
    // ** addr: 0xaa6c2c, size: 0x40
    // 0xaa6c2c: EnterFrame
    //     0xaa6c2c: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6c30: mov             fp, SP
    // 0xaa6c34: AllocStack(0x10)
    //     0xaa6c34: sub             SP, SP, #0x10
    // 0xaa6c38: CheckStackOverflow
    //     0xaa6c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6c3c: cmp             SP, x16
    //     0xaa6c40: b.ls            #0xaa6c64
    // 0xaa6c44: r16 = <SliverReorderableListState>
    //     0xaa6c44: add             x16, PP, #0x4f, lsl #12  ; [pp+0x4fe60] TypeArguments: <SliverReorderableListState>
    //     0xaa6c48: ldr             x16, [x16, #0xe60]
    // 0xaa6c4c: stp             x1, x16, [SP]
    // 0xaa6c50: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaa6c50: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaa6c54: r0 = findAncestorStateOfType()
    //     0xaa6c54: bl              #0x6a4a9c  ; [package:flutter/src/widgets/framework.dart] Element::findAncestorStateOfType
    // 0xaa6c58: LeaveFrame
    //     0xaa6c58: mov             SP, fp
    //     0xaa6c5c: ldp             fp, lr, [SP], #0x10
    // 0xaa6c60: ret
    //     0xaa6c60: ret             
    // 0xaa6c64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa6c64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6c68: b               #0xaa6c44
  }
}

// class id: 5331, size: 0x30, field offset: 0xc
//   const constructor, 
class _DragItemProxy extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xaa6c6c, size: 0x14c
    // 0xaa6c6c: EnterFrame
    //     0xaa6c6c: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6c70: mov             fp, SP
    // 0xaa6c74: AllocStack(0x28)
    //     0xaa6c74: sub             SP, SP, #0x28
    // 0xaa6c78: SetupParameters(_DragItemProxy this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xaa6c78: mov             x0, x1
    //     0xaa6c7c: stur            x1, [fp, #-8]
    //     0xaa6c80: mov             x1, x2
    //     0xaa6c84: stur            x2, [fp, #-0x10]
    // 0xaa6c88: CheckStackOverflow
    //     0xaa6c88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6c8c: cmp             SP, x16
    //     0xaa6c90: b.ls            #0xaa6dac
    // 0xaa6c94: r1 = 2
    //     0xaa6c94: movz            x1, #0x2
    // 0xaa6c98: r0 = AllocateContext()
    //     0xaa6c98: bl              #0xec126c  ; AllocateContextStub
    // 0xaa6c9c: mov             x4, x0
    // 0xaa6ca0: ldur            x0, [fp, #-8]
    // 0xaa6ca4: stur            x4, [fp, #-0x20]
    // 0xaa6ca8: StoreField: r4->field_f = r0
    //     0xaa6ca8: stur            w0, [x4, #0xf]
    // 0xaa6cac: LoadField: r1 = r0->field_2b
    //     0xaa6cac: ldur            w1, [x0, #0x2b]
    // 0xaa6cb0: DecompressPointer r1
    //     0xaa6cb0: add             x1, x1, HEAP, lsl #32
    // 0xaa6cb4: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xaa6cb4: ldur            w2, [x0, #0x17]
    // 0xaa6cb8: DecompressPointer r2
    //     0xaa6cb8: add             x2, x2, HEAP, lsl #32
    // 0xaa6cbc: LoadField: r3 = r0->field_f
    //     0xaa6cbc: ldur            x3, [x0, #0xf]
    // 0xaa6cc0: LoadField: r6 = r0->field_27
    //     0xaa6cc0: ldur            w6, [x0, #0x27]
    // 0xaa6cc4: DecompressPointer r6
    //     0xaa6cc4: add             x6, x6, HEAP, lsl #32
    // 0xaa6cc8: stur            x6, [fp, #-0x18]
    // 0xaa6ccc: cmp             w1, NULL
    // 0xaa6cd0: b.eq            #0xaa6db4
    // 0xaa6cd4: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xaa6cd4: ldur            w5, [x1, #0x17]
    // 0xaa6cd8: DecompressPointer r5
    //     0xaa6cd8: add             x5, x5, HEAP, lsl #32
    // 0xaa6cdc: r0 = BoxInt64Instr(r3)
    //     0xaa6cdc: sbfiz           x0, x3, #1, #0x1f
    //     0xaa6ce0: cmp             x3, x0, asr #1
    //     0xaa6ce4: b.eq            #0xaa6cf0
    //     0xaa6ce8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaa6cec: stur            x3, [x0, #7]
    // 0xaa6cf0: mov             x1, x5
    // 0xaa6cf4: mov             x3, x0
    // 0xaa6cf8: mov             x5, x6
    // 0xaa6cfc: r0 = _proxyDecorator()
    //     0xaa6cfc: bl              #0xa012f4  ; [package:flutter/src/material/reorderable_list.dart] _ReorderableListViewState::_proxyDecorator
    // 0xaa6d00: ldur            x1, [fp, #-0x10]
    // 0xaa6d04: stur            x0, [fp, #-8]
    // 0xaa6d08: r0 = _overlayOrigin()
    //     0xaa6d08: bl              #0xaa6018  ; [package:flutter/src/widgets/reorderable_list.dart] ::_overlayOrigin
    // 0xaa6d0c: ldur            x2, [fp, #-0x20]
    // 0xaa6d10: StoreField: r2->field_13 = r0
    //     0xaa6d10: stur            w0, [x2, #0x13]
    //     0xaa6d14: ldurb           w16, [x2, #-1]
    //     0xaa6d18: ldurb           w17, [x0, #-1]
    //     0xaa6d1c: and             x16, x17, x16, lsr #2
    //     0xaa6d20: tst             x16, HEAP, lsr #32
    //     0xaa6d24: b.eq            #0xaa6d2c
    //     0xaa6d28: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xaa6d2c: ldur            x1, [fp, #-0x10]
    // 0xaa6d30: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaa6d30: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaa6d34: r0 = _of()
    //     0xaa6d34: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xaa6d38: mov             x1, x0
    // 0xaa6d3c: r2 = true
    //     0xaa6d3c: add             x2, NULL, #0x20  ; true
    // 0xaa6d40: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaa6d40: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaa6d44: r0 = removePadding()
    //     0xaa6d44: bl              #0x9e8cac  ; [package:flutter/src/widgets/media_query.dart] MediaQueryData::removePadding
    // 0xaa6d48: ldur            x2, [fp, #-0x20]
    // 0xaa6d4c: r1 = Function '<anonymous closure>':.
    //     0xaa6d4c: add             x1, PP, #0x56, lsl #12  ; [pp+0x567d8] AnonymousClosure: (0xaa6db8), in [package:flutter/src/widgets/reorderable_list.dart] _DragItemProxy::build (0xaa6c6c)
    //     0xaa6d50: ldr             x1, [x1, #0x7d8]
    // 0xaa6d54: stur            x0, [fp, #-0x10]
    // 0xaa6d58: r0 = AllocateClosure()
    //     0xaa6d58: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa6d5c: stur            x0, [fp, #-0x20]
    // 0xaa6d60: r0 = AnimatedBuilder()
    //     0xaa6d60: bl              #0x7e5888  ; AllocateAnimatedBuilderStub -> AnimatedBuilder (size=0x18)
    // 0xaa6d64: mov             x2, x0
    // 0xaa6d68: ldur            x0, [fp, #-0x20]
    // 0xaa6d6c: stur            x2, [fp, #-0x28]
    // 0xaa6d70: StoreField: r2->field_f = r0
    //     0xaa6d70: stur            w0, [x2, #0xf]
    // 0xaa6d74: ldur            x0, [fp, #-8]
    // 0xaa6d78: StoreField: r2->field_13 = r0
    //     0xaa6d78: stur            w0, [x2, #0x13]
    // 0xaa6d7c: ldur            x0, [fp, #-0x18]
    // 0xaa6d80: StoreField: r2->field_b = r0
    //     0xaa6d80: stur            w0, [x2, #0xb]
    // 0xaa6d84: r1 = <_MediaQueryAspect>
    //     0xaa6d84: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d80] TypeArguments: <_MediaQueryAspect>
    //     0xaa6d88: ldr             x1, [x1, #0xd80]
    // 0xaa6d8c: r0 = MediaQuery()
    //     0xaa6d8c: bl              #0x9e6f0c  ; AllocateMediaQueryStub -> MediaQuery (size=0x18)
    // 0xaa6d90: ldur            x1, [fp, #-0x10]
    // 0xaa6d94: StoreField: r0->field_13 = r1
    //     0xaa6d94: stur            w1, [x0, #0x13]
    // 0xaa6d98: ldur            x1, [fp, #-0x28]
    // 0xaa6d9c: StoreField: r0->field_b = r1
    //     0xaa6d9c: stur            w1, [x0, #0xb]
    // 0xaa6da0: LeaveFrame
    //     0xaa6da0: mov             SP, fp
    //     0xaa6da4: ldp             fp, lr, [SP], #0x10
    // 0xaa6da8: ret
    //     0xaa6da8: ret             
    // 0xaa6dac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa6dac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6db0: b               #0xaa6c94
    // 0xaa6db4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xaa6db4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] Positioned <anonymous closure>(dynamic, BuildContext, Widget?) {
    // ** addr: 0xaa6db8, size: 0x330
    // 0xaa6db8: EnterFrame
    //     0xaa6db8: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6dbc: mov             fp, SP
    // 0xaa6dc0: AllocStack(0x58)
    //     0xaa6dc0: sub             SP, SP, #0x58
    // 0xaa6dc4: SetupParameters()
    //     0xaa6dc4: ldr             x0, [fp, #0x20]
    //     0xaa6dc8: ldur            w3, [x0, #0x17]
    //     0xaa6dcc: add             x3, x3, HEAP, lsl #32
    //     0xaa6dd0: stur            x3, [fp, #-0x10]
    // 0xaa6dd4: CheckStackOverflow
    //     0xaa6dd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6dd8: cmp             SP, x16
    //     0xaa6ddc: b.ls            #0xaa7074
    // 0xaa6de0: LoadField: r0 = r3->field_f
    //     0xaa6de0: ldur            w0, [x3, #0xf]
    // 0xaa6de4: DecompressPointer r0
    //     0xaa6de4: add             x0, x0, HEAP, lsl #32
    // 0xaa6de8: LoadField: r4 = r0->field_1b
    //     0xaa6de8: ldur            w4, [x0, #0x1b]
    // 0xaa6dec: DecompressPointer r4
    //     0xaa6dec: add             x4, x4, HEAP, lsl #32
    // 0xaa6df0: stur            x4, [fp, #-8]
    // 0xaa6df4: LoadField: r1 = r0->field_b
    //     0xaa6df4: ldur            w1, [x0, #0xb]
    // 0xaa6df8: DecompressPointer r1
    //     0xaa6df8: add             x1, x1, HEAP, lsl #32
    // 0xaa6dfc: LoadField: r0 = r1->field_2f
    //     0xaa6dfc: ldur            w0, [x1, #0x2f]
    // 0xaa6e00: DecompressPointer r0
    //     0xaa6e00: add             x0, x0, HEAP, lsl #32
    // 0xaa6e04: cmp             w0, NULL
    // 0xaa6e08: b.eq            #0xaa6e70
    // 0xaa6e0c: LoadField: r2 = r3->field_13
    //     0xaa6e0c: ldur            w2, [x3, #0x13]
    // 0xaa6e10: DecompressPointer r2
    //     0xaa6e10: add             x2, x2, HEAP, lsl #32
    // 0xaa6e14: mov             x1, x0
    // 0xaa6e18: r0 = -()
    //     0xaa6e18: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xaa6e1c: mov             x2, x0
    // 0xaa6e20: ldur            x0, [fp, #-0x10]
    // 0xaa6e24: stur            x2, [fp, #-0x18]
    // 0xaa6e28: LoadField: r1 = r0->field_f
    //     0xaa6e28: ldur            w1, [x0, #0xf]
    // 0xaa6e2c: DecompressPointer r1
    //     0xaa6e2c: add             x1, x1, HEAP, lsl #32
    // 0xaa6e30: LoadField: r3 = r1->field_27
    //     0xaa6e30: ldur            w3, [x1, #0x27]
    // 0xaa6e34: DecompressPointer r3
    //     0xaa6e34: add             x3, x3, HEAP, lsl #32
    // 0xaa6e38: LoadField: r1 = r3->field_37
    //     0xaa6e38: ldur            w1, [x3, #0x37]
    // 0xaa6e3c: DecompressPointer r1
    //     0xaa6e3c: add             x1, x1, HEAP, lsl #32
    // 0xaa6e40: r16 = Sentinel
    //     0xaa6e40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa6e44: cmp             w1, w16
    // 0xaa6e48: b.eq            #0xaa707c
    // 0xaa6e4c: LoadField: d0 = r1->field_7
    //     0xaa6e4c: ldur            d0, [x1, #7]
    // 0xaa6e50: r1 = Instance_Cubic
    //     0xaa6e50: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!Cubic@e14e61
    //     0xaa6e54: ldr             x1, [x1, #0xb28]
    // 0xaa6e58: r0 = transform()
    //     0xaa6e58: bl              #0xcd6c40  ; [package:flutter/src/animation/curves.dart] Curve::transform
    // 0xaa6e5c: ldur            x1, [fp, #-0x18]
    // 0xaa6e60: ldur            x2, [fp, #-8]
    // 0xaa6e64: r0 = lerp()
    //     0xaa6e64: bl              #0x7f5018  ; [dart:ui] Offset::lerp
    // 0xaa6e68: mov             x1, x0
    // 0xaa6e6c: b               #0xaa6e74
    // 0xaa6e70: ldur            x1, [fp, #-8]
    // 0xaa6e74: ldur            x0, [fp, #-0x10]
    // 0xaa6e78: LoadField: d0 = r1->field_7
    //     0xaa6e78: ldur            d0, [x1, #7]
    // 0xaa6e7c: stur            d0, [fp, #-0x58]
    // 0xaa6e80: LoadField: d1 = r1->field_f
    //     0xaa6e80: ldur            d1, [x1, #0xf]
    // 0xaa6e84: stur            d1, [fp, #-0x50]
    // 0xaa6e88: LoadField: r1 = r0->field_f
    //     0xaa6e88: ldur            w1, [x0, #0xf]
    // 0xaa6e8c: DecompressPointer r1
    //     0xaa6e8c: add             x1, x1, HEAP, lsl #32
    // 0xaa6e90: LoadField: r0 = r1->field_1f
    //     0xaa6e90: ldur            w0, [x1, #0x1f]
    // 0xaa6e94: DecompressPointer r0
    //     0xaa6e94: add             x0, x0, HEAP, lsl #32
    // 0xaa6e98: LoadField: d2 = r0->field_7
    //     0xaa6e98: ldur            d2, [x0, #7]
    // 0xaa6e9c: stur            d2, [fp, #-0x48]
    // 0xaa6ea0: LoadField: d3 = r0->field_f
    //     0xaa6ea0: ldur            d3, [x0, #0xf]
    // 0xaa6ea4: stur            d3, [fp, #-0x40]
    // 0xaa6ea8: LoadField: r0 = r1->field_23
    //     0xaa6ea8: ldur            w0, [x1, #0x23]
    // 0xaa6eac: DecompressPointer r0
    //     0xaa6eac: add             x0, x0, HEAP, lsl #32
    // 0xaa6eb0: LoadField: d4 = r0->field_7
    //     0xaa6eb0: ldur            d4, [x0, #7]
    // 0xaa6eb4: stur            d4, [fp, #-0x38]
    // 0xaa6eb8: ArrayLoad: d5 = r0[0]  ; List_8
    //     0xaa6eb8: ldur            d5, [x0, #0x17]
    // 0xaa6ebc: stur            d5, [fp, #-0x30]
    // 0xaa6ec0: LoadField: d6 = r0->field_f
    //     0xaa6ec0: ldur            d6, [x0, #0xf]
    // 0xaa6ec4: stur            d6, [fp, #-0x28]
    // 0xaa6ec8: LoadField: d7 = r0->field_1f
    //     0xaa6ec8: ldur            d7, [x0, #0x1f]
    // 0xaa6ecc: stur            d7, [fp, #-0x20]
    // 0xaa6ed0: LoadField: r0 = r1->field_b
    //     0xaa6ed0: ldur            w0, [x1, #0xb]
    // 0xaa6ed4: DecompressPointer r0
    //     0xaa6ed4: add             x0, x0, HEAP, lsl #32
    // 0xaa6ed8: mov             x1, x0
    // 0xaa6edc: r0 = _scrollDirection()
    //     0xaa6edc: bl              #0x9458e8  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_scrollDirection
    // 0xaa6ee0: r16 = Instance_Axis
    //     0xaa6ee0: ldr             x16, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xaa6ee4: cmp             w0, w16
    // 0xaa6ee8: b.ne            #0xaa6ef8
    // 0xaa6eec: r1 = Instance_Alignment
    //     0xaa6eec: add             x1, PP, #0x24, lsl #12  ; [pp+0x24b78] Obj!Alignment@e13ed1
    //     0xaa6ef0: ldr             x1, [x1, #0xb78]
    // 0xaa6ef4: b               #0xaa6f00
    // 0xaa6ef8: r1 = Instance_Alignment
    //     0xaa6ef8: add             x1, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!Alignment@e13eb1
    //     0xaa6efc: ldr             x1, [x1, #0xe0]
    // 0xaa6f00: ldr             x0, [fp, #0x10]
    // 0xaa6f04: ldur            d4, [fp, #-0x38]
    // 0xaa6f08: ldur            d5, [fp, #-0x30]
    // 0xaa6f0c: ldur            d6, [fp, #-0x28]
    // 0xaa6f10: ldur            d7, [fp, #-0x20]
    // 0xaa6f14: ldur            d0, [fp, #-0x58]
    // 0xaa6f18: ldur            d1, [fp, #-0x50]
    // 0xaa6f1c: ldur            d2, [fp, #-0x48]
    // 0xaa6f20: ldur            d3, [fp, #-0x40]
    // 0xaa6f24: stur            x1, [fp, #-8]
    // 0xaa6f28: r0 = OverflowBox()
    //     0xaa6f28: bl              #0xaa70e8  ; AllocateOverflowBoxStub -> OverflowBox (size=0x38)
    // 0xaa6f2c: mov             x1, x0
    // 0xaa6f30: ldur            x0, [fp, #-8]
    // 0xaa6f34: stur            x1, [fp, #-0x10]
    // 0xaa6f38: StoreField: r1->field_f = r0
    //     0xaa6f38: stur            w0, [x1, #0xf]
    // 0xaa6f3c: ldur            d0, [fp, #-0x38]
    // 0xaa6f40: StoreField: r1->field_13 = d0
    //     0xaa6f40: stur            d0, [x1, #0x13]
    // 0xaa6f44: ldur            d0, [fp, #-0x28]
    // 0xaa6f48: StoreField: r1->field_1b = d0
    //     0xaa6f48: stur            d0, [x1, #0x1b]
    // 0xaa6f4c: ldur            d0, [fp, #-0x30]
    // 0xaa6f50: StoreField: r1->field_23 = d0
    //     0xaa6f50: stur            d0, [x1, #0x23]
    // 0xaa6f54: ldur            d0, [fp, #-0x20]
    // 0xaa6f58: StoreField: r1->field_2b = d0
    //     0xaa6f58: stur            d0, [x1, #0x2b]
    // 0xaa6f5c: r0 = Instance_OverflowBoxFit
    //     0xaa6f5c: add             x0, PP, #0x56, lsl #12  ; [pp+0x567e0] Obj!OverflowBoxFit@e354e1
    //     0xaa6f60: ldr             x0, [x0, #0x7e0]
    // 0xaa6f64: StoreField: r1->field_33 = r0
    //     0xaa6f64: stur            w0, [x1, #0x33]
    // 0xaa6f68: ldr             x0, [fp, #0x10]
    // 0xaa6f6c: StoreField: r1->field_b = r0
    //     0xaa6f6c: stur            w0, [x1, #0xb]
    // 0xaa6f70: ldur            d0, [fp, #-0x48]
    // 0xaa6f74: r0 = inline_Allocate_Double()
    //     0xaa6f74: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xaa6f78: add             x0, x0, #0x10
    //     0xaa6f7c: cmp             x2, x0
    //     0xaa6f80: b.ls            #0xaa7084
    //     0xaa6f84: str             x0, [THR, #0x50]  ; THR::top
    //     0xaa6f88: sub             x0, x0, #0xf
    //     0xaa6f8c: movz            x2, #0xe15c
    //     0xaa6f90: movk            x2, #0x3, lsl #16
    //     0xaa6f94: stur            x2, [x0, #-1]
    // 0xaa6f98: StoreField: r0->field_7 = d0
    //     0xaa6f98: stur            d0, [x0, #7]
    // 0xaa6f9c: stur            x0, [fp, #-8]
    // 0xaa6fa0: r0 = SizedBox()
    //     0xaa6fa0: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaa6fa4: mov             x2, x0
    // 0xaa6fa8: ldur            x0, [fp, #-8]
    // 0xaa6fac: stur            x2, [fp, #-0x18]
    // 0xaa6fb0: StoreField: r2->field_f = r0
    //     0xaa6fb0: stur            w0, [x2, #0xf]
    // 0xaa6fb4: ldur            d0, [fp, #-0x40]
    // 0xaa6fb8: r0 = inline_Allocate_Double()
    //     0xaa6fb8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaa6fbc: add             x0, x0, #0x10
    //     0xaa6fc0: cmp             x1, x0
    //     0xaa6fc4: b.ls            #0xaa709c
    //     0xaa6fc8: str             x0, [THR, #0x50]  ; THR::top
    //     0xaa6fcc: sub             x0, x0, #0xf
    //     0xaa6fd0: movz            x1, #0xe15c
    //     0xaa6fd4: movk            x1, #0x3, lsl #16
    //     0xaa6fd8: stur            x1, [x0, #-1]
    // 0xaa6fdc: StoreField: r0->field_7 = d0
    //     0xaa6fdc: stur            d0, [x0, #7]
    // 0xaa6fe0: StoreField: r2->field_13 = r0
    //     0xaa6fe0: stur            w0, [x2, #0x13]
    // 0xaa6fe4: ldur            x0, [fp, #-0x10]
    // 0xaa6fe8: StoreField: r2->field_b = r0
    //     0xaa6fe8: stur            w0, [x2, #0xb]
    // 0xaa6fec: ldur            d0, [fp, #-0x58]
    // 0xaa6ff0: r0 = inline_Allocate_Double()
    //     0xaa6ff0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaa6ff4: add             x0, x0, #0x10
    //     0xaa6ff8: cmp             x1, x0
    //     0xaa6ffc: b.ls            #0xaa70b4
    //     0xaa7000: str             x0, [THR, #0x50]  ; THR::top
    //     0xaa7004: sub             x0, x0, #0xf
    //     0xaa7008: movz            x1, #0xe15c
    //     0xaa700c: movk            x1, #0x3, lsl #16
    //     0xaa7010: stur            x1, [x0, #-1]
    // 0xaa7014: StoreField: r0->field_7 = d0
    //     0xaa7014: stur            d0, [x0, #7]
    // 0xaa7018: stur            x0, [fp, #-8]
    // 0xaa701c: r1 = <StackParentData>
    //     0xaa701c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25780] TypeArguments: <StackParentData>
    //     0xaa7020: ldr             x1, [x1, #0x780]
    // 0xaa7024: r0 = Positioned()
    //     0xaa7024: bl              #0x9f19f8  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xaa7028: ldur            x1, [fp, #-8]
    // 0xaa702c: StoreField: r0->field_13 = r1
    //     0xaa702c: stur            w1, [x0, #0x13]
    // 0xaa7030: ldur            d0, [fp, #-0x50]
    // 0xaa7034: r1 = inline_Allocate_Double()
    //     0xaa7034: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xaa7038: add             x1, x1, #0x10
    //     0xaa703c: cmp             x2, x1
    //     0xaa7040: b.ls            #0xaa70cc
    //     0xaa7044: str             x1, [THR, #0x50]  ; THR::top
    //     0xaa7048: sub             x1, x1, #0xf
    //     0xaa704c: movz            x2, #0xe15c
    //     0xaa7050: movk            x2, #0x3, lsl #16
    //     0xaa7054: stur            x2, [x1, #-1]
    // 0xaa7058: StoreField: r1->field_7 = d0
    //     0xaa7058: stur            d0, [x1, #7]
    // 0xaa705c: ArrayStore: r0[0] = r1  ; List_4
    //     0xaa705c: stur            w1, [x0, #0x17]
    // 0xaa7060: ldur            x1, [fp, #-0x18]
    // 0xaa7064: StoreField: r0->field_b = r1
    //     0xaa7064: stur            w1, [x0, #0xb]
    // 0xaa7068: LeaveFrame
    //     0xaa7068: mov             SP, fp
    //     0xaa706c: ldp             fp, lr, [SP], #0x10
    // 0xaa7070: ret
    //     0xaa7070: ret             
    // 0xaa7074: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa7074: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa7078: b               #0xaa6de0
    // 0xaa707c: r9 = _value
    //     0xaa707c: ldr             x9, [PP, #0x4ea8]  ; [pp+0x4ea8] Field <AnimationController._value@441066280>: late (offset: 0x38)
    // 0xaa7080: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa7080: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaa7084: SaveReg d0
    //     0xaa7084: str             q0, [SP, #-0x10]!
    // 0xaa7088: SaveReg r1
    //     0xaa7088: str             x1, [SP, #-8]!
    // 0xaa708c: r0 = AllocateDouble()
    //     0xaa708c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xaa7090: RestoreReg r1
    //     0xaa7090: ldr             x1, [SP], #8
    // 0xaa7094: RestoreReg d0
    //     0xaa7094: ldr             q0, [SP], #0x10
    // 0xaa7098: b               #0xaa6f98
    // 0xaa709c: SaveReg d0
    //     0xaa709c: str             q0, [SP, #-0x10]!
    // 0xaa70a0: SaveReg r2
    //     0xaa70a0: str             x2, [SP, #-8]!
    // 0xaa70a4: r0 = AllocateDouble()
    //     0xaa70a4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xaa70a8: RestoreReg r2
    //     0xaa70a8: ldr             x2, [SP], #8
    // 0xaa70ac: RestoreReg d0
    //     0xaa70ac: ldr             q0, [SP], #0x10
    // 0xaa70b0: b               #0xaa6fdc
    // 0xaa70b4: SaveReg d0
    //     0xaa70b4: str             q0, [SP, #-0x10]!
    // 0xaa70b8: SaveReg r2
    //     0xaa70b8: str             x2, [SP, #-8]!
    // 0xaa70bc: r0 = AllocateDouble()
    //     0xaa70bc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xaa70c0: RestoreReg r2
    //     0xaa70c0: ldr             x2, [SP], #8
    // 0xaa70c4: RestoreReg d0
    //     0xaa70c4: ldr             q0, [SP], #0x10
    // 0xaa70c8: b               #0xaa7014
    // 0xaa70cc: SaveReg d0
    //     0xaa70cc: str             q0, [SP, #-0x10]!
    // 0xaa70d0: SaveReg r0
    //     0xaa70d0: str             x0, [SP, #-8]!
    // 0xaa70d4: r0 = AllocateDouble()
    //     0xaa70d4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xaa70d8: mov             x1, x0
    // 0xaa70dc: RestoreReg r0
    //     0xaa70dc: ldr             x0, [SP], #8
    // 0xaa70e0: RestoreReg d0
    //     0xaa70e0: ldr             q0, [SP], #0x10
    // 0xaa70e4: b               #0xaa7058
  }
}

// class id: 5332, size: 0x1c, field offset: 0xc
//   const constructor, 
class ReorderableDragStartListener extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xaa4de4, size: 0x80
    // 0xaa4de4: EnterFrame
    //     0xaa4de4: stp             fp, lr, [SP, #-0x10]!
    //     0xaa4de8: mov             fp, SP
    // 0xaa4dec: AllocStack(0x18)
    //     0xaa4dec: sub             SP, SP, #0x18
    // 0xaa4df0: SetupParameters(ReorderableDragStartListener this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xaa4df0: stur            x1, [fp, #-8]
    //     0xaa4df4: stur            x2, [fp, #-0x10]
    // 0xaa4df8: r1 = 2
    //     0xaa4df8: movz            x1, #0x2
    // 0xaa4dfc: r0 = AllocateContext()
    //     0xaa4dfc: bl              #0xec126c  ; AllocateContextStub
    // 0xaa4e00: mov             x1, x0
    // 0xaa4e04: ldur            x0, [fp, #-8]
    // 0xaa4e08: stur            x1, [fp, #-0x18]
    // 0xaa4e0c: StoreField: r1->field_f = r0
    //     0xaa4e0c: stur            w0, [x1, #0xf]
    // 0xaa4e10: ldur            x2, [fp, #-0x10]
    // 0xaa4e14: StoreField: r1->field_13 = r2
    //     0xaa4e14: stur            w2, [x1, #0x13]
    // 0xaa4e18: LoadField: r2 = r0->field_b
    //     0xaa4e18: ldur            w2, [x0, #0xb]
    // 0xaa4e1c: DecompressPointer r2
    //     0xaa4e1c: add             x2, x2, HEAP, lsl #32
    // 0xaa4e20: stur            x2, [fp, #-0x10]
    // 0xaa4e24: r0 = Listener()
    //     0xaa4e24: bl              #0x9daab0  ; AllocateListenerStub -> Listener (size=0x38)
    // 0xaa4e28: ldur            x2, [fp, #-0x18]
    // 0xaa4e2c: r1 = Function '<anonymous closure>':.
    //     0xaa4e2c: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fcb0] AnonymousClosure: (0xaa4e64), in [package:flutter/src/widgets/reorderable_list.dart] ReorderableDragStartListener::build (0xaa4de4)
    //     0xaa4e30: ldr             x1, [x1, #0xcb0]
    // 0xaa4e34: stur            x0, [fp, #-8]
    // 0xaa4e38: r0 = AllocateClosure()
    //     0xaa4e38: bl              #0xec1630  ; AllocateClosureStub
    // 0xaa4e3c: mov             x1, x0
    // 0xaa4e40: ldur            x0, [fp, #-8]
    // 0xaa4e44: StoreField: r0->field_f = r1
    //     0xaa4e44: stur            w1, [x0, #0xf]
    // 0xaa4e48: r1 = Instance_HitTestBehavior
    //     0xaa4e48: ldr             x1, [PP, #0x6c40]  ; [pp+0x6c40] Obj!HitTestBehavior@e358a1
    // 0xaa4e4c: StoreField: r0->field_33 = r1
    //     0xaa4e4c: stur            w1, [x0, #0x33]
    // 0xaa4e50: ldur            x1, [fp, #-0x10]
    // 0xaa4e54: StoreField: r0->field_b = r1
    //     0xaa4e54: stur            w1, [x0, #0xb]
    // 0xaa4e58: LeaveFrame
    //     0xaa4e58: mov             SP, fp
    //     0xaa4e5c: ldp             fp, lr, [SP], #0x10
    // 0xaa4e60: ret
    //     0xaa4e60: ret             
  }
  [closure] void <anonymous closure>(dynamic, PointerDownEvent) {
    // ** addr: 0xaa4e64, size: 0x54
    // 0xaa4e64: EnterFrame
    //     0xaa4e64: stp             fp, lr, [SP, #-0x10]!
    //     0xaa4e68: mov             fp, SP
    // 0xaa4e6c: ldr             x0, [fp, #0x18]
    // 0xaa4e70: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa4e70: ldur            w1, [x0, #0x17]
    // 0xaa4e74: DecompressPointer r1
    //     0xaa4e74: add             x1, x1, HEAP, lsl #32
    // 0xaa4e78: CheckStackOverflow
    //     0xaa4e78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa4e7c: cmp             SP, x16
    //     0xaa4e80: b.ls            #0xaa4eb0
    // 0xaa4e84: LoadField: r0 = r1->field_f
    //     0xaa4e84: ldur            w0, [x1, #0xf]
    // 0xaa4e88: DecompressPointer r0
    //     0xaa4e88: add             x0, x0, HEAP, lsl #32
    // 0xaa4e8c: LoadField: r2 = r1->field_13
    //     0xaa4e8c: ldur            w2, [x1, #0x13]
    // 0xaa4e90: DecompressPointer r2
    //     0xaa4e90: add             x2, x2, HEAP, lsl #32
    // 0xaa4e94: mov             x1, x0
    // 0xaa4e98: ldr             x3, [fp, #0x10]
    // 0xaa4e9c: r0 = _startDragging()
    //     0xaa4e9c: bl              #0xaa4eb8  ; [package:flutter/src/widgets/reorderable_list.dart] ReorderableDragStartListener::_startDragging
    // 0xaa4ea0: r0 = Null
    //     0xaa4ea0: mov             x0, NULL
    // 0xaa4ea4: LeaveFrame
    //     0xaa4ea4: mov             SP, fp
    //     0xaa4ea8: ldp             fp, lr, [SP], #0x10
    // 0xaa4eac: ret
    //     0xaa4eac: ret             
    // 0xaa4eb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa4eb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa4eb4: b               #0xaa4e84
  }
  _ _startDragging(/* No info */) {
    // ** addr: 0xaa4eb8, size: 0xe4
    // 0xaa4eb8: EnterFrame
    //     0xaa4eb8: stp             fp, lr, [SP, #-0x10]!
    //     0xaa4ebc: mov             fp, SP
    // 0xaa4ec0: AllocStack(0x28)
    //     0xaa4ec0: sub             SP, SP, #0x28
    // 0xaa4ec4: SetupParameters(ReorderableDragStartListener this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0xaa4ec4: mov             x0, x2
    //     0xaa4ec8: stur            x2, [fp, #-0x10]
    //     0xaa4ecc: mov             x2, x3
    //     0xaa4ed0: stur            x3, [fp, #-0x18]
    //     0xaa4ed4: mov             x3, x1
    //     0xaa4ed8: stur            x1, [fp, #-8]
    // 0xaa4edc: CheckStackOverflow
    //     0xaa4edc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa4ee0: cmp             SP, x16
    //     0xaa4ee4: b.ls            #0xaa4f94
    // 0xaa4ee8: mov             x1, x0
    // 0xaa4eec: r0 = maybeGestureSettingsOf()
    //     0xaa4eec: bl              #0x9a88ec  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::maybeGestureSettingsOf
    // 0xaa4ef0: ldur            x1, [fp, #-0x10]
    // 0xaa4ef4: stur            x0, [fp, #-0x10]
    // 0xaa4ef8: r0 = maybeOf()
    //     0xaa4ef8: bl              #0xaa6c2c  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableList::maybeOf
    // 0xaa4efc: stur            x0, [fp, #-0x28]
    // 0xaa4f00: cmp             w0, NULL
    // 0xaa4f04: b.eq            #0xaa4f84
    // 0xaa4f08: ldur            x1, [fp, #-8]
    // 0xaa4f0c: LoadField: r3 = r1->field_f
    //     0xaa4f0c: ldur            x3, [x1, #0xf]
    // 0xaa4f10: stur            x3, [fp, #-0x20]
    // 0xaa4f14: r2 = LoadClassIdInstr(r1)
    //     0xaa4f14: ldur            x2, [x1, #-1]
    //     0xaa4f18: ubfx            x2, x2, #0xc, #0x14
    // 0xaa4f1c: r17 = 5332
    //     0xaa4f1c: movz            x17, #0x14d4
    // 0xaa4f20: cmp             x2, x17
    // 0xaa4f24: b.ne            #0xaa4f40
    // 0xaa4f28: r0 = ImmediateMultiDragGestureRecognizer()
    //     0xaa4f28: bl              #0xaa6c20  ; AllocateImmediateMultiDragGestureRecognizerStub -> ImmediateMultiDragGestureRecognizer (size=0x20)
    // 0xaa4f2c: mov             x1, x0
    // 0xaa4f30: stur            x0, [fp, #-8]
    // 0xaa4f34: r0 = MultiDragGestureRecognizer()
    //     0xaa4f34: bl              #0xaa6b78  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::MultiDragGestureRecognizer
    // 0xaa4f38: ldur            x5, [fp, #-8]
    // 0xaa4f3c: b               #0xaa4f54
    // 0xaa4f40: r0 = DelayedMultiDragGestureRecognizer()
    //     0xaa4f40: bl              #0xaa6b6c  ; AllocateDelayedMultiDragGestureRecognizerStub -> DelayedMultiDragGestureRecognizer (size=0x24)
    // 0xaa4f44: mov             x1, x0
    // 0xaa4f48: stur            x0, [fp, #-8]
    // 0xaa4f4c: r0 = DelayedMultiDragGestureRecognizer()
    //     0xaa4f4c: bl              #0xaa6b34  ; [package:flutter/src/gestures/multidrag.dart] DelayedMultiDragGestureRecognizer::DelayedMultiDragGestureRecognizer
    // 0xaa4f50: ldur            x5, [fp, #-8]
    // 0xaa4f54: ldur            x0, [fp, #-0x10]
    // 0xaa4f58: StoreField: r5->field_7 = r0
    //     0xaa4f58: stur            w0, [x5, #7]
    //     0xaa4f5c: ldurb           w16, [x5, #-1]
    //     0xaa4f60: ldurb           w17, [x0, #-1]
    //     0xaa4f64: and             x16, x17, x16, lsr #2
    //     0xaa4f68: tst             x16, HEAP, lsr #32
    //     0xaa4f6c: b.eq            #0xaa4f74
    //     0xaa4f70: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xaa4f74: ldur            x1, [fp, #-0x28]
    // 0xaa4f78: ldur            x2, [fp, #-0x18]
    // 0xaa4f7c: ldur            x3, [fp, #-0x20]
    // 0xaa4f80: r0 = startItemDragReorder()
    //     0xaa4f80: bl              #0xaa4f9c  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::startItemDragReorder
    // 0xaa4f84: r0 = Null
    //     0xaa4f84: mov             x0, NULL
    // 0xaa4f88: LeaveFrame
    //     0xaa4f88: mov             SP, fp
    //     0xaa4f8c: ldp             fp, lr, [SP], #0x10
    // 0xaa4f90: ret
    //     0xaa4f90: ret             
    // 0xaa4f94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa4f94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa4f98: b               #0xaa4ee8
  }
}

// class id: 5333, size: 0x1c, field offset: 0x1c
//   const constructor, 
class ReorderableDelayedDragStartListener extends ReorderableDragStartListener {
}
