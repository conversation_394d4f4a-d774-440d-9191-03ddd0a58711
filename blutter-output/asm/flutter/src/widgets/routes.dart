// lib: , url: package:flutter/src/widgets/routes.dart

// class id: 1049171, size: 0x8
class :: {
}

// class id: 2597, size: 0x8, field offset: 0x8
abstract class RouteAware extends Object {
}

// class id: 2598, size: 0x14, field offset: 0x8
class LocalHistoryEntry extends Object {

  _ _notifyRemoved(/* No info */) {
    // ** addr: 0x65693c, size: 0x4c
    // 0x65693c: EnterFrame
    //     0x65693c: stp             fp, lr, [SP, #-0x10]!
    //     0x656940: mov             fp, SP
    // 0x656944: CheckStackOverflow
    //     0x656944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x656948: cmp             SP, x16
    //     0x65694c: b.ls            #0x65697c
    // 0x656950: LoadField: r0 = r1->field_7
    //     0x656950: ldur            w0, [x1, #7]
    // 0x656954: DecompressPointer r0
    //     0x656954: add             x0, x0, HEAP, lsl #32
    // 0x656958: cmp             w0, NULL
    // 0x65695c: b.eq            #0x656984
    // 0x656960: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x656960: ldur            w1, [x0, #0x17]
    // 0x656964: DecompressPointer r1
    //     0x656964: add             x1, x1, HEAP, lsl #32
    // 0x656968: r0 = _handleHistoryEntryRemoved()
    //     0x656968: bl              #0x656988  ; [package:flutter/src/material/drawer.dart] DrawerControllerState::_handleHistoryEntryRemoved
    // 0x65696c: r0 = Null
    //     0x65696c: mov             x0, NULL
    // 0x656970: LeaveFrame
    //     0x656970: mov             SP, fp
    //     0x656974: ldp             fp, lr, [SP], #0x10
    // 0x656978: ret
    //     0x656978: ret             
    // 0x65697c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65697c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x656980: b               #0x656950
    // 0x656984: r0 = NullErrorSharedWithoutFPURegs()
    //     0x656984: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
}

// class id: 2599, size: 0x8, field offset: 0x8
abstract class PredictiveBackRoute extends Object {
}

// class id: 2608, size: 0xc, field offset: 0x8
abstract class PopEntry<X0> extends Object {
}

// class id: 2647, size: 0x24, field offset: 0x24
abstract class LocalHistoryRoute<X0> extends Route<X0> {
}

// class id: 2648, size: 0x28, field offset: 0x24
abstract class OverlayRoute<X0> extends Route<X0> {

  _ didPop(/* No info */) {
    // ** addr: 0x654dac, size: 0x68
    // 0x654dac: EnterFrame
    //     0x654dac: stp             fp, lr, [SP, #-0x10]!
    //     0x654db0: mov             fp, SP
    // 0x654db4: AllocStack(0x8)
    //     0x654db4: sub             SP, SP, #8
    // 0x654db8: SetupParameters(OverlayRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0x654db8: mov             x0, x1
    //     0x654dbc: stur            x1, [fp, #-8]
    // 0x654dc0: CheckStackOverflow
    //     0x654dc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x654dc4: cmp             SP, x16
    //     0x654dc8: b.ls            #0x654e08
    // 0x654dcc: mov             x1, x0
    // 0x654dd0: r0 = didComplete()
    //     0x654dd0: bl              #0x654708  ; [package:flutter/src/widgets/navigator.dart] Route::didComplete
    // 0x654dd4: ldur            x1, [fp, #-8]
    // 0x654dd8: r0 = finishedWhenPopped()
    //     0x654dd8: bl              #0x655014  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::finishedWhenPopped
    // 0x654ddc: tbnz            w0, #4, #0x654df8
    // 0x654de0: ldur            x2, [fp, #-8]
    // 0x654de4: LoadField: r1 = r2->field_f
    //     0x654de4: ldur            w1, [x2, #0xf]
    // 0x654de8: DecompressPointer r1
    //     0x654de8: add             x1, x1, HEAP, lsl #32
    // 0x654dec: cmp             w1, NULL
    // 0x654df0: b.eq            #0x654e10
    // 0x654df4: r0 = finalizeRoute()
    //     0x654df4: bl              #0x654e14  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::finalizeRoute
    // 0x654df8: r0 = true
    //     0x654df8: add             x0, NULL, #0x20  ; true
    // 0x654dfc: LeaveFrame
    //     0x654dfc: mov             SP, fp
    //     0x654e00: ldp             fp, lr, [SP], #0x10
    // 0x654e04: ret
    //     0x654e04: ret             
    // 0x654e08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x654e08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x654e0c: b               #0x654dcc
    // 0x654e10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x654e10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xdaf5a0, size: 0x128
    // 0xdaf5a0: EnterFrame
    //     0xdaf5a0: stp             fp, lr, [SP, #-0x10]!
    //     0xdaf5a4: mov             fp, SP
    // 0xdaf5a8: AllocStack(0x28)
    //     0xdaf5a8: sub             SP, SP, #0x28
    // 0xdaf5ac: SetupParameters(OverlayRoute<X0> this /* r1 => r0, fp-0x28 */)
    //     0xdaf5ac: mov             x0, x1
    //     0xdaf5b0: stur            x1, [fp, #-0x28]
    // 0xdaf5b4: CheckStackOverflow
    //     0xdaf5b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdaf5b8: cmp             SP, x16
    //     0xdaf5bc: b.ls            #0xdaf6b8
    // 0xdaf5c0: LoadField: r2 = r0->field_23
    //     0xdaf5c0: ldur            w2, [x0, #0x23]
    // 0xdaf5c4: DecompressPointer r2
    //     0xdaf5c4: add             x2, x2, HEAP, lsl #32
    // 0xdaf5c8: stur            x2, [fp, #-0x20]
    // 0xdaf5cc: LoadField: r1 = r2->field_b
    //     0xdaf5cc: ldur            w1, [x2, #0xb]
    // 0xdaf5d0: r3 = LoadInt32Instr(r1)
    //     0xdaf5d0: sbfx            x3, x1, #1, #0x1f
    // 0xdaf5d4: stur            x3, [fp, #-0x18]
    // 0xdaf5d8: r1 = 0
    //     0xdaf5d8: movz            x1, #0
    // 0xdaf5dc: r4 = true
    //     0xdaf5dc: add             x4, NULL, #0x20  ; true
    // 0xdaf5e0: CheckStackOverflow
    //     0xdaf5e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdaf5e4: cmp             SP, x16
    //     0xdaf5e8: b.ls            #0xdaf6c0
    // 0xdaf5ec: LoadField: r5 = r2->field_b
    //     0xdaf5ec: ldur            w5, [x2, #0xb]
    // 0xdaf5f0: r6 = LoadInt32Instr(r5)
    //     0xdaf5f0: sbfx            x6, x5, #1, #0x1f
    // 0xdaf5f4: cmp             x3, x6
    // 0xdaf5f8: b.ne            #0xdaf698
    // 0xdaf5fc: cmp             x1, x6
    // 0xdaf600: b.ge            #0xdaf678
    // 0xdaf604: LoadField: r5 = r2->field_f
    //     0xdaf604: ldur            w5, [x2, #0xf]
    // 0xdaf608: DecompressPointer r5
    //     0xdaf608: add             x5, x5, HEAP, lsl #32
    // 0xdaf60c: ArrayLoad: r6 = r5[r1]  ; Unknown_4
    //     0xdaf60c: add             x16, x5, x1, lsl #2
    //     0xdaf610: ldur            w6, [x16, #0xf]
    // 0xdaf614: DecompressPointer r6
    //     0xdaf614: add             x6, x6, HEAP, lsl #32
    // 0xdaf618: stur            x6, [fp, #-0x10]
    // 0xdaf61c: add             x5, x1, #1
    // 0xdaf620: stur            x5, [fp, #-8]
    // 0xdaf624: StoreField: r6->field_23 = r4
    //     0xdaf624: stur            w4, [x6, #0x23]
    // 0xdaf628: ArrayLoad: r1 = r6[0]  ; List_4
    //     0xdaf628: ldur            w1, [x6, #0x17]
    // 0xdaf62c: DecompressPointer r1
    //     0xdaf62c: add             x1, x1, HEAP, lsl #32
    // 0xdaf630: cmp             w1, NULL
    // 0xdaf634: b.eq            #0xdaf648
    // 0xdaf638: LoadField: r7 = r1->field_27
    //     0xdaf638: ldur            w7, [x1, #0x27]
    // 0xdaf63c: DecompressPointer r7
    //     0xdaf63c: add             x7, x7, HEAP, lsl #32
    // 0xdaf640: cmp             w7, NULL
    // 0xdaf644: b.ne            #0xdaf664
    // 0xdaf648: cmp             w1, NULL
    // 0xdaf64c: b.ne            #0xdaf658
    // 0xdaf650: mov             x0, x6
    // 0xdaf654: b               #0xdaf660
    // 0xdaf658: r0 = dispose()
    //     0xdaf658: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0xdaf65c: ldur            x0, [fp, #-0x10]
    // 0xdaf660: ArrayStore: r0[0] = rNULL  ; List_4
    //     0xdaf660: stur            NULL, [x0, #0x17]
    // 0xdaf664: ldur            x1, [fp, #-8]
    // 0xdaf668: ldur            x0, [fp, #-0x28]
    // 0xdaf66c: ldur            x2, [fp, #-0x20]
    // 0xdaf670: ldur            x3, [fp, #-0x18]
    // 0xdaf674: b               #0xdaf5dc
    // 0xdaf678: ldur            x1, [fp, #-0x20]
    // 0xdaf67c: r0 = clear()
    //     0xdaf67c: bl              #0xbd9e80  ; [dart:core] _GrowableList::clear
    // 0xdaf680: ldur            x1, [fp, #-0x28]
    // 0xdaf684: r0 = dispose()
    //     0xdaf684: bl              #0xdaf6c8  ; [package:flutter/src/widgets/navigator.dart] Route::dispose
    // 0xdaf688: r0 = Null
    //     0xdaf688: mov             x0, NULL
    // 0xdaf68c: LeaveFrame
    //     0xdaf68c: mov             SP, fp
    //     0xdaf690: ldp             fp, lr, [SP], #0x10
    // 0xdaf694: ret
    //     0xdaf694: ret             
    // 0xdaf698: mov             x0, x2
    // 0xdaf69c: r0 = ConcurrentModificationError()
    //     0xdaf69c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xdaf6a0: mov             x1, x0
    // 0xdaf6a4: ldur            x0, [fp, #-0x20]
    // 0xdaf6a8: StoreField: r1->field_b = r0
    //     0xdaf6a8: stur            w0, [x1, #0xb]
    // 0xdaf6ac: mov             x0, x1
    // 0xdaf6b0: r0 = Throw()
    //     0xdaf6b0: bl              #0xec04b8  ; ThrowStub
    // 0xdaf6b4: brk             #0
    // 0xdaf6b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdaf6b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdaf6bc: b               #0xdaf5c0
    // 0xdaf6c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdaf6c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdaf6c4: b               #0xdaf5ec
  }
  _ install(/* No info */) {
    // ** addr: 0xdb056c, size: 0x4c
    // 0xdb056c: EnterFrame
    //     0xdb056c: stp             fp, lr, [SP, #-0x10]!
    //     0xdb0570: mov             fp, SP
    // 0xdb0574: AllocStack(0x8)
    //     0xdb0574: sub             SP, SP, #8
    // 0xdb0578: CheckStackOverflow
    //     0xdb0578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb057c: cmp             SP, x16
    //     0xdb0580: b.ls            #0xdb05b0
    // 0xdb0584: LoadField: r0 = r1->field_23
    //     0xdb0584: ldur            w0, [x1, #0x23]
    // 0xdb0588: DecompressPointer r0
    //     0xdb0588: add             x0, x0, HEAP, lsl #32
    // 0xdb058c: stur            x0, [fp, #-8]
    // 0xdb0590: r0 = createOverlayEntries()
    //     0xdb0590: bl              #0xdb05b8  ; [package:flutter/src/widgets/routes.dart] ModalRoute::createOverlayEntries
    // 0xdb0594: ldur            x1, [fp, #-8]
    // 0xdb0598: mov             x2, x0
    // 0xdb059c: r0 = addAll()
    //     0xdb059c: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xdb05a0: r0 = Null
    //     0xdb05a0: mov             x0, NULL
    // 0xdb05a4: LeaveFrame
    //     0xdb05a4: mov             SP, fp
    //     0xdb05a8: ldp             fp, lr, [SP], #0x10
    // 0xdb05ac: ret
    //     0xdb05ac: ret             
    // 0xdb05b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb05b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb05b4: b               #0xdb0584
  }
}

// class id: 2649, size: 0x4c, field offset: 0x28
abstract class TransitionRoute<X0> extends OverlayRoute<X0>
    implements PredictiveBackRoute {

  _ didPopNext(/* No info */) {
    // ** addr: 0x6523f0, size: 0x30
    // 0x6523f0: EnterFrame
    //     0x6523f0: stp             fp, lr, [SP, #-0x10]!
    //     0x6523f4: mov             fp, SP
    // 0x6523f8: CheckStackOverflow
    //     0x6523f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6523fc: cmp             SP, x16
    //     0x652400: b.ls            #0x652418
    // 0x652404: r0 = _updateSecondaryAnimation()
    //     0x652404: bl              #0x652420  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::_updateSecondaryAnimation
    // 0x652408: r0 = Null
    //     0x652408: mov             x0, NULL
    // 0x65240c: LeaveFrame
    //     0x65240c: mov             SP, fp
    //     0x652410: ldp             fp, lr, [SP], #0x10
    // 0x652414: ret
    //     0x652414: ret             
    // 0x652418: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x652418: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65241c: b               #0x652404
  }
  _ _updateSecondaryAnimation(/* No info */) {
    // ** addr: 0x652420, size: 0x538
    // 0x652420: EnterFrame
    //     0x652420: stp             fp, lr, [SP, #-0x10]!
    //     0x652424: mov             fp, SP
    // 0x652428: AllocStack(0x40)
    //     0x652428: sub             SP, SP, #0x40
    // 0x65242c: SetupParameters(TransitionRoute<X0> this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x65242c: mov             x0, x2
    //     0x652430: stur            x2, [fp, #-0x10]
    //     0x652434: mov             x2, x1
    //     0x652438: stur            x1, [fp, #-8]
    // 0x65243c: CheckStackOverflow
    //     0x65243c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x652440: cmp             SP, x16
    //     0x652444: b.ls            #0x652930
    // 0x652448: r1 = 5
    //     0x652448: movz            x1, #0x5
    // 0x65244c: r0 = AllocateContext()
    //     0x65244c: bl              #0xec126c  ; AllocateContextStub
    // 0x652450: mov             x4, x0
    // 0x652454: ldur            x3, [fp, #-8]
    // 0x652458: stur            x4, [fp, #-0x30]
    // 0x65245c: StoreField: r4->field_f = r3
    //     0x65245c: stur            w3, [x4, #0xf]
    // 0x652460: ldur            x5, [fp, #-0x10]
    // 0x652464: StoreField: r4->field_13 = r5
    //     0x652464: stur            w5, [x4, #0x13]
    // 0x652468: LoadField: r6 = r3->field_47
    //     0x652468: ldur            w6, [x3, #0x47]
    // 0x65246c: DecompressPointer r6
    //     0x65246c: add             x6, x6, HEAP, lsl #32
    // 0x652470: stur            x6, [fp, #-0x28]
    // 0x652474: StoreField: r3->field_47 = rNULL
    //     0x652474: stur            NULL, [x3, #0x47]
    // 0x652478: r7 = LoadClassIdInstr(r5)
    //     0x652478: ldur            x7, [x5, #-1]
    //     0x65247c: ubfx            x7, x7, #0xc, #0x14
    // 0x652480: stur            x7, [fp, #-0x20]
    // 0x652484: sub             x16, x7, #0xa5d
    // 0x652488: cmp             x16, #8
    // 0x65248c: b.hi            #0x6528f0
    // 0x652490: r0 = LoadClassIdInstr(r3)
    //     0x652490: ldur            x0, [x3, #-1]
    //     0x652494: ubfx            x0, x0, #0xc, #0x14
    // 0x652498: sub             x16, x0, #0xa5d
    // 0x65249c: cmp             x16, #1
    // 0x6524a0: b.hi            #0x6524ac
    // 0x6524a4: mov             x3, x4
    // 0x6524a8: b               #0x652624
    // 0x6524ac: cmp             x0, #0xa62
    // 0x6524b0: b.ne            #0x6524d0
    // 0x6524b4: cmp             x7, #0xa62
    // 0x6524b8: b.ne            #0x6528f0
    // 0x6524bc: LoadField: r0 = r5->field_8f
    //     0x6524bc: ldur            w0, [x5, #0x8f]
    // 0x6524c0: DecompressPointer r0
    //     0x6524c0: add             x0, x0, HEAP, lsl #32
    // 0x6524c4: tbz             w0, #4, #0x6528f0
    // 0x6524c8: mov             x3, x4
    // 0x6524cc: b               #0x652624
    // 0x6524d0: LoadField: r8 = r3->field_7
    //     0x6524d0: ldur            w8, [x3, #7]
    // 0x6524d4: DecompressPointer r8
    //     0x6524d4: add             x8, x8, HEAP, lsl #32
    // 0x6524d8: mov             x0, x5
    // 0x6524dc: mov             x2, x8
    // 0x6524e0: stur            x8, [fp, #-0x18]
    // 0x6524e4: r1 = Null
    //     0x6524e4: mov             x1, NULL
    // 0x6524e8: cmp             w0, NULL
    // 0x6524ec: b.eq            #0x652538
    // 0x6524f0: branchIfSmi(r0, 0x652538)
    //     0x6524f0: tbz             w0, #0, #0x652538
    // 0x6524f4: r3 = SubtypeTestCache
    //     0x6524f4: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a7b8] SubtypeTestCache
    //     0x6524f8: ldr             x3, [x3, #0x7b8]
    // 0x6524fc: r30 = Subtype3TestCacheStub
    //     0x6524fc: ldr             lr, [PP, #0x28]  ; [pp+0x28] Stub: Subtype3TestCache (0x5f2c84)
    // 0x652500: LoadField: r30 = r30->field_7
    //     0x652500: ldur            lr, [lr, #7]
    // 0x652504: blr             lr
    // 0x652508: cmp             w7, NULL
    // 0x65250c: b.eq            #0x652518
    // 0x652510: tbnz            w7, #4, #0x652538
    // 0x652514: b               #0x652540
    // 0x652518: r8 = PageRoute<X0>
    //     0x652518: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1a7c0] Type: PageRoute<X0>
    //     0x65251c: ldr             x8, [x8, #0x7c0]
    // 0x652520: r3 = SubtypeTestCache
    //     0x652520: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a7c8] SubtypeTestCache
    //     0x652524: ldr             x3, [x3, #0x7c8]
    // 0x652528: r30 = InstanceOfStub
    //     0x652528: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x65252c: LoadField: r30 = r30->field_7
    //     0x65252c: ldur            lr, [lr, #7]
    // 0x652530: blr             lr
    // 0x652534: b               #0x652544
    // 0x652538: r0 = false
    //     0x652538: add             x0, NULL, #0x30  ; false
    // 0x65253c: b               #0x652544
    // 0x652540: r0 = true
    //     0x652540: add             x0, NULL, #0x20  ; true
    // 0x652544: tbz             w0, #4, #0x652554
    // 0x652548: ldur            x3, [fp, #-0x10]
    // 0x65254c: r4 = true
    //     0x65254c: add             x4, NULL, #0x20  ; true
    // 0x652550: b               #0x652568
    // 0x652554: ldur            x3, [fp, #-0x10]
    // 0x652558: LoadField: r0 = r3->field_8f
    //     0x652558: ldur            w0, [x3, #0x8f]
    // 0x65255c: DecompressPointer r0
    //     0x65255c: add             x0, x0, HEAP, lsl #32
    // 0x652560: eor             x1, x0, #0x10
    // 0x652564: mov             x4, x1
    // 0x652568: mov             x0, x3
    // 0x65256c: ldur            x2, [fp, #-0x18]
    // 0x652570: stur            x4, [fp, #-0x38]
    // 0x652574: r1 = Null
    //     0x652574: mov             x1, NULL
    // 0x652578: cmp             w0, NULL
    // 0x65257c: b.eq            #0x6525c8
    // 0x652580: branchIfSmi(r0, 0x6525c8)
    //     0x652580: tbz             w0, #0, #0x6525c8
    // 0x652584: r3 = SubtypeTestCache
    //     0x652584: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a7d0] SubtypeTestCache
    //     0x652588: ldr             x3, [x3, #0x7d0]
    // 0x65258c: r30 = Subtype3TestCacheStub
    //     0x65258c: ldr             lr, [PP, #0x28]  ; [pp+0x28] Stub: Subtype3TestCache (0x5f2c84)
    // 0x652590: LoadField: r30 = r30->field_7
    //     0x652590: ldur            lr, [lr, #7]
    // 0x652594: blr             lr
    // 0x652598: cmp             w7, NULL
    // 0x65259c: b.eq            #0x6525a8
    // 0x6525a0: tbnz            w7, #4, #0x6525c8
    // 0x6525a4: b               #0x6525d0
    // 0x6525a8: r8 = ModalRoute<X0>
    //     0x6525a8: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1a7d8] Type: ModalRoute<X0>
    //     0x6525ac: ldr             x8, [x8, #0x7d8]
    // 0x6525b0: r3 = SubtypeTestCache
    //     0x6525b0: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a7e0] SubtypeTestCache
    //     0x6525b4: ldr             x3, [x3, #0x7e0]
    // 0x6525b8: r30 = InstanceOfStub
    //     0x6525b8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x6525bc: LoadField: r30 = r30->field_7
    //     0x6525bc: ldur            lr, [lr, #7]
    // 0x6525c0: blr             lr
    // 0x6525c4: b               #0x6525d4
    // 0x6525c8: r0 = false
    //     0x6525c8: add             x0, NULL, #0x30  ; false
    // 0x6525cc: b               #0x6525d4
    // 0x6525d0: r0 = true
    //     0x6525d0: add             x0, NULL, #0x20  ; true
    // 0x6525d4: tbnz            w0, #4, #0x652604
    // 0x6525d8: ldur            x1, [fp, #-0x10]
    // 0x6525dc: r0 = LoadClassIdInstr(r1)
    //     0x6525dc: ldur            x0, [x1, #-1]
    //     0x6525e0: ubfx            x0, x0, #0xc, #0x14
    // 0x6525e4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x6525e4: sub             lr, x0, #1, lsl #12
    //     0x6525e8: ldr             lr, [x21, lr, lsl #3]
    //     0x6525ec: blr             lr
    // 0x6525f0: cmp             w0, NULL
    // 0x6525f4: r16 = true
    //     0x6525f4: add             x16, NULL, #0x20  ; true
    // 0x6525f8: r17 = false
    //     0x6525f8: add             x17, NULL, #0x30  ; false
    // 0x6525fc: csel            x1, x16, x17, ne
    // 0x652600: b               #0x652608
    // 0x652604: r1 = false
    //     0x652604: add             x1, NULL, #0x30  ; false
    // 0x652608: ldur            x0, [fp, #-0x38]
    // 0x65260c: tbnz            w0, #4, #0x6528f0
    // 0x652610: ldur            x0, [fp, #-0x20]
    // 0x652614: cmp             x0, #0xa65
    // 0x652618: b.eq            #0x652620
    // 0x65261c: tbnz            w1, #4, #0x6528f0
    // 0x652620: ldur            x3, [fp, #-0x30]
    // 0x652624: LoadField: r1 = r3->field_13
    //     0x652624: ldur            w1, [x3, #0x13]
    // 0x652628: DecompressPointer r1
    //     0x652628: add             x1, x1, HEAP, lsl #32
    // 0x65262c: r0 = LoadClassIdInstr(r1)
    //     0x65262c: ldur            x0, [x1, #-1]
    //     0x652630: ubfx            x0, x0, #0xc, #0x14
    // 0x652634: ldur            x2, [fp, #-8]
    // 0x652638: r0 = GDT[cid_x0 + 0x1663]()
    //     0x652638: movz            x17, #0x1663
    //     0x65263c: add             lr, x0, x17
    //     0x652640: ldr             lr, [x21, lr, lsl #3]
    //     0x652644: blr             lr
    // 0x652648: tbnz            w0, #4, #0x6528f0
    // 0x65264c: ldur            x2, [fp, #-8]
    // 0x652650: LoadField: r0 = r2->field_3b
    //     0x652650: ldur            w0, [x2, #0x3b]
    // 0x652654: DecompressPointer r0
    //     0x652654: add             x0, x0, HEAP, lsl #32
    // 0x652658: LoadField: r1 = r0->field_23
    //     0x652658: ldur            w1, [x0, #0x23]
    // 0x65265c: DecompressPointer r1
    //     0x65265c: add             x1, x1, HEAP, lsl #32
    // 0x652660: cmp             w1, NULL
    // 0x652664: b.eq            #0x6528b0
    // 0x652668: r0 = LoadClassIdInstr(r1)
    //     0x652668: ldur            x0, [x1, #-1]
    //     0x65266c: ubfx            x0, x0, #0xc, #0x14
    // 0x652670: r17 = 5499
    //     0x652670: movz            x17, #0x157b
    // 0x652674: cmp             x0, x17
    // 0x652678: b.ne            #0x65268c
    // 0x65267c: LoadField: r0 = r1->field_13
    //     0x65267c: ldur            w0, [x1, #0x13]
    // 0x652680: DecompressPointer r0
    //     0x652680: add             x0, x0, HEAP, lsl #32
    // 0x652684: mov             x4, x0
    // 0x652688: b               #0x652690
    // 0x65268c: mov             x4, x1
    // 0x652690: ldur            x3, [fp, #-0x30]
    // 0x652694: stur            x4, [fp, #-0x18]
    // 0x652698: cmp             w4, NULL
    // 0x65269c: b.eq            #0x652938
    // 0x6526a0: LoadField: r0 = r3->field_13
    //     0x6526a0: ldur            w0, [x3, #0x13]
    // 0x6526a4: DecompressPointer r0
    //     0x6526a4: add             x0, x0, HEAP, lsl #32
    // 0x6526a8: cmp             w0, NULL
    // 0x6526ac: b.eq            #0x65293c
    // 0x6526b0: LoadField: r5 = r0->field_33
    //     0x6526b0: ldur            w5, [x0, #0x33]
    // 0x6526b4: DecompressPointer r5
    //     0x6526b4: add             x5, x5, HEAP, lsl #32
    // 0x6526b8: stur            x5, [fp, #-0x10]
    // 0x6526bc: cmp             w5, NULL
    // 0x6526c0: b.eq            #0x652940
    // 0x6526c4: mov             x0, x5
    // 0x6526c8: ArrayStore: r3[0] = r0  ; List_4
    //     0x6526c8: stur            w0, [x3, #0x17]
    //     0x6526cc: ldurb           w16, [x3, #-1]
    //     0x6526d0: ldurb           w17, [x0, #-1]
    //     0x6526d4: and             x16, x17, x16, lsr #2
    //     0x6526d8: tst             x16, HEAP, lsr #32
    //     0x6526dc: b.eq            #0x6526e4
    //     0x6526e0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x6526e4: r0 = LoadClassIdInstr(r4)
    //     0x6526e4: ldur            x0, [x4, #-1]
    //     0x6526e8: ubfx            x0, x0, #0xc, #0x14
    // 0x6526ec: mov             x1, x4
    // 0x6526f0: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6526f0: movz            x17, #0x276f
    //     0x6526f4: movk            x17, #0x1, lsl #16
    //     0x6526f8: add             lr, x0, x17
    //     0x6526fc: ldr             lr, [x21, lr, lsl #3]
    //     0x652700: blr             lr
    // 0x652704: mov             x1, x0
    // 0x652708: ldur            x0, [fp, #-0x10]
    // 0x65270c: LoadField: r2 = r0->field_37
    //     0x65270c: ldur            w2, [x0, #0x37]
    // 0x652710: DecompressPointer r2
    //     0x652710: add             x2, x2, HEAP, lsl #32
    // 0x652714: r16 = Sentinel
    //     0x652714: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x652718: cmp             w2, w16
    // 0x65271c: b.eq            #0x652944
    // 0x652720: LoadField: d0 = r1->field_7
    //     0x652720: ldur            d0, [x1, #7]
    // 0x652724: LoadField: d1 = r2->field_7
    //     0x652724: ldur            d1, [x2, #7]
    // 0x652728: fcmp            d0, d1
    // 0x65272c: b.ne            #0x652738
    // 0x652730: ldur            x1, [fp, #-0x30]
    // 0x652734: b               #0x652878
    // 0x652738: LoadField: r1 = r0->field_2f
    //     0x652738: ldur            w1, [x0, #0x2f]
    // 0x65273c: DecompressPointer r1
    //     0x65273c: add             x1, x1, HEAP, lsl #32
    // 0x652740: cmp             w1, NULL
    // 0x652744: b.eq            #0x652874
    // 0x652748: LoadField: r2 = r1->field_7
    //     0x652748: ldur            w2, [x1, #7]
    // 0x65274c: DecompressPointer r2
    //     0x65274c: add             x2, x2, HEAP, lsl #32
    // 0x652750: cmp             w2, NULL
    // 0x652754: b.ne            #0x652760
    // 0x652758: ldur            x1, [fp, #-0x30]
    // 0x65275c: b               #0x652878
    // 0x652760: ldur            x3, [fp, #-8]
    // 0x652764: ldur            x4, [fp, #-0x30]
    // 0x652768: StoreField: r4->field_1b = rNULL
    //     0x652768: stur            NULL, [x4, #0x1b]
    // 0x65276c: mov             x2, x4
    // 0x652770: r1 = Function 'jumpOnAnimationEnd':.
    //     0x652770: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a7f0] AnonymousClosure: (0x6544b4), in [package:flutter/src/widgets/routes.dart] TransitionRoute::_updateSecondaryAnimation (0x652420)
    //     0x652774: ldr             x1, [x1, #0x7f0]
    // 0x652778: r0 = AllocateClosure()
    //     0x652778: bl              #0xec1630  ; AllocateClosureStub
    // 0x65277c: mov             x4, x0
    // 0x652780: ldur            x3, [fp, #-0x30]
    // 0x652784: stur            x4, [fp, #-0x38]
    // 0x652788: StoreField: r3->field_1f = r0
    //     0x652788: stur            w0, [x3, #0x1f]
    //     0x65278c: ldurb           w16, [x3, #-1]
    //     0x652790: ldurb           w17, [x0, #-1]
    //     0x652794: and             x16, x17, x16, lsr #2
    //     0x652798: tst             x16, HEAP, lsr #32
    //     0x65279c: b.eq            #0x6527a4
    //     0x6527a0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x6527a4: mov             x2, x3
    // 0x6527a8: r1 = Function '<anonymous closure>':.
    //     0x6527a8: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a7f8] AnonymousClosure: (0x654448), in [package:flutter/src/widgets/routes.dart] TransitionRoute::_updateSecondaryAnimation (0x652420)
    //     0x6527ac: ldr             x1, [x1, #0x7f8]
    // 0x6527b0: r0 = AllocateClosure()
    //     0x6527b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x6527b4: ldur            x3, [fp, #-8]
    // 0x6527b8: StoreField: r3->field_47 = r0
    //     0x6527b8: stur            w0, [x3, #0x47]
    //     0x6527bc: ldurb           w16, [x3, #-1]
    //     0x6527c0: ldurb           w17, [x0, #-1]
    //     0x6527c4: and             x16, x17, x16, lsr #2
    //     0x6527c8: tst             x16, HEAP, lsr #32
    //     0x6527cc: b.eq            #0x6527d4
    //     0x6527d0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x6527d4: ldur            x1, [fp, #-0x10]
    // 0x6527d8: ldur            x2, [fp, #-0x38]
    // 0x6527dc: r0 = addStatusListener()
    //     0x6527dc: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0x6527e0: r1 = <double>
    //     0x6527e0: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x6527e4: r0 = TrainHoppingAnimation()
    //     0x6527e4: bl              #0x654368  ; AllocateTrainHoppingAnimationStub -> TrainHoppingAnimation (size=0x2c)
    // 0x6527e8: ldur            x2, [fp, #-0x30]
    // 0x6527ec: r1 = Function '<anonymous closure>':.
    //     0x6527ec: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a800] AnonymousClosure: (0x654374), in [package:flutter/src/widgets/routes.dart] TransitionRoute::_updateSecondaryAnimation (0x652420)
    //     0x6527f0: ldr             x1, [x1, #0x800]
    // 0x6527f4: stur            x0, [fp, #-0x38]
    // 0x6527f8: r0 = AllocateClosure()
    //     0x6527f8: bl              #0xec1630  ; AllocateClosureStub
    // 0x6527fc: str             x0, [SP]
    // 0x652800: ldur            x1, [fp, #-0x38]
    // 0x652804: ldur            x2, [fp, #-0x18]
    // 0x652808: ldur            x3, [fp, #-0x10]
    // 0x65280c: r4 = const [0, 0x4, 0x1, 0x3, onSwitchedTrain, 0x3, null]
    //     0x65280c: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a808] List(7) [0, 0x4, 0x1, 0x3, "onSwitchedTrain", 0x3, Null]
    //     0x652810: ldr             x4, [x4, #0x808]
    // 0x652814: r0 = TrainHoppingAnimation()
    //     0x652814: bl              #0x653edc  ; [package:flutter/src/animation/animations.dart] TrainHoppingAnimation::TrainHoppingAnimation
    // 0x652818: ldur            x0, [fp, #-0x38]
    // 0x65281c: ldur            x1, [fp, #-0x30]
    // 0x652820: StoreField: r1->field_1b = r0
    //     0x652820: stur            w0, [x1, #0x1b]
    //     0x652824: ldurb           w16, [x1, #-1]
    //     0x652828: ldurb           w17, [x0, #-1]
    //     0x65282c: and             x16, x17, x16, lsr #2
    //     0x652830: tst             x16, HEAP, lsr #32
    //     0x652834: b.eq            #0x65283c
    //     0x652838: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x65283c: LoadField: r0 = r1->field_13
    //     0x65283c: ldur            w0, [x1, #0x13]
    // 0x652840: DecompressPointer r0
    //     0x652840: add             x0, x0, HEAP, lsl #32
    // 0x652844: cmp             w0, NULL
    // 0x652848: b.eq            #0x65294c
    // 0x65284c: LoadField: r1 = r0->field_27
    //     0x65284c: ldur            w1, [x0, #0x27]
    // 0x652850: DecompressPointer r1
    //     0x652850: add             x1, x1, HEAP, lsl #32
    // 0x652854: LoadField: r0 = r1->field_b
    //     0x652854: ldur            w0, [x1, #0xb]
    // 0x652858: DecompressPointer r0
    //     0x652858: add             x0, x0, HEAP, lsl #32
    // 0x65285c: str             x0, [SP]
    // 0x652860: ldur            x1, [fp, #-8]
    // 0x652864: ldur            x2, [fp, #-0x38]
    // 0x652868: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x652868: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x65286c: r0 = _setSecondaryAnimation()
    //     0x65286c: bl              #0x652958  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::_setSecondaryAnimation
    // 0x652870: b               #0x652904
    // 0x652874: ldur            x1, [fp, #-0x30]
    // 0x652878: LoadField: r0 = r1->field_13
    //     0x652878: ldur            w0, [x1, #0x13]
    // 0x65287c: DecompressPointer r0
    //     0x65287c: add             x0, x0, HEAP, lsl #32
    // 0x652880: cmp             w0, NULL
    // 0x652884: b.eq            #0x652950
    // 0x652888: LoadField: r1 = r0->field_27
    //     0x652888: ldur            w1, [x0, #0x27]
    // 0x65288c: DecompressPointer r1
    //     0x65288c: add             x1, x1, HEAP, lsl #32
    // 0x652890: LoadField: r0 = r1->field_b
    //     0x652890: ldur            w0, [x1, #0xb]
    // 0x652894: DecompressPointer r0
    //     0x652894: add             x0, x0, HEAP, lsl #32
    // 0x652898: str             x0, [SP]
    // 0x65289c: ldur            x1, [fp, #-8]
    // 0x6528a0: ldur            x2, [fp, #-0x10]
    // 0x6528a4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x6528a4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x6528a8: r0 = _setSecondaryAnimation()
    //     0x6528a8: bl              #0x652958  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::_setSecondaryAnimation
    // 0x6528ac: b               #0x652904
    // 0x6528b0: ldur            x1, [fp, #-0x30]
    // 0x6528b4: LoadField: r0 = r1->field_13
    //     0x6528b4: ldur            w0, [x1, #0x13]
    // 0x6528b8: DecompressPointer r0
    //     0x6528b8: add             x0, x0, HEAP, lsl #32
    // 0x6528bc: cmp             w0, NULL
    // 0x6528c0: b.eq            #0x652954
    // 0x6528c4: LoadField: r2 = r0->field_33
    //     0x6528c4: ldur            w2, [x0, #0x33]
    // 0x6528c8: DecompressPointer r2
    //     0x6528c8: add             x2, x2, HEAP, lsl #32
    // 0x6528cc: LoadField: r1 = r0->field_27
    //     0x6528cc: ldur            w1, [x0, #0x27]
    // 0x6528d0: DecompressPointer r1
    //     0x6528d0: add             x1, x1, HEAP, lsl #32
    // 0x6528d4: LoadField: r0 = r1->field_b
    //     0x6528d4: ldur            w0, [x1, #0xb]
    // 0x6528d8: DecompressPointer r0
    //     0x6528d8: add             x0, x0, HEAP, lsl #32
    // 0x6528dc: str             x0, [SP]
    // 0x6528e0: ldur            x1, [fp, #-8]
    // 0x6528e4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x6528e4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x6528e8: r0 = _setSecondaryAnimation()
    //     0x6528e8: bl              #0x652958  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::_setSecondaryAnimation
    // 0x6528ec: b               #0x652904
    // 0x6528f0: ldur            x1, [fp, #-8]
    // 0x6528f4: r2 = Instance__AlwaysDismissedAnimation
    //     0x6528f4: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a810] Obj!_AlwaysDismissedAnimation@e25951
    //     0x6528f8: ldr             x2, [x2, #0x810]
    // 0x6528fc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6528fc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x652900: r0 = _setSecondaryAnimation()
    //     0x652900: bl              #0x652958  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::_setSecondaryAnimation
    // 0x652904: ldur            x0, [fp, #-0x28]
    // 0x652908: cmp             w0, NULL
    // 0x65290c: b.eq            #0x652920
    // 0x652910: str             x0, [SP]
    // 0x652914: ClosureCall
    //     0x652914: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x652918: ldur            x2, [x0, #0x1f]
    //     0x65291c: blr             x2
    // 0x652920: r0 = Null
    //     0x652920: mov             x0, NULL
    // 0x652924: LeaveFrame
    //     0x652924: mov             SP, fp
    //     0x652928: ldp             fp, lr, [SP], #0x10
    // 0x65292c: ret
    //     0x65292c: ret             
    // 0x652930: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x652930: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x652934: b               #0x652448
    // 0x652938: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x652938: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x65293c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x65293c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x652940: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x652940: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x652944: r9 = _value
    //     0x652944: ldr             x9, [PP, #0x4ea8]  ; [pp+0x4ea8] Field <AnimationController._value@441066280>: late (offset: 0x38)
    // 0x652948: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x652948: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x65294c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x65294c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x652950: r0 = NullErrorSharedWithoutFPURegs()
    //     0x652950: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x652954: r0 = NullErrorSharedWithoutFPURegs()
    //     0x652954: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _setSecondaryAnimation(/* No info */) {
    // ** addr: 0x652958, size: 0xbc
    // 0x652958: EnterFrame
    //     0x652958: stp             fp, lr, [SP, #-0x10]!
    //     0x65295c: mov             fp, SP
    // 0x652960: AllocStack(0x38)
    //     0x652960: sub             SP, SP, #0x38
    // 0x652964: SetupParameters(TransitionRoute<X0> this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, [dynamic _ = Null /* r0, fp-0x8 */])
    //     0x652964: stur            x1, [fp, #-0x10]
    //     0x652968: stur            x2, [fp, #-0x18]
    //     0x65296c: ldur            w0, [x4, #0x13]
    //     0x652970: sub             x3, x0, #4
    //     0x652974: cmp             w3, #2
    //     0x652978: b.lt            #0x652988
    //     0x65297c: add             x0, fp, w3, sxtw #2
    //     0x652980: ldr             x0, [x0, #8]
    //     0x652984: b               #0x65298c
    //     0x652988: mov             x0, NULL
    //     0x65298c: stur            x0, [fp, #-8]
    // 0x652990: CheckStackOverflow
    //     0x652990: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x652994: cmp             SP, x16
    //     0x652998: b.ls            #0x652a0c
    // 0x65299c: r1 = 2
    //     0x65299c: movz            x1, #0x2
    // 0x6529a0: r0 = AllocateContext()
    //     0x6529a0: bl              #0xec126c  ; AllocateContextStub
    // 0x6529a4: mov             x3, x0
    // 0x6529a8: ldur            x0, [fp, #-0x10]
    // 0x6529ac: stur            x3, [fp, #-0x20]
    // 0x6529b0: StoreField: r3->field_f = r0
    //     0x6529b0: stur            w0, [x3, #0xf]
    // 0x6529b4: ldur            x2, [fp, #-0x18]
    // 0x6529b8: StoreField: r3->field_13 = r2
    //     0x6529b8: stur            w2, [x3, #0x13]
    // 0x6529bc: LoadField: r1 = r0->field_3b
    //     0x6529bc: ldur            w1, [x0, #0x3b]
    // 0x6529c0: DecompressPointer r1
    //     0x6529c0: add             x1, x1, HEAP, lsl #32
    // 0x6529c4: r0 = parent=()
    //     0x6529c4: bl              #0x652a14  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::parent=
    // 0x6529c8: ldur            x0, [fp, #-8]
    // 0x6529cc: cmp             w0, NULL
    // 0x6529d0: b.eq            #0x6529fc
    // 0x6529d4: ldur            x2, [fp, #-0x20]
    // 0x6529d8: r1 = Function '<anonymous closure>':.
    //     0x6529d8: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a848] AnonymousClosure: (0x653404), in [package:flutter/src/widgets/routes.dart] TransitionRoute::_setSecondaryAnimation (0x652958)
    //     0x6529dc: ldr             x1, [x1, #0x848]
    // 0x6529e0: r0 = AllocateClosure()
    //     0x6529e0: bl              #0xec1630  ; AllocateClosureStub
    // 0x6529e4: r16 = <Null?>
    //     0x6529e4: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0x6529e8: ldur            lr, [fp, #-8]
    // 0x6529ec: stp             lr, x16, [SP, #8]
    // 0x6529f0: str             x0, [SP]
    // 0x6529f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6529f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6529f8: r0 = then()
    //     0x6529f8: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x6529fc: r0 = Null
    //     0x6529fc: mov             x0, NULL
    // 0x652a00: LeaveFrame
    //     0x652a00: mov             SP, fp
    //     0x652a04: ldp             fp, lr, [SP], #0x10
    // 0x652a08: ret
    //     0x652a08: ret             
    // 0x652a0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x652a0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x652a10: b               #0x65299c
  }
  [closure] Null <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x653404, size: 0xd4
    // 0x653404: EnterFrame
    //     0x653404: stp             fp, lr, [SP, #-0x10]!
    //     0x653408: mov             fp, SP
    // 0x65340c: AllocStack(0x18)
    //     0x65340c: sub             SP, SP, #0x18
    // 0x653410: SetupParameters()
    //     0x653410: ldr             x0, [fp, #0x18]
    //     0x653414: ldur            w1, [x0, #0x17]
    //     0x653418: add             x1, x1, HEAP, lsl #32
    //     0x65341c: stur            x1, [fp, #-8]
    // 0x653420: CheckStackOverflow
    //     0x653420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x653424: cmp             SP, x16
    //     0x653428: b.ls            #0x6534cc
    // 0x65342c: LoadField: r0 = r1->field_f
    //     0x65342c: ldur            w0, [x1, #0xf]
    // 0x653430: DecompressPointer r0
    //     0x653430: add             x0, x0, HEAP, lsl #32
    // 0x653434: LoadField: r2 = r0->field_3b
    //     0x653434: ldur            w2, [x0, #0x3b]
    // 0x653438: DecompressPointer r2
    //     0x653438: add             x2, x2, HEAP, lsl #32
    // 0x65343c: LoadField: r0 = r2->field_23
    //     0x65343c: ldur            w0, [x2, #0x23]
    // 0x653440: DecompressPointer r0
    //     0x653440: add             x0, x0, HEAP, lsl #32
    // 0x653444: LoadField: r2 = r1->field_13
    //     0x653444: ldur            w2, [x1, #0x13]
    // 0x653448: DecompressPointer r2
    //     0x653448: add             x2, x2, HEAP, lsl #32
    // 0x65344c: r3 = LoadClassIdInstr(r0)
    //     0x65344c: ldur            x3, [x0, #-1]
    //     0x653450: ubfx            x3, x3, #0xc, #0x14
    // 0x653454: stp             x2, x0, [SP]
    // 0x653458: mov             x0, x3
    // 0x65345c: mov             lr, x0
    // 0x653460: ldr             lr, [x21, lr, lsl #3]
    // 0x653464: blr             lr
    // 0x653468: tbnz            w0, #4, #0x6534bc
    // 0x65346c: ldur            x0, [fp, #-8]
    // 0x653470: LoadField: r1 = r0->field_f
    //     0x653470: ldur            w1, [x0, #0xf]
    // 0x653474: DecompressPointer r1
    //     0x653474: add             x1, x1, HEAP, lsl #32
    // 0x653478: LoadField: r2 = r1->field_3b
    //     0x653478: ldur            w2, [x1, #0x3b]
    // 0x65347c: DecompressPointer r2
    //     0x65347c: add             x2, x2, HEAP, lsl #32
    // 0x653480: mov             x1, x2
    // 0x653484: r2 = Instance__AlwaysDismissedAnimation
    //     0x653484: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a810] Obj!_AlwaysDismissedAnimation@e25951
    //     0x653488: ldr             x2, [x2, #0x810]
    // 0x65348c: r0 = parent=()
    //     0x65348c: bl              #0x652a14  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::parent=
    // 0x653490: ldur            x0, [fp, #-8]
    // 0x653494: LoadField: r1 = r0->field_13
    //     0x653494: ldur            w1, [x0, #0x13]
    // 0x653498: DecompressPointer r1
    //     0x653498: add             x1, x1, HEAP, lsl #32
    // 0x65349c: r0 = LoadClassIdInstr(r1)
    //     0x65349c: ldur            x0, [x1, #-1]
    //     0x6534a0: ubfx            x0, x0, #0xc, #0x14
    // 0x6534a4: r17 = 5499
    //     0x6534a4: movz            x17, #0x157b
    // 0x6534a8: cmp             x0, x17
    // 0x6534ac: b.ne            #0x6534bc
    // 0x6534b0: cmp             w1, NULL
    // 0x6534b4: b.eq            #0x6534d4
    // 0x6534b8: r0 = dispose()
    //     0x6534b8: bl              #0x6534d8  ; [package:flutter/src/animation/animations.dart] TrainHoppingAnimation::dispose
    // 0x6534bc: r0 = Null
    //     0x6534bc: mov             x0, NULL
    // 0x6534c0: LeaveFrame
    //     0x6534c0: mov             SP, fp
    //     0x6534c4: ldp             fp, lr, [SP], #0x10
    // 0x6534c8: ret
    //     0x6534c8: ret             
    // 0x6534cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6534cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6534d0: b               #0x65342c
    // 0x6534d4: r0 = NullErrorSharedWithoutFPURegs()
    //     0x6534d4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x654374, size: 0xd4
    // 0x654374: EnterFrame
    //     0x654374: stp             fp, lr, [SP, #-0x10]!
    //     0x654378: mov             fp, SP
    // 0x65437c: AllocStack(0x10)
    //     0x65437c: sub             SP, SP, #0x10
    // 0x654380: SetupParameters()
    //     0x654380: ldr             x0, [fp, #0x10]
    //     0x654384: ldur            w3, [x0, #0x17]
    //     0x654388: add             x3, x3, HEAP, lsl #32
    //     0x65438c: stur            x3, [fp, #-8]
    // 0x654390: CheckStackOverflow
    //     0x654390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x654394: cmp             SP, x16
    //     0x654398: b.ls            #0x654438
    // 0x65439c: LoadField: r1 = r3->field_f
    //     0x65439c: ldur            w1, [x3, #0xf]
    // 0x6543a0: DecompressPointer r1
    //     0x6543a0: add             x1, x1, HEAP, lsl #32
    // 0x6543a4: LoadField: r0 = r3->field_1b
    //     0x6543a4: ldur            w0, [x3, #0x1b]
    // 0x6543a8: DecompressPointer r0
    //     0x6543a8: add             x0, x0, HEAP, lsl #32
    // 0x6543ac: cmp             w0, NULL
    // 0x6543b0: b.eq            #0x654440
    // 0x6543b4: LoadField: r2 = r0->field_13
    //     0x6543b4: ldur            w2, [x0, #0x13]
    // 0x6543b8: DecompressPointer r2
    //     0x6543b8: add             x2, x2, HEAP, lsl #32
    // 0x6543bc: LoadField: r0 = r3->field_13
    //     0x6543bc: ldur            w0, [x3, #0x13]
    // 0x6543c0: DecompressPointer r0
    //     0x6543c0: add             x0, x0, HEAP, lsl #32
    // 0x6543c4: cmp             w0, NULL
    // 0x6543c8: b.eq            #0x654444
    // 0x6543cc: LoadField: r4 = r0->field_27
    //     0x6543cc: ldur            w4, [x0, #0x27]
    // 0x6543d0: DecompressPointer r4
    //     0x6543d0: add             x4, x4, HEAP, lsl #32
    // 0x6543d4: LoadField: r0 = r4->field_b
    //     0x6543d4: ldur            w0, [x4, #0xb]
    // 0x6543d8: DecompressPointer r0
    //     0x6543d8: add             x0, x0, HEAP, lsl #32
    // 0x6543dc: str             x0, [SP]
    // 0x6543e0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x6543e0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x6543e4: r0 = _setSecondaryAnimation()
    //     0x6543e4: bl              #0x652958  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::_setSecondaryAnimation
    // 0x6543e8: ldur            x1, [fp, #-8]
    // 0x6543ec: LoadField: r0 = r1->field_f
    //     0x6543ec: ldur            w0, [x1, #0xf]
    // 0x6543f0: DecompressPointer r0
    //     0x6543f0: add             x0, x0, HEAP, lsl #32
    // 0x6543f4: LoadField: r2 = r0->field_47
    //     0x6543f4: ldur            w2, [x0, #0x47]
    // 0x6543f8: DecompressPointer r2
    //     0x6543f8: add             x2, x2, HEAP, lsl #32
    // 0x6543fc: cmp             w2, NULL
    // 0x654400: b.eq            #0x654428
    // 0x654404: str             x2, [SP]
    // 0x654408: mov             x0, x2
    // 0x65440c: ClosureCall
    //     0x65440c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x654410: ldur            x2, [x0, #0x1f]
    //     0x654414: blr             x2
    // 0x654418: ldur            x1, [fp, #-8]
    // 0x65441c: LoadField: r2 = r1->field_f
    //     0x65441c: ldur            w2, [x1, #0xf]
    // 0x654420: DecompressPointer r2
    //     0x654420: add             x2, x2, HEAP, lsl #32
    // 0x654424: StoreField: r2->field_47 = rNULL
    //     0x654424: stur            NULL, [x2, #0x47]
    // 0x654428: r0 = Null
    //     0x654428: mov             x0, NULL
    // 0x65442c: LeaveFrame
    //     0x65442c: mov             SP, fp
    //     0x654430: ldp             fp, lr, [SP], #0x10
    // 0x654434: ret
    //     0x654434: ret             
    // 0x654438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x654438: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65443c: b               #0x65439c
    // 0x654440: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x654440: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x654444: r0 = NullErrorSharedWithoutFPURegs()
    //     0x654444: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x654448, size: 0x6c
    // 0x654448: EnterFrame
    //     0x654448: stp             fp, lr, [SP, #-0x10]!
    //     0x65444c: mov             fp, SP
    // 0x654450: AllocStack(0x8)
    //     0x654450: sub             SP, SP, #8
    // 0x654454: SetupParameters()
    //     0x654454: ldr             x0, [fp, #0x10]
    //     0x654458: ldur            w3, [x0, #0x17]
    //     0x65445c: add             x3, x3, HEAP, lsl #32
    //     0x654460: stur            x3, [fp, #-8]
    // 0x654464: CheckStackOverflow
    //     0x654464: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x654468: cmp             SP, x16
    //     0x65446c: b.ls            #0x6544ac
    // 0x654470: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x654470: ldur            w1, [x3, #0x17]
    // 0x654474: DecompressPointer r1
    //     0x654474: add             x1, x1, HEAP, lsl #32
    // 0x654478: LoadField: r2 = r3->field_1f
    //     0x654478: ldur            w2, [x3, #0x1f]
    // 0x65447c: DecompressPointer r2
    //     0x65447c: add             x2, x2, HEAP, lsl #32
    // 0x654480: r0 = removeStatusListener()
    //     0x654480: bl              #0xd35e3c  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::removeStatusListener
    // 0x654484: ldur            x0, [fp, #-8]
    // 0x654488: LoadField: r1 = r0->field_1b
    //     0x654488: ldur            w1, [x0, #0x1b]
    // 0x65448c: DecompressPointer r1
    //     0x65448c: add             x1, x1, HEAP, lsl #32
    // 0x654490: cmp             w1, NULL
    // 0x654494: b.eq            #0x65449c
    // 0x654498: r0 = dispose()
    //     0x654498: bl              #0x6534d8  ; [package:flutter/src/animation/animations.dart] TrainHoppingAnimation::dispose
    // 0x65449c: r0 = Null
    //     0x65449c: mov             x0, NULL
    // 0x6544a0: LeaveFrame
    //     0x6544a0: mov             SP, fp
    //     0x6544a4: ldp             fp, lr, [SP], #0x10
    // 0x6544a8: ret
    //     0x6544a8: ret             
    // 0x6544ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6544ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6544b0: b               #0x654470
  }
  [closure] void jumpOnAnimationEnd(dynamic, AnimationStatus) {
    // ** addr: 0x6544b4, size: 0xf4
    // 0x6544b4: EnterFrame
    //     0x6544b4: stp             fp, lr, [SP, #-0x10]!
    //     0x6544b8: mov             fp, SP
    // 0x6544bc: AllocStack(0x10)
    //     0x6544bc: sub             SP, SP, #0x10
    // 0x6544c0: SetupParameters()
    //     0x6544c0: ldr             x0, [fp, #0x18]
    //     0x6544c4: ldur            w3, [x0, #0x17]
    //     0x6544c8: add             x3, x3, HEAP, lsl #32
    //     0x6544cc: stur            x3, [fp, #-8]
    // 0x6544d0: CheckStackOverflow
    //     0x6544d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6544d4: cmp             SP, x16
    //     0x6544d8: b.ls            #0x65459c
    // 0x6544dc: ldr             x0, [fp, #0x10]
    // 0x6544e0: r16 = Instance_AnimationStatus
    //     0x6544e0: ldr             x16, [PP, #0x4eb0]  ; [pp+0x4eb0] Obj!AnimationStatus@e37301
    // 0x6544e4: cmp             w0, w16
    // 0x6544e8: b.eq            #0x65458c
    // 0x6544ec: r16 = Instance_AnimationStatus
    //     0x6544ec: ldr             x16, [PP, #0x4eb8]  ; [pp+0x4eb8] Obj!AnimationStatus@e372a1
    // 0x6544f0: cmp             w0, w16
    // 0x6544f4: b.eq            #0x65458c
    // 0x6544f8: r16 = Instance_AnimationStatus
    //     0x6544f8: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0x6544fc: cmp             w0, w16
    // 0x654500: b.eq            #0x654510
    // 0x654504: r16 = Instance_AnimationStatus
    //     0x654504: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0x654508: cmp             w0, w16
    // 0x65450c: b.eq            #0x654510
    // 0x654510: LoadField: r1 = r3->field_f
    //     0x654510: ldur            w1, [x3, #0xf]
    // 0x654514: DecompressPointer r1
    //     0x654514: add             x1, x1, HEAP, lsl #32
    // 0x654518: ArrayLoad: r2 = r3[0]  ; List_4
    //     0x654518: ldur            w2, [x3, #0x17]
    // 0x65451c: DecompressPointer r2
    //     0x65451c: add             x2, x2, HEAP, lsl #32
    // 0x654520: LoadField: r0 = r3->field_13
    //     0x654520: ldur            w0, [x3, #0x13]
    // 0x654524: DecompressPointer r0
    //     0x654524: add             x0, x0, HEAP, lsl #32
    // 0x654528: cmp             w0, NULL
    // 0x65452c: b.eq            #0x6545a4
    // 0x654530: LoadField: r4 = r0->field_27
    //     0x654530: ldur            w4, [x0, #0x27]
    // 0x654534: DecompressPointer r4
    //     0x654534: add             x4, x4, HEAP, lsl #32
    // 0x654538: LoadField: r0 = r4->field_b
    //     0x654538: ldur            w0, [x4, #0xb]
    // 0x65453c: DecompressPointer r0
    //     0x65453c: add             x0, x0, HEAP, lsl #32
    // 0x654540: str             x0, [SP]
    // 0x654544: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x654544: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x654548: r0 = _setSecondaryAnimation()
    //     0x654548: bl              #0x652958  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::_setSecondaryAnimation
    // 0x65454c: ldur            x1, [fp, #-8]
    // 0x654550: LoadField: r0 = r1->field_f
    //     0x654550: ldur            w0, [x1, #0xf]
    // 0x654554: DecompressPointer r0
    //     0x654554: add             x0, x0, HEAP, lsl #32
    // 0x654558: LoadField: r2 = r0->field_47
    //     0x654558: ldur            w2, [x0, #0x47]
    // 0x65455c: DecompressPointer r2
    //     0x65455c: add             x2, x2, HEAP, lsl #32
    // 0x654560: cmp             w2, NULL
    // 0x654564: b.eq            #0x65458c
    // 0x654568: str             x2, [SP]
    // 0x65456c: mov             x0, x2
    // 0x654570: ClosureCall
    //     0x654570: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x654574: ldur            x2, [x0, #0x1f]
    //     0x654578: blr             x2
    // 0x65457c: ldur            x1, [fp, #-8]
    // 0x654580: LoadField: r2 = r1->field_f
    //     0x654580: ldur            w2, [x1, #0xf]
    // 0x654584: DecompressPointer r2
    //     0x654584: add             x2, x2, HEAP, lsl #32
    // 0x654588: StoreField: r2->field_47 = rNULL
    //     0x654588: stur            NULL, [x2, #0x47]
    // 0x65458c: r0 = Null
    //     0x65458c: mov             x0, NULL
    // 0x654590: LeaveFrame
    //     0x654590: mov             SP, fp
    //     0x654594: ldp             fp, lr, [SP], #0x10
    // 0x654598: ret
    //     0x654598: ret             
    // 0x65459c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65459c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6545a0: b               #0x6544dc
    // 0x6545a4: r0 = NullErrorSharedWithoutFPURegs()
    //     0x6545a4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ didPop(/* No info */) {
    // ** addr: 0x654d24, size: 0x88
    // 0x654d24: EnterFrame
    //     0x654d24: stp             fp, lr, [SP, #-0x10]!
    //     0x654d28: mov             fp, SP
    // 0x654d2c: AllocStack(0x10)
    //     0x654d2c: sub             SP, SP, #0x10
    // 0x654d30: SetupParameters(TransitionRoute<X0> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x654d30: mov             x3, x1
    //     0x654d34: stur            x1, [fp, #-8]
    //     0x654d38: stur            x2, [fp, #-0x10]
    // 0x654d3c: CheckStackOverflow
    //     0x654d3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x654d40: cmp             SP, x16
    //     0x654d44: b.ls            #0x654da0
    // 0x654d48: mov             x0, x2
    // 0x654d4c: StoreField: r3->field_43 = r0
    //     0x654d4c: stur            w0, [x3, #0x43]
    //     0x654d50: tbz             w0, #0, #0x654d6c
    //     0x654d54: ldurb           w16, [x3, #-1]
    //     0x654d58: ldurb           w17, [x0, #-1]
    //     0x654d5c: and             x16, x17, x16, lsr #2
    //     0x654d60: tst             x16, HEAP, lsr #32
    //     0x654d64: b.eq            #0x654d6c
    //     0x654d68: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x654d6c: LoadField: r1 = r3->field_37
    //     0x654d6c: ldur            w1, [x3, #0x37]
    // 0x654d70: DecompressPointer r1
    //     0x654d70: add             x1, x1, HEAP, lsl #32
    // 0x654d74: cmp             w1, NULL
    // 0x654d78: b.eq            #0x654da8
    // 0x654d7c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x654d7c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x654d80: r0 = reverse()
    //     0x654d80: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0x654d84: ldur            x1, [fp, #-8]
    // 0x654d88: ldur            x2, [fp, #-0x10]
    // 0x654d8c: r0 = didPop()
    //     0x654d8c: bl              #0x654dac  ; [package:flutter/src/widgets/routes.dart] OverlayRoute::didPop
    // 0x654d90: r0 = true
    //     0x654d90: add             x0, NULL, #0x20  ; true
    // 0x654d94: LeaveFrame
    //     0x654d94: mov             SP, fp
    //     0x654d98: ldp             fp, lr, [SP], #0x10
    // 0x654d9c: ret
    //     0x654d9c: ret             
    // 0x654da0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x654da0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x654da4: b               #0x654d48
    // 0x654da8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x654da8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ finishedWhenPopped(/* No info */) {
    // ** addr: 0x655014, size: 0x6c
    // 0x655014: EnterFrame
    //     0x655014: stp             fp, lr, [SP, #-0x10]!
    //     0x655018: mov             fp, SP
    // 0x65501c: AllocStack(0x8)
    //     0x65501c: sub             SP, SP, #8
    // 0x655020: SetupParameters(TransitionRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0x655020: mov             x0, x1
    //     0x655024: stur            x1, [fp, #-8]
    // 0x655028: CheckStackOverflow
    //     0x655028: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65502c: cmp             SP, x16
    //     0x655030: b.ls            #0x655074
    // 0x655034: LoadField: r1 = r0->field_37
    //     0x655034: ldur            w1, [x0, #0x37]
    // 0x655038: DecompressPointer r1
    //     0x655038: add             x1, x1, HEAP, lsl #32
    // 0x65503c: cmp             w1, NULL
    // 0x655040: b.eq            #0x65507c
    // 0x655044: r0 = isDismissed()
    //     0x655044: bl              #0x655080  ; [package:flutter/src/animation/animation.dart] Animation::isDismissed
    // 0x655048: tbnz            w0, #4, #0x655064
    // 0x65504c: ldur            x1, [fp, #-8]
    // 0x655050: LoadField: r2 = r1->field_2f
    //     0x655050: ldur            w2, [x1, #0x2f]
    // 0x655054: DecompressPointer r2
    //     0x655054: add             x2, x2, HEAP, lsl #32
    // 0x655058: eor             x1, x2, #0x10
    // 0x65505c: mov             x0, x1
    // 0x655060: b               #0x655068
    // 0x655064: r0 = false
    //     0x655064: add             x0, NULL, #0x30  ; false
    // 0x655068: LeaveFrame
    //     0x655068: mov             SP, fp
    //     0x65506c: ldp             fp, lr, [SP], #0x10
    // 0x655070: ret
    //     0x655070: ret             
    // 0x655074: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x655074: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x655078: b               #0x655034
    // 0x65507c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x65507c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didReplace(/* No info */) {
    // ** addr: 0x656c04, size: 0x90
    // 0x656c04: EnterFrame
    //     0x656c04: stp             fp, lr, [SP, #-0x10]!
    //     0x656c08: mov             fp, SP
    // 0x656c0c: CheckStackOverflow
    //     0x656c0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x656c10: cmp             SP, x16
    //     0x656c14: b.ls            #0x656c7c
    // 0x656c18: r0 = LoadClassIdInstr(r2)
    //     0x656c18: ldur            x0, [x2, #-1]
    //     0x656c1c: ubfx            x0, x0, #0xc, #0x14
    // 0x656c20: sub             x16, x0, #0xa5d
    // 0x656c24: cmp             x16, #8
    // 0x656c28: b.hi            #0x656c6c
    // 0x656c2c: LoadField: r0 = r1->field_37
    //     0x656c2c: ldur            w0, [x1, #0x37]
    // 0x656c30: DecompressPointer r0
    //     0x656c30: add             x0, x0, HEAP, lsl #32
    // 0x656c34: cmp             w0, NULL
    // 0x656c38: b.eq            #0x656c84
    // 0x656c3c: LoadField: r1 = r2->field_37
    //     0x656c3c: ldur            w1, [x2, #0x37]
    // 0x656c40: DecompressPointer r1
    //     0x656c40: add             x1, x1, HEAP, lsl #32
    // 0x656c44: cmp             w1, NULL
    // 0x656c48: b.eq            #0x656c88
    // 0x656c4c: LoadField: r2 = r1->field_37
    //     0x656c4c: ldur            w2, [x1, #0x37]
    // 0x656c50: DecompressPointer r2
    //     0x656c50: add             x2, x2, HEAP, lsl #32
    // 0x656c54: r16 = Sentinel
    //     0x656c54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x656c58: cmp             w2, w16
    // 0x656c5c: b.eq            #0x656c8c
    // 0x656c60: LoadField: d0 = r2->field_7
    //     0x656c60: ldur            d0, [x2, #7]
    // 0x656c64: mov             x1, x0
    // 0x656c68: r0 = value=()
    //     0x656c68: bl              #0x6567c4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::value=
    // 0x656c6c: r0 = Null
    //     0x656c6c: mov             x0, NULL
    // 0x656c70: LeaveFrame
    //     0x656c70: mov             SP, fp
    //     0x656c74: ldp             fp, lr, [SP], #0x10
    // 0x656c78: ret
    //     0x656c78: ret             
    // 0x656c7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x656c7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x656c80: b               #0x656c18
    // 0x656c84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x656c84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x656c88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x656c88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x656c8c: r9 = _value
    //     0x656c8c: ldr             x9, [PP, #0x4ea8]  ; [pp+0x4ea8] Field <AnimationController._value@441066280>: late (offset: 0x38)
    // 0x656c90: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x656c90: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ didPush(/* No info */) {
    // ** addr: 0x656f34, size: 0x5c
    // 0x656f34: EnterFrame
    //     0x656f34: stp             fp, lr, [SP, #-0x10]!
    //     0x656f38: mov             fp, SP
    // 0x656f3c: AllocStack(0x8)
    //     0x656f3c: sub             SP, SP, #8
    // 0x656f40: SetupParameters(TransitionRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0x656f40: mov             x0, x1
    //     0x656f44: stur            x1, [fp, #-8]
    // 0x656f48: CheckStackOverflow
    //     0x656f48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x656f4c: cmp             SP, x16
    //     0x656f50: b.ls            #0x656f84
    // 0x656f54: mov             x1, x0
    // 0x656f58: r0 = didPush()
    //     0x656f58: bl              #0x65702c  ; [package:flutter/src/widgets/navigator.dart] Route::didPush
    // 0x656f5c: ldur            x0, [fp, #-8]
    // 0x656f60: LoadField: r1 = r0->field_37
    //     0x656f60: ldur            w1, [x0, #0x37]
    // 0x656f64: DecompressPointer r1
    //     0x656f64: add             x1, x1, HEAP, lsl #32
    // 0x656f68: cmp             w1, NULL
    // 0x656f6c: b.eq            #0x656f8c
    // 0x656f70: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x656f70: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x656f74: r0 = forward()
    //     0x656f74: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x656f78: LeaveFrame
    //     0x656f78: mov             SP, fp
    //     0x656f7c: ldp             fp, lr, [SP], #0x10
    // 0x656f80: ret
    //     0x656f80: ret             
    // 0x656f84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x656f84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x656f88: b               #0x656f54
    // 0x656f8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x656f8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didAdd(/* No info */) {
    // ** addr: 0x65846c, size: 0x60
    // 0x65846c: EnterFrame
    //     0x65846c: stp             fp, lr, [SP, #-0x10]!
    //     0x658470: mov             fp, SP
    // 0x658474: AllocStack(0x8)
    //     0x658474: sub             SP, SP, #8
    // 0x658478: SetupParameters(TransitionRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0x658478: mov             x0, x1
    //     0x65847c: stur            x1, [fp, #-8]
    // 0x658480: CheckStackOverflow
    //     0x658480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x658484: cmp             SP, x16
    //     0x658488: b.ls            #0x6584c0
    // 0x65848c: mov             x1, x0
    // 0x658490: r0 = didAdd()
    //     0x658490: bl              #0x6584cc  ; [package:flutter/src/widgets/navigator.dart] Route::didAdd
    // 0x658494: ldur            x0, [fp, #-8]
    // 0x658498: LoadField: r1 = r0->field_37
    //     0x658498: ldur            w1, [x0, #0x37]
    // 0x65849c: DecompressPointer r1
    //     0x65849c: add             x1, x1, HEAP, lsl #32
    // 0x6584a0: cmp             w1, NULL
    // 0x6584a4: b.eq            #0x6584c8
    // 0x6584a8: LoadField: d0 = r1->field_1b
    //     0x6584a8: ldur            d0, [x1, #0x1b]
    // 0x6584ac: r0 = value=()
    //     0x6584ac: bl              #0x6567c4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::value=
    // 0x6584b0: r0 = Null
    //     0x6584b0: mov             x0, NULL
    // 0x6584b4: LeaveFrame
    //     0x6584b4: mov             SP, fp
    //     0x6584b8: ldp             fp, lr, [SP], #0x10
    // 0x6584bc: ret
    //     0x6584bc: ret             
    // 0x6584c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6584c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6584c4: b               #0x65848c
    // 0x6584c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6584c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ TransitionRoute(/* No info */) {
    // ** addr: 0x65a4bc, size: 0x168
    // 0x65a4bc: EnterFrame
    //     0x65a4bc: stp             fp, lr, [SP, #-0x10]!
    //     0x65a4c0: mov             fp, SP
    // 0x65a4c4: AllocStack(0x28)
    //     0x65a4c4: sub             SP, SP, #0x28
    // 0x65a4c8: r3 = false
    //     0x65a4c8: add             x3, NULL, #0x30  ; false
    // 0x65a4cc: r0 = true
    //     0x65a4cc: add             x0, NULL, #0x20  ; true
    // 0x65a4d0: mov             x5, x1
    // 0x65a4d4: mov             x4, x2
    // 0x65a4d8: stur            x1, [fp, #-8]
    // 0x65a4dc: stur            x2, [fp, #-0x10]
    // 0x65a4e0: CheckStackOverflow
    //     0x65a4e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65a4e4: cmp             SP, x16
    //     0x65a4e8: b.ls            #0x65a61c
    // 0x65a4ec: StoreField: r5->field_2f = r3
    //     0x65a4ec: stur            w3, [x5, #0x2f]
    // 0x65a4f0: StoreField: r5->field_3f = r0
    //     0x65a4f0: stur            w0, [x5, #0x3f]
    // 0x65a4f4: LoadField: r2 = r5->field_7
    //     0x65a4f4: ldur            w2, [x5, #7]
    // 0x65a4f8: DecompressPointer r2
    //     0x65a4f8: add             x2, x2, HEAP, lsl #32
    // 0x65a4fc: r1 = Null
    //     0x65a4fc: mov             x1, NULL
    // 0x65a500: r3 = <X0?>
    //     0x65a500: ldr             x3, [PP, #0x398]  ; [pp+0x398] TypeArguments: <X0?>
    // 0x65a504: r0 = Null
    //     0x65a504: mov             x0, NULL
    // 0x65a508: cmp             x2, x0
    // 0x65a50c: b.eq            #0x65a51c
    // 0x65a510: r30 = InstantiateTypeArgumentsMayShareInstantiatorTAStub
    //     0x65a510: ldr             lr, [PP, #0x3a0]  ; [pp+0x3a0] Stub: InstantiateTypeArgumentsMayShareInstantiatorTA (0x5e0dac)
    // 0x65a514: LoadField: r30 = r30->field_7
    //     0x65a514: ldur            lr, [lr, #7]
    // 0x65a518: blr             lr
    // 0x65a51c: mov             x1, x0
    // 0x65a520: stur            x0, [fp, #-0x18]
    // 0x65a524: r0 = _Future()
    //     0x65a524: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x65a528: stur            x0, [fp, #-0x20]
    // 0x65a52c: StoreField: r0->field_b = rZR
    //     0x65a52c: stur            xzr, [x0, #0xb]
    // 0x65a530: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x65a530: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x65a534: ldr             x0, [x0, #0x7a0]
    //     0x65a538: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x65a53c: cmp             w0, w16
    //     0x65a540: b.ne            #0x65a54c
    //     0x65a544: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x65a548: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x65a54c: mov             x1, x0
    // 0x65a550: ldur            x0, [fp, #-0x20]
    // 0x65a554: StoreField: r0->field_13 = r1
    //     0x65a554: stur            w1, [x0, #0x13]
    // 0x65a558: ldur            x1, [fp, #-0x18]
    // 0x65a55c: r0 = _AsyncCompleter()
    //     0x65a55c: bl              #0x661210  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x65a560: mov             x1, x0
    // 0x65a564: ldur            x0, [fp, #-0x20]
    // 0x65a568: StoreField: r1->field_b = r0
    //     0x65a568: stur            w0, [x1, #0xb]
    // 0x65a56c: mov             x0, x1
    // 0x65a570: ldur            x2, [fp, #-8]
    // 0x65a574: StoreField: r2->field_27 = r0
    //     0x65a574: stur            w0, [x2, #0x27]
    //     0x65a578: ldurb           w16, [x2, #-1]
    //     0x65a57c: ldurb           w17, [x0, #-1]
    //     0x65a580: and             x16, x17, x16, lsr #2
    //     0x65a584: tst             x16, HEAP, lsr #32
    //     0x65a588: b.eq            #0x65a590
    //     0x65a58c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x65a590: r1 = <double>
    //     0x65a590: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x65a594: r0 = ProxyAnimation()
    //     0x65a594: bl              #0x65aa3c  ; AllocateProxyAnimationStub -> ProxyAnimation (size=0x28)
    // 0x65a598: stur            x0, [fp, #-0x18]
    // 0x65a59c: r16 = Instance__AlwaysDismissedAnimation
    //     0x65a59c: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a810] Obj!_AlwaysDismissedAnimation@e25951
    //     0x65a5a0: ldr             x16, [x16, #0x810]
    // 0x65a5a4: str             x16, [SP]
    // 0x65a5a8: mov             x1, x0
    // 0x65a5ac: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x65a5ac: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x65a5b0: r0 = ProxyAnimation()
    //     0x65a5b0: bl              #0x65a83c  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::ProxyAnimation
    // 0x65a5b4: ldur            x0, [fp, #-0x18]
    // 0x65a5b8: ldur            x3, [fp, #-8]
    // 0x65a5bc: StoreField: r3->field_3b = r0
    //     0x65a5bc: stur            w0, [x3, #0x3b]
    //     0x65a5c0: ldurb           w16, [x3, #-1]
    //     0x65a5c4: ldurb           w17, [x0, #-1]
    //     0x65a5c8: and             x16, x17, x16, lsr #2
    //     0x65a5cc: tst             x16, HEAP, lsr #32
    //     0x65a5d0: b.eq            #0x65a5d8
    //     0x65a5d4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x65a5d8: r1 = <OverlayEntry>
    //     0x65a5d8: ldr             x1, [PP, #0x6c18]  ; [pp+0x6c18] TypeArguments: <OverlayEntry>
    // 0x65a5dc: r2 = 0
    //     0x65a5dc: movz            x2, #0
    // 0x65a5e0: r0 = _GrowableList()
    //     0x65a5e0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x65a5e4: ldur            x1, [fp, #-8]
    // 0x65a5e8: StoreField: r1->field_23 = r0
    //     0x65a5e8: stur            w0, [x1, #0x23]
    //     0x65a5ec: ldurb           w16, [x1, #-1]
    //     0x65a5f0: ldurb           w17, [x0, #-1]
    //     0x65a5f4: and             x16, x17, x16, lsr #2
    //     0x65a5f8: tst             x16, HEAP, lsr #32
    //     0x65a5fc: b.eq            #0x65a604
    //     0x65a600: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x65a604: ldur            x2, [fp, #-0x10]
    // 0x65a608: r0 = Route()
    //     0x65a608: bl              #0x65a624  ; [package:flutter/src/widgets/navigator.dart] Route::Route
    // 0x65a60c: r0 = Null
    //     0x65a60c: mov             x0, NULL
    // 0x65a610: LeaveFrame
    //     0x65a610: mov             SP, fp
    //     0x65a614: ldp             fp, lr, [SP], #0x10
    // 0x65a618: ret
    //     0x65a618: ret             
    // 0x65a61c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65a61c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65a620: b               #0x65a4ec
  }
  _ dispose(/* No info */) {
    // ** addr: 0xdaf4dc, size: 0xc4
    // 0xdaf4dc: EnterFrame
    //     0xdaf4dc: stp             fp, lr, [SP, #-0x10]!
    //     0xdaf4e0: mov             fp, SP
    // 0xdaf4e4: AllocStack(0x18)
    //     0xdaf4e4: sub             SP, SP, #0x18
    // 0xdaf4e8: SetupParameters(TransitionRoute<X0> this /* r1 => r0, fp-0x10 */)
    //     0xdaf4e8: mov             x0, x1
    //     0xdaf4ec: stur            x1, [fp, #-0x10]
    // 0xdaf4f0: CheckStackOverflow
    //     0xdaf4f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdaf4f4: cmp             SP, x16
    //     0xdaf4f8: b.ls            #0xdaf598
    // 0xdaf4fc: LoadField: r3 = r0->field_33
    //     0xdaf4fc: ldur            w3, [x0, #0x33]
    // 0xdaf500: DecompressPointer r3
    //     0xdaf500: add             x3, x3, HEAP, lsl #32
    // 0xdaf504: stur            x3, [fp, #-8]
    // 0xdaf508: cmp             w3, NULL
    // 0xdaf50c: b.eq            #0xdaf530
    // 0xdaf510: mov             x2, x0
    // 0xdaf514: r1 = Function '_handleStatusChanged@322188637':.
    //     0xdaf514: add             x1, PP, #0x22, lsl #12  ; [pp+0x22340] AnonymousClosure: (0xdaf8e4), in [package:flutter/src/widgets/routes.dart] TransitionRoute::_handleStatusChanged (0xdaf920)
    //     0xdaf518: ldr             x1, [x1, #0x340]
    // 0xdaf51c: r0 = AllocateClosure()
    //     0xdaf51c: bl              #0xec1630  ; AllocateClosureStub
    // 0xdaf520: ldur            x1, [fp, #-8]
    // 0xdaf524: mov             x2, x0
    // 0xdaf528: r0 = removeStatusListener()
    //     0xdaf528: bl              #0xd35e3c  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::removeStatusListener
    // 0xdaf52c: ldur            x0, [fp, #-0x10]
    // 0xdaf530: LoadField: r1 = r0->field_2b
    //     0xdaf530: ldur            w1, [x0, #0x2b]
    // 0xdaf534: DecompressPointer r1
    //     0xdaf534: add             x1, x1, HEAP, lsl #32
    // 0xdaf538: cmp             w1, NULL
    // 0xdaf53c: b.eq            #0xdaf548
    // 0xdaf540: r0 = dispose()
    //     0xdaf540: bl              #0xdaf724  ; [package:flutter/src/scheduler/binding.dart] PerformanceModeRequestHandle::dispose
    // 0xdaf544: ldur            x0, [fp, #-0x10]
    // 0xdaf548: StoreField: r0->field_2b = rNULL
    //     0xdaf548: stur            NULL, [x0, #0x2b]
    // 0xdaf54c: LoadField: r1 = r0->field_37
    //     0xdaf54c: ldur            w1, [x0, #0x37]
    // 0xdaf550: DecompressPointer r1
    //     0xdaf550: add             x1, x1, HEAP, lsl #32
    // 0xdaf554: cmp             w1, NULL
    // 0xdaf558: b.eq            #0xdaf564
    // 0xdaf55c: r0 = dispose()
    //     0xdaf55c: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xdaf560: ldur            x0, [fp, #-0x10]
    // 0xdaf564: LoadField: r1 = r0->field_27
    //     0xdaf564: ldur            w1, [x0, #0x27]
    // 0xdaf568: DecompressPointer r1
    //     0xdaf568: add             x1, x1, HEAP, lsl #32
    // 0xdaf56c: LoadField: r2 = r0->field_43
    //     0xdaf56c: ldur            w2, [x0, #0x43]
    // 0xdaf570: DecompressPointer r2
    //     0xdaf570: add             x2, x2, HEAP, lsl #32
    // 0xdaf574: str             x2, [SP]
    // 0xdaf578: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xdaf578: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xdaf57c: r0 = complete()
    //     0xdaf57c: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0xdaf580: ldur            x1, [fp, #-0x10]
    // 0xdaf584: r0 = dispose()
    //     0xdaf584: bl              #0xdaf5a0  ; [package:flutter/src/widgets/routes.dart] OverlayRoute::dispose
    // 0xdaf588: r0 = Null
    //     0xdaf588: mov             x0, NULL
    // 0xdaf58c: LeaveFrame
    //     0xdaf58c: mov             SP, fp
    //     0xdaf590: ldp             fp, lr, [SP], #0x10
    // 0xdaf594: ret
    //     0xdaf594: ret             
    // 0xdaf598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdaf598: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdaf59c: b               #0xdaf4fc
  }
  [closure] void _handleStatusChanged(dynamic, AnimationStatus) {
    // ** addr: 0xdaf8e4, size: 0x3c
    // 0xdaf8e4: EnterFrame
    //     0xdaf8e4: stp             fp, lr, [SP, #-0x10]!
    //     0xdaf8e8: mov             fp, SP
    // 0xdaf8ec: ldr             x0, [fp, #0x18]
    // 0xdaf8f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xdaf8f0: ldur            w1, [x0, #0x17]
    // 0xdaf8f4: DecompressPointer r1
    //     0xdaf8f4: add             x1, x1, HEAP, lsl #32
    // 0xdaf8f8: CheckStackOverflow
    //     0xdaf8f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdaf8fc: cmp             SP, x16
    //     0xdaf900: b.ls            #0xdaf918
    // 0xdaf904: ldr             x2, [fp, #0x10]
    // 0xdaf908: r0 = _handleStatusChanged()
    //     0xdaf908: bl              #0xdaf920  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::_handleStatusChanged
    // 0xdaf90c: LeaveFrame
    //     0xdaf90c: mov             SP, fp
    //     0xdaf910: ldp             fp, lr, [SP], #0x10
    // 0xdaf914: ret
    //     0xdaf914: ret             
    // 0xdaf918: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdaf918: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdaf91c: b               #0xdaf904
  }
  _ _handleStatusChanged(/* No info */) {
    // ** addr: 0xdaf920, size: 0x1a4
    // 0xdaf920: EnterFrame
    //     0xdaf920: stp             fp, lr, [SP, #-0x10]!
    //     0xdaf924: mov             fp, SP
    // 0xdaf928: AllocStack(0x8)
    //     0xdaf928: sub             SP, SP, #8
    // 0xdaf92c: SetupParameters(TransitionRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0xdaf92c: mov             x0, x1
    //     0xdaf930: stur            x1, [fp, #-8]
    // 0xdaf934: CheckStackOverflow
    //     0xdaf934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdaf938: cmp             SP, x16
    //     0xdaf93c: b.ls            #0xdafab4
    // 0xdaf940: LoadField: r1 = r2->field_7
    //     0xdaf940: ldur            x1, [x2, #7]
    // 0xdaf944: cmp             x1, #1
    // 0xdaf948: b.gt            #0xdaf9a8
    // 0xdaf94c: cmp             x1, #0
    // 0xdaf950: b.gt            #0xdaf9b0
    // 0xdaf954: mov             x1, x0
    // 0xdaf958: r0 = isActive()
    //     0xdaf958: bl              #0xd83354  ; [package:flutter/src/widgets/navigator.dart] Route::isActive
    // 0xdaf95c: tbz             w0, #4, #0xdafaa4
    // 0xdaf960: ldur            x0, [fp, #-8]
    // 0xdaf964: LoadField: r1 = r0->field_f
    //     0xdaf964: ldur            w1, [x0, #0xf]
    // 0xdaf968: DecompressPointer r1
    //     0xdaf968: add             x1, x1, HEAP, lsl #32
    // 0xdaf96c: cmp             w1, NULL
    // 0xdaf970: b.eq            #0xdafabc
    // 0xdaf974: mov             x2, x0
    // 0xdaf978: r0 = finalizeRoute()
    //     0xdaf978: bl              #0x654e14  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::finalizeRoute
    // 0xdaf97c: ldur            x0, [fp, #-8]
    // 0xdaf980: r1 = true
    //     0xdaf980: add             x1, NULL, #0x20  ; true
    // 0xdaf984: StoreField: r0->field_2f = r1
    //     0xdaf984: stur            w1, [x0, #0x2f]
    // 0xdaf988: LoadField: r1 = r0->field_2b
    //     0xdaf988: ldur            w1, [x0, #0x2b]
    // 0xdaf98c: DecompressPointer r1
    //     0xdaf98c: add             x1, x1, HEAP, lsl #32
    // 0xdaf990: cmp             w1, NULL
    // 0xdaf994: b.eq            #0xdaf9a0
    // 0xdaf998: r0 = dispose()
    //     0xdaf998: bl              #0xdaf724  ; [package:flutter/src/scheduler/binding.dart] PerformanceModeRequestHandle::dispose
    // 0xdaf99c: ldur            x0, [fp, #-8]
    // 0xdaf9a0: StoreField: r0->field_2b = rNULL
    //     0xdaf9a0: stur            NULL, [x0, #0x2b]
    // 0xdaf9a4: b               #0xdafaa4
    // 0xdaf9a8: cmp             x1, #2
    // 0xdaf9ac: b.gt            #0xdafa24
    // 0xdaf9b0: LoadField: r1 = r0->field_23
    //     0xdaf9b0: ldur            w1, [x0, #0x23]
    // 0xdaf9b4: DecompressPointer r1
    //     0xdaf9b4: add             x1, x1, HEAP, lsl #32
    // 0xdaf9b8: LoadField: r2 = r1->field_b
    //     0xdaf9b8: ldur            w2, [x1, #0xb]
    // 0xdaf9bc: cbz             w2, #0xdaf9d0
    // 0xdaf9c0: r0 = first()
    //     0xdaf9c0: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xdaf9c4: mov             x1, x0
    // 0xdaf9c8: r2 = false
    //     0xdaf9c8: add             x2, NULL, #0x30  ; false
    // 0xdaf9cc: r0 = opaque=()
    //     0xdaf9cc: bl              #0x7e39a0  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::opaque=
    // 0xdaf9d0: ldur            x0, [fp, #-8]
    // 0xdaf9d4: LoadField: r1 = r0->field_2b
    //     0xdaf9d4: ldur            w1, [x0, #0x2b]
    // 0xdaf9d8: DecompressPointer r1
    //     0xdaf9d8: add             x1, x1, HEAP, lsl #32
    // 0xdaf9dc: cmp             w1, NULL
    // 0xdaf9e0: b.ne            #0xdafaa4
    // 0xdaf9e4: r1 = LoadStaticField(0x958)
    //     0xdaf9e4: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xdaf9e8: ldr             x1, [x1, #0x12b0]
    // 0xdaf9ec: cmp             w1, NULL
    // 0xdaf9f0: b.eq            #0xdafac0
    // 0xdaf9f4: r2 = Instance_DartPerformanceMode
    //     0xdaf9f4: add             x2, PP, #0x22, lsl #12  ; [pp+0x22348] Obj!DartPerformanceMode@e397c1
    //     0xdaf9f8: ldr             x2, [x2, #0x348]
    // 0xdaf9fc: r0 = requestPerformanceMode()
    //     0xdaf9fc: bl              #0xdafac4  ; [package:flutter/src/widgets/binding.dart] _WidgetsFlutterBinding&BindingBase&GestureBinding&SchedulerBinding::requestPerformanceMode
    // 0xdafa00: ldur            x2, [fp, #-8]
    // 0xdafa04: StoreField: r2->field_2b = r0
    //     0xdafa04: stur            w0, [x2, #0x2b]
    //     0xdafa08: ldurb           w16, [x2, #-1]
    //     0xdafa0c: ldurb           w17, [x0, #-1]
    //     0xdafa10: and             x16, x17, x16, lsr #2
    //     0xdafa14: tst             x16, HEAP, lsr #32
    //     0xdafa18: b.eq            #0xdafa20
    //     0xdafa1c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xdafa20: b               #0xdafaa4
    // 0xdafa24: mov             x2, x0
    // 0xdafa28: LoadField: r1 = r2->field_23
    //     0xdafa28: ldur            w1, [x2, #0x23]
    // 0xdafa2c: DecompressPointer r1
    //     0xdafa2c: add             x1, x1, HEAP, lsl #32
    // 0xdafa30: LoadField: r0 = r1->field_b
    //     0xdafa30: ldur            w0, [x1, #0xb]
    // 0xdafa34: cbz             w0, #0xdafa7c
    // 0xdafa38: r0 = first()
    //     0xdafa38: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xdafa3c: mov             x1, x0
    // 0xdafa40: ldur            x0, [fp, #-8]
    // 0xdafa44: r2 = LoadClassIdInstr(r0)
    //     0xdafa44: ldur            x2, [x0, #-1]
    //     0xdafa48: ubfx            x2, x2, #0xc, #0x14
    // 0xdafa4c: sub             x16, x2, #0xa5d
    // 0xdafa50: cmp             x16, #1
    // 0xdafa54: b.hi            #0xdafa60
    // 0xdafa58: r2 = false
    //     0xdafa58: add             x2, NULL, #0x30  ; false
    // 0xdafa5c: b               #0xdafa78
    // 0xdafa60: cmp             x2, #0xa62
    // 0xdafa64: b.ne            #0xdafa74
    // 0xdafa68: LoadField: r2 = r0->field_bb
    //     0xdafa68: ldur            w2, [x0, #0xbb]
    // 0xdafa6c: DecompressPointer r2
    //     0xdafa6c: add             x2, x2, HEAP, lsl #32
    // 0xdafa70: b               #0xdafa78
    // 0xdafa74: r2 = true
    //     0xdafa74: add             x2, NULL, #0x20  ; true
    // 0xdafa78: r0 = opaque=()
    //     0xdafa78: bl              #0x7e39a0  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::opaque=
    // 0xdafa7c: ldur            x0, [fp, #-8]
    // 0xdafa80: LoadField: r1 = r0->field_2b
    //     0xdafa80: ldur            w1, [x0, #0x2b]
    // 0xdafa84: DecompressPointer r1
    //     0xdafa84: add             x1, x1, HEAP, lsl #32
    // 0xdafa88: cmp             w1, NULL
    // 0xdafa8c: b.ne            #0xdafa98
    // 0xdafa90: mov             x1, x0
    // 0xdafa94: b               #0xdafaa0
    // 0xdafa98: r0 = dispose()
    //     0xdafa98: bl              #0xdaf724  ; [package:flutter/src/scheduler/binding.dart] PerformanceModeRequestHandle::dispose
    // 0xdafa9c: ldur            x1, [fp, #-8]
    // 0xdafaa0: StoreField: r1->field_2b = rNULL
    //     0xdafaa0: stur            NULL, [x1, #0x2b]
    // 0xdafaa4: r0 = Null
    //     0xdafaa4: mov             x0, NULL
    // 0xdafaa8: LeaveFrame
    //     0xdafaa8: mov             SP, fp
    //     0xdafaac: ldp             fp, lr, [SP], #0x10
    // 0xdafab0: ret
    //     0xdafab0: ret             
    // 0xdafab4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdafab4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdafab8: b               #0xdaf940
    // 0xdafabc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdafabc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdafac0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdafac0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ install(/* No info */) {
    // ** addr: 0xdb01fc, size: 0x370
    // 0xdb01fc: EnterFrame
    //     0xdb01fc: stp             fp, lr, [SP, #-0x10]!
    //     0xdb0200: mov             fp, SP
    // 0xdb0204: AllocStack(0x30)
    //     0xdb0204: sub             SP, SP, #0x30
    // 0xdb0208: SetupParameters(TransitionRoute<X0> this /* r1 => r1, fp-0x10 */)
    //     0xdb0208: stur            x1, [fp, #-0x10]
    // 0xdb020c: CheckStackOverflow
    //     0xdb020c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb0210: cmp             SP, x16
    //     0xdb0214: b.ls            #0xdb0540
    // 0xdb0218: r0 = LoadClassIdInstr(r1)
    //     0xdb0218: ldur            x0, [x1, #-1]
    //     0xdb021c: ubfx            x0, x0, #0xc, #0x14
    // 0xdb0220: stur            x0, [fp, #-8]
    // 0xdb0224: cmp             x0, #0xa5d
    // 0xdb0228: b.ne            #0xdb0238
    // 0xdb022c: mov             x2, x1
    // 0xdb0230: mov             x3, x0
    // 0xdb0234: b               #0xdb02c0
    // 0xdb0238: cmp             x0, #0xa5e
    // 0xdb023c: b.ne            #0xdb02b8
    // 0xdb0240: r0 = navigator()
    //     0xdb0240: bl              #0x63e32c  ; [package:get/get_navigation/src/extension_navigation.dart] ::navigator
    // 0xdb0244: cmp             w0, NULL
    // 0xdb0248: b.eq            #0xdb0548
    // 0xdb024c: LoadField: r1 = r0->field_2b
    //     0xdb024c: ldur            w1, [x0, #0x2b]
    // 0xdb0250: DecompressPointer r1
    //     0xdb0250: add             x1, x1, HEAP, lsl #32
    // 0xdb0254: r16 = Sentinel
    //     0xdb0254: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xdb0258: cmp             w1, w16
    // 0xdb025c: b.eq            #0xdb054c
    // 0xdb0260: r0 = currentState()
    //     0xdb0260: bl              #0x658c30  ; [package:flutter/src/widgets/framework.dart] GlobalKey::currentState
    // 0xdb0264: cmp             w0, NULL
    // 0xdb0268: b.eq            #0xdb0558
    // 0xdb026c: mov             x1, x0
    // 0xdb0270: r0 = createAnimationController()
    //     0xdb0270: bl              #0x989518  ; [package:flutter/src/material/bottom_sheet.dart] BottomSheet::createAnimationController
    // 0xdb0274: mov             x1, x0
    // 0xdb0278: ldur            x2, [fp, #-0x10]
    // 0xdb027c: StoreField: r2->field_c7 = r0
    //     0xdb027c: stur            w0, [x2, #0xc7]
    //     0xdb0280: ldurb           w16, [x2, #-1]
    //     0xdb0284: ldurb           w17, [x0, #-1]
    //     0xdb0288: and             x16, x17, x16, lsr #2
    //     0xdb028c: tst             x16, HEAP, lsr #32
    //     0xdb0290: b.eq            #0xdb0298
    //     0xdb0294: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xdb0298: r0 = Instance_Duration
    //     0xdb0298: add             x0, PP, #0x11, lsl #12  ; [pp+0x11d90] Obj!Duration@e3a0e1
    //     0xdb029c: ldr             x0, [x0, #0xd90]
    // 0xdb02a0: StoreField: r1->field_27 = r0
    //     0xdb02a0: stur            w0, [x1, #0x27]
    // 0xdb02a4: r0 = Instance_Duration
    //     0xdb02a4: add             x0, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xdb02a8: ldr             x0, [x0, #0x368]
    // 0xdb02ac: StoreField: r1->field_2b = r0
    //     0xdb02ac: stur            w0, [x1, #0x2b]
    // 0xdb02b0: mov             x0, x1
    // 0xdb02b4: b               #0xdb0438
    // 0xdb02b8: mov             x2, x1
    // 0xdb02bc: ldur            x3, [fp, #-8]
    // 0xdb02c0: r0 = LoadClassIdInstr(r2)
    //     0xdb02c0: ldur            x0, [x2, #-1]
    //     0xdb02c4: ubfx            x0, x0, #0xc, #0x14
    // 0xdb02c8: mov             x1, x2
    // 0xdb02cc: r0 = GDT[cid_x0 + -0xfee]()
    //     0xdb02cc: sub             lr, x0, #0xfee
    //     0xdb02d0: ldr             lr, [x21, lr, lsl #3]
    //     0xdb02d4: blr             lr
    // 0xdb02d8: ldur            x1, [fp, #-0x10]
    // 0xdb02dc: r0 = reverseTransitionDuration()
    //     0xdb02dc: bl              #0xdb0d18  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::reverseTransitionDuration
    // 0xdb02e0: ldur            x0, [fp, #-8]
    // 0xdb02e4: sub             x16, x0, #0xa5d
    // 0xdb02e8: cmp             x16, #1
    // 0xdb02ec: b.ls            #0xdb03e4
    // 0xdb02f0: cmp             x0, #0xa62
    // 0xdb02f4: b.ne            #0xdb0370
    // 0xdb02f8: ldur            x3, [fp, #-0x10]
    // 0xdb02fc: r1 = Null
    //     0xdb02fc: mov             x1, NULL
    // 0xdb0300: r2 = 8
    //     0xdb0300: movz            x2, #0x8
    // 0xdb0304: r0 = AllocateArray()
    //     0xdb0304: bl              #0xec22fc  ; AllocateArrayStub
    // 0xdb0308: r16 = "TransitionRoute"
    //     0xdb0308: add             x16, PP, #0x22, lsl #12  ; [pp+0x22370] "TransitionRoute"
    //     0xdb030c: ldr             x16, [x16, #0x370]
    // 0xdb0310: StoreField: r0->field_f = r16
    //     0xdb0310: stur            w16, [x0, #0xf]
    // 0xdb0314: r16 = "("
    //     0xdb0314: add             x16, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0xdb0318: ldr             x16, [x16, #0xf08]
    // 0xdb031c: StoreField: r0->field_13 = r16
    //     0xdb031c: stur            w16, [x0, #0x13]
    // 0xdb0320: ldur            x1, [fp, #-0x10]
    // 0xdb0324: LoadField: r2 = r1->field_13
    //     0xdb0324: ldur            w2, [x1, #0x13]
    // 0xdb0328: DecompressPointer r2
    //     0xdb0328: add             x2, x2, HEAP, lsl #32
    // 0xdb032c: r3 = LoadClassIdInstr(r2)
    //     0xdb032c: ldur            x3, [x2, #-1]
    //     0xdb0330: ubfx            x3, x3, #0xc, #0x14
    // 0xdb0334: cmp             x3, #0xa51
    // 0xdb0338: b.ne            #0xdb034c
    // 0xdb033c: LoadField: r3 = r2->field_7
    //     0xdb033c: ldur            w3, [x2, #7]
    // 0xdb0340: DecompressPointer r3
    //     0xdb0340: add             x3, x3, HEAP, lsl #32
    // 0xdb0344: mov             x2, x3
    // 0xdb0348: b               #0xdb0358
    // 0xdb034c: LoadField: r3 = r2->field_6b
    //     0xdb034c: ldur            w3, [x2, #0x6b]
    // 0xdb0350: DecompressPointer r3
    //     0xdb0350: add             x3, x3, HEAP, lsl #32
    // 0xdb0354: mov             x2, x3
    // 0xdb0358: ArrayStore: r0[0] = r2  ; List_4
    //     0xdb0358: stur            w2, [x0, #0x17]
    // 0xdb035c: r16 = ")"
    //     0xdb035c: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xdb0360: StoreField: r0->field_1b = r16
    //     0xdb0360: stur            w16, [x0, #0x1b]
    // 0xdb0364: str             x0, [SP]
    // 0xdb0368: r0 = _interpolate()
    //     0xdb0368: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xdb036c: b               #0xdb03e4
    // 0xdb0370: ldur            x0, [fp, #-0x10]
    // 0xdb0374: r1 = Null
    //     0xdb0374: mov             x1, NULL
    // 0xdb0378: r2 = 8
    //     0xdb0378: movz            x2, #0x8
    // 0xdb037c: r0 = AllocateArray()
    //     0xdb037c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xdb0380: r16 = "TransitionRoute"
    //     0xdb0380: add             x16, PP, #0x22, lsl #12  ; [pp+0x22370] "TransitionRoute"
    //     0xdb0384: ldr             x16, [x16, #0x370]
    // 0xdb0388: StoreField: r0->field_f = r16
    //     0xdb0388: stur            w16, [x0, #0xf]
    // 0xdb038c: r16 = "("
    //     0xdb038c: add             x16, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0xdb0390: ldr             x16, [x16, #0xf08]
    // 0xdb0394: StoreField: r0->field_13 = r16
    //     0xdb0394: stur            w16, [x0, #0x13]
    // 0xdb0398: ldur            x1, [fp, #-0x10]
    // 0xdb039c: LoadField: r2 = r1->field_13
    //     0xdb039c: ldur            w2, [x1, #0x13]
    // 0xdb03a0: DecompressPointer r2
    //     0xdb03a0: add             x2, x2, HEAP, lsl #32
    // 0xdb03a4: r3 = LoadClassIdInstr(r2)
    //     0xdb03a4: ldur            x3, [x2, #-1]
    //     0xdb03a8: ubfx            x3, x3, #0xc, #0x14
    // 0xdb03ac: cmp             x3, #0xa51
    // 0xdb03b0: b.ne            #0xdb03c4
    // 0xdb03b4: LoadField: r3 = r2->field_7
    //     0xdb03b4: ldur            w3, [x2, #7]
    // 0xdb03b8: DecompressPointer r3
    //     0xdb03b8: add             x3, x3, HEAP, lsl #32
    // 0xdb03bc: mov             x2, x3
    // 0xdb03c0: b               #0xdb03d0
    // 0xdb03c4: LoadField: r3 = r2->field_6b
    //     0xdb03c4: ldur            w3, [x2, #0x6b]
    // 0xdb03c8: DecompressPointer r3
    //     0xdb03c8: add             x3, x3, HEAP, lsl #32
    // 0xdb03cc: mov             x2, x3
    // 0xdb03d0: ArrayStore: r0[0] = r2  ; List_4
    //     0xdb03d0: stur            w2, [x0, #0x17]
    // 0xdb03d4: r16 = ")"
    //     0xdb03d4: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xdb03d8: StoreField: r0->field_1b = r16
    //     0xdb03d8: stur            w16, [x0, #0x1b]
    // 0xdb03dc: str             x0, [SP]
    // 0xdb03e0: r0 = _interpolate()
    //     0xdb03e0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xdb03e4: ldur            x0, [fp, #-0x10]
    // 0xdb03e8: LoadField: r2 = r0->field_f
    //     0xdb03e8: ldur            w2, [x0, #0xf]
    // 0xdb03ec: DecompressPointer r2
    //     0xdb03ec: add             x2, x2, HEAP, lsl #32
    // 0xdb03f0: stur            x2, [fp, #-0x18]
    // 0xdb03f4: cmp             w2, NULL
    // 0xdb03f8: b.eq            #0xdb055c
    // 0xdb03fc: r1 = <double>
    //     0xdb03fc: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xdb0400: r0 = AnimationController()
    //     0xdb0400: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0xdb0404: stur            x0, [fp, #-0x20]
    // 0xdb0408: r16 = Instance_Duration
    //     0xdb0408: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0xdb040c: ldr             x16, [x16, #0x9c0]
    // 0xdb0410: r30 = Instance_Duration
    //     0xdb0410: add             lr, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0xdb0414: ldr             lr, [lr, #0x9c0]
    // 0xdb0418: stp             lr, x16, [SP]
    // 0xdb041c: mov             x1, x0
    // 0xdb0420: ldur            x2, [fp, #-0x18]
    // 0xdb0424: r4 = const [0, 0x4, 0x2, 0x2, duration, 0x2, reverseDuration, 0x3, null]
    //     0xdb0424: add             x4, PP, #0x22, lsl #12  ; [pp+0x22378] List(9) [0, 0x4, 0x2, 0x2, "duration", 0x2, "reverseDuration", 0x3, Null]
    //     0xdb0428: ldr             x4, [x4, #0x378]
    // 0xdb042c: r0 = AnimationController()
    //     0xdb042c: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0xdb0430: ldur            x0, [fp, #-0x20]
    // 0xdb0434: ldur            x2, [fp, #-0x10]
    // 0xdb0438: StoreField: r2->field_37 = r0
    //     0xdb0438: stur            w0, [x2, #0x37]
    //     0xdb043c: ldurb           w16, [x2, #-1]
    //     0xdb0440: ldurb           w17, [x0, #-1]
    //     0xdb0444: and             x16, x17, x16, lsr #2
    //     0xdb0448: tst             x16, HEAP, lsr #32
    //     0xdb044c: b.eq            #0xdb0454
    //     0xdb0450: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xdb0454: mov             x1, x2
    // 0xdb0458: r0 = current()
    //     0xdb0458: bl              #0x68e558  ; [package:html/src/tokenizer.dart] HtmlTokenizer::current
    // 0xdb045c: ldur            x2, [fp, #-0x10]
    // 0xdb0460: r1 = Function '_handleStatusChanged@322188637':.
    //     0xdb0460: add             x1, PP, #0x22, lsl #12  ; [pp+0x22340] AnonymousClosure: (0xdaf8e4), in [package:flutter/src/widgets/routes.dart] TransitionRoute::_handleStatusChanged (0xdaf920)
    //     0xdb0464: ldr             x1, [x1, #0x340]
    // 0xdb0468: stur            x0, [fp, #-0x18]
    // 0xdb046c: r0 = AllocateClosure()
    //     0xdb046c: bl              #0xec1630  ; AllocateClosureStub
    // 0xdb0470: ldur            x1, [fp, #-0x18]
    // 0xdb0474: mov             x2, x0
    // 0xdb0478: r0 = addStatusListener()
    //     0xdb0478: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0xdb047c: ldur            x0, [fp, #-0x18]
    // 0xdb0480: ldur            x2, [fp, #-0x10]
    // 0xdb0484: StoreField: r2->field_33 = r0
    //     0xdb0484: stur            w0, [x2, #0x33]
    //     0xdb0488: ldurb           w16, [x2, #-1]
    //     0xdb048c: ldurb           w17, [x0, #-1]
    //     0xdb0490: and             x16, x17, x16, lsr #2
    //     0xdb0494: tst             x16, HEAP, lsr #32
    //     0xdb0498: b.eq            #0xdb04a0
    //     0xdb049c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xdb04a0: mov             x1, x2
    // 0xdb04a4: r0 = install()
    //     0xdb04a4: bl              #0xdb056c  ; [package:flutter/src/widgets/routes.dart] OverlayRoute::install
    // 0xdb04a8: ldur            x0, [fp, #-0x10]
    // 0xdb04ac: LoadField: r1 = r0->field_33
    //     0xdb04ac: ldur            w1, [x0, #0x33]
    // 0xdb04b0: DecompressPointer r1
    //     0xdb04b0: add             x1, x1, HEAP, lsl #32
    // 0xdb04b4: cmp             w1, NULL
    // 0xdb04b8: b.eq            #0xdb0560
    // 0xdb04bc: LoadField: r2 = r1->field_43
    //     0xdb04bc: ldur            w2, [x1, #0x43]
    // 0xdb04c0: DecompressPointer r2
    //     0xdb04c0: add             x2, x2, HEAP, lsl #32
    // 0xdb04c4: r16 = Sentinel
    //     0xdb04c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xdb04c8: cmp             w2, w16
    // 0xdb04cc: b.eq            #0xdb0564
    // 0xdb04d0: r16 = Instance_AnimationStatus
    //     0xdb04d0: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0xdb04d4: cmp             w2, w16
    // 0xdb04d8: b.ne            #0xdb0530
    // 0xdb04dc: LoadField: r1 = r0->field_23
    //     0xdb04dc: ldur            w1, [x0, #0x23]
    // 0xdb04e0: DecompressPointer r1
    //     0xdb04e0: add             x1, x1, HEAP, lsl #32
    // 0xdb04e4: LoadField: r2 = r1->field_b
    //     0xdb04e4: ldur            w2, [x1, #0xb]
    // 0xdb04e8: cbz             w2, #0xdb0530
    // 0xdb04ec: ldur            x2, [fp, #-8]
    // 0xdb04f0: r0 = first()
    //     0xdb04f0: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xdb04f4: mov             x1, x0
    // 0xdb04f8: ldur            x0, [fp, #-8]
    // 0xdb04fc: sub             x16, x0, #0xa5d
    // 0xdb0500: cmp             x16, #1
    // 0xdb0504: b.hi            #0xdb0510
    // 0xdb0508: r2 = false
    //     0xdb0508: add             x2, NULL, #0x30  ; false
    // 0xdb050c: b               #0xdb052c
    // 0xdb0510: cmp             x0, #0xa62
    // 0xdb0514: b.ne            #0xdb0528
    // 0xdb0518: ldur            x0, [fp, #-0x10]
    // 0xdb051c: LoadField: r2 = r0->field_bb
    //     0xdb051c: ldur            w2, [x0, #0xbb]
    // 0xdb0520: DecompressPointer r2
    //     0xdb0520: add             x2, x2, HEAP, lsl #32
    // 0xdb0524: b               #0xdb052c
    // 0xdb0528: r2 = true
    //     0xdb0528: add             x2, NULL, #0x20  ; true
    // 0xdb052c: r0 = opaque=()
    //     0xdb052c: bl              #0x7e39a0  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::opaque=
    // 0xdb0530: r0 = Null
    //     0xdb0530: mov             x0, NULL
    // 0xdb0534: LeaveFrame
    //     0xdb0534: mov             SP, fp
    //     0xdb0538: ldp             fp, lr, [SP], #0x10
    // 0xdb053c: ret
    //     0xdb053c: ret             
    // 0xdb0540: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb0540: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb0544: b               #0xdb0218
    // 0xdb0548: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb0548: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdb054c: r9 = _overlayKey
    //     0xdb054c: add             x9, PP, #0x1a, lsl #12  ; [pp+0x1a4c8] Field <NavigatorState._overlayKey@302124995>: late (offset: 0x2c)
    //     0xdb0550: ldr             x9, [x9, #0x4c8]
    // 0xdb0554: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xdb0554: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xdb0558: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb0558: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdb055c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb055c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdb0560: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb0560: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdb0564: r9 = _status
    //     0xdb0564: ldr             x9, [PP, #0x4ed8]  ; [pp+0x4ed8] Field <AnimationController._status@441066280>: late (offset: 0x44)
    // 0xdb0568: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xdb0568: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ reverseTransitionDuration(/* No info */) {
    // ** addr: 0xdb0d18, size: 0x44
    // 0xdb0d18: EnterFrame
    //     0xdb0d18: stp             fp, lr, [SP, #-0x10]!
    //     0xdb0d1c: mov             fp, SP
    // 0xdb0d20: CheckStackOverflow
    //     0xdb0d20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb0d24: cmp             SP, x16
    //     0xdb0d28: b.ls            #0xdb0d54
    // 0xdb0d2c: r0 = LoadClassIdInstr(r1)
    //     0xdb0d2c: ldur            x0, [x1, #-1]
    //     0xdb0d30: ubfx            x0, x0, #0xc, #0x14
    // 0xdb0d34: r0 = GDT[cid_x0 + -0xfee]()
    //     0xdb0d34: sub             lr, x0, #0xfee
    //     0xdb0d38: ldr             lr, [x21, lr, lsl #3]
    //     0xdb0d3c: blr             lr
    // 0xdb0d40: r0 = Instance_Duration
    //     0xdb0d40: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0xdb0d44: ldr             x0, [x0, #0x9c0]
    // 0xdb0d48: LeaveFrame
    //     0xdb0d48: mov             SP, fp
    //     0xdb0d4c: ldp             fp, lr, [SP], #0x10
    // 0xdb0d50: ret
    //     0xdb0d50: ret             
    // 0xdb0d54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb0d54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb0d58: b               #0xdb0d2c
  }
}

// class id: 2650, size: 0x58, field offset: 0x4c
//   transformed mixin,
abstract class _ModalRoute&TransitionRoute&LocalHistoryRoute<X0> extends TransitionRoute<X0>
     with LocalHistoryRoute<X0> {

  get _ popDisposition(/* No info */) {
    // ** addr: 0x6502a4, size: 0x5c
    // 0x6502a4: EnterFrame
    //     0x6502a4: stp             fp, lr, [SP, #-0x10]!
    //     0x6502a8: mov             fp, SP
    // 0x6502ac: AllocStack(0x8)
    //     0x6502ac: sub             SP, SP, #8
    // 0x6502b0: SetupParameters(_ModalRoute&TransitionRoute&LocalHistoryRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0x6502b0: mov             x0, x1
    //     0x6502b4: stur            x1, [fp, #-8]
    // 0x6502b8: CheckStackOverflow
    //     0x6502b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6502bc: cmp             SP, x16
    //     0x6502c0: b.ls            #0x6502f8
    // 0x6502c4: mov             x1, x0
    // 0x6502c8: r0 = willHandlePopInternally()
    //     0x6502c8: bl              #0x650560  ; [package:flutter/src/widgets/routes.dart] _ModalRoute&TransitionRoute&LocalHistoryRoute::willHandlePopInternally
    // 0x6502cc: tbnz            w0, #4, #0x6502e4
    // 0x6502d0: r0 = Instance_RoutePopDisposition
    //     0x6502d0: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1a778] Obj!RoutePopDisposition@e34041
    //     0x6502d4: ldr             x0, [x0, #0x778]
    // 0x6502d8: LeaveFrame
    //     0x6502d8: mov             SP, fp
    //     0x6502dc: ldp             fp, lr, [SP], #0x10
    // 0x6502e0: ret
    //     0x6502e0: ret             
    // 0x6502e4: ldur            x1, [fp, #-8]
    // 0x6502e8: r0 = popDisposition()
    //     0x6502e8: bl              #0x650300  ; [package:flutter/src/widgets/navigator.dart] Route::popDisposition
    // 0x6502ec: LeaveFrame
    //     0x6502ec: mov             SP, fp
    //     0x6502f0: ldp             fp, lr, [SP], #0x10
    // 0x6502f4: ret
    //     0x6502f4: ret             
    // 0x6502f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6502f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6502fc: b               #0x6502c4
  }
  get _ willHandlePopInternally(/* No info */) {
    // ** addr: 0x650560, size: 0x34
    // 0x650560: LoadField: r2 = r1->field_4b
    //     0x650560: ldur            w2, [x1, #0x4b]
    // 0x650564: DecompressPointer r2
    //     0x650564: add             x2, x2, HEAP, lsl #32
    // 0x650568: cmp             w2, NULL
    // 0x65056c: b.eq            #0x65058c
    // 0x650570: LoadField: r1 = r2->field_b
    //     0x650570: ldur            w1, [x2, #0xb]
    // 0x650574: cbnz            w1, #0x650580
    // 0x650578: r2 = false
    //     0x650578: add             x2, NULL, #0x30  ; false
    // 0x65057c: b               #0x650584
    // 0x650580: r2 = true
    //     0x650580: add             x2, NULL, #0x20  ; true
    // 0x650584: mov             x0, x2
    // 0x650588: b               #0x650590
    // 0x65058c: r0 = false
    //     0x65058c: add             x0, NULL, #0x30  ; false
    // 0x650590: ret
    //     0x650590: ret             
  }
  _ didPop(/* No info */) {
    // ** addr: 0x654bf8, size: 0x12c
    // 0x654bf8: EnterFrame
    //     0x654bf8: stp             fp, lr, [SP, #-0x10]!
    //     0x654bfc: mov             fp, SP
    // 0x654c00: AllocStack(0x18)
    //     0x654c00: sub             SP, SP, #0x18
    // 0x654c04: SetupParameters(_ModalRoute&TransitionRoute&LocalHistoryRoute<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x654c04: mov             x4, x1
    //     0x654c08: mov             x3, x2
    //     0x654c0c: stur            x1, [fp, #-8]
    //     0x654c10: stur            x2, [fp, #-0x10]
    // 0x654c14: CheckStackOverflow
    //     0x654c14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x654c18: cmp             SP, x16
    //     0x654c1c: b.ls            #0x654d14
    // 0x654c20: LoadField: r2 = r4->field_7
    //     0x654c20: ldur            w2, [x4, #7]
    // 0x654c24: DecompressPointer r2
    //     0x654c24: add             x2, x2, HEAP, lsl #32
    // 0x654c28: mov             x0, x3
    // 0x654c2c: r1 = Null
    //     0x654c2c: mov             x1, NULL
    // 0x654c30: cmp             w0, NULL
    // 0x654c34: b.eq            #0x654c5c
    // 0x654c38: cmp             w2, NULL
    // 0x654c3c: b.eq            #0x654c5c
    // 0x654c40: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x654c40: ldur            w4, [x2, #0x17]
    // 0x654c44: DecompressPointer r4
    //     0x654c44: add             x4, x4, HEAP, lsl #32
    // 0x654c48: r8 = X0?
    //     0x654c48: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x654c4c: LoadField: r9 = r4->field_7
    //     0x654c4c: ldur            x9, [x4, #7]
    // 0x654c50: r3 = Null
    //     0x654c50: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a948] Null
    //     0x654c54: ldr             x3, [x3, #0x948]
    // 0x654c58: blr             x9
    // 0x654c5c: ldur            x3, [fp, #-8]
    // 0x654c60: LoadField: r2 = r3->field_4b
    //     0x654c60: ldur            w2, [x3, #0x4b]
    // 0x654c64: DecompressPointer r2
    //     0x654c64: add             x2, x2, HEAP, lsl #32
    // 0x654c68: cmp             w2, NULL
    // 0x654c6c: b.eq            #0x654cf8
    // 0x654c70: LoadField: r0 = r2->field_b
    //     0x654c70: ldur            w0, [x2, #0xb]
    // 0x654c74: r1 = LoadInt32Instr(r0)
    //     0x654c74: sbfx            x1, x0, #1, #0x1f
    // 0x654c78: cbz             w0, #0x654cf0
    // 0x654c7c: sub             x4, x1, #1
    // 0x654c80: mov             x0, x1
    // 0x654c84: mov             x1, x4
    // 0x654c88: cmp             x1, x0
    // 0x654c8c: b.hs            #0x654d1c
    // 0x654c90: LoadField: r0 = r2->field_f
    //     0x654c90: ldur            w0, [x2, #0xf]
    // 0x654c94: DecompressPointer r0
    //     0x654c94: add             x0, x0, HEAP, lsl #32
    // 0x654c98: ArrayLoad: r5 = r0[r4]  ; Unknown_4
    //     0x654c98: add             x16, x0, x4, lsl #2
    //     0x654c9c: ldur            w5, [x16, #0xf]
    // 0x654ca0: DecompressPointer r5
    //     0x654ca0: add             x5, x5, HEAP, lsl #32
    // 0x654ca4: mov             x1, x2
    // 0x654ca8: mov             x2, x4
    // 0x654cac: stur            x5, [fp, #-0x18]
    // 0x654cb0: r0 = length=()
    //     0x654cb0: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0x654cb4: ldur            x1, [fp, #-0x18]
    // 0x654cb8: StoreField: r1->field_b = rNULL
    //     0x654cb8: stur            NULL, [x1, #0xb]
    // 0x654cbc: r0 = _notifyRemoved()
    //     0x654cbc: bl              #0x65693c  ; [package:flutter/src/widgets/routes.dart] LocalHistoryEntry::_notifyRemoved
    // 0x654cc0: ldur            x1, [fp, #-8]
    // 0x654cc4: LoadField: r0 = r1->field_4b
    //     0x654cc4: ldur            w0, [x1, #0x4b]
    // 0x654cc8: DecompressPointer r0
    //     0x654cc8: add             x0, x0, HEAP, lsl #32
    // 0x654ccc: cmp             w0, NULL
    // 0x654cd0: b.eq            #0x654d20
    // 0x654cd4: LoadField: r2 = r0->field_b
    //     0x654cd4: ldur            w2, [x0, #0xb]
    // 0x654cd8: cbnz            w2, #0x654ce0
    // 0x654cdc: r0 = changedInternalState()
    //     0x654cdc: bl              #0x6507d0  ; [package:flutter/src/widgets/routes.dart] ModalRoute::changedInternalState
    // 0x654ce0: r0 = false
    //     0x654ce0: add             x0, NULL, #0x30  ; false
    // 0x654ce4: LeaveFrame
    //     0x654ce4: mov             SP, fp
    //     0x654ce8: ldp             fp, lr, [SP], #0x10
    // 0x654cec: ret
    //     0x654cec: ret             
    // 0x654cf0: mov             x1, x3
    // 0x654cf4: b               #0x654cfc
    // 0x654cf8: mov             x1, x3
    // 0x654cfc: ldur            x2, [fp, #-0x10]
    // 0x654d00: r0 = didPop()
    //     0x654d00: bl              #0x654d24  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::didPop
    // 0x654d04: r0 = true
    //     0x654d04: add             x0, NULL, #0x20  ; true
    // 0x654d08: LeaveFrame
    //     0x654d08: mov             SP, fp
    //     0x654d0c: ldp             fp, lr, [SP], #0x10
    // 0x654d10: ret
    //     0x654d10: ret             
    // 0x654d14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x654d14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x654d18: b               #0x654c20
    // 0x654d1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x654d1c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x654d20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x654d20: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ willPop(/* No info */) async {
    // ** addr: 0x6737e4, size: 0x64
    // 0x6737e4: EnterFrame
    //     0x6737e4: stp             fp, lr, [SP, #-0x10]!
    //     0x6737e8: mov             fp, SP
    // 0x6737ec: AllocStack(0x10)
    //     0x6737ec: sub             SP, SP, #0x10
    // 0x6737f0: SetupParameters(_ModalRoute&TransitionRoute&LocalHistoryRoute<X0> this /* r1 => r1, fp-0x10 */)
    //     0x6737f0: stur            NULL, [fp, #-8]
    //     0x6737f4: stur            x1, [fp, #-0x10]
    // 0x6737f8: CheckStackOverflow
    //     0x6737f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6737fc: cmp             SP, x16
    //     0x673800: b.ls            #0x673840
    // 0x673804: InitAsync() -> Future<RoutePopDisposition>
    //     0x673804: add             x0, PP, #0x25, lsl #12  ; [pp+0x25508] TypeArguments: <RoutePopDisposition>
    //     0x673808: ldr             x0, [x0, #0x508]
    //     0x67380c: bl              #0x661298  ; InitAsyncStub
    // 0x673810: ldur            x1, [fp, #-0x10]
    // 0x673814: LoadField: r0 = r1->field_4b
    //     0x673814: ldur            w0, [x1, #0x4b]
    // 0x673818: DecompressPointer r0
    //     0x673818: add             x0, x0, HEAP, lsl #32
    // 0x67381c: cmp             w0, NULL
    // 0x673820: b.eq            #0x673838
    // 0x673824: LoadField: r2 = r0->field_b
    //     0x673824: ldur            w2, [x0, #0xb]
    // 0x673828: cbz             w2, #0x673838
    // 0x67382c: r0 = Instance_RoutePopDisposition
    //     0x67382c: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1a778] Obj!RoutePopDisposition@e34041
    //     0x673830: ldr             x0, [x0, #0x778]
    // 0x673834: r0 = ReturnAsyncNotFuture()
    //     0x673834: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x673838: r0 = willPop()
    //     0x673838: bl              #0x673848  ; [package:flutter/src/widgets/navigator.dart] Route::willPop
    // 0x67383c: r0 = ReturnAsync()
    //     0x67383c: b               #0x6576a4  ; ReturnAsyncStub
    // 0x673840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x673840: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x673844: b               #0x673804
  }
}

// class id: 2651, size: 0x90, field offset: 0x58
abstract class ModalRoute<X0> extends _ModalRoute&TransitionRoute&LocalHistoryRoute<X0> {

  late OverlayEntry _modalBarrier; // offset: 0x84
  late OverlayEntry _modalScope; // offset: 0x8c

  _ didPopNext(/* No info */) {
    // ** addr: 0x64faa8, size: 0x350
    // 0x64faa8: EnterFrame
    //     0x64faa8: stp             fp, lr, [SP, #-0x10]!
    //     0x64faac: mov             fp, SP
    // 0x64fab0: AllocStack(0x38)
    //     0x64fab0: sub             SP, SP, #0x38
    // 0x64fab4: SetupParameters(ModalRoute<X0> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x64fab4: mov             x4, x1
    //     0x64fab8: mov             x3, x2
    //     0x64fabc: stur            x1, [fp, #-0x10]
    //     0x64fac0: stur            x2, [fp, #-0x18]
    // 0x64fac4: CheckStackOverflow
    //     0x64fac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64fac8: cmp             SP, x16
    //     0x64facc: b.ls            #0x64fdf0
    // 0x64fad0: LoadField: r5 = r4->field_7
    //     0x64fad0: ldur            w5, [x4, #7]
    // 0x64fad4: DecompressPointer r5
    //     0x64fad4: add             x5, x5, HEAP, lsl #32
    // 0x64fad8: mov             x0, x3
    // 0x64fadc: mov             x2, x5
    // 0x64fae0: stur            x5, [fp, #-8]
    // 0x64fae4: r1 = Null
    //     0x64fae4: mov             x1, NULL
    // 0x64fae8: cmp             w0, NULL
    // 0x64faec: b.eq            #0x64fb38
    // 0x64faf0: branchIfSmi(r0, 0x64fb38)
    //     0x64faf0: tbz             w0, #0, #0x64fb38
    // 0x64faf4: r3 = SubtypeTestCache
    //     0x64faf4: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a6e0] SubtypeTestCache
    //     0x64faf8: ldr             x3, [x3, #0x6e0]
    // 0x64fafc: r30 = Subtype3TestCacheStub
    //     0x64fafc: ldr             lr, [PP, #0x28]  ; [pp+0x28] Stub: Subtype3TestCache (0x5f2c84)
    // 0x64fb00: LoadField: r30 = r30->field_7
    //     0x64fb00: ldur            lr, [lr, #7]
    // 0x64fb04: blr             lr
    // 0x64fb08: cmp             w7, NULL
    // 0x64fb0c: b.eq            #0x64fb18
    // 0x64fb10: tbnz            w7, #4, #0x64fb38
    // 0x64fb14: b               #0x64fb40
    // 0x64fb18: r8 = ModalRoute<X0>
    //     0x64fb18: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1a6e8] Type: ModalRoute<X0>
    //     0x64fb1c: ldr             x8, [x8, #0x6e8]
    // 0x64fb20: r3 = SubtypeTestCache
    //     0x64fb20: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a6f0] SubtypeTestCache
    //     0x64fb24: ldr             x3, [x3, #0x6f0]
    // 0x64fb28: r30 = InstanceOfStub
    //     0x64fb28: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x64fb2c: LoadField: r30 = r30->field_7
    //     0x64fb2c: ldur            lr, [lr, #7]
    // 0x64fb30: blr             lr
    // 0x64fb34: b               #0x64fb44
    // 0x64fb38: r0 = false
    //     0x64fb38: add             x0, NULL, #0x30  ; false
    // 0x64fb3c: b               #0x64fb44
    // 0x64fb40: r0 = true
    //     0x64fb40: add             x0, NULL, #0x20  ; true
    // 0x64fb44: tbnz            w0, #4, #0x64fdbc
    // 0x64fb48: ldur            x3, [fp, #-0x10]
    // 0x64fb4c: r4 = LoadClassIdInstr(r3)
    //     0x64fb4c: ldur            x4, [x3, #-1]
    //     0x64fb50: ubfx            x4, x4, #0xc, #0x14
    // 0x64fb54: stur            x4, [fp, #-0x20]
    // 0x64fb58: sub             x16, x4, #0xa5d
    // 0x64fb5c: cmp             x16, #1
    // 0x64fb60: b.hi            #0x64fb70
    // 0x64fb64: ldur            x2, [fp, #-0x18]
    // 0x64fb68: mov             x3, x4
    // 0x64fb6c: b               #0x64fcfc
    // 0x64fb70: cmp             x4, #0xa62
    // 0x64fb74: b.ne            #0x64fba4
    // 0x64fb78: ldur            x5, [fp, #-0x18]
    // 0x64fb7c: r0 = LoadClassIdInstr(r5)
    //     0x64fb7c: ldur            x0, [x5, #-1]
    //     0x64fb80: ubfx            x0, x0, #0xc, #0x14
    // 0x64fb84: cmp             x0, #0xa62
    // 0x64fb88: b.ne            #0x64fdc0
    // 0x64fb8c: LoadField: r0 = r5->field_8f
    //     0x64fb8c: ldur            w0, [x5, #0x8f]
    // 0x64fb90: DecompressPointer r0
    //     0x64fb90: add             x0, x0, HEAP, lsl #32
    // 0x64fb94: tbz             w0, #4, #0x64fdc0
    // 0x64fb98: mov             x2, x5
    // 0x64fb9c: mov             x3, x4
    // 0x64fba0: b               #0x64fcfc
    // 0x64fba4: ldur            x5, [fp, #-0x18]
    // 0x64fba8: mov             x0, x5
    // 0x64fbac: ldur            x2, [fp, #-8]
    // 0x64fbb0: r1 = Null
    //     0x64fbb0: mov             x1, NULL
    // 0x64fbb4: cmp             w0, NULL
    // 0x64fbb8: b.eq            #0x64fc04
    // 0x64fbbc: branchIfSmi(r0, 0x64fc04)
    //     0x64fbbc: tbz             w0, #0, #0x64fc04
    // 0x64fbc0: r3 = SubtypeTestCache
    //     0x64fbc0: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a6f8] SubtypeTestCache
    //     0x64fbc4: ldr             x3, [x3, #0x6f8]
    // 0x64fbc8: r30 = Subtype3TestCacheStub
    //     0x64fbc8: ldr             lr, [PP, #0x28]  ; [pp+0x28] Stub: Subtype3TestCache (0x5f2c84)
    // 0x64fbcc: LoadField: r30 = r30->field_7
    //     0x64fbcc: ldur            lr, [lr, #7]
    // 0x64fbd0: blr             lr
    // 0x64fbd4: cmp             w7, NULL
    // 0x64fbd8: b.eq            #0x64fbe4
    // 0x64fbdc: tbnz            w7, #4, #0x64fc04
    // 0x64fbe0: b               #0x64fc0c
    // 0x64fbe4: r8 = PageRoute<X0>
    //     0x64fbe4: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1a700] Type: PageRoute<X0>
    //     0x64fbe8: ldr             x8, [x8, #0x700]
    // 0x64fbec: r3 = SubtypeTestCache
    //     0x64fbec: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a708] SubtypeTestCache
    //     0x64fbf0: ldr             x3, [x3, #0x708]
    // 0x64fbf4: r30 = InstanceOfStub
    //     0x64fbf4: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x64fbf8: LoadField: r30 = r30->field_7
    //     0x64fbf8: ldur            lr, [lr, #7]
    // 0x64fbfc: blr             lr
    // 0x64fc00: b               #0x64fc10
    // 0x64fc04: r0 = false
    //     0x64fc04: add             x0, NULL, #0x30  ; false
    // 0x64fc08: b               #0x64fc10
    // 0x64fc0c: r0 = true
    //     0x64fc0c: add             x0, NULL, #0x20  ; true
    // 0x64fc10: tbz             w0, #4, #0x64fc20
    // 0x64fc14: ldur            x3, [fp, #-0x18]
    // 0x64fc18: r4 = true
    //     0x64fc18: add             x4, NULL, #0x20  ; true
    // 0x64fc1c: b               #0x64fc34
    // 0x64fc20: ldur            x3, [fp, #-0x18]
    // 0x64fc24: LoadField: r0 = r3->field_8f
    //     0x64fc24: ldur            w0, [x3, #0x8f]
    // 0x64fc28: DecompressPointer r0
    //     0x64fc28: add             x0, x0, HEAP, lsl #32
    // 0x64fc2c: eor             x1, x0, #0x10
    // 0x64fc30: mov             x4, x1
    // 0x64fc34: mov             x0, x3
    // 0x64fc38: ldur            x2, [fp, #-8]
    // 0x64fc3c: stur            x4, [fp, #-0x28]
    // 0x64fc40: r1 = Null
    //     0x64fc40: mov             x1, NULL
    // 0x64fc44: cmp             w0, NULL
    // 0x64fc48: b.eq            #0x64fc94
    // 0x64fc4c: branchIfSmi(r0, 0x64fc94)
    //     0x64fc4c: tbz             w0, #0, #0x64fc94
    // 0x64fc50: r3 = SubtypeTestCache
    //     0x64fc50: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a710] SubtypeTestCache
    //     0x64fc54: ldr             x3, [x3, #0x710]
    // 0x64fc58: r30 = Subtype3TestCacheStub
    //     0x64fc58: ldr             lr, [PP, #0x28]  ; [pp+0x28] Stub: Subtype3TestCache (0x5f2c84)
    // 0x64fc5c: LoadField: r30 = r30->field_7
    //     0x64fc5c: ldur            lr, [lr, #7]
    // 0x64fc60: blr             lr
    // 0x64fc64: cmp             w7, NULL
    // 0x64fc68: b.eq            #0x64fc74
    // 0x64fc6c: tbnz            w7, #4, #0x64fc94
    // 0x64fc70: b               #0x64fc9c
    // 0x64fc74: r8 = ModalRoute<X0>
    //     0x64fc74: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1a718] Type: ModalRoute<X0>
    //     0x64fc78: ldr             x8, [x8, #0x718]
    // 0x64fc7c: r3 = SubtypeTestCache
    //     0x64fc7c: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a720] SubtypeTestCache
    //     0x64fc80: ldr             x3, [x3, #0x720]
    // 0x64fc84: r30 = InstanceOfStub
    //     0x64fc84: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x64fc88: LoadField: r30 = r30->field_7
    //     0x64fc88: ldur            lr, [lr, #7]
    // 0x64fc8c: blr             lr
    // 0x64fc90: b               #0x64fca0
    // 0x64fc94: r0 = false
    //     0x64fc94: add             x0, NULL, #0x30  ; false
    // 0x64fc98: b               #0x64fca0
    // 0x64fc9c: r0 = true
    //     0x64fc9c: add             x0, NULL, #0x20  ; true
    // 0x64fca0: tbnz            w0, #4, #0x64fcd4
    // 0x64fca4: ldur            x2, [fp, #-0x18]
    // 0x64fca8: r0 = LoadClassIdInstr(r2)
    //     0x64fca8: ldur            x0, [x2, #-1]
    //     0x64fcac: ubfx            x0, x0, #0xc, #0x14
    // 0x64fcb0: mov             x1, x2
    // 0x64fcb4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x64fcb4: sub             lr, x0, #1, lsl #12
    //     0x64fcb8: ldr             lr, [x21, lr, lsl #3]
    //     0x64fcbc: blr             lr
    // 0x64fcc0: cmp             w0, NULL
    // 0x64fcc4: r16 = true
    //     0x64fcc4: add             x16, NULL, #0x20  ; true
    // 0x64fcc8: r17 = false
    //     0x64fcc8: add             x17, NULL, #0x30  ; false
    // 0x64fccc: csel            x1, x16, x17, ne
    // 0x64fcd0: b               #0x64fcd8
    // 0x64fcd4: r1 = false
    //     0x64fcd4: add             x1, NULL, #0x30  ; false
    // 0x64fcd8: ldur            x0, [fp, #-0x28]
    // 0x64fcdc: tbnz            w0, #4, #0x64fdb4
    // 0x64fce0: ldur            x2, [fp, #-0x18]
    // 0x64fce4: r0 = LoadClassIdInstr(r2)
    //     0x64fce4: ldur            x0, [x2, #-1]
    //     0x64fce8: ubfx            x0, x0, #0xc, #0x14
    // 0x64fcec: cmp             x0, #0xa65
    // 0x64fcf0: b.eq            #0x64fcf8
    // 0x64fcf4: tbnz            w1, #4, #0x64fdac
    // 0x64fcf8: ldur            x3, [fp, #-0x20]
    // 0x64fcfc: r0 = LoadClassIdInstr(r2)
    //     0x64fcfc: ldur            x0, [x2, #-1]
    //     0x64fd00: ubfx            x0, x0, #0xc, #0x14
    // 0x64fd04: mov             x1, x2
    // 0x64fd08: r0 = GDT[cid_x0 + -0x1000]()
    //     0x64fd08: sub             lr, x0, #1, lsl #12
    //     0x64fd0c: ldr             lr, [x21, lr, lsl #3]
    //     0x64fd10: blr             lr
    // 0x64fd14: mov             x1, x0
    // 0x64fd18: ldur            x0, [fp, #-0x20]
    // 0x64fd1c: sub             x16, x0, #0xa5d
    // 0x64fd20: cmp             x16, #1
    // 0x64fd24: b.ls            #0x64fd30
    // 0x64fd28: cmp             x0, #0xa62
    // 0x64fd2c: b.ne            #0x64fd38
    // 0x64fd30: r0 = Null
    //     0x64fd30: mov             x0, NULL
    // 0x64fd34: b               #0x64fd40
    // 0x64fd38: r0 = Closure: (BuildContext, Animation<double>, Animation<double>, bool, Widget?) => Widget? from Function '_delegatedTransition@578331911': static.
    //     0x64fd38: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1a728] Closure: (BuildContext, Animation<double>, Animation<double>, bool, Widget?) => Widget? from Function '_delegatedTransition@578331911': static. (0x7e54fb0545cc)
    //     0x64fd3c: ldr             x0, [x0, #0x728]
    // 0x64fd40: r2 = LoadClassIdInstr(r1)
    //     0x64fd40: ldur            x2, [x1, #-1]
    //     0x64fd44: ubfx            x2, x2, #0xc, #0x14
    // 0x64fd48: stp             x0, x1, [SP]
    // 0x64fd4c: mov             x0, x2
    // 0x64fd50: mov             lr, x0
    // 0x64fd54: ldr             lr, [x21, lr, lsl #3]
    // 0x64fd58: blr             lr
    // 0x64fd5c: tbz             w0, #4, #0x64fda4
    // 0x64fd60: ldur            x3, [fp, #-0x10]
    // 0x64fd64: ldur            x2, [fp, #-0x18]
    // 0x64fd68: r0 = LoadClassIdInstr(r2)
    //     0x64fd68: ldur            x0, [x2, #-1]
    //     0x64fd6c: ubfx            x0, x0, #0xc, #0x14
    // 0x64fd70: mov             x1, x2
    // 0x64fd74: r0 = GDT[cid_x0 + -0x1000]()
    //     0x64fd74: sub             lr, x0, #1, lsl #12
    //     0x64fd78: ldr             lr, [x21, lr, lsl #3]
    //     0x64fd7c: blr             lr
    // 0x64fd80: ldur            x3, [fp, #-0x10]
    // 0x64fd84: StoreField: r3->field_5f = r0
    //     0x64fd84: stur            w0, [x3, #0x5f]
    //     0x64fd88: ldurb           w16, [x3, #-1]
    //     0x64fd8c: ldurb           w17, [x0, #-1]
    //     0x64fd90: and             x16, x17, x16, lsr #2
    //     0x64fd94: tst             x16, HEAP, lsr #32
    //     0x64fd98: b.eq            #0x64fda0
    //     0x64fd9c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x64fda0: b               #0x64fdc4
    // 0x64fda4: ldur            x3, [fp, #-0x10]
    // 0x64fda8: b               #0x64fdc0
    // 0x64fdac: ldur            x3, [fp, #-0x10]
    // 0x64fdb0: b               #0x64fdc0
    // 0x64fdb4: ldur            x3, [fp, #-0x10]
    // 0x64fdb8: b               #0x64fdc0
    // 0x64fdbc: ldur            x3, [fp, #-0x10]
    // 0x64fdc0: StoreField: r3->field_5f = rNULL
    //     0x64fdc0: stur            NULL, [x3, #0x5f]
    // 0x64fdc4: mov             x1, x3
    // 0x64fdc8: ldur            x2, [fp, #-0x18]
    // 0x64fdcc: r0 = didPopNext()
    //     0x64fdcc: bl              #0x6523f0  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::didPopNext
    // 0x64fdd0: ldur            x1, [fp, #-0x10]
    // 0x64fdd4: r0 = changedInternalState()
    //     0x64fdd4: bl              #0x6507d0  ; [package:flutter/src/widgets/routes.dart] ModalRoute::changedInternalState
    // 0x64fdd8: ldur            x1, [fp, #-0x10]
    // 0x64fddc: r0 = _maybeDispatchNavigationNotification()
    //     0x64fddc: bl              #0x64fdf8  ; [package:flutter/src/widgets/routes.dart] ModalRoute::_maybeDispatchNavigationNotification
    // 0x64fde0: r0 = Null
    //     0x64fde0: mov             x0, NULL
    // 0x64fde4: LeaveFrame
    //     0x64fde4: mov             SP, fp
    //     0x64fde8: ldp             fp, lr, [SP], #0x10
    // 0x64fdec: ret
    //     0x64fdec: ret             
    // 0x64fdf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x64fdf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64fdf4: b               #0x64fad0
  }
  _ _maybeDispatchNavigationNotification(/* No info */) {
    // ** addr: 0x64fdf8, size: 0x208
    // 0x64fdf8: EnterFrame
    //     0x64fdf8: stp             fp, lr, [SP, #-0x10]!
    //     0x64fdfc: mov             fp, SP
    // 0x64fe00: AllocStack(0x30)
    //     0x64fe00: sub             SP, SP, #0x30
    // 0x64fe04: SetupParameters(ModalRoute<X0> this /* r1 => r1, fp-0x8 */)
    //     0x64fe04: stur            x1, [fp, #-8]
    // 0x64fe08: CheckStackOverflow
    //     0x64fe08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64fe0c: cmp             SP, x16
    //     0x64fe10: b.ls            #0x64fff4
    // 0x64fe14: r1 = 2
    //     0x64fe14: movz            x1, #0x2
    // 0x64fe18: r0 = AllocateContext()
    //     0x64fe18: bl              #0xec126c  ; AllocateContextStub
    // 0x64fe1c: mov             x2, x0
    // 0x64fe20: ldur            x0, [fp, #-8]
    // 0x64fe24: stur            x2, [fp, #-0x10]
    // 0x64fe28: StoreField: r2->field_f = r0
    //     0x64fe28: stur            w0, [x2, #0xf]
    // 0x64fe2c: mov             x1, x0
    // 0x64fe30: r0 = isCurrent()
    //     0x64fe30: bl              #0x650678  ; [package:flutter/src/widgets/navigator.dart] Route::isCurrent
    // 0x64fe34: tbz             w0, #4, #0x64fe48
    // 0x64fe38: r0 = Null
    //     0x64fe38: mov             x0, NULL
    // 0x64fe3c: LeaveFrame
    //     0x64fe3c: mov             SP, fp
    //     0x64fe40: ldp             fp, lr, [SP], #0x10
    // 0x64fe44: ret
    //     0x64fe44: ret             
    // 0x64fe48: ldur            x1, [fp, #-8]
    // 0x64fe4c: r0 = popDisposition()
    //     0x64fe4c: bl              #0x650194  ; [package:flutter/src/widgets/routes.dart] ModalRoute::popDisposition
    // 0x64fe50: r16 = Instance_RoutePopDisposition
    //     0x64fe50: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a740] Obj!RoutePopDisposition@e34061
    //     0x64fe54: ldr             x16, [x16, #0x740]
    // 0x64fe58: cmp             w0, w16
    // 0x64fe5c: b.ne            #0x64fe6c
    // 0x64fe60: ldur            x1, [fp, #-8]
    // 0x64fe64: r0 = true
    //     0x64fe64: add             x0, NULL, #0x20  ; true
    // 0x64fe68: b               #0x64fe8c
    // 0x64fe6c: ldur            x1, [fp, #-8]
    // 0x64fe70: LoadField: r0 = r1->field_6f
    //     0x64fe70: ldur            w0, [x1, #0x6f]
    // 0x64fe74: DecompressPointer r0
    //     0x64fe74: add             x0, x0, HEAP, lsl #32
    // 0x64fe78: LoadField: r2 = r0->field_b
    //     0x64fe78: ldur            w2, [x0, #0xb]
    // 0x64fe7c: cbnz            w2, #0x64fe88
    // 0x64fe80: r0 = false
    //     0x64fe80: add             x0, NULL, #0x30  ; false
    // 0x64fe84: b               #0x64fe8c
    // 0x64fe88: r0 = true
    //     0x64fe88: add             x0, NULL, #0x20  ; true
    // 0x64fe8c: ldur            x2, [fp, #-0x10]
    // 0x64fe90: stur            x0, [fp, #-0x18]
    // 0x64fe94: r0 = NavigationNotification()
    //     0x64fe94: bl              #0x650188  ; AllocateNavigationNotificationStub -> NavigationNotification (size=0xc)
    // 0x64fe98: mov             x2, x0
    // 0x64fe9c: ldur            x0, [fp, #-0x18]
    // 0x64fea0: stur            x2, [fp, #-0x30]
    // 0x64fea4: StoreField: r2->field_7 = r0
    //     0x64fea4: stur            w0, [x2, #7]
    // 0x64fea8: mov             x0, x2
    // 0x64feac: ldur            x1, [fp, #-0x10]
    // 0x64feb0: StoreField: r1->field_13 = r0
    //     0x64feb0: stur            w0, [x1, #0x13]
    //     0x64feb4: ldurb           w16, [x1, #-1]
    //     0x64feb8: ldurb           w17, [x0, #-1]
    //     0x64febc: and             x16, x17, x16, lsr #2
    //     0x64fec0: tst             x16, HEAP, lsr #32
    //     0x64fec4: b.eq            #0x64fecc
    //     0x64fec8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x64fecc: r0 = LoadStaticField(0x958)
    //     0x64fecc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x64fed0: ldr             x0, [x0, #0x12b0]
    // 0x64fed4: cmp             w0, NULL
    // 0x64fed8: b.eq            #0x64fffc
    // 0x64fedc: LoadField: r3 = r0->field_5f
    //     0x64fedc: ldur            w3, [x0, #0x5f]
    // 0x64fee0: DecompressPointer r3
    //     0x64fee0: add             x3, x3, HEAP, lsl #32
    // 0x64fee4: LoadField: r4 = r3->field_7
    //     0x64fee4: ldur            x4, [x3, #7]
    // 0x64fee8: cmp             x4, #2
    // 0x64feec: b.le            #0x64fef8
    // 0x64fef0: cmp             x4, #3
    // 0x64fef4: b.gt            #0x64ffd0
    // 0x64fef8: LoadField: r3 = r0->field_53
    //     0x64fef8: ldur            w3, [x0, #0x53]
    // 0x64fefc: DecompressPointer r3
    //     0x64fefc: add             x3, x3, HEAP, lsl #32
    // 0x64ff00: stur            x3, [fp, #-0x20]
    // 0x64ff04: LoadField: r0 = r3->field_7
    //     0x64ff04: ldur            w0, [x3, #7]
    // 0x64ff08: DecompressPointer r0
    //     0x64ff08: add             x0, x0, HEAP, lsl #32
    // 0x64ff0c: mov             x2, x1
    // 0x64ff10: stur            x0, [fp, #-0x18]
    // 0x64ff14: r1 = Function '<anonymous closure>':.
    //     0x64ff14: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a748] AnonymousClosure: (0x650704), in [package:flutter/src/widgets/routes.dart] ModalRoute::_maybeDispatchNavigationNotification (0x64fdf8)
    //     0x64ff18: ldr             x1, [x1, #0x748]
    // 0x64ff1c: r0 = AllocateClosure()
    //     0x64ff1c: bl              #0xec1630  ; AllocateClosureStub
    // 0x64ff20: ldur            x2, [fp, #-0x18]
    // 0x64ff24: mov             x3, x0
    // 0x64ff28: r1 = Null
    //     0x64ff28: mov             x1, NULL
    // 0x64ff2c: stur            x3, [fp, #-0x10]
    // 0x64ff30: cmp             w2, NULL
    // 0x64ff34: b.eq            #0x64ff54
    // 0x64ff38: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x64ff38: ldur            w4, [x2, #0x17]
    // 0x64ff3c: DecompressPointer r4
    //     0x64ff3c: add             x4, x4, HEAP, lsl #32
    // 0x64ff40: r8 = X0
    //     0x64ff40: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x64ff44: LoadField: r9 = r4->field_7
    //     0x64ff44: ldur            x9, [x4, #7]
    // 0x64ff48: r3 = Null
    //     0x64ff48: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a750] Null
    //     0x64ff4c: ldr             x3, [x3, #0x750]
    // 0x64ff50: blr             x9
    // 0x64ff54: ldur            x0, [fp, #-0x20]
    // 0x64ff58: LoadField: r1 = r0->field_b
    //     0x64ff58: ldur            w1, [x0, #0xb]
    // 0x64ff5c: LoadField: r2 = r0->field_f
    //     0x64ff5c: ldur            w2, [x0, #0xf]
    // 0x64ff60: DecompressPointer r2
    //     0x64ff60: add             x2, x2, HEAP, lsl #32
    // 0x64ff64: LoadField: r3 = r2->field_b
    //     0x64ff64: ldur            w3, [x2, #0xb]
    // 0x64ff68: r2 = LoadInt32Instr(r1)
    //     0x64ff68: sbfx            x2, x1, #1, #0x1f
    // 0x64ff6c: stur            x2, [fp, #-0x28]
    // 0x64ff70: r1 = LoadInt32Instr(r3)
    //     0x64ff70: sbfx            x1, x3, #1, #0x1f
    // 0x64ff74: cmp             x2, x1
    // 0x64ff78: b.ne            #0x64ff84
    // 0x64ff7c: mov             x1, x0
    // 0x64ff80: r0 = _growToNextCapacity()
    //     0x64ff80: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x64ff84: ldur            x0, [fp, #-0x20]
    // 0x64ff88: ldur            x2, [fp, #-0x28]
    // 0x64ff8c: add             x1, x2, #1
    // 0x64ff90: lsl             x3, x1, #1
    // 0x64ff94: StoreField: r0->field_b = r3
    //     0x64ff94: stur            w3, [x0, #0xb]
    // 0x64ff98: LoadField: r1 = r0->field_f
    //     0x64ff98: ldur            w1, [x0, #0xf]
    // 0x64ff9c: DecompressPointer r1
    //     0x64ff9c: add             x1, x1, HEAP, lsl #32
    // 0x64ffa0: ldur            x0, [fp, #-0x10]
    // 0x64ffa4: ArrayStore: r1[r2] = r0  ; List_4
    //     0x64ffa4: add             x25, x1, x2, lsl #2
    //     0x64ffa8: add             x25, x25, #0xf
    //     0x64ffac: str             w0, [x25]
    //     0x64ffb0: tbz             w0, #0, #0x64ffcc
    //     0x64ffb4: ldurb           w16, [x1, #-1]
    //     0x64ffb8: ldurb           w17, [x0, #-1]
    //     0x64ffbc: and             x16, x17, x16, lsr #2
    //     0x64ffc0: tst             x16, HEAP, lsr #32
    //     0x64ffc4: b.eq            #0x64ffcc
    //     0x64ffc8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x64ffcc: b               #0x64ffe4
    // 0x64ffd0: ldur            x1, [fp, #-8]
    // 0x64ffd4: r0 = subtreeContext()
    //     0x64ffd4: bl              #0x650150  ; [package:flutter/src/widgets/routes.dart] ModalRoute::subtreeContext
    // 0x64ffd8: ldur            x1, [fp, #-0x30]
    // 0x64ffdc: mov             x2, x0
    // 0x64ffe0: r0 = dispatch()
    //     0x64ffe0: bl              #0x650038  ; [package:flutter/src/widgets/notification_listener.dart] Notification::dispatch
    // 0x64ffe4: r0 = Null
    //     0x64ffe4: mov             x0, NULL
    // 0x64ffe8: LeaveFrame
    //     0x64ffe8: mov             SP, fp
    //     0x64ffec: ldp             fp, lr, [SP], #0x10
    // 0x64fff0: ret
    //     0x64fff0: ret             
    // 0x64fff4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x64fff4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64fff8: b               #0x64fe14
    // 0x64fffc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x64fffc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _maybeDispatchNavigationNotification(dynamic) {
    // ** addr: 0x650000, size: 0x38
    // 0x650000: EnterFrame
    //     0x650000: stp             fp, lr, [SP, #-0x10]!
    //     0x650004: mov             fp, SP
    // 0x650008: ldr             x0, [fp, #0x10]
    // 0x65000c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x65000c: ldur            w1, [x0, #0x17]
    // 0x650010: DecompressPointer r1
    //     0x650010: add             x1, x1, HEAP, lsl #32
    // 0x650014: CheckStackOverflow
    //     0x650014: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x650018: cmp             SP, x16
    //     0x65001c: b.ls            #0x650030
    // 0x650020: r0 = _maybeDispatchNavigationNotification()
    //     0x650020: bl              #0x64fdf8  ; [package:flutter/src/widgets/routes.dart] ModalRoute::_maybeDispatchNavigationNotification
    // 0x650024: LeaveFrame
    //     0x650024: mov             SP, fp
    //     0x650028: ldp             fp, lr, [SP], #0x10
    // 0x65002c: ret
    //     0x65002c: ret             
    // 0x650030: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x650030: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x650034: b               #0x650020
  }
  get _ subtreeContext(/* No info */) {
    // ** addr: 0x650150, size: 0x38
    // 0x650150: EnterFrame
    //     0x650150: stp             fp, lr, [SP, #-0x10]!
    //     0x650154: mov             fp, SP
    // 0x650158: CheckStackOverflow
    //     0x650158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65015c: cmp             SP, x16
    //     0x650160: b.ls            #0x650180
    // 0x650164: LoadField: r0 = r1->field_7b
    //     0x650164: ldur            w0, [x1, #0x7b]
    // 0x650168: DecompressPointer r0
    //     0x650168: add             x0, x0, HEAP, lsl #32
    // 0x65016c: mov             x1, x0
    // 0x650170: r0 = _currentElement()
    //     0x650170: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x650174: LeaveFrame
    //     0x650174: mov             SP, fp
    //     0x650178: ldp             fp, lr, [SP], #0x10
    // 0x65017c: ret
    //     0x65017c: ret             
    // 0x650180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x650180: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x650184: b               #0x650164
  }
  get _ popDisposition(/* No info */) {
    // ** addr: 0x650194, size: 0x110
    // 0x650194: EnterFrame
    //     0x650194: stp             fp, lr, [SP, #-0x10]!
    //     0x650198: mov             fp, SP
    // 0x65019c: AllocStack(0x20)
    //     0x65019c: sub             SP, SP, #0x20
    // 0x6501a0: SetupParameters(ModalRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0x6501a0: mov             x0, x1
    //     0x6501a4: stur            x1, [fp, #-8]
    // 0x6501a8: CheckStackOverflow
    //     0x6501a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6501ac: cmp             SP, x16
    //     0x6501b0: b.ls            #0x650288
    // 0x6501b4: LoadField: r1 = r0->field_73
    //     0x6501b4: ldur            w1, [x0, #0x73]
    // 0x6501b8: DecompressPointer r1
    //     0x6501b8: add             x1, x1, HEAP, lsl #32
    // 0x6501bc: r0 = iterator()
    //     0x6501bc: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x6501c0: stur            x0, [fp, #-0x18]
    // 0x6501c4: LoadField: r2 = r0->field_7
    //     0x6501c4: ldur            w2, [x0, #7]
    // 0x6501c8: DecompressPointer r2
    //     0x6501c8: add             x2, x2, HEAP, lsl #32
    // 0x6501cc: stur            x2, [fp, #-0x10]
    // 0x6501d0: CheckStackOverflow
    //     0x6501d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6501d4: cmp             SP, x16
    //     0x6501d8: b.ls            #0x650290
    // 0x6501dc: mov             x1, x0
    // 0x6501e0: r0 = moveNext()
    //     0x6501e0: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x6501e4: tbnz            w0, #4, #0x650274
    // 0x6501e8: ldur            x3, [fp, #-0x18]
    // 0x6501ec: LoadField: r4 = r3->field_33
    //     0x6501ec: ldur            w4, [x3, #0x33]
    // 0x6501f0: DecompressPointer r4
    //     0x6501f0: add             x4, x4, HEAP, lsl #32
    // 0x6501f4: stur            x4, [fp, #-0x20]
    // 0x6501f8: cmp             w4, NULL
    // 0x6501fc: b.ne            #0x650230
    // 0x650200: mov             x0, x4
    // 0x650204: ldur            x2, [fp, #-0x10]
    // 0x650208: r1 = Null
    //     0x650208: mov             x1, NULL
    // 0x65020c: cmp             w2, NULL
    // 0x650210: b.eq            #0x650230
    // 0x650214: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x650214: ldur            w4, [x2, #0x17]
    // 0x650218: DecompressPointer r4
    //     0x650218: add             x4, x4, HEAP, lsl #32
    // 0x65021c: r8 = X0
    //     0x65021c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x650220: LoadField: r9 = r4->field_7
    //     0x650220: ldur            x9, [x4, #7]
    // 0x650224: r3 = Null
    //     0x650224: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a760] Null
    //     0x650228: ldr             x3, [x3, #0x760]
    // 0x65022c: blr             x9
    // 0x650230: ldur            x0, [fp, #-0x20]
    // 0x650234: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x650234: ldur            w1, [x0, #0x17]
    // 0x650238: DecompressPointer r1
    //     0x650238: add             x1, x1, HEAP, lsl #32
    // 0x65023c: r16 = Sentinel
    //     0x65023c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x650240: cmp             w1, w16
    // 0x650244: b.eq            #0x650298
    // 0x650248: LoadField: r0 = r1->field_27
    //     0x650248: ldur            w0, [x1, #0x27]
    // 0x65024c: DecompressPointer r0
    //     0x65024c: add             x0, x0, HEAP, lsl #32
    // 0x650250: tbnz            w0, #4, #0x650260
    // 0x650254: ldur            x0, [fp, #-0x18]
    // 0x650258: ldur            x2, [fp, #-0x10]
    // 0x65025c: b               #0x6501d0
    // 0x650260: r0 = Instance_RoutePopDisposition
    //     0x650260: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1a740] Obj!RoutePopDisposition@e34061
    //     0x650264: ldr             x0, [x0, #0x740]
    // 0x650268: LeaveFrame
    //     0x650268: mov             SP, fp
    //     0x65026c: ldp             fp, lr, [SP], #0x10
    // 0x650270: ret
    //     0x650270: ret             
    // 0x650274: ldur            x1, [fp, #-8]
    // 0x650278: r0 = popDisposition()
    //     0x650278: bl              #0x6502a4  ; [package:flutter/src/widgets/routes.dart] _ModalRoute&TransitionRoute&LocalHistoryRoute::popDisposition
    // 0x65027c: LeaveFrame
    //     0x65027c: mov             SP, fp
    //     0x650280: ldp             fp, lr, [SP], #0x10
    // 0x650284: ret
    //     0x650284: ret             
    // 0x650288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x650288: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65028c: b               #0x6501b4
    // 0x650290: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x650290: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x650294: b               #0x6501dc
    // 0x650298: r9 = canPopNotifier
    //     0x650298: add             x9, PP, #0x1a, lsl #12  ; [pp+0x1a770] Field <<EMAIL>>: late final (offset: 0x18)
    //     0x65029c: ldr             x9, [x9, #0x770]
    // 0x6502a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x6502a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x650704, size: 0xcc
    // 0x650704: EnterFrame
    //     0x650704: stp             fp, lr, [SP, #-0x10]!
    //     0x650708: mov             fp, SP
    // 0x65070c: AllocStack(0x10)
    //     0x65070c: sub             SP, SP, #0x10
    // 0x650710: SetupParameters()
    //     0x650710: ldr             x0, [fp, #0x18]
    //     0x650714: ldur            w2, [x0, #0x17]
    //     0x650718: add             x2, x2, HEAP, lsl #32
    //     0x65071c: stur            x2, [fp, #-8]
    // 0x650720: CheckStackOverflow
    //     0x650720: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x650724: cmp             SP, x16
    //     0x650728: b.ls            #0x6507c8
    // 0x65072c: LoadField: r0 = r2->field_f
    //     0x65072c: ldur            w0, [x2, #0xf]
    // 0x650730: DecompressPointer r0
    //     0x650730: add             x0, x0, HEAP, lsl #32
    // 0x650734: LoadField: r1 = r0->field_7b
    //     0x650734: ldur            w1, [x0, #0x7b]
    // 0x650738: DecompressPointer r1
    //     0x650738: add             x1, x1, HEAP, lsl #32
    // 0x65073c: r0 = _currentElement()
    //     0x65073c: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x650740: cmp             w0, NULL
    // 0x650744: b.ne            #0x650750
    // 0x650748: r0 = Null
    //     0x650748: mov             x0, NULL
    // 0x65074c: b               #0x650768
    // 0x650750: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x650750: ldur            w1, [x0, #0x17]
    // 0x650754: DecompressPointer r1
    //     0x650754: add             x1, x1, HEAP, lsl #32
    // 0x650758: cmp             w1, NULL
    // 0x65075c: r16 = true
    //     0x65075c: add             x16, NULL, #0x20  ; true
    // 0x650760: r17 = false
    //     0x650760: add             x17, NULL, #0x30  ; false
    // 0x650764: csel            x0, x16, x17, ne
    // 0x650768: cmp             w0, NULL
    // 0x65076c: b.eq            #0x650774
    // 0x650770: tbz             w0, #4, #0x650784
    // 0x650774: r0 = Null
    //     0x650774: mov             x0, NULL
    // 0x650778: LeaveFrame
    //     0x650778: mov             SP, fp
    //     0x65077c: ldp             fp, lr, [SP], #0x10
    // 0x650780: ret
    //     0x650780: ret             
    // 0x650784: ldur            x0, [fp, #-8]
    // 0x650788: LoadField: r2 = r0->field_13
    //     0x650788: ldur            w2, [x0, #0x13]
    // 0x65078c: DecompressPointer r2
    //     0x65078c: add             x2, x2, HEAP, lsl #32
    // 0x650790: stur            x2, [fp, #-0x10]
    // 0x650794: LoadField: r1 = r0->field_f
    //     0x650794: ldur            w1, [x0, #0xf]
    // 0x650798: DecompressPointer r1
    //     0x650798: add             x1, x1, HEAP, lsl #32
    // 0x65079c: LoadField: r0 = r1->field_7b
    //     0x65079c: ldur            w0, [x1, #0x7b]
    // 0x6507a0: DecompressPointer r0
    //     0x6507a0: add             x0, x0, HEAP, lsl #32
    // 0x6507a4: mov             x1, x0
    // 0x6507a8: r0 = _currentElement()
    //     0x6507a8: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x6507ac: ldur            x1, [fp, #-0x10]
    // 0x6507b0: mov             x2, x0
    // 0x6507b4: r0 = dispatch()
    //     0x6507b4: bl              #0x650038  ; [package:flutter/src/widgets/notification_listener.dart] Notification::dispatch
    // 0x6507b8: r0 = Null
    //     0x6507b8: mov             x0, NULL
    // 0x6507bc: LeaveFrame
    //     0x6507bc: mov             SP, fp
    //     0x6507c0: ldp             fp, lr, [SP], #0x10
    // 0x6507c4: ret
    //     0x6507c4: ret             
    // 0x6507c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6507c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6507cc: b               #0x65072c
  }
  _ changedInternalState(/* No info */) {
    // ** addr: 0x6507d0, size: 0xd0
    // 0x6507d0: EnterFrame
    //     0x6507d0: stp             fp, lr, [SP, #-0x10]!
    //     0x6507d4: mov             fp, SP
    // 0x6507d8: AllocStack(0x8)
    //     0x6507d8: sub             SP, SP, #8
    // 0x6507dc: SetupParameters(ModalRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0x6507dc: mov             x0, x1
    //     0x6507e0: stur            x1, [fp, #-8]
    // 0x6507e4: CheckStackOverflow
    //     0x6507e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6507e8: cmp             SP, x16
    //     0x6507ec: b.ls            #0x65087c
    // 0x6507f0: r1 = LoadStaticField(0x958)
    //     0x6507f0: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x6507f4: ldr             x1, [x1, #0x12b0]
    // 0x6507f8: cmp             w1, NULL
    // 0x6507fc: b.eq            #0x650884
    // 0x650800: LoadField: r2 = r1->field_5f
    //     0x650800: ldur            w2, [x1, #0x5f]
    // 0x650804: DecompressPointer r2
    //     0x650804: add             x2, x2, HEAP, lsl #32
    // 0x650808: r16 = Instance_SchedulerPhase
    //     0x650808: ldr             x16, [PP, #0x1fc8]  ; [pp+0x1fc8] Obj!SchedulerPhase@e35141
    // 0x65080c: cmp             w2, w16
    // 0x650810: b.eq            #0x65084c
    // 0x650814: r1 = Function '<anonymous closure>':.
    //     0x650814: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a798] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x650818: ldr             x1, [x1, #0x798]
    // 0x65081c: r2 = Null
    //     0x65081c: mov             x2, NULL
    // 0x650820: r0 = AllocateClosure()
    //     0x650820: bl              #0xec1630  ; AllocateClosureStub
    // 0x650824: ldur            x1, [fp, #-8]
    // 0x650828: mov             x2, x0
    // 0x65082c: r0 = setState()
    //     0x65082c: bl              #0x650a20  ; [package:flutter/src/widgets/routes.dart] ModalRoute::setState
    // 0x650830: ldur            x0, [fp, #-8]
    // 0x650834: LoadField: r1 = r0->field_83
    //     0x650834: ldur            w1, [x0, #0x83]
    // 0x650838: DecompressPointer r1
    //     0x650838: add             x1, x1, HEAP, lsl #32
    // 0x65083c: r16 = Sentinel
    //     0x65083c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x650840: cmp             w1, w16
    // 0x650844: b.eq            #0x650888
    // 0x650848: r0 = markNeedsBuild()
    //     0x650848: bl              #0x650960  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::markNeedsBuild
    // 0x65084c: ldur            x0, [fp, #-8]
    // 0x650850: LoadField: r1 = r0->field_8b
    //     0x650850: ldur            w1, [x0, #0x8b]
    // 0x650854: DecompressPointer r1
    //     0x650854: add             x1, x1, HEAP, lsl #32
    // 0x650858: r16 = Sentinel
    //     0x650858: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x65085c: cmp             w1, w16
    // 0x650860: b.eq            #0x650894
    // 0x650864: r2 = true
    //     0x650864: add             x2, NULL, #0x20  ; true
    // 0x650868: r0 = maintainState=()
    //     0x650868: bl              #0x6508a0  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::maintainState=
    // 0x65086c: r0 = Null
    //     0x65086c: mov             x0, NULL
    // 0x650870: LeaveFrame
    //     0x650870: mov             SP, fp
    //     0x650874: ldp             fp, lr, [SP], #0x10
    // 0x650878: ret
    //     0x650878: ret             
    // 0x65087c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65087c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x650880: b               #0x6507f0
    // 0x650884: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x650884: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x650888: r9 = _modalBarrier
    //     0x650888: add             x9, PP, #0x1a, lsl #12  ; [pp+0x1a7a0] Field <ModalRoute._modalBarrier@322188637>: late (offset: 0x84)
    //     0x65088c: ldr             x9, [x9, #0x7a0]
    // 0x650890: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x650890: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x650894: r9 = _modalScope
    //     0x650894: add             x9, PP, #0x1a, lsl #12  ; [pp+0x1a7a8] Field <ModalRoute._modalScope@322188637>: late (offset: 0x8c)
    //     0x650898: ldr             x9, [x9, #0x7a8]
    // 0x65089c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x65089c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ setState(/* No info */) {
    // ** addr: 0x650a20, size: 0x8c
    // 0x650a20: EnterFrame
    //     0x650a20: stp             fp, lr, [SP, #-0x10]!
    //     0x650a24: mov             fp, SP
    // 0x650a28: AllocStack(0x18)
    //     0x650a28: sub             SP, SP, #0x18
    // 0x650a2c: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x650a2c: stur            x2, [fp, #-0x10]
    // 0x650a30: CheckStackOverflow
    //     0x650a30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x650a34: cmp             SP, x16
    //     0x650a38: b.ls            #0x650aa0
    // 0x650a3c: LoadField: r0 = r1->field_77
    //     0x650a3c: ldur            w0, [x1, #0x77]
    // 0x650a40: DecompressPointer r0
    //     0x650a40: add             x0, x0, HEAP, lsl #32
    // 0x650a44: mov             x1, x0
    // 0x650a48: stur            x0, [fp, #-8]
    // 0x650a4c: r0 = currentState()
    //     0x650a4c: bl              #0x658c30  ; [package:flutter/src/widgets/framework.dart] GlobalKey::currentState
    // 0x650a50: cmp             w0, NULL
    // 0x650a54: b.eq            #0x650a78
    // 0x650a58: ldur            x1, [fp, #-8]
    // 0x650a5c: r0 = currentState()
    //     0x650a5c: bl              #0x658c30  ; [package:flutter/src/widgets/framework.dart] GlobalKey::currentState
    // 0x650a60: cmp             w0, NULL
    // 0x650a64: b.eq            #0x650aa8
    // 0x650a68: mov             x1, x0
    // 0x650a6c: ldur            x2, [fp, #-0x10]
    // 0x650a70: r0 = _routeSetState()
    //     0x650a70: bl              #0x650aac  ; [package:flutter/src/widgets/routes.dart] _ModalScopeState::_routeSetState
    // 0x650a74: b               #0x650a90
    // 0x650a78: ldur            x16, [fp, #-0x10]
    // 0x650a7c: str             x16, [SP]
    // 0x650a80: ldur            x0, [fp, #-0x10]
    // 0x650a84: ClosureCall
    //     0x650a84: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x650a88: ldur            x2, [x0, #0x1f]
    //     0x650a8c: blr             x2
    // 0x650a90: r0 = Null
    //     0x650a90: mov             x0, NULL
    // 0x650a94: LeaveFrame
    //     0x650a94: mov             SP, fp
    //     0x650a98: ldp             fp, lr, [SP], #0x10
    // 0x650a9c: ret
    //     0x650a9c: ret             
    // 0x650aa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x650aa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x650aa4: b               #0x650a3c
    // 0x650aa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x650aa8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ onPopInvokedWithResult(/* No info */) {
    // ** addr: 0x6548f8, size: 0x18c
    // 0x6548f8: EnterFrame
    //     0x6548f8: stp             fp, lr, [SP, #-0x10]!
    //     0x6548fc: mov             fp, SP
    // 0x654900: AllocStack(0x30)
    //     0x654900: sub             SP, SP, #0x30
    // 0x654904: SetupParameters(ModalRoute<X0> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x654904: mov             x5, x1
    //     0x654908: mov             x4, x2
    //     0x65490c: stur            x1, [fp, #-8]
    //     0x654910: stur            x2, [fp, #-0x10]
    //     0x654914: stur            x3, [fp, #-0x18]
    // 0x654918: CheckStackOverflow
    //     0x654918: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65491c: cmp             SP, x16
    //     0x654920: b.ls            #0x654a70
    // 0x654924: LoadField: r2 = r5->field_7
    //     0x654924: ldur            w2, [x5, #7]
    // 0x654928: DecompressPointer r2
    //     0x654928: add             x2, x2, HEAP, lsl #32
    // 0x65492c: mov             x0, x3
    // 0x654930: r1 = Null
    //     0x654930: mov             x1, NULL
    // 0x654934: cmp             w0, NULL
    // 0x654938: b.eq            #0x654960
    // 0x65493c: cmp             w2, NULL
    // 0x654940: b.eq            #0x654960
    // 0x654944: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x654944: ldur            w4, [x2, #0x17]
    // 0x654948: DecompressPointer r4
    //     0x654948: add             x4, x4, HEAP, lsl #32
    // 0x65494c: r8 = X0?
    //     0x65494c: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x654950: LoadField: r9 = r4->field_7
    //     0x654950: ldur            x9, [x4, #7]
    // 0x654954: r3 = Null
    //     0x654954: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a8c8] Null
    //     0x654958: ldr             x3, [x3, #0x8c8]
    // 0x65495c: blr             x9
    // 0x654960: ldur            x0, [fp, #-8]
    // 0x654964: LoadField: r1 = r0->field_73
    //     0x654964: ldur            w1, [x0, #0x73]
    // 0x654968: DecompressPointer r1
    //     0x654968: add             x1, x1, HEAP, lsl #32
    // 0x65496c: r0 = iterator()
    //     0x65496c: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x654970: stur            x0, [fp, #-0x28]
    // 0x654974: LoadField: r2 = r0->field_7
    //     0x654974: ldur            w2, [x0, #7]
    // 0x654978: DecompressPointer r2
    //     0x654978: add             x2, x2, HEAP, lsl #32
    // 0x65497c: stur            x2, [fp, #-0x20]
    // 0x654980: CheckStackOverflow
    //     0x654980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x654984: cmp             SP, x16
    //     0x654988: b.ls            #0x654a78
    // 0x65498c: mov             x1, x0
    // 0x654990: r0 = moveNext()
    //     0x654990: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x654994: tbnz            w0, #4, #0x654a50
    // 0x654998: ldur            x3, [fp, #-0x28]
    // 0x65499c: LoadField: r4 = r3->field_33
    //     0x65499c: ldur            w4, [x3, #0x33]
    // 0x6549a0: DecompressPointer r4
    //     0x6549a0: add             x4, x4, HEAP, lsl #32
    // 0x6549a4: stur            x4, [fp, #-0x30]
    // 0x6549a8: cmp             w4, NULL
    // 0x6549ac: b.ne            #0x6549e0
    // 0x6549b0: mov             x0, x4
    // 0x6549b4: ldur            x2, [fp, #-0x20]
    // 0x6549b8: r1 = Null
    //     0x6549b8: mov             x1, NULL
    // 0x6549bc: cmp             w2, NULL
    // 0x6549c0: b.eq            #0x6549e0
    // 0x6549c4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6549c4: ldur            w4, [x2, #0x17]
    // 0x6549c8: DecompressPointer r4
    //     0x6549c8: add             x4, x4, HEAP, lsl #32
    // 0x6549cc: r8 = X0
    //     0x6549cc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6549d0: LoadField: r9 = r4->field_7
    //     0x6549d0: ldur            x9, [x4, #7]
    // 0x6549d4: r3 = Null
    //     0x6549d4: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a8d8] Null
    //     0x6549d8: ldr             x3, [x3, #0x8d8]
    // 0x6549dc: blr             x9
    // 0x6549e0: ldur            x3, [fp, #-0x30]
    // 0x6549e4: LoadField: r2 = r3->field_7
    //     0x6549e4: ldur            w2, [x3, #7]
    // 0x6549e8: DecompressPointer r2
    //     0x6549e8: add             x2, x2, HEAP, lsl #32
    // 0x6549ec: ldur            x0, [fp, #-0x18]
    // 0x6549f0: r1 = Null
    //     0x6549f0: mov             x1, NULL
    // 0x6549f4: cmp             w0, NULL
    // 0x6549f8: b.eq            #0x654a24
    // 0x6549fc: cmp             w2, NULL
    // 0x654a00: b.eq            #0x654a24
    // 0x654a04: LoadField: r4 = r2->field_1b
    //     0x654a04: ldur            w4, [x2, #0x1b]
    // 0x654a08: DecompressPointer r4
    //     0x654a08: add             x4, x4, HEAP, lsl #32
    // 0x654a0c: r8 = C1X0?
    //     0x654a0c: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1a8e8] TypeParameter: C1X0?
    //     0x654a10: ldr             x8, [x8, #0x8e8]
    // 0x654a14: LoadField: r9 = r4->field_7
    //     0x654a14: ldur            x9, [x4, #7]
    // 0x654a18: r3 = Null
    //     0x654a18: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a8f0] Null
    //     0x654a1c: ldr             x3, [x3, #0x8f0]
    // 0x654a20: blr             x9
    // 0x654a24: ldur            x0, [fp, #-0x30]
    // 0x654a28: LoadField: r1 = r0->field_b
    //     0x654a28: ldur            w1, [x0, #0xb]
    // 0x654a2c: DecompressPointer r1
    //     0x654a2c: add             x1, x1, HEAP, lsl #32
    // 0x654a30: cmp             w1, NULL
    // 0x654a34: b.eq            #0x654a80
    // 0x654a38: ldur            x2, [fp, #-0x10]
    // 0x654a3c: ldur            x3, [fp, #-0x18]
    // 0x654a40: r0 = _callPopInvoked()
    //     0x654a40: bl              #0x654b0c  ; [package:flutter/src/widgets/pop_scope.dart] PopScope::_callPopInvoked
    // 0x654a44: ldur            x0, [fp, #-0x28]
    // 0x654a48: ldur            x2, [fp, #-0x20]
    // 0x654a4c: b               #0x654980
    // 0x654a50: ldur            x1, [fp, #-8]
    // 0x654a54: ldur            x2, [fp, #-0x10]
    // 0x654a58: ldur            x3, [fp, #-0x18]
    // 0x654a5c: r0 = onPopInvokedWithResult()
    //     0x654a5c: bl              #0x654a84  ; [package:flutter/src/widgets/navigator.dart] Route::onPopInvokedWithResult
    // 0x654a60: r0 = Null
    //     0x654a60: mov             x0, NULL
    // 0x654a64: LeaveFrame
    //     0x654a64: mov             SP, fp
    //     0x654a68: ldp             fp, lr, [SP], #0x10
    // 0x654a6c: ret
    //     0x654a6c: ret             
    // 0x654a70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x654a70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x654a74: b               #0x654924
    // 0x654a78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x654a78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x654a7c: b               #0x65498c
    // 0x654a80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x654a80: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didPush(/* No info */) {
    // ** addr: 0x656e70, size: 0xc4
    // 0x656e70: EnterFrame
    //     0x656e70: stp             fp, lr, [SP, #-0x10]!
    //     0x656e74: mov             fp, SP
    // 0x656e78: AllocStack(0x18)
    //     0x656e78: sub             SP, SP, #0x18
    // 0x656e7c: SetupParameters(ModalRoute<X0> this /* r1 => r0, fp-0x10 */)
    //     0x656e7c: mov             x0, x1
    //     0x656e80: stur            x1, [fp, #-0x10]
    // 0x656e84: CheckStackOverflow
    //     0x656e84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x656e88: cmp             SP, x16
    //     0x656e8c: b.ls            #0x656f20
    // 0x656e90: LoadField: r2 = r0->field_77
    //     0x656e90: ldur            w2, [x0, #0x77]
    // 0x656e94: DecompressPointer r2
    //     0x656e94: add             x2, x2, HEAP, lsl #32
    // 0x656e98: mov             x1, x2
    // 0x656e9c: stur            x2, [fp, #-8]
    // 0x656ea0: r0 = currentState()
    //     0x656ea0: bl              #0x658c30  ; [package:flutter/src/widgets/framework.dart] GlobalKey::currentState
    // 0x656ea4: cmp             w0, NULL
    // 0x656ea8: b.eq            #0x656f0c
    // 0x656eac: ldur            x0, [fp, #-0x10]
    // 0x656eb0: LoadField: r1 = r0->field_f
    //     0x656eb0: ldur            w1, [x0, #0xf]
    // 0x656eb4: DecompressPointer r1
    //     0x656eb4: add             x1, x1, HEAP, lsl #32
    // 0x656eb8: cmp             w1, NULL
    // 0x656ebc: b.eq            #0x656f28
    // 0x656ec0: LoadField: r2 = r1->field_b
    //     0x656ec0: ldur            w2, [x1, #0xb]
    // 0x656ec4: DecompressPointer r2
    //     0x656ec4: add             x2, x2, HEAP, lsl #32
    // 0x656ec8: cmp             w2, NULL
    // 0x656ecc: b.eq            #0x656f2c
    // 0x656ed0: LoadField: r2 = r1->field_43
    //     0x656ed0: ldur            w2, [x1, #0x43]
    // 0x656ed4: DecompressPointer r2
    //     0x656ed4: add             x2, x2, HEAP, lsl #32
    // 0x656ed8: mov             x1, x2
    // 0x656edc: r0 = enclosingScope()
    //     0x656edc: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x656ee0: stur            x0, [fp, #-0x18]
    // 0x656ee4: cmp             w0, NULL
    // 0x656ee8: b.eq            #0x656f0c
    // 0x656eec: ldur            x1, [fp, #-8]
    // 0x656ef0: r0 = currentState()
    //     0x656ef0: bl              #0x658c30  ; [package:flutter/src/widgets/framework.dart] GlobalKey::currentState
    // 0x656ef4: cmp             w0, NULL
    // 0x656ef8: b.eq            #0x656f30
    // 0x656efc: LoadField: r2 = r0->field_1b
    //     0x656efc: ldur            w2, [x0, #0x1b]
    // 0x656f00: DecompressPointer r2
    //     0x656f00: add             x2, x2, HEAP, lsl #32
    // 0x656f04: ldur            x1, [fp, #-0x18]
    // 0x656f08: r0 = setFirstFocus()
    //     0x656f08: bl              #0x650c78  ; [package:flutter/src/widgets/focus_manager.dart] FocusScopeNode::setFirstFocus
    // 0x656f0c: ldur            x1, [fp, #-0x10]
    // 0x656f10: r0 = didPush()
    //     0x656f10: bl              #0x656f34  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::didPush
    // 0x656f14: LeaveFrame
    //     0x656f14: mov             SP, fp
    //     0x656f18: ldp             fp, lr, [SP], #0x10
    // 0x656f1c: ret
    //     0x656f1c: ret             
    // 0x656f20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x656f20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x656f24: b               #0x656e90
    // 0x656f28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x656f28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x656f2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x656f2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x656f30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x656f30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeNext(/* No info */) {
    // ** addr: 0x65805c, size: 0x348
    // 0x65805c: EnterFrame
    //     0x65805c: stp             fp, lr, [SP, #-0x10]!
    //     0x658060: mov             fp, SP
    // 0x658064: AllocStack(0x38)
    //     0x658064: sub             SP, SP, #0x38
    // 0x658068: SetupParameters(ModalRoute<X0> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x658068: mov             x4, x1
    //     0x65806c: mov             x3, x2
    //     0x658070: stur            x1, [fp, #-0x10]
    //     0x658074: stur            x2, [fp, #-0x18]
    // 0x658078: CheckStackOverflow
    //     0x658078: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65807c: cmp             SP, x16
    //     0x658080: b.ls            #0x65839c
    // 0x658084: LoadField: r5 = r4->field_7
    //     0x658084: ldur            w5, [x4, #7]
    // 0x658088: DecompressPointer r5
    //     0x658088: add             x5, x5, HEAP, lsl #32
    // 0x65808c: mov             x0, x3
    // 0x658090: mov             x2, x5
    // 0x658094: stur            x5, [fp, #-8]
    // 0x658098: r1 = Null
    //     0x658098: mov             x1, NULL
    // 0x65809c: cmp             w0, NULL
    // 0x6580a0: b.eq            #0x6580ec
    // 0x6580a4: branchIfSmi(r0, 0x6580ec)
    //     0x6580a4: tbz             w0, #0, #0x6580ec
    // 0x6580a8: r3 = SubtypeTestCache
    //     0x6580a8: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a9d8] SubtypeTestCache
    //     0x6580ac: ldr             x3, [x3, #0x9d8]
    // 0x6580b0: r30 = Subtype3TestCacheStub
    //     0x6580b0: ldr             lr, [PP, #0x28]  ; [pp+0x28] Stub: Subtype3TestCache (0x5f2c84)
    // 0x6580b4: LoadField: r30 = r30->field_7
    //     0x6580b4: ldur            lr, [lr, #7]
    // 0x6580b8: blr             lr
    // 0x6580bc: cmp             w7, NULL
    // 0x6580c0: b.eq            #0x6580cc
    // 0x6580c4: tbnz            w7, #4, #0x6580ec
    // 0x6580c8: b               #0x6580f4
    // 0x6580cc: r8 = ModalRoute<X0>
    //     0x6580cc: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1a9e0] Type: ModalRoute<X0>
    //     0x6580d0: ldr             x8, [x8, #0x9e0]
    // 0x6580d4: r3 = SubtypeTestCache
    //     0x6580d4: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a9e8] SubtypeTestCache
    //     0x6580d8: ldr             x3, [x3, #0x9e8]
    // 0x6580dc: r30 = InstanceOfStub
    //     0x6580dc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x6580e0: LoadField: r30 = r30->field_7
    //     0x6580e0: ldur            lr, [lr, #7]
    // 0x6580e4: blr             lr
    // 0x6580e8: b               #0x6580f8
    // 0x6580ec: r0 = false
    //     0x6580ec: add             x0, NULL, #0x30  ; false
    // 0x6580f0: b               #0x6580f8
    // 0x6580f4: r0 = true
    //     0x6580f4: add             x0, NULL, #0x20  ; true
    // 0x6580f8: tbnz            w0, #4, #0x658370
    // 0x6580fc: ldur            x3, [fp, #-0x10]
    // 0x658100: r4 = LoadClassIdInstr(r3)
    //     0x658100: ldur            x4, [x3, #-1]
    //     0x658104: ubfx            x4, x4, #0xc, #0x14
    // 0x658108: stur            x4, [fp, #-0x20]
    // 0x65810c: sub             x16, x4, #0xa5d
    // 0x658110: cmp             x16, #1
    // 0x658114: b.hi            #0x658124
    // 0x658118: ldur            x2, [fp, #-0x18]
    // 0x65811c: mov             x3, x4
    // 0x658120: b               #0x6582b0
    // 0x658124: cmp             x4, #0xa62
    // 0x658128: b.ne            #0x658158
    // 0x65812c: ldur            x5, [fp, #-0x18]
    // 0x658130: r0 = LoadClassIdInstr(r5)
    //     0x658130: ldur            x0, [x5, #-1]
    //     0x658134: ubfx            x0, x0, #0xc, #0x14
    // 0x658138: cmp             x0, #0xa62
    // 0x65813c: b.ne            #0x658374
    // 0x658140: LoadField: r0 = r5->field_8f
    //     0x658140: ldur            w0, [x5, #0x8f]
    // 0x658144: DecompressPointer r0
    //     0x658144: add             x0, x0, HEAP, lsl #32
    // 0x658148: tbz             w0, #4, #0x658374
    // 0x65814c: mov             x2, x5
    // 0x658150: mov             x3, x4
    // 0x658154: b               #0x6582b0
    // 0x658158: ldur            x5, [fp, #-0x18]
    // 0x65815c: mov             x0, x5
    // 0x658160: ldur            x2, [fp, #-8]
    // 0x658164: r1 = Null
    //     0x658164: mov             x1, NULL
    // 0x658168: cmp             w0, NULL
    // 0x65816c: b.eq            #0x6581b8
    // 0x658170: branchIfSmi(r0, 0x6581b8)
    //     0x658170: tbz             w0, #0, #0x6581b8
    // 0x658174: r3 = SubtypeTestCache
    //     0x658174: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a9f0] SubtypeTestCache
    //     0x658178: ldr             x3, [x3, #0x9f0]
    // 0x65817c: r30 = Subtype3TestCacheStub
    //     0x65817c: ldr             lr, [PP, #0x28]  ; [pp+0x28] Stub: Subtype3TestCache (0x5f2c84)
    // 0x658180: LoadField: r30 = r30->field_7
    //     0x658180: ldur            lr, [lr, #7]
    // 0x658184: blr             lr
    // 0x658188: cmp             w7, NULL
    // 0x65818c: b.eq            #0x658198
    // 0x658190: tbnz            w7, #4, #0x6581b8
    // 0x658194: b               #0x6581c0
    // 0x658198: r8 = PageRoute<X0>
    //     0x658198: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1a9f8] Type: PageRoute<X0>
    //     0x65819c: ldr             x8, [x8, #0x9f8]
    // 0x6581a0: r3 = SubtypeTestCache
    //     0x6581a0: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1aa00] SubtypeTestCache
    //     0x6581a4: ldr             x3, [x3, #0xa00]
    // 0x6581a8: r30 = InstanceOfStub
    //     0x6581a8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x6581ac: LoadField: r30 = r30->field_7
    //     0x6581ac: ldur            lr, [lr, #7]
    // 0x6581b0: blr             lr
    // 0x6581b4: b               #0x6581c4
    // 0x6581b8: r0 = false
    //     0x6581b8: add             x0, NULL, #0x30  ; false
    // 0x6581bc: b               #0x6581c4
    // 0x6581c0: r0 = true
    //     0x6581c0: add             x0, NULL, #0x20  ; true
    // 0x6581c4: tbz             w0, #4, #0x6581d4
    // 0x6581c8: ldur            x3, [fp, #-0x18]
    // 0x6581cc: r4 = true
    //     0x6581cc: add             x4, NULL, #0x20  ; true
    // 0x6581d0: b               #0x6581e8
    // 0x6581d4: ldur            x3, [fp, #-0x18]
    // 0x6581d8: LoadField: r0 = r3->field_8f
    //     0x6581d8: ldur            w0, [x3, #0x8f]
    // 0x6581dc: DecompressPointer r0
    //     0x6581dc: add             x0, x0, HEAP, lsl #32
    // 0x6581e0: eor             x1, x0, #0x10
    // 0x6581e4: mov             x4, x1
    // 0x6581e8: mov             x0, x3
    // 0x6581ec: ldur            x2, [fp, #-8]
    // 0x6581f0: stur            x4, [fp, #-0x28]
    // 0x6581f4: r1 = Null
    //     0x6581f4: mov             x1, NULL
    // 0x6581f8: cmp             w0, NULL
    // 0x6581fc: b.eq            #0x658248
    // 0x658200: branchIfSmi(r0, 0x658248)
    //     0x658200: tbz             w0, #0, #0x658248
    // 0x658204: r3 = SubtypeTestCache
    //     0x658204: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1aa08] SubtypeTestCache
    //     0x658208: ldr             x3, [x3, #0xa08]
    // 0x65820c: r30 = Subtype3TestCacheStub
    //     0x65820c: ldr             lr, [PP, #0x28]  ; [pp+0x28] Stub: Subtype3TestCache (0x5f2c84)
    // 0x658210: LoadField: r30 = r30->field_7
    //     0x658210: ldur            lr, [lr, #7]
    // 0x658214: blr             lr
    // 0x658218: cmp             w7, NULL
    // 0x65821c: b.eq            #0x658228
    // 0x658220: tbnz            w7, #4, #0x658248
    // 0x658224: b               #0x658250
    // 0x658228: r8 = ModalRoute<X0>
    //     0x658228: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1aa10] Type: ModalRoute<X0>
    //     0x65822c: ldr             x8, [x8, #0xa10]
    // 0x658230: r3 = SubtypeTestCache
    //     0x658230: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1aa18] SubtypeTestCache
    //     0x658234: ldr             x3, [x3, #0xa18]
    // 0x658238: r30 = InstanceOfStub
    //     0x658238: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x65823c: LoadField: r30 = r30->field_7
    //     0x65823c: ldur            lr, [lr, #7]
    // 0x658240: blr             lr
    // 0x658244: b               #0x658254
    // 0x658248: r0 = false
    //     0x658248: add             x0, NULL, #0x30  ; false
    // 0x65824c: b               #0x658254
    // 0x658250: r0 = true
    //     0x658250: add             x0, NULL, #0x20  ; true
    // 0x658254: tbnz            w0, #4, #0x658288
    // 0x658258: ldur            x2, [fp, #-0x18]
    // 0x65825c: r0 = LoadClassIdInstr(r2)
    //     0x65825c: ldur            x0, [x2, #-1]
    //     0x658260: ubfx            x0, x0, #0xc, #0x14
    // 0x658264: mov             x1, x2
    // 0x658268: r0 = GDT[cid_x0 + -0x1000]()
    //     0x658268: sub             lr, x0, #1, lsl #12
    //     0x65826c: ldr             lr, [x21, lr, lsl #3]
    //     0x658270: blr             lr
    // 0x658274: cmp             w0, NULL
    // 0x658278: r16 = true
    //     0x658278: add             x16, NULL, #0x20  ; true
    // 0x65827c: r17 = false
    //     0x65827c: add             x17, NULL, #0x30  ; false
    // 0x658280: csel            x1, x16, x17, ne
    // 0x658284: b               #0x65828c
    // 0x658288: r1 = false
    //     0x658288: add             x1, NULL, #0x30  ; false
    // 0x65828c: ldur            x0, [fp, #-0x28]
    // 0x658290: tbnz            w0, #4, #0x658368
    // 0x658294: ldur            x2, [fp, #-0x18]
    // 0x658298: r0 = LoadClassIdInstr(r2)
    //     0x658298: ldur            x0, [x2, #-1]
    //     0x65829c: ubfx            x0, x0, #0xc, #0x14
    // 0x6582a0: cmp             x0, #0xa65
    // 0x6582a4: b.eq            #0x6582ac
    // 0x6582a8: tbnz            w1, #4, #0x658360
    // 0x6582ac: ldur            x3, [fp, #-0x20]
    // 0x6582b0: r0 = LoadClassIdInstr(r2)
    //     0x6582b0: ldur            x0, [x2, #-1]
    //     0x6582b4: ubfx            x0, x0, #0xc, #0x14
    // 0x6582b8: mov             x1, x2
    // 0x6582bc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x6582bc: sub             lr, x0, #1, lsl #12
    //     0x6582c0: ldr             lr, [x21, lr, lsl #3]
    //     0x6582c4: blr             lr
    // 0x6582c8: mov             x1, x0
    // 0x6582cc: ldur            x0, [fp, #-0x20]
    // 0x6582d0: sub             x16, x0, #0xa5d
    // 0x6582d4: cmp             x16, #1
    // 0x6582d8: b.ls            #0x6582e4
    // 0x6582dc: cmp             x0, #0xa62
    // 0x6582e0: b.ne            #0x6582ec
    // 0x6582e4: r0 = Null
    //     0x6582e4: mov             x0, NULL
    // 0x6582e8: b               #0x6582f4
    // 0x6582ec: r0 = Closure: (BuildContext, Animation<double>, Animation<double>, bool, Widget?) => Widget? from Function '_delegatedTransition@578331911': static.
    //     0x6582ec: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1a728] Closure: (BuildContext, Animation<double>, Animation<double>, bool, Widget?) => Widget? from Function '_delegatedTransition@578331911': static. (0x7e54fb0545cc)
    //     0x6582f0: ldr             x0, [x0, #0x728]
    // 0x6582f4: r2 = LoadClassIdInstr(r1)
    //     0x6582f4: ldur            x2, [x1, #-1]
    //     0x6582f8: ubfx            x2, x2, #0xc, #0x14
    // 0x6582fc: stp             x0, x1, [SP]
    // 0x658300: mov             x0, x2
    // 0x658304: mov             lr, x0
    // 0x658308: ldr             lr, [x21, lr, lsl #3]
    // 0x65830c: blr             lr
    // 0x658310: tbz             w0, #4, #0x658358
    // 0x658314: ldur            x3, [fp, #-0x10]
    // 0x658318: ldur            x2, [fp, #-0x18]
    // 0x65831c: r0 = LoadClassIdInstr(r2)
    //     0x65831c: ldur            x0, [x2, #-1]
    //     0x658320: ubfx            x0, x0, #0xc, #0x14
    // 0x658324: mov             x1, x2
    // 0x658328: r0 = GDT[cid_x0 + -0x1000]()
    //     0x658328: sub             lr, x0, #1, lsl #12
    //     0x65832c: ldr             lr, [x21, lr, lsl #3]
    //     0x658330: blr             lr
    // 0x658334: ldur            x3, [fp, #-0x10]
    // 0x658338: StoreField: r3->field_5f = r0
    //     0x658338: stur            w0, [x3, #0x5f]
    //     0x65833c: ldurb           w16, [x3, #-1]
    //     0x658340: ldurb           w17, [x0, #-1]
    //     0x658344: and             x16, x17, x16, lsr #2
    //     0x658348: tst             x16, HEAP, lsr #32
    //     0x65834c: b.eq            #0x658354
    //     0x658350: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x658354: b               #0x658378
    // 0x658358: ldur            x3, [fp, #-0x10]
    // 0x65835c: b               #0x658374
    // 0x658360: ldur            x3, [fp, #-0x10]
    // 0x658364: b               #0x658374
    // 0x658368: ldur            x3, [fp, #-0x10]
    // 0x65836c: b               #0x658374
    // 0x658370: ldur            x3, [fp, #-0x10]
    // 0x658374: StoreField: r3->field_5f = rNULL
    //     0x658374: stur            NULL, [x3, #0x5f]
    // 0x658378: mov             x1, x3
    // 0x65837c: ldur            x2, [fp, #-0x18]
    // 0x658380: r0 = didPopNext()
    //     0x658380: bl              #0x6523f0  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::didPopNext
    // 0x658384: ldur            x1, [fp, #-0x10]
    // 0x658388: r0 = changedInternalState()
    //     0x658388: bl              #0x6507d0  ; [package:flutter/src/widgets/routes.dart] ModalRoute::changedInternalState
    // 0x65838c: r0 = Null
    //     0x65838c: mov             x0, NULL
    // 0x658390: LeaveFrame
    //     0x658390: mov             SP, fp
    //     0x658394: ldp             fp, lr, [SP], #0x10
    // 0x658398: ret
    //     0x658398: ret             
    // 0x65839c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65839c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6583a0: b               #0x658084
  }
  _ didAdd(/* No info */) {
    // ** addr: 0x6583a4, size: 0xc8
    // 0x6583a4: EnterFrame
    //     0x6583a4: stp             fp, lr, [SP, #-0x10]!
    //     0x6583a8: mov             fp, SP
    // 0x6583ac: AllocStack(0x18)
    //     0x6583ac: sub             SP, SP, #0x18
    // 0x6583b0: SetupParameters(ModalRoute<X0> this /* r1 => r0, fp-0x10 */)
    //     0x6583b0: mov             x0, x1
    //     0x6583b4: stur            x1, [fp, #-0x10]
    // 0x6583b8: CheckStackOverflow
    //     0x6583b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6583bc: cmp             SP, x16
    //     0x6583c0: b.ls            #0x658458
    // 0x6583c4: LoadField: r2 = r0->field_77
    //     0x6583c4: ldur            w2, [x0, #0x77]
    // 0x6583c8: DecompressPointer r2
    //     0x6583c8: add             x2, x2, HEAP, lsl #32
    // 0x6583cc: mov             x1, x2
    // 0x6583d0: stur            x2, [fp, #-8]
    // 0x6583d4: r0 = currentState()
    //     0x6583d4: bl              #0x658c30  ; [package:flutter/src/widgets/framework.dart] GlobalKey::currentState
    // 0x6583d8: cmp             w0, NULL
    // 0x6583dc: b.eq            #0x658440
    // 0x6583e0: ldur            x0, [fp, #-0x10]
    // 0x6583e4: LoadField: r1 = r0->field_f
    //     0x6583e4: ldur            w1, [x0, #0xf]
    // 0x6583e8: DecompressPointer r1
    //     0x6583e8: add             x1, x1, HEAP, lsl #32
    // 0x6583ec: cmp             w1, NULL
    // 0x6583f0: b.eq            #0x658460
    // 0x6583f4: LoadField: r2 = r1->field_b
    //     0x6583f4: ldur            w2, [x1, #0xb]
    // 0x6583f8: DecompressPointer r2
    //     0x6583f8: add             x2, x2, HEAP, lsl #32
    // 0x6583fc: cmp             w2, NULL
    // 0x658400: b.eq            #0x658464
    // 0x658404: LoadField: r2 = r1->field_43
    //     0x658404: ldur            w2, [x1, #0x43]
    // 0x658408: DecompressPointer r2
    //     0x658408: add             x2, x2, HEAP, lsl #32
    // 0x65840c: mov             x1, x2
    // 0x658410: r0 = enclosingScope()
    //     0x658410: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x658414: stur            x0, [fp, #-0x18]
    // 0x658418: cmp             w0, NULL
    // 0x65841c: b.eq            #0x658440
    // 0x658420: ldur            x1, [fp, #-8]
    // 0x658424: r0 = currentState()
    //     0x658424: bl              #0x658c30  ; [package:flutter/src/widgets/framework.dart] GlobalKey::currentState
    // 0x658428: cmp             w0, NULL
    // 0x65842c: b.eq            #0x658468
    // 0x658430: LoadField: r2 = r0->field_1b
    //     0x658430: ldur            w2, [x0, #0x1b]
    // 0x658434: DecompressPointer r2
    //     0x658434: add             x2, x2, HEAP, lsl #32
    // 0x658438: ldur            x1, [fp, #-0x18]
    // 0x65843c: r0 = setFirstFocus()
    //     0x65843c: bl              #0x650c78  ; [package:flutter/src/widgets/focus_manager.dart] FocusScopeNode::setFirstFocus
    // 0x658440: ldur            x1, [fp, #-0x10]
    // 0x658444: r0 = didAdd()
    //     0x658444: bl              #0x65846c  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::didAdd
    // 0x658448: r0 = Null
    //     0x658448: mov             x0, NULL
    // 0x65844c: LeaveFrame
    //     0x65844c: mov             SP, fp
    //     0x658450: ldp             fp, lr, [SP], #0x10
    // 0x658454: ret
    //     0x658454: ret             
    // 0x658458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x658458: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65845c: b               #0x6583c4
    // 0x658460: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x658460: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x658464: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x658464: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x658468: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x658468: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ ModalRoute(/* No info */) {
    // ** addr: 0x65a308, size: 0x1b4
    // 0x65a308: EnterFrame
    //     0x65a308: stp             fp, lr, [SP, #-0x10]!
    //     0x65a30c: mov             fp, SP
    // 0x65a310: AllocStack(0x20)
    //     0x65a310: sub             SP, SP, #0x20
    // 0x65a314: r3 = false
    //     0x65a314: add             x3, NULL, #0x30  ; false
    // 0x65a318: r0 = Sentinel
    //     0x65a318: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x65a31c: mov             x5, x1
    // 0x65a320: mov             x4, x2
    // 0x65a324: stur            x1, [fp, #-8]
    // 0x65a328: stur            x2, [fp, #-0x10]
    // 0x65a32c: CheckStackOverflow
    //     0x65a32c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65a330: cmp             SP, x16
    //     0x65a334: b.ls            #0x65a4b4
    // 0x65a338: StoreField: r5->field_63 = r3
    //     0x65a338: stur            w3, [x5, #0x63]
    // 0x65a33c: StoreField: r5->field_83 = r0
    //     0x65a33c: stur            w0, [x5, #0x83]
    // 0x65a340: StoreField: r5->field_8b = r0
    //     0x65a340: stur            w0, [x5, #0x8b]
    // 0x65a344: r1 = <(dynamic this) => Future<bool>>
    //     0x65a344: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1aa98] TypeArguments: <(dynamic this) => Future<bool>>
    //     0x65a348: ldr             x1, [x1, #0xa98]
    // 0x65a34c: r2 = 0
    //     0x65a34c: movz            x2, #0
    // 0x65a350: r0 = _GrowableList()
    //     0x65a350: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x65a354: ldur            x1, [fp, #-8]
    // 0x65a358: StoreField: r1->field_6f = r0
    //     0x65a358: stur            w0, [x1, #0x6f]
    //     0x65a35c: ldurb           w16, [x1, #-1]
    //     0x65a360: ldurb           w17, [x0, #-1]
    //     0x65a364: and             x16, x17, x16, lsr #2
    //     0x65a368: tst             x16, HEAP, lsr #32
    //     0x65a36c: b.eq            #0x65a374
    //     0x65a370: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x65a374: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x65a374: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x65a378: ldr             x0, [x0, #0x778]
    //     0x65a37c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x65a380: cmp             w0, w16
    //     0x65a384: b.ne            #0x65a390
    //     0x65a388: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x65a38c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x65a390: r1 = <PopEntry<Object?>>
    //     0x65a390: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1aaa0] TypeArguments: <PopEntry<Object?>>
    //     0x65a394: ldr             x1, [x1, #0xaa0]
    // 0x65a398: stur            x0, [fp, #-0x18]
    // 0x65a39c: r0 = _Set()
    //     0x65a39c: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x65a3a0: mov             x1, x0
    // 0x65a3a4: ldur            x0, [fp, #-0x18]
    // 0x65a3a8: stur            x1, [fp, #-0x20]
    // 0x65a3ac: StoreField: r1->field_1b = r0
    //     0x65a3ac: stur            w0, [x1, #0x1b]
    // 0x65a3b0: StoreField: r1->field_b = rZR
    //     0x65a3b0: stur            wzr, [x1, #0xb]
    // 0x65a3b4: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x65a3b4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x65a3b8: ldr             x0, [x0, #0x780]
    //     0x65a3bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x65a3c0: cmp             w0, w16
    //     0x65a3c4: b.ne            #0x65a3d0
    //     0x65a3c8: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x65a3cc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x65a3d0: mov             x1, x0
    // 0x65a3d4: ldur            x0, [fp, #-0x20]
    // 0x65a3d8: StoreField: r0->field_f = r1
    //     0x65a3d8: stur            w1, [x0, #0xf]
    // 0x65a3dc: StoreField: r0->field_13 = rZR
    //     0x65a3dc: stur            wzr, [x0, #0x13]
    // 0x65a3e0: ArrayStore: r0[0] = rZR  ; List_4
    //     0x65a3e0: stur            wzr, [x0, #0x17]
    // 0x65a3e4: ldur            x4, [fp, #-8]
    // 0x65a3e8: StoreField: r4->field_73 = r0
    //     0x65a3e8: stur            w0, [x4, #0x73]
    //     0x65a3ec: ldurb           w16, [x4, #-1]
    //     0x65a3f0: ldurb           w17, [x0, #-1]
    //     0x65a3f4: and             x16, x17, x16, lsr #2
    //     0x65a3f8: tst             x16, HEAP, lsr #32
    //     0x65a3fc: b.eq            #0x65a404
    //     0x65a400: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x65a404: LoadField: r2 = r4->field_7
    //     0x65a404: ldur            w2, [x4, #7]
    // 0x65a408: DecompressPointer r2
    //     0x65a408: add             x2, x2, HEAP, lsl #32
    // 0x65a40c: r1 = Null
    //     0x65a40c: mov             x1, NULL
    // 0x65a410: r3 = <_ModalScopeState<X0>>
    //     0x65a410: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1aaa8] TypeArguments: <_ModalScopeState<X0>>
    //     0x65a414: ldr             x3, [x3, #0xaa8]
    // 0x65a418: r30 = InstantiateTypeArgumentsStub
    //     0x65a418: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x65a41c: LoadField: r30 = r30->field_7
    //     0x65a41c: ldur            lr, [lr, #7]
    // 0x65a420: blr             lr
    // 0x65a424: mov             x1, x0
    // 0x65a428: r0 = LabeledGlobalKey()
    //     0x65a428: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0x65a42c: ldur            x2, [fp, #-8]
    // 0x65a430: StoreField: r2->field_77 = r0
    //     0x65a430: stur            w0, [x2, #0x77]
    //     0x65a434: ldurb           w16, [x2, #-1]
    //     0x65a438: ldurb           w17, [x0, #-1]
    //     0x65a43c: and             x16, x17, x16, lsr #2
    //     0x65a440: tst             x16, HEAP, lsr #32
    //     0x65a444: b.eq            #0x65a44c
    //     0x65a448: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x65a44c: r1 = <State<StatefulWidget>>
    //     0x65a44c: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0x65a450: r0 = LabeledGlobalKey()
    //     0x65a450: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0x65a454: ldur            x1, [fp, #-8]
    // 0x65a458: StoreField: r1->field_7b = r0
    //     0x65a458: stur            w0, [x1, #0x7b]
    //     0x65a45c: ldurb           w16, [x1, #-1]
    //     0x65a460: ldurb           w17, [x0, #-1]
    //     0x65a464: and             x16, x17, x16, lsr #2
    //     0x65a468: tst             x16, HEAP, lsr #32
    //     0x65a46c: b.eq            #0x65a474
    //     0x65a470: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x65a474: r0 = PageStorageBucket()
    //     0x65a474: bl              #0x65aa48  ; AllocatePageStorageBucketStub -> PageStorageBucket (size=0xc)
    // 0x65a478: ldur            x1, [fp, #-8]
    // 0x65a47c: StoreField: r1->field_7f = r0
    //     0x65a47c: stur            w0, [x1, #0x7f]
    //     0x65a480: ldurb           w16, [x1, #-1]
    //     0x65a484: ldurb           w17, [x0, #-1]
    //     0x65a488: and             x16, x17, x16, lsr #2
    //     0x65a48c: tst             x16, HEAP, lsr #32
    //     0x65a490: b.eq            #0x65a498
    //     0x65a494: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x65a498: StoreField: r1->field_4f = rZR
    //     0x65a498: stur            xzr, [x1, #0x4f]
    // 0x65a49c: ldur            x2, [fp, #-0x10]
    // 0x65a4a0: r0 = TransitionRoute()
    //     0x65a4a0: bl              #0x65a4bc  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::TransitionRoute
    // 0x65a4a4: r0 = Null
    //     0x65a4a4: mov             x0, NULL
    // 0x65a4a8: LeaveFrame
    //     0x65a4a8: mov             SP, fp
    //     0x65a4ac: ldp             fp, lr, [SP], #0x10
    // 0x65a4b0: ret
    //     0x65a4b0: ret             
    // 0x65a4b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65a4b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65a4b8: b               #0x65a338
  }
  _ willPop(/* No info */) async {
    // ** addr: 0x673664, size: 0x180
    // 0x673664: EnterFrame
    //     0x673664: stp             fp, lr, [SP, #-0x10]!
    //     0x673668: mov             fp, SP
    // 0x67366c: AllocStack(0x48)
    //     0x67366c: sub             SP, SP, #0x48
    // 0x673670: SetupParameters(ModalRoute<X0> this /* r1 => r1, fp-0x10 */)
    //     0x673670: stur            NULL, [fp, #-8]
    //     0x673674: stur            x1, [fp, #-0x10]
    // 0x673678: CheckStackOverflow
    //     0x673678: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67367c: cmp             SP, x16
    //     0x673680: b.ls            #0x6737d4
    // 0x673684: InitAsync() -> Future<RoutePopDisposition>
    //     0x673684: add             x0, PP, #0x25, lsl #12  ; [pp+0x25508] TypeArguments: <RoutePopDisposition>
    //     0x673688: ldr             x0, [x0, #0x508]
    //     0x67368c: bl              #0x661298  ; InitAsyncStub
    // 0x673690: ldur            x0, [fp, #-0x10]
    // 0x673694: LoadField: r1 = r0->field_77
    //     0x673694: ldur            w1, [x0, #0x77]
    // 0x673698: DecompressPointer r1
    //     0x673698: add             x1, x1, HEAP, lsl #32
    // 0x67369c: r0 = currentState()
    //     0x67369c: bl              #0x658c30  ; [package:flutter/src/widgets/framework.dart] GlobalKey::currentState
    // 0x6736a0: ldur            x0, [fp, #-0x10]
    // 0x6736a4: LoadField: r2 = r0->field_6f
    //     0x6736a4: ldur            w2, [x0, #0x6f]
    // 0x6736a8: DecompressPointer r2
    //     0x6736a8: add             x2, x2, HEAP, lsl #32
    // 0x6736ac: r1 = <(dynamic this) => Future<bool>>
    //     0x6736ac: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1aa98] TypeArguments: <(dynamic this) => Future<bool>>
    //     0x6736b0: ldr             x1, [x1, #0xa98]
    // 0x6736b4: r0 = _GrowableList._ofGrowableList()
    //     0x6736b4: bl              #0x60bbec  ; [dart:core] _GrowableList::_GrowableList._ofGrowableList
    // 0x6736b8: mov             x3, x0
    // 0x6736bc: stur            x3, [fp, #-0x38]
    // 0x6736c0: LoadField: r4 = r3->field_7
    //     0x6736c0: ldur            w4, [x3, #7]
    // 0x6736c4: DecompressPointer r4
    //     0x6736c4: add             x4, x4, HEAP, lsl #32
    // 0x6736c8: stur            x4, [fp, #-0x30]
    // 0x6736cc: LoadField: r0 = r3->field_b
    //     0x6736cc: ldur            w0, [x3, #0xb]
    // 0x6736d0: r5 = LoadInt32Instr(r0)
    //     0x6736d0: sbfx            x5, x0, #1, #0x1f
    // 0x6736d4: stur            x5, [fp, #-0x28]
    // 0x6736d8: r0 = 0
    //     0x6736d8: movz            x0, #0
    // 0x6736dc: CheckStackOverflow
    //     0x6736dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6736e0: cmp             SP, x16
    //     0x6736e4: b.ls            #0x6737dc
    // 0x6736e8: LoadField: r1 = r3->field_b
    //     0x6736e8: ldur            w1, [x3, #0xb]
    // 0x6736ec: r2 = LoadInt32Instr(r1)
    //     0x6736ec: sbfx            x2, x1, #1, #0x1f
    // 0x6736f0: cmp             x5, x2
    // 0x6736f4: b.ne            #0x6737b4
    // 0x6736f8: cmp             x0, x2
    // 0x6736fc: b.ge            #0x6737a8
    // 0x673700: LoadField: r1 = r3->field_f
    //     0x673700: ldur            w1, [x3, #0xf]
    // 0x673704: DecompressPointer r1
    //     0x673704: add             x1, x1, HEAP, lsl #32
    // 0x673708: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0x673708: add             x16, x1, x0, lsl #2
    //     0x67370c: ldur            w6, [x16, #0xf]
    // 0x673710: DecompressPointer r6
    //     0x673710: add             x6, x6, HEAP, lsl #32
    // 0x673714: stur            x6, [fp, #-0x20]
    // 0x673718: add             x7, x0, #1
    // 0x67371c: stur            x7, [fp, #-0x18]
    // 0x673720: cmp             w6, NULL
    // 0x673724: b.ne            #0x673758
    // 0x673728: mov             x0, x6
    // 0x67372c: mov             x2, x4
    // 0x673730: r1 = Null
    //     0x673730: mov             x1, NULL
    // 0x673734: cmp             w2, NULL
    // 0x673738: b.eq            #0x673758
    // 0x67373c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x67373c: ldur            w4, [x2, #0x17]
    // 0x673740: DecompressPointer r4
    //     0x673740: add             x4, x4, HEAP, lsl #32
    // 0x673744: r8 = X0
    //     0x673744: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x673748: LoadField: r9 = r4->field_7
    //     0x673748: ldur            x9, [x4, #7]
    // 0x67374c: r3 = Null
    //     0x67374c: add             x3, PP, #0x25, lsl #12  ; [pp+0x25510] Null
    //     0x673750: ldr             x3, [x3, #0x510]
    // 0x673754: blr             x9
    // 0x673758: ldur            x16, [fp, #-0x20]
    // 0x67375c: str             x16, [SP]
    // 0x673760: ldur            x0, [fp, #-0x20]
    // 0x673764: ClosureCall
    //     0x673764: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x673768: ldur            x2, [x0, #0x1f]
    //     0x67376c: blr             x2
    // 0x673770: mov             x1, x0
    // 0x673774: stur            x1, [fp, #-0x40]
    // 0x673778: r0 = Await()
    //     0x673778: bl              #0x661044  ; AwaitStub
    // 0x67377c: r16 = true
    //     0x67377c: add             x16, NULL, #0x20  ; true
    // 0x673780: cmp             w0, w16
    // 0x673784: b.ne            #0x67379c
    // 0x673788: ldur            x0, [fp, #-0x18]
    // 0x67378c: ldur            x4, [fp, #-0x30]
    // 0x673790: ldur            x3, [fp, #-0x38]
    // 0x673794: ldur            x5, [fp, #-0x28]
    // 0x673798: b               #0x6736dc
    // 0x67379c: r0 = Instance_RoutePopDisposition
    //     0x67379c: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1a740] Obj!RoutePopDisposition@e34061
    //     0x6737a0: ldr             x0, [x0, #0x740]
    // 0x6737a4: r0 = ReturnAsyncNotFuture()
    //     0x6737a4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x6737a8: ldur            x1, [fp, #-0x10]
    // 0x6737ac: r0 = willPop()
    //     0x6737ac: bl              #0x6737e4  ; [package:flutter/src/widgets/routes.dart] _ModalRoute&TransitionRoute&LocalHistoryRoute::willPop
    // 0x6737b0: r0 = ReturnAsync()
    //     0x6737b0: b               #0x6576a4  ; ReturnAsyncStub
    // 0x6737b4: mov             x0, x3
    // 0x6737b8: r0 = ConcurrentModificationError()
    //     0x6737b8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6737bc: mov             x1, x0
    // 0x6737c0: ldur            x0, [fp, #-0x38]
    // 0x6737c4: StoreField: r1->field_b = r0
    //     0x6737c4: stur            w0, [x1, #0xb]
    // 0x6737c8: mov             x0, x1
    // 0x6737cc: r0 = Throw()
    //     0x6737cc: bl              #0xec04b8  ; ThrowStub
    // 0x6737d0: brk             #0
    // 0x6737d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6737d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6737d8: b               #0x673684
    // 0x6737dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6737dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6737e0: b               #0x6736e8
  }
  static _ isCurrentOf(/* No info */) {
    // ** addr: 0x86006c, size: 0x60
    // 0x86006c: EnterFrame
    //     0x86006c: stp             fp, lr, [SP, #-0x10]!
    //     0x860070: mov             fp, SP
    // 0x860074: AllocStack(0x18)
    //     0x860074: sub             SP, SP, #0x18
    // 0x860078: CheckStackOverflow
    //     0x860078: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86007c: cmp             SP, x16
    //     0x860080: b.ls            #0x8600c4
    // 0x860084: r16 = <Object?>
    //     0x860084: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x860088: stp             x1, x16, [SP, #8]
    // 0x86008c: r16 = Instance__ModalRouteAspect
    //     0x86008c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23888] Obj!_ModalRouteAspect@e33c21
    //     0x860090: ldr             x16, [x16, #0x888]
    // 0x860094: str             x16, [SP]
    // 0x860098: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x860098: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x86009c: r0 = _of()
    //     0x86009c: bl              #0x8600cc  ; [package:flutter/src/widgets/routes.dart] ModalRoute::_of
    // 0x8600a0: cmp             w0, NULL
    // 0x8600a4: b.ne            #0x8600b0
    // 0x8600a8: r0 = Null
    //     0x8600a8: mov             x0, NULL
    // 0x8600ac: b               #0x8600b8
    // 0x8600b0: mov             x1, x0
    // 0x8600b4: r0 = isCurrent()
    //     0x8600b4: bl              #0x650678  ; [package:flutter/src/widgets/navigator.dart] Route::isCurrent
    // 0x8600b8: LeaveFrame
    //     0x8600b8: mov             SP, fp
    //     0x8600bc: ldp             fp, lr, [SP], #0x10
    // 0x8600c0: ret
    //     0x8600c0: ret             
    // 0x8600c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8600c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8600c8: b               #0x860084
  }
  static ModalRoute<Y0>? _of<Y0>(BuildContext, [_ModalRouteAspect?]) {
    // ** addr: 0x8600cc, size: 0xec
    // 0x8600cc: EnterFrame
    //     0x8600cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8600d0: mov             fp, SP
    // 0x8600d4: AllocStack(0x28)
    //     0x8600d4: sub             SP, SP, #0x28
    // 0x8600d8: SetupParameters(dynamic _ /* r0 */, [dynamic _ = Null /* r1 */])
    //     0x8600d8: ldur            w0, [x4, #0x13]
    //     0x8600dc: sub             x1, x0, #2
    //     0x8600e0: add             x0, fp, w1, sxtw #2
    //     0x8600e4: ldr             x0, [x0, #0x10]
    //     0x8600e8: cmp             w1, #2
    //     0x8600ec: b.lt            #0x860100
    //     0x8600f0: add             x2, fp, w1, sxtw #2
    //     0x8600f4: ldr             x2, [x2, #8]
    //     0x8600f8: mov             x1, x2
    //     0x8600fc: b               #0x860104
    //     0x860100: mov             x1, NULL
    //     0x860104: ldur            w2, [x4, #0xf]
    //     0x860108: cbnz            w2, #0x860114
    //     0x86010c: mov             x3, NULL
    //     0x860110: b               #0x860124
    //     0x860114: ldur            w3, [x4, #0x17]
    //     0x860118: add             x4, fp, w3, sxtw #2
    //     0x86011c: ldr             x4, [x4, #0x10]
    //     0x860120: mov             x3, x4
    // 0x860124: CheckStackOverflow
    //     0x860124: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x860128: cmp             SP, x16
    //     0x86012c: b.ls            #0x8601b0
    // 0x860130: cbnz            w2, #0x86013c
    // 0x860134: r2 = <Object?>
    //     0x860134: ldr             x2, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x860138: b               #0x860140
    // 0x86013c: mov             x2, x3
    // 0x860140: stur            x2, [fp, #-8]
    // 0x860144: r16 = <_ModalScopeStatus>
    //     0x860144: add             x16, PP, #0x23, lsl #12  ; [pp+0x23890] TypeArguments: <_ModalScopeStatus>
    //     0x860148: ldr             x16, [x16, #0x890]
    // 0x86014c: stp             x0, x16, [SP, #8]
    // 0x860150: str             x1, [SP]
    // 0x860154: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x860154: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x860158: r0 = inheritFrom()
    //     0x860158: bl              #0x673cc0  ; [package:flutter/src/widgets/inherited_model.dart] InheritedModel::inheritFrom
    // 0x86015c: cmp             w0, NULL
    // 0x860160: b.ne            #0x86016c
    // 0x860164: r3 = Null
    //     0x860164: mov             x3, NULL
    // 0x860168: b               #0x860178
    // 0x86016c: LoadField: r1 = r0->field_1f
    //     0x86016c: ldur            w1, [x0, #0x1f]
    // 0x860170: DecompressPointer r1
    //     0x860170: add             x1, x1, HEAP, lsl #32
    // 0x860174: mov             x3, x1
    // 0x860178: mov             x0, x3
    // 0x86017c: ldur            x1, [fp, #-8]
    // 0x860180: stur            x3, [fp, #-0x10]
    // 0x860184: r2 = Null
    //     0x860184: mov             x2, NULL
    // 0x860188: r8 = ModalRoute<Y0>?
    //     0x860188: add             x8, PP, #0x23, lsl #12  ; [pp+0x23898] Type: ModalRoute<Y0>?
    //     0x86018c: ldr             x8, [x8, #0x898]
    // 0x860190: LoadField: r9 = r8->field_7
    //     0x860190: ldur            x9, [x8, #7]
    // 0x860194: r3 = Null
    //     0x860194: add             x3, PP, #0x23, lsl #12  ; [pp+0x238a0] Null
    //     0x860198: ldr             x3, [x3, #0x8a0]
    // 0x86019c: blr             x9
    // 0x8601a0: ldur            x0, [fp, #-0x10]
    // 0x8601a4: LeaveFrame
    //     0x8601a4: mov             SP, fp
    //     0x8601a8: ldp             fp, lr, [SP], #0x10
    // 0x8601ac: ret
    //     0x8601ac: ret             
    // 0x8601b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8601b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8601b4: b               #0x860130
  }
  static _ of(/* No info */) {
    // ** addr: 0x92c598, size: 0x6c
    // 0x92c598: EnterFrame
    //     0x92c598: stp             fp, lr, [SP, #-0x10]!
    //     0x92c59c: mov             fp, SP
    // 0x92c5a0: AllocStack(0x10)
    //     0x92c5a0: sub             SP, SP, #0x10
    // 0x92c5a4: SetupParameters()
    //     0x92c5a4: ldur            w0, [x4, #0xf]
    //     0x92c5a8: cbnz            w0, #0x92c5b4
    //     0x92c5ac: mov             x1, NULL
    //     0x92c5b0: b               #0x92c5c4
    //     0x92c5b4: ldur            w1, [x4, #0x17]
    //     0x92c5b8: add             x2, fp, w1, sxtw #2
    //     0x92c5bc: ldr             x2, [x2, #0x10]
    //     0x92c5c0: mov             x1, x2
    // 0x92c5c4: CheckStackOverflow
    //     0x92c5c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92c5c8: cmp             SP, x16
    //     0x92c5cc: b.ls            #0x92c5fc
    // 0x92c5d0: cbnz            w0, #0x92c5dc
    // 0x92c5d4: r0 = <Object?>
    //     0x92c5d4: ldr             x0, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x92c5d8: b               #0x92c5e0
    // 0x92c5dc: mov             x0, x1
    // 0x92c5e0: ldr             x16, [fp, #0x10]
    // 0x92c5e4: stp             x16, x0, [SP]
    // 0x92c5e8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x92c5e8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x92c5ec: r0 = _of()
    //     0x92c5ec: bl              #0x8600cc  ; [package:flutter/src/widgets/routes.dart] ModalRoute::_of
    // 0x92c5f0: LeaveFrame
    //     0x92c5f0: mov             SP, fp
    //     0x92c5f4: ldp             fp, lr, [SP], #0x10
    // 0x92c5f8: ret
    //     0x92c5f8: ret             
    // 0x92c5fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92c5fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92c600: b               #0x92c5d0
  }
  _ registerPopEntry(/* No info */) {
    // ** addr: 0x9a7b58, size: 0x9c
    // 0x9a7b58: EnterFrame
    //     0x9a7b58: stp             fp, lr, [SP, #-0x10]!
    //     0x9a7b5c: mov             fp, SP
    // 0x9a7b60: AllocStack(0x18)
    //     0x9a7b60: sub             SP, SP, #0x18
    // 0x9a7b64: SetupParameters(ModalRoute<X0> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9a7b64: mov             x3, x1
    //     0x9a7b68: mov             x0, x2
    //     0x9a7b6c: stur            x1, [fp, #-8]
    //     0x9a7b70: stur            x2, [fp, #-0x10]
    // 0x9a7b74: CheckStackOverflow
    //     0x9a7b74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a7b78: cmp             SP, x16
    //     0x9a7b7c: b.ls            #0x9a7be0
    // 0x9a7b80: LoadField: r1 = r3->field_73
    //     0x9a7b80: ldur            w1, [x3, #0x73]
    // 0x9a7b84: DecompressPointer r1
    //     0x9a7b84: add             x1, x1, HEAP, lsl #32
    // 0x9a7b88: mov             x2, x0
    // 0x9a7b8c: r0 = add()
    //     0x9a7b8c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x9a7b90: ldur            x0, [fp, #-0x10]
    // 0x9a7b94: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x9a7b94: ldur            w3, [x0, #0x17]
    // 0x9a7b98: DecompressPointer r3
    //     0x9a7b98: add             x3, x3, HEAP, lsl #32
    // 0x9a7b9c: r16 = Sentinel
    //     0x9a7b9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a7ba0: cmp             w3, w16
    // 0x9a7ba4: b.eq            #0x9a7be8
    // 0x9a7ba8: ldur            x2, [fp, #-8]
    // 0x9a7bac: stur            x3, [fp, #-0x18]
    // 0x9a7bb0: r1 = Function '_maybeDispatchNavigationNotification@322188637':.
    //     0x9a7bb0: add             x1, PP, #0x45, lsl #12  ; [pp+0x45e28] AnonymousClosure: (0x650000), in [package:flutter/src/widgets/routes.dart] ModalRoute::_maybeDispatchNavigationNotification (0x64fdf8)
    //     0x9a7bb4: ldr             x1, [x1, #0xe28]
    // 0x9a7bb8: r0 = AllocateClosure()
    //     0x9a7bb8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a7bbc: ldur            x1, [fp, #-0x18]
    // 0x9a7bc0: mov             x2, x0
    // 0x9a7bc4: r0 = addListener()
    //     0x9a7bc4: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x9a7bc8: ldur            x1, [fp, #-8]
    // 0x9a7bcc: r0 = _maybeDispatchNavigationNotification()
    //     0x9a7bcc: bl              #0x64fdf8  ; [package:flutter/src/widgets/routes.dart] ModalRoute::_maybeDispatchNavigationNotification
    // 0x9a7bd0: r0 = Null
    //     0x9a7bd0: mov             x0, NULL
    // 0x9a7bd4: LeaveFrame
    //     0x9a7bd4: mov             SP, fp
    //     0x9a7bd8: ldp             fp, lr, [SP], #0x10
    // 0x9a7bdc: ret
    //     0x9a7bdc: ret             
    // 0x9a7be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a7be0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a7be4: b               #0x9a7b80
    // 0x9a7be8: r9 = canPopNotifier
    //     0x9a7be8: add             x9, PP, #0x1a, lsl #12  ; [pp+0x1a770] Field <<EMAIL>>: late final (offset: 0x18)
    //     0x9a7bec: ldr             x9, [x9, #0x770]
    // 0x9a7bf0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a7bf0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ unregisterPopEntry(/* No info */) {
    // ** addr: 0x9a7bf4, size: 0x9c
    // 0x9a7bf4: EnterFrame
    //     0x9a7bf4: stp             fp, lr, [SP, #-0x10]!
    //     0x9a7bf8: mov             fp, SP
    // 0x9a7bfc: AllocStack(0x18)
    //     0x9a7bfc: sub             SP, SP, #0x18
    // 0x9a7c00: SetupParameters(ModalRoute<X0> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9a7c00: mov             x3, x1
    //     0x9a7c04: mov             x0, x2
    //     0x9a7c08: stur            x1, [fp, #-8]
    //     0x9a7c0c: stur            x2, [fp, #-0x10]
    // 0x9a7c10: CheckStackOverflow
    //     0x9a7c10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a7c14: cmp             SP, x16
    //     0x9a7c18: b.ls            #0x9a7c7c
    // 0x9a7c1c: LoadField: r1 = r3->field_73
    //     0x9a7c1c: ldur            w1, [x3, #0x73]
    // 0x9a7c20: DecompressPointer r1
    //     0x9a7c20: add             x1, x1, HEAP, lsl #32
    // 0x9a7c24: mov             x2, x0
    // 0x9a7c28: r0 = remove()
    //     0x9a7c28: bl              #0xd89c04  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x9a7c2c: ldur            x0, [fp, #-0x10]
    // 0x9a7c30: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x9a7c30: ldur            w3, [x0, #0x17]
    // 0x9a7c34: DecompressPointer r3
    //     0x9a7c34: add             x3, x3, HEAP, lsl #32
    // 0x9a7c38: r16 = Sentinel
    //     0x9a7c38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a7c3c: cmp             w3, w16
    // 0x9a7c40: b.eq            #0x9a7c84
    // 0x9a7c44: ldur            x2, [fp, #-8]
    // 0x9a7c48: stur            x3, [fp, #-0x18]
    // 0x9a7c4c: r1 = Function '_maybeDispatchNavigationNotification@322188637':.
    //     0x9a7c4c: add             x1, PP, #0x45, lsl #12  ; [pp+0x45e28] AnonymousClosure: (0x650000), in [package:flutter/src/widgets/routes.dart] ModalRoute::_maybeDispatchNavigationNotification (0x64fdf8)
    //     0x9a7c50: ldr             x1, [x1, #0xe28]
    // 0x9a7c54: r0 = AllocateClosure()
    //     0x9a7c54: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a7c58: ldur            x1, [fp, #-0x18]
    // 0x9a7c5c: mov             x2, x0
    // 0x9a7c60: r0 = removeListener()
    //     0x9a7c60: bl              #0xa8a5dc  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x9a7c64: ldur            x1, [fp, #-8]
    // 0x9a7c68: r0 = _maybeDispatchNavigationNotification()
    //     0x9a7c68: bl              #0x64fdf8  ; [package:flutter/src/widgets/routes.dart] ModalRoute::_maybeDispatchNavigationNotification
    // 0x9a7c6c: r0 = Null
    //     0x9a7c6c: mov             x0, NULL
    // 0x9a7c70: LeaveFrame
    //     0x9a7c70: mov             SP, fp
    //     0x9a7c74: ldp             fp, lr, [SP], #0x10
    // 0x9a7c78: ret
    //     0x9a7c78: ret             
    // 0x9a7c7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a7c7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a7c80: b               #0x9a7c1c
    // 0x9a7c84: r9 = canPopNotifier
    //     0x9a7c84: add             x9, PP, #0x1a, lsl #12  ; [pp+0x1a770] Field <<EMAIL>>: late final (offset: 0x18)
    //     0x9a7c88: ldr             x9, [x9, #0x770]
    // 0x9a7c8c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a7c8c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ impliesAppBarDismissal(/* No info */) {
    // ** addr: 0x9e694c, size: 0x64
    // 0x9e694c: EnterFrame
    //     0x9e694c: stp             fp, lr, [SP, #-0x10]!
    //     0x9e6950: mov             fp, SP
    // 0x9e6954: AllocStack(0x8)
    //     0x9e6954: sub             SP, SP, #8
    // 0x9e6958: SetupParameters(ModalRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0x9e6958: mov             x0, x1
    //     0x9e695c: stur            x1, [fp, #-8]
    // 0x9e6960: CheckStackOverflow
    //     0x9e6960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e6964: cmp             SP, x16
    //     0x9e6968: b.ls            #0x9e69a8
    // 0x9e696c: mov             x1, x0
    // 0x9e6970: r0 = hasActiveRouteBelow()
    //     0x9e6970: bl              #0x9e8564  ; [package:flutter/src/widgets/navigator.dart] Route::hasActiveRouteBelow
    // 0x9e6974: tbnz            w0, #4, #0x9e6980
    // 0x9e6978: r0 = true
    //     0x9e6978: add             x0, NULL, #0x20  ; true
    // 0x9e697c: b               #0x9e699c
    // 0x9e6980: ldur            x1, [fp, #-8]
    // 0x9e6984: LoadField: r2 = r1->field_4f
    //     0x9e6984: ldur            x2, [x1, #0x4f]
    // 0x9e6988: cmp             x2, #0
    // 0x9e698c: r16 = true
    //     0x9e698c: add             x16, NULL, #0x20  ; true
    // 0x9e6990: r17 = false
    //     0x9e6990: add             x17, NULL, #0x30  ; false
    // 0x9e6994: csel            x1, x16, x17, gt
    // 0x9e6998: mov             x0, x1
    // 0x9e699c: LeaveFrame
    //     0x9e699c: mov             SP, fp
    //     0x9e69a0: ldp             fp, lr, [SP], #0x10
    // 0x9e69a4: ret
    //     0x9e69a4: ret             
    // 0x9e69a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e69a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e69ac: b               #0x9e696c
  }
  get _ canPop(/* No info */) {
    // ** addr: 0xa1d238, size: 0x80
    // 0xa1d238: EnterFrame
    //     0xa1d238: stp             fp, lr, [SP, #-0x10]!
    //     0xa1d23c: mov             fp, SP
    // 0xa1d240: AllocStack(0x8)
    //     0xa1d240: sub             SP, SP, #8
    // 0xa1d244: SetupParameters(ModalRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0xa1d244: mov             x0, x1
    //     0xa1d248: stur            x1, [fp, #-8]
    // 0xa1d24c: CheckStackOverflow
    //     0xa1d24c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1d250: cmp             SP, x16
    //     0xa1d254: b.ls            #0xa1d2b0
    // 0xa1d258: mov             x1, x0
    // 0xa1d25c: r0 = hasActiveRouteBelow()
    //     0xa1d25c: bl              #0x9e8564  ; [package:flutter/src/widgets/navigator.dart] Route::hasActiveRouteBelow
    // 0xa1d260: tbnz            w0, #4, #0xa1d26c
    // 0xa1d264: r0 = true
    //     0xa1d264: add             x0, NULL, #0x20  ; true
    // 0xa1d268: b               #0xa1d2a4
    // 0xa1d26c: ldur            x1, [fp, #-8]
    // 0xa1d270: LoadField: r2 = r1->field_4b
    //     0xa1d270: ldur            w2, [x1, #0x4b]
    // 0xa1d274: DecompressPointer r2
    //     0xa1d274: add             x2, x2, HEAP, lsl #32
    // 0xa1d278: cmp             w2, NULL
    // 0xa1d27c: b.eq            #0xa1d29c
    // 0xa1d280: LoadField: r1 = r2->field_b
    //     0xa1d280: ldur            w1, [x2, #0xb]
    // 0xa1d284: cbnz            w1, #0xa1d290
    // 0xa1d288: r2 = false
    //     0xa1d288: add             x2, NULL, #0x30  ; false
    // 0xa1d28c: b               #0xa1d294
    // 0xa1d290: r2 = true
    //     0xa1d290: add             x2, NULL, #0x20  ; true
    // 0xa1d294: mov             x1, x2
    // 0xa1d298: b               #0xa1d2a0
    // 0xa1d29c: r1 = false
    //     0xa1d29c: add             x1, NULL, #0x30  ; false
    // 0xa1d2a0: mov             x0, x1
    // 0xa1d2a4: LeaveFrame
    //     0xa1d2a4: mov             SP, fp
    //     0xa1d2a8: ldp             fp, lr, [SP], #0x10
    // 0xa1d2ac: ret
    //     0xa1d2ac: ret             
    // 0xa1d2b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1d2b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1d2b4: b               #0xa1d258
  }
  _ _buildFlexibleTransitions(/* No info */) {
    // ** addr: 0xa1d750, size: 0x294
    // 0xa1d750: EnterFrame
    //     0xa1d750: stp             fp, lr, [SP, #-0x10]!
    //     0xa1d754: mov             fp, SP
    // 0xa1d758: AllocStack(0x68)
    //     0xa1d758: sub             SP, SP, #0x68
    // 0xa1d75c: SetupParameters(ModalRoute<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x28 */, dynamic _ /* r3 => r2, fp-0x10 */, dynamic _ /* r5 => r3, fp-0x18 */, dynamic _ /* r6 => r6, fp-0x20 */)
    //     0xa1d75c: mov             x4, x1
    //     0xa1d760: mov             x0, x2
    //     0xa1d764: stur            x2, [fp, #-0x28]
    //     0xa1d768: mov             x2, x3
    //     0xa1d76c: stur            x3, [fp, #-0x10]
    //     0xa1d770: mov             x3, x5
    //     0xa1d774: stur            x1, [fp, #-8]
    //     0xa1d778: stur            x5, [fp, #-0x18]
    //     0xa1d77c: stur            x6, [fp, #-0x20]
    // 0xa1d780: CheckStackOverflow
    //     0xa1d780: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1d784: cmp             SP, x16
    //     0xa1d788: b.ls            #0xa1d9d0
    // 0xa1d78c: LoadField: r1 = r4->field_5f
    //     0xa1d78c: ldur            w1, [x4, #0x5f]
    // 0xa1d790: DecompressPointer r1
    //     0xa1d790: add             x1, x1, HEAP, lsl #32
    // 0xa1d794: cmp             w1, NULL
    // 0xa1d798: b.ne            #0xa1d858
    // 0xa1d79c: r1 = LoadClassIdInstr(r4)
    //     0xa1d79c: ldur            x1, [x4, #-1]
    //     0xa1d7a0: ubfx            x1, x1, #0xc, #0x14
    // 0xa1d7a4: cmp             x1, #0xa5d
    // 0xa1d7a8: b.ne            #0xa1d7dc
    // 0xa1d7ac: LoadField: r1 = r4->field_a3
    //     0xa1d7ac: ldur            w1, [x4, #0xa3]
    // 0xa1d7b0: DecompressPointer r1
    //     0xa1d7b0: add             x1, x1, HEAP, lsl #32
    // 0xa1d7b4: cmp             w1, NULL
    // 0xa1d7b8: b.eq            #0xa1d9d8
    // 0xa1d7bc: stp             x0, x1, [SP, #0x18]
    // 0xa1d7c0: stp             x3, x2, [SP, #8]
    // 0xa1d7c4: str             x6, [SP]
    // 0xa1d7c8: mov             x0, x1
    // 0xa1d7cc: ClosureCall
    //     0xa1d7cc: ldr             x4, [PP, #0xce0]  ; [pp+0xce0] List(5) [0, 0x5, 0x5, 0x5, Null]
    //     0xa1d7d0: ldur            x2, [x0, #0x1f]
    //     0xa1d7d4: blr             x2
    // 0xa1d7d8: b               #0xa1d84c
    // 0xa1d7dc: cmp             x1, #0xa5e
    // 0xa1d7e0: b.ne            #0xa1d7ec
    // 0xa1d7e4: mov             x0, x6
    // 0xa1d7e8: b               #0xa1d84c
    // 0xa1d7ec: cmp             x1, #0xa62
    // 0xa1d7f0: b.ne            #0xa1d814
    // 0xa1d7f4: LoadField: r0 = r4->field_7
    //     0xa1d7f4: ldur            w0, [x4, #7]
    // 0xa1d7f8: DecompressPointer r0
    //     0xa1d7f8: add             x0, x0, HEAP, lsl #32
    // 0xa1d7fc: stp             x4, x0, [SP, #0x18]
    // 0xa1d800: stp             x3, x2, [SP, #8]
    // 0xa1d804: str             x6, [SP]
    // 0xa1d808: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0xa1d808: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0xa1d80c: r0 = buildPageTransitions()
    //     0xa1d80c: bl              #0xa1da60  ; [package:get/get_navigation/src/routes/get_transition_mixin.dart] GetPageRouteTransitionMixin::buildPageTransitions
    // 0xa1d810: b               #0xa1d84c
    // 0xa1d814: mov             x1, x0
    // 0xa1d818: r0 = of()
    //     0xa1d818: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1d81c: ldur            x2, [fp, #-8]
    // 0xa1d820: LoadField: r0 = r2->field_7
    //     0xa1d820: ldur            w0, [x2, #7]
    // 0xa1d824: DecompressPointer r0
    //     0xa1d824: add             x0, x0, HEAP, lsl #32
    // 0xa1d828: r16 = Instance_PageTransitionsTheme
    //     0xa1d828: ldr             x16, [PP, #0x5108]  ; [pp+0x5108] Obj!PageTransitionsTheme@e1c7f1
    // 0xa1d82c: stp             x16, x0, [SP, #0x20]
    // 0xa1d830: ldur            x16, [fp, #-0x10]
    // 0xa1d834: stp             x16, x2, [SP, #0x10]
    // 0xa1d838: ldur            x16, [fp, #-0x18]
    // 0xa1d83c: ldur            lr, [fp, #-0x20]
    // 0xa1d840: stp             lr, x16, [SP]
    // 0xa1d844: r4 = const [0x1, 0x5, 0x5, 0x5, null]
    //     0xa1d844: ldr             x4, [PP, #0x1800]  ; [pp+0x1800] List(5) [0x1, 0x5, 0x5, 0x5, Null]
    // 0xa1d848: r0 = buildTransitions()
    //     0xa1d848: bl              #0xa1d9e4  ; [package:flutter/src/material/page_transitions_theme.dart] PageTransitionsTheme::buildTransitions
    // 0xa1d84c: LeaveFrame
    //     0xa1d84c: mov             SP, fp
    //     0xa1d850: ldp             fp, lr, [SP], #0x10
    // 0xa1d854: ret
    //     0xa1d854: ret             
    // 0xa1d858: mov             x2, x4
    // 0xa1d85c: r1 = <double>
    //     0xa1d85c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xa1d860: r0 = ProxyAnimation()
    //     0xa1d860: bl              #0x65aa3c  ; AllocateProxyAnimationStub -> ProxyAnimation (size=0x28)
    // 0xa1d864: mov             x1, x0
    // 0xa1d868: stur            x0, [fp, #-0x30]
    // 0xa1d86c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa1d86c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa1d870: r0 = ProxyAnimation()
    //     0xa1d870: bl              #0x65a83c  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::ProxyAnimation
    // 0xa1d874: ldur            x1, [fp, #-8]
    // 0xa1d878: r2 = LoadClassIdInstr(r1)
    //     0xa1d878: ldur            x2, [x1, #-1]
    //     0xa1d87c: ubfx            x2, x2, #0xc, #0x14
    // 0xa1d880: stur            x2, [fp, #-0x38]
    // 0xa1d884: cmp             x2, #0xa5d
    // 0xa1d888: b.ne            #0xa1d8d0
    // 0xa1d88c: LoadField: r0 = r1->field_a3
    //     0xa1d88c: ldur            w0, [x1, #0xa3]
    // 0xa1d890: DecompressPointer r0
    //     0xa1d890: add             x0, x0, HEAP, lsl #32
    // 0xa1d894: cmp             w0, NULL
    // 0xa1d898: b.eq            #0xa1d9dc
    // 0xa1d89c: ldur            x16, [fp, #-0x28]
    // 0xa1d8a0: stp             x16, x0, [SP, #0x18]
    // 0xa1d8a4: ldur            x16, [fp, #-0x10]
    // 0xa1d8a8: ldur            lr, [fp, #-0x30]
    // 0xa1d8ac: stp             lr, x16, [SP, #8]
    // 0xa1d8b0: ldur            x16, [fp, #-0x20]
    // 0xa1d8b4: str             x16, [SP]
    // 0xa1d8b8: ClosureCall
    //     0xa1d8b8: ldr             x4, [PP, #0xce0]  ; [pp+0xce0] List(5) [0, 0x5, 0x5, 0x5, Null]
    //     0xa1d8bc: ldur            x2, [x0, #0x1f]
    //     0xa1d8c0: blr             x2
    // 0xa1d8c4: mov             x4, x0
    // 0xa1d8c8: ldur            x1, [fp, #-0x38]
    // 0xa1d8cc: b               #0xa1d96c
    // 0xa1d8d0: mov             x0, x2
    // 0xa1d8d4: cmp             x0, #0xa5e
    // 0xa1d8d8: b.ne            #0xa1d8e8
    // 0xa1d8dc: ldur            x4, [fp, #-0x20]
    // 0xa1d8e0: mov             x1, x0
    // 0xa1d8e4: b               #0xa1d96c
    // 0xa1d8e8: cmp             x0, #0xa62
    // 0xa1d8ec: b.ne            #0xa1d928
    // 0xa1d8f0: ldur            x1, [fp, #-8]
    // 0xa1d8f4: LoadField: r2 = r1->field_7
    //     0xa1d8f4: ldur            w2, [x1, #7]
    // 0xa1d8f8: DecompressPointer r2
    //     0xa1d8f8: add             x2, x2, HEAP, lsl #32
    // 0xa1d8fc: stp             x1, x2, [SP, #0x18]
    // 0xa1d900: ldur            x16, [fp, #-0x10]
    // 0xa1d904: ldur            lr, [fp, #-0x30]
    // 0xa1d908: stp             lr, x16, [SP, #8]
    // 0xa1d90c: ldur            x16, [fp, #-0x20]
    // 0xa1d910: str             x16, [SP]
    // 0xa1d914: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0xa1d914: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0xa1d918: r0 = buildPageTransitions()
    //     0xa1d918: bl              #0xa1da60  ; [package:get/get_navigation/src/routes/get_transition_mixin.dart] GetPageRouteTransitionMixin::buildPageTransitions
    // 0xa1d91c: mov             x4, x0
    // 0xa1d920: ldur            x1, [fp, #-0x38]
    // 0xa1d924: b               #0xa1d96c
    // 0xa1d928: ldur            x0, [fp, #-8]
    // 0xa1d92c: ldur            x1, [fp, #-0x28]
    // 0xa1d930: r0 = of()
    //     0xa1d930: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1d934: ldur            x0, [fp, #-8]
    // 0xa1d938: LoadField: r1 = r0->field_7
    //     0xa1d938: ldur            w1, [x0, #7]
    // 0xa1d93c: DecompressPointer r1
    //     0xa1d93c: add             x1, x1, HEAP, lsl #32
    // 0xa1d940: r16 = Instance_PageTransitionsTheme
    //     0xa1d940: ldr             x16, [PP, #0x5108]  ; [pp+0x5108] Obj!PageTransitionsTheme@e1c7f1
    // 0xa1d944: stp             x16, x1, [SP, #0x20]
    // 0xa1d948: ldur            x16, [fp, #-0x10]
    // 0xa1d94c: stp             x16, x0, [SP, #0x10]
    // 0xa1d950: ldur            x16, [fp, #-0x30]
    // 0xa1d954: ldur            lr, [fp, #-0x20]
    // 0xa1d958: stp             lr, x16, [SP]
    // 0xa1d95c: r4 = const [0x1, 0x5, 0x5, 0x5, null]
    //     0xa1d95c: ldr             x4, [PP, #0x1800]  ; [pp+0x1800] List(5) [0x1, 0x5, 0x5, 0x5, Null]
    // 0xa1d960: r0 = buildTransitions()
    //     0xa1d960: bl              #0xa1d9e4  ; [package:flutter/src/material/page_transitions_theme.dart] PageTransitionsTheme::buildTransitions
    // 0xa1d964: mov             x4, x0
    // 0xa1d968: ldur            x1, [fp, #-0x38]
    // 0xa1d96c: ldur            x0, [fp, #-8]
    // 0xa1d970: stur            x4, [fp, #-0x20]
    // 0xa1d974: LoadField: r2 = r0->field_5f
    //     0xa1d974: ldur            w2, [x0, #0x5f]
    // 0xa1d978: DecompressPointer r2
    //     0xa1d978: add             x2, x2, HEAP, lsl #32
    // 0xa1d97c: cmp             w2, NULL
    // 0xa1d980: b.eq            #0xa1d9e0
    // 0xa1d984: sub             x16, x1, #0xa5d
    // 0xa1d988: cmp             x16, #1
    // 0xa1d98c: b.hi            #0xa1d998
    // 0xa1d990: r5 = false
    //     0xa1d990: add             x5, NULL, #0x30  ; false
    // 0xa1d994: b               #0xa1d9a4
    // 0xa1d998: LoadField: r1 = r0->field_93
    //     0xa1d998: ldur            w1, [x0, #0x93]
    // 0xa1d99c: DecompressPointer r1
    //     0xa1d99c: add             x1, x1, HEAP, lsl #32
    // 0xa1d9a0: mov             x5, x1
    // 0xa1d9a4: ldur            x1, [fp, #-0x28]
    // 0xa1d9a8: ldur            x2, [fp, #-0x10]
    // 0xa1d9ac: ldur            x3, [fp, #-0x18]
    // 0xa1d9b0: mov             x6, x4
    // 0xa1d9b4: r0 = _delegatedTransition()
    //     0xa1d9b4: bl              #0x65460c  ; [package:flutter/src/material/page.dart] MaterialRouteTransitionMixin::_delegatedTransition
    // 0xa1d9b8: cmp             w0, NULL
    // 0xa1d9bc: b.ne            #0xa1d9c4
    // 0xa1d9c0: ldur            x0, [fp, #-0x20]
    // 0xa1d9c4: LeaveFrame
    //     0xa1d9c4: mov             SP, fp
    //     0xa1d9c8: ldp             fp, lr, [SP], #0x10
    // 0xa1d9cc: ret
    //     0xa1d9cc: ret             
    // 0xa1d9d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1d9d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1d9d4: b               #0xa1d78c
    // 0xa1d9d8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa1d9d8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xa1d9dc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa1d9dc: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xa1d9e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1d9e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangePrevious(/* No info */) {
    // ** addr: 0xcec8cc, size: 0x30
    // 0xcec8cc: EnterFrame
    //     0xcec8cc: stp             fp, lr, [SP, #-0x10]!
    //     0xcec8d0: mov             fp, SP
    // 0xcec8d4: CheckStackOverflow
    //     0xcec8d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcec8d8: cmp             SP, x16
    //     0xcec8dc: b.ls            #0xcec8f4
    // 0xcec8e0: r0 = changedInternalState()
    //     0xcec8e0: bl              #0x6507d0  ; [package:flutter/src/widgets/routes.dart] ModalRoute::changedInternalState
    // 0xcec8e4: r0 = Null
    //     0xcec8e4: mov             x0, NULL
    // 0xcec8e8: LeaveFrame
    //     0xcec8e8: mov             SP, fp
    //     0xcec8ec: ldp             fp, lr, [SP], #0x10
    // 0xcec8f0: ret
    //     0xcec8f0: ret             
    // 0xcec8f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcec8f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcec8f8: b               #0xcec8e0
  }
  get _ popGestureEnabled(/* No info */) {
    // ** addr: 0xd3b5b0, size: 0x160
    // 0xd3b5b0: EnterFrame
    //     0xd3b5b0: stp             fp, lr, [SP, #-0x10]!
    //     0xd3b5b4: mov             fp, SP
    // 0xd3b5b8: AllocStack(0x8)
    //     0xd3b5b8: sub             SP, SP, #8
    // 0xd3b5bc: SetupParameters(ModalRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0xd3b5bc: mov             x0, x1
    //     0xd3b5c0: stur            x1, [fp, #-8]
    // 0xd3b5c4: CheckStackOverflow
    //     0xd3b5c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3b5c8: cmp             SP, x16
    //     0xd3b5cc: b.ls            #0xd3b6fc
    // 0xd3b5d0: mov             x1, x0
    // 0xd3b5d4: r0 = isFirst()
    //     0xd3b5d4: bl              #0x650398  ; [package:flutter/src/widgets/navigator.dart] Route::isFirst
    // 0xd3b5d8: tbnz            w0, #4, #0xd3b5ec
    // 0xd3b5dc: r0 = false
    //     0xd3b5dc: add             x0, NULL, #0x30  ; false
    // 0xd3b5e0: LeaveFrame
    //     0xd3b5e0: mov             SP, fp
    //     0xd3b5e4: ldp             fp, lr, [SP], #0x10
    // 0xd3b5e8: ret
    //     0xd3b5e8: ret             
    // 0xd3b5ec: ldur            x0, [fp, #-8]
    // 0xd3b5f0: LoadField: r1 = r0->field_4b
    //     0xd3b5f0: ldur            w1, [x0, #0x4b]
    // 0xd3b5f4: DecompressPointer r1
    //     0xd3b5f4: add             x1, x1, HEAP, lsl #32
    // 0xd3b5f8: cmp             w1, NULL
    // 0xd3b5fc: b.eq            #0xd3b618
    // 0xd3b600: LoadField: r2 = r1->field_b
    //     0xd3b600: ldur            w2, [x1, #0xb]
    // 0xd3b604: cbz             w2, #0xd3b618
    // 0xd3b608: r0 = false
    //     0xd3b608: add             x0, NULL, #0x30  ; false
    // 0xd3b60c: LeaveFrame
    //     0xd3b60c: mov             SP, fp
    //     0xd3b610: ldp             fp, lr, [SP], #0x10
    // 0xd3b614: ret
    //     0xd3b614: ret             
    // 0xd3b618: mov             x1, x0
    // 0xd3b61c: r0 = hasScopedWillPopCallback()
    //     0xd3b61c: bl              #0xd3b710  ; [package:flutter/src/widgets/routes.dart] ModalRoute::hasScopedWillPopCallback
    // 0xd3b620: tbz             w0, #4, #0xd3b63c
    // 0xd3b624: ldur            x1, [fp, #-8]
    // 0xd3b628: r0 = popDisposition()
    //     0xd3b628: bl              #0x650194  ; [package:flutter/src/widgets/routes.dart] ModalRoute::popDisposition
    // 0xd3b62c: r16 = Instance_RoutePopDisposition
    //     0xd3b62c: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a740] Obj!RoutePopDisposition@e34061
    //     0xd3b630: ldr             x16, [x16, #0x740]
    // 0xd3b634: cmp             w0, w16
    // 0xd3b638: b.ne            #0xd3b64c
    // 0xd3b63c: r0 = false
    //     0xd3b63c: add             x0, NULL, #0x30  ; false
    // 0xd3b640: LeaveFrame
    //     0xd3b640: mov             SP, fp
    //     0xd3b644: ldp             fp, lr, [SP], #0x10
    // 0xd3b648: ret
    //     0xd3b648: ret             
    // 0xd3b64c: ldur            x0, [fp, #-8]
    // 0xd3b650: LoadField: r1 = r0->field_67
    //     0xd3b650: ldur            w1, [x0, #0x67]
    // 0xd3b654: DecompressPointer r1
    //     0xd3b654: add             x1, x1, HEAP, lsl #32
    // 0xd3b658: cmp             w1, NULL
    // 0xd3b65c: b.eq            #0xd3b704
    // 0xd3b660: r0 = status()
    //     0xd3b660: bl              #0xd371b8  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::status
    // 0xd3b664: r16 = Instance_AnimationStatus
    //     0xd3b664: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0xd3b668: cmp             w0, w16
    // 0xd3b66c: b.eq            #0xd3b680
    // 0xd3b670: r0 = false
    //     0xd3b670: add             x0, NULL, #0x30  ; false
    // 0xd3b674: LeaveFrame
    //     0xd3b674: mov             SP, fp
    //     0xd3b678: ldp             fp, lr, [SP], #0x10
    // 0xd3b67c: ret
    //     0xd3b67c: ret             
    // 0xd3b680: ldur            x0, [fp, #-8]
    // 0xd3b684: LoadField: r1 = r0->field_6b
    //     0xd3b684: ldur            w1, [x0, #0x6b]
    // 0xd3b688: DecompressPointer r1
    //     0xd3b688: add             x1, x1, HEAP, lsl #32
    // 0xd3b68c: cmp             w1, NULL
    // 0xd3b690: b.eq            #0xd3b708
    // 0xd3b694: r0 = status()
    //     0xd3b694: bl              #0xd371b8  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::status
    // 0xd3b698: r16 = Instance_AnimationStatus
    //     0xd3b698: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0xd3b69c: cmp             w0, w16
    // 0xd3b6a0: b.eq            #0xd3b6b4
    // 0xd3b6a4: r0 = false
    //     0xd3b6a4: add             x0, NULL, #0x30  ; false
    // 0xd3b6a8: LeaveFrame
    //     0xd3b6a8: mov             SP, fp
    //     0xd3b6ac: ldp             fp, lr, [SP], #0x10
    // 0xd3b6b0: ret
    //     0xd3b6b0: ret             
    // 0xd3b6b4: ldur            x1, [fp, #-8]
    // 0xd3b6b8: LoadField: r2 = r1->field_f
    //     0xd3b6b8: ldur            w2, [x1, #0xf]
    // 0xd3b6bc: DecompressPointer r2
    //     0xd3b6bc: add             x2, x2, HEAP, lsl #32
    // 0xd3b6c0: cmp             w2, NULL
    // 0xd3b6c4: b.eq            #0xd3b70c
    // 0xd3b6c8: LoadField: r1 = r2->field_67
    //     0xd3b6c8: ldur            w1, [x2, #0x67]
    // 0xd3b6cc: DecompressPointer r1
    //     0xd3b6cc: add             x1, x1, HEAP, lsl #32
    // 0xd3b6d0: LoadField: r2 = r1->field_27
    //     0xd3b6d0: ldur            w2, [x1, #0x27]
    // 0xd3b6d4: DecompressPointer r2
    //     0xd3b6d4: add             x2, x2, HEAP, lsl #32
    // 0xd3b6d8: tbnz            w2, #4, #0xd3b6ec
    // 0xd3b6dc: r0 = false
    //     0xd3b6dc: add             x0, NULL, #0x30  ; false
    // 0xd3b6e0: LeaveFrame
    //     0xd3b6e0: mov             SP, fp
    //     0xd3b6e4: ldp             fp, lr, [SP], #0x10
    // 0xd3b6e8: ret
    //     0xd3b6e8: ret             
    // 0xd3b6ec: r0 = true
    //     0xd3b6ec: add             x0, NULL, #0x20  ; true
    // 0xd3b6f0: LeaveFrame
    //     0xd3b6f0: mov             SP, fp
    //     0xd3b6f4: ldp             fp, lr, [SP], #0x10
    // 0xd3b6f8: ret
    //     0xd3b6f8: ret             
    // 0xd3b6fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3b6fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd3b700: b               #0xd3b5d0
    // 0xd3b704: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd3b704: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd3b708: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd3b708: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd3b70c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd3b70c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ hasScopedWillPopCallback(/* No info */) {
    // ** addr: 0xd3b710, size: 0x20
    // 0xd3b710: LoadField: r2 = r1->field_6f
    //     0xd3b710: ldur            w2, [x1, #0x6f]
    // 0xd3b714: DecompressPointer r2
    //     0xd3b714: add             x2, x2, HEAP, lsl #32
    // 0xd3b718: LoadField: r1 = r2->field_b
    //     0xd3b718: ldur            w1, [x2, #0xb]
    // 0xd3b71c: cbnz            w1, #0xd3b728
    // 0xd3b720: r0 = false
    //     0xd3b720: add             x0, NULL, #0x30  ; false
    // 0xd3b724: b               #0xd3b72c
    // 0xd3b728: r0 = true
    //     0xd3b728: add             x0, NULL, #0x20  ; true
    // 0xd3b72c: ret
    //     0xd3b72c: ret             
  }
  _ install(/* No info */) {
    // ** addr: 0xdb0118, size: 0xe4
    // 0xdb0118: EnterFrame
    //     0xdb0118: stp             fp, lr, [SP, #-0x10]!
    //     0xdb011c: mov             fp, SP
    // 0xdb0120: AllocStack(0x20)
    //     0xdb0120: sub             SP, SP, #0x20
    // 0xdb0124: SetupParameters(ModalRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0xdb0124: mov             x0, x1
    //     0xdb0128: stur            x1, [fp, #-8]
    // 0xdb012c: CheckStackOverflow
    //     0xdb012c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb0130: cmp             SP, x16
    //     0xdb0134: b.ls            #0xdb01f4
    // 0xdb0138: mov             x1, x0
    // 0xdb013c: r0 = install()
    //     0xdb013c: bl              #0xdb01fc  ; [package:flutter/src/widgets/routes.dart] TransitionRoute::install
    // 0xdb0140: ldur            x0, [fp, #-8]
    // 0xdb0144: LoadField: r2 = r0->field_33
    //     0xdb0144: ldur            w2, [x0, #0x33]
    // 0xdb0148: DecompressPointer r2
    //     0xdb0148: add             x2, x2, HEAP, lsl #32
    // 0xdb014c: stur            x2, [fp, #-0x10]
    // 0xdb0150: r1 = <double>
    //     0xdb0150: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xdb0154: r0 = ProxyAnimation()
    //     0xdb0154: bl              #0x65aa3c  ; AllocateProxyAnimationStub -> ProxyAnimation (size=0x28)
    // 0xdb0158: stur            x0, [fp, #-0x18]
    // 0xdb015c: ldur            x16, [fp, #-0x10]
    // 0xdb0160: str             x16, [SP]
    // 0xdb0164: mov             x1, x0
    // 0xdb0168: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xdb0168: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xdb016c: r0 = ProxyAnimation()
    //     0xdb016c: bl              #0x65a83c  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::ProxyAnimation
    // 0xdb0170: ldur            x0, [fp, #-0x18]
    // 0xdb0174: ldur            x2, [fp, #-8]
    // 0xdb0178: StoreField: r2->field_67 = r0
    //     0xdb0178: stur            w0, [x2, #0x67]
    //     0xdb017c: ldurb           w16, [x2, #-1]
    //     0xdb0180: ldurb           w17, [x0, #-1]
    //     0xdb0184: and             x16, x17, x16, lsr #2
    //     0xdb0188: tst             x16, HEAP, lsr #32
    //     0xdb018c: b.eq            #0xdb0194
    //     0xdb0190: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xdb0194: LoadField: r0 = r2->field_3b
    //     0xdb0194: ldur            w0, [x2, #0x3b]
    // 0xdb0198: DecompressPointer r0
    //     0xdb0198: add             x0, x0, HEAP, lsl #32
    // 0xdb019c: stur            x0, [fp, #-0x10]
    // 0xdb01a0: r1 = <double>
    //     0xdb01a0: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xdb01a4: r0 = ProxyAnimation()
    //     0xdb01a4: bl              #0x65aa3c  ; AllocateProxyAnimationStub -> ProxyAnimation (size=0x28)
    // 0xdb01a8: stur            x0, [fp, #-0x18]
    // 0xdb01ac: ldur            x16, [fp, #-0x10]
    // 0xdb01b0: str             x16, [SP]
    // 0xdb01b4: mov             x1, x0
    // 0xdb01b8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xdb01b8: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xdb01bc: r0 = ProxyAnimation()
    //     0xdb01bc: bl              #0x65a83c  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::ProxyAnimation
    // 0xdb01c0: ldur            x0, [fp, #-0x18]
    // 0xdb01c4: ldur            x1, [fp, #-8]
    // 0xdb01c8: StoreField: r1->field_6b = r0
    //     0xdb01c8: stur            w0, [x1, #0x6b]
    //     0xdb01cc: ldurb           w16, [x1, #-1]
    //     0xdb01d0: ldurb           w17, [x0, #-1]
    //     0xdb01d4: and             x16, x17, x16, lsr #2
    //     0xdb01d8: tst             x16, HEAP, lsr #32
    //     0xdb01dc: b.eq            #0xdb01e4
    //     0xdb01e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xdb01e4: r0 = Null
    //     0xdb01e4: mov             x0, NULL
    // 0xdb01e8: LeaveFrame
    //     0xdb01e8: mov             SP, fp
    //     0xdb01ec: ldp             fp, lr, [SP], #0x10
    // 0xdb01f0: ret
    //     0xdb01f0: ret             
    // 0xdb01f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb01f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb01f8: b               #0xdb0138
  }
  _ createOverlayEntries(/* No info */) {
    // ** addr: 0xdb05b8, size: 0x160
    // 0xdb05b8: EnterFrame
    //     0xdb05b8: stp             fp, lr, [SP, #-0x10]!
    //     0xdb05bc: mov             fp, SP
    // 0xdb05c0: AllocStack(0x38)
    //     0xdb05c0: sub             SP, SP, #0x38
    // 0xdb05c4: SetupParameters(ModalRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0xdb05c4: mov             x0, x1
    //     0xdb05c8: stur            x1, [fp, #-8]
    // 0xdb05cc: CheckStackOverflow
    //     0xdb05cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb05d0: cmp             SP, x16
    //     0xdb05d4: b.ls            #0xdb0710
    // 0xdb05d8: mov             x2, x0
    // 0xdb05dc: r1 = Function '_buildModalBarrier@322188637':.
    //     0xdb05dc: add             x1, PP, #0x22, lsl #12  ; [pp+0x221f8] AnonymousClosure: (0xdb082c), in [package:flutter/src/widgets/routes.dart] ModalRoute::_buildModalBarrier (0xdb0868)
    //     0xdb05e0: ldr             x1, [x1, #0x1f8]
    // 0xdb05e4: r0 = AllocateClosure()
    //     0xdb05e4: bl              #0xec1630  ; AllocateClosureStub
    // 0xdb05e8: stur            x0, [fp, #-0x10]
    // 0xdb05ec: r0 = OverlayEntry()
    //     0xdb05ec: bl              #0x6a5798  ; AllocateOverlayEntryStub -> OverlayEntry (size=0x28)
    // 0xdb05f0: mov             x1, x0
    // 0xdb05f4: ldur            x2, [fp, #-0x10]
    // 0xdb05f8: stur            x0, [fp, #-0x10]
    // 0xdb05fc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xdb05fc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xdb0600: r0 = OverlayEntry()
    //     0xdb0600: bl              #0x6a55c8  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::OverlayEntry
    // 0xdb0604: ldur            x0, [fp, #-0x10]
    // 0xdb0608: ldur            x3, [fp, #-8]
    // 0xdb060c: StoreField: r3->field_83 = r0
    //     0xdb060c: stur            w0, [x3, #0x83]
    //     0xdb0610: ldurb           w16, [x3, #-1]
    //     0xdb0614: ldurb           w17, [x0, #-1]
    //     0xdb0618: and             x16, x17, x16, lsr #2
    //     0xdb061c: tst             x16, HEAP, lsr #32
    //     0xdb0620: b.eq            #0xdb0628
    //     0xdb0624: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xdb0628: r0 = LoadClassIdInstr(r3)
    //     0xdb0628: ldur            x0, [x3, #-1]
    //     0xdb062c: ubfx            x0, x0, #0xc, #0x14
    // 0xdb0630: sub             x16, x0, #0xa5d
    // 0xdb0634: cmp             x16, #1
    // 0xdb0638: b.hi            #0xdb0644
    // 0xdb063c: r4 = false
    //     0xdb063c: add             x4, NULL, #0x30  ; false
    // 0xdb0640: b               #0xdb0660
    // 0xdb0644: cmp             x0, #0xa62
    // 0xdb0648: b.ne            #0xdb065c
    // 0xdb064c: LoadField: r0 = r3->field_bb
    //     0xdb064c: ldur            w0, [x3, #0xbb]
    // 0xdb0650: DecompressPointer r0
    //     0xdb0650: add             x0, x0, HEAP, lsl #32
    // 0xdb0654: mov             x4, x0
    // 0xdb0658: b               #0xdb0660
    // 0xdb065c: r4 = true
    //     0xdb065c: add             x4, NULL, #0x20  ; true
    // 0xdb0660: ldur            x0, [fp, #-0x10]
    // 0xdb0664: mov             x2, x3
    // 0xdb0668: stur            x4, [fp, #-0x18]
    // 0xdb066c: r1 = Function '_buildModalScope@322188637':.
    //     0xdb066c: add             x1, PP, #0x22, lsl #12  ; [pp+0x22200] AnonymousClosure: (0xdb0718), in [package:flutter/src/widgets/routes.dart] ModalRoute::_buildModalScope (0xdb0754)
    //     0xdb0670: ldr             x1, [x1, #0x200]
    // 0xdb0674: r0 = AllocateClosure()
    //     0xdb0674: bl              #0xec1630  ; AllocateClosureStub
    // 0xdb0678: stur            x0, [fp, #-0x20]
    // 0xdb067c: r0 = OverlayEntry()
    //     0xdb067c: bl              #0x6a5798  ; AllocateOverlayEntryStub -> OverlayEntry (size=0x28)
    // 0xdb0680: stur            x0, [fp, #-0x28]
    // 0xdb0684: r16 = true
    //     0xdb0684: add             x16, NULL, #0x20  ; true
    // 0xdb0688: ldur            lr, [fp, #-0x18]
    // 0xdb068c: stp             lr, x16, [SP]
    // 0xdb0690: mov             x1, x0
    // 0xdb0694: ldur            x2, [fp, #-0x20]
    // 0xdb0698: r4 = const [0, 0x4, 0x2, 0x2, canSizeOverlay, 0x3, maintainState, 0x2, null]
    //     0xdb0698: add             x4, PP, #0x22, lsl #12  ; [pp+0x22208] List(9) [0, 0x4, 0x2, 0x2, "canSizeOverlay", 0x3, "maintainState", 0x2, Null]
    //     0xdb069c: ldr             x4, [x4, #0x208]
    // 0xdb06a0: r0 = OverlayEntry()
    //     0xdb06a0: bl              #0x6a55c8  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::OverlayEntry
    // 0xdb06a4: ldur            x0, [fp, #-0x28]
    // 0xdb06a8: ldur            x1, [fp, #-8]
    // 0xdb06ac: StoreField: r1->field_8b = r0
    //     0xdb06ac: stur            w0, [x1, #0x8b]
    //     0xdb06b0: ldurb           w16, [x1, #-1]
    //     0xdb06b4: ldurb           w17, [x0, #-1]
    //     0xdb06b8: and             x16, x17, x16, lsr #2
    //     0xdb06bc: tst             x16, HEAP, lsr #32
    //     0xdb06c0: b.eq            #0xdb06c8
    //     0xdb06c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xdb06c8: r1 = Null
    //     0xdb06c8: mov             x1, NULL
    // 0xdb06cc: r2 = 4
    //     0xdb06cc: movz            x2, #0x4
    // 0xdb06d0: r0 = AllocateArray()
    //     0xdb06d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xdb06d4: mov             x2, x0
    // 0xdb06d8: ldur            x0, [fp, #-0x10]
    // 0xdb06dc: stur            x2, [fp, #-8]
    // 0xdb06e0: StoreField: r2->field_f = r0
    //     0xdb06e0: stur            w0, [x2, #0xf]
    // 0xdb06e4: ldur            x0, [fp, #-0x28]
    // 0xdb06e8: StoreField: r2->field_13 = r0
    //     0xdb06e8: stur            w0, [x2, #0x13]
    // 0xdb06ec: r1 = <OverlayEntry>
    //     0xdb06ec: ldr             x1, [PP, #0x6c18]  ; [pp+0x6c18] TypeArguments: <OverlayEntry>
    // 0xdb06f0: r0 = AllocateGrowableArray()
    //     0xdb06f0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xdb06f4: ldur            x1, [fp, #-8]
    // 0xdb06f8: StoreField: r0->field_f = r1
    //     0xdb06f8: stur            w1, [x0, #0xf]
    // 0xdb06fc: r1 = 4
    //     0xdb06fc: movz            x1, #0x4
    // 0xdb0700: StoreField: r0->field_b = r1
    //     0xdb0700: stur            w1, [x0, #0xb]
    // 0xdb0704: LeaveFrame
    //     0xdb0704: mov             SP, fp
    //     0xdb0708: ldp             fp, lr, [SP], #0x10
    // 0xdb070c: ret
    //     0xdb070c: ret             
    // 0xdb0710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb0710: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb0714: b               #0xdb05d8
  }
  [closure] Widget _buildModalScope(dynamic, BuildContext) {
    // ** addr: 0xdb0718, size: 0x3c
    // 0xdb0718: EnterFrame
    //     0xdb0718: stp             fp, lr, [SP, #-0x10]!
    //     0xdb071c: mov             fp, SP
    // 0xdb0720: ldr             x0, [fp, #0x18]
    // 0xdb0724: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xdb0724: ldur            w1, [x0, #0x17]
    // 0xdb0728: DecompressPointer r1
    //     0xdb0728: add             x1, x1, HEAP, lsl #32
    // 0xdb072c: CheckStackOverflow
    //     0xdb072c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb0730: cmp             SP, x16
    //     0xdb0734: b.ls            #0xdb074c
    // 0xdb0738: ldr             x2, [fp, #0x10]
    // 0xdb073c: r0 = _buildModalScope()
    //     0xdb073c: bl              #0xdb0754  ; [package:flutter/src/widgets/routes.dart] ModalRoute::_buildModalScope
    // 0xdb0740: LeaveFrame
    //     0xdb0740: mov             SP, fp
    //     0xdb0744: ldp             fp, lr, [SP], #0x10
    // 0xdb0748: ret
    //     0xdb0748: ret             
    // 0xdb074c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb074c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb0750: b               #0xdb0738
  }
  _ _buildModalScope(/* No info */) {
    // ** addr: 0xdb0754, size: 0xcc
    // 0xdb0754: EnterFrame
    //     0xdb0754: stp             fp, lr, [SP, #-0x10]!
    //     0xdb0758: mov             fp, SP
    // 0xdb075c: AllocStack(0x28)
    //     0xdb075c: sub             SP, SP, #0x28
    // 0xdb0760: SetupParameters(ModalRoute<X0> this /* r1 => r0, fp-0x10 */)
    //     0xdb0760: mov             x0, x1
    //     0xdb0764: stur            x1, [fp, #-0x10]
    // 0xdb0768: CheckStackOverflow
    //     0xdb0768: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb076c: cmp             SP, x16
    //     0xdb0770: b.ls            #0xdb0818
    // 0xdb0774: LoadField: r1 = r0->field_87
    //     0xdb0774: ldur            w1, [x0, #0x87]
    // 0xdb0778: DecompressPointer r1
    //     0xdb0778: add             x1, x1, HEAP, lsl #32
    // 0xdb077c: cmp             w1, NULL
    // 0xdb0780: b.ne            #0xdb0808
    // 0xdb0784: LoadField: r2 = r0->field_77
    //     0xdb0784: ldur            w2, [x0, #0x77]
    // 0xdb0788: DecompressPointer r2
    //     0xdb0788: add             x2, x2, HEAP, lsl #32
    // 0xdb078c: stur            x2, [fp, #-8]
    // 0xdb0790: LoadField: r1 = r0->field_7
    //     0xdb0790: ldur            w1, [x0, #7]
    // 0xdb0794: DecompressPointer r1
    //     0xdb0794: add             x1, x1, HEAP, lsl #32
    // 0xdb0798: r0 = _ModalScope()
    //     0xdb0798: bl              #0xdb0820  ; Allocate_ModalScopeStub -> _ModalScope<X0> (size=0x14)
    // 0xdb079c: mov             x1, x0
    // 0xdb07a0: ldur            x0, [fp, #-0x10]
    // 0xdb07a4: stur            x1, [fp, #-0x18]
    // 0xdb07a8: StoreField: r1->field_f = r0
    //     0xdb07a8: stur            w0, [x1, #0xf]
    // 0xdb07ac: ldur            x2, [fp, #-8]
    // 0xdb07b0: StoreField: r1->field_7 = r2
    //     0xdb07b0: stur            w2, [x1, #7]
    // 0xdb07b4: r0 = Semantics()
    //     0xdb07b4: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0xdb07b8: stur            x0, [fp, #-8]
    // 0xdb07bc: r16 = Instance_OrdinalSortKey
    //     0xdb07bc: add             x16, PP, #0x22, lsl #12  ; [pp+0x22210] Obj!OrdinalSortKey@e18c21
    //     0xdb07c0: ldr             x16, [x16, #0x210]
    // 0xdb07c4: ldur            lr, [fp, #-0x18]
    // 0xdb07c8: stp             lr, x16, [SP]
    // 0xdb07cc: mov             x1, x0
    // 0xdb07d0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, sortKey, 0x1, null]
    //     0xdb07d0: add             x4, PP, #0x22, lsl #12  ; [pp+0x22218] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "sortKey", 0x1, Null]
    //     0xdb07d4: ldr             x4, [x4, #0x218]
    // 0xdb07d8: r0 = Semantics()
    //     0xdb07d8: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0xdb07dc: ldur            x0, [fp, #-8]
    // 0xdb07e0: ldur            x2, [fp, #-0x10]
    // 0xdb07e4: StoreField: r2->field_87 = r0
    //     0xdb07e4: stur            w0, [x2, #0x87]
    //     0xdb07e8: ldurb           w16, [x2, #-1]
    //     0xdb07ec: ldurb           w17, [x0, #-1]
    //     0xdb07f0: and             x16, x17, x16, lsr #2
    //     0xdb07f4: tst             x16, HEAP, lsr #32
    //     0xdb07f8: b.eq            #0xdb0800
    //     0xdb07fc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xdb0800: ldur            x0, [fp, #-8]
    // 0xdb0804: b               #0xdb080c
    // 0xdb0808: mov             x0, x1
    // 0xdb080c: LeaveFrame
    //     0xdb080c: mov             SP, fp
    //     0xdb0810: ldp             fp, lr, [SP], #0x10
    // 0xdb0814: ret
    //     0xdb0814: ret             
    // 0xdb0818: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb0818: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb081c: b               #0xdb0774
  }
  [closure] Widget _buildModalBarrier(dynamic, BuildContext) {
    // ** addr: 0xdb082c, size: 0x3c
    // 0xdb082c: EnterFrame
    //     0xdb082c: stp             fp, lr, [SP, #-0x10]!
    //     0xdb0830: mov             fp, SP
    // 0xdb0834: ldr             x0, [fp, #0x18]
    // 0xdb0838: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xdb0838: ldur            w1, [x0, #0x17]
    // 0xdb083c: DecompressPointer r1
    //     0xdb083c: add             x1, x1, HEAP, lsl #32
    // 0xdb0840: CheckStackOverflow
    //     0xdb0840: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb0844: cmp             SP, x16
    //     0xdb0848: b.ls            #0xdb0860
    // 0xdb084c: ldr             x2, [fp, #0x10]
    // 0xdb0850: r0 = _buildModalBarrier()
    //     0xdb0850: bl              #0xdb0868  ; [package:flutter/src/widgets/routes.dart] ModalRoute::_buildModalBarrier
    // 0xdb0854: LeaveFrame
    //     0xdb0854: mov             SP, fp
    //     0xdb0858: ldp             fp, lr, [SP], #0x10
    // 0xdb085c: ret
    //     0xdb085c: ret             
    // 0xdb0860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb0860: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb0864: b               #0xdb084c
  }
  _ _buildModalBarrier(/* No info */) {
    // ** addr: 0xdb0868, size: 0x108
    // 0xdb0868: EnterFrame
    //     0xdb0868: stp             fp, lr, [SP, #-0x10]!
    //     0xdb086c: mov             fp, SP
    // 0xdb0870: AllocStack(0x30)
    //     0xdb0870: sub             SP, SP, #0x30
    // 0xdb0874: SetupParameters(ModalRoute<X0> this /* r1 => r0, fp-0x8 */)
    //     0xdb0874: mov             x0, x1
    //     0xdb0878: stur            x1, [fp, #-8]
    // 0xdb087c: CheckStackOverflow
    //     0xdb087c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb0880: cmp             SP, x16
    //     0xdb0884: b.ls            #0xdb0964
    // 0xdb0888: mov             x1, x0
    // 0xdb088c: r0 = buildModalBarrier()
    //     0xdb088c: bl              #0xdb0970  ; [package:flutter/src/widgets/routes.dart] ModalRoute::buildModalBarrier
    // 0xdb0890: mov             x2, x0
    // 0xdb0894: ldur            x0, [fp, #-8]
    // 0xdb0898: stur            x2, [fp, #-0x10]
    // 0xdb089c: LoadField: r1 = r0->field_67
    //     0xdb089c: ldur            w1, [x0, #0x67]
    // 0xdb08a0: DecompressPointer r1
    //     0xdb08a0: add             x1, x1, HEAP, lsl #32
    // 0xdb08a4: cmp             w1, NULL
    // 0xdb08a8: b.eq            #0xdb096c
    // 0xdb08ac: r0 = isForwardOrCompleted()
    //     0xdb08ac: bl              #0x85ef3c  ; [package:flutter/src/animation/animation.dart] Animation::isForwardOrCompleted
    // 0xdb08b0: eor             x1, x0, #0x10
    // 0xdb08b4: stur            x1, [fp, #-0x18]
    // 0xdb08b8: r0 = IgnorePointer()
    //     0xdb08b8: bl              #0x9e3118  ; AllocateIgnorePointerStub -> IgnorePointer (size=0x18)
    // 0xdb08bc: mov             x1, x0
    // 0xdb08c0: ldur            x0, [fp, #-0x18]
    // 0xdb08c4: stur            x1, [fp, #-0x20]
    // 0xdb08c8: StoreField: r1->field_f = r0
    //     0xdb08c8: stur            w0, [x1, #0xf]
    // 0xdb08cc: ldur            x0, [fp, #-0x10]
    // 0xdb08d0: StoreField: r1->field_b = r0
    //     0xdb08d0: stur            w0, [x1, #0xb]
    // 0xdb08d4: ldur            x0, [fp, #-8]
    // 0xdb08d8: r2 = LoadClassIdInstr(r0)
    //     0xdb08d8: ldur            x2, [x0, #-1]
    //     0xdb08dc: ubfx            x2, x2, #0xc, #0x14
    // 0xdb08e0: cmp             x2, #0xa5d
    // 0xdb08e4: b.ne            #0xdb08f8
    // 0xdb08e8: LoadField: r2 = r0->field_93
    //     0xdb08e8: ldur            w2, [x0, #0x93]
    // 0xdb08ec: DecompressPointer r2
    //     0xdb08ec: add             x2, x2, HEAP, lsl #32
    // 0xdb08f0: tbnz            w2, #4, #0xdb0954
    // 0xdb08f4: b               #0xdb0924
    // 0xdb08f8: cmp             x2, #0xa5e
    // 0xdb08fc: b.ne            #0xdb0910
    // 0xdb0900: LoadField: r2 = r0->field_af
    //     0xdb0900: ldur            w2, [x0, #0xaf]
    // 0xdb0904: DecompressPointer r2
    //     0xdb0904: add             x2, x2, HEAP, lsl #32
    // 0xdb0908: tbnz            w2, #4, #0xdb0954
    // 0xdb090c: b               #0xdb0924
    // 0xdb0910: cmp             x2, #0xa62
    // 0xdb0914: b.ne            #0xdb0954
    // 0xdb0918: LoadField: r2 = r0->field_c3
    //     0xdb0918: ldur            w2, [x0, #0xc3]
    // 0xdb091c: DecompressPointer r2
    //     0xdb091c: add             x2, x2, HEAP, lsl #32
    // 0xdb0920: tbnz            w2, #4, #0xdb0954
    // 0xdb0924: r0 = Semantics()
    //     0xdb0924: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0xdb0928: stur            x0, [fp, #-8]
    // 0xdb092c: r16 = Instance_OrdinalSortKey
    //     0xdb092c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22318] Obj!OrdinalSortKey@e18c41
    //     0xdb0930: ldr             x16, [x16, #0x318]
    // 0xdb0934: ldur            lr, [fp, #-0x20]
    // 0xdb0938: stp             lr, x16, [SP]
    // 0xdb093c: mov             x1, x0
    // 0xdb0940: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, sortKey, 0x1, null]
    //     0xdb0940: add             x4, PP, #0x22, lsl #12  ; [pp+0x22218] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "sortKey", 0x1, Null]
    //     0xdb0944: ldr             x4, [x4, #0x218]
    // 0xdb0948: r0 = Semantics()
    //     0xdb0948: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0xdb094c: ldur            x0, [fp, #-8]
    // 0xdb0950: b               #0xdb0958
    // 0xdb0954: ldur            x0, [fp, #-0x20]
    // 0xdb0958: LeaveFrame
    //     0xdb0958: mov             SP, fp
    //     0xdb095c: ldp             fp, lr, [SP], #0x10
    // 0xdb0960: ret
    //     0xdb0960: ret             
    // 0xdb0964: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb0964: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb0968: b               #0xdb0888
    // 0xdb096c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb096c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ buildModalBarrier(/* No info */) {
    // ** addr: 0xdb0970, size: 0x39c
    // 0xdb0970: EnterFrame
    //     0xdb0970: stp             fp, lr, [SP, #-0x10]!
    //     0xdb0974: mov             fp, SP
    // 0xdb0978: AllocStack(0x30)
    //     0xdb0978: sub             SP, SP, #0x30
    // 0xdb097c: SetupParameters(ModalRoute<X0> this /* r1 => r0, fp-0x10 */)
    //     0xdb097c: mov             x0, x1
    //     0xdb0980: stur            x1, [fp, #-0x10]
    // 0xdb0984: CheckStackOverflow
    //     0xdb0984: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb0988: cmp             SP, x16
    //     0xdb098c: b.ls            #0xdb0cf8
    // 0xdb0990: r2 = LoadClassIdInstr(r0)
    //     0xdb0990: ldur            x2, [x0, #-1]
    //     0xdb0994: ubfx            x2, x2, #0xc, #0x14
    // 0xdb0998: stur            x2, [fp, #-8]
    // 0xdb099c: cmp             x2, #0xa5d
    // 0xdb09a0: b.eq            #0xdb09ac
    // 0xdb09a4: cmp             x2, #0xa5e
    // 0xdb09a8: b.ne            #0xdb0c38
    // 0xdb09ac: cmp             x2, #0xa5d
    // 0xdb09b0: b.ne            #0xdb09bc
    // 0xdb09b4: r1 = Instance_Color
    //     0xdb09b4: ldr             x1, [PP, #0x5470]  ; [pp+0x5470] Obj!Color@e270c1
    // 0xdb09b8: b               #0xdb09e4
    // 0xdb09bc: cmp             x2, #0xa5e
    // 0xdb09c0: b.ne            #0xdb09cc
    // 0xdb09c4: r1 = Instance_Color
    //     0xdb09c4: ldr             x1, [PP, #0x5470]  ; [pp+0x5470] Obj!Color@e270c1
    // 0xdb09c8: b               #0xdb09e4
    // 0xdb09cc: cmp             x2, #0xa62
    // 0xdb09d0: b.ne            #0xdb09e0
    // 0xdb09d4: LoadField: r1 = r0->field_d7
    //     0xdb09d4: ldur            w1, [x0, #0xd7]
    // 0xdb09d8: DecompressPointer r1
    //     0xdb09d8: add             x1, x1, HEAP, lsl #32
    // 0xdb09dc: b               #0xdb09e4
    // 0xdb09e0: r1 = Null
    //     0xdb09e0: mov             x1, NULL
    // 0xdb09e4: cmp             w1, NULL
    // 0xdb09e8: b.eq            #0xdb0d00
    // 0xdb09ec: r0 = value()
    //     0xdb09ec: bl              #0xd69d08  ; [dart:ui] Color::value
    // 0xdb09f0: ubfx            x0, x0, #0, #0x20
    // 0xdb09f4: r1 = 4278190080
    //     0xdb09f4: orr             x1, xzr, #0xff000000
    // 0xdb09f8: and             x2, x0, x1
    // 0xdb09fc: ubfx            x2, x2, #0, #0x20
    // 0xdb0a00: asr             x0, x2, #0x18
    // 0xdb0a04: cbz             x0, #0xdb0c28
    // 0xdb0a08: ldur            x0, [fp, #-0x10]
    // 0xdb0a0c: LoadField: r1 = r0->field_63
    //     0xdb0a0c: ldur            w1, [x0, #0x63]
    // 0xdb0a10: DecompressPointer r1
    //     0xdb0a10: add             x1, x1, HEAP, lsl #32
    // 0xdb0a14: tbz             w1, #4, #0xdb0c18
    // 0xdb0a18: ldur            x2, [fp, #-8]
    // 0xdb0a1c: LoadField: r3 = r0->field_67
    //     0xdb0a1c: ldur            w3, [x0, #0x67]
    // 0xdb0a20: DecompressPointer r3
    //     0xdb0a20: add             x3, x3, HEAP, lsl #32
    // 0xdb0a24: stur            x3, [fp, #-0x18]
    // 0xdb0a28: cmp             w3, NULL
    // 0xdb0a2c: b.eq            #0xdb0d04
    // 0xdb0a30: cmp             x2, #0xa5d
    // 0xdb0a34: b.ne            #0xdb0a40
    // 0xdb0a38: r1 = Instance_Color
    //     0xdb0a38: ldr             x1, [PP, #0x5470]  ; [pp+0x5470] Obj!Color@e270c1
    // 0xdb0a3c: b               #0xdb0a68
    // 0xdb0a40: cmp             x2, #0xa5e
    // 0xdb0a44: b.ne            #0xdb0a50
    // 0xdb0a48: r1 = Instance_Color
    //     0xdb0a48: ldr             x1, [PP, #0x5470]  ; [pp+0x5470] Obj!Color@e270c1
    // 0xdb0a4c: b               #0xdb0a68
    // 0xdb0a50: cmp             x2, #0xa62
    // 0xdb0a54: b.ne            #0xdb0a64
    // 0xdb0a58: LoadField: r1 = r0->field_d7
    //     0xdb0a58: ldur            w1, [x0, #0xd7]
    // 0xdb0a5c: DecompressPointer r1
    //     0xdb0a5c: add             x1, x1, HEAP, lsl #32
    // 0xdb0a60: b               #0xdb0a68
    // 0xdb0a64: r1 = Null
    //     0xdb0a64: mov             x1, NULL
    // 0xdb0a68: cmp             w1, NULL
    // 0xdb0a6c: b.eq            #0xdb0d08
    // 0xdb0a70: d0 = 0.000000
    //     0xdb0a70: eor             v0.16b, v0.16b, v0.16b
    // 0xdb0a74: r0 = withOpacity()
    //     0xdb0a74: bl              #0xd72290  ; [dart:ui] Color::withOpacity
    // 0xdb0a78: mov             x2, x0
    // 0xdb0a7c: ldur            x0, [fp, #-8]
    // 0xdb0a80: stur            x2, [fp, #-0x28]
    // 0xdb0a84: cmp             x0, #0xa5d
    // 0xdb0a88: b.ne            #0xdb0a98
    // 0xdb0a8c: ldur            x3, [fp, #-0x10]
    // 0xdb0a90: r4 = Instance_Color
    //     0xdb0a90: ldr             x4, [PP, #0x5470]  ; [pp+0x5470] Obj!Color@e270c1
    // 0xdb0a94: b               #0xdb0ad0
    // 0xdb0a98: cmp             x0, #0xa5e
    // 0xdb0a9c: b.ne            #0xdb0aac
    // 0xdb0aa0: ldur            x3, [fp, #-0x10]
    // 0xdb0aa4: r4 = Instance_Color
    //     0xdb0aa4: ldr             x4, [PP, #0x5470]  ; [pp+0x5470] Obj!Color@e270c1
    // 0xdb0aa8: b               #0xdb0ad0
    // 0xdb0aac: cmp             x0, #0xa62
    // 0xdb0ab0: b.ne            #0xdb0ac8
    // 0xdb0ab4: ldur            x3, [fp, #-0x10]
    // 0xdb0ab8: LoadField: r1 = r3->field_d7
    //     0xdb0ab8: ldur            w1, [x3, #0xd7]
    // 0xdb0abc: DecompressPointer r1
    //     0xdb0abc: add             x1, x1, HEAP, lsl #32
    // 0xdb0ac0: mov             x4, x1
    // 0xdb0ac4: b               #0xdb0ad0
    // 0xdb0ac8: ldur            x3, [fp, #-0x10]
    // 0xdb0acc: r4 = Null
    //     0xdb0acc: mov             x4, NULL
    // 0xdb0ad0: stur            x4, [fp, #-0x20]
    // 0xdb0ad4: r1 = <Color?>
    //     0xdb0ad4: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xdb0ad8: ldr             x1, [x1, #0x98]
    // 0xdb0adc: r0 = ColorTween()
    //     0xdb0adc: bl              #0x7e3594  ; AllocateColorTweenStub -> ColorTween (size=0x14)
    // 0xdb0ae0: mov             x2, x0
    // 0xdb0ae4: ldur            x0, [fp, #-0x28]
    // 0xdb0ae8: stur            x2, [fp, #-0x30]
    // 0xdb0aec: StoreField: r2->field_b = r0
    //     0xdb0aec: stur            w0, [x2, #0xb]
    // 0xdb0af0: ldur            x0, [fp, #-0x20]
    // 0xdb0af4: StoreField: r2->field_f = r0
    //     0xdb0af4: stur            w0, [x2, #0xf]
    // 0xdb0af8: r1 = <double>
    //     0xdb0af8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xdb0afc: r0 = CurveTween()
    //     0xdb0afc: bl              #0x796a9c  ; AllocateCurveTweenStub -> CurveTween (size=0x10)
    // 0xdb0b00: mov             x1, x0
    // 0xdb0b04: r0 = Instance_Cubic
    //     0xdb0b04: ldr             x0, [PP, #0x5088]  ; [pp+0x5088] Obj!Cubic@e14d71
    // 0xdb0b08: StoreField: r1->field_b = r0
    //     0xdb0b08: stur            w0, [x1, #0xb]
    // 0xdb0b0c: mov             x2, x1
    // 0xdb0b10: ldur            x1, [fp, #-0x30]
    // 0xdb0b14: r0 = chain()
    //     0xdb0b14: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0xdb0b18: mov             x3, x0
    // 0xdb0b1c: ldur            x0, [fp, #-0x18]
    // 0xdb0b20: r2 = Null
    //     0xdb0b20: mov             x2, NULL
    // 0xdb0b24: r1 = Null
    //     0xdb0b24: mov             x1, NULL
    // 0xdb0b28: stur            x3, [fp, #-0x20]
    // 0xdb0b2c: r8 = Animation<double>
    //     0xdb0b2c: add             x8, PP, #0x22, lsl #12  ; [pp+0x22320] Type: Animation<double>
    //     0xdb0b30: ldr             x8, [x8, #0x320]
    // 0xdb0b34: r3 = Null
    //     0xdb0b34: add             x3, PP, #0x22, lsl #12  ; [pp+0x22328] Null
    //     0xdb0b38: ldr             x3, [x3, #0x328]
    // 0xdb0b3c: r0 = Animation<double>()
    //     0xdb0b3c: bl              #0x652c7c  ; IsType_Animation<double>_Stub
    // 0xdb0b40: ldur            x1, [fp, #-0x20]
    // 0xdb0b44: ldur            x2, [fp, #-0x18]
    // 0xdb0b48: r0 = animate()
    //     0xdb0b48: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0xdb0b4c: mov             x1, x0
    // 0xdb0b50: ldur            x0, [fp, #-8]
    // 0xdb0b54: stur            x1, [fp, #-0x28]
    // 0xdb0b58: cmp             x0, #0xa5d
    // 0xdb0b5c: b.ne            #0xdb0b70
    // 0xdb0b60: ldur            x2, [fp, #-0x10]
    // 0xdb0b64: LoadField: r3 = r2->field_93
    //     0xdb0b64: ldur            w3, [x2, #0x93]
    // 0xdb0b68: DecompressPointer r3
    //     0xdb0b68: add             x3, x3, HEAP, lsl #32
    // 0xdb0b6c: b               #0xdb0ba0
    // 0xdb0b70: ldur            x2, [fp, #-0x10]
    // 0xdb0b74: cmp             x0, #0xa5e
    // 0xdb0b78: b.ne            #0xdb0b88
    // 0xdb0b7c: LoadField: r3 = r2->field_af
    //     0xdb0b7c: ldur            w3, [x2, #0xaf]
    // 0xdb0b80: DecompressPointer r3
    //     0xdb0b80: add             x3, x3, HEAP, lsl #32
    // 0xdb0b84: b               #0xdb0ba0
    // 0xdb0b88: cmp             x0, #0xa62
    // 0xdb0b8c: b.ne            #0xdb0b9c
    // 0xdb0b90: LoadField: r3 = r2->field_c3
    //     0xdb0b90: ldur            w3, [x2, #0xc3]
    // 0xdb0b94: DecompressPointer r3
    //     0xdb0b94: add             x3, x3, HEAP, lsl #32
    // 0xdb0b98: b               #0xdb0ba0
    // 0xdb0b9c: r3 = false
    //     0xdb0b9c: add             x3, NULL, #0x30  ; false
    // 0xdb0ba0: stur            x3, [fp, #-0x20]
    // 0xdb0ba4: cmp             x0, #0xa5d
    // 0xdb0ba8: b.ne            #0xdb0bb8
    // 0xdb0bac: r0 = "Dismiss"
    //     0xdb0bac: add             x0, PP, #0x22, lsl #12  ; [pp+0x22338] "Dismiss"
    //     0xdb0bb0: ldr             x0, [x0, #0x338]
    // 0xdb0bb4: b               #0xdb0be4
    // 0xdb0bb8: cmp             x0, #0xa5e
    // 0xdb0bbc: b.ne            #0xdb0bcc
    // 0xdb0bc0: LoadField: r0 = r2->field_c3
    //     0xdb0bc0: ldur            w0, [x2, #0xc3]
    // 0xdb0bc4: DecompressPointer r0
    //     0xdb0bc4: add             x0, x0, HEAP, lsl #32
    // 0xdb0bc8: b               #0xdb0be4
    // 0xdb0bcc: cmp             x0, #0xa62
    // 0xdb0bd0: b.ne            #0xdb0be0
    // 0xdb0bd4: LoadField: r0 = r2->field_db
    //     0xdb0bd4: ldur            w0, [x2, #0xdb]
    // 0xdb0bd8: DecompressPointer r0
    //     0xdb0bd8: add             x0, x0, HEAP, lsl #32
    // 0xdb0bdc: b               #0xdb0be4
    // 0xdb0be0: r0 = Null
    //     0xdb0be0: mov             x0, NULL
    // 0xdb0be4: stur            x0, [fp, #-0x18]
    // 0xdb0be8: r0 = AnimatedModalBarrier()
    //     0xdb0be8: bl              #0xdb0d0c  ; AllocateAnimatedModalBarrierStub -> AnimatedModalBarrier (size=0x28)
    // 0xdb0bec: mov             x1, x0
    // 0xdb0bf0: ldur            x0, [fp, #-0x20]
    // 0xdb0bf4: StoreField: r1->field_f = r0
    //     0xdb0bf4: stur            w0, [x1, #0xf]
    // 0xdb0bf8: ldur            x0, [fp, #-0x18]
    // 0xdb0bfc: StoreField: r1->field_13 = r0
    //     0xdb0bfc: stur            w0, [x1, #0x13]
    // 0xdb0c00: r3 = true
    //     0xdb0c00: add             x3, NULL, #0x20  ; true
    // 0xdb0c04: ArrayStore: r1[0] = r3  ; List_4
    //     0xdb0c04: stur            w3, [x1, #0x17]
    // 0xdb0c08: ldur            x0, [fp, #-0x28]
    // 0xdb0c0c: StoreField: r1->field_b = r0
    //     0xdb0c0c: stur            w0, [x1, #0xb]
    // 0xdb0c10: mov             x0, x1
    // 0xdb0c14: b               #0xdb0cec
    // 0xdb0c18: mov             x2, x0
    // 0xdb0c1c: ldur            x0, [fp, #-8]
    // 0xdb0c20: r3 = true
    //     0xdb0c20: add             x3, NULL, #0x20  ; true
    // 0xdb0c24: b               #0xdb0c48
    // 0xdb0c28: ldur            x2, [fp, #-0x10]
    // 0xdb0c2c: ldur            x0, [fp, #-8]
    // 0xdb0c30: r3 = true
    //     0xdb0c30: add             x3, NULL, #0x20  ; true
    // 0xdb0c34: b               #0xdb0c48
    // 0xdb0c38: mov             x16, x2
    // 0xdb0c3c: mov             x2, x0
    // 0xdb0c40: mov             x0, x16
    // 0xdb0c44: r3 = true
    //     0xdb0c44: add             x3, NULL, #0x20  ; true
    // 0xdb0c48: cmp             x0, #0xa5d
    // 0xdb0c4c: b.ne            #0xdb0c5c
    // 0xdb0c50: LoadField: r1 = r2->field_93
    //     0xdb0c50: ldur            w1, [x2, #0x93]
    // 0xdb0c54: DecompressPointer r1
    //     0xdb0c54: add             x1, x1, HEAP, lsl #32
    // 0xdb0c58: b               #0xdb0c88
    // 0xdb0c5c: cmp             x0, #0xa5e
    // 0xdb0c60: b.ne            #0xdb0c70
    // 0xdb0c64: LoadField: r1 = r2->field_af
    //     0xdb0c64: ldur            w1, [x2, #0xaf]
    // 0xdb0c68: DecompressPointer r1
    //     0xdb0c68: add             x1, x1, HEAP, lsl #32
    // 0xdb0c6c: b               #0xdb0c88
    // 0xdb0c70: cmp             x0, #0xa62
    // 0xdb0c74: b.ne            #0xdb0c84
    // 0xdb0c78: LoadField: r1 = r2->field_c3
    //     0xdb0c78: ldur            w1, [x2, #0xc3]
    // 0xdb0c7c: DecompressPointer r1
    //     0xdb0c7c: add             x1, x1, HEAP, lsl #32
    // 0xdb0c80: b               #0xdb0c88
    // 0xdb0c84: r1 = false
    //     0xdb0c84: add             x1, NULL, #0x30  ; false
    // 0xdb0c88: stur            x1, [fp, #-0x18]
    // 0xdb0c8c: cmp             x0, #0xa5d
    // 0xdb0c90: b.ne            #0xdb0ca0
    // 0xdb0c94: r0 = "Dismiss"
    //     0xdb0c94: add             x0, PP, #0x22, lsl #12  ; [pp+0x22338] "Dismiss"
    //     0xdb0c98: ldr             x0, [x0, #0x338]
    // 0xdb0c9c: b               #0xdb0ccc
    // 0xdb0ca0: cmp             x0, #0xa5e
    // 0xdb0ca4: b.ne            #0xdb0cb4
    // 0xdb0ca8: LoadField: r0 = r2->field_c3
    //     0xdb0ca8: ldur            w0, [x2, #0xc3]
    // 0xdb0cac: DecompressPointer r0
    //     0xdb0cac: add             x0, x0, HEAP, lsl #32
    // 0xdb0cb0: b               #0xdb0ccc
    // 0xdb0cb4: cmp             x0, #0xa62
    // 0xdb0cb8: b.ne            #0xdb0cc8
    // 0xdb0cbc: LoadField: r0 = r2->field_db
    //     0xdb0cbc: ldur            w0, [x2, #0xdb]
    // 0xdb0cc0: DecompressPointer r0
    //     0xdb0cc0: add             x0, x0, HEAP, lsl #32
    // 0xdb0cc4: b               #0xdb0ccc
    // 0xdb0cc8: r0 = Null
    //     0xdb0cc8: mov             x0, NULL
    // 0xdb0ccc: stur            x0, [fp, #-0x10]
    // 0xdb0cd0: r0 = ModalBarrier()
    //     0xdb0cd0: bl              #0xc21448  ; AllocateModalBarrierStub -> ModalBarrier (size=0x28)
    // 0xdb0cd4: ldur            x1, [fp, #-0x18]
    // 0xdb0cd8: StoreField: r0->field_f = r1
    //     0xdb0cd8: stur            w1, [x0, #0xf]
    // 0xdb0cdc: ldur            x1, [fp, #-0x10]
    // 0xdb0ce0: StoreField: r0->field_1b = r1
    //     0xdb0ce0: stur            w1, [x0, #0x1b]
    // 0xdb0ce4: r1 = true
    //     0xdb0ce4: add             x1, NULL, #0x20  ; true
    // 0xdb0ce8: ArrayStore: r0[0] = r1  ; List_4
    //     0xdb0ce8: stur            w1, [x0, #0x17]
    // 0xdb0cec: LeaveFrame
    //     0xdb0cec: mov             SP, fp
    //     0xdb0cf0: ldp             fp, lr, [SP], #0x10
    // 0xdb0cf4: ret
    //     0xdb0cf4: ret             
    // 0xdb0cf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb0cf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb0cfc: b               #0xdb0990
    // 0xdb0d00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb0d00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdb0d04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb0d04: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdb0d08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb0d08: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  set _ offstage=(/* No info */) {
    // ** addr: 0xdb5530, size: 0x110
    // 0xdb5530: EnterFrame
    //     0xdb5530: stp             fp, lr, [SP, #-0x10]!
    //     0xdb5534: mov             fp, SP
    // 0xdb5538: AllocStack(0x10)
    //     0xdb5538: sub             SP, SP, #0x10
    // 0xdb553c: SetupParameters(ModalRoute<X0> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xdb553c: stur            x1, [fp, #-8]
    //     0xdb5540: stur            x2, [fp, #-0x10]
    // 0xdb5544: CheckStackOverflow
    //     0xdb5544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb5548: cmp             SP, x16
    //     0xdb554c: b.ls            #0xdb5630
    // 0xdb5550: r1 = 2
    //     0xdb5550: movz            x1, #0x2
    // 0xdb5554: r0 = AllocateContext()
    //     0xdb5554: bl              #0xec126c  ; AllocateContextStub
    // 0xdb5558: mov             x1, x0
    // 0xdb555c: ldur            x0, [fp, #-8]
    // 0xdb5560: StoreField: r1->field_f = r0
    //     0xdb5560: stur            w0, [x1, #0xf]
    // 0xdb5564: ldur            x2, [fp, #-0x10]
    // 0xdb5568: StoreField: r1->field_13 = r2
    //     0xdb5568: stur            w2, [x1, #0x13]
    // 0xdb556c: LoadField: r3 = r0->field_63
    //     0xdb556c: ldur            w3, [x0, #0x63]
    // 0xdb5570: DecompressPointer r3
    //     0xdb5570: add             x3, x3, HEAP, lsl #32
    // 0xdb5574: cmp             w3, w2
    // 0xdb5578: b.ne            #0xdb558c
    // 0xdb557c: r0 = Null
    //     0xdb557c: mov             x0, NULL
    // 0xdb5580: LeaveFrame
    //     0xdb5580: mov             SP, fp
    //     0xdb5584: ldp             fp, lr, [SP], #0x10
    // 0xdb5588: ret
    //     0xdb5588: ret             
    // 0xdb558c: mov             x2, x1
    // 0xdb5590: r1 = Function '<anonymous closure>':.
    //     0xdb5590: add             x1, PP, #0x50, lsl #12  ; [pp+0x50370] AnonymousClosure: (0xdb5640), in [package:flutter/src/widgets/routes.dart] ModalRoute::offstage= (0xdb5530)
    //     0xdb5594: ldr             x1, [x1, #0x370]
    // 0xdb5598: r0 = AllocateClosure()
    //     0xdb5598: bl              #0xec1630  ; AllocateClosureStub
    // 0xdb559c: ldur            x1, [fp, #-8]
    // 0xdb55a0: mov             x2, x0
    // 0xdb55a4: r0 = setState()
    //     0xdb55a4: bl              #0x650a20  ; [package:flutter/src/widgets/routes.dart] ModalRoute::setState
    // 0xdb55a8: ldur            x0, [fp, #-8]
    // 0xdb55ac: LoadField: r1 = r0->field_67
    //     0xdb55ac: ldur            w1, [x0, #0x67]
    // 0xdb55b0: DecompressPointer r1
    //     0xdb55b0: add             x1, x1, HEAP, lsl #32
    // 0xdb55b4: cmp             w1, NULL
    // 0xdb55b8: b.eq            #0xdb5638
    // 0xdb55bc: LoadField: r2 = r0->field_63
    //     0xdb55bc: ldur            w2, [x0, #0x63]
    // 0xdb55c0: DecompressPointer r2
    //     0xdb55c0: add             x2, x2, HEAP, lsl #32
    // 0xdb55c4: tbnz            w2, #4, #0xdb55d4
    // 0xdb55c8: r2 = Instance__AlwaysCompleteAnimation
    //     0xdb55c8: add             x2, PP, #0x24, lsl #12  ; [pp+0x24f70] Obj!_AlwaysCompleteAnimation@e25961
    //     0xdb55cc: ldr             x2, [x2, #0xf70]
    // 0xdb55d0: b               #0xdb55dc
    // 0xdb55d4: LoadField: r2 = r0->field_33
    //     0xdb55d4: ldur            w2, [x0, #0x33]
    // 0xdb55d8: DecompressPointer r2
    //     0xdb55d8: add             x2, x2, HEAP, lsl #32
    // 0xdb55dc: r0 = parent=()
    //     0xdb55dc: bl              #0x652a14  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::parent=
    // 0xdb55e0: ldur            x0, [fp, #-8]
    // 0xdb55e4: LoadField: r1 = r0->field_6b
    //     0xdb55e4: ldur            w1, [x0, #0x6b]
    // 0xdb55e8: DecompressPointer r1
    //     0xdb55e8: add             x1, x1, HEAP, lsl #32
    // 0xdb55ec: cmp             w1, NULL
    // 0xdb55f0: b.eq            #0xdb563c
    // 0xdb55f4: LoadField: r2 = r0->field_63
    //     0xdb55f4: ldur            w2, [x0, #0x63]
    // 0xdb55f8: DecompressPointer r2
    //     0xdb55f8: add             x2, x2, HEAP, lsl #32
    // 0xdb55fc: tbnz            w2, #4, #0xdb560c
    // 0xdb5600: r2 = Instance__AlwaysDismissedAnimation
    //     0xdb5600: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a810] Obj!_AlwaysDismissedAnimation@e25951
    //     0xdb5604: ldr             x2, [x2, #0x810]
    // 0xdb5608: b               #0xdb5614
    // 0xdb560c: LoadField: r2 = r0->field_3b
    //     0xdb560c: ldur            w2, [x0, #0x3b]
    // 0xdb5610: DecompressPointer r2
    //     0xdb5610: add             x2, x2, HEAP, lsl #32
    // 0xdb5614: r0 = parent=()
    //     0xdb5614: bl              #0x652a14  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::parent=
    // 0xdb5618: ldur            x1, [fp, #-8]
    // 0xdb561c: r0 = changedInternalState()
    //     0xdb561c: bl              #0x6507d0  ; [package:flutter/src/widgets/routes.dart] ModalRoute::changedInternalState
    // 0xdb5620: r0 = Null
    //     0xdb5620: mov             x0, NULL
    // 0xdb5624: LeaveFrame
    //     0xdb5624: mov             SP, fp
    //     0xdb5628: ldp             fp, lr, [SP], #0x10
    // 0xdb562c: ret
    //     0xdb562c: ret             
    // 0xdb5630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb5630: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb5634: b               #0xdb5550
    // 0xdb5638: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb5638: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdb563c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb563c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xdb5640, size: 0x28
    // 0xdb5640: ldr             x1, [SP]
    // 0xdb5644: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xdb5644: ldur            w2, [x1, #0x17]
    // 0xdb5648: DecompressPointer r2
    //     0xdb5648: add             x2, x2, HEAP, lsl #32
    // 0xdb564c: LoadField: r1 = r2->field_f
    //     0xdb564c: ldur            w1, [x2, #0xf]
    // 0xdb5650: DecompressPointer r1
    //     0xdb5650: add             x1, x1, HEAP, lsl #32
    // 0xdb5654: LoadField: r3 = r2->field_13
    //     0xdb5654: ldur            w3, [x2, #0x13]
    // 0xdb5658: DecompressPointer r3
    //     0xdb5658: add             x3, x3, HEAP, lsl #32
    // 0xdb565c: StoreField: r1->field_63 = r3
    //     0xdb565c: stur            w3, [x1, #0x63]
    // 0xdb5660: r0 = Null
    //     0xdb5660: mov             x0, NULL
    // 0xdb5664: ret
    //     0xdb5664: ret             
  }
  get _ popGestureInProgress(/* No info */) {
    // ** addr: 0xdb8948, size: 0x30
    // 0xdb8948: LoadField: r2 = r1->field_f
    //     0xdb8948: ldur            w2, [x1, #0xf]
    // 0xdb894c: DecompressPointer r2
    //     0xdb894c: add             x2, x2, HEAP, lsl #32
    // 0xdb8950: cmp             w2, NULL
    // 0xdb8954: b.eq            #0xdb896c
    // 0xdb8958: LoadField: r1 = r2->field_67
    //     0xdb8958: ldur            w1, [x2, #0x67]
    // 0xdb895c: DecompressPointer r1
    //     0xdb895c: add             x1, x1, HEAP, lsl #32
    // 0xdb8960: LoadField: r0 = r1->field_27
    //     0xdb8960: ldur            w0, [x1, #0x27]
    // 0xdb8964: DecompressPointer r0
    //     0xdb8964: add             x0, x0, HEAP, lsl #32
    // 0xdb8968: ret
    //     0xdb8968: ret             
    // 0xdb896c: EnterFrame
    //     0xdb896c: stp             fp, lr, [SP, #-0x10]!
    //     0xdb8970: mov             fp, SP
    // 0xdb8974: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdb8974: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 2652, size: 0x90, field offset: 0x90
abstract class PopupRoute<X0> extends ModalRoute<X0> {
}

// class id: 2678, size: 0x10, field offset: 0x8
abstract class RouteObserver<X0 bound Route> extends NavigatorObserver {

  _ didPop(/* No info */) {
    // ** addr: 0xdb12f4, size: 0x388
    // 0xdb12f4: EnterFrame
    //     0xdb12f4: stp             fp, lr, [SP, #-0x10]!
    //     0xdb12f8: mov             fp, SP
    // 0xdb12fc: AllocStack(0x20)
    //     0xdb12fc: sub             SP, SP, #0x20
    // 0xdb1300: SetupParameters(RouteObserver<X0 bound Route> this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xdb1300: mov             x5, x1
    //     0xdb1304: mov             x4, x2
    //     0xdb1308: stur            x1, [fp, #-0x10]
    //     0xdb130c: stur            x2, [fp, #-0x18]
    //     0xdb1310: stur            x3, [fp, #-0x20]
    // 0xdb1314: CheckStackOverflow
    //     0xdb1314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb1318: cmp             SP, x16
    //     0xdb131c: b.ls            #0xdb166c
    // 0xdb1320: LoadField: r6 = r5->field_7
    //     0xdb1320: ldur            w6, [x5, #7]
    // 0xdb1324: DecompressPointer r6
    //     0xdb1324: add             x6, x6, HEAP, lsl #32
    // 0xdb1328: mov             x0, x4
    // 0xdb132c: mov             x2, x6
    // 0xdb1330: stur            x6, [fp, #-8]
    // 0xdb1334: r1 = Null
    //     0xdb1334: mov             x1, NULL
    // 0xdb1338: cmp             w2, NULL
    // 0xdb133c: b.eq            #0xdb13d4
    // 0xdb1340: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xdb1340: ldur            w3, [x2, #0x17]
    // 0xdb1344: DecompressPointer r3
    //     0xdb1344: add             x3, x3, HEAP, lsl #32
    // 0xdb1348: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xdb134c: cmp             w3, w16
    // 0xdb1350: b.eq            #0xdb13d4
    // 0xdb1354: r16 = Object?
    //     0xdb1354: ldr             x16, [PP, #0x1648]  ; [pp+0x1648] Type: Object?
    // 0xdb1358: cmp             w3, w16
    // 0xdb135c: b.eq            #0xdb13d4
    // 0xdb1360: r16 = void?
    //     0xdb1360: ldr             x16, [PP, #0x1650]  ; [pp+0x1650] Type: void?
    // 0xdb1364: cmp             w3, w16
    // 0xdb1368: b.eq            #0xdb13d4
    // 0xdb136c: tbnz            w0, #0, #0xdb1388
    // 0xdb1370: r16 = int
    //     0xdb1370: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdb1374: cmp             w3, w16
    // 0xdb1378: b.eq            #0xdb13d4
    // 0xdb137c: r16 = num
    //     0xdb137c: ldr             x16, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xdb1380: cmp             w3, w16
    // 0xdb1384: b.eq            #0xdb13d4
    // 0xdb1388: r3 = SubtypeTestCache
    //     0xdb1388: add             x3, PP, #0x22, lsl #12  ; [pp+0x22440] SubtypeTestCache
    //     0xdb138c: ldr             x3, [x3, #0x440]
    // 0xdb1390: r30 = Subtype4TestCacheStub
    //     0xdb1390: ldr             lr, [PP, #0x20]  ; [pp+0x20] Stub: Subtype4TestCache (0x5f2a74)
    // 0xdb1394: LoadField: r30 = r30->field_7
    //     0xdb1394: ldur            lr, [lr, #7]
    // 0xdb1398: blr             lr
    // 0xdb139c: cmp             w7, NULL
    // 0xdb13a0: b.eq            #0xdb13ac
    // 0xdb13a4: tbnz            w7, #4, #0xdb13cc
    // 0xdb13a8: b               #0xdb13d4
    // 0xdb13ac: r8 = X0 bound Route
    //     0xdb13ac: add             x8, PP, #0x22, lsl #12  ; [pp+0x22448] TypeParameter: X0 bound Route
    //     0xdb13b0: ldr             x8, [x8, #0x448]
    // 0xdb13b4: r3 = SubtypeTestCache
    //     0xdb13b4: add             x3, PP, #0x22, lsl #12  ; [pp+0x22450] SubtypeTestCache
    //     0xdb13b8: ldr             x3, [x3, #0x450]
    // 0xdb13bc: r30 = InstanceOfStub
    //     0xdb13bc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xdb13c0: LoadField: r30 = r30->field_7
    //     0xdb13c0: ldur            lr, [lr, #7]
    // 0xdb13c4: blr             lr
    // 0xdb13c8: b               #0xdb13d8
    // 0xdb13cc: r0 = false
    //     0xdb13cc: add             x0, NULL, #0x30  ; false
    // 0xdb13d0: b               #0xdb13d8
    // 0xdb13d4: r0 = true
    //     0xdb13d4: add             x0, NULL, #0x20  ; true
    // 0xdb13d8: tbnz            w0, #4, #0xdb1594
    // 0xdb13dc: ldur            x0, [fp, #-0x20]
    // 0xdb13e0: ldur            x2, [fp, #-8]
    // 0xdb13e4: r1 = Null
    //     0xdb13e4: mov             x1, NULL
    // 0xdb13e8: cmp             w2, NULL
    // 0xdb13ec: b.eq            #0xdb1484
    // 0xdb13f0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xdb13f0: ldur            w3, [x2, #0x17]
    // 0xdb13f4: DecompressPointer r3
    //     0xdb13f4: add             x3, x3, HEAP, lsl #32
    // 0xdb13f8: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xdb13fc: cmp             w3, w16
    // 0xdb1400: b.eq            #0xdb1484
    // 0xdb1404: r16 = Object?
    //     0xdb1404: ldr             x16, [PP, #0x1648]  ; [pp+0x1648] Type: Object?
    // 0xdb1408: cmp             w3, w16
    // 0xdb140c: b.eq            #0xdb1484
    // 0xdb1410: r16 = void?
    //     0xdb1410: ldr             x16, [PP, #0x1650]  ; [pp+0x1650] Type: void?
    // 0xdb1414: cmp             w3, w16
    // 0xdb1418: b.eq            #0xdb1484
    // 0xdb141c: tbnz            w0, #0, #0xdb1438
    // 0xdb1420: r16 = int
    //     0xdb1420: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdb1424: cmp             w3, w16
    // 0xdb1428: b.eq            #0xdb1484
    // 0xdb142c: r16 = num
    //     0xdb142c: ldr             x16, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xdb1430: cmp             w3, w16
    // 0xdb1434: b.eq            #0xdb1484
    // 0xdb1438: r3 = SubtypeTestCache
    //     0xdb1438: add             x3, PP, #0x22, lsl #12  ; [pp+0x22458] SubtypeTestCache
    //     0xdb143c: ldr             x3, [x3, #0x458]
    // 0xdb1440: r30 = Subtype4TestCacheStub
    //     0xdb1440: ldr             lr, [PP, #0x20]  ; [pp+0x20] Stub: Subtype4TestCache (0x5f2a74)
    // 0xdb1444: LoadField: r30 = r30->field_7
    //     0xdb1444: ldur            lr, [lr, #7]
    // 0xdb1448: blr             lr
    // 0xdb144c: cmp             w7, NULL
    // 0xdb1450: b.eq            #0xdb145c
    // 0xdb1454: tbnz            w7, #4, #0xdb147c
    // 0xdb1458: b               #0xdb1484
    // 0xdb145c: r8 = X0 bound Route
    //     0xdb145c: add             x8, PP, #0x22, lsl #12  ; [pp+0x22460] TypeParameter: X0 bound Route
    //     0xdb1460: ldr             x8, [x8, #0x460]
    // 0xdb1464: r3 = SubtypeTestCache
    //     0xdb1464: add             x3, PP, #0x22, lsl #12  ; [pp+0x22468] SubtypeTestCache
    //     0xdb1468: ldr             x3, [x3, #0x468]
    // 0xdb146c: r30 = InstanceOfStub
    //     0xdb146c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xdb1470: LoadField: r30 = r30->field_7
    //     0xdb1470: ldur            lr, [lr, #7]
    // 0xdb1474: blr             lr
    // 0xdb1478: b               #0xdb1488
    // 0xdb147c: r0 = false
    //     0xdb147c: add             x0, NULL, #0x30  ; false
    // 0xdb1480: b               #0xdb1488
    // 0xdb1484: r0 = true
    //     0xdb1484: add             x0, NULL, #0x20  ; true
    // 0xdb1488: tbnz            w0, #4, #0xdb1594
    // 0xdb148c: ldur            x0, [fp, #-0x10]
    // 0xdb1490: LoadField: r3 = r0->field_b
    //     0xdb1490: ldur            w3, [x0, #0xb]
    // 0xdb1494: DecompressPointer r3
    //     0xdb1494: add             x3, x3, HEAP, lsl #32
    // 0xdb1498: mov             x1, x3
    // 0xdb149c: ldur            x2, [fp, #-0x20]
    // 0xdb14a0: stur            x3, [fp, #-8]
    // 0xdb14a4: r0 = _getValueOrData()
    //     0xdb14a4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdb14a8: ldur            x2, [fp, #-8]
    // 0xdb14ac: LoadField: r1 = r2->field_f
    //     0xdb14ac: ldur            w1, [x2, #0xf]
    // 0xdb14b0: DecompressPointer r1
    //     0xdb14b0: add             x1, x1, HEAP, lsl #32
    // 0xdb14b4: cmp             w1, w0
    // 0xdb14b8: b.ne            #0xdb14c4
    // 0xdb14bc: r1 = Null
    //     0xdb14bc: mov             x1, NULL
    // 0xdb14c0: b               #0xdb14c8
    // 0xdb14c4: mov             x1, x0
    // 0xdb14c8: cmp             w1, NULL
    // 0xdb14cc: b.ne            #0xdb14d8
    // 0xdb14d0: r2 = Null
    //     0xdb14d0: mov             x2, NULL
    // 0xdb14d4: b               #0xdb14f8
    // 0xdb14d8: r0 = LoadClassIdInstr(r1)
    //     0xdb14d8: ldur            x0, [x1, #-1]
    //     0xdb14dc: ubfx            x0, x0, #0xc, #0x14
    // 0xdb14e0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdb14e0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdb14e4: r0 = GDT[cid_x0 + 0xd889]()
    //     0xdb14e4: movz            x17, #0xd889
    //     0xdb14e8: add             lr, x0, x17
    //     0xdb14ec: ldr             lr, [x21, lr, lsl #3]
    //     0xdb14f0: blr             lr
    // 0xdb14f4: mov             x2, x0
    // 0xdb14f8: cmp             w2, NULL
    // 0xdb14fc: b.eq            #0xdb1518
    // 0xdb1500: LoadField: r3 = r2->field_7
    //     0xdb1500: ldur            w3, [x2, #7]
    // 0xdb1504: DecompressPointer r3
    //     0xdb1504: add             x3, x3, HEAP, lsl #32
    // 0xdb1508: LoadField: r0 = r2->field_b
    //     0xdb1508: ldur            w0, [x2, #0xb]
    // 0xdb150c: r1 = LoadInt32Instr(r0)
    //     0xdb150c: sbfx            x1, x0, #1, #0x1f
    // 0xdb1510: cmp             x1, #0
    // 0xdb1514: b.gt            #0xdb15a4
    // 0xdb1518: ldur            x0, [fp, #-8]
    // 0xdb151c: mov             x1, x0
    // 0xdb1520: ldur            x2, [fp, #-0x18]
    // 0xdb1524: r0 = _getValueOrData()
    //     0xdb1524: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdb1528: mov             x1, x0
    // 0xdb152c: ldur            x0, [fp, #-8]
    // 0xdb1530: LoadField: r2 = r0->field_f
    //     0xdb1530: ldur            w2, [x0, #0xf]
    // 0xdb1534: DecompressPointer r2
    //     0xdb1534: add             x2, x2, HEAP, lsl #32
    // 0xdb1538: cmp             w2, w1
    // 0xdb153c: b.ne            #0xdb1544
    // 0xdb1540: r1 = Null
    //     0xdb1540: mov             x1, NULL
    // 0xdb1544: cmp             w1, NULL
    // 0xdb1548: b.ne            #0xdb1554
    // 0xdb154c: r2 = Null
    //     0xdb154c: mov             x2, NULL
    // 0xdb1550: b               #0xdb1574
    // 0xdb1554: r0 = LoadClassIdInstr(r1)
    //     0xdb1554: ldur            x0, [x1, #-1]
    //     0xdb1558: ubfx            x0, x0, #0xc, #0x14
    // 0xdb155c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdb155c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdb1560: r0 = GDT[cid_x0 + 0xd889]()
    //     0xdb1560: movz            x17, #0xd889
    //     0xdb1564: add             lr, x0, x17
    //     0xdb1568: ldr             lr, [x21, lr, lsl #3]
    //     0xdb156c: blr             lr
    // 0xdb1570: mov             x2, x0
    // 0xdb1574: cmp             w2, NULL
    // 0xdb1578: b.eq            #0xdb1594
    // 0xdb157c: LoadField: r3 = r2->field_7
    //     0xdb157c: ldur            w3, [x2, #7]
    // 0xdb1580: DecompressPointer r3
    //     0xdb1580: add             x3, x3, HEAP, lsl #32
    // 0xdb1584: LoadField: r0 = r2->field_b
    //     0xdb1584: ldur            w0, [x2, #0xb]
    // 0xdb1588: r1 = LoadInt32Instr(r0)
    //     0xdb1588: sbfx            x1, x0, #1, #0x1f
    // 0xdb158c: cmp             x1, #0
    // 0xdb1590: b.gt            #0xdb1608
    // 0xdb1594: r0 = Null
    //     0xdb1594: mov             x0, NULL
    // 0xdb1598: LeaveFrame
    //     0xdb1598: mov             SP, fp
    //     0xdb159c: ldp             fp, lr, [SP], #0x10
    // 0xdb15a0: ret
    //     0xdb15a0: ret             
    // 0xdb15a4: mov             x0, x1
    // 0xdb15a8: r1 = 0
    //     0xdb15a8: movz            x1, #0
    // 0xdb15ac: cmp             x1, x0
    // 0xdb15b0: b.hs            #0xdb1674
    // 0xdb15b4: LoadField: r0 = r2->field_f
    //     0xdb15b4: ldur            w0, [x2, #0xf]
    // 0xdb15b8: DecompressPointer r0
    //     0xdb15b8: add             x0, x0, HEAP, lsl #32
    // 0xdb15bc: LoadField: r1 = r0->field_f
    //     0xdb15bc: ldur            w1, [x0, #0xf]
    // 0xdb15c0: DecompressPointer r1
    //     0xdb15c0: add             x1, x1, HEAP, lsl #32
    // 0xdb15c4: cmp             w1, NULL
    // 0xdb15c8: b.ne            #0xdb15fc
    // 0xdb15cc: mov             x0, x1
    // 0xdb15d0: mov             x2, x3
    // 0xdb15d4: r1 = Null
    //     0xdb15d4: mov             x1, NULL
    // 0xdb15d8: cmp             w2, NULL
    // 0xdb15dc: b.eq            #0xdb15fc
    // 0xdb15e0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xdb15e0: ldur            w4, [x2, #0x17]
    // 0xdb15e4: DecompressPointer r4
    //     0xdb15e4: add             x4, x4, HEAP, lsl #32
    // 0xdb15e8: r8 = X0
    //     0xdb15e8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xdb15ec: LoadField: r9 = r4->field_7
    //     0xdb15ec: ldur            x9, [x4, #7]
    // 0xdb15f0: r3 = Null
    //     0xdb15f0: add             x3, PP, #0x22, lsl #12  ; [pp+0x22470] Null
    //     0xdb15f4: ldr             x3, [x3, #0x470]
    // 0xdb15f8: blr             x9
    // 0xdb15fc: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0xdb15fc: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0xdb1600: r0 = Throw()
    //     0xdb1600: bl              #0xec04b8  ; ThrowStub
    // 0xdb1604: brk             #0
    // 0xdb1608: mov             x0, x1
    // 0xdb160c: r1 = 0
    //     0xdb160c: movz            x1, #0
    // 0xdb1610: cmp             x1, x0
    // 0xdb1614: b.hs            #0xdb1678
    // 0xdb1618: LoadField: r0 = r2->field_f
    //     0xdb1618: ldur            w0, [x2, #0xf]
    // 0xdb161c: DecompressPointer r0
    //     0xdb161c: add             x0, x0, HEAP, lsl #32
    // 0xdb1620: LoadField: r1 = r0->field_f
    //     0xdb1620: ldur            w1, [x0, #0xf]
    // 0xdb1624: DecompressPointer r1
    //     0xdb1624: add             x1, x1, HEAP, lsl #32
    // 0xdb1628: cmp             w1, NULL
    // 0xdb162c: b.ne            #0xdb1660
    // 0xdb1630: mov             x0, x1
    // 0xdb1634: mov             x2, x3
    // 0xdb1638: r1 = Null
    //     0xdb1638: mov             x1, NULL
    // 0xdb163c: cmp             w2, NULL
    // 0xdb1640: b.eq            #0xdb1660
    // 0xdb1644: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xdb1644: ldur            w4, [x2, #0x17]
    // 0xdb1648: DecompressPointer r4
    //     0xdb1648: add             x4, x4, HEAP, lsl #32
    // 0xdb164c: r8 = X0
    //     0xdb164c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xdb1650: LoadField: r9 = r4->field_7
    //     0xdb1650: ldur            x9, [x4, #7]
    // 0xdb1654: r3 = Null
    //     0xdb1654: add             x3, PP, #0x22, lsl #12  ; [pp+0x22480] Null
    //     0xdb1658: ldr             x3, [x3, #0x480]
    // 0xdb165c: blr             x9
    // 0xdb1660: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0xdb1660: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0xdb1664: r0 = Throw()
    //     0xdb1664: bl              #0xec04b8  ; ThrowStub
    // 0xdb1668: brk             #0
    // 0xdb166c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb166c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb1670: b               #0xdb1320
    // 0xdb1674: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xdb1674: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xdb1678: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xdb1678: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ didPush(/* No info */) {
    // ** addr: 0xdb1bd8, size: 0x258
    // 0xdb1bd8: EnterFrame
    //     0xdb1bd8: stp             fp, lr, [SP, #-0x10]!
    //     0xdb1bdc: mov             fp, SP
    // 0xdb1be0: AllocStack(0x18)
    //     0xdb1be0: sub             SP, SP, #0x18
    // 0xdb1be4: SetupParameters(RouteObserver<X0 bound Route> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xdb1be4: mov             x4, x1
    //     0xdb1be8: mov             x0, x2
    //     0xdb1bec: stur            x1, [fp, #-0x10]
    //     0xdb1bf0: stur            x3, [fp, #-0x18]
    // 0xdb1bf4: CheckStackOverflow
    //     0xdb1bf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb1bf8: cmp             SP, x16
    //     0xdb1bfc: b.ls            #0xdb1e28
    // 0xdb1c00: LoadField: r5 = r4->field_7
    //     0xdb1c00: ldur            w5, [x4, #7]
    // 0xdb1c04: DecompressPointer r5
    //     0xdb1c04: add             x5, x5, HEAP, lsl #32
    // 0xdb1c08: mov             x2, x5
    // 0xdb1c0c: stur            x5, [fp, #-8]
    // 0xdb1c10: r1 = Null
    //     0xdb1c10: mov             x1, NULL
    // 0xdb1c14: cmp             w2, NULL
    // 0xdb1c18: b.eq            #0xdb1cb0
    // 0xdb1c1c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xdb1c1c: ldur            w3, [x2, #0x17]
    // 0xdb1c20: DecompressPointer r3
    //     0xdb1c20: add             x3, x3, HEAP, lsl #32
    // 0xdb1c24: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xdb1c28: cmp             w3, w16
    // 0xdb1c2c: b.eq            #0xdb1cb0
    // 0xdb1c30: r16 = Object?
    //     0xdb1c30: ldr             x16, [PP, #0x1648]  ; [pp+0x1648] Type: Object?
    // 0xdb1c34: cmp             w3, w16
    // 0xdb1c38: b.eq            #0xdb1cb0
    // 0xdb1c3c: r16 = void?
    //     0xdb1c3c: ldr             x16, [PP, #0x1650]  ; [pp+0x1650] Type: void?
    // 0xdb1c40: cmp             w3, w16
    // 0xdb1c44: b.eq            #0xdb1cb0
    // 0xdb1c48: tbnz            w0, #0, #0xdb1c64
    // 0xdb1c4c: r16 = int
    //     0xdb1c4c: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdb1c50: cmp             w3, w16
    // 0xdb1c54: b.eq            #0xdb1cb0
    // 0xdb1c58: r16 = num
    //     0xdb1c58: ldr             x16, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xdb1c5c: cmp             w3, w16
    // 0xdb1c60: b.eq            #0xdb1cb0
    // 0xdb1c64: r3 = SubtypeTestCache
    //     0xdb1c64: add             x3, PP, #0x22, lsl #12  ; [pp+0x223f8] SubtypeTestCache
    //     0xdb1c68: ldr             x3, [x3, #0x3f8]
    // 0xdb1c6c: r30 = Subtype4TestCacheStub
    //     0xdb1c6c: ldr             lr, [PP, #0x20]  ; [pp+0x20] Stub: Subtype4TestCache (0x5f2a74)
    // 0xdb1c70: LoadField: r30 = r30->field_7
    //     0xdb1c70: ldur            lr, [lr, #7]
    // 0xdb1c74: blr             lr
    // 0xdb1c78: cmp             w7, NULL
    // 0xdb1c7c: b.eq            #0xdb1c88
    // 0xdb1c80: tbnz            w7, #4, #0xdb1ca8
    // 0xdb1c84: b               #0xdb1cb0
    // 0xdb1c88: r8 = X0 bound Route
    //     0xdb1c88: add             x8, PP, #0x22, lsl #12  ; [pp+0x22400] TypeParameter: X0 bound Route
    //     0xdb1c8c: ldr             x8, [x8, #0x400]
    // 0xdb1c90: r3 = SubtypeTestCache
    //     0xdb1c90: add             x3, PP, #0x22, lsl #12  ; [pp+0x22408] SubtypeTestCache
    //     0xdb1c94: ldr             x3, [x3, #0x408]
    // 0xdb1c98: r30 = InstanceOfStub
    //     0xdb1c98: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xdb1c9c: LoadField: r30 = r30->field_7
    //     0xdb1c9c: ldur            lr, [lr, #7]
    // 0xdb1ca0: blr             lr
    // 0xdb1ca4: b               #0xdb1cb4
    // 0xdb1ca8: r0 = false
    //     0xdb1ca8: add             x0, NULL, #0x30  ; false
    // 0xdb1cac: b               #0xdb1cb4
    // 0xdb1cb0: r0 = true
    //     0xdb1cb0: add             x0, NULL, #0x20  ; true
    // 0xdb1cb4: tbnz            w0, #4, #0xdb1dec
    // 0xdb1cb8: ldur            x0, [fp, #-0x18]
    // 0xdb1cbc: ldur            x2, [fp, #-8]
    // 0xdb1cc0: r1 = Null
    //     0xdb1cc0: mov             x1, NULL
    // 0xdb1cc4: cmp             w2, NULL
    // 0xdb1cc8: b.eq            #0xdb1d60
    // 0xdb1ccc: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xdb1ccc: ldur            w3, [x2, #0x17]
    // 0xdb1cd0: DecompressPointer r3
    //     0xdb1cd0: add             x3, x3, HEAP, lsl #32
    // 0xdb1cd4: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xdb1cd8: cmp             w3, w16
    // 0xdb1cdc: b.eq            #0xdb1d60
    // 0xdb1ce0: r16 = Object?
    //     0xdb1ce0: ldr             x16, [PP, #0x1648]  ; [pp+0x1648] Type: Object?
    // 0xdb1ce4: cmp             w3, w16
    // 0xdb1ce8: b.eq            #0xdb1d60
    // 0xdb1cec: r16 = void?
    //     0xdb1cec: ldr             x16, [PP, #0x1650]  ; [pp+0x1650] Type: void?
    // 0xdb1cf0: cmp             w3, w16
    // 0xdb1cf4: b.eq            #0xdb1d60
    // 0xdb1cf8: tbnz            w0, #0, #0xdb1d14
    // 0xdb1cfc: r16 = int
    //     0xdb1cfc: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdb1d00: cmp             w3, w16
    // 0xdb1d04: b.eq            #0xdb1d60
    // 0xdb1d08: r16 = num
    //     0xdb1d08: ldr             x16, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xdb1d0c: cmp             w3, w16
    // 0xdb1d10: b.eq            #0xdb1d60
    // 0xdb1d14: r3 = SubtypeTestCache
    //     0xdb1d14: add             x3, PP, #0x22, lsl #12  ; [pp+0x22410] SubtypeTestCache
    //     0xdb1d18: ldr             x3, [x3, #0x410]
    // 0xdb1d1c: r30 = Subtype4TestCacheStub
    //     0xdb1d1c: ldr             lr, [PP, #0x20]  ; [pp+0x20] Stub: Subtype4TestCache (0x5f2a74)
    // 0xdb1d20: LoadField: r30 = r30->field_7
    //     0xdb1d20: ldur            lr, [lr, #7]
    // 0xdb1d24: blr             lr
    // 0xdb1d28: cmp             w7, NULL
    // 0xdb1d2c: b.eq            #0xdb1d38
    // 0xdb1d30: tbnz            w7, #4, #0xdb1d58
    // 0xdb1d34: b               #0xdb1d60
    // 0xdb1d38: r8 = X0 bound Route
    //     0xdb1d38: add             x8, PP, #0x22, lsl #12  ; [pp+0x22418] TypeParameter: X0 bound Route
    //     0xdb1d3c: ldr             x8, [x8, #0x418]
    // 0xdb1d40: r3 = SubtypeTestCache
    //     0xdb1d40: add             x3, PP, #0x22, lsl #12  ; [pp+0x22420] SubtypeTestCache
    //     0xdb1d44: ldr             x3, [x3, #0x420]
    // 0xdb1d48: r30 = InstanceOfStub
    //     0xdb1d48: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xdb1d4c: LoadField: r30 = r30->field_7
    //     0xdb1d4c: ldur            lr, [lr, #7]
    // 0xdb1d50: blr             lr
    // 0xdb1d54: b               #0xdb1d64
    // 0xdb1d58: r0 = false
    //     0xdb1d58: add             x0, NULL, #0x30  ; false
    // 0xdb1d5c: b               #0xdb1d64
    // 0xdb1d60: r0 = true
    //     0xdb1d60: add             x0, NULL, #0x20  ; true
    // 0xdb1d64: tbnz            w0, #4, #0xdb1dec
    // 0xdb1d68: ldur            x0, [fp, #-0x10]
    // 0xdb1d6c: LoadField: r3 = r0->field_b
    //     0xdb1d6c: ldur            w3, [x0, #0xb]
    // 0xdb1d70: DecompressPointer r3
    //     0xdb1d70: add             x3, x3, HEAP, lsl #32
    // 0xdb1d74: mov             x1, x3
    // 0xdb1d78: ldur            x2, [fp, #-0x18]
    // 0xdb1d7c: stur            x3, [fp, #-8]
    // 0xdb1d80: r0 = _getValueOrData()
    //     0xdb1d80: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdb1d84: mov             x1, x0
    // 0xdb1d88: ldur            x0, [fp, #-8]
    // 0xdb1d8c: LoadField: r2 = r0->field_f
    //     0xdb1d8c: ldur            w2, [x0, #0xf]
    // 0xdb1d90: DecompressPointer r2
    //     0xdb1d90: add             x2, x2, HEAP, lsl #32
    // 0xdb1d94: cmp             w2, w1
    // 0xdb1d98: b.ne            #0xdb1da0
    // 0xdb1d9c: r1 = Null
    //     0xdb1d9c: mov             x1, NULL
    // 0xdb1da0: cmp             w1, NULL
    // 0xdb1da4: b.eq            #0xdb1dec
    // 0xdb1da8: r0 = LoadClassIdInstr(r1)
    //     0xdb1da8: ldur            x0, [x1, #-1]
    //     0xdb1dac: ubfx            x0, x0, #0xc, #0x14
    // 0xdb1db0: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xdb1db0: movz            x17, #0xd35d
    //     0xdb1db4: add             lr, x0, x17
    //     0xdb1db8: ldr             lr, [x21, lr, lsl #3]
    //     0xdb1dbc: blr             lr
    // 0xdb1dc0: mov             x2, x0
    // 0xdb1dc4: stur            x2, [fp, #-8]
    // 0xdb1dc8: r0 = LoadClassIdInstr(r2)
    //     0xdb1dc8: ldur            x0, [x2, #-1]
    //     0xdb1dcc: ubfx            x0, x0, #0xc, #0x14
    // 0xdb1dd0: mov             x1, x2
    // 0xdb1dd4: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xdb1dd4: movz            x17, #0x292d
    //     0xdb1dd8: movk            x17, #0x1, lsl #16
    //     0xdb1ddc: add             lr, x0, x17
    //     0xdb1de0: ldr             lr, [x21, lr, lsl #3]
    //     0xdb1de4: blr             lr
    // 0xdb1de8: tbz             w0, #4, #0xdb1dfc
    // 0xdb1dec: r0 = Null
    //     0xdb1dec: mov             x0, NULL
    // 0xdb1df0: LeaveFrame
    //     0xdb1df0: mov             SP, fp
    //     0xdb1df4: ldp             fp, lr, [SP], #0x10
    // 0xdb1df8: ret
    //     0xdb1df8: ret             
    // 0xdb1dfc: ldur            x1, [fp, #-8]
    // 0xdb1e00: r0 = LoadClassIdInstr(r1)
    //     0xdb1e00: ldur            x0, [x1, #-1]
    //     0xdb1e04: ubfx            x0, x0, #0xc, #0x14
    // 0xdb1e08: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xdb1e08: movz            x17, #0x384d
    //     0xdb1e0c: movk            x17, #0x1, lsl #16
    //     0xdb1e10: add             lr, x0, x17
    //     0xdb1e14: ldr             lr, [x21, lr, lsl #3]
    //     0xdb1e18: blr             lr
    // 0xdb1e1c: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0xdb1e1c: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0xdb1e20: r0 = Throw()
    //     0xdb1e20: bl              #0xec04b8  ; ThrowStub
    // 0xdb1e24: brk             #0
    // 0xdb1e28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb1e28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb1e2c: b               #0xdb1c00
  }
}

// class id: 3920, size: 0x18, field offset: 0x14
class _DismissModalAction extends DismissAction {

  _ invoke(/* No info */) {
    // ** addr: 0xa53bfc, size: 0x50
    // 0xa53bfc: EnterFrame
    //     0xa53bfc: stp             fp, lr, [SP, #-0x10]!
    //     0xa53c00: mov             fp, SP
    // 0xa53c04: AllocStack(0x10)
    //     0xa53c04: sub             SP, SP, #0x10
    // 0xa53c08: CheckStackOverflow
    //     0xa53c08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa53c0c: cmp             SP, x16
    //     0xa53c10: b.ls            #0xa53c44
    // 0xa53c14: LoadField: r0 = r1->field_13
    //     0xa53c14: ldur            w0, [x1, #0x13]
    // 0xa53c18: DecompressPointer r0
    //     0xa53c18: add             x0, x0, HEAP, lsl #32
    // 0xa53c1c: mov             x1, x0
    // 0xa53c20: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa53c20: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa53c24: r0 = of()
    //     0xa53c24: bl              #0x917a34  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xa53c28: r16 = <Object?>
    //     0xa53c28: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xa53c2c: stp             x0, x16, [SP]
    // 0xa53c30: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa53c30: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa53c34: r0 = maybePop()
    //     0xa53c34: bl              #0x67350c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::maybePop
    // 0xa53c38: LeaveFrame
    //     0xa53c38: mov             SP, fp
    //     0xa53c3c: ldp             fp, lr, [SP], #0x10
    // 0xa53c40: ret
    //     0xa53c40: ret             
    // 0xa53c44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa53c44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa53c48: b               #0xa53c14
  }
  _ isEnabled(/* No info */) {
    // ** addr: 0xa8b024, size: 0x6c
    // 0xa8b024: EnterFrame
    //     0xa8b024: stp             fp, lr, [SP, #-0x10]!
    //     0xa8b028: mov             fp, SP
    // 0xa8b02c: AllocStack(0x10)
    //     0xa8b02c: sub             SP, SP, #0x10
    // 0xa8b030: CheckStackOverflow
    //     0xa8b030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8b034: cmp             SP, x16
    //     0xa8b038: b.ls            #0xa8b084
    // 0xa8b03c: LoadField: r0 = r1->field_13
    //     0xa8b03c: ldur            w0, [x1, #0x13]
    // 0xa8b040: DecompressPointer r0
    //     0xa8b040: add             x0, x0, HEAP, lsl #32
    // 0xa8b044: stp             x0, NULL, [SP]
    // 0xa8b048: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa8b048: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa8b04c: r0 = of()
    //     0xa8b04c: bl              #0x92c598  ; [package:flutter/src/widgets/routes.dart] ModalRoute::of
    // 0xa8b050: cmp             w0, NULL
    // 0xa8b054: b.eq            #0xa8b08c
    // 0xa8b058: r1 = LoadClassIdInstr(r0)
    //     0xa8b058: ldur            x1, [x0, #-1]
    //     0xa8b05c: ubfx            x1, x1, #0xc, #0x14
    // 0xa8b060: mov             x16, x0
    // 0xa8b064: mov             x0, x1
    // 0xa8b068: mov             x1, x16
    // 0xa8b06c: r0 = GDT[cid_x0 + -0xff2]()
    //     0xa8b06c: sub             lr, x0, #0xff2
    //     0xa8b070: ldr             lr, [x21, lr, lsl #3]
    //     0xa8b074: blr             lr
    // 0xa8b078: LeaveFrame
    //     0xa8b078: mov             SP, fp
    //     0xa8b07c: ldp             fp, lr, [SP], #0x10
    // 0xa8b080: ret
    //     0xa8b080: ret             
    // 0xa8b084: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8b084: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8b088: b               #0xa8b03c
    // 0xa8b08c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8b08c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4180, size: 0x24, field offset: 0x14
class _ModalScopeState<C1X0> extends State<C1X0> {

  late Listenable _listenable; // offset: 0x18

  _ _routeSetState(/* No info */) {
    // ** addr: 0x650aac, size: 0xe8
    // 0x650aac: EnterFrame
    //     0x650aac: stp             fp, lr, [SP, #-0x10]!
    //     0x650ab0: mov             fp, SP
    // 0x650ab4: AllocStack(0x10)
    //     0x650ab4: sub             SP, SP, #0x10
    // 0x650ab8: SetupParameters(_ModalScopeState<C1X0> this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x650ab8: mov             x0, x1
    //     0x650abc: stur            x1, [fp, #-8]
    //     0x650ac0: stur            x2, [fp, #-0x10]
    // 0x650ac4: CheckStackOverflow
    //     0x650ac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x650ac8: cmp             SP, x16
    //     0x650acc: b.ls            #0x650b80
    // 0x650ad0: LoadField: r1 = r0->field_b
    //     0x650ad0: ldur            w1, [x0, #0xb]
    // 0x650ad4: DecompressPointer r1
    //     0x650ad4: add             x1, x1, HEAP, lsl #32
    // 0x650ad8: cmp             w1, NULL
    // 0x650adc: b.eq            #0x650b88
    // 0x650ae0: LoadField: r3 = r1->field_f
    //     0x650ae0: ldur            w3, [x1, #0xf]
    // 0x650ae4: DecompressPointer r3
    //     0x650ae4: add             x3, x3, HEAP, lsl #32
    // 0x650ae8: mov             x1, x3
    // 0x650aec: r0 = isCurrent()
    //     0x650aec: bl              #0x650678  ; [package:flutter/src/widgets/navigator.dart] Route::isCurrent
    // 0x650af0: tbnz            w0, #4, #0x650b64
    // 0x650af4: ldur            x1, [fp, #-8]
    // 0x650af8: r0 = _shouldIgnoreFocusRequest()
    //     0x650af8: bl              #0x652314  ; [package:flutter/src/widgets/routes.dart] _ModalScopeState::_shouldIgnoreFocusRequest
    // 0x650afc: tbz             w0, #4, #0x650b64
    // 0x650b00: ldur            x1, [fp, #-8]
    // 0x650b04: r0 = _shouldRequestFocus()
    //     0x650b04: bl              #0x65227c  ; [package:flutter/src/widgets/routes.dart] _ModalScopeState::_shouldRequestFocus
    // 0x650b08: tbnz            w0, #4, #0x650b64
    // 0x650b0c: ldur            x0, [fp, #-8]
    // 0x650b10: LoadField: r1 = r0->field_b
    //     0x650b10: ldur            w1, [x0, #0xb]
    // 0x650b14: DecompressPointer r1
    //     0x650b14: add             x1, x1, HEAP, lsl #32
    // 0x650b18: cmp             w1, NULL
    // 0x650b1c: b.eq            #0x650b8c
    // 0x650b20: LoadField: r2 = r1->field_f
    //     0x650b20: ldur            w2, [x1, #0xf]
    // 0x650b24: DecompressPointer r2
    //     0x650b24: add             x2, x2, HEAP, lsl #32
    // 0x650b28: LoadField: r1 = r2->field_f
    //     0x650b28: ldur            w1, [x2, #0xf]
    // 0x650b2c: DecompressPointer r1
    //     0x650b2c: add             x1, x1, HEAP, lsl #32
    // 0x650b30: cmp             w1, NULL
    // 0x650b34: b.eq            #0x650b90
    // 0x650b38: LoadField: r2 = r1->field_43
    //     0x650b38: ldur            w2, [x1, #0x43]
    // 0x650b3c: DecompressPointer r2
    //     0x650b3c: add             x2, x2, HEAP, lsl #32
    // 0x650b40: mov             x1, x2
    // 0x650b44: r0 = enclosingScope()
    //     0x650b44: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x650b48: cmp             w0, NULL
    // 0x650b4c: b.eq            #0x650b64
    // 0x650b50: ldur            x3, [fp, #-8]
    // 0x650b54: LoadField: r2 = r3->field_1b
    //     0x650b54: ldur            w2, [x3, #0x1b]
    // 0x650b58: DecompressPointer r2
    //     0x650b58: add             x2, x2, HEAP, lsl #32
    // 0x650b5c: mov             x1, x0
    // 0x650b60: r0 = setFirstFocus()
    //     0x650b60: bl              #0x650c78  ; [package:flutter/src/widgets/focus_manager.dart] FocusScopeNode::setFirstFocus
    // 0x650b64: ldur            x1, [fp, #-8]
    // 0x650b68: ldur            x2, [fp, #-0x10]
    // 0x650b6c: r0 = setState()
    //     0x650b6c: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x650b70: r0 = Null
    //     0x650b70: mov             x0, NULL
    // 0x650b74: LeaveFrame
    //     0x650b74: mov             SP, fp
    //     0x650b78: ldp             fp, lr, [SP], #0x10
    // 0x650b7c: ret
    //     0x650b7c: ret             
    // 0x650b80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x650b80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x650b84: b               #0x650ad0
    // 0x650b88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x650b88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x650b8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x650b8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x650b90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x650b90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _shouldRequestFocus(/* No info */) {
    // ** addr: 0x65227c, size: 0x48
    // 0x65227c: EnterFrame
    //     0x65227c: stp             fp, lr, [SP, #-0x10]!
    //     0x652280: mov             fp, SP
    // 0x652284: CheckStackOverflow
    //     0x652284: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x652288: cmp             SP, x16
    //     0x65228c: b.ls            #0x6522b8
    // 0x652290: LoadField: r0 = r1->field_b
    //     0x652290: ldur            w0, [x1, #0xb]
    // 0x652294: DecompressPointer r0
    //     0x652294: add             x0, x0, HEAP, lsl #32
    // 0x652298: cmp             w0, NULL
    // 0x65229c: b.eq            #0x6522c0
    // 0x6522a0: LoadField: r1 = r0->field_f
    //     0x6522a0: ldur            w1, [x0, #0xf]
    // 0x6522a4: DecompressPointer r1
    //     0x6522a4: add             x1, x1, HEAP, lsl #32
    // 0x6522a8: r0 = requestFocus()
    //     0x6522a8: bl              #0x6522c4  ; [package:flutter/src/widgets/navigator.dart] Route::requestFocus
    // 0x6522ac: LeaveFrame
    //     0x6522ac: mov             SP, fp
    //     0x6522b0: ldp             fp, lr, [SP], #0x10
    // 0x6522b4: ret
    //     0x6522b4: ret             
    // 0x6522b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6522b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6522bc: b               #0x652290
    // 0x6522c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6522c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _shouldIgnoreFocusRequest(/* No info */) {
    // ** addr: 0x652314, size: 0xc8
    // 0x652314: EnterFrame
    //     0x652314: stp             fp, lr, [SP, #-0x10]!
    //     0x652318: mov             fp, SP
    // 0x65231c: AllocStack(0x8)
    //     0x65231c: sub             SP, SP, #8
    // 0x652320: SetupParameters(_ModalScopeState<C1X0> this /* r1 => r0, fp-0x8 */)
    //     0x652320: mov             x0, x1
    //     0x652324: stur            x1, [fp, #-8]
    // 0x652328: CheckStackOverflow
    //     0x652328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65232c: cmp             SP, x16
    //     0x652330: b.ls            #0x6523cc
    // 0x652334: LoadField: r1 = r0->field_b
    //     0x652334: ldur            w1, [x0, #0xb]
    // 0x652338: DecompressPointer r1
    //     0x652338: add             x1, x1, HEAP, lsl #32
    // 0x65233c: cmp             w1, NULL
    // 0x652340: b.eq            #0x6523d4
    // 0x652344: LoadField: r2 = r1->field_f
    //     0x652344: ldur            w2, [x1, #0xf]
    // 0x652348: DecompressPointer r2
    //     0x652348: add             x2, x2, HEAP, lsl #32
    // 0x65234c: LoadField: r1 = r2->field_67
    //     0x65234c: ldur            w1, [x2, #0x67]
    // 0x652350: DecompressPointer r1
    //     0x652350: add             x1, x1, HEAP, lsl #32
    // 0x652354: cmp             w1, NULL
    // 0x652358: b.eq            #0x652378
    // 0x65235c: r0 = status()
    //     0x65235c: bl              #0xd371b8  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::status
    // 0x652360: r16 = Instance_AnimationStatus
    //     0x652360: ldr             x16, [PP, #0x4eb8]  ; [pp+0x4eb8] Obj!AnimationStatus@e372a1
    // 0x652364: cmp             w0, w16
    // 0x652368: b.ne            #0x652374
    // 0x65236c: r0 = true
    //     0x65236c: add             x0, NULL, #0x20  ; true
    // 0x652370: b               #0x6523c0
    // 0x652374: ldur            x0, [fp, #-8]
    // 0x652378: LoadField: r1 = r0->field_b
    //     0x652378: ldur            w1, [x0, #0xb]
    // 0x65237c: DecompressPointer r1
    //     0x65237c: add             x1, x1, HEAP, lsl #32
    // 0x652380: cmp             w1, NULL
    // 0x652384: b.eq            #0x6523d8
    // 0x652388: LoadField: r0 = r1->field_f
    //     0x652388: ldur            w0, [x1, #0xf]
    // 0x65238c: DecompressPointer r0
    //     0x65238c: add             x0, x0, HEAP, lsl #32
    // 0x652390: LoadField: r1 = r0->field_f
    //     0x652390: ldur            w1, [x0, #0xf]
    // 0x652394: DecompressPointer r1
    //     0x652394: add             x1, x1, HEAP, lsl #32
    // 0x652398: cmp             w1, NULL
    // 0x65239c: b.ne            #0x6523a8
    // 0x6523a0: r1 = Null
    //     0x6523a0: mov             x1, NULL
    // 0x6523a4: b               #0x6523b0
    // 0x6523a8: r0 = userGestureInProgress()
    //     0x6523a8: bl              #0x6523dc  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::userGestureInProgress
    // 0x6523ac: mov             x1, x0
    // 0x6523b0: cmp             w1, NULL
    // 0x6523b4: b.ne            #0x6523bc
    // 0x6523b8: r1 = false
    //     0x6523b8: add             x1, NULL, #0x30  ; false
    // 0x6523bc: mov             x0, x1
    // 0x6523c0: LeaveFrame
    //     0x6523c0: mov             SP, fp
    //     0x6523c4: ldp             fp, lr, [SP], #0x10
    // 0x6523c8: ret
    //     0x6523c8: ret             
    // 0x6523cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6523cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6523d0: b               #0x652334
    // 0x6523d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6523d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6523d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6523d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x945b5c, size: 0x1d0
    // 0x945b5c: EnterFrame
    //     0x945b5c: stp             fp, lr, [SP, #-0x10]!
    //     0x945b60: mov             fp, SP
    // 0x945b64: AllocStack(0x20)
    //     0x945b64: sub             SP, SP, #0x20
    // 0x945b68: SetupParameters(_ModalScopeState<C1X0> this /* r1 => r0, fp-0x8 */)
    //     0x945b68: mov             x0, x1
    //     0x945b6c: stur            x1, [fp, #-8]
    // 0x945b70: CheckStackOverflow
    //     0x945b70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x945b74: cmp             SP, x16
    //     0x945b78: b.ls            #0x945d1c
    // 0x945b7c: r1 = <Listenable>
    //     0x945b7c: add             x1, PP, #0x25, lsl #12  ; [pp+0x254c8] TypeArguments: <Listenable>
    //     0x945b80: ldr             x1, [x1, #0x4c8]
    // 0x945b84: r2 = 0
    //     0x945b84: movz            x2, #0
    // 0x945b88: r0 = _GrowableList()
    //     0x945b88: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x945b8c: mov             x2, x0
    // 0x945b90: ldur            x0, [fp, #-8]
    // 0x945b94: stur            x2, [fp, #-0x20]
    // 0x945b98: LoadField: r1 = r0->field_b
    //     0x945b98: ldur            w1, [x0, #0xb]
    // 0x945b9c: DecompressPointer r1
    //     0x945b9c: add             x1, x1, HEAP, lsl #32
    // 0x945ba0: cmp             w1, NULL
    // 0x945ba4: b.eq            #0x945d24
    // 0x945ba8: LoadField: r3 = r1->field_f
    //     0x945ba8: ldur            w3, [x1, #0xf]
    // 0x945bac: DecompressPointer r3
    //     0x945bac: add             x3, x3, HEAP, lsl #32
    // 0x945bb0: LoadField: r4 = r3->field_67
    //     0x945bb0: ldur            w4, [x3, #0x67]
    // 0x945bb4: DecompressPointer r4
    //     0x945bb4: add             x4, x4, HEAP, lsl #32
    // 0x945bb8: stur            x4, [fp, #-0x18]
    // 0x945bbc: cmp             w4, NULL
    // 0x945bc0: b.eq            #0x945c38
    // 0x945bc4: LoadField: r1 = r2->field_b
    //     0x945bc4: ldur            w1, [x2, #0xb]
    // 0x945bc8: LoadField: r3 = r2->field_f
    //     0x945bc8: ldur            w3, [x2, #0xf]
    // 0x945bcc: DecompressPointer r3
    //     0x945bcc: add             x3, x3, HEAP, lsl #32
    // 0x945bd0: LoadField: r5 = r3->field_b
    //     0x945bd0: ldur            w5, [x3, #0xb]
    // 0x945bd4: r3 = LoadInt32Instr(r1)
    //     0x945bd4: sbfx            x3, x1, #1, #0x1f
    // 0x945bd8: stur            x3, [fp, #-0x10]
    // 0x945bdc: r1 = LoadInt32Instr(r5)
    //     0x945bdc: sbfx            x1, x5, #1, #0x1f
    // 0x945be0: cmp             x3, x1
    // 0x945be4: b.ne            #0x945bf0
    // 0x945be8: mov             x1, x2
    // 0x945bec: r0 = _growToNextCapacity()
    //     0x945bec: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x945bf0: ldur            x2, [fp, #-0x20]
    // 0x945bf4: ldur            x3, [fp, #-0x10]
    // 0x945bf8: add             x0, x3, #1
    // 0x945bfc: lsl             x1, x0, #1
    // 0x945c00: StoreField: r2->field_b = r1
    //     0x945c00: stur            w1, [x2, #0xb]
    // 0x945c04: LoadField: r1 = r2->field_f
    //     0x945c04: ldur            w1, [x2, #0xf]
    // 0x945c08: DecompressPointer r1
    //     0x945c08: add             x1, x1, HEAP, lsl #32
    // 0x945c0c: ldur            x0, [fp, #-0x18]
    // 0x945c10: ArrayStore: r1[r3] = r0  ; List_4
    //     0x945c10: add             x25, x1, x3, lsl #2
    //     0x945c14: add             x25, x25, #0xf
    //     0x945c18: str             w0, [x25]
    //     0x945c1c: tbz             w0, #0, #0x945c38
    //     0x945c20: ldurb           w16, [x1, #-1]
    //     0x945c24: ldurb           w17, [x0, #-1]
    //     0x945c28: and             x16, x17, x16, lsr #2
    //     0x945c2c: tst             x16, HEAP, lsr #32
    //     0x945c30: b.eq            #0x945c38
    //     0x945c34: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x945c38: ldur            x0, [fp, #-8]
    // 0x945c3c: LoadField: r1 = r0->field_b
    //     0x945c3c: ldur            w1, [x0, #0xb]
    // 0x945c40: DecompressPointer r1
    //     0x945c40: add             x1, x1, HEAP, lsl #32
    // 0x945c44: cmp             w1, NULL
    // 0x945c48: b.eq            #0x945d28
    // 0x945c4c: LoadField: r3 = r1->field_f
    //     0x945c4c: ldur            w3, [x1, #0xf]
    // 0x945c50: DecompressPointer r3
    //     0x945c50: add             x3, x3, HEAP, lsl #32
    // 0x945c54: LoadField: r4 = r3->field_6b
    //     0x945c54: ldur            w4, [x3, #0x6b]
    // 0x945c58: DecompressPointer r4
    //     0x945c58: add             x4, x4, HEAP, lsl #32
    // 0x945c5c: stur            x4, [fp, #-0x18]
    // 0x945c60: cmp             w4, NULL
    // 0x945c64: b.eq            #0x945cdc
    // 0x945c68: LoadField: r1 = r2->field_b
    //     0x945c68: ldur            w1, [x2, #0xb]
    // 0x945c6c: LoadField: r3 = r2->field_f
    //     0x945c6c: ldur            w3, [x2, #0xf]
    // 0x945c70: DecompressPointer r3
    //     0x945c70: add             x3, x3, HEAP, lsl #32
    // 0x945c74: LoadField: r5 = r3->field_b
    //     0x945c74: ldur            w5, [x3, #0xb]
    // 0x945c78: r3 = LoadInt32Instr(r1)
    //     0x945c78: sbfx            x3, x1, #1, #0x1f
    // 0x945c7c: stur            x3, [fp, #-0x10]
    // 0x945c80: r1 = LoadInt32Instr(r5)
    //     0x945c80: sbfx            x1, x5, #1, #0x1f
    // 0x945c84: cmp             x3, x1
    // 0x945c88: b.ne            #0x945c94
    // 0x945c8c: mov             x1, x2
    // 0x945c90: r0 = _growToNextCapacity()
    //     0x945c90: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x945c94: ldur            x2, [fp, #-0x20]
    // 0x945c98: ldur            x3, [fp, #-0x10]
    // 0x945c9c: add             x0, x3, #1
    // 0x945ca0: lsl             x1, x0, #1
    // 0x945ca4: StoreField: r2->field_b = r1
    //     0x945ca4: stur            w1, [x2, #0xb]
    // 0x945ca8: LoadField: r1 = r2->field_f
    //     0x945ca8: ldur            w1, [x2, #0xf]
    // 0x945cac: DecompressPointer r1
    //     0x945cac: add             x1, x1, HEAP, lsl #32
    // 0x945cb0: ldur            x0, [fp, #-0x18]
    // 0x945cb4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x945cb4: add             x25, x1, x3, lsl #2
    //     0x945cb8: add             x25, x25, #0xf
    //     0x945cbc: str             w0, [x25]
    //     0x945cc0: tbz             w0, #0, #0x945cdc
    //     0x945cc4: ldurb           w16, [x1, #-1]
    //     0x945cc8: ldurb           w17, [x0, #-1]
    //     0x945ccc: and             x16, x17, x16, lsr #2
    //     0x945cd0: tst             x16, HEAP, lsr #32
    //     0x945cd4: b.eq            #0x945cdc
    //     0x945cd8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x945cdc: ldur            x0, [fp, #-8]
    // 0x945ce0: r0 = _MergingListenable()
    //     0x945ce0: bl              #0x943dc4  ; Allocate_MergingListenableStub -> _MergingListenable (size=0xc)
    // 0x945ce4: ldur            x1, [fp, #-0x20]
    // 0x945ce8: StoreField: r0->field_7 = r1
    //     0x945ce8: stur            w1, [x0, #7]
    // 0x945cec: ldur            x1, [fp, #-8]
    // 0x945cf0: ArrayStore: r1[0] = r0  ; List_4
    //     0x945cf0: stur            w0, [x1, #0x17]
    //     0x945cf4: ldurb           w16, [x1, #-1]
    //     0x945cf8: ldurb           w17, [x0, #-1]
    //     0x945cfc: and             x16, x17, x16, lsr #2
    //     0x945d00: tst             x16, HEAP, lsr #32
    //     0x945d04: b.eq            #0x945d0c
    //     0x945d08: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x945d0c: r0 = Null
    //     0x945d0c: mov             x0, NULL
    // 0x945d10: LeaveFrame
    //     0x945d10: mov             SP, fp
    //     0x945d14: ldp             fp, lr, [SP], #0x10
    // 0x945d18: ret
    //     0x945d18: ret             
    // 0x945d1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945d1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x945d20: b               #0x945b7c
    // 0x945d24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945d24: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x945d28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945d28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _forceRebuildPage(/* No info */) {
    // ** addr: 0x993a90, size: 0x64
    // 0x993a90: EnterFrame
    //     0x993a90: stp             fp, lr, [SP, #-0x10]!
    //     0x993a94: mov             fp, SP
    // 0x993a98: AllocStack(0x8)
    //     0x993a98: sub             SP, SP, #8
    // 0x993a9c: SetupParameters(_ModalScopeState<C1X0> this /* r1 => r1, fp-0x8 */)
    //     0x993a9c: stur            x1, [fp, #-8]
    // 0x993aa0: CheckStackOverflow
    //     0x993aa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x993aa4: cmp             SP, x16
    //     0x993aa8: b.ls            #0x993aec
    // 0x993aac: r1 = 1
    //     0x993aac: movz            x1, #0x1
    // 0x993ab0: r0 = AllocateContext()
    //     0x993ab0: bl              #0xec126c  ; AllocateContextStub
    // 0x993ab4: mov             x1, x0
    // 0x993ab8: ldur            x0, [fp, #-8]
    // 0x993abc: StoreField: r1->field_f = r0
    //     0x993abc: stur            w0, [x1, #0xf]
    // 0x993ac0: mov             x2, x1
    // 0x993ac4: r1 = Function '<anonymous closure>':.
    //     0x993ac4: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5cbb8] AnonymousClosure: (0x85eee4), in [package:flutter/src/widgets/overlay.dart] _OverlayPortalState::hide (0x85ee78)
    //     0x993ac8: ldr             x1, [x1, #0xbb8]
    // 0x993acc: r0 = AllocateClosure()
    //     0x993acc: bl              #0xec1630  ; AllocateClosureStub
    // 0x993ad0: ldur            x1, [fp, #-8]
    // 0x993ad4: mov             x2, x0
    // 0x993ad8: r0 = setState()
    //     0x993ad8: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x993adc: r0 = Null
    //     0x993adc: mov             x0, NULL
    // 0x993ae0: LeaveFrame
    //     0x993ae0: mov             SP, fp
    //     0x993ae4: ldp             fp, lr, [SP], #0x10
    // 0x993ae8: ret
    //     0x993ae8: ret             
    // 0x993aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x993aec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x993af0: b               #0x993aac
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x998504, size: 0xac
    // 0x998504: EnterFrame
    //     0x998504: stp             fp, lr, [SP, #-0x10]!
    //     0x998508: mov             fp, SP
    // 0x99850c: AllocStack(0x18)
    //     0x99850c: sub             SP, SP, #0x18
    // 0x998510: SetupParameters(_ModalScopeState<C1X0> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x998510: mov             x4, x1
    //     0x998514: mov             x3, x2
    //     0x998518: stur            x1, [fp, #-0x10]
    //     0x99851c: stur            x2, [fp, #-0x18]
    // 0x998520: CheckStackOverflow
    //     0x998520: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x998524: cmp             SP, x16
    //     0x998528: b.ls            #0x9985a8
    // 0x99852c: LoadField: r5 = r4->field_7
    //     0x99852c: ldur            w5, [x4, #7]
    // 0x998530: DecompressPointer r5
    //     0x998530: add             x5, x5, HEAP, lsl #32
    // 0x998534: mov             x0, x3
    // 0x998538: mov             x2, x5
    // 0x99853c: stur            x5, [fp, #-8]
    // 0x998540: r1 = Null
    //     0x998540: mov             x1, NULL
    // 0x998544: r8 = _ModalScope<C1X0>
    //     0x998544: add             x8, PP, #0x25, lsl #12  ; [pp+0x254a0] Type: _ModalScope<C1X0>
    //     0x998548: ldr             x8, [x8, #0x4a0]
    // 0x99854c: LoadField: r9 = r8->field_7
    //     0x99854c: ldur            x9, [x8, #7]
    // 0x998550: r3 = Null
    //     0x998550: add             x3, PP, #0x25, lsl #12  ; [pp+0x254a8] Null
    //     0x998554: ldr             x3, [x3, #0x4a8]
    // 0x998558: blr             x9
    // 0x99855c: ldur            x0, [fp, #-0x18]
    // 0x998560: ldur            x2, [fp, #-8]
    // 0x998564: r1 = Null
    //     0x998564: mov             x1, NULL
    // 0x998568: cmp             w2, NULL
    // 0x99856c: b.eq            #0x998590
    // 0x998570: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x998570: ldur            w4, [x2, #0x17]
    // 0x998574: DecompressPointer r4
    //     0x998574: add             x4, x4, HEAP, lsl #32
    // 0x998578: r8 = X0 bound StatefulWidget
    //     0x998578: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x99857c: ldr             x8, [x8, #0x7f8]
    // 0x998580: LoadField: r9 = r4->field_7
    //     0x998580: ldur            x9, [x4, #7]
    // 0x998584: r3 = Null
    //     0x998584: add             x3, PP, #0x25, lsl #12  ; [pp+0x254b8] Null
    //     0x998588: ldr             x3, [x3, #0x4b8]
    // 0x99858c: blr             x9
    // 0x998590: ldur            x1, [fp, #-0x10]
    // 0x998594: r0 = _updateFocusScopeNode()
    //     0x998594: bl              #0x9985b0  ; [package:flutter/src/widgets/routes.dart] _ModalScopeState::_updateFocusScopeNode
    // 0x998598: r0 = Null
    //     0x998598: mov             x0, NULL
    // 0x99859c: LeaveFrame
    //     0x99859c: mov             SP, fp
    //     0x9985a0: ldp             fp, lr, [SP], #0x10
    // 0x9985a4: ret
    //     0x9985a4: ret             
    // 0x9985a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9985a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9985ac: b               #0x99852c
  }
  _ _updateFocusScopeNode(/* No info */) {
    // ** addr: 0x9985b0, size: 0x10c
    // 0x9985b0: EnterFrame
    //     0x9985b0: stp             fp, lr, [SP, #-0x10]!
    //     0x9985b4: mov             fp, SP
    // 0x9985b8: AllocStack(0x18)
    //     0x9985b8: sub             SP, SP, #0x18
    // 0x9985bc: r0 = Instance_TraversalEdgeBehavior
    //     0x9985bc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25498] Obj!TraversalEdgeBehavior@e34481
    //     0x9985c0: ldr             x0, [x0, #0x498]
    // 0x9985c4: mov             x2, x1
    // 0x9985c8: stur            x1, [fp, #-0x18]
    // 0x9985cc: CheckStackOverflow
    //     0x9985cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9985d0: cmp             SP, x16
    //     0x9985d4: b.ls            #0x9986a0
    // 0x9985d8: LoadField: r1 = r2->field_b
    //     0x9985d8: ldur            w1, [x2, #0xb]
    // 0x9985dc: DecompressPointer r1
    //     0x9985dc: add             x1, x1, HEAP, lsl #32
    // 0x9985e0: cmp             w1, NULL
    // 0x9985e4: b.eq            #0x9986a8
    // 0x9985e8: LoadField: r3 = r1->field_f
    //     0x9985e8: ldur            w3, [x1, #0xf]
    // 0x9985ec: DecompressPointer r3
    //     0x9985ec: add             x3, x3, HEAP, lsl #32
    // 0x9985f0: stur            x3, [fp, #-0x10]
    // 0x9985f4: LoadField: r1 = r3->field_f
    //     0x9985f4: ldur            w1, [x3, #0xf]
    // 0x9985f8: DecompressPointer r1
    //     0x9985f8: add             x1, x1, HEAP, lsl #32
    // 0x9985fc: cmp             w1, NULL
    // 0x998600: b.eq            #0x9986ac
    // 0x998604: LoadField: r4 = r1->field_b
    //     0x998604: ldur            w4, [x1, #0xb]
    // 0x998608: DecompressPointer r4
    //     0x998608: add             x4, x4, HEAP, lsl #32
    // 0x99860c: cmp             w4, NULL
    // 0x998610: b.eq            #0x9986b0
    // 0x998614: LoadField: r4 = r2->field_1b
    //     0x998614: ldur            w4, [x2, #0x1b]
    // 0x998618: DecompressPointer r4
    //     0x998618: add             x4, x4, HEAP, lsl #32
    // 0x99861c: stur            x4, [fp, #-8]
    // 0x998620: StoreField: r4->field_67 = r0
    //     0x998620: stur            w0, [x4, #0x67]
    // 0x998624: mov             x1, x3
    // 0x998628: r0 = isCurrent()
    //     0x998628: bl              #0x650678  ; [package:flutter/src/widgets/navigator.dart] Route::isCurrent
    // 0x99862c: tbnz            w0, #4, #0x998690
    // 0x998630: ldur            x0, [fp, #-0x18]
    // 0x998634: LoadField: r1 = r0->field_b
    //     0x998634: ldur            w1, [x0, #0xb]
    // 0x998638: DecompressPointer r1
    //     0x998638: add             x1, x1, HEAP, lsl #32
    // 0x99863c: cmp             w1, NULL
    // 0x998640: b.eq            #0x9986b4
    // 0x998644: LoadField: r0 = r1->field_f
    //     0x998644: ldur            w0, [x1, #0xf]
    // 0x998648: DecompressPointer r0
    //     0x998648: add             x0, x0, HEAP, lsl #32
    // 0x99864c: mov             x1, x0
    // 0x998650: r0 = requestFocus()
    //     0x998650: bl              #0x6522c4  ; [package:flutter/src/widgets/navigator.dart] Route::requestFocus
    // 0x998654: tbnz            w0, #4, #0x998690
    // 0x998658: ldur            x0, [fp, #-0x10]
    // 0x99865c: LoadField: r1 = r0->field_f
    //     0x99865c: ldur            w1, [x0, #0xf]
    // 0x998660: DecompressPointer r1
    //     0x998660: add             x1, x1, HEAP, lsl #32
    // 0x998664: cmp             w1, NULL
    // 0x998668: b.eq            #0x9986b8
    // 0x99866c: LoadField: r0 = r1->field_43
    //     0x99866c: ldur            w0, [x1, #0x43]
    // 0x998670: DecompressPointer r0
    //     0x998670: add             x0, x0, HEAP, lsl #32
    // 0x998674: mov             x1, x0
    // 0x998678: r0 = enclosingScope()
    //     0x998678: bl              #0x6521c4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::enclosingScope
    // 0x99867c: cmp             w0, NULL
    // 0x998680: b.eq            #0x998690
    // 0x998684: mov             x1, x0
    // 0x998688: ldur            x2, [fp, #-8]
    // 0x99868c: r0 = setFirstFocus()
    //     0x99868c: bl              #0x650c78  ; [package:flutter/src/widgets/focus_manager.dart] FocusScopeNode::setFirstFocus
    // 0x998690: r0 = Null
    //     0x998690: mov             x0, NULL
    // 0x998694: LeaveFrame
    //     0x998694: mov             SP, fp
    //     0x998698: ldp             fp, lr, [SP], #0x10
    // 0x99869c: ret
    //     0x99869c: ret             
    // 0x9986a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9986a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9986a4: b               #0x9985d8
    // 0x9986a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9986a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9986ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9986ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9986b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9986b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9986b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9986b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9986b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9986b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9a7f70, size: 0x34
    // 0x9a7f70: EnterFrame
    //     0x9a7f70: stp             fp, lr, [SP, #-0x10]!
    //     0x9a7f74: mov             fp, SP
    // 0x9a7f78: CheckStackOverflow
    //     0x9a7f78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a7f7c: cmp             SP, x16
    //     0x9a7f80: b.ls            #0x9a7f9c
    // 0x9a7f84: StoreField: r1->field_13 = rNULL
    //     0x9a7f84: stur            NULL, [x1, #0x13]
    // 0x9a7f88: r0 = _updateFocusScopeNode()
    //     0x9a7f88: bl              #0x9985b0  ; [package:flutter/src/widgets/routes.dart] _ModalScopeState::_updateFocusScopeNode
    // 0x9a7f8c: r0 = Null
    //     0x9a7f8c: mov             x0, NULL
    // 0x9a7f90: LeaveFrame
    //     0x9a7f90: mov             SP, fp
    //     0x9a7f94: ldp             fp, lr, [SP], #0x10
    // 0x9a7f98: ret
    //     0x9a7f98: ret             
    // 0x9a7f9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a7f9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a7fa0: b               #0x9a7f84
  }
  _ build(/* No info */) {
    // ** addr: 0xa1cff8, size: 0x228
    // 0xa1cff8: EnterFrame
    //     0xa1cff8: stp             fp, lr, [SP, #-0x10]!
    //     0xa1cffc: mov             fp, SP
    // 0xa1d000: AllocStack(0x50)
    //     0xa1d000: sub             SP, SP, #0x50
    // 0xa1d004: SetupParameters(_ModalScopeState<C1X0> this /* r1 => r1, fp-0x8 */)
    //     0xa1d004: stur            x1, [fp, #-8]
    // 0xa1d008: CheckStackOverflow
    //     0xa1d008: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1d00c: cmp             SP, x16
    //     0xa1d010: b.ls            #0xa1d204
    // 0xa1d014: r1 = 1
    //     0xa1d014: movz            x1, #0x1
    // 0xa1d018: r0 = AllocateContext()
    //     0xa1d018: bl              #0xec126c  ; AllocateContextStub
    // 0xa1d01c: mov             x2, x0
    // 0xa1d020: ldur            x0, [fp, #-8]
    // 0xa1d024: stur            x2, [fp, #-0x18]
    // 0xa1d028: StoreField: r2->field_f = r0
    //     0xa1d028: stur            w0, [x2, #0xf]
    // 0xa1d02c: LoadField: r3 = r0->field_1b
    //     0xa1d02c: ldur            w3, [x0, #0x1b]
    // 0xa1d030: DecompressPointer r3
    //     0xa1d030: add             x3, x3, HEAP, lsl #32
    // 0xa1d034: stur            x3, [fp, #-0x10]
    // 0xa1d038: LoadField: r1 = r0->field_b
    //     0xa1d038: ldur            w1, [x0, #0xb]
    // 0xa1d03c: DecompressPointer r1
    //     0xa1d03c: add             x1, x1, HEAP, lsl #32
    // 0xa1d040: cmp             w1, NULL
    // 0xa1d044: b.eq            #0xa1d20c
    // 0xa1d048: LoadField: r4 = r1->field_f
    //     0xa1d048: ldur            w4, [x1, #0xf]
    // 0xa1d04c: DecompressPointer r4
    //     0xa1d04c: add             x4, x4, HEAP, lsl #32
    // 0xa1d050: mov             x1, x4
    // 0xa1d054: r0 = isCurrent()
    //     0xa1d054: bl              #0x650678  ; [package:flutter/src/widgets/navigator.dart] Route::isCurrent
    // 0xa1d058: eor             x2, x0, #0x10
    // 0xa1d05c: ldur            x1, [fp, #-0x10]
    // 0xa1d060: r0 = skipTraversal=()
    //     0xa1d060: bl              #0x9412b0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::skipTraversal=
    // 0xa1d064: ldur            x0, [fp, #-8]
    // 0xa1d068: LoadField: r1 = r0->field_b
    //     0xa1d068: ldur            w1, [x0, #0xb]
    // 0xa1d06c: DecompressPointer r1
    //     0xa1d06c: add             x1, x1, HEAP, lsl #32
    // 0xa1d070: cmp             w1, NULL
    // 0xa1d074: b.eq            #0xa1d210
    // 0xa1d078: LoadField: r2 = r1->field_f
    //     0xa1d078: ldur            w2, [x1, #0xf]
    // 0xa1d07c: DecompressPointer r2
    //     0xa1d07c: add             x2, x2, HEAP, lsl #32
    // 0xa1d080: stur            x2, [fp, #-0x20]
    // 0xa1d084: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa1d084: ldur            w3, [x2, #0x17]
    // 0xa1d088: DecompressPointer r3
    //     0xa1d088: add             x3, x3, HEAP, lsl #32
    // 0xa1d08c: mov             x1, x2
    // 0xa1d090: stur            x3, [fp, #-0x10]
    // 0xa1d094: r0 = isCurrent()
    //     0xa1d094: bl              #0x650678  ; [package:flutter/src/widgets/navigator.dart] Route::isCurrent
    // 0xa1d098: mov             x2, x0
    // 0xa1d09c: ldur            x0, [fp, #-8]
    // 0xa1d0a0: stur            x2, [fp, #-0x28]
    // 0xa1d0a4: LoadField: r1 = r0->field_b
    //     0xa1d0a4: ldur            w1, [x0, #0xb]
    // 0xa1d0a8: DecompressPointer r1
    //     0xa1d0a8: add             x1, x1, HEAP, lsl #32
    // 0xa1d0ac: cmp             w1, NULL
    // 0xa1d0b0: b.eq            #0xa1d214
    // 0xa1d0b4: LoadField: r3 = r1->field_f
    //     0xa1d0b4: ldur            w3, [x1, #0xf]
    // 0xa1d0b8: DecompressPointer r3
    //     0xa1d0b8: add             x3, x3, HEAP, lsl #32
    // 0xa1d0bc: mov             x1, x3
    // 0xa1d0c0: r0 = canPop()
    //     0xa1d0c0: bl              #0xa1d238  ; [package:flutter/src/widgets/routes.dart] ModalRoute::canPop
    // 0xa1d0c4: mov             x2, x0
    // 0xa1d0c8: ldur            x0, [fp, #-8]
    // 0xa1d0cc: stur            x2, [fp, #-0x30]
    // 0xa1d0d0: LoadField: r1 = r0->field_b
    //     0xa1d0d0: ldur            w1, [x0, #0xb]
    // 0xa1d0d4: DecompressPointer r1
    //     0xa1d0d4: add             x1, x1, HEAP, lsl #32
    // 0xa1d0d8: cmp             w1, NULL
    // 0xa1d0dc: b.eq            #0xa1d218
    // 0xa1d0e0: LoadField: r3 = r1->field_f
    //     0xa1d0e0: ldur            w3, [x1, #0xf]
    // 0xa1d0e4: DecompressPointer r3
    //     0xa1d0e4: add             x3, x3, HEAP, lsl #32
    // 0xa1d0e8: mov             x1, x3
    // 0xa1d0ec: r0 = impliesAppBarDismissal()
    //     0xa1d0ec: bl              #0x9e694c  ; [package:flutter/src/widgets/routes.dart] ModalRoute::impliesAppBarDismissal
    // 0xa1d0f0: mov             x3, x0
    // 0xa1d0f4: ldur            x0, [fp, #-8]
    // 0xa1d0f8: stur            x3, [fp, #-0x40]
    // 0xa1d0fc: LoadField: r1 = r0->field_b
    //     0xa1d0fc: ldur            w1, [x0, #0xb]
    // 0xa1d100: DecompressPointer r1
    //     0xa1d100: add             x1, x1, HEAP, lsl #32
    // 0xa1d104: cmp             w1, NULL
    // 0xa1d108: b.eq            #0xa1d21c
    // 0xa1d10c: LoadField: r0 = r1->field_f
    //     0xa1d10c: ldur            w0, [x1, #0xf]
    // 0xa1d110: DecompressPointer r0
    //     0xa1d110: add             x0, x0, HEAP, lsl #32
    // 0xa1d114: LoadField: r4 = r0->field_63
    //     0xa1d114: ldur            w4, [x0, #0x63]
    // 0xa1d118: DecompressPointer r4
    //     0xa1d118: add             x4, x4, HEAP, lsl #32
    // 0xa1d11c: stur            x4, [fp, #-0x38]
    // 0xa1d120: LoadField: r5 = r0->field_7f
    //     0xa1d120: ldur            w5, [x0, #0x7f]
    // 0xa1d124: DecompressPointer r5
    //     0xa1d124: add             x5, x5, HEAP, lsl #32
    // 0xa1d128: ldur            x2, [fp, #-0x18]
    // 0xa1d12c: stur            x5, [fp, #-8]
    // 0xa1d130: r1 = Function '<anonymous closure>':.
    //     0xa1d130: add             x1, PP, #0x25, lsl #12  ; [pp+0x25420] AnonymousClosure: (0xa1d338), in [package:flutter/src/widgets/routes.dart] _ModalScopeState::build (0xa1cff8)
    //     0xa1d134: ldr             x1, [x1, #0x420]
    // 0xa1d138: r0 = AllocateClosure()
    //     0xa1d138: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1d13c: stur            x0, [fp, #-0x48]
    // 0xa1d140: r0 = Builder()
    //     0xa1d140: bl              #0x6a5c84  ; AllocateBuilderStub -> Builder (size=0x10)
    // 0xa1d144: mov             x1, x0
    // 0xa1d148: ldur            x0, [fp, #-0x48]
    // 0xa1d14c: stur            x1, [fp, #-0x50]
    // 0xa1d150: StoreField: r1->field_b = r0
    //     0xa1d150: stur            w0, [x1, #0xb]
    // 0xa1d154: r0 = PageStorage()
    //     0xa1d154: bl              #0xa1d22c  ; AllocatePageStorageStub -> PageStorage (size=0x14)
    // 0xa1d158: mov             x1, x0
    // 0xa1d15c: ldur            x0, [fp, #-8]
    // 0xa1d160: stur            x1, [fp, #-0x48]
    // 0xa1d164: StoreField: r1->field_f = r0
    //     0xa1d164: stur            w0, [x1, #0xf]
    // 0xa1d168: ldur            x0, [fp, #-0x50]
    // 0xa1d16c: StoreField: r1->field_b = r0
    //     0xa1d16c: stur            w0, [x1, #0xb]
    // 0xa1d170: r0 = Offstage()
    //     0xa1d170: bl              #0x9f0298  ; AllocateOffstageStub -> Offstage (size=0x14)
    // 0xa1d174: mov             x2, x0
    // 0xa1d178: ldur            x0, [fp, #-0x38]
    // 0xa1d17c: stur            x2, [fp, #-8]
    // 0xa1d180: StoreField: r2->field_f = r0
    //     0xa1d180: stur            w0, [x2, #0xf]
    // 0xa1d184: ldur            x0, [fp, #-0x48]
    // 0xa1d188: StoreField: r2->field_b = r0
    //     0xa1d188: stur            w0, [x2, #0xb]
    // 0xa1d18c: r1 = <_ModalRouteAspect>
    //     0xa1d18c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25428] TypeArguments: <_ModalRouteAspect>
    //     0xa1d190: ldr             x1, [x1, #0x428]
    // 0xa1d194: r0 = _ModalScopeStatus()
    //     0xa1d194: bl              #0xa1d220  ; Allocate_ModalScopeStatusStub -> _ModalScopeStatus (size=0x24)
    // 0xa1d198: mov             x3, x0
    // 0xa1d19c: ldur            x0, [fp, #-0x28]
    // 0xa1d1a0: stur            x3, [fp, #-0x38]
    // 0xa1d1a4: StoreField: r3->field_13 = r0
    //     0xa1d1a4: stur            w0, [x3, #0x13]
    // 0xa1d1a8: ldur            x0, [fp, #-0x30]
    // 0xa1d1ac: ArrayStore: r3[0] = r0  ; List_4
    //     0xa1d1ac: stur            w0, [x3, #0x17]
    // 0xa1d1b0: ldur            x0, [fp, #-0x40]
    // 0xa1d1b4: StoreField: r3->field_1b = r0
    //     0xa1d1b4: stur            w0, [x3, #0x1b]
    // 0xa1d1b8: ldur            x0, [fp, #-0x20]
    // 0xa1d1bc: StoreField: r3->field_1f = r0
    //     0xa1d1bc: stur            w0, [x3, #0x1f]
    // 0xa1d1c0: ldur            x0, [fp, #-8]
    // 0xa1d1c4: StoreField: r3->field_b = r0
    //     0xa1d1c4: stur            w0, [x3, #0xb]
    // 0xa1d1c8: ldur            x2, [fp, #-0x18]
    // 0xa1d1cc: r1 = Function '<anonymous closure>':.
    //     0xa1d1cc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25430] AnonymousClosure: (0xa1d2b8), in [package:flutter/src/widgets/routes.dart] _ModalScopeState::build (0xa1cff8)
    //     0xa1d1d0: ldr             x1, [x1, #0x430]
    // 0xa1d1d4: r0 = AllocateClosure()
    //     0xa1d1d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1d1d8: stur            x0, [fp, #-8]
    // 0xa1d1dc: r0 = AnimatedBuilder()
    //     0xa1d1dc: bl              #0x7e5888  ; AllocateAnimatedBuilderStub -> AnimatedBuilder (size=0x18)
    // 0xa1d1e0: ldur            x1, [fp, #-8]
    // 0xa1d1e4: StoreField: r0->field_f = r1
    //     0xa1d1e4: stur            w1, [x0, #0xf]
    // 0xa1d1e8: ldur            x1, [fp, #-0x38]
    // 0xa1d1ec: StoreField: r0->field_13 = r1
    //     0xa1d1ec: stur            w1, [x0, #0x13]
    // 0xa1d1f0: ldur            x1, [fp, #-0x10]
    // 0xa1d1f4: StoreField: r0->field_b = r1
    //     0xa1d1f4: stur            w1, [x0, #0xb]
    // 0xa1d1f8: LeaveFrame
    //     0xa1d1f8: mov             SP, fp
    //     0xa1d1fc: ldp             fp, lr, [SP], #0x10
    // 0xa1d200: ret
    //     0xa1d200: ret             
    // 0xa1d204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1d204: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1d208: b               #0xa1d014
    // 0xa1d20c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1d20c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1d210: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1d210: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1d214: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1d214: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1d218: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1d218: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1d21c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1d21c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RestorationScope <anonymous closure>(dynamic, BuildContext, Widget?) {
    // ** addr: 0xa1d2b8, size: 0x80
    // 0xa1d2b8: EnterFrame
    //     0xa1d2b8: stp             fp, lr, [SP, #-0x10]!
    //     0xa1d2bc: mov             fp, SP
    // 0xa1d2c0: AllocStack(0x8)
    //     0xa1d2c0: sub             SP, SP, #8
    // 0xa1d2c4: SetupParameters()
    //     0xa1d2c4: ldr             x0, [fp, #0x20]
    //     0xa1d2c8: ldur            w1, [x0, #0x17]
    //     0xa1d2cc: add             x1, x1, HEAP, lsl #32
    // 0xa1d2d0: LoadField: r0 = r1->field_f
    //     0xa1d2d0: ldur            w0, [x1, #0xf]
    // 0xa1d2d4: DecompressPointer r0
    //     0xa1d2d4: add             x0, x0, HEAP, lsl #32
    // 0xa1d2d8: LoadField: r1 = r0->field_b
    //     0xa1d2d8: ldur            w1, [x0, #0xb]
    // 0xa1d2dc: DecompressPointer r1
    //     0xa1d2dc: add             x1, x1, HEAP, lsl #32
    // 0xa1d2e0: cmp             w1, NULL
    // 0xa1d2e4: b.eq            #0xa1d330
    // 0xa1d2e8: LoadField: r0 = r1->field_f
    //     0xa1d2e8: ldur            w0, [x1, #0xf]
    // 0xa1d2ec: DecompressPointer r0
    //     0xa1d2ec: add             x0, x0, HEAP, lsl #32
    // 0xa1d2f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa1d2f0: ldur            w1, [x0, #0x17]
    // 0xa1d2f4: DecompressPointer r1
    //     0xa1d2f4: add             x1, x1, HEAP, lsl #32
    // 0xa1d2f8: LoadField: r0 = r1->field_27
    //     0xa1d2f8: ldur            w0, [x1, #0x27]
    // 0xa1d2fc: DecompressPointer r0
    //     0xa1d2fc: add             x0, x0, HEAP, lsl #32
    // 0xa1d300: ldr             x1, [fp, #0x10]
    // 0xa1d304: stur            x0, [fp, #-8]
    // 0xa1d308: cmp             w1, NULL
    // 0xa1d30c: b.eq            #0xa1d334
    // 0xa1d310: r0 = RestorationScope()
    //     0xa1d310: bl              #0xa1cfec  ; AllocateRestorationScopeStub -> RestorationScope (size=0x14)
    // 0xa1d314: ldur            x1, [fp, #-8]
    // 0xa1d318: StoreField: r0->field_f = r1
    //     0xa1d318: stur            w1, [x0, #0xf]
    // 0xa1d31c: ldr             x1, [fp, #0x10]
    // 0xa1d320: StoreField: r0->field_b = r1
    //     0xa1d320: stur            w1, [x0, #0xb]
    // 0xa1d324: LeaveFrame
    //     0xa1d324: mov             SP, fp
    //     0xa1d328: ldp             fp, lr, [SP], #0x10
    // 0xa1d32c: ret
    //     0xa1d32c: ret             
    // 0xa1d330: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1d330: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1d334: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1d334: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Actions <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xa1d338, size: 0x288
    // 0xa1d338: EnterFrame
    //     0xa1d338: stp             fp, lr, [SP, #-0x10]!
    //     0xa1d33c: mov             fp, SP
    // 0xa1d340: AllocStack(0x58)
    //     0xa1d340: sub             SP, SP, #0x58
    // 0xa1d344: SetupParameters()
    //     0xa1d344: ldr             x0, [fp, #0x18]
    //     0xa1d348: ldur            w3, [x0, #0x17]
    //     0xa1d34c: add             x3, x3, HEAP, lsl #32
    //     0xa1d350: stur            x3, [fp, #-8]
    // 0xa1d354: CheckStackOverflow
    //     0xa1d354: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1d358: cmp             SP, x16
    //     0xa1d35c: b.ls            #0xa1d5a8
    // 0xa1d360: r1 = Null
    //     0xa1d360: mov             x1, NULL
    // 0xa1d364: r2 = 4
    //     0xa1d364: movz            x2, #0x4
    // 0xa1d368: r0 = AllocateArray()
    //     0xa1d368: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa1d36c: stur            x0, [fp, #-0x10]
    // 0xa1d370: r16 = DismissIntent
    //     0xa1d370: add             x16, PP, #0x25, lsl #12  ; [pp+0x25438] Type: DismissIntent
    //     0xa1d374: ldr             x16, [x16, #0x438]
    // 0xa1d378: StoreField: r0->field_f = r16
    //     0xa1d378: stur            w16, [x0, #0xf]
    // 0xa1d37c: r1 = <DismissIntent>
    //     0xa1d37c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25440] TypeArguments: <DismissIntent>
    //     0xa1d380: ldr             x1, [x1, #0x440]
    // 0xa1d384: r0 = _DismissModalAction()
    //     0xa1d384: bl              #0xa1d5d8  ; Allocate_DismissModalActionStub -> _DismissModalAction (size=0x18)
    // 0xa1d388: mov             x2, x0
    // 0xa1d38c: ldr             x0, [fp, #0x10]
    // 0xa1d390: stur            x2, [fp, #-0x18]
    // 0xa1d394: StoreField: r2->field_13 = r0
    //     0xa1d394: stur            w0, [x2, #0x13]
    // 0xa1d398: mov             x1, x2
    // 0xa1d39c: r0 = Action()
    //     0xa1d39c: bl              #0x940bc8  ; [package:flutter/src/widgets/actions.dart] Action::Action
    // 0xa1d3a0: ldur            x1, [fp, #-0x10]
    // 0xa1d3a4: ldur            x0, [fp, #-0x18]
    // 0xa1d3a8: ArrayStore: r1[1] = r0  ; List_4
    //     0xa1d3a8: add             x25, x1, #0x13
    //     0xa1d3ac: str             w0, [x25]
    //     0xa1d3b0: tbz             w0, #0, #0xa1d3cc
    //     0xa1d3b4: ldurb           w16, [x1, #-1]
    //     0xa1d3b8: ldurb           w17, [x0, #-1]
    //     0xa1d3bc: and             x16, x17, x16, lsr #2
    //     0xa1d3c0: tst             x16, HEAP, lsr #32
    //     0xa1d3c4: b.eq            #0xa1d3cc
    //     0xa1d3c8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa1d3cc: r16 = <Type, Action<Intent>>
    //     0xa1d3cc: add             x16, PP, #0x25, lsl #12  ; [pp+0x25448] TypeArguments: <Type, Action<Intent>>
    //     0xa1d3d0: ldr             x16, [x16, #0x448]
    // 0xa1d3d4: ldur            lr, [fp, #-0x10]
    // 0xa1d3d8: stp             lr, x16, [SP]
    // 0xa1d3dc: r0 = Map._fromLiteral()
    //     0xa1d3dc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa1d3e0: mov             x3, x0
    // 0xa1d3e4: ldur            x0, [fp, #-8]
    // 0xa1d3e8: stur            x3, [fp, #-0x38]
    // 0xa1d3ec: LoadField: r4 = r0->field_f
    //     0xa1d3ec: ldur            w4, [x0, #0xf]
    // 0xa1d3f0: DecompressPointer r4
    //     0xa1d3f0: add             x4, x4, HEAP, lsl #32
    // 0xa1d3f4: stur            x4, [fp, #-0x30]
    // 0xa1d3f8: LoadField: r5 = r4->field_1f
    //     0xa1d3f8: ldur            w5, [x4, #0x1f]
    // 0xa1d3fc: DecompressPointer r5
    //     0xa1d3fc: add             x5, x5, HEAP, lsl #32
    // 0xa1d400: stur            x5, [fp, #-0x28]
    // 0xa1d404: LoadField: r6 = r4->field_1b
    //     0xa1d404: ldur            w6, [x4, #0x1b]
    // 0xa1d408: DecompressPointer r6
    //     0xa1d408: add             x6, x6, HEAP, lsl #32
    // 0xa1d40c: stur            x6, [fp, #-0x20]
    // 0xa1d410: ArrayLoad: r7 = r4[0]  ; List_4
    //     0xa1d410: ldur            w7, [x4, #0x17]
    // 0xa1d414: DecompressPointer r7
    //     0xa1d414: add             x7, x7, HEAP, lsl #32
    // 0xa1d418: r16 = Sentinel
    //     0xa1d418: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa1d41c: cmp             w7, w16
    // 0xa1d420: b.eq            #0xa1d5b0
    // 0xa1d424: stur            x7, [fp, #-0x18]
    // 0xa1d428: LoadField: r1 = r4->field_13
    //     0xa1d428: ldur            w1, [x4, #0x13]
    // 0xa1d42c: DecompressPointer r1
    //     0xa1d42c: add             x1, x1, HEAP, lsl #32
    // 0xa1d430: cmp             w1, NULL
    // 0xa1d434: b.ne            #0xa1d4c8
    // 0xa1d438: LoadField: r1 = r4->field_b
    //     0xa1d438: ldur            w1, [x4, #0xb]
    // 0xa1d43c: DecompressPointer r1
    //     0xa1d43c: add             x1, x1, HEAP, lsl #32
    // 0xa1d440: cmp             w1, NULL
    // 0xa1d444: b.eq            #0xa1d5bc
    // 0xa1d448: LoadField: r2 = r1->field_f
    //     0xa1d448: ldur            w2, [x1, #0xf]
    // 0xa1d44c: DecompressPointer r2
    //     0xa1d44c: add             x2, x2, HEAP, lsl #32
    // 0xa1d450: LoadField: r8 = r2->field_7b
    //     0xa1d450: ldur            w8, [x2, #0x7b]
    // 0xa1d454: DecompressPointer r8
    //     0xa1d454: add             x8, x8, HEAP, lsl #32
    // 0xa1d458: mov             x2, x0
    // 0xa1d45c: stur            x8, [fp, #-0x10]
    // 0xa1d460: r1 = Function '<anonymous closure>':.
    //     0xa1d460: add             x1, PP, #0x25, lsl #12  ; [pp+0x25450] AnonymousClosure: (0xa1dd80), in [package:flutter/src/widgets/routes.dart] _ModalScopeState::build (0xa1cff8)
    //     0xa1d464: ldr             x1, [x1, #0x450]
    // 0xa1d468: r0 = AllocateClosure()
    //     0xa1d468: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1d46c: stur            x0, [fp, #-0x40]
    // 0xa1d470: r0 = Builder()
    //     0xa1d470: bl              #0x6a5c84  ; AllocateBuilderStub -> Builder (size=0x10)
    // 0xa1d474: mov             x1, x0
    // 0xa1d478: ldur            x0, [fp, #-0x40]
    // 0xa1d47c: stur            x1, [fp, #-0x48]
    // 0xa1d480: StoreField: r1->field_b = r0
    //     0xa1d480: stur            w0, [x1, #0xb]
    // 0xa1d484: r0 = RepaintBoundary()
    //     0xa1d484: bl              #0x9dae3c  ; AllocateRepaintBoundaryStub -> RepaintBoundary (size=0x10)
    // 0xa1d488: mov             x1, x0
    // 0xa1d48c: ldur            x0, [fp, #-0x48]
    // 0xa1d490: StoreField: r1->field_b = r0
    //     0xa1d490: stur            w0, [x1, #0xb]
    // 0xa1d494: ldur            x0, [fp, #-0x10]
    // 0xa1d498: StoreField: r1->field_7 = r0
    //     0xa1d498: stur            w0, [x1, #7]
    // 0xa1d49c: mov             x0, x1
    // 0xa1d4a0: ldur            x2, [fp, #-0x30]
    // 0xa1d4a4: StoreField: r2->field_13 = r0
    //     0xa1d4a4: stur            w0, [x2, #0x13]
    //     0xa1d4a8: ldurb           w16, [x2, #-1]
    //     0xa1d4ac: ldurb           w17, [x0, #-1]
    //     0xa1d4b0: and             x16, x17, x16, lsr #2
    //     0xa1d4b4: tst             x16, HEAP, lsr #32
    //     0xa1d4b8: b.eq            #0xa1d4c0
    //     0xa1d4bc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa1d4c0: mov             x6, x1
    // 0xa1d4c4: b               #0xa1d4cc
    // 0xa1d4c8: mov             x6, x1
    // 0xa1d4cc: ldur            x0, [fp, #-0x38]
    // 0xa1d4d0: ldur            x3, [fp, #-0x28]
    // 0xa1d4d4: ldur            x4, [fp, #-0x20]
    // 0xa1d4d8: ldur            x5, [fp, #-0x18]
    // 0xa1d4dc: ldur            x2, [fp, #-8]
    // 0xa1d4e0: stur            x6, [fp, #-0x10]
    // 0xa1d4e4: r1 = Function '<anonymous closure>':.
    //     0xa1d4e4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25458] AnonymousClosure: (0xa1d5e4), in [package:flutter/src/widgets/routes.dart] _ModalScopeState::build (0xa1cff8)
    //     0xa1d4e8: ldr             x1, [x1, #0x458]
    // 0xa1d4ec: r0 = AllocateClosure()
    //     0xa1d4ec: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1d4f0: stur            x0, [fp, #-8]
    // 0xa1d4f4: r0 = ListenableBuilder()
    //     0xa1d4f4: bl              #0xa1d5cc  ; AllocateListenableBuilderStub -> ListenableBuilder (size=0x18)
    // 0xa1d4f8: mov             x1, x0
    // 0xa1d4fc: ldur            x0, [fp, #-8]
    // 0xa1d500: stur            x1, [fp, #-0x30]
    // 0xa1d504: StoreField: r1->field_f = r0
    //     0xa1d504: stur            w0, [x1, #0xf]
    // 0xa1d508: ldur            x0, [fp, #-0x10]
    // 0xa1d50c: StoreField: r1->field_13 = r0
    //     0xa1d50c: stur            w0, [x1, #0x13]
    // 0xa1d510: ldur            x0, [fp, #-0x18]
    // 0xa1d514: StoreField: r1->field_b = r0
    //     0xa1d514: stur            w0, [x1, #0xb]
    // 0xa1d518: r0 = RepaintBoundary()
    //     0xa1d518: bl              #0x9dae3c  ; AllocateRepaintBoundaryStub -> RepaintBoundary (size=0x10)
    // 0xa1d51c: mov             x1, x0
    // 0xa1d520: ldur            x0, [fp, #-0x30]
    // 0xa1d524: stur            x1, [fp, #-8]
    // 0xa1d528: StoreField: r1->field_b = r0
    //     0xa1d528: stur            w0, [x1, #0xb]
    // 0xa1d52c: r0 = _FocusScopeWithExternalFocusNode()
    //     0xa1d52c: bl              #0xa1d5c0  ; Allocate_FocusScopeWithExternalFocusNodeStub -> _FocusScopeWithExternalFocusNode (size=0x40)
    // 0xa1d530: mov             x1, x0
    // 0xa1d534: ldur            x0, [fp, #-8]
    // 0xa1d538: stur            x1, [fp, #-0x10]
    // 0xa1d53c: StoreField: r1->field_f = r0
    //     0xa1d53c: stur            w0, [x1, #0xf]
    // 0xa1d540: ldur            x0, [fp, #-0x20]
    // 0xa1d544: StoreField: r1->field_13 = r0
    //     0xa1d544: stur            w0, [x1, #0x13]
    // 0xa1d548: r0 = false
    //     0xa1d548: add             x0, NULL, #0x30  ; false
    // 0xa1d54c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa1d54c: stur            w0, [x1, #0x17]
    // 0xa1d550: r0 = true
    //     0xa1d550: add             x0, NULL, #0x20  ; true
    // 0xa1d554: StoreField: r1->field_37 = r0
    //     0xa1d554: stur            w0, [x1, #0x37]
    // 0xa1d558: r0 = PrimaryScrollController()
    //     0xa1d558: bl              #0xa17c1c  ; AllocatePrimaryScrollControllerStub -> PrimaryScrollController (size=0x1c)
    // 0xa1d55c: mov             x1, x0
    // 0xa1d560: ldur            x0, [fp, #-0x28]
    // 0xa1d564: stur            x1, [fp, #-8]
    // 0xa1d568: StoreField: r1->field_f = r0
    //     0xa1d568: stur            w0, [x1, #0xf]
    // 0xa1d56c: r0 = _ConstSet len:3
    //     0xa1d56c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25460] Set<TargetPlatform>(3)
    //     0xa1d570: ldr             x0, [x0, #0x460]
    // 0xa1d574: ArrayStore: r1[0] = r0  ; List_4
    //     0xa1d574: stur            w0, [x1, #0x17]
    // 0xa1d578: r0 = Instance_Axis
    //     0xa1d578: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa1d57c: StoreField: r1->field_13 = r0
    //     0xa1d57c: stur            w0, [x1, #0x13]
    // 0xa1d580: ldur            x0, [fp, #-0x10]
    // 0xa1d584: StoreField: r1->field_b = r0
    //     0xa1d584: stur            w0, [x1, #0xb]
    // 0xa1d588: r0 = Actions()
    //     0xa1d588: bl              #0x9f2aec  ; AllocateActionsStub -> Actions (size=0x18)
    // 0xa1d58c: ldur            x1, [fp, #-0x38]
    // 0xa1d590: StoreField: r0->field_f = r1
    //     0xa1d590: stur            w1, [x0, #0xf]
    // 0xa1d594: ldur            x1, [fp, #-8]
    // 0xa1d598: StoreField: r0->field_13 = r1
    //     0xa1d598: stur            w1, [x0, #0x13]
    // 0xa1d59c: LeaveFrame
    //     0xa1d59c: mov             SP, fp
    //     0xa1d5a0: ldp             fp, lr, [SP], #0x10
    // 0xa1d5a4: ret
    //     0xa1d5a4: ret             
    // 0xa1d5a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1d5a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1d5ac: b               #0xa1d360
    // 0xa1d5b0: r9 = _listenable
    //     0xa1d5b0: add             x9, PP, #0x25, lsl #12  ; [pp+0x25468] Field <_ModalScopeState@322188637._listenable@322188637>: late (offset: 0x18)
    //     0xa1d5b4: ldr             x9, [x9, #0x468]
    // 0xa1d5b8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa1d5b8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa1d5bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1d5bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, Widget?) {
    // ** addr: 0xa1d5e4, size: 0x16c
    // 0xa1d5e4: EnterFrame
    //     0xa1d5e4: stp             fp, lr, [SP, #-0x10]!
    //     0xa1d5e8: mov             fp, SP
    // 0xa1d5ec: AllocStack(0x28)
    //     0xa1d5ec: sub             SP, SP, #0x28
    // 0xa1d5f0: SetupParameters()
    //     0xa1d5f0: ldr             x0, [fp, #0x20]
    //     0xa1d5f4: ldur            w2, [x0, #0x17]
    //     0xa1d5f8: add             x2, x2, HEAP, lsl #32
    //     0xa1d5fc: stur            x2, [fp, #-0x20]
    // 0xa1d600: CheckStackOverflow
    //     0xa1d600: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1d604: cmp             SP, x16
    //     0xa1d608: b.ls            #0xa1d73c
    // 0xa1d60c: LoadField: r0 = r2->field_f
    //     0xa1d60c: ldur            w0, [x2, #0xf]
    // 0xa1d610: DecompressPointer r0
    //     0xa1d610: add             x0, x0, HEAP, lsl #32
    // 0xa1d614: LoadField: r1 = r0->field_b
    //     0xa1d614: ldur            w1, [x0, #0xb]
    // 0xa1d618: DecompressPointer r1
    //     0xa1d618: add             x1, x1, HEAP, lsl #32
    // 0xa1d61c: cmp             w1, NULL
    // 0xa1d620: b.eq            #0xa1d744
    // 0xa1d624: LoadField: r0 = r1->field_f
    //     0xa1d624: ldur            w0, [x1, #0xf]
    // 0xa1d628: DecompressPointer r0
    //     0xa1d628: add             x0, x0, HEAP, lsl #32
    // 0xa1d62c: stur            x0, [fp, #-0x18]
    // 0xa1d630: LoadField: r3 = r0->field_67
    //     0xa1d630: ldur            w3, [x0, #0x67]
    // 0xa1d634: DecompressPointer r3
    //     0xa1d634: add             x3, x3, HEAP, lsl #32
    // 0xa1d638: stur            x3, [fp, #-0x10]
    // 0xa1d63c: cmp             w3, NULL
    // 0xa1d640: b.eq            #0xa1d748
    // 0xa1d644: LoadField: r5 = r0->field_6b
    //     0xa1d644: ldur            w5, [x0, #0x6b]
    // 0xa1d648: DecompressPointer r5
    //     0xa1d648: add             x5, x5, HEAP, lsl #32
    // 0xa1d64c: stur            x5, [fp, #-8]
    // 0xa1d650: cmp             w5, NULL
    // 0xa1d654: b.eq            #0xa1d74c
    // 0xa1d658: LoadField: r1 = r0->field_f
    //     0xa1d658: ldur            w1, [x0, #0xf]
    // 0xa1d65c: DecompressPointer r1
    //     0xa1d65c: add             x1, x1, HEAP, lsl #32
    // 0xa1d660: cmp             w1, NULL
    // 0xa1d664: b.ne            #0xa1d670
    // 0xa1d668: r1 = Null
    //     0xa1d668: mov             x1, NULL
    // 0xa1d66c: b               #0xa1d67c
    // 0xa1d670: LoadField: r4 = r1->field_67
    //     0xa1d670: ldur            w4, [x1, #0x67]
    // 0xa1d674: DecompressPointer r4
    //     0xa1d674: add             x4, x4, HEAP, lsl #32
    // 0xa1d678: mov             x1, x4
    // 0xa1d67c: cmp             w1, NULL
    // 0xa1d680: b.ne            #0xa1d6d8
    // 0xa1d684: r1 = <bool>
    //     0xa1d684: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xa1d688: r0 = ValueNotifier()
    //     0xa1d688: bl              #0x65a810  ; AllocateValueNotifierStub -> ValueNotifier<X0> (size=0x2c)
    // 0xa1d68c: mov             x1, x0
    // 0xa1d690: r0 = false
    //     0xa1d690: add             x0, NULL, #0x30  ; false
    // 0xa1d694: stur            x1, [fp, #-0x28]
    // 0xa1d698: StoreField: r1->field_27 = r0
    //     0xa1d698: stur            w0, [x1, #0x27]
    // 0xa1d69c: StoreField: r1->field_7 = rZR
    //     0xa1d69c: stur            xzr, [x1, #7]
    // 0xa1d6a0: StoreField: r1->field_13 = rZR
    //     0xa1d6a0: stur            xzr, [x1, #0x13]
    // 0xa1d6a4: StoreField: r1->field_1b = rZR
    //     0xa1d6a4: stur            xzr, [x1, #0x1b]
    // 0xa1d6a8: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0xa1d6a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa1d6ac: ldr             x0, [x0, #0xca8]
    //     0xa1d6b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa1d6b4: cmp             w0, w16
    //     0xa1d6b8: b.ne            #0xa1d6c4
    //     0xa1d6bc: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0xa1d6c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa1d6c4: mov             x1, x0
    // 0xa1d6c8: ldur            x0, [fp, #-0x28]
    // 0xa1d6cc: StoreField: r0->field_f = r1
    //     0xa1d6cc: stur            w1, [x0, #0xf]
    // 0xa1d6d0: mov             x3, x0
    // 0xa1d6d4: b               #0xa1d6dc
    // 0xa1d6d8: mov             x3, x1
    // 0xa1d6dc: ldr             x0, [fp, #0x10]
    // 0xa1d6e0: ldur            x2, [fp, #-0x20]
    // 0xa1d6e4: stur            x3, [fp, #-0x28]
    // 0xa1d6e8: r1 = Function '<anonymous closure>':.
    //     0xa1d6e8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25470] AnonymousClosure: (0xa1dcfc), in [package:flutter/src/widgets/routes.dart] _ModalScopeState::build (0xa1cff8)
    //     0xa1d6ec: ldr             x1, [x1, #0x470]
    // 0xa1d6f0: r0 = AllocateClosure()
    //     0xa1d6f0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa1d6f4: stur            x0, [fp, #-0x20]
    // 0xa1d6f8: r0 = ListenableBuilder()
    //     0xa1d6f8: bl              #0xa1d5cc  ; AllocateListenableBuilderStub -> ListenableBuilder (size=0x18)
    // 0xa1d6fc: mov             x1, x0
    // 0xa1d700: ldur            x0, [fp, #-0x20]
    // 0xa1d704: StoreField: r1->field_f = r0
    //     0xa1d704: stur            w0, [x1, #0xf]
    // 0xa1d708: ldr             x0, [fp, #0x10]
    // 0xa1d70c: StoreField: r1->field_13 = r0
    //     0xa1d70c: stur            w0, [x1, #0x13]
    // 0xa1d710: ldur            x0, [fp, #-0x28]
    // 0xa1d714: StoreField: r1->field_b = r0
    //     0xa1d714: stur            w0, [x1, #0xb]
    // 0xa1d718: mov             x6, x1
    // 0xa1d71c: ldur            x1, [fp, #-0x18]
    // 0xa1d720: ldr             x2, [fp, #0x18]
    // 0xa1d724: ldur            x3, [fp, #-0x10]
    // 0xa1d728: ldur            x5, [fp, #-8]
    // 0xa1d72c: r0 = _buildFlexibleTransitions()
    //     0xa1d72c: bl              #0xa1d750  ; [package:flutter/src/widgets/routes.dart] ModalRoute::_buildFlexibleTransitions
    // 0xa1d730: LeaveFrame
    //     0xa1d730: mov             SP, fp
    //     0xa1d734: ldp             fp, lr, [SP], #0x10
    // 0xa1d738: ret
    //     0xa1d738: ret             
    // 0xa1d73c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1d73c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1d740: b               #0xa1d60c
    // 0xa1d744: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1d744: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1d748: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1d748: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1d74c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1d74c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] IgnorePointer <anonymous closure>(dynamic, BuildContext, Widget?) {
    // ** addr: 0xa1dcfc, size: 0x84
    // 0xa1dcfc: EnterFrame
    //     0xa1dcfc: stp             fp, lr, [SP, #-0x10]!
    //     0xa1dd00: mov             fp, SP
    // 0xa1dd04: AllocStack(0x10)
    //     0xa1dd04: sub             SP, SP, #0x10
    // 0xa1dd08: SetupParameters()
    //     0xa1dd08: ldr             x0, [fp, #0x20]
    //     0xa1dd0c: ldur            w2, [x0, #0x17]
    //     0xa1dd10: add             x2, x2, HEAP, lsl #32
    //     0xa1dd14: stur            x2, [fp, #-8]
    // 0xa1dd18: CheckStackOverflow
    //     0xa1dd18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1dd1c: cmp             SP, x16
    //     0xa1dd20: b.ls            #0xa1dd78
    // 0xa1dd24: LoadField: r1 = r2->field_f
    //     0xa1dd24: ldur            w1, [x2, #0xf]
    // 0xa1dd28: DecompressPointer r1
    //     0xa1dd28: add             x1, x1, HEAP, lsl #32
    // 0xa1dd2c: r0 = _shouldIgnoreFocusRequest()
    //     0xa1dd2c: bl              #0x652314  ; [package:flutter/src/widgets/routes.dart] _ModalScopeState::_shouldIgnoreFocusRequest
    // 0xa1dd30: mov             x3, x0
    // 0xa1dd34: ldur            x0, [fp, #-8]
    // 0xa1dd38: stur            x3, [fp, #-0x10]
    // 0xa1dd3c: LoadField: r1 = r0->field_f
    //     0xa1dd3c: ldur            w1, [x0, #0xf]
    // 0xa1dd40: DecompressPointer r1
    //     0xa1dd40: add             x1, x1, HEAP, lsl #32
    // 0xa1dd44: LoadField: r0 = r1->field_1b
    //     0xa1dd44: ldur            w0, [x1, #0x1b]
    // 0xa1dd48: DecompressPointer r0
    //     0xa1dd48: add             x0, x0, HEAP, lsl #32
    // 0xa1dd4c: eor             x2, x3, #0x10
    // 0xa1dd50: mov             x1, x0
    // 0xa1dd54: r0 = canRequestFocus=()
    //     0xa1dd54: bl              #0x93a5e4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::canRequestFocus=
    // 0xa1dd58: r0 = IgnorePointer()
    //     0xa1dd58: bl              #0x9e3118  ; AllocateIgnorePointerStub -> IgnorePointer (size=0x18)
    // 0xa1dd5c: ldur            x1, [fp, #-0x10]
    // 0xa1dd60: StoreField: r0->field_f = r1
    //     0xa1dd60: stur            w1, [x0, #0xf]
    // 0xa1dd64: ldr             x1, [fp, #0x10]
    // 0xa1dd68: StoreField: r0->field_b = r1
    //     0xa1dd68: stur            w1, [x0, #0xb]
    // 0xa1dd6c: LeaveFrame
    //     0xa1dd6c: mov             SP, fp
    //     0xa1dd70: ldp             fp, lr, [SP], #0x10
    // 0xa1dd74: ret
    //     0xa1dd74: ret             
    // 0xa1dd78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1dd78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1dd7c: b               #0xa1dd24
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xa1dd80, size: 0xa4
    // 0xa1dd80: EnterFrame
    //     0xa1dd80: stp             fp, lr, [SP, #-0x10]!
    //     0xa1dd84: mov             fp, SP
    // 0xa1dd88: ldr             x0, [fp, #0x18]
    // 0xa1dd8c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa1dd8c: ldur            w1, [x0, #0x17]
    // 0xa1dd90: DecompressPointer r1
    //     0xa1dd90: add             x1, x1, HEAP, lsl #32
    // 0xa1dd94: CheckStackOverflow
    //     0xa1dd94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1dd98: cmp             SP, x16
    //     0xa1dd9c: b.ls            #0xa1de10
    // 0xa1dda0: LoadField: r0 = r1->field_f
    //     0xa1dda0: ldur            w0, [x1, #0xf]
    // 0xa1dda4: DecompressPointer r0
    //     0xa1dda4: add             x0, x0, HEAP, lsl #32
    // 0xa1dda8: LoadField: r1 = r0->field_b
    //     0xa1dda8: ldur            w1, [x0, #0xb]
    // 0xa1ddac: DecompressPointer r1
    //     0xa1ddac: add             x1, x1, HEAP, lsl #32
    // 0xa1ddb0: cmp             w1, NULL
    // 0xa1ddb4: b.eq            #0xa1de18
    // 0xa1ddb8: LoadField: r0 = r1->field_f
    //     0xa1ddb8: ldur            w0, [x1, #0xf]
    // 0xa1ddbc: DecompressPointer r0
    //     0xa1ddbc: add             x0, x0, HEAP, lsl #32
    // 0xa1ddc0: LoadField: r3 = r0->field_67
    //     0xa1ddc0: ldur            w3, [x0, #0x67]
    // 0xa1ddc4: DecompressPointer r3
    //     0xa1ddc4: add             x3, x3, HEAP, lsl #32
    // 0xa1ddc8: cmp             w3, NULL
    // 0xa1ddcc: b.eq            #0xa1de1c
    // 0xa1ddd0: LoadField: r5 = r0->field_6b
    //     0xa1ddd0: ldur            w5, [x0, #0x6b]
    // 0xa1ddd4: DecompressPointer r5
    //     0xa1ddd4: add             x5, x5, HEAP, lsl #32
    // 0xa1ddd8: cmp             w5, NULL
    // 0xa1dddc: b.eq            #0xa1de20
    // 0xa1dde0: r1 = LoadClassIdInstr(r0)
    //     0xa1dde0: ldur            x1, [x0, #-1]
    //     0xa1dde4: ubfx            x1, x1, #0xc, #0x14
    // 0xa1dde8: mov             x16, x0
    // 0xa1ddec: mov             x0, x1
    // 0xa1ddf0: mov             x1, x16
    // 0xa1ddf4: ldr             x2, [fp, #0x10]
    // 0xa1ddf8: r0 = GDT[cid_x0 + 0xec2]()
    //     0xa1ddf8: add             lr, x0, #0xec2
    //     0xa1ddfc: ldr             lr, [x21, lr, lsl #3]
    //     0xa1de00: blr             lr
    // 0xa1de04: LeaveFrame
    //     0xa1de04: mov             SP, fp
    //     0xa1de08: ldp             fp, lr, [SP], #0x10
    // 0xa1de0c: ret
    //     0xa1de0c: ret             
    // 0xa1de10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1de10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1de14: b               #0xa1dda0
    // 0xa1de18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1de18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1de1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1de1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1de20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1de20: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa80c8c, size: 0x54
    // 0xa80c8c: EnterFrame
    //     0xa80c8c: stp             fp, lr, [SP, #-0x10]!
    //     0xa80c90: mov             fp, SP
    // 0xa80c94: AllocStack(0x8)
    //     0xa80c94: sub             SP, SP, #8
    // 0xa80c98: SetupParameters(_ModalScopeState<C1X0> this /* r1 => r0, fp-0x8 */)
    //     0xa80c98: mov             x0, x1
    //     0xa80c9c: stur            x1, [fp, #-8]
    // 0xa80ca0: CheckStackOverflow
    //     0xa80ca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa80ca4: cmp             SP, x16
    //     0xa80ca8: b.ls            #0xa80cd8
    // 0xa80cac: LoadField: r1 = r0->field_1b
    //     0xa80cac: ldur            w1, [x0, #0x1b]
    // 0xa80cb0: DecompressPointer r1
    //     0xa80cb0: add             x1, x1, HEAP, lsl #32
    // 0xa80cb4: r0 = dispose()
    //     0xa80cb4: bl              #0xa8b4f0  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::dispose
    // 0xa80cb8: ldur            x0, [fp, #-8]
    // 0xa80cbc: LoadField: r1 = r0->field_1f
    //     0xa80cbc: ldur            w1, [x0, #0x1f]
    // 0xa80cc0: DecompressPointer r1
    //     0xa80cc0: add             x1, x1, HEAP, lsl #32
    // 0xa80cc4: r0 = dispose()
    //     0xa80cc4: bl              #0xa876d8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xa80cc8: r0 = Null
    //     0xa80cc8: mov             x0, NULL
    // 0xa80ccc: LeaveFrame
    //     0xa80ccc: mov             SP, fp
    //     0xa80cd0: ldp             fp, lr, [SP], #0x10
    // 0xa80cd4: ret
    //     0xa80cd4: ret             
    // 0xa80cd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa80cd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa80cdc: b               #0xa80cac
  }
  _ _ModalScopeState(/* No info */) {
    // ** addr: 0xa92b58, size: 0xe8
    // 0xa92b58: EnterFrame
    //     0xa92b58: stp             fp, lr, [SP, #-0x10]!
    //     0xa92b5c: mov             fp, SP
    // 0xa92b60: AllocStack(0x20)
    //     0xa92b60: sub             SP, SP, #0x20
    // 0xa92b64: r0 = Sentinel
    //     0xa92b64: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa92b68: mov             x3, x1
    // 0xa92b6c: stur            x1, [fp, #-8]
    // 0xa92b70: CheckStackOverflow
    //     0xa92b70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa92b74: cmp             SP, x16
    //     0xa92b78: b.ls            #0xa92c38
    // 0xa92b7c: ArrayStore: r3[0] = r0  ; List_4
    //     0xa92b7c: stur            w0, [x3, #0x17]
    // 0xa92b80: r1 = Null
    //     0xa92b80: mov             x1, NULL
    // 0xa92b84: r2 = 4
    //     0xa92b84: movz            x2, #0x4
    // 0xa92b88: r0 = AllocateArray()
    //     0xa92b88: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa92b8c: r16 = _ModalScopeState
    //     0xa92b8c: add             x16, PP, #0x23, lsl #12  ; [pp+0x238d8] Type: _ModalScopeState
    //     0xa92b90: ldr             x16, [x16, #0x8d8]
    // 0xa92b94: StoreField: r0->field_f = r16
    //     0xa92b94: stur            w16, [x0, #0xf]
    // 0xa92b98: r16 = " Focus Scope"
    //     0xa92b98: add             x16, PP, #0x23, lsl #12  ; [pp+0x238e0] " Focus Scope"
    //     0xa92b9c: ldr             x16, [x16, #0x8e0]
    // 0xa92ba0: StoreField: r0->field_13 = r16
    //     0xa92ba0: stur            w16, [x0, #0x13]
    // 0xa92ba4: str             x0, [SP]
    // 0xa92ba8: r0 = _interpolate()
    //     0xa92ba8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa92bac: stur            x0, [fp, #-0x10]
    // 0xa92bb0: r0 = FocusScopeNode()
    //     0xa92bb0: bl              #0x693fe4  ; AllocateFocusScopeNodeStub -> FocusScopeNode (size=0x70)
    // 0xa92bb4: stur            x0, [fp, #-0x18]
    // 0xa92bb8: ldur            x16, [fp, #-0x10]
    // 0xa92bbc: str             x16, [SP]
    // 0xa92bc0: mov             x1, x0
    // 0xa92bc4: r4 = const [0, 0x2, 0x1, 0x1, debugLabel, 0x1, null]
    //     0xa92bc4: ldr             x4, [PP, #0x2328]  ; [pp+0x2328] List(7) [0, 0x2, 0x1, 0x1, "debugLabel", 0x1, Null]
    // 0xa92bc8: r0 = FocusScopeNode()
    //     0xa92bc8: bl              #0x693c70  ; [package:flutter/src/widgets/focus_manager.dart] FocusScopeNode::FocusScopeNode
    // 0xa92bcc: ldur            x0, [fp, #-0x18]
    // 0xa92bd0: ldur            x1, [fp, #-8]
    // 0xa92bd4: StoreField: r1->field_1b = r0
    //     0xa92bd4: stur            w0, [x1, #0x1b]
    //     0xa92bd8: ldurb           w16, [x1, #-1]
    //     0xa92bdc: ldurb           w17, [x0, #-1]
    //     0xa92be0: and             x16, x17, x16, lsr #2
    //     0xa92be4: tst             x16, HEAP, lsr #32
    //     0xa92be8: b.eq            #0xa92bf0
    //     0xa92bec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa92bf0: r0 = ScrollController()
    //     0xa92bf0: bl              #0x6852cc  ; AllocateScrollControllerStub -> ScrollController (size=0x40)
    // 0xa92bf4: mov             x1, x0
    // 0xa92bf8: stur            x0, [fp, #-0x10]
    // 0xa92bfc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa92bfc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa92c00: r0 = ScrollController()
    //     0xa92c00: bl              #0x685160  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0xa92c04: ldur            x0, [fp, #-0x10]
    // 0xa92c08: ldur            x1, [fp, #-8]
    // 0xa92c0c: StoreField: r1->field_1f = r0
    //     0xa92c0c: stur            w0, [x1, #0x1f]
    //     0xa92c10: ldurb           w16, [x1, #-1]
    //     0xa92c14: ldurb           w17, [x0, #-1]
    //     0xa92c18: and             x16, x17, x16, lsr #2
    //     0xa92c1c: tst             x16, HEAP, lsr #32
    //     0xa92c20: b.eq            #0xa92c28
    //     0xa92c24: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa92c28: r0 = Null
    //     0xa92c28: mov             x0, NULL
    // 0xa92c2c: LeaveFrame
    //     0xa92c2c: mov             SP, fp
    //     0xa92c30: ldp             fp, lr, [SP], #0x10
    // 0xa92c34: ret
    //     0xa92c34: ret             
    // 0xa92c38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa92c38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa92c3c: b               #0xa92b7c
  }
}

// class id: 4633, size: 0x24, field offset: 0x14
//   const constructor, 
class _ModalScopeStatus extends InheritedModel<dynamic> {

  _ updateShouldNotify(/* No info */) {
    // ** addr: 0xa8900c, size: 0xe0
    // 0xa8900c: EnterFrame
    //     0xa8900c: stp             fp, lr, [SP, #-0x10]!
    //     0xa89010: mov             fp, SP
    // 0xa89014: AllocStack(0x10)
    //     0xa89014: sub             SP, SP, #0x10
    // 0xa89018: SetupParameters(_ModalScopeStatus this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa89018: mov             x0, x2
    //     0xa8901c: mov             x4, x1
    //     0xa89020: mov             x3, x2
    //     0xa89024: stur            x1, [fp, #-8]
    //     0xa89028: stur            x2, [fp, #-0x10]
    // 0xa8902c: r2 = Null
    //     0xa8902c: mov             x2, NULL
    // 0xa89030: r1 = Null
    //     0xa89030: mov             x1, NULL
    // 0xa89034: r4 = 60
    //     0xa89034: movz            x4, #0x3c
    // 0xa89038: branchIfSmi(r0, 0xa89044)
    //     0xa89038: tbz             w0, #0, #0xa89044
    // 0xa8903c: r4 = LoadClassIdInstr(r0)
    //     0xa8903c: ldur            x4, [x0, #-1]
    //     0xa89040: ubfx            x4, x4, #0xc, #0x14
    // 0xa89044: r17 = 4633
    //     0xa89044: movz            x17, #0x1219
    // 0xa89048: cmp             x4, x17
    // 0xa8904c: b.eq            #0xa89064
    // 0xa89050: r8 = _ModalScopeStatus
    //     0xa89050: add             x8, PP, #0x4f, lsl #12  ; [pp+0x4fc70] Type: _ModalScopeStatus
    //     0xa89054: ldr             x8, [x8, #0xc70]
    // 0xa89058: r3 = Null
    //     0xa89058: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fc78] Null
    //     0xa8905c: ldr             x3, [x3, #0xc78]
    // 0xa89060: r0 = DefaultTypeTest()
    //     0xa89060: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xa89064: ldur            x1, [fp, #-8]
    // 0xa89068: LoadField: r2 = r1->field_13
    //     0xa89068: ldur            w2, [x1, #0x13]
    // 0xa8906c: DecompressPointer r2
    //     0xa8906c: add             x2, x2, HEAP, lsl #32
    // 0xa89070: ldur            x3, [fp, #-0x10]
    // 0xa89074: LoadField: r4 = r3->field_13
    //     0xa89074: ldur            w4, [x3, #0x13]
    // 0xa89078: DecompressPointer r4
    //     0xa89078: add             x4, x4, HEAP, lsl #32
    // 0xa8907c: cmp             w2, w4
    // 0xa89080: b.ne            #0xa890b4
    // 0xa89084: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa89084: ldur            w2, [x1, #0x17]
    // 0xa89088: DecompressPointer r2
    //     0xa89088: add             x2, x2, HEAP, lsl #32
    // 0xa8908c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xa8908c: ldur            w4, [x3, #0x17]
    // 0xa89090: DecompressPointer r4
    //     0xa89090: add             x4, x4, HEAP, lsl #32
    // 0xa89094: cmp             w2, w4
    // 0xa89098: b.ne            #0xa890b4
    // 0xa8909c: LoadField: r2 = r1->field_1b
    //     0xa8909c: ldur            w2, [x1, #0x1b]
    // 0xa890a0: DecompressPointer r2
    //     0xa890a0: add             x2, x2, HEAP, lsl #32
    // 0xa890a4: LoadField: r4 = r3->field_1b
    //     0xa890a4: ldur            w4, [x3, #0x1b]
    // 0xa890a8: DecompressPointer r4
    //     0xa890a8: add             x4, x4, HEAP, lsl #32
    // 0xa890ac: cmp             w2, w4
    // 0xa890b0: b.eq            #0xa890bc
    // 0xa890b4: r0 = true
    //     0xa890b4: add             x0, NULL, #0x20  ; true
    // 0xa890b8: b               #0xa890e0
    // 0xa890bc: LoadField: r2 = r1->field_1f
    //     0xa890bc: ldur            w2, [x1, #0x1f]
    // 0xa890c0: DecompressPointer r2
    //     0xa890c0: add             x2, x2, HEAP, lsl #32
    // 0xa890c4: LoadField: r1 = r3->field_1f
    //     0xa890c4: ldur            w1, [x3, #0x1f]
    // 0xa890c8: DecompressPointer r1
    //     0xa890c8: add             x1, x1, HEAP, lsl #32
    // 0xa890cc: cmp             w2, w1
    // 0xa890d0: r16 = true
    //     0xa890d0: add             x16, NULL, #0x20  ; true
    // 0xa890d4: r17 = false
    //     0xa890d4: add             x17, NULL, #0x30  ; false
    // 0xa890d8: csel            x3, x16, x17, ne
    // 0xa890dc: mov             x0, x3
    // 0xa890e0: LeaveFrame
    //     0xa890e0: mov             SP, fp
    //     0xa890e4: ldp             fp, lr, [SP], #0x10
    // 0xa890e8: ret
    //     0xa890e8: ret             
  }
  [closure] bool <anonymous closure>(dynamic, _ModalRouteAspect) {
    // ** addr: 0xd11fd0, size: 0xdc
    // 0xd11fd0: ldr             x1, [SP, #8]
    // 0xd11fd4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xd11fd4: ldur            w2, [x1, #0x17]
    // 0xd11fd8: DecompressPointer r2
    //     0xd11fd8: add             x2, x2, HEAP, lsl #32
    // 0xd11fdc: ldr             x1, [SP]
    // 0xd11fe0: LoadField: r3 = r1->field_7
    //     0xd11fe0: ldur            x3, [x1, #7]
    // 0xd11fe4: cmp             x3, #1
    // 0xd11fe8: b.gt            #0xd12064
    // 0xd11fec: cmp             x3, #0
    // 0xd11ff0: b.gt            #0xd1202c
    // 0xd11ff4: LoadField: r1 = r2->field_f
    //     0xd11ff4: ldur            w1, [x2, #0xf]
    // 0xd11ff8: DecompressPointer r1
    //     0xd11ff8: add             x1, x1, HEAP, lsl #32
    // 0xd11ffc: LoadField: r3 = r1->field_13
    //     0xd11ffc: ldur            w3, [x1, #0x13]
    // 0xd12000: DecompressPointer r3
    //     0xd12000: add             x3, x3, HEAP, lsl #32
    // 0xd12004: LoadField: r1 = r2->field_13
    //     0xd12004: ldur            w1, [x2, #0x13]
    // 0xd12008: DecompressPointer r1
    //     0xd12008: add             x1, x1, HEAP, lsl #32
    // 0xd1200c: LoadField: r4 = r1->field_13
    //     0xd1200c: ldur            w4, [x1, #0x13]
    // 0xd12010: DecompressPointer r4
    //     0xd12010: add             x4, x4, HEAP, lsl #32
    // 0xd12014: cmp             w3, w4
    // 0xd12018: r16 = true
    //     0xd12018: add             x16, NULL, #0x20  ; true
    // 0xd1201c: r17 = false
    //     0xd1201c: add             x17, NULL, #0x30  ; false
    // 0xd12020: csel            x1, x16, x17, ne
    // 0xd12024: mov             x0, x1
    // 0xd12028: b               #0xd120a8
    // 0xd1202c: LoadField: r1 = r2->field_f
    //     0xd1202c: ldur            w1, [x2, #0xf]
    // 0xd12030: DecompressPointer r1
    //     0xd12030: add             x1, x1, HEAP, lsl #32
    // 0xd12034: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xd12034: ldur            w3, [x1, #0x17]
    // 0xd12038: DecompressPointer r3
    //     0xd12038: add             x3, x3, HEAP, lsl #32
    // 0xd1203c: LoadField: r1 = r2->field_13
    //     0xd1203c: ldur            w1, [x2, #0x13]
    // 0xd12040: DecompressPointer r1
    //     0xd12040: add             x1, x1, HEAP, lsl #32
    // 0xd12044: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xd12044: ldur            w4, [x1, #0x17]
    // 0xd12048: DecompressPointer r4
    //     0xd12048: add             x4, x4, HEAP, lsl #32
    // 0xd1204c: cmp             w3, w4
    // 0xd12050: r16 = true
    //     0xd12050: add             x16, NULL, #0x20  ; true
    // 0xd12054: r17 = false
    //     0xd12054: add             x17, NULL, #0x30  ; false
    // 0xd12058: csel            x1, x16, x17, ne
    // 0xd1205c: mov             x0, x1
    // 0xd12060: b               #0xd120a8
    // 0xd12064: LoadField: r1 = r2->field_f
    //     0xd12064: ldur            w1, [x2, #0xf]
    // 0xd12068: DecompressPointer r1
    //     0xd12068: add             x1, x1, HEAP, lsl #32
    // 0xd1206c: LoadField: r3 = r1->field_1f
    //     0xd1206c: ldur            w3, [x1, #0x1f]
    // 0xd12070: DecompressPointer r3
    //     0xd12070: add             x3, x3, HEAP, lsl #32
    // 0xd12074: LoadField: r1 = r3->field_13
    //     0xd12074: ldur            w1, [x3, #0x13]
    // 0xd12078: DecompressPointer r1
    //     0xd12078: add             x1, x1, HEAP, lsl #32
    // 0xd1207c: LoadField: r3 = r2->field_13
    //     0xd1207c: ldur            w3, [x2, #0x13]
    // 0xd12080: DecompressPointer r3
    //     0xd12080: add             x3, x3, HEAP, lsl #32
    // 0xd12084: LoadField: r2 = r3->field_1f
    //     0xd12084: ldur            w2, [x3, #0x1f]
    // 0xd12088: DecompressPointer r2
    //     0xd12088: add             x2, x2, HEAP, lsl #32
    // 0xd1208c: LoadField: r3 = r2->field_13
    //     0xd1208c: ldur            w3, [x2, #0x13]
    // 0xd12090: DecompressPointer r3
    //     0xd12090: add             x3, x3, HEAP, lsl #32
    // 0xd12094: cmp             w1, w3
    // 0xd12098: r16 = true
    //     0xd12098: add             x16, NULL, #0x20  ; true
    // 0xd1209c: r17 = false
    //     0xd1209c: add             x17, NULL, #0x30  ; false
    // 0xd120a0: csel            x2, x16, x17, ne
    // 0xd120a4: mov             x0, x2
    // 0xd120a8: ret
    //     0xd120a8: ret             
  }
  _ updateShouldNotifyDependent(/* No info */) {
    // ** addr: 0xd2c2d0, size: 0xfc
    // 0xd2c2d0: EnterFrame
    //     0xd2c2d0: stp             fp, lr, [SP, #-0x10]!
    //     0xd2c2d4: mov             fp, SP
    // 0xd2c2d8: AllocStack(0x20)
    //     0xd2c2d8: sub             SP, SP, #0x20
    // 0xd2c2dc: SetupParameters(_ModalScopeStatus this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xd2c2dc: stur            x1, [fp, #-8]
    //     0xd2c2e0: mov             x16, x2
    //     0xd2c2e4: mov             x2, x1
    //     0xd2c2e8: mov             x1, x16
    //     0xd2c2ec: mov             x0, x3
    //     0xd2c2f0: stur            x1, [fp, #-0x10]
    //     0xd2c2f4: stur            x3, [fp, #-0x18]
    // 0xd2c2f8: CheckStackOverflow
    //     0xd2c2f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2c2fc: cmp             SP, x16
    //     0xd2c300: b.ls            #0xd2c3c4
    // 0xd2c304: r1 = 2
    //     0xd2c304: movz            x1, #0x2
    // 0xd2c308: r0 = AllocateContext()
    //     0xd2c308: bl              #0xec126c  ; AllocateContextStub
    // 0xd2c30c: mov             x3, x0
    // 0xd2c310: ldur            x0, [fp, #-8]
    // 0xd2c314: stur            x3, [fp, #-0x20]
    // 0xd2c318: StoreField: r3->field_f = r0
    //     0xd2c318: stur            w0, [x3, #0xf]
    // 0xd2c31c: ldur            x0, [fp, #-0x10]
    // 0xd2c320: StoreField: r3->field_13 = r0
    //     0xd2c320: stur            w0, [x3, #0x13]
    // 0xd2c324: r2 = Null
    //     0xd2c324: mov             x2, NULL
    // 0xd2c328: r1 = Null
    //     0xd2c328: mov             x1, NULL
    // 0xd2c32c: r4 = 60
    //     0xd2c32c: movz            x4, #0x3c
    // 0xd2c330: branchIfSmi(r0, 0xd2c33c)
    //     0xd2c330: tbz             w0, #0, #0xd2c33c
    // 0xd2c334: r4 = LoadClassIdInstr(r0)
    //     0xd2c334: ldur            x4, [x0, #-1]
    //     0xd2c338: ubfx            x4, x4, #0xc, #0x14
    // 0xd2c33c: r17 = 4633
    //     0xd2c33c: movz            x17, #0x1219
    // 0xd2c340: cmp             x4, x17
    // 0xd2c344: b.eq            #0xd2c35c
    // 0xd2c348: r8 = _ModalScopeStatus
    //     0xd2c348: add             x8, PP, #0x4f, lsl #12  ; [pp+0x4fc70] Type: _ModalScopeStatus
    //     0xd2c34c: ldr             x8, [x8, #0xc70]
    // 0xd2c350: r3 = Null
    //     0xd2c350: add             x3, PP, #0x59, lsl #12  ; [pp+0x59b90] Null
    //     0xd2c354: ldr             x3, [x3, #0xb90]
    // 0xd2c358: r0 = DefaultTypeTest()
    //     0xd2c358: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xd2c35c: ldur            x0, [fp, #-0x18]
    // 0xd2c360: r2 = Null
    //     0xd2c360: mov             x2, NULL
    // 0xd2c364: r1 = Null
    //     0xd2c364: mov             x1, NULL
    // 0xd2c368: r8 = Set<_ModalRouteAspect>
    //     0xd2c368: add             x8, PP, #0x56, lsl #12  ; [pp+0x56da0] Type: Set<_ModalRouteAspect>
    //     0xd2c36c: ldr             x8, [x8, #0xda0]
    // 0xd2c370: r3 = Null
    //     0xd2c370: add             x3, PP, #0x59, lsl #12  ; [pp+0x59ba0] Null
    //     0xd2c374: ldr             x3, [x3, #0xba0]
    // 0xd2c378: r0 = Set<_ModalRouteAspect>()
    //     0xd2c378: bl              #0xd120ac  ; IsType_Set<_ModalRouteAspect>_Stub
    // 0xd2c37c: ldur            x2, [fp, #-0x20]
    // 0xd2c380: r1 = Function '<anonymous closure>':.
    //     0xd2c380: add             x1, PP, #0x56, lsl #12  ; [pp+0x56db8] AnonymousClosure: (0xd11fd0), in [package:flutter/src/widgets/routes.dart] _ModalScopeStatus::updateShouldNotifyDependent (0xd2c2d0)
    //     0xd2c384: ldr             x1, [x1, #0xdb8]
    // 0xd2c388: r0 = AllocateClosure()
    //     0xd2c388: bl              #0xec1630  ; AllocateClosureStub
    // 0xd2c38c: ldur            x1, [fp, #-0x18]
    // 0xd2c390: r2 = LoadClassIdInstr(r1)
    //     0xd2c390: ldur            x2, [x1, #-1]
    //     0xd2c394: ubfx            x2, x2, #0xc, #0x14
    // 0xd2c398: mov             x16, x0
    // 0xd2c39c: mov             x0, x2
    // 0xd2c3a0: mov             x2, x16
    // 0xd2c3a4: r0 = GDT[cid_x0 + 0x10bb9]()
    //     0xd2c3a4: movz            x17, #0xbb9
    //     0xd2c3a8: movk            x17, #0x1, lsl #16
    //     0xd2c3ac: add             lr, x0, x17
    //     0xd2c3b0: ldr             lr, [x21, lr, lsl #3]
    //     0xd2c3b4: blr             lr
    // 0xd2c3b8: LeaveFrame
    //     0xd2c3b8: mov             SP, fp
    //     0xd2c3bc: ldp             fp, lr, [SP], #0x10
    // 0xd2c3c0: ret
    //     0xd2c3c0: ret             
    // 0xd2c3c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd2c3c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd2c3c8: b               #0xd2c304
  }
}

// class id: 4759, size: 0x14, field offset: 0xc
//   const constructor, 
class _ModalScope<X0> extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa92af4, size: 0x64
    // 0xa92af4: EnterFrame
    //     0xa92af4: stp             fp, lr, [SP, #-0x10]!
    //     0xa92af8: mov             fp, SP
    // 0xa92afc: AllocStack(0x8)
    //     0xa92afc: sub             SP, SP, #8
    // 0xa92b00: CheckStackOverflow
    //     0xa92b00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa92b04: cmp             SP, x16
    //     0xa92b08: b.ls            #0xa92b50
    // 0xa92b0c: LoadField: r2 = r1->field_b
    //     0xa92b0c: ldur            w2, [x1, #0xb]
    // 0xa92b10: DecompressPointer r2
    //     0xa92b10: add             x2, x2, HEAP, lsl #32
    // 0xa92b14: r1 = Null
    //     0xa92b14: mov             x1, NULL
    // 0xa92b18: r3 = <_ModalScope<X0>, X0>
    //     0xa92b18: add             x3, PP, #0x23, lsl #12  ; [pp+0x238d0] TypeArguments: <_ModalScope<X0>, X0>
    //     0xa92b1c: ldr             x3, [x3, #0x8d0]
    // 0xa92b20: r30 = InstantiateTypeArgumentsStub
    //     0xa92b20: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xa92b24: LoadField: r30 = r30->field_7
    //     0xa92b24: ldur            lr, [lr, #7]
    // 0xa92b28: blr             lr
    // 0xa92b2c: mov             x1, x0
    // 0xa92b30: r0 = _ModalScopeState()
    //     0xa92b30: bl              #0xa92c40  ; Allocate_ModalScopeStateStub -> _ModalScopeState<C1X0> (size=0x24)
    // 0xa92b34: mov             x1, x0
    // 0xa92b38: stur            x0, [fp, #-8]
    // 0xa92b3c: r0 = _ModalScopeState()
    //     0xa92b3c: bl              #0xa92b58  ; [package:flutter/src/widgets/routes.dart] _ModalScopeState::_ModalScopeState
    // 0xa92b40: ldur            x0, [fp, #-8]
    // 0xa92b44: LeaveFrame
    //     0xa92b44: mov             SP, fp
    //     0xa92b48: ldp             fp, lr, [SP], #0x10
    // 0xa92b4c: ret
    //     0xa92b4c: ret             
    // 0xa92b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa92b50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa92b54: b               #0xa92b0c
  }
}

// class id: 6947, size: 0x14, field offset: 0x14
enum _ModalRouteAspect extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4af1c, size: 0x64
    // 0xc4af1c: EnterFrame
    //     0xc4af1c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4af20: mov             fp, SP
    // 0xc4af24: AllocStack(0x10)
    //     0xc4af24: sub             SP, SP, #0x10
    // 0xc4af28: SetupParameters(_ModalRouteAspect this /* r1 => r0, fp-0x8 */)
    //     0xc4af28: mov             x0, x1
    //     0xc4af2c: stur            x1, [fp, #-8]
    // 0xc4af30: CheckStackOverflow
    //     0xc4af30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4af34: cmp             SP, x16
    //     0xc4af38: b.ls            #0xc4af78
    // 0xc4af3c: r1 = Null
    //     0xc4af3c: mov             x1, NULL
    // 0xc4af40: r2 = 4
    //     0xc4af40: movz            x2, #0x4
    // 0xc4af44: r0 = AllocateArray()
    //     0xc4af44: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4af48: r16 = "_ModalRouteAspect."
    //     0xc4af48: add             x16, PP, #0x25, lsl #12  ; [pp+0x25418] "_ModalRouteAspect."
    //     0xc4af4c: ldr             x16, [x16, #0x418]
    // 0xc4af50: StoreField: r0->field_f = r16
    //     0xc4af50: stur            w16, [x0, #0xf]
    // 0xc4af54: ldur            x1, [fp, #-8]
    // 0xc4af58: LoadField: r2 = r1->field_f
    //     0xc4af58: ldur            w2, [x1, #0xf]
    // 0xc4af5c: DecompressPointer r2
    //     0xc4af5c: add             x2, x2, HEAP, lsl #32
    // 0xc4af60: StoreField: r0->field_13 = r2
    //     0xc4af60: stur            w2, [x0, #0x13]
    // 0xc4af64: str             x0, [SP]
    // 0xc4af68: r0 = _interpolate()
    //     0xc4af68: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4af6c: LeaveFrame
    //     0xc4af6c: mov             SP, fp
    //     0xc4af70: ldp             fp, lr, [SP], #0x10
    // 0xc4af74: ret
    //     0xc4af74: ret             
    // 0xc4af78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4af78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4af7c: b               #0xc4af3c
  }
}
