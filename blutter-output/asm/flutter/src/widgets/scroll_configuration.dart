// lib: , url: package:flutter/src/widgets/scroll_configuration.dart

// class id: 1049175, size: 0x8
class :: {
}

// class id: 2587, size: 0x28, field offset: 0x8
//   const constructor, 
class _WrappedScrollBehavior extends Object
    implements <PERSON>rollBehavior {

  _ shouldNotify(/* No info */) {
    // ** addr: 0xd3319c, size: 0x17c
    // 0xd3319c: EnterFrame
    //     0xd3319c: stp             fp, lr, [SP, #-0x10]!
    //     0xd331a0: mov             fp, SP
    // 0xd331a4: AllocStack(0x30)
    //     0xd331a4: sub             SP, SP, #0x30
    // 0xd331a8: SetupParameters(_WrappedScrollBehavior this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xd331a8: mov             x4, x1
    //     0xd331ac: mov             x3, x2
    //     0xd331b0: stur            x1, [fp, #-8]
    //     0xd331b4: stur            x2, [fp, #-0x10]
    // 0xd331b8: CheckStackOverflow
    //     0xd331b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd331bc: cmp             SP, x16
    //     0xd331c0: b.ls            #0xd33310
    // 0xd331c4: mov             x0, x3
    // 0xd331c8: r2 = Null
    //     0xd331c8: mov             x2, NULL
    // 0xd331cc: r1 = Null
    //     0xd331cc: mov             x1, NULL
    // 0xd331d0: r4 = 60
    //     0xd331d0: movz            x4, #0x3c
    // 0xd331d4: branchIfSmi(r0, 0xd331e0)
    //     0xd331d4: tbz             w0, #0, #0xd331e0
    // 0xd331d8: r4 = LoadClassIdInstr(r0)
    //     0xd331d8: ldur            x4, [x0, #-1]
    //     0xd331dc: ubfx            x4, x4, #0xc, #0x14
    // 0xd331e0: cmp             x4, #0xa1b
    // 0xd331e4: b.eq            #0xd331fc
    // 0xd331e8: r8 = _WrappedScrollBehavior
    //     0xd331e8: add             x8, PP, #0x4f, lsl #12  ; [pp+0x4fb18] Type: _WrappedScrollBehavior
    //     0xd331ec: ldr             x8, [x8, #0xb18]
    // 0xd331f0: r3 = Null
    //     0xd331f0: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fb20] Null
    //     0xd331f4: ldr             x3, [x3, #0xb20]
    // 0xd331f8: r0 = DefaultTypeTest()
    //     0xd331f8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xd331fc: ldur            x1, [fp, #-0x10]
    // 0xd33200: LoadField: r0 = r1->field_7
    //     0xd33200: ldur            w0, [x1, #7]
    // 0xd33204: DecompressPointer r0
    //     0xd33204: add             x0, x0, HEAP, lsl #32
    // 0xd33208: ldur            x2, [fp, #-8]
    // 0xd3320c: LoadField: r3 = r2->field_7
    //     0xd3320c: ldur            w3, [x2, #7]
    // 0xd33210: DecompressPointer r3
    //     0xd33210: add             x3, x3, HEAP, lsl #32
    // 0xd33214: stur            x3, [fp, #-0x18]
    // 0xd33218: stp             x3, x0, [SP]
    // 0xd3321c: r0 = _haveSameRuntimeType()
    //     0xd3321c: bl              #0x6c18d0  ; [dart:core] Object::_haveSameRuntimeType
    // 0xd33220: tbnz            w0, #4, #0xd332f8
    // 0xd33224: ldur            x2, [fp, #-8]
    // 0xd33228: ldur            x0, [fp, #-0x10]
    // 0xd3322c: LoadField: r1 = r0->field_b
    //     0xd3322c: ldur            w1, [x0, #0xb]
    // 0xd33230: DecompressPointer r1
    //     0xd33230: add             x1, x1, HEAP, lsl #32
    // 0xd33234: LoadField: r3 = r2->field_b
    //     0xd33234: ldur            w3, [x2, #0xb]
    // 0xd33238: DecompressPointer r3
    //     0xd33238: add             x3, x3, HEAP, lsl #32
    // 0xd3323c: cmp             w1, w3
    // 0xd33240: b.ne            #0xd332f8
    // 0xd33244: LoadField: r1 = r0->field_f
    //     0xd33244: ldur            w1, [x0, #0xf]
    // 0xd33248: DecompressPointer r1
    //     0xd33248: add             x1, x1, HEAP, lsl #32
    // 0xd3324c: LoadField: r3 = r2->field_f
    //     0xd3324c: ldur            w3, [x2, #0xf]
    // 0xd33250: DecompressPointer r3
    //     0xd33250: add             x3, x3, HEAP, lsl #32
    // 0xd33254: cmp             w1, w3
    // 0xd33258: b.ne            #0xd332f8
    // 0xd3325c: mov             x1, x0
    // 0xd33260: r0 = dragDevices()
    //     0xd33260: bl              #0xdbac20  ; [package:flutter/src/widgets/scroll_configuration.dart] _WrappedScrollBehavior::dragDevices
    // 0xd33264: mov             x1, x0
    // 0xd33268: ldur            x0, [fp, #-8]
    // 0xd3326c: LoadField: r2 = r0->field_1b
    //     0xd3326c: ldur            w2, [x0, #0x1b]
    // 0xd33270: DecompressPointer r2
    //     0xd33270: add             x2, x2, HEAP, lsl #32
    // 0xd33274: cmp             w2, NULL
    // 0xd33278: b.ne            #0xd33284
    // 0xd3327c: r2 = _ConstSet len:5
    //     0xd3327c: add             x2, PP, #0x4f, lsl #12  ; [pp+0x4fb30] Set<PointerDeviceKind>(5)
    //     0xd33280: ldr             x2, [x2, #0xb30]
    // 0xd33284: r16 = <PointerDeviceKind>
    //     0xd33284: add             x16, PP, #0x25, lsl #12  ; [pp+0x25380] TypeArguments: <PointerDeviceKind>
    //     0xd33288: ldr             x16, [x16, #0x380]
    // 0xd3328c: stp             x1, x16, [SP, #8]
    // 0xd33290: str             x2, [SP]
    // 0xd33294: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xd33294: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xd33298: r0 = setEquals()
    //     0xd33298: bl              #0x7a737c  ; [package:flutter/src/foundation/collections.dart] ::setEquals
    // 0xd3329c: tbnz            w0, #4, #0xd332f8
    // 0xd332a0: ldur            x0, [fp, #-8]
    // 0xd332a4: ldur            x1, [fp, #-0x10]
    // 0xd332a8: r0 = pointerAxisModifiers()
    //     0xd332a8: bl              #0xdb8900  ; [package:flutter/src/widgets/scroll_configuration.dart] _WrappedScrollBehavior::pointerAxisModifiers
    // 0xd332ac: mov             x2, x0
    // 0xd332b0: ldur            x0, [fp, #-8]
    // 0xd332b4: stur            x2, [fp, #-0x10]
    // 0xd332b8: LoadField: r1 = r0->field_23
    //     0xd332b8: ldur            w1, [x0, #0x23]
    // 0xd332bc: DecompressPointer r1
    //     0xd332bc: add             x1, x1, HEAP, lsl #32
    // 0xd332c0: cmp             w1, NULL
    // 0xd332c4: b.ne            #0xd332d4
    // 0xd332c8: ldur            x1, [fp, #-0x18]
    // 0xd332cc: r0 = pointerAxisModifiers()
    //     0xd332cc: bl              #0xd89f98  ; [package:flutter/src/widgets/scroll_configuration.dart] ScrollBehavior::pointerAxisModifiers
    // 0xd332d0: b               #0xd332d8
    // 0xd332d4: mov             x0, x1
    // 0xd332d8: r16 = <LogicalKeyboardKey>
    //     0xd332d8: add             x16, PP, #0x43, lsl #12  ; [pp+0x43b60] TypeArguments: <LogicalKeyboardKey>
    //     0xd332dc: ldr             x16, [x16, #0xb60]
    // 0xd332e0: ldur            lr, [fp, #-0x10]
    // 0xd332e4: stp             lr, x16, [SP, #8]
    // 0xd332e8: str             x0, [SP]
    // 0xd332ec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xd332ec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xd332f0: r0 = setEquals()
    //     0xd332f0: bl              #0x7a737c  ; [package:flutter/src/foundation/collections.dart] ::setEquals
    // 0xd332f4: tbz             w0, #4, #0xd33300
    // 0xd332f8: r0 = true
    //     0xd332f8: add             x0, NULL, #0x20  ; true
    // 0xd332fc: b               #0xd33304
    // 0xd33300: r0 = false
    //     0xd33300: add             x0, NULL, #0x30  ; false
    // 0xd33304: LeaveFrame
    //     0xd33304: mov             SP, fp
    //     0xd33308: ldp             fp, lr, [SP], #0x10
    // 0xd3330c: ret
    //     0xd3330c: ret             
    // 0xd33310: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd33310: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd33314: b               #0xd331c4
  }
  _ getScrollPhysics(/* No info */) {
    // ** addr: 0xd44718, size: 0x38
    // 0xd44718: EnterFrame
    //     0xd44718: stp             fp, lr, [SP, #-0x10]!
    //     0xd4471c: mov             fp, SP
    // 0xd44720: CheckStackOverflow
    //     0xd44720: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd44724: cmp             SP, x16
    //     0xd44728: b.ls            #0xd44748
    // 0xd4472c: LoadField: r0 = r1->field_7
    //     0xd4472c: ldur            w0, [x1, #7]
    // 0xd44730: DecompressPointer r0
    //     0xd44730: add             x0, x0, HEAP, lsl #32
    // 0xd44734: mov             x1, x0
    // 0xd44738: r0 = getScrollPhysics()
    //     0xd44738: bl              #0xd39340  ; [package:flutter/src/widgets/scroll_configuration.dart] ScrollBehavior::getScrollPhysics
    // 0xd4473c: LeaveFrame
    //     0xd4473c: mov             SP, fp
    //     0xd44740: ldp             fp, lr, [SP], #0x10
    // 0xd44744: ret
    //     0xd44744: ret             
    // 0xd44748: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd44748: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd4474c: b               #0xd4472c
  }
  get _ pointerAxisModifiers(/* No info */) {
    // ** addr: 0xdb8900, size: 0x48
    // 0xdb8900: EnterFrame
    //     0xdb8900: stp             fp, lr, [SP, #-0x10]!
    //     0xdb8904: mov             fp, SP
    // 0xdb8908: CheckStackOverflow
    //     0xdb8908: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb890c: cmp             SP, x16
    //     0xdb8910: b.ls            #0xdb8940
    // 0xdb8914: LoadField: r0 = r1->field_23
    //     0xdb8914: ldur            w0, [x1, #0x23]
    // 0xdb8918: DecompressPointer r0
    //     0xdb8918: add             x0, x0, HEAP, lsl #32
    // 0xdb891c: cmp             w0, NULL
    // 0xdb8920: b.ne            #0xdb8934
    // 0xdb8924: LoadField: r0 = r1->field_7
    //     0xdb8924: ldur            w0, [x1, #7]
    // 0xdb8928: DecompressPointer r0
    //     0xdb8928: add             x0, x0, HEAP, lsl #32
    // 0xdb892c: mov             x1, x0
    // 0xdb8930: r0 = pointerAxisModifiers()
    //     0xdb8930: bl              #0xd89f98  ; [package:flutter/src/widgets/scroll_configuration.dart] ScrollBehavior::pointerAxisModifiers
    // 0xdb8934: LeaveFrame
    //     0xdb8934: mov             SP, fp
    //     0xdb8938: ldp             fp, lr, [SP], #0x10
    // 0xdb893c: ret
    //     0xdb893c: ret             
    // 0xdb8940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb8940: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb8944: b               #0xdb8914
  }
  _ velocityTrackerBuilder(/* No info */) {
    // ** addr: 0xdb8d50, size: 0x38
    // 0xdb8d50: EnterFrame
    //     0xdb8d50: stp             fp, lr, [SP, #-0x10]!
    //     0xdb8d54: mov             fp, SP
    // 0xdb8d58: CheckStackOverflow
    //     0xdb8d58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb8d5c: cmp             SP, x16
    //     0xdb8d60: b.ls            #0xdb8d80
    // 0xdb8d64: LoadField: r0 = r1->field_7
    //     0xdb8d64: ldur            w0, [x1, #7]
    // 0xdb8d68: DecompressPointer r0
    //     0xdb8d68: add             x0, x0, HEAP, lsl #32
    // 0xdb8d6c: mov             x1, x0
    // 0xdb8d70: r0 = velocityTrackerBuilder()
    //     0xdb8d70: bl              #0xd8d5e0  ; [package:flutter/src/widgets/scroll_configuration.dart] ScrollBehavior::velocityTrackerBuilder
    // 0xdb8d74: LeaveFrame
    //     0xdb8d74: mov             SP, fp
    //     0xdb8d78: ldp             fp, lr, [SP], #0x10
    // 0xdb8d7c: ret
    //     0xdb8d7c: ret             
    // 0xdb8d80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb8d80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb8d84: b               #0xdb8d64
  }
  _ buildOverscrollIndicator(/* No info */) {
    // ** addr: 0xdb8e44, size: 0x6c
    // 0xdb8e44: EnterFrame
    //     0xdb8e44: stp             fp, lr, [SP, #-0x10]!
    //     0xdb8e48: mov             fp, SP
    // 0xdb8e4c: CheckStackOverflow
    //     0xdb8e4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb8e50: cmp             SP, x16
    //     0xdb8e54: b.ls            #0xdb8ea8
    // 0xdb8e58: LoadField: r0 = r1->field_f
    //     0xdb8e58: ldur            w0, [x1, #0xf]
    // 0xdb8e5c: DecompressPointer r0
    //     0xdb8e5c: add             x0, x0, HEAP, lsl #32
    // 0xdb8e60: tbnz            w0, #4, #0xdb8e98
    // 0xdb8e64: LoadField: r0 = r1->field_7
    //     0xdb8e64: ldur            w0, [x1, #7]
    // 0xdb8e68: DecompressPointer r0
    //     0xdb8e68: add             x0, x0, HEAP, lsl #32
    // 0xdb8e6c: r1 = LoadClassIdInstr(r0)
    //     0xdb8e6c: ldur            x1, [x0, #-1]
    //     0xdb8e70: ubfx            x1, x1, #0xc, #0x14
    // 0xdb8e74: mov             x16, x0
    // 0xdb8e78: mov             x0, x1
    // 0xdb8e7c: mov             x1, x16
    // 0xdb8e80: r0 = GDT[cid_x0 + -0xfae]()
    //     0xdb8e80: sub             lr, x0, #0xfae
    //     0xdb8e84: ldr             lr, [x21, lr, lsl #3]
    //     0xdb8e88: blr             lr
    // 0xdb8e8c: LeaveFrame
    //     0xdb8e8c: mov             SP, fp
    //     0xdb8e90: ldp             fp, lr, [SP], #0x10
    // 0xdb8e94: ret
    //     0xdb8e94: ret             
    // 0xdb8e98: mov             x0, x3
    // 0xdb8e9c: LeaveFrame
    //     0xdb8e9c: mov             SP, fp
    //     0xdb8ea0: ldp             fp, lr, [SP], #0x10
    // 0xdb8ea4: ret
    //     0xdb8ea4: ret             
    // 0xdb8ea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb8ea8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb8eac: b               #0xdb8e58
  }
  _ buildScrollbar(/* No info */) {
    // ** addr: 0xdb92b0, size: 0x6c
    // 0xdb92b0: EnterFrame
    //     0xdb92b0: stp             fp, lr, [SP, #-0x10]!
    //     0xdb92b4: mov             fp, SP
    // 0xdb92b8: CheckStackOverflow
    //     0xdb92b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdb92bc: cmp             SP, x16
    //     0xdb92c0: b.ls            #0xdb9314
    // 0xdb92c4: LoadField: r0 = r1->field_b
    //     0xdb92c4: ldur            w0, [x1, #0xb]
    // 0xdb92c8: DecompressPointer r0
    //     0xdb92c8: add             x0, x0, HEAP, lsl #32
    // 0xdb92cc: tbnz            w0, #4, #0xdb9304
    // 0xdb92d0: LoadField: r0 = r1->field_7
    //     0xdb92d0: ldur            w0, [x1, #7]
    // 0xdb92d4: DecompressPointer r0
    //     0xdb92d4: add             x0, x0, HEAP, lsl #32
    // 0xdb92d8: r1 = LoadClassIdInstr(r0)
    //     0xdb92d8: ldur            x1, [x0, #-1]
    //     0xdb92dc: ubfx            x1, x1, #0xc, #0x14
    // 0xdb92e0: mov             x16, x0
    // 0xdb92e4: mov             x0, x1
    // 0xdb92e8: mov             x1, x16
    // 0xdb92ec: r0 = GDT[cid_x0 + -0xfc4]()
    //     0xdb92ec: sub             lr, x0, #0xfc4
    //     0xdb92f0: ldr             lr, [x21, lr, lsl #3]
    //     0xdb92f4: blr             lr
    // 0xdb92f8: LeaveFrame
    //     0xdb92f8: mov             SP, fp
    //     0xdb92fc: ldp             fp, lr, [SP], #0x10
    // 0xdb9300: ret
    //     0xdb9300: ret             
    // 0xdb9304: mov             x0, x3
    // 0xdb9308: LeaveFrame
    //     0xdb9308: mov             SP, fp
    //     0xdb930c: ldp             fp, lr, [SP], #0x10
    // 0xdb9310: ret
    //     0xdb9310: ret             
    // 0xdb9314: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdb9314: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdb9318: b               #0xdb92c4
  }
  _ getMultitouchDragStrategy(/* No info */) {
    // ** addr: 0xdbabe8, size: 0x38
    // 0xdbabe8: EnterFrame
    //     0xdbabe8: stp             fp, lr, [SP, #-0x10]!
    //     0xdbabec: mov             fp, SP
    // 0xdbabf0: CheckStackOverflow
    //     0xdbabf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdbabf4: cmp             SP, x16
    //     0xdbabf8: b.ls            #0xdbac18
    // 0xdbabfc: LoadField: r0 = r1->field_7
    //     0xdbabfc: ldur            w0, [x1, #7]
    // 0xdbac00: DecompressPointer r0
    //     0xdbac00: add             x0, x0, HEAP, lsl #32
    // 0xdbac04: mov             x1, x0
    // 0xdbac08: r0 = getMultitouchDragStrategy()
    //     0xdbac08: bl              #0xd902bc  ; [package:flutter/src/widgets/scroll_configuration.dart] ScrollBehavior::getMultitouchDragStrategy
    // 0xdbac0c: LeaveFrame
    //     0xdbac0c: mov             SP, fp
    //     0xdbac10: ldp             fp, lr, [SP], #0x10
    // 0xdbac14: ret
    //     0xdbac14: ret             
    // 0xdbac18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdbac18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdbac1c: b               #0xdbabfc
  }
  get _ dragDevices(/* No info */) {
    // ** addr: 0xdbac20, size: 0x24
    // 0xdbac20: LoadField: r2 = r1->field_1b
    //     0xdbac20: ldur            w2, [x1, #0x1b]
    // 0xdbac24: DecompressPointer r2
    //     0xdbac24: add             x2, x2, HEAP, lsl #32
    // 0xdbac28: cmp             w2, NULL
    // 0xdbac2c: b.ne            #0xdbac3c
    // 0xdbac30: r0 = _ConstSet len:5
    //     0xdbac30: add             x0, PP, #0x4f, lsl #12  ; [pp+0x4fb30] Set<PointerDeviceKind>(5)
    //     0xdbac34: ldr             x0, [x0, #0xb30]
    // 0xdbac38: b               #0xdbac40
    // 0xdbac3c: mov             x0, x2
    // 0xdbac40: ret
    //     0xdbac40: ret             
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xdbac9c, size: 0x1f0
    // 0xdbac9c: EnterFrame
    //     0xdbac9c: stp             fp, lr, [SP, #-0x10]!
    //     0xdbaca0: mov             fp, SP
    // 0xdbaca4: AllocStack(0x40)
    //     0xdbaca4: sub             SP, SP, #0x40
    // 0xdbaca8: SetupParameters({dynamic dragDevices = Null /* r3 */, dynamic overscroll = Null /* r5 */, dynamic pointerAxisModifiers, dynamic scrollbars = Null /* r0 */})
    //     0xdbaca8: ldur            w0, [x4, #0x13]
    //     0xdbacac: ldur            w2, [x4, #0x1f]
    //     0xdbacb0: add             x2, x2, HEAP, lsl #32
    //     0xdbacb4: add             x16, PP, #0x4f, lsl #12  ; [pp+0x4fc10] "dragDevices"
    //     0xdbacb8: ldr             x16, [x16, #0xc10]
    //     0xdbacbc: cmp             w2, w16
    //     0xdbacc0: b.ne            #0xdbace4
    //     0xdbacc4: ldur            w2, [x4, #0x23]
    //     0xdbacc8: add             x2, x2, HEAP, lsl #32
    //     0xdbaccc: sub             w3, w0, w2
    //     0xdbacd0: add             x2, fp, w3, sxtw #2
    //     0xdbacd4: ldr             x2, [x2, #8]
    //     0xdbacd8: mov             x3, x2
    //     0xdbacdc: movz            x2, #0x1
    //     0xdbace0: b               #0xdbacec
    //     0xdbace4: mov             x3, NULL
    //     0xdbace8: movz            x2, #0
    //     0xdbacec: lsl             x5, x2, #1
    //     0xdbacf0: lsl             w6, w5, #1
    //     0xdbacf4: add             w7, w6, #8
    //     0xdbacf8: add             x16, x4, w7, sxtw #1
    //     0xdbacfc: ldur            w8, [x16, #0xf]
    //     0xdbad00: add             x8, x8, HEAP, lsl #32
    //     0xdbad04: add             x16, PP, #0x4f, lsl #12  ; [pp+0x4fc18] "overscroll"
    //     0xdbad08: ldr             x16, [x16, #0xc18]
    //     0xdbad0c: cmp             w8, w16
    //     0xdbad10: b.ne            #0xdbad44
    //     0xdbad14: add             w2, w6, #0xa
    //     0xdbad18: add             x16, x4, w2, sxtw #1
    //     0xdbad1c: ldur            w6, [x16, #0xf]
    //     0xdbad20: add             x6, x6, HEAP, lsl #32
    //     0xdbad24: sub             w2, w0, w6
    //     0xdbad28: add             x6, fp, w2, sxtw #2
    //     0xdbad2c: ldr             x6, [x6, #8]
    //     0xdbad30: add             w2, w5, #2
    //     0xdbad34: sbfx            x5, x2, #1, #0x1f
    //     0xdbad38: mov             x2, x5
    //     0xdbad3c: mov             x5, x6
    //     0xdbad40: b               #0xdbad48
    //     0xdbad44: mov             x5, NULL
    //     0xdbad48: lsl             x6, x2, #1
    //     0xdbad4c: lsl             w7, w6, #1
    //     0xdbad50: add             w8, w7, #8
    //     0xdbad54: add             x16, x4, w8, sxtw #1
    //     0xdbad58: ldur            w7, [x16, #0xf]
    //     0xdbad5c: add             x7, x7, HEAP, lsl #32
    //     0xdbad60: add             x16, PP, #0x4f, lsl #12  ; [pp+0x4fc20] "pointerAxisModifiers"
    //     0xdbad64: ldr             x16, [x16, #0xc20]
    //     0xdbad68: cmp             w7, w16
    //     0xdbad6c: b.ne            #0xdbad7c
    //     0xdbad70: add             w2, w6, #2
    //     0xdbad74: sbfx            x6, x2, #1, #0x1f
    //     0xdbad78: mov             x2, x6
    //     0xdbad7c: lsl             x6, x2, #1
    //     0xdbad80: lsl             w2, w6, #1
    //     0xdbad84: add             w6, w2, #8
    //     0xdbad88: add             x16, x4, w6, sxtw #1
    //     0xdbad8c: ldur            w7, [x16, #0xf]
    //     0xdbad90: add             x7, x7, HEAP, lsl #32
    //     0xdbad94: add             x16, PP, #0x4f, lsl #12  ; [pp+0x4fc28] "scrollbars"
    //     0xdbad98: ldr             x16, [x16, #0xc28]
    //     0xdbad9c: cmp             w7, w16
    //     0xdbada0: b.ne            #0xdbadc4
    //     0xdbada4: add             w6, w2, #0xa
    //     0xdbada8: add             x16, x4, w6, sxtw #1
    //     0xdbadac: ldur            w2, [x16, #0xf]
    //     0xdbadb0: add             x2, x2, HEAP, lsl #32
    //     0xdbadb4: sub             w4, w0, w2
    //     0xdbadb8: add             x0, fp, w4, sxtw #2
    //     0xdbadbc: ldr             x0, [x0, #8]
    //     0xdbadc0: b               #0xdbadc8
    //     0xdbadc4: mov             x0, NULL
    // 0xdbadc8: CheckStackOverflow
    //     0xdbadc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdbadcc: cmp             SP, x16
    //     0xdbadd0: b.ls            #0xdbae84
    // 0xdbadd4: LoadField: r2 = r1->field_7
    //     0xdbadd4: ldur            w2, [x1, #7]
    // 0xdbadd8: DecompressPointer r2
    //     0xdbadd8: add             x2, x2, HEAP, lsl #32
    // 0xdbaddc: stur            x2, [fp, #-0x20]
    // 0xdbade0: cmp             w0, NULL
    // 0xdbade4: b.ne            #0xdbadf0
    // 0xdbade8: LoadField: r0 = r1->field_b
    //     0xdbade8: ldur            w0, [x1, #0xb]
    // 0xdbadec: DecompressPointer r0
    //     0xdbadec: add             x0, x0, HEAP, lsl #32
    // 0xdbadf0: stur            x0, [fp, #-0x18]
    // 0xdbadf4: cmp             w5, NULL
    // 0xdbadf8: b.ne            #0xdbae08
    // 0xdbadfc: LoadField: r4 = r1->field_f
    //     0xdbadfc: ldur            w4, [x1, #0xf]
    // 0xdbae00: DecompressPointer r4
    //     0xdbae00: add             x4, x4, HEAP, lsl #32
    // 0xdbae04: b               #0xdbae0c
    // 0xdbae08: mov             x4, x5
    // 0xdbae0c: stur            x4, [fp, #-0x10]
    // 0xdbae10: cmp             w3, NULL
    // 0xdbae14: b.ne            #0xdbae30
    // 0xdbae18: LoadField: r3 = r1->field_1b
    //     0xdbae18: ldur            w3, [x1, #0x1b]
    // 0xdbae1c: DecompressPointer r3
    //     0xdbae1c: add             x3, x3, HEAP, lsl #32
    // 0xdbae20: cmp             w3, NULL
    // 0xdbae24: b.ne            #0xdbae30
    // 0xdbae28: r3 = _ConstSet len:5
    //     0xdbae28: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fb30] Set<PointerDeviceKind>(5)
    //     0xdbae2c: ldr             x3, [x3, #0xb30]
    // 0xdbae30: stur            x3, [fp, #-8]
    // 0xdbae34: LoadField: r5 = r1->field_23
    //     0xdbae34: ldur            w5, [x1, #0x23]
    // 0xdbae38: DecompressPointer r5
    //     0xdbae38: add             x5, x5, HEAP, lsl #32
    // 0xdbae3c: cmp             w5, NULL
    // 0xdbae40: b.ne            #0xdbae50
    // 0xdbae44: mov             x1, x2
    // 0xdbae48: r0 = pointerAxisModifiers()
    //     0xdbae48: bl              #0xd89f98  ; [package:flutter/src/widgets/scroll_configuration.dart] ScrollBehavior::pointerAxisModifiers
    // 0xdbae4c: b               #0xdbae54
    // 0xdbae50: mov             x0, x5
    // 0xdbae54: ldur            x16, [fp, #-0x18]
    // 0xdbae58: ldur            lr, [fp, #-0x10]
    // 0xdbae5c: stp             lr, x16, [SP, #0x10]
    // 0xdbae60: ldur            x16, [fp, #-8]
    // 0xdbae64: stp             x0, x16, [SP]
    // 0xdbae68: ldur            x1, [fp, #-0x20]
    // 0xdbae6c: r4 = const [0, 0x5, 0x4, 0x1, dragDevices, 0x3, overscroll, 0x2, pointerAxisModifiers, 0x4, scrollbars, 0x1, null]
    //     0xdbae6c: add             x4, PP, #0x56, lsl #12  ; [pp+0x567a8] List(13) [0, 0x5, 0x4, 0x1, "dragDevices", 0x3, "overscroll", 0x2, "pointerAxisModifiers", 0x4, "scrollbars", 0x1, Null]
    //     0xdbae70: ldr             x4, [x4, #0x7a8]
    // 0xdbae74: r0 = copyWith()
    //     0xdbae74: bl              #0xd90314  ; [package:flutter/src/widgets/scroll_configuration.dart] ScrollBehavior::copyWith
    // 0xdbae78: LeaveFrame
    //     0xdbae78: mov             SP, fp
    //     0xdbae7c: ldp             fp, lr, [SP], #0x10
    // 0xdbae80: ret
    //     0xdbae80: ret             
    // 0xdbae84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdbae84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdbae88: b               #0xdbadd4
  }
  _ getPlatform(/* No info */) {
    // ** addr: 0xdbae8c, size: 0x54
    // 0xdbae8c: EnterFrame
    //     0xdbae8c: stp             fp, lr, [SP, #-0x10]!
    //     0xdbae90: mov             fp, SP
    // 0xdbae94: CheckStackOverflow
    //     0xdbae94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdbae98: cmp             SP, x16
    //     0xdbae9c: b.ls            #0xdbaed8
    // 0xdbaea0: LoadField: r0 = r1->field_7
    //     0xdbaea0: ldur            w0, [x1, #7]
    // 0xdbaea4: DecompressPointer r0
    //     0xdbaea4: add             x0, x0, HEAP, lsl #32
    // 0xdbaea8: r1 = LoadClassIdInstr(r0)
    //     0xdbaea8: ldur            x1, [x0, #-1]
    //     0xdbaeac: ubfx            x1, x1, #0xc, #0x14
    // 0xdbaeb0: mov             x16, x0
    // 0xdbaeb4: mov             x0, x1
    // 0xdbaeb8: mov             x1, x16
    // 0xdbaebc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xdbaebc: sub             lr, x0, #1, lsl #12
    //     0xdbaec0: ldr             lr, [x21, lr, lsl #3]
    //     0xdbaec4: blr             lr
    // 0xdbaec8: r0 = Instance_TargetPlatform
    //     0xdbaec8: ldr             x0, [PP, #0x5110]  ; [pp+0x5110] Obj!TargetPlatform@e36f61
    // 0xdbaecc: LeaveFrame
    //     0xdbaecc: mov             SP, fp
    //     0xdbaed0: ldp             fp, lr, [SP], #0x10
    // 0xdbaed4: ret
    //     0xdbaed4: ret             
    // 0xdbaed8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdbaed8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdbaedc: b               #0xdbaea0
  }
}

// class id: 3425, size: 0x8, field offset: 0x8
//   const constructor, 
class ScrollBehavior extends Object {

  _ getScrollPhysics(/* No info */) {
    // ** addr: 0xd39340, size: 0x4c
    // 0xd39340: EnterFrame
    //     0xd39340: stp             fp, lr, [SP, #-0x10]!
    //     0xd39344: mov             fp, SP
    // 0xd39348: mov             x0, x1
    // 0xd3934c: mov             x1, x2
    // 0xd39350: CheckStackOverflow
    //     0xd39350: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd39354: cmp             SP, x16
    //     0xd39358: b.ls            #0xd39384
    // 0xd3935c: r2 = LoadClassIdInstr(r0)
    //     0xd3935c: ldur            x2, [x0, #-1]
    //     0xd39360: ubfx            x2, x2, #0xc, #0x14
    // 0xd39364: cmp             x2, #0xd61
    // 0xd39368: b.eq            #0xd39370
    // 0xd3936c: r0 = of()
    //     0xd3936c: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xd39370: r0 = Instance_ClampingScrollPhysics
    //     0xd39370: add             x0, PP, #0x45, lsl #12  ; [pp+0x45ea0] Obj!ClampingScrollPhysics@e0fd71
    //     0xd39374: ldr             x0, [x0, #0xea0]
    // 0xd39378: LeaveFrame
    //     0xd39378: mov             SP, fp
    //     0xd3937c: ldp             fp, lr, [SP], #0x10
    // 0xd39380: ret
    //     0xd39380: ret             
    // 0xd39384: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd39384: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd39388: b               #0xd3935c
  }
  get _ pointerAxisModifiers(/* No info */) {
    // ** addr: 0xd89f98, size: 0xb8
    // 0xd89f98: EnterFrame
    //     0xd89f98: stp             fp, lr, [SP, #-0x10]!
    //     0xd89f9c: mov             fp, SP
    // 0xd89fa0: AllocStack(0x10)
    //     0xd89fa0: sub             SP, SP, #0x10
    // 0xd89fa4: CheckStackOverflow
    //     0xd89fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd89fa8: cmp             SP, x16
    //     0xd89fac: b.ls            #0xd8a048
    // 0xd89fb0: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xd89fb0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xd89fb4: ldr             x0, [x0, #0x778]
    //     0xd89fb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xd89fbc: cmp             w0, w16
    //     0xd89fc0: b.ne            #0xd89fcc
    //     0xd89fc4: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xd89fc8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xd89fcc: r1 = <LogicalKeyboardKey>
    //     0xd89fcc: add             x1, PP, #0x43, lsl #12  ; [pp+0x43b60] TypeArguments: <LogicalKeyboardKey>
    //     0xd89fd0: ldr             x1, [x1, #0xb60]
    // 0xd89fd4: stur            x0, [fp, #-8]
    // 0xd89fd8: r0 = _Set()
    //     0xd89fd8: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xd89fdc: mov             x1, x0
    // 0xd89fe0: ldur            x0, [fp, #-8]
    // 0xd89fe4: stur            x1, [fp, #-0x10]
    // 0xd89fe8: StoreField: r1->field_1b = r0
    //     0xd89fe8: stur            w0, [x1, #0x1b]
    // 0xd89fec: StoreField: r1->field_b = rZR
    //     0xd89fec: stur            wzr, [x1, #0xb]
    // 0xd89ff0: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xd89ff0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xd89ff4: ldr             x0, [x0, #0x780]
    //     0xd89ff8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xd89ffc: cmp             w0, w16
    //     0xd8a000: b.ne            #0xd8a00c
    //     0xd8a004: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xd8a008: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xd8a00c: mov             x1, x0
    // 0xd8a010: ldur            x0, [fp, #-0x10]
    // 0xd8a014: StoreField: r0->field_f = r1
    //     0xd8a014: stur            w1, [x0, #0xf]
    // 0xd8a018: StoreField: r0->field_13 = rZR
    //     0xd8a018: stur            wzr, [x0, #0x13]
    // 0xd8a01c: ArrayStore: r0[0] = rZR  ; List_4
    //     0xd8a01c: stur            wzr, [x0, #0x17]
    // 0xd8a020: mov             x1, x0
    // 0xd8a024: r2 = Instance_LogicalKeyboardKey
    //     0xd8a024: ldr             x2, [PP, #0x3890]  ; [pp+0x3890] Obj!LogicalKeyboardKey@e17451
    // 0xd8a028: r0 = add()
    //     0xd8a028: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xd8a02c: ldur            x1, [fp, #-0x10]
    // 0xd8a030: r2 = Instance_LogicalKeyboardKey
    //     0xd8a030: ldr             x2, [PP, #0x38a0]  ; [pp+0x38a0] Obj!LogicalKeyboardKey@e17441
    // 0xd8a034: r0 = add()
    //     0xd8a034: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xd8a038: ldur            x0, [fp, #-0x10]
    // 0xd8a03c: LeaveFrame
    //     0xd8a03c: mov             SP, fp
    //     0xd8a040: ldp             fp, lr, [SP], #0x10
    // 0xd8a044: ret
    //     0xd8a044: ret             
    // 0xd8a048: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8a048: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8a04c: b               #0xd89fb0
  }
  _ velocityTrackerBuilder(/* No info */) {
    // ** addr: 0xd8d5e0, size: 0x54
    // 0xd8d5e0: EnterFrame
    //     0xd8d5e0: stp             fp, lr, [SP, #-0x10]!
    //     0xd8d5e4: mov             fp, SP
    // 0xd8d5e8: mov             x0, x1
    // 0xd8d5ec: mov             x1, x2
    // 0xd8d5f0: CheckStackOverflow
    //     0xd8d5f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8d5f4: cmp             SP, x16
    //     0xd8d5f8: b.ls            #0xd8d62c
    // 0xd8d5fc: r2 = LoadClassIdInstr(r0)
    //     0xd8d5fc: ldur            x2, [x0, #-1]
    //     0xd8d600: ubfx            x2, x2, #0xc, #0x14
    // 0xd8d604: cmp             x2, #0xd61
    // 0xd8d608: b.eq            #0xd8d610
    // 0xd8d60c: r0 = of()
    //     0xd8d60c: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xd8d610: r1 = Function '<anonymous closure>':.
    //     0xd8d610: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fc08] AnonymousClosure: (0xd8d634), in [package:flutter/src/widgets/scroll_configuration.dart] ScrollBehavior::velocityTrackerBuilder (0xd8d5e0)
    //     0xd8d614: ldr             x1, [x1, #0xc08]
    // 0xd8d618: r2 = Null
    //     0xd8d618: mov             x2, NULL
    // 0xd8d61c: r0 = AllocateClosure()
    //     0xd8d61c: bl              #0xec1630  ; AllocateClosureStub
    // 0xd8d620: LeaveFrame
    //     0xd8d620: mov             SP, fp
    //     0xd8d624: ldp             fp, lr, [SP], #0x10
    // 0xd8d628: ret
    //     0xd8d628: ret             
    // 0xd8d62c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8d62c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8d630: b               #0xd8d5fc
  }
  [closure] VelocityTracker <anonymous closure>(dynamic, PointerEvent) {
    // ** addr: 0xd8d634, size: 0x80
    // 0xd8d634: EnterFrame
    //     0xd8d634: stp             fp, lr, [SP, #-0x10]!
    //     0xd8d638: mov             fp, SP
    // 0xd8d63c: AllocStack(0x10)
    //     0xd8d63c: sub             SP, SP, #0x10
    // 0xd8d640: CheckStackOverflow
    //     0xd8d640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8d644: cmp             SP, x16
    //     0xd8d648: b.ls            #0xd8d6ac
    // 0xd8d64c: ldr             x1, [fp, #0x10]
    // 0xd8d650: r0 = LoadClassIdInstr(r1)
    //     0xd8d650: ldur            x0, [x1, #-1]
    //     0xd8d654: ubfx            x0, x0, #0xc, #0x14
    // 0xd8d658: r0 = GDT[cid_x0 + 0x130b7]()
    //     0xd8d658: movz            x17, #0x30b7
    //     0xd8d65c: movk            x17, #0x1, lsl #16
    //     0xd8d660: add             lr, x0, x17
    //     0xd8d664: ldr             lr, [x21, lr, lsl #3]
    //     0xd8d668: blr             lr
    // 0xd8d66c: stur            x0, [fp, #-8]
    // 0xd8d670: r0 = VelocityTracker()
    //     0xd8d670: bl              #0x803194  ; AllocateVelocityTrackerStub -> VelocityTracker (size=0x1c)
    // 0xd8d674: stur            x0, [fp, #-0x10]
    // 0xd8d678: StoreField: r0->field_13 = rZR
    //     0xd8d678: stur            xzr, [x0, #0x13]
    // 0xd8d67c: r1 = <_PointAtTime?>
    //     0xd8d67c: add             x1, PP, #0x25, lsl #12  ; [pp+0x253c8] TypeArguments: <_PointAtTime?>
    //     0xd8d680: ldr             x1, [x1, #0x3c8]
    // 0xd8d684: r2 = 40
    //     0xd8d684: movz            x2, #0x28
    // 0xd8d688: r0 = AllocateArray()
    //     0xd8d688: bl              #0xec22fc  ; AllocateArrayStub
    // 0xd8d68c: mov             x1, x0
    // 0xd8d690: ldur            x0, [fp, #-0x10]
    // 0xd8d694: StoreField: r0->field_f = r1
    //     0xd8d694: stur            w1, [x0, #0xf]
    // 0xd8d698: ldur            x1, [fp, #-8]
    // 0xd8d69c: StoreField: r0->field_7 = r1
    //     0xd8d69c: stur            w1, [x0, #7]
    // 0xd8d6a0: LeaveFrame
    //     0xd8d6a0: mov             SP, fp
    //     0xd8d6a4: ldp             fp, lr, [SP], #0x10
    // 0xd8d6a8: ret
    //     0xd8d6a8: ret             
    // 0xd8d6ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8d6ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8d6b0: b               #0xd8d64c
  }
  _ buildOverscrollIndicator(/* No info */) {
    // ** addr: 0xd8e424, size: 0x5c
    // 0xd8e424: EnterFrame
    //     0xd8e424: stp             fp, lr, [SP, #-0x10]!
    //     0xd8e428: mov             fp, SP
    // 0xd8e42c: AllocStack(0x10)
    //     0xd8e42c: sub             SP, SP, #0x10
    // 0xd8e430: SetupParameters(dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xd8e430: stur            x3, [fp, #-0x10]
    // 0xd8e434: LoadField: r0 = r5->field_7
    //     0xd8e434: ldur            w0, [x5, #7]
    // 0xd8e438: DecompressPointer r0
    //     0xd8e438: add             x0, x0, HEAP, lsl #32
    // 0xd8e43c: stur            x0, [fp, #-8]
    // 0xd8e440: r0 = GlowingOverscrollIndicator()
    //     0xd8e440: bl              #0xd8e40c  ; AllocateGlowingOverscrollIndicatorStub -> GlowingOverscrollIndicator (size=0x24)
    // 0xd8e444: r1 = true
    //     0xd8e444: add             x1, NULL, #0x20  ; true
    // 0xd8e448: StoreField: r0->field_b = r1
    //     0xd8e448: stur            w1, [x0, #0xb]
    // 0xd8e44c: StoreField: r0->field_f = r1
    //     0xd8e44c: stur            w1, [x0, #0xf]
    // 0xd8e450: ldur            x1, [fp, #-8]
    // 0xd8e454: StoreField: r0->field_13 = r1
    //     0xd8e454: stur            w1, [x0, #0x13]
    // 0xd8e458: r1 = Instance_Color
    //     0xd8e458: ldr             x1, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xd8e45c: ArrayStore: r0[0] = r1  ; List_4
    //     0xd8e45c: stur            w1, [x0, #0x17]
    // 0xd8e460: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xd8e460: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xd8e464: ldr             x1, [x1, #0xf58]
    // 0xd8e468: StoreField: r0->field_1b = r1
    //     0xd8e468: stur            w1, [x0, #0x1b]
    // 0xd8e46c: ldur            x1, [fp, #-0x10]
    // 0xd8e470: StoreField: r0->field_1f = r1
    //     0xd8e470: stur            w1, [x0, #0x1f]
    // 0xd8e474: LeaveFrame
    //     0xd8e474: mov             SP, fp
    //     0xd8e478: ldp             fp, lr, [SP], #0x10
    // 0xd8e47c: ret
    //     0xd8e47c: ret             
  }
  _ buildScrollbar(/* No info */) {
    // ** addr: 0xd8e694, size: 0x8
    // 0xd8e694: mov             x0, x3
    // 0xd8e698: ret
    //     0xd8e698: ret             
  }
  _ getMultitouchDragStrategy(/* No info */) {
    // ** addr: 0xd902bc, size: 0x4c
    // 0xd902bc: EnterFrame
    //     0xd902bc: stp             fp, lr, [SP, #-0x10]!
    //     0xd902c0: mov             fp, SP
    // 0xd902c4: mov             x0, x1
    // 0xd902c8: mov             x1, x2
    // 0xd902cc: CheckStackOverflow
    //     0xd902cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd902d0: cmp             SP, x16
    //     0xd902d4: b.ls            #0xd90300
    // 0xd902d8: r2 = LoadClassIdInstr(r0)
    //     0xd902d8: ldur            x2, [x0, #-1]
    //     0xd902dc: ubfx            x2, x2, #0xc, #0x14
    // 0xd902e0: cmp             x2, #0xd61
    // 0xd902e4: b.eq            #0xd902ec
    // 0xd902e8: r0 = of()
    //     0xd902e8: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xd902ec: r0 = Instance_MultitouchDragStrategy
    //     0xd902ec: add             x0, PP, #0x25, lsl #12  ; [pp+0x253a8] Obj!MultitouchDragStrategy@e36d21
    //     0xd902f0: ldr             x0, [x0, #0x3a8]
    // 0xd902f4: LeaveFrame
    //     0xd902f4: mov             SP, fp
    //     0xd902f8: ldp             fp, lr, [SP], #0x10
    // 0xd902fc: ret
    //     0xd902fc: ret             
    // 0xd90300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd90300: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd90304: b               #0xd902d8
  }
  get _ dragDevices(/* No info */) {
    // ** addr: 0xd90308, size: 0xc
    // 0xd90308: r0 = _ConstSet len:5
    //     0xd90308: add             x0, PP, #0x4f, lsl #12  ; [pp+0x4fb30] Set<PointerDeviceKind>(5)
    //     0xd9030c: ldr             x0, [x0, #0xb30]
    // 0xd90310: ret
    //     0xd90310: ret             
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xd90314, size: 0x1c0
    // 0xd90314: EnterFrame
    //     0xd90314: stp             fp, lr, [SP, #-0x10]!
    //     0xd90318: mov             fp, SP
    // 0xd9031c: AllocStack(0x28)
    //     0xd9031c: sub             SP, SP, #0x28
    // 0xd90320: SetupParameters(ScrollBehavior this /* r1 => r1, fp-0x28 */, {dynamic dragDevices = Null /* r3, fp-0x20 */, dynamic overscroll = Null /* r5 */, dynamic pointerAxisModifiers = Null /* r6, fp-0x18 */, dynamic scrollbars = Null /* r0 */})
    //     0xd90320: stur            x1, [fp, #-0x28]
    //     0xd90324: ldur            w0, [x4, #0x13]
    //     0xd90328: ldur            w2, [x4, #0x1f]
    //     0xd9032c: add             x2, x2, HEAP, lsl #32
    //     0xd90330: add             x16, PP, #0x4f, lsl #12  ; [pp+0x4fc10] "dragDevices"
    //     0xd90334: ldr             x16, [x16, #0xc10]
    //     0xd90338: cmp             w2, w16
    //     0xd9033c: b.ne            #0xd90360
    //     0xd90340: ldur            w2, [x4, #0x23]
    //     0xd90344: add             x2, x2, HEAP, lsl #32
    //     0xd90348: sub             w3, w0, w2
    //     0xd9034c: add             x2, fp, w3, sxtw #2
    //     0xd90350: ldr             x2, [x2, #8]
    //     0xd90354: mov             x3, x2
    //     0xd90358: movz            x2, #0x1
    //     0xd9035c: b               #0xd90368
    //     0xd90360: mov             x3, NULL
    //     0xd90364: movz            x2, #0
    //     0xd90368: stur            x3, [fp, #-0x20]
    //     0xd9036c: lsl             x5, x2, #1
    //     0xd90370: lsl             w6, w5, #1
    //     0xd90374: add             w7, w6, #8
    //     0xd90378: add             x16, x4, w7, sxtw #1
    //     0xd9037c: ldur            w8, [x16, #0xf]
    //     0xd90380: add             x8, x8, HEAP, lsl #32
    //     0xd90384: add             x16, PP, #0x4f, lsl #12  ; [pp+0x4fc18] "overscroll"
    //     0xd90388: ldr             x16, [x16, #0xc18]
    //     0xd9038c: cmp             w8, w16
    //     0xd90390: b.ne            #0xd903c4
    //     0xd90394: add             w2, w6, #0xa
    //     0xd90398: add             x16, x4, w2, sxtw #1
    //     0xd9039c: ldur            w6, [x16, #0xf]
    //     0xd903a0: add             x6, x6, HEAP, lsl #32
    //     0xd903a4: sub             w2, w0, w6
    //     0xd903a8: add             x6, fp, w2, sxtw #2
    //     0xd903ac: ldr             x6, [x6, #8]
    //     0xd903b0: add             w2, w5, #2
    //     0xd903b4: sbfx            x5, x2, #1, #0x1f
    //     0xd903b8: mov             x2, x5
    //     0xd903bc: mov             x5, x6
    //     0xd903c0: b               #0xd903c8
    //     0xd903c4: mov             x5, NULL
    //     0xd903c8: lsl             x6, x2, #1
    //     0xd903cc: lsl             w7, w6, #1
    //     0xd903d0: add             w8, w7, #8
    //     0xd903d4: add             x16, x4, w8, sxtw #1
    //     0xd903d8: ldur            w9, [x16, #0xf]
    //     0xd903dc: add             x9, x9, HEAP, lsl #32
    //     0xd903e0: add             x16, PP, #0x4f, lsl #12  ; [pp+0x4fc20] "pointerAxisModifiers"
    //     0xd903e4: ldr             x16, [x16, #0xc20]
    //     0xd903e8: cmp             w9, w16
    //     0xd903ec: b.ne            #0xd90420
    //     0xd903f0: add             w2, w7, #0xa
    //     0xd903f4: add             x16, x4, w2, sxtw #1
    //     0xd903f8: ldur            w7, [x16, #0xf]
    //     0xd903fc: add             x7, x7, HEAP, lsl #32
    //     0xd90400: sub             w2, w0, w7
    //     0xd90404: add             x7, fp, w2, sxtw #2
    //     0xd90408: ldr             x7, [x7, #8]
    //     0xd9040c: add             w2, w6, #2
    //     0xd90410: sbfx            x6, x2, #1, #0x1f
    //     0xd90414: mov             x2, x6
    //     0xd90418: mov             x6, x7
    //     0xd9041c: b               #0xd90424
    //     0xd90420: mov             x6, NULL
    //     0xd90424: stur            x6, [fp, #-0x18]
    //     0xd90428: lsl             x7, x2, #1
    //     0xd9042c: lsl             w2, w7, #1
    //     0xd90430: add             w7, w2, #8
    //     0xd90434: add             x16, x4, w7, sxtw #1
    //     0xd90438: ldur            w8, [x16, #0xf]
    //     0xd9043c: add             x8, x8, HEAP, lsl #32
    //     0xd90440: add             x16, PP, #0x4f, lsl #12  ; [pp+0x4fc28] "scrollbars"
    //     0xd90444: ldr             x16, [x16, #0xc28]
    //     0xd90448: cmp             w8, w16
    //     0xd9044c: b.ne            #0xd90470
    //     0xd90450: add             w7, w2, #0xa
    //     0xd90454: add             x16, x4, w7, sxtw #1
    //     0xd90458: ldur            w2, [x16, #0xf]
    //     0xd9045c: add             x2, x2, HEAP, lsl #32
    //     0xd90460: sub             w4, w0, w2
    //     0xd90464: add             x0, fp, w4, sxtw #2
    //     0xd90468: ldr             x0, [x0, #8]
    //     0xd9046c: b               #0xd90474
    //     0xd90470: mov             x0, NULL
    // 0xd90474: cmp             w0, NULL
    // 0xd90478: b.ne            #0xd90480
    // 0xd9047c: r0 = true
    //     0xd9047c: add             x0, NULL, #0x20  ; true
    // 0xd90480: stur            x0, [fp, #-0x10]
    // 0xd90484: cmp             w5, NULL
    // 0xd90488: b.ne            #0xd90494
    // 0xd9048c: r2 = true
    //     0xd9048c: add             x2, NULL, #0x20  ; true
    // 0xd90490: b               #0xd90498
    // 0xd90494: mov             x2, x5
    // 0xd90498: stur            x2, [fp, #-8]
    // 0xd9049c: r0 = _WrappedScrollBehavior()
    //     0xd9049c: bl              #0xd904d4  ; Allocate_WrappedScrollBehaviorStub -> _WrappedScrollBehavior (size=0x28)
    // 0xd904a0: ldur            x1, [fp, #-0x28]
    // 0xd904a4: StoreField: r0->field_7 = r1
    //     0xd904a4: stur            w1, [x0, #7]
    // 0xd904a8: ldur            x1, [fp, #-0x10]
    // 0xd904ac: StoreField: r0->field_b = r1
    //     0xd904ac: stur            w1, [x0, #0xb]
    // 0xd904b0: ldur            x1, [fp, #-8]
    // 0xd904b4: StoreField: r0->field_f = r1
    //     0xd904b4: stur            w1, [x0, #0xf]
    // 0xd904b8: ldur            x1, [fp, #-0x20]
    // 0xd904bc: StoreField: r0->field_1b = r1
    //     0xd904bc: stur            w1, [x0, #0x1b]
    // 0xd904c0: ldur            x1, [fp, #-0x18]
    // 0xd904c4: StoreField: r0->field_23 = r1
    //     0xd904c4: stur            w1, [x0, #0x23]
    // 0xd904c8: LeaveFrame
    //     0xd904c8: mov             SP, fp
    //     0xd904cc: ldp             fp, lr, [SP], #0x10
    // 0xd904d0: ret
    //     0xd904d0: ret             
  }
  _ getPlatform(/* No info */) {
    // ** addr: 0xd90594, size: 0x8
    // 0xd90594: r0 = Instance_TargetPlatform
    //     0xd90594: ldr             x0, [PP, #0x5110]  ; [pp+0x5110] Obj!TargetPlatform@e36f61
    // 0xd90598: ret
    //     0xd90598: ret             
  }
}

// class id: 4622, size: 0x14, field offset: 0x10
//   const constructor, 
class ScrollConfiguration extends InheritedWidget {

  static _ of(/* No info */) {
    // ** addr: 0x999ac0, size: 0x70
    // 0x999ac0: EnterFrame
    //     0x999ac0: stp             fp, lr, [SP, #-0x10]!
    //     0x999ac4: mov             fp, SP
    // 0x999ac8: AllocStack(0x10)
    //     0x999ac8: sub             SP, SP, #0x10
    // 0x999acc: CheckStackOverflow
    //     0x999acc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x999ad0: cmp             SP, x16
    //     0x999ad4: b.ls            #0x999b28
    // 0x999ad8: r16 = <ScrollConfiguration>
    //     0x999ad8: add             x16, PP, #0x39, lsl #12  ; [pp+0x39ed8] TypeArguments: <ScrollConfiguration>
    //     0x999adc: ldr             x16, [x16, #0xed8]
    // 0x999ae0: stp             x1, x16, [SP]
    // 0x999ae4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x999ae4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x999ae8: r0 = dependOnInheritedWidgetOfExactType()
    //     0x999ae8: bl              #0x6396d8  ; [package:flutter/src/widgets/framework.dart] Element::dependOnInheritedWidgetOfExactType
    // 0x999aec: cmp             w0, NULL
    // 0x999af0: b.ne            #0x999afc
    // 0x999af4: r1 = Null
    //     0x999af4: mov             x1, NULL
    // 0x999af8: b               #0x999b04
    // 0x999afc: LoadField: r1 = r0->field_f
    //     0x999afc: ldur            w1, [x0, #0xf]
    // 0x999b00: DecompressPointer r1
    //     0x999b00: add             x1, x1, HEAP, lsl #32
    // 0x999b04: cmp             w1, NULL
    // 0x999b08: b.ne            #0x999b18
    // 0x999b0c: r0 = Instance_ScrollBehavior
    //     0x999b0c: add             x0, PP, #0x39, lsl #12  ; [pp+0x39ee0] Obj!ScrollBehavior@e149f1
    //     0x999b10: ldr             x0, [x0, #0xee0]
    // 0x999b14: b               #0x999b1c
    // 0x999b18: mov             x0, x1
    // 0x999b1c: LeaveFrame
    //     0x999b1c: mov             SP, fp
    //     0x999b20: ldp             fp, lr, [SP], #0x10
    // 0x999b24: ret
    //     0x999b24: ret             
    // 0x999b28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x999b28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x999b2c: b               #0x999ad8
  }
  _ updateShouldNotify(/* No info */) {
    // ** addr: 0xa89508, size: 0xd8
    // 0xa89508: EnterFrame
    //     0xa89508: stp             fp, lr, [SP, #-0x10]!
    //     0xa8950c: mov             fp, SP
    // 0xa89510: AllocStack(0x28)
    //     0xa89510: sub             SP, SP, #0x28
    // 0xa89514: SetupParameters(ScrollConfiguration this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xa89514: mov             x4, x1
    //     0xa89518: mov             x3, x2
    //     0xa8951c: stur            x1, [fp, #-8]
    //     0xa89520: stur            x2, [fp, #-0x10]
    // 0xa89524: CheckStackOverflow
    //     0xa89524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa89528: cmp             SP, x16
    //     0xa8952c: b.ls            #0xa895d8
    // 0xa89530: mov             x0, x3
    // 0xa89534: r2 = Null
    //     0xa89534: mov             x2, NULL
    // 0xa89538: r1 = Null
    //     0xa89538: mov             x1, NULL
    // 0xa8953c: r4 = 60
    //     0xa8953c: movz            x4, #0x3c
    // 0xa89540: branchIfSmi(r0, 0xa8954c)
    //     0xa89540: tbz             w0, #0, #0xa8954c
    // 0xa89544: r4 = LoadClassIdInstr(r0)
    //     0xa89544: ldur            x4, [x0, #-1]
    //     0xa89548: ubfx            x4, x4, #0xc, #0x14
    // 0xa8954c: r17 = 4622
    //     0xa8954c: movz            x17, #0x120e
    // 0xa89550: cmp             x4, x17
    // 0xa89554: b.eq            #0xa8956c
    // 0xa89558: r8 = ScrollConfiguration
    //     0xa89558: add             x8, PP, #0x4f, lsl #12  ; [pp+0x4fbf0] Type: ScrollConfiguration
    //     0xa8955c: ldr             x8, [x8, #0xbf0]
    // 0xa89560: r3 = Null
    //     0xa89560: add             x3, PP, #0x4f, lsl #12  ; [pp+0x4fbf8] Null
    //     0xa89564: ldr             x3, [x3, #0xbf8]
    // 0xa89568: r0 = DefaultTypeTest()
    //     0xa89568: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xa8956c: ldur            x0, [fp, #-8]
    // 0xa89570: LoadField: r1 = r0->field_f
    //     0xa89570: ldur            w1, [x0, #0xf]
    // 0xa89574: DecompressPointer r1
    //     0xa89574: add             x1, x1, HEAP, lsl #32
    // 0xa89578: ldur            x0, [fp, #-0x10]
    // 0xa8957c: stur            x1, [fp, #-0x18]
    // 0xa89580: LoadField: r2 = r0->field_f
    //     0xa89580: ldur            w2, [x0, #0xf]
    // 0xa89584: DecompressPointer r2
    //     0xa89584: add             x2, x2, HEAP, lsl #32
    // 0xa89588: stur            x2, [fp, #-8]
    // 0xa8958c: stp             x2, x1, [SP]
    // 0xa89590: r0 = _haveSameRuntimeType()
    //     0xa89590: bl              #0x6c18d0  ; [dart:core] Object::_haveSameRuntimeType
    // 0xa89594: tbz             w0, #4, #0xa895a0
    // 0xa89598: r0 = true
    //     0xa89598: add             x0, NULL, #0x20  ; true
    // 0xa8959c: b               #0xa895cc
    // 0xa895a0: ldur            x1, [fp, #-0x18]
    // 0xa895a4: ldur            x2, [fp, #-8]
    // 0xa895a8: cmp             w1, w2
    // 0xa895ac: b.eq            #0xa895c8
    // 0xa895b0: r0 = LoadClassIdInstr(r1)
    //     0xa895b0: ldur            x0, [x1, #-1]
    //     0xa895b4: ubfx            x0, x0, #0xc, #0x14
    // 0xa895b8: r0 = GDT[cid_x0 + 0xf2e]()
    //     0xa895b8: add             lr, x0, #0xf2e
    //     0xa895bc: ldr             lr, [x21, lr, lsl #3]
    //     0xa895c0: blr             lr
    // 0xa895c4: b               #0xa895cc
    // 0xa895c8: r0 = false
    //     0xa895c8: add             x0, NULL, #0x30  ; false
    // 0xa895cc: LeaveFrame
    //     0xa895cc: mov             SP, fp
    //     0xa895d0: ldp             fp, lr, [SP], #0x10
    // 0xa895d4: ret
    //     0xa895d4: ret             
    // 0xa895d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa895d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa895dc: b               #0xa89530
  }
}

// class id: 6946, size: 0x14, field offset: 0x14
enum AndroidOverscrollIndicator extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4af80, size: 0x64
    // 0xc4af80: EnterFrame
    //     0xc4af80: stp             fp, lr, [SP, #-0x10]!
    //     0xc4af84: mov             fp, SP
    // 0xc4af88: AllocStack(0x10)
    //     0xc4af88: sub             SP, SP, #0x10
    // 0xc4af8c: SetupParameters(AndroidOverscrollIndicator this /* r1 => r0, fp-0x8 */)
    //     0xc4af8c: mov             x0, x1
    //     0xc4af90: stur            x1, [fp, #-8]
    // 0xc4af94: CheckStackOverflow
    //     0xc4af94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4af98: cmp             SP, x16
    //     0xc4af9c: b.ls            #0xc4afdc
    // 0xc4afa0: r1 = Null
    //     0xc4afa0: mov             x1, NULL
    // 0xc4afa4: r2 = 4
    //     0xc4afa4: movz            x2, #0x4
    // 0xc4afa8: r0 = AllocateArray()
    //     0xc4afa8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4afac: r16 = "AndroidOverscrollIndicator."
    //     0xc4afac: add             x16, PP, #0x59, lsl #12  ; [pp+0x59b88] "AndroidOverscrollIndicator."
    //     0xc4afb0: ldr             x16, [x16, #0xb88]
    // 0xc4afb4: StoreField: r0->field_f = r16
    //     0xc4afb4: stur            w16, [x0, #0xf]
    // 0xc4afb8: ldur            x1, [fp, #-8]
    // 0xc4afbc: LoadField: r2 = r1->field_f
    //     0xc4afbc: ldur            w2, [x1, #0xf]
    // 0xc4afc0: DecompressPointer r2
    //     0xc4afc0: add             x2, x2, HEAP, lsl #32
    // 0xc4afc4: StoreField: r0->field_13 = r2
    //     0xc4afc4: stur            w2, [x0, #0x13]
    // 0xc4afc8: str             x0, [SP]
    // 0xc4afcc: r0 = _interpolate()
    //     0xc4afcc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4afd0: LeaveFrame
    //     0xc4afd0: mov             SP, fp
    //     0xc4afd4: ldp             fp, lr, [SP], #0x10
    // 0xc4afd8: ret
    //     0xc4afd8: ret             
    // 0xc4afdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4afdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4afe0: b               #0xc4afa0
  }
}
